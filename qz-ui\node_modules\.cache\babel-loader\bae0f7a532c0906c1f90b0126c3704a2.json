{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sbbgsq.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sbbgsq.vue", "mtime": 1706897324964}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["sbbgsq.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6UA;;AACA;;AAMA;;AACA;;AACA;;AAEA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAPA;eAQA;AACA,EAAA,UAAA,EAAA;AAAA,IAAA,SAAA,EAAA,kBAAA;AAAA,IAAA,QAAA,EAAA,iBAAA;AAAA,IAAA,QAAA,EAAA,wBAAA;AAAA,IAAA,QAAA,EAAA,iBAAA;AAAA,IAAA,aAAA,EAAA;AAAA,GADA;AAEA,EAAA,IAAA,EAAA,QAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA,MAAA,KAAA,EAAA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAJA,OADA;AASA;AACA,MAAA,UAAA,EAAA,EAVA;AAWA;AACA,MAAA,YAAA,EAAA,KAZA;AAaA,MAAA,YAAA,EAAA,EAbA;AAaA;AACA;AACA,MAAA,cAAA,EAAA,EAfA;AAgBA;AACA,MAAA,gBAAA,EAAA,KAjBA;AAkBA;AACA,MAAA,aAAA,EAAA;AACA,QAAA,UAAA,EAAA,EADA,CACA;;AADA,OAnBA;AAsBA;AACA,MAAA,OAAA,EAAA,EAvBA;AAwBA;AACA,MAAA,MAAA,EAAA,EAzBA;AA0BA;AACA,MAAA,MAAA,EAAA,EA3BA;AA4BA;AACA,MAAA,cAAA,EAAA,KA7BA;AA8BA,MAAA,QAAA,EAAA,EA9BA;AA+BA,MAAA,YAAA,EAAA,KA/BA;AAgCA;AACA,MAAA,cAAA,EAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAjCA;AAkCA;AACA,MAAA,MAAA,EAAA,KAnCA;AAoCA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,oBAAA,EAAA,QADA;AAEA,QAAA,WAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,QAHA;AAIA,QAAA,SAAA,EAAA,EAJA;AAKA,QAAA,WAAA,EAAA,IALA;AAMA,QAAA,QAAA,EAAA,EANA;AAOA,QAAA,WAAA,EAAA;AAPA,OArCA;AA8CA;AACA,MAAA,YAAA,EAAA,EA/CA;AAgDA;AACA,MAAA,QAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAJA,EAKA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OALA,EAMA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OANA,CAjDA;AAyDA;AACA,MAAA,UAAA,EAAA,KA1DA;AA2DA;AACA,MAAA,QAAA,EAAA,UA5DA;AA8DA;AACA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,aAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA;AAZA,OA/DA;AAoFA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,IAAA,EAAA,EADA;AAEA,UAAA,GAAA,EAAA,EAFA;AAGA,UAAA,IAAA,EAAA,EAHA;AAIA,UAAA,IAAA,EAAA,EAJA;AAKA,UAAA,IAAA,EAAA;AALA,SADA;AAQA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA,CACA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WADA,EAEA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAFA,EAGA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAHA,EAIA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAJA,EAKA;AAAA,YAAA,KAAA,EAAA,MAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WALA,EAMA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WANA;AAJA,SAFA,EAeA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,KAAA,EAAA,SAHA;AAIA,UAAA,QAAA,EAAA,WAJA;AAKA,UAAA,MAAA,EAAA;AALA,SAfA,EAsBA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAtBA,EAuBA;AACA,UAAA,KAAA,EAAA,KADA;AAEA,UAAA,IAAA,EAAA,UAFA;AAGA,UAAA,aAAA,EAAA,EAHA;AAIA,UAAA,KAAA,EAAA,MAJA;AAKA,UAAA,OAAA,EAAA;AALA,SAvBA,EA8BA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,UAFA;AAGA,UAAA,aAAA,EAAA,EAHA;AAIA,UAAA,KAAA,EAAA,MAJA;AAKA,UAAA,OAAA,EAAA,CACA;AAAA,YAAA,KAAA,EAAA,KAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WADA,EAEA;AAAA,YAAA,KAAA,EAAA,QAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAFA,EAGA;AAAA,YAAA,KAAA,EAAA,QAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAHA,EAIA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAJA;AALA,SA9BA;AARA,OArFA;AAyIA;AACA,MAAA,iBAAA,EAAA,KA1IA;AA2IA;AACA,MAAA,IAAA,EAAA;AACA,QAAA,IAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,QAAA;AADA,OA5IA;AA+IA;AACA,MAAA,UAAA,EAAA,EAhJA;AAkJA,MAAA,UAAA,EAAA,KAlJA;AAmJA;AACA,MAAA,eAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,GAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA,EAJA;AAKA,QAAA,QAAA,EAAA,EALA;AAMA,QAAA,OAAA,EAAA;AANA;AApJA,KAAA;AA6JA,GAjKA;AAkKA,EAAA,KAAA,EAAA,EAlKA;AAmKA,EAAA,OAnKA,qBAmKA;AACA;AACA,SAAA,uBAAA;AACA,GAtKA;AAuKA,EAAA,OAvKA,qBAuKA;AACA;AACA,SAAA,MAAA,CAAA,KAAA,GAAA,qBAAA;AACA,SAAA,OAAA,CAAA,KAAA,MAAA,CAAA,KAAA;AACA,SAAA,aAAA;AACA,GA5KA;AA6KA,EAAA,OAAA,EAAA;AACA;;;AAGA,IAAA,aAJA,2BAIA;AAAA;;AACA,+BAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAAA,QAAA,EAAA;AACA,SAFA;;AAGA,QAAA,KAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,IAAA,MAAA,EAAA;AACA,mBAAA,IAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;AACA;AACA,SAJA;AAKA,OATA;AAUA,KAfA;AAgBA,IAAA,uBAhBA,qCAgBA;AAAA;;AACA,2CAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,YAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KApBA;AAqBA;AACA,IAAA,UAtBA,sBAsBA,GAtBA,EAsBA;AACA;AACA;AACA,UAAA;AACA,sCAAA,GAAA,CAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA,CAAA,CAAA;AACA,OAFA,CAEA,OAAA,CAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,CAAA;AACA;AACA,KA9BA;AA+BA;AACA,IAAA,YAhCA,wBAgCA,GAhCA,EAgCA;AACA,WAAA,UAAA,GAAA,GAAA,CAAA,aAAA;AACA,WAAA,YAAA,GAAA,IAAA;AACA,KAnCA;AAoCA;AACA,IAAA,cArCA,0BAqCA,IArCA,EAqCA,QArCA,EAqCA;AACA,WAAA,YAAA,GAAA,QAAA;AACA,KAvCA;AAwCA;AACA,IAAA,cAzCA,0BAyCA,IAzCA,EAyCA;AACA,UAAA,OAAA,GAAA,IAAA,CAAA,IAAA,GAAA,IAAA,GAAA,IAAA,GAAA,EAAA;;AACA,UAAA,CAAA,OAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,kBAAA;AACA;;AACA,aAAA,OAAA;AACA,KA/CA;AAgDA;AACA,IAAA,cAjDA,0BAiDA,IAjDA,EAiDA,QAjDA,EAiDA;AACA,WAAA,YAAA,GAAA,QAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AACA,KAnDA;AAoDA;AACA,IAAA,YArDA,wBAqDA,IArDA,EAqDA,QArDA,EAqDA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,KAvDA;AAwDA;AACA,IAAA,cAzDA,0BAyDA,KAzDA,EAyDA,IAzDA,EAyDA,QAzDA,EAyDA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,KAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,MAAA,EAAA,IAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,QAAA;AACA,KA7DA;AA8DA;AACA,IAAA,wBA/DA,oCA+DA,IA/DA,EA+DA;AACA,WAAA,cAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA;AACA,WAAA,gBAAA,GAAA,IAAA;AACA,KAlEA;AAmEA,IAAA,OAnEA,qBAmEA;AACA,WAAA,gBAAA,GAAA,KAAA;AACA,KArEA;AAsEA;AACA,IAAA,YAvEA,wBAuEA,IAvEA,EAuEA,QAvEA,EAuEA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,KAzEA;AA0EA;AACA,IAAA,aA3EA,2BA2EA;AACA,WAAA,YAAA,GAAA,KAAA;AACA,KA7EA;AA8EA;AACA,IAAA,UA/EA,sBA+EA,IA/EA,EA+EA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,GADA,GACA;AACA,kBAAA,KAAA,EAAA,IAAA,CAAA,WADA;AAEA,kBAAA,OAAA,EAAA,CAFA;AAGA,kBAAA,MAAA,EAAA,IAAA,CAAA,WAAA,KAAA,UAAA,GAAA,CAAA,GAAA;AAHA,iBADA;;AAAA,sBAMA,IAAA,CAAA,WAAA,KAAA,UANA;AAAA;AAAA;AAAA;;AAAA,8BAOA,IAAA,CAAA,cAPA;AAAA,gDAQA,UARA,uBAWA,OAXA;AAAA;;AAAA;AASA,gBAAA,GAAA,CAAA,IAAA,GAAA,CAAA;AATA;;AAAA;AAYA,gBAAA,GAAA,CAAA,IAAA,GAAA,CAAA;AAZA;;AAAA;AAAA;AAAA;;AAAA;AAAA,8BAiBA,IAAA,CAAA,cAjBA;AAAA,gDAkBA,OAlBA,wBAsBA,OAtBA,wBA0BA,IA1BA;AAAA;;AAAA;AAmBA,gBAAA,GAAA,CAAA,IAAA,GAAA,CAAA;AACA,gBAAA,GAAA,CAAA,MAAA,GAAA,IAAA,CAAA,QAAA;AApBA;;AAAA;AAuBA,gBAAA,GAAA,CAAA,IAAA,GAAA,CAAA;AACA,gBAAA,GAAA,CAAA,MAAA,GAAA,IAAA,CAAA,QAAA;AAxBA;;AAAA;AA2BA,gBAAA,GAAA,CAAA,IAAA,GAAA,CAAA;AA3BA;;AAAA;AAgCA,0CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,sBAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,oBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA,EADA,CAEA;;;AACA,oBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,oBAAA,MAAA,CAAA,OAAA;;AACA,oBAAA,MAAA,CAAA,SAAA;AACA;AACA,iBARA;;AAhCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyCA,KAxHA;AAyHA;AACA,IAAA,aA1HA,2BA0HA;AACA,UAAA,KAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,EAAA;AACA,aAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,CAAA,WAAA;AACA;;AACA,WAAA,MAAA,GAAA,KAAA;AACA,KA/HA;AAgIA;AACA,IAAA,aAjIA,2BAiIA;AAAA;;AACA;AACA,UAAA,MAAA,GAAA,EAAA;AACA,UAAA,SAAA,GAAA,CAAA,KAAA,EAAA,KAAA,CAAA;AACA,UAAA,QAAA,GAAA,IAAA,QAAA,EAAA,CAJA,CAKA;;AACA,WAAA,OAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,GAAA,IAAA,SAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA;AACA;;AACA,QAAA,QAAA,CAAA,MAAA,CAAA,OAAA,EAAA,IAAA,CAAA,GAAA;AACA,OALA;AAMA,MAAA,QAAA,CAAA,MAAA,CAAA,YAAA,EAAA,KAAA,aAAA,CAAA,UAAA,EAZA,CAYA;;AACA,MAAA,QAAA,CAAA,MAAA,CAAA,QAAA,EAAA,MAAA,EAbA,CAaA;;AACA,MAAA,QAAA,CAAA,MAAA,CAAA,MAAA,EAAA,SAAA,EAdA,CAcA;;AACA,uBACA,WADA,CACA,2BADA,EACA,QADA,EACA,CADA,EAEA,IAFA,CAEA,UAAA,GAAA,EAAA;AACA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,EAAA,CAFA,CAGA;;AACA,QAAA,MAAA,CAAA,OAAA;AACA,OAPA,EAQA,KARA,CAQA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,SAAA;AACA,OAVA;AAWA,KA3JA;AA4JA;AACA,IAAA,eA7JA,6BA6JA;AAAA;;AACA;AACA,UAAA,MAAA,GAAA,EAAA;AACA,UAAA,SAAA,GAAA,CAAA,MAAA,EAAA,MAAA,EAAA,KAAA,CAAA;AACA,UAAA,QAAA,GAAA,IAAA,QAAA,EAAA,CAJA,CAKA;;AACA,WAAA,YAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,GAAA,IAAA,SAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA;AACA;;AACA,QAAA,QAAA,CAAA,MAAA,CAAA,OAAA,EAAA,IAAA,CAAA,GAAA;AACA,OALA;AAMA,MAAA,QAAA,CAAA,MAAA,CAAA,YAAA,EAAA,KAAA,aAAA,CAAA,UAAA,EAZA,CAYA;;AACA,MAAA,QAAA,CAAA,MAAA,CAAA,QAAA,EAAA,MAAA,EAbA,CAaA;;AACA,MAAA,QAAA,CAAA,MAAA,CAAA,MAAA,EAAA,SAAA,EAdA,CAcA;;AACA,uBACA,WADA,CACA,2BADA,EACA,QADA,EACA,CADA,EAEA,IAFA,CAEA,UAAA,GAAA,EAAA;AACA;AACA,QAAA,MAAA,CAAA,YAAA,GAAA,EAAA,CAFA,CAGA;;AACA,QAAA,MAAA,CAAA,OAAA;AACA,OAPA,EAQA,KARA,CAQA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,SAAA;AACA,OAVA;AAWA,KAvLA;AAwLA;AACA,IAAA,SAzLA,qBAyLA,IAzLA,EAyLA;AACA,WAAA,iBAAA,GAAA,KAAA;AACA,UAAA,GAAA,mCAAA,KAAA,IAAA,CAAA;AACA,WAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;;AACA,UAAA,IAAA,KAAA,UAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA;AACA,eAAA,GAAA;AACA,iBAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,iBAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,iBAAA,WAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAAA,WAAA,CAAA,aAAA,GAAA,EAAA;AACA;;AACA,eAAA,GAAA;AACA,iBAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,iBAAA,WAAA,CAAA,IAAA,GAAA,EAAA;AACA,iBAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,iBAAA,WAAA,CAAA,aAAA,GAAA,EAAA;AACA;;AACA,eAAA,GAAA;AACA,iBAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,iBAAA,WAAA,CAAA,WAAA,GAAA,KAAA;AACA;AAhBA;;AAkBA,aAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,OArBA,MAqBA;AACA,gBAAA,GAAA,CAAA,IAAA;AACA,eAAA,GAAA;AACA,iBAAA,cAAA,CAAA,KAAA,GAAA,QAAA;AACA,iBAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA;;AACA,eAAA,GAAA;AACA,iBAAA,cAAA,CAAA,KAAA,GAAA,QAAA;AACA,iBAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,iBAAA,MAAA,GAAA,IAAA;AACA;AATA;;AAWA,aAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,aAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,KAAA;AACA;;AACA,WAAA,MAAA,GAAA,IAAA;AACA,KAlOA;AAoOA;AACA,IAAA,YArOA,wBAqOA,GArOA,EAqOA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AADA;AAAA,uBAEA,8BAAA,MAAA,CAAA,WAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,sBAEA,IAFA;AAEA,gBAAA,IAFA,sBAEA,IAFA;AAGA,gBAAA,MAAA,CAAA,QAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,YAAA,GAAA,IAAA;;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,KA1OA;AA2OA;AACA,IAAA,cA5OA,0BA4OA,GA5OA,EA4OA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,WAAA,MAAA,GACA,iFACA,GAAA,CAAA,KADA,GAEA,KAFA,GAGA,IAAA,IAAA,GAAA,OAAA,EAJA;AAKA,KAnPA;AAoPA;AACA,IAAA,OArPA,mBAqPA,MArPA,EAqPA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,MAAA,CAAA,eAAA,+DAAA,MAAA,CAAA,eAAA,GAAA,MAAA;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,aAAA,CAAA,OAAA,GAAA,IAAA;AACA,gBAAA,KAJA,+DAIA,MAAA,CAAA,eAJA,GAIA,MAJA;AAAA;AAAA,uBAKA,qBAAA,KAAA,CALA;;AAAA;AAAA;AAKA,gBAAA,IALA,kBAKA,IALA;AAKA,gBAAA,IALA,kBAKA,IALA;;AAMA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,IAAA,CAAA,OAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,wBAAA,MAAA,GAAA,MAAA,CAAA,YAAA,CAAA,MAAA,CAAA,UAAA,KAAA,EAAA;AACA,6BAAA,KAAA,CAAA,KAAA,IAAA,IAAA,CAAA,IAAA;AACA,qBAFA,CAAA;;AAGA,wBAAA,MAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,sBAAA,IAAA,CAAA,MAAA,GAAA,MAAA,CAAA,CAAA,CAAA,CAAA,KAAA;AACA,qBAFA,MAEA;AACA,sBAAA,IAAA,CAAA,MAAA,GAAA,EAAA;AACA;AACA,mBATA;AAUA,kBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;;AACA,kBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,oBAAA,MAAA,CAAA,KAAA,CAAA,aAAA,CAAA,OAAA,GAAA,KAAA;AACA,mBAHA;AAIA;;AAvBA;AAAA;;AAAA;AAAA;AAAA;AAyBA,gBAAA,OAAA,CAAA,GAAA;;AACA,gBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,aAAA,CAAA,OAAA,GAAA,KAAA;AACA,iBAHA;;AA1BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA+BA,KApRA;AAqRA;AACA,IAAA,eAtRA,6BAsRA;AACA,WAAA,OAAA,GAAA,EAAA,CADA,CACA;AACA;;AACA,WAAA,iBAAA,GAAA,IAAA,CAHA,CAIA;;AACA,WAAA,QAAA,GAAA,UAAA;AACA,WAAA,UAAA,GAAA,KAAA,CANA,CAOA;;AACA,WAAA,IAAA,GAAA;AACA,QAAA,IAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,QAAA,EADA;AAEA,QAAA,IAAA,EAAA,mCAAA,IAAA,IAAA,EAAA,EAAA,qBAAA,CAFA;AAGA,QAAA,GAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,QAHA;AAIA,QAAA,IAAA,EAAA;AAJA,OAAA;AAMA,KApSA;AAqSA;AACA,IAAA,UAtSA,sBAsSA,GAtSA,EAsSA;AACA;AACA,WAAA,iBAAA,GAAA,IAAA,CAFA,CAGA;;AACA,WAAA,QAAA,GAAA,UAAA,CAJA,CAKA;;AACA,WAAA,UAAA,GAAA,IAAA,CANA,CAOA;;AACA,WAAA,IAAA,mCAAA,GAAA,EARA,CASA;;AACA,WAAA,OAAA,GAAA,KAAA,IAAA,CAAA,OAAA;AACA,KAjTA;AAkTA;AACA,IAAA,aAnTA,yBAmTA,GAnTA,EAmTA;AACA;AACA,WAAA,iBAAA,GAAA,IAAA,CAFA,CAGA;;AACA,WAAA,QAAA,GAAA,UAAA,CAJA,CAKA;;AACA,WAAA,UAAA,GAAA,KAAA,CANA,CAOA;;AACA,WAAA,IAAA,mCAAA,GAAA,EARA,CASA;;AACA,WAAA,OAAA,GAAA,KAAA,IAAA,CAAA,OAAA;AACA,KA9TA;AA+TA;AACA,IAAA,QAhUA,sBAgUA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAEA,MAAA,CAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,sBAAA,KAAA,EAAA;AACA,oBAAA,MAAA,CAAA,iBAAA,GAAA,KAAA;AACA,8CAAA,MAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,0BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA;AACA,wBAAA,MAAA,CAAA,aAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA,CAFA,CAGA;;AACA,wBAAA,MAAA,CAAA,aAAA,GAJA,CAKA;;;AACA,wBAAA,MAAA,CAAA,eAAA;;AACA,wBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA,EAPA,CAQA;;;AACA,wBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,wBAAA,MAAA,CAAA,OAAA;AACA;AACA,qBAbA;AAcA;AACA,iBAlBA,CAFA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAsBA,gBAAA,OAAA,CAAA,GAAA;;AAtBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAwBA,KAxVA;AAyVA;AACA,IAAA,SA1VA,qBA0VA,EA1VA,EA0VA;AAAA;;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAKA,IALA,CAKA,YAAA;AACA,4BAAA,IAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,IAAA,CAAA,gBAAA;AAAA,cAAA,IAAA,QAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,OAAA;AACA,WANA,MAMA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAIA;AACA,SAbA;AAcA,OApBA,EAqBA,KArBA,CAqBA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OA1BA;AA2BA,WAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;AACA,WAAA,OAAA;AACA,KAxXA;AAyXA;AACA,IAAA,WA1XA,yBA0XA;AACA,WAAA,UAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,KAAA,UAAA,EAAA;AACA,UAAA,IAAA,CAAA,aAAA,GAAA,EAAA;AACA;AACA,OAJA;AAKA,KAhYA;AAiYA;AACA,IAAA,YAlYA,wBAkYA,IAlYA,EAkYA;AACA,WAAA,UAAA,GAAA,IAAA;AACA;AApYA;AA7KA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      ref=\"filter1\"\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      @handleReset=\"filterReset\"\n    />\n    <el-white class=\"button-group1\">\n      <div class=\"button_btn\">\n        <el-button\n          @click=\"addSensorButton\"\n          icon=\"el-icon-plus\"\n          type=\"primary\"\n          v-hasPermi=\"['sbbgsq:button:add']\"\n          >新增</el-button\n        >\n      </div>\n      <comp-table\n        ref=\"bdywgzrzTable\"\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"selectChange\"\n        height=\"63vh\"\n      >\n        <el-table-column\n          slot=\"table_eight\"\n          align=\"center\"\n          fixed=\"right\"\n          style=\"display: block\"\n          label=\"操作\"\n          width=\"180\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              @click=\"getGqjInfo(scope.row)\"\n              title=\"详情\"\n              class=\"el-icon-view\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"\n                scope.row.lczt === '1' &&\n                  $store.getters.name === scope.row.createBy\n              \"\n              @click=\"updateGqjInfo(scope.row)\"\n              title=\"编辑\"\n              class=\"el-icon-edit\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"\n                (scope.row.lczt === '1' &&\n                  $store.getters.name === scope.row.createBy) ||\n                  'admin' === $store.getters.name\n              \"\n              @click=\"deleteRow(scope.row.objId)\"\n              title=\"删除\"\n              class=\"el-icon-delete\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"scope.row.isStart === 1\"\n              @click=\"showTimeLine(scope.row)\"\n              title=\"流程查看\"\n              class=\"el-icon-lcck commonIcon\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"scope.row.isStart === 1\"\n              @click=\"showProcessImg(scope.row)\"\n              title=\"流程图\"\n              class=\"el-icon-lct commonIcon\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              @click=\"handleDataFj(scope.row)\"\n              title=\"附件列表\"\n              class=\"el-icon-tickets\"\n            ></el-button>\n          </template>\n        </el-table-column>\n      </comp-table>\n    </el-white>\n    <el-dialog\n      :title=\"gqjTital\"\n      :visible.sync=\"dialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"40%\"\n      v-dialogDrag\n    >\n      <el-form\n        :model=\"form\"\n        ref=\"form\"\n        label-width=\"80px\"\n        :disabled=\"isDisabled\"\n        :rules=\"rules\"\n      >\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属公司\" prop=\"ssgs\">\n              <el-select v-model=\"form.ssgs\" placeholder=\"\" disabled>\n                <el-option\n                  v-for=\"item in ssgsDataList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"申请人\" prop=\"sqr\">\n              <el-select v-model=\"form.sqr\" placeholder=\"\" disabled>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"变更类型\" prop=\"bglx\">\n              <el-select v-model=\"form.bglx\" placeholder=\"变更类型\" clearable>\n                <el-option\n                  v-for=\"item in bglxList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"变更说明\" prop=\"description\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"5\"\n                v-model=\"form.description\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"申请时间\" prop=\"sqsj\">\n              <el-date-picker\n                v-model=\"form.sqsj\"\n                disabled\n                type=\"datetime\"\n                placeholder=\"选择日期\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n                format=\"yyyy-MM-dd HH:mm:ss\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"24\">\n            <el-form-item label=\"图片信息：\" label-width=\"110px\">\n              <el-upload\n                action=\"\"\n                ref=\"uploadImg\"\n                accept=\".jpg,.png\"\n                :headers=\"header\"\n                :multiple=\"true\"\n                :on-change=\"handleChange\"\n                :data=\"uploadImgData\"\n                :file-list=\"imgList\"\n                :auto-upload=\"false\"\n                list-type=\"picture-card\"\n                :on-preview=\"handlePictureCardPreview\"\n                :on-progress=\"handleProgress\"\n                :on-remove=\"handleRemove\"\n              >\n                <i class=\"el-icon-plus\"></i>\n              </el-upload>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"附件上传：\" label-width=\"110px\">\n              <el-upload\n                :on-remove=\"handleFjRemove\"\n                class=\"upload-demo\"\n                action=\"\"\n                :headers=\"header\"\n                :on-change=\"handleFjUpload\"\n                :data=\"uploadImgData\"\n                :auto-upload=\"false\"\n                :before-upload=\"beforeFjUpload\"\n                :http-request=\"uoloadOtherFile\"\n                :file-list=\"fjUploadList\"\n              >\n                <el-button size=\"small\" type=\"primary\">点击上传</el-button>\n                <div slot=\"tip\" class=\"el-upload__tip\">文件大小不超过10MB</div>\n              </el-upload>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"qxcommit\"\n          v-if=\"!isDisabled\"\n          class=\"pmyBtn\"\n          >保存</el-button\n        >\n        <el-button\n          type=\"primary\"\n          @click=\"getSbFsBj('complete')\"\n          v-if=\"\n            isDisabled &&\n              form.lczt === '1' &&\n              $store.getters.name === form.createBy\n          \"\n          class=\"pmyBtn\"\n          >提交审核</el-button\n        >\n        <el-button\n          type=\"primary\"\n          @click=\"getSbFsBj('rollback')\"\n          v-if=\"\n            isDisabled &&\n              form.lczt === '2' &&\n              $store.getters.name === form.fgsshr\n          \"\n          class=\"pmyBtn\"\n          >回 退</el-button\n        >\n        <el-button\n          type=\"primary\"\n          @click=\"getSbFsBj('rollback')\"\n          v-if=\"\n            isDisabled &&\n              form.lczt === '3' &&\n              $store.getters.name === form.sckshr\n          \"\n          class=\"pmyBtn\"\n          >回 退</el-button\n        >\n        <el-button\n          type=\"primary\"\n          @click=\"getSbFsBj('complete')\"\n          v-if=\"\n            isDisabled &&\n              form.lczt === '2' &&\n              $store.getters.name === form.fgsshr\n          \"\n          class=\"pmyBtn\"\n          >提交审核</el-button\n        >\n        <el-button\n          type=\"primary\"\n          @click=\"getSbFsBj('complete')\"\n          v-if=\"\n            isDisabled &&\n              form.lczt === '3' &&\n              $store.getters.name === form.sckshr\n          \"\n          class=\"pmyBtn\"\n          >审核通过</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <!--工作流需要-->\n    <activiti\n      ref=\"activiti\"\n      :processData=\"processData\"\n      :isShow=\"isShow\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n    ></activiti>\n    <time-line\n      :value=\"timeLineShow\"\n      :timeData=\"timeData\"\n      @closeTimeLine=\"colseTimeLine\"\n    />\n    <!--流程图查看-->\n    <el-dialog\n      title=\"审批流程图\"\n      :visible.sync=\"openLoadingImg\"\n      width=\"56%\"\n      append-to-body\n      :close-on-click-modal=\"false\"\n    >\n      <div>\n        <img :src=\"imgSrc\" style=\"width: 100%\" />\n      </div>\n    </el-dialog>\n    <!--图片放大展示弹出框-->\n    <el-image-viewer\n      v-if=\"imgDialogVisible\"\n      :on-close=\"onClose\"\n      :url-list=\"dialogImageUrl\"\n      zIndex=\"3000\"\n    />\n\n    <!--附件列表展示页面-->\n    <el-dialog title=\"附件信息\" :visible.sync=\"openFjDialog\" width=\"60%\">\n      <el-table :data=\"fjDataList\" style=\"width: 100%\" stripe border fit>\n        <el-table-column\n          prop=\"fileId\"\n          label=\"文件编号\"\n          align=\"center\"\n        ></el-table-column>\n        <el-table-column\n          prop=\"fileType\"\n          label=\"文件类型\"\n          align=\"center\"\n        ></el-table-column>\n        <el-table-column\n          prop=\"name\"\n          label=\"文件名称\"\n          align=\"center\"\n        ></el-table-column>\n        <el-table-column label=\"操作\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <el-button @click=\"downLoadFj(scope.row)\" type=\"text\" size=\"small\"\n              >下载</el-button\n            >\n          </template>\n        </el-table-column>\n      </el-table>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport {\n  getList,\n  remove,\n  saveOrUpdate,\n  downloadByFileId\n} from \"@/api/dagangOilfield/asset/sbbgsq\";\nimport CompTable from \"com/CompTable\";\nimport ElFilter from \"com/ElFilter\";\nimport { formatterDateTime } from \"@/utils/handleData\";\n//流程\nimport activiti from \"com/activiti_sbbgsq\";\nimport timeLine from \"com/timeLine\";\nimport { HistoryList } from \"@/api/activiti/processTask\";\nimport api from \"@/utils/request\";\nimport { getOrganizationSelected } from \"@/api/dagangOilfield/asset/bdsbtz\";\nimport ElImageViewer from \"element-ui/packages/image/src/image-viewer\";\nimport { getFgsOptions } from \"@/api/yxgl/bdyxgl/zbgl\";\nexport default {\n  components: { CompTable, ElFilter, activiti, timeLine, ElImageViewer },\n  name: \"sbbgsq\",\n  data() {\n    return {\n      rules: {\n        bglx: [\n          { required: true, message: \"请选择变更类型\", trigger: \"change\" }\n        ],\n        description: [\n          { required: true, message: \"变更说明不能为空\", trigger: \"change\" }\n        ]\n      },\n      //附件列表数据\n      fjDataList: [],\n      //附件列表弹出框\n      openFjDialog: false,\n      fjUploadList: [], //除图片外的上传列表\n      //图片地址url\n      dialogImageUrl: \"\",\n      //展示图片dialog控制\n      imgDialogVisible: false,\n      //上传图片时的携带的其他参数\n      uploadImgData: {\n        businessId: \"\" //携带的表单主键id\n      },\n      //图片list\n      imgList: [],\n      //上传图片时的请求头\n      header: {},\n      //流程图查看地址\n      imgSrc: \"\",\n      //流程图查看\n      openLoadingImg: false,\n      timeData: [],\n      timeLineShow: false,\n      //弹出框标题\n      activitiOption: { title: \"上报\" },\n      //工作流弹窗\n      isShow: false,\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"sbztbg\",\n        businessKey: \"\",\n        businessType: \"设备状态变更\",\n        variables: {},\n        defaultFrom: true,\n        nextUser: \"\",\n        processType: \"complete\"\n      },\n      //所属公司\n      ssgsDataList: [],\n      //变更类型list\n      bglxList: [\n        { label: \"新投\", value: \"新投\" },\n        { label: \"切改\", value: \"切改\" },\n        { label: \"退役\", value: \"退役\" },\n        { label: \"更换\", value: \"更换\" },\n        { label: \"资料完善\", value: \"资料完善\" },\n        { label: \"其它\", value: \"其它\" }\n      ],\n      //工器具详情框字段控制\n      isDisabled: false,\n      //工器具弹出框表头\n      gqjTital: \"设备变更申请新增\",\n\n      //表格内容\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"ssgsmc\", label: \"分公司\", minWidth: \"120\" },\n          { prop: \"sqr\", label: \"申请人\", minWidth: \"100\" },\n          { prop: \"bglx\", label: \"变更类型\", minWidth: \"100\" },\n          { prop: \"sqsj\", label: \"申请时间\", minWidth: \"120\" },\n          { prop: \"ztmc\", label: \"流程状态\", minWidth: \"100\" },\n          { prop: \"description\", label: \"变更说明\", minWidth: \"250\" }\n        ]\n      },\n      //筛选条件\n      filterInfo: {\n        data: {\n          ssgs: \"\",\n          sqr: \"\",\n          bglx: \"\",\n          lczt: \"\",\n          ztmc: \"\"\n        },\n        fieldList: [\n          { label: \"申请人\", type: \"input\", value: \"sqr\" },\n          {\n            label: \"变更类型\",\n            type: \"select\",\n            value: \"bglx\",\n            options: [\n              { label: \"新投\", value: \"新投\" },\n              { label: \"切改\", value: \"切改\" },\n              { label: \"退役\", value: \"退役\" },\n              { label: \"更换\", value: \"更换\" },\n              { label: \"资料完善\", value: \"资料完善\" },\n              { label: \"其它\", value: \"其它\" }\n            ]\n          },\n          {\n            label: \"申请时间\",\n            type: \"date\",\n            value: \"tyrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"变更说明\", type: \"input\", value: \"description\" },\n          {\n            label: \"分公司\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"ssgs\",\n            options: []\n          },\n          {\n            label: \"流程状态\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"ztmc\",\n            options: [\n              { label: \"待上报\", value: \"1\" },\n              { label: \"待分公司审核\", value: \"2\" },\n              { label: \"待生产科审核\", value: \"3\" },\n              { label: \"关闭\", value: \"4\" }\n            ]\n          }\n        ]\n      },\n      //工器具弹出框\n      dialogFormVisible: false,\n      //弹出框表单\n      form: {\n        ssgs: this.$store.getters.deptId.toString()\n      },\n      //删除选择列\n      selectRows: [],\n\n      isSyDetail: false,\n      //查询变更记录参数\n      queryBgsqParams: {\n        ssgs: \"\",\n        sqr: \"\",\n        bglx: \"\",\n        lczt: \"\",\n        pageSize: 10,\n        pageNum: 1\n      }\n    };\n  },\n  watch: {},\n  created() {\n    //获取组织结构下拉数据\n    this.getOrganizationSelected();\n  },\n  mounted() {\n    //获取token\n    this.header.token = getToken();\n    this.getData(this.$route.query);\n    this.getFgsOptions();\n  },\n  methods: {\n    /**\n     * 获取分公司下拉数据\n     */\n    getFgsOptions() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"ssgs\") {\n            return (item.options = res.data);\n          }\n        });\n      });\n    },\n    getOrganizationSelected() {\n      getOrganizationSelected({ parentId: \"1001\" }).then(res => {\n        this.ssgsDataList = res.data;\n      });\n    },\n    //下载附件\n    downLoadFj(row) {\n      //点击下载\n      //开始执行请求\n      try {\n        downloadByFileId(row.fileId).then(res => {});\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //行内查看附件列表\n    handleDataFj(row) {\n      this.fjDataList = row.otherFileList;\n      this.openFjDialog = true;\n    },\n    //上传的附件移除\n    handleFjRemove(file, fileList) {\n      this.fjUploadList = fileList;\n    },\n    //附件上传前的钩子函数\n    beforeFjUpload(file) {\n      const isLt10M = file.size / 1024 / 1024 < 10;\n      if (!isLt10M) {\n        this.$message.error(\"上传附件大小不能超过 10MB!\");\n      }\n      return isLt10M;\n    },\n    //除图片外的上传附件信息\n    handleFjUpload(file, fileList) {\n      this.fjUploadList = fileList.slice(-3);\n    },\n    //图片移除\n    handleRemove(file, fileList) {\n      this.imgList = fileList;\n    },\n    //上传图片成功进度条\n    handleProgress(event, file, fileList) {\n      console.log(\"event\", event);\n      console.log(\"file\", file);\n      console.log(\"fileList\", fileList);\n    },\n    //图片放大\n    handlePictureCardPreview(file) {\n      this.dialogImageUrl = [file.url];\n      this.imgDialogVisible = true;\n    },\n    onClose() {\n      this.imgDialogVisible = false;\n    },\n    // 选择图片文件时，往fileList里添加\n    handleChange(file, fileList) {\n      this.imgList = fileList;\n    },\n    //关闭流程查看页面\n    colseTimeLine() {\n      this.timeLineShow = false;\n    },\n    //工作流回传数据\n    async todoResult(data) {\n      let row = {\n        objId: data.businessKey,\n        isStart: 1,\n        isBack: data.processType === \"rollback\" ? 1 : 0\n      };\n      if (data.processType === \"rollback\") {\n        switch (data.activeTaskName) {\n          case \"设备变更信息填报\":\n            row.lczt = 1;\n            break;\n          case \"分公司审核\":\n            row.lczt = 2;\n            break;\n        }\n      } else {\n        //1-待上报；2-分公司审核；3-生产科审核，4-关闭\n        switch (data.activeTaskName) {\n          case \"分公司审核\":\n            row.lczt = 2;\n            row.fgsshr = data.nextUser;\n            break;\n          case \"生产科审批\":\n            row.lczt = 3;\n            row.sckshr = data.nextUser;\n            break;\n          case \"结束\":\n            row.lczt = 4;\n            break;\n        }\n      }\n\n      saveOrUpdate(row).then(res => {\n        if (res.code === \"0000\") {\n          this.$message.success(\"操作成功\");\n          //重置page页从1开始\n          this.tableAndPageInfo.pager.pageResize = \"Y\";\n          this.getData();\n          this.resetForm();\n        }\n      });\n    },\n    //关闭弹窗\n    closeActiviti() {\n      if (this.$refs.activiti.$refs.form) {\n        this.$refs.activiti.$refs.form.resetFields();\n      }\n      this.isShow = false;\n    },\n    //上传图片\n    uploadFormImg() {\n      //用来存放当前未改动过的图片url,此图片不进行删除处理，其余当前业务id下面的图片数据将进行删除\n      var newUrl = [];\n      var imageType = [\"png\", \"jpg\"];\n      const formData = new FormData();\n      // 因为要传一个文件数组过去，所以要循环append\n      this.imgList.forEach(file => {\n        if (file.raw == undefined) {\n          newUrl.push(file.url);\n        }\n        formData.append(\"files\", file.raw);\n      });\n      formData.append(\"businessId\", this.uploadImgData.businessId); // 自定义参数\n      formData.append(\"newUrl\", newUrl); // 未改动过的图片url\n      formData.append(\"type\", imageType); // 未改动过的图片url\n      api\n        .requestPost(\"/isc-api/file/uploadFiles\", formData, 1)\n        .then(res => {\n          // 清空图片列表（一定要清空，否则上传成功后还是会调用handleChange（）函数，上传成功后列表中还存在图片）\n          this.imgList = [];\n          //查询接口数据\n          this.getData();\n        })\n        .catch(res => {\n          this.$message.success(\"图片上传失败！\");\n        });\n    },\n    //上传除图片视频意外的附件\n    uoloadOtherFile() {\n      //用来存放当前未改动过的url,此图片不进行删除处理，其余当前业务id下面的图片数据将进行删除\n      var newUrl = [];\n      var imageType = [\"docx\", \"xlsx\", \"pdf\"];\n      const formData = new FormData();\n      // 因为要传一个文件数组过去，所以要循环append\n      this.fjUploadList.forEach(file => {\n        if (file.raw == undefined) {\n          newUrl.push(file.url);\n        }\n        formData.append(\"files\", file.raw);\n      });\n      formData.append(\"businessId\", this.uploadImgData.businessId); // 自定义参数\n      formData.append(\"newUrl\", newUrl); // 未改动过的图片url\n      formData.append(\"type\", imageType); // 未改动过的图片url\n      api\n        .requestPost(\"/isc-api/file/uploadFiles\", formData, 1)\n        .then(res => {\n          // 清空图片列表（一定要清空，否则上传成功后还是会调用handleChange（）函数，上传成功后列表中还存在图片）\n          this.fjUploadList = [];\n          //查询接口数据\n          this.getData();\n        })\n        .catch(res => {\n          this.$message.success(\"附件上传失败！\");\n        });\n    },\n    //上报发送办结\n    getSbFsBj(type) {\n      this.dialogFormVisible = false;\n      let row = { ...this.form };\n      this.processData.businessKey = row.objId;\n      if (type === \"complete\") {\n        switch (row.lczt) {\n          case \"1\":\n            this.activitiOption.title = \"提交\";\n            this.processData.defaultFrom = true;\n            this.processData.ssgs = row.ssgs;\n            this.processData.personGroupId = 46;\n            break;\n          case \"2\":\n            this.activitiOption.title = \"提交\";\n            this.processData.ssgs = \"\";\n            this.processData.defaultFrom = true;\n            this.processData.personGroupId = 47;\n            break;\n          case \"3\":\n            this.activitiOption.title = \"通过\";\n            this.processData.defaultFrom = false;\n            break;\n        }\n        this.processData.variables.pass = true;\n        this.processData.processType = \"complete\";\n      } else {\n        switch (row.lczt) {\n          case \"2\":\n            this.activitiOption.title = \"回退原因填写\";\n            this.processData.defaultFrom = true;\n            break;\n          case \"3\":\n            this.activitiOption.title = \"回退原因填写\";\n            this.processData.defaultFrom = true;\n            this.isShow = true;\n            break;\n        }\n        this.processData.processType = \"rollback\";\n        this.processData.variables.pass = false;\n      }\n      this.isShow = true;\n    },\n\n    //流程查看\n    async showTimeLine(row) {\n      this.processData.businessKey = row.objId;\n      let { code, data } = await HistoryList(this.processData);\n      this.timeData = data;\n      this.timeLineShow = true;\n    },\n    //流程图片查看\n    showProcessImg(row) {\n      this.openLoadingImg = true;\n      this.imgSrc =\n        \"/activiti-api/process/read-resource?processDefinitionKey=sbztbg&businessKey=\" +\n        row.objId +\n        \"&t=\" +\n        new Date().getTime();\n    },\n    //工器具列表查询\n    async getData(params) {\n      try {\n        this.queryBgsqParams = { ...this.queryBgsqParams, ...params };\n        this.$refs.bdywgzrzTable.loading = true;\n        const param = { ...this.queryBgsqParams, ...params };\n        const { data, code } = await getList(param);\n        if (code === \"0000\") {\n          data.records.forEach(item => {\n            let filter = this.ssgsDataList.filter(item1 => {\n              return item1.value == item.ssgs;\n            });\n            if (filter.length > 0) {\n              item.ssgsmc = filter[0].label;\n            } else {\n              item.ssgsmc = \"\";\n            }\n          });\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          this.$nextTick(() => {\n            // 以服务的方式调用的 Loading 需要异步关闭\n            this.$refs.bdywgzrzTable.loading = false;\n          });\n        }\n      } catch (e) {\n        console.log(e);\n        this.$nextTick(() => {\n          // 以服务的方式调用的 Loading 需要异步关闭\n          this.$refs.bdywgzrzTable.loading = false;\n        });\n      }\n    },\n    //工器具列表新增按钮\n    addSensorButton() {\n      this.imgList = []; //置空图片\n      //打开弹出框\n      this.dialogFormVisible = true;\n      //设置弹出框表头\n      this.gqjTital = \"设备变更申请新增\";\n      this.isDisabled = false;\n      //清空弹出框内容\n      this.form = {\n        ssgs: this.$store.getters.deptId.toString(),\n        sqsj: formatterDateTime(new Date(), \"yyyy-MM-dd hh:mm:ss\"),\n        sqr: this.$store.getters.nickName,\n        lczt: \"1\"\n      };\n    },\n    //详情按钮\n    getGqjInfo(row) {\n      //打开弹出框\n      this.dialogFormVisible = true;\n      //设置弹出框表头\n      this.gqjTital = \"设备变更申请详情\";\n      //禁用所有输入框\n      this.isDisabled = true;\n      //给弹出框赋值\n      this.form = { ...row };\n      //图片结合\n      this.imgList = this.form.imgList;\n    },\n    //修改按钮\n    updateGqjInfo(row) {\n      //打开弹出框\n      this.dialogFormVisible = true;\n      //设置弹出框表头\n      this.gqjTital = \"设备变更申请修改\";\n      //开启弹出框内输入框编辑权限\n      this.isDisabled = false;\n      //给弹出框内赋值\n      this.form = { ...row };\n      //图片结合\n      this.imgList = this.form.imgList;\n    },\n    //新增修改保存\n    async qxcommit() {\n      try {\n        await this.$refs[\"form\"].validate(valid => {\n          if (valid) {\n            this.dialogFormVisible = false;\n            saveOrUpdate(this.form).then(res => {\n              if (res.code === \"0000\") {\n                //给上传图片附加业务id参数\n                this.uploadImgData.businessId = res.data.objId;\n                //开始上传图片\n                this.uploadFormImg();\n                //上传文档信息\n                this.uoloadOtherFile();\n                this.$message.success(\"操作成功\");\n                //恢复分页\n                this.tableAndPageInfo.pager.pageResize = \"Y\";\n                this.getData();\n              }\n            });\n          }\n        });\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //删除\n    deleteRow(id) {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove(JSON.stringify(id)).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n      this.tableAndPageInfo.pager.pageResize = \"Y\";\n      this.getData();\n    },\n    //重置\n    filterReset() {\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n    },\n    //选择每一行\n    selectChange(rows) {\n      this.selectRows = rows;\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.button-group {\n  padding-left: 30px;\n  padding-right: 30px;\n  display: flex;\n  justify-content: flex-end;\n}\n\n.qxlr_dialog_insert {\n  margin-top: 6vh !important;\n}\n\n/*/deep/ .qxlr_dialog_insert .el-input--medium .el-input__inner{*/\n/*  width: 100%;*/\n/*}*/\n.el-select {\n  width: 100%;\n}\n\n.el-date-editor {\n  width: 100%;\n}\n</style>\n"], "sourceRoot": "src/views/dagangOilfield/dwzygl"}]}