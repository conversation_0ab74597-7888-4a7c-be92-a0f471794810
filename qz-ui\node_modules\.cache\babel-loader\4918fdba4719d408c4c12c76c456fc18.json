{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\asset\\zxwh.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\asset\\zxwh.js", "mtime": 1706897313909}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0UGFnZSA9IGdldFBhZ2U7CmV4cG9ydHMuZ2V0R3RzQnlYbCA9IGdldEd0c0J5WGw7CmV4cG9ydHMuc2F2ZU9yVXBkYXRlID0gc2F2ZU9yVXBkYXRlOwpleHBvcnRzLmRlbEJ5SWQgPSBkZWxCeUlkOwoKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Cgp2YXIgYmFzZVVybCA9ICIvbWFuYWdlci1hcGkvYXNzZXRYZCI7IC8vIOafpeivogoKZnVuY3Rpb24gZ2V0UGFnZShwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy9nZXRQYWdlJywgcGFyYW1zLCAxKTsKfSAvLyDmn6Xor6Lnur/ot6/kuIvnmoTmnYbloZTmlbDmja4KCgpmdW5jdGlvbiBnZXRHdHNCeVhsKHBhcmFtcykgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3RQb3N0KGJhc2VVcmwgKyAnL2dldEd0c0J5WGwnLCBwYXJhbXMsIDEpOwp9IC8vIOa3u+WKoOaIluS/ruaUuQoKCmZ1bmN0aW9uIHNhdmVPclVwZGF0ZShwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy9zYXZlT3JVcGRhdGUnLCBwYXJhbXMsIDEpOwp9IC8vIOWIoOmZpAoKCmZ1bmN0aW9uIGRlbEJ5SWQocGFyYW1zKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcvZGVsQnlJZCcsIHBhcmFtcywgMSk7Cn0="}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagangOilfield/asset/zxwh.js"], "names": ["baseUrl", "getPage", "params", "api", "requestPost", "getGtsByXl", "saveOrUpdate", "delById"], "mappings": ";;;;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,sBAAhB,C,CAEA;;AACO,SAASC,OAAT,CAAiBC,MAAjB,EAAyB;AAC9B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,UAAxB,EAAmCE,MAAnC,EAA0C,CAA1C,CAAP;AACD,C,CAED;;;AACO,SAASG,UAAT,CAAoBH,MAApB,EAA4B;AACjC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,aAAxB,EAAsCE,MAAtC,EAA6C,CAA7C,CAAP;AACD,C,CAED;;;AACO,SAASI,YAAT,CAAsBJ,MAAtB,EAA8B;AACnC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,eAAxB,EAAwCE,MAAxC,EAA+C,CAA/C,CAAP;AACD,C,CACD;;;AACO,SAASK,OAAT,CAAiBL,MAAjB,EAAyB;AAC9B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,UAAxB,EAAmCE,MAAnC,EAA0C,CAA1C,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\nconst baseUrl = \"/manager-api/assetXd\";\n\n// 查询\nexport function getPage(params) {\n  return api.requestPost(baseUrl+'/getPage',params,1)\n}\n\n// 查询线路下的杆塔数据\nexport function getGtsByXl(params) {\n  return api.requestPost(baseUrl+'/getGtsByXl',params,1)\n}\n\n// 添加或修改\nexport function saveOrUpdate(params) {\n  return api.requestPost(baseUrl+'/saveOrUpdate',params,1)\n}\n// 删除\nexport function delById(params) {\n  return api.requestPost(baseUrl+'/delById',params,1)\n}\n"]}]}