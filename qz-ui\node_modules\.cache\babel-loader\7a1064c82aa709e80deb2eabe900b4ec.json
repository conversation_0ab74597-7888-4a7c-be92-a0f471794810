{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\xldzcz\\dzczml.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\xldzcz\\dzczml.vue", "mtime": 1719917121296}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["dzczml.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAwbA;;AAQA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,QADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,oBAAA,EAAA,6BAAA;AAAA,IAAA,YAAA,EAAA,qBAAA;AAAA,IAAA,QAAA,EAAA;AAAA,GAFA;AAGA,EAAA,IAHA,kBAGA;AAAA;;AACA;AACA,MAAA,YAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,YADA;AAEA;AACA,MAAA,GAAA,EAAA,EAHA;AAIA;AACA,MAAA,UAAA,EAAA,EALA;AAMA;AACA,MAAA,MAAA,EAAA,SAPA;AAQA;AACA,MAAA,QAAA,EAAA,IATA;AAUA,MAAA,OAAA,EAAA,KAVA;AAWA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,oBAAA,EAAA,OADA;AAEA,QAAA,WAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,OAHA;AAIA,QAAA,SAAA,EAAA,EAJA;AAKA,QAAA,WAAA,EAAA,IALA;AAMA,QAAA,QAAA,EAAA,EANA;AAOA,QAAA,WAAA,EAAA;AAPA,OAZA;AAqBA,MAAA,MAAA,EAAA,KArBA;AAsBA;AACA,MAAA,cAAA,EAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAvBA;AAwBA,MAAA,KAAA,EAAA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,YAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,YAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAVA,OAxBA;AAqCA;AACA,MAAA,OAAA,EAAA,EAtCA;AAuCA;AACA,MAAA,WAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,IAxCA;AAyCA,MAAA,EAAA,EAAA,KAzCA;AA0CA;AACA,MAAA,cAAA,EAAA;AA3CA,kDA6CA,EA7CA,+CA+CA;AACA,MAAA,EAAA,EAAA,EADA;AAEA,MAAA,IAAA,EAAA,EAFA;AAGA,MAAA,IAAA,EAAA,EAHA;AAIA,MAAA,MAAA,EAAA,EAJA;AAKA,MAAA,IAAA,EAAA,EALA;AAMA,MAAA,MAAA,EAAA,EANA;AAOA,MAAA,MAAA,EAAA,EAPA;AAQA,MAAA,IAAA,EAAA;AARA,KA/CA,kDA0DA;AACA,MAAA,EAAA,EAAA,EADA;AAEA,MAAA,IAAA,EAAA,EAFA;AAGA,MAAA,GAAA,EAAA,EAHA;AAIA,MAAA,GAAA,EAAA,EAJA;AAKA,MAAA,KAAA,EAAA,EALA;AAMA,MAAA,EAAA,EAAA,CANA;AAMA;AACA,MAAA,QAAA,EAAA;AAPA,KA1DA,wDAoEA;AACA,MAAA,GAAA,EAAA,IADA;AACA;AACA,MAAA,QAAA,EAAA;AAFA,KApEA,2DAyEA;AACA,MAAA,GAAA,EAAA,IADA;AACA;AACA,MAAA,QAAA,EAAA;AAFA,KAzEA,wDA8EA,KA9EA,2DAgFA,KAhFA,qDAkFA,KAlFA,qDAmFA,KAnFA,mDAoFA,KApFA,gDAsFA,EAtFA,mDAuFA,EAvFA,qDAwFA;AACA,MAAA,IAAA,EAAA;AACA,QAAA,MAAA,EAAA;AADA,OADA;AAGA;AACA,MAAA,SAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,IAAA,EAAA,OAAA;AAAA,QAAA,SAAA,EAAA;AAAA,OADA;AAEA;;;;;;;AAOA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,IAAA,EAAA,OAAA;AAAA,QAAA,SAAA,EAAA;AAAA,OATA,EAUA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,KAAA,EAAA,SAFA;AAGA,QAAA,IAAA,EAAA,MAHA;AAIA,QAAA,QAAA,EAAA,WAJA;AAKA,QAAA,MAAA,EAAA;AALA,OAVA,EAiBA;AACA,QAAA,KAAA,EAAA,QADA;AAEA,QAAA,KAAA,EAAA,QAFA;AAGA,QAAA,IAAA,EAAA,OAHA;AAIA,QAAA,SAAA,EAAA;AAJA,OAjBA,EAuBA;AACA,QAAA,KAAA,EAAA,QADA;AAEA,QAAA,KAAA,EAAA,QAFA;AAGA,QAAA,IAAA,EAAA,OAHA;AAIA,QAAA,SAAA,EAAA;AAJA,OAvBA,EA6BA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,IAAA,EAAA,OAAA;AAAA,QAAA,SAAA,EAAA;AAAA,OA7BA;AAJA,KAxFA,2DA4HA;AACA,MAAA,KAAA,EAAA;AACA,QAAA,QAAA,EAAA,EADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,KAAA,EAAA,CAHA;AAIA,QAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,OADA;AAOA,MAAA,SAAA,EAAA,EAPA;AAQA,MAAA,WAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,IAAA,EAAA,IAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OADA;AAEA;AACA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,IAAA,EAAA,MAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,IAAA,EAAA,MAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OAJA,EAKA;AAAA,QAAA,KAAA,EAAA,QAAA;AAAA,QAAA,IAAA,EAAA,QAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OALA,EAMA;AAAA,QAAA,KAAA,EAAA,QAAA;AAAA,QAAA,IAAA,EAAA,QAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OANA,EAOA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,IAAA,EAAA,MAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OAPA,CARA;AAiBA,MAAA,MAAA,EAAA;AAAA,QAAA,QAAA,EAAA,IAAA;AAAA,QAAA,YAAA,EAAA;AAAA;AAjBA,KA5HA,iDA+IA;AACA,MAAA,EAAA,EAAA,EADA;AAEA,MAAA,IAAA,EAAA,EAFA;AAGA,MAAA,IAAA,EAAA,EAHA;AAIA,MAAA,MAAA,EAAA,EAJA;AAKA,MAAA,IAAA,EAAA,EALA;AAMA,MAAA,MAAA,EAAA;AANA,KA/IA,qDAuJA,EAvJA;AAyJA,GA7JA;AA8JA,EAAA,OA9JA,qBA8JA;AACA;AACA,SAAA,OAAA;AACA,GAjKA;AAkKA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,UAFA,sBAEA,IAFA,EAEA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,GADA,GACA;AACA,kBAAA,KAAA,EAAA,IAAA,CAAA,WADA;AAEA,kBAAA,OAAA,EAAA,CAFA;AAGA,kBAAA,MAAA,EAAA,IAAA,CAAA,WAAA,KAAA,UAAA,GAAA,CAAA,GAAA;AAHA,iBADA;AAMA,gBAAA,GAAA,CAAA,MAAA,GAAA,GAAA;AACA,gBAAA,GAAA,CAAA,MAAA,GAAA,IAAA,CAAA,QAAA;AAPA;AAAA,uBAQA,yBAAA,GAAA,CARA;;AAAA;AAAA;AAQA,gBAAA,IARA,qBAQA,IARA;;AAAA,sBASA,IAAA,KAAA,MATA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAUA,KAAA,CAAA,OAAA,EAVA;;AAAA;AAAA;AAAA;;AAAA;AAYA,gBAAA,KAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AAZA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA,KAhBA;AAiBA;AACA,IAAA,aAlBA,2BAkBA;AACA,UAAA,KAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,EAAA;AACA,aAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,CAAA,WAAA;AACA;;AACA,WAAA,MAAA,GAAA,KAAA;AACA,KAvBA;AAwBA,IAAA,cAxBA,4BAwBA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,WAAA,EAAA,GAAA,IAAA;AACA,KA3BA;AA4BA;AACA,IAAA,kBA7BA,8BA6BA,MA7BA,EA6BA;AACA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,MAAA,MAAA,CAAA,OAAA,CAAA,UAAA,CAAA,EAAA;AACA,QAAA,GAAA,IAAA,CAAA,CAAA,KAAA,GAAA,GAAA;AACA,OAFA,EAHA,CAMA;;AACA,MAAA,GAAA,GAAA,GAAA,CAAA,MAAA,CAAA,CAAA,EAAA,GAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,GAAA;AACA,KAtCA;AAuCA;AACA,IAAA,OAxCA,mBAwCA,MAxCA,EAwCA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,MAAA,CAAA,MAAA,+DAAA,MAAA,CAAA,MAAA,GAAA,MAAA;AACA,gBAAA,KAHA,GAGA,MAAA,CAAA,MAHA;AAIA,gBAAA,MAAA,CAAA,OAAA,GAAA,IAAA;AACA,gBAAA,KAAA,CAAA,OAAA,GAAA,CAAA;AAAA,kBAAA,IAAA,EAAA,YAAA;AAAA,kBAAA,GAAA,EAAA;AAAA,iBAAA,CAAA;AALA;AAAA,uBAMA,uBAAA,KAAA,CANA;;AAAA;AAAA;AAMA,gBAAA,IANA,kBAMA,IANA;AAMA,gBAAA,IANA,kBAMA,IANA;;AAOA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA,kBAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA;;AAXA;AAAA;;AAAA;AAAA;AAAA;AAaA,gBAAA,OAAA,CAAA,GAAA;;AAbA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA,KAvDA;AAwDA;AACA,IAAA,SAzDA,qBAyDA,GAzDA,EAyDA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAEA,uBAAA;AAAA,kBAAA,KAAA,EAAA,GAAA,CAAA;AAAA,iBAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,kBAEA,IAFA;AAEA,gBAAA,IAFA,kBAEA,IAFA;;AAGA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,aAAA,CAAA,QAAA,GAAA,IAAA;AACA;;AALA;AAAA;;AAAA;AAAA;AAAA;AAOA,gBAAA,OAAA,CAAA,GAAA;;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,KAlEA;AAmEA;AACA,IAAA,SApEA,uBAoEA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,aAAA,CAAA,QAAA,GAAA,EAAA;AACA,gBAAA,MAAA,CAAA,KAAA,GAAA,YAAA;AACA,gBAAA,MAAA,CAAA,UAAA,GAAA,KAAA;;AAHA,qBAIA,MAAA,CAAA,MAJA;AAAA;AAAA;AAAA;;AAKA,gBAAA,MAAA,CAAA,IAAA,GAAA,MAAA,CAAA,MAAA;AALA;AAAA,uBAMA,uBAAA;AAAA,kBAAA,KAAA,EAAA,MAAA,CAAA,IAAA,CAAA;AAAA,iBAAA,CANA;;AAAA;AAAA;AAMA,gBAAA,IANA,mBAMA,IANA;AAMA,gBAAA,IANA,mBAMA,IANA;;AAOA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,aAAA,CAAA,QAAA,GAAA,IAAA;;AACA,kBAAA,MAAA,CAAA,aAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,CAAA,EAAA;AACA,oBAAA,CAAA,CAAA,KAAA,GAAA,EAAA;AACA,mBAFA;AAGA;;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,KAAA,GAAA,EAAA;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,GAAA,GAAA,EAAA;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,EAAA,GAAA,EAAA;AAfA;AAAA;;AAAA;AAiBA,gBAAA,MAAA,CAAA,IAAA,GAAA,EAAA;;AAjBA;AAmBA,gBAAA,MAAA,CAAA,aAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,GAAA,EAAA;;AApBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBA,KAzFA;AA0FA;AACA,IAAA,SA3FA,qBA2FA,GA3FA,EA2FA;AACA,WAAA,SAAA,CAAA,GAAA;AACA,WAAA,KAAA,GAAA,YAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,KAjGA;AAkGA;AACA,IAAA,UAnGA,sBAmGA,GAnGA,EAmGA;AACA,WAAA,SAAA,CAAA,GAAA;AACA,WAAA,KAAA,GAAA,YAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,KAzGA;AA0GA;AACA,IAAA,SA3GA,uBA2GA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,MAAA,CAAA,KAAA,CAAA,MAAA,EAAA,QAAA;AAAA,oGAAA,kBAAA,KAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA,iCACA,KADA;AAAA;AAAA;AAAA;;AAAA;AAGA,4BAAA,MAAA,CAAA,IAAA,CAAA,QAAA,GAAA,MAAA,CAAA,aAAA,CAAA,QAAA;AACA,4BAAA,MAAA,CAAA,IAAA,CAAA,SAAA,GAAA,MAAA,CAAA,GAAA;AAJA;AAAA,mCAKA,4BAAA,MAAA,CAAA,IAAA,CALA;;AAAA;AAAA;AAKA,4BAAA,IALA,uBAKA,IALA;AAKA,4BAAA,IALA,uBAKA,IALA;;AAAA,kCAMA,IAAA,KAAA,MANA;AAAA;AAAA;AAAA;;AAOA;AACA,4BAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;AACA,4BAAA,MAAA,CAAA,aAAA,GAAA,KAAA;AATA;AAAA,mCAUA,MAAA,CAAA,OAAA,EAVA;;AAAA;AAWA,4BAAA,MAAA,CAAA,QAAA,GAAA,OAAA;AACA,4BAAA,MAAA,CAAA,gBAAA,GAAA,IAAA;AACA,4BAAA,MAAA,CAAA,UAAA,GAAA,IAAA;AACA,4BAAA,MAAA,CAAA,OAAA,GAAA,EAAA,CAdA,CAeA;;AACA,4BAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA,mCAAA,KAAA,CAAA,SAAA,EAAA,aAAA;AACA,6BAFA;;AAGA,4BAAA,MAAA,CAAA,OAAA,CAAA,IAAA,GAAA,IAAA,CAAA,IAAA;AACA,4BAAA,MAAA,CAAA,OAAA,CAAA,IAAA,GAAA,IAAA,CAAA,IAAA;AACA,4BAAA,MAAA,CAAA,OAAA,CAAA,IAAA,GAAA,IAAA,CAAA,KAAA;AACA,4BAAA,MAAA,CAAA,OAAA,CAAA,MAAA,GAAA,GAAA;AACA,4BAAA,MAAA,CAAA,OAAA,CAAA,GAAA,GAAA,MAAA,CAvBA,CAwBA;;AACA,4BAAA,MAAA,CAAA,OAAA,CAAA,EAAA,GAAA,CAAA;AACA,4BAAA,MAAA,CAAA,gBAAA,CAAA,QAAA,GAAA,MAAA,CAAA,aAAA,CAAA,QAAA;AACA,4BAAA,MAAA,CAAA,OAAA,CAAA,IAAA,GAAA,MAAA,CAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,4BAAA,MAAA,CAAA,OAAA,CAAA,OAAA,GAAA,MAAA,CAAA,aAAA,CAAA,QAAA,CAAA,MAAA;;AA5BA;AAAA;AAAA;;AAAA;AAAA;AAAA;AA+BA,4BAAA,OAAA,CAAA,GAAA;;AA/BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAA;;AAAA;AAAA;AAAA;AAAA,oBADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoCA,KA/IA;AAgJA;AACA,IAAA,UAjJA,wBAiJA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,OAAA,CAAA,QAAA,GAAA,MAAA,CAAA,gBAAA,CAAA,QAAA;AACA,gBAAA,MAAA,CAAA,OAAA,CAAA,SAAA,GAAA,MAAA,CAAA,GAAA,CAFA,CAGA;AACA;AACA;AACA;;AACA,4CAAA,MAAA,CAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,sBAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,oBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,oBAAA,MAAA,CAAA,gBAAA,GAAA,KAAA,CAFA,CAGA;;AACA,oBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,oBAAA,MAAA,CAAA,OAAA;AACA;AACA,iBARA;;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA,KAjKA;AAmKA;AACA,IAAA,SApKA,uBAoKA;AAAA;;AACA,WAAA,OAAA,CAAA,QAAA,GAAA,KAAA,gBAAA,CAAA,QAAA;AACA,WAAA,OAAA,CAAA,SAAA,GAAA,KAAA,GAAA;AACA,qCAAA,KAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA;AACA,UAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,UAAA,MAAA,CAAA,OAAA;;AACA,cAAA,IAAA,GAAA,GAAA,CAAA,IAAA;AACA,UAAA,MAAA,CAAA,gBAAA,GAAA,KAAA;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA,CAAA,KAAA;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,UAAA,MAAA,CAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,IAAA,GAAA,QAAA;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,EAAA,GAAA,IAAA,CAAA,GAAA;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,aAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,SAAA,GAAA,qBAAA;AACA,UAAA,MAAA,CAAA,MAAA,GAAA,IAAA;AACA;AACA,OAlBA;AAmBA,KA1LA;AA4LA,IAAA,OA5LA,qBA4LA;AAAA;;AACA,WAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,cAAA;AACA,YAAA,MAAA,CAAA,IAAA,CAAA,QAAA,GAAA,MAAA,CAAA,aAAA,CAAA,QAAA;AACA,YAAA,MAAA,CAAA,IAAA,CAAA,SAAA,GAAA,MAAA,CAAA,GAAA;AACA,wCAAA,MAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA,eAHA,CAIA;;;AACA,cAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,cAAA,MAAA,CAAA,OAAA;;AACA,cAAA,MAAA,CAAA,aAAA,GAAA,KAAA;AACA,aARA;AASA,WAZA,CAYA,OAAA,CAAA,EAAA;AACA,YAAA,OAAA,CAAA,GAAA,CAAA,CAAA;AACA;AACA;AACA,OAlBA;AAmBA,KAhNA;AAiNA;AACA,IAAA,SAlNA,qBAkNA,GAlNA,EAkNA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAKA,IALA,CAKA,YAAA;AACA,wCAAA,CAAA,GAAA,CAAA,KAAA,CAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,wBAAA,IAAA,SAAA,IAAA;;AACA,wBAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,SADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA,EADA,CAKA;;;AACA,sBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,sBAAA,MAAA,CAAA,OAAA;AACA,qBARA,MAQA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,OADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;AAIA;AACA,mBAfA;AAgBA,iBAtBA,EAuBA,KAvBA,CAuBA,YAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA;AACA,oBAAA,IAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;AAIA,iBA5BA;;AADA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA8BA,KAhPA;AAiPA;AACA,IAAA,YAlPA,0BAkPA;AACA,UAAA,GAAA,GAAA;AACA,QAAA,KAAA,EAAA,EADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA;AAKA,WAAA,aAAA,CAAA,QAAA,CAAA,IAAA,CAAA,GAAA;AACA,WAAA,aAAA,CAAA,GAAA,GAAA,GAAA;AACA,WAAA,OAAA,CAAA,IAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,WAAA,OAAA,CAAA,OAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,KA5PA;AA6PA;AACA,IAAA,YA9PA,wBA8PA,KA9PA,EA8PA,GA9PA,EA8PA;AACA,UAAA,GAAA,CAAA,KAAA,EAAA;AACA,aAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA,KAAA;AACA;;AACA,WAAA,aAAA,CAAA,QAAA,CAAA,MAAA,CAAA,KAAA,EAAA,CAAA;AACA,WAAA,OAAA,CAAA,IAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,WAAA,OAAA,CAAA,OAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,KArQA;AAsQA;AACA,IAAA,eAvQA,6BAuQA;AACA,UAAA,GAAA,GAAA;AACA,QAAA,KAAA,EAAA,IADA;AAEA;AACA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,GAAA,EAAA,EAJA;AAKA,QAAA,IAAA,EAAA,EALA;AAMA,QAAA,IAAA,EAAA,EANA;AAOA,QAAA,IAAA,EAAA;AAPA,OAAA;AASA,WAAA,gBAAA,CAAA,QAAA,CAAA,IAAA,CAAA,GAAA;AACA,WAAA,gBAAA,CAAA,GAAA,GAAA,GAAA;AACA,WAAA,OAAA,CAAA,IAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,WAAA,OAAA,CAAA,OAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,KArRA;AAsRA;AACA,IAAA,eAvRA,2BAuRA,KAvRA,EAuRA,GAvRA,EAuRA;AACA,WAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA,KAAA;AACA,WAAA,gBAAA,CAAA,QAAA,CAAA,MAAA,CAAA,KAAA,EAAA,CAAA;AACA,WAAA,OAAA,CAAA,IAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,WAAA,OAAA,CAAA,OAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,KA5RA;AA6RA;AACA,IAAA,KA9RA,mBA8RA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,KAhSA;AAiSA;AACA,IAAA,QAlSA,sBAkSA;AACA,WAAA,gBAAA,GAAA,KAAA;AACA,KApSA;AAqSA;AACA,IAAA,YAtSA,wBAsSA,SAtSA,EAsSA;AACA,WAAA,MAAA,GAAA,SAAA,CAAA,MAAA,GAAA,CAAA,mCAAA,SAAA,CAAA,CAAA,CAAA,IAAA,SAAA;AACA,WAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,KAAA;AAAA,OAAA,CAAA,CAFA,CAGA;AACA;;AACA,WAAA,UAAA,GAAA,SAAA;AACA,KA5SA;AA6SA;AACA,IAAA,QA9SA,sBA8SA;AACA,WAAA,MAAA,GAAA;AACA,QAAA,EAAA,EAAA,EADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,MAAA,EAAA,EAJA;AAKA,QAAA,IAAA,EAAA,EALA;AAMA,QAAA,MAAA,EAAA;AANA,OAAA;AAQA,KAvTA;AAwTA;AACA,IAAA,6BAzTA,yCAyTA,MAzTA,EAyTA;AACA,WAAA,cAAA,GAAA,MAAA;AACA,KA3TA;AA4TA;AACA,IAAA,iBA7TA,+BA6TA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,KA/TA;AAgUA;AACA,IAAA,UAjUA,wBAiUA;AACA,UAAA,MAAA,GAAA;AACA,QAAA,IAAA,EAAA,KAAA,MADA;AAEA,QAAA,GAAA,EAAA;AAFA,OAAA;AAIA,UAAA,QAAA,GAAA,YAAA;;AACA,UAAA,CAAA,KAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,QAAA,MAAA,CAAA,IAAA,GAAA,KAAA,MAAA;AACA,0CAAA,MAAA,EAAA,QAAA;AACA,OAHA,MAGA;AACA,QAAA,MAAA,CAAA,IAAA,GAAA,KAAA,UAAA;AACA,6CAAA,MAAA,EAAA,QAAA;AACA;AACA;AA9UA;AAlKA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\" style=\"padding:1px\">\n    <!--    <el-white>-->\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n      @handleReset=\"getReset\"\n    />\n    <!--    </el-white>-->\n\n    <div>\n      <el-white class=\"button-group\">\n        <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n          <el-col\n            style=\"\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n          \"\n          >\n            <div style=\"margin-bottom: 8px;\">\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-plus\"\n                @click=\"getInster\"\n                v-hasPermi=\"['xldzczml:button:add']\"\n                >新增\n              </el-button>\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-download\"\n                @click=\"exportWord\"\n                >导出</el-button\n              >\n            </div>\n          </el-col>\n        </el-row>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"selectChange\"\n          height=\"65vh\"\n          v-loading=\"loading\"\n        >\n          <el-table-column\n            slot=\"table_eight\"\n            align=\"center\"\n            fixed=\"right\"\n            style=\"display: block\"\n            label=\"操作\"\n            width=\"160\"\n            :resizable=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                @click=\"getDetails(scope.row)\"\n                title=\"详情\"\n                class=\"el-icon-view\"\n              ></el-button>\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                v-if=\"(scope.row.createBy === currentUser || hasSuperRole) && !scope.row.czp\"\n                @click=\"getUpdate(scope.row)\"\n                title=\"编辑\"\n                class=\"el-icon-edit\"\n              >\n              </el-button>\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                v-if=\"(scope.row.createBy === currentUser || hasSuperRole) && !scope.row.czp\"\n                @click=\"deleteRow(scope.row)\"\n                title=\"删除\"\n                class=\"el-icon-delete\"\n              >\n              </el-button>\n            </template>\n          </el-table-column>\n        </comp-table>\n      </el-white>\n    </div>\n\n    <!-- 详情/新增/修改  倒闸操作命令页面-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"50%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <div>\n          <el-row>\n            <el-col :span=\"12\">\n              <el-form-item label=\"编号：\" prop=\"bh\">\n                <el-input\n                  v-model=\"form.bh\"\n                  :disabled=\"true\"\n                  placeholder=\"保存后自动生成\"\n                />\n              </el-form-item>\n            </el-col>\n            <!--<el-col :span=\"12\">\n              <el-form-item label=\"状态\" prop=\"status\">\n                <el-input v-model=\"form.status\" :disabled=\"true\" placeholder=\"请输入状态\"/>\n              </el-form-item>\n            </el-col>-->\n            <el-col :span=\"12\">\n              <el-form-item label=\"线路名称\" prop=\"xlmc\">\n                <el-input\n                  v-model=\"form.xlmc\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请输入线路名称\"\n                  v-on:click.native=\"sysbSelectedClick()\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"通知时间\" prop=\"tzsj\">\n                <el-date-picker\n                  v-model=\"form.tzsj\"\n                  :disabled=\"isDisabled\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"预备人下令人\" prop=\"yblxlr\">\n                <el-input\n                  v-model=\"form.yblxlr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请输入预备人下令人\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"预备令接令人\" prop=\"ybljlr\">\n                <el-input\n                  v-model=\"form.ybljlr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请输入预备令接令人\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"倒闸操作时间\" prop=\"dzczsj\">\n                <el-date-picker\n                  v-model=\"form.dzczsj\"\n                  :disabled=\"isDisabled\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  :placeholder=\"isDisabled ? '' : '选择日期时间'\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"恢复操作时间\" prop=\"hfczsj\">\n                <el-date-picker\n                  v-model=\"form.hfczsj\"\n                  :disabled=\"isDisabled\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  :placeholder=\"isDisabled ? '' : '选择日期时间'\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <el-col :span=\"24\">\n              <el-form-item label=\"工作名称\" prop=\"gzmc\">\n                <el-input\n                  type=\"textarea\"\n                  :rows=\"2\"\n                  v-model=\"form.gzmc\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请输入工作名称\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n        <!--预览信息-->\n        <el-dialog :title=\"titleyl\" :visible.sync=\"yl\" append-to-body>\n          <el-table\n            :data=\"propTableData.colFirst\"\n            height=\"200\"\n            border\n            stripe\n            style=\"width: 100%\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <el-table-column\n              align=\"center\"\n              prop=\"xh\"\n              width=\"100px\"\n              label=\"序号\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <el-input\n                  placeholder=\"请输入序号\"\n                  v-model=\"scope.row.xh\"\n                  :disabled=\"true\"\n                ></el-input>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作任务\">\n              <template slot-scope=\"scope\">\n                <el-input\n                  placeholder=\"操作任务\"\n                  v-model=\"scope.row.czrw\"\n                  :disabled=\"true\"\n                ></el-input>\n              </template>\n            </el-table-column>\n          </el-table>\n        </el-dialog>\n        <!--列表-->\n        <div>\n          <el-white class=\"mb8 pull-right\">\n            <el-button\n              type=\"info\"\n              @click=\"handleYlChange\"\n              style=\"text-align: right\"\n              >预览</el-button\n            >\n          </el-white>\n          <!--第一个表格-->\n          <el-table\n            :data=\"propTableData.colFirst\"\n            height=\"200\"\n            border\n            stripe\n            style=\"width: 100%\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <el-table-column\n              align=\"center\"\n              prop=\"xh\"\n              width=\"100px\"\n              label=\"顺序号\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <el-input-number\n                  size=\"small\"\n                  v-model=\"scope.row.xh\"\n                  :min=\"1\"\n                  :precision=\"0\"\n                  controls-position=\"right\"\n                  :disabled=\"isDisabled\"\n                ></el-input-number>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作任务\">\n              <template slot-scope=\"scope\">\n                <el-input\n                  placeholder=\"操作任务\"\n                  v-model=\"scope.row.czrw\"\n                  :disabled=\"isDisabled\"\n                ></el-input>\n              </template>\n            </el-table-column>\n\n            <el-table-column align=\"center\" width=\"80\">\n              <template slot=\"header\" slot-scope=\"scope\">\n                <el-button\n                  size=\"small\"\n                  type=\"primary\"\n                  icon=\"el-icon-plus\"\n                  :disabled=\"isDisabled\"\n                  @click=\"listFirstAdd(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n              <template slot-scope=\"scope\">\n                <el-button\n                  type=\"text\"\n                  icon=\"el-icon-delete\"\n                  :disabled=\"isDisabled\"\n                  @click=\"listFirstDel(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">关 闭</el-button>\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"saveRow\"\n          >保 存</el-button\n        >\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"createCzp\"\n          >开操作票\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 详情/新增/修改  倒闸操作票页面-->\n    <el-dialog\n      :title=\"titleCzp\"\n      :visible.sync=\"isShowDetailsCzp\"\n      width=\"50%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"130px\" ref=\"formCzp\" :model=\"formCzp\">\n        <div>\n          <div>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"线路名称\" prop=\"xlmc\">\n                  <el-input\n                    v-model=\"formCzp.xlmc\"\n                    :disabled=\"isDisabled\"\n                    placeholder=\"请输入线路名称\"\n                  />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"24\">\n                <el-form-item label=\"工作名称\" prop=\"gzmc\">\n                  <el-input\n                    type=\"textarea\"\n                    :rows=\"2\"\n                    v-model=\"form.gzmc\"\n                    :disabled=\"isDisabled\"\n                    placeholder=\"请输入工作名称\"\n                  />\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </div>\n\n          <!--列表-->\n          <div>\n            <!--第一个表格-->\n            <el-table\n              :data=\"propTableDataCzp.colFirst\"\n              height=\"200\"\n              border\n              stripe\n              style=\"width: 100%\"\n              :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n            >\n              <el-table-column\n                align=\"center\"\n                prop=\"xh\"\n                width=\"100px\"\n                label=\"序号\"\n                sortable\n              >\n                <template slot-scope=\"scope\">\n                  <el-input\n                    placeholder=\"请输入序号\"\n                    v-model=\"scope.row.xh\"\n                    :disabled=\"isDisabled\"\n                  ></el-input>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n                <template slot-scope=\"scope\">\n                  <el-input\n                    placeholder=\"请输入操作项目\"\n                    v-model=\"scope.row.czrw\"\n                    :disabled=\"isDisabled\"\n                  ></el-input>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" width=\"80\">\n                <template slot=\"header\" slot-scope=\"scope\">\n                  <el-button\n                    size=\"small\"\n                    type=\"primary\"\n                    icon=\"el-icon-plus\"\n                    :disabled=\"isDisabled\"\n                    @click=\"listFirstAddCzp(scope.$index, scope.row)\"\n                  ></el-button>\n                </template>\n                <template slot-scope=\"scope\">\n                  <el-button\n                    type=\"text\"\n                    icon=\"el-icon-delete\"\n                    :disabled=\"isDisabled\"\n                    @click=\"listFirstDelCzp(scope.$index, scope.row)\"\n                  ></el-button>\n                </template>\n              </el-table-column>\n            </el-table>\n          </div>\n        </div>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"closeCzp\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"saveRowCzp\">确 认</el-button>\n        <el-button type=\"primary\" @click=\"submitCzp\">上 报</el-button>\n      </div>\n    </el-dialog>\n\n    <!--线路选择组件-->\n    <el-dialog\n      title=\"线路选择\"\n      :visible.sync=\"isShowXlDialog\"\n      width=\"20%\"\n      v-if=\"isShowXlDialog\"\n      v-dialogDrag\n    >\n      <sdxl-selected\n        @closeSysbSelectDialog=\"handleControlSysbSelectDialog\"\n        @handleAcceptSbData=\"handleAcceptSbData\"\n      ></sdxl-selected>\n    </el-dialog>\n\n    <!--  工作流  -->\n    <activiti\n      ref=\"activiti\"\n      :processData=\"processData\"\n      :isShow=\"isShow\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n    ></activiti>\n  </div>\n</template>\n\n<script>\nimport {\n  getList,\n  queryZb,\n  saveOrUpdate,\n  remove,\n  exportWordByselection,\n  exportWordByparams\n} from \"@/api/yxgl/sdyxgl/sddzczml\";\nimport { saveOrUpdates } from \"@/api/yxgl/sdyxgl/sddzczp\";\nimport ElectronicAuthDialog from \"com/ElectronicAuthDialog\";\nimport sdxlSelected from \"@/views/dagangOilfield/xjgl/sdxj/sdxlSelected1\";\nimport { saveOrUpdateCzp } from \"@/api/yxgl/bdyxgl/bddzczml\";\nimport activiti from \"com/activiti_czp\";\nimport { updateById } from \"@/api/yxgl/bdyxgl/bddzczp\";\nimport { getUUID } from \"@/utils/ruoyi\";\n\nexport default {\n  name: \"dzczml\",\n  components: { ElectronicAuthDialog, sdxlSelected, activiti },\n  data() {\n    return {\n      hasSuperRole:this.$store.getters.hasSuperRole,\n      // 多选框选中的id\n      ids: [],\n      // 多选框选选中的数据\n      selectData: [],\n      // 非单个禁用\n      single: undefined,\n      // 非多个禁用\n      multiple: true,\n      loading: false,\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"czplc\",\n        businessKey: \"\",\n        businessType: \"倒闸操作票\",\n        variables: {},\n        defaultFrom: true,\n        nextUser: \"\",\n        processType: \"complete\"\n      },\n      isShow: false,\n      //弹出框标题\n      activitiOption: { title: \"上报\" },\n      rules: {\n        xlmc: [\n          { required: true, message: \"线路名称不能为空\", trigger: \"select\" }\n        ],\n        tzsj: [\n          { required: true, message: \"通知时间不能为空\", trigger: \"blur\" }\n        ],\n        yblxlr: [\n          { required: true, message: \"预备人下令人不能为空\", trigger: \"blur\" }\n        ],\n        ybljlr: [\n          { required: true, message: \"预备人接令人不能为空\", trigger: \"blur\" }\n        ]\n      }, //表单必填校验\n      titleyl: \"\",\n      // 获取当前登录人账号\n      currentUser: this.$store.getters.name,\n      yl: false,\n      //线路选择弹出\n      isShowXlDialog: false,\n      // 多选框选中的id\n      ids: [],\n      //form表单\n      form: {\n        bh: \"\",\n        xlmc: \"\",\n        tzsj: \"\",\n        yblxlr: \"\",\n        czrw: \"\",\n        ybljlr: \"\",\n        status: \"\",\n        gzmc: \"\"\n      },\n      // 操作票form表单\n      formCzp: {\n        bh: \"\",\n        xlmc: \"\",\n        czr: \"\",\n        jhr: \"\",\n        sdshr: \"\",\n        lx: 1, //输电\n        colFirst: []\n      },\n      //弹出框中表格数据\n      propTableData: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //弹出框中表格数据\n      propTableDataCzp: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //操作命令详情弹框是否显示\n      isShowDetails: false,\n      //操作票弹框是否显示\n      isShowDetailsCzp: false,\n      //是否禁用\n      isDisabled: false,\n      authIsShow: false,\n      isSubmit: false,\n      //标题\n      title: \"\",\n      titleCzp: \"\",\n      filterInfo: {\n        data: {\n          column: \"\"\n        }, //查询条件\n        fieldList: [\n          { label: \"编号\", value: \"bh\", type: \"input\", clearable: true },\n          /*{\n            label: '状态',\n            value: 'status',\n            type: 'select',\n            clearable: true,\n            options: [{ label: '已执行', value: '已执行' }, { label: '未执行', value: '未执行' }]\n          },*/\n          { label: \"线路名称\", value: \"xlmc\", type: \"input\", clearable: true },\n          {\n            label: \"通知时间\",\n            value: \"tzsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          {\n            label: \"预备令下令人\",\n            value: \"yblxlr\",\n            type: \"input\",\n            clearable: true\n          },\n          {\n            label: \"预备令接令人\",\n            value: \"ybljlr\",\n            type: \"input\",\n            clearable: true\n          },\n          { label: \"工作名称\", value: \"gzmc\", type: \"input\", clearable: true }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"编号\", prop: \"bh\", minWidth: \"120\" },\n          /*{ label: '状态', prop: 'status', minWidth: '60' },*/\n          { label: \"线路名称\", prop: \"xlmc\", minWidth: \"120\" },\n          { label: \"通知时间\", prop: \"tzsj\", minWidth: \"160\" },\n          { label: \"预备令下令人\", prop: \"yblxlr\", minWidth: \"120\" },\n          { label: \"预备令接令人\", prop: \"ybljlr\", minWidth: \"120\" },\n          { label: \"工作名称\", prop: \"gzmc\", minWidth: \"120\" }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        bh: \"\",\n        xlmc: \"\",\n        tzsj: \"\",\n        yblxlr: \"\",\n        czrw: \"\",\n        ybljlr: \"\"\n      },\n      selectRows: []\n    };\n  },\n  mounted() {\n    //列表查询\n    this.getData();\n  },\n  methods: {\n    //工作流回传数据\n    async todoResult(data) {\n      let row = {\n        objId: data.businessKey,\n        isStart: 1,\n        isBack: data.processType === \"rollback\" ? 1 : 0\n      };\n      row.status = \"2\";\n      row.fgsspr = data.nextUser;\n      let { code } = await updateById(row);\n      if (code === \"0000\") {\n        await this.getData();\n      } else {\n        this.$message.warning(\"操作失败\");\n      }\n    },\n    //关闭弹窗\n    closeActiviti() {\n      if (this.$refs.activiti.$refs.form) {\n        this.$refs.activiti.$refs.form.resetFields();\n      }\n      this.isShow = false;\n    },\n    handleYlChange() {\n      this.titleyl = \"查看操作项目\";\n      this.yl = true;\n    },\n    //线路选择接收数据\n    handleAcceptSbData(sbData) {\n      // this.form.xlmcbm = sbData.id;\n      let str = \"\";\n      sbData.forEach(e => {\n        str += e.label + \",\";\n      });\n      //去掉最后一个逗号\n      str = str.substr(0, str.length - 1);\n      this.form.xlmc = str;\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        this.loading = true;\n        param.mySorts = [{ prop: \"updateTime\", asc: false }];\n        const { data, code } = await getList(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          this.loading = false;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //根据主表查询子表方法\n    async getListZb(row) {\n      try {\n        const { data, code } = await queryZb({ objId: row.objId });\n        if (code === \"0000\") {\n          this.propTableData.colFirst = data;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //新增按钮\n    async getInster() {\n      this.propTableData.colFirst = [];\n      this.title = \"输电倒闸操作命令新增\";\n      this.isDisabled = false;\n      if (this.single) {\n        this.form = this.single;\n        const { data, code } = await queryZb({ objId: this.form.objId });\n        if (code === \"0000\") {\n          this.propTableData.colFirst = data;\n          this.propTableData.colFirst.forEach(e => {\n            e.objId = \"\";\n          });\n        }\n        this.form.objId = \"\";\n        this.form.czp = \"\";\n        this.form.bh = \"\";\n      } else {\n        this.form = {};\n      }\n      this.isShowDetails = true;\n      this.form.status = \"\";\n    },\n    //修改按钮\n    getUpdate(row) {\n      this.getListZb(row);\n      this.title = \"输电倒闸操作命令修改\";\n      this.isDisabled = false;\n      this.form = { ...row };\n      this.isShowDetails = true;\n    },\n    //详情按钮\n    getDetails(row) {\n      this.getListZb(row);\n      this.title = \"输电倒闸操作命令详情\";\n      this.form = { ...row };\n      this.isDisabled = true;\n      this.isShowDetails = true;\n    },\n    //开操作票按钮\n    async createCzp() {\n      await this.$refs[\"form\"].validate(async valid => {\n        if (valid) {\n          try {\n            this.form.colFirst = this.propTableData.colFirst;\n            this.form.objIdList = this.ids;\n            let { code, data } = await saveOrUpdate(this.form);\n            if (code === \"0000\") {\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.isShowDetails = false;\n              await this.getData();\n              this.titleCzp = \"操作票开票\";\n              this.isShowDetailsCzp = true;\n              this.isDisabled = true;\n              this.formCzp = {};\n              //清除校验提示\n              this.$nextTick(function() {\n                this.$refs[\"formCzp\"].clearValidate();\n              });\n              this.formCzp.xlmc = data.xlmc;\n              this.formCzp.gzmc = data.gzmc;\n              this.formCzp.czml = data.objId;\n              this.formCzp.status = \"0\";\n              this.formCzp.fgs = \"3010\";\n              //输电\n              this.formCzp.lx = 1;\n              this.propTableDataCzp.colFirst = this.propTableData.colFirst;\n              this.formCzp.czxs = this.propTableData.colFirst.length;\n              this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n            }\n          } catch (e) {\n            console.log(e);\n          }\n        }\n      });\n    },\n    // 开操作票页 确认按钮\n    async saveRowCzp() {\n      this.formCzp.colFirst = this.propTableDataCzp.colFirst;\n      this.formCzp.objIdList = this.ids;\n      // let tableValid = this.propTableData.colFirst.some(item => !item.czrw)\n      // if (tableValid) {\n      //   this.$message.error(\"操作任务存在空项，请检查\");\n      // }\n      saveOrUpdates(this.formCzp).then(res => {\n        if (res.code === \"0000\") {\n          this.$message.success(\"操作成功\");\n          this.isShowDetailsCzp = false;\n          //重置page页从1开始\n          this.tableAndPageInfo.pager.pageResize = \"Y\";\n          this.getData();\n        }\n      });\n    },\n\n    //直接上报操作票\n    submitCzp() {\n      this.formCzp.colFirst = this.propTableDataCzp.colFirst;\n      this.formCzp.objIdList = this.ids;\n      saveOrUpdateCzp(this.formCzp).then(res => {\n        if (res.code === \"0000\") {\n          //重置page页从1开始\n          this.tableAndPageInfo.pager.pageResize = \"Y\";\n          this.getData();\n          let data = res.data;\n          this.isShowDetailsCzp = false;\n          this.processData.variables.pass = true;\n          this.processData.businessKey = data.objId;\n          this.processData.processType = \"complete\";\n          this.activitiOption.title = \"提交\";\n          this.processData.defaultFrom = true;\n          this.processData.rylx = \"分公司审核人\";\n          this.processData.dw = data.fgs;\n          this.processData.personGroupId = 14;\n          this.processData.routePath = \"/czpgl/xldzcz/dzczp\";\n          this.isShow = true;\n        }\n      });\n    },\n\n    saveRow() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          try {\n            this.form.colFirst = this.propTableData.colFirst;\n            this.form.objIdList = this.ids;\n            saveOrUpdate(this.form).then(res => {\n              if (res.code === \"0000\") {\n                this.$message.success(\"操作成功\");\n              }\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n              this.isShowDetails = false;\n            });\n          } catch (e) {\n            console.log(e);\n          }\n        }\n      });\n    },\n    //删除按钮\n    async deleteRow(row) {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove([row.objId]).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    //表格新增\n    listFirstAdd() {\n      let row = {\n        objId: \"\",\n        czrw: \"\",\n        uuid: getUUID()\n      };\n      this.propTableData.colFirst.push(row);\n      this.propTableData.sel = row;\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //表格删除\n    listFirstDel(index, row) {\n      if (row.objId) {\n        this.ids.push(row.objId);\n      }\n      this.propTableData.colFirst.splice(index, 1);\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //表格新增\n    listFirstAddCzp() {\n      let row = {\n        isSet: true,\n        // xh:'',\n        czrw: \"\",\n        xlr: \"\",\n        xlsj: \"\",\n        hlsj: \"\",\n        sfwc: \"\"\n      };\n      this.propTableDataCzp.colFirst.push(row);\n      this.propTableDataCzp.sel = row;\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //表格删除\n    listFirstDelCzp(index, row) {\n      this.ids.push(row.objId);\n      this.propTableDataCzp.colFirst.splice(index, 1);\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false;\n    },\n    //关闭弹窗\n    closeCzp() {\n      this.isShowDetailsCzp = false;\n    },\n    //选择行\n    selectChange(selection) {\n      this.single = selection.length > 0 ? { ...selection[0] } : undefined;\n      this.ids = selection.map(item => item.objId);\n      // this.single = selection.length !== 1\n      // this.multiple = !selection.length\n      this.selectData = selection;\n    },\n    //重置按钮\n    getReset() {\n      this.params = {\n        bh: \"\",\n        xlmc: \"\",\n        tzsj: \"\",\n        yblxlr: \"\",\n        czrw: \"\",\n        ybljlr: \"\"\n      };\n    },\n    //关闭线路弹窗\n    handleControlSysbSelectDialog(isShow) {\n      this.isShowXlDialog = isShow;\n    },\n    //线路选择事件\n    sysbSelectedClick() {\n      this.isShowXlDialog = true;\n    },\n    //导出word\n    exportWord() {\n      let params = {\n        data: this.params,\n        url: \"bzSddzczml\"\n      };\n      let fileName = \"线路倒闸操作命令记录\";\n      if (!this.selectData.length > 0) {\n        params.data = this.params;\n        exportWordByparams(params, fileName);\n      } else {\n        params.data = this.selectData;\n        exportWordByselection(params, fileName);\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.card-style {\n  background-color: #e0eff2;\n  line-height: 3vh;\n  border-radius: 3px;\n  font-weight: 800;\n  padding-left: 1vw;\n  margin-bottom: 3vh;\n}\n\n/*控制input输入框边框是否显示*/\n.elInput >>> .el-input__inner {\n  border: 0;\n}\n\n.center {\n  text-align: center; /*让div内部文字居中*/\n}\n</style>\n"], "sourceRoot": "src/views/dagangOilfield/czpgl/xldzcz"}]}