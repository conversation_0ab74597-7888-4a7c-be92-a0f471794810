{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\bddzcz\\dzczp.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\bddzcz\\dzczp.vue", "mtime": 1749014635777}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsKaW1wb3J0IGFwaSBmcm9tICJAL3V0aWxzL3JlcXVlc3QiOwppbXBvcnQgRWxJbWFnZVZpZXdlciBmcm9tICJlbGVtZW50LXVpL3BhY2thZ2VzL2ltYWdlL3NyYy9pbWFnZS12aWV3ZXIiOwppbXBvcnQgewogIGV4cG9ydFBkZiwKICBleHBvcnRXb3JkLAogIGdldEJkelNlbGVjdExpc3QsCiAgZ2V0Q3pwbXhMaXN0LAogIGdldExpc3RMc3AsCiAgcHJldmlld0ZpbGUsCiAgcmVtb3ZlLAogIHNhdmVPclVwZGF0ZSwKICB1cGRhdGVCeUlkCn0gZnJvbSAiQC9hcGkveXhnbC9iZHl4Z2wvYmRkemN6cCI7CmltcG9ydCBFbGVjdHJvbmljQXV0aERpYWxvZyBmcm9tICJjb20vRWxlY3Ryb25pY0F1dGhEaWFsb2ciOwovL+a1geeoiwppbXBvcnQgYWN0aXZpdGkgZnJvbSAiY29tL2FjdGl2aXRpX2N6cCI7CmltcG9ydCB0aW1lTGluZSBmcm9tICJjb20vdGltZUxpbmUiOwppbXBvcnQgeyBIaXN0b3J5TGlzdCB9IGZyb20gIkAvYXBpL2FjdGl2aXRpL3Byb2Nlc3NUYXNrIjsKaW1wb3J0IHsgZ2V0VXNlcnMgfSBmcm9tICJAL2FwaS9zeXN0ZW0vdXNlcmdyb3VwIjsKaW1wb3J0IHsgZ2V0VVVJRCB9IGZyb20gIkAvdXRpbHMvcnVveWkiOwppbXBvcnQgeyBnZXRGZ3NPcHRpb25zIH0gZnJvbSAiQC9hcGkveXhnbC9iZHl4Z2wvemJnbCI7CmltcG9ydCB7IGV4cG9ydFRvRXhjZWwsIGltcG9ydEZyb21FeGNlbCB9IGZyb20gIkAvY29tcG9uZW50cy9jb21tb24vZXhjZWwuanMiOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJkemN6cCIsCiAgY29tcG9uZW50czogeyBFbGVjdHJvbmljQXV0aERpYWxvZywgYWN0aXZpdGksIHRpbWVMaW5lLCBFbEltYWdlVmlld2VyIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICBoYXNTdXBlclJvbGU6IHRoaXMuJHN0b3JlLmdldHRlcnMuaGFzU3VwZXJSb2xlLAogICAgICBqbHJMaXN0OiBbXSwKICAgICAgLy/nirbmgIHkuIvmi4nmoYbmlbDmja4KICAgICAgc3RhdHVzT3B0aW9uczogWwogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAiMCIsCiAgICAgICAgICBsYWJlbDogIuaTjeS9nOelqOWhq+aKpSIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAiMSIsCiAgICAgICAgICBsYWJlbDogIuePree7hOWuoeaguCIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAiMiIsCiAgICAgICAgICBsYWJlbDogIuWIhuWFrOWPuOWuoeaguCIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAiMyIsCiAgICAgICAgICBsYWJlbDogIuaTjeS9nOelqOWKnue7kyIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAiNCIsCiAgICAgICAgICBsYWJlbDogIue7k+adnyIKICAgICAgICB9LHsgbGFiZWw6ICLkvZzlup8iLCB2YWx1ZTogIjciIH0KICAgICAgXSwKICAgICAgYnV0dG9uTmFtZVNob3c6IGZhbHNlLAogICAgICBidXR0b25OYW1lOiAiIiwKICAgICAgaXNEaXNhYmxlZEJqOiB0cnVlLAogICAgICAvL+e7hOe7h+e7k+aehOS4i+aLieaVsOaNrgogICAgICBvcmdhbml6YXRpb25TZWxlY3RlZExpc3Q6IFtdLAogICAgICBianI6ICIiLAogICAgICBjdXJyZW50VXNlcjogdGhpcy4kc3RvcmUuZ2V0dGVycy5uYW1lLAogICAgICAvL+W3peS9nOa1geS8oOWFpeWPguaVsAogICAgICBwcm9jZXNzRGF0YTogewogICAgICAgIHByb2Nlc3NEZWZpbml0aW9uS2V5OiAiY3pwc2giLAogICAgICAgIGJ1c2luZXNzS2V5OiAiIiwKICAgICAgICBidXNpbmVzc1R5cGU6ICLlgJLpl7jmk43kvZznpagiLAogICAgICAgIHZhcmlhYmxlczoge30sCiAgICAgICAgZGVmYXVsdEZyb206IHRydWUsCiAgICAgICAgbmV4dFVzZXI6ICIiLAogICAgICAgIHByb2Nlc3NUeXBlOiAiY29tcGxldGUiCiAgICAgIH0sCiAgICAgIC8vIOaYr+WQpuW3suaJp+ihjOS4i+aLieahhgogICAgICBzZnl6eExpc3Q6IFsKICAgICAgICB7IGxhYmVsOiAi5bey5omn6KGMIiwgdmFsdWU6ICLlt7LmiafooYwiIH0sCiAgICAgICAgeyBsYWJlbDogIuacquaJp+ihjCIsIHZhbHVlOiAi5pyq5omn6KGMIiB9CiAgICAgIF0sCiAgICAgIC8v5bel5L2c5rWB5by556qXCiAgICAgIGlzU2hvdzogZmFsc2UsCiAgICAgIC8v5rWB56iL5Zu+5p+l55yLCiAgICAgIG9wZW5Mb2FkaW5nSW1nOiBmYWxzZSwKICAgICAgaW1nU3JjOiAiIiwgLy/mtYHnqIvlm77mn6XnnIvlnLDlnYAKICAgICAgdGltZURhdGE6IFtdLAogICAgICB0aW1lTGluZVNob3c6IGZhbHNlLAogICAgICAvL+W8ueWHuuahhuagh+mimAogICAgICBhY3Rpdml0aU9wdGlvbjogeyB0aXRsZTogIuS4iuaKpSIgfSwKICAgICAgdGl0bGV5bDogIiIsCiAgICAgIGlzU2hvd1NoOiBmYWxzZSwKICAgICAgaXNTaFNob3dEZXRhaWxzOiBmYWxzZSwKICAgICAgeWw6IGZhbHNlLAogICAgICAvL+WbvueJh+WcsOWdgHVybAogICAgICBkaWFsb2dJbWFnZVVybDogIiIsCiAgICAgIC8v5bGV56S65Zu+54mHZGlhbG9n5o6n5Yi2CiAgICAgIGltZ0RpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBiZHpMaXN0OiBbXSwKICAgICAgaXNJbmRldGVybWluYXRlOiB0cnVlLAogICAgICAvLyDlpJrpgInmoYbpgInkuK3nmoRpZAogICAgICBpZHM6IFtdLAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICBzZWxlY3REYXRhOiBbXSwKICAgICAgLy/lvLnlh7rmoYbkuK3ooajmoLzmlbDmja4KICAgICAgcHJvcFRhYmxlRGF0YTogewogICAgICAgIHNlbDogbnVsbCwgLy8g6YCJ5Lit6KGMCiAgICAgICAgY29sRmlyc3Q6IFtdCiAgICAgIH0sCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgLy8ga3NzajogWwogICAgICAgIC8vICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn5pON5L2c5byA5aeL5pe26Ze05LiN6IO95Li656m6JywgdHJpZ2dlcjogJ2JsdXInfQogICAgICAgIC8vIF0sCiAgICAgICAgLy8ganNzajogWwogICAgICAgIC8vICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn5pON5L2c57uT5p2f5pe26Ze05LiN6IO95Li656m6JywgdHJpZ2dlcjogJ2NoYW5nZSd9CiAgICAgICAgLy8gXSwKICAgICAgICBmZ3M6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5YiG5YWs5Y+45LiN6IO95Li656m6IiwgdHJpZ2dlcjogInNlbGVjdCIgfV0sCiAgICAgICAgYmR6bWM6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlj5jnlLXnq5nkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAic2VsZWN0IiB9CiAgICAgICAgXSwKICAgICAgICBjenJ3OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5pON5L2c5Lu75Yqh5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIC8vIHhscjogWwogICAgICAgIC8vICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn5LiL5Luk5Lq65LiN6IO95Li656m6JywgdHJpZ2dlcjogJ2JsdXInfQogICAgICAgIC8vIF0sCiAgICAgICAgY3p4czogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaTjeS9nOmhueaVsOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICB5enhjenhzOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5bey5omn6KGM6aG55pWw5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHd6eGN6eHM6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmnKrmiafooYzpobnmlbDkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgLy9mb3Jt6KGo5Y2VCiAgICAgIGZvcm06IHsKICAgICAgICBiaDogIiIsCiAgICAgICAgYmR6bWM6ICIiLAogICAgICAgIGtzc2o6ICIiLAogICAgICAgIGpzc2o6ICIiLAogICAgICAgIGN6cnc6ICIiLAogICAgICAgIGN6cjogIiIsCiAgICAgICAgamhyOiAiIiwKICAgICAgICB4bHI6ICIiLAogICAgICAgIC8vIHNwcjogJycsCiAgICAgICAgc3RhdHVzOiAiIiwKICAgICAgICBseDogMiwgLy/lj5jnlLUKICAgICAgICBjb2xGaXJzdDogW10KICAgICAgfSwKICAgICAgLy/kuIrkvKDlm77niYfml7bnmoTmkLrluKbnmoTlhbbku5blj4LmlbAKICAgICAgdXBsb2FkSW1nRGF0YTogewogICAgICAgIGJ1c2luZXNzSWQ6ICIiIC8v5pC65bim55qE6KGo5Y2V5Li76ZSuaWQKICAgICAgfSwKICAgICAgLy/kuIrkvKDlm77niYfml7bnmoTor7fmsYLlpLQKICAgICAgaGVhZGVyOiB7fSwKICAgICAgLy/lm77niYdsaXN0CiAgICAgIGltZ0xpc3Q6IFtdLAogICAgICAvL+ivpuaDheW8ueahhuaYr+WQpuaYvuekugogICAgICBpc1Nob3dEZXRhaWxzOiBmYWxzZSwKICAgICAgLy/mmK/lkKbnpoHnlKgKICAgICAgaXNEaXNhYmxlZDogZmFsc2UsCiAgICAgIGNoZWNrZWRBbGw6IGZhbHNlLAogICAgICAvL+agh+mimAogICAgICB0aXRsZTogIiIsCiAgICAgIGZpbHRlckluZm86IHsKICAgICAgICBkYXRhOiB7CiAgICAgICAgICBibTogIiIsCiAgICAgICAgICBiZHptYzogIiIsCiAgICAgICAgICBjenNqQXJyOiBbXSwKICAgICAgICAgIGtzc2pBcnI6IFtdLAogICAgICAgICAgLy8ganNzakFycjogW10sCiAgICAgICAgICBjenJ3OiAiIiwKICAgICAgICAgIGN6cjogIiIsCiAgICAgICAgICBqaHI6ICIiLAogICAgICAgICAgeGxybWM6ICIiLAogICAgICAgICAgc3RhdHVzOiAiIgogICAgICAgICAgLy8gc3ByOiAnJwogICAgICAgIH0sIC8v5p+l6K+i5p2h5Lu2CiAgICAgICAgZmllbGRMaXN0OiBbCiAgICAgICAgICB7IGxhYmVsOiAi57yW5Y+3IiwgdmFsdWU6ICJibSIsIHR5cGU6ICJpbnB1dCIsIGNsZWFyYWJsZTogdHJ1ZSB9LAoKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLliIblhazlj7giLAogICAgICAgICAgICB2YWx1ZTogImZncyIsCiAgICAgICAgICAgIHR5cGU6ICJzZWxlY3QiLAogICAgICAgICAgICBvcHRpb25zOiBbXSwKICAgICAgICAgICAgY2xlYXJhYmxlOiB0cnVlCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIuWPmOeUteermeWQjeensCIsCiAgICAgICAgICAgIHZhbHVlOiAiYmR6bWMiLAogICAgICAgICAgICB0eXBlOiAic2VsZWN0IiwKICAgICAgICAgICAgY2xlYXJhYmxlOiB0cnVlLAogICAgICAgICAgICBvcHRpb25zOiBbXQogICAgICAgICAgfSwKICAgICAgICAgIHsgbGFiZWw6ICLmk43kvZzku7vliqEiLCB2YWx1ZTogImN6cnciLCB0eXBlOiAiaW5wdXQiLCBjbGVhcmFibGU6IHRydWUgfSwKICAgICAgICAgIHsgbGFiZWw6ICLmk43kvZzkuroiLCB2YWx1ZTogImN6ciIsIHR5cGU6ICJpbnB1dCIsIGNsZWFyYWJsZTogdHJ1ZSB9LAogICAgICAgICAgeyBsYWJlbDogIuebkeaKpOS6uiIsIHZhbHVlOiAiamhybWMiLCB0eXBlOiAiaW5wdXQiLCBjbGVhcmFibGU6IHRydWUgfSwKICAgICAgICAgIHsgbGFiZWw6ICLkuIvku6TkuroiLCB2YWx1ZTogInhscm1jIiwgdHlwZTogImlucHV0IiwgY2xlYXJhYmxlOiB0cnVlIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi54q25oCBIiwKICAgICAgICAgICAgdmFsdWU6ICJzdGF0dXMiLAogICAgICAgICAgICB0eXBlOiAiY2hlY2tib3giLAogICAgICAgICAgICBjaGVja2JveFZhbHVlOiBbXSwKICAgICAgICAgICAgY2xlYXJhYmxlOiB0cnVlLAogICAgICAgICAgICBvcHRpb25zOiBbCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgdmFsdWU6ICIwIiwKICAgICAgICAgICAgICAgIGxhYmVsOiAi5pON5L2c56Wo5aGr5oqlIgogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgdmFsdWU6ICIxIiwKICAgICAgICAgICAgICAgIGxhYmVsOiAi54+t57uE5a6h5qC4IgogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgdmFsdWU6ICIyIiwKICAgICAgICAgICAgICAgIGxhYmVsOiAi5YiG5YWs5Y+45a6h5qC4IgogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgdmFsdWU6ICIzIiwKICAgICAgICAgICAgICAgIGxhYmVsOiAi5pON5L2c56Wo5Yqe57uTIgogICAgICAgICAgICAgIH0seyBsYWJlbDogIuS9nOW6nyIsIHZhbHVlOiAiNyIgfQogICAgICAgICAgICBdCiAgICAgICAgICB9CiAgICAgICAgICAvLyB7bGFiZWw6ICflrqHnpajkuronLCB2YWx1ZTogJ3NwcicsIHR5cGU6ICdpbnB1dCcsIGNsZWFyYWJsZTogdHJ1ZX0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIHRhYmxlQW5kUGFnZUluZm86IHsKICAgICAgICBwYWdlcjogewogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMDBdCiAgICAgICAgfSwKICAgICAgICB0YWJsZURhdGE6IFtdLAogICAgICAgIHRhYmxlSGVhZGVyOiBbCiAgICAgICAgICB7IGxhYmVsOiAi57yW5Y+3IiwgcHJvcDogImJtIiwgbWluV2lkdGg6ICI4MCIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLnirbmgIEiLCBwcm9wOiAic3RhdHVzQ24iLCBtaW5XaWR0aDogIjgwIiB9LAogICAgICAgICAgeyBsYWJlbDogIuWIhuWFrOWPuCIsIHByb3A6ICJmZ3NtYyIsIG1pbldpZHRoOiAiMTAwIiB9LAogICAgICAgICAgeyBsYWJlbDogIuWPmOeUteermeWQjeensCIsIHByb3A6ICJiZHptY3MiLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLmk43kvZzlvIDlp4vml7bpl7QiLCBwcm9wOiAia3NzaiIsIG1pbldpZHRoOiAiMTEwIiB9LAogICAgICAgICAgeyBsYWJlbDogIuaTjeS9nOe7k+adn+aXtumXtCIsIHByb3A6ICJqc3NqIiwgbWluV2lkdGg6ICIxNDAiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5pON5L2c5Lu75YqhIiwgcHJvcDogImN6cnciLCBtaW5XaWR0aDogIjE2MCIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLmk43kvZzkuroiLCBwcm9wOiAiY3pyIiwgbWluV2lkdGg6ICI2MCIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLnm5HmiqTkuroiLCBwcm9wOiAiamhybWMiLCBtaW5XaWR0aDogIjYwIiB9LAogICAgICAgICAgeyBsYWJlbDogIuS4i+S7pOS6uiIsIHByb3A6ICJ4bHJtYyIsIG1pbldpZHRoOiAiNjAiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5a6h56Wo5Lq6IiwgcHJvcDogImJ6c3BybWMiLCBtaW5XaWR0aDogIjYwIiB9CiAgICAgICAgXSwKICAgICAgICBvcHRpb246IHsgY2hlY2tCb3g6IHRydWUsIHNlcmlhbE51bWJlcjogdHJ1ZSB9CiAgICAgIH0sCiAgICAgIHBhcmFtczogewogICAgICAgIC8v5Y+Y55S1CiAgICAgICAgbHg6IDIsCiAgICAgICAgLy/nlKjmnaXljLrliIbljoblj7LnpajlupPvvIwxLeW3suWKnue7k++8jDIt5pyq5Yqe57uTCiAgICAgICAgc2ZiajogMgogICAgICB9LAogICAgICB4bHJMaXN0OiBbXSwKICAgICAgc3ByTGlzdDogW10KICAgIH07CiAgfSwKICBhc3luYyBtb3VudGVkKCkgewogICAgdGhpcy5nZXRGZ3NPcHRpb25zKCk7CiAgICAvL+iOt+WPluWPmOeUteermeS4i+aLieahhuaVsOaNrgogICAgdGhpcy5nZXRCZHpTZWxlY3RMaXN0KCk7CiAgICAvL+iOt+WPlnRva2VuCiAgICB0aGlzLmhlYWRlci50b2tlbiA9IGdldFRva2VuKCk7CiAgICB0aGlzLnhsckxpc3QgPSBhd2FpdCB0aGlzLmdldEdyb3VwVXNlcnMoNjEsICIiKTsKICAgIHRoaXMuc3ByTGlzdCA9IGF3YWl0IHRoaXMuZ2V0R3JvdXBVc2VycygxMywgIiIpOwoKICAgIGF3YWl0IHRoaXMuZ2V0RGF0YSh0aGlzLiRyb3V0ZS5xdWVyeSk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvL+S9nOW6n+elqAogICAgbnVsbGlmeUd6cChvYmpJZCkgewogICAgICB0aGlzLiRjb25maXJtKCLnpajkvZzlup/lkI7lj6rog73mn6XnnIvvvIzkuI3og73ov5vooYzku7vkvZXmk43kvZzvvIznoa7orqTkvZzlup/lkJc/IiwgIuaPkOekuiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pCiAgICAgICAgLnRoZW4oYXN5bmMgKCkgPT4gewogICAgICAgICAgbGV0IHsgY29kZSB9ID0gYXdhaXQgdXBkYXRlQnlJZCh7IHN0YXR1czogNywgb2JqSWQ6IG9iaklkIH0pOwogICAgICAgICAgaWYgKGNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKnyEhIik7CiAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgICAgLmNhdGNoKCgpID0+IHt9KTsKICAgIH0sCiAgICBleHBvcnRFeGNlbCgpIHsKICAgICAgbGV0IGV4Y2VsRGF0YSA9IHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdC5tYXAoaXRlbSA9PiAoeyAi5pON5L2c6aG555uuIjogaXRlbS5jenJ3fSkpOwogICAgICBleHBvcnRUb0V4Y2VsKGV4Y2VsRGF0YSwgIuaTjeS9nOmhueebri54bHN4Iik7CiAgICB9LAogICAgaW1wb3J0RXhjZWwoZmlsZSwgZmlsZUxpc3QpIHsKICAgICAgbGV0IGZpbGVOYW1lID0gZmlsZS5uYW1lCiAgICAgIGlmICghZmlsZU5hbWUuaW5jbHVkZXMoIuaTjeS9nOmhueebriIpKSB7CiAgICAgICAgdGhpcy5tc2dFcnJvcigi5paH5Lu25pyJ6K+v77yM6K+35qOA5p+lIikKICAgICAgICB0aGlzLiRyZWZzLnVwbG9hZC5jbGVhckZpbGVzKCkKICAgICAgICByZXR1cm4KICAgICAgfQogICAgICBpbXBvcnRGcm9tRXhjZWwoZmlsZSkKICAgICAgICAudGhlbihkYXRhID0+IHsKICAgICAgICAgIHRoaXMuaWRzID0gdGhpcy5wcm9wVGFibGVEYXRhLmNvbEZpcnN0Lm1hcChpdGVtID0+IGl0ZW0ub2JqSWQpCiAgICAgICAgICB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QgPSBkYXRhLm1hcChpdGVtID0+ICh7eGg6IGl0ZW0uX19yb3dOdW1fXyAsIGN6cnc6IGl0ZW1bIuaTjeS9nOmhueebriJdfSkpOwogICAgICAgIH0pCiAgICAgICAgLmNhdGNoKGVycm9yID0+IHsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIuWvvOWFpeWksei0pSIsIGVycm9yKTsKICAgICAgICB9KTsKICAgICAgdGhpcy4kcmVmcy51cGxvYWQuY2xlYXJGaWxlcygpCiAgICB9LAogICAgLyoqCiAgICAgKiDojrflj5bliIblhazlj7jkuIvmi4nmlbDmja4KICAgICAqLwogICAgZ2V0RmdzT3B0aW9ucygpIHsKICAgICAgZ2V0RmdzT3B0aW9ucyh7fSkudGhlbihyZXMgPT4gewogICAgICAgIHJlcy5kYXRhLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICBpdGVtLnZhbHVlID0gaXRlbS52YWx1ZS50b1N0cmluZygpOwogICAgICAgIH0pOwogICAgICAgIHRoaXMub3JnYW5pemF0aW9uU2VsZWN0ZWRMaXN0ID0gcmVzLmRhdGE7CiAgICAgICAgdGhpcy5maWx0ZXJJbmZvLmZpZWxkTGlzdC5tYXAoaXRlbSA9PiB7CiAgICAgICAgICBpZiAoaXRlbS52YWx1ZSA9PSAiZmdzIikgewogICAgICAgICAgICByZXR1cm4gKGl0ZW0ub3B0aW9ucyA9IHRoaXMub3JnYW5pemF0aW9uU2VsZWN0ZWRMaXN0KTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgZmlsdGVyUmVzZXQodmFsKSB7CiAgICAgICh0aGlzLnBhcmFtcyA9IHsKICAgICAgICAvL+WPmOeUtQogICAgICAgIGx4OiAyLAogICAgICAgIC8v55So5p2l5Yy65YiG5Y6G5Y+y56Wo5bqT77yMMS3lt7Llip7nu5PvvIwyLeacquWKnue7kwogICAgICAgIHNmYmo6IDIKICAgICAgfSksCiAgICAgICAgdGhpcy5maWx0ZXJJbmZvLmZpZWxkTGlzdC5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgaWYgKGl0ZW0udHlwZSA9PT0gImNoZWNrYm94IikgewogICAgICAgICAgICBpdGVtLmNoZWNrYm94VmFsdWUgPSBbXTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgIH0sCiAgICBnZXRTaG93KCkgewogICAgICB0aGlzLmJ1dHRvbk5hbWVTaG93ID0gZmFsc2U7CiAgICAgIHN3aXRjaCAodGhpcy5mb3JtLnN0YXR1cykgewogICAgICAgIGNhc2UgIjAiOgogICAgICAgICAgdGhpcy5idXR0b25OYW1lID0gIuS4iiDmiqUiOwogICAgICAgICAgaWYgKHRoaXMuY3VycmVudFVzZXIgPT09IHRoaXMuZm9ybS5jcmVhdGVCeSkgewogICAgICAgICAgICB0aGlzLmJ1dHRvbk5hbWVTaG93ID0gdHJ1ZTsKICAgICAgICAgIH0KICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgIjEiOgogICAgICAgICAgdGhpcy5idXR0b25OYW1lID0gIuaPkCDkuqQiOwogICAgICAgICAgaWYgKHRoaXMuZm9ybS5ienNwciA9PT0gdGhpcy5jdXJyZW50VXNlcikgewogICAgICAgICAgICB0aGlzLmJ1dHRvbk5hbWVTaG93ID0gdHJ1ZTsKICAgICAgICAgIH0KICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgIjIiOgogICAgICAgICAgdGhpcy5idXR0b25OYW1lID0gIuaPkCDkuqQiOwogICAgICAgICAgaWYgKHRoaXMuZm9ybS5mZ3NzcHIgPT09IHRoaXMuY3VycmVudFVzZXIpIHsKICAgICAgICAgICAgdGhpcy5idXR0b25OYW1lU2hvdyA9IHRydWU7CiAgICAgICAgICB9CiAgICAgICAgICBicmVhazsKICAgICAgICBjYXNlICIzIjoKICAgICAgICAgIHRoaXMuYnV0dG9uTmFtZSA9ICLlip4g57uTIjsKICAgICAgICAgIGlmICh0aGlzLmZvcm0uYmpyID09PSB0aGlzLmN1cnJlbnRVc2VyKSB7CiAgICAgICAgICAgIHRoaXMuYnV0dG9uTmFtZVNob3cgPSB0cnVlOwogICAgICAgICAgICB0aGlzLmlzRGlzYWJsZWRCaiA9IGZhbHNlOwogICAgICAgICAgfQogICAgICAgICAgYnJlYWs7CiAgICAgIH0KICAgIH0sCgogICAgZ2V0R3JvdXBVc2VycyhwZXJzb25Hcm91cElkLCBkZXB0SWQpIHsKICAgICAgcmV0dXJuIGdldFVzZXJzKHsKICAgICAgICBwZXJzb25Hcm91cElkOiBwZXJzb25Hcm91cElkLAogICAgICAgIGRlcHRJZDogZGVwdElkLAogICAgICAgIGRlcHROYW1lOiAiIgogICAgICB9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgcmV0dXJuIHJlcy5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICAvL+mihOiniOaWh+S7tgogICAgYXN5bmMgcHJldmlld0ZpbGUocm93KSB7CiAgICAgIHRyeSB7CiAgICAgICAgbGV0IGV4cG9ydERhdGEgPSB7IC4uLnJvdyB9OwogICAgICAgIGF3YWl0IHByZXZpZXdGaWxlKGV4cG9ydERhdGEsICJiZHpkemN6cCIpOwogICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6aKE6KeI5aSx6LSl77yBIik7CiAgICAgIH0KICAgIH0sCiAgICAvL+a4heepugogICAgY2hhbmdlKGUpIHsKICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKTsKICAgIH0sCiAgICAvL+WFs+mXreW8ueeqlwogICAgY2xvc2VBY3Rpdml0aSgpIHsKICAgICAgaWYgKHRoaXMuJHJlZnMuYWN0aXZpdGkuJHJlZnMuZm9ybSkgewogICAgICAgIHRoaXMuJHJlZnMuYWN0aXZpdGkuJHJlZnMuZm9ybS5yZXNldEZpZWxkcygpOwogICAgICB9CiAgICAgIHRoaXMuaXNTaG93ID0gZmFsc2U7CiAgICB9LAogICAgLy/lhbPpl63mtYHnqIvmn6XnnIvpobXpnaIKICAgIGNvbHNlVGltZUxpbmUoKSB7CiAgICAgIHRoaXMudGltZUxpbmVTaG93ID0gZmFsc2U7CiAgICB9LAogICAgYXN5bmMgc2hvd1RpbWVMaW5lKHJvdykgewogICAgICB0aGlzLnByb2Nlc3NEYXRhLmJ1c2luZXNzS2V5ID0gcm93Lm9iaklkOwogICAgICBsZXQgeyBjb2RlLCBkYXRhIH0gPSBhd2FpdCBIaXN0b3J5TGlzdCh0aGlzLnByb2Nlc3NEYXRhKTsKICAgICAgdGhpcy50aW1lRGF0YSA9IGRhdGE7CiAgICAgIHRoaXMudGltZUxpbmVTaG93ID0gdHJ1ZTsKICAgIH0sCiAgICAvL+a1geeoi+WbvueJh+afpeeciwogICAgc2hvd1Byb2Nlc3NJbWcocm93KSB7CiAgICAgIHRoaXMub3BlbkxvYWRpbmdJbWcgPSB0cnVlOwogICAgICB0aGlzLmltZ1NyYyA9CiAgICAgICAgIi9hY3Rpdml0aS1hcGkvcHJvY2Vzcy9yZWFkLXJlc291cmNlP3Byb2Nlc3NEZWZpbml0aW9uS2V5PWN6cHNoJmJ1c2luZXNzS2V5PSIgKwogICAgICAgIHJvdy5vYmpJZCArCiAgICAgICAgIiZ0PSIgKwogICAgICAgIG5ldyBEYXRlKCkuZ2V0VGltZSgpOwogICAgfSwKICAgIC8v5bel5L2c5rWB5Zue5Lyg5pWw5o2uCiAgICBhc3luYyB0b2RvUmVzdWx0KGRhdGEpIHsKICAgICAgbGV0IHJvdyA9IHsKICAgICAgICBvYmpJZDogZGF0YS5idXNpbmVzc0tleSwKICAgICAgICBpc1N0YXJ0OiAxLAogICAgICAgIGlzQmFjazogZGF0YS5wcm9jZXNzVHlwZSA9PT0gInJvbGxiYWNrIiA/IDEgOiAwCiAgICAgIH07CiAgICAgIGlmIChkYXRhLnByb2Nlc3NUeXBlID09PSAicm9sbGJhY2siKSB7CiAgICAgICAgc3dpdGNoIChkYXRhLmFjdGl2ZVRhc2tOYW1lKSB7CiAgICAgICAgICBjYXNlICLmk43kvZznpajloavmiqUiOgogICAgICAgICAgICByb3cuc3RhdHVzID0gIjAiOwogICAgICAgICAgICBicmVhazsKICAgICAgICAgIGNhc2UgIuePree7hOWuoeaguCI6CiAgICAgICAgICAgIHJvdy5zdGF0dXMgPSAiMSI7CiAgICAgICAgICAgIGJyZWFrOwogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICBzd2l0Y2ggKGRhdGEuYWN0aXZlVGFza05hbWUpIHsKICAgICAgICAgIGNhc2UgIuePree7hOWuoeaguCI6CiAgICAgICAgICAgIHJvdy5zdGF0dXMgPSAiMSI7CiAgICAgICAgICAgIHJvdy5ienNwciA9IGRhdGEubmV4dFVzZXI7CiAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgY2FzZSAi5YiG5YWs5Y+45a6h5qC4IjoKICAgICAgICAgICAgcm93LnN0YXR1cyA9ICIyIjsKICAgICAgICAgICAgcm93LmZnc3NwciA9IGRhdGEubmV4dFVzZXI7CiAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgY2FzZSAi5pON5L2c56Wo5Yqe57uTIjoKICAgICAgICAgICAgcm93LnN0YXR1cyA9ICIzIjsKICAgICAgICAgICAgcm93LmJqciA9IGRhdGEubmV4dFVzZXI7CiAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgY2FzZSAi57uT5p2fIjoKICAgICAgICAgICAgcm93LnN0YXR1cyA9ICI0IjsKICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgfQogICAgICB9CiAgICAgIGxldCB7IGNvZGUgfSA9IGF3YWl0IHVwZGF0ZUJ5SWQocm93KTsKICAgICAgaWYgKGNvZGUgPT09ICIwMDAwIikgewogICAgICAgIGF3YWl0IHRoaXMuZ2V0RGF0YSgpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi5pON5L2c5aSx6LSlIik7CiAgICAgIH0KICAgIH0sCiAgICAvL+S4iuaKpeWPkemAgeWKnue7kwogICAgYXN5bmMgZ2V0U2JGc0JqKHR5cGUpIHsKICAgICAgbGV0IHJvdyA9IHsgLi4udGhpcy5mb3JtIH07CiAgICAgIGlmICh0eXBlID09PSAiY29tcGxldGUiKSB7CiAgICAgICAgc3dpdGNoIChyb3cuc3RhdHVzKSB7CiAgICAgICAgICBjYXNlICIwIjoKICAgICAgICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gZmFsc2U7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnBhc3MgPSB0cnVlOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmJ1c2luZXNzS2V5ID0gcm93Lm9iaklkOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnByb2Nlc3NUeXBlID0gdHlwZTsKICAgICAgICAgICAgdGhpcy5hY3Rpdml0aU9wdGlvbi50aXRsZSA9ICLmj5DkuqQiOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmRlZmF1bHRGcm9tID0gdHJ1ZTsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5yeWx4ID0gIuePree7hOWuoeaguOS6uiI7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuZHcgPSByb3cuZmdzOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnBlcnNvbkdyb3VwSWQgPSAxMzsKICAgICAgICAgICAgdGhpcy5pc1Nob3cgPSB0cnVlOwogICAgICAgICAgICBicmVhazsKICAgICAgICAgIGNhc2UgIjEiOgogICAgICAgICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSBmYWxzZTsKICAgICAgICAgICAgdGhpcy4kY29uZmlybSgi5piv5ZCm6ZyA6KaB5o+Q5Lqk5YiG5YWs5Y+45a6h5qC4PyIsICLpgInmi6kiLCB7CiAgICAgICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLmmK8iLAogICAgICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlkKYiLAogICAgICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICAgICAgICB9KQogICAgICAgICAgICAgIC50aGVuKGFzeW5jICgpID0+IHsKICAgICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnBhc3MgPSB0cnVlOwogICAgICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5idXNpbmVzc0tleSA9IHJvdy5vYmpJZDsKICAgICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEucHJvY2Vzc1R5cGUgPSB0eXBlOwogICAgICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5kdyA9IHJvdy5mZ3M7CiAgICAgICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnJ5bHggPSAi5YiG5YWs5Y+45a6h5qC45Lq6IjsKICAgICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLmlzRmdzID0gdHJ1ZTsKICAgICAgICAgICAgICAgIHRoaXMuYWN0aXZpdGlPcHRpb24udGl0bGUgPSAi5o+Q5LqkIjsKICAgICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuZGVmYXVsdEZyb20gPSB0cnVlOwogICAgICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5wZXJzb25Hcm91cElkID0gMTQ7CiAgICAgICAgICAgICAgICB0aGlzLmlzU2hvdyA9IHRydWU7CiAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICAuY2F0Y2goKCkgPT4gewogICAgICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS52YXJpYWJsZXMucGFzcyA9IHRydWU7CiAgICAgICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmJ1c2luZXNzS2V5ID0gcm93Lm9iaklkOwogICAgICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5wcm9jZXNzVHlwZSA9IHR5cGU7CiAgICAgICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmR3ID0gcm93LmZnczsKICAgICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEucnlseCA9ICLlip7nu5PkuroiOwogICAgICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS52YXJpYWJsZXMuaXNGZ3MgPSBmYWxzZTsKICAgICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuZGVmYXVsdEZyb20gPSB0cnVlOwogICAgICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5wZXJzb25Hcm91cElkID0gMTU7CiAgICAgICAgICAgICAgICB0aGlzLmlzU2hvdyA9IHRydWU7CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgY2FzZSAiMiI6CiAgICAgICAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IGZhbHNlOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnZhcmlhYmxlcy5wYXNzID0gdHJ1ZTsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5idXNpbmVzc0tleSA9IHJvdy5vYmpJZDsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5wcm9jZXNzVHlwZSA9IHR5cGU7CiAgICAgICAgICAgIHRoaXMuYWN0aXZpdGlPcHRpb24udGl0bGUgPSAi5o+Q5LqkIjsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5kZWZhdWx0RnJvbSA9IHRydWU7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEucGVyc29uR3JvdXBJZCA9IDE1OwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmR3ID0gcm93LmZnczsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5yeWx4ID0gIuWKnue7k+S6uiI7CiAgICAgICAgICAgIHRoaXMuaXNTaG93ID0gdHJ1ZTsKICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICBjYXNlICIzIjoKICAgICAgICAgICAgYXdhaXQgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKGFzeW5jIHZhbGlkID0+IHsKICAgICAgICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgICAgICAgIHRoaXMuZm9ybS5jb2xGaXJzdCA9IHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdDsKICAgICAgICAgICAgICAgICAgdGhpcy5mb3JtLm9iaklkTGlzdCA9IHRoaXMuaWRzOwogICAgICAgICAgICAgICAgICB0aGlzLnVwbG9hZEltZ0RhdGEuYnVzaW5lc3NJZCA9IHRoaXMuZm9ybS5vYmpJZDsKICAgICAgICAgICAgICAgICAgdGhpcy51cGxvYWRGb3JtKCk7CiAgICAgICAgICAgICAgICAgIGF3YWl0IHNhdmVPclVwZGF0ZSh0aGlzLmZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgICAgICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICAgICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnBhc3MgPSB0cnVlOwogICAgICAgICAgICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5idXNpbmVzc0tleSA9IHJvdy5vYmpJZDsKICAgICAgICAgICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEucHJvY2Vzc1R5cGUgPSB0eXBlOwogICAgICAgICAgICAgICAgICAgICAgdGhpcy5hY3Rpdml0aU9wdGlvbi50aXRsZSA9ICLlip7nu5MiOwogICAgICAgICAgICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5kZWZhdWx0RnJvbSA9IGZhbHNlOwogICAgICAgICAgICAgICAgICAgICAgdGhpcy5pc1Nob3cgPSB0cnVlOwogICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLlpLHotKUiKTsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgfSBjYXRjaCAoZSkge30KICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSBmYWxzZTsKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmJ1c2luZXNzS2V5ID0gcm93Lm9iaklkOwogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEucHJvY2Vzc1R5cGUgPSB0eXBlOwogICAgICAgIHRoaXMuYWN0aXZpdGlPcHRpb24udGl0bGUgPSAi5Zue6YCA5Y6f5Zug5aGr5YaZIjsKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmRlZmF1bHRGcm9tID0gdHJ1ZTsKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnZhcmlhYmxlcy5wYXNzID0gZmFsc2U7CiAgICAgICAgdGhpcy5pc1Nob3cgPSB0cnVlOwogICAgICB9CiAgICB9LAoKICAgIC8v5pS56YCg5ZCO55qE5LiK5Lyg5aSa5Liq5Zu+54mH5paH5Lu2CiAgICB1cGxvYWRGb3JtKCkgewogICAgICB2YXIgbmV3VXJsID0gW107IC8v55So5p2l5a2Y5pS+5b2T5YmN5pyq5pS55Yqo6L+H55qE5Zu+54mHdXJsLOatpOWbvueJh+S4jei/m+ihjOWIoOmZpOWkhOeQhu+8jOWFtuS9meW9k+WJjeS4muWKoWlk5LiL6Z2i55qE5Zu+54mH5pWw5o2u5bCG6L+b6KGM5Yig6ZmkCiAgICAgIHZhciBpbWFnZVR5cGUgPSBbInBuZyIsICJqcGciXTsKICAgICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKTsKICAgICAgLy8g5Zug5Li66KaB5Lyg5LiA5Liq5paH5Lu25pWw57uE6L+H5Y6777yM5omA5Lul6KaB5b6q546vYXBwZW5kCiAgICAgIHRoaXMuaW1nTGlzdC5mb3JFYWNoKGZpbGUgPT4gewogICAgICAgIGlmIChmaWxlLnJhdyA9PSB1bmRlZmluZWQpIHsKICAgICAgICAgIG5ld1VybC5wdXNoKGZpbGUudXJsKTsKICAgICAgICB9CiAgICAgICAgZm9ybURhdGEuYXBwZW5kKCJmaWxlcyIsIGZpbGUucmF3KTsKICAgICAgfSk7CiAgICAgIGZvcm1EYXRhLmFwcGVuZCgiYnVzaW5lc3NJZCIsIHRoaXMudXBsb2FkSW1nRGF0YS5idXNpbmVzc0lkKTsgLy8g6Ieq5a6a5LmJ5Y+C5pWwCiAgICAgIGZvcm1EYXRhLmFwcGVuZCgibmV3VXJsIiwgbmV3VXJsKTsgLy8g5pyq5pS55Yqo6L+H55qE5Zu+54mHdXJsCiAgICAgIGZvcm1EYXRhLmFwcGVuZCgidHlwZSIsIGltYWdlVHlwZSk7IC8vIOacquaUueWKqOi/h+eahOWbvueJh3VybAogICAgICBhcGkKICAgICAgICAucmVxdWVzdFBvc3QoIi9pc2MtYXBpL2ZpbGUvdXBsb2FkRmlsZXMiLCBmb3JtRGF0YSwgMSkKICAgICAgICAudGhlbihyZXMgPT4gewogICAgICAgICAgLy8g5riF56m65Zu+54mH5YiX6KGo77yI5LiA5a6a6KaB5riF56m677yM5ZCm5YiZ5LiK5Lyg5oiQ5Yqf5ZCO6L+Y5piv5Lya6LCD55SoaGFuZGxlQ2hhbmdl77yI77yJ5Ye95pWw77yM5LiK5Lyg5oiQ5Yqf5ZCO5YiX6KGo5Lit6L+Y5a2Y5Zyo5Zu+54mH77yJCiAgICAgICAgICB0aGlzLmltZ0xpc3QgPSBbXTsKICAgICAgICAgIC8v5p+l6K+i5o6l5Y+j5pWw5o2uCiAgICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICB9KQogICAgICAgIC5jYXRjaChyZXMgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLlm77niYfkuIrkvKDlpLHotKXvvIEiKTsKICAgICAgICB9KTsKICAgIH0sCiAgICAvLyDpgInmi6nmlofku7bml7bvvIzlvoBmaWxlTGlzdOmHjOa3u+WKoAogICAgaGFuZGxlQ2hhbmdlKGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgIHRoaXMuaW1nTGlzdCA9IGZpbGVMaXN0OwogICAgfSwKICAgIGhhbmRsZVByb2dyZXNzKGV2ZW50LCBmaWxlLCBmaWxlTGlzdCkge30sCiAgICAvL+WbvueJh+enu+mZpAogICAgaGFuZGxlUmVtb3ZlKGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgIHRoaXMuaW1nTGlzdCA9IGZpbGVMaXN0OwogICAgfSwKICAgIC8v5Zu+54mH5pS+5aSnCiAgICBoYW5kbGVQaWN0dXJlQ2FyZFByZXZpZXcoZmlsZSkgewogICAgICB0aGlzLmRpYWxvZ0ltYWdlVXJsID0gW2ZpbGUudXJsXTsKICAgICAgdGhpcy5pbWdEaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICBvbkNsb3NlKCkgewogICAgICB0aGlzLmltZ0RpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgIH0sCiAgICAvLyDlhajpgInmoYYKICAgIGhhbmRsZUNoZWNrQWxsQ2hhbmdlKHZhbCkgewogICAgICBpZiAodmFsKSB7CiAgICAgICAgdGhpcy5mb3JtLnl6eGN6eHMgPSB0aGlzLmZvcm0uY3p4czsKICAgICAgICB0aGlzLmZvcm0ud3p4Y3p4cyA9IDA7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5mb3JtLnd6eGN6eHMgPSB0aGlzLmZvcm0uY3p4czsKICAgICAgICB0aGlzLmZvcm0ueXp4Y3p4cyA9IDA7CiAgICAgIH0KICAgICAgdGhpcy5wcm9wVGFibGVEYXRhLmNvbEZpcnN0LmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgaXRlbS5zZndjID0gdmFsOwogICAgICB9KTsKICAgIH0sCiAgICAvL+mAieaLqeW3suaJp+ihjOaXtu+8jOaTjeS9nOmhueebrum7mOiupOm7mOiupOWFqOmAiQogICAgaGFuZGxlQ2hhbmdlT2ZTZnp4KHZhbCkgewogICAgICBpZiAodmFsID09PSAi5bey5omn6KGMIikgewogICAgICAgIHRoaXMuZm9ybS55enhjenhzID0gdGhpcy5mb3JtLmN6eHM7CiAgICAgICAgdGhpcy5mb3JtLnd6eGN6eHMgPSAwOwogICAgICAgIHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdC5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgaXRlbS5zZndjID0gdHJ1ZTsKICAgICAgICB9KTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmZvcm0ud3p4Y3p4cyA9IHRoaXMuZm9ybS5jenhzOwogICAgICAgIHRoaXMuZm9ybS55enhjenhzID0gMDsKICAgICAgICB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIGl0ZW0uc2Z3YyA9IGZhbHNlOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgaGFuZGxlQ2hlY2tDaGFuZ2UodmFsKSB7CiAgICAgIGlmICh2YWwpIHsKICAgICAgICArK3RoaXMuZm9ybS55enhjenhzOwogICAgICB9IGVsc2UgewogICAgICAgIC0tdGhpcy5mb3JtLnl6eGN6eHM7CiAgICAgIH0KICAgICAgdGhpcy5mb3JtLnd6eGN6eHMgPSB0aGlzLmZvcm0uY3p4cyAtIHRoaXMuZm9ybS55enhjenhzOwogICAgfSwKICAgIC8vIOmihOiniOaMiemSrgogICAgaGFuZGxlWWxDaGFuZ2UoKSB7CiAgICAgIHRoaXMudGl0bGV5bCA9ICLmn6XnnIvmk43kvZzpobnnm64iOwogICAgICB0aGlzLnlsID0gdHJ1ZTsKICAgIH0sCiAgICAvL+WIl+ihqOafpeivogogICAgYXN5bmMgZ2V0RGF0YShwYXJhbXMpIHsKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCBwYXJhbSA9IHsgLi4udGhpcy5wYXJhbXMsIC4uLnBhcmFtcyB9OwogICAgICAgIHRoaXMucGFyYW1zID0gcGFyYW07CiAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgICBpZiAoIXBhcmFtLnN0YXR1cykgewogICAgICAgICAgcGFyYW0uc3RhdHVzID0gIjAsMSwyLDMsNCI7CiAgICAgICAgfQogICAgICAgIHBhcmFtLm15U29ydHMgPSBbeyBwcm9wOiAidXBkYXRlVGltZSIsIGFzYzogZmFsc2UgfV07CiAgICAgICAgY29uc3QgeyBkYXRhLCBjb2RlIH0gPSBhd2FpdCBnZXRMaXN0THNwKHBhcmFtKTsKICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICBmb3IgKGxldCBpIG9mIGRhdGEucmVjb3JkcykgewogICAgICAgICAgICBpLmZnc21jID0gdGhpcy5mb3JtYXRTc2dzKGkuZmdzKTsKICAgICAgICAgICAgdGhpcy5zdGF0dXNPcHRpb25zLmZvckVhY2goZWxlbWVudCA9PiB7CiAgICAgICAgICAgICAgaWYgKGkuc3RhdHVzID09PSBlbGVtZW50LnZhbHVlKSB7CiAgICAgICAgICAgICAgICBpLnN0YXR1c0NuID0gZWxlbWVudC5sYWJlbDsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnRhYmxlRGF0YSA9IGRhdGEucmVjb3JkczsKICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci50b3RhbCA9IGRhdGEudG90YWw7CiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICBjb25zb2xlLmxvZyhlKTsKICAgICAgfQogICAgfSwKICAgIC8qKgogICAgICrojrflj5bmk43kvZznpajmmI7nu4YKICAgICAqLwogICAgYXN5bmMgZ2V0Q3pwbXgocm93KSB7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgeyBkYXRhLCBjb2RlIH0gPSBhd2FpdCBnZXRDenBteExpc3QoeyBvYmpJZDogcm93Lm9iaklkIH0pOwogICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdCA9IGRhdGE7CiAgICAgICAgICB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QuZm9yRWFjaChlID0+IHsKICAgICAgICAgICAgZS51dWlkID0gZ2V0VVVJRCgpOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgY29uc29sZS5sb2coZSk7CiAgICAgIH0KICAgIH0sCgogICAgLy/kv67mlLnmjInpkq4KICAgIGFzeW5jIGdldFVwZGF0ZShyb3cpIHsKICAgICAgYXdhaXQgdGhpcy5nZXRDenBteChyb3cpOwogICAgICB0aGlzLnRpdGxlID0gIuWPmOeUteWAkumXuOaTjeS9nOelqOS/ruaUuSI7CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICB0aGlzLmlzRGlzYWJsZWRCaiA9IGZhbHNlOwogICAgICB0aGlzLmZvcm0gPSB7IC4uLnJvdyB9OwogICAgICAvL+WbvueJh+e7k+WQiAogICAgICB0aGlzLmltZ0xpc3QgPSB0aGlzLmZvcm0uaW1nTGlzdDsKICAgICAgdGhpcy5qbHJMaXN0ID0gYXdhaXQgdGhpcy5nZXRHcm91cFVzZXJzKDYyLCAiIik7CiAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IHRydWU7CiAgICAgIHRoaXMuZ2V0U2hvdygpOwogICAgfSwKICAgIC8v6K+m5oOF5oyJ6ZKuCiAgICBhc3luYyBnZXREZXRhaWxzKHJvdykgewogICAgICBhd2FpdCB0aGlzLmdldEN6cG14KHJvdyk7CiAgICAgIHRoaXMudGl0bGUgPSAi5Y+Y55S15pON5L2c56Wo6K+m5oOF5p+l55yLIjsKICAgICAgdGhpcy5mb3JtID0geyAuLi5yb3cgfTsKICAgICAgdGhpcy5qbHJMaXN0ID0gYXdhaXQgdGhpcy5nZXRHcm91cFVzZXJzKDYyLCAiIik7CiAgICAgIC8v5Zu+54mH57uT5ZCICiAgICAgIHRoaXMuaW1nTGlzdCA9IHRoaXMuZm9ybS5pbWdMaXN0OwogICAgICB0aGlzLmlzRGlzYWJsZWQgPSB0cnVlOwogICAgICB0aGlzLmlzRGlzYWJsZWRCaiA9IHRydWU7CiAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IHRydWU7CiAgICAgIHRoaXMuZ2V0U2hvdygpOwogICAgfSwKICAgIC8vIOS/neWtmOaMiemSrgogICAgYXN5bmMgc2F2ZVJvdygpIHsKICAgICAgYXdhaXQgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgIHRoaXMuZm9ybS5jb2xGaXJzdCA9IHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdDsKICAgICAgICAgICAgdGhpcy5mb3JtLm9iaklkTGlzdCA9IHRoaXMuaWRzOwogICAgICAgICAgICBzYXZlT3JVcGRhdGUodGhpcy5mb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5pON5L2c5oiQ5YqfIik7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIC8v6YeN572ucGFnZemhteS7jjHlvIDlp4sKICAgICAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIucGFnZVJlc2l6ZSA9ICJZIjsKICAgICAgICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICAgICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSBmYWxzZTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGNhdGNoIChlKSB7fQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLmoKHpqozmnKrpgJrov4fvvIEiKTsKICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKCiAgICAvL+WIoOmZpOaMiemSrgogICAgYXN5bmMgZGVsZXRlUm93KHJvdykgewogICAgICBsZXQgb2JqSWQgPSAiIjsKICAgICAgaWYgKHJvdy5vYmpJZCkgewogICAgICAgIG9iaklkID0gcm93Lm9iaklkOwogICAgICB9IGVsc2UgewogICAgICAgIG9iaklkID0gdGhpcy5pZHNbMF07CiAgICAgIH0KICAgICAgdGhpcy4kY29uZmlybSgi5q2k5pON5L2c5bCG5rC45LmF5Yig6Zmk6K+l5pWw5o2uLCDmmK/lkKbnu6fnu60/IiwgIuaPkOekuiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pCiAgICAgICAgLnRoZW4oKCkgPT4gewogICAgICAgICAgcmVtb3ZlKG9iaklkKS50aGVuKCh7IGNvZGUgfSkgPT4gewogICAgICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5Yig6Zmk5oiQ5YqfISIKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICJlcnJvciIsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5Yig6Zmk5aSx6LSlISIKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfSkKICAgICAgICAuY2F0Y2goKCkgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICJpbmZvIiwKICAgICAgICAgICAgbWVzc2FnZTogIuW3suWPlua2iOWIoOmZpCIKICAgICAgICAgIH0pOwogICAgICAgIH0pOwogICAgfSwKICAgIC8v6KGo5qC85paw5aKeCiAgICBsaXN0Rmlyc3RBZGQoKSB7CiAgICAgIGxldCByb3cgPSB7CiAgICAgICAgeGg6ICIiLAogICAgICAgIGN6cnc6ICIiLAogICAgICAgIHNmd2M6ICIiLAogICAgICAgIHV1aWQ6IGdldFVVSUQoKQogICAgICB9OwogICAgICB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QucHVzaChyb3cpOwogICAgICB0aGlzLnByb3BUYWJsZURhdGEuc2VsID0gcm93OwogICAgICB0aGlzLmZvcm0uY3p4cyA9IHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdC5sZW5ndGg7CiAgICAgIHRoaXMuZm9ybS53enhjenhzID0gdGhpcy5wcm9wVGFibGVEYXRhLmNvbEZpcnN0Lmxlbmd0aDsKICAgIH0sCiAgICAvL+ihqOagvOWIoOmZpAogICAgbGlzdEZpcnN0RGVsKHJvdykgewogICAgICBpZiAocm93Lm9iaklkKSB7CiAgICAgICAgdGhpcy5pZHMucHVzaChyb3cub2JqSWQpOwogICAgICB9CiAgICAgIHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdCA9IHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdC5maWx0ZXIoCiAgICAgICAgaXRlbSA9PiBpdGVtLnV1aWQgIT09IHJvdy51dWlkCiAgICAgICk7CiAgICAgIHRoaXMuZm9ybS5jenhzID0gdGhpcy5wcm9wVGFibGVEYXRhLmNvbEZpcnN0Lmxlbmd0aDsKICAgICAgdGhpcy5mb3JtLnd6eGN6eHMgPSB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QubGVuZ3RoOwogICAgfSwKCiAgICAvL+WFs+mXreW8ueeqlwogICAgY2xvc2UoKSB7CiAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IGZhbHNlOwogICAgfSwKICAgIHNlbGVjdENoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5vYmpJZCk7CiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMTsKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOwogICAgICB0aGlzLnNlbGVjdERhdGEgPSBzZWxlY3Rpb247CiAgICB9LAogICAgLyoqCiAgICAgKiDojrflj5blj5jnlLXnq5nkuIvmi4nmoYbmlbDmja4KICAgICAqLwogICAgZ2V0QmR6U2VsZWN0TGlzdCgpIHsKICAgICAgZ2V0QmR6U2VsZWN0TGlzdCh7fSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuYmR6TGlzdCA9IHJlcy5kYXRhOwogICAgICAgIHRoaXMuZmlsdGVySW5mby5maWVsZExpc3QubWFwKGl0ZW0gPT4gewogICAgICAgICAgaWYgKGl0ZW0udmFsdWUgPT0gImJkem1jIikgewogICAgICAgICAgICByZXR1cm4gKGl0ZW0ub3B0aW9ucyA9IHRoaXMuYmR6TGlzdCk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIC8v5a+85Ye6d29yZAogICAgYXN5bmMgZXhwb3J0V29yZChyb3cpIHsKICAgICAgdHJ5IHsKICAgICAgICBsZXQgZXhwb3J0RGF0YSA9IHsgLi4ucm93IH07CiAgICAgICAgYXdhaXQgZXhwb3J0V29yZChleHBvcnREYXRhLCAiYmR6ZHpjenAiLCAi5Y+Y55S156uZ5YCS6Ze45pON5L2c56WoLmRvY3giKTsKICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuWvvOWHuuWksei0pe+8gSIpOwogICAgICB9CiAgICB9LAogICAgLy/lr7zlh7pQZGYKICAgIGFzeW5jIGV4cG9ydFBkZihyb3cpIHsKICAgICAgdHJ5IHsKICAgICAgICBsZXQgZXhwb3J0RGF0YSA9IHsgLi4ucm93IH07CiAgICAgICAgYXdhaXQgZXhwb3J0UGRmKGV4cG9ydERhdGEsICJiZHpkemN6cCIsICLlj5jnlLXnq5nlgJLpl7jmk43kvZznpagucGRmIik7CiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLlr7zlh7rlpLHotKXvvIEiKTsKICAgICAgfQogICAgfSwKICAgIC8v5LiL5ouJ5qGGY2hhbmdl5LqL5Lu2CiAgICBoYW5kbGVFdmVudCh2YWwsIGV2ZW50VmFsdWUpIHsKICAgICAgaWYgKHZhbC5sYWJlbCA9PT0gImZncyIpIHsKICAgICAgICBnZXRCZHpTZWxlY3RMaXN0KHsgc3Nkd2JtOiB2YWwudmFsdWUudG9TdHJpbmcoKSB9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgICB0aGlzLmZpbHRlckluZm8uZmllbGRMaXN0Lm1hcChpdGVtID0+IHsKICAgICAgICAgICAgaWYgKGl0ZW0udmFsdWUgPT09ICJiZHptYyIpIHsKICAgICAgICAgICAgICB0aGlzLiRzZXQoZXZlbnRWYWx1ZSwgImJkem1jIiwgIiIpOwogICAgICAgICAgICAgIHJldHVybiAoaXRlbS5vcHRpb25zID0gcmVzLmRhdGEpOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9KTsKICAgICAgfQogICAgfSwKICAgIGZvcm1hdFNzZ3Moc3NncykgewogICAgICBsZXQgcGFnZU9yZ2FuaXphdGlvblNlbGVjdGVkTGlzdCA9IEpTT04ucGFyc2UoCiAgICAgICAgSlNPTi5zdHJpbmdpZnkodGhpcy5vcmdhbml6YXRpb25TZWxlY3RlZExpc3QpCiAgICAgICk7CiAgICAgIHBhZ2VPcmdhbml6YXRpb25TZWxlY3RlZExpc3QucHVzaCh7CiAgICAgICAgbGFiZWw6ICLmuK/kuJzlj5jnlLXliIblhazlj7giLAogICAgICAgIHZhbHVlOiAiMzAwMiIKICAgICAgfSk7CiAgICAgIHBhZ2VPcmdhbml6YXRpb25TZWxlY3RlZExpc3QucHVzaCh7CiAgICAgICAgbGFiZWw6ICLmuK/kuK3lj5jnlLXliIblhazlj7giLAogICAgICAgIHZhbHVlOiAiMzAwMyIKICAgICAgfSk7CiAgICAgIGlmIChzc2dzKSB7CiAgICAgICAgbGV0IGZpbHRlciA9IHBhZ2VPcmdhbml6YXRpb25TZWxlY3RlZExpc3QuZmlsdGVyKGcgPT4gZy52YWx1ZSA9PT0gc3Nncyk7CiAgICAgICAgaWYgKGZpbHRlci5sZW5ndGggPiAwKSB7CiAgICAgICAgICByZXR1cm4gZmlsdGVyWzBdLmxhYmVsOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICByZXR1cm4gIiI7CiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiAiIjsKICAgICAgfQogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["dzczp.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsp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file": "dzczp.vue", "sourceRoot": "src/views/dagangOilfield/czpgl/bddzcz", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n      @handleEvent=\"handleEvent\"\n      @handleReset=\"filterReset\"\n    />\n\n    <div>\n      <el-white class=\"button-group\">\n        <div class=\"button_btn\">\n          <el-button\n            type=\"danger\"\n            icon=\"el-icon-delete\"\n            v-if=\"hasSuperRole\"\n            @click=\"deleteRow\"\n            :disabled=\"single\"\n            >删除</el-button\n          >\n        </div>\n        <!-- <el-table-column\n          prop=\"statusCn\"\n          slot=\"table_start\"\n          align=\"center\"\n          style=\"display: block\"\n          label=\"流程状态\"\n          min-width=\"120\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-badge\n              v-if=\"scope.row.isBack === 1\"\n              value=\"退回\"\n              class=\"item\"\n              type=\"danger\"\n            >\n            </el-badge>\n            <span>{{ scope.row.statusCn }}</span>\n          </template>\n        </el-table-column> -->\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"selectChange\"\n          height=\"69vh\"\n          v-loading=\"loading\"\n        >\n          <el-table-column\n            slot=\"table_eight\"\n            align=\"center\"\n            fixed=\"right\"\n            style=\"display: block\"\n            label=\"操作\"\n            width=\"160\"\n            :resizable=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                @click=\"getDetails(scope.row)\"\n                type=\"text\"\n                size=\"small\"\n                title=\"详情\"\n                icon=\"el-icon-view\"\n              />\n              <el-button\n                @click=\"getUpdate(scope.row)\"\n                v-if=\"\n                  (scope.row.status === '0' &&\n                    scope.row.createBy === currentUser) ||\n                    hasSuperRole\n                \"\n                type=\"text\"\n                size=\"small\"\n                title=\"编辑\"\n                icon=\"el-icon-edit\"\n              />\n              <el-button\n                @click=\"deleteRow(scope.row)\"\n                v-if=\"\n                  scope.row.status === '0' && scope.row.createBy === currentUser\n                \"\n                type=\"text\"\n                size=\"small\"\n                title=\"删除\"\n                icon=\"el-icon-delete\"\n              />\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                @click=\"nullifyGzp(scope.row.objId)\"\n                v-if=\"scope.row.status < 4 && scope.row.status > 0 &&\n                ( scope.row.createBy === currentUser || (scope.row.bzspr && scope.row.bzspr === currentUser) \n                || (scope.row.fgsspr && scope.row.fgsspr === currentUser) \n                || (scope.row.bjr && scope.row.bjr === currentUser)\n                || hasSuperRole )\n                \"\n                title=\"作废\"\n                class=\"el-icon-close\"\n              >\n              </el-button>\n              <el-button\n                @click=\"showTimeLine(scope.row)\"\n                v-if=\"scope.row.isStart === 1\"\n                type=\"text\"\n                size=\"small\"\n                title=\"流程查看\"\n                icon=\"el-icon-lcck commonIcon\"\n              />\n              <el-button\n                @click=\"showProcessImg(scope.row)\"\n                v-if=\"scope.row.isStart === 1\"\n                type=\"text\"\n                size=\"small\"\n                title=\"流程图\"\n                icon=\"el-icon-lct commonIcon\"\n              />\n              <!--              <el-button @click=\"exportWord(scope.row)\" v-if=\"scope.row.status == '待办结'\" type=\"text\" size=\"small\">\n                              导出Word\n                            </el-button>-->\n              <el-button\n                @click=\"previewFile(scope.row)\"\n                v-if=\"scope.row.status > 2\"\n                type=\"text\"\n                size=\"small\"\n                title=\"预览\"\n                class=\"el-icon-zoom-in\"\n              />\n              <el-button\n                @click=\"exportPdf(scope.row)\"\n                v-if=\"scope.row.status > 2\"\n                type=\"text\"\n                size=\"small\"\n                title=\"导出pdf\"\n                icon=\"el-icon-pdf-export commonIcon\"\n              />\n            </template>\n          </el-table-column>\n        </comp-table>\n      </el-white>\n    </div>\n    <!-- 详情/新增/修改 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      v-if=\"isShowDetails\"\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <div>\n          <el-row>\n            <el-col :span=\"8\">\n              <el-form-item label=\"编号：\" prop=\"bm\">\n                <el-input\n                  v-model=\"form.bm\"\n                  disabled\n                  placeholder=\"确认后编号自动生成\"\n                >\n                </el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"分公司：\" prop=\"fgs\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.fgs\"\n                  disabled\n                  placeholder=\"请选择分公司\"\n                >\n                  <el-option\n                    v-for=\"item in organizationSelectedList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"变电站名称：\" prop=\"bdzmc\">\n                <el-select\n                  style=\"width: 100%\"\n                  filterable\n                  v-model=\"form.bdzmc\"\n                  disabled\n                  placeholder=\"请选择变电站\"\n                >\n                  <el-option\n                    v-for=\"item in bdzList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"下令人：\"\n                prop=\"xlr\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.xlr\"\n                  :disabled=\"isDisabled && isDisabledBj\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in xlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"审票人：\" prop=\"bzspr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.bzspr\"\n                  :disabled=\"isDisabledBj\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in sprList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"操作开始时间：\"\n                prop=\"kssj\"\n                label-width=\"140px\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-date-picker\n                  :disabled=\"isDisabledBj\"\n                  v-model=\"form.kssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  :placeholder=\"isDisabledBj ? '' : '选择日期时间'\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"操作结束时间：\"\n                prop=\"jssj\"\n                label-width=\"140px\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-date-picker\n                  :disabled=\"isDisabledBj\"\n                  v-model=\"form.jssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  :placeholder=\"isDisabledBj ? '' : '选择日期时间'\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"操作人：\"\n                prop=\"czr\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-input\n                  v-model=\"form.czr\"\n                  :disabled=\"isDisabledBj\"\n                  :placeholder=\"isDisabledBj ? '' : '请输入内容'\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"监护人：\"\n                prop=\"jhr\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.jhr\"\n                  :disabled=\"isDisabledBj\"\n                  :placeholder=\"isDisabledBj ? '' : '请选择'\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in jlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作项数：\" prop=\"czxs\">\n                <el-input-number\n                  v-model=\"form.czxs\"\n                  placeholder=\"请输入操作项数\"\n                  disabled\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"已执行项数：\" prop=\"yzxczxs\">\n                <el-input-number\n                  v-model=\"form.yzxczxs\"\n                  placeholder=\"请输入已执行操作项数\"\n                  disabled\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"未执行项数：\" prop=\"wzxczxs\">\n                <el-input-number\n                  v-model=\"form.wzxczxs\"\n                  placeholder=\"请输入未执行操作项数\"\n                  disabled\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"是否已执行：\"\n                prop=\"sfyzx\"\n                @change=\"handleChangeOfSfzx\"\n                :rules=\"\n                  form.status === '3'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-select\n                  v-model=\"form.sfyzx\"\n                  :placeholder=\"isDisabledBj ? '' : '请选择'\"\n                  :disabled=\"isDisabledBj\"\n                >\n                  <el-option\n                    v-for=\"item in sfyzxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item\n                label=\"未执行原因：\"\n                prop=\"wzxyy\"\n                v-if=\"form.sfyzx === '未执行'\"\n              >\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"form.wzxyy\"\n                  :disabled=\"isDisabledBj\"\n                  placeholder=\"请输入内容\"\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"操作任务：\" prop=\"czrw\">\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"form.czrw\"\n                  :disabled=\"isDisabledBj\"\n                  placeholder=\"请输入内容\"\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"上传图片：\">\n                <el-upload\n                  :disabled=\"isDisabledBj\"\n                  action=\"\"\n                  ref=\"uploadImg\"\n                  accept=\"image/jpeg,image/jpg,image/png\"\n                  :headers=\"header\"\n                  :multiple=\"true\"\n                  :on-change=\"handleChange\"\n                  :data=\"uploadImgData\"\n                  :file-list=\"imgList\"\n                  :auto-upload=\"false\"\n                  list-type=\"picture-card\"\n                  :on-preview=\"handlePictureCardPreview\"\n                  :on-progress=\"handleProgress\"\n                  :on-remove=\"handleRemove\"\n                >\n                  <i class=\"el-icon-plus\"></i>\n                </el-upload>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n\n        <!--预览，查看全部操作项-->\n        <div>\n          <div align=\"left\">\n            <el-upload\n              v-if=\"!isDisabled\"\n              action=\"\"\n              ref=\"upload\"\n              accept=\".xlsx\"\n              :limit=\"1\"\n              :auto-upload=\"false\"\n              :show-file-list=\"false\"\n              :on-change=\"importExcel\"\n            >\n              <el-button type=\"info\" @click.stop=\"handleYlChange\"\n                >预览</el-button\n              >\n              <el-button\n                type=\"success\"\n                icon=\"el-icon-download\"\n                @click.stop=\"exportExcel\"\n                >导出</el-button\n              >\n              <el-button type=\"success\" icon=\"el-icon-upload\">导入</el-button>\n              <div\n                slot=\"tip\"\n                class=\"el-upload__tip\"\n                style=\"width: 220px;color: red\"\n              >\n                先导出，再导入，只能上传当前页面导出的Excel文件\n              </div>\n            </el-upload>\n            <!-- <input type=\"file\" @change=\"importExcel\" v-if=\"(isDisabled && buttonNameShow) || hasSuperRole\"/> -->\n          </div>\n          <el-dialog :title=\"titleyl\" :visible.sync=\"yl\" append-to-body>\n            <!--第一个表格-->\n            <el-table\n              :data=\"propTableData.colFirst\"\n              ref=\"propTable\"\n              disabled\n              border\n              stripe\n              style=\"width: 100%\"\n              :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n            >\n              <el-table-column\n                align=\"center\"\n                width=\"120\"\n                prop=\"xh\"\n                label=\"顺序号\"\n                sortable\n              >\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"请输入顺序号\"\n                      disabled\n                      v-model=\"scope.row.xh\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"请输入操作项目\"\n                      disabled\n                      v-model=\"scope.row.czrw\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-dialog>\n        </div>\n\n        <!--列表-->\n        <div>\n          <div align=\"right\">\n            <el-checkbox @change=\"handleCheckAllChange\" :disabled=\"isDisabledBj\"\n              >全选</el-checkbox\n            >\n          </div>\n          <!--第一个表格-->\n          <el-table\n            :data=\"propTableData.colFirst\"\n            ref=\"propTable\"\n            :disabled=\"isDisabled\"\n            height=\"300\"\n            border\n            stripe\n            style=\"width: 100%\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <el-table-column\n              align=\"center\"\n              width=\"120\"\n              prop=\"xh\"\n              label=\"顺序号\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input-number\n                    size=\"small\"\n                    v-model=\"scope.row.xh\"\n                    :min=\"1\"\n                    :precision=\"0\"\n                    controls-position=\"right\"\n                    :disabled=\"isDisabled\"\n                  ></el-input-number>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入操作项目\"\n                    :disabled=\"isDisabled\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" width=\"80\" label=\"是否完成\">\n              <template slot-scope=\"scope\">\n                <el-checkbox\n                  v-model=\"scope.row.sfwc\"\n                  :disabled=\"isDisabledBj\"\n                  @change=\"handleCheckChange\"\n                ></el-checkbox>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" width=\"80\">\n              <template slot=\"header\" slot-scope=\"scope\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  :disabled=\"isDisabled\"\n                  icon=\"el-icon-plus\"\n                  @click=\"listFirstAdd(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n              <template slot-scope=\"scope\">\n                <el-button\n                  type=\"text\"\n                  icon=\"el-icon-delete\"\n                  :disabled=\"isDisabled\"\n                  @click=\"listFirstDel(scope.row)\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button\n          v-if=\"\n            isDisabled &&\n              buttonNameShow &&\n              form.status > 0 &&\n              form.status !== '3'\n          \"\n          type=\"info\"\n          @click=\"getSbFsBj('rollback')\"\n          >退 回\n        </el-button>\n        <el-button\n          v-if=\"isDisabled && buttonNameShow\"\n          type=\"success\"\n          @click=\"getSbFsBj('complete')\"\n          >{{ buttonName }}\n        </el-button>\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"saveRow\"\n          >保 存</el-button\n        >\n      </div>\n    </el-dialog>\n    <!--  工作流  -->\n    <activiti\n      ref=\"activiti\"\n      :processData=\"processData\"\n      :isShow=\"isShow\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n    ></activiti>\n    <!-- 流程详情 -->\n    <time-line\n      :value=\"timeLineShow\"\n      :timeData=\"timeData\"\n      @closeTimeLine=\"colseTimeLine\"\n    />\n    <!--流程图查看-->\n    <el-dialog\n      title=\"审批流程图\"\n      :visible.sync=\"openLoadingImg\"\n      width=\"56%\"\n      append-to-body\n      :close-on-click-modal=\"false\"\n    >\n      <div>\n        <img :src=\"imgSrc\" style=\"width: 100%\" />\n      </div>\n    </el-dialog>\n    <!--图片放大展示弹出框-->\n    <el-image-viewer\n      v-if=\"imgDialogVisible\"\n      :on-close=\"onClose\"\n      :url-list=\"dialogImageUrl\"\n      zIndex=\"3000\"\n    />\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport api from \"@/utils/request\";\nimport ElImageViewer from \"element-ui/packages/image/src/image-viewer\";\nimport {\n  exportPdf,\n  exportWord,\n  getBdzSelectList,\n  getCzpmxList,\n  getListLsp,\n  previewFile,\n  remove,\n  saveOrUpdate,\n  updateById\n} from \"@/api/yxgl/bdyxgl/bddzczp\";\nimport ElectronicAuthDialog from \"com/ElectronicAuthDialog\";\n//流程\nimport activiti from \"com/activiti_czp\";\nimport timeLine from \"com/timeLine\";\nimport { HistoryList } from \"@/api/activiti/processTask\";\nimport { getUsers } from \"@/api/system/usergroup\";\nimport { getUUID } from \"@/utils/ruoyi\";\nimport { getFgsOptions } from \"@/api/yxgl/bdyxgl/zbgl\";\nimport { exportToExcel, importFromExcel } from \"@/components/common/excel.js\";\n\nexport default {\n  name: \"dzczp\",\n  components: { ElectronicAuthDialog, activiti, timeLine, ElImageViewer },\n  data() {\n    return {\n      loading: false,\n      hasSuperRole: this.$store.getters.hasSuperRole,\n      jlrList: [],\n      //状态下拉框数据\n      statusOptions: [\n        {\n          value: \"0\",\n          label: \"操作票填报\"\n        },\n        {\n          value: \"1\",\n          label: \"班组审核\"\n        },\n        {\n          value: \"2\",\n          label: \"分公司审核\"\n        },\n        {\n          value: \"3\",\n          label: \"操作票办结\"\n        },\n        {\n          value: \"4\",\n          label: \"结束\"\n        },{ label: \"作废\", value: \"7\" }\n      ],\n      buttonNameShow: false,\n      buttonName: \"\",\n      isDisabledBj: true,\n      //组织结构下拉数据\n      organizationSelectedList: [],\n      bjr: \"\",\n      currentUser: this.$store.getters.name,\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"czpsh\",\n        businessKey: \"\",\n        businessType: \"倒闸操作票\",\n        variables: {},\n        defaultFrom: true,\n        nextUser: \"\",\n        processType: \"complete\"\n      },\n      // 是否已执行下拉框\n      sfyzxList: [\n        { label: \"已执行\", value: \"已执行\" },\n        { label: \"未执行\", value: \"未执行\" }\n      ],\n      //工作流弹窗\n      isShow: false,\n      //流程图查看\n      openLoadingImg: false,\n      imgSrc: \"\", //流程图查看地址\n      timeData: [],\n      timeLineShow: false,\n      //弹出框标题\n      activitiOption: { title: \"上报\" },\n      titleyl: \"\",\n      isShowSh: false,\n      isShShowDetails: false,\n      yl: false,\n      //图片地址url\n      dialogImageUrl: \"\",\n      //展示图片dialog控制\n      imgDialogVisible: false,\n      bdzList: [],\n      isIndeterminate: true,\n      // 多选框选中的id\n      ids: [],\n      single: true,\n      multiple: true,\n      selectData: [],\n      //弹出框中表格数据\n      propTableData: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      rules: {\n        // kssj: [\n        //   {required: true, message: '操作开始时间不能为空', trigger: 'blur'}\n        // ],\n        // jssj: [\n        //   {required: true, message: '操作结束时间不能为空', trigger: 'change'}\n        // ],\n        fgs: [{ required: true, message: \"分公司不能为空\", trigger: \"select\" }],\n        bdzmc: [\n          { required: true, message: \"变电站不能为空\", trigger: \"select\" }\n        ],\n        czrw: [\n          { required: true, message: \"操作任务不能为空\", trigger: \"blur\" }\n        ],\n        // xlr: [\n        //   {required: true, message: '下令人不能为空', trigger: 'blur'}\n        // ],\n        czxs: [\n          { required: true, message: \"操作项数不能为空\", trigger: \"blur\" }\n        ],\n        yzxczxs: [\n          { required: true, message: \"已执行项数不能为空\", trigger: \"blur\" }\n        ],\n        wzxczxs: [\n          { required: true, message: \"未执行项数不能为空\", trigger: \"blur\" }\n        ]\n      },\n      //form表单\n      form: {\n        bh: \"\",\n        bdzmc: \"\",\n        kssj: \"\",\n        jssj: \"\",\n        czrw: \"\",\n        czr: \"\",\n        jhr: \"\",\n        xlr: \"\",\n        // spr: '',\n        status: \"\",\n        lx: 2, //变电\n        colFirst: []\n      },\n      //上传图片时的携带的其他参数\n      uploadImgData: {\n        businessId: \"\" //携带的表单主键id\n      },\n      //上传图片时的请求头\n      header: {},\n      //图片list\n      imgList: [],\n      //详情弹框是否显示\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      checkedAll: false,\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          bm: \"\",\n          bdzmc: \"\",\n          czsjArr: [],\n          kssjArr: [],\n          // jssjArr: [],\n          czrw: \"\",\n          czr: \"\",\n          jhr: \"\",\n          xlrmc: \"\",\n          status: \"\"\n          // spr: ''\n        }, //查询条件\n        fieldList: [\n          { label: \"编号\", value: \"bm\", type: \"input\", clearable: true },\n\n          {\n            label: \"分公司\",\n            value: \"fgs\",\n            type: \"select\",\n            options: [],\n            clearable: true\n          },\n          {\n            label: \"变电站名称\",\n            value: \"bdzmc\",\n            type: \"select\",\n            clearable: true,\n            options: []\n          },\n          { label: \"操作任务\", value: \"czrw\", type: \"input\", clearable: true },\n          { label: \"操作人\", value: \"czr\", type: \"input\", clearable: true },\n          { label: \"监护人\", value: \"jhrmc\", type: \"input\", clearable: true },\n          { label: \"下令人\", value: \"xlrmc\", type: \"input\", clearable: true },\n          {\n            label: \"状态\",\n            value: \"status\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            clearable: true,\n            options: [\n              {\n                value: \"0\",\n                label: \"操作票填报\"\n              },\n              {\n                value: \"1\",\n                label: \"班组审核\"\n              },\n              {\n                value: \"2\",\n                label: \"分公司审核\"\n              },\n              {\n                value: \"3\",\n                label: \"操作票办结\"\n              },{ label: \"作废\", value: \"7\" }\n            ]\n          }\n          // {label: '审票人', value: 'spr', type: 'input', clearable: true}\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"编号\", prop: \"bm\", minWidth: \"80\" },\n          { label: \"状态\", prop: \"statusCn\", minWidth: \"80\" },\n          { label: \"分公司\", prop: \"fgsmc\", minWidth: \"100\" },\n          { label: \"变电站名称\", prop: \"bdzmcs\", minWidth: \"120\" },\n          { label: \"操作开始时间\", prop: \"kssj\", minWidth: \"110\" },\n          { label: \"操作结束时间\", prop: \"jssj\", minWidth: \"140\" },\n          { label: \"操作任务\", prop: \"czrw\", minWidth: \"160\" },\n          { label: \"操作人\", prop: \"czr\", minWidth: \"60\" },\n          { label: \"监护人\", prop: \"jhrmc\", minWidth: \"60\" },\n          { label: \"下令人\", prop: \"xlrmc\", minWidth: \"60\" },\n          { label: \"审票人\", prop: \"bzsprmc\", minWidth: \"60\" }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        //变电\n        lx: 2,\n        //用来区分历史票库，1-已办结，2-未办结\n        sfbj: 2\n      },\n      xlrList: [],\n      sprList: []\n    };\n  },\n  async mounted() {\n    this.getFgsOptions();\n    //获取变电站下拉框数据\n    this.getBdzSelectList();\n    //获取token\n    this.header.token = getToken();\n    this.xlrList = await this.getGroupUsers(61, \"\");\n    this.sprList = await this.getGroupUsers(13, \"\");\n\n    await this.getData(this.$route.query);\n  },\n  methods: {\n    //作废票\n    nullifyGzp(objId) {\n      this.$confirm(\"票作废后只能查看，不能进行任何操作，确认作废吗?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(async () => {\n          let { code } = await updateById({ status: 7, objId: objId });\n          if (code === \"0000\") {\n            this.$message.success(\"操作成功!!\");\n            this.getData();\n          }\n        })\n        .catch(() => {});\n    },\n    exportExcel() {\n      let excelData = this.propTableData.colFirst.map(item => ({ \"操作项目\": item.czrw}));\n      exportToExcel(excelData, \"操作项目.xlsx\");\n    },\n    importExcel(file, fileList) {\n      let fileName = file.name\n      if (!fileName.includes(\"操作项目\")) {\n        this.msgError(\"文件有误，请检查\")\n        this.$refs.upload.clearFiles()\n        return\n      }\n      importFromExcel(file)\n        .then(data => {\n          this.ids = this.propTableData.colFirst.map(item => item.objId)\n          this.propTableData.colFirst = data.map(item => ({xh: item.__rowNum__ , czrw: item[\"操作项目\"]}));\n        })\n        .catch(error => {\n          console.error(\"导入失败\", error);\n        });\n      this.$refs.upload.clearFiles()\n    },\n    /**\n     * 获取分公司下拉数据\n     */\n    getFgsOptions() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.organizationSelectedList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"fgs\") {\n            return (item.options = this.organizationSelectedList);\n          }\n        });\n      });\n    },\n    filterReset(val) {\n      (this.params = {\n        //变电\n        lx: 2,\n        //用来区分历史票库，1-已办结，2-未办结\n        sfbj: 2\n      }),\n        this.filterInfo.fieldList.forEach(item => {\n          if (item.type === \"checkbox\") {\n            item.checkboxValue = [];\n          }\n        });\n    },\n    getShow() {\n      this.buttonNameShow = false;\n      switch (this.form.status) {\n        case \"0\":\n          this.buttonName = \"上 报\";\n          if (this.currentUser === this.form.createBy) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case \"1\":\n          this.buttonName = \"提 交\";\n          if (this.form.bzspr === this.currentUser) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case \"2\":\n          this.buttonName = \"提 交\";\n          if (this.form.fgsspr === this.currentUser) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case \"3\":\n          this.buttonName = \"办 结\";\n          if (this.form.bjr === this.currentUser) {\n            this.buttonNameShow = true;\n            this.isDisabledBj = false;\n          }\n          break;\n      }\n    },\n\n    getGroupUsers(personGroupId, deptId) {\n      return getUsers({\n        personGroupId: personGroupId,\n        deptId: deptId,\n        deptName: \"\"\n      }).then(res => {\n        return res.data;\n      });\n    },\n    //预览文件\n    async previewFile(row) {\n      try {\n        let exportData = { ...row };\n        await previewFile(exportData, \"bdzdzczp\");\n      } catch (e) {\n        this.$message.error(\"预览失败！\");\n      }\n    },\n    //清空\n    change(e) {\n      this.$forceUpdate();\n    },\n    //关闭弹窗\n    closeActiviti() {\n      if (this.$refs.activiti.$refs.form) {\n        this.$refs.activiti.$refs.form.resetFields();\n      }\n      this.isShow = false;\n    },\n    //关闭流程查看页面\n    colseTimeLine() {\n      this.timeLineShow = false;\n    },\n    async showTimeLine(row) {\n      this.processData.businessKey = row.objId;\n      let { code, data } = await HistoryList(this.processData);\n      this.timeData = data;\n      this.timeLineShow = true;\n    },\n    //流程图片查看\n    showProcessImg(row) {\n      this.openLoadingImg = true;\n      this.imgSrc =\n        \"/activiti-api/process/read-resource?processDefinitionKey=czpsh&businessKey=\" +\n        row.objId +\n        \"&t=\" +\n        new Date().getTime();\n    },\n    //工作流回传数据\n    async todoResult(data) {\n      let row = {\n        objId: data.businessKey,\n        isStart: 1,\n        isBack: data.processType === \"rollback\" ? 1 : 0\n      };\n      if (data.processType === \"rollback\") {\n        switch (data.activeTaskName) {\n          case \"操作票填报\":\n            row.status = \"0\";\n            break;\n          case \"班组审核\":\n            row.status = \"1\";\n            break;\n        }\n      } else {\n        switch (data.activeTaskName) {\n          case \"班组审核\":\n            row.status = \"1\";\n            row.bzspr = data.nextUser;\n            break;\n          case \"分公司审核\":\n            row.status = \"2\";\n            row.fgsspr = data.nextUser;\n            break;\n          case \"操作票办结\":\n            row.status = \"3\";\n            row.bjr = data.nextUser;\n            break;\n          case \"结束\":\n            row.status = \"4\";\n            break;\n        }\n      }\n      let { code } = await updateById(row);\n      if (code === \"0000\") {\n        await this.getData();\n      } else {\n        this.$message.warning(\"操作失败\");\n      }\n    },\n    //上报发送办结\n    async getSbFsBj(type) {\n      let row = { ...this.form };\n      if (type === \"complete\") {\n        switch (row.status) {\n          case \"0\":\n            this.isShowDetails = false;\n            this.processData.variables.pass = true;\n            this.processData.businessKey = row.objId;\n            this.processData.processType = type;\n            this.activitiOption.title = \"提交\";\n            this.processData.defaultFrom = true;\n            this.processData.rylx = \"班组审核人\";\n            this.processData.dw = row.fgs;\n            this.processData.personGroupId = 13;\n            this.isShow = true;\n            break;\n          case \"1\":\n            this.isShowDetails = false;\n            this.$confirm(\"是否需要提交分公司审核?\", \"选择\", {\n              confirmButtonText: \"是\",\n              cancelButtonText: \"否\",\n              type: \"warning\"\n            })\n              .then(async () => {\n                this.processData.variables.pass = true;\n                this.processData.businessKey = row.objId;\n                this.processData.processType = type;\n                this.processData.dw = row.fgs;\n                this.processData.rylx = \"分公司审核人\";\n                this.processData.variables.isFgs = true;\n                this.activitiOption.title = \"提交\";\n                this.processData.defaultFrom = true;\n                this.processData.personGroupId = 14;\n                this.isShow = true;\n              })\n              .catch(() => {\n                this.processData.variables.pass = true;\n                this.processData.businessKey = row.objId;\n                this.processData.processType = type;\n                this.processData.dw = row.fgs;\n                this.processData.rylx = \"办结人\";\n                this.processData.variables.isFgs = false;\n                this.processData.defaultFrom = true;\n                this.processData.personGroupId = 15;\n                this.isShow = true;\n              });\n            break;\n          case \"2\":\n            this.isShowDetails = false;\n            this.processData.variables.pass = true;\n            this.processData.businessKey = row.objId;\n            this.processData.processType = type;\n            this.activitiOption.title = \"提交\";\n            this.processData.defaultFrom = true;\n            this.processData.personGroupId = 15;\n            this.processData.dw = row.fgs;\n            this.processData.rylx = \"办结人\";\n            this.isShow = true;\n            break;\n          case \"3\":\n            await this.$refs[\"form\"].validate(async valid => {\n              if (valid) {\n                try {\n                  this.form.colFirst = this.propTableData.colFirst;\n                  this.form.objIdList = this.ids;\n                  this.uploadImgData.businessId = this.form.objId;\n                  this.uploadForm();\n                  await saveOrUpdate(this.form).then(res => {\n                    if (res.code === \"0000\") {\n                      this.isShowDetails = false;\n                      this.getData();\n                      this.processData.variables.pass = true;\n                      this.processData.businessKey = row.objId;\n                      this.processData.processType = type;\n                      this.activitiOption.title = \"办结\";\n                      this.processData.defaultFrom = false;\n                      this.isShow = true;\n                    } else {\n                      this.$message.error(\"失败\");\n                    }\n                  });\n                } catch (e) {}\n              }\n            });\n        }\n      } else {\n        this.isShowDetails = false;\n        this.processData.businessKey = row.objId;\n        this.processData.processType = type;\n        this.activitiOption.title = \"回退原因填写\";\n        this.processData.defaultFrom = true;\n        this.processData.variables.pass = false;\n        this.isShow = true;\n      }\n    },\n\n    //改造后的上传多个图片文件\n    uploadForm() {\n      var newUrl = []; //用来存放当前未改动过的图片url,此图片不进行删除处理，其余当前业务id下面的图片数据将进行删除\n      var imageType = [\"png\", \"jpg\"];\n      const formData = new FormData();\n      // 因为要传一个文件数组过去，所以要循环append\n      this.imgList.forEach(file => {\n        if (file.raw == undefined) {\n          newUrl.push(file.url);\n        }\n        formData.append(\"files\", file.raw);\n      });\n      formData.append(\"businessId\", this.uploadImgData.businessId); // 自定义参数\n      formData.append(\"newUrl\", newUrl); // 未改动过的图片url\n      formData.append(\"type\", imageType); // 未改动过的图片url\n      api\n        .requestPost(\"/isc-api/file/uploadFiles\", formData, 1)\n        .then(res => {\n          // 清空图片列表（一定要清空，否则上传成功后还是会调用handleChange（）函数，上传成功后列表中还存在图片）\n          this.imgList = [];\n          //查询接口数据\n          this.getData();\n        })\n        .catch(res => {\n          this.$message.success(\"图片上传失败！\");\n        });\n    },\n    // 选择文件时，往fileList里添加\n    handleChange(file, fileList) {\n      this.imgList = fileList;\n    },\n    handleProgress(event, file, fileList) {},\n    //图片移除\n    handleRemove(file, fileList) {\n      this.imgList = fileList;\n    },\n    //图片放大\n    handlePictureCardPreview(file) {\n      this.dialogImageUrl = [file.url];\n      this.imgDialogVisible = true;\n    },\n    onClose() {\n      this.imgDialogVisible = false;\n    },\n    // 全选框\n    handleCheckAllChange(val) {\n      if (val) {\n        this.form.yzxczxs = this.form.czxs;\n        this.form.wzxczxs = 0;\n      } else {\n        this.form.wzxczxs = this.form.czxs;\n        this.form.yzxczxs = 0;\n      }\n      this.propTableData.colFirst.forEach(item => {\n        item.sfwc = val;\n      });\n    },\n    //选择已执行时，操作项目默认默认全选\n    handleChangeOfSfzx(val) {\n      if (val === \"已执行\") {\n        this.form.yzxczxs = this.form.czxs;\n        this.form.wzxczxs = 0;\n        this.propTableData.colFirst.forEach(item => {\n          item.sfwc = true;\n        });\n      } else {\n        this.form.wzxczxs = this.form.czxs;\n        this.form.yzxczxs = 0;\n        this.propTableData.colFirst.forEach(item => {\n          item.sfwc = false;\n        });\n      }\n    },\n    handleCheckChange(val) {\n      if (val) {\n        ++this.form.yzxczxs;\n      } else {\n        --this.form.yzxczxs;\n      }\n      this.form.wzxczxs = this.form.czxs - this.form.yzxczxs;\n    },\n    // 预览按钮\n    handleYlChange() {\n      this.titleyl = \"查看操作项目\";\n      this.yl = true;\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        const param = { ...this.params, ...params };\n        this.params = param;\n        this.loading = true;\n        if (!param.status) {\n          param.status = \"0,1,2,3,4\";\n        }\n        param.mySorts = [{ prop: \"updateTime\", asc: false }];\n        const { data, code } = await getListLsp(param);\n        if (code === \"0000\") {\n          for (let i of data.records) {\n            i.fgsmc = this.formatSsgs(i.fgs);\n            this.statusOptions.forEach(element => {\n              if (i.status === element.value) {\n                i.statusCn = element.label;\n              }\n            });\n          }\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          this.loading = false;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    /**\n     *获取操作票明细\n     */\n    async getCzpmx(row) {\n      try {\n        const { data, code } = await getCzpmxList({ objId: row.objId });\n        if (code === \"0000\") {\n          this.propTableData.colFirst = data;\n          this.propTableData.colFirst.forEach(e => {\n            e.uuid = getUUID();\n          });\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n\n    //修改按钮\n    async getUpdate(row) {\n      await this.getCzpmx(row);\n      this.title = \"变电倒闸操作票修改\";\n      this.isDisabled = false;\n      this.isDisabledBj = false;\n      this.form = { ...row };\n      //图片结合\n      this.imgList = this.form.imgList;\n      this.jlrList = await this.getGroupUsers(62, \"\");\n      this.isShowDetails = true;\n      this.getShow();\n    },\n    //详情按钮\n    async getDetails(row) {\n      await this.getCzpmx(row);\n      this.title = \"变电操作票详情查看\";\n      this.form = { ...row };\n      this.jlrList = await this.getGroupUsers(62, \"\");\n      //图片结合\n      this.imgList = this.form.imgList;\n      this.isDisabled = true;\n      this.isDisabledBj = true;\n      this.isShowDetails = true;\n      this.getShow();\n    },\n    // 保存按钮\n    async saveRow() {\n      await this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          try {\n            this.form.colFirst = this.propTableData.colFirst;\n            this.form.objIdList = this.ids;\n            saveOrUpdate(this.form).then(res => {\n              if (res.code === \"0000\") {\n                this.$message.success(\"操作成功\");\n              }\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n              this.isShowDetails = false;\n            });\n          } catch (e) {}\n        } else {\n          this.$message.error(\"校验未通过！\");\n          return false;\n        }\n      });\n    },\n\n    //删除按钮\n    async deleteRow(row) {\n      let objId = \"\";\n      if (row.objId) {\n        objId = row.objId;\n      } else {\n        objId = this.ids[0];\n      }\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove(objId).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    //表格新增\n    listFirstAdd() {\n      let row = {\n        xh: \"\",\n        czrw: \"\",\n        sfwc: \"\",\n        uuid: getUUID()\n      };\n      this.propTableData.colFirst.push(row);\n      this.propTableData.sel = row;\n      this.form.czxs = this.propTableData.colFirst.length;\n      this.form.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //表格删除\n    listFirstDel(row) {\n      if (row.objId) {\n        this.ids.push(row.objId);\n      }\n      this.propTableData.colFirst = this.propTableData.colFirst.filter(\n        item => item.uuid !== row.uuid\n      );\n      this.form.czxs = this.propTableData.colFirst.length;\n      this.form.wzxczxs = this.propTableData.colFirst.length;\n    },\n\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false;\n    },\n    selectChange(selection) {\n      this.ids = selection.map(item => item.objId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n      this.selectData = selection;\n    },\n    /**\n     * 获取变电站下拉框数据\n     */\n    getBdzSelectList() {\n      getBdzSelectList({}).then(res => {\n        this.bdzList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"bdzmc\") {\n            return (item.options = this.bdzList);\n          }\n        });\n      });\n    },\n    //导出word\n    async exportWord(row) {\n      try {\n        let exportData = { ...row };\n        await exportWord(exportData, \"bdzdzczp\", \"变电站倒闸操作票.docx\");\n      } catch (e) {\n        this.$message.error(\"导出失败！\");\n      }\n    },\n    //导出Pdf\n    async exportPdf(row) {\n      try {\n        let exportData = { ...row };\n        await exportPdf(exportData, \"bdzdzczp\", \"变电站倒闸操作票.pdf\");\n      } catch (e) {\n        this.$message.error(\"导出失败！\");\n      }\n    },\n    //下拉框change事件\n    handleEvent(val, eventValue) {\n      if (val.label === \"fgs\") {\n        getBdzSelectList({ ssdwbm: val.value.toString() }).then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value === \"bdzmc\") {\n              this.$set(eventValue, \"bdzmc\", \"\");\n              return (item.options = res.data);\n            }\n          });\n        });\n      }\n    },\n    formatSsgs(ssgs) {\n      let pageOrganizationSelectedList = JSON.parse(\n        JSON.stringify(this.organizationSelectedList)\n      );\n      pageOrganizationSelectedList.push({\n        label: \"港东变电分公司\",\n        value: \"3002\"\n      });\n      pageOrganizationSelectedList.push({\n        label: \"港中变电分公司\",\n        value: \"3003\"\n      });\n      if (ssgs) {\n        let filter = pageOrganizationSelectedList.filter(g => g.value === ssgs);\n        if (filter.length > 0) {\n          return filter[0].label;\n        } else {\n          return \"\";\n        }\n      } else {\n        return \"\";\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.card-style {\n  background-color: #e0eff2;\n  line-height: 3vh;\n  border-radius: 3px;\n  font-weight: 800;\n  padding-left: 1vw;\n  margin-bottom: 3vh;\n}\n\n/*控制input输入框边框是否显示*/\n.elInput >>> .el-input__inner {\n  border: 0;\n}\n\n.center {\n  text-align: center; /*让div内部文字居中*/\n}\n</style>\n"]}]}