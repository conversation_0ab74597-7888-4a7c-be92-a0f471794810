{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\sbztpj.vue?vue&type=style&index=0&id=84574e92&scoped=true&lang=scss&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\sbztpj.vue", "mtime": 1706897323226}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5zeEluZm97CiAgYmFja2dyb3VuZDojZmZiYTAwNzU7CiAgYm9yZGVyLWNvbG9yOiAjZmZiYTAwNzU7Cn0KLnNienRwal90b3B7CiAgbGlzdC1zdHlsZS10eXBlOiBub25lOwogIG1hcmdpbjogMCAwIDE1cHggMDsKICBwYWRkaW5nLWxlZnQ6MDsKICBwYWRkaW5nLWJvdHRvbToyM3B4OwogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC13cmFwOiB3cmFwOwogIGJvcmRlcjogMXB4IHNvbGlkICNlMmU4ZWQ7CiAgbGl7CiAgICBtYXJnaW4tdG9wOiAyM3B4OwogICAgbWFyZ2luLWxlZnQ6IDM0cHg7CiAgfQp9Ci5zYnp0cGpfdGFibGV7CiAgYm9yZGVyOiAxcHggc29saWQgI2U4ZWRmMTsKfQo="}, {"version": 3, "sources": ["sbztpj.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4TA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "sbztpj.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div>\n      <el-white class=\"button-group\">\n        <div class=\"button_btn\">\n          <el-button type=\"warning\" icon=\"el-icon-search\" @click=\"selectInfo\" class=\"choose\">筛选</el-button>\n          <el-button type=\"cyan\" icon=\"el-icon-view\" @click=\"detailInfo\">查看详情</el-button>\n          <el-button type=\"cyan\" icon=\"el-icon-document\" @click=\"outInfo\">导出</el-button>\n        </div>\n        <ul class=\"sbztpj_top\" v-show=\"topShow\">\n          <li>\n            <span>设备名称：</span>\n            <el-input v-model=\"inputTxt\" placeholder=\"请输入内容\" style=\"width:200px\"></el-input>\n          </li>\n          <li>\n            <span>电压等级：</span>\n            <el-select v-model=\"dyTxt\" placeholder=\"请选择\">\n              <el-option\n                v-for=\"item in fieldList[0].options\"\n                :key=\"item.value\"\n                :label=\"item.label\"\n                :value=\"item.value\">\n              </el-option>\n            </el-select>\n          </li>\n          <li>\n            <span>定期评价结果：</span>\n            <el-select v-model=\"dqpjTxt\" placeholder=\"请选择\">\n              <el-option\n                v-for=\"item in fieldList[1].options\"\n                :key=\"item.value\"\n                :label=\"item.label\"\n                :value=\"item.value\">\n              </el-option>\n            </el-select>\n          </li>\n          <li>\n            <span>最新评价结果：</span>\n            <el-select v-model=\"zxpjTxt\" placeholder=\"请选择\">\n              <el-option\n                v-for=\"item in fieldList[2].options\"\n                :key=\"item.value\"\n                :label=\"item.label\"\n                :value=\"item.value\">\n              </el-option>\n            </el-select>\n          </li>\n          <li>\n            <span>定期评价日期：</span>\n            <el-date-picker\n              v-model=\"dateValue1\"\n              type=\"daterange\"\n              range-separator=\"至\"\n              start-placeholder=\"开始日期\"\n              end-placeholder=\"结束日期\">\n            </el-date-picker>\n          </li>\n          <li>\n            <span>最新评价日期：</span>\n            <el-date-picker\n              v-model=\"dateValue2\"\n              type=\"daterange\"\n              range-separator=\"至\"\n              start-placeholder=\"开始日期\"\n              end-placeholder=\"结束日期\">\n            </el-date-picker>\n          </li>\n          <li>\n            <el-button type=\"cyan\" icon=\"el-icon-search\" @click=\"handleFind\">查询</el-button>\n            <el-button type=\"warning\" icon=\"el-icon-refresh\" @click=\"handleReset\">重置</el-button>\n          </li>\n        </ul>\n        <comp-table :table-and-page-info=\"tableAndPageInfo\" @update:multipleSelection=\"handleSelectionChange\"/>\n      </el-white>\n    </div>\n    <!-- 详情 对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" append-to-body width=\"50%\" @close=\"handleClose\" v-dialogDrag>\n      <template>\n        <el-table\n          :data=\"tableData\"\n          style=\"width: 100%\" class=\"sbztpj_table\">\n          <el-table-column\n            prop=\"sblxmc\"\n            label=\"所属设备\"\n            width=\"auto\">\n          </el-table-column>\n          <el-table-column\n            prop=\"sbbm\"\n            label=\"部件名称\"\n            width=\"auto\">\n          </el-table-column>\n          <el-table-column\n            prop=\"sbbw\"\n            label=\"状态量名称\">\n          </el-table-column>\n          <el-table-column\n            prop=\"sblx\"\n            label=\"状态量来源\">\n          </el-table-column>\n          <el-table-column\n            prop=\"qxms\"\n            label=\"数据回溯\">\n          </el-table-column>\n          <el-table-column\n            prop=\"qxdj\"\n            label=\"扣分理由\">\n          </el-table-column>\n        </el-table>\n      </template>\n      <div v-show=\"formIsEditable\" slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"handleClose\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitForm\">确 认</el-button>\n      </div>\n    </el-dialog>\n\n  </div>\n</template>\n\n<script>\nimport { getPageDataList, remove, saveOrUpdate } from '@/api/dagangOilfield/bzgl/qxbzk'\nimport { getDictTypeData } from '@/api/system/dict/data'\nimport DeviceTree from '@/views/dagangOilfield/bzgl/sbbzk/deviceTree'\nimport { getEquipmentComponentsOptions } from '@/api/dagangOilfield/bzgl/sblxwh/sbbj'\n\nexport default {\n  name: 'xqxbzk',\n  components: { DeviceTree },\n  created() {\n    this.getData()\n  },\n  data() {\n    return {\n      //开始\n      //控制筛选框显示隐藏\n      topShow:false,\n      // 详情框标题\n      title: '',\n      // 新增/修改/详情对话框是否打开\n      open: false,\n      //详情数据\n      tableData: [{\n        flyj: \"重度：道路边的拉线应设防护设施（护坡、反光管、拉线绝缘子）而未设置\",\n        objId: \"00355e990164467a8f482184e8e9544d\",\n        qxdj: \"严重\",\n        qxms: \"拉线防护设施不满足要求\",\n        sbbm: \"拉线\",\n        sbbw: \"钢绞线\",\n        sblx: \"sd19\",\n        sblxmc: \"杆塔1\"\n      }, {\n        flyj: \"重度：道路边的拉线应设防护设施（护坡、反光管、拉线绝缘子）而未设置\",\n        objId: \"00355e990164467a8f482184e8e9544d\",\n        qxdj: \"严重\",\n        qxms: \"拉线防护设施不满足要求\",\n        sbbm: \"拉线\",\n        sbbw: \"钢绞线\",\n        sblx: \"sd19\",\n        sblxmc: \"杆塔2\"\n      }, {\n        flyj: \"重度：道路边的拉线应设防护设施（护坡、反光管、拉线绝缘子）而未设置\",\n        objId: \"00355e990164467a8f482184e8e9544d\",\n        qxdj: \"严重\",\n        qxms: \"拉线防护设施不满足要求\",\n        sbbm: \"拉线\",\n        sbbw: \"钢绞线\",\n        sblx: \"sd19\",\n        sblxmc: \"杆塔3\"\n      }, {\n        flyj: \"重度：道路边的拉线应设防护设施（护坡、反光管、拉线绝缘子）而未设置\",\n        objId: \"00355e990164467a8f482184e8e9544d\",\n        qxdj: \"严重\",\n        qxms: \"拉线防护设施不满足要求\",\n        sbbm: \"拉线\",\n        sbbw: \"钢绞线\",\n        sblx: \"sd19\",\n        sblxmc: \"杆塔4\"\n      }],\n      inputTxt:'',\n      dyTxt:'',\n      dqpjTxt:'',\n      zxpjTxt:'',\n      dateValue1:'',\n      dateValue2:'',\n      fieldList: [\n        {\n          options: [{ label: '10kV', value: '10kV' }, { label: '100kV', value: '100kV' }]\n        },\n        {\n          options: [{ label: '一般', value: '一般' }, { label: '好', value: '好' }]\n        },\n        {\n          options: [{ label: '一般', value: '一般' }, { label: '好', value: '好' }]\n        },\n      ],\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          // { prop: 'zymc', label: '专业', minWidth: '120' },\n          { prop: 'sblxmc', label: '设备类型', minWidth: '180' },\n          { prop: 'sbbm', label: '电压等级', minWidth: '120' },\n          { prop: 'sbbw', label: '出厂日期', minWidth: '120' },\n          { prop: 'qxms', label: '定期评价日期', minWidth: '120' },\n          { prop: 'flyj', label: '定期评价结果', minWidth: '120' },\n          { prop: 'qxdj', label: '最新评价类型', minWidth: '120' },\n          { prop: 'qxdj', label: '最新评价日期', minWidth: '100' },\n          { prop: 'jsyy', label: '最新评价结果', minWidth: '100' },\n          { prop: 'jsyy', label: '得分值', minWidth: '100' },\n        ]\n      },\n      //结束\n\n    }\n  },\n  mounted() {\n  },\n  methods: {\n    /**\n     * 筛选模块\n     */\n    selectInfo(){\n      //判断是否有sxInfo1 class名\n      let box = document.getElementsByClassName('choose')[0];\n      if(box.className.indexOf('sxInfo') < 0){   //判断没有sxInfo class名则添加\n        box.classList.add('sxInfo');\n        this.topShow = true;\n      }else{\n        box.classList.remove('sxInfo');\n        this.topShow = false;\n      }\n    },\n    /**\n     * 查看详情功能\n     */\n    detailInfo(){\n     // console.log('this.id',this.ids)\n   /*   if (this.ids.length !== 0) {\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove(this.ids).then(res => {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            });\n            this.getData();\n          })\n        })\n      } else {\n        this.$message({\n          type: 'info',\n          message: '请选择至少一条数据!'\n        })\n      }*/\n      this.title = '详情'\n      this.open = true\n    },\n    /**\n     * 导出功能\n     */\n    outInfo(){\n\n    },\n    /**\n     * 查询功能\n     */\n    handleFind(){\n\n    },\n    /**\n     * 重置点击事件\n     */\n    handleReset(){\n      this.inputTxt =  this.dyTxt = this.dqpjTxt = this.zxpjTxt = this.dateValue1 = this.dateValue2 = '';\n    },\n    /**\n     * 查询数据\n     */\n    getData(params) {\n      this.queryParams={...this.queryParams,...params}\n      const param = {...this.queryParams, ...params}\n      getPageDataList(param).then(res => {\n        this.tableAndPageInfo.tableData = res.data.records\n        this.tableAndPageInfo.pager.total = res.data.total\n      })\n    },\n    /**\n     * 表格多选框\n     */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.objId)\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n    },\n    /**\n     * 关闭详情弹框框\n     */\n    handleClose() {\n      this.open = false\n    },\n  },\n}\n</script>\n\n<style scoped lang=\"scss\">\n.sxInfo{\n  background:#ffba0075;\n  border-color: #ffba0075;\n}\n.sbztpj_top{\n  list-style-type: none;\n  margin: 0 0 15px 0;\n  padding-left:0;\n  padding-bottom:23px;\n  display: flex;\n  flex-wrap: wrap;\n  border: 1px solid #e2e8ed;\n  li{\n    margin-top: 23px;\n    margin-left: 34px;\n  }\n}\n.sbztpj_table{\n  border: 1px solid #e8edf1;\n}\n</style>\n"]}]}