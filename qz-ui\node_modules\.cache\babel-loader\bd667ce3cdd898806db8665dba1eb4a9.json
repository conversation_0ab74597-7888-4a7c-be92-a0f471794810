{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\activiti_ssqx\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\activiti_ssqx\\index.vue", "mtime": 1742494650095}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKCnJlcXVpcmUoInJlZ2VuZXJhdG9yLXJ1bnRpbWUvcnVudGltZSIpOwoKdmFyIF9hc3luY1RvR2VuZXJhdG9yMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovU2hhbW1wb29sL3dvcmsvY29kZS9kZ3l0LzAxXHU0RUUzXHU3ODAxL3F6LXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2FzeW5jVG9HZW5lcmF0b3IiKSk7Cgp2YXIgX29iamVjdFNwcmVhZDIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkQ6L1NoYW1tcG9vbC93b3JrL2NvZGUvZGd5dC8wMVx1NEVFM1x1NzgwMS9xei11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9vYmplY3RTcHJlYWQyIikpOwoKdmFyIF9lbGVtZW50VWkgPSByZXF1aXJlKCJlbGVtZW50LXVpIik7Cgp2YXIgX3VzZXJncm91cCA9IHJlcXVpcmUoIkAvYXBpL3N5c3RlbS91c2VyZ3JvdXAiKTsKCnZhciBfcHJvY2Vzc1Rhc2sgPSByZXF1aXJlKCJAL2FwaS9hY3Rpdml0aS9wcm9jZXNzVGFzayIpOwoKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0MiA9IHsKICBuYW1lOiAiaW5kZXgiLAogIHByb3BzOiB7CiAgICAvKua1geeoi+WPkei1t+W/heWhq+aVsOaNrgogICAgICAgIHByb2Nlc3NEYXRhOnsKICAgICAgICAgIHByb2Nlc3NEZWZpbml0aW9uS2V5OiLmtYHnqIvlrprkuYnnmoRrZXkiLC8v5b+F5aGrCiAgICAgICAgICB0YXNrSWQ6IuS7u+WKoWlk77yM5aaC5p6c5Lu75Yqh5Zyo5Luj5Yqe5YiX6KGo5pe277yM5Lya5qC55o2u5Luj5Yqe5YiX6KGo6I635Y+WdGFza2lk77yM5ZCm5YiZ6ZyA5Lyg5YWl5Lia5YqhaWTmnaXnoa7lrpp0YXNrIiwKICAgICAgICAgIGJ1c2luZXNzS2V5OiLkuJrliqFpZO+8jOWvueW6lOS4muWKoeeahOS4u+mUriIsLy90YXNraWTlkoxidXNpbmVzc0tleeS4pOiAheW/hemhu+acieWFtuS4reS4gOS4quaJjeWPr+S7peehruWumuS4gOS4qnRhc2sKICAgICAgICAgIGJ1c2luZXNzVHlwZToi5Lia5Yqh57G75Z6L77yM55So5LqO5Yy65YiG5LiN5ZCM55qE5Lia5YqhIi8v5b+F5aGrCiAgICAgICAgICB2YXJpYWJsZXM6IuaLk+WxleWPguaVsCIvL+a1geeoi+WumuS5ieS4reiuvue9rueahOWPguaVsCwKICAgICAgICAgIG5leHRVc2VyOiLlpoLmnpzmtYHnqIvlrp7kvovkuK3lubbmnKrphY3nva7mr4/kuIDkuKroioLngrnnmoTlpITnkIbkurrvvIzliJnpnIDopoHnlKjmiLfmiYvliqjpgInmi6nmr4/kuIDkuKroioLngrnnmoTlpITnkIbkuroiLAogICAgICAgICAgbmV4dFVzZXJJbmZvOnt9LC8v5omL5Yqo5Lyg55qE5a6h5qC45Lq65L+h5oGvCiAgICAgICAgICBwcm9jZXNzVHlwZTonY29tcGxldGUscm9sbGJhY2snLAogICAgICAgICAgZGVmYXVsdEZyb206dHJ1ZSxmYWxzZSDmmK/lkKbpnIDopoHpu5jorqTooajljZUKICAgICAgICB9Ki8KICAgIHByb2Nlc3NEYXRhOiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdDogZnVuY3Rpb24gX2RlZmF1bHQoKSB7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIGRlZmF1bHRGcm9tOiB0cnVlLAogICAgICAgICAgcHJvY2Vzc1R5cGU6ICJjb21wbGV0ZSIKICAgICAgICB9OwogICAgICB9CiAgICB9LAogICAgLy/mmL7npLrpmpDol48KICAgIGlzU2hvdzogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICBkZWZhdWx0OiBmYWxzZQogICAgfSwKICAgIC8v5a2Q57uE5Lu26buY6K6k5Y+C5pWwCiAgICBvcHRpb246IHsKICAgICAgdHlwZTogT2JqZWN0LAogICAgICBkZWZhdWx0OiBmdW5jdGlvbiBfZGVmYXVsdCgpIHsKICAgICAgICByZXR1cm4gewogICAgICAgICAgdGl0bGU6ICLlrqHmibkiCiAgICAgICAgfTsKICAgICAgfQogICAgfQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGRpc2FibGVkOiBmYWxzZSwKICAgICAgZm9ybToge30sCiAgICAgIGRhdGFzOiB7fSwKICAgICAgaXN0b2RvUmVzdWx0OiAiIiwKICAgICAgbG9hZGluZzogbnVsbCwKICAgICAgZ3pmenJPcHRpb25zOiBbXQogICAgfTsKICB9LAogIHdhdGNoOiB7CiAgICBwcm9jZXNzRGF0YTogewogICAgICBoYW5kbGVyOiBmdW5jdGlvbiBoYW5kbGVyKG5ld1ZhbCwgb2xkVmFsKSB7CiAgICAgICAgdmFyIF90aGlzID0gdGhpczsKCiAgICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgICAgX3RoaXMuZGF0YXMgPSAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIG5ld1ZhbCk7CiAgICAgICAgICBjb25zb2xlLmxvZygiZGF0YXMiLCBfdGhpcy5kYXRhcyk7CiAgICAgICAgICB2YXIgbGN6dCA9IF90aGlzLmRhdGFzLnZhcmlhYmxlcy5sY3p0OwogICAgICAgICAgY29uc29sZS5sb2coImFjdGl2aXRpLnRoaXMubGN6dCIsIGxjenQpOwogICAgICAgICAgdmFyIHBlcnNvbkdyb3VwSWQgPSAyODsKICAgICAgICAgIHZhciBkZXB0SWQgPSBfdGhpcy4kc3RvcmUuZ2V0dGVycy5kZXB0SWQ7CgogICAgICAgICAgc3dpdGNoIChsY3p0KSB7CiAgICAgICAgICAgIGNhc2UgIjIiOgogICAgICAgICAgICAgIHBlcnNvbkdyb3VwSWQgPSAyODsgLy8gZGVwdElkID0gdGhpcy4kc3RvcmUuZ2V0dGVycy5kZXB0SWQ7CgogICAgICAgICAgICAgIGRlcHRJZCA9ICIiOwogICAgICAgICAgICAgIGJyZWFrOwoKICAgICAgICAgICAgY2FzZSAiMyI6CiAgICAgICAgICAgICAgcGVyc29uR3JvdXBJZCA9IDI5OwogICAgICAgICAgICAgIGRlcHRJZCA9ICIiOwogICAgICAgICAgICAgIGJyZWFrOwoKICAgICAgICAgICAgY2FzZSAiMTIiOgogICAgICAgICAgICAgIHBlcnNvbkdyb3VwSWQgPSAxMzc7CiAgICAgICAgICAgICAgZGVwdElkID0gIiI7CiAgICAgICAgICAgICAgYnJlYWs7CgogICAgICAgICAgICBjYXNlICI0IjoKICAgICAgICAgICAgICBwZXJzb25Hcm91cElkID0gMzA7CiAgICAgICAgICAgICAgZGVwdElkID0gX3RoaXMuZGF0YXMudmFyaWFibGVzLmZ6Ym07CiAgICAgICAgICAgICAgYnJlYWs7CgogICAgICAgICAgICBjYXNlICI3IjoKICAgICAgICAgICAgICBwZXJzb25Hcm91cElkID0gMzI7IC8vIGRlcHRJZCA9IHRoaXMuZGF0YXMudmFyaWFibGVzLnNicmR3OwoKICAgICAgICAgICAgICBkZXB0SWQgPSAiIjsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgIH0KCiAgICAgICAgICAoMCwgX3VzZXJncm91cC5nZXRVc2VycykoewogICAgICAgICAgICBwZXJzb25Hcm91cElkOiBwZXJzb25Hcm91cElkLAogICAgICAgICAgICBkZXB0SWQ6IGRlcHRJZCwKICAgICAgICAgICAgZGVwdE5hbWU6ICIiCiAgICAgICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgX3RoaXMuZ3pmenJPcHRpb25zID0gcmVzLmRhdGE7CiAgICAgICAgICB9KTsKICAgICAgICB9KTsKICAgICAgfSwKICAgICAgZGVlcDogdHJ1ZSAvLyBpbW1lZGlhdGU6IHRydWUKCiAgICB9CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkge30sCiAgbWV0aG9kczogewogICAgLy/mtYHnqIvmj5DkuqQKICAgIHRvZG9TdWJtaXQ6IGZ1bmN0aW9uIHRvZG9TdWJtaXQoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwoKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoIC8qI19fUFVSRV9fKi9yZWdlbmVyYXRvclJ1bnRpbWUubWFyayhmdW5jdGlvbiBfY2FsbGVlKCkgewogICAgICAgIHZhciByZXN1bHREYXRhLCBfeWllbGQkY29tcGxldGVUYXNrLCBjb2RlLCBkYXRhOwoKICAgICAgICByZXR1cm4gcmVnZW5lcmF0b3JSdW50aW1lLndyYXAoZnVuY3Rpb24gX2NhbGxlZSQoX2NvbnRleHQpIHsKICAgICAgICAgIHdoaWxlICgxKSB7CiAgICAgICAgICAgIHN3aXRjaCAoX2NvbnRleHQucHJldiA9IF9jb250ZXh0Lm5leHQpIHsKICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICBfdGhpczIuZGF0YXMucm91dGVQYXRoID0gX3RoaXMyLiRyb3V0ZS5wYXRoOyAvLyBkZWJ1Z2dlcjsKCiAgICAgICAgICAgICAgICBpZiAoX3RoaXMyLmZvcm0ubmV4dFVzZXIpIHsKICAgICAgICAgICAgICAgICAgX3RoaXMyLmRhdGFzLm5leHRVc2VyID0gX3RoaXMyLmZvcm0ubmV4dFVzZXIudXNlck5hbWU7CiAgICAgICAgICAgICAgICAgIF90aGlzMi5kYXRhcy5uZXh0VXNlck5pY2tOYW1lID0gX3RoaXMyLmZvcm0ubmV4dFVzZXIubmlja05hbWU7CiAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICBpZiAoX3RoaXMyLnByb2Nlc3NEYXRhLm5leHRVc2VySW5mbykgewogICAgICAgICAgICAgICAgICAgIC8v5omL5Yqo5Lyg55qE55So5oi35L+h5oGvCiAgICAgICAgICAgICAgICAgICAgX3RoaXMyLmRhdGFzLm5leHRVc2VyID0gX3RoaXMyLnByb2Nlc3NEYXRhLm5leHRVc2VySW5mby51c2VyTmFtZTsKICAgICAgICAgICAgICAgICAgICBfdGhpczIuZGF0YXMubmV4dFVzZXJOaWNrTmFtZSA9IF90aGlzMi5wcm9jZXNzRGF0YS5uZXh0VXNlckluZm8ubmlja05hbWU7CiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coIuaJi+WKqOS8oOeahOeUqOaIt+S/oeaBryIsIF90aGlzMi5wcm9jZXNzRGF0YS5uZXh0VXNlckluZm8udXNlck5hbWUpOwogICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCJkYXRhcy5uZXh0VXNlck5pY2tOYW1lIiwgX3RoaXMyLnByb2Nlc3NEYXRhLm5leHRVc2VySW5mby5uaWNrTmFtZSk7CiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coImRhdGFzLm5leHRVc2VyTmlja05hbWUiLCBfdGhpczIucHJvY2Vzc0RhdGEubmV4dFVzZXJJbmZvKTsKICAgICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICBfdGhpczIuZGF0YXMubmV4dFVzZXIgPSB1bmRlZmluZWQ7CiAgICAgICAgICAgICAgICAgICAgX3RoaXMyLmRhdGFzLm5leHRVc2VyTmlja05hbWUgPSB1bmRlZmluZWQ7CiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICBpZiAoISghX3RoaXMyLmRhdGFzLm5leHRVc2VyICYmIF90aGlzMi5kYXRhcy5wcm9jZXNzVHlwZSA9PT0gImNvbXBsZXRlIiAmJiBfdGhpczIuZGF0YXMuZGVmYXVsdEZyb20pKSB7CiAgICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSA1OwogICAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICBfdGhpczIuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgICB0eXBlOiAiZXJyb3IiLAogICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi6K+36YCJ5oup5Lq65ZGYISIKICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5hYnJ1cHQoInJldHVybiIpOwoKICAgICAgICAgICAgICBjYXNlIDU6CiAgICAgICAgICAgICAgICBpZiAoX3RoaXMyLmZvcm0uY29tbWVudCkgewogICAgICAgICAgICAgICAgICBfdGhpczIuZGF0YXMudmFyaWFibGVzLmNvbW1lbnQgPSBfdGhpczIuZm9ybS5jb21tZW50OwogICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgIF90aGlzMi4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgICAgICAgICAgICAvLyDku6XmnI3liqHnmoTmlrnlvI/osIPnlKjnmoQgTG9hZGluZyDpnIDopoHlvILmraXlhbPpl60KICAgICAgICAgICAgICAgICAgX3RoaXMyLmxvYWRpbmcgPSBfZWxlbWVudFVpLkxvYWRpbmcuc2VydmljZSh7CiAgICAgICAgICAgICAgICAgICAgbG9jazogdHJ1ZSwKICAgICAgICAgICAgICAgICAgICAvL2xvY2vnmoTkv67mlLnnrKYtLem7mOiupOaYr2ZhbHNlCiAgICAgICAgICAgICAgICAgICAgdGV4dDogIua1geeoi+i/m+ihjOS4re+8jOivt+eojeWQjiIsCiAgICAgICAgICAgICAgICAgICAgLy/mmL7npLrlnKjliqDovb3lm77moIfkuIvmlrnnmoTliqDovb3mlofmoYgKICAgICAgICAgICAgICAgICAgICBzcGlubmVyOiAiZWwtaWNvbi1sb2FkaW5nIiwKICAgICAgICAgICAgICAgICAgICAvL+iHquWumuS5ieWKoOi9veWbvuagh+exu+WQjQogICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICJyZ2JhKDAsIDAsIDAsIDAuNykiLAogICAgICAgICAgICAgICAgICAgIC8v6YGu572p5bGC6aKc6ImyCiAgICAgICAgICAgICAgICAgICAgdGFyZ2V0OiBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCIjZGlhbG9nQWN0IikKICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICB9KTsKCiAgICAgICAgICAgICAgICBfY29udGV4dC5wcmV2ID0gNzsKICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAxMDsKICAgICAgICAgICAgICAgIHJldHVybiAoMCwgX3Byb2Nlc3NUYXNrLmNvbXBsZXRlVGFzaykoX3RoaXMyLmRhdGFzKTsKCiAgICAgICAgICAgICAgY2FzZSAxMDoKICAgICAgICAgICAgICAgIF95aWVsZCRjb21wbGV0ZVRhc2sgPSBfY29udGV4dC5zZW50OwogICAgICAgICAgICAgICAgY29kZSA9IF95aWVsZCRjb21wbGV0ZVRhc2suY29kZTsKICAgICAgICAgICAgICAgIGRhdGEgPSBfeWllbGQkY29tcGxldGVUYXNrLmRhdGE7CgogICAgICAgICAgICAgICAgaWYgKGNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgICAgICByZXN1bHREYXRhID0gZGF0YTsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICBpZiAoY29kZSkgewogICAgICAgICAgICAgICAgICBfdGhpczIuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgICAgICAgICAvLyDku6XmnI3liqHnmoTmlrnlvI/osIPnlKjnmoQgTG9hZGluZyDpnIDopoHlvILmraXlhbPpl60KICAgICAgICAgICAgICAgICAgICBfdGhpczIubG9hZGluZy5jbG9zZSgpOwogICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gMjA7CiAgICAgICAgICAgICAgICBicmVhazsKCiAgICAgICAgICAgICAgY2FzZSAxNzoKICAgICAgICAgICAgICAgIF9jb250ZXh0LnByZXYgPSAxNzsKICAgICAgICAgICAgICAgIF9jb250ZXh0LnQwID0gX2NvbnRleHRbImNhdGNoIl0oNyk7CgogICAgICAgICAgICAgICAgX3RoaXMyLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgICAgICAgIC8vIOS7peacjeWKoeeahOaWueW8j+iwg+eUqOeahCBMb2FkaW5nIOmcgOimgeW8guatpeWFs+mXrQogICAgICAgICAgICAgICAgICBfdGhpczIubG9hZGluZy5jbG9zZSgpOwogICAgICAgICAgICAgICAgfSk7CgogICAgICAgICAgICAgIGNhc2UgMjA6CiAgICAgICAgICAgICAgICBpZiAocmVzdWx0RGF0YSkgewogICAgICAgICAgICAgICAgICByZXN1bHREYXRhLnByb2Nlc3NUeXBlID0gX3RoaXMyLmRhdGFzLnByb2Nlc3NUeXBlOwoKICAgICAgICAgICAgICAgICAgX3RoaXMyLiRlbWl0KCJ0b2RvRGF0YSIsIHJlc3VsdERhdGEpOwogICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgIF90aGlzMi50b0Nsb3NlKCk7CgogICAgICAgICAgICAgIGNhc2UgMjI6CiAgICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5zdG9wKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlLCBudWxsLCBbWzcsIDE3XV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICB0b0Nsb3NlOiBmdW5jdGlvbiB0b0Nsb3NlKCkgewogICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLCAnbmV4dFVzZXInLCAiIik7CiAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICdjb21tZW50JywgIiIpOwogICAgICB0aGlzLiRlbWl0KCJ0b0Nsb3NlIiwgImNsb3NlIik7CiAgICB9CiAgfQp9OwpleHBvcnRzLmRlZmF1bHQgPSBfZGVmYXVsdDI7"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAgFA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBACA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,KAAA,EAAA;AACA;;;;;;;;;;;;AAYA,IAAA,WAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAFA,sBAEA;AACA,eAAA;AACA,UAAA,WAAA,EAAA,IADA;AAEA,UAAA,WAAA,EAAA;AAFA,SAAA;AAIA;AAPA,KAbA;AAuBA;AACA,IAAA,MAAA,EAAA;AACA,MAAA,IAAA,EAAA,OADA;AAEA,MAAA,OAAA,EAAA;AAFA,KAxBA;AA4BA;AACA,IAAA,MAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAFA,sBAEA;AACA,eAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAAA;AACA;AAJA;AA7BA,GAFA;AAuCA,EAAA,IAvCA,kBAuCA;AACA,WAAA;AACA,MAAA,QAAA,EAAA,KADA;AAEA,MAAA,IAAA,EAAA,EAFA;AAGA,MAAA,KAAA,EAAA,EAHA;AAIA,MAAA,YAAA,EAAA,EAJA;AAKA,MAAA,OAAA,EAAA,IALA;AAMA,MAAA,YAAA,EAAA;AANA,KAAA;AAQA,GAhDA;AAiDA,EAAA,KAAA,EAAA;AACA,IAAA,WAAA,EAAA;AACA,MAAA,OADA,mBACA,MADA,EACA,MADA,EACA;AAAA;;AACA,aAAA,SAAA,CAAA,YAAA;AACA,UAAA,KAAA,CAAA,KAAA,mCAAA,MAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,KAAA,CAAA,KAAA;AACA,cAAA,IAAA,GAAA,KAAA,CAAA,KAAA,CAAA,SAAA,CAAA,IAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,oBAAA,EAAA,IAAA;AACA,cAAA,aAAA,GAAA,EAAA;AACA,cAAA,MAAA,GAAA,KAAA,CAAA,MAAA,CAAA,OAAA,CAAA,MAAA;;AACA,kBAAA,IAAA;AACA,iBAAA,GAAA;AACA,cAAA,aAAA,GAAA,EAAA,CADA,CAEA;;AACA,cAAA,MAAA,GAAA,EAAA;AACA;;AACA,iBAAA,GAAA;AACA,cAAA,aAAA,GAAA,EAAA;AACA,cAAA,MAAA,GAAA,EAAA;AACA;;AACA,iBAAA,IAAA;AACA,cAAA,aAAA,GAAA,GAAA;AACA,cAAA,MAAA,GAAA,EAAA;AACA;;AACA,iBAAA,GAAA;AACA,cAAA,aAAA,GAAA,EAAA;AACA,cAAA,MAAA,GAAA,KAAA,CAAA,KAAA,CAAA,SAAA,CAAA,IAAA;AACA;;AACA,iBAAA,GAAA;AACA,cAAA,aAAA,GAAA,EAAA,CADA,CAEA;;AACA,cAAA,MAAA,GAAA,EAAA;AACA;AAtBA;;AAwBA,mCAAA;AACA,YAAA,aAAA,EAAA,aADA;AAEA,YAAA,MAAA,EAAA,MAFA;AAGA,YAAA,QAAA,EAAA;AAHA,WAAA,EAIA,IAJA,CAIA,UAAA,GAAA,EAAA;AACA,YAAA,KAAA,CAAA,YAAA,GAAA,GAAA,CAAA,IAAA;AACA,WANA;AAOA,SAtCA;AAuCA,OAzCA;AA0CA,MAAA,IAAA,EAAA,IA1CA,CA2CA;;AA3CA;AADA,GAjDA;AAgGA,EAAA,OAhGA,qBAgGA,CAAA,CAhGA;AAiGA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,UAFA,wBAEA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,SAAA,GAAA,MAAA,CAAA,MAAA,CAAA,IAAA,CADA,CAEA;;AACA,oBAAA,MAAA,CAAA,IAAA,CAAA,QAAA,EAAA;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,QAAA,GAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,QAAA;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,gBAAA,GAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,QAAA;AACA,iBAHA,MAGA;AACA,sBAAA,MAAA,CAAA,WAAA,CAAA,YAAA,EAAA;AACA;AACA,oBAAA,MAAA,CAAA,KAAA,CAAA,QAAA,GAAA,MAAA,CAAA,WAAA,CAAA,YAAA,CAAA,QAAA;AACA,oBAAA,MAAA,CAAA,KAAA,CAAA,gBAAA,GAAA,MAAA,CAAA,WAAA,CAAA,YAAA,CAAA,QAAA;AACA,oBAAA,OAAA,CAAA,GAAA,CACA,UADA,EAEA,MAAA,CAAA,WAAA,CAAA,YAAA,CAAA,QAFA;AAIA,oBAAA,OAAA,CAAA,GAAA,CACA,wBADA,EAEA,MAAA,CAAA,WAAA,CAAA,YAAA,CAAA,QAFA;AAIA,oBAAA,OAAA,CAAA,GAAA,CAAA,wBAAA,EAAA,MAAA,CAAA,WAAA,CAAA,YAAA;AACA,mBAbA,MAaA;AACA,oBAAA,MAAA,CAAA,KAAA,CAAA,QAAA,GAAA,SAAA;AACA,oBAAA,MAAA,CAAA,KAAA,CAAA,gBAAA,GAAA,SAAA;AACA;AACA;;AAxBA,sBA0BA,CAAA,MAAA,CAAA,KAAA,CAAA,QAAA,IACA,MAAA,CAAA,KAAA,CAAA,WAAA,KAAA,UADA,IAEA,MAAA,CAAA,KAAA,CAAA,WA5BA;AAAA;AAAA;AAAA;;AA8BA,gBAAA,MAAA,CAAA,QAAA,CAAA;AACA,kBAAA,IAAA,EAAA,OADA;AAEA,kBAAA,OAAA,EAAA;AAFA,iBAAA;;AA9BA;;AAAA;AAoCA,oBAAA,MAAA,CAAA,IAAA,CAAA,OAAA,EAAA;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,SAAA,CAAA,OAAA,GAAA,MAAA,CAAA,IAAA,CAAA,OAAA;AACA;;AACA,gBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,kBAAA,MAAA,CAAA,OAAA,GAAA,mBAAA,OAAA,CAAA;AACA,oBAAA,IAAA,EAAA,IADA;AACA;AACA,oBAAA,IAAA,EAAA,WAFA;AAEA;AACA,oBAAA,OAAA,EAAA,iBAHA;AAGA;AACA,oBAAA,UAAA,EAAA,oBAJA;AAIA;AACA,oBAAA,MAAA,EAAA,QAAA,CAAA,aAAA,CAAA,YAAA;AALA,mBAAA,CAAA;AAOA,iBATA;;AAvCA;AAAA;AAAA,uBAmDA,+BAAA,MAAA,CAAA,KAAA,CAnDA;;AAAA;AAAA;AAmDA,gBAAA,IAnDA,uBAmDA,IAnDA;AAmDA,gBAAA,IAnDA,uBAmDA,IAnDA;;AAoDA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,UAAA,GAAA,IAAA;AACA;;AACA,oBAAA,IAAA,EAAA;AACA,kBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,oBAAA,MAAA,CAAA,OAAA,CAAA,KAAA;AACA,mBAHA;AAIA;;AA5DA;AAAA;;AAAA;AAAA;AAAA;;AA8DA,gBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,kBAAA,MAAA,CAAA,OAAA,CAAA,KAAA;AACA,iBAHA;;AA9DA;AAmEA,oBAAA,UAAA,EAAA;AACA,kBAAA,UAAA,CAAA,WAAA,GAAA,MAAA,CAAA,KAAA,CAAA,WAAA;;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,UAAA,EAAA,UAAA;AACA;;AACA,gBAAA,MAAA,CAAA,OAAA;;AAvEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAwEA,KA1EA;AA2EA,IAAA,OA3EA,qBA2EA;AACA,WAAA,IAAA,CAAA,KAAA,IAAA,EAAA,UAAA,EAAA,EAAA;AACA,WAAA,IAAA,CAAA,KAAA,IAAA,EAAA,SAAA,EAAA,EAAA;AACA,WAAA,KAAA,CAAA,SAAA,EAAA,OAAA;AACA;AA/EA;AAjGA,C", "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      id=\"dialogAct\"\n      class=\"tree\"\n      :title=\"option.title ? option.title : ''\"\n      :top=\"option.top ? option.top : '10vh'\"\n      v-dialogDrag\n      :visible.sync=\"isShow\"\n      :width=\"option.width ? option.width : '25%'\"\n      :close-on-click-modal=\"false\"\n      @close=\"toClose\"\n      :destroy-on-colse=\"true\"\n      :modal-append-to-body=\"false\"\n    >\n      <slot name=\"Dialog_content\" />\n      <el-form\n        label-width=\"120px\"\n        ref=\"form\"\n        :model=\"form\"\n        v-if=\"datas.defaultFrom ? true : false\"\n      >\n        <div>\n          <el-row>\n            <div v-if=\"datas.processType === 'complete'\">\n              <el-col :span=\"24\" v-if=\"datas.nextUser ? datas.nextUser : true\">\n                <el-form-item prop=\"nextUser\" label=\"审批人:\">\n                  <el-select\n                    v-model=\"form.nextUser\"\n                    placeholder=\"请选择人员\"\n                    style=\"width: 100%\"\n                    value-key=\"userName\"\n                    :disabled=\"disabled\"\n                    clearable\n                    filterable\n                  >\n                    <el-option\n                      v-for=\"item in gzfzrOptions\"\n                      :key=\"item.userName\"\n                      :label=\"item.nickName\"\n                      :value=\"{\n                        userName: item.userName,\n                        nickName: item.nickName\n                      }\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </div>\n            <el-col :span=\"24\">\n              <el-form-item\n                prop=\"comment\"\n                label=\"原因填报：\"\n                v-if=\"\n                  datas.processType === 'rollback' ||\n                    datas.processType === 'gqrollback'\n                \"\n              >\n                <el-input\n                  style=\"width: 100%\"\n                  v-model=\"form.comment\"\n                  type=\"textarea\"\n                  placeholder=\"请输入原因\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n      </el-form>\n      <span v-else>请确定是否提交</span>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"info\" @click=\"toClose\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"todoSubmit('form')\">确定</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { Loading } from \"element-ui\";\nimport { getUsers } from \"@/api/system/usergroup\";\nimport { completeTask } from \"@/api/activiti/processTask\";\nexport default {\n  name: \"index\",\n  props: {\n    /*流程发起必填数据\n        processData:{\n          processDefinitionKey:\"流程定义的key\",//必填\n          taskId:\"任务id，如果任务在代办列表时，会根据代办列表获取taskid，否则需传入业务id来确定task\",\n          businessKey:\"业务id，对应业务的主键\",//taskid和businessKey两者必须有其中一个才可以确定一个task\n          businessType:\"业务类型，用于区分不同的业务\"//必填\n          variables:\"拓展参数\"//流程定义中设置的参数,\n          nextUser:\"如果流程实例中并未配置每一个节点的处理人，则需要用户手动选择每一个节点的处理人\",\n          nextUserInfo:{},//手动传的审核人信息\n          processType:'complete,rollback',\n          defaultFrom:true,false 是否需要默认表单\n        }*/\n    processData: {\n      type: Object,\n      default() {\n        return {\n          defaultFrom: true,\n          processType: \"complete\"\n        };\n      }\n    },\n\n    //显示隐藏\n    isShow: {\n      type: Boolean,\n      default: false\n    },\n    //子组件默认参数\n    option: {\n      type: Object,\n      default() {\n        return { title: \"审批\" };\n      }\n    }\n  },\n\n  data() {\n    return {\n      disabled: false,\n      form: {},\n      datas: {},\n      istodoResult: \"\",\n      loading: null,\n      gzfzrOptions: []\n    };\n  },\n  watch: {\n    processData: {\n      handler(newVal, oldVal) {\n        this.$nextTick(() => {\n          this.datas = { ...newVal };\n          console.log(\"datas\", this.datas);\n          let lczt = this.datas.variables.lczt;\n          console.log(\"activiti.this.lczt\", lczt);\n          let personGroupId = 28;\n          let deptId = this.$store.getters.deptId;\n          switch (lczt) {\n            case \"2\":\n              personGroupId = 28;\n              // deptId = this.$store.getters.deptId;\n              deptId = \"\";\n              break;\n            case \"3\":\n              personGroupId = 29;\n              deptId = \"\";\n              break;\n            case \"12\":\n              personGroupId = 137;\n              deptId = \"\";\n              break;\n            case \"4\":\n              personGroupId = 30;\n              deptId = this.datas.variables.fzbm;\n              break;\n            case \"7\":\n              personGroupId = 32;\n              // deptId = this.datas.variables.sbrdw;\n              deptId = \"\";\n              break;\n          }\n          getUsers({\n            personGroupId: personGroupId,\n            deptId: deptId,\n            deptName: \"\"\n          }).then(res => {\n            this.gzfzrOptions = res.data;\n          });\n        });\n      },\n      deep: true,\n      // immediate: true\n    }\n  },\n  mounted() {},\n  methods: {\n    //流程提交\n    async todoSubmit() {\n      this.datas.routePath = this.$route.path;\n      // debugger;\n      if (this.form.nextUser) {\n        this.datas.nextUser = this.form.nextUser.userName;\n        this.datas.nextUserNickName = this.form.nextUser.nickName;\n      } else {\n        if (this.processData.nextUserInfo) {\n          //手动传的用户信息\n          this.datas.nextUser = this.processData.nextUserInfo.userName;\n          this.datas.nextUserNickName = this.processData.nextUserInfo.nickName;\n          console.log(\n            \"手动传的用户信息\",\n            this.processData.nextUserInfo.userName\n          );\n          console.log(\n            \"datas.nextUserNickName\",\n            this.processData.nextUserInfo.nickName\n          );\n          console.log(\"datas.nextUserNickName\", this.processData.nextUserInfo);\n        } else {\n          this.datas.nextUser = undefined;\n          this.datas.nextUserNickName = undefined;\n        }\n      }\n      if (\n        !this.datas.nextUser &&\n        this.datas.processType === \"complete\" &&\n        this.datas.defaultFrom\n      ) {\n        this.$message({\n          type: \"error\",\n          message: \"请选择人员!\"\n        });\n        return;\n      }\n      if (this.form.comment) {\n        this.datas.variables.comment = this.form.comment;\n      }\n      this.$nextTick(() => {\n        // 以服务的方式调用的 Loading 需要异步关闭\n        this.loading = Loading.service({\n          lock: true, //lock的修改符--默认是false\n          text: \"流程进行中，请稍后\", //显示在加载图标下方的加载文案\n          spinner: \"el-icon-loading\", //自定义加载图标类名\n          background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n          target: document.querySelector(\"#dialogAct\")\n        });\n      });\n      let resultData;\n      try {\n        let { code, data } = await completeTask(this.datas);\n        if (code === \"0000\") {\n          resultData = data;\n        }\n        if (code) {\n          this.$nextTick(() => {\n            // 以服务的方式调用的 Loading 需要异步关闭\n            this.loading.close();\n          });\n        }\n      } catch (e) {\n        this.$nextTick(() => {\n          // 以服务的方式调用的 Loading 需要异步关闭\n          this.loading.close();\n        });\n      }\n      if (resultData) {\n        resultData.processType = this.datas.processType;\n        this.$emit(\"todoData\", resultData);\n      }\n      this.toClose();\n    },\n    toClose() {\n      this.$set(this.form,'nextUser',\"\")\n      this.$set(this.form,'comment',\"\")\n      this.$emit(\"toClose\", \"close\");\n    }\n  }\n};\n</script>\n"], "sourceRoot": "src/components/activiti_ssqx"}]}