{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\Index\\Workbench.vue?vue&type=style&index=0&id=56b534bc&scoped=true&lang=scss&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\Index\\Workbench.vue", "mtime": 1706897320623}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5hYUNsYXNzIHsKICA6aG92ZXIgewogICAgY29sb3I6IGRhcmtncmVlbgogIH0KICBkaXNwbGF5OiBibG9jazsKICAvKiAgcGFkZGluZy10b3A6IDF2aDsKICAgIHBhZGRpbmctYm90dG9tOiAxdmg7Ki8KICAvL21hcmdpbi1sZWZ0OiAydmg7CiAgY29sb3I6ICM2NjY7CiAgLml0ZW17CiAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICB6LWluZGV4OiAxOwogICAgL2RlZXAvIC5lbC1iYWRnZV9fY29udGVudC5pcy1maXhlZHsKICAgICAgLy90b3A6MTJweCAhaW1wb3J0YW50OwogICAgICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgICAgIHJpZ2h0OiAwOwogICAgICB0cmFuc2Zvcm06IGluaGVyaXQ7CiAgICAgIHRvcDogLTQycHg7CiAgICB9CiAgfQoKICAudGl0bGVDbGFzcyB7CiAgICB3b3JkLWJyZWFrOiBicmVhay1hbGw7CiAgICB3aGl0ZS1zcGFjZTogbm93cmFwOwogICAgbWFyZ2luOiAwOwogICAgZGlzcGxheTogYmxvY2s7CiAgICBjb2xvcjogIzY2NjsKICAgIGZvbnQtc2l6ZTogMTRweDsKICAgIG92ZXJmbG93OiBoaWRkZW47CiAgICB3aWR0aDogMjM1cHg7CiAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsKICAgIGZvbnQtZmFtaWx5OiAiTGlnaHRfMCIgIWltcG9ydGFudDsKICB9CgogIC8qICAudGl0bGVDbGFzczpob3ZlciB7CiAgICAgIG1hcmdpbjogMDsKICAgICAgZGlzcGxheTogYmxvY2s7CiAgICAgIGNvbG9yOiByZ2JhKDEyLCAxOTQsIDEzMSwgMC44KTsKICAgICAgISptYXJnaW4tbGVmdDogMTNweCAhaW1wb3J0YW50OyohCiAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgZm9udC1mYW1pbHk6ICJMaWdodF8wIiAhaW1wb3J0YW50OwogICAgfSovCgogIC50aW1lQ2xhc3MgewogICAgbWFyZ2luOiAwOwogICAgZm9udC1zaXplOiAxMnB4OwogICAgY29sb3I6ICNjMWNhZDQ7CiAgICB3aWR0aDogMTYwcHg7CiAgfQp9Ci50eHRUaXRsZSB7CiAgbGlzdC1zdHlsZS10eXBlOiBub25lOwogIG1hcmdpbjogMDsKICBwYWRkaW5nOjA7Cn0KLnRpdGxlQ2xhc3MgewogIHdvcmQtYnJlYWs6IGJyZWFrLWFsbDsKICB3aGl0ZS1zcGFjZTogbm93cmFwOwogIG1hcmdpbjogMDsKICBkaXNwbGF5OiBibG9jazsKICBjb2xvcjogIzY2NjsKICBmb250LXNpemU6IDE0cHg7CiAgZm9udC1mYW1pbHk6ICJMaWdodF8wIiAhaW1wb3J0YW50OwogIHBhZGRpbmctbGVmdDogNDdweDsKfQoKLyogIC50YWJBY3RpdmUgewogICAgZmxvYXQ6IGxlZnQ7CiAhKiAgIG1hcmdpbi1yaWdodDogMjVweDsqIQogICAgY3Vyc29yOiBwb2ludGVyOwogICAgY29sb3I6ICMzNTkwNzY7CiAgfQoKICAubm9BY3RpdmUgewogICAgZmxvYXQ6IGxlZnQ7CiAgICEqIG1hcmdpbi1yaWdodDogMjVweDsqIQogICAgY29sb3I6ICNiMWIxYjE7CiAgICBjdXJzb3I6IHBvaW50ZXI7CiAgfSovCgovKuiuvue9rua7muWKqOadoeagt+W8jyovCi9kZWVwLyAud29ya190YWJsZSAuZWwtdGFibGVfX2JvZHktd3JhcHBlcjo6LXdlYmtpdC1zY3JvbGxiYXIsIC9kZWVwLyAudG9kbyAuZWwtdGFibGU6Oi13ZWJraXQtc2Nyb2xsYmFyIHsKICB3aWR0aDogNHB4Owp9CgovZGVlcC8gLndvcmtfdGFibGUgLmVsLXRhYmxlX19ib2R5LXdyYXBwZXI6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iLCAvZGVlcC8gLnRvZG8gLmVsLXRhYmxlOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7CiAgYm9yZGVyLXJhZGl1czogMTBweDsKICAtd2Via2l0LWJveC1zaGFkb3c6IGluc2V0IDAgMCA1cHggcmdiYSgwLCAwLCAwLCAwLjIpOwogIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC4yKTsKfQoKL2RlZXAvIC53b3JrX3RhYmxlIC5lbC10YWJsZV9fYm9keS13cmFwcGVyOjotd2Via2l0LXNjcm9sbGJhci10cmFjaywgL2RlZXAvIC53b3JrX3RhYmxlIC5lbC10YWItcGFuZTo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sgewogIC13ZWJraXQtYm94LXNoYWRvdzogaW5zZXQgMCAwIDVweCByZ2JhKDAsIDAsIDAsIDAuMik7CiAgYm9yZGVyLXJhZGl1czogMDsKICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuMSk7Cn0KCi9kZWVwLyAud29ya190YWJsZSAuZWwtdGFibGVfX2JvZHkgewogIHdpZHRoOiAxMDAlICFpbXBvcnRhbnQ7Cn0KCi9kZWVwLyAud29ya190YWJsZSAuZWwtdGFibGUtLW1lZGl1bSB0aCwgL2RlZXAvIC53b3JrX3RhYmxlLmVsLXRhYmxlLS1tZWRpdW0gdGQgewogIGNvbG9yOiAjMDAwOwogIHBhZGRpbmc6IDA7Cn0KCi9kZWVwLyAud29ya190YWJsZSAuZWwtdGFibGUtLW1lZGl1bSB0aDpob3ZlciwgL2RlZXAvIC53b3JrX3RhYmxlLmVsLXRhYmxlLS1tZWRpdW0gdGQ6aG92ZXIgewogIC8qICAgIGNvbG9yOiAjMTFiYTZiOyovCiAgLyogIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50OwogICAgb3ZlcmZsb3c6IGluaGVyaXQgIWltcG9ydGFudDsqLwp9Ci9kZWVwLyAud29ya190YWJsZSAuZWwtdGFibGUgdHJ7CiAgY29sb3I6IHJnYmEoMTIsIDE5NCwgMTMxLCAwLjgpOwp9Ci9kZWVwLyAubm90aWNlX2JveCAuY2VsbCB7CiAgd2hpdGUtc3BhY2U6IG5vd3JhcDsKICBoZWlnaHQ6IDQycHg7CiAgbGluZS1oZWlnaHQ6IDQycHg7CiAgcGFkZGluZy1sZWZ0OiAycHg7Cn0KLm5vdGljZXN7CiAgaGVpZ2h0OiAzMzFweDsKfQoubm90aWNlX2JveHsKICB3aWR0aDogMTAwJTtwYWRkaW5nOjAgMTJweDsKICBtYXgtaGVpZ2h0OjI4NXB4OwogIGhlaWdodDogMjg1cHg7Cn0K"}, {"version": 3, "sources": ["Workbench.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8XA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Workbench.vue", "sourceRoot": "src/components/Index", "sourcesContent": ["<template>\n  <div :notSpanNum=\"notSpanNum\" class=\"borderCls1 notices\" :class=\"notDivClass\">\n    <div>\n      <div class=\"txtTitle\">\n        <!-- <span @click=\"handleClick('processTodo')\" :class=\"this.val == 'processTodo'?'tabActive':'noActive'\">待办<el-badge :value=\"processCount\"></el-badge></span> -->\n        <span @click=\"handleClick('noticeTodo')\" :class=\"this.val == 'noticeTodo'?'tabActive':'noActive'\">通知<el-badge :value=\"noticeCount\"></el-badge></span>\n        <span @click=\"handleClick('proclamationTodo')\" :class=\"this.val == 'proclamationTodo'?'tabActive':'noActive'\">公告<el-badge :value=\"proclamationCount\"></el-badge></span>\n        <span @click=\"handleClick('remindTodo')\" :class=\"this.val == 'remindTodo'?'tabActive':'noActive'\">提醒<el-badge :value=\"remindCount\"></el-badge></span>\n        <!--        <el-button style=\"float: right; margin-top: 5px; margin-right: 20px;color:#b1b1b1\" type=\"text\"\n                           @click=\"showHistoryNotice\">More+\n                </el-button>-->\n      </div>\n      <div v-show=\"this.val == 'processTodo'\">\n        <el-table :data=\"datalist\" :show-header=\"status\"  class=\"work_table notice_box\">\n          <el-table-column>\n            <template slot-scope=\"scope\">\n              <a class=\"aaClass\" @click=\"getPage(scope.row)\">\n                <el-badge :value=\"scope.row.isHandle==0?'待办理':'已办理'\" class=\"item\"\n                          :type=\"scope.row.isHandle==0?'danger':'primary'\">\n                  <p class=\"titleClass\">{{ scope.row.itemName }}</p>\n                </el-badge>\n              </a>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"todoTime\" class=\"timeClass\" width=\"160\"></el-table-column>\n        </el-table>\n      </div>\n      <div class=\"work_box\" v-show=\"this.val != 'processTodo'\">\n        <el-table :data=\"datalist2\" :show-header=\"status\" class=\"work_table notice_box\">\n          <el-table-column>\n            <template slot-scope=\"scope\">\n              <a class=\"aaClass\" @click=\"getPage(scope.row)\">\n                <el-badge class=\"item\" :value=\"scope.row.isRead==0?'未读':'已读'\" :type=\"scope.row.isRead==0?'danger':'primary'\">\n                  <p class=\"titleClass\">{{ scope.row.title }}</p>\n                </el-badge>\n              </a>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"publishStartTime\" width=\"180\" class=\"timeClass\"></el-table-column>\n        </el-table>\n      </div>\n    </div>\n\n\n    <!--历史公告弹框-->\n    <el-dialog title=\"历史公告\" :visible.sync=\"historyNotice\" width=\"60%\" v-dialogDrag>\n      <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" label-width=\"120px\">\n        <el-form-item label=\"公告标题：\" prop=\"title\">\n          <el-input v-model=\"queryParams.title\" placeholder=\"请输入标题\" clearable\n                    @keyup.enter.native=\"handleQuery\"/>\n        </el-form-item>\n        <el-form-item label=\"公告内容：\" prop=\"content\">\n          <el-input v-model=\"queryParams.content\" placeholder=\"请输入内容\" clearable\n                    @keyup.enter.native=\"handleQuery\"/>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\n          <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\n        </el-form-item>\n      </el-form>\n      <el-table stripe border :data=\"historyNoticeList\" @selection-change=\"handleSelectionChange\">\n        <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n        <el-table-column label=\"公告标题\" align=\"center\" prop=\"title\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"公告内容\" align=\"center\" prop=\"content\" width=\"150\">\n          <template slot-scope=\"scope\">\n            <el-popover v-if=\"scope.row.content.length>8\" trigger=\"hover\" placement=\"top\" width=\"200\">\n              {{ scope.row.content }}\n              <div slot=\"reference\">\n                {{ scope.row.content.substring(0,8)+'...' }}\n              </div>\n            </el-popover>\n            <span v-else>\n            {{ scope.row.content}}\n          </span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"发布时间\" align=\"center\" prop=\"publishstarttime\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"结束时间\" align=\"center\" prop=\"publishendtime\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"附件\" align=\"center\" prop=\"attachment\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"操作\" align=\"center\" width=\"180\" class-name=\"small-padding fixed-width\">\n          <template slot-scope=\"scope\">\n            <el-button size=\"mini\" type=\"text\" @click=\"downloadFile(scope.row.attachmentid)\">下载附件\n            </el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <pagination v-show=\"hTotal>0\"\n                  :total=\"hTotal\"\n                  :page.sync=\"queryParams.pageNum\"\n                  :limit.sync=\"queryParams.pageSize\"\n                  @pagination=\"getHistoryList\"/>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { list } from '@/api/activiti/DgTodoItem'\nimport { download } from '@/api/tool/file'\nimport { countNoticeNumber, getNoticeList } from '@/api/activiti/DgTodoItem'\n\nexport default {\n  name: 'Workbench',//工作台通知公告\n  props: {\n    notSpanNum: {\n      type: Number,\n      default: 12\n    },\n    notDivClass: ''\n  },\n  data() {\n    return {\n      historyNotice: false,\n      historyNoticeList: [],\n      hTotal: 0,\n      val: 'noticeTodo',\n      todoShow: true,\n      //用于布局动态设置高度\n      activeClass: 1,\n      tableData: [\n        {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022年01月31日'\n        },\n        {\n          date: '您有一个工单需要审批,请及时审批',\n          name: '2022年01月31日'\n        }, {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022年01月31日'\n        }, {\n          date: '您有一个工单需要审批,请及时审批',\n          name: '2022年01月31日'\n        }, {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022年01月31日'\n        },\n        {\n          date: '您有一个工单需要审批,请及时审批',\n          name: '2022年01月31日'\n        },\n        {\n          date: '您有一个工单需要审批,请及时审批',\n          name: '2022年01月31日'\n        },\n        {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022年01月31日'\n        }, {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022年01月31日'\n        }\n      ],\n      tableData1: [\n        {\n          date: '您提交的工单已经审批通过了、点击查看详情',\n          name: '2022-01-31'\n        },\n        {\n          date: '您提交的工单已经审批通过了、点击查看详情',\n          name: '2022-01-31'\n        },\n        {\n          date: '您提交的工单已经审批通过了、点击查看详情',\n          name: '2022-01-31'\n        },\n        {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022-01-31'\n        },\n        {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022-01-31'\n        }, {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022-01-31'\n        }, {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name:'2022-01-31'\n        }, {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022-01-31'\n        }, {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022-01-31'\n        }\n      ],\n      listData: [\n        {\n          date: 'DL/Z 398-2010-电力行业信息化标准体系'\n        }, {\n          date: 'DL/T 417-2006-电力设备局部放电现场测量导则'\n        },\n        {\n          date: 'DL/T 423-2009-绝缘油中含气量测定方法真空压差法 '\n        },\n        {\n          date: 'DL/Z 5334-2006 -电力工程勘测安全技术规程'\n        },\n        {\n          date: 'DL/Z 398-2010-电力行业信息化标准体系'\n        }, {\n          date: 'DL/T 1148-2009-电力电缆线路巡检系统'\n        },\n        {\n          date: 'DL/T 800-2001-电力企业标准编制规则'\n        }, {\n          date: 'DL/T 727-2000-互感器运行检修导则 '\n        },\n        {\n          date: 'DL/T 720-2000-电力系统继电保护柜、屏通用技术条件 '\n        }, {\n          date: 'DL/T 676-1999-带电作业绝缘鞋（靴〕通用技术条件 '\n        },\n        {\n          date: 'DL/T 664-2008-带电设备红外诊断应用规范 '\n        }, {\n          date: 'DL/T 574-2010-变压器分接开关运行维修导则 '\n        }\n      ],\n      status: false,\n      maxTableHeight: '340',//表格最大宽度\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        title: undefined,\n        content: undefined\n      },\n      params: {\n        isHandle: '0',\n        lx: 2\n      },\n      datalist:[],\n      datalist2: [],\n      remindCount: 0,\n      noticeCount: 0,\n      processCount: 0,\n      proclamationCount: 0,\n      tabRefresh: {\n        proclamationTodo: 2,\n        remindTodo: 3,\n        noticeTodo: 1\n      },\n    }\n  },\n\n  created() {\n    // this.getHistoryList()\n    //获取数据\n    this.getData();\n    //获取各个模块提醒数量\n    this.countNumber();\n  },\n  methods: {\n    getPage(item) {\n      let path, query\n      switch (this.params.lx) {\n        case 1:\n          path = '/todo/processTodo'\n          query = {objId: item.objId}\n          break;\n        case 2:\n          path = '/todo/noticeTodo'\n          query = {id: item.id}\n          break;\n        case 3:\n          path = '/todo/proclamationTodo'\n          query = {id: item.id}\n          break;\n        case 4:\n          path = '/todo/remindTodo'\n          query = {id: item.id}\n          break;\n      }\n      this.$router.push({\n        path: path,\n        query: query\n      })\n    },\n    //标签页点击\n    handleClick(tabName) {\n      switch (tabName){\n        case 'processTodo':\n          this.params.lx = 1;\n          break;\n        case 'noticeTodo':\n          this.params.lx = 2;\n          break;\n        case 'proclamationTodo':\n          this.params.lx = 3;\n          break;\n        case 'remindTodo':\n          this.params.lx = 4;\n          break;\n      }\n      this.getData();\n      this.val = tabName;\n    },\n    async countNumber() {\n      let {code, data} = await countNoticeNumber()\n      if (code === '0000') {\n        console.log('data',data);\n        await this.$store.dispatch('app/setNoticeCount', data.total)\n        this.remindCount = data.remindCount;\n        this.noticeCount = data.noticeCount;\n        this.processCount = data.processCount;\n        this.proclamationCount = data.proclamationCount;\n      }\n    },\n    async getData() {\n      switch (this.params.lx) {\n        case 1:\n          let {code, data} = await list({isHandle: '0'})\n          if (code === '0000') {\n            this.datalist = data;\n          }\n          break;\n        case 2:\n          let result = await getNoticeList({type: 1})\n          if (result.code === '0000') {\n            this.datalist2 = result.data;\n          }\n          break;\n        case 3:\n          let result2 = await getNoticeList({type: 0})\n          if (result2.code === '0000') {\n            this.datalist2 = result2.data;\n          }\n          break;\n        case 4:\n          let result3 = await getNoticeList({type: 2})\n          if (result3.code === '0000') {\n            this.datalist2 = result3.data;\n          }\n          break;\n      }\n    },\n    click(val) {\n      this.val = val\n      if (val == 'first' ||val == 'three'  ) {\n        this.todoShow = true\n      } if (val == 'second' || val == 'four' ){\n        this.todoShow = false\n      }\n    },\n    showHistoryNotice() {\n      this.historyNotice = true\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map((item) => item.noticeid)\n    },\n    handleQuery() {\n      this.getHistoryList()\n    },\n    resetQuery() {\n      this.resetForm('queryForm')\n      this.handleQuery()\n    },\n    getHistoryList() {\n      list(this.queryParams).then(response => {\n        this.historyNoticeList = response.data.records\n        this.hTotal = response.data.total\n      })\n    },\n    downloadFile(attachmentId) {\n      if (attachmentId) {\n        download(attachmentId)\n      } else {\n        this.msgWarning('暂无附件！')\n      }\n    }\n  },\n  watch: {\n    notSpanNum(newVal) {\n\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.aaClass {\n  :hover {\n    color: darkgreen\n  }\n  display: block;\n  /*  padding-top: 1vh;\n    padding-bottom: 1vh;*/\n  //margin-left: 2vh;\n  color: #666;\n  .item{\n    position: absolute;\n    z-index: 1;\n    /deep/ .el-badge__content.is-fixed{\n      //top:12px !important;\n      position: relative;\n      right: 0;\n      transform: inherit;\n      top: -42px;\n    }\n  }\n\n  .titleClass {\n    word-break: break-all;\n    white-space: nowrap;\n    margin: 0;\n    display: block;\n    color: #666;\n    font-size: 14px;\n    overflow: hidden;\n    width: 235px;\n    text-overflow: ellipsis;\n    font-family: \"Light_0\" !important;\n  }\n\n  /*  .titleClass:hover {\n      margin: 0;\n      display: block;\n      color: rgba(12, 194, 131, 0.8);\n      !*margin-left: 13px !important;*!\n      font-size: 14px;\n      font-family: \"Light_0\" !important;\n    }*/\n\n  .timeClass {\n    margin: 0;\n    font-size: 12px;\n    color: #c1cad4;\n    width: 160px;\n  }\n}\n.txtTitle {\n  list-style-type: none;\n  margin: 0;\n  padding:0;\n}\n.titleClass {\n  word-break: break-all;\n  white-space: nowrap;\n  margin: 0;\n  display: block;\n  color: #666;\n  font-size: 14px;\n  font-family: \"Light_0\" !important;\n  padding-left: 47px;\n}\n\n/*  .tabActive {\n    float: left;\n !*   margin-right: 25px;*!\n    cursor: pointer;\n    color: #359076;\n  }\n\n  .noActive {\n    float: left;\n   !* margin-right: 25px;*!\n    color: #b1b1b1;\n    cursor: pointer;\n  }*/\n\n/*设置滚动条样式*/\n/deep/ .work_table .el-table__body-wrapper::-webkit-scrollbar, /deep/ .todo .el-table::-webkit-scrollbar {\n  width: 4px;\n}\n\n/deep/ .work_table .el-table__body-wrapper::-webkit-scrollbar-thumb, /deep/ .todo .el-table::-webkit-scrollbar-thumb {\n  border-radius: 10px;\n  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);\n  background: rgba(0, 0, 0, 0.2);\n}\n\n/deep/ .work_table .el-table__body-wrapper::-webkit-scrollbar-track, /deep/ .work_table .el-tab-pane::-webkit-scrollbar-track {\n  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);\n  border-radius: 0;\n  background: rgba(0, 0, 0, 0.1);\n}\n\n/deep/ .work_table .el-table__body {\n  width: 100% !important;\n}\n\n/deep/ .work_table .el-table--medium th, /deep/ .work_table.el-table--medium td {\n  color: #000;\n  padding: 0;\n}\n\n/deep/ .work_table .el-table--medium th:hover, /deep/ .work_table.el-table--medium td:hover {\n  /*    color: #11ba6b;*/\n  /*  background: transparent;\n    overflow: inherit !important;*/\n}\n/deep/ .work_table .el-table tr{\n  color: rgba(12, 194, 131, 0.8);\n}\n/deep/ .notice_box .cell {\n  white-space: nowrap;\n  height: 42px;\n  line-height: 42px;\n  padding-left: 2px;\n}\n.notices{\n  height: 331px;\n}\n.notice_box{\n  width: 100%;padding:0 12px;\n  max-height:285px;\n  height: 285px;\n}\n</style>\n"]}]}