{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\Index\\WorkTicket.vue?vue&type=style&index=0&lang=css&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\Index\\WorkTicket.vue", "mtime": 1755543656534}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouc3BhblR4dHsKICBiYWNrZ3JvdW5kOiAjZmZmOwogIGhlaWdodDogMzVweDsKICBsaW5lLWhlaWdodDogMzVweDsKICBtYXJnaW4tdG9wOiA2cHg7CiAgbWFyZ2luLXJpZ2h0OiA5cHg7CiAgcGFkZGluZy1sZWZ0OiAxMnB4Owp9Ci5zZWxlY3RCdG57CiAgZm9udC1zaXplOiAxMGtW54S25rCU56uZ5Y+Y55S156uZNHB4Owp9Cg=="}, {"version": 3, "sources": ["WorkTicket.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2MA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "WorkTicket.vue", "sourceRoot": "src/components/Index", "sourcesContent": ["<template>\n  <div :workTSpanNum=\"workTSpanNum\" class=\"borderCls\" :class=\"wkoTDivClass\">\n    <div>\n      <div class=\"txtTitle\">\n        <span class=\"txtContent\">工作票</span>\n        <!-- <el-select  v-model=\"value\" class=\"selectBtn\" @change=\"changeYear\">\n          <el-option\n            v-for=\"item in options\"\n            :key=\"item.value\"\n            :label=\"item.label\"\n            :value=\"item.value\">\n          </el-option>\n        </el-select> -->\n        <el-date-picker type=\"year\" value-format=\"yyyy\" v-model=\"value\" class=\"selectBtn\" @change=\"changeYear\"/>\n      </div>\n      <div  ref=\"gdchart\" class=\"tjHeight\">\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nimport { mapState } from 'vuex'\nimport { fontSize} from '@/layout/mixin/publicFun'\nimport {countGzp} from \"@/api/yxgl/gzpgl/gzpgl\";\nimport {getDictTypeData} from '@/api/system/dict/data.js'\nimport { parseTime } from '@/utils/ruoyi'\n\nexport default {\n  name: 'WorkTicket',//工单完成情况\n  props:{\n    workTSpanNum:{\n      type:Number,\n      default:7,\n    },\n    wkoTDivClass:'',\n  },\n  data() {\n    return {\n      //用于布局动态设置高度\n      activeClass:1,\n      tjCharts:null,//统计图对象\n      //默认值\n      noFinNum:[],\n      finNum:[],\n      contNum:[],\n      value: parseTime(new Date(),'{y}'),\n      options: [],\n    }\n  },\n  mounted() {\n   this.getData(this.value)\n   window.addEventListener('resize',()=>{\n      this.reloadCharts();\n    })\n    // this.getYears();//获取年度下拉框\n  },\n  methods: {\n    getYears(){\n      getDictTypeData('shouye_nd').then(res=>{\n        res.data.forEach(item=>{\n          this.options.push({label:item.label,value:item.numvalue})\n        })\n      })\n    },\n    changeYear(val){\n      if(!val){\n        return false;\n      }\n      this.getData(val)\n    },\n    getData(year){\n      countGzp(year).then(res => {\n        this.noFinNum = res.data[2];\n        this.finNum = res.data[1];\n        this.contNum = res.data[0];\n        this.showGdCharts();\n      })\n    },\n    //工单完成情况\n    showGdCharts(){\n      let bar_dv = this.$refs.gdchart;\n      let myChart = echarts.init(bar_dv);\n      this.tjCharts = myChart;\n\n      let option;\n      option = {\n        title: {\n          subtext: '单位：个',\n          left: '4%'\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'cross',\n            crossStyle: {\n              color: '#999'\n            }\n          }\n        },\n        legend: {\n          itemWidth: fontSize(14),// 设置图例图形的宽\n          itemHeight: fontSize(14),\n          top:'3%',\n          right: '6%',\n          textStyle:{\n            fontSize:fontSize(16),\n          }\n        },\n        grid: {\n          left: '4%',\n          right: '4%',\n          bottom: '1%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'category',\n          data:['变电一种', '变电二种', '线路一种', '线路二种', '电缆一种', '电缆二种', '配电一种','配电二种','新能源一种','新能源二种'],\n          axisLabel:{interval: 0,rotate:30}\n        },\n        yAxis: {},\n        series: [\n          {\n            type: 'bar' ,\n            stack: 'Ad',\n            name:'完成数',\n            barWidth:fontSize(24),\n            itemStyle: {\n              color: new echarts.graphic.LinearGradient(\n                0, 0, 0, 1,\n                [\n                  {offset: 0, color: '#00D6B1'},\n                  {offset: 1, color: '#00D6B1'}\n                ]\n              )\n            },\n            data:this.finNum,\n          },\n          {\n            type: 'bar',\n            stack: 'Ad',\n            name:'执行数',\n            barWidth:fontSize(24),\n            itemStyle: {\n              color: new echarts.graphic.LinearGradient(\n                0, 0, 0, 1,\n                [\n                  {offset: 0, color: '#76eed2'},\n                  {offset: 1, color: '#76eed2'}\n                ]\n              )\n            },\n            data:this.noFinNum\n          },\n          {\n            type: 'line' ,\n            name:'开票总数',\n            barWidth:fontSize(24),\n            itemStyle: {\n               normal: {label : {show: true},color: '#00C994',}\n            },\n            data:this.contNum,\n          },\n        ]\n      };\n      option && myChart.setOption(option);\n    },\n    //重新加载eCharts图表\n    reloadCharts(){\n      this.tjCharts.resize();\n    },\n  },\n  computed: {\n    ...mapState([\"settings\",\"app\"]),\n    //工作票完成情况\n    workOrder() {\n      return this.$store.state.settings.workOrder;\n    },\n    //菜单伸缩状态\n    opened() {\n      return this.$store.state.app.sidebar.opened;\n    },\n  },\n  watch:{\n    workOrder(newVal){\n      if(newVal){\n        this.reloadCharts();\n      }\n    },\n    wkoSpanNum(newVal){\n      this.reloadCharts();\n    },\n    opened(newVal) {\n      //重新加载统计图\n      /*setTimeout(()=>{\n        this.tjCharts.resize();\n      },200)*/\n    }\n  }\n}\n</script>\n<style>\n.spanTxt{\n  background: #fff;\n  height: 35px;\n  line-height: 35px;\n  margin-top: 6px;\n  margin-right: 9px;\n  padding-left: 12px;\n}\n.selectBtn{\n  font-size: 10kV然气站变电站4px;\n}\n</style>\n\n"]}]}