{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\activiti_gzrwd\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\activiti_gzrwd\\index.vue", "mtime": 1720689651884}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/activiti_gzrwd", "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      id=\"dialogAct\"\n      class=\"tree\"\n      :title=\"option.title ? option.title : ''\"\n      :top=\"option.top ? option.top : '10vh'\"\n      v-dialogDrag\n      :visible.sync=\"isShow\"\n      :width=\"option.width ? option.width : '25%'\"\n      :close-on-click-modal=\"false\"\n      @close=\"toClose\"\n      :destroy-on-colse=\"true\"\n      :modal-append-to-body=\"false\"\n    >\n      <slot name=\"Dialog_content\" />\n      <el-form\n        label-width=\"120px\"\n        ref=\"form\"\n        :model=\"form\"\n        v-if=\"datas.defaultFrom ? true : false\"\n      >\n        <div>\n          <el-row>\n            <!--    根据人员组选人   -->\n            <div v-if=\"datas.processType === 'completeByGroup'\">\n              <el-col :span=\"24\" v-if=\"datas.nextUser ? datas.nextUser : true\">\n                <el-form-item prop=\"nextUser\" label=\"处理人选择：\">\n                  <el-select\n                    v-model=\"form.nextUser\"\n                    placeholder=\"请选择人员\"\n                    filterable\n                    style=\"width: 100%;\"\n                    value-key=\"userName\"\n                  >\n                    <el-option\n                      v-for=\"item in ryOptions\"\n                      :key=\"item.userName\"\n                      :label=\"item.nickName\"\n                      :value=\"{\n                        userName: item.userName,\n                        nickName: item.nickName\n                      }\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </div>\n            <!--    根据人员组选人(多选)   -->\n            <div v-if=\"datas.processType === 'completeMany'\">\n              <el-col :span=\"24\">\n                <el-form-item prop=\"nextUser\" label=\"验收人选择：\">\n                  <el-select\n                    v-model=\"form.nextUser\"\n                    placeholder=\"请选择验收人\"\n                    style=\"width: 100%;\"\n                    value-key=\"userName\"\n                    multiple\n                    clearable\n                    filterable\n                  >\n                    <el-option\n                      v-for=\"item in ryOptions\"\n                      :key=\"item.userName\"\n                      :label=\"item.nickName\"\n                      :value=\"{\n                        userName: item.userName,\n                        nickName: item.nickName\n                      }\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </div>\n            <el-col :span=\"24\">\n              <el-form-item\n                prop=\"comment\"\n                label=\"原因：\"\n                v-if=\"datas.processType === 'rollback'\"\n              >\n                <el-input\n                  style=\"width: 100%\"\n                  v-model=\"form.comment\"\n                  type=\"textarea\"\n                  placeholder=\"请输入原因\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n      </el-form>\n      <span v-else>请确定是否提交</span>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"info\" @click=\"toClose\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"todoSubmit('form')\">确定</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { completeGzrwdTask } from \"@/api/activiti/processTask\";\nimport { Loading } from \"element-ui\";\nimport { getUsers } from \"@/api/system/usergroup\";\nexport default {\n  name: \"index\",\n  props: {\n    /*流程发起必填数据\n        processData:{\n          processDefinitionKey:\"流程定义的key\",//必填\n          taskId:\"任务id，如果任务在代办列表时，会根据代办列表获取taskid，否则需传入业务id来确定task\",\n          businessKey:\"业务id，对应业务的主键\",//taskid和businessKey两者必须有其中一个才可以确定一个task\n          businessType:\"业务类型，用于区分不同的业务\"//必填\n          variables:\"拓展参数\"//流程定义中设置的参数,\n          nextUser:\"如果流程实例中并未配置每一个节点的处理人，则需要用户手动选择每一个节点的处理人\",\n          nextUserInfo:{},//手动传的审核人信息\n          processType:'complete,rollback',\n          defaultFrom:true,false 是否需要默认表单\n        }*/\n    processData: {\n      type: Object,\n      default() {\n        return {\n          defaultFrom: true,\n          processType: \"complete\"\n        };\n      }\n    },\n    //显示隐藏\n    isShow: {\n      type: Boolean,\n      default: false\n    },\n    //子组件默认参数\n    option: {\n      type: Object,\n      default() {\n        return { title: \"审批\" };\n      }\n    },\n    //是否执行提交\n    isCommit: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      disabled: false,\n      form: {},\n      datas: {},\n      loading: null,\n      ryOptions: []\n    };\n  },\n  watch: {\n    processData: {\n      handler(newVal, oldVal) {\n        this.$nextTick(() => {\n          this.datas = { ...newVal };\n          //如果是需要从人员组中选人的，则从人员组查人\n          if (this.datas.processType === \"completeByGroup\") {\n            getUsers({\n              personGroupId: this.datas.variables.personGroupId,\n              deptId: this.datas.variables.deptId\n                ? this.datas.variables.deptId\n                : 0,\n              deptName: this.datas.variables.deptName\n                ? this.datas.variables.deptName\n                : \"\"\n            }).then(res => {\n              this.ryOptions = res.data;\n            });\n          } else if (this.datas.processType === \"completeMany\") {\n            //一发多收\n            getUsers({\n              personGroupId: this.datas.variables.personGroupId,\n              deptId: this.datas.variables.deptId\n                ? this.datas.variables.deptId\n                : 0,\n              deptName: \"\"\n            }).then(res => {\n              this.ryOptions = res.data;\n            });\n          }\n        });\n      },\n      deep: true,\n      immediate: true\n    },\n    isCommit: {\n      handler(newVal, oldVal) {\n        this.$nextTick(() => {\n          if (newVal) {\n            this.forceSubmit(\"form\");\n          }\n        });\n      }\n    }\n  },\n  mounted() {},\n  methods: {\n    //强制提交,不需要弹框\n    async forceSubmit() {\n      this.datas.routePath = this.$route.path;\n      if (this.form.nextUser) {\n        this.datas.nextUser = this.form.nextUser.userName;\n        this.datas.nextUserNickName = this.form.nextUser.nickName;\n      } else {\n        if (this.processData.nextUserInfo) {\n          //手动传的用户信息\n          this.datas.nextUser = this.processData.nextUserInfo.userName;\n          this.datas.nextUserNickName = this.processData.nextUserInfo.nickName;\n        } else {\n          this.datas.nextUser = undefined;\n          this.datas.nextUserNickName = undefined;\n        }\n      }\n      if (\n        !this.datas.nextUser &&\n        (this.datas.processType === \"completeByGroup\" ||\n          this.datas.processType === \"completeMany\") &&\n        this.datas.defaultFrom\n      ) {\n        this.$message({\n          type: \"error\",\n          message: \"请选择人员!\"\n        });\n        return;\n      }\n      if (this.form.comment) {\n        this.datas.variables.comment = this.form.comment;\n      }\n      this.$nextTick(() => {\n        // 以服务的方式调用的 Loading 需要异步关闭\n        this.loading = Loading.service({\n          lock: true, //lock的修改符--默认是false\n          text: \"流程进行中，请稍后\", //显示在加载图标下方的加载文案\n          spinner: \"el-icon-loading\", //自定义加载图标类名\n          background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n          fullscreen: true\n        });\n      });\n      let resultData;\n      try {\n        let { code, data } = await completeGzrwdTask(this.datas);\n        if (code === \"0000\") {\n          resultData = data;\n        }\n        if (code) {\n          this.$nextTick(() => {\n            // 以服务的方式调用的 Loading 需要异步关闭\n            this.loading.close();\n          });\n        }\n      } catch (e) {\n        this.$nextTick(() => {\n          // 以服务的方式调用的 Loading 需要异步关闭\n          this.loading.close();\n        });\n      }\n      if (resultData) {\n        resultData.processType = this.datas.processType;\n        this.$emit(\"todoData\", resultData);\n      }\n      this.toClose();\n    },\n    //流程提交，流程拒绝\n    async todoSubmit() {\n      this.datas.routePath = this.$route.path;\n      if (this.form.nextUser) {\n        //处理人多选\n        if (this.form.nextUser.length > 0) {\n          let nextUser = \"\";\n          this.form.nextUser.forEach(e => {\n            nextUser += e.userName + \",\";\n          });\n          //去掉最后一个逗号\n          if (nextUser.length > 0) {\n            nextUser = nextUser.substr(0, nextUser.length - 1);\n          }\n          this.datas.nextUser = nextUser;\n        } else {\n          this.datas.nextUser = this.form.nextUser.userName;\n          this.datas.nextUserNickName = this.form.nextUser.nickName;\n        }\n      } else {\n        if (this.processData.nextUserInfo) {\n          //手动传的用户信息\n          this.datas.nextUser = this.processData.nextUserInfo.userName;\n          this.datas.nextUserNickName = this.processData.nextUserInfo.nickName;\n        }\n      }\n      if (this.form.comment) {\n        this.datas.variables.comment = this.form.comment;\n      }\n      this.$nextTick(() => {\n        // 以服务的方式调用的 Loading 需要异步关闭\n        this.loading = Loading.service({\n          lock: true, //lock的修改符--默认是false\n          text: \"流程进行中，请稍后\", //显示在加载图标下方的加载文案\n          spinner: \"el-icon-loading\", //自定义加载图标类名\n          background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n          target: document.querySelector(\"#dialogAct\")\n        });\n      });\n      let resultData;\n      try {\n        let { code, data } = await completeGzrwdTask(this.datas);\n        if (code === \"0000\") {\n          resultData = data;\n        }\n        if (code) {\n          this.$nextTick(() => {\n            // 以服务的方式调用的 Loading 需要异步关闭\n            this.loading.close();\n          });\n        }\n      } catch (e) {\n        this.$nextTick(() => {\n          // 以服务的方式调用的 Loading 需要异步关闭\n          this.loading.close();\n        });\n      }\n      if (resultData) {\n        resultData.processType = this.datas.processType;\n        this.$emit(\"todoData\", resultData);\n      }\n      this.toClose();\n    },\n    toClose() {\n      this.$set(this.form, \"nextUser\", \"\");\n      this.$set(this.form, \"comment\", \"\");\n      this.$emit(\"toClose\", \"close\");\n    }\n  }\n};\n</script>\n\n<style scoped></style>\n"]}]}