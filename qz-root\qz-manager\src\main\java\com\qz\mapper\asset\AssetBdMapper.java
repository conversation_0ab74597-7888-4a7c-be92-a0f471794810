package com.qz.mapper.asset;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qz.entity.asset.AssetBd;
import com.qz.entity.asset.LocationsBdz;
import com.qz.entity.asset.LocationsJg;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 变电设备 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-25
 */
public interface AssetBdMapper extends BaseMapper<AssetBd> {
    /**
     * 分页查询变电设备信息
     *
     * @param assetBd
     * @param page
     * @return
     */
    Page getBdAsesetListPage(@Param("assetBd") AssetBd assetBd, Page page);

    List<Map<String, String>> getBdAssetByCode(@Param("assetBd") Map<String,Object> assetBd);


    /**
     * 获取变电站下拉框数据列表
     *
     * @param locationsBdz
     * @return
     */
    List<Map<String, Object>> getBdzDataListSelected(@Param("locationsBdz") LocationsBdz locationsBdz);

    /**
     * 获取变电站下拉框数据列表(地图接口)
     *
     * @return
     */
    List<Map<String, Object>> getMapBdzList();


    /**
     * 获取间隔下拉框数据列表
     *
     * @param locationsJg
     * @return
     */
    List<Map<String, Object>> getJgDataListSelected(@Param("locationsJg") LocationsJg locationsJg);


    /**
     * 获取设备类型下拉框数据
     *
     * @param paramMap
     * @return
     */
    List<Map<String, Object>> getSblxDataListSelected(@Param("param") Map<String, Object> paramMap);


    List<Map<String, Object>> getBdzDataList(@Param("bdzmc") String bdzmc);

    List<Map<String,Object>> getJgData(@Param("ssbdz") String ssbdz);

    //获取变电站和变压器数量
    String getBdzTj();
    String getByqTj();

    //获取6kv线路信息
    List<Map<String,Object>> get6kvXlTj();

    //获取配电站和变压器数量
    String getPdzTj();
    String getPdByqTj();

    //获取配电柜数量
    String getPdgTj();

    List<Map<String,Object>>getDefectStatistics();

    List<AssetBd> selectSbXQ( @Param("param") Map<String, Object> param);

    String getGfdzTj();

}
