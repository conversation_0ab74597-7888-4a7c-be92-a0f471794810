{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\todoitem\\todo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\todoitem\\todo.vue", "mtime": 1706897322087}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["todo.vue"], "names": [], "mappings": ";;;;;;;;;;;AAiMA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;AACA,EAAA,IAAA,EAAA,UADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,eAAA,EAAA;AAAA,GAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA,MAAA,QAAA,EAAA,KADA;AAEA,MAAA,UAAA,EAAA,IAFA;AAGA;AACA,MAAA,OAAA,EAAA,IAJA;AAKA;AACA,MAAA,GAAA,EAAA,EANA;AAOA,MAAA,KAAA,EAAA,IAPA;AAQA,MAAA,MAAA,EAAA,EARA;AASA;AACA,MAAA,MAAA,EAAA,IAVA;AAWA;AACA,MAAA,QAAA,EAAA,IAZA;AAaA;AACA,MAAA,UAAA,EAAA,IAdA;AAeA,MAAA,SAAA,EAAA,EAfA;AAgBA,MAAA,UAAA,EAAA,EAhBA;AAiBA;AACA,MAAA,KAAA,EAAA,CAlBA;AAmBA;AACA,MAAA,QAAA,EAAA,IApBA;AAqBA;AACA,MAAA,IAAA,EAAA,KAtBA;AAuBA,MAAA,WAAA,EAAA,KAvBA;AAwBA,MAAA,cAAA,EAAA,KAxBA;AAyBA;AACA;AACA,MAAA,IAAA,EAAA;AACA,QAAA,OAAA,EAAA;AADA,OA3BA;AA8BA,MAAA,QAAA,EAAA,EA9BA;AA+BA,MAAA,UAAA,EAAA,EA/BA;AAgCA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,EAAA,EAAA,SADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,QAAA,EAAA,EAHA;AAIA,QAAA,QAAA,EAAA,SAJA;AAKA,QAAA,MAAA,EAAA,SALA;AAMA,QAAA,QAAA,EAAA,SANA;AAOA,QAAA,YAAA,EAAA,SAPA;AAQA,QAAA,cAAA,EAAA;AARA,OAjCA;AA2CA;AACA,MAAA,KAAA,EAAA;AACA,wBAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,QAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAJA;AA5CA,KAAA;AAqDA,GAzDA;AA0DA,EAAA,OA1DA,qBA0DA;AACA,SAAA,OAAA;AACA,SAAA,WAAA,CAAA,EAAA,GAAA,KAAA,MAAA,CAAA,MAAA,CAAA,EAAA;AACA,GA7DA;AA8DA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,OAFA,qBAEA;AAAA;;AACA,WAAA,OAAA,GAAA,IAAA;AACA,8BAAA,KAAA,WAAA,EAAA,IAAA,CACA,UAAA,QAAA,EAAA;AACA,QAAA,MAAA,CAAA,QAAA,GAAA,QAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,MAAA,CAAA,KAAA,GAAA,QAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA,OALA;AAOA,KAXA;;AAYA;;;AAGA,IAAA,MAfA,oBAeA;AACA,WAAA,IAAA,GAAA,KAAA;AACA,WAAA,KAAA;AACA,KAlBA;;AAmBA;;;AAGA,IAAA,KAtBA,mBAsBA;AACA,WAAA,IAAA,GAAA;AACA,QAAA,UAAA,EAAA,SADA;AAEA,QAAA,SAAA,EAAA,SAFA;AAGA,QAAA,SAAA,EAAA,SAHA;AAIA,QAAA,KAAA,EAAA,SAJA;AAKA,QAAA,MAAA,EAAA,SALA;AAMA,QAAA,YAAA,EAAA,SANA;AAOA,QAAA,OAAA,EAAA;AACA,UAAA,IAAA,EAAA,IADA;AAEA,UAAA,WAAA,EAAA;AAFA;AAPA,OAAA;AAYA,WAAA,SAAA,CAAA,MAAA;AACA,KApCA;AAqCA,IAAA,aArCA,2BAqCA;AACA,WAAA,QAAA,GAAA;AACA,QAAA,UAAA,EAAA,SADA;AAEA,QAAA,SAAA,EAAA,SAFA;AAGA,QAAA,SAAA,EAAA,SAHA;AAIA,QAAA,KAAA,EAAA,SAJA;AAKA,QAAA,MAAA,EAAA,SALA;AAMA,QAAA,IAAA,EAAA;AANA,OAAA;AAQA,WAAA,SAAA,CAAA,UAAA;AACA,KA/CA;;AAgDA;AACA,IAAA,WAjDA,yBAiDA;AACA,WAAA,WAAA,CAAA,OAAA,GAAA,CAAA;AACA,WAAA,OAAA;AACA,KApDA;;AAqDA;AACA,IAAA,UAtDA,wBAsDA;AACA,WAAA,SAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,WAAA;AACA,WAAA,WAAA;AACA,KA1DA;;AA2DA;AACA,IAAA,qBA5DA,iCA4DA,SA5DA,EA4DA;AACA,WAAA,UAAA,GAAA,SAAA;AACA,WAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,MAAA;AAAA,OAAA,CAAA;AACA,WAAA,MAAA,GAAA,SAAA,CAAA,MAAA,IAAA,CAAA;AACA,WAAA,QAAA,GAAA,CAAA,SAAA,CAAA,MAAA;AACA,KAjEA;;AAkEA;AACA,IAAA,cAnEA,0BAmEA,GAnEA,EAmEA;AACA,WAAA,KAAA;AACA,WAAA,IAAA,GAAA,IAAA;AACA,UAAA,GAAA,GAAA,KAAA,GAAA;;AACA,UAAA,GAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,UAAA,CAAA,SAAA;AACA,eAAA,KAAA;AACA;;AACA,UAAA,SAAA,GAAA,KAAA,UAAA,CAAA,CAAA,CAAA;AACA,UAAA,KAAA,GAAA;AAAA,QAAA,MAAA,EAAA,SAAA,CAAA,MAAA;AAAA,QAAA,MAAA,EAAA,SAAA,CAAA;AAAA,OAAA;;AACA,UAAA,KAAA,GAAA,IAAA;;AACA,qCAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,YAAA,GAAA,GAAA,CAAA,IAAA;AACA,QAAA,KAAA,CAAA,IAAA,CAAA,SAAA,GAAA,YAAA,CAAA,SAAA;AACA,QAAA,KAAA,CAAA,IAAA,CAAA,SAAA,GAAA,YAAA,CAAA,SAAA;AACA,QAAA,KAAA,CAAA,IAAA,CAAA,UAAA,GAAA,YAAA,CAAA,UAAA;AACA,QAAA,KAAA,CAAA,IAAA,CAAA,YAAA,GAAA,YAAA,CAAA,YAAA;AACA,QAAA,KAAA,CAAA,IAAA,CAAA,UAAA,GAAA,YAAA,CAAA,UAAA;AACA,QAAA,KAAA,CAAA,IAAA,CAAA,MAAA,GAAA,YAAA,CAAA,MAAA;AACA,QAAA,KAAA,CAAA,IAAA,CAAA,MAAA,GAAA,YAAA,CAAA,MAAA;AACA,QAAA,KAAA,CAAA,IAAA,CAAA,KAAA,GAAA,YAAA,CAAA,KAAA;AACA,QAAA,KAAA,CAAA,IAAA,CAAA,SAAA,GAAA,YAAA,CAAA,SAAA;AACA,QAAA,KAAA,CAAA,IAAA,CAAA,OAAA,CAAA,IAAA,GAAA,IAAA;AACA,OAZA;AAcA,KA5FA;;AA6FA;AACA,IAAA,UAAA,EAAA,sBAAA;AAAA;;AACA,WAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,cAAA,UAAA,GAAA,MAAA,CAAA,UAAA,CAAA,CAAA,CAAA;AACA,kCAAA,MAAA,CAAA,IAAA,EAAA,UAAA,CAAA,MAAA,EAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA,gBAAA,QAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,cAAA,MAAA,CAAA,UAAA,CAAA,MAAA;;AACA,cAAA,MAAA,CAAA,IAAA,GAAA,KAAA;;AACA,cAAA,MAAA,CAAA,OAAA;AACA;AACA,WANA;AAOA;AACA,OAXA;AAYA,KA3GA;;AA4GA;AACA,IAAA,eA7GA,6BA6GA;AAAA;;AACA,WAAA,aAAA;AACA,UAAA,GAAA,GAAA,KAAA,GAAA;;AACA,UAAA,GAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,UAAA,CAAA,SAAA;AACA,eAAA,KAAA;AACA;;AACA,UAAA,SAAA,GAAA,KAAA,UAAA,CAAA,CAAA,CAAA;AACA,UAAA,KAAA,GAAA;AAAA,QAAA,MAAA,EAAA,SAAA,CAAA,MAAA;AAAA,QAAA,MAAA,EAAA,SAAA,CAAA;AAAA,OAAA;AACA,WAAA,QAAA,GAAA,IAAA;AACA,qCAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KA1HA;;AA2HA;AACA,IAAA,aA5HA,yBA4HA,GA5HA,EA4HA;AACA,WAAA,WAAA,GAAA,IAAA;AACA,WAAA,UAAA,GAAA,GAAA,CAAA,UAAA;AACA,KA/HA;;AAgIA;AACA,IAAA,gBAjIA,4BAiIA,GAjIA,EAiIA;AAAA;;AACA,MAAA,MAAA,CAAA,GAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,GAAA,CAAA,GAAA;AACA,SAFA,MAEA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,GAAA,CAAA,GAAA;AACA;AACA,OANA;AAOA,KAzIA;;AA0IA;AACA,IAAA,UA3IA,sBA2IA,GA3IA,EA2IA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,WAAA,MAAA,GAAA,oDAAA,GAAA,CAAA,UAAA,GAAA,KAAA,GAAA,IAAA,IAAA,GAAA,OAAA,EAAA;AACA,KA9IA;;AA+IA;AACA,IAAA,WAhJA,uBAgJA,GAhJA,EAgJA;AACA,aAAA,GAAA,CAAA,MAAA,IAAA,GAAA,GAAA,GAAA,GAAA,GAAA,CAAA,MAAA,GAAA,MAAA,GAAA,GAAA,GAAA;AACA,KAlJA;;AAmJA;AACA,IAAA,UApJA,sBAoJA,GApJA,EAoJA;AACA,aAAA,GAAA,CAAA,MAAA,IAAA,GAAA,GAAA,GAAA,GAAA,GAAA,CAAA,MAAA,GAAA,MAAA,GAAA,GAAA,GAAA;AACA,KAtJA;;AAuJA;AACA,IAAA,aAxJA,2BAwJA;AACA,WAAA,WAAA,GAAA,KAAA;AACA,KA1JA;;AA2JA;AACA,IAAA,aA5JA,2BA4JA;AACA,WAAA,WAAA,GAAA,KAAA;AACA,KA9JA;;AA+JA;AACA,IAAA,gBAhKA,8BAgKA;AACA,WAAA,cAAA,GAAA,KAAA;AACA,KAlKA;AAmKA,IAAA,gBAnKA,8BAmKA;AACA,WAAA,cAAA,GAAA,KAAA;AACA;AArKA;AA9DA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n      <el-form class=\"searchForm1\" :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"100px\">\n        <el-row>\n          <el-col :span=\"6\">\n            <el-form-item label=\"事项标题：\" prop=\"itemName\">\n              <el-input v-model=\"queryParams.itemName\" placeholder=\"请输入事项标题\" clearable\n                        @keyup.enter.native=\"handleQuery\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"6\">\n            <el-form-item label=\"模块名称：\" prop=\"module\">\n              <el-input v-model=\"queryParams.module\" placeholder=\"请输入模块名称\" clearable\n                        @keyup.enter.native=\"handleQuery\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"6\">\n            <el-form-item label=\"任务名称：\" prop=\"taskName\">\n              <el-input v-model=\"queryParams.taskName\" placeholder=\"请输入任务名称\" clearable\n                        @keyup.enter.native=\"handleQuery\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"6\">\n            <el-form-item label=\"待办人：\" prop=\"todoUserName\">\n              <el-input v-model=\"queryParams.todoUserName\" placeholder=\"请输入待办人\" clearable\n                        @keyup.enter.native=\"handleQuery\"/>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"6\">\n            <el-form-item label=\"处理人：\" prop=\"handleUserName\">\n              <el-input v-model=\"queryParams.handleUserName\" placeholder=\"请输入处理人\" clearable c\n                        @keyup.enter.native=\"handleQuery\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"9\">\n            <el-form-item label=\"通知时间:\">\n              <el-date-picker\n                unlink-panels\n                v-model=\"dateRange\"\n                value-format=\"yyyy-MM-dd\"\n                type=\"daterange\"\n                range-separator=\"-\"\n                start-placeholder=\"开始日期\"\n                end-placeholder=\"结束日期\"\n              ></el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"9\">\n            <el-form-item label=\"处理时间:\">\n              <el-date-picker\n                unlink-panels\n                v-model=\"dateRange1\"\n                value-format=\"yyyy-MM-dd\"\n                type=\"daterange\"\n                range-separator=\"-\"\n                start-placeholder=\"开始日期\"\n                end-placeholder=\"结束日期\"\n              ></el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item>\n          <el-button type=\"cyan\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\n          <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\" type=\"warning\">重置</el-button>\n        </el-form-item>\n      </el-form>\n    <el-row :gutter=\"20\">\n      <el-col :span=\"24\">\n        <el-white class=\"button-group\">\n          <el-row :gutter=\"10\" class=\"mb8 pull-right\">\n            <el-col :span=\"1.5\">\n              <el-button type=\"primary\" :disabled=\"single\"   @click=\"handleComplete()\" >办理</el-button>\n              <!-- <el-button  type=\"primary\" :disabled=\"single\" @click=\"handleDelegation()\" >委托</el-button>-->\n              <el-button  type=\"primary\" :disabled=\"single\"  @click=\"handleApplyInfo()\"  >申请详情</el-button>\n            </el-col>\n          </el-row>\n          <el-table style=\"margin-top: 15px;\" stripe border v-loading=\"loading\" :data=\"todoList\" @selection-change=\"handleSelectionChange\">\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n            <el-table-column label=\"事项标题\" align=\"center\" prop=\"itemName\" width=\"160px\" fixed >\n              <template slot-scope=\"scope\">\n                <router-link   :to=\"{name:'报销管理' ,params:{appId:scope.row.businessId}}\"  class=\"link-type\" v-if=\"scope.row.module=='repayment'\">\n                  <span>{{ scope.row.itemName }}</span>\n                </router-link>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"申请人\" align=\"center\" prop=\"applyUserName\"     />\n            <el-table-column label=\"待办人\" align=\"center\" prop=\"todoUserName\"     />\n            <el-table-column label=\"处理人\" align=\"center\" prop=\"handleUserName\"    />\n            <el-table-column label=\"创建时间\" align=\"center\" prop=\"todoTime\" width=\"160px\"  />\n            <el-table-column label=\"处理时间\" align=\"center\" prop=\"handleTime\" width=\"160px\"  />\n            <el-table-column label=\"待办任务名称\" align=\"center\" prop=\"nodeName\"  width=\"160px\" />\n            <el-table-column label=\"操作\" align=\"center\"   class-name=\"small-padding fixed-width\" width=\"160px\" fixed=\"right\">\n              <template slot-scope=\"scope\">\n                <el-button size=\"mini\" type=\"text\"   @click=\"handleHistory(scope.row)\" >审批历史</el-button>\n                <el-button size=\"mini\" type=\"text\"   @click=\"handlePlan(scope.row)\" >流程查看</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n\n          <pagination v-show=\"total>0\"\n                      :total=\"total\"\n                      :page.sync=\"queryParams.pageNum\"\n                      :limit.sync=\"queryParams.pageSize\"\n                      @pagination=\"getList\"\n          />\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!-- 添加或修改参数配置对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body :close-on-click-modal=false v-dialogDrag>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n            <el-form-item label=\"申请人：\" prop=\"applyName    \">\n              <el-input v-model=\"form.applyUser\" placeholder=\"请输入名称\" maxlength=\"30\"/>\n            </el-form-item>\n            <el-form-item label=\"申请时间\">\n            <el-date-picker\n              v-model=\"form.applyTime\"\n              type=\"datetime\"\n              format=\"yyyy-MM-dd HH:mm:ss\"\n              value-format=\"yyyy-MM-dd HH:mm:ss\"\n              placeholder=\"选择日期时间\">\n            </el-date-picker>\n          </el-form-item>\n            <el-form-item label=\"标题：\" prop=\"title\">\n              <el-input v-model=\"form.title\" placeholder=\"请输入名称\" maxlength=\"30\"/>\n            </el-form-item>\n            <el-form-item label=\"内容：\" prop=\"reason\">\n              <el-input v-model=\"form.reason\" placeholder=\"请输入名称\" maxlength=\"30\"/>\n            </el-form-item>\n            <el-form-item label=\"审批意见：\" prop=\"pass\">\n              <el-select v-model=\"form.extData.pass\" placeholder=\"请选择\">\n                <el-option label=\"同意\" :value=\"true\" />\n                <el-option label=\"拒绝\" :value=\"false\" />\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"批注：\" prop=\"description\">\n              <el-input v-model=\"form.extData.description\" type=\"textarea\" placeholder=\"请输入内容\"></el-input>\n            </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog title=\"申请详情\" :visible.sync=\"openInfo\" width=\"600px\" append-to-body :close-on-click-modal=false v-dialogDrag>\n      <el-form ref=\"formInfo\" :model=\"formInfo\"   label-width=\"120px\">\n        <el-form-item label=\"申请人：\" prop=\"applyName    \">\n          <el-input v-model=\"formInfo.applyUser\" placeholder=\"请输入名称\" maxlength=\"30\"/>\n        </el-form-item>\n        <el-form-item label=\"申请时间\">\n          <el-date-picker\n            v-model=\"formInfo.applyTime\"\n            type=\"datetime\"\n            format=\"yyyy-MM-dd HH:mm:ss\"\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\n            placeholder=\"选择日期时间\">\n          </el-date-picker>\n        </el-form-item>\n        <el-form-item label=\"标题：\" prop=\"title\">\n          <el-input v-model=\"formInfo.title\" placeholder=\"请输入名称\" maxlength=\"30\"/>\n        </el-form-item>\n        <el-form-item label=\"内容：\" prop=\"reason\">\n          <el-input v-model=\"formInfo.reason\" placeholder=\"请输入名称\" maxlength=\"30\"/>\n        </el-form-item>\n      </el-form>\n    </el-dialog>\n\n    <el-dialog title=\"审批历史\" :visible.sync=\"openHistory\" width=\"800px\" append-to-body :close-on-click-modal=false v-dialogDrag>\n     <slot v-if=\"instanceId!=null\">\n       <activiti-history :instance-id=\"instanceId\" />\n     </slot>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitHistory\">确 定</el-button>\n        <el-button @click=\"cancelHistory\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog title=\"审批进度\" :visible.sync=\"openLoadingImg\" width=\"800px\" append-to-body :close-on-click-modal=false v-dialogDrag>\n      <img :src=\"imgSrc\"/>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitLoadingImg\">确 定</el-button>\n        <el-button @click=\"cancelLoadingImg\">关 闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  import { listTodo,getBusinessData,complete} from \"@/api/activiti/todoitem\";\n  import ActivitiHistory from \"./history\";\n  export default {\n    name: \"TodoItem\",\n    components: {ActivitiHistory},\n    data() {\n      return {\n        openInfo:false,\n        instanceId:null,\n        // 遮罩层\n        loading: true,\n        // 选中数组\n        ids: [],\n        title:'审批',\n        imgSrc:'',\n        // 非单个禁用\n        single: true,\n        // 非多个禁用\n        multiple: true,\n        // 显示搜索条件\n        showSearch: true,\n        dateRange: [],\n        dateRange1:[],\n        // 总条数\n        total: 0,\n        // 用户表格数据\n        todoList: null,\n        // 是否显示弹出层\n        open: false,\n        openHistory:false,\n        openLoadingImg:false,\n        // 部门名称\n        // 表单参数\n        form: {\n          extData:{}\n        },\n        formInfo:{},\n        selectDate:[],\n        // 查询参数\n        queryParams: {\n          id:undefined,\n          pageNum: 1,\n          pageSize: 10,\n          itemName: undefined,\n          module: undefined,\n          taskName:undefined,\n          todoUserName:undefined,\n          handleUserName:undefined,\n        },\n        // 表单校验\n        rules: {\n          'extData.pass': [\n            {required: true, message: \"审批意见不能为空\", trigger: \"blur\"},\n          ],\n          name: [\n            {required: true, message: \"名称不能为空\", trigger: \"blur\"},\n          ],\n        },\n      };\n    },\n    created() {\n      this.getList();\n      this.queryParams.id = this.$route.params.id;\n    },\n    methods: {\n      /** 查询用户列表 */\n      getList() {\n        this.loading = true;\n        listTodo(this.queryParams).then(\n          (response) => {\n            this.todoList = response.data.records;\n            this.total = response.data.total;\n            this.loading = false;\n          }\n        );\n      },\n     /**\n      * 取消按钮\n      * */\n      cancel() {\n        this.open = false;\n        this.reset();\n      },\n      /**\n       * 表单重置\n       * */\n      reset() {\n        this.form = {\n          businessId:undefined,\n          applyUser : undefined,\n          applyTime:undefined,\n          title:undefined,\n          reason:undefined,\n          businessType:undefined,\n          extData:{\n            pass: true,\n            description:undefined\n          },\n        };\n        this.resetForm(\"form\");\n      },\n      resetFormInfo(){\n        this.formInfo = {\n          businessId:undefined,\n          applyUser : undefined,\n          applyTime:undefined,\n          title:undefined,\n          reason:undefined,\n          type:undefined,\n        };\n        this.resetForm(\"formInfo\");\n      },\n      /** 搜索按钮操作 */\n      handleQuery() {\n        this.queryParams.pageNum = 1;\n        this.getList();\n      },\n      /** 重置按钮操作 */\n      resetQuery() {\n        this.dateRange = [];\n        this.resetForm(\"queryForm\");\n        this.handleQuery();\n      },\n      /***多选框选中数据***/\n      handleSelectionChange(selection) {\n        this.selectDate = selection;\n        this.ids = selection.map((item) => item.userId);\n        this.single = selection.length != 1;\n        this.multiple = !selection.length;\n      },\n      /** 办理按钮操作 */\n      handleComplete(row) {\n        this.reset();\n        this.open = true;\n        let ids = this.ids;\n        if(ids.length!=1){\n          this.msgWarning(\"请选择一条数据\");\n          return false;\n        }\n        let selectRow = this.selectDate[0];\n        let query = {taskId:selectRow.taskId,module:selectRow.module}\n        let _this = this;\n        getBusinessData(query).then(res=>{\n          let responseData = res.data;\n          _this.form.applyTime = responseData.applyTime;\n          _this.form.applyUser = responseData.applyUser;\n          _this.form.businessId = responseData.businessId;\n          _this.form.businessType = responseData.businessType;\n          _this.form.instanceId = responseData.instanceId;\n          _this.form.reason = responseData.reason;\n          _this.form.taskId = responseData.taskId;\n          _this.form.title = responseData.title;\n          _this.form.variables = responseData.variables;\n          _this.form.extData.pass = true;\n        })\n\n      },\n      /** 提交按钮 */\n      submitForm: function () {\n        this.$refs[\"form\"].validate((valid) => {\n          if (valid) {\n            let selectData = this.selectDate[0];\n            complete(this.form,selectData.taskId).then((response) => {\n              if (response.code === '0000') {\n                this.msgSuccess(\"审批成功\");\n                this.open = false;\n                this.getList();\n              }\n            });\n          }\n        });\n      },\n      /** 申请详情按钮操作 */\n      handleApplyInfo() {\n        this.resetFormInfo();\n        let ids = this.ids;\n        if(ids.length!=1){\n          this.msgWarning(\"请选择一条数据\");\n          return false;\n        }\n        let selectRow = this.selectDate[0];\n        let query = {taskId:selectRow.taskId,module:selectRow.module}\n        this.openInfo = true;\n        getBusinessData(query).then(res=>{\n          this.formInfo = res.data;\n        })\n      },\n      /***  审批历史 ***/\n      handleHistory(row){\n          this.openHistory = true;\n          this.instanceId = row.instanceId\n      },\n      /** 委托 **/\n      handleDelegation(row){\n        deploy(row.id).then(res =>{\n          if(res.code=='0000'){\n            this.msgSuccess(res.msg);\n          }else{\n            this.msgError(res.msg);\n          }\n        })\n      },\n      /**** 进度查看  ****/\n      handlePlan(row){\n        this.openLoadingImg = true\n        this.imgSrc =\"/activiti-api/process/read-resource?instanceId=\"+row.instanceId+\"&t=\"+new Date().getTime();\n      },\n      /** 是否查看 **/\n      hasLookOver(row){\n        return row.isView=='0'?'否':row.isView='1'?'是':'-';\n      },\n      /** 是否处理 **/\n      hasHandler(row){\n        return row.isView=='0'?'否':row.isView='1'?'是':'-';\n      },\n      /** 审批历史确定  **/\n      submitHistory(){\n        this.openHistory = false\n      },\n      /**** 关闭审批历史  ****/\n      cancelHistory(){\n        this.openHistory = false\n      },\n      /** 进度查看  **/\n      submitLoadingImg(){\n        this.openLoadingImg = false;\n      },\n      cancelLoadingImg(){\n        this.openLoadingImg = false\n      },\n    },\n  };\n</script>\n"], "sourceRoot": "src/views/activiti/todoitem"}]}