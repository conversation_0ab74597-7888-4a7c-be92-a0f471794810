{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\syzxm.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\syzxm.vue", "mtime": 1706897323692}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgZ2V0UGFnZURhdGFMaXN0LAogIGdldFBhZ2VLeHpEYXRhTGlzdCwKICByZW1vdmUsCiAgcmVtb3ZlS3h6RGF0YSwKICBzYXZlT3JVcGRhdGUsCiAgc2F2ZU9yVXBkYXRlS3h6RGF0YQp9IGZyb20gJ0AvYXBpL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvc3l6eG0nCmltcG9ydCB7IGdldERldmljZUNsYXNzVHJlZU5vZGVCeVBpZCB9IGZyb20gJ0AvYXBpL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvc2JseHdoL3NibHh3aCcKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnbHBiemsnLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBjdXJyZW50VXNlcjogdGhpcy4kc3RvcmUuZ2V0dGVycy5uYW1lLAogICAgICAvL+aWsOWinuaMiemSruaOp+WItgogICAgICBhZGREaXNhYmxlZDogdHJ1ZSwKICAgICAgLy/moJHnu5PmnoTmh5LliqDovb3lj4LmlbAKICAgICAgcHJvcHM6IHsKICAgICAgICBsYWJlbDogJ25hbWUnLAogICAgICAgIGNoaWxkcmVuOiAnem9uZXMnLAogICAgICAgIGlzTGVhZjogKGRhdGEsIG5vZGUpID0+IHsKICAgICAgICAgIGlmIChub2RlLmxldmVsID09PSAyKSB7CiAgICAgICAgICAgIHJldHVybiB0cnVlCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9LAogICAgICAvL+aemuS4vuWAvOaWsOWinuW8ueWHuuahhuW6lemDqOaMiemSruaOp+WItuaYvuekugogICAgICBhZGRNanpEaWFsb2dCdXR0b25TaG93OiB0cnVlLAogICAgICAvL+aOp+WItuaemuS4vuWAvOaWsOWinuW8ueWHuuahhuWGheWuueaYr+WQpuWPr+e8lui+kQogICAgICBtanpBZGREaWFsb2dEaXNhYmxlZDogZmFsc2UsCiAgICAgIC8v5p6a5Li+5YC85paw5aKeZm9ybeihqOWNlQogICAgICBtanpBZGRGb3JtOiB7CiAgICAgICAgc3l6eG1pZDogdW5kZWZpbmVkLAogICAgICAgIGt4ejogdW5kZWZpbmVkCiAgICAgIH0sCiAgICAgIC8v5p6a5Li+5YC85paw5aKe5by55Ye65qGG5qCH6aKYCiAgICAgIG1qekFkZERpYWxvZ1RpdGxlOiAn5paw5aKeJywKICAgICAgLy/mnprkuL7lgLzmlrDlop7lvLnlh7rmoYbmjqfliLYKICAgICAgaXNTaG93TWp6QWRkRGlhbG9nOiBmYWxzZSwKICAgICAgLy/pgInkuK3lrZDpobnnm67ml7bojrflj5bliLDnmoTnrKzkuIDooYzmlbDmja7nlKjmnaXmn6Xor6LmnprkuL7lgLwKICAgICAgbWp6Um93Rm9ybToge30sCiAgICAgIC8v57u05oqk5p6a5Li+5YC8YnV0dG9u5oyJ6ZKuCiAgICAgIHdobWp6QnV0dG9uRGlzYWJsZWQ6IHRydWUsCiAgICAgIC8v5p6a5Li+5YC85pWw5o2uCiAgICAgIG1qekRhdGFMaXN0OiBbXSwKICAgICAgLy/mnprkuL7lgLzlj4LmlbAKICAgICAgbWp6UXVlcnlQYXJhbXM6IHsKICAgICAgICBzeXp4bWlkOiB1bmRlZmluZWQsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHBhZ2VOdW06IDEKICAgICAgfSwKICAgICAgLy/mnprkuL7lgLzmgLvmlbAKICAgICAgTWp6dG90YWw6IDAsCiAgICAgIC8v5p6a5Li+6aG55by55Ye65qGG5qCH6aKYCiAgICAgIG1qeERpYWxvZ1RpdGxlOiAn5p6a5Li+5YC857u05oqkJywKICAgICAgLy/mnprkuL7pobnlvLnlh7rmoYYKICAgICAgaXNTaG93TWp6RGlhbG9nOiBmYWxzZSwKCiAgICAgIC8v5Yig6Zmk6YCJ5oup5YiXCiAgICAgIHNlbGVjdFJvd3M6IFtdLAogICAgICAvL+ihqOWNlemqjOivgQogICAgICBydWxlczogewogICAgICAgIHp4bW1jOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5a2Q6aG555uu5ZCN56ewJywgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIC8v562b6YCJ5qGGCiAgICAgIGZpbHRlckluZm86IHsKICAgICAgICBkYXRhOiB7CiAgICAgICAgICBqZ2x4OiAnJywKICAgICAgICAgIHp4bW1jOiAnJwogICAgICAgIH0sCiAgICAgICAgZmllbGRMaXN0OiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAn57uT5p6c57G75Z6LJywKICAgICAgICAgICAgdmFsdWU6ICdqZ2x4JywKICAgICAgICAgICAgdHlwZTogJ3NlbGVjdCcsCiAgICAgICAgICAgIG9wdGlvbnM6IFsKICAgICAgICAgICAgICB7IGxhYmVsOiAn5Zu+54mHJywgdmFsdWU6ICflm77niYcnIH0sCiAgICAgICAgICAgICAgeyBsYWJlbDogJ+aVsOWtlycsIHZhbHVlOiAn5pWw5a2XJyB9LAogICAgICAgICAgICAgIHsgbGFiZWw6ICfml6XmnJ8nLCB2YWx1ZTogJ+aXpeacnycgfSwKICAgICAgICAgICAgICB7IGxhYmVsOiAn5Y2V6YCJJywgdmFsdWU6ICfljZXpgIknIH0sCiAgICAgICAgICAgICAgeyBsYWJlbDogJ+aemuS4vicsIHZhbHVlOiAn5p6a5Li+JyB9LAogICAgICAgICAgICAgIHsgbGFiZWw6ICflrZfnrKYnLCB2YWx1ZTogJ+Wtl+espicgfQogICAgICAgICAgICBdLAogICAgICAgICAgICBjbGVhcmFibGU6IHRydWUKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAn5a2Q6aG555uu5ZCN56ewJywKICAgICAgICAgICAgdmFsdWU6ICd6eG1tYycsCiAgICAgICAgICAgIHR5cGU6ICdpbnB1dCcsCiAgICAgICAgICAgIGNsZWFyYWJsZTogdHJ1ZQogICAgICAgICAgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgLy/liJfooajpobUKICAgICAgdGFibGVBbmRQYWdlSW5mbzogewogICAgICAgIHBhZ2VyOiB7CiAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgdG90YWw6IDAsCiAgICAgICAgICBzaXplczogWzEwLCAyMCwgNTAsIDEwMF0KICAgICAgICB9LAogICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgdGFibGVIZWFkZXI6IFsKICAgICAgICAgIHsgbGFiZWw6ICflrZDpobnnm67lkI3np7AnLCBwcm9wOiAnenhtbWMnLCBtaW5XaWR0aDogJzE1MCcgfSwKICAgICAgICAgIHsgbGFiZWw6ICfmlbDlgLzljZXkvY0nLCBwcm9wOiAnc3pkdycsIG1pbldpZHRoOiAnNzAnIH0sCiAgICAgICAgICB7IGxhYmVsOiAn57uT5p6c57G75Z6LJywgcHJvcDogJ2pnbHgnLCBtaW5XaWR0aDogJzEwMCcgfSwKICAgICAgICAgIHsgbGFiZWw6ICforqHnrpfliJcnLCBwcm9wOiAnanNsJywgbWluV2lkdGg6ICc2MCcgfSwKICAgICAgICAgIHsgbGFiZWw6ICfmmK/lkKblj6/kuLrnqbonLCBwcm9wOiAnc2Zrd2snLCBtaW5XaWR0aDogJzgwJyB9LAogICAgICAgICAgeyBsYWJlbDogJ+aYr+WQpuaYvuekuicsIHByb3A6ICdzZnhzJywgbWluV2lkdGg6ICc3MCcgfSwKICAgICAgICAgIHsgbGFiZWw6ICflrZDpobnnm67mj4/ov7AnLCBwcm9wOiAnenhtbXMnLCBtaW5XaWR0aDogJzE1MCcgfSwKICAgICAgICAgIHsgbGFiZWw6ICfmlbDmja7nsr7luqYnLCBwcm9wOiAnc2pqZCcsIG1pbldpZHRoOiAnMTAwJyB9CiAgICAgICAgICAvLyB7CiAgICAgICAgICAvLyAgIHByb3A6ICdvcGVyYXRpb24nLAogICAgICAgICAgLy8gICBsYWJlbDogJ+aTjeS9nCcsCiAgICAgICAgICAvLyAgIG1pbldpZHRoOiAnMTAwcHgnLAogICAgICAgICAgLy8gICBzdHlsZToge2Rpc3BsYXk6ICdibG9jayd9LAogICAgICAgICAgLy8gICAvL+aTjeS9nOWIl+WbuuWumuWGjeWPs+S+pwogICAgICAgICAgLy8gICBmaXhlZDogJ3JpZ2h0JywKICAgICAgICAgIC8vICAgb3BlcmF0aW9uOiBbCiAgICAgICAgICAvLyAgICAge25hbWU6ICfkv67mlLknLCBjbGlja0Z1bjogdGhpcy51bmRhdGVEZXRhaWxzfSwKICAgICAgICAgIC8vICAgICB7bmFtZTogJ+ivpuaDhScsIGNsaWNrRnVuOiB0aGlzLmdldERldGFpbHNJbmZvfSwKICAgICAgICAgIC8vICAgXQogICAgICAgICAgLy8gfSwKICAgICAgICBdLAogICAgICAgIG9wdGlvbjogeyBjaGVja0JveDogdHJ1ZSwgc2VyaWFsTnVtYmVyOiB0cnVlIH0KICAgICAgfSwKICAgICAgLy/mn6Xor6Lor5XpqozlrZDpobnnm67lj4LmlbAKICAgICAgcXVlcnlTeXp4bVBhcmFtOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgc2JseGJtOiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgc2JseGJtOiAnJywKICAgICAgLy/nu5PmnpznsbvlnovkuIvmi4nmoYbmlbDmja4KICAgICAgamdseE9wdGlvbnNTZWxlY3RlZExpc3Q6IFsKICAgICAgICB7IGxhYmVsOiAn5Zu+54mHJywgdmFsdWU6ICflm77niYcnIH0sCiAgICAgICAgeyBsYWJlbDogJ+aVsOWtlycsIHZhbHVlOiAn5pWw5a2XJyB9LAogICAgICAgIHsgbGFiZWw6ICfml6XmnJ8nLCB2YWx1ZTogJ+aXpeacnycgfSwKICAgICAgICB7IGxhYmVsOiAn5Y2V6YCJJywgdmFsdWU6ICfljZXpgIknIH0sCiAgICAgICAgeyBsYWJlbDogJ+aemuS4vicsIHZhbHVlOiAn5p6a5Li+JyB9LAogICAgICAgIHsgbGFiZWw6ICflrZfnrKYnLCB2YWx1ZTogJ+Wtl+espicgfQogICAgICBdLAogICAgICAvL+iuoeeul+WIlwogICAgICBqc2xPcHRpb25zU2VsZWN0ZWRMaXN0OiBbCiAgICAgICAgeyBsYWJlbDogJ+aYrycsIHZhbHVlOiAn5pivJyB9LAogICAgICAgIHsgbGFiZWw6ICflkKYnLCB2YWx1ZTogJ+WQpicgfQogICAgICBdLAogICAgICAvL+aYr+WQpuS4uuepugogICAgICBzZmt3a09wdGlvbnNTZWxlY3RlZExpc3Q6IFsKICAgICAgICB7IGxhYmVsOiAn5pivJywgdmFsdWU6ICfmmK8nIH0sCiAgICAgICAgeyBsYWJlbDogJ+WQpicsIHZhbHVlOiAn5ZCmJyB9CiAgICAgIF0sCiAgICAgIC8v5piv5ZCm5pi+56S6CiAgICAgIHNmeHNPcHRpb25zU2VsZWN0ZWRMaXN0OiBbCiAgICAgICAgeyBsYWJlbDogJ+aYrycsIHZhbHVlOiAn5pivJyB9LAogICAgICAgIHsgbGFiZWw6ICflkKYnLCB2YWx1ZTogJ+WQpicgfQogICAgICBdLAoKICAgICAgLy9mb3Jt6KGo5Y2VCiAgICAgIGZvcm06IHt9LAogICAgICAvL+aYr+WQpuaYvuekuuW8ueahhgogICAgICBpc1Nob3dEZXRhaWxzOiBmYWxzZSwKICAgICAgLy/mmK/lkKbnpoHnlKgKICAgICAgaXNEaXNhYmxlZDogZmFsc2UsCiAgICAgIC8v6KGo5Y2V5pWw5o2uCiAgICAgIGRhdGFUYWJsZTogW10sCiAgICAgIC8v5qCH6aKYCiAgICAgIHRpdGxlOiAnJywKCiAgICAgIC8v57uE57uH5qCRCiAgICAgIHRyZWVPcHRpb25zOiBbCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICfmlq3ot6/lmagnCiAgICAgICAgfSwgewogICAgICAgICAgbGFiZWw6ICflj5jljovlmagnLAogICAgICAgICAgY2hpbGRyZW46IFt7CiAgICAgICAgICAgIGxhYmVsOiAn5Ya35Y2057O757ufJywKICAgICAgICAgICAgY2hpbGRyZW46IFt7CiAgICAgICAgICAgICAgbGFiZWw6ICfmuKnmjqfov5DooYzmg4XlhrUnCgogICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgbGFiZWw6ICfmsrnnrrEnCgogICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgbGFiZWw6ICfpk4Hoiq8nCgogICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgbGFiZWw6ICfnu5Xnu4QnCgogICAgICAgICAgICB9XQogICAgICAgICAgfV0KICAgICAgICB9XSwKICAgICAgLy/liKDpmaTmmK/lkKblj6/nlKgKICAgICAgbXVsdGlwbGVTZW5zb3I6IHRydWUsCiAgICAgIC8v5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgYW5jdW9UZXJtOiAnJwogICAgICB9LAogICAgICAvL+ihqOWNleW8gOWFswogICAgICBpc1NlYXJjaFNob3c6IGZhbHNlLAogICAgICAvL+W3peS9nOelqOexu+Wei+S4i+aLieiPnOWNlQogICAgICBnenBUeXBlT3B0aW9uczogWwogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAndHlwZTEnLAogICAgICAgICAgbGFiZWw6ICfnsbvlnosxJwogICAgICAgIH0sIHsKICAgICAgICAgIHZhbHVlOiAndHlwZTInLAogICAgICAgICAgbGFiZWw6ICfnsbvlnosyJwogICAgICAgIH0KICAgICAgXQoKICAgIH0KICB9LAogIHdhdGNoOiB7fSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXREYXRhKCkKCiAgfSwKICBtZXRob2RzOiB7CiAgICAvL+aHkuWKoOi9veWHveaVsAogICAgbG9hZE5vZGUobm9kZSwgcmVzb2x2ZSkgewogICAgICBsZXQgVHJlZXBhcmFtTWFwID0gewogICAgICAgIHBpZDogJycsCiAgICAgICAgc3BiTG9nbzogWyfovpPnlLXorr7lpIcnLCAn5Y+Y55S16K6+5aSHJywn6YWN55S16K6+5aSHJ10KICAgICAgfQogICAgICBpZiAobm9kZS5sZXZlbCA9PT0gMCkgewogICAgICAgIFRyZWVwYXJhbU1hcC5waWQgPSAnc2InCiAgICAgICAgcmV0dXJuIHRoaXMuZ2V0VHJlZU5vZGUoVHJlZXBhcmFtTWFwLCByZXNvbHZlKQogICAgICB9CiAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgIFRyZWVwYXJhbU1hcC5waWQgPSBub2RlLmRhdGEuY29kZQogICAgICAgIHRoaXMuZ2V0VHJlZU5vZGUoVHJlZXBhcmFtTWFwLCByZXNvbHZlKQogICAgICB9LCA1MDApCgogICAgfSwKCiAgICAvL+iOt+WPluagkeiKgueCueaVsOaNrgogICAgZ2V0VHJlZU5vZGUocGFyYW1NYXAsIHJlc29sdmUpIHsKICAgICAgZ2V0RGV2aWNlQ2xhc3NUcmVlTm9kZUJ5UGlkKHBhcmFtTWFwKS50aGVuKHJlcyA9PiB7CiAgICAgICAgbGV0IHRyZWVOb2RlcyA9IFtdCiAgICAgICAgcmVzLmRhdGEuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIGxldCBub2RlID0gewogICAgICAgICAgICBuYW1lOiBpdGVtLm5hbWUsCiAgICAgICAgICAgIGxldmVsOiBpdGVtLmxldmVsLAogICAgICAgICAgICBpZDogaXRlbS5pZCwKICAgICAgICAgICAgcGlkOiBpdGVtLnBpZCwKICAgICAgICAgICAgbGVhZjogZmFsc2UsCiAgICAgICAgICAgIGNvZGU6IGl0ZW0uY29kZQogICAgICAgICAgfQogICAgICAgICAgdHJlZU5vZGVzLnB1c2gobm9kZSkKICAgICAgICB9KQogICAgICAgIHJlc29sdmUodHJlZU5vZGVzKQogICAgICB9KQogICAgfSwKCiAgICAvL+agkeiKgueCueeCueWHu+S6i+S7tgogICAgaGFuZGxlTm9kZUNsaWNrKGRhdGEpIHsKICAgICAgaWYgKGRhdGEubGV2ZWwgPT09ICcxJykgewogICAgICAgIC8v5byA5pS+5paw5aKe5oyJ6ZKuCiAgICAgICAgdGhpcy5hZGREaXNhYmxlZCA9IGZhbHNlCiAgICAgICAgdGhpcy50cmVlRm9ybSA9IGRhdGEKICAgICAgICB0aGlzLnNibHhibSA9IGRhdGEuY29kZQogICAgICAgIHRoaXMucXVlcnlTeXp4bVBhcmFtLnNibHhibSA9IGRhdGEuY29kZQogICAgICAgIHRoaXMuZ2V0RGF0YSgpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5hZGREaXNhYmxlZCA9IHRydWUKICAgICAgfQogICAgfSwKCiAgICAvL+aemuS4vuWAvOaWsOWinuaMiemSrgogICAgYWRkTWp6KCkgewogICAgICB0aGlzLm1qekFkZEZvcm0ua3h6ID0gdW5kZWZpbmVkCiAgICAgIHRoaXMuaXNTaG93TWp6QWRkRGlhbG9nID0gdHJ1ZQogICAgfSwKICAgIC8v5o+Q5Lqk5paw5aKe5p6a5Li+5YC85by55Ye65qGG6KGo5Y2VCiAgICBjb21taXRBZGRNanpGb3JtKCkgewogICAgICB0aGlzLm1qekFkZEZvcm0uc3l6eG1pZCA9IHRoaXMubWp6Um93Rm9ybS5vYmpJZAogICAgICBjb25zb2xlLmxvZyh0aGlzLm1qekFkZEZvcm0pCiAgICAgIHNhdmVPclVwZGF0ZUt4ekRhdGEodGhpcy5tanpBZGRGb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgaWYgKHJlcy5jb2RlID09ICcwMDAwJykgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmk43kvZzmiJDlip/vvIEnKQogICAgICAgICAgdGhpcy5pc1Nob3dNanpBZGREaWFsb2cgPSBmYWxzZQogICAgICAgICAgdGhpcy5nZXRNanpEYXRhTGlzdCgpCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIC8v5Y+W5raI5oyJ6ZKuKOaemuS4vuWAvOaWsOWinuW8ueWHuuahhikKICAgIGNsb3NlQWRkTWp6RGlhbG9nKCkgewogICAgICB0aGlzLmlzU2hvd01qekFkZERpYWxvZyA9IGZhbHNlCiAgICB9LAogICAgLy/ojrflj5bmnprkuL7lgLzliJfooajmlrnms5UKICAgIGdldE1qekRhdGFMaXN0KCkgewogICAgICB0aGlzLm1qelF1ZXJ5UGFyYW1zLnN5enhtaWQgPSB0aGlzLm1qelJvd0Zvcm0ub2JqSWQKICAgICAgZ2V0UGFnZUt4ekRhdGFMaXN0KHRoaXMubWp6UXVlcnlQYXJhbXMpLnRoZW4ocmVzID0+IHsKICAgICAgICBjb25zb2xlLmxvZyhyZXMpCiAgICAgICAgdGhpcy5Nanp0b3RhbCA9IHJlcy5kYXRhLnRvdGFsCiAgICAgICAgdGhpcy5tanpEYXRhTGlzdCA9IHJlcy5kYXRhLnJlY29yZHMKICAgICAgfSkKICAgIH0sCiAgICAvL+aemuS4vuWAvOihjOmAieS4reS6i+S7tgogICAgaGFuZGxlU2VsZWN0aW9uTWp6Q2hhbmdlKHJvd3MpIHsKICAgICAgdGhpcy5zZWxlY3RlZEt4ekRhdGFSb3cgPSByb3dzCgogICAgfSwKICAgIC8v5Yig6Zmk5p6a5Li+5YC85YiX6KGoCiAgICBkZWxldGVNanooKSB7CiAgICAgIGlmICh0aGlzLnNlbGVjdGVkS3h6RGF0YVJvdy5sZW5ndGggPCAxKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fpgInmi6nmraPnoa7nmoTmlbDmja7vvIHvvIHvvIEnKQogICAgICAgIHJldHVybgogICAgICB9CiAgICAgIGxldCBpZHMgPSB0aGlzLnNlbGVjdGVkS3h6RGF0YVJvdy5tYXAoaXRlbSA9PiB7CiAgICAgICAgcmV0dXJuIGl0ZW0ub2JqSWQKICAgICAgfSkKICAgICAgdGhpcy4kY29uZmlybSgn5q2k5pON5L2c5bCG5rC45LmF5Yig6Zmk6K+l5pWw5o2uLCDmmK/lkKbnu6fnu60/JywgJ+aPkOekuicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHJlbW92ZUt4ekRhdGEoaWRzKS50aGVuKCh7IGNvZGUgfSkgPT4gewogICAgICAgICAgaWYgKGNvZGUgPT09ICcwMDAwJykgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOaIkOWKnyEnCiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIHRoaXMuZ2V0TWp6RGF0YUxpc3QoKQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgICAgICAgICBtZXNzYWdlOiAn5Yig6Zmk5aSx6LSlIScKICAgICAgICAgICAgfSkKICAgICAgICAgICAgdGhpcy5nZXRNanpEYXRhTGlzdCgpCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ2luZm8nLAogICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iOWIoOmZpCcKICAgICAgICB9KQogICAgICB9KQogICAgfSwKICAgIC8v57u05oqk5p6a5Li+5YC85oyJ6ZKuCiAgICBhZGRaeG1LeHooKSB7CiAgICAgIGlmICh0aGlzLm1qelJvd0Zvcm0uamdseCAhPSAn5p6a5Li+JykgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup57uT5p6c57G75Z6L5Li65p6a5Li+57G75Z6L55qE5pWw5o2u77yBJykKICAgICAgfSBlbHNlIHsKICAgICAgICAvL+aJk+W8gOW8ueeqlwogICAgICAgIHRoaXMuaXNTaG93TWp6RGlhbG9nID0gdHJ1ZQogICAgICAgIHRoaXMubWp6UXVlcnlQYXJhbXMuc3l6eG1pZCA9IHRoaXMubWp6Um93Rm9ybS5vYmpJZAogICAgICAgIC8v6I635Y+W5p6a5Li+5YC85YiX6KGoCiAgICAgICAgdGhpcy5nZXRNanpEYXRhTGlzdCgpCiAgICAgIH0KICAgIH0sCiAgICAvL+ihjOmAieS4rQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHJvd3MpIHsKICAgICAgdGhpcy5zZWxlY3RSb3dzID0gcm93cwogICAgICB0aGlzLndobWp6QnV0dG9uRGlzYWJsZWQgPSByb3dzLmxlbmd0aCAhPSAxCiAgICAgIC8v6I635Y+W5Yiw5b2T5YmN6KGM5a+56LGhCiAgICAgIHRoaXMubWp6Um93Rm9ybSA9IHJvd3NbMF0KICAgIH0sCiAgICAvL+WIl+ihqOafpeivouWtkOmhueebruWIl+ihqAogICAgYXN5bmMgZ2V0RGF0YShwYXJhbXMpIHsKICAgICAgdHJ5IHsKICAgICAgICB0aGlzLnF1ZXJ5U3l6eG1QYXJhbSA9IHsgLi4udGhpcy5xdWVyeVN5enhtUGFyYW0sIC4uLnBhcmFtcyB9CiAgICAgICAgY29uc3QgcGFyYW0gPSB0aGlzLnF1ZXJ5U3l6eG1QYXJhbQogICAgICAgIGNvbnN0IHsgZGF0YSwgY29kZSB9ID0gYXdhaXQgZ2V0UGFnZURhdGFMaXN0KHBhcmFtKQogICAgICAgIGlmIChjb2RlID09PSAnMDAwMCcpIHsKICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby50YWJsZURhdGEgPSBkYXRhLnJlY29yZHMKICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci50b3RhbCA9IGRhdGEudG90YWwKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICBjb25zb2xlLmxvZyhlKQogICAgICB9CiAgICB9LAogICAgLy/ojrflj5bor6bmg4UKICAgIGdldERldGFpbHNJbmZvKHJvdykgewogICAgICAvL+aJk+W8gOW8ueeqlwogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlCiAgICAgIC8v57uZ6KGo5Y2V6LWL5YC8CiAgICAgIHRoaXMuZm9ybSA9IHJvdwogICAgICAvL+ihqOWNleS4jeWPr+e8lui+kQogICAgICB0aGlzLmlzRGlzYWJsZWQgPSB0cnVlCiAgICAgIC8v6K6+572u5by55Ye65qGG5qCH6aKYCiAgICAgIHRoaXMudGl0bGUgPSAn6K+m5oOFJwogICAgfSwKICAgIC8v5L+u5pS55oyJ6ZKuCiAgICB1bmRhdGVEZXRhaWxzKHJvdykgewogICAgICAvL+aJk+W8gOW8ueeqlwogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlCiAgICAgIC8v57uZ6KGo5Y2V6LWL5YC8CiAgICAgIHRoaXMuZm9ybSA9IHJvdwogICAgICAvL+ihqOWNleWPr+e8lui+kQogICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZQogICAgICAvL+iuvue9ruW8ueWHuuahhuagh+mimAogICAgICB0aGlzLnRpdGxlID0gJ+S/ruaUuScKICAgIH0sCiAgICAvL+a3u+WKoOaMiemSrgogICAgYWRkU2Vuc29yQnV0dG9uKCkgewogICAgICAvL+aJk+W8gOW8ueeqlwogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlCiAgICAgIC8v57uZ6KGo5Y2V572u56m6CiAgICAgIHRoaXMuZm9ybSA9IHt9CiAgICAgIC8v6KGo5Y2V5Y+v57yW6L6RCiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlCiAgICAgIC8v6K6+572u5by55Ye65qGG5qCH6aKYCiAgICAgIHRoaXMudGl0bGUgPSAn5paw5aKeJwogICAgfSwKICAgIC8v5Yig6Zmk5oyJ6ZKuCiAgICBnZXREZWxldGUocm93KSB7CiAgICAgIHRoaXMuZm9ybSA9IHJvdwogICAgICB0aGlzLiRjb25maXJtKCfmraTmk43kvZzlsIbmsLjkuYXliKDpmaTor6XmlbDmja4sIOaYr+WQpue7p+e7rT8nLCAn5o+Q56S6JywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgcmVtb3ZlKFt0aGlzLmZvcm0ub2JqSWRdKS50aGVuKCh7IGNvZGUgfSkgPT4gewogICAgICAgICAgaWYgKGNvZGUgPT09ICcwMDAwJykgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOaIkOWKnyEnCiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci5wYWdlUmVzaXplID0gJ1knCiAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InLAogICAgICAgICAgICAgIG1lc3NhZ2U6ICfliKDpmaTlpLHotKUhJwogICAgICAgICAgICB9KQogICAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIucGFnZVJlc2l6ZSA9ICdZJwogICAgICAgICAgICB0aGlzLmdldERhdGEoKQogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgIHR5cGU6ICdpbmZvJywKICAgICAgICAgIG1lc3NhZ2U6ICflt7Llj5bmtojliKDpmaQnCiAgICAgICAgfSkKICAgICAgfSkKCiAgICB9LAogICAgLy/noa7orqTmj5DkuqTooajljZUKICAgIGNvbW1pdEZvcm0oKSB7CiAgICAgIHRoaXMuZm9ybS5zYmx4Ym0gPSB0aGlzLnNibHhibQogICAgICB0aGlzLiRyZWZzWydmb3JtJ10udmFsaWRhdGUoKHZhbGlkKSA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBzYXZlT3JVcGRhdGUodGhpcy5mb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gJzAwMDAnKSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKHJlcy5tc2cpCiAgICAgICAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnBhZ2VSZXNpemUgPSAnWScKICAgICAgICAgICAgICB0aGlzLmdldERhdGEoKQogICAgICAgICAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IGZhbHNlCiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubXNnKQogICAgICAgICAgICB9CiAgICAgICAgICB9KQoKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgLy/lhbPpl63lvLnnqpcKICAgIGNsb3NlKCkgewogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSBmYWxzZQogICAgfSwKICAgIC8v5a6a5LmJ6YeN572u5pa55rOVCiAgICBnZXRSZXNldCgpIHsKICAgICAgdGhpcy5xdWVyeVN5enhtUGFyYW0gPSB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgc2JseGJtOiB1bmRlZmluZWQKICAgICAgfQogICAgfSwKICAgIC8v5Yig6Zmk5oyJ6ZKuCiAgICBkZWxldGVTZW5zb3JCdXR0b24oKSB7CgogICAgfSwKICAgIC8v5a+85Ye65oyJ6ZKuCiAgICBoYW5kbGVFeHBvcnQoKSB7CgogICAgfSwKCiAgICAvL+aQnOe0ogogICAgaGFuZGxlUXVlcnkoKSB7CgogICAgfSwKICAgIC8v6YeN572uCiAgICByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLnJlc2V0Rm9ybSgncXVlcnlGb3JtJykKICAgIH0sCiAgICAvL+a4heepuuihqOWNleaVsOaNrgogICAgaGFuZGxlQ2xvc2UoKSB7CiAgICAgIHRoaXMuZm9ybSA9IHt9CiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICB0aGlzLmZvcm0gPSB0aGlzLiRvcHRpb25zLmRhdGEoKS5mb3JtCiAgICAgICAgdGhpcy5yZXNldEZvcm0oJ2Zvcm0nKQogICAgICB9KQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["syzxm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAi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file": "syzxm.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk", "sourcesContent": ["<template>\n\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row :gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card class=\"aside_height\" shadow=\"never\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备类型</span>\n          </div>\n          <div style=\" overflow: auto;height: 84vh\">\n            <el-tree id=\"tree\"\n                     highlight-current\n                     :props=\"props\"\n                     :load=\"loadNode\"\n                     lazy\n                     :default-expanded-keys=\"['1']\"\n                     @node-expand=\"handleNodeClick\"\n                     @node-click=\"handleNodeClick\"\n            />\n          </div>\n        </el-card>\n      </el-col>\n\n\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 180, itemWidth: 300 }\"\n          @handleReset=\"getReset\"\n        />\n\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"addSensorButton\" :disabled=\"addDisabled\"\n            >新增\n            </el-button>\n            <!--<el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"gettableAndPageInforesizableDelete\"-->\n            <!--&gt;删除-->\n            <!--</el-button>-->\n            <!-- <el-button type=\"primary\" icon=\"el-icon-edit\" @click=\"addZxmKxz\" :disabled=\"whmjzButtonDisabled\"\n            >维护枚举项\n            </el-button> -->\n          </div>\n          <comp-table :table-and-page-info=\"tableAndPageInfo\"\n                      @update:multipleSelection=\"handleSelectionChange\"\n                      height=\"69.6vh\"\n          >\n            <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\"\n                             width=\"160\"\n                             :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button @click=\"undateDetails(scope.row)\" v-show=\"scope.row.createBy == currentUser\" type=\"text\"\n                           size=\"small\" title=\"修改\"  class='el-icon-edit'\n                >\n                </el-button>\n                <el-button @click=\"getDetailsInfo(scope.row)\" type=\"text\" size=\"small\" title=\"详情\" class=\"el-icon-view\"></el-button>\n                <el-button @click=\"getDelete(scope.row)\" v-show=\"scope.row.createBy == currentUser\" type=\"text\"\n                           size=\"small\" title=\"删除\" class=\"el-icon-delete\"\n                >\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!-- 详情/新增/修改 -->\n    <el-dialog :title=title :visible.sync=\"isShowDetails\" width=\"60%\" @close=\"handleClose\" v-dialogDrag>\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :disabled=\"isDisabled\" :rules=\"rules\">\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"子项目名称：\" prop=\"zxmmc\">\n              <el-input v-model=\"form.zxmmc\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"子项目描述：\" prop=\"zxmms\">\n              <el-input v-model=\"form.zxmms\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"数值单位：\" prop=\"szdw\">\n              <el-input v-model=\"form.szdw\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"数据精度：\" prop=\"sjjd\">\n              <el-input v-model=\"form.sjjd\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"结果类型：\" prop=\"jglx\">\n              <el-select v-model=\"form.jglx\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in jglxOptionsSelectedList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"计算列：\" prop=\"jsl\">\n              <el-select v-model=\"form.jsl\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in jslOptionsSelectedList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否为空：\" prop=\"sfkwk\">\n              <el-select v-model=\"form.sfkwk\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in sfkwkOptionsSelectedList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否显示：\" prop=\"sfxs\">\n              <el-select v-model=\"form.sfxs\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in sfxsOptionsSelectedList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button v-if=\"title==='新增' || title==='修改'\" type=\"primary\" @click=\"commitForm\">确 认</el-button>\n      </div>\n    </el-dialog>\n    <!--枚举项值弹出框-->\n    <el-dialog :title=\"mjxDialogTitle\" :visible.sync=\"isShowMjzDialog\" width=\"50%\" v-dialogDrag>\n      <el-white class=\"button-group\">\n        <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n          <el-col\n            style=\"display: flex;justify-content: space-between;align-items: center;\"\n          >\n            <div>\n              <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"addMjz\"\n              >新增\n              </el-button>\n              <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"deleteMjz\"\n              >删除\n              </el-button>\n            </div>\n          </el-col>\n        </el-row>\n      </el-white>\n      <el-table stripe border :data=\"mjzDataList\" @selection-change=\"handleSelectionMjzChange\"\n                :header-cell-style=\"{'text-align':'center'}\" :cell-style=\"{'text-align':'center'}\"\n      >\n        <el-table-column type=\"selection\" width=\"55\"/>\n        <el-table-column label=\"序号\" type=\"index\" width=\"50\">\n        </el-table-column>\n        <el-table-column label=\"枚举值\" prop=\"kxz\" :show-overflow-tooltip=\"true\"/>\n      </el-table>\n      <pagination\n        v-show=\"Mjztotal>0\"\n        :total=\"Mjztotal\"\n        :page.sync=\"mjzQueryParams.pageNum\"\n        :limit.sync=\"mjzQueryParams.pageSize\"\n        @pagination=\"getMjzDataList\"\n      />\n    </el-dialog>\n    <!--枚举值新增弹窗-->\n    <el-dialog :title=\"mjzAddDialogTitle\" :visible.sync=\"isShowMjzAddDialog\" width=\"30%\" v-dialogDrag>\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"mjzAddForm\" :disabled=\"mjzAddDialogDisabled\">\n        <el-row :gutter=\"8\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"枚举值：\" prop=\"zxmmc\">\n              <el-input v-model=\"mjzAddForm.kxz\"/>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" v-show=\"addMjzDialogButtonShow\">\n        <el-button @click=\"closeAddMjzDialog\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"commitAddMjzForm\">确 认</el-button>\n      </div>\n    </el-dialog>\n  </div>\n\n</template>\n\n<script>\nimport {\n  getPageDataList,\n  getPageKxzDataList,\n  remove,\n  removeKxzData,\n  saveOrUpdate,\n  saveOrUpdateKxzData\n} from '@/api/dagangOilfield/bzgl/syzxm'\nimport { getDeviceClassTreeNodeByPid } from '@/api/dagangOilfield/bzgl/sblxwh/sblxwh'\n\nexport default {\n  name: 'lpbzk',\n  data() {\n    return {\n      currentUser: this.$store.getters.name,\n      //新增按钮控制\n      addDisabled: true,\n      //树结构懒加载参数\n      props: {\n        label: 'name',\n        children: 'zones',\n        isLeaf: (data, node) => {\n          if (node.level === 2) {\n            return true\n          }\n        }\n      },\n      //枚举值新增弹出框底部按钮控制显示\n      addMjzDialogButtonShow: true,\n      //控制枚举值新增弹出框内容是否可编辑\n      mjzAddDialogDisabled: false,\n      //枚举值新增form表单\n      mjzAddForm: {\n        syzxmid: undefined,\n        kxz: undefined\n      },\n      //枚举值新增弹出框标题\n      mjzAddDialogTitle: '新增',\n      //枚举值新增弹出框控制\n      isShowMjzAddDialog: false,\n      //选中子项目时获取到的第一行数据用来查询枚举值\n      mjzRowForm: {},\n      //维护枚举值button按钮\n      whmjzButtonDisabled: true,\n      //枚举值数据\n      mjzDataList: [],\n      //枚举值参数\n      mjzQueryParams: {\n        syzxmid: undefined,\n        pageSize: 10,\n        pageNum: 1\n      },\n      //枚举值总数\n      Mjztotal: 0,\n      //枚举项弹出框标题\n      mjxDialogTitle: '枚举值维护',\n      //枚举项弹出框\n      isShowMjzDialog: false,\n\n      //删除选择列\n      selectRows: [],\n      //表单验证\n      rules: {\n        zxmmc: [\n          { required: true, message: '请输入子项目名称', trigger: 'blur' }\n        ]\n      },\n      //筛选框\n      filterInfo: {\n        data: {\n          jglx: '',\n          zxmmc: ''\n        },\n        fieldList: [\n          {\n            label: '结果类型',\n            value: 'jglx',\n            type: 'select',\n            options: [\n              { label: '图片', value: '图片' },\n              { label: '数字', value: '数字' },\n              { label: '日期', value: '日期' },\n              { label: '单选', value: '单选' },\n              { label: '枚举', value: '枚举' },\n              { label: '字符', value: '字符' }\n            ],\n            clearable: true\n          },\n          {\n            label: '子项目名称',\n            value: 'zxmmc',\n            type: 'input',\n            clearable: true\n          }\n        ]\n      },\n      //列表页\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: '子项目名称', prop: 'zxmmc', minWidth: '150' },\n          { label: '数值单位', prop: 'szdw', minWidth: '70' },\n          { label: '结果类型', prop: 'jglx', minWidth: '100' },\n          { label: '计算列', prop: 'jsl', minWidth: '60' },\n          { label: '是否可为空', prop: 'sfkwk', minWidth: '80' },\n          { label: '是否显示', prop: 'sfxs', minWidth: '70' },\n          { label: '子项目描述', prop: 'zxmms', minWidth: '150' },\n          { label: '数据精度', prop: 'sjjd', minWidth: '100' }\n          // {\n          //   prop: 'operation',\n          //   label: '操作',\n          //   minWidth: '100px',\n          //   style: {display: 'block'},\n          //   //操作列固定再右侧\n          //   fixed: 'right',\n          //   operation: [\n          //     {name: '修改', clickFun: this.undateDetails},\n          //     {name: '详情', clickFun: this.getDetailsInfo},\n          //   ]\n          // },\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      //查询试验子项目参数\n      querySyzxmParam: {\n        pageNum: 1,\n        pageSize: 10,\n        sblxbm: undefined\n      },\n      sblxbm: '',\n      //结果类型下拉框数据\n      jglxOptionsSelectedList: [\n        { label: '图片', value: '图片' },\n        { label: '数字', value: '数字' },\n        { label: '日期', value: '日期' },\n        { label: '单选', value: '单选' },\n        { label: '枚举', value: '枚举' },\n        { label: '字符', value: '字符' }\n      ],\n      //计算列\n      jslOptionsSelectedList: [\n        { label: '是', value: '是' },\n        { label: '否', value: '否' }\n      ],\n      //是否为空\n      sfkwkOptionsSelectedList: [\n        { label: '是', value: '是' },\n        { label: '否', value: '否' }\n      ],\n      //是否显示\n      sfxsOptionsSelectedList: [\n        { label: '是', value: '是' },\n        { label: '否', value: '否' }\n      ],\n\n      //form表单\n      form: {},\n      //是否显示弹框\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //表单数据\n      dataTable: [],\n      //标题\n      title: '',\n\n      //组织树\n      treeOptions: [\n        {\n          label: '断路器'\n        }, {\n          label: '变压器',\n          children: [{\n            label: '冷却系统',\n            children: [{\n              label: '温控运行情况'\n\n            }, {\n              label: '油箱'\n\n            }, {\n              label: '铁芯'\n\n            }, {\n              label: '绕组'\n\n            }]\n          }]\n        }],\n      //删除是否可用\n      multipleSensor: true,\n      //查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        ancuoTerm: ''\n      },\n      //表单开关\n      isSearchShow: false,\n      //工作票类型下拉菜单\n      gzpTypeOptions: [\n        {\n          value: 'type1',\n          label: '类型1'\n        }, {\n          value: 'type2',\n          label: '类型2'\n        }\n      ]\n\n    }\n  },\n  watch: {},\n  created() {\n    this.getData()\n\n  },\n  methods: {\n    //懒加载函数\n    loadNode(node, resolve) {\n      let TreeparamMap = {\n        pid: '',\n        spbLogo: ['输电设备', '变电设备','配电设备']\n      }\n      if (node.level === 0) {\n        TreeparamMap.pid = 'sb'\n        return this.getTreeNode(TreeparamMap, resolve)\n      }\n      setTimeout(() => {\n        TreeparamMap.pid = node.data.code\n        this.getTreeNode(TreeparamMap, resolve)\n      }, 500)\n\n    },\n\n    //获取树节点数据\n    getTreeNode(paramMap, resolve) {\n      getDeviceClassTreeNodeByPid(paramMap).then(res => {\n        let treeNodes = []\n        res.data.forEach(item => {\n          let node = {\n            name: item.name,\n            level: item.level,\n            id: item.id,\n            pid: item.pid,\n            leaf: false,\n            code: item.code\n          }\n          treeNodes.push(node)\n        })\n        resolve(treeNodes)\n      })\n    },\n\n    //树节点点击事件\n    handleNodeClick(data) {\n      if (data.level === '1') {\n        //开放新增按钮\n        this.addDisabled = false\n        this.treeForm = data\n        this.sblxbm = data.code\n        this.querySyzxmParam.sblxbm = data.code\n        this.getData()\n      } else {\n        this.addDisabled = true\n      }\n    },\n\n    //枚举值新增按钮\n    addMjz() {\n      this.mjzAddForm.kxz = undefined\n      this.isShowMjzAddDialog = true\n    },\n    //提交新增枚举值弹出框表单\n    commitAddMjzForm() {\n      this.mjzAddForm.syzxmid = this.mjzRowForm.objId\n      console.log(this.mjzAddForm)\n      saveOrUpdateKxzData(this.mjzAddForm).then(res => {\n        if (res.code == '0000') {\n          this.$message.success('操作成功！')\n          this.isShowMjzAddDialog = false\n          this.getMjzDataList()\n        }\n      })\n    },\n    //取消按钮(枚举值新增弹出框)\n    closeAddMjzDialog() {\n      this.isShowMjzAddDialog = false\n    },\n    //获取枚举值列表方法\n    getMjzDataList() {\n      this.mjzQueryParams.syzxmid = this.mjzRowForm.objId\n      getPageKxzDataList(this.mjzQueryParams).then(res => {\n        console.log(res)\n        this.Mjztotal = res.data.total\n        this.mjzDataList = res.data.records\n      })\n    },\n    //枚举值行选中事件\n    handleSelectionMjzChange(rows) {\n      this.selectedKxzDataRow = rows\n\n    },\n    //删除枚举值列表\n    deleteMjz() {\n      if (this.selectedKxzDataRow.length < 1) {\n        this.$message.warning('请选择正确的数据！！！')\n        return\n      }\n      let ids = this.selectedKxzDataRow.map(item => {\n        return item.objId\n      })\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        removeKxzData(ids).then(({ code }) => {\n          if (code === '0000') {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            })\n            this.getMjzDataList()\n          } else {\n            this.$message({\n              type: 'error',\n              message: '删除失败!'\n            })\n            this.getMjzDataList()\n          }\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        })\n      })\n    },\n    //维护枚举值按钮\n    addZxmKxz() {\n      if (this.mjzRowForm.jglx != '枚举') {\n        this.$message.warning('请选择结果类型为枚举类型的数据！')\n      } else {\n        //打开弹窗\n        this.isShowMjzDialog = true\n        this.mjzQueryParams.syzxmid = this.mjzRowForm.objId\n        //获取枚举值列表\n        this.getMjzDataList()\n      }\n    },\n    //行选中\n    handleSelectionChange(rows) {\n      this.selectRows = rows\n      this.whmjzButtonDisabled = rows.length != 1\n      //获取到当前行对象\n      this.mjzRowForm = rows[0]\n    },\n    //列表查询子项目列表\n    async getData(params) {\n      try {\n        this.querySyzxmParam = { ...this.querySyzxmParam, ...params }\n        const param = this.querySyzxmParam\n        const { data, code } = await getPageDataList(param)\n        if (code === '0000') {\n          this.tableAndPageInfo.tableData = data.records\n          this.tableAndPageInfo.pager.total = data.total\n        }\n      } catch (e) {\n        console.log(e)\n      }\n    },\n    //获取详情\n    getDetailsInfo(row) {\n      //打开弹窗\n      this.isShowDetails = true\n      //给表单赋值\n      this.form = row\n      //表单不可编辑\n      this.isDisabled = true\n      //设置弹出框标题\n      this.title = '详情'\n    },\n    //修改按钮\n    undateDetails(row) {\n      //打开弹窗\n      this.isShowDetails = true\n      //给表单赋值\n      this.form = row\n      //表单可编辑\n      this.isDisabled = false\n      //设置弹出框标题\n      this.title = '修改'\n    },\n    //添加按钮\n    addSensorButton() {\n      //打开弹窗\n      this.isShowDetails = true\n      //给表单置空\n      this.form = {}\n      //表单可编辑\n      this.isDisabled = false\n      //设置弹出框标题\n      this.title = '新增'\n    },\n    //删除按钮\n    getDelete(row) {\n      this.form = row\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        remove([this.form.objId]).then(({ code }) => {\n          if (code === '0000') {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            })\n            this.tableAndPageInfo.pager.pageResize = 'Y'\n            this.getData()\n          } else {\n            this.$message({\n              type: 'error',\n              message: '删除失败!'\n            })\n            this.tableAndPageInfo.pager.pageResize = 'Y'\n            this.getData()\n          }\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        })\n      })\n\n    },\n    //确认提交表单\n    commitForm() {\n      this.form.sblxbm = this.sblxbm\n      this.$refs['form'].validate((valid) => {\n        if (valid) {\n          saveOrUpdate(this.form).then(res => {\n            if (res.code === '0000') {\n              this.$message.success(res.msg)\n              this.tableAndPageInfo.pager.pageResize = 'Y'\n              this.getData()\n              this.isShowDetails = false\n            } else {\n              this.$message.error(res.msg)\n            }\n          })\n\n        }\n      })\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false\n    },\n    //定义重置方法\n    getReset() {\n      this.querySyzxmParam = {\n        pageNum: 1,\n        pageSize: 10,\n        sblxbm: undefined\n      }\n    },\n    //删除按钮\n    deleteSensorButton() {\n\n    },\n    //导出按钮\n    handleExport() {\n\n    },\n\n    //搜索\n    handleQuery() {\n\n    },\n    //重置\n    resetQuery() {\n      this.resetForm('queryForm')\n    },\n    //清空表单数据\n    handleClose() {\n      this.form = {}\n      this.$nextTick(() => {\n        this.form = this.$options.data().form\n        this.resetForm('form')\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n</style>\n"]}]}