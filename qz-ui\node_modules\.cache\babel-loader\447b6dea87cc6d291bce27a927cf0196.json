{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\jbwh_jbxx.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\jbwh_jbxx.vue", "mtime": 1706897323219}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMubWFwIik7CgpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZyIpOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pdGVyYXRvciIpOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuaXRlcmF0b3IiKTsKCk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwOwoKcmVxdWlyZSgicmVnZW5lcmF0b3ItcnVudGltZS9ydW50aW1lIik7Cgp2YXIgX2FzeW5jVG9HZW5lcmF0b3IyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvYXN5bmNUb0dlbmVyYXRvciIpKTsKCnZhciBfZWxlbWVudFVpID0gcmVxdWlyZSgiZWxlbWVudC11aSIpOwoKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gewogIG5hbWU6ICdqYndoX2pieHgnLAogIHByb3BzOiB7CiAgICBzYmx4OiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJycKICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBsb2FkaW5nOiBudWxsLAogICAgICAvL+mBrue9qeWxggogICAgICB0YWJsZUFuZFBhZ2VJbmZvOiB7CiAgICAgICAgcGFnZXI6IHsKICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgIHNpemVzOiBbMTAsIDIwLCA1MCwgMTAwXQogICAgICAgIH0sCiAgICAgICAgb3B0aW9uOiB7CiAgICAgICAgICBzZXJpYWxOdW1iZXI6IHRydWUKICAgICAgICB9LAogICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgdGFibGVIZWFkZXI6IFsvLyB7IHByb3A6ICJibSIsIGxhYmVsOiAi57yW56CBIn0sCiAgICAgICAgewogICAgICAgICAgcHJvcDogIm9iaklkIiwKICAgICAgICAgIGxhYmVsOiAi57yW56CBIgogICAgICAgIH0sIHsKICAgICAgICAgIHByb3A6ICJtYyIsCiAgICAgICAgICBsYWJlbDogIuWQjeensCIKICAgICAgICB9XQogICAgICB9CiAgICB9OwogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHRoaXMuZ2V0RGF0YSgpOwogIH0sCiAgbWV0aG9kczogewogICAgZ2V0RGF0YTogZnVuY3Rpb24gZ2V0RGF0YSgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKCiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKCAvKiNfX1BVUkVfXyovcmVnZW5lcmF0b3JSdW50aW1lLm1hcmsoZnVuY3Rpb24gX2NhbGxlZSgpIHsKICAgICAgICByZXR1cm4gcmVnZW5lcmF0b3JSdW50aW1lLndyYXAoZnVuY3Rpb24gX2NhbGxlZSQoX2NvbnRleHQpIHsKICAgICAgICAgIHdoaWxlICgxKSB7CiAgICAgICAgICAgIHN3aXRjaCAoX2NvbnRleHQucHJldiA9IF9jb250ZXh0Lm5leHQpIHsKICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICAvL+W8gOWQr+mBrue9qeWxggogICAgICAgICAgICAgICAgX3RoaXMubG9hZGluZyA9IF9lbGVtZW50VWkuTG9hZGluZy5zZXJ2aWNlKHsKICAgICAgICAgICAgICAgICAgbG9jazogdHJ1ZSwKICAgICAgICAgICAgICAgICAgLy9sb2Nr55qE5L+u5pS556ymLS3pu5jorqTmmK9mYWxzZQogICAgICAgICAgICAgICAgICB0ZXh0OiAi5Yqg6L295Lit77yM6K+356iN5ZCOIiwKICAgICAgICAgICAgICAgICAgLy/mmL7npLrlnKjliqDovb3lm77moIfkuIvmlrnnmoTliqDovb3mlofmoYgKICAgICAgICAgICAgICAgICAgc3Bpbm5lcjogImVsLWljb24tbG9hZGluZyIsCiAgICAgICAgICAgICAgICAgIC8v6Ieq5a6a5LmJ5Yqg6L295Zu+5qCH57G75ZCNCiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICJyZ2JhKDAsIDAsIDAsIDAuNykiLAogICAgICAgICAgICAgICAgICAvL+mBrue9qeWxguminOiJsgogICAgICAgICAgICAgICAgICB0YXJnZXQ6IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoIiN0YWJsZV9qYnh4IikKICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICAgIF90aGlzLmxvYWRpbmcuY2xvc2UoKTsgLy/lhbPpl63pga7nvanlsYIKCgogICAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LnN0b3AoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICBjbGlja01haW5UYWJsZTogZnVuY3Rpb24gY2xpY2tNYWluVGFibGUodmFsKSB7CiAgICAgIHZhciBtYXAgPSBuZXcgTWFwKCk7CiAgICAgIG1hcC5zZXQodmFsLm9iaklkLCB2YWwubWMpOwogICAgICB0aGlzLiRlbWl0KCdkYkNsaWNrUm93JywgJ2dldFBhcmFtZXRlcignICsgdmFsLm9iaklkICsgJyknLCBtYXApOwogICAgfQogIH0KfTsKZXhwb3J0cy5kZWZhdWx0ID0gX2RlZmF1bHQ7"}, {"version": 3, "sources": ["jbwh_jbxx.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAUA;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,WADA;AAEA,EAAA,KAAA,EAAA;AACA,IAAA,IAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAAA,EAAA;AAFA;AADA,GAFA;AAQA,EAAA,IARA,kBAQA;AACA,WAAA;AACA,MAAA,OAAA,EAAA,IADA;AACA;AACA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,YAAA,EAAA;AADA,SAPA;AAUA,QAAA,SAAA,EAAA,EAVA;AAWA,QAAA,WAAA,EAAA,CACA;AACA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAHA;AAXA;AAFA,KAAA;AAoBA,GA7BA;AA8BA,EAAA,OA9BA,qBA8BA;AACA,SAAA,OAAA;AACA,GAhCA;AAiCA,EAAA,OAAA,EAAA;AACA,IAAA,OADA,qBACA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA,gBAAA,KAAA,CAAA,OAAA,GAAA,mBAAA,OAAA,CAAA;AACA,kBAAA,IAAA,EAAA,IADA;AACA;AACA,kBAAA,IAAA,EAAA,SAFA;AAEA;AACA,kBAAA,OAAA,EAAA,iBAHA;AAGA;AACA,kBAAA,UAAA,EAAA,oBAJA;AAIA;AACA,kBAAA,MAAA,EAAA,QAAA,CAAA,aAAA,CAAA,aAAA;AALA,iBAAA,CAAA;;AAQA,gBAAA,KAAA,CAAA,OAAA,CAAA,KAAA,GAVA,CAUA;;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KAZA;AAaA,IAAA,cAbA,0BAaA,GAbA,EAaA;AACA,UAAA,GAAA,GAAA,IAAA,GAAA,EAAA;AACA,MAAA,GAAA,CAAA,GAAA,CAAA,GAAA,CAAA,KAAA,EAAA,GAAA,CAAA,EAAA;AACA,WAAA,KAAA,CAAA,YAAA,EAAA,kBAAA,GAAA,CAAA,KAAA,GAAA,GAAA,EAAA,GAAA;AACA;AAjBA;AAjCA,C", "sourcesContent": ["<template>\n  <comp-table\n    :table-and-page-info=\"tableAndPageInfo\"\n    @rowDbClick=\"clickMainTable\"\n    height=\"58vh\"\n    id=\"table_jbxx\"\n  />\n</template>\n\n<script>\nimport { Loading } from 'element-ui'\n\nexport default {\n  name: 'jbwh_jbxx',\n  props: {\n    sblx:{\n      type:String,\n      default:'',\n    },\n  },\n  data() {\n    return {\n      loading: null,//遮罩层\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n        },\n        option: {\n          serialNumber: true,\n        },\n        tableData: [],\n        tableHeader: [\n          // { prop: \"bm\", label: \"编码\"},\n          { prop: \"objId\", label: \"编码\"},\n          { prop: \"mc\", label: \"名称\" },\n        ],\n      },\n    };\n  },\n  mounted() {\n    this.getData();\n  },\n  methods:{\n    async getData() {\n      //开启遮罩层\n      this.loading = Loading.service({\n        lock: true, //lock的修改符--默认是false\n        text: \"加载中，请稍后\", //显示在加载图标下方的加载文案\n        spinner: \"el-icon-loading\", //自定义加载图标类名\n        background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n        target: document.querySelector(\"#table_jbxx\"),\n      });\n\n      this.loading.close();//关闭遮罩层\n    },\n    clickMainTable(val){\n      let map = new Map();\n      map.set(val.objId,val.mc);\n      this.$emit('dbClickRow','getParameter(' + val.objId + ')',map);\n    },\n  },\n}\n</script>\n\n<style scoped>\n\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk"}]}