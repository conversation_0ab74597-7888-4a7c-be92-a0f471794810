{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\processdefinition\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\processdefinition\\index.vue", "mtime": 1706897322046}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+GA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/activiti/processdefinition", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n      <el-form class=\"searchForm1\" :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"120px\">\n        <el-form-item label=\"流程key：\" prop=\"key\">\n          <el-input v-model=\"queryParams.key\" placeholder=\"请输入流程key\" clearable class=\"common-width\"\n                    @keyup.enter.native=\"handleQuery\"/>\n        </el-form-item>\n        <el-form-item label=\"名称：\" prop=\"name\">\n          <el-input v-model=\"queryParams.name\" placeholder=\"请输入名称\" clearable class=\"common-width\"\n                    @keyup.enter.native=\"handleQuery\"/>\n        </el-form-item>\n        <el-form-item label=\"所属分类：\" prop=\"category\">\n          <el-input v-model=\"queryParams.category\" placeholder=\"请输入所属分类\" clearable class=\"common-width\"\n                    @keyup.enter.native=\"handleQuery\"/>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"cyan\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\n          <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\" type=\"warning\">重置</el-button>\n        </el-form-item>\n      </el-form>\n    <el-row :gutter=\"20\">\n      <el-col :span=\"24\">\n        <el-white class=\"button-group\">\n          <el-row :gutter=\"10\" class=\"mb8 pull-right\">\n            <el-col :span=\"1.5\">\n              <el-upload\n                :on-success=\"handleAvatarSuccess\"\n                accept=\".bpmn,.zip,.bar\"\n                :headers=\"header\"\n                action=\"/activiti-api/definition/upload\"\n                :before-upload=\"beforeUpload\"\n                ref=\"upload\"\n                multiple\n                :show-file-list=\"false\"\n              >\n                <el-button type=\"primary\" icon=\"el-icon-upload\"  >部署流程定义 </el-button>\n              </el-upload>\n            </el-col>\n          </el-row>\n          <el-table style=\"margin-top: 15px;\" stripe border v-loading=\"loading\" :data=\"processdefinition\" @selection-change=\"handleSelectionChange\" height=\"65vh\">\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n            <el-table-column label=\"流程ID\" fixed align=\"center\" prop=\"id\" width=\"160\"/>\n            <el-table-column label=\"流程KEY\" align=\"center\" prop=\"key\"  />\n            <el-table-column label=\"流程名称\" align=\"center\" prop=\"name\" width=\"200\"/>\n            <el-table-column label=\"版本\"  align=\"center\" prop=\"version\" width=\"120\" />\n            <el-table-column label=\"流程描述\" align=\"center\" prop=\"description\" width=\"120\" >\n              <template slot-scope=\"scope\">\n                <el-popover v-if=\"scope.row.description.length>20\" trigger=\"hover\" placement=\"top\" width=\"200\">\n                  {{ scope.row.description }}\n                  <div slot=\"reference\" >\n                    {{ scope.row.description.substring(0,20)+'...' }}\n                  </div>\n                </el-popover>\n                <span v-else >\n            {{ scope.row.description}}\n          </span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"所属分类\" align=\"center\" prop=\"category\"  width=\"200\"  />\n            <el-table-column label=\"部署时间\" align=\"center\" prop=\"deploymentTime\" width=\"200\"/>\n            <el-table-column label=\"流程定义\" align=\"center\" prop=\"resourceName\" width=\"200\">\n              <template slot-scope=\"scope\">\n                <el-popover v-if=\"scope.row.resourceName.length>20\" trigger=\"hover\" placement=\"top\" width=\"200\">\n                  <a style=\"color: #409EFF\" :href=\"'/activiti-api/definition/readResource?pdid='+scope.row.id+'&resourceName='+scope.row.resourceName\" target=\"_blank\" >{{ scope.row.resourceName }}</a>\n                  <div slot=\"reference\" >\n                    {{ scope.row.resourceName.substring(0,20)+'...' }}\n                  </div>\n                </el-popover>\n                <span v-else >\n             <a style=\"color: #409EFF\" :href=\"'/activiti-api/definition/readResource?pdid='+scope.row.id+'&resourceName='+scope.row.resourceName\" target=\"_blank\" >{{ scope.row.resourceName }}</a>\n          </span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"流程图\" align=\"center\" prop=\"diagramResourceName\" width=\"200\">\n              <template slot-scope=\"scope\">\n                <a style=\"color: #409EFF\" :href=\"'/activiti-api/definition/readResource?pdid='+scope.row.id+'&resourceName='+scope.row.diagramResourceName\" target=\"_blank\" >{{scope.row.key}}.png</a>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"流程定义状态\" align=\"center\" prop=\"suspendStateName\" width=\"100\" >\n              <template slot-scope=\"scope\">\n                <span v-if=\"scope.row.suspendState==1\" style=\"color: #67C23A\">已激活</span>\n                <span v-else style=\"color: #E6A23C\">已挂起</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"操作\"  fixed=\"right\" align=\"center\"   class-name=\"small-padding fixed-width\" width=\"180\">\n              <template slot-scope=\"scope\">\n                <slot v-if=\"scope.row.suspendState==2\">\n                  <el-button size=\"mini\" type=\"text\" icon=\"el-icon-check\" @click=\"handleUpdate(scope.row)\" >激活</el-button>\n                </slot>\n                <slot  v-else>\n                  <el-button size=\"mini\" type=\"text\" icon=\"el-icon-time\" @click=\"handleUpdate(scope.row)\" >挂起</el-button>\n                </slot>\n                <el-button size=\"mini\" type=\"text\" icon=\"el-icon-sort\" @click=\"handleConvertToModel(scope.row)\" >转模型</el-button>\n                <el-button  size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\"  >删除</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n\n          <pagination v-show=\"total>0\"\n                      :total=\"total\"\n                      :page.sync=\"queryParams.pageNum\"\n                      :limit.sync=\"queryParams.pageSize\"\n                      @pagination=\"getList\"\n          />\n        </el-white>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\n  import {\n    list,del,suspendOrActiveApply,convertToModel\n  } from \"@/api/activiti/processdefinition\";\n  export default {\n    name: \"ProcessDefinition\",\n    data() {\n      return {\n        //上传图片时的请求头\n      header: {},\n        // 遮罩层\n        loading: true,\n        // 选中数组\n        ids: [],\n        // 显示搜索条件\n        showSearch: true,\n        // 总条数\n        total: 0,\n        // 用户表格数据\n        processdefinition: null,\n        // 是否显示弹出层\n        open: false,\n        // 部门名称\n        // 表单参数\n        form: {},\n        // 查询参数\n        queryParams: {\n          pageNum: 1,\n          pageSize: 10,\n          key: undefined,\n          name: undefined,\n        },\n        fileList:[]\n      };\n    },\n    created() {\n      this.getList();\n    },\n    mounted() {\n      //获取token\n    this.header.token = getToken();\n    },\n    methods: {\n      // 测试数据字典方法结束\n      /** 查询用户列表 */\n      getList() {\n        this.loading = true;\n        list(this.queryParams).then(\n          (response) => {\n            this.processdefinition = response.data.records;\n            this.total = response.data.total;\n            this.loading = false;\n          }\n        );\n      },\n      /**\n       * 表单重置\n       * */\n      reset() {\n        this.form = {\n          key: undefined,\n          name: undefined,\n          category:undefined\n        };\n        this.resetForm(\"form\");\n      },\n      /** 搜索按钮操作 */\n      handleQuery() {\n        this.queryParams.pageNum = 1;\n        this.getList();\n      },\n      /** 重置按钮操作 */\n      resetQuery() {\n        this.dateRange = [];\n        this.resetForm(\"queryForm\");\n        this.handleQuery();\n      },\n      /***多选框选中数据***/\n      handleSelectionChange(selection) {\n        this.ids = selection.map((item) => item.userId);\n      },\n      /** 编辑按钮操作 */\n      handleUpdate(row) {\n        let suspendState = row.suspendState;\n        suspendOrActiveApply({id:row.id,suspendState:row.suspendState}).then(res =>{\n          if(res.code==\"0000\"){\n            this.msgSuccess(suspendState==\"2\"?\"激活成功\":\"挂起成功\");\n            this.getList();\n          }\n        })\n      },\n      /** 删除按钮操作 */\n      handleDelete(row) {\n        const deploymentId = row.deploymentId;\n        this.$confirm(\n          '是否确认删除流程ID为\"' + row.id + '\"的数据项?',\n          \"警告\",\n          {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\",\n          }\n        )\n          .then(function () {\n            return del(deploymentId);\n          })\n          .then(() => {\n            this.getList();\n            this.msgSuccess(\"删除成功\");\n          })\n          .catch(function () {\n          });\n      },\n      /**\n       * 上传成功会返回response,拿到后端/upload的返回值\n       * @param response\n       * @param file\n       */\n      handleAvatarSuccess (response, file) {\n        if (response.code === \"0000\") {\n          this.msgSuccess(\"部署成功！\")\n          this.getList();\n        } else {\n          this.$message.error(response.msg)\n          this.$refs.upload.clearFiles()\n        }\n      },\n      /** 上传文件 **/\n      beforeUpload(){\n\n      },\n      /** 转化模型 **/\n      handleConvertToModel(row){\n        convertToModel(row.id).then(res=>{\n          this.msgSuccess(\"转换成功！\")\n        })\n      }\n    },\n  };\n</script>\n"]}]}