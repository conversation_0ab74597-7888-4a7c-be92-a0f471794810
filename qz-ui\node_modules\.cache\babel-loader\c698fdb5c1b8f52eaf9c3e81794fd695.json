{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\dgTodoItem\\remindTodo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\dgTodoItem\\remindTodo.vue", "mtime": 1744298300389}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["remindTodo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAoHA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;AACA,EAAA,IAAA,EAAA,kBADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,CAEA;AAFA;AAFA,OADA;AAQA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AACA;AACA;AAAA,UAAA,IAAA,EAAA,kBAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,gBAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,CARA;AAcA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAdA,OARA;AAwBA,MAAA,UAAA,EAAA,EAxBA;AAyBA,MAAA,MAAA,EAAA;AACA,QAAA,IAAA,EAAA;AADA,OAzBA;AA4BA,MAAA,UAAA,EAAA,IA5BA;AA6BA,MAAA,UAAA,EAAA;AACA,QAAA,EAAA,EAAA,CADA;AAEA,QAAA,EAAA,EAAA;AAFA,OA7BA;AAiCA,MAAA,QAAA,EAAA,KAjCA;AAkCA,MAAA,QAAA,EAAA;AACA,QAAA,QAAA,EAAA;AADA,OAlCA;AAqCA,MAAA,UAAA,EAAA;AArCA,KAAA;AAuCA,GA1CA;AA2CA,EAAA,OA3CA,qBA2CA;AACA,SAAA,OAAA,CAAA,KAAA,MAAA,CAAA,KAAA;AACA,GA7CA;AA8CA,EAAA,OA9CA,qBA8CA,CACA;AACA,GAhDA;AAiDA,EAAA,OAAA,EAAA;AACA,IAAA,MADA,kBACA,GADA,EACA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA;AACA,QAAA,IAAA,EAAA,GAAA,CAAA,YADA;AAEA,QAAA,KAAA,EAAA;AAAA,UAAA,KAAA,EAAA,GAAA,CAAA,OAAA;AAAA,UAAA,MAAA,EAAA,GAAA,CAAA;AAAA;AAFA,OAAA,EADA,CAKA;;AACA,UAAA,GAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,0CAAA;AACA,UAAA,QAAA,EAAA,GAAA,CAAA,EADA;AAEA,UAAA,QAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA;AAFA,SAAA,EAGA,IAHA,CAGA,UAAA,GAAA,EAAA,CACA;AACA;AACA;AACA,SAPA;AAQA;AACA,KAjBA;;AAkBA;AACA,IAAA,cAnBA,0BAmBA,EAnBA,EAmBA;AACA,sCAAA,EAAA;AACA,KArBA;AAsBA,IAAA,OAtBA,mBAsBA,KAtBA,EAsBA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,GADA,+DACA,KADA,GACA,KAAA,CAAA,MADA;AAEA,gBAAA,GAAA,CAAA,QAAA,GAAA,KAAA,CAAA,MAAA,CAAA,OAAA,CAAA,IAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AAHA;AAAA,uBAIA,+BAAA,GAAA,CAJA;;AAAA;AAAA;AAIA,gBAAA,IAJA,wBAIA,IAJA;AAIA,gBAAA,IAJA,wBAIA,IAJA;;AAKA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,KAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,KAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AARA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,KA/BA;AAgCA,IAAA,YAhCA,wBAgCA,IAhCA,EAgCA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KAlCA;AAmCA,IAAA,WAnCA,uBAmCA,IAnCA,EAmCA,CAAA,CAnCA;AAqCA,IAAA,UArCA,sBAqCA,GArCA,EAqCA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,QAAA,mCAAA,GAAA;AACA,gBAAA,MAAA,CAAA,UAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,QAAA,GAAA,IAAA,CAHA,CAIA;;AAJA,sBAKA,GAAA,CAAA,MAAA,IAAA,CALA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAMA,kCAAA;AACA,kBAAA,QAAA,EAAA,GAAA,CAAA,EADA;AAEA,kBAAA,QAAA,EAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA;AAFA,iBAAA,CANA;;AAAA;AAAA;AAAA,uBAUA,MAAA,CAAA,OAAA,EAVA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,KAjDA;AAkDA,IAAA,SAlDA,uBAkDA;AACA,WAAA,QAAA,GAAA;AACA,QAAA,QAAA,EAAA;AADA,OAAA;AAGA,KAtDA;AAuDA,IAAA,WAvDA,uBAuDA,GAvDA,EAuDA;AACA,WAAA,IAAA,GAAA,IAAA,KAAA,UAAA,EAAA;AACA,YAAA,GAAA,KAAA,GAAA,CAAA,IAAA,EAAA;AACA,eAAA,MAAA,CAAA,QAAA,GAAA,KAAA,UAAA,CAAA,GAAA,CAAA;AACA,eAAA,OAAA;AACA;AACA;AACA;AA9DA;AAjDA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-white>\n      <el-filter\n        ref=\"filter1\"\n        :data=\"filterInfo.data\"\n        :field-list=\"filterInfo.fieldList\"\n        :width=\"{ labelWidth: 180, itemWidth: 200 }\"\n        @handleReset=\"filterReset\"\n      ></el-filter>\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"selectChange\"\n        height=\"75vh\"\n      >\n        <el-table-column\n          slot=\"table_start\"\n          align=\"center\"\n          style=\"display: block;word-break : normal;\"\n          label=\"提醒标题\"\n          min-width=\"200\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" size=\"small\" @click=\"goPage(scope.row)\">{{\n              scope.row.title\n            }}</el-button>\n          </template>\n        </el-table-column>\n        <el-table-column\n          slot=\"table_six\"\n          align=\"center\"\n          style=\"display: block;height: auto\"\n          label=\"状态\"\n          min-width=\"180\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-badge\n              :value=\"scope.row.isRead == 0 ? '未读' : '已读'\"\n              :type=\"scope.row.isRead == 0 ? 'danger' : 'primary'\"\n              class=\"item\"\n            >\n            </el-badge>\n          </template>\n        </el-table-column>\n        <el-table-column slot=\"table_seven\" label=\"附件\" prop=\"attachment\">\n          <template slot-scope=\"scope\">\n            <span v-if=\"scope.row.fileList.length > 0\">有附件</span>\n            <span v-else>无附件</span>\n          </template>\n        </el-table-column>\n        <el-table-column\n          slot=\"table_eight\"\n          align=\"center\"\n          fixed=\"right\"\n          style=\"display: block\"\n          label=\"操作\"\n          min-width=\"70\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" size=\"small\" @click=\"getDetails(scope.row)\"\n              >查看</el-button\n            >\n          </template>\n        </el-table-column>\n      </comp-table>\n    </el-white>\n    <el-dialog\n      title=\"提醒详情\"\n      :visible.sync=\"openInfo\"\n      width=\"50%\"\n      append-to-body\n      :close-on-click-modal=\"false\"\n      v-dialogDrag\n    >\n      <el-form ref=\"formInfo\" :model=\"formInfo\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"标题：\" prop=\"title\">\n              <el-input v-model=\"formInfo.title\" :disabled=\"isDisabled\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"内容：\" prop=\"content\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"3\"\n                v-model=\"formInfo.content\"\n                :disabled=\"isDisabled\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-form-item\n            label=\"附件：\"\n            prop=\"attachment\"\n            v-if=\"formInfo.fileList.length > 0\"\n          >\n            <template slot-scope=\"scope\">\n              <span v-for=\"it in formInfo.fileList\">{{ it.fileOldName }}</span>\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"downloadHandle(formInfo.id)\"\n                >下载</el-button\n              >\n            </template>\n          </el-form-item>\n        </el-row>\n      </el-form>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getNoticePage, updateReadStatus } from \"@/api/activiti/DgTodoItem\";\nimport { downloadByBusinessId } from \"@/api/tool/file\";\nexport default {\n  name: \"proclamationTodo\",\n  data() {\n    return {\n      filterInfo: {\n        data: {},\n        fieldList: [\n          { label: \"提醒标题\", type: \"input\", value: \"title\" }\n          //{ label: '发布人', type: 'input', value: 'senderName'}\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          // { prop: \"title\", label: \"提醒标题\", minWidth: \"140\" },\n          //{ prop: 'senderName', label: '发布人', minWidth: '120' },\n          { prop: \"publishStartTime\", label: \"提醒时间\", minWidth: \"120\" },\n          { prop: \"publishEndTime\", label: \"结束时间\", minWidth: \"120\" }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      selectRows: [],\n      params: {\n        type: 2\n      },\n      activeName: \"db\",\n      tabRefresh: {\n        db: 0,\n        yb: 1\n      },\n      openInfo: false,\n      formInfo: {\n        fileList: []\n      },\n      isDisabled: false\n    };\n  },\n  created() {\n    this.getData(this.$route.query);\n  },\n  mounted() {\n    //this.getData(this.$route.query)\n  },\n  methods: {\n    goPage(row) {\n        this.$router.push({\n          path: row.publishScope,\n          query: { objId: row.content, module: row.moduleKey }\n        });\n        //如果是未查看状态，点击查看时变成已查看\n      if (row.isRead == 0) {\n        updateReadStatus({\n          noticeId: row.id,\n          userName: this.$store.getters.name\n        }).then(res => {\n          // if (res.code === \"0000\") {\n          //   this.getData();\n          // }\n        });\n      }\n    },\n    /**下载附件*/\n    downloadHandle(id) {\n      downloadByBusinessId(id);\n    },\n    async getData(param) {\n      let par = { ...param, ...this.params };\n      par.userName = this.$store.getters.name;\n      console.log(par);\n      let { code, data } = await getNoticePage(par);\n      if (code === \"0000\") {\n        this.tableAndPageInfo.tableData = data.records;\n        this.tableAndPageInfo.pager.total = data.total;\n      }\n    },\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n    filterReset(data) {},\n\n    async getDetails(row) {\n      this.formInfo = { ...row };\n      this.isDisabled = true;\n      this.openInfo = true;\n      //如果是未查看状态，点击查看时变成已查看\n      if (row.isRead == 0) {\n        await updateReadStatus({\n          noticeId: row.id,\n          userName: this.$store.getters.name\n        });\n        await this.getData();\n      }\n    },\n    closeForm() {\n      this.formInfo = {\n        fileList: []\n      };\n    },\n    handleClick(tab) {\n      for (let key in this.tabRefresh) {\n        if (key === tab.name) {\n          this.params.isHandle = this.tabRefresh[key];\n          this.getData();\n        }\n      }\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n/*  .el-badge {\n\n  /deep/.el-badge__content\n  {\n    !*&.is-fixed {\n      position: absolute;\n      right: 10px;\n      top: 0;\n      transform: translateX(100%) translateY(-50%);\n    }*!\n    margin-top: 0.45vh;\n    font-size: 10px;\n  }\n\n  }*/\n.item {\n  width: 8.5rem;\n  height: 1.25rem;\n}\n</style>\n"], "sourceRoot": "src/views/activiti/dgTodoItem"}]}