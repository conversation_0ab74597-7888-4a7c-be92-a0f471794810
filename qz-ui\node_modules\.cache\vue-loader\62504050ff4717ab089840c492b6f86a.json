{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sdsbgl\\sdxl.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sdsbgl\\sdxl.vue", "mtime": 1706897324964}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgc2F2ZU9yVXBkYXRleGwsZ2V0TGlzdHhsLHhscmVtb3ZlCn0gZnJvbSAnQC9hcGkvZGFnYW5nT2lsZmllbGQvYXNzZXQvc2R4bCcKaW1wb3J0IHsgZ2V0T3JnYW5pemF0aW9uU2VsZWN0ZWQgfSBmcm9tICdAL2FwaS9kYWdhbmdPaWxmaWVsZC9hc3NldC9iZHNidHonCmltcG9ydCB7Z2V0RGljdFR5cGVEYXRhfSBmcm9tICJAL2FwaS9zeXN0ZW0vZGljdC9kYXRhIjsKICBleHBvcnQgZGVmYXVsdCB7CiAgICBuYW1lOiAicXhiemsiLAogICAgZGF0YSgpIHsKICAgICAgcmV0dXJuIHsKICAgICAgICBvcHRpb25zOiBbewogICAgICAgICAgdmFsdWU6ICcxMTAnLAogICAgICAgICAgbGFiZWw6ICcxMTBrVicKICAgICAgICB9LCB7CiAgICAgICAgICB2YWx1ZTogJzM1JywKICAgICAgICAgIGxhYmVsOiAnMzVrVicKICAgICAgICB9LCB7CiAgICAgICAgICB2YWx1ZTogJzEwJywKICAgICAgICAgIGxhYmVsOiAnMTBrVicKICAgICAgICB9LCB7CiAgICAgICAgICB2YWx1ZTogJzYnLAogICAgICAgICAgbGFiZWw6ICc2a1YnCiAgICAgICAgfV0sCiAgICAgICAgc2hvdzpmYWxzZSwKICAgICAgICBmaWx0ZXJJbmZvOiB7CiAgICAgICAgICBkYXRhOiB7CiAgICAgICAgICAgIGR5ZGpibTogJycsCiAgICAgICAgICAgIGxpbmVOYW1lOiAnJywKICAgICAgICAgICAgbGluZVR5cGU6ICcnLAogICAgICAgICAgICAvLyBzZnpneDogJycsCiAgICAgICAgICAgIGxpbmVTdGF0dXM6ICcnLAogICAgICAgICAgfSwKICAgICAgICAgIGZpZWxkTGlzdDogWwogICAgICAgICAgICB7CiAgICAgICAgICAgICAgbGFiZWw6ICfnlLXljovnrYnnuqcnLCB0eXBlOiAnc2VsZWN0JywgdmFsdWU6ICdkeWRqYm0nLCBvcHRpb25zOiBbCiAgICAgICAgICAgICAgICB7bGFiZWw6ICIxMTBrViIsIHZhbHVlOiAiMTEwIn0sCiAgICAgICAgICAgICAgICB7bGFiZWw6ICIzNWtWIiwgdmFsdWU6ICIzNSJ9LAogICAgICAgICAgICAgICAge2xhYmVsOiAiMTBrViIsIHZhbHVlOiAiMTAifSwKICAgICAgICAgICAgICAgIHtsYWJlbDogIjZrViIsIHZhbHVlOiAiNiJ9LAogICAgICAgICAgICAgIF0KICAgICAgICAgICAgfSwKICAgICAgICAgICAge2xhYmVsOiAn57q/6Lev5ZCN56ewJywgdHlwZTogJ2lucHV0JywgdmFsdWU6ICdsaW5lTmFtZSd9LAogICAgICAgICAgICB7CiAgICAgICAgICAgICAgbGFiZWw6ICfnur/ot6/kuJPkuJonLCB0eXBlOiAnc2VsZWN0JywgdmFsdWU6ICdsaW5lVHlwZScsIG9wdGlvbnM6IFsKICAgICAgICAgICAgICAgIHtsYWJlbDogIui+k+eUtee6v+i3ryIsIHZhbHVlOiAi6L6T55S157q/6LevIn0sCiAgICAgICAgICAgICAgICB7bGFiZWw6ICLphY3nlLXnur/ot68iLCB2YWx1ZTogIumFjeeUtee6v+i3ryJ9LAogICAgICAgICAgICAgIF0KICAgICAgICAgICAgfSwKICAgICAgICAgICAgLy8gewogICAgICAgICAgICAvLyAgIGxhYmVsOiAn5piv5ZCm5Li75bmy57q/JywKICAgICAgICAgICAgLy8gICB0eXBlOiAnc2VsZWN0JywKICAgICAgICAgICAgLy8gICB2YWx1ZTogJ3Nmemd4JywKICAgICAgICAgICAgLy8gICBvcHRpb25zOiBbe2xhYmVsOiAi5pivIiwgdmFsdWU6ICLmmK8ifSwge2xhYmVsOiAi5ZCmIiwgdmFsdWU6ICLlkKYifSxdCiAgICAgICAgICAgIC8vIH0sCiAgICAgICAgICAgIHsKICAgICAgICAgICAgICBsYWJlbDogJ+eKtuaAgScsCiAgICAgICAgICAgICAgdHlwZTogJ3NlbGVjdCcsCiAgICAgICAgICAgICAgdmFsdWU6ICdsaW5lU3RhdHVzJywKICAgICAgICAgICAgICBvcHRpb25zOiBbe2xhYmVsOiAi5Zyo6L+QIiwgdmFsdWU6ICLlnKjov5AifSwge2xhYmVsOiAi5YGc6L+QIiwgdmFsdWU6ICLlgZzov5AifSxdCiAgICAgICAgICAgIH0sCiAgICAgICAgICBdCiAgICAgICAgfSwKICAgICAgICB0YWJsZUFuZFBhZ2VJbmZvOiB7CiAgICAgICAgICBwYWdlcjogewogICAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgICBzaXplczogWzEwLCAyMCwgNTAsIDEwMF0KICAgICAgICAgIH0sCiAgICAgICAgICBvcHRpb246IHsKICAgICAgICAgICAgY2hlY2tCb3g6IHRydWUsCiAgICAgICAgICAgIHNlcmlhbE51bWJlcjogdHJ1ZQogICAgICAgICAgfSwKICAgICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgICB0YWJsZUhlYWRlcjogWwogICAgICAgICAgICB7cHJvcDogJ2xpbmVOYW1lJywgbGFiZWw6ICfnur/ot6/lkI3np7AnLCBtaW5XaWR0aDogJzEyMCd9LAogICAgICAgICAgICB7cHJvcDogJ2R5ZGonLCBsYWJlbDogJ+eUteWOi+etiee6pycsIG1pbldpZHRoOiAnMTgwJ30sCiAgICAgICAgICAgIHtwcm9wOiAnbGluZVN0YXR1cycsIGxhYmVsOiAn57q/6Lev54q25oCBJywgbWluV2lkdGg6ICcxMjAnfSwKICAgICAgICAgICAge3Byb3A6ICdsaW5lVHlwZScsIGxhYmVsOiAn57q/6Lev57G75Z6LJywgbWluV2lkdGg6ICcyNTAnfSwKICAgICAgICAgICAgLy8ge3Byb3A6ICdzZnpneCcsIGxhYmVsOiAn5piv5ZCm5Li75bmy57q/JywgbWluV2lkdGg6ICcxNDAnfSwKICAgICAgICAgICAge3Byb3A6ICd0b3RhbExlbmd0aCcsIGxhYmVsOiAn57q/6Lev5YWo6ZW/KEtNKScsIG1pbldpZHRoOiAnMTIwJ30sCiAgICAgICAgICAgIHtwcm9wOiAndHlycScsIGxhYmVsOiAn5oqV6L+Q5pel5pyfJywgbWluV2lkdGg6ICcxMjAnfSwKICAgICAgICAgICAgLyp7CiAgICAgICAgICAgICAgZml4ZWQ6ICJyaWdodCIsCiAgICAgICAgICAgICAgcHJvcDogJ29wZXJhdGlvbicsCiAgICAgICAgICAgICAgbGFiZWw6ICfmk43kvZwnLAogICAgICAgICAgICAgIG1pbldpZHRoOiAnMTMwcHgnLAogICAgICAgICAgICAgIHN0eWxlOiB7ZGlzcGxheTogJ2Jsb2NrJ30sCiAgICAgICAgICAgICAgb3BlcmF0aW9uOiBbCiAgICAgICAgICAgICAgICB7bmFtZTogJ+S/ruaUuScsIGNsaWNrRnVuOiB0aGlzLnVwZGF0ZVJvd30sCiAgICAgICAgICAgICAgICB7bmFtZTogJ+ivpuaDhScsIGNsaWNrRnVuOiB0aGlzLmRldGFpbHNJbmZvfQogICAgICAgICAgICAgIF0KICAgICAgICAgICAgfSwqLwogICAgICAgICAgXQogICAgICAgIH0sCiAgICAgICAgLy/orr7lpIflsaXljoZ0YWLpobUKICAgICAgICBzYmxsRGVzY1RhYk5hbWU6ICJzeWpsIiwKICAgICAgICAvL+i9ruaSreWbvueJhwogICAgICAgIGltZ0xpc3Q6IFsKICAgICAgICAgIHsKICAgICAgICAgICAgdXJsOiAnaHR0cHM6Ly9jdWJlLmVsZW1lY2RuLmNvbS82Lzk0LzRkM2VhNTNjMDg0YmFkNjkzMWE1NmQ1MTU4YTQ4anBlZy5qcGVnJwogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgdXJsOiAnaHR0cHM6Ly9jdWJlLmVsZW1lY2RuLmNvbS82Lzk0LzRkM2VhNTNjMDg0YmFkNjkzMWE1NmQ1MTU4YTQ4anBlZy5qcGVnJwogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgdXJsOiAnaHR0cHM6Ly9jdWJlLmVsZW1lY2RuLmNvbS82Lzk0LzRkM2VhNTNjMDg0YmFkNjkzMWE1NmQ1MTU4YTQ4anBlZy5qcGVnJwogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgdXJsOiAnaHR0cHM6Ly9jdWJlLmVsZW1lY2RuLmNvbS82Lzk0LzRkM2VhNTNjMDg0YmFkNjkzMWE1NmQ1MTU4YTQ4anBlZy5qcGVnJwogICAgICAgICAgfQogICAgICAgIF0sCiAgICAgICAgLy/orr7lpIfln7rmnKzkv6Hmga8KICAgICAgICBqYnh4Rm9ybTogewogICAgICAgICAgb2JqSWQ6dW5kZWZpbmVkLAogICAgICAgIH0sCiAgICAgICAgLy/orr7lpIfor6bmg4XpobXlupXpg6jnoa7orqTlj5bmtojmjInpkq7mjqfliLYKICAgICAgICBzYkNvbW1pdERpYWxvZ0NvdHJvbDogdHJ1ZSwKICAgICAgICAvL+W8ueWHuuahhnRhYumhtQogICAgICAgIGFjdGl2ZVRhYk5hbWU6ICJzYkRlc2MiLAogICAgICAgIC8v5Y+Y55S156uZ5bGV56S6CiAgICAgICAgYmR6U2hvd1RhYmxlOiB0cnVlLAogICAgICAgIC8v6Ze06ZqU5bGV56S6CiAgICAgICAgamdTaG93VGFibGU6IGZhbHNlLAogICAgICAgIC8v6K6+5aSH5bGV56S6CiAgICAgICAgc2JTaG93VGFibGU6IGZhbHNlLAogICAgICAgIC8v6K6+5aSH5by55Ye65qGGCiAgICAgICAgZGlhbG9nRm9ybVZpc2libGU6IGZhbHNlLAogICAgICAgIC8v5Y+Y55S156uZ5re75Yqg5oyJ6ZKu5by55Ye65qGGCiAgICAgICAgYmR6RGlhbG9nRm9ybVZpc2libGU6IGZhbHNlLAogICAgICAgIC8v6Ze06ZqU5re75Yqg5oyJ6ZKu5by55Ye65qGGCiAgICAgICAgamdEaWFsb2dGb3JtVmlzaWJsZTogZmFsc2UsCiAgICAgICAgLy/lvLnlh7rmoYbooajljZUKICAgICAgICBmb3JtOiB7fSwKICAgICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgICBzZWxlY3RSb3dzOiBbXSwKICAgICAgICAvL+WIoOmZpOaYr+WQpuWPr+eUqAogICAgICAgIG11bHRpcGxlU2Vuc29yOiB0cnVlLAogICAgICAgIC8v5p+l6K+i5Y+C5pWwCiAgICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICBkeWRqYm06ICcnLAogICAgICAgICAgbGluZU5hbWU6ICcnLAogICAgICAgICAgbGluZVR5cGU6ICcnLAogICAgICAgICAgbGluZVN0YXR1czogJycsCiAgICAgICAgfSwKICAgICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICAgIHRpdGxlOicnLAogICAgICAgIHJ1bGVzOnsKICAgICAgICAgIHhsYm06W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+i+k+WFpee6v+i3r+e8lueggScsdHJpZ2dlcjonYmx1cid9XSwKICAgICAgICAgIHNzYm1ibTpbe3JlcXVpcmVkOnRydWUsbWVzc2FnZTon6K+36YCJ5oup5omA5bGe6YOo6ZeoJyx0cmlnZ2VyOidjaGFuZ2UnfV0sCiAgICAgICAgICBsaW5lTmFtZTpbe3JlcXVpcmVkOnRydWUsbWVzc2FnZTon6K+36L6T5YWl57q/6Lev5ZCN56ewJyx0cmlnZ2VyOidibHVyJ31dLAogICAgICAgICAgZHlkamJtOlt7cmVxdWlyZWQ6dHJ1ZSxtZXNzYWdlOifor7fovpPlhaXnlLXljovnrYnnuqcnLHRyaWdnZXI6J2NoYW5nZSd9XSwKICAgICAgICAgIHNmemd4Olt7cmVxdWlyZWQ6dHJ1ZSxtZXNzYWdlOifor7fpgInmi6nmmK/lkKbkuLvlubLnur8nLHRyaWdnZXI6J2NoYW5nZSd9XSwKICAgICAgICAgIC8vIGxpbmVTdGF0dXM6W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+mAieaLqee6v+i3r+eKtuaAgScsdHJpZ2dlcjonY2hhbmdlJ31dLAoKICAgICAgICB9LAogICAgICAgIC8v57uE57uH57uT5p6E5LiL5ouJ5pWw5o2uCiAgICAgICAgT3JnYW5pemF0aW9uU2VsZWN0ZWRMaXN0OiBbXSwKICAgICAgICAvL+e6v+i3r+exu+WeiwogICAgICAgIGxpbmVUeXBlT3B0aW9uczpbCiAgICAgICAgICB7bGFiZWw6J+i+k+eUtee6v+i3rycsdmFsdWU6J+i+k+eUtee6v+i3ryd9LAogICAgICAgICAge2xhYmVsOifphY3nlLXnur/ot68nLHZhbHVlOifphY3nlLXnur/ot68nfQogICAgICAgIF0sCiAgICAgICAgLy/nur/ot6/nirbmgIEKICAgICAgICB4bHp0T3B0aW9uczpbCiAgICAgICAgICB7bGFiZWw6J+WcqOi/kCcsdmFsdWU6J+WcqOi/kCd9LAogICAgICAgICAge2xhYmVsOiflgZzov5AnLHZhbHVlOiflgZzov5AnfQogICAgICAgIF0sCgoKCgogICAgICB9OwogICAgfSwKICAgIHdhdGNoOiB7fSwKICAgIGNyZWF0ZWQoKSB7CiAgICAgIC8v5Yid5aeL5YyW5Yqg6L295pe25Yqg6L295omA5pyJ5Y+Y55S156uZ5L+h5oGvCiAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICB0aGlzLmdldE9yZ2FuaXphdGlvblNlbGVjdGVkKCk7CiAgICAgLy8gdGhpcy5pbml0KCk7CiAgICB9LAogICAgbWV0aG9kczogewogICAgICAvL+iOt+WPlue7hOe7h+e7k+aehOS4i+aLieahhuaVsOaNrgogICAgICBnZXRPcmdhbml6YXRpb25TZWxlY3RlZCgpIHsKICAgICAgICBsZXQgcGFyZW50SWQgPSAnMTAwMSc7CiAgICAgICAgZ2V0T3JnYW5pemF0aW9uU2VsZWN0ZWQoe3BhcmVudElkOiBwYXJlbnRJZH0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgIHRoaXMuT3JnYW5pemF0aW9uU2VsZWN0ZWRMaXN0ID0gcmVzLmRhdGEKICAgICAgICAgIGNvbnNvbGUubG9nKHRoaXMuT3JnYW5pemF0aW9uU2VsZWN0ZWRMaXN0KQogICAgICAgIH0pCiAgICAgIH0sCgogICAgICBhZGRMaW5lSW5mbzogZnVuY3Rpb24oKSB7CiAgICAgICAgdGhpcy4kcmVmc1snamJ4eEZvcm0nXS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgICBzYXZlT3JVcGRhdGV4bCh0aGlzLmpieHhGb3JtKS50aGVuKHJlcyA9PgogICAgICAgICAgICB7CiAgICAgICAgICAgICAgaWYocmVzLmNvZGU9PScwMDAwJyl7CiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKnyIpOwogICAgICAgICAgICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlOwogICAgICAgICAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgICAgICAgICAgICByZXR1cm4gIDsKICAgICAgICAgICAgICB9ZWxzZXsKICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi5pON5L2c5aSx6LSl77yBIik7CiAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICAgICAgICB2YXIgaXNFcnJvciA9IGRvY3VtZW50LmdldEVsZW1lbnRzQnlDbGFzc05hbWUoImlzLWVycm9yIik7CiAgICAgICAgICAgICAgaWYgKGlzRXJyb3JbMF0ucXVlcnlTZWxlY3RvcignaW5wdXQnKSkgewogICAgICAgICAgICAgICAgaXNFcnJvclswXS5xdWVyeVNlbGVjdG9yKCdpbnB1dCcpLmZvY3VzKCk7CiAgICAgICAgICAgICAgfSBlbHNlIGlmIChpc0Vycm9yWzBdLnF1ZXJ5U2VsZWN0b3IoJ3RleHRhcmVhJykpIHsKICAgICAgICAgICAgICAgIGlzRXJyb3JbMF0ucXVlcnlTZWxlY3RvcigndGV4dGFyZWEnKS5mb2N1cygpOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSwgMSkKICAgICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9LAogICAgICBnZXREYXRhOmZ1bmN0aW9uKHBhcmFtcyl7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcyA9IHsuLi50aGlzLnF1ZXJ5UGFyYW1zLC4uLnBhcmFtc30KICAgICAgICBjb25zdCBwYXJhbSA9IHsuLi50aGlzLnF1ZXJ5UGFyYW1zLCAuLi5wYXJhbXN9CiAgICAgICAgZ2V0TGlzdHhsKHBhcmFtKS50aGVuKHJlcz0+ewogICAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnRhYmxlRGF0YT1yZXMuZGF0YS5yZWNvcmRzOwogICAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnRvdGFsPXJlcy5kYXRhLnRvdGFsOwogICAgICAgICAgY29uc29sZS5sb2coInJlcy5kYXRhLnJlY29yZHMiLHJlcy5kYXRhLnJlY29yZHMpCiAgICAgICAgfSk7CiAgICAgIH0sCgogICAgICAvKioKICAgICAgICog6KGo5qC85aSa6YCJ5qGGCiAgICAgICAqLwogICAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5vYmpJZCkKICAgICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDE7CiAgICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOwogICAgICB9LAogICAgICAvKioKICAgICAgICog5Yig6ZmkCiAgICAgICAqLwogICAgICBkZWxldGVJbmZvKCkgewogICAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggIT0gMCkgewogICAgICAgICAgdGhpcy4kY29uZmlybSgn5q2k5pON5L2c5bCG5rC45LmF5Yig6Zmk6K+l5pWw5o2uLCDmmK/lkKbnu6fnu60/JywgJ+aPkOekuicsIHsKICAgICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICAgICAgeGxyZW1vdmUodGhpcy5pZHMpLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICfliKDpmaTmiJDlip8hJwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICB9KQogICAgICAgICAgfSkKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICdpbmZvJywKICAgICAgICAgICAgbWVzc2FnZTogJ+ivt+mAieaLqeiHs+WwkeS4gOadoeaVsOaNriEnCiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0sCgogICAgICB1cGRhdGVSb3c6ZnVuY3Rpb24ocm93KXsKICAgICAgICB0aGlzLnRpdGxlPSLkv67mlLnnur/ot6/kv6Hmga8iOwogICAgICAgIHRoaXMuZGlhbG9nRm9ybVZpc2libGUgPSB0cnVlCiAgICAgICAgdGhpcy5zaG93PWZhbHNlOwogICAgICAgIHRoaXMuamJ4eEZvcm09ey4uLnJvd307CiAgICAgIH0sCiAgICAgIGRldGFpbHNJbmZvOmZ1bmN0aW9uKHJvdyl7CiAgICAgICAgdGhpcy50aXRsZT0i57q/6Lev6K+m5oOFIjsKICAgICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZTsKICAgICAgICB0aGlzLnNob3c9dHJ1ZTsKICAgICAgICB0aGlzLmpieHhGb3JtPXsuLi5yb3d9OwogICAgICB9LAoKICAgICAgLy/orr7lpIfmt7vliqDmjInpkq4KICAgICAgc2JBZGRTZW5zb3JCdXR0b24oKSB7CiAgICAgICAgdGhpcy50aXRsZT0i5paw5aKe57q/6Lev5L+h5oGvIjsKICAgICAgICB0aGlzLnNob3c9ZmFsc2U7CiAgICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IHRydWUKCiAgICAgIH0sCiAgICAgIC8v5Y+Y55S156uZ5re75Yqg5oyJ6ZKuCiAgICAgIGJkekFkZFNlbnNvckJ1dHRvbigpIHsKICAgICAgICB0aGlzLmJkekRpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZQogICAgICB9LAogICAgICAvL+mXtOmalOa3u+WKoOaMiemSrgogICAgICBqZ0FkZGpnQnV0dG9uKCkgewogICAgICAgIHRoaXMuamdEaWFsb2dGb3JtVmlzaWJsZSA9IHRydWUKICAgICAgfSwKCiAgICAgIC8v5q+P6aG15bGV56S65pWw6YeP54K55Ye75LqL5Lu2CiAgICAgIGhhbmRsZVNpemVDaGFuZ2UoKSB7CgogICAgICB9LAogICAgICAvL+mhteeggeaUueWPmOS6i+S7tgogICAgICBoYW5kbGVDdXJyZW50Q2hhbmdlKCkgewoKICAgICAgfSwKICAgICAgZmlsdGVyUmVzZXQoKXsKCiAgICAgIH0sCgoKICAgICAgcmVzZXRGb3JtKCl7CiAgICAgICAgdGhpcy5qYnh4Rm9ybSA9IHt9OwogICAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICAgIHRoaXMuJHJlZnNbJ2pieHhGb3JtJ10uY2xlYXJWYWxpZGF0ZSgpOwogICAgICAgIH0pOwogICAgICAgIHRoaXMuZGlhbG9nRm9ybVZpc2libGU9ZmFsc2U7CgogICAgICB9LAogICAgICAvL+e8uumZt+agh+WHhuW6k+aWsOWinuWujOaIkAogICAgICBxeGNvbW1pdCgpIHsKICAgICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gZmFsc2U7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLmlrDlop7miJDlip8iKQogICAgICB9LAoKCiAgICAgIGluaXQoKXsKICAgICAgICBnZXREaWN0VHlwZURhdGEoJ2xpbmVUeXBlJykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICB0aGlzLmxpbmVUeXBlT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGEKICAgICAgICB9KTsKICAgICAgICBnZXREaWN0VHlwZURhdGEoJ3hsenQnKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgIHRoaXMueGx6dE9wdGlvbnMgPSByZXNwb25zZS5kYXRhCiAgICAgICAgfSk7CiAgICAgIH0sCgogICAgfQogIH07Cg=="}, {"version": 3, "sources": ["sdxl.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAySA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "sdxl.vue", "sourceRoot": "src/views/dagangOilfield/dwzygl/sdsbgl", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--变电站展示开始-->\n    <el-filter\n      ref=\"filter1\"\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 120, itemWidth: 230 }\"\n      @handleReset=\"filterReset\"\n    />\n    <el-white class=\"button-group\">\n      <div class=\"button_btn\">\n        <el-button type=\"primary\" v-hasPermi=\"['xltz:button:add']\" icon=\"el-icon-plus\" @click=\"sbAddSensorButton\">新增</el-button>\n        <el-button type=\"danger\" v-hasPermi=\"['xltz:button:delete']\" icon=\"el-icon-delete\" @click=\"deleteInfo\">删除</el-button>\n      </div>\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"handleSelectionChange\"\n        height=\"500\"\n      >\n      <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\" width=\"160\"\n                       :resizable=\"false\">\n        <template slot-scope=\"scope\">\n          <el-button @click=\"updatebdz(scope.row)\" v-hasPermi=\"['xltz:button:update']\" type=\"text\" size=\"small\" title=\"修改\"  class='el-icon-edit'></el-button>\n          <el-button @click=\"bdzDetails(scope.row)\" type=\"text\" size=\"small\" title=\"详情\" class=\"el-icon-view\"></el-button>\n        </template>\n      </el-table-column>\n      </comp-table>\n    </el-white>\n\n    <!--线路详情所用弹出框开始-->\n    <el-dialog :title=\"title\" :visible.sync=\"dialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"60%\" @close=\"resetForm\">\n      <el-form :model=\"jbxxForm\" ref=\"jbxxForm\" :disabled=\"show\" :rules=\"rules\" label-width=\"130px\">\n        <div class=\"divHeader\">\n          <span>基本信息</span>\n        </div>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路编码\" prop=\"xlbm\">\n              <el-input v-model=\"jbxxForm.xlbm\" placeholder=\"请输入线路编码\"></el-input>\n            </el-form-item>\n          </el-col>\n<!--          <el-col :span=\"8\">-->\n<!--            <el-form-item label=\"所属公司\" prop=\"ssbmbm\">-->\n<!--              <el-select v-model=\"jbxxForm.ssbmbm\" placeholder=\"请选择所属公司\" clearable >-->\n<!--                <el-option v-for=\"item in OrganizationSelectedList\"-->\n<!--                           :key=\"item.value\"-->\n<!--                           :label=\"item.label\"-->\n<!--                           :value=\"String(item.value)\">-->\n<!--                </el-option>-->\n<!--              </el-select>-->\n<!--            </el-form-item>-->\n<!--          </el-col>-->\n<!--          <el-col :span=\"8\">-->\n<!--            <el-form-item label=\"线路id\" prop=\"xlid\">-->\n<!--              <el-input v-model=\"jbxxForm.xlid\" placeholder=\"\"></el-input>-->\n<!--            </el-form-item>-->\n<!--          </el-col>-->\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路名称\" prop=\"lineName\">\n              <el-input v-model=\"jbxxForm.lineName\" placeholder=\"请输入线路名称\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路专业\" prop=\"lineType\">\n              <el-select v-model=\"jbxxForm.lineType\" placeholder=\"请选择线路专业\" clearable>\n                <el-option v-for=\"item in lineTypeOptions\"\n                           :key=\"item.value\"\n                           :label=\"item.label\"\n                           :value=\"String(item.value)\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电压等级\" prop=\"dydjbm\">\n              <el-select v-model=\"jbxxForm.dydjbm\" placeholder=\"请选择电压等级\" clearable>\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路全长\" prop=\"totalLength\">\n              <el-input v-model=\"jbxxForm.totalLength\" placeholder=\"请输入线路全长\" ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路状态\" prop=\"lineStatus\">\n              <el-select v-model=\"jbxxForm.lineStatus\" placeholder=\"请选择线路状态\" >\n                <el-option v-for=\"item in xlztOptions\"\n                           :key=\"item.value\"\n                           :label=\"item.label\"\n                           :value=\"String(item.value)\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路性质\" prop=\"xlxz\">\n              <el-input v-model=\"jbxxForm.xlxz\" placeholder=\"请选择线路性质\" clearable></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路类型\" prop=\"xllx\">\n              <el-select v-model=\"jbxxForm.xllx\" placeholder=\"请选择线路类型\" clearable>\n                <el-option label=\"架空线路\" value=\"架空线路\"></el-option>\n                <el-option label=\"混合线路\" value=\"混合线路\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路起点\" prop=\"startLine\">\n              <el-input v-model=\"jbxxForm.startLine\" placeholder=\"请输入线路起点\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路终点\" prop=\"stopLine\">\n              <el-input v-model=\"jbxxForm.stopLine\" placeholder=\"请输入线路终点\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"同杆并架长度\" prop=\"tgbjcd\">\n              <el-input v-model=\"jbxxForm.tgbjcd\" placeholder=\"请输入同杆并架长度\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路起始杆塔号\" prop=\"startTowerNum\">\n              <el-input v-model=\"jbxxForm.startTowerNum\" placeholder=\"请选择线路起始杆塔号\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路终止杆塔号\" prop=\"stopTowerNum\">\n              <el-input v-model=\"jbxxForm.stopTowerNum\" placeholder=\"请选择线路终止杆塔号\"></el-input>\n            </el-form-item>\n          </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"是否有光缆\" prop=\"sfygl\">\n                        <el-select v-model=\"jbxxForm.sfygl\" placeholder=\"请选择是否有光缆\" clearable>\n                          <el-option label=\"是\" value=\"是\"></el-option>\n                          <el-option label=\"否\" value=\"否\"></el-option>\n                        </el-select>\n                      </el-form-item>\n                    </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"光缆类别\" prop=\"gllb\">\n              <el-input v-model=\"jbxxForm.gllb\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"投运日期\" class=\"add_sy_tyrq\" prop=\"tyrq\">\n              <el-date-picker\n                v-model=\"jbxxForm.tyrq\"\n                type=\"date\"\n                placeholder=\"选择投运日期\" format=\"yyyy-MM-dd\"\n                value-format=\"yyyy-MM-dd\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"设计单位\" class=\"add_sy_tyrq\" prop=\"sjdw\">\n              <el-select v-model=\"jbxxForm.sjdw\" placeholder=\"请选择设计单位\"></el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"施工单位\" prop=\"sgdw\">\n              <el-select v-model=\"jbxxForm.sgdw\" placeholder=\"请选择施工单位\"></el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电缆长度\" prop=\"dlcd\">\n              <el-input v-model=\"jbxxForm.dlcd\" placeholder=\"请输入电缆长度\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"检修单位\" prop=\"jxdw\">\n              <el-select v-model=\"jbxxForm.jxdw\" placeholder=\"请选择检修单位\"></el-select>\n            </el-form-item>\n          </el-col>\n\n<!--          <el-col :span=\"8\">-->\n<!--            <el-form-item label=\"杆塔基数\" prop=\"gtjs\">-->\n<!--              <el-input v-model=\"jbxxForm.gtjs\" placeholder=\"请输入杆塔基数\"></el-input>-->\n<!--            </el-form-item>-->\n<!--          </el-col>-->\n<!--          <el-col :span=\"8\">-->\n<!--            <el-form-item label=\"架空长度\" prop=\"jkcd\">-->\n<!--              <el-input v-model=\"jbxxForm.jkcd\" placeholder=\"请输入架空长度\"></el-input>-->\n<!--            </el-form-item>-->\n<!--          </el-col>-->\n<!--          <el-col :span=\"8\">-->\n<!--            <el-form-item label=\"是否主干线\" prop=\"sfzgx\">-->\n<!--              <el-select v-model=\"jbxxForm.sfzgx\" placeholder=\"请选择是否主干线\" clearable>-->\n<!--                <el-option label=\"是\" value=\"是\"></el-option>-->\n<!--                <el-option label=\"否\" value=\"否\"></el-option>-->\n<!--              </el-select>-->\n<!--            </el-form-item>-->\n<!--          </el-col>-->\n<!--          <el-col :span=\"8\">-->\n<!--            <el-form-item label=\"所属主干线\" prop=\"sszgx\">-->\n<!--              <el-input v-model=\"jbxxForm.sszgx\" placeholder=\"请输入所属主干线\" clearable></el-input>-->\n<!--            </el-form-item>-->\n<!--          </el-col>-->\n          <el-col :span=\"8\">\n            <el-form-item label=\"负责人\" prop=\"fzr\">\n              <el-select v-model=\"jbxxForm.fzr\" placeholder=\"请选择负责人\"></el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <div class=\"divHeader\">\n          <span>详细信息</span>\n        </div>\n        <el-row :gutter=\"20\">\n<!--          <el-col :span=\"8\">-->\n<!--            <el-form-item label=\"资产变动方式\" prop=\"zcbdfs\">-->\n<!--              <el-select v-model=\"jbxxForm.zcbdfs\" placeholder=\"\"></el-select>-->\n<!--            </el-form-item>-->\n<!--          </el-col>-->\n\n<!--          <el-col :span=\"8\">-->\n<!--            <el-form-item label=\"资产属性编码\" prop=\"zcsxbm\">-->\n<!--              <el-select v-model=\"jbxxForm.zcsxbm\" placeholder=\"请选择资产属性\"></el-select>-->\n<!--            </el-form-item>-->\n<!--          </el-col>-->\n<!--          <el-col :span=\"8\">-->\n<!--            <el-form-item label=\"建设日期\" class=\"add_sy_tyrq\" prop=\"jsrq\">-->\n<!--              <el-date-picker-->\n<!--                v-model=\"jbxxForm.jsrq\"-->\n<!--                type=\"date\"-->\n<!--                placeholder=\"选择日期\" format=\"yyyy-MM-dd\"-->\n<!--                value-format=\"yyyy-MM-dd\">-->\n<!--              </el-date-picker>-->\n<!--            </el-form-item>-->\n<!--          </el-col>-->\n\n<!--          <el-col :span=\"8\">-->\n<!--            <el-form-item label=\"允许最大电流\" prop=\"yxzddl\">-->\n<!--              <el-select v-model=\"jbxxForm.yxzddl\" placeholder=\"请选择允许最大电流\"></el-select>-->\n<!--            </el-form-item>-->\n<!--          </el-col>-->\n<!--          <el-col :span=\"8\">-->\n<!--            <el-form-item label=\"起点侧断路器编号\" class=\"add_sy_tyrq\">-->\n<!--              <el-select v-model=\"jbxxForm.qddlqbh\" placeholder=\"请选择起点侧断路器编号\"></el-select>-->\n<!--            </el-form-item>-->\n<!--          </el-col>-->\n<!--          <el-col :span=\"8\">-->\n<!--            <el-form-item label=\"终点侧断路器编号\" prop=\"zddlqbh\">-->\n<!--              <el-select v-model=\"jbxxForm.zddlqbh\" placeholder=\"请选择终点侧断路器编号\"></el-select>-->\n<!--            </el-form-item>-->\n<!--          </el-col>-->\n          <el-col :span=\"8\">\n            <el-form-item label=\"资产性质\" class=\"add_sy_tyrq\" prop=\"zcxz\">\n              <el-select v-model=\"jbxxForm.zcxz\" placeholder=\"请输入资产性质\"></el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否为联络线路\" prop=\"sfllxl\">\n              <el-select v-model=\"jbxxForm.sfllxl\" placeholder=\"请选择是否为联络线路\" clearable>\n                <el-option label=\"是\" value=\"是\"></el-option>\n                <el-option label=\"否\" value=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"联络类型\" prop=\"lllx\">\n              <el-select v-model=\"jbxxForm.lllx\" placeholder=\"请选择联络类型\"></el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"运行班组\" prop=\"yxbz\">\n              <el-select v-model=\"jbxxForm.yxbz\" placeholder=\"请选择运行班组\"></el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\" prop=\"bz\">\n              <el-input v-model=\"jbxxForm.bz\" type=\"textarea\" rows=\"2\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" v-show=\"!show\" class=\"dialog-footer\">\n        <el-button @click=\"resetForm\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"addLineInfo\" >确 定</el-button>\n      </div>\n    </el-dialog>\n    <!--线路详情所用弹出框结束-->\n  </div>\n\n</template>\n\n<script>\nimport {\n  saveOrUpdatexl,getListxl,xlremove\n} from '@/api/dagangOilfield/asset/sdxl'\nimport { getOrganizationSelected } from '@/api/dagangOilfield/asset/bdsbtz'\nimport {getDictTypeData} from \"@/api/system/dict/data\";\n  export default {\n    name: \"qxbzk\",\n    data() {\n      return {\n        options: [{\n          value: '110',\n          label: '110kV'\n        }, {\n          value: '35',\n          label: '35kV'\n        }, {\n          value: '10',\n          label: '10kV'\n        }, {\n          value: '6',\n          label: '6kV'\n        }],\n        show:false,\n        filterInfo: {\n          data: {\n            dydjbm: '',\n            lineName: '',\n            lineType: '',\n            // sfzgx: '',\n            lineStatus: '',\n          },\n          fieldList: [\n            {\n              label: '电压等级', type: 'select', value: 'dydjbm', options: [\n                {label: \"110kV\", value: \"110\"},\n                {label: \"35kV\", value: \"35\"},\n                {label: \"10kV\", value: \"10\"},\n                {label: \"6kV\", value: \"6\"},\n              ]\n            },\n            {label: '线路名称', type: 'input', value: 'lineName'},\n            {\n              label: '线路专业', type: 'select', value: 'lineType', options: [\n                {label: \"输电线路\", value: \"输电线路\"},\n                {label: \"配电线路\", value: \"配电线路\"},\n              ]\n            },\n            // {\n            //   label: '是否主干线',\n            //   type: 'select',\n            //   value: 'sfzgx',\n            //   options: [{label: \"是\", value: \"是\"}, {label: \"否\", value: \"否\"},]\n            // },\n            {\n              label: '状态',\n              type: 'select',\n              value: 'lineStatus',\n              options: [{label: \"在运\", value: \"在运\"}, {label: \"停运\", value: \"停运\"},]\n            },\n          ]\n        },\n        tableAndPageInfo: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n            {prop: 'lineName', label: '线路名称', minWidth: '120'},\n            {prop: 'dydj', label: '电压等级', minWidth: '180'},\n            {prop: 'lineStatus', label: '线路状态', minWidth: '120'},\n            {prop: 'lineType', label: '线路类型', minWidth: '250'},\n            // {prop: 'sfzgx', label: '是否主干线', minWidth: '140'},\n            {prop: 'totalLength', label: '线路全长(KM)', minWidth: '120'},\n            {prop: 'tyrq', label: '投运日期', minWidth: '120'},\n            /*{\n              fixed: \"right\",\n              prop: 'operation',\n              label: '操作',\n              minWidth: '130px',\n              style: {display: 'block'},\n              operation: [\n                {name: '修改', clickFun: this.updateRow},\n                {name: '详情', clickFun: this.detailsInfo}\n              ]\n            },*/\n          ]\n        },\n        //设备履历tab页\n        sbllDescTabName: \"syjl\",\n        //轮播图片\n        imgList: [\n          {\n            url: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg'\n          },\n          {\n            url: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg'\n          },\n          {\n            url: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg'\n          },\n          {\n            url: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg'\n          }\n        ],\n        //设备基本信息\n        jbxxForm: {\n          objId:undefined,\n        },\n        //设备详情页底部确认取消按钮控制\n        sbCommitDialogCotrol: true,\n        //弹出框tab页\n        activeTabName: \"sbDesc\",\n        //变电站展示\n        bdzShowTable: true,\n        //间隔展示\n        jgShowTable: false,\n        //设备展示\n        sbShowTable: false,\n        //设备弹出框\n        dialogFormVisible: false,\n        //变电站添加按钮弹出框\n        bdzDialogFormVisible: false,\n        //间隔添加按钮弹出框\n        jgDialogFormVisible: false,\n        //弹出框表单\n        form: {},\n        loading: false,\n        selectRows: [],\n        //删除是否可用\n        multipleSensor: true,\n        //查询参数\n        queryParams: {\n          pageNum: 1,\n          pageSize: 10,\n          dydjbm: '',\n          lineName: '',\n          lineType: '',\n          lineStatus: '',\n        },\n        showSearch: true,\n        title:'',\n        rules:{\n          xlbm:[{required:true,message:'请输入线路编码',trigger:'blur'}],\n          ssbmbm:[{required:true,message:'请选择所属部门',trigger:'change'}],\n          lineName:[{required:true,message:'请输入线路名称',trigger:'blur'}],\n          dydjbm:[{required:true,message:'请输入电压等级',trigger:'change'}],\n          sfzgx:[{required:true,message:'请选择是否主干线',trigger:'change'}],\n          // lineStatus:[{required:true,message:'请选择线路状态',trigger:'change'}],\n\n        },\n        //组织结构下拉数据\n        OrganizationSelectedList: [],\n        //线路类型\n        lineTypeOptions:[\n          {label:'输电线路',value:'输电线路'},\n          {label:'配电线路',value:'配电线路'}\n        ],\n        //线路状态\n        xlztOptions:[\n          {label:'在运',value:'在运'},\n          {label:'停运',value:'停运'}\n        ],\n\n\n\n\n      };\n    },\n    watch: {},\n    created() {\n      //初始化加载时加载所有变电站信息\n      this.getData();\n      this.getOrganizationSelected();\n     // this.init();\n    },\n    methods: {\n      //获取组织结构下拉框数据\n      getOrganizationSelected() {\n        let parentId = '1001';\n        getOrganizationSelected({parentId: parentId}).then(res => {\n          this.OrganizationSelectedList = res.data\n          console.log(this.OrganizationSelectedList)\n        })\n      },\n\n      addLineInfo: function() {\n        this.$refs['jbxxForm'].validate((valid) => {\n          if (valid) {\n            saveOrUpdatexl(this.jbxxForm).then(res =>\n            {\n              if(res.code=='0000'){\n                this.$message.success(\"操作成功\");\n                this.dialogFormVisible = false;\n                this.getData();\n                return  ;\n              }else{\n                this.$message.warning(\"操作失败！\");\n              }\n\n            });\n          } else {\n            setTimeout(() => {\n              var isError = document.getElementsByClassName(\"is-error\");\n              if (isError[0].querySelector('input')) {\n                isError[0].querySelector('input').focus();\n              } else if (isError[0].querySelector('textarea')) {\n                isError[0].querySelector('textarea').focus();\n              }\n            }, 1)\n            return false;\n          }\n        });\n      },\n      getData:function(params){\n        this.queryParams = {...this.queryParams,...params}\n        const param = {...this.queryParams, ...params}\n        getListxl(param).then(res=>{\n          this.tableAndPageInfo.tableData=res.data.records;\n          this.tableAndPageInfo.pager.total=res.data.total;\n          console.log(\"res.data.records\",res.data.records)\n        });\n      },\n\n      /**\n       * 表格多选框\n       */\n      handleSelectionChange(selection) {\n        this.ids = selection.map(item => item.objId)\n        this.single = selection.length !== 1;\n        this.multiple = !selection.length;\n      },\n      /**\n       * 删除\n       */\n      deleteInfo() {\n        if (this.ids.length != 0) {\n          this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }).then(() => {\n            xlremove(this.ids).then(res => {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              });\n              this.getData();\n            })\n          })\n        } else {\n          this.$message({\n            type: 'info',\n            message: '请选择至少一条数据!'\n          });\n        }\n      },\n\n      updateRow:function(row){\n        this.title=\"修改线路信息\";\n        this.dialogFormVisible = true\n        this.show=false;\n        this.jbxxForm={...row};\n      },\n      detailsInfo:function(row){\n        this.title=\"线路详情\";\n        this.dialogFormVisible = true;\n        this.show=true;\n        this.jbxxForm={...row};\n      },\n\n      //设备添加按钮\n      sbAddSensorButton() {\n        this.title=\"新增线路信息\";\n        this.show=false;\n        this.dialogFormVisible = true\n\n      },\n      //变电站添加按钮\n      bdzAddSensorButton() {\n        this.bdzDialogFormVisible = true\n      },\n      //间隔添加按钮\n      jgAddjgButton() {\n        this.jgDialogFormVisible = true\n      },\n\n      //每页展示数量点击事件\n      handleSizeChange() {\n\n      },\n      //页码改变事件\n      handleCurrentChange() {\n\n      },\n      filterReset(){\n\n      },\n\n\n      resetForm(){\n        this.jbxxForm = {};\n        this.$nextTick(function () {\n          this.$refs['jbxxForm'].clearValidate();\n        });\n        this.dialogFormVisible=false;\n\n      },\n      //缺陷标准库新增完成\n      qxcommit() {\n        this.dialogFormVisible = false;\n        this.$message.success(\"新增成功\")\n      },\n\n\n      init(){\n        getDictTypeData('lineType').then(response => {\n          this.lineTypeOptions = response.data\n        });\n        getDictTypeData('xlzt').then(response => {\n          this.xlztOptions = response.data\n        });\n      },\n\n    }\n  };\n</script>\n\n<style lang='scss' scoped>\n  .head-container {\n    margin: 0 auto;\n    width: 98%;\n    height: calc(100vh - 225px);\n    overflow: auto;\n  }\n\n  .box-card {\n    margin-bottom: 15px;\n\n    .el-card__header {\n      background-color: rgb(235, 245, 255) !important;\n    }\n  }\n\n  .box-cardList {\n    height: 70%;\n  }\n\n  .item {\n    width: 200px;\n    float: left;\n  }\n\n  #main_container_dj {\n    height: calc(100vh - 84px);\n  }\n\n  .aside_height {\n    height: 96%;\n  }\n\n  .defect .el-form-item:nth-child(odd) {\n    margin-right: 70px;\n  }\n\n\n\n  .el-carousel__item h3 {\n    color: #475669;\n    font-size: 14px;\n    opacity: 0.75;\n    line-height: 150px;\n\n  }\n\n  .el-carousel__item:nth-child(2n) {\n    background-color: #99a9bf;\n  }\n\n  .el-carousel__item:nth-child(2n+1) {\n    background-color: #d3dce6;\n  }\n\n\n  /*弹出框内宽度设置*/\n  /*/deep/ .el-input--medium .el-input__inner {*/\n  /*  width: 200px;*/\n  /*}*/\n  .el-select {\n    width: 100%;\n  }\n\n  .el-date-editor{\n    width: 100%;\n  }\n\n  .divHeader {\n    width: 100%;\n    height:30px;\n    background-color: #dddddd;\n    margin-bottom: 10px;\n    text-align: left;\n    line-height: 30px;\n    font-weight: bold;\n  }\n</style>\n"]}]}