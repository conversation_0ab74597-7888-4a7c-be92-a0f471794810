{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\pddzcz\\components\\pdczp_cx.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\pddzcz\\components\\pdczp_cx.vue", "mtime": 1748604763458}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsKaW1wb3J0IHsgZ2V0UGRzT3B0aW9uc0RhdGFMaXN0IH0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYXNzZXQvcGRzZ2wiOwppbXBvcnQgeyBnZXRDenBteExpc3QsIGdldExpc3QsIHJlbW92ZSB9IGZyb20gIkAvYXBpL3l4Z2wvcGR5eGdsL3BkZHpjenAiOwppbXBvcnQgRWxJbWFnZVZpZXdlciBmcm9tICJlbGVtZW50LXVpL3BhY2thZ2VzL2ltYWdlL3NyYy9pbWFnZS12aWV3ZXIiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogImN6cF9jeCIsCiAgY29tcG9uZW50czogeyBFbEltYWdlVmlld2VyIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHBkc09wdGlvbnNEYXRhTGlzdDogW10sCiAgICAgIGlzRGlzYWJsZWRCajogdHJ1ZSwKICAgICAgc3RhdHVzT3B0aW9uczogWwogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAiMCIsCiAgICAgICAgICBsYWJlbDogIuaTjeS9nOelqOWhq+aKpSIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAiMSIsCiAgICAgICAgICBsYWJlbDogIuePree7hOWuoeaguCIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAiMiIsCiAgICAgICAgICBsYWJlbDogIuWIhuWFrOWPuOWuoeaguCIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAiMyIsCiAgICAgICAgICBsYWJlbDogIuaTjeS9nOelqOWKnue7kyIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAiNCIsCiAgICAgICAgICBsYWJlbDogIue7k+adnyIKICAgICAgICB9CiAgICAgIF0sCiAgICAgIGJ1dHRvbk5hbWVTaG93OiBmYWxzZSwKICAgICAgYnV0dG9uTmFtZTogIiIsCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgZmdzOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWNleS9jeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJzZWxlY3QiIH1dLAogICAgICAgIHBkem1jOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWNleS9jeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJzZWxlY3QiIH1dLAogICAgICAgIGZscjogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlj5Hku6TkurrkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9XSwKICAgICAgICBzbHI6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5Y+X5Luk5Lq65LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfV0sCiAgICAgICAgZmxzajogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWPkeS7pOaXtumXtOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0KICAgICAgICBdLAogICAgICAgIGN6cnc6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmk43kvZzku7vliqHkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9CiAgICAgICAgXSwKICAgICAgICBjenI6IFt7IG1lc3NhZ2U6ICLmk43kvZzkurrkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfV0sCiAgICAgICAgamhyOiBbeyBtZXNzYWdlOiAi55uR5oqk5Lq65LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH1dLAogICAgICAgIGtzc2o6IFt7IG1lc3NhZ2U6ICLmk43kvZzlvIDlp4vml7bpl7TkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9XSwKICAgICAgICBqc3NqOiBbeyBtZXNzYWdlOiAi5pON5L2c57uT5p2f5pe26Ze05LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfV0KICAgICAgfSwKICAgICAgYmpyOiAiIiwKICAgICAgLy8g5piv5ZCm5bey5omn6KGM5LiL5ouJ5qGGCiAgICAgIHNmeXp4TGlzdDogWwogICAgICAgIHsgbGFiZWw6ICLlt7LmiafooYwiLCB2YWx1ZTogIuW3suaJp+ihjCIgfSwKICAgICAgICB7IGxhYmVsOiAi5pyq5omn6KGMIiwgdmFsdWU6ICLmnKrmiafooYwiIH0KICAgICAgXSwKICAgICAgLy8g6I635Y+W5b2T5YmN55m75b2V5Lq66LSm5Y+3CiAgICAgIGN1cnJlbnRVc2VyOiB0aGlzLiRzdG9yZS5nZXR0ZXJzLm5hbWUsCiAgICAgIC8v5bel5L2c5rWB5Lyg5YWl5Y+C5pWwCiAgICAgIHByb2Nlc3NEYXRhOiB7CiAgICAgICAgcHJvY2Vzc0RlZmluaXRpb25LZXk6ICJjenBsYyIsCiAgICAgICAgYnVzaW5lc3NLZXk6ICIiLAogICAgICAgIGJ1c2luZXNzVHlwZTogIuWAkumXuOaTjeS9nOelqCIsCiAgICAgICAgdmFyaWFibGVzOiB7fSwKICAgICAgICBkZWZhdWx0RnJvbTogdHJ1ZSwKICAgICAgICBuZXh0VXNlcjogIiIsCiAgICAgICAgcHJvY2Vzc1R5cGU6ICJjb21wbGV0ZSIKICAgICAgfSwKICAgICAgLy/lt6XkvZzmtYHlvLnnqpcKICAgICAgaXNTaG93OiBmYWxzZSwKICAgICAgLy/mtYHnqIvlm77mn6XnnIsKICAgICAgb3BlbkxvYWRpbmdJbWc6IGZhbHNlLAogICAgICBpbWdTcmM6ICIiLCAvL+a1geeoi+Wbvuafpeeci+WcsOWdgAogICAgICB0aW1lRGF0YTogW10sCiAgICAgIHRpbWVMaW5lU2hvdzogZmFsc2UsCiAgICAgIC8v5by55Ye65qGG5qCH6aKYCiAgICAgIGFjdGl2aXRpT3B0aW9uOiB7IHRpdGxlOiAiIiB9LAogICAgICB0aXRsZXlsOiAiIiwKICAgICAgLy/lm77niYflnLDlnYB1cmwKICAgICAgZGlhbG9nSW1hZ2VVcmw6ICIiLAogICAgICAvL+WxleekuuWbvueJh2RpYWxvZ+aOp+WItgogICAgICBpbWdEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgLy/kuIrkvKDlm77niYfml7bnmoTmkLrluKbnmoTlhbbku5blj4LmlbAKICAgICAgdXBsb2FkSW1nRGF0YTogewogICAgICAgIGJ1c2luZXNzSWQ6ICIiIC8v5pC65bim55qE6KGo5Y2V5Li76ZSuaWQKICAgICAgfSwKICAgICAgLy/kuIrkvKDlm77niYfml7bnmoTor7fmsYLlpLQKICAgICAgaGVhZGVyOiB7fSwKICAgICAgLy/lm77niYdsaXN0CiAgICAgIGltZ0xpc3Q6IFtdLAogICAgICBsb2dpbkZvcm06IHsKICAgICAgICB1c2VyTmFtZTogIiIsCiAgICAgICAgcGFzc3dvcmQ6ICIiLAogICAgICAgIHJlbWVtYmVyTWU6IGZhbHNlLAogICAgICAgIGNvZGU6ICI2NjY2IiwKICAgICAgICB1dWlkOiAiIgogICAgICB9LAogICAgICBzZWxlY3Rpb246IFtdLAogICAgICB5bDogZmFsc2UsCiAgICAgIC8vIOWkmumAieahhumAieS4reeahGlkCiAgICAgIGlkczogW10sCiAgICAgIC8v5by55Ye65qGG5Lit6KGo5qC85pWw5o2uCiAgICAgIHByb3BUYWJsZURhdGE6IHsKICAgICAgICBzZWw6IG51bGwsIC8vIOmAieS4reihjAogICAgICAgIGNvbEZpcnN0OiBbXQogICAgICB9LAogICAgICBkd1NlbGVjdGVkOiBbeyBsYWJlbDogIumFjeeUtei/kOe7tOWIhuWFrOWPuCIsIHZhbHVlOiAiMzAxMyIgfV0sCiAgICAgIC8vZm9ybeihqOWNlQogICAgICBmb3JtOiB7CiAgICAgICAgc3RhdHVzOiAiIiwKICAgICAgICBrc3NqOiAiIiwKICAgICAgICBqc3NqOiAiIiwKICAgICAgICBjenJ3OiAiIiwKICAgICAgICBmbHI6ICIiLAogICAgICAgIHNscjogIiIsCiAgICAgICAgZmxzajogIiIsCiAgICAgICAgbHg6IDMsIC8v6YWN55S1CiAgICAgICAgY29sRmlyc3Q6IFtdLAogICAgICAgIHBkem1jOiAiIiwKICAgICAgICBjenhzOiAwLAogICAgICAgIHl6eGN6eHM6IDAsCiAgICAgICAgd3p4Y3p4czogMAogICAgICB9LAogICAgICBmb3JtQ3pwOiB7CiAgICAgICAgcGRzaHI6ICIiLAogICAgICAgIHlqOiAiIgogICAgICB9LAogICAgICAvL+ivpuaDheW8ueahhuaYr+WQpuaYvuekugogICAgICBpc1Nob3dEZXRhaWxzOiBmYWxzZSwKICAgICAgLy/mmK/lkKbnpoHnlKgKICAgICAgaXNEaXNhYmxlZDogZmFsc2UsCiAgICAgIC8v5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgZmlsdGVySW5mbzogewogICAgICAgIGRhdGE6IHsKICAgICAgICAgIHNscjogIiIsCiAgICAgICAgICBmbHI6ICIiLAogICAgICAgICAgY3pzakFycjogW10sCiAgICAgICAgICBmbHNqQXJyOiBbXQogICAgICAgIH0sCiAgICAgICAgZmllbGRMaXN0OiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi5pON5L2c5pe26Ze0IiwKICAgICAgICAgICAgdmFsdWU6ICJjenNqQXJyIiwKICAgICAgICAgICAgdHlwZTogImRhdGUiLAogICAgICAgICAgICBkYXRlVHlwZTogImRhdGVyYW5nZSIsCiAgICAgICAgICAgIGZvcm1hdDogInl5eXktTU0tZGQiCiAgICAgICAgICB9LAogICAgICAgICAgeyBsYWJlbDogIuWPl+S7pOS6uiIsIHR5cGU6ICJpbnB1dCIsIHZhbHVlOiAic2xyIiwgY2xlYXJhYmxlOiB0cnVlIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5Y+R5Luk5Lq6IiwgdHlwZTogImlucHV0IiwgdmFsdWU6ICJmbHIiLCBjbGVhcmFibGU6IHRydWUgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLlj5Hku6Tml7bpl7QiLAogICAgICAgICAgICB2YWx1ZTogImZsc2pBcnIiLAogICAgICAgICAgICB0eXBlOiAiZGF0ZSIsCiAgICAgICAgICAgIGRhdGVUeXBlOiAiZGF0ZXJhbmdlIiwKICAgICAgICAgICAgZm9ybWF0OiAieXl5eS1NTS1kZCIKICAgICAgICAgIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIHRhYmxlQW5kUGFnZUluZm86IHsKICAgICAgICBwYWdlcjogewogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMDBdCiAgICAgICAgfSwKICAgICAgICBvcHRpb246IHsKICAgICAgICAgIGNoZWNrQm94OiB0cnVlLAogICAgICAgICAgc2VyaWFsTnVtYmVyOiB0cnVlCiAgICAgICAgfSwKICAgICAgICB0YWJsZURhdGE6IFtdLAogICAgICAgIHRhYmxlSGVhZGVyOiBbCiAgICAgICAgICB7IGxhYmVsOiAi6YWN55S156uZ5ZCN56ewIiwgcHJvcDogInBkekNuIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5pON5L2c5byA5aeL5pe26Ze0IiwgcHJvcDogImtzc2oiLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLmk43kvZznu5PmnZ/ml7bpl7QiLCBwcm9wOiAianNzaiIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBsYWJlbDogIuaTjeS9nOS7u+WKoSIsIHByb3A6ICJjenJ3IiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICAvLyB7bGFiZWw6ICflrqHmoLjkuronLCBwcm9wOiAncGRzaHInLCBtaW5XaWR0aDogJzEwMCd9LAogICAgICAgICAgeyBsYWJlbDogIuWPkeS7pOS6uiIsIHByb3A6ICJmbHIiLCBtaW5XaWR0aDogIjEwMCIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLlj5fku6TkuroiLCBwcm9wOiAic2xyIiwgbWluV2lkdGg6ICIxMDAiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5Y+R5Luk5pe26Ze0IiwgcHJvcDogImZsc2oiLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLmk43kvZzpobnmlbAiLCBwcm9wOiAiY3p4cyIsIG1pbldpZHRoOiAiODAiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5bey5omn6KGM6aG55pWwIiwgcHJvcDogInl6eGN6eHMiLCBtaW5XaWR0aDogIjkwIiB9LAogICAgICAgICAgeyBsYWJlbDogIuacquaJp+ihjOmhueaVsCIsIHByb3A6ICJ3enhjenhzIiwgbWluV2lkdGg6ICI5MCIgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgcHJvcDogIm9wZXJhdGlvbiIsCiAgICAgICAgICAgIGxhYmVsOiAi5pON5L2cIiwKICAgICAgICAgICAgbWluV2lkdGg6ICIxMzBweCIsCiAgICAgICAgICAgIHN0eWxlOiB7IGRpc3BsYXk6ICJibG9jayIgfSwKICAgICAgICAgICAgLy/mk43kvZzliJflm7rlrprlho3lj7PkvqcKICAgICAgICAgICAgZml4ZWQ6ICJyaWdodCIsCiAgICAgICAgICAgIG9wZXJhdGlvbjogW3sgbmFtZTogIuivpuaDhSIsIGNsaWNrRnVuOiB0aGlzLmdldEluZm8gfV0KICAgICAgICAgIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIHBhcmFtczogewogICAgICAgIGx4OiAzLAogICAgICAgIHN0YXR1czogIjQiCiAgICAgIH0KICAgIH07CiAgfSwKICBtb3VudGVkKCkgewogICAgLy/ojrflj5Z0b2tlbgogICAgdGhpcy5oZWFkZXIudG9rZW4gPSBnZXRUb2tlbigpOwogICAgdGhpcy5nZXRQZHNPcHRpb25zRGF0YUxpc3QoKTsKICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgaWYgKCJhZG1pbiIgPT09IHRoaXMuJHN0b3JlLmdldHRlcnMubmFtZSkgewogICAgICBsZXQgb3B0aW9uID0geyBuYW1lOiAi5Yig6ZmkIiwgY2xpY2tGdW46IHRoaXMuZGVsZXRlUm93IH07CiAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby50YWJsZUhlYWRlclsxMF0ub3BlcmF0aW9uLnB1c2gob3B0aW9uKTsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIC8v5Zu+54mH5pS+5aSnCiAgICBoYW5kbGVQaWN0dXJlQ2FyZFByZXZpZXcoZmlsZSkgewogICAgICB0aGlzLmRpYWxvZ0ltYWdlVXJsID0gW2ZpbGUudXJsXTsKICAgICAgdGhpcy5pbWdEaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICBvbkNsb3NlKCkgewogICAgICB0aGlzLmltZ0RpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgIH0sCiAgICAvLyDpooTop4jlvLnmoYYKICAgIGhhbmRsZVlsQ2hhbmdlKCkgewogICAgICB0aGlzLnRpdGxleWwgPSAi5p+l55yL5pON5L2c6aG555uuIjsKICAgICAgdGhpcy55bCA9IHRydWU7CiAgICB9LAogICAgLy/pgInmi6nooYwKICAgIHNlbGVjdENoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5vYmpJZCk7CiAgICAgIHRoaXMuc2VsZWN0aW9uID0gc2VsZWN0aW9uOwogICAgICB0aGlzLmZvcm0gPSB0aGlzLnNlbGVjdGlvblswXTsKICAgIH0sCiAgICAvL+iOt+WPlumFjeeUteWupOS4i+aLieahhuaVsOaNrgogICAgZ2V0UGRzT3B0aW9uc0RhdGFMaXN0KCkgewogICAgICBnZXRQZHNPcHRpb25zRGF0YUxpc3Qoe30pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLnBkc09wdGlvbnNEYXRhTGlzdCA9IHJlcy5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICBmaWx0ZXJSZXNldCgpIHsKICAgICAgdGhpcy5wYXJhbXMuc3RhdHVzID0gIjQiOwogICAgfSwKICAgIC8v5YiX6KGo5p+l6K+iCiAgICBhc3luYyBnZXREYXRhKHBhcmFtcykgewogICAgICB0cnkgewogICAgICAgIHRoaXMucGFyYW1zID0geyAuLi50aGlzLnBhcmFtcywgLi4ucGFyYW1zIH07CiAgICAgICAgY29uc3QgcGFyYW0gPSB0aGlzLnBhcmFtczsKICAgICAgICBwYXJhbS5teVNvcnRzID0gW3sgcHJvcDogInVwZGF0ZVRpbWUiLCBhc2M6IGZhbHNlIH1dOwogICAgICAgIGlmICghcGFyYW0uc3RhdHVzKSB7CiAgICAgICAgICBwYXJhbS5zdGF0dXMgPSAiMCwxLDIsMyI7CiAgICAgICAgfQogICAgICAgIGNvbnN0IHsgZGF0YSwgY29kZSB9ID0gYXdhaXQgZ2V0TGlzdChwYXJhbSk7CiAgICAgICAgaWYgKGNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgZm9yIChsZXQgaSBvZiBkYXRhLnJlY29yZHMpIHsKICAgICAgICAgICAgaS5mZ3NtYyA9ICLphY3nlLXov5Dnu7TliIblhazlj7giOwogICAgICAgICAgICB0aGlzLnN0YXR1c09wdGlvbnMuZm9yRWFjaChlbGVtZW50ID0+IHsKICAgICAgICAgICAgICBpZiAoaS5zdGF0dXMgPT09IGVsZW1lbnQudmFsdWUpIHsKICAgICAgICAgICAgICAgIGkuc3RhdHVzQ24gPSBlbGVtZW50LmxhYmVsOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8udGFibGVEYXRhID0gZGF0YS5yZWNvcmRzOwogICAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnRvdGFsID0gZGF0YS50b3RhbDsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICBjb25zb2xlLmxvZyhlKTsKICAgICAgfQogICAgfSwKICAgIC8v5Yig6Zmk5oyJ6ZKuCiAgICBhc3luYyBkZWxldGVSb3cocm93KSB7CiAgICAgIHRoaXMuJGNvbmZpcm0oIuatpOaTjeS9nOWwhuawuOS5heWIoOmZpOivpeaVsOaNriwg5piv5ZCm57un57utPyIsICLmj5DnpLoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KQogICAgICAgIC50aGVuKCgpID0+IHsKICAgICAgICAgIHJlbW92ZShyb3cub2JqSWQpLnRoZW4oKHsgY29kZSB9KSA9PiB7CiAgICAgICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTmiJDlip8hIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIC8v6YeN572ucGFnZemhteS7jjHlvIDlp4sKICAgICAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIucGFnZVJlc2l6ZSA9ICJZIjsKICAgICAgICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICJlcnJvciIsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5Yig6Zmk5aSx6LSlISIKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfSkKICAgICAgICAuY2F0Y2goKCkgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICJpbmZvIiwKICAgICAgICAgICAgbWVzc2FnZTogIuW3suWPlua2iOWIoOmZpCIKICAgICAgICAgIH0pOwogICAgICAgIH0pOwogICAgfSwKICAgIC8v6K+m5oOFCiAgICBnZXRJbmZvKHJvdykgewogICAgICB0aGlzLmdldEN6cG14KHJvdyk7CiAgICAgIHRoaXMudGl0bGUgPSAi6YWN55S15pON5L2c56Wo6K+m5oOF5p+l55yLIjsKICAgICAgdGhpcy5mb3JtID0geyAuLi5yb3cgfTsKICAgICAgLy/lm77niYfnu5PlkIgKICAgICAgdGhpcy5pbWdMaXN0ID0gdGhpcy5mb3JtLmltZ0xpc3Q7CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IHRydWU7CiAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IHRydWU7CiAgICAgIHRoaXMuZ2V0U2hvdygpOwogICAgfSwKICAgIC8v5YWz6Zet5by556qXCiAgICBjbG9zZSgpIHsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gZmFsc2U7CiAgICB9LAogICAgLyoqCiAgICAgKuiOt+WPluaTjeS9nOelqOaYjue7hgogICAgICovCiAgICBhc3luYyBnZXRDenBteChyb3cpIHsKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCB7IGRhdGEsIGNvZGUgfSA9IGF3YWl0IGdldEN6cG14TGlzdCh7IG9iaklkOiByb3cub2JqSWQgfSk7CiAgICAgICAgaWYgKGNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgLy/nu5lsaXN05re75Yqg5a2X5q61CiAgICAgICAgICBkYXRhLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICAgIHRoaXMuJHNldChpdGVtLCAiaXNTZXQiLCB0cnVlKTsKICAgICAgICAgIH0pOwogICAgICAgICAgdGhpcy5wcm9wVGFibGVEYXRhLmNvbEZpcnN0ID0gZGF0YTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICBjb25zb2xlLmxvZyhlKTsKICAgICAgfQogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["pdczp_cx.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8QA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "pdczp_cx.vue", "sourceRoot": "src/views/dagangOilfield/czpgl/pddzcz/components", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 180 }\"\n      @handleReset=\"filterReset\"\n    />\n\n    <div>\n      <el-white class=\"button-group\">\n        <el-row :gutter=\"3\" class=\"button_btn pull-right\">\n          <el-col\n            style=\"\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n          \"\n          >\n          </el-col>\n        </el-row>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"selectChange\"\n          height=\"66vh\"\n        >\n        </comp-table>\n      </el-white>\n    </div>\n\n    <!-- 详情/新增/修改 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"180px\" ref=\"form\" :model=\"form\" disabled>\n        <div>\n          <div>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"配电站名称\" prop=\"pdzCn\">\n                  <el-input v-model=\"form.pdzCn\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"编号\" prop=\"bm\">\n                  <el-input v-model=\"form.bm\" placeholder=\"请输入状态\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"操作人\" prop=\"czr\">\n                  <el-input v-model=\"form.czr\" placeholder=\"请输入操作人\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"监护人\" prop=\"jhr\">\n                  <el-input v-model=\"form.jhr\" placeholder=\"请输入监护人\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"审核人\" prop=\"fgssprmc\">\n                  <el-input\n                    v-model=\"form.fgssprmc\"\n                    placeholder=\"请输入审核人\"\n                  />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"日期\" prop=\"rq\">\n                  <el-date-picker\n                    v-model=\"form.rq\"\n                    type=\"date\"\n                    format=\"yyyy-MM-dd\"\n                    value-format=\"yyyy-MM-dd\"\n                    style=\"width: 100%\"\n                  >\n                  </el-date-picker>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"24\">\n                <el-form-item label=\"工作名称\" prop=\"gzmc\">\n                  <el-input type=\"textarea\" :rows=\"2\" v-model=\"form.gzmc\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"24\">\n                <el-form-item label=\"上传图片：\">\n                  <el-upload\n                    action=\"\"\n                    ref=\"uploadImg\"\n                    accept=\"image/jpeg,image/jpg,image/png\"\n                    :headers=\"header\"\n                    :multiple=\"true\"\n                    :data=\"uploadImgData\"\n                    :file-list=\"imgList\"\n                    :auto-upload=\"false\"\n                    list-type=\"picture-card\"\n                    :on-preview=\"handlePictureCardPreview\"\n                  >\n                    <i class=\"el-icon-plus\"></i>\n                  </el-upload>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"8\">\n                <el-form-item label=\"操作项数：\" prop=\"czxs\">\n                  <el-input-number\n                    v-model=\"form.czxs\"\n                    placeholder=\"请输入操作项数\"\n                  />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"已执行项数：\" prop=\"yzxczxs\">\n                  <el-input-number\n                    v-model=\"form.yzxczxs\"\n                    placeholder=\"请输入已执行操作项数\"\n                  />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"未执行项数：\" prop=\"wzxczxs\">\n                  <el-input-number\n                    v-model=\"form.wzxczxs\"\n                    placeholder=\"请输入未执行操作项数\"\n                  />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"是否已执行：\" prop=\"sfyzx\">\n                  <el-select v-model=\"form.sfyzx\" placeholder=\"请选择\">\n                    <el-option\n                      v-for=\"item in sfyzxList\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </div>\n\n          <!--列表-->\n          <div>\n            <!--第一个表格-->\n            <el-table\n              :data=\"propTableData.colFirst\"\n              height=\"200\"\n              border\n              stripe\n              style=\"width: 100%\"\n            >\n              <el-table-column\n                align=\"center\"\n                prop=\"xh\"\n                width=\"100px\"\n                label=\"序号\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input\n                    placeholder=\"请输入序号\"\n                    v-model=\"scope.row.xh\"\n                  ></el-input>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n                <template slot-scope=\"scope\">\n                  <el-input\n                    v-if=\"scope.row.isSet\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </template>\n              </el-table-column>\n\n              <el-table-column\n                align=\"center\"\n                prop=\"xlr\"\n                label=\"下令人\"\n                width=\"100\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input\n                    v-if=\"scope.row.isSet\"\n                    v-model=\"scope.row.xlr\"\n                  ></el-input>\n                </template>\n              </el-table-column>\n\n              <el-table-column\n                align=\"center\"\n                prop=\"xlsj\"\n                label=\"下令时间\"\n                width=\"204\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-date-picker\n                    v-if=\"scope.row.isSet\"\n                    v-model=\"scope.row.xlsj\"\n                    type=\"datetime\"\n                    format=\"yyyy-MM-dd HH:mm\"\n                    value-format=\"yyyy-MM-dd HH:mm\"\n                    style=\"width: 100%\"\n                  >\n                  </el-date-picker>\n                </template>\n              </el-table-column>\n\n              <el-table-column\n                align=\"center\"\n                prop=\"hlsj\"\n                label=\"回令时间\"\n                width=\"204\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-date-picker\n                    v-if=\"scope.row.isSet\"\n                    v-model=\"scope.row.hlsj\"\n                    type=\"datetime\"\n                    format=\"yyyy-MM-dd HH:mm\"\n                    value-format=\"yyyy-MM-dd HH:mm\"\n                    style=\"width: 100%\"\n                  >\n                  </el-date-picker>\n                </template>\n              </el-table-column>\n              <el-table-column\n                type=\"sfwc\"\n                width=\"50\"\n                label=\"是否完成\"\n                align=\"center\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-checkbox v-model=\"scope.row.sfwc\"></el-checkbox>\n                </template>\n              </el-table-column>\n            </el-table>\n          </div>\n        </div>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!--图片放大展示弹出框-->\n    <el-image-viewer\n      v-if=\"imgDialogVisible\"\n      :on-close=\"onClose\"\n      :url-list=\"dialogImageUrl\"\n      zIndex=\"3000\"\n    />\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport { getPdsOptionsDataList } from \"@/api/dagangOilfield/asset/pdsgl\";\nimport { getCzpmxList, getList, remove } from \"@/api/yxgl/pdyxgl/pddzczp\";\nimport ElImageViewer from \"element-ui/packages/image/src/image-viewer\";\nexport default {\n  name: \"czp_cx\",\n  components: { ElImageViewer },\n  data() {\n    return {\n      pdsOptionsDataList: [],\n      isDisabledBj: true,\n      statusOptions: [\n        {\n          value: \"0\",\n          label: \"操作票填报\"\n        },\n        {\n          value: \"1\",\n          label: \"班组审核\"\n        },\n        {\n          value: \"2\",\n          label: \"分公司审核\"\n        },\n        {\n          value: \"3\",\n          label: \"操作票办结\"\n        },\n        {\n          value: \"4\",\n          label: \"结束\"\n        }\n      ],\n      buttonNameShow: false,\n      buttonName: \"\",\n      rules: {\n        fgs: [{ required: true, message: \"单位不能为空\", trigger: \"select\" }],\n        pdzmc: [{ required: true, message: \"单位不能为空\", trigger: \"select\" }],\n        flr: [{ required: true, message: \"发令人不能为空\", trigger: \"change\" }],\n        slr: [{ required: true, message: \"受令人不能为空\", trigger: \"change\" }],\n        flsj: [\n          { required: true, message: \"发令时间不能为空\", trigger: \"change\" }\n        ],\n        czrw: [\n          { required: true, message: \"操作任务不能为空\", trigger: \"change\" }\n        ],\n        czr: [{ message: \"操作人不能为空\", trigger: \"blur\" }],\n        jhr: [{ message: \"监护人不能为空\", trigger: \"blur\" }],\n        kssj: [{ message: \"操作开始时间不能为空\", trigger: \"change\" }],\n        jssj: [{ message: \"操作结束时间不能为空\", trigger: \"change\" }]\n      },\n      bjr: \"\",\n      // 是否已执行下拉框\n      sfyzxList: [\n        { label: \"已执行\", value: \"已执行\" },\n        { label: \"未执行\", value: \"未执行\" }\n      ],\n      // 获取当前登录人账号\n      currentUser: this.$store.getters.name,\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"czplc\",\n        businessKey: \"\",\n        businessType: \"倒闸操作票\",\n        variables: {},\n        defaultFrom: true,\n        nextUser: \"\",\n        processType: \"complete\"\n      },\n      //工作流弹窗\n      isShow: false,\n      //流程图查看\n      openLoadingImg: false,\n      imgSrc: \"\", //流程图查看地址\n      timeData: [],\n      timeLineShow: false,\n      //弹出框标题\n      activitiOption: { title: \"\" },\n      titleyl: \"\",\n      //图片地址url\n      dialogImageUrl: \"\",\n      //展示图片dialog控制\n      imgDialogVisible: false,\n      //上传图片时的携带的其他参数\n      uploadImgData: {\n        businessId: \"\" //携带的表单主键id\n      },\n      //上传图片时的请求头\n      header: {},\n      //图片list\n      imgList: [],\n      loginForm: {\n        userName: \"\",\n        password: \"\",\n        rememberMe: false,\n        code: \"6666\",\n        uuid: \"\"\n      },\n      selection: [],\n      yl: false,\n      // 多选框选中的id\n      ids: [],\n      //弹出框中表格数据\n      propTableData: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      dwSelected: [{ label: \"配电运维分公司\", value: \"3013\" }],\n      //form表单\n      form: {\n        status: \"\",\n        kssj: \"\",\n        jssj: \"\",\n        czrw: \"\",\n        flr: \"\",\n        slr: \"\",\n        flsj: \"\",\n        lx: 3, //配电\n        colFirst: [],\n        pdzmc: \"\",\n        czxs: 0,\n        yzxczxs: 0,\n        wzxczxs: 0\n      },\n      formCzp: {\n        pdshr: \"\",\n        yj: \"\"\n      },\n      //详情弹框是否显示\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          slr: \"\",\n          flr: \"\",\n          czsjArr: [],\n          flsjArr: []\n        },\n        fieldList: [\n          {\n            label: \"操作时间\",\n            value: \"czsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"受令人\", type: \"input\", value: \"slr\", clearable: true },\n          { label: \"发令人\", type: \"input\", value: \"flr\", clearable: true },\n          {\n            label: \"发令时间\",\n            value: \"flsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"配电站名称\", prop: \"pdzCn\", minWidth: \"120\" },\n          { label: \"操作开始时间\", prop: \"kssj\", minWidth: \"120\" },\n          { label: \"操作结束时间\", prop: \"jssj\", minWidth: \"120\" },\n          { label: \"操作任务\", prop: \"czrw\", minWidth: \"120\" },\n          // {label: '审核人', prop: 'pdshr', minWidth: '100'},\n          { label: \"发令人\", prop: \"flr\", minWidth: \"100\" },\n          { label: \"受令人\", prop: \"slr\", minWidth: \"100\" },\n          { label: \"发令时间\", prop: \"flsj\", minWidth: \"120\" },\n          { label: \"操作项数\", prop: \"czxs\", minWidth: \"80\" },\n          { label: \"已执行项数\", prop: \"yzxczxs\", minWidth: \"90\" },\n          { label: \"未执行项数\", prop: \"wzxczxs\", minWidth: \"90\" },\n          {\n            prop: \"operation\",\n            label: \"操作\",\n            minWidth: \"130px\",\n            style: { display: \"block\" },\n            //操作列固定再右侧\n            fixed: \"right\",\n            operation: [{ name: \"详情\", clickFun: this.getInfo }]\n          }\n        ]\n      },\n      params: {\n        lx: 3,\n        status: \"4\"\n      }\n    };\n  },\n  mounted() {\n    //获取token\n    this.header.token = getToken();\n    this.getPdsOptionsDataList();\n    this.getData();\n    if (\"admin\" === this.$store.getters.name) {\n      let option = { name: \"删除\", clickFun: this.deleteRow };\n      this.tableAndPageInfo.tableHeader[10].operation.push(option);\n    }\n  },\n  methods: {\n    //图片放大\n    handlePictureCardPreview(file) {\n      this.dialogImageUrl = [file.url];\n      this.imgDialogVisible = true;\n    },\n    onClose() {\n      this.imgDialogVisible = false;\n    },\n    // 预览弹框\n    handleYlChange() {\n      this.titleyl = \"查看操作项目\";\n      this.yl = true;\n    },\n    //选择行\n    selectChange(selection) {\n      this.ids = selection.map(item => item.objId);\n      this.selection = selection;\n      this.form = this.selection[0];\n    },\n    //获取配电室下拉框数据\n    getPdsOptionsDataList() {\n      getPdsOptionsDataList({}).then(res => {\n        this.pdsOptionsDataList = res.data;\n      });\n    },\n    filterReset() {\n      this.params.status = \"4\";\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        param.mySorts = [{ prop: \"updateTime\", asc: false }];\n        if (!param.status) {\n          param.status = \"0,1,2,3\";\n        }\n        const { data, code } = await getList(param);\n        if (code === \"0000\") {\n          for (let i of data.records) {\n            i.fgsmc = \"配电运维分公司\";\n            this.statusOptions.forEach(element => {\n              if (i.status === element.value) {\n                i.statusCn = element.label;\n              }\n            });\n          }\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //删除按钮\n    async deleteRow(row) {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove(row.objId).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    //详情\n    getInfo(row) {\n      this.getCzpmx(row);\n      this.title = \"配电操作票详情查看\";\n      this.form = { ...row };\n      //图片结合\n      this.imgList = this.form.imgList;\n      this.isDisabled = true;\n      this.isShowDetails = true;\n      this.getShow();\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false;\n    },\n    /**\n     *获取操作票明细\n     */\n    async getCzpmx(row) {\n      try {\n        const { data, code } = await getCzpmxList({ objId: row.objId });\n        if (code === \"0000\") {\n          //给list添加字段\n          data.forEach(item => {\n            this.$set(item, \"isSet\", true);\n          });\n          this.propTableData.colFirst = data;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.card-style {\n  background-color: #e0eff2;\n  line-height: 3vh;\n  border-radius: 3px;\n  font-weight: 800;\n  padding-left: 1vw;\n  margin-bottom: 3vh;\n}\n\n/*控制input输入框边框是否显示*/\n.elInput >>> .el-input__inner {\n  border: 0;\n}\n\n.center {\n  text-align: center; /*让div内部文字居中*/\n}\n</style>\n"]}]}