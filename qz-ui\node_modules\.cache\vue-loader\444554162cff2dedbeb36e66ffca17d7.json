{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\syxmzq.vue?vue&type=style&index=0&id=7ccc6a29&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\syxmzq.vue", "mtime": 1706897323691}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmJveC1jYXJkIHsKICBtYXJnaW4tYm90dG9tOiAxNXB4OwoKICAuZWwtY2FyZF9faGVhZGVyIHsKICAgIGJhY2tncm91bmQtY29sb3I6IHJnYigyMzUsIDI0NSwgMjU1KSAhaW1wb3J0YW50OwogIH0KfQoKLml0ZW0gewogIHdpZHRoOiAyMDBweDsKICBoZWlnaHQ6IDE0OHB4OwogIGZsb2F0OiBsZWZ0Owp9Cg=="}, {"version": 3, "sources": ["syxmzq.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2rBA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "syxmzq.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk", "sourcesContent": ["<template>\n\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 180, itemWidth: 300 }\"\n      @handleReset=\"getReset\"\n    />\n    <div>\n      <el-white class=\"button-group\">\n        <div class=\"button_btn\">\n          <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"addSensorButton\"\n          >新增\n          </el-button>\n          <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"getDelete\"\n          >删除\n          </el-button>\n        </div>\n        <comp-table :table-and-page-info=\"tableAndPageInfo\"\n                    @update:multipleSelection=\"handleSelectionChange\"\n                    height=\"57.2vh\"/>\n      </el-white>\n      <!-- 详情/新增/修改 -->\n      <el-dialog :title=title :visible.sync=\"isShowDetails\" width=\"60%\" v-dialogDrag>\n        <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :disabled=\"isDisabled\" :rules=\"rules\">\n          <el-row :gutter=\"8\" class=\"pull-left\">\n            <el-col :span=\"8\">\n              <el-form-item label=\"试验专业：\" prop=\"jglx\">\n                <el-select v-model=\"form.syzyid\" style=\"width: 100%\">\n                  <el-option\n                    v-for=\"item in syzyOptionsSelectedList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"项目名称：\" prop=\"syxmmc\">\n                <el-input v-model=\"form.syxmmc\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"项目描述：\" prop=\"zxmms\">\n                <el-input v-model=\"form.syxmms\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"录入温度：\" prop=\"lrwd\">\n                <el-select v-model=\"form.lrwd\" style=\"width: 100%\">\n                  <el-option label=\"是\" value=\"是\"></el-option>\n                  <el-option label=\"否\" value=\"否\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"录入湿度：\" prop=\"lrsd\">\n                <el-select v-model=\"form.lrsd\" style=\"width: 100%\">\n                  <el-option label=\"是\" value=\"是\"></el-option>\n                  <el-option label=\"否\" value=\"否\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"录入油温：\" prop=\"lryw\">\n                <el-select v-model=\"form.lryw\" style=\"width: 100%\">\n                  <el-option label=\"是\" value=\"是\"></el-option>\n                  <el-option label=\"否\" value=\"否\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"录入负荷：\" prop=\"lrfh\">\n                <el-select v-model=\"form.lrfh\" style=\"width: 100%\">\n                  <el-option label=\"是\" value=\"是\"></el-option>\n                  <el-option label=\"否\" value=\"否\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"可调整初值：\" prop=\"sfktzcz\">\n                <el-select v-model=\"form.sfktzcz\" style=\"width: 100%\">\n                  <el-option label=\"是\" value=\"是\"></el-option>\n                  <el-option label=\"否\" value=\"否\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"部位可拓展：\" prop=\"bwkkz\">\n                <el-select v-model=\"form.bwkkz\" style=\"width: 100%\">\n                  <el-option label=\"是\" value=\"是\"></el-option>\n                  <el-option label=\"否\" value=\"否\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"显示方式：\" prop=\"xsfs\">\n                <el-select v-model=\"form.xsfs\" style=\"width: 100%\">\n                  <el-option label=\"列为子项目\" value=\"列为子项目\"></el-option>\n                  <el-option label=\"竖排\" value=\"竖排\"></el-option>\n                  <el-option label=\"行为部位\" value=\"行为部位\"></el-option>\n                  <el-option label=\"列为部位\" value=\"列为部位\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"子项目定位：\" prop=\"zxmdw\">\n                <el-select v-model=\"form.zxmdw\" style=\"width: 100%\">\n                  <el-option label=\"相别\" value=\"相别\"></el-option>\n                  <el-option label=\"设备\" value=\"设备\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n        <div slot=\"footer\" v-show=\"dialogButtonShow\">\n          <el-button @click=\"close\">取 消</el-button>\n          <el-button type=\"primary\" @click=\"commitForm\">确 认</el-button>\n        </div>\n      </el-dialog>\n      <!--子项目列表弹出框-->\n      <el-dialog :title=\"mjxDialogTitle\" :visible.sync=\"isShowMjzDialog\" width=\"50%\" v-dialogDrag>\n        <el-white class=\"button-group\">\n          <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n            <el-col\n              style=\"display: flex;justify-content: space-between;align-items: center;\">\n              <div>\n                <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"addMjz\">新增</el-button>\n                <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"deleteMjz\">删除</el-button>\n              </div>\n            </el-col>\n          </el-row>\n        </el-white>\n        <el-table stripe border :data=\"glzxmDataList\" @selection-change=\"handleSelectionMjzChange\"\n                  :header-cell-style=\"{'text-align':'center'}\" :cell-style=\"{'text-align':'center'}\">\n          <el-table-column type=\"selection\" width=\"55\"/>\n          <el-table-column label=\"序号\" type=\"index\" width=\"50\">\n          </el-table-column>\n          <el-table-column label=\"试验子项目\" prop=\"syzxmmc\" :show-overflow-tooltip=\"true\"/>\n          <el-table-column label=\"子项目描述\" prop=\"syzxmms\" :show-overflow-tooltip=\"true\"/>\n        </el-table>\n        <pagination\n          v-show=\"glzxmTotal>0\"\n          :total=\"glzxmTotal\"\n          :page.sync=\"glzxmQueryParams.pageNum\"\n          :limit.sync=\"glzxmQueryParams.pageSize\"\n          @pagination=\"getMjzDataList\"\n        />\n      </el-dialog>\n      <!--子列表新增弹窗调用-->\n      <el-dialog :title=\"mjzAddDialogTitle\" :visible.sync=\"isShowMjzAddDialog\" width=\"50%\" v-dialogDrag>\n        <el-form label-width=\"120px\">\n          <el-row :gutter=\"3\">\n            <el-col :span=\"10\">\n              <el-form-item label=\"子项目名称：\">\n                <el-input v-model=\"zxmLibraryQueryForm.syzxmmc\"/>\n              </el-form-item>\n            </el-col>\n            <div class=\"mb8 pull-right\">\n              <el-button type=\"cyan\" size=\"mini\" icon=\"el-icon-search\" @click=\"selectZxmLibrary\">查询</el-button>\n              <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetZxmSearch\">重置</el-button>\n            </div>\n          </el-row>\n        </el-form>\n        <el-table stripe border :data=\"zxmLibraryDataList\" @selection-change=\"handleSelectedZxmLibraryChange\"\n                  :header-cell-style=\"{'text-align':'center'}\" :cell-style=\"{'text-align':'center'}\">\n          <el-table-column type=\"selection\" width=\"55\"/>\n          <el-table-column label=\"序号\" type=\"index\" width=\"50\">\n          </el-table-column>\n          <el-table-column label=\"试验子项目\" prop=\"zxmmc\" :show-overflow-tooltip=\"true\"/>\n          <el-table-column label=\"子项目描述\" prop=\"zxmms\" :show-overflow-tooltip=\"true\"/>\n        </el-table>\n        <pagination\n          v-show=\"zxmLibraryTotal>0\"\n          :total=\"zxmLibraryTotal\"\n          :page.sync=\"zxmLibraryQueryForm.pageNum\"\n          :limit.sync=\"zxmLibraryQueryForm.pageSize\"\n          @pagination=\"getZxmLibraryList\"\n        />\n        <div slot=\"footer\">\n          <el-button @click=\"closeAddMjzDialog\">取 消</el-button>\n          <el-button type=\"primary\" @click=\"commitAddMjzForm\">确 认</el-button>\n        </div>\n      </el-dialog>\n    </div>\n  </div>\n</template>\n\n<script>\n  import {\n    getPageDataList,\n    getSyzyOptionsSelected,\n    getGlSyzxmDataListByPage,\n    getSyzxmLibraryDataListByPage,\n    addBatchSyzxmToXmzxm,\n    remove,\n    saveOrUpdate,\n    getPageKxzDataList,\n    removeKxzData,\n    saveOrUpdateKxzData,\n    getSblxTree\n  } from '@/api/dagangOilfield/bzgl/syxm'\n\n\n  export default {\n    name: \"lpbzk\",\n    data() {\n      return {\n        //试验子项目库数据\n        zxmLibraryDataList: [],\n        //试验专业下拉框数据\n        syzyOptionsSelectedList: [],\n        //枚举值新增弹出框底部按钮控制显示\n        addMjzDialogButtonShow: true,\n        //控制枚举值新增弹出框内容是否可编辑\n        mjzAddDialogDisabled: false,\n        //枚举值新增form表单\n        mjzAddForm: {\n          syzxmid: undefined,\n          kxz: undefined\n        },\n        //枚举值新增弹出框标题\n        mjzAddDialogTitle: \"子项目列表\",\n        //枚举值新增弹出框控制\n        isShowMjzAddDialog: false,\n        //选中子项目时获取到的第一行数据用来查询枚举值\n        mjzRowForm: {},\n        //维护枚举值button按钮\n        whmjzButtonDisabled: true,\n        //枚举值数据\n        glzxmDataList: [],\n        //枚举值参数\n        glzxmQueryParams: {\n          syxmid: undefined,\n          pageSize: 10,\n          pageNum: 1,\n        },\n        //枚举值总数\n        glzxmTotal: 0,\n        //枚举项弹出框标题\n        mjxDialogTitle: \"关联子项目\",\n        //枚举项弹出框\n        isShowMjzDialog: false,\n\n        //删除选择列\n        selectRows: [],\n        //表单验证\n        rules: {\n          zxmmc: [\n            {required: true, message: '请输入子项目名称', trigger: 'blur'},\n          ],\n        },\n        //弹出框取消确认按钮显示\n        dialogButtonShow: true,\n        //筛选框\n        filterInfo: {\n          data: {\n            syzyid: \"\",\n            syxmmc: \"\"\n          },\n          fieldList: [\n            {\n              label: '试验专业',\n              value: 'syzyid',\n              type: 'select',\n              options: [],\n              clearable: true,\n            },\n            {\n              label: '项目名称',\n              value: 'syxmmc',\n              type: 'input',\n              clearable: true,\n            },\n          ]\n        },\n        //列表页\n        tableAndPageInfo: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          tableData: [],\n          tableHeader: [\n            {label: '专业名称', prop: 'zymc', minWidth: '100'},\n            {label: '项目名称', prop: 'syxmmc', minWidth: '150'},\n            {label: '录入温度', prop: 'lrwd', minWidth: '80'},\n            {label: '录入湿度', prop: 'lrsd', minWidth: '80'},\n            {label: '录入油温', prop: 'lryw', minWidth: '80'},\n            {label: '录入负荷', prop: 'lrfh', minWidth: '80'},\n            {label: '显示方式', prop: 'xsfs', minWidth: '80'},\n            {label: '试验项目描述', prop: 'syxmms', minWidth: '150'},\n            {label: '是否可调整初值', prop: 'sfktzcz', minWidth: '150'},\n            {label: '部位可扩展', prop: 'bwkkz', minWidth: '150'},\n            {label: '子项目定位', prop: 'zxmdw', minWidth: '150'},\n            {\n              prop: 'operation',\n              label: '操作',\n              minWidth: '150px',\n              style: {display: 'block'},\n              //操作列固定再右侧\n              fixed: 'right',\n              operation: [\n                {name: '修改', clickFun: this.undateDetails},\n                {name: '详情', clickFun: this.getDetailsInfo},\n                {name: '关联子项目', clickFun: this.getZxmDataInfo},\n              ]\n            },\n          ],\n          option: {checkBox: true, serialNumber: true},\n        },\n        //查询试验子项目参数\n        querySyzxmParam: {\n          pageNum: 1,\n          pageSize: 10\n        },\n        //结果类型下拉框数据\n        jglxOptionsSelectedList: [\n          {label: '图片', value: '图片'},\n          {label: '数字', value: '数字'},\n          {label: '日期', value: '日期'},\n          {label: '单选', value: '单选'},\n          {label: '枚举', value: '枚举'},\n          {label: '字符', value: '字符'},\n        ],\n        //计算列\n        jslOptionsSelectedList: [\n          {label: '是', value: '是'},\n          {label: '否', value: '否'},\n        ],\n        //是否为空\n        sfkwkOptionsSelectedList: [\n          {label: '是', value: '是'},\n          {label: '否', value: '否'},\n        ],\n        //是否显示\n        sfxsOptionsSelectedList: [\n          {label: '是', value: '是'},\n          {label: '否', value: '否'},\n        ],\n\n        //form表单\n        form: {},\n        //是否显示弹框\n        isShowDetails: false,\n        //是否禁用\n        isDisabled: false,\n        //表单数据\n        dataTable: [],\n        //标题\n        title: '',\n\n\n        //组织树\n        treeOptions: [\n          {\n            label: '断路器',\n          }, {\n            label: '变压器',\n            children: [{\n              label: '冷却系统',\n              children: [{\n                label: '温控运行情况',\n\n              }, {\n                label: '油箱',\n\n              }, {\n                label: '铁芯',\n\n              }, {\n                label: '绕组',\n\n              }]\n            }]\n          }],\n        //删除是否可用\n        multipleSensor: true,\n        //查询参数\n        queryParams: {\n          pageNum: 1,\n          pageSize: 10,\n          ancuoTerm: ''\n        },\n        //删除关联子项目的id数组\n        selectedKxzDataRow: [],\n        //子项目库总数\n        zxmLibraryTotal: 0,\n        //子项目库查询参数\n        zxmLibraryQueryForm: {\n          pageNum: 1,\n          pageSize: 10,\n          //试验项目id\n          syxmid: undefined,\n          syzxmmc: undefined,\n        },\n        //试验子项目库中选中新增关联子项目时的数据对象\n        zxmSelectedForm: {\n          //试验项目id\n          syxmid: undefined,\n          //试验子项目数据集合\n          zxmDataRows: []\n        },\n\n\n      };\n    },\n    watch: {},\n    created() {\n      //获取试验专业下拉框数据\n      this.getSyzyOptionsSelected();\n      //获取列表数据\n      this.getData();\n\n\n    },\n    methods: {\n      //子项目库中查询按钮\n      selectZxmLibrary() {\n        this.getZxmLibraryList();\n      },\n      //子项目库中重置按钮\n      resetZxmSearch() {\n        this.zxmLibraryQueryForm.syzxmmc = \"\";\n      },\n      //关联子项目中新增按钮\n      addMjz() {\n        this.isShowMjzAddDialog = true;\n        this.getZxmLibraryList();\n      },\n      //获取试验子项目库数据方法\n      getZxmLibraryList() {\n        getSyzxmLibraryDataListByPage(this.zxmLibraryQueryForm).then(res => {\n          this.zxmLibraryTotal = res.data.total;\n          this.zxmLibraryDataList = res.data.records;\n        })\n      },\n      //试验子项目库选中按钮\n      handleSelectedZxmLibraryChange(rows) {\n        this.zxmSelectedForm.zxmDataRows = rows;\n      },\n      //提交从子项目库中调用数据插入到关联子项目表中\n      commitAddMjzForm() {\n        if (this.zxmSelectedForm.zxmDataRows.length < 1) {\n          this.$message.info(\"未关联子项目！！！已取消\")\n          //如果未选中数据,则直接关闭弹窗\n          this.isShowMjzAddDialog = false;\n        } else {\n          //若选择数据后\n          addBatchSyzxmToXmzxm(this.zxmSelectedForm).then(res => {\n            if (res.code === '0000') {\n              this.$message.success(\"关联成功\");\n            } else {\n              this.$message.error(\"关联失败！！\");\n            }\n            //关闭弹窗\n            this.isShowMjzAddDialog = false\n            //调用获取关联子项目列表\n            this.getMjzDataList();\n          })\n\n        }\n\n      },\n      //关联子项目按钮\n      getZxmDataInfo(row) {\n        this.glzxmQueryParams.syxmid = row.objId;\n        //给子项目库查询参数赋值\n        this.zxmLibraryQueryForm.syxmid = row.objId;\n        //给批量新增关联子项目时的主项目id赋值\n        this.zxmSelectedForm.syxmid = row.objId;\n        //打开子项目列表弹出框\n        this.isShowMjzDialog = true;\n        //获取关联子项目列表数据\n        this.getMjzDataList();\n      },\n\n      //获取关联子列表方法\n      getMjzDataList() {\n        getGlSyzxmDataListByPage(this.glzxmQueryParams).then(res => {\n          this.glzxmTotal = res.data.total;\n          this.glzxmDataList = res.data.records;\n        })\n      },\n      //删除关联子列表方法\n      deleteMjz() {\n        if (this.selectedKxzDataRow.length < 1) {\n          this.$message.warning(\"请选择正确的数据！！！\")\n          return\n        }\n        let ids = this.selectedKxzDataRow.map(item => {\n          return item.objId\n        });\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          removeKxzData(ids).then(({code}) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              });\n              this.getMjzDataList()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              });\n              this.getMjzDataList()\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          });\n        });\n      },\n\n      //列表查询项目列表\n      async getData(params) {\n        try {\n          const param = {...this.querySyzxmParam, ...params}\n          const {data, code} = await getPageDataList(param);\n          if (code === '0000') {\n            this.tableAndPageInfo.tableData = data.records\n            this.tableAndPageInfo.pager.total = data.total\n          }\n        } catch (e) {\n          console.log(e)\n        }\n      },\n      //获取试验专业下拉框数据\n      getSyzyOptionsSelected() {\n        getSyzyOptionsSelected().then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value === 'syzyid') {\n              item.options = res.data;\n              this.syzyOptionsSelectedList = res.data;\n            }\n          })\n        });\n      },\n\n      //取消按钮(枚举值新增弹出框)\n      closeAddMjzDialog() {\n        this.isShowMjzAddDialog = false\n      },\n\n      //关联子列表行选中事件\n      handleSelectionMjzChange(rows) {\n        this.selectedKxzDataRow = rows;\n      },\n\n      //维护枚举值按钮\n      addZxmKxz() {\n        if (this.mjzRowForm.jglx != '枚举') {\n          this.$message.warning(\"请选择结果类型为枚举类型的数据！\")\n        } else {\n          //打开弹窗\n          this.isShowMjzDialog = true;\n          this.mjzQueryParams.syzxmid = this.mjzRowForm.objId;\n          //获取枚举值列表\n          this.getMjzDataList();\n        }\n      },\n      //行选中\n      handleSelectionChange(rows) {\n        this.selectRows = rows;\n      },\n\n      //获取详情\n      getDetailsInfo(row) {\n        //打开弹窗\n        this.isShowDetails = true;\n        //弹出框按钮不显示\n        this.dialogButtonShow = false;\n        //给表单赋值\n        this.form = row;\n        //表单不可编辑\n        this.isDisabled = true;\n        //设置弹出框标题\n        this.title = '详情';\n      },\n      //修改按钮\n      undateDetails(row) {\n        //打开弹窗\n        this.isShowDetails = true;\n        //弹出框按钮显示\n        this.dialogButtonShow = true;\n        //给表单赋值\n        this.form = row;\n        //表单可编辑\n        this.isDisabled = false;\n        //设置弹出框标题\n        this.title = '修改';\n      },\n      //添加按钮\n      addSensorButton() {\n        //打开弹窗\n        this.isShowDetails = true;\n        //弹出框按钮显示\n        this.dialogButtonShow = true;\n        //给表单置空\n        this.form = {};\n        //表单可编辑\n        this.isDisabled = false;\n        //设置弹出框标题\n        this.title = '新增';\n      },\n      //删除按钮\n      getDelete() {\n        if (this.selectRows.length < 1) {\n          this.$message.warning(\"请选择正确的数据！！！\")\n          return\n        }\n        let ids = this.selectRows.map(item => {\n          return item.objId\n        });\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove(ids).then(({code}) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              });\n              this.tableAndPageInfo.pager.pageResize = 'Y';\n              this.getData()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              });\n              this.tableAndPageInfo.pager.pageResize = 'Y';\n              this.getData()\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          });\n        });\n\n      },\n      //确认提交表单\n      commitForm() {\n        saveOrUpdate(this.form).then(res => {\n          if (res.code === '0000') {\n            this.$message.success(res.msg)\n            this.tableAndPageInfo.pager.pageResize = 'Y';\n            this.getData();\n            this.isShowDetails = false;\n          } else {\n            this.$message.error(res.msg)\n          }\n        });\n      },\n      //关闭弹窗\n      close() {\n        this.isShowDetails = false\n      },\n      //定义重置方法\n      getReset() {\n\n      },\n      //删除按钮\n      deleteSensorButton() {\n\n      },\n      //导出按钮\n      handleExport() {\n\n      },\n\n\n      //搜索\n      handleQuery() {\n\n      },\n      //重置\n      resetQuery() {\n        this.resetForm(\"queryForm\");\n      }\n    }\n  };\n</script>\n\n<style lang='scss' scoped>\n  .box-card {\n    margin-bottom: 15px;\n\n    .el-card__header {\n      background-color: rgb(235, 245, 255) !important;\n    }\n  }\n\n  .item {\n    width: 200px;\n    height: 148px;\n    float: left;\n  }\n</style>\n"]}]}