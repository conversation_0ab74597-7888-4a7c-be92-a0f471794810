{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\blgk\\bzkwh.vue?vue&type=template&id=cd77faa0&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\blgk\\bzkwh.vue", "mtime": 1706897322268}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}