{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\gqjgl\\bdgqj.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\gqjgl\\bdgqj.vue", "mtime": 1706897324693}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFVVSUQgfSBmcm9tICJAL3V0aWxzL3J1b3lpIjsKaW1wb3J0IHsKICBkZWxldGVBc3NldEdxakp4UmVjb3JkcywKICBkZWxldGVZeFN5UmVjb3JkcywKICBnZXRBc3NldEdxakp4UmVjb3JkcywKICBnZXRMaXN0LAogIGdldFl4U3lSZWNvcmRzLAogIHJlbW92ZSwKICBzYXZlT3JVcGRhdGUsCiAgc2F2ZU9yVXBkYXRlQXNzZXRHcWpKeFJlY29yZHMsCiAgc2F2ZU9yVXBkYXRlWXhTeVJlY29yZHMsCiAgZXhwb3J0RXhjZWwKfSBmcm9tICJAL2FwaS9kYWdhbmdPaWxmaWVsZC9hc3NldC9hc3NldEdxaiI7CmltcG9ydCBDb21wVGFibGUgZnJvbSAiY29tL0NvbXBUYWJsZSI7CmltcG9ydCBFbEZpbHRlciBmcm9tICJjb20vRWxGaWx0ZXIiOwppbXBvcnQgeyBnZXRCZHpTZWxlY3RMaXN0IH0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYnpnbC9scGJ6ay9kcWd6enFweiI7CmltcG9ydCB7IGdldERpY3RUeXBlRGF0YSB9IGZyb20gIkAvYXBpL3N5c3RlbS9kaWN0L2RhdGEiOwppbXBvcnQgeyBnZXRGZ3NPcHRpb25zLCBnZXRTZWxlY3RPcHRpb25zQnlPcmdUeXBlIH0gZnJvbSAiQC9hcGkveXhnbC9iZHl4Z2wvemJnbCI7CgpleHBvcnQgZGVmYXVsdCB7CiAgY29tcG9uZW50czogeyBDb21wVGFibGUsIEVsRmlsdGVyIH0sCiAgbmFtZTogImdxamdsIiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgYmRncWpseDogW10sCiAgICAgIGN1cnJVc2VyOiB0aGlzLiRzdG9yZS5nZXR0ZXJzLm5hbWUsCiAgICAgIHBhcmFtczogewogICAgICAgIHR5cGU6ICJiZCIKICAgICAgfSwKICAgICAgYmR6QWxsTGlzdDogW10sCiAgICAgIGJkekxpc3Q6IFtdLAogICAgICAvL+e7hOe7h+e7k+aehOS4i+aLieaVsOaNrgogICAgICBvcmdhbml6YXRpb25TZWxlY3RlZExpc3Q6IFtdLAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICBzbDogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fovpPlhaUiLCB0cmlnZ2VyOiAiYmx1ciIgfV0sCiAgICAgICAgc3NnczogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6kiLCB0cmlnZ2VyOiAiYmx1ciIgfV0sCiAgICAgICAgLy8gYmR6OiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqScsIHRyaWdnZXI6ICdibHVyJyB9XSwKICAgICAgICBzYm1jOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+i+k+WFpSIsIHRyaWdnZXI6ICJibHVyIiB9XSwKICAgICAgICBzYmx4OiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqSIsIHRyaWdnZXI6ICJibHVyIiB9XSwKICAgICAgICBmenI6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWlIiwgdHJpZ2dlcjogImJsdXIiIH1dCiAgICAgIH0sCiAgICAgIC8v5bel5Zmo5YW36K+m5oOF5qGG5a2X5q615o6n5Yi2CiAgICAgIGlzRGlzYWJsZWQ6IGZhbHNlLAogICAgICAvL+W3peWZqOWFt+W8ueWHuuahhuihqOWktAogICAgICBncWpUaXRhbDogIuW3peWZqOWFt+aWsOWiniIsCiAgICAgIC8v6KGo5qC85YaF5a65CiAgICAgIHRhYmxlQW5kUGFnZUluZm86IHsKICAgICAgICBwYWdlcjogewogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMDBdCiAgICAgICAgfSwKICAgICAgICBvcHRpb246IHsKICAgICAgICAgIGNoZWNrQm94OiB0cnVlLAogICAgICAgICAgc2VyaWFsTnVtYmVyOiB0cnVlCiAgICAgICAgfSwKICAgICAgICB0YWJsZURhdGE6IFtdLAogICAgICAgIHRhYmxlSGVhZGVyOiBbCiAgICAgICAgICB7IHByb3A6ICJzc2dzbWMiLCBsYWJlbDogIuaJgOWxnuWFrOWPuCIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBwcm9wOiAiYmR6bWMiLCBsYWJlbDogIuaJgOWxnuWPmOeUteermSIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBwcm9wOiAic2JtYyIsIGxhYmVsOiAi5ZCN56ewIiwgbWluV2lkdGg6ICIxODAiIH0sCiAgICAgICAgICB7IHByb3A6ICJ4aCIsIGxhYmVsOiAi6KeE5qC85Z6L5Y+3IiwgbWluV2lkdGg6ICIxODAiIH0sCiAgICAgICAgICB7IHByb3A6ICJmenIiLCBsYWJlbDogIumihueUqOS6uiIsIG1pbldpZHRoOiAiMTgwIiB9LAogICAgICAgICAgLy8ge3Byb3A6ICdjY2JoJywgbGFiZWw6ICfnvJblj7cnLCBtaW5XaWR0aDogJzE4MCd9LAogICAgICAgICAgeyBwcm9wOiAianl6cSIsIGxhYmVsOiAi5L2/55So5bm06ZmQIiwgbWluV2lkdGg6ICIxODAiIH0sCiAgICAgICAgICB7IHByb3A6ICJ0eXJxIiwgbGFiZWw6ICLpooblj5bml7bpl7QiLCBtaW5XaWR0aDogIjI1MCIgfQogICAgICAgICAgLy8gewogICAgICAgICAgLy8gICBmaXhlZDogInJpZ2h0IiwKICAgICAgICAgIC8vICAgcHJvcDogJ29wZXJhdGlvbicsCiAgICAgICAgICAvLyAgIGxhYmVsOiAn5pON5L2cJywKICAgICAgICAgIC8vICAgbWluV2lkdGg6ICcxNTBweCcsCiAgICAgICAgICAvLyAgIHN0eWxlOiB7ZGlzcGxheTogJ2Jsb2NrJ30sCiAgICAgICAgICAvLyAgIG9wZXJhdGlvbjogWwogICAgICAgICAgLy8gICAgIHtuYW1lOiAn5L+u5pS5JywgY2xpY2tGdW46IHRoaXMudXBkYXRlR3FqSW5mb30sCiAgICAgICAgICAvLyAgICAge25hbWU6ICfor6bmg4UnLCBjbGlja0Z1bjogdGhpcy5nZXRHcWpJbmZvfSwKICAgICAgICAgIC8vICAgXSwKICAgICAgICAgIC8vIH0sCiAgICAgICAgXQogICAgICB9LAogICAgICAvL+etm+mAieadoeS7tgogICAgICBmaWx0ZXJJbmZvOiB7CiAgICAgICAgZGF0YTogewogICAgICAgICAgZnpyOiAiIiwKICAgICAgICAgIHNzZ3M6ICIiLAogICAgICAgICAgeXhiejogIiIsCiAgICAgICAgICBwaG9uZTogIiIKICAgICAgICB9LAogICAgICAgIGZpZWxkTGlzdDogWwogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIuWIhuWFrOWPuCIsCiAgICAgICAgICAgIHZhbHVlOiAic3NncyIsCiAgICAgICAgICAgIHR5cGU6ICJzZWxlY3QiLAogICAgICAgICAgICBvcHRpb25zOiBbXSwKICAgICAgICAgICAgY2xlYXJhYmxlOiB0cnVlCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIuWPmOeUteermSIsCiAgICAgICAgICAgIHZhbHVlOiAiYmR6IiwKICAgICAgICAgICAgdHlwZTogInNlbGVjdCIsCiAgICAgICAgICAgIG9wdGlvbnM6IFtdLAogICAgICAgICAgICBjbGVhcmFibGU6IHRydWUKICAgICAgICAgIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5ZCN56ewIiwgdHlwZTogImlucHV0IiwgdmFsdWU6ICJzYm1jIiB9LAogICAgICAgICAgeyBsYWJlbDogIuinhOagvOWei+WPtyIsIHR5cGU6ICJpbnB1dCIsIHZhbHVlOiAieGgiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi6aKG55So5Lq6IiwgdHlwZTogImlucHV0IiwgdmFsdWU6ICJmenIiIH0KICAgICAgICAgIC8vIHtsYWJlbDogJ+aKlei/kOaXpeacnycsIHR5cGU6ICdkYXRlJywgdmFsdWU6ICd0eXJxQXJyJyxkYXRlVHlwZTogJ2RhdGVyYW5nZScsZm9ybWF0OiAneXl5eS1NTS1kZCd9LAogICAgICAgIF0KICAgICAgfSwKICAgICAgLy/mo4Dkv67orrDlvZXlvLnlh7rmoYYKICAgICAgand4RGlhbG9nRm9ybVZpc2libGU6IGZhbHNlLAogICAgICAvL+a3u+WKoOajgOS/ruiusOW9leW8ueWHuuahhgogICAgICBhZGRKd3hTeWJnRGlhbG9nRm9ybVZpc2libGU6IGZhbHNlLAogICAgICAvL+W3peWZqOWFt+W8ueWHuuahhgogICAgICBkaWFsb2dGb3JtVmlzaWJsZTogZmFsc2UsCiAgICAgIC8v6K+V6aqM5pe26Ze0CiAgICAgIHN5c2o6ICIiLAogICAgICBmaWxkdHBzOiBbXSwKICAgICAgLy/or5XpqozlvLnlh7rmoYYKICAgICAgc3liZ0RpYWxvZ0Zvcm1WaXNpYmxlOiBmYWxzZSwKICAgICAgLy/mt7vliqDor5XpqozmiqXlkYoKICAgICAgYWRkU3liZ0RpYWxvZ0Zvcm1WaXNpYmxlOiBmYWxzZSwKICAgICAgLy/lvLnlh7rmoYbooajljZUKICAgICAgZm9ybTogewogICAgICAgIHR5cGU6ICJiZCIKICAgICAgfSwKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIC8v5bel5Zmo5YW36K+V6aqM5pWw5o2u6ZuG5ZCICiAgICAgIGdxanN5TGlzdDogW10sCiAgICAgIC8v5qOA5L+u5pWw5o2u6ZuG5ZCICiAgICAgIGdxakp4TGlzdDogW10sCiAgICAgIC8v5Yig6Zmk5piv5ZCm5Y+v55SoCiAgICAgIG11bHRpcGxlU2Vuc29yOiB0cnVlLAogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICAvL+WIoOmZpOmAieaLqeWIlwogICAgICBzZWxlY3RSb3dzOiBbXSwKICAgICAgLy/lt6Xlmajlhbfmlofku7bkuIrkvKDlj4LmlbAKICAgICAgZ3FqSW5mb1VwbG9hZERhdGE6IHsKICAgICAgICBidXNpbmVzc0lkOiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgLy/lt6Xlmajlhbfmlofku7bkuIrkvKDor7fmsYLlpLQKICAgICAgZ3FqSW5mb1VwSGVhZGVyOiB7fSwKCiAgICAgIC8v6K+V6aqM5p+l6K+i5p2h5Lu2CiAgICAgIHN5UXVlcnlGb3JtOiB7CiAgICAgICAgZ3FqSWQ6ICIiLAogICAgICAgIHRvdGFsOiAwLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBwYWdlTnVtOiAxCiAgICAgIH0sCgogICAgICAvL+ivlemqjOaWsOWinuihqOWNleaVsOaNrgogICAgICBzeUZyb206IHsKICAgICAgICBpZDogIiIsCiAgICAgICAgZ3FqSWQ6ICIiLAogICAgICAgIHN5ZHdJZDogIiIsCiAgICAgICAgc3lkd05hbWU6ICIiLAogICAgICAgIHN5cnlJZDogIiIsCiAgICAgICAgc3lyeU5hbWU6ICIiLAogICAgICAgIHN5c2o6ICIiLAogICAgICAgIHN5amxDb2RlOiAiIiwKICAgICAgICBzeWpsTmFtZTogIiIsCiAgICAgICAgcmVtYXJrOiAiIgogICAgICB9LAoKICAgICAgaXNTeURldGFpbDogZmFsc2UsCgogICAgICAvL+ajgOS/ruafpeivouadoeS7tgogICAgICBqeFF1ZXJ5Rm9ybTogewogICAgICAgIGdxaklkOiAiIiwKICAgICAgICB0b3RhbDogMCwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgcGFnZU51bTogMQogICAgICB9LAogICAgICAvL+ajgOS/ruihqOWNlQogICAgICBqeEZvcm06IHsKICAgICAgICBpZDogIiIsCiAgICAgICAganhkd0lkOiAiIiwKICAgICAgICBqeGR3TmFtZTogIiIsCiAgICAgICAganhyeUlkOiAiIiwKICAgICAgICBqeHJ5TmFtZTogIiIsCiAgICAgICAganhqZzogIiIsCiAgICAgICAganhzajogIiIsCiAgICAgICAgcmVtYXJrOiAiIiwKICAgICAgICBncWpJZDogIiIKICAgICAgfSwKCiAgICAgIC8v5Li76KGo6YCJ5Lit6KGM5pWw5o2uCiAgICAgIG1haW5Sb3dEYXRhOiB7fSwKICAgICAgLy/or5Xpqox0YWJsZeWKoOi9vQogICAgICBzeUxvYWRpbmc6IGZhbHNlLAogICAgICAvL+ivlemqjOmAieS4reihjAogICAgICBzeVNlbGVjdFJvd3M6IFtdLAogICAgICAvL+ajgOS/rnRhYmxl5Yqg6L29CiAgICAgIGp4TG9hZGluZzogZmFsc2UsCiAgICAgIC8v5qOA5L+u6YCJ5Lit6KGMCiAgICAgIGp4U2VsZWN0Um93czogW10KICAgIH07CiAgfSwKICB3YXRjaDoge30sCiAgY3JlYXRlZCgpIHsKICAgIC8v6I635Y+W57uE57uH57uT5p6E5LiL5ouJ5pWw5o2uCiAgICB0aGlzLmdldEZnc09wdGlvbnMoKTsKICB9LAogIG1vdW50ZWQoKSB7CiAgICBnZXRCZHpTZWxlY3RMaXN0KHt9KS50aGVuKGFzeW5jIHJlcyA9PiB7CiAgICAgIHRoaXMuYmR6QWxsTGlzdCA9IHJlcy5kYXRhOwogICAgICBsZXQgeyBkYXRhOiBiZGdxamx4IH0gPSBhd2FpdCBnZXREaWN0VHlwZURhdGEoImJkZ3FqbHgiKTsKICAgICAgdGhpcy5iZGdxamx4ID0gYmRncWpseDsKICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICB9KTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKgogICAgICog6I635Y+W5YiG5YWs5Y+45LiL5ouJ5pWw5o2uCiAgICAgKi8KICAgIGdldEZnc09wdGlvbnMoKSB7CiAgICAgIGdldEZnc09wdGlvbnMoe30pLnRoZW4ocmVzID0+IHsKICAgICAgICByZXMuZGF0YS5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgaXRlbS52YWx1ZSA9IGl0ZW0udmFsdWUudG9TdHJpbmcoKTsKICAgICAgICB9KTsKICAgICAgICB0aGlzLm9yZ2FuaXphdGlvblNlbGVjdGVkTGlzdCA9IHJlcy5kYXRhOwogICAgICAgIHRoaXMuZmlsdGVySW5mby5maWVsZExpc3QubWFwKGl0ZW0gPT4gewogICAgICAgICAgaWYgKGl0ZW0udmFsdWUgPT0gInNzZ3MiKSB7CiAgICAgICAgICAgIHJldHVybiAoaXRlbS5vcHRpb25zID0gdGhpcy5vcmdhbml6YXRpb25TZWxlY3RlZExpc3QpOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICBleHBvcnRFeGNlbCgpIHsKICAgICAgZXhwb3J0RXhjZWwodGhpcy5wYXJhbXMsICLlj5jnlLXlt6XlmajlhbciKTsKICAgIH0sCiAgICBmb3JtYXRCZHooaWQpIHsKICAgICAgaWYgKGlkKSB7CiAgICAgICAgcmV0dXJuIHRoaXMuYmR6QWxsTGlzdC5maWx0ZXIoZyA9PiBnLnZhbHVlID09PSBpZClbMF0ubGFiZWw7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuICIiOwogICAgICB9CiAgICB9LAogICAgLy/ojrflj5blj5jnlLXnq5nkuIvmi4nmoYYKICAgIGdldEJkekxpc3QoKSB7CiAgICAgIGdldEJkelNlbGVjdExpc3QoeyBzc2R3Ym06IHRoaXMuZm9ybS5zc2dzIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmJkekxpc3QgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAoKICAgIGhhbmRsZUV2ZW50KHZhbCwgZXZlbnRWYWx1ZSkgewogICAgICBpZiAodmFsLmxhYmVsID09PSAic3NncyIpIHsKICAgICAgICBnZXRCZHpTZWxlY3RMaXN0KHsgc3Nkd2JtOiB2YWwudmFsdWUudG9TdHJpbmcoKSB9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgICB0aGlzLmZpbHRlckluZm8uZmllbGRMaXN0Lm1hcChpdGVtID0+IHsKICAgICAgICAgICAgaWYgKGl0ZW0udmFsdWUgPT0gImJkeiIpIHsKICAgICAgICAgICAgICB0aGlzLiRzZXQoZXZlbnRWYWx1ZSwgImJkeiIsICIiKTsKICAgICAgICAgICAgICByZXR1cm4gKGl0ZW0ub3B0aW9ucyA9IHJlcy5kYXRhKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0sCiAgICAvKioKICAgICAqIOS4iuS8oOmZhOmZhOS7tuS5i+WJjeeahOWkhOeQhuWHveaVsAogICAgICogQHBhcmFtIGZpbGUKICAgICAqLwogICAgZ3FqSW5mb0JlZm9yZVVwbG9hZChmaWxlKSB7CiAgICAgIGNvbnN0IGZpbGVTaXplID0gZmlsZS5zaXplIDwgMTAyNCAqIDEwMjQgKiA1MDsgLy8xME0KICAgICAgaWYgKCFmaWxlU2l6ZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuS4iuS8oOaWh+S7tuWkp+Wwj+S4jeiDvei2hei/hyA1ME1CISIpOwogICAgICB9CiAgICAgIGxldCBzaXplID0gZmlsZS5zaXplIC8gMTAyNDsKICAgIH0sCiAgICAvKioKICAgICAqIOS4iuS8oOmZhOS7tuaIkOWKn+iwg+eUqOeahOWHveaVsAogICAgICogQHBhcmFtIHJlc3BvbnNlCiAgICAgKiBAcGFyYW0gZmlsZQogICAgICogQHBhcmFtIGZpbGVMaXN0CiAgICAgKi8KICAgIGdxakluZm9vblN1Y2Nlc3MocmVzcG9uc2UsIGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgIC8v5paH5Lu2aWQKICAgICAgdGhpcy5mb3JtLmF0dGFjaG1lbnRpZCA9IHJlc3BvbnNlLmRhdGEuYnVzaW5lc3NJZDsKICAgICAgLy/mlofku7blkI3np7AKICAgICAgdGhpcy5mb3JtLmF0dGFjaG1lbnRuYW1lID0gcmVzcG9uc2UuZGF0YS5zeXNGaWxlLmZpbGVPbGROYW1lOwogICAgfSwKCiAgICAvKioKICAgICAqIOenu+mZpOaWh+S7tgogICAgICogQHBhcmFtIGZpbGUKICAgICAqIEBwYXJhbSBmaWxlTGlzdAogICAgICovCiAgICBncWpJbmZvaGFuZGxlUmVtb3ZlKGZpbGUsIGZpbGVMaXN0KSB7fSwKICAgIC8qKgogICAgICog5bel5Zmo5YW35LiK5Lyg5paH5Lu25Yiw5pyN5Yqh5ZmoCiAgICAgKi8KICAgIGdxakluZm9TdWJtaXRVcGxvYWQoKSB7CiAgICAgIGRlYnVnZ2VyOwogICAgICB0aGlzLmdxakluZm9VcGxvYWREYXRhLmJ1c2luZXNzSWQgPSBnZXRVVUlEKCk7CiAgICAgIHRoaXMuJHJlZnMudXBsb2FkR3FqSW5mby5zdWJtaXQoKTsKICAgIH0sCiAgICBmb3JtYXRTc2dzKHNzZ3MpIHsKICAgICAgaWYgKHNzZ3MgJiYgc3NncyAhPT0gIjMwMDIiKSB7CiAgICAgICAgcmV0dXJuIHRoaXMub3JnYW5pemF0aW9uU2VsZWN0ZWRMaXN0LmZpbHRlcihnID0+IGcudmFsdWUgPT09IHNzZ3MpWzBdCiAgICAgICAgICAubGFiZWw7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuICLmuK/kuJzlj5jnlLXliIblhazlj7giOwogICAgICB9CiAgICB9LAogICAgLy/lt6XlmajlhbfliJfooajmn6Xor6IKICAgIGFzeW5jIGdldERhdGEocGFyYW1zKSB7CiAgICAgICAgdGhpcy5wYXJhbXMgPSB7IC4uLnRoaXMucGFyYW1zLCAuLi5wYXJhbXMgfTsKICAgICAgICBjb25zdCB7IGRhdGEsIGNvZGUgfSA9IGF3YWl0IGdldExpc3QodGhpcy5wYXJhbXMpOwogICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIGZvciAobGV0IGkgb2YgZGF0YS5yZWNvcmRzKSB7CiAgICAgICAgICAgIGkuc3Nnc21jID0gdGhpcy5mb3JtYXRTc2dzKGkuc3Nncyk7CiAgICAgICAgICAgIGkuYmR6bWMgPSB0aGlzLmZvcm1hdEJkeihpLmJkeik7CiAgICAgICAgICB9CiAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8udGFibGVEYXRhID0gZGF0YS5yZWNvcmRzOwogICAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnRvdGFsID0gZGF0YS50b3RhbDsKICAgICAgICB9CiAgICB9LAoKICAgIC8v5bel5Zmo5YW35YiX6KGo5paw5aKe5oyJ6ZKuCiAgICBhZGRTZW5zb3JCdXR0b24oKSB7CiAgICAgIC8v5byA5ZCv5by55Ye65qGG5YaF6L6T5YWl5qGG57yW6L6R5p2D6ZmQCiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICAvL+aJk+W8gOW8ueWHuuahhgogICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZTsKICAgICAgLy/orr7nva7lvLnlh7rmoYbooajlpLQKICAgICAgdGhpcy5ncWpUaXRhbCA9ICLlt6XlmajlhbfmlrDlop4iOwogICAgICAvL+a4heepuuW8ueWHuuahhuWGheWuuQogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgdHlwZTogImJkIgogICAgICB9OwogICAgfSwKICAgIC8v5bel5Zmo5YW35YiX6KGo6K+m5oOF5oyJ6ZKuCiAgICBnZXRHcWpJbmZvKHJvdykgewogICAgICAvL+aJk+W8gOW8ueWHuuahhgogICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZTsKICAgICAgLy/orr7nva7lvLnlh7rmoYbooajlpLQKICAgICAgdGhpcy5ncWpUaXRhbCA9ICLlt6Xlmajlhbfor6bmg4UiOwogICAgICAvL+emgeeUqOaJgOaciei+k+WFpeahhgogICAgICB0aGlzLmlzRGlzYWJsZWQgPSB0cnVlOwogICAgICAvL+e7meW8ueWHuuahhui1i+WAvAogICAgICB0aGlzLmZvcm0gPSB7IC4uLnJvdyB9OwogICAgICB0aGlzLmdldEJkekxpc3QoKTsKICAgIH0sCiAgICAvL+W3peWZqOWFt+S/ruaUueaMiemSrgogICAgdXBkYXRlR3FqSW5mbyhyb3cpIHsKICAgICAgLy/miZPlvIDlvLnlh7rmoYYKICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CiAgICAgIC8v6K6+572u5by55Ye65qGG6KGo5aS0CiAgICAgIHRoaXMuZ3FqVGl0YWwgPSAi5bel5Zmo5YW35L+u5pS5IjsKICAgICAgLy/lvIDlkK/lvLnlh7rmoYblhoXovpPlhaXmoYbnvJbovpHmnYPpmZAKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gZmFsc2U7CiAgICAgIC8v57uZ5by55Ye65qGG5YaF6LWL5YC8CiAgICAgIHRoaXMuZm9ybSA9IHsgLi4ucm93IH07CiAgICAgIHRoaXMuZ2V0QmR6TGlzdCgpOwogICAgfSwKICAgIC8v5bel5Zmo5YW35YiX6KGo5paw5aKe5L+u5pS55L+d5a2YCiAgICBhc3luYyBxeGNvbW1pdCgpIHsKICAgICAgYXdhaXQgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKGFzeW5jIHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgIGxldCB7IGNvZGUgfSA9IGF3YWl0IHNhdmVPclVwZGF0ZSh0aGlzLmZvcm0pOwogICAgICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLmk43kvZzmiJDlip8iKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgICAgICBjb25zb2xlLmxvZyhlKTsKICAgICAgICAgIH0KICAgICAgICAgIC8v5oGi5aSN5YiG6aG1CiAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIucGFnZVJlc2l6ZSA9ICJZIjsKICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy/liKDpmaTlt6XlmajlhbfliJfooagKICAgIGRlbGV0ZVJvdyhpZCkgewogICAgICAvLyBpZiAodGhpcy5zZWxlY3RSb3dzLmxlbmd0aCA8IDEpIHsKICAgICAgLy8gICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+mAieaLqeato+ehrueahOaVsOaNru+8ge+8ge+8gSIpCiAgICAgIC8vICAgcmV0dXJuCiAgICAgIC8vIH0KICAgICAgLy8gbGV0IGlkcyA9IHRoaXMuc2VsZWN0Um93cy5tYXAoaXRlbSA9PiB7CiAgICAgIC8vICAgcmV0dXJuIGl0ZW0ub2JqSWQKICAgICAgLy8gfSk7CiAgICAgIGxldCBvYmogPSBbXTsKICAgICAgb2JqLnB1c2goaWQpOwogICAgICB0aGlzLiRjb25maXJtKCLmraTmk43kvZzlsIbmsLjkuYXliKDpmaTor6XmlbDmja4sIOaYr+WQpue7p+e7rT8iLCAi5o+Q56S6IiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkKICAgICAgICAudGhlbigoKSA9PiB7CiAgICAgICAgICByZW1vdmUob2JqKS50aGVuKCh7IGNvZGUgfSkgPT4gewogICAgICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5Yig6Zmk5oiQ5YqfISIKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICJlcnJvciIsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5Yig6Zmk5aSx6LSlISIKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfSkKICAgICAgICAuY2F0Y2goKCkgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICJpbmZvIiwKICAgICAgICAgICAgbWVzc2FnZTogIuW3suWPlua2iOWIoOmZpCIKICAgICAgICAgIH0pOwogICAgICAgIH0pOwogICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIucGFnZVJlc2l6ZSA9ICJZIjsKICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICB9LAoKICAgIC8v5p+l55yL6K+V6aqMCiAgICBoYW5kbGVTZWFyY2hTWUNsaWNrKHJvdykgewogICAgICB0aGlzLnN5U2VsZWN0Um93cyA9IFtdOwogICAgICB0aGlzLm1haW5Sb3dEYXRhID0gcm93OwogICAgICB0aGlzLnN5UXVlcnlGb3JtLmdxaklkID0gcm93Lm9iaklkOwogICAgICB0aGlzLnN5YmdEaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CiAgICAgIHRoaXMuZ2V0WXhTeURhdGEoKTsKICAgIH0sCgogICAgLy/mn6XnnIvmo4Dkv64KICAgIGhhbmRsZVNlcmNoSldYQ2xpY2socm93KSB7CiAgICAgIHRoaXMubWFpblJvd0RhdGEgPSByb3c7CiAgICAgIHRoaXMuanhRdWVyeUZvcm0uZ3FqSWQgPSByb3cub2JqSWQ7CiAgICAgIHRoaXMuand4RGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgICB0aGlzLmdldEp4UmVjb3JkcygpOwogICAgfSwKICAgIC8v5re75Yqg5qOA5L+uCiAgICBhZGRKeEJ1dHRvbigpIHsKICAgICAgdGhpcy5qeEZvcm0gPSB0aGlzLiRvcHRpb25zLmRhdGEoKS5qeEZvcm07CiAgICAgIHRoaXMuanhGb3JtLmdxaklkID0gdGhpcy5tYWluUm93RGF0YS5vYmpJZDsKICAgICAgdGhpcy5hZGRKd3hTeWJnRGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgfSwKICAgIHVwZGF0ZUp4KHJvdykgewogICAgICB0aGlzLmp4Rm9ybSA9IHJvdzsKICAgICAgdGhpcy5hZGRKd3hTeWJnRGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgfSwKICAgIC8v5re75Yqg6K+V6aqMCiAgICBhZGRTeUJ1dHRvbigpIHsKICAgICAgdGhpcy5zeUZyb20gPSB0aGlzLiRvcHRpb25zLmRhdGEoKS5zeUZyb207CiAgICAgIHRoaXMuc3lGcm9tLmdxaklkID0gdGhpcy5tYWluUm93RGF0YS5vYmpJZDsKICAgICAgdGhpcy5hZGRTeWJnRGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgfSwKICAgIHVwZGF0ZVN5KHJvdykgewogICAgICB0aGlzLnN5RnJvbSA9IHJvdzsKICAgICAgdGhpcy5hZGRTeWJnRGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgfSwKICAgIC8v5q+P6aG15bGV56S65pWw6YeP54K55Ye75LqL5Lu2CiAgICBoYW5kbGVTaXplQ2hhbmdlKCkge30sCiAgICAvL+mhteeggeaUueWPmOS6i+S7tgogICAgaGFuZGxlQ3VycmVudENoYW5nZSgpIHt9LAogICAgLy/moJHngrnlh7vkuovku7YKICAgIGhhbmRsZU5vZGVDbGljaygpIHt9LAoKICAgIGZpbHRlclJlc2V0KCkge30sCiAgICAvL+mAieaLqeavj+S4gOihjAogICAgc2VsZWN0Q2hhbmdlKHJvd3MpIHsKICAgICAgdGhpcy5zZWxlY3RSb3dzID0gcm93czsKICAgIH0sCgogICAgLy/ojrflj5bor5XpqozorrDlvZXmlbDmja4KICAgIGdldFl4U3lEYXRhKCkgewogICAgICB0aGlzLnN5TG9hZGluZyA9IHRydWU7CiAgICAgIGdldFl4U3lSZWNvcmRzKHRoaXMuc3lRdWVyeUZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmdxanN5TGlzdCA9IHJlcy5kYXRhLnJlY29yZHM7CiAgICAgICAgdGhpcy5zeVF1ZXJ5Rm9ybS50b3RhbCA9IHJlcy5kYXRhLnRvdGFsOwogICAgICAgIHRoaXMuc3lMb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKCiAgICAvL+aWsOWinuS/ruaUueivlemqjOiusOW9leaVsOaNrgogICAgc2F2ZU9yVXBkYXRlU3koKSB7CiAgICAgIHNhdmVPclVwZGF0ZVl4U3lSZWNvcmRzKHRoaXMuc3lGcm9tKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5nZXRZeFN5RGF0YSgpOwogICAgICAgIHRoaXMuYWRkU3liZ0RpYWxvZ0Zvcm1WaXNpYmxlID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8v5om56YeP5Yig6Zmk6K+V6aqM5pWw5o2uCiAgICBkZWxldGVZeFN5KCkgewogICAgICB0aGlzLiRjb25maXJtKCLmraTmk43kvZzlsIbmsLjkuYXliKDpmaTor6XmlbDmja4sIOaYr+WQpue7p+e7rT8iLCAi5o+Q56S6IiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkKICAgICAgICAudGhlbigoKSA9PiB7CiAgICAgICAgICBsZXQgaWRzID0gW107CiAgICAgICAgICB0aGlzLnN5U2VsZWN0Um93cy5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgICBpZHMucHVzaChpdGVtLmlkKTsKICAgICAgICAgIH0pOwogICAgICAgICAgZGVsZXRlWXhTeVJlY29yZHMoaWRzKS50aGVuKCh7IGNvZGUgfSkgPT4gewogICAgICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5Yig6Zmk5oiQ5YqfISIKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICJlcnJvciIsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5Yig6Zmk5aSx6LSlISIKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfQogICAgICAgICAgICB0aGlzLmdldFl4U3lEYXRhKCk7CiAgICAgICAgICB9KTsKICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoKSA9PiB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogImluZm8iLAogICAgICAgICAgICBtZXNzYWdlOiAi5bey5Y+W5raI5Yig6ZmkIgogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICB9LAoKICAgIC8v6I635Y+W5qOA5L+u6K6w5b2V5pWw5o2uCiAgICBnZXRKeFJlY29yZHMoKSB7CiAgICAgIHRoaXMuanhMb2FkaW5nID0gdHJ1ZTsKICAgICAgZ2V0QXNzZXRHcWpKeFJlY29yZHModGhpcy5qeFF1ZXJ5Rm9ybSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuZ3FqSnhMaXN0ID0gcmVzLmRhdGEucmVjb3JkczsKICAgICAgICB0aGlzLmp4UXVlcnlGb3JtLnRvdGFsID0gcmVzLmRhdGEudG90YWw7CiAgICAgICAgdGhpcy5qeExvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLy/mlrDlop7kv67mlLnmo4Dkv67orrDlvZXmlbDmja4KICAgIHNhdmVPclVwZGF0ZUp4KCkgewogICAgICBzYXZlT3JVcGRhdGVBc3NldEdxakp4UmVjb3Jkcyh0aGlzLmp4Rm9ybSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuZ2V0SnhSZWNvcmRzKCk7CiAgICAgICAgdGhpcy5hZGRKd3hTeWJnRGlhbG9nRm9ybVZpc2libGUgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgZGVsZXRlSnhEYXRhKCkgewogICAgICB0aGlzLiRjb25maXJtKCLmraTmk43kvZzlsIbmsLjkuYXliKDpmaTor6XmlbDmja4sIOaYr+WQpue7p+e7rT8iLCAi5o+Q56S6IiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkKICAgICAgICAudGhlbigoKSA9PiB7CiAgICAgICAgICBsZXQgaWRzID0gW107CiAgICAgICAgICB0aGlzLmp4U2VsZWN0Um93cy5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgICBpZHMucHVzaChpdGVtLmlkKTsKICAgICAgICAgIH0pOwogICAgICAgICAgZGVsZXRlQXNzZXRHcWpKeFJlY29yZHMoaWRzKS50aGVuKCh7IGNvZGUgfSkgPT4gewogICAgICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5Yig6Zmk5oiQ5YqfISIKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICJlcnJvciIsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5Yig6Zmk5aSx6LSlISIKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfQogICAgICAgICAgICB0aGlzLmdldEp4UmVjb3JkcygpOwogICAgICAgICAgfSk7CiAgICAgICAgfSkKICAgICAgICAuY2F0Y2goKCkgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICJpbmZvIiwKICAgICAgICAgICAgbWVzc2FnZTogIuW3suWPlua2iOWIoOmZpCIKICAgICAgICAgIH0pOwogICAgICAgIH0pOwogICAgfSwKCiAgICBzeVJvd0NsaWNrKHJvd3MpIHsKICAgICAgdGhpcy4kcmVmcy5zeVRhYmxlLnRvZ2dsZVJvd1NlbGVjdGlvbihyb3dzKTsKICAgIH0sCiAgICBzeUN1cnJlbnRDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMuc3lTZWxlY3RSb3dzID0gdmFsOwogICAgfSwKICAgIGp4Um93Q2xpY2socm93cykgewogICAgICB0aGlzLiRyZWZzLmp4VGFibGUudG9nZ2xlUm93U2VsZWN0aW9uKHJvd3MpOwogICAgfSwKICAgIGp4Q3VycmVudENoYW5nZSh2YWwpIHsKICAgICAgdGhpcy5qeFNlbGVjdFJvd3MgPSB2YWw7CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["bdgqj.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "bdgqj.vue", "sourceRoot": "src/views/dagangOilfield/dwzygl/gqjgl", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      ref=\"filter1\"\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      @handleReset=\"filterReset\"\n      @handleEvent=\"handleEvent\"\n    />\n    <el-white class=\"button-group1\">\n      <div class=\"button_btn\">\n        <el-button\n          @click=\"addSensorButton\"\n          v-hasPermi=\"['bdgql:button:add']\"\n          icon=\"el-icon-plus\"\n          type=\"primary\"\n          >新增\n        </el-button>\n        <el-button type=\"primary\" icon=\"el-icon-download\" @click=\"exportExcel()\"\n          >导出</el-button\n        >\n      </div>\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"selectChange\"\n        height=\"63vh\"\n      >\n        <el-table-column\n          slot=\"table_eight\"\n          align=\"center\"\n          fixed=\"right\"\n          style=\"display: block\"\n          label=\"操作\"\n          width=\"180\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              @click=\"getGqjInfo(scope.row)\"\n              class=\"el-icon-view\"\n              title=\"详情\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"scope.row.createBy == currUser\"\n              @click=\"updateGqjInfo(scope.row)\"\n              class=\"el-icon-edit\"\n              title=\"编辑\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"scope.row.createBy == currUser\"\n              @click=\"deleteRow(scope.row.objId)\"\n              class=\"el-icon-delete\"\n              title=\"删除\"\n            ></el-button>\n          </template>\n        </el-table-column>\n      </comp-table>\n    </el-white>\n    <!-- 工器具台账详情修改新增 -->\n    <el-dialog\n      :title=\"gqjTital\"\n      :visible.sync=\"dialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form\n        :model=\"form\"\n        label-width=\"80px\"\n        :disabled=\"isDisabled\"\n        :rules=\"rules\"\n        ref=\"form\"\n      >\n        <el-row>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属公司\" prop=\"ssgs\">\n              <el-select\n                v-model=\"form.ssgs\"\n                placeholder=\"所属公司\"\n                clearable\n                :disabled=\"isDisabled\"\n                @change=\"getBdzList\"\n              >\n                <el-option\n                  v-for=\"item in organizationSelectedList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属变电站\" prop=\"bdz\">\n              <el-select\n                placeholder=\"请选择变电站\"\n                clearable\n                v-model=\"form.bdz\"\n                :disabled=\"isDisabled\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in bdzList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"名称\" prop=\"sbmc\">\n              <el-input v-model=\"form.sbmc\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"设备分类\" prop=\"sblx\">\n              <el-select\n                placeholder=\"请选择\"\n                clearable\n                v-model=\"form.sblx\"\n                :disabled=\"isDisabled\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in bdgqjlx\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"规格型号\" prop=\"xh\">\n              <el-input v-model=\"form.xh\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <!--          <el-col :span=\"8\">\n                      <el-form-item label=\"编号\" style=\"width: 100%\">\n                        <el-input v-model=\"form.ccbh\" :disabled=\"isDisabled\"></el-input>\n                      </el-form-item>\n                    </el-col>-->\n          <el-col :span=\"8\">\n            <el-form-item label=\"数量\" prop=\"sl\">\n              <el-input-number\n                :min=\"1\"\n                v-model=\"form.sl\"\n                :disabled=\"isDisabled\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"使用年限\">\n              <el-input v-model=\"form.jyzq\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"领用人\" prop=\"fzr\">\n              <el-input v-model=\"form.fzr\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"批准人\" prop=\"cfdd\">\n              <el-input v-model=\"form.cfdd\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"领取时间\" prop=\"tyrq\">\n              <el-date-picker\n                v-model=\"form.tyrq\"\n                type=\"date\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"选择日期\"\n                :disabled=\"isDisabled\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\">\n              <el-input\n                type=\"textarea\"\n                v-model=\"form.bz\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"qxcommit\"\n          v-if=\"gqjTital === '工器具新增' || gqjTital === '工器具修改'\"\n          class=\"pmyBtn\"\n        >\n          确 定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getUUID } from \"@/utils/ruoyi\";\nimport {\n  deleteAssetGqjJxRecords,\n  deleteYxSyRecords,\n  getAssetGqjJxRecords,\n  getList,\n  getYxSyRecords,\n  remove,\n  saveOrUpdate,\n  saveOrUpdateAssetGqjJxRecords,\n  saveOrUpdateYxSyRecords,\n  exportExcel\n} from \"@/api/dagangOilfield/asset/assetGqj\";\nimport CompTable from \"com/CompTable\";\nimport ElFilter from \"com/ElFilter\";\nimport { getBdzSelectList } from \"@/api/dagangOilfield/bzgl/lpbzk/dqgzzqpz\";\nimport { getDictTypeData } from \"@/api/system/dict/data\";\nimport { getFgsOptions, getSelectOptionsByOrgType } from \"@/api/yxgl/bdyxgl/zbgl\";\n\nexport default {\n  components: { CompTable, ElFilter },\n  name: \"gqjgl\",\n  data() {\n    return {\n      bdgqjlx: [],\n      currUser: this.$store.getters.name,\n      params: {\n        type: \"bd\"\n      },\n      bdzAllList: [],\n      bdzList: [],\n      //组织结构下拉数据\n      organizationSelectedList: [],\n      // 表单校验\n      rules: {\n        sl: [{ required: true, message: \"请输入\", trigger: \"blur\" }],\n        ssgs: [{ required: true, message: \"请选择\", trigger: \"blur\" }],\n        // bdz: [{ required: true, message: '请选择', trigger: 'blur' }],\n        sbmc: [{ required: true, message: \"请输入\", trigger: \"blur\" }],\n        sblx: [{ required: true, message: \"请选择\", trigger: \"blur\" }],\n        fzr: [{ required: true, message: \"请输入\", trigger: \"blur\" }]\n      },\n      //工器具详情框字段控制\n      isDisabled: false,\n      //工器具弹出框表头\n      gqjTital: \"工器具新增\",\n      //表格内容\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"ssgsmc\", label: \"所属公司\", minWidth: \"120\" },\n          { prop: \"bdzmc\", label: \"所属变电站\", minWidth: \"120\" },\n          { prop: \"sbmc\", label: \"名称\", minWidth: \"180\" },\n          { prop: \"xh\", label: \"规格型号\", minWidth: \"180\" },\n          { prop: \"fzr\", label: \"领用人\", minWidth: \"180\" },\n          // {prop: 'ccbh', label: '编号', minWidth: '180'},\n          { prop: \"jyzq\", label: \"使用年限\", minWidth: \"180\" },\n          { prop: \"tyrq\", label: \"领取时间\", minWidth: \"250\" }\n          // {\n          //   fixed: \"right\",\n          //   prop: 'operation',\n          //   label: '操作',\n          //   minWidth: '150px',\n          //   style: {display: 'block'},\n          //   operation: [\n          //     {name: '修改', clickFun: this.updateGqjInfo},\n          //     {name: '详情', clickFun: this.getGqjInfo},\n          //   ],\n          // },\n        ]\n      },\n      //筛选条件\n      filterInfo: {\n        data: {\n          fzr: \"\",\n          ssgs: \"\",\n          yxbz: \"\",\n          phone: \"\"\n        },\n        fieldList: [\n          {\n            label: \"分公司\",\n            value: \"ssgs\",\n            type: \"select\",\n            options: [],\n            clearable: true\n          },\n          {\n            label: \"变电站\",\n            value: \"bdz\",\n            type: \"select\",\n            options: [],\n            clearable: true\n          },\n          { label: \"名称\", type: \"input\", value: \"sbmc\" },\n          { label: \"规格型号\", type: \"input\", value: \"xh\" },\n          { label: \"领用人\", type: \"input\", value: \"fzr\" }\n          // {label: '投运日期', type: 'date', value: 'tyrqArr',dateType: 'daterange',format: 'yyyy-MM-dd'},\n        ]\n      },\n      //检修记录弹出框\n      jwxDialogFormVisible: false,\n      //添加检修记录弹出框\n      addJwxSybgDialogFormVisible: false,\n      //工器具弹出框\n      dialogFormVisible: false,\n      //试验时间\n      sysj: \"\",\n      fildtps: [],\n      //试验弹出框\n      sybgDialogFormVisible: false,\n      //添加试验报告\n      addSybgDialogFormVisible: false,\n      //弹出框表单\n      form: {\n        type: \"bd\"\n      },\n      loading: false,\n      //工器具试验数据集合\n      gqjsyList: [],\n      //检修数据集合\n      gqjJxList: [],\n      //删除是否可用\n      multipleSensor: true,\n      showSearch: true,\n      //删除选择列\n      selectRows: [],\n      //工器具文件上传参数\n      gqjInfoUploadData: {\n        businessId: undefined\n      },\n      //工器具文件上传请求头\n      gqjInfoUpHeader: {},\n\n      //试验查询条件\n      syQueryForm: {\n        gqjId: \"\",\n        total: 0,\n        pageSize: 10,\n        pageNum: 1\n      },\n\n      //试验新增表单数据\n      syFrom: {\n        id: \"\",\n        gqjId: \"\",\n        sydwId: \"\",\n        sydwName: \"\",\n        syryId: \"\",\n        syryName: \"\",\n        sysj: \"\",\n        syjlCode: \"\",\n        syjlName: \"\",\n        remark: \"\"\n      },\n\n      isSyDetail: false,\n\n      //检修查询条件\n      jxQueryForm: {\n        gqjId: \"\",\n        total: 0,\n        pageSize: 10,\n        pageNum: 1\n      },\n      //检修表单\n      jxForm: {\n        id: \"\",\n        jxdwId: \"\",\n        jxdwName: \"\",\n        jxryId: \"\",\n        jxryName: \"\",\n        jxjg: \"\",\n        jxsj: \"\",\n        remark: \"\",\n        gqjId: \"\"\n      },\n\n      //主表选中行数据\n      mainRowData: {},\n      //试验table加载\n      syLoading: false,\n      //试验选中行\n      sySelectRows: [],\n      //检修table加载\n      jxLoading: false,\n      //检修选中行\n      jxSelectRows: []\n    };\n  },\n  watch: {},\n  created() {\n    //获取组织结构下拉数据\n    this.getFgsOptions();\n  },\n  mounted() {\n    getBdzSelectList({}).then(async res => {\n      this.bdzAllList = res.data;\n      let { data: bdgqjlx } = await getDictTypeData(\"bdgqjlx\");\n      this.bdgqjlx = bdgqjlx;\n      this.getData();\n    });\n  },\n  methods: {\n    /**\n     * 获取分公司下拉数据\n     */\n    getFgsOptions() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.organizationSelectedList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"ssgs\") {\n            return (item.options = this.organizationSelectedList);\n          }\n        });\n      });\n    },\n    exportExcel() {\n      exportExcel(this.params, \"变电工器具\");\n    },\n    formatBdz(id) {\n      if (id) {\n        return this.bdzAllList.filter(g => g.value === id)[0].label;\n      } else {\n        return \"\";\n      }\n    },\n    //获取变电站下拉框\n    getBdzList() {\n      getBdzSelectList({ ssdwbm: this.form.ssgs }).then(res => {\n        this.bdzList = res.data;\n      });\n    },\n\n    handleEvent(val, eventValue) {\n      if (val.label === \"ssgs\") {\n        getBdzSelectList({ ssdwbm: val.value.toString() }).then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value == \"bdz\") {\n              this.$set(eventValue, \"bdz\", \"\");\n              return (item.options = res.data);\n            }\n          });\n        });\n      }\n    },\n    /**\n     * 上传附附件之前的处理函数\n     * @param file\n     */\n    gqjInfoBeforeUpload(file) {\n      const fileSize = file.size < 1024 * 1024 * 50; //10M\n      if (!fileSize) {\n        this.$message.error(\"上传文件大小不能超过 50MB!\");\n      }\n      let size = file.size / 1024;\n    },\n    /**\n     * 上传附件成功调用的函数\n     * @param response\n     * @param file\n     * @param fileList\n     */\n    gqjInfoonSuccess(response, file, fileList) {\n      //文件id\n      this.form.attachmentid = response.data.businessId;\n      //文件名称\n      this.form.attachmentname = response.data.sysFile.fileOldName;\n    },\n\n    /**\n     * 移除文件\n     * @param file\n     * @param fileList\n     */\n    gqjInfohandleRemove(file, fileList) {},\n    /**\n     * 工器具上传文件到服务器\n     */\n    gqjInfoSubmitUpload() {\n      debugger;\n      this.gqjInfoUploadData.businessId = getUUID();\n      this.$refs.uploadGqjInfo.submit();\n    },\n    formatSsgs(ssgs) {\n      if (ssgs && ssgs !== \"3002\") {\n        return this.organizationSelectedList.filter(g => g.value === ssgs)[0]\n          .label;\n      } else {\n        return \"港东变电分公司\";\n      }\n    },\n    //工器具列表查询\n    async getData(params) {\n        this.params = { ...this.params, ...params };\n        const { data, code } = await getList(this.params);\n        if (code === \"0000\") {\n          for (let i of data.records) {\n            i.ssgsmc = this.formatSsgs(i.ssgs);\n            i.bdzmc = this.formatBdz(i.bdz);\n          }\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n        }\n    },\n\n    //工器具列表新增按钮\n    addSensorButton() {\n      //开启弹出框内输入框编辑权限\n      this.isDisabled = false;\n      //打开弹出框\n      this.dialogFormVisible = true;\n      //设置弹出框表头\n      this.gqjTital = \"工器具新增\";\n      //清空弹出框内容\n      this.form = {\n        type: \"bd\"\n      };\n    },\n    //工器具列表详情按钮\n    getGqjInfo(row) {\n      //打开弹出框\n      this.dialogFormVisible = true;\n      //设置弹出框表头\n      this.gqjTital = \"工器具详情\";\n      //禁用所有输入框\n      this.isDisabled = true;\n      //给弹出框赋值\n      this.form = { ...row };\n      this.getBdzList();\n    },\n    //工器具修改按钮\n    updateGqjInfo(row) {\n      //打开弹出框\n      this.dialogFormVisible = true;\n      //设置弹出框表头\n      this.gqjTital = \"工器具修改\";\n      //开启弹出框内输入框编辑权限\n      this.isDisabled = false;\n      //给弹出框内赋值\n      this.form = { ...row };\n      this.getBdzList();\n    },\n    //工器具列表新增修改保存\n    async qxcommit() {\n      await this.$refs[\"form\"].validate(async valid => {\n        if (valid) {\n          try {\n            let { code } = await saveOrUpdate(this.form);\n            if (code === \"0000\") {\n              this.$message.success(\"操作成功\");\n            }\n          } catch (e) {\n            console.log(e);\n          }\n          //恢复分页\n          this.tableAndPageInfo.pager.pageResize = \"Y\";\n          this.getData();\n          this.dialogFormVisible = false;\n        }\n      });\n    },\n    //删除工器具列表\n    deleteRow(id) {\n      // if (this.selectRows.length < 1) {\n      //   this.$message.warning(\"请选择正确的数据！！！\")\n      //   return\n      // }\n      // let ids = this.selectRows.map(item => {\n      //   return item.objId\n      // });\n      let obj = [];\n      obj.push(id);\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove(obj).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n      this.tableAndPageInfo.pager.pageResize = \"Y\";\n      this.getData();\n    },\n\n    //查看试验\n    handleSearchSYClick(row) {\n      this.sySelectRows = [];\n      this.mainRowData = row;\n      this.syQueryForm.gqjId = row.objId;\n      this.sybgDialogFormVisible = true;\n      this.getYxSyData();\n    },\n\n    //查看检修\n    handleSerchJWXClick(row) {\n      this.mainRowData = row;\n      this.jxQueryForm.gqjId = row.objId;\n      this.jwxDialogFormVisible = true;\n      this.getJxRecords();\n    },\n    //添加检修\n    addJxButton() {\n      this.jxForm = this.$options.data().jxForm;\n      this.jxForm.gqjId = this.mainRowData.objId;\n      this.addJwxSybgDialogFormVisible = true;\n    },\n    updateJx(row) {\n      this.jxForm = row;\n      this.addJwxSybgDialogFormVisible = true;\n    },\n    //添加试验\n    addSyButton() {\n      this.syFrom = this.$options.data().syFrom;\n      this.syFrom.gqjId = this.mainRowData.objId;\n      this.addSybgDialogFormVisible = true;\n    },\n    updateSy(row) {\n      this.syFrom = row;\n      this.addSybgDialogFormVisible = true;\n    },\n    //每页展示数量点击事件\n    handleSizeChange() {},\n    //页码改变事件\n    handleCurrentChange() {},\n    //树点击事件\n    handleNodeClick() {},\n\n    filterReset() {},\n    //选择每一行\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n\n    //获取试验记录数据\n    getYxSyData() {\n      this.syLoading = true;\n      getYxSyRecords(this.syQueryForm).then(res => {\n        this.gqjsyList = res.data.records;\n        this.syQueryForm.total = res.data.total;\n        this.syLoading = false;\n      });\n    },\n\n    //新增修改试验记录数据\n    saveOrUpdateSy() {\n      saveOrUpdateYxSyRecords(this.syFrom).then(res => {\n        this.getYxSyData();\n        this.addSybgDialogFormVisible = false;\n      });\n    },\n    //批量删除试验数据\n    deleteYxSy() {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          let ids = [];\n          this.sySelectRows.forEach(item => {\n            ids.push(item.id);\n          });\n          deleteYxSyRecords(ids).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n            this.getYxSyData();\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n\n    //获取检修记录数据\n    getJxRecords() {\n      this.jxLoading = true;\n      getAssetGqjJxRecords(this.jxQueryForm).then(res => {\n        this.gqjJxList = res.data.records;\n        this.jxQueryForm.total = res.data.total;\n        this.jxLoading = false;\n      });\n    },\n    //新增修改检修记录数据\n    saveOrUpdateJx() {\n      saveOrUpdateAssetGqjJxRecords(this.jxForm).then(res => {\n        this.getJxRecords();\n        this.addJwxSybgDialogFormVisible = false;\n      });\n    },\n    deleteJxData() {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          let ids = [];\n          this.jxSelectRows.forEach(item => {\n            ids.push(item.id);\n          });\n          deleteAssetGqjJxRecords(ids).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n            this.getJxRecords();\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n\n    syRowClick(rows) {\n      this.$refs.syTable.toggleRowSelection(rows);\n    },\n    syCurrentChange(val) {\n      this.sySelectRows = val;\n    },\n    jxRowClick(rows) {\n      this.$refs.jxTable.toggleRowSelection(rows);\n    },\n    jxCurrentChange(val) {\n      this.jxSelectRows = val;\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.button-group {\n  padding-left: 30px;\n  padding-right: 30px;\n  display: flex;\n  justify-content: flex-end;\n}\n\n.qxlr_dialog_insert {\n  margin-top: 6vh !important;\n}\n\n/*/deep/ .qxlr_dialog_insert .el-input--medium .el-input__inner{*/\n/*  width: 100%;*/\n/*}*/\n.el-select {\n  width: 100%;\n}\n\n.el-date-editor {\n  width: 100%;\n}\n</style>\n"]}]}