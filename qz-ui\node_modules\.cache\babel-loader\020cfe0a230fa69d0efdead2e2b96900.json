{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\lpbzk\\dqgzzqpz.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\lpbzk\\dqgzzqpz.js", "mtime": 1730862690371}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagang<PERSON>field/bzgl/lpbzk/dqgzzqpz.js"], "names": ["baseUrl", "getList", "params", "api", "requestPost", "saveOrUpdate", "remove", "JSON", "stringify", "getBdzSelectList", "param", "getGfBdzSelectList", "startJob", "chack<PERSON>ob", "<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,cAAhB,C,CAEA;;AACO,SAASC,OAAT,CAAiBC,MAAjB,EAAyB;AAC9B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,gBAAxB,EAAyCE,MAAzC,EAAgD,CAAhD,CAAP;AACD,C,CAED;;;AACO,SAASG,YAAT,CAAsBH,MAAtB,EAA8B;AACnC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,wBAAxB,EAAiDE,MAAjD,EAAwD,CAAxD,CAAP;AACD,C,CACD;;;AACO,SAASI,MAAT,CAAgBJ,MAAhB,EAAwB;AAC7B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,kBAAxB,EAA2CO,IAAI,CAACC,SAAL,CAAeN,MAAf,CAA3C,EAAkE,CAAlE,CAAP;AACD;AAED;;;;;AAGO,SAASO,gBAAT,CAA0BC,KAA1B,EAAiC;AACtC,SAAOP,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,6BAAxB,EAAsDU,KAAtD,EAA4D,CAA5D,CAAP;AACD;AAED;;;;;AAGO,SAASC,kBAAT,CAA4BD,KAA5B,EAAmC;AACxC,SAAOP,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,+BAAxB,EAAwDU,KAAxD,EAA8D,CAA9D,CAAP;AACD,C,CAED;;;AACO,SAASE,QAAT,CAAkBF,KAAlB,EAAyB;AAC9B,SAAOP,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,oBAA1B,EAA+CO,IAAI,CAACC,SAAL,CAAeE,KAAf,CAA/C,EAAsE,CAAtE,CAAP;AAED,C,CAED;;;AACO,SAASG,QAAT,CAAkBH,KAAlB,EAAyB;AAC9B,SAAOP,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,oBAA1B,EAA+CO,IAAI,CAACC,SAAL,CAAeE,KAAf,CAA/C,EAAsE,CAAtE,CAAP;AAED,C,CAED;;;AACO,SAASI,MAAT,CAAgBJ,KAAhB,EAAuB;AAC5B,SAAOP,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,kBAA1B,EAA6CO,IAAI,CAACC,SAAL,CAAeE,KAAf,CAA7C,EAAoE,CAApE,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\nconst baseUrl = \"/manager-api\";\n\n// 查询专业票操作术语表\nexport function getList(params) {\n  return api.requestPost(baseUrl+'/bzDqgzpz/page',params,1)\n}\n\n// 添加或修改\nexport function saveOrUpdate(params) {\n  return api.requestPost(baseUrl+'/bzDqgzpz/saveOrUpdate',params,1)\n}\n// 添加或修改\nexport function remove(params) {\n  return api.requestPost(baseUrl+'/bzDqgzpz/remove',JSON.stringify(params),1)\n}\n\n/**\n * 变电站下拉框数据查询\n * */\nexport function getBdzSelectList(param) {\n  return api.requestPost(baseUrl+'/equipList/getBdzSelectList',param,1)\n}\n\n/**\n * 光伏变电站下拉框数据查询\n * */\nexport function getGfBdzSelectList(param) {\n  return api.requestPost(baseUrl+'/equipList/getGfBdzSelectList',param,1)\n}\n\n// 启动任务\nexport function startJob(param) {\n  return api.requestPost(baseUrl + '/bzDqgzpz/startJob',JSON.stringify(param), 1)\n\n}\n\n// 启动一次\nexport function chackJob(param) {\n  return api.requestPost(baseUrl + '/bzDqgzpz/chackJob',JSON.stringify(param), 1)\n\n}\n\n// 结束任务\nexport function endJob(param) {\n  return api.requestPost(baseUrl + '/bzDqgzpz/endJob',JSON.stringify(param), 1)\n}\n\n"]}]}