{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sympk\\mpxqInfo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sympk\\mpxqInfo.vue", "mtime": 1706897323738}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["mpxqInfo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwJA;;AACA;;AAOA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,KAAA,EAAA;AACA,IAAA,MAAA,EAAA;AACA,MAAA,IAAA,EAAA;AADA,KADA;AAIA,IAAA,MAAA,EAAA;AACA,MAAA,IAAA,EAAA;AADA;AAJA,GADA;AASA,EAAA,IAAA,EAAA,UATA;AAUA,EAAA,MAAA,EAAA,CAAA,QAAA,CAVA;AAUA;AACA,EAAA,IAXA,kBAWA;AACA,WAAA;AACA;AACA,MAAA,EAAA,EAAA,EAFA;AAGA,MAAA,EAAA,EAAA,EAHA;AAIA;AACA,MAAA,KAAA,EAAA,EALA;AAMA,MAAA,KAAA,EAAA,EANA;AAOA;AACA,MAAA,QAAA,EAAA,EARA;AASA;AACA,MAAA,MAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,EAAA,EAAA,EAJA;AAKA,QAAA,MAAA,EAAA;AALA,OAVA;AAiBA,MAAA,KAAA,EAAA,SAjBA;AAkBA,MAAA,IAAA,EAAA;AACA,QAAA,KAAA,EAAA,SADA;AAEA,QAAA,QAAA,EAAA,SAFA;AAGA,QAAA,IAAA,EAAA,SAHA;AAIA,QAAA,IAAA,EAAA,SAJA;AAKA,QAAA,IAAA,EAAA,SALA;AAMA,QAAA,UAAA,EAAA;AANA,OAlBA;AA0BA,MAAA,OAAA,EAAA,IA1BA;AA0BA;AACA,MAAA,OAAA,EAAA,CA3BA;AA2BA;AACA,MAAA,KAAA,EAAA,IAAA,GAAA,EA5BA;AA4BA;AACA,MAAA,SAAA,EAAA,KAAA,MA7BA;AA6BA;AACA,MAAA,IAAA,EAAA,KA9BA;AA+BA,MAAA,QAAA,EAAA,EA/BA;AAgCA,MAAA,UAAA,EAAA,KAhCA;AAiCA,MAAA,WAAA,EAAA,KAjCA;AAkCA,MAAA,QAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,QAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,CAlCA;AAsCA,MAAA,YAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,CAtCA;AA0CA,MAAA,YAAA,EAAA,EA1CA;AA0CA;AACA,MAAA,cAAA,EAAA,EA3CA,CA2CA;;AA3CA,KAAA;AA6CA,GAzDA;AA0DA,EAAA,OA1DA,qBA0DA;AACA;AACA,SAAA,aAAA;AACA,GA7DA;AA8DA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,aAFA,2BAEA;AACA;AACA,WAAA,OAAA,GAAA,mBAAA,OAAA,CAAA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,UAAA,EAAA;AAFA,OAAA,CAAA;AAIA,WAAA,EAAA,GAAA,OAAA,KAAA,MAAA,CAAA,GAAA,IAAA,WAAA,GAAA,KAAA,MAAA,CAAA,GAAA,GAAA,KAAA,MAAA,CAAA,GAAA;AACA,WAAA,EAAA,GAAA,OAAA,KAAA,MAAA,CAAA,GAAA,IAAA,WAAA,GAAA,KAAA,MAAA,CAAA,GAAA,GAAA,KAAA,MAAA,CAAA,GAAA,CAPA,CASA;;AACA,WAAA,gBAAA,CAAA,CAAA,IAAA,EAAA,IAAA,CAAA;AACA,WAAA,YAAA;AACA,WAAA,OAAA,CAAA,KAAA,GAZA,CAYA;AACA,KAfA;AAgBA;AACA,IAAA,YAjBA,0BAiBA;AACA,UAAA,KAAA,GAAA,QAAA,CAAA,cAAA,CAAA,YAAA,CAAA;;AACA,UAAA,KAAA,IAAA,IAAA,EAAA;AACA,QAAA,KAAA,CAAA,SAAA,GAAA,EAAA;AACA,YAAA,EAAA,GAAA,KAAA,EAAA;AACA,YAAA,EAAA,GAAA,KAAA,EAAA;AACA,aAAA,OAAA,GAAA,MAAA,MAAA,CAAA,EAAA,CAAA;AACA,YAAA,GAAA,GAAA,EAAA;;AACA,aAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,EAAA,EAAA,CAAA,EAAA,EAAA;AACA,cAAA,IAAA,GAAA,MAAA;;AACA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,SAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,gBAAA,IAAA,GAAA,KAAA,SAAA,CAAA,CAAA,CAAA;AACA,gBAAA,IAAA,GAAA,IAAA,CAAA,IAAA,IAAA,IAAA,GAAA,GAAA,GAAA,IAAA,CAAA,IAAA;;AACA,gBAAA,IAAA,CAAA,QAAA,KAAA,CAAA,CAAA,QAAA,EAAA,EAAA;AACA,cAAA,IAAA,IACA,4BACA,IAAA,CAAA,KADA,GAEA,iDAFA,GAGA,KAAA,OAAA,GAAA,IAAA,CAAA,OAHA,GAIA,cAJA,GAKA,IAAA,CAAA,OALA,GAMA,aANA,GAOA,IAAA,CAAA,OAPA,GAQA,IARA,GASA,IATA,GAUA,OAXA;AAYA;AACA;;AACA,UAAA,IAAA,IAAA,OAAA;AACA,UAAA,GAAA,IAAA,IAAA;AACA;;AACA,QAAA,KAAA,CAAA,SAAA,GAAA,GAAA,CA7BA,CA8BA;;AACA,aAAA,aAAA;AACA;AACA,KApDA;AAqDA;AACA,IAAA,WAtDA,yBAsDA;AAAA;;AACA;AACA,WAAA,OAAA,GAAA,mBAAA,OAAA,CAAA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,UAAA,EAAA;AAFA,OAAA,CAAA;AAIA,UAAA,MAAA,GAAA,IAAA,CAAA,SAAA,CAAA;AACA,QAAA,KAAA,EAAA,KAAA,MAAA,CAAA,KADA;AACA;AACA,QAAA,GAAA,EAAA,MAAA,CAAA,KAAA,EAAA,CAFA;AAEA;AACA,QAAA,GAAA,EAAA,MAAA,CAAA,KAAA,EAAA,CAHA;AAGA;AACA,QAAA,IAAA,EAAA,GAJA,CAIA;;AAJA,OAAA,CAAA;AAMA,iCAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,QAAA,EAAA,GAAA;;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,KAAA,CAAA,WAAA;AACA,SAFA,MAEA;AACA,UAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;;AACA,UAAA,KAAA,CAAA,OAAA,CAAA,KAAA,GAFA,CAEA;;AACA;AACA,OARA;AASA,KA3EA;AA4EA;AACA,IAAA,aA7EA,2BA6EA;AACA,UAAA,KAAA,GAAA,QAAA,CAAA,sBAAA,CAAA,QAAA,CAAA,CADA,CACA;;AACA,UAAA,QAAA,GAAA,QAAA,CAAA,sBAAA,CAAA,WAAA,CAAA,CAFA,CAEA;;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,UAAA,KAAA,IAAA,IAAA,EAAA;AACA;AACA,aAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,UAAA,KAAA,CAAA,CAAA,CAAA,CAAA,OAAA,GAAA,YAAA;AACA,YAAA,IAAA,CAAA,QAAA,GAAA,IAAA;AACA,YAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,QAAA,CAAA,OAAA;AACA,YAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,QAAA,CAAA,OAAA;AACA,YAAA,IAAA,CAAA,QAAA,GAAA,IAAA,CAAA,UAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA;AACA,WALA;AAMA;AACA;;AACA,UAAA,QAAA,IAAA,IAAA,EAAA;AACA;AACA,aAAA,IAAA,EAAA,GAAA,CAAA,EAAA,EAAA,GAAA,QAAA,CAAA,MAAA,EAAA,EAAA,EAAA,EAAA;AACA,UAAA,QAAA,CAAA,EAAA,CAAA,CAAA,OAAA,GAAA,YAAA;AACA,YAAA,IAAA,CAAA,QAAA,GAAA,IAAA;AACA,YAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,QAAA,CAAA,OAAA;AACA,YAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,QAAA,CAAA,OAAA;AACA,WAJA;AAKA;AACA;AACA,KAtGA;AAuGA;AACA,IAAA,eAxGA,6BAwGA;AAAA;;AACA;AACA,WAAA,OAAA,GAAA,mBAAA,OAAA,CAAA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,UAAA,EAAA;AAFA,OAAA,CAAA,CAFA,CAMA;;AACA;;;;;;AAMA,UAAA,MAAA,GAAA,IAAA,CAAA,SAAA,CAAA;AACA,QAAA,KAAA,EAAA,KAAA,QAAA,CAAA,EADA;AAEA,QAAA,OAAA,EAAA,KAAA,KAFA;AAGA,QAAA,OAAA,EAAA,KAAA;AAHA,OAAA,CAAA,CAbA,CAkBA;;AACA,gCAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,WAAA;AACA,SAFA,MAEA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;;AACA,UAAA,MAAA,CAAA,OAAA,CAAA,KAAA,GAFA,CAEA;;AACA;AACA,OAPA;AAQA,KAnIA;AAoIA;AACA,IAAA,SArIA,qBAqIA,GArIA,EAqIA;AAAA;;AACA;AACA,UAAA,OAAA,GAAA,KAAA,QAAA,CAAA,EAAA;AACA,UAAA,IAAA,GAAA,EAAA,CAHA,CAGA;AACA;;AACA,UAAA,KAAA,KAAA,CAAA,GAAA,CAAA,OAAA,CAAA,EAAA;AACA,aAAA,KAAA,CAAA,GAAA,CAAA,OAAA,EAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,IAAA,IAAA,EAAA;AACA,YAAA,MAAA,CAAA,SAAA,CAAA,IAAA;;AACA,YAAA,IAAA,CAAA,KAAA,CAAA,OAAA,GAAA,YAAA;AACA;AACA,SALA,EADA,CAOA;;AACA,aAAA,KAAA,CAAA,MAAA,CAAA,OAAA;AACA;;AACA,UAAA,UAAA,GAAA,EAAA,CAfA,CAeA;AAEA;;AACA,UAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA;AACA,QAAA,GAAA,CAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,GAAA,QAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CADA,CAEA;;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA,CAAA,EAAA;AACA,YAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA,EAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,MAAA,CAAA,SAAA,CAAA,IAAA;;AACA,cAAA,IAAA,CAAA,KAAA,CAAA,OAAA,GAAA,YAAA;AACA,aAHA,EADA,CAKA;;;AACA,YAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,GAAA;AACA,WAVA,CAWA;;;AACA,cAAA,GAAA,EAAA;AACA,gBAAA,SAAA,GAAA,GAAA,CAAA,SAAA;;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,SAAA,CAAA,EAAA;AACA,kBAAA,SAAA,GAAA,QAAA,CAAA,cAAA,CAAA,SAAA,CAAA,CADA,CACA;;AACA,kBAAA,SAAA,EAAA;AACA,gBAAA,MAAA,CAAA,SAAA,CAAA,SAAA;AACA;;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,SAAA,EAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA;AACA,oBAAA,CAAA,GAAA,CAAA,QAAA,CAAA,IAAA,CAAA,EAAA;AACA,kBAAA,IAAA,CAAA,KAAA,CAAA,OAAA,GAAA,YAAA;AACA,iBAFA,MAEA;AACA,kBAAA,IAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,eAPA,EALA,CAaA;;;AACA,kBAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,SAAA,EAAA,IAAA;AACA,eAFA,MAEA;AACA;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,SAAA;AACA;AACA;AACA;AACA,SApCA,EAFA,CAuCA;;AACA,QAAA,GAAA,CAAA,OAAA,CAAA,UAAA,EAAA,EAAA;AACA,cAAA,GAAA,GAAA,QAAA,CAAA,cAAA,CAAA,EAAA,CAAA,CADA,CAEA;;AACA,cAAA,GAAA,EAAA;AACA,YAAA,UAAA,CAAA,IAAA,CAAA,GAAA,EADA,CACA;;AAEA,YAAA,QAAA,CAAA,cAAA,CAAA,EAAA,EAAA,KAAA,CAAA,OAAA,GAAA,MAAA,CAHA,CAIA;;AACA,YAAA,QAAA,CAAA,cAAA,CAAA,EAAA,EAAA,SAAA,GAAA,OAAA;AACA;AACA,SAVA,EAxCA,CAmDA;;AACA,aAAA,KAAA,CAAA,GAAA,CAAA,OAAA,EAAA,UAAA;AACA;AACA,KA7MA;AA8MA;AACA,IAAA,gBA/MA,8BA+MA,CAAA,CA/MA;AAiNA,IAAA,WAjNA,uBAiNA,GAjNA,EAiNA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,MAAA,GAAA,GAAA,KAAA,YAAA,CAAA,IAAA,CAAA,UAAA,IAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA,KAAA,GAAA;AACA,OAFA,CAAA;;AAGA,UAAA,GAAA,CAAA,KAAA,KAAA,GAAA,EAAA;AACA,aAAA,IAAA,CAAA,IAAA,GAAA,QAAA;AACA,aAAA,WAAA,GAAA,IAAA;AACA,OAHA,MAGA;AACA,aAAA,IAAA,CAAA,IAAA,GAAA,MAAA;AACA,aAAA,WAAA,GAAA,KAAA;AACA;;AACA,WAAA,UAAA,GAAA,IAAA,CAZA,CAaA;AACA,KA/NA;AAiOA;AACA,IAAA,cAlOA,4BAkOA;AACA,WAAA,IAAA,GAAA,KAAA;AACA,WAAA,IAAA,GAAA,EAAA;AACA,KArOA;AAuOA;AACA,IAAA,UAxOA,sBAwOA,EAxOA,EAwOA,EAxOA,EAwOA,IAxOA,EAwOA,IAxOA,EAwOA;AACA,UAAA,EAAA,KAAA,CAAA,EAAA;AACA;AACA,YAAA,EAAA,IAAA,CAAA,EAAA;AACA,eAAA,UAAA,CAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,EAAA;AACA;AACA,OALA,MAKA;AACA,YAAA,EAAA,GAAA,CAAA,EAAA;AACA;AACA;AACA,cAAA,EAAA,KAAA,CAAA,EAAA;AACA,iBAAA,SAAA,CAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,EAAA;AACA,WAFA,MAEA,IAAA,EAAA,GAAA,CAAA,EAAA;AACA;AACA,iBAAA,iBAAA,CAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,EAAA;AACA;AACA;AACA,OAjBA,CAkBA;;;AACA,WAAA,QAAA,CAAA,KAAA,CAAA,KAAA,GAAA,KAAA,OAAA,GAAA,EAAA,GAAA,GAAA,CAnBA,CAmBA;;AACA,WAAA,QAAA,CAAA,OAAA,GAAA,KAAA,KAAA;AACA,WAAA,QAAA,CAAA,OAAA,GAAA,KAAA,KAAA;AACA,KA9PA;;AA+PA;;;;;;;AAOA,IAAA,UAtQA,sBAsQA,CAtQA,EAsQA,CAtQA,EAsQA,EAtQA,EAsQA,EAtQA,EAsQA;AACA,UAAA,SAAA,GAAA,EAAA,CADA,CACA;;AACA,UAAA,KAAA,GAAA,EAAA,CAFA,CAEA;;AACA,UAAA,EAAA,GAAA,KAAA,EAAA,GAAA,CAAA,EAAA;AACA;AACA,QAAA,KAAA,GAAA,KAAA,EAAA,GAAA,CAAA;AACA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,EAAA,CAAA,EAAA,EAAA;AACA,QAAA,SAAA,CAAA,IAAA,CAAA,CAAA,GAAA,GAAA,IAAA,CAAA,GAAA,CAAA,CAAA;AACA,OATA,CAUA;;;AACA,WAAA,SAAA,CAAA,SAAA;AACA,KAlRA;;AAmRA;;;;;;;AAOA,IAAA,SA1RA,qBA0RA,CA1RA,EA0RA,CA1RA,EA0RA,EA1RA,EA0RA,EA1RA,EA0RA;AACA,UAAA,SAAA,GAAA,EAAA,CADA,CACA;;AACA,UAAA,KAAA,GAAA,EAAA,CAFA,CAEA;;AACA,UAAA,EAAA,GAAA,KAAA,EAAA,GAAA,CAAA,EAAA;AACA;AACA,QAAA,KAAA,GAAA,KAAA,EAAA,GAAA,CAAA;AACA;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,KAAA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,EAAA,CAAA,EAAA,EAAA;AACA,QAAA,SAAA,CAAA,IAAA,CAAA,CAAA,GAAA,CAAA,GAAA,GAAA,GAAA,CAAA;AACA,OAVA,CAWA;;;AACA,WAAA,SAAA,CAAA,SAAA;AACA,KAvSA;;AAwSA;;;;;;;AAOA,IAAA,iBA/SA,6BA+SA,CA/SA,EA+SA,CA/SA,EA+SA,EA/SA,EA+SA,EA/SA,EA+SA;AACA,UAAA,SAAA,GAAA,EAAA,CADA,CACA;;AACA,UAAA,QAAA,GAAA,EAAA,CAFA,CAGA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,EAAA,EAAA,CAAA,EAAA,EAAA;AACA;AACA,aAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,EAAA,EAAA,CAAA,EAAA,EAAA;AACA;AACA,UAAA,QAAA,GAAA,CAAA,GAAA,CAAA,GAAA,GAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAFA,CAGA;;AACA,cAAA,QAAA,KAAA,CAAA,GAAA,GAAA,GAAA,CAAA,EAAA;AACA,YAAA,SAAA,CAAA,IAAA,CAAA,QAAA;AACA;AACA;AACA,OAdA,CAeA;;;AACA,WAAA,SAAA,CAAA,SAAA;AACA,KAhUA;AAiUA;AACA,IAAA,gBAlUA,4BAkUA,IAlUA,EAkUA;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,YAAA,GAAA,GAAA,QAAA,CAAA,cAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;;AACA,YAAA,GAAA,IAAA,IAAA,IAAA,OAAA,GAAA,IAAA,WAAA,EAAA;AACA,kBAAA,IAAA,CAAA,CAAA,CAAA;AACA,iBAAA,IAAA;AACA,cAAA,GAAA,CAAA,KAAA,GAAA,KAAA,EAAA;AACA;;AACA,iBAAA,IAAA;AACA,cAAA,GAAA,CAAA,KAAA,GAAA,KAAA,EAAA;AACA;;AACA,iBAAA,OAAA;AACA,cAAA,GAAA,CAAA,KAAA,GAAA,KAAA,KAAA;AACA;;AACA,iBAAA,OAAA;AACA,cAAA,GAAA,CAAA,KAAA,GAAA,KAAA,KAAA;AACA;AAZA;AAcA;AACA;AACA,KAtVA;AAuVA;AACA,IAAA,UAxVA,wBAwVA;AAAA;;AACA;AACA,WAAA,OAAA,GAAA,mBAAA,OAAA,CAAA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,UAAA,EAAA;AAFA,OAAA,CAAA;AAIA,UAAA,KAAA,GAAA,KAAA,QAAA,CAAA,EAAA;AACA,UAAA,MAAA,GAAA,KAAA,UAAA,CAAA,KAAA,CAAA;AACA,gCAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,WAAA;AACA,SAFA,MAEA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;;AACA,QAAA,MAAA,CAAA,OAAA,CAAA,KAAA,GANA,CAMA;;AACA,OAPA;AAQA,KAxWA;AAyWA;AACA,IAAA,WA1WA,yBA0WA;AACA,WAAA,IAAA,GAAA,IAAA,CADA,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KA7XA;AA+XA;AACA,IAAA,OAhYA,qBAgYA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,MAAA,CAAA,IAAA,CAAA,KAAA,GAAA,MAAA,CAAA,QAAA,CAAA,KAAA;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,IAAA,GAAA,MAAA,CAAA,QAAA,CAAA,IAAA;AAHA;AAAA,uBAIA,yBAAA,MAAA,CAAA,IAAA,CAJA;;AAAA;AAAA;AAIA,gBAAA,IAJA,oBAIA,IAJA;;AAKA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,WAAA;;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA;;AARA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAUA,gBAAA,MAAA,CAAA,IAAA,GAAA,KAAA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KA3YA;AA6YA;AACA,IAAA,SA9YA,qBA8YA,GA9YA,EA8YA;AACA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,KAAA,CAAA,KAAA,GAAA,KAAA,OAAA,GAAA,GAAA;AACA,QAAA,GAAA,CAAA,OAAA,GAAA,GAAA;AACA,QAAA,GAAA,CAAA,OAAA,GAAA,GAAA;AACA;AACA,KApZA;AAqZA;AACA,IAAA,UAtZA,sBAsZA,GAtZA,EAsZA,UAtZA,EAsZA;AACA,cAAA,UAAA;AACA,aAAA,IAAA;AACA,eAAA,EAAA,GAAA,GAAA;AACA;;AACA,aAAA,IAAA;AACA,eAAA,EAAA,GAAA,GAAA;AACA;;AACA,aAAA,OAAA;AACA,eAAA,KAAA,GAAA,GAAA;AACA;;AACA,aAAA,OAAA;AACA,eAAA,KAAA,GAAA,GAAA;AACA;AAZA;AAcA,KAraA;AAsaA;AACA,IAAA,aAvaA,yBAuaA,EAvaA,EAuaA,EAvaA,EAuaA;AACA,UAAA,MAAA,GAAA,IAAA;AACA,WAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YACA,IAAA,CAAA,QAAA,KAAA,EAAA,CAAA,QAAA,EAAA,IACA,IAAA,CAAA,QAAA,KAAA,EAAA,CAAA,QAAA,EAFA,EAGA;AACA,UAAA,MAAA,GAAA,IAAA;;AACA,cAAA,MAAA,CAAA,IAAA,IAAA,IAAA,EAAA;AACA,YAAA,MAAA,CAAA,IAAA,GAAA,EAAA;AACA;AACA;AACA,OAVA;AAWA,aAAA,MAAA;AACA,KArbA;AAsbA;AACA,IAAA,UAvbA,sBAubA,KAvbA,EAubA;AACA,UAAA,MAAA,GAAA,IAAA;AACA,WAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,KAAA,KAAA,KAAA,EAAA;AACA,UAAA,MAAA,GAAA,IAAA;AACA;AACA,OAJA;AAKA,aAAA,MAAA;AACA,KA/bA;AAgcA;AACA,IAAA,WAjcA,yBAicA;AAAA;;AACA,UAAA,KAAA,GAAA,IAAA,CAAA,SAAA,CAAA;AACA,QAAA,MAAA,EAAA,KAAA,MAAA,CAAA,KADA;AAEA,QAAA,IAAA,EAAA;AAFA,OAAA,CAAA,CADA,CAKA;;AACA,2BAAA,KAAA,EAAA,IAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,IAAA;;AACA,YAAA,IAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,SAAA,GAAA,IAAA,CAAA,IAAA,CADA,CAEA;;AACA,UAAA,MAAA,CAAA,YAAA;;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA;AACA,SALA,MAKA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,eAAA;AACA;;AACA,QAAA,MAAA,CAAA,OAAA,CAAA,KAAA,GAVA,CAUA;;AACA,OAXA;AAYA;AAndA;AA9DA,C", "sourcesContent": ["<template>\n  <div class=\"mpxq_info\">\n    <div id=\"mpxq_left\">\n      <ul class=\"ul1_cont\">\n        <li>表格</li>\n        <li>\n          行：<el-input\n            id=\"hs\"\n            v-model=\"hs\"\n            placeholder=\"\"\n            class=\"inp1\"\n            @input=\"(val) => checkInput(val, 'hs')\"\n          ></el-input>\n        </li>\n        <li>\n          列：<el-input\n            id=\"ls\"\n            v-model=\"ls\"\n            placeholder=\"\"\n            class=\"inp1\"\n            @input=\"(val) => checkInput(val, 'ls')\"\n          ></el-input>\n        </li>\n        <el-button type=\"success\" @click=\"createTable\" class=\"change_btn\"\n          >创建表格</el-button\n        >\n      </ul>\n      <ul class=\"ul2_cont\">\n        <li>单元格操作</li>\n        <li>\n          行跨度：<el-input\n            id=\"addhs\"\n            v-model=\"addhs\"\n            placeholder=\"\"\n            class=\"inp2\"\n            @input=\"(val) => checkInput(val, 'addhs')\"\n          ></el-input>\n        </li>\n        <li>\n          列跨度：<el-input\n            id=\"addls\"\n            v-model=\"addls\"\n            placeholder=\"\"\n            class=\"inp2\"\n            @input=\"(val) => checkInput(val, 'addls')\"\n          ></el-input>\n        </li>\n        <el-button type=\"success\" @click=\"saveChangeTable\" class=\"change_btn\"\n          >保存</el-button\n        >\n        <el-button @click=\"clearChangeTable\" class=\"change_btn\">清除</el-button>\n      </ul>\n      <ul class=\"ul3_cont\">\n        <el-button type=\"warning\" @click=\"saveTdValue\">编辑单元格</el-button>\n        <el-button type=\"warning\" @click=\"resetTable\">重置单元格</el-button>\n      </ul>\n    </div>\n    <table id=\"mpxq_right\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\"></table>\n    <!--    <table class=\"mpxq_right\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n      <tr>\n        <td>变电站</td>\n        <td>委托单位</td>\n        <td>试验单位</td>\n        <td>运行编号</td>\n      </tr>\n      <tr>\n        <td>试验性质</td>\n        <td>试验日期</td>\n        <td>试验人员</td>\n        <td>试验地点</td>\n      </tr>\n      <tr>\n        <td>报告日期</td>\n        <td>编写人</td>\n        <td>审核人</td>\n        <td>批准人</td>\n      </tr>\n      <tr>\n        <td>试验天气</td>\n        <td>环境温度（℃）</td>\n        <td>环境相对湿度（%）</td>\n        <td>投运日期</td>\n      </tr>\n    </table>-->\n\n    <el-dialog\n      :title=\"title\"\n      v-dialogDrag\n      :visible.sync=\"show\"\n      width=\"50%\"\n      append-to-body\n      @close=\"getInsterClose\"\n    >\n      <el-form label-width=\"130px\" ref=\"form\" :model=\"form\">\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否只读：\" prop=\"readonly\">\n              <el-select\n                placeholder=\"请选择是否只读\"\n                v-model=\"form.readonly\"\n                style=\"width: 100%\"\n                @change=\"selectvalue\"\n              >\n                <el-option\n                  v-for=\"item in readonlyList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"内容类型：：\" prop=\"nrlx\">\n              <el-select\n                placeholder=\"请选择内容类型\"\n                v-model=\"form.nrlx\"\n                style=\"width: 100%\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option\n                  v-for=\"item in nrlxList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"属性名称：\" prop=\"nrbs\">\n              <el-input\n                v-model=\"form.nrbs\"\n                placeholder=\"请输入属性名称\"\n                :disabled=\"isDisabledN\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"getInsterClose\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"saveRow\">保 存</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { Loading } from \"element-ui\";\nimport {\n  resetCells,\n  createTable,\n  mergeCells,\n  editCells,\n  getCells,\n} from \"@/api/dagangOilfield/bzgl/sympk/sympInfo\";\nimport { getTable } from \"@/api/dagangOilfield/bzgl/sympk/sympk\";\n\nexport default {\n  props: {\n    mpData: {\n      type: Object,\n    },\n    mxData: {\n      type: Array,\n    },\n  },\n  name: \"mpxqInfo\",\n  inject: [\"reload\"], //inject注入根组件的reload方法\n  data() {\n    return {\n      //初始表格的行数 列数\n      hs: \"\",\n      ls: \"\",\n      //初始合并行数 列数\n      addhs: \"\",\n      addls: \"\",\n      //选中合并行、列的tr\n      changeTr: \"\",\n      //查询条件\n      params: {\n        pageNum: 1,\n        pageSize: 10,\n        mpid: \"\",\n        zy: \"\",\n        sblxbm: \"\",\n      },\n      title: \"单元格属性定义\",\n      form: {\n        objId: undefined,\n        readonly: undefined,\n        nrlx: undefined,\n        nrbs: undefined,\n        mpid: undefined,\n        readonlyMc: undefined,\n      },\n      loading: null, //遮罩层\n      tdWidth: 0, //一个单元格所占宽度\n      tdMap: new Map(), //用于存放被合并或拆分的单元格（key:当前点击的单元格,value:被处理过的单元格数组）\n      tableData: this.mxData, //表格数据\n      show: false,\n      cellData: \"\",\n      isDisabled: false,\n      isDisabledN: false,\n      nrlxList: [\n        { label: \"静态文本\", value: \"静态文本\" },\n        { label: \"铭牌实验数据\", value: \"铭牌实验数据\" },\n      ],\n      readonlyList: [\n        { label: \"是\", value: \"Y\" },\n        { label: \"否\", value: \"N\" },\n      ],\n      readonlyvalu: \"\", //选择值\n      readonlySelect: \"\", //选择值显示值\n    };\n  },\n  mounted() {\n    //获取表格初始行数和列数\n    this.initTableData();\n  },\n  methods: {\n    //获取铭牌内容数据\n    initTableData() {\n      //初始化遮罩层\n      this.loading = Loading.service({\n        text: \"加载中，请稍后...\",\n        background: \"rgba(109,106,106,0.35)\",\n      });\n      this.hs = typeof (this.mpData.AHs) != 'undefined'?this.mpData.AHs:this.mpData.aHs;\n      this.ls = typeof (this.mpData.ALs) != 'undefined'?this.mpData.ALs:this.mpData.aLs;\n\n      //更新输入框的值\n      this.updateInputValue([\"hs\", \"ls\"]);\n      this.processTable();\n      this.loading.close(); //关闭遮罩层\n    },\n    //根据行数和列数创建表格\n    processTable() {\n      var tbody = document.getElementById(\"mpxq_right\");\n      if (tbody != null) {\n        tbody.innerHTML = \"\";\n        let hs = this.hs;\n        let ls = this.ls;\n        this.tdWidth = 100 / Number(ls);\n        let str = \"\";\n        for (let i = 0; i < hs; i++) {\n          let temp = \"<tr>\";\n          for (let j = 0; j < this.tableData.length; j++) {\n            let item = this.tableData[j];\n            let nrbs = item.nrbs == null ? \"-\" : item.nrbs;\n            if (item.rowindex === i.toString()) {\n              temp +=\n                \"<td class='trName' id='\" +\n                item.objId +\n                \"' style='min-width:60px;max-width:180px;width: \" +\n                this.tdWidth * item.colspan +\n                \"%' rowspan='\" +\n                item.rowspan +\n                \"' colspan='\" +\n                item.colspan +\n                \"'>\" +\n                nrbs +\n                \"</td>\";\n            }\n          }\n          temp += \"</tr>\";\n          str += temp;\n        }\n        tbody.innerHTML = str;\n        // //给循环出来的单元格加上点击事件\n        this.addClickEvent();\n      }\n    },\n    //手动创建表格\n    createTable() {\n      //初始化遮罩层\n      this.loading = Loading.service({\n        text: \"加载中，请稍后...\",\n        background: \"rgba(109,106,106,0.35)\",\n      });\n      let params = JSON.stringify({\n        objId: this.mpData.objId, //铭牌id\n        aHs: Number(this.hs), //行数\n        aLs: Number(this.ls), //列数\n        lbbs: \"A\", //类别标识，表示修改的A表格\n      });\n      createTable(params).then((res) => {\n        console.log('手动创建表格',res)\n        if (res.code === \"0000\") {\n          this.updateTable();\n        } else {\n          this.$message.error(\"操作失败\");\n          this.loading.close(); //关闭遮罩层\n        }\n      });\n    },\n    //给循环出来的单元格加上点击事件\n    addClickEvent() {\n      let trArr = document.getElementsByClassName(\"trName\"); //不可编辑的单元格\n      let inputArr = document.getElementsByClassName(\"input_cls\"); //可编辑的单元格\n      let that = this;\n      if (trArr != null) {\n        //循环所有的tr\n        for (let i = 0; i < trArr.length; i++) {\n          trArr[i].onclick = function () {\n            that.changeTr = this;\n            that.addhs = that.changeTr.rowSpan;\n            that.addls = that.changeTr.colSpan;\n            that.cellData = that.getCellEle(that.changeTr.id);\n          };\n        }\n      }\n      if (inputArr != null) {\n        //循环所有的tr\n        for (let i = 0; i < inputArr.length; i++) {\n          inputArr[i].onclick = function () {\n            that.changeTr = this;\n            that.addhs = that.changeTr.rowSpan;\n            that.addls = that.changeTr.colSpan;\n          };\n        }\n      }\n    },\n    //合并拆分保存\n    saveChangeTable() {\n      //初始化遮罩层\n      this.loading = Loading.service({\n        text: \"加载中，请稍后...\",\n        background: \"rgba(109,106,106,0.35)\",\n      });\n      //看要合并的是第几行几列的tr   id格式  0|0 0|1\n      /*let id = this.changeTr.id;\n      let addh = Number(id.split('|')[0]);  //取当前元素的行\n      let addl = Number(id.split('|')[1]);  //取当前元素的列\n      let hs = Number(this.addhs);//行数\n      let ls = Number(this.addls);//列数*/\n\n      let params = JSON.stringify({\n        objId: this.changeTr.id,\n        rowspan: this.addhs,\n        colspan: this.addls,\n      });\n      //先请求接口，如果后台可以执行合并或拆分，则将最新的表格数据请求回来进行前端展示\n      mergeCells(params).then((res) => {\n        if (res.code === \"0000\") {\n          this.updateTable();\n        } else {\n          this.$message.error(\"操作失败\");\n          this.loading.close(); //关闭遮罩层\n        }\n      });\n    },\n    //处理合并或拆分\n    processTr(ids) {\n      //点击的单元格id\n      let clickId = this.changeTr.id;\n      let arr1 = []; //需要重新设置map的数组\n      //如果之前已经处理过该单元格,则先将其还原\n      if (this.tdMap.has(clickId)) {\n        this.tdMap.get(clickId).forEach((item) => {\n          if (item != null) {\n            this.resetCell(item);\n            item.style.display = \"table-cell\";\n          }\n        });\n        //操作完后将数据从map中删除\n        this.tdMap.delete(clickId);\n      }\n      let processEle = []; //被处理的元素\n\n      //现将连带受影响的单元格还原，再进行隐藏处理\n      if (ids.length > 0) {\n        //执行还原\n        ids.forEach((id1) => {\n          let ele = document.getElementById(id1);\n          //如果此次处理的单元格中有已经被处理过的，先将其还原\n          if (this.tdMap.has(id1)) {\n            this.tdMap.get(id1).forEach((item) => {\n              this.resetCell(item);\n              item.style.display = \"table-cell\";\n            });\n            //操作完后将数据从map中删除\n            this.tdMap.delete(id1);\n          }\n          //处理被连带的已经合并过的单元格\n          if (ele) {\n            let className = ele.className;\n            if (this.tdMap.has(className)) {\n              let mergeCell = document.getElementById(className); //被连带的已经合并过的cell\n              if (mergeCell) {\n                this.resetCell(mergeCell);\n              }\n              this.tdMap.get(className).forEach((item) => {\n                //需要把此次要隐藏的单元格排除掉，不然隐藏完下次循环又会放出来\n                if (!ids.includes(item)) {\n                  item.style.display = \"table-cell\";\n                } else {\n                  arr1.push(item);\n                }\n              });\n              //处理完成后，更新map中的值，将处理过的排除掉\n              if (arr1.length > 0) {\n                this.tdMap.set(className, arr1);\n              } else {\n                //操作完后将数据从map中删除\n                this.tdMap.delete(className);\n              }\n            }\n          }\n        });\n        //执行隐藏\n        ids.forEach((id) => {\n          let ele = document.getElementById(id);\n          //将多余的单元格隐藏\n          if (ele) {\n            processEle.push(ele); //添加数据保存到map中\n\n            document.getElementById(id).style.display = \"none\";\n            //将className设置给被操作的单元格，方便下次有连带操作时对单元格进行处理\n            document.getElementById(id).className = clickId;\n          }\n        });\n        //重新设置map中的值\n        this.tdMap.set(clickId, processEle);\n      }\n    },\n    //取消更改的合并行、列数\n    clearChangeTable() {},\n\n    selectvalue(val) {\n      let obj = {};\n      obj = this.readonlyList.find((item) => {\n        return item.value === val;\n      });\n      if (obj.value === \"N\") {\n        this.form.nrlx = \"铭牌实验数据\";\n        this.isDisabledN = true;\n      } else {\n        this.form.nrlx = \"静态文本\";\n        this.isDisabledN = false;\n      }\n      this.isDisabled = true;\n      // this.form.readonly = +obj.value;\n    },\n\n    //关闭弹窗\n    getInsterClose() {\n      this.show = false;\n      this.form = {};\n    },\n\n    //进行合并或拆分操作\n    mergeTable(hs, ls, addh, addl) {\n      if (hs === 1) {\n        //合并列\n        if (ls >= 1) {\n          this.mergeCells(addh, addl, hs, ls);\n        }\n      } else {\n        if (hs > 1) {\n          //多行\n          //合并行\n          if (ls === 1) {\n            this.mergeRows(addh, addl, hs, ls);\n          } else if (ls > 1) {\n            //合并多行多列\n            this.mergeRowsAndCells(addh, addl, hs, ls);\n          }\n        }\n      }\n      //要合并的单元格进行合并\n      this.changeTr.style.width = this.tdWidth * ls + \"%\"; //设置合并后的单元格宽度\n      this.changeTr.rowSpan = this.addhs;\n      this.changeTr.colSpan = this.addls;\n    },\n    /**\n     * 第一种情况，合并列（一行多列）\n     * @param h 当前元素所在行\n     * @param l 当前元素所在列\n     * @param hs 要合并的行数\n     * @param ls 要合并的列数\n     */\n    mergeCells(h, l, hs, ls) {\n      let removeIds = []; //要删除的元素的id数组\n      let ls_xh = ls; //要循环的列数\n      if (ls > this.ls - l) {\n        //不能超过剩余可操作的列数\n        ls_xh = this.ls - l;\n      }\n      for (let i = 1; i < ls_xh; i++) {\n        removeIds.push(h + \"|\" + (l + i));\n      }\n      //删除多余单元格\n      this.processTr(removeIds);\n    },\n    /**\n     * 第二种情况，合并行（多行一列）\n     * @param h 当前元素所在行\n     * @param l 当前元素所在列\n     * @param hs 要合并的行数\n     * @param ls 要合并的列数\n     */\n    mergeRows(h, l, hs, ls) {\n      let removeIds = []; //要删除的元素的id数组\n      let hs_xh = hs; //要循环的行数\n      if (hs > this.hs - h) {\n        //不能超过剩余可操作的行数\n        hs_xh = this.hs - h;\n      }\n      console.log(\"hs_xh\", hs_xh);\n      for (let i = 1; i < hs_xh; i++) {\n        removeIds.push(h + i + \"|\" + l);\n      }\n      //删除多余单元格\n      this.processTr(removeIds);\n    },\n    /**\n     * 第三种情况，合并多行多列\n     * @param h 当前元素所在行\n     * @param l 当前元素所在列\n     * @param hs 要合并的行数\n     * @param ls 要合并的列数\n     */\n    mergeRowsAndCells(h, l, hs, ls) {\n      let removeIds = []; //要删除的元素的id数组\n      let removeId = \"\";\n      //先循环行（从当前行开始循环）\n      for (let j = 0; j < hs; j++) {\n        //循环列\n        for (let i = 0; i < ls; i++) {\n          //从当前列循环\n          removeId = h + j + \"|\" + (l + i);\n          //将当前单元格排除掉\n          if (removeId !== h + \"|\" + l) {\n            removeIds.push(removeId);\n          }\n        }\n      }\n      //删除多余单元格\n      this.processTr(removeIds);\n    },\n    //更新输入框的值\n    updateInputValue(arrs) {\n      for (let i = 0; i < arrs.length; i++) {\n        let ele = document.getElementById(arrs[i]);\n        if (ele != null && typeof ele != \"undefined\") {\n          switch (arrs[i]) {\n            case \"hs\":\n              ele.value = this.hs;\n              break;\n            case \"ls\":\n              ele.value = this.ls;\n              break;\n            case \"addhs\":\n              ele.value = this.addhs;\n              break;\n            case \"addls\":\n              ele.value = this.addls;\n              break;\n          }\n        }\n      }\n    },\n    //重置单元格内容\n    resetTable() {\n      //初始化遮罩层\n      this.loading = Loading.service({\n        text: \"加载中，请稍后...\",\n        background: \"rgba(109,106,106,0.35)\",\n      });\n      let objId = this.changeTr.id;\n      let params = this.getCellEle(objId);\n      resetCells(params).then((res) => {\n        if (res.code === \"0000\") {\n          this.updateTable();\n        } else {\n          this.$message.error(\"操作失败\");\n        }\n        this.loading.close(); //关闭遮罩层\n      });\n    },\n    //单元格属性编辑并保存\n    saveTdValue() {\n      this.show = true;\n      //初始化遮罩层\n      // this.loading = Loading.service({\n      //   text:\"加载中，请稍后...\",\n      //   background:'rgba(109,106,106,0.35)',\n      // })\n      // let objId = this.changeTr.id;\n      // let val = this.changeTr.getElementsByTagName(\"input\")[0].value;\n      // let params = this.getCellEle(objId);\n      // params.nrbs = val;\n      // editCells(params).then(res=>{\n      //   if(res.code==='0000'){\n      //     this.updateTable();\n      //   }else{\n      //     this.$message.error('操作失败');\n      //     this.loading .close();//关闭遮罩层\n      //   }\n      // })\n    },\n\n    //单元格属性编辑\n    async saveRow() {\n      try {\n        this.form.objId = this.cellData.objId;\n        this.form.mpid = this.cellData.mpid;\n        let { code } = await editCells(this.form);\n        if (code === \"0000\") {\n          this.updateTable();\n          this.$message.success(\"操作成功\");\n        }\n      } catch (e) {}\n      this.show = false;\n    },\n\n    //重置单元格属性（宽度，合并行数，合并列数）\n    resetCell(ele) {\n      if (ele) {\n        ele.style.width = this.tdWidth + \"%\";\n        ele.rowSpan = \"1\";\n        ele.colSpan = \"1\";\n      }\n    },\n    //输入框校验\n    checkInput(val, changeType) {\n      switch (changeType) {\n        case \"hs\":\n          this.hs = val;\n          break;\n        case \"ls\":\n          this.ls = val;\n          break;\n        case \"addhs\":\n          this.addhs = val;\n          break;\n        case \"addls\":\n          this.addls = val;\n          break;\n      }\n    },\n    //获取单元格明细数据\n    getCellDetail(hs, ls) {\n      let result = null;\n      this.tableData.forEach((item) => {\n        if (\n          item.rowindex === hs.toString() &&\n          item.colindex === ls.toString()\n        ) {\n          result = item;\n          if (result.nrbs == null) {\n            result.nrbs = \"\";\n          }\n        }\n      });\n      return result;\n    },\n    //获取某个单元格对象\n    getCellEle(objId) {\n      let result = null;\n      this.tableData.forEach((item) => {\n        if (item.objId === objId) {\n          result = item;\n        }\n      });\n      return result;\n    },\n    //获取最新的表格并重新渲染\n    updateTable() {\n      let param = JSON.stringify({\n        obj_id: this.mpData.objId,\n        lbbs: \"A\",\n      });\n      //获取最新的表格数据\n      getTable(param).then((res1) => {\n        console.log('res表格数据',res1);\n        if (res1.code === \"0000\") {\n          this.tableData = res1.data;\n          //根据最新的表格数据重新画\n          this.processTable();\n          this.$message.success(res1.msg);\n        } else {\n          this.$message.error(\"无法获取更新后的表格数据！\");\n        }\n        this.loading.close(); //关闭遮罩层\n      });\n    },\n  },\n};\n</script>\n\n<style scoped lang=\"scss\">\n.mpxq_info {\n  display: flex;\n}\n#mpxq_left {\n  margin-right: 20px;\n  ul {\n    list-style-type: none;\n    margin: 0;\n    padding: 8px;\n  }\n  border: 1px solid #0cc283;\n  width: 28%;\n  li:nth-child(1) {\n    font-weight: 700;\n  }\n  li {\n    line-height: 48px;\n    padding-left: 8px;\n    .el-input {\n      width: 70%;\n    }\n  }\n}\n.change_btn {\n  margin-top: 10px !important;\n  height: 36px !important;\n}\n.change_btn:nth-child(1) {\n  margin-left: 29%;\n}\n.change_btn:nth-child(2) {\n  margin-left: 20%;\n}\n#mpxq_right {\n  width: 72%;\n  height: 180px;\n  border: 1px solid #000;\n}\n</style>\n<style>\n#mpxq_right td {\n  border: 1px solid #000;\n  height: 35px;\n  line-height: 35px;\n  text-align: center;\n}\n#mpxq_right tr {\n  height: 35px;\n}\n#mpxq_right .atc {\n  background-color: #11ba6d;\n}\n#mpxq_right .input_cls {\n  text-align: center;\n  border: none;\n  width: 99%;\n  height: 99%;\n}\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sympk"}]}