{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\hslxwh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\hslxwh.vue", "mtime": 1706897323218}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["hslxwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "hslxwh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-white>\n      <el-filter\n        :data=\"filterInfo.data\"\n        :field-list=\"filterInfo.fieldList\"\n        :width=\"{ labelWidth: 180, itemWidth: 160 }\"\n        @handleReset=\"getReset\"\n      />\n    </el-white>\n    <div>\n      <el-white class=\"button-group\">\n        <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n          <el-col\n            style=\"\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\n            \"\n          >\n            <div>\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-plus\"\n                @click=\"addSensorButton\"\n                >新增</el-button\n              >\n              <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"getDelete\"\n                >删除</el-button\n              >\n            </div>\n          </el-col>\n        </el-row>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"handleSelectionChange\"\n          height=\"69vh\"\n        />\n      </el-white>\n      <!-- 详情/新增/修改 -->\n      <el-dialog :title=\"title\" :visible.sync=\"isShowDetails\" width=\"50%\" v-dialogDrag>\n        <el-form label-width=\"120px\" ref=\"form\" :model=\"form\"  :rules=\"rules\">\n          <el-row :gutter=\"8\" class=\"pull-left\">\n            <el-col :span=\"12\">\n              <el-form-item label=\"函数分类名称：\" prop=\"hsflmc\">\n                <el-input\n                  placeholder=\"请输入函数分类名称\"\n                  v-model=\"form.hsflmc\"\n                  :disabled=\"isDisabled\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"函数分类编码：\" prop=\"hsflbm\">\n                <el-input\n                  placeholder=\"请输入分类编码\"\n                  v-model=\"form.hsflbm\"\n                  :disabled=\"isDisabled\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n        <div slot=\"footer\">\n          <el-button @click=\"close\">关 闭</el-button>\n          <el-button type=\"primary\" @click=\"commitAdd\">保 存</el-button>\n        </div>\n      </el-dialog>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {\n  getPageDataList,\n  remove,\n  saveOrUpdate,\n} from \"@/api/dagangOilfield/bzgl/hsflkwh\";\n\nexport default {\n  name: \"syhskwh\",\n  data() {\n    return {\n      //form表单\n      form: {\n        hsflmc:undefined,\n        hsflbm:undefined,\n      },\n      //是否显示弹框\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //表单数据\n      dataTable: [],\n      //删除选择列\n      selectRows: [],\n      //标题\n      title: \"\",\n      //筛选框\n      filterInfo: {\n        data: {\n          syzyid: \"\",\n          syxmmc: \"\",\n        },\n        fieldList: [\n          {\n            label: \"函数分类名称\",\n            value: \"hsflmc\",\n            type: \"input\",\n            clearable: true,\n          },\n          {\n            label: \"函数分类编码\",\n            value: \"hsflbm\",\n            type: \"input\",\n            clearable: true,\n          },\n        ],\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"函数分类名称\", prop: \"hsflmc\", minWidth: \"100\" },\n          { label: \"函数分类编码\", prop: \"hsflbm\", minWidth: \"100\" },\n          {\n            prop: \"operation\",\n            label: \"操作\",\n            minWidth: \"100px\",\n            style: { display: \"block\" },\n            //操作列固定再右侧\n            fixed: \"right\",\n            operation: [{ name: \"修改\", clickFun: this.undateDetails }],\n          },\n        ],\n        option: { checkBox: true, serialNumber: true },\n      },\n\n      //删除是否可用\n      multipleSensor: true,\n      //查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n      },\n      //填入数据校验\n       rules: {\n        hsflmc: [\n          { required: true, message: \"函数分类名称不能为空\", trigger: \"blur\" },\n        ],\n        // hsflbm: [\n        //   { required: true, message: \"函数分类编码不能为空\", trigger: \"blur\" },\n        // ],\n\n      },\n      //表单开关\n      isSearchShow: false,\n    };\n  },\n  watch: {},\n  created() {\n    this.getData();\n  },\n  methods: {\n    //添加按钮\n    addSensorButton() {\n      //打开弹窗\n      this.isShowDetails = true;\n      //给表单置空\n      this.form = {};\n      //表单可编辑\n      this.isDisabled = false;\n      //设置弹出框标题\n      this.title = \"新增\";\n    },\n\n    //修改按钮\n    undateDetails(row) {\n      //打开弹窗\n      this.isShowDetails = true;\n      //给表单赋值\n      this.form = row;\n      //表单可编辑\n      this.isDisabled = false;\n      //设置弹出框标题\n      this.title = \"修改\";\n    },\n\n    //确认提交\n    commitAdd() {\n      this.$refs[\"form\"].validate((valid) => {\n        if (valid) {\n          saveOrUpdate(this.form).then((res) => {\n            if (res.code === \"0000\") {\n              this.$message.success(res.msg);\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n              this.isShowDetails = false;\n            } else {\n              this.$message.error(res.msg);\n            }\n          });\n        }\n      });\n    },\n\n    close() {\n      this.isShowDetails = false;\n    },\n    //定义重置方法\n    getReset() {},\n    //编辑按钮\n    updateSensorButton() {\n      //打开弹窗\n      this.isShowDetails = true;\n      //给表单赋值\n      this.form = row;\n      //表单可编辑\n      this.isDisabled = false;\n      //设置弹出框标题\n      this.title = \"修改\";\n    },\n    //删除按钮\n    getDelete() {\n      if (this.selectRows.length < 1) {\n        this.$message.warning(\"请选择正确的数据！！！\");\n        return;\n      }\n      let ids = this.selectRows.map((item) => {\n        return item.objId;\n      });\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      })\n        .then(() => {\n          remove(ids).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\",\n              });\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\",\n              });\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\",\n          });\n        });\n    },\n\n    //导出按钮\n    handleExport() {},\n    //行选中\n    handleSelectionChange(rows) {\n      this.selectRows = rows;\n      this.whmjzButtonDisabled = rows.length != 1;\n      //获取到当前行对象\n      this.mjzRowForm = rows[0];\n    },\n\n    //查询列表\n    async getData(params) {\n      try {\n        const param = { ...this.querySyzxmParam, ...params };\n        const { data, code } = await getPageDataList(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n        }\n      } catch (e) {\n\n      }\n    },\n    //搜索\n    handleQuery() {},\n    //重置\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n    },\n  },\n};\n</script>\n\n<style lang='scss' scoped>\n.box-card {\n  margin-bottom: 15px;\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n</style>\n"]}]}