{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\gfqxwh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\gfqxwh.vue", "mtime": 1726318090035}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["gfqxwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAk0BA;;AAeA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,QADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,IAAA,EAAA,KADA;AAEA,MAAA,OAAA,EAAA,KAFA;AAEA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,IAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA,EAFA;AAGA,UAAA,IAAA,EAAA,EAHA;AAIA,UAAA,IAAA,EAAA,EAJA;AAKA,UAAA,IAAA,EAAA,EALA;AAMA,UAAA,IAAA,EAAA;AANA,SADA;AASA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SANA;AATA,OAHA;AAqBA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA;AAZA,OArBA;AA2CA,MAAA,WAAA,EAAA,EA3CA;AA4CA,MAAA,WAAA,EAAA,EA5CA;AA4CA;AACA,MAAA,YAAA,EAAA,EA7CA;AA6CA;AACA,MAAA,YAAA,EAAA,KA9CA;AA+CA,MAAA,UAAA,EAAA,KA/CA;AA+CA;AACA,MAAA,UAAA,EAAA,KAhDA;AAiDA,MAAA,UAAA,EAAA,KAjDA;AAkDA,MAAA,UAAA,EAAA,KAlDA;AAmDA,MAAA,QAAA,EAAA,EAnDA;AAmDA;AACA,MAAA,SAAA,EAAA;AACA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,UAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,UAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAVA;AAaA,QAAA,UAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAbA;AAgBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAhBA;AAmBA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AAnBA,OApDA;AAwEA;AACA,MAAA,QAAA,EAAA,EAzEA;AAyEA;AACA,MAAA,SAAA,EAAA;AACA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,UAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,UAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAVA;AAaA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAbA;AAgBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAhBA;AAmBA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AAnBA,OA1EA;AA8FA;AACA,MAAA,QAAA,EAAA,EA/FA;AA+FA;AACA,MAAA,SAAA,EAAA;AACA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,UAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAVA;AAaA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAbA;AAgBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAhBA;AAmBA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AAnBA,OAhGA;AAoHA;AACA,MAAA,QAAA,EAAA,EArHA;AAqHA;AACA,MAAA,SAAA,EAAA;AACA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAVA;AAaA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAbA;AAgBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAhBA;AAmBA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AAnBA,OAtHA;AA0IA;AACA,MAAA,QAAA,EAAA,EA3IA;AA2IA;AACA,MAAA,QAAA,EAAA,EA5IA;AA4IA;AACA,MAAA,QAAA,EAAA,EA7IA;AA6IA;AACA,MAAA,QAAA,EAAA,EA9IA;AA8IA;AACA,MAAA,QAAA,EAAA,EA/IA;AA+IA;AACA,MAAA,QAAA,EAAA,EAhJA;AAgJA;AACA,MAAA,IAAA,EAAA,GAjJA;AAiJA;AACA,MAAA,UAAA,EAAA,EAlJA;AAkJA;AACA,MAAA,QAAA,EAAA,EAnJA;AAmJA;AACA,MAAA,OAAA,EAAA;AApJA,KAAA;AAsJA,GAzJA;AA0JA,EAAA,KAAA,EAAA;AACA,IAAA,UADA,sBACA,GADA,EACA;AACA,WAAA,KAAA,CAAA,IAAA,CAAA,MAAA,CAAA,GAAA;AACA;AAHA,GA1JA;AA+JA,EAAA,OA/JA,qBA+JA;AACA,SAAA,WAAA,CAAA,IAAA,GAAA,KAAA,IAAA;AACA,SAAA,OAAA;AACA,SAAA,WAAA,GAHA,CAIA;;AACA,SAAA,WAAA,GALA,CAMA;;AACA,SAAA,WAAA;AACA,GAvKA;AAwKA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,WAFA,yBAEA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,yBAAA;AAAA,kBAAA,IAAA,EAAA,KAAA,CAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,KAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,KANA;AAOA;AACA,IAAA,WARA,uBAQA,IARA,EAQA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,yBAAA;AAAA,kBAAA,IAAA,EAAA,MAAA,CAAA,IAAA;AAAA,kBAAA,IAAA,EAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,KAZA;AAaA;AACA,IAAA,WAdA,uBAcA,IAdA,EAcA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,yBAAA;AAAA,kBAAA,IAAA,EAAA,MAAA,CAAA,IAAA;AAAA,kBAAA,IAAA,EAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,KAlBA;AAmBA;AACA,IAAA,WApBA,uBAoBA,IApBA,EAoBA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,yBAAA;AAAA,kBAAA,IAAA,EAAA,MAAA,CAAA,IAAA;AAAA,kBAAA,IAAA,EAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,KAxBA;AAyBA;AACA,IAAA,WA1BA,uBA0BA,IA1BA,EA0BA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,yBAAA;AAAA,kBAAA,IAAA,EAAA,MAAA,CAAA,IAAA;AAAA,kBAAA,IAAA,EAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,KA9BA;AA+BA;AACA,IAAA,WAhCA,yBAgCA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAEA,2BAAA,aAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA,CADA,CAEA;;AACA,kBAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,wBAAA,IAAA,CAAA,KAAA,IAAA,MAAA,EAAA;AACA,sBAAA,IAAA,CAAA,OAAA,GAAA,MAAA,CAAA,QAAA;AACA;AACA,mBAJA;AAKA,iBARA,CAFA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KA3CA;AA4CA;AACA,IAAA,SA7CA,qBA6CA,GA7CA,EA6CA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA,gBAAA,MAAA,CAAA,OAAA,GAAA,mBAAA,OAAA,CAAA;AACA,kBAAA,IAAA,EAAA,IADA;AACA;AACA,kBAAA,IAAA,EAAA,SAFA;AAEA;AACA,kBAAA,OAAA,EAAA,iBAHA;AAGA;AACA,kBAAA,UAAA,EAAA,oBAJA;AAIA;AACA,kBAAA,MAAA,EAAA,QAAA,CAAA,aAAA,CAAA,UAAA;AALA,iBAAA,CAAA;AAOA,gBAAA,MAAA,CAAA,QAAA,mCAAA,GAAA,EATA,CAUA;;AAVA;AAAA,uBAWA,MAAA,CAAA,WAAA,CAAA,GAAA,CAAA,MAAA,CAXA;;AAAA;AAAA;AAAA,uBAYA,MAAA,CAAA,WAAA,CAAA,GAAA,CAAA,UAAA,CAZA;;AAAA;AAAA;AAAA,uBAaA,MAAA,CAAA,WAAA,CAAA,GAAA,CAAA,UAAA,CAbA;;AAAA;AAcA,gBAAA,MAAA,CAAA,YAAA,GAAA,KAAA;AACA,gBAAA,MAAA,CAAA,OAAA,GAAA,KAAA,CAfA,CAeA;;AACA,gBAAA,MAAA,CAAA,UAAA,GAAA,IAAA;;AACA,gBAAA,MAAA,CAAA,OAAA,CAAA,KAAA,GAjBA,CAiBA;;;AAjBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA,KA/DA;AAgEA;AACA,IAAA,SAjEA,qBAiEA,GAjEA,EAiEA;AAAA;;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,oCAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,OAAA;AACA,WANA,MAMA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAIA;AACA,SAbA;AAcA,OAnBA;AAoBA,KAtFA;AAuFA;AACA,IAAA,OAxFA,mBAwFA,GAxFA,EAwFA;AACA,WAAA,QAAA,mCAAA,GAAA;AACA,WAAA,YAAA,GAAA,IAAA;AACA,KA3FA;AA4FA;AACA,IAAA,OA7FA,mBA6FA,QA7FA,EA6FA;AACA;AACA,WAAA,QAAA,GAAA,EAAA;AACA,WAAA,QAAA,GAAA,EAAA;AACA,WAAA,QAAA,GAAA,EAAA,CAJA,CAKA;;AACA,UAAA,IAAA,GAAA,KAAA,WAAA,CAAA,MAAA,GAAA,KAAA,WAAA,CAAA,MAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,KAAA,WAAA,CAAA,UAAA,GAAA,KAAA,WAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,KAAA,WAAA,CAAA,UAAA,GAAA,KAAA,WAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,YAAA,GAAA,KAAA;;AACA,cAAA,QAAA;AACA,aAAA,MAAA;AAAA;AACA,eAAA,QAAA,GAAA,EAAA,CADA,CAEA;;AACA,eAAA,UAAA,GAAA,IAAA;AACA;;AACA,aAAA,MAAA;AAAA;AACA,eAAA,QAAA,GAAA,EAAA,CADA,CAEA;AACA;;AACA,eAAA,UAAA,GAAA,IAAA;AACA;;AACA,aAAA,MAAA;AAAA;AACA,eAAA,QAAA,GAAA,EAAA,CADA,CAEA;AACA;AACA;;AACA,eAAA,UAAA,GAAA,IAAA;AACA;;AACA,aAAA,MAAA;AAAA;AACA,eAAA,QAAA,GAAA,EAAA,CADA,CAEA;AACA;AACA;;AACA,eAAA,OAAA,GAAA,IAAA;AACA,eAAA,UAAA,GAAA,IAAA;AACA;;AACA;AACA;AA5BA;AA8BA,KArIA;AAsIA;AACA,IAAA,QAvIA,oBAuIA,QAvIA,EAuIA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,MAAA,CAAA,KAAA,CAAA,QAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,sBAAA,KAAA,EAAA;AACA,wBAAA,QAAA,mCAAA;AAAA,sBAAA,IAAA,EAAA,MAAA,CAAA;AAAA,qBAAA,CAAA;;AACA,4BAAA,QAAA;AACA,2BAAA,UAAA;AAAA;AACA,wBAAA,QAAA,+DAAA,QAAA,GAAA,MAAA,CAAA,QAAA,CAAA;;AACA,wBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,8BAAA,IAAA,CAAA,KAAA,KAAA,QAAA,CAAA,UAAA,EAAA;AACA,4BAAA,QAAA,CAAA,IAAA,GAAA,IAAA,CAAA,KAAA;AACA;AACA,yBAJA;;AAKA,4BAAA,MAAA,CAAA,OAAA,EAAA;AACA;AACA,+CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gCAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,8BAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,8BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,8BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,8BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,8BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,8BAAA,MAAA,CAAA,YAAA,GAAA,KAAA;;AACA,8BAAA,MAAA,CAAA,OAAA,GAPA,CAQA;;AACA,6BATA,MASA;AACA,8BAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,2BAbA;AAcA,yBAhBA,MAgBA;AACA,kDAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,gCAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,8BAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,8BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,8BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,8BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,8BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,8BAAA,MAAA,CAAA,YAAA,GAAA,KAAA;;AACA,8BAAA,MAAA,CAAA,OAAA,GAPA,CAQA;;AACA,6BATA,MASA;AACA,8BAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,2BAdA;AAeA;;AACA;;AACA,2BAAA,UAAA;AAAA;AACA,wBAAA,QAAA,+DAAA,QAAA,GAAA,MAAA,CAAA,QAAA,CAAA;;AACA,wBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,8BAAA,IAAA,CAAA,KAAA,KAAA,QAAA,CAAA,UAAA,EAAA;AACA,4BAAA,QAAA,CAAA,IAAA,GAAA,IAAA,CAAA,KAAA;AACA;AACA,yBAJA;;AAKA,6CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,8BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,4BAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,4BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,MAAA,CAAA,YAAA,GAAA,KAAA;;AACA,4BAAA,MAAA,CAAA,OAAA,GAPA,CAQA;;AACA,2BATA,MASA;AACA,4BAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,yBAbA;AAcA;;AACA,2BAAA,UAAA;AAAA;AACA,wBAAA,QAAA,+DAAA,QAAA,GAAA,MAAA,CAAA,QAAA,CAAA;;AACA,wBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,8BAAA,IAAA,CAAA,KAAA,KAAA,QAAA,CAAA,UAAA,EAAA;AACA,4BAAA,QAAA,CAAA,IAAA,GAAA,IAAA,CAAA,KAAA;AACA;AACA,yBAJA;;AAKA,6CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,8BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,4BAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,4BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,MAAA,CAAA,YAAA,GAAA,KAAA;;AACA,4BAAA,MAAA,CAAA,OAAA,GAPA,CAQA;;AACA,2BATA,MASA;AACA,4BAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,yBAbA;AAcA;;AACA,2BAAA,UAAA;AAAA;AACA,wBAAA,QAAA,+DAAA,QAAA,GAAA,MAAA,CAAA,QAAA,CAAA;;AACA,wBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,8BAAA,IAAA,CAAA,KAAA,KAAA,QAAA,CAAA,MAAA,EAAA;AACA,4BAAA,QAAA,CAAA,IAAA,GAAA,IAAA,CAAA,KAAA;AACA;AACA,yBAJA;;AAKA,6CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,8BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,4BAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,4BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,MAAA,CAAA,YAAA,GAAA,KAAA;;AACA,4BAAA,MAAA,CAAA,OAAA,GAPA,CAQA;;AACA,2BATA,MASA;AACA,4BAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,yBAbA;AAcA;;AACA;AACA;AA7GA;AA+GA,mBAjHA,MAiHA;AACA,oBAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;;AACA,2BAAA,KAAA;AACA;AACA,iBAtHA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAwHA,KA/PA;AAgQA;AACA,IAAA,aAjQA,yBAiQA,GAjQA,EAiQA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,cAAA,CAAA,MAAA;;AADA;AAAA,uBAEA,OAAA,CAAA,WAAA,CAAA,GAAA,CAFA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,KApQA;AAqQA;AACA,IAAA,aAtQA,yBAsQA,GAtQA,EAsQA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,cAAA,CAAA,MAAA;;AADA;AAAA,uBAEA,OAAA,CAAA,WAAA,CAAA,GAAA,CAFA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,KAzQA;AA0QA;AACA,IAAA,aA3QA,yBA2QA,GA3QA,EA2QA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,cAAA,CAAA,MAAA;;AADA;AAAA,uBAEA,OAAA,CAAA,WAAA,CAAA,GAAA,CAFA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,KA9QA;AA+QA;AACA,IAAA,aAhRA,yBAgRA,GAhRA,EAgRA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,cAAA,CAAA,MAAA;;AADA;AAAA,uBAEA,OAAA,CAAA,WAAA,CAAA,GAAA,CAFA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,KAnRA;AAoRA;AACA,IAAA,cArRA,0BAqRA,IArRA,EAqRA;AACA,cAAA,IAAA;AACA,aAAA,MAAA;AAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,MAAA,EAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,YAAA,EAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,YAAA,EAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,YAAA,EAAA,EAAA;AACA,eAAA,cAAA,CAAA,MAAA;AACA;;AACA,aAAA,MAAA;AAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,MAAA,EAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,YAAA,EAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,YAAA,EAAA,EAAA;AACA,eAAA,cAAA,CAAA,MAAA;AACA;;AACA,aAAA,MAAA;AAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,MAAA,EAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,YAAA,EAAA,EAAA;AACA,eAAA,cAAA,CAAA,MAAA;AACA;;AACA,aAAA,MAAA;AAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,MAAA,EAAA,EAAA;AACA;;AACA;AACA;AAvBA;AAyBA,KA/SA;AAgTA;AACA,IAAA,QAjTA,oBAiTA,IAjTA,EAiTA;AACA,WAAA,YAAA,GAAA,KAAA;;AACA,cAAA,IAAA;AACA,aAAA,MAAA;AACA,eAAA,UAAA,GAAA,KAAA;AACA;;AACA,aAAA,MAAA;AACA,eAAA,UAAA,GAAA,KAAA;AACA;;AACA,aAAA,MAAA;AACA,eAAA,UAAA,GAAA,KAAA;AACA;;AACA,aAAA,MAAA;AACA,eAAA,UAAA,GAAA,KAAA;AACA;;AACA,aAAA,MAAA;AACA,eAAA,YAAA,GAAA,KAAA;AACA;;AACA;AACA,eAAA,UAAA,GAAA,KAAA;AACA,eAAA,UAAA,GAAA,KAAA;AACA,eAAA,UAAA,GAAA,KAAA;AACA,eAAA,UAAA,GAAA,KAAA;AACA,eAAA,YAAA,GAAA,KAAA;AACA;AAtBA;AAwBA,KA3UA;AA4UA;AACA,IAAA,WA7UA,yBA6UA;AACA,WAAA,WAAA,GAAA;AAAA,QAAA,IAAA,EAAA,KAAA;AAAA,OAAA,CADA,CACA;AACA,KA/UA;AAiVA;AACA,IAAA,UAlVA,sBAkVA,KAlVA,EAkVA,IAlVA,EAkVA;AACA,UAAA,CAAA,KAAA,EAAA,OAAA,IAAA;AACA,aAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,KAAA,MAAA,CAAA,CAAA;AACA,KArVA;AAsVA,IAAA,WAtVA,yBAsVA;AAAA;;AACA,+BAAA;AAAA,QAAA,IAAA,EAAA,KAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KA1VA;AA2VA;AACA,IAAA,eA5VA,2BA4VA,IA5VA,EA4VA;AACA,WAAA,YAAA,GAAA,IAAA;;AACA,UAAA,IAAA,CAAA,UAAA,KAAA,GAAA,EAAA;AACA;AACA,aAAA,WAAA,CAAA,MAAA,GAAA,IAAA,CAAA,EAAA;AACA,aAAA,WAAA,CAAA,UAAA,GAAA,EAAA;AACA,aAAA,WAAA,CAAA,UAAA,GAAA,EAAA;AACA,OALA,MAKA,IAAA,IAAA,CAAA,UAAA,KAAA,GAAA,EAAA;AACA;AACA,aAAA,WAAA,CAAA,MAAA,GAAA,EAAA;AACA,aAAA,WAAA,CAAA,UAAA,GAAA,IAAA,CAAA,EAAA;AACA,aAAA,WAAA,CAAA,UAAA,GAAA,EAAA;AACA,OALA,MAKA,IAAA,IAAA,CAAA,UAAA,KAAA,GAAA,EAAA;AACA;AACA,aAAA,WAAA,CAAA,MAAA,GAAA,EAAA;AACA,aAAA,WAAA,CAAA,UAAA,GAAA,EAAA;AACA,aAAA,WAAA,CAAA,UAAA,GAAA,IAAA,CAAA,EAAA;AACA,OALA,MAKA;AACA,aAAA,WAAA,GAAA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,SAAA;AACA;;AACA,WAAA,OAAA;AACA,KAjXA;AAkXA;AACA,IAAA,OAnXA,mBAmXA,MAnXA,EAmXA;AAAA;;AACA,WAAA,IAAA,GAAA,IAAA;AACA,WAAA,WAAA,+DAAA,KAAA,WAAA,GAAA,MAAA;AACA,6BAAA,KAAA,WAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,OAAA,CAAA,IAAA,GAAA,KAAA;AACA,OAJA;AAKA,KA3XA;AA4XA;AACA,IAAA,WA7XA,yBA6XA;AACA,UAAA,QAAA,GAAA,OAAA;AACA,UAAA,SAAA,GAAA,WAAA,CAFA,CAGA;AACA;AACA;AACA;AACA;;AACA,8BAAA,SAAA,EAAA,KAAA,WAAA,EAAA,QAAA;AACA;AAtYA;AAxKA,C", "sourcesContent": ["<template>\r\n  <div class=\"app-container\" id=\"sbqxDiv\">\r\n    <!--左侧树组件-->\r\n    <el-row :gutter=\"1\">\r\n      <!--   左侧树   -->\r\n      <el-col :span=\"4\">\r\n        <el-card style=\"background:#e0f8ed;padding-top:10px;\" shadow=\"never\">\r\n          <div>\r\n            <el-col>\r\n              <el-form label-width=\"62px\">\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"快速查询:\" label-width=\"80px\">\r\n                    <el-input\r\n                      placeholder=\"输入关键字过滤\"\r\n                      v-model=\"filterText\"\r\n                      clearable\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-form>\r\n            </el-col>\r\n          </div>\r\n          <div class=\"text head-container\">\r\n            <el-col style=\"padding:0;\">\r\n              <el-tree\r\n                id=\"tree\"\r\n                :data=\"treeOptions\"\r\n                @node-click=\"handleNodeClick\"\r\n                :highlight-current=\"true\"\r\n                ref=\"tree\"\r\n                :filter-node-method=\"filterNode\"\r\n                node-key=\"id\"\r\n                :default-checked-keys=\"['0']\"\r\n                :default-expanded-keys=\"['0']\"\r\n                accordion\r\n              />\r\n            </el-col>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <!--右侧列表-->\r\n      <el-col :span=\"20\">\r\n        <el-filter\r\n          ref=\"filter1\"\r\n          :data=\"filterInfo.data\"\r\n          :field-list=\"filterInfo.fieldList\"\r\n          :btnHidden=\"false\"\r\n          :width=\"{ labelWidth: 80, itemWidth: 165 }\"\r\n          @handleReset=\"filterReset\"\r\n        />\r\n        <el-white class=\"button-group1\">\r\n          <div class=\"button_btn\">\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-plus\"\r\n              v-hasPermi=\"['gfqxwh:button:add']\"\r\n              @click=\"addForm('sbbj')\"\r\n              >新增部件</el-button\r\n            >\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-plus\"\r\n              v-hasPermi=\"['gfqxwh:button:add']\"\r\n              @click=\"addForm('sbbw')\"\r\n              >新增部位</el-button\r\n            >\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-plus\"\r\n              v-hasPermi=\"['gfqxwh:button:add']\"\r\n              @click=\"addForm('qxms')\"\r\n              >新增隐患描述</el-button\r\n            >\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-plus\"\r\n              v-hasPermi=\"['gfqxwh:button:add']\"\r\n              @click=\"addForm('flyj')\"\r\n              >新增分类依据</el-button\r\n            >\r\n            <el-button\r\n              type=\"success\"\r\n              icon=\"el-icon-download\"\r\n              @click=\"exportExcel\"\r\n              >导出</el-button\r\n            >\r\n          </div>\r\n          <comp-table\r\n            :table-and-page-info=\"tableAndPageInfo\"\r\n            height=\"62.2vh\"\r\n            v-loading=\"load\"\r\n          >\r\n            <el-table-column\r\n              slot=\"table_eight\"\r\n              align=\"center\"\r\n              fixed=\"right\"\r\n              style=\"display: block\"\r\n              label=\"操作\"\r\n              width=\"200\"\r\n              :resizable=\"false\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  @click=\"updateRow(scope.row)\"\r\n                  v-hasPermi=\"['gfqxwh:button:update']\"\r\n                  title=\"修改\"\r\n                  class=\"el-icon-edit\"\r\n                >\r\n                </el-button>\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  @click=\"deleteRow(scope.row)\"\r\n                  v-hasPermi=\"['gfqxwh:button:delete']\"\r\n                  title=\"删除\"\r\n                  class=\"el-icon-delete\"\r\n                >\r\n                </el-button>\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  @click=\"viewFun(scope.row)\"\r\n                  title=\"查看\"\r\n                  class=\"el-icon-view\"\r\n                >\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </comp-table>\r\n        </el-white>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!--  新增设备部件  -->\r\n    <el-dialog\r\n      title=\"新增设备部件\"\r\n      :visible.sync=\"isShowSbbj\"\r\n      width=\"58%\"\r\n      @close=\"closeFun('sbbj')\"\r\n      v-dialogDrag\r\n    >\r\n      <el-form\r\n        label-width=\"140px\"\r\n        :rules=\"sbbjRules\"\r\n        :model=\"sbbjForm\"\r\n        ref=\"sbbjForm\"\r\n      >\r\n        <el-card shadow=\"never\" class=\"box-cont\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>设备隐患</span>\r\n          </div>\r\n          <el-row :gutter=\"15\" class=\"cont_top\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"sblxbm\" label=\"设备类型\">\r\n                <el-select\r\n                  placeholder=\"设备类型\"\r\n                  v-model=\"sbbjForm.sblxbm\"\r\n                  style=\"width:80%\"\r\n                  @change=\"sblxChangeFun\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in sblxList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"sbbj\" label=\"设备部件\">\r\n                <el-input\r\n                  v-model=\"sbbjForm.sbbj\"\r\n                  placeholder=\"请输入设备部件\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"sbbw\" label=\"设备部位\">\r\n                <el-input\r\n                  v-model=\"sbbjForm.sbbw\"\r\n                  placeholder=\"请输入设备部位\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\r\n                <el-select\r\n                  placeholder=\"隐患等级\"\r\n                  v-model=\"sbbjForm.qxdj\"\r\n                  style=\"width:80%\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in qxdjList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"隐患描述:\" prop=\"qxms\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"sbbjForm.qxms\"\r\n                  placeholder=\"请输入隐患描述\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"sbbjForm.flyj\"\r\n                  placeholder=\"请输入分类依据\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"sbbjForm.jsyy\"\r\n                  placeholder=\"请输入技术原因\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n      </el-form>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"closeFun('sbbj')\" size=\"small\">取 消</el-button>\r\n        <el-button type=\"primary\" size=\"small\" @click=\"saveForm('sbbjForm')\"\r\n          >保存</el-button\r\n        >\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!--  新增设备部位  -->\r\n    <el-dialog\r\n      title=\"新增设备部位\"\r\n      :visible.sync=\"isShowSbbw\"\r\n      width=\"58%\"\r\n      @close=\"closeFun('sbbw')\"\r\n      v-dialogDrag\r\n    >\r\n      <el-form\r\n        label-width=\"140px\"\r\n        :rules=\"sbbwRules\"\r\n        :model=\"sbbwForm\"\r\n        ref=\"sbbwForm\"\r\n      >\r\n        <el-card shadow=\"never\" class=\"box-cont\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>设备隐患</span>\r\n          </div>\r\n          <el-row :gutter=\"15\" class=\"cont_top\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"sblxbm\" label=\"设备类型\">\r\n                <el-select\r\n                  placeholder=\"设备类型\"\r\n                  v-model=\"sbbwForm.sblxbm\"\r\n                  style=\"width:80%\"\r\n                  @change=\"sblxChangeFun\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in sblxList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"parentSbbj\" label=\"设备部件\">\r\n                <el-select\r\n                  placeholder=\"设备部件\"\r\n                  v-model=\"sbbwForm.parentSbbj\"\r\n                  style=\"width:80%\"\r\n                  @change=\"sbbjChangeFun\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in sbbjList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"sbbw\" label=\"设备部位\">\r\n                <el-input\r\n                  v-model=\"sbbwForm.sbbw\"\r\n                  placeholder=\"请输入设备部位\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\r\n                <el-select\r\n                  placeholder=\"隐患等级\"\r\n                  v-model=\"sbbwForm.qxdj\"\r\n                  style=\"width:80%\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in qxdjList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"隐患描述:\" prop=\"qxms\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"sbbwForm.qxms\"\r\n                  placeholder=\"请输入隐患描述\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"sbbwForm.flyj\"\r\n                  placeholder=\"请输入分类依据\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"sbbwForm.jsyy\"\r\n                  placeholder=\"请输入技术原因\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n      </el-form>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"closeFun('sbbw')\" size=\"small\">取 消</el-button>\r\n        <el-button type=\"primary\" size=\"small\" @click=\"saveForm('sbbwForm')\"\r\n          >保存</el-button\r\n        >\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!--  新增隐患描述  -->\r\n    <el-dialog\r\n      title=\"新增隐患描述\"\r\n      :visible.sync=\"isShowQxms\"\r\n      width=\"58%\"\r\n      @close=\"closeFun('qxms')\"\r\n      v-dialogDrag\r\n    >\r\n      <el-form\r\n        label-width=\"140px\"\r\n        :rules=\"qxmsRules\"\r\n        :model=\"qxmsForm\"\r\n        ref=\"qxmsForm\"\r\n      >\r\n        <el-card shadow=\"never\" class=\"box-cont\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>设备隐患</span>\r\n          </div>\r\n          <el-row :gutter=\"15\" class=\"cont_top\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"sblxbm\" label=\"设备类型\">\r\n                <el-select\r\n                  placeholder=\"设备类型\"\r\n                  v-model=\"qxmsForm.sblxbm\"\r\n                  style=\"width:80%\"\r\n                  @change=\"sblxChangeFun\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in sblxList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"parentSbbj\" label=\"设备部件\">\r\n                <el-select\r\n                  placeholder=\"设备部件\"\r\n                  v-model=\"qxmsForm.parentSbbj\"\r\n                  style=\"width:80%\"\r\n                  @change=\"sbbjChangeFun\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in sbbjList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"parentSbbw\" label=\"设备部位\">\r\n                <el-select\r\n                  placeholder=\"设备部位\"\r\n                  v-model=\"qxmsForm.parentSbbw\"\r\n                  style=\"width:80%\"\r\n                  @change=\"sbbwChangeFun\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in sbbwList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\r\n                <el-select\r\n                  placeholder=\"隐患等级\"\r\n                  v-model=\"qxmsForm.qxdj\"\r\n                  style=\"width:80%\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in qxdjList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"隐患描述:\" prop=\"qxms\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"qxmsForm.qxms\"\r\n                  placeholder=\"请输入隐患描述\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"qxmsForm.flyj\"\r\n                  placeholder=\"请输入分类依据\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"qxmsForm.jsyy\"\r\n                  placeholder=\"请输入技术原因\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n      </el-form>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"closeFun('qxms')\" size=\"small\">取 消</el-button>\r\n        <el-button type=\"primary\" size=\"small\" @click=\"saveForm('qxmsForm')\"\r\n          >保存</el-button\r\n        >\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!--  新增分类依据  -->\r\n    <el-dialog\r\n      title=\"新增分类依据\"\r\n      :visible.sync=\"isShowFlyj\"\r\n      width=\"58%\"\r\n      @close=\"closeFun('flyj')\"\r\n      v-dialogDrag\r\n    >\r\n      <el-form\r\n        label-width=\"140px\"\r\n        :rules=\"flyjRules\"\r\n        :model=\"flyjForm\"\r\n        ref=\"flyjForm\"\r\n      >\r\n        <el-card shadow=\"never\" class=\"box-cont\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>设备隐患</span>\r\n          </div>\r\n          <el-row :gutter=\"15\" class=\"cont_top\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"sblxbm\" label=\"设备类型\">\r\n                <el-select\r\n                  placeholder=\"设备类型\"\r\n                  v-model=\"flyjForm.sblxbm\"\r\n                  style=\"width:80%\"\r\n                  @change=\"sblxChangeFun\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in sblxList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"parentSbbj\" label=\"设备部件\">\r\n                <el-select\r\n                  placeholder=\"设备部件\"\r\n                  v-model=\"flyjForm.parentSbbj\"\r\n                  style=\"width:80%\"\r\n                  @change=\"sbbjChangeFun\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in sbbjList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"parentSbbw\" label=\"设备部位\">\r\n                <el-select\r\n                  placeholder=\"设备部位\"\r\n                  v-model=\"flyjForm.parentSbbw\"\r\n                  style=\"width:80%\"\r\n                  @change=\"sbbwChangeFun\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in sbbwList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\r\n                <el-select\r\n                  placeholder=\"隐患等级\"\r\n                  v-model=\"flyjForm.qxdj\"\r\n                  style=\"width:80%\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in qxdjList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"隐患描述:\" prop=\"parentQxms\">\r\n                <el-select\r\n                  placeholder=\"隐患描述\"\r\n                  v-model=\"flyjForm.parentQxms\"\r\n                  style=\"width:80%\"\r\n                  @change=\"qxmsChangeFun\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in qxmsList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"flyjForm.flyj\"\r\n                  placeholder=\"请输入分类依据\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"flyjForm.jsyy\"\r\n                  placeholder=\"请输入技术原因\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n      </el-form>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"closeFun('flyj')\" size=\"small\">取 消</el-button>\r\n        <el-button\r\n          v-if=\"addFlyj\"\r\n          type=\"primary\"\r\n          size=\"small\"\r\n          @click=\"saveForm('flyjForm')\"\r\n          >保存</el-button\r\n        >\r\n        <el-button\r\n          v-if=\"!addFlyj\"\r\n          type=\"primary\"\r\n          size=\"small\"\r\n          @click=\"saveForm('flyjForm')\"\r\n          >保存</el-button\r\n        >\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!--  设备隐患查看  -->\r\n    <el-dialog\r\n      title=\"设备隐患查看\"\r\n      :visible.sync=\"isShowDetail\"\r\n      v-if=\"isShowDetail\"\r\n      width=\"58%\"\r\n      @close=\"closeFun('view')\"\r\n      v-dialogDrag\r\n    >\r\n      <el-form label-width=\"140px\" :model=\"viewForm\" ref=\"viewForm\">\r\n        <el-card shadow=\"never\" class=\"box-cont\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>设备隐患</span>\r\n          </div>\r\n          <el-row :gutter=\"15\" class=\"cont_top\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"sblx\" label=\"设备类型\">\r\n                <el-input\r\n                  v-model=\"viewForm.sblx\"\r\n                  placeholder=\"请输入设备类型\"\r\n                  style=\"width: 92%\"\r\n                  :disabled=\"true\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"sbbj\" label=\"设备部件\">\r\n                <el-input\r\n                  v-model=\"viewForm.sbbj\"\r\n                  placeholder=\"请输入设备部件\"\r\n                  style=\"width: 92%\"\r\n                  :disabled=\"true\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"sbbw\" label=\"设备部位\">\r\n                <el-input\r\n                  v-model=\"viewForm.sbbw\"\r\n                  placeholder=\"请输入设备部位\"\r\n                  style=\"width: 92%\"\r\n                  :disabled=\"true\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\r\n                <el-input\r\n                  v-model=\"viewForm.qxdj\"\r\n                  placeholder=\"请输入隐患等级\"\r\n                  style=\"width: 92%\"\r\n                  :disabled=\"true\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"隐患描述:\" prop=\"qxms\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"viewForm.qxms\"\r\n                  placeholder=\"请输入隐患描述\"\r\n                  style=\"width: 92%\"\r\n                  :disabled=\"true\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"viewForm.flyj\"\r\n                  placeholder=\"请输入分类依据\"\r\n                  style=\"width: 92%\"\r\n                  :disabled=\"true\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"viewForm.jsyy\"\r\n                  placeholder=\"请输入技术原因\"\r\n                  style=\"width: 92%\"\r\n                  :disabled=\"true\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n      </el-form>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  getQxList,\r\n  getQxsbTree,\r\n  getSblxList,\r\n  getSbbjList,\r\n  getSbbwList,\r\n  getQxmsList,\r\n  getFlyjList,\r\n  addFlyj,\r\n  updateFlyj,\r\n  deleteFlyjById,\r\n  addQxms,\r\n  addSbbw,\r\n  addSbbj\r\n} from \"@/api/dagangOilfield/bzgl/sbqxwh/sbqxwh\";\r\nimport { getDictTypeData } from \"@/api/system/dict/data\";\r\nimport { Loading } from \"element-ui\";\r\nimport { exportExcel } from \"@/api/bzgl/ysbzk/ysbzk\";\r\n\r\nexport default {\r\n  name: \"sblxwh\",\r\n  data() {\r\n    return {\r\n      load: false,\r\n      addFlyj: false, //是否新增分类依据\r\n      filterInfo: {\r\n        data: {\r\n          sbbj: \"\",\r\n          sbbw: \"\",\r\n          qxms: \"\",\r\n          flyj: \"\",\r\n          qxdj: \"\",\r\n          jsyy: \"\"\r\n        },\r\n        fieldList: [\r\n          { label: \"设备部件\", type: \"input\", value: \"sbbj\" },\r\n          { label: \"设备部位\", type: \"input\", value: \"sbbw\" },\r\n          { label: \"隐患描述\", type: \"input\", value: \"qxms\" },\r\n          { label: \"隐患等级\", type: \"select\", value: \"qxdj\", options: [] },\r\n          { label: \"分类依据\", type: \"input\", value: \"flyj\" },\r\n          { label: \"技术原因\", type: \"input\", value: \"jsyy\" }\r\n        ]\r\n      },\r\n      tableAndPageInfo: {\r\n        pager: {\r\n          pageSize: 10,\r\n          pageNum: 1,\r\n          total: 0,\r\n          sizes: [10, 20, 50, 100]\r\n        },\r\n        option: {\r\n          checkBox: true,\r\n          serialNumber: true\r\n        },\r\n        tableData: [],\r\n        tableHeader: [\r\n          { prop: \"sblx\", label: \"设备类型\", minWidth: \"140\" },\r\n          { prop: \"sbbj\", label: \"设备部件\", minWidth: \"180\" },\r\n          { prop: \"sbbw\", label: \"设备部位\", minWidth: \"130\" },\r\n          { prop: \"qxms\", label: \"隐患描述\", minWidth: \"200\" },\r\n          { prop: \"flyj\", label: \"分类依据\", minWidth: \"220\", showPop: true },\r\n          { prop: \"qxdj\", label: \"隐患等级\", minWidth: \"80\" },\r\n          { prop: \"jsyy\", label: \"技术原因\", minWidth: \"120\" }\r\n        ]\r\n      },\r\n      queryParams: {},\r\n      treeOptions: [], //组织树\r\n      treeNodeData: {}, //点击后的树节点数据\r\n      isShowDetail: false,\r\n      isShowSbbj: false, //新增弹框\r\n      isShowSbbw: false,\r\n      isShowQxms: false,\r\n      isShowFlyj: false,\r\n      flyjForm: {}, //表单\r\n      flyjRules: {\r\n        sblxbm: [\r\n          { required: true, message: \"设备类型不能为空\", trigger: \"select\" }\r\n        ],\r\n        parentSbbj: [\r\n          { required: true, message: \"设备部件不能为空\", trigger: \"select\" }\r\n        ],\r\n        parentSbbw: [\r\n          { required: true, message: \"设备部位不能为空\", trigger: \"select\" }\r\n        ],\r\n        qxdj: [\r\n          { required: true, message: \"隐患等级不能为空\", trigger: \"select\" }\r\n        ],\r\n        parentQxms: [\r\n          { required: true, message: \"隐患描述不能为空\", trigger: \"select\" }\r\n        ],\r\n        flyj: [\r\n          { required: true, message: \"分类依据不能为空\", trigger: \"blur\" }\r\n        ],\r\n        jsyy: [{ required: true, message: \"技术原因不能为空\", trigger: \"blur\" }]\r\n      }, //校验规则\r\n      qxmsForm: {}, //表单\r\n      qxmsRules: {\r\n        sblxbm: [\r\n          { required: true, message: \"设备类型不能为空\", trigger: \"select\" }\r\n        ],\r\n        parentSbbj: [\r\n          { required: true, message: \"设备部件不能为空\", trigger: \"select\" }\r\n        ],\r\n        parentSbbw: [\r\n          { required: true, message: \"设备部位不能为空\", trigger: \"select\" }\r\n        ],\r\n        qxdj: [\r\n          { required: true, message: \"隐患等级不能为空\", trigger: \"select\" }\r\n        ],\r\n        qxms: [\r\n          { required: true, message: \"隐患描述不能为空\", trigger: \"blur\" }\r\n        ],\r\n        flyj: [\r\n          { required: true, message: \"分类依据不能为空\", trigger: \"blur\" }\r\n        ],\r\n        jsyy: [{ required: true, message: \"技术原因不能为空\", trigger: \"blur\" }]\r\n      }, //校验规则\r\n      sbbwForm: {}, //表单\r\n      sbbwRules: {\r\n        sblxbm: [\r\n          { required: true, message: \"设备类型不能为空\", trigger: \"select\" }\r\n        ],\r\n        parentSbbj: [\r\n          { required: true, message: \"设备部件不能为空\", trigger: \"select\" }\r\n        ],\r\n        sbbw: [\r\n          { required: true, message: \"设备部位不能为空\", trigger: \"blur\" }\r\n        ],\r\n        qxdj: [\r\n          { required: true, message: \"隐患等级不能为空\", trigger: \"select\" }\r\n        ],\r\n        qxms: [\r\n          { required: true, message: \"隐患描述不能为空\", trigger: \"blur\" }\r\n        ],\r\n        flyj: [\r\n          { required: true, message: \"分类依据不能为空\", trigger: \"blur\" }\r\n        ],\r\n        jsyy: [{ required: true, message: \"技术原因不能为空\", trigger: \"blur\" }]\r\n      }, //校验规则\r\n      sbbjForm: {}, //表单\r\n      sbbjRules: {\r\n        sblxbm: [\r\n          { required: true, message: \"设备类型不能为空\", trigger: \"select\" }\r\n        ],\r\n        sbbj: [\r\n          { required: true, message: \"设备部件不能为空\", trigger: \"blur\" }\r\n        ],\r\n        sbbw: [\r\n          { required: true, message: \"设备部位不能为空\", trigger: \"blur\" }\r\n        ],\r\n        qxdj: [\r\n          { required: true, message: \"隐患等级不能为空\", trigger: \"select\" }\r\n        ],\r\n        qxms: [\r\n          { required: true, message: \"隐患描述不能为空\", trigger: \"blur\" }\r\n        ],\r\n        flyj: [\r\n          { required: true, message: \"分类依据不能为空\", trigger: \"blur\" }\r\n        ],\r\n        jsyy: [{ required: true, message: \"技术原因不能为空\", trigger: \"blur\" }]\r\n      }, //校验规则\r\n      sblxList: [], //设备类型下拉框选项\r\n      sbbjList: [], //设备部件下拉框选项\r\n      sbbwList: [], //设备部位下拉框选项\r\n      qxmsList: [], //隐患描述下拉框选项\r\n      flyjList: [], //分类依据下拉框选项\r\n      qxdjList: [], //隐患等级下拉框选项\r\n      qxlb: \"4\", //隐患类别（光伏）\r\n      filterText: \"\", //过滤\r\n      viewForm: {}, //查看表单\r\n      loading: null\r\n    };\r\n  },\r\n  watch: {\r\n    filterText(val) {\r\n      this.$refs.tree.filter(val);\r\n    }\r\n  },\r\n  created() {\r\n    this.queryParams.qxlb = this.qxlb;\r\n    this.getData();\r\n    this.getTreeData();\r\n    //设备类型下拉框\r\n    this.getSblxList();\r\n    //隐患等级下拉框\r\n    this.getQxdjList();\r\n  },\r\n  methods: {\r\n    //获取设备类型下拉框\r\n    async getSblxList() {\r\n      await getSblxList({ qxlb: this.qxlb }).then(res => {\r\n        this.sblxList = res.data;\r\n      });\r\n    },\r\n    //获取设备部件下拉框\r\n    async getSbbjList(sblx) {\r\n      await getSbbjList({ qxlb: this.qxlb, sblx: sblx }).then(res => {\r\n        this.sbbjList = res.data;\r\n      });\r\n    },\r\n    //获取设备部位下拉框\r\n    async getSbbwList(sbbj) {\r\n      await getSbbwList({ qxlb: this.qxlb, sbbj: sbbj }).then(res => {\r\n        this.sbbwList = res.data;\r\n      });\r\n    },\r\n    //获取隐患描述下拉框\r\n    async getQxmsList(sbbw) {\r\n      await getQxmsList({ qxlb: this.qxlb, sbbw: sbbw }).then(res => {\r\n        this.qxmsList = res.data;\r\n      });\r\n    },\r\n    //获取分类依据下拉框\r\n    async getFlyjList(qxms) {\r\n      await getFlyjList({ qxlb: this.qxlb, qxms: qxms }).then(res => {\r\n        this.flyjList = res.data;\r\n      });\r\n    },\r\n    //获取隐患等级字典数据\r\n    async getQxdjList() {\r\n      //查询隐患等级字典\r\n      await getDictTypeData(\"sbqxwh_qxdj\").then(res => {\r\n        this.qxdjList = res.data;\r\n        //给筛选条件赋值\r\n        this.filterInfo.fieldList.map(item => {\r\n          if (item.value == \"qxdj\") {\r\n            item.options = this.qxdjList;\r\n          }\r\n        });\r\n      });\r\n    },\r\n    //编辑\r\n    async updateRow(row) {\r\n      //开启遮罩层\r\n      this.loading = Loading.service({\r\n        lock: true, //lock的修改符--默认是false\r\n        text: \"加载中，请稍后\", //显示在加载图标下方的加载文案\r\n        spinner: \"el-icon-loading\", //自定义加载图标类名\r\n        background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\r\n        target: document.querySelector(\"#sbqxDiv\")\r\n      });\r\n      this.flyjForm = { ...row };\r\n      //下拉框回显\r\n      await this.getSbbjList(row.sblxbm);\r\n      await this.getSbbwList(row.parentSbbj);\r\n      await this.getQxmsList(row.parentSbbw);\r\n      this.isShowDetail = false;\r\n      this.addFlyj = false; //不是新增\r\n      this.isShowFlyj = true;\r\n      this.loading.close(); //关闭遮罩层\r\n    },\r\n    //删除\r\n    deleteRow(row) {\r\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        deleteFlyjById(row).then(res => {\r\n          if (res.code === \"0000\") {\r\n            this.$message({\r\n              type: \"success\",\r\n              message: \"删除成功!\"\r\n            });\r\n            this.getData();\r\n          } else {\r\n            this.$message({\r\n              type: \"error\",\r\n              message: \"删除失败!\"\r\n            });\r\n          }\r\n        });\r\n      });\r\n    },\r\n    //查看\r\n    viewFun(row) {\r\n      this.viewForm = { ...row };\r\n      this.isShowDetail = true;\r\n    },\r\n    //新增\r\n    addForm(formType) {\r\n      //先清空下拉框的值\r\n      this.sbbjList = [];\r\n      this.sbbwList = [];\r\n      this.qxmsList = [];\r\n      //如果树节点有值，则带过来\r\n      let sblx = this.queryParams.sblxbm ? this.queryParams.sblxbm : \"\";\r\n      let sbbj = this.queryParams.parentSbbj ? this.queryParams.parentSbbj : \"\";\r\n      let sbbw = this.queryParams.parentSbbw ? this.queryParams.parentSbbw : \"\";\r\n      this.isShowDetail = false;\r\n      switch (formType) {\r\n        case \"sbbj\": //设备部件\r\n          this.sbbjForm = {};\r\n          // this.$set(this.sbbjForm,'sblxbm',sblx);\r\n          this.isShowSbbj = true;\r\n          break;\r\n        case \"sbbw\": //设备部位\r\n          this.sbbwForm = {};\r\n          // this.$set(this.sbbwForm,'sblxbm',sblx);\r\n          // this.$set(this.sbbwForm,'parentSbbj',sbbj);\r\n          this.isShowSbbw = true;\r\n          break;\r\n        case \"qxms\": //隐患描述\r\n          this.qxmsForm = {};\r\n          // this.$set(this.qxmsForm,'sblxbm',sblx);\r\n          // this.$set(this.qxmsForm,'parentSbbj',sbbj);\r\n          // this.$set(this.qxmsForm,'parentSbbw',sbbw);\r\n          this.isShowQxms = true;\r\n          break;\r\n        case \"flyj\": //分类依据\r\n          this.flyjForm = {};\r\n          // this.$set(this.flyjForm,'sblxbm',sblx);\r\n          // this.$set(this.flyjForm,'parentSbbj',sbbj);\r\n          // this.$set(this.flyjForm,'parentSbbw',sbbw);\r\n          this.addFlyj = true;\r\n          this.isShowFlyj = true;\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n    //保存\r\n    async saveForm(formType) {\r\n      await this.$refs[formType].validate(valid => {\r\n        if (valid) {\r\n          let saveForm = { ...{ qxlb: this.qxlb } };\r\n          switch (formType) {\r\n            case \"flyjForm\": //新增分类依据\r\n              saveForm = { ...saveForm, ...this.flyjForm };\r\n              this.qxmsList.forEach(item => {\r\n                if (item.value === saveForm.parentQxms) {\r\n                  saveForm.qxms = item.label;\r\n                }\r\n              });\r\n              if (this.addFlyj) {\r\n                //新增\r\n                addFlyj(saveForm).then(res => {\r\n                  if (res.code === \"0000\") {\r\n                    this.$message.success(\"操作成功\");\r\n                    this.isShowFlyj = false;\r\n                    this.isShowQxms = false;\r\n                    this.isShowSbbw = false;\r\n                    this.isShowSbbj = false;\r\n                    this.isShowDetail = false;\r\n                    this.getData();\r\n                    //关闭弹框\r\n                  } else {\r\n                    this.$message.error(\"操作失败\");\r\n                  }\r\n                });\r\n              } else {\r\n                updateFlyj(saveForm).then(res => {\r\n                  //编辑\r\n                  if (res.code === \"0000\") {\r\n                    this.$message.success(\"操作成功\");\r\n                    this.isShowFlyj = false;\r\n                    this.isShowQxms = false;\r\n                    this.isShowSbbw = false;\r\n                    this.isShowSbbj = false;\r\n                    this.isShowDetail = false;\r\n                    this.getData();\r\n                    //关闭弹框\r\n                  } else {\r\n                    this.$message.error(\"操作失败\");\r\n                  }\r\n                });\r\n              }\r\n              break;\r\n            case \"qxmsForm\": //新增隐患描述\r\n              saveForm = { ...saveForm, ...this.qxmsForm };\r\n              this.sbbwList.forEach(item => {\r\n                if (item.value === saveForm.parentSbbw) {\r\n                  saveForm.sbbw = item.label;\r\n                }\r\n              });\r\n              addQxms(saveForm).then(res => {\r\n                if (res.code === \"0000\") {\r\n                  this.$message.success(\"操作成功\");\r\n                  this.isShowFlyj = false;\r\n                  this.isShowQxms = false;\r\n                  this.isShowSbbw = false;\r\n                  this.isShowSbbj = false;\r\n                  this.isShowDetail = false;\r\n                  this.getData();\r\n                  //关闭弹框\r\n                } else {\r\n                  this.$message.error(\"操作失败\");\r\n                }\r\n              });\r\n              break;\r\n            case \"sbbwForm\": //新增隐患描述\r\n              saveForm = { ...saveForm, ...this.sbbwForm };\r\n              this.sbbjList.forEach(item => {\r\n                if (item.value === saveForm.parentSbbj) {\r\n                  saveForm.sbbj = item.label;\r\n                }\r\n              });\r\n              addSbbw(saveForm).then(res => {\r\n                if (res.code === \"0000\") {\r\n                  this.$message.success(\"操作成功\");\r\n                  this.isShowFlyj = false;\r\n                  this.isShowQxms = false;\r\n                  this.isShowSbbw = false;\r\n                  this.isShowSbbj = false;\r\n                  this.isShowDetail = false;\r\n                  this.getData();\r\n                  //关闭弹框\r\n                } else {\r\n                  this.$message.error(\"操作失败\");\r\n                }\r\n              });\r\n              break;\r\n            case \"sbbjForm\": //新增隐患描述\r\n              saveForm = { ...saveForm, ...this.sbbjForm };\r\n              this.sblxList.forEach(item => {\r\n                if (item.value === saveForm.sblxbm) {\r\n                  saveForm.sblx = item.label;\r\n                }\r\n              });\r\n              addSbbj(saveForm).then(res => {\r\n                if (res.code === \"0000\") {\r\n                  this.$message.success(\"操作成功\");\r\n                  this.isShowFlyj = false;\r\n                  this.isShowQxms = false;\r\n                  this.isShowSbbw = false;\r\n                  this.isShowSbbj = false;\r\n                  this.isShowDetail = false;\r\n                  this.getData();\r\n                  //关闭弹框\r\n                } else {\r\n                  this.$message.error(\"操作失败\");\r\n                }\r\n              });\r\n              break;\r\n            default:\r\n              break;\r\n          }\r\n        } else {\r\n          this.$message.error(\"校验未通过！\");\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    //设备类型下拉框事件\r\n    async sblxChangeFun(val) {\r\n      this.clearFormField(\"sblx\");\r\n      await this.getSbbjList(val);\r\n    },\r\n    //设备部件下拉框事件\r\n    async sbbjChangeFun(val) {\r\n      this.clearFormField(\"sbbj\");\r\n      await this.getSbbwList(val);\r\n    },\r\n    //设备部位下拉框事件\r\n    async sbbwChangeFun(val) {\r\n      this.clearFormField(\"sbbw\");\r\n      await this.getQxmsList(val);\r\n    },\r\n    //隐患描述下拉框事件\r\n    async qxmsChangeFun(val) {\r\n      this.clearFormField(\"qxms\");\r\n      await this.getFlyjList(val);\r\n    },\r\n    //清空字段值\r\n    clearFormField(type) {\r\n      switch (type) {\r\n        case \"sblx\": //设备类型\r\n          this.$set(this.sbbjForm, \"sbbj\", \"\");\r\n          this.$set(this.sbbwForm, \"parentSbbj\", \"\");\r\n          this.$set(this.qxmsForm, \"parentSbbj\", \"\");\r\n          this.$set(this.flyjForm, \"parentSbbj\", \"\");\r\n          this.clearFormField(\"sbbj\");\r\n          break;\r\n        case \"sbbj\": //设备部件\r\n          this.$set(this.sbbwForm, \"sbbw\", \"\");\r\n          this.$set(this.qxmsForm, \"parentSbbw\", \"\");\r\n          this.$set(this.flyjForm, \"parentSbbw\", \"\");\r\n          this.clearFormField(\"sbbw\");\r\n          break;\r\n        case \"sbbw\": //设备部位\r\n          this.$set(this.qxmsForm, \"qxms\", \"\");\r\n          this.$set(this.flyjForm, \"parentQxms\", \"\");\r\n          this.clearFormField(\"qxms\");\r\n          break;\r\n        case \"qxms\": //隐患描述\r\n          this.$set(this.flyjForm, \"flyj\", \"\");\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n    //关闭\r\n    closeFun(type) {\r\n      this.isShowDetail = false;\r\n      switch (type) {\r\n        case \"sbbj\":\r\n          this.isShowSbbj = false;\r\n          break;\r\n        case \"sbbw\":\r\n          this.isShowSbbw = false;\r\n          break;\r\n        case \"qxms\":\r\n          this.isShowQxms = false;\r\n          break;\r\n        case \"flyj\":\r\n          this.isShowFlyj = false;\r\n          break;\r\n        case \"view\":\r\n          this.isShowDetail = false;\r\n          break;\r\n        default:\r\n          this.isShowSbbj = false;\r\n          this.isShowSbbw = false;\r\n          this.isShowQxms = false;\r\n          this.isShowFlyj = false;\r\n          this.isShowDetail = false;\r\n          break;\r\n      }\r\n    },\r\n    //重置按钮\r\n    filterReset() {\r\n      this.queryParams = { qxlb: this.qxlb }; //重置条件\r\n    },\r\n\r\n    //树监听事件\r\n    filterNode(value, data) {\r\n      if (!value) return true;\r\n      return data.label.indexOf(value) !== -1;\r\n    },\r\n    getTreeData() {\r\n      getQxsbTree({ qxlb: this.qxlb }).then(res => {\r\n        this.treeOptions = res.data;\r\n      });\r\n    },\r\n    //树节点点击事件\r\n    handleNodeClick(node) {\r\n      this.treeNodeData = node;\r\n      if (node.identifier === \"1\") {\r\n        //设备类型\r\n        this.queryParams.sblxbm = node.id;\r\n        this.queryParams.parentSbbj = \"\";\r\n        this.queryParams.parentSbbw = \"\";\r\n      } else if (node.identifier === \"2\") {\r\n        //设备部件\r\n        this.queryParams.sblxbm = \"\";\r\n        this.queryParams.parentSbbj = node.id;\r\n        this.queryParams.parentSbbw = \"\";\r\n      } else if (node.identifier === \"3\") {\r\n        //设备部位\r\n        this.queryParams.sblxbm = \"\";\r\n        this.queryParams.parentSbbj = \"\";\r\n        this.queryParams.parentSbbw = node.id;\r\n      } else {\r\n        this.queryParams = { qxlb: this.qxlb };\r\n      }\r\n      this.getData();\r\n    },\r\n    //查询列表\r\n    getData(params) {\r\n      this.load = true;\r\n      this.queryParams = { ...this.queryParams, ...params };\r\n      getQxList(this.queryParams).then(res => {\r\n        this.tableAndPageInfo.tableData = res.data.records;\r\n        this.tableAndPageInfo.pager.total = res.data.total;\r\n        this.load = false;\r\n      });\r\n    },\r\n    //导出excel\r\n    exportExcel() {\r\n      let fileName = \"隐患标准库\";\r\n      let exportUrl = \"/bzqxFlyj\";\r\n      // if(this.selectData.length > 0){\r\n      //   // this.$message.warning('请在左侧勾选要导出的数据')\r\n      //   // return\r\n      //   exportExcel(exportUrl, this.queryParams, fileName);\r\n      // }\r\n      exportExcel(exportUrl, this.queryParams, fileName);\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* 设置滚动条的样式 */\r\n::-webkit-scrollbar {\r\n  width: 12px;\r\n}\r\n\r\n/* 滚动槽 */\r\n::-webkit-scrollbar-track {\r\n  //-webkit-box-shadow:inset006pxrgba(0,0,0,0.3);\r\n  border-radius: 10px;\r\n}\r\n\r\n/* 滚动条滑块 */\r\n::-webkit-scrollbar-thumb {\r\n  border-radius: 10px;\r\n  background: rgba(0, 0, 0, 0.1);\r\n  //-webkit-box-shadow:gba(0,0,0,0.5);\r\n}\r\n\r\n::-webkit-scrollbar-thumb:window-inactive {\r\n  background: rgba(0, 0, 0, 0.1);\r\n}\r\n.head-container {\r\n  margin: 0 auto;\r\n  width: 98%;\r\n  height: 82.6vh;\r\n  max-height: 82.6vh;\r\n  overflow: auto;\r\n}\r\n/*给左侧数结构header加颜色*/\r\n.box-card .el-card__header {\r\n  background: #11ba6d !important;\r\n}\r\n.box-card {\r\n  margin: 0;\r\n}\r\n\r\n.item {\r\n  width: 200px;\r\n  height: 148px;\r\n  float: left;\r\n}\r\n\r\n.tree {\r\n  overflow-y: hidden;\r\n  overflow-x: scroll;\r\n  width: 80px;\r\n  height: 500px;\r\n}\r\n\r\n.el-tree {\r\n  min-width: 100%;\r\n  display: inline-block !important;\r\n}\r\n/deep/ .el-dialog:not(.is-fullscreen) {\r\n  margin-top: 8vh !important;\r\n}\r\n</style>\r\n<style></style>\r\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl"}]}