{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\jszlgl\\yxgc.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\jszlgl\\yxgc.vue", "mtime": 1706897325231}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["yxgc.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsNA;;AACA;;AACA;;AAQA;;AACA;;AAEA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAHA;eAKA;AACA,EAAA,UAAA,EAAA;AAAA,IAAA,QAAA,EAAA,sBAAA;AAAA,IAAA,QAAA,EAAA;AAAA,GADA;AAEA,EAAA,IAAA,EAAA,QAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA,MAAA,SAAA,EAAA,EADA;AAEA,MAAA,aAAA,EAAA,GAFA;AAGA,MAAA,cAAA,EAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAHA;AAIA,MAAA,MAAA,EAAA,KAJA;AAKA;AACA,MAAA,aAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,EAaA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAbA,EAiBA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAjBA,EAqBA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OArBA,EAyBA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAzBA,EA6BA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OA7BA,EAiCA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAjCA,EAqCA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OArCA,CANA;AAiDA;AACA,MAAA,cAAA,EAAA,KAlDA;AAmDA,MAAA,MAAA,EAAA,EAnDA;AAmDA;AACA,MAAA,QAAA,EAAA,EApDA;AAqDA,MAAA,YAAA,EAAA,KArDA;AAsDA,MAAA,UAAA,EAAA,EAtDA;AAuDA,MAAA,WAAA,EAAA;AACA,QAAA,oBAAA,EAAA,MADA;AAEA,QAAA,WAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,QAHA;AAIA,QAAA,SAAA,EAAA,EAJA;AAKA,QAAA,QAAA,EAAA,EALA;AAMA,QAAA,WAAA,EAAA,IANA;AAOA,QAAA,WAAA,EAAA;AAPA,OAvDA;AAgEA,MAAA,UAAA,EAAA,KAhEA;AAiEA,MAAA,MAAA,EAAA;AACA,QAAA,IAAA,EAAA,GADA;AAEA,QAAA,OAAA,EAAA,CAAA;AAAA,UAAA,IAAA,EAAA,YAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAAA,CAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA,EAJA;AAKA,QAAA,MAAA,EAAA;AALA,OAjEA;AAwEA,MAAA,KAAA,EAAA,EAxEA;AAyEA;AACA,MAAA,MAAA,EAAA,IA1EA;AA2EA,MAAA,UAAA,EAAA,EA3EA;AA4EA,MAAA,GAAA,EAAA,EA5EA;AA6EA,MAAA,IAAA,EAAA;AACA,QAAA,IAAA,EAAA,GADA;AAEA,QAAA,QAAA,EAAA;AAFA,OA7EA;AAiFA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,IAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA,EAFA;AAGA,UAAA,OAAA,EAAA,EAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,WAAA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,MAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,WAAA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,MAAA,EAAA;AAAA,SAJA,CAKA;AALA;AAPA,OAjFA;AAgGA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,UAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,UAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,CARA;AAiBA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAjBA,OAhGA;AAmHA,MAAA,aAAA,EAAA,IAnHA;AAoHA,MAAA,YAAA,EAAA,EApHA;AAqHA,MAAA,QAAA,EAAA,KArHA;AAsHA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,UAAA,EAAA;AAFA,OAvHA;AA2HA;AACA,MAAA,QAAA,EAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OA5HA;AA6HA,MAAA,KAAA,EAAA,IA7HA;AA8HA;AACA,MAAA,KAAA,EAAA;AACA;AACA;AACA;AACA,QAAA,EAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,GAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAVA;AAaA,QAAA,GAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAbA;AAgBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAhBA;AAmBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAnBA;AAsBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAtBA;AAyBA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAzBA,OA/HA;AA6JA,MAAA,cAAA,EAAA,KA7JA;AA8JA,MAAA,WAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,IA9JA;AA+JA,MAAA,QAAA,EAAA,CA/JA;AAgKA,MAAA,OAAA,EAAA;AAhKA,KAAA;AAkKA,GAtKA;AAuKA,EAAA,KAAA,EAAA,EAvKA;AAwKA,EAAA,OAxKA,qBAwKA;AAAA;;AACA,6BAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,MAAA,KAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,KAAA,IAAA,MAAA,EAAA;AACA,iBAAA,IAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;AACA;AACA,OAJA;;AAKA,MAAA,KAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA;;AACA,MAAA,KAAA,CAAA,OAAA,CAAA,KAAA,CAAA,MAAA,CAAA,KAAA;AACA,KARA;AASA,GAlLA;AAmLA,EAAA,OAAA,EAAA;AACA,IAAA,WADA,yBACA;AACA,WAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;AACA,WAAA,WAAA;AACA,WAAA,OAAA;AACA,KALA;AAMA;AACA,IAAA,SAPA,qBAOA,GAPA,EAOA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAEA,uBAAA,GAAA,CAAA,KAAA,EAAA,aAAA,CAFA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAIA,gBAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,OAAA;;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,KAbA;AAcA;AACA,IAAA,WAfA,uBAeA,GAfA,EAeA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAEA,yBAAA,GAAA,CAAA,KAAA,CAFA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAIA,gBAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,OAAA;;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,KArBA;AAsBA,IAAA,OAtBA,qBAsBA;AACA,WAAA,cAAA,GAAA,KAAA;AACA,WAAA,YAAA,GAAA,IAAA;;AACA,cAAA,KAAA,IAAA,CAAA,MAAA;AACA,aAAA,CAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,WAAA,KAAA,KAAA,IAAA,CAAA,QAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA;;AACA;;AACA,aAAA,CAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,WAAA,KAAA,KAAA,IAAA,CAAA,QAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA;;AACA;;AACA,aAAA,CAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,IAAA,CAAA,MAAA,KAAA,KAAA,WAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA;;AACA;;AACA,aAAA,CAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,IAAA,CAAA,SAAA,CAAA,QAAA,CAAA,KAAA,WAAA,KAAA,KAAA,IAAA,CAAA,UAAA,KAAA,KAAA,WAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA;;AACA,cAAA,KAAA,IAAA,CAAA,OAAA,KAAA,KAAA,WAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA;;AACA;;AACA,aAAA,CAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,IAAA,CAAA,OAAA,KAAA,KAAA,WAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA;;AACA;;AACA,aAAA,CAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,IAAA,CAAA,SAAA,CAAA,QAAA,CAAA,KAAA,WAAA,KAAA,CAAA,KAAA,IAAA,CAAA,UAAA,KAAA,KAAA,WAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA;;AACA;;AACA,aAAA,CAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,IAAA,CAAA,QAAA,KAAA,KAAA,WAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA;;AACA;;AACA,aAAA,CAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,IAAA,CAAA,QAAA,KAAA,KAAA,WAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA,iBAAA,YAAA,GAAA,KAAA;AACA;;AACA;;AACA,aAAA,CAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,IAAA,CAAA,SAAA,KAAA,KAAA,WAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA,iBAAA,YAAA,GAAA,KAAA;AACA;;AACA;AA3DA;AA6DA,KAtFA;AAuFA,IAAA,cAvFA,0BAuFA,EAvFA,EAuFA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,sBAAA,EAAA,CADA;;AAAA;AAAA;AACA,gBAAA,IADA,qBACA,IADA;;AAAA,sBAEA,IAAA,KAAA,MAFA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAGA,MAAA,CAAA,WAAA,EAHA;;AAAA;AAAA;AAAA,uBAIA,MAAA,CAAA,OAAA,EAJA;;AAAA;AAKA,gBAAA,MAAA,CAAA,QAAA,CAAA;AACA,kBAAA,IAAA,EAAA,SADA;AAEA,kBAAA,OAAA,EAAA;AAFA,iBAAA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,KAjGA;AAkGA,IAAA,WAlGA,yBAkGA;AACA,UAAA,KAAA,KAAA,CAAA,MAAA,EAAA;AACA,aAAA,KAAA,CAAA,MAAA,CAAA,UAAA;AACA;AACA,KAtGA;AAuGA,IAAA,WAvGA,yBAuGA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,+BAAA;AAAA,kBAAA,UAAA,EAAA,MAAA,CAAA,IAAA,CAAA;AAAA,iBAAA,CADA;;AAAA;AAAA;AACA,gBAAA,IADA,yBACA,IADA;AACA,gBAAA,IADA,yBACA,IADA;;AAEA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,IAAA,CAAA,QAAA,GAAA,IAAA;AACA;;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,KA5GA;AA6GA;AACA,IAAA,aA9GA,yBA8GA,CA9GA,EA8GA;AACA,UAAA,IAAA,GAAA,CAAA,CAAA,WAAA,EAAA;AACA,UAAA,KAAA,GAAA,CAAA,CAAA,QAAA,KAAA,CAAA,GAAA,OAAA,CAAA,CAAA,QAAA,KAAA,CAAA,CAAA,GAAA,MAAA,CAAA,CAAA,QAAA,KAAA,CAAA,CAAA;AACA,UAAA,GAAA,GAAA,CAAA,CAAA,OAAA,KAAA,EAAA,GAAA,MAAA,CAAA,CAAA,OAAA,EAAA,GAAA,KAAA,CAAA,CAAA,OAAA,EAAA;AACA,aAAA,IAAA,GAAA,GAAA,GAAA,KAAA,GAAA,GAAA,GAAA,GAAA;AACA,KAnHA;AAoHA;AACA,IAAA,OArHA,mBAqHA,MArHA,EAqHA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAAA;AACA,kBAAA,MAAA,CAAA,MAAA,+DAAA,MAAA,CAAA,MAAA,GAAA,MAAA;;AACA,sBAAA,MAAA,CAAA,aAAA,KAAA,GAAA,EAAA;AACA,oBAAA,MAAA,CAAA,MAAA,CAAA,MAAA,GAAA,MAAA,CAAA,aAAA;AACA,oBAAA,MAAA,CAAA,MAAA,CAAA,EAAA,GAAA,KAAA;AACA,mBAHA,MAGA,IAAA,MAAA,CAAA,aAAA,KAAA,GAAA,EAAA;AACA,oBAAA,MAAA,CAAA,MAAA,CAAA,MAAA,GAAA,CAAA;AACA,oBAAA,MAAA,CAAA,MAAA,CAAA,EAAA,GAAA,IAAA;AACA,mBAHA,MAGA;AACA,oBAAA,MAAA,CAAA,MAAA,CAAA,MAAA,GAAA,MAAA,CAAA,aAAA;AACA;;AACA,sBAAA,MAAA,CAAA,MAAA,CAAA,OAAA,IAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,oBAAA,MAAA,CAAA,MAAA,CAAA,SAAA,GAAA,MAAA,CAAA,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;AACA,oBAAA,MAAA,CAAA,MAAA,CAAA,OAAA,GAAA,MAAA,CAAA,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;AACA;;AACA,sBAAA,MAAA,CAAA,MAAA,CAAA,OAAA,IAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,oBAAA,MAAA,CAAA,MAAA,CAAA,SAAA,GAAA,MAAA,CAAA,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;AACA,oBAAA,MAAA,CAAA,MAAA,CAAA,OAAA,GAAA,MAAA,CAAA,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;AACA;;AAEA,yCAAA,MAAA,CAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,wBAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,sBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;;AACA,sBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,4BAAA,IAAA,CAAA,EAAA,KAAA,KAAA,EAAA;AACA,0BAAA,IAAA,CAAA,IAAA,GAAA,SAAA;AACA;;AAEA,4BAAA,IAAA,CAAA,EAAA,KAAA,IAAA,EAAA;AACA,0BAAA,IAAA,CAAA,IAAA,GAAA,QAAA;AACA;;AACA,wBAAA,MAAA,CAAA,aAAA,CAAA,OAAA,CAAA,UAAA,OAAA,EAAA;AACA,8BAAA,IAAA,CAAA,MAAA,KAAA,OAAA,CAAA,KAAA,EAAA;AACA,4BAAA,IAAA,CAAA,UAAA,GAAA,OAAA,CAAA,KAAA;AACA;AACA,yBAJA;AAKA,uBAbA;AAcA;AACA,mBAnBA;AAoBA,iBAxCA,CAwCA,OAAA,CAAA,EAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,CAAA;AACA;;AA3CA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA4CA,KAjKA;AAkKA,IAAA,aAlKA,yBAkKA,QAlKA,EAkKA,IAlKA,EAkKA,QAlKA,EAkKA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBACA,EAAA,MAAA,CAAA,QAAA,KAAA,QAAA,CAAA,MADA;AAAA;AAAA;AAAA;;AAEA,gBAAA,MAAA,CAAA,QAAA,GAAA,KAAA;;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,gBAAA,MAAA,CAAA,QAAA,GAAA,CAAA;;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,UAAA;;AACA,gBAAA,MAAA,CAAA,OAAA,CAAA,KAAA;;AANA;AAAA,uBAOA,MAAA,CAAA,OAAA,EAPA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,KA3KA;AA6KA,IAAA,OA7KA,qBA6KA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,MAAA,CAAA,KAAA,CAAA,MAAA,EAAA,QAAA;AAAA,mGAAA,kBAAA,KAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA,iCACA,KADA;AAAA;AAAA;AAAA;;AAAA;AAAA,mCAEA,8BAAA,MAAA,CAAA,IAAA,CAFA;;AAAA;AAAA;AAEA,4BAAA,IAFA,yBAEA,IAFA;AAEA,4BAAA,IAFA,yBAEA,IAFA;;AAAA,kCAGA,IAAA,KAAA,MAHA;AAAA;AAAA;AAAA;;AAAA,kCAIA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,WAAA,CAAA,MAAA,GAAA,CAJA;AAAA;AAAA;AAAA;;AAKA,4BAAA,MAAA,CAAA,OAAA,GAAA,mBAAA,OAAA,CAAA;AACA,8BAAA,IAAA,EAAA,IADA;AACA;AACA,8BAAA,IAAA,EAAA,WAFA;AAEA;AACA,8BAAA,OAAA,EAAA,iBAHA;AAGA;AACA,8BAAA,UAAA,EAAA,oBAJA;AAIA;AACA,8BAAA,MAAA,EAAA,QAAA,CAAA,aAAA,CAAA,YAAA;AALA,6BAAA,CAAA;AAOA,4BAAA,MAAA,CAAA,UAAA,CAAA,UAAA,GAAA,IAAA,CAAA,KAAA;;AACA,4BAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,MAAA;;AAbA;AAAA;;AAAA;AAeA,4BAAA,MAAA,CAAA,QAAA,GAAA,KAAA;;AACA,4BAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AAhBA;AAAA,mCAiBA,MAAA,CAAA,OAAA,EAjBA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAA;;AAAA;AAAA;AAAA;AAAA,oBADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuBA,KApMA;AAqMA;AACA,IAAA,SAtMA,qBAsMA,GAtMA,EAsMA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,WAAA;AACA,WAAA,KAAA,GAAA,QAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,QAAA,GAAA,IAAA;AACA,KA7MA;AA8MA;AACA,IAAA,OA/MA,mBA+MA,GA/MA,EA+MA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,KAAA,GAAA,QAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,QAAA,GAAA,IAAA;AACA,WAAA,OAAA;AACA,KAtNA;AAuNA;AACA,IAAA,SAxNA,qBAwNA,EAxNA,EAwNA;AAAA;;AACA,UAAA,KAAA,GAAA,EAAA;;AACA,UAAA,sBAAA,EAAA,MAAA,QAAA,EAAA;AACA,YAAA,KAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,eAAA,QAAA,CAAA,OAAA,CAAA,QAAA;AACA;AACA;;AACA,QAAA,KAAA,GAAA,KAAA,GAAA,CAAA,CAAA,CAAA;AACA,OANA,MAMA;AACA,QAAA,KAAA,GAAA,EAAA;AACA;;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,4BAAA,IAAA,CAAA,SAAA,CAAA,KAAA,CAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,cAAA,IAAA,SAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,OAAA;AACA,WANA,MAMA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAIA;AACA,SAbA;AAcA,OAnBA,EAmBA,KAnBA,CAmBA,YAAA,CACA,CApBA;AAqBA,KAxPA;AA0PA,IAAA,MA1PA,kBA0PA,QA1PA,EA0PA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,SAAA,CAAA,MAAA;AACA,WAAA,WAAA;AACA,WAAA,IAAA,GAAA;AACA,QAAA,IAAA,EAAA,GADA;AAEA,QAAA,IAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,QAAA,EAFA;AAGA,QAAA,QAAA,EAAA;AAHA,OAAA;AAKA,WAAA,KAAA,GAAA,QAAA;AACA,WAAA,IAAA,CAAA,QAAA,GAAA,QAAA,CAVA,CAWA;;AACA,WAAA,IAAA,CAAA,GAAA,GAAA,KAAA,MAAA,CAAA,OAAA,CAAA,QAAA;AACA,WAAA,QAAA,GAAA,IAAA;AACA,KAxQA;AAyQA,IAAA,WAzQA,yBAyQA;AACA,WAAA,MAAA,GAAA;AACA,QAAA,IAAA,EAAA,GADA;AAEA,QAAA,OAAA,EAAA,CAAA;AAAA,UAAA,IAAA,EAAA,YAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAAA,CAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA,EAJA;AAKA,QAAA,MAAA,EAAA;AALA,OAAA;AAOA,KAjRA;;AAkRA;;;AAGA,IAAA,YArRA,wBAqRA,IArRA,EAqRA;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,IAAA,GAAA,OAAA,IAAA,GAAA,GAAA;;AACA,UAAA,CAAA,QAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,mBAAA;AACA,aAAA,OAAA,CAAA,KAAA;AACA,eAAA,KAAA;AACA;AACA,KA5RA;;AA8RA;;;AAGA,IAAA,SAjSA,qBAiSA,QAjSA,EAiSA,IAjSA,EAiSA,QAjSA,EAiSA;AACA;AACA,WAAA,IAAA,CAAA,YAAA,GAAA,QAAA,CAAA,IAAA,CAAA,UAAA,CAFA,CAGA;;AACA,WAAA,IAAA,CAAA,cAAA,GAAA,QAAA,CAAA,IAAA,CAAA,OAAA,CAAA,WAAA,CAJA,CAKA;;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,QAAA,CAAA,IAAA,CAAA,OAAA,CAAA,WAAA;AACA,KAxSA;AAySA;AACA,IAAA,SA1SA,qBA0SA,IA1SA,EA0SA;AACA,WAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA,sCAAA,IAAA,CAAA,KAAA,EAAA,IAAA,CAAA,WAAA;AACA,KA7SA;;AA8SA;AACA,IAAA,cA/SA,0BA+SA,EA/SA,EA+SA;AACA,kCAAA,EAAA;AACA,KAjTA;;AAkTA;;;AAGA,IAAA,WArTA,yBAqTA;AACA,WAAA,SAAA,CAAA,MAAA;AACA,WAAA,IAAA,CAAA,cAAA,GAAA,EAAA;AACA,WAAA,QAAA,GAAA,KAAA;AACA,KAzTA;AA0TA;AACA,IAAA,YA3TA,wBA2TA,IA3TA,EA2TA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,GAAA,GAAA,IAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,KAAA;AAAA,OAAA,CAAA;AACA,WAAA,MAAA,GAAA,IAAA,CAAA,MAAA,KAAA,CAAA;AACA,KA/TA;AAgUA,IAAA,UAhUA,wBAgUA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,UAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,sBAAA,IAAA,CAAA,KAAA,KAAA,YAAA,EAAA;AACA,oBAAA,IAAA,CAAA,OAAA,GAAA,OAAA,CAAA,aAAA;AACA;AACA,iBAJA;;AADA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,KAvUA;AAwUA;AACA,IAAA,aAzUA,2BAyUA;AACA,WAAA,YAAA,GAAA,KAAA;AACA,KA3UA;AA4UA,IAAA,YA5UA,wBA4UA,GA5UA,EA4UA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AADA;AAAA,uBAEA,8BAAA,OAAA,CAAA,WAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,sBAEA,IAFA;AAEA,gBAAA,IAFA,sBAEA,IAFA;AAGA,gBAAA,OAAA,CAAA,QAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,YAAA,GAAA,IAAA;;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,KAjVA;AAkVA;AACA,IAAA,cAnVA,0BAmVA,GAnVA,EAmVA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,WAAA,MAAA,GAAA,+EAAA,GAAA,CAAA,KAAA,GAAA,KAAA,GAAA,IAAA,IAAA,GAAA,OAAA,EAAA;AACA,KAtVA;AAuVA,IAAA,MAvVA,kBAuVA,IAvVA,EAuVA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,QAAA,GAAA,KAAA;;AADA,sBAEA,IAAA,KAAA,UAFA;AAAA;AAAA;AAAA;;AAGA,gBAAA,OAAA,CAAA,WAAA,CAAA,MAAA,GAAA,OAAA,CAAA,IAAA,CAAA,MAAA;AAHA,gCAIA,OAAA,CAAA,IAAA,CAAA,MAJA;AAAA,oDAKA,CALA,yBAeA,CAfA,0BAwBA,CAxBA,0BAkCA,CAlCA,0BAuEA,CAvEA,0BAiFA,CAjFA,0BAkHA,CAlHA,0BA2HA,CA3HA,0BAoIA,CApIA;AAAA;;AAAA;AAMA,gBAAA,OAAA,CAAA,MAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,OAAA,CAAA,IAAA,CAAA,KAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,aAAA,GAAA,GAAA;AACA,gBAAA,OAAA,CAAA,MAAA,GAAA,IAAA;AAbA;;AAAA;AAgBA,gBAAA,OAAA,CAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,OAAA,CAAA,IAAA,CAAA,KAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,aAAA,GAAA,EAAA;AACA,gBAAA,OAAA,CAAA,MAAA,GAAA,IAAA;AAtBA;;AAAA;AAyBA,gBAAA,OAAA,CAAA,MAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,MAAA,GAAA,SAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,OAAA,CAAA,IAAA,CAAA,KAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,MAAA,GAAA,IAAA;AAhCA;;AAAA;AAAA,qBAmCA,OAAA,CAAA,IAAA,CAAA,SAAA,CAAA,QAAA,CAAA,OAAA,CAAA,WAAA,CAnCA;AAAA;AAAA;AAAA;;AAAA,oBAqCA,OAAA,CAAA,IAAA,CAAA,UArCA;AAAA;AAAA;AAAA;;AAsCA,gBAAA,OAAA,CAAA,QAAA,CAAA,QAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAIA,IAJA,qFAIA;AAAA;AAAA;AAAA;AAAA;AACA,0BAAA,OAAA,CAAA,IAAA,CAAA,UAAA,GAAA,OAAA,CAAA,WAAA;AADA;AAAA,iCAEA,wBAAA,OAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA,gCAAA,QAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,8BAAA,OAAA,CAAA,QAAA,CAAA;AACA,gCAAA,IAAA,EAAA,SADA;AAEA,gCAAA,OAAA,EAAA;AAFA,+BAAA;;AAIA,8BAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,8BAAA,OAAA,CAAA,OAAA;AACA;AACA,2BATA,CAFA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAJA,IAgBA,KAhBA,CAgBA,YAAA;AACA,kBAAA,OAAA,CAAA,QAAA,CAAA,IAAA,CAAA,KAAA;AACA,iBAlBA;;AAtCA;;AAAA;AA2DA,gBAAA,OAAA,CAAA,WAAA,CAAA,MAAA,GAAA,YAAA;;AA3DA;AA6DA,gBAAA,OAAA,CAAA,cAAA,CAAA,KAAA,GAAA,IAAA;;AACA,oBAAA,OAAA,CAAA,IAAA,CAAA,OAAA,KAAA,OAAA,CAAA,WAAA,EAAA;AACA,kBAAA,OAAA,CAAA,WAAA,CAAA,MAAA,GAAA,UAAA;AACA;;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,KAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,OAAA,CAAA,IAAA,CAAA,KAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,MAAA,GAAA,IAAA;AArEA;;AAAA;AAwEA,gBAAA,OAAA,CAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,OAAA,CAAA,IAAA,CAAA,KAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,aAAA,GAAA,EAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,MAAA,GAAA,UAAA;AACA,gBAAA,OAAA,CAAA,MAAA,GAAA,IAAA;AA/EA;;AAAA;AAAA,oBAmFA,OAAA,CAAA,IAAA,CAAA,UAnFA;AAAA;AAAA;AAAA;;AAoFA,gBAAA,OAAA,CAAA,QAAA,CAAA,QAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAIA,IAJA,qFAIA;AAAA;AAAA;AAAA;AAAA;AACA,0BAAA,OAAA,CAAA,IAAA,CAAA,UAAA,GAAA,OAAA,CAAA,WAAA;AADA;AAAA,iCAEA,wBAAA,OAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA,gCAAA,QAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,8BAAA,OAAA,CAAA,QAAA,CAAA;AACA,gCAAA,IAAA,EAAA,SADA;AAEA,gCAAA,OAAA,EAAA;AAFA,+BAAA;;AAIA,8BAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,8BAAA,OAAA,CAAA,OAAA;AACA;AACA,2BATA,CAFA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAJA,IAgBA,KAhBA,CAgBA,YAAA;AACA,kBAAA,OAAA,CAAA,QAAA,CAAA,IAAA,CAAA,KAAA;AACA,iBAlBA;;AApFA;;AAAA;AAyGA,gBAAA,OAAA,CAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,OAAA,CAAA,IAAA,CAAA,KAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,aAAA,GAAA,EAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,MAAA,GAAA,YAAA;AACA,gBAAA,OAAA,CAAA,MAAA,GAAA,IAAA;AAhHA;;AAAA;AAmHA,gBAAA,OAAA,CAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,OAAA,CAAA,IAAA,CAAA,KAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,aAAA,GAAA,EAAA;AACA,gBAAA,OAAA,CAAA,MAAA,GAAA,IAAA;AAzHA;;AAAA;AA4HA,gBAAA,OAAA,CAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,OAAA,CAAA,IAAA,CAAA,KAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,aAAA,GAAA,EAAA;AACA,gBAAA,OAAA,CAAA,MAAA,GAAA,IAAA;AAlIA;;AAAA;AAqIA,gBAAA,OAAA,CAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,KAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,OAAA,CAAA,IAAA,CAAA,KAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,MAAA,GAAA,IAAA;AA1IA;;AAAA;AAAA;AAAA;;AAAA;AA8IA,gBAAA,OAAA,CAAA,cAAA,CAAA,KAAA,GAAA,QAAA;;AACA,oBAAA,OAAA,CAAA,IAAA,CAAA,SAAA,IAAA,OAAA,CAAA,IAAA,CAAA,SAAA,CAAA,QAAA,CAAA,OAAA,CAAA,WAAA,CAAA,EAAA;AACA,kBAAA,OAAA,CAAA,WAAA,CAAA,MAAA,GAAA,YAAA;AACA;;AACA,oBAAA,OAAA,CAAA,IAAA,CAAA,OAAA,KAAA,OAAA,CAAA,WAAA,EAAA;AACA,kBAAA,OAAA,CAAA,WAAA,CAAA,MAAA,GAAA,UAAA;AACA;;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,OAAA,CAAA,IAAA,CAAA,KAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,KAAA;AACA,gBAAA,OAAA,CAAA,MAAA,GAAA,IAAA;;AAzJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA2JA,KAlfA;AAmfA;AACA,IAAA,UApfA,sBAofA,IApfA,EAofA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,IADA,GACA,EADA;;AAAA,sBAEA,IAAA,CAAA,WAAA,KAAA,UAFA;AAAA;AAAA;AAAA;;AAAA,gCAGA,IAAA,CAAA,cAHA;AAAA,oDAIA,QAJA,yBAOA,WAPA,yBAUA,SAVA,yBAaA,SAbA,0BAgBA,SAhBA;AAAA;;AAAA;AAKA,gBAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AALA;;AAAA;AAQA,gBAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AARA;;AAAA;AAWA,gBAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AAXA;;AAAA;AAcA,gBAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AAdA;;AAAA;AAiBA,gBAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AAjBA;;AAAA;AAAA;AAAA;;AAAA;AAAA,gCAqBA,IAAA,CAAA,cArBA;AAAA,oDAsBA,WAtBA,0BA0BA,SA1BA,0BA8BA,oBA9BA,0BAqCA,UArCA,0BAyCA,YAzCA,0BA6CA,SA7CA,0BAuDA,SAvDA,0BA4DA,UA5DA,0BAiEA,IAjEA;AAAA;;AAAA;AAuBA,gBAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AACA,gBAAA,IAAA,CAAA,QAAA,GAAA,IAAA,CAAA,QAAA;AAxBA;;AAAA;AA2BA,gBAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AACA,gBAAA,IAAA,CAAA,MAAA,GAAA,IAAA,CAAA,QAAA;AA5BA;;AAAA;AA+BA,gBAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AACA,gBAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,IAAA,CAAA,OAAA,GAAA,IAAA,CAAA,SAAA;AACA,gBAAA,IAAA,CAAA,OAAA,GAAA,IAAA,CAAA,QAAA;AACA,gBAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA;AAnCA;;AAAA;AAsCA,gBAAA,IAAA,CAAA,UAAA,GAAA,IAAA,CAAA,SAAA;AACA,gBAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AAvCA;;AAAA;AA0CA,gBAAA,IAAA,CAAA,QAAA,GAAA,IAAA,CAAA,SAAA;AACA,gBAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AA3CA;;AAAA;AA8CA,oBAAA,IAAA,CAAA,MAAA,KAAA,YAAA,EAAA;AACA,kBAAA,IAAA,CAAA,UAAA,GAAA,IAAA,CAAA,SAAA;AACA;;AACA,oBAAA,IAAA,CAAA,MAAA,KAAA,UAAA,EAAA;AACA,kBAAA,IAAA,CAAA,QAAA,GAAA,IAAA,CAAA,SAAA;AACA;;AACA,gBAAA,IAAA,CAAA,QAAA,GAAA,IAAA,CAAA,QAAA;AACA,gBAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AArDA;;AAAA;AAwDA,gBAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AACA,gBAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA;AACA,gBAAA,IAAA,CAAA,QAAA,GAAA,IAAA,CAAA,QAAA;AA1DA;;AAAA;AA6DA,gBAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA;AACA,gBAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AACA,gBAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,QAAA;AA/DA;;AAAA;AAkEA,gBAAA,IAAA,CAAA,UAAA,GAAA,IAAA,CAAA,SAAA;AACA,gBAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AAnEA;;AAAA;AAuEA,gBAAA,GAvEA,GAuEA;AAAA,kBAAA,KAAA,EAAA,IAAA,CAAA,WAAA;AAAA,kBAAA,OAAA,EAAA,CAAA;AAAA,kBAAA,MAAA,EAAA,IAAA,CAAA,WAAA,KAAA,UAAA,GAAA,CAAA,GAAA;AAAA,iBAvEA;AAwEA,gBAAA,GAAA,+DAAA,GAAA,GAAA,IAAA,CAAA;AACA,wCAAA,GAAA,EAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA,sBAAA,QAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,oBAAA,OAAA,CAAA,QAAA,CAAA;AACA,sBAAA,IAAA,EAAA,SADA;AAEA,sBAAA,OAAA,EAAA;AAFA,qBAAA;;AAIA,oBAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,oBAAA,OAAA,CAAA,OAAA;AACA;AACA,iBATA;;AAzEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmFA,KAvkBA;AAwkBA,IAAA,aAxkBA,2BAwkBA;AACA;;;;AAIA,WAAA,MAAA,GAAA,KAAA;AACA;AA9kBA;AAnLA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\" >\n    <el-row :gutter=\"4\">\n      <el-col :span=\"24\">\n        <el-filter ref=\"filter1\"\n                   :data=\"filterInfo.data\"\n                   :field-list=\"filterInfo.fieldList\"\n                   @handleReset=\"filterReset\"/>\n      </el-col>\n    </el-row>\n    <el-white class=\"button-group\">\n      <el-row class=\"pull-right button_btn\" :gutter=\"20\">\n        <el-col :gutter=\"4\" :span=\"1.5\">\n          <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"addRow('')\" v-hasPermi=\"['yxgc:records:add']\">新增</el-button>\n          <el-button type=\"primary\" icon=\"el-icon-plus\"  @click=\"addRow('is')\">历史规程新增</el-button>\n          <el-button type=\"danger\" icon=\"el-icon-delete\" :disabled=\"single\" @click=\"deleteRow\" v-hasPermi=\"['yxgc:records:delete']\">删除</el-button>\n        </el-col>\n        <el-col :span=\"8\">\n          <template>\n            <el-tabs v-model=\"activeNameTow\" @tab-click=\"handleClick\">\n              <el-tab-pane label=\"待审批规程\" name=\"3\"></el-tab-pane>\n              <el-tab-pane label=\"变电站正式规程\" name=\"1\"></el-tab-pane>\n              <el-tab-pane label=\"通用正式规程\" name=\"4\"></el-tab-pane>\n              <el-tab-pane label=\"往期规程\" name=\"2\"></el-tab-pane>\n            </el-tabs>\n          </template>\n        </el-col>\n      </el-row>\n      <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"selectChange\"\n          height=\"63vh\"\n      >\n        <el-table-column prop=\"statusName\" slot=\"table_start\" align=\"center\" style=\"display: block\" label=\"流程状态\"\n                         min-width=\"120\" :resizable=\"false\">\n          <template slot-scope=\"scope\">\n            <el-badge v-if=\"scope.row.isBack===1\" value=\"退回\" class=\"item\" type=\"danger\">\n            </el-badge>\n            <span>{{ scope.row.statusName }}</span>\n          </template>\n        </el-table-column>\n        <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\"\n                         min-width=\"160\" :resizable=\"false\">\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" size=\"small\" v-if=\"scope.row.fileList.length>0\" @click=\"downClick(scope.row)\" title=\"下载\"\n                       class=\"el-icon-download\"></el-button>\n            <el-button type=\"text\" size=\"small\" @click=\"getInfo(scope.row)\" title=\"详情\" class=\"el-icon-view\"></el-button>\n            <el-button type=\"text\" size=\"small\" @click=\"updateRow(scope.row)\"\n                       v-if=\"scope.row.createBy === currentUser&& scope.row.status===0\" title=\"修改\" class='el-icon-edit'>\n            </el-button>\n            <el-button type=\"text\" size=\"small\" @click=\"deleteRow(scope.row.objId)\"\n                       v-if=\"scope.row.createBy === currentUser && scope.row.isStart!==1\" title=\"删除\"\n                       class=\"el-icon-delete\"></el-button>\n            <el-button @click=\"showTimeLine(scope.row)\" v-if=\"scope.row.isStart===1\" type=\"text\" size=\"small\"\n                       title=\"流程查看\" icon=\"el-icon-lcck commonIcon\"/>\n            <el-button @click=\"showProcessImg(scope.row)\" v-if=\"scope.row.isStart===1\" type=\"text\" size=\"small\"\n                       title=\"流程图\" icon=\"el-icon-lct commonIcon\"/>\n            <el-button @click=\"previewFile(scope.row)\" v-if=\"scope.row.isStart===1\" type=\"text\" size=\"small\" title=\"预览\"\n                       class=\"el-icon-zoom-in\"/>\n            <el-button @click=\"exportPdf(scope.row)\" v-if=\"scope.row.isStart===1\" type=\"text\" size=\"small\"\n                       title=\"导出pdf\" icon=\"el-icon-pdf-export commonIcon\"/>\n          </template>\n        </el-table-column>\n      </comp-table>\n      <el-dialog :visible.sync=\"fileOpen\" v-if=\"fileOpen\" :title=\"title\" width=\"50%\" v-dialogDrag id=\"dialogAct\">\n        <el-form ref=\"form\" :model=\"form\" label-width=\"150px\" :disabled=\"isDisabled\" :rules=\"rules\">\n          <el-row>\n            <el-col :span=\"12\">\n              <el-form-item label=\"运行规程文件编号:\" prop=\"wjbh\">\n                <el-input placeholder=\"请输入文件编号\" clearable v-model=\"form.wjbh\"></el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"运行规程文件名称:\" prop=\"wjmc\">\n                <el-input type=\"textarea\" :autosize=\"{ minRows: 1, maxRows: 2}\" placeholder=\"请输入文件名称\" clearable v-model=\"form.wjmc\"></el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"运行规程类型:\" prop=\"xl\">\n                <el-select v-model=\"form.xl\"  placeholder=\"请选择\">\n                  <el-option label=\"变电站运行规程\" value=\"bdz\">\n                  </el-option>\n                  <el-option label=\"通用运行规程\" value=\"ty\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"申请单位:\" prop=\"sqdw\">\n                <el-select v-model=\"form.sqdw\" disabled placeholder=\"请选择\">\n                  <el-option\n                      v-for=\"item in dwOptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"String(item.value)\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"发布人:\" prop=\"fbr\">\n                <el-input placeholder=\"请输入发布人\" v-model=\"form.fbr\" suffix-icon=\"el-icon-s-custom\" clearable></el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"执行日期:\" prop=\"zxrq\">\n                <el-date-picker type=\"date\" placeholder=\"选择日期时间\" value-format=\"yyyy-MM-dd\" clearable v-model=\"form.zxrq\"\n                                style=\"width: 100%\"></el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"发布日期:\" prop=\"fbrq\">\n                <el-date-picker type=\"date\" placeholder=\"选择日期时间\" value-format=\"yyyy-MM-dd\" clearable v-model=\"form.fbrq\"\n                                style=\"width: 100%\"></el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"上传时间:\" prop=\"scsj\">\n                <el-date-picker type=\"datetime\" placeholder=\"选择日期时间\" value-format=\"yyyy-MM-dd HH:mm:ss\" clearable\n                                v-model=\"form.scsj\" style=\"width: 100%\"></el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"更改章节号:\" prop=\"zjh\">\n                <el-input :placeholder=\"isDisabled? '' : '请输入'\" v-model=\"form.zjh\" clearable></el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"原文内容:\" prop=\"ywnr\">\n                <el-input type=\"textarea\" :placeholder=\"isDisabled? '' : '请输入'\" v-model=\"form.ywnr\" clearable></el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"修改后内容:\" prop=\"xghnr\">\n                <el-input type=\"textarea\" :placeholder=\"isDisabled? '' : '请输入'\" v-model=\"form.xghnr\" clearable></el-input>\n              </el-form-item>\n            </el-col>\n          <el-col :span=\"24\">\n              <el-form-item label=\"已上传附件:\" prop=\"attachment\" v-if=\"form.fileList.length>0\">\n                <el-col :span=\"24\" v-for=\"(item,index) in form.fileList\">\n                    {{index + 1 }}、\n                    {{item.fileOldName}}\n                    <el-form>\n                      <el-button type=\"primary\" size=\"mini\" @click=\"downloadHandle(item.fileId)\">下载</el-button>\n                      <el-button v-if=\"!isDisabled\" type=\"danger\" size=\"mini\" @click=\"deleteFileById(item.fileId)\">删除</el-button>\n                    </el-form>\n                </el-col>\n              </el-form-item>\n            </el-col>\n          <!-- <el-row :gutter=\"4\" class=\"pull-left\">\n            <el-col :span=\"20\">\n              <el-form-item label=\"已上传附件：\" prop=\"attachment\" v-if=\"form.fileList.length>0\" id=\"pic_form\">\n                <el-col :span=\"24\" v-for=\"(item,index) in form.fileList\" style=\"margin-left: 0\">\n                  <el-form-item :label=\"(index+1).toString()\">\n                    {{ item.fileOldName }}\n                    <el-button v-if=\"!isDisabled\" type=\"error\" size=\"mini\" @click=\"deleteFileById(item.fileId)\">删除\n                    </el-button>\n                    <el-form>\n                      <el-button type=\"primary\" size=\"mini\" @click=\"downloadHandle(item.fileId)\">下载</el-button>\n                    </el-form>\n                  </el-form-item>\n                </el-col>\n              </el-form-item> -->\n            <el-col :span=\"24\">\n              <el-form-item label=\"附件上传:\" prop=\"attachmentid\">\n                <template slot-scope=\"scope\">\n                  <el-upload\n                      multiple\n                      :on-success=\"uploadSuccess\"\n                      class=\"upload-demo\"\n                      accept=\".jpg,.png,.rar,.txt,.zip,.doc,.ppt,.xls,.pdf,.docx,.xlsx,.mp4,.avi,.rmvb\"\n                      ref=\"upload\"\n                      action=\"/isc-api/file/upload\"\n                      :before-upload=\"beforeUpload\"\n                      :data=\"uploadData\"\n                      :headers=\"upHeader\"\n                      :auto-upload=\"false\">\n                    <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选取文件</el-button>\n                  </el-upload>\n                </template>\n              </el-form-item>\n            </el-col>\n          </el-row>\n\n        </el-form>\n        <div slot=\"footer\">\n          <el-button @click=\"uploadClose\">取 消</el-button>\n          <el-button type=\"primary\" v-show=\"!isDisabled\" @click=\"saveRow\">保 存</el-button>\n          <el-button v-if=\"isDisabled&&buttonNameShow&&form.status>0\" type=\"info\"\n                     @click=\"submit('rollback')\">退 回\n          </el-button>\n          <el-button v-if=\"isDisabled&&buttonNameShow\" type=\"success\" @click=\"submit('complete')\">{{ buttonName }}\n          </el-button>\n        </div>\n      </el-dialog>\n\n      <!--  工作流  -->\n      <activiti ref=\"activiti\" :processData=\"processData\" :isShow=\"isShow\" :option=\"activitiOption\"\n                @todoData=\"todoResult\"\n                @toClose=\"closeActiviti\"></activiti>\n\n      <!-- 流程详情 -->\n      <time-line :value=\"timeLineShow\" :timeData=\"timeData\" @closeTimeLine=\"colseTimeLine\"/>\n      <!--流程图查看-->\n      <el-dialog title=\"审批流程图\" :visible.sync=\"openLoadingImg\" width=\"56%\" append-to-body :close-on-click-modal=false>\n        <div>\n          <img :src=\"imgSrc\" style=\"width: 100%\"/>\n        </div>\n      </el-dialog>\n    </el-white>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport {Loading} from \"element-ui\";\nimport {\n  exportPdf,\n  getListTZ,\n  previewFile,\n  remove,\n  saveOrUpdateYxgc,\n  updateById\n} from '@/api/dagangOilfield/bzgl/bzgfgl/bzgfgl'\nimport {deleteById, downloadByBusinessId, downloadByFileId, getListByBusinessId} from '@/api/tool/file'\nimport {HistoryList} from \"@/api/activiti/processTask\";\n//流程\nimport activiti from 'com/activiti_yxgc'\nimport timeLine from \"com/timeLine\";\nimport {getDwOptions} from \"@/api/yxgl/gzpgl/gzpgl\";\n\nexport default {\n  components: {activiti, timeLine},\n  name: 'bzgfgl',\n  data() {\n    return {\n      dwOptions:[],\n      activeNameTow: \"3\",\n      activitiOption: {title: \"上报\"},\n      isShow: false,\n      //状态下拉框数据\n      statusOptions: [\n        {\n          value: 0,\n          label: '运行规程填报'\n        },\n        {\n          value: 9,\n          label: '分公司主管领导审核'\n        },\n        {\n          value: 1,\n          label: '分公司领导审核'\n        },\n        {\n          value: 2,\n          label: '电力调度中心、科技中心审核'\n        },\n        {\n          value: 3,\n          label: '电力调度中心已审核、科技中心审核中'\n        },\n        {\n          value: 4,\n          label: '科技中心已审核、电力调度中心审核中'\n        },\n        {\n          value: 5,\n          label: '生产科专工审核'\n        },\n        {\n          value: 6,\n          label: '生产科科长审核'\n        },\n        {\n          value: 7,\n          label: '公司主管领导审核'\n        },\n        {\n          value: 8,\n          label: '结束'\n        },\n\n      ],\n      //流程图查看\n      openLoadingImg: false,\n      imgSrc: '',//流程图查看地址\n      timeData: [],\n      timeLineShow: false,\n      buttonName: \"\",\n      processData: {\n        processDefinitionKey: 'yxgc',\n        businessKey: '',\n        businessType: '运行规程审批',\n        variables: {},\n        nextUser: '',\n        defaultFrom: true,\n        processType: 'complete',\n      },\n      isDisabled: false,\n      params: {\n        wjlx: '0',\n        mySorts: [{prop: 'updateTime', asc: false}],\n        fbrq: '',\n        zxrq: '',\n        status: ''\n      },\n      title: '',\n      // 非单个禁用\n      single: true,\n      selectRows: [],\n      ids: [],\n      form: {\n        wjlx: '0',\n        fileList: []\n      },\n      filterInfo: {\n        data: {\n          wjbh: '',\n          wjmc: '',\n          fbrqArr: [],\n          zxrqArr: [],\n        },\n        fieldList: [\n          {label: '文件编号', type: 'input', value: 'wjbh'},\n          {label: '文件名称', type: 'input', value: 'wjmc'},\n          {label: '发布日期', type: 'daterange', value: 'fbrqArr', format: 'yyyy-MM-dd'},\n          {label: '执行日期', type: 'daterange', value: 'zxrqArr', format: 'yyyy-MM-dd'},\n          // { label: '状态', type: 'select', value: 'statusList', multiple: true, options: [] }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          {prop: 'wjbh', label: '运行规程文件编号', minWidth: '120'},\n          {prop: 'wjmc', label: '运行规程文件名称', minWidth: '180', showPop: true},\n          {prop: 'lxmc', label: '运行规程类型', minWidth: '100', showPop: true},\n          {prop: 'fbrq', label: '发布日期', minWidth: '120'},\n          {prop: 'zxrq', label: '执行日期', minWidth: '120'},\n          {prop: 'fbr', label: '发布人', minWidth: '120'},\n          {prop: 'scsj', label: '上传时间', minWidth: '140'},\n        ],\n        option: {checkBox: true, serialNumber: true}\n      },\n      HighlightShow: true,\n      defaultProps: {},\n      fileOpen: false,\n      // 文件上传数据\n      uploadData: {\n        type: \"\",\n        businessId: undefined\n      },\n      // 文件上传请求头\n      upHeader: { token: getToken() },\n      value: null,\n      //校验规则\n      rules: {\n        // wjbh: [\n        //   {required: true, message: '文件编号不能为空', trigger: 'blur'}\n        // ],\n        xl: [\n          {required: true, message: '不能为空', trigger: 'blur'}\n        ],\n        wjmc: [\n          {required: true, message: '文件名称不能为空', trigger: 'blur'}\n        ],\n        fbr: [\n          {required: true, message: '发布人不能为空', trigger: 'blur'}\n        ],\n        zxr: [\n          {required: true, message: '执行人不能为空', trigger: 'blur'}\n        ],\n        zxrq: [\n          {required: true, message: '执行日期不能为空', trigger: 'change'}\n        ],\n        fbrq: [\n          {required: true, message: '发布日期不能为空', trigger: 'change'}\n        ],\n        scsj: [\n          {required: true, message: '上传时间不能为空', trigger: 'change'}\n        ],\n        status: [\n          {required: true, message: '上传时间不能为空', trigger: 'change'}\n        ]\n\n      },\n      buttonNameShow: false,\n      currentUser: this.$store.getters.name,\n      picCount: 0,\n      loading:null,\n    }\n  },\n  watch: {},\n  mounted() {\n    getDwOptions({}).then(res => {\n      this.filterInfo.fieldList.map(item => {\n        if (item.value == 'sqdw') {\n          return item.options = res.data\n        }\n      })\n      this.dwOptions = res.data;\n      this.getData(this.$route.query)\n    })\n  },\n  methods: {\n    handleClick() {\n      this.tableAndPageInfo.pager.pageResize = 'Y'\n      this.filterReset()\n      this.getData();\n    },\n    //导出Pdf\n    async exportPdf(row) {\n      try {\n        await exportPdf(row.objId, '运行规程变更单.pdf')\n      } catch (e) {\n        this.$message.error('导出失败！')\n      }\n    },\n    //预览文件\n    async previewFile(row) {\n      try {\n        await previewFile(row.objId)\n      } catch (e) {\n        this.$message.error(\"预览失败！\")\n      }\n    },\n    getShow() {\n      this.buttonNameShow = false\n      this.isDisabledBj = true\n      switch (this.form.status) {\n        case 0:\n          this.buttonName = '上 报'\n          if (this.currentUser === this.form.createBy) {\n            this.buttonNameShow = true\n          }\n          break;\n        case 9:\n          this.buttonName = '提 交'\n          if (this.currentUser === this.form.sckzgspr) {\n            this.buttonNameShow = true\n          }\n          break;\n        case 1:\n          this.buttonName = '提 交'\n          if (this.form.fgsspr === this.currentUser) {\n            this.buttonNameShow = true\n          }\n          break;\n        case 2:\n          this.buttonName = '提 交'\n          if (this.form.dlddzxshr.includes(this.currentUser) && this.form.dlddzxyshr !== this.currentUser) {\n            this.buttonNameShow = true\n          }\n          if (this.form.kjzxshr === this.currentUser) {\n            this.buttonNameShow = true\n          }\n          break;\n        case 3:\n          this.buttonName = '提 交'\n          if (this.form.kjzxshr === this.currentUser) {\n            this.buttonNameShow = true\n          }\n          break;\n        case 4:\n          this.buttonName = '提 交'\n          if (this.form.dlddzxshr.includes(this.currentUser)&& !this.form.dlddzxyshr !== this.currentUser) {\n            this.buttonNameShow = true\n          }\n          break;\n        case 5:\n          this.buttonName = '提 交'\n          if (this.form.sckzgshr === this.currentUser) {\n            this.buttonNameShow = true\n          }\n          break;\n        case 6:\n          this.buttonName = '提 交'\n          if (this.form.sckkzshr === this.currentUser) {\n            this.buttonNameShow = true\n            this.isDisabledBj = false\n          }\n          break;\n        case 7:\n          this.buttonName = '通 过'\n          if (this.form.gszgldshr === this.currentUser) {\n            this.buttonNameShow = true\n            this.isDisabledBj = false\n          }\n          break;\n      }\n    },\n    async deleteFileById(id) {\n      let {code} = await deleteById(id)\n      if (code === '0000') {\n        await this.getFileList();\n        await this.getData();\n        this.$message({\n          type: 'success',\n          message: '文件删除成功!'\n        });\n      }\n    },\n    clearUpload() {\n      if (this.$refs.upload) {\n        this.$refs.upload.clearFiles()\n      }\n    },\n    async getFileList() {\n      let {code, data} = await getListByBusinessId({businessId: this.form.objId})\n      if (code === '0000') {\n        this.form.fileList = data;\n      }\n    },\n    //日期格式化  yyyy-MM-dd\n    dateFormatter(d) {\n      let year = d.getFullYear()\n      let month = d.getMonth() < 9 ? '0' + (d.getMonth() + 1) : '' + (d.getMonth() + 1)\n      let day = d.getDate() < 10 ? '0' + d.getDate() : '' + d.getDate()\n      return (year + '-' + month + '-' + day)\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.params = {...this.params, ...params}\n        if (this.activeNameTow==='1'){\n          this.params.islast = this.activeNameTow;\n          this.params.xl = 'bdz'\n        }else if(this.activeNameTow==='4'){\n          this.params.islast = 1;\n          this.params.xl = 'ty'\n        }else{\n          this.params.islast = this.activeNameTow;\n        }\n        if (this.params.fbrqArr && this.params.fbrqArr.length > 0) {\n          this.params.fbrqStart = this.dateFormatter(this.params.fbrqArr[0])\n          this.params.fbrqEnd = this.dateFormatter(this.params.fbrqArr[1])\n        }\n        if (this.params.zxrqArr && this.params.zxrqArr.length > 0) {\n          this.params.zxrqStart = this.dateFormatter(this.params.zxrqArr[0])\n          this.params.zxrqEnd = this.dateFormatter(this.params.zxrqArr[1])\n        }\n\n        getListTZ(this.params).then(res => {\n          if (res.code === '0000') {\n            this.tableAndPageInfo.tableData = res.data.records\n            this.tableAndPageInfo.pager.total = res.data.total\n            this.tableAndPageInfo.tableData.forEach(item => {\n              if (item.xl === \"bdz\"){\n                item.lxmc = \"变电站运行规程\"\n              }\n\n              if (item.xl === \"ty\"){\n                item.lxmc = \"通用运行规程\"\n              }\n              this.statusOptions.forEach(element => {\n                if (item.status === element.value) {\n                  item.statusName = element.label\n                }\n              })\n            })\n          }\n        })\n      } catch (e) {\n        console.log(e)\n      }\n    },\n    async uploadSuccess(response, file, fileList) {\n      if (++this.picCount === fileList.length) {\n        this.fileOpen = false\n        this.$message.success('操作成功')\n        this.picCount = 0;\n        this.$refs.upload.clearFiles()\n        this.loading.close();\n        await this.getData()\n      }\n    },\n\n    async saveRow() {\n      await this.$refs['form'].validate(async valid => {\n        if (valid) {\n          let {code, data} = await saveOrUpdateYxgc(this.form)\n          if (code === '0000') {\n            if (this.$refs.upload.uploadFiles.length>0){\n              this.loading=Loading.service({\n                lock: true,//lock的修改符--默认是false\n                text: '文件上传中，请稍后',//显示在加载图标下方的加载文案\n                spinner: 'el-icon-loading',//自定义加载图标类名\n                background: 'rgba(0, 0, 0, 0.7)',//遮罩层颜色\n                target: document.querySelector('#dialogAct')\n              });\n              this.uploadData.businessId = data.objId\n              this.$refs.upload.submit()\n            }else {\n              this.fileOpen = false\n              this.$message.success('操作成功')\n              await this.getData()\n            }\n          }\n        }\n      })\n    },\n    //编辑按钮\n    updateRow(row) {\n      this.isDisabled = false\n      this.clearUpload()\n      this.title = '修改标准规范'\n      this.isDisabled = false\n      this.form = {...row}\n      this.fileOpen = true\n    },\n    //详情按钮\n    getInfo(row) {\n      this.isDisabled = true\n      this.title = '标准规范详情'\n      this.form = {...row}\n      this.isDisabled = true\n      this.fileOpen = true\n      this.getShow();\n    },\n    //删除\n    deleteRow(id) {\n      let objId = ''\n      if(typeof id === 'object'){\n        if (this.ids.length < 1) {\n          this.$message.warning('请勾选的数据')\n          return\n        }\n        objId = this.ids[0]\n      }else{\n        objId = id\n      }\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        remove(JSON.stringify(objId)).then(({code}) => {\n          if (code === '0000') {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            })\n            this.getData()\n          } else {\n            this.$message({\n              type: 'error',\n              message: '删除失败!'\n            })\n          }\n        })\n      }).catch(() => {\n      })\n    },\n\n    addRow(dataFlag) {\n      this.isDisabled = false\n      this.resetForm('form')\n      this.clearUpload()\n      this.form = {\n        wjlx: '0',\n        sqdw: this.$store.getters.deptId.toString(),\n        fileList: []\n      }\n      this.title = '新增标准规范'\n      this.form.dataFlag=dataFlag\n      //获取当前登录人名称\n      this.form.fbr = this.$store.getters.nickName\n      this.fileOpen = true\n    },\n    filterReset() {\n      this.params = {\n        wjlx: '0',\n        mySorts: [{prop: 'updateTime', asc: false}],\n        fbrq: '',\n        zxrq: '',\n        status: ''\n      }\n    },\n    /**\n     * 上传文件之前的处理\n     */\n    beforeUpload(file) {\n      const fileSize = file.size < 1024 * 1024 * 200\n      if (!fileSize) {\n        this.$message.error('上传文件大小不能超过 200MB!')\n        this.loading.close();\n        return false;\n      }\n    },\n\n    /**\n     * 上传成功回调函数\n     */\n    onSuccess(response, file, fileList) {\n      //文件id\n      this.form.attachmentid = response.data.businessId\n      //文件名称\n      this.form.attachmentname = response.data.sysFile.fileOldName\n      //文件名称\n      this.form.wjmc = response.data.sysFile.fileOldName\n    },\n    //下载文件\n    downClick(file) {\n      this.$message.success(\"开始下载\")\n      downloadByBusinessId(file.objId,file.fileOldName)\n    },\n    /**下载附件*/\n    downloadHandle(id) {\n      downloadByFileId(id)\n    },\n    /**\n     * 关闭上传附件对话框\n     */\n    uploadClose() {\n      this.resetForm('form')\n      this.form.attachmentname = ''\n      this.fileOpen = false\n    },\n    //选择行\n    selectChange(rows) {\n      this.selectRows = rows\n      this.ids = rows.map(item => item.objId)\n      this.single = rows.length !== 1;\n    },\n    async initDomain() {\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.value === 'statusList') {\n          item.options = this.statusOptions\n        }\n      })\n\n    },\n    //关闭流程查看页面\n    colseTimeLine() {\n      this.timeLineShow = false\n    },\n    async showTimeLine(row) {\n      this.processData.businessKey = row.objId\n      let {code, data} = await HistoryList(this.processData)\n      this.timeData = data\n      this.timeLineShow = true\n    },\n    //流程图片查看\n    showProcessImg(row) {\n      this.openLoadingImg = true\n      this.imgSrc = '/activiti-api/process/read-resource?processDefinitionKey=yxgc&businessKey=' + row.objId + '&t=' + new Date().getTime()\n    },\n    async submit(type) {\n      this.fileOpen = false;\n      if (type === \"complete\") {\n        this.processData.status = this.form.status;\n        switch (this.form.status) {\n          case 0:\n            this.isShow = true;\n            this.activitiOption.title = \"上报\"\n            this.processData.defaultFrom = true\n            this.processData.processType = 'complete'\n            this.processData.businessKey = this.form.objId\n            this.processData.variables.pass = true\n            this.processData.personGroupId = 115\n            this.isShow = true\n            break;\n          case 9:\n            this.activitiOption.title = \"通过\"\n            this.processData.defaultFrom = true\n            this.processData.processType = 'complete'\n            this.processData.businessKey = this.form.objId\n            this.processData.variables.pass = true\n            this.processData.personGroupId = 85\n            this.isShow = true\n            break;\n          case 1:\n            this.isShow = true;\n            this.activitiOption.title = \"通过\"\n            this.processData.taskId = \"fgsldsh\"\n            this.processData.defaultFrom = true\n            this.processData.processType = 'complete'\n            this.processData.businessKey = this.form.objId\n            this.processData.variables.pass = true\n            this.isShow = true\n            break;\n          case 2:\n            if (this.form.dlddzxshr.includes(this.currentUser)) {\n              //todo 未全部通过\n              if (!this.form.dlddzxyshr) {\n                this.$confirm('确定提交吗?', '提示', {\n                  confirmButtonText: '确定',\n                  cancelButtonText: '取消',\n                  type: 'warning'\n                }).then(async () => {\n                  this.form.dlddzxyshr = this.currentUser\n                  await updateById(this.form).then(response => {\n                    if (response.code === '0000') {\n                      this.$message({\n                        type: 'success',\n                        message: '操作成功!'\n                      })\n                      this.tableAndPageInfo.pager.pageResize = 'Y'\n                      this.getData()\n                    }\n                  })\n                }).catch(() => {\n                  this.$message.info(\"已取消\")\n                })\n                break;\n              }\n              this.processData.taskId = \"zxddkzzxsh\"\n            }\n            this.activitiOption.title = \"通过\"\n            if (this.form.kjzxshr === this.currentUser) {\n              this.processData.taskId = \"kjxxzxsh\"\n            }\n            this.processData.defaultFrom = false\n            this.processData.processType = 'complete'\n            this.processData.businessKey = this.form.objId\n            this.processData.variables.pass = true\n            this.isShow = true\n            break;\n          case 3:\n            this.activitiOption.title = \"通过\"\n            this.processData.defaultFrom = true\n            this.processData.processType = 'complete'\n            this.processData.businessKey = this.form.objId\n            this.processData.variables.pass = true\n            this.processData.personGroupId = 82\n            this.processData.taskId = \"kjxxzxsh\"\n            this.isShow = true\n            break;\n          case 4:\n            //todo 未全部通过\n            if (!this.form.dlddzxyshr) {\n              this.$confirm('确定提交吗?', '提示', {\n                confirmButtonText: '确定',\n                cancelButtonText: '取消',\n                type: 'warning'\n              }).then(async () => {\n                this.form.dlddzxyshr = this.currentUser\n                await updateById(this.form).then(response => {\n                  if (response.code === '0000') {\n                    this.$message({\n                      type: 'success',\n                      message: '操作成功!'\n                    })\n                    this.tableAndPageInfo.pager.pageResize = 'Y'\n                    this.getData()\n                  }\n                })\n              }).catch(() => {\n                this.$message.info(\"已取消\")\n              })\n              break;\n            }\n            this.activitiOption.title = \"通过\"\n            this.processData.defaultFrom = true\n            this.processData.processType = 'complete'\n            this.processData.businessKey = this.form.objId\n            this.processData.variables.pass = true\n            this.processData.personGroupId = 82\n            this.processData.taskId = \"zxddkzzxsh\"\n            this.isShow = true\n            break;\n          case 5:\n            this.activitiOption.title = \"通过\"\n            this.processData.defaultFrom = true\n            this.processData.processType = 'complete'\n            this.processData.businessKey = this.form.objId\n            this.processData.variables.pass = true\n            this.processData.personGroupId = 83\n            this.isShow = true\n            break;\n          case 6:\n            this.activitiOption.title = \"通过\"\n            this.processData.defaultFrom = true\n            this.processData.processType = 'complete'\n            this.processData.businessKey = this.form.objId\n            this.processData.variables.pass = true\n            this.processData.personGroupId = 84\n            this.isShow = true\n            break;\n          case 7:\n            this.activitiOption.title = \"通过\"\n            this.processData.defaultFrom = false\n            this.processData.processType = 'complete'\n            this.processData.businessKey = this.form.objId\n            this.processData.variables.pass = true\n            this.isShow = true\n            break;\n        }\n      } else {\n        this.activitiOption.title = \"回退原因填写\"\n        if (this.form.dlddzxshr && this.form.dlddzxshr.includes(this.currentUser)) {\n          this.processData.taskId = \"zxddkzzxsh\"\n        }\n        if (this.form.kjzxshr === this.currentUser) {\n          this.processData.taskId = \"kjxxzxsh\"\n        }\n        this.processData.defaultFrom = true\n        this.processData.processType = 'rollback'\n        this.processData.businessKey = this.form.objId\n        this.processData.variables.pass = false\n        this.isShow = true\n      }\n    },\n    //流程结束回调\n    async todoResult(data) {\n      let row1 = {}\n      if (data.processType === \"rollback\") {\n        switch (data.activeTaskName) {\n          case 'yxgctb':\n            row1.status = 0\n            break;\n          case 'fgszgldsh':\n            row1.status = 9\n            break;\n          case 'fgsldsh':\n            row1.status = 1\n            break;\n          case 'sckzgsh':\n            row1.status = 5\n            break;\n          case 'sckkzsh':\n            row1.status = 6\n            break;\n        }\n      } else {\n        switch (data.activeTaskName) {\n          case 'fgszgldsh':\n            row1.status = 9\n            row1.sckzgspr = data.nextUser\n            break;\n          case 'fgsldsh':\n            row1.status = 1\n            row1.fgsspr = data.nextUser\n            break;\n          case 'zxddkzzxshkjxxzxsh':\n            row1.status = 2\n            row1.dlddzxyshr = \"\"\n            row1.fgsspsj = data.applyTime\n            row1.kjzxshr = data.nextUser\n            row1.dlddzxshr = data.nextUser2\n            break;\n          case 'kjxxzxsh':\n            row1.dlddzxshsj = data.applyTime\n            row1.status = 3\n            break;\n          case 'zxddkzzxsh':\n            row1.kjzxshsj = data.applyTime\n            row1.status = 4\n            break;\n          case 'sckzgsh':\n            if (data.taskId === \"zxddkzzxsh\") {\n              row1.dlddzxshsj = data.applyTime\n            }\n            if (data.taskId === \"kjxxzxsh\") {\n              row1.kjzxshsj = data.applyTime\n            }\n            row1.sckzgshr = data.nextUser\n            row1.status = 5\n            break;\n          case 'sckkzsh':\n            row1.status = 6\n            row1.sckzgshsj = data.applyTime\n            row1.sckkzshr = data.nextUser\n            break;\n          case 'gszgldsh':\n            row1.sckkzshsj = data.applyTime\n            row1.status = 7\n            row1.gszgldshr = data.nextUser\n            break;\n          case '结束':\n            row1.gszgldshsj = data.applyTime\n            row1.status = 8\n            break;\n        }\n      }\n      let row = {objId: data.businessKey, isStart: 1, isBack: data.processType === \"rollback\" ? 1 : 0}\n      row = {...row, ...row1}\n      updateById(row).then(response => {\n        if (response.code === '0000') {\n          this.$message({\n            type: 'success',\n            message: '操作成功!'\n          })\n          this.tableAndPageInfo.pager.pageResize = 'Y'\n          this.getData()\n        }\n      })\n    },\n    closeActiviti() {\n      /*if (this.$refs.activiti.$refs.form) {\n        this.$refs.activiti.$refs.form.resetFields();\n        console.log(this.$refs.activiti.$refs.form)\n      }*/\n      this.isShow = false\n    },\n  }\n}\n</script>\n\n<style lang='scss' scoped>\n.style-bottom {\n  margin-bottom: 4vh;\n}\n\n.pull-left {\n  margin-left: 2vw !important;\n}\n\n#pic_form .el-form-item__content {\n  margin-left: 0 !important;\n}\n\n.item {\n  width: 8.5rem;\n  height: 1.25rem;\n}\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>field/jszlgl"}]}