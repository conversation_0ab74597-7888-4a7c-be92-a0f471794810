{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\sybgjlcomment\\symbSyxmSelect.vue?vue&type=style&index=0&id=2991d146&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\sybgjlcomment\\symbSyxmSelect.vue", "mtime": 1706897323435}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCjExOAouYm94LWNhcmQgewogIG1hcmdpbi1ib3R0b206IDE1cHg7CgogIC5lbC1jYXJkX19oZWFkZXIgewogICAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDIzNSwgMjQ1LCAyNTUpICFpbXBvcnRhbnQ7CiAgfQp9CgovKuWvvOWHunBkZuagvOW8j+iuvue9ruW8gOWniyovCiNwcmludENvbnRlbnRJZCB7CiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsKICB3aWR0aDogMTAwJTsKICAvKiBoZWlnaHQ6IDQwMHB4OyAqLwogIG1hcmdpbjogYXV0bzsKICBwYWRkaW5nOiAxNnB4OwogIGJveC1zaXppbmc6IGJvcmRlci1ib3g7CiAgLy/or5XpqozmlbDmja7moLflvI8KICAucHJpbnRUaXRsZSB7CiAgICB0ZXh0LWFsaWduOiBsZWZ0OwogICAgbGluZS1oZWlnaHQ6IDQwcHg7CiAgICBib3JkZXItbGVmdDogMXB4IHNvbGlkICMwMDA7CiAgICBib3JkZXItcmlnaHQ6IDFweCBzb2xpZCAjMDAwOwogICAgLy9ib3JkZXItYm90dG9tOiAxcHggc29saWQgIzAwMDsKICAgIHBhZGRpbmctbGVmdDogMTBweDsKICB9Cn0KCi8v5L+u5pS5dGFibGXooajlpLTpopzoibIKL2RlZXAvICNwcmludENvbnRlbnRJZCAuZWwtdGFibGUgLmVsLXRhYmxlX19oZWFkZXItd3JhcHBlciB0aCwgLmVsLXRhYmxlIC5lbC10YWJsZV9fZml4ZWQtaGVhZGVyLXdyYXBwZXIgdGggewogIGJhY2tncm91bmQ6ICNmZmY7CiAgYm9yZGVyLWNvbG9yOiAjMDAwOwogIGZvbnQtd2VpZ2h0OiBpbmhlcml0Owp9CgovZGVlcC8gI3ByaW50Q29udGVudElkIC5lbC10YWJsZS0tZW5hYmxlLXJvdy10cmFuc2l0aW9uIC5lbC10YWJsZV9fYm9keSB0ZCB7CiAgYm9yZGVyLWNvbG9yOiAjMDAwOwp9CgoudGFibGVfc3R5bGUgdGQsIHRoIHsKICBwYWRkaW5nOiAxMHB4OwogIGZvbnQtc2l6ZTogMTVweDsKfQoKLnRhYmxlX3N0eWxlIHsKICBib3JkZXItY29sbGFwc2U6IGNvbGxhcHNlOwogIHdpZHRoOiAxMDAlOwogIHRleHQtYWxpZ246IGNlbnRlcjsKICAvKiBib3JkZXItYm90dG9tOiAwOwogICBib3JkZXItbGVmdDogMDsKICAgYm9yZGVyLXJpZ2h0OiAwOyovCn0KCi8qIOiuvue9rua7muWKqOadoeeahOagt+W8jyAqLwo6Oi13ZWJraXQtc2Nyb2xsYmFyIHsKICB3aWR0aDogMTJweDsKfQoKLyog5rua5Yqo5qe9ICovCjo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sgewogIC8vLXdlYmtpdC1ib3gtc2hhZG93Omluc2V0MDA2cHhyZ2JhKDAsMCwwLDAuMyk7CiAgYm9yZGVyLXJhZGl1czogMTBweDsKfQoKLyog5rua5Yqo5p2h5ruR5Z2XICovCjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIgewogIGJvcmRlci1yYWRpdXM6IDEwcHg7CiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjEpOwogIC8vLXdlYmtpdC1ib3gtc2hhZG93OmdiYSgwLDAsMCwwLjUpOwp9Cgo6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iOndpbmRvdy1pbmFjdGl2ZSB7CiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjEpOwp9CgpbZGF0YS12LTY3YTk3NGIxXTo6LXdlYmtpdC1zY3JvbGxiYXIgewogIHdpZHRoOiA4cHg7Cn0KCi5pdGVtIHsKICB3aWR0aDogMjI1cHg7CiAgZmxvYXQ6IGxlZnQ7Cn0KCi8q5a+85Ye6cGRm5qC85byP6K6+572u57uT5p2fKi8KCi5pdGVtIHsKICB3aWR0aDogMjAwcHg7CiAgaGVpZ2h0OiAxNDhweDsKICBmbG9hdDogbGVmdDsKfQoKLmhlYWQtY29udGFpbmVyIHsKICBtYXJnaW46IDAgYXV0bzsKICB3aWR0aDogOTglOwogIG1heC1oZWlnaHQ6IDc5dmg7CiAgb3ZlcmZsb3c6IGF1dG87Cn0KCi5hcHAtY29udGFpbmVyIHsKICBwYWRkaW5nOiAzcHg7Cn0K"}, {"version": 3, "sources": ["symbSyxmSelect.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAysCA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "symbSyxmSelect.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk/sybgjlcomment", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row>\n      <!--右侧列表-->\n      <el-col :span=\"24\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 140, itemWidth: 190 }\"\n          @handleReset=\"filterReset\"\n        />\n        <el-white>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            :is-single=\"true\"\n            height=\"61.5vh\">\n\n            <!--            <el-table-column slot=\"table_six\" align=\"center\" label=\"关联铭牌\" min-width=\"150\" :resizable=\"false\">-->\n            <!--              <template slot-scope=\"scope\">-->\n            <!--                <el-button @click=\"handleClickGlMp(scope.row)\" type=\"text\" size=\"small\" v-if=\"scope.row.sfty == '是'\">-->\n            <!--                  查看铭牌-->\n            <!--                </el-button>-->\n            <!--                <el-button @click=\"handleClickGlMp(scope.row)\" type=\"text\" size=\"small\" v-else>关联铭牌</el-button>-->\n            <!--              </template>-->\n            <!--            </el-table-column>-->\n\n            <!--            <el-table-column slot=\"table_seven\" align=\"center\" label=\"关联项目\" min-width=\"150\" :resizable=\"false\">-->\n            <!--              <template slot-scope=\"scope\">-->\n            <!--                <el-button @click=\"handleClickGlxm(scope.row)\" type=\"text\" size=\"small\" v-if=\"scope.row.sfty == '是'\">-->\n            <!--                  查看项目-->\n            <!--                </el-button>-->\n            <!--                <el-button @click=\"handleClickGlxm(scope.row)\" type=\"text\" size=\"small\" v-else>关联项目</el-button>-->\n            <!--              </template>-->\n            <!--            </el-table-column>-->\n\n            <!--            <el-table-column slot=\"table_eight\" align=\"center\" label=\"定义模板内容\" min-width=\"150\" :resizable=\"false\">-->\n            <!--              <template slot-scope=\"scope\">-->\n            <!--                <el-button @click=\"handleClickMbnr(scope.row)\" type=\"text\" size=\"small\" v-if=\"scope.row.sfty == '是'\">-->\n            <!--                  查看模板内容-->\n            <!--                </el-button>-->\n            <!--                <el-button @click=\"handleClickMbnr(scope.row)\" type=\"text\" size=\"small\" v-else>定义模板内容</el-button>-->\n            <!--              </template>-->\n            <!--            </el-table-column>-->\n\n            <el-table-column slot=\"table_eight\" align=\"center\" label=\"模板详情\" min-width=\"150\" :resizable=\"false\">\n              <template slot-scope=\"scope\">\n                <el-button v-print=\"printObj\" type=\"text\" size=\"small\" @click=\"handleMbInfo(scope.row)\">模板详情</el-button>\n              </template>\n            </el-table-column>\n\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n    <el-row>\n      <div style=\"text-align: right;margin-top: 2vh\">\n        <el-button @click=\"closeSymbComment\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"commitMbdata\">确 定</el-button>\n      </div>\n    </el-row>\n    <!--新增\\修改\\详情弹出-->\n    <el-dialog :title=title :visible.sync=\"isShowDetails\" width=\"60%\" v-dialogDrag>\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"mbzbRules\">\n        <el-row :gutter=\"8\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"设备类型：\" prop=\"sblx\">\n              <el-input v-model=\"form.sblx\" disabled style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"设备类型编码：\" prop=\"sblxbm\">\n              <el-input v-model=\"form.sblxid\" disabled style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"模板名称：\" prop=\"mbmc\">\n              <el-input placeholder=\"请输入试验部位名称\" v-model=\"form.mbmc\" :disabled=\"isDisabled\"\n                        style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否默认：\" prop=\"sfmr\">\n              <el-select v-model=\"form.sfmr\" style=\"width: 100%\" :disabled=\"isDisabled\">\n                <el-option label=\"是\" value=\"是\"></el-option>\n                <el-option label=\"否\" value=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否停用：\" prop=\"sfty\">\n              <el-select v-model=\"form.sfty\" style=\"width: 100%\" :disabled=\"isDisabled\">\n                <el-option label=\"是\" value=\"是\"></el-option>\n                <el-option label=\"否\" value=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" v-show=\"isShow\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"save\">确 认</el-button>\n      </div>\n    </el-dialog>\n\n    <!--关联铭牌弹框-->\n    <el-dialog :visible.sync=\"showMpDialog\" title=\"已关联铭牌\" v-if=\"showMpDialog\" v-dialogDrag>\n      <glsymp :main-data=\"rowData\" :tree-data=\"treeForm\" @closeMpDialog=\"closeMpDialog\"></glsymp>\n    </el-dialog>\n\n    <!--关联试验项目弹出框-->\n    <el-dialog :title=\"glxmDialogTitle\" :visible.sync=\"isGlxmDialogShow\" width=\"60%\" v-dialogDrag>\n      <el-row :gutter=\"3\">\n        <div class=\"mb8 pull-right\">\n          <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"mini\" @click=\"addMbGlXm\">新增项目</el-button>\n          <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"mini\" @click=\"deleteMbGlXm\">删除项目</el-button>\n        </div>\n      </el-row>\n      <el-row :gutter=\"20\">\n        <el-col :span=\"12\">\n          <el-table :data=\"mbGlxmDataList\" @selection-change=\"handleGlxmSelectedChange\"\n                    @row-click=\"handleMbGlxmRowClick\">\n            <el-table-column label=\"试验项目\" align=\"center\">\n              <el-table-column type=\"selection\" width=\"55\" align=\"center\"/>\n              <el-table-column label=\"序号\" type=\"index\" width=\"50\" align=\"center\"></el-table-column>\n              <el-table-column prop=\"syxmmc\" label=\"项目名称\" width=\"180\" align=\"center\"></el-table-column>\n              <el-table-column prop=\"syxmms\" label=\"项目描述\" align=\"center\"></el-table-column>\n            </el-table-column>\n          </el-table>\n          <pagination\n            :total=\"glxmTotal\"\n            :page.sync=\"glxmQueryParams.pageNum\"\n            :limit.sync=\"glxmQueryParams.pageSize\"\n            @pagination=\"getSymbGlsyxmDataListByPage\"\n          />\n        </el-col>\n        <el-col :span=\"12\">\n          <el-table :data=\"zxmGlmbDataList\">\n            <el-table-column label=\"试验子项目\" align=\"center\">\n              <el-table-column prop=\"syzxmmc\" label=\"子项目名称\" width=\"180\" align=\"center\"></el-table-column>\n              <el-table-column prop=\"syzxmms\" label=\"子项目描述\" align=\"center\"></el-table-column>\n            </el-table-column>\n          </el-table>\n          <pagination\n            :total=\"glzxmTotal\"\n            :page.sync=\"glzxmQueryParams.pageNum\"\n            :limit.sync=\"glzxmQueryParams.pageSize\"\n            @pagination=\"getZxmDataList\"\n          />\n        </el-col>\n      </el-row>\n\n    </el-dialog>\n    <!--列表新增关联项目弹窗调用-->\n    <el-dialog :title=\"xmLibraryAddDialogTitle\" :visible.sync=\"isShowAddGlxmDialog\" width=\"50%\" v-dialogDrag>\n      <el-form label-width=\"120px\">\n        <el-row :gutter=\"3\">\n          <el-col :span=\"10\">\n            <el-form-item label=\"项目名称：\">\n              <el-input v-model=\"xmLibraryQueryForm.syxmmc\"/>\n            </el-form-item>\n          </el-col>\n          <div class=\"mb8 pull-right\">\n            <el-button type=\"cyan\" size=\"mini\" icon=\"el-icon-search\" @click=\"selectxmLibrary\">查询</el-button>\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetxmSearch\">重置</el-button>\n          </div>\n        </el-row>\n      </el-form>\n      <el-table stripe border :data=\"xmLibraryDataList\" @selection-change=\"handleSelectedXmLibraryChange\"\n                :header-cell-style=\"{'text-align':'center'}\" :cell-style=\"{'text-align':'center'}\">\n        <el-table-column type=\"selection\" width=\"55\"/>\n        <el-table-column label=\"序号\" type=\"index\" width=\"50\">\n        </el-table-column>\n        <el-table-column label=\"试验项目\" prop=\"syxmmc\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"项目描述\" prop=\"syxmms\" :show-overflow-tooltip=\"true\"/>\n      </el-table>\n      <pagination\n        v-show=\"xmLibraryTotal>0\"\n        :total=\"xmLibraryTotal\"\n        :page.sync=\"xmLibraryQueryForm.pageNum\"\n        :limit.sync=\"xmLibraryQueryForm.pageSize\"\n        @pagination=\"getXmLiraryData\"\n      />\n      <div slot=\"footer\">\n        <el-button @click=\"closeAddMjzDialog\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"commitAddMjzForm\">确 认</el-button>\n      </div>\n    </el-dialog>\n    <!--子组件定义模板内容-->\n    <el-dialog title=\"项目关联部位\" :visible.sync=\"isShowXmGlbwDialog\" v-if=\"isShowXmGlbwDialog\" v-dialogDrag>\n      <symbwh-dymbnr ref=\"symbwhDymbnrRef\" :mb-data=\"mbRowData\"></symbwh-dymbnr>\n    </el-dialog>\n    <!--  打印vue print nb插件-->\n    <div v-show=\"false\">\n      <div id=\"printHtmlId\" style=\"background:white;\">\n        <!--模板-->\n        <div style=\"text-align: center\">\n          <p>{{mbInfo.mbmc}}</p>\n        </div>\n        <p>葫芦娃，葫芦娃</p>\n        <p>一根藤上七朵花 </p>\n        <p>小小树藤是我家 啦啦啦啦 </p>\n        <p>叮当当咚咚当当　浇不大</p>\n        <p> 叮当当咚咚当当 是我家</p>\n        <p> 啦啦啦啦</p>\n        <p>...</p>\n        <div class=\"describle\">\n          <el-form :model=\"mbInfo\" :rules=\"mbzbRules\" ref=\"from\" class=\"demo-ruleForm\">\n            <el-form-item label=\"姓名:\" prop=\"name\">\n              <el-input v-model=\"mbInfo.mbmc\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"描述:\" prop=\"describle\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"4\"\n                :maxlength=\"2000\"\n                placeholder=\"\"\n                v-model=\"mbInfo.mbmc\">\n              </el-input>\n            </el-form-item>\n          </el-form>\n        </div>\n      </div>\n    </div>\n    <!--htmlToPdf插件-->\n    <el-dialog title=\"预览\" :visible.sync=\"isShowDownLoadDialog\" width=\"60%\" append-to-body v-dialogDrag>\n      <el-button @click=\"downloadPdf\">导出</el-button>\n      <div style=\"width: 100%;height:60vh;overflow: auto\">\n        <div id=\"printContentId\">\n          <div style=\"text-align: center\">\n            <h2>{{mbInfo.mbmc}}</h2></div>\n          <div>\n            <div\n              style=\"font-size: 20px;float: left;width:100%;border-left: 1px solid #000;border-right: 1px solid #000;border-top: 1px solid #000;\">\n              一、基本信息\n            </div>\n            <el-table\n              :data=\"tableData_jbxx\"\n              border\n              style=\"border: 1px solid #000;\"\n            >\n              <el-table-column\n                prop=\"column_1\"\n                label=\"变电站\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_5\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_2\"\n                label=\"委托单位\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_6\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_3\"\n                label=\"试验单位\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_7\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_4\"\n                label=\"运行编号\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_8\"\n                label=\"\"\n              >\n              </el-table-column>\n            </el-table>\n            <!--            <tbody>\n                        <tr>\n                          <td>变电站</td>\n                          <td></td>\n                          <td>委托单位</td>\n                          <td></td>\n                          <td>试验单位</td>\n                          <td></td>\n                          <td>运行编号</td>\n                          <td></td>\n                        </tr>\n                        <tr>\n                          <td>试验性质</td>\n                          <td></td>\n                          <td>试验日期</td>\n                          <td></td>\n                          <td>试验人员</td>\n                          <td></td>\n                          <td>试验地点</td>\n                          <td></td>\n                        </tr>\n                        <tr>\n                          <td>报告日期</td>\n                          <td></td>\n                          <td>编写人</td>\n                          <td></td>\n                          <td>审核人</td>\n                          <td></td>\n                          <td>批准人</td>\n                          <td></td>\n                        </tr>\n                        <tr>\n                          <td>试验天气</td>\n                          <td></td>\n                          <td>环境温度（℃）</td>\n                          <td></td>\n                          <td>环境相对湿度（%）</td>\n                          <td></td>\n                          <td>投运日期</td>\n                          <td></td>\n                        </tr>\n                        </tbody>-->\n          </div>\n          <div>\n            <div\n              style=\"font-size: 20px;float: left;width:100%;border-left: 1px solid #000;border-right: 1px solid #000;\">\n              二、设备铭牌\n            </div>\n            <el-table\n              :data=\"tableData_sbmp\"\n              border\n              :span-method=\"sbmpSpanMethod\"\n              style=\"border: 1px solid #000;\"\n            >\n              <el-table-column\n                prop=\"column_1\"\n                label=\"生产厂家\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_4\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_2\"\n                label=\"出厂编号\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_5\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_3\"\n                label=\"出厂日期\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_6\"\n                label=\"\"\n              >\n              </el-table-column>\n            </el-table>\n            <!--            <tbody id=\"sbmpTbodyId\">\n                        </tbody>-->\n          </div>\n          <div>\n            <div\n              style=\"font-size: 20px;float: left;width:100%;border-left: 1px solid #000;border-right: 1px solid #000;border-bottom: 1px solid #000;\">\n              三、试验数据\n            </div>\n            <div v-for=\"item in arr\" style=\"width: 100%\">\n              <div class=\"printTitle\">{{item.title}}</div>\n              <el-table :data=\"item.bwList\" style=\"width:100%;border: 1px solid #000;\" border\n                        :span-method=\"arraySpanMethod\">\n                <template v-for='(val) in item.zxmList'>\n                  <el-table-column\n                    :prop=\"val.column_name\"\n                    width=\"auto\"\n                    :label=\"val.label\"\n                  >\n                  </el-table-column>\n\n                </template>\n\n              </el-table>\n            </div>\n\n            <!--            <tbody id=\"sysjTableId\">\n                        <tr>\n                          <td colspan=\"5\" style=\"text-align: left;font-weight: bold\">12121212</td>\n                        </tr>\n                        <tr>\n                          <td>部位</td>\n                          <td>回路电阻初值(μΩ)</td>\n                          <td>回路电阻(μΩ)</td>\n                          <td>主回路电阻初值差(%)</td>\n                          <td>是否合格</td>\n                        </tr>\n                        <tr>\n                          <td>部位1</td>\n                          <td></td>\n                          <td></td>\n                          <td></td>\n                          <td></td>\n                        </tr>\n                        <tr>\n                          <td>部位2</td>\n                          <td></td>\n                          <td></td>\n                          <td></td>\n                          <td></td>\n                        </tr>\n                        <tr>\n                          <td>仪器型号</td>\n                          <td colspan=\"4\"></td>\n                        </tr>\n                        <tr>\n                          <td>结论</td>\n                          <td colspan=\"4\"></td>\n                        </tr>\n                        <tr>\n                          <td>备注</td>\n                          <td colspan=\"4\"></td>\n                        </tr>\n                        </tbody>-->\n          </div>\n        </div>\n      </div>\n      <div slot=\"footer\">\n        <el-button @click=\"closeYlDialog\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  //引入jquery,暂时没用\n  import $ from \"jquery\"\n  import {\n    addMbGlxmBatchToMbxm,\n    getPageDataListTosymb,\n    getSymbGlsyxmDataListByPage,\n    getXmLiraryData,\n    remove,\n    saveOrUpdate,\n    getMbGlMpinfoData,\n    getMbGlXmAndBw\n  } from '@/api/dagangOilfield/bzgl/symbwh'\n  import {getDeviceClassTreeNodeByPid} from '@/api/dagangOilfield/bzgl/sblxwh/sblxwh'\n  import {getGlSyzxmDataListByPage} from '@/api/dagangOilfield/bzgl/syxm'\n  import Glsymp from '@/views/dagangOilfield/bzgl/sybzk/glsymp'\n  import symbwhDymbnr from '@/views/dagangOilfield/bzgl/sybzk/symbwhDymbnr'\n  import htmlToPdf from '@/utils/print/htmlToPdf'\n\n  export default {\n    name: 'symbSyxmSelect',\n    components: {Glsymp, symbwhDymbnr},\n     props: {\n      //组件传值\n      symbData: {\n        type: Object,\n        default: () => ({\n          sblxid:'',  \n        })\n      },\n\n    },\n    data() {\n      return {\n        //基本信息表格数据\n        tableData_jbxx: [\n          {\n            'column_1': '试验性质',\n            'column_2': '试验日期',\n            'column_3': '试验人员',\n            'column_4': '试验地点',\n            'column_5': '',\n            'column_6': '',\n            'column_7': '',\n            'column_8': '',\n          },\n          {\n            'column_1': '报告日期',\n            'column_2': '编写人',\n            'column_3': '审核人',\n            'column_4': '批准人',\n            'column_5': '',\n            'column_6': '',\n            'column_7': '',\n            'column_8': '',\n          },\n          {\n            'column_1': '试验天气',\n            'column_2': '环境温度（℃）',\n            'column_3': '环境相对湿度（%）',\n            'column_4': '投运日期',\n            'column_5': '',\n            'column_6': '',\n            'column_7': '',\n            'column_8': '',\n          }\n        ],\n        //设备铭牌表格数据\n        tableData_sbmp: [{\n          'column_1': '额定电压',\n          'column_2': '设备型号',\n          'column_3': '',\n          'column_4': '',\n          'column_5': '',\n          'column_6': '',\n        },],\n        //要循环的试验表格数据\n        arr: [{\n          title: \"\",//试验名称\n          zxmList: [],//子项目数据（表头）\n          bwList: [],//部位数据（第一列开头）\n        }],\n        //下载弹出框控制\n        isShowDownLoadDialog: false,\n        printObj: {\n          id: \"previewId\", // 必填，渲染打印的内容使用\n          popTitle: \"&nbsp;\", //\n          previewTitle: \"&nbsp;\",\n          preview: false,\n        },\n        mbInfo: {},\n        //打印内容div中id值\n        previewId: \"\",\n        //定义模板内容弹出框传递参数\n        mbRowData: {},\n        //定义模板内容弹出框\n        isShowXmGlbwDialog: false,\n        xmSelectedForm: {\n          //试验模板id\n          symbid: undefined,\n          //试验项目数据集合\n          xmDataRows: []\n        },\n        //项目库弹出框标题\n        xmLibraryAddDialogTitle: '项目库',\n        //项目库弹出框控制\n        isShowAddGlxmDialog: false,\n        //项目库查询参数\n        xmLibraryQueryForm: {\n          symbid: undefined,\n          syxmmc: '',\n          pageNum: 1,\n          pageSize: 10\n        },\n        //项目库数据\n        xmLibraryDataList: [],\n        //项目库项目总数\n        xmLibraryTotal: 0,\n        //表单验证\n        mbzbRules: {\n          mbmc: [\n            {required: true, message: '请输入模板名称', trigger: 'blur'}\n          ]\n        },\n        // 筛选条件\n        filterInfo: {\n          data: {\n            mbmc: ''\n          },\n          fieldList: [\n            {label: '模板名称', type: 'input', value: 'mbmc', multiple: true}\n          ]\n        },\n        //新增按钮控制\n        addDisabled: true,\n        //删除选择列\n        selectRows: [],\n        //选中的单条对象\n        selectRowData: {},\n        //弹出框表单\n        form: {},\n        //查询试验部位参数\n        querySyBwParam: {\n          sblxid: undefined,\n          mbmc: undefined,\n          pageNum: 1,\n          pageSize: 10\n        },\n        //点击树节点赋值\n        treeForm: {},\n        //试验部位列表\n        tableAndPageInfo: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          tableData: [],\n          tableHeader: [\n            {label: '模板名称', prop: 'mbmc', minWidth: '100'},\n            {label: '是否默认', prop: 'sfmr', minWidth: '100'},\n            {label: '是否停用', prop: 'sfty', minWidth: '100'},\n          ],\n          option: {checkBox: true, serialNumber: true}\n        },\n        //组织树\n        treeOptions: [],\n\n        isShowDetails: false,\n        title: '',\n\n        //删除是否可用\n        multipleSensor: true,\n        //查询参数\n        queryParams: {\n          bm: undefined,\n          pageNum: 1,\n          pageSize: 10\n        },\n        isDisabled: false,\n        isShow: true,\n\n        //关联项目弹出框title\n        glxmDialogTitle: '关联项目',\n        //关联项目弹出框控制展开\n        isGlxmDialogShow: false,\n\n        //关联项目total\n        glxmTotal: 0,\n        //关联项目查询参数\n        glxmQueryParams: {\n          symbid: undefined,\n          pageNum: 1,\n          pageSize: 10\n        },\n\n        //关联子项目total\n        glzxmTotal: 0,\n        //关联子项目查询参数\n        glzxmQueryParams: {\n          syxmid: undefined,\n          pageSize: 10,\n          pageNum: 1\n        },\n\n        //模板关联项目数据\n        mbGlxmDataList: [],\n        //项目关联的子项目数据\n        zxmGlmbDataList: [],\n        //模板关联项目选中框数据\n        selectedRowDataChange: [],\n        //显示铭牌弹框\n        showMpDialog: false,\n        //选中行数据\n        rowData: {},\n\n        //关联名牌便利\n        mpList: [],\n\n        //试验数据\n        sysjDataList: [],\n        //试验表格默认固定的行\n        defaultRow: [\"仪器型号\", \"结论\", \"备注\"],\n      }\n    },\n    watch: {},\n    created() {\n      //获取数据列表\n      this.getData()\n    },\n    mounted() {\n    },\n    methods: {\n      //试验数据表格合并方法\n      arraySpanMethod({row, column, rowIndex, columnIndex}) {\n        if (this.defaultRow.includes(row.SYBW)) {\n          if (columnIndex > 0) {\n            return [1, row.totalNum]\n          }\n        }\n      },\n      //设备铭牌表格合并方法\n      sbmpSpanMethod({row, column, rowIndex, columnIndex}) {\n        if (columnIndex > 3) {\n          return [1, 2]\n        }\n      },\n      //模板详情按钮\n      handleMbInfo(row) {\n        //获取当前模板id加载页面信息\n        this.getMbGlMpinfoData(row);\n        //获取试验数据\n        this.getMbGlXmAndBw(row);\n      },\n      //导出pdf操作\n      downloadPdf() {\n        htmlToPdf.downloadPDF(document.querySelector('#printContentId'), this.mbInfo.mbmc)\n      },\n      //获取当前模板id加载页面信息\n      getMbGlMpinfoData(param) {\n        getMbGlMpinfoData(param).then(res => {\n          this.mpList = res.data;\n          //调用渲染铭牌页面开始\n          // this.applyMpHtml(this.mpList, param);\n          //打开弹出框\n          this.isShowDownLoadDialog = true;\n        })\n\n      },\n      //获取试验数据\n      getMbGlXmAndBw(rowData) {\n        //每次获取数据前先清空，再添加，否则多次进入页面时会获取重复数据\n        this.sysjDataList = [];\n        getMbGlXmAndBw(rowData).then(res => {\n          let resMap = res.data;\n          //遍历返回结果\n          for (let key in resMap) {\n            //解析试验数据\n            this.analysisSyData(key, resMap[key]);\n          }\n          //画试验数据页面\n          this.applySysjDataToHtml();\n        })\n      },\n      //解析后台试验数据\n      analysisSyData(syxmmc, zxmAndBwData) {\n        let sysjData = {}\n        sysjData.syxmmc = syxmmc;\n        for (let key in zxmAndBwData[0]) {\n          sysjData[key] = zxmAndBwData[0][key]\n        }\n        this.sysjDataList.push(sysjData);\n      },\n      //渲染实验数据到页面\n      applySysjDataToHtml() {\n        this.arr = [];\n        // $('#sysjTableId').html(\"\");\n        //进行数据处理重组\n        let data = this.sysjDataList;\n        if (data.length > 0) {\n          for (let i = 0; i < data.length; i++) {\n            let dataChild = data[i];\n            let title = dataChild.syxmmc;//试验项目名称\n            let bwList = dataChild.bwList; //部位list\n            let zxmList = dataChild.zxmList; //子项目list\n            let hx = [\n              {\n                \"label\": title, //第一个表头为试验项目名称\n                \"column_name\": \"SYBW\", //第一列对应的字段名（试验部位）\n              },\n            ];\n            zxmList.forEach(zxm => {\n              hx.push({\n                \"label\": zxm.syzxmmc, //每列的表头\n                \"column_name\": \"\", //每列对应的数值暂时设置为空白\n              })\n            })\n            let sx = [];\n            bwList.forEach(bw => {\n              sx.push({\n                \"SYBW\": bw.SYBW,\n                \"totalNum\": zxmList.length//记录总列数，用于固定行的合并事件\n              })\n            })\n            //后四行固定\n            sx.push(\n              {\n                \"SYBW\": \"结果\",\n                \"totalNum\": zxmList.length//记录总列数，用于固定行的合并事件\n              },\n              {\n                \"SYBW\": \"仪器型号\",\n                \"totalNum\": zxmList.length//记录总列数，用于固定行的合并事件\n              },\n              {\n                \"SYBW\": \"结论\",\n                \"totalNum\": zxmList.length//记录总列数，用于固定行的合并事件\n              },\n              {\n                \"SYBW\": \"备注\",\n                \"totalNum\": zxmList.length//记录总列数，用于固定行的合并事件\n              }\n            )\n            this.arr.push({\n              title: title,\n              zxmList: hx,\n              bwList: sx,\n            });\n          }\n        }\n        //拼接的铭牌表格\n        /*     let str = \"\";\n             if (this.sysjDataList.length > 0) {\n               for (let i = 0; i < this.sysjDataList.length; i++) {\n                 //拼接项目序号\n                 let xmIndex = i + 1;\n                 str += \"<tr><td colspan='5' style='text-align: left;font-weight: bold;font-size: 15px'>\" + xmIndex + \"、\" + this.sysjDataList[i].syxmmc + \"</td></tr>\";\n                 // this.sysjDataList[i].bwList;\n                 // this.sysjDataList[i].zxmList;\n                 // str += \"<tr><td>\"+this.sysjDataList[i].syxmmc+\"</td><td v-for=item in this.sysjDataList[i].bwList></td></tr>\"\n\n               }\n               this.$nextTick(() => {\n                 $('#sysjTableId').append(str)\n               })\n             }*/\n      },\n      //渲染铭牌页面开始mpList:反回的铭牌列表  row：模板行对象\n      applyMpHtml(mpList, row) {\n        //每次打开需要重新渲染一次,先将置空\n        $('#sbmpTbodyId').html(\"\");\n        //清空重新赋值\n        this.mbInfo = {}\n        this.mbInfo.mbmc = row.mbmc;\n        //拼接的铭牌表格\n        let str = \"\";\n        //先判断是否分相铭牌\n        if (mpList.length > 0) {\n          if (mpList[0].SFFX == '1') { //当前铭牌为分相铭牌时\n            //写死第一行\n            str += \"<tr><td style='padding: 10px;font-size: 15px;'>相别</td>\" +\n              \"<td style='padding: 10px;font-size: 15px;'>A</td>\" +\n              \"<td style='padding: 10px;font-size: 15px;'>B</td>\" +\n              \"<td style='padding: 10px;font-size: 15px;'>C</td>\" +\n              \"</tr>\";\n            //开始遍历展示\n            for (let a = 0; a < mpList.length; a++) {\n              str += \"<tr>\"\n              str += \"<td style='padding: 10px;font-size: 15px;'>\";\n              str += mpList[a].title + \"</td>\";\n              str += \"<td></td>>\"\n              str += \"<td></td>>\"\n              str += \"<td></td>>\"\n              str += \"</tr>\"\n            }\n          } else {  //铭牌不分相\n            //当前铭牌不属于分相铭牌\n            //每列展示单元格数量\n            let col = 3;\n            //展示行数\n            var lines = Math.ceil(mpList.length / col);\n            //遍历展示行数\n            for (var i = 0; i < lines; i++) {\n              str += \"<tr>\";\n              //遍历列\n              for (var j = 0; j < col; j++) {\n                if (i * col + j < mpList.length) {\n                  str += \"<td style='padding: 10px;font-size: 15px;'>\";\n                  //铭牌标题赋值\n                  str += mpList[i * col + j].title + \"</td>\";\n                  //铭牌值赋值\n                  str += mpList[i * col + j].sfmb == 1 ? \"<td> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>\" : \"<td>\" + mpList[i * col + j].column_name + \"</td>\"\n                }\n              }\n              str += \"</tr>\";\n            }\n          }\n        }\n        //渲染铭牌页面\n        this.$nextTick(() => {\n          $('#sbmpTbodyId').append(str)\n        })\n        //打开弹出框\n        this.isShowDownLoadDialog = true;\n      },\n      //关闭预览弹出框\n      closeYlDialog() {\n        //清空表单\n        this.mbInfo = {};\n        //赋值完关闭弹窗\n        this.isShowDownLoadDialog = false;\n      }\n      ,\n      //定义模板内容\n      handleClickMbnr(row) {\n        //打开组件弹出框\n        this.isShowXmGlbwDialog = true;\n        //给子组件传递数据\n        this.mbRowData = row;\n      }\n      ,\n      //获取项目库项目数据\n      getXmLiraryData() {\n        getXmLiraryData(this.xmLibraryQueryForm).then(res => {\n          this.xmLibraryDataList = res.data.records\n          this.xmLibraryTotal = res.data.total\n        })\n      }\n      ,\n      //项目弹出框新增按钮\n      addMbGlXm() {\n        this.getXmLiraryData()\n        this.isShowAddGlxmDialog = true\n      }\n      ,\n      //项目库弹出框取消按钮\n      closeAddMjzDialog() {\n        this.isShowAddGlxmDialog = false\n      }\n      ,\n      //项目库弹窗确认按钮\n      commitAddMjzForm() {\n        if (this.xmSelectedForm.xmDataRows.length < 1) {\n          this.$message.info('未关联项目！！！已取消')\n          //如果未选中数据,则直接关闭弹窗\n          this.isShowAddGlxmDialog = false\n        } else {\n          console.log(this.xmSelectedForm)\n          //若选择数据后\n          addMbGlxmBatchToMbxm(this.xmSelectedForm).then(res => {\n            if (res.code === '0000') {\n              this.$message.success('关联成功')\n            } else {\n              this.$message.error('关联失败！！')\n            }\n            //关闭弹窗\n            this.isShowAddGlxmDialog = false\n            //调用获取关联子项目列表\n            this.getSymbGlsyxmDataListByPage()\n          })\n        }\n      }\n      ,\n      //项目库行选中事件\n      handleSelectedXmLibraryChange(rows) {\n        this.xmSelectedForm.xmDataRows = rows\n      }\n      ,\n      //项目库查询按钮\n      selectxmLibrary() {\n        this.getXmLiraryData()\n      }\n      ,\n      //项目库重置按钮\n      resetxmSearch() {\n        this.xmLibraryQueryForm.syxmmc = ''\n        this.getXmLiraryData()\n      }\n      ,\n      //获取关联子列表方法\n      getZxmDataList() {\n        getGlSyzxmDataListByPage(this.glzxmQueryParams).then(res => {\n          this.glzxmTotal = res.data.total\n          this.zxmGlmbDataList = res.data.records\n        })\n      }\n      ,\n\n      //关联项目\n      handleClickGlxm(row) {\n        //清空原来子项目数据\n        this.zxmGlmbDataList = []\n        //打开关联项目弹出框\n        this.isGlxmDialogShow = true\n        //给参数赋值\n        this.glxmQueryParams.symbid = row.objId\n        //查询项目库数据时参数\n        this.xmLibraryQueryForm.symbid = row.objId\n        //给试验项目库添加时使用\n        this.xmSelectedForm.symbid = row.objId\n        //获取模板关联项目数据\n        this.getSymbGlsyxmDataListByPage()\n      }\n      ,\n      //获取关联项目弹出框数据\n      getSymbGlsyxmDataListByPage() {\n        getSymbGlsyxmDataListByPage(this.glxmQueryParams).then(res => {\n          this.mbGlxmDataList = res.data.records\n          this.glxmTotal = res.data.total\n        })\n      }\n      ,\n      //试验项目复选框点击时间点击操作\n      handleGlxmSelectedChange(rows) {\n        this.selectedRowDataChange = rows\n      }\n      ,\n      //删除模板关联项目\n      deleteMbGlXm() {\n        if (this.selectedRowDataChange.length < 1) {\n          this.$message.warning('请选择正确的数据！！！')\n          return\n        }\n        let ids = this.selectedRowDataChange.map(item => {\n          return item.objId\n        })\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove(ids).then(({code}) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getSymbGlsyxmDataListByPage()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n              this.getSymbGlsyxmDataListByPage()\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n      //试验项目点击行数据时的单机操作\n      handleMbGlxmRowClick(row) {\n        this.glzxmQueryParams.syxmid = row.syxmid\n        this.getZxmDataList()\n      },\n      //懒加载函数\n      loadNode(node, resolve) {\n        let TreeparamMap = {\n          pid: '',\n          spbLogo: ['输电设备', '变电设备','配电设备']\n        }\n        if (node.level === 0) {\n          TreeparamMap.pid = 'sb'\n          return this.getTreeNode(TreeparamMap, resolve)\n        }\n        setTimeout(() => {\n          TreeparamMap.pid = node.data.code\n          this.getTreeNode(TreeparamMap, resolve)\n        }, 500)\n\n      },\n      //获取树节点数据\n      getTreeNode(paramMap, resolve) {\n        getDeviceClassTreeNodeByPid(paramMap).then(res => {\n          let treeNodes = []\n          res.data.forEach(item => {\n            let node = {\n              name: item.name,\n              level: item.level,\n              id: item.id,\n              pid: item.pid,\n              leaf: false,\n              code: item.code\n            }\n            treeNodes.push(node)\n          })\n          resolve(treeNodes)\n        })\n      },\n      //添加后确认保存按钮\n      save() {\n        this.$refs['form'].validate((valid) => {\n          if (valid) {\n            saveOrUpdate(this.form).then(res => {\n              if (res.code === '0000') {\n                this.$message.success(res.msg)\n                this.tableAndPageInfo.pager.pageResize = 'Y'\n                this.getData()\n                this.isShowDetails = false\n              } else {\n                this.$message.error(res.msg)\n              }\n            })\n\n          }\n        })\n      },\n      //树节点点击事件\n      handleNodeClick(data) {\n        console.log('树节点点击')\n        console.log(data)\n        if (data.level != '0' && data.level != '1') {\n          //新增按钮可点击\n          this.addDisabled = false\n          this.treeForm = data\n          this.querySyBwParam.sblxid = data.code\n          this.getData()\n        } else {\n          this.addDisabled = true\n        }\n      },\n      //添加按钮\n      addSensorButton() {\n        this.form = {}\n        this.form.sblx = this.treeForm.name\n        this.form.sblxid = this.treeForm.code\n        this.isShowDetails = true\n        this.title = '新增'\n      },\n      //列表查询\n      async getData(params) {\n        try {\n          const param = {...this.querySyBwParam, ...params}\n          param.sblxid=this.symbData.sblxid;\n          const {data, code} = await getPageDataListTosymb(param)\n          if (code === '0000') {\n            this.tableAndPageInfo.tableData = data.records\n            this.tableAndPageInfo.pager.total = data.total\n          }\n        } catch (e) {\n          console.log(e)\n        }\n      }\n      ,\n      /**\n       * 表格多选框\n       */\n      handleSelectionChange(rows) {\n        this.selectRows = rows;\n      },\n\n      close() {\n        this.isShowDetails = false\n      }\n      ,\n      //修改模板主表内容\n      updateDetails(row) {\n        this.title = '修改'\n        this.isShowDetails = true\n        this.form = row\n        this.isDisabled = false\n        this.isShow = true\n      }\n      ,\n\n      createTemplate(row) {\n        console.log(row)\n      }\n      ,\n      //查看模板主表详情按钮\n      getDetails(row) {\n        this.title = '详情'\n        this.isShowDetails = true\n        this.form = row\n        this.isDisabled = true\n        this.isShow = false\n      }\n      ,\n\n      //删除按钮\n      deleteSensorButton() {\n        if (this.selectRows.length < 1) {\n          this.$message.warning('请选择正确的数据！！！')\n          return\n        }\n        let ids = this.selectRows.map(item => {\n          return item.objId\n        })\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove(ids).then(({code}) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.tableAndPageInfo.pager.pageResize = 'Y'\n              this.getData()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n              this.tableAndPageInfo.pager.pageResize = 'Y'\n              this.getData()\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n\n      }\n      ,\n      //导出按钮\n      handleExport() {\n\n      }\n      ,\n\n      //关联铭牌点击事件\n      handleClickGlMp(row) {\n        this.showMpDialog = true\n        this.rowData = row\n      }\n      ,\n      //关闭试验铭牌弹窗\n      closeMpDialog() {\n        this.showMpDialog = false\n      }\n      ,\n\n      filterReset() {\n      },\n\n      //关闭试验模板弹窗\n      closeSymbComment() {\n        this.$emit(\"closeSymbSelectDialog\", false)\n      },\n      //点击确认后给父组件传递数据\n      // this.selectRowData != undefined && JSON.stringify(this.selectRowData) != \"{}\"\n      commitMbdata() {\n        if (this.selectRows.length == 1 && this.selectRows != undefined) {\n         this.$emit(\"handleAcceptMbData\",this.selectRows[0]);\n          this.$emit(\"closeSymbSelectDialog\", false)\n        } else {\n          this.$message.warning(\"请选择一条数据\")\n        }\n      },\n\n    }\n  }\n</script>\n\n<style lang='scss' scoped>118\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n/*导出pdf格式设置开始*/\n#printContentId {\n  background-color: #fff;\n  width: 100%;\n  /* height: 400px; */\n  margin: auto;\n  padding: 16px;\n  box-sizing: border-box;\n  //试验数据样式\n  .printTitle {\n    text-align: left;\n    line-height: 40px;\n    border-left: 1px solid #000;\n    border-right: 1px solid #000;\n    //border-bottom: 1px solid #000;\n    padding-left: 10px;\n  }\n}\n\n//修改table表头颜色\n/deep/ #printContentId .el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th {\n  background: #fff;\n  border-color: #000;\n  font-weight: inherit;\n}\n\n/deep/ #printContentId .el-table--enable-row-transition .el-table__body td {\n  border-color: #000;\n}\n\n.table_style td, th {\n  padding: 10px;\n  font-size: 15px;\n}\n\n.table_style {\n  border-collapse: collapse;\n  width: 100%;\n  text-align: center;\n  /* border-bottom: 0;\n   border-left: 0;\n   border-right: 0;*/\n}\n\n/* 设置滚动条的样式 */\n::-webkit-scrollbar {\n  width: 12px;\n}\n\n/* 滚动槽 */\n::-webkit-scrollbar-track {\n  //-webkit-box-shadow:inset006pxrgba(0,0,0,0.3);\n  border-radius: 10px;\n}\n\n/* 滚动条滑块 */\n::-webkit-scrollbar-thumb {\n  border-radius: 10px;\n  background: rgba(0, 0, 0, 0.1);\n  //-webkit-box-shadow:gba(0,0,0,0.5);\n}\n\n::-webkit-scrollbar-thumb:window-inactive {\n  background: rgba(0, 0, 0, 0.1);\n}\n\n[data-v-67a974b1]::-webkit-scrollbar {\n  width: 8px;\n}\n\n.item {\n  width: 225px;\n  float: left;\n}\n\n/*导出pdf格式设置结束*/\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  max-height: 79vh;\n  overflow: auto;\n}\n\n.app-container {\n  padding: 3px;\n}\n</style>\n"]}]}