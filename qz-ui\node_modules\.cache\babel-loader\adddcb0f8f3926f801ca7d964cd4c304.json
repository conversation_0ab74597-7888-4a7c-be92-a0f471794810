{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\activiti\\activitimodel.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\activiti\\activitimodel.js", "mtime": 1706897313850}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMubGlzdCA9IGxpc3Q7CmV4cG9ydHMuc2F2ZSA9IHNhdmU7CmV4cG9ydHMuZGVsID0gZGVsOwpleHBvcnRzLmV4cG9ydE1vZGVsID0gZXhwb3J0TW9kZWw7CmV4cG9ydHMuZGVwbG95ID0gZGVwbG95OwoKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Cgp2YXIgYmFzZVVybCA9ICIvYWN0aXZpdGktYXBpIjsKLyoqCiAqIOafpeivouaooeadv+WIl+ihqAogKiBAcGFyYW0gcXVlcnkg6K+35rGC5Y+C5pWwCiAqIEByZXR1cm5zIHtQcm9taXNlPGFueT59CiAqLwoKZnVuY3Rpb24gbGlzdChxdWVyeSkgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3RQb3N0KGJhc2VVcmwgKyAiL3dvcmtmbG93L21vZGVsL2xpc3QiLCBxdWVyeSwgMyk7Cn0KLyoqCiAqIOS/neWtmOaooeWeiwogKiBAcGFyYW0gZGF0YQogKiBAcmV0dXJucyB7UHJvbWlzZTxhbnk+fQogKi8KCgpmdW5jdGlvbiBzYXZlKGRhdGEpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgIi93b3JrZmxvdy9tb2RlbC9jcmVhdGUiLCBkYXRhLCAzKTsKfQovKioKICog5Yig6Zmk5qih5Z6LCiAqIEBwYXJhbSBpZAogKiBAcmV0dXJucyB7UHJvbWlzZSB8IFByb21pc2U8YW55Pn0KICovCgoKZnVuY3Rpb24gZGVsKGlkKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdEdldChiYXNlVXJsICsgIi93b3JrZmxvdy9tb2RlbC9kZWxldGUvIiArIGlkLCBudWxsLCAzKTsKfQovKioKICog5a+85Ye65qih5Z6LCiAqIEBwYXJhbSBpZAogKi8KCgpmdW5jdGlvbiBleHBvcnRNb2RlbChpZCkgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3REb3dubG9hZChiYXNlVXJsLCAnL3dvcmtmbG93L21vZGVsL2V4cG9ydC8nICsgaWQsIDMpOwp9Ci8qKgogKiDpg6jnvbIKICogQHBhcmFtIG1vZGVsSWQKICogQHJldHVybnMge1Byb21pc2U8YW55Pn0KICovCgoKZnVuY3Rpb24gZGVwbG95KG1vZGVsSWQpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0R2V0KGJhc2VVcmwgKyAiL3dvcmtmbG93L21vZGVsL2RlcGxveS8iICsgbW9kZWxJZCwgbnVsbCwgMyk7Cn0="}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/activiti/activitimodel.js"], "names": ["baseUrl", "list", "query", "api", "requestPost", "save", "data", "del", "id", "requestGet", "exportModel", "requestDownload", "deploy", "modelId"], "mappings": ";;;;;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,eAAhB;AACA;;;;;;AAKO,SAASC,IAAT,CAAcC,KAAd,EAAqB;AAC1B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,sBAAxB,EAA+CE,KAA/C,EAAqD,CAArD,CAAP;AACD;AAED;;;;;;;AAKO,SAASG,IAAT,CAAcC,IAAd,EAAoB;AACzB,SAAOH,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,wBAAxB,EAAiDM,IAAjD,EAAsD,CAAtD,CAAP;AACD;AAED;;;;;;;AAKO,SAASC,GAAT,CAAaC,EAAb,EAAiB;AACtB,SAAOL,iBAAIM,UAAJ,CAAeT,OAAO,GAAC,yBAAR,GAAkCQ,EAAjD,EAAoD,IAApD,EAAyD,CAAzD,CAAP;AACD;AAED;;;;;;AAIO,SAASE,WAAT,CAAqBF,EAArB,EAAyB;AAC9B,SAAOL,iBAAIQ,eAAJ,CAAoBX,OAApB,EAA4B,4BAA0BQ,EAAtD,EAAyD,CAAzD,CAAP;AACD;AAED;;;;;;;AAKO,SAASI,MAAT,CAAgBC,OAAhB,EAAyB;AAC9B,SAAOV,iBAAIM,UAAJ,CAAeT,OAAO,GAAC,yBAAR,GAAkCa,OAAjD,EAAyD,IAAzD,EAA8D,CAA9D,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\nconst baseUrl = \"/activiti-api\";\n/**\n * 查询模板列表\n * @param query 请求参数\n * @returns {Promise<any>}\n */\nexport function list(query) {\n  return api.requestPost(baseUrl+\"/workflow/model/list\",query,3)\n}\n\n/**\n * 保存模型\n * @param data\n * @returns {Promise<any>}\n */\nexport function save(data) {\n  return api.requestPost(baseUrl+\"/workflow/model/create\",data,3)\n}\n\n/**\n * 删除模型\n * @param id\n * @returns {Promise | Promise<any>}\n */\nexport function del(id) {\n  return api.requestGet(baseUrl+\"/workflow/model/delete/\"+id,null,3)\n}\n\n/**\n * 导出模型\n * @param id\n */\nexport function exportModel(id) {\n  return api.requestDownload(baseUrl,'/workflow/model/export/'+id,3)\n}\n\n/**\n * 部署\n * @param modelId\n * @returns {Promise<any>}\n */\nexport function deploy(modelId) {\n  return api.requestGet(baseUrl+\"/workflow/model/deploy/\"+modelId,null,3)\n}\n"]}]}