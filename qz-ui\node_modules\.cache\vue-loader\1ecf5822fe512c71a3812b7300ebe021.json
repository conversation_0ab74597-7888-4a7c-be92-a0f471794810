{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\pdgl\\pdsgl.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\pdgl\\pdsgl.vue", "mtime": 1706897324785}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgZ2V0UGRzTGlzdCwgYWRkUGRzLCByZW1vdmVQZHMsIGdldFBkc1RyZWUsIGdldE9yZ2FuaXphdGlvblNlbGVjdGVkCn0gZnJvbSAnQC9hcGkvZGFnYW5nT2lsZmllbGQvYXNzZXQvcGRzZ2wnCiAgZXhwb3J0IGRlZmF1bHQgewogICAgbmFtZTogInBkc2dsIiwKICAgIGRhdGEoKSB7CiAgICAgIHJldHVybiB7CiAgICAgICAgeXdiekxpc3Q6W10sCiAgICAgICAgc2J6dDogW3sKICAgICAgICAgIHZhbHVlOiAn5Zyo6L+QJywKICAgICAgICAgIGxhYmVsOiAn5Zyo6L+QJwogICAgICAgIH0sIHsKICAgICAgICAgIHZhbHVlOiAn5YGc5q2i5L2/55SoJywKICAgICAgICAgIGxhYmVsOiAn5YGc5q2i5L2/55SoJwogICAgICAgIH0KICAgICAgICAsIHsKICAgICAgICAgIHZhbHVlOiAn5pyq5bCx57uqJywKICAgICAgICAgIGxhYmVsOiAn5pyq5bCx57uqJwogICAgICAgIH0sIHsKICAgICAgICAgIHZhbHVlOiAn5oql5bqfJywKICAgICAgICAgIGxhYmVsOiAn5oql5bqfJwogICAgICAgIH1dLAogICAgICAgIHBkc2x4OiBbewogICAgICAgICAgdmFsdWU6ICfnrrHlvI/lj5jnlLXnq5knLAogICAgICAgICAgbGFiZWw6ICfnrrHlvI/lj5jnlLXnq5knCiAgICAgICAgfSwgewogICAgICAgICAgdmFsdWU6ICfmn7HkuIrlj5jlj7Dlj5gnLAogICAgICAgICAgbGFiZWw6ICfmn7HkuIrlj5jlj7Dlj5gnCiAgICAgICAgfQogICAgICAgICwgewogICAgICAgICAgdmFsdWU6ICfphY3nlLXlrqQnLAogICAgICAgICAgbGFiZWw6ICfphY3nlLXlrqQnCiAgICAgICAgfV0sCiAgICAgICAgLy/moJHnu5PmnoTkuIrpnaLlvpfnrZvpgInmoYblj4LmlbAKICAgICAgICB0cmVlRm9ybToge30sCiAgICAgICAgaXNEaXNhYmxlZDogZmFsc2UsCiAgICAgICAgdGFibGVBbmRQYWdlSW5mbzoge30sCiAgICAgICAgZmlsdGVySW5mbzogewogICAgICAgICAgZGF0YTogewogICAgICAgICAgICBzc2dzOiBbXSwKICAgICAgICAgICAgc3N4bG1jOiAnJywKICAgICAgICAgICAgeXhiaDogJycsCiAgICAgICAgICAgIHp0OiBbXQogICAgICAgICAgfSwKICAgICAgICAgIGZpZWxkTGlzdDogWwogICAgICAgICAgICAvLyB7bGFiZWw6ICfmiYDlsZ7lhazlj7gnLCB0eXBlOiAnc2VsZWN0JywgdmFsdWU6ICdzc2dzJywgbXVsdGlwbGU6IHRydWUsIG9wdGlvbnM6IFtdfSwKICAgICAgICAgICAge2xhYmVsOiAn5omA5bGe57q/6Lev5ZCN56ewJywgdHlwZTogJ2lucHV0JywgdmFsdWU6ICdzc3hsbWMnfSwKICAgICAgICAgICAge2xhYmVsOiAn6L+Q6KGM57yW5Y+3JywgdHlwZTogJ2lucHV0JywgdmFsdWU6ICd5eGJoJ30sCiAgICAgICAgICAgIHtsYWJlbDogJ+eKtuaAgScsIHR5cGU6ICdzZWxlY3QnLCB2YWx1ZTogJ3p0TGlzdCcsIG11bHRpcGxlOiB0cnVlLCBvcHRpb25zOiBbewogICAgICAgICAgICAgICAgdmFsdWU6ICflnKjov5AnLAogICAgICAgICAgICAgICAgbGFiZWw6ICflnKjov5AnCiAgICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgICAgdmFsdWU6ICflgZzmraLkvb/nlKgnLAogICAgICAgICAgICAgICAgbGFiZWw6ICflgZzmraLkvb/nlKgnCiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgLCB7CiAgICAgICAgICAgICAgICAgIHZhbHVlOiAn5pyq5bCx57uqJywKICAgICAgICAgICAgICAgICAgbGFiZWw6ICfmnKrlsLHnu6onCiAgICAgICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgICAgIHZhbHVlOiAn5oql5bqfJywKICAgICAgICAgICAgICAgICAgbGFiZWw6ICfmiqXlup8nCiAgICAgICAgICAgICAgICB9XX0sCiAgICAgICAgICBdCiAgICAgICAgfSwKICAgICAgICB0YWJsZUFuZFBhZ2VJbmZvMTogewogICAgICAgICAgcGFnZXI6IHsKICAgICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMDBdCiAgICAgICAgICB9LAogICAgICAgICAgb3B0aW9uOiB7CiAgICAgICAgICAgIGNoZWNrQm94OiB0cnVlLAogICAgICAgICAgICBzZXJpYWxOdW1iZXI6IHRydWUKICAgICAgICAgIH0sCiAgICAgICAgICB0YWJsZURhdGE6IFtdLAogICAgICAgICAgdGFibGVIZWFkZXI6IFsKICAgICAgICAgICAgLy8ge3Byb3A6ICdzc2dzJywgbGFiZWw6ICfmiYDlsZ7lhazlj7gnLCBtaW5XaWR0aDogJzEyMCd9LAogICAgICAgICAgICB7cHJvcDogJ3NzeGxtYycsIGxhYmVsOiAn5omA5bGe57q/6Lev5ZCN56ewJywgbWluV2lkdGg6ICcxODAnfSwKICAgICAgICAgICAge3Byb3A6ICdzc3hsYmgnLCBsYWJlbDogJ+aJgOWxnue6v+i3r+e8luWPtycsIG1pbldpZHRoOiAnMTIwJ30sCiAgICAgICAgICAgIHtwcm9wOiAnc3N4ZG1jJywgbGFiZWw6ICfmiYDlsZ7nur/mrrXlkI3np7AnLCBtaW5XaWR0aDogJzE4MCd9LAogICAgICAgICAgICB7cHJvcDogJ3Bkc21jJywgbGFiZWw6ICfphY3nlLXlrqTlkI3np7AnLCBtaW5XaWR0aDogJzE0MCd9LAogICAgICAgICAgICB7cHJvcDogJ3l4YmgnLCBsYWJlbDogJ+i/kOihjOe8luWPtycsIG1pbldpZHRoOiAnMTIwJ30sCiAgICAgICAgICAgIHtwcm9wOiAneXdiem1jJywgbGFiZWw6ICfov5Dnu7Tnj63nu4QnLCBtaW5XaWR0aDogJzEyMCd9LAogICAgICAgICAgICAvLyB7cHJvcDogJ2VycEJtJywgbGFiZWw6ICdFUlDnvJbnoIEnLCBtaW5XaWR0aDogJzEyMCd9LAogICAgICAgICAgICB7cHJvcDogJ3R5cnEnLCBsYWJlbDogJ+aKlei/kOaXpeacnycsIG1pbldpZHRoOiAnMTIwJ30sCiAgICAgICAgICAgIHtwcm9wOiAnc2ZqeWh3JywgbGFiZWw6ICfmmK/lkKblhbfmnInnjq/nvZEnLCBtaW5XaWR0aDogJzEyMCd9LAogICAgICAgICAgICB7cHJvcDogJ3p0JywgbGFiZWw6ICfnirbmgIEnLCBtaW5XaWR0aDogJzEyMCd9LAogICAgICAgICAgICB7cHJvcDogJ3NjY2onLCBsYWJlbDogJ+eUn+S6p+WOguWuticsIG1pbldpZHRoOiAnMTIwJ30sCiAgICAgICAgICAgIHtwcm9wOiAndGZmcycsIGxhYmVsOiAn6YCa6aOO5pa55byPJywgbWluV2lkdGg6ICcxMjAnfSwKICAgICAgICAgICAge3Byb3A6ICdseCcsIGxhYmVsOiAn6YWN55S15a6k57G75Z6LJywgbWluV2lkdGg6ICcxMjAnfSwKICAgICAgICAgICAgLyp7CiAgICAgICAgICAgICAgZml4ZWQ6ICJyaWdodCIsCiAgICAgICAgICAgICAgcHJvcDogJ29wZXJhdGlvbicsCiAgICAgICAgICAgICAgbGFiZWw6ICfmk43kvZwnLAogICAgICAgICAgICAgIG1pbldpZHRoOiAnMTMwcHgnLAogICAgICAgICAgICAgIHN0eWxlOiB7ZGlzcGxheTogJ2Jsb2NrJ30sCiAgICAgICAgICAgICAgb3BlcmF0aW9uOiBbCiAgICAgICAgICAgICAgICB7bmFtZTogJ+S/ruaUuScsIGNsaWNrRnVuOiB0aGlzLmdldFVwZGF0ZX0sCiAgICAgICAgICAgICAgICB7bmFtZTogJ+ivpuaDhScsIGNsaWNrRnVuOiB0aGlzLmdldFhxfSwKICAgICAgICAgICAgICBdCiAgICAgICAgICAgIH0sKi8KICAgICAgICAgIF0KICAgICAgICB9LAogICAgICAgIC8v6K6+5aSH6K+m5oOF6aG15bqV6YOo56Gu6K6k5Y+W5raI5oyJ6ZKu5o6n5Yi2CiAgICAgICAgc2JDb21taXREaWFsb2dDb3Ryb2w6IHRydWUsCiAgICAgICAgLy/lvLnlh7rmoYZ0YWLpobUKICAgICAgICBhY3RpdmVUYWJOYW1lOiAic2JEZXNjIiwKICAgICAgICAvL+WPmOeUteermeWxleekugogICAgICAgIGJkelNob3dUYWJsZTogdHJ1ZSwKICAgICAgICAvL+mXtOmalOWxleekugogICAgICAgIGpnU2hvd1RhYmxlOiBmYWxzZSwKICAgICAgICAvL+iuvuWkh+WxleekugogICAgICAgIHNiU2hvd1RhYmxlOiBmYWxzZSwKICAgICAgICAvL+iuvuWkh+W8ueWHuuahhgogICAgICAgIGRpYWxvZ0Zvcm1WaXNpYmxlOiBmYWxzZSwKICAgICAgICAvL+WPmOeUteermea3u+WKoOaMiemSruW8ueWHuuahhgogICAgICAgIHBkc0RpYWxvZ0Zvcm1WaXNpYmxlOiBmYWxzZSwKICAgICAgICAvL+mXtOmalOa3u+WKoOaMiemSruW8ueWHuuahhgogICAgICAgIGpnRGlhbG9nRm9ybVZpc2libGU6IGZhbHNlLAogICAgICAgIC8v5by55Ye65qGG6KGo5Y2VCiAgICAgICAgZm9ybTogewogICAgICAgICAgLy9zc2dzOiB1bmRlZmluZWQsCiAgICAgICAgICBzc3hsbWM6IHVuZGVmaW5lZCwKICAgICAgICAgIHNzeGxiaDogdW5kZWZpbmVkLAogICAgICAgICAgc3N4ZG1jOiB1bmRlZmluZWQsCiAgICAgICAgICBwZHNtYzogdW5kZWZpbmVkLAogICAgICAgICAgeXhiaDogdW5kZWZpbmVkLAogICAgICAgICAgZXJwQm06IHVuZGVmaW5lZCwKICAgICAgICAgIGJncjogdW5kZWZpbmVkLAogICAgICAgICAgemNiZGZzOiB1bmRlZmluZWQsCiAgICAgICAgICB6Y3N4OiB1bmRlZmluZWQsCiAgICAgICAgICB6Y2JoOiB1bmRlZmluZWQsCiAgICAgICAgICB3YnNZczogdW5kZWZpbmVkLAogICAgICAgICAgemN4ejogdW5kZWZpbmVkLAogICAgICAgICAgdHlycTogdW5kZWZpbmVkLAogICAgICAgICAgc2ZqeWh3OiB1bmRlZmluZWQsCiAgICAgICAgICB6dDogdW5kZWZpbmVkLAogICAgICAgICAgZHF0ejogdW5kZWZpbmVkLAogICAgICAgICAgc2NjajogdW5kZWZpbmVkLAogICAgICAgICAgc2dkdzogdW5kZWZpbmVkLAogICAgICAgICAganp3Y2M6IHVuZGVmaW5lZCwKICAgICAgICAgIGp6d2N6OiB1bmRlZmluZWQsCiAgICAgICAgICB0ZmZzOiB1bmRlZmluZWQsCiAgICAgICAgICBkbHFnc2w6IHVuZGVmaW5lZCwKICAgICAgICAgIGR5aGdxZ3NsOiB1bmRlZmluZWQsCiAgICAgICAgICBkcmdzbDogdW5kZWZpbmVkLAogICAgICAgICAgZHlqeGZzOiB1bmRlZmluZWQsCiAgICAgICAgICBkeXB4Z3NsOiB1bmRlZmluZWQsCiAgICAgICAgICBmaGtnZ3NsOiB1bmRlZmluZWQsCiAgICAgICAgICBmaGtncmRxemhnc2w6IHVuZGVmaW5lZCwKICAgICAgICAgIGpsZ3NsOiB1bmRlZmluZWQsCiAgICAgICAgICBtbGdzbDogdW5kZWZpbmVkLAogICAgICAgICAgcGJzbDogdW5kZWZpbmVkLAogICAgICAgICAgcGJ6cmw6IHVuZGVmaW5lZCwKICAgICAgICAgIHd6OiB1bmRlZmluZWQsCiAgICAgICAgICB4ZGNnc2w6IHVuZGVmaW5lZCwKICAgICAgICAgIHpsZ3NsOiB1bmRlZmluZWQsCiAgICAgICAgICB6YnkgICAgOiB1bmRlZmluZWQsCiAgICAgICAgICBneWp4Z3NsOiB1bmRlZmluZWQsCiAgICAgICAgICBneWpsZ3NsOiB1bmRlZmluZWQsCiAgICAgICAgICBneWN4Z3NsOiB1bmRlZmluZWQsCiAgICAgICAgICBwdGdzbCAgOiB1bmRlZmluZWQsCiAgICAgICAgICBkeWp4Z3NsOiB1bmRlZmluZWQsCiAgICAgICAgICBkeWN4Z3NsOiB1bmRlZmluZWQsCiAgICAgICAgICBkeWJjZ3NsOiB1bmRlZmluZWQsCiAgICAgICAgICBkeWpsZ3NsOiB1bmRlZmluZWQsCiAgICAgICAgICBkeWxsZ3NsOiB1bmRlZmluZWQsCiAgICAgICAgICBkZ3NsICAgOiB1bmRlZmluZWQsCiAgICAgICAgICBkZ2dkICAgOiB1bmRlZmluZWQsCiAgICAgICAgICB4c2J4aCAgOiB1bmRlZmluZWQsCiAgICAgICAgICBkeXBkZ3NsOiB1bmRlZmluZWQsCiAgICAgICAgICBseCAgICAgOiB1bmRlZmluZWQKICAgICAgICB9LAoKICAgICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgICAvL+mFjeeUteWupOagkQogICAgICAgIHRyZWVPcHRpb25zOiBbXSwKICAgICAgICAvL+WIoOmZpOaYr+WQpuWPr+eUqAogICAgICAgIG11bHRpcGxlU2Vuc29yOiB0cnVlLAogICAgICAgIC8v5p+l6K+i5Y+C5pWwCiAgICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICByb2xlS2V5OiAnJywKICAgICAgICAgIHJvbGVOYW1lOiAnJywKICAgICAgICAgIHN0YXR1czogJycsCiAgICAgICAgfSwKICAgICAgICBzaG93U2VhcmNoOiB0cnVlLAoKICAgICAgICBydWxlczp7CiAgICAgICAgICBzc2dzOlt7cmVxdWlyZWQ6dHJ1ZSxtZXNzYWdlOifor7fpgInmi6nmiYDlsZ7lhazlj7gnLHRyaWdnZXI6J2NoYW5nZSd9XSwKICAgICAgICAgIHNzeGxtYzpbe3JlcXVpcmVkOnRydWUsbWVzc2FnZTon6K+36L6T5YWl5omA5bGe57q/6Lev5ZCN56ewJyx0cmlnZ2VyOidibHVyJ31dLAogICAgICAgICAgc3N4bGJoOlt7cmVxdWlyZWQ6dHJ1ZSxtZXNzYWdlOifor7fovpPlhaXmiYDlsZ7nur/ot6/nvJblj7cnLHRyaWdnZXI6J2JsdXInfV0sCiAgICAgICAgICBzc3hkbWM6W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+i+k+WFpeaJgOWxnue6v+auteWQjeensCcsdHJpZ2dlcjonYmx1cid9XSwKICAgICAgICAgIHBkc21jOlt7cmVxdWlyZWQ6dHJ1ZSxtZXNzYWdlOifor7fovpPlhaXphY3nlLXlrqTlkI3np7AnLHRyaWdnZXI6J2JsdXInfV0sCiAgICAgICAgICB5eGJoOlt7cmVxdWlyZWQ6dHJ1ZSxtZXNzYWdlOifor7fovpPlhaXov5DooYznvJblj7cnLHRyaWdnZXI6J2JsdXInfV0sCi8qICAgICAgICAgIGVycEJtOlt7cmVxdWlyZWQ6dHJ1ZSxtZXNzYWdlOifor7fovpPlhaVFUlDnvJbnoIEnLHRyaWdnZXI6J2JsdXInfV0sCiAgICAgICAgICBiZ3I6W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+i+k+WFpeS/neeuoeS6uicsdHJpZ2dlcjonYmx1cid9XSwKICAgICAgICAgIHpjYmRmczpbe3JlcXVpcmVkOnRydWUsbWVzc2FnZTon6K+36L6T5YWl6LWE5Lqn5Y+Y5Yqo5pa55byPJyx0cmlnZ2VyOidibHVyJ31dLAogICAgICAgICAgemNzeDpbe3JlcXVpcmVkOnRydWUsbWVzc2FnZTon6K+36L6T5YWl6LWE5Lqn5bGe5oCnJyx0cmlnZ2VyOidibHVyJ31dLAogICAgICAgICAgemNiaDpbe3JlcXVpcmVkOnRydWUsbWVzc2FnZTon6K+36L6T5YWl6LWE5Lqn57yW5Y+3Jyx0cmlnZ2VyOidibHVyJ31dLAogICAgICAgICAgd2JzWXM6W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+i+k+WFpVdCU+WFg+e0oCcsdHJpZ2dlcjonYmx1cid9XSwKICAgICAgICAgIHpjeHo6W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+i+k+WFpei1hOS6p+aAp+i0qCcsdHJpZ2dlcjonYmx1cid9XSwqLwogICAgICAgICAgdHlycTpbe3JlcXVpcmVkOnRydWUsbWVzc2FnZTon6K+36YCJ5oup5oqV6L+Q5pel5pyfJyx0cmlnZ2VyOidjaGFuZ2UnfV0sCiAgICAgICAgICBzZmp5aHc6W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+mAieaLqeaYr+WQpuWFt+acieeOr+e9kScsdHJpZ2dlcjonYmx1cid9XSwKICAgICAgICAgIHp0Olt7cmVxdWlyZWQ6dHJ1ZSxtZXNzYWdlOifor7fpgInmi6nnirbmgIEnLHRyaWdnZXI6J2NoYW5nZSd9XSwKICAgICAgICAgIGRxdHo6W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+i+k+WFpeWcsOWMuueJueW+gScsdHJpZ2dlcjonYmx1cid9XSwKICAgICAgICAgIHNjY2o6W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+i+k+WFpeeUn+S6p+WOguWuticsdHJpZ2dlcjonYmx1cid9XSwKICAgICAgICAgIHNnZHc6W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+i+k+WFpeaWveW3peWNleS9jScsdHJpZ2dlcjonYmx1cid9XSwKICAgICAgICAgIGp6d2NjOlt7cmVxdWlyZWQ6dHJ1ZSxtZXNzYWdlOifor7fovpPlhaXlu7rnrZHnianlsLrlr7gnLHRyaWdnZXI6J2JsdXInfV0sCiAgICAgICAgICBqendjejpbe3JlcXVpcmVkOnRydWUsbWVzc2FnZTon6K+36L6T5YWl5bu6562R54mp5p2Q6LSoJyx0cmlnZ2VyOidibHVyJ31dLAogICAgICAgICAgdGZmczpbe3JlcXVpcmVkOnRydWUsbWVzc2FnZTon6K+36L6T5YWl6YCa6aOO5pa55byPJyx0cmlnZ2VyOidibHVyJ31dLAogICAgICAgICAgLy9wYnpybDpbe3JlcXVpcmVkOnRydWUsbWVzc2FnZTon6K+36L6T5YWl6YWN5Y+Y5oC75a656YePJyx0cmlnZ2VyOidibHVyJ31dLAogICAgICAgICAgbHg6W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+mAieaLqemFjeeUteWupOexu+WeiycsdHJpZ2dlcjonYmx1cid9XQogICAgICAgICAgLy8gd3o6W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+i+k+WFpeS9jee9ricsdHJpZ2dlcjonYmx1cid9XQogICAgICAgIH0KCiAgICAgIH07CiAgICB9LAogICAgd2F0Y2g6IHt9LAogICAgY3JlYXRlZCgpIHsKICAgICAgLy/ojrflj5bmlrDnmoTorr7lpIfmi5PmiZHmoJEKICAgICAgdGhpcy5nZXRQZHNUcmVlSW5mbygpOwogICAgICAvL+WIneWni+WMluWKoOi9veaXtuWKoOi9veaJgOacieWPmOeUteermeS/oeaBrwogICAgICB0aGlzLm5ld1Rlc3REYXRhID0gdGhpcy5iZHpMaXN0CiAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mbyA9IHsuLi50aGlzLnRhYmxlQW5kUGFnZUluZm8xfQogICAgICB0aGlzLmdldERhdGEoKQogICAgICB0aGlzLmdldFl3YnpMaXMoKQogICAgfSwKICAgIG1ldGhvZHM6IHsKICAgICAgZ2V0WXdiekxpcygpewogICAgICAgIGdldE9yZ2FuaXphdGlvblNlbGVjdGVkKHtwYXJlbnRJZDoiMzAxMyJ9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgICB0aGlzLnl3YnpMaXN0ID0gcmVzLmRhdGE7CiAgICAgICAgfSkKICAgICAgfSwKICAgICAgLy/ojrflj5bmlrDnmoTorr7lpIfmi5PmiZHmoJEKICAgICAgZ2V0UGRzVHJlZUluZm8oKSB7CiAgICAgICAgZ2V0UGRzVHJlZSh0aGlzLnRyZWVGb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICB0aGlzLnRyZWVPcHRpb25zID0gcmVzLmRhdGE7CiAgICAgICAgfSkKICAgICAgfSwKICAgICAgLyoqCiAgICAgICAqIOihqOagvOWkmumAieahhgogICAgICAgKi8KICAgICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0ub2JqSWQpCiAgICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOwogICAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsKICAgICAgfSwKCiAgICAgIC8v5YiX6KGo5p+l6K+iCiAgICAgIGFzeW5jIGdldERhdGEocGFyYW1zKSB7CiAgICAgICAgY29uc3QgcGFyYW0gPSB7Li4udGhpcy5wYXJhbXMsIC4uLnBhcmFtc30KICAgICAgICBhd2FpdCBnZXRQZHNMaXN0KHBhcmFtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8xLnRhYmxlRGF0YSA9IHJlcy5kYXRhLnJlY29yZHM7CiAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8xLnRhYmxlRGF0YS5mb3JFYWNoKChpdGVtKSA9PiB7CiAgICAgICAgICAgIHRoaXMueXdiekxpc3QuZm9yRWFjaCgoZWxlbWVudCkgPT4gewogICAgICAgICAgICAgIGlmIChpdGVtLnl3YnogPT0gZWxlbWVudC52YWx1ZSkgewogICAgICAgICAgICAgICAgaXRlbS55d2J6bWMgPSBlbGVtZW50LmxhYmVsOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSk7CiAgICAgICAgICB9KTsKICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mbzEucGFnZXIudG90YWwgPSByZXMuZGF0YS50b3RhbDsKICAgICAgICAgIC8v57uZ6aG16Z2i6LWL5YC8CiAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8gPSB7Li4udGhpcy50YWJsZUFuZFBhZ2VJbmZvMX0KICAgICAgICB9KTsKICAgICAgfSwKCiAgICAgIC8v6YWN55S15a6k5re75Yqg5oyJ6ZKuCiAgICAgIHBkc0FkZFNlbnNvckJ1dHRvbigpIHsKICAgICAgICB0aGlzLmZvcm09e30KICAgICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZTsKICAgICAgICB0aGlzLnBkc0RpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZQogICAgICB9LAogICAgICAvL+WIoOmZpOmFjeeUteWupAogICAgICBhc3luYyBkZWxldGVQZHMoKXsKICAgICAgICBpZiAodGhpcy5pZHMubGVuZ3RoICE9IDApIHsKICAgICAgICAgIHRoaXMuJGNvbmZpcm0oJ+atpOaTjeS9nOWwhuawuOS5heWIoOmZpOivpeaVsOaNriwg5piv5ZCm57un57utPycsICfmj5DnpLonLCB7CiAgICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgICAgICAgcmVtb3ZlUGRzKHRoaXMuaWRzKS50aGVuKCh7Y29kZSB9KT0+ewogICAgICAgICAgICAgICAgaWYoY29kZT09PScwMDAwJyl7CiAgICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAn5Yig6Zmk5oiQ5YqfIScKICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpCiAgICAgICAgICAgICAgICB9ZWxzZXsKICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAn5Yig6Zmk5aSx6LSlIScKICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogJ2luZm8nLAogICAgICAgICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iOWIoOmZpCcKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfSk7CiAgICAgICAgfQoKICAgICAgfSwKCiAgICAgIC8v5q+P6aG15bGV56S65pWw6YeP54K55Ye75LqL5Lu2CiAgICAgIGhhbmRsZVNpemVDaGFuZ2UoKSB7CgogICAgICB9LAogICAgICAvL+mhteeggeaUueWPmOS6i+S7tgogICAgICBoYW5kbGVDdXJyZW50Q2hhbmdlKCkgewoKICAgICAgfSwKICAgICAgLy/moJHngrnlh7vkuovku7YKICAgICAgYXN5bmMgaGFuZGxlTm9kZUNsaWNrKGRhdGEpIHsKICAgICAgICBpZiAoZGF0YS5pZGVudGlmaWVyID09PSAnMScpIHsKICAgICAgICAgIGF3YWl0IHRoaXMuZ2V0RGF0YSh7b2JqSWQ6IGRhdGEuaWR9KQogICAgICAgICAgYXdhaXQgdGhpcy5nZXRYcSh0aGlzLnRhYmxlQW5kUGFnZUluZm8udGFibGVEYXRhWzBdKQogICAgICAgIH0KICAgICAgfSwKCiAgICAgIC8v5L+u5pS5CiAgICAgIGdldFVwZGF0ZShyb3cpewogICAgICAgIHRoaXMucGRzRGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgICAgIHRoaXMuZm9ybSA9IHsuLi5yb3d9OwogICAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICB9LAogICAgICAvL+ivpuaDhQogICAgICBnZXRYcShyb3cpewogICAgICAgIHRoaXMucGRzRGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgICAgIHRoaXMuZm9ybSA9IHsuLi5yb3d9OwogICAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IHRydWU7CiAgICAgIH0sCiAgICAgIC8v56Gu5a6a5oyJ6ZKuCiAgICAgIGFzeW5jIGdldERldGVybWluZSgpewogICAgICAgIHRoaXMuJHJlZnNbJ2Zvcm0nXS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICAgIGlmICh2YWxpZCl7CiAgICAgICAgICAgIGFkZFBkcyh0aGlzLmZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgICBpZiAocmVzLmNvZGUgPT0gIjAwMDAiKSB7CiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKn++8gSIpOwogICAgICAgICAgICAgICAgdGhpcy5wZHNEaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlOwogICAgICAgICAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KTsKICAgICAgICAgIH1lbHNlewogICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICAgICAgICB2YXIgaXNFcnJvciA9IGRvY3VtZW50LmdldEVsZW1lbnRzQnlDbGFzc05hbWUoImlzLWVycm9yIik7CiAgICAgICAgICAgICAgaWYgKGlzRXJyb3JbMF0ucXVlcnlTZWxlY3RvcignaW5wdXQnKSkgewogICAgICAgICAgICAgICAgaXNFcnJvclswXS5xdWVyeVNlbGVjdG9yKCdpbnB1dCcpLmZvY3VzKCk7CiAgICAgICAgICAgICAgfSBlbHNlIGlmIChpc0Vycm9yWzBdLnF1ZXJ5U2VsZWN0b3IoJ3RleHRhcmVhJykpIHsKICAgICAgICAgICAgICAgIGlzRXJyb3JbMF0ucXVlcnlTZWxlY3RvcigndGV4dGFyZWEnKS5mb2N1cygpOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSwgMSkKICAgICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9LAogICAgICBoYW5kbGVDbG9zZSgpewogICAgICAgIHRoaXMuZm9ybT17fTsKICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICB0aGlzLmZvcm0gPSB0aGlzLiRvcHRpb25zLmRhdGEoKS5mb3JtOwogICAgICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgICAgICB9KTsKICAgICAgfSwKICAgICAgZmlsdGVyUmVzZXQoKXsKCiAgICAgIH0sCiAgICB9CiAgfTsK"}, {"version": 3, "sources": ["pdsgl.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4YA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "pdsgl.vue", "sourceRoot": "src/views/dagangOilfield/dwzygl/pdgl", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card shadow=\"never\" style=\"background:#e0f8ed;\">\n          <div class=\"text head-container\">\n            <el-col style=\"padding: 0\">\n              <el-tree :expand-on-click-node=\"true\"\n                       highlight-current\n                       id=\"tree\"\n                       :data=\"treeOptions\"\n                       :default-expanded-keys=\"['1']\"\n                       @node-click=\"handleNodeClick\"\n                       node-key=\"nodeId\"\n                       accordion\n              />\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 120, itemWidth: 180 }\"\n          @handleReset=\"filterReset\"\n        />\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button icon=\"el-icon-plus\" @click=\"pdsAddSensorButton\" v-hasPermi=\"['pdztz:button:add']\" type=\"primary\">新增</el-button>\n            <el-button icon=\"el-icon-delete\" v-hasPermi=\"['pdztz:button:delete']\" type=\"danger\" @click=\"deletePds\">删除</el-button>\n          </div>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"500\"\n          >\n          <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\" width=\"160\"\n                           :resizable=\"false\">\n            <template slot-scope=\"scope\">\n              <el-button @click=\"getUpdate(scope.row)\" v-hasPermi=\"['pdztz:button:update']\" type=\"text\" size=\"small\">修改</el-button>\n              <el-button @click=\"getXq(scope.row)\" type=\"text\" size=\"small\">详情</el-button>\n            </template>\n          </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!--配电室新增、修改、详情弹框-->\n    <el-dialog title=\"配电室\" :visible.sync=\"pdsDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"70%\" @close=\"handleClose\">\n      <el-form ref=\"form\" :model=\"form\" :disabled=\"isDisabled\" :rules=\"rules\" label-width=\"130px\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"配电室类型：\" prop=\"lx\">\n              <el-select v-model=\"form.lx\" placeholder=\"请选择配电室类型\" clearable>\n                <el-option\n                    v-for=\"item in pdslx\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n<!--          <el-col :span=\"8\">\n            <el-form-item label=\"所属公司：\" prop=\"ssgs\">\n              <el-select v-model=\"form.ssgs\" placeholder=\"请选择所属公司\">\n                <el-option\n                  v-for=\"item in bdzOptionsDataList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>-->\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属线路名称：\" prop=\"ssxlmc\">\n              <el-input v-model=\"form.ssxlmc\" placeholder=\"请输入所属线路名称\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属线路编号：\" prop=\"ssxlbh\">\n              <el-input v-model=\"form.ssxlbh\" placeholder=\"请输入所属线路编号\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属线段名称：\" prop=\"ssxdmc\">\n              <el-input v-model=\"form.ssxdmc\" placeholder=\"请输入所属线段名称\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"配电室名称：\" prop=\"pdsmc\">\n              <el-input v-model=\"form.pdsmc\" placeholder=\"请输入配电室名称\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"运行编号：\" prop=\"yxbh\">\n              <el-input v-model=\"form.yxbh\" placeholder=\"请输入运行编号\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"运维班组：\" prop=\"yxbh\">\n              <el-select v-model=\"form.ywbz\" placeholder=\"请选择运维班组\" clearable>\n                <el-option\n                    v-for=\"item in ywbzList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"投运日期：\" prop=\"tyrq\">\n              <el-date-picker\n                type=\"date\"\n                placeholder=\"选择日期\"\n                value-format=\"yyyy-MM-dd\"\n                v-model=\"form.tyrq\"\n                style=\"width: 100%\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n<!--          <el-col :span=\"8\">\n            <el-form-item label=\"ERP编码：\" prop=\"erpBm\">\n              <el-input v-model=\"form.erpBm\" placeholder=\"请输入ERP编码\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"保管人：\" prop=\"bgr\">\n              <el-input v-model=\"form.bgr\" placeholder=\"请输入保管人\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"资产变动方式：\" prop=\"zcbdfs\">\n              <el-input v-model=\"form.zcbdfs\" placeholder=\"请输入资产变动方式\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"资产属性：\" prop=\"zcsx\">\n              <el-input v-model=\"form.zcsx\" placeholder=\"请输入资产属性\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"资产编号：\" prop=\"zcbh\">\n              <el-input v-model=\"form.zcbh\" placeholder=\"请输入资产编号\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"WBS元素：\" prop=\"wbsYs\">\n              <el-input v-model=\"form.wbsYs\" placeholder=\"请输入WBS元素\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"资产性质：\" prop=\"zcxz\">\n              <el-input v-model=\"form.zcxz\" placeholder=\"请输入资产性质\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"施工单位：\" prop=\"sgdw\">\n              <el-input v-model=\"form.sgdw\" placeholder=\"请输入施工单位\">\n              </el-input>\n            </el-form-item>\n          </el-col><el-col :span=\"8\">\n          <el-form-item label=\"建筑物尺寸：\" prop=\"jzwcc\">\n            <el-input v-model=\"form.jzwcc\" placeholder=\"请输入建筑物尺寸\">\n            </el-input>\n          </el-form-item>\n        </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"建筑物材质：\" prop=\"jzwcz\">\n              <el-input v-model=\"form.jzwcz\" placeholder=\"请输入建筑物材质\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否具有环网：\" prop=\"sfjyhw\">\n              <el-select v-model=\"form.sfjyhw\" placeholder=\"请选择是否具有环网\" :disabled=\"isDisabled\" clearable>\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"状态：\" prop=\"zt\">\n              <el-select v-model=\"form.zt\" placeholder=\"请选择状态\">\n                <el-option\n                  v-for=\"item in sbzt\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"生产厂家：\" prop=\"sccj\">\n              <el-input v-model=\"form.sccj\" placeholder=\"请输入生产厂家\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"通风方式：\" prop=\"tffs\">\n              <el-input v-model=\"form.tffs\" placeholder=\"请输入通风方式\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"站变用：\" prop=\"zby\">\n              <el-input-number v-model=\"form.zby\" :min=\"0\" placeholder=\"请输入站变用\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"高压进线柜数量：\" prop=\"gyjxgsl\">\n              <el-input-number v-model=\"form.gyjxgsl\" :min=\"0\" placeholder=\"请输入高压进线柜数量\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"高压计量柜数量：\" prop=\"gyjlgsl\">\n              <el-input-number v-model=\"form.gyjlgsl\" :min=\"0\" placeholder=\"请输入高压计量柜数量\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"高压出线柜数量：\" prop=\"gycxgsl\">\n              <el-input-number v-model=\"form.gycxgsl\" :min=\"0\" placeholder=\"请输入高压出线柜数量\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"PT柜数量：\" prop=\"ptgsl\">\n              <el-input-number v-model=\"form.ptgsl\" :min=\"0\" placeholder=\"请输入PT柜数量\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n\n<!--          <el-col :span=\"8\">\n            <el-form-item label=\"断路器柜数量\" prop=\"dlqgsl\">\n              <el-input-number v-model=\"form.dlqgsl\" :min=\"0\" placeholder=\"请输入断路器柜数量\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电压互感器柜数量：\" prop=\"dyhgqgsl\">\n              <el-input-number v-model=\"form.dyhgqgsl\" :min=\"0\" placeholder=\"请输入电压互感器柜数量\"></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电容柜数量：\" prop=\"drgsl\">\n              <el-input-number v-model=\"form.drgsl\" :min=\"0\" placeholder=\"请输入电容柜数量\"></el-input-number>\n            </el-form-item>\n          </el-col>-->\n          <el-col :span=\"8\">\n            <el-form-item label=\"母联柜数量：\" prop=\"mlgsl\">\n              <el-input-number v-model=\"form.mlgsl\" :min=\"0\" placeholder=\"请输入母联柜数量\"></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"低压进线柜数量：\" prop=\"dyjxgsl\">\n              <el-input-number v-model=\"form.dyjxgsl\" :min=\"0\" placeholder=\"低压进线柜数量\"></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"低压出线柜数量：\" prop=\"dycxgsl\">\n              <el-input-number v-model=\"form.dycxgsl\" :min=\"0\" placeholder=\"请输入低压出线柜数量\"></el-input-number>\n            </el-form-item>\n          </el-col>\n<!--          <el-col :span=\"8\">\n            <el-form-item label=\"低压接线方式：\" prop=\"dyjxfs\">\n              <el-input v-model=\"form.dyjxfs\" placeholder=\"请输入低压接线方式\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"低压配线柜数量：\" prop=\"dypxgsl\">\n              <el-input-number v-model=\"form.dypxgsl\" :min=\"0\" placeholder=\"请输入低压配线柜数量\"></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"负荷开关柜数量：\" prop=\"fhkggsl\">\n              <el-input-number v-model=\"form.fhkggsl\" :min=\"0\" placeholder=\"请输入负荷开关柜数量\"></el-input-number>\n            </el-form-item>\n          </el-col>-->\n          <el-col :span=\"8\">\n            <el-form-item label=\"低压补偿柜数量：\" prop=\"dybcgsl\">\n              <el-input-number v-model=\"form.dybcgsl\" :min=\"0\" placeholder=\"请输入低压补偿柜数量\"></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"低压计量柜数量：\" prop=\"dyjlgsl\">\n              <el-input-number v-model=\"form.dyjlgsl\" :min=\"0\" placeholder=\"请输入低压计量柜数量\"></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"低压联络柜数量：\" prop=\"dyllgsl\">\n              <el-input-number v-model=\"form.dyllgsl\" :min=\"0\" placeholder=\"请输入低压联络柜数量\"></el-input-number>\n            </el-form-item>\n          </el-col>\n<!--          <el-col :span=\"8\">\n            <el-form-item label=\"负荷开关熔断器组合柜数量：\" prop=\"fhkgrdqzhgsl\">\n              <el-input-number v-model=\"form.fhkgrdqzhgsl\" :min=\"0\" placeholder=\"请输入负荷开关熔断器组合柜数量\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"配变数量：\" prop=\"pbsl\">\n              <el-input-number v-model=\"form.pbsl\" :min=\"0\" placeholder=\"请输入配变数量\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"配变总容量：\" prop=\"pbzrl\">\n              <el-input-number v-model=\"form.pbzrl\" :min=\"0\" placeholder=\"请输入配变总容量(KVA)\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"蓄电池柜数量：\" prop=\"xdcgsl\">\n              <el-input-number v-model=\"form.xdcgsl\" :min=\"0\" placeholder=\"请输入蓄电池柜数量\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"直流柜数量：\" prop=\"zlgsl\">\n              <el-input-number v-model=\"form.zlgsl\" :min=\"0\" placeholder=\"请输入直流柜数量\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电杆数量：\" prop=\"dgsl\">\n              <el-input-number v-model=\"form.dgsl\" :min=\"0\" placeholder=\"请输入电杆数量\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电杆高度(m)：\" prop=\"dggd\">\n              <el-input-number v-model=\"form.dggd\" :min=\"0\" placeholder=\"请输入电杆高度\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n          <el-form-item label=\"箱式变型号：\" prop=\"xsbxh\">\n            <el-input v-model=\"form.xsbxh\" placeholder=\"请输入箱式变型号\"></el-input>\n          </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n          <el-form-item label=\"低压配电柜数量：\" prop=\"dypdgsl\">\n            <el-input-number v-model=\"form.dypdgsl\" :min=\"0\" placeholder=\"请输入低压配电柜数量\"></el-input-number>\n          </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"计量柜数量：\" prop=\"jlgsl\">\n              <el-input-number v-model=\"form.jlgsl\" :min=\"0\" placeholder=\"请输入计量柜数量\"></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"经度\" prop=\"jd\">\n              <el-input v-model=\"form.jd\" placeholder=\"请输入经度\"> </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"纬度\" prop=\"wd\">\n              <el-input v-model=\"form.wd\" placeholder=\"请输入纬度\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\" prop=\"bz\">\n              <el-input v-model=\"form.bz\" type=\"textarea\" rows=\"2\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\" v-show=\"!isDisabled\">\n        <el-button @click=\"pdsDialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"getDetermine\" >确 定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n\n</template>\n\n<script>\nimport {\n  getPdsList, addPds, removePds, getPdsTree, getOrganizationSelected\n} from '@/api/dagangOilfield/asset/pdsgl'\n  export default {\n    name: \"pdsgl\",\n    data() {\n      return {\n        ywbzList:[],\n        sbzt: [{\n          value: '在运',\n          label: '在运'\n        }, {\n          value: '停止使用',\n          label: '停止使用'\n        }\n        , {\n          value: '未就绪',\n          label: '未就绪'\n        }, {\n          value: '报废',\n          label: '报废'\n        }],\n        pdslx: [{\n          value: '箱式变电站',\n          label: '箱式变电站'\n        }, {\n          value: '柱上变台变',\n          label: '柱上变台变'\n        }\n        , {\n          value: '配电室',\n          label: '配电室'\n        }],\n        //树结构上面得筛选框参数\n        treeForm: {},\n        isDisabled: false,\n        tableAndPageInfo: {},\n        filterInfo: {\n          data: {\n            ssgs: [],\n            ssxlmc: '',\n            yxbh: '',\n            zt: []\n          },\n          fieldList: [\n            // {label: '所属公司', type: 'select', value: 'ssgs', multiple: true, options: []},\n            {label: '所属线路名称', type: 'input', value: 'ssxlmc'},\n            {label: '运行编号', type: 'input', value: 'yxbh'},\n            {label: '状态', type: 'select', value: 'ztList', multiple: true, options: [{\n                value: '在运',\n                label: '在运'\n              }, {\n                value: '停止使用',\n                label: '停止使用'\n              }\n                , {\n                  value: '未就绪',\n                  label: '未就绪'\n                }, {\n                  value: '报废',\n                  label: '报废'\n                }]},\n          ]\n        },\n        tableAndPageInfo1: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n            // {prop: 'ssgs', label: '所属公司', minWidth: '120'},\n            {prop: 'ssxlmc', label: '所属线路名称', minWidth: '180'},\n            {prop: 'ssxlbh', label: '所属线路编号', minWidth: '120'},\n            {prop: 'ssxdmc', label: '所属线段名称', minWidth: '180'},\n            {prop: 'pdsmc', label: '配电室名称', minWidth: '140'},\n            {prop: 'yxbh', label: '运行编号', minWidth: '120'},\n            {prop: 'ywbzmc', label: '运维班组', minWidth: '120'},\n            // {prop: 'erpBm', label: 'ERP编码', minWidth: '120'},\n            {prop: 'tyrq', label: '投运日期', minWidth: '120'},\n            {prop: 'sfjyhw', label: '是否具有环网', minWidth: '120'},\n            {prop: 'zt', label: '状态', minWidth: '120'},\n            {prop: 'sccj', label: '生产厂家', minWidth: '120'},\n            {prop: 'tffs', label: '通风方式', minWidth: '120'},\n            {prop: 'lx', label: '配电室类型', minWidth: '120'},\n            /*{\n              fixed: \"right\",\n              prop: 'operation',\n              label: '操作',\n              minWidth: '130px',\n              style: {display: 'block'},\n              operation: [\n                {name: '修改', clickFun: this.getUpdate},\n                {name: '详情', clickFun: this.getXq},\n              ]\n            },*/\n          ]\n        },\n        //设备详情页底部确认取消按钮控制\n        sbCommitDialogCotrol: true,\n        //弹出框tab页\n        activeTabName: \"sbDesc\",\n        //变电站展示\n        bdzShowTable: true,\n        //间隔展示\n        jgShowTable: false,\n        //设备展示\n        sbShowTable: false,\n        //设备弹出框\n        dialogFormVisible: false,\n        //变电站添加按钮弹出框\n        pdsDialogFormVisible: false,\n        //间隔添加按钮弹出框\n        jgDialogFormVisible: false,\n        //弹出框表单\n        form: {\n          //ssgs: undefined,\n          ssxlmc: undefined,\n          ssxlbh: undefined,\n          ssxdmc: undefined,\n          pdsmc: undefined,\n          yxbh: undefined,\n          erpBm: undefined,\n          bgr: undefined,\n          zcbdfs: undefined,\n          zcsx: undefined,\n          zcbh: undefined,\n          wbsYs: undefined,\n          zcxz: undefined,\n          tyrq: undefined,\n          sfjyhw: undefined,\n          zt: undefined,\n          dqtz: undefined,\n          sccj: undefined,\n          sgdw: undefined,\n          jzwcc: undefined,\n          jzwcz: undefined,\n          tffs: undefined,\n          dlqgsl: undefined,\n          dyhgqgsl: undefined,\n          drgsl: undefined,\n          dyjxfs: undefined,\n          dypxgsl: undefined,\n          fhkggsl: undefined,\n          fhkgrdqzhgsl: undefined,\n          jlgsl: undefined,\n          mlgsl: undefined,\n          pbsl: undefined,\n          pbzrl: undefined,\n          wz: undefined,\n          xdcgsl: undefined,\n          zlgsl: undefined,\n          zby    : undefined,\n          gyjxgsl: undefined,\n          gyjlgsl: undefined,\n          gycxgsl: undefined,\n          ptgsl  : undefined,\n          dyjxgsl: undefined,\n          dycxgsl: undefined,\n          dybcgsl: undefined,\n          dyjlgsl: undefined,\n          dyllgsl: undefined,\n          dgsl   : undefined,\n          dggd   : undefined,\n          xsbxh  : undefined,\n          dypdgsl: undefined,\n          lx     : undefined\n        },\n\n        loading: false,\n        //配电室树\n        treeOptions: [],\n        //删除是否可用\n        multipleSensor: true,\n        //查询参数\n        queryParams: {\n          pageNum: 1,\n          pageSize: 10,\n          roleKey: '',\n          roleName: '',\n          status: '',\n        },\n        showSearch: true,\n\n        rules:{\n          ssgs:[{required:true,message:'请选择所属公司',trigger:'change'}],\n          ssxlmc:[{required:true,message:'请输入所属线路名称',trigger:'blur'}],\n          ssxlbh:[{required:true,message:'请输入所属线路编号',trigger:'blur'}],\n          ssxdmc:[{required:true,message:'请输入所属线段名称',trigger:'blur'}],\n          pdsmc:[{required:true,message:'请输入配电室名称',trigger:'blur'}],\n          yxbh:[{required:true,message:'请输入运行编号',trigger:'blur'}],\n/*          erpBm:[{required:true,message:'请输入ERP编码',trigger:'blur'}],\n          bgr:[{required:true,message:'请输入保管人',trigger:'blur'}],\n          zcbdfs:[{required:true,message:'请输入资产变动方式',trigger:'blur'}],\n          zcsx:[{required:true,message:'请输入资产属性',trigger:'blur'}],\n          zcbh:[{required:true,message:'请输入资产编号',trigger:'blur'}],\n          wbsYs:[{required:true,message:'请输入WBS元素',trigger:'blur'}],\n          zcxz:[{required:true,message:'请输入资产性质',trigger:'blur'}],*/\n          tyrq:[{required:true,message:'请选择投运日期',trigger:'change'}],\n          sfjyhw:[{required:true,message:'请选择是否具有环网',trigger:'blur'}],\n          zt:[{required:true,message:'请选择状态',trigger:'change'}],\n          dqtz:[{required:true,message:'请输入地区特征',trigger:'blur'}],\n          sccj:[{required:true,message:'请输入生产厂家',trigger:'blur'}],\n          sgdw:[{required:true,message:'请输入施工单位',trigger:'blur'}],\n          jzwcc:[{required:true,message:'请输入建筑物尺寸',trigger:'blur'}],\n          jzwcz:[{required:true,message:'请输入建筑物材质',trigger:'blur'}],\n          tffs:[{required:true,message:'请输入通风方式',trigger:'blur'}],\n          //pbzrl:[{required:true,message:'请输入配变总容量',trigger:'blur'}],\n          lx:[{required:true,message:'请选择配电室类型',trigger:'blur'}]\n          // wz:[{required:true,message:'请输入位置',trigger:'blur'}]\n        }\n\n      };\n    },\n    watch: {},\n    created() {\n      //获取新的设备拓扑树\n      this.getPdsTreeInfo();\n      //初始化加载时加载所有变电站信息\n      this.newTestData = this.bdzList\n      this.tableAndPageInfo = {...this.tableAndPageInfo1}\n      this.getData()\n      this.getYwbzLis()\n    },\n    methods: {\n      getYwbzLis(){\n        getOrganizationSelected({parentId:\"3013\"}).then(res => {\n          this.ywbzList = res.data;\n        })\n      },\n      //获取新的设备拓扑树\n      getPdsTreeInfo() {\n        getPdsTree(this.treeForm).then(res => {\n          this.treeOptions = res.data;\n        })\n      },\n      /**\n       * 表格多选框\n       */\n      handleSelectionChange(selection) {\n        this.ids = selection.map(item => item.objId)\n        this.single = selection.length !== 1;\n        this.multiple = !selection.length;\n      },\n\n      //列表查询\n      async getData(params) {\n        const param = {...this.params, ...params}\n        await getPdsList(param).then(res => {\n          this.tableAndPageInfo1.tableData = res.data.records;\n          this.tableAndPageInfo1.tableData.forEach((item) => {\n            this.ywbzList.forEach((element) => {\n              if (item.ywbz == element.value) {\n                item.ywbzmc = element.label;\n              }\n            });\n          });\n          this.tableAndPageInfo1.pager.total = res.data.total;\n          //给页面赋值\n          this.tableAndPageInfo = {...this.tableAndPageInfo1}\n        });\n      },\n\n      //配电室添加按钮\n      pdsAddSensorButton() {\n        this.form={}\n        this.isDisabled = false;\n        this.pdsDialogFormVisible = true\n      },\n      //删除配电室\n      async deletePds(){\n        if (this.ids.length != 0) {\n          this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }).then(() => {\n              removePds(this.ids).then(({code })=>{\n                if(code==='0000'){\n                  this.$message({\n                    type: 'success',\n                    message: '删除成功!'\n                  });\n                  this.getData()\n                }else{\n                  this.$message({\n                    type: 'error',\n                    message: '删除失败!'\n                  });\n                }\n              })\n            }).catch(() => {\n              this.$message({\n                type: 'info',\n                message: '已取消删除'\n              });\n            });\n        }\n\n      },\n\n      //每页展示数量点击事件\n      handleSizeChange() {\n\n      },\n      //页码改变事件\n      handleCurrentChange() {\n\n      },\n      //树点击事件\n      async handleNodeClick(data) {\n        if (data.identifier === '1') {\n          await this.getData({objId: data.id})\n          await this.getXq(this.tableAndPageInfo.tableData[0])\n        }\n      },\n\n      //修改\n      getUpdate(row){\n        this.pdsDialogFormVisible = true;\n        this.form = {...row};\n        this.isDisabled = false;\n      },\n      //详情\n      getXq(row){\n        this.pdsDialogFormVisible = true;\n        this.form = {...row};\n        this.isDisabled = true;\n      },\n      //确定按钮\n      async getDetermine(){\n        this.$refs['form'].validate((valid) => {\n          if (valid){\n            addPds(this.form).then(res => {\n              if (res.code == \"0000\") {\n                this.$message.success(\"操作成功！\");\n                this.pdsDialogFormVisible = false;\n                this.getData();\n              }\n            });\n          }else{\n            setTimeout(() => {\n              var isError = document.getElementsByClassName(\"is-error\");\n              if (isError[0].querySelector('input')) {\n                isError[0].querySelector('input').focus();\n              } else if (isError[0].querySelector('textarea')) {\n                isError[0].querySelector('textarea').focus();\n              }\n            }, 1)\n            return false;\n          }\n        });\n      },\n      handleClose(){\n        this.form={};\n        this.$nextTick(() => {\n          this.form = this.$options.data().form;\n          this.resetForm(\"form\");\n        });\n      },\n      filterReset(){\n\n      },\n    }\n  };\n</script>\n\n<style lang='scss' scoped>\n  .head-container {\n    margin: 0 auto;\n    width: 100%;\n    height: calc(100vh - 225px);\n    overflow: auto;\n  }\n\n  .box-card {\n    margin-bottom: 15px;\n\n    .el-card__header {\n      background-color: rgb(235, 245, 255) !important;\n    }\n  }\n\n  .box-cardList {\n    height: 70%;\n  }\n\n  .item {\n    width: 200px;\n    float: left;\n  }\n\n  #main_container_dj {\n    height: calc(100vh - 84px);\n  }\n\n  .aside_height {\n    height: 81vh;\n  }\n\n  .defect .el-form-item:nth-child(odd) {\n    margin-right: 70px;\n  }\n\n  /*背景颜色调整*/\n  #main_container_dj, #main_container_dj .el-aside {\n    background-color: #b4caf1;\n  }\n\n  /deep/ .qxlr_dialog_insert .el-dialog__header {\n    background-color: #0cc283;\n  }\n\n  /deep/ .pmyBtn {\n    background: #0cc283;\n  }\n\n  /*!*弹出框内宽度设置*!*/\n\n  .el-select {\n    width: 100%;\n  }\n\n  .el-date-editor{\n    width: 100%;\n  }\n\n  .el-carousel__item h3 {\n    color: #475669;\n    font-size: 14px;\n    opacity: 0.75;\n    line-height: 150px;\n\n  }\n\n  .el-carousel__item:nth-child(2n) {\n    background-color: #99a9bf;\n  }\n\n  .el-carousel__item:nth-child(2n+1) {\n    background-color: #d3dce6;\n  }\n  //有子节点 且未展开\n  .el-tree ::v-deep .el-icon-caret-right:before {\n    background: url(\"../../../../assets/image/add.png\") no-repeat 0;\n    content: \"\";\n    display: block;\n    width: 16px;\n    height: 16px;\n    font-size: 16px;\n    background-size: 16px;\n  }\n  //有子节点 且已展开\n  .el-tree\n  ::v-deep\n  .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {\n    background: url(\"../../../../assets/image/prep.png\") no-repeat 0;\n    content: \"\";\n    display: block;\n    width: 16px;\n    height: 16px;\n    font-size: 16px;\n    background-size: 16px;\n  }\n  //没有子节点\n  .el-tree ::v-deep .el-tree-node__expand-icon.is-leaf::before {\n    background: transparent;\n    content: \"\";\n    display: block;\n    width: 16px;\n    height: 16px;\n    font-size: 16px;\n    background-size: 16px;\n  }\n</style>\n"]}]}