{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\activitimodel\\modeler.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\activitimodel\\modeler.vue", "mtime": 1706897321992}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Ci8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IHsKICBuYW1lOiAiTW9kZWxlciIsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHNyYzogIiIsCiAgICAgIGhlaWdodDogZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LmNsaWVudEhlaWdodCAtIDk0LjUgKyAicHg7IiwKICAgICAgbG9hZGluZzogdHJ1ZQogICAgfTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB2YXIgbW9kZXJJZCA9IHRoaXMuJHJvdXRlLnBhcmFtcyAmJiB0aGlzLiRyb3V0ZS5wYXJhbXMubW9kZWxJZDsKICAgIHZhciB1cmwgPSAiL2FjdGl2aXRpLWFwaS9tb2RlbGVyLmh0bWw/bW9kZWxJZD0iICsgbW9kZXJJZDsKICAgIHRoaXMuc3JjID0gdXJsOwogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHZhciB0aGF0ID0gdGhpczsKCiAgICB3aW5kb3cub25yZXNpemUgPSBmdW5jdGlvbiB0ZW1wKCkgewogICAgICB0aGF0LmhlaWdodCA9IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5jbGllbnRIZWlnaHQgLSA5NC41ICsgInB4OyI7CiAgICB9OwogIH0KfTsKZXhwb3J0cy5kZWZhdWx0ID0gX2RlZmF1bHQ7"}, {"version": 3, "sources": ["modeler.vue"], "names": [], "mappings": ";;;;;;;;;;;eAMA;AACA,EAAA,IAAA,EAAA,SADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,GAAA,EAAA,EADA;AAEA,MAAA,MAAA,EAAA,QAAA,CAAA,eAAA,CAAA,YAAA,GAAA,IAAA,GAAA,KAFA;AAGA,MAAA,OAAA,EAAA;AAHA,KAAA;AAKA,GARA;AASA,EAAA,OATA,qBASA;AACA,QAAA,OAAA,GAAA,KAAA,MAAA,CAAA,MAAA,IAAA,KAAA,MAAA,CAAA,MAAA,CAAA,OAAA;AACA,QAAA,GAAA,GAAA,wCAAA,OAAA;AACA,SAAA,GAAA,GAAA,GAAA;AACA,GAbA;AAcA,EAAA,OAAA,EAAA,mBAAA;AACA,QAAA,IAAA,GAAA,IAAA;;AACA,IAAA,MAAA,CAAA,QAAA,GAAA,SAAA,IAAA,GAAA;AACA,MAAA,IAAA,CAAA,MAAA,GAAA,QAAA,CAAA,eAAA,CAAA,YAAA,GAAA,IAAA,GAAA,KAAA;AACA,KAFA;AAGA;AAnBA,C", "sourcesContent": ["<template>\n  <div :style=\"'height:'+ height\">\n    <iframe :src=\"src\" frameborder=\"no\" style=\"width: 100%;height: 100%\" scrolling=\"auto\" />\n  </div>\n</template>\n<script>\n  export default {\n    name: \"Modeler\",\n    data() {\n      return {\n        src: \"\",\n        height: document.documentElement.clientHeight - 94.5 + \"px;\",\n        loading: true\n      };\n    },\n    created() {\n      const moderId = this.$route.params && this.$route.params.modelId;\n      let url = `/activiti-api/modeler.html?modelId=`+moderId;\n      this.src = url\n    },\n    mounted: function() {\n      const that = this;\n      window.onresize = function temp() {\n        that.height = document.documentElement.clientHeight - 94.5 + \"px;\";\n      };\n    }\n  };\n</script>\n"], "sourceRoot": "src/views/activiti/activitimodel"}]}