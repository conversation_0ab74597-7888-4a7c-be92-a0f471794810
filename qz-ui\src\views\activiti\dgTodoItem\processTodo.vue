<template>
  <div class="app-container">
    <el-white>
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane name="db">
          <span slot="label"><i class="el-icon-s-order"></i>待办</span>
        </el-tab-pane>
        <el-tab-pane name="yb">
          <span slot="label"><i class="el-icon-s-claim"></i>已办</span>
        </el-tab-pane>
      </el-tabs>
      <el-filter
        ref="filter1"
        :data="filterInfo.data"
        :field-list="filterInfo.fieldList"
        :width="{ labelWidth: 180, itemWidth: 200 }"
        @handleReset="filterReset"
        @handleEvent="handleFilterEvent"
        :btnHidden="false"
      ></el-filter>

      <comp-table
        :table-and-page-info="tableAndPageInfo"
        @update:multipleSelection="selectChange"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        height="75vh"
      >
        <el-table-column
          slot="table_start"
          align="center"
          style="display: block;word-break : normal;"
          label="事项标题"
          min-width="200"
          :resizable="false"
        >
          <template slot-scope="scope">
            <el-badge
              :value="
                scope.row.isHandle == 0
                  ? scope.row.itemContent &&
                    scope.row.itemContent.includes('退回')
                    ? '被退回'
                    : '待办理'
                  : '已办理'
              "
              class="item"
              :type="
                scope.row.isHandle == 0
                  ? scope.row.itemContent &&
                    scope.row.itemContent.includes('退回')
                    ? 'warning'
                    : 'danger'
                  : 'primary'
              "
            >
            </el-badge>
            <el-button type="text" size="small" @click="goPage(scope.row)">{{
              scope.row.itemName
            }}</el-button>
          </template>
        </el-table-column>
        <el-table-column
          slot="table_eight"
          align="center"
          fixed="right"
          style="display: block"
          label="操作"
          min-width="100"
          :resizable="false"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              title="跳转"
              class="el-icon-discover"
              @click="goPage(scope.row)"
            ></el-button>
            <el-button
              type="text"
              size="small"
              title="流程查看"
              class="el-icon-lcck commonIcon"
              @click="showTimeLine(scope.row)"
              v-if="scope.row.moduleKey !== 'probfbk'"
            ></el-button>
            <el-button
              type="text"
              @click="deleteTodo(scope.row)"
              title="删除"
              class="el-icon-delete"
            >
            </el-button>
          </template>
        </el-table-column>
      </comp-table>
    </el-white>

    <el-dialog
      title="待办详情"
      :visible.sync="openInfo"
      width="50%"
      append-to-body
      :close-on-click-modal="false"
      v-dialogDrag
    >
      <el-form ref="formInfo" :model="formInfo" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="标题：" prop="itemName">
              <el-input v-model="formInfo.itemName" :disabled="isDisabled" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="内容：" prop="itemContent">
              <el-input
                type="textarea"
                :rows="3"
                v-model="formInfo.itemContent"
                :disabled="isDisabled"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="模块名称：" prop="module">
              <el-input v-model="formInfo.module" :disabled="isDisabled" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="通知时间：" prop="todoTime">
              <el-date-picker
                v-model="formInfo.todoTime"
                type="datetime"
                :disabled="isDisabled"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
    <time-line
      :value="timeLineShow"
      :timeData="timeData"
      @closeTimeLine="colseTimeLine"
    />
  </div>
</template>

<script>
import timeLine from "com/timeLine";
import { HistoryList } from "@/api/activiti/processTask";
import {
  list,
  listByPage,
  insertOrUpdateTodoItem,
  delByIds
} from "@/api/activiti/DgTodoItem";
import { mapState, mapMutations, mapActions } from 'vuex'

export default {
  components: { timeLine },
  name: "processTodo",
  data() {
    return {
      filterInfo: {
        data: {
          module: "",
          todoUserName: "",
          handleUserName: "",
          applyUserName: ""
        },
        fieldList: [
          { label: "待办来源", type: "input", value: "module" },
          { label: "待办人", type: "input", value: "todoUserName" },
          { label: "处理人", type: "input", value: "handleUserName" },
          { label: "发起人", type: "input", value: "applyUserName" }
        ]
      },
      tableAndPageInfo: {
        pager: {
          pageSize: 10,
          pageNum: 1,
          total: 0,
          sizes: [10, 20, 50, 100]
        },
        tableData: [],
        tableHeader: [
          { prop: "module", label: "待办来源", minWidth: "120" },
          { prop: "taskName", label: "任务名称", minWidth: "140" },
          { prop: "isHandleCn", label: "是否已办", minWidth: "80" },
          { prop: "todoUserName", label: "待办人名称", minWidth: "100" },
          { prop: "handleUserName", label: "处理人名称", minWidth: "100" },
          { prop: "applyUserName", label: "发起人名称", minWidth: "100" },
          { prop: "todoTime", label: "通知时间", minWidth: "150" },
          { prop: "handleTime", label: "处理时间", minWidth: "150" }
        ],
        option: { checkBox: false, serialNumber: true }
      },
      params: {
        isHandle: 0
      },
      activeName: "db",
      selectRows: [],
      tabRefresh: {
        db: 0,
        yb: 1
      },
      openInfo: false,
      formInfo: {},
      isDisabled: false,
      timeData: [],
      timeLineShow: false,
      processData: {
        processDefinitionKey: "",
        businessKey: "",
        businessType: "",
        variables: {},
        nextUser: "",
        processType: "complete"
      }
    };
  },
  // watch: {
  //   //触发计算函数时执行
  //   qxjlObjIdChange(val) {
  //     this.getData({ objId: val });
  //   }
  // },
  //计算函数
  computed: {
    ...mapState('todoList', {
      savedActiveName: 'activeName',
      savedFilterInfo: 'filterInfo',
      savedPagination: 'pagination',
      savedScrollPosition: 'scrollPosition',
      savedTableData: 'tableData',
      savedTotal: 'total'
    })
  },
  created() {
    // 从 store 恢复状态
    this.restorePageState()
  },
  mounted() {
    // 恢复滚动位置
    this.$nextTick(() => {
      this.restoreScrollPosition()
      // 添加滚动监听
      this.addScrollListener()
    })
  },

  // keep-alive组件激活时调用
  activated() {
    console.log('组件激活，刷新数据')
    // 每次激活时刷新数据，确保显示最新状态
    this.getData(this.$route.query)

    // 恢复滚动位置
    this.$nextTick(() => {
      // 使用setTimeout确保页面完全渲染
      setTimeout(() => {
        this.restoreScrollPosition()
        // 确保滚动监听存在
        this.addScrollListener()
      }, 200)
    })
  },

  // keep-alive组件失活时调用
  deactivated() {
    console.log('组件失活，保存状态')
    // 先保存当前状态（使用当前已知的滚动位置，不重新获取）
    this.saveStateWithCurrentScrollPosition()
    // 移除滚动监听
    this.removeScrollListener()
  },

  // 组件销毁前
  beforeDestroy() {
    // 移除滚动监听
    this.removeScrollListener()
  },
  beforeRouteLeave(to, from, next) {
    // 在路由离开前保存页面状态
    // 注意：不要在这里调用saveCurrentState，因为此时表格可能已经被重置
    // 滚动位置应该已经通过滚动监听实时保存了
    console.log('路由离开，当前保存的滚动位置:', this.savedScrollPosition)
    next()
  },
  methods: {
    ...mapActions('todoList', ['savePageState', 'saveFilterInfo', 'savePagination', 'saveScrollPosition', 'saveTableData', 'markNeedRefresh', 'clearRefreshFlag']),

    // 保存当前状态
    saveCurrentState() {
      // 使用保存的滚动容器引用，或者重新查找
      let scrollPosition = 0
      if (this._scrollContainer) {
        scrollPosition = this._scrollContainer.scrollTop
      } else {
        // 重新查找滚动容器
        const tableWrapper = document.querySelector('.el-table__body-wrapper') ||
                           document.querySelector('.wrap .el-table__body-wrapper')
        if (tableWrapper) {
          scrollPosition = tableWrapper.scrollTop
        }
      }

      console.log('保存滚动位置:', scrollPosition)

      this.savePageState({
        activeName: this.activeName,
        filterInfo: this.filterInfo.data,
        pagination: {
          pageSize: this.tableAndPageInfo.pager.pageSize,
          pageNum: this.tableAndPageInfo.pager.pageNum
        },
        scrollPosition: scrollPosition,
        tableData: this.tableAndPageInfo.tableData,
        total: this.tableAndPageInfo.pager.total
      })
    },

    // 使用当前已保存的滚动位置保存状态（避免重新获取可能为0的值）
    saveStateWithCurrentScrollPosition() {
      console.log('使用当前已保存的滚动位置:', this.savedScrollPosition)

      this.savePageState({
        activeName: this.activeName,
        filterInfo: this.filterInfo.data,
        pagination: {
          pageSize: this.tableAndPageInfo.pager.pageSize,
          pageNum: this.tableAndPageInfo.pager.pageNum
        },
        scrollPosition: this.savedScrollPosition, // 使用已保存的值
        tableData: this.tableAndPageInfo.tableData,
        total: this.tableAndPageInfo.pager.total
      })
    },

    // 恢复页面状态的方法
    restorePageState() {
      // 恢复标签页
      if (this.savedActiveName && this.savedActiveName !== this.activeName) {
        this.activeName = this.savedActiveName
        this.params.isHandle = this.tabRefresh[this.savedActiveName]
      }

      // 恢复筛选条件
      if (this.savedFilterInfo && Object.keys(this.savedFilterInfo).length > 0) {
        this.filterInfo.data = { ...this.savedFilterInfo }
      }

      // 恢复分页信息
      if (this.savedPagination) {
        this.tableAndPageInfo.pager.pageSize = this.savedPagination.pageSize
        this.tableAndPageInfo.pager.pageNum = this.savedPagination.pageNum
      }

      // 直接获取最新数据，不使用缓存数据避免显示已办理的待办
      // 缓存数据仅用于滚动位置恢复，不用于数据显示
      this.getData(this.$route.query);
    },

    // 恢复滚动位置
    restoreScrollPosition() {
      if (this.savedScrollPosition > 0) {
        this.$nextTick(() => {
          // 尝试多种可能的滚动容器选择器
          let scrollContainer = null

          // 1. 尝试标准的el-table滚动容器
          scrollContainer = document.querySelector('.el-table__body-wrapper')

          // 2. 如果没找到，尝试comp-table的包装器
          if (!scrollContainer) {
            scrollContainer = document.querySelector('.wrap .el-table__body-wrapper')
          }

          // 3. 如果还没找到，尝试通过comp-table组件查找
          if (!scrollContainer) {
            const compTableEl = this.$el.querySelector('.wrap')
            if (compTableEl) {
              scrollContainer = compTableEl.querySelector('.el-table__body-wrapper')
            }
          }

          console.log('恢复滚动位置:', this.savedScrollPosition, '容器:', scrollContainer)

          if (scrollContainer) {
            scrollContainer.scrollTop = this.savedScrollPosition

            // 验证设置是否成功
            setTimeout(() => {
              console.log('滚动位置恢复验证:', scrollContainer.scrollTop)
            }, 100)
          } else {
            console.warn('未找到表格滚动容器')
          }
        })
      }
    },

    // 添加滚动监听
    addScrollListener() {
      this.$nextTick(() => {
        // 如果已经有监听器，先移除
        if (this._scrollHandler) {
          this.removeScrollListener()
        }

        // 使用和恢复滚动位置相同的逻辑查找滚动容器
        let scrollContainer = null

        // 1. 尝试标准的el-table滚动容器
        scrollContainer = document.querySelector('.el-table__body-wrapper')

        // 2. 如果没找到，尝试comp-table的包装器
        if (!scrollContainer) {
          scrollContainer = document.querySelector('.wrap .el-table__body-wrapper')
        }

        // 3. 如果还没找到，尝试通过comp-table组件查找
        if (!scrollContainer) {
          const compTableEl = this.$el.querySelector('.wrap')
          if (compTableEl) {
            scrollContainer = compTableEl.querySelector('.el-table__body-wrapper')
          }
        }

        // 4. 最后尝试通过高度属性查找
        if (!scrollContainer) {
          const tables = document.querySelectorAll('.el-table__body-wrapper')
          for (let table of tables) {
            const tableEl = table.closest('.el-table')
            if (tableEl && tableEl.style.height === '75vh') {
              scrollContainer = table
              break
            }
          }
        }

        console.log('添加滚动监听的容器:', scrollContainer)

        if (scrollContainer) {
          this._scrollContainer = scrollContainer // 保存引用
          this._scrollHandler = this.throttle(() => {
            const currentScrollTop = scrollContainer.scrollTop
            console.log('滚动位置变化:', currentScrollTop)
            // 直接更新store中的滚动位置
            this.saveScrollPosition(currentScrollTop)
          }, 300)
          scrollContainer.addEventListener('scroll', this._scrollHandler)
        } else {
          console.warn('未找到表格滚动容器，无法添加滚动监听')
        }
      })
    },

    // 移除滚动监听
    removeScrollListener() {
      if (this._scrollContainer && this._scrollHandler) {
        this._scrollContainer.removeEventListener('scroll', this._scrollHandler)
        this._scrollHandler = null
        this._scrollContainer = null
        console.log('滚动监听已移除')
      }
    },

    // 节流函数
    throttle(func, delay) {
      let timeoutId
      let lastExecTime = 0
      return function (...args) {
        const currentTime = Date.now()

        if (currentTime - lastExecTime > delay) {
          func.apply(this, args)
          lastExecTime = currentTime
        } else {
          clearTimeout(timeoutId)
          timeoutId = setTimeout(() => {
            func.apply(this, args)
            lastExecTime = Date.now()
          }, delay - (currentTime - lastExecTime))
        }
      }
    },

    // 检查并修正页码
    checkAndCorrectPageNum(records, total) {
      const currentPage = this.tableAndPageInfo.pager.pageNum
      const pageSize = this.tableAndPageInfo.pager.pageSize
      const maxPage = Math.ceil(total / pageSize) || 1

      console.log('页码检查:', {
        currentPage,
        maxPage,
        total,
        recordsLength: records.length,
        hasData: records.length > 0
      })

      // 情况1：当前页超出了最大页数
      if (currentPage > maxPage) {
        console.log(`当前页${currentPage}超出最大页${maxPage}，修正到第${maxPage}页`)
        this.tableAndPageInfo.pager.pageNum = maxPage
        // 更新store中的分页信息，并清除滚动位置
        this.savePagination({
          pageSize: pageSize,
          pageNum: maxPage
        })
        this.saveScrollPosition(0) // 清除滚动位置
        return true // 需要重新请求
      }

      // 情况2：当前页没有数据，但总数大于0（说明数据在其他页）
      if (records.length === 0 && total > 0 && currentPage > 1) {
        console.log(`当前页${currentPage}无数据但总数${total}>0，修正到第${maxPage}页`)
        this.tableAndPageInfo.pager.pageNum = maxPage
        // 更新store中的分页信息，并清除滚动位置
        this.savePagination({
          pageSize: pageSize,
          pageNum: maxPage
        })
        this.saveScrollPosition(0) // 清除滚动位置
        return true // 需要重新请求
      }

      // 情况3：总数为0，确保在第1页
      if (total === 0 && currentPage !== 1) {
        console.log('总数为0，修正到第1页')
        this.tableAndPageInfo.pager.pageNum = 1
        // 更新store中的分页信息，并清除滚动位置
        this.savePagination({
          pageSize: pageSize,
          pageNum: 1
        })
        this.saveScrollPosition(0) // 清除滚动位置
        return true // 需要重新请求
      }

      return false // 不需要修正
    },

    // 调试方法：查看页面中所有可能的滚动容器
    debugScrollContainers() {
      console.log('=== 调试滚动容器 ===')

      // 查找所有可能的滚动容器
      const containers = [
        { name: 'app-container', el: document.querySelector('.app-container') },
        { name: 'el-table__body-wrapper', el: document.querySelector('.el-table__body-wrapper') },
        { name: 'wrap .el-table__body-wrapper', el: document.querySelector('.wrap .el-table__body-wrapper') },
        { name: 'comp-table内的wrapper', el: this.$el.querySelector('.wrap .el-table__body-wrapper') }
      ]

      containers.forEach(container => {
        if (container.el) {
          console.log(`${container.name}:`, {
            element: container.el,
            scrollTop: container.el.scrollTop,
            scrollHeight: container.el.scrollHeight,
            clientHeight: container.el.clientHeight,
            hasScroll: container.el.scrollHeight > container.el.clientHeight
          })
        } else {
          console.log(`${container.name}: 未找到`)
        }
      })

      // 查找所有el-table__body-wrapper
      const allWrappers = document.querySelectorAll('.el-table__body-wrapper')
      console.log('所有el-table__body-wrapper:', allWrappers)
      allWrappers.forEach((wrapper, index) => {
        console.log(`wrapper ${index}:`, {
          element: wrapper,
          scrollTop: wrapper.scrollTop,
          scrollHeight: wrapper.scrollHeight,
          clientHeight: wrapper.clientHeight,
          hasScroll: wrapper.scrollHeight > wrapper.clientHeight,
          parentTable: wrapper.closest('.el-table')
        })
      })

      console.log('当前保存的滚动位置:', this.savedScrollPosition)
      console.log('=== 调试结束 ===')
    },
    
    async getData(param) {
      // 合并路由参数和筛选条件
      this.params = { ...this.params, ...param, ...this.filterInfo.data }

      if (!this.params.todoUserId) {
        this.params.todoUserId = this.$store.getters.name
      }

      let { code, data } = await listByPage({
        ...this.params,
        pageNum: this.tableAndPageInfo.pager.pageNum,
        pageSize: this.tableAndPageInfo.pager.pageSize
      })

      if (code === "0000") {
        // 检查页码是否需要修正
        const needPageCorrection = this.checkAndCorrectPageNum(data.records, data.total)

        if (needPageCorrection) {
          // 页码需要修正，重新请求数据
          console.log('页码需要修正，重新请求数据')
          return this.getData(param)
        }

        this.tableAndPageInfo.tableData = data.records
        this.tableAndPageInfo.pager.total = data.total

        // 保存表格数据到store
        this.saveTableData({
          tableData: data.records,
          total: data.total
        })

        // 清除刷新标记，表示数据已是最新
        this.clearRefreshFlag()

        // 数据加载完成后，延迟恢复滚动位置和添加滚动监听
        this.$nextTick(() => {
          // 使用setTimeout确保表格完全渲染
          setTimeout(() => {
            this.restoreScrollPosition()
            this.addScrollListener()
          }, 100)
        })
      }
    },
    selectChange(rows) {
      this.selectRows = rows;
    },

    // 处理筛选条件变化事件
    handleFilterEvent(obj, data) {
      // 实时保存筛选条件
      this.saveFilterInfo(data)
      // 如果是回车或者change事件，触发搜索
      if (obj.value !== undefined) {
        this.filterInfo.data = data
        this.tableAndPageInfo.pager.pageNum = 1 // 重置到第一页
        this.getData()
        // 保存分页状态
        this.savePagination({
          pageSize: this.tableAndPageInfo.pager.pageSize,
          pageNum: 1
        })
      }
    },

    // 重置筛选条件
    filterReset(data) {
      this.filterInfo.data = data
      this.tableAndPageInfo.pager.pageNum = 1 // 重置到第一页
      this.getData()
      // 保存重置后的筛选条件
      this.saveFilterInfo(data)
      // 保存分页状态
      this.savePagination({
        pageSize: this.tableAndPageInfo.pager.pageSize,
        pageNum: 1
      })
    },
    async getDetails(row) {
      this.formInfo = { ...row };
      this.isDisabled = true;
      this.openInfo = true;
      //如果是未查看状态，点击查看时变成已查看
      // if(row.isView==0){
      //   await insertOrUpdateTodoItem({objId:row.objId,isView:'1'})
      //   this.getData()
      // }
    },
    deleteTodo(row) {
      this.$confirm("确认删除选中数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          delByIds([row.objId]).then(res => {
            if (res.code === "0000") {
              this.$message({
                message: "删除成功",
                type: "success"
              });
            } else {
              this.$message.error("操作失败");
            }
            this.getData();
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          });
        });
    },
    handleClick(tab) {
      for (let key in this.tabRefresh) {
        if (key === tab.name) {
          this.activeName = tab.name
          this.params.isHandle = this.tabRefresh[key]
          // 切换标签页时重置分页到第一页
          this.tableAndPageInfo.pager.pageNum = 1
          this.getData()
          // 保存当前状态
          this.saveCurrentState()
        }
      }
    },
    closeForm() {
      this.formInfo = {};
    },
    goPage(row) {
      // 标记需要刷新数据，因为用户可能会办理待办
      this.markNeedRefresh()

      if (row.moduleKey === "gzplccs" && false) {
        //解决工作票跳转404的问题,疑似舍弃了
        const topMenus = this.$store.getters.topMenus;
        if (topMenus.length > 0) {
          for (const topMenu of topMenus) {
            if (topMenu.name === "工作票管理") {
              this.$router.push({
                path: topMenu.path,
                query: { objId: row.businessId, module: row.moduleKey }
              });
              break;
            }
          }
        }
      } else {
        this.$router.push({
          path: row.routePath,
          query: { objId: row.businessId, module: row.moduleKey }
        });
      }
    },
    async showTimeLine(row) {
      this.processData.businessKey = row.businessId;
      this.processData.processDefinitionKey = row.moduleKey;
      this.processData.businessType = row.module;
      let { code, data } = await HistoryList(this.processData);
      this.timeData = data;
      this.timeLineShow = true;
    },
    //关闭流程查看页面
    colseTimeLine() {
      this.timeLineShow = false;
    },
    // 监听分页变化
    handleSizeChange(val) {
      this.tableAndPageInfo.pager.pageSize = val
      this.tableAndPageInfo.pager.pageNum = 1 // 重置到第一页
      this.getData()
      // 保存分页状态
      this.savePagination({
        pageSize: val,
        pageNum: 1
      })
    },
    handleCurrentChange(val) {
      this.tableAndPageInfo.pager.pageNum = val
      this.getData()
      // 保存分页状态
      this.savePagination({
        pageSize: this.tableAndPageInfo.pager.pageSize,
        pageNum: val
      })
    },
    // 监听筛选条件变化
    handleFilter(data) {
      this.filterInfo.data = data
      this.tableAndPageInfo.pager.pageNum = 1 // 重置到第一页
      this.getData(data)
      // 保存筛选条件
      this.saveFilterInfo(data)
      // 保存分页状态
      this.savePagination({
        pageSize: this.tableAndPageInfo.pager.pageSize,
        pageNum: 1
      })
    }
  }
};
</script>

<style scoped lang="scss">
.item {
  width: 8.5rem;
  height: 1.25rem;
}
</style>
