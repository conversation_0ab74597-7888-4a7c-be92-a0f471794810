{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\Index\\Workbench.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\Index\\Workbench.vue", "mtime": 1706897320623}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Workbench.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAiGA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;AACA,EAAA,IAAA,EAAA,WADA;AACA;AACA,EAAA,KAAA,EAAA;AACA,IAAA,UAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAAA,EAAA;AAFA,KADA;AAKA,IAAA,WAAA,EAAA;AALA,GAFA;AASA,EAAA,IATA,kBASA;AACA,WAAA;AACA,MAAA,aAAA,EAAA,KADA;AAEA,MAAA,iBAAA,EAAA,EAFA;AAGA,MAAA,MAAA,EAAA,CAHA;AAIA,MAAA,GAAA,EAAA,YAJA;AAKA,MAAA,QAAA,EAAA,IALA;AAMA;AACA,MAAA,WAAA,EAAA,CAPA;AAQA,MAAA,SAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA,+BADA;AAEA,QAAA,IAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,IAAA,EAAA,kBADA;AAEA,QAAA,IAAA,EAAA;AAFA,OALA,EAQA;AACA,QAAA,IAAA,EAAA,+BADA;AAEA,QAAA,IAAA,EAAA;AAFA,OARA,EAWA;AACA,QAAA,IAAA,EAAA,kBADA;AAEA,QAAA,IAAA,EAAA;AAFA,OAXA,EAcA;AACA,QAAA,IAAA,EAAA,+BADA;AAEA,QAAA,IAAA,EAAA;AAFA,OAdA,EAkBA;AACA,QAAA,IAAA,EAAA,kBADA;AAEA,QAAA,IAAA,EAAA;AAFA,OAlBA,EAsBA;AACA,QAAA,IAAA,EAAA,kBADA;AAEA,QAAA,IAAA,EAAA;AAFA,OAtBA,EA0BA;AACA,QAAA,IAAA,EAAA,+BADA;AAEA,QAAA,IAAA,EAAA;AAFA,OA1BA,EA6BA;AACA,QAAA,IAAA,EAAA,+BADA;AAEA,QAAA,IAAA,EAAA;AAFA,OA7BA,CARA;AA0CA,MAAA,UAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA,sBADA;AAEA,QAAA,IAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,IAAA,EAAA,sBADA;AAEA,QAAA,IAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,IAAA,EAAA,sBADA;AAEA,QAAA,IAAA,EAAA;AAFA,OATA,EAaA;AACA,QAAA,IAAA,EAAA,+BADA;AAEA,QAAA,IAAA,EAAA;AAFA,OAbA,EAiBA;AACA,QAAA,IAAA,EAAA,+BADA;AAEA,QAAA,IAAA,EAAA;AAFA,OAjBA,EAoBA;AACA,QAAA,IAAA,EAAA,+BADA;AAEA,QAAA,IAAA,EAAA;AAFA,OApBA,EAuBA;AACA,QAAA,IAAA,EAAA,+BADA;AAEA,QAAA,IAAA,EAAA;AAFA,OAvBA,EA0BA;AACA,QAAA,IAAA,EAAA,+BADA;AAEA,QAAA,IAAA,EAAA;AAFA,OA1BA,EA6BA;AACA,QAAA,IAAA,EAAA,+BADA;AAEA,QAAA,IAAA,EAAA;AAFA,OA7BA,CA1CA;AA4EA,MAAA,QAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA;AADA,OADA,EAGA;AACA,QAAA,IAAA,EAAA;AADA,OAHA,EAMA;AACA,QAAA,IAAA,EAAA;AADA,OANA,EASA;AACA,QAAA,IAAA,EAAA;AADA,OATA,EAYA;AACA,QAAA,IAAA,EAAA;AADA,OAZA,EAcA;AACA,QAAA,IAAA,EAAA;AADA,OAdA,EAiBA;AACA,QAAA,IAAA,EAAA;AADA,OAjBA,EAmBA;AACA,QAAA,IAAA,EAAA;AADA,OAnBA,EAsBA;AACA,QAAA,IAAA,EAAA;AADA,OAtBA,EAwBA;AACA,QAAA,IAAA,EAAA;AADA,OAxBA,EA2BA;AACA,QAAA,IAAA,EAAA;AADA,OA3BA,EA6BA;AACA,QAAA,IAAA,EAAA;AADA,OA7BA,CA5EA;AA6GA,MAAA,MAAA,EAAA,KA7GA;AA8GA,MAAA,cAAA,EAAA,KA9GA;AA8GA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA,SAHA;AAIA,QAAA,OAAA,EAAA;AAJA,OA/GA;AAqHA,MAAA,MAAA,EAAA;AACA,QAAA,QAAA,EAAA,GADA;AAEA,QAAA,EAAA,EAAA;AAFA,OArHA;AAyHA,MAAA,QAAA,EAAA,EAzHA;AA0HA,MAAA,SAAA,EAAA,EA1HA;AA2HA,MAAA,WAAA,EAAA,CA3HA;AA4HA,MAAA,WAAA,EAAA,CA5HA;AA6HA,MAAA,YAAA,EAAA,CA7HA;AA8HA,MAAA,iBAAA,EAAA,CA9HA;AA+HA,MAAA,UAAA,EAAA;AACA,QAAA,gBAAA,EAAA,CADA;AAEA,QAAA,UAAA,EAAA,CAFA;AAGA,QAAA,UAAA,EAAA;AAHA;AA/HA,KAAA;AAqIA,GA/IA;AAiJA,EAAA,OAjJA,qBAiJA;AACA;AACA;AACA,SAAA,OAAA,GAHA,CAIA;;AACA,SAAA,WAAA;AACA,GAvJA;AAwJA,EAAA,OAAA,EAAA;AACA,IAAA,OADA,mBACA,IADA,EACA;AACA,UAAA,IAAA,EAAA,KAAA;;AACA,cAAA,KAAA,MAAA,CAAA,EAAA;AACA,aAAA,CAAA;AACA,UAAA,IAAA,GAAA,mBAAA;AACA,UAAA,KAAA,GAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA;AAAA,WAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,IAAA,GAAA,kBAAA;AACA,UAAA,KAAA,GAAA;AAAA,YAAA,EAAA,EAAA,IAAA,CAAA;AAAA,WAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,IAAA,GAAA,wBAAA;AACA,UAAA,KAAA,GAAA;AAAA,YAAA,EAAA,EAAA,IAAA,CAAA;AAAA,WAAA;AACA;;AACA,aAAA,CAAA;AACA,UAAA,IAAA,GAAA,kBAAA;AACA,UAAA,KAAA,GAAA;AAAA,YAAA,EAAA,EAAA,IAAA,CAAA;AAAA,WAAA;AACA;AAhBA;;AAkBA,WAAA,OAAA,CAAA,IAAA,CAAA;AACA,QAAA,IAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAAA;AAIA,KAzBA;AA0BA;AACA,IAAA,WA3BA,uBA2BA,OA3BA,EA2BA;AACA,cAAA,OAAA;AACA,aAAA,aAAA;AACA,eAAA,MAAA,CAAA,EAAA,GAAA,CAAA;AACA;;AACA,aAAA,YAAA;AACA,eAAA,MAAA,CAAA,EAAA,GAAA,CAAA;AACA;;AACA,aAAA,kBAAA;AACA,eAAA,MAAA,CAAA,EAAA,GAAA,CAAA;AACA;;AACA,aAAA,YAAA;AACA,eAAA,MAAA,CAAA,EAAA,GAAA,CAAA;AACA;AAZA;;AAcA,WAAA,OAAA;AACA,WAAA,GAAA,GAAA,OAAA;AACA,KA5CA;AA6CA,IAAA,WA7CA,yBA6CA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,oCADA;;AAAA;AAAA;AACA,gBAAA,IADA,yBACA,IADA;AACA,gBAAA,IADA,yBACA,IADA;;AAAA,sBAEA,IAAA,KAAA,MAFA;AAAA;AAAA;AAAA;;AAGA,gBAAA,OAAA,CAAA,GAAA,CAAA,MAAA,EAAA,IAAA;AAHA;AAAA,uBAIA,KAAA,CAAA,MAAA,CAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,CAAA,KAAA,CAJA;;AAAA;AAKA,gBAAA,KAAA,CAAA,WAAA,GAAA,IAAA,CAAA,WAAA;AACA,gBAAA,KAAA,CAAA,WAAA,GAAA,IAAA,CAAA,WAAA;AACA,gBAAA,KAAA,CAAA,YAAA,GAAA,IAAA,CAAA,YAAA;AACA,gBAAA,KAAA,CAAA,iBAAA,GAAA,IAAA,CAAA,iBAAA;;AARA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,KAvDA;AAwDA,IAAA,OAxDA,qBAwDA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA,+BACA,MAAA,CAAA,MAAA,CAAA,EADA;AAAA,kDAEA,CAFA,wBAQA,CARA,yBAcA,CAdA,yBAoBA,CApBA;AAAA;;AAAA;AAAA;AAAA,uBAGA,sBAAA;AAAA,kBAAA,QAAA,EAAA;AAAA,iBAAA,CAHA;;AAAA;AAAA;AAGA,gBAAA,IAHA,eAGA,IAHA;AAGA,gBAAA,IAHA,eAGA,IAHA;;AAIA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,GAAA,IAAA;AACA;;AANA;;AAAA;AAAA;AAAA,uBASA,+BAAA;AAAA,kBAAA,IAAA,EAAA;AAAA,iBAAA,CATA;;AAAA;AASA,gBAAA,MATA;;AAUA,oBAAA,MAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,SAAA,GAAA,MAAA,CAAA,IAAA;AACA;;AAZA;;AAAA;AAAA;AAAA,uBAeA,+BAAA;AAAA,kBAAA,IAAA,EAAA;AAAA,iBAAA,CAfA;;AAAA;AAeA,gBAAA,OAfA;;AAgBA,oBAAA,OAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,SAAA,GAAA,OAAA,CAAA,IAAA;AACA;;AAlBA;;AAAA;AAAA;AAAA,uBAqBA,+BAAA;AAAA,kBAAA,IAAA,EAAA;AAAA,iBAAA,CArBA;;AAAA;AAqBA,gBAAA,OArBA;;AAsBA,oBAAA,OAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,SAAA,GAAA,OAAA,CAAA,IAAA;AACA;;AAxBA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA2BA,KAnFA;AAoFA,IAAA,KApFA,iBAoFA,GApFA,EAoFA;AACA,WAAA,GAAA,GAAA,GAAA;;AACA,UAAA,GAAA,IAAA,OAAA,IAAA,GAAA,IAAA,OAAA,EAAA;AACA,aAAA,QAAA,GAAA,IAAA;AACA;;AAAA,UAAA,GAAA,IAAA,QAAA,IAAA,GAAA,IAAA,MAAA,EAAA;AACA,aAAA,QAAA,GAAA,KAAA;AACA;AACA,KA3FA;AA4FA,IAAA,iBA5FA,+BA4FA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,KA9FA;AA+FA;AACA,IAAA,qBAhGA,iCAgGA,SAhGA,EAgGA;AACA,WAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,QAAA;AAAA,OAAA,CAAA;AACA,KAlGA;AAmGA,IAAA,WAnGA,yBAmGA;AACA,WAAA,cAAA;AACA,KArGA;AAsGA,IAAA,UAtGA,wBAsGA;AACA,WAAA,SAAA,CAAA,WAAA;AACA,WAAA,WAAA;AACA,KAzGA;AA0GA,IAAA,cA1GA,4BA0GA;AAAA;;AACA,4BAAA,KAAA,WAAA,EAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA,QAAA,MAAA,CAAA,iBAAA,GAAA,QAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,MAAA,CAAA,MAAA,GAAA,QAAA,CAAA,IAAA,CAAA,KAAA;AACA,OAHA;AAIA,KA/GA;AAgHA,IAAA,YAhHA,wBAgHA,YAhHA,EAgHA;AACA,UAAA,YAAA,EAAA;AACA,4BAAA,YAAA;AACA,OAFA,MAEA;AACA,aAAA,UAAA,CAAA,OAAA;AACA;AACA;AAtHA,GAxJA;AAgRA,EAAA,KAAA,EAAA;AACA,IAAA,UADA,sBACA,MADA,EACA,CAEA;AAHA;AAhRA,C", "sourcesContent": ["<template>\n  <div :notSpanNum=\"notSpanNum\" class=\"borderCls1 notices\" :class=\"notDivClass\">\n    <div>\n      <div class=\"txtTitle\">\n        <!-- <span @click=\"handleClick('processTodo')\" :class=\"this.val == 'processTodo'?'tabActive':'noActive'\">待办<el-badge :value=\"processCount\"></el-badge></span> -->\n        <span @click=\"handleClick('noticeTodo')\" :class=\"this.val == 'noticeTodo'?'tabActive':'noActive'\">通知<el-badge :value=\"noticeCount\"></el-badge></span>\n        <span @click=\"handleClick('proclamationTodo')\" :class=\"this.val == 'proclamationTodo'?'tabActive':'noActive'\">公告<el-badge :value=\"proclamationCount\"></el-badge></span>\n        <span @click=\"handleClick('remindTodo')\" :class=\"this.val == 'remindTodo'?'tabActive':'noActive'\">提醒<el-badge :value=\"remindCount\"></el-badge></span>\n        <!--        <el-button style=\"float: right; margin-top: 5px; margin-right: 20px;color:#b1b1b1\" type=\"text\"\n                           @click=\"showHistoryNotice\">More+\n                </el-button>-->\n      </div>\n      <div v-show=\"this.val == 'processTodo'\">\n        <el-table :data=\"datalist\" :show-header=\"status\"  class=\"work_table notice_box\">\n          <el-table-column>\n            <template slot-scope=\"scope\">\n              <a class=\"aaClass\" @click=\"getPage(scope.row)\">\n                <el-badge :value=\"scope.row.isHandle==0?'待办理':'已办理'\" class=\"item\"\n                          :type=\"scope.row.isHandle==0?'danger':'primary'\">\n                  <p class=\"titleClass\">{{ scope.row.itemName }}</p>\n                </el-badge>\n              </a>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"todoTime\" class=\"timeClass\" width=\"160\"></el-table-column>\n        </el-table>\n      </div>\n      <div class=\"work_box\" v-show=\"this.val != 'processTodo'\">\n        <el-table :data=\"datalist2\" :show-header=\"status\" class=\"work_table notice_box\">\n          <el-table-column>\n            <template slot-scope=\"scope\">\n              <a class=\"aaClass\" @click=\"getPage(scope.row)\">\n                <el-badge class=\"item\" :value=\"scope.row.isRead==0?'未读':'已读'\" :type=\"scope.row.isRead==0?'danger':'primary'\">\n                  <p class=\"titleClass\">{{ scope.row.title }}</p>\n                </el-badge>\n              </a>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"publishStartTime\" width=\"180\" class=\"timeClass\"></el-table-column>\n        </el-table>\n      </div>\n    </div>\n\n\n    <!--历史公告弹框-->\n    <el-dialog title=\"历史公告\" :visible.sync=\"historyNotice\" width=\"60%\" v-dialogDrag>\n      <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" label-width=\"120px\">\n        <el-form-item label=\"公告标题：\" prop=\"title\">\n          <el-input v-model=\"queryParams.title\" placeholder=\"请输入标题\" clearable\n                    @keyup.enter.native=\"handleQuery\"/>\n        </el-form-item>\n        <el-form-item label=\"公告内容：\" prop=\"content\">\n          <el-input v-model=\"queryParams.content\" placeholder=\"请输入内容\" clearable\n                    @keyup.enter.native=\"handleQuery\"/>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\n          <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\n        </el-form-item>\n      </el-form>\n      <el-table stripe border :data=\"historyNoticeList\" @selection-change=\"handleSelectionChange\">\n        <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n        <el-table-column label=\"公告标题\" align=\"center\" prop=\"title\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"公告内容\" align=\"center\" prop=\"content\" width=\"150\">\n          <template slot-scope=\"scope\">\n            <el-popover v-if=\"scope.row.content.length>8\" trigger=\"hover\" placement=\"top\" width=\"200\">\n              {{ scope.row.content }}\n              <div slot=\"reference\">\n                {{ scope.row.content.substring(0,8)+'...' }}\n              </div>\n            </el-popover>\n            <span v-else>\n            {{ scope.row.content}}\n          </span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"发布时间\" align=\"center\" prop=\"publishstarttime\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"结束时间\" align=\"center\" prop=\"publishendtime\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"附件\" align=\"center\" prop=\"attachment\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"操作\" align=\"center\" width=\"180\" class-name=\"small-padding fixed-width\">\n          <template slot-scope=\"scope\">\n            <el-button size=\"mini\" type=\"text\" @click=\"downloadFile(scope.row.attachmentid)\">下载附件\n            </el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <pagination v-show=\"hTotal>0\"\n                  :total=\"hTotal\"\n                  :page.sync=\"queryParams.pageNum\"\n                  :limit.sync=\"queryParams.pageSize\"\n                  @pagination=\"getHistoryList\"/>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { list } from '@/api/activiti/DgTodoItem'\nimport { download } from '@/api/tool/file'\nimport { countNoticeNumber, getNoticeList } from '@/api/activiti/DgTodoItem'\n\nexport default {\n  name: 'Workbench',//工作台通知公告\n  props: {\n    notSpanNum: {\n      type: Number,\n      default: 12\n    },\n    notDivClass: ''\n  },\n  data() {\n    return {\n      historyNotice: false,\n      historyNoticeList: [],\n      hTotal: 0,\n      val: 'noticeTodo',\n      todoShow: true,\n      //用于布局动态设置高度\n      activeClass: 1,\n      tableData: [\n        {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022年01月31日'\n        },\n        {\n          date: '您有一个工单需要审批,请及时审批',\n          name: '2022年01月31日'\n        }, {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022年01月31日'\n        }, {\n          date: '您有一个工单需要审批,请及时审批',\n          name: '2022年01月31日'\n        }, {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022年01月31日'\n        },\n        {\n          date: '您有一个工单需要审批,请及时审批',\n          name: '2022年01月31日'\n        },\n        {\n          date: '您有一个工单需要审批,请及时审批',\n          name: '2022年01月31日'\n        },\n        {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022年01月31日'\n        }, {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022年01月31日'\n        }\n      ],\n      tableData1: [\n        {\n          date: '您提交的工单已经审批通过了、点击查看详情',\n          name: '2022-01-31'\n        },\n        {\n          date: '您提交的工单已经审批通过了、点击查看详情',\n          name: '2022-01-31'\n        },\n        {\n          date: '您提交的工单已经审批通过了、点击查看详情',\n          name: '2022-01-31'\n        },\n        {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022-01-31'\n        },\n        {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022-01-31'\n        }, {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022-01-31'\n        }, {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name:'2022-01-31'\n        }, {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022-01-31'\n        }, {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022-01-31'\n        }\n      ],\n      listData: [\n        {\n          date: 'DL/Z 398-2010-电力行业信息化标准体系'\n        }, {\n          date: 'DL/T 417-2006-电力设备局部放电现场测量导则'\n        },\n        {\n          date: 'DL/T 423-2009-绝缘油中含气量测定方法真空压差法 '\n        },\n        {\n          date: 'DL/Z 5334-2006 -电力工程勘测安全技术规程'\n        },\n        {\n          date: 'DL/Z 398-2010-电力行业信息化标准体系'\n        }, {\n          date: 'DL/T 1148-2009-电力电缆线路巡检系统'\n        },\n        {\n          date: 'DL/T 800-2001-电力企业标准编制规则'\n        }, {\n          date: 'DL/T 727-2000-互感器运行检修导则 '\n        },\n        {\n          date: 'DL/T 720-2000-电力系统继电保护柜、屏通用技术条件 '\n        }, {\n          date: 'DL/T 676-1999-带电作业绝缘鞋（靴〕通用技术条件 '\n        },\n        {\n          date: 'DL/T 664-2008-带电设备红外诊断应用规范 '\n        }, {\n          date: 'DL/T 574-2010-变压器分接开关运行维修导则 '\n        }\n      ],\n      status: false,\n      maxTableHeight: '340',//表格最大宽度\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        title: undefined,\n        content: undefined\n      },\n      params: {\n        isHandle: '0',\n        lx: 2\n      },\n      datalist:[],\n      datalist2: [],\n      remindCount: 0,\n      noticeCount: 0,\n      processCount: 0,\n      proclamationCount: 0,\n      tabRefresh: {\n        proclamationTodo: 2,\n        remindTodo: 3,\n        noticeTodo: 1\n      },\n    }\n  },\n\n  created() {\n    // this.getHistoryList()\n    //获取数据\n    this.getData();\n    //获取各个模块提醒数量\n    this.countNumber();\n  },\n  methods: {\n    getPage(item) {\n      let path, query\n      switch (this.params.lx) {\n        case 1:\n          path = '/todo/processTodo'\n          query = {objId: item.objId}\n          break;\n        case 2:\n          path = '/todo/noticeTodo'\n          query = {id: item.id}\n          break;\n        case 3:\n          path = '/todo/proclamationTodo'\n          query = {id: item.id}\n          break;\n        case 4:\n          path = '/todo/remindTodo'\n          query = {id: item.id}\n          break;\n      }\n      this.$router.push({\n        path: path,\n        query: query\n      })\n    },\n    //标签页点击\n    handleClick(tabName) {\n      switch (tabName){\n        case 'processTodo':\n          this.params.lx = 1;\n          break;\n        case 'noticeTodo':\n          this.params.lx = 2;\n          break;\n        case 'proclamationTodo':\n          this.params.lx = 3;\n          break;\n        case 'remindTodo':\n          this.params.lx = 4;\n          break;\n      }\n      this.getData();\n      this.val = tabName;\n    },\n    async countNumber() {\n      let {code, data} = await countNoticeNumber()\n      if (code === '0000') {\n        console.log('data',data);\n        await this.$store.dispatch('app/setNoticeCount', data.total)\n        this.remindCount = data.remindCount;\n        this.noticeCount = data.noticeCount;\n        this.processCount = data.processCount;\n        this.proclamationCount = data.proclamationCount;\n      }\n    },\n    async getData() {\n      switch (this.params.lx) {\n        case 1:\n          let {code, data} = await list({isHandle: '0'})\n          if (code === '0000') {\n            this.datalist = data;\n          }\n          break;\n        case 2:\n          let result = await getNoticeList({type: 1})\n          if (result.code === '0000') {\n            this.datalist2 = result.data;\n          }\n          break;\n        case 3:\n          let result2 = await getNoticeList({type: 0})\n          if (result2.code === '0000') {\n            this.datalist2 = result2.data;\n          }\n          break;\n        case 4:\n          let result3 = await getNoticeList({type: 2})\n          if (result3.code === '0000') {\n            this.datalist2 = result3.data;\n          }\n          break;\n      }\n    },\n    click(val) {\n      this.val = val\n      if (val == 'first' ||val == 'three'  ) {\n        this.todoShow = true\n      } if (val == 'second' || val == 'four' ){\n        this.todoShow = false\n      }\n    },\n    showHistoryNotice() {\n      this.historyNotice = true\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map((item) => item.noticeid)\n    },\n    handleQuery() {\n      this.getHistoryList()\n    },\n    resetQuery() {\n      this.resetForm('queryForm')\n      this.handleQuery()\n    },\n    getHistoryList() {\n      list(this.queryParams).then(response => {\n        this.historyNoticeList = response.data.records\n        this.hTotal = response.data.total\n      })\n    },\n    downloadFile(attachmentId) {\n      if (attachmentId) {\n        download(attachmentId)\n      } else {\n        this.msgWarning('暂无附件！')\n      }\n    }\n  },\n  watch: {\n    notSpanNum(newVal) {\n\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.aaClass {\n  :hover {\n    color: darkgreen\n  }\n  display: block;\n  /*  padding-top: 1vh;\n    padding-bottom: 1vh;*/\n  //margin-left: 2vh;\n  color: #666;\n  .item{\n    position: absolute;\n    z-index: 1;\n    /deep/ .el-badge__content.is-fixed{\n      //top:12px !important;\n      position: relative;\n      right: 0;\n      transform: inherit;\n      top: -42px;\n    }\n  }\n\n  .titleClass {\n    word-break: break-all;\n    white-space: nowrap;\n    margin: 0;\n    display: block;\n    color: #666;\n    font-size: 14px;\n    overflow: hidden;\n    width: 235px;\n    text-overflow: ellipsis;\n    font-family: \"Light_0\" !important;\n  }\n\n  /*  .titleClass:hover {\n      margin: 0;\n      display: block;\n      color: rgba(12, 194, 131, 0.8);\n      !*margin-left: 13px !important;*!\n      font-size: 14px;\n      font-family: \"Light_0\" !important;\n    }*/\n\n  .timeClass {\n    margin: 0;\n    font-size: 12px;\n    color: #c1cad4;\n    width: 160px;\n  }\n}\n.txtTitle {\n  list-style-type: none;\n  margin: 0;\n  padding:0;\n}\n.titleClass {\n  word-break: break-all;\n  white-space: nowrap;\n  margin: 0;\n  display: block;\n  color: #666;\n  font-size: 14px;\n  font-family: \"Light_0\" !important;\n  padding-left: 47px;\n}\n\n/*  .tabActive {\n    float: left;\n !*   margin-right: 25px;*!\n    cursor: pointer;\n    color: #359076;\n  }\n\n  .noActive {\n    float: left;\n   !* margin-right: 25px;*!\n    color: #b1b1b1;\n    cursor: pointer;\n  }*/\n\n/*设置滚动条样式*/\n/deep/ .work_table .el-table__body-wrapper::-webkit-scrollbar, /deep/ .todo .el-table::-webkit-scrollbar {\n  width: 4px;\n}\n\n/deep/ .work_table .el-table__body-wrapper::-webkit-scrollbar-thumb, /deep/ .todo .el-table::-webkit-scrollbar-thumb {\n  border-radius: 10px;\n  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);\n  background: rgba(0, 0, 0, 0.2);\n}\n\n/deep/ .work_table .el-table__body-wrapper::-webkit-scrollbar-track, /deep/ .work_table .el-tab-pane::-webkit-scrollbar-track {\n  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);\n  border-radius: 0;\n  background: rgba(0, 0, 0, 0.1);\n}\n\n/deep/ .work_table .el-table__body {\n  width: 100% !important;\n}\n\n/deep/ .work_table .el-table--medium th, /deep/ .work_table.el-table--medium td {\n  color: #000;\n  padding: 0;\n}\n\n/deep/ .work_table .el-table--medium th:hover, /deep/ .work_table.el-table--medium td:hover {\n  /*    color: #11ba6b;*/\n  /*  background: transparent;\n    overflow: inherit !important;*/\n}\n/deep/ .work_table .el-table tr{\n  color: rgba(12, 194, 131, 0.8);\n}\n/deep/ .notice_box .cell {\n  white-space: nowrap;\n  height: 42px;\n  line-height: 42px;\n  padding-left: 2px;\n}\n.notices{\n  height: 331px;\n}\n.notice_box{\n  width: 100%;padding:0 12px;\n  max-height:285px;\n  height: 285px;\n}\n</style>\n"], "sourceRoot": "src/components/Index"}]}