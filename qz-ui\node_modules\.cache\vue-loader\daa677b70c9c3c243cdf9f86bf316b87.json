{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\syxmxqInfo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\syxmxqInfo.vue", "mtime": 1706897323690}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IExvYWRpbmcgfSBmcm9tICJlbGVtZW50LXVpIjsKaW1wb3J0IHsKICByZXNldENlbGxzLAogIGNyZWF0ZVRhYmxlLAogIG1lcmdlQ2VsbHMsCiAgZWRpdENlbGxzLAogIGdldENlbGxzLAp9IGZyb20gIkAvYXBpL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvc3ltcGsvc3ltcEluZm8iOwppbXBvcnQgeyBnZXRUYWJsZSB9IGZyb20gIkAvYXBpL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvc3ltcGsvc3ltcGsiOwppbXBvcnQgeyBnZXRCd1NlbGVjdCwgZ2V0WnhtU2VsZWN0IH0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYnpnbC9zeXhtIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBwcm9wczogewogICAgbXBEYXRhOiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgIH0sCiAgICBteERhdGE6IHsKICAgICAgdHlwZTogQXJyYXksCiAgICB9LAogIH0sCiAgbmFtZTogInN5eG14cUluZm8iLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvL+WIneWni+ihqOagvOeahOihjOaVsCDliJfmlbAKICAgICAgaHM6ICIiLAogICAgICBsczogIiIsCiAgICAgIC8v5Yid5aeL5ZCI5bm26KGM5pWwIOWIl+aVsAogICAgICBhZGRoczogIiIsCiAgICAgIGFkZGxzOiAiIiwKICAgICAgbnJseDogIiIsCiAgICAgIC8v5LiA6KGM55qE5pWw5o2uCiAgICAgIGNlbGxEYXRhOiAiIiwKICAgICAgLy/pgInkuK3lkIjlubbooYzjgIHliJfnmoR0cgogICAgICBjaGFuZ2VUcjogIiIsCiAgICAgIC8v5p+l6K+i5p2h5Lu2CiAgICAgIHBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIG1waWQ6ICIiLAogICAgICAgIHp5OiAiIiwKICAgICAgICBzYmx4Ym06ICIiLAogICAgICAgIHp4bUlkOiIiLAogICAgICB9LAogICAgICBsb2FkaW5nOiBudWxsLCAvL+mBrue9qeWxggogICAgICB0ZFdpZHRoOiAwLCAvL+S4gOS4quWNleWFg+agvOaJgOWNoOWuveW6pgogICAgICB0ZE1hcDogbmV3IE1hcCgpLCAvL+eUqOS6juWtmOaUvuiiq+WQiOW5tuaIluaLhuWIhueahOWNleWFg+agvO+8iGtleTrlvZPliY3ngrnlh7vnmoTljZXlhYPmoLwsdmFsdWU66KKr5aSE55CG6L+H55qE5Y2V5YWD5qC85pWw57uE77yJCiAgICAgIHRhYmxlRGF0YTogdGhpcy5teERhdGEsIC8v6KGo5qC85pWw5o2uCiAgICAgIHNibHhibTogdW5kZWZpbmVkLCAvL+iuvuWkh+exu+Wei+e8lueggQoKICAgICAgdGl0bGU6ICLljZXlhYPmoLzlsZ7mgKflrprkuYkiLAogICAgICBmb3JtOiB7CiAgICAgICAgb2JqSWQ6IHVuZGVmaW5lZCwKICAgICAgICByZWFkb25seTogdW5kZWZpbmVkLAogICAgICAgIG5ybHg6IHVuZGVmaW5lZCwKICAgICAgICBid0lkOiB1bmRlZmluZWQsCiAgICAgICAgenhtSWQ6IHVuZGVmaW5lZCwKICAgICAgICBucmJzOnVuZGVmaW5lZCwKICAgICAgfSwKICAgICAgc2hvdzogZmFsc2UsCiAgICAgIGJ3TGlzdDogW10sIC8v6K+V6aqM6YOo5L2NCiAgICAgIHp4bW1EYXRhTGlzdDogW10sIC8v5a2Q6aG555uu5omA5pyJ57uT5p6c5pWw5o2uCiAgICAgIHp4bW1jTGlzdDogW10sIC8v5a2Q6aG555uuCiAgICAgIGhpZGRlOiBmYWxzZSwKICAgICAgaGlkZGVidzogZmFsc2UsCiAgICAgIGhpZGRlenhtOiBmYWxzZSwKICAgICAgbnJseExpc3Q6IFsKICAgICAgICB7IGxhYmVsOiAi6Z2Z5oCB5paH5pysIiwgdmFsdWU6ICLpnZnmgIHmlofmnKwiIH0sCiAgICAgICAgeyBsYWJlbDogIuivlemqjOWtkOmhueebriIsIHZhbHVlOiAi6K+V6aqM5a2Q6aG555uuIiB9LAogICAgICAgIHsgbGFiZWw6ICLor5Xpqozpg6jkvY0iLCB2YWx1ZTogIuivlemqjOmDqOS9jSIgfSwKICAgICAgICB7IGxhYmVsOiAi6K+V6aqM5pWw5o2uIiwgdmFsdWU6ICLor5XpqozmlbDmja4iIH0sCiAgICAgIF0sCiAgICAgIHJlYWRvbmx5TGlzdDogWwogICAgICAgIHsgbGFiZWw6ICLmmK8iLCB2YWx1ZTogIlkiIH0sCiAgICAgICAgeyBsYWJlbDogIuWQpiIsIHZhbHVlOiAiTiIgfSwKICAgICAgXSwKICAgIH07CiAgfSwKCiAgbW91bnRlZCgpIHsKICAgIC8v6I635Y+W6KGo5qC85Yid5aeL6KGM5pWw5ZKM5YiX5pWwCiAgICB0aGlzLmluaXRUYWJsZURhdGEoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8v6I635Y+W6ZOt54mM5YaF5a655pWw5o2uCiAgICBpbml0VGFibGVEYXRhKCkgewogICAgICAvL+WIneWni+WMlumBrue9qeWxggogICAgICB0aGlzLmxvYWRpbmcgPSBMb2FkaW5nLnNlcnZpY2UoewogICAgICAgIHRleHQ6ICLliqDovb3kuK3vvIzor7fnqI3lkI4uLi4iLAogICAgICAgIGJhY2tncm91bmQ6ICJyZ2JhKDEwOSwxMDYsMTA2LDAuMzUpIiwKICAgICAgfSk7CiAgICAgIHRoaXMuaHMgPSB0eXBlb2YgKHRoaXMubXBEYXRhLkFIcykgIT0gJ3VuZGVmaW5lZCc/dGhpcy5tcERhdGEuQUhzOnRoaXMubXBEYXRhLmFIczsKICAgICAgdGhpcy5scyA9IHR5cGVvZiAodGhpcy5tcERhdGEuQUxzKSAhPSAndW5kZWZpbmVkJz90aGlzLm1wRGF0YS5BTHM6dGhpcy5tcERhdGEuYUxzOwogICAgICB0aGlzLnNibHhibSA9IHRoaXMubXBEYXRhLnNibHhibTsKICAgICAgY29uc29sZS5sb2coIi0taHMtLSIgKyB0aGlzLmhzKTsKICAgICAgY29uc29sZS5sb2coIisrLW1wRGF0YS0tIiArIHRoaXMuc2JseGJtKTsKCiAgICAgIC8v5pu05paw6L6T5YWl5qGG55qE5YC8CiAgICAgIHRoaXMudXBkYXRlSW5wdXRWYWx1ZShbImhzIiwgImxzIl0pOwogICAgICB0aGlzLnByb2Nlc3NUYWJsZSgpOwogICAgICB0aGlzLmxvYWRpbmcuY2xvc2UoKTsgLy/lhbPpl63pga7nvanlsYIKICAgIH0sCgogICAgc2VsZWN0enhtdmFsdWUodmFsKSB7CiAgICAgIGxldCBvYmo9e307CiAgICAgIG9iaj10aGlzLnp4bW1jTGlzdC5maW5kKChpdGVtKT0+ewogICAgICAgIHJldHVybiBpdGVtLnZhbHVlPT09dmFsOwogICAgICB9KQogICAgICB0aGlzLmZvcm0ubnJicz1vYmoubGFiZXI7CiAgICB9LAoKICAgIHNlbGVjdGJ3dmFsdWUodmFsKXsKICAgICAgbGV0IG9iaj17fTsKICAgICAgb2JqPXRoaXMuYndMaXN0LmZpbmQoKGl0ZW0pPT57CiAgICAgICAgcmV0dXJuIGl0ZW0udmFsdWU9PT12YWw7CiAgICAgIH0pCiAgICAgICAgdGhpcy5mb3JtLm5yYnM9b2JqLmxhYmVyOwogICAgfSwKCiAgICBzZWxlY3R2YWx1ZSh2YWwpIHsKICAgICAgY29uc3QgeyB2YWx1ZSwgbGFiZWwgfSA9IHZhbDsKICAgICAgaWYgKGxhYmVsID09ICLpnZnmgIHmlofmnKwiKSB7CiAgICAgICAgdGhpcy5oaWRkZSA9IHRydWU7CiAgICAgICAgdGhpcy5oaWRkZWJ3PWZhbHNlOwogICAgICAgIHRoaXMuaGlkZGV6eG0gPSBmYWxzZTsKICAgICAgICAgdGhpcy5mb3JtLm5ybHg9IumdmeaAgeaWh+acrCIKICAgICAgfQogICAgICBpZiAobGFiZWwgPT0gIuivlemqjOWtkOmhueebriIpIHsKICAgICAgICB0aGlzLmhpZGRlenhtID0gdHJ1ZTsKICAgICAgICB0aGlzLmhpZGRlYnc9ZmFsc2U7CiAgICAgICAgIHRoaXMuaGlkZGU9ZmFsc2U7CiAgICAgICAgIHRoaXMuZm9ybS5ucmx4PSLor5XpqozlrZDpobnnm64iCiAgICAgIH0KICAgICAgaWYgKGxhYmVsID09ICLor5Xpqozpg6jkvY0iKSB7CiAgICAgICAgdGhpcy5oaWRkZWJ3ID0gdHJ1ZTsKICAgICAgICB0aGlzLmhpZGRlenhtPWZhbHNlOwogICAgICAgIHRoaXMuaGlkZGU9ZmFsc2U7CiAgICAgICAgIHRoaXMuZm9ybS5ucmx4PSLor5Xpqozpg6jkvY0iCiAgICAgIH0KICAgICAgwqBpZiAobGFiZWwgPT0gIuivlemqjOaVsOaNriIpIHsKwqAgwqAgwqAgwqAgdGhpcy5oaWRkZSA9IGZhbHNlOwrCoCDCoCDCoCDCoCB0aGlzLmhpZGRlYnc9ZmFsc2U7CsKgIMKgIMKgIMKgIHRoaXMuaGlkZGV6eG0gPSBmYWxzZTsKwqAgwqAgwqAgwqAgdGhpcy5mb3JtLm5ybHg9IuivlemqjOaVsOaNriIKwqAgwqAgwqAgfQoKICAgIH0sCgogICAgLy/ojrflj5bpg6jkvY3kuIvmi4nmoYYKICAgIGdldFN5YncoKSB7CiAgICAgIGdldEJ3U2VsZWN0KHsgc2JseGJtOiB0aGlzLnNibHhibSB9KS50aGVuKChyZXMpID0+IHsKICAgICAgICB0aGlzLmJ3TGlzdCA9IHJlcy5kYXRhOwogICAgICB9KTsKICAgIH0sCgogICAgLy8g6I635Y+W6K+V6aqM5a2Q6aG555uu5LiL5ouJ5qGG5pWw5o2uCiAgICBnZXRTeXp4bSgpIHsKICAgICAgZ2V0WnhtU2VsZWN0KCkudGhlbigocmVzKSA9PiB7CiAgICAgICAgdGhpcy56eG1tY0xpc3QgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAoKICAgIC8v5qC55o2u6KGM5pWw5ZKM5YiX5pWw5Yib5bu66KGo5qC8CiAgICBwcm9jZXNzVGFibGUoKSB7CiAgICAgIHZhciB0Ym9keSA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCJtcHhxX3JpZ2h0Iik7CiAgICAgIGlmICh0Ym9keSAhPSBudWxsKSB7CiAgICAgICAgdGJvZHkuaW5uZXJIVE1MID0gIiI7CiAgICAgICAgbGV0IGhzID0gdGhpcy5oczsKICAgICAgICBsZXQgbHMgPSB0aGlzLmxzOwogICAgICAgIHRoaXMudGRXaWR0aCA9IDEwMCAvIE51bWJlcihscyk7CiAgICAgICAgbGV0IHN0ciA9ICIiOwoKICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGhzOyBpKyspIHsKICAgICAgICAgIGxldCB0ZW1wID0gIjx0cj4iOwogICAgICAgICAgZm9yIChsZXQgaiA9IDA7IGogPCB0aGlzLnRhYmxlRGF0YS5sZW5ndGg7IGorKykgewogICAgICAgICAgICBsZXQgaXRlbSA9IHRoaXMudGFibGVEYXRhW2pdOwogICAgICAgICAgICBsZXQgc2pseCA9IGl0ZW0uc2pseDsgLy/mlbDmja7nsbvlnosKICAgICAgICAgICAgbGV0IG5yID0gIiI7CiAgICAgICAgICAgIGxldCBucmJzID0gaXRlbS5ucmJzID09IG51bGwgPyAiLSIgOiBpdGVtLm5yYnM7CiAgICAgICAgICAgIGlmIChpdGVtLnJvd2luZGV4ID09PSBpLnRvU3RyaW5nKCkpIHsKICAgICAgICAgICAgICB0ZW1wICs9CiAgICAgICAgICAgICAgICAiPHRkIGNsYXNzPSd0ck5hbWUnIGlkPSciICsKICAgICAgICAgICAgICAgIGl0ZW0ub2JqSWQgKwogICAgICAgICAgICAgICAgIicgc3R5bGU9J3dpZHRoOiAiICsKICAgICAgICAgICAgICAgIHRoaXMudGRXaWR0aCAqIGl0ZW0uY29sc3BhbiArCiAgICAgICAgICAgICAgICAiJScgcm93c3Bhbj0nIiArCiAgICAgICAgICAgICAgICBpdGVtLnJvd3NwYW4gKwogICAgICAgICAgICAgICAgIicgY29sc3Bhbj0nIiArCiAgICAgICAgICAgICAgICBpdGVtLmNvbHNwYW4gKwogICAgICAgICAgICAgICAgIic+IiArCiAgICAgICAgICAgICAgICBucmJzICsKICAgICAgICAgICAgICAgICI8L3RkPiI7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICAgIHRlbXAgKz0gIjwvdHI+IjsKICAgICAgICAgIHN0ciArPSB0ZW1wOwogICAgICAgIH0KICAgICAgICB0Ym9keS5pbm5lckhUTUwgPSBzdHI7CiAgICAgICAgLy8gLy/nu5nlvqrnjq/lh7rmnaXnmoTljZXlhYPmoLzliqDkuIrngrnlh7vkuovku7YKICAgICAgICB0aGlzLmFkZENsaWNrRXZlbnQoKTsKICAgICAgfQogICAgfSwKCiAgICAvL+aJi+WKqOWIm+W7uuihqOagvAogICAgY3JlYXRlVGFibGUoKSB7CiAgICAgIC8v5Yid5aeL5YyW6YGu572p5bGCCiAgICAgIHRoaXMubG9hZGluZyA9IExvYWRpbmcuc2VydmljZSh7CiAgICAgICAgdGV4dDogIuWKoOi9veS4re+8jOivt+eojeWQji4uLiIsCiAgICAgICAgYmFja2dyb3VuZDogInJnYmEoMTA5LDEwNiwxMDYsMC4zNSkiLAogICAgICB9KTsKICAgICAgbGV0IHBhcmFtcyA9IEpTT04uc3RyaW5naWZ5KHsKICAgICAgICBvYmpJZDogdGhpcy5tcERhdGEub2JqSWQsIC8v6ZOt54mMaWQKICAgICAgICBhSHM6IE51bWJlcih0aGlzLmhzKSwgLy/ooYzmlbAKICAgICAgICBhTHM6IE51bWJlcih0aGlzLmxzKSwgLy/liJfmlbAKICAgICAgICBsYmJzOiAiQSIsIC8v57G75Yir5qCH6K+G77yM6KGo56S65L+u5pS555qEQeihqOagvAogICAgICB9KTsKICAgICAgY3JlYXRlVGFibGUocGFyYW1zKS50aGVuKChyZXMpID0+IHsKICAgICAgICBpZiAocmVzLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgdGhpcy51cGRhdGVUYWJsZSgpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLmk43kvZzlpLHotKUiKTsKICAgICAgICAgIHRoaXMubG9hZGluZy5jbG9zZSgpOyAvL+WFs+mXremBrue9qeWxggogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy/nu5nlvqrnjq/lh7rmnaXnmoTljZXlhYPmoLzliqDkuIrngrnlh7vkuovku7YKICAgIGFkZENsaWNrRXZlbnQoKSB7CiAgICAgIGxldCB0ckFyciA9IGRvY3VtZW50LmdldEVsZW1lbnRzQnlDbGFzc05hbWUoInRyTmFtZSIpOyAvL+S4jeWPr+e8lui+keeahOWNleWFg+agvAogICAgICBsZXQgaW5wdXRBcnIgPSBkb2N1bWVudC5nZXRFbGVtZW50c0J5Q2xhc3NOYW1lKCJpbnB1dF9jbHMiKTsgLy/lj6/nvJbovpHnmoTljZXlhYPmoLwKICAgICAgbGV0IHRoYXQgPSB0aGlzOwogICAgICBpZiAodHJBcnIgIT0gbnVsbCkgewogICAgICAgIC8v5b6q546v5omA5pyJ55qEdHIKICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRyQXJyLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgICB0ckFycltpXS5vbmNsaWNrID0gZnVuY3Rpb24gKCkgewogICAgICAgICAgICB0aGF0LmNoYW5nZVRyID0gdGhpczsKICAgICAgICAgICAgdGhhdC5hZGRocyA9IHRoYXQuY2hhbmdlVHIucm93U3BhbjsKICAgICAgICAgICAgdGhhdC5hZGRscyA9IHRoYXQuY2hhbmdlVHIuY29sU3BhbjsKICAgICAgICAgICAgdGhhdC5jZWxsRGF0YSA9IHRoYXQuZ2V0Q2VsbEVsZSh0aGF0LmNoYW5nZVRyLmlkKTsKICAgICAgICAgICAgdGhhdC5ucmx4ID0gdGhhdC5jZWxsRGF0YS5ucmx4OwogICAgICAgICAgICBjb25zb2xlLmxvZyh0aGF0LmNlbGxEYXRhKTsKICAgICAgICAgIH07CiAgICAgICAgfQogICAgICB9CiAgICAgIGlmIChpbnB1dEFyciAhPSBudWxsKSB7CiAgICAgICAgLy/lvqrnjq/miYDmnInnmoR0cgogICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgaW5wdXRBcnIubGVuZ3RoOyBpKyspIHsKICAgICAgICAgIGlucHV0QXJyW2ldLm9uY2xpY2sgPSBmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgIHRoYXQuY2hhbmdlVHIgPSB0aGlzOwogICAgICAgICAgICB0aGF0LmFkZGhzID0gdGhhdC5jaGFuZ2VUci5yb3dTcGFuOwogICAgICAgICAgICB0aGF0LmFkZGxzID0gdGhhdC5jaGFuZ2VUci5jb2xTcGFuOwogICAgICAgICAgICB0aGF0Lm5ybHggPSB0aGF0LmNoYW5nZVRyLm5ybHg7CiAgICAgICAgICAgIHRoYXQuY2VsbERhdGEgPSB0aGF0LmdldENlbGxFbGUodGhhdC5jaGFuZ2VUci5pZCk7CiAgICAgICAgICAgIHRoYXQubnJseCA9IHRoYXQuY2VsbERhdGEubnJseDsKICAgICAgICAgICAgY29uc29sZS5sb2codGhhdC5jZWxsRGF0YSk7CiAgICAgICAgICB9OwogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIC8v5ZCI5bm25ouG5YiG5L+d5a2YCiAgICBzYXZlQ2hhbmdlVGFibGUoKSB7CiAgICAgIC8v5Yid5aeL5YyW6YGu572p5bGCCiAgICAgIHRoaXMubG9hZGluZyA9IExvYWRpbmcuc2VydmljZSh7CiAgICAgICAgdGV4dDogIuWKoOi9veS4re+8jOivt+eojeWQji4uLiIsCiAgICAgICAgYmFja2dyb3VuZDogInJnYmEoMTA5LDEwNiwxMDYsMC4zNSkiLAogICAgICB9KTsKCiAgICAgIGxldCBwYXJhbXMgPSBKU09OLnN0cmluZ2lmeSh7CiAgICAgICAgb2JqSWQ6IHRoaXMuY2hhbmdlVHIuaWQsCiAgICAgICAgcm93c3BhbjogdGhpcy5hZGRocywKICAgICAgICBjb2xzcGFuOiB0aGlzLmFkZGxzLAogICAgICB9KTsKICAgICAgLy/lhYjor7fmsYLmjqXlj6PvvIzlpoLmnpzlkI7lj7Dlj6/ku6XmiafooYzlkIjlubbmiJbmi4bliIbvvIzliJnlsIbmnIDmlrDnmoTooajmoLzmlbDmja7or7fmsYLlm57mnaXov5vooYzliY3nq6/lsZXnpLoKICAgICAgbWVyZ2VDZWxscyhwYXJhbXMpLnRoZW4oKHJlcykgPT4gewogICAgICAgIGlmIChyZXMuY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICB0aGlzLnVwZGF0ZVRhYmxlKCk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuaTjeS9nOWksei0pSIpOwogICAgICAgICAgdGhpcy5sb2FkaW5nLmNsb3NlKCk7IC8v5YWz6Zet6YGu572p5bGCCiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvL+WkhOeQhuWQiOW5tuaIluaLhuWIhgogICAgcHJvY2Vzc1RyKGlkcykgewogICAgICAvL+eCueWHu+eahOWNleWFg+agvGlkCiAgICAgIGxldCBjbGlja0lkID0gdGhpcy5jaGFuZ2VUci5pZDsKICAgICAgbGV0IGFycjEgPSBbXTsgLy/pnIDopoHph43mlrDorr7nva5tYXDnmoTmlbDnu4QKICAgICAgLy/lpoLmnpzkuYvliY3lt7Lnu4/lpITnkIbov4for6XljZXlhYPmoLws5YiZ5YWI5bCG5YW26L+Y5Y6fCiAgICAgIGlmICh0aGlzLnRkTWFwLmhhcyhjbGlja0lkKSkgewogICAgICAgIHRoaXMudGRNYXAuZ2V0KGNsaWNrSWQpLmZvckVhY2goKGl0ZW0pID0+IHsKICAgICAgICAgIGlmIChpdGVtICE9IG51bGwpIHsKICAgICAgICAgICAgdGhpcy5yZXNldENlbGwoaXRlbSk7CiAgICAgICAgICAgIGl0ZW0uc3R5bGUuZGlzcGxheSA9ICJ0YWJsZS1jZWxsIjsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgICAvL+aTjeS9nOWujOWQjuWwhuaVsOaNruS7jm1hcOS4reWIoOmZpAogICAgICAgIHRoaXMudGRNYXAuZGVsZXRlKGNsaWNrSWQpOwogICAgICB9CiAgICAgIGxldCBwcm9jZXNzRWxlID0gW107IC8v6KKr5aSE55CG55qE5YWD57SgCgogICAgICAvL+eOsOWwhui/nuW4puWPl+W9seWTjeeahOWNleWFg+agvOi/mOWOn++8jOWGjei/m+ihjOmakOiXj+WkhOeQhgogICAgICBpZiAoaWRzLmxlbmd0aCA+IDApIHsKICAgICAgICAvL+aJp+ihjOi/mOWOnwogICAgICAgIGlkcy5mb3JFYWNoKChpZDEpID0+IHsKICAgICAgICAgIGxldCBlbGUgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChpZDEpOwogICAgICAgICAgLy/lpoLmnpzmraTmrKHlpITnkIbnmoTljZXlhYPmoLzkuK3mnInlt7Lnu4/ooqvlpITnkIbov4fnmoTvvIzlhYjlsIblhbbov5jljp8KICAgICAgICAgIGlmICh0aGlzLnRkTWFwLmhhcyhpZDEpKSB7CiAgICAgICAgICAgIHRoaXMudGRNYXAuZ2V0KGlkMSkuZm9yRWFjaCgoaXRlbSkgPT4gewogICAgICAgICAgICAgIHRoaXMucmVzZXRDZWxsKGl0ZW0pOwogICAgICAgICAgICAgIGl0ZW0uc3R5bGUuZGlzcGxheSA9ICJ0YWJsZS1jZWxsIjsKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIC8v5pON5L2c5a6M5ZCO5bCG5pWw5o2u5LuObWFw5Lit5Yig6ZmkCiAgICAgICAgICAgIHRoaXMudGRNYXAuZGVsZXRlKGlkMSk7CiAgICAgICAgICB9CiAgICAgICAgICAvL+WkhOeQhuiiq+i/nuW4pueahOW3sue7j+WQiOW5tui/h+eahOWNleWFg+agvAogICAgICAgICAgaWYgKGVsZSkgewogICAgICAgICAgICBsZXQgY2xhc3NOYW1lID0gZWxlLmNsYXNzTmFtZTsKICAgICAgICAgICAgaWYgKHRoaXMudGRNYXAuaGFzKGNsYXNzTmFtZSkpIHsKICAgICAgICAgICAgICBsZXQgbWVyZ2VDZWxsID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoY2xhc3NOYW1lKTsgLy/ooqvov57luKbnmoTlt7Lnu4/lkIjlubbov4fnmoRjZWxsCiAgICAgICAgICAgICAgaWYgKG1lcmdlQ2VsbCkgewogICAgICAgICAgICAgICAgdGhpcy5yZXNldENlbGwobWVyZ2VDZWxsKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgdGhpcy50ZE1hcC5nZXQoY2xhc3NOYW1lKS5mb3JFYWNoKChpdGVtKSA9PiB7CiAgICAgICAgICAgICAgICAvL+mcgOimgeaKiuatpOasoeimgemakOiXj+eahOWNleWFg+agvOaOkumZpOaOie+8jOS4jeeEtumakOiXj+WujOS4i+asoeW+queOr+WPiOS8muaUvuWHuuadpQogICAgICAgICAgICAgICAgaWYgKCFpZHMuaW5jbHVkZXMoaXRlbSkpIHsKICAgICAgICAgICAgICAgICAgaXRlbS5zdHlsZS5kaXNwbGF5ID0gInRhYmxlLWNlbGwiOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgYXJyMS5wdXNoKGl0ZW0pOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIC8v5aSE55CG5a6M5oiQ5ZCO77yM5pu05pawbWFw5Lit55qE5YC877yM5bCG5aSE55CG6L+H55qE5o6S6Zmk5o6JCiAgICAgICAgICAgICAgaWYgKGFycjEubGVuZ3RoID4gMCkgewogICAgICAgICAgICAgICAgdGhpcy50ZE1hcC5zZXQoY2xhc3NOYW1lLCBhcnIxKTsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgLy/mk43kvZzlrozlkI7lsIbmlbDmja7ku45tYXDkuK3liKDpmaQKICAgICAgICAgICAgICAgIHRoaXMudGRNYXAuZGVsZXRlKGNsYXNzTmFtZSk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgICAgLy/miafooYzpmpDol48KICAgICAgICBpZHMuZm9yRWFjaCgoaWQpID0+IHsKICAgICAgICAgIGxldCBlbGUgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChpZCk7CiAgICAgICAgICAvL+WwhuWkmuS9meeahOWNleWFg+agvOmakOiXjwogICAgICAgICAgaWYgKGVsZSkgewogICAgICAgICAgICBwcm9jZXNzRWxlLnB1c2goZWxlKTsgLy/mt7vliqDmlbDmja7kv53lrZjliLBtYXDkuK0KCiAgICAgICAgICAgIGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKGlkKS5zdHlsZS5kaXNwbGF5ID0gIm5vbmUiOwogICAgICAgICAgICAvL+WwhmNsYXNzTmFtZeiuvue9rue7meiiq+aTjeS9nOeahOWNleWFg+agvO+8jOaWueS+v+S4i+asoeaciei/nuW4puaTjeS9nOaXtuWvueWNleWFg+agvOi/m+ihjOWkhOeQhgogICAgICAgICAgICBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChpZCkuY2xhc3NOYW1lID0gY2xpY2tJZDsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgICAvL+mHjeaWsOiuvue9rm1hcOS4reeahOWAvAogICAgICAgIHRoaXMudGRNYXAuc2V0KGNsaWNrSWQsIHByb2Nlc3NFbGUpOwogICAgICB9CiAgICB9LAogICAgLy/lj5bmtojmm7TmlLnnmoTlkIjlubbooYzjgIHliJfmlbAKICAgIGNsZWFyQ2hhbmdlVGFibGUoKSB7fSwKCiAgICAvL+WFs+mXreW8ueeqlwogICAgZ2V0SW5zdGVyQ2xvc2UoKSB7CiAgICAgIHRoaXMuc2hvdyA9IGZhbHNlOwogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgb2JqSWQ6IHVuZGVmaW5lZCwKICAgICAgICByZWFkb25seTogdW5kZWZpbmVkLAogICAgICAgIG5ybHg6IHVuZGVmaW5lZCwKICAgICAgICBucmJzOiB1bmRlZmluZWQsCiAgICAgICAgenhtSWQ6dW5kZWZpbmVkLAogICAgICAgIGJ3SWQ6dW5kZWZpbmVkLAogICAgICB9OwogICAgfSwKCiAgICAvL+i/m+ihjOWQiOW5tuaIluaLhuWIhuaTjeS9nAogICAgbWVyZ2VUYWJsZShocywgbHMsIGFkZGgsIGFkZGwpIHsKICAgICAgaWYgKGhzID09PSAxKSB7CiAgICAgICAgLy/lkIjlubbliJcKICAgICAgICBpZiAobHMgPj0gMSkgewogICAgICAgICAgdGhpcy5tZXJnZUNlbGxzKGFkZGgsIGFkZGwsIGhzLCBscyk7CiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIGlmIChocyA+IDEpIHsKICAgICAgICAgIC8v5aSa6KGMCiAgICAgICAgICAvL+WQiOW5tuihjAogICAgICAgICAgaWYgKGxzID09PSAxKSB7CiAgICAgICAgICAgIHRoaXMubWVyZ2VSb3dzKGFkZGgsIGFkZGwsIGhzLCBscyk7CiAgICAgICAgICB9IGVsc2UgaWYgKGxzID4gMSkgewogICAgICAgICAgICAvL+WQiOW5tuWkmuihjOWkmuWIlwogICAgICAgICAgICB0aGlzLm1lcmdlUm93c0FuZENlbGxzKGFkZGgsIGFkZGwsIGhzLCBscyk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICAgIC8v6KaB5ZCI5bm255qE5Y2V5YWD5qC86L+b6KGM5ZCI5bm2CiAgICAgIHRoaXMuY2hhbmdlVHIuc3R5bGUud2lkdGggPSB0aGlzLnRkV2lkdGggKiBscyArICIlIjsgLy/orr7nva7lkIjlubblkI7nmoTljZXlhYPmoLzlrr3luqYKICAgICAgdGhpcy5jaGFuZ2VUci5yb3dTcGFuID0gdGhpcy5hZGRoczsKICAgICAgdGhpcy5jaGFuZ2VUci5jb2xTcGFuID0gdGhpcy5hZGRsczsKICAgIH0sCiAgICAvKioKICAgICAqIOesrOS4gOenjeaDheWGte+8jOWQiOW5tuWIl++8iOS4gOihjOWkmuWIl++8iQogICAgICogQHBhcmFtIGgg5b2T5YmN5YWD57Sg5omA5Zyo6KGMCiAgICAgKiBAcGFyYW0gbCDlvZPliY3lhYPntKDmiYDlnKjliJcKICAgICAqIEBwYXJhbSBocyDopoHlkIjlubbnmoTooYzmlbAKICAgICAqIEBwYXJhbSBscyDopoHlkIjlubbnmoTliJfmlbAKICAgICAqLwogICAgbWVyZ2VDZWxscyhoLCBsLCBocywgbHMpIHsKICAgICAgbGV0IHJlbW92ZUlkcyA9IFtdOyAvL+imgeWIoOmZpOeahOWFg+e0oOeahGlk5pWw57uECiAgICAgIGxldCBsc194aCA9IGxzOyAvL+imgeW+queOr+eahOWIl+aVsAogICAgICBpZiAobHMgPiB0aGlzLmxzIC0gbCkgewogICAgICAgIC8v5LiN6IO96LaF6L+H5Ymp5L2Z5Y+v5pON5L2c55qE5YiX5pWwCiAgICAgICAgbHNfeGggPSB0aGlzLmxzIC0gbDsKICAgICAgfQogICAgICBmb3IgKGxldCBpID0gMTsgaSA8IGxzX3hoOyBpKyspIHsKICAgICAgICByZW1vdmVJZHMucHVzaChoICsgInwiICsgKGwgKyBpKSk7CiAgICAgIH0KICAgICAgLy/liKDpmaTlpJrkvZnljZXlhYPmoLwKICAgICAgdGhpcy5wcm9jZXNzVHIocmVtb3ZlSWRzKTsKICAgIH0sCiAgICAvKioKICAgICAqIOesrOS6jOenjeaDheWGte+8jOWQiOW5tuihjO+8iOWkmuihjOS4gOWIl++8iQogICAgICogQHBhcmFtIGgg5b2T5YmN5YWD57Sg5omA5Zyo6KGMCiAgICAgKiBAcGFyYW0gbCDlvZPliY3lhYPntKDmiYDlnKjliJcKICAgICAqIEBwYXJhbSBocyDopoHlkIjlubbnmoTooYzmlbAKICAgICAqIEBwYXJhbSBscyDopoHlkIjlubbnmoTliJfmlbAKICAgICAqLwogICAgbWVyZ2VSb3dzKGgsIGwsIGhzLCBscykgewogICAgICBsZXQgcmVtb3ZlSWRzID0gW107IC8v6KaB5Yig6Zmk55qE5YWD57Sg55qEaWTmlbDnu4QKICAgICAgbGV0IGhzX3hoID0gaHM7IC8v6KaB5b6q546v55qE6KGM5pWwCiAgICAgIGlmIChocyA+IHRoaXMuaHMgLSBoKSB7CiAgICAgICAgLy/kuI3og73otoXov4fliankvZnlj6/mk43kvZznmoTooYzmlbAKICAgICAgICBoc194aCA9IHRoaXMuaHMgLSBoOwogICAgICB9CiAgICAgIGNvbnNvbGUubG9nKCJoc194aCIsIGhzX3hoKTsKICAgICAgZm9yIChsZXQgaSA9IDE7IGkgPCBoc194aDsgaSsrKSB7CiAgICAgICAgcmVtb3ZlSWRzLnB1c2goaCArIGkgKyAifCIgKyBsKTsKICAgICAgfQogICAgICAvL+WIoOmZpOWkmuS9meWNleWFg+agvAogICAgICB0aGlzLnByb2Nlc3NUcihyZW1vdmVJZHMpOwogICAgfSwKICAgIC8qKgogICAgICog56ys5LiJ56eN5oOF5Ya177yM5ZCI5bm25aSa6KGM5aSa5YiXCiAgICAgKiBAcGFyYW0gaCDlvZPliY3lhYPntKDmiYDlnKjooYwKICAgICAqIEBwYXJhbSBsIOW9k+WJjeWFg+e0oOaJgOWcqOWIlwogICAgICogQHBhcmFtIGhzIOimgeWQiOW5tueahOihjOaVsAogICAgICogQHBhcmFtIGxzIOimgeWQiOW5tueahOWIl+aVsAogICAgICovCiAgICBtZXJnZVJvd3NBbmRDZWxscyhoLCBsLCBocywgbHMpIHsKICAgICAgbGV0IHJlbW92ZUlkcyA9IFtdOyAvL+imgeWIoOmZpOeahOWFg+e0oOeahGlk5pWw57uECiAgICAgIGxldCByZW1vdmVJZCA9ICIiOwogICAgICAvL+WFiOW+queOr+ihjO+8iOS7juW9k+WJjeihjOW8gOWni+W+queOr++8iQogICAgICBmb3IgKGxldCBqID0gMDsgaiA8IGhzOyBqKyspIHsKICAgICAgICAvL+W+queOr+WIlwogICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbHM7IGkrKykgewogICAgICAgICAgLy/ku47lvZPliY3liJflvqrnjq8KICAgICAgICAgIHJlbW92ZUlkID0gaCArIGogKyAifCIgKyAobCArIGkpOwogICAgICAgICAgLy/lsIblvZPliY3ljZXlhYPmoLzmjpLpmaTmjokKICAgICAgICAgIGlmIChyZW1vdmVJZCAhPT0gaCArICJ8IiArIGwpIHsKICAgICAgICAgICAgcmVtb3ZlSWRzLnB1c2gocmVtb3ZlSWQpOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgICAvL+WIoOmZpOWkmuS9meWNleWFg+agvAogICAgICB0aGlzLnByb2Nlc3NUcihyZW1vdmVJZHMpOwogICAgfSwKICAgIC8v5pu05paw6L6T5YWl5qGG55qE5YC8CiAgICB1cGRhdGVJbnB1dFZhbHVlKGFycnMpIHsKICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBhcnJzLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgbGV0IGVsZSA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKGFycnNbaV0pOwogICAgICAgIGlmIChlbGUgIT0gbnVsbCAmJiB0eXBlb2YgZWxlICE9ICJ1bmRlZmluZWQiKSB7CiAgICAgICAgICBzd2l0Y2ggKGFycnNbaV0pIHsKICAgICAgICAgICAgY2FzZSAiaHMiOgogICAgICAgICAgICAgIGVsZS52YWx1ZSA9IHRoaXMuaHM7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgImxzIjoKICAgICAgICAgICAgICBlbGUudmFsdWUgPSB0aGlzLmxzOwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlICJhZGRocyI6CiAgICAgICAgICAgICAgZWxlLnZhbHVlID0gdGhpcy5hZGRoczsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSAiYWRkbHMiOgogICAgICAgICAgICAgIGVsZS52YWx1ZSA9IHRoaXMuYWRkbHM7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgLy/ph43nva7ljZXlhYPmoLzlhoXlrrkKICAgIHJlc2V0VGFibGUoKSB7CiAgICAgIC8v5Yid5aeL5YyW6YGu572p5bGCCiAgICAgIHRoaXMubG9hZGluZyA9IExvYWRpbmcuc2VydmljZSh7CiAgICAgICAgdGV4dDogIuWKoOi9veS4re+8jOivt+eojeWQji4uLiIsCiAgICAgICAgYmFja2dyb3VuZDogInJnYmEoMTA5LDEwNiwxMDYsMC4zNSkiLAogICAgICB9KTsKICAgICAgbGV0IG9iaklkID0gdGhpcy5jaGFuZ2VUci5pZDsKICAgICAgbGV0IHBhcmFtcyA9IHRoaXMuZ2V0Q2VsbEVsZShvYmpJZCk7CiAgICAgIHJlc2V0Q2VsbHMocGFyYW1zKS50aGVuKChyZXMpID0+IHsKICAgICAgICBpZiAocmVzLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgdGhpcy51cGRhdGVUYWJsZSgpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLmk43kvZzlpLHotKUiKTsKICAgICAgICB9CiAgICAgICAgdGhpcy5sb2FkaW5nLmNsb3NlKCk7IC8v5YWz6Zet6YGu572p5bGCCiAgICAgIH0pOwogICAgfSwKICAgIC8v5Y2V5YWD5qC85bGe5oCn57yW6L6R5bm25L+d5a2YCiAgICBzYXZlVGRWYWx1ZSgpIHsKICAgICAgdGhpcy5zaG93ID0gdHJ1ZTsKICAgICAgdGhpcy5nZXRTeWJ3KCk7CiAgICAgIHRoaXMuZ2V0U3l6eG0oKTsKICAgICAgLy8gICB0aGlzLmZvcm0ucmVhZG9ubHkgPSB0aGlzLmNlbGxEYXRhLnJlYWRvbmx5OwogICAgICAvLyAgIHRoaXMuZm9ybS5ucmx4ID0gdGhpcy5jZWxsRGF0YS5ucmx4OwogICAgICAvLyAgIHRoaXMuZm9ybS5ucmJzID10aGlzLmNlbGxEYXRhLm5yYnM7CiAgICAgIC8vICAgdGhpcy5mb3JtLm9iaklkID10aGlzLmNlbGxEYXRhLm9iaklkOwogICAgICAvL+WIneWni+WMlumBrue9qeWxggogICAgICAvLyB0aGlzLmxvYWRpbmcgPSBMb2FkaW5nLnNlcnZpY2UoewogICAgICAvLyAgIHRleHQ6IuWKoOi9veS4re+8jOivt+eojeWQji4uLiIsCiAgICAgIC8vICAgYmFja2dyb3VuZDoncmdiYSgxMDksMTA2LDEwNiwwLjM1KScsCiAgICAgIC8vIH0pCiAgICAgIC8vIGxldCBvYmpJZCA9IHRoaXMuY2hhbmdlVHIuaWQ7CiAgICAgIC8vIGxldCB2YWwgPSB0aGlzLmNoYW5nZVRyLmdldEVsZW1lbnRzQnlUYWdOYW1lKCJpbnB1dCIpWzBdLnZhbHVlOwogICAgICAvLyBsZXQgcGFyYW1zID0gdGhpcy5nZXRDZWxsRWxlKG9iaklkKTsKICAgICAgLy8gcGFyYW1zLm5yYnMgPSB2YWw7CiAgICAgIC8vIGVkaXRDZWxscyhwYXJhbXMpLnRoZW4ocmVzPT57CiAgICAgIC8vICAgaWYocmVzLmNvZGU9PT0nMDAwMCcpewogICAgICAvLyAgICAgdGhpcy51cGRhdGVUYWJsZSgpOwogICAgICAvLyAgIH1lbHNlewogICAgICAvLyAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5pON5L2c5aSx6LSlJyk7CiAgICAgIC8vICAgICB0aGlzLmxvYWRpbmcgLmNsb3NlKCk7Ly/lhbPpl63pga7nvanlsYIKICAgICAgLy8gICB9CiAgICAgIC8vIH0pCiAgICB9LAoKICAgIC8v5Y2V5YWD5qC85bGe5oCn57yW6L6RCiAgICBhc3luYyBzYXZlUm93KCkgewogICAgICB0cnkgewogICAgICAgIHRoaXMuZm9ybS5vYmpJZD10aGlzLmNoYW5nZVRyLmlkOwogICAgICAgIHRoaXMuZm9ybS5tcGlkPXRoaXMuY2VsbERhdGEubXBpZDsKICAgICAgICBjb25zb2xlLmxvZygiLS1mb3JtLS0iICsgdGhpcy5mb3JtKTsKICAgICAgICBsZXQgeyBjb2RlIH0gPSBhd2FpdCBlZGl0Q2VsbHModGhpcy5mb3JtKTsKICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICB0aGlzLnVwZGF0ZVRhYmxlKCk7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKnyIpOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgIGNvbnNvbGUubG9nKGUpOwogICAgICB9CiAgICAgIHRoaXMuc2hvdyA9IGZhbHNlOwogICAgfSwKCiAgICAvL+mHjee9ruWNleWFg+agvOWxnuaAp++8iOWuveW6pu+8jOWQiOW5tuihjOaVsO+8jOWQiOW5tuWIl+aVsO+8iQogICAgcmVzZXRDZWxsKGVsZSkgewogICAgICBpZiAoZWxlKSB7CiAgICAgICAgZWxlLnN0eWxlLndpZHRoID0gdGhpcy50ZFdpZHRoICsgIiUiOwogICAgICAgIGVsZS5yb3dTcGFuID0gIjEiOwogICAgICAgIGVsZS5jb2xTcGFuID0gIjEiOwogICAgICB9CiAgICB9LAogICAgLy/ovpPlhaXmoYbmoKHpqowKICAgIGNoZWNrSW5wdXQodmFsLCBjaGFuZ2VUeXBlKSB7CiAgICAgIHN3aXRjaCAoY2hhbmdlVHlwZSkgewogICAgICAgIGNhc2UgImhzIjoKICAgICAgICAgIHRoaXMuaHMgPSB2YWw7CiAgICAgICAgICBicmVhazsKICAgICAgICBjYXNlICJscyI6CiAgICAgICAgICB0aGlzLmxzID0gdmFsOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSAiYWRkaHMiOgogICAgICAgICAgdGhpcy5hZGRocyA9IHZhbDsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgImFkZGxzIjoKICAgICAgICAgIHRoaXMuYWRkbHMgPSB2YWw7CiAgICAgICAgICBicmVhazsKICAgICAgICBjYXNlICJucmx4IjoKICAgICAgICAgIHRoaXMubnJseCA9IHZhbDsKICAgICAgICAgIGJyZWFrOwogICAgICB9CiAgICB9LAogICAgLy/ojrflj5bljZXlhYPmoLzmmI7nu4bmlbDmja4KICAgIGdldENlbGxEZXRhaWwoaHMsIGxzKSB7CiAgICAgIGxldCByZXN1bHQgPSBudWxsOwogICAgICB0aGlzLnRhYmxlRGF0YS5mb3JFYWNoKChpdGVtKSA9PiB7CiAgICAgICAgaWYgKAogICAgICAgICAgaXRlbS5yb3dpbmRleCA9PT0gaHMudG9TdHJpbmcoKSAmJgogICAgICAgICAgaXRlbS5jb2xpbmRleCA9PT0gbHMudG9TdHJpbmcoKQogICAgICAgICkgewogICAgICAgICAgcmVzdWx0ID0gaXRlbTsKICAgICAgICAgIGlmIChyZXN1bHQubnJicyA9PSBudWxsKSB7CiAgICAgICAgICAgIHJlc3VsdC5ucmJzID0gIiI7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgICAgcmV0dXJuIHJlc3VsdDsKICAgIH0sCiAgICAvL+iOt+WPluafkOS4quWNleWFg+agvOWvueixoQogICAgZ2V0Q2VsbEVsZShvYmpJZCkgewogICAgICBsZXQgcmVzdWx0ID0gbnVsbDsKICAgICAgdGhpcy50YWJsZURhdGEuZm9yRWFjaCgoaXRlbSkgPT4gewogICAgICAgIGlmIChpdGVtLm9iaklkID09PSBvYmpJZCkgewogICAgICAgICAgcmVzdWx0ID0gaXRlbTsKICAgICAgICB9CiAgICAgIH0pOwogICAgICByZXR1cm4gcmVzdWx0OwogICAgfSwKICAgIC8v6I635Y+W5pyA5paw55qE6KGo5qC85bm26YeN5paw5riy5p+TCiAgICB1cGRhdGVUYWJsZSgpIHsKICAgICAgbGV0IHBhcmFtID0gSlNPTi5zdHJpbmdpZnkoewogICAgICAgIG9ial9pZDogdGhpcy5tcERhdGEub2JqSWQsCiAgICAgICAgbGJiczogIkEiLAogICAgICB9KTsKICAgICAgLy/ojrflj5bmnIDmlrDnmoTooajmoLzmlbDmja4KICAgICAgZ2V0VGFibGUocGFyYW0pLnRoZW4oKHJlczEpID0+IHsKICAgICAgICBpZiAocmVzMS5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIHRoaXMudGFibGVEYXRhID0gcmVzMS5kYXRhOwogICAgICAgICAgLy/moLnmja7mnIDmlrDnmoTooajmoLzmlbDmja7ph43mlrDnlLsKICAgICAgICAgIHRoaXMucHJvY2Vzc1RhYmxlKCk7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MocmVzMS5tc2cpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLml6Dms5Xojrflj5bmm7TmlrDlkI7nmoTooajmoLzmlbDmja7vvIEiKTsKICAgICAgICB9CiAgICAgICAgdGhpcy5sb2FkaW5nLmNsb3NlKCk7IC8v5YWz6Zet6YGu572p5bGCCiAgICAgIH0pOwogICAgfSwKICB9LAp9Owo="}, {"version": 3, "sources": ["syxmxqInfo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6JA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "syxmxqInfo.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk", "sourcesContent": ["<template>\n  <div class=\"syxmxq_info\">\n    <div id=\"syxmxq_left\">\n      <ul class=\"ul1_cont\">\n        <li>表格</li>\n        <li>\n          行：<el-input\n            id=\"hs\"\n            v-model=\"hs\"\n            placeholder=\"\"\n            class=\"inp1\"\n            @input=\"(val) => checkInput(val, 'hs')\"\n          ></el-input>\n        </li>\n        <li>\n          列：<el-input\n            id=\"ls\"\n            v-model=\"ls\"\n            placeholder=\"\"\n            class=\"inp1\"\n            @input=\"(val) => checkInput(val, 'ls')\"\n          ></el-input>\n        </li>\n        <el-button type=\"success\" @click=\"createTable\" class=\"change_btn\"\n          >创建表格</el-button\n        >\n      </ul>\n      <ul class=\"ul2_cont\">\n        <li>单元格操作</li>\n        <li>\n          行跨度：<el-input\n            id=\"addhs\"\n            v-model=\"addhs\"\n            placeholder=\"\"\n            class=\"inp2\"\n            @input=\"(val) => checkInput(val, 'addhs')\"\n          ></el-input>\n        </li>\n        <li>\n          列跨度：<el-input\n            id=\"addls\"\n            v-model=\"addls\"\n            placeholder=\"\"\n            class=\"inp2\"\n            @input=\"(val) => checkInput(val, 'addls')\"\n          ></el-input>\n        </li>\n        <li>\n          单元格类型：<el-input\n            v-model=\"nrlx\"\n            placeholder=\"\"\n            class=\"inp1\"\n            disabled\n            @input=\"(val) => checkInput(val, 'nrlx')\"\n          ></el-input>\n        </li>\n        <el-button type=\"success\" @click=\"saveChangeTable\" class=\"change_btn\"\n          >保存</el-button\n        >\n        <el-button @click=\"clearChangeTable\" class=\"change_btn\">清除</el-button>\n      </ul>\n      <ul class=\"ul3_cont\">\n        <el-button type=\"warning\" @click=\"saveTdValue\">编辑单元格</el-button>\n        <el-button type=\"warning\" @click=\"resetTable\">重置单元格</el-button>\n      </ul>\n    </div>\n    <table id=\"mpxq_right\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\"></table>\n\n    <el-dialog\n      :title=\"title\"\n      v-dialogDrag\n      :visible.sync=\"show\"\n      width=\"50%\"\n      append-to-body\n      @close=\"getInsterClose\"\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\">\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验类型：\" prop=\"nrlx\">\n              <el-select\n                placeholder=\"请选择试验类型\"\n                v-model=\"form.nrlx\"\n                style=\"width: 100%\"\n                @change=\"selectvalue\"\n              >\n                <el-option\n                  v-for=\"item in nrlxList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"{ label: item.label, value: item.value }\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\" v-show=\"hiddezxm\">\n            <el-form-item label=\"关联试验子项目：\" prop=\"zxmId\">\n              <el-select\n                placeholder=\"请选择试验子项目\"\n                v-model=\"form.zxmId\"\n                style=\"width: 100%\"\n                filterable\n                @change=\"selectzxmvalue\"\n              >\n                <el-option\n                ref ='myselected'\n                  v-for=\"item in zxmmcList\"\n                  :key=\"item.value\"\n                  :label=\"item.laber\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\" v-show=\"hiddebw\">\n            <el-form-item label=\"关联试验部位：\" prop=\"bwId\">\n              <el-select\n                placeholder=\"请选择试验部位\"\n                v-model=\"form.bwId\"\n                style=\"width: 100%\"\n                @change=\"selectbwvalue\"\n              >\n                <el-option\n                  v-for=\"item in bwList\"\n                  :key=\"item.value\"\n                  :label=\"item.laber\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\" v-show=\"hidde\">\n            <el-form-item label=\"试验名称：\" prop=\"nrbs\">\n              <el-input\n                placeholder=\"请选择内容类型\"\n                v-model=\"form.nrbs\"\n                style=\"width: 100%\"\n              >\n              </el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"getInsterClose\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"saveRow\">保 存</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { Loading } from \"element-ui\";\nimport {\n  resetCells,\n  createTable,\n  mergeCells,\n  editCells,\n  getCells,\n} from \"@/api/dagangOilfield/bzgl/sympk/sympInfo\";\nimport { getTable } from \"@/api/dagangOilfield/bzgl/sympk/sympk\";\nimport { getBwSelect, getZxmSelect } from \"@/api/dagangOilfield/bzgl/syxm\";\n\nexport default {\n  props: {\n    mpData: {\n      type: Object,\n    },\n    mxData: {\n      type: Array,\n    },\n  },\n  name: \"syxmxqInfo\",\n  data() {\n    return {\n      //初始表格的行数 列数\n      hs: \"\",\n      ls: \"\",\n      //初始合并行数 列数\n      addhs: \"\",\n      addls: \"\",\n      nrlx: \"\",\n      //一行的数据\n      cellData: \"\",\n      //选中合并行、列的tr\n      changeTr: \"\",\n      //查询条件\n      params: {\n        pageNum: 1,\n        pageSize: 10,\n        mpid: \"\",\n        zy: \"\",\n        sblxbm: \"\",\n        zxmId:\"\",\n      },\n      loading: null, //遮罩层\n      tdWidth: 0, //一个单元格所占宽度\n      tdMap: new Map(), //用于存放被合并或拆分的单元格（key:当前点击的单元格,value:被处理过的单元格数组）\n      tableData: this.mxData, //表格数据\n      sblxbm: undefined, //设备类型编码\n\n      title: \"单元格属性定义\",\n      form: {\n        objId: undefined,\n        readonly: undefined,\n        nrlx: undefined,\n        bwId: undefined,\n        zxmId: undefined,\n        nrbs:undefined,\n      },\n      show: false,\n      bwList: [], //试验部位\n      zxmmDataList: [], //子项目所有结果数据\n      zxmmcList: [], //子项目\n      hidde: false,\n      hiddebw: false,\n      hiddezxm: false,\n      nrlxList: [\n        { label: \"静态文本\", value: \"静态文本\" },\n        { label: \"试验子项目\", value: \"试验子项目\" },\n        { label: \"试验部位\", value: \"试验部位\" },\n        { label: \"试验数据\", value: \"试验数据\" },\n      ],\n      readonlyList: [\n        { label: \"是\", value: \"Y\" },\n        { label: \"否\", value: \"N\" },\n      ],\n    };\n  },\n\n  mounted() {\n    //获取表格初始行数和列数\n    this.initTableData();\n  },\n  methods: {\n    //获取铭牌内容数据\n    initTableData() {\n      //初始化遮罩层\n      this.loading = Loading.service({\n        text: \"加载中，请稍后...\",\n        background: \"rgba(109,106,106,0.35)\",\n      });\n      this.hs = typeof (this.mpData.AHs) != 'undefined'?this.mpData.AHs:this.mpData.aHs;\n      this.ls = typeof (this.mpData.ALs) != 'undefined'?this.mpData.ALs:this.mpData.aLs;\n      this.sblxbm = this.mpData.sblxbm;\n      console.log(\"--hs--\" + this.hs);\n      console.log(\"++-mpData--\" + this.sblxbm);\n\n      //更新输入框的值\n      this.updateInputValue([\"hs\", \"ls\"]);\n      this.processTable();\n      this.loading.close(); //关闭遮罩层\n    },\n\n    selectzxmvalue(val) {\n      let obj={};\n      obj=this.zxmmcList.find((item)=>{\n        return item.value===val;\n      })\n      this.form.nrbs=obj.laber;\n    },\n\n    selectbwvalue(val){\n      let obj={};\n      obj=this.bwList.find((item)=>{\n        return item.value===val;\n      })\n        this.form.nrbs=obj.laber;\n    },\n\n    selectvalue(val) {\n      const { value, label } = val;\n      if (label == \"静态文本\") {\n        this.hidde = true;\n        this.hiddebw=false;\n        this.hiddezxm = false;\n         this.form.nrlx=\"静态文本\"\n      }\n      if (label == \"试验子项目\") {\n        this.hiddezxm = true;\n        this.hiddebw=false;\n         this.hidde=false;\n         this.form.nrlx=\"试验子项目\"\n      }\n      if (label == \"试验部位\") {\n        this.hiddebw = true;\n        this.hiddezxm=false;\n        this.hidde=false;\n         this.form.nrlx=\"试验部位\"\n      }\n       if (label == \"试验数据\") {\n        this.hidde = false;\n        this.hiddebw=false;\n        this.hiddezxm = false;\n        this.form.nrlx=\"试验数据\"\n      }\n\n    },\n\n    //获取部位下拉框\n    getSybw() {\n      getBwSelect({ sblxbm: this.sblxbm }).then((res) => {\n        this.bwList = res.data;\n      });\n    },\n\n    // 获取试验子项目下拉框数据\n    getSyzxm() {\n      getZxmSelect().then((res) => {\n        this.zxmmcList = res.data;\n      });\n    },\n\n    //根据行数和列数创建表格\n    processTable() {\n      var tbody = document.getElementById(\"mpxq_right\");\n      if (tbody != null) {\n        tbody.innerHTML = \"\";\n        let hs = this.hs;\n        let ls = this.ls;\n        this.tdWidth = 100 / Number(ls);\n        let str = \"\";\n\n        for (let i = 0; i < hs; i++) {\n          let temp = \"<tr>\";\n          for (let j = 0; j < this.tableData.length; j++) {\n            let item = this.tableData[j];\n            let sjlx = item.sjlx; //数据类型\n            let nr = \"\";\n            let nrbs = item.nrbs == null ? \"-\" : item.nrbs;\n            if (item.rowindex === i.toString()) {\n              temp +=\n                \"<td class='trName' id='\" +\n                item.objId +\n                \"' style='width: \" +\n                this.tdWidth * item.colspan +\n                \"%' rowspan='\" +\n                item.rowspan +\n                \"' colspan='\" +\n                item.colspan +\n                \"'>\" +\n                nrbs +\n                \"</td>\";\n            }\n          }\n          temp += \"</tr>\";\n          str += temp;\n        }\n        tbody.innerHTML = str;\n        // //给循环出来的单元格加上点击事件\n        this.addClickEvent();\n      }\n    },\n\n    //手动创建表格\n    createTable() {\n      //初始化遮罩层\n      this.loading = Loading.service({\n        text: \"加载中，请稍后...\",\n        background: \"rgba(109,106,106,0.35)\",\n      });\n      let params = JSON.stringify({\n        objId: this.mpData.objId, //铭牌id\n        aHs: Number(this.hs), //行数\n        aLs: Number(this.ls), //列数\n        lbbs: \"A\", //类别标识，表示修改的A表格\n      });\n      createTable(params).then((res) => {\n        if (res.code === \"0000\") {\n          this.updateTable();\n        } else {\n          this.$message.error(\"操作失败\");\n          this.loading.close(); //关闭遮罩层\n        }\n      });\n    },\n    //给循环出来的单元格加上点击事件\n    addClickEvent() {\n      let trArr = document.getElementsByClassName(\"trName\"); //不可编辑的单元格\n      let inputArr = document.getElementsByClassName(\"input_cls\"); //可编辑的单元格\n      let that = this;\n      if (trArr != null) {\n        //循环所有的tr\n        for (let i = 0; i < trArr.length; i++) {\n          trArr[i].onclick = function () {\n            that.changeTr = this;\n            that.addhs = that.changeTr.rowSpan;\n            that.addls = that.changeTr.colSpan;\n            that.cellData = that.getCellEle(that.changeTr.id);\n            that.nrlx = that.cellData.nrlx;\n            console.log(that.cellData);\n          };\n        }\n      }\n      if (inputArr != null) {\n        //循环所有的tr\n        for (let i = 0; i < inputArr.length; i++) {\n          inputArr[i].onclick = function () {\n            that.changeTr = this;\n            that.addhs = that.changeTr.rowSpan;\n            that.addls = that.changeTr.colSpan;\n            that.nrlx = that.changeTr.nrlx;\n            that.cellData = that.getCellEle(that.changeTr.id);\n            that.nrlx = that.cellData.nrlx;\n            console.log(that.cellData);\n          };\n        }\n      }\n    },\n    //合并拆分保存\n    saveChangeTable() {\n      //初始化遮罩层\n      this.loading = Loading.service({\n        text: \"加载中，请稍后...\",\n        background: \"rgba(109,106,106,0.35)\",\n      });\n\n      let params = JSON.stringify({\n        objId: this.changeTr.id,\n        rowspan: this.addhs,\n        colspan: this.addls,\n      });\n      //先请求接口，如果后台可以执行合并或拆分，则将最新的表格数据请求回来进行前端展示\n      mergeCells(params).then((res) => {\n        if (res.code === \"0000\") {\n          this.updateTable();\n        } else {\n          this.$message.error(\"操作失败\");\n          this.loading.close(); //关闭遮罩层\n        }\n      });\n    },\n    //处理合并或拆分\n    processTr(ids) {\n      //点击的单元格id\n      let clickId = this.changeTr.id;\n      let arr1 = []; //需要重新设置map的数组\n      //如果之前已经处理过该单元格,则先将其还原\n      if (this.tdMap.has(clickId)) {\n        this.tdMap.get(clickId).forEach((item) => {\n          if (item != null) {\n            this.resetCell(item);\n            item.style.display = \"table-cell\";\n          }\n        });\n        //操作完后将数据从map中删除\n        this.tdMap.delete(clickId);\n      }\n      let processEle = []; //被处理的元素\n\n      //现将连带受影响的单元格还原，再进行隐藏处理\n      if (ids.length > 0) {\n        //执行还原\n        ids.forEach((id1) => {\n          let ele = document.getElementById(id1);\n          //如果此次处理的单元格中有已经被处理过的，先将其还原\n          if (this.tdMap.has(id1)) {\n            this.tdMap.get(id1).forEach((item) => {\n              this.resetCell(item);\n              item.style.display = \"table-cell\";\n            });\n            //操作完后将数据从map中删除\n            this.tdMap.delete(id1);\n          }\n          //处理被连带的已经合并过的单元格\n          if (ele) {\n            let className = ele.className;\n            if (this.tdMap.has(className)) {\n              let mergeCell = document.getElementById(className); //被连带的已经合并过的cell\n              if (mergeCell) {\n                this.resetCell(mergeCell);\n              }\n              this.tdMap.get(className).forEach((item) => {\n                //需要把此次要隐藏的单元格排除掉，不然隐藏完下次循环又会放出来\n                if (!ids.includes(item)) {\n                  item.style.display = \"table-cell\";\n                } else {\n                  arr1.push(item);\n                }\n              });\n              //处理完成后，更新map中的值，将处理过的排除掉\n              if (arr1.length > 0) {\n                this.tdMap.set(className, arr1);\n              } else {\n                //操作完后将数据从map中删除\n                this.tdMap.delete(className);\n              }\n            }\n          }\n        });\n        //执行隐藏\n        ids.forEach((id) => {\n          let ele = document.getElementById(id);\n          //将多余的单元格隐藏\n          if (ele) {\n            processEle.push(ele); //添加数据保存到map中\n\n            document.getElementById(id).style.display = \"none\";\n            //将className设置给被操作的单元格，方便下次有连带操作时对单元格进行处理\n            document.getElementById(id).className = clickId;\n          }\n        });\n        //重新设置map中的值\n        this.tdMap.set(clickId, processEle);\n      }\n    },\n    //取消更改的合并行、列数\n    clearChangeTable() {},\n\n    //关闭弹窗\n    getInsterClose() {\n      this.show = false;\n      this.form = {\n        objId: undefined,\n        readonly: undefined,\n        nrlx: undefined,\n        nrbs: undefined,\n        zxmId:undefined,\n        bwId:undefined,\n      };\n    },\n\n    //进行合并或拆分操作\n    mergeTable(hs, ls, addh, addl) {\n      if (hs === 1) {\n        //合并列\n        if (ls >= 1) {\n          this.mergeCells(addh, addl, hs, ls);\n        }\n      } else {\n        if (hs > 1) {\n          //多行\n          //合并行\n          if (ls === 1) {\n            this.mergeRows(addh, addl, hs, ls);\n          } else if (ls > 1) {\n            //合并多行多列\n            this.mergeRowsAndCells(addh, addl, hs, ls);\n          }\n        }\n      }\n      //要合并的单元格进行合并\n      this.changeTr.style.width = this.tdWidth * ls + \"%\"; //设置合并后的单元格宽度\n      this.changeTr.rowSpan = this.addhs;\n      this.changeTr.colSpan = this.addls;\n    },\n    /**\n     * 第一种情况，合并列（一行多列）\n     * @param h 当前元素所在行\n     * @param l 当前元素所在列\n     * @param hs 要合并的行数\n     * @param ls 要合并的列数\n     */\n    mergeCells(h, l, hs, ls) {\n      let removeIds = []; //要删除的元素的id数组\n      let ls_xh = ls; //要循环的列数\n      if (ls > this.ls - l) {\n        //不能超过剩余可操作的列数\n        ls_xh = this.ls - l;\n      }\n      for (let i = 1; i < ls_xh; i++) {\n        removeIds.push(h + \"|\" + (l + i));\n      }\n      //删除多余单元格\n      this.processTr(removeIds);\n    },\n    /**\n     * 第二种情况，合并行（多行一列）\n     * @param h 当前元素所在行\n     * @param l 当前元素所在列\n     * @param hs 要合并的行数\n     * @param ls 要合并的列数\n     */\n    mergeRows(h, l, hs, ls) {\n      let removeIds = []; //要删除的元素的id数组\n      let hs_xh = hs; //要循环的行数\n      if (hs > this.hs - h) {\n        //不能超过剩余可操作的行数\n        hs_xh = this.hs - h;\n      }\n      console.log(\"hs_xh\", hs_xh);\n      for (let i = 1; i < hs_xh; i++) {\n        removeIds.push(h + i + \"|\" + l);\n      }\n      //删除多余单元格\n      this.processTr(removeIds);\n    },\n    /**\n     * 第三种情况，合并多行多列\n     * @param h 当前元素所在行\n     * @param l 当前元素所在列\n     * @param hs 要合并的行数\n     * @param ls 要合并的列数\n     */\n    mergeRowsAndCells(h, l, hs, ls) {\n      let removeIds = []; //要删除的元素的id数组\n      let removeId = \"\";\n      //先循环行（从当前行开始循环）\n      for (let j = 0; j < hs; j++) {\n        //循环列\n        for (let i = 0; i < ls; i++) {\n          //从当前列循环\n          removeId = h + j + \"|\" + (l + i);\n          //将当前单元格排除掉\n          if (removeId !== h + \"|\" + l) {\n            removeIds.push(removeId);\n          }\n        }\n      }\n      //删除多余单元格\n      this.processTr(removeIds);\n    },\n    //更新输入框的值\n    updateInputValue(arrs) {\n      for (let i = 0; i < arrs.length; i++) {\n        let ele = document.getElementById(arrs[i]);\n        if (ele != null && typeof ele != \"undefined\") {\n          switch (arrs[i]) {\n            case \"hs\":\n              ele.value = this.hs;\n              break;\n            case \"ls\":\n              ele.value = this.ls;\n              break;\n            case \"addhs\":\n              ele.value = this.addhs;\n              break;\n            case \"addls\":\n              ele.value = this.addls;\n              break;\n          }\n        }\n      }\n    },\n    //重置单元格内容\n    resetTable() {\n      //初始化遮罩层\n      this.loading = Loading.service({\n        text: \"加载中，请稍后...\",\n        background: \"rgba(109,106,106,0.35)\",\n      });\n      let objId = this.changeTr.id;\n      let params = this.getCellEle(objId);\n      resetCells(params).then((res) => {\n        if (res.code === \"0000\") {\n          this.updateTable();\n        } else {\n          this.$message.error(\"操作失败\");\n        }\n        this.loading.close(); //关闭遮罩层\n      });\n    },\n    //单元格属性编辑并保存\n    saveTdValue() {\n      this.show = true;\n      this.getSybw();\n      this.getSyzxm();\n      //   this.form.readonly = this.cellData.readonly;\n      //   this.form.nrlx = this.cellData.nrlx;\n      //   this.form.nrbs =this.cellData.nrbs;\n      //   this.form.objId =this.cellData.objId;\n      //初始化遮罩层\n      // this.loading = Loading.service({\n      //   text:\"加载中，请稍后...\",\n      //   background:'rgba(109,106,106,0.35)',\n      // })\n      // let objId = this.changeTr.id;\n      // let val = this.changeTr.getElementsByTagName(\"input\")[0].value;\n      // let params = this.getCellEle(objId);\n      // params.nrbs = val;\n      // editCells(params).then(res=>{\n      //   if(res.code==='0000'){\n      //     this.updateTable();\n      //   }else{\n      //     this.$message.error('操作失败');\n      //     this.loading .close();//关闭遮罩层\n      //   }\n      // })\n    },\n\n    //单元格属性编辑\n    async saveRow() {\n      try {\n        this.form.objId=this.changeTr.id;\n        this.form.mpid=this.cellData.mpid;\n        console.log(\"--form--\" + this.form);\n        let { code } = await editCells(this.form);\n        if (code === \"0000\") {\n          this.updateTable();\n          this.$message.success(\"操作成功\");\n        }\n      } catch (e) {\n        console.log(e);\n      }\n      this.show = false;\n    },\n\n    //重置单元格属性（宽度，合并行数，合并列数）\n    resetCell(ele) {\n      if (ele) {\n        ele.style.width = this.tdWidth + \"%\";\n        ele.rowSpan = \"1\";\n        ele.colSpan = \"1\";\n      }\n    },\n    //输入框校验\n    checkInput(val, changeType) {\n      switch (changeType) {\n        case \"hs\":\n          this.hs = val;\n          break;\n        case \"ls\":\n          this.ls = val;\n          break;\n        case \"addhs\":\n          this.addhs = val;\n          break;\n        case \"addls\":\n          this.addls = val;\n          break;\n        case \"nrlx\":\n          this.nrlx = val;\n          break;\n      }\n    },\n    //获取单元格明细数据\n    getCellDetail(hs, ls) {\n      let result = null;\n      this.tableData.forEach((item) => {\n        if (\n          item.rowindex === hs.toString() &&\n          item.colindex === ls.toString()\n        ) {\n          result = item;\n          if (result.nrbs == null) {\n            result.nrbs = \"\";\n          }\n        }\n      });\n      return result;\n    },\n    //获取某个单元格对象\n    getCellEle(objId) {\n      let result = null;\n      this.tableData.forEach((item) => {\n        if (item.objId === objId) {\n          result = item;\n        }\n      });\n      return result;\n    },\n    //获取最新的表格并重新渲染\n    updateTable() {\n      let param = JSON.stringify({\n        obj_id: this.mpData.objId,\n        lbbs: \"A\",\n      });\n      //获取最新的表格数据\n      getTable(param).then((res1) => {\n        if (res1.code === \"0000\") {\n          this.tableData = res1.data;\n          //根据最新的表格数据重新画\n          this.processTable();\n          this.$message.success(res1.msg);\n        } else {\n          this.$message.error(\"无法获取更新后的表格数据！\");\n        }\n        this.loading.close(); //关闭遮罩层\n      });\n    },\n  },\n};\n</script>\n\n\n<style scoped lang=\"scss\">\n.syxmxq_info {\n  display: flex;\n}\n#syxmxq_left {\n  margin-right: 20px;\n  ul {\n    list-style-type: none;\n    margin: 0;\n    padding: 8px;\n  }\n  border: 1px solid #0cc283;\n  width: 28%;\n  li:nth-child(1) {\n    font-weight: 700;\n  }\n  li {\n    line-height: 48px;\n    padding-left: 8px;\n    .el-input {\n      width: 70%;\n    }\n  }\n}\n.change_btn {\n  margin-top: 10px !important;\n  height: 36px !important;\n}\n.change_btn:nth-child(1) {\n  margin-left: 29%;\n}\n.change_btn:nth-child(2) {\n  margin-left: 20%;\n}\n#mpxq_right {\n  width: 72%;\n  height: 180px;\n  border: 1px solid #000;\n}\n</style>\n<style>\n#mpxq_right td {\n  border: 1px solid #000;\n  height: 35px;\n  line-height: 35px;\n  text-align: center;\n}\n#mpxq_right tr {\n  height: 35px;\n}\n#mpxq_right .atc {\n  background-color: #11ba6d;\n}\n#mpxq_right .input_cls {\n  text-align: center;\n  border: none;\n  width: 99%;\n  height: 99%;\n}\n</style>\n"]}]}