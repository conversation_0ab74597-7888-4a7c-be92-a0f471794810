{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\bddzcz\\dzczml.vue?vue&type=style&index=0&id=f7269804&scoped=true&lang=css&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\bddzcz\\dzczml.vue", "mtime": 1748606190948}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5jYXJkLXN0eWxlIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZTBlZmYyOwogIGxpbmUtaGVpZ2h0OiAzdmg7CiAgYm9yZGVyLXJhZGl1czogM3B4OwogIGZvbnQtd2VpZ2h0OiA4MDA7CiAgcGFkZGluZy1sZWZ0OiAxdnc7CiAgbWFyZ2luLWJvdHRvbTogM3ZoOwp9CgovKuaOp+WItmlucHV06L6T5YWl5qGG6L655qGG5piv5ZCm5pi+56S6Ki8KLmVsSW5wdXQgPj4+IC5lbC1pbnB1dF9faW5uZXIgewogIGJvcmRlcjogMDsKfQoKLmJveC1jYXJkIHsKICBtYXJnaW4tYm90dG9tOiAydmggIWltcG9ydGFudDsKfQo="}, {"version": 3, "sources": ["dzczml.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6tEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "dzczml.vue", "sourceRoot": "src/views/dagangOilfield/czpgl/bddzcz", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n      @handleEvent=\"handleEvent\"\n      @handleReset=\"filterReset\"\n    />\n    <div>\n      <el-white class=\"button-group\">\n        <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n          <el-col\n            style=\"\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n          \"\n          >\n            <div>\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-plus\"\n                @click=\"getInster\"\n                v-hasPermi=\"['dzczml:button:add']\"\n                >新增\n              </el-button>\n              <!-- <el-button type=\"primary\" @click=\"exportFun\"\n              >导出\n              </el-button> -->\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-download\"\n                @click=\"exportWord\"\n                >导出</el-button\n              >\n            </div>\n          </el-col>\n        </el-row>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"selectChange\"\n          height=\"66vh\"\n          v-loading=\"loading\"\n        >\n          <el-table-column\n            slot=\"table_eight\"\n            align=\"center\"\n            fixed=\"right\"\n            style=\"display: block\"\n            label=\"操作\"\n            width=\"160\"\n            :resizable=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                @click=\"getDetails(scope.row)\"\n                type=\"text\"\n                size=\"small\"\n                title=\"详情\"\n                class=\"el-icon-view\"\n              ></el-button>\n              <el-button\n                @click=\"getUpdate(scope.row)\"\n                v-if=\"(scope.row.createBy === currentUser || hasSuperRole) && !scope.row.czp\"\n                type=\"text\"\n                size=\"small\"\n                title=\"编辑\"\n                class=\"el-icon-edit\"\n              >\n              </el-button>\n              <el-button\n                @click=\"deleteRow(scope.row)\"\n                v-if=\"\n                  (scope.row.createBy === currentUser ||\n                    hasSuperRole) &&\n                    !scope.row.czp\n                \"\n                type=\"text\"\n                size=\"small\"\n                title=\"删除\"\n                class=\"el-icon-delete\"\n              >\n              </el-button>\n            </template>\n          </el-table-column>\n        </comp-table>\n      </el-white>\n    </div>\n\n    <!-- 详情/新增/修改 操作命令页-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <div>\n          <el-row style=\"display: flex;flex-wrap: wrap;\">\n            <el-col :span=\"8\">\n              <el-form-item label=\"令类型：\" prop=\"zslOrYbl\">\n                <el-switch\n                  :disabled=\"isDisabled\"\n                  style=\"width: 100%\"\n                  v-model=\"form.zslOrYbl\"\n                  active-color=\"#13ce66\"\n                  inactive-color=\"#ff4949\"\n                  active-text=\"正式令\"\n                  inactive-text=\"预备令\"\n                  @change=\"changeZslOrYbl\"\n                >\n                </el-switch>\n              </el-form-item>\n            </el-col>\n            <!--            <el-col :span=\"8\">-->\n            <!--              <el-form-item label=\"编号：\" prop=\"bh\">-->\n            <!--                <el-input v-model=\"form.bh\" disabled placeholder=\"确认后编号自动生成\"/>-->\n            <!--              </el-form-item>-->\n            <!--            </el-col>-->\n            <el-col :span=\"8\">\n              <el-form-item label=\"分公司：\" prop=\"fgs\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.fgs\"\n                  @change=\"handleFgsChange\"\n                  disabled\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in ssgsOptionsDataList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"变电站：\" prop=\"bdzmc\">\n                <el-select\n                  style=\"width: 100%\"\n                  filterable\n                  clearable\n                  v-model=\"form.bdzmc\"\n                  ref=\"bdzmc\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择变电站\"\n                >\n                  <el-option\n                    v-for=\"item in wzDataListOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"!form.zslOrYbl\"\n                label=\"通知时间：\"\n                prop=\"tzsj\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: false }]\n                    : [{ required: true, message: '不能为空' }]\n                \"\n              >\n                <el-date-picker\n                  v-model=\"form.tzsj\"\n                  :disabled=\"isDisabled\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"!form.zslOrYbl\"\n                label=\"通知人：\"\n                prop=\"tzr\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: false }]\n                    : [{ required: true, message: '不能为空' }]\n                \"\n              >\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.tzr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in tzrOrXlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"!form.zslOrYbl\"\n                label=\"预备令接令人：\"\n                prop=\"ybljlr\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: false }]\n                    : [{ required: true, message: '不能为空' }]\n                \"\n              >\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.ybljlr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in jlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"form.zslOrYbl\"\n                label=\"下令时间：\"\n                prop=\"xlsj\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-date-picker\n                  v-model=\"form.xlsj\"\n                  :disabled=\"isDisabled\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"form.zslOrYbl\"\n                label=\"下令人：\"\n                prop=\"xlr\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.xlr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in tzrOrXlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"form.zslOrYbl\"\n                label=\"接令人：\"\n                prop=\"jlr\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.jlr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in jlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"回令时间：\" prop=\"hlsj\">\n                <el-date-picker\n                  v-model=\"form.hlsj\"\n                  :disabled=\"isDisabled\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  :placeholder=\"isDisabled ? '' : '选择日期时间'\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-form-item label=\"操作任务：\" prop=\"czrw\">\n            <el-input\n              type=\"textarea\"\n              v-model=\"form.czrw\"\n              :disabled=\"isDisabled\"\n              placeholder=\"请输入操作任务\"\n              :rows=\"3\"\n            />\n          </el-form-item>\n        </div>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">关 闭</el-button>\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"saveRow\"\n          >保 存</el-button\n        >\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"createCzp\"\n          >开操作票\n        </el-button>\n        <el-button\n          v-if=\"isDisabled && (form.createBy === currentUser || hasSuperRole)&& !form.czp\"\n          type=\"primary\"\n          @click=\"createBjp\"\n          >开办结票\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!--操作票开票页-->\n    <el-dialog\n      title=\"变电倒闸操作票开票\"\n      :visible.sync=\"isCzpShowDetails\"\n      append-to-body\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form\n        label-width=\"120px\"\n        ref=\"formCzp\"\n        :model=\"formCzp\"\n        :rules=\"czpRules\"\n      >\n        <div>\n          <el-row>\n            <!--            <el-col :span=\"8\">-->\n            <!--              <el-form-item label=\"编号：\" prop=\"bm\">-->\n            <!--                <el-input v-model=\"formCzp.bm\" disabled placeholder=\"确认后编号自动生成\"/>-->\n            <!--              </el-form-item>-->\n            <!--            </el-col>-->\n            <el-col :span=\"8\">\n              <el-form-item label=\"分公司：\" prop=\"fgs\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formCzp.fgs\"\n                  @change=\"handleFgsChange\"\n                  disabled\n                  placeholder=\"请选择分公司\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in ssgsOptionsDataList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"变电站：\" prop=\"bdzmc\">\n                <el-select\n                  style=\"width: 100%\"\n                  filterable\n                  v-model=\"formCzp.bdzmc\"\n                  disabled\n                  placeholder=\"请输入内容\"\n                >\n                  <el-option\n                    v-for=\"item in wzDataListOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作开始时间：\" prop=\"kssj\">\n                <el-date-picker\n                  disabled\n                  v-model=\"formCzp.kssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作结束时间：\" prop=\"jssj\">\n                <el-date-picker\n                  disabled\n                  v-model=\"formCzp.jssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作人：\" prop=\"czr\">\n                <el-input\n                  v-model=\"formCzp.czr\"\n                  :disabled=\"isDisabledCzp\"\n                  placeholder=\"请输入内容\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"监护人：\" prop=\"jhr\">\n                <el-input\n                  v-model=\"formCzp.jhr\"\n                  :disabled=\"isDisabledCzp\"\n                  placeholder=\"请输入内容\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"下令人：\" prop=\"xlr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formCzp.xlr\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                  disabled\n                >\n                  <el-option\n                    v-for=\"item in tzrOrXlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作项数：\" prop=\"czxs\">\n                <el-input-number\n                  v-model=\"formCzp.czxs\"\n                  disabled\n                  placeholder=\"请输入操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"已执行项数：\" prop=\"yzxczxs\">\n                <el-input-number\n                  v-model=\"formCzp.yzxczxs\"\n                  disabled\n                  placeholder=\"请输入已执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"未执行项数：\" prop=\"wzxczxs\">\n                <el-input-number\n                  v-model=\"formCzp.wzxczxs\"\n                  disabled\n                  placeholder=\"请输入未执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"操作任务：\" prop=\"czrw\">\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"formCzp.czrw\"\n                  :disabled=\"isDisabledCzp\"\n                  placeholder=\"请输入内容\"\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"上传图片：\">\n                <el-upload\n                  :disabled=\"isDisabledCzp\"\n                  action=\"\"\n                  ref=\"uploadImg\"\n                  accept=\"image/jpeg,image/jpg,image/png\"\n                  :headers=\"header\"\n                  :multiple=\"true\"\n                  :on-change=\"handleChange\"\n                  :data=\"uploadImgData\"\n                  :file-list=\"imgList\"\n                  :auto-upload=\"false\"\n                  :on-preview=\"handlePictureCardPreview\"\n                  :on-progress=\"handleProgress\"\n                  :on-remove=\"handleRemove\"\n                >\n                  <el-button type=\"primary\" size=\"small\">选择文件</el-button>\n                </el-upload>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n\n        <div>\n          <el-dialog :title=\"titleyl\" :visible.sync=\"yl\" append-to-body>\n            <!--第一个表格-->\n            <div style=\"margin-bottom:10px;\">\n              <el-row>\n                <el-col :span=\"8\">\n                  <el-input\n                    v-model=\"replaceData.oldStr\"\n                    style=\"width:80%\"\n                    placeholder=\"查找字符串\"\n                  >\n                  </el-input>\n                </el-col>\n\n                <el-col :span=\"8\">\n                  <!-- <span> ---</span> -->\n                  <el-input\n                    v-model=\"replaceData.newStr\"\n                    style=\"width:80%\"\n                    placeholder=\"替换后字符串\"\n                  >\n                  </el-input>\n                </el-col>\n                <el-col :span=\"4\">\n                  <el-button\n                    type=\"primary\"\n                    icon=\"el-icon-edit\"\n                    @click=\"replaceStr\"\n                    >批量替换</el-button\n                  >\n                </el-col>\n              </el-row>\n            </div>\n\n            <el-table\n              :data=\"propTableData.colFirst\"\n              ref=\"propTable\"\n              disabled\n              border\n              stripe\n              style=\"width: 100%\"\n              :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n            >\n              <el-table-column\n                align=\"center\"\n                width=\"120\"\n                prop=\"xh\"\n                label=\"顺序号\"\n                sortable\n              >\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"请输入顺序号\"\n                      disabled\n                      v-model=\"scope.row.xh\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"请输入操作项目\"\n                      disabled\n                      v-model=\"scope.row.czrw\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-dialog>\n        </div>\n        <!--列表-->\n        <div>\n          <el-white class=\"mb8 pull-right\">\n            <el-button type=\"primary\" @click=\"getDxpkLists\">典型票库</el-button>\n            <el-button type=\"warning\" @click=\"getLspkList\">历史票库</el-button>\n            <el-button\n              type=\"info\"\n              @click=\"handleYlChange\"\n              style=\"text-align: right\"\n              >预览</el-button\n            >\n          </el-white>\n          <!--第一个表格-->\n          <el-table\n            :data=\"propTableData.colFirst\"\n            height=\"300\"\n            border\n            stripe\n            style=\"width: 100%\"\n            element-loading-text=\"正在获取数据\"\n            element-loading-spinner=\"el-icon-loading\"\n            v-loading=\"loading\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <el-table-column\n              align=\"center\"\n              width=\"120\"\n              prop=\"xh\"\n              label=\"顺序号\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input-number\n                    size=\"small\"\n                    v-model=\"scope.row.xh\"\n                    :min=\"1\"\n                    :precision=\"0\"\n                    controls-position=\"right\"\n                  ></el-input-number>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入操作项目\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" width=\"80\">\n              <template slot=\"header\" slot-scope=\"scope\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  icon=\"el-icon-plus\"\n                  @click=\"listFirstAdd(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n              <template slot-scope=\"scope\">\n                <el-button\n                  type=\"text\"\n                  icon=\"el-icon-delete\"\n                  @click=\"listFirstDel(scope.row)\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"closeCzp\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"saveRowCzp\">保 存</el-button>\n        <el-button type=\"primary\" @click=\"submitCzp\">上 报</el-button>\n      </div>\n    </el-dialog>\n\n    <!--开办结票-->\n    <el-dialog\n      title=\"开办结票\"\n      :visible.sync=\"isBjpShowDetails\"\n      append-to-body\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form\n        label-width=\"120px\"\n        ref=\"formBjp\"\n        :model=\"formBjp\"\n        :rules=\"rules2\"\n      >\n        <div>\n          <el-row>\n            <el-col :span=\"8\">\n              <el-form-item label=\"编号：\" prop=\"bm\">\n                <el-input\n                  v-model=\"formBjp.bm\"\n                  disabled\n                  placeholder=\"确认后编号自动生成\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"分公司：\" prop=\"fgs\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formBjp.fgs\"\n                  @change=\"handleFgsChange\"\n                  disabled\n                  placeholder=\"请选择分公司\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in ssgsOptionsDataList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"变电站：\" prop=\"bdzmc\">\n                <el-select\n                  style=\"width: 100%\"\n                  filterable\n                  v-model=\"formBjp.bdzmc\"\n                  placeholder=\"请输入内容\"\n                  disabled\n                >\n                  <el-option\n                    v-for=\"item in wzDataListOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作开始时间：\" prop=\"kssj\">\n                <el-date-picker\n                  v-model=\"formBjp.kssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作结束时间：\" prop=\"jssj\">\n                <el-date-picker\n                  v-model=\"formBjp.jssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作人：\" prop=\"czr\">\n                <el-input v-model=\"formBjp.czr\" placeholder=\"请输入内容\" />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"监护人：\" prop=\"jhr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formBjp.jhr\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in jlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"下令人：\" prop=\"xlr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formBjp.xlr\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in tzrOrXlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"审票人：\" prop=\"bzspr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formBjp.bzspr\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in sprList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作项数：\" prop=\"czxs\">\n                <el-input-number\n                  v-model=\"formBjp.czxs\"\n                  placeholder=\"请输入操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"已执行项数：\" prop=\"yzxczxs\">\n                <el-input-number\n                  v-model=\"formBjp.yzxczxs\"\n                  placeholder=\"请输入已执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"未执行项数：\" prop=\"wzxczxs\">\n                <el-input-number\n                  v-model=\"formBjp.wzxczxs\"\n                  placeholder=\"请输入未执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"操作任务：\" prop=\"czrw\">\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"formBjp.czrw\"\n                  placeholder=\"请输入内容\"\n                  disabled\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"上传图片：\">\n                <el-upload\n                  action=\"\"\n                  ref=\"uploadImg\"\n                  accept=\"image/jpeg,image/jpg,image/png\"\n                  :headers=\"header\"\n                  :multiple=\"true\"\n                  :on-change=\"handleChange\"\n                  :data=\"uploadImgData\"\n                  :file-list=\"imgList\"\n                  :auto-upload=\"false\"\n                  list-type=\"picture-card\"\n                  :on-preview=\"handlePictureCardPreview\"\n                  :on-progress=\"handleProgress\"\n                  :on-remove=\"handleRemove\"\n                >\n                  <i class=\"el-icon-plus\"></i>\n                </el-upload>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"closeCzp\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"saveRowBjp\">确 认</el-button>\n      </div>\n    </el-dialog>\n\n    <!--典型票库-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isDxpShowDetails\"\n      width=\"50%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"180px\" ref=\"form\" :model=\"form\">\n        <el-row>\n          <!--查询及列表-->\n          <el-col :span=\"24\">\n            <el-form\n              :model=\"queryParams\"\n              class=\"searchForm\"\n              ref=\"queryForm\"\n              label-width=\"100px\"\n              v-show=\"isShowSx\"\n            >\n              <el-row>\n                <el-col :span=\"12\">\n                  <el-form-item label=\"典型票名称：\" prop=\"dxpmc\">\n                    <el-input\n                      placeholder=\"请输入典型票名称\"\n                      v-model=\"queryParams.dxpmc\"\n                      clearable\n                    />\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"12\">\n                  <el-form-item label=\"操作任务：\" prop=\"czrw\">\n                    <el-input\n                      placeholder=\"请输入操作任务\"\n                      v-model=\"queryParams.czrw\"\n                      clearable\n                    />\n                  </el-form-item>\n                </el-col>\n                <div style=\"float: right;margin-bottom: 10px\">\n                  <el-button\n                    type=\"primary\"\n                    icon=\"el-icon-search\"\n                    @click=\"handleQuery\"\n                    >搜索</el-button\n                  >\n                  <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\"\n                    >重置</el-button\n                  >\n                </div>\n              </el-row>\n            </el-form>\n            <div style=\"float: left;margin-bottom: 10px\">\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-search\"\n                @click=\"isShowSx = isShowSx ? false : true\"\n                >筛 选</el-button\n              >\n            </div>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <!--主表信息-->\n            <comp-table\n              :table-and-page-info=\"propTableDataDxp\"\n              height=\"400\"\n              border\n              stripe\n              style=\"width: 100%\"\n              max-height=\"60vh\"\n              @getMethod=\"handleQuery\"\n              @rowClick=\"changeMx\"\n            >\n            </comp-table>\n          </el-col>\n        </el-row>\n        <!--子表信息-->\n        <el-row>\n          <el-table\n            :data=\"propTableDataDxpMx.colFirst\"\n            :disabled=\"isDisabled\"\n            height=\"300\"\n            border\n            stripe\n            style=\"width: 100%\"\n            max-height=\"40vh\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <!--子表列表-->\n            <el-table-column\n              align=\"center\"\n              prop=\"xh\"\n              label=\"序号\"\n              width=\"100\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"序号\"\n                    :disabled=\"isShowDxp\"\n                    v-model=\"scope.row.xh\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"操作项目\"\n                    :disabled=\"isShowDxp\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n          </el-table>\n        </el-row>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"saveRowDxp\">确 认 </el-button>\n      </div>\n    </el-dialog>\n\n    <!--历史票库-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isLspShowDetails\"\n      width=\"50%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"180px\" ref=\"form\" :model=\"form\">\n        <el-form\n          :model=\"queryParamsLsp\"\n          class=\"searchForm\"\n          ref=\"queryForm\"\n          label-width=\"100px\"\n          v-show=\"isShowSx\"\n        >\n          <el-row>\n            <el-col :span=\"12\">\n              <el-form-item label=\"操作时间\" prop=\"czsjArr\">\n                <el-date-picker\n                  type=\"daterange\"\n                  range-separator=\"-\"\n                  start-placeholder=\"开始时间\"\n                  end-placeholder=\"结束时间\"\n                  v-model=\"queryParamsLsp.czsjArr\"\n                  clearable\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <!--<el-col :span=\"12\">\n              <el-form-item label=\"操作结束时间\" prop=\"jssj\">\n                <el-input placeholder=\"请输入操作结束时间\" v-model=\"queryParamsLsp.jssj\" clearable primary style=\"width: 100%\"/>\n              </el-form-item>\n            </el-col>-->\n            <el-col :span=\"24\">\n              <el-form-item label=\"操作任务\" prop=\"czrw\">\n                <el-input\n                  type=\"textarea\"\n                  :rows=\"1\"\n                  placeholder=\"请输入操作任务\"\n                  v-model=\"queryParamsLsp.czrw\"\n                  clearable\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <el-col :span=\"12\">\n              <el-form-item label=\"操作人\" prop=\"czr\">\n                <el-input\n                  placeholder=\"请输入操作人\"\n                  v-model=\"queryParamsLsp.czr\"\n                  clearable\n                  primary\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"监护人\" prop=\"jhr\">\n                <el-input\n                  placeholder=\"请输入监护人\"\n                  v-model=\"queryParamsLsp.jhr\"\n                  clearable\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <el-col :span=\"12\">\n              <el-form-item label=\"下令人\" prop=\"xlr\">\n                <el-input\n                  placeholder=\"请输入下令人\"\n                  v-model=\"queryParamsLsp.xlr\"\n                  clearable\n                  primary\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n            <div style=\"float: right;margin-bottom: 10px\">\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-search\"\n                @click=\"handleQueryLsp\"\n                >搜索</el-button\n              >\n              <el-button icon=\"el-icon-refresh\" @click=\"resetQueryLsp\"\n                >重置</el-button\n              >\n            </div>\n          </el-row>\n        </el-form>\n        <!--主表信息-->\n        <div>\n          <div style=\"float: left;margin-bottom: 10px\">\n            <el-button\n              type=\"primary\"\n              icon=\"el-icon-search\"\n              @click=\"isShowSx = isShowSx ? false : true\"\n              >筛 选</el-button\n            >\n          </div>\n          <el-table\n            @row-click=\"changeLspMx\"\n            :data=\"propTableDataLsp.colFirst\"\n            :disabled=\"isDisabled\"\n            height=\"300\"\n            border\n            stripe\n            style=\"width: 100%\"\n            max-height=\"40vh\"\n          >\n            <el-table-column\n              align=\"center\"\n              prop=\"bdzmcs\"\n              label=\"变电站\"\n              width=\"170\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入变电站\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.bdzmcs\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"kssj\"\n              label=\"操作开始时间\"\n              width=\"170\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入开始时间\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.kssj\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"jssj\"\n              label=\"操作结束时间\"\n              width=\"170\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入结束时间\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.jssj\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"czrw\"\n              label=\"操作任务\"\n              width=\"230\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    type=\"textarea\"\n                    :rows=\"2\"\n                    placeholder=\"请输入操作任务\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"czr\"\n              label=\"操作人\"\n              width=\"120\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.czr\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"jhr\"\n              label=\"监护人\"\n              width=\"120\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.jhr\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"xlr\"\n              label=\"下令人\"\n              width=\"120\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.xlrmc\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"spr\"\n              label=\"审票人\"\n              width=\"150\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.bzsprmc\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n        <!--子表信息-->\n        <div>\n          <el-table\n            :data=\"propTableDataLspMx.colFirst\"\n            :disabled=\"isDisabled\"\n            height=\"300\"\n            border\n            stripe\n            style=\"width: 100%\"\n            max-height=\"40vh\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <!--子表列表-->\n            <el-table-column\n              align=\"center\"\n              width=\"120\"\n              prop=\"xh\"\n              label=\"顺序号\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入顺序号\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.xh\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入操作项目\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"saveRowLsp\">确 认 </el-button>\n      </div>\n    </el-dialog>\n\n    <!--图片放大展示弹出框-->\n    <el-dialog title=\"放大\" :visible.sync=\"imgDialogVisible\" v-dialogDrag>\n      <img width=\"100%\" :src=\"dialogImageUrl\" alt=\"\" />\n    </el-dialog>\n\n    <!--  工作流  -->\n    <activiti\n      ref=\"activiti\"\n      :processData=\"processData\"\n      :isShow=\"isShow\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n    ></activiti>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport api from \"@/utils/request\";\nimport {\n  exportExcel,\n  getBdzSelectList,\n  getList,\n  remove,\n  saveOrUpdate,\n  saveOrUpdateCzp,\n  exportWordByselection,\n  exportWordByparams\n} from \"@/api/yxgl/bdyxgl/bddzczml\";\nimport { getDxpkList, getLists } from \"@/api/bzgl/dxczp\";\nimport { getListLsp, getLspkList, updateById } from \"@/api/yxgl/bdyxgl/bddzczp\";\nimport ElectronicAuthDialog from \"com/ElectronicAuthDialog\";\nimport CompTable from \"com/CompTable\";\nimport ElFilter from \"com/ElFilter\";\nimport { getUsers } from \"@/api/system/usergroup\";\nimport activiti from \"com/activiti_czp\";\nimport { getUUID } from \"@/utils/ruoyi\";\nimport { getFgsOptions } from \"@/api/yxgl/bdyxgl/zbgl\";\n\nexport default {\n  name: \"dzczml\",\n  components: { ElectronicAuthDialog, CompTable, ElFilter, activiti },\n  data() {\n    return {\n      hasSuperRole:this.$store.getters.hasSuperRole,\n      //loading:false,\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"czpsh\",\n        businessKey: \"\",\n        businessType: \"倒闸操作票\",\n        variables: {},\n        defaultFrom: true,\n        nextUser: \"\",\n        processType: \"complete\"\n      },\n      isShow: false,\n      //弹出框标题\n      activitiOption: { title: \"上报\" },\n      czpRules: {\n        // xlr: [\n        //   {required: true, message: '不能为空', trigger: 'blur'}\n        // ],\n      },\n      rules: {\n        bdzmc: [{ required: true, message: \"不能为空\", trigger: \"select\" }],\n        fgs: [{ required: true, message: \"不能为空\", trigger: \"select\" }],\n        czrw: [{ required: true, message: \"不能为空\", trigger: \"blur\" }]\n      },\n      rules2: {\n        kssj: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        jssj: [{ required: true, message: \"不能为空\", trigger: \"select\" }],\n        czr: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        jhr: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        xlr: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        czxs: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        yzxczxs: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        wzxczxs: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        bzspr: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        czrw: [{ required: true, message: \"不能为空\", trigger: \"blur\" }]\n      },\n      currentUser: this.$store.getters.name,\n      yl: false,\n      replaceData: {},\n      titleyl: \"\",\n      //图片地址url\n      dialogImageUrl: \"\",\n      //展示图片dialog控制\n      imgDialogVisible: false,\n      //上传图片时的携带的其他参数\n      uploadImgData: {\n        businessId: \"\" //携带的表单主键id\n      },\n      //上传图片时的请求头\n      header: {},\n      //图片list\n      imgList: [],\n      isShowSx: false,\n      bdzList: [],\n      //弹出框内新增时下拉框所属位置数据\n      wzDataListOptions: [],\n      isShowDxp: false,\n      isShowLsp: false,\n      loading: false,\n      isDisabledCzp: false,\n      isDxpShowDetails: false,\n      isLspShowDetails: false,\n      //操作票弹框是否显示\n      isCzpShowDetails: false,\n      isBjpShowDetails: false,\n      // 多选框选中的id\n      ids: [],\n      selectData: [],\n      //倒闸操作票命令\n      params: {\n        //变电\n        lx: 2\n      },\n      //典型票查询条件\n      queryParams: {},\n      //历史票查询条件\n      queryParamsLsp: {\n        status: \"4\",\n        lx: 2\n      },\n      //弹出框中表格数据\n      propTableData: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //弹出框中表格数据\n      propTableDataDxpMx: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //弹出框中表格数据\n      propTableDataDxp: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 1,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"变电站\", prop: \"sbmcms\", minWidth: \"200\" },\n          { label: \"典型操作票名称\", prop: \"dxpmc\", minWidth: \"200\" },\n          { label: \"操作任务\", prop: \"czrw\", minWidth: \"200\" }\n        ],\n        option: { checkBox: false, serialNumber: true },\n        sel: null // 选中行\n      },\n      //弹出框中表格数据\n      propTableDataLspMx: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //弹出框中表格数据\n      propTableDataLsp: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //form表单\n      form: {\n        bh: \"\",\n        bdzmc: \"\",\n        tzsj: \"\",\n        tzr: \"\",\n        xlsj: \"\",\n        xlr: \"\",\n        czrw: \"\",\n        jlr: \"\",\n        hlsj: \"\",\n        status: \"\"\n      },\n      // 操作票\n      formCzp: {\n        bh: \"\",\n        bdzmc: \"\",\n        kssj: \"\",\n        jssj: \"\",\n        czrw: \"\",\n        czr: \"\",\n        jhr: \"\",\n        xlr: \"\",\n        spr: \"\",\n        lx: 2, //变电\n        colFirst: [],\n        status: \"\"\n      },\n      // 办结票\n      formBjp: {\n        bh: \"\",\n        bdzmc: \"\",\n        kssj: \"\",\n        jssj: \"\",\n        czrw: \"\",\n        czr: \"\",\n        jhr: \"\",\n        xlr: \"\",\n        spr: \"\",\n        lx: 2, //变电\n        colFirst: [],\n        status: \"\"\n      },\n\n      //所属分公司下拉框数据\n      ssgsOptionsDataList: [],\n\n      //详情弹框是否显示\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          bh: \"\",\n          bdzmc: \"\",\n          tzsjArr: \"\",\n          tzr: \"\",\n          xlsjArr: \"\",\n          xlr: \"\",\n          czrw: \"\",\n          jlr: \"\",\n          hlsjArr: \"\"\n        }, //查询条件\n        fieldList: [\n          // {\n          //   label: '令',\n          //   type: 'switch',\n          //   value: 'yblOrZsl',\n          //   textStart:'预备令',\n          //   textEnd:'正式令'\n          // },\n          // {label: '编号', value: 'bh', type: 'input', clearable: true},\n          // /*{ label: '状态', value: 'status', type: 'select', clearable: true },*/\n          {\n            label: \"分公司\",\n            value: \"fgs\",\n            type: \"select\",\n            options: [],\n            clearable: true\n          },\n          {\n            label: \"变电站\",\n            value: \"bdzmc\",\n            type: \"select\",\n            options: [],\n            clearable: true,\n            filterable: true\n          },\n          {\n            label: \"通知时间\",\n            value: \"tzsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"通知人\", value: \"tzr\", type: \"input\", clearable: true },\n          {\n            label: \"下令时间\",\n            value: \"xlsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"下令人\", value: \"xlr\", type: \"input\", clearable: true },\n          { label: \"操作任务\", value: \"czrw\", type: \"input\", clearable: true },\n          { label: \"接令人\", value: \"jlr\", type: \"input\", clearable: true },\n          {\n            label: \"回令时间\",\n            value: \"hlsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          /* { label: '状态', prop: 'status', minWidth: '70' },*/\n          { label: \"分公司\", prop: \"fgsmc\", minWidth: \"120\" },\n          { label: \"变电站\", prop: \"bdzmcs\", minWidth: \"120\" },\n          { label: \"通知人\", prop: \"tzrxm\", minWidth: \"80\" },\n          { label: \"通知时间\", prop: \"tzsj\", minWidth: \"140\" },\n          { label: \"下令人\", prop: \"xlrmc\", minWidth: \"80\" },\n          { label: \"下令时间\", prop: \"xlsj\", minWidth: \"140\" },\n          { label: \"操作任务\", prop: \"czrw\", minWidth: \"160\" },\n          { label: \"接令人\", prop: \"jlrxm\", minWidth: \"80\" },\n          { label: \"预备令接令人\", prop: \"ybljlrxm\", minWidth: \"100\" },\n          { label: \"回令时间\", prop: \"hlsj\", minWidth: \"140\" }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      tzrOrXlrList: [],\n      jlrList: [],\n      alljlrList: [],\n      sprList: []\n    };\n  },\n  async mounted() {\n    //获取token\n    this.header.token = getToken();\n    //变电站下拉框\n    this.getFgsOptions();\n    this.getAllBdzSelectList();\n    this.tzrOrXlrList = await this.getGroupUsers(61, \"\");\n    this.alljlrList = await this.getGroupUsers(62, \"\");\n    this.sprList = await this.getGroupUsers(13, \"\");\n    await this.getData();\n  },\n  methods: {\n    /**\n     * 获取分公司下拉数据\n     */\n    getFgsOptions() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.ssgsOptionsDataList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"fgs\") {\n            return (item.options = this.ssgsOptionsDataList);\n          }\n        });\n      });\n    },\n    //导出\n    exportFun() {\n      exportExcel(this.params, \"操作命令信息\");\n    },\n    //工作流回传数据\n    async todoResult(data) {\n      let row = {\n        objId: data.businessKey,\n        isStart: 1,\n        isBack: data.processType === \"rollback\" ? 1 : 0\n      };\n      row.status = \"1\";\n      row.bzspr = data.nextUser;\n      let { code } = await updateById(row);\n      if (code === \"0000\") {\n        await this.getData();\n      } else {\n        this.$message.warning(\"操作失败\");\n      }\n    },\n    //关闭弹窗\n    closeActiviti() {\n      if (this.$refs.activiti.$refs.form) {\n        this.$refs.activiti.$refs.form.resetFields();\n      }\n      this.isShow = false;\n    },\n    getAllBdzSelectList() {\n      getBdzSelectList({}).then(res => {\n        this.bdzList = res.data;\n      });\n    },\n    //所属公司change事件\n    async handleFgsChange(fgsValue) {\n      //清空之前得选中值\n      this.wzDataListOptions = [];\n      this.$set(this.form, \"bdzmc\", \"\");\n      this.$set(this.form, \"tzr\", \"\");\n      this.jlrList = [];\n      this.$set(this.form, \"jlr\", \"\");\n      this.$set(this.form, \"ybljlr\", \"\");\n      //获取变电站方法\n      await this.fgsChange(fgsValue);\n    },\n    async fgsChange(fgsValue) {\n      //获取变电站方法\n      this.getBdzSelectList(fgsValue);\n      this.jlrList = await this.getGroupUsers(62, fgsValue);\n    },\n    handleYlChange() {\n      this.titleyl = \"查看操作项目\";\n      this.yl = true;\n    },\n    replaceStr() {\n      this.propTableData.colFirst.forEach(item => {\n        item.czrw = item.czrw.replaceAll(\n          this.replaceData.oldStr,\n          this.replaceData.newStr\n        );\n      });\n    },\n    //改造后的上传多个图片文件\n    uploadForm() {\n      var newUrl = []; //用来存放当前未改动过的图片url,此图片不进行删除处理，其余当前业务id下面的图片数据将进行删除\n      var imageType = [\"png\", \"jpg\"];\n      const formData = new FormData();\n      // 因为要传一个文件数组过去，所以要循环append\n      this.imgList.forEach(file => {\n        if (file.raw == undefined) {\n          newUrl.push(file.url);\n        }\n        formData.append(\"files\", file.raw);\n      });\n      formData.append(\"businessId\", this.uploadImgData.businessId); // 自定义参数\n      formData.append(\"newUrl\", newUrl); // 未改动过的图片url\n      formData.append(\"type\", imageType); // 未改动过的图片url\n      api\n        .requestPost(\"/isc-api/file/uploadFiles\", formData, 1)\n        .then(res => {\n          // 清空图片列表（一定要清空，否则上传成功后还是会调用handleChange（）函数，上传成功后列表中还存在图片）\n          this.imgList = [];\n          //查询接口数据\n          this.getData();\n        })\n        .catch(res => {\n          this.$message.success(\"图片上传失败！\");\n        });\n    },\n    // 选择文件时，往fileList里添加\n    handleChange(file, fileList) {\n      this.imgList = fileList;\n    },\n    handleProgress(event, file, fileList) {},\n    //图片移除\n    handleRemove(file, fileList) {\n      this.imgList = fileList;\n    },\n    //图片放大\n    handlePictureCardPreview(file) {\n      this.dialogImageUrl = file.url;\n      this.imgDialogVisible = true;\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        this.loading = true;\n        param.mySorts = [{ prop: \"updateTime\", asc: false }];\n        const { data, code } = await getList(param);\n        if (code === \"0000\") {\n          for (let i of data.records) {\n            i.fgsmc = this.formatSsgs(i.fgs);\n            i.tzrxm = this.formatXlrOrTzr(i.tzr);\n            i.xlrxm = this.formatXlrOrTzr(i.xlr);\n            i.jlrxm = this.formatJlr(i.jlr);\n            i.ybljlrxm = this.formatJlr(i.ybljlr);\n          }\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          this.loading = false;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n\n    //新增按钮\n    async getInster() {\n      this.title = \"变电倒闸操作命令增加\";\n      this.isDisabled = false;\n      this.form = { fgs: this.$store.getters.deptId.toString() };\n      await this.fgsChange(this.form.fgs);\n      this.form.status = \"0\";\n      this.isShowDetails = true;\n    },\n    //修改按钮\n    async getUpdate(row) {\n      this.title = \"变电倒闸操作命令修改\";\n      this.isDisabled = false;\n      this.form = { ...row };\n      await this.fgsChange(this.form.fgs);\n      this.isShowDetails = true;\n    },\n\n    //详情按钮\n    async getDetails(row) {\n      this.title = \"变电倒闸操作命令详情\";\n      this.form = { ...row };\n      await this.fgsChange(this.form.fgs);\n      this.isDisabled = true;\n      this.isShowDetails = true;\n    },\n    /**\n     * 操作票开票按钮\n     * */\n    async createCzp() {\n      await this.$refs[\"form\"].validate(async valid => {\n        if (valid) {\n          try {\n            let { code, data } = await saveOrUpdate(this.form);\n            if (code === \"0000\") {\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.isShowDetails = false;\n              await this.getData();\n              //操作票弹出框\n              this.isDisabledCzp = true;\n              this.propTableData.colFirst = [];\n              //清除校验提示\n              this.$nextTick(function() {\n                this.$refs[\"formCzp\"].clearValidate();\n              });\n              this.formCzp = {};\n              //变电\n              this.formCzp.lx = 2;\n              this.formCzp.status = \"0\";\n              this.formCzp.czml = data.objId;\n              this.formCzp.bdzmc = data.bdzmc;\n              if (data.xlr) {\n                this.formCzp.xlr = data.xlr;\n              }\n              this.formCzp.czrw = data.czrw;\n              this.formCzp.fgs = data.fgs;\n              this.formCzp.bm = data.bh;\n              this.isDisabled = true;\n              this.isCzpShowDetails = true;\n            }\n          } catch (e) {\n            console.log(e);\n          }\n        }\n      });\n    },\n    /**\n     * 开办结票\n     * */\n    createBjp() {\n      this.isBjpShowDetails = true;\n      this.isShowDetails = false;\n      this.formBjp.bdzmc = this.form.bdzmc;\n      this.formBjp.xlr = this.form.xlr;\n      this.formBjp.czrw = this.form.czrw;\n      this.formBjp.fgs = this.form.fgs;\n      this.formBjp.status = \"4\";\n      this.formBjp.lx = 2;\n      this.formBjp.czml = this.form.objId;\n    },\n    async saveRow() {\n      await this.$refs[\"form\"].validate(async valid => {\n        if (valid) {\n          try {\n            let { code } = await saveOrUpdate(this.form);\n            if (code === \"0000\") {\n              this.$message.success(\"操作成功\");\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.isShowDetails = false;\n              await this.getData();\n            }\n          } catch (e) {\n            console.log(e);\n          }\n        }\n      });\n    },\n    //删除按钮\n    deleteRow(row) {\n      this.form = { ...row };\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove([this.form.objId]).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {});\n    },\n    //关闭弹窗\n    close() {\n      this.isDxpShowDetails = false;\n      this.isLspShowDetails = false;\n      this.isShowDetails = false;\n    },\n    //选择行\n    selectChange(selection) {\n      // this.ids = selection.map(item => item.objId);\n      // this.single = selection.length !== 1\n      // this.multiple = !selection.length\n      this.selectData = selection;\n    },\n    //表格新增\n    listFirstAdd() {\n      let row = {\n        xh: \"\",\n        czrw: \"\",\n        sfwc: \"\",\n        uuid: getUUID()\n      };\n      this.propTableData.colFirst.push(row);\n      this.propTableData.sel = row;\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //表格删除\n    listFirstDel(row) {\n      if (row.objId) {\n        this.ids.push(row.objId);\n      }\n      this.propTableData.colFirst = this.propTableData.colFirst.filter(\n        item => item.uuid !== row.uuid\n      );\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //操作票关闭弹窗\n    closeCzp() {\n      this.isCzpShowDetails = false;\n      this.isBjpShowDetails = false;\n    },\n\n    //操作票确定按钮\n    saveRowCzp() {\n      this.formCzp.colFirst = this.propTableData.colFirst;\n      this.formCzp.objIdList = this.ids;\n      let tableValid = this.propTableData.colFirst.some(item => !item.czrw);\n      if (tableValid) {\n        this.$message.error(\"操作项目存在空项，请检查\");\n      }\n      this.$refs[\"formCzp\"].validate(async valid => {\n        if (valid && !tableValid) {\n          saveOrUpdateCzp(this.formCzp).then(res => {\n            if (res.code === \"0000\") {\n              this.$message.success(\"操作成功\");\n              this.isCzpShowDetails = false;\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            }\n          });\n        }\n      });\n    },\n\n    //直接上报操作票\n    submitCzp() {\n      this.formCzp.colFirst = this.propTableData.colFirst;\n      this.formCzp.objIdList = this.ids;\n      let tableValid = this.propTableData.colFirst.some(item => !item.czrw);\n      if (tableValid) {\n        this.$message.error(\"操作项目存在空项，请检查\");\n      }\n      this.$refs[\"formCzp\"].validate(async valid => {\n        if (valid && !tableValid) {\n          saveOrUpdateCzp(this.formCzp).then(res => {\n            if (res.code === \"0000\") {\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n              let data = res.data;\n              this.isCzpShowDetails = false;\n              this.processData.variables.pass = true;\n              this.processData.businessKey = data.objId;\n              this.processData.processType = \"complete\";\n              this.activitiOption.title = \"提交\";\n              this.processData.defaultFrom = true;\n              this.processData.rylx = \"班组审核人\";\n              this.processData.dw = data.fgs;\n              this.processData.personGroupId = 13;\n              this.processData.routePath =\n                \"/bdgl/bddzcz/dagangOilfield/czpgl/bddzcz/dzczp\";\n              this.isShow = true;\n            }\n          });\n        }\n      });\n    },\n\n    //办结票确定按钮\n    saveRowBjp() {\n      this.$refs[\"formBjp\"].validate(async valid => {\n        if (valid) {\n          saveOrUpdateCzp(this.formBjp).then(res => {\n            if (res.code === \"0000\") {\n              this.uploadImgData.businessId = res.data.objId;\n              //开始上传图片\n              this.uploadForm();\n              this.$message.success(\"操作成功\");\n              this.isBjpShowDetails = false;\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            }\n          });\n        }\n      });\n    },\n    /**\n     * -----------------------------------典型票库---------------------------------------\n     * */\n    //获取典型操作票\n    getDxpkLists() {\n      this.title = \"典型票库查询\";\n      this.isDxpShowDetails = true;\n      this.isDisabled = false;\n      this.isShowDxp = true;\n      this.queryParams.sbmc = this.form.bdzmc;\n      this.handleQuery();\n    },\n    async changeMx(row) {\n      try {\n        const { data, code } = await getDxpkList({ objId: row.objId });\n        if (code === \"0000\") {\n          this.propTableDataDxpMx.colFirst = data;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //查询条件\n    async handleQuery(params) {\n      let param = { ...params, ...this.queryParams };\n      getLists(param).then(response => {\n        this.propTableDataDxp.tableData = response.data.records;\n        this.propTableDataDxp.pager.total = response.data.total;\n      });\n    },\n    //重置条件\n    resetQuery() {\n      this.queryParams = {};\n      this.handleQuery();\n    },\n    //典型票库确认按钮\n    saveRowDxp() {\n      this.propTableData.colFirst = this.propTableDataDxpMx.colFirst;\n      this.propTableData.colFirst.forEach(e => {\n        e.uuid = getUUID();\n      });\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n      this.isDxpShowDetails = false;\n    },\n    /**\n     * -----------------------------------历史票库---------------------------------------\n     * */\n    //获取历史操作票\n    getLspkList() {\n      this.title = \"历史票库查询\";\n      this.isLspShowDetails = true;\n      this.isDisabled = false;\n      this.isShowLsp = true;\n      this.queryParamsLsp.bdzmc = this.form.bdzmc;\n      this.handleQueryLsp();\n    },\n    // 当点击行时，传入参数查询\n    async changeLspMx(row) {\n      try {\n        const { data, code } = await getLspkList(row.objId);\n        if (code === \"0000\") {\n          this.propTableDataLspMx.colFirst = data;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //历史票库确认按钮\n    saveRowLsp() {\n      this.propTableData.colFirst = this.propTableDataLspMx.colFirst;\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n      this.propTableData.colFirst.forEach(e => {\n        e.sfwc = false;\n        e.uuid = getUUID();\n      });\n      this.isLspShowDetails = false;\n    },\n    //查询条件\n    handleQueryLsp() {\n      getListLsp(this.queryParamsLsp).then(response => {\n        this.propTableDataLsp.colFirst = response.data.records;\n      });\n    },\n    //重置条件\n    resetQueryLsp() {\n      this.queryParamsLsp = {\n        status: \"4\",\n        lx: 2\n      };\n      this.handleQueryLsp();\n    },\n    /**\n     * 获取变电站下拉框数据\n     */\n    getBdzSelectList(fgs) {\n      getBdzSelectList({ ssdwbm: fgs }).then(res => {\n        this.wzDataListOptions = res.data;\n      });\n    },\n    //下拉框change事件\n    handleEvent(val, eventValue) {\n      if (val.label === \"fgs\") {\n        getBdzSelectList({ ssdwbm: val.value.toString() }).then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value === \"bdzmc\") {\n              this.$set(eventValue, \"bdzmc\", \"\");\n              return (item.options = res.data);\n            }\n          });\n        });\n      }\n    },\n    filterReset() {\n      this.params = {\n        //变电\n        lx: 2\n      };\n    },\n    formatSsgs(ssgs) {\n      let pageOrganizationSelectedList = JSON.parse(JSON.stringify(this.ssgsOptionsDataList))\n      pageOrganizationSelectedList.push({label: '港东变电分公司', value: '3002'})\n      pageOrganizationSelectedList.push({label: '港中变电分公司', value: '3003'})\n      if (ssgs) {\n        let filter = pageOrganizationSelectedList.filter(g => g.value === ssgs);\n        if (filter.length > 0) {\n          return filter[0].label;\n        } else {\n          return \"\";\n        }\n      } else {\n        return \"\";\n      }\n    },\n    getGroupUsers(personGroupId, deptId) {\n      return getUsers({\n        personGroupId: personGroupId,\n        deptId: deptId,\n        deptName: \"\"\n      }).then(res => {\n        return res.data;\n      });\n    },\n    formatXlrOrTzr(xlr) {\n      if (xlr) {\n        let filter = this.tzrOrXlrList.filter(g => g.userName === xlr);\n        if (filter.length > 0) {\n          return filter[0].nickName;\n        } else {\n          return \"\";\n        }\n      } else {\n        return \"\";\n      }\n    },\n\n    formatJlr(jlr) {\n      if (jlr) {\n        let filter = this.alljlrList.filter(g => g.userName === jlr);\n        if (filter.length > 0) {\n          return filter[0].nickName;\n        } else {\n          return \"\";\n        }\n      } else {\n        return \"\";\n      }\n    },\n    //导出word\n    exportWord() {\n      let params = {\n        data: this.params,\n        url: \"bzBddzczml\"\n      };\n      let fileName = \"变电倒闸操作命令记录\";\n      if (!this.selectData.length > 0) {\n        params.data = this.params;\n        exportWordByparams(params, fileName);\n      } else {\n        params.data = this.selectData;\n        exportWordByselection(params, fileName);\n      }\n    },\n    changeZslOrYbl(val) {\n      if (val) {\n        this.$set(this.form, \"tzsj\", null);\n        this.$set(this.form, \"tzr\", \"\");\n        this.$set(this.form, \"ybljlr\", \"\");\n      } else {\n        this.$set(this.form, \"xlsj\", null);\n        this.$set(this.form, \"xlr\", \"\");\n        this.$set(this.form, \"jlr\", \"\");\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.card-style {\n  background-color: #e0eff2;\n  line-height: 3vh;\n  border-radius: 3px;\n  font-weight: 800;\n  padding-left: 1vw;\n  margin-bottom: 3vh;\n}\n\n/*控制input输入框边框是否显示*/\n.elInput >>> .el-input__inner {\n  border: 0;\n}\n\n.box-card {\n  margin-bottom: 2vh !important;\n}\n</style>\n"]}]}