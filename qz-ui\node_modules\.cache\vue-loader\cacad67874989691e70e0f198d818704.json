{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\pddzcz\\dzczp.vue?vue&type=style&index=0&id=e84985da&scoped=true&lang=scss&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\pddzcz\\dzczp.vue", "mtime": 1748604805608}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouaXRlbSB7CiAgd2lkdGg6IDguNXJlbTsKICBoZWlnaHQ6IDEuMjVyZW07Cn0K"}, {"version": 3, "sources": ["dzczp.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAy5CA;AACA;AACA;AACA", "file": "dzczp.vue", "sourceRoot": "src/views/dagangOilfield/czpgl/pddzcz", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 180 }\"\n      @handleReset=\"filterReset\"\n    />\n    <el-white class=\"button-group\">\n      <div class=\"button_btn\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          @click=\"getInster\"\n          v-hasPermi=\"['pddzczp:button:add']\"\n          >新增\n        </el-button>\n        <el-button\n          type=\"danger\"\n          icon=\"el-icon-delete\"\n          v-if=\"hasSuperRole\"\n          @click=\"deleteRow\"\n          :disabled=\"single\"\n          >删除</el-button\n        >\n      </div>\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"selectChange\"\n        height=\"65vh\"\n      >\n        <!-- <el-table-column\n          prop=\"statusCn\"\n          slot=\"table_start\"\n          align=\"center\"\n          style=\"display: block\"\n          label=\"流程状态\"\n          min-width=\"120\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-badge\n              v-if=\"scope.row.isBack === 1\"\n              value=\"退回\"\n              class=\"item\"\n              type=\"danger\"\n            >\n            </el-badge>\n            <span>{{ scope.row.statusCn }}</span>\n          </template>\n        </el-table-column> -->\n        <el-table-column\n          slot=\"table_eight\"\n          align=\"center\"\n          fixed=\"right\"\n          style=\"display: block\"\n          label=\"操作\"\n          width=\"160\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              @click=\"getInfo(scope.row)\"\n              type=\"text\"\n              size=\"small\"\n              title=\"详情\"\n              class=\"el-icon-view\"\n            />\n            <el-button\n              @click=\"getUpdate(scope.row)\"\n              class=\"el-icon-edit\"\n              title=\"编辑\"\n              v-if=\"\n                (scope.row.status === '0' &&\n                  scope.row.createBy === currentUser) ||\n                  hasSuperRole\n              \"\n              type=\"text\"\n              size=\"small\"\n            />\n            <el-button\n              @click=\"deleteRow(scope.row)\"\n              class=\"el-icon-delete\"\n              title=\"删除\"\n              v-if=\"\n                scope.row.status === '0' &&\n                  scope.row.createBy === currentUser &&\n                  scope.row.isStart === 0\n              \"\n              type=\"text\"\n              size=\"small\"\n            />\n            <el-button\n                type=\"text\"\n                size=\"small\"\n                @click=\"nullifyGzp(scope.row.objId)\"\n                v-if=\"scope.row.status < 4 && scope.row.status > 0 &&\n                ( scope.row.createBy === currentUser || (scope.row.bzspr && scope.row.bzspr === currentUser) \n                || (scope.row.fgsspr && scope.row.fgsspr === currentUser) \n                || (scope.row.bjr && scope.row.bjr === currentUser)\n                || hasSuperRole )\n                \"\n                title=\"作废\"\n                class=\"el-icon-close\"\n              >\n              </el-button>\n            <el-button\n              @click=\"showTimeLine(scope.row)\"\n              v-if=\"scope.row.isStart === 1\"\n              type=\"text\"\n              size=\"small\"\n              class=\"el-icon-lcck commonIcon\"\n              title=\"流程查看\"\n            />\n            <el-button\n              @click=\"showProcessImg(scope.row)\"\n              v-if=\"scope.row.isStart === 1\"\n              type=\"text\"\n              size=\"small\"\n              class=\"el-icon-lct commonIcon\"\n              title=\"流程图\"\n            />\n            <!--              <el-button @click=\"exportWord(scope.row)\" v-if=\"scope.row.status == '待办结'\" type=\"text\" size=\"small\">\n                              导出Word\n                            </el-button>-->\n            <el-button\n              @click=\"previewFile(scope.row)\"\n              v-if=\"scope.row.status > 2\"\n              type=\"text\"\n              size=\"small\"\n              class=\"el-icon-zoom-in\"\n              title=\"预览\"\n            />\n            <el-button\n              @click=\"exportPdf(scope.row)\"\n              v-if=\"scope.row.status > 2\"\n              type=\"text\"\n              size=\"small\"\n              class=\"el-icon-pdf-export commonIcon\"\n              title=\"导出pdf\"\n            />\n          </template>\n        </el-table-column>\n      </comp-table>\n    </el-white>\n\n    <!-- 详情/新增/修改 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <div>\n          <el-row>\n            <el-col :span=\"8\">\n              <el-form-item label=\"单位：\" prop=\"fgs\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.fgs\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择单位\"\n                  disabled\n                >\n                  <el-option\n                    v-for=\"item in dwSelected\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  />\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"配电站名称：\" prop=\"bdzmc\">\n                <el-select\n                  style=\"width: 100%\"\n                  filterable\n                  v-model=\"form.bdzmc\"\n                  :disabled=\"isDisabled\"\n                  :placeholder=\"isDisabled ? '' : '请选择'\"\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in pdsOptionsDataList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"编号：\" prop=\"bm\">\n                <el-input\n                  v-model=\"form.bm\"\n                  disabled\n                  placeholder=\"确认后编号自动生成\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"发令人：\"\n                prop=\"flr\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-input\n                  v-model=\"form.flr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请输入发令人\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"受令人：\"\n                prop=\"slr\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-input\n                  v-model=\"form.slr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请输入受令人\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"发令时间：\"\n                prop=\"flsj\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-date-picker\n                  v-model=\"form.flsj\"\n                  :disabled=\"isDisabledBj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  :placeholder=\"isDisabled ? '' : '选择日期时间'\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"操作开始时间：\"\n                prop=\"kssj\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-date-picker\n                  v-model=\"form.kssj\"\n                  :disabled=\"isDisabledBj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  :placeholder=\"isDisabled ? '' : '选择日期时间'\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"操作结束时间：\"\n                prop=\"jssj\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-date-picker\n                  v-model=\"form.jssj\"\n                  :disabled=\"isDisabledBj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  :placeholder=\"isDisabled ? '' : '选择日期时间'\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"操作人：\"\n                prop=\"czr\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-input\n                  v-model=\"form.czr\"\n                  :disabled=\"isDisabledBj\"\n                  :placeholder=\"isDisabledBj ? '' : '请输入操作人'\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"监护人：\"\n                prop=\"jhr\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-input\n                  v-model=\"form.jhr\"\n                  :disabled=\"isDisabledBj\"\n                  :placeholder=\"isDisabledBj ? '' : '请输入监护人'\"\n                />\n              </el-form-item>\n            </el-col>\n            <!--            <el-col :span=\"8\">-->\n            <!--              <el-form-item label=\"审核人：\" prop=\"pdshr\">-->\n            <!--                <el-input style=\"width:100%\" :disabled=\"true\" v-model=\"form.pdshr\" placeholder=\"请输入审核人\"/>-->\n            <!--              </el-form-item>-->\n            <!--            </el-col>-->\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作项数：\" prop=\"czxs\">\n                <el-input-number\n                  v-model=\"form.czxs\"\n                  disabled\n                  placeholder=\"请输入操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"已执行项数：\" prop=\"yzxczxs\">\n                <el-input-number\n                  v-model=\"form.yzxczxs\"\n                  disabled\n                  placeholder=\"请输入已执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"未执行项数：\" prop=\"wzxczxs\">\n                <el-input-number\n                  v-model=\"form.wzxczxs\"\n                  disabled\n                  placeholder=\"请输入未执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"是否已执行：\"\n                prop=\"sfyzx\"\n                :rules=\"\n                  form.status === '3'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-select\n                  v-model=\"form.sfyzx\"\n                  :placeholder=\"isDisabled ? '' : '请选择'\"\n                  :disabled=\"isDisabledBj\"\n                >\n                  <el-option\n                    v-for=\"item in sfyzxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"操作任务：\" prop=\"czrw\">\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"form.czrw\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请输入操作任务\"\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"备注：\" prop=\"bz\">\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"form.bz\"\n                  :disabled=\"isDisabled\"\n                  :placeholder=\"isDisabled ? '' : '请输入内容'\"\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <el-col :span=\"24\">\n              <el-form-item label=\"上传图片：\" v-if=\"\">\n                <el-upload\n                  :disabled=\"isDisabledBj\"\n                  action=\"\"\n                  ref=\"uploadImg\"\n                  accept=\"image/jpeg,image/jpg,image/png\"\n                  :headers=\"header\"\n                  :multiple=\"true\"\n                  :on-change=\"handleChange\"\n                  :data=\"uploadImgData\"\n                  :file-list=\"imgList\"\n                  :auto-upload=\"false\"\n                  list-type=\"picture-card\"\n                  :on-preview=\"handlePictureCardPreview\"\n                  :on-progress=\"handleProgress\"\n                  :on-remove=\"handleRemove\"\n                >\n                  <i class=\"el-icon-plus\"></i>\n                </el-upload>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n        <!--预览的内容-->\n        <div>\n          <div align=\"left\">\n            <el-upload\n              v-if=\"!isDisabled\"\n              action=\"\"\n              ref=\"upload\"\n              accept=\".xlsx\"\n              :limit=\"1\"\n              :auto-upload=\"false\"\n              :show-file-list=\"false\"\n              :on-change=\"importExcel\"\n            >\n              <el-button type=\"info\" @click.stop=\"handleYlChange\"\n                >预览</el-button\n              >\n              <el-button\n                type=\"success\"\n                icon=\"el-icon-download\"\n                @click.stop=\"exportExcel\"\n                >导出</el-button\n              >\n              <el-button type=\"success\" icon=\"el-icon-upload\">导入</el-button>\n              <div\n                slot=\"tip\"\n                class=\"el-upload__tip\"\n                style=\"width: 220px;color: red\"\n              >\n                先导出，再导入，只能上传当前页面导出的Excel文件\n              </div>\n            </el-upload>\n          </div>\n          <el-dialog :title=\"titleyl\" :visible.sync=\"yl\" append-to-body>\n            <!--第一个表格-->\n            <el-table\n              :data=\"propTableData.colFirst\"\n              disabled\n              border\n              stripe\n              style=\"width: 100%\"\n              :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n            >\n              <el-table-column\n                width=\"80\"\n                align=\"center\"\n                prop=\"xh\"\n                label=\"序号\"\n                sortable\n              >\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"序号\"\n                      disabled\n                      v-model=\"scope.row.xh\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"请输入操作项目\"\n                      disabled\n                      v-model=\"scope.row.czrw\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-dialog>\n        </div>\n        <!--列表-->\n        <div>\n          <div align=\"right\">\n            <el-checkbox @change=\"handleCheckAllChange\" :disabled=\"isDisabledBj\"\n              >全选</el-checkbox\n            >\n          </div>\n          <!--第一个表格-->\n          <el-table\n            :data=\"propTableData.colFirst\"\n            :disabled=\"isDisabled\"\n            height=\"300\"\n            border\n            stripe\n            style=\"width: 100%\"\n          >\n            <el-table-column width=\"70\" align=\"center\" label=\"换行操作\">\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"handleCurrentChange(scope.$index)\"\n                  type=\"text\"\n                  size=\"small\"\n                  :disabled=\"isDisabled\"\n                  >{{ flag === \"start\" ? \"点击换行\" : \"换到此行\" }}</el-button\n                >\n              </template>\n            </el-table-column>\n\n            <el-table-column\n              type=\"index\"\n              width=\"60\"\n              align=\"center\"\n              label=\"顺序号\"\n            >\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入操作项目\"\n                    :disabled=\"isDisabled\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              type=\"sfwc\"\n              width=\"120\"\n              label=\"是否完成\"\n              align=\"center\"\n            >\n              <template slot-scope=\"scope\">\n                <el-checkbox\n                  v-model=\"scope.row.sfwc\"\n                  :disabled=\"isDisabledBj\"\n                  @change=\"handleCheckChange\"\n                ></el-checkbox>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" width=\"80\">\n              <template slot=\"header\" slot-scope=\"scope\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  :disabled=\"isDisabled\"\n                  icon=\"el-icon-plus\"\n                  @click=\"listFirstAdd(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n              <template slot-scope=\"scope\">\n                <el-button\n                  type=\"text\"\n                  icon=\"el-icon-delete\"\n                  :disabled=\"isDisabled\"\n                  @click=\"listFirstDel(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button\n          v-if=\"\n            isDisabled &&\n              buttonNameShow &&\n              form.status > 0 &&\n              form.status !== '3'\n          \"\n          type=\"info\"\n          @click=\"getSbFsBj('rollback')\"\n          >退 回\n        </el-button>\n        <el-button\n          v-if=\"isDisabled && buttonNameShow\"\n          type=\"success\"\n          @click=\"getSbFsBj('complete')\"\n          >{{ buttonName }}\n        </el-button>\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"saveRow\"\n          >保 存</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <!--图片放大展示弹出框-->\n    <el-image-viewer\n      v-if=\"imgDialogVisible\"\n      :on-close=\"onClose\"\n      :url-list=\"dialogImageUrl\"\n      zIndex=\"3000\"\n    />\n\n    <!--  工作流  -->\n    <activiti\n      ref=\"activiti\"\n      :processData=\"processData\"\n      :isShow=\"isShow\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n    ></activiti>\n    <time-line\n      :value=\"timeLineShow\"\n      :timeData=\"timeData\"\n      @closeTimeLine=\"colseTimeLine\"\n    />\n    <!--流程图查看-->\n    <el-dialog\n      title=\"审批流程图\"\n      :visible.sync=\"openLoadingImg\"\n      width=\"56%\"\n      append-to-body\n      :close-on-click-modal=\"false\"\n      v-dialogDrag\n    >\n      <div>\n        <img :src=\"imgSrc\" style=\"width: 100%\" />\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport {\n  getCzpmxList,\n  getList,\n  remove,\n  saveOrUpdate\n} from \"@/api/yxgl/pdyxgl/pddzczp\";\nimport {\n  exportPdf,\n  exportWord,\n  previewFile,\n  updateById\n} from \"@/api/yxgl/bdyxgl/bddzczp\";\nimport api from \"@/utils/request\";\n//流程\nimport activiti from \"com/activiti_czp\";\nimport timeLine from \"com/timeLine\";\nimport { HistoryList } from \"@/api/activiti/processTask\";\nimport { getPdsOptionsDataList } from \"@/api/dagangOilfield/asset/pdsgl\";\nimport ElImageViewer from \"element-ui/packages/image/src/image-viewer\";\nimport { exportToExcel, importFromExcel } from \"@/components/common/excel.js\";\nexport default {\n  name: \"dzczp\",\n  components: { activiti, timeLine, ElImageViewer },\n  props: {\n    isTj: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      hasSuperRole: this.$store.getters.hasSuperRole,\n      pdsOptionsDataList: [],\n      isDisabledBj: true,\n      statusOptions: [\n        {\n          value: \"0\",\n          label: \"操作票填报\"\n        },\n        // {\n        //   value: \"1\",\n        //   label: \"班组审核\"\n        // },\n        {\n          value: \"2\",\n          label: \"分公司审核\"\n        },\n        {\n          value: \"3\",\n          label: \"操作票办结\"\n        },\n        {\n          value: \"4\",\n          label: \"结束\"\n        },{ label: \"作废\", value: \"7\" }\n      ],\n      buttonNameShow: false,\n      buttonName: \"\",\n      rules: {\n        fgs: [{ required: true, message: \"单位不能为空\", trigger: \"select\" }],\n        pdzmc: [{ required: true, message: \"单位不能为空\", trigger: \"select\" }],\n        // flr: [{ required: true, message: \"发令人不能为空\", trigger: \"change\" }],\n        // slr: [{ required: true, message: \"受令人不能为空\", trigger: \"change\" }],\n        // flsj: [\n        //   { required: true, message: \"发令时间不能为空\", trigger: \"change\" }\n        // ],\n        czrw: [\n          { required: true, message: \"操作任务不能为空\", trigger: \"change\" }\n        ]\n        // czr: [{ message: \"操作人不能为空\", trigger: \"blur\" }],\n        // jhr: [{ message: \"监护人不能为空\", trigger: \"blur\" }],\n        // kssj: [{ message: \"操作开始时间不能为空\", trigger: \"change\" }],\n        // jssj: [{ message: \"操作结束时间不能为空\", trigger: \"change\" }]\n      },\n      bjr: \"\",\n      // 是否已执行下拉框\n      sfyzxList: [\n        { label: \"已执行\", value: \"已执行\" },\n        { label: \"未执行\", value: \"未执行\" }\n      ],\n      // 获取当前登录人账号\n      currentUser: this.$store.getters.name,\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"czplc\",\n        businessKey: \"\",\n        businessType: \"倒闸操作票\",\n        variables: {},\n        defaultFrom: true,\n        nextUser: \"\",\n        processType: \"complete\"\n      },\n      //工作流弹窗\n      isShow: false,\n      //流程图查看\n      openLoadingImg: false,\n      imgSrc: \"\", //流程图查看地址\n      timeData: [],\n      timeLineShow: false,\n      //弹出框标题\n      activitiOption: { title: \"\" },\n      titleyl: \"\",\n      //图片地址url\n      dialogImageUrl: \"\",\n      //展示图片dialog控制\n      imgDialogVisible: false,\n      //上传图片时的携带的其他参数\n      uploadImgData: {\n        businessId: \"\" //携带的表单主键id\n      },\n      //上传图片时的请求头\n      header: {},\n      //图片list\n      imgList: [],\n      loginForm: {\n        userName: \"\",\n        password: \"\",\n        rememberMe: false,\n        code: \"6666\",\n        uuid: \"\"\n      },\n      selection: [],\n      yl: false,\n      // 多选框选中的id\n      ids: [],\n      single: true,\n      multiple: true,\n      //弹出框中表格数据\n      propTableData: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      dwSelected: [{ label: \"配电运维分公司\", value: \"3013\" }],\n      //form表单\n      form: {\n        status: \"\",\n        kssj: \"\",\n        jssj: \"\",\n        czrw: \"\",\n        flr: \"\",\n        slr: \"\",\n        flsj: \"\",\n        lx: 3, //配电\n        colFirst: [],\n        pdzmc: \"\",\n        czxs: 0,\n        yzxczxs: 0,\n        wzxczxs: 0\n      },\n      formCzp: {\n        pdshr: \"\",\n        yj: \"\"\n      },\n      //详情弹框是否显示\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          bm: \"\",\n          slr: \"\",\n          flr: \"\",\n          czsjArr: [],\n          flsjArr: [],\n        },\n        fieldList: [\n          { label: \"编号\", value: \"bm\", type: \"input\", clearable: true },\n          {\n            label: \"操作时间\",\n            value: \"czsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"受令人\", type: \"input\", value: \"slr\", clearable: true },\n          { label: \"发令人\", type: \"input\", value: \"flr\", clearable: true },\n          {\n            label: \"发令时间\",\n            value: \"flsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          {\n            label: \"状态\",\n            value: \"status\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            clearable: true,\n            options: [\n              {\n                value: \"0\",\n                label: \"操作票填报\"\n              },\n              /*{\n                value: '1',\n                label: '班组审核'\n              },*/\n              {\n                value: \"2\",\n                label: \"分公司审核\"\n              },\n              {\n                value: \"3\",\n                label: \"操作票办结\"\n              },{ label: \"作废\", value: \"7\" }\n            ]\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"编号\", prop: \"bm\", minWidth: \"80\" },\n          { label: \"状态\", prop: \"statusCn\", minWidth: \"80\" },\n          { label: \"配电站名称\", prop: \"pdzCn\", minWidth: \"120\" },\n          { label: \"操作开始时间\", prop: \"kssj\", minWidth: \"120\" },\n          { label: \"操作结束时间\", prop: \"jssj\", minWidth: \"120\" },\n          { label: \"操作任务\", prop: \"czrw\", minWidth: \"120\" },\n          // {label: '审核人', prop: 'pdshr', minWidth: '100'},\n          { label: \"发令人\", prop: \"flr\", minWidth: \"100\" },\n          { label: \"受令人\", prop: \"slr\", minWidth: \"100\" },\n          { label: \"发令时间\", prop: \"flsj\", minWidth: \"120\" }\n        ]\n      },\n      selectRows: [],\n      params: {\n        //配电\n        lx: 3,\n        status: \"0,1,2,3,4\" //状态。将结束状态的过滤掉\n      },\n      //换行操作\n      oldIndex: undefined,\n      newIndex: undefined,\n      flag: \"start\"\n    };\n  },\n  mounted() {\n    //获取token\n    this.header.token = getToken();\n    this.getPdsOptionsDataList();\n    //获取列表数据\n    this.getData(this.$route.query);\n  },\n  methods: {\n    //作废票\n    nullifyGzp(objId) {\n      this.$confirm(\"票作废后只能查看，不能进行任何操作，确认作废吗?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(async () => {\n          let { code } = await updateById({ status: 7, objId: objId });\n          if (code === \"0000\") {\n            this.$message.success(\"操作成功!!\");\n            this.getData();\n          }\n        })\n        .catch(() => {});\n    },\n    exportExcel() {\n      let excelData = this.propTableData.colFirst.map(item => ({ \"操作项目\": item.czrw}));\n      exportToExcel(excelData, \"操作项目.xlsx\");\n    },\n    importExcel(file, fileList) {\n      let fileName = file.name\n      if (!fileName.includes(\"操作项目\")) {\n        this.msgError(\"文件有误，请检查\")\n        this.$refs.upload.clearFiles()\n        return\n      }\n      importFromExcel(file)\n        .then(data => {\n          this.ids = this.propTableData.colFirst.map(item => item.objId)\n          this.propTableData.colFirst = data.map(item => ({xh: item.__rowNum__ , czrw: item[\"操作项目\"]}));\n        })\n        .catch(error => {\n          console.error(\"导入失败\", error);\n        });\n      this.$refs.upload.clearFiles()\n    },\n    //获取配电室下拉框数据\n    getPdsOptionsDataList() {\n      getPdsOptionsDataList({}).then(res => {\n        this.pdsOptionsDataList = res.data;\n      });\n    },\n    getShow() {\n      this.buttonNameShow = false;\n      switch (this.form.status) {\n        case \"0\":\n          this.buttonName = \"上 报\";\n          if (this.currentUser === this.form.createBy) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case \"1\":\n          this.buttonName = \"提 交\";\n          if (this.form.bzspr === this.currentUser) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case \"2\":\n          this.buttonName = \"提 交\";\n          if (this.form.fgsspr === this.currentUser) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case \"3\":\n          this.buttonName = \"办 结\";\n          if (this.form.bjr === this.currentUser) {\n            this.buttonNameShow = true;\n            this.isDisabledBj = false;\n          }\n          break;\n      }\n    },\n    filterReset() {\n      this.params = {\n        //配电\n        lx: 3,\n        status: \"0,1,2,3,4\" //状态。将结束状态的过滤掉\n      };\n    },\n    //预览文件\n    async previewFile(row) {\n      try {\n        let exportData = { ...row };\n        await previewFile(exportData, \"pdzdzczp\");\n      } catch (e) {\n        this.$message.error(\"预览失败！\");\n      }\n    },\n    //清空\n    change(e) {\n      this.$forceUpdate();\n    },\n    //关闭弹窗\n    closeActiviti() {\n      if (this.$refs.activiti.$refs.form) {\n        this.$refs.activiti.$refs.form.resetFields();\n      }\n      this.isShow = false;\n    },\n    //关闭流程查看页面\n    colseTimeLine() {\n      this.timeLineShow = false;\n    },\n    async showTimeLine(row) {\n      this.processData.businessKey = row.objId;\n      let { code, data } = await HistoryList(this.processData);\n      this.timeData = data;\n      this.timeLineShow = true;\n    },\n    //流程图片查看\n    showProcessImg(row) {\n      console.log(row);\n      this.openLoadingImg = true;\n      this.imgSrc =\n        \"/activiti-api/process/read-resource?processDefinitionKey=czplc&businessKey=\" +\n        row.objId +\n        \"&t=\" +\n        new Date().getTime();\n    },\n    //改造后的上传多个图片文件\n    uploadForm() {\n      var newUrl = []; //用来存放当前未改动过的图片url,此图片不进行删除处理，其余当前业务id下面的图片数据将进行删除\n      // console.log(this.$refs.uploadImg.fileList)\n      var imageType = [\"png\", \"jpg\"];\n      console.log(\"开始上传图片\");\n      console.log(this.imgList);\n      const formData = new FormData();\n      // 因为要传一个文件数组过去，所以要循环append\n      this.imgList.forEach(file => {\n        if (file.raw == undefined) {\n          console.log(file.url);\n          newUrl.push(file.url);\n        }\n        formData.append(\"files\", file.raw);\n      });\n      formData.append(\"businessId\", this.uploadImgData.businessId); // 自定义参数\n      formData.append(\"newUrl\", newUrl); // 未改动过的图片url\n      formData.append(\"type\", imageType); // 未改动过的图片url\n      api\n        .requestPost(\"/isc-api/file/uploadFiles\", formData, 1)\n        .then(res => {\n          console.log(\"上传图片成功的反回值\");\n          console.log(res);\n          // 清空图片列表（一定要清空，否则上传成功后还是会调用handleChange（）函数，上传成功后列表中还存在图片）\n          this.imgList = [];\n          //查询接口数据\n          this.getData();\n        })\n        .catch(res => {\n          this.$message.success(\"图片上传失败！\");\n        });\n    },\n    // 选择文件时，往fileList里添加\n    handleChange(file, fileList) {\n      this.imgList = fileList;\n    },\n    handleProgress(event, file, fileList) {\n      console.log(\"event\", event);\n      console.log(\"file\", file);\n      console.log(\"fileList\", fileList);\n    },\n    //图片移除\n    handleRemove(file, fileList) {\n      this.imgList = fileList;\n    },\n    //图片放大\n    handlePictureCardPreview(file) {\n      this.dialogImageUrl = [file.url];\n      this.imgDialogVisible = true;\n    },\n    onClose() {\n      this.imgDialogVisible = false;\n    },\n    // 预览弹框\n    handleYlChange() {\n      this.titleyl = \"查看操作项目\";\n      this.yl = true;\n    },\n    // 全选框\n    handleCheckAllChange(val) {\n      if (val) {\n        this.form.yzxczxs = this.form.czxs;\n        this.form.wzxczxs = 0;\n      } else {\n        this.form.wzxczxs = this.form.czxs;\n        this.form.yzxczxs = 0;\n      }\n      this.propTableData.colFirst.forEach(item => {\n        item.sfwc = val;\n      });\n    },\n    handleCurrentChange(index) {\n      console.log(index);\n      if (this.flag === \"start\") {\n        this.oldIndex = index;\n        this.flag = \"end\";\n        this.$message.info(\"请点击需要更换到的行\");\n      } else if (this.flag == \"end\") {\n        this.newIndex = index;\n        this.flag = \"start\";\n        const oldCurrentRow = this.propTableData.colFirst.splice(\n          this.oldIndex,\n          1\n        )[0];\n        this.propTableData.colFirst.splice(this.newIndex, 0, oldCurrentRow);\n        this.$message.success(\"更换顺序成功\");\n      }\n    },\n    handleCheckChange(val) {\n      if (val) {\n        ++this.form.yzxczxs;\n      } else {\n        --this.form.yzxczxs;\n      }\n      this.form.wzxczxs = this.form.czxs - this.form.yzxczxs;\n    },\n    //工作流回传数据\n    async todoResult(data) {\n      let row = {\n        objId: data.businessKey,\n        isStart: 1,\n        isBack: data.processType === \"rollback\" ? 1 : 0\n      };\n      if (data.processType === \"rollback\") {\n        switch (data.activeTaskName) {\n          case \"操作票填报\":\n            row.status = \"0\";\n            break;\n        }\n      } else {\n        switch (data.activeTaskName) {\n          case \"分公司审核\":\n            row.status = \"2\";\n            row.fgsspr = data.nextUser;\n            break;\n          case \"操作票办结\":\n            row.status = \"3\";\n            row.bjr = data.nextUser;\n            break;\n          case \"结束\":\n            row.status = \"4\";\n            break;\n        }\n      }\n      let { code } = await updateById(row);\n      if (code === \"0000\") {\n        await this.getData();\n      } else {\n        this.$message.warning(\"操作失败\");\n      }\n    },\n    //上报发送办结\n    async getSbFsBj(type) {\n      let row = { ...this.form };\n      if (type === \"complete\") {\n        switch (row.status) {\n          case \"0\":\n            this.isShowDetails = false;\n            this.processData.variables.pass = true;\n            this.processData.businessKey = row.objId;\n            this.processData.processType = type;\n            // this.processData.dw = row.fgs;\n            this.processData.rylx = \"分公司审核人\";\n            this.activitiOption.title = \"提交\";\n            this.processData.defaultFrom = true;\n            this.processData.personGroupId = 14;\n            this.isShow = true;\n            break;\n          case \"2\":\n            this.isShowDetails = false;\n            this.processData.variables.pass = true;\n            this.processData.businessKey = row.objId;\n            this.processData.processType = type;\n            this.activitiOption.title = \"提交\";\n            this.processData.defaultFrom = true;\n            this.processData.personGroupId = 15;\n            // this.processData.dw = row.fgs;\n            this.processData.rylx = \"办结人\";\n            this.isShow = true;\n            break;\n          case \"3\":\n            this.form.colFirst = this.propTableData.colFirst;\n            this.form.objIdList = this.ids;\n            await this.$refs[\"form\"].validate(async valid => {\n              if (valid) {\n                this.uploadImgData.businessId = this.form.objId;\n                this.uploadForm();\n                await saveOrUpdate(this.form).then(res => {\n                  if (res.code === \"0000\") {\n                    this.getData();\n                    this.isShowDetails = false;\n                    this.processData.variables.pass = true;\n                    this.processData.businessKey = row.objId;\n                    this.processData.processType = type;\n                    this.activitiOption.title = \"办结\";\n                    this.processData.defaultFrom = false;\n                    this.isShow = true;\n                  } else {\n                    this.$message.error(\"失败\");\n                  }\n                });\n              }\n            });\n        }\n      } else {\n        this.isShowDetails = false;\n        this.processData.businessKey = row.objId;\n        this.processData.processType = type;\n        this.activitiOption.title = \"回退原因填写\";\n        this.processData.defaultFrom = true;\n        this.processData.variables.pass = false;\n        this.isShow = true;\n      }\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        param.mySorts = [{ prop: \"updateTime\", asc: false }];\n        if (!param.status) {\n          param.status = \"0,1,2,3,4\";\n        }\n        const { data, code } = await getList(param);\n        if (code === \"0000\") {\n          for (let i of data.records) {\n            i.fgsmc = \"配电运维分公司\";\n            this.statusOptions.forEach(element => {\n              if (i.status === element.value) {\n                i.statusCn = element.label;\n              }\n            });\n          }\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //确定按钮\n    async saveRow() {\n      await this.$refs[\"form\"].validate(async valid => {\n        if (valid) {\n          try {\n            //保存列表序号\n            let tableArr = this.propTableData.colFirst;\n            for (var i = 0; i < tableArr.length; i++) {\n              tableArr[i].xh = i + 1;\n            }\n            this.form.colFirst = tableArr;\n            this.form.objIdList = this.ids;\n            this.form.status = \"0\";\n            let { code, data } = await saveOrUpdate(this.form);\n            if (code === \"0000\") {\n              this.$message.success(\"操作成功\");\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              await this.getData();\n              this.isShowDetails = false;\n            }\n          } catch (e) {\n            console.log(e);\n          }\n        }\n      });\n    },\n    //修改按钮\n    getUpdate(row) {\n      this.getCzpmx(row);\n      this.title = \"配电倒闸操作票修改\";\n      this.isDisabled = false;\n      this.form = { ...row };\n      //图片结合\n      this.imgList = this.form.imgList;\n      this.isDisabledBj = false;\n      this.isShowDetails = true;\n      this.getShow();\n    },\n    //详情\n    getInfo(row) {\n      this.getCzpmx(row);\n      this.title = \"配电操作票详情查看\";\n      this.form = { ...row };\n      //图片结合\n      this.imgList = this.form.imgList;\n      this.isDisabled = true;\n      this.isDisabledBj = true;\n      this.isShowDetails = true;\n      this.getShow();\n    },\n    //新增\n    getInster() {\n      this.propTableData.colFirst = [];\n      this.title = \"配电倒闸操作票增加\";\n      this.isDisabled = false;\n      this.form = { fgs: this.$store.getters.deptId.toString() };\n      this.form.status = \"新建\";\n      //配电\n      this.form.lx = 3;\n      this.isShowDetails = true;\n    },\n    //删除按钮\n    async deleteRow(row) {\n      let objId = \"\";\n      if (row.objId) {\n        objId = row.objId;\n      } else {\n        objId = this.ids[0];\n      }\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove(objId).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              //重置page页从1开始\n              // this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    //表格新增\n    listFirstAdd(index) {\n      let row = {\n        czxm: \"\",\n        sfwc: \"\"\n      };\n      this.propTableData.colFirst.push(row);\n      this.propTableData.sel = row;\n      this.form.czxs = this.propTableData.colFirst.length;\n      this.form.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //表格删除\n    listFirstDel(index, row) {\n      this.ids.push(row.objId);\n      this.propTableData.colFirst.splice(index, 1);\n      console.log(\"splice index == \", index);\n      this.form.czxs = this.propTableData.colFirst.length;\n      this.form.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false;\n    },\n    //选择行\n    selectChange(selection) {\n      this.ids = selection.map(item => item.objId);\n      this.selection = selection;\n      this.single = selection.length !== 1;\n      this.form = this.selection[0];\n    },\n    /**\n     *获取操作票明细\n     */\n    async getCzpmx(row) {\n      try {\n        const { data, code } = await getCzpmxList({ objId: row.objId });\n        if (code === \"0000\") {\n          //给list添加字段\n          data.forEach(item => {\n            this.$set(item, \"isSet\", true);\n          });\n          this.propTableData.colFirst = data;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //导出word\n    async exportWord(row) {\n      try {\n        let exportData = { ...row };\n        await exportWord(exportData, \"pdzdzczp\", \"配电倒闸操作票.docx\");\n      } catch (e) {\n        this.$message.error(\"导出失败！\");\n      }\n    },\n    //导出Pdf\n    async exportPdf(row) {\n      try {\n        let exportData = { ...row };\n        await exportPdf(exportData, \"pdzdzczp\", \"配电倒闸操作票.pdf\");\n      } catch (e) {\n        this.$message.error(\"导出失败！\");\n      }\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.item {\n  width: 8.5rem;\n  height: 1.25rem;\n}\n</style>\n"]}]}