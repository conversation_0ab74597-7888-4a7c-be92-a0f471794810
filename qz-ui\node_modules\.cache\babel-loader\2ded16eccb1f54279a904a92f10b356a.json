{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\bdgl\\bdztz.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\bdgl\\bdztz.vue", "mtime": 1706897324520}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["bdztz.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAusBA;;AACA;;AAKA;;AACA;;AACA;;AACA;;AACA;;AAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,WAAA,EAAA;AAAA,GAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,UAAA,EAAA;AADA,OADA;AAIA;AACA,MAAA,UAAA,EAAA,EALA;AAMA;AACA,MAAA,KAAA,EAAA,EAPA;AAQA;AACA,MAAA,wBAAA,EAAA,EATA;AAUA;AACA,MAAA,SAAA,EAAA,EAXA;AAYA;AACA,MAAA,QAAA,EAAA,EAbA;AAcA;AACA,MAAA,wBAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,OAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAJA,CAfA;AAqBA,MAAA,OAAA,EAAA,CACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA,OArBA;AAgCA,MAAA,OAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,EAaA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAbA,CAhCA;AAkDA;AACA,MAAA,UAAA,EAAA,KAnDA;AAoDA;AACA,MAAA,MAAA,EAAA,EArDA;AAsDA;AACA,MAAA,MAAA,EAAA,KAvDA;AAwDA;AACA,MAAA,cAAA,EAAA,KAzDA;AA2DA;AACA,MAAA,aAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,SAAA;AAAA,QAAA,MAAA,EAAA,QAAA;AAAA,QAAA,KAAA,EAAA,EAAA;AAAA,QAAA,IAAA,EAAA;AAAA,OADA,EAEA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,MAAA,EAAA,MAFA;AAGA,QAAA,KAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA,QAJA;AAKA,QAAA,OAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA;AALA,OAFA,EAYA;AAAA,QAAA,KAAA,EAAA,SAAA;AAAA,QAAA,MAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA,EAAA;AAAA,QAAA,IAAA,EAAA;AAAA,OAZA,EAaA;AAAA,QAAA,KAAA,EAAA,QAAA;AAAA,QAAA,MAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA,EAAA;AAAA,QAAA,IAAA,EAAA;AAAA,OAbA,EAcA;AAAA,QAAA,KAAA,EAAA,QAAA;AAAA,QAAA,MAAA,EAAA,QAAA;AAAA,QAAA,KAAA,EAAA,EAAA;AAAA,QAAA,IAAA,EAAA;AAAA,OAdA,EAeA;AAAA,QAAA,KAAA,EAAA,QAAA;AAAA,QAAA,MAAA,EAAA,QAAA;AAAA,QAAA,KAAA,EAAA,EAAA;AAAA,QAAA,IAAA,EAAA;AAAA,OAfA,EAgBA;AAAA,QAAA,KAAA,EAAA,QAAA;AAAA,QAAA,MAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA,EAAA;AAAA,QAAA,IAAA,EAAA;AAAA,OAhBA,EAiBA;AAAA,QAAA,KAAA,EAAA,QAAA;AAAA,QAAA,MAAA,EAAA,QAAA;AAAA,QAAA,KAAA,EAAA,EAAA;AAAA,QAAA,IAAA,EAAA;AAAA,OAjBA,EAkBA;AAAA,QAAA,KAAA,EAAA,QAAA;AAAA,QAAA,MAAA,EAAA,QAAA;AAAA,QAAA,KAAA,EAAA,EAAA;AAAA,QAAA,IAAA,EAAA;AAAA,OAlBA,CA5DA;AAgFA;AACA,MAAA,QAAA,EAAA,EAjFA;AAkFA;AACA,MAAA,WAAA,EAAA,EAnFA;AAqFA,MAAA,OAAA,EAAA,KArFA;AAsFA,MAAA,OAAA,EAAA,KAtFA;AAuFA,MAAA,OAAA,EAAA,KAvFA;AAwFA,MAAA,UAAA,EAAA,EAxFA;AAyFA,MAAA,WAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,OAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,EAFA;AAGA,UAAA,KAAA,EAAA,EAHA;AAIA,UAAA,OAAA,EAAA,EAJA;AAKA,UAAA,OAAA,EAAA,EALA;AAMA,UAAA,IAAA,EAAA;AANA,SADA;AASA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,UAHA;AAIA,UAAA,QAAA,EAAA,IAJA;AAKA,UAAA,OAAA,EAAA;AALA,SADA,EAQA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SARA,EASA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,SAHA;AAIA,UAAA,QAAA,EAAA,IAJA;AAKA,UAAA,OAAA,EAAA;AALA,SATA,EAgBA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,SAHA;AAIA,UAAA,QAAA,EAAA,IAJA;AAKA,UAAA,OAAA,EAAA;AALA,SAhBA;AATA,OAzFA;AA2HA;AACA,MAAA,gBAAA,EAAA,EA5HA;AA6HA;AACA,MAAA,iBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA;AACA;;;;;;;;;;;AARA;AAZA,OA9HA;AA+JA,MAAA,aAAA,EAAA;AACA,QAAA,KAAA,EAAA,SADA;AAEA,QAAA,IAAA,EAAA,SAFA;AAGA,QAAA,QAAA,EAAA,EAHA;AAIA,QAAA,OAAA,EAAA;AAJA,OA/JA;AAqKA,MAAA,WAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,OAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,EAFA;AAGA,UAAA,KAAA,EAAA,EAHA;AAIA,UAAA,OAAA,EAAA,EAJA;AAKA,UAAA,OAAA,EAAA,EALA;AAMA,UAAA,IAAA,EAAA;AANA,SADA;AASA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,SAHA;AAIA,UAAA,QAAA,EAAA,IAJA;AAKA,UAAA,OAAA,EAAA;AALA,SADA,EAQA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,SAHA;AAIA,UAAA,QAAA,EAAA,IAJA;AAKA,UAAA,OAAA,EAAA;AALA,SARA,EAeA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,SAHA;AAIA,UAAA,QAAA,EAAA,IAJA;AAKA,UAAA,OAAA,EAAA;AALA,SAfA,EAsBA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,SAHA;AAIA,UAAA,QAAA,EAAA,IAJA;AAKA,UAAA,OAAA,EAAA;AALA,SAtBA;AATA,OArKA;AA8MA;AACA,MAAA,eAAA,EAAA,MA/MA;AAiNA;AACA,MAAA,QAAA,EAAA;AACA,QAAA,UAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA,SAFA;AAGA,QAAA,MAAA,EAAA,SAHA;AAIA,QAAA,MAAA,EAAA,SAJA;AAKA,QAAA,IAAA,EAAA,SALA;AAMA,QAAA,KAAA,EAAA,SANA;AAOA,QAAA,IAAA,EAAA,SAPA;AAQA,QAAA,IAAA,EAAA,SARA;AASA,QAAA,IAAA,EAAA,SATA;AAUA,QAAA,EAAA,EAAA,SAVA;AAWA,QAAA,EAAA,EAAA,SAXA;AAYA,QAAA,IAAA,EAAA,SAZA;AAaA,QAAA,IAAA,EAAA,SAbA;AAcA,QAAA,EAAA,EAAA,SAdA;AAeA,QAAA,GAAA,EAAA,SAfA;AAgBA,QAAA,IAAA,EAAA,SAhBA;AAiBA,QAAA,IAAA,EAAA,SAjBA;AAkBA,QAAA,IAAA,EAAA,SAlBA;AAmBA,QAAA,IAAA,EAAA,SAnBA;AAoBA,QAAA,IAAA,EAAA,SApBA;AAqBA,QAAA,IAAA,EAAA,SArBA;AAsBA,QAAA,IAAA,EAAA,SAtBA;AAuBA,QAAA,MAAA,EAAA,SAvBA;AAwBA,QAAA,QAAA,EAAA,SAxBA;AAyBA,QAAA,IAAA,EAAA,SAzBA;AA0BA,QAAA,IAAA,EAAA,SA1BA;AA2BA,QAAA,IAAA,EAAA,SA3BA;AA4BA,QAAA,KAAA,EAAA,SA5BA;AA6BA,QAAA,OAAA,EAAA,SA7BA;AA6BA;AACA,QAAA,IAAA,EAAA,SA9BA;AA8BA;AACA,QAAA,IAAA,EAAA,SA/BA;AA+BA;AACA,QAAA,OAAA,EAAA,SAhCA;AAgCA;AACA,QAAA,QAAA,EAAA,SAjCA;AAiCA;AACA,QAAA,UAAA,EAAA,SAlCA;AAkCA;AACA,QAAA,IAAA,EAAA,SAnCA;AAmCA;AACA,QAAA,IAAA,EAAA,SApCA;AAoCA;AACA,QAAA,IAAA,EAAA,SArCA;AAqCA;AACA,QAAA,MAAA,EAAA,SAtCA;AAsCA;AACA,QAAA,EAAA,EAAA,SAvCA;AAuCA;AACA,QAAA,IAAA,EAAA,SAxCA;AAwCA;AACA,QAAA,IAAA,EAAA,SAzCA;AAyCA;AACA,QAAA,IAAA,EAAA,SA1CA;AA0CA;AACA,QAAA,IAAA,EAAA,SA3CA;AA2CA;AACA,QAAA,IAAA,EAAA,SA5CA;AA4CA;AACA,QAAA,KAAA,EAAA,SA7CA;AA6CA;AACA,QAAA,IAAA,EAAA,SA9CA;AA8CA;AACA,QAAA,KAAA,EAAA,SA/CA;AA+CA;AACA,QAAA,IAAA,EAAA,SAhDA;AAgDA;AACA,QAAA,IAAA,EAAA,SAjDA;AAiDA;AACA,QAAA,IAAA,EAAA,SAlDA;AAkDA;AACA,QAAA,OAAA,EAAA,SAnDA;AAmDA;AACA,QAAA,KAAA,EAAA,SApDA;AAoDA;AACA,QAAA,QAAA,EAAA,SArDA;AAqDA;AACA,QAAA,OAAA,EAAA,SAtDA;AAsDA;AACA,QAAA,MAAA,EAAA,SAvDA;AAuDA;AACA,QAAA,MAAA,EAAA,SAxDA;AAwDA;AACA,QAAA,EAAA,EAAA,SAzDA,CAyDA;;AAzDA,OAlNA;AA6QA,MAAA,cAAA,EAAA;AACA;AACA,QAAA,MAAA,EAAA,SAFA;AAGA,QAAA,OAAA,EAAA,CAHA;AAIA,QAAA,QAAA,EAAA;AAJA,OA7QA;AAmRA,MAAA,gBAAA,EAAA;AACA,QAAA,IAAA,EAAA,SADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,QAAA,EAAA;AAHA,OAnRA;AAwRA;AACA,MAAA,oBAAA,EAAA,IAzRA;AA0RA;AACA,MAAA,aAAA,EAAA,QA3RA;AA4RA;AACA,MAAA,YAAA,EAAA,IA7RA;AA8RA;AACA,MAAA,WAAA,EAAA,KA/RA;AAgSA;AACA,MAAA,WAAA,EAAA,KAjSA;AAkSA;AACA,MAAA,iBAAA,EAAA,KAnSA;AAoSA;AACA,MAAA,oBAAA,EAAA,KArSA;AAsSA;AACA,MAAA,mBAAA,EAAA,KAvSA;AAwSA;AACA,MAAA,IAAA,EAAA,EAzSA;AA2SA,MAAA,OAAA,EAAA,KA3SA;AA4SA;AACA,MAAA,WAAA,EAAA,EA7SA;AA+SA;AACA,MAAA,WAAA,EAAA,EAhTA;AAkTA;AACA,MAAA,cAAA,EAAA,IAnTA;AAoTA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,OAAA,EAAA,EAHA;AAIA,QAAA,QAAA,EAAA,EAJA;AAKA,QAAA,MAAA,EAAA;AALA,OArTA;AA4TA,MAAA,IAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,CA5TA;AA0UA,MAAA,UAAA,EAAA,IA1UA;AA2UA,MAAA,KAAA,EAAA;AACA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA;AACA,QAAA,KAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CALA;AAQA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CARA;AAWA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AAXA;AA3UA,KAAA;AAyVA,GA7VA;AA8VA,EAAA,KAAA,EAAA;AACA;AACA,IAAA,UAFA,sBAEA,GAFA,EAEA;AACA,WAAA,KAAA,CAAA,IAAA,CAAA,MAAA,CAAA,GAAA;AACA;AAJA,GA9VA;AAoWA,EAAA,OApWA,qBAoWA;AACA;AACA,SAAA,cAAA,GAFA,CAGA;;AACA,SAAA,aAAA;AACA,SAAA,aAAA;AACA,SAAA,OAAA,GANA,CAOA;;AACA,SAAA,gBAAA,mCAAA,KAAA,iBAAA;AACA,GA7WA;AA8WA,EAAA,OA9WA,qBA8WA;AACA;AACA,SAAA,MAAA,CAAA,KAAA,GAAA,qBAAA;AACA,GAjXA;AAkXA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,UAFA,sBAEA,KAFA,EAEA,IAFA,EAEA;AACA,UAAA,CAAA,KAAA,EAAA,OAAA,IAAA;AACA,aAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,KAAA,MAAA,CAAA,CAAA;AACA,KALA;AAMA;AACA,IAAA,cAPA,4BAOA;AAAA;;AACA,iCAAA,KAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,KAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KAXA;;AAYA;;;AAGA,IAAA,aAfA,2BAeA;AAAA;;AACA,+BAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAAA,QAAA,EAAA;AACA,SAFA;AAGA,QAAA,MAAA,CAAA,wBAAA,GAAA,GAAA,CAAA,IAAA;;AACA,QAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,IAAA,KAAA,EAAA;AACA,mBAAA,IAAA,CAAA,OAAA,GAAA,MAAA,CAAA,wBAAA;AACA;AACA,SAJA;AAKA,OAVA;AAWA,KA3BA;;AA4BA;;;AAGA,IAAA,aA/BA,2BA+BA;AAAA;;AACA,2CAAA,IAAA,CAAA,SAAA,CAAA,IAAA,CAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAAA,QAAA,EAAA;AACA,SAFA;AAGA,QAAA,MAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA;AACA,OALA;AAMA,KAtCA;AAuCA;AACA,IAAA,eAxCA,6BAwCA;AAAA;;AACA,iCAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KA5CA;AA6CA;AACA,IAAA,OA9CA,mBA8CA,MA9CA,EA8CA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,KADA,+DACA,MAAA,CAAA,cADA,GACA,MADA;AAAA;AAAA,uBAEA,uBAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,MAAA,CAAA,iBAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,kBAAA,MAAA,CAAA,iBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA,CAFA,CAGA;;AACA,kBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA,yBAAA,gBAAA,mCAAA,KAAA,iBAAA;AACA,mBAFA;AAGA,iBAPA,CAFA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,KAxDA;AAyDA,IAAA,MAzDA,oBAyDA;AAAA;;AACA,WAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,8BAAA,MAAA,CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,cAAA,MAAA,CAAA,UAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;;AACA,cAAA,MAAA,CAAA,YAAA;;AACA,cAAA,MAAA,CAAA,oBAAA,GAAA,KAAA;;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,cAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,cAAA,MAAA,CAAA,cAAA;;AACA,cAAA,MAAA,CAAA,OAAA;AACA,aARA,MAQA;AACA,cAAA,MAAA,CAAA,oBAAA,GAAA,KAAA;AACA;AACA,WAZA;AAaA,SAdA,MAcA;AACA,iBAAA,KAAA;AACA;AACA,OAlBA;AAmBA,KA7EA;;AA8EA;;;AAGA,IAAA,qBAjFA,iCAiFA,SAjFA,EAiFA;AACA,WAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,KAAA;AAAA,OAAA,CAAA;AACA,WAAA,MAAA,GAAA,SAAA,CAAA,MAAA,KAAA,CAAA;AACA,WAAA,QAAA,GAAA,CAAA,SAAA,CAAA,MAAA;AACA,KArFA;;AAsFA;;;AAGA,IAAA,SAzFA,uBAyFA;AAAA;;AACA,UAAA,KAAA,GAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,UAAA,iBAAA,EAAA,IADA;AAEA,UAAA,gBAAA,EAAA,IAFA;AAGA,UAAA,IAAA,EAAA;AAHA,SAAA,EAIA,IAJA,CAIA,YAAA;AACA,iCAAA,MAAA,CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,cAAA;;AACA,YAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,YAAA,MAAA,CAAA,OAAA;AACA,WARA;AASA,SAdA;AAeA,OAhBA,MAgBA;AACA,aAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA;AACA,KAhHA;AAkHA;AACA,IAAA,SAnHA,qBAmHA,GAnHA,EAmHA;AACA,WAAA,SAAA,qFAAA;AAAA;AAAA;AAAA;AAAA;AACA,qBAAA,WAAA;AACA,qBAAA,QAAA,mCAAA,GAAA;AACA,qBAAA,QAAA,CAAA,UAAA,GAAA,EAAA;AAHA;AAAA,uBAIA,KAAA,WAAA,EAJA;;AAAA;AAKA,qBAAA,UAAA,GAAA,KAAA;AACA,qBAAA,KAAA,GAAA,SAAA;AACA,qBAAA,oBAAA,GAAA,IAAA;;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAAA;AASA,KA7HA;AA8HA;AACA,IAAA,UA/HA,sBA+HA,GA/HA,EA+HA;AACA,WAAA,SAAA,qFAAA;AAAA;AAAA;AAAA;AAAA;AACA,qBAAA,WAAA;AACA,qBAAA,QAAA,mCAAA,GAAA;AACA,qBAAA,QAAA,CAAA,UAAA,GAAA,EAAA;AAHA;AAAA,uBAIA,KAAA,WAAA,EAJA;;AAAA;AAKA,qBAAA,oBAAA,GAAA,IAAA;AACA,qBAAA,UAAA,GAAA,IAAA;AACA,qBAAA,KAAA,GAAA,SAAA;;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAAA;AASA,KAzIA;AA0IA;AACA,IAAA,iBA3IA,+BA2IA;AACA,WAAA,iBAAA,GAAA,IAAA;AACA,KA7IA;AA8IA,IAAA,WA9IA,yBA8IA;AACA,UAAA,KAAA,KAAA,CAAA,MAAA,EAAA;AACA,aAAA,KAAA,CAAA,MAAA,CAAA,UAAA;AACA;AACA,KAlJA;AAmJA;AACA,IAAA,kBApJA,gCAoJA;AACA,WAAA,WAAA;AACA,WAAA,OAAA,GAAA,EAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,oBAAA,GAAA,IAAA;AACA,WAAA,KAAA,GAAA,SAAA;AACA,KA1JA;AA2JA,IAAA,YA3JA,wBA2JA,IA3JA,EA2JA;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,IAAA,GAAA,OAAA,IAAA,GAAA,EAAA;;AACA,UAAA,CAAA,QAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,kBAAA;AACA;AACA,KAhKA;AAiKA,IAAA,YAjKA,0BAiKA;AACA,WAAA,KAAA,CAAA,MAAA,CAAA,MAAA;AACA,KAnKA;;AAoKA;AACA,IAAA,cArKA,0BAqKA,EArKA,EAqKA;AACA,sCAAA,EAAA;AACA,KAvKA;AAwKA;AACA,IAAA,gBAzKA,8BAyKA,CAAA,CAzKA;AA0KA;AACA,IAAA,mBA3KA,iCA2KA,CAAA,CA3KA;AA4KA;AACA,IAAA,eA7KA,2BA6KA,IA7KA,EA6KA,CA7KA,EA6KA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBACA,IAAA,CAAA,UAAA,IAAA,GADA;AAAA;AAAA;AAAA;;AAEA,gBAAA,MAAA,CAAA,OAAA,GAAA,IAAA,CAFA,CAGA;;AACA,gBAAA,MAAA,CAAA,OAAA;;AAJA;AAAA;;AAAA;AAAA;AAAA,uBAMA,MAAA,CAAA,OAAA,CAAA;AAAA,kBAAA,IAAA,EAAA,IAAA,CAAA;AAAA,iBAAA,CANA;;AAAA;AAAA;AAAA,uBAOA,MAAA,CAAA,UAAA,CAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAPA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,KAtLA;AAuLA,IAAA,WAvLA,yBAuLA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,+BAAA;AACA,kBAAA,UAAA,EAAA,MAAA,CAAA,QAAA,CAAA;AADA,iBAAA,CADA;;AAAA;AAAA;AACA,gBAAA,IADA,yBACA,IADA;AACA,gBAAA,IADA,yBACA,IADA;;AAIA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA,UAAA,GAAA,IAAA;AACA,kBAAA,MAAA,CAAA,OAAA,GAAA,IAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,wBAAA,KAAA,GAAA,EAAA;AACA,oBAAA,KAAA,CAAA,IAAA,GAAA,IAAA,CAAA,QAAA;AACA,oBAAA,KAAA,CAAA,GAAA,GAAA,IAAA,CAAA,OAAA;AACA,2BAAA,KAAA;AACA,mBALA,CAAA;AAMA;;AAZA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA,KApMA;AAqMA,IAAA,cArMA,0BAqMA,EArMA,EAqMA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,sBAAA,EAAA,CADA;;AAAA;AAAA;AACA,gBAAA,IADA,qBACA,IADA;;AAAA,sBAEA,IAAA,KAAA,MAFA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAGA,OAAA,CAAA,WAAA,EAHA;;AAAA;AAIA,gBAAA,OAAA,CAAA,QAAA,CAAA;AACA,kBAAA,IAAA,EAAA,SADA;AAEA,kBAAA,OAAA,EAAA;AAFA,iBAAA;;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,KA9MA;AAgNA,IAAA,UAhNA,wBAgNA;AACA,WAAA,QAAA,GAAA;AACA,QAAA,UAAA,EAAA;AADA,OAAA;AAGA,WAAA,SAAA,CAAA,YAAA;AACA,aAAA,KAAA,CAAA,MAAA,EAAA,aAAA;AACA,OAFA;AAGA,WAAA,oBAAA,GAAA,KAAA;AACA,KAxNA;AAyNA;AACA,IAAA,QA1NA,sBA0NA;AACA,WAAA,iBAAA,GAAA,KAAA;AACA,WAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA;AA7NA;AAlXA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"16\">\n      <el-col :span=\"4\">\n        <el-card shadow=\"never\" style=\"background:#e0f8ed;padding-top:10px;\">\n          <div>\n            <el-col>\n              <el-form label-width=\"80px\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"所属公司:\"\n                    >·\n                    <el-select\n                      v-model=\"treeForm.ssdwbm\"\n                      placeholder=\"请选择所属公司\"\n                      @change=\"getNewTreeInfo\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in OrganizationSelectedList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"电压等级:\">\n                    <el-select\n                      v-model=\"treeForm.dydjbm\"\n                      placeholder=\"请选择电压等级\"\n                      @change=\"getNewTreeInfo\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in VoltageLevelSelectedList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"快速查询:\">\n                    <el-input\n                      placeholder=\"输入关键字进行过滤\"\n                      v-model=\"filterText\"\n                      clearable\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-form>\n            </el-col>\n          </div>\n          <div class=\"text head-container\">\n            <el-col style=\"padding:0;\">\n              <el-tree\n                :expand-on-click-node=\"true\"\n                id=\"tree\"\n                highlight-current\n                :data=\"treeOptions\"\n                @node-click=\"handleNodeClick\"\n                :highlight-current=\"true\"\n                ref=\"tree\"\n                :filter-node-method=\"filterNode\"\n                node-key=\"id\"\n                :default-expanded-keys=\"['0']\"\n                :default-checked-keys=\"['0']\"\n                accordion\n              />\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <!--        <el-filter-->\n        <!--          ref=\"filter1\"-->\n        <!--          :data=\"filterInfo.data\"-->\n        <!--          :field-list=\"filterInfo.fieldList\"-->\n        <!--          :width=\"{ labelWidth: 120, itemWidth: 180 }\"-->\n        <!--          @handleReset=\"filterReset\"-->\n        <!--        />-->\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button\n              icon=\"el-icon-plus\"\n              @click=\"bdzAddSensorButton\"\n              v-hasPermi=\"['bdztz:button:add']\"\n              type=\"primary\"\n              >新增</el-button\n            >\n            <el-button\n              icon=\"el-icon-delete\"\n              v-hasPermi=\"['bdztz:button:delete']\"\n              type=\"danger\"\n              @click=\"deleteBdz\"\n              >删除</el-button\n            >\n          </div>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"70vh\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"updatebdz(scope.row)\"\n                  v-hasPermi=\"['bdztz:button:update']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                ></el-button>\n                <el-button\n                  @click=\"bdzDetails(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!--变电站所用弹出框开始-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"bdzDialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"60%\"\n      v-dialogDrag\n      :before-close=\"removeForm\"\n    >\n      <el-form :model=\"jbxxForm\" label-width=\"130px\" :rules=\"rules\" ref=\"form\">\n        <div\n          class=\"block\"\n          style=\"width: 50%;height: 50%;margin-bottom: 2%;float: right\"\n        >\n          <span class=\"demonstration\">变电站图片</span>\n          <el-carousel\n            trigger=\"click\"\n            height=\"150px\"\n            indicator-position=\"none\"\n            :interval=\"2000\"\n            type=\"card\"\n          >\n            <el-carousel-item v-for=\"(img, index) in imgList\" :key=\"index\">\n              <viewer :images=\"imgList\" style=\"z-index: 999\">\n                <img :src=\"img.url\" width=\"100%\" height=\"100%\" />\n              </viewer>\n            </el-carousel-item>\n          </el-carousel>\n        </div>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属单位\" prop=\"ssdwbm\">\n              <el-select\n                v-model=\"jbxxForm.ssdwbm\"\n                placeholder=\"所属单位\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option\n                  v-for=\"item in OrganizationSelectedList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"变电站数字编号\" prop=\"bdzszbh\">\n              <el-input\n                v-model=\"jbxxForm.bdzszbh\"\n                :disabled=\"isDisabled\"\n                placeholder=\"请输入变电站数字编号\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"变电站名称\" prop=\"bdzmc\">\n              <el-input\n                v-model=\"jbxxForm.bdzmc\"\n                :disabled=\"isDisabled\"\n                placeholder=\"请输入变电站名称\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <!--          <el-col :span=\"8\">-->\n          <!--            <el-form-item label=\"设备代码\" prop=\"sbdm\">-->\n          <!--              <el-input v-model=\"jbxxForm.sbdm\" :disabled=\"isDisabled\" placeholder=\"请输入设备代码\"></el-input>-->\n          <!--            </el-form-item>-->\n          <!--          </el-col>-->\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属电网\" prop=\"ssdw\">\n              <el-input\n                v-model=\"jbxxForm.ssdw\"\n                :disabled=\"isDisabled\"\n                placeholder=\"请输入所属电网\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属基地站\" prop=\"ssjdz\">\n              <el-select\n                v-model=\"jbxxForm.ssjdz\"\n                placeholder=\"请输入所属基地站\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option\n                  v-for=\"item in ssjdzList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电压等级\" prop=\"dydjbm\">\n              <el-select\n                v-model=\"jbxxForm.dydjbm\"\n                placeholder=\"请选择电压等级\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"设备状态\" prop=\"sbzt\">\n              <el-select\n                v-model=\"jbxxForm.sbzt\"\n                placeholder=\"请选择设备状态\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option\n                  v-for=\"item in sbzt\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"投运日期\" class=\"add_sy_tyrq\" prop=\"tyrq\">\n              <el-date-picker\n                v-model=\"jbxxForm.tyrq\"\n                type=\"date\"\n                placeholder=\"选择日期\"\n                format=\"yyyy-MM-dd\"\n                value-format=\"yyyy-MM-dd\"\n                :disabled=\"isDisabled\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电站类型\" prop=\"dzlx\">\n              <el-select v-model=\"jbxxForm.dzlx\" :disabled=\"isDisabled\">\n                <el-option value=\"变电站\" label=\"变电站\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否综合自动化站\" prop=\"sfzhzdh\">\n              <el-select v-model=\"jbxxForm.sfzhzdh\" :disabled=\"isDisabled\">\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否数字化变电站\" prop=\"sfszhbdz\">\n              <el-select v-model=\"jbxxForm.sfszhbdz\" :disabled=\"isDisabled\">\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否枢纽站\" prop=\"sfsnz\">\n              <el-select v-model=\"jbxxForm.sfsnz\" :disabled=\"isDisabled\">\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item\n              label=\"退运日期\"\n              class=\"add_sy_tyrq\"\n              prop=\"returnDate\"\n            >\n              <el-date-picker\n                v-model=\"jbxxForm.returnDate\"\n                type=\"date\"\n                placeholder=\"选择日期\"\n                format=\"yyyy-MM-dd\"\n                value-format=\"yyyy-MM-dd\"\n                :disabled=\"isDisabled\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"占地面积\" prop=\"zymj\">\n              <el-input\n                v-model=\"jbxxForm.zymj\"\n                :disabled=\"isDisabled\"\n                placeholder=\"请输入占地面积\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"污秽等级\" prop=\"whdj\">\n              <el-select v-model=\"jbxxForm.whdj\" :disabled=\"isDisabled\">\n                <el-option value=\"a级\" label=\"a级\"></el-option>\n                <el-option value=\"b级\" label=\"b级\"></el-option>\n                <el-option value=\"c级\" label=\"c级\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"值班方式\" prop=\"zbfs\">\n              <el-select v-model=\"jbxxForm.zbfs\" :disabled=\"isDisabled\">\n                <el-option value=\"有人值班\" label=\"有人值班\"></el-option>\n                <el-option value=\"无人值班\" label=\"无人值班\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否光纤通讯\" prop=\"sfgqtx\">\n              <el-select v-model=\"jbxxForm.sfgqtx\" :disabled=\"isDisabled\">\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"海拔\" prop=\"hb\">\n              <el-input v-model=\"jbxxForm.hb\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"8\">\n            <el-form-item label=\"工程编号\" prop=\"gcbh\">\n              <el-input\n                v-model=\"jbxxForm.gcbh\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"8\">\n            <el-form-item label=\"设计单位\" prop=\"sjdw\">\n              <el-input\n                v-model=\"jbxxForm.sjdw\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"8\">\n            <el-form-item label=\"监理单位\" prop=\"jldw\">\n              <el-input\n                v-model=\"jbxxForm.jldw\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电站重要级别\" prop=\"zyjb\">\n              <el-select v-model=\"jbxxForm.zyjb\" :disabled=\"isDisabled\">\n                <el-option value=\"A\" label=\"A\"></el-option>\n                <el-option value=\"B\" label=\"B\"></el-option>\n                <el-option value=\"C\" label=\"C\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"布置方式\" prop=\"bzfs\">\n              <el-select v-model=\"jbxxForm.bzfs\" :disabled=\"isDisabled\">\n                <el-option value=\"户内\" label=\"户内\"></el-option>\n                <el-option value=\"户外\" label=\"户外\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"站址\" prop=\"bdzdz\">\n              <el-input\n                v-model=\"jbxxForm.bdzdz\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"建筑面积\" prop=\"jzmj\">\n              <el-input\n                v-model=\"jbxxForm.jzmj\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"联系电话\" prop=\"phone\">\n              <el-input\n                v-model=\"jbxxForm.phone\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"工程名称\" prop=\"gcmc\">\n              <el-input\n                v-model=\"jbxxForm.gcmc\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"施工单位\" prop=\"sgdw\">\n              <el-input\n                v-model=\"jbxxForm.sgdw\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"地区特征\" prop=\"dqtz\">\n              <el-select v-model=\"jbxxForm.dqtz\" :disabled=\"isDisabled\">\n                <el-option value=\"户内\" label=\"户内\"></el-option>\n                <el-option value=\"户外\" label=\"户外\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"最高调度管辖权\" prop=\"zgddgxq\">\n              <el-input\n                v-model=\"jbxxForm.zgddgxq\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否满足N-1\" prop=\"sfmzn\">\n              <el-select v-model=\"jbxxForm.sfmzn\" :disabled=\"isDisabled\">\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否接入故障系统\" prop=\"sfjrgzxt\">\n              <el-select v-model=\"jbxxForm.sfjrgzxt\" :disabled=\"isDisabled\">\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否接入AVC\" prop=\"sfjravc\">\n              <el-select v-model=\"jbxxForm.sfjravc\" :disabled=\"isDisabled\">\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否集中监控\" prop=\"sfjzjk\">\n              <el-select v-model=\"jbxxForm.sfjzjk\" :disabled=\"isDisabled\">\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"接入的监控中心\" prop=\"jkzxmc\">\n              <el-input\n                v-model=\"jbxxForm.jkzxmc\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"经度\" prop=\"jd\">\n              <el-input\n                v-model=\"jbxxForm.jd\"\n                :disabled=\"isDisabled\"\n                placeholder=\"请输入经度\"\n              >\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"纬度\" prop=\"wd\">\n              <el-input\n                v-model=\"jbxxForm.wd\"\n                :disabled=\"isDisabled\"\n                placeholder=\"请输入纬度\"\n              >\n              </el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\" prop=\"bz\">\n              <el-input\n                v-model=\"jbxxForm.bz\"\n                :disabled=\"isDisabled\"\n                type=\"textarea\"\n                rows=\"2\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-form-item\n            label=\"已上传图片：\"\n            prop=\"attachment\"\n            v-if=\"jbxxForm.attachment.length > 0\"\n            id=\"pic_form\"\n          >\n            <el-col\n              :span=\"24\"\n              v-for=\"(item, index) in jbxxForm.attachment\"\n              style=\"margin-left: 0\"\n            >\n              <el-form-item :label=\"(index + 1).toString()\">\n                {{ item.fileOldName }}\n                <el-button\n                  v-if=\"!isDisabled\"\n                  type=\"error\"\n                  size=\"mini\"\n                  @click=\"deleteFileById(item.fileId)\"\n                  >删除</el-button\n                >\n              </el-form-item>\n            </el-col>\n          </el-form-item>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-form-item label=\"上传图片：\" v-if=\"!isDisabled\">\n            <el-upload\n              list-type=\"picture-card\"\n              class=\"upload-demo\"\n              accept=\".jpg,.png\"\n              ref=\"upload\"\n              :headers=\"header\"\n              action=\"/isc-api/file/upload\"\n              :before-upload=\"beforeUpload\"\n              :data=\"uploadData\"\n              single\n              :auto-upload=\"false\"\n              multiple\n            >\n              <el-button slot=\"trigger\" type=\"primary\">选取文件</el-button>\n              <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png文件</div>\n            </el-upload>\n          </el-form-item>\n        </el-row>\n\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"间隔单元\" prop=\"jgdy\">-->\n        <!--              <el-select v-model=\"jbxxForm.jgdy\" placeholder=\"请输入间隔单元\">-->\n        <!--              </el-select>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"相别\" prop=\"xb\">-->\n        <!--              <el-input v-model=\"jbxxForm.xb\" :disabled=\"isDisabled\" placeholder=\"请输入相别\"></el-input>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"相数\" prop=\"xs\">-->\n        <!--              <el-select v-model=\"jbxxForm.xs\" placeholder=\"\" :disabled=\"isDisabled\" placeholder=\"请输入相数\">-->\n        <!--              </el-select>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"出厂日期\" class=\"add_sy_tyrq\" prop=\"ccrq\">-->\n        <!--              <el-date-picker-->\n        <!--                v-model=\"jbxxForm.ccrq\"-->\n        <!--                type=\"date\"-->\n        <!--                format=\"yyyy-MM-dd\"-->\n        <!--                value-format=\"yyyy-MM-dd\"-->\n        <!--                placeholder=\"选择日期\" :disabled=\"isDisabled\">-->\n        <!--              </el-date-picker>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"安装位置\" prop=\"azwz\">-->\n        <!--              <el-input v-model=\"jbxxForm.azwz\" :disabled=\"isDisabled\" placeholder=\"请输入安装位置\"></el-input>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"用途\" prop=\"yt\">-->\n        <!--              <el-input v-model=\"jbxxForm.yt\" :disabled=\"isDisabled\" placeholder=\"请输入用途\"></el-input>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"型号\" prop=\"xh\">-->\n        <!--              <el-select v-model=\"jbxxForm.xh\" placeholder=\"请输入型号\" :disabled=\"isDisabled\" >-->\n        <!--              </el-select>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"产品代号\" prop=\"cpdh\">-->\n        <!--              <el-input v-model=\"jbxxForm.cpdh\" :disabled=\"isDisabled\" placeholder=\"请输入产品代号\"></el-input>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"额定电压\" prop=\"eddy\">-->\n        <!--              <el-select v-model=\"jbxxForm.eddy\" placeholder=\"请选择额定电压\" :disabled=\"isDisabled\">-->\n        <!--              </el-select>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"额定频率\">-->\n        <!--              <el-select v-model=\"jbxxForm.edpl\" placeholder=\"请选择额定频率\" :disabled=\"isDisabled\">-->\n        <!--              </el-select>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"使用环境\">-->\n        <!--              <el-input v-model=\"jbxxForm.syhj\" placeholder=\"请输入使用环境\" :disabled=\"isDisabled\">-->\n        <!--              </el-input>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"生产厂家\">-->\n        <!--              <el-select v-model=\"jbxxForm.sccj\" placeholder=\"请选择生产厂家\" :disabled=\"isDisabled\">-->\n        <!--              </el-select>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"制造国家\">-->\n        <!--              <el-select v-model=\"jbxxForm.zzgj\" placeholder=\"请选择制造国家\" :disabled=\"isDisabled\">-->\n        <!--              </el-select>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"组合设备类型\">-->\n        <!--              <el-select v-model=\"jbxxForm.zhsblx\" placeholder=\"请选择组合设备类型\" :disabled=\"isDisabled\">-->\n        <!--              </el-select>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"组合设备类型名称\">-->\n        <!--              <el-input v-model=\"jbxxForm.zhsblxmc\" :disabled=\"isDisabled\" placeholder=\"请选择组合设备类型\"></el-input>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"额定电流\">-->\n        <!--              <el-input v-model=\"jbxxForm.eddl\" placeholder=\"请输入额定电流\" :disabled=\"isDisabled\">-->\n        <!--              </el-input>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"运行编号\">-->\n        <!--              <el-input v-model=\"jbxxForm.yxbh\" :disabled=\"isDisabled\" placeholder=\"请输入运行编号\"></el-input>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"出厂编号\">-->\n        <!--              <el-input v-model=\"jbxxForm.ccbh\" :disabled=\"isDisabled\" placeholder=\"请输入出厂编号\"></el-input>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"removeForm\">取 消</el-button>\n        <el-button\n          v-if=\"title == '变电站台账修改' || title == '变电站台账新增'\"\n          type=\"primary\"\n          @click=\"addBdz\"\n          class=\"pmyBtn\"\n          >确 定\n        </el-button>\n      </div>\n    </el-dialog>\n    <!--变电站所用间隔弹出框结束-->\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport {\n  addBdz,\n  getTreeInfo,\n  removeBdz\n} from \"@/api/dagangOilfield/asset/bdsbtz\";\nimport { getNewTreeInfo, getBdzList } from \"@/api/dagangOilfield/asset/bdztz\";\nimport { treeselect } from \"@/api/system/dept\";\nimport treeselect1 from \"@riophae/vue-treeselect\";\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\nimport {\n  downloadByBusinessId,\n  getListByBusinessId,\n  deleteById\n} from \"@/api/tool/file\";\nimport { getFgsOptions, getSelectOptionsByOrgType } from \"@/api/yxgl/bdyxgl/zbgl\";\n\nexport default {\n  name: \"qxbzk\",\n  components: { treeselect1 },\n  data() {\n    return {\n      uploadData: {\n        businessId: undefined\n      },\n      //树结构监听属性\n      filterText: \"\",\n      //标题\n      title: \"\",\n      //组织结构下拉数据\n      OrganizationSelectedList: [],\n      //所属基地站\n      ssjdzList: [],\n      //树结构上面得筛选框参数\n      treeForm: {},\n      //电压等级下拉框数据\n      VoltageLevelSelectedList: [\n        { label: \"110kV\", value: \"110\" },\n        { label: \"35kV\", value: \"35\" },\n        { label: \"10kV\", value: \"10\" },\n        { label: \"6kV\", value: \"6\" }\n      ],\n      imgList: [\n        // {\n        //   url: require('@/assets/image/bdz.png')\n        // },\n        // {\n        //   url: require('@/assets/image/bdz.png')\n        // },\n        // {\n        //   url: require('@/assets/image/bdz.png')\n        // }\n      ],\n      options: [\n        {\n          value: \"110\",\n          label: \"110kV\"\n        },\n        {\n          value: \"35\",\n          label: \"35kV\"\n        },\n        {\n          value: \"10\",\n          label: \"10kV\"\n        },\n        {\n          value: \"6\",\n          label: \"6kV\"\n        }\n      ],\n      //变电站信息是否可编辑\n      isDisabled: false,\n      //上传图片时的请求头\n      header: {},\n      //间隔信息是否显示\n      jgShow: false,\n      //设备信息展示\n      assetIsDisable: false,\n\n      //技术参数动态展示集合\n      jscsLabelList: [\n        { label: \"额定容量_高压\", jscsbm: \"edrlgy\", value: \"\", type: \"input\" },\n        {\n          label: \"产品代号\",\n          jscsbm: \"cpdh\",\n          value: \"\",\n          type: \"select\",\n          options: [\n            { label: \"下拉内容一\", value: \"1\" },\n            { label: \"下拉内容二\", value: \"2\" }\n          ]\n        },\n        { label: \"空载损耗_kW\", jscsbm: \"kzsh\", value: \"\", type: \"input\" },\n        { label: \"空载电流_%\", jscsbm: \"kzdl\", value: \"\", type: \"input\" },\n        { label: \"短路损耗中低\", jscsbm: \"dlshzd\", value: \"\", type: \"input\" },\n        { label: \"短路损耗高低\", jscsbm: \"dlshgd\", value: \"\", type: \"date\" },\n        { label: \"空载电流_%\", jscsbm: \"kzdl\", value: \"\", type: \"input\" },\n        { label: \"短路损耗中低\", jscsbm: \"dlshzd\", value: \"\", type: \"input\" },\n        { label: \"短路损耗高低\", jscsbm: \"dlshgd\", value: \"\", type: \"date\" }\n      ],\n      //技术参数绑定\n      jscsForm: {},\n      //单位下拉数据\n      deptOptions: [],\n\n      isShow1: false,\n      isShow2: false,\n      isShow3: false,\n      filterInfo: {},\n      filterInfo1: {\n        data: {\n          ywdwArr: [],\n          jhnyArr: [],\n          xlArr: \"\",\n          jhlxArr: [],\n          jhztArr: \"\",\n          sfdd: \"\"\n        },\n        fieldList: [\n          {\n            label: \"所属公司\",\n            type: \"select\",\n            value: \"roleName\",\n            multiple: true,\n            options: []\n          },\n          { label: \"变电站名称\", type: \"input\", value: \"roleKey\" },\n          {\n            label: \"电压等级\",\n            type: \"select\",\n            value: \"jhlxArr\",\n            multiple: true,\n            options: []\n          },\n          {\n            label: \"变电站类型\",\n            type: \"select\",\n            value: \"jhlxArr\",\n            multiple: true,\n            options: []\n          }\n        ]\n      },\n      //通用列表参数\n      tableAndPageInfo: {},\n      //变电站数据\n      tableAndPageInfo1: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"ssdwmc\", label: \"所属公司\", minWidth: \"120\" },\n          { prop: \"bdzmc\", label: \"变电站名称\", minWidth: \"180\" },\n          { prop: \"dydj\", label: \"电压等级\", minWidth: \"120\" },\n          { prop: \"sbzt\", label: \"设备状态\", minWidth: \"250\" },\n          { prop: \"sfsnz\", label: \"是否枢纽站\", minWidth: \"140\" },\n          { prop: \"sblx\", label: \"设备类型\", minWidth: \"120\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"120\" }\n          /*{\n              fixed: \"right\",\n              prop: 'operation',\n              label: '操作',\n              minWidth: '130px',\n              style: {display: 'block'},\n              operation: [\n                {name: '修改', clickFun: this.updatebdz},\n                {name: '详情', clickFun: this.bdzDetails},\n              ]\n            },*/\n        ]\n      },\n      jgQueryParams: {\n        ssbdz: undefined,\n        dydj: undefined,\n        pageSize: 10,\n        pageNum: 1\n      },\n      filterInfo3: {\n        data: {\n          ywdwArr: [],\n          jhnyArr: [],\n          xlArr: \"\",\n          jhlxArr: [],\n          jhztArr: \"\",\n          sfdd: \"\"\n        },\n        fieldList: [\n          {\n            label: \"所属公司\",\n            type: \"select\",\n            value: \"ywdwArr\",\n            multiple: true,\n            options: []\n          },\n          {\n            label: \"所属电站\",\n            type: \"select\",\n            value: \"jhnyArr\",\n            multiple: true,\n            options: []\n          },\n          {\n            label: \"电压等级\",\n            type: \"select\",\n            value: \"jhlxArr\",\n            multiple: true,\n            options: []\n          },\n          {\n            label: \"变电站类型\",\n            type: \"select\",\n            value: \"jhztArr\",\n            multiple: true,\n            options: []\n          }\n        ]\n      },\n\n      //设备履历tab页\n      sbllDescTabName: \"syjl\",\n\n      //设备基本信息\n      jbxxForm: {\n        attachment: [],\n        objId: undefined,\n        ssdwmc: undefined,\n        ssdwbm: undefined,\n        sbdm: undefined,\n        ddsbh: undefined,\n        dydj: undefined,\n        tyrq: undefined,\n        jgdy: undefined,\n        xb: undefined,\n        xs: undefined,\n        ccrq: undefined,\n        azwz: undefined,\n        yt: undefined,\n        fzr: undefined,\n        cpdh: undefined,\n        eddy: undefined,\n        edpl: undefined,\n        sbzt: undefined,\n        syhj: undefined,\n        sccj: undefined,\n        zzgj: undefined,\n        zhsblx: undefined,\n        zhsblxmc: undefined,\n        eddl: undefined,\n        yxbh: undefined,\n        ccbh: undefined,\n        bdzmc: undefined,\n        bdzszbh: undefined, //变电站数字编号\n        ssdw: undefined, //所属电网\n        dzlx: undefined, //电站类型\n        sfzhzdh: undefined, //是否综合自动化站\n        sfszhbdz: undefined, //是否数字化变电站\n        returnDate: undefined, //退运日期\n        zymj: undefined, //占地面积\n        whdj: undefined, //污秽等级\n        zbfs: undefined, //值班方式\n        sfgqtx: undefined, //是否光纤通讯\n        hb: undefined, //海拔\n        gcbh: undefined, //工程编号\n        sjdw: undefined, //设计单位\n        jldw: undefined, //监理单位\n        zyjb: undefined, // 电站重要级别\n        bzfs: undefined, //布置方式\n        bdzdz: undefined, //变电站地址\n        jzmj: undefined, // 建筑面积\n        phone: undefined, //联系电话\n        gcmc: undefined, // 工程名称\n        sgdw: undefined, //施工单位\n        dqtz: undefined, //地区特征\n        zgddgxq: undefined, // 最高调度管辖权\n        sfmzn: undefined, // 是否满足n-1\n        sfjrgzxt: undefined, //是否接入故障信息远传系统\n        sfjravc: undefined, //是否接入avc\n        sfjzjk: undefined, //是否集中监控\n        jkzxmc: undefined, //接入得监控中心\n        bz: undefined //备注\n      },\n      bdzqueryParams: {\n        //bm:undefined,\n        ssdwbm: undefined,\n        pageNum: 1,\n        pageSize: 10\n      },\n      assetQueryParams: {\n        ssjg: undefined,\n        pageNum: 1,\n        pageSize: 10\n      },\n      //设备详情页底部确认取消按钮控制\n      sbCommitDialogCotrol: true,\n      //弹出框tab页\n      activeTabName: \"sbDesc\",\n      //变电站展示\n      bdzShowTable: true,\n      //间隔展示\n      jgShowTable: false,\n      //设备展示\n      sbShowTable: false,\n      //设备弹出框\n      dialogFormVisible: false,\n      //变电站添加按钮弹出框\n      bdzDialogFormVisible: false,\n      //间隔添加按钮弹出框\n      jgDialogFormVisible: false,\n      //弹出框表单\n      form: {},\n\n      loading: false,\n      //组织树\n      treeOptions: [],\n\n      //变电站挂接数据\n      newTestData: [],\n\n      //删除是否可用\n      multipleSensor: true,\n      //查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        roleKey: \"\",\n        roleName: \"\",\n        status: \"\"\n      },\n      sbzt: [\n        {\n          value: \"在运\",\n          label: \"在运\"\n        },\n        {\n          value: \"停运\",\n          label: \"停运\"\n        },\n        {\n          value: \"报废\",\n          label: \"报废\"\n        }\n      ],\n      showSearch: true,\n      rules: {\n        ssdwbm: [\n          { required: true, message: \"请选择所属公司\", trigger: \"change\" }\n        ],\n        // sbdm: [{required: true, message: '请填写设备代码', trigger: 'blur'}],\n        bdzmc: [\n          { required: true, message: \"请填写变电站名称\", trigger: \"blur\" }\n        ],\n        dydjbm: [\n          { required: true, message: \"请选择电压等级\", trigger: \"blur\" }\n        ],\n        sbzt: [{ required: true, message: \"请选择设备状态\", trigger: \"blur\" }]\n      }\n    };\n  },\n  watch: {\n    //监听筛选框值发生变化进而筛选树结构\n    filterText(val) {\n      this.$refs.tree.filter(val);\n    }\n  },\n  created() {\n    //获取新的设备拓扑树\n    this.getNewTreeInfo();\n    //初始化加载时加载所有变电站信息\n    this.getFgsOptions()\n    this.getJdzOptions()\n    this.getData();\n    //初始化时加载页面内容\n    this.tableAndPageInfo = { ...this.tableAndPageInfo1 };\n  },\n  mounted() {\n    //获取token\n    this.header.token = getToken();\n  },\n  methods: {\n    //树监听事件\n    filterNode(value, data) {\n      if (!value) return true;\n      return data.label.indexOf(value) !== -1;\n    },\n    //获取新的设备拓扑树\n    getNewTreeInfo() {\n      getNewTreeInfo(this.treeForm).then(res => {\n        this.treeOptions = res.data;\n      });\n    },\n    /**\n     * 获取分公司下拉数据\n     */\n    getFgsOptions() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.OrganizationSelectedList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"fgs\") {\n            return (item.options = this.OrganizationSelectedList);\n          }\n        });\n      });\n    },\n    /**\n     * 获取基地站下拉数据\n     */\n    getJdzOptions() {\n      getSelectOptionsByOrgType(JSON.stringify(\"07\")).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.ssjdzList = res.data;\n      });\n    },\n    //旧树形数据获取\n    getTreeInfoList() {\n      getTreeInfo().then(res => {\n        this.treeOptions = res.data;\n      });\n    },\n    //列表查询\n    async getData(params) {\n      const param = { ...this.bdzqueryParams, ...params };\n      await getBdzList(param).then(res => {\n        this.tableAndPageInfo1.tableData = res.data.records;\n        this.tableAndPageInfo1.pager.total = res.data.total;\n        //给页面赋值\n        this.$nextTick(function() {\n          this.tableAndPageInfo = { ...this.tableAndPageInfo1 };\n        });\n      });\n    },\n    addBdz() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          addBdz(this.jbxxForm).then(res => {\n            if (res.code === \"0000\") {\n              this.uploadData.businessId = res.data.objId;\n              this.submitUpload();\n              this.bdzDialogFormVisible = false;\n              this.$message.success(\"操作成功\");\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getNewTreeInfo();\n              this.getData();\n            } else {\n              this.bdzDialogFormVisible = false;\n            }\n          });\n        } else {\n          return false;\n        }\n      });\n    },\n    /**\n     * 表格多选框\n     */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.objId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /**\n     * 删除变电站\n     */\n    deleteBdz() {\n      if (this.ids.length != 0) {\n        this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(() => {\n          removeBdz(this.ids).then(res => {\n            this.$message({\n              type: \"success\",\n              message: \"删除成功!\"\n            });\n            this.getNewTreeInfo();\n            this.tableAndPageInfo.pager.pageResize = \"Y\";\n            this.getData();\n          });\n        });\n      } else {\n        this.$message({\n          type: \"info\",\n          message: \"请选择至少一条数据!\"\n        });\n      }\n    },\n\n    //修改按钮\n    updatebdz(row) {\n      this.$nextTick(async function() {\n        this.clearUpload();\n        this.jbxxForm = { ...row };\n        this.jbxxForm.attachment = [];\n        await this.getFileList();\n        this.isDisabled = false;\n        this.title = \"变电站台账修改\";\n        this.bdzDialogFormVisible = true;\n      });\n    },\n    //详情方法\n    bdzDetails(row) {\n      this.$nextTick(async function() {\n        this.clearUpload();\n        this.jbxxForm = { ...row };\n        this.jbxxForm.attachment = [];\n        await this.getFileList();\n        this.bdzDialogFormVisible = true;\n        this.isDisabled = true;\n        this.title = \"变电站台账详情\";\n      });\n    },\n    //设备添加按钮\n    sbAddSensorButton() {\n      this.dialogFormVisible = true;\n    },\n    clearUpload() {\n      if (this.$refs.upload) {\n        this.$refs.upload.clearFiles();\n      }\n    },\n    //变电站添加按钮\n    bdzAddSensorButton() {\n      this.clearUpload();\n      this.imgList = [];\n      this.isDisabled = false;\n      this.bdzDialogFormVisible = true;\n      this.title = \"变电站台账新增\";\n    },\n    beforeUpload(file) {\n      const fileSize = file.size < 1024 * 1024 * 50;\n      if (!fileSize) {\n        this.$message.error(\"上传文件大小不能超过 50MB!\");\n      }\n    },\n    submitUpload() {\n      this.$refs.upload.submit();\n    },\n    /**下载附件*/\n    downloadHandle(id) {\n      downloadByBusinessId(id);\n    },\n    //每页展示数量点击事件\n    handleSizeChange() {},\n    //页码改变事件\n    handleCurrentChange() {},\n    //树点击事件\n    async handleNodeClick(data, e) {\n      if (data.identifier == \"0\") {\n        this.isShow1 = true;\n        //获取变电站数据\n        this.getData();\n      } else {\n        await this.getData({ sbdm: data.id });\n        await this.bdzDetails(this.tableAndPageInfo.tableData[0]);\n      }\n    },\n    async getFileList() {\n      let { code, data } = await getListByBusinessId({\n        businessId: this.jbxxForm.objId\n      });\n      if (code === \"0000\") {\n        this.jbxxForm.attachment = data;\n        this.imgList = data.map(item => {\n          let item1 = {};\n          item1.name = item.fileName;\n          item1.url = item.fileUrl;\n          return item1;\n        });\n      }\n    },\n    async deleteFileById(id) {\n      let { code } = await deleteById(id);\n      if (code === \"0000\") {\n        await this.getFileList();\n        this.$message({\n          type: \"success\",\n          message: \"文件删除成功!\"\n        });\n      }\n    },\n\n    removeForm() {\n      this.jbxxForm = {\n        attachment: []\n      };\n      this.$nextTick(function() {\n        this.$refs[\"form\"].clearValidate();\n      });\n      this.bdzDialogFormVisible = false;\n    },\n    //缺陷标准库新增完成\n    qxcommit() {\n      this.dialogFormVisible = false;\n      this.$message.success(\"新增成功\");\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  height: 76vh;\n  overflow: auto;\n}\n\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.box-cardList {\n  height: 70%;\n}\n\n.item {\n  width: 200px;\n  float: left;\n}\n\n#main_container_dj {\n  height: calc(100vh - 84px);\n}\n\n.aside_height {\n  height: 96%;\n}\n\n.defect .el-form-item:nth-child(odd) {\n  margin-right: 70px;\n}\n\n/*背景颜色调整*/\n#main_container_dj,\n#main_container_dj .el-aside {\n  background-color: #b4caf1;\n}\n\n///deep/ .qxlr_dialog_insert .el-dialog__header {\n//  background-color: #8eb3f5;\n//}\n//\n///deep/ .pmyBtn {\n//  background: #8eb3f5;\n//}\n\n/*/deep/ .add_sy_tyrq .el-input--medium .el-input__inner {*/\n/*  width: 200px;*/\n/*}*/\n\n/*添加弹出框得宽度*/\n/*/deep/ .el-input--medium .el-input__inner {*/\n/*  width: 200px;*/\n/*}*/\n.el-select {\n  width: 100%;\n}\n\n.el-date-editor {\n  width: 100%;\n}\n\n.el-carousel__item h3 {\n  color: #475669;\n  font-size: 14px;\n  opacity: 0.75;\n  line-height: 150px;\n}\n\n.el-carousel__item:nth-child(2n) {\n  background-color: #99a9bf;\n}\n\n.el-carousel__item:nth-child(2n + 1) {\n  background-color: #d3dce6;\n}\n\n/* 设置滚动条的样式 */\n::-webkit-scrollbar {\n  width: 12px;\n}\n\n/* 滚动槽 */\n::-webkit-scrollbar-track {\n  //-webkit-box-shadow:inset006pxrgba(0,0,0,0.3);\n  border-radius: 10px;\n}\n\n/* 滚动条滑块 */\n::-webkit-scrollbar-thumb {\n  border-radius: 10px;\n  background: rgba(0, 0, 0, 0.1);\n  //-webkit-box-shadow:gba(0,0,0,0.5);\n}\n\n::-webkit-scrollbar-thumb:window-inactive {\n  background: rgba(0, 0, 0, 0.1);\n}\n\n[data-v-67a974b1]::-webkit-scrollbar {\n  width: 8px;\n}\n\n.item {\n  width: 225px;\n  float: left;\n}\n\n/deep/ .box-card {\n  margin: 0 6px;\n}\n\n//有子节点 且未展开\n.el-tree ::v-deep .el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/add.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n//有子节点 且已展开\n.el-tree\n  ::v-deep\n  .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/prep.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n//没有子节点\n.el-tree ::v-deep .el-tree-node__expand-icon.is-leaf::before {\n  background: transparent;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n</style>\n<style>\n#pic_form .el-form-item__content {\n  margin-left: 0 !important;\n}\n</style>\n"], "sourceRoot": "src/views/dagangOilfield/dwzygl/bdgl"}]}