{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\sbxhwh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\sbxhwh.vue", "mtime": 1706897322893}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgZ2V0TGlzdCwKICByZW1vdmUsCiAgc2F2ZU9yVXBkYXRlCn0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYnpnbC9zYmJ6ay9zYnhoayI7CmltcG9ydCB7IGdldERldmljZUNsYXNzR3JvdXAgfSBmcm9tICJAL2FwaS9kYWdhbmdPaWxmaWVsZC9iemdsL3NibHh3aC9zYmx4d2giOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJpbmRleCIsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIERldmljZXNMaXN0R3JvdXA6IFtdLAogICAgICBzZWxlY3RSb3dzOiBbXSwKICAgICAgc2J4aExpc3Q6IFt7IGxhYmVsOiAiIiwgdmFsdWU6ICIiIH1dLAogICAgICBkeXNibHhMaXN0OiBbeyBsYWJlbDogIiIsIHZhbHVlOiAiIiB9XSwKICAgICAgZm9ybTogewogICAgICAgIHNieGg6ICIiLAogICAgICAgIGR5c2JseDogIiIKICAgICAgfSwKICAgICAgLy/or6bmg4XlvLnmoYbmmK/lkKbmmL7npLoKICAgICAgaXNTaG93RGV0YWlsczogZmFsc2UsCiAgICAgIC8v5piv5ZCm56aB55SoCiAgICAgIGlzRGlzYWJsZWQ6IGZhbHNlLAogICAgICAvL+agh+mimAogICAgICB0aXRsZTogIiIsCiAgICAgIGZpbHRlckluZm86IHsKICAgICAgICBkYXRhOiB7CiAgICAgICAgICBzYnhoOiAiIiwKICAgICAgICAgIHNibHhBcnI6IFtdCiAgICAgICAgfSwgLy/mn6Xor6LmnaHku7YKICAgICAgICBmaWVsZExpc3Q6IFsKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLorr7lpIfliIbnsbsiLAogICAgICAgICAgICB2YWx1ZTogInNibHhBcnIiLAogICAgICAgICAgICB0eXBlOiAic2VsZWN0R3JvdXBqeHhtd2giLAogICAgICAgICAgICBjbGVhcmFibGU6IHRydWUsCiAgICAgICAgICAgIGZpbHRlcmFibGU6IHRydWUsCiAgICAgICAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAgICAgICBvcHRpb25zOiBbXQogICAgICAgICAgfSwKICAgICAgICAgIHsgbGFiZWw6ICLorr7lpIflnovlj7ciLCB0eXBlOiAiaW5wdXQiLCB2YWx1ZTogInNieGgiIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIHRhYmxlQW5kUGFnZUluZm86IHsKICAgICAgICBwYWdlcjogewogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMjBdCiAgICAgICAgfSwKICAgICAgICB0YWJsZURhdGE6IFtdLAogICAgICAgIHRhYmxlSGVhZGVyOiBbCiAgICAgICAgICB7IGxhYmVsOiAi5a+55bqU6K6+5aSH57G75Z6LIiwgcHJvcDogInNibHhtYyIsIG1pbldpZHRoOiAiMTgwIiB9LAogICAgICAgICAgeyBsYWJlbDogIuiuvuWkh+Wei+WPtyIsIHByb3A6ICJzYnhoIiwgbWluV2lkdGg6ICIxODAiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5aSH5rOoIiwgcHJvcDogImJ6IiwgbWluV2lkdGg6ICIxNDAiIH0KICAgICAgICAgIC8qIHsKICAgICAgICAgICAgcHJvcDogJ29wZXJhdGlvbicsCiAgICAgICAgICAgIGxhYmVsOiAn5pON5L2cJywKICAgICAgICAgICAgbWluV2lkdGg6ICcxMzBweCcsCiAgICAgICAgICAgIHN0eWxlOiB7IGRpc3BsYXk6ICdibG9jaycgfSwKICAgICAgICAgICAgLy/mk43kvZzliJflm7rlrprlho3lj7PkvqcKICAgICAgICAgICAgZml4ZWQ6ICdyaWdodCcsCiAgICAgICAgICAgIG9wZXJhdGlvbjogWwogICAgICAgICAgICAgIHsgbmFtZTogJ+S/ruaUuScsIGNsaWNrRnVuOiB0aGlzLmdldFVwZGF0ZSB9LAogICAgICAgICAgICAgIHsgbmFtZTogJ+ivpuaDhScsIGNsaWNrRnVuOiB0aGlzLmdldERldGFpbHMgfQogICAgICAgICAgICBdCiAgICAgICAgICB9Ki8KICAgICAgICBdLAogICAgICAgIG9wdGlvbjogeyBjaGVja0JveDogdHJ1ZSwgc2VyaWFsTnVtYmVyOiB0cnVlIH0KICAgICAgfSwKICAgICAgcGFyYW1zOiB7CiAgICAgICAgc2J4aDogIiIsCiAgICAgICAgc2JseEFycjogW10KICAgICAgfSwKICAgICAgc2hvd0RldmljZVRyZWU6IGZhbHNlLAogICAgICBpc0ZpbHRlcjogZmFsc2UsCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgZHlzYmx4OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K6+5aSH5YiG57G75LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfQogICAgICAgIF0sCiAgICAgICAgc2JseG1jOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K6+5aSH5YiG57G75LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfQogICAgICAgIF0sCiAgICAgICAgc2J4aDogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLorr7lpIflnovlj7fkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfV0KICAgICAgfQogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICAKICB9LAogIG1vdW50ZWQoKSB7CiAgICAvL+WIl+ihqOafpeivogogICAgdGhpcy5nZXREYXRhKCk7CiAgICB0aGlzLmdldERldmljZUNsYXNzR3JvdXAoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGdldERldmljZUNsYXNzR3JvdXAoKSB7CiAgICAgIGdldERldmljZUNsYXNzR3JvdXAoWyJiZHNiIiwgInBkc2IiLCAic2RzYiJdKS50aGVuKHJlcyA9PiB7CiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIHRoaXMuRGV2aWNlc0xpc3RHcm91cCA9IHJlcy5kYXRhOwogICAgICAgICAgdGhpcy5maWx0ZXJJbmZvLmZpZWxkTGlzdC5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgICBpZiAoaXRlbS52YWx1ZSA9PT0gInNibHhBcnIiKSB7CiAgICAgICAgICAgICAgaXRlbS5vcHRpb25zID0gcmVzLmRhdGE7CiAgICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6I635Y+W6K6+5aSH5YiG57G75aSx6LSlIik7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvL+WIl+ihqOafpeivogogICAgYXN5bmMgZ2V0RGF0YShwYXJhbXMpIHsKICAgICAgdHJ5IHsKICAgICAgICB0aGlzLiRyZWZzLnNieGhrLmxvYWRpbmcgPSB0cnVlOwogICAgICAgIHRoaXMucGFyYW1zID0geyAuLi50aGlzLnBhcmFtcywgLi4ucGFyYW1zIH07CiAgICAgICAgY29uc3QgcGFyYW0gPSB0aGlzLnBhcmFtczsKICAgICAgICBjb25zdCB7IGRhdGEsIGNvZGUgfSA9IGF3YWl0IGdldExpc3QocGFyYW0pOwogICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby50YWJsZURhdGEgPSBkYXRhLnJlY29yZHM7CiAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIudG90YWwgPSBkYXRhLnRvdGFsOwogICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgICAvLyDku6XmnI3liqHnmoTmlrnlvI/osIPnlKjnmoQgTG9hZGluZyDpnIDopoHlvILmraXlhbPpl60KICAgICAgICAgICAgdGhpcy4kcmVmcy5zYnhoay5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICBjb25zb2xlLmxvZyhlKTsKICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICAvLyDku6XmnI3liqHnmoTmlrnlvI/osIPnlKjnmoQgTG9hZGluZyDpnIDopoHlvILmraXlhbPpl60KICAgICAgICAgIHRoaXMuJHJlZnMuc2J4aGsubG9hZGluZyA9IGZhbHNlOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgLy/ph43nva7mjInpkq4KICAgIGdldFJlc2V0KCkgewogICAgfSwKICAgIC8v6YCJ5Lit6KGMCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2UoKSB7fSwKICAgIC8v5paw5aKe5oyJ6ZKuCiAgICBnZXRJbnNlcnQoKSB7CiAgICAgIHRoaXMudGl0bGUgPSAi6K6+5aSH5Z6L5Y+35bqT5aKe5YqgIjsKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gZmFsc2U7CiAgICAgIHRoaXMuZm9ybSA9IHt9OwogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlOwogICAgfSwKICAgIC8v5L+u5pS55oyJ6ZKuCiAgICBnZXRVcGRhdGUocm93KSB7CiAgICAgIHRoaXMudGl0bGUgPSAi6K6+5aSH5Z6L5Y+35bqT5L+u5pS5IjsKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gZmFsc2U7CiAgICAgIHRoaXMuZm9ybSA9IHsgLi4ucm93IH07CiAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IHRydWU7CiAgICB9LAogICAgLy/or6bmg4XmjInpkq4KICAgIGdldERldGFpbHMocm93KSB7CiAgICAgIHRoaXMudGl0bGUgPSAi6K6+5aSH5Z6L5Y+35bqT6K+m5oOF5p+l55yLIjsKICAgICAgdGhpcy5mb3JtID0geyAuLi5yb3cgfTsKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gdHJ1ZTsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gdHJ1ZTsKICAgIH0sCiAgICAvKi8v6ZmE5Lu25p+l55yLCiAgICBnZXRGakluZm9MaXN0KCkgewogICAgICB0aGlzLnRpdGxlID0gJ+mZhOS7tuafpeeciycKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gdHJ1ZQogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlCiAgICAgIHRoaXMuZm9ybT17Li4ucm93fQogICAgfSwqLwogICAgYXN5bmMgc2F2ZVJvdygpIHsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHNhdmVPclVwZGF0ZSh0aGlzLmZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLmk43kvZzmiJDlip8iKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICAgICAgICBjb25zb2xlLmxvZyhlKTsKICAgICAgICAgICAgfQogICAgICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICAgIH0pOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgfQogICAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvL+WIoOmZpOaMiemSrgogICAgYXN5bmMgZGVsZXRlUm93KCkgewogICAgICBpZiAodGhpcy5zZWxlY3RSb3dzLmxlbmd0aCA8IDEpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+mAieaLqeato+ehrueahOaVsOaNru+8ge+8ge+8gSIpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBsZXQgaWRzID0gdGhpcy5zZWxlY3RSb3dzLm1hcChpdGVtID0+IHsKICAgICAgICByZXR1cm4gaXRlbS5zYnhoa0lkOwogICAgICB9KTsKICAgICAgdGhpcy4kY29uZmlybSgi5q2k5pON5L2c5bCG5rC45LmF5Yig6Zmk6K+l5pWw5o2uLCDmmK/lkKbnu6fnu60/IiwgIuaPkOekuiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pCiAgICAgICAgLnRoZW4oKCkgPT4gewogICAgICAgICAgcmVtb3ZlKGlkcykudGhlbigoeyBjb2RlIH0pID0+IHsKICAgICAgICAgICAgaWYgKGNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLAogICAgICAgICAgICAgICAgbWVzc2FnZTogIuWIoOmZpOaIkOWKnyEiCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICB0eXBlOiAiZXJyb3IiLAogICAgICAgICAgICAgICAgbWVzc2FnZTogIuWIoOmZpOWksei0pSEiCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0pCiAgICAgICAgLmNhdGNoKCgpID0+IHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAiaW5mbyIsCiAgICAgICAgICAgIG1lc3NhZ2U6ICLlt7Llj5bmtojliKDpmaQiCiAgICAgICAgICB9KTsKICAgICAgICB9KTsKICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICB9LAogICAgLy/lhbPpl63lvLnnqpcKICAgIGNsb3NlKCkgewogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSBmYWxzZTsKICAgIH0sCiAgICBzZWxlY3RDaGFuZ2Uocm93cykgewogICAgICB0aGlzLnNlbGVjdFJvd3MgPSByb3dzOwogICAgfSwKICAgIC8v5pi+56S66K6+5aSH57G75Z6L5qCR5by556qXCiAgICBzaG93RGV2aWNlVHJlZURpYWxvZygpIHsKICAgICAgdGhpcy5pc0ZpbHRlciA9IGZhbHNlOwogICAgICB0aGlzLnNob3dEZXZpY2VUcmVlID0gdHJ1ZTsKICAgIH0sCiAgICAvL+m8oOagh+iBmueEpuS6i+S7tgogICAgaW5wdXRGb2N1c0V2ZW50KHZhbCkgewogICAgICBpZiAodmFsLnRhcmdldC5uYW1lID09PSAiZHlzYmx4IikgewogICAgICAgIHRoaXMuc2hvd0RldmljZVRyZWUgPSB0cnVlOwogICAgICAgIHRoaXMuaXNGaWx0ZXIgPSB0cnVlOwogICAgICB9CiAgICB9LAogICAgLy/ojrflj5borr7lpIfliIbnsbvmlbDmja4KICAgIGdldERldmljZVR5cGVEYXRhKHJlcykgewogICAgICBpZiAodGhpcy5pc0ZpbHRlcikgewogICAgICAgIHRoaXMuZmlsdGVySW5mby5kYXRhLnNibHhBcnIgPSBbXTsKICAgICAgICB0aGlzLmZpbHRlckluZm8uZGF0YS5keXNibHggPSAiIjsKICAgICAgICByZXMuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIGlmIChpdGVtLmNoZWNrZWQpIHsKICAgICAgICAgICAgdGhpcy5maWx0ZXJJbmZvLmRhdGEuc2JseEFyci5wdXNoKGl0ZW0uY29kZSk7CiAgICAgICAgICAgIHRoaXMuZmlsdGVySW5mby5kYXRhLmR5c2JseCArPSBpdGVtLm5hbWUgKyAiLCI7CiAgICAgICAgICB9CiAgICAgICAgfSk7CgogICAgICAgIHRoaXMuZmlsdGVySW5mby5kYXRhLmR5c2JseCA9IHRoaXMuZmlsdGVySW5mby5kYXRhLmR5c2JseC5zdWJzdHJpbmcoCiAgICAgICAgICAwLAogICAgICAgICAgdGhpcy5maWx0ZXJJbmZvLmRhdGEuZHlzYmx4Lmxlbmd0aCAtIDEKICAgICAgICApOwogICAgICAgIHRoaXMuc2hvd0RldmljZVRyZWUgPSBmYWxzZTsKICAgICAgfSBlbHNlIHsKICAgICAgICBsZXQgdHJlZU5vZGVzID0gW107CiAgICAgICAgcmVzLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICBpZiAoaXRlbS5jaGVja2VkKSB7CiAgICAgICAgICAgIHRyZWVOb2Rlcy5wdXNoKGl0ZW0pOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICAgIGlmICh0cmVlTm9kZXMubGVuZ3RoID09PSAxKSB7CiAgICAgICAgICB0aGlzLmZvcm0uc2JseG1jID0gdHJlZU5vZGVzWzBdLm5hbWU7CiAgICAgICAgICB0aGlzLmZvcm0uZHlzYmx4ID0gdHJlZU5vZGVzWzBdLmNvZGU7CiAgICAgICAgICB0aGlzLnNob3dEZXZpY2VUcmVlID0gZmFsc2U7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+36YCJ5oup5Y2V5p2h6K6+5aSH5pWw5o2uIik7CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgY2xvc2VEZXZpY2VUeXBlRGlhbG9nKCkgewogICAgICB0aGlzLnNob3dEZXZpY2VUcmVlID0gZmFsc2U7CiAgICB9LAogICAgLy/muIXnqbrooajljZXmlbDmja4KICAgIGhhbmRsZUNsb3NlKCkgewogICAgICB0aGlzLmZvcm0gPSB7fTsKICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIHRoaXMuZm9ybSA9IHRoaXMuJG9wdGlvbnMuZGF0YSgpLmZvcm07CiAgICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgICAgfSk7CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["sbxhwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyIA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "sbxhwh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 120, itemWidth: 230 }\"\n      @handleReset=\"getReset\"\n    />\n\n    <el-white class=\"button-group1\">\n      <div class=\"button_btn\">\n        <el-button\n          type=\"primary\"\n          v-hasPermi=\"['bzsbxhk:button:add']\"\n          icon=\"el-icon-plus\"\n          @click=\"getInsert\"\n          >新增</el-button\n        >\n      </div>\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"selectChange\"\n        height=\"68vh\"\n        ref=\"sbxhk\"\n      >\n        <el-table-column\n          slot=\"table_eight\"\n          align=\"center\"\n          fixed=\"right\"\n          style=\"display: block\"\n          label=\"操作\"\n          width=\"160\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              @click=\"getUpdate(scope.row)\"\n              v-hasPermi=\"['bzsbxhk:button:update']\"\n              type=\"text\"\n              size=\"small\"\n              class=\"el-icon-edit\"\n              title=\"修改\"\n            ></el-button>\n            <el-button\n              @click=\"getDetails(scope.row)\"\n              type=\"text\"\n              size=\"small\"\n              title=\"详情\"\n              class=\"el-icon-view\"\n            ></el-button>\n            <el-button\n              @click=\"deleteRow\"\n              type=\"text\"\n              v-if=\"scope.row.createBy === $store.getters.name\"\n              icon=\"el-icon-delete\"\n            ></el-button>\n          </template>\n        </el-table-column>\n      </comp-table>\n    </el-white>\n\n    <!-- 详情/新增/修改 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"30%\"\n      @close=\"handleClose\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"130px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row class=\"box-card\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"设备分类：\" prop=\"dysblx\">\n              <el-select\n                placeholder=\"请选择设备分类\"\n                v-model=\"form.dysblx\"\n                :disabled=\"isDisabled\"\n                clearable\n                filterable\n                style=\"width: 100%\"\n              >\n                <el-option-group\n                  v-for=\"group in DevicesListGroup\"\n                  :key=\"group.label\"\n                  :label=\"group.label\"\n                >\n                  <el-option\n                    v-for=\"item in group.sbDataList\"\n                    :key=\"item.code\"\n                    :label=\"item.name\"\n                    :value=\"item.code\"\n                  >\n                  </el-option>\n                </el-option-group>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"设备型号：\" prop=\"sbxh\">\n              <el-input\n                placeholder=\"请添加设备型号\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.sbxh\"\n              >\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注：\" prop=\"bz\">\n              <el-input\n                placeholder=\"请填写备注\"\n                type=\"textarea\"\n                :rows=\"3\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.bz\"\n              >\n              </el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\" size=\"small\">取 消</el-button>\n        <el-button\n          v-if=\"title === '设备型号库增加' || title === '设备型号库修改'\"\n          type=\"primary\"\n          size=\"small\"\n          @click=\"saveRow\"\n          >确 认\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  getList,\n  remove,\n  saveOrUpdate\n} from \"@/api/dagangOilfield/bzgl/sbbzk/sbxhk\";\nimport { getDeviceClassGroup } from \"@/api/dagangOilfield/bzgl/sblxwh/sblxwh\";\n\nexport default {\n  name: \"index\",\n  data() {\n    return {\n      DevicesListGroup: [],\n      selectRows: [],\n      sbxhList: [{ label: \"\", value: \"\" }],\n      dysblxList: [{ label: \"\", value: \"\" }],\n      form: {\n        sbxh: \"\",\n        dysblx: \"\"\n      },\n      //详情弹框是否显示\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          sbxh: \"\",\n          sblxArr: []\n        }, //查询条件\n        fieldList: [\n          {\n            label: \"设备分类\",\n            value: \"sblxArr\",\n            type: \"selectGroupjxxmwh\",\n            clearable: true,\n            filterable: true,\n            multiple: true,\n            options: []\n          },\n          { label: \"设备型号\", type: \"input\", value: \"sbxh\" }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 120]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"对应设备类型\", prop: \"sblxmc\", minWidth: \"180\" },\n          { label: \"设备型号\", prop: \"sbxh\", minWidth: \"180\" },\n          { label: \"备注\", prop: \"bz\", minWidth: \"140\" }\n          /* {\n            prop: 'operation',\n            label: '操作',\n            minWidth: '130px',\n            style: { display: 'block' },\n            //操作列固定再右侧\n            fixed: 'right',\n            operation: [\n              { name: '修改', clickFun: this.getUpdate },\n              { name: '详情', clickFun: this.getDetails }\n            ]\n          }*/\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        sbxh: \"\",\n        sblxArr: []\n      },\n      showDeviceTree: false,\n      isFilter: false,\n      rules: {\n        dysblx: [\n          { required: true, message: \"设备分类不能为空\", trigger: \"change\" }\n        ],\n        sblxmc: [\n          { required: true, message: \"设备分类不能为空\", trigger: \"change\" }\n        ],\n        sbxh: [{ required: true, message: \"设备型号不能为空\", trigger: \"blur\" }]\n      }\n    };\n  },\n  created() {\n    \n  },\n  mounted() {\n    //列表查询\n    this.getData();\n    this.getDeviceClassGroup();\n  },\n  methods: {\n    getDeviceClassGroup() {\n      getDeviceClassGroup([\"bdsb\", \"pdsb\", \"sdsb\"]).then(res => {\n        if (res.code === \"0000\") {\n          this.DevicesListGroup = res.data;\n          this.filterInfo.fieldList.forEach(item => {\n            if (item.value === \"sblxArr\") {\n              item.options = res.data;\n              return;\n            }\n          });\n        } else {\n          this.$message.error(\"获取设备分类失败\");\n        }\n      });\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.$refs.sbxhk.loading = true;\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        const { data, code } = await getList(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          this.$nextTick(() => {\n            // 以服务的方式调用的 Loading 需要异步关闭\n            this.$refs.sbxhk.loading = false;\n          });\n        }\n      } catch (e) {\n        console.log(e);\n        this.$nextTick(() => {\n          // 以服务的方式调用的 Loading 需要异步关闭\n          this.$refs.sbxhk.loading = false;\n        });\n      }\n    },\n    //重置按钮\n    getReset() {\n    },\n    //选中行\n    handleSelectionChange() {},\n    //新增按钮\n    getInsert() {\n      this.title = \"设备型号库增加\";\n      this.isDisabled = false;\n      this.form = {};\n      this.isShowDetails = true;\n    },\n    //修改按钮\n    getUpdate(row) {\n      this.title = \"设备型号库修改\";\n      this.isDisabled = false;\n      this.form = { ...row };\n      this.isShowDetails = true;\n    },\n    //详情按钮\n    getDetails(row) {\n      this.title = \"设备型号库详情查看\";\n      this.form = { ...row };\n      this.isDisabled = true;\n      this.isShowDetails = true;\n    },\n    /*//附件查看\n    getFjInfoList() {\n      this.title = '附件查看'\n      this.isDisabled = true\n      this.isShowDetails = true\n      this.form={...row}\n    },*/\n    async saveRow() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          saveOrUpdate(this.form).then(res => {\n            try {\n              if (res.code === \"0000\") {\n                this.$message.success(\"操作成功\");\n              }\n            } catch (e) {\n              console.log(e);\n            }\n            this.getData();\n          });\n        } else {\n          return false;\n        }\n        this.isShowDetails = false;\n      });\n    },\n    //删除按钮\n    async deleteRow() {\n      if (this.selectRows.length < 1) {\n        this.$message.warning(\"请选择正确的数据！！！\");\n        return;\n      }\n      let ids = this.selectRows.map(item => {\n        return item.sbxhkId;\n      });\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove(ids).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n      this.getData();\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false;\n    },\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n    //显示设备类型树弹窗\n    showDeviceTreeDialog() {\n      this.isFilter = false;\n      this.showDeviceTree = true;\n    },\n    //鼠标聚焦事件\n    inputFocusEvent(val) {\n      if (val.target.name === \"dysblx\") {\n        this.showDeviceTree = true;\n        this.isFilter = true;\n      }\n    },\n    //获取设备分类数据\n    getDeviceTypeData(res) {\n      if (this.isFilter) {\n        this.filterInfo.data.sblxArr = [];\n        this.filterInfo.data.dysblx = \"\";\n        res.forEach(item => {\n          if (item.checked) {\n            this.filterInfo.data.sblxArr.push(item.code);\n            this.filterInfo.data.dysblx += item.name + \",\";\n          }\n        });\n\n        this.filterInfo.data.dysblx = this.filterInfo.data.dysblx.substring(\n          0,\n          this.filterInfo.data.dysblx.length - 1\n        );\n        this.showDeviceTree = false;\n      } else {\n        let treeNodes = [];\n        res.forEach(item => {\n          if (item.checked) {\n            treeNodes.push(item);\n          }\n        });\n        if (treeNodes.length === 1) {\n          this.form.sblxmc = treeNodes[0].name;\n          this.form.dysblx = treeNodes[0].code;\n          this.showDeviceTree = false;\n        } else {\n          this.$message.warning(\"请选择单条设备数据\");\n        }\n      }\n    },\n    closeDeviceTypeDialog() {\n      this.showDeviceTree = false;\n    },\n    //清空表单数据\n    handleClose() {\n      this.form = {};\n      this.$nextTick(() => {\n        this.form = this.$options.data().form;\n        this.resetForm(\"form\");\n      });\n    }\n  }\n};\n</script>\n\n<style scoped>\n.button-group {\n  padding-left: 30px;\n  padding-right: 30px;\n  display: flex;\n  justify-content: flex-end;\n}\n\n/*控制input输入框边框是否显示*/\n.elInput >>> .el-input__inner {\n  border: 0;\n}\n\n.box-card {\n  margin-bottom: 2vh !important;\n}\n/deep/ .el-select-group__title {\n  font-size: 24px;\n}\n</style>\n"]}]}