{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\jbwh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\jbwh.vue", "mtime": 1706897323219}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBKYnh4IGZyb20gIkAvdmlld3MvZGFnYW5nT2lsZmllbGQvYnpnbC9zYnp0cGpiemsvamJ3aF9qYnh4IjsKaW1wb3J0IENzeHggZnJvbSAiQC92aWV3cy9kYWdhbmdPaWxmaWVsZC9iemdsL3NienRwamJ6ay9qYndoX2NzeHgiOwppbXBvcnQgWHhkIGZyb20gIkAvdmlld3MvZGFnYW5nT2lsZmllbGQvYnpnbC9zYnp0cGpiemsvamJ3aF94eGQiOwppbXBvcnQgSmJFZGl0IGZyb20gIkAvdmlld3MvZGFnYW5nT2lsZmllbGQvYnpnbC9zYnp0cGpiemsvamJ3aF9lZGl0IjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnamJ3aCcsCiAgY29tcG9uZW50czoge0pieHgsQ3N4eCxYeGQsSmJFZGl0fSwKICBwcm9wczogewogICAgc2JseDp7CiAgICAgIHR5cGU6U3RyaW5nLAogICAgICBkZWZhdWx0OicnLAogICAgfSwKICAgIG1wRGF0YTogewogICAgICB0eXBlOiBPYmplY3QsCiAgICB9LAogICAgamI6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OicnLAogICAgfSwKICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBmbGFnOidqYnh4JywgLy/pu5jorqTlsZXnpLrln7rmnKzkv6Hmga/liJfooagKICAgIH07CiAgfSwKICBtb3VudGVkKCkgewogIH0sCiAgbWV0aG9kczp7CiAgICAvL3RhYuWIh+aNogogICAgY2xpY2sobWFpblRhYil7CiAgICAgIHRoaXMuZmxhZyA9IG1haW5UYWI7CiAgICB9LAogICAgLy/ooajmoLzlj4zlh7vkuovku7YKICAgIGRiQ2xpY2tSb3codmFsLG1hcCl7CiAgICAgIHRoaXMuJHJlZnMuZWRpdEpiLnByb2Nlc3NQYXJlbnRWYWwodmFsLG1hcCk7CiAgICB9LAogICAgc2V0SmJWYWwodmFsKXsKICAgICAgdGhpcy4kZW1pdCgnc2V0SmJWYWwnLHZhbCk7CiAgICB9LAogICAgLy/lhbPpl63ohJrmnKzlvLnmoYYKICAgIGpiQ2xvc2UoKXsKICAgICAgdGhpcy4kZW1pdCgnamJDbG9zZScpOwogICAgfSwKICB9LAp9Cg=="}, {"version": 3, "sources": ["jbwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2CA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "jbwh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk", "sourcesContent": ["<template>\n  <el-row :gutter=\"16\">\n    <!--   Tab页签   -->\n    <el-col :span=\"12\">\n      <div class=\"txtTitle\">\n        <span @click=\"click('jbxx')\" :class=\"this.flag === 'jbxx'?'tabActive':'noActive'\" class=\"oneBtn\">\n          <span class=\"allBtn\">基本信息列表</span>\n        </span>\n        <span @click=\"click('csxx')\" :class=\"this.flag === 'csxx'?'tabActive':'noActive'\" class=\"twoBtn\">\n          <span class=\"allBtn\">参数信息列表</span>\n        </span>\n        <span @click=\"click('xxd')\" :class=\"this.flag === 'xxd'?'tabActive':'noActive'\" class=\"twoBtn\">\n          <span class=\"allBtn\">信息点列表</span>\n        </span>\n      </div>\n    </el-col>\n    <el-col :span=\"12\">\n      <div class=\"txtTitle\">\n        <span class=\"oneBtn tabActive\">\n          <span class=\"allBtn\">脚本维护</span>\n        </span>\n      </div>\n    </el-col>\n\n    <!--  基本信息  -->\n    <el-col :span=\"12\" v-if=\"this.flag === 'jbxx'\">\n      <Jbxx :sblx=\"sblx\" @dbClickRow=\"dbClickRow\"></Jbxx>\n    </el-col>\n    <!--  参数信息  -->\n    <el-col :span=\"12\" v-if=\"this.flag === 'csxx'\">\n      <Csxx :sblx=\"sblx\" @dbClickRow=\"dbClickRow\"></Csxx>\n    </el-col>\n    <!--  信息点  -->\n    <el-col :span=\"12\" v-if=\"this.flag === 'xxd'\">\n      <Xxd :ssztl-id=\"mpData.objId\" @dbClickRow=\"dbClickRow\"></Xxd>\n    </el-col>\n    <!--  脚本编辑  -->\n    <el-col :span=\"12\">\n      <JbEdit ref=\"editJb\" :jb=\"jb\" @setJbVal=\"setJbVal\" @jbClose=\"jbClose\"></JbEdit>\n    </el-col>\n  </el-row>\n</template>\n<script>\nimport Jbxx from \"@/views/dagangOilfield/bzgl/sbztpjbzk/jbwh_jbxx\";\nimport Csxx from \"@/views/dagangOilfield/bzgl/sbztpjbzk/jbwh_csxx\";\nimport Xxd from \"@/views/dagangOilfield/bzgl/sbztpjbzk/jbwh_xxd\";\nimport JbEdit from \"@/views/dagangOilfield/bzgl/sbztpjbzk/jbwh_edit\";\n\nexport default {\n  name: 'jbwh',\n  components: {Jbxx,Csxx,Xxd,JbEdit},\n  props: {\n    sblx:{\n      type:String,\n      default:'',\n    },\n    mpData: {\n      type: Object,\n    },\n    jb: {\n      type: String,\n      default:'',\n    },\n  },\n  data() {\n    return {\n      flag:'jbxx', //默认展示基本信息列表\n    };\n  },\n  mounted() {\n  },\n  methods:{\n    //tab切换\n    click(mainTab){\n      this.flag = mainTab;\n    },\n    //表格双击事件\n    dbClickRow(val,map){\n      this.$refs.editJb.processParentVal(val,map);\n    },\n    setJbVal(val){\n      this.$emit('setJbVal',val);\n    },\n    //关闭脚本弹框\n    jbClose(){\n      this.$emit('jbClose');\n    },\n  },\n}\n</script>\n\n<style scoped lang=\"scss\">\n.jbwh_box{\n  padding: 20px 0 0 20px;\n}\n.jbwh_box1{\n  margin-top: -18px;\n}\n.tabActive{\n  //width: 10%;\n  float: left;\n  color:#fff;\n  background: #02b988;\n  border-top:0;\n}\n.noActive{\n  //width: 10%;\n  float: left;\n  background: #fff;\n  color:#545252;\n  &:hover {\n    background: #FFFFFF;\n    color: #359076;\n  }\n}\n.oneBtn{\n  margin-right: -15px;\n}\n.twoBtn{\n  transform: skewX(33deg);\n  border-right:  1px solid #9a989869;\n  .allBtn{\n    transform: skewX(-33deg);\n    display: inline-block;\n  }\n}\n\n</style>\n"]}]}