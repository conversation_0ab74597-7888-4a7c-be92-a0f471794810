{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\sbztpjbzk\\ztxxsjgl.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\sbztpjbzk\\ztxxsjgl.js", "mtime": 1706897314226}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0UGFnZUxpc3QgPSBnZXRQYWdlTGlzdDsKZXhwb3J0cy5zYXZlT3JVcGRhdGUgPSBzYXZlT3JVcGRhdGU7CmV4cG9ydHMucmVtb3ZlID0gcmVtb3ZlOwpleHBvcnRzLmdldEZseWpUcmVlID0gZ2V0Rmx5alRyZWU7CmV4cG9ydHMuZ2V0U3libU9wdGlvbnMgPSBnZXRTeWJtT3B0aW9uczsKCnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwoKdmFyIGJhc2VVcmwgPSAiL2NvbmRpdGlvbi1tYWludGVuYW5jZS1hcGkiOwp2YXIgbWFuYWdlclVybCA9ICcvbWFuYWdlci1hcGknOwoKZnVuY3Rpb24gZ2V0UGFnZUxpc3QocGFyYW1zKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcvenR4eHNqZ2xiL2dldE13dENibVp0eHhzamdsYkxpc3QnLCBwYXJhbXMsIDEpOwp9IC8vIOa3u+WKoOaIluS/ruaUuQoKCmZ1bmN0aW9uIHNhdmVPclVwZGF0ZShwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy96dHh4c2pnbGIvc2F2ZU9yVXBkYXRlJywgcGFyYW1zLCAxKTsKfSAvLyDliKDpmaQKCgpmdW5jdGlvbiByZW1vdmUocGFyYW1zKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcvenR4eHNqZ2xiL2RlbGV0ZVp0eHhzamdsYicsIHBhcmFtcywgMSk7Cn0gLy/liIbnsbvkvp3mja4KCgpmdW5jdGlvbiBnZXRGbHlqVHJlZShwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChtYW5hZ2VyVXJsICsgJy9ienF4Rmx5ai9nZXRGbHlqVHJlZScsIHBhcmFtcywgMSk7Cn0gLy/or5Xpqozpobnnm64KCgpmdW5jdGlvbiBnZXRTeWJtT3B0aW9ucyhwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChtYW5hZ2VyVXJsICsgJy9zeW1wL2dldFN5Ym1PcHRpb25zJywgcGFyYW1zLCAxKTsKfQ=="}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagang<PERSON>field/bzgl/sbztpjbzk/ztxxsjgl.js"], "names": ["baseUrl", "manager<PERSON>rl", "getPageList", "params", "api", "requestPost", "saveOrUpdate", "remove", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getSybmOptions"], "mappings": ";;;;;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,4BAAhB;AAEA,IAAMC,UAAU,GAAC,cAAjB;;AAEO,SAASC,WAAT,CAAqBC,MAArB,EAA6B;AAClC,SAAOC,iBAAIC,WAAJ,CAAgBL,OAAO,GAAC,mCAAxB,EAA4DG,MAA5D,EAAmE,CAAnE,CAAP;AACD,C,CAED;;;AACO,SAASG,YAAT,CAAsBH,MAAtB,EAA8B;AACnC,SAAOC,iBAAIC,WAAJ,CAAgBL,OAAO,GAAC,yBAAxB,EAAkDG,MAAlD,EAAyD,CAAzD,CAAP;AACD,C,CACA;;;AACM,SAASI,MAAT,CAAgBJ,MAAhB,EAAwB;AAC7B,SAAOC,iBAAIC,WAAJ,CAAgBL,OAAO,GAAC,4BAAxB,EAAqDG,MAArD,EAA4D,CAA5D,CAAP;AACD,C,CAED;;;AACO,SAASK,WAAT,CAAqBL,MAArB,EAA6B;AAClC,SAAOC,iBAAIC,WAAJ,CAAgBJ,UAAU,GAAC,uBAA3B,EAAmDE,MAAnD,EAA0D,CAA1D,CAAP;AAED,C,CAED;;;AACO,SAASM,cAAT,CAAwBN,MAAxB,EAAgC;AACrC,SAAOC,iBAAIC,WAAJ,CAAgBJ,UAAU,GAAC,sBAA3B,EAAkDE,MAAlD,EAAyD,CAAzD,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\nconst baseUrl = \"/condition-maintenance-api\";\n\nconst managerUrl='/manager-api';\n\nexport function getPageList(params) {\n  return api.requestPost(baseUrl+'/ztxxsjglb/getMwtCbmZtxxsjglbList',params,1)\n}\n\n// 添加或修改\nexport function saveOrUpdate(params) {\n  return api.requestPost(baseUrl+'/ztxxsjglb/saveOrUpdate',params,1)\n}\n // 删除\nexport function remove(params) {\n  return api.requestPost(baseUrl+'/ztxxsjglb/deleteZtxxsjglb',params,1)\n}\n\n//分类依据\nexport function getFlyjTree(params) {\n  return api.requestPost(managerUrl+'/bzqxFlyj/getFlyjTree',params,1)\n\n}\n\n//试验项目\nexport function getSybmOptions(params) {\n  return api.requestPost(managerUrl+'/symp/getSybmOptions',params,1)\n}\n"]}]}