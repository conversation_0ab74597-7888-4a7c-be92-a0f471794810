{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\sbztpjbzk\\pjdzwh.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\sbztpjbzk\\pjdzwh.js", "mtime": 1706897314226}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagang<PERSON>field/bzgl/sbztpjbzk/pjdzwh.js"], "names": ["baseUrl", "getPageDataListBJ", "query", "api", "requestPost", "removeBJ", "saveOrUpdateBJ", "getPageDataListZL", "removeZL", "saveOrUpdateZL", "getDeviceClasssblxSbzl", "params", "requestGet"], "mappings": ";;;;;;;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,4BAAhB;AAGA;;;;;;AAKQ,SAASC,iBAAT,CAA2BC,KAA3B,EAAiC;AACrC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,+BAAxB,EAAwDE,KAAxD,EAA+D,CAA/D,CAAP;AACH;AAED;;;;;;;AAKQ,SAASG,QAAT,CAAkBH,KAAlB,EAAwB;AAC5B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,8BAAxB,EAAuDE,KAAvD,EAA8D,CAA9D,CAAP;AACH;AAED;;;;;;;AAKO,SAASI,cAAT,CAAwBJ,KAAxB,EAA8B;AACjC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,0BAAxB,EAAmDE,KAAnD,EAA0D,CAA1D,CAAP;AACH;AAGD;;;;;;;AAKO,SAASK,iBAAT,CAA2BL,KAA3B,EAAiC;AACpC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,2BAAxB,EAAoDE,KAApD,EAA2D,CAA3D,CAAP;AACH;AAED;;;;;;;AAKQ,SAASM,QAAT,CAAkBN,KAAlB,EAAwB;AAC5B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,2BAAxB,EAAoDE,KAApD,EAA2D,CAA3D,CAAP;AACH;AAED;;;;;;;AAKO,SAASO,cAAT,CAAwBP,KAAxB,EAA8B;AACjC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,0BAAxB,EAAmDE,KAAnD,EAA0D,CAA1D,CAAP;AACH;AAED;;;;;;;AAKO,SAASQ,sBAAT,CAAgCC,MAAhC,EAAuC;AAC1C,SAAOR,iBAAIS,UAAJ,CAAe,0CAAf,EAA0DD,MAA1D,EAAkE,CAAlE,CAAP;AACH", "sourcesContent": ["import api from '@/utils/request'\r\nconst baseUrl = \"/condition-maintenance-api\";\r\n\r\n\r\n/**\r\n * 查询导则部件列表\r\n * @param query \r\n * @returns \r\n */\r\n export function getPageDataListBJ(query){\r\n    return api.requestPost(baseUrl+'/mwtCbmSbbj/getMwtCbmSbbjList',query, 2)\r\n}\r\n\r\n/**\r\n * 删除一条或者多条导则部件\r\n * @param query \r\n * @returns \r\n */\r\n export function removeBJ(query){\r\n    return api.requestPost(baseUrl+'/mwtCbmSbbj/deleteMwtCbmSbbj',query, 2)\r\n}\r\n\r\n/**\r\n * 添加或者修改导则部件\r\n * @param  query \r\n * @returns \r\n */\r\nexport function saveOrUpdateBJ(query){\r\n    return api.requestPost(baseUrl+'/mwtCbmSbbj/saveOrUpdate',query, 2)\r\n}\r\n\r\n\r\n/**\r\n * 导则设备种类\r\n * @param {*} query \r\n * @returns \r\n */\r\nexport function getPageDataListZL(query){\r\n    return api.requestPost(baseUrl+'/mwtCbmSbzl/getMwtCbmSbzl',query, 2)\r\n}\r\n\r\n/**\r\n * 删除一条或者多条导则设备种类\r\n * @param query \r\n * @returns \r\n */\r\n export function removeZL(query){\r\n    return api.requestPost(baseUrl+'/mwtCbmSbzl/deleteMwtCbzl',query, 2)\r\n}\r\n\r\n/**\r\n * 添加或者修改导则设备种类\r\n * @param  query \r\n * @returns \r\n */\r\nexport function saveOrUpdateZL(query){\r\n    return api.requestPost(baseUrl+'/mwtCbmSbzl/saveOrUpdate',query, 2)\r\n}\r\n\r\n/**\r\n * 根据设备获取设备种类\r\n * @param {*} params \r\n * @returns \r\n */\r\nexport function getDeviceClasssblxSbzl(params){\r\n    return api.requestGet(\"/manager-api/sblx/getDeviceClasssblxSbzl\",params, 2)\r\n}\r\n\r\n\r\n  "]}]}