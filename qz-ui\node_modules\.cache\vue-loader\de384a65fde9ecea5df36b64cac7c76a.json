{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\sblxwh.vue?vue&type=style&index=0&id=79a8f92c&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\sblxwh.vue", "mtime": 1727417331943}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouYm94LWNhcmQgLmVsLWNhcmRfX2hlYWRlciB7CiAgYmFja2dyb3VuZDogIzExYmE2ZCAhaW1wb3J0YW50Owp9Ci5ib3gtY2FyZCB7CiAgbWFyZ2luOiAwOwp9CgouaXRlbSB7CiAgd2lkdGg6IDIwMHB4OwogIGhlaWdodDogMTQ4cHg7CiAgZmxvYXQ6IGxlZnQ7Cn0KCi50cmVlIHsKICBvdmVyZmxvdy15OiBoaWRkZW47CiAgb3ZlcmZsb3cteDogc2Nyb2xsOwogIHdpZHRoOiA4MHB4OwogIGhlaWdodDogNTAwcHg7Cn0KCi5lbC10cmVlIHsKICBtaW4td2lkdGg6IDEwMCU7CiAgZGlzcGxheTogaW5saW5lLWJsb2NrICFpbXBvcnRhbnQ7Cn0KCi5oZWFkLWNvbnRhaW5lciB7CiAgbWFyZ2luOiAwIGF1dG87CiAgd2lkdGg6IDk4JTsKICBtYXgtaGVpZ2h0OiA3OXZoOwogIG92ZXJmbG93OiBhdXRvOwp9Cg=="}, {"version": 3, "sources": ["sblxwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwbA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "sblxwh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row :gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card class=\"aside_height\" shadow=\"never\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备类型</span>\n          </div>\n          <div style=\" overflow: auto;height: 84.5vh\">\n            <el-tree\n              ref=\"elTree\"\n              :data=\"treeData\"\n              node-key=\"code\"\n              highlight-current\n              default-expand-all\n              :props=\"defaultProps\"\n              @node-click=\"handleNodeClick\"\n            />\n          </div>\n        </el-card>\n      </el-col>\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 120, itemWidth: 180 }\"\n          @handleReset=\"filterReset\"\n        />\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button\n              type=\"primary\"\n              v-hasPermi=\"['bzsbflbzgl:button:add']\"\n              icon=\"el-icon-plus\"\n              @click=\"addDeviceClassify\"\n              >新增</el-button\n            >\n          </div>\n          <comp-table\n            ref=\"deviceTypeTable\"\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            v-loading=\"loading\"\n            height=\"70.1vh\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  type=\"text\"\n                  v-hasPermi=\"['bzsbflbzgl:button:update']\"\n                  @click=\"updateDeviceClassify(scope.row)\"\n                  class=\"updateBtn el-icon-edit\"\n                  title=\"修改\"\n                ></el-button>\n                <el-button\n                  type=\"text\"\n                  @click=\"getDeviceClassifyDetails(scope.row)\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n                <el-button\n                  type=\"text\"\n                  v-if=\"\n                    scope.row.createBy === $store.getters.name ||\n                      $store.getters.hasSuperRole\n                  \"\n                  title=\"删除\"\n                  icon=\"el-icon-delete\"\n                  @click=\"deleteDeviceClassify(scope.row)\"\n                >\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <dialogForm\n      v-dialogDrag\n      ref=\"dialogForm\"\n      label-width=\"150px\"\n      out-width=\"50%\"\n      :reminder=\"reminder\"\n      :rows=\"rows\"\n      @save=\"saveDeviceClassify\"\n    />\n\n    <el-dialog\n      v-dialogDrag\n      :append-to-body=\"true\"\n      title=\"技术参数/设备部件维护\"\n      :visible.sync=\"isShowParamsAndParts\"\n    >\n      <technical-and-part\n        :device-type-data=\"selectedRowData\"\n        @closeParamDialog=\"closeParamDialog\"\n      >\n      </technical-and-part>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport TechnicalAndPart from \"@/views/dagangOilfield/bzgl/sbbzk/technicalAndPart\";\nimport deviceClassifyMixin from \"@/mixins/deviceClassifyMixin\";\nimport dialogForm from \"com/dialogFrom/dialogForm\";\nimport {\n  saveOrUpdateMwtUdGySblx,\n  deleteDeviceClassify\n} from \"@/api/dagangOilfield/bzgl/sblxwh/sblxwh\";\n\nexport default {\n  components: { TechnicalAndPart, dialogForm },\n  name: \"sblxwh\",\n  mixins: [deviceClassifyMixin],\n  data() {\n    return {\n      resolve: {},\n      filterInfo: {\n        data: {},\n        fieldList: [\n          { label: \"设备类型编码\", type: \"input\", value: \"sblxbm\" },\n          { label: \"设备类型名称\", type: \"input\", value: \"sblx\" }\n        ]\n      },\n      reminder: \"修改\",\n      rows: 2,\n      formList: [\n        {\n          label: \"设备类型编码：\",\n          value: \"\",\n          name: \"sblxbm\",\n          default: true,\n          type: \"input\",\n          rules: {\n            required: true,\n            message: \"请输入设备类型编码\",\n            trigger: \"blur\"\n          }\n        },\n        {\n          label: \"设备类型名称：\",\n          value: \"\",\n          name: \"sblx\",\n          default: true,\n          type: \"input\",\n          rules: {\n            required: true,\n            message: \"请输入设备类型名称\",\n            trigger: \"blur\"\n          }\n        },\n        {\n          label: \"父类：\",\n          value: \"\",\n          name: \"fsblx\",\n          default: true,\n          type: \"disabled\",\n          rules: { required: true, message: \"请输入父类\", trigger: \"blur\" }\n        },\n        {\n          label: \"排序：\",\n          value: \"\",\n          name: \"px\",\n          default: true,\n          type: \"input\",\n          rules: { required: true, message: \"输入排序\", trigger: \"blur\" }\n        },\n        {\n          label: \"备注：\",\n          value: \"\",\n          name: \"bz\",\n          default: true,\n          type: \"textarea\",\n          rules: { required: false, message: \"请输入备注\" }\n        },\n        {\n          label: \"id：\",\n          value: \"\",\n          name: \"objId\",\n          default: true,\n          hidden: false,\n          type: \"input\",\n          rules: { required: false, message: \"请输入编码\" }\n        },\n        {\n          label: \"专业性质：\",\n          value: \"\",\n          type: \"input\",\n          name: \"zyxz\",\n          hidden: false,\n          default: true,\n          rules: { required: false, message: \"请选择专业性质\" }\n        },\n        {\n          label: \"父设备类型编码：\",\n          value: \"\",\n          type: \"input\",\n          name: \"fsblxid\",\n          hidden: false,\n          default: true,\n          rules: { required: false, message: \"请选择父设备类型编码\" }\n        },\n        {\n          label: \"设备类型全路径：\",\n          value: \"\",\n          type: \"input\",\n          name: \"fullpath\",\n          hidden: false,\n          default: true,\n          rules: { required: false, message: \"请选择设备类型全路径\" }\n        }\n      ],\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        total: 0,\n        fsblxid: \"\",\n        sblxbm: \"\"\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageResize: \"\",\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: false,\n          serialNumber: false\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"sblxbm\", label: \"设备类型编码\" },\n          { prop: \"sblx\", label: \"设备类型名称\" },\n          { prop: \"fsblx\", label: \"父类\" }\n        ]\n      },\n      loading: false,\n      treeNodeData: {},\n      isShowParamsAndParts: false,\n      selectedRowData: {},\n      selectedRowDataArr: [],\n      dialogVisible: false,\n      dialogTitle: \"\",\n      form: {},\n      defaultProps: {\n        children: \"children\",\n        label: \"name\",\n        isLeaf: (data, node) => {\n          if (node.level === 1) {\n            return true;\n          }\n        }\n      },\n      treeData: []\n    };\n  },\n  watch: {\n    isShowParamsAndParts(val) {\n      if (val) {\n        const el = this.$el.querySelector(\".el-dialog\");\n        el.style.left = 0;\n        el.style.top = 0;\n      }\n    }\n  },\n  created() {\n    this.loadNode();\n    this.getData();\n  },\n  methods: {\n    filterReset() {\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        total: 0,\n        fsblxid: \"\",\n        sblxbm: \"\"\n      };\n      this.treeNodeData = {};\n      this.$refs.elTree.setCurrentKey(null);\n    },\n    handleNodeClick(node) {\n      this.treeNodeData = node;\n      const { code } = node;\n      this.queryParams.fsblxid = code;\n      this.queryParams.sblxbm = \"\";\n      this.getData();\n    },\n    handleSelectionChange(row) {\n      this.selectedRowDataArr = row;\n    },\n    async getData(params) {\n      this.loading = true;\n      if (this.queryParams.sblxbm == \"\") {\n        this.queryParams.fsblxid === \"\"\n          ? (this.queryParams.fsblxid = \"sb\")\n          : this.queryParams.fsblxid;\n      } else {\n        this.queryParams.fsblxid = \"\";\n      }\n      this.queryParams = { ...this.queryParams, ...params };\n      const param = this.queryParams;\n      const data = await this.fetchDeviceClassifyData(param);\n      if (data) {\n        this.tableAndPageInfo.tableData = data.records;\n        this.tableAndPageInfo.pager.total = data.total;\n      }\n      this.loading = false;\n    },\n    addDeviceClassify() {\n      if (this.treeNodeData.jscsNum > 0 || this.treeNodeData.sbbjNum > 0) {\n        this.$message({\n          message: \"该设备已维护技术参数或部件,不可新增!\",\n          type: \"warning\"\n        });\n      } else {\n        this.reminder = \"新增\";\n        //初始化formList数据\n        this.formList = this.$options.data().formList;\n        const addForm = this.formList.map(item => {\n          if (item.name === \"fsblxid\") {\n            item.value = this.treeNodeData.code || \"sb\";\n          }\n          if (item.name === \"fsblx\") {\n            item.value = this.treeNodeData.name || \"设备\";\n          }\n          return item;\n        });\n        this.$refs.dialogForm.showzzc(addForm);\n      }\n    },\n    updateDeviceClassify(row) {\n      const updateList = this.formList.map(item => {\n        item.value = row[item.name];\n        return item;\n      });\n      this.reminder = \"修改\";\n      this.$refs.dialogForm.showzzc(updateList);\n    },\n    async deleteDeviceClassify(row) {\n      this.$confirm(\"确认删除选中数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(async () => {\n          let deleteAble = true;\n          let message = \"\";\n          let deviceTypeCode = [];\n          deviceTypeCode.push(row.sblxbm);\n          if (row.childNum > 0) {\n            deleteAble = false;\n            message += row.sblx;\n          }\n          if (deleteAble) {\n            const res = await deleteDeviceClassify({\n              ids: [row.objId],\n              sblxbmArr: deviceTypeCode\n            });\n            if (res.code === \"0000\") {\n              this.$message.success(\"删除成功\");\n              this.treeData = await this.fetchTreeNodeData();\n              this.getData();\n            } else {\n              this.$message.error(\"操作失败\");\n            }\n          } else {\n            this.$message.warning(\n              message + \"包含已维护子级设备,请优先删除子级设备后在删除该设备!\"\n            );\n          }\n        })\n        .catch(() => {\n        });\n    },\n    getDeviceClassifyDetails(row) {\n      const infoList = this.formList.map(item => {\n        item.value = row[item.name];\n        return item;\n      });\n      this.reminder = \"详情\";\n      this.$refs.dialogForm.showxq(infoList);\n    },\n    async saveDeviceClassify(formData) {\n      if (formData.fsblxid === \"sb\") {\n        formData.fsblx = \"设备\";\n        formData.cj = '0'\n      }\n      try {\n        const res = await saveOrUpdateMwtUdGySblx(formData);\n        if (res.code === \"0000\") {\n          this.$message.success(formData.objId ? \"修改成功\" : \"新增成功\");\n          this.treeData = await this.fetchTreeNodeData();\n          this.getData();\n        } else {\n          this.$message.error(\"操作失败\");\n        }\n      } catch (error) {\n        console.error(\"保存设备分类失败:\", error);\n        this.$message.error(\"保存失败，请稍后重试\");\n      }\n    },\n    async loadNode() {\n      // if (node.level === 0) {\n      //   resolve([{ name: \"设备\", code: \"sb\" }]);\n      // } else {\n      // const { code } = node.data;\n      this.treeData = await this.fetchTreeNodeData();\n      // resolve(children);\n      // }\n    },\n    isShowButton(row) {\n      return row.childNum === 0;\n    },\n    showParamDialog(row) {\n      this.selectedRowData = row;\n      this.isShowParamsAndParts = true;\n    },\n    closeParamDialog() {\n      this.isShowParamsAndParts = false;\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.box-card .el-card__header {\n  background: #11ba6d !important;\n}\n.box-card {\n  margin: 0;\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.tree {\n  overflow-y: hidden;\n  overflow-x: scroll;\n  width: 80px;\n  height: 500px;\n}\n\n.el-tree {\n  min-width: 100%;\n  display: inline-block !important;\n}\n\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  max-height: 79vh;\n  overflow: auto;\n}\n</style>\n<style></style>\n"]}]}