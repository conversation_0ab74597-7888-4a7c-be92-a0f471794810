{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sympk\\sympk.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sympk\\sympk.vue", "mtime": 1706897323740}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["sympk.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AA8PA;;AAQA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,QAAA,EAAA,iBAAA;AAAA,IAAA,QAAA,EAAA;AAAA,GAFA;AAGA,EAAA,MAAA,EAAA,CAAA,QAAA,CAHA;AAGA;AACA,EAAA,IAJA,kBAIA;AACA,WAAA;AACA,MAAA,MAAA,EAAA,EADA;AAEA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,IAAA,EAAA,EADA;AAEA,UAAA,EAAA,EAAA,EAFA;AAGA,UAAA,IAAA,EAAA;AAHA,SADA;AAMA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,MAFA;AAGA,UAAA,IAAA,EAAA,OAHA;AAIA,UAAA,SAAA,EAAA;AAJA,SADA,EAOA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,MAFA;AAGA,UAAA,IAAA,EAAA,QAHA;AAIA,UAAA,OAAA,EAAA,CACA;AAAA,YAAA,KAAA,EAAA,GAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WADA,EAEA;AAAA,YAAA,KAAA,EAAA,GAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAFA,CAJA;AAQA,UAAA,SAAA,EAAA;AARA,SAPA;AANA,OAHA;AA4BA,MAAA,WAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,IA5BA;AA6BA,MAAA,GAAA,EAAA,EA7BA;AA8BA,MAAA,KAAA,EAAA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,QAAA,EAAA,OAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OA9BA;AAmCA;AACA,MAAA,OAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAAA,EAAA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAAA,CApCA;AAqCA;AACA,MAAA,WAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAAA,EAAA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAAA,CAtCA;AAuCA,MAAA,IAAA,EAAA;AACA,QAAA,KAAA,EAAA,SADA;AAEA,QAAA,MAAA,EAAA,SAFA;AAGA,QAAA,IAAA,EAAA,SAHA;AAIA,QAAA,EAAA,EAAA,SAJA;AAKA,QAAA,IAAA,EAAA,EALA;AAMA,QAAA,QAAA,EAAA;AANA,OAvCA;AA+CA,MAAA,MAAA,EAAA;AACA,QAAA,MAAA,EAAA,SADA;AAEA,QAAA,IAAA,EAAA,CAFA;AAGA,QAAA,OAAA,EAAA,CAHA;AAIA,QAAA,IAAA,EAAA,CAJA;AAKA,QAAA,OAAA,EAAA,CALA;AAMA,QAAA,IAAA,EAAA,CANA;AAOA,QAAA,OAAA,EAAA,CAPA;AAQA,QAAA,IAAA,EAAA,CARA;AASA,QAAA,OAAA,EAAA,CATA;AAUA,QAAA,UAAA,EAAA,CAVA;AAWA,QAAA,UAAA,EAAA,CAXA;AAYA,QAAA,IAAA,EAAA,SAZA;AAaA,QAAA,KAAA,EAAA;AAbA,OA/CA;AA8DA,MAAA,aAAA,EAAA,KA9DA;AA+DA,MAAA,eAAA,EAAA,KA/DA;AAgEA,MAAA,KAAA,EAAA,EAhEA;AAiEA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,CAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA,SARA;AA0BA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AA1BA,OAjEA;AA6FA;AACA,MAAA,cAAA,EAAA,IA9FA;AA+FA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,EAAA,EAAA,EADA;AAEA,QAAA,MAAA,EAAA,SAFA;AAGA,QAAA,OAAA,EAAA,CAHA;AAIA,QAAA,QAAA,EAAA,EAJA;AAKA,QAAA,QAAA,EAAA;AALA,OAhGA;AAuGA,MAAA,UAAA,EAAA,KAvGA;AAwGA,MAAA,KAAA,EAAA;AACA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CADA;AAEA,QAAA,EAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,OAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAFA;AAGA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AAHA,OAxGA;AA6GA,MAAA,SAAA,EAAA,EA7GA;AA6GA;AACA,MAAA,UAAA,EAAA,SA9GA;AA8GA;AACA,MAAA,UAAA,EAAA,SA/GA;AA+GA;AACA,MAAA,YAAA,EAAA,KAhHA;AAiHA;AACA,MAAA,OAAA,EAAA,EAlHA;AAmHA;AACA,MAAA,MAAA,EAAA,EApHA;AAqHA,MAAA,MAAA,EAAA,EArHA,CAqHA;;AArHA,KAAA;AAuHA,GA5HA;AA6HA,EAAA,KAAA,EAAA,EA7HA;AA8HA,EAAA,OA9HA,qBA8HA;AACA,SAAA,OAAA;AAEA,GAjIA;AAkIA,EAAA,OAAA,EAAA;AACA,IAAA,OADA,mBACA,KADA,EACA;AACA,WAAA,OAAA,CAAA,KAAA;AACA,KAHA;AAIA,IAAA,WAJA,uBAIA,IAJA,EAIA,IAJA,EAIA;AACA,WAAA,MAAA,GAAA,IAAA;AACA,KANA;AAOA;AACA,IAAA,QARA,sBAQA;AACA,WAAA,MAAA,GAAA,EAAA;AACA,WAAA,OAAA;AACA,KAXA;AAYA;AACA,IAAA,eAbA,2BAaA,IAbA,EAaA;AACA,WAAA,IAAA,CAAA,MAAA,GAAA,IAAA,CAAA,IAAA;AACA,WAAA,MAAA,GAAA,IAAA,CAAA,IAAA;AACA,WAAA,WAAA,CAAA,MAAA,GAAA,IAAA,CAAA,IAAA;;AACA,UAAA,IAAA,CAAA,GAAA,IAAA,IAAA,EAAA;AACA,aAAA,WAAA,CAAA,EAAA,GAAA,IAAA,CAAA,GAAA,CAAA,SAAA,CAAA,CAAA,EAAA,CAAA,CAAA;AACA;;AACA,WAAA,OAAA;AACA,KArBA;;AAsBA;;;AAGA,IAAA,qBAzBA,iCAyBA,SAzBA,EAyBA;AACA,WAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,KAAA;AAAA,OAAA,CAAA;AACA,WAAA,SAAA,GAAA,SAAA;AACA,KA5BA;AA6BA;AACA,IAAA,SA9BA,uBA8BA;AACA,UAAA,KAAA,IAAA,CAAA,MAAA,IAAA,SAAA,EAAA;AACA,aAAA,aAAA,GAAA,IAAA;AACA,aAAA,UAAA,GAAA,KAAA;AACA,aAAA,IAAA,CAAA,KAAA,GAAA,SAAA;AACA,aAAA,IAAA,CAAA,IAAA,GAAA,SAAA;AACA,aAAA,IAAA,CAAA,EAAA,GAAA,SAAA;AACA,aAAA,KAAA,GAAA,IAAA;AACA,OAPA,MAOA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,eAAA;AACA;AACA;AACA,KA1CA;AA2CA,IAAA,KA3CA,mBA2CA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,KA7CA;AA8CA,IAAA,aA9CA,yBA8CA,GA9CA,EA8CA;AACA,WAAA,KAAA,GAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,IAAA,GAAA,GAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,KAnDA;AAoDA,IAAA,UApDA,sBAoDA,GApDA,EAoDA;AACA,WAAA,KAAA,GAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,IAAA,GAAA,GAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KAzDA;AA0DA,IAAA,IA1DA,kBA0DA;AAAA;;AACA,WAAA,KAAA,CAAA,IAAA,CAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,mCAAA,KAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,cAAA,KAAA,CAAA,QAAA,CAAA,OAAA,CAAA,OAAA;;AACA,cAAA,KAAA,CAAA,aAAA,GAAA,KAAA;;AACA,cAAA,KAAA,CAAA,OAAA;AACA;AACA,WANA;AAOA,SARA,MAQA;AACA,UAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,YAAA;;AACA,iBAAA,KAAA;AACA;AACA,OAbA;AAcA,KAzEA;AA0EA;AACA,IAAA,YA3EA,wBA2EA,GA3EA,EA2EA;AAAA;;AACA,WAAA,IAAA,GAAA,GAAA;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,2BAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA;AACA,YAAA,IAAA,EAAA,SADA;AAEA,YAAA,OAAA,EAAA;AAFA,WAAA;;AAIA,UAAA,MAAA,CAAA,OAAA;AACA,SANA;AAOA,OAZA;AAaA,KA1FA;AA2FA;AACA,IAAA,UA5FA,wBA4FA;AAAA;;AACA,UAAA,KAAA,GAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,eAAA,GAAA,IAAA;AACA,aAAA,MAAA,CAAA,MAAA,GAAA,KAAA,GAAA,CAAA,CAAA,CAAA;AACA,aAAA,MAAA,CAAA,IAAA,GAAA,KAAA,SAAA,CAAA,CAAA,EAAA,IAAA;AACA,aAAA,MAAA,CAAA,OAAA,GAAA,KAAA,SAAA,CAAA,CAAA,EAAA,IAAA;AACA,aAAA,MAAA,CAAA,IAAA,GAAA,KAAA,SAAA,CAAA,CAAA,EAAA,IAAA;AACA,aAAA,MAAA,CAAA,OAAA,GAAA,KAAA,SAAA,CAAA,CAAA,EAAA,IAAA;AACA,aAAA,MAAA,CAAA,IAAA,GAAA,KAAA,SAAA,CAAA,CAAA,EAAA,IAAA;AACA,aAAA,MAAA,CAAA,OAAA,GAAA,KAAA,SAAA,CAAA,CAAA,EAAA,IAAA;AACA,aAAA,MAAA,CAAA,IAAA,GAAA,KAAA,SAAA,CAAA,CAAA,EAAA,IAAA;AACA,aAAA,MAAA,CAAA,OAAA,GAAA,KAAA,SAAA,CAAA,CAAA,EAAA,IAAA;AACA,aAAA,MAAA,CAAA,KAAA,GAAA,KAAA,SAAA,CAAA,CAAA,EAAA,IAAA,CAXA,CAYA;;AACA,YAAA,KAAA,SAAA,CAAA,CAAA,EAAA,IAAA,IAAA,EAAA,IAAA,KAAA,SAAA,CAAA,CAAA,EAAA,IAAA,IAAA,EAAA,IAAA,KAAA,SAAA,CAAA,CAAA,EAAA,IAAA,IAAA,CAAA,IAAA,KAAA,SAAA,CAAA,CAAA,EAAA,IAAA,IAAA,CAAA,EAAA;AACA,eAAA,MAAA,CAAA,IAAA,GAAA,GAAA;AACA,+BAAA,KAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,GAAA,EAAA;;AACA,iBAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,IAAA,EAAA,CAAA,EAAA,EAAA;AACA,kBAAA,IAAA,GAAA,MAAA;;AACA,mBAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,oBAAA,CAAA,IAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,QAAA,EAAA;AACA,sBAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,IAAA;AACA,sBAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,IAAA,CAFA,CAGA;;AACA,sBAAA,KAAA,GAAA,EAAA;;AACA,sBAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,GAAA,EAAA;AAAA;AACA,oBAAA,KAAA,GAAA,mBAAA;AACA,mBAFA,MAEA,IAAA,IAAA,IAAA,GAAA,EAAA;AAAA;AACA,oBAAA,KAAA,GAAA,qBAAA;AACA,mBAFA,MAEA,IAAA,IAAA,IAAA,EAAA,IAAA,IAAA,IAAA,SAAA,IAAA,IAAA,IAAA,WAAA,EAAA;AAAA;AACA,oBAAA,KAAA,GAAA,qBAAA;AACA;;AACA,sBAAA,IAAA,IAAA,EAAA,IAAA,IAAA,IAAA,WAAA,IAAA,IAAA,IAAA,SAAA,EAAA;AACA,oBAAA,IAAA,GAAA,GAAA;AACA,mBAFA,MAEA,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,KAAA,CAAA,CAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,EAAA,CAEA,CAFA,MAEA,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,KAAA,CAAA,CAAA,EAAA;AACA,oBAAA,IAAA,GAAA,IAAA,CAAA,OAAA,CAAA,oBAAA,EAAA,EAAA,CAAA;AACA;;AAEA,sBAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,OAAA,IAAA,GAAA,IAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,OAAA,IAAA,GAAA,EAAA;AACA,oBAAA,IAAA,IAAA,wBAAA,KAAA,GAAA,YAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,OAAA,GAAA,eAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,OAAA,GAAA,UAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,MAAA,GAAA,KAAA,GAAA,IAAA,GAAA,OAAA;AACA,mBAFA,MAEA;AACA,oBAAA,IAAA,IAAA,wBAAA,KAAA,GAAA,QAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,MAAA,GAAA,oCAAA,GAAA,IAAA,GAAA,OAAA;AACA;AACA;AACA;;AACA,cAAA,IAAA,IAAA,OAAA;AACA,cAAA,GAAA,IAAA,IAAA;AACA;;AACA,YAAA,MAAA,CAAA,UAAA,GAAA,SAAA;AACA,YAAA,MAAA,CAAA,UAAA,GAAA,GAAA;AACA,WArCA;AAsCA,SAxCA,MAwCA;AACA,eAAA,UAAA,GAAA,SAAA;AACA,SAvDA,CAwDA;;;AACA,YAAA,KAAA,SAAA,CAAA,CAAA,EAAA,IAAA,IAAA,EAAA,IAAA,KAAA,SAAA,CAAA,CAAA,EAAA,IAAA,IAAA,EAAA,IAAA,KAAA,SAAA,CAAA,CAAA,EAAA,IAAA,IAAA,CAAA,IAAA,KAAA,SAAA,CAAA,CAAA,EAAA,IAAA,IAAA,CAAA,EAAA;AACA,eAAA,MAAA,CAAA,IAAA,GAAA,GAAA;AACA,+BAAA,KAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,GAAA,EAAA;;AACA,iBAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,IAAA,EAAA,CAAA,EAAA,EAAA;AACA,kBAAA,IAAA,GAAA,MAAA;;AACA,mBAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,oBAAA,CAAA,IAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,QAAA,EAAA;AACA,sBAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,IAAA;AACA,sBAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,IAAA,CAFA,CAGA;;AACA,sBAAA,KAAA,GAAA,EAAA;;AACA,sBAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,GAAA,EAAA;AAAA;AACA,oBAAA,KAAA,GAAA,mBAAA;AACA,mBAFA,MAEA,IAAA,IAAA,IAAA,GAAA,EAAA;AAAA;AACA,oBAAA,KAAA,GAAA,qBAAA;AACA,mBAFA,MAEA,IAAA,IAAA,IAAA,EAAA,IAAA,IAAA,IAAA,SAAA,IAAA,IAAA,IAAA,WAAA,EAAA;AAAA;AACA,oBAAA,KAAA,GAAA,qBAAA;AACA;;AACA,sBAAA,IAAA,IAAA,EAAA,IAAA,IAAA,IAAA,WAAA,IAAA,IAAA,IAAA,SAAA,EAAA;AACA,oBAAA,IAAA,GAAA,GAAA;AACA,mBAFA,MAEA,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,KAAA,CAAA,CAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,EAAA,CAEA,CAFA,MAEA,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,KAAA,CAAA,CAAA,EAAA;AACA,oBAAA,IAAA,GAAA,IAAA,CAAA,OAAA,CAAA,oBAAA,EAAA,EAAA,CAAA;AACA;;AAEA,sBAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,OAAA,IAAA,GAAA,IAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,OAAA,IAAA,GAAA,EAAA;AACA,oBAAA,IAAA,IAAA,wBAAA,KAAA,GAAA,YAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,OAAA,GAAA,eAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,OAAA,GAAA,UAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,MAAA,GAAA,KAAA,GAAA,IAAA,GAAA,OAAA;AACA,mBAFA,MAEA;AACA,oBAAA,IAAA,IAAA,wBAAA,KAAA,GAAA,QAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,MAAA,GAAA,KAAA,GAAA,IAAA,GAAA,OAAA;AACA;AACA;AACA;;AACA,cAAA,IAAA,IAAA,OAAA;AACA,cAAA,GAAA,IAAA,IAAA;AACA;;AACA,YAAA,MAAA,CAAA,UAAA,GAAA,SAAA;AACA,YAAA,MAAA,CAAA,UAAA,GAAA,GAAA;AACA,WArCA;AAsCA,SAxCA,MAwCA;AACA,eAAA,UAAA,GAAA,SAAA;AACA;AACA,OApGA,MAoGA;AACA,aAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA;AACA,KAvMA;AAwMA;AACA,IAAA,OAzMA,mBAyMA,MAzMA,EAyMA;AAAA;;AACA,WAAA,WAAA,+DAAA,KAAA,WAAA,GAAA,MAAA;AACA,UAAA,KAAA,+DAAA,KAAA,WAAA,GAAA,MAAA,CAAA;AACA,kCAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;;AAEA,QAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,MAAA,CAAA,OAAA,CAAA,OAAA,CAAA,UAAA,OAAA,EAAA;AACA,gBAAA,IAAA,CAAA,EAAA,KAAA,OAAA,CAAA,KAAA,EAAA;AACA,cAAA,IAAA,CAAA,MAAA,GAAA,OAAA,CAAA,KAAA;AACA;AACA,WAJA;;AAKA,UAAA,MAAA,CAAA,WAAA,CAAA,OAAA,CAAA,UAAA,OAAA,EAAA;AACA,gBAAA,IAAA,CAAA,IAAA,KAAA,OAAA,CAAA,KAAA,EAAA;AACA,cAAA,IAAA,CAAA,QAAA,GAAA,OAAA,CAAA,KAAA;AACA;AACA,WAJA;AAKA,SAXA;AAYA,OAhBA;AAiBA,KA7NA;AA8NA;AACA,IAAA,QA/NA,oBA+NA,IA/NA,EA+NA,OA/NA,EA+NA;AAAA;;AACA,UAAA,YAAA,GAAA;AACA,QAAA,GAAA,EAAA,EADA;AAEA,QAAA,OAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,MAAA;AAFA,OAAA;;AAIA,UAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA;AACA,QAAA,YAAA,CAAA,GAAA,GAAA,IAAA;AACA,eAAA,KAAA,WAAA,CAAA,YAAA,EAAA,OAAA,CAAA;AACA;;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,YAAA,CAAA,GAAA,GAAA,IAAA,CAAA,IAAA,CAAA,IAAA;;AACA,QAAA,MAAA,CAAA,WAAA,CAAA,YAAA,EAAA,OAAA;AACA,OAHA,EAGA,GAHA,CAAA;AAKA,KA7OA;AA8OA;AACA,IAAA,WA/OA,uBA+OA,QA/OA,EA+OA,OA/OA,EA+OA;AACA,8CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,SAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,GAAA;AACA,YAAA,IAAA,EAAA,IAAA,CAAA,IADA;AAEA,YAAA,KAAA,EAAA,IAAA,CAAA,KAFA;AAGA,YAAA,EAAA,EAAA,IAAA,CAAA,EAHA;AAIA,YAAA,GAAA,EAAA,IAAA,CAAA,GAJA;AAKA,YAAA,IAAA,EAAA,KALA;AAMA,YAAA,IAAA,EAAA,IAAA,CAAA;AANA,WAAA;AAQA,UAAA,SAAA,CAAA,IAAA,CAAA,IAAA;AACA,SAVA;AAWA,QAAA,OAAA,CAAA,SAAA,CAAA;AACA,OAdA;AAeA,KA/PA;AAgQA;AACA,IAAA,UAjQA,wBAiQA;AAAA;;AACA;AACA,UAAA,KAAA,MAAA,CAAA,IAAA,IAAA,CAAA,IAAA,KAAA,MAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA;AACA,OARA,CASA;;;AACA,UAAA,UAAA,GAAA,KAAA,MAAA,CAAA,OAAA,GAAA,KAAA,MAAA,CAAA,IAAA,CAVA,CAUA;;AACA,UAAA,UAAA,GAAA,KAAA,MAAA,CAAA,OAAA,GAAA,KAAA,MAAA,CAAA,IAAA,CAXA,CAWA;;AACA,UAAA,UAAA,IAAA,CAAA,IAAA,UAAA,IAAA,CAAA,EAAA;AAAA;AACA;AACA,OAFA,MAEA,IAAA,UAAA,GAAA,CAAA,IAAA,UAAA,GAAA,CAAA,EAAA;AAAA;AACA,aAAA,QAAA,CAAA,SAAA,EAAA,IAAA,EAAA;AACA,UAAA,iBAAA,EAAA,IADA;AAEA,UAAA,gBAAA,EAAA,IAFA;AAGA,UAAA,IAAA,EAAA;AAHA,SAAA,EAIA,IAJA,CAIA,YAAA;AACA;AACA,UAAA,MAAA,CAAA,MAAA,CAAA,IAAA,GAAA,GAAA;AACA,qCAAA,MAAA,CAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,cAAA,MAAA,CAAA,WAAA,CAAA,GAAA;;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,OAAA;;AACA,cAAA,MAAA,CAAA,MAAA,CAAA,OAAA,GAAA,MAAA,CAAA,MAAA,CAAA,IAAA;AACA,cAAA,MAAA,CAAA,MAAA,CAAA,OAAA,GAAA,MAAA,CAAA,MAAA,CAAA,IAAA;AACA;AACA,WAPA;AAQA,SAfA;AAgBA,OAjBA,MAiBA;AACA;AACA,aAAA,MAAA,CAAA,IAAA,GAAA,GAAA;AACA,mCAAA,KAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,WAAA,CAAA,GAAA;;AACA,YAAA,MAAA,CAAA,MAAA,CAAA,OAAA,GAAA,MAAA,CAAA,MAAA,CAAA,IAAA;AACA,YAAA,MAAA,CAAA,MAAA,CAAA,OAAA,GAAA,MAAA,CAAA,MAAA,CAAA,IAAA;AACA;AACA,SANA;AAOA;AACA,KA3SA;AA4SA;AACA,IAAA,UA7SA,wBA6SA;AAAA;;AACA;AACA,UAAA,KAAA,MAAA,CAAA,IAAA,IAAA,CAAA,IAAA,KAAA,MAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA;AACA,OARA,CASA;;;AACA,UAAA,UAAA,GAAA,KAAA,MAAA,CAAA,OAAA,GAAA,KAAA,MAAA,CAAA,IAAA,CAVA,CAUA;;AACA,UAAA,UAAA,GAAA,KAAA,MAAA,CAAA,OAAA,GAAA,KAAA,MAAA,CAAA,IAAA,CAXA,CAWA;;AACA,UAAA,UAAA,IAAA,CAAA,IAAA,UAAA,IAAA,CAAA,EAAA;AAAA;AACA;AACA,OAFA,MAEA,IAAA,UAAA,GAAA,CAAA,IAAA,UAAA,GAAA,CAAA,EAAA;AAAA;AACA,aAAA,QAAA,CAAA,SAAA,EAAA,IAAA,EAAA;AACA,UAAA,iBAAA,EAAA,IADA;AAEA,UAAA,gBAAA,EAAA,IAFA;AAGA,UAAA,IAAA,EAAA;AAHA,SAAA,EAIA,IAJA,CAIA,YAAA;AACA;AACA,UAAA,MAAA,CAAA,MAAA,CAAA,IAAA,GAAA,GAAA;AACA,qCAAA,MAAA,CAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,cAAA,MAAA,CAAA,WAAA,CAAA,GAAA;;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,OAAA;;AACA,cAAA,MAAA,CAAA,MAAA,CAAA,OAAA,GAAA,MAAA,CAAA,MAAA,CAAA,IAAA;AACA,cAAA,MAAA,CAAA,MAAA,CAAA,OAAA,GAAA,MAAA,CAAA,MAAA,CAAA,IAAA;AACA;AACA,WAPA;AAQA,SAfA;AAgBA,OAjBA,MAiBA;AACA;AACA,aAAA,MAAA,CAAA,IAAA,GAAA,GAAA;AACA,mCAAA,KAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,WAAA,CAAA,GAAA;;AACA,YAAA,MAAA,CAAA,MAAA,CAAA,OAAA,GAAA,MAAA,CAAA,MAAA,CAAA,IAAA;AACA,YAAA,MAAA,CAAA,MAAA,CAAA,OAAA,GAAA,MAAA,CAAA,MAAA,CAAA,IAAA;AACA;AACA,SANA;AAOA;AACA,KAvVA;AAwVA;AACA,IAAA,WAzVA,uBAyVA,iBAzVA,EAyVA;AAAA;;AACA,UAAA,iBAAA,IAAA,GAAA,EAAA;AACA,YAAA,OAAA,GAAA,KAAA,MAAA,CAAA,IAAA;AACA,6BAAA,KAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,OAAA,IAAA,CAAA,EAAA;AACA,gBAAA,IAAA,GAAA,EAAA;;AACA,iBAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,OAAA,EAAA,CAAA,EAAA,EAAA;AACA,kBAAA,IAAA,GAAA,MAAA;;AACA,mBAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,oBAAA,CAAA,IAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,QAAA,EAAA;AACA,sBAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,IAAA;AACA,sBAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,IAAA;AACA,sBAAA,KAAA,GAAA,EAAA;;AACA,sBAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,GAAA,EAAA;AAAA;AACA,oBAAA,KAAA,GAAA,mBAAA;AACA,mBAFA,MAEA,IAAA,IAAA,IAAA,GAAA,EAAA;AAAA;AACA,oBAAA,KAAA,GAAA,qBAAA;AACA,mBAFA,MAEA,IAAA,IAAA,IAAA,EAAA,IAAA,IAAA,IAAA,SAAA,IAAA,IAAA,IAAA,WAAA,EAAA;AAAA;AACA,oBAAA,KAAA,GAAA,qBAAA;AACA;;AACA,sBAAA,IAAA,IAAA,EAAA,IAAA,IAAA,IAAA,WAAA,IAAA,IAAA,IAAA,SAAA,EAAA;AACA,oBAAA,IAAA,GAAA,GAAA;AACA,mBAFA,MAEA,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,KAAA,CAAA,CAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,EAAA,CAEA,CAFA,MAEA,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,KAAA,CAAA,CAAA,EAAA;AACA,oBAAA,IAAA,GAAA,IAAA,CAAA,OAAA,CAAA,oBAAA,EAAA,EAAA,CAAA;AACA;;AACA,sBAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,OAAA,IAAA,GAAA,IAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,OAAA,IAAA,GAAA,EAAA;AACA,oBAAA,IAAA,IAAA,wBAAA,KAAA,GAAA,aAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,OAAA,GAAA,eAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,OAAA,GAAA,UAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,MAAA,GAAA,KAAA,GAAA,IAAA,GAAA,OAAA;AACA,mBAFA,MAEA;AACA,oBAAA,IAAA,IAAA,wBAAA,KAAA,GAAA,OAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,MAAA,GAAA,KAAA,GAAA,IAAA,GAAA,OAAA;AACA;AACA;AACA;;AACA,cAAA,IAAA,IAAA,OAAA;AACA,cAAA,IAAA,IAAA,IAAA;AACA;;AACA,YAAA,MAAA,CAAA,UAAA,GAAA,SAAA;AACA,YAAA,MAAA,CAAA,UAAA,GAAA,IAAA;AACA;AACA,SArCA;AAsCA,OAxCA,MAwCA;AACA,YAAA,OAAA,GAAA,KAAA,MAAA,CAAA,IAAA;AACA,6BAAA,KAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,OAAA,IAAA,CAAA,EAAA;AACA,gBAAA,IAAA,GAAA,EAAA;;AACA,iBAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,OAAA,EAAA,CAAA,EAAA,EAAA;AACA,kBAAA,IAAA,GAAA,MAAA;;AACA,mBAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,oBAAA,CAAA,IAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,QAAA,EAAA;AACA,sBAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,IAAA;AACA,sBAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,IAAA;AACA,sBAAA,KAAA,GAAA,EAAA;;AACA,sBAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,GAAA,EAAA;AAAA;AACA,oBAAA,KAAA,GAAA,mBAAA;AACA,mBAFA,MAEA,IAAA,IAAA,IAAA,GAAA,EAAA;AAAA;AACA,oBAAA,KAAA,GAAA,qBAAA;AACA,mBAFA,MAEA,IAAA,IAAA,IAAA,EAAA,IAAA,IAAA,IAAA,SAAA,IAAA,IAAA,IAAA,WAAA,EAAA;AAAA;AACA,oBAAA,KAAA,GAAA,qBAAA;AACA;;AACA,sBAAA,IAAA,IAAA,EAAA,IAAA,IAAA,IAAA,WAAA,IAAA,IAAA,IAAA,SAAA,EAAA;AACA,oBAAA,IAAA,GAAA,GAAA;AACA,mBAFA,MAEA,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,KAAA,CAAA,CAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,IAAA,IAAA,EAAA,CAEA,CAFA,MAEA,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,KAAA,CAAA,CAAA,EAAA;AACA,oBAAA,IAAA,GAAA,IAAA,CAAA,OAAA,CAAA,oBAAA,EAAA,EAAA,CAAA;AACA;;AACA,sBAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,OAAA,IAAA,GAAA,IAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,OAAA,IAAA,GAAA,EAAA;AACA,oBAAA,IAAA,IAAA,wBAAA,KAAA,GAAA,YAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,OAAA,GAAA,eAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,OAAA,GAAA,UAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,MAAA,GAAA,KAAA,GAAA,IAAA,GAAA,OAAA;AACA,mBAFA,MAEA;AACA,oBAAA,IAAA,IAAA,wBAAA,KAAA,GAAA,OAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,MAAA,GAAA,KAAA,GAAA,IAAA,GAAA,OAAA;AACA;AACA;AACA;;AACA,cAAA,IAAA,IAAA,OAAA;AACA,cAAA,IAAA,IAAA,IAAA;AACA;;AACA,YAAA,MAAA,CAAA,UAAA,GAAA,SAAA;AACA,YAAA,MAAA,CAAA,UAAA,GAAA,IAAA;AACA;AACA,SArCA;AAsCA;AACA,KA3aA;AA4aA,IAAA,MA5aA,kBA4aA,IA5aA,EA4aA,GA5aA,EA4aA;AACA,WAAA,MAAA,CAAA,IAAA,GAAA,IAAA;AAEA,KA/aA;AAgbA;AACA,IAAA,gBAjbA,4BAibA,GAjbA,EAibA;AAAA;;AACA,WAAA,OAAA,GAAA,GAAA;AACA,WAAA,OAAA,CAAA,MAAA,GAAA,KAAA,MAAA;AACA,UAAA,MAAA,GAAA,IAAA,CAAA,SAAA,CAAA;AACA,kBAAA,GAAA,CAAA,KADA;AAEA,gBAAA;AAFA,OAAA,CAAA;AAIA,2BAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,MAAA,GAAA,GAAA,CAAA,IAAA,CADA,CACA;;AACA,UAAA,MAAA,CAAA,YAAA,GAAA,IAAA;AACA;AACA,OALA;AAMA,KA9bA;AA+bA;AACA,IAAA,eAhcA,6BAgcA;AACA,WAAA,YAAA,GAAA,KAAA,CADA,CAEA;;AACA,WAAA,MAAA;AACA,KApcA;AAqcA,IAAA,QArcA,sBAqcA,CACA,CAtcA;AAucA,IAAA,KAvcA,mBAucA,CACA,CAxcA;AAycA,IAAA,kBAzcA,gCAycA,CACA,CA1cA;AA2cA,IAAA,mBA3cA,iCA2cA,CACA,CA5cA;AA6cA;AACA,IAAA,WA9cA,yBA8cA;AAAA;;AACA,WAAA,IAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,YAAA;AACA,QAAA,OAAA,CAAA,IAAA,GAAA,OAAA,CAAA,QAAA,CAAA,IAAA,GAAA,IAAA;;AACA,QAAA,OAAA,CAAA,SAAA,CAAA,MAAA;AACA,OAHA;AAIA;AApdA;AAlIA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row :gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card class=\"aside_height\" shadow=\"never\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备类型</span>\n          </div>\n          <div style=\" overflow: auto;height: 84vh\">\n            <el-tree\n              highlight-current\n              id=\"tree\"\n              :props=\"props\"\n              :load=\"loadNode\"\n              lazy\n              @node-click=\"handleNodeClick\"\n              :default-expanded-keys=\"['1']\"\n            />\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 180, itemWidth: 300 }\"\n          @handleReset=\"getReset\"\n          @handleEvent=\"handleEvent\"\n        />\n        <el-white>\n          <el-white class=\"button-group\">\n            <el-button class=\"mb8\" @click=\"addButton\" type=\"primary\" icon=\"el-icon-plus\">\n              新增\n            </el-button>\n            <!--<el-button class=\"mb8\" @click=\"deleteButton\" type=\"danger\" icon=\"el-icon-delete\">-->\n            <!--  删除-->\n            <!--</el-button>-->\n            <!--<el-button class=\"mb8\" v-show=\"false\" @click=\"viewButton\" type=\"primary\" icon=\"el-icon-circle-plus-outline\">-->\n            <!--  定义详情-->\n            <!--</el-button>-->\n          </el-white>\n          <comp-table :table-and-page-info=\"tableAndPageInfo\" @update:multipleSelection=\"handleSelectionChange\"  height=\"68.8vh\">\n            <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\"\n                             width=\"160\"\n                             :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button @click=\"updateDetails(scope.row)\" v-show=\"scope.row.createUser == currentUser\" type=\"text\"\n                           size=\"small\"  title=\"修改\"  class='el-icon-edit'\n                >\n                </el-button>\n                <el-button @click=\"getDetails(scope.row)\" type=\"text\" size=\"small\" title=\"详情\" class=\"el-icon-view\"></el-button>\n                <el-button @click=\"getNameplateInfo(scope.row)\" v-show=\"scope.row.createUser == currentUser\" type=\"text\"\n                           size=\"small\" title=\"定义详情\" class=\"el-icon-edit-outline\"\n                >\n                </el-button>\n                <el-button @click=\"deleteButton(scope.row)\" v-show=\"scope.row.createUser == currentUser\" type=\"text\"\n                           size=\"small\" title=\"删除\" class=\"el-icon-delete\"\n                >\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n    <!-- 新增、修改、详情界面 -->\n    <el-dialog :title=title :visible.sync=\"isShowDetails\" width=\"40%\" @close=\"handleClose\" v-dialogDrag>\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"铭牌名称：\" prop=\"mpmc\">\n              <el-input placeholder=\"铭牌名称\" v-model=\"form.mpmc\" :disabled=\"isDisabled\" style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"专业：\" prop=\"zy\">\n              <el-select placeholder=\"专业\" v-model=\"form.zy\" :disabled=\"isDisabled\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否分相：\" prop=\"sffx\">\n              <el-select placeholder=\"是否分相\" v-model=\"form.sffx\" :disabled=\"isDisabled\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in sffxOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button v-if=\"title==='新增' || title==='修改'\" type=\"primary\" @click=\"save\">确 认</el-button>\n      </div>\n    </el-dialog>\n    <!-- 定义详情界面 -->\n    <el-dialog title=\"定义铭牌详情\" :visible.sync=\"isShowDyDetails\" width=\"75%\" v-dialogDrag>\n      <el-form label-width=\"120px\" ref=\"dyForm\" :model=\"dyForm\">\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"6\">\n            <el-row>\n              <h4\n                style=\"margin-left: 15px;font-family: PingFangSC-Regular;font-size:14px;color:#333333;text-align:center;font-weight: bold;\"\n              >A表格</h4>\n            </el-row>\n            <el-row>\n              <el-form-item label=\"行:\" prop=\"a_hs\">\n                <el-col :span=\"20\">\n                  <el-input v-model=\"dyForm.a_hs\" style=\"width: 100%;\"></el-input>\n                </el-col>\n              </el-form-item>\n            </el-row>\n            <el-row>\n              <el-form-item label=\"列:\" prop=\"a_ls\">\n                <el-col :span=\"20\">\n                  <el-input v-model=\"dyForm.a_ls\" style=\"width: 100%;\"></el-input>\n                </el-col>\n              </el-form-item>\n            </el-row>\n            <el-row style=\"text-align:center;\">\n              <el-button type=\"primary\" @click=\"saveTableA\">创建A表格</el-button>\n            </el-row>\n            <hr style=\"height:1px;border:none;border-top:1px ridge #DBDBDB;width: 100%;\">\n            <el-row>\n              <h4\n                style=\"margin-left: 15px;font-family: PingFangSC-Regular;font-size:14px;color:#333333;text-align:center;font-weight: bold;\"\n              >B表格</h4>\n            </el-row>\n            <el-row>\n              <el-form-item label=\"行:\" prop=\"b_hs\">\n                <el-col :span=\"20\">\n                  <el-input v-model=\"dyForm.b_hs\" style=\"width: 100%;\"></el-input>\n                </el-col>\n              </el-form-item>\n            </el-row>\n            <el-row>\n              <el-form-item label=\"列:\" prop=\"b_ls\">\n                <el-col :span=\"20\">\n                  <el-input v-model=\"dyForm.b_ls\" style=\"width: 100%;\"></el-input>\n                </el-col>\n              </el-form-item>\n            </el-row>\n            <el-row style=\"text-align:center;\">\n              <el-button type=\"primary\" @click=\"saveTableB\">创建B表格</el-button>\n            </el-row>\n            <hr style=\"height:1px;border:none;border-top:1px ridge #DBDBDB;width: 100%;\">\n            <el-row>\n              <h4\n                style=\"margin-left: 15px;font-family: PingFangSC-Regular;font-size:14px;color:#333333;text-align:center;font-weight: bold;\"\n              >单元格操作</h4>\n            </el-row>\n            <el-row>\n              <el-form-item label=\"行跨度:\" prop=\"rowSpanNum\">\n                <el-col :span=\"20\">\n                  <el-input v-model=\"dyForm.rowSpanNum\" style=\"width: 100%;\"></el-input>\n                </el-col>\n              </el-form-item>\n            </el-row>\n            <el-row>\n              <el-form-item label=\"列跨度:\" prop=\"colSpanNum\">\n                <el-col :span=\"20\">\n                  <el-input v-model=\"dyForm.colSpanNum\" style=\"width: 100%;\"></el-input>\n                </el-col>\n              </el-form-item>\n            </el-row>\n            <el-row style=\"text-align:center;\">\n              <el-button type=\"primary\" @click=\"saveSpan\">保存</el-button>\n              <el-button type=\"danger\" @click=\"reset\">清除</el-button>\n            </el-row>\n            <hr style=\"height:1px;border:none;border-top:1px ridge #DBDBDB;width: 100%;\">\n            <el-row style=\"text-align:center;\">\n              <el-button @click=\"editCellProperties\">编辑单元格属性</el-button>\n              <el-button @click=\"resetCellProperties\">重置单元格内容</el-button>\n            </el-row>\n            <hr style=\"height:1px;border:none;border-top:1px ridge #DBDBDB;width: 100%;\">\n            <el-row style=\"text-align:center;\">\n              <p style=\"color: red;\">灰色单元格:静态文本</p>\n              <p style=\"color: red;\">白色单元格:动态属性</p>\n              <p style=\"color: red;\">浅灰色单元格:只读的动态属性</p>\n              <p style=\"color: red;\">浅绿色单元格:还未定义属性</p>\n              <p style=\"color: red;\">浅黄色单元格:当前选中的单元格</p>\n            </el-row>\n          </el-col>\n          <el-col :span=\"18\">\n            <el-row>\n              <h4\n                style=\"margin-left: 15px;font-family: PingFangSC-Regular;font-size:14px;color:#333333;text-align:left;font-weight: bold;\"\n              >A表格</h4>\n            </el-row>\n            <el-row>\n              <h3 v-html=\"dyForm.title\"\n                  style=\"font-weight: bold;margin: 0px 0px 0px 0px;height: 45px;background-color:#d5ddfd;line-height:2.1;font-size: 21px;text-align:center;\"\n              ></h3>\n            </el-row>\n            <el-row>\n              <el-col :span=\"24\">\n                <table v-html=\"tableDataA\" border=\"1\" style=\"width: 100%;\" @cell-click=\"handle\">\n                </table>\n              </el-col>\n            </el-row>\n            <hr style=\"height:1px;border:none;border-top:1px ridge #DBDBDB;width: 100%;\">\n            <el-row>\n              <h4\n                style=\"margin-left: 15px;font-family: PingFangSC-Regular;font-size:14px;color:#333333;text-align:left;font-weight: bold;\"\n              >B表格</h4>\n            </el-row>\n            <el-row>\n              <el-col :span=\"24\">\n                <table v-html=\"tableDataB\" border=\"1\" style=\"width: 100%;\">\n                </table>\n              </el-col>\n            </el-row>\n          </el-col>\n        </el-row>\n      </el-form>\n    </el-dialog>\n\n    <el-dialog\n      :visible.sync=\"isShowMpInfo\"\n      v-if=\"isShowMpInfo\"\n      v-dialogDrag\n      width=\"68%\"\n      title=\"铭牌内容\"\n      :append-to-body=\"true\"\n      @close=\"closeInfoDialog\"\n    >\n      <!--        <symp-info\n                :mp-data=\"rowData\"\n                @closeInfoDialog=\"closeInfoDialog\"></symp-info>-->\n      <mpxq-info :mp-data=\"rowData\" :mx-data.sync=\"mxData\"\n                 @closeInfoDialog=\"closeInfoDialog\"\n      />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n\nimport {\n  getDeviceClassTreeNodeByPid,\n  getPageDataList,\n  getTable,\n  remove,\n  saveOrUpdate,\n  updateTableNum\n} from '@/api/dagangOilfield/bzgl/sympk/sympk'\nimport SympInfo from '@/views/dagangOilfield/bzgl/sympk/sympInfo'\nimport MpxqInfo from '@/views/dagangOilfield/bzgl/sympk/mpxqInfo'\n\nexport default {\n  name: 'sympk',\n  components: { MpxqInfo, SympInfo },\n  inject: ['reload'],//inject注入根组件的reload方法\n  data() {\n    return {\n      params: {},\n      //筛选框\n      filterInfo: {\n        data: {\n          mpmc: '',\n          zy: '',\n          sffx: ''\n        },\n        fieldList: [\n          {\n            label: '铭牌名称',\n            value: 'mpmc',\n            type: 'input',\n            clearable: true\n          },\n          {\n            label: '是否分相',\n            value: 'sffx',\n            type: 'select',\n            options: [\n              {label: '是', value: '1'},\n              {label: '否', value: '0'}\n            ],\n            clearable: true\n          }\n        ]\n      },\n      currentUser: this.$store.getters.name,\n      ids: [],\n      props: {\n        label: 'name',\n        children: 'zones',\n        isLeaf: 'leaf'\n      },\n      //专业下拉框数据\n      options: [{ label: '输电', value: 'SD' }, { label: '变电', value: 'BD' }],\n      //是否分相下拉框数据\n      sffxOptions: [{ label: '是', value: '1' }, { label: '否', value: '0' }],\n      form: {\n        objId: undefined,\n        sblxbm: undefined,\n        mpmc: undefined,\n        zy: undefined,\n        sffx: '',\n        isMpSyxm: 0\n      },\n      dyForm: {\n        obj_id: undefined,\n        a_hs: 0,\n        a_hsOld: 0,\n        a_ls: 0,\n        a_lsOld: 0,\n        b_hs: 0,\n        b_hsOld: 0,\n        b_ls: 0,\n        b_lsOld: 0,\n        rowSpanNum: 1,\n        colSpanNum: 1,\n        lbbs: undefined,\n        title: undefined\n      },\n      isShowDetails: false,\n      isShowDyDetails: false,\n      title: '',\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: '铭牌名称', prop: 'mpmc', minWidth: '180' },\n          { label: '专业', prop: 'zyName', minWidth: '200' },\n          { label: '是否分相', prop: 'sffxName', minWidth: '200' }\n          // {\n          //   prop: 'operation',\n          //   label: '操作',\n          //   minWidth: '100px',\n          //   style: { display: 'block' },\n          //   //操作列固定再右侧\n          //   fixed: 'right',\n          //   operation: [\n          //     { name: '修改', clickFun: this.updateDetails },\n          //     { name: '详情', clickFun: this.getDetails },\n          //     { name: '定义详情', clickFun: this.getNameplateInfo }\n          //   ]\n          // }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      //删除是否可用\n      multipleSensor: true,\n      //查询参数\n      queryParams: {\n        zy: '',\n        sblxbm: undefined,\n        pageNum: 1,\n        pageSize: 10,\n        isMpSyxm: 0\n      },\n      isDisabled: false,\n      rules: {\n        mpmc: [{ required: true, message: '请填写铭牌名称', trigger: 'blur' }],\n        zy: [{ required: true, message: '请选择专业', trigger: 'blur' }],\n        sffx: [{ required: true, message: '请选择是否分相', trigger: 'change' }]\n      },\n      selection: [], //记录最后一次选中的行数据\n      tableDataA: undefined,  //表格A数据\n      tableDataB: undefined,  //表格B数据\n      isShowMpInfo: false,\n      //选中行数据\n      rowData: {},\n      //设备类型编码\n      sblxbm: '',\n      mxData: []//表格明细数据\n    }\n  },\n  watch: {},\n  created() {\n    this.getList()\n\n  },\n  methods: {\n    getData(param){\n      this.getList(param)\n    },\n    handleEvent(var1, var2){\n      this.params = var2\n    },\n    //定义重置方法\n    getReset() {\n      this.params = {}\n      this.getList()\n    },\n    //树节点点击事件\n    handleNodeClick(data) {\n      this.form.sblxbm = data.code\n      this.sblxbm = data.code\n      this.queryParams.sblxbm = data.code\n      if (data.pid != 'sb'){\n        this.queryParams.zy = data.pid.substring(0, 2)\n      }\n      this.getList()\n    },\n    /**\n     * 表格多选框\n     */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.objId)\n      this.selection = selection\n    },\n    //添加按钮\n    addButton() {\n      if (this.form.sblxbm != undefined) {\n        this.isShowDetails = true\n        this.isDisabled = false\n        this.form.objId = undefined\n        this.form.mpmc = undefined\n        this.form.zy = undefined\n        this.title = '新增'\n      } else {\n        this.$message.warning('请选择左侧树节点新增数据！')\n        return\n      }\n    },\n    close() {\n      this.isShowDetails = false\n    },\n    updateDetails(row) {\n      this.title = '修改'\n      this.isShowDetails = true\n      this.form = row\n      this.isDisabled = false\n    },\n    getDetails(row) {\n      this.title = '详情'\n      this.isShowDetails = true\n      this.form = row\n      this.isDisabled = true\n    },\n    save() {\n      this.$refs.form.validate((valid) => {\n        if (valid) {\n          saveOrUpdate(this.form).then(res => {\n            if (res.code == '0000') {\n              this.$message.success('保存成功！')\n              this.isShowDetails = false\n              this.getList()\n            }\n          })\n        } else {\n          this.$message.error('请输入所有必填字段！')\n          return false\n        }\n      })\n    },\n    //删除按钮\n    deleteButton(row) {\n      this.form = row\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        remove([this.form.objId]).then(res => {\n          this.$message({\n            type: 'success',\n            message: '删除成功!'\n          })\n          this.getList()\n        })\n      })\n    },\n    //定义详情按钮\n    viewButton() {\n      if (this.ids.length == 1) {\n        this.isShowDyDetails = true\n        this.dyForm.obj_id = this.ids[0]\n        this.dyForm.a_hs = this.selection[0].a_hs\n        this.dyForm.a_hsOld = this.selection[0].a_hs\n        this.dyForm.a_ls = this.selection[0].a_ls\n        this.dyForm.a_lsOld = this.selection[0].a_ls\n        this.dyForm.b_hs = this.selection[0].b_hs\n        this.dyForm.b_hsOld = this.selection[0].b_hs\n        this.dyForm.b_ls = this.selection[0].b_ls\n        this.dyForm.b_lsOld = this.selection[0].b_ls\n        this.dyForm.title = this.selection[0].mpmc\n        //A表格加载\n        if (this.selection[0].a_hs != '' && this.selection[0].a_ls != '' && this.selection[0].a_hs != 0 && this.selection[0].a_ls != 0) {\n          this.dyForm.lbbs = 'A'\n          getTable(this.dyForm).then(res => {\n            var str = ''\n            for (var i = 0; i < this.selection[0].a_hs; i++) {\n              var temp = '<tr>'\n              for (var k = 0; k < res.data.length; k++) {\n                if (i == res.data[k].rowindex) {\n                  var nrbs = res.data[k].nrbs\n                  var nrlx = res.data[k].nrlx\n                  //定义颜色\n                  var color = ''\n                  if (nrlx == '1' || nrlx == '2' || nrlx == '3' || nrlx == '4') {//白色单元格:动态属性\n                    color = ' bgcolor=\"white\" '\n                  } else if (nrlx == '5') {//灰色单元格:静态文本\n                    color = ' bgcolor=\"#EEEEE0\" '\n                  } else if (nrlx == '' || nrlx == undefined || nrlx == 'undefined') {//浅绿色单元格:还未定义属性\n                    color = ' bgcolor=\"#C7EDCC\" '\n                  }\n                  if (nrbs == '' || nrbs == 'undefined' || nrbs == undefined) {\n                    nrbs = '-'\n                  } else if (nrbs.indexOf('|') == -1 || nrbs == '℃' || nrbs == '/%') {\n\n                  } else if (nrbs.indexOf('|') != -1) {\n                    nrbs = nrbs.replace(/[^\\u4e00-\\u9fa5]/gi, '')\n                  }\n\n                  if (res.data[k].colspan != '1' || res.data[k].rowspan != '1') {\n                    temp += '<td tabindex=\\'-1\\'' + color + 'colspan=\\'' + res.data[k].colspan + '\\' rowspan=\\'' + res.data[k].rowspan + '\\' id=\\'' + res.data[k].obj_id + '\\'>' + nrbs + '</td>'\n                  } else {\n                    temp += '<td tabindex=\\'-1\\'' + color + ' id=\\'' + res.data[k].obj_id + '\\' @click=\\'tdclick(\\'A\\',this)\\'>' + nrbs + '</td>'\n                  }\n                }\n              }\n              temp += '</tr>'\n              str += temp\n            }\n            this.tableDataA = undefined\n            this.tableDataA = str\n          })\n        } else {\n          this.tableDataA = undefined\n        }\n        //B表格加载\n        if (this.selection[0].b_hs != '' && this.selection[0].b_ls != '' && this.selection[0].b_hs != 0 && this.selection[0].b_ls != 0) {\n          this.dyForm.lbbs = 'B'\n          getTable(this.dyForm).then(res => {\n            var str = ''\n            for (var i = 0; i < this.selection[0].b_hs; i++) {\n              var temp = '<tr>'\n              for (var k = 0; k < res.data.length; k++) {\n                if (i == res.data[k].rowindex) {\n                  var nrbs = res.data[k].nrbs\n                  var nrlx = res.data[k].nrlx\n                  //定义颜色\n                  var color = ''\n                  if (nrlx == '1' || nrlx == '2' || nrlx == '3' || nrlx == '4') {//白色单元格:动态属性\n                    color = ' bgcolor=\"white\" '\n                  } else if (nrlx == '5') {//灰色单元格:静态文本\n                    color = ' bgcolor=\"#EEEEE0\" '\n                  } else if (nrlx == '' || nrlx == undefined || nrlx == 'undefined') {//浅绿色单元格:还未定义属性\n                    color = ' bgcolor=\"#C7EDCC\" '\n                  }\n                  if (nrbs == '' || nrbs == 'undefined' || nrbs == undefined) {\n                    nrbs = '-'\n                  } else if (nrbs.indexOf('|') == -1 || nrbs == '℃' || nrbs == '/%') {\n\n                  } else if (nrbs.indexOf('|') != -1) {\n                    nrbs = nrbs.replace(/[^\\u4e00-\\u9fa5]/gi, '')\n                  }\n\n                  if (res.data[k].colspan != '1' || res.data[k].rowspan != '1') {\n                    temp += '<td tabindex=\\'-1\\'' + color + 'colspan=\\'' + res.data[k].colspan + '\\' rowspan=\\'' + res.data[k].rowspan + '\\' id=\\'' + res.data[k].obj_id + '\\'>' + nrbs + '</td>'\n                  } else {\n                    temp += '<td tabindex=\\'-1\\'' + color + ' id=\\'' + res.data[k].obj_id + '\\'>' + nrbs + '</td>'\n                  }\n                }\n              }\n              temp += '</tr>'\n              str += temp\n            }\n            this.tableDataB = undefined\n            this.tableDataB = str\n          })\n        } else {\n          this.tableDataB = undefined\n        }\n      } else {\n        this.$message({\n          type: 'info',\n          message: '请选择一条数据!'\n        })\n      }\n    },\n    //查询列表\n    getList(params) {\n      this.queryParams = {...this.queryParams, ...params};\n      let param = {...this.queryParams,...params}\n      getPageDataList(param).then(res => {\n        this.tableAndPageInfo.tableData = res.data.records\n        this.tableAndPageInfo.pager.total = res.data.total\n\n        this.tableAndPageInfo.tableData.forEach(item => {\n          this.options.forEach(element => {\n            if (item.zy === element.value) {\n              item.zyName = element.label\n            }\n          })\n          this.sffxOptions.forEach(element => {\n            if (item.sffx === element.value) {\n              item.sffxName = element.label\n            }\n          })\n        })\n      })\n    },\n    //懒加载函数\n    loadNode(node, resolve) {\n      let TreeparamMap = {\n        pid: '',\n        spbLogo: ['输电设备', '变电设备','配电设备']\n      }\n      if (node.level === 0) {\n        TreeparamMap.pid = 'sb'\n        return this.getTreeNode(TreeparamMap, resolve)\n      }\n      setTimeout(() => {\n        TreeparamMap.pid = node.data.code\n        this.getTreeNode(TreeparamMap, resolve)\n      }, 500)\n\n    },\n    //获取树节点数据\n    getTreeNode(paramMap, resolve) {\n      getDeviceClassTreeNodeByPid(paramMap).then(res => {\n        let treeNodes = []\n        res.data.forEach(item => {\n          let node = {\n            name: item.name,\n            level: item.level,\n            id: item.id,\n            pid: item.pid,\n            leaf: false,\n            code: item.code\n          }\n          treeNodes.push(node)\n        })\n        resolve(treeNodes)\n      })\n    },\n    //创建A表格按钮功能\n    saveTableA() {\n      //判断填写的行数与列数，值是否符合标准\n      if (this.dyForm.a_hs <= 0 || this.dyForm.a_ls <= 0) {\n        this.$message({\n          type: 'info',\n          message: '不允许填写0或小于0的数!'\n        })\n        return\n      }\n      //判断行和列的变化情况\n      let row_differ = this.dyForm.a_hsOld - this.dyForm.a_hs  //对前后变化的行做差，判断是否增加或减少行\n      let col_differ = this.dyForm.a_lsOld - this.dyForm.a_ls  //对前后变化的行做差，判断是否增加或减少列\n      if (row_differ == 0 && col_differ == 0) {//行列无发生变化，不进行任何操作\n        return\n      } else if (row_differ > 0 || col_differ > 0) {//行或者列减少,提示用户是否删除\n        this.$confirm('确定删除行列?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          //设置类别标识\n          this.dyForm.lbbs = 'A'\n          updateTableNum(this.dyForm).then(res => {\n            if (res.code == '0000') {\n              this.reloadTable('A')\n              this.$message.success('删除成功！')\n              this.dyForm.a_hsOld = this.dyForm.a_hs\n              this.dyForm.a_lsOld = this.dyForm.a_ls\n            }\n          })\n        })\n      } else {\n        //设置类别标识\n        this.dyForm.lbbs = 'A'\n        updateTableNum(this.dyForm).then(res => {\n          if (res.code == '0000') {\n            this.reloadTable('A')\n            this.dyForm.a_hsOld = this.dyForm.a_hs\n            this.dyForm.a_lsOld = this.dyForm.a_ls\n          }\n        })\n      }\n    },\n    //创建B表格按钮功能\n    saveTableB() {\n      //判断填写的行数与列数，值是否符合标准\n      if (this.dyForm.b_hs <= 0 || this.dyForm.b_ls <= 0) {\n        this.$message({\n          type: 'info',\n          message: '不允许填写0或小于0的数!'\n        })\n        return\n      }\n      //判断行和列的变化情况\n      let row_differ = this.dyForm.b_hsOld - this.dyForm.b_hs  //对前后变化的行做差，判断是否增加或减少行\n      let col_differ = this.dyForm.b_lsOld - this.dyForm.b_ls  //对前后变化的行做差，判断是否增加或减少列\n      if (row_differ == 0 && col_differ == 0) {//行列无发生变化，不进行任何操作\n        return\n      } else if (row_differ > 0 || col_differ > 0) {//行或者列减少,提示用户是否删除\n        this.$confirm('确定删除行列?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          //设置类别标识\n          this.dyForm.lbbs = 'B'\n          updateTableNum(this.dyForm).then(res => {\n            if (res.code == '0000') {\n              this.reloadTable('B')\n              this.$message.success('删除成功！')\n              this.dyForm.b_hsOld = this.dyForm.b_hs\n              this.dyForm.b_lsOld = this.dyForm.b_ls\n            }\n          })\n        })\n      } else {\n        //设置类别标识\n        this.dyForm.lbbs = 'B'\n        updateTableNum(this.dyForm).then(res => {\n          if (res.code == '0000') {\n            this.reloadTable('B')\n            this.dyForm.b_hsOld = this.dyForm.b_hs\n            this.dyForm.b_lsOld = this.dyForm.b_ls\n          }\n        })\n      }\n    },\n    //重新加载表格\n    reloadTable(reload_table_flag) {\n      if (reload_table_flag == 'B') {\n        var rowNumB = this.dyForm.b_hs\n        getTable(this.dyForm).then(res => {\n          if (rowNumB != 0) {\n            var str1 = ''\n            for (var i = 0; i < rowNumB; i++) {\n              var temp = '<tr>'\n              for (var k = 0; k < res.data.length; k++) {\n                if (i == res.data[k].rowindex) {\n                  var nrbs = res.data[k].nrbs\n                  var nrlx = res.data[k].nrlx\n                  var color = ''\n                  if (nrlx == '1' || nrlx == '2' || nrlx == '3' || nrlx == '4') {//白色单元格:动态属性\n                    color = ' bgcolor=\"white\" '\n                  } else if (nrlx == '5') {//灰色单元格:静态文本\n                    color = ' bgcolor=\"#EEEEE0\" '\n                  } else if (nrlx == '' || nrlx == undefined || nrlx == 'undefined') {//浅绿色单元格:还未定义属性\n                    color = ' bgcolor=\"#C7EDCC\" '\n                  }\n                  if (nrbs == '' || nrbs == 'undefined' || nrbs == undefined) {\n                    nrbs = '-'\n                  } else if (nrbs.indexOf('|') == -1 || nrbs == '℃' || nrbs == '/%') {\n\n                  } else if (nrbs.indexOf('|') != -1) {\n                    nrbs = nrbs.replace(/[^\\u4e00-\\u9fa5]/gi, '')\n                  }\n                  if (res.data[k].colspan != '1' || res.data[k].rowspan != '1') {\n                    temp += '<td tabindex=\\'-1\\'' + color + ' colspan=\\'' + res.data[k].colspan + '\\' rowspan=\\'' + res.data[k].rowspan + '\\' id=\\'' + res.data[k].obj_id + '\\'>' + nrbs + '</td>'\n                  } else {\n                    temp += '<td tabindex=\\'-1\\'' + color + 'id=\\'' + res.data[k].obj_id + '\\'>' + nrbs + '</td>'\n                  }\n                }\n              }\n              temp += '</tr>'\n              str1 += temp\n            }\n            this.tableDataB = undefined\n            this.tableDataB = str1\n          }\n        })\n      } else {\n        var rowNumA = this.dyForm.a_hs\n        getTable(this.dyForm).then(res => {\n          if (rowNumA != 0) {\n            var str1 = ''\n            for (var i = 0; i < rowNumA; i++) {\n              var temp = '<tr>'\n              for (var k = 0; k < res.data.length; k++) {\n                if (i == res.data[k].rowindex) {\n                  var nrbs = res.data[k].nrbs\n                  var nrlx = res.data[k].nrlx\n                  var color = ''\n                  if (nrlx == '1' || nrlx == '2' || nrlx == '3' || nrlx == '4') {//白色单元格:动态属性\n                    color = ' bgcolor=\"white\" '\n                  } else if (nrlx == '5') {//灰色单元格:静态文本\n                    color = ' bgcolor=\"#EEEEE0\" '\n                  } else if (nrlx == '' || nrlx == undefined || nrlx == 'undefined') {//浅绿色单元格:还未定义属性\n                    color = ' bgcolor=\"#C7EDCC\" '\n                  }\n                  if (nrbs == '' || nrbs == 'undefined' || nrbs == undefined) {\n                    nrbs = '-'\n                  } else if (nrbs.indexOf('|') == -1 || nrbs == '℃' || nrbs == '/%') {\n\n                  } else if (nrbs.indexOf('|') != -1) {\n                    nrbs = nrbs.replace(/[^\\u4e00-\\u9fa5]/gi, '')\n                  }\n                  if (res.data[k].colspan != '1' || res.data[k].rowspan != '1') {\n                    temp += '<td tabindex=\\'-1\\'' + color + 'colspan=\\'' + res.data[k].colspan + '\\' rowspan=\\'' + res.data[k].rowspan + '\\' id=\\'' + res.data[k].obj_id + '\\'>' + nrbs + '</td>'\n                  } else {\n                    temp += '<td tabindex=\\'-1\\'' + color + 'id=\\'' + res.data[k].obj_id + '\\'>' + nrbs + '</td>'\n                  }\n                }\n              }\n              temp += '</tr>'\n              str1 += temp\n            }\n            this.tableDataA = undefined\n            this.tableDataA = str1\n          }\n        })\n      }\n    },\n    handle(sign, obj) {\n      this.dyForm.lbbs = sign\n\n    },\n    //获取铭牌信息\n    getNameplateInfo(row) {\n      this.rowData = row\n      this.rowData.sblxbm = this.sblxbm\n      let params = JSON.stringify({\n        'obj_id': row.objId,\n        'lbbs': 'A'\n      })\n      getTable(params).then(res => {\n        if (res.code === '0000') {\n          this.mxData = res.data//需要先设置数据再弹框，否则数据传不过去\n          this.isShowMpInfo = true\n        }\n      })\n    },\n    //关闭铭牌内容弹框\n    closeInfoDialog() {\n      this.isShowMpInfo = false\n      //刷新父页面\n      this.reload()\n    },\n    saveSpan() {\n    },\n    reset() {\n    },\n    editCellProperties() {\n    },\n    resetCellProperties() {\n    },\n    //清空表单数据\n    handleClose() {\n      this.form = {}\n      this.$nextTick(() => {\n        this.form = this.$options.data().form\n        this.resetForm('form')\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  max-height: 79vh;\n  overflow: auto;\n}\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sympk"}]}