{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\ztlxxd.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\ztlxxd.vue", "mtime": 1706897323231}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ztlxxd.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ztlxxd.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk", "sourcesContent": ["<template>\n  <div>\n    <el-white class=\"button-group\">\n      <el-button\n        type=\"primary\"\n        icon=\"el-icon-plus\"\n        @click=\"addZtlxxd\"\n        >新增</el-button\n      >\n      <el-button\n        type=\"danger\"\n        icon=\"el-icon-delete\"\n        @click=\"deleteZtlxxd\"\n        >删除</el-button\n      >\n    </el-white>\n    <el-table\n      stripe\n      border\n      v-loading=\"loading\"\n      :data=\"tableData\"\n      @selection-change=\"handleSelectionChange\"\n    >\n      <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n      <el-table-column label=\"所属状态量\" prop=\"ztlmc\" />\n      <el-table-column label=\"信息点名称\" prop=\"xxdmc\" />\n      <el-table-column label=\"信息点单位\" prop=\"xxddw\" />\n      <el-table-column\n        label=\"操作\"\n        fixed=\"right\"\n        align=\"center\"\n        class-name=\"small-padding fixed-width\"\n        width=\"180\"\n      >\n       <template slot-scope=\"scope\">\n        <el-button\n          type=\"text\"\n          @click=\"updateDeviceClassify(scope.row)\"\n          class=\"updateBtn el-icon-edit\"\n          title=\"修改\"\n          ></el-button\n        >\n        <el-button type=\"text\" @click=\"getDeviceClassifyDetails(scope.row)\" title=\"详情\" class=\"el-icon-view\"\n          ></el-button\n        >\n        </template>\n      </el-table-column>\n    </el-table>\n    <pagination\n      v-show=\"queryParams.total > 0\"\n      :total=\"queryParams.total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getData\"\n    />\n    <dialog-form\n      ref=\"dialogForm\"\n      :append-to-body=\"true\"\n      label-width=\"150px\"\n      out-width=\"50%\"\n      :reminder=\"reminder\"\n      :rows=\"rows\"\n      @save=\"saveTechnicalParameter\"\n    />\n  </div>\n</template>\n\n\n<script>\nimport {\n  getPageZtlxxd,\n  saveOrUpdate,\n  remove,\n} from \"@/api/dagangOilfield/bzgl/sbztpjbzk/ztlxxd\";\nimport DialogForm from \"com/dialogFrom/dialogForm\";\n\nexport default {\n  name: \"ztlxxd\",\n  components: { DialogForm },\n  props: {\n    mpData: {\n      type: Object,\n    },\n  },\n\n  data() {\n    return {\n      //新增或修改标题\n      reminder: \"新增\",\n      rows: 2,\n      //表单数据\n      tableData: [],\n      //查询条件\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        total: 0,\n        ssztlId: \"\",\n      },\n      loading: false,\n      formList: [\n         {\n          label: 'objId',\n          value: '',\n          type: 'input',\n          name: 'objId',\n          hidden: false,\n        },\n        {\n          label: \"所属状态量：\",\n          value: \"\",\n          type: \"disabled\",\n          name: \"ztlmc\",\n          default: true,\n          rules: { required: true, message: \"请选择所属状态量\" },\n        },\n        {\n          label: \"信息点名称：\",\n          value: \"\",\n          name: \"xxdmc\",\n          default: true,\n          type: 'input',\n          rules: { required: true, message: \"请输入信息点名称\" },\n        },\n      {\n          label: 'ssztlId：',\n          value: '',\n          type: 'input',\n          name: 'ssztlId',\n          hidden: false,\n        },\n        /*{\n          label: \"信息点来源：\",\n          value: \"\",\n          name: \"xxdly\",\n          default: true,\n          type: 'input',\n        },*/\n/*        {\n          label: \"信息点类型：\",\n          value: \"\",\n          name: \"xxdlx\",\n          default: true,\n          type: 'selectChange1',\n           options: [{ label: '巡检数据', value: '巡检数据' }, { label: '试验数据', value: '试验数据' }],\n        },*/\n        {\n          label: \"信息点单位：\",\n          value: \"\",\n          name: \"xxddw\",\n          default: true,\n          type: 'input',\n        },\n\n     ],\n      //选中行数据\n      selectedRowDatas: [],\n    };\n  },\n  create() {},\n  mounted() {\n    this.getData();\n  },\n\n  methods: {\n     getData() {\n      this.loading = true;\n        this.queryParams.ssztlId=this.mpData.objId;\n       getPageZtlxxd(this.queryParams).then(res => {\n        this.tableData = res.data.records\n        this.queryParams.total = res.data.total\n        this.loading = false\n      })\n    },\n\n\n\n    //新增状态量信息点\n    addZtlxxd() {\n      this.reminder = '新增'\n      //初始化formList数据\n      this.formList.ssztlId=this.mpData.objId;\n      this.formList = this.$options.data().formList;\n      const addForm = this.formList.map(item => {\n        if(item.name === 'ztlmc'){\n          item.value=this.mpData.ztlmc\n        }\n        if(item.name === 'ssztlId'){\n            item.value=this.mpData.objId\n        }\n        return item\n      })\n      this.$refs.dialogForm.showzzc(addForm)\n    },\n\n\n    //删除\n    deleteZtlxxd() {\n      this.$confirm(\"确认删除选中数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      })\n        .then(() => {\n          let ids = [];\n          this.selectedRowDatas.forEach((item) => {\n            ids.push(item.objId);\n          });\n          remove(ids).then((res) => {\n            if (res.code === \"0000\") {\n              this.$message.success(\"删除成功\");\n              this.getData();\n            } else {\n              this.$message.error(\"操作失败\");\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\",\n          });\n        });\n    },\n\n    //修改\n    updateDeviceClassify(row) {\n      const updateList = this.formList.map((item) => {\n        item.value = row[item.name];\n        return item;\n      });\n      this.reminder = \"修改\";\n      this.$refs.dialogForm.showzzc(updateList);\n    },\n    //详情\n    getDeviceClassifyDetails(row) {\n      const infoList = this.formList.map((item) => {\n        item.value = row[item.name];\n        return item;\n      });\n      this.reminder = \"详情\";\n      this.$refs.dialogForm.showxq(infoList);\n    },\n\n    //保存\n    saveTechnicalParameter(formData) {\n      let message = \"\";\n      if (formData.objId === \"\" || !formData.objId) {\n        message = \"新增成功\";\n      } else {\n        message = \"修改成功\";\n      }\n      saveOrUpdate(formData).then((res) => {\n        if (res.code === \"0000\") {\n          this.$message.success(message);\n          this.getData();\n        } else {\n          this.$message.error(\"操作失败\");\n        }\n      });\n    },\n    //行选择事件\n    handleSelectionChange(row) {\n      this.selectedRowDatas = row;\n    },\n  },\n};\n</script>\n<style scoped>\n.pagination-container {\n  position: relative;\n  height: 25px;\n  margin-bottom: 20px !important;\n  margin-top: 15px;\n  padding: 10px 20px !important;\n}\n"]}]}