{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\todoitem\\todo.vue?vue&type=template&id=11e081c6&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\todoitem\\todo.vue", "mtime": 1706897322087}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}