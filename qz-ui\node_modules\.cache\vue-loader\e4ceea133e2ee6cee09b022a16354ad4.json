{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\sybgjlcomment\\sysbdd.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\sybgjlcomment\\sysbdd.vue", "mtime": 1706897323437}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["sysbdd.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "sysbdd.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk/sybgjlcomment", "sourcesContent": ["<template>\n  <div>\n    <el-row>\n      <el-col :span=\"24\">\n        <el-form label-width=\"80px\">\n          <el-form-item label=\"快速查询:\">\n            <el-input\n              placeholder=\"输入关键字进行过滤\"\n              v-model=\"filterText\"\n              clearable/>\n          </el-form-item>\n        </el-form>\n      </el-col>\n      <el-col :span=\"24\">\n        <div style=\"overflow: auto;height: 40vh\">\n          <el-col>\n            <el-tree\n              :data=\"treeData\"\n              show-checkbox\n              disabled=\"\"\n              node-key=\"id\"\n              :expand-on-click-node=\"true\"\n              :highlight-current=\"true\"\n              ref=\"treeRef\"\n              :props=\"defaultProps\"\n              :filter-node-method=\"filterNode\"/>\n          </el-col>\n        </div>\n      </el-col>\n    </el-row>\n\n    <div style=\"text-align: right\">\n      <el-button @click=\"close\">取 消</el-button>\n      <el-button type=\"primary\" @click=\"save\">确 定</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\n  import {getSysbddTreeOptionsData} from \"@/api/dagangOilfield/bzgl/sybglr\";\n\n\n  export default {\n    name: 'sysbdd',\n    props: {\n      //模板数据\n      mainData: {\n        type: Object,\n        default: () => ({\n          lx:'',  \n        })\n      },\n\n    },\n    data() {\n      return {\n        //树结构监听属性\n        filterText: \"\",\n        //树结构数据\n        treeData: [],\n        defaultProps: {\n          children: 'children',\n          label: 'label'\n        },\n        sydd: \"\",\n\n      }\n    },\n    mounted() {\n      //获取树结构数据\n      this.getSysbddTreeOptionsData();\n    },\n    watch: {\n      //监听筛选框值发生变化进而筛选树结构\n      filterText(val) {\n        this.$refs.treeRef.filter(val);\n      }\n    },\n    methods: {\n      //获取树结构数据\n      getSysbddTreeOptionsData() {\n        let param = {};\n        param.lx=this.mainData.lx;\n        console.log(\"param\",param);\n        getSysbddTreeOptionsData(param).then(res => {\n          this.treeData = res.data;\n        })\n      },\n\n      //树监听事件\n      filterNode(value, data) {\n        if (!value) return true;\n        return data.label.indexOf(value) !== -1;\n      },\n      //确定按钮\n      save() {\n        //获取选中\n        let nodeDataList = this.$refs.treeRef.getCheckedNodes(true);\n        if (nodeDataList.length > 1) {\n          this.$message.warning(\"只能选择一个设备地点\");\n          //清空选中\n          this.$refs.treeRef.setCheckedNodes([]);\n        } else {\n          this.$emit(\"accessTreeData\", nodeDataList[0]);\n          //关闭弹窗\n          this.$emit(\"closeSyddDialog\", false);\n        }\n      },\n\n      //取消按钮\n      close() {\n        //关闭弹窗\n        this.$emit(\"closeSyddDialog\", false);\n      },\n    }\n  }\n</script>\n\n<style scoped>\n\n  /* 设置滚动条的样式 */\n  ::-webkit-scrollbar {\n    width: 12px;\n  }\n\n  /* 滚动槽 */\n  ::-webkit-scrollbar-track {\n  //-webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.3);\n    border-radius: 10px;\n  }\n\n  /* 滚动条滑块 */\n  ::-webkit-scrollbar-thumb {\n    border-radius: 10px;\n    background: rgba(0, 0, 0, 0.1);\n  //-webkit-box-shadow: gba(0, 0, 0, 0.5);\n  }\n\n  ::-webkit-scrollbar-thumb:window-inactive {\n    background: rgba(0, 0, 0, 0.1);\n  }\n\n  [data-v-67a974b1]::-webkit-scrollbar {\n    width: 8px;\n  }\n\n  .item {\n    width: 225px;\n    float: left;\n  }\n\n</style>\n"]}]}