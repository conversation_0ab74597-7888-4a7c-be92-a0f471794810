{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\sbztpj.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\sbztpj.vue", "mtime": 1706897323226}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["sbztpj.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuHA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "sbztpj.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <div>\n      <el-white class=\"button-group\">\n        <div class=\"button_btn\">\n          <el-button type=\"warning\" icon=\"el-icon-search\" @click=\"selectInfo\" class=\"choose\">筛选</el-button>\n          <el-button type=\"cyan\" icon=\"el-icon-view\" @click=\"detailInfo\">查看详情</el-button>\n          <el-button type=\"cyan\" icon=\"el-icon-document\" @click=\"outInfo\">导出</el-button>\n        </div>\n        <ul class=\"sbztpj_top\" v-show=\"topShow\">\n          <li>\n            <span>设备名称：</span>\n            <el-input v-model=\"inputTxt\" placeholder=\"请输入内容\" style=\"width:200px\"></el-input>\n          </li>\n          <li>\n            <span>电压等级：</span>\n            <el-select v-model=\"dyTxt\" placeholder=\"请选择\">\n              <el-option\n                v-for=\"item in fieldList[0].options\"\n                :key=\"item.value\"\n                :label=\"item.label\"\n                :value=\"item.value\">\n              </el-option>\n            </el-select>\n          </li>\n          <li>\n            <span>定期评价结果：</span>\n            <el-select v-model=\"dqpjTxt\" placeholder=\"请选择\">\n              <el-option\n                v-for=\"item in fieldList[1].options\"\n                :key=\"item.value\"\n                :label=\"item.label\"\n                :value=\"item.value\">\n              </el-option>\n            </el-select>\n          </li>\n          <li>\n            <span>最新评价结果：</span>\n            <el-select v-model=\"zxpjTxt\" placeholder=\"请选择\">\n              <el-option\n                v-for=\"item in fieldList[2].options\"\n                :key=\"item.value\"\n                :label=\"item.label\"\n                :value=\"item.value\">\n              </el-option>\n            </el-select>\n          </li>\n          <li>\n            <span>定期评价日期：</span>\n            <el-date-picker\n              v-model=\"dateValue1\"\n              type=\"daterange\"\n              range-separator=\"至\"\n              start-placeholder=\"开始日期\"\n              end-placeholder=\"结束日期\">\n            </el-date-picker>\n          </li>\n          <li>\n            <span>最新评价日期：</span>\n            <el-date-picker\n              v-model=\"dateValue2\"\n              type=\"daterange\"\n              range-separator=\"至\"\n              start-placeholder=\"开始日期\"\n              end-placeholder=\"结束日期\">\n            </el-date-picker>\n          </li>\n          <li>\n            <el-button type=\"cyan\" icon=\"el-icon-search\" @click=\"handleFind\">查询</el-button>\n            <el-button type=\"warning\" icon=\"el-icon-refresh\" @click=\"handleReset\">重置</el-button>\n          </li>\n        </ul>\n        <comp-table :table-and-page-info=\"tableAndPageInfo\" @update:multipleSelection=\"handleSelectionChange\"/>\n      </el-white>\n    </div>\n    <!-- 详情 对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" append-to-body width=\"50%\" @close=\"handleClose\" v-dialogDrag>\n      <template>\n        <el-table\n          :data=\"tableData\"\n          style=\"width: 100%\" class=\"sbztpj_table\">\n          <el-table-column\n            prop=\"sblxmc\"\n            label=\"所属设备\"\n            width=\"auto\">\n          </el-table-column>\n          <el-table-column\n            prop=\"sbbm\"\n            label=\"部件名称\"\n            width=\"auto\">\n          </el-table-column>\n          <el-table-column\n            prop=\"sbbw\"\n            label=\"状态量名称\">\n          </el-table-column>\n          <el-table-column\n            prop=\"sblx\"\n            label=\"状态量来源\">\n          </el-table-column>\n          <el-table-column\n            prop=\"qxms\"\n            label=\"数据回溯\">\n          </el-table-column>\n          <el-table-column\n            prop=\"qxdj\"\n            label=\"扣分理由\">\n          </el-table-column>\n        </el-table>\n      </template>\n      <div v-show=\"formIsEditable\" slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"handleClose\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitForm\">确 认</el-button>\n      </div>\n    </el-dialog>\n\n  </div>\n</template>\n\n<script>\nimport { getPageDataList, remove, saveOrUpdate } from '@/api/dagangOilfield/bzgl/qxbzk'\nimport { getDictTypeData } from '@/api/system/dict/data'\nimport DeviceTree from '@/views/dagangOilfield/bzgl/sbbzk/deviceTree'\nimport { getEquipmentComponentsOptions } from '@/api/dagangOilfield/bzgl/sblxwh/sbbj'\n\nexport default {\n  name: 'xqxbzk',\n  components: { DeviceTree },\n  created() {\n    this.getData()\n  },\n  data() {\n    return {\n      //开始\n      //控制筛选框显示隐藏\n      topShow:false,\n      // 详情框标题\n      title: '',\n      // 新增/修改/详情对话框是否打开\n      open: false,\n      //详情数据\n      tableData: [{\n        flyj: \"重度：道路边的拉线应设防护设施（护坡、反光管、拉线绝缘子）而未设置\",\n        objId: \"00355e990164467a8f482184e8e9544d\",\n        qxdj: \"严重\",\n        qxms: \"拉线防护设施不满足要求\",\n        sbbm: \"拉线\",\n        sbbw: \"钢绞线\",\n        sblx: \"sd19\",\n        sblxmc: \"杆塔1\"\n      }, {\n        flyj: \"重度：道路边的拉线应设防护设施（护坡、反光管、拉线绝缘子）而未设置\",\n        objId: \"00355e990164467a8f482184e8e9544d\",\n        qxdj: \"严重\",\n        qxms: \"拉线防护设施不满足要求\",\n        sbbm: \"拉线\",\n        sbbw: \"钢绞线\",\n        sblx: \"sd19\",\n        sblxmc: \"杆塔2\"\n      }, {\n        flyj: \"重度：道路边的拉线应设防护设施（护坡、反光管、拉线绝缘子）而未设置\",\n        objId: \"00355e990164467a8f482184e8e9544d\",\n        qxdj: \"严重\",\n        qxms: \"拉线防护设施不满足要求\",\n        sbbm: \"拉线\",\n        sbbw: \"钢绞线\",\n        sblx: \"sd19\",\n        sblxmc: \"杆塔3\"\n      }, {\n        flyj: \"重度：道路边的拉线应设防护设施（护坡、反光管、拉线绝缘子）而未设置\",\n        objId: \"00355e990164467a8f482184e8e9544d\",\n        qxdj: \"严重\",\n        qxms: \"拉线防护设施不满足要求\",\n        sbbm: \"拉线\",\n        sbbw: \"钢绞线\",\n        sblx: \"sd19\",\n        sblxmc: \"杆塔4\"\n      }],\n      inputTxt:'',\n      dyTxt:'',\n      dqpjTxt:'',\n      zxpjTxt:'',\n      dateValue1:'',\n      dateValue2:'',\n      fieldList: [\n        {\n          options: [{ label: '10kV', value: '10kV' }, { label: '100kV', value: '100kV' }]\n        },\n        {\n          options: [{ label: '一般', value: '一般' }, { label: '好', value: '好' }]\n        },\n        {\n          options: [{ label: '一般', value: '一般' }, { label: '好', value: '好' }]\n        },\n      ],\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          // { prop: 'zymc', label: '专业', minWidth: '120' },\n          { prop: 'sblxmc', label: '设备类型', minWidth: '180' },\n          { prop: 'sbbm', label: '电压等级', minWidth: '120' },\n          { prop: 'sbbw', label: '出厂日期', minWidth: '120' },\n          { prop: 'qxms', label: '定期评价日期', minWidth: '120' },\n          { prop: 'flyj', label: '定期评价结果', minWidth: '120' },\n          { prop: 'qxdj', label: '最新评价类型', minWidth: '120' },\n          { prop: 'qxdj', label: '最新评价日期', minWidth: '100' },\n          { prop: 'jsyy', label: '最新评价结果', minWidth: '100' },\n          { prop: 'jsyy', label: '得分值', minWidth: '100' },\n        ]\n      },\n      //结束\n\n    }\n  },\n  mounted() {\n  },\n  methods: {\n    /**\n     * 筛选模块\n     */\n    selectInfo(){\n      //判断是否有sxInfo1 class名\n      let box = document.getElementsByClassName('choose')[0];\n      if(box.className.indexOf('sxInfo') < 0){   //判断没有sxInfo class名则添加\n        box.classList.add('sxInfo');\n        this.topShow = true;\n      }else{\n        box.classList.remove('sxInfo');\n        this.topShow = false;\n      }\n    },\n    /**\n     * 查看详情功能\n     */\n    detailInfo(){\n     // console.log('this.id',this.ids)\n   /*   if (this.ids.length !== 0) {\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove(this.ids).then(res => {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            });\n            this.getData();\n          })\n        })\n      } else {\n        this.$message({\n          type: 'info',\n          message: '请选择至少一条数据!'\n        })\n      }*/\n      this.title = '详情'\n      this.open = true\n    },\n    /**\n     * 导出功能\n     */\n    outInfo(){\n\n    },\n    /**\n     * 查询功能\n     */\n    handleFind(){\n\n    },\n    /**\n     * 重置点击事件\n     */\n    handleReset(){\n      this.inputTxt =  this.dyTxt = this.dqpjTxt = this.zxpjTxt = this.dateValue1 = this.dateValue2 = '';\n    },\n    /**\n     * 查询数据\n     */\n    getData(params) {\n      this.queryParams={...this.queryParams,...params}\n      const param = {...this.queryParams, ...params}\n      getPageDataList(param).then(res => {\n        this.tableAndPageInfo.tableData = res.data.records\n        this.tableAndPageInfo.pager.total = res.data.total\n      })\n    },\n    /**\n     * 表格多选框\n     */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.objId)\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n    },\n    /**\n     * 关闭详情弹框框\n     */\n    handleClose() {\n      this.open = false\n    },\n  },\n}\n</script>\n\n<style scoped lang=\"scss\">\n.sxInfo{\n  background:#ffba0075;\n  border-color: #ffba0075;\n}\n.sbztpj_top{\n  list-style-type: none;\n  margin: 0 0 15px 0;\n  padding-left:0;\n  padding-bottom:23px;\n  display: flex;\n  flex-wrap: wrap;\n  border: 1px solid #e2e8ed;\n  li{\n    margin-top: 23px;\n    margin-left: 34px;\n  }\n}\n.sbztpj_table{\n  border: 1px solid #e8edf1;\n}\n</style>\n"]}]}