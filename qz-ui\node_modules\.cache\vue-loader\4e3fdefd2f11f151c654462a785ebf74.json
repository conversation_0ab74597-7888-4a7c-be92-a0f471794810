{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\symbwhDymbnr.vue?vue&type=style&index=0&id=4d412f1e&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\symbwhDymbnr.vue", "mtime": 1706897323686}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKMTE4Ci5ib3gtY2FyZCB7CiAgbWFyZ2luLWJvdHRvbTogMTVweDsKCiAgLmVsLWNhcmRfX2hlYWRlciB7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjM1LCAyNDUsIDI1NSkgIWltcG9ydGFudDsKICB9Cn0KCi5pdGVtIHsKICB3aWR0aDogMjAwcHg7CiAgaGVpZ2h0OiAxNDhweDsKICBmbG9hdDogbGVmdDsKfQoKLmhlYWQtY29udGFpbmVyIHsKICBtYXJnaW46IDAgYXV0bzsKICB3aWR0aDogOTglOwogIG1heC1oZWlnaHQ6IDc5dmg7CiAgb3ZlcmZsb3c6IGF1dG87Cn0K"}, {"version": 3, "sources": ["symbwhDymbnr.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmSA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "symbwhDymbnr.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk", "sourcesContent": ["<template>\n  <!--列表新增关联项目弹窗调用-->\n  <div>\n    <el-row :gutter=\"3\">\n      <div class=\"mb8 pull-right\">\n        <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"mini\" @click=\"addBwToxmbw\" :disabled=\"addBwDisabled\">新增部位\n        </el-button>\n        <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"mini\" @click=\"deleteBwToxmbw\" :disabled=\"deleteBwDisabled\">\n          删除部位\n        </el-button>\n      </div>\n    </el-row>\n    <el-row :gutter=\"20\">\n      <el-col :span=\"12\">\n        <el-table :data=\"mbGlxmDataList\" @row-click=\"handleMbGlxmRowClick\"\n                  @selection-change=\"handleMbGlxmSelectedChange\">\n          <el-table-column label=\"试验项目\" align=\"center\">\n            <el-table-column type=\"selection\" align=\"center\"/>\n            <el-table-column prop=\"syxmmc\" label=\"项目名称\" width=\"180\" align=\"center\"></el-table-column>\n            <el-table-column prop=\"syxmms\" label=\"项目描述\" align=\"center\" :show-overflow-tooltip=\"true\"></el-table-column>\n          </el-table-column>\n        </el-table>\n        <pagination\n          :total=\"glxmTotal\"\n          :page.sync=\"glxmQueryParams.pageNum\"\n          :limit.sync=\"glxmQueryParams.pageSize\"\n          @pagination=\"getSymbGlsyxmDataListByPage\"\n        />\n      </el-col>\n      <el-col :span=\"12\">\n        <el-table :data=\"bwGlxmData\" @selection-change=\"handleXmBwSelectedChange\">\n          <el-table-column label=\"试验部位\" align=\"center\">\n            <el-table-column type=\"selection\" align=\"center\"/>\n            <el-table-column prop=\"sybw\" label=\"部位名称\" align=\"center\"></el-table-column>\n          </el-table-column>\n        </el-table>\n        <pagination\n          :total=\"bwglXmTotal\"\n          :page.sync=\"bwGlxmQueryParam.pageNum\"\n          :limit.sync=\"bwGlxmQueryParam.pageSize\"\n          @pagination=\"getXmBwDataByPage\"\n        />\n      </el-col>\n    </el-row>\n\n    <!--新增试验部位弹出框-->\n    <el-dialog title=\"部位库\" :visible.sync=\"isShowAddSybwDialog\" append-to-body width=\"50%\" v-dialogDrag>\n      <el-form label-width=\"120px\">\n        <el-row :gutter=\"3\">\n          <el-col :span=\"10\">\n            <el-form-item label=\"部位名称：\">\n              <el-input v-model=\"bwLibraryQueryForm.sybw\"/>\n            </el-form-item>\n          </el-col>\n          <div class=\"mb8 pull-right\">\n            <el-button type=\"cyan\" size=\"mini\" icon=\"el-icon-search\" @click=\"selectBwLibrary\">查询</el-button>\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetBwSearch\">重置</el-button>\n          </div>\n        </el-row>\n      </el-form>\n      <el-table stripe border :data=\"bwLibraryDataList\" @selection-change=\"handleSelectedBwLibraryChange\"\n                :header-cell-style=\"{'text-align':'center'}\" :cell-style=\"{'text-align':'center'}\">\n        <el-table-column type=\"selection\" width=\"55\"/>\n        <el-table-column label=\"序号\" type=\"index\" width=\"50\">\n        </el-table-column>\n        <el-table-column label=\"部位名称\" prop=\"sybw\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"备注\" prop=\"bz\" :show-overflow-tooltip=\"true\"/>\n      </el-table>\n      <pagination\n        v-show=\"bwLibraryTotal>0\"\n        :total=\"bwLibraryTotal\"\n        :page.sync=\"bwLibraryQueryForm.pageNum\"\n        :limit.sync=\"bwLibraryQueryForm.pageSize\"\n        @pagination=\"getBwLibraryBySblxAndSyxmid\"\n      />\n      <div slot=\"footer\">\n        <el-button @click=\"closeAddBwToXmbwDialog\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"commitAddBwToXmbwDialog\">确 认</el-button>\n      </div>\n    </el-dialog>\n  </div>\n\n</template>\n<script>\n  import {\n    getSymbGlsyxmDataListByPage,\n    getXmBwDataByPage,\n    getBwLibraryBySblxAndSyxmid,\n    addBatchBwToSyxm,\n    removeXmBw\n  } from '@/api/dagangOilfield/bzgl/symbwh'\n\n  export default {\n    name: \"symbwhDymbnr\",\n    props: {\n      mbData: {\n        type: Object\n      }\n    },\n    data() {\n      return {\n        //部位库选中数据参数\n        bwLibraryDataForm: {\n          //试验项目id\n          syxmid: \"\",\n          //选中的部位库数据集合\n          bwLibraryDataRows: []\n        },\n\n        //部位库查询参数\n        bwLibraryQueryForm: {\n          sybw: \"\",\n          syxmid: undefined,\n          sblxbm: undefined,\n          pageNum: 1,\n          pageSize: 10\n        },\n        //部位库数据总数\n        bwLibraryTotal: 0,\n        //部位库数据\n        bwLibraryDataList: [],\n        //新增试验部位弹出框控制\n        isShowAddSybwDialog: false,\n        //新增部位按钮控制\n        addBwDisabled: true,\n        //删除部位按钮控制\n        deleteBwDisabled: false,\n        //试验项目数据集合\n        mbGlxmDataList: [],\n        //关联项目总数\n        glxmTotal: 0,\n        //关联项目查询参数\n        glxmQueryParams: {\n          symbid: undefined,\n          pageNum: 1,\n          pageSize: 10\n        },\n        //项目关联部位数据\n        bwGlxmData: [],\n        //关联部位总数\n        bwglXmTotal: 0,\n        //部位查询参数\n        bwGlxmQueryParam: {\n          syxmid: undefined,\n          pageNum: 1,\n          pageSize: 10\n        },\n        //已关联的试验部位复选框选中数据\n        xmGlBwRows: []\n\n\n      };\n    },\n    watch: {},\n    created() {\n\n      //获取试验模板关联项目列表\n      this.getSymbGlsyxmDataListByPage();\n    },\n    methods: {\n      //获取关联项目弹出框数据\n      getSymbGlsyxmDataListByPage() {\n        this.glxmQueryParams.symbid = this.mbData.objId;\n        //设备类型赋值\n        this.bwLibraryQueryForm.sblxbm = this.mbData.sblxid;\n        getSymbGlsyxmDataListByPage(this.glxmQueryParams).then(res => {\n          this.mbGlxmDataList = res.data.records;\n          this.glxmTotal = res.data.total;\n        })\n      },\n      //试验项目行点击事件\n      handleMbGlxmRowClick(row) {\n        this.bwGlxmQueryParam.syxmid = row.syxmid;\n        this.getXmBwDataByPage();\n      },\n      //试验项目左侧复选框\n      handleMbGlxmSelectedChange(rows) {\n        this.addBwDisabled = rows.length != 1;\n        console.log(\"点击左侧复选框了\")\n        console.log(rows)\n        //给查询部位库赋值\n        this.bwLibraryQueryForm.syxmid = rows[0].syxmid;\n        //新增部位库时赋值\n        this.bwLibraryDataForm.syxmid = rows[0].syxmid;\n      },\n      //根据项目id获取试验部位数据方法\n      getXmBwDataByPage() {\n        getXmBwDataByPage(this.bwGlxmQueryParam).then(res => {\n          this.bwGlxmData = res.data.records;\n          this.bwglXmTotal = res.data.total;\n        })\n      },\n      //新增部位按钮\n      addBwToxmbw() {\n        //打开新增部位弹出框\n        this.isShowAddSybwDialog = true;\n        this.getBwLibraryBySblxAndSyxmid();\n      },\n      //获取部位库数据\n      getBwLibraryBySblxAndSyxmid() {\n        getBwLibraryBySblxAndSyxmid(this.bwLibraryQueryForm).then(res => {\n          console.log(\"部位库数据\")\n          console.log(res);\n          this.bwLibraryDataList = res.data.records;\n          this.bwLibraryTotal = res.data.total;\n        })\n      },\n      //部位库查询按钮\n      selectBwLibrary() {\n        this.getBwLibraryBySblxAndSyxmid();\n      },\n      //部位库重置按钮\n      resetBwSearch() {\n        this.bwLibraryQueryForm.sybw = \"\";\n        this.getBwLibraryBySblxAndSyxmid();\n      },\n      //部位库复选框选中事件\n      handleSelectedBwLibraryChange(rows) {\n        this.bwLibraryDataForm.bwLibraryDataRows = rows;\n\n      },\n      //部位库弹出框取消\n      closeAddBwToXmbwDialog() {\n        this.isShowAddSybwDialog = false;\n      },\n      //部位库弹出框确定\n      commitAddBwToXmbwDialog() {\n        if (this.bwLibraryDataForm.bwLibraryDataRows.length < 1) {\n          this.$message.info('未关联部位！！！已取消')\n          //如果未选中数据,则直接关闭弹窗\n          this.isShowAddSybwDialog = false\n        } else {\n          console.log(this.bwLibraryDataForm)\n          //若选择数据后\n          addBatchBwToSyxm(this.bwLibraryDataForm).then(res => {\n            if (res.code === '0000') {\n              this.$message.success('关联成功')\n            } else {\n              this.$message.error('关联失败！！')\n            }\n            //关闭弹窗\n            this.isShowAddSybwDialog = false\n            //调用获取部位列表\n            this.getXmBwDataByPage()\n          })\n        }\n      },\n      //已关联的试验部位左侧选中按钮\n      handleXmBwSelectedChange(rows) {\n        this.xmGlBwRows = rows;\n      },\n      //删除部位\n      deleteBwToxmbw() {\n        if (this.xmGlBwRows.length < 1) {\n          this.$message.warning('请选择要删除的试验部位！！！')\n          return\n        }\n        let ids = this.xmGlBwRows.map(item => {\n          return item.objId\n        })\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          removeXmBw(ids).then(({code}) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getXmBwDataByPage()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n              this.getXmBwDataByPage()\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n    }\n  };\n</script>\n\n<style lang='scss' scoped>118\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  max-height: 79vh;\n  overflow: auto;\n}\n</style>\n"]}]}