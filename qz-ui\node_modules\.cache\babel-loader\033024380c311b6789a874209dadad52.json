{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\ysbzkgl\\acceptanceDetail.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\ysbzkgl\\acceptanceDetail.vue", "mtime": 1706897323967}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["acceptanceDetail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAkEA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,UAAA,EAAA;AAAA,IAAA,UAAA,EAAA;AAAA,GADA;AAEA,EAAA,IAAA,EAAA,kBAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA;AACA,MAAA,IAAA,EAAA,CAFA;AAGA;AACA,MAAA,QAAA,EAAA,IAJA;AAKA;AACA,MAAA,aAAA,EAAA,KANA;AAOA;AACA,MAAA,YAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA,CAHA;AAIA;AACA,QAAA,MAAA,EAAA;AALA,OARA;AAeA;AACA,MAAA,QAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,OAHA;AAIA,QAAA,IAAA,EAAA,KAJA;AAKA,QAAA,OAAA,EAAA,IALA;AAMA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AANA,OADA,EASA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,UAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,OAAA,EAAA,IALA;AAMA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AANA,OATA,EAiBA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,OAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,OAAA,EAAA,IALA;AAMA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AANA,OAjBA,EAyBA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,UAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,OAAA,EAAA,IALA;AAMA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AANA,OAzBA,EAiCA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,OAAA,EAAA,IAJA;AAKA,QAAA,IAAA,EAAA;AALA,OAjCA,EAwCA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,OAAA,EAAA,KAJA;AAKA,QAAA,IAAA,EAAA,OALA;AAMA,QAAA,MAAA,EAAA;AANA,OAxCA,EAgDA;AACA,QAAA,KAAA,EAAA,SADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,QAHA;AAIA,QAAA,OAAA,EAAA,KAJA;AAKA,QAAA,IAAA,EAAA,OALA;AAMA,QAAA,MAAA,EAAA,KANA;AAOA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA;AAAA;AAPA,OAhDA,CAhBA;AA0EA;AACA;AACA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,UAAA,EAAA,EAFA;AAGA,UAAA,OAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAJA;AAKA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AALA,SADA;AAQA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,KADA;AAEA,UAAA,YAAA,EAAA;AAFA,SARA;AAYA,QAAA,SAAA,EAAA,EAZA;AAaA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SALA;AAbA,OA5EA;AAiGA;AACA,MAAA,QAAA,EAAA,KAlGA;AAmGA;AACA,MAAA,mBAAA,EAAA,EApGA;AAqGA;AACA,MAAA,eAAA,EAAA;AAtGA,KAAA;AAwGA,GA5GA;AA6GA,EAAA,OA7GA,qBA6GA,CACA,CA9GA;AA+GA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,uBAFA,mCAEA,GAFA,EAEA;AACA,WAAA,mBAAA,GAAA,GAAA;;AACA,UAAA,GAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,aAAA,QAAA,GAAA,IAAA;AACA,aAAA,OAAA;AACA,OAHA,MAGA;AACA,aAAA,QAAA,GAAA,KAAA;AACA,aAAA,aAAA,GAAA,IAAA;AACA,aAAA,gBAAA,CAAA,SAAA;AACA,aAAA,aAAA,GAAA,KAAA;AACA;AACA,KAbA;AAcA;AACA,IAAA,QAfA,sBAeA;AACA,WAAA,YAAA,CAAA,OAAA,GAAA,CAAA;AACA,WAAA,YAAA,CAAA,QAAA,GAAA,EAAA;AACA,KAlBA;AAmBA;AACA,IAAA,OApBA,mBAoBA,MApBA,EAoBA;AAAA;;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,YAAA,+DAAA,KAAA,YAAA,GAAA,MAAA;AACA,WAAA,YAAA,CAAA,MAAA,GAAA,KAAA,mBAAA,CAAA,CAAA,EAAA,EAAA;AACA,WAAA,YAAA,CAAA,IAAA,GAAA,CAAA;AACA,8BAAA,KAAA,YAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA;AACA,QAAA,KAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,KAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,KAAA,CAAA,aAAA,GAAA,KAAA;AACA,OANA;AAOA,KAhCA;AAkCA,IAAA,qBAlCA,iCAkCA,GAlCA,EAkCA;AACA,WAAA,eAAA,GAAA,GAAA;AACA,KApCA;AAsCA,IAAA,cAtCA,0BAsCA,GAtCA,EAsCA;AACA,WAAA,KAAA,CAAA,WAAA,CAAA,kBAAA,CAAA,GAAA;AACA,KAxCA;AA0CA;AACA,IAAA,cA3CA,0BA2CA,QA3CA,EA2CA;AAAA;;AACA,UAAA,OAAA,GAAA,EAAA;;AACA,UAAA,QAAA,CAAA,EAAA,KAAA,EAAA,IAAA,CAAA,QAAA,CAAA,EAAA,EAAA;AACA,QAAA,OAAA,GAAA,MAAA;AACA,QAAA,QAAA,CAAA,MAAA,GAAA,KAAA,mBAAA,CAAA,CAAA,EAAA,EAAA;AACA,OAHA,MAGA;AACA,QAAA,OAAA,GAAA,MAAA;AACA;;AACA,uCAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,OAAA;;AACA,UAAA,MAAA,CAAA,OAAA;AACA,SAHA,MAGA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,OAPA;AAQA,KA3DA;AA6DA;AACA,IAAA,aA9DA,2BA8DA;AACA,WAAA,QAAA,GAAA,IAAA,CADA,CAGA;AACA;;AAEA,UAAA,OAAA,GAAA,KAAA,QAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,eAAA,IAAA;AACA,OAFA,CAAA;AAGA,WAAA,KAAA,CAAA,UAAA,CAAA,OAAA,CAAA,OAAA;AACA,KAxEA;AA0EA;AACA,IAAA,gBA3EA,4BA2EA,GA3EA,EA2EA;AACA,UAAA,UAAA,GAAA,KAAA,QAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,eAAA,IAAA;AACA,OAHA,CAAA;AAIA,WAAA,QAAA,GAAA,IAAA;AACA,WAAA,KAAA,CAAA,UAAA,CAAA,OAAA,CAAA,UAAA;AACA,KAlFA;AAoFA;AACA,IAAA,cArFA,0BAqFA,GArFA,EAqFA;AACA,UAAA,QAAA,GAAA,KAAA,QAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,eAAA,IAAA;AACA,OAHA,CAAA;AAIA,WAAA,QAAA,GAAA,IAAA;AACA,WAAA,KAAA,CAAA,UAAA,CAAA,OAAA,CAAA,QAAA;AACA,KA5FA;AA8FA;AACA,IAAA,gBA/FA,8BA+FA;AAAA;;AACA,WAAA,QAAA,CAAA,iBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,YAAA,GAAA,GAAA,EAAA;;AACA,QAAA,MAAA,CAAA,eAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA,SAFA;;AAIA,mCAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,OAAA,EAAA,MADA;AAEA,cAAA,IAAA,EAAA;AAFA,aAAA;AAIA,WALA,MAKA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;;AACA,UAAA,MAAA,CAAA,OAAA;AACA,SAVA;AAWA,OArBA,EAqBA,KArBA,CAqBA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OA1BA;AA2BA;AA3HA;AA/GA,C", "sourcesContent": ["<template>\n  <div>\n    <el-white class=\"button-group\">\n      <div class=\"button_btn\">\n        <el-button type=\"primary\" icon=\"el-icon-plus\" v-hasPermi=\"['bzysbzk:button:add']\" @click=\"addDetailData\" :disabled=\"!isCanAdd\">新增</el-button>\n        <el-button type=\"danger\" icon=\"el-icon-delete\" v-hasPermi=\"['bzysbzk:button:delete']\" @click=\"deleteDetailData\" :disabled=\"selectedRowData.length===0\">\n          删除\n        </el-button>\n      </div>\n      <div class=\"button_btn\">验收项</div>\n      <!-- <el-table ref=\"detailTable\" stripe border v-loading=\"detailLoading\" :data=\"detailTableData\"\n        @selection-change=\"detailSelectionChange\" @row-click=\"detailRowClick\">\n        <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n        <el-table-column label=\"验收项目\" align=\"center\" prop=\"ysx\" width=\"200\"/>\n        <el-table-column label=\"验收标准\" align=\"center\" prop=\"ysbz\"\n          style=\"overflow: hidden;text-overflow: ellipsis;white-space: nowrap\" :show-overflow-tooltip=\"true\">\n          <template slot-scope=\"scope\">\n            <el-popover v-if=\"scope.row.ysbz.length>55\" trigger=\"hover\" placement=\"top\"\n              style=\"overflow: hidden;text-overflow: ellipsis;white-space: nowrap;\">\n              {{ scope.row.ysbz }}\n              <div slot=\"reference\">\n                {{ scope.row.ysbz.substring(0,55)+'...' }}\n              </div>\n            </el-popover>\n            <span v-else>\n              {{ scope.row.ysbz}}\n            </span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"检查方式\" align=\"center\" prop=\"jcfs\" :show-overflow-tooltip=\"true\" width=\"120\"/>\n        <el-table-column label=\"验收结论\" align=\"center\" prop=\"ysjl\" :show-overflow-tooltip=\"true\" width=\"120\"/>\n        <el-table-column label=\"备注\" align=\"center\" prop=\"bz\" :show-overflow-tooltip=\"true\" width=\"120\"/> -->\n      <comp-table\n            ref=\"detailTable\"\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"detailSelectionChange\"\n            v-loading=\"detailLoading\"\n      >\n        <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              :resizable=\"false\"\n              width=\"120\"\n            >\n        <!-- <el-table-column label=\"操作\" fixed=\"right\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"180\"> -->\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" v-hasPermi=\"['bzysbzk:button:update']\" @click=\"updateDetailData(scope.row)\" title=\"修改\" class='el-icon-edit'></el-button>\n            <el-button type=\"text\" @click=\"showDetailData(scope.row)\" title=\"详情\" class=\"el-icon-view\"></el-button>\n          </template>\n        </el-table-column>\n      <!-- </el-table> -->\n      </comp-table>\n\n      <!-- <pagination v-show=\"detailParams.total>0\" :total=\"detailParams.total\" :page.sync=\"detailParams.pageNum\"\n        :limit.sync=\"detailParams.pageSize\" @pagination=\"getData\" /> -->\n    </el-white>\n\n    <dialogForm ref=\"detailForm\" label-width=\"150px\" out-width=\"50%\" :reminder=\"reminder\" :rows=\"rows\"\n      @save=\"saveDetailData\" />\n  </div>\n</template>\n\n<script>\nimport { deleteBzYsbzmx, getBzYsbzmx, saveOrUpdateBzYsbzmx } from '@/api/bzgl/ysbzk/ysbzk'\nimport dialogForm from 'com/dialogFrom/dialogForm'\n\nexport default {\n  components: { dialogForm },\n  name: 'acceptanceDetail',\n  data() {\n    return {\n      //弹出框form表单每行显示几个\n      rows: 2,\n      //新增或修改标题\n      reminder: '新增',\n      //明细按钮加载\n      detailLoading: false,\n      //验收标准明细表查询条件\n      detailParams: {\n        pageNum: 1,\n        pageSize: 10,\n        total: 0,\n        //验收标准主表id\n        ysbzid: ''\n      },\n      //明细表新增表单数据\n      formList: [\n        {\n          label: '验收项：',\n          value: '',\n          type: 'input',\n          name: 'ysx',\n          default: true,\n          rules: { required: true, message: '请输入',trigger: 'blur' }\n        },\n        {\n          label: '验收标准：',\n          value: '',\n          type: 'textarea',\n          name: 'ysbz',\n          default: true,\n          rules: { required: true, message: '请输入',trigger: 'blur' }\n        },\n        {\n          label: '检查方式：',\n          value: '',\n          type: 'input',\n          name: 'jcfs',\n          default: true,\n          rules: { required: true, message: '请输入',trigger: 'blur' }\n        },\n        {\n          label: '验收结论：',\n          value: '',\n          type: 'textarea',\n          name: 'ysjl',\n          default: true,\n          rules: { required: true, message: '请输入',trigger: 'blur' }\n        },\n        {\n          label: '备注：',\n          value: '',\n          name: 'bz',\n          default: true,\n          type: 'textarea',\n        },\n        {\n          label: '主键id：',\n          value: '',\n          name: 'id',\n          default: false,\n          type: 'input',\n          hidden: false,\n        },\n        {\n          label: '验收标准id：',\n          value: '',\n          name: 'ysbzid',\n          default: false,\n          type: 'input',\n          hidden: false,\n          rules: { required: false }\n        }\n      ],\n      //验收标准明细表数据\n      // detailTableData: [],\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageResize: '',\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: false,\n          serialNumber: false\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: 'ysx', label: '验收项目',width: '200'},\n          { prop: 'ysbz', label: '验收标准', showPop: true},\n          { prop: 'jcfs', label: '检查方式',width: '200'},\n          { prop: 'ysjl', label: '验收结论',width: '100'},\n          { prop: 'bz', label: '备注',width: '100'},\n        ]\n      },\n      //新增按钮是否可用\n      isCanAdd: false,\n      //主表选中数据\n      mainTableSelectRows: [],\n      //详情表选中数据\n      selectedRowData: []\n    }\n  },\n  mounted() {\n  },\n  methods: {\n    //接受父组件传值方法\n    getMainTableSelectedRow(row) {\n      this.mainTableSelectRows = row\n      if (row.length === 1) {\n        this.isCanAdd = true\n        this.getData()\n      } else {\n        this.isCanAdd = false\n        this.detailLoading = true\n        this.tableAndPageInfo.tableData\n        this.detailLoading = false\n      }\n    },\n    //重置按钮\n    getReset() {\n      this.detailParams.pageNum = 1\n      this.detailParams.pageSize = 10\n    },\n    //获取验收标准明细表数据\n    getData(params) {\n      this.detailLoading = true\n      this.detailParams = { ...this.detailParams, ...params };\n      this.detailParams.ysbzid = this.mainTableSelectRows[0].id\n      this.detailParams.type = 0;\n      getBzYsbzmx(this.detailParams).then(res => {\n        // this.detailTableData = res.data.records\n        // this.detailParams.total = res.data.total\n        this.tableAndPageInfo.tableData = res.data.records;\n        this.tableAndPageInfo.pager.total = res.data.total\n        this.detailLoading = false\n      })\n    },\n\n    detailSelectionChange(row) {\n      this.selectedRowData = row\n    },\n\n    detailRowClick(val) {\n      this.$refs.detailTable.toggleRowSelection(val)\n    },\n\n    //保存明细表数据\n    saveDetailData(formData) {\n      let message = ''\n      if (formData.id === '' || !formData.id) {\n        message = '新增成功'\n        formData.ysbzid = this.mainTableSelectRows[0].id\n      } else {\n        message = '修改成功'\n      }\n      saveOrUpdateBzYsbzmx(formData).then(res => {\n        if (res.code === '0000') {\n          this.$message.success(message)\n          this.getData()\n        } else {\n          this.$message.error('操作失败')\n        }\n      })\n    },\n\n    //新增明细数据\n    addDetailData() {\n      this.reminder = '新增'\n\n      //初始话formList数据\n      // this.formList = this.$options.data().formList\n\n      const addForm = this.formList.map(item => {\n        return item\n      })\n      this.$refs.detailForm.showzzc(addForm)\n    },\n\n    //修改明细数据\n    updateDetailData(row) {\n      const updateList = this.formList.map(item => {\n        item.value = row[item.name]\n        return item\n      })\n      this.reminder = '修改'\n      this.$refs.detailForm.showzzc(updateList)\n    },\n\n    //明细数据详情\n    showDetailData(row) {\n      const infoList = this.formList.map(item => {\n        item.value = row[item.name]\n        return item\n      })\n      this.reminder = '详情'\n      this.$refs.detailForm.showzzc(infoList)\n    },\n\n    //删除明细信息\n    deleteDetailData() {\n      this.$confirm('确认删除选中数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        let ids = []\n        this.selectedRowData.forEach(item => {\n          ids.push(item.id)\n        })\n\n        deleteBzYsbzmx(ids).then(res => {\n          if (res.code === '0000') {\n            this.$message({\n              message: '删除成功',\n              type: 'success'\n            })\n          } else {\n            this.$message.error('操作失败')\n          }\n          this.getData()\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n\n</style>\n"], "sourceRoot": "src/views/dagangOilfield/bzgl/ysbzkgl"}]}