{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\qxbzk.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\qxbzk.vue", "mtime": 1706897322891}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Cgp2YXIgX3F4YnprID0gcmVxdWlyZSgiQC9hcGkvZGFnYW5nT2lsZmllbGQvYnpnbC9xeGJ6ayIpOwoKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gewogIG5hbWU6ICJxeGJ6ayIsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGZpbHRlckluZm86IHsKICAgICAgICBkYXRhOiB7CiAgICAgICAgICB5d2R3QXJyOiBbXQogICAgICAgIH0sCiAgICAgICAgZmllbGRMaXN0OiBbewogICAgICAgICAgbGFiZWw6ICfpmpDmgqPnrYnnuqcnLAogICAgICAgICAgdHlwZTogJ3NlbGVjdCcsCiAgICAgICAgICB2YWx1ZTogJ2RlZmVjdExldmVsJywKICAgICAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAgICAgb3B0aW9uczogW10KICAgICAgICB9LCB7CiAgICAgICAgICBsYWJlbDogJ+agh+WHhuaPj+i/sCcsCiAgICAgICAgICB0eXBlOiAnaW5wdXQnLAogICAgICAgICAgdmFsdWU6ICdkZWZlY3RMZXZlbCcKICAgICAgICB9LCB7CiAgICAgICAgICBsYWJlbDogJ+iuvuWkh+eKtuaAgScsCiAgICAgICAgICB0eXBlOiAnc2VsZWN0JywKICAgICAgICAgIHZhbHVlOiAnZGVmZWN0TGV2ZWwnLAogICAgICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgICAgICBvcHRpb25zOiBbXQogICAgICAgIH0sIHsKICAgICAgICAgIGxhYmVsOiAn55S15Y6L562J57qnJywKICAgICAgICAgIHR5cGU6ICdzZWxlY3QnLAogICAgICAgICAgdmFsdWU6ICdkZWZlY3RMZXZlbCcsCiAgICAgICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgICAgIG9wdGlvbnM6IFtdCiAgICAgICAgfSwgewogICAgICAgICAgbGFiZWw6ICfnu7TmiqTnj63nu4QnLAogICAgICAgICAgdHlwZTogJ3NlbGVjdCcsCiAgICAgICAgICB2YWx1ZTogJ2RlZmVjdExldmVsJwogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIHRhYmxlQW5kUGFnZUluZm86IHsKICAgICAgICBwYWdlcjogewogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMDBdCiAgICAgICAgfSwKICAgICAgICBvcHRpb246IHsKICAgICAgICAgIGNoZWNrQm94OiB0cnVlLAogICAgICAgICAgc2VyaWFsTnVtYmVyOiB0cnVlCiAgICAgICAgfSwKICAgICAgICB0YWJsZURhdGE6IFtdLAogICAgICAgIHRhYmxlSGVhZGVyOiBbewogICAgICAgICAgcHJvcDogJ3NienQnLAogICAgICAgICAgbGFiZWw6ICfnvLrpmbfnvJbnoIEnLAogICAgICAgICAgbWluV2lkdGg6ICcxMjAnCiAgICAgICAgfSwgewogICAgICAgICAgcHJvcDogJ3NienQnLAogICAgICAgICAgbGFiZWw6ICfpmpDmgqPnrYnnuqcnLAogICAgICAgICAgbWluV2lkdGg6ICcxMjAnCiAgICAgICAgfSwgewogICAgICAgICAgcHJvcDogJ3NienQnLAogICAgICAgICAgbGFiZWw6ICfmoIflh4bmj4/ov7AnLAogICAgICAgICAgbWluV2lkdGg6ICcxMjAnCiAgICAgICAgfSwgewogICAgICAgICAgcHJvcDogJ29wZXJhdGlvbicsCiAgICAgICAgICBsYWJlbDogJ+aTjeS9nCcsCiAgICAgICAgICBtaW5XaWR0aDogJzEzMHB4JywKICAgICAgICAgIHN0eWxlOiB7CiAgICAgICAgICAgIGRpc3BsYXk6ICdibG9jaycKICAgICAgICAgIH0sCiAgICAgICAgICAvL+aTjeS9nOWIl+WbuuWumuWGjeWPs+S+pwogICAgICAgICAgZml4ZWQ6ICdyaWdodCcsCiAgICAgICAgICBvcGVyYXRpb246IFt7CiAgICAgICAgICAgIG5hbWU6ICfor6bmg4UnLAogICAgICAgICAgICBjbGlja0Z1bjogdGhpcy5nZXREZXRhaWxzCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIG5hbWU6ICfkv67mlLknLAogICAgICAgICAgICBjbGlja0Z1bjogdGhpcy5nZXREZXRhaWxzCiAgICAgICAgICB9XQogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIC8v5by55Ye65qGGCiAgICAgIGRpYWxvZ0Zvcm1WaXNpYmxlOiBmYWxzZSwKICAgICAgLy/lvLnlh7rmoYbooajljZUKICAgICAgZm9ybToge30sCiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICAvL+e7hOe7h+agkQogICAgICB0cmVlT3B0aW9uczogW3sKICAgICAgICBsYWJlbDogJ+aWrei3r+WZqCcKICAgICAgfSwgewogICAgICAgIGxhYmVsOiAnMeWPt+WPmOWOi+WZqCcsCiAgICAgICAgY2hpbGRyZW46IFt7CiAgICAgICAgICBsYWJlbDogJ+WGt+WNtOezu+e7nycsCiAgICAgICAgICBjaGlsZHJlbjogW3sKICAgICAgICAgICAgbGFiZWw6ICfmuKnmjqfov5DooYzmg4XlhrUnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGxhYmVsOiAn5rK5566xJwogICAgICAgICAgfSwgewogICAgICAgICAgICBsYWJlbDogJ+mTgeiKrycKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgbGFiZWw6ICfnu5Xnu4QnCiAgICAgICAgICB9XQogICAgICAgIH1dCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJzLlj7flj5jljovlmagnLAogICAgICAgIGNoaWxkcmVuOiBbewogICAgICAgICAgbGFiZWw6ICflhrfljbTns7vnu58nLAogICAgICAgICAgY2hpbGRyZW46IFt7CiAgICAgICAgICAgIGxhYmVsOiAn5rip5o6n6L+Q6KGM5oOF5Ya1JwogICAgICAgICAgfSwgewogICAgICAgICAgICBsYWJlbDogJ+ayueeusScKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgbGFiZWw6ICfpk4Hoiq8nCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGxhYmVsOiAn57uV57uEJwogICAgICAgICAgfV0KICAgICAgICB9XQogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICcz5Y+35Y+Y5Y6L5ZmoJywKICAgICAgICBjaGlsZHJlbjogW3sKICAgICAgICAgIGxhYmVsOiAn5Ya35Y2057O757ufJywKICAgICAgICAgIGNoaWxkcmVuOiBbewogICAgICAgICAgICBsYWJlbDogJ+a4qeaOp+i/kOihjOaDheWGtScKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgbGFiZWw6ICfmsrnnrrEnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGxhYmVsOiAn6ZOB6IqvJwogICAgICAgICAgfSwgewogICAgICAgICAgICBsYWJlbDogJ+e7lee7hCcKICAgICAgICAgIH1dCiAgICAgICAgfV0KICAgICAgfSwgewogICAgICAgIGxhYmVsOiAnNOWPt+WPmOWOi+WZqCcsCiAgICAgICAgY2hpbGRyZW46IFt7CiAgICAgICAgICBsYWJlbDogJ+WGt+WNtOezu+e7nycsCiAgICAgICAgICBjaGlsZHJlbjogW3sKICAgICAgICAgICAgbGFiZWw6ICfmuKnmjqfov5DooYzmg4XlhrUnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGxhYmVsOiAn5rK5566xJwogICAgICAgICAgfSwgewogICAgICAgICAgICBsYWJlbDogJ+mTgeiKrycKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgbGFiZWw6ICfnu5Xnu4QnCiAgICAgICAgICB9XQogICAgICAgIH1dCiAgICAgIH1dLAogICAgICB1c2VyTGlzdDogW3sKICAgICAgICBxeGNvZGU6ICdxeDIwMjIwMTEzJywKICAgICAgICBxeGxldmVsOiAn5LiA6IisQycsCiAgICAgICAgcXhkZXNjOiAiMeWPt+S4u+WPmEHnm7jmnInovb3osIPljovnk6bmlq/nu6fnlLXlmajkuK3mnInmsJTkvZMiCiAgICAgIH0sIHsKICAgICAgICBxeGNvZGU6ICdxeDIwMjIwMTEzJywKICAgICAgICBxeGxldmVsOiAn5LiA6IisQycsCiAgICAgICAgcXhkZXNjOiAiMeWPt+S4u+WPmEHnm7jmnInovb3osIPljovnk6bmlq/nu6fnlLXlmajkuK3mnInmsJTkvZMiCiAgICAgIH0sIHsKICAgICAgICBxeGNvZGU6ICdxeDIwMjIwMTEzJywKICAgICAgICBxeGxldmVsOiAn5LiA6IisQycsCiAgICAgICAgcXhkZXNjOiAiMeWPt+S4u+WPmEHnm7jmnInovb3osIPljovnk6bmlq/nu6fnlLXlmajkuK3mnInmsJTkvZMiCiAgICAgIH0sIHsKICAgICAgICBxeGNvZGU6ICdxeDIwMjIwMTEzJywKICAgICAgICBxeGxldmVsOiAn5LiA6IisQycsCiAgICAgICAgcXhkZXNjOiAiMeWPt+S4u+WPmEHnm7jmnInovb3osIPljovnk6bmlq/nu6fnlLXlmajkuK3mnInmsJTkvZMiCiAgICAgIH0sIHsKICAgICAgICBxeGNvZGU6ICdxeDIwMjIwMTEzJywKICAgICAgICBxeGxldmVsOiAn5LiA6IisQycsCiAgICAgICAgcXhkZXNjOiAiMeWPt+S4u+WPmEHnm7jmnInovb3osIPljovnk6bmlq/nu6fnlLXlmajkuK3mnInmsJTkvZMiCiAgICAgIH0sIHsKICAgICAgICBxeGNvZGU6ICdxeDIwMjIwMTEzJywKICAgICAgICBxeGxldmVsOiAn5LiA6IisQycsCiAgICAgICAgcXhkZXNjOiAiMeWPt+S4u+WPmEHnm7jmnInovb3osIPljovnk6bmlq/nu6fnlLXlmajkuK3mnInmsJTkvZMiCiAgICAgIH0sIHsKICAgICAgICBxeGNvZGU6ICdxeDIwMjIwMTEzJywKICAgICAgICBxeGxldmVsOiAn5LiA6IisQycsCiAgICAgICAgcXhkZXNjOiAiMeWPt+S4u+WPmEHnm7jmnInovb3osIPljovnk6bmlq/nu6fnlLXlmajkuK3mnInmsJTkvZMiCiAgICAgIH0sIHsKICAgICAgICBxeGNvZGU6ICdxeDIwMjIwMTEzJywKICAgICAgICBxeGxldmVsOiAn5LiA6IisQycsCiAgICAgICAgcXhkZXNjOiAiMeWPt+S4u+WPmEHnm7jmnInovb3osIPljovnk6bmlq/nu6fnlLXlmajkuK3mnInmsJTkvZMiCiAgICAgIH0sIHsKICAgICAgICBxeGNvZGU6ICdxeDIwMjIwMTEzJywKICAgICAgICBxeGxldmVsOiAn5LiA6IisQycsCiAgICAgICAgcXhkZXNjOiAiMeWPt+S4u+WPmEHnm7jmnInovb3osIPljovnk6bmlq/nu6fnlLXlmajkuK3mnInmsJTkvZMiCiAgICAgIH0sIHsKICAgICAgICBxeGNvZGU6ICdxeDIwMjIwMTEzJywKICAgICAgICBxeGxldmVsOiAn5LiA6IisQycsCiAgICAgICAgcXhkZXNjOiAiMeWPt+S4u+WPmEHnm7jmnInovb3osIPljovnk6bmlq/nu6fnlLXlmajkuK3mnInmsJTkvZMiCiAgICAgIH0sIHsKICAgICAgICBxeGNvZGU6ICdxeDIwMjIwMTEzJywKICAgICAgICBxeGxldmVsOiAn5LiA6IisQycsCiAgICAgICAgcXhkZXNjOiAiMeWPt+S4u+WPmEHnm7jmnInovb3osIPljovnk6bmlq/nu6fnlLXlmajkuK3mnInmsJTkvZMiCiAgICAgIH0sIHsKICAgICAgICBxeGNvZGU6ICdxeDIwMjIwMTEzJywKICAgICAgICBxeGxldmVsOiAn5LiA6IisQycsCiAgICAgICAgcXhkZXNjOiAiMeWPt+S4u+WPmEHnm7jmnInovb3osIPljovnk6bmlq/nu6fnlLXlmajkuK3mnInmsJTkvZMiCiAgICAgIH0sIHsKICAgICAgICBxeGNvZGU6ICdxeDIwMjIwMTEzJywKICAgICAgICBxeGxldmVsOiAn5LiA6IisQycsCiAgICAgICAgcXhkZXNjOiAiMeWPt+S4u+WPmEHnm7jmnInovb3osIPljovnk6bmlq/nu6fnlLXlmajkuK3mnInmsJTkvZMiCiAgICAgIH0sIHsKICAgICAgICBxeGNvZGU6ICdxeDIwMjIwMTEzJywKICAgICAgICBxeGxldmVsOiAn5LiA6IisQycsCiAgICAgICAgcXhkZXNjOiAiMeWPt+S4u+WPmEHnm7jmnInovb3osIPljovnk6bmlq/nu6fnlLXlmajkuK3mnInmsJTkvZMiCiAgICAgIH0sIHsKICAgICAgICBxeGNvZGU6ICdxeDIwMjIwMTEzJywKICAgICAgICBxeGxldmVsOiAn5LiA6IisQycsCiAgICAgICAgcXhkZXNjOiAiMeWPt+S4u+WPmEHnm7jmnInovb3osIPljovnk6bmlq/nu6fnlLXlmajkuK3mnInmsJTkvZMiCiAgICAgIH0sIHsKICAgICAgICBxeGNvZGU6ICdxeDIwMjIwMTEzJywKICAgICAgICBxeGxldmVsOiAn5LiA6IisQycsCiAgICAgICAgcXhkZXNjOiAiMeWPt+S4u+WPmEHnm7jmnInovb3osIPljovnk6bmlq/nu6fnlLXlmajkuK3mnInmsJTkvZMiCiAgICAgIH0sIHsKICAgICAgICBxeGNvZGU6ICdxeDIwMjIwMTEzJywKICAgICAgICBxeGxldmVsOiAn5LiA6IisQycsCiAgICAgICAgcXhkZXNjOiAiMeWPt+S4u+WPmEHnm7jmnInovb3osIPljovnk6bmlq/nu6fnlLXlmajkuK3mnInmsJTkvZMiCiAgICAgIH0sIHsKICAgICAgICBxeGNvZGU6ICdxeDIwMjIwMTEzJywKICAgICAgICBxeGxldmVsOiAn5LiA6IisQycsCiAgICAgICAgcXhkZXNjOiAiMeWPt+S4u+WPmEHnm7jmnInovb3osIPljovnk6bmlq/nu6fnlLXlmajkuK3mnInmsJTkvZMiCiAgICAgIH0sIHsKICAgICAgICBxeGNvZGU6ICdxeDIwMjIwMTEzJywKICAgICAgICBxeGxldmVsOiAn5LiA6IisQycsCiAgICAgICAgcXhkZXNjOiAiMeWPt+S4u+WPmEHnm7jmnInovb3osIPljovnk6bmlq/nu6fnlLXlmajkuK3mnInmsJTkvZMiCiAgICAgIH0sIHsKICAgICAgICBxeGNvZGU6ICdxeDIwMjIwMTEzJywKICAgICAgICBxeGxldmVsOiAn5LiA6IisQycsCiAgICAgICAgcXhkZXNjOiAiMeWPt+S4u+WPmEHnm7jmnInovb3osIPljovnk6bmlq/nu6fnlLXlmajkuK3mnInmsJTkvZMiCiAgICAgIH0sIHsKICAgICAgICBxeGNvZGU6ICdxeDIwMjIwMTEzJywKICAgICAgICBxeGxldmVsOiAn5LiA6IisQycsCiAgICAgICAgcXhkZXNjOiAiMeWPt+S4u+WPmEHnm7jmnInovb3osIPljovnk6bmlq/nu6fnlLXlmajkuK3mnInmsJTkvZMiCiAgICAgIH0sIHsKICAgICAgICBxeGNvZGU6ICdxeDIwMjIwMTEzJywKICAgICAgICBxeGxldmVsOiAn5LiA6IisQycsCiAgICAgICAgcXhkZXNjOiAiMeWPt+S4u+WPmEHnm7jmnInovb3osIPljovnk6bmlq/nu6fnlLXlmajkuK3mnInmsJTkvZMiCiAgICAgIH1dLAogICAgICAvL+WIoOmZpOaYr+WQpuWPr+eUqAogICAgICBtdWx0aXBsZVNlbnNvcjogdHJ1ZSwKICAgICAgLy/mn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICByb2xlS2V5OiAnJywKICAgICAgICByb2xlTmFtZTogJycsCiAgICAgICAgc3RhdHVzOiAnJwogICAgICB9LAogICAgICBzaG93U2VhcmNoOiB0cnVlCiAgICB9OwogIH0sCiAgd2F0Y2g6IHt9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldFBhZ2VEYXRhTGlzdCgpOwogIH0sCiAgbWV0aG9kczogewogICAgLyoqCiAgICAgKiDmn6Xor6LmlbDmja7liJfooagKICAgICAqLwogICAgZ2V0UGFnZURhdGFMaXN0OiBmdW5jdGlvbiBnZXRQYWdlRGF0YUxpc3QoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CgogICAgICAoMCwgX3F4YnprLmdldFBhZ2VEYXRhTGlzdCkodGhpcy5xdWVyeVBhcmFtcykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXMudGFibGVBbmRQYWdlSW5mby50YWJsZURhdGEgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLy/mt7vliqDmjInpkq4KICAgIGFkZFNlbnNvckJ1dHRvbjogZnVuY3Rpb24gYWRkU2Vuc29yQnV0dG9uKCkgewogICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICAvL+avj+mhteWxleekuuaVsOmHj+eCueWHu+S6i+S7tgogICAgaGFuZGxlU2l6ZUNoYW5nZTogZnVuY3Rpb24gaGFuZGxlU2l6ZUNoYW5nZSgpIHt9LAogICAgLy/pobXnoIHmlLnlj5jkuovku7YKICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZUN1cnJlbnRDaGFuZ2UoKSB7fSwKICAgIC8v5qCR54K55Ye75LqL5Lu2CiAgICBoYW5kbGVOb2RlQ2xpY2s6IGZ1bmN0aW9uIGhhbmRsZU5vZGVDbGljaygpIHt9LAogICAgLy/nvLrpmbfmoIflh4blupPmlrDlop7lrozmiJAKICAgIHF4Y29tbWl0OiBmdW5jdGlvbiBxeGNvbW1pdCgpIHsKICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlOwogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOwogICAgfQogIH0KfTsKZXhwb3J0cy5kZWZhdWx0ID0gX2RlZmF1bHQ7"}, {"version": 3, "sources": ["qxbzk.vue"], "names": [], "mappings": ";;;;;;;AAqGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,OAAA,EAAA;AADA,SADA;AAIA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,aAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,aAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,aAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SALA;AAJA,OADA;AAaA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CAEA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AACA,UAAA,IAAA,EAAA,WADA;AAEA,UAAA,KAAA,EAAA,IAFA;AAGA,UAAA,QAAA,EAAA,OAHA;AAIA,UAAA,KAAA,EAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WAJA;AAKA;AACA,UAAA,KAAA,EAAA,OANA;AAOA,UAAA,SAAA,EAAA,CACA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WADA,EAEA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WAFA;AAPA,SALA;AAZA,OAbA;AA4CA;AACA,MAAA,iBAAA,EAAA,KA7CA;AA8CA;AACA,MAAA,IAAA,EAAA,EA/CA;AAmDA,MAAA,OAAA,EAAA,KAnDA;AAoDA;AACA,MAAA,WAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA;AADA,OADA,EAGA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,QAAA,EAAA,CAAA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,QAAA,EAAA,CAAA;AACA,YAAA,KAAA,EAAA;AADA,WAAA,EAGA;AACA,YAAA,KAAA,EAAA;AADA,WAHA,EAMA;AACA,YAAA,KAAA,EAAA;AADA,WANA,EASA;AACA,YAAA,KAAA,EAAA;AADA,WATA;AAFA,SAAA;AAFA,OAHA,EAqBA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,QAAA,EAAA,CAAA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,QAAA,EAAA,CAAA;AACA,YAAA,KAAA,EAAA;AADA,WAAA,EAGA;AACA,YAAA,KAAA,EAAA;AADA,WAHA,EAMA;AACA,YAAA,KAAA,EAAA;AADA,WANA,EASA;AACA,YAAA,KAAA,EAAA;AADA,WATA;AAFA,SAAA;AAFA,OArBA,EAuCA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,QAAA,EAAA,CAAA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,QAAA,EAAA,CAAA;AACA,YAAA,KAAA,EAAA;AADA,WAAA,EAGA;AACA,YAAA,KAAA,EAAA;AADA,WAHA,EAMA;AACA,YAAA,KAAA,EAAA;AADA,WANA,EASA;AACA,YAAA,KAAA,EAAA;AADA,WATA;AAFA,SAAA;AAFA,OAvCA,EAyDA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,QAAA,EAAA,CAAA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,QAAA,EAAA,CAAA;AACA,YAAA,KAAA,EAAA;AADA,WAAA,EAGA;AACA,YAAA,KAAA,EAAA;AADA,WAHA,EAMA;AACA,YAAA,KAAA,EAAA;AADA,WANA,EASA;AACA,YAAA,KAAA,EAAA;AADA,WATA;AAFA,SAAA;AAFA,OAzDA,CArDA;AAkIA,MAAA,QAAA,EAAA,CACA;AACA,QAAA,MAAA,EAAA,YADA;AAEA,QAAA,OAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OADA,EAKA;AACA,QAAA,MAAA,EAAA,YADA;AAEA,QAAA,OAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OALA,EASA;AACA,QAAA,MAAA,EAAA,YADA;AAEA,QAAA,OAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OATA,EAaA;AACA,QAAA,MAAA,EAAA,YADA;AAEA,QAAA,OAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAbA,EAkBA;AACA,QAAA,MAAA,EAAA,YADA;AAEA,QAAA,OAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAlBA,EAuBA;AACA,QAAA,MAAA,EAAA,YADA;AAEA,QAAA,OAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAvBA,EA4BA;AACA,QAAA,MAAA,EAAA,YADA;AAEA,QAAA,OAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OA5BA,EAiCA;AACA,QAAA,MAAA,EAAA,YADA;AAEA,QAAA,OAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAjCA,EAsCA;AACA,QAAA,MAAA,EAAA,YADA;AAEA,QAAA,OAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAtCA,EA0CA;AACA,QAAA,MAAA,EAAA,YADA;AAEA,QAAA,OAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OA1CA,EA8CA;AACA,QAAA,MAAA,EAAA,YADA;AAEA,QAAA,OAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OA9CA,EAkDA;AACA,QAAA,MAAA,EAAA,YADA;AAEA,QAAA,OAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAlDA,EAsDA;AACA,QAAA,MAAA,EAAA,YADA;AAEA,QAAA,OAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAtDA,EA0DA;AACA,QAAA,MAAA,EAAA,YADA;AAEA,QAAA,OAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OA1DA,EA8DA;AACA,QAAA,MAAA,EAAA,YADA;AAEA,QAAA,OAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OA9DA,EAkEA;AACA,QAAA,MAAA,EAAA,YADA;AAEA,QAAA,OAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAlEA,EAsEA;AACA,QAAA,MAAA,EAAA,YADA;AAEA,QAAA,OAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAtEA,EA0EA;AACA,QAAA,MAAA,EAAA,YADA;AAEA,QAAA,OAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OA1EA,EA8EA;AACA,QAAA,MAAA,EAAA,YADA;AAEA,QAAA,OAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OA9EA,EAkFA;AACA,QAAA,MAAA,EAAA,YADA;AAEA,QAAA,OAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAlFA,EAsFA;AACA,QAAA,MAAA,EAAA,YADA;AAEA,QAAA,OAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAtFA,EA0FA;AACA,QAAA,MAAA,EAAA,YADA;AAEA,QAAA,OAAA,EAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OA1FA,CAlIA;AAkOA;AACA,MAAA,cAAA,EAAA,IAnOA;AAoOA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,OAAA,EAAA,EAHA;AAIA,QAAA,QAAA,EAAA,EAJA;AAKA,QAAA,MAAA,EAAA;AALA,OArOA;AA4OA,MAAA,UAAA,EAAA;AA5OA,KAAA;AA+OA,GAlPA;AAmPA,EAAA,KAAA,EAAA,EAnPA;AAqPA,EAAA,OArPA,qBAqPA;AACA,SAAA,eAAA;AAEA,GAxPA;AAyPA,EAAA,OAAA,EAAA;AACA;;;AAGA,IAAA,eAJA,6BAIA;AAAA;;AACA,kCAAA,KAAA,WAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,KAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KARA;AAUA;AACA,IAAA,eAXA,6BAWA;AACA,WAAA,iBAAA,GAAA,IAAA;AAEA,KAdA;AAeA;AACA,IAAA,gBAhBA,8BAgBA,CAEA,CAlBA;AAmBA;AACA,IAAA,mBApBA,iCAoBA,CAEA,CAtBA;AAuBA;AACA,IAAA,eAxBA,6BAwBA,CAEA,CA1BA;AA2BA;AACA,IAAA,QA5BA,sBA4BA;AACA,WAAA,iBAAA,GAAA,KAAA;AACA,WAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA;AA/BA;AAzPA,C", "sourcesContent": ["<template>\n  <div>\n    <!--左侧树组件-->\n    <el-row>\n      <el-col :span=\"4\">\n        <el-card class=\"box-card aside_height\" shadow=\"never\">\n          <div slot=\"header\" class=\"clearfix\" style=\"text-align: center\">\n            <span >设备</span>\n          </div>\n          <div style=\" overflow: auto;height: 100vh\">\n            <el-tree :expand-on-click-node=\"false\"\n                     id=\"tree\"\n                     :data=\"treeOptions\"\n                     :default-expanded-keys=\"['1']\"\n                     @node-click=\"handleNodeClick\"\n                     node-key=\"nodeId\"/>\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-white>\n          <el-filter\n            :data=\"filterInfo.data\"\n            :field-list=\"filterInfo.fieldList\"\n            @handleReset=\"getReset\"\n          />\n        </el-white>\n        <div>\n            <el-white class=\"button-group\">\n              <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"addSensorButton\"\n              >新增\n              </el-button>\n<!--              <el-button type=\"warning\" icon=\"el-icon-edit\" @click=\"getUpdateSbflwh\"-->\n<!--              >修改-->\n<!--              </el-button>-->\n              <el-button type=\"danger\" icon=\"el-icon-delete\" :disabled=\"multipleSensor\" @click=\"getDeleteSbflwh\"\n              >删除\n              </el-button>\n            </el-white>\n          <comp-table :table-and-page-info=\"tableAndPageInfo\" @multipleSelection=\"handleSelectionChange\"/>\n\n        </div>\n      </el-col>\n    </el-row>\n    <!-- 弹出框开始 -->\n    <el-dialog title=\"缺陷录入\" :visible.sync=\"dialogFormVisible\" width=\"50%\" v-dialogDrag>\n      <el-form :model=\"form\" label-width=\"130px\" :inline=\"true\">\n        <el-row :gutter=\"8\" class=\"pull-left\" >\n          <el-col :span=\"12\">\n            <el-form-item label=\"分类依据：\" >\n              <el-select v-model=\"form.flyj\" placeholder=\"请选择活动区域\" style=\"width: 100%\">\n                <el-option label=\"XXXX发生损坏\" value=\"1\"></el-option>\n                <el-option label=\"XXX发生渗油\" value=\"2\"></el-option>\n                <el-option label=\"XXX出现断裂\" value=\"3\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"标准描述：\" >\n              <el-select v-model=\"form.bzms\" placeholder=\"标准描述\" style=\"width: 100%\">\n                <el-option label=\"进水或进雪\" value=\"4\"></el-option>\n                <el-option label=\"密封不良\" value=\"5\"></el-option>\n                <el-option label=\"受潮\" value=\"6\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"隐患等级：\" >\n              <el-select v-model=\"form.qxdj\" placeholder=\"请选择活动区域\" style=\"width: 100%\">\n                <el-option label=\"一般C\" value=\"7\"></el-option>\n                <el-option label=\"一般B\" value=\"8\"></el-option>\n                <el-option label=\"一般A\" value=\"9\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"原因类别：\" >\n              <el-select v-model=\"form.yylb\" placeholder=\"请选择活动区域\" style=\"width: 100%\">\n                <el-option label=\"机械部件\" value=\"10\"></el-option>\n                <el-option label=\"本地\" value=\"11\"></el-option>\n                <el-option label=\"其它\" value=\"12\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"qxcommit\" class=\"pmyBtn\">确 定</el-button>\n      </div>\n    </el-dialog>\n    <!-- 弹出框结束 -->\n  </div>\n\n\n</template>\n\n<script>\n\n  import { getPageDataList } from '@/api/dagangOilfield/bzgl/qxbzk'\n\n  export default {\n    name: \"qxbzk\",\n    data() {\n      return {\n        filterInfo: {\n          data: {\n            ywdwArr: [],\n          },\n          fieldList: [\n            {label: '隐患等级', type: 'select', value: 'defectLevel', multiple: true, options: []},\n            {label: '标准描述', type: 'input', value: 'defectLevel'},\n            {label: '设备状态', type: 'select', value: 'defectLevel', multiple: true, options: []},\n            {label: '电压等级', type: 'select', value: 'defectLevel', multiple: true, options: []},\n            {label: '维护班组', type: 'select', value: 'defectLevel'},\n          ]\n        },\n        tableAndPageInfo: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n\n            {prop: 'sbzt', label: '缺陷编码', minWidth: '120'},\n            {prop: 'sbzt', label: '隐患等级', minWidth: '120'},\n            {prop: 'sbzt', label: '标准描述', minWidth: '120'},\n            {\n              prop: 'operation',\n              label: '操作',\n              minWidth: '130px',\n              style: {display: 'block'},\n              //操作列固定再右侧\n              fixed:'right',\n              operation: [\n                {name: '详情', clickFun: this.getDetails},\n                {name: '修改', clickFun: this.getDetails},\n              ]\n            },\n          ]\n        },\n        //弹出框\n        dialogFormVisible:false,\n        //弹出框表单\n        form:{\n\n        },\n\n        loading: false,\n        //组织树\n        treeOptions:[\n          {\n          label: '断路器',\n        }, {\n          label: '1号变压器',\n          children: [{\n            label: '冷却系统',\n            children: [{\n              label: '温控运行情况',\n\n            }, {\n              label: '油箱',\n\n            }, {\n              label: '铁芯',\n\n            }, {\n              label: '绕组',\n\n            }]\n          }]\n        }, {\n            label: '2号变压器',\n            children: [{\n              label: '冷却系统',\n              children: [{\n                label: '温控运行情况',\n\n              }, {\n                label: '油箱',\n\n              }, {\n                label: '铁芯',\n\n              }, {\n                label: '绕组',\n\n              }]\n            }]\n          }, {\n            label: '3号变压器',\n            children: [{\n              label: '冷却系统',\n              children: [{\n                label: '温控运行情况',\n\n              }, {\n                label: '油箱',\n\n              }, {\n                label: '铁芯',\n\n              }, {\n                label: '绕组',\n\n              }]\n            }]\n          }, {\n            label: '4号变压器',\n            children: [{\n              label: '冷却系统',\n              children: [{\n                label: '温控运行情况',\n\n              }, {\n                label: '油箱',\n\n              }, {\n                label: '铁芯',\n\n              }, {\n                label: '绕组',\n\n              }]\n            }]\n          }\n        ],\n        userList:[\n          {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          },{\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          },{\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          },{\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          },\n          {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          },\n          {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          },\n          {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          },\n          {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          },\n          {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          }, {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          }, {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          }, {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          }, {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          }, {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          }, {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          }, {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          }, {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          }, {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          }, {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          }, {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          }, {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          }, {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          },\n        ],\n        //删除是否可用\n        multipleSensor: true,\n        //查询参数\n        queryParams:{\n          pageNum: 1,\n          pageSize: 10,\n          roleKey:'',\n          roleName:'',\n          status:'',\n        },\n        showSearch:true,\n\n      };\n    },\n    watch: {\n    },\n    created() {\n        this.getPageDataList();\n\n    },\n    methods: {\n      /**\n       * 查询数据列表\n       */\n      getPageDataList(){\n        getPageDataList(this.queryParams).then(res=>{\n            this.tableAndPageInfo.tableData=res.data\n        })\n      },\n\n      //添加按钮\n      addSensorButton(){\n        this.dialogFormVisible= true\n\n      },\n      //每页展示数量点击事件\n      handleSizeChange(){\n\n      },\n      //页码改变事件\n      handleCurrentChange(){\n\n      },\n      //树点击事件\n      handleNodeClick(){\n\n      },\n      //缺陷标准库新增完成\n      qxcommit(){\n        this.dialogFormVisible = false;\n        this.$message.success(\"新增成功\")\n      }\n\n    }\n  };\n</script>\n\n<style lang='scss' scoped>\n.head-container{\n  margin: 0 auto;\n  width: 98%;\n  height: calc(100vh - 225px);\n  overflow: auto;\n}\n.box-card{\n  margin-bottom: 15px;\n  .el-card__header{\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n.box-cardList{\n  height: 56%;\n}\n.item{\n  width: 200px;\n  float: left;\n}\n#main_container_dj{\n  height: calc(100vh - 84px);\n}\n.aside_height{\n  height: 96%;\n}\n.defect .el-form-item:nth-child(odd){\n  margin-right: 70px;\n}\n/*背景颜色调整*/\n#main_container_dj,#main_container_dj .el-aside{\n  background-color: #b4caf1;\n}\n/deep/ #qxlr_dialog_insert .el-dialog__header {\n  background-color: #8eb3f5;\n}\n/deep/ .pmyBtn{\n  background:#8eb3f5;\n}\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl"}]}