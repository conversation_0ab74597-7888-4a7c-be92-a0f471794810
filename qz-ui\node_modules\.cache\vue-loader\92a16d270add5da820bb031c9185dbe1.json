{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\pjcswh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\pjcswh.vue", "mtime": 1706897323222}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["pjcswh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "pjcswh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n<!--    <el-white>-->\n<!--      <el-filter-->\n<!--        :data=\"filterInfo.data\"-->\n<!--        :field-list=\"filterInfo.fieldList\"-->\n<!--        @handleReset=\"getReset\"-->\n<!--      />-->\n<!--    </el-white>-->\n    <div>\n      <el-white class=\"button-group\">\n        <div class=\"button_btn\">\n          <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"getInster\"\n          >新增</el-button>\n          <el-button type=\"danger\" icon=\"el-icon-delete\"  @click=\"getDelete\"\n          >删除</el-button>\n          <!--              <el-button type=\"warning\" icon=\"el-icon-edit\" @click=\"getUpdate\"-->\n          <!--              >修改</el-button>-->\n        </div>\n        <comp-table :table-and-page-info=\"tableAndPageInfo\" @multipleSelection=\"handleSelectionChange\"/>\n      </el-white>\n    </div>\n\n    <!--新增、修改、详情弹框-->\n    <el-dialog :title=\"title\" :visible.sync=\"show\" width=\"50%\" append-to-body @close=\"getInsterClose\" v-dialogDrag>\n      <el-form label-width=\"130px\" ref=\"form\" :model=\"form\">\n        <el-row :gutter=\"8\" class=\"pull-left\" >\n          <el-col :span=\"12\">\n            <el-form-item  label=\"评价导则：\" prop=\"scjxlb\">\n              <el-select placeholder=\"请选择评价导则\" v-model=\"form.scjxlb\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item  label=\"参数名称：\" prop=\"jyjxlb\">\n              <el-select placeholder=\"请选择参数名称\" v-model=\"form.jyjxlb\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item  label=\"参数编码：\" prop=\"jyjxlb\">\n              <el-select placeholder=\"请选择参数编码\" v-model=\"form.jyjxlb\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item  label=\"参数类型：\" prop=\"jyjxlb\">\n              <el-select placeholder=\"请选择参数类型\" v-model=\"form.jyjxlb\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item  label=\"计量单位：\" prop=\"jyjxlb\">\n              <el-select placeholder=\"请选择计量单位\" v-model=\"form.jyjxlb\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"getInsterClose\">关 闭</el-button>\n        <el-button type=\"primary\" >保 存</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n    export default {\n      name: \"pjcswh\",\n      data(){\n        return{\n          //新增按钮form表单\n          form:{\n          },\n          title:'',\n          show:false,\n          filterInfo: {\n            data: {\n              ywdwArr: [],\n            },\n            fieldList: [\n              {label: '设备类型', type: 'select', value: 'roleName', multiple: true, options: []},\n            ]\n          },\n          tableAndPageInfo: {\n            pager: {\n              pageSize: 10,\n              pageNum: 1,\n              total: 0,\n              sizes: [10, 20, 50, 100]\n            },\n            option: {\n              checkBox: true,\n              serialNumber: true\n            },\n            tableData: [],\n            tableHeader: [\n              {prop: 'ssgs', label: '评价导则', minWidth: '120'},\n              {prop: 'bdzmc', label: '参数名称', minWidth: '180'},\n              {prop: 'dydj', label: '参数编码', minWidth: '120'},\n              {prop: 'sbzt', label: '参数类型', minWidth: '250'},\n              {prop: 'sbzt', label: '计量单位', minWidth: '250'},\n              {\n                prop: 'operation',\n                label: '操作',\n                minWidth: '130px',\n                style: {display: 'block'},\n                //操作列固定再右侧\n                fixed:'right',\n                operation: [\n                  {name: '修改', clickFun: this.getDetails},\n                  {name: '详情', clickFun: this.getDetails},\n                ]\n              },\n            ]\n          },\n        }\n      },\n      create(){\n\n      },\n\n      mounted(){\n\n      },\n      methods:{\n        getDetails(){\n          this.title='详情'\n          this.show=true\n        },\n\n        getXzsbpjClose(){\n          this.show=false\n          //设置table切换默认进来显示那个table\n          this.activeName = 'first'\n        },\n        //新增按钮\n        getInster(){\n          this.show=true\n          this.title = '新增'\n        },\n        //新增弹框关闭\n        getInsterClose(){\n          this.show=false\n        },\n\n      }\n    }\n</script>\n\n<style scoped>\n\n</style>\n"]}]}