{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\bdgl\\bdztz.vue?vue&type=template&id=63e3f3de&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\bdgl\\bdztz.vue", "mtime": 1706897324520}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}