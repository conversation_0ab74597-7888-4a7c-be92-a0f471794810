{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\xqxbzk.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\xqxbzk.vue", "mtime": 1706897323967}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsKaW1wb3J0IHsgZ2V0UGFnZURhdGFMaXN0LCByZW1vdmUsIHNhdmVPclVwZGF0ZSB9IGZyb20gJ0AvYXBpL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvcXhiemsnCmltcG9ydCB7IGdldERpY3RUeXBlRGF0YSB9IGZyb20gJ0AvYXBpL3N5c3RlbS9kaWN0L2RhdGEnCmltcG9ydCBEZXZpY2VUcmVlIGZyb20gJ0Avdmlld3MvZGFnYW5nT2lsZmllbGQvYnpnbC9zYmJ6ay9kZXZpY2VUcmVlJwppbXBvcnQgeyBnZXRFcXVpcG1lbnRDb21wb25lbnRzT3B0aW9ucyB9IGZyb20gJ0AvYXBpL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvc2JseHdoL3NiYmonCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ3hxeGJ6aycsCiAgY29tcG9uZW50czogeyBEZXZpY2VUcmVlIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0RGF0YSgpCiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy/kuIrkvKDlm77niYfml7bnmoTor7fmsYLlpLQKICAgICAgaGVhZGVyOiB7fSwKICAgICAgZmlsdGVySW5mbzogewogICAgICAgIGRhdGE6IHsKICAgICAgICAgIHNiYm06IiIsCiAgICAgICAgICBzYmJ3OiIiLAogICAgICAgICAgcXhtczoiIiwKICAgICAgICAgIGZseWo6IiIsCiAgICAgICAgICBqc3l5OiIiLAogICAgICAgICAgZHlzYmx4OiAnJywgIC8v6K6+5aSH57G75Z6L5ZCN56ewCiAgICAgICAgICBzYmx4OiIiCiAgICAgICAgfSwKICAgICAgICBmaWVsZExpc3Q6IFsKICAgICAgICAgIHsgbGFiZWw6ICforr7lpIfnp43nsbsnLCB0eXBlOiAnaW5wdXQnLCB2YWx1ZTogJ2R5c2JseCcgfSwKICAgICAgICAgIHsgbGFiZWw6ICforr7lpIfpg6jku7YnLCB0eXBlOiAnaW5wdXQnLCB2YWx1ZTogJ3NiYm0nIH0sCiAgICAgICAgICB7IGxhYmVsOiAn6K6+5aSH6YOo5L2NJywgdHlwZTogJ2lucHV0JywgdmFsdWU6ICdzYmJ3JyB9LAogICAgICAgICAgeyBsYWJlbDogJ+e8uumZt+aPj+i/sCcsIHR5cGU6ICdpbnB1dCcsIHZhbHVlOiAncXhtcycgfSwKICAgICAgICAgIHsgbGFiZWw6ICfliIbnsbvkvp3mja4nLCB0eXBlOiAnaW5wdXQnLCB2YWx1ZTogJ2ZseWonIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAn6ZqQ5oKj562J57qnJywKICAgICAgICAgICAgdHlwZTogJ3NlbGVjdCcsCiAgICAgICAgICAgIHZhbHVlOiAncXhkakxpc3QnLAogICAgICAgICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgICAgICAgb3B0aW9uczogW3sgbGFiZWw6ICfkuIDoiKwnLCB2YWx1ZTogJ+S4gOiIrCcgfSwgeyBsYWJlbDogJ+S4pemHjScsIHZhbHVlOiAn5Lil6YeNJyB9LCB7IGxhYmVsOiAn5Y2x5oClJywgdmFsdWU6ICfljbHmgKUnIH1dCiAgICAgICAgICB9LAogICAgICAgICAgeyBsYWJlbDogJ+aKgOacr+WOn+WboCcsIHR5cGU6ICdpbnB1dCcsIHZhbHVlOiAnanN5eScgfSwKCiAgICAgICAgXQogICAgICB9LAogICAgICB0YWJsZUFuZFBhZ2VJbmZvOiB7CiAgICAgICAgcGFnZXI6IHsKICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgIHNpemVzOiBbMTAsIDIwLCA1MCwgMTAwXQogICAgICAgIH0sCiAgICAgICAgb3B0aW9uOiB7CiAgICAgICAgICBjaGVja0JveDogdHJ1ZSwKICAgICAgICAgIHNlcmlhbE51bWJlcjogdHJ1ZQogICAgICAgIH0sCiAgICAgICAgdGFibGVEYXRhOiBbXSwKICAgICAgICB0YWJsZUhlYWRlcjogWwogICAgICAgICAgLy8geyBwcm9wOiAnenltYycsIGxhYmVsOiAn5LiT5LiaJywgbWluV2lkdGg6ICcxMjAnIH0sCiAgICAgICAgICB7IHByb3A6ICdzYmx4bWMnLCBsYWJlbDogJ+iuvuWkh+enjeexuycsIG1pbldpZHRoOiAnMTgwJyB9LAogICAgICAgICAgeyBwcm9wOiAnc2JibScsIGxhYmVsOiAn6K6+5aSH6YOo5Lu2JywgbWluV2lkdGg6ICcxMjAnIH0sCiAgICAgICAgICB7IHByb3A6ICdzYmJ3JywgbGFiZWw6ICforr7lpIfpg6jkvY0nLCBtaW5XaWR0aDogJzEyMCcgfSwKICAgICAgICAgIHsgcHJvcDogJ3F4bXMnLCBsYWJlbDogJ+e8uumZt+aPj+i/sCcsIG1pbldpZHRoOiAnMTIwJyB9LAogICAgICAgICAgeyBwcm9wOiAnZmx5aicsIGxhYmVsOiAn5YiG57G75L6d5o2uJywgbWluV2lkdGg6ICcxMjAnIH0sCiAgICAgICAgICB7IHByb3A6ICdxeGRqJywgbGFiZWw6ICfpmpDmgqPnrYnnuqcnLCBtaW5XaWR0aDogJzEyMCcgfSwKICAgICAgICAgIHsgcHJvcDogJ2pzeXknLCBsYWJlbDogJ+aKgOacr+WOn+WboCcsIG1pbldpZHRoOiAnMTIwJyB9LAogICAgICAgICAvKiB7CiAgICAgICAgICAgIHByb3A6ICdvcGVyYXRpb24nLAogICAgICAgICAgICBsYWJlbDogJ+aTjeS9nCcsCiAgICAgICAgICAgIG1pbldpZHRoOiAnMTMwcHgnLAogICAgICAgICAgICBzdHlsZTogeyBkaXNwbGF5OiAnYmxvY2snIH0sCiAgICAgICAgICAgIC8v5pON5L2c5YiX5Zu65a6a5YaN5Y+z5L6nCiAgICAgICAgICAgIGZpeGVkOiAncmlnaHQnLAogICAgICAgICAgICBvcGVyYXRpb246IFsKICAgICAgICAgICAgICB7IG5hbWU6ICfkv67mlLknLCBjbGlja0Z1bjogdGhpcy5nZXREZXRhaWxzIH0sCiAgICAgICAgICAgICAgeyBuYW1lOiAn6K+m5oOFJywgY2xpY2tGdW46IHRoaXMuZ2V0RGV0YWlsc0luZm8gfQoKICAgICAgICAgICAgXQogICAgICAgICAgfSovCiAgICAgICAgXQogICAgICB9LAogICAgICAvLyDmmK/lkKbmmL7npLrnrZvpgInmnaHku7YKICAgICAgaXNTZWFyY2hTaG93OiBmYWxzZSwKICAgICAgLy/mn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBxeGRqTGlzdDogW10sCiAgICAgICAgc2JibToiIiwKICAgICAgICBzYmJ3OiIiLAogICAgICAgIHF4bXM6IiIsCiAgICAgICAgZmx5ajoiIiwKICAgICAgICBqc3l5OiIiLAogICAgICAgIGR5c2JseDogJycsICAvL+iuvuWkh+exu+Wei+WQjeensAogICAgICAgIHNibHg6IiIKCiAgICAgIH0sCiAgICAgIC8vIOS4k+S4muS4i+aLieahhuaVsOaNrgogICAgICBzcGVjaWFsdHlPcHRpb25zOiBbXSwKICAgICAgLy8g6YOo5Lu25LiL5ouJ5qGG5pWw5o2uCiAgICAgIHBhcnRzT3B0aW9uczogW10sCgogICAgICAvLyDpmpDmgqPnrYnnuqfkuIvmi4nmoYbmlbDmja4KICAgICAgZGVmZWN0TGV2ZWxPcHRpb25zOiBbeyB2YWx1ZTogJ+S4gOiIrCcsIGxhYmVsOiAn5LiA6IisJyB9LCB7IHZhbHVlOiAn5Lil6YeNJywgbGFiZWw6ICfkuKXph40nIH0sIHsgdmFsdWU6ICfljbHmgKUnLCBsYWJlbDogJ+WNseaApScgfV0sCiAgICAgIC8vIOWkmumAieahhumAieS4reeahOaVsOaNrmlk5YiX6KGoCiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5qCH5YeG57y66Zm35bqT6KGo5qC85pWw5o2uCgogICAgICAvLyDlr7nor53moYbmoIfpopgKICAgICAgdGl0bGU6ICcnLAogICAgICAvLyDmlrDlop4v5L+u5pS5L+ivpuaDheWvueivneahhuaYr+WQpuaJk+W8gAogICAgICBvcGVuOiBmYWxzZSwKICAgICAgLy8g5LiK5Lyg6ZmE5Lu25a+56K+d5qGG5piv5ZCm5omT5byACiAgICAgIHVwbG9hZERpYWxvZ09wZW46IGZhbHNlLAogICAgICAvLyDmlrDlop4v5L+u5pS55o+Q5Lqk55qE6KGo5Y2VCiAgICAgIGZvcm06IHsKICAgICAgICBvYmpJZDogJycsCiAgICAgICAgenk6IHVuZGVmaW5lZCwKICAgICAgICBzYnpsOiB1bmRlZmluZWQsCiAgICAgICAgc2JiajogdW5kZWZpbmVkLAogICAgICAgIHF4Ync6IHVuZGVmaW5lZCwKICAgICAgICBxeG1zOiB1bmRlZmluZWQsCiAgICAgICAgcXhkajogdW5kZWZpbmVkLAogICAgICAgIGpzeXk6IHVuZGVmaW5lZCwKICAgICAgICB6cnl5OiB1bmRlZmluZWQsCiAgICAgICAgY3JlYXRlVGltZTogdW5kZWZpbmVkLAogICAgICAgIHNibHg6ICcnLAogICAgICAgIHNibHhtYzogJycsCiAgICAgICAgZmx5ajogdW5kZWZpbmVkLAogICAgICAgIHNiYm06ICcnCiAgICAgIH0sCiAgICAgIC8vIOaWsOWini/kv67mlLnml7blr7nor53moYbnu4Tku7blj6/nvJbovpHvvIzor6bmg4Xml7bkuI3lj6/nvJbovpEKICAgICAgZm9ybUlzRWRpdGFibGU6IGZhbHNlLAogICAgICAvLyDmipjlj6DpnaLmnb/lsZXlvIDnmoTpnaLmnb8KICAgICAgYWN0aXZlTmFtZXM6IFsnYmFzZUluZm8nXSwKICAgICAgLy8g5paH5Lu25LiK5Lyg5pWw5o2uCiAgICAgIHVwbG9hZERhdGE6IHt9LAogICAgICAvLyDmlofku7bkuIrkvKDor7fmsYLlpLQKICAgICAgdXBIZWFkZXI6IHt9LAogICAgICAvLyDooajmoLzlhbHmn6Xor6Llh7rnmoTmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8v6YOo5Lu25piv5ZCm5Y+v57yW6L6RCiAgICAgIHBhcnREaXNhYmxlZDogZmFsc2UsCiAgICAgIC8v5bGV56S66K6+5aSH5YiG57G75qCRCiAgICAgIHNob3dEZXZpY2VUcmVlOiBmYWxzZSwKICAgICAgaXNGaWx0ZXI6IGZhbHNlLAogICAgICBydWxlczogewogICAgICAgIHNibHhtYzogWwogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K6+5aSH56eN57G75LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIifSwKICAgICAgICBdLAogICAgICAgIHNiYm06IFsKICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuiuvuWkh+mDqOS7tiIsIHRyaWdnZXI6ICJibHVyIn0sCiAgICAgICAgXSwKICAgICAgICBzYmJ3OiBbCiAgICAgICAgICB7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLorr7lpIfpg6jkvY3kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciJ9LAogICAgICAgIF0sCiAgICAgICAgcXhkajogWwogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6ZqQ5oKj562J57qn5LiN6IO95Li656m6IiwgdHJpZ2dlcjogInNlbGVjdCJ9LAogICAgICAgIF0sCiAgICAgICAgcXhtczogWwogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi57y66Zm35o+P6L+w5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIifSwKICAgICAgICBdLAogICAgICAgIGZseWo6IFsKICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWIhuexu+S+neaNruS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIn0sCiAgICAgICAgXSwKICAgICAgICBqc3l5OiBbCiAgICAgICAgICB7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmioDmnK/ljp/lm6DkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciJ9LAogICAgICAgIF0sCiAgICAgIH0sCiAgICB9CiAgfSwKICBtb3VudGVkKCkgewogICAgLy/ojrflj5Z0b2tlbgogICAgdGhpcy5oZWFkZXIudG9rZW4gPSBnZXRUb2tlbigpOwogICAgdGhpcy5pbml0RG9tYWluKCkKICB9LAogIG1ldGhvZHM6IHsKICAgIC8v562b6YCJ5p2h5Lu26byg5qCH6IGa54Sm5LqL5Lu2CiAgICBpbnB1dEZvY3VzRXZlbnQodmFsKSB7CiAgICAgIGlmICh2YWwudGFyZ2V0Lm5hbWUgPT09ICdkeXNibHgnKSB7CiAgICAgICAgdGhpcy5zaG93RGV2aWNlVHJlZSA9IHRydWUKICAgICAgICB0aGlzLmlzRmlsdGVyID0gdHJ1ZQogICAgICB9CiAgICB9LAoKICAgIC8qKgogICAgICog5p+l6K+i5pWw5o2uCiAgICAgKi8KICAgIGFzeW5jIGdldERhdGEocGFyYW1zKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXM9ey4uLnRoaXMucXVlcnlQYXJhbXMsLi4ucGFyYW1zfQogICAgICBjb25zdCBwYXJhbSA9IHsuLi50aGlzLnF1ZXJ5UGFyYW1zLCAuLi5wYXJhbXN9CiAgICAgIGNvbnN0IHtkYXRhLCBjb2RlfSA9IGF3YWl0IGdldFBhZ2VEYXRhTGlzdChwYXJhbSk7CiAgICAgIGlmIChjb2RlID09PSAnMDAwMCcpIHsKICAgICAgICBjb25zb2xlLmxvZyhkYXRhKQogICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby50YWJsZURhdGEgPSBkYXRhLnJlY29yZHMKICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIudG90YWwgPSBkYXRhLnRvdGFsCiAgICAgIH0KICAgIH0sCiAgICBnZXREZXRhaWxzKHJvdykgewogICAgICB0aGlzLm9wZW4gPSB0cnVlCiAgICAgIHRoaXMuZm9ybUlzRWRpdGFibGUgPSB0cnVlCiAgICAgIHRoaXMucGFydERpc2FibGVkID0gZmFsc2UKICAgICAgLy8gaWYgKHJvdy5zYmx4ICE9PSAnJyAmJiByb3cuc2JseCAhPT0gbnVsbCkgewogICAgICAvLyAgIHRoaXMuZ2V0UGFydE9wdGlvbnMocm93LnNibHgpCiAgICAgIC8vIH0KICAgICAgdGhpcy5mb3JtID0gcm93CiAgICB9LAogICAgZ2V0RGV0YWlsc0luZm8ocm93KSB7CiAgICAgIHRoaXMub3BlbiA9IHRydWUKICAgICAgdGhpcy5mb3JtSXNFZGl0YWJsZSA9IGZhbHNlCiAgICAgIHRoaXMucGFydERpc2FibGVkID0gdHJ1ZQogICAgICB0aGlzLmZvcm0gPSByb3cKICAgIH0sCiAgICAvKioKICAgICAqIOihqOagvOWkmumAieahhgogICAgICovCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0ub2JqSWQpCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgKICAgIH0sCgogICAgLy8gLyoqCiAgICAvLyAgKiDmn6Xor6LmjInpkq4KICAgIC8vICAqLwogICAgLy8gaGFuZGxlUXVlcnkoKSB7CiAgICAvLyAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDEKICAgIC8vICAgdGhpcy5nZXREYXRhKCkKICAgIC8vIH0sCiAgICAvLwogICAgLy8gLyoqCiAgICAvLyAgKiDph43nva7mjInpkq4KICAgIC8vICAqLwogICAgLy8gcmVzZXRRdWVyeSgpIHsKICAgIC8vICAgdGhpcy5yZXNldEZvcm0oJ3F1ZXJ5Rm9ybScpCiAgICAvLyAgIHRoaXMuaGFuZGxlUXVlcnkoKQogICAgLy8gfSwKCiAgICAvKioKICAgICAqIOaWsOWinuaMiemSrgogICAgICovCiAgICBoYW5kbGVBZGQoKSB7CiAgICAgIC8vIOiuvue9ruW8ueWHuuahhuihqOWNleWPr+e8lui+kQogICAgICB0aGlzLmZvcm1Jc0VkaXRhYmxlID0gdHJ1ZQogICAgICB0aGlzLnRpdGxlID0gJ+aWsOWinuagh+WHhue8uumZtycKICAgICAgdGhpcy5vcGVuID0gdHJ1ZQogICAgfSwKCiAgICAvKioKICAgICAqIOS/ruaUueaMiemSrgogICAgICogQHBhcmFtIHJvdwogICAgICovCiAgICBoYW5kbGVFZGl0KHJvdykgewogICAgICAvLyDorr7nva7lvLnlh7rmoYbooajljZXlj6/nvJbovpEKICAgICAgdGhpcy5mb3JtSXNFZGl0YWJsZSA9IHRydWUKICAgICAgLy8g5bCG5L+u5pS56KGM55qE5pWw5o2u6LWL5YC85YiwZm9ybeS4igogICAgICBmb3IgKGxldCByb3dLZXkgaW4gcm93KSB7CiAgICAgICAgdGhpcy5mb3JtW3Jvd0tleV0gPSByb3dbcm93S2V5XQogICAgICB9CiAgICAgIHRoaXMudGl0bGUgPSAn5L+u5pS55qCH5YeG57y66Zm3JwogICAgICB0aGlzLm9wZW4gPSB0cnVlCiAgICB9LAoKICAgIC8qKgogICAgICog5aSE55CG5om56YeP5Yig6ZmkCiAgICAgKi8KICAgIGhhbmRsZURlbGV0ZSgpIHsKICAgICAgaWYgKHRoaXMuaWRzLmxlbmd0aCAhPT0gMCkgewogICAgICAgIHRoaXMuJGNvbmZpcm0oJ+atpOaTjeS9nOWwhuawuOS5heWIoOmZpOivpeaVsOaNriwg5piv5ZCm57un57utPycsICfmj5DnpLonLCB7CiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgICAgcmVtb3ZlKHRoaXMuaWRzKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICAgICAgICBtZXNzYWdlOiAn5Yig6Zmk5oiQ5YqfIScKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgfSkKICAgICAgICB9KQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ2luZm8nLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+mAieaLqeiHs+WwkeS4gOadoeaVsOaNriEnCiAgICAgICAgfSkKICAgICAgfQoKICAgIH0sCgogICAgLyoqCiAgICAgKiDlr7zlh7rmjInpkq4KICAgICAqLwogICAgaGFuZGxlRXhwb3J0KCkgewogICAgICBjb25zb2xlLmxvZygn5a+85Ye6JykKICAgIH0sCgogICAgLyoqCiAgICAgKiDlr7zlhaXmjInpkq4KICAgICAqLwogICAgaGFuZGxlSW1wb3J0KCkgewogICAgICB0aGlzLnVwbG9hZERpYWxvZ09wZW4gPSB0cnVlCiAgICB9LAoKICAgIC8qKgogICAgICog5p+l55yL6K+m5oOFCiAgICAgKiBAcGFyYW0gcm93CiAgICAgKi8KICAgIGhhbmRsZURldGFpbHMocm93KSB7CiAgICAgIHRoaXMuZm9ybUlzRWRpdGFibGUgPSBmYWxzZQogICAgICAvLyDlsIbooYzmlbDmja7otYvlgLzliLBmb3Jt5LiKCiAgICAgIGZvciAobGV0IHJvd0tleSBpbiByb3cpIHsKICAgICAgICB0aGlzLmZvcm1bcm93S2V5XSA9IHJvd1tyb3dLZXldCiAgICAgIH0KICAgICAgdGhpcy50aXRsZSA9ICfmoIflh4bnvLrpmbfor6bmg4UnCiAgICAgIHRoaXMub3BlbiA9IHRydWUKICAgIH0sCgogICAgLyoqCiAgICAgKiDlhbPpl63lr7nor53moYYKICAgICAqLwogICAgaGFuZGxlQ2xvc2UoKSB7CiAgICAgIHRoaXMucmVzZXQoKQogICAgICB0aGlzLm9wZW4gPSBmYWxzZQogICAgICB0aGlzLnRpdGxlID0gJycKICAgIH0sCgogICAgLyoqCiAgICAgKiDlhbPpl63kuIrkvKDpmYTku7blr7nor53moYYKICAgICAqLwogICAgdXBsb2FkQ2xvc2UoKSB7CiAgICAgIHRoaXMucmVzZXQoKQogICAgICB0aGlzLnVwbG9hZERpYWxvZ09wZW4gPSBmYWxzZQogICAgfSwKCiAgICAvKioKICAgICAqIOaPkOS6pOihqOWNlQogICAgICovCiAgICBzdWJtaXRGb3JtKCkgewogICAgICB0aGlzLiRyZWZzWydmb3JtJ10udmFsaWRhdGUoKHZhbGlkKSA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBzYXZlT3JVcGRhdGUodGhpcy5mb3JtKS50aGVuKChyZXNwb25zZSkgPT4gewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S/neWtmOaIkOWKn++8gScpCiAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpCiAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlCiAgICAgICAgICB9KQogICAgICAgIH1lbHNlewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5qCh6aqM5pyq6YCa6L+H77yBIik7CiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgfQogICAgICB9KQogICAgfSwKCiAgICAvKioKICAgICAqIOihqOWNlemHjee9rgogICAgICovCiAgICByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIGlkOiB1bmRlZmluZWQsCiAgICAgICAgbWFpbnRhaW5UeXBlOiB1bmRlZmluZWQsCiAgICAgICAgc3BlY2lhbHR5OiB1bmRlZmluZWQsCiAgICAgICAgZGV2aWNlVHlwZTogdW5kZWZpbmVkLAogICAgICAgIHBhcnRzOiB1bmRlZmluZWQsCiAgICAgICAgZGVmZWN0UGFydDogdW5kZWZpbmVkLAogICAgICAgIGRlZmVjdERlc2NyaXB0aW9uOiB1bmRlZmluZWQsCiAgICAgICAgZGVmZWN0R2lzdDogdW5kZWZpbmVkLAogICAgICAgIGRlZmVjdExldmVsOiB1bmRlZmluZWQsCiAgICAgICAgcmVhc29uOiB1bmRlZmluZWQsCiAgICAgICAgY3JlYXRlVGltZTogdW5kZWZpbmVkCiAgICAgIH0KICAgICAgdGhpcy5yZXNldEZvcm0oJ2Zvcm0nKQogICAgfSwKCiAgICAvKioKICAgICAqIOS4iuS8oOaWh+S7tuS5i+WJjeeahOWkhOeQhgogICAgICovCiAgICBiZWZvcmVVcGxvYWQoZmlsZSkgewogICAgICBjb25zdCBmaWxlU2l6ZSA9IGZpbGUuc2l6ZSA8IDEwMjQgKiAxMDI0ICogNTAgLy8xME0KICAgICAgaWYgKCFmaWxlU2l6ZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S4iuS8oOaWh+S7tuWkp+Wwj+S4jeiDvei2hei/hyA1ME1CIScpCiAgICAgIH0KICAgICAgbGV0IHNpemUgPSBmaWxlLnNpemUgLyAxMDI0CiAgICB9LAoKICAgIC8qKgogICAgICog5LiK5Lyg5paH5Lu25Yiw5pyN5Yqh5ZmoCiAgICAgKi8KICAgIHN1Ym1pdFVwbG9hZCgpIHsKICAgICAgdGhpcy4kcmVmcy51cGxvYWQuc3VibWl0KCkKICAgIH0sCgogICAgYXN5bmMgaW5pdERvbWFpbigpIHsKCiAgICAgIGxldCB7IGRhdGE6IG1ham9yT3B0aW9ucyB9ID0gYXdhaXQgZ2V0RGljdFR5cGVEYXRhKCdtYWpvcicpCiAgICAgIHRoaXMuc3BlY2lhbHR5T3B0aW9ucyA9IG1ham9yT3B0aW9ucwoKICAgICAgdGhpcy5maWx0ZXJJbmZvLmZpZWxkTGlzdC5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgIGlmIChpdGVtLnZhbHVlID09PSAnc3BlY2lhbHR5JykgewogICAgICAgICAgaXRlbS5vcHRpb25zID0gbWFqb3JPcHRpb25zCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIC8v5omT5byA5by55Ye65byA5YWzCiAgICBzaG93RGV2aWNlVHJlZURpYWxvZygpIHsKICAgICAgLy/ku47mlrDlop7moYblhoXmiZPlvIAKICAgICAgdGhpcy5pc0ZpbHRlciA9IGZhbHNlCiAgICAgIHRoaXMuc2hvd0RldmljZVRyZWUgPSB0cnVlCiAgICB9LAogICAgLy/lj43lm57orr7lpIfnsbvlnovpgInkuK3nmoTmlbDmja4KICAgIGdldERldmljZVR5cGVEYXRhKHJlcykgewogICAgICBjb25zb2xlLmxvZygicmVz77yaIixyZXMpCiAgICAgIGlmICh0aGlzLmlzRmlsdGVyKSB7CiAgICAgICAgY29uc29sZS5sb2coIuetm+mAieahhumAieaLqeiuvuWkh+exu+WeiywiLHJlcykKICAgICAgICBsZXQgc2JseEFyciA9IFtdCiAgICAgICAgdGhpcy5maWx0ZXJJbmZvLmRhdGEuZHlzYmx4ID0gJycKICAgICAgICByZXMuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIGlmIChpdGVtLmNoZWNrZWQpIHsKICAgICAgICAgICAgc2JseEFyci5wdXNoKGl0ZW0uY29kZSkKICAgICAgICAgICAgdGhpcy5maWx0ZXJJbmZvLmRhdGEuc2JseCA9IHNibHhBcnIuam9pbignLCcpCiAgICAgICAgICAgIHRoaXMuZmlsdGVySW5mby5kYXRhLmR5c2JseCArPSBpdGVtLm5hbWUgKyAnLCcKICAgICAgICAgIH0KICAgICAgICB9KQogICAgICAgIHRoaXMuZmlsdGVySW5mby5kYXRhLmR5c2JseCA9IHRoaXMuZmlsdGVySW5mby5kYXRhLmR5c2JseC5zdWJzdHJpbmcoMCwgdGhpcy5maWx0ZXJJbmZvLmRhdGEuZHlzYmx4Lmxlbmd0aCAtIDEpCiAgICAgICAgdGhpcy5zaG93RGV2aWNlVHJlZSA9IGZhbHNlCiAgICAgIH0gZWxzZSB7CiAgICAgICAgY29uc29sZS5sb2coIuaWsOWinuahhumAieaLqeiuvuWkh+exu+WeiywiLHJlcykKICAgICAgICBsZXQgdHJlZU5vZGVzID0gW10KICAgICAgICByZXMuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIGlmIChpdGVtLmNoZWNrZWQpIHsKICAgICAgICAgICAgdHJlZU5vZGVzLnB1c2goaXRlbSkKICAgICAgICAgIH0KICAgICAgICB9KQogICAgICAgIGlmICh0cmVlTm9kZXMubGVuZ3RoID09PSAxKSB7CiAgICAgICAgICB0aGlzLmZvcm0uc2JseG1jID0gdHJlZU5vZGVzWzBdLm5hbWUKICAgICAgICAgIHRoaXMuZm9ybS5zYmx4ID0gdHJlZU5vZGVzWzBdLmNvZGUKICAgICAgICAgIHRoaXMuc2hvd0RldmljZVRyZWUgPSBmYWxzZQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+mAieaLqeWNleadoeiuvuWkh+exu+WeiycpCiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgLy/lhbPpl63orr7lpIfnsbvlnovpgInkuK3moYYKICAgIGNsb3NlRGV2aWNlVHlwZURpYWxvZygpIHsKICAgICAgdGhpcy5zaG93RGV2aWNlVHJlZSA9IGZhbHNlCiAgICB9LAogICAgZ2V0UGFydE9wdGlvbnMoc2JseCkgewogICAgICBnZXRFcXVpcG1lbnRDb21wb25lbnRzT3B0aW9ucyhzYmx4KS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5wYXJ0c09wdGlvbnMgPSByZXMuZGF0YQogICAgICB9KQogICAgfQogIH0sCiAgLy8gd2F0Y2g6IHsKICAvLyAgICdmb3JtLnNibHhtYycodmFsKSB7CiAgLy8gICAgIHRoaXMucGFydERpc2FibGVkID0gdmFsID09PSAnJyB8fCB2YWwgPT09IG51bGwKICAvLyAgIH0KICAvLyB9Cn0K"}, {"version": 3, "sources": ["xqxbzk.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAo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file": "xqxbzk.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n      <el-filter\n        :data=\"filterInfo.data\"\n        :field-list=\"filterInfo.fieldList\"\n        @onfocusEvent=\"inputFocusEvent\"\n      />\n    <div>\n<!--      <el-white class=\"button-group\">\n        <el-row :gutter=\"3\" class=\"pull-right\">\n          <el-col style=\"\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n          \">-->\n            <el-white class=\"button-group\">\n              <div class=\"button_btn\">\n                <el-button type=\"primary\" v-hasPermi=\"['bzqxbzkxz:button:add']\" icon=\"el-icon-plus\" @click=\"handleAdd\">新增</el-button>\n                <el-button type=\"danger\" v-hasPermi=\"['bzqxbzkxz:button:delete']\" icon=\"el-icon-delete\" @click=\"handleDelete\">删除</el-button>\n              </div>\n              <comp-table :table-and-page-info=\"tableAndPageInfo\" @update:multipleSelection=\"handleSelectionChange\" height=\"68vh\">\n              <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\" width=\"160\"\n                               :resizable=\"false\">\n                <template slot-scope=\"scope\">\n                  <el-button @click=\"getDetails(scope.row)\" v-hasPermi=\"['bzqxbzkxz:button:update']\" type=\"text\" size=\"small\" class='el-icon-edit' title=\"修改\"></el-button>\n                  <el-button @click=\"getDetailsInfo(scope.row)\" type=\"text\" size=\"small\" title=\"详情\" class=\"el-icon-view\"></el-button>\n                </template>\n              </el-table-column>\n              </comp-table>\n            </el-white>\n    </div>\n\n\n    <!-- 新增/修改/详情 对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" append-to-body width=\"50%\" @close=\"handleClose\" v-dialogDrag>\n      <el-form ref=\"form\"  :model=\"form\" label-width=\"80px\" :rules=\"rules\">\n        <el-tabs tab-position=\"left\" style=\"height: 100%;\">\n          <el-tab-pane label=\"基本信息\">\n                <el-row :gutter=\"10\">\n                  <el-col :span=\"12\">\n<!--                    <el-form-item v-show=\"false\">-->\n<!--                      <el-input v-model=\"form.sblx\"></el-input>-->\n<!--                      <el-input v-model=\"form.objId\"></el-input>-->\n<!--                    </el-form-item>-->\n                    <el-form-item label=\"设备种类:\" prop=\"sblxmc\">\n                      <el-input @focus=\"showDeviceTreeDialog\" clearable v-model=\"form.sblxmc\" class=\"form-item\"\n                                :disabled=\"!formIsEditable\" placeholder=\"请选择设备种类\"></el-input>\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"12\">\n                    <el-form-item label=\"设备部件:\" prop=\"sbbm\">\n                      <el-input :disabled=\"!formIsEditable\" class=\"form-item\" v-model=\"form.sbbm\" clearable placeholder=\"请输入设备部件\"></el-input>\n                    </el-form-item>\n                  </el-col>\n                </el-row>\n                <el-row :gutter=\"10\">\n                  <el-col :span=\"12\">\n                    <el-form-item label=\"设备部位:\" prop=\"sbbw\">\n                      <el-input :disabled=\"!formIsEditable\" class=\"form-item\" v-model=\"form.sbbw\" clearable placeholder=\"请输入设备部位\"></el-input>\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"12\">\n                    <el-form-item label=\"隐患等级:\" prop=\"qxdj\">\n                      <el-select :disabled=\"!formIsEditable\" class=\"form-item\" v-model=\"form.qxdj\" clearable\n                                 placeholder=\"请输入隐患等级\">\n                        <el-option\n                          v-for=\"item in defectLevelOptions\"\n                          :key=\"item.value\"\n                          :label=\"item.label\"\n                          :value=\"item.value\">\n                        </el-option>\n                      </el-select>\n                    </el-form-item>\n                  </el-col>\n                </el-row>\n                <el-row :gutter=\"10\">\n\n                  <el-col :span=\"24\">\n                    <el-form-item label=\"缺陷描述:\" prop=\"qxms\">\n                      <el-input :disabled=\"!formIsEditable\" class=\"form-item-row\" v-model=\"form.qxms\" clearable\n                                placeholder=\"请输入缺陷描述\"></el-input>\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"24\">\n                    <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                      <el-input :disabled=\"!formIsEditable\" class=\"form-item-row\" v-model=\"form.flyj\" clearable\n                                placeholder=\"请输入分类依据\"></el-input>\n                    </el-form-item>\n                  </el-col>\n                </el-row>\n                <el-row :gutter=\"10\">\n                  <el-col :span=\"24\">\n                    <el-form-item label=\"技术原因:\" prop=\"jsyy\">\n                      <el-input class=\"form-item-row\" :disabled=\"!formIsEditable\" placeholder=\"请输入技术原因\"\n                                v-model=\"form.jsyy\"></el-input>\n                    </el-form-item>\n                  </el-col>\n                </el-row>\n          </el-tab-pane>\n        </el-tabs>\n\n      </el-form>\n      <div v-show=\"formIsEditable\" slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"handleClose\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitForm\">确 认</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 导入上传文件对话框 -->\n    <el-dialog title=\"上传附件\" :before-close=\"uploadClose\" :visible.sync=\"uploadDialogOpen\" width=\"50%\" v-dialogDrag>\n      <el-form ref=\"form\" :model=\"form\" label-width=\"80px\">\n        <el-form-item label=\"上传附件:\" prop=\"attachmentid\">\n          <el-upload\n            class=\"upload-demo\"\n            accept=\".jpg,.png,.rar,.txt,.zip,.doc,.ppt,.xls,.pdf,.docx,.xlsx,.mp4,.avi,.rmvb\"\n            ref=\"upload\"\n            :headers=\"header\"\n            action=\"/isc-api/file/upload\"\n            :before-upload=\"beforeUpload\"\n            :data=\"uploadData\"\n            multiple\n            :auto-upload=\"false\">\n            <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选取文件</el-button>\n            <el-button style=\"margin-left: 10px;\" size=\"small\" type=\"success\" @click=\"submitUpload\">上传到服务器</el-button>\n          </el-upload>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"uploadClose\" size=\"small\">关闭</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog\n      :append-to-body=\"true\"\n      title=\"设备分类\"\n      :visible.sync=\"showDeviceTree\"\n      width=\"400px\"\n      v-dialogDrag\n      v-if=\"showDeviceTree\">\n      <device-tree\n        @getDeviceTypeData=\"getDeviceTypeData\"\n        @closeDeviceTypeDialog=\"closeDeviceTypeDialog\">\n      </device-tree>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport { getPageDataList, remove, saveOrUpdate } from '@/api/dagangOilfield/bzgl/qxbzk'\nimport { getDictTypeData } from '@/api/system/dict/data'\nimport DeviceTree from '@/views/dagangOilfield/bzgl/sbbzk/deviceTree'\nimport { getEquipmentComponentsOptions } from '@/api/dagangOilfield/bzgl/sblxwh/sbbj'\n\nexport default {\n  name: 'xqxbzk',\n  components: { DeviceTree },\n  created() {\n    this.getData()\n  },\n  data() {\n    return {\n      //上传图片时的请求头\n      header: {},\n      filterInfo: {\n        data: {\n          sbbm:\"\",\n          sbbw:\"\",\n          qxms:\"\",\n          flyj:\"\",\n          jsyy:\"\",\n          dysblx: '',  //设备类型名称\n          sblx:\"\"\n        },\n        fieldList: [\n          { label: '设备种类', type: 'input', value: 'dysblx' },\n          { label: '设备部件', type: 'input', value: 'sbbm' },\n          { label: '设备部位', type: 'input', value: 'sbbw' },\n          { label: '缺陷描述', type: 'input', value: 'qxms' },\n          { label: '分类依据', type: 'input', value: 'flyj' },\n          {\n            label: '隐患等级',\n            type: 'select',\n            value: 'qxdjList',\n            multiple: true,\n            options: [{ label: '一般', value: '一般' }, { label: '严重', value: '严重' }, { label: '危急', value: '危急' }]\n          },\n          { label: '技术原因', type: 'input', value: 'jsyy' },\n\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          // { prop: 'zymc', label: '专业', minWidth: '120' },\n          { prop: 'sblxmc', label: '设备种类', minWidth: '180' },\n          { prop: 'sbbm', label: '设备部件', minWidth: '120' },\n          { prop: 'sbbw', label: '设备部位', minWidth: '120' },\n          { prop: 'qxms', label: '缺陷描述', minWidth: '120' },\n          { prop: 'flyj', label: '分类依据', minWidth: '120' },\n          { prop: 'qxdj', label: '隐患等级', minWidth: '120' },\n          { prop: 'jsyy', label: '技术原因', minWidth: '120' },\n         /* {\n            prop: 'operation',\n            label: '操作',\n            minWidth: '130px',\n            style: { display: 'block' },\n            //操作列固定再右侧\n            fixed: 'right',\n            operation: [\n              { name: '修改', clickFun: this.getDetails },\n              { name: '详情', clickFun: this.getDetailsInfo }\n\n            ]\n          }*/\n        ]\n      },\n      // 是否显示筛选条件\n      isSearchShow: false,\n      //查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        qxdjList: [],\n        sbbm:\"\",\n        sbbw:\"\",\n        qxms:\"\",\n        flyj:\"\",\n        jsyy:\"\",\n        dysblx: '',  //设备类型名称\n        sblx:\"\"\n\n      },\n      // 专业下拉框数据\n      specialtyOptions: [],\n      // 部件下拉框数据\n      partsOptions: [],\n\n      // 隐患等级下拉框数据\n      defectLevelOptions: [{ value: '一般', label: '一般' }, { value: '严重', label: '严重' }, { value: '危急', label: '危急' }],\n      // 多选框选中的数据id列表\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 标准缺陷库表格数据\n\n      // 对话框标题\n      title: '',\n      // 新增/修改/详情对话框是否打开\n      open: false,\n      // 上传附件对话框是否打开\n      uploadDialogOpen: false,\n      // 新增/修改提交的表单\n      form: {\n        objId: '',\n        zy: undefined,\n        sbzl: undefined,\n        sbbj: undefined,\n        qxbw: undefined,\n        qxms: undefined,\n        qxdj: undefined,\n        jsyy: undefined,\n        zryy: undefined,\n        createTime: undefined,\n        sblx: '',\n        sblxmc: '',\n        flyj: undefined,\n        sbbm: ''\n      },\n      // 新增/修改时对话框组件可编辑，详情时不可编辑\n      formIsEditable: false,\n      // 折叠面板展开的面板\n      activeNames: ['baseInfo'],\n      // 文件上传数据\n      uploadData: {},\n      // 文件上传请求头\n      upHeader: {},\n      // 表格共查询出的条数\n      total: 0,\n      //部件是否可编辑\n      partDisabled: false,\n      //展示设备分类树\n      showDeviceTree: false,\n      isFilter: false,\n      rules: {\n        sblxmc: [\n          {required: true, message: \"设备种类不能为空\", trigger: \"blur\"},\n        ],\n        sbbm: [\n          {required: true, message: \"设备部件\", trigger: \"blur\"},\n        ],\n        sbbw: [\n          {required: true, message: \"设备部位不能为空\", trigger: \"blur\"},\n        ],\n        qxdj: [\n          {required: true, message: \"隐患等级不能为空\", trigger: \"select\"},\n        ],\n        qxms: [\n          {required: true, message: \"缺陷描述不能为空\", trigger: \"blur\"},\n        ],\n        flyj: [\n          {required: true, message: \"分类依据不能为空\", trigger: \"blur\"},\n        ],\n        jsyy: [\n          {required: true, message: \"技术原因不能为空\", trigger: \"blur\"},\n        ],\n      },\n    }\n  },\n  mounted() {\n    //获取token\n    this.header.token = getToken();\n    this.initDomain()\n  },\n  methods: {\n    //筛选条件鼠标聚焦事件\n    inputFocusEvent(val) {\n      if (val.target.name === 'dysblx') {\n        this.showDeviceTree = true\n        this.isFilter = true\n      }\n    },\n\n    /**\n     * 查询数据\n     */\n    async getData(params) {\n      this.queryParams={...this.queryParams,...params}\n      const param = {...this.queryParams, ...params}\n      const {data, code} = await getPageDataList(param);\n      if (code === '0000') {\n        console.log(data)\n        this.tableAndPageInfo.tableData = data.records\n        this.tableAndPageInfo.pager.total = data.total\n      }\n    },\n    getDetails(row) {\n      this.open = true\n      this.formIsEditable = true\n      this.partDisabled = false\n      // if (row.sblx !== '' && row.sblx !== null) {\n      //   this.getPartOptions(row.sblx)\n      // }\n      this.form = row\n    },\n    getDetailsInfo(row) {\n      this.open = true\n      this.formIsEditable = false\n      this.partDisabled = true\n      this.form = row\n    },\n    /**\n     * 表格多选框\n     */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.objId)\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n    },\n\n    // /**\n    //  * 查询按钮\n    //  */\n    // handleQuery() {\n    //   this.queryParams.pageNum = 1\n    //   this.getData()\n    // },\n    //\n    // /**\n    //  * 重置按钮\n    //  */\n    // resetQuery() {\n    //   this.resetForm('queryForm')\n    //   this.handleQuery()\n    // },\n\n    /**\n     * 新增按钮\n     */\n    handleAdd() {\n      // 设置弹出框表单可编辑\n      this.formIsEditable = true\n      this.title = '新增标准缺陷'\n      this.open = true\n    },\n\n    /**\n     * 修改按钮\n     * @param row\n     */\n    handleEdit(row) {\n      // 设置弹出框表单可编辑\n      this.formIsEditable = true\n      // 将修改行的数据赋值到form上\n      for (let rowKey in row) {\n        this.form[rowKey] = row[rowKey]\n      }\n      this.title = '修改标准缺陷'\n      this.open = true\n    },\n\n    /**\n     * 处理批量删除\n     */\n    handleDelete() {\n      if (this.ids.length !== 0) {\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove(this.ids).then(res => {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            });\n            this.getData();\n          })\n        })\n      } else {\n        this.$message({\n          type: 'info',\n          message: '请选择至少一条数据!'\n        })\n      }\n\n    },\n\n    /**\n     * 导出按钮\n     */\n    handleExport() {\n      console.log('导出')\n    },\n\n    /**\n     * 导入按钮\n     */\n    handleImport() {\n      this.uploadDialogOpen = true\n    },\n\n    /**\n     * 查看详情\n     * @param row\n     */\n    handleDetails(row) {\n      this.formIsEditable = false\n      // 将行数据赋值到form上\n      for (let rowKey in row) {\n        this.form[rowKey] = row[rowKey]\n      }\n      this.title = '标准缺陷详情'\n      this.open = true\n    },\n\n    /**\n     * 关闭对话框\n     */\n    handleClose() {\n      this.reset()\n      this.open = false\n      this.title = ''\n    },\n\n    /**\n     * 关闭上传附件对话框\n     */\n    uploadClose() {\n      this.reset()\n      this.uploadDialogOpen = false\n    },\n\n    /**\n     * 提交表单\n     */\n    submitForm() {\n      this.$refs['form'].validate((valid) => {\n        if (valid) {\n          saveOrUpdate(this.form).then((response) => {\n            this.$message.success('保存成功！')\n            this.getData()\n            this.open = false\n          })\n        }else{\n          this.$message.error(\"校验未通过！\");\n          return false;\n        }\n      })\n    },\n\n    /**\n     * 表单重置\n     */\n    reset() {\n      this.form = {\n        id: undefined,\n        maintainType: undefined,\n        specialty: undefined,\n        deviceType: undefined,\n        parts: undefined,\n        defectPart: undefined,\n        defectDescription: undefined,\n        defectGist: undefined,\n        defectLevel: undefined,\n        reason: undefined,\n        createTime: undefined\n      }\n      this.resetForm('form')\n    },\n\n    /**\n     * 上传文件之前的处理\n     */\n    beforeUpload(file) {\n      const fileSize = file.size < 1024 * 1024 * 50 //10M\n      if (!fileSize) {\n        this.$message.error('上传文件大小不能超过 50MB!')\n      }\n      let size = file.size / 1024\n    },\n\n    /**\n     * 上传文件到服务器\n     */\n    submitUpload() {\n      this.$refs.upload.submit()\n    },\n\n    async initDomain() {\n\n      let { data: majorOptions } = await getDictTypeData('major')\n      this.specialtyOptions = majorOptions\n\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.value === 'specialty') {\n          item.options = majorOptions\n        }\n      })\n    },\n    //打开弹出开关\n    showDeviceTreeDialog() {\n      //从新增框内打开\n      this.isFilter = false\n      this.showDeviceTree = true\n    },\n    //反回设备类型选中的数据\n    getDeviceTypeData(res) {\n      console.log(\"res：\",res)\n      if (this.isFilter) {\n        console.log(\"筛选框选择设备类型,\",res)\n        let sblxArr = []\n        this.filterInfo.data.dysblx = ''\n        res.forEach(item => {\n          if (item.checked) {\n            sblxArr.push(item.code)\n            this.filterInfo.data.sblx = sblxArr.join(',')\n            this.filterInfo.data.dysblx += item.name + ','\n          }\n        })\n        this.filterInfo.data.dysblx = this.filterInfo.data.dysblx.substring(0, this.filterInfo.data.dysblx.length - 1)\n        this.showDeviceTree = false\n      } else {\n        console.log(\"新增框选择设备类型,\",res)\n        let treeNodes = []\n        res.forEach(item => {\n          if (item.checked) {\n            treeNodes.push(item)\n          }\n        })\n        if (treeNodes.length === 1) {\n          this.form.sblxmc = treeNodes[0].name\n          this.form.sblx = treeNodes[0].code\n          this.showDeviceTree = false\n        } else {\n          this.$message.warning('请选择单条设备类型')\n        }\n      }\n    },\n    //关闭设备类型选中框\n    closeDeviceTypeDialog() {\n      this.showDeviceTree = false\n    },\n    getPartOptions(sblx) {\n      getEquipmentComponentsOptions(sblx).then(res => {\n        this.partsOptions = res.data\n      })\n    }\n  },\n  // watch: {\n  //   'form.sblxmc'(val) {\n  //     this.partDisabled = val === '' || val === null\n  //   }\n  // }\n}\n</script>\n\n<style scoped>\n.filter-border {\n  border-bottom: #f5f8fd solid 1px;\n}\n\n.form-item {\n  width: 80%;\n}\n\n.form-item-row {\n  width: 92%;\n}\n</style>\n"]}]}