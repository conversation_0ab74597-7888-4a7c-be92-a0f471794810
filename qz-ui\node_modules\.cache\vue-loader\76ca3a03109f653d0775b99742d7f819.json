{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\sccjk.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\sccjk.vue", "mtime": 1749558220836}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgZ2V0TGlzdCwKICByZW1vdmUsCiAgc2F2ZU9yVXBkYXRlCn0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYnpnbC9zYmJ6ay9zY2NqayI7CmltcG9ydCB7IGdldERldmljZUNsYXNzR3JvdXAgfSBmcm9tICJAL2FwaS9kYWdhbmdPaWxmaWVsZC9iemdsL3NibHh3aC9zYmx4d2giOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogImluZGV4IiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgRGV2aWNlc0xpc3RHcm91cDogW10sCiAgICAgIGZvcm06IHt9LAogICAgICAvL+ivpuaDheW8ueahhuaYr+WQpuaYvuekugogICAgICBpc1Nob3dEZXRhaWxzOiBmYWxzZSwKICAgICAgLy/mmK/lkKbnpoHnlKgKICAgICAgaXNEaXNhYmxlZDogZmFsc2UsCiAgICAgIC8v5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgZmlsdGVySW5mbzogewogICAgICAgIGRhdGE6IHsKICAgICAgICAgIHNjY2ptYzogIiIsCiAgICAgICAgICBzYmx4QXJyOiBbXQogICAgICAgIH0sIC8v5p+l6K+i5p2h5Lu2CiAgICAgICAgZmllbGRMaXN0OiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi6K6+5aSH5YiG57G7IiwKICAgICAgICAgICAgdmFsdWU6ICJzYmx4QXJyIiwKICAgICAgICAgICAgdHlwZTogInNlbGVjdEdyb3Vwanh4bXdoIiwKICAgICAgICAgICAgY2xlYXJhYmxlOiB0cnVlLAogICAgICAgICAgICBmaWx0ZXJhYmxlOiB0cnVlLAogICAgICAgICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgICAgICAgb3B0aW9uczogW10KICAgICAgICAgIH0sCiAgICAgICAgICB7IGxhYmVsOiAi55Sf5Lqn5Y6C5a625ZCN56ewIiwgdHlwZTogImlucHV0IiwgdmFsdWU6ICJzY2NqbWMiIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIHRhYmxlQW5kUGFnZUluZm86IHsKICAgICAgICBwYWdlcjogewogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMjBdCiAgICAgICAgfSwKICAgICAgICB0YWJsZURhdGE6IFtdLAogICAgICAgIHRhYmxlSGVhZGVyOiBbCiAgICAgICAgICB7IGxhYmVsOiAi6K6+5aSH5YiG57G7IiwgcHJvcDogInNibHhtYyIsIG1pbldpZHRoOiAiMTgwIiB9LAogICAgICAgICAgeyBsYWJlbDogIueUn+S6p+WOguWutuWQjeensCIsIHByb3A6ICJzY2NqbWMiLCBtaW5XaWR0aDogIjE4MCIgfQogICAgICAgICAgLyp7CiAgICAgICAgICAgIHByb3A6ICdvcGVyYXRpb24nLAogICAgICAgICAgICBsYWJlbDogJ+aTjeS9nCcsCiAgICAgICAgICAgIG1pbldpZHRoOiAnMTMwcHgnLAogICAgICAgICAgICBzdHlsZTogeyBkaXNwbGF5OiAnYmxvY2snIH0sCiAgICAgICAgICAgIC8v5pON5L2c5YiX5Zu65a6a5YaN5Y+z5L6nCiAgICAgICAgICAgIGZpeGVkOiAncmlnaHQnLAogICAgICAgICAgICBvcGVyYXRpb246IFsKICAgICAgICAgICAgICB7IG5hbWU6ICfkv67mlLknLCBjbGlja0Z1bjogdGhpcy5nZXRVcGRhdGUgfSwKICAgICAgICAgICAgICB7IG5hbWU6ICfor6bmg4UnLCBjbGlja0Z1bjogdGhpcy5nZXREZXRhaWxzIH0KICAgICAgICAgICAgXQogICAgICAgICAgfSovCiAgICAgICAgXSwKICAgICAgICBvcHRpb246IHsgY2hlY2tCb3g6IHRydWUsIHNlcmlhbE51bWJlcjogdHJ1ZSB9CiAgICAgIH0sCiAgICAgIHBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHNjY2ptYzogIiIsCiAgICAgICAgc2JseEFycjogW10KICAgICAgfSwKICAgICAgc2VsZWN0Um93czogW10sCiAgICAgIHNob3dEZXZpY2VUcmVlOiBmYWxzZSwKICAgICAgaXNGaWx0ZXI6IGZhbHNlLAogICAgICBydWxlczogewogICAgICAgIGR5c2JseDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuiuvuWkh+WIhuexu+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0KICAgICAgICBdLAogICAgICAgIHNibHhtYzogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuiuvuWkh+WIhuexu+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0KICAgICAgICBdLAogICAgICAgIHNjY2ptYzogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIueUn+S6p+WOguWutuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXQogICAgICB9CiAgICB9OwogIH0sCiAgbW91bnRlZCgpIHsKICAgIC8v5YiX6KGo5p+l6K+iCiAgICB0aGlzLmdldERhdGEoKTsKICAgIHRoaXMuZ2V0RGV2aWNlQ2xhc3NHcm91cCgpOwogIH0sCiAgd2F0Y2g6IHsKICAgIGlzU2hvd0RldGFpbHModmFsKSB7CiAgICAgIGlmICh2YWwpIHsKICAgICAgICBjb25zdCBlbCA9IHRoaXMuJGVsLnF1ZXJ5U2VsZWN0b3IoIi5lbC1kaWFsb2ciKTsKICAgICAgICBlbC5zdHlsZS5sZWZ0ID0gMDsKICAgICAgICBlbC5zdHlsZS50b3AgPSAwOwogICAgICB9CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBnZXREZXZpY2VDbGFzc0dyb3VwKCkgewogICAgICBnZXREZXZpY2VDbGFzc0dyb3VwKFsiYmRzYiIsICJwZHNiIiwgInNkc2IiXSkudGhlbihyZXMgPT4gewogICAgICAgIGlmIChyZXMuY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICB0aGlzLkRldmljZXNMaXN0R3JvdXAgPSByZXMuZGF0YTsKICAgICAgICAgIHRoaXMuZmlsdGVySW5mby5maWVsZExpc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgICAgaWYgKGl0ZW0udmFsdWUgPT09ICJzYmx4QXJyIikgewogICAgICAgICAgICAgIGl0ZW0ub3B0aW9ucyA9IHJlcy5kYXRhOwogICAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuiOt+WPluiuvuWkh+WIhuexu+Wksei0pSIpOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy/liJfooajmn6Xor6IKICAgIGdldERhdGEocGFyYW1zKSB7CiAgICAgIHRoaXMuJHJlZnMuc2NjamsubG9hZGluZyA9IHRydWU7CiAgICAgIHRoaXMucGFyYW1zID0geyAuLi50aGlzLnBhcmFtcywgLi4ucGFyYW1zIH07CiAgICAgIGNvbnN0IHBhcmFtID0gdGhpcy5wYXJhbXM7CiAgICAgIGdldExpc3QocGFyYW0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0cnkgewogICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnRhYmxlRGF0YSA9IHJlcy5kYXRhLnJlY29yZHM7CiAgICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci50b3RhbCA9IHJlcy5kYXRhLnRvdGFsOwogICAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICAgICAgLy8g5Lul5pyN5Yqh55qE5pa55byP6LCD55So55qEIExvYWRpbmcg6ZyA6KaB5byC5q2l5YWz6ZetCiAgICAgICAgICAgICAgdGhpcy4kcmVmcy5zY2Nqay5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICAgIGNvbnNvbGUubG9nKGUpOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy/ph43nva7mjInpkq4KICAgIGdldFJlc2V0KCkge30sCiAgICAvL+mAieS4reihjAogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKCkge30sCiAgICAvL+aWsOWinuaMiemSrgogICAgZ2V0SW5zdGVyKCkgewogICAgICB0aGlzLnRpdGxlID0gIueUn+S6p+WOguWutuW6k+WinuWKoCI7CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICB0aGlzLmZvcm0gPSB7fTsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gdHJ1ZTsKICAgIH0sCiAgICAvL+S/ruaUueaMiemSrgogICAgZ2V0VXBkYXRlKHJvdykgewogICAgICB0aGlzLnRpdGxlID0gIueUn+S6p+WOguWutuW6k+S/ruaUuSI7CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICB0aGlzLmZvcm0gPSB7IC4uLnJvdyB9OwogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlOwogICAgfSwKICAgIC8v6K+m5oOF5oyJ6ZKuCiAgICBnZXREZXRhaWxzKHJvdykgewogICAgICB0aGlzLnRpdGxlID0gIueUn+S6p+WOguWutuW6k+ivpuaDheafpeeciyI7CiAgICAgIHRoaXMuZm9ybSA9IHsgLi4ucm93IH07CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IHRydWU7CiAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IHRydWU7CiAgICB9LAogICAgLyovL+mZhOS7tuafpeeciwogICAgZ2V0RmpJbmZvTGlzdCgpIHsKICAgICAgdGhpcy50aXRsZSA9ICfpmYTku7bmn6XnnIsnCiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IHRydWUKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gdHJ1ZQogICAgICB0aGlzLmZvcm09ey4uLnJvd30KICAgIH0sKi8KICAgIGFzeW5jIHNhdmVSb3coKSB7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBzYXZlT3JVcGRhdGUodGhpcy5mb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5pON5L2c5oiQ5YqfIik7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgICAgICAgY29uc29sZS5sb2coZSk7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgICAgICB9KTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgIH0KICAgICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLy/liKDpmaTmjInpkq4KICAgIGFzeW5jIGRlbGV0ZVJvdyhpZCkgewogICAgICAvLyBpZiAodGhpcy5zZWxlY3RSb3dzLmxlbmd0aCA8IDEpIHsKICAgICAgLy8gICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+mAieaLqeato+ehrueahOaVsOaNru+8ge+8ge+8gScpCiAgICAgIC8vICAgcmV0dXJuCiAgICAgIC8vIH0KICAgICAgLy8gbGV0IGlkcyA9IHRoaXMuc2VsZWN0Um93cy5tYXAoaXRlbSA9PiB7CiAgICAgIC8vICAgcmV0dXJuIGl0ZW0uc2NjamtJZAogICAgICAvLyB9KQogICAgICBsZXQgc2NjamsgPSBbXTsKICAgICAgc2NjamsucHVzaChpZCk7CiAgICAgIHRoaXMuJGNvbmZpcm0oIuatpOaTjeS9nOWwhuawuOS5heWIoOmZpOivpeaVsOaNriwg5piv5ZCm57un57utPyIsICLmj5DnpLoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KQogICAgICAgIC50aGVuKCgpID0+IHsKICAgICAgICAgIHJlbW92ZShzY2NqaykudGhlbigoeyBjb2RlIH0pID0+IHsKICAgICAgICAgICAgaWYgKGNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLAogICAgICAgICAgICAgICAgbWVzc2FnZTogIuWIoOmZpOaIkOWKnyEiCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICB0eXBlOiAiZXJyb3IiLAogICAgICAgICAgICAgICAgbWVzc2FnZTogIuWIoOmZpOWksei0pSEiCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0pCiAgICAgICAgLmNhdGNoKCgpID0+IHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAiaW5mbyIsCiAgICAgICAgICAgIG1lc3NhZ2U6ICLlt7Llj5bmtojliKDpmaQiCiAgICAgICAgICB9KTsKICAgICAgICB9KTsKICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICB9LAogICAgLy/lhbPpl63lvLnnqpcKICAgIGNsb3NlKCkgewogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSBmYWxzZTsKICAgIH0sCiAgICBzZWxlY3RDaGFuZ2Uocm93cykgewogICAgICB0aGlzLnNlbGVjdFJvd3MgPSByb3dzOwogICAgfSwKICAgIC8v5pi+56S66K6+5aSH57G75Z6L5qCR5by556qXCiAgICBzaG93RGV2aWNlVHJlZURpYWxvZygpIHsKICAgICAgdGhpcy5pc0ZpbHRlciA9IGZhbHNlOwogICAgICB0aGlzLnNob3dEZXZpY2VUcmVlID0gdHJ1ZTsKICAgIH0sCiAgICAvL+m8oOagh+iBmueEpuS6i+S7tgogICAgaW5wdXRGb2N1c0V2ZW50KHZhbCkgewogICAgICBpZiAodmFsLnRhcmdldC5uYW1lID09PSAiZHlzYmx4IikgewogICAgICAgIHRoaXMuc2hvd0RldmljZVRyZWUgPSB0cnVlOwogICAgICAgIHRoaXMuaXNGaWx0ZXIgPSB0cnVlOwogICAgICB9CiAgICB9LAogICAgLy/ojrflj5borr7lpIfliIbnsbvmlbDmja4KICAgIGdldERldmljZVR5cGVEYXRhKHJlcykgewogICAgICBjb25zb2xlLmxvZygicmVz77yaIiwgcmVzKTsKICAgICAgaWYgKHRoaXMuaXNGaWx0ZXIpIHsKICAgICAgICB0aGlzLmZpbHRlckluZm8uZGF0YS5zYmx4QXJyID0gW107CiAgICAgICAgdGhpcy5maWx0ZXJJbmZvLmRhdGEuZHlzYmx4ID0gIiI7CiAgICAgICAgcmVzLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICBpZiAoaXRlbS5jaGVja2VkKSB7CiAgICAgICAgICAgIHRoaXMuZmlsdGVySW5mby5kYXRhLnNibHhBcnIucHVzaChpdGVtLmNvZGUpOwogICAgICAgICAgICB0aGlzLmZpbHRlckluZm8uZGF0YS5keXNibHggKz0gaXRlbS5uYW1lICsgIiwiOwogICAgICAgICAgfQogICAgICAgIH0pOwoKICAgICAgICB0aGlzLmZpbHRlckluZm8uZGF0YS5keXNibHggPSB0aGlzLmZpbHRlckluZm8uZGF0YS5keXNibHguc3Vic3RyaW5nKAogICAgICAgICAgMCwKICAgICAgICAgIHRoaXMuZmlsdGVySW5mby5kYXRhLmR5c2JseC5sZW5ndGggLSAxCiAgICAgICAgKTsKICAgICAgICB0aGlzLnNob3dEZXZpY2VUcmVlID0gZmFsc2U7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgbGV0IHRyZWVOb2RlcyA9IFtdOwogICAgICAgIHJlcy5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgaWYgKGl0ZW0uY2hlY2tlZCkgewogICAgICAgICAgICB0cmVlTm9kZXMucHVzaChpdGVtKTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgICBpZiAodHJlZU5vZGVzLmxlbmd0aCA9PT0gMSkgewogICAgICAgICAgdGhpcy5mb3JtLnNibHhtYyA9IHRyZWVOb2Rlc1swXS5uYW1lOwogICAgICAgICAgdGhpcy5mb3JtLmR5c2JseCA9IHRyZWVOb2Rlc1swXS5jb2RlOwogICAgICAgICAgdGhpcy5zaG93RGV2aWNlVHJlZSA9IGZhbHNlOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+mAieaLqeWNleadoeiuvuWkh+aVsOaNriIpOwogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIGNsb3NlRGV2aWNlVHlwZURpYWxvZygpIHsKICAgICAgdGhpcy5zaG93RGV2aWNlVHJlZSA9IGZhbHNlOwogICAgfSwKICAgIC8v5riF56m66KGo5Y2V5pWw5o2uCiAgICBoYW5kbGVDbG9zZSgpIHsKICAgICAgdGhpcy5mb3JtID0ge307CiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICB0aGlzLmZvcm0gPSB0aGlzLiRvcHRpb25zLmRhdGEoKS5mb3JtOwogICAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICAgIH0pOwogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["sccjk.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "sccjk.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 120, itemWidth: 230 }\"\n      @handleReset=\"getReset\"\n    />\n\n    <el-white class=\"button-group1\">\n      <div class=\"button_btn\">\n        <el-button\n          type=\"primary\"\n          v-hasPermi=\"['bzsccj:button:add']\"\n          icon=\"el-icon-plus\"\n          @click=\"getInster\"\n          >新增\n        </el-button>\n      </div>\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"selectChange\"\n        height=\"68vh\"\n        ref=\"sccjk\"\n      >\n        <el-table-column\n          slot=\"table_eight\"\n          align=\"center\"\n          fixed=\"right\"\n          style=\"display: block\"\n          label=\"操作\"\n          width=\"160\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              @click=\"getUpdate(scope.row)\"\n              v-hasPermi=\"['bzsccj:button:update']\"\n              type=\"text\"\n              size=\"small\"\n              class=\"el-icon-edit\"\n              title=\"修改\"\n            >\n            </el-button>\n            <el-button\n              @click=\"getDetails(scope.row)\"\n              type=\"text\"\n              size=\"small\"\n              title=\"详情\"\n              class=\"el-icon-view\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              v-if=\"scope.row.createBy === $store.getters.name\"\n              title=\"删除\"\n              icon=\"el-icon-delete\"\n              @click=\"deleteRow(scope.row.sccjkId)\"\n            >\n            </el-button>\n          </template>\n        </el-table-column>\n      </comp-table>\n    </el-white>\n\n    <!-- 详情/新增/修改 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"30%\"\n      @close=\"handleClose\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"130px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row class=\"box-card\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"设备分类：\" prop=\"dysblx\">\n              <el-select\n                placeholder=\"请选择设备分类\"\n                v-model=\"form.dysblx\"\n                :disabled=\"isDisabled\"\n                clearable\n                filterable\n                class=\"custom-group\"\n              >\n                <el-option-group\n                  v-for=\"group in DevicesListGroup\"\n                  :key=\"group.label\"\n                  :label=\"group.label\"\n                >\n                  <el-option\n                    v-for=\"item in group.sbDataList\"\n                    :key=\"item.code\"\n                    :label=\"item.name\"\n                    :value=\"item.code\"\n                  >\n                  </el-option>\n                </el-option-group>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"生产厂家名称：\" prop=\"sccjmc\">\n              <el-input\n                clearable\n                placeholder=\"请输入生产厂家名称\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.sccjmc\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\" size=\"small\">取 消</el-button>\n        <el-button\n          v-if=\"title === '生产厂家库增加' || title === '生产厂家库修改'\"\n          type=\"primary\"\n          size=\"small\"\n          @click=\"saveRow\"\n          >确 认\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  getList,\n  remove,\n  saveOrUpdate\n} from \"@/api/dagangOilfield/bzgl/sbbzk/sccjk\";\nimport { getDeviceClassGroup } from \"@/api/dagangOilfield/bzgl/sblxwh/sblxwh\";\nexport default {\n  name: \"index\",\n  data() {\n    return {\n      DevicesListGroup: [],\n      form: {},\n      //详情弹框是否显示\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          sccjmc: \"\",\n          sblxArr: []\n        }, //查询条件\n        fieldList: [\n          {\n            label: \"设备分类\",\n            value: \"sblxArr\",\n            type: \"selectGroupjxxmwh\",\n            clearable: true,\n            filterable: true,\n            multiple: true,\n            options: []\n          },\n          { label: \"生产厂家名称\", type: \"input\", value: \"sccjmc\" }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 120]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"设备分类\", prop: \"sblxmc\", minWidth: \"180\" },\n          { label: \"生产厂家名称\", prop: \"sccjmc\", minWidth: \"180\" }\n          /*{\n            prop: 'operation',\n            label: '操作',\n            minWidth: '130px',\n            style: { display: 'block' },\n            //操作列固定再右侧\n            fixed: 'right',\n            operation: [\n              { name: '修改', clickFun: this.getUpdate },\n              { name: '详情', clickFun: this.getDetails }\n            ]\n          }*/\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        pageNum: 1,\n        pageSize: 10,\n        sccjmc: \"\",\n        sblxArr: []\n      },\n      selectRows: [],\n      showDeviceTree: false,\n      isFilter: false,\n      rules: {\n        dysblx: [\n          { required: true, message: \"设备分类不能为空\", trigger: \"change\" }\n        ],\n        sblxmc: [\n          { required: true, message: \"设备分类不能为空\", trigger: \"change\" }\n        ],\n        sccjmc: [\n          { required: true, message: \"生产厂家不能为空\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  mounted() {\n    //列表查询\n    this.getData();\n    this.getDeviceClassGroup();\n  },\n  watch: {\n    isShowDetails(val) {\n      if (val) {\n        const el = this.$el.querySelector(\".el-dialog\");\n        el.style.left = 0;\n        el.style.top = 0;\n      }\n    }\n  },\n  methods: {\n    getDeviceClassGroup() {\n      getDeviceClassGroup([\"bdsb\", \"pdsb\", \"sdsb\"]).then(res => {\n        if (res.code === \"0000\") {\n          this.DevicesListGroup = res.data;\n          this.filterInfo.fieldList.forEach(item => {\n            if (item.value === \"sblxArr\") {\n              item.options = res.data;\n              return;\n            }\n          });\n        } else {\n          this.$message.error(\"获取设备分类失败\");\n        }\n      });\n    },\n    //列表查询\n    getData(params) {\n      this.$refs.sccjk.loading = true;\n      this.params = { ...this.params, ...params };\n      const param = this.params;\n      getList(param).then(res => {\n        try {\n          if (res.code === \"0000\") {\n            this.tableAndPageInfo.tableData = res.data.records;\n            this.tableAndPageInfo.pager.total = res.data.total;\n            this.$nextTick(() => {\n              // 以服务的方式调用的 Loading 需要异步关闭\n              this.$refs.sccjk.loading = false;\n            });\n          }\n        } catch (e) {\n          console.log(e);\n        }\n      });\n    },\n    //重置按钮\n    getReset() {},\n    //选中行\n    handleSelectionChange() {},\n    //新增按钮\n    getInster() {\n      this.title = \"生产厂家库增加\";\n      this.isDisabled = false;\n      this.form = {};\n      this.isShowDetails = true;\n    },\n    //修改按钮\n    getUpdate(row) {\n      this.title = \"生产厂家库修改\";\n      this.isDisabled = false;\n      this.form = { ...row };\n      this.isShowDetails = true;\n    },\n    //详情按钮\n    getDetails(row) {\n      this.title = \"生产厂家库详情查看\";\n      this.form = { ...row };\n      this.isDisabled = true;\n      this.isShowDetails = true;\n    },\n    /*//附件查看\n    getFjInfoList() {\n      this.title = '附件查看'\n      this.isDisabled = true\n      this.isShowDetails = true\n      this.form={...row}\n    },*/\n    async saveRow() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          saveOrUpdate(this.form).then(res => {\n            try {\n              if (res.code === \"0000\") {\n                this.$message.success(\"操作成功\");\n              }\n            } catch (e) {\n              console.log(e);\n            }\n            this.getData();\n          });\n        } else {\n          return false;\n        }\n        this.isShowDetails = false;\n      });\n    },\n    //删除按钮\n    async deleteRow(id) {\n      // if (this.selectRows.length < 1) {\n      //   this.$message.warning('请选择正确的数据！！！')\n      //   return\n      // }\n      // let ids = this.selectRows.map(item => {\n      //   return item.sccjkId\n      // })\n      let sccjk = [];\n      sccjk.push(id);\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove(sccjk).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n      this.getData();\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false;\n    },\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n    //显示设备类型树弹窗\n    showDeviceTreeDialog() {\n      this.isFilter = false;\n      this.showDeviceTree = true;\n    },\n    //鼠标聚焦事件\n    inputFocusEvent(val) {\n      if (val.target.name === \"dysblx\") {\n        this.showDeviceTree = true;\n        this.isFilter = true;\n      }\n    },\n    //获取设备分类数据\n    getDeviceTypeData(res) {\n      console.log(\"res：\", res);\n      if (this.isFilter) {\n        this.filterInfo.data.sblxArr = [];\n        this.filterInfo.data.dysblx = \"\";\n        res.forEach(item => {\n          if (item.checked) {\n            this.filterInfo.data.sblxArr.push(item.code);\n            this.filterInfo.data.dysblx += item.name + \",\";\n          }\n        });\n\n        this.filterInfo.data.dysblx = this.filterInfo.data.dysblx.substring(\n          0,\n          this.filterInfo.data.dysblx.length - 1\n        );\n        this.showDeviceTree = false;\n      } else {\n        let treeNodes = [];\n        res.forEach(item => {\n          if (item.checked) {\n            treeNodes.push(item);\n          }\n        });\n        if (treeNodes.length === 1) {\n          this.form.sblxmc = treeNodes[0].name;\n          this.form.dysblx = treeNodes[0].code;\n          this.showDeviceTree = false;\n        } else {\n          this.$message.warning(\"请选择单条设备数据\");\n        }\n      }\n    },\n    closeDeviceTypeDialog() {\n      this.showDeviceTree = false;\n    },\n    //清空表单数据\n    handleClose() {\n      this.form = {};\n      this.$nextTick(() => {\n        this.form = this.$options.data().form;\n        this.resetForm(\"form\");\n      });\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.button-group {\n  padding-left: 30px;\n  padding-right: 30px;\n  display: flex;\n  justify-content: flex-end;\n}\n\n/*控制input输入框边框是否显示*/\n.elInput >>> .el-input__inner {\n  border: 0;\n}\n\n.box-card {\n  margin-bottom: 2vh !important;\n}\n\n/deep/ .el-select-group__title {\n  font-size: 14px;\n  font-weight: bold;\n  color: #11ba6b;\n  padding: 8px 0;\n}\n</style>\n"]}]}