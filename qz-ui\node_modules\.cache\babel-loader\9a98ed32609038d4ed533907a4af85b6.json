{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\blgk\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\blgk\\index.vue", "mtime": 1750579918247}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZm9yLWVhY2giKTsKCnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5mdW5jdGlvbi5uYW1lIik7CgpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LmtleXMiKTsKCnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLmZvci1lYWNoIik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKCnZhciBfb2JqZWN0U3ByZWFkMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovU2hhbW1wb29sL3dvcmsvY29kZS9kZ3l0LzAxXHU0RUUzXHU3ODAxL3F6LXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL29iamVjdFNwcmVhZDIiKSk7CgpyZXF1aXJlKCJyZWdlbmVyYXRvci1ydW50aW1lL3J1bnRpbWUiKTsKCnZhciBfYXN5bmNUb0dlbmVyYXRvcjIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkQ6L1NoYW1tcG9vbC93b3JrL2NvZGUvZGd5dC8wMVx1NEVFM1x1NzgwMS9xei11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9hc3luY1RvR2VuZXJhdG9yIikpOwoKdmFyIF9ibGdrID0gcmVxdWlyZSgiQC9hcGkvYmxnay9ibGdrIik7Cgp2YXIgX2JsZ2tiemsgPSByZXF1aXJlKCJAL2FwaS9ibGdrL2JsZ2tiemsiKTsKCnZhciBfZGVwdCA9IHJlcXVpcmUoIkAvYXBpL3N5c3RlbS9kZXB0Iik7Cgp2YXIgX2JkeGp6cXB6ID0gcmVxdWlyZSgiQC9hcGkveXhnbC9iZHl4Z2wvYmR4anpxcHoiKTsKCnZhciBfYmRzYnR6ID0gcmVxdWlyZSgiQC9hcGkvZGFnYW5nT2lsZmllbGQvYXNzZXQvYmRzYnR6Iik7Cgp2YXIgX2hhbmRsZURhdGEgPSByZXF1aXJlKCJAL3V0aWxzL2hhbmRsZURhdGEiKTsKCnZhciBfZWxlbWVudFVpID0gcmVxdWlyZSgiZWxlbWVudC11aSIpOwoKdmFyIF9kYXRhID0gcmVxdWlyZSgiQC9hcGkvc3lzdGVtL2RpY3QvZGF0YSIpOwoKdmFyIF9jaG9vc2VCZHNiID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3ZpZXdzL2RhZ2FuZ09pbGZpZWxkL2JsZ2svY2hvb3NlQmRzYiIpKTsKCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IHsKICBuYW1lOiAiYmxnayIsCiAgY29tcG9uZW50czogewogICAgc2JDaG9vc2U6IF9jaG9vc2VCZHNiLmRlZmF1bHQKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBoYXNTdXBlclJvbGU6IHRoaXMuJHN0b3JlLmdldHRlcnMuaGFzU3VwZXJSb2xlLAogICAgICBpc1Nob3c6IGZhbHNlLAogICAgICAvL+W8ueahhuaYr+WQpuaYvuekugogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgLy/mmK/lkKbnpoHnlKgKICAgICAgaXNEaXNhYmxlZDogZmFsc2UsCiAgICAgIGZvcm06IHt9LAogICAgICAvL+afpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBwYWdlTnVtOiAxCiAgICAgIH0sCiAgICAgIC8v6K+m5oOF5a+56K+d5qGG5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgZmlsdGVySW5mbzogewogICAgICAgIGRhdGE6IHsKICAgICAgICAgIC8vIGZnc21jOiAiIiwKICAgICAgICAgIGJkem1jOiAiIiwKICAgICAgICAgIGpnbWM6ICIiLAogICAgICAgICAgc2JtYzogIiIsCiAgICAgICAgICBzYmx4OiAiIiwKICAgICAgICAgIGlzQ2xlYW46ICIiCiAgICAgICAgfSwKICAgICAgICBmaWVsZExpc3Q6IFsvLyB7IGxhYmVsOiAi5YiG5YWs5Y+4IiwgdHlwZTogInNlbGVjdCIsIHZhbHVlOiAiZmdzbWMiLCBvcHRpb25zOiBbXSB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAi5Y+Y55S156uZIiwKICAgICAgICAgIHR5cGU6ICJpbnB1dCIsCiAgICAgICAgICB2YWx1ZTogImJkem1jIgogICAgICAgIH0sIHsKICAgICAgICAgIGxhYmVsOiAi6Ze06ZqU5ZCN56ewIiwKICAgICAgICAgIHR5cGU6ICJpbnB1dCIsCiAgICAgICAgICB2YWx1ZTogImpnbWMiCiAgICAgICAgfSwgewogICAgICAgICAgbGFiZWw6ICLorr7lpIflkI3np7AiLAogICAgICAgICAgdHlwZTogImlucHV0IiwKICAgICAgICAgIHZhbHVlOiAic2JtYyIKICAgICAgICB9LCB7CiAgICAgICAgICBsYWJlbDogIuiuvuWkh+exu+WeiyIsCiAgICAgICAgICB0eXBlOiAic2VsZWN0IiwKICAgICAgICAgIHZhbHVlOiAic2JseCIsCiAgICAgICAgICBvcHRpb25zOiBbXSwKICAgICAgICAgIGNsZWFyYWJsZTogdHJ1ZSwKICAgICAgICAgIGZpbHRlcmFibGU6IHRydWUKICAgICAgICB9LCB7CiAgICAgICAgICBsYWJlbDogIuaYr+WQpua2iOmZpCIsCiAgICAgICAgICB0eXBlOiAic2VsZWN0IiwKICAgICAgICAgIHZhbHVlOiAiaXNDbGVhbiIsCiAgICAgICAgICBvcHRpb25zOiBbXSwKICAgICAgICAgIGNsZWFyYWJsZTogdHJ1ZQogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIHRhYmxlQW5kUGFnZUluZm86IHsKICAgICAgICBwYWdlcjogewogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMDBdCiAgICAgICAgfSwKICAgICAgICBvcHRpb246IHsKICAgICAgICAgIGNoZWNrQm94OiBmYWxzZSwKICAgICAgICAgIHNlcmlhbE51bWJlcjogdHJ1ZQogICAgICAgIH0sCiAgICAgICAgdGFibGVEYXRhOiBbXSwKICAgICAgICB0YWJsZUhlYWRlcjogW3sKICAgICAgICAgIHByb3A6ICJmZ3NtYyIsCiAgICAgICAgICBsYWJlbDogIuWIhuWFrOWPuCIsCiAgICAgICAgICBtaW5XaWR0aDogIjEyMCIKICAgICAgICB9LCB7CiAgICAgICAgICBwcm9wOiAiYmR6bWMiLAogICAgICAgICAgbGFiZWw6ICLlj5jnlLXnq5kiLAogICAgICAgICAgbWluV2lkdGg6ICIxMjAiCiAgICAgICAgfSwgewogICAgICAgICAgcHJvcDogImpnbWMiLAogICAgICAgICAgbGFiZWw6ICLpl7TpmpTlkI3np7AiLAogICAgICAgICAgbWluV2lkdGg6ICIxMDAiCiAgICAgICAgfSwgewogICAgICAgICAgcHJvcDogInNibHhtYyIsCiAgICAgICAgICBsYWJlbDogIuiuvuWkh+exu+WeiyIsCiAgICAgICAgICBtaW5XaWR0aDogIjEyMCIKICAgICAgICB9LCB7CiAgICAgICAgICBwcm9wOiAic2JtYyIsCiAgICAgICAgICBsYWJlbDogIuiuvuWkh+WQjeensCIsCiAgICAgICAgICBtaW5XaWR0aDogIjEyMCIKICAgICAgICB9LCB7CiAgICAgICAgICBwcm9wOiAibXMiLAogICAgICAgICAgbGFiZWw6ICLkuI3oia/lt6XlhrXmj4/ov7AiLAogICAgICAgICAgbWluV2lkdGg6ICIxNDAiLAogICAgICAgICAgc2hvd1BvcDogdHJ1ZQogICAgICAgIH0sIHsKICAgICAgICAgIHByb3A6ICJmbHlqQ24iLAogICAgICAgICAgbGFiZWw6ICLliIbnsbvkvp3mja4iLAogICAgICAgICAgbWluV2lkdGg6ICIxNDAiLAogICAgICAgICAgc2hvd1BvcDogdHJ1ZQogICAgICAgIH0sIHsKICAgICAgICAgIHByb3A6ICJscnJtYyIsCiAgICAgICAgICBsYWJlbDogIuW9leWFpeS6uiIsCiAgICAgICAgICBtaW5XaWR0aDogIjgwIgogICAgICAgIH0sIHsKICAgICAgICAgIHByb3A6ICJmeHNqIiwKICAgICAgICAgIGxhYmVsOiAi5Y+R546w5pe26Ze0IiwKICAgICAgICAgIG1pbldpZHRoOiAiMTEwIgogICAgICAgIH0sIHsKICAgICAgICAgIHByb3A6ICJ4Y3NqIiwKICAgICAgICAgIGxhYmVsOiAi5raI6Zmk5pe26Ze0IiwKICAgICAgICAgIG1pbldpZHRoOiAiMTEwIgogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgZmdzbWM6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLliIblhazlj7jkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgYmR6bWM6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLlj5jnlLXnq5nkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgamdtYzogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIumXtOmalOS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBzYmx4bWM6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLorr7lpIfnsbvlnovkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgdHlwZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuS4jeiJr+W3peWGteexu+Wei+S4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAic2VsZWN0IgogICAgICAgIH1dLAogICAgICAgIHNibWM6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLorr7lpIfkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgbXM6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLkuI3oia/lt6XlhrXkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogInNlbGVjdCIKICAgICAgICB9XSwKICAgICAgICBmbHlqOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5YiG57G75L6d5o2u5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJzZWxlY3QiCiAgICAgICAgfV0KICAgICAgfSwKICAgICAgZmdzTGlzdDogW10sCiAgICAgIC8v5YiG5YWs5Y+45LiL5ouJ5qGGCiAgICAgIGJkekxpc3Q6IFtdLAogICAgICAvL+WPmOeUteermeS4i+aLieahhgogICAgICBqZ0xpc3Q6IFtdLAogICAgICAvL+mXtOmalOS4i+aLieahhgogICAgICBzYmx4TGlzdDogW10sCiAgICAgIC8v6Ze06ZqU5LiL5ouJ5qGGCiAgICAgIHNiTGlzdDogW10sCiAgICAgIC8v6K6+5aSH5LiL5ouJ5qGGCiAgICAgIG1zTGlzdDogW10sCiAgICAgIC8v5o+P6L+w5LiL5ouJ5qGGCiAgICAgIGZseWpMaXN0OiBbXSwKICAgICAgLy/liIbnsbvkvp3mja7kuIvmi4nmoYYKICAgICAgbHhMaXN0OiBbXSwKICAgICAgLy/nsbvlnovkuIvmi4nmoYYKICAgICAgc2F2ZUxvYWRpbmc6IG51bGwsCiAgICAgIC8v5L+d5a2Y5pe255qE6YGu572p5bGCCiAgICAgIGlzU2hvd1NiQ2hvb3NlOiBmYWxzZSwKICAgICAgLy/mmK/lkKbmmL7npLrorr7lpIfpgInmi6nlvLnmoYYKICAgICAgc2JEYXRhOiB7fSAvL+iuvuWkh+aVsOaNrgoKICAgIH07CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgLy/liJfooajmn6Xor6IKICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgdGhpcy5nZXRPcHRpb25zKCk7IC8v6I635Y+W5raJ5Y+K5Yiw55qE5LiL5ouJ5qGG5a2X5YW45YC8CiAgfSwKICBtZXRob2RzOiB7CiAgICAvL+mAieaLqeiuvuWkhwogICAgY2hvb3NlU2JGdW46IGZ1bmN0aW9uIGNob29zZVNiRnVuKCkgewogICAgICB0aGlzLmlzU2hvd1NiQ2hvb3NlID0gdHJ1ZTsKICAgIH0sCiAgICAvL+WFs+mXreiuvuWkh+mAieaLqeW8ueahhgogICAgY2xvc2VDaG9vc2VGdW46IGZ1bmN0aW9uIGNsb3NlQ2hvb3NlRnVuKCkgewogICAgICB0aGlzLmlzU2hvd1NiQ2hvb3NlID0gZmFsc2U7CiAgICB9LAogICAgLy/ojrflj5borr7lpIfmlbDmja4KICAgIGdldFNiRGF0YUZ1bjogZnVuY3Rpb24gZ2V0U2JEYXRhRnVuKHNiRGF0YSkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwoKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoIC8qI19fUFVSRV9fKi9yZWdlbmVyYXRvclJ1bnRpbWUubWFyayhmdW5jdGlvbiBfY2FsbGVlKCkgewogICAgICAgIHJldHVybiByZWdlbmVyYXRvclJ1bnRpbWUud3JhcChmdW5jdGlvbiBfY2FsbGVlJChfY29udGV4dCkgewogICAgICAgICAgd2hpbGUgKDEpIHsKICAgICAgICAgICAgc3dpdGNoIChfY29udGV4dC5wcmV2ID0gX2NvbnRleHQubmV4dCkgewogICAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICAgIF90aGlzLnNiRGF0YSA9IHNiRGF0YTsKCiAgICAgICAgICAgICAgICBfdGhpcy4kc2V0KF90aGlzLmZvcm0sICJmZ3NtYyIsIHNiRGF0YS5kZXB0bmFtZSk7CgogICAgICAgICAgICAgICAgX3RoaXMuJHNldChfdGhpcy5mb3JtLCAiYmR6bWMiLCBzYkRhdGEuYmR6bWMpOwoKICAgICAgICAgICAgICAgIF90aGlzLiRzZXQoX3RoaXMuZm9ybSwgImpnbWMiLCBzYkRhdGEud3ptYyk7CgogICAgICAgICAgICAgICAgX3RoaXMuJHNldChfdGhpcy5mb3JtLCAic2JseG1jIiwgc2JEYXRhLnNibHhtYyk7CgogICAgICAgICAgICAgICAgX3RoaXMuJHNldChfdGhpcy5mb3JtLCAic2JseCIsIHNiRGF0YS5zYmx4Ym0pOwoKICAgICAgICAgICAgICAgIF90aGlzLiRzZXQoX3RoaXMuZm9ybSwgInNibWMiLCBzYkRhdGEuc2JtYyk7CgogICAgICAgICAgICAgICAgX3RoaXMuJHNldChfdGhpcy5mb3JtLCAic2JpZCIsIHNiRGF0YS5vYmpJZCk7CgogICAgICAgICAgICAgICAgX3RoaXMuJHNldChfdGhpcy5mb3JtLCAidHlwZSIsICIiKTsKCiAgICAgICAgICAgICAgICBfdGhpcy4kc2V0KF90aGlzLmZvcm0sICJtcyIsICIiKTsKCiAgICAgICAgICAgICAgICBfdGhpcy4kc2V0KF90aGlzLmZvcm0sICJmbHlqIiwgIiIpOyAvL+iOt+WPluS4jeiJr+W3peWGteexu+Wei+S4i+aLieahhgoKCiAgICAgICAgICAgICAgICBfdGhpcy5seExpc3QgPSBbXTsKICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAxNDsKICAgICAgICAgICAgICAgIHJldHVybiAoMCwgX2JsZ2tiemsuZ2V0THgpKHsKICAgICAgICAgICAgICAgICAgc2JseDogc2JEYXRhLnNibHhibQogICAgICAgICAgICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgICAgICAgIHJlcy5kYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgICAgICAgICBfdGhpcy5seExpc3QucHVzaCh7CiAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogaXRlbS5sYWJlbCwKICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiBpdGVtLmxhYmVsCiAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICAgICAgX3RoaXMubHhMaXN0LnB1c2goewogICAgICAgICAgICAgICAgICAgIGxhYmVsOiAi5YW25LuWIiwKICAgICAgICAgICAgICAgICAgICB2YWx1ZTogIuWFtuS7liIKICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICB9KTsKCiAgICAgICAgICAgICAgY2FzZSAxNDoKICAgICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LnN0b3AoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvL+afpeivouS4i+aLieahhuaVsOaNrgogICAgZ2V0T3B0aW9uczogZnVuY3Rpb24gZ2V0T3B0aW9ucygpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CgogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSggLyojX19QVVJFX18qL3JlZ2VuZXJhdG9yUnVudGltZS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUyKCkgewogICAgICAgIHJldHVybiByZWdlbmVyYXRvclJ1bnRpbWUud3JhcChmdW5jdGlvbiBfY2FsbGVlMiQoX2NvbnRleHQyKSB7CiAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0Mi5wcmV2ID0gX2NvbnRleHQyLm5leHQpIHsKICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICBfY29udGV4dDIubmV4dCA9IDI7CiAgICAgICAgICAgICAgICByZXR1cm4gX3RoaXMyLmdldFNmTGlzdCgpOwoKICAgICAgICAgICAgICBjYXNlIDI6CiAgICAgICAgICAgICAgICBfY29udGV4dDIubmV4dCA9IDQ7CiAgICAgICAgICAgICAgICByZXR1cm4gX3RoaXMyLmdldEFsbFNibHhMaXN0KCk7CgogICAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Mi5zdG9wKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMik7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8v6I635Y+W5omA5pyJ6K6+5aSH57G75Z6L5LiL5ouJ5qGG55So5LqO5p+l6K+iCiAgICBnZXRBbGxTYmx4TGlzdDogZnVuY3Rpb24gZ2V0QWxsU2JseExpc3QoKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwoKICAgICAgKDAsIF9ibGdrLmdldEFsbFNibHhMaXN0KSh7CiAgICAgICAgenk6ICJiZHNiIgogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpczMuZmlsdGVySW5mby5maWVsZExpc3QuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgaWYgKGl0ZW0udmFsdWUgPT09ICJzYmx4IikgewogICAgICAgICAgICBpdGVtLm9wdGlvbnMgPSByZXMuZGF0YTsKICAgICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICAvL+iOt+WPluaYry/lkKbvvIzlrZflhbgKICAgIGdldFNmTGlzdDogZnVuY3Rpb24gZ2V0U2ZMaXN0KCkgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKCiAgICAgICgwLCBfZGF0YS5nZXREaWN0VHlwZURhdGEpKCJzeXNfc2YiKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICB2YXIgc2ZMaXN0ID0gW107CiAgICAgICAgcmVzLmRhdGEuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgc2ZMaXN0LnB1c2goewogICAgICAgICAgICBsYWJlbDogaXRlbS5sYWJlbCwKICAgICAgICAgICAgdmFsdWU6IGl0ZW0udmFsdWUKICAgICAgICAgIH0pOwogICAgICAgIH0pOwoKICAgICAgICBfdGhpczQuZmlsdGVySW5mby5maWVsZExpc3QuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgaWYgKGl0ZW0udmFsdWUgPT09ICJpc0NsZWFuIikgewogICAgICAgICAgICBpdGVtLm9wdGlvbnMgPSBzZkxpc3Q7CiAgICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgLy/moLnmja7liIblhazlj7jojrflj5blj5jnlLXnq5kKICAgIGdldEJkelNlbGVjdExpc3Q6IGZ1bmN0aW9uIGdldEJkelNlbGVjdExpc3QodmFsKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwoKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoIC8qI19fUFVSRV9fKi9yZWdlbmVyYXRvclJ1bnRpbWUubWFyayhmdW5jdGlvbiBfY2FsbGVlMygpIHsKICAgICAgICByZXR1cm4gcmVnZW5lcmF0b3JSdW50aW1lLndyYXAoZnVuY3Rpb24gX2NhbGxlZTMkKF9jb250ZXh0MykgewogICAgICAgICAgd2hpbGUgKDEpIHsKICAgICAgICAgICAgc3dpdGNoIChfY29udGV4dDMucHJldiA9IF9jb250ZXh0My5uZXh0KSB7CiAgICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgICAgX3RoaXM1LiRzZXQoX3RoaXM1LmZvcm0sICJiZHoiLCAiIik7CgogICAgICAgICAgICAgICAgX2NvbnRleHQzLm5leHQgPSAzOwogICAgICAgICAgICAgICAgcmV0dXJuICgwLCBfYmR4anpxcHouZ2V0QmR6U2VsZWN0TGlzdCkoewogICAgICAgICAgICAgICAgICBzc2R3Ym06IHZhbAogICAgICAgICAgICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzNS5iZHpMaXN0ID0gcmVzLmRhdGE7CiAgICAgICAgICAgICAgICB9KTsKCiAgICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQzLnN0b3AoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUzKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgLy/moLnmja7lj5jnlLXnq5nojrflj5bpl7TpmpQKICAgIGdldEpnU2VsZWN0TGlzdDogZnVuY3Rpb24gZ2V0SmdTZWxlY3RMaXN0KHZhbCkgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKCiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKCAvKiNfX1BVUkVfXyovcmVnZW5lcmF0b3JSdW50aW1lLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTQoKSB7CiAgICAgICAgcmV0dXJuIHJlZ2VuZXJhdG9yUnVudGltZS53cmFwKGZ1bmN0aW9uIF9jYWxsZWU0JChfY29udGV4dDQpIHsKICAgICAgICAgIHdoaWxlICgxKSB7CiAgICAgICAgICAgIHN3aXRjaCAoX2NvbnRleHQ0LnByZXYgPSBfY29udGV4dDQubmV4dCkgewogICAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICAgIF90aGlzNi4kc2V0KF90aGlzNi5mb3JtLCAiamciLCAiIik7CgogICAgICAgICAgICAgICAgX2NvbnRleHQ0Lm5leHQgPSAzOwogICAgICAgICAgICAgICAgcmV0dXJuICgwLCBfYmRzYnR6LmdldEpnRGF0YUxpc3RTZWxlY3RlZCkoewogICAgICAgICAgICAgICAgICBzc2JkejogdmFsCiAgICAgICAgICAgICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgICAgICAgX3RoaXM2LmpnTGlzdCA9IHJlcy5kYXRhOwogICAgICAgICAgICAgICAgfSk7CgogICAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0NC5zdG9wKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlNCk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8v6Ze06ZqU5LiL5ouJ5qGGY2hhbmdl5LqL5Lu2CiAgICBqZ0NoYW5nZTogZnVuY3Rpb24gamdDaGFuZ2UodmFsKSB7CiAgICAgIHZhciBfdGhpczcgPSB0aGlzOwoKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoIC8qI19fUFVSRV9fKi9yZWdlbmVyYXRvclJ1bnRpbWUubWFyayhmdW5jdGlvbiBfY2FsbGVlNSgpIHsKICAgICAgICByZXR1cm4gcmVnZW5lcmF0b3JSdW50aW1lLndyYXAoZnVuY3Rpb24gX2NhbGxlZTUkKF9jb250ZXh0NSkgewogICAgICAgICAgd2hpbGUgKDEpIHsKICAgICAgICAgICAgc3dpdGNoIChfY29udGV4dDUucHJldiA9IF9jb250ZXh0NS5uZXh0KSB7CiAgICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgICAgX3RoaXM3LiRzZXQoX3RoaXM3LmZvcm0sICJzYmx4IiwgIiIpOwoKICAgICAgICAgICAgICAgIF9jb250ZXh0NS5uZXh0ID0gMzsKICAgICAgICAgICAgICAgIHJldHVybiAoMCwgX2JsZ2suZ2V0Qmxna1NibHhCeUpnKSh7CiAgICAgICAgICAgICAgICAgIHNzamc6IHZhbAogICAgICAgICAgICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzNy5zYmx4TGlzdCA9IHJlcy5kYXRhOwogICAgICAgICAgICAgICAgfSk7CgogICAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0NS5zdG9wKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlNSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8v6K6+5aSH57G75Z6L5LiL5ouJ5qGGY2hhbmdl5LqL5Lu2CiAgICBzYmx4Q2hhbmdlOiBmdW5jdGlvbiBzYmx4Q2hhbmdlKHZhbCkgewogICAgICB2YXIgX3RoaXM4ID0gdGhpczsKCiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKCAvKiNfX1BVUkVfXyovcmVnZW5lcmF0b3JSdW50aW1lLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTYoKSB7CiAgICAgICAgcmV0dXJuIHJlZ2VuZXJhdG9yUnVudGltZS53cmFwKGZ1bmN0aW9uIF9jYWxsZWU2JChfY29udGV4dDYpIHsKICAgICAgICAgIHdoaWxlICgxKSB7CiAgICAgICAgICAgIHN3aXRjaCAoX2NvbnRleHQ2LnByZXYgPSBfY29udGV4dDYubmV4dCkgewogICAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICAgIC8v6YeN572u6KGo5Y2V5Y+K5a2X5YW4CiAgICAgICAgICAgICAgICBfdGhpczguJHNldChfdGhpczguZm9ybSwgInNiaWQiLCAiIik7CgogICAgICAgICAgICAgICAgX3RoaXM4LiRzZXQoX3RoaXM4LmZvcm0sICJ0eXBlIiwgIiIpOwoKICAgICAgICAgICAgICAgIF90aGlzOC5zYkxpc3QgPSBbXTsKICAgICAgICAgICAgICAgIF90aGlzOC5tc0xpc3QgPSBbXTsKICAgICAgICAgICAgICAgIF90aGlzOC5seExpc3QgPSBbXTsgLy/ojrflj5borr7lpIfkuIvmi4nmoYYKCiAgICAgICAgICAgICAgICBfY29udGV4dDYubmV4dCA9IDc7CiAgICAgICAgICAgICAgICByZXR1cm4gKDAsIF9ibGdrLmdldFNiKSh7CiAgICAgICAgICAgICAgICAgIHNibHg6IHZhbCwKICAgICAgICAgICAgICAgICAgc3NqZzogX3RoaXM4LmZvcm0uamcKICAgICAgICAgICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICAgICAgICBfdGhpczguc2JMaXN0ID0gcmVzLmRhdGE7CiAgICAgICAgICAgICAgICB9KTsKCiAgICAgICAgICAgICAgY2FzZSA3OgogICAgICAgICAgICAgICAgX2NvbnRleHQ2Lm5leHQgPSA5OwogICAgICAgICAgICAgICAgcmV0dXJuICgwLCBfYmxna2J6ay5nZXRMeCkoewogICAgICAgICAgICAgICAgICBzYmx4OiB2YWwKICAgICAgICAgICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICAgICAgICByZXMuZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICAgICAgICAgICAgX3RoaXM4Lmx4TGlzdC5wdXNoKHsKICAgICAgICAgICAgICAgICAgICAgIGxhYmVsOiBpdGVtLmxhYmVsLAogICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IGl0ZW0ubGFiZWwKICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICB9KTsKCiAgICAgICAgICAgICAgY2FzZSA5OgogICAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ2LnN0b3AoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWU2KTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgbHhDaGFuZ2U6IGZ1bmN0aW9uIGx4Q2hhbmdlKHZhbCkgewogICAgICB2YXIgX3RoaXM5ID0gdGhpczsKCiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKCAvKiNfX1BVUkVfXyovcmVnZW5lcmF0b3JSdW50aW1lLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTcoKSB7CiAgICAgICAgcmV0dXJuIHJlZ2VuZXJhdG9yUnVudGltZS53cmFwKGZ1bmN0aW9uIF9jYWxsZWU3JChfY29udGV4dDcpIHsKICAgICAgICAgIHdoaWxlICgxKSB7CiAgICAgICAgICAgIHN3aXRjaCAoX2NvbnRleHQ3LnByZXYgPSBfY29udGV4dDcubmV4dCkgewogICAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICAgIC8v6YeN572u6KGo5Y2V5Y+K5a2X5YW4CiAgICAgICAgICAgICAgICBfdGhpczkuJHNldChfdGhpczkuZm9ybSwgIm1zIiwgIiIpOwoKICAgICAgICAgICAgICAgIF90aGlzOS5tc0xpc3QgPSBbXTsgLy/ojrflj5bkuI3oia/lt6XlhrXmj4/ov7DkuIvmi4nmoYYKCiAgICAgICAgICAgICAgICBfY29udGV4dDcubmV4dCA9IDQ7CiAgICAgICAgICAgICAgICByZXR1cm4gKDAsIF9ibGdrYnprLmdldE1zKSh7CiAgICAgICAgICAgICAgICAgIHNibHg6IF90aGlzOS5mb3JtLnNibHgsCiAgICAgICAgICAgICAgICAgIHR5cGU6IHZhbAogICAgICAgICAgICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzOS5tc0xpc3QgPSByZXMuZGF0YTsKICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICBjYXNlIDQ6CiAgICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDcuc3RvcCgpOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTcpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvL+S4jeiJr+W3peWGteaPj+i/sOS4i+aLieahhmNoYW5nZeS6i+S7tgogICAgbXNDaGFuZ2U6IGZ1bmN0aW9uIG1zQ2hhbmdlKHZhbCkgewogICAgICB2YXIgX3RoaXMxMCA9IHRoaXM7CgogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSggLyojX19QVVJFX18qL3JlZ2VuZXJhdG9yUnVudGltZS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWU4KCkgewogICAgICAgIHZhciBtc0xhYmVsOwogICAgICAgIHJldHVybiByZWdlbmVyYXRvclJ1bnRpbWUud3JhcChmdW5jdGlvbiBfY2FsbGVlOCQoX2NvbnRleHQ4KSB7CiAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0OC5wcmV2ID0gX2NvbnRleHQ4Lm5leHQpIHsKICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICBfdGhpczEwLiRzZXQoX3RoaXMxMC5mb3JtLCAiZmx5aiIsICIiKTsKCiAgICAgICAgICAgICAgICBfdGhpczEwLmZseWpMaXN0ID0gW107CiAgICAgICAgICAgICAgICBtc0xhYmVsID0gIiI7CgogICAgICAgICAgICAgICAgX3RoaXMxMC5tc0xpc3QuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICAgICAgICBpZiAoaXRlbS52YWx1ZSA9PSB2YWwpIHsKICAgICAgICAgICAgICAgICAgICBtc0xhYmVsID0gaXRlbS5sYWJlbDsKICAgICAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0pOyAvL+iOt+WPluWIhuexu+S+neaNruS4i+aLieahhgoKCiAgICAgICAgICAgICAgICBfY29udGV4dDgubmV4dCA9IDY7CiAgICAgICAgICAgICAgICByZXR1cm4gKDAsIF9ibGdrYnprLmdldEZseWopKHsKICAgICAgICAgICAgICAgICAgc2JseDogX3RoaXMxMC5mb3JtLnNibHgsCiAgICAgICAgICAgICAgICAgIG1zOiBtc0xhYmVsCiAgICAgICAgICAgICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgICAgICAgX3RoaXMxMC5mbHlqTGlzdCA9IHJlcy5kYXRhOwogICAgICAgICAgICAgICAgfSk7CgogICAgICAgICAgICAgIGNhc2UgNjoKICAgICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0OC5zdG9wKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlOCk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8v5L+d5a2Y6KGo5Y2VCiAgICBzYXZlRnVuOiBmdW5jdGlvbiBzYXZlRnVuKCkgewogICAgICB2YXIgX3RoaXMxMSA9IHRoaXM7CgogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSggLyojX19QVVJFX18qL3JlZ2VuZXJhdG9yUnVudGltZS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWU5KCkgewogICAgICAgIHJldHVybiByZWdlbmVyYXRvclJ1bnRpbWUud3JhcChmdW5jdGlvbiBfY2FsbGVlOSQoX2NvbnRleHQ5KSB7CiAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0OS5wcmV2ID0gX2NvbnRleHQ5Lm5leHQpIHsKICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICBfdGhpczExLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgICAgICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgICAgICAgICAgIC8v6K6+572u6Jma5ouf5a2X5q6155So5LqO5L+d5a2Y6K6+5aSH5byC5bi45Y+K5LqL5pWF5aSE55CG5pWw5o2uCiAgICAgICAgICAgICAgICAgICAgdmFyIG1zID0gIiI7CiAgICAgICAgICAgICAgICAgICAgdmFyIGZseWpDbiA9ICIiOwoKICAgICAgICAgICAgICAgICAgICBfdGhpczExLm1zTGlzdC5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICAgICAgICAgICAgICBpZiAoaXRlbS52YWx1ZSA9PSBfdGhpczExLmZvcm0ubXMpIHsKICAgICAgICAgICAgICAgICAgICAgICAgbXMgPSBpdGVtLmxhYmVsOwogICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgfSk7CgogICAgICAgICAgICAgICAgICAgIF90aGlzMTEuZmx5akxpc3QuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICAgICAgICAgICAgaWYgKGl0ZW0udmFsdWUgPT0gX3RoaXMxMS5mb3JtLmZseWopIHsKICAgICAgICAgICAgICAgICAgICAgICAgZmx5akNuID0gaXRlbS5sYWJlbDsKICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICAgICAgICBpZiAoT2JqZWN0LmtleXMoX3RoaXMxMS5zYkRhdGEpLmxlbmd0aCAhPT0gMCkgewogICAgICAgICAgICAgICAgICAgICAgX3RoaXMxMS5mb3JtLmZncyA9IF90aGlzMTEuc2JEYXRhLnNzZ3M7CiAgICAgICAgICAgICAgICAgICAgICBfdGhpczExLmZvcm0uYmR6ID0gX3RoaXMxMS5zYkRhdGEuc3NiZHo7CiAgICAgICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgICAgICBpZiAobXMpIHsKICAgICAgICAgICAgICAgICAgICAgIF90aGlzMTEuZm9ybS5tcyA9IG1zOwogICAgICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAgICAgX3RoaXMxMS5mb3JtLmZseWpDbiA9IGZseWpDbjsKCiAgICAgICAgICAgICAgICAgICAgaWYgKF90aGlzMTEuZm9ybS54Y3NqKSB7CiAgICAgICAgICAgICAgICAgICAgICBfdGhpczExLmZvcm0uaXNDbGVhbiA9ICLmmK8iOwogICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgICBfdGhpczExLmZvcm0uaXNDbGVhbiA9ICLlkKYiOyAvL+iuvue9ruaYr+WQpua2iOmZpOS4uuWQpgogICAgICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAgICAgX3RoaXMxMS4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgICAgICAgICAgICAgICAgX3RoaXMxMS5zYXZlTG9hZGluZyA9IF9lbGVtZW50VWkuTG9hZGluZy5zZXJ2aWNlKHsKICAgICAgICAgICAgICAgICAgICAgICAgbG9jazogdHJ1ZSwKICAgICAgICAgICAgICAgICAgICAgICAgLy9sb2Nr55qE5L+u5pS556ymLS3pu5jorqTmmK9mYWxzZQogICAgICAgICAgICAgICAgICAgICAgICB0ZXh0OiAi5L+d5a2Y5Lit77yM6K+356iN5ZCOIiwKICAgICAgICAgICAgICAgICAgICAgICAgLy/mmL7npLrlnKjliqDovb3lm77moIfkuIvmlrnnmoTliqDovb3mlofmoYgKICAgICAgICAgICAgICAgICAgICAgICAgc3Bpbm5lcjogImVsLWljb24tbG9hZGluZyIsCiAgICAgICAgICAgICAgICAgICAgICAgIC8v6Ieq5a6a5LmJ5Yqg6L295Zu+5qCH57G75ZCNCiAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICJyZ2JhKDAsIDAsIDAsIDAuNykiLAogICAgICAgICAgICAgICAgICAgICAgICAvL+mBrue9qeWxguminOiJsgogICAgICAgICAgICAgICAgICAgICAgICB0YXJnZXQ6IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoIiNibGdrX2RpdiIpCiAgICAgICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgICAgICAgICgwLCBfYmxnay5zYXZlT3JVcGRhdGUpKF90aGlzMTEuZm9ybSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgX3RoaXMxMS4kbWVzc2FnZS5zdWNjZXNzKCLmk43kvZzmiJDlip8hIik7CgogICAgICAgICAgICAgICAgICAgICAgICAgIF90aGlzMTEuZ2V0RGF0YSgpOwoKICAgICAgICAgICAgICAgICAgICAgICAgICBfdGhpczExLnNhdmVMb2FkaW5nLmNsb3NlKCk7CgogICAgICAgICAgICAgICAgICAgICAgICAgIF90aGlzMTEuaXNTaG93ID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgIF90aGlzMTEuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLmoKHpqozmnKrpgJrov4ciCiAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICBjYXNlIDE6CiAgICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDkuc3RvcCgpOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTkpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICBmaWx0ZXJSZXNldDogZnVuY3Rpb24gZmlsdGVyUmVzZXQoKSB7CiAgICAgIHRoaXMuZmlsdGVySW5mby5maWVsZExpc3QuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIGlmIChpdGVtLnR5cGUgPT09ICJjaGVja2JveCIpIHsKICAgICAgICAgIGl0ZW0uY2hlY2tib3hWYWx1ZSA9IFtdOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy/liIbpobXmn6Xor6LliJfooagKICAgIGdldERhdGE6IGZ1bmN0aW9uIGdldERhdGEocGFyYW1zKSB7CiAgICAgIHZhciBfdGhpczEyID0gdGhpczsKCiAgICAgIC8v5Y+C5pWw5ZCI5bm2CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIHZhciBwYXJhbSA9ICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSgoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIHRoaXMucXVlcnlQYXJhbXMpLCBwYXJhbXMpOwogICAgICB0aGlzLnF1ZXJ5UGFyYW1zID0gcGFyYW07CiAgICAgICgwLCBfYmxnay5nZXREYXRhKShwYXJhbSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXMxMi50YWJsZUFuZFBhZ2VJbmZvLnRhYmxlRGF0YSA9IHJlcy5kYXRhLnJlY29yZHM7CiAgICAgICAgX3RoaXMxMi50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnRvdGFsID0gcmVzLmRhdGEudG90YWw7CiAgICAgICAgX3RoaXMxMi5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKCiAgICAvKioKICAgICAqIOaWsOWinuS4jeiJr+W3peWGtQogICAgICovCiAgICBhZGRSb3c6IGZ1bmN0aW9uIGFkZFJvdygpIHsKICAgICAgdGhpcy50aXRsZSA9ICLmlrDlop7kuI3oia/lt6XlhrUiOwogICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZTsKICAgICAgdGhpcy5mb3JtID0ge307CiAgICAgIHRoaXMuZm9ybS5scnIgPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLm5hbWU7CiAgICAgIHRoaXMuZm9ybS5scnJtYyA9IHRoaXMuJHN0b3JlLmdldHRlcnMubmlja05hbWU7CiAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICJmeHNqIiwgKDAsIF9oYW5kbGVEYXRhLmZvcm1hdHRlckRhdGVUaW1lKShuZXcgRGF0ZSgpLCAieXl5eS1NTS1kZCIpKTsKICAgICAgdGhpcy5pc1Nob3cgPSB0cnVlOwogICAgfSwKCiAgICAvKioKICAgICAqIOe8lui+kQogICAgICovCiAgICBoYW5kbGVVcGRhdGU6IGZ1bmN0aW9uIGhhbmRsZVVwZGF0ZShyb3cpIHsKICAgICAgdmFyIF90aGlzMTMgPSB0aGlzOwoKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoIC8qI19fUFVSRV9fKi9yZWdlbmVyYXRvclJ1bnRpbWUubWFyayhmdW5jdGlvbiBfY2FsbGVlMTAoKSB7CiAgICAgICAgcmV0dXJuIHJlZ2VuZXJhdG9yUnVudGltZS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUxMCQoX2NvbnRleHQxMCkgewogICAgICAgICAgd2hpbGUgKDEpIHsKICAgICAgICAgICAgc3dpdGNoIChfY29udGV4dDEwLnByZXYgPSBfY29udGV4dDEwLm5leHQpIHsKICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICBfdGhpczEzLnNhdmVMb2FkaW5nID0gX2VsZW1lbnRVaS5Mb2FkaW5nLnNlcnZpY2UoewogICAgICAgICAgICAgICAgICBsb2NrOiB0cnVlLAogICAgICAgICAgICAgICAgICAvL2xvY2vnmoTkv67mlLnnrKYtLem7mOiupOaYr2ZhbHNlCiAgICAgICAgICAgICAgICAgIHRleHQ6ICLliqDovb3kuK3vvIzor7fnqI3lkI4iLAogICAgICAgICAgICAgICAgICAvL+aYvuekuuWcqOWKoOi9veWbvuagh+S4i+aWueeahOWKoOi9veaWh+ahiAogICAgICAgICAgICAgICAgICBzcGlubmVyOiAiZWwtaWNvbi1sb2FkaW5nIiwKICAgICAgICAgICAgICAgICAgLy/oh6rlrprkuYnliqDovb3lm77moIfnsbvlkI0KICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogInJnYmEoMCwgMCwgMCwgMC43KSIgLy/pga7nvanlsYLpopzoibIKCiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIF90aGlzMTMudGl0bGUgPSAi57yW6L6R5LiN6Imv5bel5Ya1IjsKICAgICAgICAgICAgICAgIF90aGlzMTMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICAgICAgICAgICAgX3RoaXMxMy5mb3JtID0gKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCByb3cpOwogICAgICAgICAgICAgICAgX3RoaXMxMy5mb3JtLmZncyA9IHJvdy5mZ3M7CiAgICAgICAgICAgICAgICBfdGhpczEzLmZvcm0uYmR6ID0gcm93LmJkejsgLy/lpITnkIbkuIvmi4nmoYblm57mmL4KCiAgICAgICAgICAgICAgICBfdGhpczEzLmx4TGlzdCA9IFtdOwogICAgICAgICAgICAgICAgX3RoaXMxMy5tc0xpc3QgPSBbXTsKICAgICAgICAgICAgICAgIF90aGlzMTMuZmx5akxpc3QgPSBbXTsgLy/ojrflj5bkuI3oia/lt6XlhrXnsbvlnovkuIvmi4nmoYYKCiAgICAgICAgICAgICAgICBfY29udGV4dDEwLm5leHQgPSAxMTsKICAgICAgICAgICAgICAgIHJldHVybiAoMCwgX2JsZ2tiemsuZ2V0THgpKHsKICAgICAgICAgICAgICAgICAgc2JseDogcm93LnNibHgKICAgICAgICAgICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICAgICAgICByZXMuZGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICAgICAgICAgICAgX3RoaXMxMy5seExpc3QucHVzaCh7CiAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogaXRlbS5sYWJlbCwKICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiBpdGVtLmxhYmVsCiAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICAgICAgX3RoaXMxMy5seExpc3QucHVzaCh7CiAgICAgICAgICAgICAgICAgICAgbGFiZWw6ICLlhbbku5YiLAogICAgICAgICAgICAgICAgICAgIHZhbHVlOiAi5YW25LuWIgogICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICBjYXNlIDExOgogICAgICAgICAgICAgICAgX2NvbnRleHQxMC5uZXh0ID0gMTM7CiAgICAgICAgICAgICAgICByZXR1cm4gKDAsIF9ibGdrYnprLmdldE1zKSh7CiAgICAgICAgICAgICAgICAgIHNibHg6IF90aGlzMTMuZm9ybS5zYmx4LAogICAgICAgICAgICAgICAgICB0eXBlOiByb3cudHlwZQogICAgICAgICAgICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzMTMubXNMaXN0ID0gcmVzLmRhdGE7CiAgICAgICAgICAgICAgICB9KTsKCiAgICAgICAgICAgICAgY2FzZSAxMzoKICAgICAgICAgICAgICAgIGlmIChfdGhpczEzLm1zTGlzdC5sZW5ndGgpIHsKICAgICAgICAgICAgICAgICAgX3RoaXMxMy4kc2V0KF90aGlzMTMuZm9ybSwgIm1zIiwgcm93LmZseWopOwogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgX3RoaXMxMy4kc2V0KF90aGlzMTMuZm9ybSwgIm1zIiwgcm93Lm1zKTsKICAgICAgICAgICAgICAgIH0gLy/ojrflj5bliIbnsbvkvp3mja7kuIvmi4nmoYYKCgogICAgICAgICAgICAgICAgX2NvbnRleHQxMC5uZXh0ID0gMTY7CiAgICAgICAgICAgICAgICByZXR1cm4gKDAsIF9ibGdrYnprLmdldEZseWopKHsKICAgICAgICAgICAgICAgICAgc2JseDogX3RoaXMxMy5mb3JtLnNibHgsCiAgICAgICAgICAgICAgICAgIG1zOiByb3cubXMKICAgICAgICAgICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICAgICAgICBfdGhpczEzLmZseWpMaXN0ID0gcmVzLmRhdGE7CiAgICAgICAgICAgICAgICB9KTsKCiAgICAgICAgICAgICAgY2FzZSAxNjoKICAgICAgICAgICAgICAgIC8v5YWz6Zet6YGu572p5bGCCiAgICAgICAgICAgICAgICBfdGhpczEzLnNhdmVMb2FkaW5nLmNsb3NlKCk7CgogICAgICAgICAgICAgICAgX3RoaXMxMy5pc1Nob3cgPSB0cnVlOwoKICAgICAgICAgICAgICBjYXNlIDE4OgogICAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQxMC5zdG9wKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMTApOwogICAgICB9KSkoKTsKICAgIH0sCgogICAgLyoqCiAgICAgKiDor6bmg4Xmn6XnnIsKICAgICAqLwogICAgZ2V0RGV0YWlsczogZnVuY3Rpb24gZ2V0RGV0YWlscyhyb3cpIHsKICAgICAgdGhpcy5mb3JtID0gKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCByb3cpOyAvL+WkhOeQhuWbnuaYvumXrumimAoKICAgICAgdGhpcy5mb3JtLmZseWogPSByb3cuZmx5akNuOwogICAgICB0aGlzLmlzRGlzYWJsZWQgPSB0cnVlOwogICAgICB0aGlzLmlzU2hvdyA9IHRydWU7CiAgICAgIHRoaXMudGl0bGUgPSAi5LiN6Imv5bel5Ya16K+m5oOFIjsKICAgIH0sCgogICAgLyoqCiAgICAgKiDliKDpmaQKICAgICAqLwogICAgaGFuZGxlRGVsZXRlQnlJZDogZnVuY3Rpb24gaGFuZGxlRGVsZXRlQnlJZChpZCkgewogICAgICB2YXIgX3RoaXMxNCA9IHRoaXM7CgogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSggLyojX19QVVJFX18qL3JlZ2VuZXJhdG9yUnVudGltZS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUxMSgpIHsKICAgICAgICByZXR1cm4gcmVnZW5lcmF0b3JSdW50aW1lLndyYXAoZnVuY3Rpb24gX2NhbGxlZTExJChfY29udGV4dDExKSB7CiAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0MTEucHJldiA9IF9jb250ZXh0MTEubmV4dCkgewogICAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICAgIF90aGlzMTQuJGNvbmZpcm0oIuatpOaTjeS9nOWwhuawuOS5heWIoOmZpOivpeaVsOaNriwg5piv5ZCm57un57utPyIsICLmj5DnpLoiLCB7CiAgICAgICAgICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICAgICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICAgICAgICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgICAgICAgICgwLCBfYmxnay5yZW1vdmVCbGdrKShbaWRdKS50aGVuKGZ1bmN0aW9uIChfcmVmKSB7CiAgICAgICAgICAgICAgICAgICAgdmFyIGNvZGUgPSBfcmVmLmNvZGU7CgogICAgICAgICAgICAgICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICAgICAgICAgIF90aGlzMTQuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsCiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTmiJDlip8hIgogICAgICAgICAgICAgICAgICAgICAgfSk7CgogICAgICAgICAgICAgICAgICAgICAgX3RoaXMxNC5nZXREYXRhKCk7CiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICAgIF90aGlzMTQuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAiZXJyb3IiLAogICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5Yig6Zmk5aSx6LSlISIKICAgICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzMTQuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgICAgIHR5cGU6ICJpbmZvIiwKICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5bey5Y+W5raI5Yig6ZmkIgogICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICBjYXNlIDE6CiAgICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDExLnN0b3AoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUxMSk7CiAgICAgIH0pKSgpOwogICAgfSwKCiAgICAvKioKICAgICAqIOa2iOmZpAogICAgICovCiAgICBoYW5kbGVDbGVhbjogZnVuY3Rpb24gaGFuZGxlQ2xlYW4ocm93KSB7CiAgICAgIHZhciBfdGhpczE1ID0gdGhpczsKCiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKCAvKiNfX1BVUkVfXyovcmVnZW5lcmF0b3JSdW50aW1lLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTEyKCkgewogICAgICAgIHJldHVybiByZWdlbmVyYXRvclJ1bnRpbWUud3JhcChmdW5jdGlvbiBfY2FsbGVlMTIkKF9jb250ZXh0MTIpIHsKICAgICAgICAgIHdoaWxlICgxKSB7CiAgICAgICAgICAgIHN3aXRjaCAoX2NvbnRleHQxMi5wcmV2ID0gX2NvbnRleHQxMi5uZXh0KSB7CiAgICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgICAgX3RoaXMxNS4kY29uZmlybSgi5piv5ZCm56Gu5a6a5raI6Zmk5LiN6Imv5bel5Ya1PyIsICLmj5DnpLoiLCB7CiAgICAgICAgICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICAgICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICAgICAgICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgICAgICAgIHZhciBmb3JtMSA9ICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7fSwgcm93KTsKICAgICAgICAgICAgICAgICAgKDAsIF9ibGdrLmNsZWFuKShmb3JtMSkudGhlbihmdW5jdGlvbiAoX3JlZjIpIHsKICAgICAgICAgICAgICAgICAgICB2YXIgY29kZSA9IF9yZWYyLmNvZGU7CgogICAgICAgICAgICAgICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICAgICAgICAgIF90aGlzMTUuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsCiAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLmtojpmaTmiJDlip8hIgogICAgICAgICAgICAgICAgICAgICAgfSk7CgogICAgICAgICAgICAgICAgICAgICAgX3RoaXMxNS5nZXREYXRhKCk7CiAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICAgIF90aGlzMTUuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgICAgICAgICB0eXBlOiAiZXJyb3IiLAogICAgICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5raI6Zmk5aSx6LSlISIKICAgICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzMTUuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgICAgIHR5cGU6ICJpbmZvIiwKICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5bey5Y+W5raI5raI6ZmkIgogICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICBjYXNlIDE6CiAgICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDEyLnN0b3AoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUxMik7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8v5by55qGG5YWz6Zet5LqL5Lu2CiAgICBjbG9zZUZ1bjogZnVuY3Rpb24gY2xvc2VGdW4oKSB7CiAgICAgIHRoaXMuZm9ybSA9IHt9OwogICAgICB0aGlzLmlzU2hvdyA9IGZhbHNlOwogICAgfQogIH0KfTsKZXhwb3J0cy5kZWZhdWx0ID0gX2RlZmF1bHQ7"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AA8SA;;AASA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,MADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,QAAA,EAAA;AAAA,GAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA,MAAA,YAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,YADA;AAEA,MAAA,MAAA,EAAA,KAFA;AAEA;AACA,MAAA,OAAA,EAAA,KAHA;AAIA;AACA,MAAA,UAAA,EAAA,KALA;AAMA,MAAA,IAAA,EAAA,EANA;AAOA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,QAAA,EAAA,EADA;AAEA,QAAA,OAAA,EAAA;AAFA,OARA;AAYA;AACA,MAAA,KAAA,EAAA,EAbA;AAcA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA;AACA,UAAA,KAAA,EAAA,EAFA;AAGA,UAAA,IAAA,EAAA,EAHA;AAIA,UAAA,IAAA,EAAA,EAJA;AAKA,UAAA,IAAA,EAAA,EALA;AAMA,UAAA,OAAA,EAAA;AANA,SADA;AASA,QAAA,SAAA,EAAA,CACA;AACA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAJA,EAKA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA,EAJA;AAKA,UAAA,SAAA,EAAA,IALA;AAMA,UAAA,UAAA,EAAA;AANA,SALA,EAaA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,SAHA;AAIA,UAAA,OAAA,EAAA,EAJA;AAKA,UAAA,SAAA,EAAA;AALA,SAbA;AATA,OAdA;AA6CA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,YAAA,EAAA;AAAA,SAPA;AAQA,QAAA,SAAA,EAAA,EARA;AASA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SARA,EASA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SATA,EAUA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAVA;AATA,OA7CA;AAmEA,MAAA,KAAA,EAAA;AACA,QAAA,KAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CADA;AAEA,QAAA,KAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAFA;AAGA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,QAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAHA;AAIA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,YAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,QAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAVA;AAWA,QAAA,EAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAXA;AAcA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAdA,OAnEA;AAqFA,MAAA,OAAA,EAAA,EArFA;AAqFA;AACA,MAAA,OAAA,EAAA,EAtFA;AAsFA;AACA,MAAA,MAAA,EAAA,EAvFA;AAuFA;AACA,MAAA,QAAA,EAAA,EAxFA;AAwFA;AACA,MAAA,MAAA,EAAA,EAzFA;AAyFA;AACA,MAAA,MAAA,EAAA,EA1FA;AA0FA;AACA,MAAA,QAAA,EAAA,EA3FA;AA2FA;AACA,MAAA,MAAA,EAAA,EA5FA;AA4FA;AACA,MAAA,WAAA,EAAA,IA7FA;AA6FA;AACA,MAAA,cAAA,EAAA,KA9FA;AA8FA;AACA,MAAA,MAAA,EAAA,EA/FA,CA+FA;;AA/FA,KAAA;AAiGA,GArGA;AAsGA,EAAA,OAtGA,qBAsGA;AACA;AACA,SAAA,OAAA;AACA,SAAA,UAAA,GAHA,CAGA;AACA,GA1GA;AA2GA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,WAFA,yBAEA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,KAJA;AAKA;AACA,IAAA,cANA,4BAMA;AACA,WAAA,cAAA,GAAA,KAAA;AACA,KARA;AASA;AACA,IAAA,YAVA,wBAUA,MAVA,EAUA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,KAAA,CAAA,MAAA,GAAA,MAAA;;AACA,gBAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,OAAA,EAAA,MAAA,CAAA,QAAA;;AACA,gBAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,OAAA,EAAA,MAAA,CAAA,KAAA;;AACA,gBAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,CAAA,IAAA;;AACA,gBAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,QAAA,EAAA,MAAA,CAAA,MAAA;;AACA,gBAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,CAAA,MAAA;;AACA,gBAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,CAAA,IAAA;;AACA,gBAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,CAAA,KAAA;;AACA,gBAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,MAAA,EAAA,EAAA;;AACA,gBAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,IAAA,EAAA,EAAA;;AACA,gBAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,MAAA,EAAA,EAAA,EAXA,CAYA;;;AACA,gBAAA,KAAA,CAAA,MAAA,GAAA,EAAA;AAbA;AAAA,uBAcA,oBAAA;AAAA,kBAAA,IAAA,EAAA,MAAA,CAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,oBAAA,KAAA,CAAA,MAAA,CAAA,IAAA,CAAA;AAAA,sBAAA,KAAA,EAAA,IAAA,CAAA,KAAA;AAAA,sBAAA,KAAA,EAAA,IAAA,CAAA;AAAA,qBAAA;AACA,mBAFA;;AAGA,kBAAA,KAAA,CAAA,MAAA,CAAA,IAAA,CAAA;AAAA,oBAAA,KAAA,EAAA,IAAA;AAAA,oBAAA,KAAA,EAAA;AAAA,mBAAA;AACA,iBALA,CAdA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoBA,KA9BA;AA+BA;AACA,IAAA,UAhCA,wBAgCA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,MAAA,CAAA,SAAA,EADA;;AAAA;AAAA;AAAA,uBAEA,MAAA,CAAA,cAAA,EAFA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,KAnCA;AAoCA;AACA,IAAA,cArCA,4BAqCA;AAAA;;AACA,gCAAA;AAAA,QAAA,EAAA,EAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,KAAA,MAAA,EAAA;AACA,YAAA,IAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;AACA,mBAAA,KAAA;AACA;AACA,SALA;AAMA,OAPA;AAQA,KA9CA;AA+CA;AACA,IAAA,SAhDA,uBAgDA;AAAA;;AACA,iCAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,MAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA,KAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA;AAAA,WAAA;AACA,SAFA;;AAGA,QAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,KAAA,SAAA,EAAA;AACA,YAAA,IAAA,CAAA,OAAA,GAAA,MAAA;AACA,mBAAA,KAAA;AACA;AACA,SALA;AAMA,OAXA;AAYA,KA7DA;AA8DA;AACA,IAAA,gBA/DA,4BA+DA,GA/DA,EA+DA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,EAAA,KAAA,EAAA,EAAA;;AADA;AAAA,uBAEA,gCAAA;AAAA,kBAAA,MAAA,EAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,MAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,CAFA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,KApEA;AAqEA;AACA,IAAA,eAtEA,2BAsEA,GAtEA,EAsEA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,EAAA,IAAA,EAAA,EAAA;;AADA;AAAA,uBAEA,mCAAA;AAAA,kBAAA,KAAA,EAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,MAAA,CAAA,MAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,CAFA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,KA3EA;AA4EA;AACA,IAAA,QA7EA,oBA6EA,GA7EA,EA6EA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,EAAA,MAAA,EAAA,EAAA;;AADA;AAAA,uBAEA,2BAAA;AAAA,kBAAA,IAAA,EAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,CAFA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,KAlFA;AAmFA;AACA,IAAA,UApFA,sBAoFA,GApFA,EAoFA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,EAAA,MAAA,EAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,EAAA,MAAA,EAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,MAAA,GAAA,EAAA;AACA,gBAAA,MAAA,CAAA,MAAA,GAAA,EAAA;AACA,gBAAA,MAAA,CAAA,MAAA,GAAA,EAAA,CANA,CAOA;;AAPA;AAAA,uBAQA,iBAAA;AAAA,kBAAA,IAAA,EAAA,GAAA;AAAA,kBAAA,IAAA,EAAA,MAAA,CAAA,IAAA,CAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,MAAA,CAAA,MAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,CARA;;AAAA;AAAA;AAAA,uBAYA,oBAAA;AAAA,kBAAA,IAAA,EAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,oBAAA,MAAA,CAAA,MAAA,CAAA,IAAA,CAAA;AAAA,sBAAA,KAAA,EAAA,IAAA,CAAA,KAAA;AAAA,sBAAA,KAAA,EAAA,IAAA,CAAA;AAAA,qBAAA;AACA,mBAFA;AAGA,iBAJA,CAZA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA,KArGA;AAsGA,IAAA,QAtGA,oBAsGA,GAtGA,EAsGA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,EAAA,IAAA,EAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,MAAA,GAAA,EAAA,CAHA,CAIA;;AAJA;AAAA,uBAKA,oBAAA;AAAA,kBAAA,IAAA,EAAA,MAAA,CAAA,IAAA,CAAA,IAAA;AAAA,kBAAA,IAAA,EAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,MAAA,CAAA,MAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,CALA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA,KA9GA;AA+GA;AACA,IAAA,QAhHA,oBAgHA,GAhHA,EAgHA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,IAAA,CAAA,OAAA,CAAA,IAAA,EAAA,MAAA,EAAA,EAAA;;AACA,gBAAA,OAAA,CAAA,QAAA,GAAA,EAAA;AACA,gBAAA,OAHA,GAGA,EAHA;;AAIA,gBAAA,OAAA,CAAA,MAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,sBAAA,IAAA,CAAA,KAAA,IAAA,GAAA,EAAA;AACA,oBAAA,OAAA,GAAA,IAAA,CAAA,KAAA;AACA,2BAAA,KAAA;AACA;AACA,iBALA,EAJA,CAUA;;;AAVA;AAAA,uBAWA,sBAAA;AAAA,kBAAA,IAAA,EAAA,OAAA,CAAA,IAAA,CAAA,IAAA;AAAA,kBAAA,EAAA,EAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,OAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,CAXA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA,KA9HA;AA+HA;AACA,IAAA,OAhIA,qBAgIA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,sBAAA,KAAA,EAAA;AACA;AACA,wBAAA,EAAA,GAAA,EAAA;AACA,wBAAA,MAAA,GAAA,EAAA;;AACA,oBAAA,OAAA,CAAA,MAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,0BAAA,IAAA,CAAA,KAAA,IAAA,OAAA,CAAA,IAAA,CAAA,EAAA,EAAA;AACA,wBAAA,EAAA,GAAA,IAAA,CAAA,KAAA;AACA,+BAAA,KAAA;AACA;AACA,qBALA;;AAMA,oBAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,0BAAA,IAAA,CAAA,KAAA,IAAA,OAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AACA,wBAAA,MAAA,GAAA,IAAA,CAAA,KAAA;AACA,+BAAA,KAAA;AACA;AACA,qBALA;;AAMA,wBAAA,MAAA,CAAA,IAAA,CAAA,OAAA,CAAA,MAAA,EAAA,MAAA,KAAA,CAAA,EAAA;AACA,sBAAA,OAAA,CAAA,IAAA,CAAA,GAAA,GAAA,OAAA,CAAA,MAAA,CAAA,IAAA;AACA,sBAAA,OAAA,CAAA,IAAA,CAAA,GAAA,GAAA,OAAA,CAAA,MAAA,CAAA,KAAA;AACA;;AACA,wBAAA,EAAA,EAAA;AACA,sBAAA,OAAA,CAAA,IAAA,CAAA,EAAA,GAAA,EAAA;AACA;;AACA,oBAAA,OAAA,CAAA,IAAA,CAAA,MAAA,GAAA,MAAA;;AACA,wBAAA,OAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AACA,sBAAA,OAAA,CAAA,IAAA,CAAA,OAAA,GAAA,GAAA;AACA,qBAFA,MAEA;AACA,sBAAA,OAAA,CAAA,IAAA,CAAA,OAAA,GAAA,GAAA,CADA,CACA;AACA;;AACA,oBAAA,OAAA,CAAA,SAAA,CAAA,YAAA;AACA,sBAAA,OAAA,CAAA,WAAA,GAAA,mBAAA,OAAA,CAAA;AACA,wBAAA,IAAA,EAAA,IADA;AACA;AACA,wBAAA,IAAA,EAAA,SAFA;AAEA;AACA,wBAAA,OAAA,EAAA,iBAHA;AAGA;AACA,wBAAA,UAAA,EAAA,oBAJA;AAIA;AACA,wBAAA,MAAA,EAAA,QAAA,CAAA,aAAA,CAAA,WAAA;AALA,uBAAA,CAAA;AAOA,8CAAA,OAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,4BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,0BAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,OAAA;;AACA,0BAAA,OAAA,CAAA,OAAA;;AACA,0BAAA,OAAA,CAAA,WAAA,CAAA,KAAA;;AACA,0BAAA,OAAA,CAAA,MAAA,GAAA,KAAA;AACA;AACA,uBAPA;AAQA,qBAhBA;AAiBA,mBA9CA,MA8CA;AACA,oBAAA,OAAA,CAAA,QAAA,CAAA;AAAA,sBAAA,IAAA,EAAA,OAAA;AAAA,sBAAA,OAAA,EAAA;AAAA,qBAAA;AACA;AACA,iBAlDA;;AADA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoDA,KApLA;AAqLA,IAAA,WArLA,yBAqLA;AACA,WAAA,UAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,KAAA,UAAA,EAAA;AACA,UAAA,IAAA,CAAA,aAAA,GAAA,EAAA;AACA;AACA,OAJA;AAKA,KA3LA;AA4LA;AACA,IAAA,OA7LA,mBA6LA,MA7LA,EA6LA;AAAA;;AACA;AACA,WAAA,OAAA,GAAA,IAAA;AACA,UAAA,KAAA,+DAAA,KAAA,WAAA,GAAA,MAAA,CAAA;AACA,WAAA,WAAA,GAAA,KAAA;AACA,yBAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,OAAA,CAAA,OAAA,GAAA,KAAA;AACA,OAJA;AAKA,KAvMA;;AAwMA;;;AAGA,IAAA,MA3MA,oBA2MA;AACA,WAAA,KAAA,GAAA,QAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,IAAA,GAAA,EAAA;AACA,WAAA,IAAA,CAAA,GAAA,GAAA,KAAA,MAAA,CAAA,OAAA,CAAA,IAAA;AACA,WAAA,IAAA,CAAA,KAAA,GAAA,KAAA,MAAA,CAAA,OAAA,CAAA,QAAA;AACA,WAAA,IAAA,CAAA,KAAA,IAAA,EAAA,MAAA,EAAA,mCAAA,IAAA,IAAA,EAAA,EAAA,YAAA,CAAA;AACA,WAAA,MAAA,GAAA,IAAA;AACA,KAnNA;;AAoNA;;;AAGA,IAAA,YAvNA,wBAuNA,GAvNA,EAuNA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,WAAA,GAAA,mBAAA,OAAA,CAAA;AACA,kBAAA,IAAA,EAAA,IADA;AACA;AACA,kBAAA,IAAA,EAAA,SAFA;AAEA;AACA,kBAAA,OAAA,EAAA,iBAHA;AAGA;AACA,kBAAA,UAAA,EAAA,oBAJA,CAIA;;AAJA,iBAAA,CAAA;AAMA,gBAAA,OAAA,CAAA,KAAA,GAAA,QAAA;AACA,gBAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,gBAAA,OAAA,CAAA,IAAA,mCAAA,GAAA;AACA,gBAAA,OAAA,CAAA,IAAA,CAAA,GAAA,GAAA,GAAA,CAAA,GAAA;AACA,gBAAA,OAAA,CAAA,IAAA,CAAA,GAAA,GAAA,GAAA,CAAA,GAAA,CAXA,CAYA;;AACA,gBAAA,OAAA,CAAA,MAAA,GAAA,EAAA;AACA,gBAAA,OAAA,CAAA,MAAA,GAAA,EAAA;AACA,gBAAA,OAAA,CAAA,QAAA,GAAA,EAAA,CAfA,CAgBA;;AAhBA;AAAA,uBAiBA,oBAAA;AAAA,kBAAA,IAAA,EAAA,GAAA,CAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,oBAAA,OAAA,CAAA,MAAA,CAAA,IAAA,CAAA;AAAA,sBAAA,KAAA,EAAA,IAAA,CAAA,KAAA;AAAA,sBAAA,KAAA,EAAA,IAAA,CAAA;AAAA,qBAAA;AACA,mBAFA;;AAGA,kBAAA,OAAA,CAAA,MAAA,CAAA,IAAA,CAAA;AAAA,oBAAA,KAAA,EAAA,IAAA;AAAA,oBAAA,KAAA,EAAA;AAAA,mBAAA;AACA,iBALA,CAjBA;;AAAA;AAAA;AAAA,uBAwBA,oBAAA;AAAA,kBAAA,IAAA,EAAA,OAAA,CAAA,IAAA,CAAA,IAAA;AAAA,kBAAA,IAAA,EAAA,GAAA,CAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,OAAA,CAAA,MAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,CAxBA;;AAAA;AA2BA,oBAAA,OAAA,CAAA,MAAA,CAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,IAAA,CAAA,OAAA,CAAA,IAAA,EAAA,IAAA,EAAA,GAAA,CAAA,IAAA;AACA,iBAFA,MAEA;AACA,kBAAA,OAAA,CAAA,IAAA,CAAA,OAAA,CAAA,IAAA,EAAA,IAAA,EAAA,GAAA,CAAA,EAAA;AACA,iBA/BA,CAgCA;;;AAhCA;AAAA,uBAiCA,sBAAA;AAAA,kBAAA,IAAA,EAAA,OAAA,CAAA,IAAA,CAAA,IAAA;AAAA,kBAAA,EAAA,EAAA,GAAA,CAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,OAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,CAjCA;;AAAA;AAoCA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,KAAA;;AACA,gBAAA,OAAA,CAAA,MAAA,GAAA,IAAA;;AAtCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuCA,KA9PA;;AA+PA;;;AAGA,IAAA,UAlQA,sBAkQA,GAlQA,EAkQA;AACA,WAAA,IAAA,mCAAA,GAAA,EADA,CAEA;;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,GAAA,CAAA,MAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,MAAA,GAAA,IAAA;AACA,WAAA,KAAA,GAAA,QAAA;AACA,KAzQA;;AA0QA;;;AAGA,IAAA,gBA7QA,4BA6QA,EA7QA,EA6QA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAKA,IALA,CAKA,YAAA;AACA,wCAAA,CAAA,EAAA,CAAA,EAAA,IAAA,CAAA,gBAAA;AAAA,wBAAA,IAAA,QAAA,IAAA;;AACA,wBAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,OAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,SADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;;AAIA,sBAAA,OAAA,CAAA,OAAA;AACA,qBANA,MAMA;AACA,sBAAA,OAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,OADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;AAIA;AACA,mBAbA;AAcA,iBApBA,EAqBA,KArBA,CAqBA,YAAA;AACA,kBAAA,OAAA,CAAA,QAAA,CAAA;AACA,oBAAA,IAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;AAIA,iBA1BA;;AADA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA4BA,KAzSA;;AA0SA;;;AAGA,IAAA,WA7SA,uBA6SA,GA7SA,EA6SA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,QAAA,CAAA,aAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAKA,IALA,CAKA,YAAA;AACA,sBAAA,KAAA,mCAAA,GAAA,CAAA;AACA,mCAAA,KAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,wBAAA,IAAA,SAAA,IAAA;;AACA,wBAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,OAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,SADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;;AAIA,sBAAA,OAAA,CAAA,OAAA;AACA,qBANA,MAMA;AACA,sBAAA,OAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,OADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;AAIA;AACA,mBAbA;AAcA,iBArBA,EAsBA,KAtBA,CAsBA,YAAA;AACA,kBAAA,OAAA,CAAA,QAAA,CAAA;AACA,oBAAA,IAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;AAIA,iBA3BA;;AADA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA6BA,KA1UA;AA2UA;AACA,IAAA,QA5UA,sBA4UA;AACA,WAAA,IAAA,GAAA,EAAA;AACA,WAAA,MAAA,GAAA,KAAA;AACA;AA/UA;AA3GA,C", "sourcesContent": ["<template>\n  <el-row class=\"app-container\">\n    <el-row>\n      <!--基本信息查询及显示-->\n      <el-col :span=\"24\">\n        <!--搜索条件-->\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          @handleReset=\"filterReset\"\n          :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n        />\n      </el-col>\n    </el-row>\n\n    <el-row>\n      <el-col>\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button\n              type=\"primary\"\n              icon=\"el-icon-plus\"\n              v-hasPermi=\"['blgk:button:add']\"\n              @click=\"addRow\"\n              >新增</el-button\n            >\n          </div>\n          <comp-table :table-and-page-info=\"tableAndPageInfo\" height=\"65vh\">\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"200\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  icon=\"el-icon-edit\"\n                  title=\"编辑\"\n                  @click=\"handleUpdate(scope.row)\"\n                  v-if=\"\n                    (scope.row.createBy === $store.getters.name ) ||\n                      hasSuperRole\n                  \"\n                />\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"getDetails(scope.row)\"\n                  icon=\"el-icon-view\"\n                  title=\"详情\"\n                />\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  icon=\"el-icon-remove\"\n                  title=\"消除\"\n                  @click=\"handleClean(scope.row)\"\n                  v-if=\"\n                    scope.row.createBy === $store.getters.name &&\n                      scope.row.isClean != '是'\n                  \"\n                />\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  icon=\"el-icon-delete\"\n                  title=\"删除\"\n                  @click=\"handleDeleteById(scope.row.objId)\"\n                  v-if=\"\n                    scope.row.createBy === $store.getters.name || hasSuperRole  \n                  \"\n                />\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!-- 新增、详情弹出对话框 -->\n    <el-dialog\n      id=\"blgk_div\"\n      :title=\"title\"\n      :visible.sync=\"isShow\"\n      width=\"60%\"\n      @close=\"closeFun\"\n      v-dialogDrag\n    >\n      <div class=\"button_btn\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          v-if=\"!isDisabled\"\n          @click=\"chooseSbFun\"\n          >选择设备</el-button\n        >\n      </div>\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item prop=\"fgsmc\" label=\"分公司：\">\n              <el-input\n                v-model=\"form.fgsmc\"\n                :disabled=\"true\"\n                style=\"width:80%\"\n                placeholder=\"请选择分公司\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"变电站：\" prop=\"bdzmc\">\n              <el-input\n                v-model=\"form.bdzmc\"\n                :disabled=\"true\"\n                style=\"width:80%\"\n                placeholder=\"请选择变电站\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item prop=\"jgmc\" label=\"间隔：\">\n              <el-input\n                v-model=\"form.jgmc\"\n                :disabled=\"true\"\n                style=\"width:80%\"\n                placeholder=\"请选择间隔\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"设备类型：\" prop=\"sblxmc\">\n              <el-input\n                v-model=\"form.sblxmc\"\n                :disabled=\"true\"\n                style=\"width:80%\"\n                placeholder=\"请选择设备类型\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"设备：\" prop=\"sbmc\">\n              <el-input\n                v-model=\"form.sbmc\"\n                :disabled=\"true\"\n                style=\"width:80%\"\n                placeholder=\"请选择设备\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"不良工况类型:\" prop=\"type\">\n              <el-select\n                @change=\"lxChange\"\n                placeholder=\"请选择不良工况类型\"\n                v-model=\"form.type\"\n                style=\"width:80%\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option\n                  v-for=\"item in lxList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"不良工况描述：\" prop=\"ms\">\n              <el-select\n                @change=\"msChange\"\n                placeholder=\"请选择描述\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.ms\"\n                style=\"width: 91%\"\n                filterable\n                :allow-create=\"this.form.type === '其他'\"\n              >\n                <el-option\n                  v-for=\"item in msList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\" v-if=\"this.form.type !== '其他'\">\n            <el-form-item label=\"分类依据：\" prop=\"flyj\">\n              <el-select\n                :disabled=\"isDisabled\"\n                style=\"width: 91%\"\n                v-model=\"form.flyj\"\n                placeholder=\"请选择分类依据\"\n              >\n                <el-option\n                  v-for=\"item in flyjList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"录入人：\" prop=\"lrrmc\">\n              <el-input\n                v-model=\"form.lrrmc\"\n                :disabled=\"true\"\n                style=\"width:80%\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"发现时间：\" prop=\"fxsj\">\n              <el-date-picker\n                style=\"width:80%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.fxsj\"\n                type=\"date\"\n                format=\"yyyy-MM-dd\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"请选择时间\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"消除时间：\" prop=\"xcsj\">\n              <el-date-picker\n                style=\"width:80%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.xcsj\"\n                type=\"date\"\n                format=\"yyyy-MM-dd\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"请选择时间\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注：\" prop=\"bz\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"5\"\n                style=\"width: 92%\"\n                v-model=\"form.bz\"\n                :disabled=\"isDisabled\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun\">取 消</el-button>\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"saveFun\"\n          >保 存</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <!--新主设备选择框组件-->\n    <el-dialog\n      title=\"设备选择\"\n      :visible.sync=\"isShowSbChoose\"\n      width=\"86%\"\n      v-if=\"isShowSbChoose\"\n      v-dialogDrag\n    >\n      <sb-choose\n        @closeChooseFun=\"closeChooseFun\"\n        @getSbDataFun=\"getSbDataFun\"\n      />\n    </el-dialog>\n  </el-row>\n</template>\n\n<script>\nimport {\n  removeBlgk,\n  saveOrUpdate,\n  getData,\n  getAllSblxList,\n  getBlgkSblxByJg,\n  getSb,\n  clean\n} from \"@/api/blgk/blgk\";\nimport { getLx, getMs, getFlyj } from \"@/api/blgk/blgkbzk\";\nimport { getDeptListById } from \"@/api/system/dept\";\nimport { getBdzSelectList } from \"@/api/yxgl/bdyxgl/bdxjzqpz\";\nimport { getJgDataListSelected } from \"@/api/dagangOilfield/asset/bdsbtz\";\nimport { formatterDateTime } from \"@/utils/handleData\";\nimport { Loading } from \"element-ui\";\nimport { getDictTypeData } from \"@/api/system/dict/data\";\nimport sbChoose from \"@/views/dagangOilfield/blgk/chooseBdsb\";\n\nexport default {\n  name: \"blgk\",\n  components: { sbChoose },\n  data() {\n    return {\n      hasSuperRole: this.$store.getters.hasSuperRole,\n      isShow: false, //弹框是否显示\n      loading: false,\n      //是否禁用\n      isDisabled: false,\n      form: {},\n      //查询参数\n      queryParams: {\n        pageSize: 10,\n        pageNum: 1\n      },\n      //详情对话框标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          // fgsmc: \"\",\n          bdzmc: \"\",\n          jgmc: \"\",\n          sbmc: \"\",\n          sblx: \"\",\n          isClean: \"\"\n        },\n        fieldList: [\n          // { label: \"分公司\", type: \"select\", value: \"fgsmc\", options: [] },\n          { label: \"变电站\", type: \"input\", value: \"bdzmc\" },\n          { label: \"间隔名称\", type: \"input\", value: \"jgmc\" },\n          { label: \"设备名称\", type: \"input\", value: \"sbmc\" },\n          {\n            label: \"设备类型\",\n            type: \"select\",\n            value: \"sblx\",\n            options: [],\n            clearable: true,\n            filterable: true\n          },\n          {\n            label: \"是否消除\",\n            type: \"select\",\n            value: \"isClean\",\n            options: [],\n            clearable: true\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: { checkBox: false, serialNumber: true },\n        tableData: [],\n        tableHeader: [\n          { prop: \"fgsmc\", label: \"分公司\", minWidth: \"120\" },\n          { prop: \"bdzmc\", label: \"变电站\", minWidth: \"120\" },\n          { prop: \"jgmc\", label: \"间隔名称\", minWidth: \"100\" },\n          { prop: \"sblxmc\", label: \"设备类型\", minWidth: \"120\" },\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"120\" },\n          { prop: \"ms\", label: \"不良工况描述\", minWidth: \"140\", showPop: true },\n          { prop: \"flyjCn\", label: \"分类依据\", minWidth: \"140\", showPop: true },\n          { prop: \"lrrmc\", label: \"录入人\", minWidth: \"80\" },\n          { prop: \"fxsj\", label: \"发现时间\", minWidth: \"110\" },\n          { prop: \"xcsj\", label: \"消除时间\", minWidth: \"110\" }\n        ]\n      },\n      rules: {\n        fgsmc: [{ required: true, message: \"分公司不能为空\", trigger: \"blur\" }],\n        bdzmc: [{ required: true, message: \"变电站不能为空\", trigger: \"blur\" }],\n        jgmc: [{ required: true, message: \"间隔不能为空\", trigger: \"blur\" }],\n        sblxmc: [\n          { required: true, message: \"设备类型不能为空\", trigger: \"blur\" }\n        ],\n        type: [\n          { required: true, message: \"不良工况类型不能为空\", trigger: \"select\" }\n        ],\n        sbmc: [{ required: true, message: \"设备不能为空\", trigger: \"blur\" }],\n        ms: [\n          { required: true, message: \"不良工况不能为空\", trigger: \"select\" }\n        ],\n        flyj: [\n          { required: true, message: \"分类依据不能为空\", trigger: \"select\" }\n        ]\n      },\n      fgsList: [], //分公司下拉框\n      bdzList: [], //变电站下拉框\n      jgList: [], //间隔下拉框\n      sblxList: [], //间隔下拉框\n      sbList: [], //设备下拉框\n      msList: [], //描述下拉框\n      flyjList: [], //分类依据下拉框\n      lxList: [], //类型下拉框\n      saveLoading: null, //保存时的遮罩层\n      isShowSbChoose: false, //是否显示设备选择弹框\n      sbData: {} //设备数据\n    };\n  },\n  created() {\n    //列表查询\n    this.getData();\n    this.getOptions(); //获取涉及到的下拉框字典值\n  },\n  methods: {\n    //选择设备\n    chooseSbFun() {\n      this.isShowSbChoose = true;\n    },\n    //关闭设备选择弹框\n    closeChooseFun() {\n      this.isShowSbChoose = false;\n    },\n    //获取设备数据\n    async getSbDataFun(sbData) {\n      this.sbData = sbData;\n      this.$set(this.form, \"fgsmc\", sbData.deptname);\n      this.$set(this.form, \"bdzmc\", sbData.bdzmc);\n      this.$set(this.form, \"jgmc\", sbData.wzmc);\n      this.$set(this.form, \"sblxmc\", sbData.sblxmc);\n      this.$set(this.form, \"sblx\", sbData.sblxbm);\n      this.$set(this.form, \"sbmc\", sbData.sbmc);\n      this.$set(this.form, \"sbid\", sbData.objId);\n      this.$set(this.form, \"type\", \"\");\n      this.$set(this.form, \"ms\", \"\");\n      this.$set(this.form, \"flyj\", \"\");\n      //获取不良工况类型下拉框\n      this.lxList = [];\n      await getLx({ sblx: sbData.sblxbm }).then(res => {\n        res.data.forEach(item => {\n          this.lxList.push({ label: item.label, value: item.label });\n        });\n        this.lxList.push({ label: \"其他\", value: \"其他\" });\n      });\n    },\n    //查询下拉框数据\n    async getOptions() {\n      await this.getSfList(); //是/否，字典\n      await this.getAllSblxList();\n    },\n    //获取所有设备类型下拉框用于查询\n    getAllSblxList() {\n      getAllSblxList({ zy: \"bdsb\" }).then(res => {\n        this.filterInfo.fieldList.forEach(item => {\n          if (item.value === \"sblx\") {\n            item.options = res.data;\n            return false;\n          }\n        });\n      });\n    },\n    //获取是/否，字典\n    getSfList() {\n      getDictTypeData(\"sys_sf\").then(res => {\n        let sfList = [];\n        res.data.forEach(item => {\n          sfList.push({ label: item.label, value: item.value });\n        });\n        this.filterInfo.fieldList.forEach(item => {\n          if (item.value === \"isClean\") {\n            item.options = sfList;\n            return false;\n          }\n        });\n      });\n    },\n    //根据分公司获取变电站\n    async getBdzSelectList(val) {\n      this.$set(this.form, \"bdz\", \"\");\n      await getBdzSelectList({ ssdwbm: val }).then(res => {\n        this.bdzList = res.data;\n      });\n    },\n    //根据变电站获取间隔\n    async getJgSelectList(val) {\n      this.$set(this.form, \"jg\", \"\");\n      await getJgDataListSelected({ ssbdz: val }).then(res => {\n        this.jgList = res.data;\n      });\n    },\n    //间隔下拉框change事件\n    async jgChange(val) {\n      this.$set(this.form, \"sblx\", \"\");\n      await getBlgkSblxByJg({ ssjg: val }).then(res => {\n        this.sblxList = res.data;\n      });\n    },\n    //设备类型下拉框change事件\n    async sblxChange(val) {\n      //重置表单及字典\n      this.$set(this.form, \"sbid\", \"\");\n      this.$set(this.form, \"type\", \"\");\n      this.sbList = [];\n      this.msList = [];\n      this.lxList = [];\n      //获取设备下拉框\n      await getSb({ sblx: val, ssjg: this.form.jg }).then(res => {\n        this.sbList = res.data;\n      });\n      //获取不良工况类型下拉框\n      await getLx({ sblx: val }).then(res => {\n        res.data.forEach(item => {\n          this.lxList.push({ label: item.label, value: item.label });\n        });\n      });\n    },\n    async lxChange(val) {\n      //重置表单及字典\n      this.$set(this.form, \"ms\", \"\");\n      this.msList = [];\n      //获取不良工况描述下拉框\n      await getMs({ sblx: this.form.sblx, type: val }).then(res => {\n        this.msList = res.data;\n      });\n    },\n    //不良工况描述下拉框change事件\n    async msChange(val) {\n      this.$set(this.form, \"flyj\", \"\");\n      this.flyjList = [];\n      let msLabel = \"\";\n      this.msList.forEach(item => {\n        if (item.value == val) {\n          msLabel = item.label;\n          return false;\n        }\n      });\n      //获取分类依据下拉框\n      await getFlyj({ sblx: this.form.sblx, ms: msLabel }).then(res => {\n        this.flyjList = res.data;\n      });\n    },\n    //保存表单\n    async saveFun() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          //设置虚拟字段用于保存设备异常及事故处理数据\n          let ms = \"\";\n          let flyjCn = \"\";\n          this.msList.forEach(item => {\n            if (item.value == this.form.ms) {\n              ms = item.label;\n              return false;\n            }\n          });\n          this.flyjList.forEach(item => {\n            if (item.value == this.form.flyj) {\n              flyjCn = item.label;\n              return false;\n            }\n          });\n          if (Object.keys(this.sbData).length !== 0) {\n            this.form.fgs = this.sbData.ssgs;\n            this.form.bdz = this.sbData.ssbdz;\n          }\n          if (ms) {\n            this.form.ms = ms;\n          }\n          this.form.flyjCn = flyjCn;\n          if (this.form.xcsj) {\n            this.form.isClean = \"是\";\n          } else {\n            this.form.isClean = \"否\"; //设置是否消除为否\n          }\n          this.$nextTick(() => {\n            this.saveLoading = Loading.service({\n              lock: true, //lock的修改符--默认是false\n              text: \"保存中，请稍后\", //显示在加载图标下方的加载文案\n              spinner: \"el-icon-loading\", //自定义加载图标类名\n              background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n              target: document.querySelector(\"#blgk_div\")\n            });\n            saveOrUpdate(this.form).then(res => {\n              if (res.code === \"0000\") {\n                this.$message.success(\"操作成功!\");\n                this.getData();\n                this.saveLoading.close();\n                this.isShow = false;\n              }\n            });\n          });\n        } else {\n          this.$message({ type: \"error\", message: \"校验未通过\" });\n        }\n      });\n    },\n    filterReset() {\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n    },\n    //分页查询列表\n    getData(params) {\n      //参数合并\n      this.loading = true;\n      const param = { ...this.queryParams, ...params };\n      this.queryParams = param;\n      getData(param).then(res => {\n        this.tableAndPageInfo.tableData = res.data.records;\n        this.tableAndPageInfo.pager.total = res.data.total;\n        this.loading = false;\n      });\n    },\n    /**\n     * 新增不良工况\n     */\n    addRow() {\n      this.title = \"新增不良工况\";\n      this.isDisabled = false;\n      this.form = {};\n      this.form.lrr = this.$store.getters.name;\n      this.form.lrrmc = this.$store.getters.nickName;\n      this.$set(this.form, \"fxsj\", formatterDateTime(new Date(), \"yyyy-MM-dd\"));\n      this.isShow = true;\n    },\n    /**\n     * 编辑\n     */\n    async handleUpdate(row) {\n      this.saveLoading = Loading.service({\n        lock: true, //lock的修改符--默认是false\n        text: \"加载中，请稍后\", //显示在加载图标下方的加载文案\n        spinner: \"el-icon-loading\", //自定义加载图标类名\n        background: \"rgba(0, 0, 0, 0.7)\" //遮罩层颜色\n      });\n      this.title = \"编辑不良工况\";\n      this.isDisabled = false;\n      this.form = { ...row };\n      this.form.fgs = row.fgs;\n      this.form.bdz = row.bdz;\n      //处理下拉框回显\n      this.lxList = [];\n      this.msList = [];\n      this.flyjList = [];\n      //获取不良工况类型下拉框\n      await getLx({ sblx: row.sblx }).then(res => {\n        res.data.forEach(item => {\n          this.lxList.push({ label: item.label, value: item.label });\n        });\n        this.lxList.push({ label: \"其他\", value: \"其他\" });\n      });\n      //获取不良工况描述下拉框\n      await getMs({ sblx: this.form.sblx, type: row.type }).then(res => {\n        this.msList = res.data;\n      });\n      if (this.msList.length) {\n        this.$set(this.form, \"ms\", row.flyj);\n      } else {\n        this.$set(this.form, \"ms\", row.ms);\n      }\n      //获取分类依据下拉框\n      await getFlyj({ sblx: this.form.sblx, ms: row.ms }).then(res => {\n        this.flyjList = res.data;\n      });\n      //关闭遮罩层\n      this.saveLoading.close();\n      this.isShow = true;\n    },\n    /**\n     * 详情查看\n     */\n    getDetails(row) {\n      this.form = { ...row };\n      //处理回显问题\n      this.form.flyj = row.flyjCn;\n      this.isDisabled = true;\n      this.isShow = true;\n      this.title = \"不良工况详情\";\n    },\n    /**\n     * 删除\n     */\n    async handleDeleteById(id) {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          removeBlgk([id]).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    /**\n     * 消除\n     */\n    async handleClean(row) {\n      this.$confirm(\"是否确定消除不良工况?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          let form1 = { ...row };\n          clean(form1).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"消除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"消除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消消除\"\n          });\n        });\n    },\n    //弹框关闭事件\n    closeFun() {\n      this.form = {};\n      this.isShow = false;\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.card1 {\n  margin-bottom: 6px;\n}\n\n.search-condition {\n  font-size: 13px;\n  color: #9c9c9c;\n\n  .el-select {\n    .el-input {\n      width: 100%;\n    }\n  }\n\n  .el-col {\n    vertical-align: middle;\n    line-height: 32px;\n    text-align: left;\n  }\n}\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/blgk"}]}