{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\sbxhwh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\sbxhwh.vue", "mtime": 1706897322893}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["sbxhwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAyIA;;AAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,gBAAA,EAAA,EADA;AAEA,MAAA,UAAA,EAAA,EAFA;AAGA,MAAA,QAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA,EAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAAA,CAHA;AAIA,MAAA,UAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA,EAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAAA,CAJA;AAKA,MAAA,IAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,MAAA,EAAA;AAFA,OALA;AASA;AACA,MAAA,aAAA,EAAA,KAVA;AAWA;AACA,MAAA,UAAA,EAAA,KAZA;AAaA;AACA,MAAA,KAAA,EAAA,EAdA;AAeA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,IAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA;AAFA,SADA;AAIA;AACA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,SAFA;AAGA,UAAA,IAAA,EAAA,mBAHA;AAIA,UAAA,SAAA,EAAA,IAJA;AAKA,UAAA,UAAA,EAAA,IALA;AAMA,UAAA,QAAA,EAAA,IANA;AAOA,UAAA,OAAA,EAAA;AAPA,SADA,EAUA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAVA;AALA,OAfA;AAiCA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA;AACA;;;;;;;;;;;;AAJA,SARA;AAyBA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAzBA,OAjCA;AA4DA,MAAA,MAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,OAAA,EAAA;AAFA,OA5DA;AAgEA,MAAA,cAAA,EAAA,KAhEA;AAiEA,MAAA,QAAA,EAAA,KAjEA;AAkEA,MAAA,KAAA,EAAA;AACA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AAPA;AAlEA,KAAA;AA4EA,GA/EA;AAgFA,EAAA,OAhFA,qBAgFA,CAEA,CAlFA;AAmFA,EAAA,OAnFA,qBAmFA;AACA;AACA,SAAA,OAAA;AACA,SAAA,mBAAA;AACA,GAvFA;AAwFA,EAAA,OAAA,EAAA;AACA,IAAA,mBADA,iCACA;AAAA;;AACA,uCAAA,CAAA,MAAA,EAAA,MAAA,EAAA,MAAA,CAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,KAAA,CAAA,gBAAA,GAAA,GAAA,CAAA,IAAA;;AACA,UAAA,KAAA,CAAA,UAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,gBAAA,IAAA,CAAA,KAAA,KAAA,SAAA,EAAA;AACA,cAAA,IAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;AACA;AACA;AACA,WALA;AAMA,SARA,MAQA;AACA,UAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,UAAA;AACA;AACA,OAZA;AAaA,KAfA;AAgBA;AACA,IAAA,OAjBA,mBAiBA,MAjBA,EAiBA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,MAAA,CAAA,KAAA,CAAA,KAAA,CAAA,OAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,MAAA,+DAAA,MAAA,CAAA,MAAA,GAAA,MAAA;AACA,gBAAA,KAJA,GAIA,MAAA,CAAA,MAJA;AAAA;AAAA,uBAKA,oBAAA,KAAA,CALA;;AAAA;AAAA;AAKA,gBAAA,IALA,kBAKA,IALA;AAKA,gBAAA,IALA,kBAKA,IALA;;AAMA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;;AACA,kBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,oBAAA,MAAA,CAAA,KAAA,CAAA,KAAA,CAAA,OAAA,GAAA,KAAA;AACA,mBAHA;AAIA;;AAbA;AAAA;;AAAA;AAAA;AAAA;AAeA,gBAAA,OAAA,CAAA,GAAA;;AACA,gBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,KAAA,CAAA,OAAA,GAAA,KAAA;AACA,iBAHA;;AAhBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBA,KAtCA;AAuCA;AACA,IAAA,QAxCA,sBAwCA,CACA,CAzCA;AA0CA;AACA,IAAA,qBA3CA,mCA2CA,CAAA,CA3CA;AA4CA;AACA,IAAA,SA7CA,uBA6CA;AACA,WAAA,KAAA,GAAA,SAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,IAAA,GAAA,EAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,KAlDA;AAmDA;AACA,IAAA,SApDA,qBAoDA,GApDA,EAoDA;AACA,WAAA,KAAA,GAAA,SAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,KAzDA;AA0DA;AACA,IAAA,UA3DA,sBA2DA,GA3DA,EA2DA;AACA,WAAA,KAAA,GAAA,WAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,KAhEA;;AAiEA;;;;;;;AAOA,IAAA,OAxEA,qBAwEA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,sBAAA,KAAA,EAAA;AACA,6CAAA,MAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,0BAAA;AACA,4BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,0BAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA;AACA,uBAJA,CAIA,OAAA,CAAA,EAAA;AACA,wBAAA,OAAA,CAAA,GAAA,CAAA,CAAA;AACA;;AACA,sBAAA,MAAA,CAAA,OAAA;AACA,qBATA;AAUA,mBAXA,MAWA;AACA,2BAAA,KAAA;AACA;;AACA,kBAAA,MAAA,CAAA,aAAA,GAAA,KAAA;AACA,iBAhBA;;AADA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA,KA1FA;AA2FA;AACA,IAAA,SA5FA,uBA4FA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBACA,MAAA,CAAA,UAAA,CAAA,MAAA,GAAA,CADA;AAAA;AAAA;AAAA;;AAEA,gBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,aAAA;;AAFA;;AAAA;AAKA,gBAAA,GALA,GAKA,MAAA,CAAA,UAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,yBAAA,IAAA,CAAA,OAAA;AACA,iBAFA,CALA;;AAQA,gBAAA,MAAA,CAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAKA,IALA,CAKA,YAAA;AACA,qCAAA,GAAA,EAAA,IAAA,CAAA,gBAAA;AAAA,wBAAA,IAAA,QAAA,IAAA;;AACA,wBAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,SADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;;AAIA,sBAAA,MAAA,CAAA,OAAA;AACA,qBANA,MAMA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,OADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;AAIA;AACA,mBAbA;AAcA,iBApBA,EAqBA,KArBA,CAqBA,YAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA;AACA,oBAAA,IAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;AAIA,iBA1BA;;AA2BA,gBAAA,MAAA,CAAA,OAAA;;AAnCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoCA,KAhIA;AAiIA;AACA,IAAA,KAlIA,mBAkIA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,KApIA;AAqIA,IAAA,YArIA,wBAqIA,IArIA,EAqIA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KAvIA;AAwIA;AACA,IAAA,oBAzIA,kCAyIA;AACA,WAAA,QAAA,GAAA,KAAA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,KA5IA;AA6IA;AACA,IAAA,eA9IA,2BA8IA,GA9IA,EA8IA;AACA,UAAA,GAAA,CAAA,MAAA,CAAA,IAAA,KAAA,QAAA,EAAA;AACA,aAAA,cAAA,GAAA,IAAA;AACA,aAAA,QAAA,GAAA,IAAA;AACA;AACA,KAnJA;AAoJA;AACA,IAAA,iBArJA,6BAqJA,GArJA,EAqJA;AAAA;;AACA,UAAA,KAAA,QAAA,EAAA;AACA,aAAA,UAAA,CAAA,IAAA,CAAA,OAAA,GAAA,EAAA;AACA,aAAA,UAAA,CAAA,IAAA,CAAA,MAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,OAAA,EAAA;AACA,YAAA,MAAA,CAAA,UAAA,CAAA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA;;AACA,YAAA,MAAA,CAAA,UAAA,CAAA,IAAA,CAAA,MAAA,IAAA,IAAA,CAAA,IAAA,GAAA,GAAA;AACA;AACA,SALA;AAOA,aAAA,UAAA,CAAA,IAAA,CAAA,MAAA,GAAA,KAAA,UAAA,CAAA,IAAA,CAAA,MAAA,CAAA,SAAA,CACA,CADA,EAEA,KAAA,UAAA,CAAA,IAAA,CAAA,MAAA,CAAA,MAAA,GAAA,CAFA,CAAA;AAIA,aAAA,cAAA,GAAA,KAAA;AACA,OAfA,MAeA;AACA,YAAA,SAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,OAAA,EAAA;AACA,YAAA,SAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,SAJA;;AAKA,YAAA,SAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,eAAA,IAAA,CAAA,MAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA,eAAA,IAAA,CAAA,MAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA,eAAA,cAAA,GAAA,KAAA;AACA,SAJA,MAIA;AACA,eAAA,QAAA,CAAA,OAAA,CAAA,WAAA;AACA;AACA;AACA,KApLA;AAqLA,IAAA,qBArLA,mCAqLA;AACA,WAAA,cAAA,GAAA,KAAA;AACA,KAvLA;AAwLA;AACA,IAAA,WAzLA,yBAyLA;AAAA;;AACA,WAAA,IAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,YAAA;AACA,QAAA,MAAA,CAAA,IAAA,GAAA,MAAA,CAAA,QAAA,CAAA,IAAA,GAAA,IAAA;;AACA,QAAA,MAAA,CAAA,SAAA,CAAA,MAAA;AACA,OAHA;AAIA;AA/LA;AAxFA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 120, itemWidth: 230 }\"\n      @handleReset=\"getReset\"\n    />\n\n    <el-white class=\"button-group1\">\n      <div class=\"button_btn\">\n        <el-button\n          type=\"primary\"\n          v-hasPermi=\"['bzsbxhk:button:add']\"\n          icon=\"el-icon-plus\"\n          @click=\"getInsert\"\n          >新增</el-button\n        >\n      </div>\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"selectChange\"\n        height=\"68vh\"\n        ref=\"sbxhk\"\n      >\n        <el-table-column\n          slot=\"table_eight\"\n          align=\"center\"\n          fixed=\"right\"\n          style=\"display: block\"\n          label=\"操作\"\n          width=\"160\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              @click=\"getUpdate(scope.row)\"\n              v-hasPermi=\"['bzsbxhk:button:update']\"\n              type=\"text\"\n              size=\"small\"\n              class=\"el-icon-edit\"\n              title=\"修改\"\n            ></el-button>\n            <el-button\n              @click=\"getDetails(scope.row)\"\n              type=\"text\"\n              size=\"small\"\n              title=\"详情\"\n              class=\"el-icon-view\"\n            ></el-button>\n            <el-button\n              @click=\"deleteRow\"\n              type=\"text\"\n              v-if=\"scope.row.createBy === $store.getters.name\"\n              icon=\"el-icon-delete\"\n            ></el-button>\n          </template>\n        </el-table-column>\n      </comp-table>\n    </el-white>\n\n    <!-- 详情/新增/修改 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"30%\"\n      @close=\"handleClose\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"130px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row class=\"box-card\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"设备分类：\" prop=\"dysblx\">\n              <el-select\n                placeholder=\"请选择设备分类\"\n                v-model=\"form.dysblx\"\n                :disabled=\"isDisabled\"\n                clearable\n                filterable\n                style=\"width: 100%\"\n              >\n                <el-option-group\n                  v-for=\"group in DevicesListGroup\"\n                  :key=\"group.label\"\n                  :label=\"group.label\"\n                >\n                  <el-option\n                    v-for=\"item in group.sbDataList\"\n                    :key=\"item.code\"\n                    :label=\"item.name\"\n                    :value=\"item.code\"\n                  >\n                  </el-option>\n                </el-option-group>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"设备型号：\" prop=\"sbxh\">\n              <el-input\n                placeholder=\"请添加设备型号\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.sbxh\"\n              >\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注：\" prop=\"bz\">\n              <el-input\n                placeholder=\"请填写备注\"\n                type=\"textarea\"\n                :rows=\"3\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.bz\"\n              >\n              </el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\" size=\"small\">取 消</el-button>\n        <el-button\n          v-if=\"title === '设备型号库增加' || title === '设备型号库修改'\"\n          type=\"primary\"\n          size=\"small\"\n          @click=\"saveRow\"\n          >确 认\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  getList,\n  remove,\n  saveOrUpdate\n} from \"@/api/dagangOilfield/bzgl/sbbzk/sbxhk\";\nimport { getDeviceClassGroup } from \"@/api/dagangOilfield/bzgl/sblxwh/sblxwh\";\n\nexport default {\n  name: \"index\",\n  data() {\n    return {\n      DevicesListGroup: [],\n      selectRows: [],\n      sbxhList: [{ label: \"\", value: \"\" }],\n      dysblxList: [{ label: \"\", value: \"\" }],\n      form: {\n        sbxh: \"\",\n        dysblx: \"\"\n      },\n      //详情弹框是否显示\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          sbxh: \"\",\n          sblxArr: []\n        }, //查询条件\n        fieldList: [\n          {\n            label: \"设备分类\",\n            value: \"sblxArr\",\n            type: \"selectGroupjxxmwh\",\n            clearable: true,\n            filterable: true,\n            multiple: true,\n            options: []\n          },\n          { label: \"设备型号\", type: \"input\", value: \"sbxh\" }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 120]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"对应设备类型\", prop: \"sblxmc\", minWidth: \"180\" },\n          { label: \"设备型号\", prop: \"sbxh\", minWidth: \"180\" },\n          { label: \"备注\", prop: \"bz\", minWidth: \"140\" }\n          /* {\n            prop: 'operation',\n            label: '操作',\n            minWidth: '130px',\n            style: { display: 'block' },\n            //操作列固定再右侧\n            fixed: 'right',\n            operation: [\n              { name: '修改', clickFun: this.getUpdate },\n              { name: '详情', clickFun: this.getDetails }\n            ]\n          }*/\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        sbxh: \"\",\n        sblxArr: []\n      },\n      showDeviceTree: false,\n      isFilter: false,\n      rules: {\n        dysblx: [\n          { required: true, message: \"设备分类不能为空\", trigger: \"change\" }\n        ],\n        sblxmc: [\n          { required: true, message: \"设备分类不能为空\", trigger: \"change\" }\n        ],\n        sbxh: [{ required: true, message: \"设备型号不能为空\", trigger: \"blur\" }]\n      }\n    };\n  },\n  created() {\n    \n  },\n  mounted() {\n    //列表查询\n    this.getData();\n    this.getDeviceClassGroup();\n  },\n  methods: {\n    getDeviceClassGroup() {\n      getDeviceClassGroup([\"bdsb\", \"pdsb\", \"sdsb\"]).then(res => {\n        if (res.code === \"0000\") {\n          this.DevicesListGroup = res.data;\n          this.filterInfo.fieldList.forEach(item => {\n            if (item.value === \"sblxArr\") {\n              item.options = res.data;\n              return;\n            }\n          });\n        } else {\n          this.$message.error(\"获取设备分类失败\");\n        }\n      });\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.$refs.sbxhk.loading = true;\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        const { data, code } = await getList(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          this.$nextTick(() => {\n            // 以服务的方式调用的 Loading 需要异步关闭\n            this.$refs.sbxhk.loading = false;\n          });\n        }\n      } catch (e) {\n        console.log(e);\n        this.$nextTick(() => {\n          // 以服务的方式调用的 Loading 需要异步关闭\n          this.$refs.sbxhk.loading = false;\n        });\n      }\n    },\n    //重置按钮\n    getReset() {\n    },\n    //选中行\n    handleSelectionChange() {},\n    //新增按钮\n    getInsert() {\n      this.title = \"设备型号库增加\";\n      this.isDisabled = false;\n      this.form = {};\n      this.isShowDetails = true;\n    },\n    //修改按钮\n    getUpdate(row) {\n      this.title = \"设备型号库修改\";\n      this.isDisabled = false;\n      this.form = { ...row };\n      this.isShowDetails = true;\n    },\n    //详情按钮\n    getDetails(row) {\n      this.title = \"设备型号库详情查看\";\n      this.form = { ...row };\n      this.isDisabled = true;\n      this.isShowDetails = true;\n    },\n    /*//附件查看\n    getFjInfoList() {\n      this.title = '附件查看'\n      this.isDisabled = true\n      this.isShowDetails = true\n      this.form={...row}\n    },*/\n    async saveRow() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          saveOrUpdate(this.form).then(res => {\n            try {\n              if (res.code === \"0000\") {\n                this.$message.success(\"操作成功\");\n              }\n            } catch (e) {\n              console.log(e);\n            }\n            this.getData();\n          });\n        } else {\n          return false;\n        }\n        this.isShowDetails = false;\n      });\n    },\n    //删除按钮\n    async deleteRow() {\n      if (this.selectRows.length < 1) {\n        this.$message.warning(\"请选择正确的数据！！！\");\n        return;\n      }\n      let ids = this.selectRows.map(item => {\n        return item.sbxhkId;\n      });\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove(ids).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n      this.getData();\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false;\n    },\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n    //显示设备类型树弹窗\n    showDeviceTreeDialog() {\n      this.isFilter = false;\n      this.showDeviceTree = true;\n    },\n    //鼠标聚焦事件\n    inputFocusEvent(val) {\n      if (val.target.name === \"dysblx\") {\n        this.showDeviceTree = true;\n        this.isFilter = true;\n      }\n    },\n    //获取设备分类数据\n    getDeviceTypeData(res) {\n      if (this.isFilter) {\n        this.filterInfo.data.sblxArr = [];\n        this.filterInfo.data.dysblx = \"\";\n        res.forEach(item => {\n          if (item.checked) {\n            this.filterInfo.data.sblxArr.push(item.code);\n            this.filterInfo.data.dysblx += item.name + \",\";\n          }\n        });\n\n        this.filterInfo.data.dysblx = this.filterInfo.data.dysblx.substring(\n          0,\n          this.filterInfo.data.dysblx.length - 1\n        );\n        this.showDeviceTree = false;\n      } else {\n        let treeNodes = [];\n        res.forEach(item => {\n          if (item.checked) {\n            treeNodes.push(item);\n          }\n        });\n        if (treeNodes.length === 1) {\n          this.form.sblxmc = treeNodes[0].name;\n          this.form.dysblx = treeNodes[0].code;\n          this.showDeviceTree = false;\n        } else {\n          this.$message.warning(\"请选择单条设备数据\");\n        }\n      }\n    },\n    closeDeviceTypeDialog() {\n      this.showDeviceTree = false;\n    },\n    //清空表单数据\n    handleClose() {\n      this.form = {};\n      this.$nextTick(() => {\n        this.form = this.$options.data().form;\n        this.resetForm(\"form\");\n      });\n    }\n  }\n};\n</script>\n\n<style scoped>\n.button-group {\n  padding-left: 30px;\n  padding-right: 30px;\n  display: flex;\n  justify-content: flex-end;\n}\n\n/*控制input输入框边框是否显示*/\n.elInput >>> .el-input__inner {\n  border: 0;\n}\n\n.box-card {\n  margin-bottom: 2vh !important;\n}\n/deep/ .el-select-group__title {\n  font-size: 24px;\n}\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbbzk"}]}