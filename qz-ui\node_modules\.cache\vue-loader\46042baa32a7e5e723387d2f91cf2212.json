{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\czpcxtj.vue?vue&type=style&index=0&id=671ace56&scoped=true&lang=css&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\czpcxtj.vue", "mtime": 1706897324301}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoudGl0bGUgewogIGZvbnQtc2l6ZTogMTZweDsKICBmb250LXdlaWdodDogYm9sZDsKfQoKLmVsLXNlbGVjdCB7CiAgd2lkdGg6IDEwMCU7Cn0KCi5ib3gtY2FyZCB7CiAgbWFyZ2luLWJvdHRvbTogMnZoICFpbXBvcnRhbnQ7Cn0KCi8qIOiuvue9rua7muWKqOadoeeahOagt+W8jyAqLwo6Oi13ZWJraXQtc2Nyb2xsYmFyIHsKICB3aWR0aDogMTJweDsKfQoKLyog5rua5Yqo5qe9ICovCjo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sgewovLy13ZWJraXQtYm94LXNoYWRvdzogaW5zZXQwMDZweHJnYmEoMCwgMCwgMCwgMC4zKTsKICBib3JkZXItcmFkaXVzOiAxMHB4Owp9CgovKiDmu5rliqjmnaHmu5HlnZcgKi8KOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7CiAgYm9yZGVyLXJhZGl1czogMTBweDsKICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuMSk7Ci8vLXdlYmtpdC1ib3gtc2hhZG93OiBnYmEoMCwgMCwgMCwgMC41KTsKfQoKOjotd2Via2l0LXNjcm9sbGJhci10aHVtYjp3aW5kb3ctaW5hY3RpdmUgewogIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC4xKTsKfQoKW2RhdGEtdi02N2E5NzRiMV06Oi13ZWJraXQtc2Nyb2xsYmFyIHsKICB3aWR0aDogOHB4Owp9CgouaXRlbSB7CiAgd2lkdGg6IDIyNXB4OwogIGZsb2F0OiBsZWZ0Owp9CgovKuWIl+ihqOminOiJsuiuvue9riovCi9kZWVwLyAuZWwtdGFibGUgdGh7CiAgYmFja2dyb3VuZC1jb2xvcjogI2U4ZjdmMDsKCn0K"}, {"version": 3, "sources": ["czpcxtj.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+jBA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA", "file": "czpcxtj.vue", "sourceRoot": "src/views/dagangOilfield/czpgl", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 主页面 -->\n    <el-white>\n      <el-white>\n        <el-filter\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 120, itemWidth: 230 }\"\n          @handleReset=\"filterReset\"\n        />\n      </el-white>\n      <el-table :data=\"tableDataToTj\" border fit stripe highlight-current-row>\n        <el-table-column prop=\"dw\" label=\"单位\" align=\"center\" width=\"150px\"></el-table-column>\n        <el-table-column label=\"变电站倒闸操作票\" align=\"center\">\n          <el-table-column prop=\"bdzxzs\" label=\"执行张数\" align=\"center\">\n            <template slot-scope=\"scope\">\n              {{scope.row.byfxwjqx}}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"bdljzxzs\" label=\"累计(执行张数)\" align=\"center\" width=\"120px\">\n            <template slot-scope=\"scope\">\n              {{scope.row.ljfxwjqx}}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"bdzfzs\" label=\"作废张数\" align=\"center\">\n            <template slot-scope=\"scope\">\n              {{scope.row.ljxcwjqx}}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"bdljzfzs\" label=\"累计(作废张数)\" align=\"center\" width=\"120px\">\n            <template slot-scope=\"scope\">\n              {{scope.row.byfxyzqx}}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"bdzxxs\" label=\"执行项数\" align=\"center\">\n            <template slot-scope=\"scope\">\n              {{scope.row.ljxcwjqx}}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"bdljzxxs\" label=\"累计(执行项数)\" align=\"center\" width=\"120px\">\n            <template slot-scope=\"scope\">\n              {{scope.row.ljxcwjqx}}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"bdzfxs\" label=\"作废项数\" align=\"center\">\n            <template slot-scope=\"scope\">\n              {{scope.row.byfxyzqx}}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"bdljzfxs\" label=\"累计(作废项数)\" align=\"center\" width=\"120px\">\n            <template slot-scope=\"scope\">\n              {{scope.row.ljxcwjqx}}\n            </template>\n          </el-table-column>\n        </el-table-column>\n        <el-table-column label=\"电力线路倒闸操作票\" align=\"center\">\n          <el-table-column prop=\"xlzxzs\" label=\"执行张数\" align=\"center\">\n            <template slot-scope=\"scope\">\n              {{scope.row.byfxwjqx}}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"xlljzxzs\" label=\"累计(执行张数)\" align=\"center\" width=\"120px\">\n            <template slot-scope=\"scope\">\n              {{scope.row.ljfxwjqx}}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"xlzfzs\" label=\"作废张数\" align=\"center\">\n            <template slot-scope=\"scope\">\n              {{scope.row.ljxcwjqx}}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"xlljzfzs\" label=\"累计(作废张数)\" align=\"center\" width=\"120px\">\n            <template slot-scope=\"scope\">\n              {{scope.row.byfxyzqx}}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"xlzxxs\" label=\"执行项数\" align=\"center\">\n            <template slot-scope=\"scope\">\n              {{scope.row.ljxcwjqx}}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"xlljzxxs\" label=\"累计(执行项数)\" align=\"center\" width=\"120px\">\n            <template slot-scope=\"scope\">\n              {{scope.row.ljxcwjqx}}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"xlzfxs\" label=\"作废项数\" align=\"center\">\n            <template slot-scope=\"scope\">\n              {{scope.row.byfxyzqx}}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"xlljzfxs\" label=\"累计(作废项数)\" align=\"center\" width=\"120px\">\n            <template slot-scope=\"scope\">\n              {{scope.row.ljxcwjqx}}\n            </template>\n          </el-table-column>\n        </el-table-column>\n        <el-table-column label=\"配电站倒闸操作票\" align=\"center\">\n          <el-table-column prop=\"pdzxzs\" label=\"执行张数\" align=\"center\">\n            <template slot-scope=\"scope\">\n              {{scope.row.byfxwjqx}}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"pdljzxzs\" label=\"累计(执行张数)\" align=\"center\" width=\"120px\">\n            <template slot-scope=\"scope\">\n              {{scope.row.ljfxwjqx}}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"pdzfzs\" label=\"作废张数\" align=\"center\">\n            <template slot-scope=\"scope\">\n              {{scope.row.ljxcwjqx}}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"pdljzfzs\" label=\"累计(作废张数)\" align=\"center\" width=\"120px\">\n            <template slot-scope=\"scope\">\n              {{scope.row.byfxyzqx}}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"pdzxxs\" label=\"执行项数\" align=\"center\">\n            <template slot-scope=\"scope\">\n              {{scope.row.ljxcwjqx}}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"pdljzxxs\" label=\"累计(执行项数)\" align=\"center\" width=\"120px\">\n            <template slot-scope=\"scope\">\n              {{scope.row.ljxcwjqx}}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"pdzfxs\" label=\"作废项数\" align=\"center\">\n            <template slot-scope=\"scope\">\n              {{scope.row.byfxyzqx}}\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"pdljzfxs\" label=\"累计(作废项数)\" align=\"center\" width=\"120px\">\n            <template slot-scope=\"scope\">\n              {{scope.row.ljxcwjqx}}\n            </template>\n          </el-table-column>\n        </el-table-column>\n      </el-table>\n    </el-white>\n    <asset-select v-bind:asset-select=\"assetSelect\" @exit=\"closeAssetSelect\"\n                  @submit-select=\"getAssetInfo\"></asset-select>\n\n\n  </div>\n</template>\n\n<script>\n  import Treeselect from '@riophae/vue-treeselect'\n  import '@riophae/vue-treeselect/dist/vue-treeselect.css'\n  import { getListFirst, saveOrUpdate, remove } from '@/api/yxgl/bdyxgl/qxgl'\n  import AssetSelect from '@/components/AssetSelect'\n\n  export default {\n    name: 'czpcxtj',\n    components: { AssetSelect, Treeselect },\n    watch: {\n      /**\n       * 监听下拉树筛选\n       * @param val\n       */\n      filterText(val) {\n        this.$refs.tree.filter(val)\n      }\n    },\n    mounted() {\n      //获取变电缺陷记录列表\n      this.getData()\n    },\n    data() {\n      return {\n        //查询统计tab页\n        tableDataToTj: [\n          {\n            dw: '港东分公司',\n            byfxwjqx: 10,\n            ljfxwjqx: 120,\n            ljxcwjqx: 119,\n            byfxyzqx: 20,\n            ljfxyzqx: 26,\n            ljxcyzqx: 25,\n            byfxybqx: 5,\n            ljfxybqx: 20,\n            ljxcybqx: 19\n          },\n          {\n            dw: '港东分公司',\n            byfxwjqx: 10,\n            ljfxwjqx: 120,\n            ljxcwjqx: 119,\n            byfxyzqx: 20,\n            ljfxyzqx: 26,\n            ljxcyzqx: 25,\n            byfxybqx: 5,\n            ljfxybqx: 20,\n            ljxcybqx: 19\n          },\n          {\n            dw: '港东分公司',\n            byfxwjqx: 10,\n            ljfxwjqx: 120,\n            ljxcwjqx: 119,\n            byfxyzqx: 20,\n            ljfxyzqx: 26,\n            ljxcyzqx: 25,\n            byfxybqx: 5,\n            ljfxybqx: 20,\n            ljxcybqx: 19\n          },\n          {\n            dw: '港东分公司',\n            byfxwjqx: 10,\n            ljfxwjqx: 120,\n            ljxcwjqx: 119,\n            byfxyzqx: 20,\n            ljfxyzqx: 26,\n            ljxcyzqx: 25,\n            byfxybqx: 5,\n            ljfxybqx: 20,\n            ljxcybqx: 19\n          },\n          {\n            dw: '港东分公司',\n            byfxwjqx: 10,\n            ljfxwjqx: 120,\n            ljxcwjqx: 119,\n            byfxyzqx: 20,\n            ljfxyzqx: 26,\n            ljxcyzqx: 25,\n            byfxybqx: 5,\n            ljfxybqx: 20,\n            ljxcybqx: 19\n          },\n          {\n            dw: '港东分公司',\n            byfxwjqx: 10,\n            ljfxwjqx: 120,\n            ljxcwjqx: 119,\n            byfxyzqx: 20,\n            ljfxyzqx: 26,\n            ljxcyzqx: 25,\n            byfxybqx: 5,\n            ljfxybqx: 20,\n            ljxcybqx: 19\n          },\n          {\n            dw: '港东分公司',\n            byfxwjqx: 10,\n            ljfxwjqx: 120,\n            ljxcwjqx: 119,\n            byfxyzqx: 20,\n            ljfxyzqx: 26,\n            ljxcyzqx: 25,\n            byfxybqx: 5,\n            ljfxybqx: 20,\n            ljxcybqx: 19\n          },\n          {\n            dw: '港东分公司',\n            byfxwjqx: 10,\n            ljfxwjqx: 120,\n            ljxcwjqx: 119,\n            byfxyzqx: 20,\n            ljfxyzqx: 26,\n            ljxcyzqx: 25,\n            byfxybqx: 5,\n            ljfxybqx: 20,\n            ljxcybqx: 19\n          },\n          {\n            dw: '港东分公司',\n            byfxwjqx: 10,\n            ljfxwjqx: 120,\n            ljxcwjqx: 119,\n            byfxyzqx: 20,\n            ljfxyzqx: 26,\n            ljxcyzqx: 25,\n            byfxybqx: 5,\n            ljfxybqx: 20,\n            ljxcybqx: 19\n          }\n        ],\n        //统计维度默认值\n        gzpTjWd: 0,\n        //弹出框中表格数据\n        propTableData: {\n          sel: null, // 选中行\n          colFirst: []\n        },\n        activeBtn: 0,\n        assetSelect: false,\n        //弹出框控制内容disabled\n        dialogFormDisabled: false,\n        // 统计纬度多选框数据\n        statistical: [],\n        //选中得行数\n        selectRows: [],\n        //查询参数\n        queryParams: {\n          pageNum: 1,\n          pageSize: 10\n        },\n        // 查询数据总条数\n        total: 0,\n        // 对话框标题\n        title: '',\n        // 对话框是否打开\n        open: false,\n        // 新增/修改表单\n        form: {\n          //变电\n          lx: 2\n        },\n        // 多选框选中的数据id\n        ids: [],\n        // 是否单选\n        single: true,\n        // 是否多选\n        multiple: true,\n        // 选中的数据\n        selectData: [],\n        // 表单是否可编辑\n        isEditable: true,\n        filterInfo: {\n          data: {\n            ssdz: '',\n            qxxz: '',\n            sbxh: ''\n          },\n          fieldList: [\n            { label: '单位', type: 'input', value: 'dw' }\n          ]\n        },\n        params: {\n          //变电\n          lx: 2,\n          lczt: ''\n        },\n        openSb: false,\n        form1: {}\n      }\n    },\n    methods: {\n\n      //本月发现\n      handleByFxClick(row) {\n\n      },\n      //累计发现\n      handleLjFxClick(row) {\n\n      },\n      //累计消除\n      handleLjXcClick(row) {\n\n      },\n      //选中更换事件\n      updateTjwdChange() {\n\n      },\n      //tab页点击事件\n      handleClick(tab) {\n        console.log('tab:' + tab)\n        console.log(tab)\n        this.getData()\n      },\n\n      //锚点跳转\n      goAnchor(selector, index) {\n        this.activeBtn = index\n        this.$el.querySelector(selector).scrollIntoView()\n      },\n      handleSh() {\n        if (this.selectRows.length < 1) {\n          this.$message.warning('请选择数据进行审批!!!')\n          return\n        }\n        this.openSb = true\n      },\n      closeAssetSelect() {\n        this.assetSelect = false\n      },\n      showAssetSelect() {\n        this.assetSelect = true\n      },\n      getAssetInfo(info) {\n        this.assetSelect = false\n        this.form.sb = info.sbmc\n      },\n\n      //变电缺陷记录查询\n      async getData(params) {\n        try {\n          const param = { ...this.params, ...params }\n          const { data, code } = await getListFirst(param)\n          console.log('+++++++++数据+++++++++++++++')\n          console.log(data)\n          if (code === '0000') {\n            this.tableAndPageInfo.tableData = data.records\n            this.tableAndPageInfo.pager.total = data.total\n          }\n        } catch (e) {\n          console.log(e)\n        }\n      },\n\n      //删除\n      deleteRow() {\n        if (this.selectRows.length < 1) {\n          this.$message.warning('请选择正确的数据！！！')\n          return\n        }\n        let ids = this.selectRows.map(item => {\n          return item.objId\n        })\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove(ids).then(({ code }) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getData()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n        this.tableAndPageInfo.pager.pageResize = 'Y'\n        this.getData()\n      },\n\n      /**\n       * 登记按钮\n       */\n      handleAdd() {\n        this.reset()\n        //变电\n        this.form.lx = 2\n        this.dialogFormDisabled = false\n        this.title = '缺陷登记'\n        this.open = true\n      },\n\n      /**\n       * 修改按钮\n       */\n      handleUpdate(row) {\n        this.form = { ...row }\n        this.dialogFormDisabled = false\n        this.title = '缺陷修改'\n        this.open = true\n      },\n\n      /**\n       * 缺陷记录详情\n       * */\n      getDataInfo(row) {\n        this.form = { ...row }\n        this.dialogFormDisabled = true\n        this.title = '缺陷详情'\n        this.open = true\n      },\n\n      /**\n       * 提交表单\n       */\n      async submitForm() {\n        try {\n          let { code } = await saveOrUpdate(this.form)\n          if (code === '0000') {\n            this.$message.success('操作成功')\n          }\n        } catch (e) {\n          console.log(e)\n        }\n        //恢复分页\n        this.tableAndPageInfo.pager.pageResize = 'Y'\n        //重新查数据\n        this.getData()\n        //关闭弹窗\n        this.open = false\n      },\n\n      //行选中\n      selectChange(rows) {\n        this.selectRows = rows\n      },\n\n      /**\n       * 下拉树筛选\n       * @param value\n       * @param data\n       * @return {boolean}\n       */\n      filterNode(value, data) {\n        if (!value) return true\n        return data.label.indexOf(value) !== -1\n      },\n\n      /**\n       * 多选款选中数据\n       * @param row\n       */\n      handleSelectionChange(selection) {\n        this.ids = selection.map(item => item.id)\n        this.single = selection.length !== 1\n        this.multiple = !selection.length\n        this.selectData = selection\n      },\n\n      /**\n       * 单击行选中\n       * @param row\n       */\n      rowClick(row) {\n        this.$refs.defectTable.toggleRowSelection(row)\n      },\n\n      /**\n       * 删除按钮\n       */\n      handleDelete() {\n        this.$confirm('是否确认删除当前勾选的数据?', '警告', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(function() {\n          // return del(this.ids); 删除\n        }).then(() => {\n          this.getData()\n          this.msgSuccess('删除成功')\n        }).catch(function() {\n        })\n      },\n\n      /**\n       * 对话框关闭时处理\n       */\n      handleClose() {\n        this.reset()\n        this.open = false\n      },\n      //筛选框重置\n      filterReset() {\n\n      },\n\n      /**\n       * 表单重置\n       */\n      reset() {\n        this.form = {}\n        this.resetForm('form')\n      }\n\n    }\n  }\n</script>\n\n<style scoped>\n  .title {\n    font-size: 16px;\n    font-weight: bold;\n  }\n\n  .el-select {\n    width: 100%;\n  }\n\n  .box-card {\n    margin-bottom: 2vh !important;\n  }\n\n  /* 设置滚动条的样式 */\n  ::-webkit-scrollbar {\n    width: 12px;\n  }\n\n  /* 滚动槽 */\n  ::-webkit-scrollbar-track {\n  //-webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.3);\n    border-radius: 10px;\n  }\n\n  /* 滚动条滑块 */\n  ::-webkit-scrollbar-thumb {\n    border-radius: 10px;\n    background: rgba(0, 0, 0, 0.1);\n  //-webkit-box-shadow: gba(0, 0, 0, 0.5);\n  }\n\n  ::-webkit-scrollbar-thumb:window-inactive {\n    background: rgba(0, 0, 0, 0.1);\n  }\n\n  [data-v-67a974b1]::-webkit-scrollbar {\n    width: 8px;\n  }\n\n  .item {\n    width: 225px;\n    float: left;\n  }\n\n  /*列表颜色设置*/\n  /deep/ .el-table th{\n    background-color: #e8f7f0;\n\n  }\n</style>\n"]}]}