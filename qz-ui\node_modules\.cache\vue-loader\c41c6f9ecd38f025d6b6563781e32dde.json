{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\bddzcz\\components\\czp_cx.vue?vue&type=template&id=4ed5e792&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\bddzcz\\components\\czp_cx.vue", "mtime": 1749014418040}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}