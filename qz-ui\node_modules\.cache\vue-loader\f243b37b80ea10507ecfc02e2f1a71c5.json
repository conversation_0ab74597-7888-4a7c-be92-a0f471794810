{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\Index\\SbInfo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\Index\\SbInfo.vue", "mtime": 1755539914743}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["SbInfo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "SbInfo.vue", "sourceRoot": "src/components/Index", "sourcesContent": ["<template>\n  <div :sbInfoSpanNum=\"sbInfoSpanNum\" class=\"borderCls\" :class=\"sbIDivClass\">\n    <div style=\"height: 90%\">\n      <div class=\"txtTitle\">\n        <span class=\"txtContent\">设备基本信息</span>\n      </div>\n      <div class=\"sbInfo_cont\">\n        <ul>\n          <li>\n            <i class=\"icon bd\"></i>变电\n          </li>\n          <li class=\"cont_child\"><span class=\"fontCls\">变电站：</span><span>个</span><span>{{this.bdzNum}}</span></li>\n          <li class=\"cont_child\"><span class=\"fontCls\">变压器：</span><span>个</span><span>{{this.byqNum}}</span></li>\n<!--          <li class=\"cont_child\"><span>总容量：</span><span>个</span><span>336</span></li>-->\n        </ul>\n        <ul>\n          <li><i class=\"icon line\"></i>线路</li>\n          <li class=\"cont_child\"><span class=\"fontCls\">线路数：</span><span>条</span><span>{{this.xlNum}}</span></li>\n          <li class=\"cont_child\"><span class=\"fontCls\">长度：</span><span>Km</span><span>{{this.xlcd}}</span></li>\n          <li class=\"cont_child gts\"><span class=\"fontCls\">杆塔数：</span><span>个</span><span>{{this.gtNum}}</span></li>\n          <li class=\"cont_child gts\"><span class=\"fontCls\">电缆长度：</span><span>Km</span><span>{{this.gtcd}}</span></li>\n        </ul>\n        <ul>\n          <li><i class=\"icon pds\"></i>配电室</li>\n          <li class=\"cont_child\"><span class=\"fontCls\">配电室：</span><span>座</span><span>{{this.pdsNum}}</span></li>\n          <li class=\"cont_child\"><span class=\"fontCls\">配电柜：</span><span>个</span><span>{{this.pdgNum}}</span></li>\n        </ul>\n        <ul>\n          <li>\n            <i class=\"icon bd\"></i>新能源\n          </li>\n          <li class=\"cont_child\"><span class=\"fontCls\">光伏电站：</span><span>个</span><span>{{this.bdzNum}}</span></li>\n        </ul>\n      </div>\n<!--      <div  ref=\"gdchart\" class=\"tjHeight\">\n      </div>-->\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nimport { mapState } from 'vuex'\nimport {getSbInfo} from '@/api/index/SbInfo'\n\nexport default {\n  name: 'SbInfo',//工单完成情况\n  props:{\n    sbInfoSpanNum:{\n      type:Number,\n      default:7,\n    },\n    sbIDivClass:'',\n  },\n  data() {\n    return {\n      //用于布局动态设置高度\n      activeClass:1,\n      tjCharts:null,//统计图对象\n      //默认值\n      fpNum:[302,120,278],\n      finNum:[150,100,234],\n      bdzNum:null,//变电站\n      byqNum:null,//变压器\n      xlcd:null,//线路长度\n      xlNum:null,//线路数量\n      gtNum:null,//杆塔数量\n      gtcd:null,//电缆长度\n      pdsNum:null,//配电室数量\n      pdgNum:null,//配电柜数量\n    }\n  },\n  mounted() {\n   // this.showGdCharts();\n   this.showSbInfo();\n  },\n  methods: {\n    //工单完成情况\n    showGdCharts(){\n      let bar_dv = this.$refs.gdchart;\n      let myChart = echarts.init(bar_dv);\n      this.tjCharts = myChart;\n\n      let option;\n      option = {\n        title: {\n          subtext: '单位：个',\n          left: '4%'\n        },\n        legend: {\n          top:'3%',\n          right: '6%',\n          textStyle:{\n            fontSize:16,\n          }\n        },\n        grid: {\n          left: '4%',\n          right: '4%',\n          bottom: '1%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'category',\n          data:['变电', '线路', '配电']\n        },\n        yAxis: {},\n        series: [\n          {\n            type: 'bar' ,\n            name:'分配工单数',\n            barWidth:24,\n            itemStyle: {\n              color: new echarts.graphic.LinearGradient(\n                0, 0, 0, 1,\n                [\n                  {offset: 0, color: '#11ba6d'},\n                  {offset: 1, color: '#b5e0cd'}\n                ]\n              )\n            },\n            data:this.fpNum,\n          },\n          {\n            type: 'bar',\n            name:'已完成工单数',\n            barWidth:24,\n            itemStyle: {\n              color: new echarts.graphic.LinearGradient(\n                0, 0, 0, 1,\n                [\n                  {offset: 0, color: '#ff9a09'},\n                  {offset: 1, color: '#faead3'}\n                ]\n              )\n            },\n            data:this.finNum\n          }\n        ]\n      };\n      option && myChart.setOption(option);\n    },\n    //重新加载eCharts图表\n    reloadCharts(){\n      this.tjCharts.resize();\n    },\n    async showSbInfo(){\n      let {data,code} = await getSbInfo()\n      this.bdzNum=data.BdzTj//变电站\n      this.byqNum=data.PdByqTj,//变压器\n      this.xlcd= Number(data.cd).toFixed(2),//线路长度\n      this.xlNum=data.xl,//线路数量\n      this.gtNum=data.tower,//杆塔数量\n      this.gtcd=(data.dl/1000).toFixed(2),//电缆长度\n      this.pdsNum=data.pdz,//配电室数量\n      this.pdgNum=data.pdg//配电柜数量\n      this.gfdzNum=data.gfxdz//光伏电站数量\n    }\n  },\n  computed: {\n    ...mapState([\"settings\",\"app\"]),\n    //工作票完成情况\n    workOrder() {\n      return this.$store.state.settings.workOrder;\n    },\n    //菜单伸缩状态\n    opened() {\n      return this.$store.state.app.sidebar.opened;\n    },\n  },\n  watch:{\n    workOrder(newVal){\n      if(newVal){\n        this.reloadCharts();\n      }\n    },\n    wkoSpanNum(newVal){\n      this.reloadCharts();\n    },\n    opened(newVal) {\n      //重新加载统计图\n      setTimeout(()=>{\n        //this.tjCharts.resize();\n      },200)\n    }\n  }\n}\n</script>\n<style scoped lang=\"scss\">\n.fontCls{\n  font-weight: 800;\n}\n.spanTxt{\n  float: right;\n  padding-right: 4%;\n  font-size: 16px;\n  color: #b1b1b1;\n  font-weight: 500;\n}\n.sbInfo_cont{\n  height: 100%;\n  padding:0 0 12px 12px;\n  .icon{\n    display: inline-block;\n    width: 17px;\n    height: 17px;\n    margin-right: 10px;\n  }\n  .bd{\n    background: url('../../assets/image/equ_icon1.png') no-repeat;\n    background-size: 100% 100%;\n  }\n  .line{\n    background: url('../../assets/image/equ_icon2.png') no-repeat;\n    background-size: 100% 100%;\n  }\n  .pds{\n    background: url('../../assets/image/equ_icon3.png') no-repeat;\n    background-size: 100% 100%;\n  }\n  ul{\n    list-style-type: none;\n    margin:0;\n    padding:0;\n    li:first-child{\n      line-height: 37px;\n      font-weight: 600;\n      color: #4d4d4d;\n      font-size: 18px;\n    }\n  }\n  .cont_child{\n    color: #b1b1b1;\n    width: 46%;\n    height: 30px;\n    line-height: 30px;\n    display: inline-block;\n    margin-right: 15px;\n    padding: 0 4px;\n    box-shadow: #e7e0e0 0.5px 0.5px 0.5px 1px;\n    span:first-child{\n      float: left;\n     /* width: 80px;*/\n    }\n    span:nth-child(2){\n      float:right;\n      margin-right: 0;\n  /*    max-width: 26px;*/\n    }\n    span:last-child{\n      color:#4d4d4d;\n      font-size: 25px;\n      float: right;\n/*      max-width: 89px;*/\n    }\n  }\n  .gts{\n    margin-top:10px\n  }\n}\n.txtContent{\n  width: 142px;\n}\n</style>\n\n"]}]}