{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\syzxm.vue?vue&type=style&index=0&id=4acb0a7c&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\syzxm.vue", "mtime": 1706897323692}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmJveC1jYXJkIHsKICBtYXJnaW4tYm90dG9tOiAxNXB4OwoKICAuZWwtY2FyZF9faGVhZGVyIHsKICAgIGJhY2tncm91bmQtY29sb3I6IHJnYigyMzUsIDI0NSwgMjU1KSAhaW1wb3J0YW50OwogIH0KfQoKLml0ZW0gewogIHdpZHRoOiAyMDBweDsKICBoZWlnaHQ6IDE0OHB4OwogIGZsb2F0OiBsZWZ0Owp9Cg=="}, {"version": 3, "sources": ["syzxm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0sBA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "syzxm.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk", "sourcesContent": ["<template>\n\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row :gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card class=\"aside_height\" shadow=\"never\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备类型</span>\n          </div>\n          <div style=\" overflow: auto;height: 84vh\">\n            <el-tree id=\"tree\"\n                     highlight-current\n                     :props=\"props\"\n                     :load=\"loadNode\"\n                     lazy\n                     :default-expanded-keys=\"['1']\"\n                     @node-expand=\"handleNodeClick\"\n                     @node-click=\"handleNodeClick\"\n            />\n          </div>\n        </el-card>\n      </el-col>\n\n\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 180, itemWidth: 300 }\"\n          @handleReset=\"getReset\"\n        />\n\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"addSensorButton\" :disabled=\"addDisabled\"\n            >新增\n            </el-button>\n            <!--<el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"gettableAndPageInforesizableDelete\"-->\n            <!--&gt;删除-->\n            <!--</el-button>-->\n            <!-- <el-button type=\"primary\" icon=\"el-icon-edit\" @click=\"addZxmKxz\" :disabled=\"whmjzButtonDisabled\"\n            >维护枚举项\n            </el-button> -->\n          </div>\n          <comp-table :table-and-page-info=\"tableAndPageInfo\"\n                      @update:multipleSelection=\"handleSelectionChange\"\n                      height=\"69.6vh\"\n          >\n            <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\"\n                             width=\"160\"\n                             :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button @click=\"undateDetails(scope.row)\" v-show=\"scope.row.createBy == currentUser\" type=\"text\"\n                           size=\"small\" title=\"修改\"  class='el-icon-edit'\n                >\n                </el-button>\n                <el-button @click=\"getDetailsInfo(scope.row)\" type=\"text\" size=\"small\" title=\"详情\" class=\"el-icon-view\"></el-button>\n                <el-button @click=\"getDelete(scope.row)\" v-show=\"scope.row.createBy == currentUser\" type=\"text\"\n                           size=\"small\" title=\"删除\" class=\"el-icon-delete\"\n                >\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!-- 详情/新增/修改 -->\n    <el-dialog :title=title :visible.sync=\"isShowDetails\" width=\"60%\" @close=\"handleClose\" v-dialogDrag>\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :disabled=\"isDisabled\" :rules=\"rules\">\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"子项目名称：\" prop=\"zxmmc\">\n              <el-input v-model=\"form.zxmmc\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"子项目描述：\" prop=\"zxmms\">\n              <el-input v-model=\"form.zxmms\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"数值单位：\" prop=\"szdw\">\n              <el-input v-model=\"form.szdw\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"数据精度：\" prop=\"sjjd\">\n              <el-input v-model=\"form.sjjd\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"结果类型：\" prop=\"jglx\">\n              <el-select v-model=\"form.jglx\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in jglxOptionsSelectedList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"计算列：\" prop=\"jsl\">\n              <el-select v-model=\"form.jsl\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in jslOptionsSelectedList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否为空：\" prop=\"sfkwk\">\n              <el-select v-model=\"form.sfkwk\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in sfkwkOptionsSelectedList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否显示：\" prop=\"sfxs\">\n              <el-select v-model=\"form.sfxs\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in sfxsOptionsSelectedList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button v-if=\"title==='新增' || title==='修改'\" type=\"primary\" @click=\"commitForm\">确 认</el-button>\n      </div>\n    </el-dialog>\n    <!--枚举项值弹出框-->\n    <el-dialog :title=\"mjxDialogTitle\" :visible.sync=\"isShowMjzDialog\" width=\"50%\" v-dialogDrag>\n      <el-white class=\"button-group\">\n        <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n          <el-col\n            style=\"display: flex;justify-content: space-between;align-items: center;\"\n          >\n            <div>\n              <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"addMjz\"\n              >新增\n              </el-button>\n              <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"deleteMjz\"\n              >删除\n              </el-button>\n            </div>\n          </el-col>\n        </el-row>\n      </el-white>\n      <el-table stripe border :data=\"mjzDataList\" @selection-change=\"handleSelectionMjzChange\"\n                :header-cell-style=\"{'text-align':'center'}\" :cell-style=\"{'text-align':'center'}\"\n      >\n        <el-table-column type=\"selection\" width=\"55\"/>\n        <el-table-column label=\"序号\" type=\"index\" width=\"50\">\n        </el-table-column>\n        <el-table-column label=\"枚举值\" prop=\"kxz\" :show-overflow-tooltip=\"true\"/>\n      </el-table>\n      <pagination\n        v-show=\"Mjztotal>0\"\n        :total=\"Mjztotal\"\n        :page.sync=\"mjzQueryParams.pageNum\"\n        :limit.sync=\"mjzQueryParams.pageSize\"\n        @pagination=\"getMjzDataList\"\n      />\n    </el-dialog>\n    <!--枚举值新增弹窗-->\n    <el-dialog :title=\"mjzAddDialogTitle\" :visible.sync=\"isShowMjzAddDialog\" width=\"30%\" v-dialogDrag>\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"mjzAddForm\" :disabled=\"mjzAddDialogDisabled\">\n        <el-row :gutter=\"8\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"枚举值：\" prop=\"zxmmc\">\n              <el-input v-model=\"mjzAddForm.kxz\"/>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" v-show=\"addMjzDialogButtonShow\">\n        <el-button @click=\"closeAddMjzDialog\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"commitAddMjzForm\">确 认</el-button>\n      </div>\n    </el-dialog>\n  </div>\n\n</template>\n\n<script>\nimport {\n  getPageDataList,\n  getPageKxzDataList,\n  remove,\n  removeKxzData,\n  saveOrUpdate,\n  saveOrUpdateKxzData\n} from '@/api/dagangOilfield/bzgl/syzxm'\nimport { getDeviceClassTreeNodeByPid } from '@/api/dagangOilfield/bzgl/sblxwh/sblxwh'\n\nexport default {\n  name: 'lpbzk',\n  data() {\n    return {\n      currentUser: this.$store.getters.name,\n      //新增按钮控制\n      addDisabled: true,\n      //树结构懒加载参数\n      props: {\n        label: 'name',\n        children: 'zones',\n        isLeaf: (data, node) => {\n          if (node.level === 2) {\n            return true\n          }\n        }\n      },\n      //枚举值新增弹出框底部按钮控制显示\n      addMjzDialogButtonShow: true,\n      //控制枚举值新增弹出框内容是否可编辑\n      mjzAddDialogDisabled: false,\n      //枚举值新增form表单\n      mjzAddForm: {\n        syzxmid: undefined,\n        kxz: undefined\n      },\n      //枚举值新增弹出框标题\n      mjzAddDialogTitle: '新增',\n      //枚举值新增弹出框控制\n      isShowMjzAddDialog: false,\n      //选中子项目时获取到的第一行数据用来查询枚举值\n      mjzRowForm: {},\n      //维护枚举值button按钮\n      whmjzButtonDisabled: true,\n      //枚举值数据\n      mjzDataList: [],\n      //枚举值参数\n      mjzQueryParams: {\n        syzxmid: undefined,\n        pageSize: 10,\n        pageNum: 1\n      },\n      //枚举值总数\n      Mjztotal: 0,\n      //枚举项弹出框标题\n      mjxDialogTitle: '枚举值维护',\n      //枚举项弹出框\n      isShowMjzDialog: false,\n\n      //删除选择列\n      selectRows: [],\n      //表单验证\n      rules: {\n        zxmmc: [\n          { required: true, message: '请输入子项目名称', trigger: 'blur' }\n        ]\n      },\n      //筛选框\n      filterInfo: {\n        data: {\n          jglx: '',\n          zxmmc: ''\n        },\n        fieldList: [\n          {\n            label: '结果类型',\n            value: 'jglx',\n            type: 'select',\n            options: [\n              { label: '图片', value: '图片' },\n              { label: '数字', value: '数字' },\n              { label: '日期', value: '日期' },\n              { label: '单选', value: '单选' },\n              { label: '枚举', value: '枚举' },\n              { label: '字符', value: '字符' }\n            ],\n            clearable: true\n          },\n          {\n            label: '子项目名称',\n            value: 'zxmmc',\n            type: 'input',\n            clearable: true\n          }\n        ]\n      },\n      //列表页\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: '子项目名称', prop: 'zxmmc', minWidth: '150' },\n          { label: '数值单位', prop: 'szdw', minWidth: '70' },\n          { label: '结果类型', prop: 'jglx', minWidth: '100' },\n          { label: '计算列', prop: 'jsl', minWidth: '60' },\n          { label: '是否可为空', prop: 'sfkwk', minWidth: '80' },\n          { label: '是否显示', prop: 'sfxs', minWidth: '70' },\n          { label: '子项目描述', prop: 'zxmms', minWidth: '150' },\n          { label: '数据精度', prop: 'sjjd', minWidth: '100' }\n          // {\n          //   prop: 'operation',\n          //   label: '操作',\n          //   minWidth: '100px',\n          //   style: {display: 'block'},\n          //   //操作列固定再右侧\n          //   fixed: 'right',\n          //   operation: [\n          //     {name: '修改', clickFun: this.undateDetails},\n          //     {name: '详情', clickFun: this.getDetailsInfo},\n          //   ]\n          // },\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      //查询试验子项目参数\n      querySyzxmParam: {\n        pageNum: 1,\n        pageSize: 10,\n        sblxbm: undefined\n      },\n      sblxbm: '',\n      //结果类型下拉框数据\n      jglxOptionsSelectedList: [\n        { label: '图片', value: '图片' },\n        { label: '数字', value: '数字' },\n        { label: '日期', value: '日期' },\n        { label: '单选', value: '单选' },\n        { label: '枚举', value: '枚举' },\n        { label: '字符', value: '字符' }\n      ],\n      //计算列\n      jslOptionsSelectedList: [\n        { label: '是', value: '是' },\n        { label: '否', value: '否' }\n      ],\n      //是否为空\n      sfkwkOptionsSelectedList: [\n        { label: '是', value: '是' },\n        { label: '否', value: '否' }\n      ],\n      //是否显示\n      sfxsOptionsSelectedList: [\n        { label: '是', value: '是' },\n        { label: '否', value: '否' }\n      ],\n\n      //form表单\n      form: {},\n      //是否显示弹框\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //表单数据\n      dataTable: [],\n      //标题\n      title: '',\n\n      //组织树\n      treeOptions: [\n        {\n          label: '断路器'\n        }, {\n          label: '变压器',\n          children: [{\n            label: '冷却系统',\n            children: [{\n              label: '温控运行情况'\n\n            }, {\n              label: '油箱'\n\n            }, {\n              label: '铁芯'\n\n            }, {\n              label: '绕组'\n\n            }]\n          }]\n        }],\n      //删除是否可用\n      multipleSensor: true,\n      //查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        ancuoTerm: ''\n      },\n      //表单开关\n      isSearchShow: false,\n      //工作票类型下拉菜单\n      gzpTypeOptions: [\n        {\n          value: 'type1',\n          label: '类型1'\n        }, {\n          value: 'type2',\n          label: '类型2'\n        }\n      ]\n\n    }\n  },\n  watch: {},\n  created() {\n    this.getData()\n\n  },\n  methods: {\n    //懒加载函数\n    loadNode(node, resolve) {\n      let TreeparamMap = {\n        pid: '',\n        spbLogo: ['输电设备', '变电设备','配电设备']\n      }\n      if (node.level === 0) {\n        TreeparamMap.pid = 'sb'\n        return this.getTreeNode(TreeparamMap, resolve)\n      }\n      setTimeout(() => {\n        TreeparamMap.pid = node.data.code\n        this.getTreeNode(TreeparamMap, resolve)\n      }, 500)\n\n    },\n\n    //获取树节点数据\n    getTreeNode(paramMap, resolve) {\n      getDeviceClassTreeNodeByPid(paramMap).then(res => {\n        let treeNodes = []\n        res.data.forEach(item => {\n          let node = {\n            name: item.name,\n            level: item.level,\n            id: item.id,\n            pid: item.pid,\n            leaf: false,\n            code: item.code\n          }\n          treeNodes.push(node)\n        })\n        resolve(treeNodes)\n      })\n    },\n\n    //树节点点击事件\n    handleNodeClick(data) {\n      if (data.level === '1') {\n        //开放新增按钮\n        this.addDisabled = false\n        this.treeForm = data\n        this.sblxbm = data.code\n        this.querySyzxmParam.sblxbm = data.code\n        this.getData()\n      } else {\n        this.addDisabled = true\n      }\n    },\n\n    //枚举值新增按钮\n    addMjz() {\n      this.mjzAddForm.kxz = undefined\n      this.isShowMjzAddDialog = true\n    },\n    //提交新增枚举值弹出框表单\n    commitAddMjzForm() {\n      this.mjzAddForm.syzxmid = this.mjzRowForm.objId\n      console.log(this.mjzAddForm)\n      saveOrUpdateKxzData(this.mjzAddForm).then(res => {\n        if (res.code == '0000') {\n          this.$message.success('操作成功！')\n          this.isShowMjzAddDialog = false\n          this.getMjzDataList()\n        }\n      })\n    },\n    //取消按钮(枚举值新增弹出框)\n    closeAddMjzDialog() {\n      this.isShowMjzAddDialog = false\n    },\n    //获取枚举值列表方法\n    getMjzDataList() {\n      this.mjzQueryParams.syzxmid = this.mjzRowForm.objId\n      getPageKxzDataList(this.mjzQueryParams).then(res => {\n        console.log(res)\n        this.Mjztotal = res.data.total\n        this.mjzDataList = res.data.records\n      })\n    },\n    //枚举值行选中事件\n    handleSelectionMjzChange(rows) {\n      this.selectedKxzDataRow = rows\n\n    },\n    //删除枚举值列表\n    deleteMjz() {\n      if (this.selectedKxzDataRow.length < 1) {\n        this.$message.warning('请选择正确的数据！！！')\n        return\n      }\n      let ids = this.selectedKxzDataRow.map(item => {\n        return item.objId\n      })\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        removeKxzData(ids).then(({ code }) => {\n          if (code === '0000') {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            })\n            this.getMjzDataList()\n          } else {\n            this.$message({\n              type: 'error',\n              message: '删除失败!'\n            })\n            this.getMjzDataList()\n          }\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        })\n      })\n    },\n    //维护枚举值按钮\n    addZxmKxz() {\n      if (this.mjzRowForm.jglx != '枚举') {\n        this.$message.warning('请选择结果类型为枚举类型的数据！')\n      } else {\n        //打开弹窗\n        this.isShowMjzDialog = true\n        this.mjzQueryParams.syzxmid = this.mjzRowForm.objId\n        //获取枚举值列表\n        this.getMjzDataList()\n      }\n    },\n    //行选中\n    handleSelectionChange(rows) {\n      this.selectRows = rows\n      this.whmjzButtonDisabled = rows.length != 1\n      //获取到当前行对象\n      this.mjzRowForm = rows[0]\n    },\n    //列表查询子项目列表\n    async getData(params) {\n      try {\n        this.querySyzxmParam = { ...this.querySyzxmParam, ...params }\n        const param = this.querySyzxmParam\n        const { data, code } = await getPageDataList(param)\n        if (code === '0000') {\n          this.tableAndPageInfo.tableData = data.records\n          this.tableAndPageInfo.pager.total = data.total\n        }\n      } catch (e) {\n        console.log(e)\n      }\n    },\n    //获取详情\n    getDetailsInfo(row) {\n      //打开弹窗\n      this.isShowDetails = true\n      //给表单赋值\n      this.form = row\n      //表单不可编辑\n      this.isDisabled = true\n      //设置弹出框标题\n      this.title = '详情'\n    },\n    //修改按钮\n    undateDetails(row) {\n      //打开弹窗\n      this.isShowDetails = true\n      //给表单赋值\n      this.form = row\n      //表单可编辑\n      this.isDisabled = false\n      //设置弹出框标题\n      this.title = '修改'\n    },\n    //添加按钮\n    addSensorButton() {\n      //打开弹窗\n      this.isShowDetails = true\n      //给表单置空\n      this.form = {}\n      //表单可编辑\n      this.isDisabled = false\n      //设置弹出框标题\n      this.title = '新增'\n    },\n    //删除按钮\n    getDelete(row) {\n      this.form = row\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        remove([this.form.objId]).then(({ code }) => {\n          if (code === '0000') {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            })\n            this.tableAndPageInfo.pager.pageResize = 'Y'\n            this.getData()\n          } else {\n            this.$message({\n              type: 'error',\n              message: '删除失败!'\n            })\n            this.tableAndPageInfo.pager.pageResize = 'Y'\n            this.getData()\n          }\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        })\n      })\n\n    },\n    //确认提交表单\n    commitForm() {\n      this.form.sblxbm = this.sblxbm\n      this.$refs['form'].validate((valid) => {\n        if (valid) {\n          saveOrUpdate(this.form).then(res => {\n            if (res.code === '0000') {\n              this.$message.success(res.msg)\n              this.tableAndPageInfo.pager.pageResize = 'Y'\n              this.getData()\n              this.isShowDetails = false\n            } else {\n              this.$message.error(res.msg)\n            }\n          })\n\n        }\n      })\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false\n    },\n    //定义重置方法\n    getReset() {\n      this.querySyzxmParam = {\n        pageNum: 1,\n        pageSize: 10,\n        sblxbm: undefined\n      }\n    },\n    //删除按钮\n    deleteSensorButton() {\n\n    },\n    //导出按钮\n    handleExport() {\n\n    },\n\n    //搜索\n    handleQuery() {\n\n    },\n    //重置\n    resetQuery() {\n      this.resetForm('queryForm')\n    },\n    //清空表单数据\n    handleClose() {\n      this.form = {}\n      this.$nextTick(() => {\n        this.form = this.$options.data().form\n        this.resetForm('form')\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n</style>\n"]}]}