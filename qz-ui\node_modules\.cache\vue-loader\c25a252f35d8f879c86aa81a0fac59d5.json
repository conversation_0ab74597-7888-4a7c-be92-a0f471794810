{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\Index\\WorkTicket.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\Index\\WorkTicket.vue", "mtime": 1755543656534}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["WorkTicket.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAsBA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "WorkTicket.vue", "sourceRoot": "src/components/Index", "sourcesContent": ["<template>\n  <div :workTSpanNum=\"workTSpanNum\" class=\"borderCls\" :class=\"wkoTDivClass\">\n    <div>\n      <div class=\"txtTitle\">\n        <span class=\"txtContent\">工作票</span>\n        <!-- <el-select  v-model=\"value\" class=\"selectBtn\" @change=\"changeYear\">\n          <el-option\n            v-for=\"item in options\"\n            :key=\"item.value\"\n            :label=\"item.label\"\n            :value=\"item.value\">\n          </el-option>\n        </el-select> -->\n        <el-date-picker type=\"year\" value-format=\"yyyy\" v-model=\"value\" class=\"selectBtn\" @change=\"changeYear\"/>\n      </div>\n      <div  ref=\"gdchart\" class=\"tjHeight\">\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nimport { mapState } from 'vuex'\nimport { fontSize} from '@/layout/mixin/publicFun'\nimport {countGzp} from \"@/api/yxgl/gzpgl/gzpgl\";\nimport {getDictTypeData} from '@/api/system/dict/data.js'\nimport { parseTime } from '@/utils/ruoyi'\n\nexport default {\n  name: 'WorkTicket',//工单完成情况\n  props:{\n    workTSpanNum:{\n      type:Number,\n      default:7,\n    },\n    wkoTDivClass:'',\n  },\n  data() {\n    return {\n      //用于布局动态设置高度\n      activeClass:1,\n      tjCharts:null,//统计图对象\n      //默认值\n      noFinNum:[],\n      finNum:[],\n      contNum:[],\n      value: parseTime(new Date(),'{y}'),\n      options: [],\n    }\n  },\n  mounted() {\n   this.getData(this.value)\n   window.addEventListener('resize',()=>{\n      this.reloadCharts();\n    })\n    // this.getYears();//获取年度下拉框\n  },\n  methods: {\n    getYears(){\n      getDictTypeData('shouye_nd').then(res=>{\n        res.data.forEach(item=>{\n          this.options.push({label:item.label,value:item.numvalue})\n        })\n      })\n    },\n    changeYear(val){\n      if(!val){\n        return false;\n      }\n      this.getData(val)\n    },\n    getData(year){\n      countGzp(year).then(res => {\n        this.noFinNum = res.data[2];\n        this.finNum = res.data[1];\n        this.contNum = res.data[0];\n        this.showGdCharts();\n      })\n    },\n    //工单完成情况\n    showGdCharts(){\n      let bar_dv = this.$refs.gdchart;\n      let myChart = echarts.init(bar_dv);\n      this.tjCharts = myChart;\n\n      let option;\n      option = {\n        title: {\n          subtext: '单位：个',\n          left: '4%'\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'cross',\n            crossStyle: {\n              color: '#999'\n            }\n          }\n        },\n        legend: {\n          itemWidth: fontSize(14),// 设置图例图形的宽\n          itemHeight: fontSize(14),\n          top:'3%',\n          right: '6%',\n          textStyle:{\n            fontSize:fontSize(16),\n          }\n        },\n        grid: {\n          left: '4%',\n          right: '4%',\n          bottom: '1%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'category',\n          data:['变电一种', '变电二种', '线路一种', '线路二种', '电缆一种', '电缆二种', '配电一种','配电二种','新能源一种','新能源二种'],\n          axisLabel:{interval: 0,rotate:30}\n        },\n        yAxis: {},\n        series: [\n          {\n            type: 'bar' ,\n            stack: 'Ad',\n            name:'完成数',\n            barWidth:fontSize(24),\n            itemStyle: {\n              color: new echarts.graphic.LinearGradient(\n                0, 0, 0, 1,\n                [\n                  {offset: 0, color: '#00D6B1'},\n                  {offset: 1, color: '#00D6B1'}\n                ]\n              )\n            },\n            data:this.finNum,\n          },\n          {\n            type: 'bar',\n            stack: 'Ad',\n            name:'执行数',\n            barWidth:fontSize(24),\n            itemStyle: {\n              color: new echarts.graphic.LinearGradient(\n                0, 0, 0, 1,\n                [\n                  {offset: 0, color: '#76eed2'},\n                  {offset: 1, color: '#76eed2'}\n                ]\n              )\n            },\n            data:this.noFinNum\n          },\n          {\n            type: 'line' ,\n            name:'开票总数',\n            barWidth:fontSize(24),\n            itemStyle: {\n               normal: {label : {show: true},color: '#00C994',}\n            },\n            data:this.contNum,\n          },\n        ]\n      };\n      option && myChart.setOption(option);\n    },\n    //重新加载eCharts图表\n    reloadCharts(){\n      this.tjCharts.resize();\n    },\n  },\n  computed: {\n    ...mapState([\"settings\",\"app\"]),\n    //工作票完成情况\n    workOrder() {\n      return this.$store.state.settings.workOrder;\n    },\n    //菜单伸缩状态\n    opened() {\n      return this.$store.state.app.sidebar.opened;\n    },\n  },\n  watch:{\n    workOrder(newVal){\n      if(newVal){\n        this.reloadCharts();\n      }\n    },\n    wkoSpanNum(newVal){\n      this.reloadCharts();\n    },\n    opened(newVal) {\n      //重新加载统计图\n      /*setTimeout(()=>{\n        this.tjCharts.resize();\n      },200)*/\n    }\n  }\n}\n</script>\n<style>\n.spanTxt{\n  background: #fff;\n  height: 35px;\n  line-height: 35px;\n  margin-top: 6px;\n  margin-right: 9px;\n  padding-left: 12px;\n}\n.selectBtn{\n  font-size: 10kV然气站变电站4px;\n}\n</style>\n\n"]}]}