const state = {
  activeName: 'db', // 当前选中的标签页
  filterInfo: {
    module: "",
    todoUserName: "",
    handleUserName: "",
    applyUserName: ""
  }, // 筛选条件
  pagination: {
    pageSize: 10,
    pageNum: 1
  },
  scrollPosition: 0, // 滚动位置
  tableData: [], // 表格数据缓存（仅用于性能优化，不用于显示）
  total: 0, // 总数缓存
  needRefresh: false // 是否需要刷新数据
}

const mutations = {
  SET_ACTIVE_NAME: (state, activeName) => {
    state.activeName = activeName
  },
  SET_FILTER_INFO: (state, filterInfo) => {
    state.filterInfo = { ...state.filterInfo, ...filterInfo }
  },
  SET_PAGINATION: (state, pagination) => {
    state.pagination = { ...state.pagination, ...pagination }
  },
  SET_SCROLL_POSITION: (state, position) => {
    state.scrollPosition = position
  },
  SET_TABLE_DATA: (state, tableData) => {
    state.tableData = tableData
  },
  SET_TOTAL: (state, total) => {
    state.total = total
  },
  SET_NEED_REFRESH: (state, needRefresh) => {
    state.needRefresh = needRefresh
  },
  // 重置所有状态
  RESET_STATE: (state) => {
    state.activeName = 'db'
    state.filterInfo = {
      module: "",
      todoUserName: "",
      handleUserName: "",
      applyUserName: ""
    }
    state.pagination = {
      pageSize: 10,
      pageNum: 1
    }
    state.scrollPosition = 0
    state.tableData = []
    state.total = 0
    state.needRefresh = false
  }
}

const actions = {
  // 保存整个页面状态
  savePageState({ commit }, { activeName, filterInfo, pagination, scrollPosition, tableData, total }) {
    if (activeName !== undefined) commit('SET_ACTIVE_NAME', activeName)
    if (filterInfo !== undefined) commit('SET_FILTER_INFO', filterInfo)
    if (pagination !== undefined) commit('SET_PAGINATION', pagination)
    if (scrollPosition !== undefined) commit('SET_SCROLL_POSITION', scrollPosition)
    if (tableData !== undefined) commit('SET_TABLE_DATA', tableData)
    if (total !== undefined) commit('SET_TOTAL', total)
  },

  // 保存筛选条件
  saveFilterInfo({ commit }, filterInfo) {
    commit('SET_FILTER_INFO', filterInfo)
  },

  // 保存分页信息
  savePagination({ commit }, pagination) {
    commit('SET_PAGINATION', pagination)
  },

  // 保存滚动位置
  saveScrollPosition({ commit }, position) {
    commit('SET_SCROLL_POSITION', position)
  },

  // 保存表格数据
  saveTableData({ commit }, { tableData, total }) {
    commit('SET_TABLE_DATA', tableData)
    commit('SET_TOTAL', total)
  },

  // 重置状态
  resetState({ commit }) {
    commit('RESET_STATE')
  },

  // 标记需要刷新数据
  markNeedRefresh({ commit }) {
    commit('SET_NEED_REFRESH', true)
  },

  // 清除刷新标记
  clearRefreshFlag({ commit }) {
    commit('SET_NEED_REFRESH', false)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}