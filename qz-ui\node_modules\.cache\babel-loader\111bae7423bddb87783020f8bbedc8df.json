{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\xqxbzk.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\xqxbzk.vue", "mtime": 1706897323967}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZm9yLWVhY2giKTsKCnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5qb2luIik7CgpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwIik7CgpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuZnVuY3Rpb24ubmFtZSIpOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuZm9yLWVhY2giKTsKCk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwOwoKdmFyIF9vYmplY3RTcHJlYWQyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvb2JqZWN0U3ByZWFkMiIpKTsKCnJlcXVpcmUoInJlZ2VuZXJhdG9yLXJ1bnRpbWUvcnVudGltZSIpOwoKdmFyIF9hc3luY1RvR2VuZXJhdG9yMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovU2hhbW1wb29sL3dvcmsvY29kZS9kZ3l0LzAxXHU0RUUzXHU3ODAxL3F6LXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2FzeW5jVG9HZW5lcmF0b3IiKSk7Cgp2YXIgX2F1dGggPSByZXF1aXJlKCJAL3V0aWxzL2F1dGgiKTsKCnZhciBfcXhiemsgPSByZXF1aXJlKCJAL2FwaS9kYWdhbmdPaWxmaWVsZC9iemdsL3F4YnprIik7Cgp2YXIgX2RhdGEgPSByZXF1aXJlKCJAL2FwaS9zeXN0ZW0vZGljdC9kYXRhIik7Cgp2YXIgX2RldmljZVRyZWUgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdmlld3MvZGFnYW5nT2lsZmllbGQvYnpnbC9zYmJ6ay9kZXZpY2VUcmVlIikpOwoKdmFyIF9zYmJqID0gcmVxdWlyZSgiQC9hcGkvZGFnYW5nT2lsZmllbGQvYnpnbC9zYmx4d2gvc2JiaiIpOwoKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gewogIG5hbWU6ICd4cXhiemsnLAogIGNvbXBvbmVudHM6IHsKICAgIERldmljZVRyZWU6IF9kZXZpY2VUcmVlLmRlZmF1bHQKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldERhdGEoKTsKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvL+S4iuS8oOWbvueJh+aXtueahOivt+axguWktAogICAgICBoZWFkZXI6IHt9LAogICAgICBmaWx0ZXJJbmZvOiB7CiAgICAgICAgZGF0YTogewogICAgICAgICAgc2JibTogIiIsCiAgICAgICAgICBzYmJ3OiAiIiwKICAgICAgICAgIHF4bXM6ICIiLAogICAgICAgICAgZmx5ajogIiIsCiAgICAgICAgICBqc3l5OiAiIiwKICAgICAgICAgIGR5c2JseDogJycsCiAgICAgICAgICAvL+iuvuWkh+exu+Wei+WQjeensAogICAgICAgICAgc2JseDogIiIKICAgICAgICB9LAogICAgICAgIGZpZWxkTGlzdDogW3sKICAgICAgICAgIGxhYmVsOiAn6K6+5aSH56eN57G7JywKICAgICAgICAgIHR5cGU6ICdpbnB1dCcsCiAgICAgICAgICB2YWx1ZTogJ2R5c2JseCcKICAgICAgICB9LCB7CiAgICAgICAgICBsYWJlbDogJ+iuvuWkh+mDqOS7ticsCiAgICAgICAgICB0eXBlOiAnaW5wdXQnLAogICAgICAgICAgdmFsdWU6ICdzYmJtJwogICAgICAgIH0sIHsKICAgICAgICAgIGxhYmVsOiAn6K6+5aSH6YOo5L2NJywKICAgICAgICAgIHR5cGU6ICdpbnB1dCcsCiAgICAgICAgICB2YWx1ZTogJ3NiYncnCiAgICAgICAgfSwgewogICAgICAgICAgbGFiZWw6ICfnvLrpmbfmj4/ov7AnLAogICAgICAgICAgdHlwZTogJ2lucHV0JywKICAgICAgICAgIHZhbHVlOiAncXhtcycKICAgICAgICB9LCB7CiAgICAgICAgICBsYWJlbDogJ+WIhuexu+S+neaNricsCiAgICAgICAgICB0eXBlOiAnaW5wdXQnLAogICAgICAgICAgdmFsdWU6ICdmbHlqJwogICAgICAgIH0sIHsKICAgICAgICAgIGxhYmVsOiAn6ZqQ5oKj562J57qnJywKICAgICAgICAgIHR5cGU6ICdzZWxlY3QnLAogICAgICAgICAgdmFsdWU6ICdxeGRqTGlzdCcsCiAgICAgICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgICAgIG9wdGlvbnM6IFt7CiAgICAgICAgICAgIGxhYmVsOiAn5LiA6IisJywKICAgICAgICAgICAgdmFsdWU6ICfkuIDoiKwnCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGxhYmVsOiAn5Lil6YeNJywKICAgICAgICAgICAgdmFsdWU6ICfkuKXph40nCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGxhYmVsOiAn5Y2x5oClJywKICAgICAgICAgICAgdmFsdWU6ICfljbHmgKUnCiAgICAgICAgICB9XQogICAgICAgIH0sIHsKICAgICAgICAgIGxhYmVsOiAn5oqA5pyv5Y6f5ZugJywKICAgICAgICAgIHR5cGU6ICdpbnB1dCcsCiAgICAgICAgICB2YWx1ZTogJ2pzeXknCiAgICAgICAgfV0KICAgICAgfSwKICAgICAgdGFibGVBbmRQYWdlSW5mbzogewogICAgICAgIHBhZ2VyOiB7CiAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgdG90YWw6IDAsCiAgICAgICAgICBzaXplczogWzEwLCAyMCwgNTAsIDEwMF0KICAgICAgICB9LAogICAgICAgIG9wdGlvbjogewogICAgICAgICAgY2hlY2tCb3g6IHRydWUsCiAgICAgICAgICBzZXJpYWxOdW1iZXI6IHRydWUKICAgICAgICB9LAogICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgdGFibGVIZWFkZXI6IFsvLyB7IHByb3A6ICd6eW1jJywgbGFiZWw6ICfkuJPkuJonLCBtaW5XaWR0aDogJzEyMCcgfSwKICAgICAgICB7CiAgICAgICAgICBwcm9wOiAnc2JseG1jJywKICAgICAgICAgIGxhYmVsOiAn6K6+5aSH56eN57G7JywKICAgICAgICAgIG1pbldpZHRoOiAnMTgwJwogICAgICAgIH0sIHsKICAgICAgICAgIHByb3A6ICdzYmJtJywKICAgICAgICAgIGxhYmVsOiAn6K6+5aSH6YOo5Lu2JywKICAgICAgICAgIG1pbldpZHRoOiAnMTIwJwogICAgICAgIH0sIHsKICAgICAgICAgIHByb3A6ICdzYmJ3JywKICAgICAgICAgIGxhYmVsOiAn6K6+5aSH6YOo5L2NJywKICAgICAgICAgIG1pbldpZHRoOiAnMTIwJwogICAgICAgIH0sIHsKICAgICAgICAgIHByb3A6ICdxeG1zJywKICAgICAgICAgIGxhYmVsOiAn57y66Zm35o+P6L+wJywKICAgICAgICAgIG1pbldpZHRoOiAnMTIwJwogICAgICAgIH0sIHsKICAgICAgICAgIHByb3A6ICdmbHlqJywKICAgICAgICAgIGxhYmVsOiAn5YiG57G75L6d5o2uJywKICAgICAgICAgIG1pbldpZHRoOiAnMTIwJwogICAgICAgIH0sIHsKICAgICAgICAgIHByb3A6ICdxeGRqJywKICAgICAgICAgIGxhYmVsOiAn6ZqQ5oKj562J57qnJywKICAgICAgICAgIG1pbldpZHRoOiAnMTIwJwogICAgICAgIH0sIHsKICAgICAgICAgIHByb3A6ICdqc3l5JywKICAgICAgICAgIGxhYmVsOiAn5oqA5pyv5Y6f5ZugJywKICAgICAgICAgIG1pbldpZHRoOiAnMTIwJwogICAgICAgIH0KICAgICAgICAvKiB7CiAgICAgICAgICAgcHJvcDogJ29wZXJhdGlvbicsCiAgICAgICAgICAgbGFiZWw6ICfmk43kvZwnLAogICAgICAgICAgIG1pbldpZHRoOiAnMTMwcHgnLAogICAgICAgICAgIHN0eWxlOiB7IGRpc3BsYXk6ICdibG9jaycgfSwKICAgICAgICAgICAvL+aTjeS9nOWIl+WbuuWumuWGjeWPs+S+pwogICAgICAgICAgIGZpeGVkOiAncmlnaHQnLAogICAgICAgICAgIG9wZXJhdGlvbjogWwogICAgICAgICAgICAgeyBuYW1lOiAn5L+u5pS5JywgY2xpY2tGdW46IHRoaXMuZ2V0RGV0YWlscyB9LAogICAgICAgICAgICAgeyBuYW1lOiAn6K+m5oOFJywgY2xpY2tGdW46IHRoaXMuZ2V0RGV0YWlsc0luZm8gfQogICAgICAgICAgICBdCiAgICAgICAgIH0qLwogICAgICAgIF0KICAgICAgfSwKICAgICAgLy8g5piv5ZCm5pi+56S6562b6YCJ5p2h5Lu2CiAgICAgIGlzU2VhcmNoU2hvdzogZmFsc2UsCiAgICAgIC8v5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgcXhkakxpc3Q6IFtdLAogICAgICAgIHNiYm06ICIiLAogICAgICAgIHNiYnc6ICIiLAogICAgICAgIHF4bXM6ICIiLAogICAgICAgIGZseWo6ICIiLAogICAgICAgIGpzeXk6ICIiLAogICAgICAgIGR5c2JseDogJycsCiAgICAgICAgLy/orr7lpIfnsbvlnovlkI3np7AKICAgICAgICBzYmx4OiAiIgogICAgICB9LAogICAgICAvLyDkuJPkuJrkuIvmi4nmoYbmlbDmja4KICAgICAgc3BlY2lhbHR5T3B0aW9uczogW10sCiAgICAgIC8vIOmDqOS7tuS4i+aLieahhuaVsOaNrgogICAgICBwYXJ0c09wdGlvbnM6IFtdLAogICAgICAvLyDpmpDmgqPnrYnnuqfkuIvmi4nmoYbmlbDmja4KICAgICAgZGVmZWN0TGV2ZWxPcHRpb25zOiBbewogICAgICAgIHZhbHVlOiAn5LiA6IisJywKICAgICAgICBsYWJlbDogJ+S4gOiIrCcKICAgICAgfSwgewogICAgICAgIHZhbHVlOiAn5Lil6YeNJywKICAgICAgICBsYWJlbDogJ+S4pemHjScKICAgICAgfSwgewogICAgICAgIHZhbHVlOiAn5Y2x5oClJywKICAgICAgICBsYWJlbDogJ+WNseaApScKICAgICAgfV0sCiAgICAgIC8vIOWkmumAieahhumAieS4reeahOaVsOaNrmlk5YiX6KGoCiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5qCH5YeG57y66Zm35bqT6KGo5qC85pWw5o2uCiAgICAgIC8vIOWvueivneahhuagh+mimAogICAgICB0aXRsZTogJycsCiAgICAgIC8vIOaWsOWini/kv67mlLkv6K+m5oOF5a+56K+d5qGG5piv5ZCm5omT5byACiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDkuIrkvKDpmYTku7blr7nor53moYbmmK/lkKbmiZPlvIAKICAgICAgdXBsb2FkRGlhbG9nT3BlbjogZmFsc2UsCiAgICAgIC8vIOaWsOWini/kv67mlLnmj5DkuqTnmoTooajljZUKICAgICAgZm9ybTogewogICAgICAgIG9iaklkOiAnJywKICAgICAgICB6eTogdW5kZWZpbmVkLAogICAgICAgIHNiemw6IHVuZGVmaW5lZCwKICAgICAgICBzYmJqOiB1bmRlZmluZWQsCiAgICAgICAgcXhidzogdW5kZWZpbmVkLAogICAgICAgIHF4bXM6IHVuZGVmaW5lZCwKICAgICAgICBxeGRqOiB1bmRlZmluZWQsCiAgICAgICAganN5eTogdW5kZWZpbmVkLAogICAgICAgIHpyeXk6IHVuZGVmaW5lZCwKICAgICAgICBjcmVhdGVUaW1lOiB1bmRlZmluZWQsCiAgICAgICAgc2JseDogJycsCiAgICAgICAgc2JseG1jOiAnJywKICAgICAgICBmbHlqOiB1bmRlZmluZWQsCiAgICAgICAgc2JibTogJycKICAgICAgfSwKICAgICAgLy8g5paw5aKeL+S/ruaUueaXtuWvueivneahhue7hOS7tuWPr+e8lui+ke+8jOivpuaDheaXtuS4jeWPr+e8lui+kQogICAgICBmb3JtSXNFZGl0YWJsZTogZmFsc2UsCiAgICAgIC8vIOaKmOWPoOmdouadv+WxleW8gOeahOmdouadvwogICAgICBhY3RpdmVOYW1lczogWydiYXNlSW5mbyddLAogICAgICAvLyDmlofku7bkuIrkvKDmlbDmja4KICAgICAgdXBsb2FkRGF0YToge30sCiAgICAgIC8vIOaWh+S7tuS4iuS8oOivt+axguWktAogICAgICB1cEhlYWRlcjoge30sCiAgICAgIC8vIOihqOagvOWFseafpeivouWHuueahOadoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy/pg6jku7bmmK/lkKblj6/nvJbovpEKICAgICAgcGFydERpc2FibGVkOiBmYWxzZSwKICAgICAgLy/lsZXnpLrorr7lpIfliIbnsbvmoJEKICAgICAgc2hvd0RldmljZVRyZWU6IGZhbHNlLAogICAgICBpc0ZpbHRlcjogZmFsc2UsCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgc2JseG1jOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6K6+5aSH56eN57G75LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIHNiYm06IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLorr7lpIfpg6jku7YiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgc2JidzogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuiuvuWkh+mDqOS9jeS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBxeGRqOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6ZqQ5oKj562J57qn5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJzZWxlY3QiCiAgICAgICAgfV0sCiAgICAgICAgcXhtczogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIue8uumZt+aPj+i/sOS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBmbHlqOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5YiG57G75L6d5o2u5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGpzeXk6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLmioDmnK/ljp/lm6DkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0KICAgICAgfQogICAgfTsKICB9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7CiAgICAvL+iOt+WPlnRva2VuCiAgICB0aGlzLmhlYWRlci50b2tlbiA9ICgwLCBfYXV0aC5nZXRUb2tlbikoKTsKICAgIHRoaXMuaW5pdERvbWFpbigpOwogIH0sCiAgbWV0aG9kczogewogICAgLy/nrZvpgInmnaHku7bpvKDmoIfogZrnhKbkuovku7YKICAgIGlucHV0Rm9jdXNFdmVudDogZnVuY3Rpb24gaW5wdXRGb2N1c0V2ZW50KHZhbCkgewogICAgICBpZiAodmFsLnRhcmdldC5uYW1lID09PSAnZHlzYmx4JykgewogICAgICAgIHRoaXMuc2hvd0RldmljZVRyZWUgPSB0cnVlOwogICAgICAgIHRoaXMuaXNGaWx0ZXIgPSB0cnVlOwogICAgICB9CiAgICB9LAoKICAgIC8qKgogICAgICog5p+l6K+i5pWw5o2uCiAgICAgKi8KICAgIGdldERhdGE6IGZ1bmN0aW9uIGdldERhdGEocGFyYW1zKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CgogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSggLyojX19QVVJFX18qL3JlZ2VuZXJhdG9yUnVudGltZS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUoKSB7CiAgICAgICAgdmFyIHBhcmFtLCBfeWllbGQkZ2V0UGFnZURhdGFMaXMsIGRhdGEsIGNvZGU7CgogICAgICAgIHJldHVybiByZWdlbmVyYXRvclJ1bnRpbWUud3JhcChmdW5jdGlvbiBfY2FsbGVlJChfY29udGV4dCkgewogICAgICAgICAgd2hpbGUgKDEpIHsKICAgICAgICAgICAgc3dpdGNoIChfY29udGV4dC5wcmV2ID0gX2NvbnRleHQubmV4dCkgewogICAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICAgIF90aGlzLnF1ZXJ5UGFyYW1zID0gKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKCgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7fSwgX3RoaXMucXVlcnlQYXJhbXMpLCBwYXJhbXMpOwogICAgICAgICAgICAgICAgcGFyYW0gPSAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCBfdGhpcy5xdWVyeVBhcmFtcyksIHBhcmFtcyk7CiAgICAgICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gNDsKICAgICAgICAgICAgICAgIHJldHVybiAoMCwgX3F4YnprLmdldFBhZ2VEYXRhTGlzdCkocGFyYW0pOwoKICAgICAgICAgICAgICBjYXNlIDQ6CiAgICAgICAgICAgICAgICBfeWllbGQkZ2V0UGFnZURhdGFMaXMgPSBfY29udGV4dC5zZW50OwogICAgICAgICAgICAgICAgZGF0YSA9IF95aWVsZCRnZXRQYWdlRGF0YUxpcy5kYXRhOwogICAgICAgICAgICAgICAgY29kZSA9IF95aWVsZCRnZXRQYWdlRGF0YUxpcy5jb2RlOwoKICAgICAgICAgICAgICAgIGlmIChjb2RlID09PSAnMDAwMCcpIHsKICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coZGF0YSk7CiAgICAgICAgICAgICAgICAgIF90aGlzLnRhYmxlQW5kUGFnZUluZm8udGFibGVEYXRhID0gZGF0YS5yZWNvcmRzOwogICAgICAgICAgICAgICAgICBfdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnRvdGFsID0gZGF0YS50b3RhbDsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgY2FzZSA4OgogICAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQuc3RvcCgpOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIGdldERldGFpbHM6IGZ1bmN0aW9uIGdldERldGFpbHMocm93KSB7CiAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgIHRoaXMuZm9ybUlzRWRpdGFibGUgPSB0cnVlOwogICAgICB0aGlzLnBhcnREaXNhYmxlZCA9IGZhbHNlOyAvLyBpZiAocm93LnNibHggIT09ICcnICYmIHJvdy5zYmx4ICE9PSBudWxsKSB7CiAgICAgIC8vICAgdGhpcy5nZXRQYXJ0T3B0aW9ucyhyb3cuc2JseCkKICAgICAgLy8gfQoKICAgICAgdGhpcy5mb3JtID0gcm93OwogICAgfSwKICAgIGdldERldGFpbHNJbmZvOiBmdW5jdGlvbiBnZXREZXRhaWxzSW5mbyhyb3cpIHsKICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgdGhpcy5mb3JtSXNFZGl0YWJsZSA9IGZhbHNlOwogICAgICB0aGlzLnBhcnREaXNhYmxlZCA9IHRydWU7CiAgICAgIHRoaXMuZm9ybSA9IHJvdzsKICAgIH0sCgogICAgLyoqCiAgICAgKiDooajmoLzlpJrpgInmoYYKICAgICAqLwogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLm9iaklkOwogICAgICB9KTsKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOwogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7CiAgICB9LAogICAgLy8gLyoqCiAgICAvLyAgKiDmn6Xor6LmjInpkq4KICAgIC8vICAqLwogICAgLy8gaGFuZGxlUXVlcnkoKSB7CiAgICAvLyAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDEKICAgIC8vICAgdGhpcy5nZXREYXRhKCkKICAgIC8vIH0sCiAgICAvLwogICAgLy8gLyoqCiAgICAvLyAgKiDph43nva7mjInpkq4KICAgIC8vICAqLwogICAgLy8gcmVzZXRRdWVyeSgpIHsKICAgIC8vICAgdGhpcy5yZXNldEZvcm0oJ3F1ZXJ5Rm9ybScpCiAgICAvLyAgIHRoaXMuaGFuZGxlUXVlcnkoKQogICAgLy8gfSwKCiAgICAvKioKICAgICAqIOaWsOWinuaMiemSrgogICAgICovCiAgICBoYW5kbGVBZGQ6IGZ1bmN0aW9uIGhhbmRsZUFkZCgpIHsKICAgICAgLy8g6K6+572u5by55Ye65qGG6KGo5Y2V5Y+v57yW6L6RCiAgICAgIHRoaXMuZm9ybUlzRWRpdGFibGUgPSB0cnVlOwogICAgICB0aGlzLnRpdGxlID0gJ+aWsOWinuagh+WHhue8uumZtyc7CiAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICB9LAoKICAgIC8qKgogICAgICog5L+u5pS55oyJ6ZKuCiAgICAgKiBAcGFyYW0gcm93CiAgICAgKi8KICAgIGhhbmRsZUVkaXQ6IGZ1bmN0aW9uIGhhbmRsZUVkaXQocm93KSB7CiAgICAgIC8vIOiuvue9ruW8ueWHuuahhuihqOWNleWPr+e8lui+kQogICAgICB0aGlzLmZvcm1Jc0VkaXRhYmxlID0gdHJ1ZTsgLy8g5bCG5L+u5pS56KGM55qE5pWw5o2u6LWL5YC85YiwZm9ybeS4igoKICAgICAgZm9yICh2YXIgcm93S2V5IGluIHJvdykgewogICAgICAgIHRoaXMuZm9ybVtyb3dLZXldID0gcm93W3Jvd0tleV07CiAgICAgIH0KCiAgICAgIHRoaXMudGl0bGUgPSAn5L+u5pS55qCH5YeG57y66Zm3JzsKICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgIH0sCgogICAgLyoqCiAgICAgKiDlpITnkIbmibnph4/liKDpmaQKICAgICAqLwogICAgaGFuZGxlRGVsZXRlOiBmdW5jdGlvbiBoYW5kbGVEZWxldGUoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwoKICAgICAgaWYgKHRoaXMuaWRzLmxlbmd0aCAhPT0gMCkgewogICAgICAgIHRoaXMuJGNvbmZpcm0oJ+atpOaTjeS9nOWwhuawuOS5heWIoOmZpOivpeaVsOaNriwg5piv5ZCm57un57utPycsICfmj5DnpLonLCB7CiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgICAgKDAsIF9xeGJ6ay5yZW1vdmUpKF90aGlzMi5pZHMpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICBfdGhpczIuJG1lc3NhZ2UoewogICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICAgICAgICBtZXNzYWdlOiAn5Yig6Zmk5oiQ5YqfIScKICAgICAgICAgICAgfSk7CgogICAgICAgICAgICBfdGhpczIuZ2V0RGF0YSgpOwogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICB0eXBlOiAnaW5mbycsCiAgICAgICAgICBtZXNzYWdlOiAn6K+36YCJ5oup6Iez5bCR5LiA5p2h5pWw5o2uIScKICAgICAgICB9KTsKICAgICAgfQogICAgfSwKCiAgICAvKioKICAgICAqIOWvvOWHuuaMiemSrgogICAgICovCiAgICBoYW5kbGVFeHBvcnQ6IGZ1bmN0aW9uIGhhbmRsZUV4cG9ydCgpIHsKICAgICAgY29uc29sZS5sb2coJ+WvvOWHuicpOwogICAgfSwKCiAgICAvKioKICAgICAqIOWvvOWFpeaMiemSrgogICAgICovCiAgICBoYW5kbGVJbXBvcnQ6IGZ1bmN0aW9uIGhhbmRsZUltcG9ydCgpIHsKICAgICAgdGhpcy51cGxvYWREaWFsb2dPcGVuID0gdHJ1ZTsKICAgIH0sCgogICAgLyoqCiAgICAgKiDmn6XnnIvor6bmg4UKICAgICAqIEBwYXJhbSByb3cKICAgICAqLwogICAgaGFuZGxlRGV0YWlsczogZnVuY3Rpb24gaGFuZGxlRGV0YWlscyhyb3cpIHsKICAgICAgdGhpcy5mb3JtSXNFZGl0YWJsZSA9IGZhbHNlOyAvLyDlsIbooYzmlbDmja7otYvlgLzliLBmb3Jt5LiKCgogICAgICBmb3IgKHZhciByb3dLZXkgaW4gcm93KSB7CiAgICAgICAgdGhpcy5mb3JtW3Jvd0tleV0gPSByb3dbcm93S2V5XTsKICAgICAgfQoKICAgICAgdGhpcy50aXRsZSA9ICfmoIflh4bnvLrpmbfor6bmg4UnOwogICAgICB0aGlzLm9wZW4gPSB0cnVlOwogICAgfSwKCiAgICAvKioKICAgICAqIOWFs+mXreWvueivneahhgogICAgICovCiAgICBoYW5kbGVDbG9zZTogZnVuY3Rpb24gaGFuZGxlQ2xvc2UoKSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMudGl0bGUgPSAnJzsKICAgIH0sCgogICAgLyoqCiAgICAgKiDlhbPpl63kuIrkvKDpmYTku7blr7nor53moYYKICAgICAqLwogICAgdXBsb2FkQ2xvc2U6IGZ1bmN0aW9uIHVwbG9hZENsb3NlKCkgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHRoaXMudXBsb2FkRGlhbG9nT3BlbiA9IGZhbHNlOwogICAgfSwKCiAgICAvKioKICAgICAqIOaPkOS6pOihqOWNlQogICAgICovCiAgICBzdWJtaXRGb3JtOiBmdW5jdGlvbiBzdWJtaXRGb3JtKCkgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKCiAgICAgIHRoaXMuJHJlZnNbJ2Zvcm0nXS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgICgwLCBfcXhiemsuc2F2ZU9yVXBkYXRlKShfdGhpczMuZm9ybSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgX3RoaXMzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S/neWtmOaIkOWKn++8gScpOwoKICAgICAgICAgICAgX3RoaXMzLmdldERhdGEoKTsKCiAgICAgICAgICAgIF90aGlzMy5vcGVuID0gZmFsc2U7CiAgICAgICAgICB9KTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgX3RoaXMzLiRtZXNzYWdlLmVycm9yKCLmoKHpqozmnKrpgJrov4fvvIEiKTsKCiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCgogICAgLyoqCiAgICAgKiDooajljZXph43nva4KICAgICAqLwogICAgcmVzZXQ6IGZ1bmN0aW9uIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgaWQ6IHVuZGVmaW5lZCwKICAgICAgICBtYWludGFpblR5cGU6IHVuZGVmaW5lZCwKICAgICAgICBzcGVjaWFsdHk6IHVuZGVmaW5lZCwKICAgICAgICBkZXZpY2VUeXBlOiB1bmRlZmluZWQsCiAgICAgICAgcGFydHM6IHVuZGVmaW5lZCwKICAgICAgICBkZWZlY3RQYXJ0OiB1bmRlZmluZWQsCiAgICAgICAgZGVmZWN0RGVzY3JpcHRpb246IHVuZGVmaW5lZCwKICAgICAgICBkZWZlY3RHaXN0OiB1bmRlZmluZWQsCiAgICAgICAgZGVmZWN0TGV2ZWw6IHVuZGVmaW5lZCwKICAgICAgICByZWFzb246IHVuZGVmaW5lZCwKICAgICAgICBjcmVhdGVUaW1lOiB1bmRlZmluZWQKICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oJ2Zvcm0nKTsKICAgIH0sCgogICAgLyoqCiAgICAgKiDkuIrkvKDmlofku7bkuYvliY3nmoTlpITnkIYKICAgICAqLwogICAgYmVmb3JlVXBsb2FkOiBmdW5jdGlvbiBiZWZvcmVVcGxvYWQoZmlsZSkgewogICAgICB2YXIgZmlsZVNpemUgPSBmaWxlLnNpemUgPCAxMDI0ICogMTAyNCAqIDUwOyAvLzEwTQoKICAgICAgaWYgKCFmaWxlU2l6ZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S4iuS8oOaWh+S7tuWkp+Wwj+S4jeiDvei2hei/hyA1ME1CIScpOwogICAgICB9CgogICAgICB2YXIgc2l6ZSA9IGZpbGUuc2l6ZSAvIDEwMjQ7CiAgICB9LAoKICAgIC8qKgogICAgICog5LiK5Lyg5paH5Lu25Yiw5pyN5Yqh5ZmoCiAgICAgKi8KICAgIHN1Ym1pdFVwbG9hZDogZnVuY3Rpb24gc3VibWl0VXBsb2FkKCkgewogICAgICB0aGlzLiRyZWZzLnVwbG9hZC5zdWJtaXQoKTsKICAgIH0sCiAgICBpbml0RG9tYWluOiBmdW5jdGlvbiBpbml0RG9tYWluKCkgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKCiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKCAvKiNfX1BVUkVfXyovcmVnZW5lcmF0b3JSdW50aW1lLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTIoKSB7CiAgICAgICAgdmFyIF95aWVsZCRnZXREaWN0VHlwZURhdCwgbWFqb3JPcHRpb25zOwoKICAgICAgICByZXR1cm4gcmVnZW5lcmF0b3JSdW50aW1lLndyYXAoZnVuY3Rpb24gX2NhbGxlZTIkKF9jb250ZXh0MikgewogICAgICAgICAgd2hpbGUgKDEpIHsKICAgICAgICAgICAgc3dpdGNoIChfY29udGV4dDIucHJldiA9IF9jb250ZXh0Mi5uZXh0KSB7CiAgICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgICAgX2NvbnRleHQyLm5leHQgPSAyOwogICAgICAgICAgICAgICAgcmV0dXJuICgwLCBfZGF0YS5nZXREaWN0VHlwZURhdGEpKCdtYWpvcicpOwoKICAgICAgICAgICAgICBjYXNlIDI6CiAgICAgICAgICAgICAgICBfeWllbGQkZ2V0RGljdFR5cGVEYXQgPSBfY29udGV4dDIuc2VudDsKICAgICAgICAgICAgICAgIG1ham9yT3B0aW9ucyA9IF95aWVsZCRnZXREaWN0VHlwZURhdC5kYXRhOwogICAgICAgICAgICAgICAgX3RoaXM0LnNwZWNpYWx0eU9wdGlvbnMgPSBtYWpvck9wdGlvbnM7CgogICAgICAgICAgICAgICAgX3RoaXM0LmZpbHRlckluZm8uZmllbGRMaXN0LmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgICAgICAgaWYgKGl0ZW0udmFsdWUgPT09ICdzcGVjaWFsdHknKSB7CiAgICAgICAgICAgICAgICAgICAgaXRlbS5vcHRpb25zID0gbWFqb3JPcHRpb25zOwogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9KTsKCiAgICAgICAgICAgICAgY2FzZSA2OgogICAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQyLnN0b3AoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUyKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgLy/miZPlvIDlvLnlh7rlvIDlhbMKICAgIHNob3dEZXZpY2VUcmVlRGlhbG9nOiBmdW5jdGlvbiBzaG93RGV2aWNlVHJlZURpYWxvZygpIHsKICAgICAgLy/ku47mlrDlop7moYblhoXmiZPlvIAKICAgICAgdGhpcy5pc0ZpbHRlciA9IGZhbHNlOwogICAgICB0aGlzLnNob3dEZXZpY2VUcmVlID0gdHJ1ZTsKICAgIH0sCiAgICAvL+WPjeWbnuiuvuWkh+exu+Wei+mAieS4reeahOaVsOaNrgogICAgZ2V0RGV2aWNlVHlwZURhdGE6IGZ1bmN0aW9uIGdldERldmljZVR5cGVEYXRhKHJlcykgewogICAgICB2YXIgX3RoaXM1ID0gdGhpczsKCiAgICAgIGNvbnNvbGUubG9nKCJyZXPvvJoiLCByZXMpOwoKICAgICAgaWYgKHRoaXMuaXNGaWx0ZXIpIHsKICAgICAgICBjb25zb2xlLmxvZygi562b6YCJ5qGG6YCJ5oup6K6+5aSH57G75Z6LLCIsIHJlcyk7CiAgICAgICAgdmFyIHNibHhBcnIgPSBbXTsKICAgICAgICB0aGlzLmZpbHRlckluZm8uZGF0YS5keXNibHggPSAnJzsKICAgICAgICByZXMuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgaWYgKGl0ZW0uY2hlY2tlZCkgewogICAgICAgICAgICBzYmx4QXJyLnB1c2goaXRlbS5jb2RlKTsKICAgICAgICAgICAgX3RoaXM1LmZpbHRlckluZm8uZGF0YS5zYmx4ID0gc2JseEFyci5qb2luKCcsJyk7CiAgICAgICAgICAgIF90aGlzNS5maWx0ZXJJbmZvLmRhdGEuZHlzYmx4ICs9IGl0ZW0ubmFtZSArICcsJzsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgICB0aGlzLmZpbHRlckluZm8uZGF0YS5keXNibHggPSB0aGlzLmZpbHRlckluZm8uZGF0YS5keXNibHguc3Vic3RyaW5nKDAsIHRoaXMuZmlsdGVySW5mby5kYXRhLmR5c2JseC5sZW5ndGggLSAxKTsKICAgICAgICB0aGlzLnNob3dEZXZpY2VUcmVlID0gZmFsc2U7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgY29uc29sZS5sb2coIuaWsOWinuahhumAieaLqeiuvuWkh+exu+WeiywiLCByZXMpOwogICAgICAgIHZhciB0cmVlTm9kZXMgPSBbXTsKICAgICAgICByZXMuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgaWYgKGl0ZW0uY2hlY2tlZCkgewogICAgICAgICAgICB0cmVlTm9kZXMucHVzaChpdGVtKTsKICAgICAgICAgIH0KICAgICAgICB9KTsKCiAgICAgICAgaWYgKHRyZWVOb2Rlcy5sZW5ndGggPT09IDEpIHsKICAgICAgICAgIHRoaXMuZm9ybS5zYmx4bWMgPSB0cmVlTm9kZXNbMF0ubmFtZTsKICAgICAgICAgIHRoaXMuZm9ybS5zYmx4ID0gdHJlZU5vZGVzWzBdLmNvZGU7CiAgICAgICAgICB0aGlzLnNob3dEZXZpY2VUcmVlID0gZmFsc2U7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup5Y2V5p2h6K6+5aSH57G75Z6LJyk7CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgLy/lhbPpl63orr7lpIfnsbvlnovpgInkuK3moYYKICAgIGNsb3NlRGV2aWNlVHlwZURpYWxvZzogZnVuY3Rpb24gY2xvc2VEZXZpY2VUeXBlRGlhbG9nKCkgewogICAgICB0aGlzLnNob3dEZXZpY2VUcmVlID0gZmFsc2U7CiAgICB9LAogICAgZ2V0UGFydE9wdGlvbnM6IGZ1bmN0aW9uIGdldFBhcnRPcHRpb25zKHNibHgpIHsKICAgICAgdmFyIF90aGlzNiA9IHRoaXM7CgogICAgICAoMCwgX3NiYmouZ2V0RXF1aXBtZW50Q29tcG9uZW50c09wdGlvbnMpKHNibHgpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzNi5wYXJ0c09wdGlvbnMgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9CiAgfSAvLyB3YXRjaDogewogIC8vICAgJ2Zvcm0uc2JseG1jJyh2YWwpIHsKICAvLyAgICAgdGhpcy5wYXJ0RGlzYWJsZWQgPSB2YWwgPT09ICcnIHx8IHZhbCA9PT0gbnVsbAogIC8vICAgfQogIC8vIH0KCn07CmV4cG9ydHMuZGVmYXVsdCA9IF9kZWZhdWx0Ow=="}, {"version": 3, "sources": ["xqxbzk.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAoJA;;AACA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,QADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,UAAA,EAAA;AAAA,GAFA;AAGA,EAAA,OAHA,qBAGA;AACA,SAAA,OAAA;AACA,GALA;AAMA,EAAA,IANA,kBAMA;AACA,WAAA;AACA;AACA,MAAA,MAAA,EAAA,EAFA;AAGA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,IAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA,EAFA;AAGA,UAAA,IAAA,EAAA,EAHA;AAIA,UAAA,IAAA,EAAA,EAJA;AAKA,UAAA,IAAA,EAAA,EALA;AAMA,UAAA,MAAA,EAAA,EANA;AAMA;AACA,UAAA,IAAA,EAAA;AAPA,SADA;AAUA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SALA,EAMA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,UAHA;AAIA,UAAA,QAAA,EAAA,IAJA;AAKA,UAAA,OAAA,EAAA,CAAA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAAA,EAAA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAAA,EAAA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAAA;AALA,SANA,EAaA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAbA;AAVA,OAHA;AA8BA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AACA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA;AACA;;;;;;;;;;;;AATA;AAZA,OA9BA;AAkEA;AACA,MAAA,YAAA,EAAA,KAnEA;AAoEA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,QAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA,EAJA;AAKA,QAAA,IAAA,EAAA,EALA;AAMA,QAAA,IAAA,EAAA,EANA;AAOA,QAAA,IAAA,EAAA,EAPA;AAQA,QAAA,IAAA,EAAA,EARA;AASA,QAAA,MAAA,EAAA,EATA;AASA;AACA,QAAA,IAAA,EAAA;AAVA,OArEA;AAkFA;AACA,MAAA,gBAAA,EAAA,EAnFA;AAoFA;AACA,MAAA,YAAA,EAAA,EArFA;AAuFA;AACA,MAAA,kBAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAAA,EAAA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAAA,EAAA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAAA,CAxFA;AAyFA;AACA,MAAA,GAAA,EAAA,EA1FA;AA2FA;AACA,MAAA,MAAA,EAAA,IA5FA;AA6FA;AACA,MAAA,QAAA,EAAA,IA9FA;AA+FA;AAEA;AACA,MAAA,KAAA,EAAA,EAlGA;AAmGA;AACA,MAAA,IAAA,EAAA,KApGA;AAqGA;AACA,MAAA,gBAAA,EAAA,KAtGA;AAuGA;AACA,MAAA,IAAA,EAAA;AACA,QAAA,KAAA,EAAA,EADA;AAEA,QAAA,EAAA,EAAA,SAFA;AAGA,QAAA,IAAA,EAAA,SAHA;AAIA,QAAA,IAAA,EAAA,SAJA;AAKA,QAAA,IAAA,EAAA,SALA;AAMA,QAAA,IAAA,EAAA,SANA;AAOA,QAAA,IAAA,EAAA,SAPA;AAQA,QAAA,IAAA,EAAA,SARA;AASA,QAAA,IAAA,EAAA,SATA;AAUA,QAAA,UAAA,EAAA,SAVA;AAWA,QAAA,IAAA,EAAA,EAXA;AAYA,QAAA,MAAA,EAAA,EAZA;AAaA,QAAA,IAAA,EAAA,SAbA;AAcA,QAAA,IAAA,EAAA;AAdA,OAxGA;AAwHA;AACA,MAAA,cAAA,EAAA,KAzHA;AA0HA;AACA,MAAA,WAAA,EAAA,CAAA,UAAA,CA3HA;AA4HA;AACA,MAAA,UAAA,EAAA,EA7HA;AA8HA;AACA,MAAA,QAAA,EAAA,EA/HA;AAgIA;AACA,MAAA,KAAA,EAAA,CAjIA;AAkIA;AACA,MAAA,YAAA,EAAA,KAnIA;AAoIA;AACA,MAAA,cAAA,EAAA,KArIA;AAsIA,MAAA,QAAA,EAAA,KAtIA;AAuIA,MAAA,KAAA,EAAA;AACA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAVA;AAaA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAbA;AAgBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAhBA;AAmBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAnBA;AAvIA,KAAA;AA+JA,GAtKA;AAuKA,EAAA,OAvKA,qBAuKA;AACA;AACA,SAAA,MAAA,CAAA,KAAA,GAAA,qBAAA;AACA,SAAA,UAAA;AACA,GA3KA;AA4KA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,eAFA,2BAEA,GAFA,EAEA;AACA,UAAA,GAAA,CAAA,MAAA,CAAA,IAAA,KAAA,QAAA,EAAA;AACA,aAAA,cAAA,GAAA,IAAA;AACA,aAAA,QAAA,GAAA,IAAA;AACA;AACA,KAPA;;AASA;;;AAGA,IAAA,OAZA,mBAYA,MAZA,EAYA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,KAAA,CAAA,WAAA,+DAAA,KAAA,CAAA,WAAA,GAAA,MAAA;AACA,gBAAA,KAFA,+DAEA,KAAA,CAAA,WAFA,GAEA,MAFA;AAAA;AAAA,uBAGA,4BAAA,KAAA,CAHA;;AAAA;AAAA;AAGA,gBAAA,IAHA,yBAGA,IAHA;AAGA,gBAAA,IAHA,yBAGA,IAHA;;AAIA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,IAAA;AACA,kBAAA,KAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,KAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AARA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,KArBA;AAsBA,IAAA,UAtBA,sBAsBA,GAtBA,EAsBA;AACA,WAAA,IAAA,GAAA,IAAA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,WAAA,YAAA,GAAA,KAAA,CAHA,CAIA;AACA;AACA;;AACA,WAAA,IAAA,GAAA,GAAA;AACA,KA9BA;AA+BA,IAAA,cA/BA,0BA+BA,GA/BA,EA+BA;AACA,WAAA,IAAA,GAAA,IAAA;AACA,WAAA,cAAA,GAAA,KAAA;AACA,WAAA,YAAA,GAAA,IAAA;AACA,WAAA,IAAA,GAAA,GAAA;AACA,KApCA;;AAqCA;;;AAGA,IAAA,qBAxCA,iCAwCA,SAxCA,EAwCA;AACA,WAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,KAAA;AAAA,OAAA,CAAA;AACA,WAAA,MAAA,GAAA,SAAA,CAAA,MAAA,KAAA,CAAA;AACA,WAAA,QAAA,GAAA,CAAA,SAAA,CAAA,MAAA;AACA,KA5CA;AA8CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;AAGA,IAAA,SAjEA,uBAiEA;AACA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,WAAA,KAAA,GAAA,QAAA;AACA,WAAA,IAAA,GAAA,IAAA;AACA,KAtEA;;AAwEA;;;;AAIA,IAAA,UA5EA,sBA4EA,GA5EA,EA4EA;AACA;AACA,WAAA,cAAA,GAAA,IAAA,CAFA,CAGA;;AACA,WAAA,IAAA,MAAA,IAAA,GAAA,EAAA;AACA,aAAA,IAAA,CAAA,MAAA,IAAA,GAAA,CAAA,MAAA,CAAA;AACA;;AACA,WAAA,KAAA,GAAA,QAAA;AACA,WAAA,IAAA,GAAA,IAAA;AACA,KArFA;;AAuFA;;;AAGA,IAAA,YA1FA,0BA0FA;AAAA;;AACA,UAAA,KAAA,GAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,UAAA,iBAAA,EAAA,IADA;AAEA,UAAA,gBAAA,EAAA,IAFA;AAGA,UAAA,IAAA,EAAA;AAHA,SAAA,EAIA,IAJA,CAIA,YAAA;AACA,6BAAA,MAAA,CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,OAAA;AACA,WANA;AAOA,SAZA;AAaA,OAdA,MAcA;AACA,aAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA;AAEA,KAhHA;;AAkHA;;;AAGA,IAAA,YArHA,0BAqHA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA;AACA,KAvHA;;AAyHA;;;AAGA,IAAA,YA5HA,0BA4HA;AACA,WAAA,gBAAA,GAAA,IAAA;AACA,KA9HA;;AAgIA;;;;AAIA,IAAA,aApIA,yBAoIA,GApIA,EAoIA;AACA,WAAA,cAAA,GAAA,KAAA,CADA,CAEA;;AACA,WAAA,IAAA,MAAA,IAAA,GAAA,EAAA;AACA,aAAA,IAAA,CAAA,MAAA,IAAA,GAAA,CAAA,MAAA,CAAA;AACA;;AACA,WAAA,KAAA,GAAA,QAAA;AACA,WAAA,IAAA,GAAA,IAAA;AACA,KA5IA;;AA8IA;;;AAGA,IAAA,WAjJA,yBAiJA;AACA,WAAA,KAAA;AACA,WAAA,IAAA,GAAA,KAAA;AACA,WAAA,KAAA,GAAA,EAAA;AACA,KArJA;;AAuJA;;;AAGA,IAAA,WA1JA,yBA0JA;AACA,WAAA,KAAA;AACA,WAAA,gBAAA,GAAA,KAAA;AACA,KA7JA;;AA+JA;;;AAGA,IAAA,UAlKA,wBAkKA;AAAA;;AACA,WAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,mCAAA,MAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,OAAA;;AACA,YAAA,MAAA,CAAA,OAAA;;AACA,YAAA,MAAA,CAAA,IAAA,GAAA,KAAA;AACA,WAJA;AAKA,SANA,MAMA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;;AACA,iBAAA,KAAA;AACA;AACA,OAXA;AAYA,KA/KA;;AAiLA;;;AAGA,IAAA,KApLA,mBAoLA;AACA,WAAA,IAAA,GAAA;AACA,QAAA,EAAA,EAAA,SADA;AAEA,QAAA,YAAA,EAAA,SAFA;AAGA,QAAA,SAAA,EAAA,SAHA;AAIA,QAAA,UAAA,EAAA,SAJA;AAKA,QAAA,KAAA,EAAA,SALA;AAMA,QAAA,UAAA,EAAA,SANA;AAOA,QAAA,iBAAA,EAAA,SAPA;AAQA,QAAA,UAAA,EAAA,SARA;AASA,QAAA,WAAA,EAAA,SATA;AAUA,QAAA,MAAA,EAAA,SAVA;AAWA,QAAA,UAAA,EAAA;AAXA,OAAA;AAaA,WAAA,SAAA,CAAA,MAAA;AACA,KAnMA;;AAqMA;;;AAGA,IAAA,YAxMA,wBAwMA,IAxMA,EAwMA;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,IAAA,GAAA,OAAA,IAAA,GAAA,EAAA,CADA,CACA;;AACA,UAAA,CAAA,QAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,kBAAA;AACA;;AACA,UAAA,IAAA,GAAA,IAAA,CAAA,IAAA,GAAA,IAAA;AACA,KA9MA;;AAgNA;;;AAGA,IAAA,YAnNA,0BAmNA;AACA,WAAA,KAAA,CAAA,MAAA,CAAA,MAAA;AACA,KArNA;AAuNA,IAAA,UAvNA,wBAuNA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAEA,2BAAA,OAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,YAFA,yBAEA,IAFA;AAGA,gBAAA,MAAA,CAAA,gBAAA,GAAA,YAAA;;AAEA,gBAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,sBAAA,IAAA,CAAA,KAAA,KAAA,WAAA,EAAA;AACA,oBAAA,IAAA,CAAA,OAAA,GAAA,YAAA;AACA;AACA,iBAJA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,KAjOA;AAkOA;AACA,IAAA,oBAnOA,kCAmOA;AACA;AACA,WAAA,QAAA,GAAA,KAAA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,KAvOA;AAwOA;AACA,IAAA,iBAzOA,6BAyOA,GAzOA,EAyOA;AAAA;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,MAAA,EAAA,GAAA;;AACA,UAAA,KAAA,QAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,GAAA;AACA,YAAA,OAAA,GAAA,EAAA;AACA,aAAA,UAAA,CAAA,IAAA,CAAA,MAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,OAAA,EAAA;AACA,YAAA,OAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA;AACA,YAAA,MAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA,GAAA,OAAA,CAAA,IAAA,CAAA,GAAA,CAAA;AACA,YAAA,MAAA,CAAA,UAAA,CAAA,IAAA,CAAA,MAAA,IAAA,IAAA,CAAA,IAAA,GAAA,GAAA;AACA;AACA,SANA;AAOA,aAAA,UAAA,CAAA,IAAA,CAAA,MAAA,GAAA,KAAA,UAAA,CAAA,IAAA,CAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,KAAA,UAAA,CAAA,IAAA,CAAA,MAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA,aAAA,cAAA,GAAA,KAAA;AACA,OAbA,MAaA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,GAAA;AACA,YAAA,SAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,OAAA,EAAA;AACA,YAAA,SAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,SAJA;;AAKA,YAAA,SAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,eAAA,IAAA,CAAA,MAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA,eAAA,IAAA,CAAA,IAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA,eAAA,cAAA,GAAA,KAAA;AACA,SAJA,MAIA;AACA,eAAA,QAAA,CAAA,OAAA,CAAA,WAAA;AACA;AACA;AACA,KAxQA;AAyQA;AACA,IAAA,qBA1QA,mCA0QA;AACA,WAAA,cAAA,GAAA,KAAA;AACA,KA5QA;AA6QA,IAAA,cA7QA,0BA6QA,IA7QA,EA6QA;AAAA;;AACA,+CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,YAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA;AAjRA,GA5KA,CA+bA;AACA;AACA;AACA;AACA;;AAncA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n      <el-filter\n        :data=\"filterInfo.data\"\n        :field-list=\"filterInfo.fieldList\"\n        @onfocusEvent=\"inputFocusEvent\"\n      />\n    <div>\n<!--      <el-white class=\"button-group\">\n        <el-row :gutter=\"3\" class=\"pull-right\">\n          <el-col style=\"\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n          \">-->\n            <el-white class=\"button-group\">\n              <div class=\"button_btn\">\n                <el-button type=\"primary\" v-hasPermi=\"['bzqxbzkxz:button:add']\" icon=\"el-icon-plus\" @click=\"handleAdd\">新增</el-button>\n                <el-button type=\"danger\" v-hasPermi=\"['bzqxbzkxz:button:delete']\" icon=\"el-icon-delete\" @click=\"handleDelete\">删除</el-button>\n              </div>\n              <comp-table :table-and-page-info=\"tableAndPageInfo\" @update:multipleSelection=\"handleSelectionChange\" height=\"68vh\">\n              <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\" width=\"160\"\n                               :resizable=\"false\">\n                <template slot-scope=\"scope\">\n                  <el-button @click=\"getDetails(scope.row)\" v-hasPermi=\"['bzqxbzkxz:button:update']\" type=\"text\" size=\"small\" class='el-icon-edit' title=\"修改\"></el-button>\n                  <el-button @click=\"getDetailsInfo(scope.row)\" type=\"text\" size=\"small\" title=\"详情\" class=\"el-icon-view\"></el-button>\n                </template>\n              </el-table-column>\n              </comp-table>\n            </el-white>\n    </div>\n\n\n    <!-- 新增/修改/详情 对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" append-to-body width=\"50%\" @close=\"handleClose\" v-dialogDrag>\n      <el-form ref=\"form\"  :model=\"form\" label-width=\"80px\" :rules=\"rules\">\n        <el-tabs tab-position=\"left\" style=\"height: 100%;\">\n          <el-tab-pane label=\"基本信息\">\n                <el-row :gutter=\"10\">\n                  <el-col :span=\"12\">\n<!--                    <el-form-item v-show=\"false\">-->\n<!--                      <el-input v-model=\"form.sblx\"></el-input>-->\n<!--                      <el-input v-model=\"form.objId\"></el-input>-->\n<!--                    </el-form-item>-->\n                    <el-form-item label=\"设备种类:\" prop=\"sblxmc\">\n                      <el-input @focus=\"showDeviceTreeDialog\" clearable v-model=\"form.sblxmc\" class=\"form-item\"\n                                :disabled=\"!formIsEditable\" placeholder=\"请选择设备种类\"></el-input>\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"12\">\n                    <el-form-item label=\"设备部件:\" prop=\"sbbm\">\n                      <el-input :disabled=\"!formIsEditable\" class=\"form-item\" v-model=\"form.sbbm\" clearable placeholder=\"请输入设备部件\"></el-input>\n                    </el-form-item>\n                  </el-col>\n                </el-row>\n                <el-row :gutter=\"10\">\n                  <el-col :span=\"12\">\n                    <el-form-item label=\"设备部位:\" prop=\"sbbw\">\n                      <el-input :disabled=\"!formIsEditable\" class=\"form-item\" v-model=\"form.sbbw\" clearable placeholder=\"请输入设备部位\"></el-input>\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"12\">\n                    <el-form-item label=\"隐患等级:\" prop=\"qxdj\">\n                      <el-select :disabled=\"!formIsEditable\" class=\"form-item\" v-model=\"form.qxdj\" clearable\n                                 placeholder=\"请输入隐患等级\">\n                        <el-option\n                          v-for=\"item in defectLevelOptions\"\n                          :key=\"item.value\"\n                          :label=\"item.label\"\n                          :value=\"item.value\">\n                        </el-option>\n                      </el-select>\n                    </el-form-item>\n                  </el-col>\n                </el-row>\n                <el-row :gutter=\"10\">\n\n                  <el-col :span=\"24\">\n                    <el-form-item label=\"缺陷描述:\" prop=\"qxms\">\n                      <el-input :disabled=\"!formIsEditable\" class=\"form-item-row\" v-model=\"form.qxms\" clearable\n                                placeholder=\"请输入缺陷描述\"></el-input>\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"24\">\n                    <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                      <el-input :disabled=\"!formIsEditable\" class=\"form-item-row\" v-model=\"form.flyj\" clearable\n                                placeholder=\"请输入分类依据\"></el-input>\n                    </el-form-item>\n                  </el-col>\n                </el-row>\n                <el-row :gutter=\"10\">\n                  <el-col :span=\"24\">\n                    <el-form-item label=\"技术原因:\" prop=\"jsyy\">\n                      <el-input class=\"form-item-row\" :disabled=\"!formIsEditable\" placeholder=\"请输入技术原因\"\n                                v-model=\"form.jsyy\"></el-input>\n                    </el-form-item>\n                  </el-col>\n                </el-row>\n          </el-tab-pane>\n        </el-tabs>\n\n      </el-form>\n      <div v-show=\"formIsEditable\" slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"handleClose\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitForm\">确 认</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 导入上传文件对话框 -->\n    <el-dialog title=\"上传附件\" :before-close=\"uploadClose\" :visible.sync=\"uploadDialogOpen\" width=\"50%\" v-dialogDrag>\n      <el-form ref=\"form\" :model=\"form\" label-width=\"80px\">\n        <el-form-item label=\"上传附件:\" prop=\"attachmentid\">\n          <el-upload\n            class=\"upload-demo\"\n            accept=\".jpg,.png,.rar,.txt,.zip,.doc,.ppt,.xls,.pdf,.docx,.xlsx,.mp4,.avi,.rmvb\"\n            ref=\"upload\"\n            :headers=\"header\"\n            action=\"/isc-api/file/upload\"\n            :before-upload=\"beforeUpload\"\n            :data=\"uploadData\"\n            multiple\n            :auto-upload=\"false\">\n            <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选取文件</el-button>\n            <el-button style=\"margin-left: 10px;\" size=\"small\" type=\"success\" @click=\"submitUpload\">上传到服务器</el-button>\n          </el-upload>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"uploadClose\" size=\"small\">关闭</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog\n      :append-to-body=\"true\"\n      title=\"设备分类\"\n      :visible.sync=\"showDeviceTree\"\n      width=\"400px\"\n      v-dialogDrag\n      v-if=\"showDeviceTree\">\n      <device-tree\n        @getDeviceTypeData=\"getDeviceTypeData\"\n        @closeDeviceTypeDialog=\"closeDeviceTypeDialog\">\n      </device-tree>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport { getPageDataList, remove, saveOrUpdate } from '@/api/dagangOilfield/bzgl/qxbzk'\nimport { getDictTypeData } from '@/api/system/dict/data'\nimport DeviceTree from '@/views/dagangOilfield/bzgl/sbbzk/deviceTree'\nimport { getEquipmentComponentsOptions } from '@/api/dagangOilfield/bzgl/sblxwh/sbbj'\n\nexport default {\n  name: 'xqxbzk',\n  components: { DeviceTree },\n  created() {\n    this.getData()\n  },\n  data() {\n    return {\n      //上传图片时的请求头\n      header: {},\n      filterInfo: {\n        data: {\n          sbbm:\"\",\n          sbbw:\"\",\n          qxms:\"\",\n          flyj:\"\",\n          jsyy:\"\",\n          dysblx: '',  //设备类型名称\n          sblx:\"\"\n        },\n        fieldList: [\n          { label: '设备种类', type: 'input', value: 'dysblx' },\n          { label: '设备部件', type: 'input', value: 'sbbm' },\n          { label: '设备部位', type: 'input', value: 'sbbw' },\n          { label: '缺陷描述', type: 'input', value: 'qxms' },\n          { label: '分类依据', type: 'input', value: 'flyj' },\n          {\n            label: '隐患等级',\n            type: 'select',\n            value: 'qxdjList',\n            multiple: true,\n            options: [{ label: '一般', value: '一般' }, { label: '严重', value: '严重' }, { label: '危急', value: '危急' }]\n          },\n          { label: '技术原因', type: 'input', value: 'jsyy' },\n\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          // { prop: 'zymc', label: '专业', minWidth: '120' },\n          { prop: 'sblxmc', label: '设备种类', minWidth: '180' },\n          { prop: 'sbbm', label: '设备部件', minWidth: '120' },\n          { prop: 'sbbw', label: '设备部位', minWidth: '120' },\n          { prop: 'qxms', label: '缺陷描述', minWidth: '120' },\n          { prop: 'flyj', label: '分类依据', minWidth: '120' },\n          { prop: 'qxdj', label: '隐患等级', minWidth: '120' },\n          { prop: 'jsyy', label: '技术原因', minWidth: '120' },\n         /* {\n            prop: 'operation',\n            label: '操作',\n            minWidth: '130px',\n            style: { display: 'block' },\n            //操作列固定再右侧\n            fixed: 'right',\n            operation: [\n              { name: '修改', clickFun: this.getDetails },\n              { name: '详情', clickFun: this.getDetailsInfo }\n\n            ]\n          }*/\n        ]\n      },\n      // 是否显示筛选条件\n      isSearchShow: false,\n      //查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        qxdjList: [],\n        sbbm:\"\",\n        sbbw:\"\",\n        qxms:\"\",\n        flyj:\"\",\n        jsyy:\"\",\n        dysblx: '',  //设备类型名称\n        sblx:\"\"\n\n      },\n      // 专业下拉框数据\n      specialtyOptions: [],\n      // 部件下拉框数据\n      partsOptions: [],\n\n      // 隐患等级下拉框数据\n      defectLevelOptions: [{ value: '一般', label: '一般' }, { value: '严重', label: '严重' }, { value: '危急', label: '危急' }],\n      // 多选框选中的数据id列表\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 标准缺陷库表格数据\n\n      // 对话框标题\n      title: '',\n      // 新增/修改/详情对话框是否打开\n      open: false,\n      // 上传附件对话框是否打开\n      uploadDialogOpen: false,\n      // 新增/修改提交的表单\n      form: {\n        objId: '',\n        zy: undefined,\n        sbzl: undefined,\n        sbbj: undefined,\n        qxbw: undefined,\n        qxms: undefined,\n        qxdj: undefined,\n        jsyy: undefined,\n        zryy: undefined,\n        createTime: undefined,\n        sblx: '',\n        sblxmc: '',\n        flyj: undefined,\n        sbbm: ''\n      },\n      // 新增/修改时对话框组件可编辑，详情时不可编辑\n      formIsEditable: false,\n      // 折叠面板展开的面板\n      activeNames: ['baseInfo'],\n      // 文件上传数据\n      uploadData: {},\n      // 文件上传请求头\n      upHeader: {},\n      // 表格共查询出的条数\n      total: 0,\n      //部件是否可编辑\n      partDisabled: false,\n      //展示设备分类树\n      showDeviceTree: false,\n      isFilter: false,\n      rules: {\n        sblxmc: [\n          {required: true, message: \"设备种类不能为空\", trigger: \"blur\"},\n        ],\n        sbbm: [\n          {required: true, message: \"设备部件\", trigger: \"blur\"},\n        ],\n        sbbw: [\n          {required: true, message: \"设备部位不能为空\", trigger: \"blur\"},\n        ],\n        qxdj: [\n          {required: true, message: \"隐患等级不能为空\", trigger: \"select\"},\n        ],\n        qxms: [\n          {required: true, message: \"缺陷描述不能为空\", trigger: \"blur\"},\n        ],\n        flyj: [\n          {required: true, message: \"分类依据不能为空\", trigger: \"blur\"},\n        ],\n        jsyy: [\n          {required: true, message: \"技术原因不能为空\", trigger: \"blur\"},\n        ],\n      },\n    }\n  },\n  mounted() {\n    //获取token\n    this.header.token = getToken();\n    this.initDomain()\n  },\n  methods: {\n    //筛选条件鼠标聚焦事件\n    inputFocusEvent(val) {\n      if (val.target.name === 'dysblx') {\n        this.showDeviceTree = true\n        this.isFilter = true\n      }\n    },\n\n    /**\n     * 查询数据\n     */\n    async getData(params) {\n      this.queryParams={...this.queryParams,...params}\n      const param = {...this.queryParams, ...params}\n      const {data, code} = await getPageDataList(param);\n      if (code === '0000') {\n        console.log(data)\n        this.tableAndPageInfo.tableData = data.records\n        this.tableAndPageInfo.pager.total = data.total\n      }\n    },\n    getDetails(row) {\n      this.open = true\n      this.formIsEditable = true\n      this.partDisabled = false\n      // if (row.sblx !== '' && row.sblx !== null) {\n      //   this.getPartOptions(row.sblx)\n      // }\n      this.form = row\n    },\n    getDetailsInfo(row) {\n      this.open = true\n      this.formIsEditable = false\n      this.partDisabled = true\n      this.form = row\n    },\n    /**\n     * 表格多选框\n     */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.objId)\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n    },\n\n    // /**\n    //  * 查询按钮\n    //  */\n    // handleQuery() {\n    //   this.queryParams.pageNum = 1\n    //   this.getData()\n    // },\n    //\n    // /**\n    //  * 重置按钮\n    //  */\n    // resetQuery() {\n    //   this.resetForm('queryForm')\n    //   this.handleQuery()\n    // },\n\n    /**\n     * 新增按钮\n     */\n    handleAdd() {\n      // 设置弹出框表单可编辑\n      this.formIsEditable = true\n      this.title = '新增标准缺陷'\n      this.open = true\n    },\n\n    /**\n     * 修改按钮\n     * @param row\n     */\n    handleEdit(row) {\n      // 设置弹出框表单可编辑\n      this.formIsEditable = true\n      // 将修改行的数据赋值到form上\n      for (let rowKey in row) {\n        this.form[rowKey] = row[rowKey]\n      }\n      this.title = '修改标准缺陷'\n      this.open = true\n    },\n\n    /**\n     * 处理批量删除\n     */\n    handleDelete() {\n      if (this.ids.length !== 0) {\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove(this.ids).then(res => {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            });\n            this.getData();\n          })\n        })\n      } else {\n        this.$message({\n          type: 'info',\n          message: '请选择至少一条数据!'\n        })\n      }\n\n    },\n\n    /**\n     * 导出按钮\n     */\n    handleExport() {\n      console.log('导出')\n    },\n\n    /**\n     * 导入按钮\n     */\n    handleImport() {\n      this.uploadDialogOpen = true\n    },\n\n    /**\n     * 查看详情\n     * @param row\n     */\n    handleDetails(row) {\n      this.formIsEditable = false\n      // 将行数据赋值到form上\n      for (let rowKey in row) {\n        this.form[rowKey] = row[rowKey]\n      }\n      this.title = '标准缺陷详情'\n      this.open = true\n    },\n\n    /**\n     * 关闭对话框\n     */\n    handleClose() {\n      this.reset()\n      this.open = false\n      this.title = ''\n    },\n\n    /**\n     * 关闭上传附件对话框\n     */\n    uploadClose() {\n      this.reset()\n      this.uploadDialogOpen = false\n    },\n\n    /**\n     * 提交表单\n     */\n    submitForm() {\n      this.$refs['form'].validate((valid) => {\n        if (valid) {\n          saveOrUpdate(this.form).then((response) => {\n            this.$message.success('保存成功！')\n            this.getData()\n            this.open = false\n          })\n        }else{\n          this.$message.error(\"校验未通过！\");\n          return false;\n        }\n      })\n    },\n\n    /**\n     * 表单重置\n     */\n    reset() {\n      this.form = {\n        id: undefined,\n        maintainType: undefined,\n        specialty: undefined,\n        deviceType: undefined,\n        parts: undefined,\n        defectPart: undefined,\n        defectDescription: undefined,\n        defectGist: undefined,\n        defectLevel: undefined,\n        reason: undefined,\n        createTime: undefined\n      }\n      this.resetForm('form')\n    },\n\n    /**\n     * 上传文件之前的处理\n     */\n    beforeUpload(file) {\n      const fileSize = file.size < 1024 * 1024 * 50 //10M\n      if (!fileSize) {\n        this.$message.error('上传文件大小不能超过 50MB!')\n      }\n      let size = file.size / 1024\n    },\n\n    /**\n     * 上传文件到服务器\n     */\n    submitUpload() {\n      this.$refs.upload.submit()\n    },\n\n    async initDomain() {\n\n      let { data: majorOptions } = await getDictTypeData('major')\n      this.specialtyOptions = majorOptions\n\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.value === 'specialty') {\n          item.options = majorOptions\n        }\n      })\n    },\n    //打开弹出开关\n    showDeviceTreeDialog() {\n      //从新增框内打开\n      this.isFilter = false\n      this.showDeviceTree = true\n    },\n    //反回设备类型选中的数据\n    getDeviceTypeData(res) {\n      console.log(\"res：\",res)\n      if (this.isFilter) {\n        console.log(\"筛选框选择设备类型,\",res)\n        let sblxArr = []\n        this.filterInfo.data.dysblx = ''\n        res.forEach(item => {\n          if (item.checked) {\n            sblxArr.push(item.code)\n            this.filterInfo.data.sblx = sblxArr.join(',')\n            this.filterInfo.data.dysblx += item.name + ','\n          }\n        })\n        this.filterInfo.data.dysblx = this.filterInfo.data.dysblx.substring(0, this.filterInfo.data.dysblx.length - 1)\n        this.showDeviceTree = false\n      } else {\n        console.log(\"新增框选择设备类型,\",res)\n        let treeNodes = []\n        res.forEach(item => {\n          if (item.checked) {\n            treeNodes.push(item)\n          }\n        })\n        if (treeNodes.length === 1) {\n          this.form.sblxmc = treeNodes[0].name\n          this.form.sblx = treeNodes[0].code\n          this.showDeviceTree = false\n        } else {\n          this.$message.warning('请选择单条设备类型')\n        }\n      }\n    },\n    //关闭设备类型选中框\n    closeDeviceTypeDialog() {\n      this.showDeviceTree = false\n    },\n    getPartOptions(sblx) {\n      getEquipmentComponentsOptions(sblx).then(res => {\n        this.partsOptions = res.data\n      })\n    }\n  },\n  // watch: {\n  //   'form.sblxmc'(val) {\n  //     this.partDisabled = val === '' || val === null\n  //   }\n  // }\n}\n</script>\n\n<style scoped>\n.filter-border {\n  border-bottom: #f5f8fd solid 1px;\n}\n\n.form-item {\n  width: 80%;\n}\n\n.form-item-row {\n  width: 92%;\n}\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl"}]}