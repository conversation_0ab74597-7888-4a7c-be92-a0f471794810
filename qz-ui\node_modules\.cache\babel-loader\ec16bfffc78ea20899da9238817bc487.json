{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\activiti\\process.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\activiti\\process.js", "mtime": 1706897313850}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuaGlzdG9yeUxpc3QgPSBoaXN0b3J5TGlzdDsKCnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwoKdmFyIGJhc2VVcmwgPSAiL2FjdGl2aXRpLWFwaSI7Ci8qKgogKiDmn6Xor6LmiJHnmoTlvoXlip7liJfooagKICogQHBhcmFtIHF1ZXJ5IOivt+axguWPguaVsAogKiBAcmV0dXJucyB7UHJvbWlzZTxhbnk+fQogKi8KCmZ1bmN0aW9uIGhpc3RvcnlMaXN0KHF1ZXJ5KSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICIvcHJvY2Vzcy9saXN0SGlzdG9yeSIsIHF1ZXJ5LCAzKTsKfQ=="}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/activiti/process.js"], "names": ["baseUrl", "historyList", "query", "api", "requestPost"], "mappings": ";;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,eAAhB;AACA;;;;;;AAKO,SAASC,WAAT,CAAqBC,KAArB,EAA4B;AACjC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,sBAAxB,EAA+CE,KAA/C,EAAqD,CAArD,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\nconst baseUrl = \"/activiti-api\";\n/**\n * 查询我的待办列表\n * @param query 请求参数\n * @returns {Promise<any>}\n */\nexport function historyList(query) {\n  return api.requestPost(baseUrl+\"/process/listHistory\",query,3)\n}\n\n\n"]}]}