{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sdsbgl\\zxwh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sdsbgl\\zxwh.vue", "mtime": 1706897324964}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["zxwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyGA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "zxwh.vue", "sourceRoot": "src/views/dagangOilfield/dwzygl/sdsbgl", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      ref=\"filter1\"\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{labelWidth: 100, itemWidth: 200}\"\n      @handleReset=\"filterReset\"\n    />\n    <el-white class=\"button-group\">\n      <div class=\"button_btn\">\n          <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"addRow\">新增</el-button>\n      </div>\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        height=\"42vh\"\n      >\n        <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\" minWidth=\"80\" :resizable=\"false\">\n          <template slot-scope=\"scope\">\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              @click=\"getInfo(scope.row)\"\n              title=\"详情\"\n              class=\"el-icon-view\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              @click=\"updateRow(scope.row)\"\n              title=\"编辑\"\n              class=\"el-icon-edit\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              @click=\"handleDelete(scope.row)\"\n              title=\"删除\"\n              class=\"el-icon-delete\"\n            ></el-button>\n          </template>\n        </el-table-column>\n      </comp-table>\n    </el-white>\n\n    <!-- 详情弹框 -->\n    <el-dialog title=\"查看详情\" :visible.sync=\"isShowDetails\" v-if=\"isShowDetails\" width=\"56%\" v-dialogDrag append-to-body @close=\"closeView\">\n      <el-form label-width=\"120px\" ref=\"viewForm\" :model=\"viewForm\">\n        <!--主表信息-->\n        <div>\n          <el-row>\n            <el-col :span=\"12\">\n              <el-form-item label=\"线段名称：\" prop=\"xdmc\" style=\"width: 80%\">\n                <el-input v-model=\"viewForm.xdmc\" :disabled=\"true\"/>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n\n        <!--子表信息-->\n        <div>\n          <el-table :data=\"viewForm.towers\" height=\"460\" border stripe style=\"width: 100%\">\n            <el-table-column type=\"index\" min-width=\"50\" align=\"center\" label=\"序号\"/>\n            <!--子表列表-->\n            <el-table-column align=\"center\" prop=\"gtmc\" label=\"杆塔名称\" min-width=\"100\">\n              <template slot-scope=\"scope\">\n                <el-input v-model=\"scope.row.gtmc\" :disabled=\"true\"/>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"gtbh\" label=\"杆塔编号\" min-width=\"80\">\n              <template slot-scope=\"scope\">\n                <el-input v-model=\"scope.row.gtbh\" :disabled=\"true\"/>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"dydj\" label=\"电压等级\" min-width=\"60\">\n              <template slot-scope=\"scope\">\n                <el-input v-model=\"scope.row.dydj\" :disabled=\"true\"/>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"jd\" label=\"经度\" min-width=\"100\">\n              <template slot-scope=\"scope\">\n                <el-input v-model=\"scope.row.jd\" :disabled=\"true\"/>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"wd\" label=\"纬度\" min-width=\"100\">\n              <template slot-scope=\"scope\">\n                <el-input v-model=\"scope.row.wd\" :disabled=\"true\"/>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"closeView\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 维护弹框 -->\n    <el-dialog title=\"支线维护\" :visible.sync=\"isShowAdd\" v-if=\"isShowAdd\" width=\"80%\" v-dialogDrag append-to-body @close=\"closeAdd\">\n      <zxwh-edit :xd-data=\"form\" @closeEdit=\"closeAdd\" @refreshFun=\"getData\"></zxwh-edit>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {getPage,delById} from \"@/api/dagangOilfield/asset/zxwh\";\nimport zxwhEdit from \"@/views/dagangOilfield/dwzygl/sdsbgl/zxwh_edit\";\n\nexport default {\n  name: \"zxwh\",\n  components: {zxwhEdit},\n  props: {\n    xlData: {\n      type: Object\n    }\n  },\n  data() {\n    return {\n      form:{},\n      viewForm:{},//查看表单\n      queryParams:{},\n      filterInfo: {\n        data: {\n          xdmc: '',\n        },\n        fieldList: [\n          { label: '线段名称', type: 'input', value: 'xdmc' },\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageResize: '',\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: false,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: 'xlmc', label: '线路名称', minWidth: '160' },\n          { prop: 'xdmc', label: '线段名称', minWidth: '160' },\n          { prop: 'createTime', label: '创建时间', minWidth: '100' },\n        ]\n      },\n      isShowDetails:false,\n      isShowAdd:false,\n    }\n  },\n  mounted() {\n    this.getData();\n  },\n  methods: {\n    //关闭查看弹框\n    closeView(){\n      this.viewForm = {};\n      this.isShowDetails = false;\n    },\n    //关闭查看弹框\n    closeAdd(){\n      this.form = {};\n      this.isShowAdd = false;\n    },\n    //新增支线\n    addRow(){\n      this.form = {};\n      this.form.xlid = this.xlData.objId;\n      this.form.xlmc = this.xlData.lineName;\n      this.isShowAdd = true;\n    },\n    //获取列表\n    async getData(params) {\n      this.queryParams = {...this.queryParams, ...params,...{xlid:this.xlData.objId}};\n      await getPage(this.queryParams).then(res=>{\n        this.tableAndPageInfo.tableData = res.data.records;\n        this.tableAndPageInfo.pager.total = res.data.total\n      })\n    },\n    /**\n     * 重置查询按钮\n     */\n    filterReset() {\n\n    },\n    /**\n     * 删除\n     */\n    handleDelete(row) {\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        delById([row.objId]).then(({ code }) => {\n          if (code === '0000') {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            })\n            this.getData()\n          } else {\n            this.$message({\n              type: 'error',\n              message: '删除失败!'\n            })\n          }\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        })\n      })\n    },\n    /**\n     * 详情\n     */\n    getInfo(row) {\n      this.viewForm = {...row};\n      this.isShowDetails = true;\n    },\n    /**\n     * 修改\n     */\n    updateRow(row) {\n      this.form = {...row};\n      this.isShowAdd = true;\n    },\n  },\n}\n</script>\n\n<style scoped>\n.xsdwCls{\n  font-size: 16px;\n}\n/* 设置滚动条的样式 */\n::-webkit-scrollbar {\n  width: 8px;\n}\n\n/* 滚动槽 */\n::-webkit-scrollbar-track {\n//-webkit-box-shadow:inset006pxrgba(0,0,0,0.3);\n  border-radius: 10px;\n}\n\n/* 滚动条滑块 */\n::-webkit-scrollbar-thumb {\n  border-radius: 10px;\n  background: rgba(0, 0, 0, 0.1);\n//-webkit-box-shadow:gba(0,0,0,0.5);\n}\n\n::-webkit-scrollbar-thumb:window-inactive {\n  background: rgba(0, 0, 0, 0.1);\n}\n</style>\n"]}]}