{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\userSelect\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\userSelect\\index.vue", "mtime": 1706897321243}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsKA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;;;gBAGA;AACA,EAAA,IAAA,EAAA,YADA;AAEA,EAAA,OAFA,qBAEA;AACA,SAAA,gBAAA;;AACA,QAAA,KAAA,SAAA,EAAA;AACA,WAAA,UAAA,GAAA,KAAA,KAAA,CAAA,MAAA,CAAA,WAAA;AACA;;AACA,SAAA,QAAA;AACA,GARA;AASA,EAAA,OATA,qBASA;AACA,SAAA,gBAAA;AACA,GAXA;AAYA,EAAA,MAAA,EAAA,CAAA,gBAAA,CAZA;AAaA,EAAA,UAAA,EAAA,EAbA;AAcA,EAAA,KAAA,EAAA;AACA,IAAA,WAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAAA,EAAA;AAFA,KADA;AAKA;AACA,IAAA,QAAA,EAAA;AACA,MAAA,IAAA,EAAA,OADA;AAEA,MAAA,OAAA,EAAA;AAFA,KANA;AAUA;AACA,IAAA,SAAA,EAAA;AACA,MAAA,IAAA,EAAA,OADA;AAEA,MAAA,OAAA,EAAA;AAFA,KAXA;AAeA;AACA,IAAA,QAAA,EAAA;AACA,MAAA,IAAA,EAAA,OADA;AAEA,MAAA,OAAA,EAAA;AAFA,KAhBA;AAoBA,IAAA,KAAA,EAAA;AACA,MAAA,IAAA,EAAA,CAAA,MAAA,EAAA,KAAA,CADA;AAEA,MAAA,OAFA,sBAEA;AACA,eAAA,EAAA;AACA;AAJA;AApBA,GAdA;AAyCA,EAAA,IAzCA,kBAyCA;AACA,QAAA,SAAA,GAAA,EAAA;;AACA,QAAA,KAAA,KAAA,YAAA,KAAA,EAAA;AACA,MAAA,SAAA,GAAA,GAAA,MAAA,CAAA,KAAA,KAAA,CAAA;AACA,KAFA,MAEA,IAAA,KAAA,KAAA,IAAA,KAAA,KAAA,CAAA,MAAA,EAAA;AACA,MAAA,SAAA,CAAA,IAAA,CAAA,KAAA,KAAA;AACA;;AACA,WAAA;AACA,MAAA,SAAA,EAAA,CADA;AAEA,MAAA,OAAA,EAAA,KAFA;AAGA,MAAA,SAAA,EAAA,KAHA;AAIA,MAAA,UAAA,EAAA,KAAA,KAJA;AAKA,MAAA,SAAA,EAAA,SALA;AAMA,MAAA,WAAA,EAAA,MANA;AAOA,MAAA,UAAA,EAAA,CAPA;AAQA,MAAA,aAAA,EAAA,KARA;AASA,MAAA,UAAA,EAAA;AACA,QAAA,kBAAA,EAAA;AADA,OATA;AAYA,MAAA,SAAA,EAAA,CAZA;AAaA,MAAA,QAAA,EAAA,CAbA;AAcA,MAAA,SAAA,EAAA,CAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CAdA;AAeA,MAAA,MAAA,EAAA,CAfA;AAgBA,MAAA,SAAA,EAAA,EAhBA;AAiBA,MAAA,cAAA,EAAA,IAjBA;AAkBA,MAAA,UAAA,EAAA;AACA,QAAA,KAAA,EAAA;AADA,OAlBA;AAqBA,MAAA,YAAA,EAAA;AACA,QAAA,QAAA,EAAA,UADA;AAEA,QAAA,KAAA,EAAA;AAFA,OArBA;AAyBA,MAAA,UAAA,EAAA,EAzBA;AA0BA,MAAA,QAAA,EAAA;AA1BA,KAAA;AA4BA,GA5EA;AA6EA,EAAA,QAAA,EAAA;AACA,IAAA,aADA,2BACA;AACA,UAAA,CAAA,KAAA,UAAA,EAAA;AACA,eAAA,EAAA;AACA;;AACA,UAAA,KAAA,UAAA,YAAA,KAAA,EAAA;AACA,eAAA,KAAA,UAAA;AACA;;AACA,aAAA,CAAA,KAAA,UAAA,CAAA;AACA,KATA;AAUA,IAAA,WAVA,yBAUA;AACA,UAAA,CAAA,KAAA,MAAA,EAAA;AACA,eAAA,SAAA;AACA;;AACA,UAAA,KAAA,UAAA,YAAA,KAAA,EAAA;AACA,eAAA,GAAA;AACA;;AACA,aAAA,KAAA,aAAA,CAAA,CAAA,EAAA,QAAA;AACA,KAlBA;AAmBA,IAAA,MAnBA,oBAmBA;AACA,UAAA,CAAA,KAAA,UAAA,EAAA;AACA,eAAA,KAAA;AACA;;AACA,UAAA,KAAA,UAAA,YAAA,KAAA,EAAA;AACA,eAAA,KAAA,UAAA,CAAA,MAAA,GAAA,CAAA;AACA;;AACA,aAAA,KAAA,UAAA,CAAA,MAAA;AACA;AA3BA,GA7EA;AA0GA,EAAA,OAAA,EAAA;AACA,IAAA,QADA,sBACA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,iCADA;;AAAA;AAAA;AACA,gBAAA,IADA,yBACA,IADA;AACA,gBAAA,IADA,yBACA,IADA;AAEA,gBAAA,KAAA,CAAA,QAAA,GAAA,IAAA;;AAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,KAJA;AAKA,IAAA,gBALA,4BAKA,GALA,EAKA;AACA,WAAA,QAAA,GAAA,GAAA;AACA,WAAA,OAAA;AACA,KARA;AASA,IAAA,cATA,0BASA,GATA,EASA,MATA,EASA;AACA,UAAA,MAAA,CAAA,QAAA,KAAA,SAAA,EAAA;AACA;AACA;;AACA,UAAA,KAAA,GAAA,KAAA,SAAA,CAAA,SAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,MAAA,KAAA,GAAA,CAAA,MAAA;AAAA,OAAA,MAAA,CAAA,CAAA;AACA,WAAA,qBAAA,CAAA,CAAA,KAAA,EAAA,GAAA;AACA,KAfA;AAgBA,IAAA,eAhBA,2BAgBA,GAhBA,EAgBA;AACA,UAAA,CAAA,GAAA,IAAA,CAAA,GAAA,CAAA,MAAA,EAAA;AACA;AACA;;AACA,UAAA,CAAA,KAAA,QAAA,KAAA,CAAA,KAAA,SAAA,IAAA,KAAA,SAAA,CAAA,MAAA,KAAA,CAAA,IAAA,KAAA,SAAA,CAAA,CAAA,EAAA,MAAA,KAAA,GAAA,CAAA,MAAA,CAAA,EAAA;AAAA;AACA,aAAA,SAAA,GAAA,CAAA,GAAA,CAAA;AACA;AACA;;AACA,UAAA,KAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,MAAA,KAAA,GAAA,CAAA,MAAA;AAAA,OAAA,CAAA;;AACA,UAAA,KAAA,EAAA;AACA;AACA;;AACA,WAAA,SAAA,CAAA,IAAA,CAAA,GAAA;AACA,KA7BA;AA8BA,IAAA,SA9BA,qBA8BA,GA9BA,EA8BA;AACA,aAAA,KAAA,SAAA,CAAA,MAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,MAAA,KAAA,GAAA,CAAA,MAAA;AAAA,OAAA,EAAA,MAAA,GAAA,CAAA;AACA,KAhCA;;AAiCA;;;;AAIA,IAAA,UArCA,wBAqCA;AACA,UAAA,KAAA,QAAA,EAAA;AACA;AACA;;AACA,UAAA,KAAA,SAAA,KAAA,CAAA,EAAA;AACA,aAAA,OAAA;AACA;;AACA,WAAA,aAAA,GAAA,IAAA;AACA,KA7CA;;AA8CA;;;;AAIA,IAAA,aAlDA,2BAkDA;AACA,UAAA,CAAA,KAAA,QAAA,EAAA;AACA,aAAA,UAAA,GAAA,KAAA,SAAA,CAAA,CAAA,CAAA;AACA,OAFA,MAEA;AACA,aAAA,UAAA,GAAA,GAAA,MAAA,CAAA,KAAA,SAAA,CAAA;AACA;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,KAAA,UAAA;AACA,WAAA,KAAA,CAAA,OAAA,EAAA,KAAA,UAAA;AACA,WAAA,KAAA,CAAA,QAAA,EAAA,KAAA,UAAA,EARA,CASA;;AACA,WAAA,aAAA,GAAA,KAAA;AACA,KA7DA;;AA8DA;;;;AAIA,IAAA,UAlEA,sBAkEA,IAlEA,EAkEA;AACA,UAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,MAAA,EAAA;AACA;AACA;;AACA,WAAA,qBAAA,CAAA,KAAA,EAAA,IAAA;AACA,WAAA,aAAA;AACA,KAxEA;;AAyEA;;;;AAIA,IAAA,WA7EA,yBA6EA;AACA,WAAA,SAAA,GAAA,EAAA;AACA,WAAA,aAAA;AACA,WAAA,KAAA,CAAA,OAAA;AACA,KAjFA;;AAkFA;;;;AAIA,IAAA,gBAtFA,8BAsFA;AAAA;;AACA,UAAA,CAAA,KAAA,QAAA,EAAA;AACA;AACA;;AACA,UAAA,KAAA,aAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,WAAA,GAAA,MAAA;AACA;AACA;;AAEA,WAAA,SAAA,CAAA,YAAA;AACA,YAAA,CAAA,MAAA,CAAA,KAAA,CAAA,SAAA,EAAA;AACA,YAAA,IAAA,GAAA,MAAA,CAAA,KAAA,CAAA,IAAA;AACA,YAAA,SAAA,GAAA,MAAA,EAAA;AACA,QAAA,MAAA,CAAA,WAAA,GAAA,MAAA,CAAA,aAAA,CAAA,MAAA,KAAA,CAAA,GACA,SAAA,GAAA,IADA,GAEA,IAAA,CAAA,GAAA,CACA,IAAA,GAAA,IAAA,CAAA,YAAA,IAAA,IAAA,CAAA,YAAA,GAAA,SAAA,GAAA,CAAA,GAAA,CAAA,CAAA,GAAA,CADA,EAEA,SAFA,IAGA,IALA;AAMA,OAVA;AAYA,MAAA,UAAA,CAAA,YAAA;AACA,YAAA,CAAA,MAAA,CAAA,KAAA,CAAA,SAAA,EAAA;AACA,YAAA,IAAA,GAAA,MAAA,CAAA,KAAA,CAAA,IAAA;AACA,YAAA,SAAA,GAAA,MAAA,EAAA;AACA,QAAA,MAAA,CAAA,WAAA,GAAA,MAAA,CAAA,aAAA,CAAA,MAAA,KAAA,CAAA,GACA,SAAA,GAAA,IADA,GAEA,IAAA,CAAA,GAAA,CACA,IAAA,GAAA,IAAA,CAAA,YAAA,IAAA,IAAA,CAAA,YAAA,GAAA,SAAA,GAAA,CAAA,GAAA,CAAA,CAAA,GAAA,CADA,EAEA,SAFA,IAGA,IALA;AAMA,OAVA,EAUA,GAVA,CAAA;AAYA,KAvHA;AAwHA,IAAA,qBAxHA,iCAwHA,SAxHA,EAwHA,GAxHA,EAwHA;AACA,UAAA,SAAA,EAAA;AACA,aAAA,eAAA,CAAA,GAAA;AACA;AACA;;AACA,UAAA,KAAA,GAAA,KAAA,SAAA,CAAA,SAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,MAAA,KAAA,GAAA,CAAA,MAAA;AAAA,OAAA,CAAA;;AACA,UAAA,KAAA,IAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,MAAA,CAAA,KAAA,EAAA,CAAA;AACA;AACA,KAjIA;AAkIA,IAAA,SAlIA,qBAkIA,IAlIA,EAkIA;AACA,UAAA,IAAA,CAAA,EAAA,KAAA,GAAA,EAAA;AACA,aAAA,cAAA,GAAA,EAAA;AACA;;AACA,WAAA,cAAA,GAAA,IAAA,CAAA,EAAA;AACA,WAAA,OAAA;AACA,KAxIA;AAyIA,IAAA,gBAzIA,4BAyIA,GAzIA,EAyIA;AACA,WAAA,MAAA,GAAA,GAAA;AACA,WAAA,OAAA;AACA,KA5IA;AA6IA,IAAA,OA7IA,qBA6IA;AAAA;;AACA,UAAA,KAAA,GAAA;AACA,QAAA,OAAA,EAAA,KAAA,MADA;AAEA,QAAA,QAAA,EAAA,KAAA;AAFA,OAAA;AAKA,MAAA,MAAA,CAAA,MAAA,CAAA,KAAA,EAAA,KAAA,UAAA;AACA,WAAA,OAAA,GAAA,IAAA;AAEA,0CAAA,KAAA,EAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,KAAA;;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,SAAA,GAAA,CAAA;;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA;;AACA;AACA;;AACA,QAAA,MAAA,CAAA,SAAA;AACA,QAAA,MAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,MAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,OAVA;AAWA;AAjKA,GA1GA;AA6QA,EAAA,KAAA,EAAA;AACA,IAAA,SADA,uBACA;AACA,WAAA,UAAA,GAAA,KAAA,KAAA,CAAA,MAAA,CAAA,WAAA;AACA,KAHA;AAIA,IAAA,aAJA,2BAIA;AACA,WAAA,gBAAA;AACA,KANA;AAOA,IAAA,KAPA,iBAOA,GAPA,EAOA;AACA,UAAA,SAAA,GAAA,EAAA;;AACA,UAAA,GAAA,YAAA,KAAA,EAAA;AACA,QAAA,SAAA,GAAA,GAAA,MAAA,CAAA,GAAA,CAAA;AACA,OAFA,MAEA,IAAA,GAAA,IAAA,GAAA,CAAA,MAAA,EAAA;AACA,QAAA,SAAA,CAAA,IAAA,CAAA,GAAA;AACA;;AACA,WAAA,UAAA,GAAA,GAAA;AACA,WAAA,SAAA,GAAA,SAAA;AACA;AAhBA,GA7QA;AA+RA,EAAA,OAAA,EAAA;AACA,IAAA,WADA,uBACA,KADA,EACA;AACA,UAAA,CAAA,KAAA,IAAA,KAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,eAAA,GAAA;AACA;;AACA,aAAA,KAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,eAAA,IAAA,CAAA,IAAA;AACA,OAFA,EAEA,MAFA,CAEA,UAAA,CAAA,EAAA,CAAA,EAAA;AACA,eAAA,CAAA,GAAA,GAAA,GAAA,CAAA;AACA,OAJA,CAAA;AAKA;AAVA,GA/RA;AA2SA,EAAA,aA3SA,2BA2SA,CAEA;AA7SA,C", "sourcesContent": ["<template>\n  <div ref=\"select\" class=\"pc-user-info-select \" @mouseenter=\"()=>showClear=true\" @mouseleave=\"()=>showClear=false\">\n\n    <div ref=\"tags\" class=\"el-select__tags\" v-if=\"multiple\" @click=\"showDialog\"\n         :style=\"{ 'max-width': inputWidth - 30 + 'px', width: '100%' }\">\n            <span v-if=\"hasVal\">\n                <el-tag class=\"inner-tag\" v-for=\"tag in currentValArr\" :key=\"tag.userId\" size=\"mini\" type=\"info\"\n                        @close=\"removeItem(tag)\" :closable=\"!disabled\"> {{tag.nickName}}\n                </el-tag>\n            </span>\n    </div>\n\n    <div class=\"el-input el-input--mini el-input--suffix\">\n      <input type=\"text\" readonly=\"readonly\" ref=\"reference\" @click=\"showDialog\"\n             autocomplete=\"off\" :placeholder=\"placeholder\"\n             :class=\"{'disabled':disabled}\" class=\"el-input__inner\" :value=\"currentText\" :disabled=\"disabled\"\n             :style=\"{'height':inputHeight}\"/>\n      <span class=\"el-input__suffix\"  @click.stop=\"clearHandle\"  v-show=\"showClear && hasVal\">\n                <span class=\"el-input__suffix-inner\" v-if=\"clearable && !disabled\">\n                 <i class=\"el-input__icon el-icon-circle-close el-input__clear\"/>\n                </span>\n            </span>\n    </div>\n\n    <el-dialog title=\"人员选择\" :close-on-press-escape=\"false\" append-to-body :close-on-click-modal=\"false\"\n               class=\"pc-user-info-select-dialog\" v-dialogDrag :visible.sync=\"dialogVisible\">\n      <template #title>\n        <svg-icon icon-class=\"user-add\"/>\n        <span style=\"font-size: 12px;font-weight: 900;color: #fff\">{{ dialogMate.title }}</span>\n      </template>\n\n      <gridvo-collapse-search-bar>\n        <el-form ref=\"searchForm\" v-model=\"searchForm\" :inline=\"true\">\n          <el-form-item prop=\"name\">\n            <el-input v-model=\"searchForm.nickName\" @keyup.enter.native=\"getData\"\n                      maxlength=\"16\" placeholder=\"请输入姓名\" clearable/>\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"getData\">查询</el-button>\n          </el-form-item>\n        </el-form>\n      </gridvo-collapse-search-bar>\n\n      <el-row v-if=\"multiple\" style=\"margin-top: -1px\">\n        <el-col>\n          <div class=\"select-panel\">\n            <el-tag\n              style=\"margin: 2px\"\n              v-for=\"tag in selectVal\"\n              :key=\"tag.userId\" size=\"mini\"\n              closable\n              @close=\"selectionChangeHandle(false,tag)\"\n              type=\"info\">\n              {{tag.nickName}}\n            </el-tag>\n          </div>\n        </el-col>\n      </el-row>\n\n      <el-row>\n        <el-col :span=\"6\">\n          <el-container style=\"max-height: 370px;border-right: 1px dashed #dedede; margin-right: 10px\">\n            <el-main style=\"padding: 5px 5px 5px 0\">\n              <el-scrollbar class=\"custom-scrollbar\" style=\"min-height: 360px;\">\n                <el-tree\n                  :data=\"treeData\"\n                  node-key=\"tree1\"\n                  ref=\"tree\"\n                  check-strictly\n                  :highlight-current='true'\n                  :check-on-click-node=\"false\"\n                  :accordion=\"false\"\n                  :default-checked-keys=\"[checkedkey]\"\n                  :default-expanded-keys=\"checkedkey\"\n                  :props=\"defaultProps\"\n                  :default-expand-all=\"false\"\n                  @node-click=\"nodeClick\"\n                ></el-tree>\n              </el-scrollbar>\n            </el-main>\n          </el-container>\n        </el-col>\n        <el-col :span=\"18\">\n          <div style=\"\">\n            <el-table\n              v-loading=\"loading\"\n              :data=\"tableData\"\n              row-key=\"userId\"\n              stripe\n              border\n              :highlight-current-row=\"true\"\n              @row-click=\"rowClickHandle\"\n              @select=\"selectionChangeHandle\"\n              style=\"width: 100%;\">\n              <el-table-column\n                align=\"center\"\n                property=\"nickName\"\n                label=\"姓名\" width=\"170\">\n              </el-table-column>\n              <el-table-column\n                align=\"center\"\n                property=\"sex\"\n                label=\"性别\" width=\"170\">\n                <template slot-scope=\"scope\">\n                  {{ scope.row.sex===1?'男':'女'}}\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"center\"\n                property=\"email\"\n                label=\"邮箱\" width=\"170\">\n              </el-table-column>\n              <!--<el-table-column\n                align=\"center\"\n                property=\"organization\"\n                label=\"所属组织\" width=\"155\">\n                <template slot-scope=\"scope\">\n                  {{ scope.row.organization.name || '-'}}\n                </template>\n              </el-table-column>-->\n              <!--<el-table-column\n                align=\"center\"\n                property=\"organization\"\n                label=\"用户角色\">\n                <template slot-scope=\"scope\">\n                  {{ scope.row.roles | rolesFilter}}\n                </template>\n              </el-table-column>-->\n\n              <el-table-column\n                label=\"选择\"\n                property=\"operate\"\n                align=\"center\"\n                width=\"55\">\n                <template slot-scope=\"scope\">\n                  <el-checkbox :value=\"isChecked(scope.row)\"\n                               @change=\"(val)=>selectionChangeHandle(val,scope.row)\"/>\n                </template>\n              </el-table-column>\n            </el-table>\n\n            <el-pagination\n              @current-change=\"paginationChange\"\n              @size-change=\"handleSizeChange\"\n              :page-size=\"pageSize\"\n              :page-sizes=\"pageSizes\"\n              layout=\"total,  prev, pager, next\"\n              :total=\"pageTotal\"\n              style=\"text-align: center;margin-top: 5px\"\n            >\n            </el-pagination>\n          </div>\n        </el-col>\n      </el-row>\n\n      <template slot=\"footer\">\n        <div style=\"text-align: center\">\n          <el-button @click=\"selectConfirm\" type=\"primary\">确定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n\n  import {deptTreeSelect,groupTreeSelect,UsersByDeptOrGroup} from \"@/api/component/userSelect\";\n  import emitter from 'element-ui/src/mixins/emitter';\n\n  /**\n   * 用户选择器\n   */\n  export default {\n    name: 'UserSelect',\n    mounted() {\n      this.resetInputHeight();\n      if (this.clearable) {\n        this.inputWidth = this.$refs.select.clientWidth;\n      }\n      this.initTree()\n    },\n    updated() {\n      this.resetInputHeight();\n    },\n    mixins: [emitter],\n    components: {},\n    props: {\n      placeholder: {\n        type: String,\n        default: '请选择人员'\n      },\n      //是否多选\n      multiple: {\n        type: Boolean,\n        default: false,\n      },\n      //是否清空\n      clearable: {\n        type: Boolean,\n        default: false,\n      },\n      //是否禁用\n      disabled: {\n        type: Boolean,\n        default: false,\n      },\n      value: {\n        type: [Object, Array],\n        default() {\n          return {};\n        }\n      }\n    },\n    data() {\n      let selectArr = [];\n      if (this.value instanceof Array) {\n        selectArr = [].concat(this.value);\n      } else if (this.value && this.value.userId) {\n        selectArr.push(this.value);\n      }\n      return {\n        loadTimes: 0,\n        loading: false,\n        showClear: false,\n        currentVal: this.value,\n        selectVal: selectArr,\n        inputHeight: '28px',\n        inputWidth: 0,\n        dialogVisible: false,\n        searchForm: {\n          jobNumberPhoneLike: null,\n        },\n        pageTotal: 0,\n        pageSize: 9,\n        pageSizes: [5, 10, 15, 20],\n        pageNo: 1,\n        tableData: [],\n        organizationId: null,\n        dialogMate: {\n          title: '人员选择'\n        },\n        defaultProps: {\n          children: 'children',\n          label: 'label'\n        },\n        checkedkey:[],\n        treeData:[]\n      }\n    },\n    computed: {\n      currentValArr() {\n        if (!this.currentVal) {\n          return [];\n        }\n        if (this.currentVal instanceof Array) {\n          return this.currentVal;\n        }\n        return [this.currentVal];\n      },\n      currentText() {\n        if (!this.hasVal) {\n          return undefined;\n        }\n        if (this.currentVal instanceof Array) {\n          return ' ';\n        }\n        return this.currentValArr[0].nickName;\n      },\n      hasVal() {\n        if (!this.currentVal) {\n          return false;\n        }\n        if (this.currentVal instanceof Array) {\n          return this.currentVal.length > 0;\n        }\n        return this.currentVal.userId;\n      }\n    },\n    methods: {\n      async initTree(){\n        let {data,code}=await deptTreeSelect()\n        this.treeData=data\n      },\n      handleSizeChange(val) {\n        this.pageSize = val;\n        this.getData()\n      },\n      rowClickHandle(row, column) {\n        if (column.property === \"operate\") {\n          return;\n        }\n        let exist = this.selectVal.findIndex(item => item.userId === row.userId) !== -1;\n        this.selectionChangeHandle(!exist, row);\n      },\n      appendSelectVal(val) {\n        if (!val || !val.userId) {\n          return;\n        }\n        if (!this.multiple && (!this.selectVal || this.selectVal.length === 0 || this.selectVal[0].userId !== val.userId)) {  //单选\n          this.selectVal = [val];\n          return;\n        }\n        let exist = this.selectVal.find(item => item.userId === val.userId);\n        if (exist) {\n          return;\n        }\n        this.selectVal.push(val);\n      },\n      isChecked(val) {\n        return this.selectVal.filter(item => item.userId === val.userId).length > 0\n      },\n      /**\n       * 打开对话框\n       * @date 2020/5/19 11:15\n       */\n      showDialog() {\n        if (this.disabled) {\n          return;\n        }\n        if (this.loadTimes === 0) {\n          this.getData();\n        }\n        this.dialogVisible = true;\n      },\n      /**\n       * 确认\n       * @date 2020/5/19 11:15\n       */\n      selectConfirm() {\n        if (!this.multiple) {\n          this.currentVal = this.selectVal[0];\n        } else {\n          this.currentVal = [].concat(this.selectVal);\n        }\n        console.log(this.currentVal)\n        this.$emit('input', this.currentVal);\n        this.$emit('change', this.currentVal);\n        //this.dispatch('ElFormItem', 'el.form.change', [this.currentVal]);\n        this.dialogVisible = false\n      },\n      /**\n       * 清除某项\n       * @date 2020/7/31 10:48\n       */\n      removeItem(item) {\n        if (!item || !item.userId) {\n          return;\n        }\n        this.selectionChangeHandle(false, item);\n        this.selectConfirm();\n      },\n      /**\n       * 清空\n       * @date 2020/7/31 10:33\n       */\n      clearHandle() {\n        this.selectVal = [];\n        this.selectConfirm();\n        this.$emit('clear');\n      },\n      /**\n       * 调整调度\n       * @date 2020/7/30 20:08\n       */\n      resetInputHeight() {\n        if (!this.multiple) {\n          return;\n        }\n        if (this.currentValArr.length <= 0) {\n          this.inputHeight = '28px';\n          return;\n        }\n\n        this.$nextTick(() => {\n          if (!this.$refs.reference) return;\n          const tags = this.$refs.tags;\n          const sizeInMap = 28 || 40;\n          this.inputHeight = this.currentValArr.length === 0\n            ? sizeInMap + 'px'\n            : Math.max(\n            tags ? (tags.clientHeight + (tags.clientHeight > sizeInMap ? 6 : 0)) : 0,\n            sizeInMap\n          ) + 'px';\n        });\n\n        setTimeout(() => {\n          if (!this.$refs.reference) return;\n          const tags = this.$refs.tags;\n          const sizeInMap = 28 || 40;\n          this.inputHeight = this.currentValArr.length === 0\n            ? sizeInMap + 'px'\n            : Math.max(\n            tags ? (tags.clientHeight + (tags.clientHeight > sizeInMap ? 6 : 0)) : 0,\n            sizeInMap\n          ) + 'px';\n        }, 340)\n\n      },\n      selectionChangeHandle(isChecked, row) {\n        if (isChecked) {\n          this.appendSelectVal(row);\n          return;\n        }\n        let index = this.selectVal.findIndex(item => item.userId === row.userId);\n        if (index >= 0) {\n          this.selectVal.splice(index, 1);\n        }\n      },\n      nodeClick(data) {\n        if (data.id === '0') {\n          this.organizationId = '';\n        }\n        this.organizationId = data.id;\n        this.getData();\n      },\n      paginationChange (val) {\n        this.pageNo = val;\n        this.getData();\n      },\n      getData() {\n        let query = {\n          pageNum: this.pageNo,\n          pageSize: this.pageSize,\n        };\n\n        Object.assign(query, this.searchForm);\n        this.loading = true;\n\n        UsersByDeptOrGroup(query,'dept').then(res => {\n          this.loading = false;\n          if (res.code !== '0000') {\n            this.loadTimes = 0;\n            this.$message.error(res.msg);\n            return;\n          }\n          this.loadTimes++;\n          this.pageTotal = res.data.total;\n          this.tableData = res.data.records;\n        })\n      },\n    },\n    watch: {\n      clearable() {\n        this.inputWidth = this.$refs.select.clientWidth;\n      },\n      currentValArr() {\n        this.resetInputHeight();\n      },\n      value(val) {\n        let selectArr = [];\n        if (val instanceof Array) {\n          selectArr = [].concat(val);\n        } else if (val && val.userId) {\n          selectArr.push(val);\n        }\n        this.currentVal = val;\n        this.selectVal = selectArr;\n      }\n    },\n    filters: {\n      rolesFilter(roles) {\n        if (!roles || roles.length <= 0) {\n          return '-';\n        }\n        return roles.map(role => {\n          return role.name;\n        }).reduce((x, y) => {\n          return x + \"|\" + y;\n        });\n      }\n    },\n    beforeDestroy() {\n\n    }\n  }\n</script>\n\n<style>\n  .pc-user-info-select-dialog .el-dialog__body {\n    padding: 0 20px 10px 20px !important;\n    color: #606266;\n    font-size: 14px;\n    word-break: break-all;\n  }\n</style>\n\n<style scoped>\n  .pc-user-info-select {\n    display: inline-block;\n    position: relative;\n  }\n\n  .disabled {\n    background-color: #f5f7fa;\n    border-color: #e4e7ed;\n    color: #C0C4CC;\n    cursor: not-allowed;\n  }\n\n  .select-panel {\n    border: 1px dashed #e0e0e0;\n    margin-bottom: 10px;\n    min-height: 37px;\n    padding: 5px;\n  }\n\n  .inner-tag {\n    margin: 2px 0 2px 6px;\n  }\n</style>\n"], "sourceRoot": "src/components/userSelect"}]}