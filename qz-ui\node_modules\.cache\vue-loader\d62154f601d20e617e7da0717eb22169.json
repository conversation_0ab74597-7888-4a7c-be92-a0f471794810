{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\pddzcz\\components\\pdczp_cx.vue?vue&type=style&index=0&id=44a73b78&scoped=true&lang=css&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\pddzcz\\components\\pdczp_cx.vue", "mtime": 1748604763458}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouY2FyZC1zdHlsZSB7CiAgYmFja2dyb3VuZC1jb2xvcjogI2UwZWZmMjsKICBsaW5lLWhlaWdodDogM3ZoOwogIGJvcmRlci1yYWRpdXM6IDNweDsKICBmb250LXdlaWdodDogODAwOwogIHBhZGRpbmctbGVmdDogMXZ3OwogIG1hcmdpbi1ib3R0b206IDN2aDsKfQoKLyrmjqfliLZpbnB1dOi+k+WFpeahhui+ueahhuaYr+WQpuaYvuekuiovCi5lbElucHV0ID4+PiAuZWwtaW5wdXRfX2lubmVyIHsKICBib3JkZXI6IDA7Cn0KCi5jZW50ZXIgewogIHRleHQtYWxpZ246IGNlbnRlcjsgLyrorqlkaXblhoXpg6jmloflrZflsYXkuK0qLwp9Cg=="}, {"version": 3, "sources": ["pdczp_cx.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6lBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "pdczp_cx.vue", "sourceRoot": "src/views/dagangOilfield/czpgl/pddzcz/components", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 180 }\"\n      @handleReset=\"filterReset\"\n    />\n\n    <div>\n      <el-white class=\"button-group\">\n        <el-row :gutter=\"3\" class=\"button_btn pull-right\">\n          <el-col\n            style=\"\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n          \"\n          >\n          </el-col>\n        </el-row>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"selectChange\"\n          height=\"66vh\"\n        >\n        </comp-table>\n      </el-white>\n    </div>\n\n    <!-- 详情/新增/修改 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"180px\" ref=\"form\" :model=\"form\" disabled>\n        <div>\n          <div>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"配电站名称\" prop=\"pdzCn\">\n                  <el-input v-model=\"form.pdzCn\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"编号\" prop=\"bm\">\n                  <el-input v-model=\"form.bm\" placeholder=\"请输入状态\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"操作人\" prop=\"czr\">\n                  <el-input v-model=\"form.czr\" placeholder=\"请输入操作人\" />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"监护人\" prop=\"jhr\">\n                  <el-input v-model=\"form.jhr\" placeholder=\"请输入监护人\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"审核人\" prop=\"fgssprmc\">\n                  <el-input\n                    v-model=\"form.fgssprmc\"\n                    placeholder=\"请输入审核人\"\n                  />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"日期\" prop=\"rq\">\n                  <el-date-picker\n                    v-model=\"form.rq\"\n                    type=\"date\"\n                    format=\"yyyy-MM-dd\"\n                    value-format=\"yyyy-MM-dd\"\n                    style=\"width: 100%\"\n                  >\n                  </el-date-picker>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"24\">\n                <el-form-item label=\"工作名称\" prop=\"gzmc\">\n                  <el-input type=\"textarea\" :rows=\"2\" v-model=\"form.gzmc\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"24\">\n                <el-form-item label=\"上传图片：\">\n                  <el-upload\n                    action=\"\"\n                    ref=\"uploadImg\"\n                    accept=\"image/jpeg,image/jpg,image/png\"\n                    :headers=\"header\"\n                    :multiple=\"true\"\n                    :data=\"uploadImgData\"\n                    :file-list=\"imgList\"\n                    :auto-upload=\"false\"\n                    list-type=\"picture-card\"\n                    :on-preview=\"handlePictureCardPreview\"\n                  >\n                    <i class=\"el-icon-plus\"></i>\n                  </el-upload>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"8\">\n                <el-form-item label=\"操作项数：\" prop=\"czxs\">\n                  <el-input-number\n                    v-model=\"form.czxs\"\n                    placeholder=\"请输入操作项数\"\n                  />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"已执行项数：\" prop=\"yzxczxs\">\n                  <el-input-number\n                    v-model=\"form.yzxczxs\"\n                    placeholder=\"请输入已执行操作项数\"\n                  />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"未执行项数：\" prop=\"wzxczxs\">\n                  <el-input-number\n                    v-model=\"form.wzxczxs\"\n                    placeholder=\"请输入未执行操作项数\"\n                  />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"是否已执行：\" prop=\"sfyzx\">\n                  <el-select v-model=\"form.sfyzx\" placeholder=\"请选择\">\n                    <el-option\n                      v-for=\"item in sfyzxList\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </div>\n\n          <!--列表-->\n          <div>\n            <!--第一个表格-->\n            <el-table\n              :data=\"propTableData.colFirst\"\n              height=\"200\"\n              border\n              stripe\n              style=\"width: 100%\"\n            >\n              <el-table-column\n                align=\"center\"\n                prop=\"xh\"\n                width=\"100px\"\n                label=\"序号\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input\n                    placeholder=\"请输入序号\"\n                    v-model=\"scope.row.xh\"\n                  ></el-input>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n                <template slot-scope=\"scope\">\n                  <el-input\n                    v-if=\"scope.row.isSet\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </template>\n              </el-table-column>\n\n              <el-table-column\n                align=\"center\"\n                prop=\"xlr\"\n                label=\"下令人\"\n                width=\"100\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input\n                    v-if=\"scope.row.isSet\"\n                    v-model=\"scope.row.xlr\"\n                  ></el-input>\n                </template>\n              </el-table-column>\n\n              <el-table-column\n                align=\"center\"\n                prop=\"xlsj\"\n                label=\"下令时间\"\n                width=\"204\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-date-picker\n                    v-if=\"scope.row.isSet\"\n                    v-model=\"scope.row.xlsj\"\n                    type=\"datetime\"\n                    format=\"yyyy-MM-dd HH:mm\"\n                    value-format=\"yyyy-MM-dd HH:mm\"\n                    style=\"width: 100%\"\n                  >\n                  </el-date-picker>\n                </template>\n              </el-table-column>\n\n              <el-table-column\n                align=\"center\"\n                prop=\"hlsj\"\n                label=\"回令时间\"\n                width=\"204\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-date-picker\n                    v-if=\"scope.row.isSet\"\n                    v-model=\"scope.row.hlsj\"\n                    type=\"datetime\"\n                    format=\"yyyy-MM-dd HH:mm\"\n                    value-format=\"yyyy-MM-dd HH:mm\"\n                    style=\"width: 100%\"\n                  >\n                  </el-date-picker>\n                </template>\n              </el-table-column>\n              <el-table-column\n                type=\"sfwc\"\n                width=\"50\"\n                label=\"是否完成\"\n                align=\"center\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-checkbox v-model=\"scope.row.sfwc\"></el-checkbox>\n                </template>\n              </el-table-column>\n            </el-table>\n          </div>\n        </div>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!--图片放大展示弹出框-->\n    <el-image-viewer\n      v-if=\"imgDialogVisible\"\n      :on-close=\"onClose\"\n      :url-list=\"dialogImageUrl\"\n      zIndex=\"3000\"\n    />\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport { getPdsOptionsDataList } from \"@/api/dagangOilfield/asset/pdsgl\";\nimport { getCzpmxList, getList, remove } from \"@/api/yxgl/pdyxgl/pddzczp\";\nimport ElImageViewer from \"element-ui/packages/image/src/image-viewer\";\nexport default {\n  name: \"czp_cx\",\n  components: { ElImageViewer },\n  data() {\n    return {\n      pdsOptionsDataList: [],\n      isDisabledBj: true,\n      statusOptions: [\n        {\n          value: \"0\",\n          label: \"操作票填报\"\n        },\n        {\n          value: \"1\",\n          label: \"班组审核\"\n        },\n        {\n          value: \"2\",\n          label: \"分公司审核\"\n        },\n        {\n          value: \"3\",\n          label: \"操作票办结\"\n        },\n        {\n          value: \"4\",\n          label: \"结束\"\n        }\n      ],\n      buttonNameShow: false,\n      buttonName: \"\",\n      rules: {\n        fgs: [{ required: true, message: \"单位不能为空\", trigger: \"select\" }],\n        pdzmc: [{ required: true, message: \"单位不能为空\", trigger: \"select\" }],\n        flr: [{ required: true, message: \"发令人不能为空\", trigger: \"change\" }],\n        slr: [{ required: true, message: \"受令人不能为空\", trigger: \"change\" }],\n        flsj: [\n          { required: true, message: \"发令时间不能为空\", trigger: \"change\" }\n        ],\n        czrw: [\n          { required: true, message: \"操作任务不能为空\", trigger: \"change\" }\n        ],\n        czr: [{ message: \"操作人不能为空\", trigger: \"blur\" }],\n        jhr: [{ message: \"监护人不能为空\", trigger: \"blur\" }],\n        kssj: [{ message: \"操作开始时间不能为空\", trigger: \"change\" }],\n        jssj: [{ message: \"操作结束时间不能为空\", trigger: \"change\" }]\n      },\n      bjr: \"\",\n      // 是否已执行下拉框\n      sfyzxList: [\n        { label: \"已执行\", value: \"已执行\" },\n        { label: \"未执行\", value: \"未执行\" }\n      ],\n      // 获取当前登录人账号\n      currentUser: this.$store.getters.name,\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"czplc\",\n        businessKey: \"\",\n        businessType: \"倒闸操作票\",\n        variables: {},\n        defaultFrom: true,\n        nextUser: \"\",\n        processType: \"complete\"\n      },\n      //工作流弹窗\n      isShow: false,\n      //流程图查看\n      openLoadingImg: false,\n      imgSrc: \"\", //流程图查看地址\n      timeData: [],\n      timeLineShow: false,\n      //弹出框标题\n      activitiOption: { title: \"\" },\n      titleyl: \"\",\n      //图片地址url\n      dialogImageUrl: \"\",\n      //展示图片dialog控制\n      imgDialogVisible: false,\n      //上传图片时的携带的其他参数\n      uploadImgData: {\n        businessId: \"\" //携带的表单主键id\n      },\n      //上传图片时的请求头\n      header: {},\n      //图片list\n      imgList: [],\n      loginForm: {\n        userName: \"\",\n        password: \"\",\n        rememberMe: false,\n        code: \"6666\",\n        uuid: \"\"\n      },\n      selection: [],\n      yl: false,\n      // 多选框选中的id\n      ids: [],\n      //弹出框中表格数据\n      propTableData: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      dwSelected: [{ label: \"配电运维分公司\", value: \"3013\" }],\n      //form表单\n      form: {\n        status: \"\",\n        kssj: \"\",\n        jssj: \"\",\n        czrw: \"\",\n        flr: \"\",\n        slr: \"\",\n        flsj: \"\",\n        lx: 3, //配电\n        colFirst: [],\n        pdzmc: \"\",\n        czxs: 0,\n        yzxczxs: 0,\n        wzxczxs: 0\n      },\n      formCzp: {\n        pdshr: \"\",\n        yj: \"\"\n      },\n      //详情弹框是否显示\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          slr: \"\",\n          flr: \"\",\n          czsjArr: [],\n          flsjArr: []\n        },\n        fieldList: [\n          {\n            label: \"操作时间\",\n            value: \"czsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"受令人\", type: \"input\", value: \"slr\", clearable: true },\n          { label: \"发令人\", type: \"input\", value: \"flr\", clearable: true },\n          {\n            label: \"发令时间\",\n            value: \"flsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"配电站名称\", prop: \"pdzCn\", minWidth: \"120\" },\n          { label: \"操作开始时间\", prop: \"kssj\", minWidth: \"120\" },\n          { label: \"操作结束时间\", prop: \"jssj\", minWidth: \"120\" },\n          { label: \"操作任务\", prop: \"czrw\", minWidth: \"120\" },\n          // {label: '审核人', prop: 'pdshr', minWidth: '100'},\n          { label: \"发令人\", prop: \"flr\", minWidth: \"100\" },\n          { label: \"受令人\", prop: \"slr\", minWidth: \"100\" },\n          { label: \"发令时间\", prop: \"flsj\", minWidth: \"120\" },\n          { label: \"操作项数\", prop: \"czxs\", minWidth: \"80\" },\n          { label: \"已执行项数\", prop: \"yzxczxs\", minWidth: \"90\" },\n          { label: \"未执行项数\", prop: \"wzxczxs\", minWidth: \"90\" },\n          {\n            prop: \"operation\",\n            label: \"操作\",\n            minWidth: \"130px\",\n            style: { display: \"block\" },\n            //操作列固定再右侧\n            fixed: \"right\",\n            operation: [{ name: \"详情\", clickFun: this.getInfo }]\n          }\n        ]\n      },\n      params: {\n        lx: 3,\n        status: \"4\"\n      }\n    };\n  },\n  mounted() {\n    //获取token\n    this.header.token = getToken();\n    this.getPdsOptionsDataList();\n    this.getData();\n    if (\"admin\" === this.$store.getters.name) {\n      let option = { name: \"删除\", clickFun: this.deleteRow };\n      this.tableAndPageInfo.tableHeader[10].operation.push(option);\n    }\n  },\n  methods: {\n    //图片放大\n    handlePictureCardPreview(file) {\n      this.dialogImageUrl = [file.url];\n      this.imgDialogVisible = true;\n    },\n    onClose() {\n      this.imgDialogVisible = false;\n    },\n    // 预览弹框\n    handleYlChange() {\n      this.titleyl = \"查看操作项目\";\n      this.yl = true;\n    },\n    //选择行\n    selectChange(selection) {\n      this.ids = selection.map(item => item.objId);\n      this.selection = selection;\n      this.form = this.selection[0];\n    },\n    //获取配电室下拉框数据\n    getPdsOptionsDataList() {\n      getPdsOptionsDataList({}).then(res => {\n        this.pdsOptionsDataList = res.data;\n      });\n    },\n    filterReset() {\n      this.params.status = \"4\";\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        param.mySorts = [{ prop: \"updateTime\", asc: false }];\n        if (!param.status) {\n          param.status = \"0,1,2,3\";\n        }\n        const { data, code } = await getList(param);\n        if (code === \"0000\") {\n          for (let i of data.records) {\n            i.fgsmc = \"配电运维分公司\";\n            this.statusOptions.forEach(element => {\n              if (i.status === element.value) {\n                i.statusCn = element.label;\n              }\n            });\n          }\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //删除按钮\n    async deleteRow(row) {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove(row.objId).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    //详情\n    getInfo(row) {\n      this.getCzpmx(row);\n      this.title = \"配电操作票详情查看\";\n      this.form = { ...row };\n      //图片结合\n      this.imgList = this.form.imgList;\n      this.isDisabled = true;\n      this.isShowDetails = true;\n      this.getShow();\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false;\n    },\n    /**\n     *获取操作票明细\n     */\n    async getCzpmx(row) {\n      try {\n        const { data, code } = await getCzpmxList({ objId: row.objId });\n        if (code === \"0000\") {\n          //给list添加字段\n          data.forEach(item => {\n            this.$set(item, \"isSet\", true);\n          });\n          this.propTableData.colFirst = data;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.card-style {\n  background-color: #e0eff2;\n  line-height: 3vh;\n  border-radius: 3px;\n  font-weight: 800;\n  padding-left: 1vw;\n  margin-bottom: 3vh;\n}\n\n/*控制input输入框边框是否显示*/\n.elInput >>> .el-input__inner {\n  border: 0;\n}\n\n.center {\n  text-align: center; /*让div内部文字居中*/\n}\n</style>\n"]}]}