{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\ysbzk.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\ysbzk.vue", "mtime": 1706897323967}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON>umhueebruWQjeensO+8miIsCiAgICAgICAgdmFsdWU6ICIiLAogICAgICAgIHR5cGU6ICJpbnB1dCIsCiAgICAgICAgbmFtZTogInhtbWMiLAogICAgICAgIGRlZmF1bHQ6IHRydWUsCiAgICAgICAgcnVsZXM6IHsKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpemhueebruWQjeensCIKICAgICAgICB9CiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogIuaJgOWxnuS4k+S4mu+8miIsCiAgICAgICAgdmFsdWU6ICIiLAogICAgICAgIHR5cGU6ICJzZWxlY3RDaGFuZ2UxIiwKICAgICAgICBuYW1lOiAic3BiIiwKICAgICAgICBkZWZhdWx0OiB0cnVlLAogICAgICAgIG9wdGlvbnM6IFt7CiAgICAgICAgICBsYWJlbDogIuWPmOeUtSIsCiAgICAgICAgICB2YWx1ZTogIuWPmOeUtSIKICAgICAgICB9LCB7CiAgICAgICAgICBsYWJlbDogIumFjeeUtSIsCiAgICAgICAgICB2YWx1ZTogIumFjeeUtSIKICAgICAgICB9LCB7CiAgICAgICAgICBsYWJlbDogIui+k+eUtSIsCiAgICAgICAgICB2YWx1ZTogIui+k+eUtSIKICAgICAgICB9XSwKICAgICAgICBydWxlczogewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6K+36L6T5YWl6aqM5pS257G75Z6LIgogICAgICAgIH0KICAgICAgfSwgewogICAgICAgIGxhYmVsOiAi6K6+5aSH5YiG57G777yaIiwKICAgICAgICB2YWx1ZTogIiIsCiAgICAgICAgdHlwZTogInNlbGVjdCIsCiAgICAgICAgbmFtZTogInNiZmwiLAogICAgICAgIGRlZmF1bHQ6IHRydWUsCiAgICAgICAgb3B0aW9uczogW10sCiAgICAgICAgcnVsZXM6IHsKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpeiuvuWkh+WIhuexuyIKICAgICAgICB9CiAgICAgIH0sCiAgICAgIC8qIHsKICAgICAgICBsYWJlbDogJ+iuvuWkh+WIhuexu++8micsCiAgICAgICAgdmFsdWU6ICcnLAogICAgICAgIHR5cGU6ICdpbnB1dCcsCiAgICAgICAgbmFtZTogJ3NiZmwnLAogICAgICAgIGRlZmF1bHQ6IHRydWUsCiAgICAgICAgaGlkZGVuOiBmYWxzZSwKICAgICAgICBydWxlczogeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeiuvuWkh+WIhuexuycgfQogICAgICB9LCovCiAgICAgIHsKICAgICAgICBsYWJlbDogIuWkh+azqO+8miIsCiAgICAgICAgdmFsdWU6ICIiLAogICAgICAgIG5hbWU6ICJieiIsCiAgICAgICAgZGVmYXVsdDogdHJ1ZSwKICAgICAgICB0eXBlOiAidGV4dGFyZWEiLAogICAgICAgIHJ1bGVzOiB7CiAgICAgICAgICByZXF1aXJlZDogZmFsc2UsCiAgICAgICAgICBtZXNzYWdlOiAi6K+36L6T5YWl5aSH5rOoIgogICAgICAgIH0KICAgICAgfSwgewogICAgICAgIGxhYmVsOiAi5Li76ZSuaWQ6IiwKICAgICAgICB2YWx1ZTogIiIsCiAgICAgICAgbmFtZTogImlkIiwKICAgICAgICBkZWZhdWx0OiBmYWxzZSwKICAgICAgICB0eXBlOiAiaW5wdXQiLAogICAgICAgIGhpZGRlbjogZmFsc2UsCiAgICAgICAgcnVsZXM6IHsKICAgICAgICAgIHJlcXVpcmVkOiBmYWxzZQogICAgICAgIH0KICAgICAgfV0sCiAgICAgIC8v5Li76KGo5Yqg6L29CiAgICAgIG1haW5Mb2FkaW5nOiBmYWxzZSwKICAgICAgLy/pqozmlLbmoIflh4bkuLvooajmn6Xor6LmnaHku7YKICAgICAgbWFpblBhcmFubXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICB0b3RhbDogMAogICAgICB9LAogICAgICAvL+mqjOaUtuagh+WHhuS4u+ihqOaVsOaNrgogICAgICAvLyBtYWluVGFibGVEYXRhOiBbXSwKICAgICAgdGFibGVBbmRQYWdlSW5mbzogewogICAgICAgIHBhZ2VyOiB7CiAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICBwYWdlUmVzaXplOiAnJywKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgIHNpemVzOiBbMTAsIDIwLCA1MCwgMTAwXQogICAgICAgIH0sCiAgICAgICAgb3B0aW9uOiB7CiAgICAgICAgICBjaGVja0JveDogZmFsc2UsCiAgICAgICAgICBzZXJpYWxOdW1iZXI6IGZhbHNlCiAgICAgICAgfSwKICAgICAgICB0YWJsZURhdGE6IFtdLAogICAgICAgIHRhYmxlSGVhZGVyOiBbewogICAgICAgICAgcHJvcDogJ3lzbHhOYW1lJywKICAgICAgICAgIGxhYmVsOiAn6aqM5pS257G75Z6LJywKICAgICAgICAgIG1pbldpZHRoOiAnODAnCiAgICAgICAgfSwgewogICAgICAgICAgcHJvcDogJ3NwYicsCiAgICAgICAgICBsYWJlbDogJ+aJgOWxnuS4k+S4micsCiAgICAgICAgICBtaW5XaWR0aDogJzgwJywKICAgICAgICAgIGN1c3RvbTogdHJ1ZQogICAgICAgIH0sIHsKICAgICAgICAgIHByb3A6ICdzYmZsbWMnLAogICAgICAgICAgbGFiZWw6ICforr7lpIfliIbnsbsnLAogICAgICAgICAgbWluV2lkdGg6ICc4MCcKICAgICAgICB9LCB7CiAgICAgICAgICBwcm9wOiAnanhmbCcsCiAgICAgICAgICBsYWJlbDogJ+mqjOaUtuWQjeensCcsCiAgICAgICAgICBtaW5XaWR0aDogJzIyMCcKICAgICAgICB9LCB7CiAgICAgICAgICBwcm9wOiAneG1tYycsCiAgICAgICAgICBsYWJlbDogJ+mhueebruWQjeensCcsCiAgICAgICAgICBtaW5XaWR0aDogJzEwMCcKICAgICAgICB9LCB7CiAgICAgICAgICBwcm9wOiAnYnonLAogICAgICAgICAgbGFiZWw6ICflpIfms6gnLAogICAgICAgICAgbWluV2lkdGg6ICc4MCcKICAgICAgICB9XQogICAgICB9LAogICAgICAvL+S4u+ihqOmAieS4reihjOaVsOaNrgogICAgICBtYWluVGFibGVTZWxlY3RSb3dzOiBbXSwKICAgICAgLy/mmK/lkKblsZXnpLrorr7lpIfliIbnsbvmoJEKICAgICAgc2hvd0RldmljZVRyZWU6IGZhbHNlLAogICAgICB2YWxGbGFnOiAiIiwKICAgICAgLy/pqozmlLbnsbvlnovkuIvmi4nmoYbmlbDmja4KICAgICAgeXNseE9wdGlvbnM6IFtdCiAgICB9OwogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHRoaXMuaW5pdERvbWFpbigpOwogIH0sCiAgbWV0aG9kczogewogICAgc29ydENoYW5nZTogZnVuY3Rpb24gc29ydENoYW5nZShfcmVmKSB7CiAgICAgIHZhciBjb2x1bW4gPSBfcmVmLmNvbHVtbiwKICAgICAgICAgIHByb3AgPSBfcmVmLnByb3AsCiAgICAgICAgICBvcmRlciA9IF9yZWYub3JkZXI7CgogICAgICBpZiAob3JkZXIpIHsKICAgICAgICBpZiAob3JkZXIuaW5kZXhPZigiZGVzYyIpID4gLTEpIHsKICAgICAgICAgIHRoaXMuZmlsdGVyUGFyYW1zLm15U29ydHMgPSBbewogICAgICAgICAgICBwcm9wOiBwcm9wLAogICAgICAgICAgICBhc2M6IGZhbHNlCiAgICAgICAgICB9XTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5maWx0ZXJQYXJhbXMubXlTb3J0cyA9IFt7CiAgICAgICAgICAgIHByb3A6IHByb3AsCiAgICAgICAgICAgIGFzYzogdHJ1ZQogICAgICAgICAgfV07CiAgICAgICAgfQogICAgICB9CgogICAgICB0aGlzLmdldERhdGEoKTsKICAgIH0sCiAgICBnZXREYXRhOiBmdW5jdGlvbiBnZXREYXRhKHBhcmFtcykgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwoKICAgICAgdGhpcy5maWx0ZXJQYXJhbXMgPSAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCB0aGlzLmZpbHRlclBhcmFtcyksIHBhcmFtcyk7CiAgICAgIHZhciBwYXJhbSA9IHRoaXMuZmlsdGVyUGFyYW1zOwogICAgICAoMCwgX3lzYnprLmdldEJ6WXNienpiKShwYXJhbSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAvLyB0aGlzLm1haW5UYWJsZURhdGEgPSByZXNwb25zZS5kYXRhLnJlY29yZHM7CiAgICAgICAgLy8gdGhpcy5tYWluUGFyYW5tcy50b3RhbCA9IHJlc3BvbnNlLmRhdGEudG90YWw7CiAgICAgICAgX3RoaXMudGFibGVBbmRQYWdlSW5mby50YWJsZURhdGEgPSByZXNwb25zZS5kYXRhLnJlY29yZHM7CiAgICAgICAgX3RoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci50b3RhbCA9IHJlc3BvbnNlLmRhdGEudG90YWw7CgogICAgICAgIF90aGlzLnRhYmxlQW5kUGFnZUluZm8udGFibGVEYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgIF90aGlzLnlzbHhPcHRpb25zLmZvckVhY2goZnVuY3Rpb24gKGVsZW1lbnQpIHsKICAgICAgICAgICAgaWYgKGl0ZW0ueXNseCA9PT0gZWxlbWVudC52YWx1ZSkgewogICAgICAgICAgICAgIGl0ZW0ueXNseE5hbWUgPSBlbGVtZW50LmxhYmVsOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9KTsKCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEudG90YWwgPiAwKSB7CiAgICAgICAgICBfdGhpcy5oYW5kbGVDdXJyZW50Q2hhbmdlKHJlc3BvbnNlLmRhdGEucmVjb3Jkc1swXSk7CiAgICAgICAgfQoKICAgICAgICBfdGhpcy5tYWluTG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICBnZXRTYmx4TGlzdDogZnVuY3Rpb24gZ2V0U2JseExpc3QodmFsKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwoKICAgICAgdGhpcy5mb3JtTGlzdC5tYXAoIC8qI19fUFVSRV9fKi9mdW5jdGlvbiAoKSB7CiAgICAgICAgdmFyIF9yZWYyID0gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSggLyojX19QVVJFX18qL3JlZ2VuZXJhdG9yUnVudGltZS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUoaXRlbSkgewogICAgICAgICAgdmFyIHNibHhQYXJhbTsKICAgICAgICAgIHJldHVybiByZWdlbmVyYXRvclJ1bnRpbWUud3JhcChmdW5jdGlvbiBfY2FsbGVlJChfY29udGV4dCkgewogICAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICAgIHN3aXRjaCAoX2NvbnRleHQucHJldiA9IF9jb250ZXh0Lm5leHQpIHsKICAgICAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICAgICAgaWYgKCEoaXRlbS5uYW1lID09PSAic2JmbCIpKSB7CiAgICAgICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDY7CiAgICAgICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAgIGl0ZW0udmFsdWUgPSAiIjsKICAgICAgICAgICAgICAgICAgc2JseFBhcmFtID0gewogICAgICAgICAgICAgICAgICAgIHR5cGU6IHZhbCArICLorr7lpIciCiAgICAgICAgICAgICAgICAgIH07CiAgICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSA1OwogICAgICAgICAgICAgICAgICByZXR1cm4gX3RoaXMyLmdldFNibHhEYXRhTGlzdFNlbGVjdGVkKHNibHhQYXJhbSk7CgogICAgICAgICAgICAgICAgY2FzZSA1OgogICAgICAgICAgICAgICAgICBpdGVtLm9wdGlvbnMgPSBfY29udGV4dC5zZW50OwoKICAgICAgICAgICAgICAgIGNhc2UgNjoKICAgICAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5zdG9wKCk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9LCBfY2FsbGVlKTsKICAgICAgICB9KSk7CgogICAgICAgIHJldHVybiBmdW5jdGlvbiAoX3gpIHsKICAgICAgICAgIHJldHVybiBfcmVmMi5hcHBseSh0aGlzLCBhcmd1bWVudHMpOwogICAgICAgIH07CiAgICAgIH0oKSk7CiAgICB9LAogICAgaGFuZGxlRXZlbnQ6IGZ1bmN0aW9uIGhhbmRsZUV2ZW50KHZhbCwgdmFsMSkgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKCiAgICAgIGlmICh2YWwubGFiZWwgPT09ICJzcGIiICYmIHZhbC52YWx1ZSAmJiB2YWwudmFsdWUgIT09ICIiKSB7CiAgICAgICAgdGhpcy4kc2V0KHZhbDEsICJzYmZsIiwgIiIpOwogICAgICAgIHRoaXMuZmlsdGVySW5mby5maWVsZExpc3QubWFwKCAvKiNfX1BVUkVfXyovZnVuY3Rpb24gKCkgewogICAgICAgICAgdmFyIF9yZWYzID0gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSggLyojX19QVVJFX18qL3JlZ2VuZXJhdG9yUnVudGltZS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUyKGl0ZW0pIHsKICAgICAgICAgICAgdmFyIHNibHhQYXJhbTsKICAgICAgICAgICAgcmV0dXJuIHJlZ2VuZXJhdG9yUnVudGltZS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUyJChfY29udGV4dDIpIHsKICAgICAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICAgICAgc3dpdGNoIChfY29udGV4dDIucHJldiA9IF9jb250ZXh0Mi5uZXh0KSB7CiAgICAgICAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICAgICAgICBpZiAoIShpdGVtLnZhbHVlID09PSAic2JmbCIpKSB7CiAgICAgICAgICAgICAgICAgICAgICBfY29udGV4dDIubmV4dCA9IDU7CiAgICAgICAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgICAgIHNibHhQYXJhbSA9IHsKICAgICAgICAgICAgICAgICAgICAgIHR5cGU6IHZhbC52YWx1ZSArICLorr7lpIciCiAgICAgICAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgICAgICAgICBfY29udGV4dDIubmV4dCA9IDQ7CiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIF90aGlzMy5nZXRTYmx4RGF0YUxpc3RTZWxlY3RlZChzYmx4UGFyYW0pOwoKICAgICAgICAgICAgICAgICAgY2FzZSA0OgogICAgICAgICAgICAgICAgICAgIGl0ZW0ub3B0aW9ucyA9IF9jb250ZXh0Mi5zZW50OwoKICAgICAgICAgICAgICAgICAgY2FzZSA1OgogICAgICAgICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDIuc3RvcCgpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSwgX2NhbGxlZTIpOwogICAgICAgICAgfSkpOwoKICAgICAgICAgIHJldHVybiBmdW5jdGlvbiAoX3gyKSB7CiAgICAgICAgICAgIHJldHVybiBfcmVmMy5hcHBseSh0aGlzLCBhcmd1bWVudHMpOwogICAgICAgICAgfTsKICAgICAgICB9KCkpOwogICAgICB9CiAgICB9LAoKICAgIC8qKgogICAgICog6I635Y+W6K6+5aSH57G75Z6L5LiL5ouJ5qGG5pWw5o2uCiAgICAgKi8KICAgIGdldFNibHhEYXRhTGlzdFNlbGVjdGVkOiBmdW5jdGlvbiBnZXRTYmx4RGF0YUxpc3RTZWxlY3RlZChzYmx4UGFyYW0pIHsKICAgICAgcmV0dXJuICgwLCBfYmRzYnR6LmdldFNibHhEYXRhTGlzdFNlbGVjdGVkKShzYmx4UGFyYW0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIHJldHVybiByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLy/ph43nva7mjInpkq4KICAgIGdldFJlc2V0OiBmdW5jdGlvbiBnZXRSZXNldCgpIHsKICAgICAgdGhpcy5maWx0ZXJQYXJhbXMgPSB7fTsKICAgICAgdGhpcy5maWx0ZXJJbmZvLmZpZWxkTGlzdC5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgaWYgKGl0ZW0udHlwZSA9PT0gImNoZWNrYm94IikgewogICAgICAgICAgaXRlbS5jaGVja2JveFZhbHVlID0gW107CiAgICAgICAgfQogICAgICB9KTsKICAgICAgdGhpcy4kcmVmcy5kZXRhaWwuZ2V0UmVzZXQoKTsKICAgIH0sCiAgICAvL+iOt+WPlumqjOaUtuagh+WHhuS4u+ihqOaVsOaNrgogICAgLy8gZ2V0TWFpblN0YW5kYXJkRGF0YSgpIHsKICAgIC8vICAgdGhpcy5tYWluTG9hZGluZyA9IHRydWU7CiAgICAvLyAgIGNvbnN0IHBhcmFtID0geyAuLi50aGlzLmZpbHRlclBhcmFtcywgLi4udGhpcy5tYWluUGFyYW5tcyB9OwogICAgLy8gICBnZXRCellzYnp6YihwYXJhbSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAvLyAgICAgdGhpcy5tYWluVGFibGVEYXRhID0gcmVzcG9uc2UuZGF0YS5yZWNvcmRzOwogICAgLy8gICAgIHRoaXMubWFpblBhcmFubXMudG90YWwgPSByZXNwb25zZS5kYXRhLnRvdGFsOwogICAgLy8gICAgIHRoaXMubWFpblRhYmxlRGF0YS5mb3JFYWNoKGl0ZW0gPT4gewogICAgLy8gICAgICAgdGhpcy55c2x4T3B0aW9ucy5mb3JFYWNoKGVsZW1lbnQgPT4gewogICAgLy8gICAgICAgICBpZiAoaXRlbS55c2x4ID09PSBlbGVtZW50LnZhbHVlKSB7CiAgICAvLyAgICAgICAgICAgaXRlbS55c2x4TmFtZSA9IGVsZW1lbnQubGFiZWw7CiAgICAvLyAgICAgICAgIH0KICAgIC8vICAgICAgIH0pOwogICAgLy8gICAgIH0pOwogICAgLy8gICAgIGlmIChyZXNwb25zZS5kYXRhLnRvdGFsID4gMCkgewogICAgLy8gICAgICAgdGhpcy5oYW5kbGVDdXJyZW50Q2hhbmdlKHJlc3BvbnNlLmRhdGEucmVjb3Jkc1swXSk7CiAgICAvLyAgICAgfQogICAgLy8gICAgIHRoaXMubWFpbkxvYWRpbmcgPSBmYWxzZTsKICAgIC8vICAgfSk7CiAgICAvLyB9LAogICAgLy/pqozmlLbmoIflh4bkuLvooajooYzngrnlh7vkuovku7bpgLvovpEKICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZUN1cnJlbnRDaGFuZ2UodmFsKSB7CiAgICAgIC8vIHRoaXMuc2VsZWN0RGF0YSA9IFtdCiAgICAgIC8vIOa4heepuuaJgOaciemAieaLqQogICAgICAvLyB0aGlzLiRyZWZzLm1haW50YWJsZS5jbGVhclNlbGVjdGlvbigpOwogICAgICAvLyAg6YCJ5Lit5b2T5YmN6YCJ5oupCiAgICAgIC8vIHRoaXMuJHJlZnMubWFpbnRhYmxlLnRvZ2dsZVJvd1NlbGVjdGlvbih2YWwpOwogICAgICAvLyB0aGlzLnNlbGVjdERhdGEucHVzaCh2YWwpCiAgICAgIC8vIHRoaXMuJHJlZnMubWFpbnRhYmxlLnNldEN1cnJlbnRSb3codmFsKTsKICAgICAgLy/nu5nlrZDnu4Tku7bkvKDlgLwKICAgICAgaWYgKHRoaXMudmFsRmxhZyA9PT0gdmFsKSB7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICB0aGlzLnZhbEZsYWcgPSB2YWw7CiAgICAgIHRoaXMuJHJlZnMuZGV0YWlsLmdldE1haW5UYWJsZVNlbGVjdGVkUm93KFt2YWxdKTsKICAgIH0sCiAgICAvL+WkjemAieahhumAieS4remAu+i+kQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTZWxlY3Rpb25DaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMubWFpblRhYmxlU2VsZWN0Um93cyA9IHZhbDsKICAgICAgY29uc29sZS5sb2codGhpcy5tYWluVGFibGVTZWxlY3RSb3dzKTsgLy8gdGhpcy4kcmVmcy5kZXRhaWwuZ2V0TWFpblRhYmxlU2VsZWN0ZWRSb3codmFsKQogICAgfSwKICAgIC8v5L+d5a2Y5Li76KGo5pWw5o2uCiAgICBzYXZlTWFpbkRhdGE6IGZ1bmN0aW9uIHNhdmVNYWluRGF0YShmb3JtRGF0YSkgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKCiAgICAgIHZhciBtZXNzYWdlID0gIiI7CgogICAgICBpZiAoZm9ybURhdGEuaWQgPT09ICIiIHx8ICFmb3JtRGF0YS5pZCkgewogICAgICAgIG1lc3NhZ2UgPSAi5paw5aKe5oiQ5YqfIjsKICAgICAgfSBlbHNlIHsKICAgICAgICBtZXNzYWdlID0gIuS/ruaUueaIkOWKnyI7CiAgICAgIH0KCiAgICAgICgwLCBfeXNiemsuc2F2ZU9yVXBkYXRlQnpZc2J6emIpKGZvcm1EYXRhKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBpZiAocmVzLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgX3RoaXM0LiRtZXNzYWdlLnN1Y2Nlc3MobWVzc2FnZSk7CgogICAgICAgICAgX3RoaXM0LmdldERhdGEoKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgX3RoaXM0LiRtZXNzYWdlLmVycm9yKCLmk43kvZzlpLHotKUiKTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8v5paw5aKe5Li76KGo5pWw5o2uCiAgICBhZGRNYWluRGF0YTogZnVuY3Rpb24gYWRkTWFpbkRhdGEoKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwoKICAgICAgdGhpcy5yZW1pbmRlciA9ICLmlrDlop4iOwogICAgICB2YXIgYWRkRm9ybSA9IHRoaXMuZm9ybUxpc3QubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgaWYgKGl0ZW0ubmFtZSA9PT0gInlzbHgiKSB7CiAgICAgICAgICBpdGVtLm9wdGlvbnMgPSBfdGhpczUueXNseE9wdGlvbnM7CiAgICAgICAgfQoKICAgICAgICByZXR1cm4gaXRlbTsKICAgICAgfSk7CiAgICAgIHRoaXMuJHJlZnMuZGlhbG9nRm9ybS5zaG93enpjKGFkZEZvcm0pOwogICAgfSwKICAgIC8v5L+u5pS55Li76KGo5pWw5o2uCiAgICB1cGRhdGVNYWluRGF0YTogZnVuY3Rpb24gdXBkYXRlTWFpbkRhdGEocm93KSB7CiAgICAgIHZhciBfdGhpczYgPSB0aGlzOwoKICAgICAgdGhpcy5nZXRTYmx4TGlzdChyb3cuc3BiKTsKICAgICAgdmFyIHVwZGF0ZUxpc3QgPSB0aGlzLmZvcm1MaXN0Lm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIGl0ZW0udmFsdWUgPSByb3dbaXRlbS5uYW1lXTsKCiAgICAgICAgaWYgKGl0ZW0ubmFtZSA9PT0gInlzbHgiKSB7CiAgICAgICAgICBpdGVtLm9wdGlvbnMgPSBfdGhpczYueXNseE9wdGlvbnM7CiAgICAgICAgfQoKICAgICAgICByZXR1cm4gaXRlbTsKICAgICAgfSk7CiAgICAgIHRoaXMucmVtaW5kZXIgPSAi5L+u5pS5IjsKICAgICAgdGhpcy4kcmVmcy5kaWFsb2dGb3JtLnNob3d6emModXBkYXRlTGlzdCk7CiAgICB9LAogICAgLy/mn6XnnIvkuLvooajmlbDmja7or6bmg4UKICAgIHNob3dNYWluRGF0YTogZnVuY3Rpb24gc2hvd01haW5EYXRhKHJvdykgewogICAgICB2YXIgX3RoaXM3ID0gdGhpczsKCiAgICAgIHRoaXMuZ2V0U2JseExpc3Qocm93LnNwYik7CiAgICAgIHZhciBpbmZvTGlzdCA9IHRoaXMuZm9ybUxpc3QubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgaXRlbS52YWx1ZSA9IHJvd1tpdGVtLm5hbWVdOwoKICAgICAgICBpZiAoaXRlbS5uYW1lID09PSAieXNseCIpIHsKICAgICAgICAgIGl0ZW0ub3B0aW9ucyA9IF90aGlzNy55c2x4T3B0aW9uczsKICAgICAgICB9CgogICAgICAgIHJldHVybiBpdGVtOwogICAgICB9KTsKICAgICAgdGhpcy5yZW1pbmRlciA9ICLor6bmg4UiOwogICAgICB0aGlzLiRyZWZzLmRpYWxvZ0Zvcm0uc2hvd3hxKGluZm9MaXN0KTsKICAgIH0sCiAgICAvL+WIoOmZpOS4u+ihqOaVsOaNrgogICAgZGVsZXRlTWFpbkRhdGE6IGZ1bmN0aW9uIGRlbGV0ZU1haW5EYXRhKHJvdykgewogICAgICB2YXIgX3RoaXM4ID0gdGhpczsKCiAgICAgIHRoaXMuZm9ybSA9ICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7fSwgcm93KTsKICAgICAgdGhpcy4kY29uZmlybSgi56Gu6K6k5Yig6Zmk6YCJ5Lit5pWw5o2uLCDmmK/lkKbnu6fnu60/IiwgIuaPkOekuiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgICgwLCBfeXNiemsuZGVsZXRlQnpZc2J6emIpKFtfdGhpczguZm9ybS5pZF0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgX3RoaXM4LiRtZXNzYWdlKHsKICAgICAgICAgICAgICBtZXNzYWdlOiAi5Yig6Zmk5oiQ5YqfIiwKICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBfdGhpczguJG1lc3NhZ2UuZXJyb3IoIuaTjeS9nOWksei0pSIpOwogICAgICAgICAgfQoKICAgICAgICAgIF90aGlzOC5nZXREYXRhKCk7CiAgICAgICAgfSk7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczguJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogImluZm8iLAogICAgICAgICAgbWVzc2FnZTogIuW3suWPlua2iOWIoOmZpCIKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgLy/liJ3lp4vor53kuIvmi4nmoYbmlbDmja4KICAgIGluaXREb21haW46IGZ1bmN0aW9uIGluaXREb21haW4oKSB7CiAgICAgIHZhciBfdGhpczkgPSB0aGlzOwoKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoIC8qI19fUFVSRV9fKi9yZWdlbmVyYXRvclJ1bnRpbWUubWFyayhmdW5jdGlvbiBfY2FsbGVlMygpIHsKICAgICAgICB2YXIgX3lpZWxkJGdldERpY3RUeXBlRGF0LCB5c2x4OwoKICAgICAgICByZXR1cm4gcmVnZW5lcmF0b3JSdW50aW1lLndyYXAoZnVuY3Rpb24gX2NhbGxlZTMkKF9jb250ZXh0MykgewogICAgICAgICAgd2hpbGUgKDEpIHsKICAgICAgICAgICAgc3dpdGNoIChfY29udGV4dDMucHJldiA9IF9jb250ZXh0My5uZXh0KSB7CiAgICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgICAgX2NvbnRleHQzLm5leHQgPSAyOwogICAgICAgICAgICAgICAgcmV0dXJuICgwLCBfZGF0YS5nZXREaWN0VHlwZURhdGEpKCJ5c2x4Iik7CgogICAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICAgIF95aWVsZCRnZXREaWN0VHlwZURhdCA9IF9jb250ZXh0My5zZW50OwogICAgICAgICAgICAgICAgeXNseCA9IF95aWVsZCRnZXREaWN0VHlwZURhdC5kYXRhOwogICAgICAgICAgICAgICAgX3RoaXM5LnlzbHhPcHRpb25zID0geXNseDsKCiAgICAgICAgICAgICAgICBfdGhpczkuZmlsdGVySW5mby5maWVsZExpc3QubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICAgICAgICAgIGlmIChpdGVtLnZhbHVlID09PSAieXNseCIpIHsKICAgICAgICAgICAgICAgICAgICByZXR1cm4gaXRlbS5vcHRpb25zID0geXNseDsKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfSk7CgogICAgICAgICAgICAgICAgX3RoaXM5LmdldERhdGEoKTsKCiAgICAgICAgICAgICAgY2FzZSA3OgogICAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQzLnN0b3AoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUzKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgLy9pbnB1dOi+k+WFpeahhum8oOagh+iBmueEpuS6i+S7tgogICAgaW5wdXRGb2N1c0V2ZW50OiBmdW5jdGlvbiBpbnB1dEZvY3VzRXZlbnQodmFsKSB7CiAgICAgIHRoaXMuaXNGaWx0ZXIgPSBmYWxzZTsKCiAgICAgIGlmICh2YWwudGFyZ2V0Lm5hbWUgPT09ICJzYmZsbWMiKSB7CiAgICAgICAgdGhpcy5zaG93RGV2aWNlVHJlZSA9IHRydWU7CiAgICAgIH0KCiAgICAgIGlmICh2YWwudGFyZ2V0Lm5hbWUgPT09ICJzYmZsIikgewogICAgICAgIHRoaXMuc2hvd0RldmljZVRyZWUgPSB0cnVlOwogICAgICAgIHRoaXMuaXNGaWx0ZXIgPSB0cnVlOwogICAgICB9CiAgICB9LAogICAgLy/ojrflj5borr7lpIfliIbnsbvmoJHmlbDmja4KICAgIGdldERldmljZVR5cGVEYXRhOiBmdW5jdGlvbiBnZXREZXZpY2VUeXBlRGF0YShyZXMpIHsKICAgICAgaWYgKHJlcy5sZW5ndGggPiAwKSB7CiAgICAgICAgaWYgKHRoaXMuaXNGaWx0ZXIpIHsKICAgICAgICAgIHZhciBzYmZsID0gIiI7CiAgICAgICAgICB2YXIgc2JmbG1jID0gIiI7CiAgICAgICAgICByZXMuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICBzYmZsbWMgKz0gaXRlbS5uYW1lICsgIiwiOwogICAgICAgICAgICBzYmZsICs9IGl0ZW0uY29kZSArICIsIjsKICAgICAgICAgIH0pOwogICAgICAgICAgdGhpcy5maWx0ZXJJbmZvLmRhdGEuc2JmbCA9IHNiZmwuc3Vic3RyaW5nKDAsIHNiZmwubGVuZ3RoIC0gMSk7CiAgICAgICAgICB0aGlzLmZpbHRlckluZm8uZGF0YS5zYmZsbWMgPSBzYmZsbWMuc3Vic3RyaW5nKDAsIHNiZmxtYy5sZW5ndGggLSAxKTsKICAgICAgICAgIHRoaXMuc2hvd0RldmljZVRyZWUgPSBmYWxzZTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgaWYgKHJlcy5sZW5ndGggPT09IDEpIHsKICAgICAgICAgICAgdGhpcy5mb3JtTGlzdC5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICAgICAgaWYgKGl0ZW0ubmFtZSA9PT0gInNiZmxtYyIpIHsKICAgICAgICAgICAgICAgIGl0ZW0udmFsdWUgPSByZXNbMF0ubmFtZTsKICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgIGlmIChpdGVtLm5hbWUgPT09ICJzYmZsIikgewogICAgICAgICAgICAgICAgaXRlbS52YWx1ZSA9IHJlc1swXS5jb2RlOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIHRoaXMuc2hvd0RldmljZVRyZWUgPSBmYWxzZTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+36YCJ5oup5Y2V5p2h6K6+5aSH5pWw5o2uIik7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuc2hvd0RldmljZVRyZWUgPSBmYWxzZTsKICAgICAgfQogICAgfSwKICAgIGNsb3NlRGV2aWNlVHlwZURpYWxvZzogZnVuY3Rpb24gY2xvc2VEZXZpY2VUeXBlRGlhbG9nKCkgewogICAgICB0aGlzLnNob3dEZXZpY2VUcmVlID0gZmFsc2U7CiAgICB9LAogICAgLy/lr7zlh7pleGNlbAogICAgZXhwb3J0RXhjZWw6IGZ1bmN0aW9uIGV4cG9ydEV4Y2VsKCkgewogICAgICAvLyBpZighdGhpcy5zZWxlY3REYXRhLmxlbmd0aCA+IDApewogICAgICAvLyAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35Zyo5bem5L6n5Yu+6YCJ6KaB5a+85Ye655qE5pWw5o2uJykKICAgICAgLy8gICByZXR1cm4KICAgICAgLy8gfQogICAgICB2YXIgZmlsZU5hbWUgPSAi6aqM5pS25qCH5YeG5bqTIjsKICAgICAgdmFyIGV4cG9ydFVybCA9ICIvYnpZc2J6emIiOwogICAgICAoMCwgX3lzYnprLmV4cG9ydEV4Y2VsKShleHBvcnRVcmwsIHRoaXMuZmlsdGVyUGFyYW1zLCBmaWxlTmFtZSk7CiAgICB9CiAgfQp9OwpleHBvcnRzLmRlZmF1bHQgPSBfZGVmYXVsdDs="}, {"version": 3, "sources": ["ysbzk.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAmHA;;AACA;;AAMA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,UAAA,EAAA;AAAA,IAAA,UAAA,EAAA,mBAAA;AAAA,IAAA,gBAAA,EAAA,yBAAA;AAAA,IAAA,UAAA,EAAA;AAAA,GADA;AAEA,EAAA,IAAA,EAAA,OAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA,MAAA,YAAA,EAAA,EADA;AAEA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,GAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA,EAFA;AAGA;AACA,UAAA,IAAA,EAAA,EAJA;AAKA,UAAA,IAAA,EAAA,EALA;AAMA,UAAA,EAAA,EAAA,EANA;AAOA,UAAA,IAAA,EAAA;AAPA,SADA;AASA;AACA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,KAHA;AAIA,UAAA,OAAA,EAAA,CACA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WADA,EAEA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAFA,EAGA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAHA,CAJA;AASA,UAAA,SAAA,EAAA;AATA,SADA,EAYA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA,EAJA;AAKA,UAAA,SAAA,EAAA;AALA,SAZA,EAmBA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAnBA,EAoBA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SApBA,EAqBA;AACA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,UAFA;AAGA,UAAA,aAAA,EAAA,EAHA;AAIA,UAAA,KAAA,EAAA,MAJA;AAKA,UAAA,OAAA,EAAA,EALA;AAMA,UAAA,SAAA,EAAA;AANA,SAtBA;AAVA,OAFA;AA4CA,MAAA,WAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,IA5CA;AA6CA;AACA,MAAA,QAAA,EAAA,IA9CA;AA+CA;AACA,MAAA,IAAA,EAAA,CAhDA;AAiDA;AACA,MAAA,QAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,QAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,OAAA,EAAA,IALA;AAMA,QAAA,OAAA,EAAA,EANA;AAOA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AAPA,OADA,EAUA;AACA,QAAA,KAAA,EAAA,SADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,UAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,OAAA,EAAA,IALA;AAMA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AANA,OAVA,EAkBA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,OAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,OAAA,EAAA,IALA;AAMA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AANA,OAlBA,EA0BA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,eAHA;AAIA,QAAA,IAAA,EAAA,KAJA;AAKA,QAAA,OAAA,EAAA,IALA;AAMA,QAAA,OAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAHA,CANA;AAWA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AAXA,OA1BA,EAuCA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,QAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,OAAA,EAAA,IALA;AAMA,QAAA,OAAA,EAAA,EANA;AAOA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AAPA,OAvCA;AAgDA;;;;;;;;;AASA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,OAAA,EAAA,IAJA;AAKA,QAAA,IAAA,EAAA,UALA;AAMA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AANA,OAzDA,EAkEA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,OAAA,EAAA,KAJA;AAKA,QAAA,IAAA,EAAA,OALA;AAMA,QAAA,MAAA,EAAA,KANA;AAOA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA;AAAA;AAPA,OAlEA,CAlDA;AA8HA;AACA,MAAA,WAAA,EAAA,KA/HA;AAgIA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OAjIA;AAsIA;AACA;AACA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,UAAA,EAAA,EAFA;AAGA,UAAA,OAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAJA;AAKA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AALA,SADA;AAQA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,KADA;AAEA,UAAA,YAAA,EAAA;AAFA,SARA;AAYA,QAAA,SAAA,EAAA,EAZA;AAaA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,MAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA;AAbA,OAxIA;AA8JA;AACA,MAAA,mBAAA,EAAA,EA/JA;AAgKA;AACA,MAAA,cAAA,EAAA,KAjKA;AAkKA,MAAA,OAAA,EAAA,EAlKA;AAmKA;AACA,MAAA,WAAA,EAAA;AApKA,KAAA;AAsKA,GA1KA;AA2KA,EAAA,OA3KA,qBA2KA;AACA,SAAA,UAAA;AACA,GA7KA;AA8KA,EAAA,OAAA,EAAA;AACA,IAAA,UADA,4BACA;AAAA,UAAA,MAAA,QAAA,MAAA;AAAA,UAAA,IAAA,QAAA,IAAA;AAAA,UAAA,KAAA,QAAA,KAAA;;AACA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,CAAA,OAAA,CAAA,MAAA,IAAA,CAAA,CAAA,EAAA;AACA,eAAA,YAAA,CAAA,OAAA,GAAA,CAAA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,GAAA,EAAA;AAAA,WAAA,CAAA;AACA,SAFA,MAEA;AACA,eAAA,YAAA,CAAA,OAAA,GAAA,CAAA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,GAAA,EAAA;AAAA,WAAA,CAAA;AACA;AACA;;AACA,WAAA,OAAA;AACA,KAVA;AAWA,IAAA,OAXA,mBAWA,MAXA,EAWA;AAAA;;AACA,WAAA,YAAA,+DAAA,KAAA,YAAA,GAAA,MAAA;AACA,UAAA,KAAA,GAAA,KAAA,YAAA;AACA,8BAAA,KAAA,EAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA;AACA;AACA,QAAA,KAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,QAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,KAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,QAAA,CAAA,IAAA,CAAA,KAAA;;AACA,QAAA,KAAA,CAAA,gBAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,KAAA,CAAA,WAAA,CAAA,OAAA,CAAA,UAAA,OAAA,EAAA;AACA,gBAAA,IAAA,CAAA,IAAA,KAAA,OAAA,CAAA,KAAA,EAAA;AACA,cAAA,IAAA,CAAA,QAAA,GAAA,OAAA,CAAA,KAAA;AACA;AACA,WAJA;AAKA,SANA;;AAOA,YAAA,QAAA,CAAA,IAAA,CAAA,KAAA,GAAA,CAAA,EAAA;AACA,UAAA,KAAA,CAAA,mBAAA,CAAA,QAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AACA;;AACA,QAAA,KAAA,CAAA,WAAA,GAAA,KAAA;AACA,OAhBA;AAiBA,KA/BA;AAgCA,IAAA,WAhCA,uBAgCA,GAhCA,EAgCA;AAAA;;AACA,WAAA,QAAA,CAAA,GAAA;AAAA,0FAAA,iBAAA,IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBACA,IAAA,CAAA,IAAA,KAAA,MADA;AAAA;AAAA;AAAA;;AAEA,kBAAA,IAAA,CAAA,KAAA,GAAA,EAAA;AACA,kBAAA,SAHA,GAGA;AACA,oBAAA,IAAA,EAAA,GAAA,GAAA;AADA,mBAHA;AAAA;AAAA,yBAMA,MAAA,CAAA,uBAAA,CAAA,SAAA,CANA;;AAAA;AAMA,kBAAA,IAAA,CAAA,OANA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAAA;;AAAA;AAAA;AAAA;AAAA;AASA,KA1CA;AA2CA,IAAA,WA3CA,uBA2CA,GA3CA,EA2CA,IA3CA,EA2CA;AAAA;;AACA,UAAA,GAAA,CAAA,KAAA,KAAA,KAAA,IAAA,GAAA,CAAA,KAAA,IAAA,GAAA,CAAA,KAAA,KAAA,EAAA,EAAA;AACA,aAAA,IAAA,CAAA,IAAA,EAAA,MAAA,EAAA,EAAA;AACA,aAAA,UAAA,CAAA,SAAA,CAAA,GAAA;AAAA,4FAAA,kBAAA,IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,0BACA,IAAA,CAAA,KAAA,KAAA,MADA;AAAA;AAAA;AAAA;;AAEA,oBAAA,SAFA,GAEA;AACA,sBAAA,IAAA,EAAA,GAAA,CAAA,KAAA,GAAA;AADA,qBAFA;AAAA;AAAA,2BAKA,MAAA,CAAA,uBAAA,CAAA,SAAA,CALA;;AAAA;AAKA,oBAAA,IAAA,CAAA,OALA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WAAA;;AAAA;AAAA;AAAA;AAAA;AAQA;AACA,KAvDA;;AAwDA;;;AAGA,IAAA,uBA3DA,mCA2DA,SA3DA,EA2DA;AACA,aAAA,qCAAA,SAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,eAAA,GAAA,CAAA,IAAA;AACA,OAFA,CAAA;AAGA,KA/DA;AAgEA;AACA,IAAA,QAjEA,sBAiEA;AACA,WAAA,YAAA,GAAA,EAAA;AACA,WAAA,UAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,KAAA,UAAA,EAAA;AACA,UAAA,IAAA,CAAA,aAAA,GAAA,EAAA;AACA;AACA,OAJA;AAKA,WAAA,KAAA,CAAA,MAAA,CAAA,QAAA;AACA,KAzEA;AA2EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,mBAhGA,+BAgGA,GAhGA,EAgGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA,UAAA,KAAA,OAAA,KAAA,GAAA,EAAA;AACA;AACA;;AACA,WAAA,OAAA,GAAA,GAAA;AACA,WAAA,KAAA,CAAA,MAAA,CAAA,uBAAA,CAAA,CAAA,GAAA,CAAA;AACA,KA/GA;AAgHA;AACA,IAAA,qBAjHA,iCAiHA,GAjHA,EAiHA;AACA,WAAA,mBAAA,GAAA,GAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,KAAA,mBAAA,EAFA,CAGA;AACA,KArHA;AAsHA;AACA,IAAA,YAvHA,wBAuHA,QAvHA,EAuHA;AAAA;;AACA,UAAA,OAAA,GAAA,EAAA;;AACA,UAAA,QAAA,CAAA,EAAA,KAAA,EAAA,IAAA,CAAA,QAAA,CAAA,EAAA,EAAA;AACA,QAAA,OAAA,GAAA,MAAA;AACA,OAFA,MAEA;AACA,QAAA,OAAA,GAAA,MAAA;AACA;;AACA,uCAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,OAAA;;AACA,UAAA,MAAA,CAAA,OAAA;AACA,SAHA,MAGA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,OAPA;AAQA,KAtIA;AAuIA;AACA,IAAA,WAxIA,yBAwIA;AAAA;;AACA,WAAA,QAAA,GAAA,IAAA;AACA,UAAA,OAAA,GAAA,KAAA,QAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,MAAA,CAAA,WAAA;AACA;;AACA,eAAA,IAAA;AACA,OALA,CAAA;AAMA,WAAA,KAAA,CAAA,UAAA,CAAA,OAAA,CAAA,OAAA;AACA,KAjJA;AAkJA;AACA,IAAA,cAnJA,0BAmJA,GAnJA,EAmJA;AAAA;;AACA,WAAA,WAAA,CAAA,GAAA,CAAA,GAAA;AACA,UAAA,UAAA,GAAA,KAAA,QAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;;AACA,YAAA,IAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,MAAA,CAAA,WAAA;AACA;;AACA,eAAA,IAAA;AACA,OANA,CAAA;AAOA,WAAA,QAAA,GAAA,IAAA;AACA,WAAA,KAAA,CAAA,UAAA,CAAA,OAAA,CAAA,UAAA;AACA,KA9JA;AA+JA;AACA,IAAA,YAhKA,wBAgKA,GAhKA,EAgKA;AAAA;;AACA,WAAA,WAAA,CAAA,GAAA,CAAA,GAAA;AACA,UAAA,QAAA,GAAA,KAAA,QAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;;AACA,YAAA,IAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,MAAA,CAAA,WAAA;AACA;;AACA,eAAA,IAAA;AACA,OANA,CAAA;AAOA,WAAA,QAAA,GAAA,IAAA;AACA,WAAA,KAAA,CAAA,UAAA,CAAA,MAAA,CAAA,QAAA;AACA,KA3KA;AA4KA;AACA,IAAA,cA7KA,0BA6KA,GA7KA,EA6KA;AAAA;;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,QAAA,CAAA,iBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAKA,IALA,CAKA,YAAA;AACA,mCAAA,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA,CAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,OAAA,EAAA,MADA;AAEA,cAAA,IAAA,EAAA;AAFA,aAAA;AAIA,WALA,MAKA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;;AACA,UAAA,MAAA,CAAA,OAAA;AACA,SAVA;AAWA,OAjBA,EAkBA,KAlBA,CAkBA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OAvBA;AAwBA,KAvMA;AAwMA;AACA,IAAA,UAzMA,wBAyMA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,2BAAA,MAAA,CADA;;AAAA;AAAA;AACA,gBAAA,IADA,yBACA,IADA;AAEA,gBAAA,MAAA,CAAA,WAAA,GAAA,IAAA;;AACA,gBAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,sBAAA,IAAA,CAAA,KAAA,KAAA,MAAA,EAAA;AACA,2BAAA,IAAA,CAAA,OAAA,GAAA,IAAA;AACA;AACA,iBAJA;;AAKA,gBAAA,MAAA,CAAA,OAAA;;AARA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,KAlNA;AAoNA;AACA,IAAA,eArNA,2BAqNA,GArNA,EAqNA;AACA,WAAA,QAAA,GAAA,KAAA;;AACA,UAAA,GAAA,CAAA,MAAA,CAAA,IAAA,KAAA,QAAA,EAAA;AACA,aAAA,cAAA,GAAA,IAAA;AACA;;AAEA,UAAA,GAAA,CAAA,MAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,aAAA,cAAA,GAAA,IAAA;AACA,aAAA,QAAA,GAAA,IAAA;AACA;AACA,KA/NA;AAgOA;AACA,IAAA,iBAjOA,6BAiOA,GAjOA,EAiOA;AACA,UAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,YAAA,KAAA,QAAA,EAAA;AACA,cAAA,IAAA,GAAA,EAAA;AACA,cAAA,MAAA,GAAA,EAAA;AACA,UAAA,GAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,MAAA,IAAA,IAAA,CAAA,IAAA,GAAA,GAAA;AACA,YAAA,IAAA,IAAA,IAAA,CAAA,IAAA,GAAA,GAAA;AACA,WAHA;AAIA,eAAA,UAAA,CAAA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA,IAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA,eAAA,UAAA,CAAA,IAAA,CAAA,MAAA,GAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,MAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA,eAAA,cAAA,GAAA,KAAA;AACA,SAVA,MAUA;AACA,cAAA,GAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,iBAAA,QAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,kBAAA,IAAA,CAAA,IAAA,KAAA,QAAA,EAAA;AACA,gBAAA,IAAA,CAAA,KAAA,GAAA,GAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA;;AACA,kBAAA,IAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,gBAAA,IAAA,CAAA,KAAA,GAAA,GAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA;AACA,aAPA;AAQA,iBAAA,cAAA,GAAA,KAAA;AACA,WAVA,MAUA;AACA,iBAAA,QAAA,CAAA,OAAA,CAAA,WAAA;AACA;AACA;AACA,OA1BA,MA0BA;AACA,aAAA,cAAA,GAAA,KAAA;AACA;AACA,KA/PA;AAgQA,IAAA,qBAhQA,mCAgQA;AACA,WAAA,cAAA,GAAA,KAAA;AACA,KAlQA;AAmQA;AACA,IAAA,WApQA,yBAoQA;AACA;AACA;AACA;AACA;AACA,UAAA,QAAA,GAAA,OAAA;AACA,UAAA,SAAA,GAAA,WAAA;AACA,8BAAA,SAAA,EAAA,KAAA,YAAA,EAAA,QAAA;AACA;AA5QA;AA9KA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row>\n      <el-filter\n        :data=\"filterInfo.data\"\n        :field-list=\"filterInfo.fieldList\"\n        :width=\"{ labelWidth: 80, itemWidth: 230 }\"\n        @onfocusEvent=\"inputFocusEvent\"\n        @handleReset=\"getReset\"\n        @handleEvent=\"handleEvent\"\n      />\n      <!--右侧列表-->\n      <el-col :span=\"24\">\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button\n              type=\"primary\"\n              icon=\"el-icon-plus\"\n              v-hasPermi=\"['bzysbzk:button:add']\"\n              @click=\"addMainData\"\n              >新增</el-button\n            >\n            <el-button\n              type=\"success\"\n              icon=\"el-icon-download\"\n              @click=\"exportExcel\"\n              >导出</el-button\n            >\n          </div>\n          <div class=\"button_btn\">验收标准库</div>\n          <comp-table\n            ref=\"maintable\"\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            @sort-events=\"sortChange\"\n            @rowClick=\"handleCurrentChange\"\n            v-loading=\"mainLoading\"\n            height=\"70.2vh\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              :resizable=\"false\"\n              width=\"120\"\n            >\n              <!-- <el-table-column label=\"操作\" fixed=\"right\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"180\"> -->\n              <template slot-scope=\"scope\">\n                <el-button\n                  type=\"text\"\n                  v-hasPermi=\"['bzysbzk:button:update']\"\n                  @click.stop=\"updateMainData(scope.row)\"\n                  class=\"updateBtn el-icon-edit\"\n                  title=\"修改\"\n                >\n                </el-button>\n                <el-button\n                  type=\"text\"\n                  @click.stop=\"showMainData(scope.row)\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n                <el-button\n                  type=\"text\"\n                  v-hasPermi=\"['bzysbzk:button:delete']\"\n                  @click.stop=\"deleteMainData(scope.row)\"\n                  title=\"删除\"\n                  class=\"el-icon-delete\"\n                >\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n          <!-- </el-table> -->\n          <!-- <pagination v-show=\"mainParanms.total>0\" :total=\"mainParanms.total\" :page.sync=\"mainParanms.pageNum\"\n            :limit.sync=\"mainParanms.pageSize\" @pagination=\"getMainStandardData\" /> -->\n        </el-white>\n        <el-white>\n          <acceptance-detail ref=\"detail\"></acceptance-detail>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <dialog-form\n      ref=\"dialogForm\"\n      label-width=\"150px\"\n      out-width=\"50%\"\n      :reminder=\"reminder\"\n      :rows=\"rows\"\n      @onfocusEvent=\"inputFocusEvent\"\n      @save=\"saveMainData\"\n      @inputChange1=\"getSblxList\"\n    />\n\n    <el-dialog\n      :append-to-body=\"true\"\n      title=\"设备分类\"\n      :visible.sync=\"showDeviceTree\"\n      width=\"400px\"\n      v-if=\"showDeviceTree\"\n      v-dialogDrag\n    >\n      <device-tree\n        @getDeviceTypeData=\"getDeviceTypeData\"\n        @closeDeviceTypeDialog=\"closeDeviceTypeDialog\"\n      >\n      </device-tree>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport dialogForm from \"com/dialogFrom/dialogForm\";\nimport {\n  deleteBzYsbzzb,\n  getBzYsbzzb,\n  saveOrUpdateBzYsbzzb,\n  exportExcel\n} from \"@/api/bzgl/ysbzk/ysbzk\";\nimport AcceptanceDetail from \"@/views/dagangOilfield/bzgl/ysbzkgl/acceptanceDetail\";\nimport DeviceTree from \"@/views/dagangOilfield/bzgl/sbbzk/deviceTree\";\nimport { getDictTypeData } from \"@/api/system/dict/data\";\nimport { getSblxDataListSelected } from \"@/api/dagangOilfield/asset/bdsbtz\";\n\nexport default {\n  components: { DeviceTree, AcceptanceDetail, dialogForm },\n  name: \"ysbzk\",\n  data() {\n    return {\n      filterParams: {},\n      filterInfo: {\n        data: {\n          spb: \"\",\n          sbfl: \"\",\n          // sbflmc: \"\"\n          jxfl: \"\",\n          xmmc: \"\",\n          bz: \"\",\n          yslx: \"\"\n        }, //查询条件\n        fieldList: [\n          {\n            label: \"所属专业\",\n            type: \"select\",\n            value: \"spb\",\n            options: [\n              { label: \"变电\", value: \"变电\" },\n              { label: \"配电\", value: \"配电\" },\n              { label: \"输电\", value: \"输电\" }\n            ],\n            clearable: true\n          },\n          {\n            label: \"设备分类\",\n            type: \"select\",\n            value: \"sbfl\",\n            options: [],\n            clearable: true\n          },\n          { label: \"验收名称\", type: \"input\", value: \"jxfl\" },\n          { label: \"项目名称\", type: \"input\", value: \"xmmc\" },\n          // { label: '备注', type: 'input', value: 'bz' },\n          {\n            label: \"验收类型\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"yslx\",\n            options: [],\n            clearable: true\n          }\n        ]\n      },\n      currentUser: this.$store.getters.name,\n      //新增或修改标题\n      reminder: \"修改\",\n      //弹出框form表单每行显示几个\n      rows: 2,\n      //主表新增表单数据\n      formList: [\n        {\n          label: \"验收类型：\",\n          value: \"\",\n          type: \"select\",\n          name: \"yslx\",\n          default: true,\n          options: [],\n          rules: { required: true, message: \"请输入验收类型\" }\n        },\n        {\n          label: \"验收标准名称：\",\n          value: \"\",\n          type: \"textarea\",\n          name: \"jxfl\",\n          default: true,\n          rules: { required: true, message: \"请输入验收标准名称\" }\n        },\n        {\n          label: \"项目名称：\",\n          value: \"\",\n          type: \"input\",\n          name: \"xmmc\",\n          default: true,\n          rules: { required: true, message: \"请输入项目名称\" }\n        },\n        {\n          label: \"所属专业：\",\n          value: \"\",\n          type: \"selectChange1\",\n          name: \"spb\",\n          default: true,\n          options: [\n            { label: \"变电\", value: \"变电\" },\n            { label: \"配电\", value: \"配电\" },\n            { label: \"输电\", value: \"输电\" }\n          ],\n          rules: { required: true, message: \"请输入验收类型\" }\n        },\n        {\n          label: \"设备分类：\",\n          value: \"\",\n          type: \"select\",\n          name: \"sbfl\",\n          default: true,\n          options: [],\n          rules: { required: true, message: \"请输入设备分类\" }\n        },\n        /* {\n          label: '设备分类：',\n          value: '',\n          type: 'input',\n          name: 'sbfl',\n          default: true,\n          hidden: false,\n          rules: { required: true, message: '请输入设备分类' }\n        },*/\n        {\n          label: \"备注：\",\n          value: \"\",\n          name: \"bz\",\n          default: true,\n          type: \"textarea\",\n          rules: { required: false, message: \"请输入备注\" }\n        },\n\n        {\n          label: \"主键id:\",\n          value: \"\",\n          name: \"id\",\n          default: false,\n          type: \"input\",\n          hidden: false,\n          rules: { required: false }\n        }\n      ],\n      //主表加载\n      mainLoading: false,\n      //验收标准主表查询条件\n      mainParanms: {\n        pageNum: 1,\n        pageSize: 10,\n        total: 0\n      },\n      //验收标准主表数据\n      // mainTableData: [],\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageResize: '',\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: false,\n          serialNumber: false\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: 'yslxName', label: '验收类型',minWidth: '80'},\n          { prop: 'spb', label: '所属专业',minWidth: '80', custom: true},\n          { prop: 'sbflmc', label: '设备分类',minWidth: '80'},\n          { prop: 'jxfl', label: '验收名称',minWidth: '220'},\n          { prop: 'xmmc', label: '项目名称',minWidth: '100'},\n          { prop: 'bz', label: '备注',minWidth: '80'},\n        ]\n      },\n      //主表选中行数据\n      mainTableSelectRows: [],\n      //是否展示设备分类树\n      showDeviceTree: false,\n      valFlag: \"\",\n      //验收类型下拉框数据\n      yslxOptions: []\n    };\n  },\n  mounted() {\n    this.initDomain();\n  },\n  methods: {\n    sortChange({ column, prop, order }) {\n      if (order) {\n        if (order.indexOf(\"desc\") > -1) {\n          this.filterParams.mySorts = [{ prop: prop, asc: false }];\n        } else {\n          this.filterParams.mySorts = [{ prop: prop, asc: true }];\n        }\n      }\n      this.getData();\n    },\n    getData(params) {\n      this.filterParams = { ...this.filterParams, ...params };\n      const param = this.filterParams;\n      getBzYsbzzb(param).then(response => {\n        // this.mainTableData = response.data.records;\n        // this.mainParanms.total = response.data.total;\n        this.tableAndPageInfo.tableData = response.data.records;\n        this.tableAndPageInfo.pager.total = response.data.total\n        this.tableAndPageInfo.tableData.forEach(item => {\n          this.yslxOptions.forEach(element => {\n            if (item.yslx === element.value) {\n              item.yslxName = element.label;\n            }\n          });\n        });\n        if (response.data.total > 0) {\n          this.handleCurrentChange(response.data.records[0]);\n        }\n        this.mainLoading = false;\n      });\n    },\n    getSblxList(val) {\n      this.formList.map(async item => {\n        if (item.name === \"sbfl\") {\n          item.value = \"\";\n          let sblxParam = {\n            type: val + \"设备\"\n          };\n          item.options = await this.getSblxDataListSelected(sblxParam);\n        }\n      });\n    },\n    handleEvent(val, val1) {\n      if (val.label === \"spb\" && val.value && val.value !== \"\") {\n        this.$set(val1, \"sbfl\", \"\");\n        this.filterInfo.fieldList.map(async item => {\n          if (item.value === \"sbfl\") {\n            let sblxParam = {\n              type: val.value + \"设备\"\n            };\n            item.options = await this.getSblxDataListSelected(sblxParam);\n          }\n        });\n      }\n    },\n    /**\n     * 获取设备类型下拉框数据\n     */\n    getSblxDataListSelected(sblxParam) {\n      return getSblxDataListSelected(sblxParam).then(res => {\n        return res.data;\n      });\n    },\n    //重置按钮\n    getReset() {\n      this.filterParams = {};\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n      this.$refs.detail.getReset();\n    },\n\n    //获取验收标准主表数据\n    // getMainStandardData() {\n    //   this.mainLoading = true;\n    //   const param = { ...this.filterParams, ...this.mainParanms };\n    //   getBzYsbzzb(param).then(response => {\n    //     this.mainTableData = response.data.records;\n    //     this.mainParanms.total = response.data.total;\n    //     this.mainTableData.forEach(item => {\n    //       this.yslxOptions.forEach(element => {\n    //         if (item.yslx === element.value) {\n    //           item.yslxName = element.label;\n    //         }\n    //       });\n    //     });\n    //     if (response.data.total > 0) {\n    //       this.handleCurrentChange(response.data.records[0]);\n    //     }\n    //     this.mainLoading = false;\n    //   });\n    // },\n    //验收标准主表行点击事件逻辑\n    handleCurrentChange(val) {\n      // this.selectData = []\n      // 清空所有选择\n      // this.$refs.maintable.clearSelection();\n      //  选中当前选择\n      // this.$refs.maintable.toggleRowSelection(val);\n      // this.selectData.push(val)\n      // this.$refs.maintable.setCurrentRow(val);\n\n      //给子组件传值\n      if (this.valFlag === val) {\n        return;\n      }\n      this.valFlag = val;\n      this.$refs.detail.getMainTableSelectedRow([val]);\n    },\n    //复选框选中逻辑\n    handleSelectionChange(val) {\n      this.mainTableSelectRows = val;\n      console.log(this.mainTableSelectRows);\n      // this.$refs.detail.getMainTableSelectedRow(val)\n    },\n    //保存主表数据\n    saveMainData(formData) {\n      let message = \"\";\n      if (formData.id === \"\" || !formData.id) {\n        message = \"新增成功\";\n      } else {\n        message = \"修改成功\";\n      }\n      saveOrUpdateBzYsbzzb(formData).then(res => {\n        if (res.code === \"0000\") {\n          this.$message.success(message);\n          this.getData();\n        } else {\n          this.$message.error(\"操作失败\");\n        }\n      });\n    },\n    //新增主表数据\n    addMainData() {\n      this.reminder = \"新增\";\n      const addForm = this.formList.map(item => {\n        if (item.name === \"yslx\") {\n          item.options = this.yslxOptions;\n        }\n        return item;\n      });\n      this.$refs.dialogForm.showzzc(addForm);\n    },\n    //修改主表数据\n    updateMainData(row) {\n      this.getSblxList(row.spb);\n      const updateList = this.formList.map(item => {\n        item.value = row[item.name];\n        if (item.name === \"yslx\") {\n          item.options = this.yslxOptions;\n        }\n        return item;\n      });\n      this.reminder = \"修改\";\n      this.$refs.dialogForm.showzzc(updateList);\n    },\n    //查看主表数据详情\n    showMainData(row) {\n      this.getSblxList(row.spb);\n      const infoList = this.formList.map(item => {\n        item.value = row[item.name];\n        if (item.name === \"yslx\") {\n          item.options = this.yslxOptions;\n        }\n        return item;\n      });\n      this.reminder = \"详情\";\n      this.$refs.dialogForm.showxq(infoList);\n    },\n    //删除主表数据\n    deleteMainData(row) {\n      this.form = { ...row };\n      this.$confirm(\"确认删除选中数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          deleteBzYsbzzb([this.form.id]).then(res => {\n            if (res.code === \"0000\") {\n              this.$message({\n                message: \"删除成功\",\n                type: \"success\"\n              });\n            } else {\n              this.$message.error(\"操作失败\");\n            }\n            this.getData();\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    //初始话下拉框数据\n    async initDomain() {\n      let { data: yslx } = await getDictTypeData(\"yslx\");\n      this.yslxOptions = yslx;\n      this.filterInfo.fieldList.map(item => {\n        if (item.value === \"yslx\") {\n          return (item.options = yslx);\n        }\n      });\n      this.getData();\n    },\n\n    //input输入框鼠标聚焦事件\n    inputFocusEvent(val) {\n      this.isFilter = false;\n      if (val.target.name === \"sbflmc\") {\n        this.showDeviceTree = true;\n      }\n\n      if (val.target.name === \"sbfl\") {\n        this.showDeviceTree = true;\n        this.isFilter = true;\n      }\n    },\n    //获取设备分类树数据\n    getDeviceTypeData(res) {\n      if (res.length > 0) {\n        if (this.isFilter) {\n          let sbfl = \"\";\n          let sbflmc = \"\";\n          res.forEach(item => {\n            sbflmc += item.name + \",\";\n            sbfl += item.code + \",\";\n          });\n          this.filterInfo.data.sbfl = sbfl.substring(0, sbfl.length - 1);\n          this.filterInfo.data.sbflmc = sbflmc.substring(0, sbflmc.length - 1);\n          this.showDeviceTree = false;\n        } else {\n          if (res.length === 1) {\n            this.formList.forEach(item => {\n              if (item.name === \"sbflmc\") {\n                item.value = res[0].name;\n              }\n              if (item.name === \"sbfl\") {\n                item.value = res[0].code;\n              }\n            });\n            this.showDeviceTree = false;\n          } else {\n            this.$message.warning(\"请选择单条设备数据\");\n          }\n        }\n      } else {\n        this.showDeviceTree = false;\n      }\n    },\n    closeDeviceTypeDialog() {\n      this.showDeviceTree = false;\n    },\n    //导出excel\n    exportExcel() {\n      // if(!this.selectData.length > 0){\n      //   this.$message.warning('请在左侧勾选要导出的数据')\n      //   return\n      // }\n      let fileName = \"验收标准库\";\n      let exportUrl = \"/bzYsbzzb\";\n      exportExcel(exportUrl, this.filterParams, fileName);\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  height: calc(100vh - 225px);\n  overflow: auto;\n}\n\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.box-cardList {\n  height: 56%;\n}\n\n.item {\n  width: 200px;\n  float: left;\n}\n\n#main_container_dj {\n  height: calc(100vh - 84px);\n}\n\n.aside_height {\n  height: 96%;\n}\n\n.defect .el-form-item:nth-child(odd) {\n  margin-right: 70px;\n}\n\n/*背景颜色调整*/\n#main_container_dj,\n#main_container_dj .el-aside {\n  background-color: #b4caf1;\n}\n\n/deep/ #qxlr_dialog_insert .el-dialog__header {\n  background-color: #8eb3f5;\n}\n\n/deep/ .pmyBtn {\n  background: #8eb3f5;\n}\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl"}]}