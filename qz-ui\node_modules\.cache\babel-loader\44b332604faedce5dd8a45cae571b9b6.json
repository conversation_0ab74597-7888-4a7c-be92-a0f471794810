{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\sbztpjbzk\\ztlmxwh.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\sbztpjbzk\\ztlmxwh.js", "mtime": 1706897314226}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0UGFnZUxpc3QgPSBnZXRQYWdlTGlzdDsKZXhwb3J0cy5zYXZlT3JVcGRhdGUgPSBzYXZlT3JVcGRhdGU7CmV4cG9ydHMucmVtb3ZlID0gcmVtb3ZlOwpleHBvcnRzLmdldFNibHhBbmRTYmJqVHJlZSA9IGdldFNibHhBbmRTYmJqVHJlZTsKZXhwb3J0cy5nZXRQamd6VHJlZSA9IGdldFBqZ3pUcmVlOwoKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Cgp2YXIgYmFzZVVybCA9ICIvY29uZGl0aW9uLW1haW50ZW5hbmNlLWFwaSI7CgpmdW5jdGlvbiBnZXRQYWdlTGlzdChwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy96dGxteC9nZXRNd3RDYm1adGxteExpc3QnLCBwYXJhbXMsIDEpOwp9IC8vIOa3u+WKoOaIluS/ruaUuQoKCmZ1bmN0aW9uIHNhdmVPclVwZGF0ZShwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy96dGxteC9zYXZlT3JVcGRhdGUnLCBwYXJhbXMsIDEpOwp9IC8vIOWIoOmZpAoKCmZ1bmN0aW9uIHJlbW92ZShwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy96dGxteC9kZWxldGVadGxteCcsIHBhcmFtcywgMSk7Cn0KLyoqCiAqIOW3puS+p+agkeW9oue7k+aehAogKiBAcmV0dXJucwogKi8KCgpmdW5jdGlvbiBnZXRTYmx4QW5kU2JialRyZWUocXVlcnkpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0R2V0KGJhc2VVcmwgKyAnL213dENibVNiYmovZ2V0U2JseEFuZFNiYmonLCBxdWVyeSwgMik7Cn0KLyoqCiAqIOivhOS7t+inhOWImeW3puS+p+agkeW9oue7k+aehAogKiBAcmV0dXJucwogKi8KCgpmdW5jdGlvbiBnZXRQamd6VHJlZShxdWVyeSkgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3RHZXQoYmFzZVVybCArICcvbXd0Q2JtU2Jiai9nZXRQamd6VHJlZScsIHF1ZXJ5LCAyKTsKfQ=="}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagang<PERSON><PERSON>/bzgl/sbztpjbzk/ztlmxwh.js"], "names": ["baseUrl", "getPageList", "params", "api", "requestPost", "saveOrUpdate", "remove", "getSblxAndSbbjTree", "query", "requestGet", "getPjgzTree"], "mappings": ";;;;;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,4BAAhB;;AAEO,SAASC,WAAT,CAAqBC,MAArB,EAA6B;AAClC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,2BAAxB,EAAoDE,MAApD,EAA2D,CAA3D,CAAP;AACD,C,CAED;;;AACO,SAASG,YAAT,CAAsBH,MAAtB,EAA8B;AACnC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,qBAAxB,EAA8CE,MAA9C,EAAqD,CAArD,CAAP;AACD,C,CACA;;;AACM,SAASI,MAAT,CAAgBJ,MAAhB,EAAwB;AAC7B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,oBAAxB,EAA6CE,MAA7C,EAAoD,CAApD,CAAP;AACD;AAID;;;;;;AAIQ,SAASK,kBAAT,CAA4BC,KAA5B,EAAmC;AACzC,SAAOL,iBAAIM,UAAJ,CAAeT,OAAO,GAAC,4BAAvB,EAAoDQ,KAApD,EAA0D,CAA1D,CAAP;AACD;AAED;;;;;;AAIQ,SAASE,WAAT,CAAqBF,KAArB,EAA4B;AAClC,SAAOL,iBAAIM,UAAJ,CAAeT,OAAO,GAAC,yBAAvB,EAAiDQ,KAAjD,EAAuD,CAAvD,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\nconst baseUrl = \"/condition-maintenance-api\";\n\nexport function getPageList(params) {\n  return api.requestPost(baseUrl+'/ztlmx/getMwtCbmZtlmxList',params,1)\n}\n\n// 添加或修改\nexport function saveOrUpdate(params) {\n  return api.requestPost(baseUrl+'/ztlmx/saveOrUpdate',params,1)\n}\n // 删除\nexport function remove(params) {\n  return api.requestPost(baseUrl+'/ztlmx/deleteZtlmx',params,1)\n}\n\n\n\n/**\n * 左侧树形结构\n * @returns\n */\n export function getSblxAndSbbjTree(query) {\n  return api.requestGet(baseUrl+'/mwtCbmSbbj/getSblxAndSbbj',query,2)\n}\n\n/**\n * 评价规则左侧树形结构\n * @returns\n */\n export function getPjgzTree(query) {\n  return api.requestGet(baseUrl+'/mwtCbmSbbj/getPjgzTree',query,2)\n}\n"]}]}