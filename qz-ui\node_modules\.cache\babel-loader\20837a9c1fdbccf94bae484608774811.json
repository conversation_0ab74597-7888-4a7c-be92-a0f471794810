{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\technicalAndPart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\technicalAndPart.vue", "mtime": 1706897322897}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuZnVuY3Rpb24ubmFtZSIpOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Cgp2YXIgX3RlY2huaWNhbFBhcmFtZXRlciA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC92aWV3cy9kYWdhbmdPaWxmaWVsZC9iemdsL3NiYnprL3RlY2huaWNhbFBhcmFtZXRlciIpKTsKCnZhciBfZXF1aXBtZW50Q29tcG9uZW50cyA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC92aWV3cy9kYWdhbmdPaWxmaWVsZC9iemdsL3NiYnprL2VxdWlwbWVudENvbXBvbmVudHMiKSk7CgovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSB7CiAgcHJvcHM6IHsKICAgIGRldmljZVR5cGVEYXRhOiB7CiAgICAgIHR5cGU6IE9iamVjdAogICAgfQogIH0sCiAgbmFtZTogJ3RlY2huaWNhbEFuZFBhcnQnLAogIGNvbXBvbmVudHM6IHsKICAgIEVxdWlwbWVudENvbXBvbmVudHM6IF9lcXVpcG1lbnRDb21wb25lbnRzLmRlZmF1bHQsCiAgICBUZWNobmljYWxQYXJhbWV0ZXI6IF90ZWNobmljYWxQYXJhbWV0ZXIuZGVmYXVsdAogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGFjdGl2ZU5hbWU6ICdmaXJzdCcsCiAgICAgIHRhYnNJbmRleDogMQogICAgfTsKICB9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7fSwKICBtZXRob2RzOiB7CiAgICAvL3RhYuS5n+WIh+aNoueCueWHu+S6i+S7tgogICAgaGFuZGxlQ2xpY2s6IGZ1bmN0aW9uIGhhbmRsZUNsaWNrKHRhYikgewogICAgICB0aGlzLnRhYnNJbmRleCA9IHRhYi5uYW1lID09PSAnZmlyc3QnID8gMSA6IDI7CiAgICB9LAogICAgLy/lhbPpl63lvLnnqpcKICAgIGNsb3NlRGlhbG9nOiBmdW5jdGlvbiBjbG9zZURpYWxvZygpIHsKICAgICAgdGhpcy4kZW1pdCgnY2xvc2VQYXJhbURpYWxvZycpOwogICAgfQogIH0KfTsKZXhwb3J0cy5kZWZhdWx0ID0gX2RlZmF1bHQ7"}, {"version": 3, "sources": ["technicalAndPart.vue"], "names": [], "mappings": ";;;;;;;;;;;AAiBA;;AACA;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,KAAA,EAAA;AACA,IAAA,cAAA,EAAA;AACA,MAAA,IAAA,EAAA;AADA;AADA,GADA;AAMA,EAAA,IAAA,EAAA,kBANA;AAOA,EAAA,UAAA,EAAA;AAAA,IAAA,mBAAA,EAAA,4BAAA;AAAA,IAAA,kBAAA,EAAA;AAAA,GAPA;AAQA,EAAA,IARA,kBAQA;AACA,WAAA;AACA,MAAA,UAAA,EAAA,OADA;AAEA,MAAA,SAAA,EAAA;AAFA,KAAA;AAIA,GAbA;AAcA,EAAA,OAdA,qBAcA,CACA,CAfA;AAgBA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,WAFA,uBAEA,GAFA,EAEA;AACA,WAAA,SAAA,GAAA,GAAA,CAAA,IAAA,KAAA,OAAA,GAAA,CAAA,GAAA,CAAA;AACA,KAJA;AAKA;AACA,IAAA,WANA,yBAMA;AACA,WAAA,KAAA,CAAA,kBAAA;AACA;AARA;AAhBA,C", "sourcesContent": ["<template>\n  <div>\n    <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\n      <el-tab-pane label=\"技术参数\" name=\"first\">\n        <technical-parameter ref=\"technicalParameter\" v-if=\"tabsIndex === 1 \" :device-type-data=\"deviceTypeData\"/>\n      </el-tab-pane>\n      <el-tab-pane label=\"设备部件\" name=\"second\">\n        <equipment-components ref=\"equipmentComponents\" v-if=\"tabsIndex === 2 \" :device-type-data=\"deviceTypeData\"/>\n      </el-tab-pane>\n    </el-tabs>\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"closeDialog\" style=\"margin-left: 92%;margin-top: 1%;\">取 消</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport TechnicalParameter from '@/views/dagangOilfield/bzgl/sbbzk/technicalParameter'\nimport EquipmentComponents from '@/views/dagangOilfield/bzgl/sbbzk/equipmentComponents'\n\nexport default {\n  props: {\n    deviceTypeData: {\n      type: Object\n    }\n  },\n  name: 'technicalAndPart',\n  components: { EquipmentComponents, TechnicalParameter },\n  data() {\n    return {\n      activeName: 'first',\n      tabsIndex: 1\n    }\n  },\n  mounted() {\n  },\n  methods: {\n    //tab也切换点击事件\n    handleClick(tab) {\n      this.tabsIndex = tab.name === 'first' ? 1 : 2\n    },\n    //关闭弹窗\n    closeDialog() {\n      this.$emit('closeParamDialog')\n    }\n  }\n}\n</script>\n\n<style scoped>\n\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbbzk"}]}