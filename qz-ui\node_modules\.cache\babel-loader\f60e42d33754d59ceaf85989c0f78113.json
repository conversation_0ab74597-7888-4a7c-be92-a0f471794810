{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\DictTag\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\DictTag\\index.vue", "mtime": 1706897320305}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm51bWJlci5jb25zdHJ1Y3RvciIpOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IHsKICBuYW1lOiAiRGljdFRhZyIsCiAgcHJvcHM6IHsKICAgIG9wdGlvbnM6IHsKICAgICAgdHlwZTogQXJyYXksCiAgICAgIGRlZmF1bHQ6IG51bGwKICAgIH0sCiAgICB2YWx1ZTogW051bWJlciwgU3RyaW5nLCBBcnJheV0KICB9LAogIGNvbXB1dGVkOiB7CiAgICB2YWx1ZXM6IGZ1bmN0aW9uIHZhbHVlcygpIHsKICAgICAgaWYgKHRoaXMudmFsdWUgIT09IG51bGwgJiYgdHlwZW9mIHRoaXMudmFsdWUgIT09ICd1bmRlZmluZWQnKSB7CiAgICAgICAgcmV0dXJuIEFycmF5LmlzQXJyYXkodGhpcy52YWx1ZSkgPyB0aGlzLnZhbHVlIDogW1N0cmluZyh0aGlzLnZhbHVlKV07CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuIFtdOwogICAgICB9CiAgICB9CiAgfQp9OwpleHBvcnRzLmRlZmF1bHQgPSBfZGVmYXVsdDs="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;eAeA;AACA,EAAA,IAAA,EAAA,SADA;AAEA,EAAA,KAAA,EAAA;AACA,IAAA,OAAA,EAAA;AACA,MAAA,IAAA,EAAA,KADA;AAEA,MAAA,OAAA,EAAA;AAFA,KADA;AAKA,IAAA,KAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,KAAA;AALA,GAFA;AASA,EAAA,QAAA,EAAA;AACA,IAAA,MADA,oBACA;AACA,UAAA,KAAA,KAAA,KAAA,IAAA,IAAA,OAAA,KAAA,KAAA,KAAA,WAAA,EAAA;AACA,eAAA,KAAA,CAAA,OAAA,CAAA,KAAA,KAAA,IAAA,KAAA,KAAA,GAAA,CAAA,MAAA,CAAA,KAAA,KAAA,CAAA,CAAA;AACA,OAFA,MAEA;AACA,eAAA,EAAA;AACA;AACA;AAPA;AATA,C", "sourcesContent": ["<template>\r\n  <div>\r\n    <template v-for=\"(item, index) in options\">\r\n      <template v-if=\"values.includes(item.value)\">\r\n        <span\r\n          :key=\"item.value\"\r\n          :index=\"index\"\r\n          >{{ item.label }}</span\r\n        >\r\n      </template>\r\n    </template>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"DictTag\",\r\n  props: {\r\n    options: {\r\n      type: Array,\r\n      default: null,\r\n    },\r\n    value: [Number, String, Array],\r\n  },\r\n  computed: {\r\n    values() {\r\n      if (this.value !== null && typeof this.value !== 'undefined') {\r\n        return Array.isArray(this.value) ? this.value : [String(this.value)];\r\n      } else {\r\n        return [];\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.el-tag + .el-tag {\r\n  margin-left: 10px;\r\n}\r\n</style>"], "sourceRoot": "src/components/DictTag"}]}