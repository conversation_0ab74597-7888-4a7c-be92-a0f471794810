{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\userSelect\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\userSelect\\index.vue", "mtime": 1706897321243}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCgppbXBvcnQge2RlcHRUcmVlU2VsZWN0LGdyb3VwVHJlZVNlbGVjdCxVc2Vyc0J5RGVwdE9yR3JvdXB9IGZyb20gIkAvYXBpL2NvbXBvbmVudC91c2VyU2VsZWN0IjsKaW1wb3J0IGVtaXR0ZXIgZnJvbSAnZWxlbWVudC11aS9zcmMvbWl4aW5zL2VtaXR0ZXInOwoKLyoqCiAqIOeUqOaIt+mAieaLqeWZqAogKi8KZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdVc2VyU2VsZWN0JywKICBtb3VudGVkKCkgewogICAgdGhpcy5yZXNldElucHV0SGVpZ2h0KCk7CiAgICBpZiAodGhpcy5jbGVhcmFibGUpIHsKICAgICAgdGhpcy5pbnB1dFdpZHRoID0gdGhpcy4kcmVmcy5zZWxlY3QuY2xpZW50V2lkdGg7CiAgICB9CiAgICB0aGlzLmluaXRUcmVlKCkKICB9LAogIHVwZGF0ZWQoKSB7CiAgICB0aGlzLnJlc2V0SW5wdXRIZWlnaHQoKTsKICB9LAogIG1peGluczogW2VtaXR0ZXJdLAogIGNvbXBvbmVudHM6IHt9LAogIHByb3BzOiB7CiAgICBwbGFjZWhvbGRlcjogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICfor7fpgInmi6nkurrlkZgnCiAgICB9LAogICAgLy/mmK/lkKblpJrpgIkKICAgIG11bHRpcGxlOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlLAogICAgfSwKICAgIC8v5piv5ZCm5riF56m6CiAgICBjbGVhcmFibGU6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgZGVmYXVsdDogZmFsc2UsCiAgICB9LAogICAgLy/mmK/lkKbnpoHnlKgKICAgIGRpc2FibGVkOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlLAogICAgfSwKICAgIHZhbHVlOiB7CiAgICAgIHR5cGU6IFtPYmplY3QsIEFycmF5XSwKICAgICAgZGVmYXVsdCgpIHsKICAgICAgICByZXR1cm4ge307CiAgICAgIH0KICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICBsZXQgc2VsZWN0QXJyID0gW107CiAgICBpZiAodGhpcy52YWx1ZSBpbnN0YW5jZW9mIEFycmF5KSB7CiAgICAgIHNlbGVjdEFyciA9IFtdLmNvbmNhdCh0aGlzLnZhbHVlKTsKICAgIH0gZWxzZSBpZiAodGhpcy52YWx1ZSAmJiB0aGlzLnZhbHVlLnVzZXJJZCkgewogICAgICBzZWxlY3RBcnIucHVzaCh0aGlzLnZhbHVlKTsKICAgIH0KICAgIHJldHVybiB7CiAgICAgIGxvYWRUaW1lczogMCwKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIHNob3dDbGVhcjogZmFsc2UsCiAgICAgIGN1cnJlbnRWYWw6IHRoaXMudmFsdWUsCiAgICAgIHNlbGVjdFZhbDogc2VsZWN0QXJyLAogICAgICBpbnB1dEhlaWdodDogJzI4cHgnLAogICAgICBpbnB1dFdpZHRoOiAwLAogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgc2VhcmNoRm9ybTogewogICAgICAgIGpvYk51bWJlclBob25lTGlrZTogbnVsbCwKICAgICAgfSwKICAgICAgcGFnZVRvdGFsOiAwLAogICAgICBwYWdlU2l6ZTogOSwKICAgICAgcGFnZVNpemVzOiBbNSwgMTAsIDE1LCAyMF0sCiAgICAgIHBhZ2VObzogMSwKICAgICAgdGFibGVEYXRhOiBbXSwKICAgICAgb3JnYW5pemF0aW9uSWQ6IG51bGwsCiAgICAgIGRpYWxvZ01hdGU6IHsKICAgICAgICB0aXRsZTogJ+S6uuWRmOmAieaLqScKICAgICAgfSwKICAgICAgZGVmYXVsdFByb3BzOiB7CiAgICAgICAgY2hpbGRyZW46ICdjaGlsZHJlbicsCiAgICAgICAgbGFiZWw6ICdsYWJlbCcKICAgICAgfSwKICAgICAgY2hlY2tlZGtleTpbXSwKICAgICAgdHJlZURhdGE6W10KICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICBjdXJyZW50VmFsQXJyKCkgewogICAgICBpZiAoIXRoaXMuY3VycmVudFZhbCkgewogICAgICAgIHJldHVybiBbXTsKICAgICAgfQogICAgICBpZiAodGhpcy5jdXJyZW50VmFsIGluc3RhbmNlb2YgQXJyYXkpIHsKICAgICAgICByZXR1cm4gdGhpcy5jdXJyZW50VmFsOwogICAgICB9CiAgICAgIHJldHVybiBbdGhpcy5jdXJyZW50VmFsXTsKICAgIH0sCiAgICBjdXJyZW50VGV4dCgpIHsKICAgICAgaWYgKCF0aGlzLmhhc1ZhbCkgewogICAgICAgIHJldHVybiB1bmRlZmluZWQ7CiAgICAgIH0KICAgICAgaWYgKHRoaXMuY3VycmVudFZhbCBpbnN0YW5jZW9mIEFycmF5KSB7CiAgICAgICAgcmV0dXJuICcgJzsKICAgICAgfQogICAgICByZXR1cm4gdGhpcy5jdXJyZW50VmFsQXJyWzBdLm5pY2tOYW1lOwogICAgfSwKICAgIGhhc1ZhbCgpIHsKICAgICAgaWYgKCF0aGlzLmN1cnJlbnRWYWwpIHsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgICAgaWYgKHRoaXMuY3VycmVudFZhbCBpbnN0YW5jZW9mIEFycmF5KSB7CiAgICAgICAgcmV0dXJuIHRoaXMuY3VycmVudFZhbC5sZW5ndGggPiAwOwogICAgICB9CiAgICAgIHJldHVybiB0aGlzLmN1cnJlbnRWYWwudXNlcklkOwogICAgfQogIH0sCiAgbWV0aG9kczogewogICAgYXN5bmMgaW5pdFRyZWUoKXsKICAgICAgbGV0IHtkYXRhLGNvZGV9PWF3YWl0IGRlcHRUcmVlU2VsZWN0KCkKICAgICAgdGhpcy50cmVlRGF0YT1kYXRhCiAgICB9LAogICAgaGFuZGxlU2l6ZUNoYW5nZSh2YWwpIHsKICAgICAgdGhpcy5wYWdlU2l6ZSA9IHZhbDsKICAgICAgdGhpcy5nZXREYXRhKCkKICAgIH0sCiAgICByb3dDbGlja0hhbmRsZShyb3csIGNvbHVtbikgewogICAgICBpZiAoY29sdW1uLnByb3BlcnR5ID09PSAib3BlcmF0ZSIpIHsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgbGV0IGV4aXN0ID0gdGhpcy5zZWxlY3RWYWwuZmluZEluZGV4KGl0ZW0gPT4gaXRlbS51c2VySWQgPT09IHJvdy51c2VySWQpICE9PSAtMTsKICAgICAgdGhpcy5zZWxlY3Rpb25DaGFuZ2VIYW5kbGUoIWV4aXN0LCByb3cpOwogICAgfSwKICAgIGFwcGVuZFNlbGVjdFZhbCh2YWwpIHsKICAgICAgaWYgKCF2YWwgfHwgIXZhbC51c2VySWQpIHsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgaWYgKCF0aGlzLm11bHRpcGxlICYmICghdGhpcy5zZWxlY3RWYWwgfHwgdGhpcy5zZWxlY3RWYWwubGVuZ3RoID09PSAwIHx8IHRoaXMuc2VsZWN0VmFsWzBdLnVzZXJJZCAhPT0gdmFsLnVzZXJJZCkpIHsgIC8v5Y2V6YCJCiAgICAgICAgdGhpcy5zZWxlY3RWYWwgPSBbdmFsXTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgbGV0IGV4aXN0ID0gdGhpcy5zZWxlY3RWYWwuZmluZChpdGVtID0+IGl0ZW0udXNlcklkID09PSB2YWwudXNlcklkKTsKICAgICAgaWYgKGV4aXN0KSB7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuc2VsZWN0VmFsLnB1c2godmFsKTsKICAgIH0sCiAgICBpc0NoZWNrZWQodmFsKSB7CiAgICAgIHJldHVybiB0aGlzLnNlbGVjdFZhbC5maWx0ZXIoaXRlbSA9PiBpdGVtLnVzZXJJZCA9PT0gdmFsLnVzZXJJZCkubGVuZ3RoID4gMAogICAgfSwKICAgIC8qKgogICAgICog5omT5byA5a+56K+d5qGGCiAgICAgKiBAZGF0ZSAyMDIwLzUvMTkgMTE6MTUKICAgICAqLwogICAgc2hvd0RpYWxvZygpIHsKICAgICAgaWYgKHRoaXMuZGlzYWJsZWQpIHsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgaWYgKHRoaXMubG9hZFRpbWVzID09PSAwKSB7CiAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgIH0KICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICAvKioKICAgICAqIOehruiupAogICAgICogQGRhdGUgMjAyMC81LzE5IDExOjE1CiAgICAgKi8KICAgIHNlbGVjdENvbmZpcm0oKSB7CiAgICAgIGlmICghdGhpcy5tdWx0aXBsZSkgewogICAgICAgIHRoaXMuY3VycmVudFZhbCA9IHRoaXMuc2VsZWN0VmFsWzBdOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuY3VycmVudFZhbCA9IFtdLmNvbmNhdCh0aGlzLnNlbGVjdFZhbCk7CiAgICAgIH0KICAgICAgY29uc29sZS5sb2codGhpcy5jdXJyZW50VmFsKQogICAgICB0aGlzLiRlbWl0KCdpbnB1dCcsIHRoaXMuY3VycmVudFZhbCk7CiAgICAgIHRoaXMuJGVtaXQoJ2NoYW5nZScsIHRoaXMuY3VycmVudFZhbCk7CiAgICAgIC8vdGhpcy5kaXNwYXRjaCgnRWxGb3JtSXRlbScsICdlbC5mb3JtLmNoYW5nZScsIFt0aGlzLmN1cnJlbnRWYWxdKTsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gZmFsc2UKICAgIH0sCiAgICAvKioKICAgICAqIOa4hemZpOafkOmhuQogICAgICogQGRhdGUgMjAyMC83LzMxIDEwOjQ4CiAgICAgKi8KICAgIHJlbW92ZUl0ZW0oaXRlbSkgewogICAgICBpZiAoIWl0ZW0gfHwgIWl0ZW0udXNlcklkKSB7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuc2VsZWN0aW9uQ2hhbmdlSGFuZGxlKGZhbHNlLCBpdGVtKTsKICAgICAgdGhpcy5zZWxlY3RDb25maXJtKCk7CiAgICB9LAogICAgLyoqCiAgICAgKiDmuIXnqboKICAgICAqIEBkYXRlIDIwMjAvNy8zMSAxMDozMwogICAgICovCiAgICBjbGVhckhhbmRsZSgpIHsKICAgICAgdGhpcy5zZWxlY3RWYWwgPSBbXTsKICAgICAgdGhpcy5zZWxlY3RDb25maXJtKCk7CiAgICAgIHRoaXMuJGVtaXQoJ2NsZWFyJyk7CiAgICB9LAogICAgLyoqCiAgICAgKiDosIPmlbTosIPluqYKICAgICAqIEBkYXRlIDIwMjAvNy8zMCAyMDowOAogICAgICovCiAgICByZXNldElucHV0SGVpZ2h0KCkgewogICAgICBpZiAoIXRoaXMubXVsdGlwbGUpIHsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgaWYgKHRoaXMuY3VycmVudFZhbEFyci5sZW5ndGggPD0gMCkgewogICAgICAgIHRoaXMuaW5wdXRIZWlnaHQgPSAnMjhweCc7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgaWYgKCF0aGlzLiRyZWZzLnJlZmVyZW5jZSkgcmV0dXJuOwogICAgICAgIGNvbnN0IHRhZ3MgPSB0aGlzLiRyZWZzLnRhZ3M7CiAgICAgICAgY29uc3Qgc2l6ZUluTWFwID0gMjggfHwgNDA7CiAgICAgICAgdGhpcy5pbnB1dEhlaWdodCA9IHRoaXMuY3VycmVudFZhbEFyci5sZW5ndGggPT09IDAKICAgICAgICAgID8gc2l6ZUluTWFwICsgJ3B4JwogICAgICAgICAgOiBNYXRoLm1heCgKICAgICAgICAgIHRhZ3MgPyAodGFncy5jbGllbnRIZWlnaHQgKyAodGFncy5jbGllbnRIZWlnaHQgPiBzaXplSW5NYXAgPyA2IDogMCkpIDogMCwKICAgICAgICAgIHNpemVJbk1hcAogICAgICAgICkgKyAncHgnOwogICAgICB9KTsKCiAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgIGlmICghdGhpcy4kcmVmcy5yZWZlcmVuY2UpIHJldHVybjsKICAgICAgICBjb25zdCB0YWdzID0gdGhpcy4kcmVmcy50YWdzOwogICAgICAgIGNvbnN0IHNpemVJbk1hcCA9IDI4IHx8IDQwOwogICAgICAgIHRoaXMuaW5wdXRIZWlnaHQgPSB0aGlzLmN1cnJlbnRWYWxBcnIubGVuZ3RoID09PSAwCiAgICAgICAgICA/IHNpemVJbk1hcCArICdweCcKICAgICAgICAgIDogTWF0aC5tYXgoCiAgICAgICAgICB0YWdzID8gKHRhZ3MuY2xpZW50SGVpZ2h0ICsgKHRhZ3MuY2xpZW50SGVpZ2h0ID4gc2l6ZUluTWFwID8gNiA6IDApKSA6IDAsCiAgICAgICAgICBzaXplSW5NYXAKICAgICAgICApICsgJ3B4JzsKICAgICAgfSwgMzQwKQoKICAgIH0sCiAgICBzZWxlY3Rpb25DaGFuZ2VIYW5kbGUoaXNDaGVja2VkLCByb3cpIHsKICAgICAgaWYgKGlzQ2hlY2tlZCkgewogICAgICAgIHRoaXMuYXBwZW5kU2VsZWN0VmFsKHJvdyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGxldCBpbmRleCA9IHRoaXMuc2VsZWN0VmFsLmZpbmRJbmRleChpdGVtID0+IGl0ZW0udXNlcklkID09PSByb3cudXNlcklkKTsKICAgICAgaWYgKGluZGV4ID49IDApIHsKICAgICAgICB0aGlzLnNlbGVjdFZhbC5zcGxpY2UoaW5kZXgsIDEpOwogICAgICB9CiAgICB9LAogICAgbm9kZUNsaWNrKGRhdGEpIHsKICAgICAgaWYgKGRhdGEuaWQgPT09ICcwJykgewogICAgICAgIHRoaXMub3JnYW5pemF0aW9uSWQgPSAnJzsKICAgICAgfQogICAgICB0aGlzLm9yZ2FuaXphdGlvbklkID0gZGF0YS5pZDsKICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICB9LAogICAgcGFnaW5hdGlvbkNoYW5nZSAodmFsKSB7CiAgICAgIHRoaXMucGFnZU5vID0gdmFsOwogICAgICB0aGlzLmdldERhdGEoKTsKICAgIH0sCiAgICBnZXREYXRhKCkgewogICAgICBsZXQgcXVlcnkgPSB7CiAgICAgICAgcGFnZU51bTogdGhpcy5wYWdlTm8sCiAgICAgICAgcGFnZVNpemU6IHRoaXMucGFnZVNpemUsCiAgICAgIH07CgogICAgICBPYmplY3QuYXNzaWduKHF1ZXJ5LCB0aGlzLnNlYXJjaEZvcm0pOwogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwoKICAgICAgVXNlcnNCeURlcHRPckdyb3VwKHF1ZXJ5LCdkZXB0JykudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICAgIGlmIChyZXMuY29kZSAhPT0gJzAwMDAnKSB7CiAgICAgICAgICB0aGlzLmxvYWRUaW1lcyA9IDA7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlcy5tc2cpOwogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KICAgICAgICB0aGlzLmxvYWRUaW1lcysrOwogICAgICAgIHRoaXMucGFnZVRvdGFsID0gcmVzLmRhdGEudG90YWw7CiAgICAgICAgdGhpcy50YWJsZURhdGEgPSByZXMuZGF0YS5yZWNvcmRzOwogICAgICB9KQogICAgfSwKICB9LAogIHdhdGNoOiB7CiAgICBjbGVhcmFibGUoKSB7CiAgICAgIHRoaXMuaW5wdXRXaWR0aCA9IHRoaXMuJHJlZnMuc2VsZWN0LmNsaWVudFdpZHRoOwogICAgfSwKICAgIGN1cnJlbnRWYWxBcnIoKSB7CiAgICAgIHRoaXMucmVzZXRJbnB1dEhlaWdodCgpOwogICAgfSwKICAgIHZhbHVlKHZhbCkgewogICAgICBsZXQgc2VsZWN0QXJyID0gW107CiAgICAgIGlmICh2YWwgaW5zdGFuY2VvZiBBcnJheSkgewogICAgICAgIHNlbGVjdEFyciA9IFtdLmNvbmNhdCh2YWwpOwogICAgICB9IGVsc2UgaWYgKHZhbCAmJiB2YWwudXNlcklkKSB7CiAgICAgICAgc2VsZWN0QXJyLnB1c2godmFsKTsKICAgICAgfQogICAgICB0aGlzLmN1cnJlbnRWYWwgPSB2YWw7CiAgICAgIHRoaXMuc2VsZWN0VmFsID0gc2VsZWN0QXJyOwogICAgfQogIH0sCiAgZmlsdGVyczogewogICAgcm9sZXNGaWx0ZXIocm9sZXMpIHsKICAgICAgaWYgKCFyb2xlcyB8fCByb2xlcy5sZW5ndGggPD0gMCkgewogICAgICAgIHJldHVybiAnLSc7CiAgICAgIH0KICAgICAgcmV0dXJuIHJvbGVzLm1hcChyb2xlID0+IHsKICAgICAgICByZXR1cm4gcm9sZS5uYW1lOwogICAgICB9KS5yZWR1Y2UoKHgsIHkpID0+IHsKICAgICAgICByZXR1cm4geCArICJ8IiArIHk7CiAgICAgIH0pOwogICAgfQogIH0sCiAgYmVmb3JlRGVzdHJveSgpIHsKCiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsKA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "index.vue", "sourceRoot": "src/components/userSelect", "sourcesContent": ["<template>\n  <div ref=\"select\" class=\"pc-user-info-select \" @mouseenter=\"()=>showClear=true\" @mouseleave=\"()=>showClear=false\">\n\n    <div ref=\"tags\" class=\"el-select__tags\" v-if=\"multiple\" @click=\"showDialog\"\n         :style=\"{ 'max-width': inputWidth - 30 + 'px', width: '100%' }\">\n            <span v-if=\"hasVal\">\n                <el-tag class=\"inner-tag\" v-for=\"tag in currentValArr\" :key=\"tag.userId\" size=\"mini\" type=\"info\"\n                        @close=\"removeItem(tag)\" :closable=\"!disabled\"> {{tag.nickName}}\n                </el-tag>\n            </span>\n    </div>\n\n    <div class=\"el-input el-input--mini el-input--suffix\">\n      <input type=\"text\" readonly=\"readonly\" ref=\"reference\" @click=\"showDialog\"\n             autocomplete=\"off\" :placeholder=\"placeholder\"\n             :class=\"{'disabled':disabled}\" class=\"el-input__inner\" :value=\"currentText\" :disabled=\"disabled\"\n             :style=\"{'height':inputHeight}\"/>\n      <span class=\"el-input__suffix\"  @click.stop=\"clearHandle\"  v-show=\"showClear && hasVal\">\n                <span class=\"el-input__suffix-inner\" v-if=\"clearable && !disabled\">\n                 <i class=\"el-input__icon el-icon-circle-close el-input__clear\"/>\n                </span>\n            </span>\n    </div>\n\n    <el-dialog title=\"人员选择\" :close-on-press-escape=\"false\" append-to-body :close-on-click-modal=\"false\"\n               class=\"pc-user-info-select-dialog\" v-dialogDrag :visible.sync=\"dialogVisible\">\n      <template #title>\n        <svg-icon icon-class=\"user-add\"/>\n        <span style=\"font-size: 12px;font-weight: 900;color: #fff\">{{ dialogMate.title }}</span>\n      </template>\n\n      <gridvo-collapse-search-bar>\n        <el-form ref=\"searchForm\" v-model=\"searchForm\" :inline=\"true\">\n          <el-form-item prop=\"name\">\n            <el-input v-model=\"searchForm.nickName\" @keyup.enter.native=\"getData\"\n                      maxlength=\"16\" placeholder=\"请输入姓名\" clearable/>\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"getData\">查询</el-button>\n          </el-form-item>\n        </el-form>\n      </gridvo-collapse-search-bar>\n\n      <el-row v-if=\"multiple\" style=\"margin-top: -1px\">\n        <el-col>\n          <div class=\"select-panel\">\n            <el-tag\n              style=\"margin: 2px\"\n              v-for=\"tag in selectVal\"\n              :key=\"tag.userId\" size=\"mini\"\n              closable\n              @close=\"selectionChangeHandle(false,tag)\"\n              type=\"info\">\n              {{tag.nickName}}\n            </el-tag>\n          </div>\n        </el-col>\n      </el-row>\n\n      <el-row>\n        <el-col :span=\"6\">\n          <el-container style=\"max-height: 370px;border-right: 1px dashed #dedede; margin-right: 10px\">\n            <el-main style=\"padding: 5px 5px 5px 0\">\n              <el-scrollbar class=\"custom-scrollbar\" style=\"min-height: 360px;\">\n                <el-tree\n                  :data=\"treeData\"\n                  node-key=\"tree1\"\n                  ref=\"tree\"\n                  check-strictly\n                  :highlight-current='true'\n                  :check-on-click-node=\"false\"\n                  :accordion=\"false\"\n                  :default-checked-keys=\"[checkedkey]\"\n                  :default-expanded-keys=\"checkedkey\"\n                  :props=\"defaultProps\"\n                  :default-expand-all=\"false\"\n                  @node-click=\"nodeClick\"\n                ></el-tree>\n              </el-scrollbar>\n            </el-main>\n          </el-container>\n        </el-col>\n        <el-col :span=\"18\">\n          <div style=\"\">\n            <el-table\n              v-loading=\"loading\"\n              :data=\"tableData\"\n              row-key=\"userId\"\n              stripe\n              border\n              :highlight-current-row=\"true\"\n              @row-click=\"rowClickHandle\"\n              @select=\"selectionChangeHandle\"\n              style=\"width: 100%;\">\n              <el-table-column\n                align=\"center\"\n                property=\"nickName\"\n                label=\"姓名\" width=\"170\">\n              </el-table-column>\n              <el-table-column\n                align=\"center\"\n                property=\"sex\"\n                label=\"性别\" width=\"170\">\n                <template slot-scope=\"scope\">\n                  {{ scope.row.sex===1?'男':'女'}}\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"center\"\n                property=\"email\"\n                label=\"邮箱\" width=\"170\">\n              </el-table-column>\n              <!--<el-table-column\n                align=\"center\"\n                property=\"organization\"\n                label=\"所属组织\" width=\"155\">\n                <template slot-scope=\"scope\">\n                  {{ scope.row.organization.name || '-'}}\n                </template>\n              </el-table-column>-->\n              <!--<el-table-column\n                align=\"center\"\n                property=\"organization\"\n                label=\"用户角色\">\n                <template slot-scope=\"scope\">\n                  {{ scope.row.roles | rolesFilter}}\n                </template>\n              </el-table-column>-->\n\n              <el-table-column\n                label=\"选择\"\n                property=\"operate\"\n                align=\"center\"\n                width=\"55\">\n                <template slot-scope=\"scope\">\n                  <el-checkbox :value=\"isChecked(scope.row)\"\n                               @change=\"(val)=>selectionChangeHandle(val,scope.row)\"/>\n                </template>\n              </el-table-column>\n            </el-table>\n\n            <el-pagination\n              @current-change=\"paginationChange\"\n              @size-change=\"handleSizeChange\"\n              :page-size=\"pageSize\"\n              :page-sizes=\"pageSizes\"\n              layout=\"total,  prev, pager, next\"\n              :total=\"pageTotal\"\n              style=\"text-align: center;margin-top: 5px\"\n            >\n            </el-pagination>\n          </div>\n        </el-col>\n      </el-row>\n\n      <template slot=\"footer\">\n        <div style=\"text-align: center\">\n          <el-button @click=\"selectConfirm\" type=\"primary\">确定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n\n  import {deptTreeSelect,groupTreeSelect,UsersByDeptOrGroup} from \"@/api/component/userSelect\";\n  import emitter from 'element-ui/src/mixins/emitter';\n\n  /**\n   * 用户选择器\n   */\n  export default {\n    name: 'UserSelect',\n    mounted() {\n      this.resetInputHeight();\n      if (this.clearable) {\n        this.inputWidth = this.$refs.select.clientWidth;\n      }\n      this.initTree()\n    },\n    updated() {\n      this.resetInputHeight();\n    },\n    mixins: [emitter],\n    components: {},\n    props: {\n      placeholder: {\n        type: String,\n        default: '请选择人员'\n      },\n      //是否多选\n      multiple: {\n        type: Boolean,\n        default: false,\n      },\n      //是否清空\n      clearable: {\n        type: Boolean,\n        default: false,\n      },\n      //是否禁用\n      disabled: {\n        type: Boolean,\n        default: false,\n      },\n      value: {\n        type: [Object, Array],\n        default() {\n          return {};\n        }\n      }\n    },\n    data() {\n      let selectArr = [];\n      if (this.value instanceof Array) {\n        selectArr = [].concat(this.value);\n      } else if (this.value && this.value.userId) {\n        selectArr.push(this.value);\n      }\n      return {\n        loadTimes: 0,\n        loading: false,\n        showClear: false,\n        currentVal: this.value,\n        selectVal: selectArr,\n        inputHeight: '28px',\n        inputWidth: 0,\n        dialogVisible: false,\n        searchForm: {\n          jobNumberPhoneLike: null,\n        },\n        pageTotal: 0,\n        pageSize: 9,\n        pageSizes: [5, 10, 15, 20],\n        pageNo: 1,\n        tableData: [],\n        organizationId: null,\n        dialogMate: {\n          title: '人员选择'\n        },\n        defaultProps: {\n          children: 'children',\n          label: 'label'\n        },\n        checkedkey:[],\n        treeData:[]\n      }\n    },\n    computed: {\n      currentValArr() {\n        if (!this.currentVal) {\n          return [];\n        }\n        if (this.currentVal instanceof Array) {\n          return this.currentVal;\n        }\n        return [this.currentVal];\n      },\n      currentText() {\n        if (!this.hasVal) {\n          return undefined;\n        }\n        if (this.currentVal instanceof Array) {\n          return ' ';\n        }\n        return this.currentValArr[0].nickName;\n      },\n      hasVal() {\n        if (!this.currentVal) {\n          return false;\n        }\n        if (this.currentVal instanceof Array) {\n          return this.currentVal.length > 0;\n        }\n        return this.currentVal.userId;\n      }\n    },\n    methods: {\n      async initTree(){\n        let {data,code}=await deptTreeSelect()\n        this.treeData=data\n      },\n      handleSizeChange(val) {\n        this.pageSize = val;\n        this.getData()\n      },\n      rowClickHandle(row, column) {\n        if (column.property === \"operate\") {\n          return;\n        }\n        let exist = this.selectVal.findIndex(item => item.userId === row.userId) !== -1;\n        this.selectionChangeHandle(!exist, row);\n      },\n      appendSelectVal(val) {\n        if (!val || !val.userId) {\n          return;\n        }\n        if (!this.multiple && (!this.selectVal || this.selectVal.length === 0 || this.selectVal[0].userId !== val.userId)) {  //单选\n          this.selectVal = [val];\n          return;\n        }\n        let exist = this.selectVal.find(item => item.userId === val.userId);\n        if (exist) {\n          return;\n        }\n        this.selectVal.push(val);\n      },\n      isChecked(val) {\n        return this.selectVal.filter(item => item.userId === val.userId).length > 0\n      },\n      /**\n       * 打开对话框\n       * @date 2020/5/19 11:15\n       */\n      showDialog() {\n        if (this.disabled) {\n          return;\n        }\n        if (this.loadTimes === 0) {\n          this.getData();\n        }\n        this.dialogVisible = true;\n      },\n      /**\n       * 确认\n       * @date 2020/5/19 11:15\n       */\n      selectConfirm() {\n        if (!this.multiple) {\n          this.currentVal = this.selectVal[0];\n        } else {\n          this.currentVal = [].concat(this.selectVal);\n        }\n        console.log(this.currentVal)\n        this.$emit('input', this.currentVal);\n        this.$emit('change', this.currentVal);\n        //this.dispatch('ElFormItem', 'el.form.change', [this.currentVal]);\n        this.dialogVisible = false\n      },\n      /**\n       * 清除某项\n       * @date 2020/7/31 10:48\n       */\n      removeItem(item) {\n        if (!item || !item.userId) {\n          return;\n        }\n        this.selectionChangeHandle(false, item);\n        this.selectConfirm();\n      },\n      /**\n       * 清空\n       * @date 2020/7/31 10:33\n       */\n      clearHandle() {\n        this.selectVal = [];\n        this.selectConfirm();\n        this.$emit('clear');\n      },\n      /**\n       * 调整调度\n       * @date 2020/7/30 20:08\n       */\n      resetInputHeight() {\n        if (!this.multiple) {\n          return;\n        }\n        if (this.currentValArr.length <= 0) {\n          this.inputHeight = '28px';\n          return;\n        }\n\n        this.$nextTick(() => {\n          if (!this.$refs.reference) return;\n          const tags = this.$refs.tags;\n          const sizeInMap = 28 || 40;\n          this.inputHeight = this.currentValArr.length === 0\n            ? sizeInMap + 'px'\n            : Math.max(\n            tags ? (tags.clientHeight + (tags.clientHeight > sizeInMap ? 6 : 0)) : 0,\n            sizeInMap\n          ) + 'px';\n        });\n\n        setTimeout(() => {\n          if (!this.$refs.reference) return;\n          const tags = this.$refs.tags;\n          const sizeInMap = 28 || 40;\n          this.inputHeight = this.currentValArr.length === 0\n            ? sizeInMap + 'px'\n            : Math.max(\n            tags ? (tags.clientHeight + (tags.clientHeight > sizeInMap ? 6 : 0)) : 0,\n            sizeInMap\n          ) + 'px';\n        }, 340)\n\n      },\n      selectionChangeHandle(isChecked, row) {\n        if (isChecked) {\n          this.appendSelectVal(row);\n          return;\n        }\n        let index = this.selectVal.findIndex(item => item.userId === row.userId);\n        if (index >= 0) {\n          this.selectVal.splice(index, 1);\n        }\n      },\n      nodeClick(data) {\n        if (data.id === '0') {\n          this.organizationId = '';\n        }\n        this.organizationId = data.id;\n        this.getData();\n      },\n      paginationChange (val) {\n        this.pageNo = val;\n        this.getData();\n      },\n      getData() {\n        let query = {\n          pageNum: this.pageNo,\n          pageSize: this.pageSize,\n        };\n\n        Object.assign(query, this.searchForm);\n        this.loading = true;\n\n        UsersByDeptOrGroup(query,'dept').then(res => {\n          this.loading = false;\n          if (res.code !== '0000') {\n            this.loadTimes = 0;\n            this.$message.error(res.msg);\n            return;\n          }\n          this.loadTimes++;\n          this.pageTotal = res.data.total;\n          this.tableData = res.data.records;\n        })\n      },\n    },\n    watch: {\n      clearable() {\n        this.inputWidth = this.$refs.select.clientWidth;\n      },\n      currentValArr() {\n        this.resetInputHeight();\n      },\n      value(val) {\n        let selectArr = [];\n        if (val instanceof Array) {\n          selectArr = [].concat(val);\n        } else if (val && val.userId) {\n          selectArr.push(val);\n        }\n        this.currentVal = val;\n        this.selectVal = selectArr;\n      }\n    },\n    filters: {\n      rolesFilter(roles) {\n        if (!roles || roles.length <= 0) {\n          return '-';\n        }\n        return roles.map(role => {\n          return role.name;\n        }).reduce((x, y) => {\n          return x + \"|\" + y;\n        });\n      }\n    },\n    beforeDestroy() {\n\n    }\n  }\n</script>\n\n<style>\n  .pc-user-info-select-dialog .el-dialog__body {\n    padding: 0 20px 10px 20px !important;\n    color: #606266;\n    font-size: 14px;\n    word-break: break-all;\n  }\n</style>\n\n<style scoped>\n  .pc-user-info-select {\n    display: inline-block;\n    position: relative;\n  }\n\n  .disabled {\n    background-color: #f5f7fa;\n    border-color: #e4e7ed;\n    color: #C0C4CC;\n    cursor: not-allowed;\n  }\n\n  .select-panel {\n    border: 1px dashed #e0e0e0;\n    margin-bottom: 10px;\n    min-height: 37px;\n    padding: 5px;\n  }\n\n  .inner-tag {\n    margin: 2px 0 2px 6px;\n  }\n</style>\n"]}]}