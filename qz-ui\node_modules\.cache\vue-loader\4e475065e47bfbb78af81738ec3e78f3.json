{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sdsbgl\\sdgt.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sdsbgl\\sdgt.vue", "mtime": 1706897324964}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsKaW1wb3J0IHsgZ2V0TGlzdGd0LCBndHJlbW92ZSwgc2F2ZU9yVXBkYXRlZ3QsIGdldFRyZWVMaXN0LCB1cGRhdGVTdGF0dXMgfSBmcm9tICdAL2FwaS9kYWdhbmdPaWxmaWVsZC9hc3NldC9zZGd0JwppbXBvcnQgeyBnZXRSZXN1bURhdGFMaXN0IH0gZnJvbSAnQC9hcGkvZGFnYW5nT2lsZmllbGQvYXNzZXQvc2RzYicKaW1wb3J0IHsgZ2V0TGlzdEpjLCByZW1vdmVKYywgc2F2ZU9yVXBkYXRlSmMgfSBmcm9tICdAL2FwaS9kYWdhbmdPaWxmaWVsZC9kd3p5Z2wvc2RzYmdsL3NkZ3RqYycKaW1wb3J0IHsgZ2V0TGlzdEp5eiwgcmVtb3ZlSnl6LCBzYXZlT3JVcGRhdGVKeXogfSBmcm9tICdAL2FwaS9kYWdhbmdPaWxmaWVsZC9kd3p5Z2wvc2RzYmdsL3NkZ3RqeXonCmltcG9ydCB7IGdldExpc3RKZCwgcmVtb3ZlSmQsIHNhdmVPclVwZGF0ZUpkIH0gZnJvbSAnQC9hcGkvZGFnYW5nT2lsZmllbGQvZHd6eWdsL3Nkc2JnbC9zZGd0amQnCmltcG9ydCB7IGdldExpc3RKaiwgcmVtb3ZlSmosIHNhdmVPclVwZGF0ZUpqIH0gZnJvbSAnQC9hcGkvZGFnYW5nT2lsZmllbGQvZHd6eWdsL3Nkc2JnbC9zZGd0amonCmltcG9ydCB7IGdldExpc3RMeCwgcmVtb3ZlTHgsIHNhdmVPclVwZGF0ZUx4IH0gZnJvbSAnQC9hcGkvZGFnYW5nT2lsZmllbGQvZHd6eWdsL3Nkc2JnbC9zZGd0bHgnCmltcG9ydCB7IGdldExpc3RGcywgcmVtb3ZlRnMsIHNhdmVPclVwZGF0ZUZzIH0gZnJvbSAnQC9hcGkvZGFnYW5nT2lsZmllbGQvZHd6eWdsL3Nkc2JnbC9zZGd0ZnMnCmltcG9ydCB7IGdldExpc3RIZCwgcmVtb3ZlSGQsIHNhdmVPclVwZGF0ZUhkIH0gZnJvbSAnQC9hcGkvZGFnYW5nT2lsZmllbGQvZHd6eWdsL3Nkc2JnbC9zZGd0aGQnCmltcG9ydCB7ZGVsZXRlQnlJZCwgZ2V0TGlzdEJ5QnVzaW5lc3NJZH0gZnJvbSAiQC9hcGkvdG9vbC9maWxlIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnc2RndCcsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHVwbG9hZERhdGE6IHsKICAgICAgICB0eXBlOiIiLAogICAgICAgIGJ1c2luZXNzSWQ6IHVuZGVmaW5lZAogICAgICB9LAogICAgICAvL+adhu<PERSON><PERSON><PERSON>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"}, {"version": 3, "sources": ["sdgt.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAirCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "sdgt.vue", "sourceRoot": "src/views/dagangOilfield/dwzygl/sdsbgl", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"16\">\n      <el-col :span=\"4\">\n        <el-card>\n          <!--<div class=\"clearfix\" slot=\"header\">\n            <span></span>\n          </div>-->\n          <div style=\"overflow: auto;height: 80vh\">\n            <el-tree :expand-on-click-node=\"true\"\n                     highlight-current\n                     id=\"tree\"\n                     :data=\"treeOptions\"\n                     :default-expanded-keys=\"['1']\"\n                     @node-click=\"handleNodeClick\"\n                     node-key=\"nodeId\"\n                     accordion\n            />\n          </div>\n        </el-card>\n      </el-col>\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :btnHidden=\"false\"\n          @getMethod=\"getTableList\"\n          @handleReset=\"filterReset\"\n        />\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button type=\"primary\" v-hasPermi=\"['gttz:button:add']\" icon=\"el-icon-plus\" @click=\"bdzAddSensorButton\">新增</el-button>\n            <el-button type=\"danger\" v-hasPermi=\"['gttz:button:delete']\" icon=\"el-icon-delete\" @click=\"deleteInfo\">删除</el-button>\n          </div>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"500\"\n            @getMethod=\"getTableList\"\n          >\n            <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\" width=\"160\"\n                             :resizable=\"false\">\n              <template slot-scope=\"scope\">\n                <el-button @click=\"updatebdz(scope.row)\" v-hasPermi=\"['gttz:button:update']\" type=\"text\" size=\"small\" title=\"修改\"  class='el-icon-edit'></el-button>\n                <el-button @click=\"bdzDetails(scope.row)\" type=\"text\" size=\"small\" title=\"详情\" class=\"el-icon-view\"></el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!-- 杆塔弹出框开始展示设备履历 -->\n    <el-dialog title=\"杆塔详情\" :visible.sync=\"gtDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"60%\"\n               :before-close=\"handleClose\">\n      <el-tabs v-model=\"activeTabName\">\n        <!--基本信息-->\n        <el-tab-pane label=\"基本信息\" name=\"sbDesc\">\n          <div class=\"block\" style=\"width: 50%;height: 50%;margin-bottom: 2%;float: right\">\n            <span class=\"demonstration\">设备图片</span>\n            <el-carousel trigger=\"click\" height=\"150px\" indicator-position=\"none\" :interval=\"2000\" type=\"card\">\n              <el-carousel-item v-for=\"(img,index) in imgList\" :key=\"index\">\n                <viewer :images=\"imgList\">\n                  <img :src=\"img.url\" width=\"100%\" height=\"100%\"/>\n                </viewer>\n              </el-carousel-item>\n            </el-carousel>\n          </div>\n          <el-form :model=\"jbxxForm\" ref=\"jbxxForm\" :disabled=\"show\" :rules=\"rules\" label-width=\"130px\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔编号\" prop=\"gtbh\">\n                  <el-input v-model=\"jbxxForm.gtbh\" placeholder=\"请输入杆塔编号\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔名称\" prop=\"gtmc\">\n                  <el-input v-model=\"jbxxForm.gtmc\" placeholder=\"请输入杆塔名称\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"所属线路\" prop=\"lineName\">\n                  <el-input v-model=\"jbxxForm.lineName\" placeholder=\"请输入所属线路\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔材质\" prop=\"gtcz\">\n                  <el-select v-model=\"jbxxForm.gtcz\" placeholder=\"请选择杆塔材质\" clearable>\n                    <el-option label=\"木杆\" value=\"木杆\"></el-option>\n                    <el-option label=\"水泥杆\" value=\"水泥杆\"></el-option>\n                    <el-option label=\"轻型铁塔\" value=\"轻型铁塔\"></el-option>\n                    <el-option label=\"钢管塔\" value=\"钢管塔\"></el-option>\n                    <el-option label=\"铁塔\" value=\"铁塔\"></el-option>\n                    <el-option label=\"门架\" value=\"门架\"></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"运行班组\" prop=\"yxbz\">\n                  <el-select v-model=\"jbxxForm.yxbz\" placeholder=\"请选择运行班组\" clearable>\n                    <el-option label=\"线路一班\" value=\"线路一班\"></el-option>\n                    <el-option label=\"线路二班\" value=\"线路二班\"></el-option>\n                    <el-option label=\"线路三班\" value=\"线路三班\"></el-option>\n                    <el-option label=\"线路四班\" value=\"线路四班\"></el-option>\n                    <el-option label=\"线路五班\" value=\"线路五班\"></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"所属部门\" prop=\"ssbm\">\n                  <el-input v-model=\"jbxxForm.ssbm\" placeholder=\"请输入所属部门\" disabled></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"电压等级\" prop=\"dydj\">\n                  <el-select v-model=\"jbxxForm.dydj\" placeholder=\"请选择电压等级\" clearable>\n                    <el-option\n                      v-for=\"item in options\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\">\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔型式\" prop=\"gtxs\">\n                  <el-select v-model=\"jbxxForm.gtxs\" placeholder=\"请选择杆塔型式\" clearable>\n                    <el-option label=\"三杆\" value=\"三杆\"></el-option>\n                    <el-option label=\"其它\" value=\"其它\"></el-option>\n                    <el-option label=\"单杆\" value=\"单杆\"></el-option>\n                    <el-option label=\"双杆\" value=\"双杆\"></el-option>\n                    <el-option label=\"拉线塔\" value=\"拉线塔\"></el-option>\n                    <el-option label=\"自立塔\" value=\"自立塔\"></el-option>\n                    <el-option label=\"钢管塔\" value=\"钢管塔\"></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔性质\" prop=\"gtNature\">\n                  <el-select v-model=\"jbxxForm.gtNature\" placeholder=\"请选择杆塔性质\" clearable>\n                    <el-option label=\"分支\" value=\"分支\"></el-option>\n                    <el-option label=\"换位\" value=\"换位\"></el-option>\n                    <el-option label=\"直线\" value=\"直线\"></el-option>\n                    <el-option label=\"终端\" value=\"终端\"></el-option>\n                    <el-option label=\"耐张\" value=\"耐张\"></el-option>\n                    <el-option label=\"转角\" value=\"转角\"></el-option>\n                    <el-option label=\"门架\" value=\"门架\"></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔形状\" prop=\"gtxz\">\n                    <el-select v-model=\"jbxxForm.gtxz\" placeholder=\"请选择杆塔形状\" clearable>\n                      <el-option\n                        v-for=\"item in gtxzOptions\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\">\n                      </el-option>\n                    </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"相序\" prop=\"xx\">\n                  <el-select v-model=\"jbxxForm.xx\" placeholder=\"请选择相序\" clearable>\n                    <el-option label=\"ABC\" value=\"ABC\"></el-option>\n                    <el-option label=\"ACB\" value=\"ACB\"></el-option>\n                    <el-option label=\"BAC\" value=\"BAC\"></el-option>\n                    <el-option label=\"BCA\" value=\"BCA\"></el-option>\n                    <el-option label=\"CAB\" value=\"CAB\"></el-option>\n                    <el-option label=\"CBA\" value=\"CBA\"></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔呼称高(m)\" prop=\"gthcg\">\n                  <el-input v-model=\"jbxxForm.gthcg\" type=\"number\" placeholder=\"请输入杆塔呼称高\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔高度(m)\" prop=\"towerHeight\">\n                  <el-input v-model=\"jbxxForm.towerHeight\" type=\"number\" placeholder=\"请输入杆塔高度\"></el-input>\n                </el-form-item>\n              </el-col>\n\n              <el-col :span=\"8\">\n                <el-form-item label=\"至上基塔档距(m)\" prop=\"zsjtdj\">\n                  <el-input v-model=\"jbxxForm.zsjtdj\" type=\"number\" placeholder=\"请输入至上基塔档距\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"导线排列方式\" prop=\"dxplfs\">\n                  <el-select v-model=\"jbxxForm.dxplfs\" placeholder=\"请选择导线排列方式\" clearable>\n                    <el-option label=\"三角\" value=\"三角\"></el-option>\n                    <el-option label=\"垂直\" value=\"垂直\"></el-option>\n                    <el-option label=\"水平\" value=\"水平\"></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"是否同杆并架\" prop=\"sftgbj\">\n                  <el-select v-model=\"jbxxForm.sftgbj\" placeholder=\"请选择是否同杆并架\" clearable>\n                    <el-option label=\"是\" value=\"是\"></el-option>\n                    <el-option label=\"否\" value=\"否\"></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔规格型号\" prop=\"gtggxh\">\n                  <el-input v-model=\"jbxxForm.gtggxh\"  placeholder=\"请输入杆塔规格型号\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"坐标经度\" prop=\"jd\">\n                  <el-input v-model=\"jbxxForm.jd\" placeholder=\"请输入坐标经度\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"坐标纬度\" prop=\"wd\">\n                  <el-input v-model=\"jbxxForm.wd\" placeholder=\"请输入坐标纬度\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"同杆架设回路数\" prop=\"tgjshls\">\n                  <el-input v-model=\"jbxxForm.tgjshls\" placeholder=\"请输入同杆架设回路数\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"同杆线路位置\" prop=\"tgxlwz\">\n                  <el-input v-model=\"jbxxForm.tgxlwz\" placeholder=\"请输入同杆线路位置\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"是否换相\" prop=\"sfhx\">\n                  <el-select v-model=\"jbxxForm.sfhx\" placeholder=\"请选择是否换相\" clearable>\n                    <el-option label=\"是\" value=\"是\"></el-option>\n                    <el-option label=\"否\" value=\"否\"></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"投运日期\" class=\"add_sy_tyrq\" prop=\"tyrq\">\n                  <el-date-picker\n                    v-model=\"jbxxForm.tyrq\"\n                    type=\"date\"\n                    placeholder=\"选择日期\"\n                    format=\"yyyy-MM-dd\"\n                    value-format=\"yyyy-MM-dd\">\n                  </el-date-picker>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"接地体材料\" prop=\"jdtcl\">\n                  <el-select v-model=\"jbxxForm.jdtcl\" placeholder=\"请选择接地体材料\" clearable>\n                    <el-option label=\"石墨\" value=\"石墨\"></el-option>\n                    <el-option label=\"钢材\" value=\"钢材\"></el-option>\n                    <el-option label=\"其它\" value=\"其它\"></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"实际阻值\" prop=\"sjzz\">\n                  <el-input v-model=\"jbxxForm.sjzz\" placeholder=\"请输入实际阻值\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"导线型号\" prop=\"dxxh\">\n                  <el-input v-model=\"jbxxForm.dxxh\" placeholder=\"请输入导线型号\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"地线型号\" prop=\"jdxh\">\n                  <el-input v-model=\"jbxxForm.jdxh\" placeholder=\"请输入地线型号\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"设备状态\" prop=\"sbzk\">\n                  <el-select v-model=\"jbxxForm.sbzt\" placeholder=\"请选择设备状况\">\n                    <el-option\n                      v-for=\"item in sbzt\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\">\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n<!--              <el-col :span=\"8\">-->\n<!--                <el-form-item label=\"负责人\" prop=\"fzr\">-->\n<!--                  <el-select v-model=\"jbxxForm.fzr\" placeholder=\"请选择负责人\"></el-select>-->\n<!--                </el-form-item>-->\n<!--              </el-col>-->\n            </el-row>\n            <el-row>\n              <el-form-item label=\"备注\" prop=\"bz\">\n                <el-input v-model=\"jbxxForm.bz\" type=\"textarea\" :row=\"2\" placeholder=\"备注\"></el-input>\n              </el-form-item>\n            </el-row>\n\n            <el-row :gutter=\"20\">\n              <el-form-item label=\"已上传图片：\" prop=\"attachment\" v-if=\"jbxxForm.attachment.length>0\" id=\"pic_form\">\n                <el-col :span=\"24\" v-for=\"(item,index) in jbxxForm.attachment\" style=\"margin-left: 0\">\n                  <el-form-item :label=\"(index+1).toString()\">\n                    {{item.fileOldName}}\n                    <el-button v-if=\"!show\" type=\"error\" size=\"mini\" @click=\"deleteFileById(item.fileId)\">删除</el-button>\n                  </el-form-item>\n                </el-col>\n              </el-form-item>\n            </el-row>\n            <el-row :gutter=\"20\">\n              <el-form-item label=\"上传图片：\" v-if=\"!show\">\n                <el-upload list-type=\"picture-card\"\n                           class=\"upload-demo\"\n                           accept=\".jpg,.png\"\n                           ref=\"upload\"\n                           :headers=\"header\"\n                           action=\"/isc-api/file/upload\"\n                           :before-upload=\"beforeUpload\"\n                           :data=\"uploadData\"\n                           single\n                           :auto-upload=\"false\"\n                           multiple>\n                  <el-button slot=\"trigger\"  type=\"primary\">选取文件</el-button>\n                  <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png文件</div>\n                </el-upload>\n              </el-form-item>\n            </el-row>\n\n<!--              <el-col :span=\"8\">-->\n<!--                <el-form-item label=\"设计污秽等级\" prop=\"whdj\">-->\n<!--                  <el-input v-model=\"jbxxForm.whdj\" placeholder=\"请输入污秽等级\"></el-input>-->\n<!--                </el-form-item>-->\n<!--              </el-col>-->\n<!--              <el-col :span=\"8\">-->\n<!--                <el-form-item label=\"土壤性质\" prop=\"trxz\">-->\n<!--                  <el-select v-model=\"jbxxForm.trxz\" placeholder=\"请选择土壤性质\">-->\n<!--                  </el-select>-->\n<!--                </el-form-item>-->\n<!--              </el-col>-->\n\n\n<!--              <el-col :span=\"8\">-->\n<!--                <el-form-item label=\"实际污秽等级\" prop=\"sjwhdj\">-->\n<!--                  <el-select v-model=\"jbxxForm.sjwhdj\" placeholder=\"请选择实际污秽等级\">-->\n<!--                  </el-select>-->\n<!--                </el-form-item>-->\n<!--              </el-col>-->\n<!--              <el-col :span=\"8\">-->\n<!--                <el-form-item label=\"设计K值(曲率)\" prop=\"edpl\">-->\n<!--                  <el-select v-model=\"jbxxForm.edpl\" placeholder=\"请选择K值(曲率)\">-->\n<!--                  </el-select>-->\n<!--                </el-form-item>-->\n<!--              </el-col>-->\n\n\n<!--              <el-col :span=\"8\">-->\n<!--                <el-form-item label=\"建设日期\" class=\"add_sy_tyrq\" prop=\"jsrq\">-->\n<!--                  <el-date-picker-->\n<!--                    v-model=\"jbxxForm.jsrq\"-->\n<!--                    type=\"date\"-->\n<!--                    placeholder=\"选择日期\"-->\n<!--                    format=\"yyyy-MM-dd\"-->\n<!--                    value-format=\"yyyy-MM-dd\">-->\n<!--                  </el-date-picker>-->\n<!--                </el-form-item>-->\n<!--              </el-col>-->\n\n\n<!--              <el-col :span=\"8\">-->\n<!--                <el-form-item label=\"基础形式\" prop=\"jcxs\">-->\n<!--                  <el-select v-model=\"jbxxForm.jcxs\" placeholder=\"请选择基础设施\">-->\n<!--                  </el-select>-->\n<!--                </el-form-item>-->\n<!--              </el-col>-->\n\n\n\n<!--              <el-col :span=\"8\">-->\n<!--                <el-form-item label=\"生产厂家\" prop=\"sccj\">-->\n<!--                  <el-input v-model=\"jbxxForm.sccj\" placeholder=\"请输入生产厂家\"></el-input>-->\n<!--                </el-form-item>-->\n<!--              </el-col>-->\n          </el-form>\n        </el-tab-pane>\n\n        <!--设备--><!--暂时不要了 2022.06.16-->\n        <!--<el-tab-pane label=\"设备\" name=\"sbjscs\">\n          <el-tabs v-model=\"gtgjsbTabName\" type=\"card\">\n            &lt;!&ndash;基础&ndash;&gt;\n            <el-tab-pane label=\"基础\" name=\"jc\">\n              &lt;!&ndash;查询及列表&ndash;&gt;\n              <el-col :span=\"24\">\n                <el-card class=\"box-card\">\n                  <el-filter\n                    ref=\"filter1\"\n                    :data=\"filterInfoJC.data\"\n                    :field-list=\"filterInfoJC.fieldList\"\n                    @handleReset=\"filterReset\"\n                    :width=\"{ labelWidth: 140, itemWidth: 190 }\"\n                  />\n                  <el-white class=\"button-group\">\n                    <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n                      <el-col\n                        style=\"\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\">\n                        <div>\n                          <el-button icon=\"el-icon-plus\" type=\"primary\" @click=\"JcClick\">新增</el-button>\n                          <el-button icon=\"el-icon-delete\" type=\"danger\" @click=\"JcDelete\">删除</el-button>\n                        </div>\n                      </el-col>\n                    </el-row>\n                  </el-white>\n                  <comp-table :table-and-page-info=\"tableAndPageInfoJC\" @update:multipleSelection=\"selectChange\"/>\n                </el-card>\n              </el-col>\n              &lt;!&ndash;列表页的基础弹出框展示&ndash;&gt;\n              <el-dialog :title=\"jctitle\" :visible.sync=\"JcDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"50%\"\n                         append-to-body>\n                &lt;!&ndash;基本信息&ndash;&gt;\n                <el-form :model=\"jcForm\" label-width=\"100px\">\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线路\" prop=\"ssxl\">\n                        <el-select v-model=\"jcForm.ssxl\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线段\" prop=\"ssxd\">\n                        <el-select v-model=\"jcForm.ssxd\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属杆塔\" prop=\"ssgt\">\n                        <el-select v-model=\"jcForm.ssgt\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"基础型式\" prop=\"jcxs\">\n                        <el-select v-model=\"jcForm.jcxs\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"基础材质\" prop=\"jccz\">\n                        <el-select v-model=\"jcForm.jccz\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"竣工日期\" prop=\"jgrq\">\n                        <el-date-picker\n                          v-model=\"jcForm.jgrq\"\n                          :disabled=\"isDisabled\"\n                          type=\"date\"\n                          placeholder=\"选择日期\">\n                        </el-date-picker>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"记录人\" prop=\"jlr\">\n                        <el-select v-model=\"jcForm.jlr\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                </el-form>\n\n                <div slot=\"footer\" class=\"dialog-footer\">\n                  <el-button @click=\"JcDialogFormVisible = false\">取 消</el-button>\n                  <el-button v-if=\"jctitle=='基础增加' || jctitle=='基础修改'\" type=\"primary\" @click=\"JcSubmit\"\n                             class=\"pmyBtn\">确 定</el-button>\n                </div>\n\n              </el-dialog>\n            </el-tab-pane>\n\n            &lt;!&ndash;绝缘子&ndash;&gt;\n            <el-tab-pane label=\"绝缘子\" name=\"jyz\">\n              &lt;!&ndash;查询及列表&ndash;&gt;\n              <el-col :span=\"24\">\n                <el-card class=\"box-card\">\n                  <el-filter\n                    ref=\"filter1\"\n                    :data=\"filterInfoJyz.data\"\n                    :field-list=\"filterInfoJyz.fieldList\"\n                    @handleReset=\"filterReset\"\n                    :width=\"{ labelWidth: 140, itemWidth: 190 }\"\n                  />\n                  <el-white class=\"button-group\">\n                    <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n                      <el-col\n                        style=\"\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\">\n                        <div>\n                          <el-button icon=\"el-icon-plus\" type=\"primary\" @click=\"JyzClick\">新增</el-button>\n                          <el-button icon=\"el-icon-delete\" type=\"danger\" @click=\"JyzDelete\">删除</el-button>\n                        </div>\n                      </el-col>\n                    </el-row>\n                  </el-white>\n                  <comp-table :table-and-page-info=\"tableAndPageInfoJyz\" @update:multipleSelection=\"selectChange\"/>\n                </el-card>\n              </el-col>\n              &lt;!&ndash;列表页的基础弹出框展示&ndash;&gt;\n              <el-dialog :title=\"jyztitle\" :visible.sync=\"JyzDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"50%\"\n                         append-to-body>\n                &lt;!&ndash;基本信息&ndash;&gt;\n                <el-form :model=\"jyzForm\" label-width=\"100px\">\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线路\" prop=\"ssxl\">\n                        <el-select v-model=\"jyzForm.ssxl\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线段\" prop=\"ssxd\">\n                        <el-select v-model=\"jyzForm.ssxd\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属杆塔\" prop=\"ssgt\">\n                        <el-select v-model=\"jyzForm.ssgt\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"绝缘子数量\" prop=\"jyzsl\">\n                        <el-input v-model=\"jyzForm.jyzsl\" :disabled=\"isDisabled\" placeholder=\"(个)\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"绝缘子型号\" prop=\"jyzxh\">\n                        <el-input v-model=\"jyzForm.jyzxh\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"绝缘子型式 \" prop=\"jyzxs\">\n                        <el-input v-model=\"jyzForm.jyzxs\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"绝缘子材料\" prop=\"jyzcl\">\n                        <el-input v-model=\"jyzForm.jyzcl\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"记录人\" prop=\"jlr\">\n                        <el-input v-model=\"jyzForm.jlr\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"生产厂家 \" prop=\"sccj\">\n                        <el-input v-model=\"jyzForm.sccj\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                </el-form>\n\n                <div slot=\"footer\" class=\"dialog-footer\">\n                  <el-button @click=\"JyzDialogFormVisible = false\">取 消</el-button>\n                  <el-button v-if=\"jyztitle=='绝缘子增加' || jyztitle=='绝缘子修改'\" type=\"primary\" @click=\"JyzSubmit\"\n                             class=\"pmyBtn\">确 定\n                  </el-button>\n                </div>\n\n              </el-dialog>\n            </el-tab-pane>\n\n            &lt;!&ndash;接地&ndash;&gt;\n            <el-tab-pane label=\"接地\" name=\"jd\">\n              &lt;!&ndash;查询及列表&ndash;&gt;\n              <el-col :span=\"24\">\n                <el-card class=\"box-card\">\n                  <el-filter\n                    ref=\"filter1\"\n                    :data=\"filterInfoJd.data\"\n                    :field-list=\"filterInfoJd.fieldList\"\n                    @handleReset=\"filterReset\"\n                    :width=\"{ labelWidth: 140, itemWidth: 190 }\"\n                  />\n                  <el-white class=\"button-group\">\n                    <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n                      <el-col\n                        style=\"\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\">\n                        <div>\n                          <el-button icon=\"el-icon-plus\" type=\"primary\" @click=\"JdClick\">新增</el-button>\n                          <el-button icon=\"el-icon-delete\" type=\"danger\" @click=\"JdDelete\">删除</el-button>\n                        </div>\n                      </el-col>\n                    </el-row>\n                  </el-white>\n                  <comp-table :table-and-page-info=\"tableAndPageInfoJd\" @update:multipleSelection=\"selectChange\"/>\n                </el-card>\n              </el-col>\n              &lt;!&ndash;列表页的基础弹出框展示&ndash;&gt;\n              <el-dialog :title=\"jdtitle\" :visible.sync=\"JdDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"50%\"\n                         append-to-body>\n                &lt;!&ndash;基本信息&ndash;&gt;\n                <el-form :model=\"jdForm\" label-width=\"100px\">\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线路\" prop=\"ssxl\">\n                        <el-select v-model=\"jdForm.ssxl\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线段\" prop=\"ssxd\">\n                        <el-select v-model=\"jdForm.ssxd\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属杆塔\" prop=\"ssgt\">\n                        <el-select v-model=\"jdForm.ssgt\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"接地材质\" prop=\"jdcz\">\n                        <el-input v-model=\"jdForm.jdcz\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"接地类型\" prop=\"jdlx\">\n                        <el-input v-model=\"jdForm.jdlx\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"接地极数量\" prop=\"jdjsl\">\n                        <el-input v-model=\"jdForm.jdjsl\" :disabled=\"isDisabled\" placeholder=\"(个)\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"接地电阻值\" prop=\"jddzz\">\n                        <el-input v-model=\"jdForm.jddzz\" :disabled=\"isDisabled\" placeholder=\"(Ω)\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"记录人\" prop=\"jlr\">\n                        <el-input v-model=\"jdForm.jlr\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                </el-form>\n\n                <div slot=\"footer\" class=\"dialog-footer\">\n                  <el-button @click=\"JdDialogFormVisible = false\">取 消</el-button>\n                  <el-button v-if=\"jdtitle=='接地增加' || jdtitle=='接地修改'\" type=\"primary\" @click=\"JdSubmit\" class=\"pmyBtn\">确\n                    定\n                  </el-button>\n                </div>\n\n              </el-dialog>\n            </el-tab-pane>\n\n            &lt;!&ndash;金具&ndash;&gt;\n            <el-tab-pane label=\"金具\" name=\"jj\">\n              &lt;!&ndash;查询及列表&ndash;&gt;\n              <el-col :span=\"24\">\n                <el-card class=\"box-card\">\n                  <el-filter\n                    ref=\"filter1\"\n                    :data=\"filterInfoJj.data\"\n                    :field-list=\"filterInfoJj.fieldList\"\n                    @handleReset=\"filterReset\"\n                    :width=\"{ labelWidth: 140, itemWidth: 190 }\"\n                  />\n                  <el-white class=\"button-group\">\n                    <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n                      <el-col\n                        style=\"\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\">\n                        <div>\n                          <el-button icon=\"el-icon-plus\" type=\"primary\" @click=\"JjClick\">新增</el-button>\n                          <el-button icon=\"el-icon-delete\" type=\"danger\" @click=\"JjDelete\">删除</el-button>\n                        </div>\n                      </el-col>\n                    </el-row>\n                  </el-white>\n                  <comp-table :table-and-page-info=\"tableAndPageInfoJj\" @update:multipleSelection=\"selectChange\"/>\n                </el-card>\n              </el-col>\n              &lt;!&ndash;列表页的基础弹出框展示&ndash;&gt;\n              <el-dialog :title=\"jjtitle\" :visible.sync=\"JjDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"50%\"\n                         append-to-body>\n                &lt;!&ndash;基本信息&ndash;&gt;\n                <el-form :model=\"jjForm\" label-width=\"100px\">\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线路\" prop=\"ssxl\">\n                        <el-select v-model=\"jjForm.ssxl\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线段\" prop=\"ssxd\">\n                        <el-select v-model=\"jjForm.ssxd\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属杆塔\" prop=\"ssgt\">\n                        <el-select v-model=\"jjForm.ssgt\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"描述\" prop=\"ms\">\n                        <el-input v-model=\"jjForm.ms\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"金具型号规格\" prop=\"jjxhgg\">\n                        <el-input v-model=\"jjForm.jjxhgg\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"金具类别\" prop=\"jjlb\">\n                        <el-input v-model=\"jjForm.jjlb\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"数量(个)\" prop=\"sl\">\n                        <el-input v-model=\"jjForm.sl\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"记录人\" prop=\"jlr\">\n                        <el-input v-model=\"jjForm.jlr\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"生产厂家\" prop=\"sccj\">\n                        <el-input v-model=\"jjForm.sccj\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                </el-form>\n\n                <div slot=\"footer\" class=\"dialog-footer\">\n                  <el-button @click=\"JjDialogFormVisible = false\">取 消</el-button>\n                  <el-button v-if=\"jjtitle=='金具增加' || jjtitle=='金具修改'\" type=\"primary\" @click=\"JjSubmit\" class=\"pmyBtn\">确\n                    定\n                  </el-button>\n                </div>\n\n              </el-dialog>\n            </el-tab-pane>\n\n            &lt;!&ndash;拉线&ndash;&gt;\n            <el-tab-pane label=\"拉线\" name=\"lx\">\n              &lt;!&ndash;查询及列表&ndash;&gt;\n              <el-col :span=\"24\">\n                <el-card class=\"box-card\">\n                  <el-filter\n                    ref=\"filter1\"\n                    :data=\"filterInfoLx.data\"\n                    :field-list=\"filterInfoLx.fieldList\"\n                    @handleReset=\"filterReset\"\n                    :width=\"{ labelWidth: 140, itemWidth: 190 }\"\n                  />\n                  <el-white class=\"button-group\">\n                    <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n                      <el-col\n                        style=\"\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\">\n                        <div>\n                          <el-button icon=\"el-icon-plus\" type=\"primary\" @click=\"LxClick\">新增</el-button>\n                          <el-button icon=\"el-icon-delete\" type=\"danger\" @click=\"LxDelete\">删除</el-button>\n                        </div>\n                      </el-col>\n                    </el-row>\n                  </el-white>\n                  <comp-table :table-and-page-info=\"tableAndPageInfoLx\" @update:multipleSelection=\"selectChange\"/>\n                </el-card>\n              </el-col>\n              &lt;!&ndash;列表页的基础弹出框展示&ndash;&gt;\n              <el-dialog :title=\"lxtitle\" :visible.sync=\"LxDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"50%\"\n                         append-to-body>\n                &lt;!&ndash;基本信息&ndash;&gt;\n                <el-form :model=\"lxForm\" label-width=\"100px\">\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线路\" prop=\"ssxl\">\n                        <el-select v-model=\"lxForm.ssxl\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线段\" prop=\"ssxd\">\n                        <el-select v-model=\"lxForm.ssxd\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属杆塔\" prop=\"ssgt\">\n                        <el-select v-model=\"lxForm.ssgt\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"拉线型式\" prop=\"lxxs\">\n                        <el-input v-model=\"lxForm.lxxs\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"拉线数量\" prop=\"lxsl\">\n                        <el-input v-model=\"lxForm.lxsl\" :disabled=\"isDisabled\" placeholder=\"(个)\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"戗杆规格\" prop=\"qggg\">\n                        <el-input v-model=\"lxForm.qggg\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"拉线棒规格\" prop=\"lxbgg\">\n                        <el-input v-model=\"lxForm.lxbgg\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"钢绞线规格\" prop=\"gjxgg\">\n                        <el-input v-model=\"lxForm.gjxgg\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"拉线盘规格\" prop=\"lxpgg\">\n                        <el-input v-model=\"lxForm.lxpgg\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"拉线绝缘子规格\" prop=\"lxjyzgg\">\n                        <el-input v-model=\"lxForm.lxjyzgg\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"记录人\" prop=\"jlr\">\n                        <el-input v-model=\"lxForm.jlr\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                </el-form>\n\n                <div slot=\"footer\" class=\"dialog-footer\">\n                  <el-button @click=\"LxDialogFormVisible = false\">取 消</el-button>\n                  <el-button v-if=\"lxtitle=='拉线增加' || lxtitle=='拉线修改'\" type=\"primary\" @click=\"LxSubmit\" class=\"pmyBtn\">确 定</el-button>\n                </div>\n\n              </el-dialog>\n            </el-tab-pane>\n\n            &lt;!&ndash;附属&ndash;&gt;\n            <el-tab-pane label=\"附属\" name=\"fs\">\n              &lt;!&ndash;查询及列表&ndash;&gt;\n              <el-col :span=\"24\">\n                <el-card class=\"box-card\">\n                  <el-filter\n                    ref=\"filter1\"\n                    :data=\"filterInfoFs.data\"\n                    :field-list=\"filterInfoFs.fieldList\"\n                    @handleReset=\"filterReset\"\n                    :width=\"{ labelWidth: 140, itemWidth: 190 }\"\n                  />\n                  <el-white class=\"button-group\">\n                    <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n                      <el-col\n                        style=\"\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\">\n                        <div>\n                          <el-button icon=\"el-icon-plus\" type=\"primary\" @click=\"FsClick\">新增</el-button>\n                          <el-button icon=\"el-icon-delete\" type=\"danger\" @click=\"FsDelete\">删除</el-button>\n                        </div>\n                      </el-col>\n                    </el-row>\n                  </el-white>\n                  <comp-table :table-and-page-info=\"tableAndPageInfoFs\" @update:multipleSelection=\"selectChange\"/>\n                </el-card>\n              </el-col>\n              &lt;!&ndash;列表页的基础弹出框展示&ndash;&gt;\n              <el-dialog :title=\"fstitle\" :visible.sync=\"FsDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"50%\"\n                         append-to-body>\n                &lt;!&ndash;基本信息&ndash;&gt;\n                <el-form :model=\"fsForm\" label-width=\"100px\">\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线路\" prop=\"ssxl\">\n                        <el-select v-model=\"fsForm.ssxl\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线段\" prop=\"ssxd\">\n                        <el-select v-model=\"fsForm.ssxd\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"设备名称\" prop=\"sbmc\">\n                        <el-select v-model=\"fsForm.sbmc\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"附属类别\" prop=\"fslb\">\n                        <el-input v-model=\"fsForm.fslb\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"投运日期\" prop=\"tyrq\">\n                        <el-date-picker\n                          v-model=\"fsForm.tyrq\"\n                          :disabled=\"isDisabled\"\n                          type=\"date\"\n                          placeholder=\"选择日期\">\n                        </el-date-picker>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"数量 \" prop=\"sl\">\n                        <el-input v-model=\"fsForm.sl\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"生产厂家\" prop=\"sccj\">\n                        <el-input v-model=\"fsForm.sccj\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"出厂日期\" prop=\"ccrq\">\n                        <el-date-picker\n                          v-model=\"fsForm.ccrq\"\n                          :disabled=\"isDisabled\"\n                          type=\"date\"\n                          placeholder=\"选择日期\">\n                        </el-date-picker>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"记录人\" prop=\"jlr\">\n                        <el-input v-model=\"fsForm.jlr\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                </el-form>\n\n                <div slot=\"footer\" class=\"dialog-footer\">\n                  <el-button @click=\"FsDialogFormVisible = false\">取 消</el-button>\n                  <el-button v-if=\"fstitle=='附属增加' || fstitle=='附属修改'\" type=\"primary\" @click=\"FsSubmit\" class=\"pmyBtn\">确\n                    定\n                  </el-button>\n                </div>\n\n              </el-dialog>\n            </el-tab-pane>\n\n            &lt;!&ndash;同杆并架&ndash;&gt;\n            &lt;!&ndash;<el-tab-pane label=\"同杆并架\" name=\"tgbj\">\n              <el-card>\n                <el-form :model=\"queryParams\" ref=\"queryForm\" v-show=\"showSearch\" :inline=\"true\" class=\"form_box\">\n                  <el-form-item label=\"电压等级\" prop=\"roleName\">\n                    <el-select v-model=\"form.ssxl\" placeholder=\"请选择电压等级\" size=\"small\">\n                      <el-option label=\"110kV\" value=\"10\"></el-option>\n                      <el-option label=\"35kV\" value=\"11\"></el-option>\n                      <el-option label=\"10kV\" value=\"12\"></el-option>\n                      <el-option label=\"6kV\" value=\"13\"></el-option>\n                    </el-select>\n                  </el-form-item>\n                  <el-form-item label=\"设备状态\" prop=\"roleName\">\n                    <el-select v-model=\"form.sbzt\" placeholder=\"请选择设备状态\" size=\"small\">\n                      <el-option\n                        v-for=\"item in sbzt\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\">\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                  <el-form-item class=\"mb8 pull-right\">\n                    <el-button type=\"cyan\" icon=\"el-icon-search\" style=\"margin-left: 5px;\" size=\"small\">搜索\n                    </el-button>\n                    <el-button icon=\"el-icon-refresh\" size=\"small\">重置</el-button>\n                  </el-form-item>\n                  <el-form>\n                    <el-form-item style=\"float: right\">\n                      <el-button class=\"mb8\" @click=\"bdzAddSensorButton\" type=\"primary\">添加</el-button>\n                      <el-button class=\"mb8\" type=\"warning\">修改</el-button>\n                      <el-button class=\"mb8\" type=\"danger\">删除</el-button>\n                    </el-form-item>\n                  </el-form>\n                </el-form>\n              </el-card>\n              <el-card class=\"box-cardList\" shadow=\"never\">\n                <el-table stripe border v-loading=\"loading\" max-height=\"300\">\n                  <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n                  <el-table-column label=\"所属线路\" align=\"center\" prop=\"ssxl\"/>\n                  <el-table-column label=\"电压等级\" align=\"center\" prop=\"dydj\" :show-overflow-tooltip=\"true\"/>\n                  <el-table-column label=\"设备状态\" align=\"center\" prop=\"sbzt\" :show-overflow-tooltip=\"true\"/>\n                  <el-table-column label=\"杆塔材质\" align=\"center\" prop=\"gtcz\"/>\n                  <el-table-column label=\"负责人\" align=\"center\" prop=\"fzr\" :show-overflow-tooltip=\"true\"/>\n                  <el-table-column label=\"投运日期\" align=\"center\" prop=\"tyrq\" :show-overflow-tooltip=\"true\"/>\n                  <el-table-column\n                    align=\"center\"\n                    label=\"操作\"\n                    width=\"100\">\n                    <template slot-scope=\"scope\">\n                      <el-button @click=\"bdzAddSensorButton(scope.row)\" type=\"text\" size=\"small\">详情</el-button>\n                    </template>\n                  </el-table-column>\n                </el-table>\n              </el-card>\n              <el-pagination\n                style=\"float: right;margin-top: 10px\"\n                @size-change=\"handleSizeChange\"\n                @current-change=\"handleCurrentChange\"\n                :current-page=\"1\"\n                :page-sizes=\"[100, 200, 300, 400]\"\n                :page-size=\"100\"\n                layout=\"total, sizes, prev, pager, next, jumper\"\n                :total=\"400\">\n              </el-pagination>\n            </el-tab-pane>&ndash;&gt;\n\n            &lt;!&ndash;横担&ndash;&gt;\n            <el-tab-pane label=\"横担\" name=\"hd\">\n              &lt;!&ndash;查询及列表&ndash;&gt;\n              <el-col :span=\"24\">\n                <el-card class=\"box-card\">\n                  <el-filter\n                    ref=\"filter1\"\n                    :data=\"filterInfoHd.data\"\n                    :field-list=\"filterInfoHd.fieldList\"\n                    @handleReset=\"filterReset\"\n                    :width=\"{ labelWidth: 140, itemWidth: 190 }\"\n                  />\n                  <el-white class=\"button-group\">\n                    <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n                      <el-col\n                        style=\"\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\">\n                        <div>\n                          <el-button icon=\"el-icon-plus\" type=\"primary\" @click=\"HdClick\">新增</el-button>\n                          <el-button icon=\"el-icon-delete\" type=\"danger\" @click=\"HdDelete\">删除</el-button>\n                        </div>\n                      </el-col>\n                    </el-row>\n                  </el-white>\n                  <comp-table :table-and-page-info=\"tableAndPageInfoHd\" @update:multipleSelection=\"selectChange\"/>\n                </el-card>\n              </el-col>\n              &lt;!&ndash;列表页的基础弹出框展示&ndash;&gt;\n              <el-dialog :title=\"hdtitle\" :visible.sync=\"HdDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"50%\"\n                         append-to-body>\n                &lt;!&ndash;基本信息&ndash;&gt;\n                <el-form :model=\"hdForm\" label-width=\"100px\">\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线路\" prop=\"ssxl\">\n                        <el-select v-model=\"hdForm.ssxl\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线段\" prop=\"ssxd\">\n                        <el-select v-model=\"hdForm.ssxd\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属杆塔\" prop=\"ssgt\">\n                        <el-select v-model=\"hdForm.ssgt\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"横担类型\" prop=\"hdlx\">\n                        <el-input v-model=\"hdForm.hdlx\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"数量\" prop=\"sl\">\n                        <el-input v-model=\"hdForm.sl\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"生产厂家\" prop=\"sccj\">\n                        <el-input v-model=\"hdForm.sccj\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"记录人\" prop=\"jl\">\n                        <el-input v-model=\"hdForm.jl\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                </el-form>\n\n                <div slot=\"footer\" class=\"dialog-footer\">\n                  <el-button @click=\"HdDialogFormVisible = false\">取 消</el-button>\n                  <el-button v-if=\"hdtitle=='横担增加' || hdtitle=='横担修改'\" type=\"primary\" @click=\"HdSubmit\" class=\"pmyBtn\">确 定\n                  </el-button>\n                </div>\n\n              </el-dialog>\n            </el-tab-pane>\n          </el-tabs>\n        </el-tab-pane>-->\n\n        <!--设备履历-->\n        <el-tab-pane label=\"设备履历\" name=\"sbRecord\">\n          <!--@tab-click=\"handleSbllDescTabNameClick\"-->\n          <el-tabs v-model=\"sbllDescTabName\" type=\"card\">\n            <el-tab-pane label=\"缺陷记录\" name=\"qxjl\">\n              <el-table stripe border v-loading=\"loading\" :data=\"sbllqxjlList\" max-height=\"550\">\n                <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n                <el-table-column label=\"所属线路\" align=\"center\" prop=\"lineName\"/>\n                <el-table-column label=\"杆塔号\" align=\"center\" prop=\"gth\" :show-overflow-tooltip=\"true\"/>\n                <el-table-column label=\"设备类型\" align=\"center\" prop=\"sblx\" :show-overflow-tooltip=\"true\"/>\n                <el-table-column label=\"缺陷性质\" align=\"center\" prop=\"qxxz\" :show-overflow-tooltip=\"true\"/>\n                <el-table-column label=\"电压等级\" align=\"center\" prop=\"dydj\" :show-overflow-tooltip=\"true\"/>\n                <el-table-column label=\"设备型号\" align=\"center\" prop=\"sbxh\"/>\n                <el-table-column label=\"生产厂家\" align=\"center\" prop=\"sccj\" :show-overflow-tooltip=\"true\"/>\n              </el-table>\n            </el-tab-pane>\n            <el-tab-pane label=\"状态变更记录\" name=\"ztbgjl\">\n              <comp-table\n                :table-and-page-info=\"resumPageInfo\"\n                @getMethod=\"getResumList\"\n                @update:multipleSelection=\"handleSelectionChange\"\n                height=\"30vh\"\n              />\n            </el-tab-pane>\n          </el-tabs>\n        </el-tab-pane>\n\n      </el-tabs>\n      <div slot=\"footer\" class=\"dialog-footer\" v-show=\"!show\">\n        <el-button @click=\"handleClose\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"addGtInfo\">确 定</el-button>\n      </div>\n    </el-dialog>\n    <!--设备状态变更弹框展示-->\n    <el-dialog title=\"设备状态变更\" :visible.sync=\"dialogVisible\" width=\"30%\" append-to-body>\n      <el-form :inline=\"true\" label-width=\"100px\" class=\"qxlr_dialog_insert\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"设备状态:\">\n              <el-select v-model=\"updateList.sbzt\">\n                <el-option\n                  v-for=\"item in sbzt\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitStatus\">确 定</el-button>\n      </div>\n    </el-dialog>\n\n\n  </div>\n\n</template>\n\n<script>\n  import { getToken } from \"@/utils/auth\";\n  import { getListgt, gtremove, saveOrUpdategt, getTreeList, updateStatus } from '@/api/dagangOilfield/asset/sdgt'\n  import { getResumDataList } from '@/api/dagangOilfield/asset/sdsb'\n  import { getListJc, removeJc, saveOrUpdateJc } from '@/api/dagangOilfield/dwzygl/sdsbgl/sdgtjc'\n  import { getListJyz, removeJyz, saveOrUpdateJyz } from '@/api/dagangOilfield/dwzygl/sdsbgl/sdgtjyz'\n  import { getListJd, removeJd, saveOrUpdateJd } from '@/api/dagangOilfield/dwzygl/sdsbgl/sdgtjd'\n  import { getListJj, removeJj, saveOrUpdateJj } from '@/api/dagangOilfield/dwzygl/sdsbgl/sdgtjj'\n  import { getListLx, removeLx, saveOrUpdateLx } from '@/api/dagangOilfield/dwzygl/sdsbgl/sdgtlx'\n  import { getListFs, removeFs, saveOrUpdateFs } from '@/api/dagangOilfield/dwzygl/sdsbgl/sdgtfs'\n  import { getListHd, removeHd, saveOrUpdateHd } from '@/api/dagangOilfield/dwzygl/sdsbgl/sdgthd'\n  import {deleteById, getListByBusinessId} from \"@/api/tool/file\";\n\n  export default {\n    name: 'sdgt',\n    data() {\n      return {\n        uploadData: {\n          type:\"\",\n          businessId: undefined\n        },\n        //杆塔形状结合\n        gtxzOptions:[\n          {label:'4字型',value:'4字型'},\n          {label:'A型',value:'A型'},\n          {label:'三角形',value:'三角形'},\n          {label:'上字型',value:'上字型'},\n          {label:'伞型终端',value:'伞型终端'},\n          {label:'倒伞型',value:'倒伞型'},\n          {label:'分歧型',value:'分歧型'},\n          {label:'分崎型',value:'分崎型'},\n          {label:'叉骨型换位塔',value:'叉骨型换位塔'},\n          {label:'干字型',value:'干字型'},\n          {label:'拉V型',value:'拉V型'},\n          {label:'桥型',value:'桥型'},\n          {label:'猫头型',value:'猫头型'},\n          {label:'紧凑型',value:'紧凑型'},\n          {label:'羊角型',value:'羊角型'},\n          {label:'酒杯型',value:'酒杯型'},\n          {label:'门型',value:'门型'},\n          {label:'门型拉线塔',value:'门型拉线塔'},\n          {label:'鼓型塔',value:'鼓型塔'},\n        ],\n        // 多选框选中的id\n        ids: [],\n        //是否禁用\n        isDisabled: false,\n        // 单击tab选中的节点\n        selectNode: 'jc',\n        //基础标题\n        jctitle: '',\n        //绝缘子标题\n        jyztitle: '',\n        //导线标题\n        daoxtitle: '',\n        //地线标题\n        jdtitle: '',\n        //金具标题\n        jjtitle: '',\n        //拉线标题\n        lxtitle: '',\n        //附属标题\n        fstitle: '',\n        //同杆并架标题\n        tgbjtitle: '',\n        //横担标题\n        hdtitle: '',\n\n        //基础弹出框是否弹出\n        JcDialogFormVisible: false,\n        //绝缘子弹出框是否弹出\n        JyzDialogFormVisible: false,\n        //导线弹出框是否弹出\n        DaoxDialogFormVisible: false,\n        //接地弹出框是否弹出\n        JdDialogFormVisible: false,\n        //金具弹出框是否弹出\n        JjDialogFormVisible: false,\n        //拉线弹出框是否弹出\n        LxDialogFormVisible: false,\n        //附属弹出框是否弹出\n        FsDialogFormVisible: false,\n        //同杆并架弹出框是否弹出\n        TgbjDialogFormVisible: false,\n        //横担弹出框是否弹出\n        HdDialogFormVisible: false,\n\n        //基础form\n        jcForm: {\n          ssxl: '',\n          ssxd: '',\n          ssgt: '',\n          jcxs: '',\n          jccz: '',\n          jlr: '',\n          jgrq: ''\n        },\n        //绝缘子form\n        jyzForm: {\n          ssxl: '',\n          ssxd: '',\n          ssgt: '',\n          jyzsl: '',\n          jyzxh: '',\n          jyzxs: '',\n          jyzcl: '',\n          jlr: '',\n          sccj: ''\n        },\n        //导线form\n        daoxForm: {\n          ssxd: '',\n          ssgt: '',\n          jcxs: '',\n          jccz: '',\n          jlr: '',\n          jgrq: ''\n        },\n        //接地form\n        jdForm: {\n          ssxl: '',\n          ssxd: '',\n          ssgt: '',\n          jdcz: '',\n          jdlx: '',\n          jdjsl: '',\n          jddzz: '',\n          jlr: ''\n        },\n        //金具form\n        jjForm: {\n          ssxl: '',\n          ssxd: '',\n          ssgt: '',\n          ms: '',\n          jjxhgg: '',\n          jjlb: '',\n          sl: '',\n          jlr: '',\n          sccj: ''\n        },\n        //拉线form\n        lxForm: {\n          ssxl: '',\n          ssxd: '',\n          ssgt: '',\n          lxxs: '',\n          lxsl: '',\n          qggg: '',\n          lxbgg: '',\n          gjxgg: '',\n          lxpgg: '',\n          lxjyzgg: '',\n          jlr: ''\n        },\n        //附属form\n        fsForm: {\n          ssxl: '',\n          ssxd: '',\n          sbmc: '',\n          fslb: '',\n          tyrq: '',\n          sl: '',\n          sccj: '',\n          ccrq: '',\n          jlr: ''\n        },\n        //同杆并架form\n        tgbjForm: {\n          ssxd: '',\n          ssgt: '',\n          jcxs: '',\n          jccz: '',\n          jlr: '',\n          jgrq: ''\n        },\n        //横担form\n        hdForm: {\n          ssxl: '',\n          ssxd: '',\n          ssgt: '',\n          hdlx: '',\n          sl: '',\n          sccj: '',\n          jlr: ''\n        },\n        resumeQuery: {\n          foreignNum: undefined,\n          sblx: undefined\n        },\n        updateList: {\n          sbzt: '',\n          objId: ''\n        },\n        dialogVisible: false,\n        options: [{\n          value: '110kV',\n          label: '110kV'\n        }, {\n          value: '35kV',\n          label: '35kV'\n        }, {\n          value: '10kV',\n          label: '10kV'\n        }, {\n          value: '6kV',\n          label: '6kV'\n        }], sbzt: [{\n          value: '在运',\n          label: '在运'\n        }, {\n          value: '停运',\n          label: '停运'\n        }, {\n          value: '报废',\n          label: '报废'\n        }],\n        //筛选条件\n        filterInfos: {},\n        //列表信息查看\n        tableAndPageInfos: {},\n        /*---------------------基础--------------------*/\n        //输电杆塔设备基础\n        filterInfoJC: {\n          data: {\n            ssxl: '',\n            ssxd: '',\n            ssgt: '',\n            jcxs: '',\n            jccz: '',\n            jlr: '',\n            jgrq: '',\n          },\n          fieldList: [\n            { label: '所属线路', type: 'input', value: 'ssxl', },\n            { label: '所属杆塔', type: 'input', value: 'ssgt', }]\n        },\n        tableAndPageInfoJC: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n            { prop: 'ssxl', label: '所属线路', minWidth: '120' },\n            { prop: 'ssgt', label: '所属杆塔', minWidth: '100' },\n            { prop: 'jcxs', label: '基础型式', minWidth: '120' },\n            { prop: 'jccz', label: '基础材质', minWidth: '100' },\n            { prop: 'jlr', label: '记录人', minWidth: '80' },\n            { prop: 'jgrq', label: '竣工日期', minWidth: '90' },\n           /* {\n              fixed: 'right',\n              prop: 'operation',\n              label: '操作',\n              minWidth: '120px',\n              style: { display: 'block' },\n              operation: [\n                { name: '修改', clickFun: this.getJcUpdate },\n                { name: '详情', clickFun: this.getJcDetails }\n              ]\n            }*/\n          ],\n          paramJc: {\n            ssxl: '',\n            ssxd: '',\n            ssgt: '',\n            jcxs: '',\n            jccz: '',\n            jlr: '',\n            jgrq: ''\n          }\n        },\n        /*---------------------绝缘子--------------------*/\n        //输电杆塔绝缘子\n        filterInfoJyz: {\n          data: {\n            ssxd: '',\n            ssgt: '',\n            jyzsl: '',\n            jyzxh: '',\n            jyzxs: '',\n            jyzcl: '',\n            jlr: '',\n            sccj: ''\n          },\n          fieldList: [\n            { label: '所属线路', type: 'select', value: 'ssxl', multiple: true, options: [] },\n            { label: '所属线段', type: 'select', value: 'ssxd', multiple: true, options: [] }]\n        },\n        tableAndPageInfoJyz: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n            { prop: 'ssxl', label: '所属线路', minWidth: '100' },\n            { prop: 'ssxd', label: '所属线段', minWidth: '100' },\n            { prop: 'ssgt', label: '所属杆塔', minWidth: '70 ' },\n            { prop: 'jyzsl', label: '绝缘子数量(个)', minWidth: '60' },\n            { prop: 'jyzxh', label: '绝缘子型号 ', minWidth: '100' },\n            { prop: 'jyzxs', label: '绝缘子型式', minWidth: '75' },\n            /*{ prop: 'jyzcl', label: '绝缘子材料', minWidth: '80' },\n            { prop: 'jlr', label: '记录人', minWidth: '80' },\n            { prop: 'sccj', label: '生产厂家', minWidth: '100' },*/\n            {\n              fixed: 'right',\n              prop: 'operation',\n              label: '操作',\n              minWidth: '100',\n              style: { display: 'block' },\n              operation: [\n                { name: '修改', clickFun: this.getJyzUpdate },\n                { name: '详情', clickFun: this.getJyzDetails }\n              ]\n            }\n          ],\n          paramJyz: {\n            ssxd: '',\n            ssgt: '',\n            jyzsl: '',\n            jyzxh: '',\n            jyzxs: '',\n            jyzcl: '',\n            jlr: '',\n            sccj: ''\n          }\n        },\n        /*---------------------接地--------------------*/\n        //输电杆塔接地\n        filterInfoJd: {\n          data: {},\n          fieldList: [\n            { label: '所属线段', type: 'select', value: 'ssxd', multiple: true, options: [] },\n            { label: '所属杆塔', type: 'select', value: 'ssgt', multiple: true, options: [] }]\n        },\n        tableAndPageInfoJd: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n            { prop: 'ssxd', label: '所属线段', minWidth: '100' },\n            { prop: 'ssgt', label: '所属杆塔', minWidth: '100' },\n            { prop: 'jdcz', label: '接地材质', minWidth: '100' },\n            { prop: 'jdlx', label: '接地类型', minWidth: '80' },\n            { prop: 'jdjsl', label: '接地极数量(个)', minWidth: '60' },\n            /*{ prop: 'jddzz', label: '接地电阻值(Ω)', minWidth: '60' },*/\n            { prop: 'jlr', label: '记录人', minWidth: '70' },\n            {\n              fixed: 'right',\n              prop: 'operation',\n              label: '操作',\n              minWidth: '100',\n              style: { display: 'block' },\n              operation: [\n                { name: '修改', clickFun: this.getJdUpdate },\n                { name: '详情', clickFun: this.getJdDetails }\n              ]\n            }\n          ],\n          paramJd: {\n            ssxl: '',\n            ssxd: '',\n            ssgt: '',\n            jdcz: '',\n            jdlx: '',\n            jdjsl: '',\n            jddzz: '',\n            jlr: ''\n          }\n        },\n        /*---------------------金具--------------------*/\n        //输电杆塔接地\n        filterInfoJj: {\n          data: {},\n          fieldList: [\n            { label: '所属线路', type: 'select', value: 'ssxl', multiple: true, options: [] },\n            { label: '所属线段', type: 'select', value: 'ssxd', multiple: true, options: [] }]\n        },\n        tableAndPageInfoJj: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n            { prop: 'ssxl', label: '所属线路', minWidth: '100' },\n            { prop: 'ssxd', label: '所属线段', minWidth: '100' },\n            { prop: 'ssgt', label: '所属杆塔', minWidth: '100' },\n            { prop: 'jjxhgg', label: '金具型号规格', minWidth: '120' },\n            { prop: 'jjlb', label: '金具类别', minWidth: '80' },\n            { prop: 'sl', label: '数量(个)', minWidth: '80' },\n            /*{ prop: 'jlr', label: '记录人', minWidth: '80' },*/\n            {\n              fixed: 'right',\n              prop: 'operation',\n              label: '操作',\n              minWidth: '100',\n              style: { display: 'block' },\n              operation: [\n                { name: '修改', clickFun: this.getJjUpdate },\n                { name: '详情', clickFun: this.getJjDetails }\n              ]\n            }\n          ],\n          paramJj: {\n            ssxl: '',\n            ssxd: '',\n            ssgt: '',\n            ms: '',\n            jjxhgg: '',\n            jjlb: '',\n            sl: '',\n            jlr: '',\n            sccj: ''\n          }\n        },\n        /*---------------------拉线--------------------*/\n        //输电杆塔接地\n        filterInfoLx: {\n          data: {},\n          fieldList: [\n            { label: '所属线路', type: 'select', value: 'ssxl', multiple: true, options: [] },\n            { label: '所属线段', type: 'select', value: 'ssxd', multiple: true, options: [] }]\n        },\n        tableAndPageInfoLx: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n            { prop: 'ssxl', label: '所属线路', minWidth: '100' },\n            { prop: 'ssxd', label: '所属线段', minWidth: '100' },\n            { prop: 'ssgt', label: '所属杆塔', minWidth: '100' },\n            { prop: 'lxxs', label: '拉线型式', minWidth: '90' },\n            { prop: 'lxsl', label: '拉线数量', minWidth: '70' },\n            { prop: 'qggg', label: '戗杆规格', minWidth: '90' },\n            {\n              fixed: 'right',\n              prop: 'operation',\n              label: '操作',\n              minWidth: '100',\n              style: { display: 'block' },\n              operation: [\n                { name: '修改', clickFun: this.getLxUpdate },\n                { name: '详情', clickFun: this.getLxDetails }\n              ]\n            }\n          ],\n          paramLx: {\n            ssxl: '',\n            ssxd: '',\n            ssgt: '',\n            lxxs: '',\n            lxsl: '',\n            qggg: '',\n            lxbgg: '',\n            gjxgg: '',\n            lxpgg: '',\n            lxjyzgg: '',\n            jlr: ''\n          }\n        },\n        /*---------------------附属--------------------*/\n        //输电杆塔接地\n        filterInfoFs: {\n          data: {},\n          fieldList: [\n            { label: '所属线路', type: 'select', value: 'ssxl', multiple: true, options: [] },\n            { label: '所属线段', type: 'select', value: 'ssxd', multiple: true, options: [] }]\n        },\n        tableAndPageInfoFs: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n            { prop: 'ssxl', label: '所属线路', minWidth: '100' },\n            { prop: 'ssxd', label: '所属线段', minWidth: '100' },\n            { prop: 'sbmc', label: '设备名称', minWidth: '100' },\n            { prop: 'fslb', label: '附属类别', minWidth: '100' },\n            { prop: 'tyrq', label: '投运日期', minWidth: '80' },\n            { prop: 'sl', label: '数量', minWidth: '60' },\n            {\n              fixed: 'right',\n              prop: 'operation',\n              label: '操作',\n              minWidth: '100',\n              style: { display: 'block' },\n              operation: [\n                { name: '修改', clickFun: this.getFsUpdate },\n                { name: '详情', clickFun: this.getFsDetails }\n              ]\n            }\n          ],\n          paramFs: {\n            ssxl: '',\n            ssxd: '',\n            sbmc: '',\n            fslb: '',\n            tyrq: '',\n            sl: '',\n            sccj: '',\n            ccrq: '',\n            jlr: ''\n          }\n        },\n        /*---------------------横担--------------------*/\n        //输电杆塔接地\n        filterInfoHd: {\n          data: {},\n          fieldList: [\n            { label: '所属线路', type: 'select', value: 'ssxl', multiple: true, options: [] },\n            { label: '所属线段', type: 'select', value: 'ssxd', multiple: true, options: [] }]\n        },\n        tableAndPageInfoHd: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n            { prop: 'ssxl', label: '所属线路', minWidth: '100' },\n            { prop: 'ssxd', label: '所属线段', minWidth: '100' },\n            { prop: 'ssgt', label: '所属杆塔', minWidth: '100' },\n            { prop: 'hdlx', label: '横担类型', minWidth: '100' },\n            { prop: 'sl', label: '数量', minWidth: '100' },\n            { prop: 'sccj', label: '生产厂家', minWidth: '100' },\n            {\n              fixed: 'right',\n              prop: 'operation',\n              label: '操作',\n              minWidth: '100',\n              style: { display: 'block' },\n              operation: [\n                { name: '修改', clickFun: this.getHdUpdate },\n                { name: '详情', clickFun: this.getHdDetails }\n              ]\n            }\n          ],\n          paramHd: {\n            ssxl: '',\n            ssxd: '',\n            ssgt: '',\n            hdlx: '',\n            sl: '',\n            sccj: '',\n            jlr: ''\n          },\n        },\n        resumPageInfo: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n            { prop: 'foreignNum', label: '设备名称', minWidth: '120' },\n            { prop: 'sblx', label: '设备类型', minWidth: '180' },\n            { prop: 'bglx', label: '变更类型', minWidth: '120' },\n            { prop: 'ms', label: '描述', minWidth: '250' },\n            { prop: 'bgr', label: '变更人', minWidth: '140' },\n            { prop: 'bgsj', label: '变更时间', minWidth: '140' }\n          ]\n        },\n\n        //电压等级下拉框\n        voltageLevelListSelected: [],\n        filterInfo: {\n          data: {\n            sbzt: '',\n            lineName:'',\n            dydj:'',\n          },\n          fieldList: [\n            {\n              label: '所属线路', type: 'input', value: 'lineName', options: []\n            },\n            {\n              label: '电压等级', type: 'select', value: 'dydj', options: [ { label: '6kV', value: '6kV' },{ label: '10kV', value: '10kV' }, { label: '35kV', value: '35kV' }, { label: '110kV', value: '110kV' },\n               ]\n            },\n            {\n              label: '设备状态',\n              type: 'select',\n              value: 'sbzt',\n              options: [{ label: '在运', value: '在运' }, { label: '停运', value: '停运' }]\n            },\n          ]\n        },\n\n        tableAndPageInfo: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n            { prop: 'lineName', label: '所属线路', minWidth: '120' },\n            { prop: 'gtmc', label: '杆塔名称', minWidth: '130' },\n            { prop: 'gtbh', label: '杆塔编号', minWidth: '100' },\n            { prop: 'dydj', label: '电压等级', minWidth: '100' },\n            { prop: 'sbzt', label: '设备状态', minWidth: '50' },\n            { prop: 'gtcz', label: '杆塔材质', minWidth: '50' },\n            { prop: 'tyrq', label: '投运日期', minWidth: '120' },\n            {\n              fixed: 'right',\n              prop: 'operation',\n              label: '操作',\n              minWidth: '130px',\n              style: { display: 'block' },\n              operation: [\n                { name: '状态变更', clickFun: this.updateStatus },\n                { name: '修改', clickFun: this.updateRow },\n                { name: '详情', clickFun: this.detailsInfo }\n              ]\n            }\n          ]\n        },\n        //杆塔挂接设备tab页\n        gtgjsbTabName: 'jc',\n        //杆塔详情弹出框\n        gtDialogFormVisible: false,\n        //设备履历状态变更记录\n        sbllztbgjlList: [\n          {\n            ssgs: '110kVXXX线路',\n            bdzmc: '001',\n            sblx: '杆塔',\n            bglx: '状态变更',\n            desc: '状态变更为报废',\n            bgsj: '2022-12-12',\n            bgr: '张三'\n          }, {\n            ssgs: '110kVXXX线路',\n            bdzmc: '001',\n            sblx: '杆塔',\n            bglx: '状态变更',\n            desc: '状态变更为报废',\n            bgsj: '2022-12-12',\n            bgr: '张三'\n          }, {\n            ssgs: '110kVXXX线路',\n            bdzmc: '001',\n            sblx: '杆塔',\n            bglx: '状态变更',\n            desc: '状态变更为报废',\n            bgsj: '2022-12-12',\n            bgr: '张三'\n          }, {\n            ssgs: '110kVXXX线路',\n            bdzmc: '001',\n            sblx: '杆塔',\n            bglx: '状态变更',\n            desc: '状态变更为报废',\n            bgsj: '2022-12-12',\n            bgr: '张三'\n          }, {\n            ssgs: '110kVXXX线路',\n            bdzmc: '001',\n            sblx: '杆塔',\n            bglx: '状态变更',\n            desc: '状态变更为报废',\n            bgsj: '2022-12-12',\n            bgr: '张三'\n          }\n        ],\n        //设备履历缺陷记录数据集合\n        sbllqxjlList: [\n          {\n            ssgs: '110kVXXX线路',\n            gth: '001#',\n            sblx: '杆塔',\n            qxxz: '严重',\n            dydj: '35kV',\n            sbxh: 'XXX型号',\n            sccj: 'XXX厂家'\n          }, {\n            ssgs: '110kVXXX线路',\n            gth: '001#',\n            sblx: '杆塔',\n            qxxz: '严重',\n            dydj: '35kV',\n            sbxh: 'XXX型号',\n            sccj: 'XXX厂家'\n          }, {\n            ssgs: '110kVXXX线路',\n            gth: '001#',\n            sblx: '杆塔',\n            qxxz: '严重',\n            dydj: '35kV',\n            sbxh: 'XXX型号',\n            sccj: 'XXX厂家'\n          }, {\n            ssgs: '110kVXXX线路',\n            gth: '001#',\n            sblx: '杆塔',\n            qxxz: '严重',\n            dydj: '35kV',\n            sbxh: 'XXX型号',\n            sccj: 'XXX厂家'\n          }, {\n            ssgs: '110kVXXX线路',\n            gth: '001#',\n            sblx: '杆塔',\n            qxxz: '严重',\n            dydj: '35kV',\n            sbxh: 'XXX型号',\n            sccj: 'XXX厂家'\n          }\n        ],\n        //设备履历试验记录数据\n        sblvsyjlList: [\n          {\n            syzy: '带电',\n            syxz: '例行试验',\n            symc: 'XXXXX',\n            gzdd: 'XXX平台',\n            sysb: '主变压器',\n            sybg: '',\n            tq: '晴',\n            syrq: '2022-01-01',\n            lrr: '张三',\n            syjl: 'XXXXX'\n          }, {\n            syzy: '带电',\n            syxz: '例行试验',\n            symc: 'XXXXX',\n            gzdd: 'XXX平台',\n            sysb: '主变压器',\n            sybg: '',\n            tq: '晴',\n            syrq: '2022-01-01',\n            lrr: '张三',\n            syjl: 'XXXXX'\n          }, {\n            syzy: '带电',\n            syxz: '例行试验',\n            symc: 'XXXXX',\n            gzdd: 'XXX平台',\n            sysb: '主变压器',\n            sybg: '',\n            tq: '晴',\n            syrq: '2022-01-01',\n            lrr: '张三',\n            syjl: 'XXXXX'\n          }, {\n            syzy: '带电',\n            syxz: '例行试验',\n            symc: 'XXXXX',\n            gzdd: 'XXX平台',\n            sysb: '主变压器',\n            sybg: '',\n            tq: '晴',\n            syrq: '2022-01-01',\n            lrr: '张三',\n            syjl: 'XXXXX'\n          }, {\n            syzy: '带电',\n            syxz: '例行试验',\n            symc: 'XXXXX',\n            gzdd: 'XXX平台',\n            sysb: '主变压器',\n            sybg: '',\n            tq: '晴',\n            syrq: '2022-01-01',\n            lrr: '张三',\n            syjl: 'XXXXX'\n          }, {\n            syzy: '带电',\n            syxz: '例行试验',\n            symc: 'XXXXX',\n            gzdd: 'XXX平台',\n            sysb: '主变压器',\n            sybg: '',\n            tq: '晴',\n            syrq: '2022-01-01',\n            lrr: '张三',\n            syjl: 'XXXXX'\n          }\n        ],\n        //设备履历tab页\n        sbllDescTabName: 'qxjl',\n        //轮播图片\n        imgList: [\n          // {\n          //   url: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg'\n          // },\n        ],\n        show: false,\n        //上传图片时的请求头\n      header: {},\n        //设备基本信息\n        jbxxForm: {\n          attachment:[],\n          objId: undefined,\n          gtbh:'',\n          ssbm:'线路分公司'\n        },\n        //设备详情页底部确认取消按钮控制\n        sbCommitDialogCotrol: true,\n        //弹出框tab页\n        activeTabName: 'sbDesc',\n        //变电站展示\n        bdzShowTable: true,\n        //间隔展示\n        jgShowTable: false,\n        //设备展示\n        sbShowTable: false,\n        //设备弹出框\n        dialogFormVisible: false,\n        //变电站添加按钮弹出框\n        bdzDialogFormVisible: false,\n        //间隔添加按钮弹出框\n        jgDialogFormVisible: false,\n        //弹出框表单\n        form: {},\n\n        loading: false,\n        //组织树\n        treeOptions: [{\n          label: '大港油田电力公司',\n          id: '1',\n          pid: undefined,\n          identifier: '0',\n          children: [\n            {\n              label: '110kV',\n              id: '101',\n              pid: '1',\n              identifier: '1',\n              children: [\n                {\n                  label: '110kVXXXX线路',\n                  id: '1010101',\n                  pid: '1',\n                  identifier: '2',\n                  children: []\n                },\n                {\n                  label: '110kVXXXX线路',\n                  id: '1010102',\n                  pid: '1',\n                  identifier: '2',\n                  children: []\n                }\n              ]\n            },\n            {\n              label: '35kV',\n              id: '102',\n              pid: undefined,\n              identifier: '1',\n              children: []\n            },\n            {\n              label: '10kV',\n              id: '103',\n              pid: undefined,\n              identifier: '1',\n              children: []\n            },\n            {\n              label: '6kV',\n              id: '104',\n              pid: undefined,\n              identifier: '1',\n              children: []\n            }\n          ]\n        }],\n        //所有变电站数据集合\n        bdzList: [\n          {\n            ssxl: '110kVXXX线路',\n            dydj: '110kV',\n            sbzt: '在运',\n            gtcz: '角钢材质',\n            fzr: '张三',\n            tyrq: '2022-12-12'\n          }, {\n            ssxl: '110kVXXX线路',\n            dydj: '110kV',\n            sbzt: '在运',\n            gtcz: '角钢材质',\n            fzr: '张三',\n            tyrq: '2022-12-12'\n          }, {\n            ssxl: '110kVXXX线路',\n            dydj: '110kV',\n            sbzt: '在运',\n            gtcz: '角钢材质',\n            fzr: '张三',\n            tyrq: '2022-12-12'\n          }, {\n            ssxl: '110kVXXX线路',\n            dydj: '110kV',\n            sbzt: '在运',\n            gtcz: '角钢材质',\n            fzr: '张三',\n            tyrq: '2022-12-12'\n          }, {\n            ssxl: '110kVXXX线路',\n            dydj: '110kV',\n            sbzt: '在运',\n            gtcz: '角钢材质',\n            fzr: '张三',\n            tyrq: '2022-12-12'\n          }, {\n            ssxl: '110kVXXX线路',\n            dydj: '110kV',\n            sbzt: '在运',\n            gtcz: '角钢材质',\n            fzr: '张三',\n            tyrq: '2022-12-12'\n          }, {\n            ssxl: '110kVXXX线路',\n            dydj: '110kV',\n            sbzt: '在运',\n            gtcz: '角钢材质',\n            fzr: '张三',\n            tyrq: '2022-12-12'\n          }, {\n            ssxl: '110kVXXX线路',\n            dydj: '110kV',\n            sbzt: '在运',\n            gtcz: '角钢材质',\n            fzr: '张三',\n            tyrq: '2022-12-12'\n          }, {\n            ssxl: '110kVXXX线路',\n            dydj: '110kV',\n            sbzt: '在运',\n            gtcz: '角钢材质',\n            fzr: '张三',\n            tyrq: '2022-12-12'\n          }, {\n            ssxl: '110kVXXX线路',\n            dydj: '110kV',\n            sbzt: '在运',\n            gtcz: '角钢材质',\n            fzr: '张三',\n            tyrq: '2022-12-12'\n          }\n        ],\n\n        selectRows: [],\n        //变电站挂接数据\n        newTestData: [],\n\n        //删除是否可用\n        multipleSensor: true,\n        //查询参数\n        queryParams: {\n          pageNum: 1,\n          pageSize: 10,\n          roleKey: '',\n          roleName: '',\n          status: '',\n          lineName: undefined\n        },\n        showSearch: true,\n\n        //查询杆塔参数\n        queryGtParam:{\n          sbzt: '',\n          lineName:'',\n          dydj:'',\n          pageNum: 1,\n          pageSize: 10,\n        },\n\n        rules:{\n          gtbh:[{required:true,message:'请输入杆塔编号',trigger:'blur'}],\n          // ssbm:[{required:true,message:'请输入所属部门',trigger:'blur'}],\n          // yxbz:[{required:true,message:'请输入运行班组',trigger:'blur'}],\n          // sftgbj:[{required:true,message:'请选择是否同杆并架',trigger:'change'}],\n          // dydj:[{required:true,message:'请选择电压等级',trigger:'change'}],\n        }\n\n\n        /*rules:{\n          ssdwbm:[{ required: true, message: '请选择所属单位', trigger: 'change' }],\n          sbdm:[{required: true, message: '请填写设备代码', trigger: 'blur' }],\n          bdzmc:[{required: true, message: '请填写变电站名称', trigger: 'blur' }],\n          dydj:[{required: true, message: '请选择电压等级', trigger: 'blur' }],\n          sbzt:[{required: true, message: '请选择设备状态', trigger: 'blur' }],\n        },*/\n\n\n\n      }\n    },\n    watch: {},\n    created() {\n      //初始化加载时加载所有变电站信息\n      this.treeList()\n      this.getTableList()\n\n    },\n    mounted() {\n      //获取token\n    this.header.token = getToken();\n      this.getVoltageLeVelList()\n      this.filterInfos = { ...this.filterInfoJC }\n      this.tableAndPageInfos = { ...this.tableAndPageInfoJC }\n      this.getData()\n      this.getDataJyz()\n      this.getDataJd()\n      this.getDataJj()\n      this.getDataLx()\n      this.getDataFs()\n      this.getDataHd()\n    },\n    methods: {\n      /**----------------------------------------基础-------------------------------------------*/\n      //获取基础详情\n      getJcDetails(row) {\n        this.jctitle = '基础详情查看'\n        this.jcForm = { ...row }\n        this.isDisabled = true\n        this.JcDialogFormVisible = true\n      },\n      //基础增加弹框\n      JcClick() {\n        this.jctitle = '基础增加'\n        this.jcForm = {}\n        this.isDisabled = false\n        this.JcDialogFormVisible = true\n      },\n      //基础修改\n      getJcUpdate(row) {\n        this.jctitle = '基础修改'\n        this.jcForm = { ...row }\n        this.isDisabled = false\n        this.JcDialogFormVisible = true\n      },\n      //基础删除\n      async JcDelete() {\n        if (this.ids.length < 1) {\n          this.$message.warning('请选择正确的数据！！！')\n          return\n        }\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          //设备异常及事故\n          removeJc(this.ids).then(({ code }) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getData()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n      //基础 提交按钮\n      async JcSubmit() {\n        try {\n          let { code } = await saveOrUpdateJc(this.jcForm)\n          if (code === '0000') {\n            this.$message.success('操作成功')\n            await this.getData()\n          }\n        } catch (e) {\n          console.log(e)\n        }\n        this.JcDialogFormVisible = false\n      },\n\n      /**----------------------------------------绝缘子-------------------------------------------*/\n      //获取绝缘子详情\n      getJyzDetails(row) {\n        this.jyztitle = '绝缘子详情查看'\n        this.jyzForm = { ...row }\n        this.isDisabled = true\n        this.JyzDialogFormVisible = true\n      },\n      //绝缘子增加弹框\n      JyzClick() {\n        this.jyztitle = '绝缘子增加'\n        this.jyzForm = {}\n        this.isDisabled = false\n        this.JyzDialogFormVisible = true\n      },\n      //绝缘子修改\n      getJyzUpdate(row) {\n        this.jyztitle = '绝缘子修改'\n        this.jyzForm = { ...row }\n        this.isDisabled = false\n        this.JyzDialogFormVisible = true\n      },\n      //绝缘子\n      async JyzDelete() {\n        if (this.ids.length < 1) {\n          this.$message.warning('请选择正确的数据！！！')\n          return\n        }\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          removeJyz(this.ids).then(({ code }) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getDataJyz()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n      //绝缘子 提交按钮\n      async JyzSubmit() {\n        try {\n          let { code } = await saveOrUpdateJyz(this.jyzForm)\n          if (code === '0000') {\n            this.$message.success('操作成功')\n            await this.getDataJyz()\n          }\n        } catch (e) {\n          console.log(e)\n        }\n        this.JyzDialogFormVisible = false\n      },\n\n      //导线增加弹框\n      DaoxClick() {\n        this.daoxtitle = '导线增加'\n        this.isDisabled = false\n        this.DaoxDialogFormVisible = true\n      },\n      /**----------------------------------------接地-------------------------------------------*/\n      //获取接地详情\n      getJdDetails(row) {\n        this.jdtitle = '接地详情查看'\n        this.jdForm = { ...row }\n        this.isDisabled = true\n        this.JdDialogFormVisible = true\n      },\n      //接地增加弹框\n      JdClick() {\n        this.jdtitle = '接地增加'\n        this.jdForm = {}\n        this.isDisabled = false\n        this.JdDialogFormVisible = true\n      },\n      //接地修改\n      getJdUpdate(row) {\n        this.jdtitle = '接地修改'\n        this.jdForm = { ...row }\n        this.isDisabled = false\n        this.JdDialogFormVisible = true\n      },\n      //接地删除\n      async JdDelete() {\n        if (this.ids.length < 1) {\n          this.$message.warning('请选择正确的数据！！！')\n          return\n        }\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          removeJd(this.ids).then(({ code }) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getDataJd()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n      //接地 提交按钮\n      async JdSubmit() {\n        try {\n          let { code } = await saveOrUpdateJd(this.jdForm)\n          if (code === '0000') {\n            this.$message.success('操作成功')\n            await this.getDataJd()\n          }\n        } catch (e) {\n          console.log(e)\n        }\n        this.JdDialogFormVisible = false\n      },\n      /**----------------------------------------金具-------------------------------------------*/\n      //获取金具详情\n      getJjDetails(row) {\n        this.jjtitle = '金具详情查看'\n        this.jjForm = { ...row }\n        this.isDisabled = true\n        this.JjDialogFormVisible = true\n      },\n      //金具增加弹框\n      JjClick() {\n        this.jjtitle = '金具增加'\n        this.jjForm = {}\n        this.isDisabled = false\n        this.JjDialogFormVisible = true\n      },\n      //金具修改\n      getJjUpdate(row) {\n        this.jjtitle = '金具修改'\n        this.jjForm = { ...row }\n        this.isDisabled = false\n        this.JjDialogFormVisible = true\n      },\n      //金具删除\n      async JjDelete() {\n        if (this.ids.length < 1) {\n          this.$message.warning('请选择正确的数据！！！')\n          return\n        }\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          removeJj(this.ids).then(({ code }) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getDataJj()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n      //金具 提交按钮\n      async JjSubmit() {\n        try {\n          let { code } = await saveOrUpdateJj(this.jjForm)\n          if (code === '0000') {\n            this.$message.success('操作成功')\n            await this.getDataJj()\n          }\n        } catch (e) {\n          console.log(e)\n        }\n        this.JjDialogFormVisible = false\n      },\n\n      /**----------------------------------------拉线-------------------------------------------*/\n      //获取拉线详情\n      getLxDetails(row) {\n        this.lxtitle = '拉线详情查看'\n        this.lxForm = { ...row }\n        this.isDisabled = true\n        this.LxDialogFormVisible = true\n      },\n      //拉线增加弹框\n      LxClick() {\n        this.lxtitle = '拉线增加'\n        this.lxForm = {}\n        this.isDisabled = false\n        this.LxDialogFormVisible = true\n      },\n      //金具修改\n      getLxUpdate(row) {\n        this.lxtitle = '拉线修改'\n        this.lxForm = { ...row }\n        this.isDisabled = false\n        this.LxDialogFormVisible = true\n      },\n      //金具删除\n      async LxDelete() {\n        if (this.ids.length < 1) {\n          this.$message.warning('请选择正确的数据！！！')\n          return\n        }\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          removeLx(this.ids).then(({ code }) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getDataLx()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n      //金具 提交按钮\n      async LxSubmit() {\n        try {\n          let { code } = await saveOrUpdateLx(this.lxForm)\n          if (code === '0000') {\n            this.$message.success('操作成功')\n            await this.getDataLx()\n          }\n        } catch (e) {\n          console.log(e)\n        }\n        this.LxDialogFormVisible = false\n      },\n\n      /**----------------------------------------附属-------------------------------------------*/\n      //获取附属详情\n      getFsDetails(row) {\n        this.fstitle = '附属详情查看'\n        this.fsForm = { ...row }\n        this.isDisabled = true\n        this.FsDialogFormVisible = true\n      },\n      //附属增加弹框\n      FsClick() {\n        this.fstitle = '附属增加'\n        this.fsForm = {}\n        this.isDisabled = false\n        this.FsDialogFormVisible = true\n      },\n      //附属修改\n      getFsUpdate(row) {\n        this.fstitle = '附属修改'\n        this.fsForm = { ...row }\n        this.isDisabled = false\n        this.FsDialogFormVisible = true\n      },\n      //附属删除\n      async FsDelete() {\n        if (this.ids.length < 1) {\n          this.$message.warning('请选择正确的数据！！！')\n          return\n        }\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          removeFs(this.ids).then(({ code }) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getDataFs()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n      //附属 提交按钮\n      async FsSubmit() {\n        try {\n          let { code } = await saveOrUpdateFs(this.fsForm)\n          if (code === '0000') {\n            this.$message.success('操作成功')\n            await this.getDataFs()\n          }\n        } catch (e) {\n          console.log(e)\n        }\n        this.FsDialogFormVisible = false\n      },\n\n      /**----------------------------------------同杆并架-------------------------------------------*/\n      //同杆并架增加弹框\n      TgbjClick() {\n        this.tgbjtitle = '同杆并架增加'\n        this.isDisabled = false\n        this.TgbjDialogFormVisible = true\n      },\n\n      /**----------------------------------------横担-------------------------------------------*/\n      //获取附属详情\n      getHdDetails(row) {\n        this.hdtitle = '横担详情查看'\n        this.hdForm = { ...row }\n        this.isDisabled = true\n        this.HdDialogFormVisible = true\n      },\n      //横担增加弹框\n      HdClick() {\n        this.hdtitle = '横担增加'\n        this.hdForm = {}\n        this.isDisabled = false\n        this.HdDialogFormVisible = true\n      },\n      //附属修改\n      getHdUpdate(row) {\n        this.hdtitle = '横担修改'\n        this.hdForm = { ...row }\n        this.isDisabled = false\n        this.HdDialogFormVisible = true\n      },\n      //附属删除\n      async HdDelete() {\n        if (this.ids.length < 1) {\n          this.$message.warning('请选择正确的数据！！！')\n          return\n        }\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          removeHd(this.ids).then(({ code }) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getDataHd()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n      //附属 提交按钮\n      async HdSubmit() {\n        try {\n          let { code } = await saveOrUpdateHd(this.hdForm)\n          if (code === '0000') {\n            this.$message.success('操作成功')\n            await this.getDataHd()\n          }\n        } catch (e) {\n          console.log(e)\n        }\n        this.HdDialogFormVisible = false\n      },\n\n      /** 列表查询 */\n      //获取输电杆塔设备  基础  列表查询\n      async getData(params) {\n        try {\n          const param = { ...this.paramJc, ...params }\n          const { data, code } = await getListJc(param)\n          /*console.log(data)*/\n          if (code === '0000') {\n            this.tableAndPageInfoJC.tableData = data.records\n            this.tableAndPageInfoJC.pager.total = data.total\n          }\n        } catch (e) {\n          console.log(e)\n        }\n      },\n      //获取输电杆塔设备  绝缘子  列表查询\n      async getDataJyz(params) {\n        try {\n          const param = { ...this.paramJyz, ...params }\n          const { data, code } = await getListJyz(param)\n          /*console.log(data)*/\n          if (code === '0000') {\n            this.tableAndPageInfoJyz.tableData = data.records\n            this.tableAndPageInfoJyz.pager.total = data.total\n          }\n        } catch (e) {\n          console.log(e)\n        }\n      },\n      //获取输电杆塔设备  接地  列表查询\n      async getDataJd(params) {\n        try {\n          const param = { ...this.paramJd, ...params }\n          const { data, code } = await getListJd(param)\n          /*console.log(data)*/\n          if (code === '0000') {\n            this.tableAndPageInfoJd.tableData = data.records\n            this.tableAndPageInfoJd.pager.total = data.total\n          }\n        } catch (e) {\n          console.log(e)\n        }\n      },\n      //获取输电杆塔设备  金具  列表查询\n      async getDataJj(params) {\n        try {\n          const param = { ...this.paramJj, ...params }\n          const { data, code } = await getListJj(param)\n          /*console.log(data)*/\n          if (code === '0000') {\n            this.tableAndPageInfoJj.tableData = data.records\n            this.tableAndPageInfoJj.pager.total = data.total\n          }\n        } catch (e) {\n          console.log(e)\n        }\n      },\n      //获取输电杆塔设备  拉线  列表查询\n      async getDataLx(params) {\n        try {\n          const param = { ...this.paramLx, ...params }\n          const { data, code } = await getListLx(param)\n          /*console.log(data)*/\n          if (code === '0000') {\n            this.tableAndPageInfoLx.tableData = data.records\n            this.tableAndPageInfoLx.pager.total = data.total\n          }\n        } catch (e) {\n          console.log(e)\n        }\n      },\n      //获取输电杆塔设备  附属  列表查询\n      async getDataFs(params) {\n        try {\n          const param = { ...this.paramFs, ...params }\n          const { data, code } = await getListFs(param)\n          /*console.log(data)*/\n          if (code === '0000') {\n            this.tableAndPageInfoFs.tableData = data.records\n            this.tableAndPageInfoFs.pager.total = data.total\n          }\n        } catch (e) {\n          console.log(e)\n        }\n      },\n\n      //获取输电杆塔设备  横担  列表查询\n      async getDataHd(params) {\n        try {\n          const param = { ...this.paramHd, ...params }\n          const { data, code } = await getListHd(param)\n          if (code === '0000') {\n            this.tableAndPageInfoHd.tableData = data.records\n            this.tableAndPageInfoHd.pager.total = data.total\n          }\n        } catch (e) {\n          console.log(e)\n        }\n      },\n\n      getVoltageLeVelList() {\n        this.filterInfo.fieldList.forEach(item => {\n          if (item.value == 'ssgs') {\n            item.options = [\n              { label: '110kV', value: '110kV' },\n              { label: '35kV', value: '35kV' },\n              { label: '10kV', value: '10kV' },\n              { label: '6kV', value: '6kV' }\n            ]\n          }\n        })\n      },\n\n      addGtInfo: function() {\n        this.$refs['jbxxForm'].validate((valid) => {\n          if (valid) {\n            saveOrUpdategt(this.jbxxForm).then(res =>\n            {\n              if(res.code=='0000'){\n                console.log(res.data);\n                this.uploadData.businessId = res.data.objId;\n                this.submitUpload();\n                this.$message.success(\"操作成功\");\n                this.gtDialogFormVisible = false;\n                this.getTableList();\n              }else{\n                this.$message.warning(\"操作失败！\");\n                 return false;\n              }\n\n            });\n          } else {\n            setTimeout(() => {\n              var isError = document.getElementsByClassName(\"is-error\");\n              if (isError[0].querySelector('input')) {\n                isError[0].querySelector('input').focus();\n              } else if (isError[0].querySelector('textarea')) {\n                isError[0].querySelector('textarea').focus();\n              }\n            }, 1)\n            return false;\n          }\n        });\n      },\n\n      getTableList: function(params) {\n        this.queryGtParam = {...this.queryGtParam, ...params}\n        const param = {...this.queryGtParam, ...params}\n        getListgt(param).then(res => {\n          this.tableAndPageInfo.tableData = res.data.records\n          this.tableAndPageInfo.pager.total = res.data.total\n        })\n      },\n\n      /**\n       * 表格多选框\n       */\n      handleSelectionChange(selection) {\n        this.ids = selection.map(item => item.objId)\n        this.single = selection.length !== 1\n        this.multiple = !selection.length\n      },\n      /**\n       * 删除\n       */\n      deleteInfo() {\n        if (this.ids.length != 0) {\n          this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }).then(() => {\n            gtremove(this.ids).then(res => {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getTableList()\n            })\n          })\n        } else {\n          this.$message({\n            type: 'info',\n            message: '请选择至少一条数据!'\n          })\n        }\n      },\n      updateStatus(row) {\n        this.updateList.sbzt = row.sbzt\n        this.updateList.objId = row.objId,\n          this.dialogVisible = true\n\n      },\n      submitStatus() {\n        this.$confirm('确认将设备状态修改为' + this.updateList.sbzt + '?', '', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(res => {\n          updateStatus(this.updateList).then(res => {\n            if (res.code == '0000') {\n              this.$message.success('设备状态已变更！')\n              this.dialogVisible = false\n              this.getTableList()\n            }\n          })\n        })\n      },\n      clearUpload(){\n        if ( this.$refs.upload){\n          this.$refs.upload.clearFiles()\n        }\n      },\n      updateRow: async function (row) {\n        this.clearUpload();\n        this.resumeQuery.foreignNum = row.ssbm\n        this.resumeQuery.sblx = row.sblx\n        this.getResumList()\n        this.jbxxForm = {...row}\n        this.jbxxForm.attachment = []\n        await this.getFileList();\n        this.gtDialogFormVisible = true\n        this.show = false\n      },\n      detailsInfo: async function (row) {\n        this.clearUpload();\n        this.resumeQuery.foreignNum = row.ssbm\n        this.resumeQuery.sblx = row.sblx\n        this.getResumList()\n        this.jbxxForm = {...row}\n        this.jbxxForm.attachment = []\n        await this.getFileList();\n        this.gtDialogFormVisible = true\n        this.show = true\n      },\n      filterReset() {\n\n      },\n      filterResetTableList(){\n        this.getTableList();\n      },\n      treeList() {\n        getTreeList().then(res => {\n          this.treeOptions = res.data\n        })\n      },\n\n      handleClose() {\n        this.jbxxForm = {\n          attachment: []\n        }\n        this.$nextTick(function () {\n          this.$refs['jbxxForm'].clearValidate();\n        });\n        this.gtDialogFormVisible = false;\n      },\n      getResumList(par) {\n        let params={...par,...this.resumeQuery}\n        getResumDataList(params).then(res => {\n          this.resumPageInfo.tableData = res.data.records\n          this.resumPageInfo.pager.total = res.data.total\n        })\n      },\n\n      /*//list查询\n      getData() {\n        this.tableAndPageInfo.tableData = this.bdzList\n      },*/\n\n      //设备添加按钮\n      sbAddSensorButton() {\n\n      },\n      //杆塔详情展示\n      bdzAddSensorButton() {\n        if (this.jbxxForm.lineId != undefined) {\n          this.imgList = []\n          this.show = false\n          this.gtDialogFormVisible = true\n        } else {\n          this.$message.info('请先选择所属线路再新增杆塔')\n        }\n      },\n      //间隔添加按钮\n      jgAddjgButton() {\n        this.jgDialogFormVisible = true\n      },\n      selectChange(rows) {\n        this.ids = rows.map(item => item.objId)\n        this.single = rows.length !== 1\n        this.multiple = !rows.length\n        this.selectRows = rows\n      },\n      //每页展示数量点击事件\n      handleSizeChange() {\n\n      },\n      //页码改变事件\n      handleCurrentChange() {\n\n      },\n      //树点击事件\n      handleNodeClick(data) {\n        if (data.identifier == '3') {\n          this.jbxxForm.lineName = data.label\n          this.jbxxForm.lineId = data.id\n          // this.filterInfo.data.lineId=data.id;\n          let lineId\n          this.filterInfo.data.lineName=data.label;\n          this.getTableList(this.filterInfo.data);\n        }\n      },\n      beforeUpload (file) {\n        const fileSize = file.size < 1024 * 1024 * 50\n        if (!fileSize) {\n          this.$message.error('上传文件大小不能超过 50MB!')\n        }\n      },\n      submitUpload() {\n        this.$refs.upload.submit();\n      },\n      async getFileList(){\n        let {code,data}=await  getListByBusinessId({businessId:this.jbxxForm.objId})\n        if(code==='0000'){\n          this.jbxxForm.attachment = data;\n          this.imgList= data.map(item=>{\n            let item1={}\n            item1.name=item.fileName\n            item1.url=item.fileUrl\n            return item1\n          })\n        }\n      },\n      async deleteFileById(id){\n        let {code}=await deleteById(id)\n        if(code==='0000'){\n          await this.getFileList();\n          this.$message({\n            type: 'success',\n            message: '文件删除成功!'\n          });\n        }\n      },\n    }\n  }\n</script>\n\n<style lang='scss' scoped>\n  .head-container {\n    margin: 0 auto;\n    width: 98%;\n    height: calc(100vh - 225px);\n    overflow: auto;\n  }\n\n  .box-card {\n    margin-bottom: 2vh !important;\n\n    .el-card__header {\n      background-color: rgb(235, 245, 255) !important;\n    }\n  }\n\n  .box-cardList {\n    height: 70%;\n  }\n\n  .item {\n    width: 200px;\n    float: left;\n  }\n\n  #main_container_dj {\n    height: calc(100vh - 84px);\n  }\n\n  .aside_height {\n    height: 96%;\n  }\n\n  .defect .el-form-item:nth-child(odd) {\n    margin-right: 70px;\n  }\n\n  /*背景颜色调整*/\n  #main_container_dj, #main_container_dj .el-aside {\n    background-color: #b4caf1;\n  }\n\n  /deep/ .qxlr_dialog_insert .el-dialog__header {\n    background-color: #0cc283;\n  }\n\n  /deep/ .pmyBtn {\n    background: #0cc283;\n  }\n\n\n  /*/deep/ .el-input--medium .el-input__inner {*/\n  /*  width: 200px;*/\n  /*}*/\n  .el-select {\n    width: 100%;\n  }\n\n  .el-date-editor {\n    width: 100%;\n  }\n\n  .el-carousel__item h3 {\n    color: #475669;\n    font-size: 14px;\n    opacity: 0.75;\n    line-height: 150px;\n\n  }\n\n  .el-carousel__item:nth-child(2n) {\n    background-color: #99a9bf;\n  }\n\n  .el-carousel__item:nth-child(2n+1) {\n    background-color: #d3dce6;\n  }\n\n  /deep/ .box-card {\n    margin: 0 6px;\n  }\n  //有子节点 且未展开\n  .el-tree ::v-deep .el-icon-caret-right:before {\n    background: url(\"../../../../assets/image/add.png\") no-repeat 0;\n    content: \"\";\n    display: block;\n    width: 16px;\n    height: 16px;\n    font-size: 16px;\n    background-size: 16px;\n  }\n  //有子节点 且已展开\n  .el-tree\n  ::v-deep\n  .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {\n    background: url(\"../../../../assets/image/prep.png\") no-repeat 0;\n    content: \"\";\n    display: block;\n    width: 16px;\n    height: 16px;\n    font-size: 16px;\n    background-size: 16px;\n  }\n  //没有子节点\n  .el-tree ::v-deep .el-tree-node__expand-icon.is-leaf::before {\n    background: transparent;\n    content: \"\";\n    display: block;\n    width: 16px;\n    height: 16px;\n    font-size: 16px;\n    background-size: 16px;\n  }\n</style>\n<style>\n#pic_form .el-form-item__content{\n  margin-left: 0 !important;\n}\n</style>\n"]}]}