{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sdsbgl\\sdxd.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sdsbgl\\sdxd.vue", "mtime": 1706897324964}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON><PERSON>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"}, {"version": 3, "sources": ["sdxd.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAsSA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,MADA;AAEA,EAAA,KAAA,EAAA;AACA;;;AAGA,IAAA,UAJA,sBAIA,GAJA,EAIA;AACA,WAAA,KAAA,CAAA,IAAA,CAAA,MAAA,CAAA,GAAA;AACA;AANA,GAFA;AAWA,EAAA,IAXA,kBAWA;AACA,WAAA;AACA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA;AAFA,OAFA;AAMA;AACA,MAAA,UAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA,EAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OAAA,CAPA;AAQA;AACA,MAAA,WAAA,EAAA,EATA;AAUA;AACA,MAAA,WAAA,EAAA,EAXA;AAYA;AACA,MAAA,YAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAAA,EAAA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAAA,CAbA;AAcA;AACA,MAAA,UAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAAA,EAAA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAAA,CAfA;AAgBA;AACA,MAAA,WAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAAA,EAAA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAAA,CAjBA;AAkBA;AACA,MAAA,IAAA,EAAA;AACA,QAAA,EAAA,EAAA,EADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,QAAA,EAAA,EAJA;AAKA,QAAA,OAAA,EAAA,EALA;AAMA,QAAA,OAAA,EAAA,EANA;AAOA,QAAA,MAAA,EAAA,EAPA;AAQA,QAAA,KAAA,EAAA,EARA;AASA,QAAA,GAAA,EAAA,EATA;AAUA,QAAA,MAAA,EAAA,EAVA;AAWA,QAAA,IAAA,EAAA,EAXA;AAYA,QAAA,KAAA,EAAA,EAZA;AAaA,QAAA,IAAA,EAAA,EAbA;AAcA,QAAA,IAAA,EAAA,EAdA;AAeA,QAAA,IAAA,EAAA,EAfA;AAgBA,QAAA,IAAA,EAAA,EAhBA;AAiBA,QAAA,IAAA,EAAA,EAjBA;AAkBA,QAAA,IAAA,EAAA,EAlBA;AAmBA,QAAA,EAAA,EAAA,EAnBA;AAoBA,QAAA,EAAA,EAAA,EApBA;AAqBA,QAAA,IAAA,EAAA,EArBA;AAsBA,QAAA,IAAA,EAAA,EAtBA;AAuBA,QAAA,MAAA,EAAA,EAvBA;AAwBA,QAAA,IAAA,EAAA,EAxBA;AAyBA,QAAA,IAAA,EAAA,EAzBA;AA0BA,QAAA,IAAA,EAAA,EA1BA;AA2BA,QAAA,OAAA,EAAA,EA3BA;AA4BA,QAAA,IAAA,EAAA,EA5BA;AA6BA,QAAA,IAAA,EAAA,EA7BA;AA8BA,QAAA,MAAA,EAAA,EA9BA;AA+BA,QAAA,IAAA,EAAA,EA/BA;AAgCA,QAAA,EAAA,EAAA,EAhCA;AAiCA,QAAA,IAAA,EAAA,EAjCA;AAkCA,QAAA,IAAA,EAAA,EAlCA;AAmCA,QAAA,EAAA,EAAA;AAnCA,OAnBA;AAwDA;AACA,MAAA,UAAA,EAAA,KAzDA;AA0DA;AACA,MAAA,aAAA,EAAA,KA3DA;AA4DA;AACA,MAAA,UAAA,EAAA,KA7DA;AA8DA;AACA,MAAA,QAAA,EAAA,KA/DA;AAgEA;AACA,MAAA,QAAA,EAAA,KAjEA;AAkEA;AACA,MAAA,QAAA,EAAA,KAnEA;AAoEA;AACA,MAAA,UAAA,EAAA,EArEA;AAsEA;AACA,MAAA,KAAA,EAAA,EAvEA;AAwEA;AACA,MAAA,QAAA,EAAA,EAzEA;AA0EA,MAAA,YAAA,EAAA;AACA,QAAA,QAAA,EAAA,UADA;AAEA,QAAA,KAAA,EAAA;AAFA,OA1EA;AA8EA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,EAAA,EAAA,EADA;AAEA,UAAA,QAAA,EAAA,EAFA;AAGA,UAAA,IAAA,EAAA,EAHA;AAIA,UAAA,IAAA,EAAA,EAJA;AAKA,UAAA,IAAA,EAAA;AALA,SADA;AAOA;AACA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,UAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,WAAA;AAAA,UAAA,MAAA,EAAA;AAAA,SARA,EASA;AAAA,UAAA,KAAA,EAAA,UAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SATA;AARA,OA9EA;AAkGA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,UAAA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,SAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SARA,EASA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SATA,EAUA;AAAA,UAAA,KAAA,EAAA,UAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAVA,EAWA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAXA,EAYA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,IAAA,EAAA,SAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAZA,EAaA;AACA,UAAA,IAAA,EAAA,WADA;AAEA,UAAA,KAAA,EAAA,IAFA;AAGA,UAAA,QAAA,EAAA,OAHA;AAIA,UAAA,KAAA,EAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WAJA;AAKA;AACA,UAAA,KAAA,EAAA,OANA;AAOA,UAAA,SAAA,EAAA,CACA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WADA,EAEA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA;AACA;AAHA;AAPA,SAbA,CARA;AAmCA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAnCA,OAlGA;AAuIA,MAAA,MAAA,EAAA;AACA,QAAA,QAAA,EAAA,EADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA;AAJA,OAvIA;AA6IA,MAAA,UAAA,EAAA,EA7IA;AA8IA,MAAA,KAAA,EAAA;AACA,QAAA,QAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CADA;AAEA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,aAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAFA;AAGA,QAAA,OAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAHA;AAIA,QAAA,QAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,WAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAJA;AAKA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CALA;AAMA,QAAA,OAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,aAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CANA;AAOA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAPA;AAQA,QAAA,EAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CARA;AASA,QAAA,EAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AATA;AA9IA,KAAA;AA0JA,GAtKA;AAuKA,EAAA,OAvKA,qBAuKA;AACA;AACA,SAAA,OAAA,GAFA,CAGA;;AACA,SAAA,QAAA;AACA,GA5KA;AA6KA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,aAFA,yBAEA,GAFA,EAEA;AACA,UAAA,GAAA,IAAA,GAAA,EAAA;AACA,aAAA,QAAA,GAAA,IAAA;AACA,OAFA,MAEA;AACA,aAAA,QAAA,GAAA,KAAA;AACA;AACA,KARA;AASA;AACA,IAAA,aAVA,yBAUA,GAVA,EAUA;AACA,UAAA,GAAA,IAAA,GAAA,EAAA;AACA,aAAA,QAAA,GAAA,KAAA;AACA,OAFA,MAEA;AACA,aAAA,QAAA,GAAA,IAAA;AACA;AACA,KAhBA;AAiBA;AACA,IAAA,aAlBA,yBAkBA,GAlBA,EAkBA;AACA,UAAA,GAAA,IAAA,GAAA,EAAA;AACA,aAAA,QAAA,GAAA,KAAA;AACA,OAFA,MAEA;AACA,aAAA,QAAA,GAAA,IAAA;AACA;AACA,KAxBA;AAyBA;AACA,IAAA,OA1BA,mBA0BA,MA1BA,EA0BA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,KAFA,+DAEA,KAAA,CAAA,MAFA,GAEA,MAFA;AAAA;AAAA,uBAGA,oBAAA,KAAA,CAHA;;AAAA;AAAA;AAGA,gBAAA,IAHA,mBAGA,IAHA;AAGA,gBAAA,IAHA,mBAGA,IAHA;;AAIA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,KAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,KAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AAPA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,KApCA;AAsCA;AACA,IAAA,YAAA,EAAA,sBAAA,MAAA,EAAA;AAAA;;AACA,MAAA,OAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA;AACA,OAHA;AAIA,KA5CA;AA6CA;AACA,IAAA,MA9CA,oBA8CA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,KAhDA;AAkDA;AACA,IAAA,QAnDA,sBAmDA;AACA,WAAA,OAAA;AACA,KArDA;AAsDA;AACA,IAAA,qBAvDA,mCAuDA,CAEA,CAzDA;AA0DA;AACA,IAAA,OA3DA,qBA2DA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KA7DA;AA8DA;AACA,IAAA,SA/DA,uBA+DA;AACA,UAAA,KAAA,IAAA,CAAA,QAAA,IAAA,EAAA,EAAA;AACA,aAAA,KAAA,GAAA,QAAA;AACA,aAAA,UAAA,GAAA,KAAA,CAFA,CAGA;;AACA;;AACA,aAAA,aAAA,GAAA,IAAA;AACA,OANA,MAMA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,aAAA;AACA,eAAA,KAAA;AACA;AAEA,KA3EA;AA4EA;AACA,IAAA,SA7EA,qBA6EA,GA7EA,EA6EA;AACA,WAAA,KAAA,GAAA,QAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,aAAA,GAAA,IAAA,CAJA,CAKA;;AACA,UAAA,GAAA,CAAA,QAAA,IAAA,GAAA,EAAA;AACA,aAAA,QAAA,GAAA,IAAA;AACA,OAFA,MAEA;AACA,aAAA,QAAA,GAAA,KAAA;AACA;;AAEA,UAAA,GAAA,CAAA,MAAA,IAAA,GAAA,EAAA;AACA,aAAA,QAAA,GAAA,KAAA;AACA,OAFA,MAEA;AACA,aAAA,QAAA,GAAA,IAAA;AACA;;AAEA,UAAA,GAAA,CAAA,OAAA,IAAA,GAAA,EAAA;AACA,aAAA,QAAA,GAAA,IAAA;AACA,OAFA,MAEA;AACA,aAAA,QAAA,GAAA,KAAA;AACA;AACA,KApGA;AAqGA;AACA,IAAA,UAtGA,sBAsGA,GAtGA,EAsGA;AACA,WAAA,KAAA,GAAA,QAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,QAAA,GAAA,IAAA;AACA,WAAA,QAAA,GAAA,IAAA;AACA,WAAA,QAAA,GAAA,IAAA;AACA,KA9GA;;AA+GA;;;;;;;AAOA;AACA,IAAA,OAvHA,qBAuHA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAAA;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,wBAAA,KAAA,EAAA;AAAA,0CACA,wBAAA,MAAA,CAAA,IAAA,CADA;AAAA,0BACA,IADA,iBACA,IADA;;AAEA,0BAAA,IAAA,KAAA,MAAA,EAAA;AACA,wBAAA,MAAA,CAAA,OAAA;;AACA,wBAAA,MAAA,CAAA,aAAA,GAAA,KAAA;;AACA,wBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA;AACA,qBAPA,MAOA;AACA,sBAAA,UAAA,CAAA,YAAA;AACA,4BAAA,OAAA,GAAA,QAAA,CAAA,sBAAA,CAAA,UAAA,CAAA;;AACA,4BAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,OAAA,CAAA,EAAA;AACA,0BAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,OAAA,EAAA,KAAA;AACA,yBAFA,MAEA,IAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,UAAA,CAAA,EAAA;AACA,0BAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,UAAA,EAAA,KAAA;AACA;AACA,uBAPA,EAOA,CAPA,CAAA;AAQA,6BAAA,KAAA;AACA;AACA,mBAnBA;AAoBA,iBArBA,CAqBA,OAAA,CAAA,EAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,CAAA;AACA;;AAxBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA0BA,KAjJA;AAkJA;AACA,IAAA,SAnJA,uBAmJA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBACA,MAAA,CAAA,UAAA,CAAA,MAAA,GAAA,CADA;AAAA;AAAA;AAAA;;AAEA,gBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,aAAA;;AAFA;;AAAA;AAKA,gBAAA,GALA,GAKA,MAAA,CAAA,UAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,yBAAA,IAAA,CAAA,EAAA;AACA,iBAFA,CALA;;AAQA,gBAAA,MAAA,CAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAIA,IAJA,CAIA,YAAA;AACA,oCAAA,GAAA,EAAA,IAAA,CAAA,gBAAA;AAAA,wBAAA,IAAA,QAAA,IAAA;;AACA,wBAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,SADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;;AAIA,sBAAA,MAAA,CAAA,OAAA;AACA,qBANA,MAMA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,OADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;AAIA;AACA,mBAbA;AAcA,iBAnBA,EAmBA,KAnBA,CAmBA,YAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA;AACA,oBAAA,IAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;AAIA,iBAxBA;;AARA;AAAA,uBAiCA,MAAA,CAAA,OAAA,EAjCA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkCA,KArLA;AAsLA,IAAA,QAtLA,sBAsLA;AAAA;;AACA,+BAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KA1LA;AA2LA;AACA,IAAA,KA5LA,mBA4LA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,KA9LA;AA+LA,IAAA,SA/LA,uBA+LA;AACA,WAAA,IAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,YAAA;AACA,aAAA,KAAA,CAAA,MAAA,EAAA,aAAA;AACA,OAFA;AAGA,WAAA,aAAA,GAAA,KAAA;AAEA,KAtMA;AAuMA;AACA,IAAA,YAxMA,wBAwMA,IAxMA,EAwMA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KA1MA;AA2MA;AACA,IAAA,eA5MA,2BA4MA,IA5MA,EA4MA;AACA,UAAA,IAAA,CAAA,UAAA,IAAA,GAAA,EAAA;AACA,aAAA,IAAA,CAAA,QAAA,GAAA,IAAA,CAAA,KAAA;AACA,aAAA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA,EAAA;AACA,aAAA,UAAA,CAAA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA,EAAA;AACA,aAAA,OAAA;AACA,OALA,MAKA,IAAA,IAAA,CAAA,UAAA,IAAA,GAAA,EAAA;AACA,aAAA,UAAA,CAAA,IAAA,CAAA,IAAA,GAAA,EAAA;AACA,aAAA,OAAA;AACA;AAEA,KAvNA;AAwNA,IAAA,KAxNA,mBAwNA;AACA,WAAA,UAAA,CAAA,IAAA,mCAAA,KAAA,UAAA,CAAA,IAAA;AACA;AA1NA;AA7KA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row gutter=\"16\">\n      <!--左侧菜单树-->\n      <el-col :span=\"4\">\n        <el-card>\n          <div style=\"overflow: auto;height: 80vh\">\n            <el-tree highlight-current class=\"filter-tree\" :data=\"treeData\"\n                     :props=\"defaultProps\" ref=\"tree\" @node-click=\"handleNodeClick\" accordion><!--@node-click=\"handleNodeClick\"-->\n            </el-tree>\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--基本信息查询及显示-->\n      <el-col :span=\"20\">\n        <!--搜索条件-->\n        <el-filter\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 120, itemWidth: 230 }\"\n          @getMethod=\"query\"\n          @handleReset=\"getReset\"\n        />\n\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"getInster\">新增</el-button>\n            <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"deleteRow\">删除</el-button>\n          </div>\n          <comp-table  @getMethod=\"query\" :table-and-page-info=\"tableAndPageInfo\" @update:multipleSelection=\"selectChange\" height=\"61vh\"/>\n\n        </el-white>\n\n      </el-col>\n    </el-row>\n\n\n    <!-- 详情/新增/修改 -->\n    <el-dialog :title=title :visible.sync=\"isShowDetails\" width=\"50%\" :before-close=\"resetForm\">\n      <el-form label-width=\"180px\" ref=\"form\" :model=\"form\" :rules=\"rules\" >\n        <div>\n          <!--线段基本信息-->\n          <el-card class=\"box-card\">\n            <div slot=\"header\">\n              <span>基本信息</span>\n            </div>\n            <el-col :span=\"12\">\n              <el-form-item label=\"所属线路名称：\" prop=\"lineName\">\n                <el-input v-model=\"form.lineName\" :disabled=\"isDisabled\"  placeholder=\"请输入内容\">\n                </el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"所属线路运行编号：\" prop=\"xlbm\">\n                <el-input v-model=\"form.xlbm\" :disabled=\"true\" placeholder=\"请输入内容\"></el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"线段名称：\" prop=\"zgxxdmc\">\n                <el-input v-model=\"form.zgxxdmc\" :disabled=\"isDisabled\" placeholder=\"请输入内容\">\n                  <!--<el-option\n                    v-for=\"item in zgxxdmcList\"\n                    :key=\"item.value\"\n                    :label=\"item.bdz\"\n                    :value=\"item.bdz\">\n                  </el-option>-->\n                </el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"线段编码：\" prop=\"xdbm\">\n                <el-input v-model=\"form.xdbm\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"是否为主干线线段：\" prop=\"sfwzgxxd\" class=\"red\">\n                <el-select @change=\"changeClickZx\" v-model=\"form.sfwzgxxd\" :disabled=\"isDisabled\" placeholder=\"必填\">\n                  <el-option\n                    v-for=\"item in sfwzgxxdList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"所属主干线名称：\" prop=\"sszgxmc\">\n                <el-input v-model=\"form.sszgxmc\" :disabled=\"isShowZx\" placeholder=\"请输入内容\">\n                  <!--<el-option\n                    v-for=\"item in sszgxmcList\"\n                    :key=\"item.value\"\n                    :label=\"item.bdz\"\n                    :value=\"item.bdz\">\n                  </el-option>-->\n                </el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"资产属性：\" prop=\"zcsx\">\n                <el-input v-model=\"form.zcsx\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item label=\"地区特征：\" prop=\"dqtz\">\n                <el-input v-model=\"form.dqtz\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"线段类型：\" prop=\"xdlx\">\n                <el-select v-model=\"form.xdlx\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item label=\"起点：\" prop=\"qd\">\n                <el-input v-model=\"form.qd\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"终点：\" prop=\"zd\">\n                <el-input v-model=\"form.zd\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item label=\"投运日期：\" prop=\"tyrq\">\n                <el-date-picker\n                  v-model=\"form.tyrq\"\n                  :disabled=\"isDisabled\"\n                  type=\"date\"\n                  placeholder=\"选择日期时间\"\n                  style=\"width: 100%\">\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"线段长度(Km)：\" prop=\"xdcd\">\n                <el-input v-model=\"form.xdcd\" @input=\"(v) => (form.xdcd = v.replace(/[^\\d]/g, ''))\"\n                          :disabled=\"isDisabled\" placeholder=\"请输入数字\"/>\n              </el-form-item>\n            </el-col>\n          </el-card>\n          <!--其它信息-->\n          <el-card class=\"box-card\">\n            <div slot=\"header\">\n              <span>其它信息</span>\n            </div>\n            <el-col :span=\"12\">\n              <el-form-item label=\"导线类型：\" prop=\"dxlx\">\n                <el-input v-model=\"form.dxlx\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"导线型号：\" prop=\"dxxh\">\n                <el-input v-model=\"form.dxxh\" :disabled=\"isDisabled\" placeholder=\"如果为导线时填写\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"是否联络线路：\" prop=\"sfllxl\">\n                <el-select @change=\"changeClickXl\" v-model=\"form.sfllxl\" :disabled=\"isDisabled\" placeholder=\"请输入内容\">\n                  <el-option\n                    v-for=\"item in sfllxlList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"联络类型：\" prop=\"lllx\">\n                <el-input v-model=\"form.lllx\" :disabled=\"isShowXl\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"是否为出线开关：\" prop=\"sfwcxkg\">\n                <el-select @change=\"changeClickKg\" v-model=\"form.sfwcxkg\" :disabled=\"isDisabled\" placeholder=\"请输入内容\">\n                  <el-option\n                    v-for=\"item in sfwcxkgList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"开关类型：\" prop=\"kglx\">\n                <el-input v-model=\"form.kglx\" :disabled=\"isShowKg\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"开关编号：\" prop=\"kgbh\">\n                <el-input v-model=\"form.kgbh\" :disabled=\"isShowKg\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"导线截面（mm2）：\" prop=\"dxjm\">\n                <el-input v-model=\"form.dxjm\" @input=\"(v) => (form.dxjm = v.replace(/[^\\d]/g, ''))\"\n                          :disabled=\"isDisabled\" placeholder=\"请输入数字\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"生产厂家：\" prop=\"sccj\">\n                <el-input v-model=\"form.sccj\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"电缆绝缘材质：\" prop=\"dljycz\">\n                <el-input v-model=\"form.dljycz\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"电缆型号：\" prop=\"dlxh\">\n                <el-input v-model=\"form.dlxh\" :disabled=\"isDisabled\" placeholder=\"如果为电缆填写\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"芯数：\" prop=\"xs\">\n                <el-input v-model=\"form.xs\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"敷设方式：\" prop=\"fsfs\">\n                <el-input v-model=\"form.fsfs\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"线段性质：\" prop=\"xdxz\">\n                <el-input v-model=\"form.xdxz\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"备注：\" prop=\"bz\">\n                <el-input type=\"textarea\" v-model=\"form.bz\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n          </el-card>\n\n        </div>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"resetForm\" size=\"small\">关 闭</el-button>\n        <el-button v-if=\"title=='新增输电线段' || title=='修改输电线段'\" type=\"primary\" size=\"small\" @click=\"saveRow\">保 存\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!--线路名称弹窗-->\n    <el-dialog title=\"请选择线路名称\" :visible.sync=\"isShowXlmc\" width=\"25%\" append-to-body>\n      <el-form :inline=\"true\" label-width=\"100px\" class=\"qxlr_dialog_insert\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"线路名称：\">\n              <el-select placeholder=\"请选择\" v-model=\"form.lineName\">\n                <el-option\n                  v-for=\"item in ssxlmcList\"\n                  :key=\"item.label\"\n                  :label=\"item.lineName\"\n                  :value=\"item.lineName\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"线路编码：\">\n              <el-select placeholder=\"请选择\" v-model=\"form.xlbm\">\n                <el-option\n                  v-for=\"item in ssxlmcList\"\n                  :key=\"item.label\"\n                  :label=\"item.xlbm\"\n                  :value=\"item.xlbm\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n\n      <span slot=\"footer\" class=\"dialog-footer\">\n    <el-button @click=\"isShowXlmc = false\">取 消</el-button>\n    <el-button type=\"primary\" @click=\"commit\">确 定</el-button>\n  </span>\n    </el-dialog>\n\n  </div>\n</template>\n\n\n<script>\n  import { getLists, saveOrUpdate, remove } from '@/api/dagangOilfield/dwzygl/sdsbgl/sdxd'\n  import { getTreeList } from '@/api/dagangOilfield/asset/sdgt'\n\n  export default {\n    name: 'sdxd',\n    watch: {\n      /**\n       * 监听下拉树筛选\n       */\n      filterText(val) {\n        this.$refs.tree.filter(val)\n      }\n\n    },\n    data() {\n      return {\n        //查询线路的参数\n        queryParams: {\n          pageNum: 1,\n          pageSize: 10\n        },\n        //所属线路名称\n        ssxlmcList: [{ label: '', lineName: '' }],\n        //主干线线段名称\n        zgxxdmcList: [],\n        //支线线段名称\n        sszgxmcList: [],\n        //是否为主干线线段\n        sfwzgxxdList: [{ label: '是', value: '是' }, { label: '否', value: '否' }],\n        //是否联络线路\n        sfllxlList: [{ label: '是', value: '是' }, { label: '否', value: '否' }],\n        //是否为出线开关\n        sfwcxkgList: [{ label: '是', value: '是' }, { label: '否', value: '否' }],\n        //form表单\n        form: {\n          bh: '',\n          lineName: '',\n          xlbm: '',\n          sfwzgxxd: '',\n          zgxxdmc: '',\n          sszgxmc: '',\n          zxxdbm: '',\n          erpbm: '',\n          bgr: '',\n          zcbdfs: '',\n          zcbh: '',\n          wbsys: '',\n          dxlx: '',\n          dxxh: '',\n          xdbm: '',\n          zcsx: '',\n          dqtz: '',\n          xdlx: '',\n          qd: '',\n          zd: '',\n          tyrq: '',\n          xdcd: '',\n          sfllxl: '',\n          lllx: '',\n          kglx: '',\n          kgbh: '',\n          sfwcxkg: '',\n          dxjm: '',\n          sccj: '',\n          dljycz: '',\n          dlxh: '',\n          xs: '',\n          fsfs: '',\n          xdxz: '',\n          bz: ''\n        },\n        //是否展示线路名称\n        isShowXlmc: false,\n        //详情弹框是否显示\n        isShowDetails: false,\n        //是否禁用\n        isDisabled: false,\n        //是否展示为支线\n        isShowZx: false,\n        //是否展示联络线路\n        isShowXl: false,\n        //是否展示为出线开关\n        isShowKg: false,\n        //监听下拉树筛选\n        filterText: '',\n        //标题\n        title: '',\n        // 左侧下拉树数据\n        treeData: [],\n        defaultProps: {\n          children: 'children',\n          label: 'label'\n        },\n        filterInfo: {\n          data: {\n            bh: '',\n            lineName: '',\n            xlbm:'',\n            zcsx:'',\n            dqtz:'',\n          },//查询条件\n          fieldList: [\n            { label: '所属线路名称', value: 'lineName', type: 'select', clearable: true },\n            { label: '线段名称', value: 'zgxxdmc', type: 'select', clearable: true },\n            { label: '资产属性', value: 'zcsx', type: 'input', clearable: true },\n            { label: '地区特征', value: 'dqtz', type: 'input', clearable: true },\n            { label: '线段类型', value: 'xdlx', type: 'input', clearable: true },\n            { label: '起点', value: 'qd', type: 'input', clearable: true },\n            { label: '终点', value: 'zd', type: 'input', clearable: true },\n            { label: '投运日期', value: 'tyrq', type: 'date', dateType: 'daterange', format: 'yyyy-MM-dd' },\n            { label: '线段长度(km)', value: 'xdcd', type: 'input', clearable: true }\n          ]\n        },\n        tableAndPageInfo: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          tableData: [],\n          tableHeader: [\n            { label: '所属线路名称', prop: 'lineName', minWidth: '90' },\n            { label: '是否为主干线线段', prop: 'sfwzgxxd', minWidth: '70' },\n            { label: '线段名称', prop: 'zgxxdmc', minWidth: '120' },\n            { label: '资产属性', prop: 'zcsx', minWidth: '90' },\n            { label: '地区特征', prop: 'dqtz', minWidth: '75' },\n            { label: '线段类型', prop: 'xdlx', minWidth: '80' },\n            { label: '起点', prop: 'qd', minWidth: '60' },\n            { label: '终点', prop: 'zd', minWidth: '60' },\n            { label: '投运日期', prop: 'tyrq', minWidth: '87' },\n            { label: '线段长度(km)', prop: 'xdcd', minWidth: '70' },\n            { label: '是否联络线路', prop: 'sfllxl', minWidth: '60' },\n            { label: '是否为出线开关', prop: 'sfwcxkg', minWidth: '60' },\n            {\n              prop: 'operation',\n              label: '操作',\n              minWidth: '100px',\n              style: { display: 'block' },\n              //操作列固定再右侧\n              fixed: 'right',\n              operation: [\n                { name: '修改', clickFun: this.getUpdate },\n                { name: '详情', clickFun: this.getDetails }\n                /*{ name: '附件查看', clickFun: this.getFjInfoList },*/\n              ]\n            }\n          ],\n          option: { checkBox: true, serialNumber: true }\n        },\n        params: {\n          lineName: '',\n          xlbm: '',\n          zcsx:'',\n          dqtz:'',\n        },\n        selectRows: [],\n        rules:{\n          lineName:[{required:true,message:'请选择所属线路',trigger:'change'}],\n          xlbm:[{required:true,message:'请选择所属线路运行编号',trigger:'blur'}],\n          zgxxdmc:[{required:true,message:'请输入线段名称',trigger:'blur'}],\n          sfwzgxxd:[{required:true,message:'请选择是否主干线段',trigger:'change'}],\n          xdbm:[{required:true,message:'请输入线段编码',trigger:'blur'}],\n          sszgxmc:[{required:true,message:'请输入所属主干线段名称',trigger:'blur'}],\n          zcsx:[{required:true,message:'请输入资产属性',trigger:'blur'}],\n          qd:[{required:true,message:'请输入线段起点',trigger:'blur'}],\n          zd:[{required:true,message:'请输入线段终点',trigger:'blur'}],\n        },\n      }\n    },\n    created() {\n      //列表查询\n      this.getData()\n      //this.getTableList(this.queryParams);\n      this.treeList();\n    },\n    methods: {\n      //是否为支线\n      changeClickZx(val) {\n        if (val == '是') {\n          this.isShowZx = true\n        } else {\n          this.isShowZx = false\n        }\n      },\n      //是否为联络线路\n      changeClickXl(val) {\n        if (val == '是') {\n          this.isShowXl = false\n        } else {\n          this.isShowXl = true\n        }\n      },\n      //是否为出线开关\n      changeClickKg(val) {\n        if (val == '是') {\n          this.isShowKg = false\n        } else {\n          this.isShowKg = true\n        }\n      },\n      //列表查询\n      async getData(params) {\n        try {\n          const param = {...this.params, ...params}\n          const { data, code } = await getLists(param)\n          if (code === '0000') {\n            this.tableAndPageInfo.tableData = data.records\n            this.tableAndPageInfo.pager.total = data.total\n          }\n        } catch (e) {\n        }\n      },\n\n      //查询输电线路信息P\n      getTableList: function(params) {\n        getList(params).then(res => {\n          this.ssxlmcList = res.data.records\n          /*this.tableAndPageInfo.tableData = res.data.records*/\n        })\n      },\n      //提交查询到的输电线路信息\n      commit() {\n        this.isShowXlmc = false\n      },\n\n      //重置按钮\n      getReset() {\n        this.getData();\n      },\n      //选中行\n      handleSelectionChange() {\n\n      },\n      //线路名称弹框\n      getXlmc() {\n        this.isShowXlmc = true\n      },\n      //新增按钮\n      getInster() {\n        if(this.form.lineName!=''){\n          this.title = '新增输电线段'\n          this.isDisabled = false\n          //this.form = {}\n          /*this.ssxlmcList= ''*/\n          this.isShowDetails = true\n        }else{\n          this.$message.warning(\"请先选择线路后新增线段\");\n          return false;\n        }\n\n      },\n      //修改按钮\n      getUpdate(row) {\n        this.title = '修改输电线段'\n        this.isDisabled = false\n        this.form = { ...row }\n        this.isShowDetails = true\n        //获取是或否进行判断，是否可以编辑\n        if (row.sfwzgxxd == '是') {\n          this.isShowZx = true\n        } else {\n          this.isShowZx = false\n        }\n\n        if (row.sfllxl == '是') {\n          this.isShowXl = false\n        } else {\n          this.isShowXl = true\n        }\n\n        if (row.sfwcxkg == '否') {\n          this.isShowKg = true\n        } else {\n          this.isShowKg = false\n        }\n      },\n      //详情按钮\n      getDetails(row) {\n        this.title = '输电线段详情'\n        this.form = { ...row }\n        this.isDisabled = true\n        this.isShowDetails = true\n        this.isShowZx = true\n        this.isShowXl = true\n        this.isShowKg = true\n      },\n      /*//附件查看\n      getFjInfoList() {\n        this.title = '附件查看'\n        this.isDisabled = true\n        this.isShowDetails = true\n        this.form={...row}\n      },*/\n      //保存按钮\n      async saveRow() {\n        try {\n            this.$refs['form'].validate((valid) => {\n              if (valid) {\n                  let { code } =  saveOrUpdate(this.form)\n                  if (code === '0000') {\n                    this.getData()\n                    this.isShowDetails = false\n                    this.$message.success('操作成功')\n                  }\n              } else {\n                setTimeout(() => {\n                  var isError = document.getElementsByClassName(\"is-error\");\n                  if (isError[0].querySelector('input')) {\n                    isError[0].querySelector('input').focus();\n                  } else if (isError[0].querySelector('textarea')) {\n                    isError[0].querySelector('textarea').focus();\n                  }\n                }, 1)\n                return false;\n              }\n            });\n        } catch (e) {\n            console.log(e)\n        }\n\n      },\n      //删除按钮\n      async deleteRow() {\n        if (this.selectRows.length < 1) {\n          this.$message.warning('请选择正确的数据！！！')\n          return\n        }\n        let ids = this.selectRows.map(item => {\n          return item.id\n        })\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove(ids).then(({ code }) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getData()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n        await this.getData()\n      },\n      treeList(){\n          getTreeList().then(res => {\n            this.treeData = res.data\n          });\n      },\n      //关闭弹窗\n      close() {\n        this.isShowDetails = false\n      },\n      resetForm(){\n        this.form = {}\n        this.$nextTick(function () {\n          this.$refs['form'].clearValidate();\n        });\n        this.isShowDetails = false;\n\n      },\n      //筛选条件\n      selectChange(rows) {\n        this.selectRows = rows\n      },\n      //左侧树点击事件\n      handleNodeClick(data){\n        if(data.identifier=='3'){\n            this.form.lineName=data.label;\n            this.form.xlbm=data.id;\n            this.filterInfo.data.xlbm=data.id;\n          this.getData();\n        }else if(data.identifier=='1'){\n          this.filterInfo.data.xlbm='';\n          this.getData();\n        }\n\n      },\n      query(){\n       this.filterInfo.data={...this.filterInfo.data}\n      },\n    },\n\n\n  }\n</script>\n\n<style>\n  /*控制input输入框边框是否显示*/\n  .elInput >>> .el-input__inner {\n    border: 0;\n  }\n\n  .box-card {\n    margin-bottom: 2vh !important;\n  }\n\n  .red .el-form-item__label {\n    color: red;\n  }\n</style>\n\n"], "sourceRoot": "src/views/dagangOilfield/dwzygl/sdsbgl"}]}