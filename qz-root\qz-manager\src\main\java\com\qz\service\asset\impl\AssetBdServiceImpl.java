package com.qz.service.asset.impl;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.afterturn.easypoi.exception.excel.ExcelImportException;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qz.entity.asset.AssetBd;
import com.qz.entity.asset.LocationsBdz;
import com.qz.entity.asset.LocationsJg;
import com.qz.enums.ResponseCode;
import com.qz.exception.GlobalException;
import com.qz.mapper.asset.AssetBdMapper;
import com.qz.mapper.asset.AssetYxTowerMapper;
import com.qz.service.asset.IAssetBdService;
import com.qz.utils.ExportUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 变电设备 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-25
 */
@Service
public class AssetBdServiceImpl extends ServiceImpl<AssetBdMapper, AssetBd> implements IAssetBdService {

    @Autowired
    private AssetBdMapper assetBdMapper;

    @Autowired
    private AssetYxTowerMapper assetYxTowerMapper;

    @Value("${attachment.upload}")
    private String filePath;

    /**
     * 分页查询设备信息
     *
     * @param assetBd
     * @return
     */
    @Override
    public Page getBdAsesetListPage(AssetBd assetBd) {
        return assetBdMapper.getBdAsesetListPage(assetBd, assetBd.createPage());
    }

    /**
     * 获取变电站下拉框列表
     *
     * @param locationsBdz
     * @return
     */
    @Override
    public List<Map<String, Object>> getBdzDataListSelected(LocationsBdz locationsBdz) {
        return assetBdMapper.getBdzDataListSelected(locationsBdz);
    }

    /**
     * 获取间隔下拉框数据列表
     *
     * @param locationsJg
     * @return
     */
    @Override
    public List<Map<String, Object>> getJgDataListSelected(LocationsJg locationsJg) {
        return assetBdMapper.getJgDataListSelected(locationsJg);
    }

    /**
     * 获取设备类型下拉框数据
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> getSblxDataListSelected(Map<String, Object> paramMap) {
        return assetBdMapper.getSblxDataListSelected(paramMap);
    }

    @Override
    public List<Map<String, Object>> getSbslTj() {
        Map<String, Object> map = new HashMap<>();
        List<Map<String, Object>> list = new ArrayList<>();
        String bdzTj = assetBdMapper.getBdzTj();
        String byqTj = assetBdMapper.getByqTj();
        List<Map<String, Object>> xlTj = assetBdMapper.get6kvXlTj();
        String pdzTj = assetBdMapper.getPdzTj();
        String pdbyqTj = assetBdMapper.getPdByqTj();
        map.put("bdz", bdzTj);
        map.put("bdbyq", byqTj);
        map.put("xlTj6", xlTj);
        map.put("pdz", pdzTj);
        map.put("pdbyq", pdbyqTj);
        list.add(map);
        return list;
    }

    @Override
    public void exportExcel(HttpServletResponse response, Map<String, Object> dataMap) {
        String os = System.getProperty("os.name");
        String templatePath;
        if (os != null && os.toLowerCase().startsWith("windows")) {
            templatePath = System.getProperty("user.dir") + File.separator + "qz-manager" + File.separator + "src" + File.separator + "main" + File.separator + "resources" + File.separator + "excel" + File.separator + "sbjbxx.xls";
        } else {
            templatePath = filePath + "excel" + File.separator + "sbjbxx.xls";
        }
        Map<String, Object> map = new HashMap<>();
        List<Map<String, Object>> list = (List<Map<String, Object>>) dataMap.get("xlTj6");
        for (int i = 0; i < 3; i++) {
            Map<String, Object> list_map = list.get(i);
            map.put("xl" + i, list_map.get("xl"));
            map.put("cd" + i, list_map.get("cd"));
            map.put("gts" + i, list_map.get("gts"));
            map.put("dlcd" + i, list_map.get("dlcd"));
        }
        Object bdz = dataMap.get("bdz");
        Object bdbyq = dataMap.get("bdbyq");
        Object pdz = dataMap.get("pdz");
        Object pdbyq = dataMap.get("pdbyq");
        map.put("bdz", bdz);
        map.put("bdbyq", bdbyq);
        map.put("pdz", pdz);
        map.put("pdbyq", pdbyq);
        ExportUtils.exportExcelByEasyPoi(response, templatePath, map);
    }

    @Override
    public List<Map<String, Object>> getMapBdzList() {
        return assetBdMapper.getMapBdzList();
    }

    @Override
    public Map<String, Object> getHomePageSbjbxx() {
        HashMap<String, Object> map = new HashMap<>();
        double xlCount = 0;
        double cdCount = 0;
        double dlCount = 0;
        //线路数量
        for (Map<String, Object> objectMap : assetBdMapper.get6kvXlTj()) {
            String xl = objectMap.get("xl").toString();
            String cd = objectMap.get("cd").toString();
            String dl = objectMap.get("dlcd").toString();
            xlCount += Double.parseDouble(xl);
            cdCount += Double.parseDouble(cd);
            dlCount += Double.parseDouble(dl);
        }
        //变电站
        map.put("BdzTj", assetBdMapper.getBdzTj());
        //变压器
        map.put("PdByqTj", assetBdMapper.getByqTj());
        //线路
        map.put("xl", xlCount);
        map.put("cd", String.format("%.3f", cdCount));
        map.put("dl", dlCount);
        //杆塔
        map.put("tower", assetYxTowerMapper.getDataCount());
        //配电室
        map.put("pdz", assetBdMapper.getPdzTj());
        map.put("pdg", assetBdMapper.getPdgTj());
        //光伏电站
        map.put("gfdzTj", assetBdMapper.getGfdzTj());
        return map;
    }

    @Override
    public HashMap<String, List<Object>> getDefectStatistics() {
        List<Map<String, Object>> list = assetBdMapper.getDefectStatistics();
        HashMap<String, List<Object>> map = new HashMap<>();
        map.put("qxlb", new ArrayList<>());
        map.put("num", new ArrayList<>());
        map.put("num_all", new ArrayList<>());
        //试用这个方式
        List<String> strings = Arrays.asList("变电", "配电", "输电");
        if ( CollectionUtils.isNotEmpty(list)) {
            List<String> qxlb = list.stream().map(e -> e.get("qxlb").toString()).collect(Collectors.toList());
            for (String string : strings) {
                if (!qxlb.contains(string)) {
                    HashMap<String, Object> tempMap = new HashMap<>(3);
                    tempMap.put("qxlb",string);
                    tempMap.put("num",0);
                    tempMap.put("num_all",0);
                    list.add(tempMap);
                }
            }
            for (Map<String, Object> temp : list) {
                //库里有垃圾数据，这里进行一个判断来排除
                if (temp.get("qxlb")!=null) {
                    map.get("qxlb").add(temp.get("qxlb").toString());
                    map.get("num").add(Integer.parseInt(temp.get("num").toString()));
                    map.get("num_all").add(Integer.parseInt(temp.get("num_all").toString()));
                }
            }
        }else {
            map.get("qxlb").add("暂无数据");
            map.get("num").add(0);
            map.get("num_all").add(0);
        }
        return map;
    }

    @Override
    public void exportExcelAssetInfo(HttpServletResponse response, LocationsJg locationsJg) {
        locationsJg.setPageSize(500);
        //get变电站间隔信息
        List<Map<String, Object>> jgDataList = this.getJgDataListSelected(locationsJg);
        //结果集
        ArrayList<AssetBd> exportDataList = new ArrayList<>();
        //获取每个间隔设备信息
        jgDataList.forEach(a -> {
            String value = a.get("value").toString();
            AssetBd assetBd = new AssetBd();
            assetBd.setPageSize(500);
            assetBd.setSsjg(value);
            List<AssetBd> bdAssetDataList = this.getBdAsesetListPage(assetBd).getRecords();
            //add every Gj
            exportDataList.addAll(bdAssetDataList);
        });
        ExportUtils.exportExcelByEasyPoi(response, new ExportParams(exportDataList.get(0).getBdzmc() + "设备信息表", "变电设备信息表"), AssetBd.class, exportDataList);
    }

    @Override
    public Boolean importExcel(MultipartFile file, String ssgs, String ssbdz, String ssjg) throws Exception{
        ImportParams params = new ImportParams();
        params.setTitleRows(1);
        params.setHeadRows(1);
        //Excel列规定
        String[] importField = {};
        params.setImportFields(importField);
        //Excel字段值校验开启
        params.setNeedVerify(false);
        ExcelImportResult<AssetBd> excelImportResult = null;
        try {
            excelImportResult = ExcelImportUtil.importExcelMore(file.getInputStream(), AssetBd.class, params);
        } catch (ExcelImportException e) {
            throw new GlobalException(ResponseCode.FAILURE, "不是规范的Excel模板,请勿新增或删除列");
        }
        if (excelImportResult.isVerifyFail()) {
            throw new GlobalException(ResponseCode.FAILURE, "主键列存在空值，请检查要上传的Excel文件");
        } else {
            //处理导入数据
            List<AssetBd> list = excelImportResult.getList();
            for (AssetBd assetBd : list) {
                assetBd.setSsgs(ssgs);
                assetBd.setSsbdz(ssbdz);
                assetBd.setSsjg(ssjg);
                assetBd.setXh(assetBd.getGgxh());
            }
            this.saveBatch(list);
        }
        return true;
    }
}
