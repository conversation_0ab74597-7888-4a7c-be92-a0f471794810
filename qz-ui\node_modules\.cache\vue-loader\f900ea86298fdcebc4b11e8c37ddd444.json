{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\activiti_tdsqd\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\activiti_tdsqd\\index.vue", "mtime": 1720689385854}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/activiti_tdsqd", "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      id=\"dialogAct\"\n      class=\"tree\"\n      :title=\"option.title ? option.title : ''\"\n      :top=\"option.top ? option.top : '10vh'\"\n      v-dialogDrag\n      :visible.sync=\"isShow\"\n      :width=\"option.width ? option.width : '25%'\"\n      :close-on-click-modal=\"false\"\n      @close=\"toClose\"\n      :destroy-on-colse=\"true\"\n      :modal-append-to-body=\"false\"\n    >\n      <slot name=\"Dialog_content\" />\n      <el-form\n        label-width=\"120px\"\n        ref=\"form\"\n        :model=\"form\"\n        v-if=\"datas.defaultFrom ? true : false\"\n      >\n        <div>\n          <el-row>\n            <!--    根据人员组选人   -->\n            <div v-if=\"datas.processType === 'completeByGroup'\">\n              <el-col :span=\"24\" v-if=\"datas.nextUser ? datas.nextUser : true\">\n                <el-form-item prop=\"nextUser\" label=\"处理人选择：\">\n                  <el-select\n                    v-model=\"form.nextUser\"\n                    placeholder=\"请选择人员\"\n                    filterable\n                    multiple\n                    style=\"width: 100%;\"\n                    value-key=\"userName\"\n                  >\n                    <el-option\n                      v-for=\"item in ryOptions\"\n                      :key=\"item.userName\"\n                      :label=\"item.nickName\"\n                      :value=\"{\n                        userName: item.userName,\n                        nickName: item.nickName\n                      }\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </div>\n            <el-col :span=\"24\">\n              <el-form-item\n                prop=\"comment\"\n                label=\"回退原因：\"\n                v-if=\"datas.processType === 'rollback'\"\n              >\n                <el-input\n                  style=\"width: 100%\"\n                  v-model=\"form.comment\"\n                  type=\"textarea\"\n                  placeholder=\"请输入原因\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n      </el-form>\n      <span v-else>请确定是否提交</span>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"info\" @click=\"toClose\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"todoSubmit('form')\">确定</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { completeYjrwdTask } from \"@/api/activiti/processTask\";\nimport { Loading } from \"element-ui\";\nimport { getUsers } from \"@/api/system/usergroup\";\nexport default {\n  name: \"index\",\n  props: {\n    /*流程发起必填数据\n        processData:{\n          processDefinitionKey:\"流程定义的key\",//必填\n          taskId:\"任务id，如果任务在代办列表时，会根据代办列表获取taskid，否则需传入业务id来确定task\",\n          businessKey:\"业务id，对应业务的主键\",//taskid和businessKey两者必须有其中一个才可以确定一个task\n          businessType:\"业务类型，用于区分不同的业务\"//必填\n          variables:\"拓展参数\"//流程定义中设置的参数,\n          nextUser:\"如果流程实例中并未配置每一个节点的处理人，则需要用户手动选择每一个节点的处理人\",\n          nextUserInfo:{},//手动传的审核人信息\n          processType:'complete,rollback',\n          defaultFrom:true,false 是否需要默认表单\n        }*/\n    processData: {\n      type: Object,\n      default() {\n        return {\n          defaultFrom: true,\n          processType: \"complete\"\n        };\n      }\n    },\n    //显示隐藏\n    isShow: {\n      type: Boolean,\n      default: false\n    },\n    //子组件默认参数\n    option: {\n      type: Object,\n      default() {\n        return { title: \"审批\" };\n      }\n    }\n  },\n  data() {\n    return {\n      disabled: false,\n      form: {},\n      datas: {},\n      loading: null,\n      ryOptions: []\n    };\n  },\n  watch: {\n    processData: {\n      handler(newVal, oldVal) {\n        this.$nextTick(() => {\n          this.datas = { ...newVal };\n          //如果是需要从人员组中选人的，则从人员组查人\n          if (this.datas.processType === \"completeByGroup\") {\n            getUsers({\n              personGroupId: this.datas.variables.personGroupId,\n              deptId: this.datas.variables.deptId\n                ? this.datas.variables.deptId\n                : 0,\n              deptName: this.datas.variables.deptName\n                ? this.datas.variables.deptName\n                : \"\"\n            }).then(res => {\n              this.ryOptions = res.data;\n            });\n          }\n        });\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  mounted() {},\n  methods: {\n    //流程提交，流程拒绝\n    async todoSubmit() {\n      this.datas.routePath = this.$route.path;\n      if (this.form.nextUser) {\n        this.datas.nextUser = this.form.nextUser.map(item => item.userName).join(\",\")\n        this.datas.nextUserNickName = this.form.nextUser.map(item => item.nickName).join(\",\")\n      } else {\n        if (this.processData.nextUserInfo) {\n          //手动传的用户信息\n          this.datas.nextUser = this.processData.nextUserInfo.userName;\n          this.datas.nextUserNickName = this.processData.nextUserInfo.nickName;\n        } else {\n          this.datas.nextUser = undefined;\n          this.datas.nextUserNickName = undefined;\n        }\n      }\n      if (\n        !this.datas.nextUser &&\n        this.datas.processType === \"completeByGroup\" &&\n        this.datas.defaultFrom\n      ) {\n        this.$message({\n          type: \"error\",\n          message: \"请选择人员!\"\n        });\n        return;\n      }\n      if (this.form.comment) {\n        this.datas.variables.comment = this.form.comment;\n      }\n      this.$nextTick(() => {\n        // 以服务的方式调用的 Loading 需要异步关闭\n        this.loading = Loading.service({\n          lock: true, //lock的修改符--默认是false\n          text: \"流程进行中，请稍后\", //显示在加载图标下方的加载文案\n          spinner: \"el-icon-loading\", //自定义加载图标类名\n          background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n          target: document.querySelector(\"#dialogAct\")\n        });\n      });\n      let resultData;\n      //if(this.datas.processType==='complete'){\n      try {\n        let { code, data } = await completeYjrwdTask(this.datas);\n        if (code === \"0000\") {\n          resultData = data;\n        }\n        if (code) {\n          this.$nextTick(() => {\n            // 以服务的方式调用的 Loading 需要异步关闭\n            this.loading.close();\n          });\n        }\n      } catch (e) {\n        this.$nextTick(() => {\n          // 以服务的方式调用的 Loading 需要异步关闭\n          this.loading.close();\n        });\n      }\n      // }else{\n      //   try {\n      //     console.log(this.datas)\n      //     let {code,data} =await rollbackTask(this.datas)\n      //     if(code==='0000'){\n      //       resultData=data\n      //     }\n      //     if(code){\n      //       this.$nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭\n      //         this.loading.close();\n      //       });\n      //     }\n      //   }catch (e) {\n      //     this.$nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭\n      //       this.loading.close();\n      //     });\n      //   }\n      // }\n      if (resultData) {\n        console.log(resultData);\n        resultData.processType = this.datas.processType;\n        this.$emit(\"todoData\", resultData);\n      }\n      this.toClose();\n    },\n    toClose() {\n      this.$set(this.form,'nextUser',\"\")\n      this.$set(this.form,'comment',\"\")\n      this.$emit(\"toClose\", \"close\");\n    }\n  }\n};\n</script>\n\n<style scoped></style>\n"]}]}