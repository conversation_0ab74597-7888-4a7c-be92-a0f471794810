{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\bddzcz\\dzczp.vue?vue&type=template&id=35e11ae1&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\bddzcz\\dzczp.vue", "mtime": 1749014635777}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}