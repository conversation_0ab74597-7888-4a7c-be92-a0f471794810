{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\store\\modules\\tagsView.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\store\\modules\\tagsView.js", "mtime": 1706897321702}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/store/modules/tagsView.js"], "names": ["state", "visitedViews", "cachedViews", "mutations", "ADD_VISITED_VIEW", "view", "some", "v", "path", "push", "Object", "assign", "title", "meta", "ADD_CACHED_VIEW", "includes", "name", "noCache", "DEL_VISITED_VIEW", "entries", "i", "splice", "DEL_CACHED_VIEW", "index", "indexOf", "DEL_OTHERS_VISITED_VIEWS", "filter", "affix", "DEL_OTHERS_CACHED_VIEWS", "slice", "DEL_ALL_VISITED_VIEWS", "affixTags", "tag", "DEL_ALL_CACHED_VIEWS", "UPDATE_VISITED_VIEW", "actions", "add<PERSON><PERSON><PERSON>", "dispatch", "addVisitedView", "commit", "add<PERSON><PERSON>d<PERSON>iew", "<PERSON><PERSON><PERSON><PERSON>", "Promise", "resolve", "delVisitedView", "del<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "delOthersViews", "delOthersVisitedViews", "delOthersCachedViews", "delAllViews", "delAllVisitedViews", "delAllCachedViews", "updateVisitedView", "namespaced"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAMA,KAAK,GAAG;AACZC,EAAAA,YAAY,EAAE,EADF;AAEZC,EAAAA,WAAW,EAAE;AAFD,CAAd;AAKA,IAAMC,SAAS,GAAG;AAChBC,EAAAA,gBAAgB,EAAE,0BAACJ,KAAD,EAAQK,IAAR,EAAiB;AACjC,QAAIL,KAAK,CAACC,YAAN,CAAmBK,IAAnB,CAAwB,UAAAC,CAAC;AAAA,aAAIA,CAAC,CAACC,IAAF,KAAWH,IAAI,CAACG,IAApB;AAAA,KAAzB,CAAJ,EAAwD;AACxDR,IAAAA,KAAK,CAACC,YAAN,CAAmBQ,IAAnB,CACEC,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBN,IAAlB,EAAwB;AACtBO,MAAAA,KAAK,EAAEP,IAAI,CAACQ,IAAL,CAAUD,KAAV,IAAmB;AADJ,KAAxB,CADF;AAKD,GARe;AAShBE,EAAAA,eAAe,EAAE,yBAACd,KAAD,EAAQK,IAAR,EAAiB;AAChC,QAAIL,KAAK,CAACE,WAAN,CAAkBa,QAAlB,CAA2BV,IAAI,CAACW,IAAhC,CAAJ,EAA2C;;AAC3C,QAAI,CAACX,IAAI,CAACQ,IAAL,CAAUI,OAAf,EAAwB;AACtBjB,MAAAA,KAAK,CAACE,WAAN,CAAkBO,IAAlB,CAAuBJ,IAAI,CAACW,IAA5B;AACD;AACF,GAde;AAgBhBE,EAAAA,gBAAgB,EAAE,0BAAClB,KAAD,EAAQK,IAAR,EAAiB;AAAA,6DACZL,KAAK,CAACC,YAAN,CAAmBkB,OAAnB,EADY;AAAA;;AAAA;AACjC,0DAAmD;AAAA;AAAA,YAAvCC,CAAuC;AAAA,YAApCb,CAAoC;;AACjD,YAAIA,CAAC,CAACC,IAAF,KAAWH,IAAI,CAACG,IAApB,EAA0B;AACxBR,UAAAA,KAAK,CAACC,YAAN,CAAmBoB,MAAnB,CAA0BD,CAA1B,EAA6B,CAA7B;AACA;AACD;AACF;AANgC;AAAA;AAAA;AAAA;AAAA;AAOlC,GAvBe;AAwBhBE,EAAAA,eAAe,EAAE,yBAACtB,KAAD,EAAQK,IAAR,EAAiB;AAChC,QAAMkB,KAAK,GAAGvB,KAAK,CAACE,WAAN,CAAkBsB,OAAlB,CAA0BnB,IAAI,CAACW,IAA/B,CAAd;AACAO,IAAAA,KAAK,GAAG,CAAC,CAAT,IAAcvB,KAAK,CAACE,WAAN,CAAkBmB,MAAlB,CAAyBE,KAAzB,EAAgC,CAAhC,CAAd;AACD,GA3Be;AA6BhBE,EAAAA,wBAAwB,EAAE,kCAACzB,KAAD,EAAQK,IAAR,EAAiB;AACzCL,IAAAA,KAAK,CAACC,YAAN,GAAqBD,KAAK,CAACC,YAAN,CAAmByB,MAAnB,CAA0B,UAAAnB,CAAC,EAAI;AAClD,aAAOA,CAAC,CAACM,IAAF,CAAOc,KAAP,IAAgBpB,CAAC,CAACC,IAAF,KAAWH,IAAI,CAACG,IAAvC;AACD,KAFoB,CAArB;AAGD,GAjCe;AAkChBoB,EAAAA,uBAAuB,EAAE,iCAAC5B,KAAD,EAAQK,IAAR,EAAiB;AACxC,QAAMkB,KAAK,GAAGvB,KAAK,CAACE,WAAN,CAAkBsB,OAAlB,CAA0BnB,IAAI,CAACW,IAA/B,CAAd;;AACA,QAAIO,KAAK,GAAG,CAAC,CAAb,EAAgB;AACdvB,MAAAA,KAAK,CAACE,WAAN,GAAoBF,KAAK,CAACE,WAAN,CAAkB2B,KAAlB,CAAwBN,KAAxB,EAA+BA,KAAK,GAAG,CAAvC,CAApB;AACD,KAFD,MAEO;AACLvB,MAAAA,KAAK,CAACE,WAAN,GAAoB,EAApB;AACD;AACF,GAzCe;AA2ChB4B,EAAAA,qBAAqB,EAAE,+BAAA9B,KAAK,EAAI;AAC9B;AACA,QAAM+B,SAAS,GAAG/B,KAAK,CAACC,YAAN,CAAmByB,MAAnB,CAA0B,UAAAM,GAAG;AAAA,aAAIA,GAAG,CAACnB,IAAJ,CAASc,KAAb;AAAA,KAA7B,CAAlB;AACA3B,IAAAA,KAAK,CAACC,YAAN,GAAqB8B,SAArB;AACD,GA/Ce;AAgDhBE,EAAAA,oBAAoB,EAAE,8BAAAjC,KAAK,EAAI;AAC7BA,IAAAA,KAAK,CAACE,WAAN,GAAoB,EAApB;AACD,GAlDe;AAoDhBgC,EAAAA,mBAAmB,EAAE,6BAAClC,KAAD,EAAQK,IAAR,EAAiB;AAAA,8DACtBL,KAAK,CAACC,YADgB;AAAA;;AAAA;AACpC,6DAAkC;AAAA,YAAzBM,CAAyB;;AAChC,YAAIA,CAAC,CAACC,IAAF,KAAWH,IAAI,CAACG,IAApB,EAA0B;AACxBD,UAAAA,CAAC,GAAGG,MAAM,CAACC,MAAP,CAAcJ,CAAd,EAAiBF,IAAjB,CAAJ;AACA;AACD;AACF;AANmC;AAAA;AAAA;AAAA;AAAA;AAOrC;AA3De,CAAlB;AA8DA,IAAM8B,OAAO,GAAG;AACdC,EAAAA,OADc,yBACQ/B,IADR,EACc;AAAA,QAAlBgC,QAAkB,QAAlBA,QAAkB;AAC1BA,IAAAA,QAAQ,CAAC,gBAAD,EAAmBhC,IAAnB,CAAR;AACAgC,IAAAA,QAAQ,CAAC,eAAD,EAAkBhC,IAAlB,CAAR;AACD,GAJa;AAKdiC,EAAAA,cALc,iCAKajC,IALb,EAKmB;AAAA,QAAhBkC,MAAgB,SAAhBA,MAAgB;AAC/BA,IAAAA,MAAM,CAAC,kBAAD,EAAqBlC,IAArB,CAAN;AACD,GAPa;AAQdmC,EAAAA,aARc,gCAQYnC,IARZ,EAQkB;AAAA,QAAhBkC,MAAgB,SAAhBA,MAAgB;AAC9BA,IAAAA,MAAM,CAAC,iBAAD,EAAoBlC,IAApB,CAAN;AACD,GAVa;AAYdoC,EAAAA,OAZc,0BAYepC,IAZf,EAYqB;AAAA,QAAzBgC,QAAyB,SAAzBA,QAAyB;AAAA,QAAfrC,KAAe,SAAfA,KAAe;AACjC,WAAO,IAAI0C,OAAJ,CAAY,UAAAC,OAAO,EAAI;AAC5BN,MAAAA,QAAQ,CAAC,gBAAD,EAAmBhC,IAAnB,CAAR;AACAgC,MAAAA,QAAQ,CAAC,eAAD,EAAkBhC,IAAlB,CAAR;AACAsC,MAAAA,OAAO,CAAC;AACN1C,QAAAA,YAAY,mCAAMD,KAAK,CAACC,YAAZ,CADN;AAENC,QAAAA,WAAW,mCAAMF,KAAK,CAACE,WAAZ;AAFL,OAAD,CAAP;AAID,KAPM,CAAP;AAQD,GArBa;AAsBd0C,EAAAA,cAtBc,iCAsBoBvC,IAtBpB,EAsB0B;AAAA,QAAvBkC,MAAuB,SAAvBA,MAAuB;AAAA,QAAfvC,KAAe,SAAfA,KAAe;AACtC,WAAO,IAAI0C,OAAJ,CAAY,UAAAC,OAAO,EAAI;AAC5BJ,MAAAA,MAAM,CAAC,kBAAD,EAAqBlC,IAArB,CAAN;AACAsC,MAAAA,OAAO,kCAAK3C,KAAK,CAACC,YAAX,EAAP;AACD,KAHM,CAAP;AAID,GA3Ba;AA4Bd4C,EAAAA,aA5Bc,gCA4BmBxC,IA5BnB,EA4ByB;AAAA,QAAvBkC,MAAuB,SAAvBA,MAAuB;AAAA,QAAfvC,KAAe,SAAfA,KAAe;AACrC,WAAO,IAAI0C,OAAJ,CAAY,UAAAC,OAAO,EAAI;AAC5BJ,MAAAA,MAAM,CAAC,iBAAD,EAAoBlC,IAApB,CAAN;AACAsC,MAAAA,OAAO,kCAAK3C,KAAK,CAACE,WAAX,EAAP;AACD,KAHM,CAAP;AAID,GAjCa;AAmCd4C,EAAAA,cAnCc,iCAmCsBzC,IAnCtB,EAmC4B;AAAA,QAAzBgC,QAAyB,SAAzBA,QAAyB;AAAA,QAAfrC,KAAe,SAAfA,KAAe;AACxC,WAAO,IAAI0C,OAAJ,CAAY,UAAAC,OAAO,EAAI;AAC5BN,MAAAA,QAAQ,CAAC,uBAAD,EAA0BhC,IAA1B,CAAR;AACAgC,MAAAA,QAAQ,CAAC,sBAAD,EAAyBhC,IAAzB,CAAR;AACAsC,MAAAA,OAAO,CAAC;AACN1C,QAAAA,YAAY,mCAAMD,KAAK,CAACC,YAAZ,CADN;AAENC,QAAAA,WAAW,mCAAMF,KAAK,CAACE,WAAZ;AAFL,OAAD,CAAP;AAID,KAPM,CAAP;AAQD,GA5Ca;AA6Cd6C,EAAAA,qBA7Cc,wCA6C2B1C,IA7C3B,EA6CiC;AAAA,QAAvBkC,MAAuB,SAAvBA,MAAuB;AAAA,QAAfvC,KAAe,SAAfA,KAAe;AAC7C,WAAO,IAAI0C,OAAJ,CAAY,UAAAC,OAAO,EAAI;AAC5BJ,MAAAA,MAAM,CAAC,0BAAD,EAA6BlC,IAA7B,CAAN;AACAsC,MAAAA,OAAO,kCAAK3C,KAAK,CAACC,YAAX,EAAP;AACD,KAHM,CAAP;AAID,GAlDa;AAmDd+C,EAAAA,oBAnDc,uCAmD0B3C,IAnD1B,EAmDgC;AAAA,QAAvBkC,MAAuB,SAAvBA,MAAuB;AAAA,QAAfvC,KAAe,SAAfA,KAAe;AAC5C,WAAO,IAAI0C,OAAJ,CAAY,UAAAC,OAAO,EAAI;AAC5BJ,MAAAA,MAAM,CAAC,yBAAD,EAA4BlC,IAA5B,CAAN;AACAsC,MAAAA,OAAO,kCAAK3C,KAAK,CAACE,WAAX,EAAP;AACD,KAHM,CAAP;AAID,GAxDa;AA0Dd+C,EAAAA,WA1Dc,+BA0DmB5C,IA1DnB,EA0DyB;AAAA,QAAzBgC,QAAyB,UAAzBA,QAAyB;AAAA,QAAfrC,KAAe,UAAfA,KAAe;AACrC,WAAO,IAAI0C,OAAJ,CAAY,UAAAC,OAAO,EAAI;AAC5BN,MAAAA,QAAQ,CAAC,oBAAD,EAAuBhC,IAAvB,CAAR;AACAgC,MAAAA,QAAQ,CAAC,mBAAD,EAAsBhC,IAAtB,CAAR;AACAsC,MAAAA,OAAO,CAAC;AACN1C,QAAAA,YAAY,mCAAMD,KAAK,CAACC,YAAZ,CADN;AAENC,QAAAA,WAAW,mCAAMF,KAAK,CAACE,WAAZ;AAFL,OAAD,CAAP;AAID,KAPM,CAAP;AAQD,GAnEa;AAoEdgD,EAAAA,kBApEc,sCAoEwB;AAAA,QAAjBX,MAAiB,UAAjBA,MAAiB;AAAA,QAATvC,KAAS,UAATA,KAAS;AACpC,WAAO,IAAI0C,OAAJ,CAAY,UAAAC,OAAO,EAAI;AAC5BJ,MAAAA,MAAM,CAAC,uBAAD,CAAN;AACAI,MAAAA,OAAO,kCAAK3C,KAAK,CAACC,YAAX,EAAP;AACD,KAHM,CAAP;AAID,GAzEa;AA0EdkD,EAAAA,iBA1Ec,qCA0EuB;AAAA,QAAjBZ,MAAiB,UAAjBA,MAAiB;AAAA,QAATvC,KAAS,UAATA,KAAS;AACnC,WAAO,IAAI0C,OAAJ,CAAY,UAAAC,OAAO,EAAI;AAC5BJ,MAAAA,MAAM,CAAC,sBAAD,CAAN;AACAI,MAAAA,OAAO,kCAAK3C,KAAK,CAACE,WAAX,EAAP;AACD,KAHM,CAAP;AAID,GA/Ea;AAiFdkD,EAAAA,iBAjFc,qCAiFgB/C,IAjFhB,EAiFsB;AAAA,QAAhBkC,MAAgB,UAAhBA,MAAgB;AAClCA,IAAAA,MAAM,CAAC,qBAAD,EAAwBlC,IAAxB,CAAN;AACD;AAnFa,CAAhB;eAsFe;AACbgD,EAAAA,UAAU,EAAE,IADC;AAEbrD,EAAAA,KAAK,EAALA,KAFa;AAGbG,EAAAA,SAAS,EAATA,SAHa;AAIbgC,EAAAA,OAAO,EAAPA;AAJa,C", "sourcesContent": ["const state = {\n  visitedViews: [],\n  cachedViews: []\n}\n\nconst mutations = {\n  ADD_VISITED_VIEW: (state, view) => {\n    if (state.visitedViews.some(v => v.path === view.path)) return\n    state.visitedViews.push(\n      Object.assign({}, view, {\n        title: view.meta.title || 'no-name'\n      })\n    )\n  },\n  ADD_CACHED_VIEW: (state, view) => {\n    if (state.cachedViews.includes(view.name)) return\n    if (!view.meta.noCache) {\n      state.cachedViews.push(view.name)\n    }\n  },\n\n  DEL_VISITED_VIEW: (state, view) => {\n    for (const [i, v] of state.visitedViews.entries()) {\n      if (v.path === view.path) {\n        state.visitedViews.splice(i, 1)\n        break\n      }\n    }\n  },\n  DEL_CACHED_VIEW: (state, view) => {\n    const index = state.cachedViews.indexOf(view.name)\n    index > -1 && state.cachedViews.splice(index, 1)\n  },\n\n  DEL_OTHERS_VISITED_VIEWS: (state, view) => {\n    state.visitedViews = state.visitedViews.filter(v => {\n      return v.meta.affix || v.path === view.path\n    })\n  },\n  DEL_OTHERS_CACHED_VIEWS: (state, view) => {\n    const index = state.cachedViews.indexOf(view.name)\n    if (index > -1) {\n      state.cachedViews = state.cachedViews.slice(index, index + 1)\n    } else {\n      state.cachedViews = []\n    }\n  },\n\n  DEL_ALL_VISITED_VIEWS: state => {\n    // keep affix tags\n    const affixTags = state.visitedViews.filter(tag => tag.meta.affix)\n    state.visitedViews = affixTags\n  },\n  DEL_ALL_CACHED_VIEWS: state => {\n    state.cachedViews = []\n  },\n\n  UPDATE_VISITED_VIEW: (state, view) => {\n    for (let v of state.visitedViews) {\n      if (v.path === view.path) {\n        v = Object.assign(v, view)\n        break\n      }\n    }\n  }\n}\n\nconst actions = {\n  addView({ dispatch }, view) {\n    dispatch('addVisitedView', view)\n    dispatch('addCachedView', view)\n  },\n  addVisitedView({ commit }, view) {\n    commit('ADD_VISITED_VIEW', view)\n  },\n  addCachedView({ commit }, view) {\n    commit('ADD_CACHED_VIEW', view)\n  },\n\n  delView({ dispatch, state }, view) {\n    return new Promise(resolve => {\n      dispatch('delVisitedView', view)\n      dispatch('delCachedView', view)\n      resolve({\n        visitedViews: [...state.visitedViews],\n        cachedViews: [...state.cachedViews]\n      })\n    })\n  },\n  delVisitedView({ commit, state }, view) {\n    return new Promise(resolve => {\n      commit('DEL_VISITED_VIEW', view)\n      resolve([...state.visitedViews])\n    })\n  },\n  delCachedView({ commit, state }, view) {\n    return new Promise(resolve => {\n      commit('DEL_CACHED_VIEW', view)\n      resolve([...state.cachedViews])\n    })\n  },\n\n  delOthersViews({ dispatch, state }, view) {\n    return new Promise(resolve => {\n      dispatch('delOthersVisitedViews', view)\n      dispatch('delOthersCachedViews', view)\n      resolve({\n        visitedViews: [...state.visitedViews],\n        cachedViews: [...state.cachedViews]\n      })\n    })\n  },\n  delOthersVisitedViews({ commit, state }, view) {\n    return new Promise(resolve => {\n      commit('DEL_OTHERS_VISITED_VIEWS', view)\n      resolve([...state.visitedViews])\n    })\n  },\n  delOthersCachedViews({ commit, state }, view) {\n    return new Promise(resolve => {\n      commit('DEL_OTHERS_CACHED_VIEWS', view)\n      resolve([...state.cachedViews])\n    })\n  },\n\n  delAllViews({ dispatch, state }, view) {\n    return new Promise(resolve => {\n      dispatch('delAllVisitedViews', view)\n      dispatch('delAllCachedViews', view)\n      resolve({\n        visitedViews: [...state.visitedViews],\n        cachedViews: [...state.cachedViews]\n      })\n    })\n  },\n  delAllVisitedViews({ commit, state }) {\n    return new Promise(resolve => {\n      commit('DEL_ALL_VISITED_VIEWS')\n      resolve([...state.visitedViews])\n    })\n  },\n  delAllCachedViews({ commit, state }) {\n    return new Promise(resolve => {\n      commit('DEL_ALL_CACHED_VIEWS')\n      resolve([...state.cachedViews])\n    })\n  },\n\n  updateVisitedView({ commit }, view) {\n    commit('UPDATE_VISITED_VIEW', view)\n  }\n}\n\nexport default {\n  namespaced: true,\n  state,\n  mutations,\n  actions\n}\n"]}]}