{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\xldzcz\\dzczp.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\xldzcz\\dzczp.vue", "mtime": 1719919561036}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsKaW1wb3J0IHsKICBnZXRDenBteExpc3QsCiAgZ2V0TGlzdCwKICByZW1vdmUsCiAgc2F2ZU9yVXBkYXRlcwp9IGZyb20gIkAvYXBpL3l4Z2wvc2R5eGdsL3NkZHpjenAiOwppbXBvcnQgc2R4bFNlbGVjdGVkIGZyb20gIkAvdmlld3MvZGFnYW5nT2lsZmllbGQveGpnbC9zZHhqL3NkeGxTZWxlY3RlZCI7CmltcG9ydCB7CiAgZXhwb3J0UGRmLAogIGV4cG9ydFdvcmQsCiAgcHJldmlld0ZpbGUsCiAgc2F2ZU9yVXBkYXRlLAogIHVwZGF0ZUJ5SWQKfSBmcm9tICJAL2FwaS95eGdsL2JkeXhnbC9iZGR6Y3pwIjsKaW1wb3J0IGFwaSBmcm9tICJAL3V0aWxzL3JlcXVlc3QiOwovL+a1geeoiwppbXBvcnQgYWN0aXZpdGkgZnJvbSAiY29tL2FjdGl2aXRpX2N6cCI7CmltcG9ydCB0aW1lTGluZSBmcm9tICJjb20vdGltZUxpbmUiOwppbXBvcnQgeyBIaXN0b3J5TGlzdCB9IGZyb20gIkAvYXBpL2FjdGl2aXRpL3Byb2Nlc3NUYXNrIjsKaW1wb3J0IEVsSW1hZ2VWaWV3ZXIgZnJvbSAiZWxlbWVudC11aS9wYWNrYWdlcy9pbWFnZS9zcmMvaW1hZ2Utdmlld2VyIjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJkemN6cCIsCiAgY29tcG9uZW50czogeyBzZHhsU2VsZWN0ZWQsIGFjdGl2aXRpLCB0aW1lTGluZSwgRWxJbWFnZVZpZXdlciB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBoYXNTdXBlclJvbGU6IHRoaXMuJHN0b3JlLmdldHRlcnMuaGFzU3VwZXJSb2xlLAogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgaXNEaXNhYmxlZEJqOiB0cnVlLAogICAgICBidXR0b25OYW1lU2hvdzogZmFsc2UsCiAgICAgIGJ1dHRvbk5hbWU6ICIiLAogICAgICBydWxlczogewogICAgICAgIGtzc2o6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmk43kvZzlvIDlp4vml7bpl7TkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAganNzajogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaTjeS9nOe7k+adn+aXtumXtOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0KICAgICAgICBdLAogICAgICAgIGZnczogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLliIblhazlj7jkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAic2VsZWN0IiB9XSwKICAgICAgICBiZHptYzogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWPmOeUteermeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJzZWxlY3QiIH0KICAgICAgICBdLAogICAgICAgIGN6cnc6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmk43kvZzku7vliqHkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgLy8gY3pyOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaTjeS9nOS6uuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9XSwKICAgICAgICAvLyBqaHI6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi55uR5oqk5Lq65LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH1dLAogICAgICAgIHhscjogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuIvku6TkurrkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfV0sCiAgICAgICAgLy8gc3ByOiBbCiAgICAgICAgLy8gICB7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICflrqHnpajkurrkuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnYmx1cid9CiAgICAgICAgLy8gXSwKICAgICAgICBjenhzOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5pON5L2c6aG55pWw5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHl6eGN6eHM6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlt7LmiafooYzpobnmlbDkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgd3p4Y3p4czogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuacquaJp+ihjOmhueaVsOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBzZnl6eDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaYr+WQpuW3suaJp+ihjOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJzZWxlY3QiIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIC8v54q25oCB5LiL5ouJ5qGG5pWw5o2uCiAgICAgIHN0YXR1c09wdGlvbnM6IFsKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIjAiLAogICAgICAgICAgbGFiZWw6ICLmk43kvZznpajloavmiqUiCiAgICAgICAgfSwKICAgICAgICAvLyB7CiAgICAgICAgLy8gICB2YWx1ZTogIjEiLAogICAgICAgIC8vICAgbGFiZWw6ICLnj63nu4TlrqHmoLgiCiAgICAgICAgLy8gfSwKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIjIiLAogICAgICAgICAgbGFiZWw6ICLliIblhazlj7jlrqHmoLgiCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIjMiLAogICAgICAgICAgbGFiZWw6ICLmk43kvZznpajlip7nu5MiCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIjQiLAogICAgICAgICAgbGFiZWw6ICLnu5PmnZ8iCiAgICAgICAgfSx7IGxhYmVsOiAi5L2c5bqfIiwgdmFsdWU6ICI3IiB9CiAgICAgIF0sCiAgICAgIGJqcjogIiIsCiAgICAgIC8vIOaYr+WQpuW3suaJp+ihjOS4i+aLieahhgogICAgICBzZnl6eExpc3Q6IFsKICAgICAgICB7IGxhYmVsOiAi5bey5omn6KGMIiwgdmFsdWU6ICLlt7LmiafooYwiIH0sCiAgICAgICAgeyBsYWJlbDogIuacquaJp+ihjCIsIHZhbHVlOiAi5pyq5omn6KGMIiB9CiAgICAgIF0sCiAgICAgIC8vIOiOt+WPluW9k+WJjeeZu+W9leS6uui0puWPtwogICAgICBjdXJyZW50VXNlcjogdGhpcy4kc3RvcmUuZ2V0dGVycy5uYW1lLAogICAgICAvL+S4iuS8oOWbvueJh+aXtueahOaQuuW4pueahOWFtuS7luWPguaVsAogICAgICB1cGxvYWRJbWdEYXRhOiB7CiAgICAgICAgYnVzaW5lc3NJZDogIiIgLy/mkLrluKbnmoTooajljZXkuLvplK5pZAogICAgICB9LAogICAgICAvL+S4iuS8oOWbvueJh+aXtueahOivt+axguWktAogICAgICBoZWFkZXI6IHt9LAogICAgICAvL+WbvueJh2xpc3QKICAgICAgaW1nTGlzdDogW10sCiAgICAgIC8v5Zu+54mH5Zyw5Z2AdXJsCiAgICAgIGRpYWxvZ0ltYWdlVXJsOiAiIiwKICAgICAgLy/lsZXnpLrlm77niYdkaWFsb2fmjqfliLYKICAgICAgaW1nRGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIC8v5bel5L2c5rWB5Lyg5YWl5Y+C5pWwCiAgICAgIHByb2Nlc3NEYXRhOiB7CiAgICAgICAgcHJvY2Vzc0RlZmluaXRpb25LZXk6ICJjenBsYyIsCiAgICAgICAgYnVzaW5lc3NLZXk6ICIiLAogICAgICAgIGJ1c2luZXNzVHlwZTogIuWAkumXuOaTjeS9nOelqCIsCiAgICAgICAgdmFyaWFibGVzOiB7fSwKICAgICAgICBkZWZhdWx0RnJvbTogdHJ1ZSwKICAgICAgICBuZXh0VXNlcjogIiIsCiAgICAgICAgcHJvY2Vzc1R5cGU6ICJjb21wbGV0ZSIKICAgICAgfSwKICAgICAgLy/lt6XkvZzmtYHlvLnnqpcKICAgICAgaXNTaG93OiBmYWxzZSwKICAgICAgLy/mtYHnqIvlm77mn6XnnIsKICAgICAgb3BlbkxvYWRpbmdJbWc6IGZhbHNlLAogICAgICBpbWdTcmM6ICIiLCAvL+a1geeoi+Wbvuafpeeci+WcsOWdgAogICAgICB0aW1lRGF0YTogW10sCiAgICAgIHRpbWVMaW5lU2hvdzogZmFsc2UsCiAgICAgIC8v5by55Ye65qGG5qCH6aKYCiAgICAgIGFjdGl2aXRpT3B0aW9uOiB7IHRpdGxlOiAi5LiK5oqlIiB9LAogICAgICB0aXRsZXlsOiAiIiwKICAgICAgeWw6IGZhbHNlLAogICAgICBpc1NhdmVSb3c6IGZhbHNlLAogICAgICAvL+e6v+i3r+mAieaLqeW8ueWHugogICAgICBpc1Nob3dYbERpYWxvZzogZmFsc2UsCiAgICAgIC8vIOWkmumAieahhumAieS4reeahGlkCiAgICAgIGlkczogW10sCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgIHNlbGVjdERhdGE6IFtdLAogICAgICAvL+W8ueWHuuahhuS4reihqOagvOaVsOaNrgogICAgICBwcm9wVGFibGVEYXRhOiB7CiAgICAgICAgc2VsOiBudWxsLCAvLyDpgInkuK3ooYwKICAgICAgICBjb2xGaXJzdDogW10KICAgICAgfSwKICAgICAgLy9mb3Jt6KGo5Y2VCiAgICAgIGZvcm06IHsKICAgICAgICBiaDogIiIsCiAgICAgICAgc3RhdHVzOiAiIiwKICAgICAgICBjenI6ICIiLAogICAgICAgIGpocjogIiIsCiAgICAgICAgc2RzaHI6ICIiLAogICAgICAgIGx4OiAxLCAvL+i+k+eUtQogICAgICAgIGNvbEZpcnN0OiBbXSwKICAgICAgICBiejogIiIKICAgICAgfSwKICAgICAgZm9ybUN6cDogewogICAgICAgIHNkc2hyOiAiIgogICAgICB9LAogICAgICAvL+ivpuaDheW8ueahhuaYr+WQpuaYvuekugogICAgICBpc1Nob3dEZXRhaWxzOiBmYWxzZSwKICAgICAgLy/mmK/lkKbnpoHnlKgKICAgICAgaXNEaXNhYmxlZDogZmFsc2UsCiAgICAgIC8v5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgZmlsdGVySW5mbzogewogICAgICAgIGRhdGE6IHsKICAgICAgICAgIGJtOiAiIiwKICAgICAgICAgIHN0YXR1czogIiIsCiAgICAgICAgICBjenJ3OiAiIiwKICAgICAgICAgIHhscjogIiIsCiAgICAgICAgICB4bHNqOiAiIiwKICAgICAgICAgIGhsc2o6ICIiLAogICAgICAgICAgY3pyOiAiIiwKICAgICAgICAgIGpocjogIiIsCiAgICAgICAgICBzZHNocjogIiIsCiAgICAgICAgICBycUFycjogW10KICAgICAgICB9LCAvL+afpeivouadoeS7tgogICAgICAgIGZpZWxkTGlzdDogWwogICAgICAgICAgeyBsYWJlbDogIue8luWPtyIsIHZhbHVlOiAiYm0iLCB0eXBlOiAiaW5wdXQiLCBjbGVhcmFibGU6IHRydWUgfSwKCiAgICAgICAgICB7IGxhYmVsOiAi57q/6Lev5ZCN56ewIiwgdmFsdWU6ICJ4bG1jIiwgdHlwZTogImlucHV0IiwgY2xlYXJhYmxlOiB0cnVlIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5pON5L2c5Lq6IiwgdmFsdWU6ICJjenIiLCB0eXBlOiAiaW5wdXQiLCBjbGVhcmFibGU6IHRydWUgfSwKICAgICAgICAgIHsgbGFiZWw6ICLnm5HmiqTkuroiLCB2YWx1ZTogImpociIsIHR5cGU6ICJpbnB1dCIsIGNsZWFyYWJsZTogdHJ1ZSB9LAogICAgICAgICAgLy97IGxhYmVsOiAn5a6h5qC45Lq6JywgdmFsdWU6ICdzZHNocicsIHR5cGU6ICdpbnB1dCcsIGNsZWFyYWJsZTogdHJ1ZSB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIuaXpeacnyIsCiAgICAgICAgICAgIHZhbHVlOiAicnFBcnIiLAogICAgICAgICAgICB0eXBlOiAiZGF0ZSIsCiAgICAgICAgICAgIGRhdGVUeXBlOiAiZGF0ZXJhbmdlIiwKICAgICAgICAgICAgZm9ybWF0OiAieXl5eS1NTS1kZCIKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi54q25oCBIiwKICAgICAgICAgICAgdmFsdWU6ICJzdGF0dXMiLAogICAgICAgICAgICB0eXBlOiAiY2hlY2tib3giLAogICAgICAgICAgICBjaGVja2JveFZhbHVlOiBbXSwKICAgICAgICAgICAgY2xlYXJhYmxlOiB0cnVlLAogICAgICAgICAgICBvcHRpb25zOiBbCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgdmFsdWU6ICIwIiwKICAgICAgICAgICAgICAgIGxhYmVsOiAi5pON5L2c56Wo5aGr5oqlIgogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgLyogewogICAgICAgICAgICAgICAgdmFsdWU6ICcxJywKICAgICAgICAgICAgICAgIGxhYmVsOiAn54+t57uE5a6h5qC4JwogICAgICAgICAgICAgIH0sKi8KICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICB2YWx1ZTogIjIiLAogICAgICAgICAgICAgICAgbGFiZWw6ICLliIblhazlj7jlrqHmoLgiCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICB2YWx1ZTogIjMiLAogICAgICAgICAgICAgICAgbGFiZWw6ICLmk43kvZznpajlip7nu5MiCiAgICAgICAgICAgICAgfSx7IGxhYmVsOiAi5L2c5bqfIiwgdmFsdWU6ICI3IiB9CiAgICAgICAgICAgIF0KICAgICAgICAgIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIHRhYmxlQW5kUGFnZUluZm86IHsKICAgICAgICBwYWdlcjogewogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHRvdGFsOiAxLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMDBdCiAgICAgICAgfSwKICAgICAgICB0YWJsZURhdGE6IFtdLAogICAgICAgIHRhYmxlSGVhZGVyOiBbCiAgICAgICAgICB7IGxhYmVsOiAi57yW5Y+3IiwgcHJvcDogImJtIiwgbWluV2lkdGg6ICI4MCIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLnirbmgIEiLCBwcm9wOiAic3RhdHVzQ24iLCBtaW5XaWR0aDogIjgwIiB9LAogICAgICAgICAgeyBsYWJlbDogIue6v+i3r+WQjeensCIsIHByb3A6ICJ4bG1jIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5bel5L2c5ZCN56ewIiwgcHJvcDogImd6bWMiLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLlvIDnpajml7bpl7QiLCBwcm9wOiAiY3JlYXRlVGltZSIsIG1pbldpZHRoOiAiODAiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5pON5L2c5Lq6IiwgcHJvcDogImN6ciIsIG1pbldpZHRoOiAiNjAiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi55uR5oqk5Lq6IiwgcHJvcDogImpociIsIG1pbldpZHRoOiAiNjAiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5pel5pyfIiwgcHJvcDogInJxIiwgbWluV2lkdGg6ICI4MCIgfQogICAgICAgIF0sCiAgICAgICAgb3B0aW9uOiB7IGNoZWNrQm94OiB0cnVlLCBzZXJpYWxOdW1iZXI6IHRydWUgfQogICAgICB9LAogICAgICBwYXJhbXM6IHsKICAgICAgICAvL+i+k+eUtQogICAgICAgIGx4OiAxLAogICAgICAgIHN0YXR1czogIiIKICAgICAgfSwKICAgICAgc2VsZWN0Um93czogW10KICAgIH07CiAgfSwKICBtb3VudGVkKCkgewogICAgLy/ojrflj5Z0b2tlbgogICAgdGhpcy5oZWFkZXIudG9rZW4gPSBnZXRUb2tlbigpOwogICAgLy/liJfooajmn6Xor6IKICAgIHRoaXMuZ2V0RGF0YSh0aGlzLiRyb3V0ZS5xdWVyeSk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvL+S9nOW6n+elqAogICAgbnVsbGlmeUd6cChvYmpJZCkgewogICAgICB0aGlzLiRjb25maXJtKCLnpajkvZzlup/lkI7lj6rog73mn6XnnIvvvIzkuI3og73ov5vooYzku7vkvZXmk43kvZzvvIznoa7orqTkvZzlup/lkJc/IiwgIuaPkOekuiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pCiAgICAgICAgLnRoZW4oYXN5bmMgKCkgPT4gewogICAgICAgICAgbGV0IHsgY29kZSB9ID0gYXdhaXQgdXBkYXRlQnlJZCh7IHN0YXR1czogNywgb2JqSWQ6IG9iaklkIH0pOwogICAgICAgICAgaWYgKGNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKnyEhIik7CiAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgICAgLmNhdGNoKCgpID0+IHt9KTsKICAgIH0sCiAgICBnZXRTaG93KCkgewogICAgICB0aGlzLmJ1dHRvbk5hbWVTaG93ID0gZmFsc2U7CiAgICAgIHN3aXRjaCAodGhpcy5mb3JtLnN0YXR1cykgewogICAgICAgIGNhc2UgIjAiOgogICAgICAgICAgdGhpcy5idXR0b25OYW1lID0gIuS4iiDmiqUiOwogICAgICAgICAgaWYgKHRoaXMuY3VycmVudFVzZXIgPT09IHRoaXMuZm9ybS5jcmVhdGVCeSkgewogICAgICAgICAgICB0aGlzLmJ1dHRvbk5hbWVTaG93ID0gdHJ1ZTsKICAgICAgICAgIH0KICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgIjEiOgogICAgICAgICAgdGhpcy5idXR0b25OYW1lID0gIuaPkCDkuqQiOwogICAgICAgICAgaWYgKHRoaXMuZm9ybS5ienNwciA9PT0gdGhpcy5jdXJyZW50VXNlcikgewogICAgICAgICAgICB0aGlzLmJ1dHRvbk5hbWVTaG93ID0gdHJ1ZTsKICAgICAgICAgIH0KICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgIjIiOgogICAgICAgICAgdGhpcy5idXR0b25OYW1lID0gIuaPkCDkuqQiOwogICAgICAgICAgaWYgKHRoaXMuZm9ybS5mZ3NzcHIgPT09IHRoaXMuY3VycmVudFVzZXIpIHsKICAgICAgICAgICAgdGhpcy5idXR0b25OYW1lU2hvdyA9IHRydWU7CiAgICAgICAgICB9CiAgICAgICAgICBicmVhazsKICAgICAgICBjYXNlICIzIjoKICAgICAgICAgIHRoaXMuYnV0dG9uTmFtZSA9ICLlip4g57uTIjsKICAgICAgICAgIGlmICh0aGlzLmZvcm0uYmpyID09PSB0aGlzLmN1cnJlbnRVc2VyKSB7CiAgICAgICAgICAgIHRoaXMuYnV0dG9uTmFtZVNob3cgPSB0cnVlOwogICAgICAgICAgICB0aGlzLmlzRGlzYWJsZWRCaiA9IGZhbHNlOwogICAgICAgICAgfQogICAgICAgICAgYnJlYWs7CiAgICAgIH0KICAgIH0sCiAgICAvL+aUuemAoOWQjueahOS4iuS8oOWkmuS4quWbvueJh+aWh+S7tgogICAgdXBsb2FkRm9ybSgpIHsKICAgICAgdmFyIG5ld1VybCA9IFtdOyAvL+eUqOadpeWtmOaUvuW9k+WJjeacquaUueWKqOi/h+eahOWbvueJh3VybCzmraTlm77niYfkuI3ov5vooYzliKDpmaTlpITnkIbvvIzlhbbkvZnlvZPliY3kuJrliqFpZOS4i+mdoueahOWbvueJh+aVsOaNruWwhui/m+ihjOWIoOmZpAogICAgICAvLyBjb25zb2xlLmxvZyh0aGlzLiRyZWZzLnVwbG9hZEltZy5maWxlTGlzdCkKICAgICAgdmFyIGltYWdlVHlwZSA9IFsicG5nIiwgImpwZyJdOwoKICAgICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKTsKICAgICAgLy8g5Zug5Li66KaB5Lyg5LiA5Liq5paH5Lu25pWw57uE6L+H5Y6777yM5omA5Lul6KaB5b6q546vYXBwZW5kCiAgICAgIHRoaXMuaW1nTGlzdC5mb3JFYWNoKGZpbGUgPT4gewogICAgICAgIGlmIChmaWxlLnJhdyA9PSB1bmRlZmluZWQpIHsKICAgICAgICAgIG5ld1VybC5wdXNoKGZpbGUudXJsKTsKICAgICAgICB9CiAgICAgICAgZm9ybURhdGEuYXBwZW5kKCJmaWxlcyIsIGZpbGUucmF3KTsKICAgICAgfSk7CiAgICAgIGZvcm1EYXRhLmFwcGVuZCgiYnVzaW5lc3NJZCIsIHRoaXMudXBsb2FkSW1nRGF0YS5idXNpbmVzc0lkKTsgLy8g6Ieq5a6a5LmJ5Y+C5pWwCiAgICAgIGZvcm1EYXRhLmFwcGVuZCgibmV3VXJsIiwgbmV3VXJsKTsgLy8g5pyq5pS55Yqo6L+H55qE5Zu+54mHdXJsCiAgICAgIGZvcm1EYXRhLmFwcGVuZCgidHlwZSIsIGltYWdlVHlwZSk7IC8vIOacquaUueWKqOi/h+eahOWbvueJh3VybAogICAgICBhcGkKICAgICAgICAucmVxdWVzdFBvc3QoIi9pc2MtYXBpL2ZpbGUvdXBsb2FkRmlsZXMiLCBmb3JtRGF0YSwgMSkKICAgICAgICAudGhlbihyZXMgPT4gewogICAgICAgICAgLy8g5riF56m65Zu+54mH5YiX6KGo77yI5LiA5a6a6KaB5riF56m677yM5ZCm5YiZ5LiK5Lyg5oiQ5Yqf5ZCO6L+Y5piv5Lya6LCD55SoaGFuZGxlQ2hhbmdl77yI77yJ5Ye95pWw77yM5LiK5Lyg5oiQ5Yqf5ZCO5YiX6KGo5Lit6L+Y5a2Y5Zyo5Zu+54mH77yJCiAgICAgICAgICB0aGlzLmltZ0xpc3QgPSBbXTsKICAgICAgICAgIC8v5p+l6K+i5o6l5Y+j5pWw5o2uCiAgICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICB9KQogICAgICAgIC5jYXRjaChyZXMgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLlm77niYfkuIrkvKDlpLHotKXvvIEiKTsKICAgICAgICB9KTsKICAgIH0sCiAgICAvLyDpgInmi6nmlofku7bml7bvvIzlvoBmaWxlTGlzdOmHjOa3u+WKoAogICAgaGFuZGxlQ2hhbmdlKGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgIHRoaXMuaW1nTGlzdCA9IGZpbGVMaXN0OwogICAgfSwKICAgIGhhbmRsZVByb2dyZXNzKGV2ZW50LCBmaWxlLCBmaWxlTGlzdCkge30sCiAgICAvL+WbvueJh+enu+mZpAogICAgaGFuZGxlUmVtb3ZlKGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgIHRoaXMuaW1nTGlzdCA9IGZpbGVMaXN0OwogICAgfSwKICAgIC8v5Zu+54mH5pS+5aSnCiAgICBoYW5kbGVQaWN0dXJlQ2FyZFByZXZpZXcoZmlsZSkgewogICAgICB0aGlzLmRpYWxvZ0ltYWdlVXJsID0gW2ZpbGUudXJsXTsKICAgICAgdGhpcy5pbWdEaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICBvbkNsb3NlKCkgewogICAgICB0aGlzLmltZ0RpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgIH0sCiAgICAvL+a4heepugogICAgY2hhbmdlKGUpIHsKICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKTsKICAgIH0sCiAgICAvL+WFs+mXreW8ueeqlwogICAgY2xvc2VBY3Rpdml0aSgpIHsKICAgICAgaWYgKHRoaXMuJHJlZnMuYWN0aXZpdGkuJHJlZnMuZm9ybSkgewogICAgICAgIHRoaXMuJHJlZnMuYWN0aXZpdGkuJHJlZnMuZm9ybS5yZXNldEZpZWxkcygpOwogICAgICB9CiAgICAgIHRoaXMuaXNTaG93ID0gZmFsc2U7CiAgICB9LAogICAgLy/lhbPpl63mtYHnqIvmn6XnnIvpobXpnaIKICAgIGNvbHNlVGltZUxpbmUoKSB7CiAgICAgIHRoaXMudGltZUxpbmVTaG93ID0gZmFsc2U7CiAgICB9LAogICAgYXN5bmMgc2hvd1RpbWVMaW5lKHJvdykgewogICAgICB0aGlzLnByb2Nlc3NEYXRhLmJ1c2luZXNzS2V5ID0gcm93Lm9iaklkOwogICAgICBsZXQgeyBjb2RlLCBkYXRhIH0gPSBhd2FpdCBIaXN0b3J5TGlzdCh0aGlzLnByb2Nlc3NEYXRhKTsKICAgICAgdGhpcy50aW1lRGF0YSA9IGRhdGE7CiAgICAgIHRoaXMudGltZUxpbmVTaG93ID0gdHJ1ZTsKICAgIH0sCiAgICAvL+a1geeoi+WbvueJh+afpeeciwogICAgc2hvd1Byb2Nlc3NJbWcocm93KSB7CiAgICAgIHRoaXMub3BlbkxvYWRpbmdJbWcgPSB0cnVlOwogICAgICB0aGlzLmltZ1NyYyA9CiAgICAgICAgIi9hY3Rpdml0aS1hcGkvcHJvY2Vzcy9yZWFkLXJlc291cmNlP3Byb2Nlc3NEZWZpbml0aW9uS2V5PWN6cGxjJmJ1c2luZXNzS2V5PSIgKwogICAgICAgIHJvdy5vYmpJZCArCiAgICAgICAgIiZ0PSIgKwogICAgICAgIG5ldyBEYXRlKCkuZ2V0VGltZSgpOwogICAgfSwKICAgIC8v5bel5L2c5rWB5Zue5Lyg5pWw5o2uCiAgICBhc3luYyB0b2RvUmVzdWx0KGRhdGEpIHsKICAgICAgbGV0IHJvdyA9IHsKICAgICAgICBvYmpJZDogZGF0YS5idXNpbmVzc0tleSwKICAgICAgICBpc1N0YXJ0OiAxLAogICAgICAgIGlzQmFjazogZGF0YS5wcm9jZXNzVHlwZSA9PT0gInJvbGxiYWNrIiA/IDEgOiAwCiAgICAgIH07CiAgICAgIGlmIChkYXRhLnByb2Nlc3NUeXBlID09PSAicm9sbGJhY2siKSB7CiAgICAgICAgc3dpdGNoIChkYXRhLmFjdGl2ZVRhc2tOYW1lKSB7CiAgICAgICAgICBjYXNlICLmk43kvZznpajloavmiqUiOgogICAgICAgICAgICByb3cuc3RhdHVzID0gIjAiOwogICAgICAgICAgICBicmVhazsKICAgICAgICAgIGNhc2UgIuWIhuWFrOWPuOWuoeaguCI6CiAgICAgICAgICAgIHJvdy5zdGF0dXMgPSAiMiI7CiAgICAgICAgICAgIGJyZWFrOwogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICBzd2l0Y2ggKGRhdGEuYWN0aXZlVGFza05hbWUpIHsKICAgICAgICAgIGNhc2UgIuWIhuWFrOWPuOWuoeaguCI6CiAgICAgICAgICAgIHJvdy5zdGF0dXMgPSAiMiI7CiAgICAgICAgICAgIHJvdy5mZ3NzcHIgPSBkYXRhLm5leHRVc2VyOwogICAgICAgICAgICBicmVhazsKICAgICAgICAgIGNhc2UgIuaTjeS9nOelqOWKnue7kyI6CiAgICAgICAgICAgIHJvdy5zdGF0dXMgPSAiMyI7CiAgICAgICAgICAgIHJvdy5ianIgPSBkYXRhLm5leHRVc2VyOwogICAgICAgICAgICBicmVhazsKICAgICAgICAgIGNhc2UgIue7k+adnyI6CiAgICAgICAgICAgIHJvdy5zdGF0dXMgPSAiNCI7CiAgICAgICAgICAgIGJyZWFrOwogICAgICAgIH0KICAgICAgfQogICAgICBsZXQgeyBjb2RlIH0gPSBhd2FpdCB1cGRhdGVCeUlkKHJvdyk7CiAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICBhd2FpdCB0aGlzLmdldERhdGEoKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuaTjeS9nOWksei0pSIpOwogICAgICB9CiAgICB9LAogICAgLy/kuIrmiqXlj5HpgIHlip7nu5MKICAgIGFzeW5jIGdldFNiRnNCaih0eXBlKSB7CiAgICAgIGxldCByb3cgPSB7IC4uLnRoaXMuZm9ybSB9OwogICAgICBpZiAodHlwZSA9PT0gImNvbXBsZXRlIikgewogICAgICAgIHN3aXRjaCAocm93LnN0YXR1cykgewogICAgICAgICAgY2FzZSAiMCI6CiAgICAgICAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IGZhbHNlOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnZhcmlhYmxlcy5wYXNzID0gdHJ1ZTsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5idXNpbmVzc0tleSA9IHJvdy5vYmpJZDsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5wcm9jZXNzVHlwZSA9IHR5cGU7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuZHcgPSByb3cuZmdzOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnJ5bHggPSAi5YiG5YWs5Y+45a6h5qC45Lq6IjsKICAgICAgICAgICAgdGhpcy5hY3Rpdml0aU9wdGlvbi50aXRsZSA9ICLmj5DkuqQiOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmRlZmF1bHRGcm9tID0gdHJ1ZTsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5wZXJzb25Hcm91cElkID0gMTQ7CiAgICAgICAgICAgIHRoaXMuaXNTaG93ID0gdHJ1ZTsKICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICBjYXNlICIyIjoKICAgICAgICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gZmFsc2U7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnBhc3MgPSB0cnVlOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmJ1c2luZXNzS2V5ID0gcm93Lm9iaklkOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnByb2Nlc3NUeXBlID0gdHlwZTsKICAgICAgICAgICAgdGhpcy5hY3Rpdml0aU9wdGlvbi50aXRsZSA9ICLmj5DkuqQiOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmRlZmF1bHRGcm9tID0gdHJ1ZTsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5wZXJzb25Hcm91cElkID0gMTU7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuZHcgPSByb3cuZmdzOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnJ5bHggPSAi5Yqe57uT5Lq6IjsKICAgICAgICAgICAgdGhpcy5pc1Nob3cgPSB0cnVlOwogICAgICAgICAgICBicmVhazsKICAgICAgICAgIGNhc2UgIjMiOgogICAgICAgICAgICB0aGlzLmZvcm0uY29sRmlyc3QgPSB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3Q7CiAgICAgICAgICAgIHRoaXMuZm9ybS5vYmpJZExpc3QgPSB0aGlzLmlkczsKICAgICAgICAgICAgLy90aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3Qg6aqM6K+B5bey5omT6ZKp55qE5pWw5o2u5Lit5LiN6IO95pyJ56m65YaF5a65CiAgICAgICAgICAgIGxldCB0YWJsZVZhbGlkID0gdGhpcy5wcm9wVGFibGVEYXRhLmNvbEZpcnN0LnNvbWUoCiAgICAgICAgICAgICAgaXRlbSA9PiBpdGVtLnNmd2MgJiYgIShpdGVtLnhsciAmJiBpdGVtLnhsc2ogJiYgaXRlbS5obHNqKQogICAgICAgICAgICApOwogICAgICAgICAgICBhd2FpdCB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUoYXN5bmMgdmFsaWQgPT4gewogICAgICAgICAgICAgIGlmICh2YWxpZCAmJiAhdGFibGVWYWxpZCkgewogICAgICAgICAgICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gZmFsc2U7CiAgICAgICAgICAgICAgICB0aGlzLnVwbG9hZEltZ0RhdGEuYnVzaW5lc3NJZCA9IHRoaXMuZm9ybS5vYmpJZDsKICAgICAgICAgICAgICAgIHRoaXMudXBsb2FkRm9ybSgpOwogICAgICAgICAgICAgICAgYXdhaXQgc2F2ZU9yVXBkYXRlKHRoaXMuZm9ybSkudGhlbihyZXMgPT4gewogICAgICAgICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnBhc3MgPSB0cnVlOwogICAgICAgICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuYnVzaW5lc3NLZXkgPSByb3cub2JqSWQ7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5wcm9jZXNzVHlwZSA9IHR5cGU7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5hY3Rpdml0aU9wdGlvbi50aXRsZSA9ICLlip7nu5MiOwogICAgICAgICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuZGVmYXVsdEZyb20gPSBmYWxzZTsKICAgICAgICAgICAgICAgICAgICB0aGlzLmlzU2hvdyA9IHRydWU7CiAgICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5aSx6LSlIik7CiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLor7flsIblrozmiJDnmoTmk43kvZzpobnnm67loavlhpnlrozmlbQiKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSBmYWxzZTsKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmJ1c2luZXNzS2V5ID0gcm93Lm9iaklkOwogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEucHJvY2Vzc1R5cGUgPSB0eXBlOwogICAgICAgIHRoaXMuYWN0aXZpdGlPcHRpb24udGl0bGUgPSAi5Zue6YCA5Y6f5Zug5aGr5YaZIjsKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmRlZmF1bHRGcm9tID0gdHJ1ZTsKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnZhcmlhYmxlcy5wYXNzID0gZmFsc2U7CiAgICAgICAgdGhpcy5pc1Nob3cgPSB0cnVlOwogICAgICB9CiAgICB9LAogICAgLy8g6aKE6KeI5oyJ6ZKuCiAgICBoYW5kbGVZbENoYW5nZSgpIHsKICAgICAgdGhpcy50aXRsZXlsID0gIuafpeeci+aTjeS9nOmhueebriI7CiAgICAgIHRoaXMueWwgPSB0cnVlOwogICAgfSwKICAgIC8v57q/6Lev6YCJ5oup5o6l5pS25pWw5o2uCiAgICBoYW5kbGVBY2NlcHRTYkRhdGEoc2JEYXRhKSB7CiAgICAgIHRoaXMuZm9ybS54bG1jID0gc2JEYXRhLmxhYmVsOwogICAgfSwKICAgIC8vIOWFqOmAieaMiemSrgogICAgaGFuZGxlQ2hlY2tBbGxDaGFuZ2UodmFsKSB7CiAgICAgIGlmICh2YWwpIHsKICAgICAgICB0aGlzLmZvcm0ueXp4Y3p4cyA9IHRoaXMuZm9ybS5jenhzOwogICAgICAgIHRoaXMuZm9ybS53enhjenhzID0gMDsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmZvcm0ud3p4Y3p4cyA9IHRoaXMuZm9ybS5jenhzOwogICAgICAgIHRoaXMuZm9ybS55enhjenhzID0gMDsKICAgICAgfQogICAgICB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICBpdGVtLnNmd2MgPSB2YWw7CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZUNoZWNrQ2hhbmdlKHZhbCkgewogICAgICBpZiAodmFsKSB7CiAgICAgICAgKyt0aGlzLmZvcm0ueXp4Y3p4czsKICAgICAgfSBlbHNlIHsKICAgICAgICAtLXRoaXMuZm9ybS55enhjenhzOwogICAgICB9CiAgICAgIHRoaXMuZm9ybS53enhjenhzID0gdGhpcy5mb3JtLmN6eHMgLSB0aGlzLmZvcm0ueXp4Y3p4czsKICAgIH0sCiAgICBmaWx0ZXJSZXNldCgpIHsKICAgICAgdGhpcy5maWx0ZXJJbmZvLmZpZWxkTGlzdC5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgIGlmIChpdGVtLnR5cGUgPT09ICJjaGVja2JveCIpIHsKICAgICAgICAgIGl0ZW0uY2hlY2tib3hWYWx1ZSA9IFtdOwogICAgICAgIH0KICAgICAgfSk7CiAgICAgIHRoaXMucGFyYW1zID0gewogICAgICAgIC8v6L6T55S1CiAgICAgICAgbHg6IDEsCiAgICAgICAgc3RhdHVzOiAiIgogICAgICB9OwogICAgfSwKICAgIC8v5YiX6KGo5p+l6K+iCiAgICBhc3luYyBnZXREYXRhKHBhcmFtcykgewogICAgICB0cnkgewogICAgICAgIHRoaXMucGFyYW1zID0geyAuLi50aGlzLnBhcmFtcywgLi4ucGFyYW1zIH07CiAgICAgICAgY29uc3QgcGFyYW0gPSB0aGlzLnBhcmFtczsKICAgICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICAgIHBhcmFtLm15U29ydHMgPSBbeyBwcm9wOiAidXBkYXRlVGltZSIsIGFzYzogZmFsc2UgfV07CiAgICAgICAgaWYgKCFwYXJhbS5zdGF0dXMpIHsKICAgICAgICAgIHBhcmFtLnN0YXR1cyA9ICIwLDEsMiwzLDQiOwogICAgICAgIH0KICAgICAgICBjb25zdCB7IGRhdGEsIGNvZGUgfSA9IGF3YWl0IGdldExpc3QocGFyYW0pOwogICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIGZvciAobGV0IGkgb2YgZGF0YS5yZWNvcmRzKSB7CiAgICAgICAgICAgIHRoaXMuc3RhdHVzT3B0aW9ucy5mb3JFYWNoKGVsZW1lbnQgPT4gewogICAgICAgICAgICAgIGlmIChpLnN0YXR1cyA9PT0gZWxlbWVudC52YWx1ZSkgewogICAgICAgICAgICAgICAgaS5zdGF0dXNDbiA9IGVsZW1lbnQubGFiZWw7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby50YWJsZURhdGEgPSBkYXRhLnJlY29yZHM7CiAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIudG90YWwgPSBkYXRhLnRvdGFsOwogICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgY29uc29sZS5sb2coZSk7CiAgICAgIH0KICAgIH0sCgogICAgLy/kv67mlLnmjInpkq4KICAgIGdldFVwZGF0ZShyb3cpIHsKICAgICAgdGhpcy5nZXRDenBteChyb3cpOwogICAgICB0aGlzLnRpdGxlID0gIui+k+eUteaTjeS9nOelqOS/ruaUuSI7CiAgICAgIHRoaXMuZm9ybSA9IHsgLi4ucm93IH07CiAgICAgIHRoaXMuaW1nTGlzdCA9IHRoaXMuZm9ybS5pbWdMaXN0OwogICAgICB0aGlzLmlzRGlzYWJsZWRCaiA9IGZhbHNlOwogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlOwogICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZTsKICAgICAgdGhpcy5nZXRTaG93KCk7CiAgICB9LAogICAgLy/or6bmg4XmjInpkq4KICAgIGdldERldGFpbHMocm93KSB7CiAgICAgIHRoaXMuZ2V0Q3pwbXgocm93KTsKICAgICAgdGhpcy50aXRsZSA9ICLovpPnlLXmk43kvZznpajor6bmg4Xmn6XnnIsiOwogICAgICB0aGlzLmZvcm0gPSB7IC4uLnJvdyB9OwogICAgICAvL+WbvueJh+e7k+WQiAogICAgICB0aGlzLmltZ0xpc3QgPSB0aGlzLmZvcm0uaW1nTGlzdDsKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gdHJ1ZTsKICAgICAgdGhpcy5pc0Rpc2FibGVkQmogPSB0cnVlOwogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlOwogICAgICB0aGlzLmdldFNob3coKTsKICAgIH0sCgogICAgLy8g5paw5aKe5L+u5pS5IOehruiupOaMiemSrgogICAgYXN5bmMgc2F2ZVJvdygpIHsKICAgICAgdHJ5IHsKICAgICAgICB0aGlzLmZvcm0uY29sRmlyc3QgPSB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3Q7CiAgICAgICAgdGhpcy5mb3JtLm9iaklkTGlzdCA9IHRoaXMuaWRzOwogICAgICAgIGxldCB7IGNvZGUgfSA9IGF3YWl0IHNhdmVPclVwZGF0ZXModGhpcy5mb3JtKTsKICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKnyIpOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgIGNvbnNvbGUubG9nKGUpOwogICAgICB9CiAgICAgIC8v6YeN572ucGFnZemhteS7jjHlvIDlp4sKICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnBhZ2VSZXNpemUgPSAiWSI7CiAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSBmYWxzZTsKICAgIH0sCgogICAgLy/liKDpmaTmjInpkq4KICAgIGFzeW5jIGRlbGV0ZVJvdyhyb3cpIHsKICAgICAgbGV0IG9iaklkID0gIiI7CiAgICAgIGlmIChyb3cub2JqSWQpIHsKICAgICAgICBvYmpJZCA9IHJvdy5vYmpJZDsKICAgICAgfSBlbHNlIHsKICAgICAgICBvYmpJZCA9IHRoaXMuaWRzWzBdOwogICAgICB9CiAgICAgIHRoaXMuJGNvbmZpcm0oIuatpOaTjeS9nOWwhuawuOS5heWIoOmZpOivpeaVsOaNriwg5piv5ZCm57un57utPyIsICLmj5DnpLoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KQogICAgICAgIC50aGVuKCgpID0+IHsKICAgICAgICAgIHJlbW92ZShvYmpJZCkudGhlbigoeyBjb2RlIH0pID0+IHsKICAgICAgICAgICAgaWYgKGNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLAogICAgICAgICAgICAgICAgbWVzc2FnZTogIuWIoOmZpOaIkOWKnyEiCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgLy/ph43nva5wYWdl6aG15LuOMeW8gOWniwogICAgICAgICAgICAgIC8vIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci5wYWdlUmVzaXplID0gIlkiOwogICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTlpLHotKUhIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoKSA9PiB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogImluZm8iLAogICAgICAgICAgICBtZXNzYWdlOiAi5bey5Y+W5raI5Yig6ZmkIgogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICB9LAogICAgLy/ooajmoLzmlrDlop4KICAgIGxpc3RGaXJzdEFkZCgpIHsKICAgICAgbGV0IHJvdyA9IHsKICAgICAgICBjenJ3OiAiIiwKICAgICAgICB4bHI6ICIiLAogICAgICAgIHhsc2o6ICIiLAogICAgICAgIGhsc2o6ICIiLAogICAgICAgIHNmd2M6ICIiCiAgICAgIH07CiAgICAgIHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdC5wdXNoKHJvdyk7CiAgICAgIHRoaXMucHJvcFRhYmxlRGF0YS5zZWwgPSByb3c7CiAgICAgIHRoaXMuZm9ybS5jenhzID0gdGhpcy5wcm9wVGFibGVEYXRhLmNvbEZpcnN0Lmxlbmd0aDsKICAgICAgdGhpcy5mb3JtLnd6eGN6eHMgPSB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QubGVuZ3RoOwogICAgfSwKICAgIC8v6KGo5qC85Yig6ZmkCiAgICBsaXN0Rmlyc3REZWwoaW5kZXgsIHJvdykgewogICAgICB0aGlzLmlkcy5wdXNoKHJvdy5vYmpJZCk7CiAgICAgIHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdC5zcGxpY2UoaW5kZXgsIDEpOwogICAgICB0aGlzLmZvcm0uY3p4cyA9IHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdC5sZW5ndGg7CiAgICAgIHRoaXMuZm9ybS53enhjenhzID0gdGhpcy5wcm9wVGFibGVEYXRhLmNvbEZpcnN0Lmxlbmd0aDsKICAgIH0sCiAgICAvL+mAieaLqeW3suaJp+ihjOaXtu+8jOaTjeS9nOmhueebrum7mOiupOm7mOiupOWFqOmAiQogICAgaGFuZGxlQ2hhbmdlT2ZTZnp4KHZhbCkgewogICAgICBpZiAodmFsID09PSAi5bey5omn6KGMIikgewogICAgICAgIHRoaXMuZm9ybS55enhjenhzID0gdGhpcy5mb3JtLmN6eHM7CiAgICAgICAgdGhpcy5mb3JtLnd6eGN6eHMgPSAwOwogICAgICAgIHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdC5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgaXRlbS5zZndjID0gdHJ1ZTsKICAgICAgICB9KTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmZvcm0ud3p4Y3p4cyA9IHRoaXMuZm9ybS5jenhzOwogICAgICAgIHRoaXMuZm9ybS55enhjenhzID0gMDsKICAgICAgICB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIGl0ZW0uc2Z3YyA9IGZhbHNlOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgLy/lhbPpl63lvLnnqpcKICAgIGNsb3NlKCkgewogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSBmYWxzZTsKICAgIH0sCiAgICAvL+mAieaLqeihjAogICAgc2VsZWN0Q2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLm9iaklkKTsKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOwogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7CiAgICAgIHRoaXMuc2VsZWN0RGF0YSA9IHNlbGVjdGlvbjsKICAgIH0sCiAgICAvKioKICAgICAq6I635Y+W5pON5L2c56Wo5piO57uGCiAgICAgKi8KICAgIGFzeW5jIGdldEN6cG14KHJvdykgewogICAgICB0cnkgewogICAgICAgIGNvbnN0IHsgZGF0YSwgY29kZSB9ID0gYXdhaXQgZ2V0Q3pwbXhMaXN0KHsgb2JqSWQ6IHJvdy5vYmpJZCB9KTsKICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QgPSBkYXRhOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgIGNvbnNvbGUubG9nKGUpOwogICAgICB9CiAgICB9LAogICAgLy/lhbPpl63nur/ot6/lvLnnqpcKICAgIGhhbmRsZUNvbnRyb2xTeXNiU2VsZWN0RGlhbG9nKGlzU2hvdykgewogICAgICB0aGlzLmlzU2hvd1hsRGlhbG9nID0gaXNTaG93OwogICAgfSwKICAgIC8v57q/6Lev6YCJ5oup5LqL5Lu2CiAgICBzeXNiU2VsZWN0ZWRDbGljaygpIHsKICAgICAgdGhpcy5pc1Nob3dYbERpYWxvZyA9IHRydWU7CiAgICB9LAogICAgLy/lr7zlh7p3b3JkCiAgICBhc3luYyBleHBvcnRXb3JkKHJvdykgewogICAgICB0cnkgewogICAgICAgIGxldCBleHBvcnREYXRhID0geyAuLi5yb3cgfTsKICAgICAgICBhd2FpdCBleHBvcnRXb3JkKGV4cG9ydERhdGEsICJ4bGR6Y3pwIiwgIueUteWKm+ezu+e7n+e6v+i3r+WAkumXuOaTjeS9nOelqC5kb2N4Iik7CiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLlr7zlh7rlpLHotKXvvIEiKTsKICAgICAgfQogICAgfSwKICAgIC8v5a+85Ye6UGRmCiAgICBhc3luYyBleHBvcnRQZGYocm93KSB7CiAgICAgIHRyeSB7CiAgICAgICAgbGV0IGV4cG9ydERhdGEgPSB7IC4uLnJvdyB9OwogICAgICAgIGF3YWl0IGV4cG9ydFBkZihleHBvcnREYXRhLCAieGxkemN6cCIsICLnlLXlipvns7vnu5/nur/ot6/lgJLpl7jmk43kvZznpagucGRmIik7CiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLlr7zlh7rlpLHotKXvvIEiKTsKICAgICAgfQogICAgfSwKICAgIC8v6aKE6KeI5paH5Lu2CiAgICBhc3luYyBwcmV2aWV3RmlsZShyb3cpIHsKICAgICAgdHJ5IHsKICAgICAgICBsZXQgZXhwb3J0RGF0YSA9IHsgLi4ucm93IH07CiAgICAgICAgYXdhaXQgcHJldmlld0ZpbGUoZXhwb3J0RGF0YSwgInhsZHpjenAiKTsKICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIumihOiniOWksei0pe+8gSIpOwogICAgICB9CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["dzczp.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwnBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "dzczp.vue", "sourceRoot": "src/views/dagangOilfield/czpgl/xldzcz", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n      @handleReset=\"filterReset\"\n    />\n    <div>\n      <el-white class=\"button-group\">\n        <div class=\"button_btn\">\n          <el-button\n            type=\"danger\"\n            icon=\"el-icon-delete\"\n            v-if=\"hasSuperRole\"\n            @click=\"deleteRow\"\n            :disabled=\"single\"\n            >删除</el-button\n          >\n        </div>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"selectChange\"\n          height=\"69vh\"\n          v-loading=\"loading\"\n        >\n          <!-- <el-table-column\n            prop=\"statusCn\"\n            slot=\"table_six\"\n            align=\"center\"\n            style=\"display: block\"\n            label=\"流程状态\"\n            min-width=\"120\"\n            :resizable=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-badge\n                v-if=\"scope.row.isBack === 1\"\n                value=\"退回\"\n                class=\"item\"\n                type=\"danger\"\n              >\n              </el-badge>\n              <span>{{ scope.row.statusCn }}</span>\n            </template>\n          </el-table-column> -->\n          <el-table-column\n            slot=\"table_eight\"\n            align=\"center\"\n            fixed=\"right\"\n            style=\"display: block\"\n            label=\"操作\"\n            width=\"160\"\n            :resizable=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                @click=\"getDetails(scope.row)\"\n                type=\"text\"\n                size=\"small\"\n                title=\"详情\"\n                class=\"el-icon-view\"\n              />\n              <el-button\n                @click=\"getUpdate(scope.row)\"\n                class=\"el-icon-edit\"\n                title=\"编辑\"\n                v-if=\"\n                  (scope.row.status === '0' && scope.row.createBy === currentUser) || hasSuperRole\n                \"\n                type=\"text\"\n                size=\"small\"\n              />\n              <el-button\n                @click=\"deleteRow(scope.row)\"\n                class=\"el-icon-delete\"\n                title=\"删除\"\n                v-if=\"\n                  scope.row.status === '0' && scope.row.createBy === currentUser\n                \"\n                type=\"text\"\n                size=\"small\"\n              />\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                @click=\"nullifyGzp(scope.row.objId)\"\n                v-if=\"scope.row.status < 4 && scope.row.status > 0 &&\n                ( scope.row.createBy === currentUser || (scope.row.bzspr && scope.row.bzspr === currentUser) \n                || (scope.row.fgsspr && scope.row.fgsspr === currentUser) \n                || (scope.row.bjr && scope.row.bjr === currentUser)\n                || hasSuperRole )\n                \"\n                title=\"作废\"\n                class=\"el-icon-close\"\n              >\n              </el-button>\n              <el-button\n                @click=\"showTimeLine(scope.row)\"\n                v-if=\"scope.row.isStart === 1\"\n                type=\"text\"\n                size=\"small\"\n                class=\"el-icon-lcck commonIcon\"\n                title=\"流程查看\"\n              />\n              <el-button\n                @click=\"showProcessImg(scope.row)\"\n                v-if=\"scope.row.isStart === 1\"\n                type=\"text\"\n                size=\"small\"\n                class=\"el-icon-lct commonIcon\"\n                title=\"流程图\"\n              />\n              <!--              <el-button @click=\"exportWord(scope.row)\" v-if=\"scope.row.status == '待办结'\" type=\"text\" size=\"small\">\n                              导出Word\n                            </el-button>-->\n              <el-button\n                @click=\"previewFile(scope.row)\"\n                v-if=\"scope.row.status > 2\"\n                type=\"text\"\n                size=\"small\"\n                class=\"el-icon-zoom-in\"\n                title=\"预览\"\n              />\n              <el-button\n                @click=\"exportPdf(scope.row)\"\n                v-if=\"scope.row.status > 2\"\n                type=\"text\"\n                size=\"small\"\n                class=\"el-icon-pdf-export commonIcon\"\n                title=\"导出pdf\"\n              />\n            </template>\n          </el-table-column>\n        </comp-table>\n      </el-white>\n    </div>\n\n    <!-- 详情/新增/修改 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      v-if=\"isShowDetails\"\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <div>\n          <el-row>\n            <el-col :span=\"24\">\n              <el-form-item label=\"工作名称\" prop=\"gzmc\">\n                <el-input\n                  type=\"textarea\"\n                  :rows=\"2\"\n                  v-model=\"form.gzmc\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请输入工作名称\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <el-col :span=\"8\">\n              <el-form-item label=\"编号\" prop=\"bm\">\n                <el-input v-model=\"form.bm\" disabled placeholder=\"请输入\" />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"线路名称\" prop=\"xlmc\">\n                <el-input\n                  v-model=\"form.xlmc\"\n                  disabled\n                  placeholder=\"请输入线路名称\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"操作人\"\n                prop=\"czr\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-input\n                  v-model=\"form.czr\"\n                  :disabled=\"isDisabledBj\"\n                  placeholder=\"请输入操作人\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"监护人\"\n                prop=\"jhr\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-input\n                  v-model=\"form.jhr\"\n                  :disabled=\"isDisabledBj\"\n                  placeholder=\"请输入监护人\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"日期\" prop=\"rq\">\n                <el-date-picker\n                  v-model=\"form.rq\"\n                  :disabled=\"isDisabledBj\"\n                  type=\"date\"\n                  placeholder=\"选择日期\"\n                  format=\"yyyy-MM-dd\"\n                  value-format=\"yyyy-MM-dd\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作项数：\" prop=\"czxs\">\n                <el-input-number\n                  v-model=\"form.czxs\"\n                  disabled\n                  placeholder=\"请输入操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"已执行项数：\" prop=\"yzxczxs\">\n                <el-input-number\n                  v-model=\"form.yzxczxs\"\n                  disabled\n                  placeholder=\"请输入已执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"未执行项数：\" prop=\"wzxczxs\">\n                <el-input-number\n                  v-model=\"form.wzxczxs\"\n                  disabled\n                  placeholder=\"请输入未执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <el-col :span=\"8\">\n              <el-form-item label=\"是否已执行：\" prop=\"sfyzx\">\n                <el-select\n                  v-model=\"form.sfyzx\"\n                  placeholder=\"请选择\"\n                  :disabled=\"isDisabledBj\"\n                  @change=\"handleChangeOfSfzx\"\n                >\n                  <el-option\n                    v-for=\"item in sfyzxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item\n                label=\"未执行原因：\"\n                prop=\"wzxyy\"\n                v-if=\"form.sfyzx === '未执行'\"\n              >\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"form.wzxyy\"\n                  :disabled=\"isDisabledBj\"\n                  placeholder=\"请输入内容\"\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n\n          <el-row>\n            <el-col :span=\"24\">\n              <el-form-item label=\"上传图片：\">\n                <el-upload\n                  :disabled=\"isDisabledBj\"\n                  action=\"\"\n                  ref=\"uploadImg\"\n                  accept=\"image/jpeg,image/jpg,image/png\"\n                  :headers=\"header\"\n                  :multiple=\"true\"\n                  :on-change=\"handleChange\"\n                  :data=\"uploadImgData\"\n                  :file-list=\"imgList\"\n                  :auto-upload=\"false\"\n                  list-type=\"picture-card\"\n                  :on-preview=\"handlePictureCardPreview\"\n                  :on-progress=\"handleProgress\"\n                  :on-remove=\"handleRemove\"\n                >\n                  <i class=\"el-icon-plus\"></i>\n                </el-upload>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n        <!--预览信息-->\n        <div>\n          <el-dialog\n            :title=\"titleyl\"\n            :visible.sync=\"yl\"\n            append-to-body\n            width=\"60%\"\n          >\n            <el-table\n              :data=\"propTableData.colFirst\"\n              border\n              stripe\n              style=\"width: 100%\"\n              :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n            >\n              <el-table-column\n                align=\"center\"\n                prop=\"xh\"\n                width=\"100px\"\n                label=\"序号\"\n                sortable\n              >\n                <template slot-scope=\"scope\">\n                  <el-input\n                    placeholder=\"请输入序号\"\n                    v-model=\"scope.row.xh\"\n                    disabled\n                  ></el-input>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n                <template slot-scope=\"scope\">\n                  <el-input\n                    type=\"textarea\"\n                    :autosize=\"{ minRows: 1, maxRows: 4 }\"\n                    placeholder=\"请输入操作项目\"\n                    v-model=\"scope.row.czrw\"\n                    disabled\n                  ></el-input>\n                </template>\n              </el-table-column>\n\n              <el-table-column\n                align=\"center\"\n                prop=\"xlr\"\n                label=\"下令人\"\n                width=\"100\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input\n                    placeholder=\"请输入内容\"\n                    v-model=\"scope.row.xlr\"\n                    disabled\n                  ></el-input>\n                </template>\n              </el-table-column>\n\n              <el-table-column\n                align=\"center\"\n                prop=\"xlsj\"\n                label=\"下令时间\"\n                width=\"204\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-date-picker\n                    v-model=\"scope.row.xlsj\"\n                    disabled\n                    type=\"datetime\"\n                    placeholder=\"选择下令时间\"\n                    format=\"HH:mm\"\n                    value-format=\"yyyy-MM-dd HH:mm\"\n                    style=\"width: 100%\"\n                  >\n                  </el-date-picker>\n                </template>\n              </el-table-column>\n\n              <el-table-column\n                align=\"center\"\n                prop=\"hlsj\"\n                label=\"回令时间\"\n                width=\"204\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-date-picker\n                    v-model=\"scope.row.hlsj\"\n                    disabled\n                    type=\"datetime\"\n                    placeholder=\"选择回令时间\"\n                    format=\"HH:mm\"\n                    value-format=\"yyyy-MM-dd HH:mm\"\n                    style=\"width: 100%\"\n                  >\n                  </el-date-picker>\n                </template>\n              </el-table-column>\n              <el-table-column\n                type=\"sfwc\"\n                width=\"50\"\n                label=\"是否完成\"\n                align=\"center\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-checkbox v-model=\"scope.row.sfwc\" disabled></el-checkbox>\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-dialog>\n        </div>\n        <!--列表-->\n        <div>\n          <div align=\"left\">\n            <el-button type=\"info\" @click=\"handleYlChange\">预览</el-button>\n          </div>\n          <div align=\"right\">\n            <el-checkbox @change=\"handleCheckAllChange\" :disabled=\"isDisabledBj\"\n              >全选</el-checkbox\n            >\n          </div>\n          <!--第一个表格-->\n          <el-table\n            :data=\"propTableData.colFirst\"\n            height=\"200\"\n            border\n            stripe\n            style=\"width: 100%\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <el-table-column\n              align=\"center\"\n              prop=\"xh\"\n              width=\"100px\"\n              label=\"序号\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <el-input-number\n                  size=\"small\"\n                  v-model=\"scope.row.xh\"\n                  :min=\"1\"\n                  :precision=\"0\"\n                  controls-position=\"right\"\n                  :disabled=\"isDisabled\"\n                ></el-input-number>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n              <template slot-scope=\"scope\">\n                <el-input\n                  placeholder=\"请输入操作项目\"\n                  v-model=\"scope.row.czrw\"\n                  :disabled=\"isDisabled\"\n                ></el-input>\n              </template>\n            </el-table-column>\n\n            <el-table-column\n              align=\"center\"\n              prop=\"xlr\"\n              label=\"下令人\"\n              width=\"100\"\n            >\n              <template slot-scope=\"scope\">\n                <el-input\n                  placeholder=\"请输入内容\"\n                  v-model=\"scope.row.xlr\"\n                  :disabled=\"isDisabledBj\"\n                ></el-input>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"xlsj\"\n              label=\"下令时间\"\n              width=\"204\"\n            >\n              <template slot-scope=\"scope\">\n                <el-date-picker\n                  v-model=\"scope.row.xlsj\"\n                  :disabled=\"isDisabledBj\"\n                  type=\"datetime\"\n                  placeholder=\"选择下令时间\"\n                  format=\"HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"hlsj\"\n              label=\"回令时间\"\n              width=\"204\"\n            >\n              <template slot-scope=\"scope\">\n                <el-date-picker\n                  v-model=\"scope.row.hlsj\"\n                  :disabled=\"isDisabledBj\"\n                  type=\"datetime\"\n                  placeholder=\"选择回令时间\"\n                  format=\"HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </template>\n            </el-table-column>\n            <el-table-column\n              type=\"sfwc\"\n              width=\"50\"\n              label=\"是否完成\"\n              align=\"center\"\n            >\n              <template slot-scope=\"scope\">\n                <el-checkbox\n                  v-model=\"scope.row.sfwc\"\n                  :disabled=\"isDisabledBj\"\n                  @change=\"handleCheckChange\"\n                ></el-checkbox>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" width=\"80\">\n              <template slot=\"header\" slot-scope=\"scope\">\n                <el-button\n                  size=\"small\"\n                  type=\"primary\"\n                  icon=\"el-icon-plus\"\n                  :disabled=\"isDisabled\"\n                  @click=\"listFirstAdd(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n              <template slot-scope=\"scope\">\n                <el-button\n                  type=\"text\"\n                  icon=\"el-icon-delete\"\n                  :disabled=\"isDisabled\"\n                  @click=\"listFirstDel(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button\n          v-if=\"isDisabled && buttonNameShow && form.status > 0\"\n          type=\"info\"\n          @click=\"getSbFsBj('rollback')\"\n          >退 回\n        </el-button>\n        <el-button\n          v-if=\"isDisabled && buttonNameShow\"\n          type=\"success\"\n          @click=\"getSbFsBj('complete')\"\n          >{{ buttonName }}\n        </el-button>\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"saveRow\"\n          >保 存</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <!--线路选择组件-->\n    <el-dialog\n      title=\"线路选择\"\n      :visible.sync=\"isShowXlDialog\"\n      width=\"20%\"\n      v-if=\"isShowXlDialog\"\n      v-dialogDrag\n    >\n      <sdxl-selected\n        @closeSysbSelectDialog=\"handleControlSysbSelectDialog\"\n        @handleAcceptSbData=\"handleAcceptSbData\"\n      ></sdxl-selected>\n    </el-dialog>\n\n    <!--  工作流  -->\n    <activiti\n      ref=\"activiti\"\n      :processData=\"processData\"\n      :isShow=\"isShow\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n    ></activiti>\n    <time-line\n      :value=\"timeLineShow\"\n      :timeData=\"timeData\"\n      @closeTimeLine=\"colseTimeLine\"\n    />\n    <!--流程图查看-->\n    <el-dialog\n      title=\"审批流程图\"\n      :visible.sync=\"openLoadingImg\"\n      width=\"56%\"\n      append-to-body\n      :close-on-click-modal=\"false\"\n    >\n      <div>\n        <img :src=\"imgSrc\" style=\"width: 100%\" />\n      </div>\n    </el-dialog>\n\n    <!--图片放大展示弹出框-->\n    <el-image-viewer\n      v-if=\"imgDialogVisible\"\n      :on-close=\"onClose\"\n      :url-list=\"dialogImageUrl\"\n      zIndex=\"3000\"\n    />\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport {\n  getCzpmxList,\n  getList,\n  remove,\n  saveOrUpdates\n} from \"@/api/yxgl/sdyxgl/sddzczp\";\nimport sdxlSelected from \"@/views/dagangOilfield/xjgl/sdxj/sdxlSelected\";\nimport {\n  exportPdf,\n  exportWord,\n  previewFile,\n  saveOrUpdate,\n  updateById\n} from \"@/api/yxgl/bdyxgl/bddzczp\";\nimport api from \"@/utils/request\";\n//流程\nimport activiti from \"com/activiti_czp\";\nimport timeLine from \"com/timeLine\";\nimport { HistoryList } from \"@/api/activiti/processTask\";\nimport ElImageViewer from \"element-ui/packages/image/src/image-viewer\";\nexport default {\n  name: \"dzczp\",\n  components: { sdxlSelected, activiti, timeLine, ElImageViewer },\n  data() {\n    return {\n      hasSuperRole: this.$store.getters.hasSuperRole,\n      loading: false,\n      isDisabledBj: true,\n      buttonNameShow: false,\n      buttonName: \"\",\n      rules: {\n        kssj: [\n          { required: true, message: \"操作开始时间不能为空\", trigger: \"blur\" }\n        ],\n        jssj: [\n          { required: true, message: \"操作结束时间不能为空\", trigger: \"change\" }\n        ],\n        fgs: [{ required: true, message: \"分公司不能为空\", trigger: \"select\" }],\n        bdzmc: [\n          { required: true, message: \"变电站不能为空\", trigger: \"select\" }\n        ],\n        czrw: [\n          { required: true, message: \"操作任务不能为空\", trigger: \"blur\" }\n        ],\n        // czr: [{ required: true, message: \"操作人不能为空\", trigger: \"blur\" }],\n        // jhr: [{ required: true, message: \"监护人不能为空\", trigger: \"blur\" }],\n        xlr: [{ required: true, message: \"下令人不能为空\", trigger: \"blur\" }],\n        // spr: [\n        //   {required: true, message: '审票人不能为空', trigger: 'blur'}\n        // ],\n        czxs: [\n          { required: true, message: \"操作项数不能为空\", trigger: \"blur\" }\n        ],\n        yzxczxs: [\n          { required: true, message: \"已执行项数不能为空\", trigger: \"blur\" }\n        ],\n        wzxczxs: [\n          { required: true, message: \"未执行项数不能为空\", trigger: \"blur\" }\n        ],\n        sfyzx: [\n          { required: true, message: \"是否已执行不能为空\", trigger: \"select\" }\n        ]\n      },\n      //状态下拉框数据\n      statusOptions: [\n        {\n          value: \"0\",\n          label: \"操作票填报\"\n        },\n        // {\n        //   value: \"1\",\n        //   label: \"班组审核\"\n        // },\n        {\n          value: \"2\",\n          label: \"分公司审核\"\n        },\n        {\n          value: \"3\",\n          label: \"操作票办结\"\n        },\n        {\n          value: \"4\",\n          label: \"结束\"\n        },{ label: \"作废\", value: \"7\" }\n      ],\n      bjr: \"\",\n      // 是否已执行下拉框\n      sfyzxList: [\n        { label: \"已执行\", value: \"已执行\" },\n        { label: \"未执行\", value: \"未执行\" }\n      ],\n      // 获取当前登录人账号\n      currentUser: this.$store.getters.name,\n      //上传图片时的携带的其他参数\n      uploadImgData: {\n        businessId: \"\" //携带的表单主键id\n      },\n      //上传图片时的请求头\n      header: {},\n      //图片list\n      imgList: [],\n      //图片地址url\n      dialogImageUrl: \"\",\n      //展示图片dialog控制\n      imgDialogVisible: false,\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"czplc\",\n        businessKey: \"\",\n        businessType: \"倒闸操作票\",\n        variables: {},\n        defaultFrom: true,\n        nextUser: \"\",\n        processType: \"complete\"\n      },\n      //工作流弹窗\n      isShow: false,\n      //流程图查看\n      openLoadingImg: false,\n      imgSrc: \"\", //流程图查看地址\n      timeData: [],\n      timeLineShow: false,\n      //弹出框标题\n      activitiOption: { title: \"上报\" },\n      titleyl: \"\",\n      yl: false,\n      isSaveRow: false,\n      //线路选择弹出\n      isShowXlDialog: false,\n      // 多选框选中的id\n      ids: [],\n      single: true,\n      multiple: true,\n      selectData: [],\n      //弹出框中表格数据\n      propTableData: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //form表单\n      form: {\n        bh: \"\",\n        status: \"\",\n        czr: \"\",\n        jhr: \"\",\n        sdshr: \"\",\n        lx: 1, //输电\n        colFirst: [],\n        bz: \"\"\n      },\n      formCzp: {\n        sdshr: \"\"\n      },\n      //详情弹框是否显示\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          bm: \"\",\n          status: \"\",\n          czrw: \"\",\n          xlr: \"\",\n          xlsj: \"\",\n          hlsj: \"\",\n          czr: \"\",\n          jhr: \"\",\n          sdshr: \"\",\n          rqArr: []\n        }, //查询条件\n        fieldList: [\n          { label: \"编号\", value: \"bm\", type: \"input\", clearable: true },\n\n          { label: \"线路名称\", value: \"xlmc\", type: \"input\", clearable: true },\n          { label: \"操作人\", value: \"czr\", type: \"input\", clearable: true },\n          { label: \"监护人\", value: \"jhr\", type: \"input\", clearable: true },\n          //{ label: '审核人', value: 'sdshr', type: 'input', clearable: true },\n          {\n            label: \"日期\",\n            value: \"rqArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          {\n            label: \"状态\",\n            value: \"status\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            clearable: true,\n            options: [\n              {\n                value: \"0\",\n                label: \"操作票填报\"\n              },\n              /* {\n                value: '1',\n                label: '班组审核'\n              },*/\n              {\n                value: \"2\",\n                label: \"分公司审核\"\n              },\n              {\n                value: \"3\",\n                label: \"操作票办结\"\n              },{ label: \"作废\", value: \"7\" }\n            ]\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 1,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"编号\", prop: \"bm\", minWidth: \"80\" },\n          { label: \"状态\", prop: \"statusCn\", minWidth: \"80\" },\n          { label: \"线路名称\", prop: \"xlmc\", minWidth: \"120\" },\n          { label: \"工作名称\", prop: \"gzmc\", minWidth: \"120\" },\n          { label: \"开票时间\", prop: \"createTime\", minWidth: \"80\" },\n          { label: \"操作人\", prop: \"czr\", minWidth: \"60\" },\n          { label: \"监护人\", prop: \"jhr\", minWidth: \"60\" },\n          { label: \"日期\", prop: \"rq\", minWidth: \"80\" }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        //输电\n        lx: 1,\n        status: \"\"\n      },\n      selectRows: []\n    };\n  },\n  mounted() {\n    //获取token\n    this.header.token = getToken();\n    //列表查询\n    this.getData(this.$route.query);\n  },\n  methods: {\n    //作废票\n    nullifyGzp(objId) {\n      this.$confirm(\"票作废后只能查看，不能进行任何操作，确认作废吗?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(async () => {\n          let { code } = await updateById({ status: 7, objId: objId });\n          if (code === \"0000\") {\n            this.$message.success(\"操作成功!!\");\n            this.getData();\n          }\n        })\n        .catch(() => {});\n    },\n    getShow() {\n      this.buttonNameShow = false;\n      switch (this.form.status) {\n        case \"0\":\n          this.buttonName = \"上 报\";\n          if (this.currentUser === this.form.createBy) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case \"1\":\n          this.buttonName = \"提 交\";\n          if (this.form.bzspr === this.currentUser) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case \"2\":\n          this.buttonName = \"提 交\";\n          if (this.form.fgsspr === this.currentUser) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case \"3\":\n          this.buttonName = \"办 结\";\n          if (this.form.bjr === this.currentUser) {\n            this.buttonNameShow = true;\n            this.isDisabledBj = false;\n          }\n          break;\n      }\n    },\n    //改造后的上传多个图片文件\n    uploadForm() {\n      var newUrl = []; //用来存放当前未改动过的图片url,此图片不进行删除处理，其余当前业务id下面的图片数据将进行删除\n      // console.log(this.$refs.uploadImg.fileList)\n      var imageType = [\"png\", \"jpg\"];\n\n      const formData = new FormData();\n      // 因为要传一个文件数组过去，所以要循环append\n      this.imgList.forEach(file => {\n        if (file.raw == undefined) {\n          newUrl.push(file.url);\n        }\n        formData.append(\"files\", file.raw);\n      });\n      formData.append(\"businessId\", this.uploadImgData.businessId); // 自定义参数\n      formData.append(\"newUrl\", newUrl); // 未改动过的图片url\n      formData.append(\"type\", imageType); // 未改动过的图片url\n      api\n        .requestPost(\"/isc-api/file/uploadFiles\", formData, 1)\n        .then(res => {\n          // 清空图片列表（一定要清空，否则上传成功后还是会调用handleChange（）函数，上传成功后列表中还存在图片）\n          this.imgList = [];\n          //查询接口数据\n          this.getData();\n        })\n        .catch(res => {\n          this.$message.success(\"图片上传失败！\");\n        });\n    },\n    // 选择文件时，往fileList里添加\n    handleChange(file, fileList) {\n      this.imgList = fileList;\n    },\n    handleProgress(event, file, fileList) {},\n    //图片移除\n    handleRemove(file, fileList) {\n      this.imgList = fileList;\n    },\n    //图片放大\n    handlePictureCardPreview(file) {\n      this.dialogImageUrl = [file.url];\n      this.imgDialogVisible = true;\n    },\n    onClose() {\n      this.imgDialogVisible = false;\n    },\n    //清空\n    change(e) {\n      this.$forceUpdate();\n    },\n    //关闭弹窗\n    closeActiviti() {\n      if (this.$refs.activiti.$refs.form) {\n        this.$refs.activiti.$refs.form.resetFields();\n      }\n      this.isShow = false;\n    },\n    //关闭流程查看页面\n    colseTimeLine() {\n      this.timeLineShow = false;\n    },\n    async showTimeLine(row) {\n      this.processData.businessKey = row.objId;\n      let { code, data } = await HistoryList(this.processData);\n      this.timeData = data;\n      this.timeLineShow = true;\n    },\n    //流程图片查看\n    showProcessImg(row) {\n      this.openLoadingImg = true;\n      this.imgSrc =\n        \"/activiti-api/process/read-resource?processDefinitionKey=czplc&businessKey=\" +\n        row.objId +\n        \"&t=\" +\n        new Date().getTime();\n    },\n    //工作流回传数据\n    async todoResult(data) {\n      let row = {\n        objId: data.businessKey,\n        isStart: 1,\n        isBack: data.processType === \"rollback\" ? 1 : 0\n      };\n      if (data.processType === \"rollback\") {\n        switch (data.activeTaskName) {\n          case \"操作票填报\":\n            row.status = \"0\";\n            break;\n          case \"分公司审核\":\n            row.status = \"2\";\n            break;\n        }\n      } else {\n        switch (data.activeTaskName) {\n          case \"分公司审核\":\n            row.status = \"2\";\n            row.fgsspr = data.nextUser;\n            break;\n          case \"操作票办结\":\n            row.status = \"3\";\n            row.bjr = data.nextUser;\n            break;\n          case \"结束\":\n            row.status = \"4\";\n            break;\n        }\n      }\n      let { code } = await updateById(row);\n      if (code === \"0000\") {\n        await this.getData();\n      } else {\n        this.$message.warning(\"操作失败\");\n      }\n    },\n    //上报发送办结\n    async getSbFsBj(type) {\n      let row = { ...this.form };\n      if (type === \"complete\") {\n        switch (row.status) {\n          case \"0\":\n            this.isShowDetails = false;\n            this.processData.variables.pass = true;\n            this.processData.businessKey = row.objId;\n            this.processData.processType = type;\n            this.processData.dw = row.fgs;\n            this.processData.rylx = \"分公司审核人\";\n            this.activitiOption.title = \"提交\";\n            this.processData.defaultFrom = true;\n            this.processData.personGroupId = 14;\n            this.isShow = true;\n            break;\n          case \"2\":\n            this.isShowDetails = false;\n            this.processData.variables.pass = true;\n            this.processData.businessKey = row.objId;\n            this.processData.processType = type;\n            this.activitiOption.title = \"提交\";\n            this.processData.defaultFrom = true;\n            this.processData.personGroupId = 15;\n            this.processData.dw = row.fgs;\n            this.processData.rylx = \"办结人\";\n            this.isShow = true;\n            break;\n          case \"3\":\n            this.form.colFirst = this.propTableData.colFirst;\n            this.form.objIdList = this.ids;\n            //this.propTableData.colFirst 验证已打钩的数据中不能有空内容\n            let tableValid = this.propTableData.colFirst.some(\n              item => item.sfwc && !(item.xlr && item.xlsj && item.hlsj)\n            );\n            await this.$refs[\"form\"].validate(async valid => {\n              if (valid && !tableValid) {\n                this.isShowDetails = false;\n                this.uploadImgData.businessId = this.form.objId;\n                this.uploadForm();\n                await saveOrUpdate(this.form).then(res => {\n                  if (res.code === \"0000\") {\n                    this.getData();\n                    this.processData.variables.pass = true;\n                    this.processData.businessKey = row.objId;\n                    this.processData.processType = type;\n                    this.activitiOption.title = \"办结\";\n                    this.processData.defaultFrom = false;\n                    this.isShow = true;\n                  } else {\n                    this.$message.error(\"失败\");\n                  }\n                });\n              } else {\n                this.$message.error(\"请将完成的操作项目填写完整\");\n              }\n            });\n        }\n      } else {\n        this.isShowDetails = false;\n        this.processData.businessKey = row.objId;\n        this.processData.processType = type;\n        this.activitiOption.title = \"回退原因填写\";\n        this.processData.defaultFrom = true;\n        this.processData.variables.pass = false;\n        this.isShow = true;\n      }\n    },\n    // 预览按钮\n    handleYlChange() {\n      this.titleyl = \"查看操作项目\";\n      this.yl = true;\n    },\n    //线路选择接收数据\n    handleAcceptSbData(sbData) {\n      this.form.xlmc = sbData.label;\n    },\n    // 全选按钮\n    handleCheckAllChange(val) {\n      if (val) {\n        this.form.yzxczxs = this.form.czxs;\n        this.form.wzxczxs = 0;\n      } else {\n        this.form.wzxczxs = this.form.czxs;\n        this.form.yzxczxs = 0;\n      }\n      this.propTableData.colFirst.forEach(item => {\n        item.sfwc = val;\n      });\n    },\n    handleCheckChange(val) {\n      if (val) {\n        ++this.form.yzxczxs;\n      } else {\n        --this.form.yzxczxs;\n      }\n      this.form.wzxczxs = this.form.czxs - this.form.yzxczxs;\n    },\n    filterReset() {\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n      this.params = {\n        //输电\n        lx: 1,\n        status: \"\"\n      };\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        this.loading = true;\n        param.mySorts = [{ prop: \"updateTime\", asc: false }];\n        if (!param.status) {\n          param.status = \"0,1,2,3,4\";\n        }\n        const { data, code } = await getList(param);\n        if (code === \"0000\") {\n          for (let i of data.records) {\n            this.statusOptions.forEach(element => {\n              if (i.status === element.value) {\n                i.statusCn = element.label;\n              }\n            });\n          }\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          this.loading = false;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n\n    //修改按钮\n    getUpdate(row) {\n      this.getCzpmx(row);\n      this.title = \"输电操作票修改\";\n      this.form = { ...row };\n      this.imgList = this.form.imgList;\n      this.isDisabledBj = false;\n      this.isShowDetails = true;\n      this.isDisabled = false;\n      this.getShow();\n    },\n    //详情按钮\n    getDetails(row) {\n      this.getCzpmx(row);\n      this.title = \"输电操作票详情查看\";\n      this.form = { ...row };\n      //图片结合\n      this.imgList = this.form.imgList;\n      this.isDisabled = true;\n      this.isDisabledBj = true;\n      this.isShowDetails = true;\n      this.getShow();\n    },\n\n    // 新增修改 确认按钮\n    async saveRow() {\n      try {\n        this.form.colFirst = this.propTableData.colFirst;\n        this.form.objIdList = this.ids;\n        let { code } = await saveOrUpdates(this.form);\n        if (code === \"0000\") {\n          this.$message.success(\"操作成功\");\n        }\n      } catch (e) {\n        console.log(e);\n      }\n      //重置page页从1开始\n      this.tableAndPageInfo.pager.pageResize = \"Y\";\n      this.getData();\n      this.isShowDetails = false;\n    },\n\n    //删除按钮\n    async deleteRow(row) {\n      let objId = \"\";\n      if (row.objId) {\n        objId = row.objId;\n      } else {\n        objId = this.ids[0];\n      }\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove(objId).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              //重置page页从1开始\n              // this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    //表格新增\n    listFirstAdd() {\n      let row = {\n        czrw: \"\",\n        xlr: \"\",\n        xlsj: \"\",\n        hlsj: \"\",\n        sfwc: \"\"\n      };\n      this.propTableData.colFirst.push(row);\n      this.propTableData.sel = row;\n      this.form.czxs = this.propTableData.colFirst.length;\n      this.form.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //表格删除\n    listFirstDel(index, row) {\n      this.ids.push(row.objId);\n      this.propTableData.colFirst.splice(index, 1);\n      this.form.czxs = this.propTableData.colFirst.length;\n      this.form.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //选择已执行时，操作项目默认默认全选\n    handleChangeOfSfzx(val) {\n      if (val === \"已执行\") {\n        this.form.yzxczxs = this.form.czxs;\n        this.form.wzxczxs = 0;\n        this.propTableData.colFirst.forEach(item => {\n          item.sfwc = true;\n        });\n      } else {\n        this.form.wzxczxs = this.form.czxs;\n        this.form.yzxczxs = 0;\n        this.propTableData.colFirst.forEach(item => {\n          item.sfwc = false;\n        });\n      }\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false;\n    },\n    //选择行\n    selectChange(selection) {\n      this.ids = selection.map(item => item.objId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n      this.selectData = selection;\n    },\n    /**\n     *获取操作票明细\n     */\n    async getCzpmx(row) {\n      try {\n        const { data, code } = await getCzpmxList({ objId: row.objId });\n        if (code === \"0000\") {\n          this.propTableData.colFirst = data;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //关闭线路弹窗\n    handleControlSysbSelectDialog(isShow) {\n      this.isShowXlDialog = isShow;\n    },\n    //线路选择事件\n    sysbSelectedClick() {\n      this.isShowXlDialog = true;\n    },\n    //导出word\n    async exportWord(row) {\n      try {\n        let exportData = { ...row };\n        await exportWord(exportData, \"xldzczp\", \"电力系统线路倒闸操作票.docx\");\n      } catch (e) {\n        this.$message.error(\"导出失败！\");\n      }\n    },\n    //导出Pdf\n    async exportPdf(row) {\n      try {\n        let exportData = { ...row };\n        await exportPdf(exportData, \"xldzczp\", \"电力系统线路倒闸操作票.pdf\");\n      } catch (e) {\n        this.$message.error(\"导出失败！\");\n      }\n    },\n    //预览文件\n    async previewFile(row) {\n      try {\n        let exportData = { ...row };\n        await previewFile(exportData, \"xldzczp\");\n      } catch (e) {\n        this.$message.error(\"预览失败！\");\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.card-style {\n  background-color: #e0eff2;\n  line-height: 3vh;\n  border-radius: 3px;\n  font-weight: 800;\n  padding-left: 1vw;\n  margin-bottom: 3vh;\n}\n\n/*控制input输入框边框是否显示*/\n.elInput >>> .el-input__inner {\n  border: 0;\n}\n\n.center {\n  text-align: center; /*让div内部文字居中*/\n}\n\n.item {\n  width: 8.5rem;\n  height: 1.25rem;\n}\n</style>\n"]}]}