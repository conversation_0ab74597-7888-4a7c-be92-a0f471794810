{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\asset\\gfztz.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\asset\\gfztz.js", "mtime": 1730102030153}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagangOilfield/asset/gfztz.js"], "names": ["baseUrl", "getOrganizationSelected", "params", "api", "requestGet", "getNewTreeInfo", "requestPost", "getBdzList", "addBdz", "removeBdz"], "mappings": ";;;;;;;;;;;;;AAAA;;AAEA,IAAMA,OAAO,GAAG,cAAhB;AAGA;;;;;;AAKO,SAASC,uBAAT,CAAiCC,MAAjC,EAAyC;AAC9C,SAAOC,iBAAIC,UAAJ,CAAeJ,OAAO,GAAG,iCAAzB,EAA4DE,MAA5D,EAAoE,CAApE,CAAP;AACD;AAED;;;;;;;AAKO,SAASG,cAAT,CAAwBH,MAAxB,EAAgC;AACrC,SAAOC,iBAAIG,WAAJ,CAAgBN,OAAO,GAAG,4BAA1B,EAAwDE,MAAxD,EAAgE,CAAhE,CAAP;AACD;AAGD;;;;;;AAIO,SAASK,UAAT,CAAoBL,MAApB,EAA4B;AACjC,SAAOC,iBAAIG,WAAJ,CAAgBN,OAAO,GAAG,4BAA1B,EAAwDE,MAAxD,EAAgE,CAAhE,CAAP;AACD;AAED;;;;;;;AAKO,SAASM,MAAT,CAAgBN,MAAhB,EAAwB;AAC7B,SAAOC,iBAAIG,WAAJ,CAAgBN,OAAO,GAAG,wBAA1B,EAAoDE,MAApD,EAA4D,CAA5D,CAAP;AACD;AAED;;;;;;;AAKO,SAASO,SAAT,CAAmBP,MAAnB,EAA2B;AAChC,SAAOC,iBAAIG,WAAJ,CAAgBN,OAAO,GAAG,2BAA1B,EAAuDE,MAAvD,EAA+D,CAA/D,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\r\n\r\nconst baseUrl = \"/manager-api\";\r\n\r\n\r\n/**\r\n * 获取组织结构下拉框数据\r\n * @param params\r\n * @returns {Promise | Promise<any>}\r\n */\r\nexport function getOrganizationSelected(params) {\r\n  return api.requestGet(baseUrl + '/select/getOrganizationSelected', params, 2);\r\n}\r\n\r\n/**\r\n *\r\n * @param params\r\n * @returns {Promise | Promise<unknown>}\r\n */\r\nexport function getNewTreeInfo(params) {\r\n  return api.requestPost(baseUrl + '/equipListOfGfz/getBdzTree', params, 2);\r\n}\r\n\r\n\r\n/**\r\n * 查询变电站台账信息\r\n * @returns {Promise | Promise<unknown>}\r\n */\r\nexport function getBdzList(params) {\r\n  return api.requestPost(baseUrl + '/equipListOfGfz/getBdzList', params, 2);\r\n}\r\n\r\n/**\r\n * 新增变电站\r\n * @param params\r\n * @returns {Promise<unknown>}\r\n */\r\nexport function addBdz(params) {\r\n  return api.requestPost(baseUrl + '/equipListOfGfz/addBdz', params, 2);\r\n}\r\n\r\n/**\r\n * 删除变电站信息\r\n * @param params\r\n * @returns {Promise | Promise<unknown>}\r\n */\r\nexport function removeBdz(params) {\r\n  return api.requestPost(baseUrl + '/equipListOfGfz/removeBdz', params, 2);\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n"]}]}