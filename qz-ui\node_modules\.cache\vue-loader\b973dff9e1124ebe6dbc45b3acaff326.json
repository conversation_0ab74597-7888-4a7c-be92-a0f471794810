{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\technicalParameter.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\technicalParameter.vue", "mtime": 1706897322897}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["technicalParameter.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "technicalParameter.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbbzk", "sourcesContent": ["<template>\n  <div>\n    <el-white class=\"button-group\">\n      <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"addTechnicalParameter\">新增</el-button>\n      <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"deleteTechnicalParameter\">删除</el-button>\n    </el-white>\n    <el-table\n      stripe\n      border\n      v-loading=\"loading\"\n      :data=\"tableData\"\n      @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n      <el-table-column label=\"技术参数编码\" align=\"center\" prop=\"jscsbm\"/>\n      <el-table-column label=\"技术参数名称\" align=\"center\" prop=\"jscsmc\" :show-overflow-tooltip=\"true\"/>\n      <el-table-column label=\"单位\" align=\"center\" prop=\"dw\" :show-overflow-tooltip=\"true\"/>\n      <el-table-column label=\"显示类型\" align=\"center\" prop=\"cslx\" :show-overflow-tooltip=\"true\"/>\n      <el-table-column label=\"操作\" fixed=\"right\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <el-button type=\"text\" @click=\"updateTechnicalParameter(scope.row)\" class=\"updateBtn\">修改</el-button>\n          <el-button type=\"text\" @click=\"getDetails(scope.row)\">详情</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <pagination\n      v-show=\"queryParams.total > 0\"\n      :total=\"queryParams.total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"/>\n    <dialog-form\n      ref=\"dialogForm\"\n      :append-to-body=\"true\"\n      label-width=\"150px\"\n      out-width=\"50%\"\n      :reminder=\"reminder\"\n      :rows=\"rows\"\n      @inputChange1=\"paramsTypeChange\"\n      @save=\"saveTechnicalParameter\"\n    />\n  </div>\n</template>\n\n<script>\nimport {\n  deleteTechnicalParameter,\n  getTechnicalParameter,\n  saveOrUpdateTechnicalParameter\n} from '@/api/dagangOilfield/bzgl/sblxwh/jscs'\nimport DialogForm from 'com/dialogFrom/dialogForm'\n\nexport default {\n  components: { DialogForm },\n  props: {\n    deviceTypeData: {\n      type: Object\n    }\n  },\n  name: 'technicalParameter',\n  data() {\n    return {\n      reminder: '新增',\n      rows: 2,\n      //表单数据\n      tableData: [],\n      //查询条件\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        total: 0,\n        sblxbm: ''\n      },\n      loading: false,\n      formList: [\n        {\n          label: '设备类型名称：',\n          value: '',\n          type: 'disabled',\n          name: 'sblxmc',\n          default: true,\n          rules: { required: true, message: '请选择设备类型名称' }\n        },\n        {\n          label: '技术参数名称：',\n          value: '',\n          name: 'jscsmc',\n          default: true,\n          type: 'input',\n          rules: { required: true, message: '请输入技术参数名称' }\n        },\n        {\n          label: '技术参数类型：',\n          value: '',\n          name: 'jscslx',\n          default: true,\n          type: 'selectChange1',\n          options: [{ label: '字符', value: '字符' }, { label: '数值', value: '数值' }],\n          rules: { required: true, message: '请选择技术参数类型' }\n        },\n        {\n          label: '技术参数编码：',\n          value: '',\n          name: 'jscsbm',\n          default: true,\n          type: 'select',\n          options: [],\n          rules: { required: true, message: '请选择技术参数编码' }\n        },\n        {\n          label: '单位：',\n          value: '',\n          type: 'input',\n          name: 'dw',\n          default: true,\n          rules: { required: false, message: '请输入单位' }\n        },\n        {\n          label: '显示类型：',\n          value: '',\n          type: 'select',\n          name: 'cslx',\n          default: true,\n          options: [\n            { label: 'input', value: 'input' },\n            { label: 'select', value: 'select' },\n            { label: 'date', value: 'date' },\n            { label: 'datetime', value: 'datetime' }\n          ],\n          rules: { required: false, message: '请选择显示类型' }\n        },\n        {\n          label: '排序：',\n          value: '',\n          type: 'input',\n          name: 'px',\n          default: true,\n          rules: { required: false, message: '请输入排序' }\n        },\n        {\n          label: '设备类型编码：',\n          value: '',\n          type: 'input',\n          name: 'sblxbm',\n          default: true,\n          hidden: false,\n          rules: { required: true, message: '请选择设备类型编码' }\n        },\n        {\n          label: 'id：',\n          value: '',\n          name: 'objId',\n          default: true,\n          hidden: false,\n          type: 'input',\n          rules: { required: false, message: '请输入编码' }\n        }\n      ],\n      //选中行数据\n      selectedRowDatas: []\n\n    }\n  },\n  mounted() {\n    this.getList()\n  },\n  methods: {\n    //获取技术参数数据\n    getList() {\n      this.loading = true\n      this.queryParams.sblxbm = this.deviceTypeData.sblxbm\n      getTechnicalParameter(this.queryParams).then(res => {\n        this.tableData = res.data.records\n        this.queryParams.total = res.data.total\n        this.loading = false\n      })\n    },\n    //新增技术参数\n    addTechnicalParameter() {\n      this.reminder = '新增'\n      //初始化formList数据\n      this.formList = this.$options.data().formList\n      const addForm = this.formList.map(item => {\n        if (item.name === 'sblxbm') {\n          item.value = this.deviceTypeData.sblxbm\n        }\n        if (item.name === 'sblxmc') {\n          item.value = this.deviceTypeData.sblx\n        }\n        return item\n      })\n      this.$refs.dialogForm.showzzc(addForm)\n    },\n    //批量删除技术参数数据\n    deleteTechnicalParameter() {\n\n      this.$confirm('确认删除选中数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n\n        let ids = []\n        this.selectedRowDatas.forEach(item => {\n          ids.push(item.objId)\n        })\n\n        deleteTechnicalParameter(ids).then(res => {\n          if (res.code === '0000') {\n            this.$message.success('删除成功')\n            this.getList()\n          } else {\n            this.$message.error('操作失败')\n          }\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        })\n      })\n    },\n    //修改基础参数数据\n    updateTechnicalParameter(row) {\n      const updateList = this.formList.map(item => {\n        item.value = row[item.name]\n        return item\n      })\n      this.reminder = '修改'\n      this.$refs.dialogForm.showzzc(updateList)\n    },\n    //技术参数详情\n    getDetails(row) {\n      const infoList = this.formList.map(item => {\n        item.value = row[item.name]\n        return item\n      })\n      this.reminder = '详情'\n      this.$refs.dialogForm.showxq(infoList)\n    },\n    //保存技术参数\n    saveTechnicalParameter(formData) {\n      let message = ''\n      if (formData.objId === '' || !formData.objId) {\n        message = '新增成功'\n      } else {\n        message = '修改成功'\n      }\n      saveOrUpdateTechnicalParameter(formData).then(res => {\n        if (res.code === '0000') {\n          this.$message.success(message)\n          this.getList()\n        } else {\n          this.$message.error('操作失败')\n        }\n      })\n\n    },\n    //行选择事件\n    handleSelectionChange(row) {\n      this.selectedRowDatas = row\n    },\n\n    paramsTypeChange(val) {\n      //技术参数编码下拉框数据\n      let tenhnicalParamOptions = []\n      let type = ''\n      if (val === '字符') {\n        type = 'zf_value'\n        this.getTechnicalParameterOptions(type, tenhnicalParamOptions)\n      } else if (val === '数值') {\n        type = 'sz_value'\n        this.getTechnicalParameterOptions(type, tenhnicalParamOptions)\n      }\n      this.$refs.dialogForm.addUpdateList.list.forEach(item => {\n        if (item.name === 'jscsbm') {\n          item.options = tenhnicalParamOptions\n          item.value = ''\n          return\n        }\n      })\n    },\n    //设置技术参数编码下拉框数据\n    getTechnicalParameterOptions(type, tenhnicalParamOptions) {\n      for (let i = 0; i < 20; i++) {\n        let index = i + 1\n        tenhnicalParamOptions.push({\n          label: type + index,\n          value: type + index\n        })\n      }\n\n    }\n  }\n}\n</script>\n\n<style scoped>\n.pagination-container {\n  position: relative;\n  height: 25px;\n  margin-bottom: 20px !important;\n  margin-top: 15px;\n  padding: 10px 20px !important;\n}\n</style>\n"]}]}