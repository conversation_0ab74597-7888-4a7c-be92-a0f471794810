{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\zxmInfo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\zxmInfo.vue", "mtime": 1706897323692}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["zxmInfo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "zxmInfo.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk", "sourcesContent": ["<template>\r\n  <div class=\"syxmxq_info\">\r\n    <table id=\"mpxq_right\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\"></table>\r\n\r\n    <div id=\"syxmxq_left\">\r\n      <ul class=\"ul2_cont\">\r\n        <li>单元格信息</li>\r\n        <li>\r\n          单元格类型：<el-input\r\n            v-model=\"nrlx\"\r\n            placeholder=\"\"\r\n            class=\"inp1\"\r\n            disabled\r\n            @input=\"val => checkInput(val, 'nrlx')\"\r\n          ></el-input>\r\n        </li>\r\n        <li>\r\n          本次试验数值：<el-input\r\n            v-model=\"text\"\r\n            placeholder=\"\"\r\n            class=\"inp1\"\r\n            disabled\r\n            @input=\"val => checkInput(val, 'text')\"\r\n          ></el-input>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n\r\n    <div id=\"syxmxq_button\" v-show=\"changeTr.id\">\r\n      <dataChart :cell-id=\"changeTr.id\"></dataChart>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { Loading } from \"element-ui\";\r\nimport {\r\n  resetCells,\r\n  createTable,\r\n  mergeCells,\r\n  editCells,\r\n  getCells\r\n} from \"@/api/dagangOilfield/bzgl/sympk/sympInfo\";\r\nimport { getTable } from \"@/api/dagangOilfield/bzgl/sympk/sympk\";\r\nimport { getBwSelect, getZxmSelect } from \"@/api/dagangOilfield/bzgl/syxm\";\r\nimport dataChart from \"@/views/dagangOilfield/bzgl/sybzk/dataChart.vue\";\r\nexport default {\r\n  components: { dataChart },\r\n  props: {\r\n    mpData: {\r\n      type: Object\r\n    },\r\n    mxData: {\r\n      type: Array\r\n    }\r\n  },\r\n  name: \"zxmInfo\",\r\n  data() {\r\n    return {\r\n      //初始表格的行数 列数\r\n      hs: \"\",\r\n      ls: \"\",\r\n      //初始合并行数 列数\r\n      addhs: \"\",\r\n      addls: \"\",\r\n      nrlx: \"\",\r\n      text: \"\",\r\n      //一行的数据\r\n      cellData: \"\",\r\n      //选中合并行、列的tr\r\n      changeTr: \"\",\r\n      //查询条件\r\n      params: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        mpid: \"\",\r\n        zy: \"\",\r\n        sblxbm: \"\",\r\n        zxmId: \"\"\r\n      },\r\n      loading: null, //遮罩层\r\n      tdWidth: 0, //一个单元格所占宽度\r\n      tdMap: new Map(), //用于存放被合并或拆分的单元格（key:当前点击的单元格,value:被处理过的单元格数组）\r\n      tableData: this.mxData, //表格数据\r\n      sblxbm: undefined, //设备类型编码\r\n\r\n      title: \"单元格属性定义\",\r\n      form: {\r\n        objId: undefined,\r\n        readonly: undefined,\r\n        nrlx: undefined,\r\n        bwId: undefined,\r\n        zxmId: undefined,\r\n        nrbs: undefined\r\n      },\r\n      show: false,\r\n      bwList: [], //试验部位\r\n      zxmmDataList: [], //子项目所有结果数据\r\n      zxmmcList: [], //子项目\r\n      hidde: false,\r\n      hiddebw: false,\r\n      hiddezxm: false,\r\n      nrlxList: [\r\n        { label: \"静态文本\", value: \"静态文本\" },\r\n        { label: \"试验子项目\", value: \"试验子项目\" },\r\n        { label: \"试验部位\", value: \"试验部位\" },\r\n        { label: \"试验数据\", value: \"试验数据\" }\r\n      ],\r\n      readonlyList: [\r\n        { label: \"是\", value: \"Y\" },\r\n        { label: \"否\", value: \"N\" }\r\n      ]\r\n    };\r\n  },\r\n\r\n  mounted() {\r\n    //获取表格初始行数和列数\r\n    this.initTableData();\r\n  },\r\n  methods: {\r\n    //获取铭牌内容数据\r\n    initTableData() {\r\n      //初始化遮罩层\r\n      this.loading = Loading.service({\r\n        text: \"加载中，请稍后...\",\r\n        background: \"rgba(109,106,106,0.35)\"\r\n      });\r\n      this.hs =\r\n        typeof this.mpData.AHs != \"undefined\"\r\n          ? this.mpData.AHs\r\n          : this.mpData.aHs;\r\n      this.ls =\r\n        typeof this.mpData.ALs != \"undefined\"\r\n          ? this.mpData.ALs\r\n          : this.mpData.aLs;\r\n      this.sblxbm = this.mpData.sblxbm;\r\n      //更新输入框的值\r\n      this.updateInputValue([\"hs\", \"ls\"]);\r\n      this.processTable();\r\n      this.loading.close(); //关闭遮罩层\r\n    },\r\n\r\n    selectzxmvalue(val) {\r\n      let obj = {};\r\n      obj = this.zxmmcList.find(item => {\r\n        return item.value === val;\r\n      });\r\n      this.form.nrbs = obj.laber;\r\n    },\r\n\r\n    selectbwvalue(val) {\r\n      let obj = {};\r\n      obj = this.bwList.find(item => {\r\n        return item.value === val;\r\n      });\r\n      this.form.nrbs = obj.laber;\r\n    },\r\n\r\n    selectvalue(val) {\r\n      const { value, label } = val;\r\n      if (label == \"静态文本\") {\r\n        this.hidde = true;\r\n        this.hiddebw = false;\r\n        this.hiddezxm = false;\r\n        this.form.nrlx = \"静态文本\";\r\n      }\r\n      if (label == \"试验子项目\") {\r\n        this.hiddezxm = true;\r\n        this.hiddebw = false;\r\n        this.hidde = false;\r\n        this.form.nrlx = \"试验子项目\";\r\n      }\r\n      if (label == \"试验部位\") {\r\n        this.hiddebw = true;\r\n        this.hiddezxm = false;\r\n        this.hidde = false;\r\n        this.form.nrlx = \"试验部位\";\r\n      }\r\n      if (label == \"试验数据\") {\r\n        this.hidde = false;\r\n        this.hiddebw = false;\r\n        this.hiddezxm = false;\r\n        this.form.nrlx = \"试验数据\";\r\n      }\r\n    },\r\n\r\n    //获取部位下拉框\r\n    getSybw() {\r\n      getBwSelect({ sblxbm: this.sblxbm }).then(res => {\r\n        this.bwList = res.data;\r\n      });\r\n    },\r\n\r\n    // 获取试验子项目下拉框数据\r\n    getSyzxm() {\r\n      getZxmSelect().then(res => {\r\n        this.zxmmcList = res.data;\r\n      });\r\n    },\r\n\r\n    //根据行数和列数创建表格\r\n    processTable() {\r\n      var tbody = document.getElementById(\"mpxq_right\");\r\n      if (tbody != null) {\r\n        tbody.innerHTML = \"\";\r\n        let hs = this.hs;\r\n        let ls = this.ls;\r\n        this.tdWidth = 100 / Number(ls);\r\n        let str = \"\";\r\n\r\n        for (let i = 0; i < hs; i++) {\r\n          let temp = \"<tr>\";\r\n          for (let j = 0; j < this.tableData.length; j++) {\r\n            let item = this.tableData[j];\r\n            let sjlx = item.sjlx; //数据类型\r\n            let text = item.text;\r\n            let nrbs = item.nrbs == null ? (text ? text : \"\") : item.nrbs;\r\n            if (item.rowindex === i.toString()) {\r\n              temp +=\r\n                \"<td class='trName' id='\" +\r\n                item.objId +\r\n                \"' style='width: \" +\r\n                this.tdWidth * item.colspan +\r\n                \"%' rowspan='\" +\r\n                item.rowspan +\r\n                \"' colspan='\" +\r\n                item.colspan +\r\n                \"'>\" +\r\n                nrbs +\r\n                \"</td>\";\r\n            }\r\n          }\r\n          temp += \"</tr>\";\r\n          str += temp;\r\n        }\r\n        tbody.innerHTML = str;\r\n        // //给循环出来的单元格加上点击事件\r\n        this.addClickEvent();\r\n      }\r\n    },\r\n\r\n    //手动创建表格\r\n    createTable() {\r\n      //初始化遮罩层\r\n      this.loading = Loading.service({\r\n        text: \"加载中，请稍后...\",\r\n        background: \"rgba(109,106,106,0.35)\"\r\n      });\r\n      let params = JSON.stringify({\r\n        objId: this.mpData.objId, //铭牌id\r\n        aHs: Number(this.hs), //行数\r\n        aLs: Number(this.ls), //列数\r\n        lbbs: \"A\" //类别标识，表示修改的A表格\r\n      });\r\n      createTable(params).then(res => {\r\n        if (res.code === \"0000\") {\r\n          this.updateTable();\r\n        } else {\r\n          this.$message.error(\"操作失败\");\r\n          this.loading.close(); //关闭遮罩层\r\n        }\r\n      });\r\n    },\r\n    //给循环出来的单元格加上点击事件\r\n    addClickEvent() {\r\n      let trArr = document.getElementsByClassName(\"trName\"); //不可编辑的单元格\r\n      let inputArr = document.getElementsByClassName(\"input_cls\"); //可编辑的单元格\r\n      let that = this;\r\n      if (trArr != null) {\r\n        //循环所有的tr\r\n        for (let i = 0; i < trArr.length; i++) {\r\n          trArr[i].onclick = function() {\r\n            that.changeTr = this;\r\n            that.addhs = that.changeTr.rowSpan;\r\n            that.addls = that.changeTr.colSpan;\r\n            that.cellData = that.getCellEle(that.changeTr.id);\r\n            that.nrlx = that.cellData.nrlx;\r\n            that.text = that.cellData.text;\r\n          };\r\n        }\r\n      }\r\n      if (inputArr != null) {\r\n        //循环所有的tr\r\n        for (let i = 0; i < inputArr.length; i++) {\r\n          inputArr[i].onclick = function() {\r\n            that.changeTr = this;\r\n            that.addhs = that.changeTr.rowSpan;\r\n            that.addls = that.changeTr.colSpan;\r\n            that.cellData = that.getCellEle();\r\n            that.nrlx = that.cellData.nrlx;\r\n            that.text = that.cellData.text;\r\n            console.log(that.cellData);\r\n          };\r\n        }\r\n      }\r\n    },\r\n    //合并拆分保存\r\n    saveChangeTable() {\r\n      //初始化遮罩层\r\n      this.loading = Loading.service({\r\n        text: \"加载中，请稍后...\",\r\n        background: \"rgba(109,106,106,0.35)\"\r\n      });\r\n\r\n      let params = JSON.stringify({\r\n        objId: this.changeTr.id,\r\n        rowspan: this.addhs,\r\n        colspan: this.addls\r\n      });\r\n      //先请求接口，如果后台可以执行合并或拆分，则将最新的表格数据请求回来进行前端展示\r\n      mergeCells(params).then(res => {\r\n        if (res.code === \"0000\") {\r\n          this.updateTable();\r\n        } else {\r\n          this.$message.error(\"操作失败\");\r\n          this.loading.close(); //关闭遮罩层\r\n        }\r\n      });\r\n    },\r\n    //处理合并或拆分\r\n    processTr(ids) {\r\n      //点击的单元格id\r\n      let clickId = this.changeTr.id;\r\n      let arr1 = []; //需要重新设置map的数组\r\n      //如果之前已经处理过该单元格,则先将其还原\r\n      if (this.tdMap.has(clickId)) {\r\n        this.tdMap.get(clickId).forEach(item => {\r\n          if (item != null) {\r\n            this.resetCell(item);\r\n            item.style.display = \"table-cell\";\r\n          }\r\n        });\r\n        //操作完后将数据从map中删除\r\n        this.tdMap.delete(clickId);\r\n      }\r\n      let processEle = []; //被处理的元素\r\n\r\n      //现将连带受影响的单元格还原，再进行隐藏处理\r\n      if (ids.length > 0) {\r\n        //执行还原\r\n        ids.forEach(id1 => {\r\n          let ele = document.getElementById(id1);\r\n          //如果此次处理的单元格中有已经被处理过的，先将其还原\r\n          if (this.tdMap.has(id1)) {\r\n            this.tdMap.get(id1).forEach(item => {\r\n              this.resetCell(item);\r\n              item.style.display = \"table-cell\";\r\n            });\r\n            //操作完后将数据从map中删除\r\n            this.tdMap.delete(id1);\r\n          }\r\n          //处理被连带的已经合并过的单元格\r\n          if (ele) {\r\n            let className = ele.className;\r\n            if (this.tdMap.has(className)) {\r\n              let mergeCell = document.getElementById(className); //被连带的已经合并过的cell\r\n              if (mergeCell) {\r\n                this.resetCell(mergeCell);\r\n              }\r\n              this.tdMap.get(className).forEach(item => {\r\n                //需要把此次要隐藏的单元格排除掉，不然隐藏完下次循环又会放出来\r\n                if (!ids.includes(item)) {\r\n                  item.style.display = \"table-cell\";\r\n                } else {\r\n                  arr1.push(item);\r\n                }\r\n              });\r\n              //处理完成后，更新map中的值，将处理过的排除掉\r\n              if (arr1.length > 0) {\r\n                this.tdMap.set(className, arr1);\r\n              } else {\r\n                //操作完后将数据从map中删除\r\n                this.tdMap.delete(className);\r\n              }\r\n            }\r\n          }\r\n        });\r\n        //执行隐藏\r\n        ids.forEach(id => {\r\n          let ele = document.getElementById(id);\r\n          //将多余的单元格隐藏\r\n          if (ele) {\r\n            processEle.push(ele); //添加数据保存到map中\r\n\r\n            document.getElementById(id).style.display = \"none\";\r\n            //将className设置给被操作的单元格，方便下次有连带操作时对单元格进行处理\r\n            document.getElementById(id).className = clickId;\r\n          }\r\n        });\r\n        //重新设置map中的值\r\n        this.tdMap.set(clickId, processEle);\r\n      }\r\n    },\r\n    //取消更改的合并行、列数\r\n    clearChangeTable() {},\r\n\r\n    //关闭弹窗\r\n    getInsterClose() {\r\n      this.show = false;\r\n      this.form = {\r\n        objId: undefined,\r\n        readonly: undefined,\r\n        nrlx: undefined,\r\n        nrbs: undefined,\r\n        zxmId: undefined,\r\n        bwId: undefined\r\n      };\r\n    },\r\n\r\n    //进行合并或拆分操作\r\n    mergeTable(hs, ls, addh, addl) {\r\n      if (hs === 1) {\r\n        //合并列\r\n        if (ls >= 1) {\r\n          this.mergeCells(addh, addl, hs, ls);\r\n        }\r\n      } else {\r\n        if (hs > 1) {\r\n          //多行\r\n          //合并行\r\n          if (ls === 1) {\r\n            this.mergeRows(addh, addl, hs, ls);\r\n          } else if (ls > 1) {\r\n            //合并多行多列\r\n            this.mergeRowsAndCells(addh, addl, hs, ls);\r\n          }\r\n        }\r\n      }\r\n      //要合并的单元格进行合并\r\n      this.changeTr.style.width = this.tdWidth * ls + \"%\"; //设置合并后的单元格宽度\r\n      this.changeTr.rowSpan = this.addhs;\r\n      this.changeTr.colSpan = this.addls;\r\n    },\r\n    /**\r\n     * 第一种情况，合并列（一行多列）\r\n     * @param h 当前元素所在行\r\n     * @param l 当前元素所在列\r\n     * @param hs 要合并的行数\r\n     * @param ls 要合并的列数\r\n     */\r\n    mergeCells(h, l, hs, ls) {\r\n      let removeIds = []; //要删除的元素的id数组\r\n      let ls_xh = ls; //要循环的列数\r\n      if (ls > this.ls - l) {\r\n        //不能超过剩余可操作的列数\r\n        ls_xh = this.ls - l;\r\n      }\r\n      for (let i = 1; i < ls_xh; i++) {\r\n        removeIds.push(h + \"|\" + (l + i));\r\n      }\r\n      //删除多余单元格\r\n      this.processTr(removeIds);\r\n    },\r\n    /**\r\n     * 第二种情况，合并行（多行一列）\r\n     * @param h 当前元素所在行\r\n     * @param l 当前元素所在列\r\n     * @param hs 要合并的行数\r\n     * @param ls 要合并的列数\r\n     */\r\n    mergeRows(h, l, hs, ls) {\r\n      let removeIds = []; //要删除的元素的id数组\r\n      let hs_xh = hs; //要循环的行数\r\n      if (hs > this.hs - h) {\r\n        //不能超过剩余可操作的行数\r\n        hs_xh = this.hs - h;\r\n      }\r\n      console.log(\"hs_xh\", hs_xh);\r\n      for (let i = 1; i < hs_xh; i++) {\r\n        removeIds.push(h + i + \"|\" + l);\r\n      }\r\n      //删除多余单元格\r\n      this.processTr(removeIds);\r\n    },\r\n    /**\r\n     * 第三种情况，合并多行多列\r\n     * @param h 当前元素所在行\r\n     * @param l 当前元素所在列\r\n     * @param hs 要合并的行数\r\n     * @param ls 要合并的列数\r\n     */\r\n    mergeRowsAndCells(h, l, hs, ls) {\r\n      let removeIds = []; //要删除的元素的id数组\r\n      let removeId = \"\";\r\n      //先循环行（从当前行开始循环）\r\n      for (let j = 0; j < hs; j++) {\r\n        //循环列\r\n        for (let i = 0; i < ls; i++) {\r\n          //从当前列循环\r\n          removeId = h + j + \"|\" + (l + i);\r\n          //将当前单元格排除掉\r\n          if (removeId !== h + \"|\" + l) {\r\n            removeIds.push(removeId);\r\n          }\r\n        }\r\n      }\r\n      //删除多余单元格\r\n      this.processTr(removeIds);\r\n    },\r\n    //更新输入框的值\r\n    updateInputValue(arrs) {\r\n      for (let i = 0; i < arrs.length; i++) {\r\n        let ele = document.getElementById(arrs[i]);\r\n        if (ele != null && typeof ele != \"undefined\") {\r\n          switch (arrs[i]) {\r\n            case \"hs\":\r\n              ele.value = this.hs;\r\n              break;\r\n            case \"ls\":\r\n              ele.value = this.ls;\r\n              break;\r\n            case \"addhs\":\r\n              ele.value = this.addhs;\r\n              break;\r\n            case \"addls\":\r\n              ele.value = this.addls;\r\n              break;\r\n          }\r\n        }\r\n      }\r\n    },\r\n    //重置单元格内容\r\n    resetTable() {\r\n      //初始化遮罩层\r\n      this.loading = Loading.service({\r\n        text: \"加载中，请稍后...\",\r\n        background: \"rgba(109,106,106,0.35)\"\r\n      });\r\n      let objId = this.changeTr.id;\r\n      let params = this.getCellEle(objId);\r\n      resetCells(params).then(res => {\r\n        if (res.code === \"0000\") {\r\n          this.updateTable();\r\n        } else {\r\n          this.$message.error(\"操作失败\");\r\n        }\r\n        this.loading.close(); //关闭遮罩层\r\n      });\r\n    },\r\n    //单元格属性编辑并保存\r\n    saveTdValue() {\r\n      this.show = true;\r\n      this.getSybw();\r\n      this.getSyzxm();\r\n      //   this.form.readonly = this.cellData.readonly;\r\n      //   this.form.nrlx = this.cellData.nrlx;\r\n      //   this.form.nrbs =this.cellData.nrbs;\r\n      //   this.form.objId =this.cellData.objId;\r\n      //初始化遮罩层\r\n      // this.loading = Loading.service({\r\n      //   text:\"加载中，请稍后...\",\r\n      //   background:'rgba(109,106,106,0.35)',\r\n      // })\r\n      // let objId = this.changeTr.id;\r\n      // let val = this.changeTr.getElementsByTagName(\"input\")[0].value;\r\n      // let params = this.getCellEle(objId);\r\n      // params.nrbs = val;\r\n      // editCells(params).then(res=>{\r\n      //   if(res.code==='0000'){\r\n      //     this.updateTable();\r\n      //   }else{\r\n      //     this.$message.error('操作失败');\r\n      //     this.loading .close();//关闭遮罩层\r\n      //   }\r\n      // })\r\n    },\r\n\r\n    //单元格属性编辑\r\n    async saveRow() {\r\n      try {\r\n        this.form.objId = this.changeTr.id;\r\n        this.form.mpid = this.cellData.mpid;\r\n        console.log(\"--form--\" + this.form);\r\n        let { code } = await editCells(this.form);\r\n        if (code === \"0000\") {\r\n          this.updateTable();\r\n          this.$message.success(\"操作成功\");\r\n        }\r\n      } catch (e) {\r\n        console.log(e);\r\n      }\r\n      this.show = false;\r\n    },\r\n\r\n    //重置单元格属性（宽度，合并行数，合并列数）\r\n    resetCell(ele) {\r\n      if (ele) {\r\n        ele.style.width = this.tdWidth + \"%\";\r\n        ele.rowSpan = \"1\";\r\n        ele.colSpan = \"1\";\r\n      }\r\n    },\r\n    //输入框校验\r\n    checkInput(val, changeType) {\r\n      switch (changeType) {\r\n        case \"hs\":\r\n          this.hs = val;\r\n          break;\r\n        case \"ls\":\r\n          this.ls = val;\r\n          break;\r\n        case \"addhs\":\r\n          this.addhs = val;\r\n          break;\r\n        case \"addls\":\r\n          this.addls = val;\r\n          break;\r\n        case \"nrlx\":\r\n          this.nrlx = val;\r\n          break;\r\n      }\r\n    },\r\n    //获取单元格明细数据\r\n    getCellDetail(hs, ls) {\r\n      let result = null;\r\n      this.tableData.forEach(item => {\r\n        if (\r\n          item.rowindex === hs.toString() &&\r\n          item.colindex === ls.toString()\r\n        ) {\r\n          result = item;\r\n          if (result.nrbs == null) {\r\n            result.nrbs = \"\";\r\n          }\r\n        }\r\n      });\r\n      return result;\r\n    },\r\n    //获取某个单元格对象\r\n    getCellEle(objId) {\r\n      let result = null;\r\n      this.tableData.forEach(item => {\r\n        if (item.objId === objId) {\r\n          result = item;\r\n        }\r\n      });\r\n      return result;\r\n    },\r\n    //获取最新的表格并重新渲染\r\n    updateTable() {\r\n      let param = JSON.stringify({\r\n        obj_id: this.mpData.objId,\r\n        lbbs: \"A\"\r\n      });\r\n      //获取最新的表格数据\r\n      getTable(param).then(res1 => {\r\n        if (res1.code === \"0000\") {\r\n          this.tableData = res1.data;\r\n          //根据最新的表格数据重新画\r\n          this.processTable();\r\n          this.$message.success(res1.msg);\r\n        } else {\r\n          this.$message.error(\"无法获取更新后的表格数据！\");\r\n        }\r\n        this.loading.close(); //关闭遮罩层\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.syxmxq_info {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n#syxmxq_left {\r\n  margin-left: 20px;\r\n  ul {\r\n    list-style-type: none;\r\n    margin: 0;\r\n    padding: 8px;\r\n  }\r\n  border: 1px solid #0cc283;\r\n  width: 28%;\r\n  li:nth-child(1) {\r\n    font-weight: 700;\r\n  }\r\n  li {\r\n    line-height: 48px;\r\n    padding-left: 8px;\r\n    .el-input {\r\n      width: 70%;\r\n    }\r\n  }\r\n}\r\n#syxmxq_button {\r\n  margin-top: 20px;\r\n  ul {\r\n    list-style-type: none;\r\n    margin: 0;\r\n    padding: 8px;\r\n  }\r\n  border: 1px solid #0cc283;\r\n  width: 99.5%;\r\n}\r\n.change_btn {\r\n  margin-top: 10px !important;\r\n  height: 36px !important;\r\n}\r\n.change_btn:nth-child(1) {\r\n  margin-left: 29%;\r\n}\r\n.change_btn:nth-child(2) {\r\n  margin-left: 20%;\r\n}\r\n#mpxq_right {\r\n  width: 70%;\r\n  height: 180px;\r\n  border: 1px solid #000;\r\n}\r\n</style>\r\n<style>\r\n#mpxq_right td {\r\n  border: 1px solid #000;\r\n  height: 35px;\r\n  line-height: 35px;\r\n  text-align: center;\r\n}\r\n#mpxq_right tr {\r\n  height: 35px;\r\n}\r\n#mpxq_right .atc {\r\n  background-color: #11ba6d;\r\n}\r\n#mpxq_right .input_cls {\r\n  text-align: center;\r\n  border: none;\r\n  width: 99%;\r\n  height: 99%;\r\n}\r\n</style>\r\n"]}]}