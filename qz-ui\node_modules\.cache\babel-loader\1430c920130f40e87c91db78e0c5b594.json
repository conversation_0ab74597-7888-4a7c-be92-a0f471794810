{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sdsbgl\\sdsb.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sdsbgl\\sdsb.vue", "mtime": 1706897324964}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["sdsb.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmgDA;;AAWA;;AAKA;;AAMA;;AAKA;;AAIA;;AACA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,IAAA,EAAA;AAAA,GAFA;AAGA,EAAA,IAHA,kBAGA;AAAA;;AACA,QAAA,mBAAA,GAAA,SAAA,mBAAA,CAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA,UAAA,CAAA,KAAA,CAAA,iBAAA,CAAA,WAAA,EAAA;AACA,QAAA,QAAA,CAAA,IAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AACA;;AACA,UACA,KAAA,CAAA,iBAAA,CAAA,WAAA,GACA,KAAA,CAAA,iBAAA,CAAA,aAFA,EAGA;AACA,QAAA,QAAA,CAAA,IAAA,KAAA,CAAA,gBAAA,CAAA,CAAA;AACA,OALA,MAKA;AACA,QAAA,QAAA;AACA;AACA,KAZA;;AAaA,WAAA;AACA,MAAA,GAAA,EAAA,EADA;AAGA,MAAA,KAAA,EAAA;AACA,WAAA,mBADA;AAEA,WAAA,WAFA;AAGA,WAAA,WAHA;AAIA,WAAA;AAJA,OAHA;AASA,MAAA,MAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA;AAFA,OATA;AAaA,MAAA,WAAA,EAAA;AACA,QAAA,UAAA,EAAA,SADA;AAEA,QAAA,IAAA,EAAA;AAFA,OAbA;AAiBA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAjBA;AAqBA,MAAA,aAAA,EAAA,KArBA;AAsBA,MAAA,OAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,EAaA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAbA,CAtBA;AAwCA,MAAA,IAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,CAxCA;AAuDA;AACA,MAAA,wBAAA,EAAA,EAxDA;AAyDA,MAAA,iBAAA,EAAA,EAzDA;AA0DA,MAAA,YAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,IAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA,EAFA;AAGA,UAAA,IAAA,EAAA,EAHA;AAIA,UAAA,IAAA,EAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAHA,EAIA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA,CACA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WADA,EAEA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAFA;AAJA,SAJA,EAaA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,KAAA,EAAA,SAHA;AAIA,UAAA,QAAA,EAAA,WAJA;AAKA,UAAA,MAAA,EAAA;AALA,SAbA,EAoBA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SApBA;AAPA,OA1DA;AAyFA,MAAA,iBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA;AACA;;;;;;;;;;;AATA;AAZA,OAzFA;AA4HA,MAAA,aAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,YAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA;AAZA,OA5HA;AAkJA;AACA,MAAA,aAAA,EAAA,IAnJA;AAoJA;AACA,MAAA,mBAAA,EAAA,KArJA;AAsJA;AACA,MAAA,cAAA,EAAA,EAvJA;AAwJA;AACA,MAAA,YAAA,EAAA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AADA,OAzJA;AAoMA;AACA,MAAA,YAAA,EAAA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AADA,OArMA;AA0QA;AACA,MAAA,eAAA,EAAA,MA3QA;AA4QA,MAAA,IAAA,EAAA,KA5QA;AA6QA;AACA,MAAA,QAAA,EAAA;AACA,QAAA,KAAA,EAAA,SADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OA9QA;AAmRA;AACA,MAAA,oBAAA,EAAA,IApRA;AAqRA;AACA,MAAA,aAAA,EAAA,QAtRA;AAuRA;AACA,MAAA,YAAA,EAAA,IAxRA;AAyRA;AACA,MAAA,WAAA,EAAA,KA1RA;AA2RA;AACA,MAAA,WAAA,EAAA,KA5RA;AA6RA;AACA,MAAA,iBAAA,EAAA,KA9RA;AA+RA;AACA,MAAA,oBAAA,EAAA,KAhSA;AAiSA;AACA,MAAA,mBAAA,EAAA,KAlSA;AAmSA;AACA,MAAA,IAAA,EAAA,EApSA;AAsSA,MAAA,OAAA,EAAA,KAtSA;AAuSA;AACA,MAAA,WAAA,EAAA,EAxSA;AA0SA,MAAA,UAAA,EAAA,EA1SA;AA2SA;AACA,MAAA,WAAA,EAAA,EA5SA;AA8SA;AACA,MAAA,cAAA,EAAA,IA/SA;AAgTA;;AACA;;;;;;;AAOA,MAAA,UAAA,EAAA,IAxTA;AAyTA;AACA,MAAA,KAAA,EAAA;AACA;AACA;AACA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAHA;AAIA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA,CAOA;AACA;AACA;AACA;AACA;;AAXA,OA1TA;AAwUA,MAAA,aAAA,EAAA,EAxUA;AAyUA;AACA,MAAA,QAAA,EAAA,EA1UA;AA2UA,MAAA,UAAA,EAAA;AACA,QAAA,MAAA,EAAA;AADA,OA3UA;AA8UA,MAAA,uBAAA,EAAA,EA9UA;AA+UA,MAAA,QAAA,EAAA,IA/UA;AAgVA,MAAA,QAAA,EAAA,KAhVA;AAiVA,MAAA,QAAA,EAAA,KAjVA;AAkVA,MAAA,KAAA,EAAA,EAlVA;AAmVA;AACA,MAAA,iBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,YAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AACA;AAAA,UAAA,IAAA,EAAA,aAAA;AAAA,UAAA,KAAA,EAAA,UAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA;AACA;;;;;;;;;;;AARA;AAZA,OApVA;AAqXA,MAAA,WAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,MAAA,EAAA,EAHA;AAIA,QAAA,QAAA,EAAA,EAJA;AAKA,QAAA,QAAA,EAAA,EALA;AAMA,QAAA,UAAA,EAAA;AANA,OArXA;AA6XA;AACA,MAAA,mBAAA,EAAA,KA9XA;AA+XA,MAAA,MAAA,EAAA,KA/XA;AAgYA;AACA,MAAA,MAAA,EAAA;AACA,QAAA,KAAA,EAAA;AADA,OAjYA;AAoYA;AACA,MAAA,eAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,CArYA;AAyYA;AACA,MAAA,WAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,CA1YA;AA8YA,MAAA,SAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,EAaA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAbA,CA9YA;AAgaA,MAAA,YAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,MAAA,EAAA,EADA;AAEA,UAAA,QAAA,EAAA,EAFA;AAGA,UAAA,QAAA,EAAA,EAHA;AAIA;AACA,UAAA,UAAA,EAAA;AALA,SADA;AAQA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,OAFA;AAGA,UAAA,KAAA,EAAA,aAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SAFA,EAQA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,MAAA,EAAA,YAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SARA,EAcA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,UAFA;AAGA,UAAA,aAAA,EAAA,EAHA;AAIA,UAAA,KAAA,EAAA,QAJA;AAKA,UAAA,OAAA,EAAA,CACA;AAAA,YAAA,KAAA,EAAA,OAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WADA,EAEA;AAAA,YAAA,KAAA,EAAA,MAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAFA,EAGA;AAAA,YAAA,KAAA,EAAA,MAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAHA,EAIA;AAAA,YAAA,KAAA,EAAA,KAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAJA;AALA,SAdA,EA0BA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,UAFA;AAGA,UAAA,aAAA,EAAA,EAHA;AAIA,UAAA,KAAA,EAAA,YAJA;AAKA,UAAA,OAAA,EAAA,CACA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WADA,EAEA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAFA;AALA,SA1BA,EAoCA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,UAFA;AAGA,UAAA,aAAA,EAAA,EAHA;AAIA,UAAA,KAAA,EAAA,UAJA;AAKA,UAAA,OAAA,EAAA,CACA;AAAA,YAAA,KAAA,EAAA,MAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WADA,EAEA;AAAA,YAAA,KAAA,EAAA,MAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAFA;AALA,SApCA,CA8CA;AACA;AACA;AACA;AACA;AACA;AAnDA;AARA,OAhaA;AA8dA;AACA;AACA,MAAA,mBAAA,EAAA,KAheA;AAieA;AACA,MAAA,eAAA,EAAA,QAleA;AAmeA;AACA,MAAA,OAAA,EAAA,EApeA;AAqeA,MAAA,SAAA,EAAA,CACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA,OAreA;AAgfA,MAAA,YAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,OAAA,EAAA,CAJA;AAKA,QAAA,QAAA,EAAA,EALA,CAMA;;AANA,OAhfA;AAwfA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAA,QAAA,EAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OA/fA;AAggBA;AACA,MAAA,gBAAA,EAAA,SAjgBA;AAkgBA,MAAA,eAAA,EAAA,KAlgBA;AAmgBA;AACA;AACA,MAAA,QAAA,EAAA,EArgBA;AAsgBA;AACA,MAAA,QAAA,EAAA,EAvgBA;AAwgBA,MAAA,SAAA,EAAA,KAxgBA;AAygBA,MAAA,iBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA;AACA;;;;;;;;;;;;AATA;AAZA,OAzgBA;AA4iBA;AACA,MAAA,MAAA,EAAA;AACA,QAAA,UAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA,SAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA;AAJA,OA7iBA;AAmjBA,MAAA,YAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,IAAA,EAAA,EADA;AAEA,UAAA,QAAA,EAAA,EAFA;AAGA,UAAA,IAAA,EAAA;AAHA,SADA;AAMA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,OAFA;AAGA,UAAA,KAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SADA,EAOA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,OAFA;AAGA,UAAA,KAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SAPA,EAaA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA,CACA;AAAA,YAAA,KAAA,EAAA,KAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WADA,EAEA;AAAA,YAAA,KAAA,EAAA,MAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAFA,EAGA;AACA,YAAA,KAAA,EAAA,MADA;AAEA,YAAA,KAAA,EAAA;AAFA,WAHA,EAOA;AAAA,YAAA,KAAA,EAAA,OAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAPA;AAJA,SAbA,EA2BA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA,CACA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WADA,EAEA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAFA;AAJA,SA3BA,EAoCA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,OAFA;AAGA,UAAA,KAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SApCA,EA0CA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,KAAA,EAAA,MAHA;AAIA,UAAA,MAAA,EAAA;AAJA,SA1CA;AANA,OAnjBA;AA2mBA,MAAA,MAAA,EAAA,KA3mBA;AA4mBA,MAAA,SAAA,EAAA,EA5mBA;AA6mBA,MAAA,MAAA,EAAA,EA7mBA;AA8mBA;AACA,MAAA,QAAA,EAAA,EA/mBA;AAgnBA;AACA,MAAA,WAAA,EAAA,EAjnBA;AAknBA,MAAA,UAAA,EAAA;AACA,QAAA,UAAA,EAAA;AADA,OAlnBA;AAqnBA,MAAA,aAAA,EAAA;AACA,QAAA,UAAA,EAAA,SADA;AAEA,QAAA,IAAA,EAAA;AAFA,OArnBA;AAynBA,MAAA,eAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,YAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA;AAZA,OAznBA;AA8oBA,MAAA,OAAA,EAAA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,QAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,QAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAVA;AAaA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAbA;AAgBA,QAAA,UAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAhBA,OA9oBA;AAkqBA,MAAA,OAAA,EAAA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,KAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,QAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAVA;AAaA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAbA;AAgBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAhBA;AAmBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAnBA;AAsBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAtBA,OAlqBA;AA4rBA,MAAA,MAAA,EAAA,EA5rBA;AA6rBA,MAAA,QAAA,EAAA,EA7rBA;AA8rBA,MAAA,YAAA,EAAA,EA9rBA;AA+rBA,MAAA,SAAA,EAAA,EA/rBA;AAgsBA,MAAA,SAAA,EAAA,EAhsBA;AAisBA,MAAA,iBAAA,EAAA;AACA,QAAA,eAAA,EAAA,EADA;AAEA,QAAA,eAAA,EAAA,EAFA;AAGA,QAAA,iBAAA,EAAA,EAHA;AAIA,QAAA,iBAAA,EAAA,EAJA;AAKA,QAAA,iBAAA,EAAA,EALA;AAMA,QAAA,aAAA,EAAA;AANA,OAjsBA;AAysBA,MAAA,8BAAA,EAAA,KAzsBA;AA0sBA,MAAA,kBAAA,EAAA;AACA,QAAA,QAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,eAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,eAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,aAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAVA;AAaA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,QAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SADA;AAbA,OA1sBA;AA2tBA,MAAA,MAAA,EAAA,EA3tBA;AA2tBA;AACA,MAAA,QAAA,EAAA,KA5tBA;AA4tBA;AACA,MAAA,WAAA,EAAA,EA7tBA;AA6tBA;AACA,MAAA,WAAA,EAAA,EA9tBA;AA8tBA;AACA,MAAA,SAAA,EAAA,EA/tBA;AA+tBA;AACA,MAAA,WAAA,EAAA,EAhuBA;AAguBA;AACA,MAAA,SAAA,EAAA,EAjuBA;AAiuBA;AACA,MAAA,YAAA,EAAA,EAluBA,CAkuBA;;AAluBA,KAAA;AAouBA,GArvBA;AAsvBA,EAAA,KAAA,EAAA,EAtvBA;AAuvBA,EAAA,QAAA,EAAA;AACA,IAAA,kBAAA,EAAA,8BAAA;AACA,aACA,KAAA,iBAAA,CAAA,iBAAA,GACA,KAAA,iBAAA,CAAA,aADA,GAEA,KAAA,iBAAA,CAAA,iBAHA;AAKA,KAPA;AAQA,IAAA,gBAAA,EAAA,4BAAA;AACA,aACA,KAAA,iBAAA,CAAA,eAAA,GACA,KAAA,iBAAA,CAAA,aADA,GAEA,KAAA,iBAAA,CAAA,iBAFA,GAGA,KAAA,iBAAA,CAAA,eAJA;AAMA;AAfA,GAvvBA;AAwwBA,EAAA,OAxwBA,qBAwwBA;AACA;AACA,SAAA,QAAA;AACA,SAAA,uBAAA;AACA,SAAA,UAAA,CAAA,KAAA,MAAA,CAAA,KAAA,EAJA,CAIA;;AACA,SAAA,UAAA,GALA,CAKA;AACA,GA9wBA;AA+wBA,EAAA,OA/wBA,qBA+wBA;AACA,SAAA,uBAAA;AACA,GAjxBA;AAkxBA,EAAA,OAAA,EAAA;AACA,IAAA,UADA,wBACA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,MAAA,CAAA,WAAA,EADA;;AAAA;AAAA;AAAA,uBAEA,MAAA,CAAA,WAAA,EAFA;;AAAA;AAAA;AAAA,uBAGA,MAAA,CAAA,WAAA,EAHA;;AAAA;AAAA;AAAA,uBAIA,MAAA,CAAA,WAAA,EAJA;;AAAA;AAAA;AAAA,uBAKA,MAAA,CAAA,YAAA,EALA;;AAAA;AAAA;AAAA,uBAMA,MAAA,CAAA,WAAA,EANA;;AAAA;AAAA;AAAA,uBAOA,MAAA,CAAA,WAAA,EAPA;;AAAA;AAAA;AAAA,uBAQA,MAAA,CAAA,SAAA,EARA;;AAAA;AAAA;AAAA,uBASA,MAAA,CAAA,YAAA,EATA;;AAAA;AAAA;AAAA,uBAUA,MAAA,CAAA,WAAA,EAVA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KAZA;AAaA,IAAA,WAbA,yBAaA;AAAA;;AACA,iCAAA,WAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,MAAA,CAAA,SAAA,CAAA,IAAA,CAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA,KAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA;AAAA,WAAA;AACA,SAFA;AAGA,OAJA;AAKA,KAnBA;AAoBA,IAAA,WApBA,yBAoBA;AAAA;;AACA,iCAAA,WAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,IAAA,CAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA,KAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA;AAAA,WAAA;AACA,SAFA;AAGA,OAJA;AAKA,KA1BA;AA2BA,IAAA,WA3BA,yBA2BA;AAAA;;AACA,iCAAA,WAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,IAAA,CAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA,KAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA;AAAA,WAAA;AACA,SAFA;AAGA,OAJA;AAKA,KAjCA;AAkCA,IAAA,WAlCA,yBAkCA;AAAA;;AACA,iCAAA,WAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA,KAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA;AAAA,WAAA;AACA,SAFA;AAGA,OAJA;AAKA,KAxCA;AAyCA,IAAA,YAzCA,0BAyCA;AAAA;;AACA,iCAAA,YAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,IAAA,CAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA,KAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA;AAAA,WAAA;AACA,SAFA;AAGA,OAJA;AAKA,KA/CA;AAgDA,IAAA,WAhDA,yBAgDA;AAAA;;AACA,iCAAA,WAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,MAAA,CAAA,SAAA,CAAA,IAAA,CAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA,KAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA;AAAA,WAAA;AACA,SAFA;AAGA,OAJA;AAKA,KAtDA;AAuDA,IAAA,WAvDA,yBAuDA;AAAA;;AACA,iCAAA,WAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,MAAA,CAAA,WAAA,CAAA,IAAA,CAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA,KAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA;AAAA,WAAA;AACA,SAFA;AAGA,OAJA;AAKA,KA7DA;AA8DA,IAAA,SA9DA,uBA8DA;AAAA;;AACA,iCAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,OAAA,CAAA,SAAA,CAAA,IAAA,CAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA,KAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA;AAAA,WAAA;AACA,SAFA;AAGA,OAJA;AAKA,KApEA;AAqEA,IAAA,YArEA,0BAqEA;AAAA;;AACA,iCAAA,YAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,OAAA,CAAA,YAAA,CAAA,IAAA,CAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA,KAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA;AAAA,WAAA;AACA,SAFA;AAGA,OAJA;AAKA,KA3EA;AA4EA,IAAA,WA5EA,yBA4EA;AAAA;;AACA,iCAAA,WAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,OAAA,CAAA,MAAA,CAAA,IAAA,CAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA,KAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA;AAAA,WAAA;AACA,SAFA;AAGA,OAJA;AAKA,KAlFA;AAmFA;AACA,IAAA,YApFA,0BAoFA;AACA,WAAA,MAAA,GAAA,EAAA;AACA,WAAA,QAAA,GAAA,KAAA;AACA,KAvFA;AAwFA;AACA,IAAA,OAzFA,mBAyFA,GAzFA,EAyFA;AACA,WAAA,MAAA,mCAAA,GAAA;AACA,WAAA,QAAA,GAAA,IAAA;AACA,KA5FA;AA6FA,IAAA,mBA7FA,iCA6FA;AACA,WAAA,YAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,KAAA,IAAA,MAAA,EAAA;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,CACA;AAAA,YAAA,KAAA,EAAA,OAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WADA,EAEA;AAAA,YAAA,KAAA,EAAA,MAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAFA,EAGA;AAAA,YAAA,KAAA,EAAA,MAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAHA,EAIA;AAAA,YAAA,KAAA,EAAA,KAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAJA,CAAA;AAMA;AACA,OATA;AAUA,KAxGA;AAyGA;AACA,IAAA,uBA1GA,qCA0GA;AAAA;;AACA,UAAA,QAAA,GAAA,MAAA,CADA,CACA;;AACA,2CAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,OAAA,CAAA,iBAAA,CAAA,IAAA,CAAA;AACA,YAAA,KAAA,EAAA,IAAA,CAAA,KADA;AAEA,YAAA,KAAA,EAAA,IAAA,CAAA,KAAA,CAAA,QAAA;AAFA,WAAA;AAIA,SALA;;AAMA,QAAA,OAAA,CAAA,YAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,KAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,OAAA,GAAA,OAAA,CAAA,iBAAA;AACA,mBAAA,KAAA;AACA;AACA,SALA;AAMA,OAbA;AAcA,KA1HA;AA2HA;AACA,IAAA,YAAA,EAAA,wBAAA;AAAA;;AACA,WAAA,QAAA,CAAA,cAAA,GAAA,KAAA,QAAA;AAEA,8BAAA,KAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,UAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,UAAA,OAAA,CAAA,mBAAA,GAAA,KAAA;;AACA,UAAA,OAAA,CAAA,OAAA,CAAA;AAAA,YAAA,MAAA,EAAA,OAAA,CAAA,QAAA,CAAA;AAAA,WAAA;;AACA;AACA,SALA,MAKA;AACA,UAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,OAAA;AACA;AACA,OATA;AAUA,KAzIA;AA0IA;AACA,IAAA,UA3IA,sBA2IA,MA3IA,EA2IA;AAAA;;AACA,WAAA,WAAA,+DAAA,KAAA,WAAA,GAAA,MAAA;AACA,UAAA,KAAA,+DAAA,KAAA,WAAA,GAAA,MAAA,CAAA;AACA,2BAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,QAAA,GAAA,IAAA;AACA,QAAA,OAAA,CAAA,QAAA,GAAA,OAAA,CAAA,QAAA,GAAA,KAAA;AACA,QAAA,OAAA,CAAA,iBAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,OAAA,CAAA,iBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,OALA;AAMA,KApJA;AAqJA;AACA,IAAA,WAtJA,uBAsJA,WAtJA,EAsJA,EAtJA,EAsJA;AACA,UAAA,KAAA,GAAA;AACA;AACA,QAAA,IAAA,EAAA,WAFA;AAGA,QAAA,IAAA,EAAA,KAAA,MAAA,CAAA,MAHA;AAIA,QAAA,MAAA,EAAA,GAJA;AAKA,QAAA,IAAA,EAAA,GALA;AAMA,QAAA,OAAA,EAAA,CAAA;AAAA,UAAA,IAAA,EAAA,YAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAAA;AANA,OAAA;AAQA,6BAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA;AACA,cAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,UAAA,IAAA,CAAA,OAAA,CAAA,UAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,KAAA,GAAA,MAAA,CAAA,IAAA;AACA,WAFA;AAGA,UAAA,EAAA,CAAA,IAAA,CAAA;AACA;AACA,OATA;AAUA,KAzKA;AA0KA,IAAA,YA1KA,wBA0KA,IA1KA,EA0KA;AACA,WAAA,SAAA,GAAA,IAAA,CAAA,QAAA,CAAA,MAAA,CACA,UAAA,MAAA;AAAA,eAAA,MAAA,CAAA,QAAA,KAAA,KAAA;AAAA,OADA,CAAA;AAGA,KA9KA;AA+KA;AACA,IAAA,QAhLA,oBAgLA,MAhLA,EAgLA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAAA;AACA,kBAAA,OAAA,CAAA,YAAA,2FACA,OAAA,CAAA,YADA,GAEA,MAFA,GAGA;AAAA,oBAAA,QAAA,EAAA,OAAA,CAAA;AAAA,mBAHA;AAKA,uCAAA,OAAA,CAAA,YAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,oBAAA,OAAA,CAAA,QAAA,GAAA,IAAA;AACA,oBAAA,OAAA,CAAA,QAAA,GAAA,OAAA,CAAA,QAAA,GAAA,KAAA;AACA,oBAAA,OAAA,CAAA,iBAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,oBAAA,OAAA,CAAA,iBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,mBALA;AAMA,iBAZA,CAYA,OAAA,CAAA,EAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,CAAA;AACA;;AAfA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA,KAhMA;AAiMA,IAAA,mBAjMA,qCAiMA;AAAA,UAAA,MAAA,QAAA,MAAA;AAAA,UAAA,IAAA,QAAA,IAAA;AAAA,UAAA,KAAA,QAAA,KAAA;;AACA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,CAAA,OAAA,CAAA,MAAA,IAAA,CAAA,CAAA,EAAA;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,iBAAA,YAAA,CAAA,OAAA,GAAA,CACA;AAAA,cAAA,IAAA,EAAA,sBAAA;AAAA,cAAA,GAAA,EAAA;AAAA,aADA,CAAA;AAGA,WAJA,MAIA;AACA,iBAAA,YAAA,CAAA,OAAA,GAAA,CAAA;AAAA,cAAA,IAAA,EAAA,IAAA;AAAA,cAAA,GAAA,EAAA;AAAA,aAAA,CAAA;AACA;AACA,SARA,MAQA;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,iBAAA,YAAA,CAAA,OAAA,GAAA,CACA;AAAA,cAAA,IAAA,EAAA,sBAAA;AAAA,cAAA,GAAA,EAAA;AAAA,aADA,CAAA;AAGA,WAJA,MAIA;AACA,iBAAA,YAAA,CAAA,OAAA,GAAA,CAAA;AAAA,cAAA,IAAA,EAAA,IAAA;AAAA,cAAA,GAAA,EAAA;AAAA,aAAA,CAAA;AACA;AACA;AACA,OAlBA,MAkBA;AACA,aAAA,YAAA,CAAA,OAAA,GAAA,CAAA;AAAA,UAAA,IAAA,EAAA,kBAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAAA,CAAA;AACA;;AACA,WAAA,QAAA,CAAA,KAAA,YAAA,CAAA,IAAA;AACA,KAxNA;AAyNA;AACA,IAAA,QA1NA,oBA0NA,MA1NA,EA0NA;AAAA;;AACA,UAAA,KAAA,2FAAA,KAAA,MAAA,GAAA,MAAA,GAAA;AAAA,QAAA,MAAA,EAAA,KAAA;AAAA,OAAA,CAAA;AACA,yBAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,QAAA,GAAA,IAAA;AACA,QAAA,OAAA,CAAA,QAAA,GAAA,OAAA,CAAA,QAAA,GAAA,KAAA;AACA,QAAA,OAAA,CAAA,iBAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,OAAA,CAAA,iBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,OALA;AAMA,KAlOA;AAmOA;AACA,IAAA,OAAA,EAAA,iBAAA,MAAA,EAAA;AACA,UAAA,KAAA,QAAA,EAAA;AACA,aAAA,UAAA,CAAA,MAAA;AACA;;AACA,UAAA,KAAA,QAAA,EAAA;AACA,aAAA,QAAA,CAAA,MAAA;AACA;;AACA,UAAA,KAAA,QAAA,EAAA;AACA,aAAA,QAAA,CAAA,MAAA;AACA;AACA,KA9OA;AA+OA;AACA,IAAA,UAhPA,sBAgPA,GAhPA,EAgPA;AACA,WAAA,KAAA,GAAA,QAAA;AACA,WAAA,MAAA,mCAAA,GAAA;AACA,WAAA,UAAA,CAAA,GAAA,CAAA,IAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,WAAA,MAAA,GAAA,KAAA;AACA,KAtPA;AAuPA;AACA,IAAA,YAxPA,wBAwPA,GAxPA,EAwPA;AACA,WAAA,KAAA,GAAA,MAAA;AACA,WAAA,MAAA,mCAAA,GAAA;AACA,WAAA,UAAA,CAAA,GAAA,CAAA,IAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,WAAA,MAAA,GAAA,IAAA;AACA,KA9PA;AA+PA,IAAA,UA/PA,sBA+PA,GA/PA,EA+PA;AACA,MAAA,GAAA;AACA,KAjQA;AAkQA,IAAA,UAlQA,sBAkQA,IAlQA,EAkQA;AAAA;;AACA,WAAA,SAAA,GAAA,EAAA;AACA,UAAA,KAAA,GAAA;AACA,QAAA,IAAA,EAAA,IADA;AAEA,QAAA,MAAA,EAAA,GAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA;AAKA,6BAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,cAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA,EAAA,QAAA,CAAA,MAAA,CACA,UAAA,MAAA;AAAA,mBAAA,MAAA,CAAA,QAAA,KAAA,KAAA;AAAA,WADA,CAAA;AAGA,UAAA,OAAA,CAAA,SAAA,GAAA,IAAA;AACA;AACA,OAPA;AAQA,KAjRA;AAkRA;AACA,IAAA,WAnRA,yBAmRA;AACA,WAAA,MAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,YAAA;AACA,aAAA,KAAA,CAAA,QAAA,EAAA,aAAA;AACA,OAFA;AAGA,WAAA,mBAAA,GAAA,KAAA;AACA,KAzRA;AA0RA;AACA,IAAA,WA3RA,yBA2RA;AAAA;;AACA,UAAA,MAAA,GAAA;AACA,QAAA,EAAA,EAAA,MADA;AAEA,QAAA,EAAA,EAAA,KAAA,MAAA,CAAA;AAFA,OAAA;AAIA,WAAA,KAAA,CAAA,QAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,oCAAA,OAAA,CAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA;AACA,qCAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,oBAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA,CACA;AACA,eAHA;;AAIA,cAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,YAAA;;AACA,cAAA,OAAA,CAAA,QAAA;;AACA,cAAA,OAAA,CAAA,mBAAA,GAAA,KAAA;;AACA,cAAA,OAAA,CAAA,UAAA;;AACA;AACA,aAXA,MAWA;AACA,cAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,OAAA;AACA;AACA,WAfA;AAgBA,SAjBA,MAiBA;AACA,UAAA,UAAA,CAAA,YAAA;AACA,gBAAA,OAAA,GAAA,QAAA,CAAA,sBAAA,CAAA,UAAA,CAAA;;AACA,gBAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,OAAA,CAAA,EAAA;AACA,cAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,OAAA,EAAA,KAAA;AACA,aAFA,MAEA,IAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,UAAA,CAAA,EAAA;AACA,cAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,UAAA,EAAA,KAAA;AACA;AACA,WAPA,EAOA,CAPA,CAAA;AAQA,iBAAA,KAAA;AACA;AACA,OA7BA;AA8BA,KA9TA;AA+TA,IAAA,YAAA,EAAA,sBAAA,MAAA,EAAA;AAAA;;AACA,WAAA,YAAA,+DAAA,KAAA,YAAA,GAAA,MAAA;AACA,UAAA,KAAA,+DAAA,KAAA,YAAA,GAAA,MAAA,CAAA;AACA,2BAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,iBAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,OAAA,CAAA,iBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,OAHA;AAIA,KAtUA;;AAuUA;;;AAGA,IAAA,qBA1UA,iCA0UA,SA1UA,EA0UA;AACA,WAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,KAAA;AAAA,OAAA,CAAA;AACA,WAAA,MAAA,GAAA,SAAA,CAAA,MAAA,KAAA,CAAA;AACA,WAAA,QAAA,GAAA,CAAA,SAAA,CAAA,MAAA;AACA,KA9UA;;AA+UA;;;AAGA,IAAA,YAlVA,0BAkVA;AAAA;;AACA,UAAA,KAAA,GAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,UAAA,iBAAA,EAAA,IADA;AAEA,UAAA,gBAAA,EAAA,IAFA;AAGA,UAAA,IAAA,EAAA;AAHA,SAAA,EAIA,IAJA,CAIA,YAAA;AACA,8BAAA,OAAA,CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,OAAA,CAAA,UAAA;AACA,WANA;AAOA,SAZA;AAaA,OAdA,MAcA;AACA,aAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA;AACA,KAvWA;AAwWA,IAAA,YAxWA,0BAwWA;AAAA;;AACA,UAAA,KAAA,GAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,UAAA,iBAAA,EAAA,IADA;AAEA,UAAA,gBAAA,EAAA,IAFA;AAGA,UAAA,IAAA,EAAA;AAHA,SAAA,EAIA,IAJA,CAIA,YAAA;AACA,4BAAA,OAAA,CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,OAAA,CAAA,QAAA;AACA,WANA;AAOA,SAZA;AAaA,OAdA,MAcA;AACA,aAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA;AACA,KA7XA;AA8XA,IAAA,YA9XA,0BA8XA;AAAA;;AACA,UAAA,KAAA,GAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,UAAA,iBAAA,EAAA,IADA;AAEA,UAAA,gBAAA,EAAA,IAFA;AAGA,UAAA,IAAA,EAAA;AAHA,SAAA,EAIA,IAJA,CAIA,YAAA;AACA,8BAAA,OAAA,CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,OAAA,CAAA,QAAA;AACA,WANA;AAOA,SAZA;AAaA,OAdA,MAcA;AACA,aAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA;AACA,KAnZA;AAoZA;AACA,IAAA,aArZA,2BAqZA;AACA;AACA;AACA;AACA;AACA,UAAA,QAAA,GAAA,SAAA;AACA,UAAA,SAAA,GAAA,mBAAA;AACA,6BAAA,SAAA,EAAA,KAAA,WAAA,EAAA,QAAA;AACA,KA7ZA;AA8ZA;AACA,IAAA,aA/ZA,2BA+ZA;AACA;AACA;AACA;AACA;AACA,UAAA,QAAA,GAAA,KAAA,QAAA,GAAA,OAAA;AACA,UAAA,SAAA,GAAA,oBAAA;AACA,6BAAA,SAAA,EAAA,KAAA,YAAA,EAAA,QAAA;AACA,KAvaA;AAwaA,IAAA,UAxaA,wBAwaA;AACA,UAAA,KAAA,QAAA,EAAA;AACA,aAAA,YAAA;AACA;;AACA,UAAA,KAAA,QAAA,EAAA;AACA,aAAA,YAAA;AACA;;AACA,UAAA,KAAA,QAAA,EAAA;AACA,aAAA,YAAA;AACA;AACA,KAlbA;AAmbA,IAAA,WAnbA,yBAmbA;AACA,UAAA,KAAA,QAAA,EAAA;AACA,aAAA,aAAA;AACA;;AACA,UAAA,KAAA,QAAA,EAAA;AACA,aAAA,aAAA;AACA;AACA,KA1bA;AA2bA;AACA,IAAA,WA5bA,yBA4bA;AACA,WAAA,eAAA,GAAA,IAAA;AACA,WAAA,QAAA,GAAA,EAAA;AACA,KA/bA;;AAgcA;AACA,IAAA,eAjcA,6BAicA;AAAA;;AACA,UAAA,KAAA,UAAA,CAAA,QAAA,KAAA,KAAA,QAAA,EAAA;AACA,aAAA,SAAA,GAAA,IAAA;AACA,aAAA,KAAA,CAAA,MAAA,CAAA,MAAA;AACA,OAHA,MAGA;AACA,aAAA,QAAA,CACA,gCADA,EAEA,IAFA,EAGA;AACA,UAAA,iBAAA,EAAA,IADA;AAEA,UAAA,gBAAA,EAAA,IAFA;AAGA,UAAA,IAAA,EAAA;AAHA,SAHA,EASA,IATA,CASA,YAAA;AACA;AACA,UAAA,OAAA,CAAA,SAAA,GAAA,IAAA;;AACA,UAAA,OAAA,CAAA,KAAA,CAAA,MAAA,CAAA,MAAA;AACA,SAbA,EAcA,KAdA,CAcA,YAAA;AACA,UAAA,OAAA,CAAA,QAAA,CAAA;AACA,YAAA,IAAA,EAAA,MADA;AAEA,YAAA,OAAA,EAAA;AAFA,WAAA;AAIA,SAnBA;AAoBA;AACA,KA3dA;;AA4dA;AACA,IAAA,YA7dA,0BA6dA;AACA,WAAA,SAAA,GAAA,KAAA;AACA,WAAA,eAAA,GAAA,KAAA,CAFA,CAGA;;AACA,WAAA,KAAA,CAAA,MAAA,CAAA,UAAA;AACA,KAleA;;AAmeA;AACA,IAAA,aApeA,yBAoeA,GApeA,EAoeA;AACA,UAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,aAAA,UAAA,CAAA,MAAA;AACA,aAAA,QAAA,GAAA,EAAA,CAFA,CAGA;;AACA,aAAA,KAAA,CAAA,MAAA,CAAA,UAAA;AACA,aAAA,SAAA,GAAA,KAAA;AACA,OANA,MAMA;AACA,aAAA,SAAA,GAAA,KAAA;AACA,aAAA,QAAA,CAAA,GAAA,CAAA,GAAA;AACA,aAAA,KAAA,CAAA,MAAA,CAAA,UAAA;AACA,OAXA,CAYA;;;AACA,WAAA,QAAA,CAAA,KAAA,YAAA,CAAA,IAAA;AACA,WAAA,eAAA,GAAA,KAAA;AACA,KAnfA;;AAofA;AACA,IAAA,YArfA,wBAqfA,IArfA,EAqfA;AACA,WAAA,UAAA,CAAA,QAAA,GAAA,IAAA,CAAA,IAAA,CAAA,SAAA,CACA,CADA,EAEA,IAAA,CAAA,IAAA,CAAA,OAAA,CAAA,GAAA,CAFA,CAAA;AAIA,WAAA,QAAA,GAAA,EAAA;AACA,UAAA,YAAA,GAAA,IAAA,CAAA,IAAA;AACA,WAAA,QAAA,GAAA,YAAA;AACA,KA7fA;;AA8fA;AACA,IAAA,YA/fA,0BA+fA;AACA,WAAA,QAAA,GAAA,EAAA;AACA,WAAA,UAAA,CAAA,MAAA;AACA,KAlgBA;;AAmgBA;AACA,IAAA,YApgBA,wBAogBA,IApgBA,EAogBA,QApgBA,EAogBA;AACA,WAAA,UAAA,CAAA,oBAAA;AACA,KAtgBA;AAugBA,IAAA,YAvgBA,wBAugBA,GAvgBA,EAugBA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,KAAA,EAAA,GAAA;AACA,WAAA,UAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA;AACA,WAAA,UAAA,CAAA,KAAA,GAAA,GAAA,CAAA,KAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,KA5gBA;AA6gBA,IAAA,YA7gBA,0BA6gBA;AAAA;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,iBAAA,EAAA,KAAA,UAAA;AACA,WAAA,QAAA,CAAA,eAAA,KAAA,UAAA,CAAA,IAAA,GAAA,GAAA,EAAA,EAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,UAAA,GAAA,EAAA;AACA,gCAAA,OAAA,CAAA,UAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA;;AACA,YAAA,OAAA,CAAA,aAAA,GAAA,KAAA;;AACA,YAAA,OAAA,CAAA,OAAA;AACA;AACA,SANA;AAOA,OAZA;AAaA,KA5hBA;AA6hBA;AACA,IAAA,SAAA,EAAA,mBAAA,GAAA,EAAA;AACA,WAAA,KAAA,GAAA,UAAA;AACA,WAAA,mBAAA,CAAA,GAAA;AACA,WAAA,WAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA;AACA,WAAA,WAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA;AACA,WAAA,YAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,WAAA,IAAA,GAAA,KAAA;AACA,WAAA,QAAA,GAAA,GAAA;AACA,KAviBA;AAwiBA;AACA,IAAA,WAAA,EAAA,qBAAA,GAAA,EAAA;AACA,WAAA,KAAA,GAAA,QAAA;AACA,WAAA,mBAAA,CAAA,GAAA;AACA,WAAA,WAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA;AACA,WAAA,WAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA;AACA,WAAA,YAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,WAAA,IAAA,GAAA,IAAA;AACA,WAAA,QAAA,GAAA,GAAA;AACA,KAljBA;AAmjBA,IAAA,WAnjBA,uBAmjBA,GAnjBA,EAmjBA,IAnjBA,EAmjBA;AACA,UAAA,IAAA,KAAA,IAAA,EAAA;AACA,aAAA,YAAA,GAAA;AACA,UAAA,QAAA,EAAA,KAAA,YAAA,CAAA,QADA,CAEA;;AAFA,SAAA;AAIA,aAAA,QAAA,CAAA,KAAA,YAAA;AACA;;AACA,WAAA,YAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,KAAA,UAAA,EAAA;AACA,UAAA,IAAA,CAAA,aAAA,GAAA,EAAA;AACA;AACA,OAJA;AAKA,KAhkBA;AAikBA;AACA,IAAA,QAlkBA,sBAkkBA;AAAA;;AACA,+BAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KAtkBA;AAukBA,IAAA,WAvkBA,yBAukBA;AACA,WAAA,QAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,YAAA;AACA,aAAA,KAAA,CAAA,UAAA,EAAA,aAAA;AACA,OAFA;AAIA,WAAA,QAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,YAAA;AACA,aAAA,KAAA,CAAA,UAAA,EAAA,aAAA;AACA,OAFA;AAIA,WAAA,mBAAA,GAAA,KAAA;AACA,KAnlBA;AAolBA,IAAA,YAplBA,wBAolBA,GAplBA,EAolBA;AAAA;;AACA,UAAA,KAAA,QAAA,EAAA;AACA,YAAA,MAAA,+DAAA,GAAA,GAAA,KAAA,aAAA,CAAA;AACA,oCAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,eAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,UAAA,OAAA,CAAA,eAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,SAHA;AAIA;;AACA,UAAA,KAAA,QAAA,EAAA;AACA,YAAA,OAAA,+DAAA,GAAA,GAAA,KAAA,WAAA,CAAA;;AACA,oCAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,aAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,UAAA,OAAA,CAAA,aAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,SAHA;AAIA;AACA,KAnmBA;AAomBA;AACA,IAAA,iBArmBA,+BAqmBA,CAAA,CArmBA;AAsmBA;AACA,IAAA,eAvmBA,6BAumBA;AACA,UAAA,KAAA,QAAA,EAAA;AACA,aAAA,KAAA,GAAA,QAAA;AACA,aAAA,SAAA,GAAA,EAAA;AACA,aAAA,MAAA,GAAA,KAAA;AACA,aAAA,mBAAA,GAAA,IAAA;AACA;;AACA,UAAA,KAAA,QAAA,EAAA;AACA,YAAA,KAAA,MAAA,CAAA,MAAA,IAAA,SAAA,EAAA;AACA,eAAA,KAAA,GAAA,QAAA;AACA,eAAA,OAAA,GAAA,EAAA;AACA,eAAA,MAAA,GAAA,KAAA;AACA,eAAA,mBAAA,GAAA,IAAA;AACA,SALA,MAKA;AACA,eAAA,QAAA,CAAA,IAAA,CAAA,eAAA;AACA;AACA;;AACA,UAAA,KAAA,QAAA,EAAA;AACA,YAAA,KAAA,QAAA,CAAA,MAAA,KAAA,EAAA,IAAA,KAAA,QAAA,CAAA,IAAA,KAAA,EAAA,EAAA;AACA,eAAA,QAAA,CAAA,IAAA,CAAA,mBAAA;AACA;AACA;;AACA,aAAA,QAAA,CAAA,IAAA,GAAA,KAAA,YAAA;AACA,aAAA,QAAA,CAAA,QAAA,GAAA,KAAA,QAAA;AACA,aAAA,QAAA,CAAA,MAAA,GAAA,KAAA,SAAA;AACA,aAAA,QAAA,CAAA,IAAA,GAAA,KAAA,SAAA;AACA,aAAA,KAAA,GAAA,UAAA,CATA,CAUA;;AACA,aAAA,IAAA,GAAA,KAAA,CAXA,CAYA;;AACA,aAAA,mBAAA,GAAA,IAAA,CAbA,CAcA;;AACA,YAAA,GAAA,GAAA,EAAA,CAfA,CAgBA;;AACA,aAAA,mBAAA,CAAA,GAAA;AACA;AACA,KA3oBA;AA4oBA,IAAA,mBA5oBA,iCA4oBA;AACA,UAAA,KAAA,YAAA,IAAA,KAAA,QAAA,EAAA;AACA,aAAA,iBAAA,CAAA,QAAA,GAAA,KAAA,QAAA;AACA,aAAA,IAAA,CAAA,KAAA,iBAAA,EAAA,mBAAA,EAAA,GAAA;AACA,aAAA,IAAA,CAAA,KAAA,iBAAA,EAAA,mBAAA,EAAA,GAAA;AACA,aAAA,iBAAA,CAAA,SAAA,GAAA;AACA,UAAA,MAAA,EAAA,KAAA,YADA;AAEA,UAAA,QAAA,EAAA,KAAA,QAFA;AAGA,UAAA,MAAA,EAAA,KAAA,MAHA;AAIA,UAAA,IAAA,EAAA;AAJA,SAAA;AAMA,aAAA,KAAA,GAAA,QAAA;AACA,aAAA,8BAAA,GAAA,IAAA;AACA;AACA,KA1pBA;AA2pBA,IAAA,oBA3pBA,kCA2pBA;AAAA;;AACA,WAAA,KAAA,CAAA,mBAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,oCAAA,OAAA,CAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,cAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,cAAA,OAAA,CAAA,8BAAA,GAAA,KAAA;;AACA,cAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,YAAA,CAAA,IAAA;AACA;AACA,WANA;AAOA;AACA,OAVA;AAWA,KAvqBA;AAwqBA,IAAA,YAxqBA,wBAwqBA,IAxqBA,EAwqBA;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,IAAA,GAAA,OAAA,IAAA,GAAA,EAAA;;AACA,UAAA,CAAA,QAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,kBAAA;AACA;AACA,KA7qBA;AA8qBA;AACA,IAAA,UA/qBA,sBA+qBA,GA/qBA,EA+qBA;AACA,WAAA,KAAA,GAAA,QAAA;AACA,WAAA,WAAA;AACA,WAAA,aAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA;AACA,WAAA,aAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA;AACA,WAAA,YAAA;AACA,WAAA,MAAA,mCAAA,GAAA;AACA,WAAA,MAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,QAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,WAAA,MAAA,GAAA,KAAA;AACA,KA1rBA;AA2rBA;AACA,IAAA,YA5rBA,wBA4rBA,GA5rBA,EA4rBA;AACA,WAAA,KAAA,GAAA,MAAA;AACA,WAAA,WAAA;AACA,WAAA,aAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA;AACA,WAAA,aAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA;AACA,WAAA,YAAA;AACA,WAAA,MAAA,mCAAA,GAAA;AACA,WAAA,MAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,QAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,WAAA,MAAA,GAAA,IAAA;AACA,KAvsBA;AAwsBA,IAAA,WAxsBA,yBAwsBA;AACA,UAAA,KAAA,KAAA,CAAA,MAAA,EAAA;AACA,aAAA,KAAA,CAAA,MAAA,CAAA,UAAA;AACA;AACA,KA5sBA;AA6sBA;AACA,IAAA,aA9sBA,2BA8sBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAA,MAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,MAAA,CAAA,KAAA,GAAA,SAAA;AACA,WAAA,MAAA,CAAA,MAAA,GAAA,KAAA,YAAA;AACA,WAAA,SAAA,CAAA,YAAA;AACA,aAAA,KAAA,CAAA,QAAA,EAAA,aAAA;AACA,OAFA;AAGA,WAAA,mBAAA,GAAA,KAAA;AACA,KA5tBA;AA6tBA;AACA,IAAA,SA9tBA,uBA8tBA;AAAA;;AACA,WAAA,KAAA,CAAA,QAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,oCAAA,OAAA,CAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,cAAA,OAAA,CAAA,UAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;;AACA,cAAA,OAAA,CAAA,KAAA,CAAA,MAAA,CAAA,MAAA;;AACA,cAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,cAAA,OAAA,CAAA,mBAAA,GAAA,KAAA;;AACA,cAAA,OAAA,CAAA,QAAA;AACA,aANA,MAMA;AACA,cAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,OAAA;;AACA,qBAAA,KAAA;AACA;AACA,WAXA;AAYA,SAbA,MAaA;AACA,UAAA,UAAA,CAAA,YAAA;AACA,gBAAA,OAAA,GAAA,QAAA,CAAA,sBAAA,CAAA,UAAA,CAAA;;AACA,gBAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,OAAA,CAAA,EAAA;AACA,cAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,OAAA,EAAA,KAAA;AACA,aAFA,MAEA,IAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,UAAA,CAAA,EAAA;AACA,cAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,UAAA,EAAA,KAAA;AACA;AACA,WAPA,EAOA,CAPA,CAAA;AAQA,iBAAA,KAAA;AACA;AACA,OAzBA;AA0BA,KAzvBA;AA0vBA;AACA,IAAA,aA3vBA,2BA2vBA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,KA7vBA;AA8vBA,IAAA,YA9vBA,wBA8vBA,IA9vBA,EA8vBA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KAhwBA;AAiwBA;AACA,IAAA,gBAlwBA,8BAkwBA,CAAA,CAlwBA;AAmwBA;AACA,IAAA,mBApwBA,iCAowBA,CAAA,CApwBA;AAqwBA;AACA,IAAA,eAtwBA,2BAswBA,IAtwBA,EAswBA,IAtwBA,EAswBA,QAtwBA,EAswBA;AAAA;;AACA,WAAA,UAAA,GAAA,IAAA,CAAA,UAAA;AACA,WAAA,WAAA,CAAA,IAAA,GAAA,EAAA,CAFA,CAEA;AACA;;AACA,UAAA,IAAA,CAAA,UAAA,IAAA,GAAA,EAAA;AACA;AACA,aAAA,WAAA,CAAA,QAAA,GAAA,EAAA;AACA,aAAA,UAAA,CAAA,KAAA,WAAA;AACA,OAJA,MAIA,IAAA,IAAA,CAAA,UAAA,IAAA,GAAA,EAAA;AACA;AACA,aAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,KAAA,IAAA,CAAA,EAAA,EAAA;AACA,YAAA,OAAA,CAAA,MAAA,GAAA,IAAA,CAAA,KAAA;AACA,mBAAA,KAAA;AACA;AACA,SALA;AAMA,aAAA,IAAA,CAAA,KAAA,MAAA,EAAA,QAAA,EAAA,KAAA,MAAA,EARA,CAQA;;AACA,aAAA,WAAA,CAAA,IAAA,GAAA,IAAA,CAAA,EAAA;AACA,aAAA,UAAA,CAAA,KAAA,WAAA,EAVA,CAUA;AACA,OAXA,MAWA,IAAA,IAAA,CAAA,UAAA,IAAA,GAAA,EAAA;AACA,aAAA,MAAA,CAAA,QAAA,GAAA,IAAA,CAAA,KAAA;AACA,aAAA,MAAA,CAAA,MAAA,GAAA,IAAA,CAAA,EAAA;AACA,aAAA,YAAA,GAAA,IAAA,CAAA,EAAA;AACA,aAAA,YAAA,CAAA,IAAA,CAAA,QAAA,GAAA,IAAA,CAAA,KAAA;AACA,aAAA,QAAA,GAAA,IAAA,CAAA,KAAA;AACA,aAAA,QAAA,CAAA,KAAA,YAAA,CAAA,IAAA;AACA,OAPA,MAOA,IAAA,IAAA,CAAA,UAAA,IAAA,GAAA,EAAA;AACA,aAAA,SAAA,GAAA,IAAA,CAAA,EAAA,CADA,CACA;;AACA,aAAA,SAAA,GAAA,IAAA,CAAA,KAAA;AACA,aAAA,QAAA,CAAA,IAAA,GAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA,CAHA,CAGA;;AACA,aAAA,QAAA,CAAA,QAAA,GAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAJA,CAIA;;AACA,aAAA,QAAA,CAAA,MAAA,GAAA,IAAA,CAAA,EAAA,CALA,CAKA;;AACA,aAAA,QAAA,CAAA,IAAA,GAAA,IAAA,CAAA,KAAA,CANA,CAMA;;AACA,YAAA,MAAA,GAAA,IAAA,CAAA,EAAA,CAPA,CAOA;;AACA,aAAA,QAAA;AACA;AACA,KA1yBA;AA2yBA;AACA,IAAA,QA5yBA,sBA4yBA;AACA,WAAA,iBAAA,GAAA,KAAA;AACA,WAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA,KA/yBA;AAgzBA,IAAA,MAhzBA,oBAgzBA;AAAA;;AACA,WAAA,KAAA,CAAA,UAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,UAAA,OAAA,CAAA,YAAA,GADA,CAEA;;AACA,SAHA,MAGA;AACA,UAAA,UAAA,CAAA,YAAA;AACA,gBAAA,OAAA,GAAA,QAAA,CAAA,sBAAA,CAAA,UAAA,CAAA;;AACA,gBAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,OAAA,CAAA,EAAA;AACA,cAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,OAAA,EAAA,KAAA;AACA,aAFA,MAEA,IAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,UAAA,CAAA,EAAA;AACA,cAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,UAAA,EAAA,KAAA;AACA;AACA,WAPA,EAOA,CAPA,CAAA;AAQA,iBAAA,KAAA;AACA;AACA,OAfA;AAgBA,KAj0BA;AAk0BA;AACA,IAAA,uBAn0BA,qCAm0BA;AAAA;;AACA,UAAA,SAAA,GAAA;AACA,QAAA,IAAA,EAAA;AADA,OAAA;AAGA,2CAAA,SAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,uBAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KA10BA;AA20BA;AACA,IAAA,UA50BA,sBA40BA,IA50BA,EA40BA;AACA,WAAA,UAAA,CAAA,MAAA,GAAA,IAAA;AACA,WAAA,aAAA;AACA,KA/0BA;AAg1BA;AACA,IAAA,mBAj1BA,+BAi1BA,GAj1BA,EAi1BA;AACA;AACA,WAAA,QAAA,GAAA,EAAA;AACA,WAAA,UAAA,CAAA,MAAA,GAAA,GAAA,CAAA,MAAA;AACA,WAAA,QAAA,CAAA,MAAA,GAAA,GAAA,CAAA,MAAA;AACA,WAAA,QAAA,CAAA,IAAA,GAAA,GAAA,CAAA,KAAA;AACA,WAAA,aAAA;AACA,KAx1BA;AAy1BA;AACA,IAAA,aA11BA,2BA01BA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,aAAA,GAAA,EAAA;AACA,kDAAA,OAAA,CAAA,UAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,OAAA,CAAA,aAAA,GAAA,GAAA,CAAA,IAAA;;AACA,kBAAA,OAAA,CAAA,aAAA;AACA,iBAHA;;AAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,KAh2BA;AAi2BA;AACA,IAAA,aAl2BA,2BAk2BA;AAAA;;AACA,sCAAA,KAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,IAAA,EAAA,EAAA;AACA,UAAA,OAAA,CAAA,QAAA,mCAAA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AACA;AACA,OAJA;AAKA,KAx2BA;AAy2BA;AACA,IAAA,eA12BA,6BA02BA;AAAA;;AACA,sCAAA,KAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,mBAAA,GAAA,KAAA;AACA,OAFA;AAGA,KA92BA;AA+2BA,IAAA,cA/2BA,0BA+2BA,EA/2BA,EA+2BA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,sBAAA,EAAA,CADA;;AAAA;AAAA;AACA,gBAAA,IADA,qBACA,IADA;;AAAA,sBAEA,IAAA,KAAA,MAFA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAGA,OAAA,CAAA,WAAA,EAHA;;AAAA;AAIA,gBAAA,OAAA,CAAA,QAAA,CAAA;AACA,kBAAA,IAAA,EAAA,SADA;AAEA,kBAAA,OAAA,EAAA;AAFA,iBAAA;;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAx3BA;AAlxBA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card>\n          <div style=\"overflow: auto;height: 90vh\">\n            <el-tree\n              :expand-on-click-node=\"true\"\n              highlight-current\n              ref=\"tree\"\n              id=\"tree\"\n              :data=\"treeOptions\"\n              :default-expanded-keys=\"['1001']\"\n              @node-click=\"handleNodeClick\"\n              node-key=\"id\"\n              accordion\n            >\n              <span slot-scope=\"{ node, data }\">\n                <i :class=\"icons[data.identifier]\" />\n                <span style=\"margin-left:5px;\" :title=\"data.label\">{{\n                  data.label\n                }}</span>\n              </span>\n            </el-tree>\n          </div>\n        </el-card>\n      </el-col>\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <!--变电站查询-->\n        <el-filter\n          ref=\"filter1\"\n          :data=\"xlfilterInfo.data\"\n          :field-list=\"xlfilterInfo.fieldList\"\n          :width=\"{ labelWidth: 130, itemWidth: 165 }\"\n          comp-table=\"tableAndPageInfo1\"\n          @handleReset=\"filterReset($event, 'xl')\"\n          v-show=\"xltzData\"\n        />\n        <!--  杆塔查询 -->\n        <el-filter\n          ref=\"filter1\"\n          :data=\"gtfilterInfo.data\"\n          :field-list=\"gtfilterInfo.fieldList\"\n          :btnHidden=\"false\"\n          comp-table=\"tableAndPageInfo2\"\n          @handleReset=\"filterReset($event, 'gt')\"\n          v-show=\"gttzData\"\n        />\n        <!--设备查询-->\n        <el-filter\n          ref=\"filter1\"\n          :data=\"sbfilterInfo.data\"\n          :field-list=\"sbfilterInfo.fieldList\"\n          :btnHidden=\"false\"\n          comp-table=\"tableAndPageInfo3\"\n          @handleReset=\"filterReset($event, 'sb')\"\n          v-show=\"sbtzData\"\n        />\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button\n              type=\"primary\"\n              v-hasPermi=\"['sbtz:button:add']\"\n              icon=\"el-icon-plus\"\n              @click=\"AddSensorButton\"\n              >新增\n            </el-button>\n            <el-button\n              v-show=\"gttzData\"\n              type=\"primary\"\n              v-hasPermi=\"['sbtz:button:add']\"\n              icon=\"el-icon-plus\"\n              @click=\"AddTowerBatchButton\"\n              >批量新增\n            </el-button>\n            <el-button\n              v-show=\"!sbtzData\"\n              type=\"success\"\n              icon=\"el-icon-download\"\n              @click=\"exportExcel\"\n              >导出</el-button\n            >\n            <el-button\n              v-show=\"gttzData\"\n              type=\"success\"\n              icon=\"el-icon-download\"\n              @click=\"importExcel\"\n              >导入</el-button\n            >\n            <el-button\n              type=\"danger\"\n              v-hasPermi=\"['sbtz:button:delete']\"\n              icon=\"el-icon-delete\"\n              @click=\"deleteInfo\"\n              >删除\n            </el-button>\n          </div>\n\n          <!--变电站查询-->\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo1\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"61vh\"\n            v-show=\"xltzData\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"updateRow1(scope.row)\"\n                  v-hasPermi=\"['sbtz:button:updete1']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                >\n                </el-button>\n                <el-button\n                  @click=\"detailsInfo1(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n                <router-link\n                  :to=\"{\n                    path: '/jszlgl/tzgl',\n                    query: { wjbh: scope.row.xlbm, dydj: '' }\n                  }\"\n                  style=\"margin-left:10px;\"\n                >\n                  <el-button\n                    type=\"text\"\n                    size=\"small\"\n                    title=\"图纸跳转\"\n                    class=\"el-icon-discover\"\n                  ></el-button>\n                </router-link>\n                <!-- <el-button type=\"text\" size=\"small\" @click=\"jumpToTzgl(scope.row)\" title=\"图纸跳转\" class=\"el-icon-discover\"></el-button> -->\n                <el-button\n                  @click=\"zxwhFun(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"支线维护\"\n                  class=\"el-icon-setting\"\n                  style=\"margin-left:10px;\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n\n          <!--  杆塔查询 -->\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo2\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            @sort-events=\"sortChangeTowerData\"\n            height=\"65.4vh\"\n            v-show=\"gttzData\"\n            @getMethod=\"gtTzData\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"updateStatus(scope.row)\"\n                  v-hasPermi=\"['sbtz:button:ztbg']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"状态变更\"\n                  class=\"el-icon-set-up\"\n                >\n                </el-button>\n                <el-button\n                  @click=\"updateRow2(scope.row)\"\n                  v-hasPermi=\"['sbtz:button:update2']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                >\n                </el-button>\n                <el-button\n                  @click=\"detailsInfo2(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n\n          <!--设备查询-->\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo3\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"65.4vh\"\n            v-show=\"sbtzData\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"updateRow(scope.row)\"\n                  v-hasPermi=\"['sbtz:button:update3']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                >\n                </el-button>\n                <el-button\n                  @click=\"detailsInfo(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!-- 线路详情所用弹出框开始-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"xlDialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"60%\"\n      @close=\"resetxlForm\"\n      v-dialogDrag\n    >\n      <el-form\n        :model=\"xlForm\"\n        ref=\"xlForm\"\n        :disabled=\"xlshow\"\n        :rules=\"xlRules\"\n        label-width=\"130px\"\n      >\n        <!-- <div class=\"divHeader\">\n          <span>基本信息</span>\n        </div> -->\n        <div\n          class=\"block\"\n          style=\"width: 50%;height: 50%;margin-bottom: 2%;float: right\"\n        >\n          <span class=\"demonstration\">线路图</span>\n          <el-carousel\n            trigger=\"click\"\n            class=\"imgCls\"\n            height=\"150px\"\n            indicator-position=\"none\"\n            arrow=\"always\"\n            type=\"card\"\n          >\n            <el-carousel-item v-for=\"(img, index) in xlImgList\" :key=\"index\">\n              <viewer :images=\"xlImgList\" style=\"z-index: 999\">\n                <img :src=\"img.fileUrl\" width=\"100%\" height=\"100%\" />\n              </viewer>\n            </el-carousel-item>\n          </el-carousel>\n        </div>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路图编号\" prop=\"xlbm\">\n              <el-autocomplete\n                v-model=\"xlForm.xlbm\"\n                placeholder=\"请输入线路图编号\"\n                popper-class=\"my-autocomplete\"\n                :fetch-suggestions=\"querySearch\"\n                :trigger-on-focus=\"false\"\n                @select=\"handleSelect\"\n              >\n                <template slot-scope=\"{ item }\">\n                  <div class=\"name\">{{ item.wjbh }}</div>\n                  <span class=\"addr\">{{ item.wjmc }}</span>\n                </template>\n              </el-autocomplete>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路名称\" prop=\"lineName\">\n              <el-input\n                v-model=\"xlForm.lineName\"\n                placeholder=\"请输入线路名称\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路专业\" prop=\"lineType\">\n              <el-select\n                v-model=\"xlForm.lineType\"\n                placeholder=\"请选择线路专业\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in lineTypeOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电压等级\" prop=\"dydjbm\">\n              <el-select\n                v-model=\"xlForm.dydjbm\"\n                placeholder=\"请选择电压等级\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in xloptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"负责人\" prop=\"fzr\">\n              <el-input\n                v-model=\"xlForm.fzr\"\n                :placeholder=\"xlshow ? '' : '请输入负责人'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"运行班组\" prop=\"yxbz\">\n              <el-select\n                v-model=\"xlForm.yxbz\"\n                :placeholder=\"xlshow ? '' : '请选择运行班组'\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in deviceNameOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.label\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"线路性质\" prop=\"xlxz\">\n              <el-input v-model=\"xlForm.xlxz\" placeholder=\"请选择线路性质\" clearable></el-input>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路类型\" prop=\"xllx\">\n              <el-select\n                v-model=\"xlForm.xllx\"\n                :placeholder=\"xlshow ? '' : '请选择线路类型'\"\n                clearable\n              >\n                <el-option label=\"架空线路\" value=\"架空线路\"></el-option>\n                <el-option label=\"混合线路\" value=\"混合线路\"></el-option>\n                <el-option label=\"电缆线路\" value=\"电缆线路\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路起点\" prop=\"startLine\">\n              <el-input\n                v-model=\"xlForm.startLine\"\n                placeholder=\"请输入线路起点\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路终点\" prop=\"stopLine\">\n              <el-input\n                v-model=\"xlForm.stopLine\"\n                :placeholder=\"xlshow ? '' : '请输入线路终点'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"同杆并架长度\" prop=\"tgbjcd\">\n              <el-input v-model=\"xlForm.tgbjcd\" placeholder=\"请输入同杆并架长度\"></el-input>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路起始杆塔号\" prop=\"startTowerNum\">\n              <el-input\n                v-model=\"xlForm.startTowerNum\"\n                :placeholder=\"xlshow ? '' : '请选择线路起始杆塔号'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路终止杆塔号\" prop=\"stopTowerNum\">\n              <el-input\n                v-model=\"xlForm.stopTowerNum\"\n                :placeholder=\"xlshow ? '' : '请选择线路终止杆塔号'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"是否有光缆\" prop=\"sfygl\">\n              <el-select v-model=\"xlForm.sfygl\" placeholder=\"请选择是否有光缆\" clearable>\n                <el-option label=\"是\" value=\"是\"></el-option>\n                <el-option label=\"否\" value=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"光缆类别\" prop=\"gllb\">\n              <el-input\n                v-model=\"xlForm.gllb\"\n                :placeholder=\"xlshow ? '' : '请输入光缆类别'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"投运日期\" class=\"add_sy_tyrq\" prop=\"tyrq\">\n              <el-date-picker\n                v-model=\"xlForm.tyrq\"\n                type=\"date\"\n                :placeholder=\"xlshow ? '' : '选择投运日期'\"\n                format=\"yyyy-MM-dd\"\n                value-format=\"yyyy-MM-dd\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"设计单位\" class=\"add_sy_tyrq\" prop=\"sjdw\">\n              <el-input v-model=\"xlForm.sjdw\" placeholder=\"请输入设计单位\"></el-input>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"电缆型号\" prop=\"dlxh\">\n              <el-input\n                v-model=\"xlForm.dlxh\"\n                :placeholder=\"xlshow ? '' : '请输入电缆型号'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电缆长度(M)\" prop=\"dlLength\">\n              <el-input\n                v-model=\"xlForm.dlLength\"\n                placeholder=\"请输入电缆长度\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"检修单位\" prop=\"jxdw\">\n              <el-input v-model=\"xlForm.jxdw\" placeholder=\"请输入检修单位\"></el-input>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路全长（KM）\" prop=\"totalLength\">\n              <el-input\n                v-model=\"xlForm.totalLength\"\n                placeholder=\"请输入线路全长\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路状态\" prop=\"lineStatus\">\n              <el-select\n                v-model=\"xlForm.lineStatus\"\n                placeholder=\"请选择线路状态\"\n              >\n                <el-option\n                  v-for=\"item in xlztOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"负责人\" prop=\"fzr\">\n              <el-input v-model=\"xlForm.fzr\" placeholder=\"请输入负责人\"></el-input>\n            </el-form-item>\n          </el-col> -->\n          <!-- </el-row> -->\n          <!-- <div class=\"divHeader\">\n          <span>详细信息</span>\n        </div>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"资产性质\" class=\"add_sy_tyrq\" prop=\"zcxz\">\n              <el-select v-model=\"xlForm.zcxz\" placeholder=\"请选择资产性质\" clearable >\n                <el-option label=\"公用\" value=\"公用\"></el-option>\n                <el-option label=\"专用\" value=\"专用\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否为联络线路\" prop=\"sfllxl\">\n              <el-select v-model=\"xlForm.sfllxl\" placeholder=\"请选择是否为联络线路\" clearable>\n                <el-option label=\"是\" value=\"是\"></el-option>\n                <el-option label=\"否\" value=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"联络类型\" prop=\"lllx\">\n              <el-select v-model=\"xlForm.lllx\" placeholder=\"请选择联络类型\" clearable>\n                <el-option label=\"低压\" value=\"低压\"></el-option>\n                <el-option label=\"高压\" value=\"高压\"></el-option>\n                <el-option label=\"单环\" value=\"单环\"></el-option>\n                <el-option label=\"双环\" value=\"双环\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"运行班组\" prop=\"yxbz\">\n              <el-select v-model=\"xlForm.yxbz\" placeholder=\"请选择运行班组\" clearable>\n                <el-option\n                  v-for=\"item in deviceNameOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.label\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col> -->\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\" prop=\"bz\">\n              <el-input v-model=\"xlForm.bz\" type=\"textarea\" rows=\"2\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"xlDialogFormVisible = false\">取 消</el-button>\n        <el-button\n          v-if=\"title == '修改线路信息' || title == '新增线路信息'\"\n          type=\"primary\"\n          @click=\"addLineInfo\"\n          >确 定</el-button\n        >\n      </div>\n    </el-dialog>\n    <!-- 线路详情所用弹出框结束-->\n\n    <!-- 杆塔弹出框开始展示设备履历 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"gtDialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"60%\"\n      @close=\"gthandleClose\"\n      v-dialogDrag\n    >\n      <el-tabs v-model=\"gtactiveTabName\">\n        <!--基本信息-->\n        <el-tab-pane label=\"基本信息\" name=\"sbDesc\">\n          <!--          <div class=\"block\" style=\"width: 50%;height: 50%;margin-bottom: 2%;float: right\">\n            <span class=\"demonstration\">设备图片</span>\n            <el-carousel trigger=\"click\" height=\"150px\" indicator-position=\"none\" :interval=\"2000\" type=\"card\">\n              <el-carousel-item v-for=\"(img,index) in imgList\" :key=\"index\">\n                <viewer :images=\"imgList\">\n                  <img :src=\"img.url\" width=\"100%\" height=\"100%\"/>\n                </viewer>\n              </el-carousel-item>\n            </el-carousel>\n          </div>-->\n          <el-form\n            :model=\"gtForm\"\n            ref=\"gtForm\"\n            :disabled=\"gtshow\"\n            :rules=\"gtRules\"\n            label-width=\"130px\"\n          >\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔编号\" prop=\"gtbh\">\n                  <el-input\n                    v-model=\"gtForm.gtbh\"\n                    placeholder=\"请输入杆塔编号\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔名称\" prop=\"gtmc\">\n                  <el-input\n                    v-model=\"gtForm.gtmc\"\n                    placeholder=\"请输入杆塔名称\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"所属线路\" prop=\"lineName\">\n                  <el-input\n                    v-model=\"gtForm.lineName\"\n                    placeholder=\"请输入所属线路\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔材质\" prop=\"gtcz\">\n                  <el-select\n                    v-model=\"gtForm.gtcz\"\n                    placeholder=\"请选择杆塔材质\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in gtczOptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"运行班组\" prop=\"yxbz\">\n                  <el-select\n                    v-model=\"gtForm.yxbz\"\n                    placeholder=\"请选择运行班组\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in deviceNameOptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.label\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"所属部门\" prop=\"ssbm\">\n                  <el-input\n                    v-model=\"gtForm.ssbm\"\n                    placeholder=\"请输入所属部门\"\n                    disabled\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"电压等级\" prop=\"dydj\">\n                  <el-select\n                    v-model=\"gtForm.dydj\"\n                    placeholder=\"请选择电压等级\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in gtoptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔型式\" prop=\"gtxs\">\n                  <el-select\n                    v-model=\"gtForm.gtxs\"\n                    :placeholder=\"gtshow ? '' : '请选择杆塔型式'\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in gtxsOptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔性质\" prop=\"gtNature\">\n                  <el-select\n                    v-model=\"gtForm.gtNature\"\n                    placeholder=\"请选择杆塔性质\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in gtxzList\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item\n                  label=\"杆塔形状\"\n                  prop=\"gtxz\"\n                  v-if=\"this.gtForm.dydj !== '6kV'\"\n                >\n                  <el-select\n                    v-model=\"gtForm.gtxz\"\n                    placeholder=\"请选择杆塔形状\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in gtxzOptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"相序\" prop=\"xx\">\n                  <el-select\n                    v-model=\"gtForm.xx\"\n                    :placeholder=\"gtshow ? '' : '请选择相序'\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in xxOptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\" v-if=\"this.gtForm.dydj !== '6kV'\">\n                <el-form-item label=\"杆塔呼称高(m)\" prop=\"gthcg\">\n                  <el-input\n                    v-model=\"gtForm.gthcg\"\n                    type=\"number\"\n                    placeholder=\"请输入杆塔呼称高\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔高度(m)\" prop=\"towerHeight\">\n                  <el-input\n                    v-model=\"gtForm.towerHeight\"\n                    type=\"number\"\n                    placeholder=\"请输入杆塔高度\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n\n              <el-col :span=\"8\">\n                <el-form-item\n                  label=\"至上基塔档距(m)\"\n                  prop=\"zsjtdj\"\n                  v-if=\"this.gtForm.dydj !== '6kV'\"\n                >\n                  <el-input\n                    v-model=\"gtForm.zsjtdj\"\n                    type=\"number\"\n                    placeholder=\"请输入至上基塔档距\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"导线排列方式\" prop=\"dxplfs\">\n                  <el-select\n                    v-model=\"gtForm.dxplfs\"\n                    :placeholder=\"gtshow ? '' : '请选择导线排列方式'\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in dxplOptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"是否同杆并架\" prop=\"sftgbj\">\n                  <el-select\n                    v-model=\"gtForm.sftgbj\"\n                    :placeholder=\"gtshow ? '' : '请选择是否同杆并架'\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in sfOptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔规格型号\" prop=\"gtggxh\">\n                  <el-input\n                    v-model=\"gtForm.gtggxh\"\n                    :placeholder=\"gtshow ? '' : '请输入杆塔规格型号'\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"坐标经度\" prop=\"jd\">\n                  <el-input\n                    v-model=\"gtForm.jd\"\n                    placeholder=\"请输入坐标经度\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"坐标纬度\" prop=\"wd\">\n                  <el-input\n                    v-model=\"gtForm.wd\"\n                    placeholder=\"请输入坐标纬度\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"同杆架设回路数\" prop=\"tgjshls\">\n                  <el-input\n                    v-model=\"gtForm.tgjshls\"\n                    :placeholder=\"gtshow ? '' : '请输入同杆架设回路数'\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"同杆线路位置\" prop=\"tgxlwz\">\n                  <el-input\n                    v-model=\"gtForm.tgxlwz\"\n                    :placeholder=\"gtshow ? '' : '请输入同杆线路位置'\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item\n                  label=\"是否换相\"\n                  prop=\"sfhx\"\n                  v-if=\"this.gtForm.dydj !== '6kV'\"\n                >\n                  <el-select\n                    v-model=\"gtForm.sfhx\"\n                    placeholder=\"请选择是否换相\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in sfOptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"投运日期\" class=\"add_sy_tyrq\" prop=\"tyrq\">\n                  <el-date-picker\n                    v-model=\"gtForm.tyrq\"\n                    type=\"date\"\n                    placeholder=\"选择日期\"\n                    format=\"yyyy-MM-dd\"\n                    value-format=\"yyyy-MM-dd\"\n                  >\n                  </el-date-picker>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"接地体材料\" prop=\"jdtcl\">\n                  <el-select\n                    v-model=\"gtForm.jdtcl\"\n                    :placeholder=\"gtshow ? '' : '请选择接地体材料'\"\n                    clearable\n                  >\n                    <el-option\n                    v-for=\"item in jdtclOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                    />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"导线型号\" prop=\"dxxh\">\n                  <el-input\n                    v-model=\"gtForm.dxxh\"\n                    :placeholder=\"gtshow ? '' : '请输入导线型号'\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item\n                  label=\"地线型号\"\n                  prop=\"jdxh\"\n                  v-if=\"this.gtForm.dydj !== '6kV'\"\n                >\n                  <el-input\n                    v-model=\"gtForm.jdxh\"\n                    placeholder=\"请输入地线型号\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"设备状态\" prop=\"sbzk\">\n                  <el-select\n                    v-model=\"gtForm.sbzt\"\n                    :placeholder=\"gtshow ? '' : '请选择设备状况'\"\n                  >\n                    <el-option\n                      v-for=\"item in gtsbzt\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔排序\" prop=\"gtnum\">\n                  <el-input\n                    v-model=\"gtForm.gtnum\"\n                    type=\"number\"\n                    placeholder=\"请输入杆塔排序\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"电缆型号\" prop=\"dlxh\">\n                  <el-input\n                    v-model=\"gtForm.dlxh\"\n                    :placeholder=\"gtshow ? '' : '请输入电缆型号'\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-form-item label=\"备注\" prop=\"bz\">\n                <el-input\n                  v-model=\"gtForm.bz\"\n                  type=\"textarea\"\n                  :row=\"2\"\n                  :placeholder=\"gtshow ? '' : '备注'\"\n                ></el-input>\n              </el-form-item>\n            </el-row>\n\n            <el-row :gutter=\"20\">\n              <el-form-item\n                label=\"已上传图片：\"\n                prop=\"attachment\"\n                v-if=\"gtForm.attachment.length > 0\"\n                id=\"pic_form\"\n              >\n                <el-col\n                  :span=\"24\"\n                  v-for=\"(item, index) in gtForm.attachment\"\n                  style=\"margin-left: 0\"\n                >\n                  <el-form-item :label=\"(index + 1).toString()\">\n                    {{ item.fileOldName }}\n                    <el-button\n                      v-if=\"!show\"\n                      type=\"error\"\n                      size=\"mini\"\n                      @click=\"deleteFileById(item.fileId)\"\n                      >删除</el-button\n                    >\n                  </el-form-item>\n                </el-col>\n              </el-form-item>\n            </el-row>\n            <el-row :gutter=\"20\">\n              <el-form-item label=\"上传图片：\" v-if=\"!show\">\n                <el-upload\n                  list-type=\"picture-card\"\n                  class=\"upload-demo\"\n                  accept=\".jpg,.png\"\n                  ref=\"upload\"\n                  :headers=\"upHeader\"\n                  action=\"/isc-api/file/upload\"\n                  :before-upload=\"beforeUpload\"\n                  :data=\"uploadData\"\n                  single\n                  :auto-upload=\"false\"\n                  multiple\n                >\n                  <el-button slot=\"trigger\" type=\"primary\">选取文件</el-button>\n                  <div slot=\"tip\" class=\"el-upload__tip\">\n                    只能上传jpg/png文件\n                  </div>\n                </el-upload>\n              </el-form-item>\n            </el-row>\n          </el-form>\n        </el-tab-pane>\n        <!--设备履历-->\n        <el-tab-pane label=\"设备履历\" name=\"sbRecord\">\n          <el-tabs v-model=\"sbllDescTabName\" type=\"card\">\n            <el-tab-pane label=\"隐患记录\" name=\"qxjl\">\n              <el-table\n                stripe\n                border\n                v-loading=\"loading\"\n                :data=\"sbllqxjlList\"\n                max-height=\"550\"\n              >\n                <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n                <el-table-column\n                  label=\"所属线路\"\n                  align=\"center\"\n                  prop=\"lineName\"\n                />\n                <el-table-column\n                  label=\"杆塔号\"\n                  align=\"center\"\n                  prop=\"gth\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"设备类型\"\n                  align=\"center\"\n                  prop=\"sblx\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"隐患性质\"\n                  align=\"center\"\n                  prop=\"qxxz\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"电压等级\"\n                  align=\"center\"\n                  prop=\"dydj\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column label=\"设备型号\" align=\"center\" prop=\"sbxh\" />\n                <el-table-column\n                  label=\"生产厂家\"\n                  align=\"center\"\n                  prop=\"sccj\"\n                  :show-overflow-tooltip=\"true\"\n                />\n              </el-table>\n            </el-tab-pane>\n            <el-tab-pane label=\"状态变更记录\" name=\"ztbgjl\">\n              <comp-table\n                :table-and-page-info=\"gtresumPageInfo\"\n                @getMethod=\"getResumList\"\n                @update:multipleSelection=\"handleSelectionChange\"\n                height=\"30vh\"\n              />\n            </el-tab-pane>\n          </el-tabs>\n        </el-tab-pane>\n      </el-tabs>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"gtDialogFormVisible = false\">取 消</el-button>\n        <el-button\n          v-if=\"title == '修改杆塔信息' || title == '新增杆塔信息'\"\n          type=\"primary\"\n          @click=\"addGtInfo\"\n          >确 定</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <!-- 杆塔弹出框开始展示设备履历 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"sbDialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"60%\"\n      @close=\"handleClose\"\n    >\n      <el-tabs v-model=\"activeTabName\">\n        <el-tab-pane label=\"基本信息\" name=\"sbDesc\">\n          <el-form\n            :model=\"jbxxForm\"\n            ref=\"jbxxForm\"\n            :disabled=\"show\"\n            :rules=\"rules\"\n            label-width=\"130px\"\n          >\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-form-item label=\"所属线路\" prop=\"lineName\">\n                  <el-input\n                    v-model=\"jbxxForm.lineName\"\n                    placeholder=\"请输入所属线路\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"所属杆塔\" prop=\"gtmc\">\n                  <el-input\n                    v-model=\"jbxxForm.gtmc\"\n                    placeholder=\"请输入所属杆塔\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"设备类型\" prop=\"sbflbm\">\n                  <el-select\n                    v-model=\"jbxxForm.sbflbm\"\n                    placeholder=\"请输入选择设备类型\"\n                    @change=\"showParams\"\n                  >\n                    <el-option\n                      v-for=\"item in sblxOptionsDataSelected\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"设备名称\" prop=\"sbmc\">\n                  <el-input\n                    v-model=\"jbxxForm.sbmc\"\n                    placeholder=\"请输入设备名称\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"投运日期\" prop=\"tyrq\">\n                  <el-date-picker\n                    v-model=\"jbxxForm.tyrq\"\n                    type=\"date\"\n                    placeholder=\"选择日期\"\n                    format=\"yyyy-MM-dd\"\n                    value-format=\"yyyy-MM-dd\"\n                  >\n                  </el-date-picker>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"规格型号\" prop=\"ggxh\">\n                  <el-input\n                    v-model=\"jbxxForm.ggxh\"\n                    :placeholder=\"show ? '' : '请选择规格型号'\"\n                  >\n                  </el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"状态\" prop=\"yxzt\">\n                  <el-select v-model=\"jbxxForm.yxzt\" placeholder=\"请选择状态\">\n                    <el-option\n                      v-for=\"item in sbzt\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"生产厂家\" prop=\"sccj\">\n                  <el-input\n                    v-model=\"jbxxForm.sccj\"\n                    :placeholder=\"show ? '' : '请输入生产厂家'\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row :gutter=\"20\">\n              <el-col :span=\"24\">\n                <el-form-item label=\"备注\" prop=\"bz\">\n                  <el-input\n                    v-model=\"jbxxForm.bz\"\n                    type=\"textarea\"\n                    rows=\"2\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </el-form>\n        </el-tab-pane>\n        <el-tab-pane label=\"技术参数\" name=\"sbjscs\">\n          <el-form :model=\"jscsForm\" label-width=\"130px\">\n            <el-col :span=\"8\" v-for=\"item in jscsLabelList\">\n              <el-form-item\n                :label=\"\n                  item.dw != '' ? item.label + '(' + item.dw + ')' : item.label\n                \"\n              >\n                <el-input\n                  v-if=\"item.type === 'input'\"\n                  v-model=\"jscsForm[item.jscsbm]\"\n                  placeholder=\"\"\n                >\n                </el-input>\n                <el-select\n                  v-if=\"item.type === 'select'\"\n                  v-model=\"jscsForm[item.jscsbm]\"\n                  placeholder=\"\"\n                >\n                  <el-option\n                    v-for=\"(childItem, key) in item.options\"\n                    :key=\"key\"\n                    :label=\"childItem.label\"\n                    :value=\"childItem.value\"\n                    :disabled=\"childItem.disabled\"\n                    style=\"display: flex; align-items: center;\"\n                    clearable\n                  >\n                  </el-option>\n                </el-select>\n                <el-date-picker\n                  v-if=\"item.type === 'date'\"\n                  v-model=\"jscsForm[item.jscsbm]\"\n                  type=\"date\"\n                  placeholder=\"选择日期\"\n                  value-format=\"yyyy-MM-dd\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-form>\n        </el-tab-pane>\n        <el-tab-pane label=\"设备履历\" name=\"sbRecord\">\n          <el-tabs v-model=\"sbllDescTabName\" type=\"card\">\n            <el-tab-pane label=\"缺陷记录\" name=\"qxjl\">\n              <el-table\n                stripe\n                border\n                v-loading=\"loading\"\n                :data=\"sbllqxjlList\"\n                max-height=\"550\"\n              >\n                <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n                <el-table-column\n                  label=\"所属线路\"\n                  align=\"center\"\n                  prop=\"lineName\"\n                />\n                <el-table-column\n                  label=\"杆塔号\"\n                  align=\"center\"\n                  prop=\"gth\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"设备类型\"\n                  align=\"center\"\n                  prop=\"sblx\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"缺陷性质\"\n                  align=\"center\"\n                  prop=\"qxxz\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"电压等级\"\n                  align=\"center\"\n                  prop=\"dydj\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column label=\"设备型号\" align=\"center\" prop=\"sbxh\" />\n                <el-table-column\n                  label=\"生产厂家\"\n                  align=\"center\"\n                  prop=\"sccj\"\n                  :show-overflow-tooltip=\"true\"\n                />\n              </el-table>\n            </el-tab-pane>\n            <el-tab-pane label=\"状态变更记录\" name=\"ztbgjl\">\n              <comp-table\n                :table-and-page-info=\"resumPageInfo\"\n                @getMethod=\"getResumList\"\n                @update:multipleSelection=\"handleSelectionChange\"\n                height=\"30vh\"\n              />\n            </el-tab-pane>\n          </el-tabs>\n        </el-tab-pane>\n      </el-tabs>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"handleClose\">取 消</el-button>\n        <el-button\n          v-if=\"title == '修改输电设备信息' || title == '新增输电设备信息'\"\n          type=\"primary\"\n          @click=\"submit\"\n          class=\"pmyBtn\"\n          >确 定</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <!-- 修改设备状态变更-->\n    <el-dialog\n      title=\"设备状态变更\"\n      :visible.sync=\"dialogVisible\"\n      width=\"30%\"\n      append-to-body\n    >\n      <el-form :inline=\"true\" label-width=\"100px\" class=\"qxlr_dialog_insert\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"设备状态:\">\n              <el-select v-model=\"updateList.sbzt\">\n                <el-option\n                  v-for=\"item in sbzt\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitStatus\">确 定</el-button>\n      </span>\n    </el-dialog>\n\n    <el-dialog\n      :title=\"ExcelImportTitle\"\n      :visible.sync=\"openExcelDialog\"\n      width=\"700px\"\n      :close-on-click-modal=\"false\"\n      v-dialogDrag\n    >\n      <el-form label-position=\"right\" label-width=\"180px\">\n        <el-row>\n          <el-col :span=\"17\">\n            <el-form-item label=\"上传文件\" prop=\"fileName\">\n              <el-input\n                v-model=\"fileName\"\n                :readonly=\"true\"\n                placeholder=\"请选择文件\"\n                style=\"width:200px;\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-upload\n              action=\"/manager-api/tower/importExcel\"\n              ref=\"upload\"\n              :data=\"uploadData\"\n              accept=\".xls,.xlsx\"\n              :limit=\"1\"\n              :file-list=\"fileList\"\n              :auto-upload=\"false\"\n              :on-success=\"uploadSuccess\"\n              :on-change=\"handleChange\"\n              :on-remove=\"handleRemove\"\n              :headers=\"upHeader\"\n              :on-exceed=\"handleExceed\"\n            >\n              <el-button slot=\"trigger\" type=\"primary\" plain\n                >选取文件</el-button\n              >\n              <div\n                slot=\"tip\"\n                class=\"el-upload__tip\"\n                style=\"width: 220px;color: red\"\n              >\n                只能上传excel文件，且不超过100MB\n              </div>\n            </el-upload>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\" style=\"margin-top: 30px\">\n        <el-button type=\"primary\" @click=\"submitExcelForm\" :loading=\"isloading\"\n          >确 定</el-button\n        >\n        <el-button @click=\"cancelImport\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"addTowerBatchDialogFormVisible\"\n      width=\"50%\"\n      v-dialogDrag\n    >\n      <el-form\n        label-width=\"120px\"\n        ref=\"addTowerBatchForm\"\n        :model=\"addTowerBatchForm\"\n        :rules=\"addTowerBatchRules\"\n      >\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"towerStartNum\" label=\"杆塔起始编号\">\n              <el-input-number\n                :min=\"1\"\n                :max=\"99\"\n                style=\"width: 100%\"\n                v-model=\"addTowerBatchForm.towerStartNum\"\n                placeholder=\"请输入杆塔起始编号\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"towerEndNum\" label=\"杆塔结束编号\">\n              <el-input-number\n                :min=\"this.addTowerBatchForm.towerStartNum\"\n                :max=\"99\"\n                style=\"width: 100%\"\n                v-model=\"addTowerBatchForm.towerEndNum\"\n                placeholder=\"请输入杆塔结束编号\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"towerNamePrefix\" label=\"杆塔名称前缀\">\n              <el-input\n                style=\"width: 100%\"\n                v-model=\"addTowerBatchForm.towerNamePrefix\"\n                placeholder=\"请输入杆塔名称前缀\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"towerNameSuffix\" label=\"杆塔名称后缀\">\n              <el-input\n                style=\"width: 100%\"\n                v-model=\"addTowerBatchForm.towerNameSuffix\"\n                placeholder=\"请输入杆塔名称后缀\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"towerNameLinkFlag\" label=\"杆塔名称连接符\">\n              <el-input\n                style=\"width: 100%\"\n                v-model=\"addTowerBatchForm.towerNameLinkFlag\"\n                placeholder=\"请输入杆塔名称连接符\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"towerNumberPrefix\" label=\"杆塔编号前缀\">\n              <el-input\n                style=\"width: 100%\"\n                v-model=\"addTowerBatchForm.towerNumberPrefix\"\n                placeholder=\"请输入杆塔编号前缀\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"towerNumberSuffix\" label=\"杆塔编号后缀\">\n              <el-input\n                style=\"width: 100%\"\n                v-model=\"addTowerBatchForm.towerNumberSuffix\"\n                placeholder=\"请输入编号后缀\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"lineName\" label=\"杆塔所属线路\">\n              <el-input\n                disabled\n                style=\"width: 100%\"\n                v-model=\"addTowerBatchForm.lineName\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <!-- <el-form-item prop=\"towerNameExample\" label=\"杆塔名称生成示例: \">\n              <el-input\n                disabled\n                style=\"width: 100%\"\n                v-model=\"towerNameExample\"\n              />\n            </el-form-item> -->\n            <h2>杆塔名称生成示例:{{ towerNameExample }}</h2>\n          </el-col>\n          <el-col :span=\"12\">\n            <!-- <el-form-item prop=\"towerNumberExample\" label=\"杆塔编号生成示例: \">\n              <el-input\n                disabled\n                style=\"width: 100%\"\n                v-model=\"towerNumberExample\"\n              />\n            </el-form-item> -->\n            <h2>杆塔编号生成示例:{{ towerNumberExample }}</h2>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"addTowerBatchDialogFormVisible = false\"\n          >关 闭</el-button\n        >\n        <el-button type=\"primary\" @click=\"saveTowerBatchButton\"\n          >确 认</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <el-dialog title=\"支线维护\" :visible.sync=\"showZxwh\" width=\"60%\" v-dialogDrag @close=\"closeZxwhFun\" v-if=\"showZxwh\">\n      <zxwh :xl-data=\"xlData\"></zxwh>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  getList,\n  getResumDataList,\n  getTreeList,\n  remove,\n  saveOrUpdate,\n  updateStatus,\n  adddwzyfstz,\n  exportExcel,\n  importExcel\n} from \"@/api/dagangOilfield/asset/sdsb\";\nimport {\n  getListxl,\n  saveOrUpdatexl,\n  xlremove\n} from \"@/api/dagangOilfield/asset/sdxl\";\nimport {\n  getListgt,\n  saveOrUpdategt,\n  gtremove,\n  saveTowerBatch\n} from \"@/api/dagangOilfield/asset/sdgt\";\nimport {\n  getParamDataList,\n  getParamsValue,\n  saveParamValue\n} from \"@/api/dagangOilfield/asset/parameters\";\nimport {\n  getSblxDataListSelected,\n  getOrganizationSelected\n} from \"@/api/dagangOilfield/asset/bdsbtz\";\nimport { getListTZ } from \"@/api/dagangOilfield/bzgl/bzgfgl/bzgfgl\";\nimport { getToken } from \"@/utils/auth\";\nimport zxwh from \"@/views/dagangOilfield/dwzygl/sdsbgl/zxwh\";\nimport {deleteById} from \"@/api/tool/file\";\nimport {getDictTypeData} from \"@/api/system/dict/data\";\n\nexport default {\n  name: \"qxbzk\",\n  components: {zxwh},\n  data() {\n    var validateTowerEndNum = (rule, value, callback) => {\n      if (!this.addTowerBatchForm.towerEndNum) {\n        callback(new Error(\"不能为空\"));\n      }\n      if (\n        this.addTowerBatchForm.towerEndNum <\n        this.addTowerBatchForm.towerStartNum\n      ) {\n        callback(new Error(\"杆塔结束编号不能小于起始编号\"));\n      } else {\n        callback();\n      }\n    };\n    return {\n      ids: [],\n\n      icons: {\n        1: \"categoryTreeIcons\",\n        2: \"tableIcon\",\n        3: \"classIcon\",\n        4: \"classIcon2\"\n      },\n      params: {\n        pageNum: 1,\n        pageSize: 10,\n      },\n      resumeQuery: {\n        foreignNum: undefined,\n        sblx: undefined\n      },\n      updateList: {\n        sbzt: \"\",\n        objId: \"\"\n      },\n      dialogVisible: false,\n      options: [\n        {\n          value: \"110kV\",\n          label: \"110kV\"\n        },\n        {\n          value: \"35kV\",\n          label: \"35kV\"\n        },\n        {\n          value: \"10kV\",\n          label: \"10kV\"\n        },\n        {\n          value: \"6kV\",\n          label: \"6kV\"\n        }\n      ],\n      sbzt: [\n        {\n          value: \"在运\",\n          label: \"在运\"\n        },\n        {\n          value: \"停运\",\n          label: \"停运\"\n        },\n        {\n          value: \"报废\",\n          label: \"报废\"\n        }\n      ],\n\n      //电压等级下拉框\n      voltageLevelListSelected: [],\n      deviceNameOptions: [],\n      sbfilterInfo: {\n        data: {\n          ssxl: [],\n          ssgs: [],\n          sbzt: \"\",\n          yxbz: []\n        },\n        fieldList: [\n          { label: \"所属杆塔名称\", type: \"input\", value: \"gtmc\" },\n          { label: \"设备类型\", type: \"input\", value: \"sblxmc\" },\n          { label: \"设备名称\", type: \"input\", value: \"sbmc\" },\n          {\n            label: \"运行状态\",\n            type: \"select\",\n            value: \"yxzt\",\n            options: [\n              { label: \"在运\", value: \"110kV\" },\n              { label: \"停运\", value: \"35kV\" }\n            ]\n          },\n          {\n            label: \"投运日期\",\n            type: \"date\",\n            value: \"tyrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"生产厂家\", type: \"input\", value: \"sscj\" }\n        ]\n      },\n\n      tableAndPageInfo3: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"lineName\", label: \"所属线路\", minWidth: \"120\" },\n          { prop: \"gtmc\", label: \"所属杆塔名称\", minWidth: \"120\" },\n          { prop: \"sblxmc\", label: \"设备类型\", minWidth: \"120\" },\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"180\" },\n          { prop: \"ggxh\", label: \"规格型号\", minWidth: \"180\" },\n          { prop: \"yxzt\", label: \"运行状态\", minWidth: \"120\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"250\" },\n          { prop: \"sccj\", label: \"生产厂家\", minWidth: \"140\" }\n          /*{\n            fixed:\"right\",\n            prop: 'operation',\n            label: '操作',\n            minWidth: '130px',\n            style: {display: 'block'},\n            operation: [\n              {name: '修改', clickFun: this.updateRow},\n              {name: '详情', clickFun: this.detailsInfo}\n            ]\n          },*/\n        ]\n      },\n\n      resumPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"foreignNum\", label: \"设备名称\", minWidth: \"120\" },\n          { prop: \"sblx\", label: \"设备类型\", minWidth: \"180\" },\n          { prop: \"bglx\", label: \"变更类型\", minWidth: \"120\" },\n          { prop: \"ms\", label: \"描述\", minWidth: \"250\" },\n          { prop: \"bgr\", label: \"变更人\", minWidth: \"140\" },\n          { prop: \"bgsj\", label: \"变更时间\", minWidth: \"140\" }\n        ]\n      },\n\n      //杆塔挂接设备tab页\n      gtgjsbTabName: \"jc\",\n      //杆塔详情弹出框\n      sbDialogFormVisible: false,\n      //设备履历状态变更记录\n      sbllztbgjlList: [],\n      //设备履历缺陷记录数据集合\n      sbllqxjlList: [\n        /*{\n          ssgs: '110kVXXX线路',\n          gth: '001#',\n          sblx: '杆塔',\n          qxxz: '严重',\n          dydj: '35kV',\n          sbxh: 'XXX型号',\n          sccj: 'XXX厂家'\n        }, {\n          ssgs: '110kVXXX线路',\n          gth: '001#',\n          sblx: '杆塔',\n          qxxz: '严重',\n          dydj: '35kV',\n          sbxh: 'XXX型号',\n          sccj: 'XXX厂家'\n        }, {\n          ssgs: '110kVXXX线路',\n          gth: '001#',\n          sblx: '杆塔',\n          qxxz: '严重',\n          dydj: '35kV',\n          sbxh: 'XXX型号',\n          sccj: 'XXX厂家'\n        }, {\n          ssgs: '110kVXXX线路',\n          gth: '001#',\n          sblx: '杆塔',\n          qxxz: '严重',\n          dydj: '35kV',\n          sbxh: 'XXX型号',\n          sccj: 'XXX厂家'\n        }, {\n          ssgs: '110kVXXX线路',\n          gth: '001#',\n          sblx: '杆塔',\n          qxxz: '严重',\n          dydj: '35kV',\n          sbxh: 'XXX型号',\n          sccj: 'XXX厂家'\n        }*/\n      ],\n      //设备履历试验记录数据\n      sblvsyjlList: [\n        /* {\n          syzy: '带电',\n          syxz: '例行试验',\n          symc: 'XXXXX',\n          gzdd: 'XXX平台',\n          sysb: '主变压器',\n          sybg: '',\n          tq: '晴',\n          syrq: '2022-01-01',\n          lrr: '张三',\n          syjl: 'XXXXX'\n        }, {\n          syzy: '带电',\n          syxz: '例行试验',\n          symc: 'XXXXX',\n          gzdd: 'XXX平台',\n          sysb: '主变压器',\n          sybg: '',\n          tq: '晴',\n          syrq: '2022-01-01',\n          lrr: '张三',\n          syjl: 'XXXXX'\n        }, {\n          syzy: '带电',\n          syxz: '例行试验',\n          symc: 'XXXXX',\n          gzdd: 'XXX平台',\n          sysb: '主变压器',\n          sybg: '',\n          tq: '晴',\n          syrq: '2022-01-01',\n          lrr: '张三',\n          syjl: 'XXXXX'\n        }, {\n          syzy: '带电',\n          syxz: '例行试验',\n          symc: 'XXXXX',\n          gzdd: 'XXX平台',\n          sysb: '主变压器',\n          sybg: '',\n          tq: '晴',\n          syrq: '2022-01-01',\n          lrr: '张三',\n          syjl: 'XXXXX'\n        }, {\n          syzy: '带电',\n          syxz: '例行试验',\n          symc: 'XXXXX',\n          gzdd: 'XXX平台',\n          sysb: '主变压器',\n          sybg: '',\n          tq: '晴',\n          syrq: '2022-01-01',\n          lrr: '张三',\n          syjl: 'XXXXX'\n        }, {\n          syzy: '带电',\n          syxz: '例行试验',\n          symc: 'XXXXX',\n          gzdd: 'XXX平台',\n          sysb: '主变压器',\n          sybg: '',\n          tq: '晴',\n          syrq: '2022-01-01',\n          lrr: '张三',\n          syjl: 'XXXXX'\n        }*/\n      ],\n      //设备履历tab页\n      sbllDescTabName: \"qxjl\",\n      show: false,\n      //设备基本信息表单\n      jbxxForm: {\n        objId: undefined,\n        ssxl: \"\",\n        ssgtbh: \"\"\n      },\n      //设备详情页底部确认取消按钮控制\n      sbCommitDialogCotrol: true,\n      //弹出框tab页\n      activeTabName: \"sbDesc\",\n      //变电站展示\n      bdzShowTable: true,\n      //间隔展示\n      jgShowTable: false,\n      //设备展示\n      sbShowTable: false,\n      //设备弹出框\n      dialogFormVisible: false,\n      //变电站添加按钮弹出框\n      bdzDialogFormVisible: false,\n      //间隔添加按钮弹出框\n      jgDialogFormVisible: false,\n      //弹出框表单\n      form: {},\n\n      loading: false,\n      //组织树\n      treeOptions: [],\n\n      selectRows: [],\n      //变电站挂接数据\n      newTestData: [],\n\n      //删除是否可用\n      multipleSensor: true,\n      //查询参数\n      /*       queryParams: {\n               pageNum: 1,\n               pageSize: 10,\n               roleKey: '',\n               roleName: '',\n               status: '',\n             },*/\n      showSearch: true,\n      //ssgtbh sbdm sbmc sbflbm bgr tyrq zcxz yxzt dydj sccj ccbh ccrq ggxh\n      rules: {\n        // ssgtbh:[{required:true,message:'请输入所属杆塔',trigger:'blur'}],\n        // sbdm:[{required:true,message:'请输入设备代码',trigger:'blur'}],\n        sbmc: [{ required: true, message: \"请输入设备名称\", trigger: \"blur\" }],\n        sbflbm: [\n          { required: true, message: \"请选择设备类型\", trigger: \"change\" }\n        ]\n        // bgr:[{required:true,message:'请输入保管人',trigger:'blur'}],\n        // yxzt:[{required:true,message:'请选择状态',trigger:'change'}],\n        // dydj:[{required:true,message:'请选择电压等级',trigger:'change'}],\n        // sccj:[{required:true,message:'请输入生产厂家',trigger:'blur'}],\n        // ccbh:[{required:true,message:'请输入出场编号',trigger:'blur'}]\n      },\n\n      jscsLabelList: [],\n      //技术参数绑定\n      jscsForm: {},\n      paramQuery: {\n        sblxbm: undefined\n      },\n      sblxOptionsDataSelected: {},\n      xltzData: true,\n      sbtzData: false,\n      gttzData: false,\n      title: \"\",\n      //线路数据相关\n      tableAndPageInfo1: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"lineName\", label: \"线路名称\", minWidth: \"120\" },\n          { prop: \"dydj\", label: \"电压等级\", minWidth: \"120\" },\n          { prop: \"lineStatus\", label: \"线路状态\", minWidth: \"120\" },\n          { prop: \"lineType\", label: \"线路类型\", minWidth: \"120\" },\n          // {prop: 'sfzgx', label: '是否主干线', minWidth: '140'},\n          { prop: \"totalLength\", label: \"线路全长(KM)\", minWidth: \"120\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"120\" }\n          /*{\n            fixed: \"right\",\n            prop: 'operation',\n            label: '操作',\n            minWidth: '130px',\n            style: {display: 'block'},\n            operation: [\n              {name: '修改', clickFun: this.updateRow1},\n              {name: '详情', clickFun: this.detailsInfo1}\n            ]\n          },*/\n        ]\n      },\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        dydjbm: \"\",\n        lineName: \"\",\n        lineType: \"\",\n        lineStatus: \"\"\n      },\n      //设备弹出框\n      xlDialogFormVisible: false,\n      xlshow: false,\n      //设备基本信息\n      xlForm: {\n        objId: undefined\n      },\n      //线路类型\n      lineTypeOptions: [\n        { label: \"输电线路\", value: \"输电线路\" },\n        { label: \"配电线路\", value: \"配电线路\" }\n      ],\n      //线路状态\n      xlztOptions: [\n        { label: \"在运\", value: \"在运\" },\n        { label: \"停运\", value: \"停运\" }\n      ],\n      xloptions: [\n        {\n          value: \"110\",\n          label: \"110kV\"\n        },\n        {\n          value: \"35\",\n          label: \"35kV\"\n        },\n        {\n          value: \"10\",\n          label: \"10kV\"\n        },\n        {\n          value: \"6\",\n          label: \"6kV\"\n        }\n      ],\n      xlfilterInfo: {\n        data: {\n          dydjbm: \"\",\n          lineName: \"\",\n          lineType: \"\",\n          // sfzgx: '',\n          lineStatus: \"\"\n        },\n        fieldList: [\n          { label: \"线路名称\", type: \"input\", value: \"lineName\" },\n          {\n            label: \"线路全长\",\n            type: \"input\",\n            value: \"totalLength\",\n            options: []\n          },\n          {\n            label: \"投运日期\",\n            type: \"date\",\n            format: \"yyyy-MM-dd\",\n            options: []\n          },\n          {\n            label: \"电压等级\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"dydjbm\",\n            options: [\n              { label: \"110kV\", value: \"110\" },\n              { label: \"35kV\", value: \"35\" },\n              { label: \"10kV\", value: \"10\" },\n              { label: \"6kV\", value: \"6\" }\n            ]\n          },\n          {\n            label: \"状态\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"lineStatus\",\n            options: [\n              { label: \"在运\", value: \"在运\" },\n              { label: \"停运\", value: \"停运\" }\n            ]\n          },\n          {\n            label: \"线路类型\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"lineType\",\n            options: [\n              { label: \"输电线路\", value: \"输电线路\" },\n              { label: \"配电线路\", value: \"配电线路\" }\n            ]\n          }\n          // {\n          //   label: '是否主干线',\n          //   type: 'select',\n          //   value: 'sfzgx',\n          //   options: [{label: \"是\", value: \"是\"}, {label: \"否\", value: \"否\"},]\n          // },\n        ]\n      },\n      //查询杆塔参数   杆塔数据相关开始\n      //杆塔详情弹出框\n      gtDialogFormVisible: false,\n      //弹出框tab页\n      gtactiveTabName: \"sbDesc\",\n      //轮播图片\n      imgList: [],\n      xlImgList: [\n        //         {\n        //   fileUrl: require('@/assets/image/bdz.png')\n        // },\n        // {\n        //   fileUrl: require('@/assets/image/bdz.png')\n        // },\n        // {\n        //   fileUrl: require('@/assets/image/bdz.png')\n        // }\n      ],\n      queryGtParam: {\n        sbzt: \"\",\n        lineName: \"\",\n        dydj: \"\",\n        pageNum: 1,\n        pageSize: 10,\n        // mySorts: [{ prop: \"get_number(gtbh) + 1\", asc: true }]\n      },\n      // 文件上传数据\n      // uploadData: {\n      //   type: \"\",\n      //   businessId: undefined,\n      //   lineName : this.lineName\n      // },\n      // 文件上传请求头\n      upHeader: { token: getToken() },\n      //  文件导入弹出框相关\n      ExcelImportTitle: \"Excel导入\",\n      openExcelDialog: false,\n      // 文件上传请求头\n      //文件名\n      fileName: \"\",\n      //上传得文件数组\n      fileList: [],\n      isloading: false,\n      tableAndPageInfo2: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"lineName\", label: \"所属线路\", minWidth: \"120\" },\n          { prop: \"gtmc\", label: \"杆塔名称\", minWidth: \"130\" },\n          { prop: \"gtbh\", label: \"杆塔编号\", minWidth: \"90\",  },\n          { prop: \"dydj\", label: \"电压等级\", minWidth: \"60\" },\n          { prop: \"sbzt\", label: \"设备状态\", minWidth: \"60\" },\n          { prop: \"gtcz\", label: \"杆塔材质\", minWidth: \"70\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"80\" },\n          { prop: \"gtnum\", label: \"杆塔排序\", minWidth: \"70\",  }\n          /*{\n            fixed: 'right',\n            prop: 'operation',\n            label: '操作',\n            minWidth: '130px',\n            style: { display: 'block' },\n            operation: [\n              { name: '状态变更', clickFun: this.updateStatus },\n              { name: '修改', clickFun: this.updateRow2 },\n              { name: '详情', clickFun: this.detailsInfo2 }\n            ]\n          }*/\n        ]\n      },\n      //设备基本信息\n      gtForm: {\n        attachment: [],\n        objId: undefined,\n        gtbh: \"\",\n        ssbm: \"线路分公司\"\n      },\n      gtfilterInfo: {\n        data: {\n          sbzt: \"\",\n          lineName: \"\",\n          dydj: \"\"\n        },\n        fieldList: [\n          {\n            label: \"杆塔名称\",\n            type: \"input\",\n            value: \"gtmc\",\n            options: []\n          },\n          {\n            label: \"杆塔编号\",\n            type: \"input\",\n            value: \"gtbh\",\n            options: []\n          },\n          {\n            label: \"电压等级\",\n            type: \"select\",\n            value: \"dydj\",\n            options: [\n              { label: \"6kV\", value: \"6kV\" },\n              { label: \"10kV\", value: \"10kV\" },\n              {\n                label: \"35kV\",\n                value: \"35kV\"\n              },\n              { label: \"110kV\", value: \"110kV\" }\n            ]\n          },\n          {\n            label: \"设备状态\",\n            type: \"select\",\n            value: \"sbzt\",\n            options: [\n              { label: \"在运\", value: \"在运\" },\n              { label: \"停运\", value: \"停运\" }\n            ]\n          },\n          {\n            label: \"杆塔材质\",\n            type: \"input\",\n            value: \"gtcz\",\n            options: []\n          },\n          {\n            label: \"投运日期\",\n            type: \"date\",\n            value: \"tyrq\",\n            format: \"yyyy-MM-dd\"\n          }\n        ]\n      },\n      gtshow: false,\n      gtoptions: [],\n      gtsbzt: [],\n      //杆塔性质\n      gtxzList: [],\n      //杆塔形状结合\n      gtxzOptions: [],\n      uploadData: {\n        businessId: undefined\n      },\n      gtresumeQuery: {\n        foreignNum: undefined,\n        sblx: undefined\n      },\n      gtresumPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"foreignNum\", label: \"设备名称\", minWidth: \"120\" },\n          { prop: \"sblx\", label: \"设备类型\", minWidth: \"180\" },\n          { prop: \"bglx\", label: \"变更类型\", minWidth: \"120\" },\n          { prop: \"ms\", label: \"描述\", minWidth: \"250\" },\n          { prop: \"bgr\", label: \"变更人\", minWidth: \"140\" },\n          { prop: \"bgsj\", label: \"变更时间\", minWidth: \"140\" }\n        ]\n      },\n      xlRules: {\n        xlbm: [\n          { required: true, message: \"线路编码不能为空\", trigger: \"blur\" }\n        ],\n        lineName: [\n          { required: true, message: \"线路名称不能为空\", trigger: \"blur\" }\n        ],\n        lineType: [\n          { required: true, message: \"线路专业不能为空\", trigger: \"select\" }\n        ],\n        dydjbm: [\n          { required: true, message: \"电压等级不能为空\", trigger: \"select\" }\n        ],\n        totalLength: [\n          { required: true, message: \"线路全长不能为空\", trigger: \"blur\" }\n        ],\n        lineStatus: [\n          { required: true, message: \"线路状态不能为空\", trigger: \"select\" }\n        ]\n      },\n      gtRules: {\n        gtbh: [\n          { required: true, message: \"杆塔编号不能为空\", trigger: \"blur\" }\n        ],\n        gtnum: [\n          { required: true, message: \"杆塔排序不能为空\", trigger: \"blur\" }\n        ],\n        gtmc: [\n          { required: true, message: \"杆塔名称不能为空\", trigger: \"blur\" }\n        ],\n        lineName: [\n          { required: true, message: \"所属线路不能为空\", trigger: \"blur\" }\n        ],\n        gtcz: [\n          { required: true, message: \"杆塔材质不能为空\", trigger: \"select\" }\n        ],\n        yxbz: [\n          { required: true, message: \"运行班组不能为空\", trigger: \"select\" }\n        ],\n        ssbm: [\n          { required: true, message: \"所属部门不能为空\", trigger: \"blur\" }\n        ],\n        dydj: [\n          { required: true, message: \"电压等级不能为空\", trigger: \"select\" }\n        ]\n      },\n      dydjbm: \"\",\n      lineName: \"\",\n      linedIdStore: \"\",\n      gtbhStore: \"\",\n      gtmcStore: \"\",\n      addTowerBatchForm: {\n        towerNamePrefix: \"\",\n        towerNameSuffix: \"\",\n        towerNameLinkFlag: \"\",\n        towerNumberPrefix: \"\",\n        towerNumberSuffix: \"\",\n        towerStartNum: 1\n      },\n      addTowerBatchDialogFormVisible: false,\n      addTowerBatchRules: {\n        lineName: [\n          { required: true, message: \"所属线路不能为空\", trigger: \"blur\" }\n        ],\n        towerNamePrefix: [\n          { required: true, message: \"不能为空\", trigger: \"blur\" }\n        ],\n        towerNameSuffix: [\n          { required: true, message: \"不能为空\", trigger: \"blur\" }\n        ],\n        towerStartNum: [\n          { required: true, message: \"不能为空\", trigger: \"change\" }\n        ],\n        towerEndNum: [\n          { required: true, trigger: \"change\", validator: validateTowerEndNum }\n        ]\n      },\n      xlData:{},//传给支线维护页面的线路数据\n      showZxwh:false,//是否显示支线维护弹框\n      gtczOptions:[],//杆塔材质下拉框\n      gtxsOptions:[],//杆塔形式下拉框\n      xxOptions:[],//相序下拉框\n      dxplOptions:[],//导线排列下拉框\n      sfOptions:[],//是/否\n      jdtclOptions:[],//接地体材料\n    };\n  },\n  watch: {},\n  computed: {\n    towerNumberExample: function() {\n      return (\n        this.addTowerBatchForm.towerNumberPrefix +\n        this.addTowerBatchForm.towerStartNum +\n        this.addTowerBatchForm.towerNumberSuffix\n      );\n    },\n    towerNameExample: function() {\n      return (\n        this.addTowerBatchForm.towerNamePrefix +\n        this.addTowerBatchForm.towerStartNum +\n        this.addTowerBatchForm.towerNameLinkFlag +\n        this.addTowerBatchForm.towerNameSuffix\n      );\n    }\n  },\n  created() {\n    //初始化加载时加载所有变电站信息\n    this.treeList();\n    this.getSblxDataListSelected();\n    this.lineTzData(this.$route.query); //初始请求线路台账\n    this.getOptions();//获取下拉框字典\n  },\n  mounted() {\n    this.getOrganizationSelected();\n  },\n  methods: {\n    async getOptions(){\n      await this.getDydjList();//电压等级\n      await this.getGtczList();//杆塔材质\n      await this.getGtxsList();//杆塔形式\n      await this.getGtxzList();//杆塔性质\n      await this.getGtxzList1();//杆塔形状\n      await this.getGtxxList();//杆塔相序\n      await this.getDxplList();//导线排列\n      await this.getSfList();//是/否\n      await this.getJdtclList();//接地体材料\n      await this.getGtztList();//杆塔状态\n    },\n    getDydjList(){\n      getDictTypeData('gttz-dydj').then(res=>{\n        res.data.forEach(item=>{\n          this.gtoptions.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    getGtczList(){\n      getDictTypeData('gttz-gtcz').then(res=>{\n        res.data.forEach(item=>{\n          this.gtczOptions.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    getGtxsList(){\n      getDictTypeData('gttz-gtxs').then(res=>{\n        res.data.forEach(item=>{\n          this.gtxsOptions.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    getGtxzList(){\n      getDictTypeData('gttz-gtxz').then(res=>{\n        res.data.forEach(item=>{\n          this.gtxzList.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    getGtxzList1(){\n      getDictTypeData('gttz-gtxz1').then(res=>{\n        res.data.forEach(item=>{\n          this.gtxzOptions.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    getGtxxList(){\n      getDictTypeData('gttz-gtxx').then(res=>{\n        res.data.forEach(item=>{\n          this.xxOptions.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    getDxplList(){\n      getDictTypeData('gttz-dxpl').then(res=>{\n        res.data.forEach(item=>{\n          this.dxplOptions.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    getSfList(){\n      getDictTypeData('sys_sf').then(res=>{\n        res.data.forEach(item=>{\n          this.sfOptions.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    getJdtclList(){\n      getDictTypeData('gttz-jdtcl').then(res=>{\n        res.data.forEach(item=>{\n          this.jdtclOptions.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    getGtztList(){\n      getDictTypeData('gttz-gtzt').then(res=>{\n        res.data.forEach(item=>{\n          this.gtsbzt.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    //关闭支线维护弹框\n    closeZxwhFun(){\n      this.xlData = {};\n      this.showZxwh = false;\n    },\n    //支线维护方法\n    zxwhFun(row){\n      this.xlData = {...row};\n      this.showZxwh = true;\n    },\n    getVoltageLeVelList() {\n      this.sbfilterInfo.fieldList.forEach(item => {\n        if (item.value == \"ssgs\") {\n          item.options = [\n            { label: \"110kV\", value: \"110kV\" },\n            { label: \"35kV\", value: \"35kV\" },\n            { label: \"10kV\", value: \"10kV\" },\n            { label: \"6kV\", value: \"6kV\" }\n          ];\n        }\n      });\n    },\n    //获取班组\n    getOrganizationSelected() {\n      let parentId = \"3010\"; //线路分公司\n      getOrganizationSelected({ parentId: parentId }).then(res => {\n        res.data.forEach(item => {\n          this.deviceNameOptions.push({\n            label: item.label,\n            value: item.value.toString()\n          });\n        });\n        this.sbfilterInfo.fieldList.forEach(item => {\n          if (item.value === \"bz\") {\n            item.options = this.deviceNameOptions;\n            return false;\n          }\n        });\n      });\n    },\n    //保存设备基本信息\n    addEquipInfo: function() {\n      this.jbxxForm.sbClassCsValue = this.jscsForm;\n\n      saveOrUpdate(this.jbxxForm).then(res => {\n        if (res.code == \"0000\") {\n          this.$message.success(\"操作成功\");\n          this.sbDialogFormVisible = false;\n          this.getData({ ssgtbh: this.jbxxForm.ssgtbh });\n          return;\n        } else {\n          this.$message.warning(\"操作失败！\");\n        }\n      });\n    },\n    //初始进来发请求线路台账接口\n    lineTzData(params) {\n      this.queryParams = { ...this.queryParams, ...params };\n      const param = { ...this.queryParams, ...params };\n      getListxl(param).then(res => {\n        this.xltzData = true;\n        this.gttzData = this.sbtzData = false;\n        this.tableAndPageInfo1.tableData = res.data.records;\n        this.tableAndPageInfo1.pager.total = res.data.total;\n      });\n    },\n    //图纸查询\n    querySearch(queryString, cb) {\n      let param = {\n        // wjbh: queryString,\n        wjmc: queryString,\n        dydj: this.xlForm.dydjbm,\n        islast: \"1\",\n        wjlx: \"1\",\n        mySorts: [{ prop: \"updateTime\", asc: false }]\n      };\n      getListTZ(param).then(res => {\n        if (res.code === \"0000\") {\n          // 调用 callback 返回建议列表的数据\n          let data = res.data.records;\n          data.forEach(record => {\n            record.value = record.wjbh;\n          });\n          cb(data);\n        }\n      });\n    },\n    handleSelect(item) {\n      this.xlImgList = item.fileList.filter(\n        record => record.fileType !== \"vsd\"\n      );\n    },\n    //杆塔台账\n    async gtTzData(params) {\n      try {\n        this.queryGtParam = {\n          ...this.queryGtParam,\n          ...params,\n          ...{ lineName: this.lineName }\n        };\n        getListgt(this.queryGtParam).then(res => {\n          this.gttzData = true;\n          this.xltzData = this.sbtzData = false;\n          this.tableAndPageInfo2.tableData = res.data.records;\n          this.tableAndPageInfo2.pager.total = res.data.total;\n        });\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    sortChangeTowerData({ column, prop, order }) {\n      if (order) {\n        if (order.indexOf(\"desc\") > -1) {\n          if (prop === \"gtbh\") {\n            this.queryGtParam.mySorts = [\n              { prop: \"get_number(gtbh) + 0\", asc: false }\n            ];\n          } else {\n            this.queryGtParam.mySorts = [{ prop: prop, asc: false }];\n          }\n        } else {\n          if (prop === \"gtbh\") {\n            this.queryGtParam.mySorts = [\n              { prop: \"get_number(gtbh) + 0\", asc: true }\n            ];\n          } else {\n            this.queryGtParam.mySorts = [{ prop: prop, asc: true }];\n          }\n        }\n      } else {\n        this.queryGtParam.mySorts = [{ prop: \"get_number(gtbh)\", asc: true }];\n      }\n      this.gtTzData(this.gtfilterInfo.data);\n    },\n    //设备台账\n    sbTzData(params) {\n      const param = { ...this.params, ...params,...{ ssgtbh: this.gtbhStore } };\n      getList(param).then(res => {\n        this.sbtzData = true;\n        this.gttzData = this.xltzData = false;\n        this.tableAndPageInfo3.tableData = res.data.records;\n        this.tableAndPageInfo3.pager.total = res.data.total;\n      });\n    },\n    //获取列表数据\n    getData: function(params) {\n      if (this.xltzData) {\n        this.lineTzData(params);\n      }\n      if (this.gttzData) {\n        this.gtTzData(params);\n      }\n      if (this.sbtzData) {\n        this.sbTzData(params);\n      }\n    },\n    //线路修改操作\n    updateRow1(row) {\n      this.title = \"修改线路信息\";\n      this.xlForm = { ...row };\n      this.getImgList(row.xlbm);\n      this.xlDialogFormVisible = true;\n      this.xlshow = false;\n    },\n    //线路查看详情\n    detailsInfo1(row) {\n      this.title = \"线路详情\";\n      this.xlForm = { ...row };\n      this.getImgList(row.xlbm);\n      this.xlDialogFormVisible = true;\n      this.xlshow = true;\n    },\n    jumpToTzgl(row) {\n      row;\n    },\n    getImgList(wjbh) {\n      this.xlImgList = [];\n      let param = {\n        wjbh: wjbh,\n        islast: \"1\",\n        wjlx: \"1\"\n      };\n      getListTZ(param).then(res => {\n        if (res.code === \"0000\") {\n          let data = res.data.records[0].fileList.filter(\n            record => record.fileType !== \"vsd\"\n          );\n          this.xlImgList = data;\n        }\n      });\n    },\n    //线路取消弹框\n    resetxlForm() {\n      this.xlForm = {};\n      this.$nextTick(function() {\n        this.$refs[\"xlForm\"].clearValidate();\n      });\n      this.xlDialogFormVisible = false;\n    },\n    //线路弹框内容保存\n    addLineInfo() {\n      let params = {\n        lx: \"输电设备\",\n        mc: this.xlForm.lineName\n      };\n      this.$refs[\"xlForm\"].validate(valid => {\n        if (valid) {\n          saveOrUpdatexl(this.xlForm).then(res => {\n            if (res.code == \"0000\") {\n              //新增成功后发送通知\n              adddwzyfstz(params).then(res => {\n                if (res.code === \"0000\") {\n                }\n              });\n              this.$message.success(\"操作成功,通知已发送\");\n              this.treeList();\n              this.xlDialogFormVisible = false;\n              this.lineTzData();\n              return;\n            } else {\n              this.$message.warning(\"操作失败！\");\n            }\n          });\n        } else {\n          setTimeout(() => {\n            var isError = document.getElementsByClassName(\"is-error\");\n            if (isError[0].querySelector(\"input\")) {\n              isError[0].querySelector(\"input\").focus();\n            } else if (isError[0].querySelector(\"textarea\")) {\n              isError[0].querySelector(\"textarea\").focus();\n            }\n          }, 1);\n          return false;\n        }\n      });\n    },\n    getTableList: function(params) {\n      this.queryGtParam = { ...this.queryGtParam, ...params };\n      const param = { ...this.queryGtParam, ...params };\n      getListgt(param).then(res => {\n        this.tableAndPageInfo2.tableData = res.data.records;\n        this.tableAndPageInfo2.pager.total = res.data.total;\n      });\n    },\n    /**\n     * 表格多选框\n     */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.objId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /**\n     * 删除\n     */\n    xldeleteInfo() {\n      if (this.ids.length != 0) {\n        this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(() => {\n          xlremove(this.ids).then(res => {\n            this.$message({\n              type: \"success\",\n              message: \"删除成功!\"\n            });\n            this.lineTzData();\n          });\n        });\n      } else {\n        this.$message({\n          type: \"info\",\n          message: \"请选择至少一条数据!\"\n        });\n      }\n    },\n    sbdeleteInfo() {\n      if (this.ids.length != 0) {\n        this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(() => {\n          remove(this.ids).then(res => {\n            this.$message({\n              type: \"success\",\n              message: \"删除成功!\"\n            });\n            this.sbTzData();\n          });\n        });\n      } else {\n        this.$message({\n          type: \"info\",\n          message: \"请选择至少一条数据!\"\n        });\n      }\n    },\n    gtdeleteInfo() {\n      if (this.ids.length != 0) {\n        this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(() => {\n          gtremove(this.ids).then(res => {\n            this.$message({\n              type: \"success\",\n              message: \"删除成功!\"\n            });\n            this.gtTzData();\n          });\n        });\n      } else {\n        this.$message({\n          type: \"info\",\n          message: \"请选择至少一条数据!\"\n        });\n      }\n    },\n    //导出excel\n    xlexportExcel() {\n      // if(!this.selectData.length > 0){\n      //   this.$message.warning('请在左侧勾选要导出的数据')\n      //   return\n      // }\n      let fileName = \"输电线路信息表\";\n      let exportUrl = \"/sdxl/exportExcel\";\n      exportExcel(exportUrl, this.queryParams, fileName);\n    },\n    //导出excel\n    gtexportExcel() {\n      // if(!this.selectData.length > 0){\n      //   this.$message.warning('请在左侧勾选要导出的数据')\n      //   return\n      // }\n      let fileName = this.lineName + \"杆塔信息表\";\n      let exportUrl = \"/tower/exportExcel\";\n      exportExcel(exportUrl, this.queryGtParam, fileName);\n    },\n    deleteInfo() {\n      if (this.xltzData) {\n        this.xldeleteInfo();\n      }\n      if (this.gttzData) {\n        this.gtdeleteInfo();\n      }\n      if (this.sbtzData) {\n        this.sbdeleteInfo();\n      }\n    },\n    exportExcel() {\n      if (this.xltzData) {\n        this.xlexportExcel();\n      }\n      if (this.gttzData) {\n        this.gtexportExcel();\n      }\n    },\n    //导入\n    importExcel() {\n      this.openExcelDialog = true;\n      this.fileName = \"\";\n    },\n    /**导入文件提交按钮*/\n    submitExcelForm() {\n      if (this.uploadData.lineName === this.lineName) {\n        this.isloading = true;\n        this.$refs.upload.submit();\n      } else {\n        this.$confirm(\n          \"上传的Excel线路名称与所在页面线路不一致，是否继续上传？\",\n          \"提示\",\n          {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\"\n          }\n        )\n          .then(() => {\n            //再次提交\n            this.isloading = true;\n            this.$refs.upload.submit();\n          })\n          .catch(() => {\n            this.$message({\n              type: \"info\",\n              message: \"已取消上传\"\n            });\n          });\n      }\n    },\n    /**导入文件取消按钮*/\n    cancelImport() {\n      this.isloading = false;\n      this.openExcelDialog = false;\n      //取消时清空上传文件列表\n      this.$refs.upload.clearFiles();\n    },\n    /**上传成功方法*/\n    uploadSuccess(res) {\n      if (res.code == \"0000\") {\n        this.msgSuccess(\"上传成功\");\n        this.fileName = \"\";\n        //清空上传的文件列表\n        this.$refs.upload.clearFiles();\n        this.isloading = false;\n      } else {\n        this.isloading = false;\n        this.msgError(res.msg);\n        this.$refs.upload.clearFiles();\n      }\n      //重新渲染\n      this.gtTzData(this.gtfilterInfo.data);\n      this.openExcelDialog = false;\n    },\n    /**文件改变时调用的函数*/\n    handleChange(file) {\n      this.uploadData.lineName = file.name.substring(\n        0,\n        file.name.indexOf(\"杆\")\n      );\n      this.fileName = \"\";\n      let testFileName = file.name;\n      this.fileName = testFileName;\n    },\n    /** 文件移除时函数*/\n    handleRemove() {\n      this.fileName = \"\";\n      this.msgSuccess(\"移除成功\");\n    },\n    /**文件超出限制时调用*/\n    handleExceed(file, fileList) {\n      this.msgWarning(\"只能添加一个文件，请先删除之前的文件\");\n    },\n    updateStatus(row) {\n      console.log(\"row\", row);\n      this.updateList.sbzt = row.sbzt;\n      this.updateList.objId = row.objId;\n      this.dialogVisible = true;\n    },\n    submitStatus() {\n      console.log(\"this.updateList\", this.updateList);\n      this.$confirm(\"确认将设备状态修改为\" + this.updateList.sbzt + \"?\", \"\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(res => {\n        updateStatus(this.updateList).then(res => {\n          if (res.code == \"0000\") {\n            this.$message.success(\"设备状态已变更！\");\n            this.dialogVisible = false;\n            this.getData();\n          }\n        });\n      });\n    },\n    // 修改操作\n    updateRow: function(row) {\n      this.title = \"修改输电设备信息\";\n      this.technicalParameters(row);\n      this.resumeQuery.foreignNum = row.sbmc;\n      this.resumeQuery.sblx = row.sblx;\n      this.getResumList();\n      this.sbDialogFormVisible = true;\n      this.show = false;\n      this.jbxxForm = row;\n    },\n    //详情\n    detailsInfo: function(row) {\n      this.title = \"输电设备信息\";\n      this.technicalParameters(row);\n      this.resumeQuery.foreignNum = row.sbmc;\n      this.resumeQuery.sblx = row.sblx;\n      this.getResumList();\n      this.sbDialogFormVisible = true;\n      this.show = true;\n      this.jbxxForm = row;\n    },\n    filterReset(val, type) {\n      if (type === \"gt\") {\n        this.queryGtParam = {\n          lineName: this.queryGtParam.lineName,\n          // mySorts: [{ prop: \"get_number(gtbh)\", asc: true }]\n        };\n        this.gtTzData(this.queryGtParam);\n      }\n      this.xlfilterInfo.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n    },\n    //获取左侧树节点\n    treeList() {\n      getTreeList().then(res => {\n        this.treeOptions = res.data;\n      });\n    },\n    handleClose() {\n      this.jbxxForm = {};\n      this.$nextTick(function() {\n        this.$refs[\"jbxxForm\"].clearValidate();\n      });\n\n      this.jscsForm = {};\n      this.$nextTick(function() {\n        this.$refs[\"jscsForm\"].clearValidate();\n      });\n\n      this.sbDialogFormVisible = false;\n    },\n    getResumList(par) {\n      if (this.gttzData) {\n        let params = { ...par, ...this.gtresumeQuery };\n        getResumDataList(params).then(res => {\n          this.gtresumPageInfo.tableData = res.data.records;\n          this.gtresumPageInfo.pager.total = res.data.total;\n        });\n      }\n      if (this.sbtzData) {\n        let params = { ...par, ...this.resumeQuery };\n        getResumDataList(params).then(res => {\n          this.resumPageInfo.tableData = res.data.records;\n          this.resumPageInfo.pager.total = res.data.total;\n        });\n      }\n    },\n    //设备添加按钮\n    sbAddSensorButton() {},\n    //新增按钮\n    AddSensorButton() {\n      if (this.xltzData) {\n        this.title = \"新增线路信息\";\n        this.xlImgList = [];\n        this.xlshow = false;\n        this.xlDialogFormVisible = true;\n      }\n      if (this.gttzData) {\n        if (this.gtForm.lineId != undefined) {\n          this.title = \"新增杆塔信息\";\n          this.imgList = [];\n          this.gtshow = false;\n          this.gtDialogFormVisible = true;\n        } else {\n          this.$message.info(\"请先选择所属线路再新增杆塔\");\n        }\n      }\n      if (this.sbtzData) {\n        if (this.jbxxForm.ssgtbh === \"\" && this.jbxxForm.ssxl === \"\") {\n          this.$message.info(\"请在左侧树选择具体线路或杆塔在尝试\");\n          return;\n        }\n        this.jbxxForm.ssxl = this.linedIdStore\n        this.jbxxForm.lineName = this.lineName\n        this.jbxxForm.ssgtbh = this.gtbhStore\n        this.jbxxForm.gtmc = this.gtmcStore\n        this.title = \"新增输电设备信息\";\n        //不禁用表单输入\n        this.show = false;\n        //打开弹出框\n        this.sbDialogFormVisible = true;\n        //新增时先置空去查询基数参数\n        let row = {};\n        //获取技术参数\n        this.technicalParameters(row);\n      }\n    },\n    AddTowerBatchButton() {\n      if (this.linedIdStore && this.lineName) {\n        this.addTowerBatchForm.lineName = this.lineName;\n        this.$set(this.addTowerBatchForm, \"towerNameLinkFlag\", \"#\");\n        this.$set(this.addTowerBatchForm, \"towerNumberSuffix\", \"#\");\n        this.addTowerBatchForm.towerInfo = {\n          lineId: this.linedIdStore,\n          lineName: this.lineName,\n          dydjbm: this.dydjbm,\n          ssbm: \"线路分公司\"\n        };\n        this.title = \"批量新增杆塔\";\n        this.addTowerBatchDialogFormVisible = true;\n      }\n    },\n    saveTowerBatchButton() {\n      this.$refs[\"addTowerBatchForm\"].validate(valid => {\n        if (valid) {\n          saveTowerBatch(this.addTowerBatchForm).then(res => {\n            if (res.code === \"0000\") {\n              this.$message.success(\"操作成功\");\n              this.addTowerBatchDialogFormVisible = false;\n              this.gtTzData(this.gtfilterInfo.data);\n            }\n          });\n        }\n      });\n    },\n    beforeUpload(file) {\n      const fileSize = file.size < 1024 * 1024 * 50;\n      if (!fileSize) {\n        this.$message.error(\"上传文件大小不能超过 50MB!\");\n      }\n    },\n    //杆塔修改操作\n    updateRow2(row) {\n      this.title = \"修改杆塔信息\";\n      this.clearUpload();\n      this.gtresumeQuery.foreignNum = row.ssbm;\n      this.gtresumeQuery.sblx = row.sblx;\n      this.getResumList();\n      this.gtForm = { ...row };\n      this.gtForm.attachment = [];\n      this.gtTzData();\n      this.gtDialogFormVisible = true;\n      this.gtshow = false;\n    },\n    //杆塔查看详情\n    detailsInfo2(row) {\n      this.title = \"杆塔信息\";\n      this.clearUpload();\n      this.gtresumeQuery.foreignNum = row.ssbm;\n      this.gtresumeQuery.sblx = row.sblx;\n      this.getResumList();\n      this.gtForm = { ...row };\n      this.gtForm.attachment = [];\n      this.gtTzData();\n      this.gtDialogFormVisible = true;\n      this.gtshow = true;\n    },\n    clearUpload() {\n      if (this.$refs.upload) {\n        this.$refs.upload.clearFiles();\n      }\n    },\n    //杆塔弹框关闭\n    gthandleClose() {\n      // this.gtForm = {\n      //   attachment: [],\n      //   ssbm : '线路分公司',\n      //   lineId : this.linedIdStore,\n      //   lineName : this.lineName\n      // };\n      this.gtForm.attachment = [];\n      this.gtForm.objId = undefined;\n      this.gtForm.lineId = this.linedIdStore;\n      this.$nextTick(function() {\n        this.$refs[\"gtForm\"].clearValidate();\n      });\n      this.gtDialogFormVisible = false;\n    },\n    //杆塔内容保存\n    addGtInfo() {\n      this.$refs[\"gtForm\"].validate(valid => {\n        if (valid) {\n          saveOrUpdategt(this.gtForm).then(res => {\n            if (res.code == \"0000\") {\n              this.uploadData.businessId = res.data.objId;\n              this.$refs.upload.submit();\n              this.$message.success(\"操作成功\");\n              this.gtDialogFormVisible = false;\n              this.gtTzData();\n            } else {\n              this.$message.warning(\"操作失败！\");\n              return false;\n            }\n          });\n        } else {\n          setTimeout(() => {\n            var isError = document.getElementsByClassName(\"is-error\");\n            if (isError[0].querySelector(\"input\")) {\n              isError[0].querySelector(\"input\").focus();\n            } else if (isError[0].querySelector(\"textarea\")) {\n              isError[0].querySelector(\"textarea\").focus();\n            }\n          }, 1);\n          return false;\n        }\n      });\n    },\n    //间隔添加按钮\n    jgAddjgButton() {\n      this.jgDialogFormVisible = true;\n    },\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n    //每页展示数量点击事件\n    handleSizeChange() {},\n    //页码改变事件\n    handleCurrentChange() {},\n    //树点击事件\n    handleNodeClick(data, node, nodeInfo) {\n      this.identifier = data.identifier;\n      this.queryParams.dydj = \"\"; //清空电压等级条件\n      //当前节点是线路节点\n      if (data.identifier == \"1\") {\n        //线路台账\n        this.queryParams.lineName = \"\";\n        this.lineTzData(this.queryParams);\n      } else if (data.identifier == \"2\") {\n        //电压等级节点\n        this.xloptions.forEach(item => {\n          if (item.label === data.id) {\n            this.dydjbm = item.value;\n            return false;\n          }\n        });\n        this.$set(this.xlForm, \"dydjbm\", this.dydjbm); //设置电压等级\n        this.queryParams.dydj = data.id;\n        this.lineTzData(this.queryParams); //请求线路数据\n      } else if (data.identifier == \"3\") {\n        this.gtForm.lineName = data.label;\n        this.gtForm.lineId = data.id;\n        this.linedIdStore = data.id;\n        this.gtfilterInfo.data.lineName = data.label;\n        this.lineName = data.label;\n        this.gtTzData(this.gtfilterInfo.data);\n      } else if (data.identifier == \"4\") {\n        this.gtbhStore = data.id; //杆塔编号\n        this.gtmcStore = data.label;\n        this.jbxxForm.ssxl = node.parent.data.id; //线路编码\n        this.jbxxForm.lineName = node.parent.data.label; //线路名称\n        this.jbxxForm.ssgtbh = data.id; //杆塔编号\n        this.jbxxForm.gtmc = data.label; //杆塔名称\n        let ssgtbh = data.id; //杆塔编号\n        this.sbTzData();\n      }\n    },\n    //缺陷标准库新增完成\n    qxcommit() {\n      this.dialogFormVisible = false;\n      this.$message.success(\"新增成功\");\n    },\n    submit() {\n      this.$refs[\"jbxxForm\"].validate(valid => {\n        if (valid) {\n          this.addEquipInfo();\n          //  this.submitParameter();\n        } else {\n          setTimeout(() => {\n            var isError = document.getElementsByClassName(\"is-error\");\n            if (isError[0].querySelector(\"input\")) {\n              isError[0].querySelector(\"input\").focus();\n            } else if (isError[0].querySelector(\"textarea\")) {\n              isError[0].querySelector(\"textarea\").focus();\n            }\n          }, 1);\n          return false;\n        }\n      });\n    },\n    //获取设备类型下拉框数据\n    getSblxDataListSelected() {\n      let sblxParam = {\n        type: \"输电设备\"\n      };\n      getSblxDataListSelected(sblxParam).then(res => {\n        this.sblxOptionsDataSelected = res.data;\n      });\n    },\n    //设备类型change事件\n    showParams(data) {\n      this.paramQuery.sblxbm = data;\n      this.getParameters();\n    },\n    //点击新增，修改，详情时，重新获取对应的技术参数信息\n    technicalParameters(row) {\n      //设备类型\n      this.jscsForm = {};\n      this.paramQuery.sblxbm = row.sbflbm;\n      this.jscsForm.sblxbm = row.sbflbm;\n      this.jscsForm.sbbm = row.objId;\n      this.getParameters();\n    },\n    //获取技术参数对照信息，展示对应得技术参数label信息\n    async getParameters() {\n      this.jscsLabelList = [];\n      getParamDataList(this.paramQuery).then(res => {\n        this.jscsLabelList = res.data;\n        this.getParamValue();\n      });\n    },\n    //获取值方法\n    getParamValue() {\n      getParamsValue(this.jscsForm).then(res => {\n        if (res.data != \"\") {\n          this.jscsForm = { ...res.data[0] };\n        }\n      });\n    },\n    //保存基数参数值信息\n    submitParameter() {\n      saveParamValue(this.jscsForm).then(res => {\n        this.sbDialogFormVisible = false;\n      });\n    },\n    async deleteFileById(id){\n      let {code}=await deleteById(id)\n      if(code==='0000'){\n        await this.getFileList();\n        this.$message({\n          type: 'success',\n          message: '文件删除成功!'\n        });\n      }\n    },\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  height: calc(100vh - 225px);\n  overflow: auto;\n}\n\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.box-cardList {\n  height: 70%;\n}\n\n.item {\n  width: 200px;\n  float: left;\n}\n\n#main_container_dj {\n  height: calc(100vh - 84px);\n}\n\n.aside_height {\n  height: 96%;\n}\n\n.defect .el-form-item:nth-child(odd) {\n  margin-right: 70px;\n}\n\n.imgCls {\n  height: 150px !important;\n}\n\n/*背景颜色调整*/\n#main_container_dj,\n#main_container_dj .el-aside {\n  background-color: #b4caf1;\n}\n\n.el-select {\n  width: 100%;\n}\n\n.el-date-editor {\n  width: 100%;\n}\n\n.el-carousel__item h3 {\n  color: #475669;\n  font-size: 14px;\n  opacity: 0.75;\n  line-height: 150px;\n}\n\n.el-carousel__item:nth-child(2n) {\n  background-color: #99a9bf;\n}\n\n.el-carousel__item:nth-child(2n + 1) {\n  background-color: #d3dce6;\n}\n\n/deep/ .box-card {\n  margin: 0 6px;\n}\n\n//有子节点 且未展开\n.el-tree ::v-deep .el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/add.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n\n//有子节点 且已展开\n.el-tree\n  ::v-deep\n  .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/prep.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n\n//没有子节点\n.el-tree ::v-deep .el-tree-node__expand-icon.is-leaf::before {\n  background: transparent;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n\n.divHeader {\n  width: 100%;\n  height: 30px;\n  background-color: #dddddd;\n  margin-bottom: 10px;\n  text-align: left;\n  line-height: 30px;\n  font-weight: bold;\n}\n\n.categoryTreeIcons {\n  background: url(\"../../../../assets/icons/icon/icon1.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n\n.tableIcon {\n  background: url(\"../../../../assets/icons/icon/icon2.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n\n.classIcon {\n  background: url(\"../../../../assets/icons/icon/icon4.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n\n.classIcon2 {\n  background: url(\"../../../../assets/icons/icon/icon4.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n\n.my-autocomplete {\n  li {\n    line-height: normal;\n    padding: 7px;\n\n    .name {\n      text-overflow: ellipsis;\n      overflow: hidden;\n      font-size: 18px;\n    }\n    .addr {\n      font-size: 12px;\n      color: #b4b4b4;\n    }\n\n    .highlighted .addr {\n      color: #ddd;\n    }\n  }\n}\n</style>\n\n<style>\n.el-carousel__container {\n  background-color: #ffffff !important;\n}\n</style>\n"], "sourceRoot": "src/views/dagangOilfield/dwzygl/sdsbgl"}]}