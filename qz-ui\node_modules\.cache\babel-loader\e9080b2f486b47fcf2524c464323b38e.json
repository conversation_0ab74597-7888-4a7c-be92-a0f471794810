{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\lpbzk\\xlyxbzk.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\lpbzk\\xlyxbzk.js", "mtime": 1706897313941}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0TGlzdFhseXhiemsgPSBnZXRMaXN0WGx5eGJ6azsKZXhwb3J0cy5zYXZlT3JVcGRhdGVYbHl4YnprID0gc2F2ZU9yVXBkYXRlWGx5eGJ6azsKZXhwb3J0cy5yZW1vdmVYbHl4YnprID0gcmVtb3ZlWGx5eGJ6azsKCnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwoKdmFyIGJhc2VVcmwgPSAiL21hbmFnZXItYXBpIjsKLyoqCiAqIOafpeivouW3oeajgOS7u+WKoQogKiAqLwoKZnVuY3Rpb24gZ2V0TGlzdFhseXhiemsocGFyYW1zKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcveGx4amJ6L2dldERhdGFMaXN0JywgcGFyYW1zLCAxKTsKfSAvLyDmt7vliqDmiJbkv67mlLkKCgpmdW5jdGlvbiBzYXZlT3JVcGRhdGVYbHl4YnprKHBhcmFtcykgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3RQb3N0KGJhc2VVcmwgKyAnL3hseGpiei9zYXZlT3JVcGRhdGUnLCBwYXJhbXMsIDEpOwp9IC8vIOWIoOmZpAoKCmZ1bmN0aW9uIHJlbW92ZVhseXhiemsocGFyYW1zKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcveGx4amJ6L3JlbW92ZScsIHBhcmFtcywgMSk7Cn0="}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagang<PERSON>field/bzgl/lpbzk/xlyxbzk.js"], "names": ["baseUrl", "getListXlyxbzk", "params", "api", "requestPost", "saveOrUpdateXlyxbzk", "removeXlyxbzk"], "mappings": ";;;;;;;;;;;AAAA;;AAEA,IAAMA,OAAO,GAAG,cAAhB;AAEA;;;;AAGO,SAASC,cAAT,CAAwBC,MAAxB,EAAgC;AACrC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,qBAAxB,EAA8CE,MAA9C,EAAqD,CAArD,CAAP;AACD,C,CAED;;;AACO,SAASG,mBAAT,CAA6BH,MAA7B,EAAqC;AAC1C,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,sBAAxB,EAA+CE,MAA/C,EAAsD,CAAtD,CAAP;AACD,C,CACD;;;AACO,SAASI,aAAT,CAAuBJ,MAAvB,EAA+B;AACpC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,gBAAxB,EAAyCE,MAAzC,EAAgD,CAAhD,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\n\nconst baseUrl = \"/manager-api\";\n\n/**\n * 查询巡检任务\n * */\nexport function getListXlyxbzk(params) {\n  return api.requestPost(baseUrl+'/xlxjbz/getDataList',params,1)\n}\n\n// 添加或修改\nexport function saveOrUpdateXlyxbzk(params) {\n  return api.requestPost(baseUrl+'/xlxjbz/saveOrUpdate',params,1)\n}\n// 删除\nexport function removeXlyxbzk(params) {\n  return api.requestPost(baseUrl+'/xlxjbz/remove',params,1)\n}\n"]}]}