{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\pdyj.vue?vue&type=style&index=0&id=2aa80939&scoped=true&lang=css&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\pdyj.vue", "mtime": 1706897323222}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5wYWdpbmF0aW9uLWNvbnRhaW5lciB7CiAgcG9zaXRpb246IHJlbGF0aXZlOwogIGhlaWdodDogMjVweDsKICBtYXJnaW4tYm90dG9tOiAyMHB4ICFpbXBvcnRhbnQ7CiAgbWFyZ2luLXRvcDogMTVweDsKICBwYWRkaW5nOiAxMHB4IDIwcHggIWltcG9ydGFudDsKfQo="}, {"version": 3, "sources": ["pdyj.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqRA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "pdyj.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk", "sourcesContent": ["<template>\n  <div>\n    <el-white>\n      <el-white class=\"button-group\">\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"addpdyj\">新增</el-button>\n        <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"deletepdyj\">删除</el-button>\n      </el-white>\n      <el-table\n          stripe\n          border\n          v-loading=\"loading\"\n          :data=\"tableData\"\n          @selection-change=\"handleSelectionChange\">\n        <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n        <el-table-column label=\"所属状态量\" align=\"center\" prop=\"ztlmc\"/>\n        <el-table-column label=\"判断依据\" align=\"center\" prop=\"clgzms\"/>\n        <!--        <el-table-column label=\"处理方式\" align=\"center\" prop=\"clfs\" />-->\n        <el-table-column label=\"基本扣分值\" align=\"center\" prop=\"jbkfz\"/>\n        <el-table-column label=\"脚本\" align=\"center\" prop=\"jb\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"操作\" fixed=\"right\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"180\">\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" @click=\"updatePdyj(scope.row)\">修改</el-button>\n            <el-button type=\"text\" @click=\"showDetail(scope.row)\">详情</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      <pagination\n          v-show=\"queryParams.total>0\"\n          :total=\"queryParams.total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getListPdyj\"/>\n    </el-white>\n\n    <dialog-form\n        ref=\"dialogForm\"\n        :append-to-body=\"true\"\n        label-width=\"150px\"\n        out-width=\"50%\"\n        :reminder=\"reminder\"\n        :rows=\"rows\"\n        @save=\"savePdyj\"\n        @textareaClick=\"textareaClick\"\n    />\n\n    <el-dialog\n        title=\"脚本维护\"\n        :visible.sync=\"isShowJb\"\n        v-dialogDrag\n        width=\"80%\"\n        append-to-body\n        @close=\"jbClose\"\n    >\n      <!--  脚本维护框  -->\n      <JBwh :sblx=\"sblx\" :mp-data=\"mpData\" :jb=\"formList[5].value\" @setJbVal=\"setJbVal\" @jbClose=\"jbClose\"></JBwh>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  getPagePdyj,\n  saveOrUpdatePdyj,\n  removePdyj,\n} from \"@/api/dagangOilfield/bzgl/sbztpjbzk/pdyj\";\nimport DialogForm from \"com/dialogFrom/dialogForm\";\nimport JBwh from \"@/views/dagangOilfield/bzgl/sbztpjbzk/jbwh\";\n\nexport default {\n  name: \"pdyj\",\n  components: {DialogForm, JBwh},\n  props: {\n    mpData: {\n      type: Object,\n    },\n    sblx: {\n      type: String,\n      default: ''\n    },\n  },\n\n  data() {\n    return {\n      isShowJb: false,//是否显示脚本维护框\n      //新增或修改标题\n      reminder: \"新增\",\n      rows: 2,\n      tableData: [],\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        total: 0,\n        ssztl: \"\",\n      },\n      loading: false,\n      jbkfz: {\n        label: \"基本扣分值：\",\n        value: \"\",\n        type: \"selectChange1\",\n        name: \"jbkfz\",\n        default: true,\n        options: [{label: \"2\", value: \"2\"}, {label: \"4\", value: \"4\"}, {label: \"8\", value: \"8\"}, {label: \"10\", value: \"10\"}],\n        rules: {required: true, message: \"请选择基本扣分值\"},\n      },\n      formList: [\n        {\n          label: 'objId',\n          value: '',\n          type: 'input',\n          name: 'objId',\n          hidden: false,\n        },\n        {\n          label: 'ssztl：',\n          value: '',\n          type: 'input',\n          name: 'ssztl',\n          hidden: false,\n        },\n        {\n          label: \"所属状态量：\",\n          value: \"\",\n          type: \"disabled\",\n          name: \"ztlmc\",\n          default: true,\n          rules: {required: false, message: \"请输入所属状态量\"},\n        },\n        {\n          label: \"判断依据：\",\n          value: \"\",\n          type: \"input\",\n          name: \"clgzms\",\n          default: true,\n          rules: {required: true, message: \"请输入判断依据\"},\n        },\n        {\n          label: \"基本扣分值：\",\n          value: 0,\n          type: \"input\",\n          name: \"jbkfz\",\n          default: true,\n          rules: {required: true, message: \"请输入基本扣分值\"},\n        },\n        {\n          label: \"脚本：\",\n          value: \"\",\n          type: \"textarea\",\n          name: \"jb\",\n          default: true,\n          rules: {required: true, message: \"请输入脚本\"},\n        }\n      ],\n      //选中行数据\n      selectedRowData: [],\n    };\n  },\n  mounted() {\n    if (this.sblx && this.sblx.indexOf('bd') > -1) {\n      this.formList[4] = this.jbkfz;\n    }\n    this.getListPdyj();\n  },\n  methods: {\n    jbClose() {\n      this.isShowJb = false;\n    },\n    textareaClick(val, isDisabled) {\n      if (!isDisabled) {\n        this.isShowJb = true;\n      }\n    },\n    //获取判断依据\n    getListPdyj() {\n      this.loading = true;\n      this.queryParams.ssztl = this.mpData.objId;\n      getPagePdyj(this.queryParams).then((res) => {\n        this.tableData = res.data.records;\n        this.queryParams.total = res.data.total;\n        this.loading = false;\n      });\n\n    },\n\n    //新增判断依据\n    addpdyj() {\n      this.reminder = \"新增\";\n      this.formList.ssztl = this.mpData.objId;\n      this.formList = this.$options.data().formList;\n      if (this.sblx && this.sblx.indexOf('bd') > -1) {\n        this.formList[4] = this.jbkfz\n      }\n      const addForm = this.formList.map((item) => {\n        if (item.name === 'ztlmc') {\n          item.value = this.mpData.ztlmc\n        }\n        if (item.name === 'ssztl') {\n          item.value = this.mpData.objId\n        }\n        return item;\n      });\n      this.$refs.dialogForm.showzzc(addForm);\n    },\n\n    //修改判断依据\n    updatePdyj(row) {\n      const updateList = this.formList.map((item) => {\n        item.value = row[item.name];\n        return item;\n      });\n      this.reminder = \"修改\";\n      this.$refs.dialogForm.showzzc(updateList);\n    },\n\n    //查看详情\n    showDetail(row) {\n      const infoList = this.formList.map((item) => {\n        item.value = row[item.name];\n        return item;\n      });\n      this.reminder = \"详情\";\n      this.$refs.dialogForm.showxq(infoList);\n    },\n\n    //批量删除判断依据\n    deletepdyj() {\n      this.$confirm(\"确认删除选中数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      })\n          .then(() => {\n            let ids = [];\n            this.selectedRowData.forEach((item) => {\n              ids.push(item.objId);\n            });\n\n            removePdyj(ids).then((res) => {\n              if (res.code === \"0000\") {\n                this.$message.success(\"删除成功\");\n                this.getListPdyj();\n              } else {\n                this.$message.error(\"操作失败\");\n              }\n            });\n          })\n          .catch(() => {\n            this.$message({\n              type: \"info\",\n              message: \"已取消删除\",\n            });\n          });\n    },\n\n    //保存数据\n    savePdyj(formData) {\n      saveOrUpdatePdyj(formData).then(res => {\n        if (res.code === '0000') {\n          this.$message.success('操作成功')\n          this.getListPdyj()\n        } else {\n          this.$message.error('操作失败')\n        }\n      })\n    },\n    //行选中事件\n    handleSelectionChange(row) {\n      this.selectedRowData = row\n    },\n    //设置脚本值\n    setJbVal(val) {\n      this.$refs.dialogForm.setFieldVal('jb', val);\n    },\n  },\n};\n</script>\n\n<style scoped>\n.pagination-container {\n  position: relative;\n  height: 25px;\n  margin-bottom: 20px !important;\n  margin-top: 15px;\n  padding: 10px 20px !important;\n}\n</style>\n\n\n"]}]}