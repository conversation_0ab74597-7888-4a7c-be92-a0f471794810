{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\lpbzk\\gzpacdyk.vue?vue&type=style&index=0&id=62c92e0c&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\lpbzk\\gzpacdyk.vue", "mtime": 1706897322614}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5ib3gtY2FyZCB7CiAgbWFyZ2luLWJvdHRvbTogMTVweDsKCiAgLmVsLWNhcmRfX2hlYWRlciB7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjM1LCAyNDUsIDI1NSkgIWltcG9ydGFudDsKICB9Cn0KCi5pdGVtIHsKICB3aWR0aDogMjAwcHg7CiAgaGVpZ2h0OiAxNDhweDsKICBmbG9hdDogbGVmdDsKfQo="}, {"version": 3, "sources": ["gzpacdyk.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiVA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "gzpacdyk.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/lpbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n        :data=\"filterInfo.data\"\n        :field-list=\"filterInfo.fieldList\"\n        @handleReset=\"filterReset\"\n    />\n    <div>\n      <el-white class=\"button-group\">\n        <div class=\"button_btn\">\n          <el-button type=\"primary\" v-hasPermi=\"['bzgzpacdyk:button:add']\" icon=\"el-icon-plus\" @click=\"addSensorButton\">\n            新增\n          </el-button>\n\n        </div>\n        <comp-table :table-and-page-info=\"tableAndPageInfo\" @update:multipleSelection=\"selectChange\" height=\"70vh\"\n                    ref=\"gzpacdyk\"\n        >\n          <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\" width=\"160\"\n                           :resizable=\"false\">\n            <template slot-scope=\"scope\">\n              <el-button @click=\"updateRow(scope.row)\" v-hasPermi=\"['bzgzpacdyk:button:update']\" type=\"text\"\n                         size=\"small\" title=\"修改\" class='el-icon-edit'></el-button>\n              <el-button @click=\"getInfo(scope.row)\" type=\"text\" size=\"small\" title=\"详情\"\n                         class=\"el-icon-view\"></el-button>\n                         <el-button type=\"text\" size=\"small\" @click=\"deleteRow(scope.row.objId)\" v-if=\"scope.row.createBy === $store.getters.name\" title=\"删除\" class=\"el-icon-delete\">\n          </el-button>\n            </template>\n          </el-table-column>\n        </comp-table>\n      </el-white>\n    </div>\n\n    <!--新增、修改、详情弹框-->\n    <el-dialog :title=title :visible.sync=\"show\" v-if=\"show\" width=\"30%\" append-to-body v-dialogDrag>\n      <el-form label-width=\"130px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"工作票类型：\" prop=\"gzplx\">\n              <el-select placeholder=\"请选择工作票类型\" v-model=\"form.gzplx\" style=\"width: 100%\" :disabled=\"isDisabled\" multiple\n                         filterable clearable>\n                <el-option\n                    v-for=\"item in options\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"工作类型：\" prop=\"gzlx\">\n              <el-input v-model=\"form.gzlx\" placeholder=\"请输入工作类型\" :disabled=\"isDisabled\"/>\n              <!--            <el-select placeholder=\"请选择工作类型\" v-model=\"form.gzlx\" style=\"width: 100%\" :disabled=\"isDisabled\">\n                            <el-option\n                                v-for=\"item in gzlxList\"\n                                :key=\"item.value\"\n                                :label=\"item.label\"\n                                :value=\"item.value\">\n                            </el-option>\n                          </el-select>-->\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"安措短语：\" prop=\"acdy\">\n              <el-input type=\"textarea\" v-model=\"form.acdy\" placeholder=\"请输入安措短语\" :disabled=\"isDisabled\"/>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"getInsterClose\">取 消</el-button>\n        <el-button v-if=\"title==='修改' || title==='新增'\" type=\"primary\" @click=\"saveRow\">确 认</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {getList, saveOrUpdate, remove, exportExcel} from '@/api/dagangOilfield/bzgl/lpbzk/gzpacdyk'\n\nexport default {\n  name: \"lpbzk\",\n  data() {\n    return {\n      selectRows: [],\n      //新增按钮form表单\n      form: {\n      },\n      loading: false,\n      isDisabled: false,\n      title: '',\n      show: false,\n      options: [\n        // {label:'变电站(发电厂)第一种工作票',value:'变电站(发电厂)第一种工作票'},\n        // {label:'变电站(发电厂)第二种工作票',value:'变电站(发电厂)第二种工作票'},\n        {label: '变电站(发电厂)和线路事故应急抢修单', value: '3'},\n        {label: '电力线路第一种工作票', value: '4'},\n        {label: '电力线路第二种工作票', value: '5'},\n        // {label:'电力电缆第一种工作票',value:'6'},\n        // {label:'电力电缆第二种工作票',value:'7'},\n        {label: '配电站工作票', value: '8'},\n        {label: '配电站事故应急抢修单', value: '9'},\n        {label: '配电站检维修工作票', value: '10'},\n        {label: '配电站工作任务单', value: '11'}\n      ],\n      filterInfo: {\n        data: {\n          gzplxArr: [],\n          acdy: ''\n        },\n        fieldList: [\n          {\n            label: '工作票类型', type: 'select', value: 'gzplx',\n            options: [\n              /*{label:'变电站(发电厂)第一种工作票',value:'变电站(发电厂)第一种工作票'},\n              {label:'变电站(发电厂)第二种工作票',value:'变电站(发电厂)第二种工作票'},*/\n              {label: '变电站(发电厂)和线路事故应急抢修单', value: '3'},\n              {label: '电力线路第一种工作票', value: '4'},\n              {label: '电力线路第二种工作票', value: '5'},\n              // {label:'电力电缆第一种工作票',value:'6'},\n              // {label:'电力电缆第二种工作票',value:'7'},\n              {label: '配电站工作票', value: '8'},\n              {label: '配电站事故应急抢修单', value: '9'},\n              {label: '配电站检维修工作票', value: '10'},\n              {label: '配电站工作任务单', value: '11'}], clearable: true\n          },\n          {label: '工作类型', type: 'input', value: 'gzlx'},\n          {label: '安措短语', type: 'input', value: 'acdy'},\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          {prop: 'gzplxmc', label: '工作票类型', minWidth: '90'},\n          {prop: 'gzlx', label: '工作类型', minWidth: '40'},\n          {prop: 'acdy', label: '安措短语', minWidth: '180'},\n          /* {\n             prop: 'operation',\n             label: '操作',\n             minWidth: '130px',\n             style: {display: 'block'},\n             //操作列固定再右侧\n             fixed:'right',\n             operation: [\n               {name: '修改', clickFun: this.updateRow},\n               {name: '详情', clickFun: this.getInfo},\n             ]\n           },*/\n        ]\n      },\n      //查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        ancuoTerm: ''\n      },\n      //填入数据校验\n      rules: {\n        gzplx: [\n          {required: true, message: '请选择工作票类型', trigger: 'change'}\n        ],\n        gzlx: [\n          {required: true, message: '请输入工作类型', trigger: 'blur'}\n        ],\n        acdy: [\n          {required: true, message: '请输入按错短语', trigger: 'blur'}\n        ]\n      },\n      //表单开关\n      isSearchShow: false,\n\n      params: {\n        acdy: '',\n        gzplxArr: [],\n        gzlxArr: []\n      },\n    }\n  },\n  watch: {},\n  created() {\n\n\n  },\n  mounted() {\n    this.getData();\n  },\n  methods: {\n    formatGzlx(gzlx) {\n      let filter = this.options.filter(g => gzlx.indexOf(g.value) > -1);\n      if (filter.length > 0) {\n        return filter.map(g => g.label).join(\"，\");\n      }else {\n        return \"\"\n      }\n    },\n    //加载列表\n    async getData(params) {\n      try {\n        this.$refs.gzpacdyk.loading = true\n        this.params = {...this.params, ...params}\n        const param = this.params\n        param.mySorts = [{prop: 'gzplx', asc: true},{prop: 'acdy', asc: true}]\n        const {data, code} = await getList(param);\n        if (code === '0000') {\n          for (let i of data.records) {\n            i.gzplxmc = this.formatGzlx(i.gzplx);\n          }\n          this.tableAndPageInfo.tableData = data.records\n          this.tableAndPageInfo.pager.total = data.total\n          this.$nextTick(() => {\n            this.$refs.gzpacdyk.loading = false\n          });\n        }\n      } catch (e) {\n        console.log(e)\n      }\n\n\n    },\n    //树节点点击事件\n    handleNodeClick(data) {\n\n    },\n    //添加按钮\n    addSensorButton() {\n      this.form = {}\n      this.show = true\n      this.isDisabled = false\n      this.title = '新增'\n    },\n    //关闭弹框\n    getInsterClose() {\n      this.show = false\n    },\n    //重置按钮\n    filterReset() {\n      this.params =  {\n        acdy: '',\n        gzplxArr: [],\n        gzlxArr: []\n      }\n    },\n    //编辑按钮\n    updateRow(row) {\n      this.title = '修改'\n      this.isDisabled = false\n      this.form = {...row}\n      this.form.gzplx = this.form.gzplx.split(',')\n      this.show = true\n    },\n    //详情按钮\n    getInfo(row) {\n      this.title = '详情'\n      this.form = {...row}\n      this.form.gzplx = this.form.gzplx.split(',')\n      this.isDisabled = true\n      this.show = true\n    },\n    //保存\n    async saveRow() {\n      await this.$refs['form'].validate(valid => {\n        if (valid) {\n          this.form.gzplx = this.form.gzplx.toString()\n          saveOrUpdate(this.form).then(res => {\n            try {\n              if (res.code === '0000') {\n                this.$message.success('操作成功')\n              }\n            } catch (e) {\n              console.log(e)\n            }\n            this.getData()\n          })\n        } else {\n          return false\n        }\n        this.show = false\n      })\n    },\n    //删除按钮\n    async deleteRow(id) {\n      // if (this.selectRows.length < 1) {\n      //   this.$message.warning(\"请选择正确的数据！！！\")\n      //   return\n      // }\n      // let ids = this.selectRows.map(item => {\n      //   return item.objId\n      // });\n      let obj=[];\n        obj.push(id);\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        remove(obj).then(({code}) => {\n          if (code === '0000') {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            });\n            this.getData()\n          } else {\n            this.$message({\n              type: 'error',\n              message: '删除失败!'\n            });\n          }\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        });\n      });\n\n    },\n    //列表选中事件\n    selectChange(rows) {\n      this.selectRows = rows\n    },\n  }\n};\n</script>\n\n<style lang='scss' scoped>\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n</style>\n"]}]}