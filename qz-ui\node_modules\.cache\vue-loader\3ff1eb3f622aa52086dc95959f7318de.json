{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\dgTodoItem\\noticeTodo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\dgTodoItem\\noticeTodo.vue", "mtime": 1706897322042}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["noticeTodo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "noticeTodo.vue", "sourceRoot": "src/views/activiti/dgTodoItem", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-white>\n      <el-filter\n        ref=\"filter1\"\n        :data=\"filterInfo.data\"\n        :field-list=\"filterInfo.fieldList\"\n        :width=\"{ labelWidth: 180, itemWidth: 200 }\"\n        @handleReset=\"filterReset\"\n      ></el-filter>\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"selectChange\"\n        height=\"75vh\"\n      >\n        <el-table-column\n          slot=\"table_six\"\n          align=\"center\"\n          style=\"display: block;height: auto\"\n          label=\"状态\"\n          min-width=\"180\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-badge\n              :value=\"scope.row.isRead == 0 ? '未读' : '已读'\"\n              :type=\"scope.row.isRead == 0 ? 'danger' : 'primary'\"\n              class=\"item\"\n            >\n            </el-badge>\n          </template>\n        </el-table-column>\n        <el-table-column slot=\"table_seven\" label=\"附件\" prop=\"attachment\">\n          <template slot-scope=\"scope\">\n            <span v-if=\"scope.row.fileList.length > 0\">有附件</span>\n            <span v-else>无附件</span>\n          </template>\n        </el-table-column>\n        <el-table-column\n          slot=\"table_eight\"\n          align=\"center\"\n          fixed=\"right\"\n          style=\"display: block\"\n          label=\"操作\"\n          min-width=\"70\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" size=\"small\" @click=\"getDetails(scope.row)\"\n              >查看</el-button\n            >\n          </template>\n        </el-table-column>\n      </comp-table>\n    </el-white>\n    <el-dialog\n      title=\"通知详情\"\n      :visible.sync=\"openInfo\"\n      width=\"50%\"\n      append-to-body\n      :close-on-click-modal=\"false\"\n      v-dialogDrag\n    >\n      <el-form ref=\"formInfo\" :model=\"formInfo\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"标题：\" prop=\"title\">\n              <el-input v-model=\"formInfo.title\" :disabled=\"isDisabled\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"内容：\" prop=\"content\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"3\"\n                v-model=\"formInfo.content\"\n                :disabled=\"isDisabled\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-form-item\n            label=\"附件：\"\n            prop=\"attachment\"\n            v-if=\"formInfo.fileList.length > 0\"\n          >\n            <template slot-scope=\"scope\">\n              <span v-for=\"it in formInfo.fileList\">{{ it.fileOldName }}</span>\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"downloadHandle(formInfo.id)\"\n                >下载</el-button\n              >\n            </template>\n          </el-form-item>\n        </el-row>\n      </el-form>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getNoticePage, updateReadStatus } from \"@/api/activiti/DgTodoItem\";\nimport { downloadByBusinessId } from \"@/api/tool/file\";\nexport default {\n  name: \"proclamationTodo\",\n  data() {\n    return {\n      filterInfo: {\n        data: {},\n        fieldList: [\n          { label: \"通知标题\", type: \"input\", value: \"title\" },\n          { label: \"发送人\", type: \"input\", value: \"senderName\" }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"title\", label: \"通知标题\", minWidth: \"140\" },\n          { prop: \"senderName\", label: \"发送人\", minWidth: \"120\" },\n          { prop: \"publishStartTime\", label: \"发送时间\", minWidth: \"120\" }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      selectRows: [],\n      params: {\n        type: 1\n      },\n      activeName: \"db\",\n      tabRefresh: {\n        db: 0,\n        yb: 1\n      },\n      openInfo: false,\n      formInfo: {\n        fileList: []\n      },\n      isDisabled: false\n    };\n  },\n  created() {\n    this.getData(this.$route.query);\n  },\n  mounted() {\n    //this.getData(this.$route.query)\n  },\n  methods: {\n    /**下载附件*/\n    downloadHandle(id) {\n      downloadByBusinessId(id);\n    },\n    async getData(param) {\n      let par = { ...param, ...this.params };\n      par.userName = this.$store.getters.name;\n      let { code, data } = await getNoticePage(par);\n      if (code === \"0000\") {\n        this.tableAndPageInfo.tableData = data.records;\n        this.tableAndPageInfo.pager.total = data.total;\n      }\n    },\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n    filterReset(data) {},\n\n    async getDetails(row) {\n      this.formInfo = { ...row };\n      this.isDisabled = true;\n      this.openInfo = true;\n      //如果是未查看状态，点击查看时变成已查看\n      if (row.isRead == 0) {\n        await updateReadStatus({\n          noticeId: row.id,\n          userName: this.$store.getters.name\n        });\n        await this.getData();\n      }\n    },\n    closeForm() {\n      this.formInfo = {\n        fileList: []\n      };\n    },\n    handleClick(tab) {\n      for (let key in this.tabRefresh) {\n        if (key === tab.name) {\n          this.params.isHandle = this.tabRefresh[key];\n          this.getData();\n        }\n      }\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n/*  .el-badge {\n\n  /deep/.el-badge__content\n  {\n    !*&.is-fixed {\n      position: absolute;\n      right: 10px;\n      top: 0;\n      transform: translateX(100%) translateY(-50%);\n    }*!\n    margin-top: 0.45vh;\n    font-size: 10px;\n  }\n\n  }*/\n.item {\n  width: 8.5rem;\n  height: 1.25rem;\n}\n</style>\n"]}]}