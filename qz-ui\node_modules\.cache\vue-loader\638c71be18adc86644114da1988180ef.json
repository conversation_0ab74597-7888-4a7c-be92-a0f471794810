{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\activiti_yxgc\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\activiti_yxgc\\index.vue", "mtime": 1720606743102}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGNvbXBsZXRlWXhnY1Rhc2sgfSBmcm9tICJAL2FwaS9hY3Rpdml0aS9wcm9jZXNzVGFzayI7CmltcG9ydCB7IExvYWRpbmcgfSBmcm9tICJlbGVtZW50LXVpIjsKaW1wb3J0IHsgZ2V0VXNlcnMgfSBmcm9tICJAL2FwaS9zeXN0ZW0vdXNlcmdyb3VwIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiaW5kZXgiLAogIHByb3BzOiB7CiAgICAvKua1geeoi+WPkei1t+W/heWhq+aVsOaNrgogICAgcHJvY2Vzc0RhdGE6ewogICAgICBwcm9jZXNzRGVmaW5pdGlvbktleToi5rWB56iL5a6a5LmJ55qEa2V5IiwvL+W/heWhqwogICAgICB0YXNrSWQ6IuS7u+WKoWlk77yM5aaC5p6c5Lu75Yqh5Zyo5Luj5Yqe5YiX6KGo5pe277yM5Lya5qC55o2u5Luj5Yqe5YiX6KGo6I635Y+WdGFza2lk77yM5ZCm5YiZ6ZyA5Lyg5YWl5Lia5YqhaWTmnaXnoa7lrpp0YXNrIiwKICAgICAgYnVzaW5lc3NLZXk6IuS4muWKoWlk77yM5a+55bqU5Lia5Yqh55qE5Li76ZSuIiwvL3Rhc2tpZOWSjGJ1c2luZXNzS2V55Lik6ICF5b+F6aG75pyJ5YW25Lit5LiA5Liq5omN5Y+v5Lul56Gu5a6a5LiA5LiqdGFzawogICAgICBidXNpbmVzc1R5cGU6IuS4muWKoeexu+Wei++8jOeUqOS6juWMuuWIhuS4jeWQjOeahOS4muWKoSIvL+W/heWhqwogICAgICB2YXJpYWJsZXM6IuaLk+WxleWPguaVsCIvL+a1geeoi+WumuS5ieS4reiuvue9rueahOWPguaVsCwKICAgICAgbmV4dFVzZXI6IuWmguaenOa1geeoi+WunuS+i+S4reW5tuacqumFjee9ruavj+S4gOS4quiKgueCueeahOWkhOeQhuS6uu+8jOWImemcgOimgeeUqOaIt+aJi+WKqOmAieaLqeavj+S4gOS4quiKgueCueeahOWkhOeQhuS6uiIsCiAgICAgIHByb2Nlc3NUeXBlOidjb21wbGV0ZSxyb2xsYmFjaycsCiAgICAgIGRlZmF1bHRGcm9tOnRydWUsZmFsc2Ug5piv5ZCm6ZyA6KaB6buY6K6k6KGo5Y2VCiAgICB9Ki8KICAgIHByb2Nlc3NEYXRhOiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdCgpIHsKICAgICAgICByZXR1cm4gewogICAgICAgICAgZGVmYXVsdEZyb206IHRydWUsCiAgICAgICAgICBwcm9jZXNzVHlwZTogImNvbXBsZXRlIgogICAgICAgIH07CiAgICAgIH0KICAgIH0sCiAgICAvL+aYvuekuumakOiXjwogICAgaXNTaG93OiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlCiAgICB9LAogICAgLy/lrZDnu4Tku7bpu5jorqTlj4LmlbAKICAgIG9wdGlvbjogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQoKSB7CiAgICAgICAgcmV0dXJuIHsgdGl0bGU6ICLlrqHmibkiIH07CiAgICAgIH0KICAgIH0KICB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBmb3JtOiB7fSwKICAgICAgZGF0YXM6IHt9LAogICAgICBsb2FkaW5nOiBudWxsLAogICAgICBkbGRkc2hyT3B0aW9uczogW10sCiAgICAgIGtqenhzaHJPcHRpb25zOiBbXSwKICAgICAgdXNlcnNPcHRpb25zOiBbXQogICAgfTsKICB9LAogIHdhdGNoOiB7CiAgICBwcm9jZXNzRGF0YTogewogICAgICBoYW5kbGVyKG5ld1ZhbCwgb2xkVmFsKSB7CiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgIm5leHRVc2VyMiIsIFtdKTsKICAgICAgICAgIHRoaXMuZGF0YXMgPSB7IC4uLm5ld1ZhbCB9OwogICAgICAgICAgaWYgKHRoaXMuZGF0YXMudGFza0lkID09PSAiZmdzbGRzaCIpIHsKICAgICAgICAgICAgZ2V0VXNlcnMoeyBwZXJzb25Hcm91cElkOiA4MCwgZGVwdElkOiAiIiwgZGVwdE5hbWU6ICIiIH0pLnRoZW4oCiAgICAgICAgICAgICAgcmVzID0+IHsKICAgICAgICAgICAgICAgIHRoaXMuZGxkZHNock9wdGlvbnMgPSByZXMuZGF0YTsKICAgICAgICAgICAgICAgIHRoaXMuZm9ybS5uZXh0VXNlcjIgPSB0aGlzLmRsZGRzaHJPcHRpb25zOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgKTsKICAgICAgICAgICAgZ2V0VXNlcnMoeyBwZXJzb25Hcm91cElkOiA4MSwgZGVwdElkOiAiIiwgZGVwdE5hbWU6ICIiIH0pLnRoZW4oCiAgICAgICAgICAgICAgcmVzID0+IHsKICAgICAgICAgICAgICAgIHRoaXMua2p6eHNock9wdGlvbnMgPSByZXMuZGF0YTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBpZiAodGhpcy5kYXRhcy5wZXJzb25Hcm91cElkKSB7CiAgICAgICAgICAgICAgZ2V0VXNlcnMoewogICAgICAgICAgICAgICAgcGVyc29uR3JvdXBJZDogdGhpcy5kYXRhcy5wZXJzb25Hcm91cElkLAogICAgICAgICAgICAgICAgZGVwdElkOiAiIiwKICAgICAgICAgICAgICAgIGRlcHROYW1lOiAiIgogICAgICAgICAgICAgIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgICAgIHRoaXMudXNlcnNPcHRpb25zID0gcmVzLmRhdGE7CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSwKICAgICAgZGVlcDogdHJ1ZSwKICAgICAgaW1tZWRpYXRlOiB0cnVlCiAgICB9CiAgfSwKICBtb3VudGVkKCkge30sCiAgbWV0aG9kczogewogICAgLy/mtYHnqIvmj5DkuqTvvIzmtYHnqIvmi5Lnu50KICAgIGFzeW5jIHRvZG9TdWJtaXQoKSB7CiAgICAgIGlmICghdGhpcy5kYXRhcy5yb3V0ZVBhdGgpIHsKICAgICAgICB0aGlzLmRhdGFzLnJvdXRlUGF0aCA9IHRoaXMuJHJvdXRlLnBhdGg7CiAgICAgIH0KICAgICAgaWYgKHRoaXMuZm9ybS5uZXh0VXNlcikgewogICAgICAgIHRoaXMuZGF0YXMubmV4dFVzZXIgPSB0aGlzLmZvcm0ubmV4dFVzZXIudXNlck5hbWU7CiAgICAgICAgdGhpcy5kYXRhcy5uZXh0VXNlck5pY2tOYW1lID0gdGhpcy5mb3JtLm5leHRVc2VyLm5pY2tOYW1lOwogICAgICB9CgogICAgICBpZiAodGhpcy5mb3JtLm5leHRVc2VyMiAmJiB0aGlzLmZvcm0ubmV4dFVzZXIyLmxlbmd0aCA+IDApIHsKICAgICAgICBsZXQgbmV4dFVzZXIgPSAiIjsKICAgICAgICBsZXQgbmV4dE5pY2sgPSAiIjsKICAgICAgICB0aGlzLmZvcm0ubmV4dFVzZXIyLmZvckVhY2goZSA9PiB7CiAgICAgICAgICBuZXh0VXNlciArPSBlLnVzZXJOYW1lICsgIiwiOwogICAgICAgICAgbmV4dE5pY2sgKz0gZS5uaWNrTmFtZSArICIsIjsKICAgICAgICB9KTsKICAgICAgICAvL+WOu+aOieacgOWQjuS4gOS4qumAl+WPtwogICAgICAgIGlmIChuZXh0VXNlci5sZW5ndGggPiAwKSB7CiAgICAgICAgICBuZXh0VXNlciA9IG5leHRVc2VyLnN1YnN0cmluZygwLCBuZXh0VXNlci5sZW5ndGggLSAxKTsKICAgICAgICAgIG5leHROaWNrID0gbmV4dE5pY2suc3Vic3RyaW5nKDAsIG5leHROaWNrLmxlbmd0aCAtIDEpOwogICAgICAgIH0KICAgICAgICB0aGlzLmRhdGFzLm5leHRVc2VyMiA9IG5leHRVc2VyOwogICAgICAgIHRoaXMuZGF0YXMubmV4dFVzZXJOaWNrTmFtZTIgPSBuZXh0TmljazsKICAgICAgfQoKICAgICAgaWYgKHRoaXMuZGF0YXMudGFza0lkID09PSAiZmdzbGRzaCIpIHsKICAgICAgICBpZiAoIXRoaXMuZGF0YXMubmV4dFVzZXIgfHwgIXRoaXMuZGF0YXMubmV4dFVzZXIyKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgbWVzc2FnZTogIuivt+mAieaLqeS6uuWRmCEiCiAgICAgICAgICB9KTsKICAgICAgICAgIHJldHVybjsKICAgICAgICB9CiAgICAgIH0KICAgICAgaWYgKAogICAgICAgICF0aGlzLmRhdGFzLm5leHRVc2VyICYmCiAgICAgICAgdGhpcy5kYXRhcy5wcm9jZXNzVHlwZSA9PT0gImNvbXBsZXRlIiAmJgogICAgICAgIHRoaXMuZGF0YXMuZGVmYXVsdEZyb20KICAgICAgKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICB0eXBlOiAiZXJyb3IiLAogICAgICAgICAgbWVzc2FnZTogIuivt+mAieaLqeS6uuWRmCEiCiAgICAgICAgfSk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGlmICh0aGlzLmZvcm0uY29tbWVudCkgewogICAgICAgIHRoaXMuZGF0YXMudmFyaWFibGVzLmNvbW1lbnQgPSB0aGlzLmZvcm0uY29tbWVudDsKICAgICAgfQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgLy8g5Lul5pyN5Yqh55qE5pa55byP6LCD55So55qEIExvYWRpbmcg6ZyA6KaB5byC5q2l5YWz6ZetCiAgICAgICAgdGhpcy5sb2FkaW5nID0gTG9hZGluZy5zZXJ2aWNlKHsKICAgICAgICAgIGxvY2s6IHRydWUsIC8vbG9ja+eahOS/ruaUueespi0t6buY6K6k5pivZmFsc2UKICAgICAgICAgIHRleHQ6ICLmtYHnqIvov5vooYzkuK3vvIzor7fnqI3lkI4iLCAvL+aYvuekuuWcqOWKoOi9veWbvuagh+S4i+aWueeahOWKoOi9veaWh+ahiAogICAgICAgICAgc3Bpbm5lcjogImVsLWljb24tbG9hZGluZyIsIC8v6Ieq5a6a5LmJ5Yqg6L295Zu+5qCH57G75ZCNCiAgICAgICAgICBiYWNrZ3JvdW5kOiAicmdiYSgwLCAwLCAwLCAwLjcpIiwgLy/pga7nvanlsYLpopzoibIKICAgICAgICAgIHRhcmdldDogZG9jdW1lbnQucXVlcnlTZWxlY3RvcigiI2RpYWxvZ0FjdCIpCiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgICBsZXQgcmVzdWx0RGF0YTsKICAgICAgdHJ5IHsKICAgICAgICBsZXQgeyBjb2RlLCBkYXRhIH0gPSBhd2FpdCBjb21wbGV0ZVl4Z2NUYXNrKHRoaXMuZGF0YXMpOwogICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIHJlc3VsdERhdGEgPSBkYXRhOwogICAgICAgIH0KICAgICAgICBpZiAoY29kZSkgewogICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgICAvLyDku6XmnI3liqHnmoTmlrnlvI/osIPnlKjnmoQgTG9hZGluZyDpnIDopoHlvILmraXlhbPpl60KICAgICAgICAgICAgdGhpcy5sb2FkaW5nLmNsb3NlKCk7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICAvLyDku6XmnI3liqHnmoTmlrnlvI/osIPnlKjnmoQgTG9hZGluZyDpnIDopoHlvILmraXlhbPpl60KICAgICAgICAgIHRoaXMubG9hZGluZy5jbG9zZSgpOwogICAgICAgIH0pOwogICAgICB9CiAgICAgIGlmIChyZXN1bHREYXRhKSB7CiAgICAgICAgcmVzdWx0RGF0YS5wcm9jZXNzVHlwZSA9IHRoaXMuZGF0YXMucHJvY2Vzc1R5cGU7CiAgICAgICAgdGhpcy4kZW1pdCgidG9kb0RhdGEiLCByZXN1bHREYXRhKTsKICAgICAgfQogICAgICB0aGlzLnRvQ2xvc2UoKTsKICAgIH0sCiAgICB0b0Nsb3NlKCkgewogICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLCAibmV4dFVzZXIiLCAiIik7CiAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICJjb21tZW50IiwgIiIpOwogICAgICB0aGlzLiRlbWl0KCJ0b0Nsb3NlIiwgImNsb3NlIik7CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqIA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/activiti_yxgc", "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      id=\"dialogAct\"\n      class=\"tree\"\n      :title=\"option.title ? option.title : ''\"\n      :top=\"option.top ? option.top : '10vh'\"\n      v-dialogDrag\n      :visible.sync=\"isShow\"\n      :width=\"option.width ? option.width : '25%'\"\n      :close-on-click-modal=\"false\"\n      @close=\"toClose\"\n      :destroy-on-colse=\"true\"\n      :modal-append-to-body=\"false\"\n    >\n      <slot name=\"Dialog_content\" />\n      <div v-if=\"datas.defaultFrom\">\n        <el-form\n          label-width=\"120px\"\n          ref=\"form\"\n          :model=\"form\"\n          v-if=\"datas.taskId === 'fgsldsh'\"\n        >\n          <div>\n            <el-row>\n              <div v-if=\"datas.processType === 'complete'\">\n                <el-col :span=\"24\">\n                  <el-form-item prop=\"nextUser2\" label=\"电力调度中心审核人\">\n                    <el-select\n                      v-model=\"form.nextUser2\"\n                      placeholder=\"请选择人员\"\n                      style=\"width: 100%;\"\n                      value-key=\"userName\"\n                      clearable\n                      filterable\n                      multiple\n                      disabled\n                    >\n                      <el-option\n                        v-for=\"item in dlddshrOptions\"\n                        :key=\"item.userName\"\n                        :label=\"item.nickName\"\n                        :value=\"item\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"24\">\n                  <el-form-item prop=\"nextUser\" label=\"科技中心审核人\">\n                    <el-select\n                      v-model=\"form.nextUser\"\n                      placeholder=\"请选择人员\"\n                      style=\"width: 100%;\"\n                      value-key=\"userName\"\n                      clearable\n                      filterable\n                    >\n                      <el-option\n                        v-for=\"item in kjzxshrOptions\"\n                        :key=\"item.userName\"\n                        :label=\"item.nickName\"\n                        :value=\"{\n                          userName: item.userName,\n                          nickName: item.nickName\n                        }\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n              </div>\n            </el-row>\n          </div>\n        </el-form>\n        <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" v-else>\n          <div>\n            <el-row>\n              <div v-if=\"datas.processType === 'complete'\">\n                <el-col :span=\"24\">\n                  <el-form-item prop=\"nextUser\" label=\"审核人\">\n                    <el-select\n                      v-model=\"form.nextUser\"\n                      placeholder=\"请选择人员\"\n                      style=\"width: 100%;\"\n                      value-key=\"userName\"\n                      clearable\n                      filterable\n                    >\n                      <el-option\n                        v-for=\"item in usersOptions\"\n                        :key=\"item.userName\"\n                        :label=\"item.nickName\"\n                        :value=\"{\n                          userName: item.userName,\n                          nickName: item.nickName\n                        }\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n              </div>\n              <el-col :span=\"24\">\n                <el-form-item\n                  prop=\"comment\"\n                  label=\"回退原因：\"\n                  v-if=\"datas.processType === 'rollback'\"\n                >\n                  <el-input\n                    style=\"width: 100%\"\n                    v-model=\"form.comment\"\n                    type=\"textarea\"\n                    placeholder=\"请输入原因\"\n                  />\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </div>\n        </el-form>\n      </div>\n      <div v-else>\n        <span>请确定是否提交</span>\n      </div>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"info\" @click=\"toClose\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"todoSubmit('form')\">确定</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { completeYxgcTask } from \"@/api/activiti/processTask\";\nimport { Loading } from \"element-ui\";\nimport { getUsers } from \"@/api/system/usergroup\";\n\nexport default {\n  name: \"index\",\n  props: {\n    /*流程发起必填数据\n    processData:{\n      processDefinitionKey:\"流程定义的key\",//必填\n      taskId:\"任务id，如果任务在代办列表时，会根据代办列表获取taskid，否则需传入业务id来确定task\",\n      businessKey:\"业务id，对应业务的主键\",//taskid和businessKey两者必须有其中一个才可以确定一个task\n      businessType:\"业务类型，用于区分不同的业务\"//必填\n      variables:\"拓展参数\"//流程定义中设置的参数,\n      nextUser:\"如果流程实例中并未配置每一个节点的处理人，则需要用户手动选择每一个节点的处理人\",\n      processType:'complete,rollback',\n      defaultFrom:true,false 是否需要默认表单\n    }*/\n    processData: {\n      type: Object,\n      default() {\n        return {\n          defaultFrom: true,\n          processType: \"complete\"\n        };\n      }\n    },\n    //显示隐藏\n    isShow: {\n      type: Boolean,\n      default: false\n    },\n    //子组件默认参数\n    option: {\n      type: Object,\n      default() {\n        return { title: \"审批\" };\n      }\n    }\n  },\n  data() {\n    return {\n      form: {},\n      datas: {},\n      loading: null,\n      dlddshrOptions: [],\n      kjzxshrOptions: [],\n      usersOptions: []\n    };\n  },\n  watch: {\n    processData: {\n      handler(newVal, oldVal) {\n        this.$nextTick(() => {\n          this.$set(this.form, \"nextUser2\", []);\n          this.datas = { ...newVal };\n          if (this.datas.taskId === \"fgsldsh\") {\n            getUsers({ personGroupId: 80, deptId: \"\", deptName: \"\" }).then(\n              res => {\n                this.dlddshrOptions = res.data;\n                this.form.nextUser2 = this.dlddshrOptions;\n              }\n            );\n            getUsers({ personGroupId: 81, deptId: \"\", deptName: \"\" }).then(\n              res => {\n                this.kjzxshrOptions = res.data;\n              }\n            );\n          } else {\n            if (this.datas.personGroupId) {\n              getUsers({\n                personGroupId: this.datas.personGroupId,\n                deptId: \"\",\n                deptName: \"\"\n              }).then(res => {\n                this.usersOptions = res.data;\n              });\n            }\n          }\n        });\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  mounted() {},\n  methods: {\n    //流程提交，流程拒绝\n    async todoSubmit() {\n      if (!this.datas.routePath) {\n        this.datas.routePath = this.$route.path;\n      }\n      if (this.form.nextUser) {\n        this.datas.nextUser = this.form.nextUser.userName;\n        this.datas.nextUserNickName = this.form.nextUser.nickName;\n      }\n\n      if (this.form.nextUser2 && this.form.nextUser2.length > 0) {\n        let nextUser = \"\";\n        let nextNick = \"\";\n        this.form.nextUser2.forEach(e => {\n          nextUser += e.userName + \",\";\n          nextNick += e.nickName + \",\";\n        });\n        //去掉最后一个逗号\n        if (nextUser.length > 0) {\n          nextUser = nextUser.substring(0, nextUser.length - 1);\n          nextNick = nextNick.substring(0, nextNick.length - 1);\n        }\n        this.datas.nextUser2 = nextUser;\n        this.datas.nextUserNickName2 = nextNick;\n      }\n\n      if (this.datas.taskId === \"fgsldsh\") {\n        if (!this.datas.nextUser || !this.datas.nextUser2) {\n          this.$message({\n            type: \"error\",\n            message: \"请选择人员!\"\n          });\n          return;\n        }\n      }\n      if (\n        !this.datas.nextUser &&\n        this.datas.processType === \"complete\" &&\n        this.datas.defaultFrom\n      ) {\n        this.$message({\n          type: \"error\",\n          message: \"请选择人员!\"\n        });\n        return;\n      }\n      if (this.form.comment) {\n        this.datas.variables.comment = this.form.comment;\n      }\n      this.$nextTick(() => {\n        // 以服务的方式调用的 Loading 需要异步关闭\n        this.loading = Loading.service({\n          lock: true, //lock的修改符--默认是false\n          text: \"流程进行中，请稍后\", //显示在加载图标下方的加载文案\n          spinner: \"el-icon-loading\", //自定义加载图标类名\n          background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n          target: document.querySelector(\"#dialogAct\")\n        });\n      });\n      let resultData;\n      try {\n        let { code, data } = await completeYxgcTask(this.datas);\n        if (code === \"0000\") {\n          resultData = data;\n        }\n        if (code) {\n          this.$nextTick(() => {\n            // 以服务的方式调用的 Loading 需要异步关闭\n            this.loading.close();\n          });\n        }\n      } catch (e) {\n        this.$nextTick(() => {\n          // 以服务的方式调用的 Loading 需要异步关闭\n          this.loading.close();\n        });\n      }\n      if (resultData) {\n        resultData.processType = this.datas.processType;\n        this.$emit(\"todoData\", resultData);\n      }\n      this.toClose();\n    },\n    toClose() {\n      this.$set(this.form, \"nextUser\", \"\");\n      this.$set(this.form, \"comment\", \"\");\n      this.$emit(\"toClose\", \"close\");\n    }\n  }\n};\n</script>\n\n<style scoped></style>\n"]}]}