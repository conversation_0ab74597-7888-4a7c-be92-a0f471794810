{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\blgk\\bzkwh.vue?vue&type=style&index=0&id=cd77faa0&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\blgk\\bzkwh.vue", "mtime": 1706897322268}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmhlYWQtY29udGFpbmVyIHsKICBtYXJnaW46IDAgYXV0bzsKICB3aWR0aDogOTglOwogIGhlaWdodDogODl2aDsKICBtYXgtaGVpZ2h0OiA4OXZoOwogIG92ZXJmbG93OiBhdXRvOwp9Ci8q57uZ5bem5L6n5pWw57uT5p6EaGVhZGVy5Yqg6aKc6ImyKi8KLmJveC1jYXJkIC5lbC1jYXJkX19oZWFkZXIgewogIGJhY2tncm91bmQ6ICMxMWJhNmQgIWltcG9ydGFudDsKfQouYm94LWNhcmQgewogIG1hcmdpbjowOwp9CgouaXRlbSB7CiAgd2lkdGg6IDIwMHB4OwogIGhlaWdodDogMTQ4cHg7CiAgZmxvYXQ6IGxlZnQ7Cn0KCi50cmVlIHsKICBvdmVyZmxvdy15OiBoaWRkZW47CiAgb3ZlcmZsb3cteDogc2Nyb2xsOwogIHdpZHRoOiA4MHB4OwogIGhlaWdodDogNTAwcHg7Cn0KCi5lbC10cmVlIHsKICBtaW4td2lkdGg6IDEwMCU7CiAgZGlzcGxheTogaW5saW5lLWJsb2NrICFpbXBvcnRhbnQ7Cn0KL2RlZXAvIC5lbC1kaWFsb2c6bm90KC5pcy1mdWxsc2NyZWVuKSB7CiAgbWFyZ2luLXRvcDogOHZoICFpbXBvcnRhbnQ7Cn0K"}, {"version": 3, "sources": ["bzkwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgwBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "bzkwh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/blgk", "sourcesContent": ["<template>\n  <div class=\"app-container\" id=\"sbqxDiv\">\n    <!--左侧树组件-->\n    <el-row :gutter=\"1\">\n      <!--   左侧树   -->\n      <el-col :span=\"4\">\n        <el-card style=\"background:#e0f8ed;padding-top:10px;\" shadow=\"never\">\n          <div class=\"text head-container\">\n            <el-col style=\"padding:0;\">\n              <el-tree\n                id=\"tree\"\n                :data=\"treeOptions\"\n                @node-click=\"handleNodeClick\"\n                :highlight-current=\"true\"\n                ref=\"tree\"\n                node-key=\"id\"\n                :default-checked-keys=\"['0']\"\n                :default-expanded-keys=\"['0']\"\n                accordion\n              />\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :btnHidden=\"false\"\n          :width=\"{ labelWidth: 80, itemWidth: 165 }\"\n          @handleReset=\"filterReset\"\n        />\n        <el-white class=\"button-group1\">\n          <div class=\"button_btn\">\n            <el-button type=\"primary\" icon=\"el-icon-plus\" v-hasPermi=\"['blgkbzk:button:add']\" @click=\"addForm('lx')\">新增不良工况类型</el-button>\n            <el-button type=\"primary\" icon=\"el-icon-plus\" v-hasPermi=\"['blgkbzk:button:add']\" @click=\"addForm('blgkms')\">新增不良工况描述</el-button>\n            <el-button type=\"primary\" icon=\"el-icon-plus\" v-hasPermi=\"['blgkbzk:button:add']\" @click=\"addForm('flyj')\">新增分类依据</el-button>\n          </div>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            height=\"67vh\"\n            v-loading=\"load\">\n            <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\" width=\"200\" :resizable=\"false\">\n              <template slot-scope=\"scope\">\n                <el-button type=\"text\" size=\"small\" @click=\"updateRow(scope.row)\" v-hasPermi=\"['blgkbzk:button:update']\" title=\"修改\"  class='el-icon-edit'/>\n                <el-button type=\"text\" size=\"small\" @click=\"viewFun(scope.row)\" title=\"查看\" class='el-icon-view'/>\n                <el-button type=\"text\" size=\"small\" @click=\"deleteRow(scope.row)\" v-hasPermi=\"['blgkbzk:button:delete']\" title=\"删除\" class='el-icon-delete'/>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!--  新增不良工况类型  -->\n    <el-dialog title=\"新增不良工况类型\" :visible.sync=\"isShowLx\" width=\"58%\" @close=\"closeFun('lx')\" v-dialogDrag>\n      <el-form label-width=\"140px\" :rules=\"lxRules\" :model=\"lxForm\" ref=\"lxForm\">\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sblx\" label=\"设备类型\">\n                <el-select placeholder=\"设备类型\" v-model=\"lxForm.sblx\" style=\"width:80%\" filterable clearable>\n                  <el-option\n                    v-for=\"item in allSblxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"不良工况类型:\" prop=\"type\">\n                <el-input v-model=\"lxForm.type\" placeholder=\"请输入类型\" style=\"width: 80%\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"dj\" label=\"等级\">\n                <el-select placeholder=\"等级\" v-model=\"lxForm.dj\" style=\"width:80%\" filterable clearable>\n                  <el-option\n                    v-for=\"item in djList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n<!--            <el-col :span=\"12\">-->\n<!--              <el-form-item label=\"扣分值:\" prop=\"kfz\">-->\n<!--                <el-input v-model=\"lxForm.kfz\" placeholder=\"请输入扣分值\" style=\"width: 80%\"/>-->\n<!--              </el-form-item>-->\n<!--            </el-col>-->\n            <el-col :span=\"12\">\n              <el-form-item prop=\"pjjg\" label=\"评价结果\">\n                <el-select placeholder=\"请选择评价结果\" v-model=\"lxForm.pjjg\" style=\"width:80%\" filterable clearable>\n                  <el-option\n                    v-for=\"item in pjjgList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"不良工况描述:\" prop=\"ms\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"lxForm.ms\" placeholder=\"请输入不良工况描述\" style=\"width: 92%\"/>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"lxForm.flyj\" placeholder=\"请输入分类依据\" style=\"width: 92%\"/>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun('lx')\" size=\"small\">取 消</el-button>\n        <el-button type=\"primary\" size=\"small\" @click=\"saveForm('lxForm')\">保存</el-button>\n      </div>\n    </el-dialog>\n\n    <!--  新增不良工况描述  -->\n    <el-dialog title=\"新增不良工况描述\" :visible.sync=\"isShowBlgkMs\" width=\"58%\" @close=\"closeFun('blgkms')\" v-dialogDrag>\n      <el-form label-width=\"140px\" :rules=\"blgkmsRules\" :model=\"blgkmsForm\" ref=\"blgkmsForm\">\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sblx\" label=\"设备类型\">\n                <el-select placeholder=\"设备类型\" @change=\"getLxList\" v-model=\"blgkmsForm.sblx\" style=\"width:80%\" filterable clearable>\n                  <el-option\n                    v-for=\"item in sblxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"不良工况类型:\" prop=\"type\">\n                <el-select placeholder=\"不良工况类型\" v-model=\"blgkmsForm.type\" style=\"width:80%\" filterable clearable>\n                  <el-option\n                    v-for=\"item in lxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"dj\" label=\"等级\">\n                <el-select placeholder=\"等级\" v-model=\"blgkmsForm.dj\" style=\"width:80%\" filterable clearable>\n                  <el-option\n                    v-for=\"item in djList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n<!--            <el-col :span=\"12\">-->\n<!--              <el-form-item label=\"扣分值:\" prop=\"kfz\">-->\n<!--                <el-input v-model=\"blgkmsForm.kfz\" placeholder=\"请输入扣分值\" style=\"width: 80%\"/>-->\n<!--              </el-form-item>-->\n<!--            </el-col>-->\n            <el-col :span=\"12\">\n              <el-form-item prop=\"pjjg\" label=\"评价结果\">\n                <el-select placeholder=\"请选择评价结果\" v-model=\"blgkmsForm.pjjg\" style=\"width:80%\" filterable clearable>\n                  <el-option\n                    v-for=\"item in pjjgList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"不良工况描述:\" prop=\"ms\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"blgkmsForm.ms\" placeholder=\"请输入不良工况描述\" style=\"width: 92%\"/>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"blgkmsForm.flyj\" placeholder=\"请输入分类依据\" style=\"width: 92%\"/>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun('qxms')\" size=\"small\">取 消</el-button>\n        <el-button type=\"primary\" size=\"small\" @click=\"saveForm('blgkmsForm')\">保存</el-button>\n      </div>\n    </el-dialog>\n\n    <!--  新增分类依据  -->\n    <el-dialog title=\"新增分类依据\" :visible.sync=\"isShowFlyj\"  width=\"58%\" @close=\"closeFun('flyj')\" v-dialogDrag>\n      <el-form label-width=\"140px\" :rules=\"flyjRules\" :model=\"flyjForm\" ref=\"flyjForm\">\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sblx\" label=\"设备类型\">\n                <el-select placeholder=\"设备类型\" v-model=\"flyjForm.sblx\" style=\"width:80%\" @change=\"getLxList\" filterable clearable>\n                  <el-option\n                    v-for=\"item in sblxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"不良工况类型:\" prop=\"type\">\n                <el-select placeholder=\"不良工况类型\" @change=\"getblgkmsList\" v-model=\"flyjForm.type\" style=\"width:80%\" filterable clearable>\n                  <el-option\n                    v-for=\"item in lxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"dj\" label=\"等级\">\n                <el-select placeholder=\"等级\" v-model=\"flyjForm.dj\" style=\"width:80%\" filterable clearable>\n                  <el-option\n                    v-for=\"item in djList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n<!--            <el-col :span=\"12\">-->\n<!--              <el-form-item label=\"扣分值:\" prop=\"kfz\">-->\n<!--                <el-input v-model=\"flyjForm.kfz\" placeholder=\"请输入扣分值\" style=\"width: 80%\"/>-->\n<!--              </el-form-item>-->\n<!--            </el-col>-->\n            <el-col :span=\"12\">\n              <el-form-item prop=\"pjjg\" label=\"评价结果\">\n                <el-select placeholder=\"请选择评价结果\" v-model=\"flyjForm.pjjg\" style=\"width:80%\" filterable clearable>\n                  <el-option\n                    v-for=\"item in pjjgList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"不良工况描述:\" prop=\"ms\">\n                <el-select placeholder=\"不良工况描述\" v-model=\"flyjForm.ms\" style=\"width:90%\" filterable clearable>\n                  <el-option\n                    v-for=\"item in blgkmsList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"flyjForm.flyj\" placeholder=\"请输入分类依据\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun('flyj')\" size=\"small\">取 消</el-button>\n        <el-button type=\"primary\" size=\"small\" @click=\"saveForm('flyjForm')\">保存</el-button>\n      </div>\n    </el-dialog>\n\n    <!--  详情查看  -->\n    <el-dialog title=\"详情查看\" :visible.sync=\"isShowDetail\" v-if=\"isShowDetail\"  width=\"58%\" @close=\"closeFun('view')\" v-dialogDrag>\n      <el-form label-width=\"140px\" :model=\"viewForm\" ref=\"viewForm\">\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>不良工况</span>\n          </div>\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sblxmc\" label=\"设备类型\">\n                <el-input v-model=\"viewForm.sblxmc\" placeholder=\"请输入设备类型\" style=\"width: 92%\" :disabled=\"true\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"type\" label=\"不良工况类型\">\n                <el-input v-model=\"viewForm.type\" placeholder=\"请输入不良工况类型\" style=\"width: 92%\" :disabled=\"true\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"dj\" label=\"等级\">\n                <el-input v-model=\"viewForm.dj\" placeholder=\"请输入不良工况等级\" style=\"width: 92%\" :disabled=\"true\"/>\n              </el-form-item>\n            </el-col>\n<!--            <el-col :span=\"12\">-->\n<!--              <el-form-item label=\"扣分值:\" prop=\"kfz\">-->\n<!--                <el-input v-model=\"viewForm.kfz\" placeholder=\"请输入扣分值\" style=\"width: 80%\" :disabled=\"true\"/>-->\n<!--              </el-form-item>-->\n<!--            </el-col>-->\n            <el-col :span=\"12\">\n              <el-form-item label=\"评价结果:\" prop=\"pjjgCn\">\n                <el-input v-model=\"viewForm.pjjgCn\" placeholder=\"请输入评价结果\" style=\"width: 80%\" :disabled=\"true\"/>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"不良工况描述:\" prop=\"ms\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"viewForm.ms\" placeholder=\"请输入不良工况描述\" style=\"width: 92%\" :disabled=\"true\"/>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"viewForm.flyj\" placeholder=\"请输入分类依据\" style=\"width: 92%\" :disabled=\"true\"/>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun('view')\" size=\"small\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n\n</template>\n\n<script>\nimport {getData,saveOrUpdate,removeBzk,getMs,getLx,getDeviceClassTreeNodeByPid} from '@/api/blgk/blgkbzk'\nimport {getAllSblxList,getBlgkTree} from '@/api/blgk/blgk'\nimport {getDictTypeData} from \"@/api/system/dict/data\";\n\nexport default {\n  name: 'bzkwh',\n  data() {\n    return {\n      load:false,\n      filterInfo: {\n        data: {\n          sblx:'',\n          ms: '',\n          flyj: '',\n          pjjgCn: '',\n        },\n        fieldList: [\n          { label: '设备类型', type: 'select', value: 'sblx',options:[]},\n          { label: '不良工况描述', type: 'input', value: 'ms'},\n          { label: '分类依据', type: 'input', value: 'flyj'},\n          { label: '评价结果', type: 'select', value: 'pjjgCn',options:[]},\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: 'sblxmc', label: '设备类型', minWidth: '140' },\n          { prop: 'type', label: '不良工况类型', minWidth: '140'},\n          { prop: 'ms', label: '不良工况描述', minWidth: '220',showPop:true},\n          { prop: 'flyj', label: '分类依据', minWidth: '220',showPop:true},\n          { prop: 'dj', label: '等级', minWidth: '100'},\n          { prop: 'pjjgCn', label: '评价结果', minWidth: '120'},\n          // { prop: 'kfz', label: '扣分值', minWidth: '80'},\n        ]\n      },\n      queryParams:{},\n      treeOptions: [], //组织树\n      treeNodeData:{},//点击后的树节点数据\n      isShowDetail:false,\n      isShowLx:false,\n      isShowBlgkMs:false,\n      isShowFlyj:false,\n      flyjForm:{},//表单\n      flyjRules:{\n        sblx: [\n          { required: true, message: '设备类型不能为空', trigger: 'select' }\n        ],\n        type: [\n          { required: true, message: '不良工况类型不能为空', trigger: 'select' }\n        ],\n        ms: [\n          { required: true, message: '不良工况描述不能为空', trigger: 'select' }\n        ],\n        flyj: [\n          { required: true, message: '分类依据不能为空', trigger: 'blur' }\n        ],\n        dj: [\n          { required: true, message: '等级不能为空', trigger: 'select' }\n        ],\n        pjjg: [\n          { required: true, message: '评价结果不能为空', trigger: 'select' }\n        ],\n        // kfz: [\n        //   {required: true, message: '扣分值不能为空', trigger: 'blur'},\n        //   {pattern: /^[+-]?(0|([1-9]\\d*))(\\.\\d+)?$/g,message: \"请输入数字\",trigger: \"blur\"},\n        // ],\n      },//校验规则\n      blgkmsForm:{},//表单\n      blgkmsRules:{\n        sblx: [\n          { required: true, message: '设备类型不能为空', trigger: 'select' }\n        ],\n        type: [\n          { required: true, message: '不良工况类型不能为空', trigger: 'select' }\n        ],\n        ms: [\n          { required: true, message: '不良工况描述不能为空', trigger: 'blur' }\n        ],\n        flyj: [\n          { required: true, message: '分类依据不能为空', trigger: 'blur' }\n        ],\n        dj: [\n          { required: true, message: '等级不能为空', trigger: 'select' }\n        ],\n        pjjg: [\n          { required: true, message: '评价结果不能为空', trigger: 'select' }\n        ],\n        // kfz: [\n        //   { required: true, message: '扣分值不能为空', trigger: 'blur' },\n        //   {pattern: /^[+-]?(0|([1-9]\\d*))(\\.\\d+)?$/g,message: \"请输入数字\",trigger: \"blur\"},\n        // ],\n      },//校验规则\n      lxForm:{},//表单\n      lxRules:{\n        sblx: [\n          { required: true, message: '设备类型不能为空', trigger: 'select' }\n        ],\n        type: [\n          { required: true, message: '不良工况类型不能为空', trigger: 'blur' }\n        ],\n        ms: [\n          { required: true, message: '不良工况描述不能为空', trigger: 'blur' }\n        ],\n        flyj: [\n          { required: true, message: '分类依据不能为空', trigger: 'blur' }\n        ],\n        dj: [\n          { required: true, message: '等级不能为空', trigger: 'select' }\n        ],\n        pjjg: [\n          { required: true, message: '评价结果不能为空', trigger: 'select' }\n        ],\n        // kfz: [\n        //   { required: true, message: '扣分值不能为空', trigger: 'blur' },\n        //   {pattern: /^[+-]?(0|([1-9]\\d*))(\\.\\d+)?$/g,message: \"请输入数字\",trigger: \"blur\"},\n        // ],\n      },//校验规则\n      sblxList:[],//设备类型下拉框选项\n      allSblxList:[],//设备类型下拉框选项\n      blgkmsList:[],//不良工况描述下拉框选项\n      lxList:[],//不良工况类型下拉框选项\n      viewForm:{},//查看表单\n      djList:[],//不良工况等级下拉框\n      pjjgList:[],//评价结果下拉框\n    }\n  },\n  created() {\n    this.getData();\n    this.getTreeData();\n    //设备类型下拉框\n    this.getSblxList();\n    //设备类型下拉框\n    this.getAllSblxList();\n    //不良工况等级下拉框\n    this.getDjList();\n    //评价结果下拉框\n    this.getPjjgList();\n  },\n  methods: {\n    //评价结果\n    async getPjjgList() {\n      getDictTypeData('pjgz_pjjg').then(res => {\n        res.data.forEach(item => {\n          this.pjjgList.push({label: item.label, value: item.numvalue})\n        })\n        this.filterInfo.fieldList.forEach(item => {\n          if (item.value === 'pjjgCn') {\n            item.options = this.pjjgList;\n            return false;\n          }\n        })\n      })\n    },\n    //获取不良工况等级下拉框字典\n    getDjList(){\n      getDictTypeData('blgk_dj').then(res=>{\n        res.data.forEach(item=>{\n          this.djList.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    //获取所有设备类型下拉框用于查询\n    getSblxList(){\n      getAllSblxList({zy:'bdsb'}).then(res=>{\n        this.sblxList = res.data;\n        this.filterInfo.fieldList.forEach(item=>{\n          if(item.value === 'sblx'){\n            item.options = res.data;\n            return false;\n          }\n        })\n      })\n    },\n    //获取所有设备类型下拉框用于查询\n    getAllSblxList(){\n      this.allSblxList = [];\n      getDeviceClassTreeNodeByPid({pid:'bdsb',sbpLogo:[\"输电设备\", \"变电设备\", \"配电设备\"]}).then(res=>{\n        if(res.data){\n          res.data.forEach(item=>{\n            this.allSblxList.push({label:item.name,value:item.code});\n          })\n        }\n      })\n    },\n    //获取不良工况类型下拉框\n    async getLxList(val){\n      this.lxList = [];\n      this.$set(this.flyjForm,'type','');\n      this.$set(this.blgkmsForm,'type','');\n      this.$set(this.lxForm,'type','');\n      this.$set(this.flyjForm,'ms','');\n      this.$set(this.blgkmsForm,'ms','');\n      this.$set(this.lxForm,'ms','');\n      //获取不良工况类型下拉框\n      await getLx({sblx:val}).then(res => {\n        this.lxList = res.data;\n      })\n    },\n    //获取不良工况描述下拉框\n    async getblgkmsList(val){\n      this.blgkmsList = [];\n      this.$set(this.flyjForm,'ms','');\n      this.$set(this.blgkmsForm,'ms','');\n      this.$set(this.lxForm,'ms','');\n\n      let type = '';\n      this.lxList.forEach(item=>{\n        if(item.value == val){\n          type = item.label;\n          return false;\n        }\n      })\n      //获取不良工况描述下拉框\n      await getMs({sblx:this.flyjForm.sblx,type: type}).then(res => {\n        this.blgkmsList = res.data;\n      })\n    },\n    //编辑\n    async updateRow(row){\n      this.flyjForm = {...row};\n      //下拉框回显\n      await this.getLxList(row.sblx);\n      await this.getblgkmsList(row.type);\n      this.$set(this.flyjForm,'type',row.type);\n      this.$set(this.flyjForm,'ms',row.ms);\n      this.isShowDetail = false;\n      this.isShowFlyj = true;\n    },\n    //删除\n    deleteRow(row){\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        removeBzk([row.objId]).then(res => {\n          if(res.code === '0000'){\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            });\n            this.getData();\n          }else{\n            this.$message({\n              type: 'error',\n              message: '删除失败!'\n            });\n          }\n        })\n      })\n    },\n    //查看\n    viewFun(row){\n      this.viewForm = {...row};\n      this.isShowDetail = true;\n    },\n    //新增\n    async addForm(formType){\n      //先清空下拉框的值\n      this.blgkmsList = [];\n      this.isShowDetail = false;\n      //如果树节点有值，则带过来\n      if(this.queryParams.sblx){\n        await this.getblgkmsList(this.queryParams.sblx);\n      }\n      switch (formType){\n        case 'lx'://类型\n          this.lxForm = {};\n          this.$set(this.lxForm,'sblx',this.queryParams.sblx);\n          this.isShowLx = true;\n          break;\n        case 'blgkms'://描述\n          this.blgkmsForm = {};\n          this.$set(this.blgkmsForm,'sblx',this.queryParams.sblx);\n          this.isShowBlgkMs = true;\n          break;\n        case 'flyj'://分类依据\n          this.flyjForm = {};\n          this.$set(this.flyjForm,'sblx',this.queryParams.sblx);\n          this.isShowFlyj = true;\n          break;\n        default:\n          break;\n      }\n    },\n    //保存\n    async saveForm(formType){\n      await this.$refs[formType].validate((valid) => {\n        if (valid) {\n          let saveForm = {};\n          switch (formType){\n            case 'flyjForm'://新增分类依据\n              saveForm = {...saveForm,...this.flyjForm};\n              this.blgkmsList.forEach(item=>{\n                if(item.value === saveForm.ms){\n                  saveForm.ms = item.label;\n                }\n              })\n              this.lxList.forEach(item=>{\n                if(item.value === saveForm.type){\n                  saveForm.type = item.label;\n                }\n              })\n              break;\n            case 'blgkmsForm'://新增不良工况描述\n              saveForm = {...saveForm,...this.blgkmsForm};\n              this.lxList.forEach(item=>{\n                if(item.value === saveForm.type){\n                  saveForm.type = item.label;\n                }\n              })\n              break;\n            case 'lxForm'://新增不良工况类型\n              saveForm = {...saveForm,...this.lxForm};\n              break;\n            default:\n              break;\n          }\n          saveOrUpdate(saveForm).then(res=>{\n            if (res.code === '0000') {\n              this.$message.success('操作成功');\n              this.isShowFlyj = false;\n              this.isShowBlgkMs = false;\n              this.isShowDetail = false;\n              this.isShowLx = false;\n              this.getData();\n              //如果是新增类型，保存后需要重新刷新左侧树列表,及其他新增弹框中的设备类型选项\n              if(formType == 'lxForm'){\n                this.getTreeData();\n                //设备类型下拉框\n                this.getSblxList();\n              }\n              //关闭弹框\n            } else {\n              this.$message.error('操作失败')\n            }\n          });\n        } else {\n          this.$message.error('校验未通过！')\n          return false\n        }\n      })\n    },\n    //关闭\n    closeFun(type){\n      this.isShowDetail = false;\n      switch (type){\n        case 'lx':\n          this.isShowLx = false;\n          break;\n        case 'blgkms':\n          this.isShowBlgkMs = false;\n          break;\n        case 'flyj':\n          this.isShowFlyj = false;\n          break;\n        case 'view':\n          this.isShowDetail = false;\n          break;\n        default:\n          this.isShowLx = false;\n          this.isShowBlgkMs = false;\n          this.isShowFlyj = false;\n          this.isShowDetail = false;\n          break;\n      }\n    },\n    //重置按钮\n    filterReset() {\n      this.queryParams = {qxlb:this.qxlb};//重置条件\n    },\n    getTreeData(){\n      getBlgkTree({zy:'bdsb'}).then(res=>{\n        this.treeOptions = res.data;\n      })\n    },\n    //树节点点击事件\n    handleNodeClick(node) {\n      this.treeNodeData = node;\n      if (node.identifier === '1') {//设备类型一级\n        this.queryParams.sblx = node.id;\n      } else{ //上一级\n        this.queryParams.sblx = '';\n      }\n      this.getData()\n    },\n    //查询列表\n    getData(params) {\n      this.load = true\n      this.queryParams = {...this.queryParams, ...params}\n      getData(this.queryParams).then(res => {\n        this.tableAndPageInfo.tableData = res.data.records\n        this.tableAndPageInfo.pager.total = res.data.total\n        this.load = false\n      })\n    },\n  }\n}\n</script>\n\n<style lang='scss' scoped>\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  height: 89vh;\n  max-height: 89vh;\n  overflow: auto;\n}\n/*给左侧数结构header加颜色*/\n.box-card .el-card__header {\n  background: #11ba6d !important;\n}\n.box-card {\n  margin:0;\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.tree {\n  overflow-y: hidden;\n  overflow-x: scroll;\n  width: 80px;\n  height: 500px;\n}\n\n.el-tree {\n  min-width: 100%;\n  display: inline-block !important;\n}\n/deep/ .el-dialog:not(.is-fullscreen) {\n  margin-top: 8vh !important;\n}\n</style>\n<style>\n\n</style>\n\n\n\n\n\n"]}]}