{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\todoitem\\history.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\todoitem\\history.vue", "mtime": 1706897322086}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["history.vue"], "names": [], "mappings": ";;;;;;;;;AAkCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;AACA,EAAA,IAAA,EAAA,iBADA;AAEA,EAAA,KAAA,EAAA;AACA,IAAA,UAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAAA,EAAA;AAFA;AADA,GAFA;AAQA,EAAA,IARA,kBAQA;AACA,WAAA;AACA,MAAA,GAAA,EAAA,KAAA,UADA;AAEA;AACA,MAAA,OAAA,EAAA,IAHA;AAIA;AACA,MAAA,GAAA,EAAA,EALA;AAMA;AACA,MAAA,MAAA,EAAA,IAPA;AAQA;AACA,MAAA,QAAA,EAAA,IATA;AAUA;AACA,MAAA,UAAA,EAAA,IAXA;AAYA,MAAA,SAAA,EAAA,EAZA;AAaA,MAAA,UAAA,EAAA,EAbA;AAcA;AACA,MAAA,KAAA,EAAA,CAfA;AAgBA;AACA,MAAA,WAAA,EAAA,IAjBA;AAkBA;AACA;AACA;AACA,MAAA,IAAA,EAAA,EArBA;AAsBA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,SAHA;AAIA,QAAA,QAAA,EAAA;AAJA;AAvBA,KAAA;AA8BA,GAvCA;AAwCA,EAAA,KAAA,EAAA;AACA;AACA,IAAA,UAFA,sBAEA,GAFA,EAEA;AACA,WAAA,GAAA,GAAA,GAAA;AACA,WAAA,OAAA;AACA;AALA,GAxCA;AAgDA,EAAA,OAhDA,qBAgDA;AACA,SAAA,OAAA;AACA,GAlDA;AAmDA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,OAFA,qBAEA;AAAA;;AACA,WAAA,OAAA,GAAA,IAAA;AACA,WAAA,WAAA,CAAA,UAAA,GAAA,KAAA,GAAA;AACA,gCAAA,KAAA,WAAA,EAAA,IAAA,CACA,UAAA,QAAA,EAAA;AACA,QAAA,KAAA,CAAA,WAAA,GAAA,QAAA,CAAA,IAAA;AACA,QAAA,KAAA,CAAA,KAAA,GAAA,QAAA,CAAA,IAAA,CAAA,MAAA;AACA,QAAA,KAAA,CAAA,OAAA,GAAA,KAAA;AACA,OALA;AAOA,KAZA;;AAaA;;;AAGA,IAAA,MAhBA,oBAgBA;AACA,WAAA,IAAA,GAAA,KAAA;AACA,WAAA,KAAA;AACA,KAnBA;;AAoBA;;;AAGA,IAAA,KAvBA,mBAuBA;AACA,WAAA,IAAA,GAAA;AACA,QAAA,IAAA,EAAA,SADA;AAEA,QAAA,IAAA,EAAA,SAFA;AAGA,QAAA,WAAA,EAAA;AAHA,OAAA;AAKA,WAAA,SAAA,CAAA,MAAA;AACA,KA9BA;;AA+BA;AACA,IAAA,WAhCA,yBAgCA;AACA,WAAA,WAAA,CAAA,OAAA,GAAA,CAAA;AACA,WAAA,OAAA;AACA,KAnCA;;AAoCA;AACA,IAAA,UArCA,wBAqCA;AACA,WAAA,SAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,WAAA;AACA,WAAA,WAAA;AACA,KAzCA;;AA0CA;AACA,IAAA,qBA3CA,iCA2CA,SA3CA,EA2CA;AACA,WAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,MAAA;AAAA,OAAA,CAAA;AACA,WAAA,MAAA,GAAA,SAAA,CAAA,MAAA,IAAA,CAAA;AACA,WAAA,QAAA,GAAA,CAAA,SAAA,CAAA,MAAA;AACA;AA/CA;AAnDA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form class=\"searchForm1\" :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"100px\">\n          <el-form-item label=\"节点名称：\" prop=\"activityName\">\n            <el-input v-model=\"queryParams.activityName\" placeholder=\"请输入节点名称\" clearable\n                      @keyup.enter.native=\"handleQuery\"/>\n          </el-form-item>\n          <el-form-item label=\"办理人ID：\" prop=\"assignee\">\n            <el-input v-model=\"queryParams.assignee\" placeholder=\"请输入办理人ID\" clearable\n                      @keyup.enter.native=\"handleQuery\"/>\n          </el-form-item>\n        <el-form-item>\n          <el-button type=\"cyan\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\n          <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\" type=\"warning\">重置</el-button>\n        </el-form-item>\n    </el-form>\n    <el-row :gutter=\"20\">\n      <el-col :span=\"24\">\n        <el-white class=\"button-group\">\n          <el-table style=\"margin-top: 15px;\" stripe border v-loading=\"loading\" :data=\"historyList\" height=\"400\" @selection-change=\"handleSelectionChange\">\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n            <el-table-column label=\"节点名称\" align=\"center\" prop=\"activityName\" fixed  />\n            <el-table-column label=\"处理人\" align=\"center\" prop=\"assigneeName\" />\n            <el-table-column label=\"审批意见\" align=\"center\" prop=\"comment\" width=\"160\"/>\n            <el-table-column label=\"提报时间\" align=\"center\" prop=\"startTime\" width=\"160\" />\n            <el-table-column label=\"审批时间\" align=\"center\" prop=\"endTime\" width=\"160\"/>\n          </el-table>\n        </el-white>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\n  import { historyList } from \"@/api/activiti/process\";\n  export default {\n    name: \"ActivitiHistory\",\n    props:{\n      instanceId:{\n        type: String,\n        default: null\n      }\n    },\n    data() {\n      return {\n        ins:this.instanceId,\n        // 遮罩层\n        loading: true,\n        // 选中数组\n        ids: [],\n        // 非单个禁用\n        single: true,\n        // 非多个禁用\n        multiple: true,\n        // 显示搜索条件\n        showSearch: true,\n        dateRange: [],\n        dateRange1:[],\n        // 总条数\n        total: 0,\n        // 用户表格数据\n        historyList: null,\n        // 是否显示弹出层\n        // 部门名称\n        // 表单参数\n        form: {},\n        // 查询参数\n        queryParams: {\n          pageNum: 1,\n          pageSize: 10,\n          activityName: undefined,\n          assignee: undefined,\n        },\n      };\n    },\n    watch: {\n      //监听父组件传值\n      instanceId(val) {\n        this.ins = val;\n        this.getList();\n      },\n    },\n\n    created() {\n      this.getList();\n    },\n    methods: {\n      /** 查询用户列表 */\n      getList() {\n        this.loading = true;\n        this.queryParams.instanceId = this.ins;\n        historyList(this.queryParams).then(\n          (response) => {\n            this.historyList = response.data;\n            this.total = response.data.length;\n            this.loading = false;\n          }\n        );\n      },\n      /**\n       * 取消按钮\n       * */\n      cancel() {\n        this.open = false;\n        this.reset();\n      },\n      /**\n       * 表单重置\n       * */\n      reset() {\n        this.form = {\n          pass: undefined,\n          name: undefined,\n          description:undefined\n        };\n        this.resetForm(\"form\");\n      },\n      /** 搜索按钮操作 */\n      handleQuery() {\n        this.queryParams.pageNum = 1;\n        this.getList();\n      },\n      /** 重置按钮操作 */\n      resetQuery() {\n        this.dateRange = [];\n        this.resetForm(\"queryForm\");\n        this.handleQuery();\n      },\n      /***多选框选中数据***/\n      handleSelectionChange(selection) {\n        this.ids = selection.map((item) => item.userId);\n        this.single = selection.length != 1;\n        this.multiple = !selection.length;\n      },\n    },\n  };\n</script>\n"], "sourceRoot": "src/views/activiti/todoitem"}]}