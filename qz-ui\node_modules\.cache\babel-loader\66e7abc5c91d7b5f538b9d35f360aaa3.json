{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\utils\\print\\htmlToPdf.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\utils\\print\\htmlToPdf.js", "mtime": 1706897321865}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/utils/print/htmlToPdf.js"], "names": ["downloadPDF", "ele", "pdfName", "Promise", "resolve", "reject", "eleW", "offsetWidth", "eleH", "offsetHeight", "eleOffsetTop", "offsetTop", "eleOffsetLeft", "offsetLeft", "canvas", "document", "createElement", "abs", "win_in", "documentElement", "clientWidth", "body", "win_out", "window", "innerWidth", "width", "height", "context", "getContext", "scale", "translate", "dpi", "useCORS", "then", "contentWidth", "contentHeight", "pageHeight", "leftHeight", "position", "imgWidth", "imgHeight", "pageData", "toDataURL", "pdf", "JsPDF", "addImage", "addPage", "name", "outPutPdfFn", "splitClassName", "A4_WIDTH", "A4_HEIGHT", "style", "padding", "scrollWidth", "domList", "getElementsByClassName", "pageNum", "eleBounding", "getBoundingClientRect", "i", "length", "node", "bound", "offset2Ele", "top", "currentPage", "Math", "ceil", "bottom", "divParent", "parentNode", "newNode", "className", "background", "next", "nextS<PERSON>ling", "insertBefore", "append<PERSON><PERSON><PERSON>", "zipChange", "promises", "title", "funct", "all", "pdfs", "zip", "JSZip", "for<PERSON>ach", "item", "index", "save", "file", "output", "generateAsync", "type", "content", "FileSaver", "saveAs"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEA;;AACA;;AAEA;;AACA;;AANA;AACA;;AAMA;;;;AAKA,SAASA,WAAT,CAAqBC,GAArB,EAA0BC,OAA1B,EAAmC;AAC/B,SAAO,IAAIC,OAAJ,CAAY,UAACC,OAAD,EAASC,MAAT,EAAkB;AAGjC,QAAIC,IAAI,GAAGL,GAAG,CAACM,WAAf,CAHiC,CAGN;;AAC3B,QAAIC,IAAI,GAAGP,GAAG,CAACQ,YAAf,CAJiC,CAIL;;AAC5B,QAAIC,YAAY,GAAGT,GAAG,CAACU,SAAvB,CALiC,CAKE;;AACnC,QAAIC,aAAa,GAAGX,GAAG,CAACY,UAAxB,CANiC,CAMG;;AACpC,QAAIC,MAAM,GAAGC,QAAQ,CAACC,aAAT,CAAuB,QAAvB,CAAb;AACA,QAAIC,GAAG,GAAG,CAAV;AAEA,QAAIC,MAAM,GAAGH,QAAQ,CAACI,eAAT,CAAyBC,WAAzB,IAAwCL,QAAQ,CAACM,IAAT,CAAcD,WAAnE,CAViC,CAU+C;;AAChF,QAAIE,OAAO,GAAGC,MAAM,CAACC,UAArB,CAXiC,CAWA;;AAEjC,QAAIF,OAAO,GAAGJ,MAAd,EAAsB;AAClB;AACAD,MAAAA,GAAG,GAAG,CAACK,OAAO,GAAGJ,MAAX,IAAqB,CAA3B,CAFkB,CAEe;AACjC;AACH;;AACDJ,IAAAA,MAAM,CAACW,KAAP,GAAenB,IAAI,GAAG,CAAtB,CAlBiC,CAkBL;;AAC5BQ,IAAAA,MAAM,CAACY,MAAP,GAAgBlB,IAAI,GAAG,CAAvB;AAEA,QAAImB,OAAO,GAAGb,MAAM,CAACc,UAAP,CAAkB,IAAlB,CAAd;AACAD,IAAAA,OAAO,CAACE,KAAR,CAAc,CAAd,EAAiB,CAAjB;AACAF,IAAAA,OAAO,CAACG,SAAR,CAAkB,CAAClB,aAAD,GAAiBK,GAAnC,EAAwC,CAACP,YAAzC,EAvBiC,CAwBjC;AACA;AAEA;AACA;;AACA,8BAAYT,GAAZ,EAAiB;AACb8B,MAAAA,GAAG,EAAE,GADQ;AAEb;AACAC,MAAAA,OAAO,EAAE,IAHI,CAGE;;AAHF,KAAjB,EAIGC,IAJH,CAIQ,UAACnB,MAAD,EAAY;AAChB,UAAIoB,YAAY,GAAGpB,MAAM,CAACW,KAA1B;AACA,UAAIU,aAAa,GAAGrB,MAAM,CAACY,MAA3B,CAFgB,CAGhB;;AACA,UAAIU,UAAU,GAAGF,YAAY,GAAG,MAAf,GAAwB,MAAzC,CAJgB,CAKhB;;AACA,UAAIG,UAAU,GAAGF,aAAjB,CANgB,CAOhB;;AACA,UAAIG,QAAQ,GAAG,CAAf,CARgB,CAShB;;AACA,UAAIC,QAAQ,GAAG,MAAf;AACA,UAAIC,SAAS,GAAG,SAASN,YAAT,GAAwBC,aAAxC;AACA,UAAIM,QAAQ,GAAG3B,MAAM,CAAC4B,SAAP,CAAiB,YAAjB,EAA+B,GAA/B,CAAf;AACA,UAAIC,GAAG,GAAG,IAAIC,cAAJ,CAAU,EAAV,EAAc,IAAd,EAAoB,IAApB,CAAV,CAbgB,CAchB;AACA;;AACA,UAAIP,UAAU,GAAGD,UAAjB,EAA6B;AACzB;AACAO,QAAAA,GAAG,CAACE,QAAJ,CAAaJ,QAAb,EAAuB,MAAvB,EAA+B,CAA/B,EAAkC,CAAlC,EAAqCF,QAArC,EAA+CC,SAA/C,EAFyB,CAGzB;AACH,OAJD,MAIO;AAAK;AACR,eAAOH,UAAU,GAAG,CAApB,EAAuB;AACnBM,UAAAA,GAAG,CAACE,QAAJ,CAAaJ,QAAb,EAAuB,MAAvB,EAA+B,CAA/B,EAAkCH,QAAlC,EAA4CC,QAA5C,EAAsDC,SAAtD;AACAH,UAAAA,UAAU,IAAID,UAAd;AACAE,UAAAA,QAAQ,IAAI,MAAZ,CAHmB,CAInB;;AACA,cAAID,UAAU,GAAG,CAAjB,EAAoB;AAChBM,YAAAA,GAAG,CAACG,OAAJ;AACH;AACJ;AACJ,OA9Be,CA+BhB;;;AACA1C,MAAAA,OAAO,CAAC;AAACuC,QAAAA,GAAG,EAACA,GAAL;AAASI,QAAAA,IAAI,EAAC7C,OAAO,GAAC;AAAtB,OAAD,CAAP;AACH,KArCD;AAsCH,GAnEM,CAAP;AAqEH;;AAED,SAAS8C,WAAT,CAAqB/C,GAArB,EAA0BgD,cAA1B,EAA0C;AACtC,MAAMC,QAAQ,GAAG,MAAjB;AAAA,MAAyBC,SAAS,GAAG,MAArC;AACAlD,EAAAA,GAAG,CAACmD,KAAJ,CAAU1B,MAAV,GAAmB,SAAnB;AACAzB,EAAAA,GAAG,CAACmD,KAAJ,CAAUC,OAAV,GAAoB,MAApB;AACA,MAAIjB,UAAU,GAAGnC,GAAG,CAACqD,WAAJ,GAAkBJ,QAAlB,GAA6BC,SAA9C,CAJsC,CAKtC;;AACA,MAAII,OAAO,GAAGxC,QAAQ,CAACyC,sBAAT,CAAgCP,cAAhC,CAAd,CANsC,CAOtC;;AACA,MAAIQ,OAAO,GAAG,CAAd,CARsC,CAQrB;;AACjB,MAAIC,WAAW,GAAGzD,GAAG,CAAC0D,qBAAJ,EAAlB;;AACA,OAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGL,OAAO,CAACM,MAA5B,EAAoCD,CAAC,EAArC,EAAyC;AACrC,QAAIE,IAAI,GAAGP,OAAO,CAACK,CAAD,CAAlB;AACA,QAAIG,KAAK,GAAGD,IAAI,CAACH,qBAAL,EAAZ;AACA,QAAIK,UAAU,GAAGD,KAAK,CAACE,GAAN,GAAYP,WAAW,CAACO,GAAzC;AACA,QAAIC,WAAW,GAAGC,IAAI,CAACC,IAAL,CAAU,CAACL,KAAK,CAACM,MAAN,GAAeX,WAAW,CAACO,GAA5B,IAAmC7B,UAA7C,CAAlB,CAJqC,CAIuC;;AAC5E,QAAIqB,OAAO,GAAGS,WAAd,EAA2B;AACvBT,MAAAA,OAAO;AACP,UAAIa,SAAS,GAAGf,OAAO,CAACK,CAAD,CAAP,CAAWW,UAA3B,CAFuB,CAEgB;;AACvC,UAAIC,OAAO,GAAGzD,QAAQ,CAACC,aAAT,CAAuB,IAAvB,CAAd;AACAwD,MAAAA,OAAO,CAACC,SAAR,GAAoB,UAApB;AACAD,MAAAA,OAAO,CAACpB,KAAR,CAAcsB,UAAd,GAA2B,OAA3B;AACAF,MAAAA,OAAO,CAACpB,KAAR,CAAc1B,MAAd,GAAwBU,UAAU,IAAIqB,OAAO,GAAG,CAAd,CAAV,GAA6BO,UAA7B,GAA0C,EAA3C,GAAiD,IAAxE,CANuB,CAMuD;;AAC9EQ,MAAAA,OAAO,CAACpB,KAAR,CAAc3B,KAAd,GAAsB,MAAtB;AACA,UAAIkD,IAAI,GAAGpB,OAAO,CAACK,CAAD,CAAP,CAAWgB,WAAtB,CARuB,CAQY;AACnC;;AACA,UAAID,IAAJ,EAAU;AACN;AACAL,QAAAA,SAAS,CAACO,YAAV,CAAuBL,OAAvB,EAAgCV,IAAhC;AACH,OAHD,MAGO;AACH;AACAQ,QAAAA,SAAS,CAACQ,WAAV,CAAsBN,OAAtB;AACH;AACJ;AACJ;AACJ;;AACD,SAASO,SAAT,CAAmBC,QAAnB,EAA4BC,KAA5B,EAAkCC,KAAlC,EAAyC;AACrC/E,EAAAA,OAAO,CAACgF,GAAR,CAAYH,QAAZ,EAAsB/C,IAAtB;AAAA,qFAA2B,kBAAMmD,IAAN;AAAA;AAAA;AAAA;AAAA;AAAA;AACnBC,cAAAA,GADmB,GACb,IAAIC,cAAJ,EADa;AAEzBN,cAAAA,QAAQ,CAACO,OAAT;AAAA,kGAAiB,iBAAMC,IAAN,EAAYC,KAAZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAEJ9C,0BAAAA,GAFI,GAEQ6C,IAFR,CAEJ7C,GAFI,EAEAI,IAFA,GAEQyC,IAFR,CAEAzC,IAFA;;AAAA,gCAGTiC,QAAQ,CAACnB,MAAT,KAAoB,CAHX;AAAA;AAAA;AAAA;;AAIXlB,0BAAAA,GAAG,CAAC+C,IAAJ,CAAS3C,IAAT;AAJW;AAAA;;AAAA;AAAA;AAAA,iCAMLsC,GAAG,CAACM,IAAJ,CAAS5C,IAAT,EAAeJ,GAAG,CAACiD,MAAJ,CAAW,MAAX,CAAf,CANK;;AAAA;AAQbV,0BAAAA,KAAK,IAAEA,KAAK,CAACM,IAAD,EAAMC,KAAN,CAAZ;;AARa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAAjB;;AAAA;AAAA;AAAA;AAAA;;AAUJ,kBAAIT,QAAQ,CAACnB,MAAT,GAAkB,CAAtB,EAAyB;AACvBwB,gBAAAA,GAAG,CAACQ,aAAJ,CAAkB;AAACC,kBAAAA,IAAI,EAAE;AAAP,iBAAlB,EAAkC7D,IAAlC,CAAuC,UAAC8D,OAAD,EAAY;AACjDC,qCAAUC,MAAV,CAAiBF,OAAjB,EAA0Bd,KAAK,GAAG,MAAlC;AACD,iBAFD;AAGD;;AAhB4B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAA3B;;AAAA;AAAA;AAAA;AAAA;AAkBH;;eAEc;AACXjF,EAAAA,WAAW,EAAXA,WADW;AACEgD,EAAAA,WAAW,EAAXA,WADF;AACe+B,EAAAA,SAAS,EAATA;AADf,C", "sourcesContent": ["/* eslint-disable */\n//不使用JQuery版的\nimport html2canvas from 'html2canvas';\nimport JsPD<PERSON> from 'jspdf';\n\nimport JSZip from 'jszip'\nimport FileSaver from 'file-saver'\n/**\n * @param  ele          要生成 pdf 的DOM元素（容器）\n * @param  padfName     PDF文件生成后的文件名字\n * */\n\nfunction downloadPDF(ele, pdfName) {\n    return new Promise((resolve,reject)=>{\n\n\n        let eleW = ele.offsetWidth;// 获得该容器的宽\n        let eleH = ele.offsetHeight;// 获得该容器的高\n        let eleOffsetTop = ele.offsetTop;  // 获得该容器到文档顶部的距离\n        let eleOffsetLeft = ele.offsetLeft; // 获得该容器到文档最左的距离\n        var canvas = document.createElement(\"canvas\");\n        var abs = 0;\n\n        let win_in = document.documentElement.clientWidth || document.body.clientWidth; // 获得当前可视窗口的宽度（不包含滚动条）\n        let win_out = window.innerWidth; // 获得当前窗口的宽度（包含滚动条）\n\n        if (win_out > win_in) {\n            // abs = (win_o - win_i)/2;    // 获得滚动条长度的一半\n            abs = (win_out - win_in) / 2;    // 获得滚动条宽度的一半\n            // console.log(a, '新abs');\n        }\n        canvas.width = eleW * 2;    // 将画布宽&&高放大两倍\n        canvas.height = eleH * 2;\n\n        var context = canvas.getContext(\"2d\");\n        context.scale(2, 2);\n        context.translate(-eleOffsetLeft - abs, -eleOffsetTop);\n        // 这里默认横向没有滚动条的情况，因为offset.left(),有无滚动条的时候存在差值，因此\n        // translate的时候，要把这个差值去掉\n\n        // html2canvas(element).then( (canvas)=>{ //报错\n        // html2canvas(element[0]).then( (canvas)=>{\n        html2canvas(ele, {\n            dpi: 300,\n            // allowTaint: true,  //允许 canvas 污染， allowTaint参数要去掉，否则是无法通过toDataURL导出canvas数据的\n            useCORS: true  //允许canvas画布内 可以跨域请求外部链接图片, 允许跨域请求。\n        }).then((canvas) => {\n            var contentWidth = canvas.width;\n            var contentHeight = canvas.height;\n            //一页pdf显示html页面生成的canvas高度;\n            var pageHeight = contentWidth / 592.28 * 841.89;\n            //未生成pdf的html页面高度\n            var leftHeight = contentHeight;\n            //页面偏移\n            var position = 0;\n            //a4纸的尺寸[595.28,841.89]，html页面生成的canvas在pdf中图片的宽高\n            var imgWidth = 595.28;\n            var imgHeight = 595.28 / contentWidth * contentHeight;\n            var pageData = canvas.toDataURL('image/jpeg', 1.0);\n            var pdf = new JsPDF('', 'pt', 'a4');\n            //有两个高度需要区分，一个是html页面的实际高度，和生成pdf的页面高度(841.89)\n            //当内容未超过pdf一页显示的范围，无需分页\n            if (leftHeight < pageHeight) {\n                //在pdf.addImage(pageData, 'JPEG', 左，上，宽度，高度)设置在pdf中显示；\n                pdf.addImage(pageData, 'JPEG', 0, 0, imgWidth, imgHeight);\n                // pdf.addImage(pageData, 'JPEG', 20, 40, imgWidth, imgHeight);\n            } else {    // 分页\n                while (leftHeight > 0) {\n                    pdf.addImage(pageData, 'JPEG', 0, position, imgWidth, imgHeight);\n                    leftHeight -= pageHeight;\n                    position -= 841.89;\n                    //避免添加空白页\n                    if (leftHeight > 0) {\n                        pdf.addPage();\n                    }\n                }\n            }\n            //可动态生成\n            resolve({pdf:pdf,name:pdfName+\".pdf\"})\n        })\n    })\n\n}\n\nfunction outPutPdfFn(ele, splitClassName) {\n    const A4_WIDTH = 595.28, A4_HEIGHT = 841.89;\n    ele.style.height = 'initial';\n    ele.style.padding = '30px';\n    let pageHeight = ele.scrollWidth / A4_WIDTH * A4_HEIGHT;\n    // 获取分割dom，此处为class类名为item的dom\n    let domList = document.getElementsByClassName(splitClassName);\n    // 进行分割操作，当dom内容已超出a4的高度，则将该dom前插入一个空dom，把他挤下去，分割\n    let pageNum = 1; //pdf页数\n    let eleBounding = ele.getBoundingClientRect();\n    for (let i = 0; i < domList.length; i++) {\n        let node = domList[i];\n        let bound = node.getBoundingClientRect();\n        let offset2Ele = bound.top - eleBounding.top\n        let currentPage = Math.ceil((bound.bottom - eleBounding.top) / pageHeight); //当前元素应该在哪一页\n        if (pageNum < currentPage) {\n            pageNum++\n            let divParent = domList[i].parentNode; // 获取该div的父节点\n            let newNode = document.createElement('tr');\n            newNode.className = 'emptyDiv';\n            newNode.style.background = 'white';\n            newNode.style.height = (pageHeight * (pageNum - 1) - offset2Ele + 30) + 'px'; //+30为了在换下一页时有顶部的边距\n            newNode.style.width = '100%';\n            let next = domList[i].nextSibling; // 获取div的下一个兄弟节点\n            // 判断兄弟节点是否存在\n            if (next) {\n                // 存在则将新节点插入到div的下一个兄弟节点之前，即div之后\n                divParent.insertBefore(newNode, node);\n            } else {\n                // 不存在则直接添加到最后,appendChild默认添加到divParent的最后\n                divParent.appendChild(newNode);\n            }\n        }\n    }\n}\nfunction zipChange(promises,title,funct) {\n    Promise.all(promises).then(async(pdfs) =>{\n      const zip = new JSZip()\n      promises.forEach(async(item, index) =>{\n\n            const {pdf,name} = item\n          if (promises.length === 1) {\n            pdf.save(name)\n          } else {\n            await zip.file(name, pdf.output('blob'))\n          }\n          funct&&funct(item,index)\n      })\n  if (promises.length > 1) {\n    zip.generateAsync({type: 'blob'}).then((content) =>{\n      FileSaver.saveAs(content, title + '.zip')\n    })\n  }\n})\n}\n\nexport default {\n    downloadPDF, outPutPdfFn ,zipChange\n}\n\n"]}]}