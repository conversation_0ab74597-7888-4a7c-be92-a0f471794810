package com.qz.service.impl.gzpgl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qz.annotations.TodoConfig;
import com.qz.controller.gzpgl.GzpCount;
import com.qz.entity.gzpgl.*;
import com.qz.enums.ProcessType;
import com.qz.feignService.SignFeignClient;
import com.qz.mapper.SequenceMapper;
import com.qz.mapper.gzpgl.*;
import com.qz.service.gzpgl.IYxGzpService;
import com.qz.service.gzpgl.IYxGzprwService;
import com.qz.utils.ExportUtils;
import com.qz.utils.LocalDateTimeUtils;
import com.qz.utils.SignUtils;
import com.qz.utils.WordUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 工作票主表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-25
 */
@Service
public class YxGzpServiceImpl extends ServiceImpl<YxGzpMapper, YxGzp> implements IYxGzpService {

    @Value("${attachment.filePath}")
    private String filePath;

    @Resource
    private SignFeignClient signFeignClient;

    @Resource
    private YxGzprwMapper yxGzprwMapper;

    @Resource
    private YxBdzdyzgzpAqcs1Mapper yxBdzdyzgzpAqcs1Mapper;

    @Resource
    private YxBdzdyzgzpAqcs2Mapper yxBdzdyzgzpAqcs2Mapper;

    @Resource
    private YxBdzdyzgzpAqcs3Mapper yxBdzdyzgzpAqcs3Mapper;

    @Resource
    private YxBdzdyzgzpAqcs4Mapper yxBdzdyzgzpAqcs4Mapper;

    @Resource
    private YxBdzdyzgzpAqcs5Mapper yxBdzdyzgzpAqcs5Mapper;

    @Resource
    private YxBdzdyzgzpEcaqcspMapper yxBdzdyzgzpEcaqcspMapper;

    @Resource
    private YxDlxldyzgzpAqcspMapper yxDlxldyzgzpAqcspMapper;

    @Resource
    private SequenceMapper sequenceMapper;

    @Resource
    private IYxGzprwService iYxGzprwService;

    /**
     * 列表查询
     *
     * @param yxGzp
     * @return
     */
    @Override
    public Page<YxGzp> getList(YxGzp yxGzp) {
        return baseMapper.getList(yxGzp, yxGzp.createPage());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @TodoConfig(processType = ProcessType.GZP, businessId = "#id")
    public boolean removeGzp(@RequestBody String id) {
        //删除工作票任务数据
        ArrayList<String> ids = new ArrayList<>();
        ids.add(id);
        iYxGzprwService.getRemove(ids);
        return this.removeById(id);
    }

    /**
     * 按模板导出
     * 主要逻辑：
     * 前端拿到导入的模板类型 -> 走对应模板类型方法 -> 模板基本都有需要实现的几个参数，由processBaseInfo方法处理，剩下的需要自己额外添加
     * 最终根据模板类型以及对应的数据调用对应的WordUtils方法即可。
     *
     * @param response 响应
     * @param objId    工作票实体
     * @param isPdf    是否pdf
     */
    @Override
    public void exportBdzGzp(HttpServletResponse response, String objId, Boolean isPdf) throws IOException {
        //模板路径;
        String os = System.getProperty("os.name");
        String gzpTYpe;
        String projectPath;
        if (os != null && os.toLowerCase().startsWith("windows")) {
            projectPath = System.getProperty("user.dir") + File.separator + "qz-operation" + File.separator + "src" + File.separator + "main" + File.separator + "resources" + File.separator;
        } else {
            projectPath = filePath;
        }
        String templatePath = "";
        //定义集合用于替换模板中的数据
        Map<String, Object> map = new HashMap();
        YxGzp yxGzp = baseMapper.getById(objId);
        //如果接收时间为空，则赋值当前时间
        if (yxGzp.getJssj() == null) {
            yxGzp.setJssj(LocalDateTime.now());
            baseMapper.updateById(yxGzp);
        }
        switch (yxGzp.getLx()) {
            case 1://变电站第一种工作票
            case 12:
//                if (UnitType.getUnitCodeStr().contains(yxGzp.getDw())) {
//                    templatePath = "bdzgzp_1_1";
//                } else {
//                    templatePath = "bdzgzp_1";
//                }
                templatePath = "bdzgzp_1";
                gzpTYpe = "bdz1";
                processBdz1Map(yxGzp, map, gzpTYpe);
                break;
            case 2://变电站第二种工作票
            case 13:
//                if (UnitType.getUnitCodeStr().contains(yxGzp.getDw()) && !UnitType.getUnitCodeStr("pd").contains(yxGzp.getDw())) {
//                    templatePath = "bdzgzp_2_1";
//                } else {
//                    templatePath = "bdzgzp_2";
//                }
                templatePath = "bdzgzp_2";
                gzpTYpe = "bdz2";
                processBdz1Map(yxGzp, map, gzpTYpe);
                break;
            case 3://变电站(发电厂)和线路事故应急抢修单
            case 14:
                templatePath = "bdzyxsgqxd";
                gzpTYpe = "bdzyxsgqxd";
                processbdzQxdMap(yxGzp, map, gzpTYpe);
                break;
            case 4://电力线路第一种工作票
//                if (UnitType.getUnitCodeStr().contains(yxGzp.getDw())) {
//                    templatePath = "dlxlgzp_1_1";
//                } else {
//                    templatePath = "dlxlgzp_1";
//                }
                templatePath = "dlxlgzp_1";
                gzpTYpe = "dlxl1";
                processXl1Map(yxGzp, map, gzpTYpe);
                break;
            case 5://电力线路第二种工作票
//                if (UnitType.getUnitCodeStr().contains(yxGzp.getDw())) {
//                    templatePath = "dlxlgzp_2_1";
//                } else {
//                    templatePath = "dlxlgzp_2";
//                }
                templatePath = "dlxlgzp_2";
                gzpTYpe = "dlxl2";
                processXl2Map(yxGzp, map, gzpTYpe);
                break;
            case 6://电力电缆第一种工作票
//                if (UnitType.getUnitCodeStr().contains(yxGzp.getDw())) {
//                    templatePath = "dldlgzp_1_1";
//                } else {
//                    templatePath = "dldlgzp_1";
//                }
                templatePath = "dldlgzp_1";
                gzpTYpe = "dldl1";
                processDl1Map(yxGzp, map, gzpTYpe);
                break;
            case 7://电力电缆第二种工作票
//                if (UnitType.getUnitCodeStr().contains(yxGzp.getDw())) {
//                    templatePath = "dldlgzp_2_1";
//                } else {
//                    templatePath = "dldlgzp_2";
//                }
                templatePath = "dldlgzp_2";
                gzpTYpe = "dldl2";
                processDl2Map(yxGzp, map, gzpTYpe);
                break;
//            case 3://二次工作安全措施
//                templatePath = "gzaqcs_2";
//                gzpTYpe="aqcs2";
//                processAqcs2Map(yxGzp,map,gzpTYpe);
//                break;
            case 8://配电站工作票
                gzpTYpe = "pdzgzp1";
                templatePath = "pdzgzp1";
                processPdzgzp1Map(yxGzp, map, gzpTYpe);
                break;
            case 9://配电站事故应急抢修单
                templatePath = "pdzgzjjqxd";
                gzpTYpe = "pdzgzp3";
                processPdzqxdMap(yxGzp, map, gzpTYpe);
                break;
            case 10://配电站检维修工作票
                templatePath = "pdzgzp2";
                gzpTYpe = "pdzgzp2";
                processPdzjwxMap(yxGzp, map, gzpTYpe);
                break;
            case 11://配电工作任务单
                templatePath = "pdzdygzp";
                gzpTYpe = "pdzgzp4";
                processPdzMap(yxGzp, map, gzpTYpe);
                break;
            default:
                break;
        }
        try {
            WordUtils.exportFile(projectPath + "docx" + File.separator + "gzp" + File.separator + templatePath, map, response, isPdf);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public String getLastGzpBhByDw(String lx) {
        return baseMapper.getLastGzpBhByDw(lx);
    }

    @Override
    public Page getysbzList(Ysbz ysbz) {
        return baseMapper.getysbzList(ysbz, ysbz.createPage());
    }

    @Override
    public Page getysbzListByGzpId(Ysbz ysbz) {
        return baseMapper.getysbzListByGzpId(ysbz, ysbz.createPage());
    }

    @Override
    public void deleteYsbzByGzpId(String gzpId) {
        baseMapper.deleteYsbzByGzpId(gzpId);
    }

    @Override
    public void saveYsxListByGzpId(List<Ysbz> ysbzs) {
        baseMapper.saveYsxListByGzpId(ysbzs);
    }

    /**
     * 处理模板数据（变电站第一种工作票）
     *
     * @param yxGzp 工作票实体
     * @param map   用于替换模板数据的map集合
     */
    public void processBdz1Map(YxGzp yxGzp, Map<String, Object> map, String gzpType) throws IOException {
        int[] defaultTableLen = {5, 5, 8, 7, 8};//表格默认初始行数，数组下标顺序按模板从上到下
        int exceedLen = 0;//超过的行数
        //安全措施子表1(第一部分——左)
        List<Map<String, String>> aqcsList1_1_1 = new ArrayList<>();
        //安全措施子表1(第一部分——右)
        List<Map<String, String>> aqcsList1_1_2 = new ArrayList<>();
        //安全措施子表1(第二部分——左)
        List<Map<String, String>> aqcsList1_2_1 = new ArrayList<>();
        //安全措施子表1(第二部分——右)
        List<Map<String, String>> aqcsList1_2_2 = new ArrayList<>();
        //安全措施子表2
        List<Map<String, String>> aqcsList2 = new ArrayList<>();
        //安全措施子表3
        List<Map<String, String>> aqcsList3 = new ArrayList<>();
        //安全措施子表4
        List<Map<String, String>> aqcsList4 = new ArrayList<>();
        //安全措施子表5
        List<Map<String, String>> aqcsList5 = new ArrayList<>();
        //开收工子表
        List<Map<String, String>> ksgList = new ArrayList<>();

        //基础信息处理
        processBaseInfo(yxGzp, map, gzpType);

        //工作任务子表(最小三行)
        List<YxGzprw> list = yxGzprwMapper.getListByGzpid(yxGzp.getObjId());
        exceedLen = Math.max(list.size() - 3, 0);

        //6.安全措施
        String objId = yxGzp.getObjId();
        //1>.安全措施1
        List<YxBdzdyzgzpAqcs1> yxBdzdyzgzpAqcs1ByGzpid = yxBdzdyzgzpAqcs1Mapper.getYxBdzdyzgzpAqcs1ByGzpid(objId);
        int i1 = yxBdzdyzgzpAqcs1ByGzpid.size();
        int len1 = Math.max((defaultTableLen[0] - exceedLen), 0) * 2 + defaultTableLen[1] * 2;//第一部分和第二部分总长度

        //如果子表数据大于默认总长度，则占满后往下顺延
        if (len1 <= i1) {
            int gtNum = i1 - len1;//多出的数据
            int len2 = gtNum % 2 == 0 ? gtNum / 2 : gtNum / 2 + 1;//第二部分需要超出的行数
            //处理数据
            for (int i = 0; i < i1; i++) {
                YxBdzdyzgzpAqcs1 detail;
                detail = yxBdzdyzgzpAqcs1ByGzpid.get(i);
                Map<String, String> cs1 = new HashMap<>();
                if (i < (defaultTableLen[0] - exceedLen)) {
                    cs1.put("yldlq", detail.getYldlq());
                    cs1.put("yzx", detail.getYzx() == null ? "" : detail.getYzx() ? "是" : "否");
                    aqcsList1_1_1.add(cs1);
                } else if (i < (defaultTableLen[0] - exceedLen) * 2) {
                    cs1.put("yldlq_2", detail.getYldlq());
                    cs1.put("yzxt", detail.getYzx() == null ? "" : detail.getYzx() ? "是" : "否");
                    aqcsList1_1_2.add(cs1);
                } else if (i < (Math.max((defaultTableLen[0] - exceedLen), 0) * 2 + defaultTableLen[1] + len2)) {//把占满的第一部分算进去，再把超过的行数和默认的行数算进去
                    cs1.put("yldlq", detail.getYldlq());
                    cs1.put("yzx", detail.getYzx() == null ? "" : detail.getYzx() ? "是" : "否");
                    aqcsList1_2_1.add(cs1);
                } else {
                    cs1.put("yldlq_2", detail.getYldlq());
                    cs1.put("yzxt", detail.getYzx() == null ? "" : detail.getYzx() ? "是" : "否");
                    aqcsList1_2_2.add(cs1);
                }
            }
            //超出的行数
            exceedLen = len2;
        } else {//空行自动补全
            for (int i = 0; i < len1; i++) {//把多出的空行减去
                YxBdzdyzgzpAqcs1 detail;
                //如果超过总长度，则创建空对象给后续获取
                if (i < i1) {
                    detail = yxBdzdyzgzpAqcs1ByGzpid.get(i);
                } else {
                    detail = new YxBdzdyzgzpAqcs1();
                }
                Map<String, String> cs1 = new HashMap<>();
                if (i < (defaultTableLen[0] - exceedLen)) {
                    cs1.put("yldlq", detail.getYldlq());
                    cs1.put("yzx", detail.getYzx() == null ? "" : detail.getYzx() ? "是" : "否");
                    aqcsList1_1_1.add(cs1);
                } else if (i < (defaultTableLen[0] - exceedLen) * 2) {
                    cs1.put("yldlq_2", detail.getYldlq());
                    cs1.put("yzxt", detail.getYzx() == null ? "" : detail.getYzx() ? "是" : "否");
                    aqcsList1_1_2.add(cs1);
                } else if (i < ((defaultTableLen[0] - exceedLen) * 2 + defaultTableLen[1])) {
                    cs1.put("yldlq", detail.getYldlq());
                    cs1.put("yzx", detail.getYzx() == null ? "" : detail.getYzx() ? "是" : "否");
                    aqcsList1_2_1.add(cs1);
                } else {
                    cs1.put("yldlq_2", detail.getYldlq());
                    cs1.put("yzxt", detail.getYzx() == null ? "" : detail.getYzx() ? "是" : "否");
                    aqcsList1_2_2.add(cs1);
                }
            }
            exceedLen = 0;//重置超出的行数
        }

        List<YxBdzdyzgzpAqcs2> yxBdzdyzgzpAqcs2ByGzpid = yxBdzdyzgzpAqcs2Mapper.getYxBdzdyzgzpAqcs2ByGzpid(objId);
        int i2 = yxBdzdyzgzpAqcs2ByGzpid.size();
        Map<String, Object> processMap2 = new HashMap<>();
        processMap2.put("list", aqcsList2);
        processMap2.put("dataSize", i2);
        processMap2.put("exceedLen", exceedLen);
        processMap2.put("defaultTableLen", defaultTableLen[2]);
        processMap2.put("fieldStr", "yzjdx,yzx");
        if (CollectionUtils.isNotEmpty(yxBdzdyzgzpAqcs2ByGzpid)) {
            for (YxBdzdyzgzpAqcs2 detail : yxBdzdyzgzpAqcs2ByGzpid) {
                Map<String, String> cs2 = new HashMap<String, String>();
                cs2.put("yzjdx", detail.getYzjdx());
                cs2.put("yzx", detail.getYzx() == null ? "" : detail.getYzx() ? "是" : "否");
                aqcsList2.add(cs2);
            }
            exceedLen = processTableLines(processMap2, true, "1");
        } else {//添加空行
            exceedLen = processTableLines(processMap2, false, "1");
        }
        //3>.安全措施3
        List<YxBdzdyzgzpAqcs3> yxBdzdyzgzpAqcs3ByGzpid = yxBdzdyzgzpAqcs3Mapper.getYxBdzdyzgzpAqcs3ByGzpid(objId);
        int i3 = yxBdzdyzgzpAqcs3ByGzpid.size();
        Map<String, Object> processMap3 = new HashMap<>();
        processMap3.put("list", aqcsList3);
        processMap3.put("dataSize", i3);
        processMap3.put("exceedLen", exceedLen);
        processMap3.put("defaultTableLen", defaultTableLen[3]);
        processMap3.put("fieldStr", "yszl,yzx");
        if (CollectionUtils.isNotEmpty(yxBdzdyzgzpAqcs3ByGzpid)) {
            for (YxBdzdyzgzpAqcs3 detail : yxBdzdyzgzpAqcs3ByGzpid) {
                Map<String, String> cs3 = new HashMap<String, String>();
                cs3.put("yszl", detail.getYszl());
                cs3.put("yzx", detail.getYzx() == null ? "" : detail.getYzx() ? "是" : "否");
                aqcsList3.add(cs3);
            }
            exceedLen = processTableLines(processMap3, true, "1");
        } else {//添加空行
            exceedLen = processTableLines(processMap3, false, "1");
        }
        //第四和第五部分共用一个exceedLen,不需要更新exceedLen的值
        //4>.安全措施4
        List<YxBdzdyzgzpAqcs4> yxBdzdyzgzpAqcs4ByGzpid = yxBdzdyzgzpAqcs4Mapper.getYxBdzdyzgzpAqcs4ByGzpid(objId);
        int i4 = yxBdzdyzgzpAqcs4ByGzpid.size();
        Map<String, Object> processMap4 = new HashMap<>();
        processMap4.put("list", aqcsList4);
        processMap4.put("dataSize", i4);
        processMap4.put("exceedLen", exceedLen);
        processMap4.put("defaultTableLen", defaultTableLen[4]);
        processMap4.put("fieldStr", "gzddbl");
        if (CollectionUtils.isNotEmpty(yxBdzdyzgzpAqcs4ByGzpid)) {
            for (YxBdzdyzgzpAqcs4 detail : yxBdzdyzgzpAqcs4ByGzpid) {
                Map<String, String> cs4 = new HashMap<String, String>();
                cs4.put("gzddbl", detail.getGzddbl());
                aqcsList4.add(cs4);
            }
            exceedLen = processTableLines(processMap4, true, "2");
        } else {//添加空行
            exceedLen = processTableLines(processMap4, false, "2");
        }
        //5>.安全措施5
        List<YxBdzdyzgzpAqcs5> yxBdzdyzgzpAqcs5ByGzpid = yxBdzdyzgzpAqcs5Mapper.getYxBdzdyzgzpAqcs5ByGzpid(objId);
        int i5 = yxBdzdyzgzpAqcs5ByGzpid.size();
        Map<String, Object> processMap5 = new HashMap<>();
        processMap5.put("list", aqcsList5);
        processMap5.put("dataSize", i5);
        processMap5.put("exceedLen", exceedLen);
        processMap5.put("defaultTableLen", defaultTableLen[4]);
        processMap5.put("fieldStr", "bcgzddbl");
        if (CollectionUtils.isNotEmpty(yxBdzdyzgzpAqcs5ByGzpid)) {
            for (YxBdzdyzgzpAqcs5 detail : yxGzp.getYxBdzdyzgzpAqcs5List()) {
                Map<String, String> cs5 = new HashMap<String, String>();
                cs5.put("bcgzddbl", detail.getBcgzddbl());
                aqcsList5.add(cs5);
            }
            exceedLen = processTableLines(processMap5, true, "2");
        } else {
            exceedLen = processTableLines(processMap5, false, "2");
        }

        //7.收到工作票时间
        map.put("jssjy", getDateStr(yxGzp.getJssj(), "y"));//年
        map.put("jssjM", getDateStr(yxGzp.getJssj(), "M"));//月
        map.put("jssjd", getDateStr(yxGzp.getJssj(), "d"));//日
        map.put("jssjh", getDateStr(yxGzp.getJssj(), "h"));//时
        map.put("jssjm", getDateStr(yxGzp.getJssj(), "m"));//分
        map.put("zbfzr", SignUtils.getSignImg(signFeignClient.getFile(yxGzp.getZbfzr()).body().asInputStream(), yxGzp.getZbfzr()));
        map.put("gzfzr2", SignUtils.getSignImg(signFeignClient.getFile(yxGzp.getGzfzr()).body().asInputStream(), yxGzp.getGzfzr()));
        //8.确认本工作票1~7项
        map.put("xkr", SignUtils.getSignImg(signFeignClient.getFile(yxGzp.getXkr()).body().asInputStream(), yxGzp.getXkr()));
        map.put("xkgzkssjy", getDateStr(yxGzp.getXkgzkssj(), "y"));//年
        map.put("xkgzkssjM", getDateStr(yxGzp.getXkgzkssj(), "M"));//月
        map.put("xkgzkssjd", getDateStr(yxGzp.getXkgzkssj(), "d"));//日
        map.put("xkgzkssjh", getDateStr(yxGzp.getXkgzkssj(), "h"));//时
        map.put("xkgzkssjm", getDateStr(yxGzp.getXkgzkssj(), "m"));//分

        //9.确认工作负责人布置的任务和本施工项目安全措施
        map.put("qrgzbry", yxGzprwMapper.getUserName(yxGzp.getQrgzbry()));

        //10.工作负责人变动情况
/*        map.put("gzfzr4", SignUtils.getSignImg(signPath + signFeignClient.getFileName(yxGzp.getGzfzrxm()), WordUtils.processStr(yxGzp.getGzfzrxm(), 4)));
        map.put("xkr", SignUtils.getSignImg(signFeignClient.getFile(yxGzp.getXkr()).body().asInputStream(), yxGzp.getXkr()));
        map.put("gzfzr5", SignUtils.getSignImg(signPath + signFeignClient.getFileName(yxGzp.getGzfzrxm()), WordUtils.processStr(yxGzp.getGzfzrxm(), 4)));
        map.put("xkr", SignUtils.getSignImg(signFeignClient.getFile(yxGzp.getXkr()).body().asInputStream(), yxGzp.getXkr()));*/
        map.put("bdsjy", getDateStr(yxGzp.getBdsj(), "y"));//年
        map.put("bdsjM", getDateStr(yxGzp.getBdsj(), "M"));//月
        map.put("bdsjd", getDateStr(yxGzp.getBdsj(), "d"));//日
        map.put("bdsjh", getDateStr(yxGzp.getBdsj(), "h"));//时
        map.put("bdsjm", getDateStr(yxGzp.getBdsj(), "m"));//分
        map.put("bdqk", yxGzp.getBdqk());

        //11.工作票延期
        map.put("yxqycy", getDateStr(yxGzp.getYxqyc(), "y"));//年
        map.put("yxqycM", getDateStr(yxGzp.getYxqyc(), "M"));//月
        map.put("yxqycd", getDateStr(yxGzp.getYxqyc(), "d"));//日
        map.put("yxqych", getDateStr(yxGzp.getYxqyc(), "h"));//时
        map.put("yxqycm", getDateStr(yxGzp.getYxqyc(), "m"));//分
        map.put("yqfzsjy", getDateStr(yxGzp.getYqfzsj(), "y"));//年
        map.put("yqfzsjM", getDateStr(yxGzp.getYqfzsj(), "M"));//月
        map.put("yqfzsjd", getDateStr(yxGzp.getYqfzsj(), "d"));//日
        map.put("yqfzsjh", getDateStr(yxGzp.getYqfzsj(), "h"));//时
        map.put("yqfzsjm", getDateStr(yxGzp.getYqfzsj(), "m"));//分
//        map.put("yqxkr", SignUtils.getSignImg(signPath + signFeignClient.getFileName(yxGzp.getYqxkr()), WordUtils.processStr(yxGzp.getYqxkr(), 4)));
        map.put("yqxksjy", getDateStr(yxGzp.getYqxksj(), "y"));//年
        map.put("yqxksjM", getDateStr(yxGzp.getYqxksj(), "M"));//月
        map.put("yqxksjd", getDateStr(yxGzp.getYqxksj(), "d"));//日
        map.put("yqxksjh", getDateStr(yxGzp.getYqxksj(), "h"));//时
        map.put("yqxksjm", getDateStr(yxGzp.getYqxksj(), "m"));//分

        //13.工作终结
        map.put("gzzjsjy", getDateStr(yxGzp.getGzzjsj(), "y"));//年
        map.put("gzzjsjM", getDateStr(yxGzp.getGzzjsj(), "M"));//月
        map.put("gzzjsjd", getDateStr(yxGzp.getGzzjsj(), "d"));//日
        map.put("gzzjsjh", getDateStr(yxGzp.getGzzjsj(), "h"));//时
        map.put("gzzjsjm", getDateStr(yxGzp.getGzzjsj(), "m"));//分
//        map.put("gzfzr6", SignUtils.getSignImg(signPath + signFeignClient.getFileName(yxGzp.getGzfzrxm()), WordUtils.processStr(yxGzp.getGzfzrxm(), 3)));
        map.put("zjxkr", SignUtils.getSignImg(signFeignClient.getFile(yxGzp.getXkr()).body().asInputStream(), yxGzp.getXkr()));
        //14.工作票终结
        map.put("wccdx", yxGzp.getWccdx());
        map.put("dxs", yxGzp.getDxs());
        map.put("dzs", yxGzp.getDzs());
        map.put("pzjxkr", SignUtils.getSignImg(signFeignClient.getFile(yxGzp.getXkr()).body().asInputStream(), yxGzp.getXkr()));
        map.put("pzjxksjy", getDateStr(yxGzp.getPzjxksj(), "y"));//年
        map.put("pzjxksjM", getDateStr(yxGzp.getPzjxksj(), "M"));//月
        map.put("pzjxksjd", getDateStr(yxGzp.getPzjxksj(), "d"));//日
        map.put("pzjxksjh", getDateStr(yxGzp.getPzjxksj(), "h"));//时
        map.put("pzjxksjm", getDateStr(yxGzp.getPzjxksj(), "m"));//分

        //15.备注
        map.put("zzjjh", yxGzp.getZzjjh());
        map.put("jhnr", yxGzp.getJhnr());
        map.put("qtsx", yxGzp.getQtsx());
        map.put("zzbcbm", yxGzp.getZzbcbm());

        //将处理好的子表集合添加到最后的map中
        map.put("gzrwList", processGzrwList(yxGzp, 3));
        map.put("aqcsList1_1_1", processMapData(aqcsList1_1_1, aqcsList1_1_2, "yldlq_2,yldlq"));
        map.put("aqcsList1_2_1", processMapData(aqcsList1_2_1, aqcsList1_2_2, "yldlq_2,yldlq"));
        map.put("aqcsList2", aqcsList2);
        map.put("aqcsList3", aqcsList3);
        map.put("aqcsList4", processMapData(aqcsList4, aqcsList5, "bcgzddbl,gzddbl"));
        map.put("ksgList", ksgList);

        //有的字段没有暂时先这么补充了，实在不好看
        map.put("gztj", yxGzp.getGztj());
        map.put("zysx", yxGzp.getZysx());
    }

    public void processPdzgzp1Map(YxGzp yxGzp, Map<String, Object> map, String gzpType) throws IOException {
        int[] defaultTableLen = {5, 5, 8, 7, 8};//表格默认初始行数，数组下标顺序按模板从上到下
        int exceedLen = 0;//超过的行数
        //安全措施子表1(第一部分——左)
        List<Map<String, String>> aqcsList1_1_1 = new ArrayList<>();
        //安全措施子表1(第一部分——右)
        List<Map<String, String>> aqcsList1_1_2 = new ArrayList<>();
        //安全措施子表1(第二部分——左)
        List<Map<String, String>> aqcsList1_2_1 = new ArrayList<>();
        //安全措施子表1(第二部分——右)
        List<Map<String, String>> aqcsList1_2_2 = new ArrayList<>();
        //安全措施子表2
        List<Map<String, String>> aqcsList2 = new ArrayList<>();
        //安全措施子表3
        List<Map<String, String>> aqcsList3 = new ArrayList<>();
        //开收工子表
        List<Map<String, String>> ksgList = new ArrayList<>();

        //基础信息处理
        processBaseInfo(yxGzp, map, gzpType);

        //工作任务子表(最小三行)
        List<YxGzprw> list = yxGzprwMapper.getListByGzpid(yxGzp.getObjId());
        exceedLen = Math.max(list.size() - 3, 0);

        //6.安全措施
        String objId = yxGzp.getObjId();
        //1>.安全措施1
        List<YxBdzdyzgzpAqcs1> yxBdzdyzgzpAqcs1ByGzpid = yxBdzdyzgzpAqcs1Mapper.getYxBdzdyzgzpAqcs1ByGzpid(objId);
        int i1 = yxBdzdyzgzpAqcs1ByGzpid.size();
        int len1 = Math.max((defaultTableLen[0] - exceedLen), 0) * 2 + defaultTableLen[1] * 2;//第一部分和第二部分总长度

        //如果子表数据大于默认总长度，则占满后往下顺延
        if (CollectionUtils.isNotEmpty(yxBdzdyzgzpAqcs1ByGzpid)) {
            if (len1 <= i1) {
                int gtNum = i1 - len1;//多出的数据
                int len2 = gtNum % 2 == 0 ? gtNum / 2 : gtNum / 2 + 1;//第二部分需要超出的行数
                //处理数据
                for (int i = 0; i < i1; i++) {
                    YxBdzdyzgzpAqcs1 detail;
                    detail = yxBdzdyzgzpAqcs1ByGzpid.get(i);
                    Map<String, String> cs1 = new HashMap<>();
                    if (i < (defaultTableLen[0] - exceedLen)) {
                        cs1.put("yldlq", detail.getYldlq());
                        cs1.put("yzx", detail.getYzx() == null ? "" : detail.getYzx() ? "是" : "否");
                        aqcsList1_1_1.add(cs1);
                    } else if (i < (defaultTableLen[0] - exceedLen) * 2) {
                        cs1.put("yldlq_2", detail.getYldlq());
                        cs1.put("yzxt", detail.getYzx() == null ? "" : detail.getYzx() ? "是" : "否");
                        aqcsList1_1_2.add(cs1);
                    } else if (i < (Math.max((defaultTableLen[0] - exceedLen), 0) * 2 + defaultTableLen[1] + len2)) {//把占满的第一部分算进去，再把超过的行数和默认的行数算进去
                        cs1.put("yldlq", detail.getYldlq());
                        cs1.put("yzx", detail.getYzx() == null ? "" : detail.getYzx() ? "是" : "否");
                        aqcsList1_2_1.add(cs1);
                    } else {
                        cs1.put("yldlq_2", detail.getYldlq());
                        cs1.put("yzxt", detail.getYzx() == null ? "" : detail.getYzx() ? "是" : "否");
                        aqcsList1_2_2.add(cs1);
                    }
                }
                //超出的行数
                exceedLen = len2;
            } else {//空行自动补全
                for (int i = 0; i < len1; i++) {//把多出的空行减去
                    YxBdzdyzgzpAqcs1 detail;
                    //如果超过总长度，则创建空对象给后续获取
                    if (i < i1) {
                        detail = yxBdzdyzgzpAqcs1ByGzpid.get(i);
                    } else {
                        detail = new YxBdzdyzgzpAqcs1();
                    }
                    Map<String, String> cs1 = new HashMap<>();
                    if (i < (defaultTableLen[0] - exceedLen)) {
                        cs1.put("yldlq", detail.getYldlq());
                        cs1.put("yzx", detail.getYzx() == null ? "" : detail.getYzx() ? "是" : "否");
                        aqcsList1_1_1.add(cs1);
                    } else if (i < (defaultTableLen[0] - exceedLen) * 2) {
                        cs1.put("yldlq_2", detail.getYldlq());
                        cs1.put("yzxt", detail.getYzx() == null ? "" : detail.getYzx() ? "是" : "否");
                        aqcsList1_1_2.add(cs1);
                    } else if (i < ((defaultTableLen[0] - exceedLen) * 2 + defaultTableLen[1])) {
                        cs1.put("yldlq", detail.getYldlq());
                        cs1.put("yzx", detail.getYzx() == null ? "" : detail.getYzx() ? "是" : "否");
                        aqcsList1_2_1.add(cs1);
                    } else {
                        cs1.put("yldlq_2", detail.getYldlq());
                        cs1.put("yzxt", detail.getYzx() == null ? "" : detail.getYzx() ? "是" : "否");
                        aqcsList1_2_2.add(cs1);
                    }
                }
                exceedLen = 0;//重置超出的行数
            }
        }

        List<YxBdzdyzgzpAqcs2> yxBdzdyzgzpAqcs2ByGzpid = yxBdzdyzgzpAqcs2Mapper.getYxBdzdyzgzpAqcs2ByGzpid(objId);
        int i2 = yxBdzdyzgzpAqcs2ByGzpid.size();
        Map<String, Object> processMap2 = new HashMap<>();
        processMap2.put("list", aqcsList2);
        processMap2.put("dataSize", i2);
        processMap2.put("exceedLen", exceedLen);
        processMap2.put("defaultTableLen", defaultTableLen[2]);
        processMap2.put("fieldStr", "yzjdx,yzx");
        if (CollectionUtils.isNotEmpty(yxBdzdyzgzpAqcs2ByGzpid)) {
            for (YxBdzdyzgzpAqcs2 detail : yxBdzdyzgzpAqcs2ByGzpid) {
                Map<String, String> cs2 = new HashMap<String, String>();
                cs2.put("yzjdx", detail.getYzjdx());
                cs2.put("yzx", detail.getYzx() == null ? "" : detail.getYzx() ? "是" : "否");
                aqcsList2.add(cs2);
            }
            exceedLen = processTableLines(processMap2, true, "1");
        }
//        else {//添加空行
//            exceedLen = processTableLines(processMap2, false, "1");
//        }
        //3>.安全措施3
        List<YxBdzdyzgzpAqcs3> yxBdzdyzgzpAqcs3ByGzpid = yxBdzdyzgzpAqcs3Mapper.getYxBdzdyzgzpAqcs3ByGzpid(objId);
        int i3 = yxBdzdyzgzpAqcs3ByGzpid.size();
        Map<String, Object> processMap3 = new HashMap<>();
        processMap3.put("list", aqcsList3);
        processMap3.put("dataSize", i3);
        processMap3.put("exceedLen", exceedLen);
        processMap3.put("defaultTableLen", defaultTableLen[3]);
        processMap3.put("fieldStr", "yszl,yzx");
        if (CollectionUtils.isNotEmpty(yxBdzdyzgzpAqcs3ByGzpid)) {
            for (YxBdzdyzgzpAqcs3 detail : yxBdzdyzgzpAqcs3ByGzpid) {
                Map<String, String> cs3 = new HashMap<String, String>();
                cs3.put("yszl", detail.getYszl());
                cs3.put("yzx", detail.getYzx() == null ? "" : detail.getYzx() ? "是" : "否");
                aqcsList3.add(cs3);
            }
        }
//        else {//添加空行
//            exceedLen = processTableLines(processMap3, false, "1");
//        }

        //7.收到工作票时间
        map.put("jssjy", getDateStr(yxGzp.getJssj(), "y"));//年
        map.put("jssjM", getDateStr(yxGzp.getJssj(), "M"));//月
        map.put("jssjd", getDateStr(yxGzp.getJssj(), "d"));//日
        map.put("jssjh", getDateStr(yxGzp.getJssj(), "h"));//时
        map.put("jssjm", getDateStr(yxGzp.getJssj(), "m"));//分
        map.put("zbfzr", SignUtils.getSignImg(signFeignClient.getFile(yxGzp.getZbfzr()).body().asInputStream(), yxGzp.getZbfzr()));
        map.put("gzfzr2", SignUtils.getSignImg(signFeignClient.getFile(yxGzp.getGzfzr()).body().asInputStream(), yxGzp.getGzfzr()));
        //8.确认本工作票1~7项
        map.put("xkr", SignUtils.getSignImg(signFeignClient.getFile(yxGzp.getXkr()).body().asInputStream(), yxGzp.getXkr()));
        map.put("xkgzkssjy", getDateStr(yxGzp.getXkgzkssj(), "y"));//年
        map.put("xkgzkssjM", getDateStr(yxGzp.getXkgzkssj(), "M"));//月
        map.put("xkgzkssjd", getDateStr(yxGzp.getXkgzkssj(), "d"));//日
        map.put("xkgzkssjh", getDateStr(yxGzp.getXkgzkssj(), "h"));//时
        map.put("xkgzkssjm", getDateStr(yxGzp.getXkgzkssj(), "m"));//分

        //9.确认工作负责人布置的任务和本施工项目安全措施
        map.put("qrgzbry", yxGzprwMapper.getUserName(yxGzp.getQrgzbry()));

        //10.工作负责人变动情况
/*        map.put("gzfzr4", SignUtils.getSignImg(signPath + signFeignClient.getFileName(yxGzp.getGzfzrxm()), WordUtils.processStr(yxGzp.getGzfzrxm(), 4)));
        map.put("xkr", SignUtils.getSignImg(signFeignClient.getFile(yxGzp.getXkr()).body().asInputStream(), yxGzp.getXkr()));
        map.put("gzfzr5", SignUtils.getSignImg(signPath + signFeignClient.getFileName(yxGzp.getGzfzrxm()), WordUtils.processStr(yxGzp.getGzfzrxm(), 4)));
        map.put("xkr", SignUtils.getSignImg(signFeignClient.getFile(yxGzp.getXkr()).body().asInputStream(), yxGzp.getXkr()));*/
        map.put("bdsjy", getDateStr(yxGzp.getBdsj(), "y"));//年
        map.put("bdsjM", getDateStr(yxGzp.getBdsj(), "M"));//月
        map.put("bdsjd", getDateStr(yxGzp.getBdsj(), "d"));//日
        map.put("bdsjh", getDateStr(yxGzp.getBdsj(), "h"));//时
        map.put("bdsjm", getDateStr(yxGzp.getBdsj(), "m"));//分
        map.put("bdqk", yxGzp.getBdqk());

        //11.工作票延期
        map.put("yxqycy", getDateStr(yxGzp.getYxqyc(), "y"));//年
        map.put("yxqycM", getDateStr(yxGzp.getYxqyc(), "M"));//月
        map.put("yxqycd", getDateStr(yxGzp.getYxqyc(), "d"));//日
        map.put("yxqych", getDateStr(yxGzp.getYxqyc(), "h"));//时
        map.put("yxqycm", getDateStr(yxGzp.getYxqyc(), "m"));//分
        map.put("yqfzsjy", getDateStr(yxGzp.getYqfzsj(), "y"));//年
        map.put("yqfzsjM", getDateStr(yxGzp.getYqfzsj(), "M"));//月
        map.put("yqfzsjd", getDateStr(yxGzp.getYqfzsj(), "d"));//日
        map.put("yqfzsjh", getDateStr(yxGzp.getYqfzsj(), "h"));//时
        map.put("yqfzsjm", getDateStr(yxGzp.getYqfzsj(), "m"));//分
//        map.put("yqxkr", SignUtils.getSignImg(signPath + signFeignClient.getFileName(yxGzp.getYqxkr()), WordUtils.processStr(yxGzp.getYqxkr(), 4)));
        map.put("yqxksjy", getDateStr(yxGzp.getYqxksj(), "y"));//年
        map.put("yqxksjM", getDateStr(yxGzp.getYqxksj(), "M"));//月
        map.put("yqxksjd", getDateStr(yxGzp.getYqxksj(), "d"));//日
        map.put("yqxksjh", getDateStr(yxGzp.getYqxksj(), "h"));//时
        map.put("yqxksjm", getDateStr(yxGzp.getYqxksj(), "m"));//分

        //13.工作终结
        map.put("gzzjsjy", getDateStr(yxGzp.getGzzjsj(), "y"));//年
        map.put("gzzjsjM", getDateStr(yxGzp.getGzzjsj(), "M"));//月
        map.put("gzzjsjd", getDateStr(yxGzp.getGzzjsj(), "d"));//日
        map.put("gzzjsjh", getDateStr(yxGzp.getGzzjsj(), "h"));//时
        map.put("gzzjsjm", getDateStr(yxGzp.getGzzjsj(), "m"));//分
//        map.put("gzfzr6", SignUtils.getSignImg(signPath + signFeignClient.getFileName(yxGzp.getGzfzrxm()), WordUtils.processStr(yxGzp.getGzfzrxm(), 3)));
        map.put("zjxkr", SignUtils.getSignImg(signFeignClient.getFile(yxGzp.getXkr()).body().asInputStream(), yxGzp.getXkr()));
        //14.工作票终结
        map.put("wccdx", yxGzp.getWccdx());
        map.put("dxs", yxGzp.getDxs());
        map.put("dzs", yxGzp.getDzs());
        map.put("pzjxkr", SignUtils.getSignImg(signFeignClient.getFile(yxGzp.getXkr()).body().asInputStream(), yxGzp.getXkr()));
        map.put("pzjxksjy", getDateStr(yxGzp.getPzjxksj(), "y"));//年
        map.put("pzjxksjM", getDateStr(yxGzp.getPzjxksj(), "M"));//月
        map.put("pzjxksjd", getDateStr(yxGzp.getPzjxksj(), "d"));//日
        map.put("pzjxksjh", getDateStr(yxGzp.getPzjxksj(), "h"));//时
        map.put("pzjxksjm", getDateStr(yxGzp.getPzjxksj(), "m"));//分

        //15.备注
        map.put("zzjjh", yxGzp.getZzjjh());
        map.put("jhnr", yxGzp.getJhnr());
        map.put("qtsx", yxGzp.getQtsx());
        map.put("zzbcbm", yxGzp.getZzbcbm());

        //将处理好的子表集合添加到最后的map中
        map.put("gzrwList", processGzrwList(yxGzp, 3));
        map.put("aqcsList1_1_1", CollectionUtils.isNotEmpty(aqcsList1_1_1) ? processMapData(aqcsList1_1_1, aqcsList1_1_2, "yldlq_2,yldlq") : aqcsList1_1_1);
//        map.put("aqcsList1_2_1", processMapData(aqcsList1_2_1, aqcsList1_2_2, "yldlq_2,yldlq"));
        map.put("aqcsList2", aqcsList2);
        map.put("aqcsList3", aqcsList3);
        map.put("ksgList", ksgList);

        //有的字段没有暂时先这么补充了，实在不好看
        map.put("gztj", yxGzp.getGztj());
        map.put("zysx", yxGzp.getZysx());
        //配电
        //保留带电部位
        map.put("blddbw", yxGzp.getBlhljsb());
        //补充安全措施
        map.put("bcaqcs", yxGzp.getBcaqcs());
    }

    /**
     * 处理模板数据（变电站第二种工作票）
     *
     * @param yxGzp 工作票实体
     * @param map   用于替换模板数据的map集合
     */
    public void processBdz2Map(YxGzp yxGzp, Map<String, Object> map, String gzpType) throws IOException {
        //基础信息处理
        processBaseInfo(yxGzp, map, gzpType);

        //6.工作条件
        map.put("gztj", yxGzp.getGztj());

        //8.补充安全措施
        map.put("bcaqcs", yxGzp.getBcaqcs());

        //将处理好的子表集合添加到最后的map中
        map.put("gzrwList", processGzrwList(yxGzp, 5));
    }

    /**
     * 处理模板数据（变电站(发电厂)和线路事故应急抢修单）
     *
     * @param yxGzp 工作票实体
     * @param map   用于替换模板数据的map集合
     */
    private void processbdzQxdMap(YxGzp yxGzp, Map<String, Object> map, String gzpType) throws IOException {
        //基础信息处理
        processBaseInfo(yxGzp, map, gzpType);
        //抢修班人员
        map.put("gzbry", yxGzp.getGzbry());
        //抢修任务
        map.put("qxrw", yxGzp.getQxrw());
        //安全措施
        map.put("aqcsnr", yxGzp.getAqcsnr());
        //注意事项
        map.put("zysx", yxGzp.getZysx());
    }

    /**
     * 处理模板数据（电力线路第一种工作票）
     *
     * @param yxGzp 工作票实体
     * @param map   用于替换模板数据的map集合
     */
    public void processXl1Map(YxGzp yxGzp, Map<String, Object> map, String gzpType) throws IOException {
        //安全措施子表

        //基础信息处理
        processBaseInfo(yxGzp, map, gzpType);

        //6.安全措施
        map.put("ylkdsb", yxGzp.getYlkdsb());
        map.put("blhljsb", yxGzp.getBlhljsb());
        map.put("zysx", yxGzp.getZysx());
        //获取数据电力线路数据
        LambdaQueryWrapper<YxDlxldyzgzpAqcsp> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(YxDlxldyzgzpAqcsp::getGzpid, yxGzp.getObjId());
        List<YxDlxldyzgzpAqcsp> yxDlxldyzgzpAqcsps = yxDlxldyzgzpAqcspMapper.selectList(wrapper);
        //6.接地线子表
        // TODO:easypoi没有横向插入word的办法，现在只能硬性插入了！
        for (int i = 0; i < 21; i++) {
            YxDlxldyzgzpAqcsp detail = new YxDlxldyzgzpAqcsp();
            if (i < yxDlxldyzgzpAqcsps.size()) {
                detail = yxDlxldyzgzpAqcsps.get(i);
            }
            map.put("xlmc" + i, detail.getXlmc());
            map.put("jdxbh" + i, detail.getJdxbh());
        }

        //工作负责人9
        map.put("gzfzr9", SignUtils.getSignImg(signFeignClient.getFile(yxGzp.getGzfzr()).body().asInputStream(), yxGzp.getGzfzrxm()));
        //负责人接收时间
        map.put("fzrsjy", getDateStr(yxGzp.getJssj(), "y"));//年
        map.put("fzrsjM", getDateStr(yxGzp.getJssj(), "M"));//月
        map.put("fzrsjd", getDateStr(yxGzp.getJssj(), "d"));//日
        map.put("fzrsjh", getDateStr(yxGzp.getJssj(), "h"));//时
        map.put("fzrsjm", getDateStr(yxGzp.getJssj(), "m"));//分

        //将处理好的子表集合添加到最后的map中
        map.put("gzrwList", processGzrwList(yxGzp, 6));
    }

    /**
     * 处理模板数据（电力线路第二种工作票）
     *
     * @param yxGzp 工作票实体
     * @param map   用于替换模板数据的map集合
     */
    public void processXl2Map(YxGzp yxGzp, Map<String, Object> map, String gzpType) throws IOException {

        //基础信息处理
        processBaseInfo(yxGzp, map, gzpType);

        //确认任务项目安全措施
        map.put("aqcs", yxGzp.getRwxmaqcs());

        //工作负责人
        map.put("gzfzr2", SignUtils.getSignImg(signFeignClient.getFile(yxGzp.getGzfzr()).body().asInputStream(), yxGzp.getGzfzrxm()));
        //负责人接收时间
        map.put("fzrsjy", getDateStr(yxGzp.getJssj(), "y"));//年
        map.put("fzrsjM", getDateStr(yxGzp.getJssj(), "M"));//月
        map.put("fzrsjd", getDateStr(yxGzp.getJssj(), "d"));//日
        map.put("fzrsjh", getDateStr(yxGzp.getJssj(), "h"));//时
        map.put("fzrsjm", getDateStr(yxGzp.getJssj(), "m"));//分

        //将处理好的子表集合添加到最后的map中
        map.put("gzrwList", processGzrwList(yxGzp, 4));
    }

    /**
     * 处理模板数据（电力电缆第一种工作票）
     *
     * @param yxGzp 工作票实体
     * @param map   用于替换模板数据的map集合
     */
    public void processDl1Map(YxGzp yxGzp, Map<String, Object> map, String gzpType) throws IOException {
        //安全措施子表1
        List<Map<String, String>> aqcsList1 = new ArrayList<Map<String, String>>();
        //安全措施子表2
        List<Map<String, String>> aqcsList2 = new ArrayList<Map<String, String>>();
        //安全措施子表3
        List<Map<String, String>> aqcsList3 = new ArrayList<Map<String, String>>();
        //安全措施子表4
        List<Map<String, String>> aqcsList4 = new ArrayList<Map<String, String>>();
        //安全措施子表5
        List<Map<String, String>> aqcsList5 = new ArrayList<Map<String, String>>();
        //基础信息处理
        processBaseInfo(yxGzp, map, gzpType);

        //工作任务子表
        List<YxGzprw> list = yxGzprwMapper.getListByGzpid(yxGzp.getObjId());
        int exceedLen = Math.max(list.size() - 10, 0);
        int[] defaultTableLen = {3, 3, 3, 6};//表格默认初始行数，数组下标顺序按模板从上到下

        //6.安全措施
        //1>.安全措施1
        int aqcsSize = yxGzp.getYxDldldyzgzpAqcsp1List().size();
        Map<String, Object> processMap1 = new HashMap<>();
        processMap1.put("list", aqcsList1);
        processMap1.put("dataSize", aqcsSize);
        processMap1.put("exceedLen", exceedLen);
        processMap1.put("defaultTableLen", defaultTableLen[0]);
        processMap1.put("fieldStr", "xlmc,yldlq,zxr,yzx");
        if (CollectionUtils.isNotEmpty(yxGzp.getYxDldldyzgzpAqcsp1List())) {
            for (YxDldldyzgzpAqcsp1 detail : yxGzp.getYxDldldyzgzpAqcsp1List()) {
                Map<String, String> cs1 = new HashMap<String, String>();
                cs1.put("xlmc", detail.getBpdzxlmc());
                cs1.put("yldlq", detail.getYldlq());
                cs1.put("zxr", detail.getZxr());
                cs1.put("yzx", detail.getYzx() == null ? "" : detail.getYzx() ? "是" : "否");

                aqcsList1.add(cs1);
            }
            exceedLen = processTableLines(processMap1, true, "1");
        } else {//添加空行
            exceedLen = processTableLines(processMap1, false, "1");
        }
        //2>.安全措施2
        int aqcsSize2 = yxGzp.getYxDldldyzgzpAqcsp2List().size();
        Map<String, Object> processMap2 = new HashMap<>();
        processMap2.put("list", aqcsList2);
        processMap2.put("dataSize", aqcsSize2);
        processMap2.put("exceedLen", exceedLen);
        processMap2.put("defaultTableLen", defaultTableLen[1]);
        processMap2.put("fieldStr", "scmc,jdxbh,zxr");
        if (CollectionUtils.isNotEmpty(yxGzp.getYxDldldyzgzpAqcsp2List())) {
            for (YxDldldyzgzpAqcsp2 detail : yxGzp.getYxDldldyzgzpAqcsp2List()) {
                Map<String, String> cs2 = new HashMap<String, String>();
                cs2.put("scmc", detail.getJddzscmc());
                cs2.put("jdxbh", detail.getJdxbh());
                cs2.put("zxr", detail.getZxr());

                aqcsList2.add(cs2);
            }
            exceedLen = processTableLines(processMap2, true, "1");
        } else {//添加空行
            exceedLen = processTableLines(processMap2, false, "1");
        }
        //3>.安全措施3
        int aqcsSize3 = yxGzp.getYxDldldyzgzpAqcsp3List().size();
        Map<String, Object> processMap3 = new HashMap<>();
        processMap3.put("list", aqcsList3);
        processMap3.put("dataSize", aqcsSize3);
        processMap3.put("exceedLen", exceedLen);
        processMap3.put("defaultTableLen", defaultTableLen[2]);
        processMap3.put("fieldStr", "yzszl,zxr");
        if (CollectionUtils.isNotEmpty(yxGzp.getYxDldldyzgzpAqcsp3List())) {
            for (YxDldldyzgzpAqcsp3 detail : yxGzp.getYxDldldyzgzpAqcsp3List()) {
                Map<String, String> cs3 = new HashMap<String, String>();
                cs3.put("yzszl", detail.getYzszl());
                cs3.put("zxr", detail.getZxr());

                aqcsList3.add(cs3);
            }
            exceedLen = processTableLines(processMap3, true, "1");
        } else {//添加空行
            exceedLen = processTableLines(processMap3, false, "1");
        }
        //4>.安全措施4
        int aqcsSize4 = yxGzp.getYxDldldyzgzpAqcsp4List().size();
        Map<String, Object> processMap4 = new HashMap<>();
        processMap4.put("list", aqcsList4);
        processMap4.put("dataSize", aqcsSize4);
        processMap4.put("exceedLen", exceedLen);
        processMap4.put("defaultTableLen", defaultTableLen[3]);
        processMap4.put("fieldStr", "gzdbl");
        if (CollectionUtils.isNotEmpty(yxGzp.getYxDldldyzgzpAqcsp4List())) {
            for (YxDldldyzgzpAqcsp4 detail : yxGzp.getYxDldldyzgzpAqcsp4List()) {
                Map<String, String> cs4 = new HashMap<String, String>();
                cs4.put("gzdbl", detail.getGzdbl());

                aqcsList4.add(cs4);
            }
            exceedLen = processTableLines(processMap4, true, "2");
        } else {//添加空行
            exceedLen = processTableLines(processMap4, false, "2");
        }
        //5>.安全措施5
        int aqcsSize5 = yxGzp.getYxDldldyzgzpAqcsp5List().size();
        Map<String, Object> processMap5 = new HashMap<>();
        processMap5.put("list", aqcsList5);
        processMap5.put("dataSize", aqcsSize5);
        processMap5.put("exceedLen", exceedLen);
        processMap5.put("defaultTableLen", defaultTableLen[3]);
        processMap5.put("fieldStr", "gzdbc");
        if (CollectionUtils.isNotEmpty(yxGzp.getYxDldldyzgzpAqcsp5List())) {
            for (YxDldldyzgzpAqcsp5 detail : yxGzp.getYxDldldyzgzpAqcsp5List()) {
                Map<String, String> cs5 = new HashMap<String, String>();
                cs5.put("gzdbc", detail.getBcgzddbl());

                aqcsList5.add(cs5);
            }
            exceedLen = processTableLines(processMap5, true, "2");
        } else {
            exceedLen = processTableLines(processMap5, false, "2");
        }

        //电力电缆双重名称
        map.put("dldlmc", yxGzp.getDldlmc());

        //将处理好的子表集合添加到最后的map中
        map.put("gzrwList", processGzrwList(yxGzp, 10));
        map.put("aqcsList1", aqcsList1);
        map.put("aqcsList2", aqcsList2);
        map.put("aqcsList3", aqcsList3);
        map.put("aqcsList4", aqcsList4);
        map.put("aqcsList5", aqcsList5);
    }

    /**
     * 处理模板数据（电力电缆第二种工作票）
     *
     * @param yxGzp 工作票实体
     * @param map   用于替换模板数据的map集合
     */
    public void processDl2Map(YxGzp yxGzp, Map<String, Object> map, String gzpType) throws IOException {
        //基础信息处理
        processBaseInfo(yxGzp, map, gzpType);

        //工作条件和安全措施
        map.put("gztj", yxGzp.getGztjjaqcs());
        //将处理好的子表集合添加到最后的map中
        map.put("gzrwList", processGzrwList(yxGzp, 3));
    }

    /**
     * 处理模板数据（二次工作安全措施工作票）
     *
     * @param yxGzp 工作票实体
     * @param map   用于替换模板数据的map集合
     */
    public void processAqcs2Map(YxGzp yxGzp, Map<String, Object> map, String gzpType) throws IOException {
        List<Map<String, Object>> aqcsList = new ArrayList<>();
        //获取安全措施表数据
        LambdaQueryWrapper<YxBdzdyzgzpEcaqcsp> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(YxBdzdyzgzpEcaqcsp::getGzpid, yxGzp.getObjId());
        List<YxBdzdyzgzpEcaqcsp> yxBdzdyzgzpEcaqcspList = yxBdzdyzgzpEcaqcspMapper.selectList(wrapper);
        int size = yxBdzdyzgzpEcaqcspList.size();
        //如果内容不为空
        if (CollectionUtils.isNotEmpty(yxBdzdyzgzpEcaqcspList)) {
            for (YxBdzdyzgzpEcaqcsp detail : yxBdzdyzgzpEcaqcspList) {
                Map<String, Object> mp = new HashMap<>();
                if (detail.getXh() != null) {
                    mp.put("xh", detail.getXh());
                }
                if (detail.getZx() != null) {
                    mp.put("zx", detail.getZx());
                }
                if (detail.getAqcsnr() != null) {
                    mp.put("aqcsnr", detail.getAqcsnr());
                }
                if (detail.getHf() != null) {
                    mp.put("hf", detail.getHf());
                }
                aqcsList.add(mp);
            }
            if (size < 14) {
                for (int i = size; i < 14; i++) {
                    Map<String, Object> mp = new HashMap<>();
                    mp.put("xh", "");
                    mp.put("zx", "");
                    mp.put("aqcsnr", "");
                    mp.put("hf", "");
                    aqcsList.add(mp);
                }
            }
        } else {
            for (int i = 0; i < 14; i++) {
                Map<String, Object> mp = new HashMap<>();
                mp.put("xh", "");
                mp.put("zx", "");
                mp.put("aqcsnr", "");
                mp.put("hf", "");
                aqcsList.add(mp);
            }
        }
        //基础信息处理
        processBaseInfo(yxGzp, map, gzpType);
        //工作任务
        map.put("gzrw", yxGzp.getGzrw());
        //被试设备名称
        map.put("bssbmc", yxGzp.getBssbmc());
        //二次工作安全措施票
        map.put("aqcsList", aqcsList);
    }

    /**
     * 处理模板数据（配电站工作票）
     *
     * @param yxGzp 工作票实体
     * @param map   用于替换模板数据的map集合
     */
    public void processPdzMap(YxGzp yxGzp, Map<String, Object> map, String gzpType) throws IOException {
        //基础信息处理
        processBaseInfo(yxGzp, map, gzpType);

        //工作任务
        map.put("gzrw", yxGzp.getGzrw());
        //工作地点与电杆号
        map.put("gzdd", yxGzp.getJhjgzdd());
        //安全措施
        map.put("aqcsnr", yxGzp.getAqcsnr());
        //保留带电部位
        map.put("blddbw", yxGzp.getBlhljsb());
        //补充安全措施
        map.put("bcaqcs", yxGzp.getBcaqcs());

        //开工时间（进行日期格式化）
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(" dd日 hh时 mm分");

        map.put("kgsj", yxGzp.getXkgzkssj() == null ? "" : formatter.format(yxGzp.getXkgzkssj()));
        //工作负责人
        map.put("gzfzr4", SignUtils.getSignImg(signFeignClient.getFile(yxGzp.getGzfzr()).body().asInputStream(), yxGzp.getGzfzrxm()));
        //负责人接收时间
        map.put("fzrsjy", getDateStr(yxGzp.getJssj(), "y"));//年
        map.put("fzrsjM", getDateStr(yxGzp.getJssj(), "M"));//月
        map.put("fzrsjd", getDateStr(yxGzp.getJssj(), "d"));//日
        map.put("fzrsjh", getDateStr(yxGzp.getJssj(), "h"));//时
        map.put("fzrsjm", getDateStr(yxGzp.getJssj(), "m"));//分
        //许可人
        map.put("xkrxm", SignUtils.getSignImg(signFeignClient.getFile(yxGzp.getXkr()).body().asInputStream(), yxGzp.getXkr()));

    }

    /**
     * 处理模板数据（配电站事故应急抢修单）
     *
     * @param yxGzp 工作票实体
     * @param map   用于替换模板数据的map集合
     */
    public void processPdzqxdMap(YxGzp yxGzp, Map<String, Object> map, String gzpType) throws IOException {
        //基础信息处理
        processBaseInfo(yxGzp, map, gzpType);
        //抢修班人员
        map.put("gzbry", yxGzp.getGzbry());
        //抢修任务
        map.put("qxrw", yxGzp.getQxrw());
        //安全措施
        map.put("aqcsnr", yxGzp.getAqcsnr());
        //注意事项
        map.put("zysx", yxGzp.getZysx());
    }

    /**
     * 处理模板数据（配电站检维修工作票）
     *
     * @param yxGzp 工作票实体
     * @param map   用于替换模板数据的map集合
     */
    public void processPdzjwxMap(YxGzp yxGzp, Map<String, Object> map, String gzpType) throws IOException {
        //基础信息处理
        processBaseInfo(yxGzp, map, gzpType);

        //安全措施
        map.put("aqcsnr", yxGzp.getAqcsnr());
        //工作负责人
        map.put("gzfzr2", SignUtils.getSignImg(signFeignClient.getFile(yxGzp.getGzfzr()).body().asInputStream(), yxGzp.getGzfzr()));
        //接收时间
        map.put("jssjy", getDateStr(yxGzp.getJssj(), "y"));//年
        map.put("jssjM", getDateStr(yxGzp.getJssj(), "M"));//月
        map.put("jssjd", getDateStr(yxGzp.getJssj(), "d"));//日
        map.put("jssjh", getDateStr(yxGzp.getJssj(), "h"));//时
        map.put("jssjm", getDateStr(yxGzp.getJssj(), "m"));//分
        //工作地点
        map.put("gzdd", yxGzp.getJhjgzdd());
        //补充安全措施
        map.put("bcaqcs", yxGzp.getBcaqcs());

        //工作任务list
        map.put("gzrwList", processGzrwList(yxGzp, 4));
    }

    /**
     * 处理模板数据（配电站工作任务单）
     *
     * @param yxGzp 工作票实体
     * @param map   用于替换模板数据的map集合
     */
    public void processPdzrwdMap(YxGzp yxGzp, Map<String, Object> map, String gzpType) throws IOException {
        //基础信息处理
        processBaseInfo(yxGzp, map, gzpType);

        //安全措施
        map.put("aqcsnr", yxGzp.getAqcsnr());
        //小组名称
        map.put("xzmc", yxGzp.getXzmc());
        //小组负责人
        map.put("xzfzr", SignUtils.getSignImg(signFeignClient.getFile(yxGzp.getXzfzrxm()).body().asInputStream(), yxGzp.getXzfzrxm()));
        //小组人员
        map.put("xzry", yxGzp.getXzry());
        //应装接地线
        map.put("yzjdx", yxGzp.getYzsdjdxdwz());
        //安全标示
        map.put("aqbs", yxGzp.getYzsdaqbs());
        //注意事项
        map.put("zysx", yxGzp.getQtwxdykcs());
        //工作负责人2
        map.put("gzfzr2", SignUtils.getSignImg(signFeignClient.getFile(yxGzp.getGzfzr()).body().asInputStream(), yxGzp.getGzfzr()));
        //确认时间
        map.put("qrsjy", getDateStr(yxGzp.getJssj(), "y"));//年
        map.put("qrsjM", getDateStr(yxGzp.getJssj(), "M"));//月
        map.put("qrsjd", getDateStr(yxGzp.getJssj(), "d"));//日
        map.put("qrsjh", getDateStr(yxGzp.getJssj(), "h"));//时
        map.put("qrsjm", getDateStr(yxGzp.getJssj(), "m"));//分

        //工作任务list
        map.put("gzrwList", processGzrwList(yxGzp, 1));
    }

    /**
     * 日期时间格式转换
     *
     * @param dateTime 时间
     * @param type     类型
     * @return dateStr
     */
    public String getDateStr(LocalDateTime dateTime, String type) {
        String dateStr = "    ";//填充空格
        String dateStr1 = " ";//填充空格(一半)
        switch (type) {
            case "y"://年
                if (dateTime != null) {
                    dateStr = String.valueOf(dateTime.getYear());
                }
                break;
            case "M"://月
                if (dateTime != null) {
                    dateStr = dateStr1 + String.format("%02d", dateTime.getMonthValue()) + dateStr1;
                }
                break;
            case "d"://日
                if (dateTime != null) {
                    dateStr = dateStr1 + String.format("%02d", dateTime.getDayOfMonth()) + dateStr1;
                }
                break;
            case "h"://时
                if (dateTime != null) {
                    dateStr = dateStr1 + String.format("%02d", dateTime.getHour()) + dateStr1;
                }
                break;
            case "m"://分
                if (dateTime != null) {
                    dateStr = dateStr1 + String.format("%02d", dateTime.getMinute()) + dateStr1;
                }
                break;
        }
        return dateStr;
    }

    /**
     * 处理模板中的基本数据（各工作票通用）
     *
     * @param yxGzp 工作票实体
     * @param map   用于替换模板数据的map集合
     * @param type  工作票类型
     */
    public void processBaseInfo(YxGzp yxGzp, Map<String, Object> map, String type) throws IOException {

        LocalDateTime jhkssj;//计划开始时间
        LocalDateTime jhjssj;//计划结束时间
        LocalDateTime zgbmqrsj;//主管部门确认时间
        LocalDateTime qfsj;//签发时间
        LocalDateTime hqsj;//会签时间
        String qfr;//签发人
        String hqr;//会签人
        String zgbmqrrxm;//主管部门确认人姓名
        switch (type) {
            case "dlxl1"://电力线路第一种工作票
                jhkssj = yxGzp.getJhkssj();
                jhjssj = yxGzp.getJhjssj();
                zgbmqrsj = yxGzp.getZgbmqrsj();
                qfr = yxGzprwMapper.getUserName(yxGzp.getWdwqfr());
                hqr = yxGzprwMapper.getUserName(yxGzp.getYxqfr());
                zgbmqrrxm = yxGzprwMapper.getUserName(yxGzp.getZgbmqrr());
                qfsj = yxGzp.getWdwqfsj();
                hqsj = yxGzp.getYxqfsj();
                break;
            case "dlxl2"://电力线路第二种工作票
                jhkssj = yxGzp.getJhkssj();
                jhjssj = yxGzp.getJhjssj();
                qfr = yxGzprwMapper.getUserName(yxGzp.getWdwqfr());
                hqr = yxGzprwMapper.getUserName(yxGzp.getYxqfr());
                zgbmqrrxm = yxGzprwMapper.getUserName(yxGzp.getZgbmqrr());
                zgbmqrsj = yxGzp.getZgbmqrsj();
                qfsj = yxGzp.getWdwqfsj();
                hqsj = yxGzp.getYxqfsj();
                break;
            case "dldl1"://电力电缆第一种工作票
                jhkssj = yxGzp.getJhkssj();
                jhjssj = yxGzp.getJhjssj();
                qfr = yxGzprwMapper.getUserName(yxGzp.getWdwqfr());
                hqr = yxGzprwMapper.getUserName(yxGzp.getYxqfr());
                zgbmqrrxm = yxGzprwMapper.getUserName(yxGzp.getZgbmqrr());
                zgbmqrsj = yxGzp.getZgbmqrsj();
                qfsj = yxGzp.getWdwqfsj();
                hqsj = yxGzp.getYxqfsj();
                break;
            case "dldl2"://电力电缆第二种工作票
                jhkssj = yxGzp.getJhkssj();
                jhjssj = yxGzp.getJhjssj();
                qfr = yxGzprwMapper.getUserName(yxGzp.getWdwqfr());
                hqr = yxGzprwMapper.getUserName(yxGzp.getYxqfr());
                zgbmqrrxm = yxGzprwMapper.getUserName(yxGzp.getZgbmqrr());
                zgbmqrsj = yxGzp.getZgbmqrsj();
                qfsj = yxGzp.getWdwqfsj();
                hqsj = yxGzp.getYxqfsj();
                break;
            default:
                jhkssj = yxGzp.getJhkssj();
                jhjssj = yxGzp.getJhjssj();
                qfr = yxGzprwMapper.getUserName(yxGzp.getWdwqfr());
                hqr = yxGzprwMapper.getUserName(yxGzp.getYxqfr());
                zgbmqrrxm = yxGzprwMapper.getUserName(yxGzp.getZgbmqrr());
                zgbmqrsj = yxGzp.getZgbmqrsj();
                qfsj = yxGzp.getWdwqfsj();
                hqsj = yxGzp.getYxqfsj();
                break;
        }
        //单位
        map.put("dw", yxGzp.getDwmc());
        //工作票编号
        map.put("gzpbh", yxGzp.getGzpbh());
        //1.工作负责人
        map.put("gzfzrxm", SignUtils.getSignImg(signFeignClient.getFile(yxGzp.getGzfzr()).body().asInputStream(), yxGzp.getGzfzr()));
        //工作班组
        map.put("gzbz", yxGzp.getGzbz());
        //2.工作班人员
        map.put("gzbry", yxGzp.getGzbry());
        //人数
        map.put("rs", yxGzp.getRs());
        //3工作的变、配电站名称及设备双重名称(一行34个汉字，默认显示3行)
        map.put("sbmc", WordUtils.processStr(yxGzp.getSbmc(), 34));
        //主管部门确认人姓名
        map.put("zgbmqrrxm", SignUtils.getSignImg(signFeignClient.getFile(yxGzp.getZgbmqrr()).body().asInputStream(), zgbmqrrxm));
        //5.计划工作时间
        //计划开时时间
        map.put("jhkssjy", getDateStr(jhkssj, "y"));//年
        map.put("jhkssjM", getDateStr(jhkssj, "M"));//月
        map.put("jhkssjd", getDateStr(jhkssj, "d"));//日
        map.put("jhkssjh", getDateStr(jhkssj, "h"));//时
        map.put("jhkssjm", getDateStr(jhkssj, "m"));//分

        //计划结束时间
        map.put("jhjssjy", getDateStr(jhjssj, "y"));//年
        map.put("jhjssjM", getDateStr(jhjssj, "M"));//月
        map.put("jhjssjd", getDateStr(jhjssj, "d"));//日
        map.put("jhjssjh", getDateStr(jhjssj, "h"));//时
        map.put("jhjssjm", getDateStr(jhjssj, "m"));//分

        //主管部门确认时间
        map.put("zgbmqrsjy", getDateStr(zgbmqrsj, "y"));//年
        map.put("zgbmqrsjM", getDateStr(zgbmqrsj, "M"));//月
        map.put("zgbmqrsjd", getDateStr(zgbmqrsj, "d"));//日
        map.put("zgbmqrsjh", getDateStr(zgbmqrsj, "h"));//时
        map.put("zgbmqrsjm", getDateStr(zgbmqrsj, "m"));//分
        //工作票签发人
        map.put("qfrxm", SignUtils.getSignImg(signFeignClient.getFile(yxGzp.getWdwqfr()).body().asInputStream(), qfr));
        map.put("qfsjy", getDateStr(qfsj, "y"));//年
        map.put("qfsjM", getDateStr(qfsj, "M"));//月
        map.put("qfsjd", getDateStr(qfsj, "d"));//日
        map.put("qfsjh", getDateStr(qfsj, "h"));//时
        map.put("qfsjm", getDateStr(qfsj, "m"));//分
        //工作票会签人
        map.put("hqrxm", SignUtils.getSignImg(signFeignClient.getFile(yxGzp.getYxqfr()).body().asInputStream(), hqr));
        map.put("hqsjy", getDateStr(hqsj, "y"));//年
        map.put("hqsjM", getDateStr(hqsj, "M"));//月
        map.put("hqsjd", getDateStr(hqsj, "d"));//日
        map.put("hqsjh", getDateStr(hqsj, "h"));//时
        map.put("hqsjm", getDateStr(hqsj, "m"));//分
    }

    /**
     * 处理模板中的工作任务子表
     *
     * @param yxGzp  工作票实体
     * @param mixLen 最小行数
     * @return gzrwList
     */
    public List<Map<String, String>> processGzrwList(YxGzp yxGzp, int mixLen) {
        //工作任务子表,拿到每行数据并返回
        List<Map<String, String>> gzrwList = new ArrayList<Map<String, String>>();
        if (StringUtils.isNotBlank(yxGzp.getObjId())) {
            List<YxGzprw> list = yxGzprwMapper.getListByGzpid(yxGzp.getObjId());
            if (CollectionUtils.isNotEmpty(list)) {
                for (YxGzprw yxGzprw : list) {
                    Map<String, String> rw = new HashMap<String, String>();
                    rw.put("sbmc", yxGzprw.getSbmc());
                    rw.put("gzdd", yxGzprw.getGzdd());
                    rw.put("gzrw", yxGzprw.getGzrw());
                    gzrwList.add(rw);
                }
                //补充字段
                if (list.size() < mixLen) {
                    for (int i = list.size(); i < mixLen; i++) {
                        Map<String, String> rw = new HashMap<String, String>();
                        rw.put("sbmc", "");
                        rw.put("gzdd", "");
                        rw.put("gzrw", "");
                        gzrwList.add(rw);
                    }
                }
                return gzrwList;
            }
        }
        //若为空返回单行空数据
//        Map<String, String> rw = new HashMap<String, String>();
//        rw.put("sbmc", "");
//        rw.put("gzdd", "");
//        rw.put("gzrw", "");
//        gzrwList.add(rw);
        return gzrwList;
    }

    @Override
    public YxGzp getById(Serializable id) {
        return baseMapper.getById(id);
    }


    /**
     * 工程复验表单打印
     * objId-> dg_gzp_ysx.gzp_id    得到ysx_id
     * 根据 ysx_id 去bz_ysbzmx查询   ysx字段和ysbz字段
     *
     * @param response 返回
     * @param yxGzp_id 工作票id
     * <AUTHOR> xiaojiajun
     * @date : 2022/07/07
     */
    @Override
    public void exportGcysfyjl(HttpServletResponse response, String yxGzp_id, String gcmc) throws IOException {
        //路径控制
        String os = System.getProperty("os.name");
        String projectPath;
        if (os != null && os.toLowerCase().startsWith("windows")) {
            projectPath = System.getProperty("user.dir") + File.separator + "qz-operation" + File.separator + "src" + File.separator + "main" + File.separator + "resources" + File.separator;
        } else {
            projectPath = filePath;
        }
        //工作票基本信息查询
        YxGzp yxGzp = baseMapper.getById(yxGzp_id);
        HashMap<String, Object> map = new HashMap<>();
        //基本数据查询
        processBaseInfo(yxGzp, map, "default");
        //验收时间
        map.put("xkr", SignUtils.getSignImg(signFeignClient.getFile(yxGzp.getXkr()).body().asInputStream(), yxGzp.getXkr()));
        //工作任务赋值 若没有结果,返回无
        StringBuilder gzrw = new StringBuilder("无");
        List<Map<String, String>> gzrwList = new ArrayList<>();
        List<YxGzprw> list = yxGzprwMapper.getListByGzpid(yxGzp.getObjId());
        if (CollectionUtils.isNotEmpty(list)) {
            for (YxGzprw yxGzprw : list) {
                Map<String, String> rw = new HashMap<String, String>();
                rw.put("sbmc", yxGzprw.getSbmc());
                rw.put("gzdd", yxGzprw.getGzdd());
                rw.put("gzrw", yxGzprw.getGzrw());
                gzrwList.add(rw);
            }
        }
        if (CollectionUtils.isNotEmpty(list)) {
            gzrw = new StringBuilder();
            for (Map<String, String> stringStringMap : gzrwList) {
                gzrw.append(stringStringMap.get("sbmc")).append(":").append(stringStringMap.get("gzrw")).append("\r\n");
            }
        }
        map.put("gzrw", gzrw.toString());

        //验收查询   若没有结果，返回无。
        List<Map<String, Object>> gcfydList = yxGzprwMapper.getGcfyd(yxGzp.getObjId());
        StringBuilder gcfyd = new StringBuilder("无");
        if (CollectionUtils.isNotEmpty(gcfydList)) {
            gcfyd = new StringBuilder();
            for (Map<String, Object> stringObjectMap : gcfydList) {
                gcfyd.append(stringObjectMap.get("ysx")).append(":").append(stringObjectMap.get("ysbz")).append("\r\n");
            }
        }
        map.put("gcfyd", gcfyd.toString());

        map.put("dw", yxGzp.getDwmc());
        map.put("yssj", LocalDateTimeUtils.formatDateTime(yxGzp.getTjyssj()));
        map.put("yxdw", yxGzprwMapper.getDwmc(yxGzp.getYxdw()));
        map.put("gcmc", gcmc);
        //生成编号
        map.put("bh", sequenceMapper.selectValByDefined("SEQ_GCYS", "%Y%m%d-", "3"));
        try {
            WordUtils.exportFile(projectPath + "docx" + File.separator + "gzp" + File.separator + "gcysfyjl", map, response, false);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 查询工作票统计
     *
     * @return
     * <AUTHOR> xiaojiajun
     * @since : 2022/07/11
     */
    @Override
    public Page<GzpCount> selectGzpCount(GzpCount gzpCount) {
        return yxGzprwMapper.selectGzpCount(gzpCount, gzpCount.createPage());
    }

    @Override
    public void updateYsxById(Ysbz ysbz) {
        baseMapper.updateYsxById(ysbz);
    }

    /**
     * @Description: 关联验收项导出
     * @Param: [yxGzpId, list]
     * @return: void
     * @author: xiaojj
     * @Date: 2022/7/18
     */
    @Override
    public void exportGlysx(String yxGzpId, HttpServletResponse response, Integer ysType) throws IOException {
        //路径控制
        String os = System.getProperty("os.name");
        String projectPath;
        if (os != null && os.toLowerCase().startsWith("windows")) {
            projectPath = System.getProperty("user.dir") + File.separator + "qz-operation" + File.separator + "src" + File.separator + "main" + File.separator + "resources" + File.separator;
        } else {
            projectPath = filePath;
        }
        //获取工作票基本信息
        YxGzp yxGzp = baseMapper.getById(yxGzpId);
        //基本信息处理
        Map<String, Object> map = new HashMap<>();
        processBaseInfo(yxGzp, map, "0");
        List<Map<String, Object>> list;

        list = yxGzprwMapper.getGlysx(yxGzpId, ysType);
        map.put("list", list);
        try {
            if (ysType == 0) {
                WordUtils.exportFile(projectPath + "docx" + File.separator + "gzp" + File.separator + "glysx", map, response, false);
            } else {
                WordUtils.exportFile(projectPath + "docx" + File.separator + "gzp" + File.separator + "glwtgysx", map, response, false);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public List<List<Long>> countGzp(Integer year) {
        List<List<Long>> count = new ArrayList<>(3);
        List<GzpCount> gzpCounts = baseMapper.countGzp(year);
        List<Long> allCount = new ArrayList<>(10);
        allCount.add(gzpCounts.stream().filter(gzpCount -> "1".equals(gzpCount.getLx())).count());
        allCount.add(gzpCounts.stream().filter(gzpCount -> "2".equals(gzpCount.getLx())).count());
        allCount.add(gzpCounts.stream().filter(gzpCount -> "4".equals(gzpCount.getLx())).count());
        allCount.add(gzpCounts.stream().filter(gzpCount -> "5".equals(gzpCount.getLx())).count());
        allCount.add(gzpCounts.stream().filter(gzpCount -> "6".equals(gzpCount.getLx())).count());
        allCount.add(gzpCounts.stream().filter(gzpCount -> "7".equals(gzpCount.getLx())).count());
        allCount.add(gzpCounts.stream().filter(gzpCount -> "8".equals(gzpCount.getLx())).count());
        allCount.add(gzpCounts.stream().filter(gzpCount -> "10".equals(gzpCount.getLx())).count());
        allCount.add(gzpCounts.stream().filter(gzpCount -> "12".equals(gzpCount.getLx())).count());
        allCount.add(gzpCounts.stream().filter(gzpCount -> "13".equals(gzpCount.getLx())).count());
        List<Long> passCount = new ArrayList<>(10);
        passCount.add(gzpCounts.stream().filter(gzpCount -> "1".equals(gzpCount.getLx()) && "6".equals(gzpCount.getStatus())).count());
        passCount.add(gzpCounts.stream().filter(gzpCount -> "2".equals(gzpCount.getLx()) && "6".equals(gzpCount.getStatus())).count());
        passCount.add(gzpCounts.stream().filter(gzpCount -> "4".equals(gzpCount.getLx()) && "6".equals(gzpCount.getStatus())).count());
        passCount.add(gzpCounts.stream().filter(gzpCount -> "5".equals(gzpCount.getLx()) && "6".equals(gzpCount.getStatus())).count());
        passCount.add(gzpCounts.stream().filter(gzpCount -> "6".equals(gzpCount.getLx()) && "6".equals(gzpCount.getStatus())).count());
        passCount.add(gzpCounts.stream().filter(gzpCount -> "7".equals(gzpCount.getLx()) && "6".equals(gzpCount.getStatus())).count());
        passCount.add(gzpCounts.stream().filter(gzpCount -> "8".equals(gzpCount.getLx()) && "6".equals(gzpCount.getStatus())).count());
        passCount.add(gzpCounts.stream().filter(gzpCount -> "10".equals(gzpCount.getLx()) && "6".equals(gzpCount.getStatus())).count());
        passCount.add(gzpCounts.stream().filter(gzpCount -> "12".equals(gzpCount.getLx()) && "6".equals(gzpCount.getStatus())).count());
        passCount.add(gzpCounts.stream().filter(gzpCount -> "13".equals(gzpCount.getLx()) && "6".equals(gzpCount.getStatus())).count());
        List<Long> noPassCount = new ArrayList<>(10);
        noPassCount.add(gzpCounts.stream().filter(gzpCount -> "1".equals(gzpCount.getLx()) && !"6".equals(gzpCount.getStatus())).count());
        noPassCount.add(gzpCounts.stream().filter(gzpCount -> "2".equals(gzpCount.getLx()) && !"6".equals(gzpCount.getStatus())).count());
        noPassCount.add(gzpCounts.stream().filter(gzpCount -> "4".equals(gzpCount.getLx()) && !"6".equals(gzpCount.getStatus())).count());
        noPassCount.add(gzpCounts.stream().filter(gzpCount -> "5".equals(gzpCount.getLx()) && !"6".equals(gzpCount.getStatus())).count());
        noPassCount.add(gzpCounts.stream().filter(gzpCount -> "6".equals(gzpCount.getLx()) && !"6".equals(gzpCount.getStatus())).count());
        noPassCount.add(gzpCounts.stream().filter(gzpCount -> "7".equals(gzpCount.getLx()) && !"6".equals(gzpCount.getStatus())).count());
        noPassCount.add(gzpCounts.stream().filter(gzpCount -> "8".equals(gzpCount.getLx()) && !"6".equals(gzpCount.getStatus())).count());
        noPassCount.add(gzpCounts.stream().filter(gzpCount -> "10".equals(gzpCount.getLx()) && !"6".equals(gzpCount.getStatus())).count());
        noPassCount.add(gzpCounts.stream().filter(gzpCount -> "12".equals(gzpCount.getLx()) && !"6".equals(gzpCount.getStatus())).count());
        noPassCount.add(gzpCounts.stream().filter(gzpCount -> "13".equals(gzpCount.getLx()) && !"6".equals(gzpCount.getStatus())).count());
        count.add(allCount);
        count.add(passCount);
        count.add(noPassCount);
        return count;
    }

    @Override
    public List<Ysbz> getYsbzByGzpId(String gzpId) {
        return baseMapper.getYsbzByGzpId(gzpId);
    }

    @Override
    public void deleteYsxList(List<Ysbz> ysbzs) {
        baseMapper.deleteYsxList(ysbzs);
    }

    @Override
    public Ysbz getYsbzByObjId(String objId) {
        return baseMapper.getYsbzByObjId(objId);
    }

    /**
     * 处理导出的表格空行数及超出的行数
     */
    private int processTableLines(Map<String, Object> processMap, boolean isExceed, String processType) {
        List<Map<String, String>> list = (List<Map<String, String>>) processMap.get("list");
        int dataSize = (int) processMap.get("dataSize");
        int exceedLen = (int) processMap.get("exceedLen");
        int defaultTableLen = (int) processMap.get("defaultTableLen");
        String fieldStr = (String) processMap.get("fieldStr");
        switch (processType) {
            case "1"://普通情况
                //是否超出最大初始行数
                if (isExceed) {
                    //如果超出该表格默认行数
                    if (dataSize + exceedLen >= defaultTableLen) {
                        exceedLen += dataSize - defaultTableLen;//重新计算超出的行数
                    } else {
                        //补充空行
                        if (dataSize < defaultTableLen) {
                            for (int i = dataSize; i < defaultTableLen - exceedLen; i++) {
                                Map<String, String> param = new HashMap<>();
                                for (String s : fieldStr.split(",")) {
                                    param.put(s, "");
                                }
                                list.add(param);
                            }
                        }
                        exceedLen = 0;//重置超出的行数
                    }
                } else {
                    if (exceedLen < defaultTableLen) {
                        processEmptyLines(list, exceedLen, defaultTableLen, fieldStr);
                        exceedLen = 0;//重置超出的行数
                    } else {
                        exceedLen -= defaultTableLen;
                    }
                }
                break;
            case "2"://最后一部分表格，且表格由两部分组成
                if (isExceed) {
                    //补充空行
                    if (dataSize < defaultTableLen) {
                        for (int i = dataSize; i < defaultTableLen; i++) {
                            Map<String, String> param = new HashMap<>();
                            for (String s : fieldStr.split(",")) {
                                param.put(s, "");
                            }
                            list.add(param);
                        }
                    }
                } else {
                    if (exceedLen < defaultTableLen) {
                        processEmptyLines(list, exceedLen, defaultTableLen, fieldStr);
                    }
                }
                break;
            default:
                break;
        }
        return exceedLen;
    }

    //处理空行
    private void processEmptyLines(List<Map<String, String>> list, int exceedLen, int defaultTableLen, String fieldStr) {
        for (int i = 0; i < defaultTableLen - exceedLen; i++) {
            Map<String, String> param = new HashMap<>();
            for (String s : fieldStr.split(",")) {
                param.put(s, "");
            }
            list.add(param);
        }
    }

    //处理map
    private List<Map<String, String>> processMapData(List list1, List list2, String str) {
        List<Map<String, String>> result;
        String[] arr = str.split(",");
        if (list1.size() > list2.size()) {
            //处理数据
            for (int i = 0; i < list1.size(); i++) {
                Map<String, String> map1 = (Map<String, String>) list1.get(i);
                if (i < list2.size()) {
                    Map<String, String> map2 = (Map<String, String>) list2.get(i);
                    map1.put(arr[0], map2.get(arr[0]));
                } else {
                    map1.put(arr[0], "");
                }
            }
            result = list1;
        } else {
            for (int i = 0; i < list2.size(); i++) {
                Map<String, String> map2 = (Map<String, String>) list2.get(i);
                if (i < list1.size()) {
                    Map<String, String> map1 = (Map<String, String>) list1.get(i);
                    map2.put(arr[1], map1.get(arr[1]));
                } else {
                    map2.put(arr[1], "");
                }
            }
            result = list2;
        }
        return result;
    }

    @Override
    public void exportExcel(HttpServletResponse response, YxGzp yxGzp) {
        List<YxGzp> dataList = baseMapper.getList1(yxGzp);

        String os = System.getProperty("os.name");
        String templatePath;
        if (os != null && os.toLowerCase().startsWith("windows")) {
            templatePath = System.getProperty("user.dir") + File.separator + "qz-operation" + File.separator + "src" + File.separator + "main" + File.separator + "resources" + File.separator + "excel" + File.separator + "yxGzp.xls";
        } else {
            templatePath = filePath + "excel" + File.separator + "yxGzp.xls";
        }

        Map<String, Object> map = new HashMap<>();
        //子表
        List<Map<String, String>> map1 = new ArrayList<>();
        int xh = 0;//序号
        for (YxGzp gzp : dataList) {
            xh += 1;
            map1.add(processJdMap(gzp, xh));
        }

        map.put("dataList", map1);
        //工具类导出，合并单元格
        ExportUtils.exportExcelByEasyPoi(response, templatePath, map, 1, 0);
    }

    private Map<String, String> processJdMap(YxGzp gzp, int xh) {
        //工作任务处理
        List<YxGzprw> yxGzprws = yxGzprwMapper.selectList(new LambdaQueryWrapper<YxGzprw>()
                .eq(YxGzprw::getGzpid, gzp.getObjId())
                .orderByAsc(YxGzprw::getXh)
        );
        String bdgzrw = "";
        String pdGzrw = "";
        if (!yxGzprws.isEmpty()) {
            bdgzrw = yxGzprws.stream().map(e -> e.getSbmc() == null ? "" : e.getSbmc() + ": " + e.getGzrw()).collect(Collectors.joining("\n"));
            pdGzrw = yxGzprws.stream().map(e -> e.getGzdd() == null? "" : e.getGzdd() + ": " + e.getGzrw()).collect(Collectors.joining("\n"));
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日HH时mm分");
        Map<String, String> lm1 = new HashMap<>();
        lm1.put("xh", String.valueOf(xh));
        switch (gzp.getLx()) {
            case 1:
            case 12:
                lm1.put("sbmc", gzp.getSbmc());
                lm1.put("gzrw", bdgzrw);
                break;
            case 2:
            case 13:
                lm1.put("sbmc", gzp.getSbmc());
                lm1.put("gzrw", bdgzrw);
                break;
            case 3:
            case 14:
                lm1.put("sbmc", "");
                lm1.put("gzrw", gzp.getQxrw());
                break;
            case 4:
                lm1.put("sbmc", gzp.getSbmc());
                lm1.put("gzrw", bdgzrw);
                break;
            case 5:
                lm1.put("sbmc", "");
                lm1.put("gzrw", bdgzrw);
                break;
            case 6:
                lm1.put("sbmc", gzp.getDldlmc());
                lm1.put("gzrw", bdgzrw);
                break;
            case 7:
                lm1.put("sbmc", "");
                lm1.put("gzrw", bdgzrw);
                break;
            case 8:
                lm1.put("sbmc", gzp.getJhjgzdd());
                lm1.put("gzrw", gzp.getGzrw());
                break;
            case 9:
                lm1.put("sbmc", "");
                lm1.put("gzrw", gzp.getQxrw());
                break;
            case 10:
                lm1.put("sbmc", "");
                lm1.put("gzrw", bdgzrw);
                break;
            case 11:
                lm1.put("sbmc", "");
                lm1.put("gzrw", pdGzrw);
                break;
            default:
                lm1.put("sbmc", "");
        }
        lm1.put("gzpbh", gzp.getGzpbh() == null ? "" : gzp.getGzpbh());
        lm1.put("dwmc", gzp.getDwmc() == null ? "" : gzp.getDwmc());
        lm1.put("gzfzr", gzp.getGzfzrxm() == null ? "" : gzp.getGzfzrxm());
        lm1.put("gzbry", gzp.getGzbry() == null ? "" : gzp.getGzbry());
        lm1.put("rs", gzp.getRs() == null ? "" : gzp.getRs().toString());
        lm1.put("jhkssj", gzp.getJhkssj() == null ? "" : formatter.format(gzp.getJhkssj()));
        lm1.put("jhjssj", gzp.getJhjssj() == null ? "" : formatter.format(gzp.getJhjssj()));
        lm1.put("wdwqfr", gzp.getWdwqfrxm() == null ? "" : gzp.getWdwqfrxm());
        lm1.put("yxqfr", gzp.getYxqfrxm() == null ? "" : gzp.getYxqfrxm());
        lm1.put("zgbmqrr", gzp.getZgbmqrrxm() == null ? "" : gzp.getZgbmqrrxm());
        lm1.put("yxqfsj", gzp.getYxqfsj() == null ? "" : formatter.format(gzp.getYxqfsj()));
        return lm1;
    }

}
