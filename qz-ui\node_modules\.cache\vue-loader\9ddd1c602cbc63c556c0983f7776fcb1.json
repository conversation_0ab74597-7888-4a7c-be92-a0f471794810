{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\bdgl\\bdsbgl.vue?vue&type=style&index=0&id=47374033&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\bdgl\\bdsbgl.vue", "mtime": 1706897324519}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmhlYWQtY29udGFpbmVyIHsKICBtYXJnaW46IDAgYXV0bzsKICB3aWR0aDogOTglOwogIGhlaWdodDogNzZ2aDsKICBvdmVyZmxvdzogYXV0bzsKfQoKLmJveC1jYXJkIHsKICBtYXJnaW4tYm90dG9tOiAxNXB4OwoKICAuZWwtY2FyZF9faGVhZGVyIHsKICAgIGJhY2tncm91bmQtY29sb3I6IHJnYigyMzUsIDI0NSwgMjU1KSAhaW1wb3J0YW50OwogIH0KfQoKLmJveC1jYXJkTGlzdCB7CiAgaGVpZ2h0OiA3MCU7Cn0KCi5pdGVtIHsKICB3aWR0aDogMjAwcHg7CiAgZmxvYXQ6IGxlZnQ7Cn0KCiNtYWluX2NvbnRhaW5lcl9kaiB7CiAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gODRweCk7Cn0KCi5hc2lkZV9oZWlnaHQgewogIGhlaWdodDogOTYlOwp9CgouZGVmZWN0IC5lbC1mb3JtLWl0ZW06bnRoLWNoaWxkKG9kZCkgewogIG1hcmdpbi1yaWdodDogNzBweDsKfQoKLyrog4zmma/popzoibLosIPmlbQqLwojbWFpbl9jb250YWluZXJfZGosCiNtYWluX2NvbnRhaW5lcl9kaiAuZWwtYXNpZGUgewogIGJhY2tncm91bmQtY29sb3I6ICNiNGNhZjE7Cn0KCi8vL2RlZXAvIC5xeGxyX2RpYWxvZ19pbnNlcnQgLmVsLWRpYWxvZ19faGVhZGVyIHsKLy8gIGJhY2tncm91bmQtY29sb3I6ICM4ZWIzZjU7Ci8vfQoKLyogL2RlZXAvIC5wbXlCdG4gewogICAgYmFja2dyb3VuZDogIzhlYjNmNTsKICB9Ki8KCi8qL2RlZXAvIC5hZGRfc3lfdHlycSAuZWwtaW5wdXQtLW1lZGl1bSAuZWwtaW5wdXRfX2lubmVyIHsqLwovKiAgd2lkdGg6IDIwMHB4OyovCi8qfSovCgovKua3u+WKoOW8ueWHuuahhuW+l+WuveW6piovCi8qL2RlZXAvIC5lbC1pbnB1dC0tbWVkaXVtIC5lbC1pbnB1dF9faW5uZXIgeyovCi8qICB3aWR0aDogMjAwcHg7Ki8KLyp9Ki8KLmVsLXNlbGVjdCB7CiAgd2lkdGg6IDEwMCU7Cn0KCi5lbC1kYXRlLWVkaXRvciB7CiAgd2lkdGg6IDEwMCU7Cn0KCi5lbC1jYXJvdXNlbF9faXRlbSBoMyB7CiAgY29sb3I6ICM0NzU2Njk7CiAgZm9udC1zaXplOiAxNHB4OwogIG9wYWNpdHk6IDAuNzU7CiAgbGluZS1oZWlnaHQ6IDE1MHB4Owp9CgouZWwtY2Fyb3VzZWxfX2l0ZW06bnRoLWNoaWxkKDJuKSB7CiAgYmFja2dyb3VuZC1jb2xvcjogIzk5YTliZjsKfQoKLmVsLWNhcm91c2VsX19pdGVtOm50aC1jaGlsZCgybiArIDEpIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZDNkY2U2Owp9CgovKiDorr7nva7mu5rliqjmnaHnmoTmoLflvI8gKi8KOjotd2Via2l0LXNjcm9sbGJhciB7CiAgd2lkdGg6IDEycHg7Cn0KCi8qIOa7muWKqOanvSAqLwo6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHsKICAvLy13ZWJraXQtYm94LXNoYWRvdzppbnNldDAwNnB4cmdiYSgwLDAsMCwwLjMpOwogIGJvcmRlci1yYWRpdXM6IDEwcHg7Cn0KCi8qIOa7muWKqOadoea7keWdlyAqLwo6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHsKICBib3JkZXItcmFkaXVzOiAxMHB4OwogIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC4xKTsKICAvLy13ZWJraXQtYm94LXNoYWRvdzpnYmEoMCwwLDAsMC41KTsKfQoKOjotd2Via2l0LXNjcm9sbGJhci10aHVtYjp3aW5kb3ctaW5hY3RpdmUgewogIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC4xKTsKfQoKW2RhdGEtdi02N2E5NzRiMV06Oi13ZWJraXQtc2Nyb2xsYmFyIHsKICB3aWR0aDogOHB4Owp9CgouaXRlbSB7CiAgd2lkdGg6IDIyNXB4OwogIGZsb2F0OiBsZWZ0Owp9Ci9kZWVwLy5ib3gtY2FyZCB7CiAgbWFyZ2luOiAwIDZweDsKfQovL+acieWtkOiKgueCuSDkuJTmnKrlsZXlvIAKLmVsLXRyZWUgOjp2LWRlZXAgLmVsLWljb24tY2FyZXQtcmlnaHQ6YmVmb3JlIHsKICBiYWNrZ3JvdW5kOiB1cmwoIi4uLy4uLy4uLy4uL2Fzc2V0cy9pbWFnZS9hZGQucG5nIikgbm8tcmVwZWF0IDA7CiAgY29udGVudDogIiI7CiAgZGlzcGxheTogYmxvY2s7CiAgd2lkdGg6IDE2cHg7CiAgaGVpZ2h0OiAxNnB4OwogIGZvbnQtc2l6ZTogMTZweDsKICBiYWNrZ3JvdW5kLXNpemU6IDE2cHg7Cn0KLy/mnInlrZDoioLngrkg5LiU5bey5bGV5byACi5lbC10cmVlCiAgOjp2LWRlZXAKICAuZWwtdHJlZS1ub2RlX19leHBhbmQtaWNvbi5leHBhbmRlZC5lbC1pY29uLWNhcmV0LXJpZ2h0OmJlZm9yZSB7CiAgYmFja2dyb3VuZDogdXJsKCIuLi8uLi8uLi8uLi9hc3NldHMvaW1hZ2UvcHJlcC5wbmciKSBuby1yZXBlYXQgMDsKICBjb250ZW50OiAiIjsKICBkaXNwbGF5OiBibG9jazsKICB3aWR0aDogMTZweDsKICBoZWlnaHQ6IDE2cHg7CiAgZm9udC1zaXplOiAxNnB4OwogIGJhY2tncm91bmQtc2l6ZTogMTZweDsKfQovL+ayoeacieWtkOiKgueCuQouZWwtdHJlZSA6OnYtZGVlcCAuZWwtdHJlZS1ub2RlX19leHBhbmQtaWNvbi5pcy1sZWFmOjpiZWZvcmUgewogIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50OwogIGNvbnRlbnQ6ICIiOwogIGRpc3BsYXk6IGJsb2NrOwogIHdpZHRoOiAxNnB4OwogIGhlaWdodDogMTZweDsKICBmb250LXNpemU6IDE2cHg7CiAgYmFja2dyb3VuZC1zaXplOiAxNnB4Owp9Cg=="}, {"version": 3, "sources": ["bdsbgl.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+gDA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "bdsbgl.vue", "sourceRoot": "src/views/dagangOilfield/dwzygl/bdgl", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"16\">\n      <el-col :span=\"4\">\n        <el-card shadow=\"never\" style=\"background:#e0f8ed;padding-top:10px;\">\n          <div>\n            <el-col>\n              <el-form label-width=\"80px\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"所属公司:\">\n                    <el-select\n                      v-model=\"treeForm.ssdwbm\"\n                      placeholder=\"请选择所属公司\"\n                      @change=\"getNewTreeInfo\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in OrganizationSelectedList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"String(item.value)\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"电压等级:\">\n                    <el-select\n                      v-model=\"treeForm.dydjbm\"\n                      placeholder=\"请选择电压等级\"\n                      @change=\"getNewTreeInfo\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in VoltageLevelSelectedList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"快速查询:\">\n                    <el-input\n                      placeholder=\"输入关键字进行过滤\"\n                      v-model=\"filterText\"\n                      clearable\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-form>\n            </el-col>\n          </div>\n          <div class=\"text head-container\">\n            <el-col style=\"padding:0\">\n              <el-tree\n                :expand-on-click-node=\"true\"\n                id=\"tree\"\n                :data=\"treeOptions\"\n                @node-click=\"handleNodeClick\"\n                ref=\"tree\"\n                :filter-node-method=\"filterNode\"\n                node-key=\"id\"\n                :default-expanded-keys=\"['0']\"\n                :default-checked-keys=\"['0']\"\n                :indent=\"18\"\n                accordion\n              />\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo1.data\"\n          :field-list=\"filterInfo1.fieldList\"\n          :width=\"{ labelWidth: 120, itemWidth: 180 }\"\n          @handleReset=\"filterReset\"\n        />\n        <el-white class=\"button-group\">\n          <div v-if=\"isShow3\" class=\"button_btn\">\n            <el-button\n              icon=\"el-icon-plus\"\n              @click=\"sbAddSensorButton\"\n              v-hasPermi=\"['zlsbtz:button:add']\"\n              type=\"primary\"\n              >新增</el-button\n            >\n            <el-button\n              icon=\"el-icon-delete\"\n              v-hasPermi=\"['zlsbtz:button:delete']\"\n              type=\"danger\"\n              @click=\"removeAsset\"\n              >删除</el-button\n            >\n            <!--<el-button icon=\"el-icon-s-check\" type=\"success\" @click=\"spZtbgjl\" :disabled=\"spButtonDisabled\">审批</el-button>-->\n          </div>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"64vh\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"updateAsset(scope.row)\"\n                  v-hasPermi=\"['zlsbtz:button:update']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                ></el-button>\n                <el-button\n                  @click=\"assetDetails(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!--设备展示结束-->\n    <!-- 一次设备弹出框开始展示设备履历 -->\n    <el-dialog\n      title=\"设备详情\"\n      :visible.sync=\"dialogFormVisible\"\n      width=\"60%\"\n      :before-close=\"resetForm\"\n      v-dialogDrag\n    >\n      <el-tabs v-model=\"activeTabName\">\n        <el-tab-pane label=\"基本信息\" name=\"sbDesc\">\n          <!--          <div class=\"block\" style=\"width: 50%;height: 50%;margin-bottom: 2%;float: right\">-->\n          <!--            <span class=\"demonstration\">设备图片</span>-->\n          <!--            <el-carousel trigger=\"click\" height=\"150px\" indicator-position=\"none\" :interval=\"2000\" type=\"card\">-->\n          <!--              <el-carousel-item v-for=\"(img,index) in imgList\" :key=\"index\">-->\n          <!--                <img :src=\"img.url\" width=\"100%\" height=\"100%\">-->\n          <!--              </el-carousel-item>-->\n          <!--            </el-carousel>-->\n          <!--          </div>-->\n          <el-form\n            :model=\"sbxxForm\"\n            label-width=\"130px\"\n            ref=\"sbxxForm\"\n            :rules=\"rules\"\n            :disabled=\"assetIsDisable\"\n          >\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-form-item label=\"所属公司\" prop=\"ssgs\">\n                  <el-select\n                    v-model=\"sbxxForm.ssgs\"\n                    placeholder=\"请选择所属公司\"\n                    filterable\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in OrganizationSelectedList\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"String(item.value)\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"所属电站\" prop=\"ssbdz\">\n                  <el-select\n                    v-model=\"sbxxForm.ssbdz\"\n                    placeholder=\"请选择所属电站\"\n                    filterable\n                    @change=\"bdzOptionsChangeClick\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in bdzOptionsDataList\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"所属间隔\" prop=\"ssjg\">\n                  <el-select\n                    v-model=\"sbxxForm.ssjg\"\n                    placeholder=\"请选择所属间隔\"\n                    filterable\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in jgOptionsDataList\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"设备代码\" prop=\"sbdm\">\n                  <el-input\n                    v-model=\"sbxxForm.sbdm\"\n                    placeholder=\"请填写设备代码\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"设备名称\" prop=\"sbmc\">\n                  <el-input\n                    v-model=\"sbxxForm.sbmc\"\n                    placeholder=\"请填写设备名称\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"设备类型\" prop=\"assetTypeCode\">\n                  <el-select\n                    v-model=\"sbxxForm.assetTypeCode\"\n                    placeholder=\"请选择设备类型\"\n                    @change=\"showParams\"\n                    filterable\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in sblxOptionsDataSelected\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"电压等级\" prop=\"dydjbm\">\n                  <el-select\n                    v-model=\"sbxxForm.dydjbm\"\n                    placeholder=\"请选择电压等级\"\n                  >\n                    <el-option\n                      v-for=\"item in dydjOptionsDataList\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"设备状态\" prop=\"sbzt\">\n                  <el-select\n                    v-model=\"sbxxForm.sbzt\"\n                    placeholder=\"请选择设备状态\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in sbztOptionsDataList\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"相别\">\n                  <el-input\n                    v-model=\"sbxxForm.xb\"\n                    placeholder=\"请填写相别\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"相数\">\n                  <el-input\n                    v-model=\"sbxxForm.xs\"\n                    placeholder=\"请填写相数\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"安装位置\">\n                  <el-input\n                    v-model=\"sbxxForm.azwz\"\n                    placeholder=\"请填写安装位置\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"用途\">\n                  <el-input\n                    v-model=\"sbxxForm.yt\"\n                    placeholder=\"请填写用途\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"型号\">\n                  <el-select v-model=\"sbxxForm.xh\" placeholder=\"请选择型号\">\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"产品代号\">\n                  <el-input\n                    v-model=\"sbxxForm.cpdh\"\n                    placeholder=\"请填写产品代号\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"额定电压\">\n                  <el-input\n                    v-model=\"sbxxForm.eddy\"\n                    placeholder=\"请填写额定电压\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"额定频率\">\n                  <el-input\n                    v-model=\"sbxxForm.edpl\"\n                    placeholder=\"请填写额定频率\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"使用环境\">\n                  <el-input\n                    v-model=\"sbxxForm.syhj\"\n                    placeholder=\"请填写使用环境\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"生产厂家\">\n                  <el-input\n                    v-model=\"sbxxForm.sccj\"\n                    placeholder=\"请填写生产厂家\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"制造国家\">\n                  <el-input\n                    v-model=\"sbxxForm.zzgj\"\n                    placeholder=\"请填写制造国家\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"组合设备类型\">\n                  <el-input\n                    v-model=\"sbxxForm.zhsblxbm\"\n                    placeholder=\"请填写组合设备类型\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"组合设备类型名称\">\n                  <el-input\n                    v-model=\"sbxxForm.zhsblx\"\n                    placeholder=\"请填写组合设备类型名称\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"额定电流\" prop=\"eddl\">\n                  <el-input\n                    v-model=\"sbxxForm.eddl\"\n                    placeholder=\"请填写额定电流\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"运行编号\">\n                  <el-input\n                    v-model=\"sbxxForm.yxbh\"\n                    placeholder=\"请填写运行编号\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"出厂编号\">\n                  <el-input\n                    v-model=\"sbxxForm.ccbh\"\n                    placeholder=\"请填写出厂编号\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"出厂日期\" class=\"add_sy_tyrq\">\n                  <el-date-picker\n                    v-model=\"sbxxForm.ccrq\"\n                    type=\"date\"\n                    placeholder=\"选择日期\"\n                    format=\"yyyy-MM-dd\"\n                    value-format=\"yyyy-MM-dd\"\n                  >\n                  </el-date-picker>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"投运日期\" class=\"add_sy_tyrq\">\n                  <el-date-picker\n                    v-model=\"sbxxForm.tyrq\"\n                    type=\"date\"\n                    placeholder=\"选择日期\"\n                    format=\"yyyy-MM-dd\"\n                    value-format=\"yyyy-MM-dd\"\n                  >\n                  </el-date-picker>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row :gutter=\"20\">\n              <el-col :span=\"24\">\n                <el-form-item label=\"备注\" prop=\"bz\">\n                  <el-input\n                    v-model=\"sbxxForm.bz\"\n                    type=\"textarea\"\n                    rows=\"2\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </el-form>\n        </el-tab-pane>\n        <el-tab-pane label=\"技术参数\" name=\"sbjscs\">\n          <el-form :model=\"jscsForm\" label-width=\"130px\">\n            <el-col :span=\"8\" v-for=\"item in jscsLabelList\">\n              <el-form-item\n                :label=\"\n                  item.dw != '' ? item.label + '(' + item.dw + ')' : item.label\n                \"\n              >\n                <el-input\n                  v-if=\"item.type === 'input'\"\n                  v-model=\"jscsForm[item.jscsbm]\"\n                  placeholder=\"\"\n                >\n                </el-input>\n                <el-select\n                  v-if=\"item.type === 'select'\"\n                  v-model=\"jscsForm[item.jscsbm]\"\n                  placeholder=\"\"\n                >\n                  <el-option\n                    v-for=\"(childItem, key) in item.options\"\n                    :key=\"key\"\n                    :label=\"childItem.label\"\n                    :value=\"childItem.value\"\n                    :disabled=\"childItem.disabled\"\n                    style=\"display: flex; align-items: center;\"\n                    clearable\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-form>\n        </el-tab-pane>\n        <el-tab-pane label=\"设备履历\" name=\"sbRecord\">\n          <el-tabs\n            v-model=\"sbllDescTabName\"\n            @tab-click=\"handleSbllDescTabNameClick\"\n            type=\"card\"\n          >\n            <el-tab-pane label=\"试验记录\" name=\"syjl\">\n              <el-table\n                stripe\n                border\n                v-loading=\"loading\"\n                :data=\"sblvsyjlList\"\n                max-height=\"550\"\n              >\n                <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n                <el-table-column label=\"试验专业\" align=\"center\" prop=\"syzy\" />\n                <el-table-column\n                  label=\"试验性质\"\n                  align=\"center\"\n                  prop=\"syxz\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"试验名称\"\n                  align=\"center\"\n                  prop=\"symc\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"工作地点\"\n                  align=\"center\"\n                  prop=\"gzdd\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"试验设备\"\n                  align=\"center\"\n                  prop=\"sysb\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column label=\"试验报告\" align=\"center\" prop=\"sybg\" />\n                <el-table-column\n                  label=\"天气\"\n                  align=\"center\"\n                  prop=\"tq\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"试验日期\"\n                  align=\"center\"\n                  prop=\"syrq\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"录入人\"\n                  align=\"center\"\n                  prop=\"lrr\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"试验结论\"\n                  align=\"center\"\n                  prop=\"syjl\"\n                  :show-overflow-tooltip=\"true\"\n                />\n              </el-table>\n            </el-tab-pane>\n            <el-tab-pane label=\"缺陷记录\" name=\"qxjl\">\n              <el-table\n                stripe\n                border\n                v-loading=\"loading\"\n                :data=\"sbllqxjlList\"\n                max-height=\"550\"\n              >\n                <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n                <el-table-column label=\"所属公司\" align=\"center\" prop=\"ssgs\" />\n                <el-table-column\n                  label=\"变电站名称\"\n                  align=\"center\"\n                  prop=\"bdzmc\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"设备类型\"\n                  align=\"center\"\n                  prop=\"sblx\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"缺陷性质\"\n                  align=\"center\"\n                  prop=\"qxxz\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"电压等级\"\n                  align=\"center\"\n                  prop=\"dydj\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column label=\"设备型号\" align=\"center\" prop=\"sbxh\" />\n                <el-table-column\n                  label=\"生产厂家\"\n                  align=\"center\"\n                  prop=\"sccj\"\n                  :show-overflow-tooltip=\"true\"\n                />\n              </el-table>\n            </el-tab-pane>\n            <el-tab-pane label=\"状态变更记录\" name=\"ztbgjl\">\n              <comp-table\n                :table-and-page-info=\"resumPageInfo\"\n                @getMethod=\"getResumList\"\n                @update:multipleSelection=\"handleSelectionChange\"\n                height=\"30vh\"\n              />\n            </el-tab-pane>\n          </el-tabs>\n        </el-tab-pane>\n        <el-tab-pane label=\"附属设施\" name=\"fsss\">\n          <el-table\n            stripe\n            border\n            v-loading=\"loading\"\n            :data=\"fsssList\"\n            max-height=\"550\"\n          >\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n            <el-table-column label=\"设备名称\" align=\"center\" prop=\"syzy\" />\n            <el-table-column\n              label=\"设备型号\"\n              align=\"center\"\n              prop=\"syxz\"\n              :show-overflow-tooltip=\"true\"\n            />\n            <el-table-column\n              label=\"生产厂家\"\n              align=\"center\"\n              prop=\"symc\"\n              :show-overflow-tooltip=\"true\"\n            />\n            <el-table-column\n              label=\"出厂日期\"\n              align=\"center\"\n              prop=\"gzdd\"\n              :show-overflow-tooltip=\"true\"\n            />\n            <el-table-column\n              label=\"投运日期\"\n              align=\"center\"\n              prop=\"sysb\"\n              :show-overflow-tooltip=\"true\"\n            />\n            <el-table-column label=\"更换日期\" align=\"center\" prop=\"sybg\" />\n          </el-table>\n        </el-tab-pane>\n      </el-tabs>\n      <div slot=\"footer\" class=\"dialog-footer\" v-show=\"!assetIsDisable\">\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submit\" class=\"pmyBtn\"\n          >确 定</el-button\n        >\n      </div>\n    </el-dialog>\n    <el-dialog\n      title=\"设备状态变更\"\n      :visible.sync=\"dialogVisible\"\n      width=\"30%\"\n      append-to-body\n      v-dialogDrag\n    >\n      <el-form :inline=\"true\" label-width=\"100px\" class=\"qxlr_dialog_insert\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"设备状态:\">\n              <el-select v-model=\"updateList.sbzt\">\n                <el-option\n                  v-for=\"item in sbztOptionsDataList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitStatus\">确 定</el-button>\n      </span>\n    </el-dialog>\n\n    <!--状态变更使用-->\n    <activiti\n      :processData=\"processData\"\n      :isShow=\"isShow\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n    ></activiti>\n    <time-line\n      :value=\"timeLineShow\"\n      :timeData=\"timeData\"\n      @closeTimeLine=\"colseTimeLine\"\n    />\n  </div>\n</template>\n\n<script>\nimport {\n  addAsset,\n  addBdz,\n  addJg,\n  getAssetListInfo,\n  getJgInfoList,\n  getTreeInfo,\n  removeAsset,\n  removeBdz,\n  removeJg,\n  getOrganizationSelected,\n  getNewTreeInfo,\n  getBdAsesetListPage,\n  getBdzDataListSelected,\n  getJgDataListSelected,\n  getSblxDataListSelected,\n  updateStatus\n} from \"@/api/dagangOilfield/asset/bdsbtz\";\nimport { getResumDataList } from \"@/api/dagangOilfield/asset/sdsb\";\nimport TechnicalParameter from \"@/views/dagangOilfield/bzgl/sbbzk/technicalParameter\";\nimport {\n  getParamDataList,\n  getParamsValue,\n  saveParamValue\n} from \"@/api/dagangOilfield/asset/parameters\";\nimport { saveOrUpdate } from \"@/api/yxgl/bdyxgl/qxgl\";\n//流程\nimport activiti from \"com/activiti\";\nimport timeLine from \"com/timeLine\";\nimport { HistoryList } from \"@/api/activiti/processTask\";\n\nexport default {\n  name: \"qxbzk\",\n  components: { TechnicalParameter, activiti, timeLine },\n  data() {\n    return {\n      //流程参数\n      processData: {\n        processDefinitionKey: \"dwzyztbg\",\n        businessKey: \"\",\n        businessType: \"变电设备台账\",\n        variables: {},\n        nextUser: \"\",\n        defaultFrom: true,\n        processType: \"complete\"\n      },\n      //流程变更申请标题\n      activitiOption: { title: \"状态变更申请\" },\n      //流程跟踪数据\n      timeData: [],\n      //流程跟踪显示隐藏\n      timeLineShow: false,\n      //流程组件显示隐藏\n      isShow: false,\n\n      resumeQuery: {\n        foreignNum: undefined,\n        sblx: undefined\n      },\n      updateList: {\n        sbzt: \"\",\n        objId: \"\"\n      },\n      dialogVisible: false,\n      //设备删除选择列\n      selectRows: [],\n      //设备类型下拉框数据\n      sblxOptionsDataSelected: [],\n      //查询变电站下拉框数据的参数\n      selectBdzOptionsParam: {},\n      //查询间隔下拉框数据的参数\n      selectJgOptionsParam: {},\n      //设备表单\n      sbxxForm: {},\n      //设备附属设施list\n      fsssList: [],\n      //变电设备查询参数\n      bdzqueryParams: {\n        ssgs: undefined,\n        dydjbm: undefined,\n        ssbdz: undefined,\n        ssjg: undefined,\n        sbmc: undefined,\n        assetTypeCode: undefined,\n        sbzt: undefined,\n        pageNum: 1,\n        pageSize: 10\n      },\n      //新增设备时设备状态下拉框数据\n      sbztOptionsDataList: [\n        { label: \"在运\", value: \"在运\" },\n        { label: \"停运\", value: \"停运\" },\n        { label: \"报废\", value: \"报废\" }\n      ],\n      //新增设备时所属间隔下拉框列表\n      jgOptionsDataList: [],\n      //新增设备时电压等级下拉框数据\n      dydjOptionsDataList: [\n        { label: \"110kV\", value: \"110\" },\n        { label: \"35kV\", value: \"35\" },\n        { label: \"10kV\", value: \"10\" },\n        { label: \"6kV\", value: \"6\" }\n      ],\n      //新增设备时变电站下拉框\n      bdzOptionsDataList: [],\n      //变电设备列表数据\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"deptname\", label: \"所属公司\", minWidth: \"120\" },\n          { prop: \"bdzmc\", label: \"变电站名称\", minWidth: \"180\" },\n          { prop: \"wzmc\", label: \"所属间隔\", minWidth: \"180\" },\n          { prop: \"sblxmc\", label: \"设备类型\", minWidth: \"180\" },\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"180\" },\n          { prop: \"dydjName\", label: \"电压等级\", minWidth: \"120\" },\n          { prop: \"sbzt\", label: \"设备状态\", minWidth: \"250\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"120\" },\n          { prop: \"ggxh\", label: \"规格型号\", minWidth: \"120\" },\n          { prop: \"eddy\", label: \"额定电压\", minWidth: \"120\" },\n          { prop: \"eddl\", label: \"额定电流\", minWidth: \"120\" },\n          { prop: \"edpl\", label: \"额定频率\", minWidth: \"120\" },\n          { prop: \"sccj\", label: \"生产厂家\", minWidth: \"120\" }\n          /* {\n              fixed: \"right\",\n              prop: 'operation',\n              label: '操作',\n              minWidth: '120px',\n              style: {display: 'block'},\n              operation: [\n                /!*{name: \"状态变更\", clickFun: this.updateStatus},\n                {name: \"流程查看\", clickFun: this.ztbglcSay},*!/\n                {name: '修改', clickFun: this.updateAsset},\n                {name: '详情', clickFun: this.assetDetails},\n              ]\n            },*/\n        ]\n      },\n      resumPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"foreignNum\", label: \"设备名称\", minWidth: \"120\" },\n          { prop: \"sblx\", label: \"设备类型\", minWidth: \"180\" },\n          { prop: \"bglx\", label: \"变更类型\", minWidth: \"120\" },\n          { prop: \"ms\", label: \"描述\", minWidth: \"250\" },\n          { prop: \"bgr\", label: \"变更人\", minWidth: \"140\" },\n          { prop: \"bgsj\", label: \"变更时间\", minWidth: \"140\" }\n        ]\n      },\n      //变电设备筛选条件\n      filterInfo1: {\n        data: {\n          sbmc: \"\",\n          assetTypeCode: \"\",\n          sbzt: \"\"\n        },\n        fieldList: [\n          { label: \"设备名称\", type: \"input\", value: \"sbmc\" },\n          {\n            label: \"设备类型\",\n            type: \"select\",\n            value: \"assetTypeCode\",\n            options: [\n              { label: \"干式站用变\", value: \"02\" },\n              { label: \"sf6断路器\", value: \"03\" },\n              { label: \"真空断路器\", value: \"04\" }\n            ]\n          },\n          {\n            label: \"投运状态\",\n            type: \"select\",\n            value: \"sbzt\",\n            options: [\n              { label: \"在运\", value: \"在运\" },\n              { label: \"停运\", value: \"停运\" }\n            ]\n          }\n        ]\n      },\n      //树结构监听属性\n      filterText: \"\",\n      //组织结构下拉数据\n      OrganizationSelectedList: [],\n      //树结构上面得筛选框参数\n      treeForm: {},\n      //电压等级下拉框数据\n      VoltageLevelSelectedList: [\n        { label: \"110kV\", value: \"110\" },\n        { label: \"35kV\", value: \"35\" },\n        { label: \"10kV\", value: \"10\" },\n        { label: \"6kV\", value: \"6\" }\n      ],\n      //设备信息展示\n      assetIsDisable: false,\n      //技术参数动态展示集合\n      jscsLabelList: [],\n      //技术参数绑定\n      jscsForm: {},\n      //设备上面展示的按钮(新增、删除)\n      isShow3: true,\n      //设备履历状态变更记录\n      sbllztbgjlList: [\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          bglx: \"状态变更\",\n          desc: \"状态变更为报废\",\n          bgsj: \"2022-12-12\",\n          bgr: \"张三\"\n        },\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          bglx: \"状态变更\",\n          desc: \"状态变更为报废\",\n          bgsj: \"2022-12-12\",\n          bgr: \"张三\"\n        },\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          bglx: \"状态变更\",\n          desc: \"状态变更为报废\",\n          bgsj: \"2022-12-12\",\n          bgr: \"张三\"\n        },\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          bglx: \"状态变更\",\n          desc: \"状态变更为报废\",\n          bgsj: \"2022-12-12\",\n          bgr: \"张三\"\n        },\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          bglx: \"状态变更\",\n          desc: \"状态变更为报废\",\n          bgsj: \"2022-12-12\",\n          bgr: \"张三\"\n        }\n      ],\n      //设备履历缺陷记录数据集合\n      sbllqxjlList: [\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          qxxz: \"严重\",\n          dydj: \"35kV\",\n          sbxh: \"XXX型号\",\n          sccj: \"XXX厂家\"\n        },\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          qxxz: \"严重\",\n          dydj: \"35kV\",\n          sbxh: \"XXX型号\",\n          sccj: \"XXX厂家\"\n        },\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          qxxz: \"严重\",\n          dydj: \"35kV\",\n          sbxh: \"XXX型号\",\n          sccj: \"XXX厂家\"\n        },\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          qxxz: \"严重\",\n          dydj: \"35kV\",\n          sbxh: \"XXX型号\",\n          sccj: \"XXX厂家\"\n        },\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          qxxz: \"严重\",\n          dydj: \"35kV\",\n          sbxh: \"XXX型号\",\n          sccj: \"XXX厂家\"\n        }\n      ],\n      //设备履历试验记录数据\n      sblvsyjlList: [\n        {\n          syzy: \"带电\",\n          syxz: \"例行试验\",\n          symc: \"XXXXX\",\n          gzdd: \"XXX平台\",\n          sysb: \"主变压器\",\n          sybg: \"\",\n          tq: \"晴\",\n          syrq: \"2022-01-01\",\n          lrr: \"张三\",\n          syjl: \"XXXXX\"\n        },\n        {\n          syzy: \"带电\",\n          syxz: \"例行试验\",\n          symc: \"XXXXX\",\n          gzdd: \"XXX平台\",\n          sysb: \"主变压器\",\n          sybg: \"\",\n          tq: \"晴\",\n          syrq: \"2022-01-01\",\n          lrr: \"张三\",\n          syjl: \"XXXXX\"\n        },\n        {\n          syzy: \"带电\",\n          syxz: \"例行试验\",\n          symc: \"XXXXX\",\n          gzdd: \"XXX平台\",\n          sysb: \"主变压器\",\n          sybg: \"\",\n          tq: \"晴\",\n          syrq: \"2022-01-01\",\n          lrr: \"张三\",\n          syjl: \"XXXXX\"\n        },\n        {\n          syzy: \"带电\",\n          syxz: \"例行试验\",\n          symc: \"XXXXX\",\n          gzdd: \"XXX平台\",\n          sysb: \"主变压器\",\n          sybg: \"\",\n          tq: \"晴\",\n          syrq: \"2022-01-01\",\n          lrr: \"张三\",\n          syjl: \"XXXXX\"\n        },\n        {\n          syzy: \"带电\",\n          syxz: \"例行试验\",\n          symc: \"XXXXX\",\n          gzdd: \"XXX平台\",\n          sysb: \"主变压器\",\n          sybg: \"\",\n          tq: \"晴\",\n          syrq: \"2022-01-01\",\n          lrr: \"张三\",\n          syjl: \"XXXXX\"\n        },\n        {\n          syzy: \"带电\",\n          syxz: \"例行试验\",\n          symc: \"XXXXX\",\n          gzdd: \"XXX平台\",\n          sysb: \"主变压器\",\n          sybg: \"\",\n          tq: \"晴\",\n          syrq: \"2022-01-01\",\n          lrr: \"张三\",\n          syjl: \"XXXXX\"\n        }\n      ],\n      //设备履历tab页\n      sbllDescTabName: \"syjl\",\n      //轮播图片\n      imgList: [\n        {\n          url:\n            \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\"\n        },\n        {\n          url:\n            \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\"\n        },\n        {\n          url:\n            \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\"\n        },\n        {\n          url:\n            \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\"\n        }\n      ],\n      //组织树\n      treeOptions: [],\n      //设备详情页底部确认取消按钮控制\n      sbCommitDialogCotrol: true,\n      //弹出框tab页\n      activeTabName: \"sbDesc\",\n      //变电站展示\n      bdzShowTable: true,\n      //间隔展示\n      jgShowTable: false,\n      //设备展示\n      sbShowTable: false,\n      //设备弹出框\n      dialogFormVisible: false,\n      //变电站添加按钮弹出框\n      bdzDialogFormVisible: false,\n      //间隔添加按钮弹出框\n      jgDialogFormVisible: false,\n      //弹出框表单\n      form: {},\n      loading: false,\n      paramQuery: {\n        sblxbm: undefined\n      },\n      rules: {\n        ssbdz: [\n          { required: true, message: \"请选择所属变电站\", trigger: \"change\" }\n        ],\n        sbdm: [{ required: true, message: \"请填写设备代码\", trigger: \"blur\" }],\n        sbmc: [{ required: true, message: \"请填写设备名称\", trigger: \"blur\" }],\n        dydjbm: [\n          { required: true, message: \"请选择电压等级\", trigger: \"change\" }\n        ],\n        sbzt: [\n          { required: true, message: \"请选择设备状态\", trigger: \"change\" }\n        ],\n        assetTypeCode: [\n          { required: true, message: \"请选择设备类型\", trigger: \"change\" }\n        ],\n        ssjg: [\n          { required: true, message: \"请选择所属间隔\", trigger: \"change\" }\n        ],\n        ssgs: [{ required: true, message: \"请选择所属公司\", trigger: \"change\" }]\n      },\n\n      //审批按钮\n      spButtonDisabled: true\n    };\n  },\n  watch: {\n    //监听筛选框值发生变化进而筛选树结构\n    filterText(val) {\n      this.$refs.tree.filter(val);\n    }\n  },\n  created() {\n    //获取组织结构下拉数据\n    this.getOrganizationSelected();\n    //获取新的设备拓扑树\n    this.getNewTreeInfo();\n    //初始化加载时加载所有变电站下面的设备信息\n    this.getData();\n    //获取设备类型下拉框数据\n    this.getSblxDataListSelected();\n  },\n  mounted() {\n    //获取变电站下拉框数据\n    this.getBdzDataListSelected();\n  },\n  methods: {\n    //审批\n    spZtbgjl() {\n      let row = this.selectRows[0];\n      this.activitiOption.title = \"流程提交\";\n      this.processData.defaultFrom = false;\n      this.processData.processType = \"complete\";\n      this.processData.businessKey = row.objId;\n      this.processData.variables.pass = true;\n      this.isShow = true;\n    },\n    //流程查看\n    async ztbglcSay(row) {\n      this.processData.businessKey = row.objId;\n      let { code, data } = await HistoryList(this.processData);\n      this.timeData = data;\n      this.timeLineShow = true;\n    },\n    //流程回调\n    async todoResult(data) {\n      console.log(\"流程回调方法：\", data);\n      switch (data.activeTaskName) {\n        case \"结束\":\n          updateStatus(data.variables.updateList).then(res => {\n            if (res.code == \"0000\") {\n              this.$message.success(\"设备状态已变更！\");\n              this.dialogVisible = false;\n              this.getData();\n            }\n          });\n          break;\n      }\n    },\n    //上报发送办结\n    getSbFsBj(args) {\n      console.log(args);\n      let row = { ...args.data };\n      if (args.type === \"complete\") {\n        this.activitiOption.title = \"人员选择\";\n        this.processData.defaultFrom = true;\n        this.processData.processType = \"complete\";\n        this.processData.businessKey = row.objId;\n        this.processData.variables.pass = true;\n        this.processData.variables.updateList = row;\n      } else {\n        this.activitiOption.title = \"回退原因提报\";\n        this.processData.defaultFrom = true;\n        this.processData.processType = \"rollback\";\n        this.processData.businessKey = row.objId;\n        this.processData.variables.pass = true;\n      }\n      this.isShow = true;\n    },\n    //关闭流程组件\n    closeActiviti() {\n      this.isShow = false;\n    },\n    //关闭流程查看页面\n    colseTimeLine() {\n      this.timeLineShow = false;\n    },\n    /**\n     * 设备表格多选框\n     */\n    handleSelectionChange(rows) {\n      this.selectRows = rows;\n\n      //审批按钮\n      this.spButtonDisabled = rows.length != 1;\n    },\n    /**\n     * 获取设备类型下拉框数据\n     */\n    getSblxDataListSelected() {\n      let sblxParam = {\n        type: \"变电设备\"\n      };\n      getSblxDataListSelected(sblxParam).then(res => {\n        this.sblxOptionsDataSelected = res.data;\n      });\n    },\n\n    /**\n     * 删除设备信息\n     */\n    removeAsset() {\n      if (this.selectRows.length < 1) {\n        this.$message.warning(\"请选择正确的数据！！！\");\n        return;\n      }\n      let ids = this.selectRows.map(item => {\n        return item.objId;\n      });\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          removeAsset(ids).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n      this.tableAndPageInfo.pager.pageResize = \"Y\";\n      this.getData();\n    },\n    //设备添加按钮\n    sbAddSensorButton() {\n      //清空表单\n      // this.sbxxForm = {};\n      // this.jscsForm={};\n      //\n      // let row={};\n      // this.technicalParameters(row);\n      //打开弹出框\n      this.dialogFormVisible = true;\n      //按钮和表单是否可编辑控制\n      this.assetIsDisable = false;\n    },\n    //变电站下拉框中的change事件\n    bdzOptionsChangeClick() {\n      //当发生change事件时先清空之前的间隔信息\n      this.$set(this.sbxxForm, \"ssjg\", \"\");\n      //调用查询间隔方法\n      this.getJgDataListSelected();\n    },\n    //获取变电站下拉框数据\n    getBdzDataListSelected() {\n      getBdzDataListSelected(this.selectBdzOptionsParam).then(res => {\n        this.bdzOptionsDataList = res.data;\n      });\n      //调用查询间隔方法\n      // this.getJgDataListSelected();\n    },\n    //获取间隔下拉框数据\n    getJgDataListSelected() {\n      //给获取间隔下拉框查询参数赋值\n      this.selectJgOptionsParam.ssbdz = this.sbxxForm.ssbdz;\n      getJgDataListSelected(this.selectJgOptionsParam).then(res => {\n        this.jgOptionsDataList = res.data;\n      });\n    },\n    updateStatus(row) {\n      this.updateList.sbzt = row.sbzt;\n      (this.updateList.objId = row.objId), (this.dialogVisible = true);\n    },\n    submitStatus() {\n      this.$confirm(\"确认将设备状态修改为\" + this.updateList.sbzt + \"?\", \"\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(res => {\n        //关闭状态变更弹窗\n        this.dialogVisible = false;\n        //打开人员选择弹窗\n        this.isShow = true;\n        this.getSbFsBj({ data: this.updateList, type: \"complete\" });\n        // updateStatus(this.updateList).then(res=>{\n        //   if(res.code==\"0000\"){\n        //     this.$message.success(\"设备状态已变更！\")\n        //     this.dialogVisible=false;\n        //     this.getData();\n        //   }\n        // })\n      });\n    },\n\n    /**\n     * 设备履历\n     */\n    getResumList(par) {\n      let param = { ...par, ...this.resumeQuery };\n      getResumDataList(param).then(res => {\n        this.resumPageInfo.tableData = res.data.records;\n        this.resumPageInfo.pager.total = res.data.total;\n      });\n    },\n\n    //设备修改操作\n    updateAsset(row) {\n      this.technicalParameters(row);\n      this.paramQuery.sblxbm = row.assetTypeCode;\n      this.getParameters();\n      this.resumeQuery.foreignNum = row.sbmc;\n      this.resumeQuery.sblx = row.sblx;\n      this.getResumList();\n      //打开设备弹出框\n      this.dialogFormVisible = true;\n      //给表单赋值\n      this.sbxxForm = row;\n      //按钮和表单是否可编辑控制\n      this.assetIsDisable = false;\n    },\n    //设备详情操作\n    assetDetails(row) {\n      this.technicalParameters(row);\n\n      this.resumeQuery.foreignNum = row.sbmc;\n      this.resumeQuery.sblx = row.sblx;\n      this.getResumList();\n      //打开设备弹出框\n      this.dialogFormVisible = true;\n      //给表单赋值\n      this.sbxxForm = row;\n      //按钮和表单是否可编辑控制\n      this.assetIsDisable = true;\n    },\n    //设备履历tab页点击事件\n    handleSbllDescTabNameClick(tab, event) {\n      console.log(tab, event);\n    },\n    //筛选条件重置\n    filterReset() {},\n    //变电设备台账查询\n    async getData(params) {\n      try {\n        const param = { ...this.bdzqueryParams, ...params };\n        const { data, code } = await getBdAsesetListPage(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          console.log(\"data:设备数据\", data.records);\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //树监听事件\n    filterNode(value, data) {\n      if (!value) return true;\n      return data.label.indexOf(value) !== -1;\n    },\n    //获取新的设备拓扑树\n    getNewTreeInfo() {\n      //给查询设备参数赋值\n      this.bdzqueryParams.ssgs = this.treeForm.ssdwbm;\n      this.bdzqueryParams.dydjbm = this.treeForm.dydjbm;\n      getNewTreeInfo(this.treeForm).then(res => {\n        console.log(\"s树结构数据:\", res);\n        this.treeOptions = res.data;\n      });\n      //获取设备方法\n      this.getData();\n    },\n    //获取组织结构下拉框数据\n    getOrganizationSelected() {\n      let parentId = \"1001\";\n      getOrganizationSelected({ parentId: parentId }).then(res => {\n        this.OrganizationSelectedList = res.data;\n        console.log(this.OrganizationSelectedList);\n      });\n    },\n    //保存设备信息\n    submit() {\n      this.$refs[\"sbxxForm\"].validate(valid => {\n        if (valid) {\n          this.addAsset();\n          //保存技术参数信息\n          // this.submitParameter();\n        } else {\n          return false;\n        }\n      });\n    },\n    /**\n     * 添加设备保存基本信息\n     */\n    addAsset() {\n      this.sbxxForm.sbClassCsValue = this.jscsForm;\n      addAsset(this.sbxxForm).then(res => {\n        if (res.code == \"0000\") {\n          this.$message({\n            type: \"success\",\n            message: \"操作成功!\"\n          });\n          this.dialogFormVisible = false;\n          this.tableAndPageInfo.pager.pageResize = \"Y\";\n          this.getData();\n        }\n      });\n    },\n    //保存技术参数\n    submitParameter() {\n      saveParamValue(this.jscsForm).then(res => {\n        this.dialogFormVisible = false;\n      });\n    },\n    //设备类型change事件。获取技术参数信息\n    showParams(data) {\n      this.paramQuery.sblxbm = data;\n      this.getParameters();\n    },\n    //获取技术参数对照信息，展示对应得技术参数label信息\n    async getParameters() {\n      this.jscsLabelList = [];\n      getParamDataList(this.paramQuery).then(res => {\n        console.log(\"技术参数对照信息\", res);\n        this.jscsLabelList = res.data;\n        this.getParamValue();\n      });\n    },\n    //获取技术参数值信息\n    getParamValue() {\n      getParamsValue(this.jscsForm).then(res => {\n        if (res.data != \"\") {\n          this.jscsForm = { ...res.data[0] };\n          console.log(\"技术参数值信息\", this.jscsForm);\n        }\n      });\n    },\n    //点击新增，修改，详情时，重新获取对应的技术参数信息\n    technicalParameters(row) {\n      this.jscsForm = {};\n      this.paramQuery.sblxbm = row.assetTypeCode;\n      this.jscsForm.sblxbm = row.assetTypeCode;\n      this.jscsForm.sbbm = row.objId;\n      this.getParameters();\n    },\n\n    //树点击事件\n    handleNodeClick(data, e) {\n      if (data.identifier == \"0\") {\n        //点击父节点时查询当前所有设备\n        //清空查询参数\n        this.bdzqueryParams = {};\n        //获取变电站数据\n        this.getData();\n      } else if (data.identifier == \"1\") {\n        //点击第二层级的展示设备\n        this.bdzqueryParams.ssjg = \"\";\n        this.bdzqueryParams.ssbdz = data.id;\n        //获取变电站数据\n        this.getData();\n      } else if (data.identifier == \"2\") {\n        //点击第二层级的展示设备\n        this.bdzqueryParams.ssbdz = \"\";\n        this.bdzqueryParams.ssjg = data.id;\n        //获取变电站数据\n        this.getData();\n      }\n    },\n\n    //清空表单\n    resetForm() {\n      this.sbxxForm = this.$options.data().form;\n      this.jscsForm = this.$options.data().form;\n      this.$nextTick(function() {\n        this.$refs[\"sbxxForm\"].clearValidate();\n      });\n      this.dialogFormVisible = false;\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  height: 76vh;\n  overflow: auto;\n}\n\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.box-cardList {\n  height: 70%;\n}\n\n.item {\n  width: 200px;\n  float: left;\n}\n\n#main_container_dj {\n  height: calc(100vh - 84px);\n}\n\n.aside_height {\n  height: 96%;\n}\n\n.defect .el-form-item:nth-child(odd) {\n  margin-right: 70px;\n}\n\n/*背景颜色调整*/\n#main_container_dj,\n#main_container_dj .el-aside {\n  background-color: #b4caf1;\n}\n\n///deep/ .qxlr_dialog_insert .el-dialog__header {\n//  background-color: #8eb3f5;\n//}\n\n/* /deep/ .pmyBtn {\n    background: #8eb3f5;\n  }*/\n\n/*/deep/ .add_sy_tyrq .el-input--medium .el-input__inner {*/\n/*  width: 200px;*/\n/*}*/\n\n/*添加弹出框得宽度*/\n/*/deep/ .el-input--medium .el-input__inner {*/\n/*  width: 200px;*/\n/*}*/\n.el-select {\n  width: 100%;\n}\n\n.el-date-editor {\n  width: 100%;\n}\n\n.el-carousel__item h3 {\n  color: #475669;\n  font-size: 14px;\n  opacity: 0.75;\n  line-height: 150px;\n}\n\n.el-carousel__item:nth-child(2n) {\n  background-color: #99a9bf;\n}\n\n.el-carousel__item:nth-child(2n + 1) {\n  background-color: #d3dce6;\n}\n\n/* 设置滚动条的样式 */\n::-webkit-scrollbar {\n  width: 12px;\n}\n\n/* 滚动槽 */\n::-webkit-scrollbar-track {\n  //-webkit-box-shadow:inset006pxrgba(0,0,0,0.3);\n  border-radius: 10px;\n}\n\n/* 滚动条滑块 */\n::-webkit-scrollbar-thumb {\n  border-radius: 10px;\n  background: rgba(0, 0, 0, 0.1);\n  //-webkit-box-shadow:gba(0,0,0,0.5);\n}\n\n::-webkit-scrollbar-thumb:window-inactive {\n  background: rgba(0, 0, 0, 0.1);\n}\n\n[data-v-67a974b1]::-webkit-scrollbar {\n  width: 8px;\n}\n\n.item {\n  width: 225px;\n  float: left;\n}\n/deep/.box-card {\n  margin: 0 6px;\n}\n//有子节点 且未展开\n.el-tree ::v-deep .el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/add.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n//有子节点 且已展开\n.el-tree\n  ::v-deep\n  .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/prep.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n//没有子节点\n.el-tree ::v-deep .el-tree-node__expand-icon.is-leaf::before {\n  background: transparent;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n</style>\n"]}]}