{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\blgk\\index.vue?vue&type=style&index=0&id=38738f5e&scoped=true&lang=scss&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\blgk\\index.vue", "mtime": 1750579918247}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmNhcmQxIHsKICBtYXJnaW4tYm90dG9tOiA2cHg7Cn0KCi5zZWFyY2gtY29uZGl0aW9uIHsKICBmb250LXNpemU6IDEzcHg7CiAgY29sb3I6ICM5YzljOWM7CgogIC5lbC1zZWxlY3QgewogICAgLmVsLWlucHV0IHsKICAgICAgd2lkdGg6IDEwMCU7CiAgICB9CiAgfQoKICAuZWwtY29sIHsKICAgIHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7CiAgICBsaW5lLWhlaWdodDogMzJweDsKICAgIHRleHQtYWxpZ246IGxlZnQ7CiAgfQp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgwBA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/blgk", "sourcesContent": ["<template>\n  <el-row class=\"app-container\">\n    <el-row>\n      <!--基本信息查询及显示-->\n      <el-col :span=\"24\">\n        <!--搜索条件-->\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          @handleReset=\"filterReset\"\n          :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n        />\n      </el-col>\n    </el-row>\n\n    <el-row>\n      <el-col>\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button\n              type=\"primary\"\n              icon=\"el-icon-plus\"\n              v-hasPermi=\"['blgk:button:add']\"\n              @click=\"addRow\"\n              >新增</el-button\n            >\n          </div>\n          <comp-table :table-and-page-info=\"tableAndPageInfo\" height=\"65vh\">\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"200\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  icon=\"el-icon-edit\"\n                  title=\"编辑\"\n                  @click=\"handleUpdate(scope.row)\"\n                  v-if=\"\n                    (scope.row.createBy === $store.getters.name ) ||\n                      hasSuperRole\n                  \"\n                />\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"getDetails(scope.row)\"\n                  icon=\"el-icon-view\"\n                  title=\"详情\"\n                />\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  icon=\"el-icon-remove\"\n                  title=\"消除\"\n                  @click=\"handleClean(scope.row)\"\n                  v-if=\"\n                    scope.row.createBy === $store.getters.name &&\n                      scope.row.isClean != '是'\n                  \"\n                />\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  icon=\"el-icon-delete\"\n                  title=\"删除\"\n                  @click=\"handleDeleteById(scope.row.objId)\"\n                  v-if=\"\n                    scope.row.createBy === $store.getters.name || hasSuperRole  \n                  \"\n                />\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!-- 新增、详情弹出对话框 -->\n    <el-dialog\n      id=\"blgk_div\"\n      :title=\"title\"\n      :visible.sync=\"isShow\"\n      width=\"60%\"\n      @close=\"closeFun\"\n      v-dialogDrag\n    >\n      <div class=\"button_btn\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          v-if=\"!isDisabled\"\n          @click=\"chooseSbFun\"\n          >选择设备</el-button\n        >\n      </div>\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item prop=\"fgsmc\" label=\"分公司：\">\n              <el-input\n                v-model=\"form.fgsmc\"\n                :disabled=\"true\"\n                style=\"width:80%\"\n                placeholder=\"请选择分公司\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"变电站：\" prop=\"bdzmc\">\n              <el-input\n                v-model=\"form.bdzmc\"\n                :disabled=\"true\"\n                style=\"width:80%\"\n                placeholder=\"请选择变电站\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item prop=\"jgmc\" label=\"间隔：\">\n              <el-input\n                v-model=\"form.jgmc\"\n                :disabled=\"true\"\n                style=\"width:80%\"\n                placeholder=\"请选择间隔\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"设备类型：\" prop=\"sblxmc\">\n              <el-input\n                v-model=\"form.sblxmc\"\n                :disabled=\"true\"\n                style=\"width:80%\"\n                placeholder=\"请选择设备类型\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"设备：\" prop=\"sbmc\">\n              <el-input\n                v-model=\"form.sbmc\"\n                :disabled=\"true\"\n                style=\"width:80%\"\n                placeholder=\"请选择设备\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"不良工况类型:\" prop=\"type\">\n              <el-select\n                @change=\"lxChange\"\n                placeholder=\"请选择不良工况类型\"\n                v-model=\"form.type\"\n                style=\"width:80%\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option\n                  v-for=\"item in lxList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"不良工况描述：\" prop=\"ms\">\n              <el-select\n                @change=\"msChange\"\n                placeholder=\"请选择描述\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.ms\"\n                style=\"width: 91%\"\n                filterable\n                :allow-create=\"this.form.type === '其他'\"\n              >\n                <el-option\n                  v-for=\"item in msList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\" v-if=\"this.form.type !== '其他'\">\n            <el-form-item label=\"分类依据：\" prop=\"flyj\">\n              <el-select\n                :disabled=\"isDisabled\"\n                style=\"width: 91%\"\n                v-model=\"form.flyj\"\n                placeholder=\"请选择分类依据\"\n              >\n                <el-option\n                  v-for=\"item in flyjList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"录入人：\" prop=\"lrrmc\">\n              <el-input\n                v-model=\"form.lrrmc\"\n                :disabled=\"true\"\n                style=\"width:80%\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"发现时间：\" prop=\"fxsj\">\n              <el-date-picker\n                style=\"width:80%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.fxsj\"\n                type=\"date\"\n                format=\"yyyy-MM-dd\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"请选择时间\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"消除时间：\" prop=\"xcsj\">\n              <el-date-picker\n                style=\"width:80%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.xcsj\"\n                type=\"date\"\n                format=\"yyyy-MM-dd\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"请选择时间\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注：\" prop=\"bz\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"5\"\n                style=\"width: 92%\"\n                v-model=\"form.bz\"\n                :disabled=\"isDisabled\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun\">取 消</el-button>\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"saveFun\"\n          >保 存</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <!--新主设备选择框组件-->\n    <el-dialog\n      title=\"设备选择\"\n      :visible.sync=\"isShowSbChoose\"\n      width=\"86%\"\n      v-if=\"isShowSbChoose\"\n      v-dialogDrag\n    >\n      <sb-choose\n        @closeChooseFun=\"closeChooseFun\"\n        @getSbDataFun=\"getSbDataFun\"\n      />\n    </el-dialog>\n  </el-row>\n</template>\n\n<script>\nimport {\n  removeBlgk,\n  saveOrUpdate,\n  getData,\n  getAllSblxList,\n  getBlgkSblxByJg,\n  getSb,\n  clean\n} from \"@/api/blgk/blgk\";\nimport { getLx, getMs, getFlyj } from \"@/api/blgk/blgkbzk\";\nimport { getDeptListById } from \"@/api/system/dept\";\nimport { getBdzSelectList } from \"@/api/yxgl/bdyxgl/bdxjzqpz\";\nimport { getJgDataListSelected } from \"@/api/dagangOilfield/asset/bdsbtz\";\nimport { formatterDateTime } from \"@/utils/handleData\";\nimport { Loading } from \"element-ui\";\nimport { getDictTypeData } from \"@/api/system/dict/data\";\nimport sbChoose from \"@/views/dagangOilfield/blgk/chooseBdsb\";\n\nexport default {\n  name: \"blgk\",\n  components: { sbChoose },\n  data() {\n    return {\n      hasSuperRole: this.$store.getters.hasSuperRole,\n      isShow: false, //弹框是否显示\n      loading: false,\n      //是否禁用\n      isDisabled: false,\n      form: {},\n      //查询参数\n      queryParams: {\n        pageSize: 10,\n        pageNum: 1\n      },\n      //详情对话框标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          // fgsmc: \"\",\n          bdzmc: \"\",\n          jgmc: \"\",\n          sbmc: \"\",\n          sblx: \"\",\n          isClean: \"\"\n        },\n        fieldList: [\n          // { label: \"分公司\", type: \"select\", value: \"fgsmc\", options: [] },\n          { label: \"变电站\", type: \"input\", value: \"bdzmc\" },\n          { label: \"间隔名称\", type: \"input\", value: \"jgmc\" },\n          { label: \"设备名称\", type: \"input\", value: \"sbmc\" },\n          {\n            label: \"设备类型\",\n            type: \"select\",\n            value: \"sblx\",\n            options: [],\n            clearable: true,\n            filterable: true\n          },\n          {\n            label: \"是否消除\",\n            type: \"select\",\n            value: \"isClean\",\n            options: [],\n            clearable: true\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: { checkBox: false, serialNumber: true },\n        tableData: [],\n        tableHeader: [\n          { prop: \"fgsmc\", label: \"分公司\", minWidth: \"120\" },\n          { prop: \"bdzmc\", label: \"变电站\", minWidth: \"120\" },\n          { prop: \"jgmc\", label: \"间隔名称\", minWidth: \"100\" },\n          { prop: \"sblxmc\", label: \"设备类型\", minWidth: \"120\" },\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"120\" },\n          { prop: \"ms\", label: \"不良工况描述\", minWidth: \"140\", showPop: true },\n          { prop: \"flyjCn\", label: \"分类依据\", minWidth: \"140\", showPop: true },\n          { prop: \"lrrmc\", label: \"录入人\", minWidth: \"80\" },\n          { prop: \"fxsj\", label: \"发现时间\", minWidth: \"110\" },\n          { prop: \"xcsj\", label: \"消除时间\", minWidth: \"110\" }\n        ]\n      },\n      rules: {\n        fgsmc: [{ required: true, message: \"分公司不能为空\", trigger: \"blur\" }],\n        bdzmc: [{ required: true, message: \"变电站不能为空\", trigger: \"blur\" }],\n        jgmc: [{ required: true, message: \"间隔不能为空\", trigger: \"blur\" }],\n        sblxmc: [\n          { required: true, message: \"设备类型不能为空\", trigger: \"blur\" }\n        ],\n        type: [\n          { required: true, message: \"不良工况类型不能为空\", trigger: \"select\" }\n        ],\n        sbmc: [{ required: true, message: \"设备不能为空\", trigger: \"blur\" }],\n        ms: [\n          { required: true, message: \"不良工况不能为空\", trigger: \"select\" }\n        ],\n        flyj: [\n          { required: true, message: \"分类依据不能为空\", trigger: \"select\" }\n        ]\n      },\n      fgsList: [], //分公司下拉框\n      bdzList: [], //变电站下拉框\n      jgList: [], //间隔下拉框\n      sblxList: [], //间隔下拉框\n      sbList: [], //设备下拉框\n      msList: [], //描述下拉框\n      flyjList: [], //分类依据下拉框\n      lxList: [], //类型下拉框\n      saveLoading: null, //保存时的遮罩层\n      isShowSbChoose: false, //是否显示设备选择弹框\n      sbData: {} //设备数据\n    };\n  },\n  created() {\n    //列表查询\n    this.getData();\n    this.getOptions(); //获取涉及到的下拉框字典值\n  },\n  methods: {\n    //选择设备\n    chooseSbFun() {\n      this.isShowSbChoose = true;\n    },\n    //关闭设备选择弹框\n    closeChooseFun() {\n      this.isShowSbChoose = false;\n    },\n    //获取设备数据\n    async getSbDataFun(sbData) {\n      this.sbData = sbData;\n      this.$set(this.form, \"fgsmc\", sbData.deptname);\n      this.$set(this.form, \"bdzmc\", sbData.bdzmc);\n      this.$set(this.form, \"jgmc\", sbData.wzmc);\n      this.$set(this.form, \"sblxmc\", sbData.sblxmc);\n      this.$set(this.form, \"sblx\", sbData.sblxbm);\n      this.$set(this.form, \"sbmc\", sbData.sbmc);\n      this.$set(this.form, \"sbid\", sbData.objId);\n      this.$set(this.form, \"type\", \"\");\n      this.$set(this.form, \"ms\", \"\");\n      this.$set(this.form, \"flyj\", \"\");\n      //获取不良工况类型下拉框\n      this.lxList = [];\n      await getLx({ sblx: sbData.sblxbm }).then(res => {\n        res.data.forEach(item => {\n          this.lxList.push({ label: item.label, value: item.label });\n        });\n        this.lxList.push({ label: \"其他\", value: \"其他\" });\n      });\n    },\n    //查询下拉框数据\n    async getOptions() {\n      await this.getSfList(); //是/否，字典\n      await this.getAllSblxList();\n    },\n    //获取所有设备类型下拉框用于查询\n    getAllSblxList() {\n      getAllSblxList({ zy: \"bdsb\" }).then(res => {\n        this.filterInfo.fieldList.forEach(item => {\n          if (item.value === \"sblx\") {\n            item.options = res.data;\n            return false;\n          }\n        });\n      });\n    },\n    //获取是/否，字典\n    getSfList() {\n      getDictTypeData(\"sys_sf\").then(res => {\n        let sfList = [];\n        res.data.forEach(item => {\n          sfList.push({ label: item.label, value: item.value });\n        });\n        this.filterInfo.fieldList.forEach(item => {\n          if (item.value === \"isClean\") {\n            item.options = sfList;\n            return false;\n          }\n        });\n      });\n    },\n    //根据分公司获取变电站\n    async getBdzSelectList(val) {\n      this.$set(this.form, \"bdz\", \"\");\n      await getBdzSelectList({ ssdwbm: val }).then(res => {\n        this.bdzList = res.data;\n      });\n    },\n    //根据变电站获取间隔\n    async getJgSelectList(val) {\n      this.$set(this.form, \"jg\", \"\");\n      await getJgDataListSelected({ ssbdz: val }).then(res => {\n        this.jgList = res.data;\n      });\n    },\n    //间隔下拉框change事件\n    async jgChange(val) {\n      this.$set(this.form, \"sblx\", \"\");\n      await getBlgkSblxByJg({ ssjg: val }).then(res => {\n        this.sblxList = res.data;\n      });\n    },\n    //设备类型下拉框change事件\n    async sblxChange(val) {\n      //重置表单及字典\n      this.$set(this.form, \"sbid\", \"\");\n      this.$set(this.form, \"type\", \"\");\n      this.sbList = [];\n      this.msList = [];\n      this.lxList = [];\n      //获取设备下拉框\n      await getSb({ sblx: val, ssjg: this.form.jg }).then(res => {\n        this.sbList = res.data;\n      });\n      //获取不良工况类型下拉框\n      await getLx({ sblx: val }).then(res => {\n        res.data.forEach(item => {\n          this.lxList.push({ label: item.label, value: item.label });\n        });\n      });\n    },\n    async lxChange(val) {\n      //重置表单及字典\n      this.$set(this.form, \"ms\", \"\");\n      this.msList = [];\n      //获取不良工况描述下拉框\n      await getMs({ sblx: this.form.sblx, type: val }).then(res => {\n        this.msList = res.data;\n      });\n    },\n    //不良工况描述下拉框change事件\n    async msChange(val) {\n      this.$set(this.form, \"flyj\", \"\");\n      this.flyjList = [];\n      let msLabel = \"\";\n      this.msList.forEach(item => {\n        if (item.value == val) {\n          msLabel = item.label;\n          return false;\n        }\n      });\n      //获取分类依据下拉框\n      await getFlyj({ sblx: this.form.sblx, ms: msLabel }).then(res => {\n        this.flyjList = res.data;\n      });\n    },\n    //保存表单\n    async saveFun() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          //设置虚拟字段用于保存设备异常及事故处理数据\n          let ms = \"\";\n          let flyjCn = \"\";\n          this.msList.forEach(item => {\n            if (item.value == this.form.ms) {\n              ms = item.label;\n              return false;\n            }\n          });\n          this.flyjList.forEach(item => {\n            if (item.value == this.form.flyj) {\n              flyjCn = item.label;\n              return false;\n            }\n          });\n          if (Object.keys(this.sbData).length !== 0) {\n            this.form.fgs = this.sbData.ssgs;\n            this.form.bdz = this.sbData.ssbdz;\n          }\n          if (ms) {\n            this.form.ms = ms;\n          }\n          this.form.flyjCn = flyjCn;\n          if (this.form.xcsj) {\n            this.form.isClean = \"是\";\n          } else {\n            this.form.isClean = \"否\"; //设置是否消除为否\n          }\n          this.$nextTick(() => {\n            this.saveLoading = Loading.service({\n              lock: true, //lock的修改符--默认是false\n              text: \"保存中，请稍后\", //显示在加载图标下方的加载文案\n              spinner: \"el-icon-loading\", //自定义加载图标类名\n              background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n              target: document.querySelector(\"#blgk_div\")\n            });\n            saveOrUpdate(this.form).then(res => {\n              if (res.code === \"0000\") {\n                this.$message.success(\"操作成功!\");\n                this.getData();\n                this.saveLoading.close();\n                this.isShow = false;\n              }\n            });\n          });\n        } else {\n          this.$message({ type: \"error\", message: \"校验未通过\" });\n        }\n      });\n    },\n    filterReset() {\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n    },\n    //分页查询列表\n    getData(params) {\n      //参数合并\n      this.loading = true;\n      const param = { ...this.queryParams, ...params };\n      this.queryParams = param;\n      getData(param).then(res => {\n        this.tableAndPageInfo.tableData = res.data.records;\n        this.tableAndPageInfo.pager.total = res.data.total;\n        this.loading = false;\n      });\n    },\n    /**\n     * 新增不良工况\n     */\n    addRow() {\n      this.title = \"新增不良工况\";\n      this.isDisabled = false;\n      this.form = {};\n      this.form.lrr = this.$store.getters.name;\n      this.form.lrrmc = this.$store.getters.nickName;\n      this.$set(this.form, \"fxsj\", formatterDateTime(new Date(), \"yyyy-MM-dd\"));\n      this.isShow = true;\n    },\n    /**\n     * 编辑\n     */\n    async handleUpdate(row) {\n      this.saveLoading = Loading.service({\n        lock: true, //lock的修改符--默认是false\n        text: \"加载中，请稍后\", //显示在加载图标下方的加载文案\n        spinner: \"el-icon-loading\", //自定义加载图标类名\n        background: \"rgba(0, 0, 0, 0.7)\" //遮罩层颜色\n      });\n      this.title = \"编辑不良工况\";\n      this.isDisabled = false;\n      this.form = { ...row };\n      this.form.fgs = row.fgs;\n      this.form.bdz = row.bdz;\n      //处理下拉框回显\n      this.lxList = [];\n      this.msList = [];\n      this.flyjList = [];\n      //获取不良工况类型下拉框\n      await getLx({ sblx: row.sblx }).then(res => {\n        res.data.forEach(item => {\n          this.lxList.push({ label: item.label, value: item.label });\n        });\n        this.lxList.push({ label: \"其他\", value: \"其他\" });\n      });\n      //获取不良工况描述下拉框\n      await getMs({ sblx: this.form.sblx, type: row.type }).then(res => {\n        this.msList = res.data;\n      });\n      if (this.msList.length) {\n        this.$set(this.form, \"ms\", row.flyj);\n      } else {\n        this.$set(this.form, \"ms\", row.ms);\n      }\n      //获取分类依据下拉框\n      await getFlyj({ sblx: this.form.sblx, ms: row.ms }).then(res => {\n        this.flyjList = res.data;\n      });\n      //关闭遮罩层\n      this.saveLoading.close();\n      this.isShow = true;\n    },\n    /**\n     * 详情查看\n     */\n    getDetails(row) {\n      this.form = { ...row };\n      //处理回显问题\n      this.form.flyj = row.flyjCn;\n      this.isDisabled = true;\n      this.isShow = true;\n      this.title = \"不良工况详情\";\n    },\n    /**\n     * 删除\n     */\n    async handleDeleteById(id) {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          removeBlgk([id]).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    /**\n     * 消除\n     */\n    async handleClean(row) {\n      this.$confirm(\"是否确定消除不良工况?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          let form1 = { ...row };\n          clean(form1).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"消除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"消除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消消除\"\n          });\n        });\n    },\n    //弹框关闭事件\n    closeFun() {\n      this.form = {};\n      this.isShow = false;\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.card1 {\n  margin-bottom: 6px;\n}\n\n.search-condition {\n  font-size: 13px;\n  color: #9c9c9c;\n\n  .el-select {\n    .el-input {\n      width: 100%;\n    }\n  }\n\n  .el-col {\n    vertical-align: middle;\n    line-height: 32px;\n    text-align: left;\n  }\n}\n</style>\n"]}]}