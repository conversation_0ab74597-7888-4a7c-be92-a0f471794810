{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\technicalAndPart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\technicalAndPart.vue", "mtime": 1706897322897}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBUZWNobmljYWxQYXJhbWV0ZXIgZnJvbSAnQC92aWV3cy9kYWdhbmdPaWxmaWVsZC9iemdsL3NiYnprL3RlY2huaWNhbFBhcmFtZXRlcicKaW1wb3J0IEVxdWlwbWVudENvbXBvbmVudHMgZnJvbSAnQC92aWV3cy9kYWdhbmdPaWxmaWVsZC9iemdsL3NiYnprL2VxdWlwbWVudENvbXBvbmVudHMnCgpleHBvcnQgZGVmYXVsdCB7CiAgcHJvcHM6IHsKICAgIGRldmljZVR5cGVEYXRhOiB7CiAgICAgIHR5cGU6IE9iamVjdAogICAgfQogIH0sCiAgbmFtZTogJ3RlY2huaWNhbEFuZFBhcnQnLAogIGNvbXBvbmVudHM6IHsgRXF1aXBtZW50Q29tcG9uZW50cywgVGVjaG5pY2FsUGFyYW1ldGVyIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGFjdGl2ZU5hbWU6ICdmaXJzdCcsCiAgICAgIHRhYnNJbmRleDogMQogICAgfQogIH0sCiAgbW91bnRlZCgpIHsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8vdGFi5Lmf5YiH5o2i54K55Ye75LqL5Lu2CiAgICBoYW5kbGVDbGljayh0YWIpIHsKICAgICAgdGhpcy50YWJzSW5kZXggPSB0YWIubmFtZSA9PT0gJ2ZpcnN0JyA/IDEgOiAyCiAgICB9LAogICAgLy/lhbPpl63lvLnnqpcKICAgIGNsb3NlRGlhbG9nKCkgewogICAgICB0aGlzLiRlbWl0KCdjbG9zZVBhcmFtRGlhbG9nJykKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["technicalAndPart.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAiBA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "technicalAndPart.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbbzk", "sourcesContent": ["<template>\n  <div>\n    <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\n      <el-tab-pane label=\"技术参数\" name=\"first\">\n        <technical-parameter ref=\"technicalParameter\" v-if=\"tabsIndex === 1 \" :device-type-data=\"deviceTypeData\"/>\n      </el-tab-pane>\n      <el-tab-pane label=\"设备部件\" name=\"second\">\n        <equipment-components ref=\"equipmentComponents\" v-if=\"tabsIndex === 2 \" :device-type-data=\"deviceTypeData\"/>\n      </el-tab-pane>\n    </el-tabs>\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"closeDialog\" style=\"margin-left: 92%;margin-top: 1%;\">取 消</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport TechnicalParameter from '@/views/dagangOilfield/bzgl/sbbzk/technicalParameter'\nimport EquipmentComponents from '@/views/dagangOilfield/bzgl/sbbzk/equipmentComponents'\n\nexport default {\n  props: {\n    deviceTypeData: {\n      type: Object\n    }\n  },\n  name: 'technicalAndPart',\n  components: { EquipmentComponents, TechnicalParameter },\n  data() {\n    return {\n      activeName: 'first',\n      tabsIndex: 1\n    }\n  },\n  mounted() {\n  },\n  methods: {\n    //tab也切换点击事件\n    handleClick(tab) {\n      this.tabsIndex = tab.name === 'first' ? 1 : 2\n    },\n    //关闭弹窗\n    closeDialog() {\n      this.$emit('closeParamDialog')\n    }\n  }\n}\n</script>\n\n<style scoped>\n\n</style>\n"]}]}