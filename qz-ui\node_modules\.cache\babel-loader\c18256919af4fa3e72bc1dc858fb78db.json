{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\syxmxqInfo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\syxmxqInfo.vue", "mtime": 1706897323690}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["syxmxqInfo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6JA;;AACA;;AAOA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,KAAA,EAAA;AACA,IAAA,MAAA,EAAA;AACA,MAAA,IAAA,EAAA;AADA,KADA;AAIA,IAAA,MAAA,EAAA;AACA,MAAA,IAAA,EAAA;AADA;AAJA,GADA;AASA,EAAA,IAAA,EAAA,YATA;AAUA,EAAA,IAVA,kBAUA;AACA,WAAA;AACA;AACA,MAAA,EAAA,EAAA,EAFA;AAGA,MAAA,EAAA,EAAA,EAHA;AAIA;AACA,MAAA,KAAA,EAAA,EALA;AAMA,MAAA,KAAA,EAAA,EANA;AAOA,MAAA,IAAA,EAAA,EAPA;AAQA;AACA,MAAA,QAAA,EAAA,EATA;AAUA;AACA,MAAA,QAAA,EAAA,EAXA;AAYA;AACA,MAAA,MAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,EAAA,EAAA,EAJA;AAKA,QAAA,MAAA,EAAA,EALA;AAMA,QAAA,KAAA,EAAA;AANA,OAbA;AAqBA,MAAA,OAAA,EAAA,IArBA;AAqBA;AACA,MAAA,OAAA,EAAA,CAtBA;AAsBA;AACA,MAAA,KAAA,EAAA,IAAA,GAAA,EAvBA;AAuBA;AACA,MAAA,SAAA,EAAA,KAAA,MAxBA;AAwBA;AACA,MAAA,MAAA,EAAA,SAzBA;AAyBA;AAEA,MAAA,KAAA,EAAA,SA3BA;AA4BA,MAAA,IAAA,EAAA;AACA,QAAA,KAAA,EAAA,SADA;AAEA,QAAA,QAAA,EAAA,SAFA;AAGA,QAAA,IAAA,EAAA,SAHA;AAIA,QAAA,IAAA,EAAA,SAJA;AAKA,QAAA,KAAA,EAAA,SALA;AAMA,QAAA,IAAA,EAAA;AANA,OA5BA;AAoCA,MAAA,IAAA,EAAA,KApCA;AAqCA,MAAA,MAAA,EAAA,EArCA;AAqCA;AACA,MAAA,YAAA,EAAA,EAtCA;AAsCA;AACA,MAAA,SAAA,EAAA,EAvCA;AAuCA;AACA,MAAA,KAAA,EAAA,KAxCA;AAyCA,MAAA,OAAA,EAAA,KAzCA;AA0CA,MAAA,QAAA,EAAA,KA1CA;AA2CA,MAAA,QAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,OAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAJA,CA3CA;AAiDA,MAAA,YAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA;AAjDA,KAAA;AAsDA,GAjEA;AAmEA,EAAA,OAnEA,qBAmEA;AACA;AACA,SAAA,aAAA;AACA,GAtEA;AAuEA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,aAFA,2BAEA;AACA;AACA,WAAA,OAAA,GAAA,mBAAA,OAAA,CAAA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,UAAA,EAAA;AAFA,OAAA,CAAA;AAIA,WAAA,EAAA,GAAA,OAAA,KAAA,MAAA,CAAA,GAAA,IAAA,WAAA,GAAA,KAAA,MAAA,CAAA,GAAA,GAAA,KAAA,MAAA,CAAA,GAAA;AACA,WAAA,EAAA,GAAA,OAAA,KAAA,MAAA,CAAA,GAAA,IAAA,WAAA,GAAA,KAAA,MAAA,CAAA,GAAA,GAAA,KAAA,MAAA,CAAA,GAAA;AACA,WAAA,MAAA,GAAA,KAAA,MAAA,CAAA,MAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,WAAA,KAAA,EAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,gBAAA,KAAA,MAAA,EAVA,CAYA;;AACA,WAAA,gBAAA,CAAA,CAAA,IAAA,EAAA,IAAA,CAAA;AACA,WAAA,YAAA;AACA,WAAA,OAAA,CAAA,KAAA,GAfA,CAeA;AACA,KAlBA;AAoBA,IAAA,cApBA,0BAoBA,GApBA,EAoBA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,MAAA,GAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,UAAA,IAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA,KAAA,GAAA;AACA,OAFA,CAAA;AAGA,WAAA,IAAA,CAAA,IAAA,GAAA,GAAA,CAAA,KAAA;AACA,KA1BA;AA4BA,IAAA,aA5BA,yBA4BA,GA5BA,EA4BA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,MAAA,GAAA,GAAA,KAAA,MAAA,CAAA,IAAA,CAAA,UAAA,IAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA,KAAA,GAAA;AACA,OAFA,CAAA;AAGA,WAAA,IAAA,CAAA,IAAA,GAAA,GAAA,CAAA,KAAA;AACA,KAlCA;AAoCA,IAAA,WApCA,uBAoCA,GApCA,EAoCA;AAAA,UACA,KADA,GACA,GADA,CACA,KADA;AAAA,UACA,KADA,GACA,GADA,CACA,KADA;;AAEA,UAAA,KAAA,IAAA,MAAA,EAAA;AACA,aAAA,KAAA,GAAA,IAAA;AACA,aAAA,OAAA,GAAA,KAAA;AACA,aAAA,QAAA,GAAA,KAAA;AACA,aAAA,IAAA,CAAA,IAAA,GAAA,MAAA;AACA;;AACA,UAAA,KAAA,IAAA,OAAA,EAAA;AACA,aAAA,QAAA,GAAA,IAAA;AACA,aAAA,OAAA,GAAA,KAAA;AACA,aAAA,KAAA,GAAA,KAAA;AACA,aAAA,IAAA,CAAA,IAAA,GAAA,OAAA;AACA;;AACA,UAAA,KAAA,IAAA,MAAA,EAAA;AACA,aAAA,OAAA,GAAA,IAAA;AACA,aAAA,QAAA,GAAA,KAAA;AACA,aAAA,KAAA,GAAA,KAAA;AACA,aAAA,IAAA,CAAA,IAAA,GAAA,MAAA;AACA;;AACA,UAAA,KAAA,IAAA,MAAA,EAAA;AACA,aAAA,KAAA,GAAA,KAAA;AACA,aAAA,OAAA,GAAA,KAAA;AACA,aAAA,QAAA,GAAA,KAAA;AACA,aAAA,IAAA,CAAA,IAAA,GAAA,MAAA;AACA;AAEA,KA/DA;AAiEA;AACA,IAAA,OAlEA,qBAkEA;AAAA;;AACA,6BAAA;AAAA,QAAA,MAAA,EAAA,KAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,KAAA,CAAA,MAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KAtEA;AAwEA;AACA,IAAA,QAzEA,sBAyEA;AAAA;;AACA,gCAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KA7EA;AA+EA;AACA,IAAA,YAhFA,0BAgFA;AACA,UAAA,KAAA,GAAA,QAAA,CAAA,cAAA,CAAA,YAAA,CAAA;;AACA,UAAA,KAAA,IAAA,IAAA,EAAA;AACA,QAAA,KAAA,CAAA,SAAA,GAAA,EAAA;AACA,YAAA,EAAA,GAAA,KAAA,EAAA;AACA,YAAA,EAAA,GAAA,KAAA,EAAA;AACA,aAAA,OAAA,GAAA,MAAA,MAAA,CAAA,EAAA,CAAA;AACA,YAAA,GAAA,GAAA,EAAA;;AAEA,aAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,EAAA,EAAA,CAAA,EAAA,EAAA;AACA,cAAA,IAAA,GAAA,MAAA;;AACA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,SAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,gBAAA,IAAA,GAAA,KAAA,SAAA,CAAA,CAAA,CAAA;AACA,gBAAA,IAAA,GAAA,IAAA,CAAA,IAAA,CAFA,CAEA;;AACA,gBAAA,EAAA,GAAA,EAAA;AACA,gBAAA,IAAA,GAAA,IAAA,CAAA,IAAA,IAAA,IAAA,GAAA,GAAA,GAAA,IAAA,CAAA,IAAA;;AACA,gBAAA,IAAA,CAAA,QAAA,KAAA,CAAA,CAAA,QAAA,EAAA,EAAA;AACA,cAAA,IAAA,IACA,4BACA,IAAA,CAAA,KADA,GAEA,kBAFA,GAGA,KAAA,OAAA,GAAA,IAAA,CAAA,OAHA,GAIA,cAJA,GAKA,IAAA,CAAA,OALA,GAMA,aANA,GAOA,IAAA,CAAA,OAPA,GAQA,IARA,GASA,IATA,GAUA,OAXA;AAYA;AACA;;AACA,UAAA,IAAA,IAAA,OAAA;AACA,UAAA,GAAA,IAAA,IAAA;AACA;;AACA,QAAA,KAAA,CAAA,SAAA,GAAA,GAAA,CAhCA,CAiCA;;AACA,aAAA,aAAA;AACA;AACA,KAtHA;AAwHA;AACA,IAAA,WAzHA,yBAyHA;AAAA;;AACA;AACA,WAAA,OAAA,GAAA,mBAAA,OAAA,CAAA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,UAAA,EAAA;AAFA,OAAA,CAAA;AAIA,UAAA,MAAA,GAAA,IAAA,CAAA,SAAA,CAAA;AACA,QAAA,KAAA,EAAA,KAAA,MAAA,CAAA,KADA;AACA;AACA,QAAA,GAAA,EAAA,MAAA,CAAA,KAAA,EAAA,CAFA;AAEA;AACA,QAAA,GAAA,EAAA,MAAA,CAAA,KAAA,EAAA,CAHA;AAGA;AACA,QAAA,IAAA,EAAA,GAJA,CAIA;;AAJA,OAAA,CAAA;AAMA,iCAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,WAAA;AACA,SAFA,MAEA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;;AACA,UAAA,MAAA,CAAA,OAAA,CAAA,KAAA,GAFA,CAEA;;AACA;AACA,OAPA;AAQA,KA7IA;AA8IA;AACA,IAAA,aA/IA,2BA+IA;AACA,UAAA,KAAA,GAAA,QAAA,CAAA,sBAAA,CAAA,QAAA,CAAA,CADA,CACA;;AACA,UAAA,QAAA,GAAA,QAAA,CAAA,sBAAA,CAAA,WAAA,CAAA,CAFA,CAEA;;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,UAAA,KAAA,IAAA,IAAA,EAAA;AACA;AACA,aAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,UAAA,KAAA,CAAA,CAAA,CAAA,CAAA,OAAA,GAAA,YAAA;AACA,YAAA,IAAA,CAAA,QAAA,GAAA,IAAA;AACA,YAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,QAAA,CAAA,OAAA;AACA,YAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,QAAA,CAAA,OAAA;AACA,YAAA,IAAA,CAAA,QAAA,GAAA,IAAA,CAAA,UAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA;AACA,YAAA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA,QAAA,CAAA,IAAA;AACA,YAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,QAAA;AACA,WAPA;AAQA;AACA;;AACA,UAAA,QAAA,IAAA,IAAA,EAAA;AACA;AACA,aAAA,IAAA,EAAA,GAAA,CAAA,EAAA,EAAA,GAAA,QAAA,CAAA,MAAA,EAAA,EAAA,EAAA,EAAA;AACA,UAAA,QAAA,CAAA,EAAA,CAAA,CAAA,OAAA,GAAA,YAAA;AACA,YAAA,IAAA,CAAA,QAAA,GAAA,IAAA;AACA,YAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,QAAA,CAAA,OAAA;AACA,YAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,QAAA,CAAA,OAAA;AACA,YAAA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA,QAAA,CAAA,IAAA;AACA,YAAA,IAAA,CAAA,QAAA,GAAA,IAAA,CAAA,UAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA;AACA,YAAA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA,QAAA,CAAA,IAAA;AACA,YAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,QAAA;AACA,WARA;AASA;AACA;AACA,KA9KA;AA+KA;AACA,IAAA,eAhLA,6BAgLA;AAAA;;AACA;AACA,WAAA,OAAA,GAAA,mBAAA,OAAA,CAAA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,UAAA,EAAA;AAFA,OAAA,CAAA;AAKA,UAAA,MAAA,GAAA,IAAA,CAAA,SAAA,CAAA;AACA,QAAA,KAAA,EAAA,KAAA,QAAA,CAAA,EADA;AAEA,QAAA,OAAA,EAAA,KAAA,KAFA;AAGA,QAAA,OAAA,EAAA,KAAA;AAHA,OAAA,CAAA,CAPA,CAYA;;AACA,gCAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,WAAA;AACA,SAFA,MAEA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;;AACA,UAAA,MAAA,CAAA,OAAA,CAAA,KAAA,GAFA,CAEA;;AACA;AACA,OAPA;AAQA,KArMA;AAsMA;AACA,IAAA,SAvMA,qBAuMA,GAvMA,EAuMA;AAAA;;AACA;AACA,UAAA,OAAA,GAAA,KAAA,QAAA,CAAA,EAAA;AACA,UAAA,IAAA,GAAA,EAAA,CAHA,CAGA;AACA;;AACA,UAAA,KAAA,KAAA,CAAA,GAAA,CAAA,OAAA,CAAA,EAAA;AACA,aAAA,KAAA,CAAA,GAAA,CAAA,OAAA,EAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,IAAA,IAAA,EAAA;AACA,YAAA,MAAA,CAAA,SAAA,CAAA,IAAA;;AACA,YAAA,IAAA,CAAA,KAAA,CAAA,OAAA,GAAA,YAAA;AACA;AACA,SALA,EADA,CAOA;;AACA,aAAA,KAAA,CAAA,MAAA,CAAA,OAAA;AACA;;AACA,UAAA,UAAA,GAAA,EAAA,CAfA,CAeA;AAEA;;AACA,UAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA;AACA,QAAA,GAAA,CAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,GAAA,QAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CADA,CAEA;;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA,CAAA,EAAA;AACA,YAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA,EAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,MAAA,CAAA,SAAA,CAAA,IAAA;;AACA,cAAA,IAAA,CAAA,KAAA,CAAA,OAAA,GAAA,YAAA;AACA,aAHA,EADA,CAKA;;;AACA,YAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,GAAA;AACA,WAVA,CAWA;;;AACA,cAAA,GAAA,EAAA;AACA,gBAAA,SAAA,GAAA,GAAA,CAAA,SAAA;;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,SAAA,CAAA,EAAA;AACA,kBAAA,SAAA,GAAA,QAAA,CAAA,cAAA,CAAA,SAAA,CAAA,CADA,CACA;;AACA,kBAAA,SAAA,EAAA;AACA,gBAAA,MAAA,CAAA,SAAA,CAAA,SAAA;AACA;;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,SAAA,EAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA;AACA,oBAAA,CAAA,GAAA,CAAA,QAAA,CAAA,IAAA,CAAA,EAAA;AACA,kBAAA,IAAA,CAAA,KAAA,CAAA,OAAA,GAAA,YAAA;AACA,iBAFA,MAEA;AACA,kBAAA,IAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,eAPA,EALA,CAaA;;;AACA,kBAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,SAAA,EAAA,IAAA;AACA,eAFA,MAEA;AACA;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,SAAA;AACA;AACA;AACA;AACA,SApCA,EAFA,CAuCA;;AACA,QAAA,GAAA,CAAA,OAAA,CAAA,UAAA,EAAA,EAAA;AACA,cAAA,GAAA,GAAA,QAAA,CAAA,cAAA,CAAA,EAAA,CAAA,CADA,CAEA;;AACA,cAAA,GAAA,EAAA;AACA,YAAA,UAAA,CAAA,IAAA,CAAA,GAAA,EADA,CACA;;AAEA,YAAA,QAAA,CAAA,cAAA,CAAA,EAAA,EAAA,KAAA,CAAA,OAAA,GAAA,MAAA,CAHA,CAIA;;AACA,YAAA,QAAA,CAAA,cAAA,CAAA,EAAA,EAAA,SAAA,GAAA,OAAA;AACA;AACA,SAVA,EAxCA,CAmDA;;AACA,aAAA,KAAA,CAAA,GAAA,CAAA,OAAA,EAAA,UAAA;AACA;AACA,KA/QA;AAgRA;AACA,IAAA,gBAjRA,8BAiRA,CAAA,CAjRA;AAmRA;AACA,IAAA,cApRA,4BAoRA;AACA,WAAA,IAAA,GAAA,KAAA;AACA,WAAA,IAAA,GAAA;AACA,QAAA,KAAA,EAAA,SADA;AAEA,QAAA,QAAA,EAAA,SAFA;AAGA,QAAA,IAAA,EAAA,SAHA;AAIA,QAAA,IAAA,EAAA,SAJA;AAKA,QAAA,KAAA,EAAA,SALA;AAMA,QAAA,IAAA,EAAA;AANA,OAAA;AAQA,KA9RA;AAgSA;AACA,IAAA,UAjSA,sBAiSA,EAjSA,EAiSA,EAjSA,EAiSA,IAjSA,EAiSA,IAjSA,EAiSA;AACA,UAAA,EAAA,KAAA,CAAA,EAAA;AACA;AACA,YAAA,EAAA,IAAA,CAAA,EAAA;AACA,eAAA,UAAA,CAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,EAAA;AACA;AACA,OALA,MAKA;AACA,YAAA,EAAA,GAAA,CAAA,EAAA;AACA;AACA;AACA,cAAA,EAAA,KAAA,CAAA,EAAA;AACA,iBAAA,SAAA,CAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,EAAA;AACA,WAFA,MAEA,IAAA,EAAA,GAAA,CAAA,EAAA;AACA;AACA,iBAAA,iBAAA,CAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,EAAA;AACA;AACA;AACA,OAjBA,CAkBA;;;AACA,WAAA,QAAA,CAAA,KAAA,CAAA,KAAA,GAAA,KAAA,OAAA,GAAA,EAAA,GAAA,GAAA,CAnBA,CAmBA;;AACA,WAAA,QAAA,CAAA,OAAA,GAAA,KAAA,KAAA;AACA,WAAA,QAAA,CAAA,OAAA,GAAA,KAAA,KAAA;AACA,KAvTA;;AAwTA;;;;;;;AAOA,IAAA,UA/TA,sBA+TA,CA/TA,EA+TA,CA/TA,EA+TA,EA/TA,EA+TA,EA/TA,EA+TA;AACA,UAAA,SAAA,GAAA,EAAA,CADA,CACA;;AACA,UAAA,KAAA,GAAA,EAAA,CAFA,CAEA;;AACA,UAAA,EAAA,GAAA,KAAA,EAAA,GAAA,CAAA,EAAA;AACA;AACA,QAAA,KAAA,GAAA,KAAA,EAAA,GAAA,CAAA;AACA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,EAAA,CAAA,EAAA,EAAA;AACA,QAAA,SAAA,CAAA,IAAA,CAAA,CAAA,GAAA,GAAA,IAAA,CAAA,GAAA,CAAA,CAAA;AACA,OATA,CAUA;;;AACA,WAAA,SAAA,CAAA,SAAA;AACA,KA3UA;;AA4UA;;;;;;;AAOA,IAAA,SAnVA,qBAmVA,CAnVA,EAmVA,CAnVA,EAmVA,EAnVA,EAmVA,EAnVA,EAmVA;AACA,UAAA,SAAA,GAAA,EAAA,CADA,CACA;;AACA,UAAA,KAAA,GAAA,EAAA,CAFA,CAEA;;AACA,UAAA,EAAA,GAAA,KAAA,EAAA,GAAA,CAAA,EAAA;AACA;AACA,QAAA,KAAA,GAAA,KAAA,EAAA,GAAA,CAAA;AACA;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,KAAA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,EAAA,CAAA,EAAA,EAAA;AACA,QAAA,SAAA,CAAA,IAAA,CAAA,CAAA,GAAA,CAAA,GAAA,GAAA,GAAA,CAAA;AACA,OAVA,CAWA;;;AACA,WAAA,SAAA,CAAA,SAAA;AACA,KAhWA;;AAiWA;;;;;;;AAOA,IAAA,iBAxWA,6BAwWA,CAxWA,EAwWA,CAxWA,EAwWA,EAxWA,EAwWA,EAxWA,EAwWA;AACA,UAAA,SAAA,GAAA,EAAA,CADA,CACA;;AACA,UAAA,QAAA,GAAA,EAAA,CAFA,CAGA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,EAAA,EAAA,CAAA,EAAA,EAAA;AACA;AACA,aAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,EAAA,EAAA,CAAA,EAAA,EAAA;AACA;AACA,UAAA,QAAA,GAAA,CAAA,GAAA,CAAA,GAAA,GAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAFA,CAGA;;AACA,cAAA,QAAA,KAAA,CAAA,GAAA,GAAA,GAAA,CAAA,EAAA;AACA,YAAA,SAAA,CAAA,IAAA,CAAA,QAAA;AACA;AACA;AACA,OAdA,CAeA;;;AACA,WAAA,SAAA,CAAA,SAAA;AACA,KAzXA;AA0XA;AACA,IAAA,gBA3XA,4BA2XA,IA3XA,EA2XA;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,YAAA,GAAA,GAAA,QAAA,CAAA,cAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;;AACA,YAAA,GAAA,IAAA,IAAA,IAAA,OAAA,GAAA,IAAA,WAAA,EAAA;AACA,kBAAA,IAAA,CAAA,CAAA,CAAA;AACA,iBAAA,IAAA;AACA,cAAA,GAAA,CAAA,KAAA,GAAA,KAAA,EAAA;AACA;;AACA,iBAAA,IAAA;AACA,cAAA,GAAA,CAAA,KAAA,GAAA,KAAA,EAAA;AACA;;AACA,iBAAA,OAAA;AACA,cAAA,GAAA,CAAA,KAAA,GAAA,KAAA,KAAA;AACA;;AACA,iBAAA,OAAA;AACA,cAAA,GAAA,CAAA,KAAA,GAAA,KAAA,KAAA;AACA;AAZA;AAcA;AACA;AACA,KA/YA;AAgZA;AACA,IAAA,UAjZA,wBAiZA;AAAA;;AACA;AACA,WAAA,OAAA,GAAA,mBAAA,OAAA,CAAA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,UAAA,EAAA;AAFA,OAAA,CAAA;AAIA,UAAA,KAAA,GAAA,KAAA,QAAA,CAAA,EAAA;AACA,UAAA,MAAA,GAAA,KAAA,UAAA,CAAA,KAAA,CAAA;AACA,gCAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,WAAA;AACA,SAFA,MAEA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;;AACA,QAAA,MAAA,CAAA,OAAA,CAAA,KAAA,GANA,CAMA;;AACA,OAPA;AAQA,KAjaA;AAkaA;AACA,IAAA,WAnaA,yBAmaA;AACA,WAAA,IAAA,GAAA,IAAA;AACA,WAAA,OAAA;AACA,WAAA,QAAA,GAHA,CAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KA5bA;AA8bA;AACA,IAAA,OA/bA,qBA+bA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,MAAA,CAAA,IAAA,CAAA,KAAA,GAAA,MAAA,CAAA,QAAA,CAAA,EAAA;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,IAAA,GAAA,MAAA,CAAA,QAAA,CAAA,IAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,aAAA,MAAA,CAAA,IAAA;AAJA;AAAA,uBAKA,yBAAA,MAAA,CAAA,IAAA,CALA;;AAAA;AAAA;AAKA,gBAAA,IALA,oBAKA,IALA;;AAMA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,WAAA;;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA;;AATA;AAAA;;AAAA;AAAA;AAAA;AAWA,gBAAA,OAAA,CAAA,GAAA;;AAXA;AAaA,gBAAA,MAAA,CAAA,IAAA,GAAA,KAAA;;AAbA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA,KA7cA;AA+cA;AACA,IAAA,SAhdA,qBAgdA,GAhdA,EAgdA;AACA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,KAAA,CAAA,KAAA,GAAA,KAAA,OAAA,GAAA,GAAA;AACA,QAAA,GAAA,CAAA,OAAA,GAAA,GAAA;AACA,QAAA,GAAA,CAAA,OAAA,GAAA,GAAA;AACA;AACA,KAtdA;AAudA;AACA,IAAA,UAxdA,sBAwdA,GAxdA,EAwdA,UAxdA,EAwdA;AACA,cAAA,UAAA;AACA,aAAA,IAAA;AACA,eAAA,EAAA,GAAA,GAAA;AACA;;AACA,aAAA,IAAA;AACA,eAAA,EAAA,GAAA,GAAA;AACA;;AACA,aAAA,OAAA;AACA,eAAA,KAAA,GAAA,GAAA;AACA;;AACA,aAAA,OAAA;AACA,eAAA,KAAA,GAAA,GAAA;AACA;;AACA,aAAA,MAAA;AACA,eAAA,IAAA,GAAA,GAAA;AACA;AAfA;AAiBA,KA1eA;AA2eA;AACA,IAAA,aA5eA,yBA4eA,EA5eA,EA4eA,EA5eA,EA4eA;AACA,UAAA,MAAA,GAAA,IAAA;AACA,WAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YACA,IAAA,CAAA,QAAA,KAAA,EAAA,CAAA,QAAA,EAAA,IACA,IAAA,CAAA,QAAA,KAAA,EAAA,CAAA,QAAA,EAFA,EAGA;AACA,UAAA,MAAA,GAAA,IAAA;;AACA,cAAA,MAAA,CAAA,IAAA,IAAA,IAAA,EAAA;AACA,YAAA,MAAA,CAAA,IAAA,GAAA,EAAA;AACA;AACA;AACA,OAVA;AAWA,aAAA,MAAA;AACA,KA1fA;AA2fA;AACA,IAAA,UA5fA,sBA4fA,KA5fA,EA4fA;AACA,UAAA,MAAA,GAAA,IAAA;AACA,WAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,KAAA,KAAA,KAAA,EAAA;AACA,UAAA,MAAA,GAAA,IAAA;AACA;AACA,OAJA;AAKA,aAAA,MAAA;AACA,KApgBA;AAqgBA;AACA,IAAA,WAtgBA,yBAsgBA;AAAA;;AACA,UAAA,KAAA,GAAA,IAAA,CAAA,SAAA,CAAA;AACA,QAAA,MAAA,EAAA,KAAA,MAAA,CAAA,KADA;AAEA,QAAA,IAAA,EAAA;AAFA,OAAA,CAAA,CADA,CAKA;;AACA,2BAAA,KAAA,EAAA,IAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,SAAA,GAAA,IAAA,CAAA,IAAA,CADA,CAEA;;AACA,UAAA,MAAA,CAAA,YAAA;;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA;AACA,SALA,MAKA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,eAAA;AACA;;AACA,QAAA,MAAA,CAAA,OAAA,CAAA,KAAA,GATA,CASA;;AACA,OAVA;AAWA;AAvhBA;AAvEA,C", "sourcesContent": ["<template>\n  <div class=\"syxmxq_info\">\n    <div id=\"syxmxq_left\">\n      <ul class=\"ul1_cont\">\n        <li>表格</li>\n        <li>\n          行：<el-input\n            id=\"hs\"\n            v-model=\"hs\"\n            placeholder=\"\"\n            class=\"inp1\"\n            @input=\"(val) => checkInput(val, 'hs')\"\n          ></el-input>\n        </li>\n        <li>\n          列：<el-input\n            id=\"ls\"\n            v-model=\"ls\"\n            placeholder=\"\"\n            class=\"inp1\"\n            @input=\"(val) => checkInput(val, 'ls')\"\n          ></el-input>\n        </li>\n        <el-button type=\"success\" @click=\"createTable\" class=\"change_btn\"\n          >创建表格</el-button\n        >\n      </ul>\n      <ul class=\"ul2_cont\">\n        <li>单元格操作</li>\n        <li>\n          行跨度：<el-input\n            id=\"addhs\"\n            v-model=\"addhs\"\n            placeholder=\"\"\n            class=\"inp2\"\n            @input=\"(val) => checkInput(val, 'addhs')\"\n          ></el-input>\n        </li>\n        <li>\n          列跨度：<el-input\n            id=\"addls\"\n            v-model=\"addls\"\n            placeholder=\"\"\n            class=\"inp2\"\n            @input=\"(val) => checkInput(val, 'addls')\"\n          ></el-input>\n        </li>\n        <li>\n          单元格类型：<el-input\n            v-model=\"nrlx\"\n            placeholder=\"\"\n            class=\"inp1\"\n            disabled\n            @input=\"(val) => checkInput(val, 'nrlx')\"\n          ></el-input>\n        </li>\n        <el-button type=\"success\" @click=\"saveChangeTable\" class=\"change_btn\"\n          >保存</el-button\n        >\n        <el-button @click=\"clearChangeTable\" class=\"change_btn\">清除</el-button>\n      </ul>\n      <ul class=\"ul3_cont\">\n        <el-button type=\"warning\" @click=\"saveTdValue\">编辑单元格</el-button>\n        <el-button type=\"warning\" @click=\"resetTable\">重置单元格</el-button>\n      </ul>\n    </div>\n    <table id=\"mpxq_right\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\"></table>\n\n    <el-dialog\n      :title=\"title\"\n      v-dialogDrag\n      :visible.sync=\"show\"\n      width=\"50%\"\n      append-to-body\n      @close=\"getInsterClose\"\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\">\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验类型：\" prop=\"nrlx\">\n              <el-select\n                placeholder=\"请选择试验类型\"\n                v-model=\"form.nrlx\"\n                style=\"width: 100%\"\n                @change=\"selectvalue\"\n              >\n                <el-option\n                  v-for=\"item in nrlxList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"{ label: item.label, value: item.value }\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\" v-show=\"hiddezxm\">\n            <el-form-item label=\"关联试验子项目：\" prop=\"zxmId\">\n              <el-select\n                placeholder=\"请选择试验子项目\"\n                v-model=\"form.zxmId\"\n                style=\"width: 100%\"\n                filterable\n                @change=\"selectzxmvalue\"\n              >\n                <el-option\n                ref ='myselected'\n                  v-for=\"item in zxmmcList\"\n                  :key=\"item.value\"\n                  :label=\"item.laber\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\" v-show=\"hiddebw\">\n            <el-form-item label=\"关联试验部位：\" prop=\"bwId\">\n              <el-select\n                placeholder=\"请选择试验部位\"\n                v-model=\"form.bwId\"\n                style=\"width: 100%\"\n                @change=\"selectbwvalue\"\n              >\n                <el-option\n                  v-for=\"item in bwList\"\n                  :key=\"item.value\"\n                  :label=\"item.laber\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\" v-show=\"hidde\">\n            <el-form-item label=\"试验名称：\" prop=\"nrbs\">\n              <el-input\n                placeholder=\"请选择内容类型\"\n                v-model=\"form.nrbs\"\n                style=\"width: 100%\"\n              >\n              </el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"getInsterClose\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"saveRow\">保 存</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { Loading } from \"element-ui\";\nimport {\n  resetCells,\n  createTable,\n  mergeCells,\n  editCells,\n  getCells,\n} from \"@/api/dagangOilfield/bzgl/sympk/sympInfo\";\nimport { getTable } from \"@/api/dagangOilfield/bzgl/sympk/sympk\";\nimport { getBwSelect, getZxmSelect } from \"@/api/dagangOilfield/bzgl/syxm\";\n\nexport default {\n  props: {\n    mpData: {\n      type: Object,\n    },\n    mxData: {\n      type: Array,\n    },\n  },\n  name: \"syxmxqInfo\",\n  data() {\n    return {\n      //初始表格的行数 列数\n      hs: \"\",\n      ls: \"\",\n      //初始合并行数 列数\n      addhs: \"\",\n      addls: \"\",\n      nrlx: \"\",\n      //一行的数据\n      cellData: \"\",\n      //选中合并行、列的tr\n      changeTr: \"\",\n      //查询条件\n      params: {\n        pageNum: 1,\n        pageSize: 10,\n        mpid: \"\",\n        zy: \"\",\n        sblxbm: \"\",\n        zxmId:\"\",\n      },\n      loading: null, //遮罩层\n      tdWidth: 0, //一个单元格所占宽度\n      tdMap: new Map(), //用于存放被合并或拆分的单元格（key:当前点击的单元格,value:被处理过的单元格数组）\n      tableData: this.mxData, //表格数据\n      sblxbm: undefined, //设备类型编码\n\n      title: \"单元格属性定义\",\n      form: {\n        objId: undefined,\n        readonly: undefined,\n        nrlx: undefined,\n        bwId: undefined,\n        zxmId: undefined,\n        nrbs:undefined,\n      },\n      show: false,\n      bwList: [], //试验部位\n      zxmmDataList: [], //子项目所有结果数据\n      zxmmcList: [], //子项目\n      hidde: false,\n      hiddebw: false,\n      hiddezxm: false,\n      nrlxList: [\n        { label: \"静态文本\", value: \"静态文本\" },\n        { label: \"试验子项目\", value: \"试验子项目\" },\n        { label: \"试验部位\", value: \"试验部位\" },\n        { label: \"试验数据\", value: \"试验数据\" },\n      ],\n      readonlyList: [\n        { label: \"是\", value: \"Y\" },\n        { label: \"否\", value: \"N\" },\n      ],\n    };\n  },\n\n  mounted() {\n    //获取表格初始行数和列数\n    this.initTableData();\n  },\n  methods: {\n    //获取铭牌内容数据\n    initTableData() {\n      //初始化遮罩层\n      this.loading = Loading.service({\n        text: \"加载中，请稍后...\",\n        background: \"rgba(109,106,106,0.35)\",\n      });\n      this.hs = typeof (this.mpData.AHs) != 'undefined'?this.mpData.AHs:this.mpData.aHs;\n      this.ls = typeof (this.mpData.ALs) != 'undefined'?this.mpData.ALs:this.mpData.aLs;\n      this.sblxbm = this.mpData.sblxbm;\n      console.log(\"--hs--\" + this.hs);\n      console.log(\"++-mpData--\" + this.sblxbm);\n\n      //更新输入框的值\n      this.updateInputValue([\"hs\", \"ls\"]);\n      this.processTable();\n      this.loading.close(); //关闭遮罩层\n    },\n\n    selectzxmvalue(val) {\n      let obj={};\n      obj=this.zxmmcList.find((item)=>{\n        return item.value===val;\n      })\n      this.form.nrbs=obj.laber;\n    },\n\n    selectbwvalue(val){\n      let obj={};\n      obj=this.bwList.find((item)=>{\n        return item.value===val;\n      })\n        this.form.nrbs=obj.laber;\n    },\n\n    selectvalue(val) {\n      const { value, label } = val;\n      if (label == \"静态文本\") {\n        this.hidde = true;\n        this.hiddebw=false;\n        this.hiddezxm = false;\n         this.form.nrlx=\"静态文本\"\n      }\n      if (label == \"试验子项目\") {\n        this.hiddezxm = true;\n        this.hiddebw=false;\n         this.hidde=false;\n         this.form.nrlx=\"试验子项目\"\n      }\n      if (label == \"试验部位\") {\n        this.hiddebw = true;\n        this.hiddezxm=false;\n        this.hidde=false;\n         this.form.nrlx=\"试验部位\"\n      }\n       if (label == \"试验数据\") {\n        this.hidde = false;\n        this.hiddebw=false;\n        this.hiddezxm = false;\n        this.form.nrlx=\"试验数据\"\n      }\n\n    },\n\n    //获取部位下拉框\n    getSybw() {\n      getBwSelect({ sblxbm: this.sblxbm }).then((res) => {\n        this.bwList = res.data;\n      });\n    },\n\n    // 获取试验子项目下拉框数据\n    getSyzxm() {\n      getZxmSelect().then((res) => {\n        this.zxmmcList = res.data;\n      });\n    },\n\n    //根据行数和列数创建表格\n    processTable() {\n      var tbody = document.getElementById(\"mpxq_right\");\n      if (tbody != null) {\n        tbody.innerHTML = \"\";\n        let hs = this.hs;\n        let ls = this.ls;\n        this.tdWidth = 100 / Number(ls);\n        let str = \"\";\n\n        for (let i = 0; i < hs; i++) {\n          let temp = \"<tr>\";\n          for (let j = 0; j < this.tableData.length; j++) {\n            let item = this.tableData[j];\n            let sjlx = item.sjlx; //数据类型\n            let nr = \"\";\n            let nrbs = item.nrbs == null ? \"-\" : item.nrbs;\n            if (item.rowindex === i.toString()) {\n              temp +=\n                \"<td class='trName' id='\" +\n                item.objId +\n                \"' style='width: \" +\n                this.tdWidth * item.colspan +\n                \"%' rowspan='\" +\n                item.rowspan +\n                \"' colspan='\" +\n                item.colspan +\n                \"'>\" +\n                nrbs +\n                \"</td>\";\n            }\n          }\n          temp += \"</tr>\";\n          str += temp;\n        }\n        tbody.innerHTML = str;\n        // //给循环出来的单元格加上点击事件\n        this.addClickEvent();\n      }\n    },\n\n    //手动创建表格\n    createTable() {\n      //初始化遮罩层\n      this.loading = Loading.service({\n        text: \"加载中，请稍后...\",\n        background: \"rgba(109,106,106,0.35)\",\n      });\n      let params = JSON.stringify({\n        objId: this.mpData.objId, //铭牌id\n        aHs: Number(this.hs), //行数\n        aLs: Number(this.ls), //列数\n        lbbs: \"A\", //类别标识，表示修改的A表格\n      });\n      createTable(params).then((res) => {\n        if (res.code === \"0000\") {\n          this.updateTable();\n        } else {\n          this.$message.error(\"操作失败\");\n          this.loading.close(); //关闭遮罩层\n        }\n      });\n    },\n    //给循环出来的单元格加上点击事件\n    addClickEvent() {\n      let trArr = document.getElementsByClassName(\"trName\"); //不可编辑的单元格\n      let inputArr = document.getElementsByClassName(\"input_cls\"); //可编辑的单元格\n      let that = this;\n      if (trArr != null) {\n        //循环所有的tr\n        for (let i = 0; i < trArr.length; i++) {\n          trArr[i].onclick = function () {\n            that.changeTr = this;\n            that.addhs = that.changeTr.rowSpan;\n            that.addls = that.changeTr.colSpan;\n            that.cellData = that.getCellEle(that.changeTr.id);\n            that.nrlx = that.cellData.nrlx;\n            console.log(that.cellData);\n          };\n        }\n      }\n      if (inputArr != null) {\n        //循环所有的tr\n        for (let i = 0; i < inputArr.length; i++) {\n          inputArr[i].onclick = function () {\n            that.changeTr = this;\n            that.addhs = that.changeTr.rowSpan;\n            that.addls = that.changeTr.colSpan;\n            that.nrlx = that.changeTr.nrlx;\n            that.cellData = that.getCellEle(that.changeTr.id);\n            that.nrlx = that.cellData.nrlx;\n            console.log(that.cellData);\n          };\n        }\n      }\n    },\n    //合并拆分保存\n    saveChangeTable() {\n      //初始化遮罩层\n      this.loading = Loading.service({\n        text: \"加载中，请稍后...\",\n        background: \"rgba(109,106,106,0.35)\",\n      });\n\n      let params = JSON.stringify({\n        objId: this.changeTr.id,\n        rowspan: this.addhs,\n        colspan: this.addls,\n      });\n      //先请求接口，如果后台可以执行合并或拆分，则将最新的表格数据请求回来进行前端展示\n      mergeCells(params).then((res) => {\n        if (res.code === \"0000\") {\n          this.updateTable();\n        } else {\n          this.$message.error(\"操作失败\");\n          this.loading.close(); //关闭遮罩层\n        }\n      });\n    },\n    //处理合并或拆分\n    processTr(ids) {\n      //点击的单元格id\n      let clickId = this.changeTr.id;\n      let arr1 = []; //需要重新设置map的数组\n      //如果之前已经处理过该单元格,则先将其还原\n      if (this.tdMap.has(clickId)) {\n        this.tdMap.get(clickId).forEach((item) => {\n          if (item != null) {\n            this.resetCell(item);\n            item.style.display = \"table-cell\";\n          }\n        });\n        //操作完后将数据从map中删除\n        this.tdMap.delete(clickId);\n      }\n      let processEle = []; //被处理的元素\n\n      //现将连带受影响的单元格还原，再进行隐藏处理\n      if (ids.length > 0) {\n        //执行还原\n        ids.forEach((id1) => {\n          let ele = document.getElementById(id1);\n          //如果此次处理的单元格中有已经被处理过的，先将其还原\n          if (this.tdMap.has(id1)) {\n            this.tdMap.get(id1).forEach((item) => {\n              this.resetCell(item);\n              item.style.display = \"table-cell\";\n            });\n            //操作完后将数据从map中删除\n            this.tdMap.delete(id1);\n          }\n          //处理被连带的已经合并过的单元格\n          if (ele) {\n            let className = ele.className;\n            if (this.tdMap.has(className)) {\n              let mergeCell = document.getElementById(className); //被连带的已经合并过的cell\n              if (mergeCell) {\n                this.resetCell(mergeCell);\n              }\n              this.tdMap.get(className).forEach((item) => {\n                //需要把此次要隐藏的单元格排除掉，不然隐藏完下次循环又会放出来\n                if (!ids.includes(item)) {\n                  item.style.display = \"table-cell\";\n                } else {\n                  arr1.push(item);\n                }\n              });\n              //处理完成后，更新map中的值，将处理过的排除掉\n              if (arr1.length > 0) {\n                this.tdMap.set(className, arr1);\n              } else {\n                //操作完后将数据从map中删除\n                this.tdMap.delete(className);\n              }\n            }\n          }\n        });\n        //执行隐藏\n        ids.forEach((id) => {\n          let ele = document.getElementById(id);\n          //将多余的单元格隐藏\n          if (ele) {\n            processEle.push(ele); //添加数据保存到map中\n\n            document.getElementById(id).style.display = \"none\";\n            //将className设置给被操作的单元格，方便下次有连带操作时对单元格进行处理\n            document.getElementById(id).className = clickId;\n          }\n        });\n        //重新设置map中的值\n        this.tdMap.set(clickId, processEle);\n      }\n    },\n    //取消更改的合并行、列数\n    clearChangeTable() {},\n\n    //关闭弹窗\n    getInsterClose() {\n      this.show = false;\n      this.form = {\n        objId: undefined,\n        readonly: undefined,\n        nrlx: undefined,\n        nrbs: undefined,\n        zxmId:undefined,\n        bwId:undefined,\n      };\n    },\n\n    //进行合并或拆分操作\n    mergeTable(hs, ls, addh, addl) {\n      if (hs === 1) {\n        //合并列\n        if (ls >= 1) {\n          this.mergeCells(addh, addl, hs, ls);\n        }\n      } else {\n        if (hs > 1) {\n          //多行\n          //合并行\n          if (ls === 1) {\n            this.mergeRows(addh, addl, hs, ls);\n          } else if (ls > 1) {\n            //合并多行多列\n            this.mergeRowsAndCells(addh, addl, hs, ls);\n          }\n        }\n      }\n      //要合并的单元格进行合并\n      this.changeTr.style.width = this.tdWidth * ls + \"%\"; //设置合并后的单元格宽度\n      this.changeTr.rowSpan = this.addhs;\n      this.changeTr.colSpan = this.addls;\n    },\n    /**\n     * 第一种情况，合并列（一行多列）\n     * @param h 当前元素所在行\n     * @param l 当前元素所在列\n     * @param hs 要合并的行数\n     * @param ls 要合并的列数\n     */\n    mergeCells(h, l, hs, ls) {\n      let removeIds = []; //要删除的元素的id数组\n      let ls_xh = ls; //要循环的列数\n      if (ls > this.ls - l) {\n        //不能超过剩余可操作的列数\n        ls_xh = this.ls - l;\n      }\n      for (let i = 1; i < ls_xh; i++) {\n        removeIds.push(h + \"|\" + (l + i));\n      }\n      //删除多余单元格\n      this.processTr(removeIds);\n    },\n    /**\n     * 第二种情况，合并行（多行一列）\n     * @param h 当前元素所在行\n     * @param l 当前元素所在列\n     * @param hs 要合并的行数\n     * @param ls 要合并的列数\n     */\n    mergeRows(h, l, hs, ls) {\n      let removeIds = []; //要删除的元素的id数组\n      let hs_xh = hs; //要循环的行数\n      if (hs > this.hs - h) {\n        //不能超过剩余可操作的行数\n        hs_xh = this.hs - h;\n      }\n      console.log(\"hs_xh\", hs_xh);\n      for (let i = 1; i < hs_xh; i++) {\n        removeIds.push(h + i + \"|\" + l);\n      }\n      //删除多余单元格\n      this.processTr(removeIds);\n    },\n    /**\n     * 第三种情况，合并多行多列\n     * @param h 当前元素所在行\n     * @param l 当前元素所在列\n     * @param hs 要合并的行数\n     * @param ls 要合并的列数\n     */\n    mergeRowsAndCells(h, l, hs, ls) {\n      let removeIds = []; //要删除的元素的id数组\n      let removeId = \"\";\n      //先循环行（从当前行开始循环）\n      for (let j = 0; j < hs; j++) {\n        //循环列\n        for (let i = 0; i < ls; i++) {\n          //从当前列循环\n          removeId = h + j + \"|\" + (l + i);\n          //将当前单元格排除掉\n          if (removeId !== h + \"|\" + l) {\n            removeIds.push(removeId);\n          }\n        }\n      }\n      //删除多余单元格\n      this.processTr(removeIds);\n    },\n    //更新输入框的值\n    updateInputValue(arrs) {\n      for (let i = 0; i < arrs.length; i++) {\n        let ele = document.getElementById(arrs[i]);\n        if (ele != null && typeof ele != \"undefined\") {\n          switch (arrs[i]) {\n            case \"hs\":\n              ele.value = this.hs;\n              break;\n            case \"ls\":\n              ele.value = this.ls;\n              break;\n            case \"addhs\":\n              ele.value = this.addhs;\n              break;\n            case \"addls\":\n              ele.value = this.addls;\n              break;\n          }\n        }\n      }\n    },\n    //重置单元格内容\n    resetTable() {\n      //初始化遮罩层\n      this.loading = Loading.service({\n        text: \"加载中，请稍后...\",\n        background: \"rgba(109,106,106,0.35)\",\n      });\n      let objId = this.changeTr.id;\n      let params = this.getCellEle(objId);\n      resetCells(params).then((res) => {\n        if (res.code === \"0000\") {\n          this.updateTable();\n        } else {\n          this.$message.error(\"操作失败\");\n        }\n        this.loading.close(); //关闭遮罩层\n      });\n    },\n    //单元格属性编辑并保存\n    saveTdValue() {\n      this.show = true;\n      this.getSybw();\n      this.getSyzxm();\n      //   this.form.readonly = this.cellData.readonly;\n      //   this.form.nrlx = this.cellData.nrlx;\n      //   this.form.nrbs =this.cellData.nrbs;\n      //   this.form.objId =this.cellData.objId;\n      //初始化遮罩层\n      // this.loading = Loading.service({\n      //   text:\"加载中，请稍后...\",\n      //   background:'rgba(109,106,106,0.35)',\n      // })\n      // let objId = this.changeTr.id;\n      // let val = this.changeTr.getElementsByTagName(\"input\")[0].value;\n      // let params = this.getCellEle(objId);\n      // params.nrbs = val;\n      // editCells(params).then(res=>{\n      //   if(res.code==='0000'){\n      //     this.updateTable();\n      //   }else{\n      //     this.$message.error('操作失败');\n      //     this.loading .close();//关闭遮罩层\n      //   }\n      // })\n    },\n\n    //单元格属性编辑\n    async saveRow() {\n      try {\n        this.form.objId=this.changeTr.id;\n        this.form.mpid=this.cellData.mpid;\n        console.log(\"--form--\" + this.form);\n        let { code } = await editCells(this.form);\n        if (code === \"0000\") {\n          this.updateTable();\n          this.$message.success(\"操作成功\");\n        }\n      } catch (e) {\n        console.log(e);\n      }\n      this.show = false;\n    },\n\n    //重置单元格属性（宽度，合并行数，合并列数）\n    resetCell(ele) {\n      if (ele) {\n        ele.style.width = this.tdWidth + \"%\";\n        ele.rowSpan = \"1\";\n        ele.colSpan = \"1\";\n      }\n    },\n    //输入框校验\n    checkInput(val, changeType) {\n      switch (changeType) {\n        case \"hs\":\n          this.hs = val;\n          break;\n        case \"ls\":\n          this.ls = val;\n          break;\n        case \"addhs\":\n          this.addhs = val;\n          break;\n        case \"addls\":\n          this.addls = val;\n          break;\n        case \"nrlx\":\n          this.nrlx = val;\n          break;\n      }\n    },\n    //获取单元格明细数据\n    getCellDetail(hs, ls) {\n      let result = null;\n      this.tableData.forEach((item) => {\n        if (\n          item.rowindex === hs.toString() &&\n          item.colindex === ls.toString()\n        ) {\n          result = item;\n          if (result.nrbs == null) {\n            result.nrbs = \"\";\n          }\n        }\n      });\n      return result;\n    },\n    //获取某个单元格对象\n    getCellEle(objId) {\n      let result = null;\n      this.tableData.forEach((item) => {\n        if (item.objId === objId) {\n          result = item;\n        }\n      });\n      return result;\n    },\n    //获取最新的表格并重新渲染\n    updateTable() {\n      let param = JSON.stringify({\n        obj_id: this.mpData.objId,\n        lbbs: \"A\",\n      });\n      //获取最新的表格数据\n      getTable(param).then((res1) => {\n        if (res1.code === \"0000\") {\n          this.tableData = res1.data;\n          //根据最新的表格数据重新画\n          this.processTable();\n          this.$message.success(res1.msg);\n        } else {\n          this.$message.error(\"无法获取更新后的表格数据！\");\n        }\n        this.loading.close(); //关闭遮罩层\n      });\n    },\n  },\n};\n</script>\n\n\n<style scoped lang=\"scss\">\n.syxmxq_info {\n  display: flex;\n}\n#syxmxq_left {\n  margin-right: 20px;\n  ul {\n    list-style-type: none;\n    margin: 0;\n    padding: 8px;\n  }\n  border: 1px solid #0cc283;\n  width: 28%;\n  li:nth-child(1) {\n    font-weight: 700;\n  }\n  li {\n    line-height: 48px;\n    padding-left: 8px;\n    .el-input {\n      width: 70%;\n    }\n  }\n}\n.change_btn {\n  margin-top: 10px !important;\n  height: 36px !important;\n}\n.change_btn:nth-child(1) {\n  margin-left: 29%;\n}\n.change_btn:nth-child(2) {\n  margin-left: 20%;\n}\n#mpxq_right {\n  width: 72%;\n  height: 180px;\n  border: 1px solid #000;\n}\n</style>\n<style>\n#mpxq_right td {\n  border: 1px solid #000;\n  height: 35px;\n  line-height: 35px;\n  text-align: center;\n}\n#mpxq_right tr {\n  height: 35px;\n}\n#mpxq_right .atc {\n  background-color: #11ba6d;\n}\n#mpxq_right .input_cls {\n  text-align: center;\n  border: none;\n  width: 99%;\n  height: 99%;\n}\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk"}]}