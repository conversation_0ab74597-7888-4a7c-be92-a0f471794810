{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\xldzcz\\components\\czp_cx.vue?vue&type=template&id=6955e078&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\xldzcz\\components\\czp_cx.vue", "mtime": 1706897324343}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}