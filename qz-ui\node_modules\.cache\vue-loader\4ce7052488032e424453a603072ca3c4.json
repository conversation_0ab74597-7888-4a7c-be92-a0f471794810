{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\gfqxwh.vue?vue&type=template&id=3c07bd68&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\gfqxwh.vue", "mtime": 1726318090035}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImFwcC1jb250YWluZXIiIGlkPSJzYnF4RGl2Ij4KICA8IS0t5bem5L6n5qCR57uE5Lu2LS0+CiAgPGVsLXJvdyA6Z3V0dGVyPSIxIj4KICAgIDwhLS0gICDlt6bkvqfmoJEgICAtLT4KICAgIDxlbC1jb2wgOnNwYW49IjQiPgogICAgICA8ZWwtY2FyZCBzdHlsZT0iYmFja2dyb3VuZDojZTBmOGVkO3BhZGRpbmctdG9wOjEwcHg7IiBzaGFkb3c9Im5ldmVyIj4KICAgICAgICA8ZGl2PgogICAgICAgICAgPGVsLWNvbD4KICAgICAgICAgICAgPGVsLWZvcm0gbGFiZWwtd2lkdGg9IjYycHgiPgogICAgICAgICAgICAgIDxlbC1jb2wgOnNwYW49IjI0Ij4KICAgICAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuW/q+mAn+afpeivojoiIGxhYmVsLXdpZHRoPSI4MHB4Ij4KICAgICAgICAgICAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iui+k+WFpeWFs+mUruWtl+i/h+a7pCIKICAgICAgICAgICAgICAgICAgICB2LW1vZGVsPSJmaWx0ZXJUZXh0IgogICAgICAgICAgICAgICAgICAgIGNsZWFyYWJsZQogICAgICAgICAgICAgICAgICAvPgogICAgICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgICAgIDwvZWwtZm9ybT4KICAgICAgICAgIDwvZWwtY29sPgogICAgICAgIDwvZGl2PgogICAgICAgIDxkaXYgY2xhc3M9InRleHQgaGVhZC1jb250YWluZXIiPgogICAgICAgICAgPGVsLWNvbCBzdHlsZT0icGFkZGluZzowOyI+CiAgICAgICAgICAgIDxlbC10cmVlCiAgICAgICAgICAgICAgaWQ9InRyZWUiCiAgICAgICAgICAgICAgOmRhdGE9InRyZWVPcHRpb25zIgogICAgICAgICAgICAgIEBub2RlLWNsaWNrPSJoYW5kbGVOb2RlQ2xpY2siCiAgICAgICAgICAgICAgOmhpZ2hsaWdodC1jdXJyZW50PSJ0cnVlIgogICAgICAgICAgICAgIHJlZj0idHJlZSIKICAgICAgICAgICAgICA6ZmlsdGVyLW5vZGUtbWV0aG9kPSJmaWx0ZXJOb2RlIgogICAgICAgICAgICAgIG5vZGUta2V5PSJpZCIKICAgICAgICAgICAgICA6ZGVmYXVsdC1jaGVja2VkLWtleXM9IlsnMCddIgogICAgICAgICAgICAgIDpkZWZhdWx0LWV4cGFuZGVkLWtleXM9IlsnMCddIgogICAgICAgICAgICAgIGFjY29yZGlvbgogICAgICAgICAgICAvPgogICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgPC9kaXY+CiAgICAgIDwvZWwtY2FyZD4KICAgIDwvZWwtY29sPgogICAgPCEtLeWPs+S+p+WIl+ihqC0tPgogICAgPGVsLWNvbCA6c3Bhbj0iMjAiPgogICAgICA8ZWwtZmlsdGVyCiAgICAgICAgcmVmPSJmaWx0ZXIxIgogICAgICAgIDpkYXRhPSJmaWx0ZXJJbmZvLmRhdGEiCiAgICAgICAgOmZpZWxkLWxpc3Q9ImZpbHRlckluZm8uZmllbGRMaXN0IgogICAgICAgIDpidG5IaWRkZW49ImZhbHNlIgogICAgICAgIDp3aWR0aD0ieyBsYWJlbFdpZHRoOiA4MCwgaXRlbVdpZHRoOiAxNjUgfSIKICAgICAgICBAaGFuZGxlUmVzZXQ9ImZpbHRlclJlc2V0IgogICAgICAvPgogICAgICA8ZWwtd2hpdGUgY2xhc3M9ImJ1dHRvbi1ncm91cDEiPgogICAgICAgIDxkaXYgY2xhc3M9ImJ1dHRvbl9idG4iPgogICAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgICB0eXBlPSJwcmltYXJ5IgogICAgICAgICAgICBpY29uPSJlbC1pY29uLXBsdXMiCiAgICAgICAgICAgIHYtaGFzUGVybWk9IlsnZ2ZxeHdoOmJ1dHRvbjphZGQnXSIKICAgICAgICAgICAgQGNsaWNrPSJhZGRGb3JtKCdzYmJqJykiCiAgICAgICAgICAgID7mlrDlop7pg6jku7Y8L2VsLWJ1dHRvbgogICAgICAgICAgPgogICAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgICB0eXBlPSJwcmltYXJ5IgogICAgICAgICAgICBpY29uPSJlbC1pY29uLXBsdXMiCiAgICAgICAgICAgIHYtaGFzUGVybWk9IlsnZ2ZxeHdoOmJ1dHRvbjphZGQnXSIKICAgICAgICAgICAgQGNsaWNrPSJhZGRGb3JtKCdzYmJ3JykiCiAgICAgICAgICAgID7mlrDlop7pg6jkvY08L2VsLWJ1dHRvbgogICAgICAgICAgPgogICAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgICB0eXBlPSJwcmltYXJ5IgogICAgICAgICAgICBpY29uPSJlbC1pY29uLXBsdXMiCiAgICAgICAgICAgIHYtaGFzUGVybWk9IlsnZ2ZxeHdoOmJ1dHRvbjphZGQnXSIKICAgICAgICAgICAgQGNsaWNrPSJhZGRGb3JtKCdxeG1zJykiCiAgICAgICAgICAgID7mlrDlop7pmpDmgqPmj4/ov7A8L2VsLWJ1dHRvbgogICAgICAgICAgPgogICAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgICB0eXBlPSJwcmltYXJ5IgogICAgICAgICAgICBpY29uPSJlbC1pY29uLXBsdXMiCiAgICAgICAgICAgIHYtaGFzUGVybWk9IlsnZ2ZxeHdoOmJ1dHRvbjphZGQnXSIKICAgICAgICAgICAgQGNsaWNrPSJhZGRGb3JtKCdmbHlqJykiCiAgICAgICAgICAgID7mlrDlop7liIbnsbvkvp3mja48L2VsLWJ1dHRvbgogICAgICAgICAgPgogICAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgICB0eXBlPSJzdWNjZXNzIgogICAgICAgICAgICBpY29uPSJlbC1pY29uLWRvd25sb2FkIgogICAgICAgICAgICBAY2xpY2s9ImV4cG9ydEV4Y2VsIgogICAgICAgICAgICA+5a+85Ye6PC9lbC1idXR0b24KICAgICAgICAgID4KICAgICAgICA8L2Rpdj4KICAgICAgICA8Y29tcC10YWJsZQogICAgICAgICAgOnRhYmxlLWFuZC1wYWdlLWluZm89InRhYmxlQW5kUGFnZUluZm8iCiAgICAgICAgICBoZWlnaHQ9IjYyLjJ2aCIKICAgICAgICAgIHYtbG9hZGluZz0ibG9hZCIKICAgICAgICA+CiAgICAgICAgICA8ZWwtdGFibGUtY29sdW1uCiAgICAgICAgICAgIHNsb3Q9InRhYmxlX2VpZ2h0IgogICAgICAgICAgICBhbGlnbj0iY2VudGVyIgogICAgICAgICAgICBmaXhlZD0icmlnaHQiCiAgICAgICAgICAgIHN0eWxlPSJkaXNwbGF5OiBibG9jayIKICAgICAgICAgICAgbGFiZWw9IuaTjeS9nCIKICAgICAgICAgICAgd2lkdGg9IjIwMCIKICAgICAgICAgICAgOnJlc2l6YWJsZT0iZmFsc2UiCiAgICAgICAgICA+CiAgICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90LXNjb3BlPSJzY29wZSI+CiAgICAgICAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgICAgICAgdHlwZT0idGV4dCIKICAgICAgICAgICAgICAgIHNpemU9InNtYWxsIgogICAgICAgICAgICAgICAgQGNsaWNrPSJ1cGRhdGVSb3coc2NvcGUucm93KSIKICAgICAgICAgICAgICAgIHYtaGFzUGVybWk9IlsnZ2ZxeHdoOmJ1dHRvbjp1cGRhdGUnXSIKICAgICAgICAgICAgICAgIHRpdGxlPSLkv67mlLkiCiAgICAgICAgICAgICAgICBjbGFzcz0iZWwtaWNvbi1lZGl0IgogICAgICAgICAgICAgID4KICAgICAgICAgICAgICA8L2VsLWJ1dHRvbj4KICAgICAgICAgICAgICA8ZWwtYnV0dG9uCiAgICAgICAgICAgICAgICB0eXBlPSJ0ZXh0IgogICAgICAgICAgICAgICAgc2l6ZT0ic21hbGwiCiAgICAgICAgICAgICAgICBAY2xpY2s9ImRlbGV0ZVJvdyhzY29wZS5yb3cpIgogICAgICAgICAgICAgICAgdi1oYXNQZXJtaT0iWydnZnF4d2g6YnV0dG9uOmRlbGV0ZSddIgogICAgICAgICAgICAgICAgdGl0bGU9IuWIoOmZpCIKICAgICAgICAgICAgICAgIGNsYXNzPSJlbC1pY29uLWRlbGV0ZSIKICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgPC9lbC1idXR0b24+CiAgICAgICAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgICAgICAgdHlwZT0idGV4dCIKICAgICAgICAgICAgICAgIHNpemU9InNtYWxsIgogICAgICAgICAgICAgICAgQGNsaWNrPSJ2aWV3RnVuKHNjb3BlLnJvdykiCiAgICAgICAgICAgICAgICB0aXRsZT0i5p+l55yLIgogICAgICAgICAgICAgICAgY2xhc3M9ImVsLWljb24tdmlldyIKICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgPC9lbC1idXR0b24+CiAgICAgICAgICAgIDwvdGVtcGxhdGU+CiAgICAgICAgICA8L2VsLXRhYmxlLWNvbHVtbj4KICAgICAgICA8L2NvbXAtdGFibGU+CiAgICAgIDwvZWwtd2hpdGU+CiAgICA8L2VsLWNvbD4KICA8L2VsLXJvdz4KCiAgPCEtLSAg5paw5aKe6K6+5aSH6YOo5Lu2ICAtLT4KICA8ZWwtZGlhbG9nCiAgICB0aXRsZT0i5paw5aKe6K6+5aSH6YOo5Lu2IgogICAgOnZpc2libGUuc3luYz0iaXNTaG93U2JiaiIKICAgIHdpZHRoPSI1OCUiCiAgICBAY2xvc2U9ImNsb3NlRnVuKCdzYmJqJykiCiAgICB2LWRpYWxvZ0RyYWcKICA+CiAgICA8ZWwtZm9ybQogICAgICBsYWJlbC13aWR0aD0iMTQwcHgiCiAgICAgIDpydWxlcz0ic2JialJ1bGVzIgogICAgICA6bW9kZWw9InNiYmpGb3JtIgogICAgICByZWY9InNiYmpGb3JtIgogICAgPgogICAgICA8ZWwtY2FyZCBzaGFkb3c9Im5ldmVyIiBjbGFzcz0iYm94LWNvbnQiPgogICAgICAgIDxkaXYgc2xvdD0iaGVhZGVyIiBjbGFzcz0iY2xlYXJmaXgiPgogICAgICAgICAgPHNwYW4+6K6+5aSH6ZqQ5oKjPC9zcGFuPgogICAgICAgIDwvZGl2PgogICAgICAgIDxlbC1yb3cgOmd1dHRlcj0iMTUiIGNsYXNzPSJjb250X3RvcCI+CiAgICAgICAgICA8ZWwtY29sIDpzcGFuPSIxMiI+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gcHJvcD0ic2JseGJtIiBsYWJlbD0i6K6+5aSH57G75Z6LIj4KICAgICAgICAgICAgICA8ZWwtc2VsZWN0CiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K6+5aSH57G75Z6LIgogICAgICAgICAgICAgICAgdi1tb2RlbD0ic2JiakZvcm0uc2JseGJtIgogICAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOjgwJSIKICAgICAgICAgICAgICAgIEBjaGFuZ2U9InNibHhDaGFuZ2VGdW4iCiAgICAgICAgICAgICAgICBmaWx0ZXJhYmxlCiAgICAgICAgICAgICAgICBjbGVhcmFibGUKICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICA8ZWwtb3B0aW9uCiAgICAgICAgICAgICAgICAgIHYtZm9yPSJpdGVtIGluIHNibHhMaXN0IgogICAgICAgICAgICAgICAgICA6a2V5PSJpdGVtLnZhbHVlIgogICAgICAgICAgICAgICAgICA6bGFiZWw9Iml0ZW0ubGFiZWwiCiAgICAgICAgICAgICAgICAgIDp2YWx1ZT0iU3RyaW5nKGl0ZW0udmFsdWUpIgogICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgPC9lbC1vcHRpb24+CiAgICAgICAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgICA8ZWwtY29sIDpzcGFuPSIxMiI+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gcHJvcD0ic2JiaiIgbGFiZWw9IuiuvuWkh+mDqOS7tiI+CiAgICAgICAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICAgICAgICB2LW1vZGVsPSJzYmJqRm9ybS5zYmJqIgogICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeiuvuWkh+mDqOS7tiIKICAgICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogOTIlIgogICAgICAgICAgICAgIC8+CiAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgPC9lbC1yb3c+CgogICAgICAgIDxlbC1yb3cgOmd1dHRlcj0iMTUiIGNsYXNzPSJwdWxsLWxlZnQiPgogICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIHByb3A9InNiYnciIGxhYmVsPSLorr7lpIfpg6jkvY0iPgogICAgICAgICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgICAgICAgdi1tb2RlbD0ic2JiakZvcm0uc2JidyIKICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaXorr7lpIfpg6jkvY0iCiAgICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDkyJSIKICAgICAgICAgICAgICAvPgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgIDwvZWwtY29sPgogICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIHByb3A9InF4ZGoiIGxhYmVsPSLpmpDmgqPnrYnnuqciPgogICAgICAgICAgICAgIDxlbC1zZWxlY3QKICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLpmpDmgqPnrYnnuqciCiAgICAgICAgICAgICAgICB2LW1vZGVsPSJzYmJqRm9ybS5xeGRqIgogICAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOjgwJSIKICAgICAgICAgICAgICAgIGZpbHRlcmFibGUKICAgICAgICAgICAgICAgIGNsZWFyYWJsZQogICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgIDxlbC1vcHRpb24KICAgICAgICAgICAgICAgICAgdi1mb3I9Iml0ZW0gaW4gcXhkakxpc3QiCiAgICAgICAgICAgICAgICAgIDprZXk9Iml0ZW0udmFsdWUiCiAgICAgICAgICAgICAgICAgIDpsYWJlbD0iaXRlbS5sYWJlbCIKICAgICAgICAgICAgICAgICAgOnZhbHVlPSJTdHJpbmcoaXRlbS52YWx1ZSkiCiAgICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICA8L2VsLW9wdGlvbj4KICAgICAgICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICA8L2VsLWNvbD4KICAgICAgICA8L2VsLXJvdz4KICAgICAgICA8ZWwtcm93IDpndXR0ZXI9IjE1IiBjbGFzcz0icHVsbC1sZWZ0Ij4KICAgICAgICAgIDxlbC1jb2wgOnNwYW49IjI0Ij4KICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i6ZqQ5oKj5o+P6L+wOiIgcHJvcD0icXhtcyI+CiAgICAgICAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICAgICAgICB0eXBlPSJ0ZXh0YXJlYSIKICAgICAgICAgICAgICAgIDpyb3dzPSI1IgogICAgICAgICAgICAgICAgdi1tb2RlbD0ic2JiakZvcm0ucXhtcyIKICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaXpmpDmgqPmj4/ov7AiCiAgICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDkyJSIKICAgICAgICAgICAgICAvPgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgIDwvZWwtY29sPgogICAgICAgIDwvZWwtcm93PgogICAgICAgIDxlbC1yb3cgOmd1dHRlcj0iMTUiIGNsYXNzPSJwdWxsLWxlZnQiPgogICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMjQiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLliIbnsbvkvp3mja46IiBwcm9wPSJmbHlqIj4KICAgICAgICAgICAgICA8ZWwtaW5wdXQKICAgICAgICAgICAgICAgIHR5cGU9InRleHRhcmVhIgogICAgICAgICAgICAgICAgOnJvd3M9IjUiCiAgICAgICAgICAgICAgICB2LW1vZGVsPSJzYmJqRm9ybS5mbHlqIgogICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeWIhuexu+S+neaNriIKICAgICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogOTIlIgogICAgICAgICAgICAgIC8+CiAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgPC9lbC1yb3c+CiAgICAgICAgPGVsLXJvdyA6Z3V0dGVyPSIxNSIgY2xhc3M9InB1bGwtbGVmdCI+CiAgICAgICAgICA8ZWwtY29sIDpzcGFuPSIyNCI+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuaKgOacr+WOn+WboDoiIHByb3A9ImpzeXkiPgogICAgICAgICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgICAgICAgdHlwZT0idGV4dGFyZWEiCiAgICAgICAgICAgICAgICA6cm93cz0iNSIKICAgICAgICAgICAgICAgIHYtbW9kZWw9InNiYmpGb3JtLmpzeXkiCiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36L6T5YWl5oqA5pyv5Y6f5ZugIgogICAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOiA5MiUiCiAgICAgICAgICAgICAgLz4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICA8L2VsLWNvbD4KICAgICAgICA8L2VsLXJvdz4KICAgICAgPC9lbC1jYXJkPgogICAgPC9lbC1mb3JtPgogICAgPGRpdiBzbG90PSJmb290ZXIiPgogICAgICA8ZWwtYnV0dG9uIEBjbGljaz0iY2xvc2VGdW4oJ3NiYmonKSIgc2l6ZT0ic21hbGwiPuWPliDmtog8L2VsLWJ1dHRvbj4KICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBzaXplPSJzbWFsbCIgQGNsaWNrPSJzYXZlRm9ybSgnc2JiakZvcm0nKSIKICAgICAgICA+5L+d5a2YPC9lbC1idXR0b24KICAgICAgPgogICAgPC9kaXY+CiAgPC9lbC1kaWFsb2c+CgogIDwhLS0gIOaWsOWinuiuvuWkh+mDqOS9jSAgLS0+CiAgPGVsLWRpYWxvZwogICAgdGl0bGU9IuaWsOWinuiuvuWkh+mDqOS9jSIKICAgIDp2aXNpYmxlLnN5bmM9ImlzU2hvd1NiYnciCiAgICB3aWR0aD0iNTglIgogICAgQGNsb3NlPSJjbG9zZUZ1bignc2JidycpIgogICAgdi1kaWFsb2dEcmFnCiAgPgogICAgPGVsLWZvcm0KICAgICAgbGFiZWwtd2lkdGg9IjE0MHB4IgogICAgICA6cnVsZXM9InNiYndSdWxlcyIKICAgICAgOm1vZGVsPSJzYmJ3Rm9ybSIKICAgICAgcmVmPSJzYmJ3Rm9ybSIKICAgID4KICAgICAgPGVsLWNhcmQgc2hhZG93PSJuZXZlciIgY2xhc3M9ImJveC1jb250Ij4KICAgICAgICA8ZGl2IHNsb3Q9ImhlYWRlciIgY2xhc3M9ImNsZWFyZml4Ij4KICAgICAgICAgIDxzcGFuPuiuvuWkh+makOaCozwvc3Bhbj4KICAgICAgICA8L2Rpdj4KICAgICAgICA8ZWwtcm93IDpndXR0ZXI9IjE1IiBjbGFzcz0iY29udF90b3AiPgogICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIHByb3A9InNibHhibSIgbGFiZWw9IuiuvuWkh+exu+WeiyI+CiAgICAgICAgICAgICAgPGVsLXNlbGVjdAogICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9IuiuvuWkh+exu+WeiyIKICAgICAgICAgICAgICAgIHYtbW9kZWw9InNiYndGb3JtLnNibHhibSIKICAgICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDo4MCUiCiAgICAgICAgICAgICAgICBAY2hhbmdlPSJzYmx4Q2hhbmdlRnVuIgogICAgICAgICAgICAgICAgZmlsdGVyYWJsZQogICAgICAgICAgICAgICAgY2xlYXJhYmxlCiAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgPGVsLW9wdGlvbgogICAgICAgICAgICAgICAgICB2LWZvcj0iaXRlbSBpbiBzYmx4TGlzdCIKICAgICAgICAgICAgICAgICAgOmtleT0iaXRlbS52YWx1ZSIKICAgICAgICAgICAgICAgICAgOmxhYmVsPSJpdGVtLmxhYmVsIgogICAgICAgICAgICAgICAgICA6dmFsdWU9IlN0cmluZyhpdGVtLnZhbHVlKSIKICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgIDwvZWwtb3B0aW9uPgogICAgICAgICAgICAgIDwvZWwtc2VsZWN0PgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgIDwvZWwtY29sPgogICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIHByb3A9InBhcmVudFNiYmoiIGxhYmVsPSLorr7lpIfpg6jku7YiPgogICAgICAgICAgICAgIDxlbC1zZWxlY3QKICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLorr7lpIfpg6jku7YiCiAgICAgICAgICAgICAgICB2LW1vZGVsPSJzYmJ3Rm9ybS5wYXJlbnRTYmJqIgogICAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOjgwJSIKICAgICAgICAgICAgICAgIEBjaGFuZ2U9InNiYmpDaGFuZ2VGdW4iCiAgICAgICAgICAgICAgICBmaWx0ZXJhYmxlCiAgICAgICAgICAgICAgICBjbGVhcmFibGUKICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICA8ZWwtb3B0aW9uCiAgICAgICAgICAgICAgICAgIHYtZm9yPSJpdGVtIGluIHNiYmpMaXN0IgogICAgICAgICAgICAgICAgICA6a2V5PSJpdGVtLnZhbHVlIgogICAgICAgICAgICAgICAgICA6bGFiZWw9Iml0ZW0ubGFiZWwiCiAgICAgICAgICAgICAgICAgIDp2YWx1ZT0iU3RyaW5nKGl0ZW0udmFsdWUpIgogICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgPC9lbC1vcHRpb24+CiAgICAgICAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgPC9lbC1yb3c+CgogICAgICAgIDxlbC1yb3cgOmd1dHRlcj0iMTUiIGNsYXNzPSJwdWxsLWxlZnQiPgogICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIHByb3A9InNiYnciIGxhYmVsPSLorr7lpIfpg6jkvY0iPgogICAgICAgICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgICAgICAgdi1tb2RlbD0ic2Jid0Zvcm0uc2JidyIKICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaXorr7lpIfpg6jkvY0iCiAgICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDkyJSIKICAgICAgICAgICAgICAvPgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgIDwvZWwtY29sPgogICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIHByb3A9InF4ZGoiIGxhYmVsPSLpmpDmgqPnrYnnuqciPgogICAgICAgICAgICAgIDxlbC1zZWxlY3QKICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLpmpDmgqPnrYnnuqciCiAgICAgICAgICAgICAgICB2LW1vZGVsPSJzYmJ3Rm9ybS5xeGRqIgogICAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOjgwJSIKICAgICAgICAgICAgICAgIGZpbHRlcmFibGUKICAgICAgICAgICAgICAgIGNsZWFyYWJsZQogICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgIDxlbC1vcHRpb24KICAgICAgICAgICAgICAgICAgdi1mb3I9Iml0ZW0gaW4gcXhkakxpc3QiCiAgICAgICAgICAgICAgICAgIDprZXk9Iml0ZW0udmFsdWUiCiAgICAgICAgICAgICAgICAgIDpsYWJlbD0iaXRlbS5sYWJlbCIKICAgICAgICAgICAgICAgICAgOnZhbHVlPSJTdHJpbmcoaXRlbS52YWx1ZSkiCiAgICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICA8L2VsLW9wdGlvbj4KICAgICAgICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICA8L2VsLWNvbD4KICAgICAgICA8L2VsLXJvdz4KICAgICAgICA8ZWwtcm93IDpndXR0ZXI9IjE1IiBjbGFzcz0icHVsbC1sZWZ0Ij4KICAgICAgICAgIDxlbC1jb2wgOnNwYW49IjI0Ij4KICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i6ZqQ5oKj5o+P6L+wOiIgcHJvcD0icXhtcyI+CiAgICAgICAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICAgICAgICB0eXBlPSJ0ZXh0YXJlYSIKICAgICAgICAgICAgICAgIDpyb3dzPSI1IgogICAgICAgICAgICAgICAgdi1tb2RlbD0ic2Jid0Zvcm0ucXhtcyIKICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaXpmpDmgqPmj4/ov7AiCiAgICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDkyJSIKICAgICAgICAgICAgICAvPgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgIDwvZWwtY29sPgogICAgICAgIDwvZWwtcm93PgogICAgICAgIDxlbC1yb3cgOmd1dHRlcj0iMTUiIGNsYXNzPSJwdWxsLWxlZnQiPgogICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMjQiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLliIbnsbvkvp3mja46IiBwcm9wPSJmbHlqIj4KICAgICAgICAgICAgICA8ZWwtaW5wdXQKICAgICAgICAgICAgICAgIHR5cGU9InRleHRhcmVhIgogICAgICAgICAgICAgICAgOnJvd3M9IjUiCiAgICAgICAgICAgICAgICB2LW1vZGVsPSJzYmJ3Rm9ybS5mbHlqIgogICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeWIhuexu+S+neaNriIKICAgICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogOTIlIgogICAgICAgICAgICAgIC8+CiAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgPC9lbC1yb3c+CiAgICAgICAgPGVsLXJvdyA6Z3V0dGVyPSIxNSIgY2xhc3M9InB1bGwtbGVmdCI+CiAgICAgICAgICA8ZWwtY29sIDpzcGFuPSIyNCI+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuaKgOacr+WOn+WboDoiIHByb3A9ImpzeXkiPgogICAgICAgICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgICAgICAgdHlwZT0idGV4dGFyZWEiCiAgICAgICAgICAgICAgICA6cm93cz0iNSIKICAgICAgICAgICAgICAgIHYtbW9kZWw9InNiYndGb3JtLmpzeXkiCiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36L6T5YWl5oqA5pyv5Y6f5ZugIgogICAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOiA5MiUiCiAgICAgICAgICAgICAgLz4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICA8L2VsLWNvbD4KICAgICAgICA8L2VsLXJvdz4KICAgICAgPC9lbC1jYXJkPgogICAgPC9lbC1mb3JtPgogICAgPGRpdiBzbG90PSJmb290ZXIiPgogICAgICA8ZWwtYnV0dG9uIEBjbGljaz0iY2xvc2VGdW4oJ3NiYncnKSIgc2l6ZT0ic21hbGwiPuWPliDmtog8L2VsLWJ1dHRvbj4KICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBzaXplPSJzbWFsbCIgQGNsaWNrPSJzYXZlRm9ybSgnc2Jid0Zvcm0nKSIKICAgICAgICA+5L+d5a2YPC9lbC1idXR0b24KICAgICAgPgogICAgPC9kaXY+CiAgPC9lbC1kaWFsb2c+CgogIDwhLS0gIOaWsOWinumakOaCo+aPj+i/sCAgLS0+CiAgPGVsLWRpYWxvZwogICAgdGl0bGU9IuaWsOWinumakOaCo+aPj+i/sCIKICAgIDp2aXNpYmxlLnN5bmM9ImlzU2hvd1F4bXMiCiAgICB3aWR0aD0iNTglIgogICAgQGNsb3NlPSJjbG9zZUZ1bigncXhtcycpIgogICAgdi1kaWFsb2dEcmFnCiAgPgogICAgPGVsLWZvcm0KICAgICAgbGFiZWwtd2lkdGg9IjE0MHB4IgogICAgICA6cnVsZXM9InF4bXNSdWxlcyIKICAgICAgOm1vZGVsPSJxeG1zRm9ybSIKICAgICAgcmVmPSJxeG1zRm9ybSIKICAgID4KICAgICAgPGVsLWNhcmQgc2hhZG93PSJuZXZlciIgY2xhc3M9ImJveC1jb250Ij4KICAgICAgICA8ZGl2IHNsb3Q9ImhlYWRlciIgY2xhc3M9ImNsZWFyZml4Ij4KICAgICAgICAgIDxzcGFuPuiuvuWkh+makOaCozwvc3Bhbj4KICAgICAgICA8L2Rpdj4KICAgICAgICA8ZWwtcm93IDpndXR0ZXI9IjE1IiBjbGFzcz0iY29udF90b3AiPgogICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIHByb3A9InNibHhibSIgbGFiZWw9IuiuvuWkh+exu+WeiyI+CiAgICAgICAgICAgICAgPGVsLXNlbGVjdAogICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9IuiuvuWkh+exu+WeiyIKICAgICAgICAgICAgICAgIHYtbW9kZWw9InF4bXNGb3JtLnNibHhibSIKICAgICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDo4MCUiCiAgICAgICAgICAgICAgICBAY2hhbmdlPSJzYmx4Q2hhbmdlRnVuIgogICAgICAgICAgICAgICAgZmlsdGVyYWJsZQogICAgICAgICAgICAgICAgY2xlYXJhYmxlCiAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgPGVsLW9wdGlvbgogICAgICAgICAgICAgICAgICB2LWZvcj0iaXRlbSBpbiBzYmx4TGlzdCIKICAgICAgICAgICAgICAgICAgOmtleT0iaXRlbS52YWx1ZSIKICAgICAgICAgICAgICAgICAgOmxhYmVsPSJpdGVtLmxhYmVsIgogICAgICAgICAgICAgICAgICA6dmFsdWU9IlN0cmluZyhpdGVtLnZhbHVlKSIKICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgIDwvZWwtb3B0aW9uPgogICAgICAgICAgICAgIDwvZWwtc2VsZWN0PgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgIDwvZWwtY29sPgogICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIHByb3A9InBhcmVudFNiYmoiIGxhYmVsPSLorr7lpIfpg6jku7YiPgogICAgICAgICAgICAgIDxlbC1zZWxlY3QKICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLorr7lpIfpg6jku7YiCiAgICAgICAgICAgICAgICB2LW1vZGVsPSJxeG1zRm9ybS5wYXJlbnRTYmJqIgogICAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOjgwJSIKICAgICAgICAgICAgICAgIEBjaGFuZ2U9InNiYmpDaGFuZ2VGdW4iCiAgICAgICAgICAgICAgICBmaWx0ZXJhYmxlCiAgICAgICAgICAgICAgICBjbGVhcmFibGUKICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICA8ZWwtb3B0aW9uCiAgICAgICAgICAgICAgICAgIHYtZm9yPSJpdGVtIGluIHNiYmpMaXN0IgogICAgICAgICAgICAgICAgICA6a2V5PSJpdGVtLnZhbHVlIgogICAgICAgICAgICAgICAgICA6bGFiZWw9Iml0ZW0ubGFiZWwiCiAgICAgICAgICAgICAgICAgIDp2YWx1ZT0iU3RyaW5nKGl0ZW0udmFsdWUpIgogICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgPC9lbC1vcHRpb24+CiAgICAgICAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgPC9lbC1yb3c+CgogICAgICAgIDxlbC1yb3cgOmd1dHRlcj0iMTUiIGNsYXNzPSJwdWxsLWxlZnQiPgogICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIHByb3A9InBhcmVudFNiYnciIGxhYmVsPSLorr7lpIfpg6jkvY0iPgogICAgICAgICAgICAgIDxlbC1zZWxlY3QKICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLorr7lpIfpg6jkvY0iCiAgICAgICAgICAgICAgICB2LW1vZGVsPSJxeG1zRm9ybS5wYXJlbnRTYmJ3IgogICAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOjgwJSIKICAgICAgICAgICAgICAgIEBjaGFuZ2U9InNiYndDaGFuZ2VGdW4iCiAgICAgICAgICAgICAgICBmaWx0ZXJhYmxlCiAgICAgICAgICAgICAgICBjbGVhcmFibGUKICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICA8ZWwtb3B0aW9uCiAgICAgICAgICAgICAgICAgIHYtZm9yPSJpdGVtIGluIHNiYndMaXN0IgogICAgICAgICAgICAgICAgICA6a2V5PSJpdGVtLnZhbHVlIgogICAgICAgICAgICAgICAgICA6bGFiZWw9Iml0ZW0ubGFiZWwiCiAgICAgICAgICAgICAgICAgIDp2YWx1ZT0iU3RyaW5nKGl0ZW0udmFsdWUpIgogICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgPC9lbC1vcHRpb24+CiAgICAgICAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgICA8ZWwtY29sIDpzcGFuPSIxMiI+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gcHJvcD0icXhkaiIgbGFiZWw9IumakOaCo+etiee6pyI+CiAgICAgICAgICAgICAgPGVsLXNlbGVjdAogICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9IumakOaCo+etiee6pyIKICAgICAgICAgICAgICAgIHYtbW9kZWw9InF4bXNGb3JtLnF4ZGoiCiAgICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6ODAlIgogICAgICAgICAgICAgICAgZmlsdGVyYWJsZQogICAgICAgICAgICAgICAgY2xlYXJhYmxlCiAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgPGVsLW9wdGlvbgogICAgICAgICAgICAgICAgICB2LWZvcj0iaXRlbSBpbiBxeGRqTGlzdCIKICAgICAgICAgICAgICAgICAgOmtleT0iaXRlbS52YWx1ZSIKICAgICAgICAgICAgICAgICAgOmxhYmVsPSJpdGVtLmxhYmVsIgogICAgICAgICAgICAgICAgICA6dmFsdWU9IlN0cmluZyhpdGVtLnZhbHVlKSIKICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgIDwvZWwtb3B0aW9uPgogICAgICAgICAgICAgIDwvZWwtc2VsZWN0PgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgIDwvZWwtY29sPgogICAgICAgIDwvZWwtcm93PgogICAgICAgIDxlbC1yb3cgOmd1dHRlcj0iMTUiIGNsYXNzPSJwdWxsLWxlZnQiPgogICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMjQiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLpmpDmgqPmj4/ov7A6IiBwcm9wPSJxeG1zIj4KICAgICAgICAgICAgICA8ZWwtaW5wdXQKICAgICAgICAgICAgICAgIHR5cGU9InRleHRhcmVhIgogICAgICAgICAgICAgICAgOnJvd3M9IjUiCiAgICAgICAgICAgICAgICB2LW1vZGVsPSJxeG1zRm9ybS5xeG1zIgogICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpemakOaCo+aPj+i/sCIKICAgICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogOTIlIgogICAgICAgICAgICAgIC8+CiAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgPC9lbC1yb3c+CiAgICAgICAgPGVsLXJvdyA6Z3V0dGVyPSIxNSIgY2xhc3M9InB1bGwtbGVmdCI+CiAgICAgICAgICA8ZWwtY29sIDpzcGFuPSIyNCI+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuWIhuexu+S+neaNrjoiIHByb3A9ImZseWoiPgogICAgICAgICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgICAgICAgdHlwZT0idGV4dGFyZWEiCiAgICAgICAgICAgICAgICA6cm93cz0iNSIKICAgICAgICAgICAgICAgIHYtbW9kZWw9InF4bXNGb3JtLmZseWoiCiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36L6T5YWl5YiG57G75L6d5o2uIgogICAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOiA5MiUiCiAgICAgICAgICAgICAgLz4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICA8L2VsLWNvbD4KICAgICAgICA8L2VsLXJvdz4KICAgICAgICA8ZWwtcm93IDpndXR0ZXI9IjE1IiBjbGFzcz0icHVsbC1sZWZ0Ij4KICAgICAgICAgIDxlbC1jb2wgOnNwYW49IjI0Ij4KICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5oqA5pyv5Y6f5ZugOiIgcHJvcD0ianN5eSI+CiAgICAgICAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICAgICAgICB0eXBlPSJ0ZXh0YXJlYSIKICAgICAgICAgICAgICAgIDpyb3dzPSI1IgogICAgICAgICAgICAgICAgdi1tb2RlbD0icXhtc0Zvcm0uanN5eSIKICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaXmioDmnK/ljp/lm6AiCiAgICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDkyJSIKICAgICAgICAgICAgICAvPgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgIDwvZWwtY29sPgogICAgICAgIDwvZWwtcm93PgogICAgICA8L2VsLWNhcmQ+CiAgICA8L2VsLWZvcm0+CiAgICA8ZGl2IHNsb3Q9ImZvb3RlciI+CiAgICAgIDxlbC1idXR0b24gQGNsaWNrPSJjbG9zZUZ1bigncXhtcycpIiBzaXplPSJzbWFsbCI+5Y+WIOa2iDwvZWwtYnV0dG9uPgogICAgICA8ZWwtYnV0dG9uIHR5cGU9InByaW1hcnkiIHNpemU9InNtYWxsIiBAY2xpY2s9InNhdmVGb3JtKCdxeG1zRm9ybScpIgogICAgICAgID7kv53lrZg8L2VsLWJ1dHRvbgogICAgICA+CiAgICA8L2Rpdj4KICA8L2VsLWRpYWxvZz4KCiAgPCEtLSAg5paw5aKe5YiG57G75L6d5o2uICAtLT4KICA8ZWwtZGlhbG9nCiAgICB0aXRsZT0i5paw5aKe5YiG57G75L6d5o2uIgogICAgOnZpc2libGUuc3luYz0iaXNTaG93Rmx5aiIKICAgIHdpZHRoPSI1OCUiCiAgICBAY2xvc2U9ImNsb3NlRnVuKCdmbHlqJykiCiAgICB2LWRpYWxvZ0RyYWcKICA+CiAgICA8ZWwtZm9ybQogICAgICBsYWJlbC13aWR0aD0iMTQwcHgiCiAgICAgIDpydWxlcz0iZmx5alJ1bGVzIgogICAgICA6bW9kZWw9ImZseWpGb3JtIgogICAgICByZWY9ImZseWpGb3JtIgogICAgPgogICAgICA8ZWwtY2FyZCBzaGFkb3c9Im5ldmVyIiBjbGFzcz0iYm94LWNvbnQiPgogICAgICAgIDxkaXYgc2xvdD0iaGVhZGVyIiBjbGFzcz0iY2xlYXJmaXgiPgogICAgICAgICAgPHNwYW4+6K6+5aSH6ZqQ5oKjPC9zcGFuPgogICAgICAgIDwvZGl2PgogICAgICAgIDxlbC1yb3cgOmd1dHRlcj0iMTUiIGNsYXNzPSJjb250X3RvcCI+CiAgICAgICAgICA8ZWwtY29sIDpzcGFuPSIxMiI+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gcHJvcD0ic2JseGJtIiBsYWJlbD0i6K6+5aSH57G75Z6LIj4KICAgICAgICAgICAgICA8ZWwtc2VsZWN0CiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K6+5aSH57G75Z6LIgogICAgICAgICAgICAgICAgdi1tb2RlbD0iZmx5akZvcm0uc2JseGJtIgogICAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOjgwJSIKICAgICAgICAgICAgICAgIEBjaGFuZ2U9InNibHhDaGFuZ2VGdW4iCiAgICAgICAgICAgICAgICBmaWx0ZXJhYmxlCiAgICAgICAgICAgICAgICBjbGVhcmFibGUKICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICA8ZWwtb3B0aW9uCiAgICAgICAgICAgICAgICAgIHYtZm9yPSJpdGVtIGluIHNibHhMaXN0IgogICAgICAgICAgICAgICAgICA6a2V5PSJpdGVtLnZhbHVlIgogICAgICAgICAgICAgICAgICA6bGFiZWw9Iml0ZW0ubGFiZWwiCiAgICAgICAgICAgICAgICAgIDp2YWx1ZT0iU3RyaW5nKGl0ZW0udmFsdWUpIgogICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgPC9lbC1vcHRpb24+CiAgICAgICAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgICA8ZWwtY29sIDpzcGFuPSIxMiI+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gcHJvcD0icGFyZW50U2JiaiIgbGFiZWw9IuiuvuWkh+mDqOS7tiI+CiAgICAgICAgICAgICAgPGVsLXNlbGVjdAogICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9IuiuvuWkh+mDqOS7tiIKICAgICAgICAgICAgICAgIHYtbW9kZWw9ImZseWpGb3JtLnBhcmVudFNiYmoiCiAgICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6ODAlIgogICAgICAgICAgICAgICAgQGNoYW5nZT0ic2JiakNoYW5nZUZ1biIKICAgICAgICAgICAgICAgIGZpbHRlcmFibGUKICAgICAgICAgICAgICAgIGNsZWFyYWJsZQogICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgIDxlbC1vcHRpb24KICAgICAgICAgICAgICAgICAgdi1mb3I9Iml0ZW0gaW4gc2Jiakxpc3QiCiAgICAgICAgICAgICAgICAgIDprZXk9Iml0ZW0udmFsdWUiCiAgICAgICAgICAgICAgICAgIDpsYWJlbD0iaXRlbS5sYWJlbCIKICAgICAgICAgICAgICAgICAgOnZhbHVlPSJTdHJpbmcoaXRlbS52YWx1ZSkiCiAgICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICA8L2VsLW9wdGlvbj4KICAgICAgICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICA8L2VsLWNvbD4KICAgICAgICA8L2VsLXJvdz4KCiAgICAgICAgPGVsLXJvdyA6Z3V0dGVyPSIxNSIgY2xhc3M9InB1bGwtbGVmdCI+CiAgICAgICAgICA8ZWwtY29sIDpzcGFuPSIxMiI+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gcHJvcD0icGFyZW50U2JidyIgbGFiZWw9IuiuvuWkh+mDqOS9jSI+CiAgICAgICAgICAgICAgPGVsLXNlbGVjdAogICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9IuiuvuWkh+mDqOS9jSIKICAgICAgICAgICAgICAgIHYtbW9kZWw9ImZseWpGb3JtLnBhcmVudFNiYnciCiAgICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6ODAlIgogICAgICAgICAgICAgICAgQGNoYW5nZT0ic2Jid0NoYW5nZUZ1biIKICAgICAgICAgICAgICAgIGZpbHRlcmFibGUKICAgICAgICAgICAgICAgIGNsZWFyYWJsZQogICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgIDxlbC1vcHRpb24KICAgICAgICAgICAgICAgICAgdi1mb3I9Iml0ZW0gaW4gc2Jid0xpc3QiCiAgICAgICAgICAgICAgICAgIDprZXk9Iml0ZW0udmFsdWUiCiAgICAgICAgICAgICAgICAgIDpsYWJlbD0iaXRlbS5sYWJlbCIKICAgICAgICAgICAgICAgICAgOnZhbHVlPSJTdHJpbmcoaXRlbS52YWx1ZSkiCiAgICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICA8L2VsLW9wdGlvbj4KICAgICAgICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICA8L2VsLWNvbD4KICAgICAgICAgIDxlbC1jb2wgOnNwYW49IjEyIj4KICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBwcm9wPSJxeGRqIiBsYWJlbD0i6ZqQ5oKj562J57qnIj4KICAgICAgICAgICAgICA8ZWwtc2VsZWN0CiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6ZqQ5oKj562J57qnIgogICAgICAgICAgICAgICAgdi1tb2RlbD0iZmx5akZvcm0ucXhkaiIKICAgICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDo4MCUiCiAgICAgICAgICAgICAgICBmaWx0ZXJhYmxlCiAgICAgICAgICAgICAgICBjbGVhcmFibGUKICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICA8ZWwtb3B0aW9uCiAgICAgICAgICAgICAgICAgIHYtZm9yPSJpdGVtIGluIHF4ZGpMaXN0IgogICAgICAgICAgICAgICAgICA6a2V5PSJpdGVtLnZhbHVlIgogICAgICAgICAgICAgICAgICA6bGFiZWw9Iml0ZW0ubGFiZWwiCiAgICAgICAgICAgICAgICAgIDp2YWx1ZT0iU3RyaW5nKGl0ZW0udmFsdWUpIgogICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgPC9lbC1vcHRpb24+CiAgICAgICAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgPC9lbC1yb3c+CiAgICAgICAgPGVsLXJvdyA6Z3V0dGVyPSIxNSIgY2xhc3M9InB1bGwtbGVmdCI+CiAgICAgICAgICA8ZWwtY29sIDpzcGFuPSIyNCI+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IumakOaCo+aPj+i/sDoiIHByb3A9InBhcmVudFF4bXMiPgogICAgICAgICAgICAgIDxlbC1zZWxlY3QKICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLpmpDmgqPmj4/ov7AiCiAgICAgICAgICAgICAgICB2LW1vZGVsPSJmbHlqRm9ybS5wYXJlbnRReG1zIgogICAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOjgwJSIKICAgICAgICAgICAgICAgIEBjaGFuZ2U9InF4bXNDaGFuZ2VGdW4iCiAgICAgICAgICAgICAgICBmaWx0ZXJhYmxlCiAgICAgICAgICAgICAgICBjbGVhcmFibGUKICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICA8ZWwtb3B0aW9uCiAgICAgICAgICAgICAgICAgIHYtZm9yPSJpdGVtIGluIHF4bXNMaXN0IgogICAgICAgICAgICAgICAgICA6a2V5PSJpdGVtLnZhbHVlIgogICAgICAgICAgICAgICAgICA6bGFiZWw9Iml0ZW0ubGFiZWwiCiAgICAgICAgICAgICAgICAgIDp2YWx1ZT0iU3RyaW5nKGl0ZW0udmFsdWUpIgogICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgPC9lbC1vcHRpb24+CiAgICAgICAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgPC9lbC1yb3c+CiAgICAgICAgPGVsLXJvdyA6Z3V0dGVyPSIxNSIgY2xhc3M9InB1bGwtbGVmdCI+CiAgICAgICAgICA8ZWwtY29sIDpzcGFuPSIyNCI+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuWIhuexu+S+neaNrjoiIHByb3A9ImZseWoiPgogICAgICAgICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgICAgICAgdHlwZT0idGV4dGFyZWEiCiAgICAgICAgICAgICAgICA6cm93cz0iNSIKICAgICAgICAgICAgICAgIHYtbW9kZWw9ImZseWpGb3JtLmZseWoiCiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36L6T5YWl5YiG57G75L6d5o2uIgogICAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOiA5MiUiCiAgICAgICAgICAgICAgLz4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICA8L2VsLWNvbD4KICAgICAgICA8L2VsLXJvdz4KICAgICAgICA8ZWwtcm93IDpndXR0ZXI9IjE1IiBjbGFzcz0icHVsbC1sZWZ0Ij4KICAgICAgICAgIDxlbC1jb2wgOnNwYW49IjI0Ij4KICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5oqA5pyv5Y6f5ZugOiIgcHJvcD0ianN5eSI+CiAgICAgICAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICAgICAgICB0eXBlPSJ0ZXh0YXJlYSIKICAgICAgICAgICAgICAgIDpyb3dzPSI1IgogICAgICAgICAgICAgICAgdi1tb2RlbD0iZmx5akZvcm0uanN5eSIKICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaXmioDmnK/ljp/lm6AiCiAgICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDkyJSIKICAgICAgICAgICAgICAvPgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgIDwvZWwtY29sPgogICAgICAgIDwvZWwtcm93PgogICAgICA8L2VsLWNhcmQ+CiAgICA8L2VsLWZvcm0+CiAgICA8ZGl2IHNsb3Q9ImZvb3RlciI+CiAgICAgIDxlbC1idXR0b24gQGNsaWNrPSJjbG9zZUZ1bignZmx5aicpIiBzaXplPSJzbWFsbCI+5Y+WIOa2iDwvZWwtYnV0dG9uPgogICAgICA8ZWwtYnV0dG9uCiAgICAgICAgdi1pZj0iYWRkRmx5aiIKICAgICAgICB0eXBlPSJwcmltYXJ5IgogICAgICAgIHNpemU9InNtYWxsIgogICAgICAgIEBjbGljaz0ic2F2ZUZvcm0oJ2ZseWpGb3JtJykiCiAgICAgICAgPuS/neWtmDwvZWwtYnV0dG9uCiAgICAgID4KICAgICAgPGVsLWJ1dHRvbgogICAgICAgIHYtaWY9IiFhZGRGbHlqIgogICAgICAgIHR5cGU9InByaW1hcnkiCiAgICAgICAgc2l6ZT0ic21hbGwiCiAgICAgICAgQGNsaWNrPSJzYXZlRm9ybSgnZmx5akZvcm0nKSIKICAgICAgICA+5L+d5a2YPC9lbC1idXR0b24KICAgICAgPgogICAgPC9kaXY+CiAgPC9lbC1kaWFsb2c+CgogIDwhLS0gIOiuvuWkh+makOaCo+afpeeciyAgLS0+CiAgPGVsLWRpYWxvZwogICAgdGl0bGU9IuiuvuWkh+makOaCo+afpeeciyIKICAgIDp2aXNpYmxlLnN5bmM9ImlzU2hvd0RldGFpbCIKICAgIHYtaWY9ImlzU2hvd0RldGFpbCIKICAgIHdpZHRoPSI1OCUiCiAgICBAY2xvc2U9ImNsb3NlRnVuKCd2aWV3JykiCiAgICB2LWRpYWxvZ0RyYWcKICA+CiAgICA8ZWwtZm9ybSBsYWJlbC13aWR0aD0iMTQwcHgiIDptb2RlbD0idmlld0Zvcm0iIHJlZj0idmlld0Zvcm0iPgogICAgICA8ZWwtY2FyZCBzaGFkb3c9Im5ldmVyIiBjbGFzcz0iYm94LWNvbnQiPgogICAgICAgIDxkaXYgc2xvdD0iaGVhZGVyIiBjbGFzcz0iY2xlYXJmaXgiPgogICAgICAgICAgPHNwYW4+6K6+5aSH6ZqQ5oKjPC9zcGFuPgogICAgICAgIDwvZGl2PgogICAgICAgIDxlbC1yb3cgOmd1dHRlcj0iMTUiIGNsYXNzPSJjb250X3RvcCI+CiAgICAgICAgICA8ZWwtY29sIDpzcGFuPSIxMiI+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gcHJvcD0ic2JseCIgbGFiZWw9IuiuvuWkh+exu+WeiyI+CiAgICAgICAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICAgICAgICB2LW1vZGVsPSJ2aWV3Rm9ybS5zYmx4IgogICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeiuvuWkh+exu+WeiyIKICAgICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogOTIlIgogICAgICAgICAgICAgICAgOmRpc2FibGVkPSJ0cnVlIgogICAgICAgICAgICAgIC8+CiAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgICA8ZWwtY29sIDpzcGFuPSIxMiI+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gcHJvcD0ic2JiaiIgbGFiZWw9IuiuvuWkh+mDqOS7tiI+CiAgICAgICAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICAgICAgICB2LW1vZGVsPSJ2aWV3Rm9ybS5zYmJqIgogICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeiuvuWkh+mDqOS7tiIKICAgICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogOTIlIgogICAgICAgICAgICAgICAgOmRpc2FibGVkPSJ0cnVlIgogICAgICAgICAgICAgIC8+CiAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgPC9lbC1yb3c+CgogICAgICAgIDxlbC1yb3cgOmd1dHRlcj0iMTUiIGNsYXNzPSJwdWxsLWxlZnQiPgogICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIHByb3A9InNiYnciIGxhYmVsPSLorr7lpIfpg6jkvY0iPgogICAgICAgICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgICAgICAgdi1tb2RlbD0idmlld0Zvcm0uc2JidyIKICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaXorr7lpIfpg6jkvY0iCiAgICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDkyJSIKICAgICAgICAgICAgICAgIDpkaXNhYmxlZD0idHJ1ZSIKICAgICAgICAgICAgICAvPgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgIDwvZWwtY29sPgogICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIHByb3A9InF4ZGoiIGxhYmVsPSLpmpDmgqPnrYnnuqciPgogICAgICAgICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgICAgICAgdi1tb2RlbD0idmlld0Zvcm0ucXhkaiIKICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaXpmpDmgqPnrYnnuqciCiAgICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDkyJSIKICAgICAgICAgICAgICAgIDpkaXNhYmxlZD0idHJ1ZSIKICAgICAgICAgICAgICAvPgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgIDwvZWwtY29sPgogICAgICAgIDwvZWwtcm93PgogICAgICAgIDxlbC1yb3cgOmd1dHRlcj0iMTUiIGNsYXNzPSJwdWxsLWxlZnQiPgogICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMjQiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLpmpDmgqPmj4/ov7A6IiBwcm9wPSJxeG1zIj4KICAgICAgICAgICAgICA8ZWwtaW5wdXQKICAgICAgICAgICAgICAgIHR5cGU9InRleHRhcmVhIgogICAgICAgICAgICAgICAgOnJvd3M9IjUiCiAgICAgICAgICAgICAgICB2LW1vZGVsPSJ2aWV3Rm9ybS5xeG1zIgogICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpemakOaCo+aPj+i/sCIKICAgICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogOTIlIgogICAgICAgICAgICAgICAgOmRpc2FibGVkPSJ0cnVlIgogICAgICAgICAgICAgIC8+CiAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgPC9lbC1yb3c+CiAgICAgICAgPGVsLXJvdyA6Z3V0dGVyPSIxNSIgY2xhc3M9InB1bGwtbGVmdCI+CiAgICAgICAgICA8ZWwtY29sIDpzcGFuPSIyNCI+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuWIhuexu+S+neaNrjoiIHByb3A9ImZseWoiPgogICAgICAgICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgICAgICAgdHlwZT0idGV4dGFyZWEiCiAgICAgICAgICAgICAgICA6cm93cz0iNSIKICAgICAgICAgICAgICAgIHYtbW9kZWw9InZpZXdGb3JtLmZseWoiCiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36L6T5YWl5YiG57G75L6d5o2uIgogICAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOiA5MiUiCiAgICAgICAgICAgICAgICA6ZGlzYWJsZWQ9InRydWUiCiAgICAgICAgICAgICAgLz4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICA8L2VsLWNvbD4KICAgICAgICA8L2VsLXJvdz4KICAgICAgICA8ZWwtcm93IDpndXR0ZXI9IjE1IiBjbGFzcz0icHVsbC1sZWZ0Ij4KICAgICAgICAgIDxlbC1jb2wgOnNwYW49IjI0Ij4KICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5oqA5pyv5Y6f5ZugOiIgcHJvcD0ianN5eSI+CiAgICAgICAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICAgICAgICB0eXBlPSJ0ZXh0YXJlYSIKICAgICAgICAgICAgICAgIDpyb3dzPSI1IgogICAgICAgICAgICAgICAgdi1tb2RlbD0idmlld0Zvcm0uanN5eSIKICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaXmioDmnK/ljp/lm6AiCiAgICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDkyJSIKICAgICAgICAgICAgICAgIDpkaXNhYmxlZD0idHJ1ZSIKICAgICAgICAgICAgICAvPgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgIDwvZWwtY29sPgogICAgICAgIDwvZWwtcm93PgogICAgICA8L2VsLWNhcmQ+CiAgICA8L2VsLWZvcm0+CiAgPC9lbC1kaWFsb2c+CjwvZGl2Pgo="}, null]}