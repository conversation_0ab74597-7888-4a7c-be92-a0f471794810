{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\dwzygl\\sdsbgl\\sdgtjc.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\dwzygl\\sdsbgl\\sdgtjc.js", "mtime": 1706897314647}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0TGlzdEpjID0gZ2V0TGlzdEpjOwpleHBvcnRzLnNhdmVPclVwZGF0ZUpjID0gc2F2ZU9yVXBkYXRlSmM7CmV4cG9ydHMucmVtb3ZlSmMgPSByZW1vdmVKYzsKCnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwoKdmFyIGJhc2VVcmwgPSAiL21hbmFnZXItYXBpIjsgLy8g5p+l6K+iCgpmdW5jdGlvbiBnZXRMaXN0SmMocGFyYW1zKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcvZHd6eVNkZ3RqYy9wYWdlJywgcGFyYW1zLCAxKTsKfSAvLyDmt7vliqDmiJbkv67mlLkKCgpmdW5jdGlvbiBzYXZlT3JVcGRhdGVKYyhwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy9kd3p5U2RndGpjL3NhdmVPclVwZGF0ZScsIHBhcmFtcywgMSk7Cn0gLy8g5Yig6ZmkCgoKZnVuY3Rpb24gcmVtb3ZlSmMocGFyYW1zKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcvZHd6eVNkZ3RqYy9yZW1vdmUnLCBwYXJhbXMsIDEpOwp9"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagang<PERSON>ilfield/dwzygl/sdsbgl/sdgtjc.js"], "names": ["baseUrl", "getListJc", "params", "api", "requestPost", "saveOrUpdateJc", "removeJc"], "mappings": ";;;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,cAAhB,C,CAEA;;AACO,SAASC,SAAT,CAAmBC,MAAnB,EAA2B;AAChC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,kBAAxB,EAA2CE,MAA3C,EAAkD,CAAlD,CAAP;AACD,C,CAED;;;AACO,SAASG,cAAT,CAAwBH,MAAxB,EAAgC;AACrC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,0BAAxB,EAAmDE,MAAnD,EAA0D,CAA1D,CAAP;AACD,C,CACD;;;AACO,SAASI,QAAT,CAAkBJ,MAAlB,EAA0B;AAC/B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,oBAAxB,EAA6CE,MAA7C,EAAoD,CAApD,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\nconst baseUrl = \"/manager-api\";\n\n// 查询\nexport function getListJc(params) {\n  return api.requestPost(baseUrl+'/dwzySdgtjc/page',params,1)\n}\n\n// 添加或修改\nexport function saveOrUpdateJc(params) {\n  return api.requestPost(baseUrl+'/dwzySdgtjc/saveOrUpdate',params,1)\n}\n// 删除\nexport function removeJc(params) {\n  return api.requestPost(baseUrl+'/dwzySdgtjc/remove',params,1)\n}\n\n"]}]}