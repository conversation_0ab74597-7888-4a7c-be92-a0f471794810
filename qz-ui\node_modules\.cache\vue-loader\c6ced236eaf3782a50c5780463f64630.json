{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\gfdzcz\\dzczml.vue?vue&type=template&id=4a3c747e&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\gfdzcz\\dzczml.vue", "mtime": 1751374208669}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}