{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\bddzcz\\dzczml.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\bddzcz\\dzczml.vue", "mtime": 1748606190948}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsKaW1wb3J0IGFwaSBmcm9tICJAL3V0aWxzL3JlcXVlc3QiOwppbXBvcnQgewogIGV4cG9ydEV4Y2VsLAogIGdldEJkelNlbGVjdExpc3QsCiAgZ2V0TGlzdCwKICByZW1vdmUsCiAgc2F2ZU9yVXBkYXRlLAogIHNhdmVPclVwZGF0ZUN6cCwKICBleHBvcnRXb3JkQnlzZWxlY3Rpb24sCiAgZXhwb3J0V29yZEJ5cGFyYW1zCn0gZnJvbSAiQC9hcGkveXhnbC9iZHl4Z2wvYmRkemN6bWwiOwppbXBvcnQgeyBnZXREeHBrTGlzdCwgZ2V0TGlzdHMgfSBmcm9tICJAL2FwaS9iemdsL2R4Y3pwIjsKaW1wb3J0IHsgZ2V0TGlzdExzcCwgZ2V0THNwa0xpc3QsIHVwZGF0ZUJ5SWQgfSBmcm9tICJAL2FwaS95eGdsL2JkeXhnbC9iZGR6Y3pwIjsKaW1wb3J0IEVsZWN0cm9uaWNBdXRoRGlhbG9nIGZyb20gImNvbS9FbGVjdHJvbmljQXV0aERpYWxvZyI7CmltcG9ydCBDb21wVGFibGUgZnJvbSAiY29tL0NvbXBUYWJsZSI7CmltcG9ydCBFbEZpbHRlciBmcm9tICJjb20vRWxGaWx0ZXIiOwppbXBvcnQgeyBnZXRVc2VycyB9IGZyb20gIkAvYXBpL3N5c3RlbS91c2VyZ3JvdXAiOwppbXBvcnQgYWN0aXZpdGkgZnJvbSAiY29tL2FjdGl2aXRpX2N6cCI7CmltcG9ydCB7IGdldFVVSUQgfSBmcm9tICJAL3V0aWxzL3J1b3lpIjsKaW1wb3J0IHsgZ2V0RmdzT3B0aW9ucyB9IGZyb20gIkAvYXBpL3l4Z2wvYmR5eGdsL3piZ2wiOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJkemN6bWwiLAogIGNvbXBvbmVudHM6IHsgRWxlY3Ryb25pY0F1dGhEaWFsb2csIENvbXBUYWJsZSwgRWxGaWx0ZXIsIGFjdGl2aXRpIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGhhc1N1cGVyUm9sZTp0aGlzLiRzdG9yZS5nZXR0ZXJzLmhhc1N1cGVyUm9sZSwKICAgICAgLy9sb2FkaW5nOmZhbHNlLAogICAgICAvL+W3peS9nOa1geS8oOWFpeWPguaVsAogICAgICBwcm9jZXNzRGF0YTogewogICAgICAgIHByb2Nlc3NEZWZpbml0aW9uS2V5OiAiY3pwc2giLAogICAgICAgIGJ1c2luZXNzS2V5OiAiIiwKICAgICAgICBidXNpbmVzc1R5cGU6ICLlgJLpl7jmk43kvZznpagiLAogICAgICAgIHZhcmlhYmxlczoge30sCiAgICAgICAgZGVmYXVsdEZyb206IHRydWUsCiAgICAgICAgbmV4dFVzZXI6ICIiLAogICAgICAgIHByb2Nlc3NUeXBlOiAiY29tcGxldGUiCiAgICAgIH0sCiAgICAgIGlzU2hvdzogZmFsc2UsCiAgICAgIC8v5by55Ye65qGG5qCH6aKYCiAgICAgIGFjdGl2aXRpT3B0aW9uOiB7IHRpdGxlOiAi5LiK5oqlIiB9LAogICAgICBjenBSdWxlczogewogICAgICAgIC8vIHhscjogWwogICAgICAgIC8vICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn5LiN6IO95Li656m6JywgdHJpZ2dlcjogJ2JsdXInfQogICAgICAgIC8vIF0sCiAgICAgIH0sCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgYmR6bWM6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5LiN6IO95Li656m6IiwgdHJpZ2dlcjogInNlbGVjdCIgfV0sCiAgICAgICAgZmdzOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJzZWxlY3QiIH1dLAogICAgICAgIGN6cnc6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH1dCiAgICAgIH0sCiAgICAgIHJ1bGVzMjogewogICAgICAgIGtzc2o6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH1dLAogICAgICAgIGpzc2o6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5LiN6IO95Li656m6IiwgdHJpZ2dlcjogInNlbGVjdCIgfV0sCiAgICAgICAgY3pyOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9XSwKICAgICAgICBqaHI6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH1dLAogICAgICAgIHhscjogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfV0sCiAgICAgICAgY3p4czogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfV0sCiAgICAgICAgeXp4Y3p4czogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfV0sCiAgICAgICAgd3p4Y3p4czogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfV0sCiAgICAgICAgYnpzcHI6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH1dLAogICAgICAgIGN6cnc6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH1dCiAgICAgIH0sCiAgICAgIGN1cnJlbnRVc2VyOiB0aGlzLiRzdG9yZS5nZXR0ZXJzLm5hbWUsCiAgICAgIHlsOiBmYWxzZSwKICAgICAgcmVwbGFjZURhdGE6IHt9LAogICAgICB0aXRsZXlsOiAiIiwKICAgICAgLy/lm77niYflnLDlnYB1cmwKICAgICAgZGlhbG9nSW1hZ2VVcmw6ICIiLAogICAgICAvL+WxleekuuWbvueJh2RpYWxvZ+aOp+WItgogICAgICBpbWdEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgLy/kuIrkvKDlm77niYfml7bnmoTmkLrluKbnmoTlhbbku5blj4LmlbAKICAgICAgdXBsb2FkSW1nRGF0YTogewogICAgICAgIGJ1c2luZXNzSWQ6ICIiIC8v5pC65bim55qE6KGo5Y2V5Li76ZSuaWQKICAgICAgfSwKICAgICAgLy/kuIrkvKDlm77niYfml7bnmoTor7fmsYLlpLQKICAgICAgaGVhZGVyOiB7fSwKICAgICAgLy/lm77niYdsaXN0CiAgICAgIGltZ0xpc3Q6IFtdLAogICAgICBpc1Nob3dTeDogZmFsc2UsCiAgICAgIGJkekxpc3Q6IFtdLAogICAgICAvL+W8ueWHuuahhuWGheaWsOWinuaXtuS4i+aLieahhuaJgOWxnuS9jee9ruaVsOaNrgogICAgICB3ekRhdGFMaXN0T3B0aW9uczogW10sCiAgICAgIGlzU2hvd0R4cDogZmFsc2UsCiAgICAgIGlzU2hvd0xzcDogZmFsc2UsCiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICBpc0Rpc2FibGVkQ3pwOiBmYWxzZSwKICAgICAgaXNEeHBTaG93RGV0YWlsczogZmFsc2UsCiAgICAgIGlzTHNwU2hvd0RldGFpbHM6IGZhbHNlLAogICAgICAvL+aTjeS9nOelqOW8ueahhuaYr+WQpuaYvuekugogICAgICBpc0N6cFNob3dEZXRhaWxzOiBmYWxzZSwKICAgICAgaXNCanBTaG93RGV0YWlsczogZmFsc2UsCiAgICAgIC8vIOWkmumAieahhumAieS4reeahGlkCiAgICAgIGlkczogW10sCiAgICAgIHNlbGVjdERhdGE6IFtdLAogICAgICAvL+WAkumXuOaTjeS9nOelqOWRveS7pAogICAgICBwYXJhbXM6IHsKICAgICAgICAvL+WPmOeUtQogICAgICAgIGx4OiAyCiAgICAgIH0sCiAgICAgIC8v5YW45Z6L56Wo5p+l6K+i5p2h5Lu2CiAgICAgIHF1ZXJ5UGFyYW1zOiB7fSwKICAgICAgLy/ljoblj7Lnpajmn6Xor6LmnaHku7YKICAgICAgcXVlcnlQYXJhbXNMc3A6IHsKICAgICAgICBzdGF0dXM6ICI0IiwKICAgICAgICBseDogMgogICAgICB9LAogICAgICAvL+W8ueWHuuahhuS4reihqOagvOaVsOaNrgogICAgICBwcm9wVGFibGVEYXRhOiB7CiAgICAgICAgc2VsOiBudWxsLCAvLyDpgInkuK3ooYwKICAgICAgICBjb2xGaXJzdDogW10KICAgICAgfSwKICAgICAgLy/lvLnlh7rmoYbkuK3ooajmoLzmlbDmja4KICAgICAgcHJvcFRhYmxlRGF0YUR4cE14OiB7CiAgICAgICAgc2VsOiBudWxsLCAvLyDpgInkuK3ooYwKICAgICAgICBjb2xGaXJzdDogW10KICAgICAgfSwKICAgICAgLy/lvLnlh7rmoYbkuK3ooajmoLzmlbDmja4KICAgICAgcHJvcFRhYmxlRGF0YUR4cDogewogICAgICAgIHBhZ2VyOiB7CiAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgdG90YWw6IDEsCiAgICAgICAgICBzaXplczogWzEwLCAyMCwgNTAsIDEwMF0KICAgICAgICB9LAogICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgdGFibGVIZWFkZXI6IFsKICAgICAgICAgIHsgbGFiZWw6ICLlj5jnlLXnq5kiLCBwcm9wOiAic2JtY21zIiwgbWluV2lkdGg6ICIyMDAiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5YW45Z6L5pON5L2c56Wo5ZCN56ewIiwgcHJvcDogImR4cG1jIiwgbWluV2lkdGg6ICIyMDAiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5pON5L2c5Lu75YqhIiwgcHJvcDogImN6cnciLCBtaW5XaWR0aDogIjIwMCIgfQogICAgICAgIF0sCiAgICAgICAgb3B0aW9uOiB7IGNoZWNrQm94OiBmYWxzZSwgc2VyaWFsTnVtYmVyOiB0cnVlIH0sCiAgICAgICAgc2VsOiBudWxsIC8vIOmAieS4reihjAogICAgICB9LAogICAgICAvL+W8ueWHuuahhuS4reihqOagvOaVsOaNrgogICAgICBwcm9wVGFibGVEYXRhTHNwTXg6IHsKICAgICAgICBzZWw6IG51bGwsIC8vIOmAieS4reihjAogICAgICAgIGNvbEZpcnN0OiBbXQogICAgICB9LAogICAgICAvL+W8ueWHuuahhuS4reihqOagvOaVsOaNrgogICAgICBwcm9wVGFibGVEYXRhTHNwOiB7CiAgICAgICAgc2VsOiBudWxsLCAvLyDpgInkuK3ooYwKICAgICAgICBjb2xGaXJzdDogW10KICAgICAgfSwKICAgICAgLy9mb3Jt6KGo5Y2VCiAgICAgIGZvcm06IHsKICAgICAgICBiaDogIiIsCiAgICAgICAgYmR6bWM6ICIiLAogICAgICAgIHR6c2o6ICIiLAogICAgICAgIHR6cjogIiIsCiAgICAgICAgeGxzajogIiIsCiAgICAgICAgeGxyOiAiIiwKICAgICAgICBjenJ3OiAiIiwKICAgICAgICBqbHI6ICIiLAogICAgICAgIGhsc2o6ICIiLAogICAgICAgIHN0YXR1czogIiIKICAgICAgfSwKICAgICAgLy8g5pON5L2c56WoCiAgICAgIGZvcm1DenA6IHsKICAgICAgICBiaDogIiIsCiAgICAgICAgYmR6bWM6ICIiLAogICAgICAgIGtzc2o6ICIiLAogICAgICAgIGpzc2o6ICIiLAogICAgICAgIGN6cnc6ICIiLAogICAgICAgIGN6cjogIiIsCiAgICAgICAgamhyOiAiIiwKICAgICAgICB4bHI6ICIiLAogICAgICAgIHNwcjogIiIsCiAgICAgICAgbHg6IDIsIC8v5Y+Y55S1CiAgICAgICAgY29sRmlyc3Q6IFtdLAogICAgICAgIHN0YXR1czogIiIKICAgICAgfSwKICAgICAgLy8g5Yqe57uT56WoCiAgICAgIGZvcm1CanA6IHsKICAgICAgICBiaDogIiIsCiAgICAgICAgYmR6bWM6ICIiLAogICAgICAgIGtzc2o6ICIiLAogICAgICAgIGpzc2o6ICIiLAogICAgICAgIGN6cnc6ICIiLAogICAgICAgIGN6cjogIiIsCiAgICAgICAgamhyOiAiIiwKICAgICAgICB4bHI6ICIiLAogICAgICAgIHNwcjogIiIsCiAgICAgICAgbHg6IDIsIC8v5Y+Y55S1CiAgICAgICAgY29sRmlyc3Q6IFtdLAogICAgICAgIHN0YXR1czogIiIKICAgICAgfSwKCiAgICAgIC8v5omA5bGe5YiG5YWs5Y+45LiL5ouJ5qGG5pWw5o2uCiAgICAgIHNzZ3NPcHRpb25zRGF0YUxpc3Q6IFtdLAoKICAgICAgLy/or6bmg4XlvLnmoYbmmK/lkKbmmL7npLoKICAgICAgaXNTaG93RGV0YWlsczogZmFsc2UsCiAgICAgIC8v5piv5ZCm56aB55SoCiAgICAgIGlzRGlzYWJsZWQ6IGZhbHNlLAogICAgICAvL+agh+mimAogICAgICB0aXRsZTogIiIsCiAgICAgIGZpbHRlckluZm86IHsKICAgICAgICBkYXRhOiB7CiAgICAgICAgICBiaDogIiIsCiAgICAgICAgICBiZHptYzogIiIsCiAgICAgICAgICB0enNqQXJyOiAiIiwKICAgICAgICAgIHR6cjogIiIsCiAgICAgICAgICB4bHNqQXJyOiAiIiwKICAgICAgICAgIHhscjogIiIsCiAgICAgICAgICBjenJ3OiAiIiwKICAgICAgICAgIGpscjogIiIsCiAgICAgICAgICBobHNqQXJyOiAiIgogICAgICAgIH0sIC8v5p+l6K+i5p2h5Lu2CiAgICAgICAgZmllbGRMaXN0OiBbCiAgICAgICAgICAvLyB7CiAgICAgICAgICAvLyAgIGxhYmVsOiAn5LukJywKICAgICAgICAgIC8vICAgdHlwZTogJ3N3aXRjaCcsCiAgICAgICAgICAvLyAgIHZhbHVlOiAneWJsT3Jac2wnLAogICAgICAgICAgLy8gICB0ZXh0U3RhcnQ6J+mihOWkh+S7pCcsCiAgICAgICAgICAvLyAgIHRleHRFbmQ6J+ato+W8j+S7pCcKICAgICAgICAgIC8vIH0sCiAgICAgICAgICAvLyB7bGFiZWw6ICfnvJblj7cnLCB2YWx1ZTogJ2JoJywgdHlwZTogJ2lucHV0JywgY2xlYXJhYmxlOiB0cnVlfSwKICAgICAgICAgIC8vIC8qeyBsYWJlbDogJ+eKtuaAgScsIHZhbHVlOiAnc3RhdHVzJywgdHlwZTogJ3NlbGVjdCcsIGNsZWFyYWJsZTogdHJ1ZSB9LCovCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi5YiG5YWs5Y+4IiwKICAgICAgICAgICAgdmFsdWU6ICJmZ3MiLAogICAgICAgICAgICB0eXBlOiAic2VsZWN0IiwKICAgICAgICAgICAgb3B0aW9uczogW10sCiAgICAgICAgICAgIGNsZWFyYWJsZTogdHJ1ZQogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLlj5jnlLXnq5kiLAogICAgICAgICAgICB2YWx1ZTogImJkem1jIiwKICAgICAgICAgICAgdHlwZTogInNlbGVjdCIsCiAgICAgICAgICAgIG9wdGlvbnM6IFtdLAogICAgICAgICAgICBjbGVhcmFibGU6IHRydWUsCiAgICAgICAgICAgIGZpbHRlcmFibGU6IHRydWUKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi6YCa55+l5pe26Ze0IiwKICAgICAgICAgICAgdmFsdWU6ICJ0enNqQXJyIiwKICAgICAgICAgICAgdHlwZTogImRhdGUiLAogICAgICAgICAgICBkYXRlVHlwZTogImRhdGVyYW5nZSIsCiAgICAgICAgICAgIGZvcm1hdDogInl5eXktTU0tZGQiCiAgICAgICAgICB9LAogICAgICAgICAgeyBsYWJlbDogIumAmuefpeS6uiIsIHZhbHVlOiAidHpyIiwgdHlwZTogImlucHV0IiwgY2xlYXJhYmxlOiB0cnVlIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi5LiL5Luk5pe26Ze0IiwKICAgICAgICAgICAgdmFsdWU6ICJ4bHNqQXJyIiwKICAgICAgICAgICAgdHlwZTogImRhdGUiLAogICAgICAgICAgICBkYXRlVHlwZTogImRhdGVyYW5nZSIsCiAgICAgICAgICAgIGZvcm1hdDogInl5eXktTU0tZGQiCiAgICAgICAgICB9LAogICAgICAgICAgeyBsYWJlbDogIuS4i+S7pOS6uiIsIHZhbHVlOiAieGxyIiwgdHlwZTogImlucHV0IiwgY2xlYXJhYmxlOiB0cnVlIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5pON5L2c5Lu75YqhIiwgdmFsdWU6ICJjenJ3IiwgdHlwZTogImlucHV0IiwgY2xlYXJhYmxlOiB0cnVlIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5o6l5Luk5Lq6IiwgdmFsdWU6ICJqbHIiLCB0eXBlOiAiaW5wdXQiLCBjbGVhcmFibGU6IHRydWUgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLlm57ku6Tml7bpl7QiLAogICAgICAgICAgICB2YWx1ZTogImhsc2pBcnIiLAogICAgICAgICAgICB0eXBlOiAiZGF0ZSIsCiAgICAgICAgICAgIGRhdGVUeXBlOiAiZGF0ZXJhbmdlIiwKICAgICAgICAgICAgZm9ybWF0OiAieXl5eS1NTS1kZCIKICAgICAgICAgIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIHRhYmxlQW5kUGFnZUluZm86IHsKICAgICAgICBwYWdlcjogewogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMDBdCiAgICAgICAgfSwKICAgICAgICB0YWJsZURhdGE6IFtdLAogICAgICAgIHRhYmxlSGVhZGVyOiBbCiAgICAgICAgICAvKiB7IGxhYmVsOiAn54q25oCBJywgcHJvcDogJ3N0YXR1cycsIG1pbldpZHRoOiAnNzAnIH0sKi8KICAgICAgICAgIHsgbGFiZWw6ICLliIblhazlj7giLCBwcm9wOiAiZmdzbWMiLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLlj5jnlLXnq5kiLCBwcm9wOiAiYmR6bWNzIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi6YCa55+l5Lq6IiwgcHJvcDogInR6cnhtIiwgbWluV2lkdGg6ICI4MCIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLpgJrnn6Xml7bpl7QiLCBwcm9wOiAidHpzaiIsIG1pbldpZHRoOiAiMTQwIiB9LAogICAgICAgICAgeyBsYWJlbDogIuS4i+S7pOS6uiIsIHByb3A6ICJ4bHJtYyIsIG1pbldpZHRoOiAiODAiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5LiL5Luk5pe26Ze0IiwgcHJvcDogInhsc2oiLCBtaW5XaWR0aDogIjE0MCIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLmk43kvZzku7vliqEiLCBwcm9wOiAiY3pydyIsIG1pbldpZHRoOiAiMTYwIiB9LAogICAgICAgICAgeyBsYWJlbDogIuaOpeS7pOS6uiIsIHByb3A6ICJqbHJ4bSIsIG1pbldpZHRoOiAiODAiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi6aKE5aSH5Luk5o6l5Luk5Lq6IiwgcHJvcDogInlibGpscnhtIiwgbWluV2lkdGg6ICIxMDAiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5Zue5Luk5pe26Ze0IiwgcHJvcDogImhsc2oiLCBtaW5XaWR0aDogIjE0MCIgfQogICAgICAgIF0sCiAgICAgICAgb3B0aW9uOiB7IGNoZWNrQm94OiB0cnVlLCBzZXJpYWxOdW1iZXI6IHRydWUgfQogICAgICB9LAogICAgICB0enJPclhsckxpc3Q6IFtdLAogICAgICBqbHJMaXN0OiBbXSwKICAgICAgYWxsamxyTGlzdDogW10sCiAgICAgIHNwckxpc3Q6IFtdCiAgICB9OwogIH0sCiAgYXN5bmMgbW91bnRlZCgpIHsKICAgIC8v6I635Y+WdG9rZW4KICAgIHRoaXMuaGVhZGVyLnRva2VuID0gZ2V0VG9rZW4oKTsKICAgIC8v5Y+Y55S156uZ5LiL5ouJ5qGGCiAgICB0aGlzLmdldEZnc09wdGlvbnMoKTsKICAgIHRoaXMuZ2V0QWxsQmR6U2VsZWN0TGlzdCgpOwogICAgdGhpcy50enJPclhsckxpc3QgPSBhd2FpdCB0aGlzLmdldEdyb3VwVXNlcnMoNjEsICIiKTsKICAgIHRoaXMuYWxsamxyTGlzdCA9IGF3YWl0IHRoaXMuZ2V0R3JvdXBVc2Vycyg2MiwgIiIpOwogICAgdGhpcy5zcHJMaXN0ID0gYXdhaXQgdGhpcy5nZXRHcm91cFVzZXJzKDEzLCAiIik7CiAgICBhd2FpdCB0aGlzLmdldERhdGEoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKgogICAgICog6I635Y+W5YiG5YWs5Y+45LiL5ouJ5pWw5o2uCiAgICAgKi8KICAgIGdldEZnc09wdGlvbnMoKSB7CiAgICAgIGdldEZnc09wdGlvbnMoe30pLnRoZW4ocmVzID0+IHsKICAgICAgICByZXMuZGF0YS5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgaXRlbS52YWx1ZSA9IGl0ZW0udmFsdWUudG9TdHJpbmcoKTsKICAgICAgICB9KTsKICAgICAgICB0aGlzLnNzZ3NPcHRpb25zRGF0YUxpc3QgPSByZXMuZGF0YTsKICAgICAgICB0aGlzLmZpbHRlckluZm8uZmllbGRMaXN0Lm1hcChpdGVtID0+IHsKICAgICAgICAgIGlmIChpdGVtLnZhbHVlID09ICJmZ3MiKSB7CiAgICAgICAgICAgIHJldHVybiAoaXRlbS5vcHRpb25zID0gdGhpcy5zc2dzT3B0aW9uc0RhdGFMaXN0KTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgLy/lr7zlh7oKICAgIGV4cG9ydEZ1bigpIHsKICAgICAgZXhwb3J0RXhjZWwodGhpcy5wYXJhbXMsICLmk43kvZzlkb3ku6Tkv6Hmga8iKTsKICAgIH0sCiAgICAvL+W3peS9nOa1geWbnuS8oOaVsOaNrgogICAgYXN5bmMgdG9kb1Jlc3VsdChkYXRhKSB7CiAgICAgIGxldCByb3cgPSB7CiAgICAgICAgb2JqSWQ6IGRhdGEuYnVzaW5lc3NLZXksCiAgICAgICAgaXNTdGFydDogMSwKICAgICAgICBpc0JhY2s6IGRhdGEucHJvY2Vzc1R5cGUgPT09ICJyb2xsYmFjayIgPyAxIDogMAogICAgICB9OwogICAgICByb3cuc3RhdHVzID0gIjEiOwogICAgICByb3cuYnpzcHIgPSBkYXRhLm5leHRVc2VyOwogICAgICBsZXQgeyBjb2RlIH0gPSBhd2FpdCB1cGRhdGVCeUlkKHJvdyk7CiAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICBhd2FpdCB0aGlzLmdldERhdGEoKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuaTjeS9nOWksei0pSIpOwogICAgICB9CiAgICB9LAogICAgLy/lhbPpl63lvLnnqpcKICAgIGNsb3NlQWN0aXZpdGkoKSB7CiAgICAgIGlmICh0aGlzLiRyZWZzLmFjdGl2aXRpLiRyZWZzLmZvcm0pIHsKICAgICAgICB0aGlzLiRyZWZzLmFjdGl2aXRpLiRyZWZzLmZvcm0ucmVzZXRGaWVsZHMoKTsKICAgICAgfQogICAgICB0aGlzLmlzU2hvdyA9IGZhbHNlOwogICAgfSwKICAgIGdldEFsbEJkelNlbGVjdExpc3QoKSB7CiAgICAgIGdldEJkelNlbGVjdExpc3Qoe30pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmJkekxpc3QgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLy/miYDlsZ7lhazlj7hjaGFuZ2Xkuovku7YKICAgIGFzeW5jIGhhbmRsZUZnc0NoYW5nZShmZ3NWYWx1ZSkgewogICAgICAvL+a4heepuuS5i+WJjeW+l+mAieS4reWAvAogICAgICB0aGlzLnd6RGF0YUxpc3RPcHRpb25zID0gW107CiAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICJiZHptYyIsICIiKTsKICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgInR6ciIsICIiKTsKICAgICAgdGhpcy5qbHJMaXN0ID0gW107CiAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICJqbHIiLCAiIik7CiAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICJ5YmxqbHIiLCAiIik7CiAgICAgIC8v6I635Y+W5Y+Y55S156uZ5pa55rOVCiAgICAgIGF3YWl0IHRoaXMuZmdzQ2hhbmdlKGZnc1ZhbHVlKTsKICAgIH0sCiAgICBhc3luYyBmZ3NDaGFuZ2UoZmdzVmFsdWUpIHsKICAgICAgLy/ojrflj5blj5jnlLXnq5nmlrnms5UKICAgICAgdGhpcy5nZXRCZHpTZWxlY3RMaXN0KGZnc1ZhbHVlKTsKICAgICAgdGhpcy5qbHJMaXN0ID0gYXdhaXQgdGhpcy5nZXRHcm91cFVzZXJzKDYyLCBmZ3NWYWx1ZSk7CiAgICB9LAogICAgaGFuZGxlWWxDaGFuZ2UoKSB7CiAgICAgIHRoaXMudGl0bGV5bCA9ICLmn6XnnIvmk43kvZzpobnnm64iOwogICAgICB0aGlzLnlsID0gdHJ1ZTsKICAgIH0sCiAgICByZXBsYWNlU3RyKCkgewogICAgICB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICBpdGVtLmN6cncgPSBpdGVtLmN6cncucmVwbGFjZUFsbCgKICAgICAgICAgIHRoaXMucmVwbGFjZURhdGEub2xkU3RyLAogICAgICAgICAgdGhpcy5yZXBsYWNlRGF0YS5uZXdTdHIKICAgICAgICApOwogICAgICB9KTsKICAgIH0sCiAgICAvL+aUuemAoOWQjueahOS4iuS8oOWkmuS4quWbvueJh+aWh+S7tgogICAgdXBsb2FkRm9ybSgpIHsKICAgICAgdmFyIG5ld1VybCA9IFtdOyAvL+eUqOadpeWtmOaUvuW9k+WJjeacquaUueWKqOi/h+eahOWbvueJh3VybCzmraTlm77niYfkuI3ov5vooYzliKDpmaTlpITnkIbvvIzlhbbkvZnlvZPliY3kuJrliqFpZOS4i+mdoueahOWbvueJh+aVsOaNruWwhui/m+ihjOWIoOmZpAogICAgICB2YXIgaW1hZ2VUeXBlID0gWyJwbmciLCAianBnIl07CiAgICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCk7CiAgICAgIC8vIOWboOS4uuimgeS8oOS4gOS4quaWh+S7tuaVsOe7hOi/h+WOu++8jOaJgOS7peimgeW+queOr2FwcGVuZAogICAgICB0aGlzLmltZ0xpc3QuZm9yRWFjaChmaWxlID0+IHsKICAgICAgICBpZiAoZmlsZS5yYXcgPT0gdW5kZWZpbmVkKSB7CiAgICAgICAgICBuZXdVcmwucHVzaChmaWxlLnVybCk7CiAgICAgICAgfQogICAgICAgIGZvcm1EYXRhLmFwcGVuZCgiZmlsZXMiLCBmaWxlLnJhdyk7CiAgICAgIH0pOwogICAgICBmb3JtRGF0YS5hcHBlbmQoImJ1c2luZXNzSWQiLCB0aGlzLnVwbG9hZEltZ0RhdGEuYnVzaW5lc3NJZCk7IC8vIOiHquWumuS5ieWPguaVsAogICAgICBmb3JtRGF0YS5hcHBlbmQoIm5ld1VybCIsIG5ld1VybCk7IC8vIOacquaUueWKqOi/h+eahOWbvueJh3VybAogICAgICBmb3JtRGF0YS5hcHBlbmQoInR5cGUiLCBpbWFnZVR5cGUpOyAvLyDmnKrmlLnliqjov4fnmoTlm77niYd1cmwKICAgICAgYXBpCiAgICAgICAgLnJlcXVlc3RQb3N0KCIvaXNjLWFwaS9maWxlL3VwbG9hZEZpbGVzIiwgZm9ybURhdGEsIDEpCiAgICAgICAgLnRoZW4ocmVzID0+IHsKICAgICAgICAgIC8vIOa4heepuuWbvueJh+WIl+ihqO+8iOS4gOWumuimgea4heepuu+8jOWQpuWImeS4iuS8oOaIkOWKn+WQjui/mOaYr+S8muiwg+eUqGhhbmRsZUNoYW5nZe+8iO+8ieWHveaVsO+8jOS4iuS8oOaIkOWKn+WQjuWIl+ihqOS4rei/mOWtmOWcqOWbvueJh++8iQogICAgICAgICAgdGhpcy5pbWdMaXN0ID0gW107CiAgICAgICAgICAvL+afpeivouaOpeWPo+aVsOaNrgogICAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgICAgfSkKICAgICAgICAuY2F0Y2gocmVzID0+IHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5Zu+54mH5LiK5Lyg5aSx6LSl77yBIik7CiAgICAgICAgfSk7CiAgICB9LAogICAgLy8g6YCJ5oup5paH5Lu25pe277yM5b6AZmlsZUxpc3Tph4zmt7vliqAKICAgIGhhbmRsZUNoYW5nZShmaWxlLCBmaWxlTGlzdCkgewogICAgICB0aGlzLmltZ0xpc3QgPSBmaWxlTGlzdDsKICAgIH0sCiAgICBoYW5kbGVQcm9ncmVzcyhldmVudCwgZmlsZSwgZmlsZUxpc3QpIHt9LAogICAgLy/lm77niYfnp7vpmaQKICAgIGhhbmRsZVJlbW92ZShmaWxlLCBmaWxlTGlzdCkgewogICAgICB0aGlzLmltZ0xpc3QgPSBmaWxlTGlzdDsKICAgIH0sCiAgICAvL+WbvueJh+aUvuWkpwogICAgaGFuZGxlUGljdHVyZUNhcmRQcmV2aWV3KGZpbGUpIHsKICAgICAgdGhpcy5kaWFsb2dJbWFnZVVybCA9IGZpbGUudXJsOwogICAgICB0aGlzLmltZ0RpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgfSwKICAgIC8v5YiX6KGo5p+l6K+iCiAgICBhc3luYyBnZXREYXRhKHBhcmFtcykgewogICAgICB0cnkgewogICAgICAgIHRoaXMucGFyYW1zID0geyAuLi50aGlzLnBhcmFtcywgLi4ucGFyYW1zIH07CiAgICAgICAgY29uc3QgcGFyYW0gPSB0aGlzLnBhcmFtczsKICAgICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICAgIHBhcmFtLm15U29ydHMgPSBbeyBwcm9wOiAidXBkYXRlVGltZSIsIGFzYzogZmFsc2UgfV07CiAgICAgICAgY29uc3QgeyBkYXRhLCBjb2RlIH0gPSBhd2FpdCBnZXRMaXN0KHBhcmFtKTsKICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICBmb3IgKGxldCBpIG9mIGRhdGEucmVjb3JkcykgewogICAgICAgICAgICBpLmZnc21jID0gdGhpcy5mb3JtYXRTc2dzKGkuZmdzKTsKICAgICAgICAgICAgaS50enJ4bSA9IHRoaXMuZm9ybWF0WGxyT3JUenIoaS50enIpOwogICAgICAgICAgICBpLnhscnhtID0gdGhpcy5mb3JtYXRYbHJPclR6cihpLnhscik7CiAgICAgICAgICAgIGkuamxyeG0gPSB0aGlzLmZvcm1hdEpscihpLmpscik7CiAgICAgICAgICAgIGkueWJsamxyeG0gPSB0aGlzLmZvcm1hdEpscihpLnlibGpscik7CiAgICAgICAgICB9CiAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8udGFibGVEYXRhID0gZGF0YS5yZWNvcmRzOwogICAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnRvdGFsID0gZGF0YS50b3RhbDsKICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgIGNvbnNvbGUubG9nKGUpOwogICAgICB9CiAgICB9LAoKICAgIC8v5paw5aKe5oyJ6ZKuCiAgICBhc3luYyBnZXRJbnN0ZXIoKSB7CiAgICAgIHRoaXMudGl0bGUgPSAi5Y+Y55S15YCS6Ze45pON5L2c5ZG95Luk5aKe5YqgIjsKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gZmFsc2U7CiAgICAgIHRoaXMuZm9ybSA9IHsgZmdzOiB0aGlzLiRzdG9yZS5nZXR0ZXJzLmRlcHRJZC50b1N0cmluZygpIH07CiAgICAgIGF3YWl0IHRoaXMuZmdzQ2hhbmdlKHRoaXMuZm9ybS5mZ3MpOwogICAgICB0aGlzLmZvcm0uc3RhdHVzID0gIjAiOwogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlOwogICAgfSwKICAgIC8v5L+u5pS55oyJ6ZKuCiAgICBhc3luYyBnZXRVcGRhdGUocm93KSB7CiAgICAgIHRoaXMudGl0bGUgPSAi5Y+Y55S15YCS6Ze45pON5L2c5ZG95Luk5L+u5pS5IjsKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gZmFsc2U7CiAgICAgIHRoaXMuZm9ybSA9IHsgLi4ucm93IH07CiAgICAgIGF3YWl0IHRoaXMuZmdzQ2hhbmdlKHRoaXMuZm9ybS5mZ3MpOwogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlOwogICAgfSwKCiAgICAvL+ivpuaDheaMiemSrgogICAgYXN5bmMgZ2V0RGV0YWlscyhyb3cpIHsKICAgICAgdGhpcy50aXRsZSA9ICLlj5jnlLXlgJLpl7jmk43kvZzlkb3ku6Tor6bmg4UiOwogICAgICB0aGlzLmZvcm0gPSB7IC4uLnJvdyB9OwogICAgICBhd2FpdCB0aGlzLmZnc0NoYW5nZSh0aGlzLmZvcm0uZmdzKTsKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gdHJ1ZTsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gdHJ1ZTsKICAgIH0sCiAgICAvKioKICAgICAqIOaTjeS9nOelqOW8gOelqOaMiemSrgogICAgICogKi8KICAgIGFzeW5jIGNyZWF0ZUN6cCgpIHsKICAgICAgYXdhaXQgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKGFzeW5jIHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgIGxldCB7IGNvZGUsIGRhdGEgfSA9IGF3YWl0IHNhdmVPclVwZGF0ZSh0aGlzLmZvcm0pOwogICAgICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgICAgLy/ph43nva5wYWdl6aG15LuOMeW8gOWniwogICAgICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci5wYWdlUmVzaXplID0gIlkiOwogICAgICAgICAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IGZhbHNlOwogICAgICAgICAgICAgIGF3YWl0IHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICAgIC8v5pON5L2c56Wo5by55Ye65qGGCiAgICAgICAgICAgICAgdGhpcy5pc0Rpc2FibGVkQ3pwID0gdHJ1ZTsKICAgICAgICAgICAgICB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QgPSBbXTsKICAgICAgICAgICAgICAvL+a4hemZpOagoemqjOaPkOekugogICAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uKCkgewogICAgICAgICAgICAgICAgdGhpcy4kcmVmc1siZm9ybUN6cCJdLmNsZWFyVmFsaWRhdGUoKTsKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICB0aGlzLmZvcm1DenAgPSB7fTsKICAgICAgICAgICAgICAvL+WPmOeUtQogICAgICAgICAgICAgIHRoaXMuZm9ybUN6cC5seCA9IDI7CiAgICAgICAgICAgICAgdGhpcy5mb3JtQ3pwLnN0YXR1cyA9ICIwIjsKICAgICAgICAgICAgICB0aGlzLmZvcm1DenAuY3ptbCA9IGRhdGEub2JqSWQ7CiAgICAgICAgICAgICAgdGhpcy5mb3JtQ3pwLmJkem1jID0gZGF0YS5iZHptYzsKICAgICAgICAgICAgICBpZiAoZGF0YS54bHIpIHsKICAgICAgICAgICAgICAgIHRoaXMuZm9ybUN6cC54bHIgPSBkYXRhLnhscjsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgdGhpcy5mb3JtQ3pwLmN6cncgPSBkYXRhLmN6cnc7CiAgICAgICAgICAgICAgdGhpcy5mb3JtQ3pwLmZncyA9IGRhdGEuZmdzOwogICAgICAgICAgICAgIHRoaXMuZm9ybUN6cC5ibSA9IGRhdGEuYmg7CiAgICAgICAgICAgICAgdGhpcy5pc0Rpc2FibGVkID0gdHJ1ZTsKICAgICAgICAgICAgICB0aGlzLmlzQ3pwU2hvd0RldGFpbHMgPSB0cnVlOwogICAgICAgICAgICB9CiAgICAgICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgICAgIGNvbnNvbGUubG9nKGUpOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqCiAgICAgKiDlvIDlip7nu5PnpagKICAgICAqICovCiAgICBjcmVhdGVCanAoKSB7CiAgICAgIHRoaXMuaXNCanBTaG93RGV0YWlscyA9IHRydWU7CiAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IGZhbHNlOwogICAgICB0aGlzLmZvcm1CanAuYmR6bWMgPSB0aGlzLmZvcm0uYmR6bWM7CiAgICAgIHRoaXMuZm9ybUJqcC54bHIgPSB0aGlzLmZvcm0ueGxyOwogICAgICB0aGlzLmZvcm1CanAuY3pydyA9IHRoaXMuZm9ybS5jenJ3OwogICAgICB0aGlzLmZvcm1CanAuZmdzID0gdGhpcy5mb3JtLmZnczsKICAgICAgdGhpcy5mb3JtQmpwLnN0YXR1cyA9ICI0IjsKICAgICAgdGhpcy5mb3JtQmpwLmx4ID0gMjsKICAgICAgdGhpcy5mb3JtQmpwLmN6bWwgPSB0aGlzLmZvcm0ub2JqSWQ7CiAgICB9LAogICAgYXN5bmMgc2F2ZVJvdygpIHsKICAgICAgYXdhaXQgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKGFzeW5jIHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgIGxldCB7IGNvZGUgfSA9IGF3YWl0IHNhdmVPclVwZGF0ZSh0aGlzLmZvcm0pOwogICAgICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLmk43kvZzmiJDlip8iKTsKICAgICAgICAgICAgICAvL+mHjee9rnBhZ2XpobXku44x5byA5aeLCiAgICAgICAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnBhZ2VSZXNpemUgPSAiWSI7CiAgICAgICAgICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gZmFsc2U7CiAgICAgICAgICAgICAgYXdhaXQgdGhpcy5nZXREYXRhKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICAgICAgY29uc29sZS5sb2coZSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvL+WIoOmZpOaMiemSrgogICAgZGVsZXRlUm93KHJvdykgewogICAgICB0aGlzLmZvcm0gPSB7IC4uLnJvdyB9OwogICAgICB0aGlzLiRjb25maXJtKCLmraTmk43kvZzlsIbmsLjkuYXliKDpmaTor6XmlbDmja4sIOaYr+WQpue7p+e7rT8iLCAi5o+Q56S6IiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkKICAgICAgICAudGhlbigoKSA9PiB7CiAgICAgICAgICByZW1vdmUoW3RoaXMuZm9ybS5vYmpJZF0pLnRoZW4oKHsgY29kZSB9KSA9PiB7CiAgICAgICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTmiJDlip8hIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTlpLHotKUhIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoKSA9PiB7fSk7CiAgICB9LAogICAgLy/lhbPpl63lvLnnqpcKICAgIGNsb3NlKCkgewogICAgICB0aGlzLmlzRHhwU2hvd0RldGFpbHMgPSBmYWxzZTsKICAgICAgdGhpcy5pc0xzcFNob3dEZXRhaWxzID0gZmFsc2U7CiAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IGZhbHNlOwogICAgfSwKICAgIC8v6YCJ5oup6KGMCiAgICBzZWxlY3RDaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIC8vIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0ub2JqSWQpOwogICAgICAvLyB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDEKICAgICAgLy8gdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoCiAgICAgIHRoaXMuc2VsZWN0RGF0YSA9IHNlbGVjdGlvbjsKICAgIH0sCiAgICAvL+ihqOagvOaWsOWingogICAgbGlzdEZpcnN0QWRkKCkgewogICAgICBsZXQgcm93ID0gewogICAgICAgIHhoOiAiIiwKICAgICAgICBjenJ3OiAiIiwKICAgICAgICBzZndjOiAiIiwKICAgICAgICB1dWlkOiBnZXRVVUlEKCkKICAgICAgfTsKICAgICAgdGhpcy5wcm9wVGFibGVEYXRhLmNvbEZpcnN0LnB1c2gocm93KTsKICAgICAgdGhpcy5wcm9wVGFibGVEYXRhLnNlbCA9IHJvdzsKICAgICAgdGhpcy5mb3JtQ3pwLmN6eHMgPSB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QubGVuZ3RoOwogICAgICB0aGlzLmZvcm1DenAud3p4Y3p4cyA9IHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdC5sZW5ndGg7CiAgICB9LAogICAgLy/ooajmoLzliKDpmaQKICAgIGxpc3RGaXJzdERlbChyb3cpIHsKICAgICAgaWYgKHJvdy5vYmpJZCkgewogICAgICAgIHRoaXMuaWRzLnB1c2gocm93Lm9iaklkKTsKICAgICAgfQogICAgICB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QgPSB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QuZmlsdGVyKAogICAgICAgIGl0ZW0gPT4gaXRlbS51dWlkICE9PSByb3cudXVpZAogICAgICApOwogICAgICB0aGlzLmZvcm1DenAuY3p4cyA9IHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdC5sZW5ndGg7CiAgICAgIHRoaXMuZm9ybUN6cC53enhjenhzID0gdGhpcy5wcm9wVGFibGVEYXRhLmNvbEZpcnN0Lmxlbmd0aDsKICAgIH0sCiAgICAvL+aTjeS9nOelqOWFs+mXreW8ueeqlwogICAgY2xvc2VDenAoKSB7CiAgICAgIHRoaXMuaXNDenBTaG93RGV0YWlscyA9IGZhbHNlOwogICAgICB0aGlzLmlzQmpwU2hvd0RldGFpbHMgPSBmYWxzZTsKICAgIH0sCgogICAgLy/mk43kvZznpajnoa7lrprmjInpkq4KICAgIHNhdmVSb3dDenAoKSB7CiAgICAgIHRoaXMuZm9ybUN6cC5jb2xGaXJzdCA9IHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdDsKICAgICAgdGhpcy5mb3JtQ3pwLm9iaklkTGlzdCA9IHRoaXMuaWRzOwogICAgICBsZXQgdGFibGVWYWxpZCA9IHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdC5zb21lKGl0ZW0gPT4gIWl0ZW0uY3pydyk7CiAgICAgIGlmICh0YWJsZVZhbGlkKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5pON5L2c6aG555uu5a2Y5Zyo56m66aG577yM6K+35qOA5p+lIik7CiAgICAgIH0KICAgICAgdGhpcy4kcmVmc1siZm9ybUN6cCJdLnZhbGlkYXRlKGFzeW5jIHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQgJiYgIXRhYmxlVmFsaWQpIHsKICAgICAgICAgIHNhdmVPclVwZGF0ZUN6cCh0aGlzLmZvcm1DenApLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMuaXNDenBTaG93RGV0YWlscyA9IGZhbHNlOwogICAgICAgICAgICAgIC8v6YeN572ucGFnZemhteS7jjHlvIDlp4sKICAgICAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIucGFnZVJlc2l6ZSA9ICJZIjsKICAgICAgICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCgogICAgLy/nm7TmjqXkuIrmiqXmk43kvZznpagKICAgIHN1Ym1pdEN6cCgpIHsKICAgICAgdGhpcy5mb3JtQ3pwLmNvbEZpcnN0ID0gdGhpcy5wcm9wVGFibGVEYXRhLmNvbEZpcnN0OwogICAgICB0aGlzLmZvcm1DenAub2JqSWRMaXN0ID0gdGhpcy5pZHM7CiAgICAgIGxldCB0YWJsZVZhbGlkID0gdGhpcy5wcm9wVGFibGVEYXRhLmNvbEZpcnN0LnNvbWUoaXRlbSA9PiAhaXRlbS5jenJ3KTsKICAgICAgaWYgKHRhYmxlVmFsaWQpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLmk43kvZzpobnnm67lrZjlnKjnqbrpobnvvIzor7fmo4Dmn6UiKTsKICAgICAgfQogICAgICB0aGlzLiRyZWZzWyJmb3JtQ3pwIl0udmFsaWRhdGUoYXN5bmMgdmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCAmJiAhdGFibGVWYWxpZCkgewogICAgICAgICAgc2F2ZU9yVXBkYXRlQ3pwKHRoaXMuZm9ybUN6cCkudGhlbihyZXMgPT4gewogICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgIC8v6YeN572ucGFnZemhteS7jjHlvIDlp4sKICAgICAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIucGFnZVJlc2l6ZSA9ICJZIjsKICAgICAgICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICAgICAgICBsZXQgZGF0YSA9IHJlcy5kYXRhOwogICAgICAgICAgICAgIHRoaXMuaXNDenBTaG93RGV0YWlscyA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnBhc3MgPSB0cnVlOwogICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuYnVzaW5lc3NLZXkgPSBkYXRhLm9iaklkOwogICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEucHJvY2Vzc1R5cGUgPSAiY29tcGxldGUiOwogICAgICAgICAgICAgIHRoaXMuYWN0aXZpdGlPcHRpb24udGl0bGUgPSAi5o+Q5LqkIjsKICAgICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmRlZmF1bHRGcm9tID0gdHJ1ZTsKICAgICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnJ5bHggPSAi54+t57uE5a6h5qC45Lq6IjsKICAgICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmR3ID0gZGF0YS5mZ3M7CiAgICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5wZXJzb25Hcm91cElkID0gMTM7CiAgICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5yb3V0ZVBhdGggPQogICAgICAgICAgICAgICAgIi9iZGdsL2JkZHpjei9kYWdhbmdPaWxmaWVsZC9jenBnbC9iZGR6Y3ovZHpjenAiOwogICAgICAgICAgICAgIHRoaXMuaXNTaG93ID0gdHJ1ZTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCgogICAgLy/lip7nu5Pnpajnoa7lrprmjInpkq4KICAgIHNhdmVSb3dCanAoKSB7CiAgICAgIHRoaXMuJHJlZnNbImZvcm1CanAiXS52YWxpZGF0ZShhc3luYyB2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBzYXZlT3JVcGRhdGVDenAodGhpcy5mb3JtQmpwKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgICAgdGhpcy51cGxvYWRJbWdEYXRhLmJ1c2luZXNzSWQgPSByZXMuZGF0YS5vYmpJZDsKICAgICAgICAgICAgICAvL+W8gOWni+S4iuS8oOWbvueJhwogICAgICAgICAgICAgIHRoaXMudXBsb2FkRm9ybSgpOwogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5pON5L2c5oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5pc0JqcFNob3dEZXRhaWxzID0gZmFsc2U7CiAgICAgICAgICAgICAgLy/ph43nva5wYWdl6aG15LuOMeW8gOWniwogICAgICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci5wYWdlUmVzaXplID0gIlkiOwogICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKgogICAgICogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS3lhbjlnovnpajlupMtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0KICAgICAqICovCiAgICAvL+iOt+WPluWFuOWei+aTjeS9nOelqAogICAgZ2V0RHhwa0xpc3RzKCkgewogICAgICB0aGlzLnRpdGxlID0gIuWFuOWei+elqOW6k+afpeivoiI7CiAgICAgIHRoaXMuaXNEeHBTaG93RGV0YWlscyA9IHRydWU7CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICB0aGlzLmlzU2hvd0R4cCA9IHRydWU7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMuc2JtYyA9IHRoaXMuZm9ybS5iZHptYzsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIGFzeW5jIGNoYW5nZU14KHJvdykgewogICAgICB0cnkgewogICAgICAgIGNvbnN0IHsgZGF0YSwgY29kZSB9ID0gYXdhaXQgZ2V0RHhwa0xpc3QoeyBvYmpJZDogcm93Lm9iaklkIH0pOwogICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIHRoaXMucHJvcFRhYmxlRGF0YUR4cE14LmNvbEZpcnN0ID0gZGF0YTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICBjb25zb2xlLmxvZyhlKTsKICAgICAgfQogICAgfSwKICAgIC8v5p+l6K+i5p2h5Lu2CiAgICBhc3luYyBoYW5kbGVRdWVyeShwYXJhbXMpIHsKICAgICAgbGV0IHBhcmFtID0geyAuLi5wYXJhbXMsIC4uLnRoaXMucXVlcnlQYXJhbXMgfTsKICAgICAgZ2V0TGlzdHMocGFyYW0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMucHJvcFRhYmxlRGF0YUR4cC50YWJsZURhdGEgPSByZXNwb25zZS5kYXRhLnJlY29yZHM7CiAgICAgICAgdGhpcy5wcm9wVGFibGVEYXRhRHhwLnBhZ2VyLnRvdGFsID0gcmVzcG9uc2UuZGF0YS50b3RhbDsKICAgICAgfSk7CiAgICB9LAogICAgLy/ph43nva7mnaHku7YKICAgIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMgPSB7fTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8v5YW45Z6L56Wo5bqT56Gu6K6k5oyJ6ZKuCiAgICBzYXZlUm93RHhwKCkgewogICAgICB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QgPSB0aGlzLnByb3BUYWJsZURhdGFEeHBNeC5jb2xGaXJzdDsKICAgICAgdGhpcy5wcm9wVGFibGVEYXRhLmNvbEZpcnN0LmZvckVhY2goZSA9PiB7CiAgICAgICAgZS51dWlkID0gZ2V0VVVJRCgpOwogICAgICB9KTsKICAgICAgdGhpcy5mb3JtQ3pwLmN6eHMgPSB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QubGVuZ3RoOwogICAgICB0aGlzLmZvcm1DenAud3p4Y3p4cyA9IHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdC5sZW5ndGg7CiAgICAgIHRoaXMuaXNEeHBTaG93RGV0YWlscyA9IGZhbHNlOwogICAgfSwKICAgIC8qKgogICAgICogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS3ljoblj7LnpajlupMtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0KICAgICAqICovCiAgICAvL+iOt+WPluWOhuWPsuaTjeS9nOelqAogICAgZ2V0THNwa0xpc3QoKSB7CiAgICAgIHRoaXMudGl0bGUgPSAi5Y6G5Y+y56Wo5bqT5p+l6K+iIjsKICAgICAgdGhpcy5pc0xzcFNob3dEZXRhaWxzID0gdHJ1ZTsKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gZmFsc2U7CiAgICAgIHRoaXMuaXNTaG93THNwID0gdHJ1ZTsKICAgICAgdGhpcy5xdWVyeVBhcmFtc0xzcC5iZHptYyA9IHRoaXMuZm9ybS5iZHptYzsKICAgICAgdGhpcy5oYW5kbGVRdWVyeUxzcCgpOwogICAgfSwKICAgIC8vIOW9k+eCueWHu+ihjOaXtu+8jOS8oOWFpeWPguaVsOafpeivogogICAgYXN5bmMgY2hhbmdlTHNwTXgocm93KSB7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgeyBkYXRhLCBjb2RlIH0gPSBhd2FpdCBnZXRMc3BrTGlzdChyb3cub2JqSWQpOwogICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIHRoaXMucHJvcFRhYmxlRGF0YUxzcE14LmNvbEZpcnN0ID0gZGF0YTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICBjb25zb2xlLmxvZyhlKTsKICAgICAgfQogICAgfSwKICAgIC8v5Y6G5Y+y56Wo5bqT56Gu6K6k5oyJ6ZKuCiAgICBzYXZlUm93THNwKCkgewogICAgICB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QgPSB0aGlzLnByb3BUYWJsZURhdGFMc3BNeC5jb2xGaXJzdDsKICAgICAgdGhpcy5mb3JtQ3pwLmN6eHMgPSB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QubGVuZ3RoOwogICAgICB0aGlzLmZvcm1DenAud3p4Y3p4cyA9IHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdC5sZW5ndGg7CiAgICAgIHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdC5mb3JFYWNoKGUgPT4gewogICAgICAgIGUuc2Z3YyA9IGZhbHNlOwogICAgICAgIGUudXVpZCA9IGdldFVVSUQoKTsKICAgICAgfSk7CiAgICAgIHRoaXMuaXNMc3BTaG93RGV0YWlscyA9IGZhbHNlOwogICAgfSwKICAgIC8v5p+l6K+i5p2h5Lu2CiAgICBoYW5kbGVRdWVyeUxzcCgpIHsKICAgICAgZ2V0TGlzdExzcCh0aGlzLnF1ZXJ5UGFyYW1zTHNwKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLnByb3BUYWJsZURhdGFMc3AuY29sRmlyc3QgPSByZXNwb25zZS5kYXRhLnJlY29yZHM7CiAgICAgIH0pOwogICAgfSwKICAgIC8v6YeN572u5p2h5Lu2CiAgICByZXNldFF1ZXJ5THNwKCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zTHNwID0gewogICAgICAgIHN0YXR1czogIjQiLAogICAgICAgIGx4OiAyCiAgICAgIH07CiAgICAgIHRoaXMuaGFuZGxlUXVlcnlMc3AoKTsKICAgIH0sCiAgICAvKioKICAgICAqIOiOt+WPluWPmOeUteermeS4i+aLieahhuaVsOaNrgogICAgICovCiAgICBnZXRCZHpTZWxlY3RMaXN0KGZncykgewogICAgICBnZXRCZHpTZWxlY3RMaXN0KHsgc3Nkd2JtOiBmZ3MgfSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMud3pEYXRhTGlzdE9wdGlvbnMgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLy/kuIvmi4nmoYZjaGFuZ2Xkuovku7YKICAgIGhhbmRsZUV2ZW50KHZhbCwgZXZlbnRWYWx1ZSkgewogICAgICBpZiAodmFsLmxhYmVsID09PSAiZmdzIikgewogICAgICAgIGdldEJkelNlbGVjdExpc3QoeyBzc2R3Ym06IHZhbC52YWx1ZS50b1N0cmluZygpIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgIHRoaXMuZmlsdGVySW5mby5maWVsZExpc3QubWFwKGl0ZW0gPT4gewogICAgICAgICAgICBpZiAoaXRlbS52YWx1ZSA9PT0gImJkem1jIikgewogICAgICAgICAgICAgIHRoaXMuJHNldChldmVudFZhbHVlLCAiYmR6bWMiLCAiIik7CiAgICAgICAgICAgICAgcmV0dXJuIChpdGVtLm9wdGlvbnMgPSByZXMuZGF0YSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgZmlsdGVyUmVzZXQoKSB7CiAgICAgIHRoaXMucGFyYW1zID0gewogICAgICAgIC8v5Y+Y55S1CiAgICAgICAgbHg6IDIKICAgICAgfTsKICAgIH0sCiAgICBmb3JtYXRTc2dzKHNzZ3MpIHsKICAgICAgbGV0IHBhZ2VPcmdhbml6YXRpb25TZWxlY3RlZExpc3QgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMuc3Nnc09wdGlvbnNEYXRhTGlzdCkpCiAgICAgIHBhZ2VPcmdhbml6YXRpb25TZWxlY3RlZExpc3QucHVzaCh7bGFiZWw6ICfmuK/kuJzlj5jnlLXliIblhazlj7gnLCB2YWx1ZTogJzMwMDInfSkKICAgICAgcGFnZU9yZ2FuaXphdGlvblNlbGVjdGVkTGlzdC5wdXNoKHtsYWJlbDogJ+a4r+S4reWPmOeUteWIhuWFrOWPuCcsIHZhbHVlOiAnMzAwMyd9KQogICAgICBpZiAoc3NncykgewogICAgICAgIGxldCBmaWx0ZXIgPSBwYWdlT3JnYW5pemF0aW9uU2VsZWN0ZWRMaXN0LmZpbHRlcihnID0+IGcudmFsdWUgPT09IHNzZ3MpOwogICAgICAgIGlmIChmaWx0ZXIubGVuZ3RoID4gMCkgewogICAgICAgICAgcmV0dXJuIGZpbHRlclswXS5sYWJlbDsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgcmV0dXJuICIiOwogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gIiI7CiAgICAgIH0KICAgIH0sCiAgICBnZXRHcm91cFVzZXJzKHBlcnNvbkdyb3VwSWQsIGRlcHRJZCkgewogICAgICByZXR1cm4gZ2V0VXNlcnMoewogICAgICAgIHBlcnNvbkdyb3VwSWQ6IHBlcnNvbkdyb3VwSWQsCiAgICAgICAgZGVwdElkOiBkZXB0SWQsCiAgICAgICAgZGVwdE5hbWU6ICIiCiAgICAgIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICByZXR1cm4gcmVzLmRhdGE7CiAgICAgIH0pOwogICAgfSwKICAgIGZvcm1hdFhsck9yVHpyKHhscikgewogICAgICBpZiAoeGxyKSB7CiAgICAgICAgbGV0IGZpbHRlciA9IHRoaXMudHpyT3JYbHJMaXN0LmZpbHRlcihnID0+IGcudXNlck5hbWUgPT09IHhscik7CiAgICAgICAgaWYgKGZpbHRlci5sZW5ndGggPiAwKSB7CiAgICAgICAgICByZXR1cm4gZmlsdGVyWzBdLm5pY2tOYW1lOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICByZXR1cm4gIiI7CiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiAiIjsKICAgICAgfQogICAgfSwKCiAgICBmb3JtYXRKbHIoamxyKSB7CiAgICAgIGlmIChqbHIpIHsKICAgICAgICBsZXQgZmlsdGVyID0gdGhpcy5hbGxqbHJMaXN0LmZpbHRlcihnID0+IGcudXNlck5hbWUgPT09IGpscik7CiAgICAgICAgaWYgKGZpbHRlci5sZW5ndGggPiAwKSB7CiAgICAgICAgICByZXR1cm4gZmlsdGVyWzBdLm5pY2tOYW1lOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICByZXR1cm4gIiI7CiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiAiIjsKICAgICAgfQogICAgfSwKICAgIC8v5a+85Ye6d29yZAogICAgZXhwb3J0V29yZCgpIHsKICAgICAgbGV0IHBhcmFtcyA9IHsKICAgICAgICBkYXRhOiB0aGlzLnBhcmFtcywKICAgICAgICB1cmw6ICJiekJkZHpjem1sIgogICAgICB9OwogICAgICBsZXQgZmlsZU5hbWUgPSAi5Y+Y55S15YCS6Ze45pON5L2c5ZG95Luk6K6w5b2VIjsKICAgICAgaWYgKCF0aGlzLnNlbGVjdERhdGEubGVuZ3RoID4gMCkgewogICAgICAgIHBhcmFtcy5kYXRhID0gdGhpcy5wYXJhbXM7CiAgICAgICAgZXhwb3J0V29yZEJ5cGFyYW1zKHBhcmFtcywgZmlsZU5hbWUpOwogICAgICB9IGVsc2UgewogICAgICAgIHBhcmFtcy5kYXRhID0gdGhpcy5zZWxlY3REYXRhOwogICAgICAgIGV4cG9ydFdvcmRCeXNlbGVjdGlvbihwYXJhbXMsIGZpbGVOYW1lKTsKICAgICAgfQogICAgfSwKICAgIGNoYW5nZVpzbE9yWWJsKHZhbCkgewogICAgICBpZiAodmFsKSB7CiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgInR6c2oiLCBudWxsKTsKICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLCAidHpyIiwgIiIpOwogICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICJ5YmxqbHIiLCAiIik7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgInhsc2oiLCBudWxsKTsKICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLCAieGxyIiwgIiIpOwogICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICJqbHIiLCAiIik7CiAgICAgIH0KICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["dzczml.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAm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file": "dzczml.vue", "sourceRoot": "src/views/dagangOilfield/czpgl/bddzcz", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n      @handleEvent=\"handleEvent\"\n      @handleReset=\"filterReset\"\n    />\n    <div>\n      <el-white class=\"button-group\">\n        <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n          <el-col\n            style=\"\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n          \"\n          >\n            <div>\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-plus\"\n                @click=\"getInster\"\n                v-hasPermi=\"['dzczml:button:add']\"\n                >新增\n              </el-button>\n              <!-- <el-button type=\"primary\" @click=\"exportFun\"\n              >导出\n              </el-button> -->\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-download\"\n                @click=\"exportWord\"\n                >导出</el-button\n              >\n            </div>\n          </el-col>\n        </el-row>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"selectChange\"\n          height=\"66vh\"\n          v-loading=\"loading\"\n        >\n          <el-table-column\n            slot=\"table_eight\"\n            align=\"center\"\n            fixed=\"right\"\n            style=\"display: block\"\n            label=\"操作\"\n            width=\"160\"\n            :resizable=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                @click=\"getDetails(scope.row)\"\n                type=\"text\"\n                size=\"small\"\n                title=\"详情\"\n                class=\"el-icon-view\"\n              ></el-button>\n              <el-button\n                @click=\"getUpdate(scope.row)\"\n                v-if=\"(scope.row.createBy === currentUser || hasSuperRole) && !scope.row.czp\"\n                type=\"text\"\n                size=\"small\"\n                title=\"编辑\"\n                class=\"el-icon-edit\"\n              >\n              </el-button>\n              <el-button\n                @click=\"deleteRow(scope.row)\"\n                v-if=\"\n                  (scope.row.createBy === currentUser ||\n                    hasSuperRole) &&\n                    !scope.row.czp\n                \"\n                type=\"text\"\n                size=\"small\"\n                title=\"删除\"\n                class=\"el-icon-delete\"\n              >\n              </el-button>\n            </template>\n          </el-table-column>\n        </comp-table>\n      </el-white>\n    </div>\n\n    <!-- 详情/新增/修改 操作命令页-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <div>\n          <el-row style=\"display: flex;flex-wrap: wrap;\">\n            <el-col :span=\"8\">\n              <el-form-item label=\"令类型：\" prop=\"zslOrYbl\">\n                <el-switch\n                  :disabled=\"isDisabled\"\n                  style=\"width: 100%\"\n                  v-model=\"form.zslOrYbl\"\n                  active-color=\"#13ce66\"\n                  inactive-color=\"#ff4949\"\n                  active-text=\"正式令\"\n                  inactive-text=\"预备令\"\n                  @change=\"changeZslOrYbl\"\n                >\n                </el-switch>\n              </el-form-item>\n            </el-col>\n            <!--            <el-col :span=\"8\">-->\n            <!--              <el-form-item label=\"编号：\" prop=\"bh\">-->\n            <!--                <el-input v-model=\"form.bh\" disabled placeholder=\"确认后编号自动生成\"/>-->\n            <!--              </el-form-item>-->\n            <!--            </el-col>-->\n            <el-col :span=\"8\">\n              <el-form-item label=\"分公司：\" prop=\"fgs\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.fgs\"\n                  @change=\"handleFgsChange\"\n                  disabled\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in ssgsOptionsDataList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"变电站：\" prop=\"bdzmc\">\n                <el-select\n                  style=\"width: 100%\"\n                  filterable\n                  clearable\n                  v-model=\"form.bdzmc\"\n                  ref=\"bdzmc\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择变电站\"\n                >\n                  <el-option\n                    v-for=\"item in wzDataListOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"!form.zslOrYbl\"\n                label=\"通知时间：\"\n                prop=\"tzsj\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: false }]\n                    : [{ required: true, message: '不能为空' }]\n                \"\n              >\n                <el-date-picker\n                  v-model=\"form.tzsj\"\n                  :disabled=\"isDisabled\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"!form.zslOrYbl\"\n                label=\"通知人：\"\n                prop=\"tzr\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: false }]\n                    : [{ required: true, message: '不能为空' }]\n                \"\n              >\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.tzr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in tzrOrXlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"!form.zslOrYbl\"\n                label=\"预备令接令人：\"\n                prop=\"ybljlr\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: false }]\n                    : [{ required: true, message: '不能为空' }]\n                \"\n              >\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.ybljlr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in jlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"form.zslOrYbl\"\n                label=\"下令时间：\"\n                prop=\"xlsj\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-date-picker\n                  v-model=\"form.xlsj\"\n                  :disabled=\"isDisabled\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"form.zslOrYbl\"\n                label=\"下令人：\"\n                prop=\"xlr\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.xlr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in tzrOrXlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"form.zslOrYbl\"\n                label=\"接令人：\"\n                prop=\"jlr\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.jlr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in jlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"回令时间：\" prop=\"hlsj\">\n                <el-date-picker\n                  v-model=\"form.hlsj\"\n                  :disabled=\"isDisabled\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  :placeholder=\"isDisabled ? '' : '选择日期时间'\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-form-item label=\"操作任务：\" prop=\"czrw\">\n            <el-input\n              type=\"textarea\"\n              v-model=\"form.czrw\"\n              :disabled=\"isDisabled\"\n              placeholder=\"请输入操作任务\"\n              :rows=\"3\"\n            />\n          </el-form-item>\n        </div>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">关 闭</el-button>\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"saveRow\"\n          >保 存</el-button\n        >\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"createCzp\"\n          >开操作票\n        </el-button>\n        <el-button\n          v-if=\"isDisabled && (form.createBy === currentUser || hasSuperRole)&& !form.czp\"\n          type=\"primary\"\n          @click=\"createBjp\"\n          >开办结票\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!--操作票开票页-->\n    <el-dialog\n      title=\"变电倒闸操作票开票\"\n      :visible.sync=\"isCzpShowDetails\"\n      append-to-body\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form\n        label-width=\"120px\"\n        ref=\"formCzp\"\n        :model=\"formCzp\"\n        :rules=\"czpRules\"\n      >\n        <div>\n          <el-row>\n            <!--            <el-col :span=\"8\">-->\n            <!--              <el-form-item label=\"编号：\" prop=\"bm\">-->\n            <!--                <el-input v-model=\"formCzp.bm\" disabled placeholder=\"确认后编号自动生成\"/>-->\n            <!--              </el-form-item>-->\n            <!--            </el-col>-->\n            <el-col :span=\"8\">\n              <el-form-item label=\"分公司：\" prop=\"fgs\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formCzp.fgs\"\n                  @change=\"handleFgsChange\"\n                  disabled\n                  placeholder=\"请选择分公司\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in ssgsOptionsDataList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"变电站：\" prop=\"bdzmc\">\n                <el-select\n                  style=\"width: 100%\"\n                  filterable\n                  v-model=\"formCzp.bdzmc\"\n                  disabled\n                  placeholder=\"请输入内容\"\n                >\n                  <el-option\n                    v-for=\"item in wzDataListOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作开始时间：\" prop=\"kssj\">\n                <el-date-picker\n                  disabled\n                  v-model=\"formCzp.kssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作结束时间：\" prop=\"jssj\">\n                <el-date-picker\n                  disabled\n                  v-model=\"formCzp.jssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作人：\" prop=\"czr\">\n                <el-input\n                  v-model=\"formCzp.czr\"\n                  :disabled=\"isDisabledCzp\"\n                  placeholder=\"请输入内容\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"监护人：\" prop=\"jhr\">\n                <el-input\n                  v-model=\"formCzp.jhr\"\n                  :disabled=\"isDisabledCzp\"\n                  placeholder=\"请输入内容\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"下令人：\" prop=\"xlr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formCzp.xlr\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                  disabled\n                >\n                  <el-option\n                    v-for=\"item in tzrOrXlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作项数：\" prop=\"czxs\">\n                <el-input-number\n                  v-model=\"formCzp.czxs\"\n                  disabled\n                  placeholder=\"请输入操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"已执行项数：\" prop=\"yzxczxs\">\n                <el-input-number\n                  v-model=\"formCzp.yzxczxs\"\n                  disabled\n                  placeholder=\"请输入已执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"未执行项数：\" prop=\"wzxczxs\">\n                <el-input-number\n                  v-model=\"formCzp.wzxczxs\"\n                  disabled\n                  placeholder=\"请输入未执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"操作任务：\" prop=\"czrw\">\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"formCzp.czrw\"\n                  :disabled=\"isDisabledCzp\"\n                  placeholder=\"请输入内容\"\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"上传图片：\">\n                <el-upload\n                  :disabled=\"isDisabledCzp\"\n                  action=\"\"\n                  ref=\"uploadImg\"\n                  accept=\"image/jpeg,image/jpg,image/png\"\n                  :headers=\"header\"\n                  :multiple=\"true\"\n                  :on-change=\"handleChange\"\n                  :data=\"uploadImgData\"\n                  :file-list=\"imgList\"\n                  :auto-upload=\"false\"\n                  :on-preview=\"handlePictureCardPreview\"\n                  :on-progress=\"handleProgress\"\n                  :on-remove=\"handleRemove\"\n                >\n                  <el-button type=\"primary\" size=\"small\">选择文件</el-button>\n                </el-upload>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n\n        <div>\n          <el-dialog :title=\"titleyl\" :visible.sync=\"yl\" append-to-body>\n            <!--第一个表格-->\n            <div style=\"margin-bottom:10px;\">\n              <el-row>\n                <el-col :span=\"8\">\n                  <el-input\n                    v-model=\"replaceData.oldStr\"\n                    style=\"width:80%\"\n                    placeholder=\"查找字符串\"\n                  >\n                  </el-input>\n                </el-col>\n\n                <el-col :span=\"8\">\n                  <!-- <span> ---</span> -->\n                  <el-input\n                    v-model=\"replaceData.newStr\"\n                    style=\"width:80%\"\n                    placeholder=\"替换后字符串\"\n                  >\n                  </el-input>\n                </el-col>\n                <el-col :span=\"4\">\n                  <el-button\n                    type=\"primary\"\n                    icon=\"el-icon-edit\"\n                    @click=\"replaceStr\"\n                    >批量替换</el-button\n                  >\n                </el-col>\n              </el-row>\n            </div>\n\n            <el-table\n              :data=\"propTableData.colFirst\"\n              ref=\"propTable\"\n              disabled\n              border\n              stripe\n              style=\"width: 100%\"\n              :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n            >\n              <el-table-column\n                align=\"center\"\n                width=\"120\"\n                prop=\"xh\"\n                label=\"顺序号\"\n                sortable\n              >\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"请输入顺序号\"\n                      disabled\n                      v-model=\"scope.row.xh\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"请输入操作项目\"\n                      disabled\n                      v-model=\"scope.row.czrw\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-dialog>\n        </div>\n        <!--列表-->\n        <div>\n          <el-white class=\"mb8 pull-right\">\n            <el-button type=\"primary\" @click=\"getDxpkLists\">典型票库</el-button>\n            <el-button type=\"warning\" @click=\"getLspkList\">历史票库</el-button>\n            <el-button\n              type=\"info\"\n              @click=\"handleYlChange\"\n              style=\"text-align: right\"\n              >预览</el-button\n            >\n          </el-white>\n          <!--第一个表格-->\n          <el-table\n            :data=\"propTableData.colFirst\"\n            height=\"300\"\n            border\n            stripe\n            style=\"width: 100%\"\n            element-loading-text=\"正在获取数据\"\n            element-loading-spinner=\"el-icon-loading\"\n            v-loading=\"loading\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <el-table-column\n              align=\"center\"\n              width=\"120\"\n              prop=\"xh\"\n              label=\"顺序号\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input-number\n                    size=\"small\"\n                    v-model=\"scope.row.xh\"\n                    :min=\"1\"\n                    :precision=\"0\"\n                    controls-position=\"right\"\n                  ></el-input-number>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入操作项目\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" width=\"80\">\n              <template slot=\"header\" slot-scope=\"scope\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  icon=\"el-icon-plus\"\n                  @click=\"listFirstAdd(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n              <template slot-scope=\"scope\">\n                <el-button\n                  type=\"text\"\n                  icon=\"el-icon-delete\"\n                  @click=\"listFirstDel(scope.row)\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"closeCzp\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"saveRowCzp\">保 存</el-button>\n        <el-button type=\"primary\" @click=\"submitCzp\">上 报</el-button>\n      </div>\n    </el-dialog>\n\n    <!--开办结票-->\n    <el-dialog\n      title=\"开办结票\"\n      :visible.sync=\"isBjpShowDetails\"\n      append-to-body\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form\n        label-width=\"120px\"\n        ref=\"formBjp\"\n        :model=\"formBjp\"\n        :rules=\"rules2\"\n      >\n        <div>\n          <el-row>\n            <el-col :span=\"8\">\n              <el-form-item label=\"编号：\" prop=\"bm\">\n                <el-input\n                  v-model=\"formBjp.bm\"\n                  disabled\n                  placeholder=\"确认后编号自动生成\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"分公司：\" prop=\"fgs\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formBjp.fgs\"\n                  @change=\"handleFgsChange\"\n                  disabled\n                  placeholder=\"请选择分公司\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in ssgsOptionsDataList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"变电站：\" prop=\"bdzmc\">\n                <el-select\n                  style=\"width: 100%\"\n                  filterable\n                  v-model=\"formBjp.bdzmc\"\n                  placeholder=\"请输入内容\"\n                  disabled\n                >\n                  <el-option\n                    v-for=\"item in wzDataListOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作开始时间：\" prop=\"kssj\">\n                <el-date-picker\n                  v-model=\"formBjp.kssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作结束时间：\" prop=\"jssj\">\n                <el-date-picker\n                  v-model=\"formBjp.jssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作人：\" prop=\"czr\">\n                <el-input v-model=\"formBjp.czr\" placeholder=\"请输入内容\" />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"监护人：\" prop=\"jhr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formBjp.jhr\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in jlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"下令人：\" prop=\"xlr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formBjp.xlr\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in tzrOrXlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"审票人：\" prop=\"bzspr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formBjp.bzspr\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in sprList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作项数：\" prop=\"czxs\">\n                <el-input-number\n                  v-model=\"formBjp.czxs\"\n                  placeholder=\"请输入操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"已执行项数：\" prop=\"yzxczxs\">\n                <el-input-number\n                  v-model=\"formBjp.yzxczxs\"\n                  placeholder=\"请输入已执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"未执行项数：\" prop=\"wzxczxs\">\n                <el-input-number\n                  v-model=\"formBjp.wzxczxs\"\n                  placeholder=\"请输入未执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"操作任务：\" prop=\"czrw\">\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"formBjp.czrw\"\n                  placeholder=\"请输入内容\"\n                  disabled\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"上传图片：\">\n                <el-upload\n                  action=\"\"\n                  ref=\"uploadImg\"\n                  accept=\"image/jpeg,image/jpg,image/png\"\n                  :headers=\"header\"\n                  :multiple=\"true\"\n                  :on-change=\"handleChange\"\n                  :data=\"uploadImgData\"\n                  :file-list=\"imgList\"\n                  :auto-upload=\"false\"\n                  list-type=\"picture-card\"\n                  :on-preview=\"handlePictureCardPreview\"\n                  :on-progress=\"handleProgress\"\n                  :on-remove=\"handleRemove\"\n                >\n                  <i class=\"el-icon-plus\"></i>\n                </el-upload>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"closeCzp\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"saveRowBjp\">确 认</el-button>\n      </div>\n    </el-dialog>\n\n    <!--典型票库-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isDxpShowDetails\"\n      width=\"50%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"180px\" ref=\"form\" :model=\"form\">\n        <el-row>\n          <!--查询及列表-->\n          <el-col :span=\"24\">\n            <el-form\n              :model=\"queryParams\"\n              class=\"searchForm\"\n              ref=\"queryForm\"\n              label-width=\"100px\"\n              v-show=\"isShowSx\"\n            >\n              <el-row>\n                <el-col :span=\"12\">\n                  <el-form-item label=\"典型票名称：\" prop=\"dxpmc\">\n                    <el-input\n                      placeholder=\"请输入典型票名称\"\n                      v-model=\"queryParams.dxpmc\"\n                      clearable\n                    />\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"12\">\n                  <el-form-item label=\"操作任务：\" prop=\"czrw\">\n                    <el-input\n                      placeholder=\"请输入操作任务\"\n                      v-model=\"queryParams.czrw\"\n                      clearable\n                    />\n                  </el-form-item>\n                </el-col>\n                <div style=\"float: right;margin-bottom: 10px\">\n                  <el-button\n                    type=\"primary\"\n                    icon=\"el-icon-search\"\n                    @click=\"handleQuery\"\n                    >搜索</el-button\n                  >\n                  <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\"\n                    >重置</el-button\n                  >\n                </div>\n              </el-row>\n            </el-form>\n            <div style=\"float: left;margin-bottom: 10px\">\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-search\"\n                @click=\"isShowSx = isShowSx ? false : true\"\n                >筛 选</el-button\n              >\n            </div>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <!--主表信息-->\n            <comp-table\n              :table-and-page-info=\"propTableDataDxp\"\n              height=\"400\"\n              border\n              stripe\n              style=\"width: 100%\"\n              max-height=\"60vh\"\n              @getMethod=\"handleQuery\"\n              @rowClick=\"changeMx\"\n            >\n            </comp-table>\n          </el-col>\n        </el-row>\n        <!--子表信息-->\n        <el-row>\n          <el-table\n            :data=\"propTableDataDxpMx.colFirst\"\n            :disabled=\"isDisabled\"\n            height=\"300\"\n            border\n            stripe\n            style=\"width: 100%\"\n            max-height=\"40vh\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <!--子表列表-->\n            <el-table-column\n              align=\"center\"\n              prop=\"xh\"\n              label=\"序号\"\n              width=\"100\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"序号\"\n                    :disabled=\"isShowDxp\"\n                    v-model=\"scope.row.xh\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"操作项目\"\n                    :disabled=\"isShowDxp\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n          </el-table>\n        </el-row>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"saveRowDxp\">确 认 </el-button>\n      </div>\n    </el-dialog>\n\n    <!--历史票库-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isLspShowDetails\"\n      width=\"50%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"180px\" ref=\"form\" :model=\"form\">\n        <el-form\n          :model=\"queryParamsLsp\"\n          class=\"searchForm\"\n          ref=\"queryForm\"\n          label-width=\"100px\"\n          v-show=\"isShowSx\"\n        >\n          <el-row>\n            <el-col :span=\"12\">\n              <el-form-item label=\"操作时间\" prop=\"czsjArr\">\n                <el-date-picker\n                  type=\"daterange\"\n                  range-separator=\"-\"\n                  start-placeholder=\"开始时间\"\n                  end-placeholder=\"结束时间\"\n                  v-model=\"queryParamsLsp.czsjArr\"\n                  clearable\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <!--<el-col :span=\"12\">\n              <el-form-item label=\"操作结束时间\" prop=\"jssj\">\n                <el-input placeholder=\"请输入操作结束时间\" v-model=\"queryParamsLsp.jssj\" clearable primary style=\"width: 100%\"/>\n              </el-form-item>\n            </el-col>-->\n            <el-col :span=\"24\">\n              <el-form-item label=\"操作任务\" prop=\"czrw\">\n                <el-input\n                  type=\"textarea\"\n                  :rows=\"1\"\n                  placeholder=\"请输入操作任务\"\n                  v-model=\"queryParamsLsp.czrw\"\n                  clearable\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <el-col :span=\"12\">\n              <el-form-item label=\"操作人\" prop=\"czr\">\n                <el-input\n                  placeholder=\"请输入操作人\"\n                  v-model=\"queryParamsLsp.czr\"\n                  clearable\n                  primary\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"监护人\" prop=\"jhr\">\n                <el-input\n                  placeholder=\"请输入监护人\"\n                  v-model=\"queryParamsLsp.jhr\"\n                  clearable\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <el-col :span=\"12\">\n              <el-form-item label=\"下令人\" prop=\"xlr\">\n                <el-input\n                  placeholder=\"请输入下令人\"\n                  v-model=\"queryParamsLsp.xlr\"\n                  clearable\n                  primary\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n            <div style=\"float: right;margin-bottom: 10px\">\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-search\"\n                @click=\"handleQueryLsp\"\n                >搜索</el-button\n              >\n              <el-button icon=\"el-icon-refresh\" @click=\"resetQueryLsp\"\n                >重置</el-button\n              >\n            </div>\n          </el-row>\n        </el-form>\n        <!--主表信息-->\n        <div>\n          <div style=\"float: left;margin-bottom: 10px\">\n            <el-button\n              type=\"primary\"\n              icon=\"el-icon-search\"\n              @click=\"isShowSx = isShowSx ? false : true\"\n              >筛 选</el-button\n            >\n          </div>\n          <el-table\n            @row-click=\"changeLspMx\"\n            :data=\"propTableDataLsp.colFirst\"\n            :disabled=\"isDisabled\"\n            height=\"300\"\n            border\n            stripe\n            style=\"width: 100%\"\n            max-height=\"40vh\"\n          >\n            <el-table-column\n              align=\"center\"\n              prop=\"bdzmcs\"\n              label=\"变电站\"\n              width=\"170\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入变电站\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.bdzmcs\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"kssj\"\n              label=\"操作开始时间\"\n              width=\"170\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入开始时间\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.kssj\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"jssj\"\n              label=\"操作结束时间\"\n              width=\"170\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入结束时间\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.jssj\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"czrw\"\n              label=\"操作任务\"\n              width=\"230\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    type=\"textarea\"\n                    :rows=\"2\"\n                    placeholder=\"请输入操作任务\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"czr\"\n              label=\"操作人\"\n              width=\"120\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.czr\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"jhr\"\n              label=\"监护人\"\n              width=\"120\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.jhr\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"xlr\"\n              label=\"下令人\"\n              width=\"120\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.xlrmc\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"spr\"\n              label=\"审票人\"\n              width=\"150\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.bzsprmc\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n        <!--子表信息-->\n        <div>\n          <el-table\n            :data=\"propTableDataLspMx.colFirst\"\n            :disabled=\"isDisabled\"\n            height=\"300\"\n            border\n            stripe\n            style=\"width: 100%\"\n            max-height=\"40vh\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <!--子表列表-->\n            <el-table-column\n              align=\"center\"\n              width=\"120\"\n              prop=\"xh\"\n              label=\"顺序号\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入顺序号\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.xh\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入操作项目\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"saveRowLsp\">确 认 </el-button>\n      </div>\n    </el-dialog>\n\n    <!--图片放大展示弹出框-->\n    <el-dialog title=\"放大\" :visible.sync=\"imgDialogVisible\" v-dialogDrag>\n      <img width=\"100%\" :src=\"dialogImageUrl\" alt=\"\" />\n    </el-dialog>\n\n    <!--  工作流  -->\n    <activiti\n      ref=\"activiti\"\n      :processData=\"processData\"\n      :isShow=\"isShow\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n    ></activiti>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport api from \"@/utils/request\";\nimport {\n  exportExcel,\n  getBdzSelectList,\n  getList,\n  remove,\n  saveOrUpdate,\n  saveOrUpdateCzp,\n  exportWordByselection,\n  exportWordByparams\n} from \"@/api/yxgl/bdyxgl/bddzczml\";\nimport { getDxpkList, getLists } from \"@/api/bzgl/dxczp\";\nimport { getListLsp, getLspkList, updateById } from \"@/api/yxgl/bdyxgl/bddzczp\";\nimport ElectronicAuthDialog from \"com/ElectronicAuthDialog\";\nimport CompTable from \"com/CompTable\";\nimport ElFilter from \"com/ElFilter\";\nimport { getUsers } from \"@/api/system/usergroup\";\nimport activiti from \"com/activiti_czp\";\nimport { getUUID } from \"@/utils/ruoyi\";\nimport { getFgsOptions } from \"@/api/yxgl/bdyxgl/zbgl\";\n\nexport default {\n  name: \"dzczml\",\n  components: { ElectronicAuthDialog, CompTable, ElFilter, activiti },\n  data() {\n    return {\n      hasSuperRole:this.$store.getters.hasSuperRole,\n      //loading:false,\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"czpsh\",\n        businessKey: \"\",\n        businessType: \"倒闸操作票\",\n        variables: {},\n        defaultFrom: true,\n        nextUser: \"\",\n        processType: \"complete\"\n      },\n      isShow: false,\n      //弹出框标题\n      activitiOption: { title: \"上报\" },\n      czpRules: {\n        // xlr: [\n        //   {required: true, message: '不能为空', trigger: 'blur'}\n        // ],\n      },\n      rules: {\n        bdzmc: [{ required: true, message: \"不能为空\", trigger: \"select\" }],\n        fgs: [{ required: true, message: \"不能为空\", trigger: \"select\" }],\n        czrw: [{ required: true, message: \"不能为空\", trigger: \"blur\" }]\n      },\n      rules2: {\n        kssj: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        jssj: [{ required: true, message: \"不能为空\", trigger: \"select\" }],\n        czr: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        jhr: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        xlr: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        czxs: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        yzxczxs: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        wzxczxs: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        bzspr: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        czrw: [{ required: true, message: \"不能为空\", trigger: \"blur\" }]\n      },\n      currentUser: this.$store.getters.name,\n      yl: false,\n      replaceData: {},\n      titleyl: \"\",\n      //图片地址url\n      dialogImageUrl: \"\",\n      //展示图片dialog控制\n      imgDialogVisible: false,\n      //上传图片时的携带的其他参数\n      uploadImgData: {\n        businessId: \"\" //携带的表单主键id\n      },\n      //上传图片时的请求头\n      header: {},\n      //图片list\n      imgList: [],\n      isShowSx: false,\n      bdzList: [],\n      //弹出框内新增时下拉框所属位置数据\n      wzDataListOptions: [],\n      isShowDxp: false,\n      isShowLsp: false,\n      loading: false,\n      isDisabledCzp: false,\n      isDxpShowDetails: false,\n      isLspShowDetails: false,\n      //操作票弹框是否显示\n      isCzpShowDetails: false,\n      isBjpShowDetails: false,\n      // 多选框选中的id\n      ids: [],\n      selectData: [],\n      //倒闸操作票命令\n      params: {\n        //变电\n        lx: 2\n      },\n      //典型票查询条件\n      queryParams: {},\n      //历史票查询条件\n      queryParamsLsp: {\n        status: \"4\",\n        lx: 2\n      },\n      //弹出框中表格数据\n      propTableData: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //弹出框中表格数据\n      propTableDataDxpMx: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //弹出框中表格数据\n      propTableDataDxp: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 1,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"变电站\", prop: \"sbmcms\", minWidth: \"200\" },\n          { label: \"典型操作票名称\", prop: \"dxpmc\", minWidth: \"200\" },\n          { label: \"操作任务\", prop: \"czrw\", minWidth: \"200\" }\n        ],\n        option: { checkBox: false, serialNumber: true },\n        sel: null // 选中行\n      },\n      //弹出框中表格数据\n      propTableDataLspMx: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //弹出框中表格数据\n      propTableDataLsp: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //form表单\n      form: {\n        bh: \"\",\n        bdzmc: \"\",\n        tzsj: \"\",\n        tzr: \"\",\n        xlsj: \"\",\n        xlr: \"\",\n        czrw: \"\",\n        jlr: \"\",\n        hlsj: \"\",\n        status: \"\"\n      },\n      // 操作票\n      formCzp: {\n        bh: \"\",\n        bdzmc: \"\",\n        kssj: \"\",\n        jssj: \"\",\n        czrw: \"\",\n        czr: \"\",\n        jhr: \"\",\n        xlr: \"\",\n        spr: \"\",\n        lx: 2, //变电\n        colFirst: [],\n        status: \"\"\n      },\n      // 办结票\n      formBjp: {\n        bh: \"\",\n        bdzmc: \"\",\n        kssj: \"\",\n        jssj: \"\",\n        czrw: \"\",\n        czr: \"\",\n        jhr: \"\",\n        xlr: \"\",\n        spr: \"\",\n        lx: 2, //变电\n        colFirst: [],\n        status: \"\"\n      },\n\n      //所属分公司下拉框数据\n      ssgsOptionsDataList: [],\n\n      //详情弹框是否显示\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          bh: \"\",\n          bdzmc: \"\",\n          tzsjArr: \"\",\n          tzr: \"\",\n          xlsjArr: \"\",\n          xlr: \"\",\n          czrw: \"\",\n          jlr: \"\",\n          hlsjArr: \"\"\n        }, //查询条件\n        fieldList: [\n          // {\n          //   label: '令',\n          //   type: 'switch',\n          //   value: 'yblOrZsl',\n          //   textStart:'预备令',\n          //   textEnd:'正式令'\n          // },\n          // {label: '编号', value: 'bh', type: 'input', clearable: true},\n          // /*{ label: '状态', value: 'status', type: 'select', clearable: true },*/\n          {\n            label: \"分公司\",\n            value: \"fgs\",\n            type: \"select\",\n            options: [],\n            clearable: true\n          },\n          {\n            label: \"变电站\",\n            value: \"bdzmc\",\n            type: \"select\",\n            options: [],\n            clearable: true,\n            filterable: true\n          },\n          {\n            label: \"通知时间\",\n            value: \"tzsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"通知人\", value: \"tzr\", type: \"input\", clearable: true },\n          {\n            label: \"下令时间\",\n            value: \"xlsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"下令人\", value: \"xlr\", type: \"input\", clearable: true },\n          { label: \"操作任务\", value: \"czrw\", type: \"input\", clearable: true },\n          { label: \"接令人\", value: \"jlr\", type: \"input\", clearable: true },\n          {\n            label: \"回令时间\",\n            value: \"hlsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          /* { label: '状态', prop: 'status', minWidth: '70' },*/\n          { label: \"分公司\", prop: \"fgsmc\", minWidth: \"120\" },\n          { label: \"变电站\", prop: \"bdzmcs\", minWidth: \"120\" },\n          { label: \"通知人\", prop: \"tzrxm\", minWidth: \"80\" },\n          { label: \"通知时间\", prop: \"tzsj\", minWidth: \"140\" },\n          { label: \"下令人\", prop: \"xlrmc\", minWidth: \"80\" },\n          { label: \"下令时间\", prop: \"xlsj\", minWidth: \"140\" },\n          { label: \"操作任务\", prop: \"czrw\", minWidth: \"160\" },\n          { label: \"接令人\", prop: \"jlrxm\", minWidth: \"80\" },\n          { label: \"预备令接令人\", prop: \"ybljlrxm\", minWidth: \"100\" },\n          { label: \"回令时间\", prop: \"hlsj\", minWidth: \"140\" }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      tzrOrXlrList: [],\n      jlrList: [],\n      alljlrList: [],\n      sprList: []\n    };\n  },\n  async mounted() {\n    //获取token\n    this.header.token = getToken();\n    //变电站下拉框\n    this.getFgsOptions();\n    this.getAllBdzSelectList();\n    this.tzrOrXlrList = await this.getGroupUsers(61, \"\");\n    this.alljlrList = await this.getGroupUsers(62, \"\");\n    this.sprList = await this.getGroupUsers(13, \"\");\n    await this.getData();\n  },\n  methods: {\n    /**\n     * 获取分公司下拉数据\n     */\n    getFgsOptions() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.ssgsOptionsDataList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"fgs\") {\n            return (item.options = this.ssgsOptionsDataList);\n          }\n        });\n      });\n    },\n    //导出\n    exportFun() {\n      exportExcel(this.params, \"操作命令信息\");\n    },\n    //工作流回传数据\n    async todoResult(data) {\n      let row = {\n        objId: data.businessKey,\n        isStart: 1,\n        isBack: data.processType === \"rollback\" ? 1 : 0\n      };\n      row.status = \"1\";\n      row.bzspr = data.nextUser;\n      let { code } = await updateById(row);\n      if (code === \"0000\") {\n        await this.getData();\n      } else {\n        this.$message.warning(\"操作失败\");\n      }\n    },\n    //关闭弹窗\n    closeActiviti() {\n      if (this.$refs.activiti.$refs.form) {\n        this.$refs.activiti.$refs.form.resetFields();\n      }\n      this.isShow = false;\n    },\n    getAllBdzSelectList() {\n      getBdzSelectList({}).then(res => {\n        this.bdzList = res.data;\n      });\n    },\n    //所属公司change事件\n    async handleFgsChange(fgsValue) {\n      //清空之前得选中值\n      this.wzDataListOptions = [];\n      this.$set(this.form, \"bdzmc\", \"\");\n      this.$set(this.form, \"tzr\", \"\");\n      this.jlrList = [];\n      this.$set(this.form, \"jlr\", \"\");\n      this.$set(this.form, \"ybljlr\", \"\");\n      //获取变电站方法\n      await this.fgsChange(fgsValue);\n    },\n    async fgsChange(fgsValue) {\n      //获取变电站方法\n      this.getBdzSelectList(fgsValue);\n      this.jlrList = await this.getGroupUsers(62, fgsValue);\n    },\n    handleYlChange() {\n      this.titleyl = \"查看操作项目\";\n      this.yl = true;\n    },\n    replaceStr() {\n      this.propTableData.colFirst.forEach(item => {\n        item.czrw = item.czrw.replaceAll(\n          this.replaceData.oldStr,\n          this.replaceData.newStr\n        );\n      });\n    },\n    //改造后的上传多个图片文件\n    uploadForm() {\n      var newUrl = []; //用来存放当前未改动过的图片url,此图片不进行删除处理，其余当前业务id下面的图片数据将进行删除\n      var imageType = [\"png\", \"jpg\"];\n      const formData = new FormData();\n      // 因为要传一个文件数组过去，所以要循环append\n      this.imgList.forEach(file => {\n        if (file.raw == undefined) {\n          newUrl.push(file.url);\n        }\n        formData.append(\"files\", file.raw);\n      });\n      formData.append(\"businessId\", this.uploadImgData.businessId); // 自定义参数\n      formData.append(\"newUrl\", newUrl); // 未改动过的图片url\n      formData.append(\"type\", imageType); // 未改动过的图片url\n      api\n        .requestPost(\"/isc-api/file/uploadFiles\", formData, 1)\n        .then(res => {\n          // 清空图片列表（一定要清空，否则上传成功后还是会调用handleChange（）函数，上传成功后列表中还存在图片）\n          this.imgList = [];\n          //查询接口数据\n          this.getData();\n        })\n        .catch(res => {\n          this.$message.success(\"图片上传失败！\");\n        });\n    },\n    // 选择文件时，往fileList里添加\n    handleChange(file, fileList) {\n      this.imgList = fileList;\n    },\n    handleProgress(event, file, fileList) {},\n    //图片移除\n    handleRemove(file, fileList) {\n      this.imgList = fileList;\n    },\n    //图片放大\n    handlePictureCardPreview(file) {\n      this.dialogImageUrl = file.url;\n      this.imgDialogVisible = true;\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        this.loading = true;\n        param.mySorts = [{ prop: \"updateTime\", asc: false }];\n        const { data, code } = await getList(param);\n        if (code === \"0000\") {\n          for (let i of data.records) {\n            i.fgsmc = this.formatSsgs(i.fgs);\n            i.tzrxm = this.formatXlrOrTzr(i.tzr);\n            i.xlrxm = this.formatXlrOrTzr(i.xlr);\n            i.jlrxm = this.formatJlr(i.jlr);\n            i.ybljlrxm = this.formatJlr(i.ybljlr);\n          }\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          this.loading = false;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n\n    //新增按钮\n    async getInster() {\n      this.title = \"变电倒闸操作命令增加\";\n      this.isDisabled = false;\n      this.form = { fgs: this.$store.getters.deptId.toString() };\n      await this.fgsChange(this.form.fgs);\n      this.form.status = \"0\";\n      this.isShowDetails = true;\n    },\n    //修改按钮\n    async getUpdate(row) {\n      this.title = \"变电倒闸操作命令修改\";\n      this.isDisabled = false;\n      this.form = { ...row };\n      await this.fgsChange(this.form.fgs);\n      this.isShowDetails = true;\n    },\n\n    //详情按钮\n    async getDetails(row) {\n      this.title = \"变电倒闸操作命令详情\";\n      this.form = { ...row };\n      await this.fgsChange(this.form.fgs);\n      this.isDisabled = true;\n      this.isShowDetails = true;\n    },\n    /**\n     * 操作票开票按钮\n     * */\n    async createCzp() {\n      await this.$refs[\"form\"].validate(async valid => {\n        if (valid) {\n          try {\n            let { code, data } = await saveOrUpdate(this.form);\n            if (code === \"0000\") {\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.isShowDetails = false;\n              await this.getData();\n              //操作票弹出框\n              this.isDisabledCzp = true;\n              this.propTableData.colFirst = [];\n              //清除校验提示\n              this.$nextTick(function() {\n                this.$refs[\"formCzp\"].clearValidate();\n              });\n              this.formCzp = {};\n              //变电\n              this.formCzp.lx = 2;\n              this.formCzp.status = \"0\";\n              this.formCzp.czml = data.objId;\n              this.formCzp.bdzmc = data.bdzmc;\n              if (data.xlr) {\n                this.formCzp.xlr = data.xlr;\n              }\n              this.formCzp.czrw = data.czrw;\n              this.formCzp.fgs = data.fgs;\n              this.formCzp.bm = data.bh;\n              this.isDisabled = true;\n              this.isCzpShowDetails = true;\n            }\n          } catch (e) {\n            console.log(e);\n          }\n        }\n      });\n    },\n    /**\n     * 开办结票\n     * */\n    createBjp() {\n      this.isBjpShowDetails = true;\n      this.isShowDetails = false;\n      this.formBjp.bdzmc = this.form.bdzmc;\n      this.formBjp.xlr = this.form.xlr;\n      this.formBjp.czrw = this.form.czrw;\n      this.formBjp.fgs = this.form.fgs;\n      this.formBjp.status = \"4\";\n      this.formBjp.lx = 2;\n      this.formBjp.czml = this.form.objId;\n    },\n    async saveRow() {\n      await this.$refs[\"form\"].validate(async valid => {\n        if (valid) {\n          try {\n            let { code } = await saveOrUpdate(this.form);\n            if (code === \"0000\") {\n              this.$message.success(\"操作成功\");\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.isShowDetails = false;\n              await this.getData();\n            }\n          } catch (e) {\n            console.log(e);\n          }\n        }\n      });\n    },\n    //删除按钮\n    deleteRow(row) {\n      this.form = { ...row };\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove([this.form.objId]).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {});\n    },\n    //关闭弹窗\n    close() {\n      this.isDxpShowDetails = false;\n      this.isLspShowDetails = false;\n      this.isShowDetails = false;\n    },\n    //选择行\n    selectChange(selection) {\n      // this.ids = selection.map(item => item.objId);\n      // this.single = selection.length !== 1\n      // this.multiple = !selection.length\n      this.selectData = selection;\n    },\n    //表格新增\n    listFirstAdd() {\n      let row = {\n        xh: \"\",\n        czrw: \"\",\n        sfwc: \"\",\n        uuid: getUUID()\n      };\n      this.propTableData.colFirst.push(row);\n      this.propTableData.sel = row;\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //表格删除\n    listFirstDel(row) {\n      if (row.objId) {\n        this.ids.push(row.objId);\n      }\n      this.propTableData.colFirst = this.propTableData.colFirst.filter(\n        item => item.uuid !== row.uuid\n      );\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //操作票关闭弹窗\n    closeCzp() {\n      this.isCzpShowDetails = false;\n      this.isBjpShowDetails = false;\n    },\n\n    //操作票确定按钮\n    saveRowCzp() {\n      this.formCzp.colFirst = this.propTableData.colFirst;\n      this.formCzp.objIdList = this.ids;\n      let tableValid = this.propTableData.colFirst.some(item => !item.czrw);\n      if (tableValid) {\n        this.$message.error(\"操作项目存在空项，请检查\");\n      }\n      this.$refs[\"formCzp\"].validate(async valid => {\n        if (valid && !tableValid) {\n          saveOrUpdateCzp(this.formCzp).then(res => {\n            if (res.code === \"0000\") {\n              this.$message.success(\"操作成功\");\n              this.isCzpShowDetails = false;\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            }\n          });\n        }\n      });\n    },\n\n    //直接上报操作票\n    submitCzp() {\n      this.formCzp.colFirst = this.propTableData.colFirst;\n      this.formCzp.objIdList = this.ids;\n      let tableValid = this.propTableData.colFirst.some(item => !item.czrw);\n      if (tableValid) {\n        this.$message.error(\"操作项目存在空项，请检查\");\n      }\n      this.$refs[\"formCzp\"].validate(async valid => {\n        if (valid && !tableValid) {\n          saveOrUpdateCzp(this.formCzp).then(res => {\n            if (res.code === \"0000\") {\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n              let data = res.data;\n              this.isCzpShowDetails = false;\n              this.processData.variables.pass = true;\n              this.processData.businessKey = data.objId;\n              this.processData.processType = \"complete\";\n              this.activitiOption.title = \"提交\";\n              this.processData.defaultFrom = true;\n              this.processData.rylx = \"班组审核人\";\n              this.processData.dw = data.fgs;\n              this.processData.personGroupId = 13;\n              this.processData.routePath =\n                \"/bdgl/bddzcz/dagangOilfield/czpgl/bddzcz/dzczp\";\n              this.isShow = true;\n            }\n          });\n        }\n      });\n    },\n\n    //办结票确定按钮\n    saveRowBjp() {\n      this.$refs[\"formBjp\"].validate(async valid => {\n        if (valid) {\n          saveOrUpdateCzp(this.formBjp).then(res => {\n            if (res.code === \"0000\") {\n              this.uploadImgData.businessId = res.data.objId;\n              //开始上传图片\n              this.uploadForm();\n              this.$message.success(\"操作成功\");\n              this.isBjpShowDetails = false;\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            }\n          });\n        }\n      });\n    },\n    /**\n     * -----------------------------------典型票库---------------------------------------\n     * */\n    //获取典型操作票\n    getDxpkLists() {\n      this.title = \"典型票库查询\";\n      this.isDxpShowDetails = true;\n      this.isDisabled = false;\n      this.isShowDxp = true;\n      this.queryParams.sbmc = this.form.bdzmc;\n      this.handleQuery();\n    },\n    async changeMx(row) {\n      try {\n        const { data, code } = await getDxpkList({ objId: row.objId });\n        if (code === \"0000\") {\n          this.propTableDataDxpMx.colFirst = data;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //查询条件\n    async handleQuery(params) {\n      let param = { ...params, ...this.queryParams };\n      getLists(param).then(response => {\n        this.propTableDataDxp.tableData = response.data.records;\n        this.propTableDataDxp.pager.total = response.data.total;\n      });\n    },\n    //重置条件\n    resetQuery() {\n      this.queryParams = {};\n      this.handleQuery();\n    },\n    //典型票库确认按钮\n    saveRowDxp() {\n      this.propTableData.colFirst = this.propTableDataDxpMx.colFirst;\n      this.propTableData.colFirst.forEach(e => {\n        e.uuid = getUUID();\n      });\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n      this.isDxpShowDetails = false;\n    },\n    /**\n     * -----------------------------------历史票库---------------------------------------\n     * */\n    //获取历史操作票\n    getLspkList() {\n      this.title = \"历史票库查询\";\n      this.isLspShowDetails = true;\n      this.isDisabled = false;\n      this.isShowLsp = true;\n      this.queryParamsLsp.bdzmc = this.form.bdzmc;\n      this.handleQueryLsp();\n    },\n    // 当点击行时，传入参数查询\n    async changeLspMx(row) {\n      try {\n        const { data, code } = await getLspkList(row.objId);\n        if (code === \"0000\") {\n          this.propTableDataLspMx.colFirst = data;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //历史票库确认按钮\n    saveRowLsp() {\n      this.propTableData.colFirst = this.propTableDataLspMx.colFirst;\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n      this.propTableData.colFirst.forEach(e => {\n        e.sfwc = false;\n        e.uuid = getUUID();\n      });\n      this.isLspShowDetails = false;\n    },\n    //查询条件\n    handleQueryLsp() {\n      getListLsp(this.queryParamsLsp).then(response => {\n        this.propTableDataLsp.colFirst = response.data.records;\n      });\n    },\n    //重置条件\n    resetQueryLsp() {\n      this.queryParamsLsp = {\n        status: \"4\",\n        lx: 2\n      };\n      this.handleQueryLsp();\n    },\n    /**\n     * 获取变电站下拉框数据\n     */\n    getBdzSelectList(fgs) {\n      getBdzSelectList({ ssdwbm: fgs }).then(res => {\n        this.wzDataListOptions = res.data;\n      });\n    },\n    //下拉框change事件\n    handleEvent(val, eventValue) {\n      if (val.label === \"fgs\") {\n        getBdzSelectList({ ssdwbm: val.value.toString() }).then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value === \"bdzmc\") {\n              this.$set(eventValue, \"bdzmc\", \"\");\n              return (item.options = res.data);\n            }\n          });\n        });\n      }\n    },\n    filterReset() {\n      this.params = {\n        //变电\n        lx: 2\n      };\n    },\n    formatSsgs(ssgs) {\n      let pageOrganizationSelectedList = JSON.parse(JSON.stringify(this.ssgsOptionsDataList))\n      pageOrganizationSelectedList.push({label: '港东变电分公司', value: '3002'})\n      pageOrganizationSelectedList.push({label: '港中变电分公司', value: '3003'})\n      if (ssgs) {\n        let filter = pageOrganizationSelectedList.filter(g => g.value === ssgs);\n        if (filter.length > 0) {\n          return filter[0].label;\n        } else {\n          return \"\";\n        }\n      } else {\n        return \"\";\n      }\n    },\n    getGroupUsers(personGroupId, deptId) {\n      return getUsers({\n        personGroupId: personGroupId,\n        deptId: deptId,\n        deptName: \"\"\n      }).then(res => {\n        return res.data;\n      });\n    },\n    formatXlrOrTzr(xlr) {\n      if (xlr) {\n        let filter = this.tzrOrXlrList.filter(g => g.userName === xlr);\n        if (filter.length > 0) {\n          return filter[0].nickName;\n        } else {\n          return \"\";\n        }\n      } else {\n        return \"\";\n      }\n    },\n\n    formatJlr(jlr) {\n      if (jlr) {\n        let filter = this.alljlrList.filter(g => g.userName === jlr);\n        if (filter.length > 0) {\n          return filter[0].nickName;\n        } else {\n          return \"\";\n        }\n      } else {\n        return \"\";\n      }\n    },\n    //导出word\n    exportWord() {\n      let params = {\n        data: this.params,\n        url: \"bzBddzczml\"\n      };\n      let fileName = \"变电倒闸操作命令记录\";\n      if (!this.selectData.length > 0) {\n        params.data = this.params;\n        exportWordByparams(params, fileName);\n      } else {\n        params.data = this.selectData;\n        exportWordByselection(params, fileName);\n      }\n    },\n    changeZslOrYbl(val) {\n      if (val) {\n        this.$set(this.form, \"tzsj\", null);\n        this.$set(this.form, \"tzr\", \"\");\n        this.$set(this.form, \"ybljlr\", \"\");\n      } else {\n        this.$set(this.form, \"xlsj\", null);\n        this.$set(this.form, \"xlr\", \"\");\n        this.$set(this.form, \"jlr\", \"\");\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.card-style {\n  background-color: #e0eff2;\n  line-height: 3vh;\n  border-radius: 3px;\n  font-weight: 800;\n  padding-left: 1vw;\n  margin-bottom: 3vh;\n}\n\n/*控制input输入框边框是否显示*/\n.elInput >>> .el-input__inner {\n  border: 0;\n}\n\n.box-card {\n  margin-bottom: 2vh !important;\n}\n</style>\n"]}]}