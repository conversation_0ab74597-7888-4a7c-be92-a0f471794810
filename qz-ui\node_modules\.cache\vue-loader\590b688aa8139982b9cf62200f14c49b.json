{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sympk\\sympk.vue?vue&type=style&index=0&id=0fd75650&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sympk\\sympk.vue", "mtime": 1706897323740}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouYm94LWNhcmQgewogIG1hcmdpbi1ib3R0b206IDE1cHg7CgogIC5lbC1jYXJkX19oZWFkZXIgewogICAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDIzNSwgMjQ1LCAyNTUpICFpbXBvcnRhbnQ7CiAgfQp9CgouaXRlbSB7CiAgd2lkdGg6IDIwMHB4OwogIGhlaWdodDogMTQ4cHg7CiAgZmxvYXQ6IGxlZnQ7Cn0KCi5oZWFkLWNvbnRhaW5lciB7CiAgbWFyZ2luOiAwIGF1dG87CiAgd2lkdGg6IDk4JTsKICBtYXgtaGVpZ2h0OiA3OXZoOwogIG92ZXJmbG93OiBhdXRvOwp9Cg=="}, {"version": 3, "sources": ["sympk.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAq2BA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "sympk.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sympk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row :gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card class=\"aside_height\" shadow=\"never\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备类型</span>\n          </div>\n          <div style=\" overflow: auto;height: 84vh\">\n            <el-tree\n              highlight-current\n              id=\"tree\"\n              :props=\"props\"\n              :load=\"loadNode\"\n              lazy\n              @node-click=\"handleNodeClick\"\n              :default-expanded-keys=\"['1']\"\n            />\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 180, itemWidth: 300 }\"\n          @handleReset=\"getReset\"\n          @handleEvent=\"handleEvent\"\n        />\n        <el-white>\n          <el-white class=\"button-group\">\n            <el-button class=\"mb8\" @click=\"addButton\" type=\"primary\" icon=\"el-icon-plus\">\n              新增\n            </el-button>\n            <!--<el-button class=\"mb8\" @click=\"deleteButton\" type=\"danger\" icon=\"el-icon-delete\">-->\n            <!--  删除-->\n            <!--</el-button>-->\n            <!--<el-button class=\"mb8\" v-show=\"false\" @click=\"viewButton\" type=\"primary\" icon=\"el-icon-circle-plus-outline\">-->\n            <!--  定义详情-->\n            <!--</el-button>-->\n          </el-white>\n          <comp-table :table-and-page-info=\"tableAndPageInfo\" @update:multipleSelection=\"handleSelectionChange\"  height=\"68.8vh\">\n            <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\"\n                             width=\"160\"\n                             :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button @click=\"updateDetails(scope.row)\" v-show=\"scope.row.createUser == currentUser\" type=\"text\"\n                           size=\"small\"  title=\"修改\"  class='el-icon-edit'\n                >\n                </el-button>\n                <el-button @click=\"getDetails(scope.row)\" type=\"text\" size=\"small\" title=\"详情\" class=\"el-icon-view\"></el-button>\n                <el-button @click=\"getNameplateInfo(scope.row)\" v-show=\"scope.row.createUser == currentUser\" type=\"text\"\n                           size=\"small\" title=\"定义详情\" class=\"el-icon-edit-outline\"\n                >\n                </el-button>\n                <el-button @click=\"deleteButton(scope.row)\" v-show=\"scope.row.createUser == currentUser\" type=\"text\"\n                           size=\"small\" title=\"删除\" class=\"el-icon-delete\"\n                >\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n    <!-- 新增、修改、详情界面 -->\n    <el-dialog :title=title :visible.sync=\"isShowDetails\" width=\"40%\" @close=\"handleClose\" v-dialogDrag>\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"铭牌名称：\" prop=\"mpmc\">\n              <el-input placeholder=\"铭牌名称\" v-model=\"form.mpmc\" :disabled=\"isDisabled\" style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"专业：\" prop=\"zy\">\n              <el-select placeholder=\"专业\" v-model=\"form.zy\" :disabled=\"isDisabled\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否分相：\" prop=\"sffx\">\n              <el-select placeholder=\"是否分相\" v-model=\"form.sffx\" :disabled=\"isDisabled\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in sffxOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button v-if=\"title==='新增' || title==='修改'\" type=\"primary\" @click=\"save\">确 认</el-button>\n      </div>\n    </el-dialog>\n    <!-- 定义详情界面 -->\n    <el-dialog title=\"定义铭牌详情\" :visible.sync=\"isShowDyDetails\" width=\"75%\" v-dialogDrag>\n      <el-form label-width=\"120px\" ref=\"dyForm\" :model=\"dyForm\">\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"6\">\n            <el-row>\n              <h4\n                style=\"margin-left: 15px;font-family: PingFangSC-Regular;font-size:14px;color:#333333;text-align:center;font-weight: bold;\"\n              >A表格</h4>\n            </el-row>\n            <el-row>\n              <el-form-item label=\"行:\" prop=\"a_hs\">\n                <el-col :span=\"20\">\n                  <el-input v-model=\"dyForm.a_hs\" style=\"width: 100%;\"></el-input>\n                </el-col>\n              </el-form-item>\n            </el-row>\n            <el-row>\n              <el-form-item label=\"列:\" prop=\"a_ls\">\n                <el-col :span=\"20\">\n                  <el-input v-model=\"dyForm.a_ls\" style=\"width: 100%;\"></el-input>\n                </el-col>\n              </el-form-item>\n            </el-row>\n            <el-row style=\"text-align:center;\">\n              <el-button type=\"primary\" @click=\"saveTableA\">创建A表格</el-button>\n            </el-row>\n            <hr style=\"height:1px;border:none;border-top:1px ridge #DBDBDB;width: 100%;\">\n            <el-row>\n              <h4\n                style=\"margin-left: 15px;font-family: PingFangSC-Regular;font-size:14px;color:#333333;text-align:center;font-weight: bold;\"\n              >B表格</h4>\n            </el-row>\n            <el-row>\n              <el-form-item label=\"行:\" prop=\"b_hs\">\n                <el-col :span=\"20\">\n                  <el-input v-model=\"dyForm.b_hs\" style=\"width: 100%;\"></el-input>\n                </el-col>\n              </el-form-item>\n            </el-row>\n            <el-row>\n              <el-form-item label=\"列:\" prop=\"b_ls\">\n                <el-col :span=\"20\">\n                  <el-input v-model=\"dyForm.b_ls\" style=\"width: 100%;\"></el-input>\n                </el-col>\n              </el-form-item>\n            </el-row>\n            <el-row style=\"text-align:center;\">\n              <el-button type=\"primary\" @click=\"saveTableB\">创建B表格</el-button>\n            </el-row>\n            <hr style=\"height:1px;border:none;border-top:1px ridge #DBDBDB;width: 100%;\">\n            <el-row>\n              <h4\n                style=\"margin-left: 15px;font-family: PingFangSC-Regular;font-size:14px;color:#333333;text-align:center;font-weight: bold;\"\n              >单元格操作</h4>\n            </el-row>\n            <el-row>\n              <el-form-item label=\"行跨度:\" prop=\"rowSpanNum\">\n                <el-col :span=\"20\">\n                  <el-input v-model=\"dyForm.rowSpanNum\" style=\"width: 100%;\"></el-input>\n                </el-col>\n              </el-form-item>\n            </el-row>\n            <el-row>\n              <el-form-item label=\"列跨度:\" prop=\"colSpanNum\">\n                <el-col :span=\"20\">\n                  <el-input v-model=\"dyForm.colSpanNum\" style=\"width: 100%;\"></el-input>\n                </el-col>\n              </el-form-item>\n            </el-row>\n            <el-row style=\"text-align:center;\">\n              <el-button type=\"primary\" @click=\"saveSpan\">保存</el-button>\n              <el-button type=\"danger\" @click=\"reset\">清除</el-button>\n            </el-row>\n            <hr style=\"height:1px;border:none;border-top:1px ridge #DBDBDB;width: 100%;\">\n            <el-row style=\"text-align:center;\">\n              <el-button @click=\"editCellProperties\">编辑单元格属性</el-button>\n              <el-button @click=\"resetCellProperties\">重置单元格内容</el-button>\n            </el-row>\n            <hr style=\"height:1px;border:none;border-top:1px ridge #DBDBDB;width: 100%;\">\n            <el-row style=\"text-align:center;\">\n              <p style=\"color: red;\">灰色单元格:静态文本</p>\n              <p style=\"color: red;\">白色单元格:动态属性</p>\n              <p style=\"color: red;\">浅灰色单元格:只读的动态属性</p>\n              <p style=\"color: red;\">浅绿色单元格:还未定义属性</p>\n              <p style=\"color: red;\">浅黄色单元格:当前选中的单元格</p>\n            </el-row>\n          </el-col>\n          <el-col :span=\"18\">\n            <el-row>\n              <h4\n                style=\"margin-left: 15px;font-family: PingFangSC-Regular;font-size:14px;color:#333333;text-align:left;font-weight: bold;\"\n              >A表格</h4>\n            </el-row>\n            <el-row>\n              <h3 v-html=\"dyForm.title\"\n                  style=\"font-weight: bold;margin: 0px 0px 0px 0px;height: 45px;background-color:#d5ddfd;line-height:2.1;font-size: 21px;text-align:center;\"\n              ></h3>\n            </el-row>\n            <el-row>\n              <el-col :span=\"24\">\n                <table v-html=\"tableDataA\" border=\"1\" style=\"width: 100%;\" @cell-click=\"handle\">\n                </table>\n              </el-col>\n            </el-row>\n            <hr style=\"height:1px;border:none;border-top:1px ridge #DBDBDB;width: 100%;\">\n            <el-row>\n              <h4\n                style=\"margin-left: 15px;font-family: PingFangSC-Regular;font-size:14px;color:#333333;text-align:left;font-weight: bold;\"\n              >B表格</h4>\n            </el-row>\n            <el-row>\n              <el-col :span=\"24\">\n                <table v-html=\"tableDataB\" border=\"1\" style=\"width: 100%;\">\n                </table>\n              </el-col>\n            </el-row>\n          </el-col>\n        </el-row>\n      </el-form>\n    </el-dialog>\n\n    <el-dialog\n      :visible.sync=\"isShowMpInfo\"\n      v-if=\"isShowMpInfo\"\n      v-dialogDrag\n      width=\"68%\"\n      title=\"铭牌内容\"\n      :append-to-body=\"true\"\n      @close=\"closeInfoDialog\"\n    >\n      <!--        <symp-info\n                :mp-data=\"rowData\"\n                @closeInfoDialog=\"closeInfoDialog\"></symp-info>-->\n      <mpxq-info :mp-data=\"rowData\" :mx-data.sync=\"mxData\"\n                 @closeInfoDialog=\"closeInfoDialog\"\n      />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n\nimport {\n  getDeviceClassTreeNodeByPid,\n  getPageDataList,\n  getTable,\n  remove,\n  saveOrUpdate,\n  updateTableNum\n} from '@/api/dagangOilfield/bzgl/sympk/sympk'\nimport SympInfo from '@/views/dagangOilfield/bzgl/sympk/sympInfo'\nimport MpxqInfo from '@/views/dagangOilfield/bzgl/sympk/mpxqInfo'\n\nexport default {\n  name: 'sympk',\n  components: { MpxqInfo, SympInfo },\n  inject: ['reload'],//inject注入根组件的reload方法\n  data() {\n    return {\n      params: {},\n      //筛选框\n      filterInfo: {\n        data: {\n          mpmc: '',\n          zy: '',\n          sffx: ''\n        },\n        fieldList: [\n          {\n            label: '铭牌名称',\n            value: 'mpmc',\n            type: 'input',\n            clearable: true\n          },\n          {\n            label: '是否分相',\n            value: 'sffx',\n            type: 'select',\n            options: [\n              {label: '是', value: '1'},\n              {label: '否', value: '0'}\n            ],\n            clearable: true\n          }\n        ]\n      },\n      currentUser: this.$store.getters.name,\n      ids: [],\n      props: {\n        label: 'name',\n        children: 'zones',\n        isLeaf: 'leaf'\n      },\n      //专业下拉框数据\n      options: [{ label: '输电', value: 'SD' }, { label: '变电', value: 'BD' }],\n      //是否分相下拉框数据\n      sffxOptions: [{ label: '是', value: '1' }, { label: '否', value: '0' }],\n      form: {\n        objId: undefined,\n        sblxbm: undefined,\n        mpmc: undefined,\n        zy: undefined,\n        sffx: '',\n        isMpSyxm: 0\n      },\n      dyForm: {\n        obj_id: undefined,\n        a_hs: 0,\n        a_hsOld: 0,\n        a_ls: 0,\n        a_lsOld: 0,\n        b_hs: 0,\n        b_hsOld: 0,\n        b_ls: 0,\n        b_lsOld: 0,\n        rowSpanNum: 1,\n        colSpanNum: 1,\n        lbbs: undefined,\n        title: undefined\n      },\n      isShowDetails: false,\n      isShowDyDetails: false,\n      title: '',\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: '铭牌名称', prop: 'mpmc', minWidth: '180' },\n          { label: '专业', prop: 'zyName', minWidth: '200' },\n          { label: '是否分相', prop: 'sffxName', minWidth: '200' }\n          // {\n          //   prop: 'operation',\n          //   label: '操作',\n          //   minWidth: '100px',\n          //   style: { display: 'block' },\n          //   //操作列固定再右侧\n          //   fixed: 'right',\n          //   operation: [\n          //     { name: '修改', clickFun: this.updateDetails },\n          //     { name: '详情', clickFun: this.getDetails },\n          //     { name: '定义详情', clickFun: this.getNameplateInfo }\n          //   ]\n          // }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      //删除是否可用\n      multipleSensor: true,\n      //查询参数\n      queryParams: {\n        zy: '',\n        sblxbm: undefined,\n        pageNum: 1,\n        pageSize: 10,\n        isMpSyxm: 0\n      },\n      isDisabled: false,\n      rules: {\n        mpmc: [{ required: true, message: '请填写铭牌名称', trigger: 'blur' }],\n        zy: [{ required: true, message: '请选择专业', trigger: 'blur' }],\n        sffx: [{ required: true, message: '请选择是否分相', trigger: 'change' }]\n      },\n      selection: [], //记录最后一次选中的行数据\n      tableDataA: undefined,  //表格A数据\n      tableDataB: undefined,  //表格B数据\n      isShowMpInfo: false,\n      //选中行数据\n      rowData: {},\n      //设备类型编码\n      sblxbm: '',\n      mxData: []//表格明细数据\n    }\n  },\n  watch: {},\n  created() {\n    this.getList()\n\n  },\n  methods: {\n    getData(param){\n      this.getList(param)\n    },\n    handleEvent(var1, var2){\n      this.params = var2\n    },\n    //定义重置方法\n    getReset() {\n      this.params = {}\n      this.getList()\n    },\n    //树节点点击事件\n    handleNodeClick(data) {\n      this.form.sblxbm = data.code\n      this.sblxbm = data.code\n      this.queryParams.sblxbm = data.code\n      if (data.pid != 'sb'){\n        this.queryParams.zy = data.pid.substring(0, 2)\n      }\n      this.getList()\n    },\n    /**\n     * 表格多选框\n     */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.objId)\n      this.selection = selection\n    },\n    //添加按钮\n    addButton() {\n      if (this.form.sblxbm != undefined) {\n        this.isShowDetails = true\n        this.isDisabled = false\n        this.form.objId = undefined\n        this.form.mpmc = undefined\n        this.form.zy = undefined\n        this.title = '新增'\n      } else {\n        this.$message.warning('请选择左侧树节点新增数据！')\n        return\n      }\n    },\n    close() {\n      this.isShowDetails = false\n    },\n    updateDetails(row) {\n      this.title = '修改'\n      this.isShowDetails = true\n      this.form = row\n      this.isDisabled = false\n    },\n    getDetails(row) {\n      this.title = '详情'\n      this.isShowDetails = true\n      this.form = row\n      this.isDisabled = true\n    },\n    save() {\n      this.$refs.form.validate((valid) => {\n        if (valid) {\n          saveOrUpdate(this.form).then(res => {\n            if (res.code == '0000') {\n              this.$message.success('保存成功！')\n              this.isShowDetails = false\n              this.getList()\n            }\n          })\n        } else {\n          this.$message.error('请输入所有必填字段！')\n          return false\n        }\n      })\n    },\n    //删除按钮\n    deleteButton(row) {\n      this.form = row\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        remove([this.form.objId]).then(res => {\n          this.$message({\n            type: 'success',\n            message: '删除成功!'\n          })\n          this.getList()\n        })\n      })\n    },\n    //定义详情按钮\n    viewButton() {\n      if (this.ids.length == 1) {\n        this.isShowDyDetails = true\n        this.dyForm.obj_id = this.ids[0]\n        this.dyForm.a_hs = this.selection[0].a_hs\n        this.dyForm.a_hsOld = this.selection[0].a_hs\n        this.dyForm.a_ls = this.selection[0].a_ls\n        this.dyForm.a_lsOld = this.selection[0].a_ls\n        this.dyForm.b_hs = this.selection[0].b_hs\n        this.dyForm.b_hsOld = this.selection[0].b_hs\n        this.dyForm.b_ls = this.selection[0].b_ls\n        this.dyForm.b_lsOld = this.selection[0].b_ls\n        this.dyForm.title = this.selection[0].mpmc\n        //A表格加载\n        if (this.selection[0].a_hs != '' && this.selection[0].a_ls != '' && this.selection[0].a_hs != 0 && this.selection[0].a_ls != 0) {\n          this.dyForm.lbbs = 'A'\n          getTable(this.dyForm).then(res => {\n            var str = ''\n            for (var i = 0; i < this.selection[0].a_hs; i++) {\n              var temp = '<tr>'\n              for (var k = 0; k < res.data.length; k++) {\n                if (i == res.data[k].rowindex) {\n                  var nrbs = res.data[k].nrbs\n                  var nrlx = res.data[k].nrlx\n                  //定义颜色\n                  var color = ''\n                  if (nrlx == '1' || nrlx == '2' || nrlx == '3' || nrlx == '4') {//白色单元格:动态属性\n                    color = ' bgcolor=\"white\" '\n                  } else if (nrlx == '5') {//灰色单元格:静态文本\n                    color = ' bgcolor=\"#EEEEE0\" '\n                  } else if (nrlx == '' || nrlx == undefined || nrlx == 'undefined') {//浅绿色单元格:还未定义属性\n                    color = ' bgcolor=\"#C7EDCC\" '\n                  }\n                  if (nrbs == '' || nrbs == 'undefined' || nrbs == undefined) {\n                    nrbs = '-'\n                  } else if (nrbs.indexOf('|') == -1 || nrbs == '℃' || nrbs == '/%') {\n\n                  } else if (nrbs.indexOf('|') != -1) {\n                    nrbs = nrbs.replace(/[^\\u4e00-\\u9fa5]/gi, '')\n                  }\n\n                  if (res.data[k].colspan != '1' || res.data[k].rowspan != '1') {\n                    temp += '<td tabindex=\\'-1\\'' + color + 'colspan=\\'' + res.data[k].colspan + '\\' rowspan=\\'' + res.data[k].rowspan + '\\' id=\\'' + res.data[k].obj_id + '\\'>' + nrbs + '</td>'\n                  } else {\n                    temp += '<td tabindex=\\'-1\\'' + color + ' id=\\'' + res.data[k].obj_id + '\\' @click=\\'tdclick(\\'A\\',this)\\'>' + nrbs + '</td>'\n                  }\n                }\n              }\n              temp += '</tr>'\n              str += temp\n            }\n            this.tableDataA = undefined\n            this.tableDataA = str\n          })\n        } else {\n          this.tableDataA = undefined\n        }\n        //B表格加载\n        if (this.selection[0].b_hs != '' && this.selection[0].b_ls != '' && this.selection[0].b_hs != 0 && this.selection[0].b_ls != 0) {\n          this.dyForm.lbbs = 'B'\n          getTable(this.dyForm).then(res => {\n            var str = ''\n            for (var i = 0; i < this.selection[0].b_hs; i++) {\n              var temp = '<tr>'\n              for (var k = 0; k < res.data.length; k++) {\n                if (i == res.data[k].rowindex) {\n                  var nrbs = res.data[k].nrbs\n                  var nrlx = res.data[k].nrlx\n                  //定义颜色\n                  var color = ''\n                  if (nrlx == '1' || nrlx == '2' || nrlx == '3' || nrlx == '4') {//白色单元格:动态属性\n                    color = ' bgcolor=\"white\" '\n                  } else if (nrlx == '5') {//灰色单元格:静态文本\n                    color = ' bgcolor=\"#EEEEE0\" '\n                  } else if (nrlx == '' || nrlx == undefined || nrlx == 'undefined') {//浅绿色单元格:还未定义属性\n                    color = ' bgcolor=\"#C7EDCC\" '\n                  }\n                  if (nrbs == '' || nrbs == 'undefined' || nrbs == undefined) {\n                    nrbs = '-'\n                  } else if (nrbs.indexOf('|') == -1 || nrbs == '℃' || nrbs == '/%') {\n\n                  } else if (nrbs.indexOf('|') != -1) {\n                    nrbs = nrbs.replace(/[^\\u4e00-\\u9fa5]/gi, '')\n                  }\n\n                  if (res.data[k].colspan != '1' || res.data[k].rowspan != '1') {\n                    temp += '<td tabindex=\\'-1\\'' + color + 'colspan=\\'' + res.data[k].colspan + '\\' rowspan=\\'' + res.data[k].rowspan + '\\' id=\\'' + res.data[k].obj_id + '\\'>' + nrbs + '</td>'\n                  } else {\n                    temp += '<td tabindex=\\'-1\\'' + color + ' id=\\'' + res.data[k].obj_id + '\\'>' + nrbs + '</td>'\n                  }\n                }\n              }\n              temp += '</tr>'\n              str += temp\n            }\n            this.tableDataB = undefined\n            this.tableDataB = str\n          })\n        } else {\n          this.tableDataB = undefined\n        }\n      } else {\n        this.$message({\n          type: 'info',\n          message: '请选择一条数据!'\n        })\n      }\n    },\n    //查询列表\n    getList(params) {\n      this.queryParams = {...this.queryParams, ...params};\n      let param = {...this.queryParams,...params}\n      getPageDataList(param).then(res => {\n        this.tableAndPageInfo.tableData = res.data.records\n        this.tableAndPageInfo.pager.total = res.data.total\n\n        this.tableAndPageInfo.tableData.forEach(item => {\n          this.options.forEach(element => {\n            if (item.zy === element.value) {\n              item.zyName = element.label\n            }\n          })\n          this.sffxOptions.forEach(element => {\n            if (item.sffx === element.value) {\n              item.sffxName = element.label\n            }\n          })\n        })\n      })\n    },\n    //懒加载函数\n    loadNode(node, resolve) {\n      let TreeparamMap = {\n        pid: '',\n        spbLogo: ['输电设备', '变电设备','配电设备']\n      }\n      if (node.level === 0) {\n        TreeparamMap.pid = 'sb'\n        return this.getTreeNode(TreeparamMap, resolve)\n      }\n      setTimeout(() => {\n        TreeparamMap.pid = node.data.code\n        this.getTreeNode(TreeparamMap, resolve)\n      }, 500)\n\n    },\n    //获取树节点数据\n    getTreeNode(paramMap, resolve) {\n      getDeviceClassTreeNodeByPid(paramMap).then(res => {\n        let treeNodes = []\n        res.data.forEach(item => {\n          let node = {\n            name: item.name,\n            level: item.level,\n            id: item.id,\n            pid: item.pid,\n            leaf: false,\n            code: item.code\n          }\n          treeNodes.push(node)\n        })\n        resolve(treeNodes)\n      })\n    },\n    //创建A表格按钮功能\n    saveTableA() {\n      //判断填写的行数与列数，值是否符合标准\n      if (this.dyForm.a_hs <= 0 || this.dyForm.a_ls <= 0) {\n        this.$message({\n          type: 'info',\n          message: '不允许填写0或小于0的数!'\n        })\n        return\n      }\n      //判断行和列的变化情况\n      let row_differ = this.dyForm.a_hsOld - this.dyForm.a_hs  //对前后变化的行做差，判断是否增加或减少行\n      let col_differ = this.dyForm.a_lsOld - this.dyForm.a_ls  //对前后变化的行做差，判断是否增加或减少列\n      if (row_differ == 0 && col_differ == 0) {//行列无发生变化，不进行任何操作\n        return\n      } else if (row_differ > 0 || col_differ > 0) {//行或者列减少,提示用户是否删除\n        this.$confirm('确定删除行列?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          //设置类别标识\n          this.dyForm.lbbs = 'A'\n          updateTableNum(this.dyForm).then(res => {\n            if (res.code == '0000') {\n              this.reloadTable('A')\n              this.$message.success('删除成功！')\n              this.dyForm.a_hsOld = this.dyForm.a_hs\n              this.dyForm.a_lsOld = this.dyForm.a_ls\n            }\n          })\n        })\n      } else {\n        //设置类别标识\n        this.dyForm.lbbs = 'A'\n        updateTableNum(this.dyForm).then(res => {\n          if (res.code == '0000') {\n            this.reloadTable('A')\n            this.dyForm.a_hsOld = this.dyForm.a_hs\n            this.dyForm.a_lsOld = this.dyForm.a_ls\n          }\n        })\n      }\n    },\n    //创建B表格按钮功能\n    saveTableB() {\n      //判断填写的行数与列数，值是否符合标准\n      if (this.dyForm.b_hs <= 0 || this.dyForm.b_ls <= 0) {\n        this.$message({\n          type: 'info',\n          message: '不允许填写0或小于0的数!'\n        })\n        return\n      }\n      //判断行和列的变化情况\n      let row_differ = this.dyForm.b_hsOld - this.dyForm.b_hs  //对前后变化的行做差，判断是否增加或减少行\n      let col_differ = this.dyForm.b_lsOld - this.dyForm.b_ls  //对前后变化的行做差，判断是否增加或减少列\n      if (row_differ == 0 && col_differ == 0) {//行列无发生变化，不进行任何操作\n        return\n      } else if (row_differ > 0 || col_differ > 0) {//行或者列减少,提示用户是否删除\n        this.$confirm('确定删除行列?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          //设置类别标识\n          this.dyForm.lbbs = 'B'\n          updateTableNum(this.dyForm).then(res => {\n            if (res.code == '0000') {\n              this.reloadTable('B')\n              this.$message.success('删除成功！')\n              this.dyForm.b_hsOld = this.dyForm.b_hs\n              this.dyForm.b_lsOld = this.dyForm.b_ls\n            }\n          })\n        })\n      } else {\n        //设置类别标识\n        this.dyForm.lbbs = 'B'\n        updateTableNum(this.dyForm).then(res => {\n          if (res.code == '0000') {\n            this.reloadTable('B')\n            this.dyForm.b_hsOld = this.dyForm.b_hs\n            this.dyForm.b_lsOld = this.dyForm.b_ls\n          }\n        })\n      }\n    },\n    //重新加载表格\n    reloadTable(reload_table_flag) {\n      if (reload_table_flag == 'B') {\n        var rowNumB = this.dyForm.b_hs\n        getTable(this.dyForm).then(res => {\n          if (rowNumB != 0) {\n            var str1 = ''\n            for (var i = 0; i < rowNumB; i++) {\n              var temp = '<tr>'\n              for (var k = 0; k < res.data.length; k++) {\n                if (i == res.data[k].rowindex) {\n                  var nrbs = res.data[k].nrbs\n                  var nrlx = res.data[k].nrlx\n                  var color = ''\n                  if (nrlx == '1' || nrlx == '2' || nrlx == '3' || nrlx == '4') {//白色单元格:动态属性\n                    color = ' bgcolor=\"white\" '\n                  } else if (nrlx == '5') {//灰色单元格:静态文本\n                    color = ' bgcolor=\"#EEEEE0\" '\n                  } else if (nrlx == '' || nrlx == undefined || nrlx == 'undefined') {//浅绿色单元格:还未定义属性\n                    color = ' bgcolor=\"#C7EDCC\" '\n                  }\n                  if (nrbs == '' || nrbs == 'undefined' || nrbs == undefined) {\n                    nrbs = '-'\n                  } else if (nrbs.indexOf('|') == -1 || nrbs == '℃' || nrbs == '/%') {\n\n                  } else if (nrbs.indexOf('|') != -1) {\n                    nrbs = nrbs.replace(/[^\\u4e00-\\u9fa5]/gi, '')\n                  }\n                  if (res.data[k].colspan != '1' || res.data[k].rowspan != '1') {\n                    temp += '<td tabindex=\\'-1\\'' + color + ' colspan=\\'' + res.data[k].colspan + '\\' rowspan=\\'' + res.data[k].rowspan + '\\' id=\\'' + res.data[k].obj_id + '\\'>' + nrbs + '</td>'\n                  } else {\n                    temp += '<td tabindex=\\'-1\\'' + color + 'id=\\'' + res.data[k].obj_id + '\\'>' + nrbs + '</td>'\n                  }\n                }\n              }\n              temp += '</tr>'\n              str1 += temp\n            }\n            this.tableDataB = undefined\n            this.tableDataB = str1\n          }\n        })\n      } else {\n        var rowNumA = this.dyForm.a_hs\n        getTable(this.dyForm).then(res => {\n          if (rowNumA != 0) {\n            var str1 = ''\n            for (var i = 0; i < rowNumA; i++) {\n              var temp = '<tr>'\n              for (var k = 0; k < res.data.length; k++) {\n                if (i == res.data[k].rowindex) {\n                  var nrbs = res.data[k].nrbs\n                  var nrlx = res.data[k].nrlx\n                  var color = ''\n                  if (nrlx == '1' || nrlx == '2' || nrlx == '3' || nrlx == '4') {//白色单元格:动态属性\n                    color = ' bgcolor=\"white\" '\n                  } else if (nrlx == '5') {//灰色单元格:静态文本\n                    color = ' bgcolor=\"#EEEEE0\" '\n                  } else if (nrlx == '' || nrlx == undefined || nrlx == 'undefined') {//浅绿色单元格:还未定义属性\n                    color = ' bgcolor=\"#C7EDCC\" '\n                  }\n                  if (nrbs == '' || nrbs == 'undefined' || nrbs == undefined) {\n                    nrbs = '-'\n                  } else if (nrbs.indexOf('|') == -1 || nrbs == '℃' || nrbs == '/%') {\n\n                  } else if (nrbs.indexOf('|') != -1) {\n                    nrbs = nrbs.replace(/[^\\u4e00-\\u9fa5]/gi, '')\n                  }\n                  if (res.data[k].colspan != '1' || res.data[k].rowspan != '1') {\n                    temp += '<td tabindex=\\'-1\\'' + color + 'colspan=\\'' + res.data[k].colspan + '\\' rowspan=\\'' + res.data[k].rowspan + '\\' id=\\'' + res.data[k].obj_id + '\\'>' + nrbs + '</td>'\n                  } else {\n                    temp += '<td tabindex=\\'-1\\'' + color + 'id=\\'' + res.data[k].obj_id + '\\'>' + nrbs + '</td>'\n                  }\n                }\n              }\n              temp += '</tr>'\n              str1 += temp\n            }\n            this.tableDataA = undefined\n            this.tableDataA = str1\n          }\n        })\n      }\n    },\n    handle(sign, obj) {\n      this.dyForm.lbbs = sign\n\n    },\n    //获取铭牌信息\n    getNameplateInfo(row) {\n      this.rowData = row\n      this.rowData.sblxbm = this.sblxbm\n      let params = JSON.stringify({\n        'obj_id': row.objId,\n        'lbbs': 'A'\n      })\n      getTable(params).then(res => {\n        if (res.code === '0000') {\n          this.mxData = res.data//需要先设置数据再弹框，否则数据传不过去\n          this.isShowMpInfo = true\n        }\n      })\n    },\n    //关闭铭牌内容弹框\n    closeInfoDialog() {\n      this.isShowMpInfo = false\n      //刷新父页面\n      this.reload()\n    },\n    saveSpan() {\n    },\n    reset() {\n    },\n    editCellProperties() {\n    },\n    resetCellProperties() {\n    },\n    //清空表单数据\n    handleClose() {\n      this.form = {}\n      this.$nextTick(() => {\n        this.form = this.$options.data().form\n        this.resetForm('form')\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  max-height: 79vh;\n  overflow: auto;\n}\n</style>\n"]}]}