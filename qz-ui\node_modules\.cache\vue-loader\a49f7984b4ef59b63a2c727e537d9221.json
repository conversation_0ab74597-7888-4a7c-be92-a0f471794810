{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\lpbzk\\xsdwpz_pd.vue?vue&type=style&index=0&id=88c46d54&lang=css&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\lpbzk\\xsdwpz_pd.vue", "mtime": 1733860567733}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi8q5o6n5Yi2aW5wdXTovpPlhaXmoYbovrnmoYbmmK/lkKbmmL7npLoqLwouZWxJbnB1dCA+Pj4gLmVsLWlucHV0X19pbm5lciB7CiAgYm9yZGVyOiAwOwp9CgouYm94LWNhcmQgewogIG1hcmdpbi1ib3R0b206IDJ2aCAhaW1wb3J0YW50Owp9CgouZWwtc2VsZWN0IHsKICB3aWR0aDogMTAwJTsKfQoK"}, {"version": 3, "sources": ["xsdwpz_pd.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAylBA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "xsdwpz_pd.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/lpbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row>\n      <!--基本信息查询及显示-->\n      <el-col>\n        <!--搜索条件-->\n        <el-filter\n          ref=\"filter\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 80, itemWidth: 230 }\"\n          @handleReset=\"getReset\"\n          @onfocusEvent=\"inputFocusEvent\"\n          @handleEvent=\"handleEvent\"\n        />\n\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button type=\"primary\" v-hasPermi=\"['bzxsdwpe:button:add']\" icon=\"el-icon-plus\" @click=\"getInster\">新增\n            </el-button>\n\n          </div>\n          <comp-table :table-and-page-info=\"tableAndPageInfo\" @update:multipleSelection=\"selectChange\" height=\"70vh\" v-loading=\"loading\">\n            <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\"\n                             width=\"160\"\n                             :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button @click=\"getUpdate(scope.row)\" v-hasPermi=\"['bzxsdwpe:button:update']\" type=\"text\"\n                           size=\"small\"    title=\"修改\"  class='el-icon-edit'\n                >\n                </el-button>\n                <el-button @click=\"getDetail(scope.row)\" type=\"text\" size=\"small\" title=\"详情\" class=\"el-icon-view\"></el-button>\n                <el-button type=\"text\" v-if=\"scope.row.createBy === $store.getters.name\" title=\"删除\" icon=\"el-icon-delete\" @click=\"deleteRow(scope.row.objId)\">\n            </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n\n\n      </el-col>\n    </el-row>\n\n\n    <!-- 详情/新增/修改 -->\n    <el-dialog :title=title :visible.sync=\"isShowDetails\" v-if=\"isShowDetails\" width=\"50%\" v-dialogDrag>\n      <el-form label-width=\"180px\" ref=\"form\" :model=\"form\">\n\n        <!--主表信息-->\n        <div>\n          <el-col :span=\"12\">\n            <el-form-item label=\"专业：\" prop=\"zy\">\n              <el-select v-model=\"form.zy\" disabled=\"disabled\" @change=\"getBdzAndPds\" placeholder=\"请输入内容\">\n                <el-option\n                  v-for=\"item in zyList\"\n                  :key=\"item.label\"\n                  :label=\"item.value\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"地点：\" prop=\"ddid\">\n              <el-select v-model=\"form.ddid\" ref=\"ddid\" :disabled=\"isDisabled\" placeholder=\"请输入内容\" @change=\"getAllPdList\">\n                <el-option\n                  v-for=\"item in ddList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"点位名称：\" prop=\"dwmc\">\n              <el-input v-model=\"form.dwmc\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"点位描述：\" prop=\"dwms\">\n              <el-input v-model=\"form.dwms\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"标签绑定值：\" prop=\"bqbdz\">\n              <el-input v-model=\"form.bqbdz\" :disabled=\"isDisabled\" placeholder=\"请输入标签绑定值\"></el-input>\n            </el-form-item>\n          </el-col>\n        </div>\n\n        <!--子表信息-->\n        <div>\n          <el-table :data=\"propTableData.colFirst\" :disabled=\"isDisabled\" height=\"300\" border stripe\n                    style=\"width: 100%\"\n          >\n            <el-table-column\n              type=\"index\"\n              width=\"50\"\n              align=\"center\"\n              label=\"序号\"\n            />\n            <!--子表列表-->\n            <el-table-column align=\"center\" prop=\"sbid\" label=\"设备名称\">\n              <template slot-scope=\"scope\">\n                <el-select v-model=\"scope.row.sbid\" placeholder=\"请输入设备名称\" :disabled=\"isDisabled\" clearable filterable @change=\"sbmcChange(scope.row)\">\n                  <el-option\n                    v-for=\"item in pdSbmcList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"sblx\" label=\"设备类型\">\n              <template slot-scope=\"scope\">\n                <el-select v-model=\"scope.row.sblx\" placeholder=\"请选择设备类型\" :disabled=\"isDisabled\" clearable filterable multiple>\n                  <el-option\n                    v-for=\"item in sblxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" width=\"80\">\n              <!--子表添加按钮-->\n              <template slot=\"header\" slot-scope=\"scope\">\n                <el-button type=\"primary\" size=\"small\" :disabled=\"isDisabled\" icon=\"el-icon-plus\"\n                           @click=\"listFirstAdd(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n              <!--子表删除按钮-->\n              <template slot-scope=\"scope\">\n                <el-button type=\"text\" icon=\"el-icon-delete\" :disabled=\"isDisabled\"\n                           @click=\"listFirstDel(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button v-if=\"title=='巡视点位增加' || title=='巡视点位修改'\" type=\"primary\" @click=\"saveRow\">确 认\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!--设备名称弹框-->\n    <el-dialog\n      v-dialogDrag\n      :append-to-body=\"true\"\n      title=\"设备名称\"\n      :visible.sync=\"ZbDialogFormVisible\"\n      width=\"400px\"\n      v-if=\"ZbDialogFormVisible\"\n    >\n      <device-tree\n        @getDeviceTypeData=\"getDeviceTypeData\"\n        @closeDeviceTypeDialog=\"closeDeviceTypeDialog\"\n      >\n      </device-tree>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  getAllPdSelectList,\n  getList,\n  getPdSblxList,\n  getSblxListByZy,\n  queryZb,\n  remove,\n  saveOrUpdate\n} from '@/api/dagangOilfield/bzgl/lpbzk/xsdwpz'\nimport DeviceTree from '@/views/dagangOilfield/bzgl/sbbzk/deviceTree'\nimport { getSblxDataListSelected } from '@/api/dagangOilfield/asset/bdsbtz'\nimport { getBdzSelectList } from '@/api/yxgl/bdyxgl/bdxjzqpz'\nimport { getPdsTreeList } from '@/api/dagangOilfield/asset/pdg'\n\nexport default {\n  name: 'xsdwpz',\n  components: { DeviceTree },\n  data() {\n    return {\n      loading:false,\n      // 多选框选中的id\n      ids: [],\n      //弹出框中表格数据\n      propTableData: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //标题\n      title: '',\n      //子表标题\n      zbtitle: '',\n      //详情弹框是否显示\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //子表\n      ZbDialogFormVisible: false,\n      //子表弹框展示\n      isShowSbDetails: false,\n      //子表增加框是否展示\n      isShowZbAdd: false,\n      //子表删除框是否展示\n      isShowZbDelete: false,\n      //子表设备名称是否展示\n      isShowSbmc: false,\n      isFilter: false,\n      //专业下拉框\n      zyList: [{ label: '配电', value: '配电' }],\n      //地点下拉框\n      ddList: [],\n      ddMap:new Map(),\n      //设备类型\n      sblxOptionsDataSelected: [],\n      //设备名称\n      sbmcList: [{ label: '一', value: '一' }, { label: '二', value: '二' }],\n      //form表单\n      form: {\n        zy: '',\n        dd: '',\n        dwmc: '',\n        dwms: '',\n        bqbdz: '',\n        colFirst: [],\n        sbmc: ''\n      },\n      //查询条件\n      filterInfo: {\n        data: {\n          // zy: '',\n          ddid: '',\n          dwmc: '',\n          dwms: '',\n          bqbdz: ''\n        },\n        fieldList: [\n          {\n            label: '地点',\n            value: 'ddid',\n            type: 'select',\n            options: [],\n          },\n          { label: '点位名称', value: 'dwmc', type: 'input', clearable: true },\n          { label: '点位描述', value: 'dwms', type: 'input', clearable: true },\n          { label: '标签绑定值', value: 'bqbdz', type: 'input', clearable: true }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: '专业', prop: 'zy', minWidth: '120' },\n          { label: '地点', prop: 'dd', minWidth: '120' },\n          { label: '点位名称', prop: 'dwmc', minWidth: '120' },\n          { label: '点位描述', prop: 'dwms', minWidth: '160' },\n          { label: '标签绑定值', prop: 'bqbdz', minWidth: '160' }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        zy:'配电',\n      },\n      pdSbmcList:[],//设备名称下拉框\n      sblxList:[],//设备类型\n      sblxListAll:[],//设备类型\n    }\n  },\n  created() {\n    //列表查询\n    this.getData();\n    this.handleEvent({label:'zy',value:'配电'},'');\n    this.getSblxListAll();\n\n  },\n  methods: {\n    async getSblxListAll(){\n      await getSblxListByZy({zy:'pdsb'}).then(res=>{\n          this.sblxListAll = res.data;\n          this.sblxList = res.data;\n      })\n    },\n    //设备名称change事件\n    async sbmcChange(val){\n      val.sblx = [];//清空该行的设备类型字段\n      await getPdSblxList({sbid:val.sbid}).then(res=>{\n        this.sblxList = res.data;\n        if(val.sbid === 'NEIBU' || val.sbid === 'WAIBU'){//内部、外部附属设施\n          this.sblxList.push({label:'附属设施',value:'pd24'});//附属设施\n        }\n      })\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.loading= true\n        this.params = { ...this.params, ...params}\n        const param = this.params\n        const { data, code } = await getList(param)\n        if (code === '0000') {\n          this.tableAndPageInfo.tableData = data.records\n          this.tableAndPageInfo.pager.total = data.total\n          this.loading = false\n        }\n      } catch (e) {\n      }\n    },\n    //根据主表查询子表方法\n    async getListZb(row) {\n      try {\n        const { data, code } = await queryZb({ objId: row.objId })\n        if (code === '0000') {\n          data.forEach(item=>{\n            item.sblx = item.sblx&&item.sblx.split(',');\n          })\n          this.propTableData.colFirst = data\n        }\n      } catch (e) {\n      }\n    },\n    /*----------------------主表-----------------------*/\n    //新增按钮\n    getInster() {\n      this.propTableData.colFirst = []\n      this.title = '巡视点位增加'\n      this.isDisabled = false\n      this.$nextTick(() => {\n        this.form = this.$options.data().form\n        this.resetForm('form')\n        this.form.zy = '配电';\n        this.getBdzAndPds(this.form.zy)\n      })\n      this.ddList = []\n      this.sblxOptionsDataSelected = []\n      this.isShowDetails = true\n    },\n    //修改按钮\n    async getUpdate(row) {\n      await this.getListZb(row)\n      await this.getBdzAndPds(row.zy)\n      await this.getAllPdList(row.ddid);\n      this.sblxList = this.sblxListAll;\n      this.title = '巡视点位修改'\n      this.isDisabled = false\n      this.form = { ...row }\n      this.isShowDetails = true\n      this.ZbDialogFormVisible = false\n    },\n    async getAllPdList(dd){\n      await getAllPdSelectList({ddid:dd}).then(res=>{\n        this.pdSbmcList = res.data;\n        this.pdSbmcList.push({label:'内部附属设施',value:'NEIBU'});\n        this.pdSbmcList.push({label:'外部附属设施',value:'WAIBU'});\n      })\n    },\n    //详情按钮\n    async getDetail(row) {\n      await this.getListZb(row)\n      await this.getBdzAndPds(row.zy)\n      await this.getAllPdList(row.ddid);\n      this.sblxList = this.sblxListAll;\n      this.title = '巡视点位详情查看'\n      this.form = { ...row }\n      this.isDisabled = true\n      this.isShowDetails = true\n      this.isShowSbmc = true\n      this.ZbDialogFormVisible = false\n    },\n    //保存按钮\n    async saveRow() {\n      this.propTableData.colFirst.forEach(item=>{\n        item.sblx = item.sblx.join(',');\n      })\n      this.form.dd = this.ddMap.get(this.form.ddid);\n      this.form.colFirst = this.propTableData.colFirst\n      this.form.objIdList = this.ids\n      let { code } = await saveOrUpdate(this.form)\n      if (code === '0000') {\n        this.$message.success('操作成功')\n      }\n      this.isShowDetails = false\n      //重置page页从1开始\n      this.tableAndPageInfo.pager.pageResize = 'Y'\n      await this.getData()\n    },\n    //删除按钮\n    async deleteRow(id) {\n      // if (this.ids.length < 1) {\n      //   this.$message.warning('请选择正确的数据！！！')\n      //   return\n      // }\n      let obj=[];\n        obj.push(id);\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        remove(obj).then(({ code }) => {\n          if (code === '0000') {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            })\n            //重置page页从1开始\n            this.tableAndPageInfo.pager.pageResize = 'Y'\n            this.getData()\n          } else {\n            this.$message({\n              type: 'error',\n              message: '删除失败!'\n            })\n          }\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        })\n      })\n    },\n    /*-----------------------------子表----------------------------*/\n    //表格新增\n    listFirstAdd() {\n      this.sblxList = [];//清空设备类型下拉框数据\n      let row = {\n        objId: '',\n        sbmc: ''\n      }\n      this.propTableData.colFirst.push(row)\n      this.propTableData.sel = row\n    },\n    //子表删除\n    listFirstDel(index, row) {\n      this.ids.push(row.objId)\n      this.propTableData.colFirst.splice(index, 1)\n    },\n    //子表增加弹框\n    ZbAdd() {\n      this.yxbh = this.$refs.dd.selected.value\n      this.zbtitle = '设备增加'\n      this.isDisabled = false\n      this.isShowSbmc = false\n      this.isFilter = false\n      this.ZbDialogFormVisible = true\n    },\n    //重置按钮\n    getReset() {\n      this.params = {\n        zy:'配电',\n      }\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false\n    },\n    //筛选条件\n    selectChange(selection) {\n      this.ids = selection.map(item => item.objId)\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n    },\n    /*搜索条件*/\n    inputFocusEvent(val) {\n      if (val.target.name === 'sbmc') {\n        this.ZbDialogFormVisible = true\n        this.isFilter = true\n      }\n    },\n    /*获取设备树*/\n    getDeviceTypeData(res) {\n      if (this.isFilter) {\n        this.filterInfo.data.sbmcArr = []\n        this.filterInfo.data.sbmc = ''\n        res.forEach(item => {\n          if (item.checked) {\n            this.filterInfo.data.sbmcArr.push(item.code)\n            this.filterInfo.data.sbmc += item.name + ','\n          }\n        })\n        this.filterInfo.data.sbmc = this.filterInfo.data.sbmc.substring(0, this.filterInfo.data.sbmc.length - 1)\n        this.ZbDialogFormVisible = false\n      } else {\n        let treeNodes = []\n        res.forEach(item => {\n          if (item.checked) {\n            treeNodes.push(item)\n          }\n        })\n        if (treeNodes.length === 1) {\n          this.form.sbmc = treeNodes[0].name\n          this.form.sbmc = treeNodes[0].code\n          this.ZbDialogFormVisible = false\n        } else {\n          this.$message.warning('请选择单条设备数据')\n        }\n      }\n    },\n    /*关闭对话框*/\n    closeDeviceTypeDialog() {\n      this.ZbDialogFormVisible = false\n    },\n    //下拉框change事件\n    handleEvent(val, val1) {\n      // this.params = val1\n      if (val.label === 'zy' && val.value && val.value !== '') {\n        if (val.value === '变电') {\n          getBdzSelectList({}).then(res => {\n            this.filterInfo.fieldList.map(item => {\n              if (item.value == 'dd') {\n                return item.options = res.data\n              }\n            })\n          })\n          getSblxDataListSelected({ type: '变电设备' }).then(res => {\n            // this.sblxOptionsDataSelected = res.data;\n            this.filterInfo.fieldList.map(item => {\n              if (item.value == 'sblx') {\n                return item.options = res.data\n              }\n            })\n          })\n        } else if (val.value === '配电') {\n          getPdsTreeList({}).then(res => {\n            let pdzOption = res.data[0].children.map(item => {\n              let obj = {}\n              obj.label = item.label\n              obj.value = item.id\n              return obj\n            })\n            this.filterInfo.fieldList.map(item => {\n              if (item.value == 'ddid') {\n                return item.options = pdzOption\n              }\n            })\n          })\n          getSblxDataListSelected({ type: '配电设备' }).then(res => {\n            this.filterInfo.fieldList.map(item => {\n              if (item.value == 'sblx') {\n                return item.options = res.data\n              }\n            })\n          })\n        }\n      }\n    },\n    //获取变电站和配电室\n    getBdzAndPds(val) {\n      this.$set(this.form, 'dd', '')\n      this.$set(this.form, 'sblx', '')\n      if (val === '变电') {\n        getBdzSelectList({}).then(res => {\n          this.ddList = res.data\n        })\n        this.getSblxDataListSelected('变电设备')\n      } else if (val === '配电') {\n        getPdsTreeList({}).then(res => {\n          this.ddList = res.data[0].children.map(item => {\n            this.ddMap.set(item.id, item.label);\n            let obj = {}\n            obj.label = item.label\n            obj.value = item.id\n            return obj\n          })\n        })\n\n        this.getSblxDataListSelected('配电设备')\n      }\n    },\n    /**\n     * 获取设备类型下拉框数据\n     */\n    getSblxDataListSelected(value) {\n      getSblxDataListSelected({ type: value }).then(res => {\n        this.sblxOptionsDataSelected = res.data\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"css\" scoped>\n/*控制input输入框边框是否显示*/\n.elInput >>> .el-input__inner {\n  border: 0;\n}\n\n.box-card {\n  margin-bottom: 2vh !important;\n}\n\n.el-select {\n  width: 100%;\n}\n\n</style>\n\n"]}]}