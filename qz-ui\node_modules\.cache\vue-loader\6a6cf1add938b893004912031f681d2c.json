{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\Index\\Workbench.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\Index\\Workbench.vue", "mtime": 1706897320623}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Workbench.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "Workbench.vue", "sourceRoot": "src/components/Index", "sourcesContent": ["<template>\n  <div :notSpanNum=\"notSpanNum\" class=\"borderCls1 notices\" :class=\"notDivClass\">\n    <div>\n      <div class=\"txtTitle\">\n        <!-- <span @click=\"handleClick('processTodo')\" :class=\"this.val == 'processTodo'?'tabActive':'noActive'\">待办<el-badge :value=\"processCount\"></el-badge></span> -->\n        <span @click=\"handleClick('noticeTodo')\" :class=\"this.val == 'noticeTodo'?'tabActive':'noActive'\">通知<el-badge :value=\"noticeCount\"></el-badge></span>\n        <span @click=\"handleClick('proclamationTodo')\" :class=\"this.val == 'proclamationTodo'?'tabActive':'noActive'\">公告<el-badge :value=\"proclamationCount\"></el-badge></span>\n        <span @click=\"handleClick('remindTodo')\" :class=\"this.val == 'remindTodo'?'tabActive':'noActive'\">提醒<el-badge :value=\"remindCount\"></el-badge></span>\n        <!--        <el-button style=\"float: right; margin-top: 5px; margin-right: 20px;color:#b1b1b1\" type=\"text\"\n                           @click=\"showHistoryNotice\">More+\n                </el-button>-->\n      </div>\n      <div v-show=\"this.val == 'processTodo'\">\n        <el-table :data=\"datalist\" :show-header=\"status\"  class=\"work_table notice_box\">\n          <el-table-column>\n            <template slot-scope=\"scope\">\n              <a class=\"aaClass\" @click=\"getPage(scope.row)\">\n                <el-badge :value=\"scope.row.isHandle==0?'待办理':'已办理'\" class=\"item\"\n                          :type=\"scope.row.isHandle==0?'danger':'primary'\">\n                  <p class=\"titleClass\">{{ scope.row.itemName }}</p>\n                </el-badge>\n              </a>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"todoTime\" class=\"timeClass\" width=\"160\"></el-table-column>\n        </el-table>\n      </div>\n      <div class=\"work_box\" v-show=\"this.val != 'processTodo'\">\n        <el-table :data=\"datalist2\" :show-header=\"status\" class=\"work_table notice_box\">\n          <el-table-column>\n            <template slot-scope=\"scope\">\n              <a class=\"aaClass\" @click=\"getPage(scope.row)\">\n                <el-badge class=\"item\" :value=\"scope.row.isRead==0?'未读':'已读'\" :type=\"scope.row.isRead==0?'danger':'primary'\">\n                  <p class=\"titleClass\">{{ scope.row.title }}</p>\n                </el-badge>\n              </a>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"publishStartTime\" width=\"180\" class=\"timeClass\"></el-table-column>\n        </el-table>\n      </div>\n    </div>\n\n\n    <!--历史公告弹框-->\n    <el-dialog title=\"历史公告\" :visible.sync=\"historyNotice\" width=\"60%\" v-dialogDrag>\n      <el-form :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" label-width=\"120px\">\n        <el-form-item label=\"公告标题：\" prop=\"title\">\n          <el-input v-model=\"queryParams.title\" placeholder=\"请输入标题\" clearable\n                    @keyup.enter.native=\"handleQuery\"/>\n        </el-form-item>\n        <el-form-item label=\"公告内容：\" prop=\"content\">\n          <el-input v-model=\"queryParams.content\" placeholder=\"请输入内容\" clearable\n                    @keyup.enter.native=\"handleQuery\"/>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\n          <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\n        </el-form-item>\n      </el-form>\n      <el-table stripe border :data=\"historyNoticeList\" @selection-change=\"handleSelectionChange\">\n        <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n        <el-table-column label=\"公告标题\" align=\"center\" prop=\"title\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"公告内容\" align=\"center\" prop=\"content\" width=\"150\">\n          <template slot-scope=\"scope\">\n            <el-popover v-if=\"scope.row.content.length>8\" trigger=\"hover\" placement=\"top\" width=\"200\">\n              {{ scope.row.content }}\n              <div slot=\"reference\">\n                {{ scope.row.content.substring(0,8)+'...' }}\n              </div>\n            </el-popover>\n            <span v-else>\n            {{ scope.row.content}}\n          </span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"发布时间\" align=\"center\" prop=\"publishstarttime\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"结束时间\" align=\"center\" prop=\"publishendtime\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"附件\" align=\"center\" prop=\"attachment\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"操作\" align=\"center\" width=\"180\" class-name=\"small-padding fixed-width\">\n          <template slot-scope=\"scope\">\n            <el-button size=\"mini\" type=\"text\" @click=\"downloadFile(scope.row.attachmentid)\">下载附件\n            </el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <pagination v-show=\"hTotal>0\"\n                  :total=\"hTotal\"\n                  :page.sync=\"queryParams.pageNum\"\n                  :limit.sync=\"queryParams.pageSize\"\n                  @pagination=\"getHistoryList\"/>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { list } from '@/api/activiti/DgTodoItem'\nimport { download } from '@/api/tool/file'\nimport { countNoticeNumber, getNoticeList } from '@/api/activiti/DgTodoItem'\n\nexport default {\n  name: 'Workbench',//工作台通知公告\n  props: {\n    notSpanNum: {\n      type: Number,\n      default: 12\n    },\n    notDivClass: ''\n  },\n  data() {\n    return {\n      historyNotice: false,\n      historyNoticeList: [],\n      hTotal: 0,\n      val: 'noticeTodo',\n      todoShow: true,\n      //用于布局动态设置高度\n      activeClass: 1,\n      tableData: [\n        {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022年01月31日'\n        },\n        {\n          date: '您有一个工单需要审批,请及时审批',\n          name: '2022年01月31日'\n        }, {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022年01月31日'\n        }, {\n          date: '您有一个工单需要审批,请及时审批',\n          name: '2022年01月31日'\n        }, {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022年01月31日'\n        },\n        {\n          date: '您有一个工单需要审批,请及时审批',\n          name: '2022年01月31日'\n        },\n        {\n          date: '您有一个工单需要审批,请及时审批',\n          name: '2022年01月31日'\n        },\n        {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022年01月31日'\n        }, {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022年01月31日'\n        }\n      ],\n      tableData1: [\n        {\n          date: '您提交的工单已经审批通过了、点击查看详情',\n          name: '2022-01-31'\n        },\n        {\n          date: '您提交的工单已经审批通过了、点击查看详情',\n          name: '2022-01-31'\n        },\n        {\n          date: '您提交的工单已经审批通过了、点击查看详情',\n          name: '2022-01-31'\n        },\n        {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022-01-31'\n        },\n        {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022-01-31'\n        }, {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022-01-31'\n        }, {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name:'2022-01-31'\n        }, {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022-01-31'\n        }, {\n          date: '您将与2022年02月01日开始巡视XXXXXXXX条线路',\n          name: '2022-01-31'\n        }\n      ],\n      listData: [\n        {\n          date: 'DL/Z 398-2010-电力行业信息化标准体系'\n        }, {\n          date: 'DL/T 417-2006-电力设备局部放电现场测量导则'\n        },\n        {\n          date: 'DL/T 423-2009-绝缘油中含气量测定方法真空压差法 '\n        },\n        {\n          date: 'DL/Z 5334-2006 -电力工程勘测安全技术规程'\n        },\n        {\n          date: 'DL/Z 398-2010-电力行业信息化标准体系'\n        }, {\n          date: 'DL/T 1148-2009-电力电缆线路巡检系统'\n        },\n        {\n          date: 'DL/T 800-2001-电力企业标准编制规则'\n        }, {\n          date: 'DL/T 727-2000-互感器运行检修导则 '\n        },\n        {\n          date: 'DL/T 720-2000-电力系统继电保护柜、屏通用技术条件 '\n        }, {\n          date: 'DL/T 676-1999-带电作业绝缘鞋（靴〕通用技术条件 '\n        },\n        {\n          date: 'DL/T 664-2008-带电设备红外诊断应用规范 '\n        }, {\n          date: 'DL/T 574-2010-变压器分接开关运行维修导则 '\n        }\n      ],\n      status: false,\n      maxTableHeight: '340',//表格最大宽度\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        title: undefined,\n        content: undefined\n      },\n      params: {\n        isHandle: '0',\n        lx: 2\n      },\n      datalist:[],\n      datalist2: [],\n      remindCount: 0,\n      noticeCount: 0,\n      processCount: 0,\n      proclamationCount: 0,\n      tabRefresh: {\n        proclamationTodo: 2,\n        remindTodo: 3,\n        noticeTodo: 1\n      },\n    }\n  },\n\n  created() {\n    // this.getHistoryList()\n    //获取数据\n    this.getData();\n    //获取各个模块提醒数量\n    this.countNumber();\n  },\n  methods: {\n    getPage(item) {\n      let path, query\n      switch (this.params.lx) {\n        case 1:\n          path = '/todo/processTodo'\n          query = {objId: item.objId}\n          break;\n        case 2:\n          path = '/todo/noticeTodo'\n          query = {id: item.id}\n          break;\n        case 3:\n          path = '/todo/proclamationTodo'\n          query = {id: item.id}\n          break;\n        case 4:\n          path = '/todo/remindTodo'\n          query = {id: item.id}\n          break;\n      }\n      this.$router.push({\n        path: path,\n        query: query\n      })\n    },\n    //标签页点击\n    handleClick(tabName) {\n      switch (tabName){\n        case 'processTodo':\n          this.params.lx = 1;\n          break;\n        case 'noticeTodo':\n          this.params.lx = 2;\n          break;\n        case 'proclamationTodo':\n          this.params.lx = 3;\n          break;\n        case 'remindTodo':\n          this.params.lx = 4;\n          break;\n      }\n      this.getData();\n      this.val = tabName;\n    },\n    async countNumber() {\n      let {code, data} = await countNoticeNumber()\n      if (code === '0000') {\n        console.log('data',data);\n        await this.$store.dispatch('app/setNoticeCount', data.total)\n        this.remindCount = data.remindCount;\n        this.noticeCount = data.noticeCount;\n        this.processCount = data.processCount;\n        this.proclamationCount = data.proclamationCount;\n      }\n    },\n    async getData() {\n      switch (this.params.lx) {\n        case 1:\n          let {code, data} = await list({isHandle: '0'})\n          if (code === '0000') {\n            this.datalist = data;\n          }\n          break;\n        case 2:\n          let result = await getNoticeList({type: 1})\n          if (result.code === '0000') {\n            this.datalist2 = result.data;\n          }\n          break;\n        case 3:\n          let result2 = await getNoticeList({type: 0})\n          if (result2.code === '0000') {\n            this.datalist2 = result2.data;\n          }\n          break;\n        case 4:\n          let result3 = await getNoticeList({type: 2})\n          if (result3.code === '0000') {\n            this.datalist2 = result3.data;\n          }\n          break;\n      }\n    },\n    click(val) {\n      this.val = val\n      if (val == 'first' ||val == 'three'  ) {\n        this.todoShow = true\n      } if (val == 'second' || val == 'four' ){\n        this.todoShow = false\n      }\n    },\n    showHistoryNotice() {\n      this.historyNotice = true\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map((item) => item.noticeid)\n    },\n    handleQuery() {\n      this.getHistoryList()\n    },\n    resetQuery() {\n      this.resetForm('queryForm')\n      this.handleQuery()\n    },\n    getHistoryList() {\n      list(this.queryParams).then(response => {\n        this.historyNoticeList = response.data.records\n        this.hTotal = response.data.total\n      })\n    },\n    downloadFile(attachmentId) {\n      if (attachmentId) {\n        download(attachmentId)\n      } else {\n        this.msgWarning('暂无附件！')\n      }\n    }\n  },\n  watch: {\n    notSpanNum(newVal) {\n\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.aaClass {\n  :hover {\n    color: darkgreen\n  }\n  display: block;\n  /*  padding-top: 1vh;\n    padding-bottom: 1vh;*/\n  //margin-left: 2vh;\n  color: #666;\n  .item{\n    position: absolute;\n    z-index: 1;\n    /deep/ .el-badge__content.is-fixed{\n      //top:12px !important;\n      position: relative;\n      right: 0;\n      transform: inherit;\n      top: -42px;\n    }\n  }\n\n  .titleClass {\n    word-break: break-all;\n    white-space: nowrap;\n    margin: 0;\n    display: block;\n    color: #666;\n    font-size: 14px;\n    overflow: hidden;\n    width: 235px;\n    text-overflow: ellipsis;\n    font-family: \"Light_0\" !important;\n  }\n\n  /*  .titleClass:hover {\n      margin: 0;\n      display: block;\n      color: rgba(12, 194, 131, 0.8);\n      !*margin-left: 13px !important;*!\n      font-size: 14px;\n      font-family: \"Light_0\" !important;\n    }*/\n\n  .timeClass {\n    margin: 0;\n    font-size: 12px;\n    color: #c1cad4;\n    width: 160px;\n  }\n}\n.txtTitle {\n  list-style-type: none;\n  margin: 0;\n  padding:0;\n}\n.titleClass {\n  word-break: break-all;\n  white-space: nowrap;\n  margin: 0;\n  display: block;\n  color: #666;\n  font-size: 14px;\n  font-family: \"Light_0\" !important;\n  padding-left: 47px;\n}\n\n/*  .tabActive {\n    float: left;\n !*   margin-right: 25px;*!\n    cursor: pointer;\n    color: #359076;\n  }\n\n  .noActive {\n    float: left;\n   !* margin-right: 25px;*!\n    color: #b1b1b1;\n    cursor: pointer;\n  }*/\n\n/*设置滚动条样式*/\n/deep/ .work_table .el-table__body-wrapper::-webkit-scrollbar, /deep/ .todo .el-table::-webkit-scrollbar {\n  width: 4px;\n}\n\n/deep/ .work_table .el-table__body-wrapper::-webkit-scrollbar-thumb, /deep/ .todo .el-table::-webkit-scrollbar-thumb {\n  border-radius: 10px;\n  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);\n  background: rgba(0, 0, 0, 0.2);\n}\n\n/deep/ .work_table .el-table__body-wrapper::-webkit-scrollbar-track, /deep/ .work_table .el-tab-pane::-webkit-scrollbar-track {\n  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);\n  border-radius: 0;\n  background: rgba(0, 0, 0, 0.1);\n}\n\n/deep/ .work_table .el-table__body {\n  width: 100% !important;\n}\n\n/deep/ .work_table .el-table--medium th, /deep/ .work_table.el-table--medium td {\n  color: #000;\n  padding: 0;\n}\n\n/deep/ .work_table .el-table--medium th:hover, /deep/ .work_table.el-table--medium td:hover {\n  /*    color: #11ba6b;*/\n  /*  background: transparent;\n    overflow: inherit !important;*/\n}\n/deep/ .work_table .el-table tr{\n  color: rgba(12, 194, 131, 0.8);\n}\n/deep/ .notice_box .cell {\n  white-space: nowrap;\n  height: 42px;\n  line-height: 42px;\n  padding-left: 2px;\n}\n.notices{\n  height: 331px;\n}\n.notice_box{\n  width: 100%;padding:0 12px;\n  max-height:285px;\n  height: 285px;\n}\n</style>\n"]}]}