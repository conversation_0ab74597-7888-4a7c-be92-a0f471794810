{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\gqjgl\\xlgqj.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\gqjgl\\xlgqj.vue", "mtime": 1706897324701}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFVVSUQgfSBmcm9tICdAL3V0aWxzL3J1b3lpJwppbXBvcnQgewogIGRlbGV0ZUFzc2V0R3FqSnhSZWNvcmRzLAogIGRlbGV0ZVl4U3lSZWNvcmRzLCBleHBvcnRFeGNlbCwKICBnZXRBc3NldEdxakp4UmVjb3JkcywKICBnZXRMaXN0LAogIGdldFl4U3lSZWNvcmRzLAogIHJlbW92ZSwKICBzYXZlT3JVcGRhdGUsCiAgc2F2ZU9yVXBkYXRlQXNzZXRHcWpKeFJlY29yZHMsCiAgc2F2ZU9yVXBkYXRlWXhTeVJlY29yZHMKfSBmcm9tICdAL2FwaS9kYWdhbmdPaWxmaWVsZC9hc3NldC9hc3NldEdxaicKaW1wb3J0IENvbXBUYWJsZSBmcm9tICdjb20vQ29tcFRhYmxlJwppbXBvcnQgRWxGaWx0ZXIgZnJvbSAnY29tL0VsRmlsdGVyJwppbXBvcnQgeyBkb3dubG9hZCB9IGZyb20gJ0AvYXBpL3Rvb2wvZmlsZScKCmV4cG9ydCBkZWZhdWx0IHsKICAgIGNvbXBvbmVudHM6IHtDb21wVGFibGUsIEVsRmlsdGVyfSwKICAgIG5hbWU6ICJncWpnbCIsCiAgICBkYXRhKCkgewogICAgICByZXR1cm4gewogICAgICAgIGN1cnJVc2VyOnRoaXMuJHN0b3JlLmdldHRlcnMubmFtZSwKICAgICAgICAvLyDooajljZXmoKHpqowKICAgICAgICBydWxlczogewogICAgICAgICAgc2w6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWlJywgdHJpZ2dlcjogJ2JsdXInIH1dLAogICAgICAgICAgc2JtYzogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaUnLCB0cmlnZ2VyOiAnYmx1cicgfV0sCiAgICAgICAgICB4aDogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaUnLCB0cmlnZ2VyOiAnYmx1cicgfV0sCiAgICAgICAgICBmenI6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWlJywgdHJpZ2dlcjogJ2JsdXInIH1dLAogICAgICAgIH0sCiAgICAgICAgYWN0aXZlTmFtZToiZ3JnaiIsCiAgICAgICAgcGFyYW1zOnsKICAgICAgICAgIHR5cGU6InhsIgogICAgICAgIH0sCiAgICAgICAgLy/lt6Xlmajlhbfor6bmg4XmoYblrZfmrrXmjqfliLYKICAgICAgICBpc0Rpc2FibGVkOiBmYWxzZSwKICAgICAgICAvL+W3peWZqOWFt+W8ueWHuuahhuihqOWktAogICAgICAgIGdxalRpdGFsOiAi5bel5Zmo5YW35paw5aKeIiwKCiAgICAgICAgLy/ooajmoLzlhoXlrrkKICAgICAgICB0YWJsZUFuZFBhZ2VJbmZvOiB7CiAgICAgICAgICBwYWdlcjogewogICAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgICBzaXplczogWzEwLCAyMCwgNTAsIDEwMF0KICAgICAgICAgIH0sCiAgICAgICAgICBvcHRpb246IHsKICAgICAgICAgICAgY2hlY2tCb3g6IHRydWUsCiAgICAgICAgICAgIHNlcmlhbE51bWJlcjogdHJ1ZQogICAgICAgICAgfSwKICAgICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgICB0YWJsZUhlYWRlcjogWwogICAgICAgICAgICB7cHJvcDogJ3NibWMnLCBsYWJlbDogJ+WQjeensCcsIG1pbldpZHRoOiAnMTgwJyxjdXN0b206dHJ1ZX0sCiAgICAgICAgICAgIHtwcm9wOiAneGgnLCBsYWJlbDogJ+inhOagvOWei+WPtycsIG1pbldpZHRoOiAnMTgwJ30sCiAgICAgICAgICAgIHtwcm9wOiAnZnpyJywgbGFiZWw6ICfotJ/otKPkuronLCBtaW5XaWR0aDogJzEyMCd9LAogICAgICAgICAgICB7cHJvcDogJ3NzZ3MnLCBsYWJlbDogJ+WNleS9jScsIG1pbldpZHRoOiAnMTIwJ30sCiAgICAgICAgICAgIHtwcm9wOiAnc2wnLCBsYWJlbDogJ+aVsOmHjycsIG1pbldpZHRoOiAnNTAnfSwKICAgICAgICAgICAge3Byb3A6ICdzY2NqJywgbGFiZWw6ICfnlJ/kuqfljoLlrrYnLCBtaW5XaWR0aDogJzEyMCd9LAogICAgICAgICAgICB7cHJvcDogJ3R5cnEnLCBsYWJlbDogJ+WQr+eUqOW5tOaciCcsIG1pbldpZHRoOiAnMTIwJyxjdXN0b206dHJ1ZX0sCiAgICAgICAgICAgIHtwcm9wOiAnY2ZkZCcsIGxhYmVsOiAn5a2Y5pS+5Zyw54K5JywgbWluV2lkdGg6ICcyNTAnfSwKICAgICAgICAgICAgLy8gewogICAgICAgICAgICAvLyAgIGZpeGVkOiAicmlnaHQiLAogICAgICAgICAgICAvLyAgIHByb3A6ICdvcGVyYXRpb24nLAogICAgICAgICAgICAvLyAgIGxhYmVsOiAn5pON5L2cJywKICAgICAgICAgICAgLy8gICBtaW5XaWR0aDogJzE1MHB4JywKICAgICAgICAgICAgLy8gICBzdHlsZToge2Rpc3BsYXk6ICdibG9jayd9LAogICAgICAgICAgICAvLyAgIG9wZXJhdGlvbjogWwogICAgICAgICAgICAvLyAgICAgLy8ge25hbWU6ICfor5XpqownLCBjbGlja0Z1bjogdGhpcy5oYW5kbGVTZWFyY2hTWUNsaWNrfSwKICAgICAgICAgICAgLy8gICAgIC8vIHtuYW1lOiAn5qOA5L+uJywgY2xpY2tGdW46IHRoaXMuaGFuZGxlU2VyY2hKV1hDbGlja30sCiAgICAgICAgICAgIC8vICAgICB7bmFtZTogJ+S/ruaUuScsIGNsaWNrRnVuOiB0aGlzLnVwZGF0ZUdxakluZm99LAogICAgICAgICAgICAvLyAgICAge25hbWU6ICfor6bmg4UnLCBjbGlja0Z1bjogdGhpcy5nZXRHcWpJbmZvfSwKICAgICAgICAgICAgLy8gICBdLAogICAgICAgICAgICAvLyB9LAogICAgICAgICAgXQogICAgICAgIH0sCiAgICAgICAgLy/nrZvpgInmnaHku7YKICAgICAgICBmaWx0ZXJJbmZvOiB7CiAgICAgICAgICBkYXRhOiB7CiAgICAgICAgICAgIGZ6cjogJycsCiAgICAgICAgICAgIHNzZ3M6ICcnLAogICAgICAgICAgICB5eGJ6OiAnJywKICAgICAgICAgICAgcGhvbmU6ICcnLAogICAgICAgICAgfSwKICAgICAgICAgIGZpZWxkTGlzdDogWwogICAgICAgICAgICB7bGFiZWw6ICflkI3np7AnLCB0eXBlOiAnaW5wdXQnLCB2YWx1ZTogJ3NibWMnfSwKICAgICAgICAgICAge2xhYmVsOiAn6KeE5qC85Z6L5Y+3JywgdHlwZTogJ2lucHV0JywgdmFsdWU6ICd4aCd9LAogICAgICAgICAgICB7bGFiZWw6ICfotJ/otKPkuronLCB0eXBlOiAnaW5wdXQnLCB2YWx1ZTogJ2Z6cid9LAogICAgICAgICAgICB7bGFiZWw6ICflrZjmlL7lnLDngrknLCB0eXBlOiAnaW5wdXQnLCB2YWx1ZTogJ2NmZGQnfSwKICAgICAgICAgIF0KICAgICAgICB9LAogICAgICAgIC8v5qOA5L+u6K6w5b2V5by55Ye65qGGCiAgICAgICAgand4RGlhbG9nRm9ybVZpc2libGU6IGZhbHNlLAogICAgICAgIC8v5re75Yqg5qOA5L+u6K6w5b2V5by55Ye65qGGCiAgICAgICAgYWRkSnd4U3liZ0RpYWxvZ0Zvcm1WaXNpYmxlOiBmYWxzZSwKICAgICAgICAvL+W3peWZqOWFt+W8ueWHuuahhgogICAgICAgIGRpYWxvZ0Zvcm1WaXNpYmxlOiBmYWxzZSwKICAgICAgICAvL+ivlemqjOaXtumXtAogICAgICAgIHN5c2o6ICcnLAogICAgICAgIGZpbGR0cHM6IFtdLAogICAgICAgIC8v6K+V6aqM5by55Ye65qGGCiAgICAgICAgc3liZ0RpYWxvZ0Zvcm1WaXNpYmxlOiBmYWxzZSwKICAgICAgICAvL+a3u+WKoOivlemqjOaKpeWRigogICAgICAgIGFkZFN5YmdEaWFsb2dGb3JtVmlzaWJsZTogZmFsc2UsCiAgICAgICAgLy/lvLnlh7rmoYbooajljZUKICAgICAgICBmb3JtOiB7CiAgICAgICAgICB0eXBlOiJ4bCIKICAgICAgICB9LAogICAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICAgIC8v5bel5Zmo5YW36K+V6aqM5pWw5o2u6ZuG5ZCICiAgICAgICAgZ3Fqc3lMaXN0OiBbXSwKICAgICAgICAvL+ajgOS/ruaVsOaNrumbhuWQiAogICAgICAgIGdxakp4TGlzdDpbXSwKICAgICAgICAvL+WIoOmZpOaYr+WQpuWPr+eUqAogICAgICAgIG11bHRpcGxlU2Vuc29yOiB0cnVlLAogICAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgICAgLy/liKDpmaTpgInmi6nliJcKICAgICAgICBzZWxlY3RSb3dzOiBbXSwKICAgICAgICAvL+W3peWZqOWFt+aWh+S7tuS4iuS8oOWPguaVsAogICAgICAgIGdxakluZm9VcGxvYWREYXRhOiB7CiAgICAgICAgICBidXNpbmVzc0lkOiB1bmRlZmluZWQsCiAgICAgICAgfSwKICAgICAgICAvL+W3peWZqOWFt+aWh+S7tuS4iuS8oOivt+axguWktAogICAgICAgIGdxakluZm9VcEhlYWRlcjoge30sCgogICAgICAgIC8v6K+V6aqM5p+l6K+i5p2h5Lu2CiAgICAgICAgc3lRdWVyeUZvcm06IHsKICAgICAgICAgIGdxaklkOiAnJywKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMQogICAgICAgIH0sCgogICAgICAgIC8v6K+V6aqM5paw5aKe6KGo5Y2V5pWw5o2uCiAgICAgICAgc3lGcm9tOiB7CiAgICAgICAgICBpZDogJycsCiAgICAgICAgICBncWpJZDogJycsCiAgICAgICAgICBzeWR3SWQ6ICcnLAogICAgICAgICAgc3lkd05hbWU6ICcnLAogICAgICAgICAgc3lyeUlkOiAnJywKICAgICAgICAgIHN5cnlOYW1lOiAnJywKICAgICAgICAgIHN5c2o6ICcnLAogICAgICAgICAgc3lqbENvZGU6ICcnLAogICAgICAgICAgc3lqbE5hbWU6ICcnLAogICAgICAgICAgcmVtYXJrOiAnJwogICAgICAgIH0sCgogICAgICAgIGlzU3lEZXRhaWw6ZmFsc2UsCgogICAgICAgIC8v5qOA5L+u5p+l6K+i5p2h5Lu2CiAgICAgICAganhRdWVyeUZvcm06IHsKICAgICAgICAgIGdxaklkOiAnJywKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMQogICAgICAgIH0sCiAgICAgICAgLy/mo4Dkv67ooajljZUKICAgICAgICBqeEZvcm06IHsKICAgICAgICAgIGlkOiAnJywKICAgICAgICAgIGp4ZHdJZDogJycsCiAgICAgICAgICBqeGR3TmFtZTogJycsCiAgICAgICAgICBqeHJ5SWQ6ICcnLAogICAgICAgICAganhyeU5hbWU6ICcnLAogICAgICAgICAganhqZzogJycsCiAgICAgICAgICBqeHNqOiAnJywKICAgICAgICAgIHJlbWFyazogJycsCiAgICAgICAgICBncWpJZDogJycKICAgICAgICB9LAoKICAgICAgICAvL+S4u+ihqOmAieS4reihjOaVsOaNrgogICAgICAgIG1haW5Sb3dEYXRhOiB7fSwKICAgICAgICAvL+ivlemqjHRhYmxl5Yqg6L29CiAgICAgICAgc3lMb2FkaW5nOiBmYWxzZSwKICAgICAgICAvL+ivlemqjOmAieS4reihjAogICAgICAgIHN5U2VsZWN0Um93czogW10sCiAgICAgICAgLy/mo4Dkv650YWJsZeWKoOi9vQogICAgICAgIGp4TG9hZGluZzogZmFsc2UsCiAgICAgICAgLy/mo4Dkv67pgInkuK3ooYwKICAgICAgICBqeFNlbGVjdFJvd3M6IFtdCiAgICAgIH07CiAgICB9LAogICAgd2F0Y2g6IHt9LAogICAgY3JlYXRlZCgpIHsKCiAgICB9LAogICAgbW91bnRlZCgpIHsKICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICB9LAogICAgbWV0aG9kczogewogICAgICBleHBvcnRFeGNlbCgpewogICAgICAgIGV4cG9ydEV4Y2VsKHRoaXMucGFyYW1zLCfnur/ot6/lt6XlmajlhbcnKQogICAgICB9LAogICAgICBzb3J0Q2hhbmdlKHtjb2x1bW4sIHByb3AsIG9yZGVyfSl7CiAgICAgICAgaWYgKG9yZGVyKXsKICAgICAgICAgIGlmIChvcmRlci5pbmRleE9mKCJkZXNjIik+LTEpewogICAgICAgICAgICB0aGlzLnBhcmFtcy5teVNvcnRzID0gW3twcm9wOnByb3AsYXNjOmZhbHNlfV0KICAgICAgICAgIH1lbHNlIHsKICAgICAgICAgICAgdGhpcy5wYXJhbXMubXlTb3J0cyA9IFt7cHJvcDpwcm9wLGFzYzp0cnVlfV0KICAgICAgICAgIH0KICAgICAgICB9ZWxzZSB7CiAgICAgICAgICB0aGlzLnBhcmFtcy5teVNvcnRzID0gW3twcm9wOid1cGRhdGVUaW1lJyxhc2M6ZmFsc2V9XQogICAgICAgIH0KICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgfSwKICAgICAgaGFuZGxlQ2xpY2soKSB7CiAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgIH0sCiAgICAgIC8qKuS4i+i9vemZhOS7tiovCiAgICAgIGRvd25sb2FkSGFuZGxlKGlkKSB7CiAgICAgICAgZG93bmxvYWQoaWQpCiAgICAgIH0sCiAgICAgIC8qKgogICAgICAgKiDkuIrkvKDpmYTpmYTku7bkuYvliY3nmoTlpITnkIblh73mlbAKICAgICAgICogQHBhcmFtIGZpbGUKICAgICAgICovCiAgICAgIGdxakluZm9CZWZvcmVVcGxvYWQoZmlsZSkgewogICAgICAgIGNvbnN0IGZpbGVTaXplID0gZmlsZS5zaXplIDwgMTAyNCAqIDEwMjQgKiA1MCAvLzEwTQogICAgICAgIGlmICghZmlsZVNpemUpIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S4iuS8oOaWh+S7tuWkp+Wwj+S4jeiDvei2hei/hyA1ME1CIScpCiAgICAgICAgfQogICAgICAgIGxldCBzaXplID0gZmlsZS5zaXplIC8gMTAyNAogICAgICB9LAogICAgICAvKioKICAgICAgICog5LiK5Lyg6ZmE5Lu25oiQ5Yqf6LCD55So55qE5Ye95pWwCiAgICAgICAqIEBwYXJhbSByZXNwb25zZQogICAgICAgKiBAcGFyYW0gZmlsZQogICAgICAgKiBAcGFyYW0gZmlsZUxpc3QKICAgICAgICovCiAgICAgIGdxakluZm9vblN1Y2Nlc3MocmVzcG9uc2UsIGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgICAgY29uc29sZS5sb2coInJlc3BvbnNlOiIsIHJlc3BvbnNlKQogICAgICAgIGNvbnNvbGUubG9nKCJmaWxlOiIsIGZpbGUpCiAgICAgICAgY29uc29sZS5sb2coImZpbGVMaXN0OiIsIGZpbGVMaXN0KQoKICAgICAgICAvL+aWh+S7tmlkCiAgICAgICAgdGhpcy5mb3JtLmF0dGFjaG1lbnRpZCA9IHJlc3BvbnNlLmRhdGEuYnVzaW5lc3NJZAogICAgICAgIC8v5paH5Lu25ZCN56ewCiAgICAgICAgdGhpcy5mb3JtLmF0dGFjaG1lbnRuYW1lID0gcmVzcG9uc2UuZGF0YS5zeXNGaWxlLmZpbGVPbGROYW1lCiAgICAgIH0sCgogICAgICAvKioKICAgICAgICog56e76Zmk5paH5Lu2CiAgICAgICAqIEBwYXJhbSBmaWxlCiAgICAgICAqIEBwYXJhbSBmaWxlTGlzdAogICAgICAgKi8KICAgICAgZ3FqSW5mb2hhbmRsZVJlbW92ZShmaWxlLCBmaWxlTGlzdCkgewogICAgICBjb25zb2xlLmxvZygi5Yig6Zmk5paH5Lu254uX5a2QKysrKysrKysrKysrKysrKyIpCiAgICAgICAgY29uc29sZS5sb2coZmlsZSk7CiAgICAgIGNvbnNvbGUubG9nKGZpbGVMaXN0KQogICAgICB9LAogICAgICAvKioKICAgICAgICog5bel5Zmo5YW35LiK5Lyg5paH5Lu25Yiw5pyN5Yqh5ZmoCiAgICAgICAqLwogICAgICBncWpJbmZvU3VibWl0VXBsb2FkKCl7CiAgICAgICAgZGVidWdnZXIKICAgICAgICB0aGlzLmdxakluZm9VcGxvYWREYXRhLmJ1c2luZXNzSWQgPSBnZXRVVUlEKCkKICAgICAgICB0aGlzLiRyZWZzLnVwbG9hZEdxakluZm8uc3VibWl0KCk7CiAgICAgIH0sCgogICAgICAvL+W3peWZqOWFt+WIl+ihqOafpeivogogICAgICBhc3luYyBnZXREYXRhKHBhcmFtcykgewogICAgICAgIHRyeSB7CiAgICAgICAgICBjb25zdCBwYXJhbSA9IHsuLi50aGlzLnBhcmFtcywgLi4ucGFyYW1zfQogICAgICAgICAgLy9wYXJhbS5pc1B1YmxpYyA9IHRoaXMuYWN0aXZlTmFtZTsKICAgICAgICAgIGNvbnN0IHtkYXRhLCBjb2RlfSA9IGF3YWl0IGdldExpc3QocGFyYW0pOwogICAgICAgICAgaWYgKGNvZGUgPT09ICcwMDAwJykgewogICAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8udGFibGVEYXRhID0gZGF0YS5yZWNvcmRzCiAgICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci50b3RhbCA9IGRhdGEudG90YWwKICAgICAgICAgIH0KICAgICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgICBjb25zb2xlLmxvZyhlKQogICAgICAgIH0KICAgICAgfSwKCiAgICAgIC8v5bel5Zmo5YW35YiX6KGo5paw5aKe5oyJ6ZKuCiAgICAgIGFkZFNlbnNvckJ1dHRvbigpIHsKICAgICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZTsKICAgICAgICAvL+aJk+W8gOW8ueWHuuahhgogICAgICAgIHRoaXMuZGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgICAgIC8v6K6+572u5by55Ye65qGG6KGo5aS0CiAgICAgICAgdGhpcy5ncWpUaXRhbCA9ICLlt6XlmajlhbfmlrDlop4iOwogICAgICAgIC8v5riF56m65by55Ye65qGG5YaF5a65CiAgICAgICAgdGhpcy5mb3JtID0gewogICAgICAgICAgaXNQdWJsaWM6dGhpcy5hY3RpdmVOYW1lLAogICAgICAgICAgdHlwZToieGwiCiAgICAgICAgfTsKICAgICAgfSwKICAgICAgLy/lt6XlmajlhbfliJfooajor6bmg4XmjInpkq4KICAgICAgZ2V0R3FqSW5mbyhyb3cpIHsKICAgICAgICAvL+aJk+W8gOW8ueWHuuahhgogICAgICAgIHRoaXMuZGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgICAgIC8v6K6+572u5by55Ye65qGG6KGo5aS0CiAgICAgICAgdGhpcy5ncWpUaXRhbCA9ICLlt6Xlmajlhbfor6bmg4UiOwogICAgICAgIC8v56aB55So5omA5pyJ6L6T5YWl5qGGCiAgICAgICAgdGhpcy5pc0Rpc2FibGVkID0gdHJ1ZTsKICAgICAgICAvL+e7meW8ueWHuuahhui1i+WAvAogICAgICAgIHRoaXMuZm9ybSA9IHsuLi5yb3d9CiAgICAgIH0sCiAgICAgIC8v5bel5Zmo5YW35L+u5pS55oyJ6ZKuCiAgICAgIHVwZGF0ZUdxakluZm8ocm93KSB7CiAgICAgICAgLy/miZPlvIDlvLnlh7rmoYYKICAgICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZTsKICAgICAgICAvL+iuvue9ruW8ueWHuuahhuihqOWktAogICAgICAgIHRoaXMuZ3FqVGl0YWwgPSAi5bel5Zmo5YW35L+u5pS5IjsKICAgICAgICAvL+W8gOWQr+W8ueWHuuahhuWGhei+k+WFpeahhue8lui+keadg+mZkAogICAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICAgIC8v57uZ5by55Ye65qGG5YaF6LWL5YC8CiAgICAgICAgdGhpcy5mb3JtID0gey4uLnJvd307CgogICAgICB9LAogICAgICAvL+W3peWZqOWFt+WIl+ihqOaWsOWinuS/ruaUueS/neWtmAogICAgICBhc3luYyBxeGNvbW1pdCgpIHsKICAgICAgICBhd2FpdCB0aGlzLiRyZWZzWydmb3JtJ10udmFsaWRhdGUoYXN5bmMgKHZhbGlkKSA9PiB7CiAgICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICBsZXQge2NvZGV9ID0gYXdhaXQgc2F2ZU9yVXBkYXRlKHRoaXMuZm9ybSkKICAgICAgICAgICAgICBpZiAoY29kZSA9PT0gJzAwMDAnKSB7CiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKnyIpCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgICAgICAgY29uc29sZS5sb2coZSkKICAgICAgICAgICAgfQogICAgICAgICAgICAvL+aBouWkjeWIhumhtQogICAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIucGFnZVJlc2l6ZSA9ICdZJzsKICAgICAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgICAgICAgIHRoaXMuZGlhbG9nRm9ybVZpc2libGUgPSBmYWxzZTsKICAgICAgICAgIH19KQogICAgICB9LAogICAgICAvL+WIoOmZpOW3peWZqOWFt+WIl+ihqAogICAgICBkZWxldGVSb3coaWQpIHsKICAgICAgICAvLyBpZiAodGhpcy5zZWxlY3RSb3dzLmxlbmd0aCA8IDEpIHsKICAgICAgICAvLyAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+36YCJ5oup5q2j56Gu55qE5pWw5o2u77yB77yB77yBIikKICAgICAgICAvLyAgIHJldHVybgogICAgICAgIC8vIH0KICAgICAgICAvLyBsZXQgaWRzID0gdGhpcy5zZWxlY3RSb3dzLm1hcChpdGVtID0+IHsKICAgICAgICAvLyAgIHJldHVybiBpdGVtLm9iaklkCiAgICAgICAgLy8gfSk7CiAgICAgICAgbGV0IGlkcz1bXTsKICAgICAgICBpZHMucHVzaChpZCk7CiAgICAgICAgdGhpcy4kY29uZmlybSgn5q2k5pON5L2c5bCG5rC45LmF5Yig6Zmk6K+l5pWw5o2uLCDmmK/lkKbnu6fnu60/JywgJ+aPkOekuicsIHsKICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgICByZW1vdmUoaWRzKS50aGVuKCh7Y29kZX0pID0+IHsKICAgICAgICAgICAgaWYgKGNvZGUgPT09ICcwMDAwJykgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOaIkOWKnyEnCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgdGhpcy5nZXREYXRhKCkKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAn5Yig6Zmk5aSx6LSlIScKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfQogICAgICAgICAgfSkKICAgICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogJ2luZm8nLAogICAgICAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raI5Yig6ZmkJwogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnBhZ2VSZXNpemUgPSAnWSc7CiAgICAgICAgdGhpcy5nZXREYXRhKCkKICAgICAgfSwKCiAgICAgIC8v5p+l55yL6K+V6aqMCiAgICAgIGhhbmRsZVNlYXJjaFNZQ2xpY2socm93KSB7CiAgICAgICAgdGhpcy5zeVNlbGVjdFJvd3MgPSBbXQogICAgICAgIHRoaXMubWFpblJvd0RhdGEgPSByb3cKICAgICAgICB0aGlzLnN5UXVlcnlGb3JtLmdxaklkID0gcm93Lm9iaklkCiAgICAgICAgdGhpcy5zeWJnRGlhbG9nRm9ybVZpc2libGUgPSB0cnVlCiAgICAgICAgdGhpcy5nZXRZeFN5RGF0YSgpCiAgICAgIH0sCgogICAgICAvL+afpeeci+ajgOS/rgogICAgICBoYW5kbGVTZXJjaEpXWENsaWNrKHJvdykgewogICAgICAgIHRoaXMubWFpblJvd0RhdGEgPSByb3cKICAgICAgICB0aGlzLmp4UXVlcnlGb3JtLmdxaklkID0gcm93Lm9iaklkCiAgICAgICAgdGhpcy5qd3hEaWFsb2dGb3JtVmlzaWJsZSA9IHRydWUKICAgICAgICB0aGlzLmdldEp4UmVjb3JkcygpCiAgICAgIH0sCiAgICAgIC8v5re75Yqg5qOA5L+uCiAgICAgIGFkZEp4QnV0dG9uKCkgewogICAgICAgIHRoaXMuanhGb3JtID0gdGhpcy4kb3B0aW9ucy5kYXRhKCkuanhGb3JtCiAgICAgICAgdGhpcy5qeEZvcm0uZ3FqSWQgPSB0aGlzLm1haW5Sb3dEYXRhLm9iaklkCiAgICAgICAgdGhpcy5hZGRKd3hTeWJnRGlhbG9nRm9ybVZpc2libGUgPSB0cnVlCiAgICAgIH0sCiAgICAgIHVwZGF0ZUp4KHJvdykgewogICAgICAgIHRoaXMuanhGb3JtID0gcm93CiAgICAgICAgdGhpcy5hZGRKd3hTeWJnRGlhbG9nRm9ybVZpc2libGUgPSB0cnVlCiAgICAgIH0sCiAgICAgIC8v5re75Yqg6K+V6aqMCiAgICAgIGFkZFN5QnV0dG9uKCkgewogICAgICAgIHRoaXMuc3lGcm9tID0gdGhpcy4kb3B0aW9ucy5kYXRhKCkuc3lGcm9tCiAgICAgICAgdGhpcy5zeUZyb20uZ3FqSWQgPSB0aGlzLm1haW5Sb3dEYXRhLm9iaklkCiAgICAgICAgdGhpcy5hZGRTeWJnRGlhbG9nRm9ybVZpc2libGUgPSB0cnVlCiAgICAgIH0sCiAgICAgIHVwZGF0ZVN5KHJvdykgewogICAgICAgIHRoaXMuc3lGcm9tID0gcm93CiAgICAgICAgdGhpcy5hZGRTeWJnRGlhbG9nRm9ybVZpc2libGUgPSB0cnVlCiAgICAgIH0sCiAgICAgIC8v5q+P6aG15bGV56S65pWw6YeP54K55Ye75LqL5Lu2CiAgICAgIGhhbmRsZVNpemVDaGFuZ2UoKSB7CgogICAgICB9LAogICAgICAvL+mhteeggeaUueWPmOS6i+S7tgogICAgICBoYW5kbGVDdXJyZW50Q2hhbmdlKCkgewoKICAgICAgfSwKICAgICAgLy/moJHngrnlh7vkuovku7YKICAgICAgaGFuZGxlTm9kZUNsaWNrKCkgewoKICAgICAgfSwKCiAgICAgIGZpbHRlclJlc2V0KCkgewoKICAgICAgfSwKICAgICAgLy/pgInmi6nmr4/kuIDooYwKICAgICAgc2VsZWN0Q2hhbmdlKHJvd3MpIHsKICAgICAgICB0aGlzLnNlbGVjdFJvd3MgPSByb3dzCiAgICAgIH0sCgogICAgICAvL+iOt+WPluivlemqjOiusOW9leaVsOaNrgogICAgICBnZXRZeFN5RGF0YSgpIHsKICAgICAgICB0aGlzLnN5TG9hZGluZyA9IHRydWUKICAgICAgICBnZXRZeFN5UmVjb3Jkcyh0aGlzLnN5UXVlcnlGb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICB0aGlzLmdxanN5TGlzdCA9IHJlcy5kYXRhLnJlY29yZHMKICAgICAgICAgIHRoaXMuc3lRdWVyeUZvcm0udG90YWwgPSByZXMuZGF0YS50b3RhbAogICAgICAgICAgdGhpcy5zeUxvYWRpbmcgPSBmYWxzZQogICAgICAgIH0pCiAgICAgIH0sCgogICAgICAvL+aWsOWinuS/ruaUueivlemqjOiusOW9leaVsOaNrgogICAgICBzYXZlT3JVcGRhdGVTeSgpIHsKICAgICAgICBzYXZlT3JVcGRhdGVZeFN5UmVjb3Jkcyh0aGlzLnN5RnJvbSkudGhlbihyZXMgPT4gewogICAgICAgICAgdGhpcy5nZXRZeFN5RGF0YSgpCiAgICAgICAgICB0aGlzLmFkZFN5YmdEaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlCiAgICAgICAgfSkKICAgICAgfSwKICAgICAgLy/mibnph4/liKDpmaTor5XpqozmlbDmja4KICAgICAgZGVsZXRlWXhTeSgpIHsKICAgICAgICB0aGlzLiRjb25maXJtKCfmraTmk43kvZzlsIbmsLjkuYXliKDpmaTor6XmlbDmja4sIOaYr+WQpue7p+e7rT8nLCAn5o+Q56S6JywgewogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICAgIGxldCBpZHMgPSBbXQogICAgICAgICAgdGhpcy5zeVNlbGVjdFJvd3MuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgICAgaWRzLnB1c2goaXRlbS5pZCkKICAgICAgICAgIH0pCiAgICAgICAgICBkZWxldGVZeFN5UmVjb3JkcyhpZHMpLnRoZW4oKHsgY29kZSB9KSA9PiB7CiAgICAgICAgICAgIGlmIChjb2RlID09PSAnMDAwMCcpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICfliKDpmaTmiJDlip8hJwogICAgICAgICAgICAgIH0pCgogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICfliKDpmaTlpLHotKUhJwogICAgICAgICAgICAgIH0pCiAgICAgICAgICAgIH0KICAgICAgICAgICAgdGhpcy5nZXRZeFN5RGF0YSgpCiAgICAgICAgICB9KQogICAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAnaW5mbycsCiAgICAgICAgICAgIG1lc3NhZ2U6ICflt7Llj5bmtojliKDpmaQnCiAgICAgICAgICB9KQogICAgICAgIH0pCiAgICAgIH0sCgogICAgICAvL+iOt+WPluajgOS/ruiusOW9leaVsOaNrgogICAgICBnZXRKeFJlY29yZHMoKSB7CiAgICAgICAgdGhpcy5qeExvYWRpbmcgPSB0cnVlCiAgICAgICAgZ2V0QXNzZXRHcWpKeFJlY29yZHModGhpcy5qeFF1ZXJ5Rm9ybSkudGhlbihyZXMgPT4gewogICAgICAgICAgdGhpcy5ncWpKeExpc3QgPSByZXMuZGF0YS5yZWNvcmRzCiAgICAgICAgICB0aGlzLmp4UXVlcnlGb3JtLnRvdGFsID0gcmVzLmRhdGEudG90YWwKICAgICAgICAgIHRoaXMuanhMb2FkaW5nID0gZmFsc2UKICAgICAgICB9KQogICAgICB9LAogICAgICAvL+aWsOWinuS/ruaUueajgOS/ruiusOW9leaVsOaNrgogICAgICBzYXZlT3JVcGRhdGVKeCgpIHsKICAgICAgICBzYXZlT3JVcGRhdGVBc3NldEdxakp4UmVjb3Jkcyh0aGlzLmp4Rm9ybSkudGhlbihyZXMgPT4gewogICAgICAgICAgdGhpcy5nZXRKeFJlY29yZHMoKQogICAgICAgICAgdGhpcy5hZGRKd3hTeWJnRGlhbG9nRm9ybVZpc2libGUgPSBmYWxzZQogICAgICAgIH0pCiAgICAgIH0sCiAgICAgIGRlbGV0ZUp4RGF0YSgpIHsKCiAgICAgICAgdGhpcy4kY29uZmlybSgn5q2k5pON5L2c5bCG5rC45LmF5Yig6Zmk6K+l5pWw5o2uLCDmmK/lkKbnu6fnu60/JywgJ+aPkOekuicsIHsKICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgICBsZXQgaWRzID0gW10KICAgICAgICAgIHRoaXMuanhTZWxlY3RSb3dzLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICAgIGlkcy5wdXNoKGl0ZW0uaWQpCiAgICAgICAgICB9KQogICAgICAgICAgZGVsZXRlQXNzZXRHcWpKeFJlY29yZHMoaWRzKS50aGVuKCh7IGNvZGUgfSkgPT4gewogICAgICAgICAgICBpZiAoY29kZSA9PT0gJzAwMDAnKSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAn5Yig6Zmk5oiQ5YqfIScKICAgICAgICAgICAgICB9KQoKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAn5Yig6Zmk5aSx6LSlIScKICAgICAgICAgICAgICB9KQogICAgICAgICAgICB9CiAgICAgICAgICAgIHRoaXMuZ2V0SnhSZWNvcmRzKCkKICAgICAgICAgIH0pCiAgICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICdpbmZvJywKICAgICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iOWIoOmZpCcKICAgICAgICAgIH0pCiAgICAgICAgfSkKICAgICAgfSwKCiAgICAgIHN5Um93Q2xpY2socm93cykgewogICAgICAgIHRoaXMuJHJlZnMuc3lUYWJsZS50b2dnbGVSb3dTZWxlY3Rpb24ocm93cykKICAgICAgfSwKICAgICAgc3lDdXJyZW50Q2hhbmdlKHZhbCkgewogICAgICAgIHRoaXMuc3lTZWxlY3RSb3dzID0gdmFsCiAgICAgIH0sCiAgICAgIGp4Um93Q2xpY2socm93cykgewogICAgICAgIHRoaXMuJHJlZnMuanhUYWJsZS50b2dnbGVSb3dTZWxlY3Rpb24ocm93cykKICAgICAgfSwKICAgICAganhDdXJyZW50Q2hhbmdlKHZhbCkgewogICAgICAgIHRoaXMuanhTZWxlY3RSb3dzID0gdmFsCiAgICAgIH0KICAgIH0KICB9Owo="}, {"version": 3, "sources": ["xlgqj.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0WA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "xlgqj.vue", "sourceRoot": "src/views/dagangOilfield/dwzygl/gqjgl", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      ref=\"filter1\"\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      @handleReset=\"filterReset\"\n    />\n    <el-white class=\"button-group1\">\n<!--      <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\" type=\"card\">\n        <el-tab-pane label=\"个人工具\" name=\"grgj\">\n        </el-tab-pane>\n        <el-tab-pane label=\"公用工具\" name=\"gygj\">\n        </el-tab-pane>\n      </el-tabs>-->\n      <div class=\"button_btn\">\n        <el-button @click=\"addSensorButton\"  v-hasPermi=\"['xlgql:button:add']\" icon=\"el-icon-plus\" type=\"primary\">新增\n<!--                   v-hasPermi=\"['gqjgl:button:insert']\"-->\n        </el-button>\n        <el-button type=\"primary\" icon=\"el-icon-download\" @click=\"exportExcel()\">导出</el-button>\n      </div>\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"selectChange\"\n        height=\"67vh\"\n        @sort-events= \"sortChange\"\n      >\n      <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\" width=\"180\" :resizable=\"false\">\n        <template slot-scope=\"scope\">\n           <el-button  type=\"text\" size=\"small\" @click=\"getGqjInfo(scope.row)\" class=\"el-icon-view\" title=\"详情\"></el-button>\n           <el-button  type=\"text\" size=\"small\" v-if=\"scope.row.createBy==currUser\"  @click=\"updateGqjInfo(scope.row)\" class='el-icon-edit' title=\"编辑\"></el-button>\n           <el-button  type=\"text\" size=\"small\" v-if=\"scope.row.createBy==currUser\"  @click=\"deleteRow(scope.row.objId)\" class=\"el-icon-delete\" title=\"删除\"></el-button>\n        </template>\n      </el-table-column>\n      </comp-table>\n    </el-white>\n    <!-- 工器具台账详情修改新增 -->\n    <el-dialog :title=\"gqjTital\" :visible.sync=\"dialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"60%\" v-dialogDrag>\n      <el-form :model=\"form\" label-width=\"80px\" :disabled=\"isDisabled\" :rules=\"rules\" ref=\"form\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"名称\" prop=\"sbmc\">\n              <el-input v-model=\"form.sbmc\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"规格型号\" prop=\"xh\">\n              <el-input v-model=\"form.xh\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"单位\" prop=\"ssgs\">\n              <el-input v-model=\"form.ssgs\" placeholder=\"\" :disabled=\"isDisabled\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"数量\" prop=\"sl\">\n              <el-input-number :min=\"1\" v-model=\"form.sl\" :disabled=\"isDisabled\"></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\" >\n            <el-form-item label=\"负责人\" prop=\"fzr\">\n              <el-input v-model=\"form.fzr\" placeholder=\"\" :disabled=\"isDisabled\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"生产厂家\" style=\"width: 100%\">\n              <el-input v-model=\"form.sccj\" placeholder=\"\" :disabled=\"isDisabled\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"启用年月\" class=\"add_sy_tyrq\">\n              <el-date-picker\n                  v-model=\"form.tyrq\"\n                  type=\"date\"\n                  placeholder=\"选择日期\"\n                  value-format=\"yyyy-MM-dd\"\n                  :disabled=\"isDisabled\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"存放地点\">\n              <el-input v-model=\"form.cfdd\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\">\n              <el-input type=\"textarea\" v-model=\"form.bz\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n<!--        <el-row :gutter=\"4\" class=\"pull-left\">\n          <el-col :span=\"20\">\n            <el-form-item label=\"附件:\" prop=\"attachmentid\">\n              <template slot-scope=\"scope\">\n                <span><a style=\"color: blue\" @click=\"downloadHandle(form.attachmentid)\">{{ form.attachmentname }}</a>\n                </span>\n                <el-upload\n                  class=\"upload-demo\"\n                  accept=\".jpg,.png,.rar,.txt,.zip,.doc,.ppt,.xls,.pdf,.docx,.xlsx,.mp4,.avi,.rmvb\"\n                  ref=\"uploadGqjInfo\"\n                  action=\"/isc-api/file/upload\"\n                  :before-upload=\"gqjInfoBeforeUpload\"\n                  :on-success=\"gqjInfoonSuccess\"\n                  :on-remove=\"gqjInfohandleRemove\"\n                  :data=\"gqjInfoUploadData\"\n                  :headers=\"gqjInfoUpHeader\"\n                  :auto-upload=\"false\">\n                  <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选取文件</el-button>\n                  <el-button style=\"margin-left: 10px;\" size=\"small\" type=\"success\" @click=\"gqjInfoSubmitUpload\">上传到服务器\n                  </el-button>\n                </el-upload>\n              </template>\n            </el-form-item>\n          </el-col>\n        </el-row>-->\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"qxcommit\" v-if=\"gqjTital === '工器具新增' || gqjTital === '工器具修改' \" class=\"pmyBtn\">\n          确 定\n        </el-button>\n      </div>\n    </el-dialog>\n    <!--试验报告弹出框-->\n    <el-dialog title=\"试验报告记录\" :visible.sync=\"sybgDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"60%\">\n      <el-form :model=\"form\" label-width=\"80px\">\n        <el-form-item style=\"float: right\">\n          <el-button class=\"mb8\" @click=\"addSyButton\" type=\"primary\" icon=\"el-icon-plus\"\n                     v-hasPermi=\"['gqjgl:button:sydialog:insert']\">添加\n          </el-button>\n          <el-button class=\"mb8\" @click=\"deleteYxSy\" type=\"danger\" icon=\"el-icon-delete\" v-hasPermi=\"['gqjgl:button:sydialog:delete']\">删除\n          </el-button>\n        </el-form-item>\n      </el-form>\n      <el-table\n        ref=\"syTable\"\n        stripe\n        border\n        v-loading=\"syLoading\"\n        :data=\"gqjsyList\"\n        @row-click=\"syRowClick\"\n        @selection-change=\"syCurrentChange\"\n        max-height=\"550\">\n        <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n        <el-table-column label=\"试验单位\" align=\"center\" prop=\"sydwName\"></el-table-column>\n        <el-table-column label=\"试验人员\" align=\"center\" prop=\"syryName\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"试验结论\" align=\"center\" prop=\"syjlName\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"试验时间\" align=\"center\" prop=\"sysj\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column\n          align=\"center\"\n          label=\"操作\"\n          width=\"150\">\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" size=\"small\">下载附件</el-button>\n            <el-button type=\"text\" @click=\"updateSy(scope.row)\" size=\"small\">修改</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      <pagination\n        v-show=\"syQueryForm.total>0\"\n        :total=\"syQueryForm.total\"\n        :page.sync=\"syQueryForm.pageNum\"\n        :limit.sync=\"syQueryForm.pageSize\"\n        @pagination=\"getYxSyData\"\n      />\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"sybgDialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"sybgDialogFormVisible = false\" class=\"pmyBtn\">确 定</el-button>\n      </div>\n    </el-dialog>\n    <!--添加试验报告-->\n    <el-dialog title=\"添加试验报告\" :visible.sync=\"addSybgDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"50%\">\n      <el-form label-width=\"80px\" v-model=\"syFrom\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item hidden>\n              <el-input v-model=\"syFrom.id\"></el-input>\n              <el-input v-model=\"syFrom.gqjId\"></el-input>\n              <el-input v-model=\"syFrom.sydwId\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"试验单位\">\n<!--              <el-select v-model=\"syFrom.sydwName\" placeholder=\"\" :disabled=\"isSyDetail\"></el-select>-->\n              <el-input v-model=\"syFrom.sydwName\" placeholder=\"\" :disabled=\"isSyDetail\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item hidden label=\"试验人员id\">\n              <el-input v-model=\"syFrom.syryId\" hidden></el-input>\n            </el-form-item>\n            <el-form-item label=\"试验人员\">\n              <!--<el-select v-model=\"syFrom.syryName\" placeholder=\"\">\n              </el-select>-->\n              <el-input v-model=\"syFrom.syryName\" :disabled=\"isSyDetail\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"试验时间\" class=\"add_sy_tyrq\">\n              <el-date-picker\n                :disabled=\"isSyDetail\"\n                v-model=\"syFrom.sysj\"\n                type=\"date\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"选择日期时间\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item hidden>\n              <el-input type=\"textarea\" v-model=\"syFrom.syjlCode\" hidden :disabled=\"isSyDetail\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"试验结论\">\n              <el-input type=\"textarea\" v-model=\"syFrom.syjlName\" :disabled=\"isSyDetail\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"20\">\n            <el-form-item label=\"备注\">\n              <el-input type=\"textarea\" v-model=\"syFrom.remark\" :disabled=\"isSyDetail\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n<!--        <el-row :gutter=\"20\">-->\n<!--          <el-upload-->\n<!--            style=\"float: right\"-->\n<!--            class=\"upload-demo\"-->\n<!--            action=\"https://jsonplaceholder.typicode.com/posts/\"-->\n<!--            :on-preview=\"handlePreview\"-->\n<!--            :on-remove=\"handleRemove\"-->\n<!--            :before-remove=\"beforeRemove\"-->\n<!--            multiple-->\n<!--            :limit=\"3\"-->\n<!--            :on-exceed=\"handleExceed\"-->\n<!--            :file-list=\"fileList\">-->\n<!--            <el-button size=\"small\" type=\"primary\">点击上传</el-button>-->\n<!--            <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png文件，且不超过500kb</div>-->\n<!--          </el-upload>-->\n<!--        </el-row>-->\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"addSybgDialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"saveOrUpdateSy\" class=\"pmyBtn\">确 定</el-button>\n      </div>\n    </el-dialog>\n    <!--检修记录-->\n    <!--检修记录弹出框-->\n    <el-dialog title=\"检修维护记录\" :visible.sync=\"jwxDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"60%\">\n      <el-form :model=\"form\" label-width=\"80px\">\n        <el-form-item style=\"float: right\">\n          <el-button class=\"mb8\" @click=\"addJxButton\" type=\"primary\" icon=\"el-icon-plus\"\n                     v-hasPermi=\"['gqjgl:button:sydialog:insert']\">\n            添加\n          </el-button>\n          <el-button class=\"mb8\" @click=\"deleteJxData\" type=\"danger\" icon=\"el-icon-delete\" v-hasPermi=\"['gqjgl:button:sydialog:delete']\">删除\n          </el-button>\n        </el-form-item>\n      </el-form>\n      <el-table\n        ref=\"jxTable\"\n        stripe\n        border\n        v-loading=\"jxLoading\"\n        :data=\"gqjJxList\"\n        @row-click=\"jxRowClick\"\n        @selection-change=\"jxCurrentChange\"\n        max-height=\"550\">\n        <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n        <el-table-column label=\"检修单位\" align=\"center\" prop=\"jxdwName\"></el-table-column>\n        <el-table-column label=\"检修人员\" align=\"center\" prop=\"jxryName\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"检修结果\" align=\"center\" prop=\"jxjg\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"检修时间\" align=\"center\" prop=\"jxsj\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column\n          align=\"center\"\n          label=\"操作\"\n          width=\"150\">\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" size=\"small\">下载附件</el-button>\n            <el-button type=\"text\" @click=\"updateJx(scope.row)\" size=\"small\">修改</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      <pagination\n        v-show=\"jxQueryForm.total>0\"\n        :total=\"jxQueryForm.total\"\n        :page.sync=\"jxQueryForm.pageNum\"\n        :limit.sync=\"jxQueryForm.pageSize\"\n        @pagination=\"getJxRecords\"\n      />\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"jwxDialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"jwxDialogFormVisible = false\" class=\"pmyBtn\">确 定</el-button>\n      </div>\n    </el-dialog>\n    <!--添加检修记录-->\n    <el-dialog title=\"添加检修维护记录\" :visible.sync=\"addJwxSybgDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"50%\">\n      <el-form label-width=\"80px\" :model=\"jxForm\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item hidden>\n              <el-input v-model=\"jxForm.id\"></el-input>\n              <el-input v-model=\"jxForm.gqjId\"></el-input>\n              <el-input v-model=\"jxForm.jxdwId\"></el-input>\n              <el-input v-model=\"jxForm.jxryId\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"检修单位\">\n<!--              <el-select v-model=\"jxForm.jxdwName\" placeholder=\"\"></el-select>-->\n              <el-input v-model=\"jxForm.jxdwName\" placeholder=\"\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"检修人员\">\n<!--              <el-select v-model=\"jxForm.jxryName\" placeholder=\"\"></el-select>-->\n              <el-input v-model=\"jxForm.jxryName\" placeholder=\"\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"检修时间\" class=\"add_sy_tyrq\">\n              <el-date-picker\n                v-model=\"jxForm.jxsj\"\n                type=\"date\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"选择日期时间\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"检修结果\">\n              <el-input type=\"textarea\" v-model=\"jxForm.jxjg\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\">\n              <el-input type=\"textarea\" v-model=\"jxForm.remark\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"addJwxSybgDialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"saveOrUpdateJx\" class=\"pmyBtn\">确 定</el-button>\n      </div>\n    </el-dialog>\n    <!-- 弹出框结束 -->\n  </div>\n\n</template>\n\n<script>\nimport { getUUID } from '@/utils/ruoyi'\nimport {\n  deleteAssetGqjJxRecords,\n  deleteYxSyRecords, exportExcel,\n  getAssetGqjJxRecords,\n  getList,\n  getYxSyRecords,\n  remove,\n  saveOrUpdate,\n  saveOrUpdateAssetGqjJxRecords,\n  saveOrUpdateYxSyRecords\n} from '@/api/dagangOilfield/asset/assetGqj'\nimport CompTable from 'com/CompTable'\nimport ElFilter from 'com/ElFilter'\nimport { download } from '@/api/tool/file'\n\nexport default {\n    components: {CompTable, ElFilter},\n    name: \"gqjgl\",\n    data() {\n      return {\n        currUser:this.$store.getters.name,\n        // 表单校验\n        rules: {\n          sl: [{ required: true, message: '请输入', trigger: 'blur' }],\n          sbmc: [{ required: true, message: '请输入', trigger: 'blur' }],\n          xh: [{ required: true, message: '请输入', trigger: 'blur' }],\n          fzr: [{ required: true, message: '请输入', trigger: 'blur' }],\n        },\n        activeName:\"grgj\",\n        params:{\n          type:\"xl\"\n        },\n        //工器具详情框字段控制\n        isDisabled: false,\n        //工器具弹出框表头\n        gqjTital: \"工器具新增\",\n\n        //表格内容\n        tableAndPageInfo: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n            {prop: 'sbmc', label: '名称', minWidth: '180',custom:true},\n            {prop: 'xh', label: '规格型号', minWidth: '180'},\n            {prop: 'fzr', label: '负责人', minWidth: '120'},\n            {prop: 'ssgs', label: '单位', minWidth: '120'},\n            {prop: 'sl', label: '数量', minWidth: '50'},\n            {prop: 'sccj', label: '生产厂家', minWidth: '120'},\n            {prop: 'tyrq', label: '启用年月', minWidth: '120',custom:true},\n            {prop: 'cfdd', label: '存放地点', minWidth: '250'},\n            // {\n            //   fixed: \"right\",\n            //   prop: 'operation',\n            //   label: '操作',\n            //   minWidth: '150px',\n            //   style: {display: 'block'},\n            //   operation: [\n            //     // {name: '试验', clickFun: this.handleSearchSYClick},\n            //     // {name: '检修', clickFun: this.handleSerchJWXClick},\n            //     {name: '修改', clickFun: this.updateGqjInfo},\n            //     {name: '详情', clickFun: this.getGqjInfo},\n            //   ],\n            // },\n          ]\n        },\n        //筛选条件\n        filterInfo: {\n          data: {\n            fzr: '',\n            ssgs: '',\n            yxbz: '',\n            phone: '',\n          },\n          fieldList: [\n            {label: '名称', type: 'input', value: 'sbmc'},\n            {label: '规格型号', type: 'input', value: 'xh'},\n            {label: '负责人', type: 'input', value: 'fzr'},\n            {label: '存放地点', type: 'input', value: 'cfdd'},\n          ]\n        },\n        //检修记录弹出框\n        jwxDialogFormVisible: false,\n        //添加检修记录弹出框\n        addJwxSybgDialogFormVisible: false,\n        //工器具弹出框\n        dialogFormVisible: false,\n        //试验时间\n        sysj: '',\n        fildtps: [],\n        //试验弹出框\n        sybgDialogFormVisible: false,\n        //添加试验报告\n        addSybgDialogFormVisible: false,\n        //弹出框表单\n        form: {\n          type:\"xl\"\n        },\n        loading: false,\n        //工器具试验数据集合\n        gqjsyList: [],\n        //检修数据集合\n        gqjJxList:[],\n        //删除是否可用\n        multipleSensor: true,\n        showSearch: true,\n        //删除选择列\n        selectRows: [],\n        //工器具文件上传参数\n        gqjInfoUploadData: {\n          businessId: undefined,\n        },\n        //工器具文件上传请求头\n        gqjInfoUpHeader: {},\n\n        //试验查询条件\n        syQueryForm: {\n          gqjId: '',\n          total: 0,\n          pageSize: 10,\n          pageNum: 1\n        },\n\n        //试验新增表单数据\n        syFrom: {\n          id: '',\n          gqjId: '',\n          sydwId: '',\n          sydwName: '',\n          syryId: '',\n          syryName: '',\n          sysj: '',\n          syjlCode: '',\n          syjlName: '',\n          remark: ''\n        },\n\n        isSyDetail:false,\n\n        //检修查询条件\n        jxQueryForm: {\n          gqjId: '',\n          total: 0,\n          pageSize: 10,\n          pageNum: 1\n        },\n        //检修表单\n        jxForm: {\n          id: '',\n          jxdwId: '',\n          jxdwName: '',\n          jxryId: '',\n          jxryName: '',\n          jxjg: '',\n          jxsj: '',\n          remark: '',\n          gqjId: ''\n        },\n\n        //主表选中行数据\n        mainRowData: {},\n        //试验table加载\n        syLoading: false,\n        //试验选中行\n        sySelectRows: [],\n        //检修table加载\n        jxLoading: false,\n        //检修选中行\n        jxSelectRows: []\n      };\n    },\n    watch: {},\n    created() {\n\n    },\n    mounted() {\n      this.getData();\n    },\n    methods: {\n      exportExcel(){\n        exportExcel(this.params,'线路工器具')\n      },\n      sortChange({column, prop, order}){\n        if (order){\n          if (order.indexOf(\"desc\")>-1){\n            this.params.mySorts = [{prop:prop,asc:false}]\n          }else {\n            this.params.mySorts = [{prop:prop,asc:true}]\n          }\n        }else {\n          this.params.mySorts = [{prop:'updateTime',asc:false}]\n        }\n        this.getData();\n      },\n      handleClick() {\n        this.getData();\n      },\n      /**下载附件*/\n      downloadHandle(id) {\n        download(id)\n      },\n      /**\n       * 上传附附件之前的处理函数\n       * @param file\n       */\n      gqjInfoBeforeUpload(file) {\n        const fileSize = file.size < 1024 * 1024 * 50 //10M\n        if (!fileSize) {\n          this.$message.error('上传文件大小不能超过 50MB!')\n        }\n        let size = file.size / 1024\n      },\n      /**\n       * 上传附件成功调用的函数\n       * @param response\n       * @param file\n       * @param fileList\n       */\n      gqjInfoonSuccess(response, file, fileList) {\n        console.log(\"response:\", response)\n        console.log(\"file:\", file)\n        console.log(\"fileList:\", fileList)\n\n        //文件id\n        this.form.attachmentid = response.data.businessId\n        //文件名称\n        this.form.attachmentname = response.data.sysFile.fileOldName\n      },\n\n      /**\n       * 移除文件\n       * @param file\n       * @param fileList\n       */\n      gqjInfohandleRemove(file, fileList) {\n      console.log(\"删除文件狗子++++++++++++++++\")\n        console.log(file);\n      console.log(fileList)\n      },\n      /**\n       * 工器具上传文件到服务器\n       */\n      gqjInfoSubmitUpload(){\n        debugger\n        this.gqjInfoUploadData.businessId = getUUID()\n        this.$refs.uploadGqjInfo.submit();\n      },\n\n      //工器具列表查询\n      async getData(params) {\n        try {\n          const param = {...this.params, ...params}\n          //param.isPublic = this.activeName;\n          const {data, code} = await getList(param);\n          if (code === '0000') {\n            this.tableAndPageInfo.tableData = data.records\n            this.tableAndPageInfo.pager.total = data.total\n          }\n        } catch (e) {\n          console.log(e)\n        }\n      },\n\n      //工器具列表新增按钮\n      addSensorButton() {\n        this.isDisabled = false;\n        //打开弹出框\n        this.dialogFormVisible = true;\n        //设置弹出框表头\n        this.gqjTital = \"工器具新增\";\n        //清空弹出框内容\n        this.form = {\n          isPublic:this.activeName,\n          type:\"xl\"\n        };\n      },\n      //工器具列表详情按钮\n      getGqjInfo(row) {\n        //打开弹出框\n        this.dialogFormVisible = true;\n        //设置弹出框表头\n        this.gqjTital = \"工器具详情\";\n        //禁用所有输入框\n        this.isDisabled = true;\n        //给弹出框赋值\n        this.form = {...row}\n      },\n      //工器具修改按钮\n      updateGqjInfo(row) {\n        //打开弹出框\n        this.dialogFormVisible = true;\n        //设置弹出框表头\n        this.gqjTital = \"工器具修改\";\n        //开启弹出框内输入框编辑权限\n        this.isDisabled = false;\n        //给弹出框内赋值\n        this.form = {...row};\n\n      },\n      //工器具列表新增修改保存\n      async qxcommit() {\n        await this.$refs['form'].validate(async (valid) => {\n          if (valid) {\n            try {\n              let {code} = await saveOrUpdate(this.form)\n              if (code === '0000') {\n                this.$message.success(\"操作成功\")\n              }\n            } catch (e) {\n              console.log(e)\n            }\n            //恢复分页\n            this.tableAndPageInfo.pager.pageResize = 'Y';\n            this.getData();\n            this.dialogFormVisible = false;\n          }})\n      },\n      //删除工器具列表\n      deleteRow(id) {\n        // if (this.selectRows.length < 1) {\n        //   this.$message.warning(\"请选择正确的数据！！！\")\n        //   return\n        // }\n        // let ids = this.selectRows.map(item => {\n        //   return item.objId\n        // });\n        let ids=[];\n        ids.push(id);\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove(ids).then(({code}) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              });\n              this.getData()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              });\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          });\n        });\n        this.tableAndPageInfo.pager.pageResize = 'Y';\n        this.getData()\n      },\n\n      //查看试验\n      handleSearchSYClick(row) {\n        this.sySelectRows = []\n        this.mainRowData = row\n        this.syQueryForm.gqjId = row.objId\n        this.sybgDialogFormVisible = true\n        this.getYxSyData()\n      },\n\n      //查看检修\n      handleSerchJWXClick(row) {\n        this.mainRowData = row\n        this.jxQueryForm.gqjId = row.objId\n        this.jwxDialogFormVisible = true\n        this.getJxRecords()\n      },\n      //添加检修\n      addJxButton() {\n        this.jxForm = this.$options.data().jxForm\n        this.jxForm.gqjId = this.mainRowData.objId\n        this.addJwxSybgDialogFormVisible = true\n      },\n      updateJx(row) {\n        this.jxForm = row\n        this.addJwxSybgDialogFormVisible = true\n      },\n      //添加试验\n      addSyButton() {\n        this.syFrom = this.$options.data().syFrom\n        this.syFrom.gqjId = this.mainRowData.objId\n        this.addSybgDialogFormVisible = true\n      },\n      updateSy(row) {\n        this.syFrom = row\n        this.addSybgDialogFormVisible = true\n      },\n      //每页展示数量点击事件\n      handleSizeChange() {\n\n      },\n      //页码改变事件\n      handleCurrentChange() {\n\n      },\n      //树点击事件\n      handleNodeClick() {\n\n      },\n\n      filterReset() {\n\n      },\n      //选择每一行\n      selectChange(rows) {\n        this.selectRows = rows\n      },\n\n      //获取试验记录数据\n      getYxSyData() {\n        this.syLoading = true\n        getYxSyRecords(this.syQueryForm).then(res => {\n          this.gqjsyList = res.data.records\n          this.syQueryForm.total = res.data.total\n          this.syLoading = false\n        })\n      },\n\n      //新增修改试验记录数据\n      saveOrUpdateSy() {\n        saveOrUpdateYxSyRecords(this.syFrom).then(res => {\n          this.getYxSyData()\n          this.addSybgDialogFormVisible = false\n        })\n      },\n      //批量删除试验数据\n      deleteYxSy() {\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          let ids = []\n          this.sySelectRows.forEach(item => {\n            ids.push(item.id)\n          })\n          deleteYxSyRecords(ids).then(({ code }) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n            }\n            this.getYxSyData()\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n\n      //获取检修记录数据\n      getJxRecords() {\n        this.jxLoading = true\n        getAssetGqjJxRecords(this.jxQueryForm).then(res => {\n          this.gqjJxList = res.data.records\n          this.jxQueryForm.total = res.data.total\n          this.jxLoading = false\n        })\n      },\n      //新增修改检修记录数据\n      saveOrUpdateJx() {\n        saveOrUpdateAssetGqjJxRecords(this.jxForm).then(res => {\n          this.getJxRecords()\n          this.addJwxSybgDialogFormVisible = false\n        })\n      },\n      deleteJxData() {\n\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          let ids = []\n          this.jxSelectRows.forEach(item => {\n            ids.push(item.id)\n          })\n          deleteAssetGqjJxRecords(ids).then(({ code }) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n            }\n            this.getJxRecords()\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n\n      syRowClick(rows) {\n        this.$refs.syTable.toggleRowSelection(rows)\n      },\n      syCurrentChange(val) {\n        this.sySelectRows = val\n      },\n      jxRowClick(rows) {\n        this.$refs.jxTable.toggleRowSelection(rows)\n      },\n      jxCurrentChange(val) {\n        this.jxSelectRows = val\n      }\n    }\n  };\n</script>\n\n<style lang='scss' scoped>\n\n  .button-group {\n    padding-left: 30px;\n    padding-right: 30px;\n    display: flex;\n    justify-content: flex-end;\n  }\n\n  .qxlr_dialog_insert {\n    margin-top: 6vh !important\n  }\n\n  /*/deep/ .qxlr_dialog_insert .el-input--medium .el-input__inner{*/\n  /*  width: 100%;*/\n  /*}*/\n  .el-select {\n    width: 100%;\n  }\n\n  .el-date-editor {\n    width: 100%;\n  }\n</style>\n"]}]}