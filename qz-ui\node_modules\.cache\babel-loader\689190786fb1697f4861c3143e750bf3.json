{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\jszlgl\\jsbz.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\jszlgl\\jsbz.vue", "mtime": 1706897325228}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["jsbz.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+GA;;AACA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,QADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,UAAA,EAAA,KADA;AAEA,MAAA,MAAA,EAAA;AACA,QAAA,IAAA,EAAA,GADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,MAAA,EAAA,EAJA;AAKA,QAAA,QAAA,EAAA,EALA;AAMA,QAAA,UAAA,EAAA;AANA,OAFA;AAUA,MAAA,KAAA,EAAA,EAVA;AAWA;AACA,MAAA,MAAA,EAAA,IAZA;AAaA,MAAA,UAAA,EAAA,EAbA;AAcA,MAAA,GAAA,EAAA,EAdA;AAeA,MAAA,IAAA,EAAA;AACA,QAAA,IAAA,EAAA,GADA;AAEA,QAAA,QAAA,EAAA;AAFA,OAfA;AAmBA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA;AACA,UAAA,IAAA,EAAA,EAFA;AAGA,UAAA,IAAA,EAAA,EAHA;AAIA,UAAA,OAAA,EAAA,EAJA;AAKA,UAAA,OAAA,EAAA,EALA;AAMA,UAAA,MAAA,EAAA,EANA;AAOA,UAAA,QAAA,EAAA,EAPA;AAQA,UAAA,UAAA,EAAA;AARA,SADA;AAWA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,WAAA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,MAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,WAAA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,MAAA,EAAA;AAAA,SAJA,CAKA;AALA;AAXA,OAnBA;AAsCA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,UAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,UAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,CAQA;AARA,SARA;AAkBA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAlBA,OAtCA;AA0DA,MAAA,aAAA,EAAA,IA1DA;AA2DA,MAAA,YAAA,EAAA,EA3DA;AA4DA,MAAA,QAAA,EAAA,KA5DA;AA6DA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,UAAA,EAAA;AAFA,OA9DA;AAkEA;AACA,MAAA,QAAA,EAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAnEA;AAoEA,MAAA,KAAA,EAAA,IApEA;AAqEA;AACA,MAAA,aAAA,EAAA,EAtEA;AAuEA;AACA,MAAA,KAAA,EAAA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,GAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,GAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAVA;AAaA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAbA;AAgBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAhBA;AAmBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAnBA;AAsBA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAtBA;AAxEA,KAAA;AAqGA,GAxGA;AAyGA,EAAA,KAAA,EAAA,EAzGA;AA0GA,EAAA,OA1GA,qBA0GA;AACA,SAAA,UAAA;AACA,GA5GA;AA6GA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,SAFA,qBAEA,EAFA,EAEA;AACA,WAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA,sCAAA,EAAA;AACA,KALA;AAMA,IAAA,cANA,0BAMA,EANA,EAMA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,sBAAA,EAAA,CADA;;AAAA;AAAA;AACA,gBAAA,IADA,qBACA,IADA;;AAAA,sBAEA,IAAA,KAAA,MAFA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAGA,KAAA,CAAA,WAAA,EAHA;;AAAA;AAAA;AAAA,uBAIA,KAAA,CAAA,OAAA,EAJA;;AAAA;AAKA,gBAAA,KAAA,CAAA,QAAA,CAAA;AACA,kBAAA,IAAA,EAAA,SADA;AAEA,kBAAA,OAAA,EAAA;AAFA,iBAAA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,KAhBA;AAiBA,IAAA,WAjBA,yBAiBA;AACA,UAAA,KAAA,KAAA,CAAA,MAAA,EAAA;AACA,aAAA,KAAA,CAAA,MAAA,CAAA,UAAA;AACA;AACA,KArBA;AAsBA,IAAA,WAtBA,yBAsBA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,+BAAA;AAAA,kBAAA,UAAA,EAAA,MAAA,CAAA,IAAA,CAAA;AAAA,iBAAA,CADA;;AAAA;AAAA;AACA,gBAAA,IADA,yBACA,IADA;AACA,gBAAA,IADA,yBACA,IADA;;AAEA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,IAAA,CAAA,QAAA,GAAA,IAAA;AACA;;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,KA3BA;AA4BA;AACA,IAAA,aA7BA,yBA6BA,CA7BA,EA6BA;AACA,UAAA,IAAA,GAAA,CAAA,CAAA,WAAA,EAAA;AACA,UAAA,KAAA,GAAA,CAAA,CAAA,QAAA,KAAA,CAAA,GAAA,OAAA,CAAA,CAAA,QAAA,KAAA,CAAA,CAAA,GAAA,MAAA,CAAA,CAAA,QAAA,KAAA,CAAA,CAAA;AACA,UAAA,GAAA,GAAA,CAAA,CAAA,OAAA,KAAA,EAAA,GAAA,MAAA,CAAA,CAAA,OAAA,EAAA,GAAA,KAAA,CAAA,CAAA,OAAA,EAAA;AACA,aAAA,IAAA,GAAA,GAAA,GAAA,KAAA,GAAA,GAAA,GAAA,GAAA;AACA,KAlCA;AAmCA;AACA,IAAA,OApCA,mBAoCA,MApCA,EAoCA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAAA;AACA,kBAAA,MAAA,CAAA,MAAA,+DAAA,MAAA,CAAA,MAAA,GAAA,MAAA;AACA,kBAAA,KAFA,GAEA,MAAA,CAAA,MAFA;;AAGA,sBAAA,KAAA,CAAA,OAAA,IAAA,KAAA,CAAA,OAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,oBAAA,KAAA,CAAA,SAAA,GAAA,MAAA,CAAA,aAAA,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;AACA,oBAAA,KAAA,CAAA,OAAA,GAAA,MAAA,CAAA,aAAA,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;AACA;;AACA,sBAAA,KAAA,CAAA,OAAA,IAAA,KAAA,CAAA,OAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,oBAAA,KAAA,CAAA,SAAA,GAAA,MAAA,CAAA,aAAA,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;AACA,oBAAA,KAAA,CAAA,OAAA,GAAA,MAAA,CAAA,aAAA,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;AACA;;AACA,yCAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,wBAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,sBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;;AACA,sBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,wBAAA,MAAA,CAAA,aAAA,CAAA,OAAA,CAAA,UAAA,OAAA,EAAA;AACA,8BAAA,IAAA,CAAA,MAAA,KAAA,OAAA,CAAA,KAAA,EAAA;AACA,4BAAA,IAAA,CAAA,UAAA,GAAA,OAAA,CAAA,KAAA;AACA;AACA,yBAJA;AAKA,uBANA;AAOA;AAEA,mBAbA;AAcA,iBAzBA,CAyBA,OAAA,CAAA,EAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,CAAA;AACA;;AA5BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA6BA,KAjEA;AAkEA;AACA,IAAA,OAnEA,qBAmEA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,MAAA,CAAA,KAAA,CAAA,MAAA,EAAA,QAAA;AAAA,mGAAA,kBAAA,KAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA,iCACA,KADA;AAAA;AAAA;AAAA;;AAAA;AAAA,mCAEA,0BAAA,MAAA,CAAA,IAAA,CAFA;;AAAA;AAAA;AAEA,4BAAA,IAFA,uBAEA,IAFA;AAEA,4BAAA,IAFA,uBAEA,IAFA;;AAAA,kCAGA,IAAA,KAAA,MAHA;AAAA;AAAA;AAAA;;AAIA,4BAAA,MAAA,CAAA,UAAA,CAAA,UAAA,GAAA,IAAA,CAAA,KAAA;;AACA,4BAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,MAAA;;AACA,4BAAA,MAAA,CAAA,QAAA,GAAA,KAAA;;AACA,4BAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AAPA;AAAA,mCAQA,MAAA,CAAA,OAAA,EARA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAA;;AAAA;AAAA;AAAA;AAAA,oBADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA,KAhFA;AAiFA;AACA,IAAA,SAlFA,qBAkFA,GAlFA,EAkFA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,WAAA;AACA,WAAA,KAAA,GAAA,QAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,QAAA,GAAA,IAAA;AACA,KAzFA;AA0FA;AACA,IAAA,OA3FA,mBA2FA,GA3FA,EA2FA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,KAAA,GAAA,QAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,QAAA,GAAA,IAAA;AACA,KAjGA;AAkGA;AACA,IAAA,SAnGA,qBAmGA,EAnGA,EAmGA;AAAA;;AACA,UAAA,KAAA,GAAA,EAAA;;AACA,UAAA,sBAAA,EAAA,MAAA,QAAA,EAAA;AACA,YAAA,KAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,eAAA,QAAA,CAAA,OAAA,CAAA,QAAA;AACA;AACA;;AACA,QAAA,KAAA,GAAA,KAAA,GAAA,CAAA,CAAA,CAAA;AACA,OANA,MAMA;AACA,QAAA,KAAA,GAAA,EAAA;AACA;;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,4BAAA,IAAA,CAAA,SAAA,CAAA,KAAA,CAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,cAAA,IAAA,SAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,OAAA;AACA,WANA,MAMA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAIA;AACA,SAbA;AAcA,OAnBA,EAmBA,KAnBA,CAmBA,YAAA,CACA,CApBA;AAqBA,KAnIA;;AAoIA;;;AAGA,IAAA,MAvIA,oBAuIA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,SAAA,CAAA,MAAA;AACA,WAAA,WAAA;AACA,WAAA,IAAA,GAAA,KAAA,IAAA,GAAA;AACA,QAAA,IAAA,EAAA,GADA;AAEA,QAAA,QAAA,EAAA;AAFA,OAAA;AAIA,WAAA,KAAA,GAAA,QAAA,CARA,CASA;;AACA,WAAA,IAAA,CAAA,GAAA,GAAA,KAAA,MAAA,CAAA,OAAA,CAAA,QAAA;AACA,WAAA,QAAA,GAAA,IAAA;AACA,KAnJA;AAoJA,IAAA,WApJA,yBAoJA;AACA,WAAA,MAAA,GAAA;AACA,QAAA,IAAA,EAAA,GADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,MAAA,EAAA,EAJA;AAKA,QAAA,QAAA,EAAA,EALA;AAMA,QAAA,UAAA,EAAA;AANA,OAAA;AAQA,KA7JA;;AA8JA;;;AAGA,IAAA,YAjKA,wBAiKA,IAjKA,EAiKA;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,IAAA,GAAA,OAAA,IAAA,GAAA,EAAA,CADA,CACA;;AACA,UAAA,CAAA,QAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,kBAAA;AACA;;AACA,UAAA,IAAA,GAAA,IAAA,CAAA,IAAA,GAAA,IAAA;AACA,KAvKA;;AAyKA;;;AAGA,IAAA,YA5KA,0BA4KA;AACA,WAAA,UAAA,CAAA,UAAA,GAAA,qBAAA;AACA,WAAA,KAAA,CAAA,MAAA,CAAA,MAAA;AACA,KA/KA;;AAgLA;;;AAGA,IAAA,SAnLA,qBAmLA,QAnLA,EAmLA,IAnLA,EAmLA,QAnLA,EAmLA;AAEA;AACA,WAAA,IAAA,CAAA,YAAA,GAAA,QAAA,CAAA,IAAA,CAAA,UAAA,CAHA,CAIA;;AACA,WAAA,IAAA,CAAA,cAAA,GAAA,QAAA,CAAA,IAAA,CAAA,OAAA,CAAA,WAAA,CALA,CAMA;AACA;AACA;;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,QAAA,CAAA,IAAA,CAAA,OAAA,CAAA,WAAA;AACA,KA7LA;;AA8LA;;;AAGA,IAAA,YAjMA,wBAiMA,IAjMA,EAiMA,QAjMA,EAiMA;AACA,UAAA,OAAA,GAAA,KAAA,YAAA,CAAA,cAAA,CAAA,KAAA,CAAA,GAAA,CAAA;AACA,UAAA,KAAA,GAAA,MAAA,CAAA,KAAA,UAAA,CAAA,CAAA,KAAA,CAAA,GAAA,CAAA;;AACA,UAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,OAAA,CAAA,MAAA,EAAA;AACA,QAAA,UAAA,CAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,UAAA,EAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,OAAA,CAAA,MAAA,CAAA;AACA,aAAA,YAAA,CAAA,cAAA,GAAA,EAAA;AACA,aAAA,UAAA,GAAA,EAAA;;AACA,aAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,OAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,cAAA,IAAA,CAAA,IAAA,KAAA,OAAA,CAAA,CAAA,CAAA,EAAA;AACA,gBAAA,KAAA,YAAA,CAAA,cAAA,EAAA;AACA,mBAAA,YAAA,CAAA,cAAA,GAAA,KAAA,YAAA,CAAA,cAAA,GAAA,GAAA,GAAA,OAAA,CAAA,CAAA,CAAA;AACA,aAFA,MAEA;AACA,mBAAA,YAAA,CAAA,cAAA,GAAA,OAAA,CAAA,CAAA,CAAA;AACA;AACA;AACA;;AACA,aAAA,IAAA,EAAA,GAAA,CAAA,EAAA,EAAA,GAAA,KAAA,CAAA,MAAA,EAAA,EAAA,EAAA,EAAA;AACA,cAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,OAAA,CAAA,MAAA,KAAA,KAAA,CAAA,EAAA,CAAA,EAAA;AACA,gBAAA,KAAA,UAAA,EAAA;AACA,mBAAA,UAAA,GAAA,KAAA,UAAA,GAAA,GAAA,GAAA,KAAA,CAAA,EAAA,CAAA;AACA,aAFA,MAEA;AACA,mBAAA,UAAA,GAAA,KAAA,CAAA,EAAA,CAAA;AACA;AACA;AACA;AACA;AACA,KA3NA;;AA4NA;AACA,IAAA,cA7NA,0BA6NA,EA7NA,EA6NA;AACA,kCAAA,EAAA;AACA,KA/NA;;AAgOA;;;AAGA,IAAA,WAnOA,yBAmOA;AACA,WAAA,SAAA,CAAA,MAAA;AACA,WAAA,IAAA,CAAA,cAAA,GAAA,EAAA;AACA,WAAA,QAAA,GAAA,KAAA;AACA,KAvOA;AAwOA;AACA,IAAA,YAzOA,wBAyOA,IAzOA,EAyOA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,GAAA,GAAA,IAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,KAAA;AAAA,OAAA,CAAA;AACA,WAAA,MAAA,GAAA,IAAA,CAAA,MAAA,KAAA,CAAA;AACA,KA7OA;AA8OA,IAAA,UA9OA,wBA8OA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,sBAAA,IAAA,CAAA,KAAA,KAAA,YAAA,EAAA;AACA,oBAAA,IAAA,CAAA,OAAA,GAAA,MAAA,CAAA,aAAA;AACA;AACA,iBAJA;;AADA;AAAA,uBAOA,MAAA,CAAA,OAAA,EAPA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AAtPA;AA7GA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"4\">\n      <el-col :span=\"24\">\n        <el-filter ref=\"filter1\"\n                   :data=\"filterInfo.data\"\n                   :field-list=\"filterInfo.fieldList\"\n                   @handleReset=\"filterReset\"/>\n      </el-col>\n    </el-row>\n    <el-white class=\"button-group\">\n      <el-row class=\"pull-right button_btn\" :gutter=\"4\">\n        <el-col :span=\"1.5\">\n          <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"addRow\" v-hasPermi=\"['jsbz:records:add']\">新增</el-button>\n          <el-button type=\"danger\" icon=\"el-icon-delete\" :disabled=\"single\" @click=\"deleteRow\" v-hasPermi=\"['jsbz:records:delete']\">删除</el-button>\n        </el-col>\n      </el-row>\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"selectChange\"\n        height=\"65vh\"\n      >\n        <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\"\n                         min-width=\"120\" :resizable=\"false\">\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" size=\"small\" @click=\"downClick(scope.row.objId)\" v-if=\"scope.row.fileList.length>0\" title=\"下载\" class=\"el-icon-download\"></el-button>\n            <el-button type=\"text\" size=\"small\" @click=\"getInfo(scope.row)\" title=\"详情\" class=\"el-icon-view\"></el-button>\n            <el-button type=\"text\" size=\"small\" @click=\"updateRow(scope.row)\" v-if=\"scope.row.createBy === $store.getters.name\" title=\"修改\" class='el-icon-edit'>\n            </el-button>\n            <el-button type=\"text\" size=\"small\" @click=\"deleteRow(scope.row.objId)\" v-if=\"scope.row.createBy === $store.getters.name\" title=\"删除\" class=\"el-icon-delete\"></el-button>\n          </template>\n        </el-table-column>\n      </comp-table>\n      <el-dialog :visible.sync=\"fileOpen\" v-if=\"fileOpen\" :title=\"title\" width=\"50%\" v-dialogDrag>\n        <el-form ref=\"form\" :model=\"form\" label-width=\"150px\" :disabled=\"isDisabled\" :rules=\"rules\">\n          <el-row >\n            <el-col :span=\"12\">\n              <el-form-item label=\"技术标准文件编号:\" prop=\"wjbh\">\n                <el-input placeholder=\"请输入文件编号\" clearable v-model=\"form.wjbh\"></el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"技术标准文件名称:\" prop=\"wjmc\">\n                <el-input type=\"textarea\" :autosize=\"{ minRows: 1, maxRows: 2}\" placeholder=\"选择输入文件名称\" clearable v-model=\"form.wjmc\"></el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"发布人:\" prop=\"fbr\">\n                <el-input placeholder=\"选择发布人\" clearable v-model=\"form.fbr\" suffix-icon=\"el-icon-s-custom\"></el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"执行日期:\" prop=\"zxrq\">\n                <el-date-picker type=\"date\" placeholder=\"选择日期时间\" value-format=\"yyyy-MM-dd\" clearable v-model=\"form.zxrq\"\n                                style=\"width: 100%\"></el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"发布日期:\" prop=\"fbrq\">\n                <el-date-picker type=\"date\" placeholder=\"选择日期时间\" value-format=\"yyyy-MM-dd\" clearable v-model=\"form.fbrq\"\n                                style=\"width: 100%\"></el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"上传时间:\" prop=\"scsj\">\n                <el-date-picker type=\"datetime\" placeholder=\"选择日期时间\" value-format=\"yyyy-MM-dd HH:mm:ss\" clearable\n                                v-model=\"form.scsj\" style=\"width: 100%\"></el-date-picker>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row >\n            <el-col :span=\"20\">\n              <el-form-item label=\"已上传附件:\" prop=\"attachment\" v-if=\"form.fileList.length>0\">\n                <el-col :span=\"24\" v-for=\"(item,index) in form.fileList\">\n                    {{index + 1 }}、\n                    {{item.fileOldName}}\n                    <el-form>\n                      <el-button type=\"primary\" size=\"mini\" @click=\"downloadHandle(item.fileId)\">下载</el-button>\n                      <el-button v-if=\"!isDisabled\" type=\"danger\" size=\"mini\" @click=\"deleteFileById(item.fileId)\">删除</el-button>\n                    </el-form>\n                </el-col>\n              </el-form-item>\n              <el-form-item label=\"附件:\" prop=\"attachmentid\">\n                <template slot-scope=\"scope\">\n                  <el-upload\n                    multiple\n                    class=\"upload-demo\"\n                    accept=\".jpg,.png,.rar,.txt,.zip,.doc,.ppt,.xls,.pdf,.docx,.xlsx,.mp4,.avi,.rmvb\"\n                    ref=\"upload\"\n                    action=\"/isc-api/file/upload\"\n                    :before-upload=\"beforeUpload\"\n                    :data=\"uploadData\"\n                    :headers=\"upHeader\"\n                    :auto-upload=\"false\">\n                    <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选取文件</el-button>\n                  </el-upload>\n                </template>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n        <div slot=\"footer\">\n          <el-button @click=\"uploadClose\">取 消</el-button>\n          <el-button type=\"primary\" v-show=\"!isDisabled\" @click=\"saveRow\">确 定</el-button>\n        </div>\n      </el-dialog>\n    </el-white>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport { getUUID } from '@/utils/ruoyi'\nimport { getListTZ, remove, saveOrUpdate } from '@/api/dagangOilfield/bzgl/bzgfgl/bzgfgl'\nimport {deleteById, downloadByBusinessId, downloadByFileId, getListByBusinessId} from '@/api/tool/file'\nimport { getDictTypeData } from '@/api/system/dict/data'\n\nexport default {\n  name: 'bzgfgl',\n  data() {\n    return {\n      isDisabled: false,\n      params: {\n        wjlx: '3',\n        fbrq: '',\n        zxrq: '',\n        status: '',\n        wjlxList: [],\n        statusList: []\n      },\n      title: '',\n      // 非单个禁用\n      single: true,\n      selectRows: [],\n      ids: [],\n      form: {\n        wjlx: '3',\n        fileList:[]\n      },\n      filterInfo: {\n        data: {\n          // wjlx: '3',\n          wjbh: '',\n          wjmc: '',\n          fbrqArr: '',\n          zxrqArr: '',\n          status: '',\n          wjlxList: [],\n          statusList: []\n        },\n        fieldList: [\n          { label: '文件编号', type: 'input', value: 'wjbh' },\n          { label: '文件名称', type: 'input', value: 'wjmc' },\n          { label: '发布日期', type: 'daterange', value: 'fbrqArr', format: 'yyyy-MM-dd' },\n          { label: '执行日期', type: 'daterange', value: 'zxrqArr', format: 'yyyy-MM-dd' },\n          // { label: '状态', type: 'select', value: 'statusList', multiple: true, options: [] }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: 'wjbh', label: '技术标准文件编号', minWidth: '120' },\n          { prop: 'wjmc', label: '技术标准文件名称', minWidth: '180',showpop:true },\n          { prop: 'fbrq', label: '发布日期', minWidth: '120' },\n          { prop: 'zxrq', label: '执行日期', minWidth: '120' },\n          { prop: 'fbr', label: '发布人', minWidth: '120' },\n          // { prop: 'zxr', label: '执行人', minWidth: '120' },\n          { prop: 'scsj', label: '上传时间', minWidth: '140' },\n          // { prop: 'statusName', label: '状态', minWidth: '120' },\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      HighlightShow: true,\n      defaultProps: {},\n      fileOpen: false,\n      // 文件上传数据\n      uploadData: {\n        type:\"\",\n        businessId: undefined\n      },\n      // 文件上传请求头\n      upHeader: { token: getToken() },\n      value: null,\n      //状态下拉框数据\n      statusOptions: [],\n      //校验规则\n      rules: {\n        wjbh: [\n          { required: true, message: '文件编号不能为空', trigger: 'blur' }\n        ],\n        wjmc: [\n          { required: true, message: '文件名称不能为空', trigger: 'blur' }\n        ],\n        fbr: [\n          { required: true, message: '发布人不能为空', trigger: 'blur' }\n        ],\n        zxr: [\n          { required: true, message: '执行人不能为空', trigger: 'blur' }\n        ],\n        zxrq: [\n          { required: true, message: '执行日期不能为空', trigger: 'change' }\n        ],\n        fbrq: [\n          { required: true, message: '发布日期不能为空', trigger: 'change' }\n        ],\n        scsj: [\n          { required: true, message: '上传时间不能为空', trigger: 'change' }\n        ],\n        status: [\n          { required: true, message: '上传时间不能为空', trigger: 'change' }\n        ]\n\n      }\n\n    }\n  },\n  watch: {},\n  created() {\n    this.initDomain()\n  },\n  methods: {\n    //下载文件\n    downClick(id){\n      this.$message.success(\"开始下载\")\n      downloadByBusinessId(id)\n    },\n    async deleteFileById(id){\n      let {code}=await deleteById(id)\n      if(code==='0000'){\n        await this.getFileList();\n        await this.getData();\n        this.$message({\n          type: 'success',\n          message: '文件删除成功!'\n        });\n      }\n    },\n    clearUpload(){\n      if ( this.$refs.upload){\n        this.$refs.upload.clearFiles()\n      }\n    },\n    async getFileList(){\n      let {code,data}=await  getListByBusinessId({businessId:this.form.objId})\n      if(code==='0000'){\n        this.form.fileList = data;\n      }\n    },\n    //日期格式化  yyyy-MM-dd\n    dateFormatter(d) {\n      let year = d.getFullYear()\n      let month = d.getMonth() < 9 ? '0' + (d.getMonth() + 1) : '' + (d.getMonth() + 1)\n      let day = d.getDate() < 10 ? '0' + d.getDate() : '' + d.getDate()\n      return (year + '-' + month + '-' + day)\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params }\n        const param = this.params\n        if (param.fbrqArr && param.fbrqArr.length > 0) {\n          param.fbrqStart = this.dateFormatter(param.fbrqArr[0])\n          param.fbrqEnd = this.dateFormatter(param.fbrqArr[1])\n        }\n        if (param.zxrqArr && param.zxrqArr.length > 0) {\n          param.zxrqStart = this.dateFormatter(param.zxrqArr[0])\n          param.zxrqEnd = this.dateFormatter(param.zxrqArr[1])\n        }\n        getListTZ(param).then(res => {\n          if (res.code === '0000') {\n            this.tableAndPageInfo.tableData = res.data.records\n            this.tableAndPageInfo.pager.total = res.data.total\n            this.tableAndPageInfo.tableData.forEach(item => {\n              this.statusOptions.forEach(element => {\n                if (item.status === element.value) {\n                  item.statusName = element.label\n                }\n              })\n            })\n          }\n\n        })\n      } catch (e) {\n        console.log(e)\n      }\n    },\n    //新增\n    async saveRow() {\n      await this.$refs['form'].validate(async valid => {\n        if (valid) {\n          let {code, data} = await saveOrUpdate(this.form)\n          if (code === '0000') {\n            this.uploadData.businessId = data.objId\n            this.$refs.upload.submit()\n            this.fileOpen = false\n            this.$message.success('操作成功')\n            await this.getData()\n          }\n        }\n      })\n    },\n    //编辑按钮\n    updateRow(row) {\n      this.isDisabled = false\n      this.clearUpload()\n      this.title = '修改标准规范'\n      this.isDisabled = false\n      this.form = { ...row }\n      this.fileOpen = true\n    },\n    //详情按钮\n    getInfo(row) {\n      this.isDisabled = true\n      this.title = '标准规范详情'\n      this.form = { ...row }\n      this.isDisabled = true\n      this.fileOpen = true\n    },\n    //删除\n    deleteRow(id) {\n      let objId = ''\n      if(typeof id === 'object'){\n        if (this.ids.length < 1) {\n          this.$message.warning('请勾选的数据')\n          return\n        }\n        objId = this.ids[0]\n      }else{\n        objId = id\n      }\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        remove(JSON.stringify(objId)).then(({ code }) => {\n          if (code === '0000') {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            })\n            this.getData()\n          } else {\n            this.$message({\n              type: 'error',\n              message: '删除失败!'\n            })\n          }\n        })\n      }).catch(() => {\n      })\n    },\n    /**\n     * 上传\n     */\n    addRow() {\n      this.isDisabled = false\n      this.resetForm('form')\n      this.clearUpload()\n      this.form= this.form = {\n        wjlx: '3',\n        fileList:[]\n      }\n      this.title = '新增标准规范'\n      //获取当前登录人名称\n      this.form.fbr = this.$store.getters.nickName\n      this.fileOpen = true\n    },\n    filterReset() {\n      this.params = {\n        wjlx: '3',\n        fbrq: '',\n        zxrq: '',\n        status: '',\n        wjlxList: [],\n        statusList: []\n      }\n    },\n    /**\n     * 上传文件之前的处理\n     */\n    beforeUpload(file) {\n      const fileSize = file.size < 1024 * 1024 * 50 //10M\n      if (!fileSize) {\n        this.$message.error('上传文件大小不能超过 50MB!')\n      }\n      let size = file.size / 1024\n    },\n\n    /**\n     * 上传文件到服务器\n     */\n    submitUpload() {\n      this.uploadData.businessId = getUUID()\n      this.$refs.upload.submit()\n    },\n    /**\n     * 上传成功回调函数\n     */\n    onSuccess(response, file, fileList) {\n\n      //文件id\n      this.form.attachmentid = response.data.businessId\n      //文件名称\n      this.form.attachmentname = response.data.sysFile.fileOldName\n      //文件类型\n      //this.form.wjlx = response.data.sysFile.fileType\n      //文件名称\n      this.form.wjmc = response.data.sysFile.fileOldName\n    },\n    /**\n     * 移除文件\n     */\n    handleRemove(file, fileList) {\n      var attName = this.progressInfo.AttachmentName.split(',')\n      var attId = String(this.tempFileId).split(',')\n      if (file.response.data.sysFile.fileId) {\n        deleteFile(file.response.data.businessId, file.response.data.sysFile.fileId)\n        this.progressInfo.AttachmentName = ''\n        this.tempFileId = ''\n        for (let i = 0; i < attName.length; i++) {\n          if (file.name !== attName[i]) {\n            if (this.progressInfo.AttachmentName) {\n              this.progressInfo.AttachmentName = this.progressInfo.AttachmentName + ',' + attName[i]\n            } else {\n              this.progressInfo.AttachmentName = attName[i]\n            }\n          }\n        }\n        for (let i = 0; i < attId.length; i++) {\n          if (file.response.data.sysFile.fileId !== attId[i]) {\n            if (this.tempFileId) {\n              this.tempFileId = this.tempFileId + ',' + attId[i]\n            } else {\n              this.tempFileId = attId[i]\n            }\n          }\n        }\n      }\n    },\n    /**下载附件*/\n    downloadHandle(id) {\n      downloadByFileId(id)\n    },\n    /**\n     * 关闭上传附件对话框\n     */\n    uploadClose() {\n      this.resetForm('form')\n      this.form.attachmentname = ''\n      this.fileOpen = false\n    },\n    //选择行\n    selectChange(rows) {\n      this.selectRows = rows\n      this.ids = rows.map(item => item.objId)\n      this.single = rows.length !== 1;\n    },\n    async initDomain() {\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.value === 'statusList') {\n          item.options = this.statusOptions\n        }\n      })\n\n      await this.getData()\n    }\n  }\n}\n</script>\n\n<style lang='scss' scoped>\n.style-bottom {\n  margin-bottom: 4vh;\n}\n\n.pull-left {\n  margin-left: 2vw !important;\n}\n#pic_form .el-form-item__content{\n  margin-left: 0 !important;\n}\n\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>field/jszlgl"}]}