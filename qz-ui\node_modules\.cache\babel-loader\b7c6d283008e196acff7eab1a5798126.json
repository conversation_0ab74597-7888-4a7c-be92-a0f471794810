{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\sybgjlcomment\\symbSelect.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\sybgjlcomment\\symbSelect.vue", "mtime": 1706897323434}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["symbSelect.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AA+cA;;AACA;;AAUA;;AACA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAhBA;eAkBA;AACA,EAAA,IAAA,EAAA,YADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,MAAA,EAAA,eAAA;AAAA,IAAA,YAAA,EAAA;AAAA,GAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA;AACA,MAAA,cAAA,EAAA,CACA;AACA,oBAAA,MADA;AAEA,oBAAA,MAFA;AAGA,oBAAA,MAHA;AAIA,oBAAA,MAJA;AAKA,oBAAA,EALA;AAMA,oBAAA,EANA;AAOA,oBAAA,EAPA;AAQA,oBAAA;AARA,OADA,EAWA;AACA,oBAAA,MADA;AAEA,oBAAA,KAFA;AAGA,oBAAA,KAHA;AAIA,oBAAA,KAJA;AAKA,oBAAA,EALA;AAMA,oBAAA,EANA;AAOA,oBAAA,EAPA;AAQA,oBAAA;AARA,OAXA,EAqBA;AACA,oBAAA,MADA;AAEA,oBAAA,SAFA;AAGA,oBAAA,WAHA;AAIA,oBAAA,MAJA;AAKA,oBAAA,EALA;AAMA,oBAAA,EANA;AAOA,oBAAA,EAPA;AAQA,oBAAA;AARA,OArBA,CAFA;AAkCA;AACA,MAAA,cAAA,EAAA,CAAA;AACA,oBAAA,MADA;AAEA,oBAAA,MAFA;AAGA,oBAAA,EAHA;AAIA,oBAAA,EAJA;AAKA,oBAAA,EALA;AAMA,oBAAA;AANA,OAAA,CAnCA;AA2CA;AACA,MAAA,GAAA,EAAA,CAAA;AACA,QAAA,KAAA,EAAA,EADA;AACA;AACA,QAAA,OAAA,EAAA,EAFA;AAEA;AACA,QAAA,MAAA,EAAA,EAHA,CAGA;;AAHA,OAAA,CA5CA;AAiDA;AACA,MAAA,oBAAA,EAAA,KAlDA;AAmDA,MAAA,QAAA,EAAA;AACA,QAAA,EAAA,EAAA,WADA;AACA;AACA,QAAA,QAAA,EAAA,QAFA;AAEA;AACA,QAAA,YAAA,EAAA,QAHA;AAIA,QAAA,OAAA,EAAA;AAJA,OAnDA;AAyDA,MAAA,MAAA,EAAA,EAzDA;AA0DA;AACA,MAAA,SAAA,EAAA,EA3DA;AA4DA;AACA,MAAA,SAAA,EAAA,EA7DA;AA8DA;AACA,MAAA,kBAAA,EAAA,KA/DA;AAgEA,MAAA,cAAA,EAAA;AACA;AACA,QAAA,MAAA,EAAA,SAFA;AAGA;AACA,QAAA,UAAA,EAAA;AAJA,OAhEA;AAsEA;AACA,MAAA,uBAAA,EAAA,KAvEA;AAwEA;AACA,MAAA,mBAAA,EAAA,KAzEA;AA0EA;AACA,MAAA,kBAAA,EAAA;AACA,QAAA,MAAA,EAAA,SADA;AAEA,QAAA,MAAA,EAAA,EAFA;AAGA,QAAA,OAAA,EAAA,CAHA;AAIA,QAAA,QAAA,EAAA;AAJA,OA3EA;AAiFA;AACA,MAAA,iBAAA,EAAA,EAlFA;AAmFA;AACA,MAAA,cAAA,EAAA,CApFA;AAqFA;AACA,MAAA,SAAA,EAAA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AADA,OAtFA;AA2FA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,IAAA,EAAA;AADA,SADA;AAIA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA;AAJA,OA5FA;AAoGA;AACA,MAAA,WAAA,EAAA,IArGA;AAsGA;AACA,MAAA,KAAA,EAAA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,QAAA,EAAA,OAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAvGA;AA4GA;AACA,MAAA,UAAA,EAAA,EA7GA;AA8GA;AACA,MAAA,aAAA,EAAA,EA/GA;AAgHA;AACA,MAAA,IAAA,EAAA,EAjHA;AAkHA;AACA,MAAA,cAAA,EAAA;AACA,QAAA,MAAA,EAAA,SADA;AAEA,QAAA,IAAA,EAAA,SAFA;AAGA,QAAA,OAAA,EAAA,CAHA;AAIA,QAAA,QAAA,EAAA;AAJA,OAnHA;AAyHA;AACA,MAAA,QAAA,EAAA,EA1HA;AA2HA;AACA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,CARA;AAaA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAbA,OA5HA;AA2IA;AACA,MAAA,WAAA,EAAA,EA5IA;AA8IA,MAAA,aAAA,EAAA,KA9IA;AA+IA,MAAA,KAAA,EAAA,EA/IA;AAiJA;AACA,MAAA,cAAA,EAAA,IAlJA;AAmJA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,EAAA,EAAA,SADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,QAAA,EAAA;AAHA,OApJA;AAyJA,MAAA,UAAA,EAAA,KAzJA;AA0JA,MAAA,MAAA,EAAA,IA1JA;AA4JA;AACA,MAAA,eAAA,EAAA,MA7JA;AA8JA;AACA,MAAA,gBAAA,EAAA,KA/JA;AAiKA;AACA,MAAA,SAAA,EAAA,CAlKA;AAmKA;AACA,MAAA,eAAA,EAAA;AACA,QAAA,MAAA,EAAA,SADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,QAAA,EAAA;AAHA,OApKA;AA0KA;AACA,MAAA,UAAA,EAAA,CA3KA;AA4KA;AACA,MAAA,gBAAA,EAAA;AACA,QAAA,MAAA,EAAA,SADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,OAAA,EAAA;AAHA,OA7KA;AAmLA;AACA,MAAA,cAAA,EAAA,EApLA;AAqLA;AACA,MAAA,eAAA,EAAA,EAtLA;AAuLA;AACA,MAAA,qBAAA,EAAA,EAxLA;AAyLA;AACA,MAAA,YAAA,EAAA,KA1LA;AA2LA;AACA,MAAA,OAAA,EAAA,EA5LA;AA8LA;AACA,MAAA,MAAA,EAAA,EA/LA;AAiMA;AACA,MAAA,YAAA,EAAA,EAlMA;AAmMA;AACA,MAAA,UAAA,EAAA,CAAA,MAAA,EAAA,IAAA,EAAA,IAAA;AApMA,KAAA;AAsMA,GA1MA;AA2MA,EAAA,KAAA,EAAA,EA3MA;AA4MA,EAAA,OA5MA,qBA4MA;AACA;AACA,SAAA,OAAA;AACA,GA/MA;AAgNA,EAAA,OAhNA,qBAgNA,CACA,CAjNA;AAkNA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,eAFA,iCAEA;AAAA,UAAA,GAAA,QAAA,GAAA;AAAA,UAAA,MAAA,QAAA,MAAA;AAAA,UAAA,QAAA,QAAA,QAAA;AAAA,UAAA,WAAA,QAAA,WAAA;;AACA,UAAA,KAAA,UAAA,CAAA,QAAA,CAAA,GAAA,CAAA,IAAA,CAAA,EAAA;AACA,YAAA,WAAA,GAAA,CAAA,EAAA;AACA,iBAAA,CAAA,CAAA,EAAA,GAAA,CAAA,QAAA,CAAA;AACA;AACA;AACA,KARA;AASA;AACA,IAAA,cAVA,iCAUA;AAAA,UAAA,GAAA,SAAA,GAAA;AAAA,UAAA,MAAA,SAAA,MAAA;AAAA,UAAA,QAAA,SAAA,QAAA;AAAA,UAAA,WAAA,SAAA,WAAA;;AACA,UAAA,WAAA,GAAA,CAAA,EAAA;AACA,eAAA,CAAA,CAAA,EAAA,CAAA,CAAA;AACA;AACA,KAdA;AAeA;AACA,IAAA,YAhBA,wBAgBA,GAhBA,EAgBA;AACA;AACA,WAAA,iBAAA,CAAA,GAAA,EAFA,CAGA;;AACA,WAAA,cAAA,CAAA,GAAA;AACA,KArBA;AAsBA;AACA,IAAA,WAvBA,yBAuBA;AACA,yBAAA,WAAA,CAAA,QAAA,CAAA,aAAA,CAAA,iBAAA,CAAA,EAAA,KAAA,MAAA,CAAA,IAAA;AACA,KAzBA;AA0BA;AACA,IAAA,iBA3BA,6BA2BA,KA3BA,EA2BA;AAAA;;AACA,qCAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,KAAA,CAAA,MAAA,GAAA,GAAA,CAAA,IAAA,CADA,CAEA;AACA;AACA;;AACA,QAAA,KAAA,CAAA,oBAAA,GAAA,IAAA;AACA,OANA;AAQA,KApCA;AAqCA;AACA,IAAA,cAtCA,0BAsCA,OAtCA,EAsCA;AAAA;;AACA;AACA,WAAA,YAAA,GAAA,EAAA;AACA,kCAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,MAAA,GAAA,GAAA,CAAA,IAAA,CADA,CAEA;;AACA,aAAA,IAAA,GAAA,IAAA,MAAA,EAAA;AACA;AACA,UAAA,MAAA,CAAA,cAAA,CAAA,GAAA,EAAA,MAAA,CAAA,GAAA,CAAA;AACA,SANA,CAOA;;;AACA,QAAA,MAAA,CAAA,mBAAA;AACA,OATA;AAUA,KAnDA;AAoDA;AACA,IAAA,cArDA,0BAqDA,MArDA,EAqDA,YArDA,EAqDA;AACA,UAAA,QAAA,GAAA,EAAA;AACA,MAAA,QAAA,CAAA,MAAA,GAAA,MAAA;;AACA,WAAA,IAAA,GAAA,IAAA,YAAA,CAAA,CAAA,CAAA,EAAA;AACA,QAAA,QAAA,CAAA,GAAA,CAAA,GAAA,YAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA;AACA;;AACA,WAAA,YAAA,CAAA,IAAA,CAAA,QAAA;AACA,KA5DA;AA6DA;AACA,IAAA,mBA9DA,iCA8DA;AAAA;;AACA,WAAA,GAAA,GAAA,EAAA,CADA,CAEA;AACA;;AACA,UAAA,IAAA,GAAA,KAAA,YAAA;;AACA,UAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AAAA,mCACA,CADA;AAEA,cAAA,SAAA,GAAA,IAAA,CAAA,CAAA,CAAA;AACA,cAAA,KAAA,GAAA,SAAA,CAAA,MAAA,CAHA,CAGA;;AACA,cAAA,MAAA,GAAA,SAAA,CAAA,MAAA,CAJA,CAIA;;AACA,cAAA,OAAA,GAAA,SAAA,CAAA,OAAA,CALA,CAKA;;AACA,cAAA,EAAA,GAAA,CACA;AACA,qBAAA,KADA;AACA;AACA,2BAAA,MAFA,CAEA;;AAFA,WADA,CAAA;AAMA,UAAA,OAAA,CAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,EAAA,CAAA,IAAA,CAAA;AACA,uBAAA,GAAA,CAAA,OADA;AACA;AACA,6BAAA,EAFA,CAEA;;AAFA,aAAA;AAIA,WALA;AAMA,cAAA,EAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,OAAA,CAAA,UAAA,EAAA,EAAA;AACA,YAAA,EAAA,CAAA,IAAA,CAAA;AACA,sBAAA,EAAA,CAAA,IADA;AAEA,0BAAA,OAAA,CAAA,MAFA,CAEA;;AAFA,aAAA;AAIA,WALA,EAnBA,CAyBA;;AACA,UAAA,EAAA,CAAA,IAAA,CACA;AACA,oBAAA,IADA;AAEA,wBAAA,OAAA,CAAA,MAFA,CAEA;;AAFA,WADA,EAKA;AACA,oBAAA,MADA;AAEA,wBAAA,OAAA,CAAA,MAFA,CAEA;;AAFA,WALA,EASA;AACA,oBAAA,IADA;AAEA,wBAAA,OAAA,CAAA,MAFA,CAEA;;AAFA,WATA,EAaA;AACA,oBAAA,IADA;AAEA,wBAAA,OAAA,CAAA,MAFA,CAEA;;AAFA,WAbA;;AAkBA,UAAA,MAAA,CAAA,GAAA,CAAA,IAAA,CAAA;AACA,YAAA,KAAA,EAAA,KADA;AAEA,YAAA,OAAA,EAAA,EAFA;AAGA,YAAA,MAAA,EAAA;AAHA,WAAA;AA5CA;;AACA,aAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AAAA,gBAAA,CAAA;AAgDA;AACA,OAvDA,CAwDA;;AACA;;;;;;;;;;;;;;;AAeA,KAtIA;AAuIA;AACA,IAAA,WAxIA,uBAwIA,MAxIA,EAwIA,GAxIA,EAwIA;AACA;AACA,2BAAA,cAAA,EAAA,IAAA,CAAA,EAAA,EAFA,CAGA;;AACA,WAAA,MAAA,GAAA,EAAA;AACA,WAAA,MAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CALA,CAMA;;AACA,UAAA,GAAA,GAAA,EAAA,CAPA,CAQA;;AACA,UAAA,MAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,YAAA,MAAA,CAAA,CAAA,CAAA,CAAA,IAAA,IAAA,GAAA,EAAA;AAAA;AACA;AACA,UAAA,GAAA,IAAA,2DACA,mDADA,GAEA,mDAFA,GAGA,mDAHA,GAIA,OAJA,CAFA,CAOA;;AACA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,MAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,YAAA,GAAA,IAAA,MAAA;AACA,YAAA,GAAA,IAAA,6CAAA;AACA,YAAA,GAAA,IAAA,MAAA,CAAA,CAAA,CAAA,CAAA,KAAA,GAAA,OAAA;AACA,YAAA,GAAA,IAAA,YAAA;AACA,YAAA,GAAA,IAAA,YAAA;AACA,YAAA,GAAA,IAAA,YAAA;AACA,YAAA,GAAA,IAAA,OAAA;AACA;AACA,SAjBA,MAiBA;AAAA;AACA;AACA;AACA,cAAA,GAAA,GAAA,CAAA,CAHA,CAIA;;AACA,cAAA,KAAA,GAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAAA,MAAA,GAAA,GAAA,CAAA,CALA,CAMA;;AACA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,EAAA,CAAA,EAAA,EAAA;AACA,YAAA,GAAA,IAAA,MAAA,CADA,CAEA;;AACA,iBAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,GAAA,EAAA,CAAA,EAAA,EAAA;AACA,kBAAA,CAAA,GAAA,GAAA,GAAA,CAAA,GAAA,MAAA,CAAA,MAAA,EAAA;AACA,gBAAA,GAAA,IAAA,6CAAA,CADA,CAEA;;AACA,gBAAA,GAAA,IAAA,MAAA,CAAA,CAAA,GAAA,GAAA,GAAA,CAAA,CAAA,CAAA,KAAA,GAAA,OAAA,CAHA,CAIA;;AACA,gBAAA,GAAA,IAAA,MAAA,CAAA,CAAA,GAAA,GAAA,GAAA,CAAA,CAAA,CAAA,IAAA,IAAA,CAAA,GAAA,4DAAA,GAAA,SAAA,MAAA,CAAA,CAAA,GAAA,GAAA,GAAA,CAAA,CAAA,CAAA,WAAA,GAAA,OAAA;AACA;AACA;;AACA,YAAA,GAAA,IAAA,OAAA;AACA;AACA;AACA,OAjDA,CAkDA;;;AACA,WAAA,SAAA,CAAA,YAAA;AACA,6BAAA,cAAA,EAAA,MAAA,CAAA,GAAA;AACA,OAFA,EAnDA,CAsDA;;AACA,WAAA,oBAAA,GAAA,IAAA;AACA,KAhMA;AAiMA;AACA,IAAA,aAlMA,2BAkMA;AACA;AACA,WAAA,MAAA,GAAA,EAAA,CAFA,CAGA;;AACA,WAAA,oBAAA,GAAA,KAAA;AACA,KAvMA;AAyMA;AACA,IAAA,eA1MA,2BA0MA,GA1MA,EA0MA;AACA;AACA,WAAA,kBAAA,GAAA,IAAA,CAFA,CAGA;;AACA,WAAA,SAAA,GAAA,GAAA;AACA,KA/MA;AAiNA;AACA,IAAA,eAlNA,6BAkNA;AAAA;;AACA,mCAAA,KAAA,kBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,iBAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,MAAA,CAAA,cAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,OAHA;AAIA,KAvNA;AAyNA;AACA,IAAA,SA1NA,uBA0NA;AACA,WAAA,eAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,KA7NA;AA+NA;AACA,IAAA,iBAhOA,+BAgOA;AACA,WAAA,mBAAA,GAAA,KAAA;AACA,KAlOA;AAoOA;AACA,IAAA,gBArOA,8BAqOA;AAAA;;AACA,UAAA,KAAA,cAAA,CAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,aAAA,EADA,CAEA;;AACA,aAAA,mBAAA,GAAA,KAAA;AACA,OAJA,MAIA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,KAAA,cAAA,EADA,CAEA;;AACA,0CAAA,KAAA,cAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA,WAFA,MAEA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;AACA,WALA,CAMA;;;AACA,UAAA,MAAA,CAAA,mBAAA,GAAA,KAAA,CAPA,CAQA;;AACA,UAAA,MAAA,CAAA,2BAAA;AACA,SAVA;AAWA;AACA,KAzPA;AA2PA;AACA,IAAA,6BA5PA,yCA4PA,IA5PA,EA4PA;AACA,WAAA,cAAA,CAAA,UAAA,GAAA,IAAA;AACA,KA9PA;AAgQA;AACA,IAAA,eAjQA,6BAiQA;AACA,WAAA,eAAA;AACA,KAnQA;AAqQA;AACA,IAAA,aAtQA,2BAsQA;AACA,WAAA,kBAAA,CAAA,MAAA,GAAA,EAAA;AACA,WAAA,eAAA;AACA,KAzQA;AA2QA;AACA,IAAA,cA5QA,4BA4QA;AAAA;;AACA,0CAAA,KAAA,gBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,MAAA,CAAA,eAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,OAHA;AAIA,KAjRA;AAoRA;AACA,IAAA,eArRA,2BAqRA,GArRA,EAqRA;AACA;AACA,WAAA,eAAA,GAAA,EAAA,CAFA,CAGA;;AACA,WAAA,gBAAA,GAAA,IAAA,CAJA,CAKA;;AACA,WAAA,eAAA,CAAA,MAAA,GAAA,GAAA,CAAA,KAAA,CANA,CAOA;;AACA,WAAA,kBAAA,CAAA,MAAA,GAAA,GAAA,CAAA,KAAA,CARA,CASA;;AACA,WAAA,cAAA,CAAA,MAAA,GAAA,GAAA,CAAA,KAAA,CAVA,CAWA;;AACA,WAAA,2BAAA;AACA,KAlSA;AAoSA;AACA,IAAA,2BArSA,yCAqSA;AAAA;;AACA,+CAAA,KAAA,eAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,cAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,MAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,OAHA;AAIA,KA1SA;AA4SA;AACA,IAAA,wBA7SA,oCA6SA,IA7SA,EA6SA;AACA,WAAA,qBAAA,GAAA,IAAA;AACA,KA/SA;AAiTA;AACA,IAAA,YAlTA,0BAkTA;AAAA;;AACA,UAAA,KAAA,qBAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,aAAA;AACA;AACA;;AACA,UAAA,GAAA,GAAA,KAAA,qBAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA;AACA,OAFA,CAAA;AAGA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,4BAAA,GAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,cAAA,IAAA,SAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,2BAAA;AACA,WANA,MAMA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,2BAAA;AACA;AACA,SAdA;AAeA,OApBA,EAoBA,KApBA,CAoBA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OAzBA;AA0BA,KApVA;AAqVA;AACA,IAAA,oBAtVA,gCAsVA,GAtVA,EAsVA;AACA,WAAA,gBAAA,CAAA,MAAA,GAAA,GAAA,CAAA,MAAA;AACA,WAAA,cAAA;AACA,KAzVA;AA0VA;AACA,IAAA,QA3VA,oBA2VA,IA3VA,EA2VA,OA3VA,EA2VA;AAAA;;AACA,UAAA,YAAA,GAAA;AACA,QAAA,GAAA,EAAA,EADA;AAEA,QAAA,OAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,MAAA;AAFA,OAAA;;AAIA,UAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA;AACA,QAAA,YAAA,CAAA,GAAA,GAAA,IAAA;AACA,eAAA,KAAA,WAAA,CAAA,YAAA,EAAA,OAAA,CAAA;AACA;;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,YAAA,CAAA,GAAA,GAAA,IAAA,CAAA,IAAA,CAAA,IAAA;;AACA,QAAA,MAAA,CAAA,WAAA,CAAA,YAAA,EAAA,OAAA;AACA,OAHA,EAGA,GAHA,CAAA;AAKA,KAzWA;AA0WA;AACA,IAAA,WA3WA,uBA2WA,QA3WA,EA2WA,OA3WA,EA2WA;AACA,+CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,SAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,GAAA;AACA,YAAA,IAAA,EAAA,IAAA,CAAA,IADA;AAEA,YAAA,KAAA,EAAA,IAAA,CAAA,KAFA;AAGA,YAAA,EAAA,EAAA,IAAA,CAAA,EAHA;AAIA,YAAA,GAAA,EAAA,IAAA,CAAA,GAJA;AAKA,YAAA,IAAA,EAAA,KALA;AAMA,YAAA,IAAA,EAAA,IAAA,CAAA;AANA,WAAA;AAQA,UAAA,SAAA,CAAA,IAAA,CAAA,IAAA;AACA,SAVA;AAWA,QAAA,OAAA,CAAA,SAAA,CAAA;AACA,OAdA;AAeA,KA3XA;AA4XA;AACA,IAAA,IA7XA,kBA6XA;AAAA;;AACA,WAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,oCAAA,OAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,cAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,GAAA,CAAA,GAAA;;AACA,cAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,cAAA,OAAA,CAAA,OAAA;;AACA,cAAA,OAAA,CAAA,aAAA,GAAA,KAAA;AACA,aALA,MAKA;AACA,cAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA;AACA;AACA,WATA;AAWA;AACA,OAdA;AAeA,KA7YA;AA8YA;AACA,IAAA,eA/YA,2BA+YA,IA/YA,EA+YA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,OAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA;;AACA,UAAA,IAAA,CAAA,KAAA,IAAA,GAAA,IAAA,IAAA,CAAA,KAAA,IAAA,GAAA,EAAA;AACA;AACA,aAAA,WAAA,GAAA,KAAA;AACA,aAAA,QAAA,GAAA,IAAA;AACA,aAAA,cAAA,CAAA,MAAA,GAAA,IAAA,CAAA,IAAA;AACA,aAAA,OAAA;AACA,OANA,MAMA;AACA,aAAA,WAAA,GAAA,IAAA;AACA;AACA,KA3ZA;AA4ZA;AACA,IAAA,eA7ZA,6BA6ZA;AACA,WAAA,IAAA,GAAA,EAAA;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,KAAA,QAAA,CAAA,IAAA;AACA,WAAA,IAAA,CAAA,MAAA,GAAA,KAAA,QAAA,CAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,KAAA,GAAA,IAAA;AACA,KAnaA;AAoaA;AACA,IAAA,OAraA,mBAqaA,MAraA,EAqaA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,KAFA,+DAEA,OAAA,CAAA,cAFA,GAEA,MAFA;AAAA;AAAA,uBAGA,mCAAA,KAAA,CAHA;;AAAA;AAAA;AAGA,gBAAA,IAHA,yBAGA,IAHA;AAGA,gBAAA,IAHA,yBAGA,IAHA;;AAIA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AAPA;AAAA;;AAAA;AAAA;AAAA;AASA,gBAAA,OAAA,CAAA,GAAA;;AATA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KAhbA;;AAkbA;;;AAGA,IAAA,qBArbA,iCAqbA,IArbA,EAqbA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KAvbA;AAybA,IAAA,KAzbA,mBAybA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,KA3bA;AA6bA;AACA,IAAA,aA9bA,yBA8bA,GA9bA,EA8bA;AACA,WAAA,KAAA,GAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,IAAA,GAAA,GAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,MAAA,GAAA,IAAA;AACA,KApcA;AAucA,IAAA,cAvcA,0BAucA,GAvcA,EAucA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,KAzcA;AA2cA;AACA,IAAA,UA5cA,sBA4cA,GA5cA,EA4cA;AACA,WAAA,KAAA,GAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,IAAA,GAAA,GAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,MAAA,GAAA,KAAA;AACA,KAldA;AAqdA;AACA,IAAA,kBAtdA,gCAsdA;AAAA;;AACA,UAAA,KAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,aAAA;AACA;AACA;;AACA,UAAA,GAAA,GAAA,KAAA,UAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA;AACA,OAFA,CAAA;AAGA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,4BAAA,GAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,cAAA,IAAA,SAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,YAAA,OAAA,CAAA,OAAA;AACA,WAPA,MAOA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,YAAA,OAAA,CAAA,OAAA;AACA;AACA,SAhBA;AAiBA,OAtBA,EAsBA,KAtBA,CAsBA,YAAA;AACA,QAAA,OAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OA3BA;AA6BA,KA3fA;AA6fA;AACA,IAAA,YA9fA,0BA8fA,CAEA,CAhgBA;AAmgBA;AACA,IAAA,eApgBA,2BAogBA,GApgBA,EAogBA;AACA,WAAA,YAAA,GAAA,IAAA;AACA,WAAA,OAAA,GAAA,GAAA;AACA,KAvgBA;AAygBA;AACA,IAAA,aA1gBA,2BA0gBA;AACA,WAAA,YAAA,GAAA,KAAA;AACA,KA5gBA;AA+gBA,IAAA,WA/gBA,yBA+gBA,CACA,CAhhBA;AAkhBA;AACA,IAAA,gBAnhBA,8BAmhBA;AACA,WAAA,KAAA,CAAA,uBAAA,EAAA,KAAA;AACA,KArhBA;AAshBA;AACA;AACA,IAAA,YAxhBA,0BAwhBA;AACA,UAAA,KAAA,UAAA,CAAA,MAAA,IAAA,CAAA,IAAA,KAAA,UAAA,IAAA,SAAA,EAAA;AACA,aAAA,KAAA,CAAA,oBAAA,EAAA,KAAA,UAAA,CAAA,CAAA,CAAA;AACA,aAAA,KAAA,CAAA,uBAAA,EAAA,KAAA;AACA,OAHA,MAGA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,SAAA;AACA;AACA;AA/hBA;AAlNA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row>\n      <el-col :span=\"4\">\n        <el-card class=\"box-card aside_height\" shadow=\"never\">\n          <div slot=\"header\" class=\"clearfix\" style=\"text-align: center\">\n            <span>设备类型</span>\n          </div>\n          <div style=\" overflow: auto;height: 60vh\">\n            <el-col>\n              <el-tree id=\"tree\"\n                       :props=\"props\"\n                       :load=\"loadNode\"\n                       lazy\n                       :default-expanded-keys=\"['1']\"\n                       @node-expand=\"handleNodeClick\"\n                       @node-click=\"handleNodeClick\"/>\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 140, itemWidth: 190 }\"\n          @handleReset=\"filterReset\"\n        />\n        <el-white>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            :is-single=\"true\"\n            height=\"61.5vh\">\n\n            <!--            <el-table-column slot=\"table_six\" align=\"center\" label=\"关联铭牌\" min-width=\"150\" :resizable=\"false\">-->\n            <!--              <template slot-scope=\"scope\">-->\n            <!--                <el-button @click=\"handleClickGlMp(scope.row)\" type=\"text\" size=\"small\" v-if=\"scope.row.sfty == '是'\">-->\n            <!--                  查看铭牌-->\n            <!--                </el-button>-->\n            <!--                <el-button @click=\"handleClickGlMp(scope.row)\" type=\"text\" size=\"small\" v-else>关联铭牌</el-button>-->\n            <!--              </template>-->\n            <!--            </el-table-column>-->\n\n            <!--            <el-table-column slot=\"table_seven\" align=\"center\" label=\"关联项目\" min-width=\"150\" :resizable=\"false\">-->\n            <!--              <template slot-scope=\"scope\">-->\n            <!--                <el-button @click=\"handleClickGlxm(scope.row)\" type=\"text\" size=\"small\" v-if=\"scope.row.sfty == '是'\">-->\n            <!--                  查看项目-->\n            <!--                </el-button>-->\n            <!--                <el-button @click=\"handleClickGlxm(scope.row)\" type=\"text\" size=\"small\" v-else>关联项目</el-button>-->\n            <!--              </template>-->\n            <!--            </el-table-column>-->\n\n            <!--            <el-table-column slot=\"table_eight\" align=\"center\" label=\"定义模板内容\" min-width=\"150\" :resizable=\"false\">-->\n            <!--              <template slot-scope=\"scope\">-->\n            <!--                <el-button @click=\"handleClickMbnr(scope.row)\" type=\"text\" size=\"small\" v-if=\"scope.row.sfty == '是'\">-->\n            <!--                  查看模板内容-->\n            <!--                </el-button>-->\n            <!--                <el-button @click=\"handleClickMbnr(scope.row)\" type=\"text\" size=\"small\" v-else>定义模板内容</el-button>-->\n            <!--              </template>-->\n            <!--            </el-table-column>-->\n\n            <el-table-column slot=\"table_eight\" align=\"center\" label=\"模板详情\" min-width=\"150\" :resizable=\"false\">\n              <template slot-scope=\"scope\">\n                <el-button v-print=\"printObj\" type=\"text\" size=\"small\" @click=\"handleMbInfo(scope.row)\">模板详情</el-button>\n              </template>\n            </el-table-column>\n\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n    <el-row>\n      <div style=\"text-align: right;margin-top: 2vh\">\n        <el-button @click=\"closeSymbComment\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"commitMbdata\">确 定</el-button>\n      </div>\n    </el-row>\n    <!--新增\\修改\\详情弹出-->\n    <el-dialog :title=title :visible.sync=\"isShowDetails\" width=\"60%\" v-dialogDrag>\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"mbzbRules\">\n        <el-row :gutter=\"8\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"设备类型：\" prop=\"sblx\">\n              <el-input v-model=\"form.sblx\" disabled style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"设备类型编码：\" prop=\"sblxbm\">\n              <el-input v-model=\"form.sblxid\" disabled style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"模板名称：\" prop=\"mbmc\">\n              <el-input placeholder=\"请输入试验部位名称\" v-model=\"form.mbmc\" :disabled=\"isDisabled\"\n                        style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否默认：\" prop=\"sfmr\">\n              <el-select v-model=\"form.sfmr\" style=\"width: 100%\" :disabled=\"isDisabled\">\n                <el-option label=\"是\" value=\"是\"></el-option>\n                <el-option label=\"否\" value=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否停用：\" prop=\"sfty\">\n              <el-select v-model=\"form.sfty\" style=\"width: 100%\" :disabled=\"isDisabled\">\n                <el-option label=\"是\" value=\"是\"></el-option>\n                <el-option label=\"否\" value=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" v-show=\"isShow\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"save\">确 认</el-button>\n      </div>\n    </el-dialog>\n\n    <!--关联铭牌弹框-->\n    <el-dialog :visible.sync=\"showMpDialog\" title=\"已关联铭牌\" v-if=\"showMpDialog\" v-dialogDrag>\n      <glsymp :main-data=\"rowData\" :tree-data=\"treeForm\" @closeMpDialog=\"closeMpDialog\"></glsymp>\n    </el-dialog>\n\n    <!--关联试验项目弹出框-->\n    <el-dialog :title=\"glxmDialogTitle\" :visible.sync=\"isGlxmDialogShow\" width=\"60%\" v-dialogDrag>\n      <el-row :gutter=\"3\">\n        <div class=\"mb8 pull-right\">\n          <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"mini\" @click=\"addMbGlXm\">新增项目</el-button>\n          <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"mini\" @click=\"deleteMbGlXm\">删除项目</el-button>\n        </div>\n      </el-row>\n      <el-row :gutter=\"20\">\n        <el-col :span=\"12\">\n          <el-table :data=\"mbGlxmDataList\" @selection-change=\"handleGlxmSelectedChange\"\n                    @row-click=\"handleMbGlxmRowClick\">\n            <el-table-column label=\"试验项目\" align=\"center\">\n              <el-table-column type=\"selection\" width=\"55\" align=\"center\"/>\n              <el-table-column label=\"序号\" type=\"index\" width=\"50\" align=\"center\"></el-table-column>\n              <el-table-column prop=\"syxmmc\" label=\"项目名称\" width=\"180\" align=\"center\"></el-table-column>\n              <el-table-column prop=\"syxmms\" label=\"项目描述\" align=\"center\"></el-table-column>\n            </el-table-column>\n          </el-table>\n          <pagination\n            :total=\"glxmTotal\"\n            :page.sync=\"glxmQueryParams.pageNum\"\n            :limit.sync=\"glxmQueryParams.pageSize\"\n            @pagination=\"getSymbGlsyxmDataListByPage\"\n          />\n        </el-col>\n        <el-col :span=\"12\">\n          <el-table :data=\"zxmGlmbDataList\">\n            <el-table-column label=\"试验子项目\" align=\"center\">\n              <el-table-column prop=\"syzxmmc\" label=\"子项目名称\" width=\"180\" align=\"center\"></el-table-column>\n              <el-table-column prop=\"syzxmms\" label=\"子项目描述\" align=\"center\"></el-table-column>\n            </el-table-column>\n          </el-table>\n          <pagination\n            :total=\"glzxmTotal\"\n            :page.sync=\"glzxmQueryParams.pageNum\"\n            :limit.sync=\"glzxmQueryParams.pageSize\"\n            @pagination=\"getZxmDataList\"\n          />\n        </el-col>\n      </el-row>\n\n    </el-dialog>\n    <!--列表新增关联项目弹窗调用-->\n    <el-dialog :title=\"xmLibraryAddDialogTitle\" :visible.sync=\"isShowAddGlxmDialog\" width=\"50%\" v-dialogDrag>\n      <el-form label-width=\"120px\">\n        <el-row :gutter=\"3\">\n          <el-col :span=\"10\">\n            <el-form-item label=\"项目名称：\">\n              <el-input v-model=\"xmLibraryQueryForm.syxmmc\"/>\n            </el-form-item>\n          </el-col>\n          <div class=\"mb8 pull-right\">\n            <el-button type=\"cyan\" size=\"mini\" icon=\"el-icon-search\" @click=\"selectxmLibrary\">查询</el-button>\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetxmSearch\">重置</el-button>\n          </div>\n        </el-row>\n      </el-form>\n      <el-table stripe border :data=\"xmLibraryDataList\" @selection-change=\"handleSelectedXmLibraryChange\"\n                :header-cell-style=\"{'text-align':'center'}\" :cell-style=\"{'text-align':'center'}\">\n        <el-table-column type=\"selection\" width=\"55\"/>\n        <el-table-column label=\"序号\" type=\"index\" width=\"50\">\n        </el-table-column>\n        <el-table-column label=\"试验项目\" prop=\"syxmmc\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"项目描述\" prop=\"syxmms\" :show-overflow-tooltip=\"true\"/>\n      </el-table>\n      <pagination\n        v-show=\"xmLibraryTotal>0\"\n        :total=\"xmLibraryTotal\"\n        :page.sync=\"xmLibraryQueryForm.pageNum\"\n        :limit.sync=\"xmLibraryQueryForm.pageSize\"\n        @pagination=\"getXmLiraryData\"\n      />\n      <div slot=\"footer\">\n        <el-button @click=\"closeAddMjzDialog\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"commitAddMjzForm\">确 认</el-button>\n      </div>\n    </el-dialog>\n    <!--子组件定义模板内容-->\n    <el-dialog title=\"项目关联部位\" :visible.sync=\"isShowXmGlbwDialog\" v-if=\"isShowXmGlbwDialog\" v-dialogDrag>\n      <symbwh-dymbnr ref=\"symbwhDymbnrRef\" :mb-data=\"mbRowData\"></symbwh-dymbnr>\n    </el-dialog>\n    <!--  打印vue print nb插件-->\n    <div v-show=\"false\">\n      <div id=\"printHtmlId\" style=\"background:white;\">\n        <!--模板-->\n        <div style=\"text-align: center\">\n          <p>{{mbInfo.mbmc}}</p>\n        </div>\n        <p>葫芦娃，葫芦娃</p>\n        <p>一根藤上七朵花 </p>\n        <p>小小树藤是我家 啦啦啦啦 </p>\n        <p>叮当当咚咚当当　浇不大</p>\n        <p> 叮当当咚咚当当 是我家</p>\n        <p> 啦啦啦啦</p>\n        <p>...</p>\n        <div class=\"describle\">\n          <el-form :model=\"mbInfo\" :rules=\"mbzbRules\" ref=\"from\" class=\"demo-ruleForm\">\n            <el-form-item label=\"姓名:\" prop=\"name\">\n              <el-input v-model=\"mbInfo.mbmc\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"描述:\" prop=\"describle\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"4\"\n                :maxlength=\"2000\"\n                placeholder=\"\"\n                v-model=\"mbInfo.mbmc\">\n              </el-input>\n            </el-form-item>\n          </el-form>\n        </div>\n      </div>\n    </div>\n    <!--htmlToPdf插件-->\n    <el-dialog title=\"预览\" :visible.sync=\"isShowDownLoadDialog\" width=\"60%\" append-to-body>\n      <el-button @click=\"downloadPdf\">导出</el-button>\n      <div style=\"width: 100%;height:60vh;overflow: auto\">\n        <div id=\"printContentId\">\n          <div style=\"text-align: center\">\n            <h2>{{mbInfo.mbmc}}</h2></div>\n          <div>\n            <div\n              style=\"font-size: 20px;float: left;width:100%;border-left: 1px solid #000;border-right: 1px solid #000;border-top: 1px solid #000;\">\n              一、基本信息\n            </div>\n            <el-table\n              :data=\"tableData_jbxx\"\n              border\n              style=\"border: 1px solid #000;\"\n            >\n              <el-table-column\n                prop=\"column_1\"\n                label=\"变电站\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_5\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_2\"\n                label=\"委托单位\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_6\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_3\"\n                label=\"试验单位\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_7\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_4\"\n                label=\"运行编号\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_8\"\n                label=\"\"\n              >\n              </el-table-column>\n            </el-table>\n            <!--            <tbody>\n                        <tr>\n                          <td>变电站</td>\n                          <td></td>\n                          <td>委托单位</td>\n                          <td></td>\n                          <td>试验单位</td>\n                          <td></td>\n                          <td>运行编号</td>\n                          <td></td>\n                        </tr>\n                        <tr>\n                          <td>试验性质</td>\n                          <td></td>\n                          <td>试验日期</td>\n                          <td></td>\n                          <td>试验人员</td>\n                          <td></td>\n                          <td>试验地点</td>\n                          <td></td>\n                        </tr>\n                        <tr>\n                          <td>报告日期</td>\n                          <td></td>\n                          <td>编写人</td>\n                          <td></td>\n                          <td>审核人</td>\n                          <td></td>\n                          <td>批准人</td>\n                          <td></td>\n                        </tr>\n                        <tr>\n                          <td>试验天气</td>\n                          <td></td>\n                          <td>环境温度（℃）</td>\n                          <td></td>\n                          <td>环境相对湿度（%）</td>\n                          <td></td>\n                          <td>投运日期</td>\n                          <td></td>\n                        </tr>\n                        </tbody>-->\n          </div>\n          <div>\n            <div\n              style=\"font-size: 20px;float: left;width:100%;border-left: 1px solid #000;border-right: 1px solid #000;\">\n              二、设备铭牌\n            </div>\n            <el-table\n              :data=\"tableData_sbmp\"\n              border\n              :span-method=\"sbmpSpanMethod\"\n              style=\"border: 1px solid #000;\"\n            >\n              <el-table-column\n                prop=\"column_1\"\n                label=\"生产厂家\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_4\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_2\"\n                label=\"出厂编号\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_5\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_3\"\n                label=\"出厂日期\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_6\"\n                label=\"\"\n              >\n              </el-table-column>\n            </el-table>\n            <!--            <tbody id=\"sbmpTbodyId\">\n                        </tbody>-->\n          </div>\n          <div>\n            <div\n              style=\"font-size: 20px;float: left;width:100%;border-left: 1px solid #000;border-right: 1px solid #000;border-bottom: 1px solid #000;\">\n              三、试验数据\n            </div>\n            <div v-for=\"item in arr\" style=\"width: 100%\">\n              <div class=\"printTitle\">{{item.title}}</div>\n              <el-table :data=\"item.bwList\" style=\"width:100%;border: 1px solid #000;\" border\n                        :span-method=\"arraySpanMethod\">\n                <template v-for='(val) in item.zxmList'>\n                  <el-table-column\n                    :prop=\"val.column_name\"\n                    width=\"auto\"\n                    :label=\"val.label\"\n                  >\n                  </el-table-column>\n\n                </template>\n\n              </el-table>\n            </div>\n\n            <!--            <tbody id=\"sysjTableId\">\n                        <tr>\n                          <td colspan=\"5\" style=\"text-align: left;font-weight: bold\">12121212</td>\n                        </tr>\n                        <tr>\n                          <td>部位</td>\n                          <td>回路电阻初值(μΩ)</td>\n                          <td>回路电阻(μΩ)</td>\n                          <td>主回路电阻初值差(%)</td>\n                          <td>是否合格</td>\n                        </tr>\n                        <tr>\n                          <td>部位1</td>\n                          <td></td>\n                          <td></td>\n                          <td></td>\n                          <td></td>\n                        </tr>\n                        <tr>\n                          <td>部位2</td>\n                          <td></td>\n                          <td></td>\n                          <td></td>\n                          <td></td>\n                        </tr>\n                        <tr>\n                          <td>仪器型号</td>\n                          <td colspan=\"4\"></td>\n                        </tr>\n                        <tr>\n                          <td>结论</td>\n                          <td colspan=\"4\"></td>\n                        </tr>\n                        <tr>\n                          <td>备注</td>\n                          <td colspan=\"4\"></td>\n                        </tr>\n                        </tbody>-->\n          </div>\n        </div>\n      </div>\n      <div slot=\"footer\">\n        <el-button @click=\"closeYlDialog\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  //引入jquery,暂时没用\n  import $ from \"jquery\"\n  import {\n    addMbGlxmBatchToMbxm,\n    getPageDataListTosymb,\n    getSymbGlsyxmDataListByPage,\n    getXmLiraryData,\n    remove,\n    saveOrUpdate,\n    getMbGlMpinfoData,\n    getMbGlXmAndBw\n  } from '@/api/dagangOilfield/bzgl/symbwh'\n  import {getDeviceClassTreeNodeByPid} from '@/api/dagangOilfield/bzgl/sblxwh/sblxwh'\n  import {getGlSyzxmDataListByPage} from '@/api/dagangOilfield/bzgl/syxm'\n  import Glsymp from '@/views/dagangOilfield/bzgl/sybzk/glsymp'\n  import symbwhDymbnr from '@/views/dagangOilfield/bzgl/sybzk/symbwhDymbnr'\n  import htmlToPdf from '@/utils/print/htmlToPdf'\n\n  export default {\n    name: 'symbSelect',\n    components: {Glsymp, symbwhDymbnr},\n    data() {\n      return {\n        //基本信息表格数据\n        tableData_jbxx: [\n          {\n            'column_1': '试验性质',\n            'column_2': '试验日期',\n            'column_3': '试验人员',\n            'column_4': '试验地点',\n            'column_5': '',\n            'column_6': '',\n            'column_7': '',\n            'column_8': '',\n          },\n          {\n            'column_1': '报告日期',\n            'column_2': '编写人',\n            'column_3': '审核人',\n            'column_4': '批准人',\n            'column_5': '',\n            'column_6': '',\n            'column_7': '',\n            'column_8': '',\n          },\n          {\n            'column_1': '试验天气',\n            'column_2': '环境温度（℃）',\n            'column_3': '环境相对湿度（%）',\n            'column_4': '投运日期',\n            'column_5': '',\n            'column_6': '',\n            'column_7': '',\n            'column_8': '',\n          }\n        ],\n        //设备铭牌表格数据\n        tableData_sbmp: [{\n          'column_1': '额定电压',\n          'column_2': '设备型号',\n          'column_3': '',\n          'column_4': '',\n          'column_5': '',\n          'column_6': '',\n        },],\n        //要循环的试验表格数据\n        arr: [{\n          title: \"\",//试验名称\n          zxmList: [],//子项目数据（表头）\n          bwList: [],//部位数据（第一列开头）\n        }],\n        //下载弹出框控制\n        isShowDownLoadDialog: false,\n        printObj: {\n          id: \"previewId\", // 必填，渲染打印的内容使用\n          popTitle: \"&nbsp;\", //\n          previewTitle: \"&nbsp;\",\n          preview: false,\n        },\n        mbInfo: {},\n        //打印内容div中id值\n        previewId: \"\",\n        //定义模板内容弹出框传递参数\n        mbRowData: {},\n        //定义模板内容弹出框\n        isShowXmGlbwDialog: false,\n        xmSelectedForm: {\n          //试验模板id\n          symbid: undefined,\n          //试验项目数据集合\n          xmDataRows: []\n        },\n        //项目库弹出框标题\n        xmLibraryAddDialogTitle: '项目库',\n        //项目库弹出框控制\n        isShowAddGlxmDialog: false,\n        //项目库查询参数\n        xmLibraryQueryForm: {\n          symbid: undefined,\n          syxmmc: '',\n          pageNum: 1,\n          pageSize: 10\n        },\n        //项目库数据\n        xmLibraryDataList: [],\n        //项目库项目总数\n        xmLibraryTotal: 0,\n        //表单验证\n        mbzbRules: {\n          mbmc: [\n            {required: true, message: '请输入模板名称', trigger: 'blur'}\n          ]\n        },\n        // 筛选条件\n        filterInfo: {\n          data: {\n            mbmc: ''\n          },\n          fieldList: [\n            {label: '模板名称', type: 'input', value: 'mbmc', multiple: true}\n          ]\n        },\n        //新增按钮控制\n        addDisabled: true,\n        //树结构懒加载参数\n        props: {\n          label: 'name',\n          children: 'zones',\n          isLeaf: 'leaf',\n        },\n        //删除选择列\n        selectRows: [],\n        //选中的单条对象\n        selectRowData: {},\n        //弹出框表单\n        form: {},\n        //查询试验部位参数\n        querySyBwParam: {\n          sblxid: undefined,\n          mbmc: undefined,\n          pageNum: 1,\n          pageSize: 10\n        },\n        //点击树节点赋值\n        treeForm: {},\n        //试验部位列表\n        tableAndPageInfo: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          tableData: [],\n          tableHeader: [\n            {label: '模板名称', prop: 'mbmc', minWidth: '100'},\n            {label: '是否默认', prop: 'sfmr', minWidth: '100'},\n            {label: '是否停用', prop: 'sfty', minWidth: '100'},\n          ],\n          option: {checkBox: true, serialNumber: true}\n        },\n        //组织树\n        treeOptions: [],\n\n        isShowDetails: false,\n        title: '',\n\n        //删除是否可用\n        multipleSensor: true,\n        //查询参数\n        queryParams: {\n          bm: undefined,\n          pageNum: 1,\n          pageSize: 10\n        },\n        isDisabled: false,\n        isShow: true,\n\n        //关联项目弹出框title\n        glxmDialogTitle: '关联项目',\n        //关联项目弹出框控制展开\n        isGlxmDialogShow: false,\n\n        //关联项目total\n        glxmTotal: 0,\n        //关联项目查询参数\n        glxmQueryParams: {\n          symbid: undefined,\n          pageNum: 1,\n          pageSize: 10\n        },\n\n        //关联子项目total\n        glzxmTotal: 0,\n        //关联子项目查询参数\n        glzxmQueryParams: {\n          syxmid: undefined,\n          pageSize: 10,\n          pageNum: 1\n        },\n\n        //模板关联项目数据\n        mbGlxmDataList: [],\n        //项目关联的子项目数据\n        zxmGlmbDataList: [],\n        //模板关联项目选中框数据\n        selectedRowDataChange: [],\n        //显示铭牌弹框\n        showMpDialog: false,\n        //选中行数据\n        rowData: {},\n\n        //关联名牌便利\n        mpList: [],\n\n        //试验数据\n        sysjDataList: [],\n        //试验表格默认固定的行\n        defaultRow: [\"仪器型号\", \"结论\", \"备注\"],\n      }\n    },\n    watch: {},\n    created() {\n      //获取数据列表\n      this.getData()\n    },\n    mounted() {\n    },\n    methods: {\n      //试验数据表格合并方法\n      arraySpanMethod({row, column, rowIndex, columnIndex}) {\n        if (this.defaultRow.includes(row.SYBW)) {\n          if (columnIndex > 0) {\n            return [1, row.totalNum]\n          }\n        }\n      },\n      //设备铭牌表格合并方法\n      sbmpSpanMethod({row, column, rowIndex, columnIndex}) {\n        if (columnIndex > 3) {\n          return [1, 2]\n        }\n      },\n      //模板详情按钮\n      handleMbInfo(row) {\n        //获取当前模板id加载页面信息\n        this.getMbGlMpinfoData(row);\n        //获取试验数据\n        this.getMbGlXmAndBw(row);\n      },\n      //导出pdf操作\n      downloadPdf() {\n        htmlToPdf.downloadPDF(document.querySelector('#printContentId'), this.mbInfo.mbmc)\n      },\n      //获取当前模板id加载页面信息\n      getMbGlMpinfoData(param) {\n        getMbGlMpinfoData(param).then(res => {\n          this.mpList = res.data;\n          //调用渲染铭牌页面开始\n          // this.applyMpHtml(this.mpList, param);\n          //打开弹出框\n          this.isShowDownLoadDialog = true;\n        })\n\n      },\n      //获取试验数据\n      getMbGlXmAndBw(rowData) {\n        //每次获取数据前先清空，再添加，否则多次进入页面时会获取重复数据\n        this.sysjDataList = [];\n        getMbGlXmAndBw(rowData).then(res => {\n          let resMap = res.data;\n          //遍历返回结果\n          for (let key in resMap) {\n            //解析试验数据\n            this.analysisSyData(key, resMap[key]);\n          }\n          //画试验数据页面\n          this.applySysjDataToHtml();\n        })\n      },\n      //解析后台试验数据\n      analysisSyData(syxmmc, zxmAndBwData) {\n        let sysjData = {}\n        sysjData.syxmmc = syxmmc;\n        for (let key in zxmAndBwData[0]) {\n          sysjData[key] = zxmAndBwData[0][key]\n        }\n        this.sysjDataList.push(sysjData);\n      },\n      //渲染实验数据到页面\n      applySysjDataToHtml() {\n        this.arr = [];\n        // $('#sysjTableId').html(\"\");\n        //进行数据处理重组\n        let data = this.sysjDataList;\n        if (data.length > 0) {\n          for (let i = 0; i < data.length; i++) {\n            let dataChild = data[i];\n            let title = dataChild.syxmmc;//试验项目名称\n            let bwList = dataChild.bwList; //部位list\n            let zxmList = dataChild.zxmList; //子项目list\n            let hx = [\n              {\n                \"label\": title, //第一个表头为试验项目名称\n                \"column_name\": \"SYBW\", //第一列对应的字段名（试验部位）\n              },\n            ];\n            zxmList.forEach(zxm => {\n              hx.push({\n                \"label\": zxm.syzxmmc, //每列的表头\n                \"column_name\": \"\", //每列对应的数值暂时设置为空白\n              })\n            })\n            let sx = [];\n            bwList.forEach(bw => {\n              sx.push({\n                \"SYBW\": bw.SYBW,\n                \"totalNum\": zxmList.length//记录总列数，用于固定行的合并事件\n              })\n            })\n            //后四行固定\n            sx.push(\n              {\n                \"SYBW\": \"结果\",\n                \"totalNum\": zxmList.length//记录总列数，用于固定行的合并事件\n              },\n              {\n                \"SYBW\": \"仪器型号\",\n                \"totalNum\": zxmList.length//记录总列数，用于固定行的合并事件\n              },\n              {\n                \"SYBW\": \"结论\",\n                \"totalNum\": zxmList.length//记录总列数，用于固定行的合并事件\n              },\n              {\n                \"SYBW\": \"备注\",\n                \"totalNum\": zxmList.length//记录总列数，用于固定行的合并事件\n              }\n            )\n            this.arr.push({\n              title: title,\n              zxmList: hx,\n              bwList: sx,\n            });\n          }\n        }\n        //拼接的铭牌表格\n        /*     let str = \"\";\n             if (this.sysjDataList.length > 0) {\n               for (let i = 0; i < this.sysjDataList.length; i++) {\n                 //拼接项目序号\n                 let xmIndex = i + 1;\n                 str += \"<tr><td colspan='5' style='text-align: left;font-weight: bold;font-size: 15px'>\" + xmIndex + \"、\" + this.sysjDataList[i].syxmmc + \"</td></tr>\";\n                 // this.sysjDataList[i].bwList;\n                 // this.sysjDataList[i].zxmList;\n                 // str += \"<tr><td>\"+this.sysjDataList[i].syxmmc+\"</td><td v-for=item in this.sysjDataList[i].bwList></td></tr>\"\n\n               }\n               this.$nextTick(() => {\n                 $('#sysjTableId').append(str)\n               })\n             }*/\n      },\n      //渲染铭牌页面开始mpList:反回的铭牌列表  row：模板行对象\n      applyMpHtml(mpList, row) {\n        //每次打开需要重新渲染一次,先将置空\n        $('#sbmpTbodyId').html(\"\");\n        //清空重新赋值\n        this.mbInfo = {}\n        this.mbInfo.mbmc = row.mbmc;\n        //拼接的铭牌表格\n        let str = \"\";\n        //先判断是否分相铭牌\n        if (mpList.length > 0) {\n          if (mpList[0].SFFX == '1') { //当前铭牌为分相铭牌时\n            //写死第一行\n            str += \"<tr><td style='padding: 10px;font-size: 15px;'>相别</td>\" +\n              \"<td style='padding: 10px;font-size: 15px;'>A</td>\" +\n              \"<td style='padding: 10px;font-size: 15px;'>B</td>\" +\n              \"<td style='padding: 10px;font-size: 15px;'>C</td>\" +\n              \"</tr>\";\n            //开始遍历展示\n            for (let a = 0; a < mpList.length; a++) {\n              str += \"<tr>\"\n              str += \"<td style='padding: 10px;font-size: 15px;'>\";\n              str += mpList[a].title + \"</td>\";\n              str += \"<td></td>>\"\n              str += \"<td></td>>\"\n              str += \"<td></td>>\"\n              str += \"</tr>\"\n            }\n          } else {  //铭牌不分相\n            //当前铭牌不属于分相铭牌\n            //每列展示单元格数量\n            let col = 3;\n            //展示行数\n            var lines = Math.ceil(mpList.length / col);\n            //遍历展示行数\n            for (var i = 0; i < lines; i++) {\n              str += \"<tr>\";\n              //遍历列\n              for (var j = 0; j < col; j++) {\n                if (i * col + j < mpList.length) {\n                  str += \"<td style='padding: 10px;font-size: 15px;'>\";\n                  //铭牌标题赋值\n                  str += mpList[i * col + j].title + \"</td>\";\n                  //铭牌值赋值\n                  str += mpList[i * col + j].sfmb == 1 ? \"<td> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>\" : \"<td>\" + mpList[i * col + j].column_name + \"</td>\"\n                }\n              }\n              str += \"</tr>\";\n            }\n          }\n        }\n        //渲染铭牌页面\n        this.$nextTick(() => {\n          $('#sbmpTbodyId').append(str)\n        })\n        //打开弹出框\n        this.isShowDownLoadDialog = true;\n      },\n      //关闭预览弹出框\n      closeYlDialog() {\n        //清空表单\n        this.mbInfo = {};\n        //赋值完关闭弹窗\n        this.isShowDownLoadDialog = false;\n      }\n      ,\n      //定义模板内容\n      handleClickMbnr(row) {\n        //打开组件弹出框\n        this.isShowXmGlbwDialog = true;\n        //给子组件传递数据\n        this.mbRowData = row;\n      }\n      ,\n      //获取项目库项目数据\n      getXmLiraryData() {\n        getXmLiraryData(this.xmLibraryQueryForm).then(res => {\n          this.xmLibraryDataList = res.data.records\n          this.xmLibraryTotal = res.data.total\n        })\n      }\n      ,\n      //项目弹出框新增按钮\n      addMbGlXm() {\n        this.getXmLiraryData()\n        this.isShowAddGlxmDialog = true\n      }\n      ,\n      //项目库弹出框取消按钮\n      closeAddMjzDialog() {\n        this.isShowAddGlxmDialog = false\n      }\n      ,\n      //项目库弹窗确认按钮\n      commitAddMjzForm() {\n        if (this.xmSelectedForm.xmDataRows.length < 1) {\n          this.$message.info('未关联项目！！！已取消')\n          //如果未选中数据,则直接关闭弹窗\n          this.isShowAddGlxmDialog = false\n        } else {\n          console.log(this.xmSelectedForm)\n          //若选择数据后\n          addMbGlxmBatchToMbxm(this.xmSelectedForm).then(res => {\n            if (res.code === '0000') {\n              this.$message.success('关联成功')\n            } else {\n              this.$message.error('关联失败！！')\n            }\n            //关闭弹窗\n            this.isShowAddGlxmDialog = false\n            //调用获取关联子项目列表\n            this.getSymbGlsyxmDataListByPage()\n          })\n        }\n      }\n      ,\n      //项目库行选中事件\n      handleSelectedXmLibraryChange(rows) {\n        this.xmSelectedForm.xmDataRows = rows\n      }\n      ,\n      //项目库查询按钮\n      selectxmLibrary() {\n        this.getXmLiraryData()\n      }\n      ,\n      //项目库重置按钮\n      resetxmSearch() {\n        this.xmLibraryQueryForm.syxmmc = ''\n        this.getXmLiraryData()\n      }\n      ,\n      //获取关联子列表方法\n      getZxmDataList() {\n        getGlSyzxmDataListByPage(this.glzxmQueryParams).then(res => {\n          this.glzxmTotal = res.data.total\n          this.zxmGlmbDataList = res.data.records\n        })\n      }\n      ,\n\n      //关联项目\n      handleClickGlxm(row) {\n        //清空原来子项目数据\n        this.zxmGlmbDataList = []\n        //打开关联项目弹出框\n        this.isGlxmDialogShow = true\n        //给参数赋值\n        this.glxmQueryParams.symbid = row.objId\n        //查询项目库数据时参数\n        this.xmLibraryQueryForm.symbid = row.objId\n        //给试验项目库添加时使用\n        this.xmSelectedForm.symbid = row.objId\n        //获取模板关联项目数据\n        this.getSymbGlsyxmDataListByPage()\n      }\n      ,\n      //获取关联项目弹出框数据\n      getSymbGlsyxmDataListByPage() {\n        getSymbGlsyxmDataListByPage(this.glxmQueryParams).then(res => {\n          this.mbGlxmDataList = res.data.records\n          this.glxmTotal = res.data.total\n        })\n      }\n      ,\n      //试验项目复选框点击时间点击操作\n      handleGlxmSelectedChange(rows) {\n        this.selectedRowDataChange = rows\n      }\n      ,\n      //删除模板关联项目\n      deleteMbGlXm() {\n        if (this.selectedRowDataChange.length < 1) {\n          this.$message.warning('请选择正确的数据！！！')\n          return\n        }\n        let ids = this.selectedRowDataChange.map(item => {\n          return item.objId\n        })\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove(ids).then(({code}) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getSymbGlsyxmDataListByPage()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n              this.getSymbGlsyxmDataListByPage()\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n      //试验项目点击行数据时的单机操作\n      handleMbGlxmRowClick(row) {\n        this.glzxmQueryParams.syxmid = row.syxmid\n        this.getZxmDataList()\n      },\n      //懒加载函数\n      loadNode(node, resolve) {\n        let TreeparamMap = {\n          pid: '',\n          spbLogo: ['输电设备', '变电设备','配电设备']\n        }\n        if (node.level === 0) {\n          TreeparamMap.pid = 'sb'\n          return this.getTreeNode(TreeparamMap, resolve)\n        }\n        setTimeout(() => {\n          TreeparamMap.pid = node.data.code\n          this.getTreeNode(TreeparamMap, resolve)\n        }, 500)\n\n      },\n      //获取树节点数据\n      getTreeNode(paramMap, resolve) {\n        getDeviceClassTreeNodeByPid(paramMap).then(res => {\n          let treeNodes = []\n          res.data.forEach(item => {\n            let node = {\n              name: item.name,\n              level: item.level,\n              id: item.id,\n              pid: item.pid,\n              leaf: false,\n              code: item.code\n            }\n            treeNodes.push(node)\n          })\n          resolve(treeNodes)\n        })\n      },\n      //添加后确认保存按钮\n      save() {\n        this.$refs['form'].validate((valid) => {\n          if (valid) {\n            saveOrUpdate(this.form).then(res => {\n              if (res.code === '0000') {\n                this.$message.success(res.msg)\n                this.tableAndPageInfo.pager.pageResize = 'Y'\n                this.getData()\n                this.isShowDetails = false\n              } else {\n                this.$message.error(res.msg)\n              }\n            })\n\n          }\n        })\n      },\n      //树节点点击事件\n      handleNodeClick(data) {\n        console.log('树节点点击')\n        console.log(data)\n        if (data.level != '0' && data.level != '1') {\n          //新增按钮可点击\n          this.addDisabled = false\n          this.treeForm = data\n          this.querySyBwParam.sblxid = data.code\n          this.getData()\n        } else {\n          this.addDisabled = true\n        }\n      },\n      //添加按钮\n      addSensorButton() {\n        this.form = {}\n        this.form.sblx = this.treeForm.name\n        this.form.sblxid = this.treeForm.code\n        this.isShowDetails = true\n        this.title = '新增'\n      },\n      //列表查询\n      async getData(params) {\n        try {\n          const param = {...this.querySyBwParam, ...params}\n          const {data, code} = await getPageDataListTosymb(param)\n          if (code === '0000') {\n            this.tableAndPageInfo.tableData = data.records\n            this.tableAndPageInfo.pager.total = data.total\n          }\n        } catch (e) {\n          console.log(e)\n        }\n      }\n      ,\n      /**\n       * 表格多选框\n       */\n      handleSelectionChange(rows) {\n        this.selectRows = rows;\n      },\n\n      close() {\n        this.isShowDetails = false\n      }\n      ,\n      //修改模板主表内容\n      updateDetails(row) {\n        this.title = '修改'\n        this.isShowDetails = true\n        this.form = row\n        this.isDisabled = false\n        this.isShow = true\n      }\n      ,\n\n      createTemplate(row) {\n        console.log(row)\n      }\n      ,\n      //查看模板主表详情按钮\n      getDetails(row) {\n        this.title = '详情'\n        this.isShowDetails = true\n        this.form = row\n        this.isDisabled = true\n        this.isShow = false\n      }\n      ,\n\n      //删除按钮\n      deleteSensorButton() {\n        if (this.selectRows.length < 1) {\n          this.$message.warning('请选择正确的数据！！！')\n          return\n        }\n        let ids = this.selectRows.map(item => {\n          return item.objId\n        })\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove(ids).then(({code}) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.tableAndPageInfo.pager.pageResize = 'Y'\n              this.getData()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n              this.tableAndPageInfo.pager.pageResize = 'Y'\n              this.getData()\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n\n      }\n      ,\n      //导出按钮\n      handleExport() {\n\n      }\n      ,\n\n      //关联铭牌点击事件\n      handleClickGlMp(row) {\n        this.showMpDialog = true\n        this.rowData = row\n      }\n      ,\n      //关闭试验铭牌弹窗\n      closeMpDialog() {\n        this.showMpDialog = false\n      }\n      ,\n\n      filterReset() {\n      },\n\n      //关闭试验模板弹窗\n      closeSymbComment() {\n        this.$emit(\"closeSymbSelectDialog\", false)\n      },\n      //点击确认后给父组件传递数据\n      // this.selectRowData != undefined && JSON.stringify(this.selectRowData) != \"{}\"\n      commitMbdata() {\n        if (this.selectRows.length == 1 && this.selectRows != undefined) {\n         this.$emit(\"handleAcceptMbData\",this.selectRows[0]);\n          this.$emit(\"closeSymbSelectDialog\", false)\n        } else {\n          this.$message.warning(\"请选择一条数据\")\n        }\n      },\n\n    }\n  }\n</script>\n\n<style lang='scss' scoped>118\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n/*导出pdf格式设置开始*/\n#printContentId {\n  background-color: #fff;\n  width: 100%;\n  /* height: 400px; */\n  margin: auto;\n  padding: 16px;\n  box-sizing: border-box;\n  //试验数据样式\n  .printTitle {\n    text-align: left;\n    line-height: 40px;\n    border-left: 1px solid #000;\n    border-right: 1px solid #000;\n    //border-bottom: 1px solid #000;\n    padding-left: 10px;\n  }\n}\n\n//修改table表头颜色\n/deep/ #printContentId .el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th {\n  background: #fff;\n  border-color: #000;\n  font-weight: inherit;\n}\n\n/deep/ #printContentId .el-table--enable-row-transition .el-table__body td {\n  border-color: #000;\n}\n\n.table_style td, th {\n  padding: 10px;\n  font-size: 15px;\n}\n\n.table_style {\n  border-collapse: collapse;\n  width: 100%;\n  text-align: center;\n  /* border-bottom: 0;\n   border-left: 0;\n   border-right: 0;*/\n}\n\n/* 设置滚动条的样式 */\n::-webkit-scrollbar {\n  width: 12px;\n}\n\n/* 滚动槽 */\n::-webkit-scrollbar-track {\n  //-webkit-box-shadow:inset006pxrgba(0,0,0,0.3);\n  border-radius: 10px;\n}\n\n/* 滚动条滑块 */\n::-webkit-scrollbar-thumb {\n  border-radius: 10px;\n  background: rgba(0, 0, 0, 0.1);\n  //-webkit-box-shadow:gba(0,0,0,0.5);\n}\n\n::-webkit-scrollbar-thumb:window-inactive {\n  background: rgba(0, 0, 0, 0.1);\n}\n\n[data-v-67a974b1]::-webkit-scrollbar {\n  width: 8px;\n}\n\n.item {\n  width: 225px;\n  float: left;\n}\n\n/*导出pdf格式设置结束*/\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  max-height: 79vh;\n  overflow: auto;\n}\n\n.app-container {\n  padding: 3px;\n}\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk/sybgjlcomment"}]}