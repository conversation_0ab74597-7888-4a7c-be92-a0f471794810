{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sdsbgl\\sdsb.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sdsbgl\\sdsb.vue", "mtime": 1706897324964}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgZ2V0TGlzdCwKICBnZXRSZXN1bURhdGFMaXN0LAogIGdldFRyZWVMaXN0LAogIHJlbW92ZSwKICBzYXZlT3JVcGRhdGUsCiAgdXBkYXRlU3RhdHVzLAogIGFkZGR3enlmc3R6LAogIGV4cG9ydEV4Y2VsLAogIGltcG9ydEV4Y2VsCn0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYXNzZXQvc2RzYiI7CmltcG9ydCB7CiAgZ2V0TGlzdHhsLAogIHNhdmVPclVwZGF0ZXhsLAogIHhscmVtb3ZlCn0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYXNzZXQvc2R4bCI7CmltcG9ydCB7CiAgZ2V0TGlzdGd0LAogIHNhdmVPclVwZGF0ZWd0LAogIGd0cmVtb3ZlLAogIHNhdmVUb3dlckJhdGNoCn0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYXNzZXQvc2RndCI7CmltcG9ydCB7CiAgZ2V0UGFyYW1EYXRhTGlzdCwKICBnZXRQYXJhbXNWYWx1ZSwKICBzYXZlUGFyYW1WYWx1ZQp9IGZyb20gIkAvYXBpL2RhZ2FuZ09pbGZpZWxkL2Fzc2V0L3BhcmFtZXRlcnMiOwppbXBvcnQgewogIGdldFNibHhEYXRhTGlzdFNlbGVjdGVkLAogIGdldE9yZ2FuaXphdGlvblNlbGVjdGVkCn0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYXNzZXQvYmRzYnR6IjsKaW1wb3J0IHsgZ2V0TGlzdFRaIH0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYnpnbC9iemdmZ2wvYnpnZmdsIjsKaW1wb3J0IHsgZ2V0VG9rZW4gfSBmcm9tICJAL3V0aWxzL2F1dGgiOwppbXBvcnQgenh3aCBmcm9tICJAL3ZpZXdzL2RhZ2FuZ09pbGZpZWxkL2R3enlnbC9zZHNiZ2wvenh3aCI7CmltcG9ydCB7ZGVsZXRlQnlJZH0gZnJvbSAiQC9hcGkvdG9vbC9maWxlIjsKaW1wb3J0IHtnZXREaWN0VHlwZURhdGF9IGZyb20gIkAvYXBpL3N5c3RlbS9kaWN0L2RhdGEiOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJxeGJ6ayIsCiAgY29tcG9uZW50czoge3p4d2h9LAogIGRhdGEoKSB7CiAgICB2YXIgdmFsaWRhdGVUb3dlckVuZE51bSA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsKICAgICAgaWYgKCF0aGlzLmFkZFRvd2VyQmF0Y2hGb3JtLnRvd2VyRW5kTnVtKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCLkuI3og73kuLrnqboiKSk7CiAgICAgIH0KICAgICAgaWYgKAogICAgICAgIHRoaXMuYWRkVG93ZXJCYXRjaEZvcm0udG93ZXJFbmROdW0gPAogICAgICAgIHRoaXMuYWRkVG93ZXJCYXRjaEZvcm0udG93ZXJTdGFydE51bQogICAgICApIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuadhuWhlOe7k+adn+e8luWPt+S4jeiDveWwj+S6jui1t+Wni+e8luWPtyIpKTsKICAgICAgfSBlbHNlIHsKICAgICAgICBjYWxsYmFjaygpOwogICAgICB9CiAgICB9OwogICAgcmV0dXJuIHsKICAgICAgaWRzOiBbXSwKCiAgICAgIGljb25zOiB7CiAgICAgICAgMTogImNhdGVnb3J5VHJlZUljb25zIiwKICAgICAgICAyOiAidGFibGVJY29uIiwKICAgICAgICAzOiAiY2xhc3NJY29uIiwKICAgICAgICA0OiAiY2xhc3NJY29uMiIKICAgICAgfSwKICAgICAgcGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgIH0sCiAgICAgIHJlc3VtZVF1ZXJ5OiB7CiAgICAgICAgZm9yZWlnbk51bTogdW5kZWZpbmVkLAogICAgICAgIHNibHg6IHVuZGVmaW5lZAogICAgICB9LAogICAgICB1cGRhdGVMaXN0OiB7CiAgICAgICAgc2J6dDogIiIsCiAgICAgICAgb2JqSWQ6ICIiCiAgICAgIH0sCiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICBvcHRpb25zOiBbCiAgICAgICAgewogICAgICAgICAgdmFsdWU6ICIxMTBrViIsCiAgICAgICAgICBsYWJlbDogIjExMGtWIgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdmFsdWU6ICIzNWtWIiwKICAgICAgICAgIGxhYmVsOiAiMzVrViIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAiMTBrViIsCiAgICAgICAgICBsYWJlbDogIjEwa1YiCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIjZrViIsCiAgICAgICAgICBsYWJlbDogIjZrViIKICAgICAgICB9CiAgICAgIF0sCiAgICAgIHNienQ6IFsKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIuWcqOi/kCIsCiAgICAgICAgICBsYWJlbDogIuWcqOi/kCIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAi5YGc6L+QIiwKICAgICAgICAgIGxhYmVsOiAi5YGc6L+QIgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdmFsdWU6ICLmiqXlup8iLAogICAgICAgICAgbGFiZWw6ICLmiqXlup8iCiAgICAgICAgfQogICAgICBdLAoKICAgICAgLy/nlLXljovnrYnnuqfkuIvmi4nmoYYKICAgICAgdm9sdGFnZUxldmVsTGlzdFNlbGVjdGVkOiBbXSwKICAgICAgZGV2aWNlTmFtZU9wdGlvbnM6IFtdLAogICAgICBzYmZpbHRlckluZm86IHsKICAgICAgICBkYXRhOiB7CiAgICAgICAgICBzc3hsOiBbXSwKICAgICAgICAgIHNzZ3M6IFtdLAogICAgICAgICAgc2J6dDogIiIsCiAgICAgICAgICB5eGJ6OiBbXQogICAgICAgIH0sCiAgICAgICAgZmllbGRMaXN0OiBbCiAgICAgICAgICB7IGxhYmVsOiAi5omA5bGe5p2G5aGU5ZCN56ewIiwgdHlwZTogImlucHV0IiwgdmFsdWU6ICJndG1jIiB9LAogICAgICAgICAgeyBsYWJlbDogIuiuvuWkh+exu+WeiyIsIHR5cGU6ICJpbnB1dCIsIHZhbHVlOiAic2JseG1jIiB9LAogICAgICAgICAgeyBsYWJlbDogIuiuvuWkh+WQjeensCIsIHR5cGU6ICJpbnB1dCIsIHZhbHVlOiAic2JtYyIgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLov5DooYznirbmgIEiLAogICAgICAgICAgICB0eXBlOiAic2VsZWN0IiwKICAgICAgICAgICAgdmFsdWU6ICJ5eHp0IiwKICAgICAgICAgICAgb3B0aW9uczogWwogICAgICAgICAgICAgIHsgbGFiZWw6ICLlnKjov5AiLCB2YWx1ZTogIjExMGtWIiB9LAogICAgICAgICAgICAgIHsgbGFiZWw6ICLlgZzov5AiLCB2YWx1ZTogIjM1a1YiIH0KICAgICAgICAgICAgXQogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLmipXov5Dml6XmnJ8iLAogICAgICAgICAgICB0eXBlOiAiZGF0ZSIsCiAgICAgICAgICAgIHZhbHVlOiAidHlycUFyciIsCiAgICAgICAgICAgIGRhdGVUeXBlOiAiZGF0ZXJhbmdlIiwKICAgICAgICAgICAgZm9ybWF0OiAieXl5eS1NTS1kZCIKICAgICAgICAgIH0sCiAgICAgICAgICB7IGxhYmVsOiAi55Sf5Lqn5Y6C5a62IiwgdHlwZTogImlucHV0IiwgdmFsdWU6ICJzc2NqIiB9CiAgICAgICAgXQogICAgICB9LAoKICAgICAgdGFibGVBbmRQYWdlSW5mbzM6IHsKICAgICAgICBwYWdlcjogewogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMDBdCiAgICAgICAgfSwKICAgICAgICBvcHRpb246IHsKICAgICAgICAgIGNoZWNrQm94OiB0cnVlLAogICAgICAgICAgc2VyaWFsTnVtYmVyOiB0cnVlCiAgICAgICAgfSwKICAgICAgICB0YWJsZURhdGE6IFtdLAogICAgICAgIHRhYmxlSGVhZGVyOiBbCiAgICAgICAgICB7IHByb3A6ICJsaW5lTmFtZSIsIGxhYmVsOiAi5omA5bGe57q/6LevIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJndG1jIiwgbGFiZWw6ICLmiYDlsZ7mnYbloZTlkI3np7AiLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgcHJvcDogInNibHhtYyIsIGxhYmVsOiAi6K6+5aSH57G75Z6LIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJzYm1jIiwgbGFiZWw6ICLorr7lpIflkI3np7AiLCBtaW5XaWR0aDogIjE4MCIgfSwKICAgICAgICAgIHsgcHJvcDogImdneGgiLCBsYWJlbDogIuinhOagvOWei+WPtyIsIG1pbldpZHRoOiAiMTgwIiB9LAogICAgICAgICAgeyBwcm9wOiAieXh6dCIsIGxhYmVsOiAi6L+Q6KGM54q25oCBIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJ0eXJxIiwgbGFiZWw6ICLmipXov5Dml6XmnJ8iLCBtaW5XaWR0aDogIjI1MCIgfSwKICAgICAgICAgIHsgcHJvcDogInNjY2oiLCBsYWJlbDogIueUn+S6p+WOguWutiIsIG1pbldpZHRoOiAiMTQwIiB9CiAgICAgICAgICAvKnsKICAgICAgICAgICAgZml4ZWQ6InJpZ2h0IiwKICAgICAgICAgICAgcHJvcDogJ29wZXJhdGlvbicsCiAgICAgICAgICAgIGxhYmVsOiAn5pON5L2cJywKICAgICAgICAgICAgbWluV2lkdGg6ICcxMzBweCcsCiAgICAgICAgICAgIHN0eWxlOiB7ZGlzcGxheTogJ2Jsb2NrJ30sCiAgICAgICAgICAgIG9wZXJhdGlvbjogWwogICAgICAgICAgICAgIHtuYW1lOiAn5L+u5pS5JywgY2xpY2tGdW46IHRoaXMudXBkYXRlUm93fSwKICAgICAgICAgICAgICB7bmFtZTogJ+ivpuaDhScsIGNsaWNrRnVuOiB0aGlzLmRldGFpbHNJbmZvfQogICAgICAgICAgICBdCiAgICAgICAgICB9LCovCiAgICAgICAgXQogICAgICB9LAoKICAgICAgcmVzdW1QYWdlSW5mbzogewogICAgICAgIHBhZ2VyOiB7CiAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgdG90YWw6IDAsCiAgICAgICAgICBzaXplczogWzEwLCAyMCwgNTAsIDEwMF0KICAgICAgICB9LAogICAgICAgIG9wdGlvbjogewogICAgICAgICAgY2hlY2tCb3g6IHRydWUsCiAgICAgICAgICBzZXJpYWxOdW1iZXI6IHRydWUKICAgICAgICB9LAogICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgdGFibGVIZWFkZXI6IFsKICAgICAgICAgIHsgcHJvcDogImZvcmVpZ25OdW0iLCBsYWJlbDogIuiuvuWkh+WQjeensCIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBwcm9wOiAic2JseCIsIGxhYmVsOiAi6K6+5aSH57G75Z6LIiwgbWluV2lkdGg6ICIxODAiIH0sCiAgICAgICAgICB7IHByb3A6ICJiZ2x4IiwgbGFiZWw6ICLlj5jmm7TnsbvlnosiLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgcHJvcDogIm1zIiwgbGFiZWw6ICLmj4/ov7AiLCBtaW5XaWR0aDogIjI1MCIgfSwKICAgICAgICAgIHsgcHJvcDogImJnciIsIGxhYmVsOiAi5Y+Y5pu05Lq6IiwgbWluV2lkdGg6ICIxNDAiIH0sCiAgICAgICAgICB7IHByb3A6ICJiZ3NqIiwgbGFiZWw6ICLlj5jmm7Tml7bpl7QiLCBtaW5XaWR0aDogIjE0MCIgfQogICAgICAgIF0KICAgICAgfSwKCiAgICAgIC8v5p2G5aGU5oyC5o6l6K6+5aSHdGFi6aG1CiAgICAgIGd0Z2pzYlRhYk5hbWU6ICJqYyIsCiAgICAgIC8v5p2G5aGU6K+m5oOF5by55Ye65qGGCiAgICAgIHNiRGlhbG9nRm9ybVZpc2libGU6IGZhbHNlLAogICAgICAvL+iuvuWkh+WxpeWOhueKtuaAgeWPmOabtOiusOW9lQogICAgICBzYmxsenRiZ2psTGlzdDogW10sCiAgICAgIC8v6K6+5aSH5bGl5Y6G57y66Zm36K6w5b2V5pWw5o2u6ZuG5ZCICiAgICAgIHNibGxxeGpsTGlzdDogWwogICAgICAgIC8qewogICAgICAgICAgc3NnczogJzExMGtWWFhY57q/6LevJywKICAgICAgICAgIGd0aDogJzAwMSMnLAogICAgICAgICAgc2JseDogJ+adhuWhlCcsCiAgICAgICAgICBxeHh6OiAn5Lil6YeNJywKICAgICAgICAgIGR5ZGo6ICczNWtWJywKICAgICAgICAgIHNieGg6ICdYWFjlnovlj7cnLAogICAgICAgICAgc2NjajogJ1hYWOWOguWuticKICAgICAgICB9LCB7CiAgICAgICAgICBzc2dzOiAnMTEwa1ZYWFjnur/ot68nLAogICAgICAgICAgZ3RoOiAnMDAxIycsCiAgICAgICAgICBzYmx4OiAn5p2G5aGUJywKICAgICAgICAgIHF4eHo6ICfkuKXph40nLAogICAgICAgICAgZHlkajogJzM1a1YnLAogICAgICAgICAgc2J4aDogJ1hYWOWei+WPtycsCiAgICAgICAgICBzY2NqOiAnWFhY5Y6C5a62JwogICAgICAgIH0sIHsKICAgICAgICAgIHNzZ3M6ICcxMTBrVlhYWOe6v+i3rycsCiAgICAgICAgICBndGg6ICcwMDEjJywKICAgICAgICAgIHNibHg6ICfmnYbloZQnLAogICAgICAgICAgcXh4ejogJ+S4pemHjScsCiAgICAgICAgICBkeWRqOiAnMzVrVicsCiAgICAgICAgICBzYnhoOiAnWFhY5Z6L5Y+3JywKICAgICAgICAgIHNjY2o6ICdYWFjljoLlrrYnCiAgICAgICAgfSwgewogICAgICAgICAgc3NnczogJzExMGtWWFhY57q/6LevJywKICAgICAgICAgIGd0aDogJzAwMSMnLAogICAgICAgICAgc2JseDogJ+adhuWhlCcsCiAgICAgICAgICBxeHh6OiAn5Lil6YeNJywKICAgICAgICAgIGR5ZGo6ICczNWtWJywKICAgICAgICAgIHNieGg6ICdYWFjlnovlj7cnLAogICAgICAgICAgc2NjajogJ1hYWOWOguWuticKICAgICAgICB9LCB7CiAgICAgICAgICBzc2dzOiAnMTEwa1ZYWFjnur/ot68nLAogICAgICAgICAgZ3RoOiAnMDAxIycsCiAgICAgICAgICBzYmx4OiAn5p2G5aGUJywKICAgICAgICAgIHF4eHo6ICfkuKXph40nLAogICAgICAgICAgZHlkajogJzM1a1YnLAogICAgICAgICAgc2J4aDogJ1hYWOWei+WPtycsCiAgICAgICAgICBzY2NqOiAnWFhY5Y6C5a62JwogICAgICAgIH0qLwogICAgICBdLAogICAgICAvL+iuvuWkh+WxpeWOhuivlemqjOiusOW9leaVsOaNrgogICAgICBzYmx2c3lqbExpc3Q6IFsKICAgICAgICAvKiB7CiAgICAgICAgICBzeXp5OiAn5bim55S1JywKICAgICAgICAgIHN5eHo6ICfkvovooYzor5XpqownLAogICAgICAgICAgc3ltYzogJ1hYWFhYJywKICAgICAgICAgIGd6ZGQ6ICdYWFjlubPlj7AnLAogICAgICAgICAgc3lzYjogJ+S4u+WPmOWOi+WZqCcsCiAgICAgICAgICBzeWJnOiAnJywKICAgICAgICAgIHRxOiAn5pm0JywKICAgICAgICAgIHN5cnE6ICcyMDIyLTAxLTAxJywKICAgICAgICAgIGxycjogJ+W8oOS4iScsCiAgICAgICAgICBzeWpsOiAnWFhYWFgnCiAgICAgICAgfSwgewogICAgICAgICAgc3l6eTogJ+W4pueUtScsCiAgICAgICAgICBzeXh6OiAn5L6L6KGM6K+V6aqMJywKICAgICAgICAgIHN5bWM6ICdYWFhYWCcsCiAgICAgICAgICBnemRkOiAnWFhY5bmz5Y+wJywKICAgICAgICAgIHN5c2I6ICfkuLvlj5jljovlmagnLAogICAgICAgICAgc3liZzogJycsCiAgICAgICAgICB0cTogJ+aZtCcsCiAgICAgICAgICBzeXJxOiAnMjAyMi0wMS0wMScsCiAgICAgICAgICBscnI6ICflvKDkuIknLAogICAgICAgICAgc3lqbDogJ1hYWFhYJwogICAgICAgIH0sIHsKICAgICAgICAgIHN5enk6ICfluKbnlLUnLAogICAgICAgICAgc3l4ejogJ+S+i+ihjOivlemqjCcsCiAgICAgICAgICBzeW1jOiAnWFhYWFgnLAogICAgICAgICAgZ3pkZDogJ1hYWOW5s+WPsCcsCiAgICAgICAgICBzeXNiOiAn5Li75Y+Y5Y6L5ZmoJywKICAgICAgICAgIHN5Ymc6ICcnLAogICAgICAgICAgdHE6ICfmmbQnLAogICAgICAgICAgc3lycTogJzIwMjItMDEtMDEnLAogICAgICAgICAgbHJyOiAn5byg5LiJJywKICAgICAgICAgIHN5amw6ICdYWFhYWCcKICAgICAgICB9LCB7CiAgICAgICAgICBzeXp5OiAn5bim55S1JywKICAgICAgICAgIHN5eHo6ICfkvovooYzor5XpqownLAogICAgICAgICAgc3ltYzogJ1hYWFhYJywKICAgICAgICAgIGd6ZGQ6ICdYWFjlubPlj7AnLAogICAgICAgICAgc3lzYjogJ+S4u+WPmOWOi+WZqCcsCiAgICAgICAgICBzeWJnOiAnJywKICAgICAgICAgIHRxOiAn5pm0JywKICAgICAgICAgIHN5cnE6ICcyMDIyLTAxLTAxJywKICAgICAgICAgIGxycjogJ+W8oOS4iScsCiAgICAgICAgICBzeWpsOiAnWFhYWFgnCiAgICAgICAgfSwgewogICAgICAgICAgc3l6eTogJ+W4pueUtScsCiAgICAgICAgICBzeXh6OiAn5L6L6KGM6K+V6aqMJywKICAgICAgICAgIHN5bWM6ICdYWFhYWCcsCiAgICAgICAgICBnemRkOiAnWFhY5bmz5Y+wJywKICAgICAgICAgIHN5c2I6ICfkuLvlj5jljovlmagnLAogICAgICAgICAgc3liZzogJycsCiAgICAgICAgICB0cTogJ+aZtCcsCiAgICAgICAgICBzeXJxOiAnMjAyMi0wMS0wMScsCiAgICAgICAgICBscnI6ICflvKDkuIknLAogICAgICAgICAgc3lqbDogJ1hYWFhYJwogICAgICAgIH0sIHsKICAgICAgICAgIHN5enk6ICfluKbnlLUnLAogICAgICAgICAgc3l4ejogJ+S+i+ihjOivlemqjCcsCiAgICAgICAgICBzeW1jOiAnWFhYWFgnLAogICAgICAgICAgZ3pkZDogJ1hYWOW5s+WPsCcsCiAgICAgICAgICBzeXNiOiAn5Li75Y+Y5Y6L5ZmoJywKICAgICAgICAgIHN5Ymc6ICcnLAogICAgICAgICAgdHE6ICfmmbQnLAogICAgICAgICAgc3lycTogJzIwMjItMDEtMDEnLAogICAgICAgICAgbHJyOiAn5byg5LiJJywKICAgICAgICAgIHN5amw6ICdYWFhYWCcKICAgICAgICB9Ki8KICAgICAgXSwKICAgICAgLy/orr7lpIflsaXljoZ0YWLpobUKICAgICAgc2JsbERlc2NUYWJOYW1lOiAicXhqbCIsCiAgICAgIHNob3c6IGZhbHNlLAogICAgICAvL+iuvuWkh+WfuuacrOS/oeaBr+ihqOWNlQogICAgICBqYnh4Rm9ybTogewogICAgICAgIG9iaklkOiB1bmRlZmluZWQsCiAgICAgICAgc3N4bDogIiIsCiAgICAgICAgc3NndGJoOiAiIgogICAgICB9LAogICAgICAvL+iuvuWkh+ivpuaDhemhteW6lemDqOehruiupOWPlua2iOaMiemSruaOp+WItgogICAgICBzYkNvbW1pdERpYWxvZ0NvdHJvbDogdHJ1ZSwKICAgICAgLy/lvLnlh7rmoYZ0YWLpobUKICAgICAgYWN0aXZlVGFiTmFtZTogInNiRGVzYyIsCiAgICAgIC8v5Y+Y55S156uZ5bGV56S6CiAgICAgIGJkelNob3dUYWJsZTogdHJ1ZSwKICAgICAgLy/pl7TpmpTlsZXnpLoKICAgICAgamdTaG93VGFibGU6IGZhbHNlLAogICAgICAvL+iuvuWkh+WxleekugogICAgICBzYlNob3dUYWJsZTogZmFsc2UsCiAgICAgIC8v6K6+5aSH5by55Ye65qGGCiAgICAgIGRpYWxvZ0Zvcm1WaXNpYmxlOiBmYWxzZSwKICAgICAgLy/lj5jnlLXnq5nmt7vliqDmjInpkq7lvLnlh7rmoYYKICAgICAgYmR6RGlhbG9nRm9ybVZpc2libGU6IGZhbHNlLAogICAgICAvL+mXtOmalOa3u+WKoOaMiemSruW8ueWHuuahhgogICAgICBqZ0RpYWxvZ0Zvcm1WaXNpYmxlOiBmYWxzZSwKICAgICAgLy/lvLnlh7rmoYbooajljZUKICAgICAgZm9ybToge30sCgogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgLy/nu4Tnu4fmoJEKICAgICAgdHJlZU9wdGlvbnM6IFtdLAoKICAgICAgc2VsZWN0Um93czogW10sCiAgICAgIC8v5Y+Y55S156uZ5oyC5o6l5pWw5o2uCiAgICAgIG5ld1Rlc3REYXRhOiBbXSwKCiAgICAgIC8v5Yig6Zmk5piv5ZCm5Y+v55SoCiAgICAgIG11bHRpcGxlU2Vuc29yOiB0cnVlLAogICAgICAvL+afpeivouWPguaVsAogICAgICAvKiAgICAgICBxdWVyeVBhcmFtczogewogICAgICAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICAgICAgIHJvbGVLZXk6ICcnLAogICAgICAgICAgICAgICByb2xlTmFtZTogJycsCiAgICAgICAgICAgICAgIHN0YXR1czogJycsCiAgICAgICAgICAgICB9LCovCiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vc3NndGJoIHNiZG0gc2JtYyBzYmZsYm0gYmdyIHR5cnEgemN4eiB5eHp0IGR5ZGogc2NjaiBjY2JoIGNjcnEgZ2d4aAogICAgICBydWxlczogewogICAgICAgIC8vIHNzZ3RiaDpbe3JlcXVpcmVkOnRydWUsbWVzc2FnZTon6K+36L6T5YWl5omA5bGe5p2G5aGUJyx0cmlnZ2VyOidibHVyJ31dLAogICAgICAgIC8vIHNiZG06W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+i+k+WFpeiuvuWkh+S7o+eggScsdHJpZ2dlcjonYmx1cid9XSwKICAgICAgICBzYm1jOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+i+k+WFpeiuvuWkh+WQjeensCIsIHRyaWdnZXI6ICJibHVyIiB9XSwKICAgICAgICBzYmZsYm06IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6norr7lpIfnsbvlnosiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9CiAgICAgICAgXQogICAgICAgIC8vIGJncjpbe3JlcXVpcmVkOnRydWUsbWVzc2FnZTon6K+36L6T5YWl5L+d566h5Lq6Jyx0cmlnZ2VyOidibHVyJ31dLAogICAgICAgIC8vIHl4enQ6W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+mAieaLqeeKtuaAgScsdHJpZ2dlcjonY2hhbmdlJ31dLAogICAgICAgIC8vIGR5ZGo6W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+mAieaLqeeUteWOi+etiee6pycsdHJpZ2dlcjonY2hhbmdlJ31dLAogICAgICAgIC8vIHNjY2o6W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+i+k+WFpeeUn+S6p+WOguWuticsdHJpZ2dlcjonYmx1cid9XSwKICAgICAgICAvLyBjY2JoOlt7cmVxdWlyZWQ6dHJ1ZSxtZXNzYWdlOifor7fovpPlhaXlh7rlnLrnvJblj7cnLHRyaWdnZXI6J2JsdXInfV0KICAgICAgfSwKCiAgICAgIGpzY3NMYWJlbExpc3Q6IFtdLAogICAgICAvL+aKgOacr+WPguaVsOe7keWumgogICAgICBqc2NzRm9ybToge30sCiAgICAgIHBhcmFtUXVlcnk6IHsKICAgICAgICBzYmx4Ym06IHVuZGVmaW5lZAogICAgICB9LAogICAgICBzYmx4T3B0aW9uc0RhdGFTZWxlY3RlZDoge30sCiAgICAgIHhsdHpEYXRhOiB0cnVlLAogICAgICBzYnR6RGF0YTogZmFsc2UsCiAgICAgIGd0dHpEYXRhOiBmYWxzZSwKICAgICAgdGl0bGU6ICIiLAogICAgICAvL+e6v+i3r+aVsOaNruebuOWFswogICAgICB0YWJsZUFuZFBhZ2VJbmZvMTogewogICAgICAgIHBhZ2VyOiB7CiAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgdG90YWw6IDAsCiAgICAgICAgICBzaXplczogWzEwLCAyMCwgNTAsIDEwMF0KICAgICAgICB9LAogICAgICAgIG9wdGlvbjogewogICAgICAgICAgY2hlY2tCb3g6IHRydWUsCiAgICAgICAgICBzZXJpYWxOdW1iZXI6IHRydWUKICAgICAgICB9LAogICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgdGFibGVIZWFkZXI6IFsKICAgICAgICAgIHsgcHJvcDogImxpbmVOYW1lIiwgbGFiZWw6ICLnur/ot6/lkI3np7AiLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgcHJvcDogImR5ZGoiLCBsYWJlbDogIueUteWOi+etiee6pyIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBwcm9wOiAibGluZVN0YXR1cyIsIGxhYmVsOiAi57q/6Lev54q25oCBIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJsaW5lVHlwZSIsIGxhYmVsOiAi57q/6Lev57G75Z6LIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICAvLyB7cHJvcDogJ3Nmemd4JywgbGFiZWw6ICfmmK/lkKbkuLvlubLnur8nLCBtaW5XaWR0aDogJzE0MCd9LAogICAgICAgICAgeyBwcm9wOiAidG90YWxMZW5ndGgiLCBsYWJlbDogIue6v+i3r+WFqOmVvyhLTSkiLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgcHJvcDogInR5cnEiLCBsYWJlbDogIuaKlei/kOaXpeacnyIsIG1pbldpZHRoOiAiMTIwIiB9CiAgICAgICAgICAvKnsKICAgICAgICAgICAgZml4ZWQ6ICJyaWdodCIsCiAgICAgICAgICAgIHByb3A6ICdvcGVyYXRpb24nLAogICAgICAgICAgICBsYWJlbDogJ+aTjeS9nCcsCiAgICAgICAgICAgIG1pbldpZHRoOiAnMTMwcHgnLAogICAgICAgICAgICBzdHlsZToge2Rpc3BsYXk6ICdibG9jayd9LAogICAgICAgICAgICBvcGVyYXRpb246IFsKICAgICAgICAgICAgICB7bmFtZTogJ+S/ruaUuScsIGNsaWNrRnVuOiB0aGlzLnVwZGF0ZVJvdzF9LAogICAgICAgICAgICAgIHtuYW1lOiAn6K+m5oOFJywgY2xpY2tGdW46IHRoaXMuZGV0YWlsc0luZm8xfQogICAgICAgICAgICBdCiAgICAgICAgICB9LCovCiAgICAgICAgXQogICAgICB9LAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIGR5ZGpibTogIiIsCiAgICAgICAgbGluZU5hbWU6ICIiLAogICAgICAgIGxpbmVUeXBlOiAiIiwKICAgICAgICBsaW5lU3RhdHVzOiAiIgogICAgICB9LAogICAgICAvL+iuvuWkh+W8ueWHuuahhgogICAgICB4bERpYWxvZ0Zvcm1WaXNpYmxlOiBmYWxzZSwKICAgICAgeGxzaG93OiBmYWxzZSwKICAgICAgLy/orr7lpIfln7rmnKzkv6Hmga8KICAgICAgeGxGb3JtOiB7CiAgICAgICAgb2JqSWQ6IHVuZGVmaW5lZAogICAgICB9LAogICAgICAvL+e6v+i3r+exu+WeiwogICAgICBsaW5lVHlwZU9wdGlvbnM6IFsKICAgICAgICB7IGxhYmVsOiAi6L6T55S157q/6LevIiwgdmFsdWU6ICLovpPnlLXnur/ot68iIH0sCiAgICAgICAgeyBsYWJlbDogIumFjeeUtee6v+i3ryIsIHZhbHVlOiAi6YWN55S157q/6LevIiB9CiAgICAgIF0sCiAgICAgIC8v57q/6Lev54q25oCBCiAgICAgIHhsenRPcHRpb25zOiBbCiAgICAgICAgeyBsYWJlbDogIuWcqOi/kCIsIHZhbHVlOiAi5Zyo6L+QIiB9LAogICAgICAgIHsgbGFiZWw6ICLlgZzov5AiLCB2YWx1ZTogIuWBnOi/kCIgfQogICAgICBdLAogICAgICB4bG9wdGlvbnM6IFsKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIjExMCIsCiAgICAgICAgICBsYWJlbDogIjExMGtWIgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdmFsdWU6ICIzNSIsCiAgICAgICAgICBsYWJlbDogIjM1a1YiCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIjEwIiwKICAgICAgICAgIGxhYmVsOiAiMTBrViIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAiNiIsCiAgICAgICAgICBsYWJlbDogIjZrViIKICAgICAgICB9CiAgICAgIF0sCiAgICAgIHhsZmlsdGVySW5mbzogewogICAgICAgIGRhdGE6IHsKICAgICAgICAgIGR5ZGpibTogIiIsCiAgICAgICAgICBsaW5lTmFtZTogIiIsCiAgICAgICAgICBsaW5lVHlwZTogIiIsCiAgICAgICAgICAvLyBzZnpneDogJycsCiAgICAgICAgICBsaW5lU3RhdHVzOiAiIgogICAgICAgIH0sCiAgICAgICAgZmllbGRMaXN0OiBbCiAgICAgICAgICB7IGxhYmVsOiAi57q/6Lev5ZCN56ewIiwgdHlwZTogImlucHV0IiwgdmFsdWU6ICJsaW5lTmFtZSIgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLnur/ot6/lhajplb8iLAogICAgICAgICAgICB0eXBlOiAiaW5wdXQiLAogICAgICAgICAgICB2YWx1ZTogInRvdGFsTGVuZ3RoIiwKICAgICAgICAgICAgb3B0aW9uczogW10KICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi5oqV6L+Q5pel5pyfIiwKICAgICAgICAgICAgdHlwZTogImRhdGUiLAogICAgICAgICAgICBmb3JtYXQ6ICJ5eXl5LU1NLWRkIiwKICAgICAgICAgICAgb3B0aW9uczogW10KICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi55S15Y6L562J57qnIiwKICAgICAgICAgICAgdHlwZTogImNoZWNrYm94IiwKICAgICAgICAgICAgY2hlY2tib3hWYWx1ZTogW10sCiAgICAgICAgICAgIHZhbHVlOiAiZHlkamJtIiwKICAgICAgICAgICAgb3B0aW9uczogWwogICAgICAgICAgICAgIHsgbGFiZWw6ICIxMTBrViIsIHZhbHVlOiAiMTEwIiB9LAogICAgICAgICAgICAgIHsgbGFiZWw6ICIzNWtWIiwgdmFsdWU6ICIzNSIgfSwKICAgICAgICAgICAgICB7IGxhYmVsOiAiMTBrViIsIHZhbHVlOiAiMTAiIH0sCiAgICAgICAgICAgICAgeyBsYWJlbDogIjZrViIsIHZhbHVlOiAiNiIgfQogICAgICAgICAgICBdCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIueKtuaAgSIsCiAgICAgICAgICAgIHR5cGU6ICJjaGVja2JveCIsCiAgICAgICAgICAgIGNoZWNrYm94VmFsdWU6IFtdLAogICAgICAgICAgICB2YWx1ZTogImxpbmVTdGF0dXMiLAogICAgICAgICAgICBvcHRpb25zOiBbCiAgICAgICAgICAgICAgeyBsYWJlbDogIuWcqOi/kCIsIHZhbHVlOiAi5Zyo6L+QIiB9LAogICAgICAgICAgICAgIHsgbGFiZWw6ICLlgZzov5AiLCB2YWx1ZTogIuWBnOi/kCIgfQogICAgICAgICAgICBdCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIue6v+i3r+exu+WeiyIsCiAgICAgICAgICAgIHR5cGU6ICJjaGVja2JveCIsCiAgICAgICAgICAgIGNoZWNrYm94VmFsdWU6IFtdLAogICAgICAgICAgICB2YWx1ZTogImxpbmVUeXBlIiwKICAgICAgICAgICAgb3B0aW9uczogWwogICAgICAgICAgICAgIHsgbGFiZWw6ICLovpPnlLXnur/ot68iLCB2YWx1ZTogIui+k+eUtee6v+i3ryIgfSwKICAgICAgICAgICAgICB7IGxhYmVsOiAi6YWN55S157q/6LevIiwgdmFsdWU6ICLphY3nlLXnur/ot68iIH0KICAgICAgICAgICAgXQogICAgICAgICAgfQogICAgICAgICAgLy8gewogICAgICAgICAgLy8gICBsYWJlbDogJ+aYr+WQpuS4u+W5sue6vycsCiAgICAgICAgICAvLyAgIHR5cGU6ICdzZWxlY3QnLAogICAgICAgICAgLy8gICB2YWx1ZTogJ3Nmemd4JywKICAgICAgICAgIC8vICAgb3B0aW9uczogW3tsYWJlbDogIuaYryIsIHZhbHVlOiAi5pivIn0sIHtsYWJlbDogIuWQpiIsIHZhbHVlOiAi5ZCmIn0sXQogICAgICAgICAgLy8gfSwKICAgICAgICBdCiAgICAgIH0sCiAgICAgIC8v5p+l6K+i5p2G5aGU5Y+C5pWwICAg5p2G5aGU5pWw5o2u55u45YWz5byA5aeLCiAgICAgIC8v5p2G5aGU6K+m5oOF5by55Ye65qGGCiAgICAgIGd0RGlhbG9nRm9ybVZpc2libGU6IGZhbHNlLAogICAgICAvL+W8ueWHuuahhnRhYumhtQogICAgICBndGFjdGl2ZVRhYk5hbWU6ICJzYkRlc2MiLAogICAgICAvL+i9ruaSreWbvueJhwogICAgICBpbWdMaXN0OiBbXSwKICAgICAgeGxJbWdMaXN0OiBbCiAgICAgICAgLy8gICAgICAgICB7CiAgICAgICAgLy8gICBmaWxlVXJsOiByZXF1aXJlKCdAL2Fzc2V0cy9pbWFnZS9iZHoucG5nJykKICAgICAgICAvLyB9LAogICAgICAgIC8vIHsKICAgICAgICAvLyAgIGZpbGVVcmw6IHJlcXVpcmUoJ0AvYXNzZXRzL2ltYWdlL2Jkei5wbmcnKQogICAgICAgIC8vIH0sCiAgICAgICAgLy8gewogICAgICAgIC8vICAgZmlsZVVybDogcmVxdWlyZSgnQC9hc3NldHMvaW1hZ2UvYmR6LnBuZycpCiAgICAgICAgLy8gfQogICAgICBdLAogICAgICBxdWVyeUd0UGFyYW06IHsKICAgICAgICBzYnp0OiAiIiwKICAgICAgICBsaW5lTmFtZTogIiIsCiAgICAgICAgZHlkajogIiIsCiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgLy8gbXlTb3J0czogW3sgcHJvcDogImdldF9udW1iZXIoZ3RiaCkgKyAxIiwgYXNjOiB0cnVlIH1dCiAgICAgIH0sCiAgICAgIC8vIOaWh+S7tuS4iuS8oOaVsOaNrgogICAgICAvLyB1cGxvYWREYXRhOiB7CiAgICAgIC8vICAgdHlwZTogIiIsCiAgICAgIC8vICAgYnVzaW5lc3NJZDogdW5kZWZpbmVkLAogICAgICAvLyAgIGxpbmVOYW1lIDogdGhpcy5saW5lTmFtZQogICAgICAvLyB9LAogICAgICAvLyDmlofku7bkuIrkvKDor7fmsYLlpLQKICAgICAgdXBIZWFkZXI6IHsgdG9rZW46IGdldFRva2VuKCkgfSwKICAgICAgLy8gIOaWh+S7tuWvvOWFpeW8ueWHuuahhuebuOWFswogICAgICBFeGNlbEltcG9ydFRpdGxlOiAiRXhjZWzlr7zlhaUiLAogICAgICBvcGVuRXhjZWxEaWFsb2c6IGZhbHNlLAogICAgICAvLyDmlofku7bkuIrkvKDor7fmsYLlpLQKICAgICAgLy/mlofku7blkI0KICAgICAgZmlsZU5hbWU6ICIiLAogICAgICAvL+S4iuS8oOW+l+aWh+S7tuaVsOe7hAogICAgICBmaWxlTGlzdDogW10sCiAgICAgIGlzbG9hZGluZzogZmFsc2UsCiAgICAgIHRhYmxlQW5kUGFnZUluZm8yOiB7CiAgICAgICAgcGFnZXI6IHsKICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgIHNpemVzOiBbMTAsIDIwLCA1MCwgMTAwXQogICAgICAgIH0sCiAgICAgICAgb3B0aW9uOiB7CiAgICAgICAgICBjaGVja0JveDogdHJ1ZSwKICAgICAgICAgIHNlcmlhbE51bWJlcjogdHJ1ZQogICAgICAgIH0sCiAgICAgICAgdGFibGVEYXRhOiBbXSwKICAgICAgICB0YWJsZUhlYWRlcjogWwogICAgICAgICAgeyBwcm9wOiAibGluZU5hbWUiLCBsYWJlbDogIuaJgOWxnue6v+i3ryIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBwcm9wOiAiZ3RtYyIsIGxhYmVsOiAi5p2G5aGU5ZCN56ewIiwgbWluV2lkdGg6ICIxMzAiIH0sCiAgICAgICAgICB7IHByb3A6ICJndGJoIiwgbGFiZWw6ICLmnYbloZTnvJblj7ciLCBtaW5XaWR0aDogIjkwIiwgIH0sCiAgICAgICAgICB7IHByb3A6ICJkeWRqIiwgbGFiZWw6ICLnlLXljovnrYnnuqciLCBtaW5XaWR0aDogIjYwIiB9LAogICAgICAgICAgeyBwcm9wOiAic2J6dCIsIGxhYmVsOiAi6K6+5aSH54q25oCBIiwgbWluV2lkdGg6ICI2MCIgfSwKICAgICAgICAgIHsgcHJvcDogImd0Y3oiLCBsYWJlbDogIuadhuWhlOadkOi0qCIsIG1pbldpZHRoOiAiNzAiIH0sCiAgICAgICAgICB7IHByb3A6ICJ0eXJxIiwgbGFiZWw6ICLmipXov5Dml6XmnJ8iLCBtaW5XaWR0aDogIjgwIiB9LAogICAgICAgICAgeyBwcm9wOiAiZ3RudW0iLCBsYWJlbDogIuadhuWhlOaOkuW6jyIsIG1pbldpZHRoOiAiNzAiLCAgfQogICAgICAgICAgLyp7CiAgICAgICAgICAgIGZpeGVkOiAncmlnaHQnLAogICAgICAgICAgICBwcm9wOiAnb3BlcmF0aW9uJywKICAgICAgICAgICAgbGFiZWw6ICfmk43kvZwnLAogICAgICAgICAgICBtaW5XaWR0aDogJzEzMHB4JywKICAgICAgICAgICAgc3R5bGU6IHsgZGlzcGxheTogJ2Jsb2NrJyB9LAogICAgICAgICAgICBvcGVyYXRpb246IFsKICAgICAgICAgICAgICB7IG5hbWU6ICfnirbmgIHlj5jmm7QnLCBjbGlja0Z1bjogdGhpcy51cGRhdGVTdGF0dXMgfSwKICAgICAgICAgICAgICB7IG5hbWU6ICfkv67mlLknLCBjbGlja0Z1bjogdGhpcy51cGRhdGVSb3cyIH0sCiAgICAgICAgICAgICAgeyBuYW1lOiAn6K+m5oOFJywgY2xpY2tGdW46IHRoaXMuZGV0YWlsc0luZm8yIH0KICAgICAgICAgICAgXQogICAgICAgICAgfSovCiAgICAgICAgXQogICAgICB9LAogICAgICAvL+iuvuWkh+WfuuacrOS/oeaBrwogICAgICBndEZvcm06IHsKICAgICAgICBhdHRhY2htZW50OiBbXSwKICAgICAgICBvYmpJZDogdW5kZWZpbmVkLAogICAgICAgIGd0Ymg6ICIiLAogICAgICAgIHNzYm06ICLnur/ot6/liIblhazlj7giCiAgICAgIH0sCiAgICAgIGd0ZmlsdGVySW5mbzogewogICAgICAgIGRhdGE6IHsKICAgICAgICAgIHNienQ6ICIiLAogICAgICAgICAgbGluZU5hbWU6ICIiLAogICAgICAgICAgZHlkajogIiIKICAgICAgICB9LAogICAgICAgIGZpZWxkTGlzdDogWwogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIuadhuWhlOWQjeensCIsCiAgICAgICAgICAgIHR5cGU6ICJpbnB1dCIsCiAgICAgICAgICAgIHZhbHVlOiAiZ3RtYyIsCiAgICAgICAgICAgIG9wdGlvbnM6IFtdCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIuadhuWhlOe8luWPtyIsCiAgICAgICAgICAgIHR5cGU6ICJpbnB1dCIsCiAgICAgICAgICAgIHZhbHVlOiAiZ3RiaCIsCiAgICAgICAgICAgIG9wdGlvbnM6IFtdCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIueUteWOi+etiee6pyIsCiAgICAgICAgICAgIHR5cGU6ICJzZWxlY3QiLAogICAgICAgICAgICB2YWx1ZTogImR5ZGoiLAogICAgICAgICAgICBvcHRpb25zOiBbCiAgICAgICAgICAgICAgeyBsYWJlbDogIjZrViIsIHZhbHVlOiAiNmtWIiB9LAogICAgICAgICAgICAgIHsgbGFiZWw6ICIxMGtWIiwgdmFsdWU6ICIxMGtWIiB9LAogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIGxhYmVsOiAiMzVrViIsCiAgICAgICAgICAgICAgICB2YWx1ZTogIjM1a1YiCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICB7IGxhYmVsOiAiMTEwa1YiLCB2YWx1ZTogIjExMGtWIiB9CiAgICAgICAgICAgIF0KICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi6K6+5aSH54q25oCBIiwKICAgICAgICAgICAgdHlwZTogInNlbGVjdCIsCiAgICAgICAgICAgIHZhbHVlOiAic2J6dCIsCiAgICAgICAgICAgIG9wdGlvbnM6IFsKICAgICAgICAgICAgICB7IGxhYmVsOiAi5Zyo6L+QIiwgdmFsdWU6ICLlnKjov5AiIH0sCiAgICAgICAgICAgICAgeyBsYWJlbDogIuWBnOi/kCIsIHZhbHVlOiAi5YGc6L+QIiB9CiAgICAgICAgICAgIF0KICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi5p2G5aGU5p2Q6LSoIiwKICAgICAgICAgICAgdHlwZTogImlucHV0IiwKICAgICAgICAgICAgdmFsdWU6ICJndGN6IiwKICAgICAgICAgICAgb3B0aW9uczogW10KICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi5oqV6L+Q5pel5pyfIiwKICAgICAgICAgICAgdHlwZTogImRhdGUiLAogICAgICAgICAgICB2YWx1ZTogInR5cnEiLAogICAgICAgICAgICBmb3JtYXQ6ICJ5eXl5LU1NLWRkIgogICAgICAgICAgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgZ3RzaG93OiBmYWxzZSwKICAgICAgZ3RvcHRpb25zOiBbXSwKICAgICAgZ3RzYnp0OiBbXSwKICAgICAgLy/mnYbloZTmgKfotKgKICAgICAgZ3R4ekxpc3Q6IFtdLAogICAgICAvL+adhuWhlOW9oueKtue7k+WQiAogICAgICBndHh6T3B0aW9uczogW10sCiAgICAgIHVwbG9hZERhdGE6IHsKICAgICAgICBidXNpbmVzc0lkOiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgZ3RyZXN1bWVRdWVyeTogewogICAgICAgIGZvcmVpZ25OdW06IHVuZGVmaW5lZCwKICAgICAgICBzYmx4OiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgZ3RyZXN1bVBhZ2VJbmZvOiB7CiAgICAgICAgcGFnZXI6IHsKICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgIHNpemVzOiBbMTAsIDIwLCA1MCwgMTAwXQogICAgICAgIH0sCiAgICAgICAgb3B0aW9uOiB7CiAgICAgICAgICBjaGVja0JveDogdHJ1ZSwKICAgICAgICAgIHNlcmlhbE51bWJlcjogdHJ1ZQogICAgICAgIH0sCiAgICAgICAgdGFibGVEYXRhOiBbXSwKICAgICAgICB0YWJsZUhlYWRlcjogWwogICAgICAgICAgeyBwcm9wOiAiZm9yZWlnbk51bSIsIGxhYmVsOiAi6K6+5aSH5ZCN56ewIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJzYmx4IiwgbGFiZWw6ICLorr7lpIfnsbvlnosiLCBtaW5XaWR0aDogIjE4MCIgfSwKICAgICAgICAgIHsgcHJvcDogImJnbHgiLCBsYWJlbDogIuWPmOabtOexu+WeiyIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBwcm9wOiAibXMiLCBsYWJlbDogIuaPj+i/sCIsIG1pbldpZHRoOiAiMjUwIiB9LAogICAgICAgICAgeyBwcm9wOiAiYmdyIiwgbGFiZWw6ICLlj5jmm7TkuroiLCBtaW5XaWR0aDogIjE0MCIgfSwKICAgICAgICAgIHsgcHJvcDogImJnc2oiLCBsYWJlbDogIuWPmOabtOaXtumXtCIsIG1pbldpZHRoOiAiMTQwIiB9CiAgICAgICAgXQogICAgICB9LAogICAgICB4bFJ1bGVzOiB7CiAgICAgICAgeGxibTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIue6v+i3r+e8lueggeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBsaW5lTmFtZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIue6v+i3r+WQjeensOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBsaW5lVHlwZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIue6v+i3r+S4k+S4muS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJzZWxlY3QiIH0KICAgICAgICBdLAogICAgICAgIGR5ZGpibTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIueUteWOi+etiee6p+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJzZWxlY3QiIH0KICAgICAgICBdLAogICAgICAgIHRvdGFsTGVuZ3RoOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi57q/6Lev5YWo6ZW/5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIGxpbmVTdGF0dXM6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnur/ot6/nirbmgIHkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAic2VsZWN0IiB9CiAgICAgICAgXQogICAgICB9LAogICAgICBndFJ1bGVzOiB7CiAgICAgICAgZ3RiaDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuadhuWhlOe8luWPt+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBndG51bTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuadhuWhlOaOkuW6j+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBndG1jOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5p2G5aGU5ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIGxpbmVOYW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5omA5bGe57q/6Lev5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIGd0Y3o6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmnYbloZTmnZDotKjkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAic2VsZWN0IiB9CiAgICAgICAgXSwKICAgICAgICB5eGJ6OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6L+Q6KGM54+t57uE5LiN6IO95Li656m6IiwgdHJpZ2dlcjogInNlbGVjdCIgfQogICAgICAgIF0sCiAgICAgICAgc3NibTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaJgOWxnumDqOmXqOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBkeWRqOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi55S15Y6L562J57qn5LiN6IO95Li656m6IiwgdHJpZ2dlcjogInNlbGVjdCIgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgZHlkamJtOiAiIiwKICAgICAgbGluZU5hbWU6ICIiLAogICAgICBsaW5lZElkU3RvcmU6ICIiLAogICAgICBndGJoU3RvcmU6ICIiLAogICAgICBndG1jU3RvcmU6ICIiLAogICAgICBhZGRUb3dlckJhdGNoRm9ybTogewogICAgICAgIHRvd2VyTmFtZVByZWZpeDogIiIsCiAgICAgICAgdG93ZXJOYW1lU3VmZml4OiAiIiwKICAgICAgICB0b3dlck5hbWVMaW5rRmxhZzogIiIsCiAgICAgICAgdG93ZXJOdW1iZXJQcmVmaXg6ICIiLAogICAgICAgIHRvd2VyTnVtYmVyU3VmZml4OiAiIiwKICAgICAgICB0b3dlclN0YXJ0TnVtOiAxCiAgICAgIH0sCiAgICAgIGFkZFRvd2VyQmF0Y2hEaWFsb2dGb3JtVmlzaWJsZTogZmFsc2UsCiAgICAgIGFkZFRvd2VyQmF0Y2hSdWxlczogewogICAgICAgIGxpbmVOYW1lOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5omA5bGe57q/6Lev5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHRvd2VyTmFtZVByZWZpeDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICB0b3dlck5hbWVTdWZmaXg6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgdG93ZXJTdGFydE51bTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0KICAgICAgICBdLAogICAgICAgIHRvd2VyRW5kTnVtOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCB0cmlnZ2VyOiAiY2hhbmdlIiwgdmFsaWRhdG9yOiB2YWxpZGF0ZVRvd2VyRW5kTnVtIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIHhsRGF0YTp7fSwvL+S8oOe7meaUr+e6v+e7tOaKpOmhtemdoueahOe6v+i3r+aVsOaNrgogICAgICBzaG93Wnh3aDpmYWxzZSwvL+aYr+WQpuaYvuekuuaUr+e6v+e7tOaKpOW8ueahhgogICAgICBndGN6T3B0aW9uczpbXSwvL+adhuWhlOadkOi0qOS4i+aLieahhgogICAgICBndHhzT3B0aW9uczpbXSwvL+adhuWhlOW9ouW8j+S4i+aLieahhgogICAgICB4eE9wdGlvbnM6W10sLy/nm7jluo/kuIvmi4nmoYYKICAgICAgZHhwbE9wdGlvbnM6W10sLy/lr7znur/mjpLliJfkuIvmi4nmoYYKICAgICAgc2ZPcHRpb25zOltdLC8v5pivL+WQpgogICAgICBqZHRjbE9wdGlvbnM6W10sLy/mjqXlnLDkvZPmnZDmlpkKICAgIH07CiAgfSwKICB3YXRjaDoge30sCiAgY29tcHV0ZWQ6IHsKICAgIHRvd2VyTnVtYmVyRXhhbXBsZTogZnVuY3Rpb24oKSB7CiAgICAgIHJldHVybiAoCiAgICAgICAgdGhpcy5hZGRUb3dlckJhdGNoRm9ybS50b3dlck51bWJlclByZWZpeCArCiAgICAgICAgdGhpcy5hZGRUb3dlckJhdGNoRm9ybS50b3dlclN0YXJ0TnVtICsKICAgICAgICB0aGlzLmFkZFRvd2VyQmF0Y2hGb3JtLnRvd2VyTnVtYmVyU3VmZml4CiAgICAgICk7CiAgICB9LAogICAgdG93ZXJOYW1lRXhhbXBsZTogZnVuY3Rpb24oKSB7CiAgICAgIHJldHVybiAoCiAgICAgICAgdGhpcy5hZGRUb3dlckJhdGNoRm9ybS50b3dlck5hbWVQcmVmaXggKwogICAgICAgIHRoaXMuYWRkVG93ZXJCYXRjaEZvcm0udG93ZXJTdGFydE51bSArCiAgICAgICAgdGhpcy5hZGRUb3dlckJhdGNoRm9ybS50b3dlck5hbWVMaW5rRmxhZyArCiAgICAgICAgdGhpcy5hZGRUb3dlckJhdGNoRm9ybS50b3dlck5hbWVTdWZmaXgKICAgICAgKTsKICAgIH0KICB9LAogIGNyZWF0ZWQoKSB7CiAgICAvL+WIneWni+WMluWKoOi9veaXtuWKoOi9veaJgOacieWPmOeUteermeS/oeaBrwogICAgdGhpcy50cmVlTGlzdCgpOwogICAgdGhpcy5nZXRTYmx4RGF0YUxpc3RTZWxlY3RlZCgpOwogICAgdGhpcy5saW5lVHpEYXRhKHRoaXMuJHJvdXRlLnF1ZXJ5KTsgLy/liJ3lp4vor7fmsYLnur/ot6/lj7DotKYKICAgIHRoaXMuZ2V0T3B0aW9ucygpOy8v6I635Y+W5LiL5ouJ5qGG5a2X5YW4CiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy5nZXRPcmdhbml6YXRpb25TZWxlY3RlZCgpOwogIH0sCiAgbWV0aG9kczogewogICAgYXN5bmMgZ2V0T3B0aW9ucygpewogICAgICBhd2FpdCB0aGlzLmdldER5ZGpMaXN0KCk7Ly/nlLXljovnrYnnuqcKICAgICAgYXdhaXQgdGhpcy5nZXRHdGN6TGlzdCgpOy8v5p2G5aGU5p2Q6LSoCiAgICAgIGF3YWl0IHRoaXMuZ2V0R3R4c0xpc3QoKTsvL+adhuWhlOW9ouW8jwogICAgICBhd2FpdCB0aGlzLmdldEd0eHpMaXN0KCk7Ly/mnYbloZTmgKfotKgKICAgICAgYXdhaXQgdGhpcy5nZXRHdHh6TGlzdDEoKTsvL+adhuWhlOW9oueKtgogICAgICBhd2FpdCB0aGlzLmdldEd0eHhMaXN0KCk7Ly/mnYbloZTnm7jluo8KICAgICAgYXdhaXQgdGhpcy5nZXREeHBsTGlzdCgpOy8v5a+857q/5o6S5YiXCiAgICAgIGF3YWl0IHRoaXMuZ2V0U2ZMaXN0KCk7Ly/mmK8v5ZCmCiAgICAgIGF3YWl0IHRoaXMuZ2V0SmR0Y2xMaXN0KCk7Ly/mjqXlnLDkvZPmnZDmlpkKICAgICAgYXdhaXQgdGhpcy5nZXRHdHp0TGlzdCgpOy8v5p2G5aGU54q25oCBCiAgICB9LAogICAgZ2V0RHlkakxpc3QoKXsKICAgICAgZ2V0RGljdFR5cGVEYXRhKCdndHR6LWR5ZGonKS50aGVuKHJlcz0+ewogICAgICAgIHJlcy5kYXRhLmZvckVhY2goaXRlbT0+ewogICAgICAgICAgdGhpcy5ndG9wdGlvbnMucHVzaCh7bGFiZWw6aXRlbS5sYWJlbCx2YWx1ZTppdGVtLnZhbHVlfSkKICAgICAgICB9KQogICAgICB9KQogICAgfSwKICAgIGdldEd0Y3pMaXN0KCl7CiAgICAgIGdldERpY3RUeXBlRGF0YSgnZ3R0ei1ndGN6JykudGhlbihyZXM9PnsKICAgICAgICByZXMuZGF0YS5mb3JFYWNoKGl0ZW09PnsKICAgICAgICAgIHRoaXMuZ3Rjek9wdGlvbnMucHVzaCh7bGFiZWw6aXRlbS5sYWJlbCx2YWx1ZTppdGVtLnZhbHVlfSkKICAgICAgICB9KQogICAgICB9KQogICAgfSwKICAgIGdldEd0eHNMaXN0KCl7CiAgICAgIGdldERpY3RUeXBlRGF0YSgnZ3R0ei1ndHhzJykudGhlbihyZXM9PnsKICAgICAgICByZXMuZGF0YS5mb3JFYWNoKGl0ZW09PnsKICAgICAgICAgIHRoaXMuZ3R4c09wdGlvbnMucHVzaCh7bGFiZWw6aXRlbS5sYWJlbCx2YWx1ZTppdGVtLnZhbHVlfSkKICAgICAgICB9KQogICAgICB9KQogICAgfSwKICAgIGdldEd0eHpMaXN0KCl7CiAgICAgIGdldERpY3RUeXBlRGF0YSgnZ3R0ei1ndHh6JykudGhlbihyZXM9PnsKICAgICAgICByZXMuZGF0YS5mb3JFYWNoKGl0ZW09PnsKICAgICAgICAgIHRoaXMuZ3R4ekxpc3QucHVzaCh7bGFiZWw6aXRlbS5sYWJlbCx2YWx1ZTppdGVtLnZhbHVlfSkKICAgICAgICB9KQogICAgICB9KQogICAgfSwKICAgIGdldEd0eHpMaXN0MSgpewogICAgICBnZXREaWN0VHlwZURhdGEoJ2d0dHotZ3R4ejEnKS50aGVuKHJlcz0+ewogICAgICAgIHJlcy5kYXRhLmZvckVhY2goaXRlbT0+ewogICAgICAgICAgdGhpcy5ndHh6T3B0aW9ucy5wdXNoKHtsYWJlbDppdGVtLmxhYmVsLHZhbHVlOml0ZW0udmFsdWV9KQogICAgICAgIH0pCiAgICAgIH0pCiAgICB9LAogICAgZ2V0R3R4eExpc3QoKXsKICAgICAgZ2V0RGljdFR5cGVEYXRhKCdndHR6LWd0eHgnKS50aGVuKHJlcz0+ewogICAgICAgIHJlcy5kYXRhLmZvckVhY2goaXRlbT0+ewogICAgICAgICAgdGhpcy54eE9wdGlvbnMucHVzaCh7bGFiZWw6aXRlbS5sYWJlbCx2YWx1ZTppdGVtLnZhbHVlfSkKICAgICAgICB9KQogICAgICB9KQogICAgfSwKICAgIGdldER4cGxMaXN0KCl7CiAgICAgIGdldERpY3RUeXBlRGF0YSgnZ3R0ei1keHBsJykudGhlbihyZXM9PnsKICAgICAgICByZXMuZGF0YS5mb3JFYWNoKGl0ZW09PnsKICAgICAgICAgIHRoaXMuZHhwbE9wdGlvbnMucHVzaCh7bGFiZWw6aXRlbS5sYWJlbCx2YWx1ZTppdGVtLnZhbHVlfSkKICAgICAgICB9KQogICAgICB9KQogICAgfSwKICAgIGdldFNmTGlzdCgpewogICAgICBnZXREaWN0VHlwZURhdGEoJ3N5c19zZicpLnRoZW4ocmVzPT57CiAgICAgICAgcmVzLmRhdGEuZm9yRWFjaChpdGVtPT57CiAgICAgICAgICB0aGlzLnNmT3B0aW9ucy5wdXNoKHtsYWJlbDppdGVtLmxhYmVsLHZhbHVlOml0ZW0udmFsdWV9KQogICAgICAgIH0pCiAgICAgIH0pCiAgICB9LAogICAgZ2V0SmR0Y2xMaXN0KCl7CiAgICAgIGdldERpY3RUeXBlRGF0YSgnZ3R0ei1qZHRjbCcpLnRoZW4ocmVzPT57CiAgICAgICAgcmVzLmRhdGEuZm9yRWFjaChpdGVtPT57CiAgICAgICAgICB0aGlzLmpkdGNsT3B0aW9ucy5wdXNoKHtsYWJlbDppdGVtLmxhYmVsLHZhbHVlOml0ZW0udmFsdWV9KQogICAgICAgIH0pCiAgICAgIH0pCiAgICB9LAogICAgZ2V0R3R6dExpc3QoKXsKICAgICAgZ2V0RGljdFR5cGVEYXRhKCdndHR6LWd0enQnKS50aGVuKHJlcz0+ewogICAgICAgIHJlcy5kYXRhLmZvckVhY2goaXRlbT0+ewogICAgICAgICAgdGhpcy5ndHNienQucHVzaCh7bGFiZWw6aXRlbS5sYWJlbCx2YWx1ZTppdGVtLnZhbHVlfSkKICAgICAgICB9KQogICAgICB9KQogICAgfSwKICAgIC8v5YWz6Zet5pSv57q/57u05oqk5by55qGGCiAgICBjbG9zZVp4d2hGdW4oKXsKICAgICAgdGhpcy54bERhdGEgPSB7fTsKICAgICAgdGhpcy5zaG93Wnh3aCA9IGZhbHNlOwogICAgfSwKICAgIC8v5pSv57q/57u05oqk5pa55rOVCiAgICB6eHdoRnVuKHJvdyl7CiAgICAgIHRoaXMueGxEYXRhID0gey4uLnJvd307CiAgICAgIHRoaXMuc2hvd1p4d2ggPSB0cnVlOwogICAgfSwKICAgIGdldFZvbHRhZ2VMZVZlbExpc3QoKSB7CiAgICAgIHRoaXMuc2JmaWx0ZXJJbmZvLmZpZWxkTGlzdC5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgIGlmIChpdGVtLnZhbHVlID09ICJzc2dzIikgewogICAgICAgICAgaXRlbS5vcHRpb25zID0gWwogICAgICAgICAgICB7IGxhYmVsOiAiMTEwa1YiLCB2YWx1ZTogIjExMGtWIiB9LAogICAgICAgICAgICB7IGxhYmVsOiAiMzVrViIsIHZhbHVlOiAiMzVrViIgfSwKICAgICAgICAgICAgeyBsYWJlbDogIjEwa1YiLCB2YWx1ZTogIjEwa1YiIH0sCiAgICAgICAgICAgIHsgbGFiZWw6ICI2a1YiLCB2YWx1ZTogIjZrViIgfQogICAgICAgICAgXTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8v6I635Y+W54+t57uECiAgICBnZXRPcmdhbml6YXRpb25TZWxlY3RlZCgpIHsKICAgICAgbGV0IHBhcmVudElkID0gIjMwMTAiOyAvL+e6v+i3r+WIhuWFrOWPuAogICAgICBnZXRPcmdhbml6YXRpb25TZWxlY3RlZCh7IHBhcmVudElkOiBwYXJlbnRJZCB9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgcmVzLmRhdGEuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIHRoaXMuZGV2aWNlTmFtZU9wdGlvbnMucHVzaCh7CiAgICAgICAgICAgIGxhYmVsOiBpdGVtLmxhYmVsLAogICAgICAgICAgICB2YWx1ZTogaXRlbS52YWx1ZS50b1N0cmluZygpCiAgICAgICAgICB9KTsKICAgICAgICB9KTsKICAgICAgICB0aGlzLnNiZmlsdGVySW5mby5maWVsZExpc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIGlmIChpdGVtLnZhbHVlID09PSAiYnoiKSB7CiAgICAgICAgICAgIGl0ZW0ub3B0aW9ucyA9IHRoaXMuZGV2aWNlTmFtZU9wdGlvbnM7CiAgICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgLy/kv53lrZjorr7lpIfln7rmnKzkv6Hmga8KICAgIGFkZEVxdWlwSW5mbzogZnVuY3Rpb24oKSB7CiAgICAgIHRoaXMuamJ4eEZvcm0uc2JDbGFzc0NzVmFsdWUgPSB0aGlzLmpzY3NGb3JtOwoKICAgICAgc2F2ZU9yVXBkYXRlKHRoaXMuamJ4eEZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICBpZiAocmVzLmNvZGUgPT0gIjAwMDAiKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKnyIpOwogICAgICAgICAgdGhpcy5zYkRpYWxvZ0Zvcm1WaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICB0aGlzLmdldERhdGEoeyBzc2d0Ymg6IHRoaXMuamJ4eEZvcm0uc3NndGJoIH0pOwogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuaTjeS9nOWksei0pe+8gSIpOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy/liJ3lp4vov5vmnaXlj5Hor7fmsYLnur/ot6/lj7DotKbmjqXlj6MKICAgIGxpbmVUekRhdGEocGFyYW1zKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMgPSB7IC4uLnRoaXMucXVlcnlQYXJhbXMsIC4uLnBhcmFtcyB9OwogICAgICBjb25zdCBwYXJhbSA9IHsgLi4udGhpcy5xdWVyeVBhcmFtcywgLi4ucGFyYW1zIH07CiAgICAgIGdldExpc3R4bChwYXJhbSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMueGx0ekRhdGEgPSB0cnVlOwogICAgICAgIHRoaXMuZ3R0ekRhdGEgPSB0aGlzLnNidHpEYXRhID0gZmFsc2U7CiAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvMS50YWJsZURhdGEgPSByZXMuZGF0YS5yZWNvcmRzOwogICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mbzEucGFnZXIudG90YWwgPSByZXMuZGF0YS50b3RhbDsKICAgICAgfSk7CiAgICB9LAogICAgLy/lm77nurjmn6Xor6IKICAgIHF1ZXJ5U2VhcmNoKHF1ZXJ5U3RyaW5nLCBjYikgewogICAgICBsZXQgcGFyYW0gPSB7CiAgICAgICAgLy8gd2piaDogcXVlcnlTdHJpbmcsCiAgICAgICAgd2ptYzogcXVlcnlTdHJpbmcsCiAgICAgICAgZHlkajogdGhpcy54bEZvcm0uZHlkamJtLAogICAgICAgIGlzbGFzdDogIjEiLAogICAgICAgIHdqbHg6ICIxIiwKICAgICAgICBteVNvcnRzOiBbeyBwcm9wOiAidXBkYXRlVGltZSIsIGFzYzogZmFsc2UgfV0KICAgICAgfTsKICAgICAgZ2V0TGlzdFRaKHBhcmFtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIC8vIOiwg+eUqCBjYWxsYmFjayDov5Tlm57lu7rorq7liJfooajnmoTmlbDmja4KICAgICAgICAgIGxldCBkYXRhID0gcmVzLmRhdGEucmVjb3JkczsKICAgICAgICAgIGRhdGEuZm9yRWFjaChyZWNvcmQgPT4gewogICAgICAgICAgICByZWNvcmQudmFsdWUgPSByZWNvcmQud2piaDsKICAgICAgICAgIH0pOwogICAgICAgICAgY2IoZGF0YSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVTZWxlY3QoaXRlbSkgewogICAgICB0aGlzLnhsSW1nTGlzdCA9IGl0ZW0uZmlsZUxpc3QuZmlsdGVyKAogICAgICAgIHJlY29yZCA9PiByZWNvcmQuZmlsZVR5cGUgIT09ICJ2c2QiCiAgICAgICk7CiAgICB9LAogICAgLy/mnYbloZTlj7DotKYKICAgIGFzeW5jIGd0VHpEYXRhKHBhcmFtcykgewogICAgICB0cnkgewogICAgICAgIHRoaXMucXVlcnlHdFBhcmFtID0gewogICAgICAgICAgLi4udGhpcy5xdWVyeUd0UGFyYW0sCiAgICAgICAgICAuLi5wYXJhbXMsCiAgICAgICAgICAuLi57IGxpbmVOYW1lOiB0aGlzLmxpbmVOYW1lIH0KICAgICAgICB9OwogICAgICAgIGdldExpc3RndCh0aGlzLnF1ZXJ5R3RQYXJhbSkudGhlbihyZXMgPT4gewogICAgICAgICAgdGhpcy5ndHR6RGF0YSA9IHRydWU7CiAgICAgICAgICB0aGlzLnhsdHpEYXRhID0gdGhpcy5zYnR6RGF0YSA9IGZhbHNlOwogICAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvMi50YWJsZURhdGEgPSByZXMuZGF0YS5yZWNvcmRzOwogICAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvMi5wYWdlci50b3RhbCA9IHJlcy5kYXRhLnRvdGFsOwogICAgICAgIH0pOwogICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgY29uc29sZS5sb2coZSk7CiAgICAgIH0KICAgIH0sCiAgICBzb3J0Q2hhbmdlVG93ZXJEYXRhKHsgY29sdW1uLCBwcm9wLCBvcmRlciB9KSB7CiAgICAgIGlmIChvcmRlcikgewogICAgICAgIGlmIChvcmRlci5pbmRleE9mKCJkZXNjIikgPiAtMSkgewogICAgICAgICAgaWYgKHByb3AgPT09ICJndGJoIikgewogICAgICAgICAgICB0aGlzLnF1ZXJ5R3RQYXJhbS5teVNvcnRzID0gWwogICAgICAgICAgICAgIHsgcHJvcDogImdldF9udW1iZXIoZ3RiaCkgKyAwIiwgYXNjOiBmYWxzZSB9CiAgICAgICAgICAgIF07CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLnF1ZXJ5R3RQYXJhbS5teVNvcnRzID0gW3sgcHJvcDogcHJvcCwgYXNjOiBmYWxzZSB9XTsKICAgICAgICAgIH0KICAgICAgICB9IGVsc2UgewogICAgICAgICAgaWYgKHByb3AgPT09ICJndGJoIikgewogICAgICAgICAgICB0aGlzLnF1ZXJ5R3RQYXJhbS5teVNvcnRzID0gWwogICAgICAgICAgICAgIHsgcHJvcDogImdldF9udW1iZXIoZ3RiaCkgKyAwIiwgYXNjOiB0cnVlIH0KICAgICAgICAgICAgXTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMucXVlcnlHdFBhcmFtLm15U29ydHMgPSBbeyBwcm9wOiBwcm9wLCBhc2M6IHRydWUgfV07CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMucXVlcnlHdFBhcmFtLm15U29ydHMgPSBbeyBwcm9wOiAiZ2V0X251bWJlcihndGJoKSIsIGFzYzogdHJ1ZSB9XTsKICAgICAgfQogICAgICB0aGlzLmd0VHpEYXRhKHRoaXMuZ3RmaWx0ZXJJbmZvLmRhdGEpOwogICAgfSwKICAgIC8v6K6+5aSH5Y+w6LSmCiAgICBzYlR6RGF0YShwYXJhbXMpIHsKICAgICAgY29uc3QgcGFyYW0gPSB7IC4uLnRoaXMucGFyYW1zLCAuLi5wYXJhbXMsLi4ueyBzc2d0Ymg6IHRoaXMuZ3RiaFN0b3JlIH0gfTsKICAgICAgZ2V0TGlzdChwYXJhbSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuc2J0ekRhdGEgPSB0cnVlOwogICAgICAgIHRoaXMuZ3R0ekRhdGEgPSB0aGlzLnhsdHpEYXRhID0gZmFsc2U7CiAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvMy50YWJsZURhdGEgPSByZXMuZGF0YS5yZWNvcmRzOwogICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mbzMucGFnZXIudG90YWwgPSByZXMuZGF0YS50b3RhbDsKICAgICAgfSk7CiAgICB9LAogICAgLy/ojrflj5bliJfooajmlbDmja4KICAgIGdldERhdGE6IGZ1bmN0aW9uKHBhcmFtcykgewogICAgICBpZiAodGhpcy54bHR6RGF0YSkgewogICAgICAgIHRoaXMubGluZVR6RGF0YShwYXJhbXMpOwogICAgICB9CiAgICAgIGlmICh0aGlzLmd0dHpEYXRhKSB7CiAgICAgICAgdGhpcy5ndFR6RGF0YShwYXJhbXMpOwogICAgICB9CiAgICAgIGlmICh0aGlzLnNidHpEYXRhKSB7CiAgICAgICAgdGhpcy5zYlR6RGF0YShwYXJhbXMpOwogICAgICB9CiAgICB9LAogICAgLy/nur/ot6/kv67mlLnmk43kvZwKICAgIHVwZGF0ZVJvdzEocm93KSB7CiAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS557q/6Lev5L+h5oGvIjsKICAgICAgdGhpcy54bEZvcm0gPSB7IC4uLnJvdyB9OwogICAgICB0aGlzLmdldEltZ0xpc3Qocm93LnhsYm0pOwogICAgICB0aGlzLnhsRGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgICB0aGlzLnhsc2hvdyA9IGZhbHNlOwogICAgfSwKICAgIC8v57q/6Lev5p+l55yL6K+m5oOFCiAgICBkZXRhaWxzSW5mbzEocm93KSB7CiAgICAgIHRoaXMudGl0bGUgPSAi57q/6Lev6K+m5oOFIjsKICAgICAgdGhpcy54bEZvcm0gPSB7IC4uLnJvdyB9OwogICAgICB0aGlzLmdldEltZ0xpc3Qocm93LnhsYm0pOwogICAgICB0aGlzLnhsRGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgICB0aGlzLnhsc2hvdyA9IHRydWU7CiAgICB9LAogICAganVtcFRvVHpnbChyb3cpIHsKICAgICAgcm93OwogICAgfSwKICAgIGdldEltZ0xpc3Qod2piaCkgewogICAgICB0aGlzLnhsSW1nTGlzdCA9IFtdOwogICAgICBsZXQgcGFyYW0gPSB7CiAgICAgICAgd2piaDogd2piaCwKICAgICAgICBpc2xhc3Q6ICIxIiwKICAgICAgICB3amx4OiAiMSIKICAgICAgfTsKICAgICAgZ2V0TGlzdFRaKHBhcmFtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIGxldCBkYXRhID0gcmVzLmRhdGEucmVjb3Jkc1swXS5maWxlTGlzdC5maWx0ZXIoCiAgICAgICAgICAgIHJlY29yZCA9PiByZWNvcmQuZmlsZVR5cGUgIT09ICJ2c2QiCiAgICAgICAgICApOwogICAgICAgICAgdGhpcy54bEltZ0xpc3QgPSBkYXRhOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy/nur/ot6/lj5bmtojlvLnmoYYKICAgIHJlc2V0eGxGb3JtKCkgewogICAgICB0aGlzLnhsRm9ybSA9IHt9OwogICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbigpIHsKICAgICAgICB0aGlzLiRyZWZzWyJ4bEZvcm0iXS5jbGVhclZhbGlkYXRlKCk7CiAgICAgIH0pOwogICAgICB0aGlzLnhsRGlhbG9nRm9ybVZpc2libGUgPSBmYWxzZTsKICAgIH0sCiAgICAvL+e6v+i3r+W8ueahhuWGheWuueS/neWtmAogICAgYWRkTGluZUluZm8oKSB7CiAgICAgIGxldCBwYXJhbXMgPSB7CiAgICAgICAgbHg6ICLovpPnlLXorr7lpIciLAogICAgICAgIG1jOiB0aGlzLnhsRm9ybS5saW5lTmFtZQogICAgICB9OwogICAgICB0aGlzLiRyZWZzWyJ4bEZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBzYXZlT3JVcGRhdGV4bCh0aGlzLnhsRm9ybSkudGhlbihyZXMgPT4gewogICAgICAgICAgICBpZiAocmVzLmNvZGUgPT0gIjAwMDAiKSB7CiAgICAgICAgICAgICAgLy/mlrDlop7miJDlip/lkI7lj5HpgIHpgJrnn6UKICAgICAgICAgICAgICBhZGRkd3p5ZnN0eihwYXJhbXMpLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLmk43kvZzmiJDlip8s6YCa55+l5bey5Y+R6YCBIik7CiAgICAgICAgICAgICAgdGhpcy50cmVlTGlzdCgpOwogICAgICAgICAgICAgIHRoaXMueGxEaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMubGluZVR6RGF0YSgpOwogICAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuaTjeS9nOWksei0pe+8gSIpOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgICAgIHZhciBpc0Vycm9yID0gZG9jdW1lbnQuZ2V0RWxlbWVudHNCeUNsYXNzTmFtZSgiaXMtZXJyb3IiKTsKICAgICAgICAgICAgaWYgKGlzRXJyb3JbMF0ucXVlcnlTZWxlY3RvcigiaW5wdXQiKSkgewogICAgICAgICAgICAgIGlzRXJyb3JbMF0ucXVlcnlTZWxlY3RvcigiaW5wdXQiKS5mb2N1cygpOwogICAgICAgICAgICB9IGVsc2UgaWYgKGlzRXJyb3JbMF0ucXVlcnlTZWxlY3RvcigidGV4dGFyZWEiKSkgewogICAgICAgICAgICAgIGlzRXJyb3JbMF0ucXVlcnlTZWxlY3RvcigidGV4dGFyZWEiKS5mb2N1cygpOwogICAgICAgICAgICB9CiAgICAgICAgICB9LCAxKTsKICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIGdldFRhYmxlTGlzdDogZnVuY3Rpb24ocGFyYW1zKSB7CiAgICAgIHRoaXMucXVlcnlHdFBhcmFtID0geyAuLi50aGlzLnF1ZXJ5R3RQYXJhbSwgLi4ucGFyYW1zIH07CiAgICAgIGNvbnN0IHBhcmFtID0geyAuLi50aGlzLnF1ZXJ5R3RQYXJhbSwgLi4ucGFyYW1zIH07CiAgICAgIGdldExpc3RndChwYXJhbSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mbzIudGFibGVEYXRhID0gcmVzLmRhdGEucmVjb3JkczsKICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8yLnBhZ2VyLnRvdGFsID0gcmVzLmRhdGEudG90YWw7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKgogICAgICog6KGo5qC85aSa6YCJ5qGGCiAgICAgKi8KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5vYmpJZCk7CiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMTsKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOwogICAgfSwKICAgIC8qKgogICAgICog5Yig6ZmkCiAgICAgKi8KICAgIHhsZGVsZXRlSW5mbygpIHsKICAgICAgaWYgKHRoaXMuaWRzLmxlbmd0aCAhPSAwKSB7CiAgICAgICAgdGhpcy4kY29uZmlybSgi5q2k5pON5L2c5bCG5rC45LmF5Yig6Zmk6K+l5pWw5o2uLCDmmK/lkKbnu6fnu60/IiwgIuaPkOekuiIsIHsKICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgICB4bHJlbW92ZSh0aGlzLmlkcykudGhlbihyZXMgPT4gewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsCiAgICAgICAgICAgICAgbWVzc2FnZTogIuWIoOmZpOaIkOWKnyEiCiAgICAgICAgICAgIH0pOwogICAgICAgICAgICB0aGlzLmxpbmVUekRhdGEoKTsKICAgICAgICAgIH0pOwogICAgICAgIH0pOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogImluZm8iLAogICAgICAgICAgbWVzc2FnZTogIuivt+mAieaLqeiHs+WwkeS4gOadoeaVsOaNriEiCiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0sCiAgICBzYmRlbGV0ZUluZm8oKSB7CiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggIT0gMCkgewogICAgICAgIHRoaXMuJGNvbmZpcm0oIuatpOaTjeS9nOWwhuawuOS5heWIoOmZpOivpeaVsOaNriwg5piv5ZCm57un57utPyIsICLmj5DnpLoiLCB7CiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgICAgcmVtb3ZlKHRoaXMuaWRzKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgICAgICBtZXNzYWdlOiAi5Yig6Zmk5oiQ5YqfISIKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIHRoaXMuc2JUekRhdGEoKTsKICAgICAgICAgIH0pOwogICAgICAgIH0pOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogImluZm8iLAogICAgICAgICAgbWVzc2FnZTogIuivt+mAieaLqeiHs+WwkeS4gOadoeaVsOaNriEiCiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0sCiAgICBndGRlbGV0ZUluZm8oKSB7CiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggIT0gMCkgewogICAgICAgIHRoaXMuJGNvbmZpcm0oIuatpOaTjeS9nOWwhuawuOS5heWIoOmZpOivpeaVsOaNriwg5piv5ZCm57un57utPyIsICLmj5DnpLoiLCB7CiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgICAgZ3RyZW1vdmUodGhpcy5pZHMpLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLAogICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTmiJDlip8hIgogICAgICAgICAgICB9KTsKICAgICAgICAgICAgdGhpcy5ndFR6RGF0YSgpOwogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICB0eXBlOiAiaW5mbyIsCiAgICAgICAgICBtZXNzYWdlOiAi6K+36YCJ5oup6Iez5bCR5LiA5p2h5pWw5o2uISIKICAgICAgICB9KTsKICAgICAgfQogICAgfSwKICAgIC8v5a+85Ye6ZXhjZWwKICAgIHhsZXhwb3J0RXhjZWwoKSB7CiAgICAgIC8vIGlmKCF0aGlzLnNlbGVjdERhdGEubGVuZ3RoID4gMCl7CiAgICAgIC8vICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7flnKjlt6bkvqfli77pgInopoHlr7zlh7rnmoTmlbDmja4nKQogICAgICAvLyAgIHJldHVybgogICAgICAvLyB9CiAgICAgIGxldCBmaWxlTmFtZSA9ICLovpPnlLXnur/ot6/kv6Hmga/ooagiOwogICAgICBsZXQgZXhwb3J0VXJsID0gIi9zZHhsL2V4cG9ydEV4Y2VsIjsKICAgICAgZXhwb3J0RXhjZWwoZXhwb3J0VXJsLCB0aGlzLnF1ZXJ5UGFyYW1zLCBmaWxlTmFtZSk7CiAgICB9LAogICAgLy/lr7zlh7pleGNlbAogICAgZ3RleHBvcnRFeGNlbCgpIHsKICAgICAgLy8gaWYoIXRoaXMuc2VsZWN0RGF0YS5sZW5ndGggPiAwKXsKICAgICAgLy8gICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+WcqOW3puS+p+WLvumAieimgeWvvOWHuueahOaVsOaNricpCiAgICAgIC8vICAgcmV0dXJuCiAgICAgIC8vIH0KICAgICAgbGV0IGZpbGVOYW1lID0gdGhpcy5saW5lTmFtZSArICLmnYbloZTkv6Hmga/ooagiOwogICAgICBsZXQgZXhwb3J0VXJsID0gIi90b3dlci9leHBvcnRFeGNlbCI7CiAgICAgIGV4cG9ydEV4Y2VsKGV4cG9ydFVybCwgdGhpcy5xdWVyeUd0UGFyYW0sIGZpbGVOYW1lKTsKICAgIH0sCiAgICBkZWxldGVJbmZvKCkgewogICAgICBpZiAodGhpcy54bHR6RGF0YSkgewogICAgICAgIHRoaXMueGxkZWxldGVJbmZvKCk7CiAgICAgIH0KICAgICAgaWYgKHRoaXMuZ3R0ekRhdGEpIHsKICAgICAgICB0aGlzLmd0ZGVsZXRlSW5mbygpOwogICAgICB9CiAgICAgIGlmICh0aGlzLnNidHpEYXRhKSB7CiAgICAgICAgdGhpcy5zYmRlbGV0ZUluZm8oKTsKICAgICAgfQogICAgfSwKICAgIGV4cG9ydEV4Y2VsKCkgewogICAgICBpZiAodGhpcy54bHR6RGF0YSkgewogICAgICAgIHRoaXMueGxleHBvcnRFeGNlbCgpOwogICAgICB9CiAgICAgIGlmICh0aGlzLmd0dHpEYXRhKSB7CiAgICAgICAgdGhpcy5ndGV4cG9ydEV4Y2VsKCk7CiAgICAgIH0KICAgIH0sCiAgICAvL+WvvOWFpQogICAgaW1wb3J0RXhjZWwoKSB7CiAgICAgIHRoaXMub3BlbkV4Y2VsRGlhbG9nID0gdHJ1ZTsKICAgICAgdGhpcy5maWxlTmFtZSA9ICIiOwogICAgfSwKICAgIC8qKuWvvOWFpeaWh+S7tuaPkOS6pOaMiemSriovCiAgICBzdWJtaXRFeGNlbEZvcm0oKSB7CiAgICAgIGlmICh0aGlzLnVwbG9hZERhdGEubGluZU5hbWUgPT09IHRoaXMubGluZU5hbWUpIHsKICAgICAgICB0aGlzLmlzbG9hZGluZyA9IHRydWU7CiAgICAgICAgdGhpcy4kcmVmcy51cGxvYWQuc3VibWl0KCk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kY29uZmlybSgKICAgICAgICAgICLkuIrkvKDnmoRFeGNlbOe6v+i3r+WQjeensOS4juaJgOWcqOmhtemdoue6v+i3r+S4jeS4gOiHtO+8jOaYr+WQpue7p+e7reS4iuS8oO+8nyIsCiAgICAgICAgICAi5o+Q56S6IiwKICAgICAgICAgIHsKICAgICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgICAgICB9CiAgICAgICAgKQogICAgICAgICAgLnRoZW4oKCkgPT4gewogICAgICAgICAgICAvL+WGjeasoeaPkOS6pAogICAgICAgICAgICB0aGlzLmlzbG9hZGluZyA9IHRydWU7CiAgICAgICAgICAgIHRoaXMuJHJlZnMudXBsb2FkLnN1Ym1pdCgpOwogICAgICAgICAgfSkKICAgICAgICAgIC5jYXRjaCgoKSA9PiB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgIHR5cGU6ICJpbmZvIiwKICAgICAgICAgICAgICBtZXNzYWdlOiAi5bey5Y+W5raI5LiK5LygIgogICAgICAgICAgICB9KTsKICAgICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgLyoq5a+85YWl5paH5Lu25Y+W5raI5oyJ6ZKuKi8KICAgIGNhbmNlbEltcG9ydCgpIHsKICAgICAgdGhpcy5pc2xvYWRpbmcgPSBmYWxzZTsKICAgICAgdGhpcy5vcGVuRXhjZWxEaWFsb2cgPSBmYWxzZTsKICAgICAgLy/lj5bmtojml7bmuIXnqbrkuIrkvKDmlofku7bliJfooagKICAgICAgdGhpcy4kcmVmcy51cGxvYWQuY2xlYXJGaWxlcygpOwogICAgfSwKICAgIC8qKuS4iuS8oOaIkOWKn+aWueazlSovCiAgICB1cGxvYWRTdWNjZXNzKHJlcykgewogICAgICBpZiAocmVzLmNvZGUgPT0gIjAwMDAiKSB7CiAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLkuIrkvKDmiJDlip8iKTsKICAgICAgICB0aGlzLmZpbGVOYW1lID0gIiI7CiAgICAgICAgLy/muIXnqbrkuIrkvKDnmoTmlofku7bliJfooagKICAgICAgICB0aGlzLiRyZWZzLnVwbG9hZC5jbGVhckZpbGVzKCk7CiAgICAgICAgdGhpcy5pc2xvYWRpbmcgPSBmYWxzZTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmlzbG9hZGluZyA9IGZhbHNlOwogICAgICAgIHRoaXMubXNnRXJyb3IocmVzLm1zZyk7CiAgICAgICAgdGhpcy4kcmVmcy51cGxvYWQuY2xlYXJGaWxlcygpOwogICAgICB9CiAgICAgIC8v6YeN5paw5riy5p+TCiAgICAgIHRoaXMuZ3RUekRhdGEodGhpcy5ndGZpbHRlckluZm8uZGF0YSk7CiAgICAgIHRoaXMub3BlbkV4Y2VsRGlhbG9nID0gZmFsc2U7CiAgICB9LAogICAgLyoq5paH5Lu25pS55Y+Y5pe26LCD55So55qE5Ye95pWwKi8KICAgIGhhbmRsZUNoYW5nZShmaWxlKSB7CiAgICAgIHRoaXMudXBsb2FkRGF0YS5saW5lTmFtZSA9IGZpbGUubmFtZS5zdWJzdHJpbmcoCiAgICAgICAgMCwKICAgICAgICBmaWxlLm5hbWUuaW5kZXhPZigi5p2GIikKICAgICAgKTsKICAgICAgdGhpcy5maWxlTmFtZSA9ICIiOwogICAgICBsZXQgdGVzdEZpbGVOYW1lID0gZmlsZS5uYW1lOwogICAgICB0aGlzLmZpbGVOYW1lID0gdGVzdEZpbGVOYW1lOwogICAgfSwKICAgIC8qKiDmlofku7bnp7vpmaTml7blh73mlbAqLwogICAgaGFuZGxlUmVtb3ZlKCkgewogICAgICB0aGlzLmZpbGVOYW1lID0gIiI7CiAgICAgIHRoaXMubXNnU3VjY2Vzcygi56e76Zmk5oiQ5YqfIik7CiAgICB9LAogICAgLyoq5paH5Lu26LaF5Ye66ZmQ5Yi25pe26LCD55SoKi8KICAgIGhhbmRsZUV4Y2VlZChmaWxlLCBmaWxlTGlzdCkgewogICAgICB0aGlzLm1zZ1dhcm5pbmcoIuWPquiDvea3u+WKoOS4gOS4quaWh+S7tu+8jOivt+WFiOWIoOmZpOS5i+WJjeeahOaWh+S7tiIpOwogICAgfSwKICAgIHVwZGF0ZVN0YXR1cyhyb3cpIHsKICAgICAgY29uc29sZS5sb2coInJvdyIsIHJvdyk7CiAgICAgIHRoaXMudXBkYXRlTGlzdC5zYnp0ID0gcm93LnNienQ7CiAgICAgIHRoaXMudXBkYXRlTGlzdC5vYmpJZCA9IHJvdy5vYmpJZDsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICBzdWJtaXRTdGF0dXMoKSB7CiAgICAgIGNvbnNvbGUubG9nKCJ0aGlzLnVwZGF0ZUxpc3QiLCB0aGlzLnVwZGF0ZUxpc3QpOwogICAgICB0aGlzLiRjb25maXJtKCLnoa7orqTlsIborr7lpIfnirbmgIHkv67mlLnkuLoiICsgdGhpcy51cGRhdGVMaXN0LnNienQgKyAiPyIsICIiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgdXBkYXRlU3RhdHVzKHRoaXMudXBkYXRlTGlzdCkudGhlbihyZXMgPT4gewogICAgICAgICAgaWYgKHJlcy5jb2RlID09ICIwMDAwIikgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuiuvuWkh+eKtuaAgeW3suWPmOabtO+8gSIpOwogICAgICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgICAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOS/ruaUueaTjeS9nAogICAgdXBkYXRlUm93OiBmdW5jdGlvbihyb3cpIHsKICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnovpPnlLXorr7lpIfkv6Hmga8iOwogICAgICB0aGlzLnRlY2huaWNhbFBhcmFtZXRlcnMocm93KTsKICAgICAgdGhpcy5yZXN1bWVRdWVyeS5mb3JlaWduTnVtID0gcm93LnNibWM7CiAgICAgIHRoaXMucmVzdW1lUXVlcnkuc2JseCA9IHJvdy5zYmx4OwogICAgICB0aGlzLmdldFJlc3VtTGlzdCgpOwogICAgICB0aGlzLnNiRGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgICB0aGlzLnNob3cgPSBmYWxzZTsKICAgICAgdGhpcy5qYnh4Rm9ybSA9IHJvdzsKICAgIH0sCiAgICAvL+ivpuaDhQogICAgZGV0YWlsc0luZm86IGZ1bmN0aW9uKHJvdykgewogICAgICB0aGlzLnRpdGxlID0gIui+k+eUteiuvuWkh+S/oeaBryI7CiAgICAgIHRoaXMudGVjaG5pY2FsUGFyYW1ldGVycyhyb3cpOwogICAgICB0aGlzLnJlc3VtZVF1ZXJ5LmZvcmVpZ25OdW0gPSByb3cuc2JtYzsKICAgICAgdGhpcy5yZXN1bWVRdWVyeS5zYmx4ID0gcm93LnNibHg7CiAgICAgIHRoaXMuZ2V0UmVzdW1MaXN0KCk7CiAgICAgIHRoaXMuc2JEaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CiAgICAgIHRoaXMuc2hvdyA9IHRydWU7CiAgICAgIHRoaXMuamJ4eEZvcm0gPSByb3c7CiAgICB9LAogICAgZmlsdGVyUmVzZXQodmFsLCB0eXBlKSB7CiAgICAgIGlmICh0eXBlID09PSAiZ3QiKSB7CiAgICAgICAgdGhpcy5xdWVyeUd0UGFyYW0gPSB7CiAgICAgICAgICBsaW5lTmFtZTogdGhpcy5xdWVyeUd0UGFyYW0ubGluZU5hbWUsCiAgICAgICAgICAvLyBteVNvcnRzOiBbeyBwcm9wOiAiZ2V0X251bWJlcihndGJoKSIsIGFzYzogdHJ1ZSB9XQogICAgICAgIH07CiAgICAgICAgdGhpcy5ndFR6RGF0YSh0aGlzLnF1ZXJ5R3RQYXJhbSk7CiAgICAgIH0KICAgICAgdGhpcy54bGZpbHRlckluZm8uZmllbGRMaXN0LmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgaWYgKGl0ZW0udHlwZSA9PT0gImNoZWNrYm94IikgewogICAgICAgICAgaXRlbS5jaGVja2JveFZhbHVlID0gW107CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvL+iOt+WPluW3puS+p+agkeiKgueCuQogICAgdHJlZUxpc3QoKSB7CiAgICAgIGdldFRyZWVMaXN0KCkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMudHJlZU9wdGlvbnMgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlQ2xvc2UoKSB7CiAgICAgIHRoaXMuamJ4eEZvcm0gPSB7fTsKICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24oKSB7CiAgICAgICAgdGhpcy4kcmVmc1siamJ4eEZvcm0iXS5jbGVhclZhbGlkYXRlKCk7CiAgICAgIH0pOwoKICAgICAgdGhpcy5qc2NzRm9ybSA9IHt9OwogICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbigpIHsKICAgICAgICB0aGlzLiRyZWZzWyJqc2NzRm9ybSJdLmNsZWFyVmFsaWRhdGUoKTsKICAgICAgfSk7CgogICAgICB0aGlzLnNiRGlhbG9nRm9ybVZpc2libGUgPSBmYWxzZTsKICAgIH0sCiAgICBnZXRSZXN1bUxpc3QocGFyKSB7CiAgICAgIGlmICh0aGlzLmd0dHpEYXRhKSB7CiAgICAgICAgbGV0IHBhcmFtcyA9IHsgLi4ucGFyLCAuLi50aGlzLmd0cmVzdW1lUXVlcnkgfTsKICAgICAgICBnZXRSZXN1bURhdGFMaXN0KHBhcmFtcykudGhlbihyZXMgPT4gewogICAgICAgICAgdGhpcy5ndHJlc3VtUGFnZUluZm8udGFibGVEYXRhID0gcmVzLmRhdGEucmVjb3JkczsKICAgICAgICAgIHRoaXMuZ3RyZXN1bVBhZ2VJbmZvLnBhZ2VyLnRvdGFsID0gcmVzLmRhdGEudG90YWw7CiAgICAgICAgfSk7CiAgICAgIH0KICAgICAgaWYgKHRoaXMuc2J0ekRhdGEpIHsKICAgICAgICBsZXQgcGFyYW1zID0geyAuLi5wYXIsIC4uLnRoaXMucmVzdW1lUXVlcnkgfTsKICAgICAgICBnZXRSZXN1bURhdGFMaXN0KHBhcmFtcykudGhlbihyZXMgPT4gewogICAgICAgICAgdGhpcy5yZXN1bVBhZ2VJbmZvLnRhYmxlRGF0YSA9IHJlcy5kYXRhLnJlY29yZHM7CiAgICAgICAgICB0aGlzLnJlc3VtUGFnZUluZm8ucGFnZXIudG90YWwgPSByZXMuZGF0YS50b3RhbDsKICAgICAgICB9KTsKICAgICAgfQogICAgfSwKICAgIC8v6K6+5aSH5re75Yqg5oyJ6ZKuCiAgICBzYkFkZFNlbnNvckJ1dHRvbigpIHt9LAogICAgLy/mlrDlop7mjInpkq4KICAgIEFkZFNlbnNvckJ1dHRvbigpIHsKICAgICAgaWYgKHRoaXMueGx0ekRhdGEpIHsKICAgICAgICB0aGlzLnRpdGxlID0gIuaWsOWinue6v+i3r+S/oeaBryI7CiAgICAgICAgdGhpcy54bEltZ0xpc3QgPSBbXTsKICAgICAgICB0aGlzLnhsc2hvdyA9IGZhbHNlOwogICAgICAgIHRoaXMueGxEaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CiAgICAgIH0KICAgICAgaWYgKHRoaXMuZ3R0ekRhdGEpIHsKICAgICAgICBpZiAodGhpcy5ndEZvcm0ubGluZUlkICE9IHVuZGVmaW5lZCkgewogICAgICAgICAgdGhpcy50aXRsZSA9ICLmlrDlop7mnYbloZTkv6Hmga8iOwogICAgICAgICAgdGhpcy5pbWdMaXN0ID0gW107CiAgICAgICAgICB0aGlzLmd0c2hvdyA9IGZhbHNlOwogICAgICAgICAgdGhpcy5ndERpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCLor7flhYjpgInmi6nmiYDlsZ7nur/ot6/lho3mlrDlop7mnYbloZQiKTsKICAgICAgICB9CiAgICAgIH0KICAgICAgaWYgKHRoaXMuc2J0ekRhdGEpIHsKICAgICAgICBpZiAodGhpcy5qYnh4Rm9ybS5zc2d0YmggPT09ICIiICYmIHRoaXMuamJ4eEZvcm0uc3N4bCA9PT0gIiIpIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygi6K+35Zyo5bem5L6n5qCR6YCJ5oup5YW35L2T57q/6Lev5oiW5p2G5aGU5Zyo5bCd6K+VIik7CiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQogICAgICAgIHRoaXMuamJ4eEZvcm0uc3N4bCA9IHRoaXMubGluZWRJZFN0b3JlCiAgICAgICAgdGhpcy5qYnh4Rm9ybS5saW5lTmFtZSA9IHRoaXMubGluZU5hbWUKICAgICAgICB0aGlzLmpieHhGb3JtLnNzZ3RiaCA9IHRoaXMuZ3RiaFN0b3JlCiAgICAgICAgdGhpcy5qYnh4Rm9ybS5ndG1jID0gdGhpcy5ndG1jU3RvcmUKICAgICAgICB0aGlzLnRpdGxlID0gIuaWsOWinui+k+eUteiuvuWkh+S/oeaBryI7CiAgICAgICAgLy/kuI3npoHnlKjooajljZXovpPlhaUKICAgICAgICB0aGlzLnNob3cgPSBmYWxzZTsKICAgICAgICAvL+aJk+W8gOW8ueWHuuahhgogICAgICAgIHRoaXMuc2JEaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CiAgICAgICAgLy/mlrDlop7ml7blhYjnva7nqbrljrvmn6Xor6Lln7rmlbDlj4LmlbAKICAgICAgICBsZXQgcm93ID0ge307CiAgICAgICAgLy/ojrflj5bmioDmnK/lj4LmlbAKICAgICAgICB0aGlzLnRlY2huaWNhbFBhcmFtZXRlcnMocm93KTsKICAgICAgfQogICAgfSwKICAgIEFkZFRvd2VyQmF0Y2hCdXR0b24oKSB7CiAgICAgIGlmICh0aGlzLmxpbmVkSWRTdG9yZSAmJiB0aGlzLmxpbmVOYW1lKSB7CiAgICAgICAgdGhpcy5hZGRUb3dlckJhdGNoRm9ybS5saW5lTmFtZSA9IHRoaXMubGluZU5hbWU7CiAgICAgICAgdGhpcy4kc2V0KHRoaXMuYWRkVG93ZXJCYXRjaEZvcm0sICJ0b3dlck5hbWVMaW5rRmxhZyIsICIjIik7CiAgICAgICAgdGhpcy4kc2V0KHRoaXMuYWRkVG93ZXJCYXRjaEZvcm0sICJ0b3dlck51bWJlclN1ZmZpeCIsICIjIik7CiAgICAgICAgdGhpcy5hZGRUb3dlckJhdGNoRm9ybS50b3dlckluZm8gPSB7CiAgICAgICAgICBsaW5lSWQ6IHRoaXMubGluZWRJZFN0b3JlLAogICAgICAgICAgbGluZU5hbWU6IHRoaXMubGluZU5hbWUsCiAgICAgICAgICBkeWRqYm06IHRoaXMuZHlkamJtLAogICAgICAgICAgc3NibTogIue6v+i3r+WIhuWFrOWPuCIKICAgICAgICB9OwogICAgICAgIHRoaXMudGl0bGUgPSAi5om56YeP5paw5aKe5p2G5aGUIjsKICAgICAgICB0aGlzLmFkZFRvd2VyQmF0Y2hEaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CiAgICAgIH0KICAgIH0sCiAgICBzYXZlVG93ZXJCYXRjaEJ1dHRvbigpIHsKICAgICAgdGhpcy4kcmVmc1siYWRkVG93ZXJCYXRjaEZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBzYXZlVG93ZXJCYXRjaCh0aGlzLmFkZFRvd2VyQmF0Y2hGb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLmk43kvZzmiJDlip8iKTsKICAgICAgICAgICAgICB0aGlzLmFkZFRvd2VyQmF0Y2hEaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ3RUekRhdGEodGhpcy5ndGZpbHRlckluZm8uZGF0YSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgYmVmb3JlVXBsb2FkKGZpbGUpIHsKICAgICAgY29uc3QgZmlsZVNpemUgPSBmaWxlLnNpemUgPCAxMDI0ICogMTAyNCAqIDUwOwogICAgICBpZiAoIWZpbGVTaXplKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5LiK5Lyg5paH5Lu25aSn5bCP5LiN6IO96LaF6L+HIDUwTUIhIik7CiAgICAgIH0KICAgIH0sCiAgICAvL+adhuWhlOS/ruaUueaTjeS9nAogICAgdXBkYXRlUm93Mihyb3cpIHsKICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnmnYbloZTkv6Hmga8iOwogICAgICB0aGlzLmNsZWFyVXBsb2FkKCk7CiAgICAgIHRoaXMuZ3RyZXN1bWVRdWVyeS5mb3JlaWduTnVtID0gcm93LnNzYm07CiAgICAgIHRoaXMuZ3RyZXN1bWVRdWVyeS5zYmx4ID0gcm93LnNibHg7CiAgICAgIHRoaXMuZ2V0UmVzdW1MaXN0KCk7CiAgICAgIHRoaXMuZ3RGb3JtID0geyAuLi5yb3cgfTsKICAgICAgdGhpcy5ndEZvcm0uYXR0YWNobWVudCA9IFtdOwogICAgICB0aGlzLmd0VHpEYXRhKCk7CiAgICAgIHRoaXMuZ3REaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CiAgICAgIHRoaXMuZ3RzaG93ID0gZmFsc2U7CiAgICB9LAogICAgLy/mnYbloZTmn6XnnIvor6bmg4UKICAgIGRldGFpbHNJbmZvMihyb3cpIHsKICAgICAgdGhpcy50aXRsZSA9ICLmnYbloZTkv6Hmga8iOwogICAgICB0aGlzLmNsZWFyVXBsb2FkKCk7CiAgICAgIHRoaXMuZ3RyZXN1bWVRdWVyeS5mb3JlaWduTnVtID0gcm93LnNzYm07CiAgICAgIHRoaXMuZ3RyZXN1bWVRdWVyeS5zYmx4ID0gcm93LnNibHg7CiAgICAgIHRoaXMuZ2V0UmVzdW1MaXN0KCk7CiAgICAgIHRoaXMuZ3RGb3JtID0geyAuLi5yb3cgfTsKICAgICAgdGhpcy5ndEZvcm0uYXR0YWNobWVudCA9IFtdOwogICAgICB0aGlzLmd0VHpEYXRhKCk7CiAgICAgIHRoaXMuZ3REaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CiAgICAgIHRoaXMuZ3RzaG93ID0gdHJ1ZTsKICAgIH0sCiAgICBjbGVhclVwbG9hZCgpIHsKICAgICAgaWYgKHRoaXMuJHJlZnMudXBsb2FkKSB7CiAgICAgICAgdGhpcy4kcmVmcy51cGxvYWQuY2xlYXJGaWxlcygpOwogICAgICB9CiAgICB9LAogICAgLy/mnYbloZTlvLnmoYblhbPpl60KICAgIGd0aGFuZGxlQ2xvc2UoKSB7CiAgICAgIC8vIHRoaXMuZ3RGb3JtID0gewogICAgICAvLyAgIGF0dGFjaG1lbnQ6IFtdLAogICAgICAvLyAgIHNzYm0gOiAn57q/6Lev5YiG5YWs5Y+4JywKICAgICAgLy8gICBsaW5lSWQgOiB0aGlzLmxpbmVkSWRTdG9yZSwKICAgICAgLy8gICBsaW5lTmFtZSA6IHRoaXMubGluZU5hbWUKICAgICAgLy8gfTsKICAgICAgdGhpcy5ndEZvcm0uYXR0YWNobWVudCA9IFtdOwogICAgICB0aGlzLmd0Rm9ybS5vYmpJZCA9IHVuZGVmaW5lZDsKICAgICAgdGhpcy5ndEZvcm0ubGluZUlkID0gdGhpcy5saW5lZElkU3RvcmU7CiAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uKCkgewogICAgICAgIHRoaXMuJHJlZnNbImd0Rm9ybSJdLmNsZWFyVmFsaWRhdGUoKTsKICAgICAgfSk7CiAgICAgIHRoaXMuZ3REaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlOwogICAgfSwKICAgIC8v5p2G5aGU5YaF5a655L+d5a2YCiAgICBhZGRHdEluZm8oKSB7CiAgICAgIHRoaXMuJHJlZnNbImd0Rm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHNhdmVPclVwZGF0ZWd0KHRoaXMuZ3RGb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PSAiMDAwMCIpIHsKICAgICAgICAgICAgICB0aGlzLnVwbG9hZERhdGEuYnVzaW5lc3NJZCA9IHJlcy5kYXRhLm9iaklkOwogICAgICAgICAgICAgIHRoaXMuJHJlZnMudXBsb2FkLnN1Ym1pdCgpOwogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5pON5L2c5oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5ndERpYWxvZ0Zvcm1WaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy5ndFR6RGF0YSgpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi5pON5L2c5aSx6LSl77yBIik7CiAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgICAgIHZhciBpc0Vycm9yID0gZG9jdW1lbnQuZ2V0RWxlbWVudHNCeUNsYXNzTmFtZSgiaXMtZXJyb3IiKTsKICAgICAgICAgICAgaWYgKGlzRXJyb3JbMF0ucXVlcnlTZWxlY3RvcigiaW5wdXQiKSkgewogICAgICAgICAgICAgIGlzRXJyb3JbMF0ucXVlcnlTZWxlY3RvcigiaW5wdXQiKS5mb2N1cygpOwogICAgICAgICAgICB9IGVsc2UgaWYgKGlzRXJyb3JbMF0ucXVlcnlTZWxlY3RvcigidGV4dGFyZWEiKSkgewogICAgICAgICAgICAgIGlzRXJyb3JbMF0ucXVlcnlTZWxlY3RvcigidGV4dGFyZWEiKS5mb2N1cygpOwogICAgICAgICAgICB9CiAgICAgICAgICB9LCAxKTsKICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8v6Ze06ZqU5re75Yqg5oyJ6ZKuCiAgICBqZ0FkZGpnQnV0dG9uKCkgewogICAgICB0aGlzLmpnRGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgfSwKICAgIHNlbGVjdENoYW5nZShyb3dzKSB7CiAgICAgIHRoaXMuc2VsZWN0Um93cyA9IHJvd3M7CiAgICB9LAogICAgLy/mr4/pobXlsZXnpLrmlbDph4/ngrnlh7vkuovku7YKICAgIGhhbmRsZVNpemVDaGFuZ2UoKSB7fSwKICAgIC8v6aG156CB5pS55Y+Y5LqL5Lu2CiAgICBoYW5kbGVDdXJyZW50Q2hhbmdlKCkge30sCiAgICAvL+agkeeCueWHu+S6i+S7tgogICAgaGFuZGxlTm9kZUNsaWNrKGRhdGEsIG5vZGUsIG5vZGVJbmZvKSB7CiAgICAgIHRoaXMuaWRlbnRpZmllciA9IGRhdGEuaWRlbnRpZmllcjsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5keWRqID0gIiI7IC8v5riF56m655S15Y6L562J57qn5p2h5Lu2CiAgICAgIC8v5b2T5YmN6IqC54K55piv57q/6Lev6IqC54K5CiAgICAgIGlmIChkYXRhLmlkZW50aWZpZXIgPT0gIjEiKSB7CiAgICAgICAgLy/nur/ot6/lj7DotKYKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmxpbmVOYW1lID0gIiI7CiAgICAgICAgdGhpcy5saW5lVHpEYXRhKHRoaXMucXVlcnlQYXJhbXMpOwogICAgICB9IGVsc2UgaWYgKGRhdGEuaWRlbnRpZmllciA9PSAiMiIpIHsKICAgICAgICAvL+eUteWOi+etiee6p+iKgueCuQogICAgICAgIHRoaXMueGxvcHRpb25zLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICBpZiAoaXRlbS5sYWJlbCA9PT0gZGF0YS5pZCkgewogICAgICAgICAgICB0aGlzLmR5ZGpibSA9IGl0ZW0udmFsdWU7CiAgICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgICB0aGlzLiRzZXQodGhpcy54bEZvcm0sICJkeWRqYm0iLCB0aGlzLmR5ZGpibSk7IC8v6K6+572u55S15Y6L562J57qnCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5keWRqID0gZGF0YS5pZDsKICAgICAgICB0aGlzLmxpbmVUekRhdGEodGhpcy5xdWVyeVBhcmFtcyk7IC8v6K+35rGC57q/6Lev5pWw5o2uCiAgICAgIH0gZWxzZSBpZiAoZGF0YS5pZGVudGlmaWVyID09ICIzIikgewogICAgICAgIHRoaXMuZ3RGb3JtLmxpbmVOYW1lID0gZGF0YS5sYWJlbDsKICAgICAgICB0aGlzLmd0Rm9ybS5saW5lSWQgPSBkYXRhLmlkOwogICAgICAgIHRoaXMubGluZWRJZFN0b3JlID0gZGF0YS5pZDsKICAgICAgICB0aGlzLmd0ZmlsdGVySW5mby5kYXRhLmxpbmVOYW1lID0gZGF0YS5sYWJlbDsKICAgICAgICB0aGlzLmxpbmVOYW1lID0gZGF0YS5sYWJlbDsKICAgICAgICB0aGlzLmd0VHpEYXRhKHRoaXMuZ3RmaWx0ZXJJbmZvLmRhdGEpOwogICAgICB9IGVsc2UgaWYgKGRhdGEuaWRlbnRpZmllciA9PSAiNCIpIHsKICAgICAgICB0aGlzLmd0YmhTdG9yZSA9IGRhdGEuaWQ7IC8v5p2G5aGU57yW5Y+3CiAgICAgICAgdGhpcy5ndG1jU3RvcmUgPSBkYXRhLmxhYmVsOwogICAgICAgIHRoaXMuamJ4eEZvcm0uc3N4bCA9IG5vZGUucGFyZW50LmRhdGEuaWQ7IC8v57q/6Lev57yW56CBCiAgICAgICAgdGhpcy5qYnh4Rm9ybS5saW5lTmFtZSA9IG5vZGUucGFyZW50LmRhdGEubGFiZWw7IC8v57q/6Lev5ZCN56ewCiAgICAgICAgdGhpcy5qYnh4Rm9ybS5zc2d0YmggPSBkYXRhLmlkOyAvL+adhuWhlOe8luWPtwogICAgICAgIHRoaXMuamJ4eEZvcm0uZ3RtYyA9IGRhdGEubGFiZWw7IC8v5p2G5aGU5ZCN56ewCiAgICAgICAgbGV0IHNzZ3RiaCA9IGRhdGEuaWQ7IC8v5p2G5aGU57yW5Y+3CiAgICAgICAgdGhpcy5zYlR6RGF0YSgpOwogICAgICB9CiAgICB9LAogICAgLy/nvLrpmbfmoIflh4blupPmlrDlop7lrozmiJAKICAgIHF4Y29tbWl0KCkgewogICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gZmFsc2U7CiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7CiAgICB9LAogICAgc3VibWl0KCkgewogICAgICB0aGlzLiRyZWZzWyJqYnh4Rm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHRoaXMuYWRkRXF1aXBJbmZvKCk7CiAgICAgICAgICAvLyAgdGhpcy5zdWJtaXRQYXJhbWV0ZXIoKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgICAgIHZhciBpc0Vycm9yID0gZG9jdW1lbnQuZ2V0RWxlbWVudHNCeUNsYXNzTmFtZSgiaXMtZXJyb3IiKTsKICAgICAgICAgICAgaWYgKGlzRXJyb3JbMF0ucXVlcnlTZWxlY3RvcigiaW5wdXQiKSkgewogICAgICAgICAgICAgIGlzRXJyb3JbMF0ucXVlcnlTZWxlY3RvcigiaW5wdXQiKS5mb2N1cygpOwogICAgICAgICAgICB9IGVsc2UgaWYgKGlzRXJyb3JbMF0ucXVlcnlTZWxlY3RvcigidGV4dGFyZWEiKSkgewogICAgICAgICAgICAgIGlzRXJyb3JbMF0ucXVlcnlTZWxlY3RvcigidGV4dGFyZWEiKS5mb2N1cygpOwogICAgICAgICAgICB9CiAgICAgICAgICB9LCAxKTsKICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8v6I635Y+W6K6+5aSH57G75Z6L5LiL5ouJ5qGG5pWw5o2uCiAgICBnZXRTYmx4RGF0YUxpc3RTZWxlY3RlZCgpIHsKICAgICAgbGV0IHNibHhQYXJhbSA9IHsKICAgICAgICB0eXBlOiAi6L6T55S16K6+5aSHIgogICAgICB9OwogICAgICBnZXRTYmx4RGF0YUxpc3RTZWxlY3RlZChzYmx4UGFyYW0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLnNibHhPcHRpb25zRGF0YVNlbGVjdGVkID0gcmVzLmRhdGE7CiAgICAgIH0pOwogICAgfSwKICAgIC8v6K6+5aSH57G75Z6LY2hhbmdl5LqL5Lu2CiAgICBzaG93UGFyYW1zKGRhdGEpIHsKICAgICAgdGhpcy5wYXJhbVF1ZXJ5LnNibHhibSA9IGRhdGE7CiAgICAgIHRoaXMuZ2V0UGFyYW1ldGVycygpOwogICAgfSwKICAgIC8v54K55Ye75paw5aKe77yM5L+u5pS577yM6K+m5oOF5pe277yM6YeN5paw6I635Y+W5a+55bqU55qE5oqA5pyv5Y+C5pWw5L+h5oGvCiAgICB0ZWNobmljYWxQYXJhbWV0ZXJzKHJvdykgewogICAgICAvL+iuvuWkh+exu+WeiwogICAgICB0aGlzLmpzY3NGb3JtID0ge307CiAgICAgIHRoaXMucGFyYW1RdWVyeS5zYmx4Ym0gPSByb3cuc2JmbGJtOwogICAgICB0aGlzLmpzY3NGb3JtLnNibHhibSA9IHJvdy5zYmZsYm07CiAgICAgIHRoaXMuanNjc0Zvcm0uc2JibSA9IHJvdy5vYmpJZDsKICAgICAgdGhpcy5nZXRQYXJhbWV0ZXJzKCk7CiAgICB9LAogICAgLy/ojrflj5bmioDmnK/lj4LmlbDlr7nnhafkv6Hmga/vvIzlsZXnpLrlr7nlupTlvpfmioDmnK/lj4LmlbBsYWJlbOS/oeaBrwogICAgYXN5bmMgZ2V0UGFyYW1ldGVycygpIHsKICAgICAgdGhpcy5qc2NzTGFiZWxMaXN0ID0gW107CiAgICAgIGdldFBhcmFtRGF0YUxpc3QodGhpcy5wYXJhbVF1ZXJ5KS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5qc2NzTGFiZWxMaXN0ID0gcmVzLmRhdGE7CiAgICAgICAgdGhpcy5nZXRQYXJhbVZhbHVlKCk7CiAgICAgIH0pOwogICAgfSwKICAgIC8v6I635Y+W5YC85pa55rOVCiAgICBnZXRQYXJhbVZhbHVlKCkgewogICAgICBnZXRQYXJhbXNWYWx1ZSh0aGlzLmpzY3NGb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgaWYgKHJlcy5kYXRhICE9ICIiKSB7CiAgICAgICAgICB0aGlzLmpzY3NGb3JtID0geyAuLi5yZXMuZGF0YVswXSB9OwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy/kv53lrZjln7rmlbDlj4LmlbDlgLzkv6Hmga8KICAgIHN1Ym1pdFBhcmFtZXRlcigpIHsKICAgICAgc2F2ZVBhcmFtVmFsdWUodGhpcy5qc2NzRm9ybSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuc2JEaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICBhc3luYyBkZWxldGVGaWxlQnlJZChpZCl7CiAgICAgIGxldCB7Y29kZX09YXdhaXQgZGVsZXRlQnlJZChpZCkKICAgICAgaWYoY29kZT09PScwMDAwJyl7CiAgICAgICAgYXdhaXQgdGhpcy5nZXRGaWxlTGlzdCgpOwogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgbWVzc2FnZTogJ+aWh+S7tuWIoOmZpOaIkOWKnyEnCiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0sCiAgfQp9Owo="}, {"version": 3, "sources": ["sdsb.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmgDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "sdsb.vue", "sourceRoot": "src/views/dagangOilfield/dwzygl/sdsbgl", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card>\n          <div style=\"overflow: auto;height: 90vh\">\n            <el-tree\n              :expand-on-click-node=\"true\"\n              highlight-current\n              ref=\"tree\"\n              id=\"tree\"\n              :data=\"treeOptions\"\n              :default-expanded-keys=\"['1001']\"\n              @node-click=\"handleNodeClick\"\n              node-key=\"id\"\n              accordion\n            >\n              <span slot-scope=\"{ node, data }\">\n                <i :class=\"icons[data.identifier]\" />\n                <span style=\"margin-left:5px;\" :title=\"data.label\">{{\n                  data.label\n                }}</span>\n              </span>\n            </el-tree>\n          </div>\n        </el-card>\n      </el-col>\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <!--变电站查询-->\n        <el-filter\n          ref=\"filter1\"\n          :data=\"xlfilterInfo.data\"\n          :field-list=\"xlfilterInfo.fieldList\"\n          :width=\"{ labelWidth: 130, itemWidth: 165 }\"\n          comp-table=\"tableAndPageInfo1\"\n          @handleReset=\"filterReset($event, 'xl')\"\n          v-show=\"xltzData\"\n        />\n        <!--  杆塔查询 -->\n        <el-filter\n          ref=\"filter1\"\n          :data=\"gtfilterInfo.data\"\n          :field-list=\"gtfilterInfo.fieldList\"\n          :btnHidden=\"false\"\n          comp-table=\"tableAndPageInfo2\"\n          @handleReset=\"filterReset($event, 'gt')\"\n          v-show=\"gttzData\"\n        />\n        <!--设备查询-->\n        <el-filter\n          ref=\"filter1\"\n          :data=\"sbfilterInfo.data\"\n          :field-list=\"sbfilterInfo.fieldList\"\n          :btnHidden=\"false\"\n          comp-table=\"tableAndPageInfo3\"\n          @handleReset=\"filterReset($event, 'sb')\"\n          v-show=\"sbtzData\"\n        />\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button\n              type=\"primary\"\n              v-hasPermi=\"['sbtz:button:add']\"\n              icon=\"el-icon-plus\"\n              @click=\"AddSensorButton\"\n              >新增\n            </el-button>\n            <el-button\n              v-show=\"gttzData\"\n              type=\"primary\"\n              v-hasPermi=\"['sbtz:button:add']\"\n              icon=\"el-icon-plus\"\n              @click=\"AddTowerBatchButton\"\n              >批量新增\n            </el-button>\n            <el-button\n              v-show=\"!sbtzData\"\n              type=\"success\"\n              icon=\"el-icon-download\"\n              @click=\"exportExcel\"\n              >导出</el-button\n            >\n            <el-button\n              v-show=\"gttzData\"\n              type=\"success\"\n              icon=\"el-icon-download\"\n              @click=\"importExcel\"\n              >导入</el-button\n            >\n            <el-button\n              type=\"danger\"\n              v-hasPermi=\"['sbtz:button:delete']\"\n              icon=\"el-icon-delete\"\n              @click=\"deleteInfo\"\n              >删除\n            </el-button>\n          </div>\n\n          <!--变电站查询-->\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo1\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"61vh\"\n            v-show=\"xltzData\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"updateRow1(scope.row)\"\n                  v-hasPermi=\"['sbtz:button:updete1']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                >\n                </el-button>\n                <el-button\n                  @click=\"detailsInfo1(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n                <router-link\n                  :to=\"{\n                    path: '/jszlgl/tzgl',\n                    query: { wjbh: scope.row.xlbm, dydj: '' }\n                  }\"\n                  style=\"margin-left:10px;\"\n                >\n                  <el-button\n                    type=\"text\"\n                    size=\"small\"\n                    title=\"图纸跳转\"\n                    class=\"el-icon-discover\"\n                  ></el-button>\n                </router-link>\n                <!-- <el-button type=\"text\" size=\"small\" @click=\"jumpToTzgl(scope.row)\" title=\"图纸跳转\" class=\"el-icon-discover\"></el-button> -->\n                <el-button\n                  @click=\"zxwhFun(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"支线维护\"\n                  class=\"el-icon-setting\"\n                  style=\"margin-left:10px;\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n\n          <!--  杆塔查询 -->\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo2\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            @sort-events=\"sortChangeTowerData\"\n            height=\"65.4vh\"\n            v-show=\"gttzData\"\n            @getMethod=\"gtTzData\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"updateStatus(scope.row)\"\n                  v-hasPermi=\"['sbtz:button:ztbg']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"状态变更\"\n                  class=\"el-icon-set-up\"\n                >\n                </el-button>\n                <el-button\n                  @click=\"updateRow2(scope.row)\"\n                  v-hasPermi=\"['sbtz:button:update2']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                >\n                </el-button>\n                <el-button\n                  @click=\"detailsInfo2(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n\n          <!--设备查询-->\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo3\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"65.4vh\"\n            v-show=\"sbtzData\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"updateRow(scope.row)\"\n                  v-hasPermi=\"['sbtz:button:update3']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                >\n                </el-button>\n                <el-button\n                  @click=\"detailsInfo(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!-- 线路详情所用弹出框开始-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"xlDialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"60%\"\n      @close=\"resetxlForm\"\n      v-dialogDrag\n    >\n      <el-form\n        :model=\"xlForm\"\n        ref=\"xlForm\"\n        :disabled=\"xlshow\"\n        :rules=\"xlRules\"\n        label-width=\"130px\"\n      >\n        <!-- <div class=\"divHeader\">\n          <span>基本信息</span>\n        </div> -->\n        <div\n          class=\"block\"\n          style=\"width: 50%;height: 50%;margin-bottom: 2%;float: right\"\n        >\n          <span class=\"demonstration\">线路图</span>\n          <el-carousel\n            trigger=\"click\"\n            class=\"imgCls\"\n            height=\"150px\"\n            indicator-position=\"none\"\n            arrow=\"always\"\n            type=\"card\"\n          >\n            <el-carousel-item v-for=\"(img, index) in xlImgList\" :key=\"index\">\n              <viewer :images=\"xlImgList\" style=\"z-index: 999\">\n                <img :src=\"img.fileUrl\" width=\"100%\" height=\"100%\" />\n              </viewer>\n            </el-carousel-item>\n          </el-carousel>\n        </div>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路图编号\" prop=\"xlbm\">\n              <el-autocomplete\n                v-model=\"xlForm.xlbm\"\n                placeholder=\"请输入线路图编号\"\n                popper-class=\"my-autocomplete\"\n                :fetch-suggestions=\"querySearch\"\n                :trigger-on-focus=\"false\"\n                @select=\"handleSelect\"\n              >\n                <template slot-scope=\"{ item }\">\n                  <div class=\"name\">{{ item.wjbh }}</div>\n                  <span class=\"addr\">{{ item.wjmc }}</span>\n                </template>\n              </el-autocomplete>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路名称\" prop=\"lineName\">\n              <el-input\n                v-model=\"xlForm.lineName\"\n                placeholder=\"请输入线路名称\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路专业\" prop=\"lineType\">\n              <el-select\n                v-model=\"xlForm.lineType\"\n                placeholder=\"请选择线路专业\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in lineTypeOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电压等级\" prop=\"dydjbm\">\n              <el-select\n                v-model=\"xlForm.dydjbm\"\n                placeholder=\"请选择电压等级\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in xloptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"负责人\" prop=\"fzr\">\n              <el-input\n                v-model=\"xlForm.fzr\"\n                :placeholder=\"xlshow ? '' : '请输入负责人'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"运行班组\" prop=\"yxbz\">\n              <el-select\n                v-model=\"xlForm.yxbz\"\n                :placeholder=\"xlshow ? '' : '请选择运行班组'\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in deviceNameOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.label\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"线路性质\" prop=\"xlxz\">\n              <el-input v-model=\"xlForm.xlxz\" placeholder=\"请选择线路性质\" clearable></el-input>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路类型\" prop=\"xllx\">\n              <el-select\n                v-model=\"xlForm.xllx\"\n                :placeholder=\"xlshow ? '' : '请选择线路类型'\"\n                clearable\n              >\n                <el-option label=\"架空线路\" value=\"架空线路\"></el-option>\n                <el-option label=\"混合线路\" value=\"混合线路\"></el-option>\n                <el-option label=\"电缆线路\" value=\"电缆线路\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路起点\" prop=\"startLine\">\n              <el-input\n                v-model=\"xlForm.startLine\"\n                placeholder=\"请输入线路起点\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路终点\" prop=\"stopLine\">\n              <el-input\n                v-model=\"xlForm.stopLine\"\n                :placeholder=\"xlshow ? '' : '请输入线路终点'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"同杆并架长度\" prop=\"tgbjcd\">\n              <el-input v-model=\"xlForm.tgbjcd\" placeholder=\"请输入同杆并架长度\"></el-input>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路起始杆塔号\" prop=\"startTowerNum\">\n              <el-input\n                v-model=\"xlForm.startTowerNum\"\n                :placeholder=\"xlshow ? '' : '请选择线路起始杆塔号'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路终止杆塔号\" prop=\"stopTowerNum\">\n              <el-input\n                v-model=\"xlForm.stopTowerNum\"\n                :placeholder=\"xlshow ? '' : '请选择线路终止杆塔号'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"是否有光缆\" prop=\"sfygl\">\n              <el-select v-model=\"xlForm.sfygl\" placeholder=\"请选择是否有光缆\" clearable>\n                <el-option label=\"是\" value=\"是\"></el-option>\n                <el-option label=\"否\" value=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"光缆类别\" prop=\"gllb\">\n              <el-input\n                v-model=\"xlForm.gllb\"\n                :placeholder=\"xlshow ? '' : '请输入光缆类别'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"投运日期\" class=\"add_sy_tyrq\" prop=\"tyrq\">\n              <el-date-picker\n                v-model=\"xlForm.tyrq\"\n                type=\"date\"\n                :placeholder=\"xlshow ? '' : '选择投运日期'\"\n                format=\"yyyy-MM-dd\"\n                value-format=\"yyyy-MM-dd\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"设计单位\" class=\"add_sy_tyrq\" prop=\"sjdw\">\n              <el-input v-model=\"xlForm.sjdw\" placeholder=\"请输入设计单位\"></el-input>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"电缆型号\" prop=\"dlxh\">\n              <el-input\n                v-model=\"xlForm.dlxh\"\n                :placeholder=\"xlshow ? '' : '请输入电缆型号'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电缆长度(M)\" prop=\"dlLength\">\n              <el-input\n                v-model=\"xlForm.dlLength\"\n                placeholder=\"请输入电缆长度\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"检修单位\" prop=\"jxdw\">\n              <el-input v-model=\"xlForm.jxdw\" placeholder=\"请输入检修单位\"></el-input>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路全长（KM）\" prop=\"totalLength\">\n              <el-input\n                v-model=\"xlForm.totalLength\"\n                placeholder=\"请输入线路全长\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路状态\" prop=\"lineStatus\">\n              <el-select\n                v-model=\"xlForm.lineStatus\"\n                placeholder=\"请选择线路状态\"\n              >\n                <el-option\n                  v-for=\"item in xlztOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"负责人\" prop=\"fzr\">\n              <el-input v-model=\"xlForm.fzr\" placeholder=\"请输入负责人\"></el-input>\n            </el-form-item>\n          </el-col> -->\n          <!-- </el-row> -->\n          <!-- <div class=\"divHeader\">\n          <span>详细信息</span>\n        </div>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"资产性质\" class=\"add_sy_tyrq\" prop=\"zcxz\">\n              <el-select v-model=\"xlForm.zcxz\" placeholder=\"请选择资产性质\" clearable >\n                <el-option label=\"公用\" value=\"公用\"></el-option>\n                <el-option label=\"专用\" value=\"专用\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否为联络线路\" prop=\"sfllxl\">\n              <el-select v-model=\"xlForm.sfllxl\" placeholder=\"请选择是否为联络线路\" clearable>\n                <el-option label=\"是\" value=\"是\"></el-option>\n                <el-option label=\"否\" value=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"联络类型\" prop=\"lllx\">\n              <el-select v-model=\"xlForm.lllx\" placeholder=\"请选择联络类型\" clearable>\n                <el-option label=\"低压\" value=\"低压\"></el-option>\n                <el-option label=\"高压\" value=\"高压\"></el-option>\n                <el-option label=\"单环\" value=\"单环\"></el-option>\n                <el-option label=\"双环\" value=\"双环\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"运行班组\" prop=\"yxbz\">\n              <el-select v-model=\"xlForm.yxbz\" placeholder=\"请选择运行班组\" clearable>\n                <el-option\n                  v-for=\"item in deviceNameOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.label\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col> -->\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\" prop=\"bz\">\n              <el-input v-model=\"xlForm.bz\" type=\"textarea\" rows=\"2\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"xlDialogFormVisible = false\">取 消</el-button>\n        <el-button\n          v-if=\"title == '修改线路信息' || title == '新增线路信息'\"\n          type=\"primary\"\n          @click=\"addLineInfo\"\n          >确 定</el-button\n        >\n      </div>\n    </el-dialog>\n    <!-- 线路详情所用弹出框结束-->\n\n    <!-- 杆塔弹出框开始展示设备履历 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"gtDialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"60%\"\n      @close=\"gthandleClose\"\n      v-dialogDrag\n    >\n      <el-tabs v-model=\"gtactiveTabName\">\n        <!--基本信息-->\n        <el-tab-pane label=\"基本信息\" name=\"sbDesc\">\n          <!--          <div class=\"block\" style=\"width: 50%;height: 50%;margin-bottom: 2%;float: right\">\n            <span class=\"demonstration\">设备图片</span>\n            <el-carousel trigger=\"click\" height=\"150px\" indicator-position=\"none\" :interval=\"2000\" type=\"card\">\n              <el-carousel-item v-for=\"(img,index) in imgList\" :key=\"index\">\n                <viewer :images=\"imgList\">\n                  <img :src=\"img.url\" width=\"100%\" height=\"100%\"/>\n                </viewer>\n              </el-carousel-item>\n            </el-carousel>\n          </div>-->\n          <el-form\n            :model=\"gtForm\"\n            ref=\"gtForm\"\n            :disabled=\"gtshow\"\n            :rules=\"gtRules\"\n            label-width=\"130px\"\n          >\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔编号\" prop=\"gtbh\">\n                  <el-input\n                    v-model=\"gtForm.gtbh\"\n                    placeholder=\"请输入杆塔编号\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔名称\" prop=\"gtmc\">\n                  <el-input\n                    v-model=\"gtForm.gtmc\"\n                    placeholder=\"请输入杆塔名称\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"所属线路\" prop=\"lineName\">\n                  <el-input\n                    v-model=\"gtForm.lineName\"\n                    placeholder=\"请输入所属线路\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔材质\" prop=\"gtcz\">\n                  <el-select\n                    v-model=\"gtForm.gtcz\"\n                    placeholder=\"请选择杆塔材质\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in gtczOptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"运行班组\" prop=\"yxbz\">\n                  <el-select\n                    v-model=\"gtForm.yxbz\"\n                    placeholder=\"请选择运行班组\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in deviceNameOptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.label\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"所属部门\" prop=\"ssbm\">\n                  <el-input\n                    v-model=\"gtForm.ssbm\"\n                    placeholder=\"请输入所属部门\"\n                    disabled\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"电压等级\" prop=\"dydj\">\n                  <el-select\n                    v-model=\"gtForm.dydj\"\n                    placeholder=\"请选择电压等级\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in gtoptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔型式\" prop=\"gtxs\">\n                  <el-select\n                    v-model=\"gtForm.gtxs\"\n                    :placeholder=\"gtshow ? '' : '请选择杆塔型式'\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in gtxsOptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔性质\" prop=\"gtNature\">\n                  <el-select\n                    v-model=\"gtForm.gtNature\"\n                    placeholder=\"请选择杆塔性质\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in gtxzList\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item\n                  label=\"杆塔形状\"\n                  prop=\"gtxz\"\n                  v-if=\"this.gtForm.dydj !== '6kV'\"\n                >\n                  <el-select\n                    v-model=\"gtForm.gtxz\"\n                    placeholder=\"请选择杆塔形状\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in gtxzOptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"相序\" prop=\"xx\">\n                  <el-select\n                    v-model=\"gtForm.xx\"\n                    :placeholder=\"gtshow ? '' : '请选择相序'\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in xxOptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\" v-if=\"this.gtForm.dydj !== '6kV'\">\n                <el-form-item label=\"杆塔呼称高(m)\" prop=\"gthcg\">\n                  <el-input\n                    v-model=\"gtForm.gthcg\"\n                    type=\"number\"\n                    placeholder=\"请输入杆塔呼称高\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔高度(m)\" prop=\"towerHeight\">\n                  <el-input\n                    v-model=\"gtForm.towerHeight\"\n                    type=\"number\"\n                    placeholder=\"请输入杆塔高度\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n\n              <el-col :span=\"8\">\n                <el-form-item\n                  label=\"至上基塔档距(m)\"\n                  prop=\"zsjtdj\"\n                  v-if=\"this.gtForm.dydj !== '6kV'\"\n                >\n                  <el-input\n                    v-model=\"gtForm.zsjtdj\"\n                    type=\"number\"\n                    placeholder=\"请输入至上基塔档距\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"导线排列方式\" prop=\"dxplfs\">\n                  <el-select\n                    v-model=\"gtForm.dxplfs\"\n                    :placeholder=\"gtshow ? '' : '请选择导线排列方式'\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in dxplOptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"是否同杆并架\" prop=\"sftgbj\">\n                  <el-select\n                    v-model=\"gtForm.sftgbj\"\n                    :placeholder=\"gtshow ? '' : '请选择是否同杆并架'\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in sfOptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔规格型号\" prop=\"gtggxh\">\n                  <el-input\n                    v-model=\"gtForm.gtggxh\"\n                    :placeholder=\"gtshow ? '' : '请输入杆塔规格型号'\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"坐标经度\" prop=\"jd\">\n                  <el-input\n                    v-model=\"gtForm.jd\"\n                    placeholder=\"请输入坐标经度\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"坐标纬度\" prop=\"wd\">\n                  <el-input\n                    v-model=\"gtForm.wd\"\n                    placeholder=\"请输入坐标纬度\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"同杆架设回路数\" prop=\"tgjshls\">\n                  <el-input\n                    v-model=\"gtForm.tgjshls\"\n                    :placeholder=\"gtshow ? '' : '请输入同杆架设回路数'\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"同杆线路位置\" prop=\"tgxlwz\">\n                  <el-input\n                    v-model=\"gtForm.tgxlwz\"\n                    :placeholder=\"gtshow ? '' : '请输入同杆线路位置'\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item\n                  label=\"是否换相\"\n                  prop=\"sfhx\"\n                  v-if=\"this.gtForm.dydj !== '6kV'\"\n                >\n                  <el-select\n                    v-model=\"gtForm.sfhx\"\n                    placeholder=\"请选择是否换相\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in sfOptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"投运日期\" class=\"add_sy_tyrq\" prop=\"tyrq\">\n                  <el-date-picker\n                    v-model=\"gtForm.tyrq\"\n                    type=\"date\"\n                    placeholder=\"选择日期\"\n                    format=\"yyyy-MM-dd\"\n                    value-format=\"yyyy-MM-dd\"\n                  >\n                  </el-date-picker>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"接地体材料\" prop=\"jdtcl\">\n                  <el-select\n                    v-model=\"gtForm.jdtcl\"\n                    :placeholder=\"gtshow ? '' : '请选择接地体材料'\"\n                    clearable\n                  >\n                    <el-option\n                    v-for=\"item in jdtclOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                    />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"导线型号\" prop=\"dxxh\">\n                  <el-input\n                    v-model=\"gtForm.dxxh\"\n                    :placeholder=\"gtshow ? '' : '请输入导线型号'\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item\n                  label=\"地线型号\"\n                  prop=\"jdxh\"\n                  v-if=\"this.gtForm.dydj !== '6kV'\"\n                >\n                  <el-input\n                    v-model=\"gtForm.jdxh\"\n                    placeholder=\"请输入地线型号\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"设备状态\" prop=\"sbzk\">\n                  <el-select\n                    v-model=\"gtForm.sbzt\"\n                    :placeholder=\"gtshow ? '' : '请选择设备状况'\"\n                  >\n                    <el-option\n                      v-for=\"item in gtsbzt\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔排序\" prop=\"gtnum\">\n                  <el-input\n                    v-model=\"gtForm.gtnum\"\n                    type=\"number\"\n                    placeholder=\"请输入杆塔排序\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"电缆型号\" prop=\"dlxh\">\n                  <el-input\n                    v-model=\"gtForm.dlxh\"\n                    :placeholder=\"gtshow ? '' : '请输入电缆型号'\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-form-item label=\"备注\" prop=\"bz\">\n                <el-input\n                  v-model=\"gtForm.bz\"\n                  type=\"textarea\"\n                  :row=\"2\"\n                  :placeholder=\"gtshow ? '' : '备注'\"\n                ></el-input>\n              </el-form-item>\n            </el-row>\n\n            <el-row :gutter=\"20\">\n              <el-form-item\n                label=\"已上传图片：\"\n                prop=\"attachment\"\n                v-if=\"gtForm.attachment.length > 0\"\n                id=\"pic_form\"\n              >\n                <el-col\n                  :span=\"24\"\n                  v-for=\"(item, index) in gtForm.attachment\"\n                  style=\"margin-left: 0\"\n                >\n                  <el-form-item :label=\"(index + 1).toString()\">\n                    {{ item.fileOldName }}\n                    <el-button\n                      v-if=\"!show\"\n                      type=\"error\"\n                      size=\"mini\"\n                      @click=\"deleteFileById(item.fileId)\"\n                      >删除</el-button\n                    >\n                  </el-form-item>\n                </el-col>\n              </el-form-item>\n            </el-row>\n            <el-row :gutter=\"20\">\n              <el-form-item label=\"上传图片：\" v-if=\"!show\">\n                <el-upload\n                  list-type=\"picture-card\"\n                  class=\"upload-demo\"\n                  accept=\".jpg,.png\"\n                  ref=\"upload\"\n                  :headers=\"upHeader\"\n                  action=\"/isc-api/file/upload\"\n                  :before-upload=\"beforeUpload\"\n                  :data=\"uploadData\"\n                  single\n                  :auto-upload=\"false\"\n                  multiple\n                >\n                  <el-button slot=\"trigger\" type=\"primary\">选取文件</el-button>\n                  <div slot=\"tip\" class=\"el-upload__tip\">\n                    只能上传jpg/png文件\n                  </div>\n                </el-upload>\n              </el-form-item>\n            </el-row>\n          </el-form>\n        </el-tab-pane>\n        <!--设备履历-->\n        <el-tab-pane label=\"设备履历\" name=\"sbRecord\">\n          <el-tabs v-model=\"sbllDescTabName\" type=\"card\">\n            <el-tab-pane label=\"隐患记录\" name=\"qxjl\">\n              <el-table\n                stripe\n                border\n                v-loading=\"loading\"\n                :data=\"sbllqxjlList\"\n                max-height=\"550\"\n              >\n                <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n                <el-table-column\n                  label=\"所属线路\"\n                  align=\"center\"\n                  prop=\"lineName\"\n                />\n                <el-table-column\n                  label=\"杆塔号\"\n                  align=\"center\"\n                  prop=\"gth\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"设备类型\"\n                  align=\"center\"\n                  prop=\"sblx\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"隐患性质\"\n                  align=\"center\"\n                  prop=\"qxxz\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"电压等级\"\n                  align=\"center\"\n                  prop=\"dydj\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column label=\"设备型号\" align=\"center\" prop=\"sbxh\" />\n                <el-table-column\n                  label=\"生产厂家\"\n                  align=\"center\"\n                  prop=\"sccj\"\n                  :show-overflow-tooltip=\"true\"\n                />\n              </el-table>\n            </el-tab-pane>\n            <el-tab-pane label=\"状态变更记录\" name=\"ztbgjl\">\n              <comp-table\n                :table-and-page-info=\"gtresumPageInfo\"\n                @getMethod=\"getResumList\"\n                @update:multipleSelection=\"handleSelectionChange\"\n                height=\"30vh\"\n              />\n            </el-tab-pane>\n          </el-tabs>\n        </el-tab-pane>\n      </el-tabs>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"gtDialogFormVisible = false\">取 消</el-button>\n        <el-button\n          v-if=\"title == '修改杆塔信息' || title == '新增杆塔信息'\"\n          type=\"primary\"\n          @click=\"addGtInfo\"\n          >确 定</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <!-- 杆塔弹出框开始展示设备履历 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"sbDialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"60%\"\n      @close=\"handleClose\"\n    >\n      <el-tabs v-model=\"activeTabName\">\n        <el-tab-pane label=\"基本信息\" name=\"sbDesc\">\n          <el-form\n            :model=\"jbxxForm\"\n            ref=\"jbxxForm\"\n            :disabled=\"show\"\n            :rules=\"rules\"\n            label-width=\"130px\"\n          >\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-form-item label=\"所属线路\" prop=\"lineName\">\n                  <el-input\n                    v-model=\"jbxxForm.lineName\"\n                    placeholder=\"请输入所属线路\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"所属杆塔\" prop=\"gtmc\">\n                  <el-input\n                    v-model=\"jbxxForm.gtmc\"\n                    placeholder=\"请输入所属杆塔\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"设备类型\" prop=\"sbflbm\">\n                  <el-select\n                    v-model=\"jbxxForm.sbflbm\"\n                    placeholder=\"请输入选择设备类型\"\n                    @change=\"showParams\"\n                  >\n                    <el-option\n                      v-for=\"item in sblxOptionsDataSelected\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"设备名称\" prop=\"sbmc\">\n                  <el-input\n                    v-model=\"jbxxForm.sbmc\"\n                    placeholder=\"请输入设备名称\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"投运日期\" prop=\"tyrq\">\n                  <el-date-picker\n                    v-model=\"jbxxForm.tyrq\"\n                    type=\"date\"\n                    placeholder=\"选择日期\"\n                    format=\"yyyy-MM-dd\"\n                    value-format=\"yyyy-MM-dd\"\n                  >\n                  </el-date-picker>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"规格型号\" prop=\"ggxh\">\n                  <el-input\n                    v-model=\"jbxxForm.ggxh\"\n                    :placeholder=\"show ? '' : '请选择规格型号'\"\n                  >\n                  </el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"状态\" prop=\"yxzt\">\n                  <el-select v-model=\"jbxxForm.yxzt\" placeholder=\"请选择状态\">\n                    <el-option\n                      v-for=\"item in sbzt\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"生产厂家\" prop=\"sccj\">\n                  <el-input\n                    v-model=\"jbxxForm.sccj\"\n                    :placeholder=\"show ? '' : '请输入生产厂家'\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row :gutter=\"20\">\n              <el-col :span=\"24\">\n                <el-form-item label=\"备注\" prop=\"bz\">\n                  <el-input\n                    v-model=\"jbxxForm.bz\"\n                    type=\"textarea\"\n                    rows=\"2\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </el-form>\n        </el-tab-pane>\n        <el-tab-pane label=\"技术参数\" name=\"sbjscs\">\n          <el-form :model=\"jscsForm\" label-width=\"130px\">\n            <el-col :span=\"8\" v-for=\"item in jscsLabelList\">\n              <el-form-item\n                :label=\"\n                  item.dw != '' ? item.label + '(' + item.dw + ')' : item.label\n                \"\n              >\n                <el-input\n                  v-if=\"item.type === 'input'\"\n                  v-model=\"jscsForm[item.jscsbm]\"\n                  placeholder=\"\"\n                >\n                </el-input>\n                <el-select\n                  v-if=\"item.type === 'select'\"\n                  v-model=\"jscsForm[item.jscsbm]\"\n                  placeholder=\"\"\n                >\n                  <el-option\n                    v-for=\"(childItem, key) in item.options\"\n                    :key=\"key\"\n                    :label=\"childItem.label\"\n                    :value=\"childItem.value\"\n                    :disabled=\"childItem.disabled\"\n                    style=\"display: flex; align-items: center;\"\n                    clearable\n                  >\n                  </el-option>\n                </el-select>\n                <el-date-picker\n                  v-if=\"item.type === 'date'\"\n                  v-model=\"jscsForm[item.jscsbm]\"\n                  type=\"date\"\n                  placeholder=\"选择日期\"\n                  value-format=\"yyyy-MM-dd\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-form>\n        </el-tab-pane>\n        <el-tab-pane label=\"设备履历\" name=\"sbRecord\">\n          <el-tabs v-model=\"sbllDescTabName\" type=\"card\">\n            <el-tab-pane label=\"缺陷记录\" name=\"qxjl\">\n              <el-table\n                stripe\n                border\n                v-loading=\"loading\"\n                :data=\"sbllqxjlList\"\n                max-height=\"550\"\n              >\n                <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n                <el-table-column\n                  label=\"所属线路\"\n                  align=\"center\"\n                  prop=\"lineName\"\n                />\n                <el-table-column\n                  label=\"杆塔号\"\n                  align=\"center\"\n                  prop=\"gth\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"设备类型\"\n                  align=\"center\"\n                  prop=\"sblx\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"缺陷性质\"\n                  align=\"center\"\n                  prop=\"qxxz\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"电压等级\"\n                  align=\"center\"\n                  prop=\"dydj\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column label=\"设备型号\" align=\"center\" prop=\"sbxh\" />\n                <el-table-column\n                  label=\"生产厂家\"\n                  align=\"center\"\n                  prop=\"sccj\"\n                  :show-overflow-tooltip=\"true\"\n                />\n              </el-table>\n            </el-tab-pane>\n            <el-tab-pane label=\"状态变更记录\" name=\"ztbgjl\">\n              <comp-table\n                :table-and-page-info=\"resumPageInfo\"\n                @getMethod=\"getResumList\"\n                @update:multipleSelection=\"handleSelectionChange\"\n                height=\"30vh\"\n              />\n            </el-tab-pane>\n          </el-tabs>\n        </el-tab-pane>\n      </el-tabs>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"handleClose\">取 消</el-button>\n        <el-button\n          v-if=\"title == '修改输电设备信息' || title == '新增输电设备信息'\"\n          type=\"primary\"\n          @click=\"submit\"\n          class=\"pmyBtn\"\n          >确 定</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <!-- 修改设备状态变更-->\n    <el-dialog\n      title=\"设备状态变更\"\n      :visible.sync=\"dialogVisible\"\n      width=\"30%\"\n      append-to-body\n    >\n      <el-form :inline=\"true\" label-width=\"100px\" class=\"qxlr_dialog_insert\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"设备状态:\">\n              <el-select v-model=\"updateList.sbzt\">\n                <el-option\n                  v-for=\"item in sbzt\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitStatus\">确 定</el-button>\n      </span>\n    </el-dialog>\n\n    <el-dialog\n      :title=\"ExcelImportTitle\"\n      :visible.sync=\"openExcelDialog\"\n      width=\"700px\"\n      :close-on-click-modal=\"false\"\n      v-dialogDrag\n    >\n      <el-form label-position=\"right\" label-width=\"180px\">\n        <el-row>\n          <el-col :span=\"17\">\n            <el-form-item label=\"上传文件\" prop=\"fileName\">\n              <el-input\n                v-model=\"fileName\"\n                :readonly=\"true\"\n                placeholder=\"请选择文件\"\n                style=\"width:200px;\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-upload\n              action=\"/manager-api/tower/importExcel\"\n              ref=\"upload\"\n              :data=\"uploadData\"\n              accept=\".xls,.xlsx\"\n              :limit=\"1\"\n              :file-list=\"fileList\"\n              :auto-upload=\"false\"\n              :on-success=\"uploadSuccess\"\n              :on-change=\"handleChange\"\n              :on-remove=\"handleRemove\"\n              :headers=\"upHeader\"\n              :on-exceed=\"handleExceed\"\n            >\n              <el-button slot=\"trigger\" type=\"primary\" plain\n                >选取文件</el-button\n              >\n              <div\n                slot=\"tip\"\n                class=\"el-upload__tip\"\n                style=\"width: 220px;color: red\"\n              >\n                只能上传excel文件，且不超过100MB\n              </div>\n            </el-upload>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\" style=\"margin-top: 30px\">\n        <el-button type=\"primary\" @click=\"submitExcelForm\" :loading=\"isloading\"\n          >确 定</el-button\n        >\n        <el-button @click=\"cancelImport\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"addTowerBatchDialogFormVisible\"\n      width=\"50%\"\n      v-dialogDrag\n    >\n      <el-form\n        label-width=\"120px\"\n        ref=\"addTowerBatchForm\"\n        :model=\"addTowerBatchForm\"\n        :rules=\"addTowerBatchRules\"\n      >\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"towerStartNum\" label=\"杆塔起始编号\">\n              <el-input-number\n                :min=\"1\"\n                :max=\"99\"\n                style=\"width: 100%\"\n                v-model=\"addTowerBatchForm.towerStartNum\"\n                placeholder=\"请输入杆塔起始编号\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"towerEndNum\" label=\"杆塔结束编号\">\n              <el-input-number\n                :min=\"this.addTowerBatchForm.towerStartNum\"\n                :max=\"99\"\n                style=\"width: 100%\"\n                v-model=\"addTowerBatchForm.towerEndNum\"\n                placeholder=\"请输入杆塔结束编号\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"towerNamePrefix\" label=\"杆塔名称前缀\">\n              <el-input\n                style=\"width: 100%\"\n                v-model=\"addTowerBatchForm.towerNamePrefix\"\n                placeholder=\"请输入杆塔名称前缀\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"towerNameSuffix\" label=\"杆塔名称后缀\">\n              <el-input\n                style=\"width: 100%\"\n                v-model=\"addTowerBatchForm.towerNameSuffix\"\n                placeholder=\"请输入杆塔名称后缀\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"towerNameLinkFlag\" label=\"杆塔名称连接符\">\n              <el-input\n                style=\"width: 100%\"\n                v-model=\"addTowerBatchForm.towerNameLinkFlag\"\n                placeholder=\"请输入杆塔名称连接符\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"towerNumberPrefix\" label=\"杆塔编号前缀\">\n              <el-input\n                style=\"width: 100%\"\n                v-model=\"addTowerBatchForm.towerNumberPrefix\"\n                placeholder=\"请输入杆塔编号前缀\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"towerNumberSuffix\" label=\"杆塔编号后缀\">\n              <el-input\n                style=\"width: 100%\"\n                v-model=\"addTowerBatchForm.towerNumberSuffix\"\n                placeholder=\"请输入编号后缀\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"lineName\" label=\"杆塔所属线路\">\n              <el-input\n                disabled\n                style=\"width: 100%\"\n                v-model=\"addTowerBatchForm.lineName\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <!-- <el-form-item prop=\"towerNameExample\" label=\"杆塔名称生成示例: \">\n              <el-input\n                disabled\n                style=\"width: 100%\"\n                v-model=\"towerNameExample\"\n              />\n            </el-form-item> -->\n            <h2>杆塔名称生成示例:{{ towerNameExample }}</h2>\n          </el-col>\n          <el-col :span=\"12\">\n            <!-- <el-form-item prop=\"towerNumberExample\" label=\"杆塔编号生成示例: \">\n              <el-input\n                disabled\n                style=\"width: 100%\"\n                v-model=\"towerNumberExample\"\n              />\n            </el-form-item> -->\n            <h2>杆塔编号生成示例:{{ towerNumberExample }}</h2>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"addTowerBatchDialogFormVisible = false\"\n          >关 闭</el-button\n        >\n        <el-button type=\"primary\" @click=\"saveTowerBatchButton\"\n          >确 认</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <el-dialog title=\"支线维护\" :visible.sync=\"showZxwh\" width=\"60%\" v-dialogDrag @close=\"closeZxwhFun\" v-if=\"showZxwh\">\n      <zxwh :xl-data=\"xlData\"></zxwh>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  getList,\n  getResumDataList,\n  getTreeList,\n  remove,\n  saveOrUpdate,\n  updateStatus,\n  adddwzyfstz,\n  exportExcel,\n  importExcel\n} from \"@/api/dagangOilfield/asset/sdsb\";\nimport {\n  getListxl,\n  saveOrUpdatexl,\n  xlremove\n} from \"@/api/dagangOilfield/asset/sdxl\";\nimport {\n  getListgt,\n  saveOrUpdategt,\n  gtremove,\n  saveTowerBatch\n} from \"@/api/dagangOilfield/asset/sdgt\";\nimport {\n  getParamDataList,\n  getParamsValue,\n  saveParamValue\n} from \"@/api/dagangOilfield/asset/parameters\";\nimport {\n  getSblxDataListSelected,\n  getOrganizationSelected\n} from \"@/api/dagangOilfield/asset/bdsbtz\";\nimport { getListTZ } from \"@/api/dagangOilfield/bzgl/bzgfgl/bzgfgl\";\nimport { getToken } from \"@/utils/auth\";\nimport zxwh from \"@/views/dagangOilfield/dwzygl/sdsbgl/zxwh\";\nimport {deleteById} from \"@/api/tool/file\";\nimport {getDictTypeData} from \"@/api/system/dict/data\";\n\nexport default {\n  name: \"qxbzk\",\n  components: {zxwh},\n  data() {\n    var validateTowerEndNum = (rule, value, callback) => {\n      if (!this.addTowerBatchForm.towerEndNum) {\n        callback(new Error(\"不能为空\"));\n      }\n      if (\n        this.addTowerBatchForm.towerEndNum <\n        this.addTowerBatchForm.towerStartNum\n      ) {\n        callback(new Error(\"杆塔结束编号不能小于起始编号\"));\n      } else {\n        callback();\n      }\n    };\n    return {\n      ids: [],\n\n      icons: {\n        1: \"categoryTreeIcons\",\n        2: \"tableIcon\",\n        3: \"classIcon\",\n        4: \"classIcon2\"\n      },\n      params: {\n        pageNum: 1,\n        pageSize: 10,\n      },\n      resumeQuery: {\n        foreignNum: undefined,\n        sblx: undefined\n      },\n      updateList: {\n        sbzt: \"\",\n        objId: \"\"\n      },\n      dialogVisible: false,\n      options: [\n        {\n          value: \"110kV\",\n          label: \"110kV\"\n        },\n        {\n          value: \"35kV\",\n          label: \"35kV\"\n        },\n        {\n          value: \"10kV\",\n          label: \"10kV\"\n        },\n        {\n          value: \"6kV\",\n          label: \"6kV\"\n        }\n      ],\n      sbzt: [\n        {\n          value: \"在运\",\n          label: \"在运\"\n        },\n        {\n          value: \"停运\",\n          label: \"停运\"\n        },\n        {\n          value: \"报废\",\n          label: \"报废\"\n        }\n      ],\n\n      //电压等级下拉框\n      voltageLevelListSelected: [],\n      deviceNameOptions: [],\n      sbfilterInfo: {\n        data: {\n          ssxl: [],\n          ssgs: [],\n          sbzt: \"\",\n          yxbz: []\n        },\n        fieldList: [\n          { label: \"所属杆塔名称\", type: \"input\", value: \"gtmc\" },\n          { label: \"设备类型\", type: \"input\", value: \"sblxmc\" },\n          { label: \"设备名称\", type: \"input\", value: \"sbmc\" },\n          {\n            label: \"运行状态\",\n            type: \"select\",\n            value: \"yxzt\",\n            options: [\n              { label: \"在运\", value: \"110kV\" },\n              { label: \"停运\", value: \"35kV\" }\n            ]\n          },\n          {\n            label: \"投运日期\",\n            type: \"date\",\n            value: \"tyrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"生产厂家\", type: \"input\", value: \"sscj\" }\n        ]\n      },\n\n      tableAndPageInfo3: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"lineName\", label: \"所属线路\", minWidth: \"120\" },\n          { prop: \"gtmc\", label: \"所属杆塔名称\", minWidth: \"120\" },\n          { prop: \"sblxmc\", label: \"设备类型\", minWidth: \"120\" },\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"180\" },\n          { prop: \"ggxh\", label: \"规格型号\", minWidth: \"180\" },\n          { prop: \"yxzt\", label: \"运行状态\", minWidth: \"120\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"250\" },\n          { prop: \"sccj\", label: \"生产厂家\", minWidth: \"140\" }\n          /*{\n            fixed:\"right\",\n            prop: 'operation',\n            label: '操作',\n            minWidth: '130px',\n            style: {display: 'block'},\n            operation: [\n              {name: '修改', clickFun: this.updateRow},\n              {name: '详情', clickFun: this.detailsInfo}\n            ]\n          },*/\n        ]\n      },\n\n      resumPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"foreignNum\", label: \"设备名称\", minWidth: \"120\" },\n          { prop: \"sblx\", label: \"设备类型\", minWidth: \"180\" },\n          { prop: \"bglx\", label: \"变更类型\", minWidth: \"120\" },\n          { prop: \"ms\", label: \"描述\", minWidth: \"250\" },\n          { prop: \"bgr\", label: \"变更人\", minWidth: \"140\" },\n          { prop: \"bgsj\", label: \"变更时间\", minWidth: \"140\" }\n        ]\n      },\n\n      //杆塔挂接设备tab页\n      gtgjsbTabName: \"jc\",\n      //杆塔详情弹出框\n      sbDialogFormVisible: false,\n      //设备履历状态变更记录\n      sbllztbgjlList: [],\n      //设备履历缺陷记录数据集合\n      sbllqxjlList: [\n        /*{\n          ssgs: '110kVXXX线路',\n          gth: '001#',\n          sblx: '杆塔',\n          qxxz: '严重',\n          dydj: '35kV',\n          sbxh: 'XXX型号',\n          sccj: 'XXX厂家'\n        }, {\n          ssgs: '110kVXXX线路',\n          gth: '001#',\n          sblx: '杆塔',\n          qxxz: '严重',\n          dydj: '35kV',\n          sbxh: 'XXX型号',\n          sccj: 'XXX厂家'\n        }, {\n          ssgs: '110kVXXX线路',\n          gth: '001#',\n          sblx: '杆塔',\n          qxxz: '严重',\n          dydj: '35kV',\n          sbxh: 'XXX型号',\n          sccj: 'XXX厂家'\n        }, {\n          ssgs: '110kVXXX线路',\n          gth: '001#',\n          sblx: '杆塔',\n          qxxz: '严重',\n          dydj: '35kV',\n          sbxh: 'XXX型号',\n          sccj: 'XXX厂家'\n        }, {\n          ssgs: '110kVXXX线路',\n          gth: '001#',\n          sblx: '杆塔',\n          qxxz: '严重',\n          dydj: '35kV',\n          sbxh: 'XXX型号',\n          sccj: 'XXX厂家'\n        }*/\n      ],\n      //设备履历试验记录数据\n      sblvsyjlList: [\n        /* {\n          syzy: '带电',\n          syxz: '例行试验',\n          symc: 'XXXXX',\n          gzdd: 'XXX平台',\n          sysb: '主变压器',\n          sybg: '',\n          tq: '晴',\n          syrq: '2022-01-01',\n          lrr: '张三',\n          syjl: 'XXXXX'\n        }, {\n          syzy: '带电',\n          syxz: '例行试验',\n          symc: 'XXXXX',\n          gzdd: 'XXX平台',\n          sysb: '主变压器',\n          sybg: '',\n          tq: '晴',\n          syrq: '2022-01-01',\n          lrr: '张三',\n          syjl: 'XXXXX'\n        }, {\n          syzy: '带电',\n          syxz: '例行试验',\n          symc: 'XXXXX',\n          gzdd: 'XXX平台',\n          sysb: '主变压器',\n          sybg: '',\n          tq: '晴',\n          syrq: '2022-01-01',\n          lrr: '张三',\n          syjl: 'XXXXX'\n        }, {\n          syzy: '带电',\n          syxz: '例行试验',\n          symc: 'XXXXX',\n          gzdd: 'XXX平台',\n          sysb: '主变压器',\n          sybg: '',\n          tq: '晴',\n          syrq: '2022-01-01',\n          lrr: '张三',\n          syjl: 'XXXXX'\n        }, {\n          syzy: '带电',\n          syxz: '例行试验',\n          symc: 'XXXXX',\n          gzdd: 'XXX平台',\n          sysb: '主变压器',\n          sybg: '',\n          tq: '晴',\n          syrq: '2022-01-01',\n          lrr: '张三',\n          syjl: 'XXXXX'\n        }, {\n          syzy: '带电',\n          syxz: '例行试验',\n          symc: 'XXXXX',\n          gzdd: 'XXX平台',\n          sysb: '主变压器',\n          sybg: '',\n          tq: '晴',\n          syrq: '2022-01-01',\n          lrr: '张三',\n          syjl: 'XXXXX'\n        }*/\n      ],\n      //设备履历tab页\n      sbllDescTabName: \"qxjl\",\n      show: false,\n      //设备基本信息表单\n      jbxxForm: {\n        objId: undefined,\n        ssxl: \"\",\n        ssgtbh: \"\"\n      },\n      //设备详情页底部确认取消按钮控制\n      sbCommitDialogCotrol: true,\n      //弹出框tab页\n      activeTabName: \"sbDesc\",\n      //变电站展示\n      bdzShowTable: true,\n      //间隔展示\n      jgShowTable: false,\n      //设备展示\n      sbShowTable: false,\n      //设备弹出框\n      dialogFormVisible: false,\n      //变电站添加按钮弹出框\n      bdzDialogFormVisible: false,\n      //间隔添加按钮弹出框\n      jgDialogFormVisible: false,\n      //弹出框表单\n      form: {},\n\n      loading: false,\n      //组织树\n      treeOptions: [],\n\n      selectRows: [],\n      //变电站挂接数据\n      newTestData: [],\n\n      //删除是否可用\n      multipleSensor: true,\n      //查询参数\n      /*       queryParams: {\n               pageNum: 1,\n               pageSize: 10,\n               roleKey: '',\n               roleName: '',\n               status: '',\n             },*/\n      showSearch: true,\n      //ssgtbh sbdm sbmc sbflbm bgr tyrq zcxz yxzt dydj sccj ccbh ccrq ggxh\n      rules: {\n        // ssgtbh:[{required:true,message:'请输入所属杆塔',trigger:'blur'}],\n        // sbdm:[{required:true,message:'请输入设备代码',trigger:'blur'}],\n        sbmc: [{ required: true, message: \"请输入设备名称\", trigger: \"blur\" }],\n        sbflbm: [\n          { required: true, message: \"请选择设备类型\", trigger: \"change\" }\n        ]\n        // bgr:[{required:true,message:'请输入保管人',trigger:'blur'}],\n        // yxzt:[{required:true,message:'请选择状态',trigger:'change'}],\n        // dydj:[{required:true,message:'请选择电压等级',trigger:'change'}],\n        // sccj:[{required:true,message:'请输入生产厂家',trigger:'blur'}],\n        // ccbh:[{required:true,message:'请输入出场编号',trigger:'blur'}]\n      },\n\n      jscsLabelList: [],\n      //技术参数绑定\n      jscsForm: {},\n      paramQuery: {\n        sblxbm: undefined\n      },\n      sblxOptionsDataSelected: {},\n      xltzData: true,\n      sbtzData: false,\n      gttzData: false,\n      title: \"\",\n      //线路数据相关\n      tableAndPageInfo1: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"lineName\", label: \"线路名称\", minWidth: \"120\" },\n          { prop: \"dydj\", label: \"电压等级\", minWidth: \"120\" },\n          { prop: \"lineStatus\", label: \"线路状态\", minWidth: \"120\" },\n          { prop: \"lineType\", label: \"线路类型\", minWidth: \"120\" },\n          // {prop: 'sfzgx', label: '是否主干线', minWidth: '140'},\n          { prop: \"totalLength\", label: \"线路全长(KM)\", minWidth: \"120\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"120\" }\n          /*{\n            fixed: \"right\",\n            prop: 'operation',\n            label: '操作',\n            minWidth: '130px',\n            style: {display: 'block'},\n            operation: [\n              {name: '修改', clickFun: this.updateRow1},\n              {name: '详情', clickFun: this.detailsInfo1}\n            ]\n          },*/\n        ]\n      },\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        dydjbm: \"\",\n        lineName: \"\",\n        lineType: \"\",\n        lineStatus: \"\"\n      },\n      //设备弹出框\n      xlDialogFormVisible: false,\n      xlshow: false,\n      //设备基本信息\n      xlForm: {\n        objId: undefined\n      },\n      //线路类型\n      lineTypeOptions: [\n        { label: \"输电线路\", value: \"输电线路\" },\n        { label: \"配电线路\", value: \"配电线路\" }\n      ],\n      //线路状态\n      xlztOptions: [\n        { label: \"在运\", value: \"在运\" },\n        { label: \"停运\", value: \"停运\" }\n      ],\n      xloptions: [\n        {\n          value: \"110\",\n          label: \"110kV\"\n        },\n        {\n          value: \"35\",\n          label: \"35kV\"\n        },\n        {\n          value: \"10\",\n          label: \"10kV\"\n        },\n        {\n          value: \"6\",\n          label: \"6kV\"\n        }\n      ],\n      xlfilterInfo: {\n        data: {\n          dydjbm: \"\",\n          lineName: \"\",\n          lineType: \"\",\n          // sfzgx: '',\n          lineStatus: \"\"\n        },\n        fieldList: [\n          { label: \"线路名称\", type: \"input\", value: \"lineName\" },\n          {\n            label: \"线路全长\",\n            type: \"input\",\n            value: \"totalLength\",\n            options: []\n          },\n          {\n            label: \"投运日期\",\n            type: \"date\",\n            format: \"yyyy-MM-dd\",\n            options: []\n          },\n          {\n            label: \"电压等级\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"dydjbm\",\n            options: [\n              { label: \"110kV\", value: \"110\" },\n              { label: \"35kV\", value: \"35\" },\n              { label: \"10kV\", value: \"10\" },\n              { label: \"6kV\", value: \"6\" }\n            ]\n          },\n          {\n            label: \"状态\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"lineStatus\",\n            options: [\n              { label: \"在运\", value: \"在运\" },\n              { label: \"停运\", value: \"停运\" }\n            ]\n          },\n          {\n            label: \"线路类型\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"lineType\",\n            options: [\n              { label: \"输电线路\", value: \"输电线路\" },\n              { label: \"配电线路\", value: \"配电线路\" }\n            ]\n          }\n          // {\n          //   label: '是否主干线',\n          //   type: 'select',\n          //   value: 'sfzgx',\n          //   options: [{label: \"是\", value: \"是\"}, {label: \"否\", value: \"否\"},]\n          // },\n        ]\n      },\n      //查询杆塔参数   杆塔数据相关开始\n      //杆塔详情弹出框\n      gtDialogFormVisible: false,\n      //弹出框tab页\n      gtactiveTabName: \"sbDesc\",\n      //轮播图片\n      imgList: [],\n      xlImgList: [\n        //         {\n        //   fileUrl: require('@/assets/image/bdz.png')\n        // },\n        // {\n        //   fileUrl: require('@/assets/image/bdz.png')\n        // },\n        // {\n        //   fileUrl: require('@/assets/image/bdz.png')\n        // }\n      ],\n      queryGtParam: {\n        sbzt: \"\",\n        lineName: \"\",\n        dydj: \"\",\n        pageNum: 1,\n        pageSize: 10,\n        // mySorts: [{ prop: \"get_number(gtbh) + 1\", asc: true }]\n      },\n      // 文件上传数据\n      // uploadData: {\n      //   type: \"\",\n      //   businessId: undefined,\n      //   lineName : this.lineName\n      // },\n      // 文件上传请求头\n      upHeader: { token: getToken() },\n      //  文件导入弹出框相关\n      ExcelImportTitle: \"Excel导入\",\n      openExcelDialog: false,\n      // 文件上传请求头\n      //文件名\n      fileName: \"\",\n      //上传得文件数组\n      fileList: [],\n      isloading: false,\n      tableAndPageInfo2: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"lineName\", label: \"所属线路\", minWidth: \"120\" },\n          { prop: \"gtmc\", label: \"杆塔名称\", minWidth: \"130\" },\n          { prop: \"gtbh\", label: \"杆塔编号\", minWidth: \"90\",  },\n          { prop: \"dydj\", label: \"电压等级\", minWidth: \"60\" },\n          { prop: \"sbzt\", label: \"设备状态\", minWidth: \"60\" },\n          { prop: \"gtcz\", label: \"杆塔材质\", minWidth: \"70\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"80\" },\n          { prop: \"gtnum\", label: \"杆塔排序\", minWidth: \"70\",  }\n          /*{\n            fixed: 'right',\n            prop: 'operation',\n            label: '操作',\n            minWidth: '130px',\n            style: { display: 'block' },\n            operation: [\n              { name: '状态变更', clickFun: this.updateStatus },\n              { name: '修改', clickFun: this.updateRow2 },\n              { name: '详情', clickFun: this.detailsInfo2 }\n            ]\n          }*/\n        ]\n      },\n      //设备基本信息\n      gtForm: {\n        attachment: [],\n        objId: undefined,\n        gtbh: \"\",\n        ssbm: \"线路分公司\"\n      },\n      gtfilterInfo: {\n        data: {\n          sbzt: \"\",\n          lineName: \"\",\n          dydj: \"\"\n        },\n        fieldList: [\n          {\n            label: \"杆塔名称\",\n            type: \"input\",\n            value: \"gtmc\",\n            options: []\n          },\n          {\n            label: \"杆塔编号\",\n            type: \"input\",\n            value: \"gtbh\",\n            options: []\n          },\n          {\n            label: \"电压等级\",\n            type: \"select\",\n            value: \"dydj\",\n            options: [\n              { label: \"6kV\", value: \"6kV\" },\n              { label: \"10kV\", value: \"10kV\" },\n              {\n                label: \"35kV\",\n                value: \"35kV\"\n              },\n              { label: \"110kV\", value: \"110kV\" }\n            ]\n          },\n          {\n            label: \"设备状态\",\n            type: \"select\",\n            value: \"sbzt\",\n            options: [\n              { label: \"在运\", value: \"在运\" },\n              { label: \"停运\", value: \"停运\" }\n            ]\n          },\n          {\n            label: \"杆塔材质\",\n            type: \"input\",\n            value: \"gtcz\",\n            options: []\n          },\n          {\n            label: \"投运日期\",\n            type: \"date\",\n            value: \"tyrq\",\n            format: \"yyyy-MM-dd\"\n          }\n        ]\n      },\n      gtshow: false,\n      gtoptions: [],\n      gtsbzt: [],\n      //杆塔性质\n      gtxzList: [],\n      //杆塔形状结合\n      gtxzOptions: [],\n      uploadData: {\n        businessId: undefined\n      },\n      gtresumeQuery: {\n        foreignNum: undefined,\n        sblx: undefined\n      },\n      gtresumPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"foreignNum\", label: \"设备名称\", minWidth: \"120\" },\n          { prop: \"sblx\", label: \"设备类型\", minWidth: \"180\" },\n          { prop: \"bglx\", label: \"变更类型\", minWidth: \"120\" },\n          { prop: \"ms\", label: \"描述\", minWidth: \"250\" },\n          { prop: \"bgr\", label: \"变更人\", minWidth: \"140\" },\n          { prop: \"bgsj\", label: \"变更时间\", minWidth: \"140\" }\n        ]\n      },\n      xlRules: {\n        xlbm: [\n          { required: true, message: \"线路编码不能为空\", trigger: \"blur\" }\n        ],\n        lineName: [\n          { required: true, message: \"线路名称不能为空\", trigger: \"blur\" }\n        ],\n        lineType: [\n          { required: true, message: \"线路专业不能为空\", trigger: \"select\" }\n        ],\n        dydjbm: [\n          { required: true, message: \"电压等级不能为空\", trigger: \"select\" }\n        ],\n        totalLength: [\n          { required: true, message: \"线路全长不能为空\", trigger: \"blur\" }\n        ],\n        lineStatus: [\n          { required: true, message: \"线路状态不能为空\", trigger: \"select\" }\n        ]\n      },\n      gtRules: {\n        gtbh: [\n          { required: true, message: \"杆塔编号不能为空\", trigger: \"blur\" }\n        ],\n        gtnum: [\n          { required: true, message: \"杆塔排序不能为空\", trigger: \"blur\" }\n        ],\n        gtmc: [\n          { required: true, message: \"杆塔名称不能为空\", trigger: \"blur\" }\n        ],\n        lineName: [\n          { required: true, message: \"所属线路不能为空\", trigger: \"blur\" }\n        ],\n        gtcz: [\n          { required: true, message: \"杆塔材质不能为空\", trigger: \"select\" }\n        ],\n        yxbz: [\n          { required: true, message: \"运行班组不能为空\", trigger: \"select\" }\n        ],\n        ssbm: [\n          { required: true, message: \"所属部门不能为空\", trigger: \"blur\" }\n        ],\n        dydj: [\n          { required: true, message: \"电压等级不能为空\", trigger: \"select\" }\n        ]\n      },\n      dydjbm: \"\",\n      lineName: \"\",\n      linedIdStore: \"\",\n      gtbhStore: \"\",\n      gtmcStore: \"\",\n      addTowerBatchForm: {\n        towerNamePrefix: \"\",\n        towerNameSuffix: \"\",\n        towerNameLinkFlag: \"\",\n        towerNumberPrefix: \"\",\n        towerNumberSuffix: \"\",\n        towerStartNum: 1\n      },\n      addTowerBatchDialogFormVisible: false,\n      addTowerBatchRules: {\n        lineName: [\n          { required: true, message: \"所属线路不能为空\", trigger: \"blur\" }\n        ],\n        towerNamePrefix: [\n          { required: true, message: \"不能为空\", trigger: \"blur\" }\n        ],\n        towerNameSuffix: [\n          { required: true, message: \"不能为空\", trigger: \"blur\" }\n        ],\n        towerStartNum: [\n          { required: true, message: \"不能为空\", trigger: \"change\" }\n        ],\n        towerEndNum: [\n          { required: true, trigger: \"change\", validator: validateTowerEndNum }\n        ]\n      },\n      xlData:{},//传给支线维护页面的线路数据\n      showZxwh:false,//是否显示支线维护弹框\n      gtczOptions:[],//杆塔材质下拉框\n      gtxsOptions:[],//杆塔形式下拉框\n      xxOptions:[],//相序下拉框\n      dxplOptions:[],//导线排列下拉框\n      sfOptions:[],//是/否\n      jdtclOptions:[],//接地体材料\n    };\n  },\n  watch: {},\n  computed: {\n    towerNumberExample: function() {\n      return (\n        this.addTowerBatchForm.towerNumberPrefix +\n        this.addTowerBatchForm.towerStartNum +\n        this.addTowerBatchForm.towerNumberSuffix\n      );\n    },\n    towerNameExample: function() {\n      return (\n        this.addTowerBatchForm.towerNamePrefix +\n        this.addTowerBatchForm.towerStartNum +\n        this.addTowerBatchForm.towerNameLinkFlag +\n        this.addTowerBatchForm.towerNameSuffix\n      );\n    }\n  },\n  created() {\n    //初始化加载时加载所有变电站信息\n    this.treeList();\n    this.getSblxDataListSelected();\n    this.lineTzData(this.$route.query); //初始请求线路台账\n    this.getOptions();//获取下拉框字典\n  },\n  mounted() {\n    this.getOrganizationSelected();\n  },\n  methods: {\n    async getOptions(){\n      await this.getDydjList();//电压等级\n      await this.getGtczList();//杆塔材质\n      await this.getGtxsList();//杆塔形式\n      await this.getGtxzList();//杆塔性质\n      await this.getGtxzList1();//杆塔形状\n      await this.getGtxxList();//杆塔相序\n      await this.getDxplList();//导线排列\n      await this.getSfList();//是/否\n      await this.getJdtclList();//接地体材料\n      await this.getGtztList();//杆塔状态\n    },\n    getDydjList(){\n      getDictTypeData('gttz-dydj').then(res=>{\n        res.data.forEach(item=>{\n          this.gtoptions.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    getGtczList(){\n      getDictTypeData('gttz-gtcz').then(res=>{\n        res.data.forEach(item=>{\n          this.gtczOptions.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    getGtxsList(){\n      getDictTypeData('gttz-gtxs').then(res=>{\n        res.data.forEach(item=>{\n          this.gtxsOptions.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    getGtxzList(){\n      getDictTypeData('gttz-gtxz').then(res=>{\n        res.data.forEach(item=>{\n          this.gtxzList.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    getGtxzList1(){\n      getDictTypeData('gttz-gtxz1').then(res=>{\n        res.data.forEach(item=>{\n          this.gtxzOptions.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    getGtxxList(){\n      getDictTypeData('gttz-gtxx').then(res=>{\n        res.data.forEach(item=>{\n          this.xxOptions.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    getDxplList(){\n      getDictTypeData('gttz-dxpl').then(res=>{\n        res.data.forEach(item=>{\n          this.dxplOptions.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    getSfList(){\n      getDictTypeData('sys_sf').then(res=>{\n        res.data.forEach(item=>{\n          this.sfOptions.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    getJdtclList(){\n      getDictTypeData('gttz-jdtcl').then(res=>{\n        res.data.forEach(item=>{\n          this.jdtclOptions.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    getGtztList(){\n      getDictTypeData('gttz-gtzt').then(res=>{\n        res.data.forEach(item=>{\n          this.gtsbzt.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    //关闭支线维护弹框\n    closeZxwhFun(){\n      this.xlData = {};\n      this.showZxwh = false;\n    },\n    //支线维护方法\n    zxwhFun(row){\n      this.xlData = {...row};\n      this.showZxwh = true;\n    },\n    getVoltageLeVelList() {\n      this.sbfilterInfo.fieldList.forEach(item => {\n        if (item.value == \"ssgs\") {\n          item.options = [\n            { label: \"110kV\", value: \"110kV\" },\n            { label: \"35kV\", value: \"35kV\" },\n            { label: \"10kV\", value: \"10kV\" },\n            { label: \"6kV\", value: \"6kV\" }\n          ];\n        }\n      });\n    },\n    //获取班组\n    getOrganizationSelected() {\n      let parentId = \"3010\"; //线路分公司\n      getOrganizationSelected({ parentId: parentId }).then(res => {\n        res.data.forEach(item => {\n          this.deviceNameOptions.push({\n            label: item.label,\n            value: item.value.toString()\n          });\n        });\n        this.sbfilterInfo.fieldList.forEach(item => {\n          if (item.value === \"bz\") {\n            item.options = this.deviceNameOptions;\n            return false;\n          }\n        });\n      });\n    },\n    //保存设备基本信息\n    addEquipInfo: function() {\n      this.jbxxForm.sbClassCsValue = this.jscsForm;\n\n      saveOrUpdate(this.jbxxForm).then(res => {\n        if (res.code == \"0000\") {\n          this.$message.success(\"操作成功\");\n          this.sbDialogFormVisible = false;\n          this.getData({ ssgtbh: this.jbxxForm.ssgtbh });\n          return;\n        } else {\n          this.$message.warning(\"操作失败！\");\n        }\n      });\n    },\n    //初始进来发请求线路台账接口\n    lineTzData(params) {\n      this.queryParams = { ...this.queryParams, ...params };\n      const param = { ...this.queryParams, ...params };\n      getListxl(param).then(res => {\n        this.xltzData = true;\n        this.gttzData = this.sbtzData = false;\n        this.tableAndPageInfo1.tableData = res.data.records;\n        this.tableAndPageInfo1.pager.total = res.data.total;\n      });\n    },\n    //图纸查询\n    querySearch(queryString, cb) {\n      let param = {\n        // wjbh: queryString,\n        wjmc: queryString,\n        dydj: this.xlForm.dydjbm,\n        islast: \"1\",\n        wjlx: \"1\",\n        mySorts: [{ prop: \"updateTime\", asc: false }]\n      };\n      getListTZ(param).then(res => {\n        if (res.code === \"0000\") {\n          // 调用 callback 返回建议列表的数据\n          let data = res.data.records;\n          data.forEach(record => {\n            record.value = record.wjbh;\n          });\n          cb(data);\n        }\n      });\n    },\n    handleSelect(item) {\n      this.xlImgList = item.fileList.filter(\n        record => record.fileType !== \"vsd\"\n      );\n    },\n    //杆塔台账\n    async gtTzData(params) {\n      try {\n        this.queryGtParam = {\n          ...this.queryGtParam,\n          ...params,\n          ...{ lineName: this.lineName }\n        };\n        getListgt(this.queryGtParam).then(res => {\n          this.gttzData = true;\n          this.xltzData = this.sbtzData = false;\n          this.tableAndPageInfo2.tableData = res.data.records;\n          this.tableAndPageInfo2.pager.total = res.data.total;\n        });\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    sortChangeTowerData({ column, prop, order }) {\n      if (order) {\n        if (order.indexOf(\"desc\") > -1) {\n          if (prop === \"gtbh\") {\n            this.queryGtParam.mySorts = [\n              { prop: \"get_number(gtbh) + 0\", asc: false }\n            ];\n          } else {\n            this.queryGtParam.mySorts = [{ prop: prop, asc: false }];\n          }\n        } else {\n          if (prop === \"gtbh\") {\n            this.queryGtParam.mySorts = [\n              { prop: \"get_number(gtbh) + 0\", asc: true }\n            ];\n          } else {\n            this.queryGtParam.mySorts = [{ prop: prop, asc: true }];\n          }\n        }\n      } else {\n        this.queryGtParam.mySorts = [{ prop: \"get_number(gtbh)\", asc: true }];\n      }\n      this.gtTzData(this.gtfilterInfo.data);\n    },\n    //设备台账\n    sbTzData(params) {\n      const param = { ...this.params, ...params,...{ ssgtbh: this.gtbhStore } };\n      getList(param).then(res => {\n        this.sbtzData = true;\n        this.gttzData = this.xltzData = false;\n        this.tableAndPageInfo3.tableData = res.data.records;\n        this.tableAndPageInfo3.pager.total = res.data.total;\n      });\n    },\n    //获取列表数据\n    getData: function(params) {\n      if (this.xltzData) {\n        this.lineTzData(params);\n      }\n      if (this.gttzData) {\n        this.gtTzData(params);\n      }\n      if (this.sbtzData) {\n        this.sbTzData(params);\n      }\n    },\n    //线路修改操作\n    updateRow1(row) {\n      this.title = \"修改线路信息\";\n      this.xlForm = { ...row };\n      this.getImgList(row.xlbm);\n      this.xlDialogFormVisible = true;\n      this.xlshow = false;\n    },\n    //线路查看详情\n    detailsInfo1(row) {\n      this.title = \"线路详情\";\n      this.xlForm = { ...row };\n      this.getImgList(row.xlbm);\n      this.xlDialogFormVisible = true;\n      this.xlshow = true;\n    },\n    jumpToTzgl(row) {\n      row;\n    },\n    getImgList(wjbh) {\n      this.xlImgList = [];\n      let param = {\n        wjbh: wjbh,\n        islast: \"1\",\n        wjlx: \"1\"\n      };\n      getListTZ(param).then(res => {\n        if (res.code === \"0000\") {\n          let data = res.data.records[0].fileList.filter(\n            record => record.fileType !== \"vsd\"\n          );\n          this.xlImgList = data;\n        }\n      });\n    },\n    //线路取消弹框\n    resetxlForm() {\n      this.xlForm = {};\n      this.$nextTick(function() {\n        this.$refs[\"xlForm\"].clearValidate();\n      });\n      this.xlDialogFormVisible = false;\n    },\n    //线路弹框内容保存\n    addLineInfo() {\n      let params = {\n        lx: \"输电设备\",\n        mc: this.xlForm.lineName\n      };\n      this.$refs[\"xlForm\"].validate(valid => {\n        if (valid) {\n          saveOrUpdatexl(this.xlForm).then(res => {\n            if (res.code == \"0000\") {\n              //新增成功后发送通知\n              adddwzyfstz(params).then(res => {\n                if (res.code === \"0000\") {\n                }\n              });\n              this.$message.success(\"操作成功,通知已发送\");\n              this.treeList();\n              this.xlDialogFormVisible = false;\n              this.lineTzData();\n              return;\n            } else {\n              this.$message.warning(\"操作失败！\");\n            }\n          });\n        } else {\n          setTimeout(() => {\n            var isError = document.getElementsByClassName(\"is-error\");\n            if (isError[0].querySelector(\"input\")) {\n              isError[0].querySelector(\"input\").focus();\n            } else if (isError[0].querySelector(\"textarea\")) {\n              isError[0].querySelector(\"textarea\").focus();\n            }\n          }, 1);\n          return false;\n        }\n      });\n    },\n    getTableList: function(params) {\n      this.queryGtParam = { ...this.queryGtParam, ...params };\n      const param = { ...this.queryGtParam, ...params };\n      getListgt(param).then(res => {\n        this.tableAndPageInfo2.tableData = res.data.records;\n        this.tableAndPageInfo2.pager.total = res.data.total;\n      });\n    },\n    /**\n     * 表格多选框\n     */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.objId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /**\n     * 删除\n     */\n    xldeleteInfo() {\n      if (this.ids.length != 0) {\n        this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(() => {\n          xlremove(this.ids).then(res => {\n            this.$message({\n              type: \"success\",\n              message: \"删除成功!\"\n            });\n            this.lineTzData();\n          });\n        });\n      } else {\n        this.$message({\n          type: \"info\",\n          message: \"请选择至少一条数据!\"\n        });\n      }\n    },\n    sbdeleteInfo() {\n      if (this.ids.length != 0) {\n        this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(() => {\n          remove(this.ids).then(res => {\n            this.$message({\n              type: \"success\",\n              message: \"删除成功!\"\n            });\n            this.sbTzData();\n          });\n        });\n      } else {\n        this.$message({\n          type: \"info\",\n          message: \"请选择至少一条数据!\"\n        });\n      }\n    },\n    gtdeleteInfo() {\n      if (this.ids.length != 0) {\n        this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(() => {\n          gtremove(this.ids).then(res => {\n            this.$message({\n              type: \"success\",\n              message: \"删除成功!\"\n            });\n            this.gtTzData();\n          });\n        });\n      } else {\n        this.$message({\n          type: \"info\",\n          message: \"请选择至少一条数据!\"\n        });\n      }\n    },\n    //导出excel\n    xlexportExcel() {\n      // if(!this.selectData.length > 0){\n      //   this.$message.warning('请在左侧勾选要导出的数据')\n      //   return\n      // }\n      let fileName = \"输电线路信息表\";\n      let exportUrl = \"/sdxl/exportExcel\";\n      exportExcel(exportUrl, this.queryParams, fileName);\n    },\n    //导出excel\n    gtexportExcel() {\n      // if(!this.selectData.length > 0){\n      //   this.$message.warning('请在左侧勾选要导出的数据')\n      //   return\n      // }\n      let fileName = this.lineName + \"杆塔信息表\";\n      let exportUrl = \"/tower/exportExcel\";\n      exportExcel(exportUrl, this.queryGtParam, fileName);\n    },\n    deleteInfo() {\n      if (this.xltzData) {\n        this.xldeleteInfo();\n      }\n      if (this.gttzData) {\n        this.gtdeleteInfo();\n      }\n      if (this.sbtzData) {\n        this.sbdeleteInfo();\n      }\n    },\n    exportExcel() {\n      if (this.xltzData) {\n        this.xlexportExcel();\n      }\n      if (this.gttzData) {\n        this.gtexportExcel();\n      }\n    },\n    //导入\n    importExcel() {\n      this.openExcelDialog = true;\n      this.fileName = \"\";\n    },\n    /**导入文件提交按钮*/\n    submitExcelForm() {\n      if (this.uploadData.lineName === this.lineName) {\n        this.isloading = true;\n        this.$refs.upload.submit();\n      } else {\n        this.$confirm(\n          \"上传的Excel线路名称与所在页面线路不一致，是否继续上传？\",\n          \"提示\",\n          {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\"\n          }\n        )\n          .then(() => {\n            //再次提交\n            this.isloading = true;\n            this.$refs.upload.submit();\n          })\n          .catch(() => {\n            this.$message({\n              type: \"info\",\n              message: \"已取消上传\"\n            });\n          });\n      }\n    },\n    /**导入文件取消按钮*/\n    cancelImport() {\n      this.isloading = false;\n      this.openExcelDialog = false;\n      //取消时清空上传文件列表\n      this.$refs.upload.clearFiles();\n    },\n    /**上传成功方法*/\n    uploadSuccess(res) {\n      if (res.code == \"0000\") {\n        this.msgSuccess(\"上传成功\");\n        this.fileName = \"\";\n        //清空上传的文件列表\n        this.$refs.upload.clearFiles();\n        this.isloading = false;\n      } else {\n        this.isloading = false;\n        this.msgError(res.msg);\n        this.$refs.upload.clearFiles();\n      }\n      //重新渲染\n      this.gtTzData(this.gtfilterInfo.data);\n      this.openExcelDialog = false;\n    },\n    /**文件改变时调用的函数*/\n    handleChange(file) {\n      this.uploadData.lineName = file.name.substring(\n        0,\n        file.name.indexOf(\"杆\")\n      );\n      this.fileName = \"\";\n      let testFileName = file.name;\n      this.fileName = testFileName;\n    },\n    /** 文件移除时函数*/\n    handleRemove() {\n      this.fileName = \"\";\n      this.msgSuccess(\"移除成功\");\n    },\n    /**文件超出限制时调用*/\n    handleExceed(file, fileList) {\n      this.msgWarning(\"只能添加一个文件，请先删除之前的文件\");\n    },\n    updateStatus(row) {\n      console.log(\"row\", row);\n      this.updateList.sbzt = row.sbzt;\n      this.updateList.objId = row.objId;\n      this.dialogVisible = true;\n    },\n    submitStatus() {\n      console.log(\"this.updateList\", this.updateList);\n      this.$confirm(\"确认将设备状态修改为\" + this.updateList.sbzt + \"?\", \"\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(res => {\n        updateStatus(this.updateList).then(res => {\n          if (res.code == \"0000\") {\n            this.$message.success(\"设备状态已变更！\");\n            this.dialogVisible = false;\n            this.getData();\n          }\n        });\n      });\n    },\n    // 修改操作\n    updateRow: function(row) {\n      this.title = \"修改输电设备信息\";\n      this.technicalParameters(row);\n      this.resumeQuery.foreignNum = row.sbmc;\n      this.resumeQuery.sblx = row.sblx;\n      this.getResumList();\n      this.sbDialogFormVisible = true;\n      this.show = false;\n      this.jbxxForm = row;\n    },\n    //详情\n    detailsInfo: function(row) {\n      this.title = \"输电设备信息\";\n      this.technicalParameters(row);\n      this.resumeQuery.foreignNum = row.sbmc;\n      this.resumeQuery.sblx = row.sblx;\n      this.getResumList();\n      this.sbDialogFormVisible = true;\n      this.show = true;\n      this.jbxxForm = row;\n    },\n    filterReset(val, type) {\n      if (type === \"gt\") {\n        this.queryGtParam = {\n          lineName: this.queryGtParam.lineName,\n          // mySorts: [{ prop: \"get_number(gtbh)\", asc: true }]\n        };\n        this.gtTzData(this.queryGtParam);\n      }\n      this.xlfilterInfo.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n    },\n    //获取左侧树节点\n    treeList() {\n      getTreeList().then(res => {\n        this.treeOptions = res.data;\n      });\n    },\n    handleClose() {\n      this.jbxxForm = {};\n      this.$nextTick(function() {\n        this.$refs[\"jbxxForm\"].clearValidate();\n      });\n\n      this.jscsForm = {};\n      this.$nextTick(function() {\n        this.$refs[\"jscsForm\"].clearValidate();\n      });\n\n      this.sbDialogFormVisible = false;\n    },\n    getResumList(par) {\n      if (this.gttzData) {\n        let params = { ...par, ...this.gtresumeQuery };\n        getResumDataList(params).then(res => {\n          this.gtresumPageInfo.tableData = res.data.records;\n          this.gtresumPageInfo.pager.total = res.data.total;\n        });\n      }\n      if (this.sbtzData) {\n        let params = { ...par, ...this.resumeQuery };\n        getResumDataList(params).then(res => {\n          this.resumPageInfo.tableData = res.data.records;\n          this.resumPageInfo.pager.total = res.data.total;\n        });\n      }\n    },\n    //设备添加按钮\n    sbAddSensorButton() {},\n    //新增按钮\n    AddSensorButton() {\n      if (this.xltzData) {\n        this.title = \"新增线路信息\";\n        this.xlImgList = [];\n        this.xlshow = false;\n        this.xlDialogFormVisible = true;\n      }\n      if (this.gttzData) {\n        if (this.gtForm.lineId != undefined) {\n          this.title = \"新增杆塔信息\";\n          this.imgList = [];\n          this.gtshow = false;\n          this.gtDialogFormVisible = true;\n        } else {\n          this.$message.info(\"请先选择所属线路再新增杆塔\");\n        }\n      }\n      if (this.sbtzData) {\n        if (this.jbxxForm.ssgtbh === \"\" && this.jbxxForm.ssxl === \"\") {\n          this.$message.info(\"请在左侧树选择具体线路或杆塔在尝试\");\n          return;\n        }\n        this.jbxxForm.ssxl = this.linedIdStore\n        this.jbxxForm.lineName = this.lineName\n        this.jbxxForm.ssgtbh = this.gtbhStore\n        this.jbxxForm.gtmc = this.gtmcStore\n        this.title = \"新增输电设备信息\";\n        //不禁用表单输入\n        this.show = false;\n        //打开弹出框\n        this.sbDialogFormVisible = true;\n        //新增时先置空去查询基数参数\n        let row = {};\n        //获取技术参数\n        this.technicalParameters(row);\n      }\n    },\n    AddTowerBatchButton() {\n      if (this.linedIdStore && this.lineName) {\n        this.addTowerBatchForm.lineName = this.lineName;\n        this.$set(this.addTowerBatchForm, \"towerNameLinkFlag\", \"#\");\n        this.$set(this.addTowerBatchForm, \"towerNumberSuffix\", \"#\");\n        this.addTowerBatchForm.towerInfo = {\n          lineId: this.linedIdStore,\n          lineName: this.lineName,\n          dydjbm: this.dydjbm,\n          ssbm: \"线路分公司\"\n        };\n        this.title = \"批量新增杆塔\";\n        this.addTowerBatchDialogFormVisible = true;\n      }\n    },\n    saveTowerBatchButton() {\n      this.$refs[\"addTowerBatchForm\"].validate(valid => {\n        if (valid) {\n          saveTowerBatch(this.addTowerBatchForm).then(res => {\n            if (res.code === \"0000\") {\n              this.$message.success(\"操作成功\");\n              this.addTowerBatchDialogFormVisible = false;\n              this.gtTzData(this.gtfilterInfo.data);\n            }\n          });\n        }\n      });\n    },\n    beforeUpload(file) {\n      const fileSize = file.size < 1024 * 1024 * 50;\n      if (!fileSize) {\n        this.$message.error(\"上传文件大小不能超过 50MB!\");\n      }\n    },\n    //杆塔修改操作\n    updateRow2(row) {\n      this.title = \"修改杆塔信息\";\n      this.clearUpload();\n      this.gtresumeQuery.foreignNum = row.ssbm;\n      this.gtresumeQuery.sblx = row.sblx;\n      this.getResumList();\n      this.gtForm = { ...row };\n      this.gtForm.attachment = [];\n      this.gtTzData();\n      this.gtDialogFormVisible = true;\n      this.gtshow = false;\n    },\n    //杆塔查看详情\n    detailsInfo2(row) {\n      this.title = \"杆塔信息\";\n      this.clearUpload();\n      this.gtresumeQuery.foreignNum = row.ssbm;\n      this.gtresumeQuery.sblx = row.sblx;\n      this.getResumList();\n      this.gtForm = { ...row };\n      this.gtForm.attachment = [];\n      this.gtTzData();\n      this.gtDialogFormVisible = true;\n      this.gtshow = true;\n    },\n    clearUpload() {\n      if (this.$refs.upload) {\n        this.$refs.upload.clearFiles();\n      }\n    },\n    //杆塔弹框关闭\n    gthandleClose() {\n      // this.gtForm = {\n      //   attachment: [],\n      //   ssbm : '线路分公司',\n      //   lineId : this.linedIdStore,\n      //   lineName : this.lineName\n      // };\n      this.gtForm.attachment = [];\n      this.gtForm.objId = undefined;\n      this.gtForm.lineId = this.linedIdStore;\n      this.$nextTick(function() {\n        this.$refs[\"gtForm\"].clearValidate();\n      });\n      this.gtDialogFormVisible = false;\n    },\n    //杆塔内容保存\n    addGtInfo() {\n      this.$refs[\"gtForm\"].validate(valid => {\n        if (valid) {\n          saveOrUpdategt(this.gtForm).then(res => {\n            if (res.code == \"0000\") {\n              this.uploadData.businessId = res.data.objId;\n              this.$refs.upload.submit();\n              this.$message.success(\"操作成功\");\n              this.gtDialogFormVisible = false;\n              this.gtTzData();\n            } else {\n              this.$message.warning(\"操作失败！\");\n              return false;\n            }\n          });\n        } else {\n          setTimeout(() => {\n            var isError = document.getElementsByClassName(\"is-error\");\n            if (isError[0].querySelector(\"input\")) {\n              isError[0].querySelector(\"input\").focus();\n            } else if (isError[0].querySelector(\"textarea\")) {\n              isError[0].querySelector(\"textarea\").focus();\n            }\n          }, 1);\n          return false;\n        }\n      });\n    },\n    //间隔添加按钮\n    jgAddjgButton() {\n      this.jgDialogFormVisible = true;\n    },\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n    //每页展示数量点击事件\n    handleSizeChange() {},\n    //页码改变事件\n    handleCurrentChange() {},\n    //树点击事件\n    handleNodeClick(data, node, nodeInfo) {\n      this.identifier = data.identifier;\n      this.queryParams.dydj = \"\"; //清空电压等级条件\n      //当前节点是线路节点\n      if (data.identifier == \"1\") {\n        //线路台账\n        this.queryParams.lineName = \"\";\n        this.lineTzData(this.queryParams);\n      } else if (data.identifier == \"2\") {\n        //电压等级节点\n        this.xloptions.forEach(item => {\n          if (item.label === data.id) {\n            this.dydjbm = item.value;\n            return false;\n          }\n        });\n        this.$set(this.xlForm, \"dydjbm\", this.dydjbm); //设置电压等级\n        this.queryParams.dydj = data.id;\n        this.lineTzData(this.queryParams); //请求线路数据\n      } else if (data.identifier == \"3\") {\n        this.gtForm.lineName = data.label;\n        this.gtForm.lineId = data.id;\n        this.linedIdStore = data.id;\n        this.gtfilterInfo.data.lineName = data.label;\n        this.lineName = data.label;\n        this.gtTzData(this.gtfilterInfo.data);\n      } else if (data.identifier == \"4\") {\n        this.gtbhStore = data.id; //杆塔编号\n        this.gtmcStore = data.label;\n        this.jbxxForm.ssxl = node.parent.data.id; //线路编码\n        this.jbxxForm.lineName = node.parent.data.label; //线路名称\n        this.jbxxForm.ssgtbh = data.id; //杆塔编号\n        this.jbxxForm.gtmc = data.label; //杆塔名称\n        let ssgtbh = data.id; //杆塔编号\n        this.sbTzData();\n      }\n    },\n    //缺陷标准库新增完成\n    qxcommit() {\n      this.dialogFormVisible = false;\n      this.$message.success(\"新增成功\");\n    },\n    submit() {\n      this.$refs[\"jbxxForm\"].validate(valid => {\n        if (valid) {\n          this.addEquipInfo();\n          //  this.submitParameter();\n        } else {\n          setTimeout(() => {\n            var isError = document.getElementsByClassName(\"is-error\");\n            if (isError[0].querySelector(\"input\")) {\n              isError[0].querySelector(\"input\").focus();\n            } else if (isError[0].querySelector(\"textarea\")) {\n              isError[0].querySelector(\"textarea\").focus();\n            }\n          }, 1);\n          return false;\n        }\n      });\n    },\n    //获取设备类型下拉框数据\n    getSblxDataListSelected() {\n      let sblxParam = {\n        type: \"输电设备\"\n      };\n      getSblxDataListSelected(sblxParam).then(res => {\n        this.sblxOptionsDataSelected = res.data;\n      });\n    },\n    //设备类型change事件\n    showParams(data) {\n      this.paramQuery.sblxbm = data;\n      this.getParameters();\n    },\n    //点击新增，修改，详情时，重新获取对应的技术参数信息\n    technicalParameters(row) {\n      //设备类型\n      this.jscsForm = {};\n      this.paramQuery.sblxbm = row.sbflbm;\n      this.jscsForm.sblxbm = row.sbflbm;\n      this.jscsForm.sbbm = row.objId;\n      this.getParameters();\n    },\n    //获取技术参数对照信息，展示对应得技术参数label信息\n    async getParameters() {\n      this.jscsLabelList = [];\n      getParamDataList(this.paramQuery).then(res => {\n        this.jscsLabelList = res.data;\n        this.getParamValue();\n      });\n    },\n    //获取值方法\n    getParamValue() {\n      getParamsValue(this.jscsForm).then(res => {\n        if (res.data != \"\") {\n          this.jscsForm = { ...res.data[0] };\n        }\n      });\n    },\n    //保存基数参数值信息\n    submitParameter() {\n      saveParamValue(this.jscsForm).then(res => {\n        this.sbDialogFormVisible = false;\n      });\n    },\n    async deleteFileById(id){\n      let {code}=await deleteById(id)\n      if(code==='0000'){\n        await this.getFileList();\n        this.$message({\n          type: 'success',\n          message: '文件删除成功!'\n        });\n      }\n    },\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  height: calc(100vh - 225px);\n  overflow: auto;\n}\n\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.box-cardList {\n  height: 70%;\n}\n\n.item {\n  width: 200px;\n  float: left;\n}\n\n#main_container_dj {\n  height: calc(100vh - 84px);\n}\n\n.aside_height {\n  height: 96%;\n}\n\n.defect .el-form-item:nth-child(odd) {\n  margin-right: 70px;\n}\n\n.imgCls {\n  height: 150px !important;\n}\n\n/*背景颜色调整*/\n#main_container_dj,\n#main_container_dj .el-aside {\n  background-color: #b4caf1;\n}\n\n.el-select {\n  width: 100%;\n}\n\n.el-date-editor {\n  width: 100%;\n}\n\n.el-carousel__item h3 {\n  color: #475669;\n  font-size: 14px;\n  opacity: 0.75;\n  line-height: 150px;\n}\n\n.el-carousel__item:nth-child(2n) {\n  background-color: #99a9bf;\n}\n\n.el-carousel__item:nth-child(2n + 1) {\n  background-color: #d3dce6;\n}\n\n/deep/ .box-card {\n  margin: 0 6px;\n}\n\n//有子节点 且未展开\n.el-tree ::v-deep .el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/add.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n\n//有子节点 且已展开\n.el-tree\n  ::v-deep\n  .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/prep.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n\n//没有子节点\n.el-tree ::v-deep .el-tree-node__expand-icon.is-leaf::before {\n  background: transparent;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n\n.divHeader {\n  width: 100%;\n  height: 30px;\n  background-color: #dddddd;\n  margin-bottom: 10px;\n  text-align: left;\n  line-height: 30px;\n  font-weight: bold;\n}\n\n.categoryTreeIcons {\n  background: url(\"../../../../assets/icons/icon/icon1.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n\n.tableIcon {\n  background: url(\"../../../../assets/icons/icon/icon2.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n\n.classIcon {\n  background: url(\"../../../../assets/icons/icon/icon4.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n\n.classIcon2 {\n  background: url(\"../../../../assets/icons/icon/icon4.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n\n.my-autocomplete {\n  li {\n    line-height: normal;\n    padding: 7px;\n\n    .name {\n      text-overflow: ellipsis;\n      overflow: hidden;\n      font-size: 18px;\n    }\n    .addr {\n      font-size: 12px;\n      color: #b4b4b4;\n    }\n\n    .highlighted .addr {\n      color: #ddd;\n    }\n  }\n}\n</style>\n\n<style>\n.el-carousel__container {\n  background-color: #ffffff !important;\n}\n</style>\n"]}]}