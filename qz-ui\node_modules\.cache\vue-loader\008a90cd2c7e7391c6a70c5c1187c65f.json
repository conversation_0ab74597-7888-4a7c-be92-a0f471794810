{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\jxbzk\\jxzqwh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\jxbzk\\jxzqwh.vue", "mtime": 1706897322435}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["jxzqwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+GA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "jxzqwh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/jxbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      @handleReset=\"getReset\"\n      @onfocusEvent=\"inputFocusEvent\"\n    />\n    <div>\n      <el-white class=\"button-group\">\n        <div class=\"button_btn\">\n          <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"getInsert\">新增</el-button>\n        </div>\n        <comp-table :table-and-page-info=\"tableAndPageInfo\" @update:multipleSelection=\"selectChange\">\n          <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\" width=\"160\"\n                           :resizable=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button @click=\"updateRow(scope.row)\" v-show=\"scope.row.createBy == currentUser\"  type=\"text\" size=\"small\" title=\"修改\"  class='el-icon-edit'>\n              </el-button>\n              <el-button @click=\"getInfo(scope.row)\" type=\"text\" size=\"small\" title=\"详情\" class=\"el-icon-view\"></el-button>\n              <el-button type=\"text\" size=\"small\" v-show=\"scope.row.createBy == currentUser\" @click=\"deleteRow(scope.row)\" title=\"删除\" class=\"el-icon-delete\"></el-button>\n            </template>\n          </el-table-column>\n        </comp-table>\n      </el-white>\n      <!-- 详情/新增/修改 -->\n      <el-dialog :title=title :visible.sync=\"isShowDetails\" width=\"40%\" @close=\"handleClose\" v-dialogDrag>\n        <el-form\n          label-width=\"120px\"\n          ref=\"form\"\n          :model=\"form\"\n          :rules=\"rules\"\n        >\n          <el-row :gutter=\"8\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item v-show=\"false\" label=\"设备分类:\" prop=\"sbfl\">\n                <el-input v-model=\"form.sbfl\"></el-input>\n                <el-input v-model=\"form.objId\"></el-input>\n              </el-form-item>\n              <el-form-item label=\"设备分类：\" prop=\"sbflmc\">\n                <el-input placeholder=\"请选择设备分类\" @focus=\"showDeviceTreeDialog\" v-model=\"form.sbflmc\"\n                          :disabled=\"isDisabled\" clearable\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"检修分类：\" prop=\"jxfl\">\n                <el-select placeholder=\"请选择检修分类\" clearable v-model=\"form.jxfl\" :disabled=\"isDisabled\"\n                           style=\"width: 100%\"\n                >\n                  <el-option\n                    v-for=\"item in jxflList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"周期单位：\" prop=\"zqdw\">\n                <el-select placeholder=\"请选择周期单位\" clearable v-model=\"form.zqdw\" :disabled=\"isDisabled\"\n                           style=\"width: 100%\"\n                >\n                  <el-option\n                    v-for=\"item in zqdwList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"检修周期：\" clearable prop=\"jxzq\">\n                <el-input-number placeholder=\"请输入检修周期\" v-model=\"form.jxzq\" :disabled=\"isDisabled\"/>\n              </el-form-item>\n            </el-col>\n\n          </el-row>\n        </el-form>\n        <div slot=\"footer\">\n          <el-button @click=\"close\">取 消</el-button>\n          <el-button v-if=\"title==='检修周期新增'||title==='检修周期修改'\" type=\"primary\" @click=\"saveRow\">确 认</el-button>\n        </div>\n      </el-dialog>\n\n      <el-dialog\n        :append-to-body=\"true\"\n        title=\"设备分类\"\n        v-dialogDrag\n        :visible.sync=\"showDeviceTree\"\n        width=\"400px\"\n        v-if=\"showDeviceTree\"\n      >\n        <device-tree\n          @getDeviceTypeData=\"getDeviceTypeData\"\n          @closeDeviceTypeDialog=\"closeDeviceTypeDialog\"\n        >\n        </device-tree>\n      </el-dialog>\n\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getList, remove, saveOrUpdate } from '@/api/dagangOilfield/bzgl/jxbzk/jxzqwh'\nimport DeviceTree from '@/views/dagangOilfield/bzgl/sbbzk/deviceTree'\nimport { getDictTypeData } from '@/api/system/dict/data'\n\nexport default {\n  name: 'jxzqwh',\n  components: { DeviceTree },\n  data() {\n    return {\n      currentUser: this.$store.getters.name,\n      //form表单\n      form: {},\n      //是否显示弹框\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //表单数据\n      dataTable: [],\n      zqdwList: [],\n      jxflList: [],\n      //标题\n      title: '',\n      filterInfo: {\n        data: {\n          sbflArr: [],\n          sbfl: '',\n          jxflArr: [],\n          jxzq: '',\n          zqdw: ''\n        },\n        fieldList: [\n          {\n            label: '设备分类',\n            value: 'sbfl',\n            type: 'input',\n            clearable: true\n          },\n\n\n          {\n            label: '检修周期',\n            value: 'jxzq',\n            type: 'input'\n          },\n          /*{\n            label: '检修分类',\n            value: 'jxflArr',\n            type: 'select',\n            // type: 'checkbox',\n            // checkboxValue: [],\n            multiple: true,\n            options: [],\n            clearable: true\n          },*/\n          {\n            label: '检修分类',\n            value: 'jxfl',\n            type: 'checkbox',\n            checkboxValue: [],\n            multiple: true,\n            options: [],\n            clearable: true\n          },\n          {\n            label: '周期单位',\n            value: 'zqdw',\n            // type: 'select',\n              type: 'checkbox',\n            checkboxValue: [],\n            options: [],\n            clearable: true\n          },\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 120]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: '设备分类', prop: 'sbflmc', minWidth: '100' },\n          { label: '检修分类', prop: 'jxflName', minWidth: '150' },\n          { label: '周期单位', prop: 'zqdwName', minWidth: '150' },\n          { label: '检修周期', prop: 'jxzq', minWidth: '80' }\n          /*{\n            prop: 'operation',\n            label: '操作',\n            minWidth: '130px',\n            style: { display: 'block' },\n            //操作列固定再右侧\n            fixed: 'right',\n            operation: [\n              { name: '修改', clickFun: this.updateRow },\n              { name: '详情', clickFun: this.getInfo }\n            ]\n          }*/\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        sbflArr: [],\n        jxflArr: [],\n        jxzq: '',\n        zqdw: ''\n      },\n      selectRows: [],\n      showDeviceTree: false,\n      isFilter: false,\n      rules: {\n        sbflmc: [\n          { required: true, message: '设备分类不能为空', trigger: 'change' }\n        ],\n        sbfl: [\n          { required: true, message: '设备分类不能为空', trigger: 'change' }\n        ],\n        jxfl: [\n          { required: true, message: '检修分类不能为空', trigger: 'change' }\n        ],\n        zqdw: [\n          { required: true, message: '周期单位不能为空', trigger: 'change' }\n        ],\n        jxzq: [\n          { required: true, message: '检修周期不能为空', trigger: 'blur' }\n        ]\n      }\n    }\n  },\n  mounted() {\n    this.initDomain()\n  },\n  methods: {\n    //列表查询\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params }\n        const param = this.params\n        param.sbflArr = param.sbfl === '' ? [] : param.sbflArr\n        const { data, code } = await getList(param)\n        if (code === '0000') {\n          this.tableAndPageInfo.tableData = data.records\n          this.tableAndPageInfo.pager.total = data.total\n\n          this.tableAndPageInfo.tableData.forEach(item => {\n            this.jxflList.forEach(element => {\n              if (item.jxfl === element.value) {\n                item.jxflName = element.label\n                return\n              }\n            })\n            this.zqdwList.forEach(element => {\n              if (item.zqdw === element.value) {\n                item.zqdwName = element.label\n                return\n              }\n            })\n          })\n        }\n      } catch (e) {\n        console.log(e)\n      }\n    },\n    //重置按钮\n    getReset() {\n   this.filterInfo.fieldList.forEach(item=>{\n        if(item.type === 'checkbox'){\n          item.checkboxValue = [];\n        }\n      })\n      this.params =  {\n        sbflArr: [],\n        jxflArr: [],\n        jxzq: '',\n        zqdw: ''\n      }\n    },\n    //选中行\n    handleSelectionChange() {\n\n    },\n    //详情\n    getDetails() {\n      this.title = '检修周期详情'\n      this.isDisabled = true\n      this.isShowDetails = true\n      this.form = { ...row }\n    },\n    //新增\n    getInsert() {\n      this.title = '检修周期新增'\n      this.isDisabled = false\n      this.isShowDetails = true\n      this.form = {\n        sbfl: '',\n        sbflmc: '',\n        jxfl: '',\n        zqdw: '',\n        jxzq: '',\n        objId: ''\n      }\n    },\n    //编辑按钮\n    updateRow(row) {\n      this.title = '检修周期修改'\n      this.isDisabled = false\n      this.form = { ...row }\n      this.isShowDetails = true\n    },\n    //详情按钮\n    getInfo(row) {\n      this.title = '检修周期详情'\n      this.form = { ...row }\n      this.isDisabled = true\n      this.isShowDetails = true\n    },\n    async saveRow() {\n\n      this.$refs['form'].validate(valid => {\n        if (valid) {\n          saveOrUpdate(this.form).then(res => {\n\n            try {\n              if (res.code === '0000') {\n                this.$message.success('操作成功')\n              }\n            } catch (e) {\n              console.log(e)\n            }\n            this.getData()\n          })\n        } else {\n          return false\n        }\n        this.isShowDetails = false\n      })\n    },\n    //删除按钮\n    async deleteRow(row) {\n      this.form = { ...row }\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        remove([this.form.objId]).then(({ code }) => {\n          if (code === '0000') {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            })\n            this.getData()\n          } else {\n            this.$message({\n              type: 'error',\n              message: '删除失败!'\n            })\n          }\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        })\n      })\n      await this.getData()\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false\n    },\n    selectChange(rows) {\n      this.selectRows = rows\n    },\n\n    showDeviceTreeDialog() {\n      this.isFilter = false\n      this.showDeviceTree = true\n    },\n\n    async initDomain() {\n\n      let { data: jxfl } = await getDictTypeData('jxfl')\n      this.jxflList = jxfl\n      let { data: zqdw } = await getDictTypeData('zqdw')\n      this.zqdwList = zqdw\n\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.value === 'jxfl') {\n          item.options = jxfl\n        }\n        if (item.value === 'zqdw') {\n          item.options = zqdw\n        }\n      })\n      //列表查询\n      this.getData()\n    },\n\n    inputFocusEvent(val) {\n      if (val.target.name === 'sbfl') {\n        this.showDeviceTree = true\n        this.isFilter = true\n      }\n    },\n\n    getDeviceTypeData(res) {\n      if (this.isFilter) {\n        this.filterInfo.data.sbflArr = []\n        this.filterInfo.data.sbfl = ''\n        res.forEach(item => {\n          if (item.checked) {\n            this.filterInfo.data.sbflArr.push(item.code)\n            this.filterInfo.data.sbfl += item.name + ','\n          }\n        })\n\n        this.filterInfo.data.sbfl = this.filterInfo.data.sbfl.substring(0, this.filterInfo.data.sbfl.length - 1)\n        this.showDeviceTree = false\n      } else {\n        let treeNodes = []\n        res.forEach(item => {\n          if (item.checked) {\n            treeNodes.push(item)\n          }\n        })\n        if (treeNodes.length === 1) {\n          this.form.sbflmc = treeNodes[0].name\n          this.form.sbfl = treeNodes[0].code\n          this.showDeviceTree = false\n        } else {\n          this.$message.warning('请选择单条设备数据')\n        }\n      }\n    },\n    closeDeviceTypeDialog() {\n      this.showDeviceTree = false\n    },\n    //清空表单数据\n    handleClose() {\n      this.form = {}\n      this.$nextTick(() => {\n        this.form = this.$options.data().form\n        this.resetForm('form')\n      })\n    }\n\n  }\n}\n</script>\n\n<style scoped>\n\n</style>\n"]}]}