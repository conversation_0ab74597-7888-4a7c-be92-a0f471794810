{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\yxgl\\bdyxgl\\components\\sbqx.vue?vue&type=style&index=0&id=fb97d558&scoped=true&lang=scss&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\yxgl\\bdyxgl\\components\\sbqx.vue", "mtime": 1755528799754}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5zYnF4X2JveCB7CiAgcGFkZGluZzogMjBweCAwIDAgMjBweDsKfQouc2JxeF9ib3hUIHsKICBtYXJnaW4tdG9wOiAtMThweDsKfQoudGFiQWN0aXZlIHsKICB3aWR0aDogMTAlOwogIGZsb2F0OiBsZWZ0OwogIGNvbG9yOiAjZmZmOwogIGJhY2tncm91bmQ6ICMwMmI5ODg7CiAgYm9yZGVyLXRvcDogMDsKfQoubm9BY3RpdmUgewogIHdpZHRoOiAxMCU7CiAgZmxvYXQ6IGxlZnQ7CiAgYmFja2dyb3VuZDogI2ZmZjsKICBjb2xvcjogIzU0NTI1MjsKICAmOmhvdmVyIHsKICAgIGJhY2tncm91bmQ6ICNmZmZmZmY7CiAgICBjb2xvcjogIzM1OTA3NjsKICB9Cn0KLm9uZUJ0biB7CiAgbWFyZ2luLXJpZ2h0OiAtMTVweDsKfQoudHdvQnRuIHsKICB0cmFuc2Zvcm06IHNrZXdYKDMzZGVnKTsKICBib3JkZXItcmlnaHQ6IDFweCBzb2xpZCAjOWE5ODk4Njk7CiAgLmFsbEJ0biB7CiAgICB0cmFuc2Zvcm06IHNrZXdYKC0zM2RlZyk7CiAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7CiAgfQp9Cg=="}, {"version": 3, "sources": ["sbqx.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmfA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "sbqx.vue", "sourceRoot": "src/views/dagangOilfield/yxgl/bdyxgl/components", "sourcesContent": ["<template>\n  <div class=\"\" id=\"dialogActstdiv\">\n    <el-row class=\"sbqx\">\n      <!--   Tab页签   -->\n      <el-col :span=\"24\" class=\"sbqx_box\">\n        <div class=\"txtTitle\">\n          <span\n            @click=\"click('bdqx')\"\n            :class=\"this.flag === 'bdqx' ? 'tabActive' : 'noActive'\"\n            class=\"oneBtn\"\n          >\n            <span class=\"allBtn\">变电隐患</span>\n          </span>\n          <span\n            @click=\"click('xlqx')\"\n            :class=\"this.flag === 'xlqx' ? 'tabActive' : 'noActive'\"\n            class=\"twoBtn\"\n          >\n            <span class=\"allBtn\">线路隐患</span>\n          </span>\n          <span\n            @click=\"click('pdqx')\"\n            :class=\"this.flag === 'pdqx' ? 'tabActive' : 'noActive'\"\n            class=\"twoBtn\"\n          >\n            <span class=\"allBtn\">配电隐患</span>\n          </span>\n          <span\n            @click=\"click('gfqx')\"\n            :class=\"this.flag === 'gfqx' ? 'tabActive' : 'noActive'\"\n            class=\"twoBtn\"\n          >\n            <span class=\"allBtn\">新能源隐患</span>\n          </span>\n        </div>\n      </el-col>\n      <!--   变电缺陷   -->\n      <el-col :span=\"24\" v-if=\"this.flag === 'bdqx'\" class=\"sbqx_boxT\">\n        <qxgl_ys></qxgl_ys>\n      </el-col>\n      <!--   线路缺陷   -->\n      <el-col :span=\"24\" v-if=\"this.flag === 'xlqx'\" class=\"sbqx_boxT\">\n        <qxgl_xl></qxgl_xl>\n      </el-col>\n      <!--   配电缺陷   -->\n      <el-col :span=\"24\" v-if=\"this.flag === 'pdqx'\" class=\"sbqx_boxT\">\n        <qxgl_pd></qxgl_pd>\n      </el-col>\n      <!--   供服缺陷   -->\n      <el-col :span=\"24\" v-if=\"this.flag === 'gfqx'\" class=\"sbqx_boxT\">\n        <qxgl_gf></qxgl_gf>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport Qxgl_ys from \"@/views/dagangOilfield/yxgl/bdyxgl/components/qxgl_ys\";\nimport Qxgl_xl from \"@/views/dagangOilfield/yxgl/bdyxgl/components/qxgl_xl\";\nimport Qxgl_pd from \"@/views/dagangOilfield/yxgl/bdyxgl/components/qxgl_pd\";\nimport Qxgl_gf from \"@/views/dagangOilfield/yxgl/gfyxgl/components/qxgl_gf\";\n\nexport default {\n  name: \"sbqx\",\n  components: {\n    Qxgl_pd,\n    Qxgl_xl,\n    Qxgl_ys,\n    Qxgl_gf\n  },\n\n  data() {\n    return {\n      flag: \"bdqx\", //默认展示变电缺陷的内容\n      flyjMap: new Map(),\n      sbbjList: [], //设备部件list\n      sbbwList: [], //设备部位list\n      qxmsList: [], //缺陷描述list\n      qxflList: [], //缺陷分类list\n      qxflData: [], //缺陷分类所有数据\n      jsyyList: [],\n      //滚动条高度\n      scroll: \"\",\n      //当前显示的菜单区域\n      istyle: -1,\n      deptId: undefined,\n      jsbutten: false,\n      currUser: \"\",\n      currUserdw: this.$store.getters.deptId,\n      //提交审核按钮\n      buttonTjshShow: false,\n      zt: \"\",\n      // 消项验收结论\n      isShowYs: false,\n      // 消除处理三个复选框\n      isShowXqcl: false,\n      // 生产科处理建议\n      isShowScksh: false,\n      // 分公司处理建议\n      isShowFgssh: false,\n      tableDatas: [],\n      //巡视点位弹框\n      isShowXsdw: false,\n      //上传图片时的携带的其他参数\n      uploadImgData: {\n        businessId: \"\" //携带的表单主键id\n      },\n      //上传图片时的请求头\n      header: {},\n      //缺陷标准库dialog\n      isShowQxbzDialog: false,\n      //工作票\n      isShowGzp: false,\n      //使用当前设备类型编码查询缺陷标准库\n\n      //主设备选择传递子组件参数\n      selectedSbParam: {\n        lx: \"bd\",\n        sbmc: \"\"\n      },\n      //主设备弹出框\n      isShowSysbDialog: false,\n      //操作审核按钮\n      shButtonControl: true,\n      videoForm: {\n        showVideoPath: \"\"\n      },\n      //展示图片dialog控制\n      imgDialogVisible: false,\n      //图片地址url\n      dialogImageUrl: \"\",\n      //图片list\n      imgList: [],\n      //消项验收\n      xqimgList: [],\n      //缺陷类别\n      qxlbOptions: [\n        { label: \"变电\", value: \"变电\" },\n        { label: \"配电\", value: \"配电\" },\n        { label: \"输电\", value: \"输电\" }\n      ],\n      //验收结论\n      ysjlDisabled: false,\n      //隐藏部分card数据\n      fgsShCardShow: false,\n      //弹出框中表格数据\n      propTableData: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //锚点跳转按钮名称\n      // buttonArr: ['缺陷上报', '缺陷描述信息', '监控应急措施','消项处理', '消项验收'],\n      buttonArr: [\n        \"缺陷上报\",\n        \"缺陷描述信息\",\n        \"监控应急措施\",\n        \"班组审核\",\n        \"分公司审核\",\n        \"生产科审核\",\n        \"消项处理\",\n        \"消项验收\"\n      ],\n      activeBtn: 0,\n      assetSelect: false,\n      //设备部位\n      sbbwOptions: [\n        { label: \"本体端子箱\", value: \"本体端子箱\" },\n        { label: \"储油柜\", value: \"储油柜\" },\n        { label: \"呼吸器\", value: \"呼吸器\" }\n      ],\n      //弹出框内新增时下拉框变电站数据\n      bdzDataListOptions: [\n        { label: \"35kV然气站变电站\", value: \"35kV然气站变电站\" },\n        { label: \"110kV然气站变电站\", value: \"110kV然气站变电站\" },\n        { label: \"10kV然气站变电站\", value: \"10kV然气站变电站\" }\n      ],\n      //弹出框内新增时归属下拉框数据\n      gsOptionsDataList: [\n        { label: \"运行\", value: \"运行\" },\n        { label: \"分公司\", value: \"分公司\" },\n        { label: \"班组\", value: \"班组\" }\n      ],\n      // 发现方式选项\n      findWayOptions: [\n        { value: \"在线监测\", label: \"在线监测\" },\n        { value: \"人工发现\", label: \"人工发现\" },\n        { value: \"日常巡视\", label: \"日常巡视\" }\n      ],\n      // 检测技术选项\n      detectingOptions: [\n        { value: \"视频监控\", label: \"视频监控\" },\n        { value: \"人工判断\", label: \"人工判断\" },\n        { value: \"红外识别\", label: \"红外识别\" }\n      ],\n      // 设备部件选项\n      partsOptions: [\n        { value: \"本体\", label: \"本体\" },\n        { value: \"非电量保护\", label: \"非电量保护\" },\n        { value: \"基础\", label: \"基础\" },\n        { value: \"冷却器系统\", label: \"冷却器系统\" },\n        { value: \"分接开关\", label: \"分接开关\" },\n        { value: \"套管\", label: \"套管\" }\n      ],\n      // 缺陷性质选项\n      defectQualityOptions: [\n        { value: \"一般\", label: \"一般\" },\n        { value: \"严重\", label: \"严重\" },\n        { value: \"危急\", label: \"危急\" }\n      ],\n      //所属分公司下拉框数据\n      allFgsList: [],\n      //所属分公司下拉框数据\n      ssgsOptionsDataList: [],\n      //变电分公司对象\n      bdfgsObjArr: [],\n      //弹出框控制内容disabled\n      dialogFormDisabled: false,\n      dialogFormDisabledst: false,\n      dialogFormDisabledbz: false, //班组审核意见\n      //是否禁用后续编辑,默认禁用\n      isHistoryDisabled: true,\n      // 下拉树筛选文字\n      filterText: \"\",\n      zsb: \"\",\n      defaultProps: {\n        children: \"children\",\n        label: \"label\"\n      },\n      //选中得行数\n      selectRows: [],\n      // 查询数据总条数\n      total: 0,\n      // 对话框标题\n      title: \"\",\n      // 对话框是否打开\n      open: false,\n      // 新增/修改表单\n      form: {\n        id: undefined,\n        substation: undefined,\n        mainDevice: undefined,\n        defectId: undefined,\n        deviceType: undefined,\n        deviceModel: undefined,\n        manufacturer: undefined,\n        runNumber: undefined,\n        findDate: undefined,\n        attribution: undefined,\n        enterPerson: undefined,\n        enterDept: undefined,\n        findWay: undefined,\n        detecting: undefined,\n        findPerson: undefined,\n        parts: undefined,\n        defectDescription: undefined,\n        classifyGist: undefined,\n        defectQuality: undefined,\n        defectContent: undefined,\n        reason: undefined,\n        condition: undefined,\n        remark: undefined,\n        aqyxcqcs: undefined,\n        xsjcyq: undefined,\n        gzqk: undefined,\n        yjczfa: undefined,\n        qtyq: undefined,\n        fgsshr: undefined,\n        fgsshsj: undefined,\n        qrdqxfl: undefined,\n        fgscljy: undefined,\n        sckshr: undefined,\n        sckshsj: undefined,\n        sckcljy: undefined,\n        xqfzr: undefined,\n        xqfzrdw: undefined,\n        xqclsj: undefined,\n        xqcljg: undefined,\n        xqylwt: undefined,\n        xqysr: undefined,\n        xqyssj: undefined,\n        ysjl: undefined,\n        ysjg: undefined,\n        bzfzr: \"\",\n        bz: \"\",\n        //变电\n        lx: 2,\n        xqfzrldsj: \"\"\n      },\n      lrrdw: undefined,\n      xqfzrdw: undefined,\n      // 多选框选中的数据id\n      ids: [],\n      // 是否单选\n      single: true,\n      // 是否多选\n      multiple: true,\n      // 选中的数据\n      selectData: [],\n      // 归属选项\n      attributionOptions: [{ value: \"a1\", label: \"a1\" }],\n\n      // 缺陷描述选项\n      defectDescriptionOptions: [{ value: \"a1\", label: \"a1\" }],\n      // 分类依据\n      classifyGistOptions: [{ value: \"a1\", label: \"a1\" }],\n\n      // 表单校验\n      rules: {\n        qxnr: [{ required: true, message: \"请输入缺陷内容\", trigger: \"blur\" }],\n        qxlb: [\n          { required: true, message: \"请选择缺陷类别\", trigger: \"select\" }\n        ],\n        ssgs: [{ required: true, message: \"请选择分公司\", trigger: \"select\" }],\n        ssdz: [{ required: true, message: \"请选择所属位置\", trigger: \"blur\" }],\n        sbxh: [{ required: true, message: \"请输入设备型号\", trigger: \"blur\" }],\n        sccj: [{ required: true, message: \"请输入生产厂家\", trigger: \"blur\" }],\n        bzqxBj: [\n          { required: true, message: \"请输入设备部件\", trigger: \"blur\" }\n        ],\n        bzqxBw: [\n          { required: true, message: \"请输入设备部位\", trigger: \"blur\" }\n        ],\n        bzqxQxms: [\n          { required: true, message: \"请输入缺陷描述\", trigger: \"blur\" }\n        ],\n        bzqxFlyj: [\n          { required: true, message: \"请输入分类依据\", trigger: \"blur\" }\n        ],\n        // xsdw: [{ required: true, message: \"请输入巡视点位\", trigger: \"blur\" }],\n        sb: [{ required: true, message: \"请选择主设备\", trigger: \"blur\" }],\n        sblx: [{ required: true, message: \"请选择设备类型\", trigger: \"blur\" }],\n        fxrq: [\n          { required: true, message: \"请选择发现日期\", trigger: \"change\" }\n        ],\n        // xsdw: [\n        //   { required: true, message: \"请选择巡视点位\", trigger: \"change\" },\n        // ],\n        fxr: [{ required: true, message: \"请输入发现人\", trigger: \"change\" }],\n        fxfs: [\n          { required: true, message: \"请选择发现方式\", trigger: \"change\" }\n        ],\n        jcjs: [\n          { required: true, message: \"请选择检测技术\", trigger: \"change\" }\n        ],\n        sbbj: [\n          { required: true, message: \"请选择获取标准库\", trigger: \"change\" }\n        ]\n      },\n      // 表单是否可编辑\n      isEditable: true,\n      filterInfo: {\n        data: {\n          ssgs: [],\n          ssdz: \"\",\n          qxxz: \"\",\n          sbxh: \"\",\n          sb: \"\",\n          sblx: \"\",\n          lczt: []\n        },\n        fieldList: [\n          {\n            label: \"所属公司\",\n            type: \"select\",\n            value: \"ssgs\",\n            multiple: true,\n            options: [\n              { value: \"港东变电分公司\", label: \"港东变电分公司\" },\n              { value: \"港中变电分公司\", label: \"港中变电分公司\" },\n              { value: \"南部变电分公司\", label: \"南部变电分公司\" },\n              { value: \"线路分公司\", label: \"线路分公司\" },\n              { value: \"配电运维分公司\", label: \"配电运维分公司\" }\n            ]\n          },\n          { label: \"所属位置\", type: \"input\", value: \"ssdz\" },\n          { label: \"设备名称\", type: \"input\", value: \"sb\" },\n          { label: \"设备类型\", type: \"input\", value: \"sblx\" },\n          {\n            label: \"缺陷性质\",\n            type: \"select\",\n            value: \"qxxz\",\n            options: [\n              { label: \"一般\", value: \"一般\" },\n              { label: \"严重\", value: \"严重\" },\n              { label: \"危急\", value: \"危急\" }\n            ]\n          },\n          {\n            label: \"状态\",\n            type: \"select\",\n            value: \"lczt\",\n            multiple: true,\n            options: [\n              { label: \"待上报\", value: \"1\" },\n              { label: \"历史录入\", value: \"0\" },\n              { label: \"分公司审核\", value: \"2\" },\n              { label: \"生产科审核\", value: \"3\" },\n              { label: \"检修安排\", value: \"4\" },\n              { label: \"待处理\", value: \"5\" },\n              { label: \"待验收\", value: \"6\" },\n              { label: \"已消项\", value: \"7\" }\n            ]\n          },\n          { label: \"设备型号\", type: \"input\", value: \"sbxh\" }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          pageResize: \"\",\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"ssgs\", label: \"所属公司\", minWidth: \"120\" },\n          { prop: \"ssdz\", label: \"所属位置\", minWidth: \"120\" },\n          { prop: \"sb\", label: \"主设备\", minWidth: \"120\" },\n          { prop: \"sblx\", label: \"设备类型\", minWidth: \"100\" },\n          { prop: \"bzqxQxdj\", label: \"缺陷性质\", minWidth: \"120\" },\n          { prop: \"ztmc\", label: \"状态\", minWidth: \"120\" },\n          // {prop: 'dydj', label: '电压等级', minWidth: '100'},\n          { prop: \"sbxh\", label: \"设备型号\", minWidth: \"120\" },\n          { prop: \"sccj\", label: \"生产厂家\", minWidth: \"120\" },\n          // { prop: \"gzpbh\", label: \"工作票编号\", minWidth: \"120\" },\n          { prop: \"createTime\", label: \"创建时间\", minWidth: \"150\" }\n        ]\n      },\n      params: {\n        //变电\n        lx: 2,\n        lczt: \"1,2,3,4,5,6,7,8,9,11\" //查看所有状态数据\n      },\n      loading: null,\n      openSb: false,\n      form1: {},\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"qxlccs\",\n        businessKey: \"\",\n        businessType: \"缺陷管理\",\n        variables: {},\n        defaultFrom: true,\n        nextUser: \"\",\n        processType: \"complete\"\n      },\n      activitiOption: { title: \"上报\" },\n      timeData: [],\n      timeLineShow: false,\n      isShow: false,\n      openLoadingImg: false,\n      imgSrc: \"\",\n\n      //线路分公司数组\n      lineFgsArr: [\"线路分公司\"],\n      //配电分公司数组\n      pdFgsArr: [\"配电运维分公司\"],\n      //弹出框内新增时下拉框所属位置数据\n      wzDataListOptions: [],\n      //获取设备类型弹出框\n      showSblxTree: false,\n      //获取设备类型弹出框传递参数\n      selectSbp: [],\n      //判断从哪点击的设备类型弹出框\n      isFilter: false,\n      //巡视点位下拉框数据\n      xsdwOptions: [],\n\n      tempFileName: \"设备缺陷表单\",\n\n      //视频上传进度条\n      videoFlag: false,\n      //是否显示视频进度条\n      videoUploadPercent: \"\",\n      videoList: [],\n      paramss: {},\n      xsdwparams: {\n        sswz: \"\",\n        sbmc: \"\",\n        xsdw: \"\"\n      },\n      isDz: false,\n      qxlb: \"变电\"\n    };\n  },\n  methods: {\n    click(mainTab) {\n      this.flag = mainTab;\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.sbqx_box {\n  padding: 20px 0 0 20px;\n}\n.sbqx_boxT {\n  margin-top: -18px;\n}\n.tabActive {\n  width: 10%;\n  float: left;\n  color: #fff;\n  background: #02b988;\n  border-top: 0;\n}\n.noActive {\n  width: 10%;\n  float: left;\n  background: #fff;\n  color: #545252;\n  &:hover {\n    background: #ffffff;\n    color: #359076;\n  }\n}\n.oneBtn {\n  margin-right: -15px;\n}\n.twoBtn {\n  transform: skewX(33deg);\n  border-right: 1px solid #9a989869;\n  .allBtn {\n    transform: skewX(-33deg);\n    display: inline-block;\n  }\n}\n</style>\n"]}]}