{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\bdgl\\bdsbgl.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\bdgl\\bdsbgl.vue", "mtime": 1706897324519}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgYWRkQXNzZXQsCiAgYWRkQmR6LAogIGFkZEpnLAogIGdldEFzc2V0TGlzdEluZm8sCiAgZ2V0SmdJbmZvTGlzdCwKICBnZXRUcmVlSW5mbywKICByZW1vdmVBc3NldCwKICByZW1vdmVCZHosCiAgcmVtb3ZlSmcsCiAgZ2V0T3JnYW5pemF0aW9uU2VsZWN0ZWQsCiAgZ2V0TmV3VHJlZUluZm8sCiAgZ2V0QmRBc2VzZXRMaXN0UGFnZSwKICBnZXRCZHpEYXRhTGlzdFNlbGVjdGVkLAogIGdldEpnRGF0YUxpc3RTZWxlY3RlZCwKICBnZXRTYmx4RGF0YUxpc3RTZWxlY3RlZCwKICB1cGRhdGVTdGF0dXMKfSBmcm9tICJAL2FwaS9kYWdhbmdPaWxmaWVsZC9hc3NldC9iZHNidHoiOwppbXBvcnQgeyBnZXRSZXN1bURhdGFMaXN0IH0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYXNzZXQvc2RzYiI7CmltcG9ydCBUZWNobmljYWxQYXJhbWV0ZXIgZnJvbSAiQC92aWV3cy9kYWdhbmdPaWxmaWVsZC9iemdsL3NiYnprL3RlY2huaWNhbFBhcmFtZXRlciI7CmltcG9ydCB7CiAgZ2V0UGFyYW1EYXRhTGlzdCwKICBnZXRQYXJhbXNWYWx1ZSwKICBzYXZlUGFyYW1WYWx1ZQp9IGZyb20gIkAvYXBpL2RhZ2FuZ09pbGZpZWxkL2Fzc2V0L3BhcmFtZXRlcnMiOwppbXBvcnQgeyBzYXZlT3JVcGRhdGUgfSBmcm9tICJAL2FwaS95eGdsL2JkeXhnbC9xeGdsIjsKLy/mtYHnqIsKaW1wb3J0IGFjdGl2aXRpIGZyb20gImNvbS9hY3Rpdml0aSI7CmltcG9ydCB0aW1lTGluZSBmcm9tICJjb20vdGltZUxpbmUiOwppbXBvcnQgeyBIaXN0b3J5TGlzdCB9IGZyb20gIkAvYXBpL2FjdGl2aXRpL3Byb2Nlc3NUYXNrIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAicXhiemsiLAogIGNvbXBvbmVudHM6IHsgVGVjaG5pY2FsUGFyYW1ldGVyLCBhY3Rpdml0aSwgdGltZUxpbmUgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy/mtYHnqIvlj4LmlbAKICAgICAgcHJvY2Vzc0RhdGE6IHsKICAgICAgICBwcm9jZXNzRGVmaW5pdGlvbktleTogImR3enl6dGJnIiwKICAgICAgICBidXNpbmVzc0tleTogIiIsCiAgICAgICAgYnVzaW5lc3NUeXBlOiAi5Y+Y55S16K6+5aSH5Y+w6LSmIiwKICAgICAgICB2YXJpYWJsZXM6IHt9LAogICAgICAgIG5leHRVc2VyOiAiIiwKICAgICAgICBkZWZhdWx0RnJvbTogdHJ1ZSwKICAgICAgICBwcm9jZXNzVHlwZTogImNvbXBsZXRlIgogICAgICB9LAogICAgICAvL+a1geeoi+WPmOabtOeUs+ivt+agh+mimAogICAgICBhY3Rpdml0aU9wdGlvbjogeyB0aXRsZTogIueKtuaAgeWPmOabtOeUs+ivtyIgfSwKICAgICAgLy/mtYHnqIvot5/ouKrmlbDmja4KICAgICAgdGltZURhdGE6IFtdLAogICAgICAvL+a1geeoi+i3n+i4quaYvuekuumakOiXjwogICAgICB0aW1lTGluZVNob3c6IGZhbHNlLAogICAgICAvL+a1geeoi+e7hOS7tuaYvuekuumakOiXjwogICAgICBpc1Nob3c6IGZhbHNlLAoKICAgICAgcmVzdW1lUXVlcnk6IHsKICAgICAgICBmb3JlaWduTnVtOiB1bmRlZmluZWQsCiAgICAgICAgc2JseDogdW5kZWZpbmVkCiAgICAgIH0sCiAgICAgIHVwZGF0ZUxpc3Q6IHsKICAgICAgICBzYnp0OiAiIiwKICAgICAgICBvYmpJZDogIiIKICAgICAgfSwKICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIC8v6K6+5aSH5Yig6Zmk6YCJ5oup5YiXCiAgICAgIHNlbGVjdFJvd3M6IFtdLAogICAgICAvL+iuvuWkh+exu+Wei+S4i+aLieahhuaVsOaNrgogICAgICBzYmx4T3B0aW9uc0RhdGFTZWxlY3RlZDogW10sCiAgICAgIC8v5p+l6K+i5Y+Y55S156uZ5LiL5ouJ5qGG5pWw5o2u55qE5Y+C5pWwCiAgICAgIHNlbGVjdEJkek9wdGlvbnNQYXJhbToge30sCiAgICAgIC8v5p+l6K+i6Ze06ZqU5LiL5ouJ5qGG5pWw5o2u55qE5Y+C5pWwCiAgICAgIHNlbGVjdEpnT3B0aW9uc1BhcmFtOiB7fSwKICAgICAgLy/orr7lpIfooajljZUKICAgICAgc2J4eEZvcm06IHt9LAogICAgICAvL+iuvuWkh+mZhOWxnuiuvuaWvWxpc3QKICAgICAgZnNzc0xpc3Q6IFtdLAogICAgICAvL+WPmOeUteiuvuWkh+afpeivouWPguaVsAogICAgICBiZHpxdWVyeVBhcmFtczogewogICAgICAgIHNzZ3M6IHVuZGVmaW5lZCwKICAgICAgICBkeWRqYm06IHVuZGVmaW5lZCwKICAgICAgICBzc2JkejogdW5kZWZpbmVkLAogICAgICAgIHNzamc6IHVuZGVmaW5lZCwKICAgICAgICBzYm1jOiB1bmRlZmluZWQsCiAgICAgICAgYXNzZXRUeXBlQ29kZTogdW5kZWZpbmVkLAogICAgICAgIHNienQ6IHVuZGVmaW5lZCwKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMAogICAgICB9LAogICAgICAvL+aWsOWinuiuvuWkh+aXtuiuvuWkh+eKtuaAgeS4i+aLieahhuaVsOaNrgogICAgICBzYnp0T3B0aW9uc0RhdGFMaXN0OiBbCiAgICAgICAgeyBsYWJlbDogIuWcqOi/kCIsIHZhbHVlOiAi5Zyo6L+QIiB9LAogICAgICAgIHsgbGFiZWw6ICLlgZzov5AiLCB2YWx1ZTogIuWBnOi/kCIgfSwKICAgICAgICB7IGxhYmVsOiAi5oql5bqfIiwgdmFsdWU6ICLmiqXlup8iIH0KICAgICAgXSwKICAgICAgLy/mlrDlop7orr7lpIfml7bmiYDlsZ7pl7TpmpTkuIvmi4nmoYbliJfooagKICAgICAgamdPcHRpb25zRGF0YUxpc3Q6IFtdLAogICAgICAvL+aWsOWinuiuvuWkh+aXtueUteWOi+etiee6p+S4i+aLieahhuaVsOaNrgogICAgICBkeWRqT3B0aW9uc0RhdGFMaXN0OiBbCiAgICAgICAgeyBsYWJlbDogIjExMGtWIiwgdmFsdWU6ICIxMTAiIH0sCiAgICAgICAgeyBsYWJlbDogIjM1a1YiLCB2YWx1ZTogIjM1IiB9LAogICAgICAgIHsgbGFiZWw6ICIxMGtWIiwgdmFsdWU6ICIxMCIgfSwKICAgICAgICB7IGxhYmVsOiAiNmtWIiwgdmFsdWU6ICI2IiB9CiAgICAgIF0sCiAgICAgIC8v5paw5aKe6K6+5aSH5pe25Y+Y55S156uZ5LiL5ouJ5qGGCiAgICAgIGJkek9wdGlvbnNEYXRhTGlzdDogW10sCiAgICAgIC8v5Y+Y55S16K6+5aSH5YiX6KGo5pWw5o2uCiAgICAgIHRhYmxlQW5kUGFnZUluZm86IHsKICAgICAgICBwYWdlcjogewogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMDBdCiAgICAgICAgfSwKICAgICAgICBvcHRpb246IHsKICAgICAgICAgIGNoZWNrQm94OiB0cnVlLAogICAgICAgICAgc2VyaWFsTnVtYmVyOiB0cnVlCiAgICAgICAgfSwKICAgICAgICB0YWJsZURhdGE6IFtdLAogICAgICAgIHRhYmxlSGVhZGVyOiBbCiAgICAgICAgICB7IHByb3A6ICJkZXB0bmFtZSIsIGxhYmVsOiAi5omA5bGe5YWs5Y+4IiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJiZHptYyIsIGxhYmVsOiAi5Y+Y55S156uZ5ZCN56ewIiwgbWluV2lkdGg6ICIxODAiIH0sCiAgICAgICAgICB7IHByb3A6ICJ3em1jIiwgbGFiZWw6ICLmiYDlsZ7pl7TpmpQiLCBtaW5XaWR0aDogIjE4MCIgfSwKICAgICAgICAgIHsgcHJvcDogInNibHhtYyIsIGxhYmVsOiAi6K6+5aSH57G75Z6LIiwgbWluV2lkdGg6ICIxODAiIH0sCiAgICAgICAgICB7IHByb3A6ICJzYm1jIiwgbGFiZWw6ICLorr7lpIflkI3np7AiLCBtaW5XaWR0aDogIjE4MCIgfSwKICAgICAgICAgIHsgcHJvcDogImR5ZGpOYW1lIiwgbGFiZWw6ICLnlLXljovnrYnnuqciLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgcHJvcDogInNienQiLCBsYWJlbDogIuiuvuWkh+eKtuaAgSIsIG1pbldpZHRoOiAiMjUwIiB9LAogICAgICAgICAgeyBwcm9wOiAidHlycSIsIGxhYmVsOiAi5oqV6L+Q5pel5pyfIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJnZ3hoIiwgbGFiZWw6ICLop4TmoLzlnovlj7ciLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgcHJvcDogImVkZHkiLCBsYWJlbDogIumineWumueUteWOiyIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBwcm9wOiAiZWRkbCIsIGxhYmVsOiAi6aKd5a6a55S15rWBIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJlZHBsIiwgbGFiZWw6ICLpop3lrprpopHnjociLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgcHJvcDogInNjY2oiLCBsYWJlbDogIueUn+S6p+WOguWutiIsIG1pbldpZHRoOiAiMTIwIiB9CiAgICAgICAgICAvKiB7CiAgICAgICAgICAgICAgZml4ZWQ6ICJyaWdodCIsCiAgICAgICAgICAgICAgcHJvcDogJ29wZXJhdGlvbicsCiAgICAgICAgICAgICAgbGFiZWw6ICfmk43kvZwnLAogICAgICAgICAgICAgIG1pbldpZHRoOiAnMTIwcHgnLAogICAgICAgICAgICAgIHN0eWxlOiB7ZGlzcGxheTogJ2Jsb2NrJ30sCiAgICAgICAgICAgICAgb3BlcmF0aW9uOiBbCiAgICAgICAgICAgICAgICAvISp7bmFtZTogIueKtuaAgeWPmOabtCIsIGNsaWNrRnVuOiB0aGlzLnVwZGF0ZVN0YXR1c30sCiAgICAgICAgICAgICAgICB7bmFtZTogIua1geeoi+afpeeciyIsIGNsaWNrRnVuOiB0aGlzLnp0YmdsY1NheX0sKiEvCiAgICAgICAgICAgICAgICB7bmFtZTogJ+S/ruaUuScsIGNsaWNrRnVuOiB0aGlzLnVwZGF0ZUFzc2V0fSwKICAgICAgICAgICAgICAgIHtuYW1lOiAn6K+m5oOFJywgY2xpY2tGdW46IHRoaXMuYXNzZXREZXRhaWxzfSwKICAgICAgICAgICAgICBdCiAgICAgICAgICAgIH0sKi8KICAgICAgICBdCiAgICAgIH0sCiAgICAgIHJlc3VtUGFnZUluZm86IHsKICAgICAgICBwYWdlcjogewogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMDBdCiAgICAgICAgfSwKICAgICAgICBvcHRpb246IHsKICAgICAgICAgIGNoZWNrQm94OiB0cnVlLAogICAgICAgICAgc2VyaWFsTnVtYmVyOiB0cnVlCiAgICAgICAgfSwKICAgICAgICB0YWJsZURhdGE6IFtdLAogICAgICAgIHRhYmxlSGVhZGVyOiBbCiAgICAgICAgICB7IHByb3A6ICJmb3JlaWduTnVtIiwgbGFiZWw6ICLorr7lpIflkI3np7AiLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgcHJvcDogInNibHgiLCBsYWJlbDogIuiuvuWkh+exu+WeiyIsIG1pbldpZHRoOiAiMTgwIiB9LAogICAgICAgICAgeyBwcm9wOiAiYmdseCIsIGxhYmVsOiAi5Y+Y5pu057G75Z6LIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJtcyIsIGxhYmVsOiAi5o+P6L+wIiwgbWluV2lkdGg6ICIyNTAiIH0sCiAgICAgICAgICB7IHByb3A6ICJiZ3IiLCBsYWJlbDogIuWPmOabtOS6uiIsIG1pbldpZHRoOiAiMTQwIiB9LAogICAgICAgICAgeyBwcm9wOiAiYmdzaiIsIGxhYmVsOiAi5Y+Y5pu05pe26Ze0IiwgbWluV2lkdGg6ICIxNDAiIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIC8v5Y+Y55S16K6+5aSH562b6YCJ5p2h5Lu2CiAgICAgIGZpbHRlckluZm8xOiB7CiAgICAgICAgZGF0YTogewogICAgICAgICAgc2JtYzogIiIsCiAgICAgICAgICBhc3NldFR5cGVDb2RlOiAiIiwKICAgICAgICAgIHNienQ6ICIiCiAgICAgICAgfSwKICAgICAgICBmaWVsZExpc3Q6IFsKICAgICAgICAgIHsgbGFiZWw6ICLorr7lpIflkI3np7AiLCB0eXBlOiAiaW5wdXQiLCB2YWx1ZTogInNibWMiIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi6K6+5aSH57G75Z6LIiwKICAgICAgICAgICAgdHlwZTogInNlbGVjdCIsCiAgICAgICAgICAgIHZhbHVlOiAiYXNzZXRUeXBlQ29kZSIsCiAgICAgICAgICAgIG9wdGlvbnM6IFsKICAgICAgICAgICAgICB7IGxhYmVsOiAi5bmy5byP56uZ55So5Y+YIiwgdmFsdWU6ICIwMiIgfSwKICAgICAgICAgICAgICB7IGxhYmVsOiAic2Y25pat6Lev5ZmoIiwgdmFsdWU6ICIwMyIgfSwKICAgICAgICAgICAgICB7IGxhYmVsOiAi55yf56m65pat6Lev5ZmoIiwgdmFsdWU6ICIwNCIgfQogICAgICAgICAgICBdCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIuaKlei/kOeKtuaAgSIsCiAgICAgICAgICAgIHR5cGU6ICJzZWxlY3QiLAogICAgICAgICAgICB2YWx1ZTogInNienQiLAogICAgICAgICAgICBvcHRpb25zOiBbCiAgICAgICAgICAgICAgeyBsYWJlbDogIuWcqOi/kCIsIHZhbHVlOiAi5Zyo6L+QIiB9LAogICAgICAgICAgICAgIHsgbGFiZWw6ICLlgZzov5AiLCB2YWx1ZTogIuWBnOi/kCIgfQogICAgICAgICAgICBdCiAgICAgICAgICB9CiAgICAgICAgXQogICAgICB9LAogICAgICAvL+agkee7k+aehOebkeWQrOWxnuaApwogICAgICBmaWx0ZXJUZXh0OiAiIiwKICAgICAgLy/nu4Tnu4fnu5PmnoTkuIvmi4nmlbDmja4KICAgICAgT3JnYW5pemF0aW9uU2VsZWN0ZWRMaXN0OiBbXSwKICAgICAgLy/moJHnu5PmnoTkuIrpnaLlvpfnrZvpgInmoYblj4LmlbAKICAgICAgdHJlZUZvcm06IHt9LAogICAgICAvL+eUteWOi+etiee6p+S4i+aLieahhuaVsOaNrgogICAgICBWb2x0YWdlTGV2ZWxTZWxlY3RlZExpc3Q6IFsKICAgICAgICB7IGxhYmVsOiAiMTEwa1YiLCB2YWx1ZTogIjExMCIgfSwKICAgICAgICB7IGxhYmVsOiAiMzVrViIsIHZhbHVlOiAiMzUiIH0sCiAgICAgICAgeyBsYWJlbDogIjEwa1YiLCB2YWx1ZTogIjEwIiB9LAogICAgICAgIHsgbGFiZWw6ICI2a1YiLCB2YWx1ZTogIjYiIH0KICAgICAgXSwKICAgICAgLy/orr7lpIfkv6Hmga/lsZXnpLoKICAgICAgYXNzZXRJc0Rpc2FibGU6IGZhbHNlLAogICAgICAvL+aKgOacr+WPguaVsOWKqOaAgeWxleekuumbhuWQiAogICAgICBqc2NzTGFiZWxMaXN0OiBbXSwKICAgICAgLy/mioDmnK/lj4LmlbDnu5HlrpoKICAgICAganNjc0Zvcm06IHt9LAogICAgICAvL+iuvuWkh+S4iumdouWxleekuueahOaMiemSrijmlrDlop7jgIHliKDpmaQpCiAgICAgIGlzU2hvdzM6IHRydWUsCiAgICAgIC8v6K6+5aSH5bGl5Y6G54q25oCB5Y+Y5pu06K6w5b2VCiAgICAgIHNibGx6dGJnamxMaXN0OiBbCiAgICAgICAgewogICAgICAgICAgc3NnczogIua4r+S4nOWIhuWFrOWPuCIsCiAgICAgICAgICBkem1jOiAiMeWPt+WPmOeUteermSIsCiAgICAgICAgICBzYmx4OiAi5Li75Y+Y5Y6L5ZmoIiwKICAgICAgICAgIGJnbHg6ICLnirbmgIHlj5jmm7QiLAogICAgICAgICAgZGVzYzogIueKtuaAgeWPmOabtOS4uuaKpeW6nyIsCiAgICAgICAgICBiZ3NqOiAiMjAyMi0xMi0xMiIsCiAgICAgICAgICBiZ3I6ICLlvKDkuIkiCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBzc2dzOiAi5riv5Lic5YiG5YWs5Y+4IiwKICAgICAgICAgIGR6bWM6ICIx5Y+35Y+Y55S156uZIiwKICAgICAgICAgIHNibHg6ICLkuLvlj5jljovlmagiLAogICAgICAgICAgYmdseDogIueKtuaAgeWPmOabtCIsCiAgICAgICAgICBkZXNjOiAi54q25oCB5Y+Y5pu05Li65oql5bqfIiwKICAgICAgICAgIGJnc2o6ICIyMDIyLTEyLTEyIiwKICAgICAgICAgIGJncjogIuW8oOS4iSIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHNzZ3M6ICLmuK/kuJzliIblhazlj7giLAogICAgICAgICAgZHptYzogIjHlj7flj5jnlLXnq5kiLAogICAgICAgICAgc2JseDogIuS4u+WPmOWOi+WZqCIsCiAgICAgICAgICBiZ2x4OiAi54q25oCB5Y+Y5pu0IiwKICAgICAgICAgIGRlc2M6ICLnirbmgIHlj5jmm7TkuLrmiqXlup8iLAogICAgICAgICAgYmdzajogIjIwMjItMTItMTIiLAogICAgICAgICAgYmdyOiAi5byg5LiJIgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgc3NnczogIua4r+S4nOWIhuWFrOWPuCIsCiAgICAgICAgICBkem1jOiAiMeWPt+WPmOeUteermSIsCiAgICAgICAgICBzYmx4OiAi5Li75Y+Y5Y6L5ZmoIiwKICAgICAgICAgIGJnbHg6ICLnirbmgIHlj5jmm7QiLAogICAgICAgICAgZGVzYzogIueKtuaAgeWPmOabtOS4uuaKpeW6nyIsCiAgICAgICAgICBiZ3NqOiAiMjAyMi0xMi0xMiIsCiAgICAgICAgICBiZ3I6ICLlvKDkuIkiCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBzc2dzOiAi5riv5Lic5YiG5YWs5Y+4IiwKICAgICAgICAgIGR6bWM6ICIx5Y+35Y+Y55S156uZIiwKICAgICAgICAgIHNibHg6ICLkuLvlj5jljovlmagiLAogICAgICAgICAgYmdseDogIueKtuaAgeWPmOabtCIsCiAgICAgICAgICBkZXNjOiAi54q25oCB5Y+Y5pu05Li65oql5bqfIiwKICAgICAgICAgIGJnc2o6ICIyMDIyLTEyLTEyIiwKICAgICAgICAgIGJncjogIuW8oOS4iSIKICAgICAgICB9CiAgICAgIF0sCiAgICAgIC8v6K6+5aSH5bGl5Y6G57y66Zm36K6w5b2V5pWw5o2u6ZuG5ZCICiAgICAgIHNibGxxeGpsTGlzdDogWwogICAgICAgIHsKICAgICAgICAgIHNzZ3M6ICLmuK/kuJzliIblhazlj7giLAogICAgICAgICAgZHptYzogIjHlj7flj5jnlLXnq5kiLAogICAgICAgICAgc2JseDogIuS4u+WPmOWOi+WZqCIsCiAgICAgICAgICBxeHh6OiAi5Lil6YeNIiwKICAgICAgICAgIGR5ZGo6ICIzNWtWIiwKICAgICAgICAgIHNieGg6ICJYWFjlnovlj7ciLAogICAgICAgICAgc2NjajogIlhYWOWOguWutiIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHNzZ3M6ICLmuK/kuJzliIblhazlj7giLAogICAgICAgICAgZHptYzogIjHlj7flj5jnlLXnq5kiLAogICAgICAgICAgc2JseDogIuS4u+WPmOWOi+WZqCIsCiAgICAgICAgICBxeHh6OiAi5Lil6YeNIiwKICAgICAgICAgIGR5ZGo6ICIzNWtWIiwKICAgICAgICAgIHNieGg6ICJYWFjlnovlj7ciLAogICAgICAgICAgc2NjajogIlhYWOWOguWutiIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHNzZ3M6ICLmuK/kuJzliIblhazlj7giLAogICAgICAgICAgZHptYzogIjHlj7flj5jnlLXnq5kiLAogICAgICAgICAgc2JseDogIuS4u+WPmOWOi+WZqCIsCiAgICAgICAgICBxeHh6OiAi5Lil6YeNIiwKICAgICAgICAgIGR5ZGo6ICIzNWtWIiwKICAgICAgICAgIHNieGg6ICJYWFjlnovlj7ciLAogICAgICAgICAgc2NjajogIlhYWOWOguWutiIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHNzZ3M6ICLmuK/kuJzliIblhazlj7giLAogICAgICAgICAgZHptYzogIjHlj7flj5jnlLXnq5kiLAogICAgICAgICAgc2JseDogIuS4u+WPmOWOi+WZqCIsCiAgICAgICAgICBxeHh6OiAi5Lil6YeNIiwKICAgICAgICAgIGR5ZGo6ICIzNWtWIiwKICAgICAgICAgIHNieGg6ICJYWFjlnovlj7ciLAogICAgICAgICAgc2NjajogIlhYWOWOguWutiIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHNzZ3M6ICLmuK/kuJzliIblhazlj7giLAogICAgICAgICAgZHptYzogIjHlj7flj5jnlLXnq5kiLAogICAgICAgICAgc2JseDogIuS4u+WPmOWOi+WZqCIsCiAgICAgICAgICBxeHh6OiAi5Lil6YeNIiwKICAgICAgICAgIGR5ZGo6ICIzNWtWIiwKICAgICAgICAgIHNieGg6ICJYWFjlnovlj7ciLAogICAgICAgICAgc2NjajogIlhYWOWOguWutiIKICAgICAgICB9CiAgICAgIF0sCiAgICAgIC8v6K6+5aSH5bGl5Y6G6K+V6aqM6K6w5b2V5pWw5o2uCiAgICAgIHNibHZzeWpsTGlzdDogWwogICAgICAgIHsKICAgICAgICAgIHN5enk6ICLluKbnlLUiLAogICAgICAgICAgc3l4ejogIuS+i+ihjOivlemqjCIsCiAgICAgICAgICBzeW1jOiAiWFhYWFgiLAogICAgICAgICAgZ3pkZDogIlhYWOW5s+WPsCIsCiAgICAgICAgICBzeXNiOiAi5Li75Y+Y5Y6L5ZmoIiwKICAgICAgICAgIHN5Ymc6ICIiLAogICAgICAgICAgdHE6ICLmmbQiLAogICAgICAgICAgc3lycTogIjIwMjItMDEtMDEiLAogICAgICAgICAgbHJyOiAi5byg5LiJIiwKICAgICAgICAgIHN5amw6ICJYWFhYWCIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHN5enk6ICLluKbnlLUiLAogICAgICAgICAgc3l4ejogIuS+i+ihjOivlemqjCIsCiAgICAgICAgICBzeW1jOiAiWFhYWFgiLAogICAgICAgICAgZ3pkZDogIlhYWOW5s+WPsCIsCiAgICAgICAgICBzeXNiOiAi5Li75Y+Y5Y6L5ZmoIiwKICAgICAgICAgIHN5Ymc6ICIiLAogICAgICAgICAgdHE6ICLmmbQiLAogICAgICAgICAgc3lycTogIjIwMjItMDEtMDEiLAogICAgICAgICAgbHJyOiAi5byg5LiJIiwKICAgICAgICAgIHN5amw6ICJYWFhYWCIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHN5enk6ICLluKbnlLUiLAogICAgICAgICAgc3l4ejogIuS+i+ihjOivlemqjCIsCiAgICAgICAgICBzeW1jOiAiWFhYWFgiLAogICAgICAgICAgZ3pkZDogIlhYWOW5s+WPsCIsCiAgICAgICAgICBzeXNiOiAi5Li75Y+Y5Y6L5ZmoIiwKICAgICAgICAgIHN5Ymc6ICIiLAogICAgICAgICAgdHE6ICLmmbQiLAogICAgICAgICAgc3lycTogIjIwMjItMDEtMDEiLAogICAgICAgICAgbHJyOiAi5byg5LiJIiwKICAgICAgICAgIHN5amw6ICJYWFhYWCIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHN5enk6ICLluKbnlLUiLAogICAgICAgICAgc3l4ejogIuS+i+ihjOivlemqjCIsCiAgICAgICAgICBzeW1jOiAiWFhYWFgiLAogICAgICAgICAgZ3pkZDogIlhYWOW5s+WPsCIsCiAgICAgICAgICBzeXNiOiAi5Li75Y+Y5Y6L5ZmoIiwKICAgICAgICAgIHN5Ymc6ICIiLAogICAgICAgICAgdHE6ICLmmbQiLAogICAgICAgICAgc3lycTogIjIwMjItMDEtMDEiLAogICAgICAgICAgbHJyOiAi5byg5LiJIiwKICAgICAgICAgIHN5amw6ICJYWFhYWCIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHN5enk6ICLluKbnlLUiLAogICAgICAgICAgc3l4ejogIuS+i+ihjOivlemqjCIsCiAgICAgICAgICBzeW1jOiAiWFhYWFgiLAogICAgICAgICAgZ3pkZDogIlhYWOW5s+WPsCIsCiAgICAgICAgICBzeXNiOiAi5Li75Y+Y5Y6L5ZmoIiwKICAgICAgICAgIHN5Ymc6ICIiLAogICAgICAgICAgdHE6ICLmmbQiLAogICAgICAgICAgc3lycTogIjIwMjItMDEtMDEiLAogICAgICAgICAgbHJyOiAi5byg5LiJIiwKICAgICAgICAgIHN5amw6ICJYWFhYWCIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHN5enk6ICLluKbnlLUiLAogICAgICAgICAgc3l4ejogIuS+i+ihjOivlemqjCIsCiAgICAgICAgICBzeW1jOiAiWFhYWFgiLAogICAgICAgICAgZ3pkZDogIlhYWOW5s+WPsCIsCiAgICAgICAgICBzeXNiOiAi5Li75Y+Y5Y6L5ZmoIiwKICAgICAgICAgIHN5Ymc6ICIiLAogICAgICAgICAgdHE6ICLmmbQiLAogICAgICAgICAgc3lycTogIjIwMjItMDEtMDEiLAogICAgICAgICAgbHJyOiAi5byg5LiJIiwKICAgICAgICAgIHN5amw6ICJYWFhYWCIKICAgICAgICB9CiAgICAgIF0sCiAgICAgIC8v6K6+5aSH5bGl5Y6GdGFi6aG1CiAgICAgIHNibGxEZXNjVGFiTmFtZTogInN5amwiLAogICAgICAvL+i9ruaSreWbvueJhwogICAgICBpbWdMaXN0OiBbCiAgICAgICAgewogICAgICAgICAgdXJsOgogICAgICAgICAgICAiaHR0cHM6Ly9jdWJlLmVsZW1lY2RuLmNvbS82Lzk0LzRkM2VhNTNjMDg0YmFkNjkzMWE1NmQ1MTU4YTQ4anBlZy5qcGVnIgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdXJsOgogICAgICAgICAgICAiaHR0cHM6Ly9jdWJlLmVsZW1lY2RuLmNvbS82Lzk0LzRkM2VhNTNjMDg0YmFkNjkzMWE1NmQ1MTU4YTQ4anBlZy5qcGVnIgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdXJsOgogICAgICAgICAgICAiaHR0cHM6Ly9jdWJlLmVsZW1lY2RuLmNvbS82Lzk0LzRkM2VhNTNjMDg0YmFkNjkzMWE1NmQ1MTU4YTQ4anBlZy5qcGVnIgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdXJsOgogICAgICAgICAgICAiaHR0cHM6Ly9jdWJlLmVsZW1lY2RuLmNvbS82Lzk0LzRkM2VhNTNjMDg0YmFkNjkzMWE1NmQ1MTU4YTQ4anBlZy5qcGVnIgogICAgICAgIH0KICAgICAgXSwKICAgICAgLy/nu4Tnu4fmoJEKICAgICAgdHJlZU9wdGlvbnM6IFtdLAogICAgICAvL+iuvuWkh+ivpuaDhemhteW6lemDqOehruiupOWPlua2iOaMiemSruaOp+WItgogICAgICBzYkNvbW1pdERpYWxvZ0NvdHJvbDogdHJ1ZSwKICAgICAgLy/lvLnlh7rmoYZ0YWLpobUKICAgICAgYWN0aXZlVGFiTmFtZTogInNiRGVzYyIsCiAgICAgIC8v5Y+Y55S156uZ5bGV56S6CiAgICAgIGJkelNob3dUYWJsZTogdHJ1ZSwKICAgICAgLy/pl7TpmpTlsZXnpLoKICAgICAgamdTaG93VGFibGU6IGZhbHNlLAogICAgICAvL+iuvuWkh+WxleekugogICAgICBzYlNob3dUYWJsZTogZmFsc2UsCiAgICAgIC8v6K6+5aSH5by55Ye65qGGCiAgICAgIGRpYWxvZ0Zvcm1WaXNpYmxlOiBmYWxzZSwKICAgICAgLy/lj5jnlLXnq5nmt7vliqDmjInpkq7lvLnlh7rmoYYKICAgICAgYmR6RGlhbG9nRm9ybVZpc2libGU6IGZhbHNlLAogICAgICAvL+mXtOmalOa3u+WKoOaMiemSruW8ueWHuuahhgogICAgICBqZ0RpYWxvZ0Zvcm1WaXNpYmxlOiBmYWxzZSwKICAgICAgLy/lvLnlh7rmoYbooajljZUKICAgICAgZm9ybToge30sCiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICBwYXJhbVF1ZXJ5OiB7CiAgICAgICAgc2JseGJtOiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgcnVsZXM6IHsKICAgICAgICBzc2JkejogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqeaJgOWxnuWPmOeUteermSIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0KICAgICAgICBdLAogICAgICAgIHNiZG06IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+35aGr5YaZ6K6+5aSH5Luj56CBIiwgdHJpZ2dlcjogImJsdXIiIH1dLAogICAgICAgIHNibWM6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+35aGr5YaZ6K6+5aSH5ZCN56ewIiwgdHJpZ2dlcjogImJsdXIiIH1dLAogICAgICAgIGR5ZGpibTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqeeUteWOi+etiee6pyIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0KICAgICAgICBdLAogICAgICAgIHNienQ6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6norr7lpIfnirbmgIEiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9CiAgICAgICAgXSwKICAgICAgICBhc3NldFR5cGVDb2RlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup6K6+5aSH57G75Z6LIiwgdHJpZ2dlcjogImNoYW5nZSIgfQogICAgICAgIF0sCiAgICAgICAgc3NqZzogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqeaJgOWxnumXtOmalCIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0KICAgICAgICBdLAogICAgICAgIHNzZ3M6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup5omA5bGe5YWs5Y+4IiwgdHJpZ2dlcjogImNoYW5nZSIgfV0KICAgICAgfSwKCiAgICAgIC8v5a6h5om55oyJ6ZKuCiAgICAgIHNwQnV0dG9uRGlzYWJsZWQ6IHRydWUKICAgIH07CiAgfSwKICB3YXRjaDogewogICAgLy/nm5HlkKznrZvpgInmoYblgLzlj5HnlJ/lj5jljJbov5vogIznrZvpgInmoJHnu5PmnoQKICAgIGZpbHRlclRleHQodmFsKSB7CiAgICAgIHRoaXMuJHJlZnMudHJlZS5maWx0ZXIodmFsKTsKICAgIH0KICB9LAogIGNyZWF0ZWQoKSB7CiAgICAvL+iOt+WPlue7hOe7h+e7k+aehOS4i+aLieaVsOaNrgogICAgdGhpcy5nZXRPcmdhbml6YXRpb25TZWxlY3RlZCgpOwogICAgLy/ojrflj5bmlrDnmoTorr7lpIfmi5PmiZHmoJEKICAgIHRoaXMuZ2V0TmV3VHJlZUluZm8oKTsKICAgIC8v5Yid5aeL5YyW5Yqg6L295pe25Yqg6L295omA5pyJ5Y+Y55S156uZ5LiL6Z2i55qE6K6+5aSH5L+h5oGvCiAgICB0aGlzLmdldERhdGEoKTsKICAgIC8v6I635Y+W6K6+5aSH57G75Z6L5LiL5ouJ5qGG5pWw5o2uCiAgICB0aGlzLmdldFNibHhEYXRhTGlzdFNlbGVjdGVkKCk7CiAgfSwKICBtb3VudGVkKCkgewogICAgLy/ojrflj5blj5jnlLXnq5nkuIvmi4nmoYbmlbDmja4KICAgIHRoaXMuZ2V0QmR6RGF0YUxpc3RTZWxlY3RlZCgpOwogIH0sCiAgbWV0aG9kczogewogICAgLy/lrqHmibkKICAgIHNwWnRiZ2psKCkgewogICAgICBsZXQgcm93ID0gdGhpcy5zZWxlY3RSb3dzWzBdOwogICAgICB0aGlzLmFjdGl2aXRpT3B0aW9uLnRpdGxlID0gIua1geeoi+aPkOS6pCI7CiAgICAgIHRoaXMucHJvY2Vzc0RhdGEuZGVmYXVsdEZyb20gPSBmYWxzZTsKICAgICAgdGhpcy5wcm9jZXNzRGF0YS5wcm9jZXNzVHlwZSA9ICJjb21wbGV0ZSI7CiAgICAgIHRoaXMucHJvY2Vzc0RhdGEuYnVzaW5lc3NLZXkgPSByb3cub2JqSWQ7CiAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnBhc3MgPSB0cnVlOwogICAgICB0aGlzLmlzU2hvdyA9IHRydWU7CiAgICB9LAogICAgLy/mtYHnqIvmn6XnnIsKICAgIGFzeW5jIHp0YmdsY1NheShyb3cpIHsKICAgICAgdGhpcy5wcm9jZXNzRGF0YS5idXNpbmVzc0tleSA9IHJvdy5vYmpJZDsKICAgICAgbGV0IHsgY29kZSwgZGF0YSB9ID0gYXdhaXQgSGlzdG9yeUxpc3QodGhpcy5wcm9jZXNzRGF0YSk7CiAgICAgIHRoaXMudGltZURhdGEgPSBkYXRhOwogICAgICB0aGlzLnRpbWVMaW5lU2hvdyA9IHRydWU7CiAgICB9LAogICAgLy/mtYHnqIvlm57osIMKICAgIGFzeW5jIHRvZG9SZXN1bHQoZGF0YSkgewogICAgICBjb25zb2xlLmxvZygi5rWB56iL5Zue6LCD5pa55rOV77yaIiwgZGF0YSk7CiAgICAgIHN3aXRjaCAoZGF0YS5hY3RpdmVUYXNrTmFtZSkgewogICAgICAgIGNhc2UgIue7k+adnyI6CiAgICAgICAgICB1cGRhdGVTdGF0dXMoZGF0YS52YXJpYWJsZXMudXBkYXRlTGlzdCkudGhlbihyZXMgPT4gewogICAgICAgICAgICBpZiAocmVzLmNvZGUgPT0gIjAwMDAiKSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLorr7lpIfnirbmgIHlt7Llj5jmm7TvvIEiKTsKICAgICAgICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgICBicmVhazsKICAgICAgfQogICAgfSwKICAgIC8v5LiK5oql5Y+R6YCB5Yqe57uTCiAgICBnZXRTYkZzQmooYXJncykgewogICAgICBjb25zb2xlLmxvZyhhcmdzKTsKICAgICAgbGV0IHJvdyA9IHsgLi4uYXJncy5kYXRhIH07CiAgICAgIGlmIChhcmdzLnR5cGUgPT09ICJjb21wbGV0ZSIpIHsKICAgICAgICB0aGlzLmFjdGl2aXRpT3B0aW9uLnRpdGxlID0gIuS6uuWRmOmAieaLqSI7CiAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5kZWZhdWx0RnJvbSA9IHRydWU7CiAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5wcm9jZXNzVHlwZSA9ICJjb21wbGV0ZSI7CiAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5idXNpbmVzc0tleSA9IHJvdy5vYmpJZDsKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnZhcmlhYmxlcy5wYXNzID0gdHJ1ZTsKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnZhcmlhYmxlcy51cGRhdGVMaXN0ID0gcm93OwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuYWN0aXZpdGlPcHRpb24udGl0bGUgPSAi5Zue6YCA5Y6f5Zug5o+Q5oqlIjsKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmRlZmF1bHRGcm9tID0gdHJ1ZTsKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnByb2Nlc3NUeXBlID0gInJvbGxiYWNrIjsKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmJ1c2luZXNzS2V5ID0gcm93Lm9iaklkOwogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnBhc3MgPSB0cnVlOwogICAgICB9CiAgICAgIHRoaXMuaXNTaG93ID0gdHJ1ZTsKICAgIH0sCiAgICAvL+WFs+mXrea1geeoi+e7hOS7tgogICAgY2xvc2VBY3Rpdml0aSgpIHsKICAgICAgdGhpcy5pc1Nob3cgPSBmYWxzZTsKICAgIH0sCiAgICAvL+WFs+mXrea1geeoi+afpeeci+mhtemdogogICAgY29sc2VUaW1lTGluZSgpIHsKICAgICAgdGhpcy50aW1lTGluZVNob3cgPSBmYWxzZTsKICAgIH0sCiAgICAvKioKICAgICAqIOiuvuWkh+ihqOagvOWkmumAieahhgogICAgICovCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uocm93cykgewogICAgICB0aGlzLnNlbGVjdFJvd3MgPSByb3dzOwoKICAgICAgLy/lrqHmibnmjInpkq4KICAgICAgdGhpcy5zcEJ1dHRvbkRpc2FibGVkID0gcm93cy5sZW5ndGggIT0gMTsKICAgIH0sCiAgICAvKioKICAgICAqIOiOt+WPluiuvuWkh+exu+Wei+S4i+aLieahhuaVsOaNrgogICAgICovCiAgICBnZXRTYmx4RGF0YUxpc3RTZWxlY3RlZCgpIHsKICAgICAgbGV0IHNibHhQYXJhbSA9IHsKICAgICAgICB0eXBlOiAi5Y+Y55S16K6+5aSHIgogICAgICB9OwogICAgICBnZXRTYmx4RGF0YUxpc3RTZWxlY3RlZChzYmx4UGFyYW0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLnNibHhPcHRpb25zRGF0YVNlbGVjdGVkID0gcmVzLmRhdGE7CiAgICAgIH0pOwogICAgfSwKCiAgICAvKioKICAgICAqIOWIoOmZpOiuvuWkh+S/oeaBrwogICAgICovCiAgICByZW1vdmVBc3NldCgpIHsKICAgICAgaWYgKHRoaXMuc2VsZWN0Um93cy5sZW5ndGggPCAxKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7fpgInmi6nmraPnoa7nmoTmlbDmja7vvIHvvIHvvIEiKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgbGV0IGlkcyA9IHRoaXMuc2VsZWN0Um93cy5tYXAoaXRlbSA9PiB7CiAgICAgICAgcmV0dXJuIGl0ZW0ub2JqSWQ7CiAgICAgIH0pOwogICAgICB0aGlzLiRjb25maXJtKCLmraTmk43kvZzlsIbmsLjkuYXliKDpmaTor6XmlbDmja4sIOaYr+WQpue7p+e7rT8iLCAi5o+Q56S6IiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkKICAgICAgICAudGhlbigoKSA9PiB7CiAgICAgICAgICByZW1vdmVBc3NldChpZHMpLnRoZW4oKHsgY29kZSB9KSA9PiB7CiAgICAgICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTmiJDlip8hIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTlpLHotKUhIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoKSA9PiB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogImluZm8iLAogICAgICAgICAgICBtZXNzYWdlOiAi5bey5Y+W5raI5Yig6ZmkIgogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci5wYWdlUmVzaXplID0gIlkiOwogICAgICB0aGlzLmdldERhdGEoKTsKICAgIH0sCiAgICAvL+iuvuWkh+a3u+WKoOaMiemSrgogICAgc2JBZGRTZW5zb3JCdXR0b24oKSB7CiAgICAgIC8v5riF56m66KGo5Y2VCiAgICAgIC8vIHRoaXMuc2J4eEZvcm0gPSB7fTsKICAgICAgLy8gdGhpcy5qc2NzRm9ybT17fTsKICAgICAgLy8KICAgICAgLy8gbGV0IHJvdz17fTsKICAgICAgLy8gdGhpcy50ZWNobmljYWxQYXJhbWV0ZXJzKHJvdyk7CiAgICAgIC8v5omT5byA5by55Ye65qGGCiAgICAgIHRoaXMuZGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgICAvL+aMiemSruWSjOihqOWNleaYr+WQpuWPr+e8lui+keaOp+WItgogICAgICB0aGlzLmFzc2V0SXNEaXNhYmxlID0gZmFsc2U7CiAgICB9LAogICAgLy/lj5jnlLXnq5nkuIvmi4nmoYbkuK3nmoRjaGFuZ2Xkuovku7YKICAgIGJkek9wdGlvbnNDaGFuZ2VDbGljaygpIHsKICAgICAgLy/lvZPlj5HnlJ9jaGFuZ2Xkuovku7bml7blhYjmuIXnqbrkuYvliY3nmoTpl7TpmpTkv6Hmga8KICAgICAgdGhpcy4kc2V0KHRoaXMuc2J4eEZvcm0sICJzc2pnIiwgIiIpOwogICAgICAvL+iwg+eUqOafpeivoumXtOmalOaWueazlQogICAgICB0aGlzLmdldEpnRGF0YUxpc3RTZWxlY3RlZCgpOwogICAgfSwKICAgIC8v6I635Y+W5Y+Y55S156uZ5LiL5ouJ5qGG5pWw5o2uCiAgICBnZXRCZHpEYXRhTGlzdFNlbGVjdGVkKCkgewogICAgICBnZXRCZHpEYXRhTGlzdFNlbGVjdGVkKHRoaXMuc2VsZWN0QmR6T3B0aW9uc1BhcmFtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5iZHpPcHRpb25zRGF0YUxpc3QgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICAgIC8v6LCD55So5p+l6K+i6Ze06ZqU5pa55rOVCiAgICAgIC8vIHRoaXMuZ2V0SmdEYXRhTGlzdFNlbGVjdGVkKCk7CiAgICB9LAogICAgLy/ojrflj5bpl7TpmpTkuIvmi4nmoYbmlbDmja4KICAgIGdldEpnRGF0YUxpc3RTZWxlY3RlZCgpIHsKICAgICAgLy/nu5nojrflj5bpl7TpmpTkuIvmi4nmoYbmn6Xor6Llj4LmlbDotYvlgLwKICAgICAgdGhpcy5zZWxlY3RKZ09wdGlvbnNQYXJhbS5zc2JkeiA9IHRoaXMuc2J4eEZvcm0uc3NiZHo7CiAgICAgIGdldEpnRGF0YUxpc3RTZWxlY3RlZCh0aGlzLnNlbGVjdEpnT3B0aW9uc1BhcmFtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5qZ09wdGlvbnNEYXRhTGlzdCA9IHJlcy5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICB1cGRhdGVTdGF0dXMocm93KSB7CiAgICAgIHRoaXMudXBkYXRlTGlzdC5zYnp0ID0gcm93LnNienQ7CiAgICAgICh0aGlzLnVwZGF0ZUxpc3Qub2JqSWQgPSByb3cub2JqSWQpLCAodGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZSk7CiAgICB9LAogICAgc3VibWl0U3RhdHVzKCkgewogICAgICB0aGlzLiRjb25maXJtKCLnoa7orqTlsIborr7lpIfnirbmgIHkv67mlLnkuLoiICsgdGhpcy51cGRhdGVMaXN0LnNienQgKyAiPyIsICIiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgLy/lhbPpl63nirbmgIHlj5jmm7TlvLnnqpcKICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgICAgICAvL+aJk+W8gOS6uuWRmOmAieaLqeW8ueeqlwogICAgICAgIHRoaXMuaXNTaG93ID0gdHJ1ZTsKICAgICAgICB0aGlzLmdldFNiRnNCaih7IGRhdGE6IHRoaXMudXBkYXRlTGlzdCwgdHlwZTogImNvbXBsZXRlIiB9KTsKICAgICAgICAvLyB1cGRhdGVTdGF0dXModGhpcy51cGRhdGVMaXN0KS50aGVuKHJlcz0+ewogICAgICAgIC8vICAgaWYocmVzLmNvZGU9PSIwMDAwIil7CiAgICAgICAgLy8gICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi6K6+5aSH54q25oCB5bey5Y+Y5pu077yBIikKICAgICAgICAvLyAgICAgdGhpcy5kaWFsb2dWaXNpYmxlPWZhbHNlOwogICAgICAgIC8vICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICAvLyAgIH0KICAgICAgICAvLyB9KQogICAgICB9KTsKICAgIH0sCgogICAgLyoqCiAgICAgKiDorr7lpIflsaXljoYKICAgICAqLwogICAgZ2V0UmVzdW1MaXN0KHBhcikgewogICAgICBsZXQgcGFyYW0gPSB7IC4uLnBhciwgLi4udGhpcy5yZXN1bWVRdWVyeSB9OwogICAgICBnZXRSZXN1bURhdGFMaXN0KHBhcmFtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5yZXN1bVBhZ2VJbmZvLnRhYmxlRGF0YSA9IHJlcy5kYXRhLnJlY29yZHM7CiAgICAgICAgdGhpcy5yZXN1bVBhZ2VJbmZvLnBhZ2VyLnRvdGFsID0gcmVzLmRhdGEudG90YWw7CiAgICAgIH0pOwogICAgfSwKCiAgICAvL+iuvuWkh+S/ruaUueaTjeS9nAogICAgdXBkYXRlQXNzZXQocm93KSB7CiAgICAgIHRoaXMudGVjaG5pY2FsUGFyYW1ldGVycyhyb3cpOwogICAgICB0aGlzLnBhcmFtUXVlcnkuc2JseGJtID0gcm93LmFzc2V0VHlwZUNvZGU7CiAgICAgIHRoaXMuZ2V0UGFyYW1ldGVycygpOwogICAgICB0aGlzLnJlc3VtZVF1ZXJ5LmZvcmVpZ25OdW0gPSByb3cuc2JtYzsKICAgICAgdGhpcy5yZXN1bWVRdWVyeS5zYmx4ID0gcm93LnNibHg7CiAgICAgIHRoaXMuZ2V0UmVzdW1MaXN0KCk7CiAgICAgIC8v5omT5byA6K6+5aSH5by55Ye65qGGCiAgICAgIHRoaXMuZGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgICAvL+e7meihqOWNlei1i+WAvAogICAgICB0aGlzLnNieHhGb3JtID0gcm93OwogICAgICAvL+aMiemSruWSjOihqOWNleaYr+WQpuWPr+e8lui+keaOp+WItgogICAgICB0aGlzLmFzc2V0SXNEaXNhYmxlID0gZmFsc2U7CiAgICB9LAogICAgLy/orr7lpIfor6bmg4Xmk43kvZwKICAgIGFzc2V0RGV0YWlscyhyb3cpIHsKICAgICAgdGhpcy50ZWNobmljYWxQYXJhbWV0ZXJzKHJvdyk7CgogICAgICB0aGlzLnJlc3VtZVF1ZXJ5LmZvcmVpZ25OdW0gPSByb3cuc2JtYzsKICAgICAgdGhpcy5yZXN1bWVRdWVyeS5zYmx4ID0gcm93LnNibHg7CiAgICAgIHRoaXMuZ2V0UmVzdW1MaXN0KCk7CiAgICAgIC8v5omT5byA6K6+5aSH5by55Ye65qGGCiAgICAgIHRoaXMuZGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgICAvL+e7meihqOWNlei1i+WAvAogICAgICB0aGlzLnNieHhGb3JtID0gcm93OwogICAgICAvL+aMiemSruWSjOihqOWNleaYr+WQpuWPr+e8lui+keaOp+WItgogICAgICB0aGlzLmFzc2V0SXNEaXNhYmxlID0gdHJ1ZTsKICAgIH0sCiAgICAvL+iuvuWkh+WxpeWOhnRhYumhteeCueWHu+S6i+S7tgogICAgaGFuZGxlU2JsbERlc2NUYWJOYW1lQ2xpY2sodGFiLCBldmVudCkgewogICAgICBjb25zb2xlLmxvZyh0YWIsIGV2ZW50KTsKICAgIH0sCiAgICAvL+etm+mAieadoeS7tumHjee9rgogICAgZmlsdGVyUmVzZXQoKSB7fSwKICAgIC8v5Y+Y55S16K6+5aSH5Y+w6LSm5p+l6K+iCiAgICBhc3luYyBnZXREYXRhKHBhcmFtcykgewogICAgICB0cnkgewogICAgICAgIGNvbnN0IHBhcmFtID0geyAuLi50aGlzLmJkenF1ZXJ5UGFyYW1zLCAuLi5wYXJhbXMgfTsKICAgICAgICBjb25zdCB7IGRhdGEsIGNvZGUgfSA9IGF3YWl0IGdldEJkQXNlc2V0TGlzdFBhZ2UocGFyYW0pOwogICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby50YWJsZURhdGEgPSBkYXRhLnJlY29yZHM7CiAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIudG90YWwgPSBkYXRhLnRvdGFsOwogICAgICAgICAgY29uc29sZS5sb2coImRhdGE66K6+5aSH5pWw5o2uIiwgZGF0YS5yZWNvcmRzKTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICBjb25zb2xlLmxvZyhlKTsKICAgICAgfQogICAgfSwKICAgIC8v5qCR55uR5ZCs5LqL5Lu2CiAgICBmaWx0ZXJOb2RlKHZhbHVlLCBkYXRhKSB7CiAgICAgIGlmICghdmFsdWUpIHJldHVybiB0cnVlOwogICAgICByZXR1cm4gZGF0YS5sYWJlbC5pbmRleE9mKHZhbHVlKSAhPT0gLTE7CiAgICB9LAogICAgLy/ojrflj5bmlrDnmoTorr7lpIfmi5PmiZHmoJEKICAgIGdldE5ld1RyZWVJbmZvKCkgewogICAgICAvL+e7meafpeivouiuvuWkh+WPguaVsOi1i+WAvAogICAgICB0aGlzLmJkenF1ZXJ5UGFyYW1zLnNzZ3MgPSB0aGlzLnRyZWVGb3JtLnNzZHdibTsKICAgICAgdGhpcy5iZHpxdWVyeVBhcmFtcy5keWRqYm0gPSB0aGlzLnRyZWVGb3JtLmR5ZGpibTsKICAgICAgZ2V0TmV3VHJlZUluZm8odGhpcy50cmVlRm9ybSkudGhlbihyZXMgPT4gewogICAgICAgIGNvbnNvbGUubG9nKCJz5qCR57uT5p6E5pWw5o2uOiIsIHJlcyk7CiAgICAgICAgdGhpcy50cmVlT3B0aW9ucyA9IHJlcy5kYXRhOwogICAgICB9KTsKICAgICAgLy/ojrflj5borr7lpIfmlrnms5UKICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICB9LAogICAgLy/ojrflj5bnu4Tnu4fnu5PmnoTkuIvmi4nmoYbmlbDmja4KICAgIGdldE9yZ2FuaXphdGlvblNlbGVjdGVkKCkgewogICAgICBsZXQgcGFyZW50SWQgPSAiMTAwMSI7CiAgICAgIGdldE9yZ2FuaXphdGlvblNlbGVjdGVkKHsgcGFyZW50SWQ6IHBhcmVudElkIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLk9yZ2FuaXphdGlvblNlbGVjdGVkTGlzdCA9IHJlcy5kYXRhOwogICAgICAgIGNvbnNvbGUubG9nKHRoaXMuT3JnYW5pemF0aW9uU2VsZWN0ZWRMaXN0KTsKICAgICAgfSk7CiAgICB9LAogICAgLy/kv53lrZjorr7lpIfkv6Hmga8KICAgIHN1Ym1pdCgpIHsKICAgICAgdGhpcy4kcmVmc1sic2J4eEZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB0aGlzLmFkZEFzc2V0KCk7CiAgICAgICAgICAvL+S/neWtmOaKgOacr+WPguaVsOS/oeaBrwogICAgICAgICAgLy8gdGhpcy5zdWJtaXRQYXJhbWV0ZXIoKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqCiAgICAgKiDmt7vliqDorr7lpIfkv53lrZjln7rmnKzkv6Hmga8KICAgICAqLwogICAgYWRkQXNzZXQoKSB7CiAgICAgIHRoaXMuc2J4eEZvcm0uc2JDbGFzc0NzVmFsdWUgPSB0aGlzLmpzY3NGb3JtOwogICAgICBhZGRBc3NldCh0aGlzLnNieHhGb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgaWYgKHJlcy5jb2RlID09ICIwMDAwIikgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgICAgbWVzc2FnZTogIuaTjeS9nOaIkOWKnyEiCiAgICAgICAgICB9KTsKICAgICAgICAgIHRoaXMuZGlhbG9nRm9ybVZpc2libGUgPSBmYWxzZTsKICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci5wYWdlUmVzaXplID0gIlkiOwogICAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvL+S/neWtmOaKgOacr+WPguaVsAogICAgc3VibWl0UGFyYW1ldGVyKCkgewogICAgICBzYXZlUGFyYW1WYWx1ZSh0aGlzLmpzY3NGb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvL+iuvuWkh+exu+Wei2NoYW5nZeS6i+S7tuOAguiOt+WPluaKgOacr+WPguaVsOS/oeaBrwogICAgc2hvd1BhcmFtcyhkYXRhKSB7CiAgICAgIHRoaXMucGFyYW1RdWVyeS5zYmx4Ym0gPSBkYXRhOwogICAgICB0aGlzLmdldFBhcmFtZXRlcnMoKTsKICAgIH0sCiAgICAvL+iOt+WPluaKgOacr+WPguaVsOWvueeFp+S/oeaBr++8jOWxleekuuWvueW6lOW+l+aKgOacr+WPguaVsGxhYmVs5L+h5oGvCiAgICBhc3luYyBnZXRQYXJhbWV0ZXJzKCkgewogICAgICB0aGlzLmpzY3NMYWJlbExpc3QgPSBbXTsKICAgICAgZ2V0UGFyYW1EYXRhTGlzdCh0aGlzLnBhcmFtUXVlcnkpLnRoZW4ocmVzID0+IHsKICAgICAgICBjb25zb2xlLmxvZygi5oqA5pyv5Y+C5pWw5a+554Wn5L+h5oGvIiwgcmVzKTsKICAgICAgICB0aGlzLmpzY3NMYWJlbExpc3QgPSByZXMuZGF0YTsKICAgICAgICB0aGlzLmdldFBhcmFtVmFsdWUoKTsKICAgICAgfSk7CiAgICB9LAogICAgLy/ojrflj5bmioDmnK/lj4LmlbDlgLzkv6Hmga8KICAgIGdldFBhcmFtVmFsdWUoKSB7CiAgICAgIGdldFBhcmFtc1ZhbHVlKHRoaXMuanNjc0Zvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICBpZiAocmVzLmRhdGEgIT0gIiIpIHsKICAgICAgICAgIHRoaXMuanNjc0Zvcm0gPSB7IC4uLnJlcy5kYXRhWzBdIH07CiAgICAgICAgICBjb25zb2xlLmxvZygi5oqA5pyv5Y+C5pWw5YC85L+h5oGvIiwgdGhpcy5qc2NzRm9ybSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvL+eCueWHu+aWsOWinu+8jOS/ruaUue+8jOivpuaDheaXtu+8jOmHjeaWsOiOt+WPluWvueW6lOeahOaKgOacr+WPguaVsOS/oeaBrwogICAgdGVjaG5pY2FsUGFyYW1ldGVycyhyb3cpIHsKICAgICAgdGhpcy5qc2NzRm9ybSA9IHt9OwogICAgICB0aGlzLnBhcmFtUXVlcnkuc2JseGJtID0gcm93LmFzc2V0VHlwZUNvZGU7CiAgICAgIHRoaXMuanNjc0Zvcm0uc2JseGJtID0gcm93LmFzc2V0VHlwZUNvZGU7CiAgICAgIHRoaXMuanNjc0Zvcm0uc2JibSA9IHJvdy5vYmpJZDsKICAgICAgdGhpcy5nZXRQYXJhbWV0ZXJzKCk7CiAgICB9LAoKICAgIC8v5qCR54K55Ye75LqL5Lu2CiAgICBoYW5kbGVOb2RlQ2xpY2soZGF0YSwgZSkgewogICAgICBpZiAoZGF0YS5pZGVudGlmaWVyID09ICIwIikgewogICAgICAgIC8v54K55Ye754i26IqC54K55pe25p+l6K+i5b2T5YmN5omA5pyJ6K6+5aSHCiAgICAgICAgLy/muIXnqbrmn6Xor6Llj4LmlbAKICAgICAgICB0aGlzLmJkenF1ZXJ5UGFyYW1zID0ge307CiAgICAgICAgLy/ojrflj5blj5jnlLXnq5nmlbDmja4KICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgfSBlbHNlIGlmIChkYXRhLmlkZW50aWZpZXIgPT0gIjEiKSB7CiAgICAgICAgLy/ngrnlh7vnrKzkuozlsYLnuqfnmoTlsZXnpLrorr7lpIcKICAgICAgICB0aGlzLmJkenF1ZXJ5UGFyYW1zLnNzamcgPSAiIjsKICAgICAgICB0aGlzLmJkenF1ZXJ5UGFyYW1zLnNzYmR6ID0gZGF0YS5pZDsKICAgICAgICAvL+iOt+WPluWPmOeUteermeaVsOaNrgogICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICB9IGVsc2UgaWYgKGRhdGEuaWRlbnRpZmllciA9PSAiMiIpIHsKICAgICAgICAvL+eCueWHu+esrOS6jOWxgue6p+eahOWxleekuuiuvuWkhwogICAgICAgIHRoaXMuYmR6cXVlcnlQYXJhbXMuc3NiZHogPSAiIjsKICAgICAgICB0aGlzLmJkenF1ZXJ5UGFyYW1zLnNzamcgPSBkYXRhLmlkOwogICAgICAgIC8v6I635Y+W5Y+Y55S156uZ5pWw5o2uCiAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgIH0KICAgIH0sCgogICAgLy/muIXnqbrooajljZUKICAgIHJlc2V0Rm9ybSgpIHsKICAgICAgdGhpcy5zYnh4Rm9ybSA9IHRoaXMuJG9wdGlvbnMuZGF0YSgpLmZvcm07CiAgICAgIHRoaXMuanNjc0Zvcm0gPSB0aGlzLiRvcHRpb25zLmRhdGEoKS5mb3JtOwogICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbigpIHsKICAgICAgICB0aGlzLiRyZWZzWyJzYnh4Rm9ybSJdLmNsZWFyVmFsaWRhdGUoKTsKICAgICAgfSk7CiAgICAgIHRoaXMuZGlhbG9nRm9ybVZpc2libGUgPSBmYWxzZTsKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["bdsbgl.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0r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file": "bdsbgl.vue", "sourceRoot": "src/views/dagangOilfield/dwzygl/bdgl", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"16\">\n      <el-col :span=\"4\">\n        <el-card shadow=\"never\" style=\"background:#e0f8ed;padding-top:10px;\">\n          <div>\n            <el-col>\n              <el-form label-width=\"80px\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"所属公司:\">\n                    <el-select\n                      v-model=\"treeForm.ssdwbm\"\n                      placeholder=\"请选择所属公司\"\n                      @change=\"getNewTreeInfo\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in OrganizationSelectedList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"String(item.value)\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"电压等级:\">\n                    <el-select\n                      v-model=\"treeForm.dydjbm\"\n                      placeholder=\"请选择电压等级\"\n                      @change=\"getNewTreeInfo\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in VoltageLevelSelectedList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"快速查询:\">\n                    <el-input\n                      placeholder=\"输入关键字进行过滤\"\n                      v-model=\"filterText\"\n                      clearable\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-form>\n            </el-col>\n          </div>\n          <div class=\"text head-container\">\n            <el-col style=\"padding:0\">\n              <el-tree\n                :expand-on-click-node=\"true\"\n                id=\"tree\"\n                :data=\"treeOptions\"\n                @node-click=\"handleNodeClick\"\n                ref=\"tree\"\n                :filter-node-method=\"filterNode\"\n                node-key=\"id\"\n                :default-expanded-keys=\"['0']\"\n                :default-checked-keys=\"['0']\"\n                :indent=\"18\"\n                accordion\n              />\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo1.data\"\n          :field-list=\"filterInfo1.fieldList\"\n          :width=\"{ labelWidth: 120, itemWidth: 180 }\"\n          @handleReset=\"filterReset\"\n        />\n        <el-white class=\"button-group\">\n          <div v-if=\"isShow3\" class=\"button_btn\">\n            <el-button\n              icon=\"el-icon-plus\"\n              @click=\"sbAddSensorButton\"\n              v-hasPermi=\"['zlsbtz:button:add']\"\n              type=\"primary\"\n              >新增</el-button\n            >\n            <el-button\n              icon=\"el-icon-delete\"\n              v-hasPermi=\"['zlsbtz:button:delete']\"\n              type=\"danger\"\n              @click=\"removeAsset\"\n              >删除</el-button\n            >\n            <!--<el-button icon=\"el-icon-s-check\" type=\"success\" @click=\"spZtbgjl\" :disabled=\"spButtonDisabled\">审批</el-button>-->\n          </div>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"64vh\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"updateAsset(scope.row)\"\n                  v-hasPermi=\"['zlsbtz:button:update']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                ></el-button>\n                <el-button\n                  @click=\"assetDetails(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!--设备展示结束-->\n    <!-- 一次设备弹出框开始展示设备履历 -->\n    <el-dialog\n      title=\"设备详情\"\n      :visible.sync=\"dialogFormVisible\"\n      width=\"60%\"\n      :before-close=\"resetForm\"\n      v-dialogDrag\n    >\n      <el-tabs v-model=\"activeTabName\">\n        <el-tab-pane label=\"基本信息\" name=\"sbDesc\">\n          <!--          <div class=\"block\" style=\"width: 50%;height: 50%;margin-bottom: 2%;float: right\">-->\n          <!--            <span class=\"demonstration\">设备图片</span>-->\n          <!--            <el-carousel trigger=\"click\" height=\"150px\" indicator-position=\"none\" :interval=\"2000\" type=\"card\">-->\n          <!--              <el-carousel-item v-for=\"(img,index) in imgList\" :key=\"index\">-->\n          <!--                <img :src=\"img.url\" width=\"100%\" height=\"100%\">-->\n          <!--              </el-carousel-item>-->\n          <!--            </el-carousel>-->\n          <!--          </div>-->\n          <el-form\n            :model=\"sbxxForm\"\n            label-width=\"130px\"\n            ref=\"sbxxForm\"\n            :rules=\"rules\"\n            :disabled=\"assetIsDisable\"\n          >\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-form-item label=\"所属公司\" prop=\"ssgs\">\n                  <el-select\n                    v-model=\"sbxxForm.ssgs\"\n                    placeholder=\"请选择所属公司\"\n                    filterable\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in OrganizationSelectedList\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"String(item.value)\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"所属电站\" prop=\"ssbdz\">\n                  <el-select\n                    v-model=\"sbxxForm.ssbdz\"\n                    placeholder=\"请选择所属电站\"\n                    filterable\n                    @change=\"bdzOptionsChangeClick\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in bdzOptionsDataList\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"所属间隔\" prop=\"ssjg\">\n                  <el-select\n                    v-model=\"sbxxForm.ssjg\"\n                    placeholder=\"请选择所属间隔\"\n                    filterable\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in jgOptionsDataList\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"设备代码\" prop=\"sbdm\">\n                  <el-input\n                    v-model=\"sbxxForm.sbdm\"\n                    placeholder=\"请填写设备代码\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"设备名称\" prop=\"sbmc\">\n                  <el-input\n                    v-model=\"sbxxForm.sbmc\"\n                    placeholder=\"请填写设备名称\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"设备类型\" prop=\"assetTypeCode\">\n                  <el-select\n                    v-model=\"sbxxForm.assetTypeCode\"\n                    placeholder=\"请选择设备类型\"\n                    @change=\"showParams\"\n                    filterable\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in sblxOptionsDataSelected\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"电压等级\" prop=\"dydjbm\">\n                  <el-select\n                    v-model=\"sbxxForm.dydjbm\"\n                    placeholder=\"请选择电压等级\"\n                  >\n                    <el-option\n                      v-for=\"item in dydjOptionsDataList\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"设备状态\" prop=\"sbzt\">\n                  <el-select\n                    v-model=\"sbxxForm.sbzt\"\n                    placeholder=\"请选择设备状态\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in sbztOptionsDataList\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"相别\">\n                  <el-input\n                    v-model=\"sbxxForm.xb\"\n                    placeholder=\"请填写相别\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"相数\">\n                  <el-input\n                    v-model=\"sbxxForm.xs\"\n                    placeholder=\"请填写相数\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"安装位置\">\n                  <el-input\n                    v-model=\"sbxxForm.azwz\"\n                    placeholder=\"请填写安装位置\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"用途\">\n                  <el-input\n                    v-model=\"sbxxForm.yt\"\n                    placeholder=\"请填写用途\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"型号\">\n                  <el-select v-model=\"sbxxForm.xh\" placeholder=\"请选择型号\">\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"产品代号\">\n                  <el-input\n                    v-model=\"sbxxForm.cpdh\"\n                    placeholder=\"请填写产品代号\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"额定电压\">\n                  <el-input\n                    v-model=\"sbxxForm.eddy\"\n                    placeholder=\"请填写额定电压\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"额定频率\">\n                  <el-input\n                    v-model=\"sbxxForm.edpl\"\n                    placeholder=\"请填写额定频率\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"使用环境\">\n                  <el-input\n                    v-model=\"sbxxForm.syhj\"\n                    placeholder=\"请填写使用环境\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"生产厂家\">\n                  <el-input\n                    v-model=\"sbxxForm.sccj\"\n                    placeholder=\"请填写生产厂家\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"制造国家\">\n                  <el-input\n                    v-model=\"sbxxForm.zzgj\"\n                    placeholder=\"请填写制造国家\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"组合设备类型\">\n                  <el-input\n                    v-model=\"sbxxForm.zhsblxbm\"\n                    placeholder=\"请填写组合设备类型\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"组合设备类型名称\">\n                  <el-input\n                    v-model=\"sbxxForm.zhsblx\"\n                    placeholder=\"请填写组合设备类型名称\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"额定电流\" prop=\"eddl\">\n                  <el-input\n                    v-model=\"sbxxForm.eddl\"\n                    placeholder=\"请填写额定电流\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"运行编号\">\n                  <el-input\n                    v-model=\"sbxxForm.yxbh\"\n                    placeholder=\"请填写运行编号\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"出厂编号\">\n                  <el-input\n                    v-model=\"sbxxForm.ccbh\"\n                    placeholder=\"请填写出厂编号\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"出厂日期\" class=\"add_sy_tyrq\">\n                  <el-date-picker\n                    v-model=\"sbxxForm.ccrq\"\n                    type=\"date\"\n                    placeholder=\"选择日期\"\n                    format=\"yyyy-MM-dd\"\n                    value-format=\"yyyy-MM-dd\"\n                  >\n                  </el-date-picker>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"投运日期\" class=\"add_sy_tyrq\">\n                  <el-date-picker\n                    v-model=\"sbxxForm.tyrq\"\n                    type=\"date\"\n                    placeholder=\"选择日期\"\n                    format=\"yyyy-MM-dd\"\n                    value-format=\"yyyy-MM-dd\"\n                  >\n                  </el-date-picker>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row :gutter=\"20\">\n              <el-col :span=\"24\">\n                <el-form-item label=\"备注\" prop=\"bz\">\n                  <el-input\n                    v-model=\"sbxxForm.bz\"\n                    type=\"textarea\"\n                    rows=\"2\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </el-form>\n        </el-tab-pane>\n        <el-tab-pane label=\"技术参数\" name=\"sbjscs\">\n          <el-form :model=\"jscsForm\" label-width=\"130px\">\n            <el-col :span=\"8\" v-for=\"item in jscsLabelList\">\n              <el-form-item\n                :label=\"\n                  item.dw != '' ? item.label + '(' + item.dw + ')' : item.label\n                \"\n              >\n                <el-input\n                  v-if=\"item.type === 'input'\"\n                  v-model=\"jscsForm[item.jscsbm]\"\n                  placeholder=\"\"\n                >\n                </el-input>\n                <el-select\n                  v-if=\"item.type === 'select'\"\n                  v-model=\"jscsForm[item.jscsbm]\"\n                  placeholder=\"\"\n                >\n                  <el-option\n                    v-for=\"(childItem, key) in item.options\"\n                    :key=\"key\"\n                    :label=\"childItem.label\"\n                    :value=\"childItem.value\"\n                    :disabled=\"childItem.disabled\"\n                    style=\"display: flex; align-items: center;\"\n                    clearable\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-form>\n        </el-tab-pane>\n        <el-tab-pane label=\"设备履历\" name=\"sbRecord\">\n          <el-tabs\n            v-model=\"sbllDescTabName\"\n            @tab-click=\"handleSbllDescTabNameClick\"\n            type=\"card\"\n          >\n            <el-tab-pane label=\"试验记录\" name=\"syjl\">\n              <el-table\n                stripe\n                border\n                v-loading=\"loading\"\n                :data=\"sblvsyjlList\"\n                max-height=\"550\"\n              >\n                <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n                <el-table-column label=\"试验专业\" align=\"center\" prop=\"syzy\" />\n                <el-table-column\n                  label=\"试验性质\"\n                  align=\"center\"\n                  prop=\"syxz\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"试验名称\"\n                  align=\"center\"\n                  prop=\"symc\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"工作地点\"\n                  align=\"center\"\n                  prop=\"gzdd\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"试验设备\"\n                  align=\"center\"\n                  prop=\"sysb\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column label=\"试验报告\" align=\"center\" prop=\"sybg\" />\n                <el-table-column\n                  label=\"天气\"\n                  align=\"center\"\n                  prop=\"tq\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"试验日期\"\n                  align=\"center\"\n                  prop=\"syrq\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"录入人\"\n                  align=\"center\"\n                  prop=\"lrr\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"试验结论\"\n                  align=\"center\"\n                  prop=\"syjl\"\n                  :show-overflow-tooltip=\"true\"\n                />\n              </el-table>\n            </el-tab-pane>\n            <el-tab-pane label=\"缺陷记录\" name=\"qxjl\">\n              <el-table\n                stripe\n                border\n                v-loading=\"loading\"\n                :data=\"sbllqxjlList\"\n                max-height=\"550\"\n              >\n                <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n                <el-table-column label=\"所属公司\" align=\"center\" prop=\"ssgs\" />\n                <el-table-column\n                  label=\"变电站名称\"\n                  align=\"center\"\n                  prop=\"bdzmc\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"设备类型\"\n                  align=\"center\"\n                  prop=\"sblx\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"缺陷性质\"\n                  align=\"center\"\n                  prop=\"qxxz\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"电压等级\"\n                  align=\"center\"\n                  prop=\"dydj\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column label=\"设备型号\" align=\"center\" prop=\"sbxh\" />\n                <el-table-column\n                  label=\"生产厂家\"\n                  align=\"center\"\n                  prop=\"sccj\"\n                  :show-overflow-tooltip=\"true\"\n                />\n              </el-table>\n            </el-tab-pane>\n            <el-tab-pane label=\"状态变更记录\" name=\"ztbgjl\">\n              <comp-table\n                :table-and-page-info=\"resumPageInfo\"\n                @getMethod=\"getResumList\"\n                @update:multipleSelection=\"handleSelectionChange\"\n                height=\"30vh\"\n              />\n            </el-tab-pane>\n          </el-tabs>\n        </el-tab-pane>\n        <el-tab-pane label=\"附属设施\" name=\"fsss\">\n          <el-table\n            stripe\n            border\n            v-loading=\"loading\"\n            :data=\"fsssList\"\n            max-height=\"550\"\n          >\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n            <el-table-column label=\"设备名称\" align=\"center\" prop=\"syzy\" />\n            <el-table-column\n              label=\"设备型号\"\n              align=\"center\"\n              prop=\"syxz\"\n              :show-overflow-tooltip=\"true\"\n            />\n            <el-table-column\n              label=\"生产厂家\"\n              align=\"center\"\n              prop=\"symc\"\n              :show-overflow-tooltip=\"true\"\n            />\n            <el-table-column\n              label=\"出厂日期\"\n              align=\"center\"\n              prop=\"gzdd\"\n              :show-overflow-tooltip=\"true\"\n            />\n            <el-table-column\n              label=\"投运日期\"\n              align=\"center\"\n              prop=\"sysb\"\n              :show-overflow-tooltip=\"true\"\n            />\n            <el-table-column label=\"更换日期\" align=\"center\" prop=\"sybg\" />\n          </el-table>\n        </el-tab-pane>\n      </el-tabs>\n      <div slot=\"footer\" class=\"dialog-footer\" v-show=\"!assetIsDisable\">\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submit\" class=\"pmyBtn\"\n          >确 定</el-button\n        >\n      </div>\n    </el-dialog>\n    <el-dialog\n      title=\"设备状态变更\"\n      :visible.sync=\"dialogVisible\"\n      width=\"30%\"\n      append-to-body\n      v-dialogDrag\n    >\n      <el-form :inline=\"true\" label-width=\"100px\" class=\"qxlr_dialog_insert\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"设备状态:\">\n              <el-select v-model=\"updateList.sbzt\">\n                <el-option\n                  v-for=\"item in sbztOptionsDataList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitStatus\">确 定</el-button>\n      </span>\n    </el-dialog>\n\n    <!--状态变更使用-->\n    <activiti\n      :processData=\"processData\"\n      :isShow=\"isShow\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n    ></activiti>\n    <time-line\n      :value=\"timeLineShow\"\n      :timeData=\"timeData\"\n      @closeTimeLine=\"colseTimeLine\"\n    />\n  </div>\n</template>\n\n<script>\nimport {\n  addAsset,\n  addBdz,\n  addJg,\n  getAssetListInfo,\n  getJgInfoList,\n  getTreeInfo,\n  removeAsset,\n  removeBdz,\n  removeJg,\n  getOrganizationSelected,\n  getNewTreeInfo,\n  getBdAsesetListPage,\n  getBdzDataListSelected,\n  getJgDataListSelected,\n  getSblxDataListSelected,\n  updateStatus\n} from \"@/api/dagangOilfield/asset/bdsbtz\";\nimport { getResumDataList } from \"@/api/dagangOilfield/asset/sdsb\";\nimport TechnicalParameter from \"@/views/dagangOilfield/bzgl/sbbzk/technicalParameter\";\nimport {\n  getParamDataList,\n  getParamsValue,\n  saveParamValue\n} from \"@/api/dagangOilfield/asset/parameters\";\nimport { saveOrUpdate } from \"@/api/yxgl/bdyxgl/qxgl\";\n//流程\nimport activiti from \"com/activiti\";\nimport timeLine from \"com/timeLine\";\nimport { HistoryList } from \"@/api/activiti/processTask\";\n\nexport default {\n  name: \"qxbzk\",\n  components: { TechnicalParameter, activiti, timeLine },\n  data() {\n    return {\n      //流程参数\n      processData: {\n        processDefinitionKey: \"dwzyztbg\",\n        businessKey: \"\",\n        businessType: \"变电设备台账\",\n        variables: {},\n        nextUser: \"\",\n        defaultFrom: true,\n        processType: \"complete\"\n      },\n      //流程变更申请标题\n      activitiOption: { title: \"状态变更申请\" },\n      //流程跟踪数据\n      timeData: [],\n      //流程跟踪显示隐藏\n      timeLineShow: false,\n      //流程组件显示隐藏\n      isShow: false,\n\n      resumeQuery: {\n        foreignNum: undefined,\n        sblx: undefined\n      },\n      updateList: {\n        sbzt: \"\",\n        objId: \"\"\n      },\n      dialogVisible: false,\n      //设备删除选择列\n      selectRows: [],\n      //设备类型下拉框数据\n      sblxOptionsDataSelected: [],\n      //查询变电站下拉框数据的参数\n      selectBdzOptionsParam: {},\n      //查询间隔下拉框数据的参数\n      selectJgOptionsParam: {},\n      //设备表单\n      sbxxForm: {},\n      //设备附属设施list\n      fsssList: [],\n      //变电设备查询参数\n      bdzqueryParams: {\n        ssgs: undefined,\n        dydjbm: undefined,\n        ssbdz: undefined,\n        ssjg: undefined,\n        sbmc: undefined,\n        assetTypeCode: undefined,\n        sbzt: undefined,\n        pageNum: 1,\n        pageSize: 10\n      },\n      //新增设备时设备状态下拉框数据\n      sbztOptionsDataList: [\n        { label: \"在运\", value: \"在运\" },\n        { label: \"停运\", value: \"停运\" },\n        { label: \"报废\", value: \"报废\" }\n      ],\n      //新增设备时所属间隔下拉框列表\n      jgOptionsDataList: [],\n      //新增设备时电压等级下拉框数据\n      dydjOptionsDataList: [\n        { label: \"110kV\", value: \"110\" },\n        { label: \"35kV\", value: \"35\" },\n        { label: \"10kV\", value: \"10\" },\n        { label: \"6kV\", value: \"6\" }\n      ],\n      //新增设备时变电站下拉框\n      bdzOptionsDataList: [],\n      //变电设备列表数据\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"deptname\", label: \"所属公司\", minWidth: \"120\" },\n          { prop: \"bdzmc\", label: \"变电站名称\", minWidth: \"180\" },\n          { prop: \"wzmc\", label: \"所属间隔\", minWidth: \"180\" },\n          { prop: \"sblxmc\", label: \"设备类型\", minWidth: \"180\" },\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"180\" },\n          { prop: \"dydjName\", label: \"电压等级\", minWidth: \"120\" },\n          { prop: \"sbzt\", label: \"设备状态\", minWidth: \"250\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"120\" },\n          { prop: \"ggxh\", label: \"规格型号\", minWidth: \"120\" },\n          { prop: \"eddy\", label: \"额定电压\", minWidth: \"120\" },\n          { prop: \"eddl\", label: \"额定电流\", minWidth: \"120\" },\n          { prop: \"edpl\", label: \"额定频率\", minWidth: \"120\" },\n          { prop: \"sccj\", label: \"生产厂家\", minWidth: \"120\" }\n          /* {\n              fixed: \"right\",\n              prop: 'operation',\n              label: '操作',\n              minWidth: '120px',\n              style: {display: 'block'},\n              operation: [\n                /!*{name: \"状态变更\", clickFun: this.updateStatus},\n                {name: \"流程查看\", clickFun: this.ztbglcSay},*!/\n                {name: '修改', clickFun: this.updateAsset},\n                {name: '详情', clickFun: this.assetDetails},\n              ]\n            },*/\n        ]\n      },\n      resumPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"foreignNum\", label: \"设备名称\", minWidth: \"120\" },\n          { prop: \"sblx\", label: \"设备类型\", minWidth: \"180\" },\n          { prop: \"bglx\", label: \"变更类型\", minWidth: \"120\" },\n          { prop: \"ms\", label: \"描述\", minWidth: \"250\" },\n          { prop: \"bgr\", label: \"变更人\", minWidth: \"140\" },\n          { prop: \"bgsj\", label: \"变更时间\", minWidth: \"140\" }\n        ]\n      },\n      //变电设备筛选条件\n      filterInfo1: {\n        data: {\n          sbmc: \"\",\n          assetTypeCode: \"\",\n          sbzt: \"\"\n        },\n        fieldList: [\n          { label: \"设备名称\", type: \"input\", value: \"sbmc\" },\n          {\n            label: \"设备类型\",\n            type: \"select\",\n            value: \"assetTypeCode\",\n            options: [\n              { label: \"干式站用变\", value: \"02\" },\n              { label: \"sf6断路器\", value: \"03\" },\n              { label: \"真空断路器\", value: \"04\" }\n            ]\n          },\n          {\n            label: \"投运状态\",\n            type: \"select\",\n            value: \"sbzt\",\n            options: [\n              { label: \"在运\", value: \"在运\" },\n              { label: \"停运\", value: \"停运\" }\n            ]\n          }\n        ]\n      },\n      //树结构监听属性\n      filterText: \"\",\n      //组织结构下拉数据\n      OrganizationSelectedList: [],\n      //树结构上面得筛选框参数\n      treeForm: {},\n      //电压等级下拉框数据\n      VoltageLevelSelectedList: [\n        { label: \"110kV\", value: \"110\" },\n        { label: \"35kV\", value: \"35\" },\n        { label: \"10kV\", value: \"10\" },\n        { label: \"6kV\", value: \"6\" }\n      ],\n      //设备信息展示\n      assetIsDisable: false,\n      //技术参数动态展示集合\n      jscsLabelList: [],\n      //技术参数绑定\n      jscsForm: {},\n      //设备上面展示的按钮(新增、删除)\n      isShow3: true,\n      //设备履历状态变更记录\n      sbllztbgjlList: [\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          bglx: \"状态变更\",\n          desc: \"状态变更为报废\",\n          bgsj: \"2022-12-12\",\n          bgr: \"张三\"\n        },\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          bglx: \"状态变更\",\n          desc: \"状态变更为报废\",\n          bgsj: \"2022-12-12\",\n          bgr: \"张三\"\n        },\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          bglx: \"状态变更\",\n          desc: \"状态变更为报废\",\n          bgsj: \"2022-12-12\",\n          bgr: \"张三\"\n        },\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          bglx: \"状态变更\",\n          desc: \"状态变更为报废\",\n          bgsj: \"2022-12-12\",\n          bgr: \"张三\"\n        },\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          bglx: \"状态变更\",\n          desc: \"状态变更为报废\",\n          bgsj: \"2022-12-12\",\n          bgr: \"张三\"\n        }\n      ],\n      //设备履历缺陷记录数据集合\n      sbllqxjlList: [\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          qxxz: \"严重\",\n          dydj: \"35kV\",\n          sbxh: \"XXX型号\",\n          sccj: \"XXX厂家\"\n        },\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          qxxz: \"严重\",\n          dydj: \"35kV\",\n          sbxh: \"XXX型号\",\n          sccj: \"XXX厂家\"\n        },\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          qxxz: \"严重\",\n          dydj: \"35kV\",\n          sbxh: \"XXX型号\",\n          sccj: \"XXX厂家\"\n        },\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          qxxz: \"严重\",\n          dydj: \"35kV\",\n          sbxh: \"XXX型号\",\n          sccj: \"XXX厂家\"\n        },\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          qxxz: \"严重\",\n          dydj: \"35kV\",\n          sbxh: \"XXX型号\",\n          sccj: \"XXX厂家\"\n        }\n      ],\n      //设备履历试验记录数据\n      sblvsyjlList: [\n        {\n          syzy: \"带电\",\n          syxz: \"例行试验\",\n          symc: \"XXXXX\",\n          gzdd: \"XXX平台\",\n          sysb: \"主变压器\",\n          sybg: \"\",\n          tq: \"晴\",\n          syrq: \"2022-01-01\",\n          lrr: \"张三\",\n          syjl: \"XXXXX\"\n        },\n        {\n          syzy: \"带电\",\n          syxz: \"例行试验\",\n          symc: \"XXXXX\",\n          gzdd: \"XXX平台\",\n          sysb: \"主变压器\",\n          sybg: \"\",\n          tq: \"晴\",\n          syrq: \"2022-01-01\",\n          lrr: \"张三\",\n          syjl: \"XXXXX\"\n        },\n        {\n          syzy: \"带电\",\n          syxz: \"例行试验\",\n          symc: \"XXXXX\",\n          gzdd: \"XXX平台\",\n          sysb: \"主变压器\",\n          sybg: \"\",\n          tq: \"晴\",\n          syrq: \"2022-01-01\",\n          lrr: \"张三\",\n          syjl: \"XXXXX\"\n        },\n        {\n          syzy: \"带电\",\n          syxz: \"例行试验\",\n          symc: \"XXXXX\",\n          gzdd: \"XXX平台\",\n          sysb: \"主变压器\",\n          sybg: \"\",\n          tq: \"晴\",\n          syrq: \"2022-01-01\",\n          lrr: \"张三\",\n          syjl: \"XXXXX\"\n        },\n        {\n          syzy: \"带电\",\n          syxz: \"例行试验\",\n          symc: \"XXXXX\",\n          gzdd: \"XXX平台\",\n          sysb: \"主变压器\",\n          sybg: \"\",\n          tq: \"晴\",\n          syrq: \"2022-01-01\",\n          lrr: \"张三\",\n          syjl: \"XXXXX\"\n        },\n        {\n          syzy: \"带电\",\n          syxz: \"例行试验\",\n          symc: \"XXXXX\",\n          gzdd: \"XXX平台\",\n          sysb: \"主变压器\",\n          sybg: \"\",\n          tq: \"晴\",\n          syrq: \"2022-01-01\",\n          lrr: \"张三\",\n          syjl: \"XXXXX\"\n        }\n      ],\n      //设备履历tab页\n      sbllDescTabName: \"syjl\",\n      //轮播图片\n      imgList: [\n        {\n          url:\n            \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\"\n        },\n        {\n          url:\n            \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\"\n        },\n        {\n          url:\n            \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\"\n        },\n        {\n          url:\n            \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\"\n        }\n      ],\n      //组织树\n      treeOptions: [],\n      //设备详情页底部确认取消按钮控制\n      sbCommitDialogCotrol: true,\n      //弹出框tab页\n      activeTabName: \"sbDesc\",\n      //变电站展示\n      bdzShowTable: true,\n      //间隔展示\n      jgShowTable: false,\n      //设备展示\n      sbShowTable: false,\n      //设备弹出框\n      dialogFormVisible: false,\n      //变电站添加按钮弹出框\n      bdzDialogFormVisible: false,\n      //间隔添加按钮弹出框\n      jgDialogFormVisible: false,\n      //弹出框表单\n      form: {},\n      loading: false,\n      paramQuery: {\n        sblxbm: undefined\n      },\n      rules: {\n        ssbdz: [\n          { required: true, message: \"请选择所属变电站\", trigger: \"change\" }\n        ],\n        sbdm: [{ required: true, message: \"请填写设备代码\", trigger: \"blur\" }],\n        sbmc: [{ required: true, message: \"请填写设备名称\", trigger: \"blur\" }],\n        dydjbm: [\n          { required: true, message: \"请选择电压等级\", trigger: \"change\" }\n        ],\n        sbzt: [\n          { required: true, message: \"请选择设备状态\", trigger: \"change\" }\n        ],\n        assetTypeCode: [\n          { required: true, message: \"请选择设备类型\", trigger: \"change\" }\n        ],\n        ssjg: [\n          { required: true, message: \"请选择所属间隔\", trigger: \"change\" }\n        ],\n        ssgs: [{ required: true, message: \"请选择所属公司\", trigger: \"change\" }]\n      },\n\n      //审批按钮\n      spButtonDisabled: true\n    };\n  },\n  watch: {\n    //监听筛选框值发生变化进而筛选树结构\n    filterText(val) {\n      this.$refs.tree.filter(val);\n    }\n  },\n  created() {\n    //获取组织结构下拉数据\n    this.getOrganizationSelected();\n    //获取新的设备拓扑树\n    this.getNewTreeInfo();\n    //初始化加载时加载所有变电站下面的设备信息\n    this.getData();\n    //获取设备类型下拉框数据\n    this.getSblxDataListSelected();\n  },\n  mounted() {\n    //获取变电站下拉框数据\n    this.getBdzDataListSelected();\n  },\n  methods: {\n    //审批\n    spZtbgjl() {\n      let row = this.selectRows[0];\n      this.activitiOption.title = \"流程提交\";\n      this.processData.defaultFrom = false;\n      this.processData.processType = \"complete\";\n      this.processData.businessKey = row.objId;\n      this.processData.variables.pass = true;\n      this.isShow = true;\n    },\n    //流程查看\n    async ztbglcSay(row) {\n      this.processData.businessKey = row.objId;\n      let { code, data } = await HistoryList(this.processData);\n      this.timeData = data;\n      this.timeLineShow = true;\n    },\n    //流程回调\n    async todoResult(data) {\n      console.log(\"流程回调方法：\", data);\n      switch (data.activeTaskName) {\n        case \"结束\":\n          updateStatus(data.variables.updateList).then(res => {\n            if (res.code == \"0000\") {\n              this.$message.success(\"设备状态已变更！\");\n              this.dialogVisible = false;\n              this.getData();\n            }\n          });\n          break;\n      }\n    },\n    //上报发送办结\n    getSbFsBj(args) {\n      console.log(args);\n      let row = { ...args.data };\n      if (args.type === \"complete\") {\n        this.activitiOption.title = \"人员选择\";\n        this.processData.defaultFrom = true;\n        this.processData.processType = \"complete\";\n        this.processData.businessKey = row.objId;\n        this.processData.variables.pass = true;\n        this.processData.variables.updateList = row;\n      } else {\n        this.activitiOption.title = \"回退原因提报\";\n        this.processData.defaultFrom = true;\n        this.processData.processType = \"rollback\";\n        this.processData.businessKey = row.objId;\n        this.processData.variables.pass = true;\n      }\n      this.isShow = true;\n    },\n    //关闭流程组件\n    closeActiviti() {\n      this.isShow = false;\n    },\n    //关闭流程查看页面\n    colseTimeLine() {\n      this.timeLineShow = false;\n    },\n    /**\n     * 设备表格多选框\n     */\n    handleSelectionChange(rows) {\n      this.selectRows = rows;\n\n      //审批按钮\n      this.spButtonDisabled = rows.length != 1;\n    },\n    /**\n     * 获取设备类型下拉框数据\n     */\n    getSblxDataListSelected() {\n      let sblxParam = {\n        type: \"变电设备\"\n      };\n      getSblxDataListSelected(sblxParam).then(res => {\n        this.sblxOptionsDataSelected = res.data;\n      });\n    },\n\n    /**\n     * 删除设备信息\n     */\n    removeAsset() {\n      if (this.selectRows.length < 1) {\n        this.$message.warning(\"请选择正确的数据！！！\");\n        return;\n      }\n      let ids = this.selectRows.map(item => {\n        return item.objId;\n      });\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          removeAsset(ids).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n      this.tableAndPageInfo.pager.pageResize = \"Y\";\n      this.getData();\n    },\n    //设备添加按钮\n    sbAddSensorButton() {\n      //清空表单\n      // this.sbxxForm = {};\n      // this.jscsForm={};\n      //\n      // let row={};\n      // this.technicalParameters(row);\n      //打开弹出框\n      this.dialogFormVisible = true;\n      //按钮和表单是否可编辑控制\n      this.assetIsDisable = false;\n    },\n    //变电站下拉框中的change事件\n    bdzOptionsChangeClick() {\n      //当发生change事件时先清空之前的间隔信息\n      this.$set(this.sbxxForm, \"ssjg\", \"\");\n      //调用查询间隔方法\n      this.getJgDataListSelected();\n    },\n    //获取变电站下拉框数据\n    getBdzDataListSelected() {\n      getBdzDataListSelected(this.selectBdzOptionsParam).then(res => {\n        this.bdzOptionsDataList = res.data;\n      });\n      //调用查询间隔方法\n      // this.getJgDataListSelected();\n    },\n    //获取间隔下拉框数据\n    getJgDataListSelected() {\n      //给获取间隔下拉框查询参数赋值\n      this.selectJgOptionsParam.ssbdz = this.sbxxForm.ssbdz;\n      getJgDataListSelected(this.selectJgOptionsParam).then(res => {\n        this.jgOptionsDataList = res.data;\n      });\n    },\n    updateStatus(row) {\n      this.updateList.sbzt = row.sbzt;\n      (this.updateList.objId = row.objId), (this.dialogVisible = true);\n    },\n    submitStatus() {\n      this.$confirm(\"确认将设备状态修改为\" + this.updateList.sbzt + \"?\", \"\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(res => {\n        //关闭状态变更弹窗\n        this.dialogVisible = false;\n        //打开人员选择弹窗\n        this.isShow = true;\n        this.getSbFsBj({ data: this.updateList, type: \"complete\" });\n        // updateStatus(this.updateList).then(res=>{\n        //   if(res.code==\"0000\"){\n        //     this.$message.success(\"设备状态已变更！\")\n        //     this.dialogVisible=false;\n        //     this.getData();\n        //   }\n        // })\n      });\n    },\n\n    /**\n     * 设备履历\n     */\n    getResumList(par) {\n      let param = { ...par, ...this.resumeQuery };\n      getResumDataList(param).then(res => {\n        this.resumPageInfo.tableData = res.data.records;\n        this.resumPageInfo.pager.total = res.data.total;\n      });\n    },\n\n    //设备修改操作\n    updateAsset(row) {\n      this.technicalParameters(row);\n      this.paramQuery.sblxbm = row.assetTypeCode;\n      this.getParameters();\n      this.resumeQuery.foreignNum = row.sbmc;\n      this.resumeQuery.sblx = row.sblx;\n      this.getResumList();\n      //打开设备弹出框\n      this.dialogFormVisible = true;\n      //给表单赋值\n      this.sbxxForm = row;\n      //按钮和表单是否可编辑控制\n      this.assetIsDisable = false;\n    },\n    //设备详情操作\n    assetDetails(row) {\n      this.technicalParameters(row);\n\n      this.resumeQuery.foreignNum = row.sbmc;\n      this.resumeQuery.sblx = row.sblx;\n      this.getResumList();\n      //打开设备弹出框\n      this.dialogFormVisible = true;\n      //给表单赋值\n      this.sbxxForm = row;\n      //按钮和表单是否可编辑控制\n      this.assetIsDisable = true;\n    },\n    //设备履历tab页点击事件\n    handleSbllDescTabNameClick(tab, event) {\n      console.log(tab, event);\n    },\n    //筛选条件重置\n    filterReset() {},\n    //变电设备台账查询\n    async getData(params) {\n      try {\n        const param = { ...this.bdzqueryParams, ...params };\n        const { data, code } = await getBdAsesetListPage(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          console.log(\"data:设备数据\", data.records);\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //树监听事件\n    filterNode(value, data) {\n      if (!value) return true;\n      return data.label.indexOf(value) !== -1;\n    },\n    //获取新的设备拓扑树\n    getNewTreeInfo() {\n      //给查询设备参数赋值\n      this.bdzqueryParams.ssgs = this.treeForm.ssdwbm;\n      this.bdzqueryParams.dydjbm = this.treeForm.dydjbm;\n      getNewTreeInfo(this.treeForm).then(res => {\n        console.log(\"s树结构数据:\", res);\n        this.treeOptions = res.data;\n      });\n      //获取设备方法\n      this.getData();\n    },\n    //获取组织结构下拉框数据\n    getOrganizationSelected() {\n      let parentId = \"1001\";\n      getOrganizationSelected({ parentId: parentId }).then(res => {\n        this.OrganizationSelectedList = res.data;\n        console.log(this.OrganizationSelectedList);\n      });\n    },\n    //保存设备信息\n    submit() {\n      this.$refs[\"sbxxForm\"].validate(valid => {\n        if (valid) {\n          this.addAsset();\n          //保存技术参数信息\n          // this.submitParameter();\n        } else {\n          return false;\n        }\n      });\n    },\n    /**\n     * 添加设备保存基本信息\n     */\n    addAsset() {\n      this.sbxxForm.sbClassCsValue = this.jscsForm;\n      addAsset(this.sbxxForm).then(res => {\n        if (res.code == \"0000\") {\n          this.$message({\n            type: \"success\",\n            message: \"操作成功!\"\n          });\n          this.dialogFormVisible = false;\n          this.tableAndPageInfo.pager.pageResize = \"Y\";\n          this.getData();\n        }\n      });\n    },\n    //保存技术参数\n    submitParameter() {\n      saveParamValue(this.jscsForm).then(res => {\n        this.dialogFormVisible = false;\n      });\n    },\n    //设备类型change事件。获取技术参数信息\n    showParams(data) {\n      this.paramQuery.sblxbm = data;\n      this.getParameters();\n    },\n    //获取技术参数对照信息，展示对应得技术参数label信息\n    async getParameters() {\n      this.jscsLabelList = [];\n      getParamDataList(this.paramQuery).then(res => {\n        console.log(\"技术参数对照信息\", res);\n        this.jscsLabelList = res.data;\n        this.getParamValue();\n      });\n    },\n    //获取技术参数值信息\n    getParamValue() {\n      getParamsValue(this.jscsForm).then(res => {\n        if (res.data != \"\") {\n          this.jscsForm = { ...res.data[0] };\n          console.log(\"技术参数值信息\", this.jscsForm);\n        }\n      });\n    },\n    //点击新增，修改，详情时，重新获取对应的技术参数信息\n    technicalParameters(row) {\n      this.jscsForm = {};\n      this.paramQuery.sblxbm = row.assetTypeCode;\n      this.jscsForm.sblxbm = row.assetTypeCode;\n      this.jscsForm.sbbm = row.objId;\n      this.getParameters();\n    },\n\n    //树点击事件\n    handleNodeClick(data, e) {\n      if (data.identifier == \"0\") {\n        //点击父节点时查询当前所有设备\n        //清空查询参数\n        this.bdzqueryParams = {};\n        //获取变电站数据\n        this.getData();\n      } else if (data.identifier == \"1\") {\n        //点击第二层级的展示设备\n        this.bdzqueryParams.ssjg = \"\";\n        this.bdzqueryParams.ssbdz = data.id;\n        //获取变电站数据\n        this.getData();\n      } else if (data.identifier == \"2\") {\n        //点击第二层级的展示设备\n        this.bdzqueryParams.ssbdz = \"\";\n        this.bdzqueryParams.ssjg = data.id;\n        //获取变电站数据\n        this.getData();\n      }\n    },\n\n    //清空表单\n    resetForm() {\n      this.sbxxForm = this.$options.data().form;\n      this.jscsForm = this.$options.data().form;\n      this.$nextTick(function() {\n        this.$refs[\"sbxxForm\"].clearValidate();\n      });\n      this.dialogFormVisible = false;\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  height: 76vh;\n  overflow: auto;\n}\n\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.box-cardList {\n  height: 70%;\n}\n\n.item {\n  width: 200px;\n  float: left;\n}\n\n#main_container_dj {\n  height: calc(100vh - 84px);\n}\n\n.aside_height {\n  height: 96%;\n}\n\n.defect .el-form-item:nth-child(odd) {\n  margin-right: 70px;\n}\n\n/*背景颜色调整*/\n#main_container_dj,\n#main_container_dj .el-aside {\n  background-color: #b4caf1;\n}\n\n///deep/ .qxlr_dialog_insert .el-dialog__header {\n//  background-color: #8eb3f5;\n//}\n\n/* /deep/ .pmyBtn {\n    background: #8eb3f5;\n  }*/\n\n/*/deep/ .add_sy_tyrq .el-input--medium .el-input__inner {*/\n/*  width: 200px;*/\n/*}*/\n\n/*添加弹出框得宽度*/\n/*/deep/ .el-input--medium .el-input__inner {*/\n/*  width: 200px;*/\n/*}*/\n.el-select {\n  width: 100%;\n}\n\n.el-date-editor {\n  width: 100%;\n}\n\n.el-carousel__item h3 {\n  color: #475669;\n  font-size: 14px;\n  opacity: 0.75;\n  line-height: 150px;\n}\n\n.el-carousel__item:nth-child(2n) {\n  background-color: #99a9bf;\n}\n\n.el-carousel__item:nth-child(2n + 1) {\n  background-color: #d3dce6;\n}\n\n/* 设置滚动条的样式 */\n::-webkit-scrollbar {\n  width: 12px;\n}\n\n/* 滚动槽 */\n::-webkit-scrollbar-track {\n  //-webkit-box-shadow:inset006pxrgba(0,0,0,0.3);\n  border-radius: 10px;\n}\n\n/* 滚动条滑块 */\n::-webkit-scrollbar-thumb {\n  border-radius: 10px;\n  background: rgba(0, 0, 0, 0.1);\n  //-webkit-box-shadow:gba(0,0,0,0.5);\n}\n\n::-webkit-scrollbar-thumb:window-inactive {\n  background: rgba(0, 0, 0, 0.1);\n}\n\n[data-v-67a974b1]::-webkit-scrollbar {\n  width: 8px;\n}\n\n.item {\n  width: 225px;\n  float: left;\n}\n/deep/.box-card {\n  margin: 0 6px;\n}\n//有子节点 且未展开\n.el-tree ::v-deep .el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/add.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n//有子节点 且已展开\n.el-tree\n  ::v-deep\n  .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/prep.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n//没有子节点\n.el-tree ::v-deep .el-tree-node__expand-icon.is-leaf::before {\n  background: transparent;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n</style>\n"]}]}