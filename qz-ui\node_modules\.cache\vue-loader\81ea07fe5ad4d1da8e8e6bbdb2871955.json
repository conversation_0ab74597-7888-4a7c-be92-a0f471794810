{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\bdgl\\fsss.vue?vue&type=style&index=0&id=2f653f2a&scoped=true&lang=scss&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\bdgl\\fsss.vue", "mtime": 1706897324521}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmNhcmQxIHsKICBtYXJnaW4tYm90dG9tOiA2cHg7Cn0KCi5zZWFyY2gtY29uZGl0aW9uIHsKICBmb250LXNpemU6IDEzcHg7CiAgY29sb3I6ICM5YzljOWM7CgogIC5lbC1zZWxlY3QgewogICAgLmVsLWlucHV0IHsKICAgICAgd2lkdGg6IDEwMCU7CiAgICB9CiAgfQoKICAuZWwtY29sIHsKICAgIHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7CiAgICBsaW5lLWhlaWdodDogMzJweDsKICAgIHRleHQtYWxpZ246IGxlZnQ7CiAgfQp9Cg=="}, {"version": 3, "sources": ["fsss.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0UA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "fsss.vue", "sourceRoot": "src/views/dagangOilfield/dwzygl/bdgl", "sourcesContent": ["<template>\n  <el-row class=\"app-container\">\n    <el-row>\n      <!--基本信息查询及显示-->\n      <el-col :span=\"24\">\n        <!--搜索条件-->\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          @handleReset=\"filterReset\"\n          :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n        />\n      </el-col>\n    </el-row>\n\n    <el-row>\n      <el-col>\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button type=\"primary\" icon=\"el-icon-plus\" v-hasPermi=\"['jgdy:button:add']\" @click=\"addRow\">新增</el-button>\n          </div>\n          <comp-table :table-and-page-info=\"tableAndPageInfo\" height=\"42vh\">\n            <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\" width=\"200\" :resizable=\"false\">\n              <template slot-scope=\"scope\">\n                <el-button  type=\"text\" size=\"small\" @click=\"updateRow(scope.row)\" icon=\"el-icon-edit\" title=\"编辑\" v-if=\"scope.row.createBy === $store.getters.name && canEdit\"/>\n                <el-button  type=\"text\" size=\"small\" @click=\"getDetails(scope.row)\" icon=\"el-icon-view\" title=\"详情\"/>\n                <el-button  type=\"text\" size=\"small\" icon=\"el-icon-delete\" title=\"删除\" @click=\"handleDeleteById(scope.row.objId)\" v-if=\"scope.row.createBy === $store.getters.name && canEdit\"/>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!-- 新增、详情弹出对话框 -->\n    <el-dialog id=\"saveDialog\" :title=title :visible.sync=\"isShow\" width=\"52%\" @close=\"closeFun\" v-dialogDrag append-to-body>\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"所属部件：\" prop=\"ssbj\">\n              <el-select @change=\"bjChange\" filterable style=\"width: 80%\" v-model=\"form.ssbj\" :disabled=\"isDisabled\" placeholder=\"请选择所属部件\">\n                <el-option\n                  v-for=\"item in bjList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"设备名称：\" prop=\"sbmc\">\n              <el-select filterable style=\"width: 80%\" v-model=\"form.sbmc\" :disabled=\"isDisabled\" placeholder=\"请选择设备名称\">\n                <el-option\n                  v-for=\"item in bwList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"设备型号：\" prop=\"xh\">\n              <el-input v-model=\"form.xh\" :disabled=\"isDisabled\" style=\"width:80%\" placeholder=\"请输入设备型号\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"生产厂家：\" prop=\"sccj\">\n              <el-input v-model=\"form.sccj\" :disabled=\"isDisabled\" style=\"width:80%\" placeholder=\"请输入生产厂家\"/>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"出厂日期：\" prop=\"ccrq\">\n              <el-date-picker style=\"width:80%\" :disabled=\"isDisabled\" v-model=\"form.ccrq\" type=\"date\" format=\"yyyy-MM-dd\" value-format=\"yyyy-MM-dd\" placeholder=\"请选择时间\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"投运日期：\" prop=\"tyrq\">\n              <el-date-picker style=\"width:80%\" :disabled=\"isDisabled\" v-model=\"form.tyrq\" type=\"date\" format=\"yyyy-MM-dd\" value-format=\"yyyy-MM-dd\" placeholder=\"请选择时间\"/>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun\">取 消</el-button>\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"saveFun\">保 存</el-button>\n      </div>\n    </el-dialog>\n  </el-row>\n</template>\n\n<script>\nimport { remove,saveOrUpdate,getPage} from '@/api/dagangOilfield/asset/fsss.js'\nimport {Loading} from \"element-ui\";\nimport {getSbbjList, getSbbwList} from \"@/api/dagangOilfield/bzgl/sbqxwh/sbqxwh\";\n\n  export default {\n    name: 'fsss',\n    props: {\n      sbInfo: {\n        type: Object\n      },\n      canEdit: {\n        type: Boolean\n      },\n    },\n    data() {\n      return {\n        isShow:false,//弹框是否显示\n        loading:false,\n        //是否禁用\n        isDisabled: false,\n        form: {},\n        //查询参数\n        queryParams: {\n          pageSize: 10,\n          pageNum: 1\n        },\n        //详情对话框标题\n        title: '',\n        filterInfo: {\n          data: {\n            ssbj: '',\n            sbmcCn:'',\n            xh:'',\n            sccj:'',\n          },\n          fieldList: [\n            { label: '所属部件', type: 'select', value: 'ssbj',options:[]},\n            { label: '设备名称', type: 'input', value: 'sbmcCn'},\n            { label: '设备型号', type: 'input', value: 'xh'},\n            { label: '生产厂家', type: 'input', value: 'sccj'},\n          ]\n        },\n        tableAndPageInfo: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: { checkBox: false, serialNumber: true },\n          tableData: [],\n          tableHeader: [\n            { prop: 'sbbjCn', label: '所属部件', minWidth: '120' },\n            { prop: 'sbmcCn', label: '设备名称', minWidth: '120' },\n            { prop: 'xh', label: '设备型号', minWidth: '100' },\n            { prop: 'sccj', label: '生产厂家', minWidth: '100' },\n            { prop: 'ccrq', label: '出厂日期', minWidth: '80' },\n            { prop: 'tyrq', label: '投运日期', minWidth: '80' },\n          ]\n        },\n        rules: {\n          ssbj: [\n            {required: true, message: \"所属部件不能为空\", trigger: \"select\"},\n          ],\n          sbmc: [\n            {required: true, message: \"设备名称不能为空\", trigger: \"select\"},\n          ],\n          xh: [\n            {required: true, message: \"设备型号不能为空\", trigger: \"blur\"},\n          ],\n          sccj: [\n            {required: true, message: \"生产厂家不能为空\", trigger: \"blur\"},\n          ],\n        },\n        bjList:[],//部件下拉框\n        bwList:[],//部位（设备名称）下拉框\n      }\n    },\n    created() {\n      //列表查询\n      this.getData();\n      this.getOptions();//获取涉及到的下拉框字典值\n    },\n    methods: {\n      //查询下拉框数据\n      async getOptions(){\n        await this.getBjList();//获取所属部件\n      },\n      //获取所有设备类型下拉框用于查询\n      getBjList(){\n        this.bjList = [];\n        getSbbjList({qxlb:'1',sblx:this.sbInfo.assetTypeCode}).then(res=>{\n          this.bjList = res.data;\n          this.filterInfo.fieldList.forEach(item=>{\n            if(item.value === 'ssbj'){\n              item.options = res.data;\n              return false;\n            }\n          })\n        })\n      },\n      //间隔下拉框change事件\n      async bjChange(val){\n        this.$set(this.form,'sbmc','');\n        this.bwList = [];\n        await getSbbwList({qxlb:'1',sbbj:val}).then(res=>{\n          this.bwList = res.data;\n        })\n      },\n      //保存表单\n      async saveFun() {\n        this.$refs['form'].validate((valid) => {\n          if (valid) {\n            //设置设备id\n            this.form.sbid = this.sbInfo.objId;\n            this.$nextTick(() => {\n              this.saveLoading = Loading.service({\n                lock: true,//lock的修改符--默认是false\n                text: '保存中，请稍后',//显示在加载图标下方的加载文案\n                spinner: 'el-icon-loading',//自定义加载图标类名\n                background: 'rgba(0, 0, 0, 0.7)',//遮罩层颜色\n                target: document.querySelector('#saveDialog')\n              });\n              saveOrUpdate(this.form).then(res=>{\n                if (res.code === '0000') {\n                  this.$message.success('操作成功!');\n                  this.getData();\n                }\n                this.saveLoading.close();\n                this.isShow = false;\n              });\n            })\n          }else{\n            this.$message({type: 'error',message: '校验未通过'});\n          }\n        })\n      },\n      filterReset() {\n        this.filterInfo.fieldList.forEach(item=>{\n        if(item.type === 'checkbox'){\n          item.checkboxValue = [];\n        }\n      })\n      },\n      //分页查询列表\n      getData(params) {\n        //参数合并\n        this.loading = true;\n        const param = { ...this.queryParams, ...params,...{sbid:this.sbInfo.objId}}\n        this.queryParams = param;\n        getPage(param).then(res => {\n          this.tableAndPageInfo.tableData = res.data.records;\n          this.tableAndPageInfo.pager.total = res.data.total;\n          this.loading = false;\n        })\n      },\n      /**\n       * 新增附属设施\n       */\n      addRow() {\n        this.title = '新增附属设施'\n        this.isDisabled = false;\n        this.form = {};\n        this.isShow = true;\n      },\n      /**\n       * 详情查看\n       */\n      getDetails(row) {\n        this.form = {...row};\n        //处理回显问题\n        this.form.ssbj = row.sbbjCn;\n        this.form.sbmc = row.sbmcCn;\n        this.isDisabled = true;\n        this.isShow = true;\n        this.title = '附属设施详情';\n      },\n      /**\n       * 编辑\n       */\n      async updateRow(row) {\n        this.form = {...row};\n        //处理回显问题\n        await this.bjChange(row.ssbj);\n        this.form.sbmc = row.sbmc;\n        this.isDisabled = false;\n        this.isShow = true;\n        this.title = '附属设施编辑';\n      },\n      /**\n       * 删除\n       */\n      async handleDeleteById(id) {\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove([id]).then(({ code }) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getData();\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n      //弹框关闭事件\n      closeFun(){\n        this.form = {};\n        this.isShow = false;\n      },\n    }\n  }\n</script>\n\n<style scoped lang=\"scss\">\n  .card1 {\n    margin-bottom: 6px;\n  }\n\n  .search-condition {\n    font-size: 13px;\n    color: #9c9c9c;\n\n    .el-select {\n      .el-input {\n        width: 100%;\n      }\n    }\n\n    .el-col {\n      vertical-align: middle;\n      line-height: 32px;\n      text-align: left;\n    }\n  }\n</style>\n\n"]}]}