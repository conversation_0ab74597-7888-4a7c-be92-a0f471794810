{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\sbztpjbzk\\sbcs.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\sbztpjbzk\\sbcs.js", "mtime": 1706897314226}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0UGFnZSA9IGdldFBhZ2U7CmV4cG9ydHMuc2F2ZU9yVXBkYXRlID0gc2F2ZU9yVXBkYXRlOwpleHBvcnRzLmRlbEJ5SWQgPSBkZWxCeUlkOwoKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Cgp2YXIgYmFzZVVybCA9ICIvY29uZGl0aW9uLW1haW50ZW5hbmNlLWFwaSI7Ci8q5p+l6K+i5YiX6KGoKi8KCmZ1bmN0aW9uIGdldFBhZ2UocGFyYW1zKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcvc2Jjcy9nZXRQYWdlJywgcGFyYW1zLCAxKTsKfSAvLyDmt7vliqDmiJbkv67mlLkKCgpmdW5jdGlvbiBzYXZlT3JVcGRhdGUocGFyYW1zKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcvc2Jjcy9zYXZlT3JVcGRhdGUnLCBwYXJhbXMsIDEpOwp9IC8vIOWIoOmZpAoKCmZ1bmN0aW9uIGRlbEJ5SWQocGFyYW1zKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcvc2Jjcy9kZWxCeUlkJywgcGFyYW1zLCAxKTsKfQ=="}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagang<PERSON>field/bzgl/sbztpjbzk/sbcs.js"], "names": ["baseUrl", "getPage", "params", "api", "requestPost", "saveOrUpdate", "delById"], "mappings": ";;;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,4BAAhB;AAEA;;AACO,SAASC,OAAT,CAAiBC,MAAjB,EAAyB;AAC9B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,eAAxB,EAAwCE,MAAxC,EAA+C,CAA/C,CAAP;AACD,C,CAED;;;AACO,SAASG,YAAT,CAAsBH,MAAtB,EAA8B;AACnC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,oBAAxB,EAA6CE,MAA7C,EAAoD,CAApD,CAAP;AACD,C,CACA;;;AACM,SAASI,OAAT,CAAiBJ,MAAjB,EAAyB;AAC9B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,eAAxB,EAAwCE,MAAxC,EAA+C,CAA/C,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\nconst baseUrl = \"/condition-maintenance-api\";\n\n/*查询列表*/\nexport function getPage(params) {\n  return api.requestPost(baseUrl+'/sbcs/getPage',params,1)\n}\n\n// 添加或修改\nexport function saveOrUpdate(params) {\n  return api.requestPost(baseUrl+'/sbcs/saveOrUpdate',params,1)\n}\n // 删除\nexport function delById(params) {\n  return api.requestPost(baseUrl+'/sbcs/delById',params,1)\n}\n"]}]}