{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\jbwh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\jbwh.vue", "mtime": 1706897323219}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKCnZhciBfamJ3aF9qYnh4ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3ZpZXdzL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvc2J6dHBqYnprL2pid2hfamJ4eCIpKTsKCnZhciBfamJ3aF9jc3h4ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3ZpZXdzL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvc2J6dHBqYnprL2pid2hfY3N4eCIpKTsKCnZhciBfamJ3aF94eGQgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdmlld3MvZGFnYW5nT2lsZmllbGQvYnpnbC9zYnp0cGpiemsvamJ3aF94eGQiKSk7Cgp2YXIgX2pid2hfZWRpdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC92aWV3cy9kYWdhbmdPaWxmaWVsZC9iemdsL3NienRwamJ6ay9qYndoX2VkaXQiKSk7CgovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSB7CiAgbmFtZTogJ2pid2gnLAogIGNvbXBvbmVudHM6IHsKICAgIEpieHg6IF9qYndoX2pieHguZGVmYXVsdCwKICAgIENzeHg6IF9qYndoX2NzeHguZGVmYXVsdCwKICAgIFh4ZDogX2pid2hfeHhkLmRlZmF1bHQsCiAgICBKYkVkaXQ6IF9qYndoX2VkaXQuZGVmYXVsdAogIH0sCiAgcHJvcHM6IHsKICAgIHNibHg6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnJwogICAgfSwKICAgIG1wRGF0YTogewogICAgICB0eXBlOiBPYmplY3QKICAgIH0sCiAgICBqYjogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIGRlZmF1bHQ6ICcnCiAgICB9CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZmxhZzogJ2pieHgnIC8v6buY6K6k5bGV56S65Z+65pys5L+h5oGv5YiX6KGoCgogICAgfTsKICB9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7fSwKICBtZXRob2RzOiB7CiAgICAvL3RhYuWIh+aNogogICAgY2xpY2s6IGZ1bmN0aW9uIGNsaWNrKG1haW5UYWIpIHsKICAgICAgdGhpcy5mbGFnID0gbWFpblRhYjsKICAgIH0sCiAgICAvL+ihqOagvOWPjOWHu+S6i+S7tgogICAgZGJDbGlja1JvdzogZnVuY3Rpb24gZGJDbGlja1Jvdyh2YWwsIG1hcCkgewogICAgICB0aGlzLiRyZWZzLmVkaXRKYi5wcm9jZXNzUGFyZW50VmFsKHZhbCwgbWFwKTsKICAgIH0sCiAgICBzZXRKYlZhbDogZnVuY3Rpb24gc2V0SmJWYWwodmFsKSB7CiAgICAgIHRoaXMuJGVtaXQoJ3NldEpiVmFsJywgdmFsKTsKICAgIH0sCiAgICAvL+WFs+mXreiEmuacrOW8ueahhgogICAgamJDbG9zZTogZnVuY3Rpb24gamJDbG9zZSgpIHsKICAgICAgdGhpcy4kZW1pdCgnamJDbG9zZScpOwogICAgfQogIH0KfTsKZXhwb3J0cy5kZWZhdWx0ID0gX2RlZmF1bHQ7"}, {"version": 3, "sources": ["jbwh.vue"], "names": [], "mappings": ";;;;;;;;;AA2CA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,MADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,IAAA,EAAA,kBAAA;AAAA,IAAA,IAAA,EAAA,kBAAA;AAAA,IAAA,GAAA,EAAA,iBAAA;AAAA,IAAA,MAAA,EAAA;AAAA,GAFA;AAGA,EAAA,KAAA,EAAA;AACA,IAAA,IAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAAA,EAAA;AAFA,KADA;AAKA,IAAA,MAAA,EAAA;AACA,MAAA,IAAA,EAAA;AADA,KALA;AAQA,IAAA,EAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAAA,EAAA;AAFA;AARA,GAHA;AAgBA,EAAA,IAhBA,kBAgBA;AACA,WAAA;AACA,MAAA,IAAA,EAAA,MADA,CACA;;AADA,KAAA;AAGA,GApBA;AAqBA,EAAA,OArBA,qBAqBA,CACA,CAtBA;AAuBA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,KAFA,iBAEA,OAFA,EAEA;AACA,WAAA,IAAA,GAAA,OAAA;AACA,KAJA;AAKA;AACA,IAAA,UANA,sBAMA,GANA,EAMA,GANA,EAMA;AACA,WAAA,KAAA,CAAA,MAAA,CAAA,gBAAA,CAAA,GAAA,EAAA,GAAA;AACA,KARA;AASA,IAAA,QATA,oBASA,GATA,EASA;AACA,WAAA,KAAA,CAAA,UAAA,EAAA,GAAA;AACA,KAXA;AAYA;AACA,IAAA,OAbA,qBAaA;AACA,WAAA,KAAA,CAAA,SAAA;AACA;AAfA;AAvBA,C", "sourcesContent": ["<template>\n  <el-row :gutter=\"16\">\n    <!--   Tab页签   -->\n    <el-col :span=\"12\">\n      <div class=\"txtTitle\">\n        <span @click=\"click('jbxx')\" :class=\"this.flag === 'jbxx'?'tabActive':'noActive'\" class=\"oneBtn\">\n          <span class=\"allBtn\">基本信息列表</span>\n        </span>\n        <span @click=\"click('csxx')\" :class=\"this.flag === 'csxx'?'tabActive':'noActive'\" class=\"twoBtn\">\n          <span class=\"allBtn\">参数信息列表</span>\n        </span>\n        <span @click=\"click('xxd')\" :class=\"this.flag === 'xxd'?'tabActive':'noActive'\" class=\"twoBtn\">\n          <span class=\"allBtn\">信息点列表</span>\n        </span>\n      </div>\n    </el-col>\n    <el-col :span=\"12\">\n      <div class=\"txtTitle\">\n        <span class=\"oneBtn tabActive\">\n          <span class=\"allBtn\">脚本维护</span>\n        </span>\n      </div>\n    </el-col>\n\n    <!--  基本信息  -->\n    <el-col :span=\"12\" v-if=\"this.flag === 'jbxx'\">\n      <Jbxx :sblx=\"sblx\" @dbClickRow=\"dbClickRow\"></Jbxx>\n    </el-col>\n    <!--  参数信息  -->\n    <el-col :span=\"12\" v-if=\"this.flag === 'csxx'\">\n      <Csxx :sblx=\"sblx\" @dbClickRow=\"dbClickRow\"></Csxx>\n    </el-col>\n    <!--  信息点  -->\n    <el-col :span=\"12\" v-if=\"this.flag === 'xxd'\">\n      <Xxd :ssztl-id=\"mpData.objId\" @dbClickRow=\"dbClickRow\"></Xxd>\n    </el-col>\n    <!--  脚本编辑  -->\n    <el-col :span=\"12\">\n      <JbEdit ref=\"editJb\" :jb=\"jb\" @setJbVal=\"setJbVal\" @jbClose=\"jbClose\"></JbEdit>\n    </el-col>\n  </el-row>\n</template>\n<script>\nimport Jbxx from \"@/views/dagangOilfield/bzgl/sbztpjbzk/jbwh_jbxx\";\nimport Csxx from \"@/views/dagangOilfield/bzgl/sbztpjbzk/jbwh_csxx\";\nimport Xxd from \"@/views/dagangOilfield/bzgl/sbztpjbzk/jbwh_xxd\";\nimport JbEdit from \"@/views/dagangOilfield/bzgl/sbztpjbzk/jbwh_edit\";\n\nexport default {\n  name: 'jbwh',\n  components: {Jbxx,Csxx,Xxd,JbEdit},\n  props: {\n    sblx:{\n      type:String,\n      default:'',\n    },\n    mpData: {\n      type: Object,\n    },\n    jb: {\n      type: String,\n      default:'',\n    },\n  },\n  data() {\n    return {\n      flag:'jbxx', //默认展示基本信息列表\n    };\n  },\n  mounted() {\n  },\n  methods:{\n    //tab切换\n    click(mainTab){\n      this.flag = mainTab;\n    },\n    //表格双击事件\n    dbClickRow(val,map){\n      this.$refs.editJb.processParentVal(val,map);\n    },\n    setJbVal(val){\n      this.$emit('setJbVal',val);\n    },\n    //关闭脚本弹框\n    jbClose(){\n      this.$emit('jbClose');\n    },\n  },\n}\n</script>\n\n<style scoped lang=\"scss\">\n.jbwh_box{\n  padding: 20px 0 0 20px;\n}\n.jbwh_box1{\n  margin-top: -18px;\n}\n.tabActive{\n  //width: 10%;\n  float: left;\n  color:#fff;\n  background: #02b988;\n  border-top:0;\n}\n.noActive{\n  //width: 10%;\n  float: left;\n  background: #fff;\n  color:#545252;\n  &:hover {\n    background: #FFFFFF;\n    color: #359076;\n  }\n}\n.oneBtn{\n  margin-right: -15px;\n}\n.twoBtn{\n  transform: skewX(33deg);\n  border-right:  1px solid #9a989869;\n  .allBtn{\n    transform: skewX(-33deg);\n    display: inline-block;\n  }\n}\n\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk"}]}