{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\bdgl\\bdsbgl.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\bdgl\\bdsbgl.vue", "mtime": 1706897324519}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["bdsbgl.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AA0rBA;;AAkBA;;AACA;;AACA;;AAKA;;AAEA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAHA;eAKA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,kBAAA,EAAA,2BAAA;AAAA,IAAA,QAAA,EAAA,iBAAA;AAAA,IAAA,QAAA,EAAA;AAAA,GAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,oBAAA,EAAA,UADA;AAEA,QAAA,WAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,QAHA;AAIA,QAAA,SAAA,EAAA,EAJA;AAKA,QAAA,QAAA,EAAA,EALA;AAMA,QAAA,WAAA,EAAA,IANA;AAOA,QAAA,WAAA,EAAA;AAPA,OAFA;AAWA;AACA,MAAA,cAAA,EAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAZA;AAaA;AACA,MAAA,QAAA,EAAA,EAdA;AAeA;AACA,MAAA,YAAA,EAAA,KAhBA;AAiBA;AACA,MAAA,MAAA,EAAA,KAlBA;AAoBA,MAAA,WAAA,EAAA;AACA,QAAA,UAAA,EAAA,SADA;AAEA,QAAA,IAAA,EAAA;AAFA,OApBA;AAwBA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAxBA;AA4BA,MAAA,aAAA,EAAA,KA5BA;AA6BA;AACA,MAAA,UAAA,EAAA,EA9BA;AA+BA;AACA,MAAA,uBAAA,EAAA,EAhCA;AAiCA;AACA,MAAA,qBAAA,EAAA,EAlCA;AAmCA;AACA,MAAA,oBAAA,EAAA,EApCA;AAqCA;AACA,MAAA,QAAA,EAAA,EAtCA;AAuCA;AACA,MAAA,QAAA,EAAA,EAxCA;AAyCA;AACA,MAAA,cAAA,EAAA;AACA,QAAA,IAAA,EAAA,SADA;AAEA,QAAA,MAAA,EAAA,SAFA;AAGA,QAAA,KAAA,EAAA,SAHA;AAIA,QAAA,IAAA,EAAA,SAJA;AAKA,QAAA,IAAA,EAAA,SALA;AAMA,QAAA,aAAA,EAAA,SANA;AAOA,QAAA,IAAA,EAAA,SAPA;AAQA,QAAA,OAAA,EAAA,CARA;AASA,QAAA,QAAA,EAAA;AATA,OA1CA;AAqDA;AACA,MAAA,mBAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAHA,CAtDA;AA2DA;AACA,MAAA,iBAAA,EAAA,EA5DA;AA6DA;AACA,MAAA,mBAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,OAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAJA,CA9DA;AAoEA;AACA,MAAA,kBAAA,EAAA,EArEA;AAsEA;AACA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SARA,EASA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SATA,EAUA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAVA,EAWA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAXA,EAYA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAZA,EAaA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA;AACA;;;;;;;;;;;;;AAdA;AAZA,OAvEA;AAgHA,MAAA,aAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,YAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA;AAZA,OAhHA;AAqIA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,IAAA,EAAA,EADA;AAEA,UAAA,aAAA,EAAA,EAFA;AAGA,UAAA,IAAA,EAAA;AAHA,SADA;AAMA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,eAHA;AAIA,UAAA,OAAA,EAAA,CACA;AAAA,YAAA,KAAA,EAAA,OAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WADA,EAEA;AAAA,YAAA,KAAA,EAAA,QAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAFA,EAGA;AAAA,YAAA,KAAA,EAAA,OAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAHA;AAJA,SAFA,EAYA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA,CACA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WADA,EAEA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAFA;AAJA,SAZA;AANA,OAtIA;AAmKA;AACA,MAAA,UAAA,EAAA,EApKA;AAqKA;AACA,MAAA,wBAAA,EAAA,EAtKA;AAuKA;AACA,MAAA,QAAA,EAAA,EAxKA;AAyKA;AACA,MAAA,wBAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,OAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAJA,CA1KA;AAgLA;AACA,MAAA,cAAA,EAAA,KAjLA;AAkLA;AACA,MAAA,aAAA,EAAA,EAnLA;AAoLA;AACA,MAAA,QAAA,EAAA,EArLA;AAsLA;AACA,MAAA,OAAA,EAAA,IAvLA;AAwLA;AACA,MAAA,cAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,IAAA,EAAA,OAFA;AAGA,QAAA,IAAA,EAAA,MAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,IAAA,EAAA,SALA;AAMA,QAAA,IAAA,EAAA,YANA;AAOA,QAAA,GAAA,EAAA;AAPA,OADA,EAUA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,IAAA,EAAA,OAFA;AAGA,QAAA,IAAA,EAAA,MAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,IAAA,EAAA,SALA;AAMA,QAAA,IAAA,EAAA,YANA;AAOA,QAAA,GAAA,EAAA;AAPA,OAVA,EAmBA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,IAAA,EAAA,OAFA;AAGA,QAAA,IAAA,EAAA,MAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,IAAA,EAAA,SALA;AAMA,QAAA,IAAA,EAAA,YANA;AAOA,QAAA,GAAA,EAAA;AAPA,OAnBA,EA4BA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,IAAA,EAAA,OAFA;AAGA,QAAA,IAAA,EAAA,MAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,IAAA,EAAA,SALA;AAMA,QAAA,IAAA,EAAA,YANA;AAOA,QAAA,GAAA,EAAA;AAPA,OA5BA,EAqCA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,IAAA,EAAA,OAFA;AAGA,QAAA,IAAA,EAAA,MAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,IAAA,EAAA,SALA;AAMA,QAAA,IAAA,EAAA,YANA;AAOA,QAAA,GAAA,EAAA;AAPA,OArCA,CAzLA;AAwOA;AACA,MAAA,YAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,IAAA,EAAA,OAFA;AAGA,QAAA,IAAA,EAAA,MAHA;AAIA,QAAA,IAAA,EAAA,IAJA;AAKA,QAAA,IAAA,EAAA,MALA;AAMA,QAAA,IAAA,EAAA,OANA;AAOA,QAAA,IAAA,EAAA;AAPA,OADA,EAUA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,IAAA,EAAA,OAFA;AAGA,QAAA,IAAA,EAAA,MAHA;AAIA,QAAA,IAAA,EAAA,IAJA;AAKA,QAAA,IAAA,EAAA,MALA;AAMA,QAAA,IAAA,EAAA,OANA;AAOA,QAAA,IAAA,EAAA;AAPA,OAVA,EAmBA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,IAAA,EAAA,OAFA;AAGA,QAAA,IAAA,EAAA,MAHA;AAIA,QAAA,IAAA,EAAA,IAJA;AAKA,QAAA,IAAA,EAAA,MALA;AAMA,QAAA,IAAA,EAAA,OANA;AAOA,QAAA,IAAA,EAAA;AAPA,OAnBA,EA4BA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,IAAA,EAAA,OAFA;AAGA,QAAA,IAAA,EAAA,MAHA;AAIA,QAAA,IAAA,EAAA,IAJA;AAKA,QAAA,IAAA,EAAA,MALA;AAMA,QAAA,IAAA,EAAA,OANA;AAOA,QAAA,IAAA,EAAA;AAPA,OA5BA,EAqCA;AACA,QAAA,IAAA,EAAA,OADA;AAEA,QAAA,IAAA,EAAA,OAFA;AAGA,QAAA,IAAA,EAAA,MAHA;AAIA,QAAA,IAAA,EAAA,IAJA;AAKA,QAAA,IAAA,EAAA,MALA;AAMA,QAAA,IAAA,EAAA,OANA;AAOA,QAAA,IAAA,EAAA;AAPA,OArCA,CAzOA;AAwRA;AACA,MAAA,YAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,MAFA;AAGA,QAAA,IAAA,EAAA,OAHA;AAIA,QAAA,IAAA,EAAA,OAJA;AAKA,QAAA,IAAA,EAAA,MALA;AAMA,QAAA,IAAA,EAAA,EANA;AAOA,QAAA,EAAA,EAAA,GAPA;AAQA,QAAA,IAAA,EAAA,YARA;AASA,QAAA,GAAA,EAAA,IATA;AAUA,QAAA,IAAA,EAAA;AAVA,OADA,EAaA;AACA,QAAA,IAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,MAFA;AAGA,QAAA,IAAA,EAAA,OAHA;AAIA,QAAA,IAAA,EAAA,OAJA;AAKA,QAAA,IAAA,EAAA,MALA;AAMA,QAAA,IAAA,EAAA,EANA;AAOA,QAAA,EAAA,EAAA,GAPA;AAQA,QAAA,IAAA,EAAA,YARA;AASA,QAAA,GAAA,EAAA,IATA;AAUA,QAAA,IAAA,EAAA;AAVA,OAbA,EAyBA;AACA,QAAA,IAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,MAFA;AAGA,QAAA,IAAA,EAAA,OAHA;AAIA,QAAA,IAAA,EAAA,OAJA;AAKA,QAAA,IAAA,EAAA,MALA;AAMA,QAAA,IAAA,EAAA,EANA;AAOA,QAAA,EAAA,EAAA,GAPA;AAQA,QAAA,IAAA,EAAA,YARA;AASA,QAAA,GAAA,EAAA,IATA;AAUA,QAAA,IAAA,EAAA;AAVA,OAzBA,EAqCA;AACA,QAAA,IAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,MAFA;AAGA,QAAA,IAAA,EAAA,OAHA;AAIA,QAAA,IAAA,EAAA,OAJA;AAKA,QAAA,IAAA,EAAA,MALA;AAMA,QAAA,IAAA,EAAA,EANA;AAOA,QAAA,EAAA,EAAA,GAPA;AAQA,QAAA,IAAA,EAAA,YARA;AASA,QAAA,GAAA,EAAA,IATA;AAUA,QAAA,IAAA,EAAA;AAVA,OArCA,EAiDA;AACA,QAAA,IAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,MAFA;AAGA,QAAA,IAAA,EAAA,OAHA;AAIA,QAAA,IAAA,EAAA,OAJA;AAKA,QAAA,IAAA,EAAA,MALA;AAMA,QAAA,IAAA,EAAA,EANA;AAOA,QAAA,EAAA,EAAA,GAPA;AAQA,QAAA,IAAA,EAAA,YARA;AASA,QAAA,GAAA,EAAA,IATA;AAUA,QAAA,IAAA,EAAA;AAVA,OAjDA,EA6DA;AACA,QAAA,IAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,MAFA;AAGA,QAAA,IAAA,EAAA,OAHA;AAIA,QAAA,IAAA,EAAA,OAJA;AAKA,QAAA,IAAA,EAAA,MALA;AAMA,QAAA,IAAA,EAAA,EANA;AAOA,QAAA,EAAA,EAAA,GAPA;AAQA,QAAA,IAAA,EAAA,YARA;AASA,QAAA,GAAA,EAAA,IATA;AAUA,QAAA,IAAA,EAAA;AAVA,OA7DA,CAzRA;AAmWA;AACA,MAAA,eAAA,EAAA,MApWA;AAqWA;AACA,MAAA,OAAA,EAAA,CACA;AACA,QAAA,GAAA,EACA;AAFA,OADA,EAKA;AACA,QAAA,GAAA,EACA;AAFA,OALA,EASA;AACA,QAAA,GAAA,EACA;AAFA,OATA,EAaA;AACA,QAAA,GAAA,EACA;AAFA,OAbA,CAtWA;AAwXA;AACA,MAAA,WAAA,EAAA,EAzXA;AA0XA;AACA,MAAA,oBAAA,EAAA,IA3XA;AA4XA;AACA,MAAA,aAAA,EAAA,QA7XA;AA8XA;AACA,MAAA,YAAA,EAAA,IA/XA;AAgYA;AACA,MAAA,WAAA,EAAA,KAjYA;AAkYA;AACA,MAAA,WAAA,EAAA,KAnYA;AAoYA;AACA,MAAA,iBAAA,EAAA,KArYA;AAsYA;AACA,MAAA,oBAAA,EAAA,KAvYA;AAwYA;AACA,MAAA,mBAAA,EAAA,KAzYA;AA0YA;AACA,MAAA,IAAA,EAAA,EA3YA;AA4YA,MAAA,OAAA,EAAA,KA5YA;AA6YA,MAAA,UAAA,EAAA;AACA,QAAA,MAAA,EAAA;AADA,OA7YA;AAgZA,MAAA,KAAA,EAAA;AACA,QAAA,KAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAJA;AAKA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CALA;AAMA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CANA;AASA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CATA;AAYA,QAAA,aAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAZA;AAeA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAfA;AAkBA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AAlBA,OAhZA;AAqaA;AACA,MAAA,gBAAA,EAAA;AAtaA,KAAA;AAwaA,GA5aA;AA6aA,EAAA,KAAA,EAAA;AACA;AACA,IAAA,UAFA,sBAEA,GAFA,EAEA;AACA,WAAA,KAAA,CAAA,IAAA,CAAA,MAAA,CAAA,GAAA;AACA;AAJA,GA7aA;AAmbA,EAAA,OAnbA,qBAmbA;AACA;AACA,SAAA,uBAAA,GAFA,CAGA;;AACA,SAAA,cAAA,GAJA,CAKA;;AACA,SAAA,OAAA,GANA,CAOA;;AACA,SAAA,uBAAA;AACA,GA5bA;AA6bA,EAAA,OA7bA,qBA6bA;AACA;AACA,SAAA,sBAAA;AACA,GAhcA;AAicA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,QAFA,sBAEA;AACA,UAAA,GAAA,GAAA,KAAA,UAAA,CAAA,CAAA,CAAA;AACA,WAAA,cAAA,CAAA,KAAA,GAAA,MAAA;AACA,WAAA,WAAA,CAAA,WAAA,GAAA,KAAA;AACA,WAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,WAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AACA,WAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,WAAA,MAAA,GAAA,IAAA;AACA,KAVA;AAWA;AACA,IAAA,SAZA,qBAYA,GAZA,EAYA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,KAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AADA;AAAA,uBAEA,8BAAA,KAAA,CAAA,WAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,sBAEA,IAFA;AAEA,gBAAA,IAFA,sBAEA,IAFA;AAGA,gBAAA,KAAA,CAAA,QAAA,GAAA,IAAA;AACA,gBAAA,KAAA,CAAA,YAAA,GAAA,IAAA;;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,KAjBA;AAkBA;AACA,IAAA,UAnBA,sBAmBA,IAnBA,EAmBA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,IAAA;AADA,+BAEA,IAAA,CAAA,cAFA;AAAA,kDAGA,IAHA;AAAA;;AAAA;AAIA,0CAAA,IAAA,CAAA,SAAA,CAAA,UAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,sBAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,oBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA;;AACA,oBAAA,MAAA,CAAA,aAAA,GAAA,KAAA;;AACA,oBAAA,MAAA,CAAA,OAAA;AACA;AACA,iBANA;AAJA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA,KAhCA;AAiCA;AACA,IAAA,SAlCA,qBAkCA,IAlCA,EAkCA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA;AACA,UAAA,GAAA,mCAAA,IAAA,CAAA,IAAA,CAAA;;AACA,UAAA,IAAA,CAAA,IAAA,KAAA,UAAA,EAAA;AACA,aAAA,cAAA,CAAA,KAAA,GAAA,MAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AACA,aAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,aAAA,WAAA,CAAA,SAAA,CAAA,UAAA,GAAA,GAAA;AACA,OAPA,MAOA;AACA,aAAA,cAAA,CAAA,KAAA,GAAA,QAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AACA,aAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA;;AACA,WAAA,MAAA,GAAA,IAAA;AACA,KApDA;AAqDA;AACA,IAAA,aAtDA,2BAsDA;AACA,WAAA,MAAA,GAAA,KAAA;AACA,KAxDA;AAyDA;AACA,IAAA,aA1DA,2BA0DA;AACA,WAAA,YAAA,GAAA,KAAA;AACA,KA5DA;;AA6DA;;;AAGA,IAAA,qBAhEA,iCAgEA,IAhEA,EAgEA;AACA,WAAA,UAAA,GAAA,IAAA,CADA,CAGA;;AACA,WAAA,gBAAA,GAAA,IAAA,CAAA,MAAA,IAAA,CAAA;AACA,KArEA;;AAsEA;;;AAGA,IAAA,uBAzEA,qCAyEA;AAAA;;AACA,UAAA,SAAA,GAAA;AACA,QAAA,IAAA,EAAA;AADA,OAAA;AAGA,2CAAA,SAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,uBAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KAhFA;;AAkFA;;;AAGA,IAAA,WArFA,yBAqFA;AAAA;;AACA,UAAA,KAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,aAAA;AACA;AACA;;AACA,UAAA,GAAA,GAAA,KAAA,UAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA;AACA,OAFA,CAAA;AAGA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAKA,IALA,CAKA,YAAA;AACA,iCAAA,GAAA,EAAA,IAAA,CAAA,gBAAA;AAAA,cAAA,IAAA,QAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,OAAA;AACA,WANA,MAMA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAIA;AACA,SAbA;AAcA,OApBA,EAqBA,KArBA,CAqBA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OA1BA;AA2BA,WAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;AACA,WAAA,OAAA;AACA,KA1HA;AA2HA;AACA,IAAA,iBA5HA,+BA4HA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAA,iBAAA,GAAA,IAAA,CARA,CASA;;AACA,WAAA,cAAA,GAAA,KAAA;AACA,KAvIA;AAwIA;AACA,IAAA,qBAzIA,mCAyIA;AACA;AACA,WAAA,IAAA,CAAA,KAAA,QAAA,EAAA,MAAA,EAAA,EAAA,EAFA,CAGA;;AACA,WAAA,qBAAA;AACA,KA9IA;AA+IA;AACA,IAAA,sBAhJA,oCAgJA;AAAA;;AACA,0CAAA,KAAA,qBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,kBAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA,EADA,CAIA;AACA;AACA,KAtJA;AAuJA;AACA,IAAA,qBAxJA,mCAwJA;AAAA;;AACA;AACA,WAAA,oBAAA,CAAA,KAAA,GAAA,KAAA,QAAA,CAAA,KAAA;AACA,yCAAA,KAAA,oBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,iBAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KA9JA;AA+JA,IAAA,YA/JA,wBA+JA,GA/JA,EA+JA;AACA,WAAA,UAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA;AACA,WAAA,UAAA,CAAA,KAAA,GAAA,GAAA,CAAA,KAAA,EAAA,KAAA,aAAA,GAAA,IAAA;AACA,KAlKA;AAmKA,IAAA,YAnKA,0BAmKA;AAAA;;AACA,WAAA,QAAA,CAAA,eAAA,KAAA,UAAA,CAAA,IAAA,GAAA,GAAA,EAAA,EAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,UAAA,GAAA,EAAA;AACA;AACA,QAAA,MAAA,CAAA,aAAA,GAAA,KAAA,CAFA,CAGA;;AACA,QAAA,MAAA,CAAA,MAAA,GAAA,IAAA;;AACA,QAAA,MAAA,CAAA,SAAA,CAAA;AAAA,UAAA,IAAA,EAAA,MAAA,CAAA,UAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SAAA,EALA,CAMA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAjBA;AAkBA,KAtLA;;AAwLA;;;AAGA,IAAA,YA3LA,wBA2LA,GA3LA,EA2LA;AAAA;;AACA,UAAA,KAAA,+DAAA,GAAA,GAAA,KAAA,WAAA,CAAA;AACA,kCAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,aAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,MAAA,CAAA,aAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,OAHA;AAIA,KAjMA;AAmMA;AACA,IAAA,WApMA,uBAoMA,GApMA,EAoMA;AACA,WAAA,mBAAA,CAAA,GAAA;AACA,WAAA,UAAA,CAAA,MAAA,GAAA,GAAA,CAAA,aAAA;AACA,WAAA,aAAA;AACA,WAAA,WAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA;AACA,WAAA,WAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA;AACA,WAAA,YAAA,GANA,CAOA;;AACA,WAAA,iBAAA,GAAA,IAAA,CARA,CASA;;AACA,WAAA,QAAA,GAAA,GAAA,CAVA,CAWA;;AACA,WAAA,cAAA,GAAA,KAAA;AACA,KAjNA;AAkNA;AACA,IAAA,YAnNA,wBAmNA,GAnNA,EAmNA;AACA,WAAA,mBAAA,CAAA,GAAA;AAEA,WAAA,WAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA;AACA,WAAA,WAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA;AACA,WAAA,YAAA,GALA,CAMA;;AACA,WAAA,iBAAA,GAAA,IAAA,CAPA,CAQA;;AACA,WAAA,QAAA,GAAA,GAAA,CATA,CAUA;;AACA,WAAA,cAAA,GAAA,IAAA;AACA,KA/NA;AAgOA;AACA,IAAA,0BAjOA,sCAiOA,GAjOA,EAiOA,KAjOA,EAiOA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,GAAA,EAAA,KAAA;AACA,KAnOA;AAoOA;AACA,IAAA,WArOA,yBAqOA,CAAA,CArOA;AAsOA;AACA,IAAA,OAvOA,mBAuOA,MAvOA,EAuOA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,KAFA,+DAEA,MAAA,CAAA,cAFA,GAEA,MAFA;AAAA;AAAA,uBAGA,iCAAA,KAAA,CAHA;;AAAA;AAAA;AAGA,gBAAA,IAHA,yBAGA,IAHA;AAGA,gBAAA,IAHA,yBAGA,IAHA;;AAIA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA,IAAA,CAAA,OAAA;AACA;;AARA;AAAA;;AAAA;AAAA;AAAA;AAUA,gBAAA,OAAA,CAAA,GAAA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,KAnPA;AAoPA;AACA,IAAA,UArPA,sBAqPA,KArPA,EAqPA,IArPA,EAqPA;AACA,UAAA,CAAA,KAAA,EAAA,OAAA,IAAA;AACA,aAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,KAAA,MAAA,CAAA,CAAA;AACA,KAxPA;AAyPA;AACA,IAAA,cA1PA,4BA0PA;AAAA;;AACA;AACA,WAAA,cAAA,CAAA,IAAA,GAAA,KAAA,QAAA,CAAA,MAAA;AACA,WAAA,cAAA,CAAA,MAAA,GAAA,KAAA,QAAA,CAAA,MAAA;AACA,kCAAA,KAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,GAAA;AACA,QAAA,OAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAHA,EAJA,CAQA;;AACA,WAAA,OAAA;AACA,KApQA;AAqQA;AACA,IAAA,uBAtQA,qCAsQA;AAAA;;AACA,UAAA,QAAA,GAAA,MAAA;AACA,2CAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,wBAAA,GAAA,GAAA,CAAA,IAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,OAAA,CAAA,wBAAA;AACA,OAHA;AAIA,KA5QA;AA6QA;AACA,IAAA,MA9QA,oBA8QA;AAAA;;AACA,WAAA,KAAA,CAAA,UAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,UAAA,OAAA,CAAA,QAAA,GADA,CAEA;AACA;;AACA,SAJA,MAIA;AACA,iBAAA,KAAA;AACA;AACA,OARA;AASA,KAxRA;;AAyRA;;;AAGA,IAAA,QA5RA,sBA4RA;AAAA;;AACA,WAAA,QAAA,CAAA,cAAA,GAAA,KAAA,QAAA;AACA,4BAAA,KAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,UAAA,OAAA,CAAA,QAAA,CAAA;AACA,YAAA,IAAA,EAAA,SADA;AAEA,YAAA,OAAA,EAAA;AAFA,WAAA;;AAIA,UAAA,OAAA,CAAA,iBAAA,GAAA,KAAA;AACA,UAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,UAAA,OAAA,CAAA,OAAA;AACA;AACA,OAVA;AAWA,KAzSA;AA0SA;AACA,IAAA,eA3SA,6BA2SA;AAAA;;AACA,sCAAA,KAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,iBAAA,GAAA,KAAA;AACA,OAFA;AAGA,KA/SA;AAgTA;AACA,IAAA,UAjTA,sBAiTA,IAjTA,EAiTA;AACA,WAAA,UAAA,CAAA,MAAA,GAAA,IAAA;AACA,WAAA,aAAA;AACA,KApTA;AAqTA;AACA,IAAA,aAtTA,2BAsTA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,aAAA,GAAA,EAAA;AACA,kDAAA,OAAA,CAAA,UAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,GAAA;AACA,kBAAA,OAAA,CAAA,aAAA,GAAA,GAAA,CAAA,IAAA;;AACA,kBAAA,OAAA,CAAA,aAAA;AACA,iBAJA;;AAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,KA7TA;AA8TA;AACA,IAAA,aA/TA,2BA+TA;AAAA;;AACA,sCAAA,KAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,IAAA,EAAA,EAAA;AACA,UAAA,OAAA,CAAA,QAAA,mCAAA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,OAAA,CAAA,QAAA;AACA;AACA,OALA;AAMA,KAtUA;AAuUA;AACA,IAAA,mBAxUA,+BAwUA,GAxUA,EAwUA;AACA,WAAA,QAAA,GAAA,EAAA;AACA,WAAA,UAAA,CAAA,MAAA,GAAA,GAAA,CAAA,aAAA;AACA,WAAA,QAAA,CAAA,MAAA,GAAA,GAAA,CAAA,aAAA;AACA,WAAA,QAAA,CAAA,IAAA,GAAA,GAAA,CAAA,KAAA;AACA,WAAA,aAAA;AACA,KA9UA;AAgVA;AACA,IAAA,eAjVA,2BAiVA,IAjVA,EAiVA,CAjVA,EAiVA;AACA,UAAA,IAAA,CAAA,UAAA,IAAA,GAAA,EAAA;AACA;AACA;AACA,aAAA,cAAA,GAAA,EAAA,CAHA,CAIA;;AACA,aAAA,OAAA;AACA,OANA,MAMA,IAAA,IAAA,CAAA,UAAA,IAAA,GAAA,EAAA;AACA;AACA,aAAA,cAAA,CAAA,IAAA,GAAA,EAAA;AACA,aAAA,cAAA,CAAA,KAAA,GAAA,IAAA,CAAA,EAAA,CAHA,CAIA;;AACA,aAAA,OAAA;AACA,OANA,MAMA,IAAA,IAAA,CAAA,UAAA,IAAA,GAAA,EAAA;AACA;AACA,aAAA,cAAA,CAAA,KAAA,GAAA,EAAA;AACA,aAAA,cAAA,CAAA,IAAA,GAAA,IAAA,CAAA,EAAA,CAHA,CAIA;;AACA,aAAA,OAAA;AACA;AACA,KArWA;AAuWA;AACA,IAAA,SAxWA,uBAwWA;AACA,WAAA,QAAA,GAAA,KAAA,QAAA,CAAA,IAAA,GAAA,IAAA;AACA,WAAA,QAAA,GAAA,KAAA,QAAA,CAAA,IAAA,GAAA,IAAA;AACA,WAAA,SAAA,CAAA,YAAA;AACA,aAAA,KAAA,CAAA,UAAA,EAAA,aAAA;AACA,OAFA;AAGA,WAAA,iBAAA,GAAA,KAAA;AACA;AA/WA;AAjcA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"16\">\n      <el-col :span=\"4\">\n        <el-card shadow=\"never\" style=\"background:#e0f8ed;padding-top:10px;\">\n          <div>\n            <el-col>\n              <el-form label-width=\"80px\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"所属公司:\">\n                    <el-select\n                      v-model=\"treeForm.ssdwbm\"\n                      placeholder=\"请选择所属公司\"\n                      @change=\"getNewTreeInfo\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in OrganizationSelectedList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"String(item.value)\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"电压等级:\">\n                    <el-select\n                      v-model=\"treeForm.dydjbm\"\n                      placeholder=\"请选择电压等级\"\n                      @change=\"getNewTreeInfo\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in VoltageLevelSelectedList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"快速查询:\">\n                    <el-input\n                      placeholder=\"输入关键字进行过滤\"\n                      v-model=\"filterText\"\n                      clearable\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-form>\n            </el-col>\n          </div>\n          <div class=\"text head-container\">\n            <el-col style=\"padding:0\">\n              <el-tree\n                :expand-on-click-node=\"true\"\n                id=\"tree\"\n                :data=\"treeOptions\"\n                @node-click=\"handleNodeClick\"\n                ref=\"tree\"\n                :filter-node-method=\"filterNode\"\n                node-key=\"id\"\n                :default-expanded-keys=\"['0']\"\n                :default-checked-keys=\"['0']\"\n                :indent=\"18\"\n                accordion\n              />\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo1.data\"\n          :field-list=\"filterInfo1.fieldList\"\n          :width=\"{ labelWidth: 120, itemWidth: 180 }\"\n          @handleReset=\"filterReset\"\n        />\n        <el-white class=\"button-group\">\n          <div v-if=\"isShow3\" class=\"button_btn\">\n            <el-button\n              icon=\"el-icon-plus\"\n              @click=\"sbAddSensorButton\"\n              v-hasPermi=\"['zlsbtz:button:add']\"\n              type=\"primary\"\n              >新增</el-button\n            >\n            <el-button\n              icon=\"el-icon-delete\"\n              v-hasPermi=\"['zlsbtz:button:delete']\"\n              type=\"danger\"\n              @click=\"removeAsset\"\n              >删除</el-button\n            >\n            <!--<el-button icon=\"el-icon-s-check\" type=\"success\" @click=\"spZtbgjl\" :disabled=\"spButtonDisabled\">审批</el-button>-->\n          </div>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"64vh\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"updateAsset(scope.row)\"\n                  v-hasPermi=\"['zlsbtz:button:update']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                ></el-button>\n                <el-button\n                  @click=\"assetDetails(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!--设备展示结束-->\n    <!-- 一次设备弹出框开始展示设备履历 -->\n    <el-dialog\n      title=\"设备详情\"\n      :visible.sync=\"dialogFormVisible\"\n      width=\"60%\"\n      :before-close=\"resetForm\"\n      v-dialogDrag\n    >\n      <el-tabs v-model=\"activeTabName\">\n        <el-tab-pane label=\"基本信息\" name=\"sbDesc\">\n          <!--          <div class=\"block\" style=\"width: 50%;height: 50%;margin-bottom: 2%;float: right\">-->\n          <!--            <span class=\"demonstration\">设备图片</span>-->\n          <!--            <el-carousel trigger=\"click\" height=\"150px\" indicator-position=\"none\" :interval=\"2000\" type=\"card\">-->\n          <!--              <el-carousel-item v-for=\"(img,index) in imgList\" :key=\"index\">-->\n          <!--                <img :src=\"img.url\" width=\"100%\" height=\"100%\">-->\n          <!--              </el-carousel-item>-->\n          <!--            </el-carousel>-->\n          <!--          </div>-->\n          <el-form\n            :model=\"sbxxForm\"\n            label-width=\"130px\"\n            ref=\"sbxxForm\"\n            :rules=\"rules\"\n            :disabled=\"assetIsDisable\"\n          >\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-form-item label=\"所属公司\" prop=\"ssgs\">\n                  <el-select\n                    v-model=\"sbxxForm.ssgs\"\n                    placeholder=\"请选择所属公司\"\n                    filterable\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in OrganizationSelectedList\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"String(item.value)\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"所属电站\" prop=\"ssbdz\">\n                  <el-select\n                    v-model=\"sbxxForm.ssbdz\"\n                    placeholder=\"请选择所属电站\"\n                    filterable\n                    @change=\"bdzOptionsChangeClick\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in bdzOptionsDataList\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"所属间隔\" prop=\"ssjg\">\n                  <el-select\n                    v-model=\"sbxxForm.ssjg\"\n                    placeholder=\"请选择所属间隔\"\n                    filterable\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in jgOptionsDataList\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"设备代码\" prop=\"sbdm\">\n                  <el-input\n                    v-model=\"sbxxForm.sbdm\"\n                    placeholder=\"请填写设备代码\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"设备名称\" prop=\"sbmc\">\n                  <el-input\n                    v-model=\"sbxxForm.sbmc\"\n                    placeholder=\"请填写设备名称\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"设备类型\" prop=\"assetTypeCode\">\n                  <el-select\n                    v-model=\"sbxxForm.assetTypeCode\"\n                    placeholder=\"请选择设备类型\"\n                    @change=\"showParams\"\n                    filterable\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in sblxOptionsDataSelected\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"电压等级\" prop=\"dydjbm\">\n                  <el-select\n                    v-model=\"sbxxForm.dydjbm\"\n                    placeholder=\"请选择电压等级\"\n                  >\n                    <el-option\n                      v-for=\"item in dydjOptionsDataList\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"设备状态\" prop=\"sbzt\">\n                  <el-select\n                    v-model=\"sbxxForm.sbzt\"\n                    placeholder=\"请选择设备状态\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in sbztOptionsDataList\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"相别\">\n                  <el-input\n                    v-model=\"sbxxForm.xb\"\n                    placeholder=\"请填写相别\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"相数\">\n                  <el-input\n                    v-model=\"sbxxForm.xs\"\n                    placeholder=\"请填写相数\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"安装位置\">\n                  <el-input\n                    v-model=\"sbxxForm.azwz\"\n                    placeholder=\"请填写安装位置\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"用途\">\n                  <el-input\n                    v-model=\"sbxxForm.yt\"\n                    placeholder=\"请填写用途\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"型号\">\n                  <el-select v-model=\"sbxxForm.xh\" placeholder=\"请选择型号\">\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"产品代号\">\n                  <el-input\n                    v-model=\"sbxxForm.cpdh\"\n                    placeholder=\"请填写产品代号\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"额定电压\">\n                  <el-input\n                    v-model=\"sbxxForm.eddy\"\n                    placeholder=\"请填写额定电压\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"额定频率\">\n                  <el-input\n                    v-model=\"sbxxForm.edpl\"\n                    placeholder=\"请填写额定频率\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"使用环境\">\n                  <el-input\n                    v-model=\"sbxxForm.syhj\"\n                    placeholder=\"请填写使用环境\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"生产厂家\">\n                  <el-input\n                    v-model=\"sbxxForm.sccj\"\n                    placeholder=\"请填写生产厂家\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"制造国家\">\n                  <el-input\n                    v-model=\"sbxxForm.zzgj\"\n                    placeholder=\"请填写制造国家\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"组合设备类型\">\n                  <el-input\n                    v-model=\"sbxxForm.zhsblxbm\"\n                    placeholder=\"请填写组合设备类型\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"组合设备类型名称\">\n                  <el-input\n                    v-model=\"sbxxForm.zhsblx\"\n                    placeholder=\"请填写组合设备类型名称\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"额定电流\" prop=\"eddl\">\n                  <el-input\n                    v-model=\"sbxxForm.eddl\"\n                    placeholder=\"请填写额定电流\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"运行编号\">\n                  <el-input\n                    v-model=\"sbxxForm.yxbh\"\n                    placeholder=\"请填写运行编号\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"出厂编号\">\n                  <el-input\n                    v-model=\"sbxxForm.ccbh\"\n                    placeholder=\"请填写出厂编号\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"出厂日期\" class=\"add_sy_tyrq\">\n                  <el-date-picker\n                    v-model=\"sbxxForm.ccrq\"\n                    type=\"date\"\n                    placeholder=\"选择日期\"\n                    format=\"yyyy-MM-dd\"\n                    value-format=\"yyyy-MM-dd\"\n                  >\n                  </el-date-picker>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"投运日期\" class=\"add_sy_tyrq\">\n                  <el-date-picker\n                    v-model=\"sbxxForm.tyrq\"\n                    type=\"date\"\n                    placeholder=\"选择日期\"\n                    format=\"yyyy-MM-dd\"\n                    value-format=\"yyyy-MM-dd\"\n                  >\n                  </el-date-picker>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row :gutter=\"20\">\n              <el-col :span=\"24\">\n                <el-form-item label=\"备注\" prop=\"bz\">\n                  <el-input\n                    v-model=\"sbxxForm.bz\"\n                    type=\"textarea\"\n                    rows=\"2\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </el-form>\n        </el-tab-pane>\n        <el-tab-pane label=\"技术参数\" name=\"sbjscs\">\n          <el-form :model=\"jscsForm\" label-width=\"130px\">\n            <el-col :span=\"8\" v-for=\"item in jscsLabelList\">\n              <el-form-item\n                :label=\"\n                  item.dw != '' ? item.label + '(' + item.dw + ')' : item.label\n                \"\n              >\n                <el-input\n                  v-if=\"item.type === 'input'\"\n                  v-model=\"jscsForm[item.jscsbm]\"\n                  placeholder=\"\"\n                >\n                </el-input>\n                <el-select\n                  v-if=\"item.type === 'select'\"\n                  v-model=\"jscsForm[item.jscsbm]\"\n                  placeholder=\"\"\n                >\n                  <el-option\n                    v-for=\"(childItem, key) in item.options\"\n                    :key=\"key\"\n                    :label=\"childItem.label\"\n                    :value=\"childItem.value\"\n                    :disabled=\"childItem.disabled\"\n                    style=\"display: flex; align-items: center;\"\n                    clearable\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-form>\n        </el-tab-pane>\n        <el-tab-pane label=\"设备履历\" name=\"sbRecord\">\n          <el-tabs\n            v-model=\"sbllDescTabName\"\n            @tab-click=\"handleSbllDescTabNameClick\"\n            type=\"card\"\n          >\n            <el-tab-pane label=\"试验记录\" name=\"syjl\">\n              <el-table\n                stripe\n                border\n                v-loading=\"loading\"\n                :data=\"sblvsyjlList\"\n                max-height=\"550\"\n              >\n                <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n                <el-table-column label=\"试验专业\" align=\"center\" prop=\"syzy\" />\n                <el-table-column\n                  label=\"试验性质\"\n                  align=\"center\"\n                  prop=\"syxz\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"试验名称\"\n                  align=\"center\"\n                  prop=\"symc\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"工作地点\"\n                  align=\"center\"\n                  prop=\"gzdd\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"试验设备\"\n                  align=\"center\"\n                  prop=\"sysb\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column label=\"试验报告\" align=\"center\" prop=\"sybg\" />\n                <el-table-column\n                  label=\"天气\"\n                  align=\"center\"\n                  prop=\"tq\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"试验日期\"\n                  align=\"center\"\n                  prop=\"syrq\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"录入人\"\n                  align=\"center\"\n                  prop=\"lrr\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"试验结论\"\n                  align=\"center\"\n                  prop=\"syjl\"\n                  :show-overflow-tooltip=\"true\"\n                />\n              </el-table>\n            </el-tab-pane>\n            <el-tab-pane label=\"缺陷记录\" name=\"qxjl\">\n              <el-table\n                stripe\n                border\n                v-loading=\"loading\"\n                :data=\"sbllqxjlList\"\n                max-height=\"550\"\n              >\n                <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n                <el-table-column label=\"所属公司\" align=\"center\" prop=\"ssgs\" />\n                <el-table-column\n                  label=\"变电站名称\"\n                  align=\"center\"\n                  prop=\"bdzmc\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"设备类型\"\n                  align=\"center\"\n                  prop=\"sblx\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"缺陷性质\"\n                  align=\"center\"\n                  prop=\"qxxz\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"电压等级\"\n                  align=\"center\"\n                  prop=\"dydj\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column label=\"设备型号\" align=\"center\" prop=\"sbxh\" />\n                <el-table-column\n                  label=\"生产厂家\"\n                  align=\"center\"\n                  prop=\"sccj\"\n                  :show-overflow-tooltip=\"true\"\n                />\n              </el-table>\n            </el-tab-pane>\n            <el-tab-pane label=\"状态变更记录\" name=\"ztbgjl\">\n              <comp-table\n                :table-and-page-info=\"resumPageInfo\"\n                @getMethod=\"getResumList\"\n                @update:multipleSelection=\"handleSelectionChange\"\n                height=\"30vh\"\n              />\n            </el-tab-pane>\n          </el-tabs>\n        </el-tab-pane>\n        <el-tab-pane label=\"附属设施\" name=\"fsss\">\n          <el-table\n            stripe\n            border\n            v-loading=\"loading\"\n            :data=\"fsssList\"\n            max-height=\"550\"\n          >\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n            <el-table-column label=\"设备名称\" align=\"center\" prop=\"syzy\" />\n            <el-table-column\n              label=\"设备型号\"\n              align=\"center\"\n              prop=\"syxz\"\n              :show-overflow-tooltip=\"true\"\n            />\n            <el-table-column\n              label=\"生产厂家\"\n              align=\"center\"\n              prop=\"symc\"\n              :show-overflow-tooltip=\"true\"\n            />\n            <el-table-column\n              label=\"出厂日期\"\n              align=\"center\"\n              prop=\"gzdd\"\n              :show-overflow-tooltip=\"true\"\n            />\n            <el-table-column\n              label=\"投运日期\"\n              align=\"center\"\n              prop=\"sysb\"\n              :show-overflow-tooltip=\"true\"\n            />\n            <el-table-column label=\"更换日期\" align=\"center\" prop=\"sybg\" />\n          </el-table>\n        </el-tab-pane>\n      </el-tabs>\n      <div slot=\"footer\" class=\"dialog-footer\" v-show=\"!assetIsDisable\">\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submit\" class=\"pmyBtn\"\n          >确 定</el-button\n        >\n      </div>\n    </el-dialog>\n    <el-dialog\n      title=\"设备状态变更\"\n      :visible.sync=\"dialogVisible\"\n      width=\"30%\"\n      append-to-body\n      v-dialogDrag\n    >\n      <el-form :inline=\"true\" label-width=\"100px\" class=\"qxlr_dialog_insert\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"设备状态:\">\n              <el-select v-model=\"updateList.sbzt\">\n                <el-option\n                  v-for=\"item in sbztOptionsDataList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitStatus\">确 定</el-button>\n      </span>\n    </el-dialog>\n\n    <!--状态变更使用-->\n    <activiti\n      :processData=\"processData\"\n      :isShow=\"isShow\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n    ></activiti>\n    <time-line\n      :value=\"timeLineShow\"\n      :timeData=\"timeData\"\n      @closeTimeLine=\"colseTimeLine\"\n    />\n  </div>\n</template>\n\n<script>\nimport {\n  addAsset,\n  addBdz,\n  addJg,\n  getAssetListInfo,\n  getJgInfoList,\n  getTreeInfo,\n  removeAsset,\n  removeBdz,\n  removeJg,\n  getOrganizationSelected,\n  getNewTreeInfo,\n  getBdAsesetListPage,\n  getBdzDataListSelected,\n  getJgDataListSelected,\n  getSblxDataListSelected,\n  updateStatus\n} from \"@/api/dagangOilfield/asset/bdsbtz\";\nimport { getResumDataList } from \"@/api/dagangOilfield/asset/sdsb\";\nimport TechnicalParameter from \"@/views/dagangOilfield/bzgl/sbbzk/technicalParameter\";\nimport {\n  getParamDataList,\n  getParamsValue,\n  saveParamValue\n} from \"@/api/dagangOilfield/asset/parameters\";\nimport { saveOrUpdate } from \"@/api/yxgl/bdyxgl/qxgl\";\n//流程\nimport activiti from \"com/activiti\";\nimport timeLine from \"com/timeLine\";\nimport { HistoryList } from \"@/api/activiti/processTask\";\n\nexport default {\n  name: \"qxbzk\",\n  components: { TechnicalParameter, activiti, timeLine },\n  data() {\n    return {\n      //流程参数\n      processData: {\n        processDefinitionKey: \"dwzyztbg\",\n        businessKey: \"\",\n        businessType: \"变电设备台账\",\n        variables: {},\n        nextUser: \"\",\n        defaultFrom: true,\n        processType: \"complete\"\n      },\n      //流程变更申请标题\n      activitiOption: { title: \"状态变更申请\" },\n      //流程跟踪数据\n      timeData: [],\n      //流程跟踪显示隐藏\n      timeLineShow: false,\n      //流程组件显示隐藏\n      isShow: false,\n\n      resumeQuery: {\n        foreignNum: undefined,\n        sblx: undefined\n      },\n      updateList: {\n        sbzt: \"\",\n        objId: \"\"\n      },\n      dialogVisible: false,\n      //设备删除选择列\n      selectRows: [],\n      //设备类型下拉框数据\n      sblxOptionsDataSelected: [],\n      //查询变电站下拉框数据的参数\n      selectBdzOptionsParam: {},\n      //查询间隔下拉框数据的参数\n      selectJgOptionsParam: {},\n      //设备表单\n      sbxxForm: {},\n      //设备附属设施list\n      fsssList: [],\n      //变电设备查询参数\n      bdzqueryParams: {\n        ssgs: undefined,\n        dydjbm: undefined,\n        ssbdz: undefined,\n        ssjg: undefined,\n        sbmc: undefined,\n        assetTypeCode: undefined,\n        sbzt: undefined,\n        pageNum: 1,\n        pageSize: 10\n      },\n      //新增设备时设备状态下拉框数据\n      sbztOptionsDataList: [\n        { label: \"在运\", value: \"在运\" },\n        { label: \"停运\", value: \"停运\" },\n        { label: \"报废\", value: \"报废\" }\n      ],\n      //新增设备时所属间隔下拉框列表\n      jgOptionsDataList: [],\n      //新增设备时电压等级下拉框数据\n      dydjOptionsDataList: [\n        { label: \"110kV\", value: \"110\" },\n        { label: \"35kV\", value: \"35\" },\n        { label: \"10kV\", value: \"10\" },\n        { label: \"6kV\", value: \"6\" }\n      ],\n      //新增设备时变电站下拉框\n      bdzOptionsDataList: [],\n      //变电设备列表数据\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"deptname\", label: \"所属公司\", minWidth: \"120\" },\n          { prop: \"bdzmc\", label: \"变电站名称\", minWidth: \"180\" },\n          { prop: \"wzmc\", label: \"所属间隔\", minWidth: \"180\" },\n          { prop: \"sblxmc\", label: \"设备类型\", minWidth: \"180\" },\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"180\" },\n          { prop: \"dydjName\", label: \"电压等级\", minWidth: \"120\" },\n          { prop: \"sbzt\", label: \"设备状态\", minWidth: \"250\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"120\" },\n          { prop: \"ggxh\", label: \"规格型号\", minWidth: \"120\" },\n          { prop: \"eddy\", label: \"额定电压\", minWidth: \"120\" },\n          { prop: \"eddl\", label: \"额定电流\", minWidth: \"120\" },\n          { prop: \"edpl\", label: \"额定频率\", minWidth: \"120\" },\n          { prop: \"sccj\", label: \"生产厂家\", minWidth: \"120\" }\n          /* {\n              fixed: \"right\",\n              prop: 'operation',\n              label: '操作',\n              minWidth: '120px',\n              style: {display: 'block'},\n              operation: [\n                /!*{name: \"状态变更\", clickFun: this.updateStatus},\n                {name: \"流程查看\", clickFun: this.ztbglcSay},*!/\n                {name: '修改', clickFun: this.updateAsset},\n                {name: '详情', clickFun: this.assetDetails},\n              ]\n            },*/\n        ]\n      },\n      resumPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"foreignNum\", label: \"设备名称\", minWidth: \"120\" },\n          { prop: \"sblx\", label: \"设备类型\", minWidth: \"180\" },\n          { prop: \"bglx\", label: \"变更类型\", minWidth: \"120\" },\n          { prop: \"ms\", label: \"描述\", minWidth: \"250\" },\n          { prop: \"bgr\", label: \"变更人\", minWidth: \"140\" },\n          { prop: \"bgsj\", label: \"变更时间\", minWidth: \"140\" }\n        ]\n      },\n      //变电设备筛选条件\n      filterInfo1: {\n        data: {\n          sbmc: \"\",\n          assetTypeCode: \"\",\n          sbzt: \"\"\n        },\n        fieldList: [\n          { label: \"设备名称\", type: \"input\", value: \"sbmc\" },\n          {\n            label: \"设备类型\",\n            type: \"select\",\n            value: \"assetTypeCode\",\n            options: [\n              { label: \"干式站用变\", value: \"02\" },\n              { label: \"sf6断路器\", value: \"03\" },\n              { label: \"真空断路器\", value: \"04\" }\n            ]\n          },\n          {\n            label: \"投运状态\",\n            type: \"select\",\n            value: \"sbzt\",\n            options: [\n              { label: \"在运\", value: \"在运\" },\n              { label: \"停运\", value: \"停运\" }\n            ]\n          }\n        ]\n      },\n      //树结构监听属性\n      filterText: \"\",\n      //组织结构下拉数据\n      OrganizationSelectedList: [],\n      //树结构上面得筛选框参数\n      treeForm: {},\n      //电压等级下拉框数据\n      VoltageLevelSelectedList: [\n        { label: \"110kV\", value: \"110\" },\n        { label: \"35kV\", value: \"35\" },\n        { label: \"10kV\", value: \"10\" },\n        { label: \"6kV\", value: \"6\" }\n      ],\n      //设备信息展示\n      assetIsDisable: false,\n      //技术参数动态展示集合\n      jscsLabelList: [],\n      //技术参数绑定\n      jscsForm: {},\n      //设备上面展示的按钮(新增、删除)\n      isShow3: true,\n      //设备履历状态变更记录\n      sbllztbgjlList: [\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          bglx: \"状态变更\",\n          desc: \"状态变更为报废\",\n          bgsj: \"2022-12-12\",\n          bgr: \"张三\"\n        },\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          bglx: \"状态变更\",\n          desc: \"状态变更为报废\",\n          bgsj: \"2022-12-12\",\n          bgr: \"张三\"\n        },\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          bglx: \"状态变更\",\n          desc: \"状态变更为报废\",\n          bgsj: \"2022-12-12\",\n          bgr: \"张三\"\n        },\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          bglx: \"状态变更\",\n          desc: \"状态变更为报废\",\n          bgsj: \"2022-12-12\",\n          bgr: \"张三\"\n        },\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          bglx: \"状态变更\",\n          desc: \"状态变更为报废\",\n          bgsj: \"2022-12-12\",\n          bgr: \"张三\"\n        }\n      ],\n      //设备履历缺陷记录数据集合\n      sbllqxjlList: [\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          qxxz: \"严重\",\n          dydj: \"35kV\",\n          sbxh: \"XXX型号\",\n          sccj: \"XXX厂家\"\n        },\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          qxxz: \"严重\",\n          dydj: \"35kV\",\n          sbxh: \"XXX型号\",\n          sccj: \"XXX厂家\"\n        },\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          qxxz: \"严重\",\n          dydj: \"35kV\",\n          sbxh: \"XXX型号\",\n          sccj: \"XXX厂家\"\n        },\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          qxxz: \"严重\",\n          dydj: \"35kV\",\n          sbxh: \"XXX型号\",\n          sccj: \"XXX厂家\"\n        },\n        {\n          ssgs: \"港东分公司\",\n          dzmc: \"1号变电站\",\n          sblx: \"主变压器\",\n          qxxz: \"严重\",\n          dydj: \"35kV\",\n          sbxh: \"XXX型号\",\n          sccj: \"XXX厂家\"\n        }\n      ],\n      //设备履历试验记录数据\n      sblvsyjlList: [\n        {\n          syzy: \"带电\",\n          syxz: \"例行试验\",\n          symc: \"XXXXX\",\n          gzdd: \"XXX平台\",\n          sysb: \"主变压器\",\n          sybg: \"\",\n          tq: \"晴\",\n          syrq: \"2022-01-01\",\n          lrr: \"张三\",\n          syjl: \"XXXXX\"\n        },\n        {\n          syzy: \"带电\",\n          syxz: \"例行试验\",\n          symc: \"XXXXX\",\n          gzdd: \"XXX平台\",\n          sysb: \"主变压器\",\n          sybg: \"\",\n          tq: \"晴\",\n          syrq: \"2022-01-01\",\n          lrr: \"张三\",\n          syjl: \"XXXXX\"\n        },\n        {\n          syzy: \"带电\",\n          syxz: \"例行试验\",\n          symc: \"XXXXX\",\n          gzdd: \"XXX平台\",\n          sysb: \"主变压器\",\n          sybg: \"\",\n          tq: \"晴\",\n          syrq: \"2022-01-01\",\n          lrr: \"张三\",\n          syjl: \"XXXXX\"\n        },\n        {\n          syzy: \"带电\",\n          syxz: \"例行试验\",\n          symc: \"XXXXX\",\n          gzdd: \"XXX平台\",\n          sysb: \"主变压器\",\n          sybg: \"\",\n          tq: \"晴\",\n          syrq: \"2022-01-01\",\n          lrr: \"张三\",\n          syjl: \"XXXXX\"\n        },\n        {\n          syzy: \"带电\",\n          syxz: \"例行试验\",\n          symc: \"XXXXX\",\n          gzdd: \"XXX平台\",\n          sysb: \"主变压器\",\n          sybg: \"\",\n          tq: \"晴\",\n          syrq: \"2022-01-01\",\n          lrr: \"张三\",\n          syjl: \"XXXXX\"\n        },\n        {\n          syzy: \"带电\",\n          syxz: \"例行试验\",\n          symc: \"XXXXX\",\n          gzdd: \"XXX平台\",\n          sysb: \"主变压器\",\n          sybg: \"\",\n          tq: \"晴\",\n          syrq: \"2022-01-01\",\n          lrr: \"张三\",\n          syjl: \"XXXXX\"\n        }\n      ],\n      //设备履历tab页\n      sbllDescTabName: \"syjl\",\n      //轮播图片\n      imgList: [\n        {\n          url:\n            \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\"\n        },\n        {\n          url:\n            \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\"\n        },\n        {\n          url:\n            \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\"\n        },\n        {\n          url:\n            \"https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg\"\n        }\n      ],\n      //组织树\n      treeOptions: [],\n      //设备详情页底部确认取消按钮控制\n      sbCommitDialogCotrol: true,\n      //弹出框tab页\n      activeTabName: \"sbDesc\",\n      //变电站展示\n      bdzShowTable: true,\n      //间隔展示\n      jgShowTable: false,\n      //设备展示\n      sbShowTable: false,\n      //设备弹出框\n      dialogFormVisible: false,\n      //变电站添加按钮弹出框\n      bdzDialogFormVisible: false,\n      //间隔添加按钮弹出框\n      jgDialogFormVisible: false,\n      //弹出框表单\n      form: {},\n      loading: false,\n      paramQuery: {\n        sblxbm: undefined\n      },\n      rules: {\n        ssbdz: [\n          { required: true, message: \"请选择所属变电站\", trigger: \"change\" }\n        ],\n        sbdm: [{ required: true, message: \"请填写设备代码\", trigger: \"blur\" }],\n        sbmc: [{ required: true, message: \"请填写设备名称\", trigger: \"blur\" }],\n        dydjbm: [\n          { required: true, message: \"请选择电压等级\", trigger: \"change\" }\n        ],\n        sbzt: [\n          { required: true, message: \"请选择设备状态\", trigger: \"change\" }\n        ],\n        assetTypeCode: [\n          { required: true, message: \"请选择设备类型\", trigger: \"change\" }\n        ],\n        ssjg: [\n          { required: true, message: \"请选择所属间隔\", trigger: \"change\" }\n        ],\n        ssgs: [{ required: true, message: \"请选择所属公司\", trigger: \"change\" }]\n      },\n\n      //审批按钮\n      spButtonDisabled: true\n    };\n  },\n  watch: {\n    //监听筛选框值发生变化进而筛选树结构\n    filterText(val) {\n      this.$refs.tree.filter(val);\n    }\n  },\n  created() {\n    //获取组织结构下拉数据\n    this.getOrganizationSelected();\n    //获取新的设备拓扑树\n    this.getNewTreeInfo();\n    //初始化加载时加载所有变电站下面的设备信息\n    this.getData();\n    //获取设备类型下拉框数据\n    this.getSblxDataListSelected();\n  },\n  mounted() {\n    //获取变电站下拉框数据\n    this.getBdzDataListSelected();\n  },\n  methods: {\n    //审批\n    spZtbgjl() {\n      let row = this.selectRows[0];\n      this.activitiOption.title = \"流程提交\";\n      this.processData.defaultFrom = false;\n      this.processData.processType = \"complete\";\n      this.processData.businessKey = row.objId;\n      this.processData.variables.pass = true;\n      this.isShow = true;\n    },\n    //流程查看\n    async ztbglcSay(row) {\n      this.processData.businessKey = row.objId;\n      let { code, data } = await HistoryList(this.processData);\n      this.timeData = data;\n      this.timeLineShow = true;\n    },\n    //流程回调\n    async todoResult(data) {\n      console.log(\"流程回调方法：\", data);\n      switch (data.activeTaskName) {\n        case \"结束\":\n          updateStatus(data.variables.updateList).then(res => {\n            if (res.code == \"0000\") {\n              this.$message.success(\"设备状态已变更！\");\n              this.dialogVisible = false;\n              this.getData();\n            }\n          });\n          break;\n      }\n    },\n    //上报发送办结\n    getSbFsBj(args) {\n      console.log(args);\n      let row = { ...args.data };\n      if (args.type === \"complete\") {\n        this.activitiOption.title = \"人员选择\";\n        this.processData.defaultFrom = true;\n        this.processData.processType = \"complete\";\n        this.processData.businessKey = row.objId;\n        this.processData.variables.pass = true;\n        this.processData.variables.updateList = row;\n      } else {\n        this.activitiOption.title = \"回退原因提报\";\n        this.processData.defaultFrom = true;\n        this.processData.processType = \"rollback\";\n        this.processData.businessKey = row.objId;\n        this.processData.variables.pass = true;\n      }\n      this.isShow = true;\n    },\n    //关闭流程组件\n    closeActiviti() {\n      this.isShow = false;\n    },\n    //关闭流程查看页面\n    colseTimeLine() {\n      this.timeLineShow = false;\n    },\n    /**\n     * 设备表格多选框\n     */\n    handleSelectionChange(rows) {\n      this.selectRows = rows;\n\n      //审批按钮\n      this.spButtonDisabled = rows.length != 1;\n    },\n    /**\n     * 获取设备类型下拉框数据\n     */\n    getSblxDataListSelected() {\n      let sblxParam = {\n        type: \"变电设备\"\n      };\n      getSblxDataListSelected(sblxParam).then(res => {\n        this.sblxOptionsDataSelected = res.data;\n      });\n    },\n\n    /**\n     * 删除设备信息\n     */\n    removeAsset() {\n      if (this.selectRows.length < 1) {\n        this.$message.warning(\"请选择正确的数据！！！\");\n        return;\n      }\n      let ids = this.selectRows.map(item => {\n        return item.objId;\n      });\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          removeAsset(ids).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n      this.tableAndPageInfo.pager.pageResize = \"Y\";\n      this.getData();\n    },\n    //设备添加按钮\n    sbAddSensorButton() {\n      //清空表单\n      // this.sbxxForm = {};\n      // this.jscsForm={};\n      //\n      // let row={};\n      // this.technicalParameters(row);\n      //打开弹出框\n      this.dialogFormVisible = true;\n      //按钮和表单是否可编辑控制\n      this.assetIsDisable = false;\n    },\n    //变电站下拉框中的change事件\n    bdzOptionsChangeClick() {\n      //当发生change事件时先清空之前的间隔信息\n      this.$set(this.sbxxForm, \"ssjg\", \"\");\n      //调用查询间隔方法\n      this.getJgDataListSelected();\n    },\n    //获取变电站下拉框数据\n    getBdzDataListSelected() {\n      getBdzDataListSelected(this.selectBdzOptionsParam).then(res => {\n        this.bdzOptionsDataList = res.data;\n      });\n      //调用查询间隔方法\n      // this.getJgDataListSelected();\n    },\n    //获取间隔下拉框数据\n    getJgDataListSelected() {\n      //给获取间隔下拉框查询参数赋值\n      this.selectJgOptionsParam.ssbdz = this.sbxxForm.ssbdz;\n      getJgDataListSelected(this.selectJgOptionsParam).then(res => {\n        this.jgOptionsDataList = res.data;\n      });\n    },\n    updateStatus(row) {\n      this.updateList.sbzt = row.sbzt;\n      (this.updateList.objId = row.objId), (this.dialogVisible = true);\n    },\n    submitStatus() {\n      this.$confirm(\"确认将设备状态修改为\" + this.updateList.sbzt + \"?\", \"\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(res => {\n        //关闭状态变更弹窗\n        this.dialogVisible = false;\n        //打开人员选择弹窗\n        this.isShow = true;\n        this.getSbFsBj({ data: this.updateList, type: \"complete\" });\n        // updateStatus(this.updateList).then(res=>{\n        //   if(res.code==\"0000\"){\n        //     this.$message.success(\"设备状态已变更！\")\n        //     this.dialogVisible=false;\n        //     this.getData();\n        //   }\n        // })\n      });\n    },\n\n    /**\n     * 设备履历\n     */\n    getResumList(par) {\n      let param = { ...par, ...this.resumeQuery };\n      getResumDataList(param).then(res => {\n        this.resumPageInfo.tableData = res.data.records;\n        this.resumPageInfo.pager.total = res.data.total;\n      });\n    },\n\n    //设备修改操作\n    updateAsset(row) {\n      this.technicalParameters(row);\n      this.paramQuery.sblxbm = row.assetTypeCode;\n      this.getParameters();\n      this.resumeQuery.foreignNum = row.sbmc;\n      this.resumeQuery.sblx = row.sblx;\n      this.getResumList();\n      //打开设备弹出框\n      this.dialogFormVisible = true;\n      //给表单赋值\n      this.sbxxForm = row;\n      //按钮和表单是否可编辑控制\n      this.assetIsDisable = false;\n    },\n    //设备详情操作\n    assetDetails(row) {\n      this.technicalParameters(row);\n\n      this.resumeQuery.foreignNum = row.sbmc;\n      this.resumeQuery.sblx = row.sblx;\n      this.getResumList();\n      //打开设备弹出框\n      this.dialogFormVisible = true;\n      //给表单赋值\n      this.sbxxForm = row;\n      //按钮和表单是否可编辑控制\n      this.assetIsDisable = true;\n    },\n    //设备履历tab页点击事件\n    handleSbllDescTabNameClick(tab, event) {\n      console.log(tab, event);\n    },\n    //筛选条件重置\n    filterReset() {},\n    //变电设备台账查询\n    async getData(params) {\n      try {\n        const param = { ...this.bdzqueryParams, ...params };\n        const { data, code } = await getBdAsesetListPage(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          console.log(\"data:设备数据\", data.records);\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //树监听事件\n    filterNode(value, data) {\n      if (!value) return true;\n      return data.label.indexOf(value) !== -1;\n    },\n    //获取新的设备拓扑树\n    getNewTreeInfo() {\n      //给查询设备参数赋值\n      this.bdzqueryParams.ssgs = this.treeForm.ssdwbm;\n      this.bdzqueryParams.dydjbm = this.treeForm.dydjbm;\n      getNewTreeInfo(this.treeForm).then(res => {\n        console.log(\"s树结构数据:\", res);\n        this.treeOptions = res.data;\n      });\n      //获取设备方法\n      this.getData();\n    },\n    //获取组织结构下拉框数据\n    getOrganizationSelected() {\n      let parentId = \"1001\";\n      getOrganizationSelected({ parentId: parentId }).then(res => {\n        this.OrganizationSelectedList = res.data;\n        console.log(this.OrganizationSelectedList);\n      });\n    },\n    //保存设备信息\n    submit() {\n      this.$refs[\"sbxxForm\"].validate(valid => {\n        if (valid) {\n          this.addAsset();\n          //保存技术参数信息\n          // this.submitParameter();\n        } else {\n          return false;\n        }\n      });\n    },\n    /**\n     * 添加设备保存基本信息\n     */\n    addAsset() {\n      this.sbxxForm.sbClassCsValue = this.jscsForm;\n      addAsset(this.sbxxForm).then(res => {\n        if (res.code == \"0000\") {\n          this.$message({\n            type: \"success\",\n            message: \"操作成功!\"\n          });\n          this.dialogFormVisible = false;\n          this.tableAndPageInfo.pager.pageResize = \"Y\";\n          this.getData();\n        }\n      });\n    },\n    //保存技术参数\n    submitParameter() {\n      saveParamValue(this.jscsForm).then(res => {\n        this.dialogFormVisible = false;\n      });\n    },\n    //设备类型change事件。获取技术参数信息\n    showParams(data) {\n      this.paramQuery.sblxbm = data;\n      this.getParameters();\n    },\n    //获取技术参数对照信息，展示对应得技术参数label信息\n    async getParameters() {\n      this.jscsLabelList = [];\n      getParamDataList(this.paramQuery).then(res => {\n        console.log(\"技术参数对照信息\", res);\n        this.jscsLabelList = res.data;\n        this.getParamValue();\n      });\n    },\n    //获取技术参数值信息\n    getParamValue() {\n      getParamsValue(this.jscsForm).then(res => {\n        if (res.data != \"\") {\n          this.jscsForm = { ...res.data[0] };\n          console.log(\"技术参数值信息\", this.jscsForm);\n        }\n      });\n    },\n    //点击新增，修改，详情时，重新获取对应的技术参数信息\n    technicalParameters(row) {\n      this.jscsForm = {};\n      this.paramQuery.sblxbm = row.assetTypeCode;\n      this.jscsForm.sblxbm = row.assetTypeCode;\n      this.jscsForm.sbbm = row.objId;\n      this.getParameters();\n    },\n\n    //树点击事件\n    handleNodeClick(data, e) {\n      if (data.identifier == \"0\") {\n        //点击父节点时查询当前所有设备\n        //清空查询参数\n        this.bdzqueryParams = {};\n        //获取变电站数据\n        this.getData();\n      } else if (data.identifier == \"1\") {\n        //点击第二层级的展示设备\n        this.bdzqueryParams.ssjg = \"\";\n        this.bdzqueryParams.ssbdz = data.id;\n        //获取变电站数据\n        this.getData();\n      } else if (data.identifier == \"2\") {\n        //点击第二层级的展示设备\n        this.bdzqueryParams.ssbdz = \"\";\n        this.bdzqueryParams.ssjg = data.id;\n        //获取变电站数据\n        this.getData();\n      }\n    },\n\n    //清空表单\n    resetForm() {\n      this.sbxxForm = this.$options.data().form;\n      this.jscsForm = this.$options.data().form;\n      this.$nextTick(function() {\n        this.$refs[\"sbxxForm\"].clearValidate();\n      });\n      this.dialogFormVisible = false;\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  height: 76vh;\n  overflow: auto;\n}\n\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.box-cardList {\n  height: 70%;\n}\n\n.item {\n  width: 200px;\n  float: left;\n}\n\n#main_container_dj {\n  height: calc(100vh - 84px);\n}\n\n.aside_height {\n  height: 96%;\n}\n\n.defect .el-form-item:nth-child(odd) {\n  margin-right: 70px;\n}\n\n/*背景颜色调整*/\n#main_container_dj,\n#main_container_dj .el-aside {\n  background-color: #b4caf1;\n}\n\n///deep/ .qxlr_dialog_insert .el-dialog__header {\n//  background-color: #8eb3f5;\n//}\n\n/* /deep/ .pmyBtn {\n    background: #8eb3f5;\n  }*/\n\n/*/deep/ .add_sy_tyrq .el-input--medium .el-input__inner {*/\n/*  width: 200px;*/\n/*}*/\n\n/*添加弹出框得宽度*/\n/*/deep/ .el-input--medium .el-input__inner {*/\n/*  width: 200px;*/\n/*}*/\n.el-select {\n  width: 100%;\n}\n\n.el-date-editor {\n  width: 100%;\n}\n\n.el-carousel__item h3 {\n  color: #475669;\n  font-size: 14px;\n  opacity: 0.75;\n  line-height: 150px;\n}\n\n.el-carousel__item:nth-child(2n) {\n  background-color: #99a9bf;\n}\n\n.el-carousel__item:nth-child(2n + 1) {\n  background-color: #d3dce6;\n}\n\n/* 设置滚动条的样式 */\n::-webkit-scrollbar {\n  width: 12px;\n}\n\n/* 滚动槽 */\n::-webkit-scrollbar-track {\n  //-webkit-box-shadow:inset006pxrgba(0,0,0,0.3);\n  border-radius: 10px;\n}\n\n/* 滚动条滑块 */\n::-webkit-scrollbar-thumb {\n  border-radius: 10px;\n  background: rgba(0, 0, 0, 0.1);\n  //-webkit-box-shadow:gba(0,0,0,0.5);\n}\n\n::-webkit-scrollbar-thumb:window-inactive {\n  background: rgba(0, 0, 0, 0.1);\n}\n\n[data-v-67a974b1]::-webkit-scrollbar {\n  width: 8px;\n}\n\n.item {\n  width: 225px;\n  float: left;\n}\n/deep/.box-card {\n  margin: 0 6px;\n}\n//有子节点 且未展开\n.el-tree ::v-deep .el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/add.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n//有子节点 且已展开\n.el-tree\n  ::v-deep\n  .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/prep.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n//没有子节点\n.el-tree ::v-deep .el-tree-node__expand-icon.is-leaf::before {\n  background: transparent;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n</style>\n"], "sourceRoot": "src/views/dagangOilfield/dwzygl/bdgl"}]}