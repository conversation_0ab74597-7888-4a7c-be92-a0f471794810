{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\App.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\App.vue", "mtime": 1735911653806}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKCnZhciBfanF1ZXJ5ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJqcXVlcnkiKSk7CgovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSB7CiAgbmFtZTogIkFwcCIsCiAgcHJvdmlkZTogZnVuY3Rpb24gcHJvdmlkZSgpIHsKICAgIC8v54i257uE5Lu25Lit6YCa6L+HcHJvdmlkZeadpeaPkOS+m+WPmOmHj++8jOWcqOWtkOe7hOS7tuS4remAmui/h2luamVjdOadpeazqOWFpeWPmOmHjwogICAgcmV0dXJuIHsKICAgICAgcmVsb2FkOiB0aGlzLnJlbG9hZAogICAgfTsKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBpc1Nob3c6IHRydWUgLy/mjqfliLbop4blm77mmK/lkKbmmL7npLrnmoTlj5jph48KCiAgICB9OwogIH0sCiAgbWV0aG9kczogewogICAgcmVsb2FkOiBmdW5jdGlvbiByZWxvYWQoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CgogICAgICB0aGlzLmlzU2hvdyA9IGZhbHNlOyAvL+WFiOWFs+mXrQoKICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzLmlzU2hvdyA9IHRydWU7IC8v5YaN5omT5byACiAgICAgIH0pOwogICAgfQogIH0KfTsKZXhwb3J0cy5kZWZhdWx0ID0gX2RlZmF1bHQ7CnZhciBub3dUaW1lID0gbmV3IERhdGUoKS5nZXRUaW1lKCk7IC8v57uT5p2f5pe26Ze0Cgp2YXIgZW5kVGltZSA9IG5ldyBEYXRlKCIyMDIyLzEyLzAzIDAwOjAwOjAwIikuZ2V0VGltZSgpOyAvLyBpZihub3dUaW1lIDwgZW5kVGltZSl7Ci8v5re75Yqg5YWo5bGA54Gw6Imy5ruk6ZWcCi8vICQoImh0bWwiKS5jc3MoewovLyAgICctd2Via2l0LWZpbHRlcic6ICdncmF5c2NhbGUoMTAwJSknLAovLyAgICctbW96LWZpbHRlcic6ICdncmF5c2NhbGUoMTAwJSknLAovLyAgICctbXMtZmlsdGVyJzogJ2dyYXlzY2FsZSgxMDAlKScsCi8vICAgJy1vLWZpbHRlcic6ICdncmF5c2NhbGUoMTAwJSknLAovLyAgIC8vIGll5ruk6ZWcCi8vICAgJ2ZpbHRlcic6ICdwcm9naWQ6RFhJbWFnZVRyYW5zZm9ybS5NaWNyb3NvZnQuQmFzaWNJbWFnZShncmF5c2NhbGU9MSknLAovLyAgIC8vIGllNiDnrYnkvY7niYjmnKzmtY/op4jlmajkuI3pnIDopoHliqDmu6TplZwKLy8gICAnX2ZpbHRlcic6ICdub25lJwovLyB9KQovLyB9"}, {"version": 3, "sources": ["App.vue"], "names": [], "mappings": ";;;;;;;;;AAOA;;;;;;;;eACA;AACA,EAAA,IAAA,EAAA,KADA;AAEA,EAAA,OAFA,qBAEA;AACA;AACA,WAAA;AACA,MAAA,MAAA,EAAA,KAAA;AADA,KAAA;AAGA,GAPA;AAQA,EAAA,IARA,kBAQA;AACA,WAAA;AACA,MAAA,MAAA,EAAA,IADA,CACA;;AADA,KAAA;AAGA,GAZA;AAaA,EAAA,OAAA,EAAA;AACA,IAAA,MADA,oBACA;AAAA;;AACA,WAAA,MAAA,GAAA,KAAA,CADA,CACA;;AACA,WAAA,SAAA,CAAA,YAAA;AACA,QAAA,KAAA,CAAA,MAAA,GAAA,IAAA,CADA,CACA;AACA,OAFA;AAGA;AANA;AAbA,C;;AAuBA,IAAA,OAAA,GAAA,IAAA,IAAA,GAAA,OAAA,EAAA,C,CACA;;AACA,IAAA,OAAA,GAAA,IAAA,IAAA,CAAA,qBAAA,EAAA,OAAA,EAAA,C,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sourcesContent": ["<template>\n  <div id=\"app\">\n    <router-view v-if=\"isShow\" />\n  </div>\n</template>\n\n<script>\nimport $ from \"jquery\";\nexport default {\n  name: \"App\",\n  provide() {\n    //父组件中通过provide来提供变量，在子组件中通过inject来注入变量\n    return {\n      reload: this.reload\n    };\n  },\n  data() {\n    return {\n      isShow: true //控制视图是否显示的变量\n    };\n  },\n  methods: {\n    reload() {\n      this.isShow = false; //先关闭\n      this.$nextTick(() => {\n        this.isShow = true; //再打开\n      });\n    }\n  },\n};\n\nvar nowTime = new Date().getTime();\n//结束时间\nvar endTime = new Date(\"2022/12/03 00:00:00\").getTime();\n\n// if(nowTime < endTime){\n//添加全局灰色滤镜\n// $(\"html\").css({\n//   '-webkit-filter': 'grayscale(100%)',\n//   '-moz-filter': 'grayscale(100%)',\n//   '-ms-filter': 'grayscale(100%)',\n//   '-o-filter': 'grayscale(100%)',\n//   // ie滤镜\n//   'filter': 'progid:DXImageTransform.Microsoft.BasicImage(grayscale=1)',\n//   // ie6 等低版本浏览器不需要加滤镜\n//   '_filter': 'none'\n// })\n// }\n</script>\n"], "sourceRoot": "src"}]}