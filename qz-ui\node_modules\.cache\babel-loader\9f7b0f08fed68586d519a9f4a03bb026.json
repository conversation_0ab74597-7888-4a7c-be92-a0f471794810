{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\gqjgl\\gfgqj.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\gqjgl\\gfgqj.vue", "mtime": 1752489170836}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["gfgqj.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwNA;;AACA;;AAaA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;AACA,EAAA,UAAA,EAAA;AAAA,IAAA,SAAA,EAAA,kBAAA;AAAA,IAAA,QAAA,EAAA;AAAA,GADA;AAEA,EAAA,IAAA,EAAA,OAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA,MAAA,OAAA,EAAA,EADA;AAEA,MAAA,QAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,IAFA;AAGA,MAAA,MAAA,EAAA;AACA,QAAA,IAAA,EAAA;AADA,OAHA;AAMA,MAAA,UAAA,EAAA,EANA;AAOA,MAAA,OAAA,EAAA,EAPA;AAQA;AACA,MAAA,wBAAA,EAAA,EATA;AAUA;AACA,MAAA,KAAA,EAAA;AACA,QAAA,EAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CADA;AAEA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAFA;AAGA;AACA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAJA;AAKA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CALA;AAMA,QAAA,GAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AANA,OAXA;AAmBA;AACA,MAAA,UAAA,EAAA,KApBA;AAqBA;AACA,MAAA,QAAA,EAAA,OAtBA;AAuBA;AACA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SARA,CASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAnBA;AAZA,OAxBA;AA0DA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,GAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA,EAFA;AAGA,UAAA,IAAA,EAAA,EAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,KADA;AAEA,UAAA,KAAA,EAAA,MAFA;AAGA,UAAA,IAAA,EAAA,QAHA;AAIA,UAAA,OAAA,EAAA,EAJA;AAKA,UAAA,SAAA,EAAA;AALA,SADA,EAQA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,KAFA;AAGA,UAAA,IAAA,EAAA,QAHA;AAIA,UAAA,OAAA,EAAA,EAJA;AAKA,UAAA,SAAA,EAAA;AALA,SARA,EAeA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAfA,EAgBA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAhBA,EAiBA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAjBA,CAkBA;AAlBA;AAPA,OA3DA;AAuFA;AACA,MAAA,oBAAA,EAAA,KAxFA;AAyFA;AACA,MAAA,2BAAA,EAAA,KA1FA;AA2FA;AACA,MAAA,iBAAA,EAAA,KA5FA;AA6FA;AACA,MAAA,IAAA,EAAA,EA9FA;AA+FA,MAAA,OAAA,EAAA,EA/FA;AAgGA;AACA,MAAA,qBAAA,EAAA,KAjGA;AAkGA;AACA,MAAA,wBAAA,EAAA,KAnGA;AAoGA;AACA,MAAA,IAAA,EAAA;AACA,QAAA,IAAA,EAAA;AADA,OArGA;AAwGA,MAAA,OAAA,EAAA,KAxGA;AAyGA;AACA,MAAA,SAAA,EAAA,EA1GA;AA2GA;AACA,MAAA,SAAA,EAAA,EA5GA;AA6GA;AACA,MAAA,cAAA,EAAA,IA9GA;AA+GA,MAAA,UAAA,EAAA,IA/GA;AAgHA;AACA,MAAA,UAAA,EAAA,EAjHA;AAkHA;AACA,MAAA,iBAAA,EAAA;AACA,QAAA,UAAA,EAAA;AADA,OAnHA;AAsHA;AACA,MAAA,eAAA,EAAA,EAvHA;AAyHA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,KAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA,CAFA;AAGA,QAAA,QAAA,EAAA,EAHA;AAIA,QAAA,OAAA,EAAA;AAJA,OA1HA;AAiIA;AACA,MAAA,MAAA,EAAA;AACA,QAAA,EAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,MAAA,EAAA,EAHA;AAIA,QAAA,QAAA,EAAA,EAJA;AAKA,QAAA,MAAA,EAAA,EALA;AAMA,QAAA,QAAA,EAAA,EANA;AAOA,QAAA,IAAA,EAAA,EAPA;AAQA,QAAA,QAAA,EAAA,EARA;AASA,QAAA,QAAA,EAAA,EATA;AAUA,QAAA,MAAA,EAAA;AAVA,OAlIA;AA+IA,MAAA,UAAA,EAAA,KA/IA;AAiJA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,KAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA,CAFA;AAGA,QAAA,QAAA,EAAA,EAHA;AAIA,QAAA,OAAA,EAAA;AAJA,OAlJA;AAwJA;AACA,MAAA,MAAA,EAAA;AACA,QAAA,EAAA,EAAA,EADA;AAEA,QAAA,MAAA,EAAA,EAFA;AAGA,QAAA,QAAA,EAAA,EAHA;AAIA,QAAA,MAAA,EAAA,EAJA;AAKA,QAAA,QAAA,EAAA,EALA;AAMA,QAAA,IAAA,EAAA,EANA;AAOA,QAAA,IAAA,EAAA,EAPA;AAQA,QAAA,MAAA,EAAA,EARA;AASA,QAAA,KAAA,EAAA;AATA,OAzJA;AAqKA;AACA,MAAA,WAAA,EAAA,EAtKA;AAuKA;AACA,MAAA,SAAA,EAAA,KAxKA;AAyKA;AACA,MAAA,YAAA,EAAA,EA1KA;AA2KA;AACA,MAAA,SAAA,EAAA,KA5KA;AA6KA;AACA,MAAA,YAAA,EAAA;AA9KA,KAAA;AAgLA,GApLA;AAqLA,EAAA,KAAA,EAAA,EArLA;AAsLA,EAAA,OAtLA,qBAsLA;AACA;AACA,SAAA,aAAA;AACA,GAzLA;AA0LA,EAAA,OA1LA,qBA0LA;AAAA;;AACA,sCAAA,EAAA,EAAA,IAAA;AAAA,uFAAA,iBAAA,GAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,KAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA;AADA;AAAA,uBAEA,2BAAA,SAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,OAFA,yBAEA,IAFA;AAGA,gBAAA,KAAA,CAAA,OAAA,GAAA,OAAA;;AACA,gBAAA,KAAA,CAAA,OAAA;;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAAA;;AAAA;AAAA;AAAA;AAAA;AAMA,GAjMA;AAkMA,EAAA,OAAA,EAAA;AACA;;;AAGA,IAAA,aAJA,2BAIA;AAAA;;AACA,iCAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAAA,QAAA,EAAA;AACA,SAFA;AAGA,QAAA,MAAA,CAAA,wBAAA,GAAA,GAAA,CAAA,IAAA;;AACA,QAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,IAAA,MAAA,EAAA;AACA,mBAAA,IAAA,CAAA,OAAA,GAAA,MAAA,CAAA,wBAAA;AACA;AACA,SAJA;AAKA,OAVA;AAWA,KAhBA;AAiBA,IAAA,WAjBA,yBAiBA;AACA,iCAAA,KAAA,MAAA,EAAA,OAAA;AACA,KAnBA;AAoBA,IAAA,SApBA,qBAoBA,EApBA,EAoBA;AACA,UAAA,EAAA,EAAA;AACA,eAAA,KAAA,UAAA,CAAA,MAAA,CAAA,UAAA,CAAA;AAAA,iBAAA,CAAA,CAAA,KAAA,KAAA,EAAA;AAAA,SAAA,EAAA,CAAA,EAAA,KAAA;AACA,OAFA,MAEA;AACA,eAAA,EAAA;AACA;AACA,KA1BA;AA2BA;AACA,IAAA,UA5BA,wBA4BA;AAAA;;AACA,wCAAA;AAAA,QAAA,MAAA,EAAA,KAAA,IAAA,CAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KAhCA;AAkCA,IAAA,WAlCA,uBAkCA,GAlCA,EAkCA,UAlCA,EAkCA;AAAA;;AACA,UAAA,GAAA,CAAA,KAAA,KAAA,MAAA,EAAA;AACA,0CAAA;AAAA,UAAA,MAAA,EAAA,GAAA,CAAA,KAAA,CAAA,QAAA;AAAA,SAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,gBAAA,IAAA,CAAA,KAAA,IAAA,KAAA,EAAA;AACA,cAAA,MAAA,CAAA,IAAA,CAAA,UAAA,EAAA,KAAA,EAAA,EAAA;;AACA,qBAAA,IAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;AACA;AACA,WALA;AAMA,SAPA;AAQA;AACA,KA7CA;;AA8CA;;;;AAIA,IAAA,mBAlDA,+BAkDA,IAlDA,EAkDA;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,IAAA,GAAA,OAAA,IAAA,GAAA,EAAA,CADA,CACA;;AACA,UAAA,CAAA,QAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,kBAAA;AACA;;AACA,UAAA,IAAA,GAAA,IAAA,CAAA,IAAA,GAAA,IAAA;AACA,KAxDA;;AAyDA;;;;;;AAMA,IAAA,gBA/DA,4BA+DA,QA/DA,EA+DA,IA/DA,EA+DA,QA/DA,EA+DA;AACA;AACA,WAAA,IAAA,CAAA,YAAA,GAAA,QAAA,CAAA,IAAA,CAAA,UAAA,CAFA,CAGA;;AACA,WAAA,IAAA,CAAA,cAAA,GAAA,QAAA,CAAA,IAAA,CAAA,OAAA,CAAA,WAAA;AACA,KApEA;;AAsEA;;;;;AAKA,IAAA,mBA3EA,+BA2EA,IA3EA,EA2EA,QA3EA,EA2EA,CAAA,CA3EA;;AA4EA;;;AAGA,IAAA,mBA/EA,iCA+EA;AACA;AACA,WAAA,iBAAA,CAAA,UAAA,GAAA,qBAAA;AACA,WAAA,KAAA,CAAA,aAAA,CAAA,MAAA;AACA,KAnFA;AAoFA,IAAA,UApFA,sBAoFA,IApFA,EAoFA;AACA,UAAA,IAAA,IAAA,IAAA,KAAA,MAAA,EAAA;AACA,eAAA,KAAA,wBAAA,CAAA,MAAA,CAAA,UAAA,CAAA;AAAA,iBAAA,CAAA,CAAA,KAAA,KAAA,IAAA;AAAA,SAAA,EAAA,CAAA,EACA,KADA;AAEA,OAHA,MAGA;AACA,eAAA,UAAA;AACA;AACA,KA3FA;AA4FA;AACA,IAAA,OA7FA,mBA6FA,MA7FA,EA6FA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,MAAA,+DAAA,MAAA,CAAA,MAAA,GAAA,MAAA;AADA;AAAA,uBAEA,uBAAA,MAAA,CAAA,MAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,kBAEA,IAFA;AAEA,gBAAA,IAFA,kBAEA,IAFA;;AAGA,oBAAA,IAAA,KAAA,MAAA,EAAA;AAAA,uEACA,IAAA,CAAA,OADA;;AAAA;AACA,wEAAA;AAAA,sBAAA,CAAA;AACA,sBAAA,CAAA,CAAA,MAAA,GAAA,MAAA,CAAA,UAAA,CAAA,CAAA,CAAA,IAAA,CAAA;AACA,sBAAA,CAAA,CAAA,KAAA,GAAA,MAAA,CAAA,SAAA,CAAA,CAAA,CAAA,GAAA,CAAA;AACA;AAJA;AAAA;AAAA;AAAA;AAAA;;AAKA,kBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KAxGA;AA0GA;AACA,IAAA,eA3GA,6BA2GA;AACA;AACA,WAAA,UAAA,GAAA,KAAA,CAFA,CAGA;;AACA,WAAA,iBAAA,GAAA,IAAA,CAJA,CAKA;;AACA,WAAA,QAAA,GAAA,OAAA,CANA,CAOA;;AACA,WAAA,IAAA,GAAA;AACA,QAAA,IAAA,EAAA;AADA,OAAA;AAGA,KAtHA;AAuHA;AACA,IAAA,UAxHA,sBAwHA,GAxHA,EAwHA;AACA;AACA,WAAA,iBAAA,GAAA,IAAA,CAFA,CAGA;;AACA,WAAA,QAAA,GAAA,OAAA,CAJA,CAKA;;AACA,WAAA,UAAA,GAAA,IAAA,CANA,CAOA;;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,UAAA;AACA,KAlIA;AAmIA;AACA,IAAA,aApIA,yBAoIA,GApIA,EAoIA;AACA;AACA,WAAA,iBAAA,GAAA,IAAA,CAFA,CAGA;;AACA,WAAA,QAAA,GAAA,OAAA,CAJA,CAKA;;AACA,WAAA,UAAA,GAAA,KAAA,CANA,CAOA;;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,UAAA;AACA,KA9IA;AA+IA;AACA,IAAA,QAhJA,sBAgJA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,MAAA,CAAA,KAAA,CAAA,MAAA,EAAA,QAAA;AAAA,oGAAA,kBAAA,KAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA,iCACA,KADA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA,mCAGA,4BAAA,MAAA,CAAA,IAAA,CAHA;;AAAA;AAAA;AAGA,4BAAA,IAHA,uBAGA,IAHA;;AAIA,gCAAA,IAAA,KAAA,MAAA,EAAA;AACA,8BAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA;;AANA;AAAA;;AAAA;AAAA;AAAA;AAQA,4BAAA,OAAA,CAAA,GAAA;;AARA;AAUA;AACA,4BAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,4BAAA,MAAA,CAAA,OAAA;;AACA,4BAAA,MAAA,CAAA,iBAAA,GAAA,KAAA;;AAbA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAA;;AAAA;AAAA;AAAA;AAAA,oBADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA,KAjKA;AAkKA;AACA,IAAA,SAnKA,qBAmKA,EAnKA,EAmKA;AAAA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,MAAA,GAAA,CAAA,IAAA,CAAA,EAAA;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAKA,IALA,CAKA,YAAA;AACA,8BAAA,GAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,cAAA,IAAA,SAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,OAAA;AACA,WANA,MAMA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAIA;AACA,SAbA;AAcA,OApBA,EAqBA,KArBA,CAqBA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OA1BA;AA2BA,WAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;AACA,WAAA,OAAA;AACA,KA1MA;AA4MA;AACA,IAAA,mBA7MA,+BA6MA,GA7MA,EA6MA;AACA,WAAA,YAAA,GAAA,EAAA;AACA,WAAA,WAAA,GAAA,GAAA;AACA,WAAA,WAAA,CAAA,KAAA,GAAA,GAAA,CAAA,KAAA;AACA,WAAA,qBAAA,GAAA,IAAA;AACA,WAAA,WAAA;AACA,KAnNA;AAqNA;AACA,IAAA,mBAtNA,+BAsNA,GAtNA,EAsNA;AACA,WAAA,WAAA,GAAA,GAAA;AACA,WAAA,WAAA,CAAA,KAAA,GAAA,GAAA,CAAA,KAAA;AACA,WAAA,oBAAA,GAAA,IAAA;AACA,WAAA,YAAA;AACA,KA3NA;AA4NA;AACA,IAAA,WA7NA,yBA6NA;AACA,WAAA,MAAA,GAAA,KAAA,QAAA,CAAA,IAAA,GAAA,MAAA;AACA,WAAA,MAAA,CAAA,KAAA,GAAA,KAAA,WAAA,CAAA,KAAA;AACA,WAAA,2BAAA,GAAA,IAAA;AACA,KAjOA;AAkOA,IAAA,QAlOA,oBAkOA,GAlOA,EAkOA;AACA,WAAA,MAAA,GAAA,GAAA;AACA,WAAA,2BAAA,GAAA,IAAA;AACA,KArOA;AAsOA;AACA,IAAA,WAvOA,yBAuOA;AACA,WAAA,MAAA,GAAA,KAAA,QAAA,CAAA,IAAA,GAAA,MAAA;AACA,WAAA,MAAA,CAAA,KAAA,GAAA,KAAA,WAAA,CAAA,KAAA;AACA,WAAA,wBAAA,GAAA,IAAA;AACA,KA3OA;AA4OA,IAAA,QA5OA,oBA4OA,GA5OA,EA4OA;AACA,WAAA,MAAA,GAAA,GAAA;AACA,WAAA,wBAAA,GAAA,IAAA;AACA,KA/OA;AAgPA;AACA,IAAA,gBAjPA,8BAiPA,CAAA,CAjPA;AAkPA;AACA,IAAA,mBAnPA,iCAmPA,CAAA,CAnPA;AAoPA;AACA,IAAA,eArPA,6BAqPA,CAAA,CArPA;AAuPA,IAAA,WAvPA,yBAuPA,CAAA,CAvPA;AAwPA;AACA,IAAA,YAzPA,wBAyPA,IAzPA,EAyPA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KA3PA;AA6PA;AACA,IAAA,WA9PA,yBA8PA;AAAA;;AACA,WAAA,SAAA,GAAA,IAAA;AACA,oCAAA,KAAA,WAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,MAAA,CAAA,WAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,MAAA,CAAA,SAAA,GAAA,KAAA;AACA,OAJA;AAKA,KArQA;AAuQA;AACA,IAAA,cAxQA,4BAwQA;AAAA;;AACA,6CAAA,KAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,WAAA;;AACA,QAAA,MAAA,CAAA,wBAAA,GAAA,KAAA;AACA,OAHA;AAIA,KA7QA;AA8QA;AACA,IAAA,UA/QA,wBA+QA;AAAA;;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAKA,IALA,CAKA,YAAA;AACA,YAAA,GAAA,GAAA,EAAA;;AACA,QAAA,OAAA,CAAA,YAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA,SAFA;;AAGA,yCAAA,GAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,cAAA,IAAA,SAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAIA,WALA,MAKA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAIA;;AACA,UAAA,OAAA,CAAA,WAAA;AACA,SAbA;AAcA,OAxBA,EAyBA,KAzBA,CAyBA,YAAA;AACA,QAAA,OAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OA9BA;AA+BA,KA/SA;AAiTA;AACA,IAAA,YAlTA,0BAkTA;AAAA;;AACA,WAAA,SAAA,GAAA,IAAA;AACA,0CAAA,KAAA,WAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,OAAA,CAAA,WAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,OAAA,CAAA,SAAA,GAAA,KAAA;AACA,OAJA;AAKA,KAzTA;AA0TA;AACA,IAAA,cA3TA,4BA2TA;AAAA;;AACA,mDAAA,KAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,YAAA;;AACA,QAAA,OAAA,CAAA,2BAAA,GAAA,KAAA;AACA,OAHA;AAIA,KAhUA;AAiUA,IAAA,YAjUA,0BAiUA;AAAA;;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAKA,IALA,CAKA,YAAA;AACA,YAAA,GAAA,GAAA,EAAA;;AACA,QAAA,OAAA,CAAA,YAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA,SAFA;;AAGA,+CAAA,GAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,cAAA,IAAA,SAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAIA,WALA,MAKA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAIA;;AACA,UAAA,OAAA,CAAA,YAAA;AACA,SAbA;AAcA,OAxBA,EAyBA,KAzBA,CAyBA,YAAA;AACA,QAAA,OAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OA9BA;AA+BA,KAjWA;AAmWA,IAAA,UAnWA,sBAmWA,IAnWA,EAmWA;AACA,WAAA,KAAA,CAAA,OAAA,CAAA,kBAAA,CAAA,IAAA;AACA,KArWA;AAsWA,IAAA,eAtWA,2BAsWA,GAtWA,EAsWA;AACA,WAAA,YAAA,GAAA,GAAA;AACA,KAxWA;AAyWA,IAAA,UAzWA,sBAyWA,IAzWA,EAyWA;AACA,WAAA,KAAA,CAAA,OAAA,CAAA,kBAAA,CAAA,IAAA;AACA,KA3WA;AA4WA,IAAA,eA5WA,2BA4WA,GA5WA,EA4WA;AACA,WAAA,YAAA,GAAA,GAAA;AACA;AA9WA;AAlMA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      ref=\"filter1\"\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      @handleReset=\"filterReset\"\n      @handleEvent=\"handleEvent\"\n    />\n    <el-white class=\"button-group1\">\n      <div class=\"button_btn\">\n        <el-button\n          @click=\"addSensorButton\"\n          v-hasPermi=\"['gfgql:button:add']\"\n          icon=\"el-icon-plus\"\n          type=\"primary\"\n          >新增\n        </el-button>\n        <el-button type=\"primary\" icon=\"el-icon-download\" @click=\"exportExcel()\"\n          >导出</el-button\n        >\n      </div>\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"selectChange\"\n        height=\"63vh\"\n      >\n        <el-table-column\n          slot=\"table_eight\"\n          align=\"center\"\n          fixed=\"right\"\n          style=\"display: block\"\n          label=\"操作\"\n          width=\"180\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              @click=\"getGqjInfo(scope.row)\"\n              class=\"el-icon-view\"\n              title=\"详情\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"scope.row.createBy == currUser\"\n              @click=\"updateGqjInfo(scope.row)\"\n              class=\"el-icon-edit\"\n              title=\"编辑\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"scope.row.createBy == currUser\"\n              @click=\"deleteRow(scope.row.objId)\"\n              class=\"el-icon-delete\"\n              title=\"删除\"\n            ></el-button>\n          </template>\n        </el-table-column>\n      </comp-table>\n    </el-white>\n    <!-- 工器具台账详情修改新增 -->\n    <el-dialog\n      :title=\"gqjTital\"\n      :visible.sync=\"dialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form\n        :model=\"form\"\n        label-width=\"80px\"\n        :disabled=\"isDisabled\"\n        :rules=\"rules\"\n        ref=\"form\"\n      >\n        <el-row>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属公司\" prop=\"ssgs\">\n              <el-select\n                v-model=\"form.ssgs\"\n                placeholder=\"所属公司\"\n                clearable\n                :disabled=\"isDisabled\"\n                @change=\"getBdzList\"\n              >\n                <el-option\n                  v-for=\"item in organizationSelectedList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属光伏电站\" prop=\"bdz\">\n              <el-select\n                placeholder=\"请选择光伏电站\"\n                clearable\n                v-model=\"form.bdz\"\n                :disabled=\"isDisabled\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in bdzList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"名称\" prop=\"sbmc\">\n              <el-input v-model=\"form.sbmc\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"设备分类\" prop=\"sblx\">\n              <el-select\n                placeholder=\"请选择\"\n                clearable\n                v-model=\"form.sblx\"\n                :disabled=\"isDisabled\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in gfgqjlx\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"规格型号\" prop=\"xh\">\n              <el-input v-model=\"form.xh\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <!--          <el-col :span=\"8\">\n                      <el-form-item label=\"编号\" style=\"width: 100%\">\n                        <el-input v-model=\"form.ccbh\" :disabled=\"isDisabled\"></el-input>\n                      </el-form-item>\n                    </el-col>-->\n          <el-col :span=\"8\">\n            <el-form-item label=\"数量\" prop=\"sl\">\n              <el-input-number\n                :min=\"1\"\n                v-model=\"form.sl\"\n                :disabled=\"isDisabled\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"使用年限\">\n              <el-input v-model=\"form.jyzq\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"领用人\" prop=\"fzr\">\n              <el-input v-model=\"form.fzr\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"批准人\" prop=\"cfdd\">\n              <el-input v-model=\"form.cfdd\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"领取时间\" prop=\"tyrq\">\n              <el-date-picker\n                v-model=\"form.tyrq\"\n                type=\"date\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"选择日期\"\n                :disabled=\"isDisabled\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\">\n              <el-input\n                type=\"textarea\"\n                v-model=\"form.bz\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"qxcommit\"\n          v-if=\"gqjTital === '工器具新增' || gqjTital === '工器具修改'\"\n          class=\"pmyBtn\"\n        >\n          确 定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getUUID } from \"@/utils/ruoyi\";\nimport {\n  deleteAssetGqjJxRecords,\n  deleteYxSyRecords,\n  getAssetGqjJxRecords,\n  getList,\n  getYxSyRecords,\n  remove,\n  saveOrUpdate,\n  saveOrUpdateAssetGqjJxRecords,\n  saveOrUpdateYxSyRecords,\n  exportExcel,\n  getGfBdzSelectList\n} from \"@/api/dagangOilfield/asset/assetGqj\";\nimport CompTable from \"com/CompTable\";\nimport ElFilter from \"com/ElFilter\";\nimport { getDictTypeData } from \"@/api/system/dict/data\";\nimport { getFgsOptions } from \"@/api/yxgl/gfyxgl/gfzbgl\";\n\n\nexport default {\n  components: { CompTable, ElFilter },\n  name: \"gqjgl\",\n  data() {\n    return {\n      gfgqjlx: [],\n      currUser: this.$store.getters.name,\n      params: {\n        type: \"gf\"\n      },\n      bdzAllList: [],\n      bdzList: [],\n      //组织结构下拉数据\n      organizationSelectedList: [],\n      // 表单校验\n      rules: {\n        sl: [{ required: true, message: \"请输入\", trigger: \"blur\" }],\n        ssgs: [{ required: true, message: \"请选择\", trigger: \"blur\" }],\n        // bdz: [{ required: true, message: '请选择', trigger: 'blur' }],\n        sbmc: [{ required: true, message: \"请输入\", trigger: \"blur\" }],\n        sblx: [{ required: true, message: \"请选择\", trigger: \"blur\" }],\n        fzr: [{ required: true, message: \"请输入\", trigger: \"blur\" }]\n      },\n      //工器具详情框字段控制\n      isDisabled: false,\n      //工器具弹出框表头\n      gqjTital: \"工器具新增\",\n      //表格内容\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"ssgsmc\", label: \"所属公司\", minWidth: \"120\" },\n          { prop: \"bdzmc\", label: \"所属光伏电站\", minWidth: \"120\" },\n          { prop: \"sbmc\", label: \"名称\", minWidth: \"180\" },\n          { prop: \"xh\", label: \"规格型号\", minWidth: \"180\" },\n          { prop: \"fzr\", label: \"领用人\", minWidth: \"180\" },\n          // {prop: 'ccbh', label: '编号', minWidth: '180'},\n          { prop: \"jyzq\", label: \"使用年限\", minWidth: \"180\" },\n          { prop: \"tyrq\", label: \"领取时间\", minWidth: \"250\" }\n          // {\n          //   fixed: \"right\",\n          //   prop: 'operation',\n          //   label: '操作',\n          //   minWidth: '150px',\n          //   style: {display: 'block'},\n          //   operation: [\n          //     {name: '修改', clickFun: this.updateGqjInfo},\n          //     {name: '详情', clickFun: this.getGqjInfo},\n          //   ],\n          // },\n        ]\n      },\n      //筛选条件\n      filterInfo: {\n        data: {\n          fzr: \"\",\n          ssgs: \"\",\n          yxbz: \"\",\n          phone: \"\"\n        },\n        fieldList: [\n          {\n            label: \"分公司\",\n            value: \"ssgs\",\n            type: \"select\",\n            options: [],\n            clearable: true\n          },\n          {\n            label: \"光伏电站\",\n            value: \"bdz\",\n            type: \"select\",\n            options: [],\n            clearable: true\n          },\n          { label: \"名称\", type: \"input\", value: \"sbmc\" },\n          { label: \"规格型号\", type: \"input\", value: \"xh\" },\n          { label: \"领用人\", type: \"input\", value: \"fzr\" }\n          // {label: '投运日期', type: 'date', value: 'tyrqArr',dateType: 'daterange',format: 'yyyy-MM-dd'},\n        ]\n      },\n      //检修记录弹出框\n      jwxDialogFormVisible: false,\n      //添加检修记录弹出框\n      addJwxSybgDialogFormVisible: false,\n      //工器具弹出框\n      dialogFormVisible: false,\n      //试验时间\n      sysj: \"\",\n      fildtps: [],\n      //试验弹出框\n      sybgDialogFormVisible: false,\n      //添加试验报告\n      addSybgDialogFormVisible: false,\n      //弹出框表单\n      form: {\n        type: \"gf\"\n      },\n      loading: false,\n      //工器具试验数据集合\n      gqjsyList: [],\n      //检修数据集合\n      gqjJxList: [],\n      //删除是否可用\n      multipleSensor: true,\n      showSearch: true,\n      //删除选择列\n      selectRows: [],\n      //工器具文件上传参数\n      gqjInfoUploadData: {\n        businessId: undefined\n      },\n      //工器具文件上传请求头\n      gqjInfoUpHeader: {},\n\n      //试验查询条件\n      syQueryForm: {\n        gqjId: \"\",\n        total: 0,\n        pageSize: 10,\n        pageNum: 1\n      },\n\n      //试验新增表单数据\n      syFrom: {\n        id: \"\",\n        gqjId: \"\",\n        sydwId: \"\",\n        sydwName: \"\",\n        syryId: \"\",\n        syryName: \"\",\n        sysj: \"\",\n        syjlCode: \"\",\n        syjlName: \"\",\n        remark: \"\"\n      },\n\n      isSyDetail: false,\n\n      //检修查询条件\n      jxQueryForm: {\n        gqjId: \"\",\n        total: 0,\n        pageSize: 10,\n        pageNum: 1\n      },\n      //检修表单\n      jxForm: {\n        id: \"\",\n        jxdwId: \"\",\n        jxdwName: \"\",\n        jxryId: \"\",\n        jxryName: \"\",\n        jxjg: \"\",\n        jxsj: \"\",\n        remark: \"\",\n        gqjId: \"\"\n      },\n\n      //主表选中行数据\n      mainRowData: {},\n      //试验table加载\n      syLoading: false,\n      //试验选中行\n      sySelectRows: [],\n      //检修table加载\n      jxLoading: false,\n      //检修选中行\n      jxSelectRows: []\n    };\n  },\n  watch: {},\n  created() {\n    //获取组织结构下拉数据\n    this.getFgsOptions();\n  },\n  mounted() {\n    getGfBdzSelectList({}).then(async res => {\n      this.bdzAllList = res.data;\n      let { data: gfgqjlx } = await getDictTypeData(\"gfgqjlx\");\n      this.gfgqjlx = gfgqjlx;\n      this.getData();\n    });\n  },\n  methods: {\n    /**\n     * 获取分公司下拉数据\n     */\n    getFgsOptions() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.organizationSelectedList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"ssgs\") {\n            return (item.options = this.organizationSelectedList);\n          }\n        });\n      });\n    },\n    exportExcel() {\n      exportExcel(this.params, \"光伏工器具\");\n    },\n    formatBdz(id) {\n      if (id) {\n        return this.bdzAllList.filter(g => g.value === id)[0].label;\n      } else {\n        return \"\";\n      }\n    },\n    //获取光伏电站下拉框\n    getBdzList() {\n      getGfBdzSelectList({ ssdwbm: this.form.ssgs }).then(res => {\n        this.bdzList = res.data;\n      });\n    },\n\n    handleEvent(val, eventValue) {\n      if (val.label === \"ssgs\") {\n        getGfBdzSelectList({ ssdwbm: val.value.toString() }).then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value == \"bdz\") {\n              this.$set(eventValue, \"bdz\", \"\");\n              return (item.options = res.data);\n            }\n          });\n        });\n      }\n    },\n    /**\n     * 上传附附件之前的处理函数\n     * @param file\n     */\n    gqjInfoBeforeUpload(file) {\n      const fileSize = file.size < 1024 * 1024 * 50; //10M\n      if (!fileSize) {\n        this.$message.error(\"上传文件大小不能超过 50MB!\");\n      }\n      let size = file.size / 1024;\n    },\n    /**\n     * 上传附件成功调用的函数\n     * @param response\n     * @param file\n     * @param fileList\n     */\n    gqjInfoonSuccess(response, file, fileList) {\n      //文件id\n      this.form.attachmentid = response.data.businessId;\n      //文件名称\n      this.form.attachmentname = response.data.sysFile.fileOldName;\n    },\n\n    /**\n     * 移除文件\n     * @param file\n     * @param fileList\n     */\n    gqjInfohandleRemove(file, fileList) {},\n    /**\n     * 工器具上传文件到服务器\n     */\n    gqjInfoSubmitUpload() {\n      debugger;\n      this.gqjInfoUploadData.businessId = getUUID();\n      this.$refs.uploadGqjInfo.submit();\n    },\n    formatSsgs(ssgs) {\n      if (ssgs && ssgs !== \"3002\") {\n        return this.organizationSelectedList.filter(g => g.value === ssgs)[0]\n          .label;\n      } else {\n        return \"港东光伏电分公司\";\n      }\n    },\n    //工器具列表查询\n    async getData(params) {\n        this.params = { ...this.params, ...params };\n        const { data, code } = await getList(this.params);\n        if (code === \"0000\") {\n          for (let i of data.records) {\n            i.ssgsmc = this.formatSsgs(i.ssgs);\n            i.bdzmc = this.formatBdz(i.bdz);\n          }\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n        }\n    },\n\n    //工器具列表新增按钮\n    addSensorButton() {\n      //开启弹出框内输入框编辑权限\n      this.isDisabled = false;\n      //打开弹出框\n      this.dialogFormVisible = true;\n      //设置弹出框表头\n      this.gqjTital = \"工器具新增\";\n      //清空弹出框内容\n      this.form = {\n        type: \"gf\"\n      };\n    },\n    //工器具列表详情按钮\n    getGqjInfo(row) {\n      //打开弹出框\n      this.dialogFormVisible = true;\n      //设置弹出框表头\n      this.gqjTital = \"工器具详情\";\n      //禁用所有输入框\n      this.isDisabled = true;\n      //给弹出框赋值\n      this.form = { ...row };\n      this.getBdzList();\n    },\n    //工器具修改按钮\n    updateGqjInfo(row) {\n      //打开弹出框\n      this.dialogFormVisible = true;\n      //设置弹出框表头\n      this.gqjTital = \"工器具修改\";\n      //开启弹出框内输入框编辑权限\n      this.isDisabled = false;\n      //给弹出框内赋值\n      this.form = { ...row };\n      this.getBdzList();\n    },\n    //工器具列表新增修改保存\n    async qxcommit() {\n      await this.$refs[\"form\"].validate(async valid => {\n        if (valid) {\n          try {\n            let { code } = await saveOrUpdate(this.form);\n            if (code === \"0000\") {\n              this.$message.success(\"操作成功\");\n            }\n          } catch (e) {\n            console.log(e);\n          }\n          //恢复分页\n          this.tableAndPageInfo.pager.pageResize = \"Y\";\n          this.getData();\n          this.dialogFormVisible = false;\n        }\n      });\n    },\n    //删除工器具列表\n    deleteRow(id) {\n      // if (this.selectRows.length < 1) {\n      //   this.$message.warning(\"请选择正确的数据！！！\")\n      //   return\n      // }\n      // let ids = this.selectRows.map(item => {\n      //   return item.objId\n      // });\n      let obj = [];\n      obj.push(id);\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove(obj).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n      this.tableAndPageInfo.pager.pageResize = \"Y\";\n      this.getData();\n    },\n\n    //查看试验\n    handleSearchSYClick(row) {\n      this.sySelectRows = [];\n      this.mainRowData = row;\n      this.syQueryForm.gqjId = row.objId;\n      this.sybgDialogFormVisible = true;\n      this.getYxSyData();\n    },\n\n    //查看检修\n    handleSerchJWXClick(row) {\n      this.mainRowData = row;\n      this.jxQueryForm.gqjId = row.objId;\n      this.jwxDialogFormVisible = true;\n      this.getJxRecords();\n    },\n    //添加检修\n    addJxButton() {\n      this.jxForm = this.$options.data().jxForm;\n      this.jxForm.gqjId = this.mainRowData.objId;\n      this.addJwxSybgDialogFormVisible = true;\n    },\n    updateJx(row) {\n      this.jxForm = row;\n      this.addJwxSybgDialogFormVisible = true;\n    },\n    //添加试验\n    addSyButton() {\n      this.syFrom = this.$options.data().syFrom;\n      this.syFrom.gqjId = this.mainRowData.objId;\n      this.addSybgDialogFormVisible = true;\n    },\n    updateSy(row) {\n      this.syFrom = row;\n      this.addSybgDialogFormVisible = true;\n    },\n    //每页展示数量点击事件\n    handleSizeChange() {},\n    //页码改变事件\n    handleCurrentChange() {},\n    //树点击事件\n    handleNodeClick() {},\n\n    filterReset() {},\n    //选择每一行\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n\n    //获取试验记录数据\n    getYxSyData() {\n      this.syLoading = true;\n      getYxSyRecords(this.syQueryForm).then(res => {\n        this.gqjsyList = res.data.records;\n        this.syQueryForm.total = res.data.total;\n        this.syLoading = false;\n      });\n    },\n\n    //新增修改试验记录数据\n    saveOrUpdateSy() {\n      saveOrUpdateYxSyRecords(this.syFrom).then(res => {\n        this.getYxSyData();\n        this.addSybgDialogFormVisible = false;\n      });\n    },\n    //批量删除试验数据\n    deleteYxSy() {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          let ids = [];\n          this.sySelectRows.forEach(item => {\n            ids.push(item.id);\n          });\n          deleteYxSyRecords(ids).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n            this.getYxSyData();\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n\n    //获取检修记录数据\n    getJxRecords() {\n      this.jxLoading = true;\n      getAssetGqjJxRecords(this.jxQueryForm).then(res => {\n        this.gqjJxList = res.data.records;\n        this.jxQueryForm.total = res.data.total;\n        this.jxLoading = false;\n      });\n    },\n    //新增修改检修记录数据\n    saveOrUpdateJx() {\n      saveOrUpdateAssetGqjJxRecords(this.jxForm).then(res => {\n        this.getJxRecords();\n        this.addJwxSybgDialogFormVisible = false;\n      });\n    },\n    deleteJxData() {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          let ids = [];\n          this.jxSelectRows.forEach(item => {\n            ids.push(item.id);\n          });\n          deleteAssetGqjJxRecords(ids).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n            this.getJxRecords();\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n\n    syRowClick(rows) {\n      this.$refs.syTable.toggleRowSelection(rows);\n    },\n    syCurrentChange(val) {\n      this.sySelectRows = val;\n    },\n    jxRowClick(rows) {\n      this.$refs.jxTable.toggleRowSelection(rows);\n    },\n    jxCurrentChange(val) {\n      this.jxSelectRows = val;\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.button-group {\n  padding-left: 30px;\n  padding-right: 30px;\n  display: flex;\n  justify-content: flex-end;\n}\n\n.qxlr_dialog_insert {\n  margin-top: 6vh !important;\n}\n\n/*/deep/ .qxlr_dialog_insert .el-input--medium .el-input__inner{*/\n/*  width: 100%;*/\n/*}*/\n.el-select {\n  width: 100%;\n}\n\n.el-date-editor {\n  width: 100%;\n}\n</style>\n"], "sourceRoot": "src/views/dagangOilfield/dwzygl/gqjgl"}]}