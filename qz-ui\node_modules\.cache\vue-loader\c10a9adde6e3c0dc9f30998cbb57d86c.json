{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\pdgl\\pdsgl.vue?vue&type=style&index=0&id=8aa5922e&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\pdgl\\pdsgl.vue", "mtime": 1706897324785}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouaGVhZC1jb250YWluZXIgewogIG1hcmdpbjogMCBhdXRvOwogIHdpZHRoOiAxMDAlOwogIGhlaWdodDogY2FsYygxMDB2aCAtIDIyNXB4KTsKICBvdmVyZmxvdzogYXV0bzsKfQoKLmJveC1jYXJkIHsKICBtYXJnaW4tYm90dG9tOiAxNXB4OwoKICAuZWwtY2FyZF9faGVhZGVyIHsKICAgIGJhY2tncm91bmQtY29sb3I6IHJnYigyMzUsIDI0NSwgMjU1KSAhaW1wb3J0YW50OwogIH0KfQoKLmJveC1jYXJkTGlzdCB7CiAgaGVpZ2h0OiA3MCU7Cn0KCi5pdGVtIHsKICB3aWR0aDogMjAwcHg7CiAgZmxvYXQ6IGxlZnQ7Cn0KCiNtYWluX2NvbnRhaW5lcl9kaiB7CiAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gODRweCk7Cn0KCi5hc2lkZV9oZWlnaHQgewogIGhlaWdodDogODF2aDsKfQoKLmRlZmVjdCAuZWwtZm9ybS1pdGVtOm50aC1jaGlsZChvZGQpIHsKICBtYXJnaW4tcmlnaHQ6IDcwcHg7Cn0KCi8q6IOM5pmv6aKc6Imy6LCD5pW0Ki8KI21haW5fY29udGFpbmVyX2RqLCAjbWFpbl9jb250YWluZXJfZGogLmVsLWFzaWRlIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjYjRjYWYxOwp9CgovZGVlcC8gLnF4bHJfZGlhbG9nX2luc2VydCAuZWwtZGlhbG9nX19oZWFkZXIgewogIGJhY2tncm91bmQtY29sb3I6ICMwY2MyODM7Cn0KCi9kZWVwLyAucG15QnRuIHsKICBiYWNrZ3JvdW5kOiAjMGNjMjgzOwp9CgovKiEq5by55Ye65qGG5YaF5a695bqm6K6+572uKiEqLwoKLmVsLXNlbGVjdCB7CiAgd2lkdGg6IDEwMCU7Cn0KCi5lbC1kYXRlLWVkaXRvcnsKICB3aWR0aDogMTAwJTsKfQoKLmVsLWNhcm91c2VsX19pdGVtIGgzIHsKICBjb2xvcjogIzQ3NTY2OTsKICBmb250LXNpemU6IDE0cHg7CiAgb3BhY2l0eTogMC43NTsKICBsaW5lLWhlaWdodDogMTUwcHg7Cgp9CgouZWwtY2Fyb3VzZWxfX2l0ZW06bnRoLWNoaWxkKDJuKSB7CiAgYmFja2dyb3VuZC1jb2xvcjogIzk5YTliZjsKfQoKLmVsLWNhcm91c2VsX19pdGVtOm50aC1jaGlsZCgybisxKSB7CiAgYmFja2dyb3VuZC1jb2xvcjogI2QzZGNlNjsKfQovL+acieWtkOiKgueCuSDkuJTmnKrlsZXlvIAKLmVsLXRyZWUgOjp2LWRlZXAgLmVsLWljb24tY2FyZXQtcmlnaHQ6YmVmb3JlIHsKICBiYWNrZ3JvdW5kOiB1cmwoIi4uLy4uLy4uLy4uL2Fzc2V0cy9pbWFnZS9hZGQucG5nIikgbm8tcmVwZWF0IDA7CiAgY29udGVudDogIiI7CiAgZGlzcGxheTogYmxvY2s7CiAgd2lkdGg6IDE2cHg7CiAgaGVpZ2h0OiAxNnB4OwogIGZvbnQtc2l6ZTogMTZweDsKICBiYWNrZ3JvdW5kLXNpemU6IDE2cHg7Cn0KLy/mnInlrZDoioLngrkg5LiU5bey5bGV5byACi5lbC10cmVlCjo6di1kZWVwCi5lbC10cmVlLW5vZGVfX2V4cGFuZC1pY29uLmV4cGFuZGVkLmVsLWljb24tY2FyZXQtcmlnaHQ6YmVmb3JlIHsKICBiYWNrZ3JvdW5kOiB1cmwoIi4uLy4uLy4uLy4uL2Fzc2V0cy9pbWFnZS9wcmVwLnBuZyIpIG5vLXJlcGVhdCAwOwogIGNvbnRlbnQ6ICIiOwogIGRpc3BsYXk6IGJsb2NrOwogIHdpZHRoOiAxNnB4OwogIGhlaWdodDogMTZweDsKICBmb250LXNpemU6IDE2cHg7CiAgYmFja2dyb3VuZC1zaXplOiAxNnB4Owp9Ci8v5rKh5pyJ5a2Q6IqC54K5Ci5lbC10cmVlIDo6di1kZWVwIC5lbC10cmVlLW5vZGVfX2V4cGFuZC1pY29uLmlzLWxlYWY6OmJlZm9yZSB7CiAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7CiAgY29udGVudDogIiI7CiAgZGlzcGxheTogYmxvY2s7CiAgd2lkdGg6IDE2cHg7CiAgaGVpZ2h0OiAxNnB4OwogIGZvbnQtc2l6ZTogMTZweDsKICBiYWNrZ3JvdW5kLXNpemU6IDE2cHg7Cn0K"}, {"version": 3, "sources": ["pdsgl.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkwBA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "pdsgl.vue", "sourceRoot": "src/views/dagangOilfield/dwzygl/pdgl", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card shadow=\"never\" style=\"background:#e0f8ed;\">\n          <div class=\"text head-container\">\n            <el-col style=\"padding: 0\">\n              <el-tree :expand-on-click-node=\"true\"\n                       highlight-current\n                       id=\"tree\"\n                       :data=\"treeOptions\"\n                       :default-expanded-keys=\"['1']\"\n                       @node-click=\"handleNodeClick\"\n                       node-key=\"nodeId\"\n                       accordion\n              />\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 120, itemWidth: 180 }\"\n          @handleReset=\"filterReset\"\n        />\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button icon=\"el-icon-plus\" @click=\"pdsAddSensorButton\" v-hasPermi=\"['pdztz:button:add']\" type=\"primary\">新增</el-button>\n            <el-button icon=\"el-icon-delete\" v-hasPermi=\"['pdztz:button:delete']\" type=\"danger\" @click=\"deletePds\">删除</el-button>\n          </div>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"500\"\n          >\n          <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\" width=\"160\"\n                           :resizable=\"false\">\n            <template slot-scope=\"scope\">\n              <el-button @click=\"getUpdate(scope.row)\" v-hasPermi=\"['pdztz:button:update']\" type=\"text\" size=\"small\">修改</el-button>\n              <el-button @click=\"getXq(scope.row)\" type=\"text\" size=\"small\">详情</el-button>\n            </template>\n          </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!--配电室新增、修改、详情弹框-->\n    <el-dialog title=\"配电室\" :visible.sync=\"pdsDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"70%\" @close=\"handleClose\">\n      <el-form ref=\"form\" :model=\"form\" :disabled=\"isDisabled\" :rules=\"rules\" label-width=\"130px\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"配电室类型：\" prop=\"lx\">\n              <el-select v-model=\"form.lx\" placeholder=\"请选择配电室类型\" clearable>\n                <el-option\n                    v-for=\"item in pdslx\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n<!--          <el-col :span=\"8\">\n            <el-form-item label=\"所属公司：\" prop=\"ssgs\">\n              <el-select v-model=\"form.ssgs\" placeholder=\"请选择所属公司\">\n                <el-option\n                  v-for=\"item in bdzOptionsDataList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>-->\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属线路名称：\" prop=\"ssxlmc\">\n              <el-input v-model=\"form.ssxlmc\" placeholder=\"请输入所属线路名称\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属线路编号：\" prop=\"ssxlbh\">\n              <el-input v-model=\"form.ssxlbh\" placeholder=\"请输入所属线路编号\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属线段名称：\" prop=\"ssxdmc\">\n              <el-input v-model=\"form.ssxdmc\" placeholder=\"请输入所属线段名称\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"配电室名称：\" prop=\"pdsmc\">\n              <el-input v-model=\"form.pdsmc\" placeholder=\"请输入配电室名称\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"运行编号：\" prop=\"yxbh\">\n              <el-input v-model=\"form.yxbh\" placeholder=\"请输入运行编号\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"运维班组：\" prop=\"yxbh\">\n              <el-select v-model=\"form.ywbz\" placeholder=\"请选择运维班组\" clearable>\n                <el-option\n                    v-for=\"item in ywbzList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"投运日期：\" prop=\"tyrq\">\n              <el-date-picker\n                type=\"date\"\n                placeholder=\"选择日期\"\n                value-format=\"yyyy-MM-dd\"\n                v-model=\"form.tyrq\"\n                style=\"width: 100%\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n<!--          <el-col :span=\"8\">\n            <el-form-item label=\"ERP编码：\" prop=\"erpBm\">\n              <el-input v-model=\"form.erpBm\" placeholder=\"请输入ERP编码\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"保管人：\" prop=\"bgr\">\n              <el-input v-model=\"form.bgr\" placeholder=\"请输入保管人\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"资产变动方式：\" prop=\"zcbdfs\">\n              <el-input v-model=\"form.zcbdfs\" placeholder=\"请输入资产变动方式\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"资产属性：\" prop=\"zcsx\">\n              <el-input v-model=\"form.zcsx\" placeholder=\"请输入资产属性\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"资产编号：\" prop=\"zcbh\">\n              <el-input v-model=\"form.zcbh\" placeholder=\"请输入资产编号\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"WBS元素：\" prop=\"wbsYs\">\n              <el-input v-model=\"form.wbsYs\" placeholder=\"请输入WBS元素\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"资产性质：\" prop=\"zcxz\">\n              <el-input v-model=\"form.zcxz\" placeholder=\"请输入资产性质\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"施工单位：\" prop=\"sgdw\">\n              <el-input v-model=\"form.sgdw\" placeholder=\"请输入施工单位\">\n              </el-input>\n            </el-form-item>\n          </el-col><el-col :span=\"8\">\n          <el-form-item label=\"建筑物尺寸：\" prop=\"jzwcc\">\n            <el-input v-model=\"form.jzwcc\" placeholder=\"请输入建筑物尺寸\">\n            </el-input>\n          </el-form-item>\n        </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"建筑物材质：\" prop=\"jzwcz\">\n              <el-input v-model=\"form.jzwcz\" placeholder=\"请输入建筑物材质\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否具有环网：\" prop=\"sfjyhw\">\n              <el-select v-model=\"form.sfjyhw\" placeholder=\"请选择是否具有环网\" :disabled=\"isDisabled\" clearable>\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"状态：\" prop=\"zt\">\n              <el-select v-model=\"form.zt\" placeholder=\"请选择状态\">\n                <el-option\n                  v-for=\"item in sbzt\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"生产厂家：\" prop=\"sccj\">\n              <el-input v-model=\"form.sccj\" placeholder=\"请输入生产厂家\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"通风方式：\" prop=\"tffs\">\n              <el-input v-model=\"form.tffs\" placeholder=\"请输入通风方式\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"站变用：\" prop=\"zby\">\n              <el-input-number v-model=\"form.zby\" :min=\"0\" placeholder=\"请输入站变用\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"高压进线柜数量：\" prop=\"gyjxgsl\">\n              <el-input-number v-model=\"form.gyjxgsl\" :min=\"0\" placeholder=\"请输入高压进线柜数量\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"高压计量柜数量：\" prop=\"gyjlgsl\">\n              <el-input-number v-model=\"form.gyjlgsl\" :min=\"0\" placeholder=\"请输入高压计量柜数量\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"高压出线柜数量：\" prop=\"gycxgsl\">\n              <el-input-number v-model=\"form.gycxgsl\" :min=\"0\" placeholder=\"请输入高压出线柜数量\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"PT柜数量：\" prop=\"ptgsl\">\n              <el-input-number v-model=\"form.ptgsl\" :min=\"0\" placeholder=\"请输入PT柜数量\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n\n<!--          <el-col :span=\"8\">\n            <el-form-item label=\"断路器柜数量\" prop=\"dlqgsl\">\n              <el-input-number v-model=\"form.dlqgsl\" :min=\"0\" placeholder=\"请输入断路器柜数量\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电压互感器柜数量：\" prop=\"dyhgqgsl\">\n              <el-input-number v-model=\"form.dyhgqgsl\" :min=\"0\" placeholder=\"请输入电压互感器柜数量\"></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电容柜数量：\" prop=\"drgsl\">\n              <el-input-number v-model=\"form.drgsl\" :min=\"0\" placeholder=\"请输入电容柜数量\"></el-input-number>\n            </el-form-item>\n          </el-col>-->\n          <el-col :span=\"8\">\n            <el-form-item label=\"母联柜数量：\" prop=\"mlgsl\">\n              <el-input-number v-model=\"form.mlgsl\" :min=\"0\" placeholder=\"请输入母联柜数量\"></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"低压进线柜数量：\" prop=\"dyjxgsl\">\n              <el-input-number v-model=\"form.dyjxgsl\" :min=\"0\" placeholder=\"低压进线柜数量\"></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"低压出线柜数量：\" prop=\"dycxgsl\">\n              <el-input-number v-model=\"form.dycxgsl\" :min=\"0\" placeholder=\"请输入低压出线柜数量\"></el-input-number>\n            </el-form-item>\n          </el-col>\n<!--          <el-col :span=\"8\">\n            <el-form-item label=\"低压接线方式：\" prop=\"dyjxfs\">\n              <el-input v-model=\"form.dyjxfs\" placeholder=\"请输入低压接线方式\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"低压配线柜数量：\" prop=\"dypxgsl\">\n              <el-input-number v-model=\"form.dypxgsl\" :min=\"0\" placeholder=\"请输入低压配线柜数量\"></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"负荷开关柜数量：\" prop=\"fhkggsl\">\n              <el-input-number v-model=\"form.fhkggsl\" :min=\"0\" placeholder=\"请输入负荷开关柜数量\"></el-input-number>\n            </el-form-item>\n          </el-col>-->\n          <el-col :span=\"8\">\n            <el-form-item label=\"低压补偿柜数量：\" prop=\"dybcgsl\">\n              <el-input-number v-model=\"form.dybcgsl\" :min=\"0\" placeholder=\"请输入低压补偿柜数量\"></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"低压计量柜数量：\" prop=\"dyjlgsl\">\n              <el-input-number v-model=\"form.dyjlgsl\" :min=\"0\" placeholder=\"请输入低压计量柜数量\"></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"低压联络柜数量：\" prop=\"dyllgsl\">\n              <el-input-number v-model=\"form.dyllgsl\" :min=\"0\" placeholder=\"请输入低压联络柜数量\"></el-input-number>\n            </el-form-item>\n          </el-col>\n<!--          <el-col :span=\"8\">\n            <el-form-item label=\"负荷开关熔断器组合柜数量：\" prop=\"fhkgrdqzhgsl\">\n              <el-input-number v-model=\"form.fhkgrdqzhgsl\" :min=\"0\" placeholder=\"请输入负荷开关熔断器组合柜数量\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"配变数量：\" prop=\"pbsl\">\n              <el-input-number v-model=\"form.pbsl\" :min=\"0\" placeholder=\"请输入配变数量\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"配变总容量：\" prop=\"pbzrl\">\n              <el-input-number v-model=\"form.pbzrl\" :min=\"0\" placeholder=\"请输入配变总容量(KVA)\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"蓄电池柜数量：\" prop=\"xdcgsl\">\n              <el-input-number v-model=\"form.xdcgsl\" :min=\"0\" placeholder=\"请输入蓄电池柜数量\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"直流柜数量：\" prop=\"zlgsl\">\n              <el-input-number v-model=\"form.zlgsl\" :min=\"0\" placeholder=\"请输入直流柜数量\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电杆数量：\" prop=\"dgsl\">\n              <el-input-number v-model=\"form.dgsl\" :min=\"0\" placeholder=\"请输入电杆数量\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电杆高度(m)：\" prop=\"dggd\">\n              <el-input-number v-model=\"form.dggd\" :min=\"0\" placeholder=\"请输入电杆高度\">\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n          <el-form-item label=\"箱式变型号：\" prop=\"xsbxh\">\n            <el-input v-model=\"form.xsbxh\" placeholder=\"请输入箱式变型号\"></el-input>\n          </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n          <el-form-item label=\"低压配电柜数量：\" prop=\"dypdgsl\">\n            <el-input-number v-model=\"form.dypdgsl\" :min=\"0\" placeholder=\"请输入低压配电柜数量\"></el-input-number>\n          </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"计量柜数量：\" prop=\"jlgsl\">\n              <el-input-number v-model=\"form.jlgsl\" :min=\"0\" placeholder=\"请输入计量柜数量\"></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"经度\" prop=\"jd\">\n              <el-input v-model=\"form.jd\" placeholder=\"请输入经度\"> </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"纬度\" prop=\"wd\">\n              <el-input v-model=\"form.wd\" placeholder=\"请输入纬度\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\" prop=\"bz\">\n              <el-input v-model=\"form.bz\" type=\"textarea\" rows=\"2\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\" v-show=\"!isDisabled\">\n        <el-button @click=\"pdsDialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"getDetermine\" >确 定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n\n</template>\n\n<script>\nimport {\n  getPdsList, addPds, removePds, getPdsTree, getOrganizationSelected\n} from '@/api/dagangOilfield/asset/pdsgl'\n  export default {\n    name: \"pdsgl\",\n    data() {\n      return {\n        ywbzList:[],\n        sbzt: [{\n          value: '在运',\n          label: '在运'\n        }, {\n          value: '停止使用',\n          label: '停止使用'\n        }\n        , {\n          value: '未就绪',\n          label: '未就绪'\n        }, {\n          value: '报废',\n          label: '报废'\n        }],\n        pdslx: [{\n          value: '箱式变电站',\n          label: '箱式变电站'\n        }, {\n          value: '柱上变台变',\n          label: '柱上变台变'\n        }\n        , {\n          value: '配电室',\n          label: '配电室'\n        }],\n        //树结构上面得筛选框参数\n        treeForm: {},\n        isDisabled: false,\n        tableAndPageInfo: {},\n        filterInfo: {\n          data: {\n            ssgs: [],\n            ssxlmc: '',\n            yxbh: '',\n            zt: []\n          },\n          fieldList: [\n            // {label: '所属公司', type: 'select', value: 'ssgs', multiple: true, options: []},\n            {label: '所属线路名称', type: 'input', value: 'ssxlmc'},\n            {label: '运行编号', type: 'input', value: 'yxbh'},\n            {label: '状态', type: 'select', value: 'ztList', multiple: true, options: [{\n                value: '在运',\n                label: '在运'\n              }, {\n                value: '停止使用',\n                label: '停止使用'\n              }\n                , {\n                  value: '未就绪',\n                  label: '未就绪'\n                }, {\n                  value: '报废',\n                  label: '报废'\n                }]},\n          ]\n        },\n        tableAndPageInfo1: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n            // {prop: 'ssgs', label: '所属公司', minWidth: '120'},\n            {prop: 'ssxlmc', label: '所属线路名称', minWidth: '180'},\n            {prop: 'ssxlbh', label: '所属线路编号', minWidth: '120'},\n            {prop: 'ssxdmc', label: '所属线段名称', minWidth: '180'},\n            {prop: 'pdsmc', label: '配电室名称', minWidth: '140'},\n            {prop: 'yxbh', label: '运行编号', minWidth: '120'},\n            {prop: 'ywbzmc', label: '运维班组', minWidth: '120'},\n            // {prop: 'erpBm', label: 'ERP编码', minWidth: '120'},\n            {prop: 'tyrq', label: '投运日期', minWidth: '120'},\n            {prop: 'sfjyhw', label: '是否具有环网', minWidth: '120'},\n            {prop: 'zt', label: '状态', minWidth: '120'},\n            {prop: 'sccj', label: '生产厂家', minWidth: '120'},\n            {prop: 'tffs', label: '通风方式', minWidth: '120'},\n            {prop: 'lx', label: '配电室类型', minWidth: '120'},\n            /*{\n              fixed: \"right\",\n              prop: 'operation',\n              label: '操作',\n              minWidth: '130px',\n              style: {display: 'block'},\n              operation: [\n                {name: '修改', clickFun: this.getUpdate},\n                {name: '详情', clickFun: this.getXq},\n              ]\n            },*/\n          ]\n        },\n        //设备详情页底部确认取消按钮控制\n        sbCommitDialogCotrol: true,\n        //弹出框tab页\n        activeTabName: \"sbDesc\",\n        //变电站展示\n        bdzShowTable: true,\n        //间隔展示\n        jgShowTable: false,\n        //设备展示\n        sbShowTable: false,\n        //设备弹出框\n        dialogFormVisible: false,\n        //变电站添加按钮弹出框\n        pdsDialogFormVisible: false,\n        //间隔添加按钮弹出框\n        jgDialogFormVisible: false,\n        //弹出框表单\n        form: {\n          //ssgs: undefined,\n          ssxlmc: undefined,\n          ssxlbh: undefined,\n          ssxdmc: undefined,\n          pdsmc: undefined,\n          yxbh: undefined,\n          erpBm: undefined,\n          bgr: undefined,\n          zcbdfs: undefined,\n          zcsx: undefined,\n          zcbh: undefined,\n          wbsYs: undefined,\n          zcxz: undefined,\n          tyrq: undefined,\n          sfjyhw: undefined,\n          zt: undefined,\n          dqtz: undefined,\n          sccj: undefined,\n          sgdw: undefined,\n          jzwcc: undefined,\n          jzwcz: undefined,\n          tffs: undefined,\n          dlqgsl: undefined,\n          dyhgqgsl: undefined,\n          drgsl: undefined,\n          dyjxfs: undefined,\n          dypxgsl: undefined,\n          fhkggsl: undefined,\n          fhkgrdqzhgsl: undefined,\n          jlgsl: undefined,\n          mlgsl: undefined,\n          pbsl: undefined,\n          pbzrl: undefined,\n          wz: undefined,\n          xdcgsl: undefined,\n          zlgsl: undefined,\n          zby    : undefined,\n          gyjxgsl: undefined,\n          gyjlgsl: undefined,\n          gycxgsl: undefined,\n          ptgsl  : undefined,\n          dyjxgsl: undefined,\n          dycxgsl: undefined,\n          dybcgsl: undefined,\n          dyjlgsl: undefined,\n          dyllgsl: undefined,\n          dgsl   : undefined,\n          dggd   : undefined,\n          xsbxh  : undefined,\n          dypdgsl: undefined,\n          lx     : undefined\n        },\n\n        loading: false,\n        //配电室树\n        treeOptions: [],\n        //删除是否可用\n        multipleSensor: true,\n        //查询参数\n        queryParams: {\n          pageNum: 1,\n          pageSize: 10,\n          roleKey: '',\n          roleName: '',\n          status: '',\n        },\n        showSearch: true,\n\n        rules:{\n          ssgs:[{required:true,message:'请选择所属公司',trigger:'change'}],\n          ssxlmc:[{required:true,message:'请输入所属线路名称',trigger:'blur'}],\n          ssxlbh:[{required:true,message:'请输入所属线路编号',trigger:'blur'}],\n          ssxdmc:[{required:true,message:'请输入所属线段名称',trigger:'blur'}],\n          pdsmc:[{required:true,message:'请输入配电室名称',trigger:'blur'}],\n          yxbh:[{required:true,message:'请输入运行编号',trigger:'blur'}],\n/*          erpBm:[{required:true,message:'请输入ERP编码',trigger:'blur'}],\n          bgr:[{required:true,message:'请输入保管人',trigger:'blur'}],\n          zcbdfs:[{required:true,message:'请输入资产变动方式',trigger:'blur'}],\n          zcsx:[{required:true,message:'请输入资产属性',trigger:'blur'}],\n          zcbh:[{required:true,message:'请输入资产编号',trigger:'blur'}],\n          wbsYs:[{required:true,message:'请输入WBS元素',trigger:'blur'}],\n          zcxz:[{required:true,message:'请输入资产性质',trigger:'blur'}],*/\n          tyrq:[{required:true,message:'请选择投运日期',trigger:'change'}],\n          sfjyhw:[{required:true,message:'请选择是否具有环网',trigger:'blur'}],\n          zt:[{required:true,message:'请选择状态',trigger:'change'}],\n          dqtz:[{required:true,message:'请输入地区特征',trigger:'blur'}],\n          sccj:[{required:true,message:'请输入生产厂家',trigger:'blur'}],\n          sgdw:[{required:true,message:'请输入施工单位',trigger:'blur'}],\n          jzwcc:[{required:true,message:'请输入建筑物尺寸',trigger:'blur'}],\n          jzwcz:[{required:true,message:'请输入建筑物材质',trigger:'blur'}],\n          tffs:[{required:true,message:'请输入通风方式',trigger:'blur'}],\n          //pbzrl:[{required:true,message:'请输入配变总容量',trigger:'blur'}],\n          lx:[{required:true,message:'请选择配电室类型',trigger:'blur'}]\n          // wz:[{required:true,message:'请输入位置',trigger:'blur'}]\n        }\n\n      };\n    },\n    watch: {},\n    created() {\n      //获取新的设备拓扑树\n      this.getPdsTreeInfo();\n      //初始化加载时加载所有变电站信息\n      this.newTestData = this.bdzList\n      this.tableAndPageInfo = {...this.tableAndPageInfo1}\n      this.getData()\n      this.getYwbzLis()\n    },\n    methods: {\n      getYwbzLis(){\n        getOrganizationSelected({parentId:\"3013\"}).then(res => {\n          this.ywbzList = res.data;\n        })\n      },\n      //获取新的设备拓扑树\n      getPdsTreeInfo() {\n        getPdsTree(this.treeForm).then(res => {\n          this.treeOptions = res.data;\n        })\n      },\n      /**\n       * 表格多选框\n       */\n      handleSelectionChange(selection) {\n        this.ids = selection.map(item => item.objId)\n        this.single = selection.length !== 1;\n        this.multiple = !selection.length;\n      },\n\n      //列表查询\n      async getData(params) {\n        const param = {...this.params, ...params}\n        await getPdsList(param).then(res => {\n          this.tableAndPageInfo1.tableData = res.data.records;\n          this.tableAndPageInfo1.tableData.forEach((item) => {\n            this.ywbzList.forEach((element) => {\n              if (item.ywbz == element.value) {\n                item.ywbzmc = element.label;\n              }\n            });\n          });\n          this.tableAndPageInfo1.pager.total = res.data.total;\n          //给页面赋值\n          this.tableAndPageInfo = {...this.tableAndPageInfo1}\n        });\n      },\n\n      //配电室添加按钮\n      pdsAddSensorButton() {\n        this.form={}\n        this.isDisabled = false;\n        this.pdsDialogFormVisible = true\n      },\n      //删除配电室\n      async deletePds(){\n        if (this.ids.length != 0) {\n          this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }).then(() => {\n              removePds(this.ids).then(({code })=>{\n                if(code==='0000'){\n                  this.$message({\n                    type: 'success',\n                    message: '删除成功!'\n                  });\n                  this.getData()\n                }else{\n                  this.$message({\n                    type: 'error',\n                    message: '删除失败!'\n                  });\n                }\n              })\n            }).catch(() => {\n              this.$message({\n                type: 'info',\n                message: '已取消删除'\n              });\n            });\n        }\n\n      },\n\n      //每页展示数量点击事件\n      handleSizeChange() {\n\n      },\n      //页码改变事件\n      handleCurrentChange() {\n\n      },\n      //树点击事件\n      async handleNodeClick(data) {\n        if (data.identifier === '1') {\n          await this.getData({objId: data.id})\n          await this.getXq(this.tableAndPageInfo.tableData[0])\n        }\n      },\n\n      //修改\n      getUpdate(row){\n        this.pdsDialogFormVisible = true;\n        this.form = {...row};\n        this.isDisabled = false;\n      },\n      //详情\n      getXq(row){\n        this.pdsDialogFormVisible = true;\n        this.form = {...row};\n        this.isDisabled = true;\n      },\n      //确定按钮\n      async getDetermine(){\n        this.$refs['form'].validate((valid) => {\n          if (valid){\n            addPds(this.form).then(res => {\n              if (res.code == \"0000\") {\n                this.$message.success(\"操作成功！\");\n                this.pdsDialogFormVisible = false;\n                this.getData();\n              }\n            });\n          }else{\n            setTimeout(() => {\n              var isError = document.getElementsByClassName(\"is-error\");\n              if (isError[0].querySelector('input')) {\n                isError[0].querySelector('input').focus();\n              } else if (isError[0].querySelector('textarea')) {\n                isError[0].querySelector('textarea').focus();\n              }\n            }, 1)\n            return false;\n          }\n        });\n      },\n      handleClose(){\n        this.form={};\n        this.$nextTick(() => {\n          this.form = this.$options.data().form;\n          this.resetForm(\"form\");\n        });\n      },\n      filterReset(){\n\n      },\n    }\n  };\n</script>\n\n<style lang='scss' scoped>\n  .head-container {\n    margin: 0 auto;\n    width: 100%;\n    height: calc(100vh - 225px);\n    overflow: auto;\n  }\n\n  .box-card {\n    margin-bottom: 15px;\n\n    .el-card__header {\n      background-color: rgb(235, 245, 255) !important;\n    }\n  }\n\n  .box-cardList {\n    height: 70%;\n  }\n\n  .item {\n    width: 200px;\n    float: left;\n  }\n\n  #main_container_dj {\n    height: calc(100vh - 84px);\n  }\n\n  .aside_height {\n    height: 81vh;\n  }\n\n  .defect .el-form-item:nth-child(odd) {\n    margin-right: 70px;\n  }\n\n  /*背景颜色调整*/\n  #main_container_dj, #main_container_dj .el-aside {\n    background-color: #b4caf1;\n  }\n\n  /deep/ .qxlr_dialog_insert .el-dialog__header {\n    background-color: #0cc283;\n  }\n\n  /deep/ .pmyBtn {\n    background: #0cc283;\n  }\n\n  /*!*弹出框内宽度设置*!*/\n\n  .el-select {\n    width: 100%;\n  }\n\n  .el-date-editor{\n    width: 100%;\n  }\n\n  .el-carousel__item h3 {\n    color: #475669;\n    font-size: 14px;\n    opacity: 0.75;\n    line-height: 150px;\n\n  }\n\n  .el-carousel__item:nth-child(2n) {\n    background-color: #99a9bf;\n  }\n\n  .el-carousel__item:nth-child(2n+1) {\n    background-color: #d3dce6;\n  }\n  //有子节点 且未展开\n  .el-tree ::v-deep .el-icon-caret-right:before {\n    background: url(\"../../../../assets/image/add.png\") no-repeat 0;\n    content: \"\";\n    display: block;\n    width: 16px;\n    height: 16px;\n    font-size: 16px;\n    background-size: 16px;\n  }\n  //有子节点 且已展开\n  .el-tree\n  ::v-deep\n  .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {\n    background: url(\"../../../../assets/image/prep.png\") no-repeat 0;\n    content: \"\";\n    display: block;\n    width: 16px;\n    height: 16px;\n    font-size: 16px;\n    background-size: 16px;\n  }\n  //没有子节点\n  .el-tree ::v-deep .el-tree-node__expand-icon.is-leaf::before {\n    background: transparent;\n    content: \"\";\n    display: block;\n    width: 16px;\n    height: 16px;\n    font-size: 16px;\n    background-size: 16px;\n  }\n</style>\n"]}]}