{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\syxmzq.vue?vue&type=template&id=7ccc6a29&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\syxmzq.vue", "mtime": 1706897323691}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}