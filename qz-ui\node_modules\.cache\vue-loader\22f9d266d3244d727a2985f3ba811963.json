{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\bdqxwh.vue?vue&type=style&index=0&id=17f7e4a2&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\bdqxwh.vue", "mtime": 1706897322432}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi8qIOiuvue9rua7muWKqOadoeeahOagt+W8jyAqLwo6Oi13ZWJraXQtc2Nyb2xsYmFyIHsKICB3aWR0aDogMTJweDsKfQoKLyog5rua5Yqo5qe9ICovCjo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sgewogIC8vLXdlYmtpdC1ib3gtc2hhZG93Omluc2V0MDA2cHhyZ2JhKDAsMCwwLDAuMyk7CiAgYm9yZGVyLXJhZGl1czogMTBweDsKfQoKLyog5rua5Yqo5p2h5ruR5Z2XICovCjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIgewogIGJvcmRlci1yYWRpdXM6IDEwcHg7CiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjEpOwogIC8vLXdlYmtpdC1ib3gtc2hhZG93OmdiYSgwLDAsMCwwLjUpOwp9Cgo6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iOndpbmRvdy1pbmFjdGl2ZSB7CiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjEpOwp9Ci5oZWFkLWNvbnRhaW5lciB7CiAgbWFyZ2luOiAwIGF1dG87CiAgd2lkdGg6IDk4JTsKICBoZWlnaHQ6IDgyLjZ2aDsKICBtYXgtaGVpZ2h0OiA4Mi42dmg7CiAgb3ZlcmZsb3c6IGF1dG87Cn0KLyrnu5nlt6bkvqfmlbDnu5PmnoRoZWFkZXLliqDpopzoibIqLwouYm94LWNhcmQgLmVsLWNhcmRfX2hlYWRlciB7CiAgYmFja2dyb3VuZDogIzExYmE2ZCAhaW1wb3J0YW50Owp9Ci5ib3gtY2FyZCB7CiAgbWFyZ2luOjA7Cn0KCi5pdGVtIHsKICB3aWR0aDogMjAwcHg7CiAgaGVpZ2h0OiAxNDhweDsKICBmbG9hdDogbGVmdDsKfQoKLnRyZWUgewogIG92ZXJmbG93LXk6IGhpZGRlbjsKICBvdmVyZmxvdy14OiBzY3JvbGw7CiAgd2lkdGg6IDgwcHg7CiAgaGVpZ2h0OiA1MDBweDsKfQoKLmVsLXRyZWUgewogIG1pbi13aWR0aDogMTAwJTsKICBkaXNwbGF5OiBpbmxpbmUtYmxvY2sgIWltcG9ydGFudDsKfQovZGVlcC8gLmVsLWRpYWxvZzpub3QoLmlzLWZ1bGxzY3JlZW4pIHsKICBtYXJnaW4tdG9wOiA4dmggIWltcG9ydGFudDsKfQo="}, {"version": 3, "sources": ["bdqxwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwnCA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "bdqxwh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl", "sourcesContent": ["<template>\n  <div class=\"app-container\" id=\"sbqxDiv\">\n    <!--左侧树组件-->\n    <el-row :gutter=\"1\">\n      <!--   左侧树   -->\n      <el-col :span=\"4\">\n        <el-card style=\"background:#e0f8ed;padding-top:10px;\" shadow=\"never\">\n          <div>\n            <el-col>\n              <el-form label-width=\"62px\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"快速查询:\" label-width=\"80px\">\n                    <el-input\n                      placeholder=\"输入关键字过滤\"\n                      v-model=\"filterText\"\n                      clearable\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-form>\n            </el-col>\n          </div>\n          <div class=\"text head-container\">\n            <el-col style=\"padding:0;\">\n              <el-tree\n                id=\"tree\"\n                :data=\"treeOptions\"\n                @node-click=\"handleNodeClick\"\n                :highlight-current=\"true\"\n                ref=\"tree\"\n                :filter-node-method=\"filterNode\"\n                node-key=\"id\"\n                :default-checked-keys=\"['0']\"\n                :default-expanded-keys=\"['0']\"\n                accordion\n              />\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :btnHidden=\"false\"\n          :width=\"{ labelWidth: 80, itemWidth: 165 }\"\n          @handleReset=\"filterReset\"\n        />\n        <el-white class=\"button-group1\">\n          <div class=\"button_btn\">\n            <el-button type=\"primary\" icon=\"el-icon-plus\" v-hasPermi=\"['bdqxwh:button:add']\" @click=\"addForm('sbbj')\">新增部件</el-button>\n            <el-button type=\"primary\" icon=\"el-icon-plus\" v-hasPermi=\"['bdqxwh:button:add']\" @click=\"addForm('sbbw')\">新增部位</el-button>\n            <el-button type=\"primary\" icon=\"el-icon-plus\" v-hasPermi=\"['bdqxwh:button:add']\" @click=\"addForm('qxms')\">新增隐患描述</el-button>\n            <el-button type=\"primary\" icon=\"el-icon-plus\" v-hasPermi=\"['bdqxwh:button:add']\" @click=\"addForm('flyj')\">新增分类依据</el-button>\n            <el-button type=\"success\" icon=\"el-icon-download\" @click=\"exportExcel\">导出</el-button>\n          </div>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            height=\"62.2vh\"\n            v-loading=\"load\"\n          >\n            <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\"\n                             width=\"200\" :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button type=\"text\" size=\"small\"\n                           @click=\"updateRow(scope.row)\" v-hasPermi=\"['bdqxwh:button:update']\" title=\"修改\"  class='el-icon-edit'\n                >\n                </el-button>\n                <el-button type=\"text\" size=\"small\"\n                           @click=\"deleteRow(scope.row)\" v-hasPermi=\"['bdqxwh:button:delete']\" title=\"删除\" class='el-icon-delete'\n                >\n                </el-button>\n                <el-button type=\"text\" size=\"small\"\n                           @click=\"viewFun(scope.row)\" title=\"查看\" class='el-icon-view'\n                >\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!--  新增设备部件  -->\n    <el-dialog title=\"新增设备部件\" :visible.sync=\"isShowSbbj\" width=\"58%\" @close=\"closeFun('sbbj')\" v-dialogDrag>\n      <el-form label-width=\"140px\" :rules=\"sbbjRules\" :model=\"sbbjForm\" ref=\"sbbjForm\">\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备隐患</span>\n          </div>\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sblxbm\" label=\"设备类型\">\n                <el-select placeholder=\"设备类型\" v-model=\"sbbjForm.sblxbm\" style=\"width:80%\"\n                           @change=\"sblxChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in sblxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sbbj\" label=\"设备部件\">\n                <el-input v-model=\"sbbjForm.sbbj\" placeholder=\"请输入设备部件\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sbbw\" label=\"设备部位\">\n                <el-input v-model=\"sbbjForm.sbbw\" placeholder=\"请输入设备部位\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\n                <el-select placeholder=\"隐患等级\" v-model=\"sbbjForm.qxdj\" style=\"width:80%\"\n                           filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in qxdjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"隐患描述:\" prop=\"qxms\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"sbbjForm.qxms\" placeholder=\"请输入隐患描述\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"sbbjForm.flyj\" placeholder=\"请输入分类依据\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"sbbjForm.jsyy\" placeholder=\"请输入技术原因\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun('sbbj')\" size=\"small\">取 消</el-button>\n        <el-button type=\"primary\" size=\"small\" @click=\"saveForm('sbbjForm')\">保存</el-button>\n      </div>\n    </el-dialog>\n\n    <!--  新增设备部位  -->\n    <el-dialog title=\"新增设备部位\" :visible.sync=\"isShowSbbw\" width=\"58%\" @close=\"closeFun('sbbw')\" v-dialogDrag>\n      <el-form label-width=\"140px\" :rules=\"sbbwRules\" :model=\"sbbwForm\" ref=\"sbbwForm\">\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备隐患</span>\n          </div>\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sblxbm\" label=\"设备类型\">\n                <el-select placeholder=\"设备类型\" v-model=\"sbbwForm.sblxbm\" style=\"width:80%\"\n                           @change=\"sblxChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in sblxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"parentSbbj\" label=\"设备部件\">\n                <el-select placeholder=\"设备部件\" v-model=\"sbbwForm.parentSbbj\" style=\"width:80%\"\n                           @change=\"sbbjChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in sbbjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sbbw\" label=\"设备部位\">\n                <el-input v-model=\"sbbwForm.sbbw\" placeholder=\"请输入设备部位\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\n                <el-select placeholder=\"隐患等级\" v-model=\"sbbwForm.qxdj\" style=\"width:80%\"\n                           filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in qxdjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"隐患描述:\" prop=\"qxms\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"sbbwForm.qxms\" placeholder=\"请输入隐患描述\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"sbbwForm.flyj\" placeholder=\"请输入分类依据\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"sbbwForm.jsyy\" placeholder=\"请输入技术原因\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun('sbbw')\" size=\"small\">取 消</el-button>\n        <el-button type=\"primary\" size=\"small\" @click=\"saveForm('sbbwForm')\">保存</el-button>\n      </div>\n    </el-dialog>\n\n    <!--  新增隐患描述  -->\n    <el-dialog title=\"新增隐患描述\" :visible.sync=\"isShowQxms\" width=\"58%\" @close=\"closeFun('qxms')\" v-dialogDrag>\n      <el-form label-width=\"140px\" :rules=\"qxmsRules\" :model=\"qxmsForm\" ref=\"qxmsForm\">\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备隐患</span>\n          </div>\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sblxbm\" label=\"设备类型\">\n                <el-select placeholder=\"设备类型\" v-model=\"qxmsForm.sblxbm\" style=\"width:80%\"\n                           @change=\"sblxChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in sblxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"parentSbbj\" label=\"设备部件\">\n                <el-select placeholder=\"设备部件\" v-model=\"qxmsForm.parentSbbj\" style=\"width:80%\"\n                           @change=\"sbbjChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in sbbjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"parentSbbw\" label=\"设备部位\">\n                <el-select placeholder=\"设备部位\" v-model=\"qxmsForm.parentSbbw\" style=\"width:80%\"\n                           @change=\"sbbwChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in sbbwList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\n                <el-select placeholder=\"隐患等级\" v-model=\"qxmsForm.qxdj\" style=\"width:80%\"\n                           filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in qxdjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"隐患描述:\" prop=\"qxms\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"qxmsForm.qxms\" placeholder=\"请输入隐患描述\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"qxmsForm.flyj\" placeholder=\"请输入分类依据\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"qxmsForm.jsyy\" placeholder=\"请输入技术原因\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun('qxms')\" size=\"small\">取 消</el-button>\n        <el-button type=\"primary\" size=\"small\" @click=\"saveForm('qxmsForm')\">保存</el-button>\n      </div>\n    </el-dialog>\n\n    <!--  新增分类依据  -->\n    <el-dialog title=\"新增分类依据\" :visible.sync=\"isShowFlyj\"  width=\"58%\" @close=\"closeFun('flyj')\" v-dialogDrag>\n      <el-form label-width=\"140px\" :rules=\"flyjRules\" :model=\"flyjForm\" ref=\"flyjForm\">\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备隐患</span>\n          </div>\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sblxbm\" label=\"设备类型\">\n                <el-select placeholder=\"设备类型\" v-model=\"flyjForm.sblxbm\" style=\"width:80%\"\n                           @change=\"sblxChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in sblxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"parentSbbj\" label=\"设备部件\">\n                <el-select placeholder=\"设备部件\" v-model=\"flyjForm.parentSbbj\" style=\"width:80%\"\n                           @change=\"sbbjChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in sbbjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"parentSbbw\" label=\"设备部位\">\n                <el-select placeholder=\"设备部位\" v-model=\"flyjForm.parentSbbw\" style=\"width:80%\"\n                           @change=\"sbbwChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in sbbwList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\n                <el-select placeholder=\"隐患等级\" v-model=\"flyjForm.qxdj\" style=\"width:80%\"\n                           filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in qxdjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"隐患描述:\" prop=\"parentQxms\">\n                <el-select placeholder=\"隐患描述\" v-model=\"flyjForm.parentQxms\" style=\"width:80%\"\n                           @change=\"qxmsChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in qxmsList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"flyjForm.flyj\" placeholder=\"请输入分类依据\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"flyjForm.jsyy\" placeholder=\"请输入技术原因\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun('flyj')\" size=\"small\">取 消</el-button>\n        <el-button v-if=\"addFlyj\" type=\"primary\" size=\"small\" @click=\"saveForm('flyjForm')\">保存</el-button>\n        <el-button v-if=\"!addFlyj\" type=\"primary\" size=\"small\" @click=\"saveForm('flyjForm')\">保存</el-button>\n      </div>\n    </el-dialog>\n\n    <!--  设备隐患查看  -->\n    <el-dialog title=\"设备隐患查看\" :visible.sync=\"isShowDetail\" v-if=\"isShowDetail\"  width=\"58%\" @close=\"closeFun('view')\" v-dialogDrag >\n      <el-form label-width=\"140px\" :model=\"viewForm\" ref=\"viewForm\">\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备隐患</span>\n          </div>\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sblx\" label=\"设备类型\">\n                <el-input v-model=\"viewForm.sblx\" placeholder=\"请输入设备类型\" style=\"width: 92%\"\n                          :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sbbj\" label=\"设备部件\">\n                <el-input v-model=\"viewForm.sbbj\" placeholder=\"请输入设备部件\" style=\"width: 92%\"\n                          :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sbbw\" label=\"设备部位\">\n                <el-input v-model=\"viewForm.sbbw\" placeholder=\"请输入设备部位\" style=\"width: 92%\"\n                          :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\n                <el-input v-model=\"viewForm.qxdj\" placeholder=\"请输入隐患等级\" style=\"width: 92%\"\n                          :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"隐患描述:\" prop=\"qxms\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"viewForm.qxms\" placeholder=\"请输入隐患描述\" style=\"width: 92%\"\n                          :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"viewForm.flyj\" placeholder=\"请输入分类依据\" style=\"width: 92%\"\n                          :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"viewForm.jsyy\" placeholder=\"请输入技术原因\" style=\"width: 92%\"\n                          :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n    </el-dialog>\n  </div>\n\n</template>\n\n<script>\nimport {getQxList,\n  getQxsbTree,\n  getSblxList,\n  getSbbjList,\n  getSbbwList,\n  getQxmsList,\n  getFlyjList,\n  addFlyj,\n  updateFlyj,\n  deleteFlyjById,\n  addQxms,\n  addSbbw,\n  addSbbj,\n} from '@/api/dagangOilfield/bzgl/sbqxwh/sbqxwh'\nimport {getDictTypeData} from '@/api/system/dict/data'\nimport { Loading } from 'element-ui'\nimport {exportExcel} from '@/api/bzgl/ysbzk/ysbzk'\n\nexport default {\n  name: 'sblxwh',\n  data() {\n    return {\n      load:false,\n      addFlyj:false,//是否新增分类依据\n      filterInfo: {\n        data: {\n          sbbj: '',\n          sbbw: '',\n          qxms: '',\n          flyj: '',\n          qxdj: '',\n          jsyy: '',\n        },\n        fieldList: [\n          { label: '设备部件', type: 'input', value: 'sbbj' },\n          { label: '设备部位', type: 'input', value: 'sbbw'},\n          { label: '隐患描述', type: 'input', value: 'qxms'},\n          { label: '隐患等级', type: 'select', value: 'qxdj', options: []},\n          { label: '分类依据', type: 'input', value: 'flyj'},\n          { label: '技术原因', type: 'input', value: 'jsyy'}\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: 'sblx', label: '设备类型', minWidth: '140' },\n          { prop: 'sbbj', label: '设备部件', minWidth: '180' },\n          { prop: 'sbbw', label: '设备部位', minWidth: '130' },\n          { prop: 'qxms', label: '隐患描述', minWidth: '200' },\n          { prop: 'flyj', label: '分类依据', minWidth: '220',showPop:true},\n          { prop: 'qxdj', label: '隐患等级', minWidth: '80'},\n          { prop: 'jsyy', label: '技术原因', minWidth: '120' }\n        ]\n      },\n      queryParams:{},\n      treeOptions: [], //组织树\n      treeNodeData:{},//点击后的树节点数据\n      isShowDetail:false,\n      isShowSbbj:false,//新增弹框\n      isShowSbbw:false,\n      isShowQxms:false,\n      isShowFlyj:false,\n      flyjForm:{},//表单\n      flyjRules:{\n        sblxbm: [\n          { required: true, message: '设备类型不能为空', trigger: 'select' }\n        ],\n        parentSbbj: [\n          { required: true, message: '设备部件不能为空', trigger: 'select' }\n        ],\n        parentSbbw: [\n          { required: true, message: '设备部位不能为空', trigger: 'select' }\n        ],\n        qxdj: [\n          { required: true, message: '隐患等级不能为空', trigger: 'select' }\n        ],\n        parentQxms: [\n          { required: true, message: '隐患描述不能为空', trigger: 'select' }\n        ],\n        flyj: [\n          { required: true, message: '分类依据不能为空', trigger: 'blur' }\n        ],\n        jsyy: [\n          { required: true, message: '技术原因不能为空', trigger: 'blur' }\n        ],\n      },//校验规则\n      qxmsForm:{},//表单\n      qxmsRules:{\n        sblxbm: [\n          { required: true, message: '设备类型不能为空', trigger: 'select' }\n        ],\n        parentSbbj: [\n          { required: true, message: '设备部件不能为空', trigger: 'select' }\n        ],\n        parentSbbw: [\n          { required: true, message: '设备部位不能为空', trigger: 'select' }\n        ],\n        qxdj: [\n          { required: true, message: '隐患等级不能为空', trigger: 'select' }\n        ],\n        qxms: [\n          { required: true, message: '隐患描述不能为空', trigger: 'blur' }\n        ],\n        flyj: [\n          { required: true, message: '分类依据不能为空', trigger: 'blur' }\n        ],\n        jsyy: [\n          { required: true, message: '技术原因不能为空', trigger: 'blur' }\n        ],\n      },//校验规则\n      sbbwForm:{},//表单\n      sbbwRules:{\n        sblxbm: [\n          { required: true, message: '设备类型不能为空', trigger: 'select' }\n        ],\n        parentSbbj: [\n          { required: true, message: '设备部件不能为空', trigger: 'select' }\n        ],\n        sbbw: [\n          { required: true, message: '设备部位不能为空', trigger: 'blur' }\n        ],\n        qxdj: [\n          { required: true, message: '隐患等级不能为空', trigger: 'select' }\n        ],\n        qxms: [\n          { required: true, message: '隐患描述不能为空', trigger: 'blur' }\n        ],\n        flyj: [\n          { required: true, message: '分类依据不能为空', trigger: 'blur' }\n        ],\n        jsyy: [\n          { required: true, message: '技术原因不能为空', trigger: 'blur' }\n        ],\n      },//校验规则\n      sbbjForm:{},//表单\n      sbbjRules:{\n        sblxbm: [\n          { required: true, message: '设备类型不能为空', trigger: 'select' }\n        ],\n        sbbj: [\n          { required: true, message: '设备部件不能为空', trigger: 'blur' }\n        ],\n        sbbw: [\n          { required: true, message: '设备部位不能为空', trigger: 'blur' }\n        ],\n        qxdj: [\n          { required: true, message: '隐患等级不能为空', trigger: 'select' }\n        ],\n        qxms: [\n          { required: true, message: '隐患描述不能为空', trigger: 'blur' }\n        ],\n        flyj: [\n          { required: true, message: '分类依据不能为空', trigger: 'blur' }\n        ],\n        jsyy: [\n          { required: true, message: '技术原因不能为空', trigger: 'blur' }\n        ],\n      },//校验规则\n      sblxList:[],//设备类型下拉框选项\n      sbbjList:[],//设备部件下拉框选项\n      sbbwList:[],//设备部位下拉框选项\n      qxmsList:[],//隐患描述下拉框选项\n      flyjList:[],//分类依据下拉框选项\n      qxdjList:[],//隐患等级下拉框选项\n      qxlb:'1',//隐患类别（变电）\n      filterText:'',//过滤\n      viewForm:{},//查看表单\n      loading: null,\n    }\n  },\n  watch: {\n    filterText(val) {\n      this.$refs.tree.filter(val);\n    },\n  },\n  created() {\n    this.queryParams.qxlb = this.qxlb;\n    this.getData();\n    this.getTreeData();\n    //设备类型下拉框\n    this.getSblxList();\n    //隐患等级下拉框\n    this.getQxdjList();\n  },\n  methods: {\n    //获取设备类型下拉框\n    async getSblxList(){\n      await getSblxList({qxlb:this.qxlb}).then(res=>{\n        this.sblxList = res.data;\n      })\n    },\n    //获取设备部件下拉框\n    async getSbbjList(sblx){\n      await getSbbjList({qxlb:this.qxlb,sblx:sblx}).then(res=>{\n        this.sbbjList = res.data;\n      })\n    },\n    //获取设备部位下拉框\n    async getSbbwList(sbbj){\n      await getSbbwList({qxlb:this.qxlb,sbbj:sbbj}).then(res=>{\n        this.sbbwList = res.data;\n      })\n    },\n    //获取隐患描述下拉框\n    async getQxmsList(sbbw){\n      await getQxmsList({qxlb:this.qxlb,sbbw:sbbw}).then(res=>{\n        this.qxmsList = res.data;\n      })\n    },\n    //获取分类依据下拉框\n    async getFlyjList(qxms){\n      await getFlyjList({qxlb:this.qxlb,qxms:qxms}).then(res=>{\n        this.flyjList = res.data;\n      })\n    },\n    //获取隐患等级字典数据\n    async getQxdjList(){//查询隐患等级字典\n      await getDictTypeData('sbqxwh_qxdj').then(res=>{\n        this.qxdjList = res.data;\n        //给筛选条件赋值\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == 'qxdj') {\n            item.options = this.qxdjList\n          }\n        })\n      })\n    },\n    //编辑\n    async updateRow(row){\n      //开启遮罩层\n      this.loading = Loading.service({\n        lock: true, //lock的修改符--默认是false\n        text: \"加载中，请稍后\", //显示在加载图标下方的加载文案\n        spinner: \"el-icon-loading\", //自定义加载图标类名\n        background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n        target: document.querySelector(\"#sbqxDiv\"),\n      });\n      this.flyjForm = {...row};\n      //下拉框回显\n      await this.getSbbjList(row.sblxbm);\n      await this.getSbbwList(row.parentSbbj);\n      await this.getQxmsList(row.parentSbbw);\n      this.isShowDetail = false;\n      this.addFlyj = false;//不是新增\n      this.isShowFlyj = true;\n      this.loading.close();//关闭遮罩层\n    },\n    //删除\n    deleteRow(row){\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        deleteFlyjById(row).then(res => {\n          if(res.code === '0000'){\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            });\n            this.getData();\n          }else{\n            this.$message({\n              type: 'error',\n              message: '删除失败!'\n            });\n          }\n        })\n      })\n    },\n    //查看\n    viewFun(row){\n      this.viewForm = {...row};\n      this.isShowDetail = true;\n    },\n    //新增\n    addForm(formType){\n      //先清空下拉框的值\n      this.sbbjList = [];\n      this.sbbwList = [];\n      this.qxmsList = [];\n      //如果树节点有值，则带过来\n      let sblx = this.queryParams.sblxbm?this.queryParams.sblxbm:'';\n      let sbbj = this.queryParams.parentSbbj?this.queryParams.parentSbbj:'';\n      let sbbw = this.queryParams.parentSbbw?this.queryParams.parentSbbw:'';\n      this.isShowDetail = false;\n      switch (formType){\n        case 'sbbj'://设备部件\n          this.sbbjForm = {};\n          // this.$set(this.sbbjForm,'sblxbm',sblx);\n          this.isShowSbbj = true;\n          break;\n        case 'sbbw'://设备部位\n          this.sbbwForm = {};\n          // this.$set(this.sbbwForm,'sblxbm',sblx);\n          // this.$set(this.sbbwForm,'parentSbbj',sbbj);\n          this.isShowSbbw = true;\n          break;\n        case 'qxms'://隐患描述\n          this.qxmsForm = {};\n          // this.$set(this.qxmsForm,'sblxbm',sblx);\n          // this.$set(this.qxmsForm,'parentSbbj',sbbj);\n          // this.$set(this.qxmsForm,'parentSbbw',sbbw);\n          this.isShowQxms = true;\n          break;\n        case 'flyj'://分类依据\n          this.flyjForm = {};\n          // this.$set(this.flyjForm,'sblxbm',sblx);\n          // this.$set(this.flyjForm,'parentSbbj',sbbj);\n          // this.$set(this.flyjForm,'parentSbbw',sbbw);\n          this.addFlyj = true;\n          this.isShowFlyj = true;\n          break;\n        default:\n          break;\n      }\n    },\n    //保存\n    async saveForm(formType){\n      await this.$refs[formType].validate((valid) => {\n        if (valid) {\n          let saveForm = {...{qxlb:this.qxlb}};\n          switch (formType){\n            case 'flyjForm'://新增分类依据\n              saveForm = {...saveForm,...this.flyjForm};\n              this.qxmsList.forEach(item=>{\n                if(item.value === saveForm.parentQxms){\n                  saveForm.qxms = item.label;\n                }\n              })\n              if(this.addFlyj){//新增\n                addFlyj(saveForm).then(res=>{\n                  if (res.code === '0000') {\n                    this.$message.success('操作成功');\n                    this.isShowFlyj = false;\n                    this.isShowQxms = false;\n                    this.isShowSbbw = false;\n                    this.isShowSbbj = false;\n                    this.isShowDetail = false;\n                    this.getData();\n                    //关闭弹框\n                  } else {\n                    this.$message.error('操作失败')\n                  }\n                });\n              }else{\n                updateFlyj(saveForm).then(res=> {//编辑\n                  if (res.code === '0000') {\n                    this.$message.success('操作成功');\n                    this.isShowFlyj = false;\n                    this.isShowQxms = false;\n                    this.isShowSbbw = false;\n                    this.isShowSbbj = false;\n                    this.isShowDetail = false;\n                    this.getData();\n                    //关闭弹框\n                  } else {\n                    this.$message.error('操作失败')\n                  }\n                })\n              }\n              break;\n            case 'qxmsForm'://新增隐患描述\n              saveForm = {...saveForm,...this.qxmsForm};\n              this.sbbwList.forEach(item=>{\n                if(item.value === saveForm.parentSbbw){\n                  saveForm.sbbw = item.label;\n                }\n              })\n              addQxms(saveForm).then(res=>{\n                if (res.code === '0000') {\n                  this.$message.success('操作成功');\n                  this.isShowFlyj = false;\n                  this.isShowQxms = false;\n                  this.isShowSbbw = false;\n                  this.isShowSbbj = false;\n                  this.isShowDetail = false;\n                  this.getData();\n                  //关闭弹框\n                } else {\n                  this.$message.error('操作失败')\n                }\n              });\n              break;\n            case 'sbbwForm'://新增隐患描述\n              saveForm = {...saveForm,...this.sbbwForm};\n              this.sbbjList.forEach(item=>{\n                if(item.value === saveForm.parentSbbj){\n                  saveForm.sbbj = item.label;\n                }\n              })\n              addSbbw(saveForm).then(res=>{\n                if (res.code === '0000') {\n                  this.$message.success('操作成功');\n                  this.isShowFlyj = false;\n                  this.isShowQxms = false;\n                  this.isShowSbbw = false;\n                  this.isShowSbbj = false;\n                  this.isShowDetail = false;\n                  this.getData();\n                  //关闭弹框\n                } else {\n                  this.$message.error('操作失败')\n                }\n              });\n              break;\n            case 'sbbjForm'://新增隐患描述\n              saveForm = {...saveForm,...this.sbbjForm};\n              this.sblxList.forEach(item=>{\n                if(item.value === saveForm.sblxbm){\n                  saveForm.sblx = item.label;\n                }\n              })\n              addSbbj(saveForm).then(res=>{\n                if (res.code === '0000') {\n                  this.$message.success('操作成功');\n                  this.isShowFlyj = false;\n                  this.isShowQxms = false;\n                  this.isShowSbbw = false;\n                  this.isShowSbbj = false;\n                  this.isShowDetail = false;\n                  this.getData();\n                  //关闭弹框\n                } else {\n                  this.$message.error('操作失败')\n                }\n              });\n              break;\n            default:\n              break;\n          }\n        } else {\n          this.$message.error('校验未通过！')\n          return false\n        }\n      })\n    },\n    //设备类型下拉框事件\n    async sblxChangeFun(val){\n      this.clearFormField('sblx');\n      await this.getSbbjList(val);\n    },\n    //设备部件下拉框事件\n    async sbbjChangeFun(val){\n      this.clearFormField('sbbj');\n      await this.getSbbwList(val);\n    },\n    //设备部位下拉框事件\n    async sbbwChangeFun(val){\n      this.clearFormField('sbbw');\n      await this.getQxmsList(val);\n    },\n    //隐患描述下拉框事件\n    async qxmsChangeFun(val){\n      this.clearFormField('qxms');\n      await this.getFlyjList(val);\n    },\n    //清空字段值\n    clearFormField(type){\n      switch (type){\n        case 'sblx'://设备类型\n          this.$set(this.sbbjForm,'sbbj','');\n          this.$set(this.sbbwForm,'parentSbbj','');\n          this.$set(this.qxmsForm,'parentSbbj','');\n          this.$set(this.flyjForm,'parentSbbj','');\n          this.clearFormField('sbbj');\n          break;\n        case 'sbbj'://设备部件\n          this.$set(this.sbbwForm,'sbbw','');\n          this.$set(this.qxmsForm,'parentSbbw','');\n          this.$set(this.flyjForm,'parentSbbw','');\n          this.clearFormField('sbbw');\n          break;\n        case 'sbbw'://设备部位\n          this.$set(this.qxmsForm,'qxms','');\n          this.$set(this.flyjForm,'parentQxms','');\n          this.clearFormField('qxms');\n          break;\n        case 'qxms'://隐患描述\n          this.$set(this.flyjForm,'flyj','');\n          break;\n          default:\n            break;\n      }\n    },\n    //关闭\n    closeFun(type){\n      this.isShowDetail = false;\n      switch (type){\n        case 'sbbj':\n          this.isShowSbbj = false;\n          break;\n        case 'sbbw':\n          this.isShowSbbw = false;\n          break;\n        case 'qxms':\n          this.isShowQxms = false;\n          break;\n        case 'flyj':\n          this.isShowFlyj = false;\n          break;\n        case 'view':\n          this.isShowDetail = false;\n          break;\n        default:\n          this.isShowSbbj = false;\n          this.isShowSbbw = false;\n          this.isShowQxms = false;\n          this.isShowFlyj = false;\n          this.isShowDetail = false;\n          break;\n      }\n    },\n    //重置按钮\n    filterReset() {\n      this.queryParams = {qxlb:this.qxlb};//重置条件\n    },\n\n    //树监听事件\n    filterNode(value, data) {\n      if (!value) return true\n      return data.label.indexOf(value) !== -1\n    },\n    getTreeData(){\n      getQxsbTree({qxlb:this.qxlb}).then(res=>{\n        this.treeOptions = res.data;\n      })\n    },\n    //树节点点击事件\n    handleNodeClick(node) {\n      this.treeNodeData = node\n      if (node.identifier === '1') {//设备类型\n        this.queryParams.sblxbm = node.id;\n        this.queryParams.parentSbbj = '';\n        this.queryParams.parentSbbw = '';\n      } else if (node.identifier === '2') {//设备部件\n        this.queryParams.sblxbm = '';\n        this.queryParams.parentSbbj = node.id;\n        this.queryParams.parentSbbw = '';\n      } else if (node.identifier === '3') {//设备部位\n        this.queryParams.sblxbm = '';\n        this.queryParams.parentSbbj = '';\n        this.queryParams.parentSbbw = node.id;\n      }else {\n        this.queryParams = {qxlb:this.qxlb}\n      }\n      this.getData()\n    },\n    //查询列表\n    getData(params) {\n      this.load = true\n      this.queryParams = {...this.queryParams, ...params}\n      getQxList(this.queryParams).then(res => {\n        this.tableAndPageInfo.tableData = res.data.records\n        this.tableAndPageInfo.pager.total = res.data.total\n        this.load = false\n      })\n    },\n    //导出excel\n    exportExcel() {\n      let fileName = \"隐患标准库\";\n      let exportUrl = \"/bzqxFlyj\";\n      // if(this.selectData.length > 0){\n      //   // this.$message.warning('请在左侧勾选要导出的数据')\n      //   // return\n      //   exportExcel(exportUrl, this.queryParams, fileName);\n      // }\n      exportExcel(exportUrl, this.queryParams, fileName);\n    }\n  }\n}\n</script>\n\n<style lang='scss' scoped>\n/* 设置滚动条的样式 */\n::-webkit-scrollbar {\n  width: 12px;\n}\n\n/* 滚动槽 */\n::-webkit-scrollbar-track {\n  //-webkit-box-shadow:inset006pxrgba(0,0,0,0.3);\n  border-radius: 10px;\n}\n\n/* 滚动条滑块 */\n::-webkit-scrollbar-thumb {\n  border-radius: 10px;\n  background: rgba(0, 0, 0, 0.1);\n  //-webkit-box-shadow:gba(0,0,0,0.5);\n}\n\n::-webkit-scrollbar-thumb:window-inactive {\n  background: rgba(0, 0, 0, 0.1);\n}\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  height: 82.6vh;\n  max-height: 82.6vh;\n  overflow: auto;\n}\n/*给左侧数结构header加颜色*/\n.box-card .el-card__header {\n  background: #11ba6d !important;\n}\n.box-card {\n  margin:0;\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.tree {\n  overflow-y: hidden;\n  overflow-x: scroll;\n  width: 80px;\n  height: 500px;\n}\n\n.el-tree {\n  min-width: 100%;\n  display: inline-block !important;\n}\n/deep/ .el-dialog:not(.is-fullscreen) {\n  margin-top: 8vh !important;\n}\n</style>\n<style>\n\n</style>\n\n\n\n\n\n"]}]}