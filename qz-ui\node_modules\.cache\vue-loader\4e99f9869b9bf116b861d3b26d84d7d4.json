{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\xldzcz\\dzczml.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\xldzcz\\dzczml.vue", "mtime": 1719917121296}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgZ2V0TGlzdCwKICBxdWVyeVpiLAogIHNhdmVPclVwZGF0ZSwKICByZW1vdmUsCiAgZXhwb3J0V29yZEJ5c2VsZWN0aW9uLAogIGV4cG9ydFdvcmRCeXBhcmFtcwp9IGZyb20gIkAvYXBpL3l4Z2wvc2R5eGdsL3NkZHpjem1sIjsKaW1wb3J0IHsgc2F2ZU9yVXBkYXRlcyB9IGZyb20gIkAvYXBpL3l4Z2wvc2R5eGdsL3NkZHpjenAiOwppbXBvcnQgRWxlY3Ryb25pY0F1dGhEaWFsb2cgZnJvbSAiY29tL0VsZWN0cm9uaWNBdXRoRGlhbG9nIjsKaW1wb3J0IHNkeGxTZWxlY3RlZCBmcm9tICJAL3ZpZXdzL2RhZ2FuZ09pbGZpZWxkL3hqZ2wvc2R4ai9zZHhsU2VsZWN0ZWQxIjsKaW1wb3J0IHsgc2F2ZU9yVXBkYXRlQ3pwIH0gZnJvbSAiQC9hcGkveXhnbC9iZHl4Z2wvYmRkemN6bWwiOwppbXBvcnQgYWN0aXZpdGkgZnJvbSAiY29tL2FjdGl2aXRpX2N6cCI7CmltcG9ydCB7IHVwZGF0ZUJ5SWQgfSBmcm9tICJAL2FwaS95eGdsL2JkeXhnbC9iZGR6Y3pwIjsKaW1wb3J0IHsgZ2V0VVVJRCB9IGZyb20gIkAvdXRpbHMvcnVveWkiOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJkemN6bWwiLAogIGNvbXBvbmVudHM6IHsgRWxlY3Ryb25pY0F1dGhEaWFsb2csIHNkeGxTZWxlY3RlZCwgYWN0aXZpdGkgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgaGFzU3VwZXJSb2xlOnRoaXMuJHN0b3JlLmdldHRlcnMuaGFzU3VwZXJSb2xlLAogICAgICAvLyDlpJrpgInmoYbpgInkuK3nmoRpZAogICAgICBpZHM6IFtdLAogICAgICAvLyDlpJrpgInmoYbpgInpgInkuK3nmoTmlbDmja4KICAgICAgc2VsZWN0RGF0YTogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHVuZGVmaW5lZCwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgLy/lt6XkvZzmtYHkvKDlhaXlj4LmlbAKICAgICAgcHJvY2Vzc0RhdGE6IHsKICAgICAgICBwcm9jZXNzRGVmaW5pdGlvbktleTogImN6cGxjIiwKICAgICAgICBidXNpbmVzc0tleTogIiIsCiAgICAgICAgYnVzaW5lc3NUeXBlOiAi5YCS6Ze45pON5L2c56WoIiwKICAgICAgICB2YXJpYWJsZXM6IHt9LAogICAgICAgIGRlZmF1bHRGcm9tOiB0cnVlLAogICAgICAgIG5leHRVc2VyOiAiIiwKICAgICAgICBwcm9jZXNzVHlwZTogImNvbXBsZXRlIgogICAgICB9LAogICAgICBpc1Nob3c6IGZhbHNlLAogICAgICAvL+W8ue<PERSON>H<PERSON><PERSON>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"}, {"version": 3, "sources": ["dzczml.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwb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file": "dzczml.vue", "sourceRoot": "src/views/dagangOilfield/czpgl/xldzcz", "sourcesContent": ["<template>\n  <div class=\"app-container\" style=\"padding:1px\">\n    <!--    <el-white>-->\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n      @handleReset=\"getReset\"\n    />\n    <!--    </el-white>-->\n\n    <div>\n      <el-white class=\"button-group\">\n        <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n          <el-col\n            style=\"\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n          \"\n          >\n            <div style=\"margin-bottom: 8px;\">\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-plus\"\n                @click=\"getInster\"\n                v-hasPermi=\"['xldzczml:button:add']\"\n                >新增\n              </el-button>\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-download\"\n                @click=\"exportWord\"\n                >导出</el-button\n              >\n            </div>\n          </el-col>\n        </el-row>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"selectChange\"\n          height=\"65vh\"\n          v-loading=\"loading\"\n        >\n          <el-table-column\n            slot=\"table_eight\"\n            align=\"center\"\n            fixed=\"right\"\n            style=\"display: block\"\n            label=\"操作\"\n            width=\"160\"\n            :resizable=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                @click=\"getDetails(scope.row)\"\n                title=\"详情\"\n                class=\"el-icon-view\"\n              ></el-button>\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                v-if=\"(scope.row.createBy === currentUser || hasSuperRole) && !scope.row.czp\"\n                @click=\"getUpdate(scope.row)\"\n                title=\"编辑\"\n                class=\"el-icon-edit\"\n              >\n              </el-button>\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                v-if=\"(scope.row.createBy === currentUser || hasSuperRole) && !scope.row.czp\"\n                @click=\"deleteRow(scope.row)\"\n                title=\"删除\"\n                class=\"el-icon-delete\"\n              >\n              </el-button>\n            </template>\n          </el-table-column>\n        </comp-table>\n      </el-white>\n    </div>\n\n    <!-- 详情/新增/修改  倒闸操作命令页面-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"50%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <div>\n          <el-row>\n            <el-col :span=\"12\">\n              <el-form-item label=\"编号：\" prop=\"bh\">\n                <el-input\n                  v-model=\"form.bh\"\n                  :disabled=\"true\"\n                  placeholder=\"保存后自动生成\"\n                />\n              </el-form-item>\n            </el-col>\n            <!--<el-col :span=\"12\">\n              <el-form-item label=\"状态\" prop=\"status\">\n                <el-input v-model=\"form.status\" :disabled=\"true\" placeholder=\"请输入状态\"/>\n              </el-form-item>\n            </el-col>-->\n            <el-col :span=\"12\">\n              <el-form-item label=\"线路名称\" prop=\"xlmc\">\n                <el-input\n                  v-model=\"form.xlmc\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请输入线路名称\"\n                  v-on:click.native=\"sysbSelectedClick()\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"通知时间\" prop=\"tzsj\">\n                <el-date-picker\n                  v-model=\"form.tzsj\"\n                  :disabled=\"isDisabled\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"预备人下令人\" prop=\"yblxlr\">\n                <el-input\n                  v-model=\"form.yblxlr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请输入预备人下令人\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"预备令接令人\" prop=\"ybljlr\">\n                <el-input\n                  v-model=\"form.ybljlr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请输入预备令接令人\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"倒闸操作时间\" prop=\"dzczsj\">\n                <el-date-picker\n                  v-model=\"form.dzczsj\"\n                  :disabled=\"isDisabled\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  :placeholder=\"isDisabled ? '' : '选择日期时间'\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"恢复操作时间\" prop=\"hfczsj\">\n                <el-date-picker\n                  v-model=\"form.hfczsj\"\n                  :disabled=\"isDisabled\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  :placeholder=\"isDisabled ? '' : '选择日期时间'\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <el-col :span=\"24\">\n              <el-form-item label=\"工作名称\" prop=\"gzmc\">\n                <el-input\n                  type=\"textarea\"\n                  :rows=\"2\"\n                  v-model=\"form.gzmc\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请输入工作名称\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n        <!--预览信息-->\n        <el-dialog :title=\"titleyl\" :visible.sync=\"yl\" append-to-body>\n          <el-table\n            :data=\"propTableData.colFirst\"\n            height=\"200\"\n            border\n            stripe\n            style=\"width: 100%\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <el-table-column\n              align=\"center\"\n              prop=\"xh\"\n              width=\"100px\"\n              label=\"序号\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <el-input\n                  placeholder=\"请输入序号\"\n                  v-model=\"scope.row.xh\"\n                  :disabled=\"true\"\n                ></el-input>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作任务\">\n              <template slot-scope=\"scope\">\n                <el-input\n                  placeholder=\"操作任务\"\n                  v-model=\"scope.row.czrw\"\n                  :disabled=\"true\"\n                ></el-input>\n              </template>\n            </el-table-column>\n          </el-table>\n        </el-dialog>\n        <!--列表-->\n        <div>\n          <el-white class=\"mb8 pull-right\">\n            <el-button\n              type=\"info\"\n              @click=\"handleYlChange\"\n              style=\"text-align: right\"\n              >预览</el-button\n            >\n          </el-white>\n          <!--第一个表格-->\n          <el-table\n            :data=\"propTableData.colFirst\"\n            height=\"200\"\n            border\n            stripe\n            style=\"width: 100%\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <el-table-column\n              align=\"center\"\n              prop=\"xh\"\n              width=\"100px\"\n              label=\"顺序号\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <el-input-number\n                  size=\"small\"\n                  v-model=\"scope.row.xh\"\n                  :min=\"1\"\n                  :precision=\"0\"\n                  controls-position=\"right\"\n                  :disabled=\"isDisabled\"\n                ></el-input-number>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作任务\">\n              <template slot-scope=\"scope\">\n                <el-input\n                  placeholder=\"操作任务\"\n                  v-model=\"scope.row.czrw\"\n                  :disabled=\"isDisabled\"\n                ></el-input>\n              </template>\n            </el-table-column>\n\n            <el-table-column align=\"center\" width=\"80\">\n              <template slot=\"header\" slot-scope=\"scope\">\n                <el-button\n                  size=\"small\"\n                  type=\"primary\"\n                  icon=\"el-icon-plus\"\n                  :disabled=\"isDisabled\"\n                  @click=\"listFirstAdd(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n              <template slot-scope=\"scope\">\n                <el-button\n                  type=\"text\"\n                  icon=\"el-icon-delete\"\n                  :disabled=\"isDisabled\"\n                  @click=\"listFirstDel(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">关 闭</el-button>\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"saveRow\"\n          >保 存</el-button\n        >\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"createCzp\"\n          >开操作票\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 详情/新增/修改  倒闸操作票页面-->\n    <el-dialog\n      :title=\"titleCzp\"\n      :visible.sync=\"isShowDetailsCzp\"\n      width=\"50%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"130px\" ref=\"formCzp\" :model=\"formCzp\">\n        <div>\n          <div>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"线路名称\" prop=\"xlmc\">\n                  <el-input\n                    v-model=\"formCzp.xlmc\"\n                    :disabled=\"isDisabled\"\n                    placeholder=\"请输入线路名称\"\n                  />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"24\">\n                <el-form-item label=\"工作名称\" prop=\"gzmc\">\n                  <el-input\n                    type=\"textarea\"\n                    :rows=\"2\"\n                    v-model=\"form.gzmc\"\n                    :disabled=\"isDisabled\"\n                    placeholder=\"请输入工作名称\"\n                  />\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </div>\n\n          <!--列表-->\n          <div>\n            <!--第一个表格-->\n            <el-table\n              :data=\"propTableDataCzp.colFirst\"\n              height=\"200\"\n              border\n              stripe\n              style=\"width: 100%\"\n              :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n            >\n              <el-table-column\n                align=\"center\"\n                prop=\"xh\"\n                width=\"100px\"\n                label=\"序号\"\n                sortable\n              >\n                <template slot-scope=\"scope\">\n                  <el-input\n                    placeholder=\"请输入序号\"\n                    v-model=\"scope.row.xh\"\n                    :disabled=\"isDisabled\"\n                  ></el-input>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n                <template slot-scope=\"scope\">\n                  <el-input\n                    placeholder=\"请输入操作项目\"\n                    v-model=\"scope.row.czrw\"\n                    :disabled=\"isDisabled\"\n                  ></el-input>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" width=\"80\">\n                <template slot=\"header\" slot-scope=\"scope\">\n                  <el-button\n                    size=\"small\"\n                    type=\"primary\"\n                    icon=\"el-icon-plus\"\n                    :disabled=\"isDisabled\"\n                    @click=\"listFirstAddCzp(scope.$index, scope.row)\"\n                  ></el-button>\n                </template>\n                <template slot-scope=\"scope\">\n                  <el-button\n                    type=\"text\"\n                    icon=\"el-icon-delete\"\n                    :disabled=\"isDisabled\"\n                    @click=\"listFirstDelCzp(scope.$index, scope.row)\"\n                  ></el-button>\n                </template>\n              </el-table-column>\n            </el-table>\n          </div>\n        </div>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"closeCzp\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"saveRowCzp\">确 认</el-button>\n        <el-button type=\"primary\" @click=\"submitCzp\">上 报</el-button>\n      </div>\n    </el-dialog>\n\n    <!--线路选择组件-->\n    <el-dialog\n      title=\"线路选择\"\n      :visible.sync=\"isShowXlDialog\"\n      width=\"20%\"\n      v-if=\"isShowXlDialog\"\n      v-dialogDrag\n    >\n      <sdxl-selected\n        @closeSysbSelectDialog=\"handleControlSysbSelectDialog\"\n        @handleAcceptSbData=\"handleAcceptSbData\"\n      ></sdxl-selected>\n    </el-dialog>\n\n    <!--  工作流  -->\n    <activiti\n      ref=\"activiti\"\n      :processData=\"processData\"\n      :isShow=\"isShow\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n    ></activiti>\n  </div>\n</template>\n\n<script>\nimport {\n  getList,\n  queryZb,\n  saveOrUpdate,\n  remove,\n  exportWordByselection,\n  exportWordByparams\n} from \"@/api/yxgl/sdyxgl/sddzczml\";\nimport { saveOrUpdates } from \"@/api/yxgl/sdyxgl/sddzczp\";\nimport ElectronicAuthDialog from \"com/ElectronicAuthDialog\";\nimport sdxlSelected from \"@/views/dagangOilfield/xjgl/sdxj/sdxlSelected1\";\nimport { saveOrUpdateCzp } from \"@/api/yxgl/bdyxgl/bddzczml\";\nimport activiti from \"com/activiti_czp\";\nimport { updateById } from \"@/api/yxgl/bdyxgl/bddzczp\";\nimport { getUUID } from \"@/utils/ruoyi\";\n\nexport default {\n  name: \"dzczml\",\n  components: { ElectronicAuthDialog, sdxlSelected, activiti },\n  data() {\n    return {\n      hasSuperRole:this.$store.getters.hasSuperRole,\n      // 多选框选中的id\n      ids: [],\n      // 多选框选选中的数据\n      selectData: [],\n      // 非单个禁用\n      single: undefined,\n      // 非多个禁用\n      multiple: true,\n      loading: false,\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"czplc\",\n        businessKey: \"\",\n        businessType: \"倒闸操作票\",\n        variables: {},\n        defaultFrom: true,\n        nextUser: \"\",\n        processType: \"complete\"\n      },\n      isShow: false,\n      //弹出框标题\n      activitiOption: { title: \"上报\" },\n      rules: {\n        xlmc: [\n          { required: true, message: \"线路名称不能为空\", trigger: \"select\" }\n        ],\n        tzsj: [\n          { required: true, message: \"通知时间不能为空\", trigger: \"blur\" }\n        ],\n        yblxlr: [\n          { required: true, message: \"预备人下令人不能为空\", trigger: \"blur\" }\n        ],\n        ybljlr: [\n          { required: true, message: \"预备人接令人不能为空\", trigger: \"blur\" }\n        ]\n      }, //表单必填校验\n      titleyl: \"\",\n      // 获取当前登录人账号\n      currentUser: this.$store.getters.name,\n      yl: false,\n      //线路选择弹出\n      isShowXlDialog: false,\n      // 多选框选中的id\n      ids: [],\n      //form表单\n      form: {\n        bh: \"\",\n        xlmc: \"\",\n        tzsj: \"\",\n        yblxlr: \"\",\n        czrw: \"\",\n        ybljlr: \"\",\n        status: \"\",\n        gzmc: \"\"\n      },\n      // 操作票form表单\n      formCzp: {\n        bh: \"\",\n        xlmc: \"\",\n        czr: \"\",\n        jhr: \"\",\n        sdshr: \"\",\n        lx: 1, //输电\n        colFirst: []\n      },\n      //弹出框中表格数据\n      propTableData: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //弹出框中表格数据\n      propTableDataCzp: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //操作命令详情弹框是否显示\n      isShowDetails: false,\n      //操作票弹框是否显示\n      isShowDetailsCzp: false,\n      //是否禁用\n      isDisabled: false,\n      authIsShow: false,\n      isSubmit: false,\n      //标题\n      title: \"\",\n      titleCzp: \"\",\n      filterInfo: {\n        data: {\n          column: \"\"\n        }, //查询条件\n        fieldList: [\n          { label: \"编号\", value: \"bh\", type: \"input\", clearable: true },\n          /*{\n            label: '状态',\n            value: 'status',\n            type: 'select',\n            clearable: true,\n            options: [{ label: '已执行', value: '已执行' }, { label: '未执行', value: '未执行' }]\n          },*/\n          { label: \"线路名称\", value: \"xlmc\", type: \"input\", clearable: true },\n          {\n            label: \"通知时间\",\n            value: \"tzsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          {\n            label: \"预备令下令人\",\n            value: \"yblxlr\",\n            type: \"input\",\n            clearable: true\n          },\n          {\n            label: \"预备令接令人\",\n            value: \"ybljlr\",\n            type: \"input\",\n            clearable: true\n          },\n          { label: \"工作名称\", value: \"gzmc\", type: \"input\", clearable: true }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"编号\", prop: \"bh\", minWidth: \"120\" },\n          /*{ label: '状态', prop: 'status', minWidth: '60' },*/\n          { label: \"线路名称\", prop: \"xlmc\", minWidth: \"120\" },\n          { label: \"通知时间\", prop: \"tzsj\", minWidth: \"160\" },\n          { label: \"预备令下令人\", prop: \"yblxlr\", minWidth: \"120\" },\n          { label: \"预备令接令人\", prop: \"ybljlr\", minWidth: \"120\" },\n          { label: \"工作名称\", prop: \"gzmc\", minWidth: \"120\" }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        bh: \"\",\n        xlmc: \"\",\n        tzsj: \"\",\n        yblxlr: \"\",\n        czrw: \"\",\n        ybljlr: \"\"\n      },\n      selectRows: []\n    };\n  },\n  mounted() {\n    //列表查询\n    this.getData();\n  },\n  methods: {\n    //工作流回传数据\n    async todoResult(data) {\n      let row = {\n        objId: data.businessKey,\n        isStart: 1,\n        isBack: data.processType === \"rollback\" ? 1 : 0\n      };\n      row.status = \"2\";\n      row.fgsspr = data.nextUser;\n      let { code } = await updateById(row);\n      if (code === \"0000\") {\n        await this.getData();\n      } else {\n        this.$message.warning(\"操作失败\");\n      }\n    },\n    //关闭弹窗\n    closeActiviti() {\n      if (this.$refs.activiti.$refs.form) {\n        this.$refs.activiti.$refs.form.resetFields();\n      }\n      this.isShow = false;\n    },\n    handleYlChange() {\n      this.titleyl = \"查看操作项目\";\n      this.yl = true;\n    },\n    //线路选择接收数据\n    handleAcceptSbData(sbData) {\n      // this.form.xlmcbm = sbData.id;\n      let str = \"\";\n      sbData.forEach(e => {\n        str += e.label + \",\";\n      });\n      //去掉最后一个逗号\n      str = str.substr(0, str.length - 1);\n      this.form.xlmc = str;\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        this.loading = true;\n        param.mySorts = [{ prop: \"updateTime\", asc: false }];\n        const { data, code } = await getList(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          this.loading = false;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //根据主表查询子表方法\n    async getListZb(row) {\n      try {\n        const { data, code } = await queryZb({ objId: row.objId });\n        if (code === \"0000\") {\n          this.propTableData.colFirst = data;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //新增按钮\n    async getInster() {\n      this.propTableData.colFirst = [];\n      this.title = \"输电倒闸操作命令新增\";\n      this.isDisabled = false;\n      if (this.single) {\n        this.form = this.single;\n        const { data, code } = await queryZb({ objId: this.form.objId });\n        if (code === \"0000\") {\n          this.propTableData.colFirst = data;\n          this.propTableData.colFirst.forEach(e => {\n            e.objId = \"\";\n          });\n        }\n        this.form.objId = \"\";\n        this.form.czp = \"\";\n        this.form.bh = \"\";\n      } else {\n        this.form = {};\n      }\n      this.isShowDetails = true;\n      this.form.status = \"\";\n    },\n    //修改按钮\n    getUpdate(row) {\n      this.getListZb(row);\n      this.title = \"输电倒闸操作命令修改\";\n      this.isDisabled = false;\n      this.form = { ...row };\n      this.isShowDetails = true;\n    },\n    //详情按钮\n    getDetails(row) {\n      this.getListZb(row);\n      this.title = \"输电倒闸操作命令详情\";\n      this.form = { ...row };\n      this.isDisabled = true;\n      this.isShowDetails = true;\n    },\n    //开操作票按钮\n    async createCzp() {\n      await this.$refs[\"form\"].validate(async valid => {\n        if (valid) {\n          try {\n            this.form.colFirst = this.propTableData.colFirst;\n            this.form.objIdList = this.ids;\n            let { code, data } = await saveOrUpdate(this.form);\n            if (code === \"0000\") {\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.isShowDetails = false;\n              await this.getData();\n              this.titleCzp = \"操作票开票\";\n              this.isShowDetailsCzp = true;\n              this.isDisabled = true;\n              this.formCzp = {};\n              //清除校验提示\n              this.$nextTick(function() {\n                this.$refs[\"formCzp\"].clearValidate();\n              });\n              this.formCzp.xlmc = data.xlmc;\n              this.formCzp.gzmc = data.gzmc;\n              this.formCzp.czml = data.objId;\n              this.formCzp.status = \"0\";\n              this.formCzp.fgs = \"3010\";\n              //输电\n              this.formCzp.lx = 1;\n              this.propTableDataCzp.colFirst = this.propTableData.colFirst;\n              this.formCzp.czxs = this.propTableData.colFirst.length;\n              this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n            }\n          } catch (e) {\n            console.log(e);\n          }\n        }\n      });\n    },\n    // 开操作票页 确认按钮\n    async saveRowCzp() {\n      this.formCzp.colFirst = this.propTableDataCzp.colFirst;\n      this.formCzp.objIdList = this.ids;\n      // let tableValid = this.propTableData.colFirst.some(item => !item.czrw)\n      // if (tableValid) {\n      //   this.$message.error(\"操作任务存在空项，请检查\");\n      // }\n      saveOrUpdates(this.formCzp).then(res => {\n        if (res.code === \"0000\") {\n          this.$message.success(\"操作成功\");\n          this.isShowDetailsCzp = false;\n          //重置page页从1开始\n          this.tableAndPageInfo.pager.pageResize = \"Y\";\n          this.getData();\n        }\n      });\n    },\n\n    //直接上报操作票\n    submitCzp() {\n      this.formCzp.colFirst = this.propTableDataCzp.colFirst;\n      this.formCzp.objIdList = this.ids;\n      saveOrUpdateCzp(this.formCzp).then(res => {\n        if (res.code === \"0000\") {\n          //重置page页从1开始\n          this.tableAndPageInfo.pager.pageResize = \"Y\";\n          this.getData();\n          let data = res.data;\n          this.isShowDetailsCzp = false;\n          this.processData.variables.pass = true;\n          this.processData.businessKey = data.objId;\n          this.processData.processType = \"complete\";\n          this.activitiOption.title = \"提交\";\n          this.processData.defaultFrom = true;\n          this.processData.rylx = \"分公司审核人\";\n          this.processData.dw = data.fgs;\n          this.processData.personGroupId = 14;\n          this.processData.routePath = \"/czpgl/xldzcz/dzczp\";\n          this.isShow = true;\n        }\n      });\n    },\n\n    saveRow() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          try {\n            this.form.colFirst = this.propTableData.colFirst;\n            this.form.objIdList = this.ids;\n            saveOrUpdate(this.form).then(res => {\n              if (res.code === \"0000\") {\n                this.$message.success(\"操作成功\");\n              }\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n              this.isShowDetails = false;\n            });\n          } catch (e) {\n            console.log(e);\n          }\n        }\n      });\n    },\n    //删除按钮\n    async deleteRow(row) {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove([row.objId]).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    //表格新增\n    listFirstAdd() {\n      let row = {\n        objId: \"\",\n        czrw: \"\",\n        uuid: getUUID()\n      };\n      this.propTableData.colFirst.push(row);\n      this.propTableData.sel = row;\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //表格删除\n    listFirstDel(index, row) {\n      if (row.objId) {\n        this.ids.push(row.objId);\n      }\n      this.propTableData.colFirst.splice(index, 1);\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //表格新增\n    listFirstAddCzp() {\n      let row = {\n        isSet: true,\n        // xh:'',\n        czrw: \"\",\n        xlr: \"\",\n        xlsj: \"\",\n        hlsj: \"\",\n        sfwc: \"\"\n      };\n      this.propTableDataCzp.colFirst.push(row);\n      this.propTableDataCzp.sel = row;\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //表格删除\n    listFirstDelCzp(index, row) {\n      this.ids.push(row.objId);\n      this.propTableDataCzp.colFirst.splice(index, 1);\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false;\n    },\n    //关闭弹窗\n    closeCzp() {\n      this.isShowDetailsCzp = false;\n    },\n    //选择行\n    selectChange(selection) {\n      this.single = selection.length > 0 ? { ...selection[0] } : undefined;\n      this.ids = selection.map(item => item.objId);\n      // this.single = selection.length !== 1\n      // this.multiple = !selection.length\n      this.selectData = selection;\n    },\n    //重置按钮\n    getReset() {\n      this.params = {\n        bh: \"\",\n        xlmc: \"\",\n        tzsj: \"\",\n        yblxlr: \"\",\n        czrw: \"\",\n        ybljlr: \"\"\n      };\n    },\n    //关闭线路弹窗\n    handleControlSysbSelectDialog(isShow) {\n      this.isShowXlDialog = isShow;\n    },\n    //线路选择事件\n    sysbSelectedClick() {\n      this.isShowXlDialog = true;\n    },\n    //导出word\n    exportWord() {\n      let params = {\n        data: this.params,\n        url: \"bzSddzczml\"\n      };\n      let fileName = \"线路倒闸操作命令记录\";\n      if (!this.selectData.length > 0) {\n        params.data = this.params;\n        exportWordByparams(params, fileName);\n      } else {\n        params.data = this.selectData;\n        exportWordByselection(params, fileName);\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.card-style {\n  background-color: #e0eff2;\n  line-height: 3vh;\n  border-radius: 3px;\n  font-weight: 800;\n  padding-left: 1vw;\n  margin-bottom: 3vh;\n}\n\n/*控制input输入框边框是否显示*/\n.elInput >>> .el-input__inner {\n  border: 0;\n}\n\n.center {\n  text-align: center; /*让div内部文字居中*/\n}\n</style>\n"]}]}