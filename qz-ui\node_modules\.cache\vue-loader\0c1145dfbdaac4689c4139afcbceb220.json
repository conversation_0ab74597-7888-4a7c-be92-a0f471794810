{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\pdgl\\pdsgl.vue?vue&type=template&id=8aa5922e&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\pdgl\\pdsgl.vue", "mtime": 1706897324785}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}