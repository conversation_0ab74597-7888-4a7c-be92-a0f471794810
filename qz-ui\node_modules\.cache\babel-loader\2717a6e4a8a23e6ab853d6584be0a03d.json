{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\asset\\gfjgtz.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\asset\\gfjgtz.js", "mtime": 1730101099991}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagangOilfield/asset/gfjgtz.js"], "names": ["baseUrl", "tzUrl", "getOrganizationSelected", "params", "api", "requestGet", "getTreeInfo", "requestPost", "getNewTreeInfo", "getZtpjTreeInfo", "getLocationsBdzListPage", "addBdz", "removeBdz", "addJg", "removeJg", "getJgInfoList", "getFgsByBdzId", "addAsset", "removeAsset", "getAssetListInfo", "exportExcel", "url", "fileName", "addd<PERSON><PERSON><PERSON><PERSON>", "copyAsset"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAEA,IAAMA,OAAO,GAAG,cAAhB;AACA,IAAMC,KAAK,GAAG,eAAd;AAEA;;;;;;AAKO,SAASC,uBAAT,CAAiCC,MAAjC,EAAyC;AAC9C,SAAOC,iBAAIC,UAAJ,CAAeL,OAAO,GAAG,iCAAzB,EAA4DG,MAA5D,EAAoE,CAApE,CAAP;AACD;AAED;;;;;;;AAKO,SAASG,WAAT,CAAqBH,MAArB,EAA6B;AAClC,SAAOC,iBAAIG,WAAJ,CAAgBP,OAAO,GAAG,6BAA1B,EAAyDG,MAAzD,EAAiE,CAAjE,CAAP;AACD;AAED;;;;;;;AAKO,SAASK,cAAT,CAAwBL,MAAxB,EAAgC;AACrC,SAAOC,iBAAIG,WAAJ,CAAgBP,OAAO,GAAG,gCAA1B,EAA4DG,MAA5D,EAAoE,CAApE,CAAP;AACD;AACD;;;;;;;AAKO,SAASM,eAAT,CAAyBN,MAAzB,EAAiC;AACtC,SAAOC,iBAAIG,WAAJ,CAAgBP,OAAO,GAAG,iCAA1B,EAA6DG,MAA7D,EAAqE,CAArE,CAAP;AACD;AAED;;;;;;AAIO,SAASO,uBAAT,CAAiCP,MAAjC,EAAyC;AAC9C,SAAOC,iBAAIG,WAAJ,CAAgBP,OAAO,GAAG,gCAA1B,EAA4DG,MAA5D,EAAoE,CAApE,CAAP;AACD;AAED;;;;;;;AAKO,SAASQ,MAAT,CAAgBR,MAAhB,EAAwB;AAC7B,SAAOC,iBAAIG,WAAJ,CAAgBP,OAAO,GAAG,wBAA1B,EAAoDG,MAApD,EAA4D,CAA5D,CAAP;AACD;AAED;;;;;;;AAKO,SAASS,SAAT,CAAmBT,MAAnB,EAA2B;AAChC,SAAOC,iBAAIG,WAAJ,CAAgBP,OAAO,GAAG,2BAA1B,EAAuDG,MAAvD,EAA+D,CAA/D,CAAP;AACD;AAED;;;;;;;AAKO,SAASU,KAAT,CAAeV,MAAf,EAAuB;AAC5B,SAAOC,iBAAIG,WAAJ,CAAgBP,OAAO,GAAG,6BAA1B,EAAyDG,MAAzD,EAAiE,CAAjE,CAAP;AACD;AAED;;;;;;;AAKO,SAASW,QAAT,CAAkBX,MAAlB,EAA0B;AAC/B;AACA,SAAOC,iBAAIG,WAAJ,CAAgBP,OAAO,GAAG,+BAA1B,EAA2DG,MAA3D,EAAmE,CAAnE,CAAP;AACD;AAED;;;;;;;AAKO,SAASY,aAAT,CAAuBZ,MAAvB,EAA+B;AACpC,SAAOC,iBAAIG,WAAJ,CAAgBP,OAAO,GAAG,mCAA1B,EAA+DG,MAA/D,EAAuE,CAAvE,CAAP;AAED,C,CAED;;;AACO,SAASa,aAAT,CAAuBb,MAAvB,EAA+B;AACpC,SAAOC,iBAAIG,WAAJ,CAAgBP,OAAO,GAAG,+BAA1B,EAA2DG,MAA3D,EAAmE,CAAnE,CAAP;AAED;AAGD;;;;;;;AAKO,SAASc,QAAT,CAAkBd,MAAlB,EAA0B;AAC/B,SAAOC,iBAAIG,WAAJ,CAAgBP,OAAO,GAAG,4BAA1B,EAAwDG,MAAxD,EAAgE,CAAhE,CAAP;AACD;AAED;;;;;;;AAKO,SAASe,WAAT,CAAqBf,MAArB,EAA6B;AAClC;AACA,SAAOC,iBAAIG,WAAJ,CAAgBP,OAAO,GAAG,iCAA1B,EAA6DG,MAA7D,EAAqE,CAArE,CAAP;AACD;AAED;;;;;;;AAKO,SAASgB,gBAAT,CAA0BhB,MAA1B,EAAkC;AACvC,SAAOC,iBAAIG,WAAJ,CAAgBP,OAAO,GAAG,+BAA1B,EAA2DG,MAA3D,EAAmE,CAAnE,CAAP;AAED;;AAEM,SAASiB,WAAT,CAAqBC,GAArB,EAAyBlB,MAAzB,EAAgCmB,QAAhC,EAAyC;AAC9C,SAAOlB,iBAAIgB,WAAJ,CAAgBpB,OAAO,GAACqB,GAAxB,EAA4BlB,MAA5B,EAAmCmB,QAAnC,CAAP;AACD;AAED;;;;;;;AAKO,SAASC,WAAT,CAAqBpB,MAArB,EAA4B;AAC/B,SAAOC,iBAAIG,WAAJ,CAAgBN,KAAK,GAAC,sBAAtB,EAA6CE,MAA7C,EAAqD,CAArD,CAAP;AACH;AAED;;;;;;;AAKO,SAASqB,SAAT,CAAmBrB,MAAnB,EAA0B;AAC7B,SAAOC,iBAAIG,WAAJ,CAAgBP,OAAO,GAAC,iBAAxB,EAA0CG,MAA1C,EAAkD,CAAlD,CAAP;AACH", "sourcesContent": ["import api from '@/utils/request'\r\n\r\nconst baseUrl = \"/manager-api\";\r\nconst tzUrl = \"/activiti-api\";\r\n\r\n/**\r\n * 获取组织结构下拉框数据\r\n * @param params\r\n * @returns {Promise | Promise<any>}\r\n */\r\nexport function getOrganizationSelected(params) {\r\n  return api.requestGet(baseUrl + '/select/getOrganizationSelected', params, 2);\r\n}\r\n\r\n/**\r\n *\r\n * @param params\r\n * @returns {Promise | Promise<unknown>}\r\n */\r\nexport function getTreeInfo(params) {\r\n  return api.requestPost(baseUrl + '/equipListOfGfz/getTreeInfo', params, 2);\r\n}\r\n\r\n/**\r\n * 新的获取变电设备左侧设备树\r\n * @param params\r\n * @returns {Promise | Promise<any>}\r\n */\r\nexport function getNewTreeInfo(params) {\r\n  return api.requestPost(baseUrl + '/equipListOfGfz/getNewTreeInfo', params, 2);\r\n}\r\n/**\r\n * 状态评价变电设备左侧树\r\n * @param params\r\n * @returns {Promise | Promise<any>}\r\n */\r\nexport function getZtpjTreeInfo(params) {\r\n  return api.requestPost(baseUrl + '/equipListOfGfz/getZtpjTreeInfo', params, 2);\r\n}\r\n\r\n/**\r\n * 查询变电站台账信息\r\n * @returns {Promise | Promise<unknown>}\r\n */\r\nexport function getLocationsBdzListPage(params) {\r\n  return api.requestPost(baseUrl + '/equipListOfGfz/getBdzListPage', params, 2);\r\n}\r\n\r\n/**\r\n * 新增变电站\r\n * @param params\r\n * @returns {Promise<unknown>}\r\n */\r\nexport function addBdz(params) {\r\n  return api.requestPost(baseUrl + '/equipListOfGfz/addBdz', params, 2);\r\n}\r\n\r\n/**\r\n * 删除变电站信息\r\n * @param params\r\n * @returns {Promise | Promise<unknown>}\r\n */\r\nexport function removeBdz(params) {\r\n  return api.requestPost(baseUrl + '/equipListOfGfz/removeBdz', params, 2);\r\n}\r\n\r\n/**\r\n * 新增间隔\r\n * @param params\r\n * @returns {Promise | Promise<unknown>}\r\n */\r\nexport function addJg(params) {\r\n  return api.requestPost(baseUrl + '/equipListOfGfz/addLocation', params, 2);\r\n}\r\n\r\n/**\r\n * 删除间隔\r\n * @param params\r\n * @returns {Promise | Promise<unknown>}\r\n */\r\nexport function removeJg(params) {\r\n  // return api.requestPost(baseUrl + '/equipListOfGfz/removeLocations', params, 2);\r\n  return api.requestPost(baseUrl + '/equipListOfGfz/removeBdAsset', params, 2);\r\n}\r\n\r\n/**\r\n * 间隔列表\r\n * @param params\r\n * @returns {Promise | Promise<unknown>}\r\n */\r\nexport function getJgInfoList(params) {\r\n  return api.requestPost(baseUrl + '/equipListOfGfz/listLocationsPage', params, 2);\r\n\r\n}\r\n\r\n// 根据变电站ID查询所属分公司\r\nexport function getFgsByBdzId(params) {\r\n  return api.requestPost(baseUrl + '/equipListOfGfz/getFgsByBdzid', params, 2);\r\n\r\n}\r\n\r\n\r\n/**\r\n * 新增设备\r\n * @param params\r\n * @returns {Promise | Promise<unknown>}\r\n */\r\nexport function addAsset(params) {\r\n  return api.requestPost(baseUrl + '/equipListOfGfz/addBdAsset', params, 2);\r\n}\r\n\r\n/**\r\n * 删除设备\r\n * @param params\r\n * @returns {Promise | Promise<unknown>}\r\n */\r\nexport function removeAsset(params) {\r\n  // return api.requestPost(baseUrl + '/equipListOfGfz/removeBdAsset', params, 2);\r\n  return api.requestPost(baseUrl + '/equipListOfGfz/removeLocations', params, 2);\r\n}\r\n\r\n/**\r\n * 间隔列表\r\n * @param params\r\n * @returns {Promise | Promise<unknown>}\r\n */\r\nexport function getAssetListInfo(params) {\r\n  return api.requestPost(baseUrl + '/equipListOfGfz/assetListPage', params, 2);\r\n\r\n}\r\n\r\nexport function exportExcel(url,params,fileName){\r\n  return api.exportExcel(baseUrl+url,params,fileName)\r\n}\r\n\r\n/**\r\n * 变电站新增时发送通知\r\n * @param {*} params\r\n * @returns\r\n */\r\nexport function adddwzyfstz(params){\r\n    return api.requestPost(tzUrl+'/api/notice/dwzyfstz',params, 2);\r\n}\r\n\r\n/**\r\n * 设备复制\r\n * @param {*} params\r\n * @returns\r\n */\r\nexport function copyAsset(params){\r\n    return api.requestPost(baseUrl+'/gfsb/copyAsset',params, 2);\r\n}\r\n\r\n"]}]}