{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\sybgjlcomment\\symbSelect.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\sybgjlcomment\\symbSelect.vue", "mtime": 1706897323434}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCi8v5byV5YWlanF1ZXJ5LOaaguaXtuayoeeUqAppbXBvcnQgJCBmcm9tICJqcXVlcnkiCmltcG9ydCB7CiAgYWRkTWJHbHhtQmF0Y2hUb01ieG0sCiAgZ2V0UGFnZURhdGFMaXN0VG9zeW1iLAogIGdldFN5bWJHbHN5eG1EYXRhTGlzdEJ5UGFnZSwKICBnZXRYbUxpcmFyeURhdGEsCiAgcmVtb3ZlLAogIHNhdmVPclVwZGF0ZSwKICBnZXRNYkdsTXBpbmZvRGF0YSwKICBnZXRNYkdsWG1BbmRCdwp9IGZyb20gJ0AvYXBpL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvc3ltYndoJwppbXBvcnQge2dldERldmljZUNsYXNzVHJlZU5vZGVCeVBpZH0gZnJvbSAnQC9hcGkvZGFnYW5nT2lsZmllbGQvYnpnbC9zYmx4d2gvc2JseHdoJwppbXBvcnQge2dldEdsU3l6eG1EYXRhTGlzdEJ5UGFnZX0gZnJvbSAnQC9hcGkvZGFnYW5nT2lsZmllbGQvYnpnbC9zeXhtJwppbXBvcnQgR2xzeW1wIGZyb20gJ0Avdmlld3MvZGFnYW5nT2lsZmllbGQvYnpnbC9zeWJ6ay9nbHN5bXAnCmltcG9ydCBzeW1id2hEeW1ibnIgZnJvbSAnQC92aWV3cy9kYWdhbmdPaWxmaWVsZC9iemdsL3N5YnprL3N5bWJ3aER5bWJucicKaW1wb3J0IGh0bWxUb1BkZiBmcm9tICdAL3V0aWxzL3ByaW50L2h0bWxUb1BkZicKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnc3ltYlNlbGVjdCcsCiAgY29tcG9uZW50czoge0dsc3ltcCwgc3ltYndoRHltYm5yfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy/ln7rmnKzkv6Hmga/ooajmoLzmlbDmja4KICAgICAgdGFibGVEYXRhX2pieHg6IFsKICAgICAgICB7CiAgICAgICAgICAnY29sdW1uXzEnOiAn6K+V6aqM5oCn6LSoJywKICAgICAgICAgICdjb2x1bW5fMic6ICfor5Xpqozml6XmnJ8nLAogICAgICAgICAgJ2NvbHVtbl8zJzogJ+ivlemqjOS6uuWRmCcsCiAgICAgICAgICAnY29sdW1uXzQnOiAn6K+V6aqM5Zyw54K5JywKICAgICAgICAgICdjb2x1bW5fNSc6ICcnLAogICAgICAgICAgJ2NvbHVtbl82JzogJycsCiAgICAgICAgICAnY29sdW1uXzcnOiAnJywKICAgICAgICAgICdjb2x1bW5fOCc6ICcnLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgJ2NvbHVtbl8xJzogJ+aKpeWRiuaXpeacnycsCiAgICAgICAgICAnY29sdW1uXzInOiAn57yW5YaZ5Lq6JywKICAgICAgICAgICdjb2x1bW5fMyc6ICflrqHmoLjkuronLAogICAgICAgICAgJ2NvbHVtbl80JzogJ+aJueWHhuS6uicsCiAgICAgICAgICAnY29sdW1uXzUnOiAnJywKICAgICAgICAgICdjb2x1bW5fNic6ICcnLAogICAgICAgICAgJ2NvbHVtbl83JzogJycsCiAgICAgICAgICAnY29sdW1uXzgnOiAnJywKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgICdjb2x1bW5fMSc6ICfor5XpqozlpKnmsJQnLAogICAgICAgICAgJ2NvbHVtbl8yJzogJ+eOr+Wig+a4qeW6pu+8iOKEg++8iScsCiAgICAgICAgICAnY29sdW1uXzMnOiAn546v5aKD55u45a+55rm/5bqm77yIJe+8iScsCiAgICAgICAgICAnY29sdW1uXzQnOiAn5oqV6L+Q5pel5pyfJywKICAgICAgICAgICdjb2x1bW5fNSc6ICcnLAogICAgICAgICAgJ2NvbHVtbl82JzogJycsCiAgICAgICAgICAnY29sdW1uXzcnOiAnJywKICAgICAgICAgICdjb2x1bW5fOCc6ICcnLAogICAgICAgIH0KICAgICAgXSwKICAgICAgLy/orr7lpIfpk63niYzooajmoLzmlbDmja4KICAgICAgdGFibGVEYXRhX3NibXA6IFt7CiAgICAgICAgJ2NvbHVtbl8xJzogJ+mineWumueUteWOiycsCiAgICAgICAgJ2NvbHVtbl8yJzogJ+iuvuWkh+Wei+WPtycsCiAgICAgICAgJ2NvbHVtbl8zJzogJycsCiAgICAgICAgJ2NvbHVtbl80JzogJycsCiAgICAgICAgJ2NvbHVtbl81JzogJycsCiAgICAgICAgJ2NvbHVtbl82JzogJycsCiAgICAgIH0sXSwKICAgICAgLy/opoHlvqrnjq/nmoTor5XpqozooajmoLzmlbDmja4KICAgICAgYXJyOiBbewogICAgICAgIHRpdGxlOiAiIiwvL+ivlemqjOWQjeensAogICAgICAgIHp4bUxpc3Q6IFtdLC8v5a2Q6aG555uu5pWw5o2u77yI6KGo5aS077yJCiAgICAgICAgYndMaXN0OiBbXSwvL+mDqOS9jeaVsOaNru+8iOesrOS4gOWIl+W8gOWktO+8iQogICAgICB9XSwKICAgICAgLy/kuIvovb3lvLnlh7rmoYbmjqfliLYKICAgICAgaXNTaG93RG93bkxvYWREaWFsb2c6IGZhbHNlLAogICAgICBwcmludE9iajogewogICAgICAgIGlkOiAicHJldmlld0lkIiwgLy8g5b+F5aGr77yM5riy5p+T5omT5Y2w55qE5YaF5a655L2/55SoCiAgICAgICAgcG9wVGl0bGU6ICImbmJzcDsiLCAvLwogICAgICAgIHByZXZpZXdUaXRsZTogIiZuYnNwOyIsCiAgICAgICAgcHJldmlldzogZmFsc2UsCiAgICAgIH0sCiAgICAgIG1iSW5mbzoge30sCiAgICAgIC8v5omT5Y2w5YaF5a65ZGl25LitaWTlgLwKICAgICAgcHJldmlld0lkOiAiIiwKICAgICAgLy/lrprkuYnmqKHmnb/lhoXlrrnlvLnlh7rmoYbkvKDpgJLlj4LmlbAKICAgICAgbWJSb3dEYXRhOiB7fSwKICAgICAgLy/lrprkuYnmqKHmnb/lhoXlrrnlvLnlh7rmoYYKICAgICAgaXNTaG93WG1HbGJ3RGlhbG9nOiBmYWxzZSwKICAgICAgeG1TZWxlY3RlZEZvcm06IHsKICAgICAgICAvL+ivlemqjOaooeadv2lkCiAgICAgICAgc3ltYmlkOiB1bmRlZmluZWQsCiAgICAgICAgLy/or5Xpqozpobnnm67mlbDmja7pm4blkIgKICAgICAgICB4bURhdGFSb3dzOiBbXQogICAgICB9LAogICAgICAvL+mhueebruW6k+W8ueWHuuahhuagh+mimAogICAgICB4bUxpYnJhcnlBZGREaWFsb2dUaXRsZTogJ+mhueebruW6kycsCiAgICAgIC8v6aG555uu5bqT5by55Ye65qGG5o6n5Yi2CiAgICAgIGlzU2hvd0FkZEdseG1EaWFsb2c6IGZhbHNlLAogICAgICAvL+mhueebruW6k+afpeivouWPguaVsAogICAgICB4bUxpYnJhcnlRdWVyeUZvcm06IHsKICAgICAgICBzeW1iaWQ6IHVuZGVmaW5lZCwKICAgICAgICBzeXhtbWM6ICcnLAogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwCiAgICAgIH0sCiAgICAgIC8v6aG555uu5bqT5pWw5o2uCiAgICAgIHhtTGlicmFyeURhdGFMaXN0OiBbXSwKICAgICAgLy/pobnnm67lupPpobnnm67mgLvmlbAKICAgICAgeG1MaWJyYXJ5VG90YWw6IDAsCiAgICAgIC8v6KGo5Y2V6aqM6K+BCiAgICAgIG1iemJSdWxlczogewogICAgICAgIG1ibWM6IFsKICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeaooeadv+WQjeensCcsIHRyaWdnZXI6ICdibHVyJ30KICAgICAgICBdCiAgICAgIH0sCiAgICAgIC8vIOetm+mAieadoeS7tgogICAgICBmaWx0ZXJJbmZvOiB7CiAgICAgICAgZGF0YTogewogICAgICAgICAgbWJtYzogJycKICAgICAgICB9LAogICAgICAgIGZpZWxkTGlzdDogWwogICAgICAgICAge2xhYmVsOiAn5qih5p2/5ZCN56ewJywgdHlwZTogJ2lucHV0JywgdmFsdWU6ICdtYm1jJywgbXVsdGlwbGU6IHRydWV9CiAgICAgICAgXQogICAgICB9LAogICAgICAvL+aWsOWinuaMiemSruaOp+WItgogICAgICBhZGREaXNhYmxlZDogdHJ1ZSwKICAgICAgLy/moJHnu5PmnoTmh5LliqDovb3lj4LmlbAKICAgICAgcHJvcHM6IHsKICAgICAgICBsYWJlbDogJ25hbWUnLAogICAgICAgIGNoaWxkcmVuOiAnem9uZXMnLAogICAgICAgIGlzTGVhZjogJ2xlYWYnLAogICAgICB9LAogICAgICAvL+WIoOmZpOmAieaLqeWIlwogICAgICBzZWxlY3RSb3dzOiBbXSwKICAgICAgLy/pgInkuK3nmoTljZXmnaHlr7nosaEKICAgICAgc2VsZWN0Um93RGF0YToge30sCiAgICAgIC8v5by55Ye65qGG6KGo5Y2VCiAgICAgIGZvcm06IHt9LAogICAgICAvL+afpeivouivlemqjOmDqOS9jeWPguaVsAogICAgICBxdWVyeVN5QndQYXJhbTogewogICAgICAgIHNibHhpZDogdW5kZWZpbmVkLAogICAgICAgIG1ibWM6IHVuZGVmaW5lZCwKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMAogICAgICB9LAogICAgICAvL+eCueWHu+agkeiKgueCuei1i+WAvAogICAgICB0cmVlRm9ybToge30sCiAgICAgIC8v6K+V6aqM6YOo5L2N5YiX6KGoCiAgICAgIHRhYmxlQW5kUGFnZUluZm86IHsKICAgICAgICBwYWdlcjogewogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMDBdCiAgICAgICAgfSwKICAgICAgICB0YWJsZURhdGE6IFtdLAogICAgICAgIHRhYmxlSGVhZGVyOiBbCiAgICAgICAgICB7bGFiZWw6ICfmqKHmnb/lkI3np7AnLCBwcm9wOiAnbWJtYycsIG1pbldpZHRoOiAnMTAwJ30sCiAgICAgICAgICB7bGFiZWw6ICfmmK/lkKbpu5jorqQnLCBwcm9wOiAnc2ZtcicsIG1pbldpZHRoOiAnMTAwJ30sCiAgICAgICAgICB7bGFiZWw6ICfmmK/lkKblgZznlKgnLCBwcm9wOiAnc2Z0eScsIG1pbldpZHRoOiAnMTAwJ30sCiAgICAgICAgXSwKICAgICAgICBvcHRpb246IHtjaGVja0JveDogdHJ1ZSwgc2VyaWFsTnVtYmVyOiB0cnVlfQogICAgICB9LAogICAgICAvL+e7hOe7h+agkQogICAgICB0cmVlT3B0aW9uczogW10sCgogICAgICBpc1Nob3dEZXRhaWxzOiBmYWxzZSwKICAgICAgdGl0bGU6ICcnLAoKICAgICAgLy/liKDpmaTmmK/lkKblj6/nlKgKICAgICAgbXVsdGlwbGVTZW5zb3I6IHRydWUsCiAgICAgIC8v5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgYm06IHVuZGVmaW5lZCwKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMAogICAgICB9LAogICAgICBpc0Rpc2FibGVkOiBmYWxzZSwKICAgICAgaXNTaG93OiB0cnVlLAoKICAgICAgLy/lhbPogZTpobnnm67lvLnlh7rmoYZ0aXRsZQogICAgICBnbHhtRGlhbG9nVGl0bGU6ICflhbPogZTpobnnm64nLAogICAgICAvL+WFs+iBlOmhueebruW8ueWHuuahhuaOp+WItuWxleW8gAogICAgICBpc0dseG1EaWFsb2dTaG93OiBmYWxzZSwKCiAgICAgIC8v5YWz6IGU6aG555uudG90YWwKICAgICAgZ2x4bVRvdGFsOiAwLAogICAgICAvL+WFs+iBlOmhueebruafpeivouWPguaVsAogICAgICBnbHhtUXVlcnlQYXJhbXM6IHsKICAgICAgICBzeW1iaWQ6IHVuZGVmaW5lZCwKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMAogICAgICB9LAoKICAgICAgLy/lhbPogZTlrZDpobnnm650b3RhbAogICAgICBnbHp4bVRvdGFsOiAwLAogICAgICAvL+WFs+iBlOWtkOmhueebruafpeivouWPguaVsAogICAgICBnbHp4bVF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgc3l4bWlkOiB1bmRlZmluZWQsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHBhZ2VOdW06IDEKICAgICAgfSwKCiAgICAgIC8v5qih5p2/5YWz6IGU6aG555uu5pWw5o2uCiAgICAgIG1iR2x4bURhdGFMaXN0OiBbXSwKICAgICAgLy/pobnnm67lhbPogZTnmoTlrZDpobnnm67mlbDmja4KICAgICAgenhtR2xtYkRhdGFMaXN0OiBbXSwKICAgICAgLy/mqKHmnb/lhbPogZTpobnnm67pgInkuK3moYbmlbDmja4KICAgICAgc2VsZWN0ZWRSb3dEYXRhQ2hhbmdlOiBbXSwKICAgICAgLy/mmL7npLrpk63niYzlvLnmoYYKICAgICAgc2hvd01wRGlhbG9nOiBmYWxzZSwKICAgICAgLy/pgInkuK3ooYzmlbDmja4KICAgICAgcm93RGF0YToge30sCgogICAgICAvL+WFs+iBlOWQjeeJjOS+v+WIqQogICAgICBtcExpc3Q6IFtdLAoKICAgICAgLy/or5XpqozmlbDmja4KICAgICAgc3lzakRhdGFMaXN0OiBbXSwKICAgICAgLy/or5XpqozooajmoLzpu5jorqTlm7rlrprnmoTooYwKICAgICAgZGVmYXVsdFJvdzogWyLku6rlmajlnovlj7ciLCAi57uT6K66IiwgIuWkh+azqCJdLAogICAgfQogIH0sCiAgd2F0Y2g6IHt9LAogIGNyZWF0ZWQoKSB7CiAgICAvL+iOt+WPluaVsOaNruWIl+ihqAogICAgdGhpcy5nZXREYXRhKCkKICB9LAogIG1vdW50ZWQoKSB7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvL+ivlemqjOaVsOaNruihqOagvOWQiOW5tuaWueazlQogICAgYXJyYXlTcGFuTWV0aG9kKHtyb3csIGNvbHVtbiwgcm93SW5kZXgsIGNvbHVtbkluZGV4fSkgewogICAgICBpZiAodGhpcy5kZWZhdWx0Um93LmluY2x1ZGVzKHJvdy5TWUJXKSkgewogICAgICAgIGlmIChjb2x1bW5JbmRleCA+IDApIHsKICAgICAgICAgIHJldHVybiBbMSwgcm93LnRvdGFsTnVtXQogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIC8v6K6+5aSH6ZOt54mM6KGo5qC85ZCI5bm25pa55rOVCiAgICBzYm1wU3Bhbk1ldGhvZCh7cm93LCBjb2x1bW4sIHJvd0luZGV4LCBjb2x1bW5JbmRleH0pIHsKICAgICAgaWYgKGNvbHVtbkluZGV4ID4gMykgewogICAgICAgIHJldHVybiBbMSwgMl0KICAgICAgfQogICAgfSwKICAgIC8v5qih5p2/6K+m5oOF5oyJ6ZKuCiAgICBoYW5kbGVNYkluZm8ocm93KSB7CiAgICAgIC8v6I635Y+W5b2T5YmN5qih5p2/aWTliqDovb3pobXpnaLkv6Hmga8KICAgICAgdGhpcy5nZXRNYkdsTXBpbmZvRGF0YShyb3cpOwogICAgICAvL+iOt+WPluivlemqjOaVsOaNrgogICAgICB0aGlzLmdldE1iR2xYbUFuZEJ3KHJvdyk7CiAgICB9LAogICAgLy/lr7zlh7pwZGbmk43kvZwKICAgIGRvd25sb2FkUGRmKCkgewogICAgICBodG1sVG9QZGYuZG93bmxvYWRQREYoZG9jdW1lbnQucXVlcnlTZWxlY3RvcignI3ByaW50Q29udGVudElkJyksIHRoaXMubWJJbmZvLm1ibWMpCiAgICB9LAogICAgLy/ojrflj5blvZPliY3mqKHmnb9pZOWKoOi9vemhtemdouS/oeaBrwogICAgZ2V0TWJHbE1waW5mb0RhdGEocGFyYW0pIHsKICAgICAgZ2V0TWJHbE1waW5mb0RhdGEocGFyYW0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLm1wTGlzdCA9IHJlcy5kYXRhOwogICAgICAgIC8v6LCD55So5riy5p+T6ZOt54mM6aG16Z2i5byA5aeLCiAgICAgICAgLy8gdGhpcy5hcHBseU1wSHRtbCh0aGlzLm1wTGlzdCwgcGFyYW0pOwogICAgICAgIC8v5omT5byA5by55Ye65qGGCiAgICAgICAgdGhpcy5pc1Nob3dEb3duTG9hZERpYWxvZyA9IHRydWU7CiAgICAgIH0pCgogICAgfSwKICAgIC8v6I635Y+W6K+V6aqM5pWw5o2uCiAgICBnZXRNYkdsWG1BbmRCdyhyb3dEYXRhKSB7CiAgICAgIC8v5q+P5qyh6I635Y+W5pWw5o2u5YmN5YWI5riF56m677yM5YaN5re75Yqg77yM5ZCm5YiZ5aSa5qyh6L+b5YWl6aG16Z2i5pe25Lya6I635Y+W6YeN5aSN5pWw5o2uCiAgICAgIHRoaXMuc3lzakRhdGFMaXN0ID0gW107CiAgICAgIGdldE1iR2xYbUFuZEJ3KHJvd0RhdGEpLnRoZW4ocmVzID0+IHsKICAgICAgICBsZXQgcmVzTWFwID0gcmVzLmRhdGE7CiAgICAgICAgLy/pgY3ljobov5Tlm57nu5PmnpwKICAgICAgICBmb3IgKGxldCBrZXkgaW4gcmVzTWFwKSB7CiAgICAgICAgICAvL+ino+aekOivlemqjOaVsOaNrgogICAgICAgICAgdGhpcy5hbmFseXNpc1N5RGF0YShrZXksIHJlc01hcFtrZXldKTsKICAgICAgICB9CiAgICAgICAgLy/nlLvor5XpqozmlbDmja7pobXpnaIKICAgICAgICB0aGlzLmFwcGx5U3lzakRhdGFUb0h0bWwoKTsKICAgICAgfSkKICAgIH0sCiAgICAvL+ino+aekOWQjuWPsOivlemqjOaVsOaNrgogICAgYW5hbHlzaXNTeURhdGEoc3l4bW1jLCB6eG1BbmRCd0RhdGEpIHsKICAgICAgbGV0IHN5c2pEYXRhID0ge30KICAgICAgc3lzakRhdGEuc3l4bW1jID0gc3l4bW1jOwogICAgICBmb3IgKGxldCBrZXkgaW4genhtQW5kQndEYXRhWzBdKSB7CiAgICAgICAgc3lzakRhdGFba2V5XSA9IHp4bUFuZEJ3RGF0YVswXVtrZXldCiAgICAgIH0KICAgICAgdGhpcy5zeXNqRGF0YUxpc3QucHVzaChzeXNqRGF0YSk7CiAgICB9LAogICAgLy/muLLmn5Plrp7pqozmlbDmja7liLDpobXpnaIKICAgIGFwcGx5U3lzakRhdGFUb0h0bWwoKSB7CiAgICAgIHRoaXMuYXJyID0gW107CiAgICAgIC8vICQoJyNzeXNqVGFibGVJZCcpLmh0bWwoIiIpOwogICAgICAvL+i/m+ihjOaVsOaNruWkhOeQhumHjee7hAogICAgICBsZXQgZGF0YSA9IHRoaXMuc3lzakRhdGFMaXN0OwogICAgICBpZiAoZGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBkYXRhLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgICBsZXQgZGF0YUNoaWxkID0gZGF0YVtpXTsKICAgICAgICAgIGxldCB0aXRsZSA9IGRhdGFDaGlsZC5zeXhtbWM7Ly/or5Xpqozpobnnm67lkI3np7AKICAgICAgICAgIGxldCBid0xpc3QgPSBkYXRhQ2hpbGQuYndMaXN0OyAvL+mDqOS9jWxpc3QKICAgICAgICAgIGxldCB6eG1MaXN0ID0gZGF0YUNoaWxkLnp4bUxpc3Q7IC8v5a2Q6aG555uubGlzdAogICAgICAgICAgbGV0IGh4ID0gWwogICAgICAgICAgICB7CiAgICAgICAgICAgICAgImxhYmVsIjogdGl0bGUsIC8v56ys5LiA5Liq6KGo5aS05Li66K+V6aqM6aG555uu5ZCN56ewCiAgICAgICAgICAgICAgImNvbHVtbl9uYW1lIjogIlNZQlciLCAvL+esrOS4gOWIl+WvueW6lOeahOWtl+auteWQje+8iOivlemqjOmDqOS9je+8iQogICAgICAgICAgICB9LAogICAgICAgICAgXTsKICAgICAgICAgIHp4bUxpc3QuZm9yRWFjaCh6eG0gPT4gewogICAgICAgICAgICBoeC5wdXNoKHsKICAgICAgICAgICAgICAibGFiZWwiOiB6eG0uc3l6eG1tYywgLy/mr4/liJfnmoTooajlpLQKICAgICAgICAgICAgICAiY29sdW1uX25hbWUiOiAiIiwgLy/mr4/liJflr7nlupTnmoTmlbDlgLzmmoLml7borr7nva7kuLrnqbrnmb0KICAgICAgICAgICAgfSkKICAgICAgICAgIH0pCiAgICAgICAgICBsZXQgc3ggPSBbXTsKICAgICAgICAgIGJ3TGlzdC5mb3JFYWNoKGJ3ID0+IHsKICAgICAgICAgICAgc3gucHVzaCh7CiAgICAgICAgICAgICAgIlNZQlciOiBidy5TWUJXLAogICAgICAgICAgICAgICJ0b3RhbE51bSI6IHp4bUxpc3QubGVuZ3RoLy/orrDlvZXmgLvliJfmlbDvvIznlKjkuo7lm7rlrprooYznmoTlkIjlubbkuovku7YKICAgICAgICAgICAgfSkKICAgICAgICAgIH0pCiAgICAgICAgICAvL+WQjuWbm+ihjOWbuuWumgogICAgICAgICAgc3gucHVzaCgKICAgICAgICAgICAgewogICAgICAgICAgICAgICJTWUJXIjogIue7k+aenCIsCiAgICAgICAgICAgICAgInRvdGFsTnVtIjogenhtTGlzdC5sZW5ndGgvL+iusOW9leaAu+WIl+aVsO+8jOeUqOS6juWbuuWumuihjOeahOWQiOW5tuS6i+S7tgogICAgICAgICAgICB9LAogICAgICAgICAgICB7CiAgICAgICAgICAgICAgIlNZQlciOiAi5Luq5Zmo5Z6L5Y+3IiwKICAgICAgICAgICAgICAidG90YWxOdW0iOiB6eG1MaXN0Lmxlbmd0aC8v6K6w5b2V5oC75YiX5pWw77yM55So5LqO5Zu65a6a6KGM55qE5ZCI5bm25LqL5Lu2CiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIHsKICAgICAgICAgICAgICAiU1lCVyI6ICLnu5PorroiLAogICAgICAgICAgICAgICJ0b3RhbE51bSI6IHp4bUxpc3QubGVuZ3RoLy/orrDlvZXmgLvliJfmlbDvvIznlKjkuo7lm7rlrprooYznmoTlkIjlubbkuovku7YKICAgICAgICAgICAgfSwKICAgICAgICAgICAgewogICAgICAgICAgICAgICJTWUJXIjogIuWkh+azqCIsCiAgICAgICAgICAgICAgInRvdGFsTnVtIjogenhtTGlzdC5sZW5ndGgvL+iusOW9leaAu+WIl+aVsO+8jOeUqOS6juWbuuWumuihjOeahOWQiOW5tuS6i+S7tgogICAgICAgICAgICB9CiAgICAgICAgICApCiAgICAgICAgICB0aGlzLmFyci5wdXNoKHsKICAgICAgICAgICAgdGl0bGU6IHRpdGxlLAogICAgICAgICAgICB6eG1MaXN0OiBoeCwKICAgICAgICAgICAgYndMaXN0OiBzeCwKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfQogICAgICAvL+aLvOaOpeeahOmTreeJjOihqOagvAogICAgICAvKiAgICAgbGV0IHN0ciA9ICIiOwogICAgICAgICAgIGlmICh0aGlzLnN5c2pEYXRhTGlzdC5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMuc3lzakRhdGFMaXN0Lmxlbmd0aDsgaSsrKSB7CiAgICAgICAgICAgICAgIC8v5ou85o6l6aG555uu5bqP5Y+3CiAgICAgICAgICAgICAgIGxldCB4bUluZGV4ID0gaSArIDE7CiAgICAgICAgICAgICAgIHN0ciArPSAiPHRyPjx0ZCBjb2xzcGFuPSc1JyBzdHlsZT0ndGV4dC1hbGlnbjogbGVmdDtmb250LXdlaWdodDogYm9sZDtmb250LXNpemU6IDE1cHgnPiIgKyB4bUluZGV4ICsgIuOAgSIgKyB0aGlzLnN5c2pEYXRhTGlzdFtpXS5zeXhtbWMgKyAiPC90ZD48L3RyPiI7CiAgICAgICAgICAgICAgIC8vIHRoaXMuc3lzakRhdGFMaXN0W2ldLmJ3TGlzdDsKICAgICAgICAgICAgICAgLy8gdGhpcy5zeXNqRGF0YUxpc3RbaV0uenhtTGlzdDsKICAgICAgICAgICAgICAgLy8gc3RyICs9ICI8dHI+PHRkPiIrdGhpcy5zeXNqRGF0YUxpc3RbaV0uc3l4bW1jKyI8L3RkPjx0ZCB2LWZvcj1pdGVtIGluIHRoaXMuc3lzakRhdGFMaXN0W2ldLmJ3TGlzdD48L3RkPjwvdHI+IgoKICAgICAgICAgICAgIH0KICAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgICAgICAgJCgnI3N5c2pUYWJsZUlkJykuYXBwZW5kKHN0cikKICAgICAgICAgICAgIH0pCiAgICAgICAgICAgfSovCiAgICB9LAogICAgLy/muLLmn5Ppk63niYzpobXpnaLlvIDlp4ttcExpc3Q65Y+N5Zue55qE6ZOt54mM5YiX6KGoICByb3fvvJrmqKHmnb/ooYzlr7nosaEKICAgIGFwcGx5TXBIdG1sKG1wTGlzdCwgcm93KSB7CiAgICAgIC8v5q+P5qyh5omT5byA6ZyA6KaB6YeN5paw5riy5p+T5LiA5qyhLOWFiOWwhue9ruepugogICAgICAkKCcjc2JtcFRib2R5SWQnKS5odG1sKCIiKTsKICAgICAgLy/muIXnqbrph43mlrDotYvlgLwKICAgICAgdGhpcy5tYkluZm8gPSB7fQogICAgICB0aGlzLm1iSW5mby5tYm1jID0gcm93Lm1ibWM7CiAgICAgIC8v5ou85o6l55qE6ZOt54mM6KGo5qC8CiAgICAgIGxldCBzdHIgPSAiIjsKICAgICAgLy/lhYjliKTmlq3mmK/lkKbliIbnm7jpk63niYwKICAgICAgaWYgKG1wTGlzdC5sZW5ndGggPiAwKSB7CiAgICAgICAgaWYgKG1wTGlzdFswXS5TRkZYID09ICcxJykgeyAvL+W9k+WJjemTreeJjOS4uuWIhuebuOmTreeJjOaXtgogICAgICAgICAgLy/lhpnmrbvnrKzkuIDooYwKICAgICAgICAgIHN0ciArPSAiPHRyPjx0ZCBzdHlsZT0ncGFkZGluZzogMTBweDtmb250LXNpemU6IDE1cHg7Jz7nm7jliKs8L3RkPiIgKwogICAgICAgICAgICAiPHRkIHN0eWxlPSdwYWRkaW5nOiAxMHB4O2ZvbnQtc2l6ZTogMTVweDsnPkE8L3RkPiIgKwogICAgICAgICAgICAiPHRkIHN0eWxlPSdwYWRkaW5nOiAxMHB4O2ZvbnQtc2l6ZTogMTVweDsnPkI8L3RkPiIgKwogICAgICAgICAgICAiPHRkIHN0eWxlPSdwYWRkaW5nOiAxMHB4O2ZvbnQtc2l6ZTogMTVweDsnPkM8L3RkPiIgKwogICAgICAgICAgICAiPC90cj4iOwogICAgICAgICAgLy/lvIDlp4vpgY3ljoblsZXnpLoKICAgICAgICAgIGZvciAobGV0IGEgPSAwOyBhIDwgbXBMaXN0Lmxlbmd0aDsgYSsrKSB7CiAgICAgICAgICAgIHN0ciArPSAiPHRyPiIKICAgICAgICAgICAgc3RyICs9ICI8dGQgc3R5bGU9J3BhZGRpbmc6IDEwcHg7Zm9udC1zaXplOiAxNXB4Oyc+IjsKICAgICAgICAgICAgc3RyICs9IG1wTGlzdFthXS50aXRsZSArICI8L3RkPiI7CiAgICAgICAgICAgIHN0ciArPSAiPHRkPjwvdGQ+PiIKICAgICAgICAgICAgc3RyICs9ICI8dGQ+PC90ZD4+IgogICAgICAgICAgICBzdHIgKz0gIjx0ZD48L3RkPj4iCiAgICAgICAgICAgIHN0ciArPSAiPC90cj4iCiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsgIC8v6ZOt54mM5LiN5YiG55u4CiAgICAgICAgICAvL+W9k+WJjemTreeJjOS4jeWxnuS6juWIhuebuOmTreeJjAogICAgICAgICAgLy/mr4/liJflsZXnpLrljZXlhYPmoLzmlbDph48KICAgICAgICAgIGxldCBjb2wgPSAzOwogICAgICAgICAgLy/lsZXnpLrooYzmlbAKICAgICAgICAgIHZhciBsaW5lcyA9IE1hdGguY2VpbChtcExpc3QubGVuZ3RoIC8gY29sKTsKICAgICAgICAgIC8v6YGN5Y6G5bGV56S66KGM5pWwCiAgICAgICAgICBmb3IgKHZhciBpID0gMDsgaSA8IGxpbmVzOyBpKyspIHsKICAgICAgICAgICAgc3RyICs9ICI8dHI+IjsKICAgICAgICAgICAgLy/pgY3ljobliJcKICAgICAgICAgICAgZm9yICh2YXIgaiA9IDA7IGogPCBjb2w7IGorKykgewogICAgICAgICAgICAgIGlmIChpICogY29sICsgaiA8IG1wTGlzdC5sZW5ndGgpIHsKICAgICAgICAgICAgICAgIHN0ciArPSAiPHRkIHN0eWxlPSdwYWRkaW5nOiAxMHB4O2ZvbnQtc2l6ZTogMTVweDsnPiI7CiAgICAgICAgICAgICAgICAvL+mTreeJjOagh+mimOi1i+WAvAogICAgICAgICAgICAgICAgc3RyICs9IG1wTGlzdFtpICogY29sICsgal0udGl0bGUgKyAiPC90ZD4iOwogICAgICAgICAgICAgICAgLy/pk63niYzlgLzotYvlgLwKICAgICAgICAgICAgICAgIHN0ciArPSBtcExpc3RbaSAqIGNvbCArIGpdLnNmbWIgPT0gMSA/ICI8dGQ+ICZuYnNwOyZuYnNwOyZuYnNwOyZuYnNwOyZuYnNwOyZuYnNwOyZuYnNwOyZuYnNwOzwvdGQ+IiA6ICI8dGQ+IiArIG1wTGlzdFtpICogY29sICsgal0uY29sdW1uX25hbWUgKyAiPC90ZD4iCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICAgIHN0ciArPSAiPC90cj4iOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgICAvL+a4suafk+mTreeJjOmhtemdogogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgJCgnI3NibXBUYm9keUlkJykuYXBwZW5kKHN0cikKICAgICAgfSkKICAgICAgLy/miZPlvIDlvLnlh7rmoYYKICAgICAgdGhpcy5pc1Nob3dEb3duTG9hZERpYWxvZyA9IHRydWU7CiAgICB9LAogICAgLy/lhbPpl63pooTop4jlvLnlh7rmoYYKICAgIGNsb3NlWWxEaWFsb2coKSB7CiAgICAgIC8v5riF56m66KGo5Y2VCiAgICAgIHRoaXMubWJJbmZvID0ge307CiAgICAgIC8v6LWL5YC85a6M5YWz6Zet5by556qXCiAgICAgIHRoaXMuaXNTaG93RG93bkxvYWREaWFsb2cgPSBmYWxzZTsKICAgIH0KICAgICwKICAgIC8v5a6a5LmJ5qih5p2/5YaF5a65CiAgICBoYW5kbGVDbGlja01ibnIocm93KSB7CiAgICAgIC8v5omT5byA57uE5Lu25by55Ye65qGGCiAgICAgIHRoaXMuaXNTaG93WG1HbGJ3RGlhbG9nID0gdHJ1ZTsKICAgICAgLy/nu5nlrZDnu4Tku7bkvKDpgJLmlbDmja4KICAgICAgdGhpcy5tYlJvd0RhdGEgPSByb3c7CiAgICB9CiAgICAsCiAgICAvL+iOt+WPlumhueebruW6k+mhueebruaVsOaNrgogICAgZ2V0WG1MaXJhcnlEYXRhKCkgewogICAgICBnZXRYbUxpcmFyeURhdGEodGhpcy54bUxpYnJhcnlRdWVyeUZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLnhtTGlicmFyeURhdGFMaXN0ID0gcmVzLmRhdGEucmVjb3JkcwogICAgICAgIHRoaXMueG1MaWJyYXJ5VG90YWwgPSByZXMuZGF0YS50b3RhbAogICAgICB9KQogICAgfQogICAgLAogICAgLy/pobnnm67lvLnlh7rmoYbmlrDlop7mjInpkq4KICAgIGFkZE1iR2xYbSgpIHsKICAgICAgdGhpcy5nZXRYbUxpcmFyeURhdGEoKQogICAgICB0aGlzLmlzU2hvd0FkZEdseG1EaWFsb2cgPSB0cnVlCiAgICB9CiAgICAsCiAgICAvL+mhueebruW6k+W8ueWHuuahhuWPlua2iOaMiemSrgogICAgY2xvc2VBZGRNanpEaWFsb2coKSB7CiAgICAgIHRoaXMuaXNTaG93QWRkR2x4bURpYWxvZyA9IGZhbHNlCiAgICB9CiAgICAsCiAgICAvL+mhueebruW6k+W8ueeql+ehruiupOaMiemSrgogICAgY29tbWl0QWRkTWp6Rm9ybSgpIHsKICAgICAgaWYgKHRoaXMueG1TZWxlY3RlZEZvcm0ueG1EYXRhUm93cy5sZW5ndGggPCAxKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCfmnKrlhbPogZTpobnnm67vvIHvvIHvvIHlt7Llj5bmtognKQogICAgICAgIC8v5aaC5p6c5pyq6YCJ5Lit5pWw5o2uLOWImeebtOaOpeWFs+mXreW8ueeqlwogICAgICAgIHRoaXMuaXNTaG93QWRkR2x4bURpYWxvZyA9IGZhbHNlCiAgICAgIH0gZWxzZSB7CiAgICAgICAgY29uc29sZS5sb2codGhpcy54bVNlbGVjdGVkRm9ybSkKICAgICAgICAvL+iLpemAieaLqeaVsOaNruWQjgogICAgICAgIGFkZE1iR2x4bUJhdGNoVG9NYnhtKHRoaXMueG1TZWxlY3RlZEZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gJzAwMDAnKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5YWz6IGU5oiQ5YqfJykKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WFs+iBlOWksei0pe+8ge+8gScpCiAgICAgICAgICB9CiAgICAgICAgICAvL+WFs+mXreW8ueeqlwogICAgICAgICAgdGhpcy5pc1Nob3dBZGRHbHhtRGlhbG9nID0gZmFsc2UKICAgICAgICAgIC8v6LCD55So6I635Y+W5YWz6IGU5a2Q6aG555uu5YiX6KGoCiAgICAgICAgICB0aGlzLmdldFN5bWJHbHN5eG1EYXRhTGlzdEJ5UGFnZSgpCiAgICAgICAgfSkKICAgICAgfQogICAgfQogICAgLAogICAgLy/pobnnm67lupPooYzpgInkuK3kuovku7YKICAgIGhhbmRsZVNlbGVjdGVkWG1MaWJyYXJ5Q2hhbmdlKHJvd3MpIHsKICAgICAgdGhpcy54bVNlbGVjdGVkRm9ybS54bURhdGFSb3dzID0gcm93cwogICAgfQogICAgLAogICAgLy/pobnnm67lupPmn6Xor6LmjInpkq4KICAgIHNlbGVjdHhtTGlicmFyeSgpIHsKICAgICAgdGhpcy5nZXRYbUxpcmFyeURhdGEoKQogICAgfQogICAgLAogICAgLy/pobnnm67lupPph43nva7mjInpkq4KICAgIHJlc2V0eG1TZWFyY2goKSB7CiAgICAgIHRoaXMueG1MaWJyYXJ5UXVlcnlGb3JtLnN5eG1tYyA9ICcnCiAgICAgIHRoaXMuZ2V0WG1MaXJhcnlEYXRhKCkKICAgIH0KICAgICwKICAgIC8v6I635Y+W5YWz6IGU5a2Q5YiX6KGo5pa55rOVCiAgICBnZXRaeG1EYXRhTGlzdCgpIHsKICAgICAgZ2V0R2xTeXp4bURhdGFMaXN0QnlQYWdlKHRoaXMuZ2x6eG1RdWVyeVBhcmFtcykudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuZ2x6eG1Ub3RhbCA9IHJlcy5kYXRhLnRvdGFsCiAgICAgICAgdGhpcy56eG1HbG1iRGF0YUxpc3QgPSByZXMuZGF0YS5yZWNvcmRzCiAgICAgIH0pCiAgICB9CiAgICAsCgogICAgLy/lhbPogZTpobnnm64KICAgIGhhbmRsZUNsaWNrR2x4bShyb3cpIHsKICAgICAgLy/muIXnqbrljp/mnaXlrZDpobnnm67mlbDmja4KICAgICAgdGhpcy56eG1HbG1iRGF0YUxpc3QgPSBbXQogICAgICAvL+aJk+W8gOWFs+iBlOmhueebruW8ueWHuuahhgogICAgICB0aGlzLmlzR2x4bURpYWxvZ1Nob3cgPSB0cnVlCiAgICAgIC8v57uZ5Y+C5pWw6LWL5YC8CiAgICAgIHRoaXMuZ2x4bVF1ZXJ5UGFyYW1zLnN5bWJpZCA9IHJvdy5vYmpJZAogICAgICAvL+afpeivoumhueebruW6k+aVsOaNruaXtuWPguaVsAogICAgICB0aGlzLnhtTGlicmFyeVF1ZXJ5Rm9ybS5zeW1iaWQgPSByb3cub2JqSWQKICAgICAgLy/nu5nor5Xpqozpobnnm67lupPmt7vliqDml7bkvb/nlKgKICAgICAgdGhpcy54bVNlbGVjdGVkRm9ybS5zeW1iaWQgPSByb3cub2JqSWQKICAgICAgLy/ojrflj5bmqKHmnb/lhbPogZTpobnnm67mlbDmja4KICAgICAgdGhpcy5nZXRTeW1iR2xzeXhtRGF0YUxpc3RCeVBhZ2UoKQogICAgfQogICAgLAogICAgLy/ojrflj5blhbPogZTpobnnm67lvLnlh7rmoYbmlbDmja4KICAgIGdldFN5bWJHbHN5eG1EYXRhTGlzdEJ5UGFnZSgpIHsKICAgICAgZ2V0U3ltYkdsc3l4bURhdGFMaXN0QnlQYWdlKHRoaXMuZ2x4bVF1ZXJ5UGFyYW1zKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5tYkdseG1EYXRhTGlzdCA9IHJlcy5kYXRhLnJlY29yZHMKICAgICAgICB0aGlzLmdseG1Ub3RhbCA9IHJlcy5kYXRhLnRvdGFsCiAgICAgIH0pCiAgICB9CiAgICAsCiAgICAvL+ivlemqjOmhueebruWkjemAieahhueCueWHu+aXtumXtOeCueWHu+aTjeS9nAogICAgaGFuZGxlR2x4bVNlbGVjdGVkQ2hhbmdlKHJvd3MpIHsKICAgICAgdGhpcy5zZWxlY3RlZFJvd0RhdGFDaGFuZ2UgPSByb3dzCiAgICB9CiAgICAsCiAgICAvL+WIoOmZpOaooeadv+WFs+iBlOmhueebrgogICAgZGVsZXRlTWJHbFhtKCkgewogICAgICBpZiAodGhpcy5zZWxlY3RlZFJvd0RhdGFDaGFuZ2UubGVuZ3RoIDwgMSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup5q2j56Gu55qE5pWw5o2u77yB77yB77yBJykKICAgICAgICByZXR1cm4KICAgICAgfQogICAgICBsZXQgaWRzID0gdGhpcy5zZWxlY3RlZFJvd0RhdGFDaGFuZ2UubWFwKGl0ZW0gPT4gewogICAgICAgIHJldHVybiBpdGVtLm9iaklkCiAgICAgIH0pCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+atpOaTjeS9nOWwhuawuOS5heWIoOmZpOivpeaVsOaNriwg5piv5ZCm57un57utPycsICfmj5DnpLonLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICByZW1vdmUoaWRzKS50aGVuKCh7Y29kZX0pID0+IHsKICAgICAgICAgIGlmIChjb2RlID09PSAnMDAwMCcpIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgICAgIG1lc3NhZ2U6ICfliKDpmaTmiJDlip8hJwogICAgICAgICAgICB9KQogICAgICAgICAgICB0aGlzLmdldFN5bWJHbHN5eG1EYXRhTGlzdEJ5UGFnZSgpCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InLAogICAgICAgICAgICAgIG1lc3NhZ2U6ICfliKDpmaTlpLHotKUhJwogICAgICAgICAgICB9KQogICAgICAgICAgICB0aGlzLmdldFN5bWJHbHN5eG1EYXRhTGlzdEJ5UGFnZSgpCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ2luZm8nLAogICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iOWIoOmZpCcKICAgICAgICB9KQogICAgICB9KQogICAgfSwKICAgIC8v6K+V6aqM6aG555uu54K55Ye76KGM5pWw5o2u5pe255qE5Y2V5py65pON5L2cCiAgICBoYW5kbGVNYkdseG1Sb3dDbGljayhyb3cpIHsKICAgICAgdGhpcy5nbHp4bVF1ZXJ5UGFyYW1zLnN5eG1pZCA9IHJvdy5zeXhtaWQKICAgICAgdGhpcy5nZXRaeG1EYXRhTGlzdCgpCiAgICB9LAogICAgLy/mh5LliqDovb3lh73mlbAKICAgIGxvYWROb2RlKG5vZGUsIHJlc29sdmUpIHsKICAgICAgbGV0IFRyZWVwYXJhbU1hcCA9IHsKICAgICAgICBwaWQ6ICcnLAogICAgICAgIHNwYkxvZ286IFsn6L6T55S16K6+5aSHJywgJ+WPmOeUteiuvuWkhycsJ+mFjeeUteiuvuWkhyddCiAgICAgIH0KICAgICAgaWYgKG5vZGUubGV2ZWwgPT09IDApIHsKICAgICAgICBUcmVlcGFyYW1NYXAucGlkID0gJ3NiJwogICAgICAgIHJldHVybiB0aGlzLmdldFRyZWVOb2RlKFRyZWVwYXJhbU1hcCwgcmVzb2x2ZSkKICAgICAgfQogICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICBUcmVlcGFyYW1NYXAucGlkID0gbm9kZS5kYXRhLmNvZGUKICAgICAgICB0aGlzLmdldFRyZWVOb2RlKFRyZWVwYXJhbU1hcCwgcmVzb2x2ZSkKICAgICAgfSwgNTAwKQoKICAgIH0sCiAgICAvL+iOt+WPluagkeiKgueCueaVsOaNrgogICAgZ2V0VHJlZU5vZGUocGFyYW1NYXAsIHJlc29sdmUpIHsKICAgICAgZ2V0RGV2aWNlQ2xhc3NUcmVlTm9kZUJ5UGlkKHBhcmFtTWFwKS50aGVuKHJlcyA9PiB7CiAgICAgICAgbGV0IHRyZWVOb2RlcyA9IFtdCiAgICAgICAgcmVzLmRhdGEuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIGxldCBub2RlID0gewogICAgICAgICAgICBuYW1lOiBpdGVtLm5hbWUsCiAgICAgICAgICAgIGxldmVsOiBpdGVtLmxldmVsLAogICAgICAgICAgICBpZDogaXRlbS5pZCwKICAgICAgICAgICAgcGlkOiBpdGVtLnBpZCwKICAgICAgICAgICAgbGVhZjogZmFsc2UsCiAgICAgICAgICAgIGNvZGU6IGl0ZW0uY29kZQogICAgICAgICAgfQogICAgICAgICAgdHJlZU5vZGVzLnB1c2gobm9kZSkKICAgICAgICB9KQogICAgICAgIHJlc29sdmUodHJlZU5vZGVzKQogICAgICB9KQogICAgfSwKICAgIC8v5re75Yqg5ZCO56Gu6K6k5L+d5a2Y5oyJ6ZKuCiAgICBzYXZlKCkgewogICAgICB0aGlzLiRyZWZzWydmb3JtJ10udmFsaWRhdGUoKHZhbGlkKSA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBzYXZlT3JVcGRhdGUodGhpcy5mb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gJzAwMDAnKSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKHJlcy5tc2cpCiAgICAgICAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnBhZ2VSZXNpemUgPSAnWScKICAgICAgICAgICAgICB0aGlzLmdldERhdGEoKQogICAgICAgICAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IGZhbHNlCiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubXNnKQogICAgICAgICAgICB9CiAgICAgICAgICB9KQoKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgLy/moJHoioLngrnngrnlh7vkuovku7YKICAgIGhhbmRsZU5vZGVDbGljayhkYXRhKSB7CiAgICAgIGNvbnNvbGUubG9nKCfmoJHoioLngrnngrnlh7snKQogICAgICBjb25zb2xlLmxvZyhkYXRhKQogICAgICBpZiAoZGF0YS5sZXZlbCAhPSAnMCcgJiYgZGF0YS5sZXZlbCAhPSAnMScpIHsKICAgICAgICAvL+aWsOWinuaMiemSruWPr+eCueWHuwogICAgICAgIHRoaXMuYWRkRGlzYWJsZWQgPSBmYWxzZQogICAgICAgIHRoaXMudHJlZUZvcm0gPSBkYXRhCiAgICAgICAgdGhpcy5xdWVyeVN5QndQYXJhbS5zYmx4aWQgPSBkYXRhLmNvZGUKICAgICAgICB0aGlzLmdldERhdGEoKQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuYWRkRGlzYWJsZWQgPSB0cnVlCiAgICAgIH0KICAgIH0sCiAgICAvL+a3u+WKoOaMiemSrgogICAgYWRkU2Vuc29yQnV0dG9uKCkgewogICAgICB0aGlzLmZvcm0gPSB7fQogICAgICB0aGlzLmZvcm0uc2JseCA9IHRoaXMudHJlZUZvcm0ubmFtZQogICAgICB0aGlzLmZvcm0uc2JseGlkID0gdGhpcy50cmVlRm9ybS5jb2RlCiAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IHRydWUKICAgICAgdGhpcy50aXRsZSA9ICfmlrDlop4nCiAgICB9LAogICAgLy/liJfooajmn6Xor6IKICAgIGFzeW5jIGdldERhdGEocGFyYW1zKSB7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcGFyYW0gPSB7Li4udGhpcy5xdWVyeVN5QndQYXJhbSwgLi4ucGFyYW1zfQogICAgICAgIGNvbnN0IHtkYXRhLCBjb2RlfSA9IGF3YWl0IGdldFBhZ2VEYXRhTGlzdFRvc3ltYihwYXJhbSkKICAgICAgICBpZiAoY29kZSA9PT0gJzAwMDAnKSB7CiAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8udGFibGVEYXRhID0gZGF0YS5yZWNvcmRzCiAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIudG90YWwgPSBkYXRhLnRvdGFsCiAgICAgICAgfQogICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgY29uc29sZS5sb2coZSkKICAgICAgfQogICAgfQogICAgLAogICAgLyoqCiAgICAgKiDooajmoLzlpJrpgInmoYYKICAgICAqLwogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHJvd3MpIHsKICAgICAgdGhpcy5zZWxlY3RSb3dzID0gcm93czsKICAgIH0sCgogICAgY2xvc2UoKSB7CiAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IGZhbHNlCiAgICB9CiAgICAsCiAgICAvL+S/ruaUueaooeadv+S4u+ihqOWGheWuuQogICAgdXBkYXRlRGV0YWlscyhyb3cpIHsKICAgICAgdGhpcy50aXRsZSA9ICfkv67mlLknCiAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IHRydWUKICAgICAgdGhpcy5mb3JtID0gcm93CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlCiAgICAgIHRoaXMuaXNTaG93ID0gdHJ1ZQogICAgfQogICAgLAoKICAgIGNyZWF0ZVRlbXBsYXRlKHJvdykgewogICAgICBjb25zb2xlLmxvZyhyb3cpCiAgICB9CiAgICAsCiAgICAvL+afpeeci+aooeadv+S4u+ihqOivpuaDheaMiemSrgogICAgZ2V0RGV0YWlscyhyb3cpIHsKICAgICAgdGhpcy50aXRsZSA9ICfor6bmg4UnCiAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IHRydWUKICAgICAgdGhpcy5mb3JtID0gcm93CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IHRydWUKICAgICAgdGhpcy5pc1Nob3cgPSBmYWxzZQogICAgfQogICAgLAoKICAgIC8v5Yig6Zmk5oyJ6ZKuCiAgICBkZWxldGVTZW5zb3JCdXR0b24oKSB7CiAgICAgIGlmICh0aGlzLnNlbGVjdFJvd3MubGVuZ3RoIDwgMSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup5q2j56Gu55qE5pWw5o2u77yB77yB77yBJykKICAgICAgICByZXR1cm4KICAgICAgfQogICAgICBsZXQgaWRzID0gdGhpcy5zZWxlY3RSb3dzLm1hcChpdGVtID0+IHsKICAgICAgICByZXR1cm4gaXRlbS5vYmpJZAogICAgICB9KQogICAgICB0aGlzLiRjb25maXJtKCfmraTmk43kvZzlsIbmsLjkuYXliKDpmaTor6XmlbDmja4sIOaYr+WQpue7p+e7rT8nLCAn5o+Q56S6JywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgcmVtb3ZlKGlkcykudGhlbigoe2NvZGV9KSA9PiB7CiAgICAgICAgICBpZiAoY29kZSA9PT0gJzAwMDAnKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICAgICAgICBtZXNzYWdlOiAn5Yig6Zmk5oiQ5YqfIScKICAgICAgICAgICAgfSkKICAgICAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnBhZ2VSZXNpemUgPSAnWScKICAgICAgICAgICAgdGhpcy5nZXREYXRhKCkKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOWksei0pSEnCiAgICAgICAgICAgIH0pCiAgICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci5wYWdlUmVzaXplID0gJ1knCiAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ2luZm8nLAogICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iOWIoOmZpCcKICAgICAgICB9KQogICAgICB9KQoKICAgIH0KICAgICwKICAgIC8v5a+85Ye65oyJ6ZKuCiAgICBoYW5kbGVFeHBvcnQoKSB7CgogICAgfQogICAgLAoKICAgIC8v5YWz6IGU6ZOt54mM54K55Ye75LqL5Lu2CiAgICBoYW5kbGVDbGlja0dsTXAocm93KSB7CiAgICAgIHRoaXMuc2hvd01wRGlhbG9nID0gdHJ1ZQogICAgICB0aGlzLnJvd0RhdGEgPSByb3cKICAgIH0KICAgICwKICAgIC8v5YWz6Zet6K+V6aqM6ZOt54mM5by556qXCiAgICBjbG9zZU1wRGlhbG9nKCkgewogICAgICB0aGlzLnNob3dNcERpYWxvZyA9IGZhbHNlCiAgICB9CiAgICAsCgogICAgZmlsdGVyUmVzZXQoKSB7CiAgICB9LAoKICAgIC8v5YWz6Zet6K+V6aqM5qih5p2/5by556qXCiAgICBjbG9zZVN5bWJDb21tZW50KCkgewogICAgICB0aGlzLiRlbWl0KCJjbG9zZVN5bWJTZWxlY3REaWFsb2ciLCBmYWxzZSkKICAgIH0sCiAgICAvL+eCueWHu+ehruiupOWQjue7meeItue7hOS7tuS8oOmAkuaVsOaNrgogICAgLy8gdGhpcy5zZWxlY3RSb3dEYXRhICE9IHVuZGVmaW5lZCAmJiBKU09OLnN0cmluZ2lmeSh0aGlzLnNlbGVjdFJvd0RhdGEpICE9ICJ7fSIKICAgIGNvbW1pdE1iZGF0YSgpIHsKICAgICAgaWYgKHRoaXMuc2VsZWN0Um93cy5sZW5ndGggPT0gMSAmJiB0aGlzLnNlbGVjdFJvd3MgIT0gdW5kZWZpbmVkKSB7CiAgICAgICB0aGlzLiRlbWl0KCJoYW5kbGVBY2NlcHRNYkRhdGEiLHRoaXMuc2VsZWN0Um93c1swXSk7CiAgICAgICAgdGhpcy4kZW1pdCgiY2xvc2VTeW1iU2VsZWN0RGlhbG9nIiwgZmFsc2UpCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7fpgInmi6nkuIDmnaHmlbDmja4iKQogICAgICB9CiAgICB9LAoKICB9Cn0K"}, {"version": 3, "sources": ["symbSelect.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8c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file": "symbSelect.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk/sybgjlcomment", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row>\n      <el-col :span=\"4\">\n        <el-card class=\"box-card aside_height\" shadow=\"never\">\n          <div slot=\"header\" class=\"clearfix\" style=\"text-align: center\">\n            <span>设备类型</span>\n          </div>\n          <div style=\" overflow: auto;height: 60vh\">\n            <el-col>\n              <el-tree id=\"tree\"\n                       :props=\"props\"\n                       :load=\"loadNode\"\n                       lazy\n                       :default-expanded-keys=\"['1']\"\n                       @node-expand=\"handleNodeClick\"\n                       @node-click=\"handleNodeClick\"/>\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 140, itemWidth: 190 }\"\n          @handleReset=\"filterReset\"\n        />\n        <el-white>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            :is-single=\"true\"\n            height=\"61.5vh\">\n\n            <!--            <el-table-column slot=\"table_six\" align=\"center\" label=\"关联铭牌\" min-width=\"150\" :resizable=\"false\">-->\n            <!--              <template slot-scope=\"scope\">-->\n            <!--                <el-button @click=\"handleClickGlMp(scope.row)\" type=\"text\" size=\"small\" v-if=\"scope.row.sfty == '是'\">-->\n            <!--                  查看铭牌-->\n            <!--                </el-button>-->\n            <!--                <el-button @click=\"handleClickGlMp(scope.row)\" type=\"text\" size=\"small\" v-else>关联铭牌</el-button>-->\n            <!--              </template>-->\n            <!--            </el-table-column>-->\n\n            <!--            <el-table-column slot=\"table_seven\" align=\"center\" label=\"关联项目\" min-width=\"150\" :resizable=\"false\">-->\n            <!--              <template slot-scope=\"scope\">-->\n            <!--                <el-button @click=\"handleClickGlxm(scope.row)\" type=\"text\" size=\"small\" v-if=\"scope.row.sfty == '是'\">-->\n            <!--                  查看项目-->\n            <!--                </el-button>-->\n            <!--                <el-button @click=\"handleClickGlxm(scope.row)\" type=\"text\" size=\"small\" v-else>关联项目</el-button>-->\n            <!--              </template>-->\n            <!--            </el-table-column>-->\n\n            <!--            <el-table-column slot=\"table_eight\" align=\"center\" label=\"定义模板内容\" min-width=\"150\" :resizable=\"false\">-->\n            <!--              <template slot-scope=\"scope\">-->\n            <!--                <el-button @click=\"handleClickMbnr(scope.row)\" type=\"text\" size=\"small\" v-if=\"scope.row.sfty == '是'\">-->\n            <!--                  查看模板内容-->\n            <!--                </el-button>-->\n            <!--                <el-button @click=\"handleClickMbnr(scope.row)\" type=\"text\" size=\"small\" v-else>定义模板内容</el-button>-->\n            <!--              </template>-->\n            <!--            </el-table-column>-->\n\n            <el-table-column slot=\"table_eight\" align=\"center\" label=\"模板详情\" min-width=\"150\" :resizable=\"false\">\n              <template slot-scope=\"scope\">\n                <el-button v-print=\"printObj\" type=\"text\" size=\"small\" @click=\"handleMbInfo(scope.row)\">模板详情</el-button>\n              </template>\n            </el-table-column>\n\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n    <el-row>\n      <div style=\"text-align: right;margin-top: 2vh\">\n        <el-button @click=\"closeSymbComment\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"commitMbdata\">确 定</el-button>\n      </div>\n    </el-row>\n    <!--新增\\修改\\详情弹出-->\n    <el-dialog :title=title :visible.sync=\"isShowDetails\" width=\"60%\" v-dialogDrag>\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"mbzbRules\">\n        <el-row :gutter=\"8\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"设备类型：\" prop=\"sblx\">\n              <el-input v-model=\"form.sblx\" disabled style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"设备类型编码：\" prop=\"sblxbm\">\n              <el-input v-model=\"form.sblxid\" disabled style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"模板名称：\" prop=\"mbmc\">\n              <el-input placeholder=\"请输入试验部位名称\" v-model=\"form.mbmc\" :disabled=\"isDisabled\"\n                        style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否默认：\" prop=\"sfmr\">\n              <el-select v-model=\"form.sfmr\" style=\"width: 100%\" :disabled=\"isDisabled\">\n                <el-option label=\"是\" value=\"是\"></el-option>\n                <el-option label=\"否\" value=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否停用：\" prop=\"sfty\">\n              <el-select v-model=\"form.sfty\" style=\"width: 100%\" :disabled=\"isDisabled\">\n                <el-option label=\"是\" value=\"是\"></el-option>\n                <el-option label=\"否\" value=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" v-show=\"isShow\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"save\">确 认</el-button>\n      </div>\n    </el-dialog>\n\n    <!--关联铭牌弹框-->\n    <el-dialog :visible.sync=\"showMpDialog\" title=\"已关联铭牌\" v-if=\"showMpDialog\" v-dialogDrag>\n      <glsymp :main-data=\"rowData\" :tree-data=\"treeForm\" @closeMpDialog=\"closeMpDialog\"></glsymp>\n    </el-dialog>\n\n    <!--关联试验项目弹出框-->\n    <el-dialog :title=\"glxmDialogTitle\" :visible.sync=\"isGlxmDialogShow\" width=\"60%\" v-dialogDrag>\n      <el-row :gutter=\"3\">\n        <div class=\"mb8 pull-right\">\n          <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"mini\" @click=\"addMbGlXm\">新增项目</el-button>\n          <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"mini\" @click=\"deleteMbGlXm\">删除项目</el-button>\n        </div>\n      </el-row>\n      <el-row :gutter=\"20\">\n        <el-col :span=\"12\">\n          <el-table :data=\"mbGlxmDataList\" @selection-change=\"handleGlxmSelectedChange\"\n                    @row-click=\"handleMbGlxmRowClick\">\n            <el-table-column label=\"试验项目\" align=\"center\">\n              <el-table-column type=\"selection\" width=\"55\" align=\"center\"/>\n              <el-table-column label=\"序号\" type=\"index\" width=\"50\" align=\"center\"></el-table-column>\n              <el-table-column prop=\"syxmmc\" label=\"项目名称\" width=\"180\" align=\"center\"></el-table-column>\n              <el-table-column prop=\"syxmms\" label=\"项目描述\" align=\"center\"></el-table-column>\n            </el-table-column>\n          </el-table>\n          <pagination\n            :total=\"glxmTotal\"\n            :page.sync=\"glxmQueryParams.pageNum\"\n            :limit.sync=\"glxmQueryParams.pageSize\"\n            @pagination=\"getSymbGlsyxmDataListByPage\"\n          />\n        </el-col>\n        <el-col :span=\"12\">\n          <el-table :data=\"zxmGlmbDataList\">\n            <el-table-column label=\"试验子项目\" align=\"center\">\n              <el-table-column prop=\"syzxmmc\" label=\"子项目名称\" width=\"180\" align=\"center\"></el-table-column>\n              <el-table-column prop=\"syzxmms\" label=\"子项目描述\" align=\"center\"></el-table-column>\n            </el-table-column>\n          </el-table>\n          <pagination\n            :total=\"glzxmTotal\"\n            :page.sync=\"glzxmQueryParams.pageNum\"\n            :limit.sync=\"glzxmQueryParams.pageSize\"\n            @pagination=\"getZxmDataList\"\n          />\n        </el-col>\n      </el-row>\n\n    </el-dialog>\n    <!--列表新增关联项目弹窗调用-->\n    <el-dialog :title=\"xmLibraryAddDialogTitle\" :visible.sync=\"isShowAddGlxmDialog\" width=\"50%\" v-dialogDrag>\n      <el-form label-width=\"120px\">\n        <el-row :gutter=\"3\">\n          <el-col :span=\"10\">\n            <el-form-item label=\"项目名称：\">\n              <el-input v-model=\"xmLibraryQueryForm.syxmmc\"/>\n            </el-form-item>\n          </el-col>\n          <div class=\"mb8 pull-right\">\n            <el-button type=\"cyan\" size=\"mini\" icon=\"el-icon-search\" @click=\"selectxmLibrary\">查询</el-button>\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetxmSearch\">重置</el-button>\n          </div>\n        </el-row>\n      </el-form>\n      <el-table stripe border :data=\"xmLibraryDataList\" @selection-change=\"handleSelectedXmLibraryChange\"\n                :header-cell-style=\"{'text-align':'center'}\" :cell-style=\"{'text-align':'center'}\">\n        <el-table-column type=\"selection\" width=\"55\"/>\n        <el-table-column label=\"序号\" type=\"index\" width=\"50\">\n        </el-table-column>\n        <el-table-column label=\"试验项目\" prop=\"syxmmc\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"项目描述\" prop=\"syxmms\" :show-overflow-tooltip=\"true\"/>\n      </el-table>\n      <pagination\n        v-show=\"xmLibraryTotal>0\"\n        :total=\"xmLibraryTotal\"\n        :page.sync=\"xmLibraryQueryForm.pageNum\"\n        :limit.sync=\"xmLibraryQueryForm.pageSize\"\n        @pagination=\"getXmLiraryData\"\n      />\n      <div slot=\"footer\">\n        <el-button @click=\"closeAddMjzDialog\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"commitAddMjzForm\">确 认</el-button>\n      </div>\n    </el-dialog>\n    <!--子组件定义模板内容-->\n    <el-dialog title=\"项目关联部位\" :visible.sync=\"isShowXmGlbwDialog\" v-if=\"isShowXmGlbwDialog\" v-dialogDrag>\n      <symbwh-dymbnr ref=\"symbwhDymbnrRef\" :mb-data=\"mbRowData\"></symbwh-dymbnr>\n    </el-dialog>\n    <!--  打印vue print nb插件-->\n    <div v-show=\"false\">\n      <div id=\"printHtmlId\" style=\"background:white;\">\n        <!--模板-->\n        <div style=\"text-align: center\">\n          <p>{{mbInfo.mbmc}}</p>\n        </div>\n        <p>葫芦娃，葫芦娃</p>\n        <p>一根藤上七朵花 </p>\n        <p>小小树藤是我家 啦啦啦啦 </p>\n        <p>叮当当咚咚当当　浇不大</p>\n        <p> 叮当当咚咚当当 是我家</p>\n        <p> 啦啦啦啦</p>\n        <p>...</p>\n        <div class=\"describle\">\n          <el-form :model=\"mbInfo\" :rules=\"mbzbRules\" ref=\"from\" class=\"demo-ruleForm\">\n            <el-form-item label=\"姓名:\" prop=\"name\">\n              <el-input v-model=\"mbInfo.mbmc\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"描述:\" prop=\"describle\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"4\"\n                :maxlength=\"2000\"\n                placeholder=\"\"\n                v-model=\"mbInfo.mbmc\">\n              </el-input>\n            </el-form-item>\n          </el-form>\n        </div>\n      </div>\n    </div>\n    <!--htmlToPdf插件-->\n    <el-dialog title=\"预览\" :visible.sync=\"isShowDownLoadDialog\" width=\"60%\" append-to-body>\n      <el-button @click=\"downloadPdf\">导出</el-button>\n      <div style=\"width: 100%;height:60vh;overflow: auto\">\n        <div id=\"printContentId\">\n          <div style=\"text-align: center\">\n            <h2>{{mbInfo.mbmc}}</h2></div>\n          <div>\n            <div\n              style=\"font-size: 20px;float: left;width:100%;border-left: 1px solid #000;border-right: 1px solid #000;border-top: 1px solid #000;\">\n              一、基本信息\n            </div>\n            <el-table\n              :data=\"tableData_jbxx\"\n              border\n              style=\"border: 1px solid #000;\"\n            >\n              <el-table-column\n                prop=\"column_1\"\n                label=\"变电站\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_5\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_2\"\n                label=\"委托单位\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_6\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_3\"\n                label=\"试验单位\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_7\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_4\"\n                label=\"运行编号\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_8\"\n                label=\"\"\n              >\n              </el-table-column>\n            </el-table>\n            <!--            <tbody>\n                        <tr>\n                          <td>变电站</td>\n                          <td></td>\n                          <td>委托单位</td>\n                          <td></td>\n                          <td>试验单位</td>\n                          <td></td>\n                          <td>运行编号</td>\n                          <td></td>\n                        </tr>\n                        <tr>\n                          <td>试验性质</td>\n                          <td></td>\n                          <td>试验日期</td>\n                          <td></td>\n                          <td>试验人员</td>\n                          <td></td>\n                          <td>试验地点</td>\n                          <td></td>\n                        </tr>\n                        <tr>\n                          <td>报告日期</td>\n                          <td></td>\n                          <td>编写人</td>\n                          <td></td>\n                          <td>审核人</td>\n                          <td></td>\n                          <td>批准人</td>\n                          <td></td>\n                        </tr>\n                        <tr>\n                          <td>试验天气</td>\n                          <td></td>\n                          <td>环境温度（℃）</td>\n                          <td></td>\n                          <td>环境相对湿度（%）</td>\n                          <td></td>\n                          <td>投运日期</td>\n                          <td></td>\n                        </tr>\n                        </tbody>-->\n          </div>\n          <div>\n            <div\n              style=\"font-size: 20px;float: left;width:100%;border-left: 1px solid #000;border-right: 1px solid #000;\">\n              二、设备铭牌\n            </div>\n            <el-table\n              :data=\"tableData_sbmp\"\n              border\n              :span-method=\"sbmpSpanMethod\"\n              style=\"border: 1px solid #000;\"\n            >\n              <el-table-column\n                prop=\"column_1\"\n                label=\"生产厂家\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_4\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_2\"\n                label=\"出厂编号\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_5\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_3\"\n                label=\"出厂日期\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_6\"\n                label=\"\"\n              >\n              </el-table-column>\n            </el-table>\n            <!--            <tbody id=\"sbmpTbodyId\">\n                        </tbody>-->\n          </div>\n          <div>\n            <div\n              style=\"font-size: 20px;float: left;width:100%;border-left: 1px solid #000;border-right: 1px solid #000;border-bottom: 1px solid #000;\">\n              三、试验数据\n            </div>\n            <div v-for=\"item in arr\" style=\"width: 100%\">\n              <div class=\"printTitle\">{{item.title}}</div>\n              <el-table :data=\"item.bwList\" style=\"width:100%;border: 1px solid #000;\" border\n                        :span-method=\"arraySpanMethod\">\n                <template v-for='(val) in item.zxmList'>\n                  <el-table-column\n                    :prop=\"val.column_name\"\n                    width=\"auto\"\n                    :label=\"val.label\"\n                  >\n                  </el-table-column>\n\n                </template>\n\n              </el-table>\n            </div>\n\n            <!--            <tbody id=\"sysjTableId\">\n                        <tr>\n                          <td colspan=\"5\" style=\"text-align: left;font-weight: bold\">12121212</td>\n                        </tr>\n                        <tr>\n                          <td>部位</td>\n                          <td>回路电阻初值(μΩ)</td>\n                          <td>回路电阻(μΩ)</td>\n                          <td>主回路电阻初值差(%)</td>\n                          <td>是否合格</td>\n                        </tr>\n                        <tr>\n                          <td>部位1</td>\n                          <td></td>\n                          <td></td>\n                          <td></td>\n                          <td></td>\n                        </tr>\n                        <tr>\n                          <td>部位2</td>\n                          <td></td>\n                          <td></td>\n                          <td></td>\n                          <td></td>\n                        </tr>\n                        <tr>\n                          <td>仪器型号</td>\n                          <td colspan=\"4\"></td>\n                        </tr>\n                        <tr>\n                          <td>结论</td>\n                          <td colspan=\"4\"></td>\n                        </tr>\n                        <tr>\n                          <td>备注</td>\n                          <td colspan=\"4\"></td>\n                        </tr>\n                        </tbody>-->\n          </div>\n        </div>\n      </div>\n      <div slot=\"footer\">\n        <el-button @click=\"closeYlDialog\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  //引入jquery,暂时没用\n  import $ from \"jquery\"\n  import {\n    addMbGlxmBatchToMbxm,\n    getPageDataListTosymb,\n    getSymbGlsyxmDataListByPage,\n    getXmLiraryData,\n    remove,\n    saveOrUpdate,\n    getMbGlMpinfoData,\n    getMbGlXmAndBw\n  } from '@/api/dagangOilfield/bzgl/symbwh'\n  import {getDeviceClassTreeNodeByPid} from '@/api/dagangOilfield/bzgl/sblxwh/sblxwh'\n  import {getGlSyzxmDataListByPage} from '@/api/dagangOilfield/bzgl/syxm'\n  import Glsymp from '@/views/dagangOilfield/bzgl/sybzk/glsymp'\n  import symbwhDymbnr from '@/views/dagangOilfield/bzgl/sybzk/symbwhDymbnr'\n  import htmlToPdf from '@/utils/print/htmlToPdf'\n\n  export default {\n    name: 'symbSelect',\n    components: {Glsymp, symbwhDymbnr},\n    data() {\n      return {\n        //基本信息表格数据\n        tableData_jbxx: [\n          {\n            'column_1': '试验性质',\n            'column_2': '试验日期',\n            'column_3': '试验人员',\n            'column_4': '试验地点',\n            'column_5': '',\n            'column_6': '',\n            'column_7': '',\n            'column_8': '',\n          },\n          {\n            'column_1': '报告日期',\n            'column_2': '编写人',\n            'column_3': '审核人',\n            'column_4': '批准人',\n            'column_5': '',\n            'column_6': '',\n            'column_7': '',\n            'column_8': '',\n          },\n          {\n            'column_1': '试验天气',\n            'column_2': '环境温度（℃）',\n            'column_3': '环境相对湿度（%）',\n            'column_4': '投运日期',\n            'column_5': '',\n            'column_6': '',\n            'column_7': '',\n            'column_8': '',\n          }\n        ],\n        //设备铭牌表格数据\n        tableData_sbmp: [{\n          'column_1': '额定电压',\n          'column_2': '设备型号',\n          'column_3': '',\n          'column_4': '',\n          'column_5': '',\n          'column_6': '',\n        },],\n        //要循环的试验表格数据\n        arr: [{\n          title: \"\",//试验名称\n          zxmList: [],//子项目数据（表头）\n          bwList: [],//部位数据（第一列开头）\n        }],\n        //下载弹出框控制\n        isShowDownLoadDialog: false,\n        printObj: {\n          id: \"previewId\", // 必填，渲染打印的内容使用\n          popTitle: \"&nbsp;\", //\n          previewTitle: \"&nbsp;\",\n          preview: false,\n        },\n        mbInfo: {},\n        //打印内容div中id值\n        previewId: \"\",\n        //定义模板内容弹出框传递参数\n        mbRowData: {},\n        //定义模板内容弹出框\n        isShowXmGlbwDialog: false,\n        xmSelectedForm: {\n          //试验模板id\n          symbid: undefined,\n          //试验项目数据集合\n          xmDataRows: []\n        },\n        //项目库弹出框标题\n        xmLibraryAddDialogTitle: '项目库',\n        //项目库弹出框控制\n        isShowAddGlxmDialog: false,\n        //项目库查询参数\n        xmLibraryQueryForm: {\n          symbid: undefined,\n          syxmmc: '',\n          pageNum: 1,\n          pageSize: 10\n        },\n        //项目库数据\n        xmLibraryDataList: [],\n        //项目库项目总数\n        xmLibraryTotal: 0,\n        //表单验证\n        mbzbRules: {\n          mbmc: [\n            {required: true, message: '请输入模板名称', trigger: 'blur'}\n          ]\n        },\n        // 筛选条件\n        filterInfo: {\n          data: {\n            mbmc: ''\n          },\n          fieldList: [\n            {label: '模板名称', type: 'input', value: 'mbmc', multiple: true}\n          ]\n        },\n        //新增按钮控制\n        addDisabled: true,\n        //树结构懒加载参数\n        props: {\n          label: 'name',\n          children: 'zones',\n          isLeaf: 'leaf',\n        },\n        //删除选择列\n        selectRows: [],\n        //选中的单条对象\n        selectRowData: {},\n        //弹出框表单\n        form: {},\n        //查询试验部位参数\n        querySyBwParam: {\n          sblxid: undefined,\n          mbmc: undefined,\n          pageNum: 1,\n          pageSize: 10\n        },\n        //点击树节点赋值\n        treeForm: {},\n        //试验部位列表\n        tableAndPageInfo: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          tableData: [],\n          tableHeader: [\n            {label: '模板名称', prop: 'mbmc', minWidth: '100'},\n            {label: '是否默认', prop: 'sfmr', minWidth: '100'},\n            {label: '是否停用', prop: 'sfty', minWidth: '100'},\n          ],\n          option: {checkBox: true, serialNumber: true}\n        },\n        //组织树\n        treeOptions: [],\n\n        isShowDetails: false,\n        title: '',\n\n        //删除是否可用\n        multipleSensor: true,\n        //查询参数\n        queryParams: {\n          bm: undefined,\n          pageNum: 1,\n          pageSize: 10\n        },\n        isDisabled: false,\n        isShow: true,\n\n        //关联项目弹出框title\n        glxmDialogTitle: '关联项目',\n        //关联项目弹出框控制展开\n        isGlxmDialogShow: false,\n\n        //关联项目total\n        glxmTotal: 0,\n        //关联项目查询参数\n        glxmQueryParams: {\n          symbid: undefined,\n          pageNum: 1,\n          pageSize: 10\n        },\n\n        //关联子项目total\n        glzxmTotal: 0,\n        //关联子项目查询参数\n        glzxmQueryParams: {\n          syxmid: undefined,\n          pageSize: 10,\n          pageNum: 1\n        },\n\n        //模板关联项目数据\n        mbGlxmDataList: [],\n        //项目关联的子项目数据\n        zxmGlmbDataList: [],\n        //模板关联项目选中框数据\n        selectedRowDataChange: [],\n        //显示铭牌弹框\n        showMpDialog: false,\n        //选中行数据\n        rowData: {},\n\n        //关联名牌便利\n        mpList: [],\n\n        //试验数据\n        sysjDataList: [],\n        //试验表格默认固定的行\n        defaultRow: [\"仪器型号\", \"结论\", \"备注\"],\n      }\n    },\n    watch: {},\n    created() {\n      //获取数据列表\n      this.getData()\n    },\n    mounted() {\n    },\n    methods: {\n      //试验数据表格合并方法\n      arraySpanMethod({row, column, rowIndex, columnIndex}) {\n        if (this.defaultRow.includes(row.SYBW)) {\n          if (columnIndex > 0) {\n            return [1, row.totalNum]\n          }\n        }\n      },\n      //设备铭牌表格合并方法\n      sbmpSpanMethod({row, column, rowIndex, columnIndex}) {\n        if (columnIndex > 3) {\n          return [1, 2]\n        }\n      },\n      //模板详情按钮\n      handleMbInfo(row) {\n        //获取当前模板id加载页面信息\n        this.getMbGlMpinfoData(row);\n        //获取试验数据\n        this.getMbGlXmAndBw(row);\n      },\n      //导出pdf操作\n      downloadPdf() {\n        htmlToPdf.downloadPDF(document.querySelector('#printContentId'), this.mbInfo.mbmc)\n      },\n      //获取当前模板id加载页面信息\n      getMbGlMpinfoData(param) {\n        getMbGlMpinfoData(param).then(res => {\n          this.mpList = res.data;\n          //调用渲染铭牌页面开始\n          // this.applyMpHtml(this.mpList, param);\n          //打开弹出框\n          this.isShowDownLoadDialog = true;\n        })\n\n      },\n      //获取试验数据\n      getMbGlXmAndBw(rowData) {\n        //每次获取数据前先清空，再添加，否则多次进入页面时会获取重复数据\n        this.sysjDataList = [];\n        getMbGlXmAndBw(rowData).then(res => {\n          let resMap = res.data;\n          //遍历返回结果\n          for (let key in resMap) {\n            //解析试验数据\n            this.analysisSyData(key, resMap[key]);\n          }\n          //画试验数据页面\n          this.applySysjDataToHtml();\n        })\n      },\n      //解析后台试验数据\n      analysisSyData(syxmmc, zxmAndBwData) {\n        let sysjData = {}\n        sysjData.syxmmc = syxmmc;\n        for (let key in zxmAndBwData[0]) {\n          sysjData[key] = zxmAndBwData[0][key]\n        }\n        this.sysjDataList.push(sysjData);\n      },\n      //渲染实验数据到页面\n      applySysjDataToHtml() {\n        this.arr = [];\n        // $('#sysjTableId').html(\"\");\n        //进行数据处理重组\n        let data = this.sysjDataList;\n        if (data.length > 0) {\n          for (let i = 0; i < data.length; i++) {\n            let dataChild = data[i];\n            let title = dataChild.syxmmc;//试验项目名称\n            let bwList = dataChild.bwList; //部位list\n            let zxmList = dataChild.zxmList; //子项目list\n            let hx = [\n              {\n                \"label\": title, //第一个表头为试验项目名称\n                \"column_name\": \"SYBW\", //第一列对应的字段名（试验部位）\n              },\n            ];\n            zxmList.forEach(zxm => {\n              hx.push({\n                \"label\": zxm.syzxmmc, //每列的表头\n                \"column_name\": \"\", //每列对应的数值暂时设置为空白\n              })\n            })\n            let sx = [];\n            bwList.forEach(bw => {\n              sx.push({\n                \"SYBW\": bw.SYBW,\n                \"totalNum\": zxmList.length//记录总列数，用于固定行的合并事件\n              })\n            })\n            //后四行固定\n            sx.push(\n              {\n                \"SYBW\": \"结果\",\n                \"totalNum\": zxmList.length//记录总列数，用于固定行的合并事件\n              },\n              {\n                \"SYBW\": \"仪器型号\",\n                \"totalNum\": zxmList.length//记录总列数，用于固定行的合并事件\n              },\n              {\n                \"SYBW\": \"结论\",\n                \"totalNum\": zxmList.length//记录总列数，用于固定行的合并事件\n              },\n              {\n                \"SYBW\": \"备注\",\n                \"totalNum\": zxmList.length//记录总列数，用于固定行的合并事件\n              }\n            )\n            this.arr.push({\n              title: title,\n              zxmList: hx,\n              bwList: sx,\n            });\n          }\n        }\n        //拼接的铭牌表格\n        /*     let str = \"\";\n             if (this.sysjDataList.length > 0) {\n               for (let i = 0; i < this.sysjDataList.length; i++) {\n                 //拼接项目序号\n                 let xmIndex = i + 1;\n                 str += \"<tr><td colspan='5' style='text-align: left;font-weight: bold;font-size: 15px'>\" + xmIndex + \"、\" + this.sysjDataList[i].syxmmc + \"</td></tr>\";\n                 // this.sysjDataList[i].bwList;\n                 // this.sysjDataList[i].zxmList;\n                 // str += \"<tr><td>\"+this.sysjDataList[i].syxmmc+\"</td><td v-for=item in this.sysjDataList[i].bwList></td></tr>\"\n\n               }\n               this.$nextTick(() => {\n                 $('#sysjTableId').append(str)\n               })\n             }*/\n      },\n      //渲染铭牌页面开始mpList:反回的铭牌列表  row：模板行对象\n      applyMpHtml(mpList, row) {\n        //每次打开需要重新渲染一次,先将置空\n        $('#sbmpTbodyId').html(\"\");\n        //清空重新赋值\n        this.mbInfo = {}\n        this.mbInfo.mbmc = row.mbmc;\n        //拼接的铭牌表格\n        let str = \"\";\n        //先判断是否分相铭牌\n        if (mpList.length > 0) {\n          if (mpList[0].SFFX == '1') { //当前铭牌为分相铭牌时\n            //写死第一行\n            str += \"<tr><td style='padding: 10px;font-size: 15px;'>相别</td>\" +\n              \"<td style='padding: 10px;font-size: 15px;'>A</td>\" +\n              \"<td style='padding: 10px;font-size: 15px;'>B</td>\" +\n              \"<td style='padding: 10px;font-size: 15px;'>C</td>\" +\n              \"</tr>\";\n            //开始遍历展示\n            for (let a = 0; a < mpList.length; a++) {\n              str += \"<tr>\"\n              str += \"<td style='padding: 10px;font-size: 15px;'>\";\n              str += mpList[a].title + \"</td>\";\n              str += \"<td></td>>\"\n              str += \"<td></td>>\"\n              str += \"<td></td>>\"\n              str += \"</tr>\"\n            }\n          } else {  //铭牌不分相\n            //当前铭牌不属于分相铭牌\n            //每列展示单元格数量\n            let col = 3;\n            //展示行数\n            var lines = Math.ceil(mpList.length / col);\n            //遍历展示行数\n            for (var i = 0; i < lines; i++) {\n              str += \"<tr>\";\n              //遍历列\n              for (var j = 0; j < col; j++) {\n                if (i * col + j < mpList.length) {\n                  str += \"<td style='padding: 10px;font-size: 15px;'>\";\n                  //铭牌标题赋值\n                  str += mpList[i * col + j].title + \"</td>\";\n                  //铭牌值赋值\n                  str += mpList[i * col + j].sfmb == 1 ? \"<td> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>\" : \"<td>\" + mpList[i * col + j].column_name + \"</td>\"\n                }\n              }\n              str += \"</tr>\";\n            }\n          }\n        }\n        //渲染铭牌页面\n        this.$nextTick(() => {\n          $('#sbmpTbodyId').append(str)\n        })\n        //打开弹出框\n        this.isShowDownLoadDialog = true;\n      },\n      //关闭预览弹出框\n      closeYlDialog() {\n        //清空表单\n        this.mbInfo = {};\n        //赋值完关闭弹窗\n        this.isShowDownLoadDialog = false;\n      }\n      ,\n      //定义模板内容\n      handleClickMbnr(row) {\n        //打开组件弹出框\n        this.isShowXmGlbwDialog = true;\n        //给子组件传递数据\n        this.mbRowData = row;\n      }\n      ,\n      //获取项目库项目数据\n      getXmLiraryData() {\n        getXmLiraryData(this.xmLibraryQueryForm).then(res => {\n          this.xmLibraryDataList = res.data.records\n          this.xmLibraryTotal = res.data.total\n        })\n      }\n      ,\n      //项目弹出框新增按钮\n      addMbGlXm() {\n        this.getXmLiraryData()\n        this.isShowAddGlxmDialog = true\n      }\n      ,\n      //项目库弹出框取消按钮\n      closeAddMjzDialog() {\n        this.isShowAddGlxmDialog = false\n      }\n      ,\n      //项目库弹窗确认按钮\n      commitAddMjzForm() {\n        if (this.xmSelectedForm.xmDataRows.length < 1) {\n          this.$message.info('未关联项目！！！已取消')\n          //如果未选中数据,则直接关闭弹窗\n          this.isShowAddGlxmDialog = false\n        } else {\n          console.log(this.xmSelectedForm)\n          //若选择数据后\n          addMbGlxmBatchToMbxm(this.xmSelectedForm).then(res => {\n            if (res.code === '0000') {\n              this.$message.success('关联成功')\n            } else {\n              this.$message.error('关联失败！！')\n            }\n            //关闭弹窗\n            this.isShowAddGlxmDialog = false\n            //调用获取关联子项目列表\n            this.getSymbGlsyxmDataListByPage()\n          })\n        }\n      }\n      ,\n      //项目库行选中事件\n      handleSelectedXmLibraryChange(rows) {\n        this.xmSelectedForm.xmDataRows = rows\n      }\n      ,\n      //项目库查询按钮\n      selectxmLibrary() {\n        this.getXmLiraryData()\n      }\n      ,\n      //项目库重置按钮\n      resetxmSearch() {\n        this.xmLibraryQueryForm.syxmmc = ''\n        this.getXmLiraryData()\n      }\n      ,\n      //获取关联子列表方法\n      getZxmDataList() {\n        getGlSyzxmDataListByPage(this.glzxmQueryParams).then(res => {\n          this.glzxmTotal = res.data.total\n          this.zxmGlmbDataList = res.data.records\n        })\n      }\n      ,\n\n      //关联项目\n      handleClickGlxm(row) {\n        //清空原来子项目数据\n        this.zxmGlmbDataList = []\n        //打开关联项目弹出框\n        this.isGlxmDialogShow = true\n        //给参数赋值\n        this.glxmQueryParams.symbid = row.objId\n        //查询项目库数据时参数\n        this.xmLibraryQueryForm.symbid = row.objId\n        //给试验项目库添加时使用\n        this.xmSelectedForm.symbid = row.objId\n        //获取模板关联项目数据\n        this.getSymbGlsyxmDataListByPage()\n      }\n      ,\n      //获取关联项目弹出框数据\n      getSymbGlsyxmDataListByPage() {\n        getSymbGlsyxmDataListByPage(this.glxmQueryParams).then(res => {\n          this.mbGlxmDataList = res.data.records\n          this.glxmTotal = res.data.total\n        })\n      }\n      ,\n      //试验项目复选框点击时间点击操作\n      handleGlxmSelectedChange(rows) {\n        this.selectedRowDataChange = rows\n      }\n      ,\n      //删除模板关联项目\n      deleteMbGlXm() {\n        if (this.selectedRowDataChange.length < 1) {\n          this.$message.warning('请选择正确的数据！！！')\n          return\n        }\n        let ids = this.selectedRowDataChange.map(item => {\n          return item.objId\n        })\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove(ids).then(({code}) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getSymbGlsyxmDataListByPage()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n              this.getSymbGlsyxmDataListByPage()\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n      //试验项目点击行数据时的单机操作\n      handleMbGlxmRowClick(row) {\n        this.glzxmQueryParams.syxmid = row.syxmid\n        this.getZxmDataList()\n      },\n      //懒加载函数\n      loadNode(node, resolve) {\n        let TreeparamMap = {\n          pid: '',\n          spbLogo: ['输电设备', '变电设备','配电设备']\n        }\n        if (node.level === 0) {\n          TreeparamMap.pid = 'sb'\n          return this.getTreeNode(TreeparamMap, resolve)\n        }\n        setTimeout(() => {\n          TreeparamMap.pid = node.data.code\n          this.getTreeNode(TreeparamMap, resolve)\n        }, 500)\n\n      },\n      //获取树节点数据\n      getTreeNode(paramMap, resolve) {\n        getDeviceClassTreeNodeByPid(paramMap).then(res => {\n          let treeNodes = []\n          res.data.forEach(item => {\n            let node = {\n              name: item.name,\n              level: item.level,\n              id: item.id,\n              pid: item.pid,\n              leaf: false,\n              code: item.code\n            }\n            treeNodes.push(node)\n          })\n          resolve(treeNodes)\n        })\n      },\n      //添加后确认保存按钮\n      save() {\n        this.$refs['form'].validate((valid) => {\n          if (valid) {\n            saveOrUpdate(this.form).then(res => {\n              if (res.code === '0000') {\n                this.$message.success(res.msg)\n                this.tableAndPageInfo.pager.pageResize = 'Y'\n                this.getData()\n                this.isShowDetails = false\n              } else {\n                this.$message.error(res.msg)\n              }\n            })\n\n          }\n        })\n      },\n      //树节点点击事件\n      handleNodeClick(data) {\n        console.log('树节点点击')\n        console.log(data)\n        if (data.level != '0' && data.level != '1') {\n          //新增按钮可点击\n          this.addDisabled = false\n          this.treeForm = data\n          this.querySyBwParam.sblxid = data.code\n          this.getData()\n        } else {\n          this.addDisabled = true\n        }\n      },\n      //添加按钮\n      addSensorButton() {\n        this.form = {}\n        this.form.sblx = this.treeForm.name\n        this.form.sblxid = this.treeForm.code\n        this.isShowDetails = true\n        this.title = '新增'\n      },\n      //列表查询\n      async getData(params) {\n        try {\n          const param = {...this.querySyBwParam, ...params}\n          const {data, code} = await getPageDataListTosymb(param)\n          if (code === '0000') {\n            this.tableAndPageInfo.tableData = data.records\n            this.tableAndPageInfo.pager.total = data.total\n          }\n        } catch (e) {\n          console.log(e)\n        }\n      }\n      ,\n      /**\n       * 表格多选框\n       */\n      handleSelectionChange(rows) {\n        this.selectRows = rows;\n      },\n\n      close() {\n        this.isShowDetails = false\n      }\n      ,\n      //修改模板主表内容\n      updateDetails(row) {\n        this.title = '修改'\n        this.isShowDetails = true\n        this.form = row\n        this.isDisabled = false\n        this.isShow = true\n      }\n      ,\n\n      createTemplate(row) {\n        console.log(row)\n      }\n      ,\n      //查看模板主表详情按钮\n      getDetails(row) {\n        this.title = '详情'\n        this.isShowDetails = true\n        this.form = row\n        this.isDisabled = true\n        this.isShow = false\n      }\n      ,\n\n      //删除按钮\n      deleteSensorButton() {\n        if (this.selectRows.length < 1) {\n          this.$message.warning('请选择正确的数据！！！')\n          return\n        }\n        let ids = this.selectRows.map(item => {\n          return item.objId\n        })\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove(ids).then(({code}) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.tableAndPageInfo.pager.pageResize = 'Y'\n              this.getData()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n              this.tableAndPageInfo.pager.pageResize = 'Y'\n              this.getData()\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n\n      }\n      ,\n      //导出按钮\n      handleExport() {\n\n      }\n      ,\n\n      //关联铭牌点击事件\n      handleClickGlMp(row) {\n        this.showMpDialog = true\n        this.rowData = row\n      }\n      ,\n      //关闭试验铭牌弹窗\n      closeMpDialog() {\n        this.showMpDialog = false\n      }\n      ,\n\n      filterReset() {\n      },\n\n      //关闭试验模板弹窗\n      closeSymbComment() {\n        this.$emit(\"closeSymbSelectDialog\", false)\n      },\n      //点击确认后给父组件传递数据\n      // this.selectRowData != undefined && JSON.stringify(this.selectRowData) != \"{}\"\n      commitMbdata() {\n        if (this.selectRows.length == 1 && this.selectRows != undefined) {\n         this.$emit(\"handleAcceptMbData\",this.selectRows[0]);\n          this.$emit(\"closeSymbSelectDialog\", false)\n        } else {\n          this.$message.warning(\"请选择一条数据\")\n        }\n      },\n\n    }\n  }\n</script>\n\n<style lang='scss' scoped>118\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n/*导出pdf格式设置开始*/\n#printContentId {\n  background-color: #fff;\n  width: 100%;\n  /* height: 400px; */\n  margin: auto;\n  padding: 16px;\n  box-sizing: border-box;\n  //试验数据样式\n  .printTitle {\n    text-align: left;\n    line-height: 40px;\n    border-left: 1px solid #000;\n    border-right: 1px solid #000;\n    //border-bottom: 1px solid #000;\n    padding-left: 10px;\n  }\n}\n\n//修改table表头颜色\n/deep/ #printContentId .el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th {\n  background: #fff;\n  border-color: #000;\n  font-weight: inherit;\n}\n\n/deep/ #printContentId .el-table--enable-row-transition .el-table__body td {\n  border-color: #000;\n}\n\n.table_style td, th {\n  padding: 10px;\n  font-size: 15px;\n}\n\n.table_style {\n  border-collapse: collapse;\n  width: 100%;\n  text-align: center;\n  /* border-bottom: 0;\n   border-left: 0;\n   border-right: 0;*/\n}\n\n/* 设置滚动条的样式 */\n::-webkit-scrollbar {\n  width: 12px;\n}\n\n/* 滚动槽 */\n::-webkit-scrollbar-track {\n  //-webkit-box-shadow:inset006pxrgba(0,0,0,0.3);\n  border-radius: 10px;\n}\n\n/* 滚动条滑块 */\n::-webkit-scrollbar-thumb {\n  border-radius: 10px;\n  background: rgba(0, 0, 0, 0.1);\n  //-webkit-box-shadow:gba(0,0,0,0.5);\n}\n\n::-webkit-scrollbar-thumb:window-inactive {\n  background: rgba(0, 0, 0, 0.1);\n}\n\n[data-v-67a974b1]::-webkit-scrollbar {\n  width: 8px;\n}\n\n.item {\n  width: 225px;\n  float: left;\n}\n\n/*导出pdf格式设置结束*/\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  max-height: 79vh;\n  overflow: auto;\n}\n\n.app-container {\n  padding: 3px;\n}\n</style>\n"]}]}