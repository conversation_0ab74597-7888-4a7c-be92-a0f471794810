{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\canvg\\node_modules\\@babel\\runtime\\helpers\\defineProperty.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\canvg\\node_modules\\@babel\\runtime\\helpers\\defineProperty.js", "mtime": 456789000000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZnVuY3Rpb24gX2RlZmluZVByb3BlcnR5KG9iaiwga2V5LCB2YWx1ZSkgewogIGlmIChrZXkgaW4gb2JqKSB7CiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkob2JqLCBrZXksIHsKICAgICAgdmFsdWU6IHZhbHVlLAogICAgICBlbnVtZXJhYmxlOiB0cnVlLAogICAgICBjb25maWd1cmFibGU6IHRydWUsCiAgICAgIHdyaXRhYmxlOiB0cnVlCiAgICB9KTsKICB9IGVsc2UgewogICAgb2JqW2tleV0gPSB2YWx1ZTsKICB9CgogIHJldHVybiBvYmo7Cn0KCm1vZHVsZS5leHBvcnRzID0gX2RlZmluZVByb3BlcnR5LCBtb2R1bGUuZXhwb3J0cy5fX2VzTW9kdWxlID0gdHJ1ZSwgbW9kdWxlLmV4cG9ydHNbImRlZmF1bHQiXSA9IG1vZHVsZS5leHBvcnRzOw=="}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/node_modules/canvg/node_modules/@babel/runtime/helpers/defineProperty.js"], "names": ["_defineProperty", "obj", "key", "value", "Object", "defineProperty", "enumerable", "configurable", "writable", "module", "exports", "__esModule"], "mappings": "AAAA,SAASA,eAAT,CAAyBC,GAAzB,EAA8BC,GAA9B,EAAmCC,KAAnC,EAA0C;AACxC,MAAID,GAAG,IAAID,GAAX,EAAgB;AACdG,IAAAA,MAAM,CAACC,cAAP,CAAsBJ,GAAtB,EAA2BC,GAA3B,EAAgC;AAC9BC,MAAAA,KAAK,EAAEA,KADuB;AAE9BG,MAAAA,UAAU,EAAE,IAFkB;AAG9BC,MAAAA,YAAY,EAAE,IAHgB;AAI9BC,MAAAA,QAAQ,EAAE;AAJoB,KAAhC;AAMD,GAPD,MAOO;AACLP,IAAAA,GAAG,CAACC,GAAD,CAAH,GAAWC,KAAX;AACD;;AAED,SAAOF,GAAP;AACD;;AAEDQ,MAAM,CAACC,OAAP,GAAiBV,eAAjB,EAAkCS,MAAM,CAACC,OAAP,CAAeC,UAAf,GAA4B,IAA9D,EAAoEF,MAAM,CAACC,OAAP,CAAe,SAAf,IAA4BD,MAAM,CAACC,OAAvG", "sourcesContent": ["function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nmodule.exports = _defineProperty, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"]}]}