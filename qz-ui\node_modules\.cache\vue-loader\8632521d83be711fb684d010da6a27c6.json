{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\gfdzcz\\components\\fwzz_ybj.vue?vue&type=template&id=8a6a4732&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\gfdzcz\\components\\fwzz_ybj.vue", "mtime": 1751368448942}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}