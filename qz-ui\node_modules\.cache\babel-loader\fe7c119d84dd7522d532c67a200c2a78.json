{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\jxjjhgl\\wxxmgl\\whxmjh.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\jxjjhgl\\wxxmgl\\whxmjh.js", "mtime": 1706897314772}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0UGFnZURhdGFMaXN0ID0gZ2V0UGFnZURhdGFMaXN0OwpleHBvcnRzLnNhdmVPclVwZGF0ZSA9IHNhdmVPclVwZGF0ZTsKZXhwb3J0cy5yZW1vdmUgPSByZW1vdmU7Cgp2YXIgX3JlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdXRpbHMvcmVxdWVzdCIpKTsKCnZhciBiYXNlVXJsID0gIi9yZXBhaXItc2NoZWR1bGUtYXBpIjsgLy8g5p+l6K+i5YW45Z6L5pON5L2c56Wo5Y+K5piO57uG6KGoCgpmdW5jdGlvbiBnZXRQYWdlRGF0YUxpc3QocGFyYW1zKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcvd2h4bWpoL3BhZ2UnLCBwYXJhbXMsIDEpOwp9IC8vIOa3u+WKoOaIluS/ruaUuQoKCmZ1bmN0aW9uIHNhdmVPclVwZGF0ZShwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy93aHhtamgvc2F2ZU9yVXBkYXRlJywgcGFyYW1zLCAxKTsKfSAvLyDliKDpmaQKCgpmdW5jdGlvbiByZW1vdmUocGFyYW1zKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcvd2h4bWpoL3JlbW92ZScsIHBhcmFtcywgMSk7Cn0="}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagang<PERSON>field/jxjjhgl/wxxmgl/whxmjh.js"], "names": ["baseUrl", "getPageDataList", "params", "api", "requestPost", "saveOrUpdate", "remove"], "mappings": ";;;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,sBAAhB,C,CAEA;;AACO,SAASC,eAAT,CAAyBC,MAAzB,EAAiC;AACtC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,cAAxB,EAAuCE,MAAvC,EAA8C,CAA9C,CAAP;AACD,C,CAED;;;AACO,SAASG,YAAT,CAAsBH,MAAtB,EAA8B;AACnC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,sBAAxB,EAA+CE,MAA/C,EAAsD,CAAtD,CAAP;AACD,C,CAED;;;AACO,SAASI,MAAT,CAAgBJ,MAAhB,EAAwB;AAC7B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,gBAAxB,EAAyCE,MAAzC,EAAgD,CAAhD,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\nconst baseUrl = \"/repair-schedule-api\";\n\n// 查询典型操作票及明细表\nexport function getPageDataList(params) {\n  return api.requestPost(baseUrl+'/whxmjh/page',params,1)\n}\n\n// 添加或修改\nexport function saveOrUpdate(params) {\n  return api.requestPost(baseUrl+'/whxmjh/saveOrUpdate',params,1)\n}\n\n// 删除\nexport function remove(params) {\n  return api.requestPost(baseUrl+'/whxmjh/remove',params,1)\n}\n"]}]}