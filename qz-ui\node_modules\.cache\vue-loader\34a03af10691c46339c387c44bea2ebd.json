{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sympk\\sympInfo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sympk\\sympInfo.vue", "mtime": 1706897323740}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["sympInfo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+DA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "sympInfo.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sympk", "sourcesContent": ["<template>\n  <div>\n    <el-white>\n      <el-white class=\"button-group\">\n        <el-button class=\"mb8\" @click=\"addButton\" type=\"primary\" icon=\"el-icon-plus\">\n          新增\n        </el-button>\n        <el-button class=\"mb8\" @click=\"deleteButton\" type=\"danger\" icon=\"el-icon-delete\">\n          删除\n        </el-button>\n      </el-white>\n      <comp-table ref=\"mainTable\" :table-and-page-info=\"tableAndPageInfo\"\n                  @update:multipleSelection=\"handleSelectionChange\"/>\n    </el-white>\n\n    <el-dialog\n      :title=\"dialogTittle\"\n      v-dialogDrag\n      :visible.sync=\"isShowDetails\"\n      :append-to-body=\"true\"\n      v-if=\"isShowDetails\"\n      width=\"40%\">\n      <el-form ref=\"mpInfoForm\" :model=\"mpInfoForm\" label-width=\"80px\">\n        <el-row :gutter=\"8\">\n          <el-col :span=\"12\">\n            <el-form-item prop=\"title\" label=\"标题:\">\n              <el-input v-model=\"mpInfoForm.title\" class=\"form-item\" placeholder=\"请输入标题\"\n                        :disabled=\"isDetails\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"内容来源:\" prop=\"contentSource\">\n              <el-select clearable :disabled=\"isDetails\" class=\"form-item\" v-model=\"mpInfoForm.contentSource\">\n                <el-option v-for=\"item in contentSourceOptions\"\n                           :key=\"item.value\"\n                           :label=\"item.label\"\n                           :value=\"item.value\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"字段名称:\" prop=\"columnName\">\n              <el-select clearable :disabled=\"isDetails\" class=\"form-item\" v-model=\"mpInfoForm.columnName\">\n                <el-option v-for=\"item in columnNameOptions\"\n                           :key=\"item.value\"\n                           :label=\"item.label\"\n                           :value=\"item.value\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"handleClose\">取 消</el-button>\n        <el-button v-show=\"!isDetails\" type=\"primary\" @click=\"submitForm\">确 定</el-button>\n      </div>\n    </el-dialog>\n\n  </div>\n\n</template>\n\n<script>\nimport {\n  deleteNameplateContent,\n  getColumnNameOptions,\n  getNameplateContent,\n  saveNameplateContent\n} from '@/api/dagangOilfield/bzgl/sympk/sympInfo'\n\nexport default {\n  props: {\n    mpData: {\n      type: Object\n    }\n  },\n  name: 'sympInfo',\n  data() {\n    return {\n      //表单 分页数据\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [], tableHeader: [\n          { label: '标题', prop: 'title', minWidth: '180' },\n          { label: '内容来源', prop: 'contentSourceName', minWidth: '200' },\n          { label: '字段名', prop: 'zdmc', minWidth: '200' },\n          {\n            prop: 'operation',\n            label: '操作',\n            minWidth: '100px',\n            style: { display: 'block' },\n            //操作列固定再右侧\n            fixed: 'right',\n            operation: [\n              { name: '修改', clickFun: this.updateDetails },\n              { name: '详情', clickFun: this.getDetails }\n            ]\n          }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      mpInfoForm: {\n        id: '',\n        title: '',\n        order: '',\n        contentSource: '',\n        columnName: '',\n        mpid: ''\n      },\n      //是否展示详情弹窗\n      isShowDetails: false,\n      //铭牌内容弹窗标题\n      dialogTittle: '',\n      //是否详情\n      isDetails: false,\n      //内容来源下拉框数据\n      contentSourceOptions: [{ label: '技术参数', value: 'jscs' }, { label: '设备', value: 'sb' }],\n      //字段名称下拉框数据\n      columnNameOptions: [],\n      //选中行数据\n      selectedRowData: [],\n      //查询条件\n      params: {\n        pageNum: 1,\n        pageSize: 10,\n        mpid: '',\n        zy: ''\n      }\n    }\n  },\n  mounted() {\n    this.getData()\n  },\n  methods: {\n    //获取铭牌内容数据\n    getData() {\n      debugger;\n      this.$refs.mainTable.loading = true\n      this.params.mpid = this.mpData.obj_id\n      this.params.zy = this.mpData.zy\n      this.params.sblxbm = this.mpData.sblxbm\n      getNameplateContent(this.params).then(res => {\n        if (res.code === '0000') {\n          this.tableAndPageInfo.tableData = res.data.records\n          this.tableAndPageInfo.pager.total = res.data.total\n          //列表转码\n          this.tableAndPageInfo.tableData.forEach(item => {\n            //内容来源为设备时 切除专业编码\n            if (item.contentSource.indexOf('sb') === 0) {\n              item.contentSource = item.contentSource.substring(0, item.contentSource.length - 2)\n            }\n            this.contentSourceOptions.forEach(element => {\n              if (item.contentSource.indexOf(element.value) === 0) {\n                item.contentSourceName = element.label\n              }\n            })\n          })\n          this.$refs.mainTable.loading = false\n        }\n      })\n    },\n    //新增按钮\n    addButton() {\n      this.dialogTittle = '新增铭牌内容'\n      this.mpInfoForm = this.$options.data().mpInfoForm\n      this.mpInfoForm.mpid = this.mpData.obj_id\n      this.isShowDetails = true\n      this.isDetails = false\n    },\n    //修改\n    updateDetails(row) {\n      this.dialogTittle = '修改铭牌内容'\n      this.mpInfoForm = row\n      this.isDetails = false\n      this.isShowDetails = true\n    },\n    //详情\n    getDetails(row) {\n      this.dialogTittle = '铭牌内容详情'\n      this.mpInfoForm = row\n      this.isDetails = true\n      this.isShowDetails = true\n    },\n    //删除\n    deleteButton() {\n\n      this.$confirm('是否确认删除当前勾选的数据?', '警告', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function() {\n        let ids = []\n        this.selectedRowData.forEach(item => {\n          ids.push(item.id)\n        })\n\n        deleteNameplateContent(ids).then(res => {\n          if (res.code === '0000') {\n            this.$message.success('删除成功')\n          } else {\n            this.$message.error(res.msg)\n          }\n        })\n      }).catch(function() {\n      })\n\n    },\n\n    handleSelectionChange(row) {\n      this.selectedRowData = row\n    },\n\n    handleClose() {\n      this.isShowDetails = false\n    },\n    submitForm() {\n      this.mpInfoForm.zy = this.mpData.zy\n      saveNameplateContent(this.mpInfoForm).then(res => {\n        if (res.code === '0000') {\n          this.isShowDetails = false\n          this.getData()\n        }\n      })\n    }\n\n  },\n  watch: {\n    'mpInfoForm.contentSource'(val) {\n      if (!val || val === '') {\n        this.columnNameOptions = []\n      } else {\n        let params = {}\n        params.type = val\n        params.zy = this.mpData.zy\n        params.sblxbm = this.mpData.sblxbm\n        getColumnNameOptions(params).then(res => {\n          this.columnNameOptions = res.data\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.form-item {\n  width: 80%;\n}\n</style>\n"]}]}