{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\bdgl\\jgtz.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\bdgl\\jgtz.vue", "mtime": 1752462716963}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["jgtz.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAskDA;;AACA;;AAgBA;;AAQA;;AACA;;AACA;;AAIA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AAOA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,MADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,IAAA,EAAA,aAAA;AAAA,IAAA,UAAA,EAAA;AAAA,GAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA,MAAA,iBAAA,EAAA,EADA;AAEA,MAAA,UAAA,EAAA,EAFA;AAGA,MAAA,QAAA,EAAA,EAHA;AAIA,MAAA,UAAA,EAAA,KAJA;AAKA,MAAA,OAAA,EAAA,KALA;AAMA,MAAA,KAAA,EAAA;AACA,QAAA,OAAA,EAAA,mBADA;AAEA,QAAA,GAAA,EAAA,WAFA;AAGA,QAAA,EAAA,EAAA,WAHA;AAIA,QAAA,IAAA,EAAA,WAJA,CAIA;;AAJA,OANA;AAaA;AACA,MAAA,kBAAA,EAAA,EAdA;AAeA;AACA,MAAA,mBAAA,EAAA,EAhBA;AAiBA;AACA,MAAA,UAAA,EAAA,EAlBA;AAmBA;AACA,MAAA,wBAAA,EAAA,EApBA;AAqBA;AACA,MAAA,QAAA,EAAA,EAtBA;AAuBA;AACA,MAAA,wBAAA,EAAA,EAxBA;AAyBA;AACA,MAAA,YAAA,EAAA,EA1BA;AA2BA;AACA,MAAA,qBAAA,EAAA,EA5BA;AA6BA;AACA,MAAA,mBAAA,EAAA,EA9BA;AA+BA;AACA,MAAA,YAAA,EAAA,EAhCA;AAiCA,MAAA,SAAA,EAAA,EAjCA;AAkCA,MAAA,SAAA,EAAA,EAlCA;AAmCA,MAAA,eAAA,EAAA,SAnCA;AAoCA;AACA,MAAA,MAAA,EAAA,KArCA;AAsCA,MAAA,UAAA,EAAA,EAtCA;AAuCA;AACA,MAAA,gBAAA,EAAA,EAxCA;AAyCA,MAAA,aAAA,EAAA;AACA,QAAA,KAAA,EAAA,SADA;AAEA,QAAA,IAAA,EAAA,SAFA;AAGA,QAAA,QAAA,EAAA,EAHA;AAIA,QAAA,OAAA,EAAA;AAJA,OAzCA;AA+CA,MAAA,QAAA,EAAA;AACA,QAAA,KAAA,EAAA,SADA;AAEA,QAAA,IAAA,EAAA,SAFA;AAGA,QAAA,IAAA,EAAA,SAHA;AAIA,QAAA,IAAA,EAAA,SAJA;AAKA,QAAA,IAAA,EAAA,SALA;AAMA,QAAA,IAAA,EAAA,SANA;AAOA,QAAA,IAAA,EAAA,SAPA;AAQA,QAAA,IAAA,EAAA,SARA;AASA,QAAA,EAAA,EAAA,SATA;AAUA,QAAA,IAAA,EAAA,SAVA;AAWA,QAAA,IAAA,EAAA,SAXA;AAYA,QAAA,EAAA,EAAA,SAZA;AAaA,QAAA,EAAA,EAAA,SAbA;AAcA,QAAA,IAAA,EAAA,SAdA;AAeA,QAAA,KAAA,EAAA,SAfA;AAgBA,QAAA,KAAA,EAAA,SAhBA;AAiBA,QAAA,EAAA,EAAA;AAjBA,OA/CA;AAkEA;AACA,MAAA,WAAA,EAAA,KAnEA;AAoEA;AACA,MAAA,mBAAA,EAAA,KArEA;AAsEA;AACA,MAAA,IAAA,EAAA,EAvEA;AAwEA,MAAA,aAAA,EAAA;AACA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,IAAA,EAAA,IADA;AAEA,UAAA,OAFA,mBAEA,MAFA,EAEA;AACA,YAAA,MAAA,CAAA,KAAA,CAAA,MAAA,EAAA,IAAA,IAAA,EAAA;AACA;AAJA,SADA,EAOA;AACA,UAAA,IAAA,EAAA,IADA;AAEA,UAAA,OAFA,mBAEA,MAFA,EAEA;AACA,gBAAA,IAAA,GAAA,IAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,OAAA,KAAA,OAAA,IAAA,GAAA,EAAA;AACA,YAAA,MAAA,CAAA,KAAA,CAAA,MAAA,EAAA,IAAA;AACA;AANA,SAPA,EAeA;AACA,UAAA,IAAA,EAAA,IADA;AAEA,UAAA,OAFA,mBAEA,MAFA,EAEA;AACA,gBAAA,IAAA,GAAA,IAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,OAAA,KAAA,OAAA,IAAA,GAAA,EAAA;AACA,YAAA,MAAA,CAAA,KAAA,CAAA,MAAA,EAAA,IAAA;AACA;AANA,SAfA;AADA,OAxEA;AAkGA;AACA,MAAA,WAAA,EAAA,EAnGA;AAoGA;AACA,MAAA,cAAA,EAAA,IArGA;AAsGA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,OAAA,EAAA,EAHA;AAIA,QAAA,QAAA,EAAA,EAJA;AAKA,QAAA,MAAA,EAAA;AALA,OAvGA;AA8GA,MAAA,UAAA,EAAA,IA9GA;AA+GA,MAAA,KAAA,EAAA;AACA;AACA;AACA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAHA;AAIA;AACA,QAAA,KAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CALA;AAQA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AARA,OA/GA;AAyHA;AACA;AACA,MAAA,WAAA,EAAA,IA3HA;AA4HA,MAAA,cAAA,EAAA,EA5HA;AA6HA,MAAA,QAAA,EAAA,EA7HA;AA8HA;AACA,MAAA,UAAA,EAAA,KA/HA;AAgIA;AACA,MAAA,YAAA,EAAA,KAjIA;AAkIA,MAAA,cAAA,EAAA;AACA,QAAA,IAAA,EAAA,SADA;AAEA,QAAA,MAAA,EAAA,SAFA;AAGA,QAAA,KAAA,EAAA,SAHA;AAIA,QAAA,IAAA,EAAA,SAJA;AAKA,QAAA,IAAA,EAAA,SALA;AAMA,QAAA,aAAA,EAAA,SANA;AAOA,QAAA,IAAA,EAAA,SAPA;AAQA,QAAA,OAAA,EAAA,CARA;AASA,QAAA,QAAA,EAAA;AATA,OAlIA;AA6IA,MAAA,MAAA,EAAA;AACA;AACA;AACA,QAAA,OAAA,EAAA,CAHA;AAIA,QAAA,QAAA,EAAA;AAJA,OA7IA;AAmJA,MAAA,WAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,OAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,EAFA;AAGA,UAAA,KAAA,EAAA,EAHA;AAIA,UAAA,OAAA,EAAA,EAJA;AAKA,UAAA,OAAA,EAAA,EALA;AAMA,UAAA,IAAA,EAAA;AANA,SADA;AASA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,KAAA,EAAA,SAHA;AAIA,UAAA,QAAA,EAAA,WAJA;AAKA,UAAA,MAAA,EAAA;AALA,SAFA,EASA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,UAFA;AAGA,UAAA,aAAA,EAAA,EAHA;AAIA,UAAA,KAAA,EAAA,QAJA;AAKA,UAAA,OAAA,EAAA;AALA,SATA,EAgBA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,UAFA;AAGA,UAAA,aAAA,EAAA,EAHA;AAIA,UAAA,KAAA,EAAA,OAJA;AAKA,UAAA,OAAA,EAAA,CACA;AACA,YAAA,KAAA,EAAA,GADA;AAEA,YAAA,KAAA,EAAA;AAFA,WADA,EAKA;AACA,YAAA,KAAA,EAAA,GADA;AAEA,YAAA,KAAA,EAAA;AAFA,WALA;AALA,SAhBA,EAgCA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,UAFA;AAGA,UAAA,aAAA,EAAA,EAHA;AAIA,UAAA,KAAA,EAAA,MAJA;AAKA,UAAA,OAAA,EAAA;AALA,SAhCA,EAuCA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,UAFA;AAGA,UAAA,aAAA,EAAA,EAHA;AAIA,UAAA,KAAA,EAAA,MAJA;AAKA,UAAA,OAAA,EAAA;AALA,SAvCA;AATA,OAnJA;AA4MA;AACA,MAAA,iBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA,CAJA;AAKA,UAAA,UAAA,EAAA;AALA,SADA;AAQA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SARA;AAYA,QAAA,SAAA,EAAA,EAZA;AAaA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA;AACA;;;;;;;;;;;AARA;AAoBA;;;;;;;;;;;;;;;;;;;;;;AAjCA,OA7MA;AAqQA,MAAA,WAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,OAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,EAFA;AAGA,UAAA,KAAA,EAAA,EAHA;AAIA,UAAA,OAAA,EAAA,EAJA;AAKA,UAAA,OAAA,EAAA,EALA;AAMA,UAAA,IAAA,EAAA;AANA,SADA;AASA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA,EAGA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SAHA,EASA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,KAAA,EAAA,SAHA;AAIA,UAAA,QAAA,EAAA,WAJA;AAKA,UAAA,MAAA,EAAA;AALA,SATA;AATA,OArQA;AAgSA;AACA,MAAA,iBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA,CAJA;AAKA,UAAA,UAAA,EAAA;AALA,SADA;AAQA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SARA;AAYA,QAAA,SAAA,EAAA,EAZA;AAaA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA;AACA;;;;;;;;;;;AALA;AAbA,OAjSA;AAgUA,MAAA,WAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,OAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,EAFA;AAGA,UAAA,KAAA,EAAA,EAHA;AAIA,UAAA,OAAA,EAAA,EAJA;AAKA,UAAA,OAAA,EAAA,EALA;AAMA,UAAA,IAAA,EAAA;AANA,SADA;AASA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA,EAGA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,UAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SAHA,EASA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SATA,EAeA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,KAAA,EAAA,SAHA;AAIA,UAAA,QAAA,EAAA,WAJA;AAKA,UAAA,MAAA,EAAA;AALA,SAfA,EAsBA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAtBA,EAuBA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAvBA,EAwBA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAxBA,EAyBA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAzBA,EA0BA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SA1BA;AATA,OAhUA;AAsWA;AACA,MAAA,iBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA,CAJA;AAKA,UAAA,UAAA,EAAA;AALA,SADA;AAQA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SARA;AAYA,QAAA,SAAA,EAAA,EAZA;AAaA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SARA,EASA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SATA,EAUA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAVA,EAWA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAXA,EAYA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAZA,EAaA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA;AACA;;;;;;;;;;;;;AAdA;AAbA,OAvWA;AAiZA;AACA,MAAA,QAAA,EAAA;AACA,QAAA,UAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA,SAFA;AAGA,QAAA,MAAA,EAAA,SAHA;AAIA,QAAA,MAAA,EAAA,SAJA;AAKA,QAAA,IAAA,EAAA,SALA;AAMA,QAAA,KAAA,EAAA,SANA;AAOA,QAAA,IAAA,EAAA,SAPA;AAQA,QAAA,IAAA,EAAA,SARA;AASA,QAAA,IAAA,EAAA,SATA;AAUA,QAAA,EAAA,EAAA,SAVA;AAWA,QAAA,EAAA,EAAA,SAXA;AAYA,QAAA,IAAA,EAAA,SAZA;AAaA,QAAA,IAAA,EAAA,SAbA;AAcA,QAAA,EAAA,EAAA,SAdA;AAeA,QAAA,GAAA,EAAA,SAfA;AAgBA,QAAA,IAAA,EAAA,SAhBA;AAiBA,QAAA,IAAA,EAAA,SAjBA;AAkBA,QAAA,IAAA,EAAA,SAlBA;AAmBA,QAAA,IAAA,EAAA,SAnBA;AAoBA,QAAA,IAAA,EAAA,SApBA;AAqBA,QAAA,IAAA,EAAA,SArBA;AAsBA,QAAA,IAAA,EAAA,SAtBA;AAuBA,QAAA,MAAA,EAAA,SAvBA;AAwBA,QAAA,QAAA,EAAA,SAxBA;AAyBA,QAAA,IAAA,EAAA,SAzBA;AA0BA,QAAA,IAAA,EAAA,SA1BA;AA2BA,QAAA,IAAA,EAAA,SA3BA;AA4BA,QAAA,KAAA,EAAA,SA5BA;AA6BA,QAAA,OAAA,EAAA,SA7BA;AA6BA;AACA,QAAA,IAAA,EAAA,SA9BA;AA8BA;AACA,QAAA,IAAA,EAAA,SA/BA;AA+BA;AACA,QAAA,OAAA,EAAA,SAhCA;AAgCA;AACA,QAAA,QAAA,EAAA,SAjCA;AAiCA;AACA,QAAA,UAAA,EAAA,SAlCA;AAkCA;AACA,QAAA,IAAA,EAAA,SAnCA;AAmCA;AACA,QAAA,IAAA,EAAA,SApCA;AAoCA;AACA,QAAA,IAAA,EAAA,SArCA;AAqCA;AACA,QAAA,MAAA,EAAA,SAtCA;AAsCA;AACA,QAAA,EAAA,EAAA,SAvCA;AAuCA;AACA,QAAA,IAAA,EAAA,SAxCA;AAwCA;AACA,QAAA,IAAA,EAAA,SAzCA;AAyCA;AACA,QAAA,IAAA,EAAA,SA1CA;AA0CA;AACA,QAAA,IAAA,EAAA,SA3CA;AA2CA;AACA,QAAA,IAAA,EAAA,SA5CA;AA4CA;AACA,QAAA,KAAA,EAAA,SA7CA;AA6CA;AACA,QAAA,IAAA,EAAA,SA9CA;AA8CA;AACA,QAAA,KAAA,EAAA,SA/CA;AA+CA;AACA,QAAA,IAAA,EAAA,SAhDA;AAgDA;AACA,QAAA,IAAA,EAAA,SAjDA;AAiDA;AACA,QAAA,IAAA,EAAA,SAlDA;AAkDA;AACA,QAAA,OAAA,EAAA,SAnDA;AAmDA;AACA,QAAA,KAAA,EAAA,SApDA;AAoDA;AACA,QAAA,QAAA,EAAA,SArDA;AAqDA;AACA,QAAA,OAAA,EAAA,SAtDA;AAsDA;AACA,QAAA,MAAA,EAAA,SAvDA;AAuDA;AACA,QAAA,MAAA,EAAA,SAxDA;AAwDA;AACA,QAAA,EAAA,EAAA,SAzDA,CAyDA;;AAzDA,OAlZA;AA6cA;AACA,MAAA,oBAAA,EAAA,KA9cA;AA+cA;AACA,MAAA,KAAA,EAAA,EAhdA;AAidA,MAAA,OAAA,EAAA,EAjdA;AAkdA,MAAA,QAAA,EAAA,EAldA;AAmdA;AACA,MAAA,UAAA,EAAA,KApdA;AAqdA;AACA,MAAA,MAAA,EAAA,EAtdA;AAudA;AACA,MAAA,SAAA,EAAA,EAxdA;AAydA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,UAAA,EAAA;AAFA,OAzdA;AA6dA,MAAA,eAAA,EAAA,EA7dA;AA8dA;AACA,MAAA,cAAA,EAAA,KA/dA;AAgeA,MAAA,UAAA,EAAA;AACA,QAAA,MAAA,EAAA;AADA,OAheA;AAmeA,MAAA,WAAA,EAAA;AACA,QAAA,UAAA,EAAA,SADA;AAEA,QAAA,IAAA,EAAA;AAFA,OAneA;AAueA,MAAA,cAAA,EAAA,KAveA;AAweA,MAAA,kBAAA,EAAA,KAxeA;AAyeA;AACA,MAAA,QAAA,EAAA,EA1eA;AA2eA;AACA,MAAA,cAAA,EAAA,KA5eA;AA6eA;AACA,MAAA,aAAA,EAAA,QA9eA;AA+eA;AACA,MAAA,oBAAA,EAAA,EAhfA;AAifA;AACA,MAAA,iBAAA,EAAA,EAlfA;AAmfA;AACA,MAAA,uBAAA,EAAA,EApfA;AAqfA;AACA,MAAA,QAAA,EAAA,EAtfA;AAufA;AACA,MAAA,aAAA,EAAA,EAxfA;AAyfA;AACA,MAAA,eAAA,EAAA,QA1fA;AA2fA;AACA,MAAA,YAAA,EAAA,EA5fA;AA6fA;AACA,MAAA,YAAA,EAAA,EA9fA;AA+fA,MAAA,aAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,KADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,YAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA;AAZA,OA/fA;AAohBA;AACA,MAAA,QAAA,EAAA,EArhBA;AAshBA;AACA,MAAA,SAAA,EAAA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,KAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAVA;AAaA,QAAA,aAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAbA;AAgBA;AACA;AACA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAnBA;AAsBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAlCA;AAqCA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CArCA;AAwCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAjDA,OAvhBA;AA4kBA,MAAA,SAAA,EAAA;AACA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,GAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAPA,OA5kBA;AAulBA,MAAA,QAAA,EAAA,EAvlBA;AAulBA;AACA,MAAA,MAAA,EAAA,EAxlBA;AAwlBA;AACA,MAAA,KAAA,EAAA,EAzlBA;AAylBA;AACA,MAAA,OAAA,EAAA,EA1lBA;AA2lBA,MAAA,IAAA,EAAA,EA3lBA;AA2lBA;AACA,MAAA,UAAA,EAAA;AACA;AACA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,OAAA,EAAA;AAHA,OA5lBA;AAimBA,MAAA,MAAA,EAAA,EAjmBA;AAkmBA,MAAA,OAAA,EAAA,IAAA,GAAA,EAlmBA;AAkmBA;AACA,MAAA,UAAA,EAAA,EAnmBA;AAmmBA;AACA,MAAA,QAAA,EAAA,KApmBA;AAomBA;AACA,MAAA,MAAA,EAAA,EArmBA;AAqmBA;AACA,MAAA,IAAA,EAAA,EAtmBA;AAsmBA;AACA;AACA,MAAA,YAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,YAAA,EAAA;AAAA,SAPA;AAQA,QAAA,SAAA,EAAA,EARA;AASA,QAAA,WAAA,EAAA,CACA;AACA;AACA;AACA;AACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SARA,EASA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SATA,EAUA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAVA;AATA,OAxmBA;AA+nBA;AACA,MAAA,cAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,YAAA,EAAA;AAAA,SAPA;AAQA,QAAA,SAAA,EAAA,EARA;AASA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA;AATA,OAhoBA;AAipBA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAHA,EAIA;AACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SARA,EASA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SATA,EAUA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SAVA,CARA;AAoBA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AApBA,OAlpBA;AAwqBA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,UAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AALA,SADA;AAQA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,KADA;AAEA,UAAA,YAAA,EAAA;AAFA,SARA;AAYA,QAAA,SAAA,EAAA,EAZA;AAaA,QAAA,WAAA,EAAA,CACA;AACA;AACA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AACA;AACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SARA,EASA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SATA,EAUA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,UAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAVA,EAWA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAXA,EAYA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,MAAA,EAAA;AAAA,SAZA;AAbA,OAzqBA;AAqsBA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,UAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AALA,SADA;AAQA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,KADA;AAEA,UAAA,YAAA,EAAA;AAFA,SARA;AAYA,QAAA,SAAA,EAAA,EAZA;AAaA,QAAA,WAAA,EAAA,CACA;AACA;AACA;AACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SARA,EASA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SATA,EAUA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAVA,EAWA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAXA;AAbA,OAtsBA;AAiuBA;AACA,MAAA,YAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,UAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AALA,SADA;AAQA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,KADA;AAEA,UAAA,YAAA,EAAA;AAFA,SARA;AAYA,QAAA,SAAA,EAAA,EAZA;AAaA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,SAAA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA;AAbA,OAluBA;AAwvBA;AACA,MAAA,YAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,UAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AALA,SADA;AAQA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,KADA;AAEA,UAAA,YAAA,EAAA;AAFA,SARA;AAYA,QAAA,SAAA,EAAA,EAZA;AAaA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AACA,UAAA,IAAA,EAAA,QADA;AAEA,UAAA,KAAA,EAAA,QAFA;AAGA,UAAA,QAAA,EAAA,KAHA;AAIA,UAAA,UAAA,EAAA;AAJA,SAFA,EAQA;AAAA,UAAA,IAAA,EAAA,SAAA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SARA,EASA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SATA,EAUA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAVA,EAWA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAXA,EAYA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAZA,EAaA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAbA;AAbA,OAzvBA;AAsxBA,MAAA,SAAA,EAAA,KAtxBA;AAuxBA,MAAA,SAAA,EAAA,KAvxBA;AAwxBA,MAAA,WAAA,EAAA,KAxxBA;AAyxBA,MAAA,aAAA,EAAA,KAzxBA;AA0xBA,MAAA,SAAA,EAAA,KA1xBA;AA2xBA,MAAA,WAAA,EAAA,KA3xBA;AA4xBA,MAAA,WAAA,EAAA,KA5xBA;AA6xBA,MAAA,uBAAA,EAAA,KA7xBA,CA6xBA;;AA7xBA,KAAA;AA+xBA,GAnyBA;AAoyBA,EAAA,KAAA,EAAA;AACA;AACA,IAAA,UAFA,sBAEA,GAFA,EAEA;AACA,WAAA,KAAA,CAAA,IAAA,CAAA,MAAA,CAAA,GAAA;AACA;AAJA,GApyBA;AA0yBA,EAAA,OA1yBA,qBA0yBA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA,cAAA,KAAA,CAAA,aAAA;;AACA,cAAA,KAAA,CAAA,aAAA,GAHA,CAIA;;;AACA,cAAA,KAAA,CAAA,sBAAA,GALA,CAMA;;;AACA,cAAA,KAAA,CAAA,iBAAA,GAPA,CAQA;;;AARA;AAAA,qBASA,KAAA,CAAA,cAAA,EATA;;AAAA;AAUA,cAAA,KAAA,CAAA,OAAA,GAAA,IAAA,CAVA,CAWA;;AAXA;AAAA,qBAYA,KAAA,CAAA,OAAA,EAZA;;AAAA;AAAA;AAAA,qBAaA,KAAA,CAAA,WAAA,EAbA;;AAAA;AAcA;AACA,cAAA,KAAA,CAAA,gBAAA,mCAAA,KAAA,CAAA,iBAAA;;AAfA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA,GA1zBA;AA2zBA,EAAA,OA3zBA,qBA2zBA;AACA;AACA,SAAA,MAAA,CAAA,KAAA,GAAA,qBAAA;AACA,SAAA,QAAA,GAAA,KAAA,MAAA,CAAA,OAAA,CAAA,IAAA;AACA,GA/zBA;AAg0BA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,eAFA,2BAEA,QAFA,EAEA;AAAA;;AACA;AACA,WAAA,iBAAA,GAAA,EAAA;AACA,WAAA,IAAA,CAAA,KAAA,QAAA,EAAA,KAAA,EAAA,EAAA,EAHA,CAIA;;AACA,0CAAA;AAAA,QAAA,MAAA,EAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,iBAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KAVA;AAWA,IAAA,kBAXA,8BAWA,GAXA,EAWA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,QAAA,EAAA,MAAA,EAAA,EAAA;;AADA;AAAA,uBAEA,mCAAA;AAAA,kBAAA,KAAA,EAAA,GAAA,GAAA;AAAA,iBAAA,CAFA;;AAAA;AAEA,gBAAA,GAFA;;AAGA,oBAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,MAEA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA;AACA,oBAAA,IAAA,EAAA,OADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;AAIA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KAtBA;AAuBA,IAAA,YAvBA,wBAuBA,GAvBA,EAuBA;AACA,UAAA,GAAA,KAAA,IAAA,EAAA;AACA,aAAA,IAAA,CAAA,KAAA,QAAA,EAAA,IAAA,EAAA,KAAA;AACA,OAFA,MAEA;AACA,aAAA,IAAA,CAAA,KAAA,QAAA,EAAA,IAAA,EAAA,EAAA;AACA;AACA,KA7BA;;AA8BA;;;AAGA,IAAA,aAjCA,2BAiCA;AAAA;;AACA,2CAAA,IAAA,CAAA,SAAA,CAAA,IAAA,CAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAAA,QAAA,EAAA;AACA,SAFA;AAGA,QAAA,MAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA;AACA,OALA;AAMA,KAxCA;AAyCA;AACA,IAAA,SA1CA,qBA0CA,MA1CA,EA0CA;AAAA;;AACA,WAAA,SAAA,GAAA,IAAA;AACA,UAAA,KAAA,uHACA;AAAA,QAAA,QAAA,EAAA,EAAA;AAAA,QAAA,OAAA,EAAA;AAAA,OADA,GAEA,MAFA,GAGA;AAAA,QAAA,IAAA,EAAA,KAAA,IAAA;AAAA,QAAA,GAAA,EAAA,KAAA;AAAA,OAHA,GAIA;AAAA,QAAA,OAAA,EAAA,CAAA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAAA;AAAA,OAJA,CAAA;AAMA,6BAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,UAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,MAAA,CAAA,UAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,MAAA,CAAA,SAAA,GAAA,KAAA;AACA,OAJA;AAKA,KAvDA;AAwDA,IAAA,WAxDA,uBAwDA,MAxDA,EAwDA;AAAA;;AACA,WAAA,WAAA,GAAA,IAAA;AACA,UAAA,KAAA,uHACA;AAAA,QAAA,QAAA,EAAA,EAAA;AAAA,QAAA,OAAA,EAAA;AAAA,OADA,GAEA,MAFA,GAGA;AAAA,QAAA,IAAA,EAAA,KAAA,IAAA;AAAA,QAAA,GAAA,EAAA,KAAA;AAAA,OAHA,GAIA;AAAA,QAAA,OAAA,EAAA,CAAA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAAA;AAAA,OAJA,CAAA;AAMA,+BAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,YAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,MAAA,CAAA,YAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,MAAA,CAAA,WAAA,GAAA,KAAA;AACA,OAJA;AAKA,KArEA;AAsEA,IAAA,WAtEA,uBAsEA,MAtEA,EAsEA;AAAA;;AACA,WAAA,WAAA,GAAA,IAAA;AACA,UAAA,KAAA,uHACA;AAAA,QAAA,QAAA,EAAA,EAAA;AAAA,QAAA,OAAA,EAAA;AAAA,OADA,GAEA,MAFA,GAGA;AAAA,QAAA,KAAA,EAAA,KAAA,IAAA;AAAA,QAAA,GAAA,EAAA,KAAA;AAAA,OAHA,GAIA;AAAA,QAAA,OAAA,EAAA,CAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAAA;AAAA,OAJA,CAAA;AAMA,8BAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,YAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,MAAA,CAAA,YAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,MAAA,CAAA,WAAA,GAAA,KAAA;AACA,OAJA;AAKA,KAnFA;AAoFA;AACA,IAAA,WArFA,uBAqFA,MArFA,EAqFA;AAAA;;AACA,WAAA,WAAA,GAAA,IAAA;AACA,UAAA,KAAA,2FACA;AAAA,QAAA,QAAA,EAAA,EAAA;AAAA,QAAA,OAAA,EAAA;AAAA,OADA,GAEA,MAFA,GAGA;AAAA,QAAA,IAAA,EAAA,KAAA;AAAA,OAHA,CAAA;AAKA,yBAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,YAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,MAAA,CAAA,YAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,MAAA,CAAA,WAAA,GAAA,KAAA;AACA,OAJA;AAKA,KAjGA;AAmGA;AACA,IAAA,aApGA,yBAoGA,MApGA,EAoGA;AAAA;;AACA,WAAA,aAAA,GAAA,IAAA;AACA,UAAA,KAAA,2FACA;AAAA,QAAA,QAAA,EAAA,EAAA;AAAA,QAAA,OAAA,EAAA;AAAA,OADA,GAEA,MAFA,GAGA;AAAA,QAAA,IAAA,EAAA,KAAA;AAAA,OAHA,CAAA;AAKA,+BAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,cAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,MAAA,CAAA,cAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,MAAA,CAAA,aAAA,GAAA,KAAA;AACA,OAJA;AAKA,KAhHA;AAkHA;AACA,IAAA,SAnHA,qBAmHA,MAnHA,EAmHA;AAAA;;AACA,WAAA,SAAA,GAAA,IAAA;AACA,UAAA,KAAA,2FACA;AAAA,QAAA,QAAA,EAAA,EAAA;AAAA,QAAA,OAAA,EAAA;AAAA,OADA,GAEA,MAFA,GAGA;AAAA,QAAA,MAAA,EAAA,KAAA;AAAA,OAHA,CAAA;AAKA,uCAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,OAAA,CAAA,UAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,UAAA,OAAA,CAAA,UAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA;;AACA,QAAA,OAAA,CAAA,SAAA,GAAA,KAAA;AACA,OANA;AAOA,KAjIA;AAkIA;AACA,IAAA,SAnIA,qBAmIA,MAnIA,EAmIA;AAAA;;AACA,WAAA,SAAA,GAAA,IAAA;AACA,UAAA,KAAA,2FACA;AAAA,QAAA,QAAA,EAAA,EAAA;AAAA,QAAA,OAAA,EAAA;AAAA,OADA,GAEA,MAFA,GAGA;AAAA,QAAA,IAAA,EAAA,KAAA;AAAA,OAHA,CAAA;AAKA,8BAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,OAAA,CAAA,UAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,UAAA,OAAA,CAAA,UAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA;;AACA,QAAA,OAAA,CAAA,SAAA,GAAA,KAAA;AACA,OANA;AAOA,KAjJA;AAkJA;AACA,IAAA,WAnJA,yBAmJA;AAAA;;AACA,iCAAA,WAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,mBAAA,GAAA,GAAA,CAAA,IAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,CAAA,OAAA,CAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,KAAA,CAAA,EAAA;AACA,YAAA,OAAA,CAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,KAAA,EAAA,IAAA,CAAA,MAAA;AACA;AACA,SAJA;AAKA,OAPA;AAQA,KA5JA;AA6JA,IAAA,iBA7JA,+BA6JA;AAAA;;AACA;AACA,iCAAA,SAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,wBAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA,EAFA,CAKA;;AACA,iCAAA,aAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,YAAA,GAAA,GAAA,CAAA,IAAA;;AACA,QAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,IAAA,MAAA,EAAA;AACA,mBAAA,IAAA,CAAA,OAAA,GAAA,OAAA,CAAA,YAAA;AACA;AACA,SAJA;AAKA,OAPA,EANA,CAcA;;AACA,iCAAA,WAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,qBAAA,GAAA,GAAA,CAAA,IAAA;;AACA,QAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,IAAA,MAAA,EAAA;AACA,mBAAA,IAAA,CAAA,OAAA,GAAA,OAAA,CAAA,qBAAA;AACA;AACA,SAJA;;AAMA,QAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,IAAA,UAAA,EAAA;AACA,mBAAA,IAAA,CAAA,OAAA,GAAA,OAAA,CAAA,qBAAA;AACA;AACA,SAJA;AAKA,OAbA,EAfA,CA6BA;;AACA,iCAAA,WAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,mBAAA,GAAA,GAAA,CAAA,IAAA;;AACA,QAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,IAAA,MAAA,EAAA;AACA,mBAAA,IAAA,CAAA,OAAA,GAAA,OAAA,CAAA,mBAAA;AACA;AACA,SAJA;;AAKA,QAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,IAAA,MAAA,EAAA;AACA,mBAAA,IAAA,CAAA,OAAA,GAAA,OAAA,CAAA,mBAAA;AACA;AACA,SAJA;AAKA,OAZA,EA9BA,CA2CA;;AACA,iCAAA,WAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,YAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA,EA5CA,CA+CA;;AACA,iCAAA,SAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA,EAhDA,CAmDA;;AACA,iCAAA,SAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KApNA;AAqNA;AACA,IAAA,OAtNA,qBAsNA;AACA,WAAA,IAAA,CAAA,KAAA,QAAA,EAAA,OAAA,EAAA,KAAA,UAAA;AACA,KAxNA;AAyNA,IAAA,cAzNA,0BAyNA,EAzNA,EAyNA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,sBAAA,EAAA,CADA;;AAAA;AAAA;AACA,gBAAA,IADA,qBACA,IADA;;AAAA,sBAEA,IAAA,KAAA,MAFA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAGA,OAAA,CAAA,WAAA,EAHA;;AAAA;AAIA,gBAAA,OAAA,CAAA,QAAA,CAAA;AACA,kBAAA,IAAA,EAAA,SADA;AAEA,kBAAA,OAAA,EAAA;AAFA,iBAAA;;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,KAlOA;AAmOA;AACA,IAAA,UApOA,sBAoOA,KApOA,EAoOA,IApOA,EAoOA;AACA,UAAA,CAAA,KAAA,EAAA,OAAA,IAAA;AACA,aAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,KAAA,MAAA,CAAA,CAAA;AACA,KAvOA;AAwOA;AACA,IAAA,cAzOA,4BAyOA;AAAA;;AACA,+BAAA;AAAA,QAAA,aAAA,EAAA,EAAA;AAAA,QAAA,MAAA,EAAA,CAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,MAAA,GAAA,OAAA,CAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,QAAA,EAAA;;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,QAAA,KAAA,OAAA,CAAA,QAAA,EAAA;AACA;AACA,YAAA,MAAA,GAAA,EAAA;AACA,mBAAA,KAAA;AACA;AACA,SANA;;AAOA,YAAA,OAAA,CAAA,MAAA,CAAA,QAAA,CAAA,MAAA,CAAA,EAAA;AACA,UAAA,OAAA,CAAA,QAAA,CAAA,MAAA,GAAA,MAAA;AACA;;AACA,kCAAA,OAAA,CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA,SAFA;AAGA,OAfA;AAgBA,KA1PA;;AA2PA;;;AAGA,IAAA,aA9PA,2BA8PA;AAAA;;AACA,+BAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAAA,QAAA,EAAA;;AACA,UAAA,OAAA,CAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,KAAA,CAAA,QAAA,EAAA;AACA,SAHA;AAIA,QAAA,OAAA,CAAA,wBAAA,GAAA,GAAA,CAAA,IAAA;;AACA,QAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,IAAA,QAAA,EAAA;AACA,mBAAA,IAAA,CAAA,OAAA,GAAA,OAAA,CAAA,wBAAA;AACA;AACA,SAJA;AAKA,OAXA;AAYA,KA3QA;AA4QA;AACA,IAAA,eA7QA,6BA6QA;AAAA;;AACA,+BAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KAjRA;AAkRA;AACA,IAAA,OAnRA,mBAmRA,KAnRA,EAmRA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,OAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,MAAA,GAAA,KAAA;AAFA;AAAA,uBAGA,qCAAA;AAAA,kBAAA,IAAA,EAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,OAAA,CAAA,uBAAA,GAAA,GAAA,CAAA,IAAA;AACA,kBAAA,OAAA,CAAA,OAAA,GAAA,KAAA;AACA,iBAHA,CAHA;;AAAA;AAAA,qBAQA,OAAA,CAAA,WARA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAUA,OAAA,CAAA,UAAA,CAAA,KAAA,CAVA;;AAAA;AAAA,qBAYA,OAAA,CAAA,UAZA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAaA,OAAA,CAAA,SAAA,CAAA,KAAA,CAbA;;AAAA;AAAA,qBAeA,OAAA,CAAA,YAfA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAgBA,OAAA,CAAA,WAAA,CAAA,KAAA,CAhBA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA,KArSA;;AAsSA;;;AAGA,IAAA,qBAzSA,iCAySA,SAzSA,EAySA;AACA;AACA;AACA,WAAA,eAAA,GACA,SAAA,CAAA,MAAA,GAAA,CAAA,mCAAA,SAAA,CAAA,CAAA,CAAA,IAAA,SADA,CAHA,CAKA;AACA,KA/SA;AAiTA;AACA,IAAA,aAlTA,2BAkTA;AACA,WAAA,MAAA,GAAA,KAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,KArTA;AAsTA,IAAA,KAtTA,mBAsTA;AAAA;;AACA,WAAA,KAAA,CAAA,UAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,cAAA,IAAA,GAAA,OAAA,CAAA,QAAA,CAAA,IAAA;;AACA,cAAA,IAAA,EAAA;AACA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA,IAAA,GAAA,OAAA,CAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA;AACA;;AACA,2BAAA,OAAA,CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,cAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,OAAA;;AACA,cAAA,OAAA,CAAA,mBAAA,GAAA,KAAA,CAFA,CAGA;;AACA,cAAA,OAAA,CAAA,SAAA,GAJA,CAKA;;;AACA,cAAA,OAAA,CAAA,cAAA;AACA;AACA,WATA;AAUA,SAhBA,MAgBA;AACA,iBAAA,KAAA;AACA;AACA,OApBA;AAqBA,KA5UA;AA6UA,IAAA,aA7UA,2BA6UA;AAAA;;AACA,+BAAA,KAAA,aAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,iBAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,OAAA,CAAA,iBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA,CAFA,CAGA;AACA,OAJA;AAKA,KAnVA;;AAoVA;;;AAGA,IAAA,SAvVA,qBAuVA,GAvVA,EAuVA;AACA,UAAA,KAAA,WAAA,EAAA;AACA,aAAA,SAAA,CAAA,GAAA;AACA;;AACA,UAAA,KAAA,UAAA,EAAA;AACA,aAAA,WAAA,CAAA,GAAA;AACA;;AACA,UAAA,KAAA,YAAA,EAAA;AACA,aAAA,QAAA,CAAA,GAAA;AACA;AACA,KAjWA;AAkWA,IAAA,WAlWA,uBAkWA,GAlWA,EAkWA;AAAA;;AACA,WAAA,IAAA,GAAA,GAAA;AAEA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAKA,IALA,CAKA,YAAA;AACA,+BAAA,CAAA,OAAA,CAAA,IAAA,CAAA,KAAA,CAAA,EAAA,IAAA,CAAA,gBAAA;AAAA,cAAA,IAAA,QAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,OAAA,CAAA,SAAA,GALA,CAMA;;;AACA,YAAA,OAAA,CAAA,cAAA,GAPA,CAQA;;AACA,WATA,MASA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAIA;AACA,SAhBA;AAiBA,OAvBA,EAwBA,KAxBA,CAwBA,YAAA;AACA,QAAA,OAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OA7BA;AA8BA,KAnYA;AAoYA,IAAA,SApYA,qBAoYA,GApYA,EAoYA;AAAA;;AACA,WAAA,IAAA,GAAA,GAAA;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,6BAAA,CAAA,OAAA,CAAA,IAAA,CAAA,KAAA,CAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,QAAA,CAAA;AACA,YAAA,IAAA,EAAA,SADA;AAEA,YAAA,OAAA,EAAA;AAFA,WAAA,EADA,CAKA;;;AACA,UAAA,OAAA,CAAA,UAAA;AACA,SAPA;AAQA,OAbA;AAcA,KApZA;AAqZA,IAAA,QArZA,oBAqZA,GArZA,EAqZA;AAAA;;AACA,WAAA,IAAA,GAAA,GAAA;AAEA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,4BAAA,CAAA,OAAA,CAAA,IAAA,CAAA,KAAA,CAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,QAAA,CAAA;AACA,YAAA,IAAA,EAAA,SADA;AAEA,YAAA,OAAA,EAAA;AAFA,WAAA,EADA,CAKA;;;AACA,UAAA,OAAA,CAAA,WAAA;AACA,SAPA;AAQA,OAbA;AAcA,KAtaA;AAuaA,IAAA,QAvaA,oBAuaA,GAvaA,EAuaA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,mBAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,QAAA,mCAAA,GAAA;AACA,gBAAA,OAAA,CAAA,MAAA,GAAA,KAAA;;AAHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,KA3aA;AA6aA,IAAA,SA7aA,qBA6aA,GA7aA,EA6aA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,mBAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,QAAA,mCAAA,GAAA;AACA,gBAAA,OAAA,CAAA,MAAA,GAAA,IAAA;;AAHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,KAjbA;AAmbA;AACA,IAAA,gBApbA,8BAobA,CAAA,CApbA;AAqbA;AACA,IAAA,mBAtbA,iCAsbA,CAAA,CAtbA;AAubA;AACA,IAAA,eAxbA,2BAwbA,IAxbA,EAwbA,CAxbA,EAwbA;AACA;AACA,UAAA,IAAA,CAAA,UAAA,IAAA,GAAA,EAAA;AACA;AACA;AACA,aAAA,OAAA,GAAA,KAAA,CAHA,CAIA;;AACA,aAAA,cAAA,GAAA;AACA,UAAA,OAAA,EAAA,CADA;AAEA,UAAA,QAAA,EAAA;AAFA,SAAA;AAIA,aAAA,KAAA,CAAA,QAAA,CAAA,WAAA,GAAA,CAAA,CATA,CAUA;;AACA,aAAA,UAAA;AACA,aAAA,WAAA,GAAA,IAAA;AACA,aAAA,YAAA,GAAA,KAAA,UAAA,GAAA,KAAA;AACA,OAdA,CAeA;AAfA,WAgBA,IAAA,IAAA,CAAA,UAAA,IAAA,GAAA,EAAA;AACA;AACA,eAAA,UAAA,GAAA,IAAA,CAAA,EAAA;AACA,eAAA,OAAA,GAAA,IAAA,CAAA,KAAA;AACA,eAAA,KAAA,GAAA,IAAA,CAAA,EAAA,CAJA,CAKA;;AACA,eAAA,KAAA,CAAA,OAAA,CAAA,WAAA,GAAA,CAAA;AACA,eAAA,aAAA,CAAA,OAAA,GAAA,CAAA;AACA,eAAA,aAAA,CAAA,IAAA,GAAA,EAAA,CARA,CAQA;;AACA,eAAA,aAAA;AACA,eAAA,SAAA,CAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA;AAAA,WAAA;AACA,eAAA,UAAA,GAAA,IAAA;AACA,eAAA,YAAA,GAAA,KAAA,WAAA,GAAA,KAAA;AACA,SAbA,CAaA;AAbA,aAcA,IAAA,IAAA,CAAA,UAAA,IAAA,GAAA,EAAA;AACA;AACA;AACA,iBAAA,UAAA,GAAA,IAAA,CAAA,KAAA;AACA,iBAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAJA,CAKA;;AACA,iBAAA,KAAA,CAAA,OAAA,CAAA,WAAA,GAAA,CAAA;AACA,iBAAA,aAAA,CAAA,OAAA,GAAA,CAAA;AACA,iBAAA,aAAA,CAAA,IAAA,GAAA,IAAA,CAAA,EAAA;AACA,iBAAA,MAAA,GAAA,IAAA,CAAA,EAAA;AACA,iBAAA,aAAA;AACA,iBAAA,SAAA,CAAA;AAAA,cAAA,KAAA,EAAA,KAAA,KAAA;AAAA,cAAA,IAAA,EAAA,IAAA,CAAA;AAAA,aAAA;AACA,iBAAA,UAAA,GAAA,IAAA;AACA,iBAAA,YAAA,GAAA,KAAA,WAAA,GAAA,KAAA;AACA,WAdA,MAcA,IAAA,IAAA,CAAA,UAAA,IAAA,GAAA,EAAA;AACA;AACA,iBAAA,MAAA,GAAA,IAAA,CAAA,IAAA;AACA,iBAAA,QAAA,CAAA,KAAA,GAAA,KAAA,KAAA,CAHA,CAIA;;AACA,iBAAA,KAAA,CAAA,SAAA,CAAA,WAAA,GAAA,CAAA;AACA,iBAAA,UAAA,CAAA,OAAA,GAAA,CAAA;AACA,iBAAA,qBAAA;AACA,iBAAA,IAAA,GAAA,IAAA,CAAA,EAAA;AACA,iBAAA,QAAA,CAAA,IAAA,GAAA,KAAA,IAAA,CATA,CAUA;;AACA,iBAAA,cAAA,CAAA,KAAA,GAAA,EAAA;AACA,iBAAA,cAAA,CAAA,IAAA,GAAA,IAAA,CAAA,EAAA;AACA,iBAAA,WAAA;AACA,iBAAA,YAAA,GAAA,IAAA;AACA,iBAAA,UAAA,GAAA,KAAA,WAAA,GAAA,KAAA;AACA;AACA,KAvfA;AAwfA,IAAA,aAxfA,2BAwfA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,yBAAA;AAAA,kBAAA,IAAA,EAAA,OAAA,CAAA;AAAA,iBAAA,CADA;;AAAA;AAAA;AACA,gBAAA,IADA,yBACA,IADA;AACA,gBAAA,IADA,yBACA,IADA;;AAEA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,IAAA,GAAA,IAAA,CAAA,KAAA;AACA,kBAAA,OAAA,CAAA,QAAA,CAAA,IAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,KA9fA;AA+fA;AACA,IAAA,UAhgBA,sBAggBA,KAhgBA,EAggBA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,cAAA,+DAAA,OAAA,CAAA,cAAA,GAAA,KAAA;AACA,gBAAA,GAFA,+DAEA,OAAA,CAAA,cAFA,GAEA,KAFA;;AAGA,oBAAA,OAAA,CAAA,QAAA,CAAA,MAAA,EAAA;AACA,kBAAA,GAAA,CAAA,MAAA,GAAA,OAAA,CAAA,QAAA,CAAA,MAAA;AACA;;AALA;AAAA,uBAMA,uBAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,OAAA,CAAA,WAAA,GAAA,IAAA;AACA,kBAAA,OAAA,CAAA,UAAA,GAAA,OAAA,CAAA,YAAA,GAAA,KAAA;AACA,kBAAA,OAAA,CAAA,iBAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,kBAAA,OAAA,CAAA,iBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,iBALA,CANA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,KA5gBA;AA6gBA;AACA,IAAA,aA9gBA,yBA8gBA,CA9gBA,EA8gBA;AACA,UAAA,IAAA,GAAA,CAAA,CAAA,WAAA,EAAA;AACA,UAAA,KAAA,GACA,CAAA,CAAA,QAAA,KAAA,CAAA,GAAA,OAAA,CAAA,CAAA,QAAA,KAAA,CAAA,CAAA,GAAA,MAAA,CAAA,CAAA,QAAA,KAAA,CAAA,CADA;AAEA,UAAA,GAAA,GAAA,CAAA,CAAA,OAAA,KAAA,EAAA,GAAA,MAAA,CAAA,CAAA,OAAA,EAAA,GAAA,KAAA,CAAA,CAAA,OAAA,EAAA;AACA,aAAA,IAAA,GAAA,GAAA,GAAA,KAAA,GAAA,GAAA,GAAA,GAAA;AACA,KAphBA;AAqhBA;AACA,IAAA,SAthBA,qBAshBA,KAthBA,EAshBA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,aAAA,+DAAA,OAAA,CAAA,aAAA,GAAA,KAAA;AADA;AAAA;AAAA,uBAGA,yBAAA,OAAA,CAAA,aAAA,CAHA;;AAAA;AAAA;AAGA,gBAAA,IAHA,wBAGA,IAHA;AAGA,gBAAA,IAHA,wBAGA,IAHA;;AAIA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,UAAA,GAAA,IAAA;AACA,kBAAA,OAAA,CAAA,WAAA,GAAA,OAAA,CAAA,YAAA,GAAA,KAAA;AACA,kBAAA,OAAA,CAAA,iBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,OAAA,CAAA,iBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AATA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KAjiBA;AAkiBA;AACA,IAAA,WAniBA,uBAmiBA,MAniBA,EAmiBA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,OAAA,CAAA,UAAA,+DAAA,OAAA,CAAA,UAAA,GAAA,MAAA;AACA,gBAAA,KAHA,GAGA,OAAA,CAAA,UAHA;AAIA,gBAAA,KAAA,CAAA,IAAA,GAAA,OAAA,CAAA,IAAA;AAJA;AAAA,uBAKA,iCAAA,KAAA,CALA;;AAAA;AAAA;AAKA,gBAAA,IALA,yBAKA,IALA;AAKA,gBAAA,IALA,yBAKA,IALA;;AAMA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,YAAA,GAAA,IAAA;AACA,kBAAA,OAAA,CAAA,UAAA,GAAA,OAAA,CAAA,WAAA,GAAA,KAAA;AACA,kBAAA,OAAA,CAAA,iBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,OAAA,CAAA,iBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAJA,CAKA;AACA;AACA;;AAbA;AAAA;;AAAA;AAAA;AAAA;AAeA,gBAAA,OAAA,CAAA,GAAA;;AAfA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA,KApjBA;AAqjBA;AACA;AACA,IAAA,SAvjBA,qBAujBA,GAvjBA,EAujBA;AACA,WAAA,SAAA,qFAAA;AAAA;AAAA;AAAA;AAAA;AACA,qBAAA,WAAA;AACA,qBAAA,QAAA,mCAAA,GAAA;AACA,qBAAA,QAAA,CAAA,UAAA,GAAA,EAAA;AAHA;AAAA,uBAIA,KAAA,WAAA,EAJA;;AAAA;AAKA,qBAAA,UAAA,GAAA,KAAA;AACA,qBAAA,KAAA,GAAA,SAAA;AACA,qBAAA,oBAAA,GAAA,IAAA;;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAAA;AASA,KAjkBA;AAkkBA,IAAA,WAlkBA,yBAkkBA;AACA,UAAA,KAAA,KAAA,CAAA,MAAA,EAAA;AACA,aAAA,KAAA,CAAA,MAAA,CAAA,UAAA;AACA;AACA,KAtkBA;AAukBA;AACA,IAAA,UAxkBA,wBAwkBA;AACA,WAAA,QAAA,GAAA;AACA,QAAA,UAAA,EAAA;AADA,OAAA;AAGA,WAAA,SAAA,CAAA,YAAA;AACA,aAAA,KAAA,CAAA,MAAA,EAAA,aAAA;AACA,OAFA;AAGA,WAAA,oBAAA,GAAA,KAAA;AACA,KAhlBA;AAilBA,IAAA,YAjlBA,wBAilBA,IAjlBA,EAilBA;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,IAAA,GAAA,OAAA,IAAA,GAAA,EAAA;;AACA,UAAA,CAAA,QAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,kBAAA;AACA;AACA,KAtlBA;AAulBA,IAAA,WAvlBA,yBAulBA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,+BAAA;AACA,kBAAA,UAAA,EAAA,OAAA,CAAA,QAAA,CAAA;AADA,iBAAA,CADA;;AAAA;AAAA;AACA,gBAAA,IADA,yBACA,IADA;AACA,gBAAA,IADA,yBACA,IADA;;AAIA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,QAAA,CAAA,UAAA,GAAA,IAAA;AACA,kBAAA,OAAA,CAAA,OAAA,GAAA,IAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,wBAAA,KAAA,GAAA,EAAA;AACA,oBAAA,KAAA,CAAA,IAAA,GAAA,IAAA,CAAA,QAAA;AACA,oBAAA,KAAA,CAAA,GAAA,GAAA,OAAA,CAAA,MAAA,CAAA,OAAA,CAAA,QAAA,GAAA,IAAA,CAAA,OAAA;AACA,2BAAA,KAAA;AACA,mBALA,CAAA;AAMA;;AAZA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA,KApmBA;AAqmBA;AACA,IAAA,UAtmBA,sBAsmBA,GAtmBA,EAsmBA;AACA,WAAA,SAAA,qFAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,qBAAA,WAAA;AACA,qBAAA,QAAA,mCAAA,GAAA;AACA,gBAAA,IAHA,GAGA,KAAA,QAAA,CAAA,MAHA;AAIA,qBAAA,QAAA,CAAA,MAAA,GAAA,KAAA,QAAA,CAAA,MAAA;AACA,qBAAA,QAAA,CAAA,MAAA,GAAA,IAAA;AACA,qBAAA,QAAA,CAAA,UAAA,GAAA,EAAA;AANA;AAAA,uBAOA,KAAA,WAAA,EAPA;;AAAA;AAQA,qBAAA,oBAAA,GAAA,IAAA;AACA,qBAAA,UAAA,GAAA,IAAA;AACA,qBAAA,KAAA,GAAA,SAAA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAAA;AAYA,KAnnBA;AAonBA,IAAA,MApnBA,oBAonBA;AAAA;;AACA,UAAA,MAAA,GAAA;AACA,QAAA,EAAA,EAAA,MADA;AAEA,QAAA,IAAA,EAAA,KAAA,QAAA,CAAA,MAFA;AAGA,QAAA,EAAA,EAAA,KAAA,QAAA,CAAA;AAHA,OAAA;AAKA,WAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,4BAAA,OAAA,CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA;AACA,qCAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,oBAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA,CACA;AACA,eAHA;AAIA,cAAA,OAAA,CAAA,UAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;;AACA,cAAA,OAAA,CAAA,YAAA;;AACA,cAAA,OAAA,CAAA,oBAAA,GAAA,KAAA;;AACA,cAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,YAAA,EATA,CAUA;;;AACA,cAAA,OAAA,CAAA,cAAA;;AACA,cAAA,OAAA,CAAA,OAAA;AACA,aAbA,MAaA;AACA,cAAA,OAAA,CAAA,oBAAA,GAAA,KAAA;AACA;AACA,WAjBA;AAkBA,SAnBA,MAmBA;AACA,iBAAA,KAAA;AACA;AACA,OAvBA;AAwBA,KAlpBA;AAopBA,IAAA,YAppBA,0BAopBA;AACA,WAAA,KAAA,CAAA,MAAA,CAAA,MAAA;AACA,KAtpBA;AAupBA;AACA;AACA,IAAA,SAzpBA,uBAypBA;AACA,WAAA,QAAA,GAAA,KAAA;;AACA,UAAA,KAAA,WAAA,EAAA;AACA,aAAA,WAAA;AACA,aAAA,OAAA,GAAA,EAAA;AACA,aAAA,UAAA,GAAA,KAAA;AACA,aAAA,oBAAA,GAAA,IAAA;AACA,aAAA,KAAA,GAAA,SAAA;AACA;;AACA,UAAA,KAAA,UAAA,EAAA;AACA,aAAA,QAAA,GAAA,EAAA;AACA,aAAA,OAAA,GAFA,CAEA;;AACA,aAAA,MAAA,GAAA,KAAA;AACA,aAAA,mBAAA,GAAA,IAAA;AACA;;AACA,UAAA,KAAA,YAAA,EAAA;AACA,aAAA,aAAA,GAAA,QAAA,CADA,CAEA;;AACA,YAAA,KAAA,eAAA,EAAA;AACA,eAAA,QAAA,mCAAA,KAAA,eAAA;AACA,eAAA,QAAA,CAAA,KAAA,GAAA,SAAA;AACA;;AACA,aAAA,QAAA,CAAA,IAAA,GAAA,KAAA,IAAA;AACA,aAAA,QAAA,CAAA,KAAA,GAAA,KAAA,KAAA;AACA,aAAA,QAAA,CAAA,IAAA,GAAA,KAAA,IAAA,CATA,CAUA;;AACA,aAAA,cAAA,GAAA,IAAA,CAXA,CAYA;;AACA,aAAA,cAAA,GAAA,KAAA;AACA;AACA,KAvrBA;AAwrBA,IAAA,WAxrBA,yBAwrBA;AACA,UAAA,QAAA,GAAA,KAAA,OAAA,GAAA,OAAA;AACA,UAAA,SAAA,GAAA,6BAAA;AACA,6BAAA,SAAA,EAAA,KAAA,aAAA,EAAA,QAAA;AACA,KA5rBA;AA6rBA,IAAA,gBA7rBA,8BA6rBA;AACA,UAAA,QAAA,GAAA,QAAA;AACA,UAAA,SAAA,GAAA,6BAAA;AACA,6BAAA,SAAA,EAAA,KAAA,aAAA,EAAA,QAAA;AACA,KAjsBA;AAksBA,IAAA,gBAlsBA,8BAksBA;AACA,WAAA,cAAA,GAAA,yCAAA;AACA,WAAA,QAAA,GAAA,QAAA;AACA,WAAA,KAAA,CAAA,WAAA,CAAA,WAAA;AACA,KAtsBA;AAusBA,IAAA,aAvsBA,yBAusBA,QAvsBA,EAusBA;AACA,cAAA,QAAA;AACA,aAAA,QAAA;AACA,eAAA,UAAA;AACA;;AACA,aAAA,OAAA;AACA,eAAA,SAAA;AACA;;AACA,aAAA,OAAA;AACA,eAAA,WAAA;AACA;;AACA;AACA;AAXA;;AAaA,WAAA,cAAA;AACA,KAttBA;AAutBA,IAAA,eAvtBA,6BAutBA;AACA,UAAA,QAAA,GAAA,OAAA;AACA,UAAA,SAAA,GAAA,4BAAA;AACA,6BAAA,SAAA,EAAA,KAAA,aAAA,EAAA,QAAA;AACA,KA3tBA;AA4tBA,IAAA,eA5tBA,6BA4tBA;AACA,WAAA,eAAA,CAAA,KAAA,GAAA,KAAA,UAAA;AACA,WAAA,cAAA,GAAA,wCAAA;AACA,WAAA,QAAA,GAAA,OAAA;AACA,WAAA,KAAA,CAAA,WAAA,CAAA,WAAA;AACA,KAjuBA;AAkuBA,IAAA,kBAluBA,gCAkuBA;AACA,UAAA,QAAA,GAAA,OAAA;AACA,UAAA,SAAA,GAAA,+BAAA;AACA,UAAA,KAAA,GAAA,EAAA;AACA,MAAA,KAAA,CAAA,IAAA,GAAA,KAAA,IAAA;AACA,6BAAA,SAAA,EAAA,KAAA,EAAA,QAAA;AACA,KAxuBA;AAyuBA,IAAA,kBAzuBA,gCAyuBA;AACA,WAAA,eAAA,CAAA,IAAA,GAAA,KAAA,IAAA;AACA,WAAA,eAAA,CAAA,KAAA,GAAA,KAAA,KAAA;AACA,WAAA,eAAA,CAAA,IAAA,GAAA,KAAA,IAAA;AACA,WAAA,cAAA,GAAA,2CAAA;AACA,WAAA,QAAA,GAAA,OAAA;AACA,WAAA,KAAA,CAAA,WAAA,CAAA,WAAA;AACA,KAhvBA;AAivBA,IAAA,YAjvBA,0BAivBA;AAAA;;AACA,WAAA,KAAA,CAAA,UAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,UAAA,OAAA,CAAA,QAAA,CAAA,UAAA,GAAA,OAAA,CAAA,IAAA;AACA,+BAAA,OAAA,CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,cAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,cAAA,OAAA,CAAA,UAAA,GAAA,KAAA;;AACA,cAAA,OAAA,CAAA,KAAA,CAAA,QAAA,CAAA,WAAA;AACA;AACA,WANA;AAOA,SATA,MASA;AACA,iBAAA,KAAA;AACA;AACA,OAbA;AAcA,KAhwBA;AAiwBA;AACA,IAAA,aAlwBA,yBAkwBA,IAlwBA,EAkwBA;AAAA;;AACA,WAAA,QAAA,GAAA,EAAA;AACA,+BAAA;AAAA,QAAA,MAAA,EAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA;AACA,UAAA,OAAA,CAAA,QAAA,GAAA,CAAA,GAAA,CAAA,IAAA,IAAA,EAAA,EAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,+EACA,IADA;AAEA,cAAA,KAAA,EAAA,IAAA,CAAA,KAAA,IAAA,IAAA,CAAA,KAAA,IAAA,IAAA,CAAA,EAAA,IAAA;AAFA;AAIA,WALA,CAAA;AAMA;AACA,OAVA;AAWA,KA/wBA;;AAixBA;AACA;AACA,IAAA,WAnxBA,uBAmxBA,GAnxBA,EAmxBA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,GAAA,CAAA,EAAA,GAAA,GAAA,CAAA,IAAA,CADA,CACA;;AACA,gBAAA,OAAA,CAAA,mBAAA,CAAA,GAAA;;AACA,gBAAA,OAAA,CAAA,UAAA,CAAA,MAAA,GAAA,GAAA,CAAA,aAAA;AAHA;AAAA,uBAIA,OAAA,CAAA,aAAA,EAJA;;AAAA;AAKA,gBAAA,OAAA,CAAA,WAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA;AACA,gBAAA,OAAA,CAAA,MAAA,GAAA,GAAA,CAAA,KAAA;AACA,gBAAA,OAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA;AACA,gBAAA,OAAA,CAAA,KAAA,GAAA,GAAA,CAAA,KAAA;;AACA,gBAAA,OAAA,CAAA,YAAA;;AAVA;AAAA,uBAWA,OAAA,CAAA,aAAA,CAAA,GAAA,CAAA,aAAA,CAXA;;AAAA;AAWA;AACA;AACA,gBAAA,OAAA,CAAA,QAAA,mCAAA,GAAA,EAbA,CAcA;;AACA,gBAAA,OAAA,CAAA,cAAA,GAAA,KAAA;AACA,gBAAA,OAAA,CAAA,QAAA,GAAA,IAAA,CAhBA,CAiBA;;AACA,gBAAA,OAAA,CAAA,cAAA,GAAA,IAAA;;AAlBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmBA,KAtyBA;AAuyBA;AACA,IAAA,mBAxyBA,+BAwyBA,GAxyBA,EAwyBA;AACA,WAAA,QAAA,GAAA,EAAA;AACA,WAAA,UAAA,CAAA,MAAA,GAAA,GAAA,CAAA,aAAA;AACA,WAAA,QAAA,CAAA,MAAA,GAAA,GAAA,CAAA,aAAA;AACA,WAAA,QAAA,CAAA,IAAA,GAAA,GAAA,CAAA,KAAA;AACA,WAAA,aAAA;AACA,KA9yBA;AA+yBA;AACA,IAAA,aAhzBA,2BAgzBA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,aAAA,GAAA,EAAA;AACA,kDAAA,OAAA,CAAA,UAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,OAAA,CAAA,aAAA,GAAA,GAAA,CAAA,IAAA;;AACA,kBAAA,OAAA,CAAA,aAAA;AACA,iBAHA;;AAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,KAtzBA;AAuzBA;AACA,IAAA,aAxzBA,2BAwzBA;AAAA;;AACA,sCAAA,KAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,IAAA,EAAA,EAAA;AACA,UAAA,OAAA,CAAA,QAAA,mCAAA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AACA;AACA,OAJA;AAKA,KA9zBA;;AA+zBA;;;AAGA,IAAA,YAl0BA,wBAk0BA,GAl0BA,EAk0BA;AAAA;;AACA,UAAA,KAAA,+DAAA,GAAA,GAAA,KAAA,WAAA,CAAA;AACA,kCAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,aAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,OAAA,CAAA,aAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,OAHA;AAIA,KAx0BA;AAy0BA;AACA,IAAA,qBA10BA,mCA00BA;AACA;AACA,WAAA,IAAA,CAAA,KAAA,QAAA,EAAA,MAAA,EAAA,EAAA,EAFA,CAGA;;AACA,WAAA,qBAAA;AACA,KA/0BA;AAg1BA;AACA,IAAA,qBAj1BA,mCAi1BA;AAAA;;AACA;AACA,WAAA,oBAAA,CAAA,KAAA,GAAA,KAAA,QAAA,CAAA,KAAA;AACA,yCAAA,KAAA,oBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,iBAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KAv1BA;AAw1BA;AACA,IAAA,UAz1BA,sBAy1BA,IAz1BA,EAy1BA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,UAAA,CAAA,MAAA,GAAA,IAAA;AADA;AAAA,uBAEA,OAAA,CAAA,aAAA,EAFA;;AAAA;AAAA;AAAA,uBAGA,OAAA,CAAA,aAAA,CAAA,IAAA,CAHA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,KA71BA;AA81BA;AACA,IAAA,0BA/1BA,sCA+1BA,GA/1BA,EA+1BA,KA/1BA,EA+1BA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gCACA,GAAA,CAAA,IADA;AAAA,oDAEA,QAFA,yBAKA,IALA,yBAQA,IARA,yBAWA,MAXA,0BAcA,QAdA,0BAiBA,IAjBA,0BAoBA,MApBA,0BAuBA,MAvBA;AAAA;;AAAA;AAAA;AAAA,uBAGA,OAAA,CAAA,YAAA,EAHA;;AAAA;AAAA;;AAAA;AAAA;AAAA,uBAMA,OAAA,CAAA,SAAA,EANA;;AAAA;AAAA;;AAAA;AAAA;AAAA,uBASA,OAAA,CAAA,SAAA,EATA;;AAAA;AAAA;;AAAA;AAAA;AAAA,uBAYA,OAAA,CAAA,WAAA,EAZA;;AAAA;AAAA;;AAAA;AAAA;AAAA,uBAeA,OAAA,CAAA,aAAA,EAfA;;AAAA;AAAA;;AAAA;AAAA;AAAA,uBAkBA,OAAA,CAAA,SAAA,EAlBA;;AAAA;AAAA;;AAAA;AAAA;AAAA,uBAqBA,OAAA,CAAA,WAAA,EArBA;;AAAA;AAAA;;AAAA;AAAA;AAAA,uBAwBA,OAAA,CAAA,WAAA,EAxBA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA2BA,KA13BA;AA23BA;AACA,IAAA,MA53BA,oBA43BA;AAAA;;AACA,WAAA,kBAAA,GAAA,IAAA;AACA,WAAA,KAAA,CAAA,UAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,UAAA,OAAA,CAAA,QAAA;AACA,SAFA,MAEA;AACA,UAAA,OAAA,CAAA,kBAAA,GAAA,KAAA;;AACA,UAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;;AACA,iBAAA,KAAA;AACA;AACA,OARA;AASA,KAv4BA;AAw4BA;AACA;AACA,IAAA,UA14BA,wBA04BA;AACA,WAAA,QAAA,GAAA,KAAA,QAAA,CAAA,IAAA,GAAA,IAAA;AACA,WAAA,QAAA,GAAA,KAAA,QAAA,CAAA,IAAA,GAAA,IAAA;AACA,WAAA,SAAA,CAAA,YAAA;AACA,aAAA,KAAA,CAAA,UAAA,EAAA,aAAA;AACA,OAFA;AAGA,WAAA,eAAA,GAAA,QAAA;AACA,WAAA,cAAA,GAAA,KAAA;AACA,KAl5BA;;AAm5BA;;;AAGA,IAAA,QAt5BA,sBAs5BA;AAAA;;AACA,WAAA,QAAA,CAAA,cAAA,GAAA,KAAA,QAAA;AACA,0BAAA,KAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,UAAA,OAAA,CAAA,QAAA,CAAA;AACA,YAAA,IAAA,EAAA,SADA;AAEA,YAAA,OAAA,EAAA;AAFA,WAAA;;AAIA,UAAA,OAAA,CAAA,cAAA,GAAA,KAAA,CALA,CAMA;;AACA,UAAA,OAAA,CAAA,WAAA;AACA;;AACA,QAAA,OAAA,CAAA,kBAAA,GAAA,KAAA;AACA,OAXA;AAYA,KAp6BA;AAq6BA;AACA,IAAA,YAt6BA,wBAs6BA,GAt6BA,EAs6BA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,GAAA,CAAA,EAAA,GAAA,GAAA,CAAA,IAAA,CADA,CACA;;AACA,gBAAA,OAAA,CAAA,mBAAA,CAAA,GAAA;;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA;AACA,gBAAA,OAAA,CAAA,MAAA,GAAA,GAAA,CAAA,KAAA;AACA,gBAAA,OAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA;AACA,gBAAA,OAAA,CAAA,KAAA,GAAA,GAAA,CAAA,KAAA;;AACA,gBAAA,OAAA,CAAA,YAAA;;AARA;AAAA,uBASA,OAAA,CAAA,aAAA,CAAA,GAAA,CAAA,aAAA,CATA;;AAAA;AASA;AACA;AACA,gBAAA,OAAA,CAAA,QAAA,mCAAA,GAAA;AACA,gBAAA,OAAA,CAAA,iBAAA,GAAA,CACA;AAAA,kBAAA,KAAA,EAAA,OAAA,CAAA,QAAA,CAAA,IAAA;AAAA,kBAAA,KAAA,EAAA,OAAA,CAAA,QAAA,CAAA;AAAA,iBADA,CAAA,CAZA,CAeA;;AACA,gBAAA,OAAA,CAAA,cAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,QAAA,GAAA,IAAA,CAjBA,CAkBA;;AACA,gBAAA,OAAA,CAAA,cAAA,GAAA,IAAA;;AAnBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoBA,KA17BA;AA27BA;AACA,IAAA,sBA57BA,oCA47BA;AAAA;;AACA,0CAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,kBAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KAh8BA;AAi8BA,IAAA,SAj8BA,uBAi8BA;AACA,WAAA,mBAAA,GAAA,KAAA;AACA,WAAA,KAAA,CAAA,QAAA,CAAA,WAAA;AACA,KAp8BA;AAq8BA;AACA,IAAA,WAt8BA,yBAs8BA;AACA;AACA,UAAA,KAAA,WAAA,EAAA;AACA,aAAA,cAAA,GAAA;AACA,UAAA,OAAA,EAAA,CADA;AAEA,UAAA,QAAA,EAAA;AAFA,SAAA;AAIA,OAPA,CASA;;;AACA,UAAA,KAAA,UAAA,EAAA;AACA,YAAA,KAAA,GAAA,KAAA,aAAA,CAAA,KAAA;AACA,YAAA,IAAA,GAAA,KAAA,aAAA,CAAA,IAAA;AACA,aAAA,aAAA,GAAA;AACA,UAAA,KAAA,EAAA,KADA;AAEA,UAAA,IAAA,EAAA,IAFA;AAGA,UAAA,IAAA,EAAA,SAHA;AAIA,UAAA,QAAA,EAAA,EAJA;AAKA,UAAA,OAAA,EAAA;AALA,SAAA;AAOA,OApBA,CAsBA;;;AACA,UAAA,KAAA,YAAA,EAAA;AACA,aAAA,UAAA,GAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA,CADA,CAKA;;AACA,aAAA,cAAA,GAAA;AACA,UAAA,KAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA,KAAA,IAFA;AAGA,UAAA,OAAA,EAAA,CAHA;AAIA,UAAA,QAAA,EAAA;AAJA,SAAA;AAMA,OAnCA,CAqCA;;;AACA,WAAA,WAAA,CAAA,IAAA,GAAA;AACA,QAAA,OAAA,EAAA,EADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA,EAHA;AAIA,QAAA,OAAA,EAAA,EAJA;AAKA,QAAA,OAAA,EAAA,EALA;AAMA,QAAA,IAAA,EAAA;AANA,OAAA;AAQA,WAAA,WAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,KAAA,UAAA,EAAA;AACA,UAAA,IAAA,CAAA,aAAA,GAAA,EAAA;AACA;AACA,OAJA;AAMA,WAAA,WAAA,CAAA,IAAA,GAAA;AACA,QAAA,OAAA,EAAA,EADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA,EAHA;AAIA,QAAA,OAAA,EAAA,EAJA;AAKA,QAAA,OAAA,EAAA,EALA;AAMA,QAAA,IAAA,EAAA;AANA,OAAA;AAQA,WAAA,WAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,KAAA,UAAA,EAAA;AACA,UAAA,IAAA,CAAA,aAAA,GAAA,EAAA;AACA;AACA,OAJA;AAMA,WAAA,WAAA,CAAA,IAAA,GAAA;AACA,QAAA,OAAA,EAAA,EADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA,EAHA;AAIA,QAAA,OAAA,EAAA,EAJA;AAKA,QAAA,OAAA,EAAA,EALA;AAMA,QAAA,IAAA,EAAA;AANA,OAAA;AAQA,WAAA,WAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,KAAA,UAAA,EAAA;AACA,UAAA,IAAA,CAAA,aAAA,GAAA,EAAA;AACA;AACA,OAJA,EA1EA,CAgFA;;AACA,UAAA,KAAA,WAAA,IAAA,KAAA,KAAA,CAAA,QAAA,EAAA;AACA,aAAA,KAAA,CAAA,QAAA,CAAA,WAAA,GAAA,CAAA;AACA;;AACA,UAAA,KAAA,UAAA,IAAA,KAAA,KAAA,CAAA,OAAA,EAAA;AACA,aAAA,KAAA,CAAA,OAAA,CAAA,WAAA,GAAA,CAAA;AACA;;AACA,UAAA,KAAA,YAAA,IAAA,KAAA,KAAA,CAAA,SAAA,EAAA;AACA,aAAA,KAAA,CAAA,SAAA,CAAA,WAAA,GAAA,CAAA;AACA,OAzFA,CA2FA;;;AACA,UAAA,KAAA,WAAA,EAAA;AACA,aAAA,UAAA;AACA,OAFA,MAEA,IAAA,KAAA,UAAA,EAAA;AACA,aAAA,SAAA;AACA,OAFA,MAEA,IAAA,KAAA,YAAA,EAAA;AACA,aAAA,WAAA;AACA;AACA,KAziCA;AA0iCA,IAAA,gBA1iCA,4BA0iCA,WA1iCA,EA0iCA,EA1iCA,EA0iCA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OADA,GACA,WAAA,GACA,OAAA,CAAA,QAAA,CAAA,MAAA,CACA,UAAA,IAAA;AAAA,yBACA,IAAA,CAAA,KAAA,CAAA,WAAA,GAAA,OAAA,CAAA,WAAA,CAAA,WAAA,EAAA,MAAA,CAAA,CADA;AAAA,iBADA,CADA,GAKA,OAAA,CAAA,QANA,EAOA;;AACA,gBAAA,gBARA,GAQA,OAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,yBAAA;AACA,oBAAA,KAAA,EAAA,IAAA,CAAA,KADA;AAEA,oBAAA,KAAA,EAAA,IAAA,CAAA,KAAA,IAAA,IAAA,CAAA,KAFA,CAEA;;AAFA,mBAAA;AAAA,iBAAA,CARA;AAYA,gBAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,gBAAA;AACA,gBAAA,EAAA,CAAA,gBAAA,CAAA;;AAbA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA,KAxjCA;AAyjCA,IAAA,YAzjCA,wBAyjCA,IAzjCA,EAyjCA;AACA,WAAA,uBAAA,GAAA,IAAA,CADA,CACA;;AACA,WAAA,QAAA,CAAA,EAAA,GAAA,IAAA,CAAA,KAAA;AACA,KA5jCA;AA6jCA,IAAA,YA7jCA,wBA6jCA,KA7jCA,EA6jCA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA,gBAAA,UAAA,CAAA,YAAA;AACA,sBAAA,OAAA,CAAA,uBAAA,EAAA;AACA,oBAAA,OAAA,CAAA,uBAAA,GAAA,KAAA,CADA,CACA;;AACA;AACA,mBAJA,CAKA;;;AACA,sBAAA,KAAA,IAAA,CAAA,OAAA,CAAA,QAAA,CAAA,IAAA,CAAA,UAAA,IAAA;AAAA,2BAAA,IAAA,CAAA,KAAA,KAAA,KAAA;AAAA,mBAAA,CAAA,EAAA;AACA,wBAAA;AACA,0BAAA,MAAA,GAAA;AACA,wBAAA,IAAA,EAAA,KADA;AAEA,wBAAA,MAAA,EAAA,OAAA,CAAA,QAAA,CAAA,aAFA,CAEA;;AAFA,uBAAA;AAIA,2CAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,4BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,0BAAA,OAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACA,4BAAA,KAAA,EAAA,KADA;AAEA,4BAAA,KAAA,EAAA,KAFA,CAEA;;AAFA,2BAAA;;AAIA,0BAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,SAAA;AACA,yBANA,MAMA;AACA,0BAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,GAAA,CAAA,GAAA,IAAA,QAAA;AACA;AACA,uBAVA;AAWA,qBAhBA,CAgBA,OAAA,KAAA,EAAA;AACA,sBAAA,OAAA,CAAA,OAAA,CAAA,SAAA,EAAA,KAAA;;AACA,sBAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,QAAA;AACA;AACA;AACA,iBA5BA,EA4BA,GA5BA,CAAA;;AAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA+BA;AA5lCA;AAh0BA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card shadow=\"never\" style=\"background:#e0f8ed;padding-top:10px;\">\n          <div>\n            <el-col>\n              <el-form label-width=\"80px\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"所属公司:\">\n                    <el-select\n                      v-model=\"treeForm.ssdwbm\"\n                      placeholder=\"请选择所属公司\"\n                      @change=\"getNewTreeInfo\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in OrganizationSelectedList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"电压等级:\">\n                    <el-select\n                      v-model=\"treeForm.dydjbm\"\n                      placeholder=\"请选择电压等级\"\n                      @change=\"getNewTreeInfo\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in VoltageLevelSelectedList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"快速查询:\">\n                    <el-input\n                      placeholder=\"输入关键字进行过滤\"\n                      v-model=\"filterText\"\n                      clearable\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-form>\n            </el-col>\n          </div>\n          <div class=\"text head-container\">\n            <el-col style=\"padding:0;\">\n              <el-tree\n                :expand-on-click-node=\"true\"\n                id=\"tree\"\n                :data=\"treeOptions\"\n                @node-click=\"handleNodeClick\"\n                :highlight-current=\"true\"\n                ref=\"tree\"\n                :filter-node-method=\"filterNode\"\n                node-key=\"id\"\n                :default-checked-keys=\"['0']\"\n                :default-expanded-keys=\"['0']\"\n                accordion\n              >\n                <span slot-scope=\"{ node, data }\">\n                  <i :class=\"icons[data.icon]\" />\n                  <span style=\"margin-left:5px;\" :title=\"data.label\">{{\n                    data.label\n                  }}</span>\n                </span>\n              </el-tree>\n              <!--              :default-expanded-keys=\"['0']\"-->\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo1.data\"\n          :field-list=\"filterInfo1.fieldList\"\n          :width=\"{ labelWidth: 100, itemWidth: 170 }\"\n          @handleReset=\"filterReset\"\n          comp-table=\"tableAndPageInfo1\"\n          v-show=\"bdzdataShow\"\n        />\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo2.data\"\n          :field-list=\"filterInfo2.fieldList\"\n          :width=\"{ labelWidth: 100, itemWidth: 150 }\"\n          @handleReset=\"filterReset\"\n          comp-table=\"tableAndPageInfo2\"\n          v-show=\"jgdataShow\"\n        />\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo3.data\"\n          :field-list=\"filterInfo3.fieldList\"\n          :width=\"{ labelWidth: 100, itemWidth: 150 }\"\n          @handleReset=\"filterReset\"\n          comp-table=\"tableAndPageInfo3\"\n          v-show=\"znsbdataShow\"\n        />\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button\n              icon=\"el-icon-plus\"\n              @click=\"AddButton\"\n              v-hasPermi=\"['jgdy:button:add']\"\n              type=\"primary\"\n              >新增\n            </el-button>\n            <el-button\n              v-show=\"jgdataShow\"\n              type=\"success\"\n              icon=\"el-icon-download\"\n              @click=\"exportExcel\"\n              >导出整站设备</el-button\n            >\n            <el-button\n              v-show=\"bdzdataShow\"\n              type=\"success\"\n              icon=\"el-icon-download\"\n              @click=\"exportExcelOfBdz\"\n              >导出变电站</el-button\n            >\n            <el-button\n              v-hasPermi=\"['jgdy:button:add']\"\n              v-show=\"bdzdataShow\"\n              type=\"success\"\n              icon=\"el-icon-upload\"\n              @click=\"importExcelOfBdz\"\n              >导入变电站</el-button\n            >\n            <el-button\n              v-show=\"jgdataShow\"\n              type=\"success\"\n              icon=\"el-icon-download\"\n              @click=\"exportExcelOfJg\"\n              >导出间隔</el-button\n            >\n            <el-button\n              v-hasPermi=\"['jgdy:button:add']\"\n              v-show=\"jgdataShow\"\n              type=\"success\"\n              icon=\"el-icon-upload\"\n              @click=\"importExcelOfJg\"\n              >导入间隔</el-button\n            >\n            <el-button\n              v-hasPermi=\"['jgdy:button:add']\"\n              v-show=\"znsbdataShow\"\n              type=\"primary\"\n              icon=\"el-icon-plus\"\n              @click=\"isShowCopy = true\"\n              >设备复制</el-button\n            >\n            <el-button\n              v-show=\"znsbdataShow\"\n              type=\"success\"\n              icon=\"el-icon-download\"\n              @click=\"exportExcelOfAsset\"\n              >导出设备</el-button\n            >\n            <el-button\n              v-hasPermi=\"['jgdy:button:add']\"\n              v-show=\"znsbdataShow\"\n              type=\"success\"\n              icon=\"el-icon-upload\"\n              @click=\"importExcelOfAsset\"\n              >导入设备</el-button\n            >\n            <!--<el-button icon=\"el-icon-delete\" v-hasPermi=\"['jgdy:button:delete']\" type=\"danger\" @click=\"removeAll\">删除-->\n            <!--</el-button>-->\n          </div>\n          <!--变电站-->\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo1\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"61vh\"\n            v-show=\"bdzdataShow\"\n            @getMethod=\"getbdzData\"\n            v-loading=\"loading\"\n            ref=\"bdzTable\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"updatebdz(scope.row)\"\n                  v-hasPermi=\"['jgdy:button:update']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                >\n                </el-button>\n                <el-button\n                  @click=\"bdzDetails(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n                <el-button\n                  v-hasPermi=\"['jgdy:button:delete']\"\n                  type=\"text\"\n                  v-if=\"scope.row.createBy === $store.getters.name\"\n                  size=\"small\"\n                  @click=\"removeAll(scope.row)\"\n                  title=\"删除\"\n                  class=\"el-icon-delete\"\n                >\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n          <!--间隔-->\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo2\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"70vh\"\n            v-show=\"jgdataShow\"\n            @getMethod=\"getJgData\"\n            ref=\"jgTable\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"updateJg(scope.row)\"\n                  v-hasPermi=\"['jgdy:button:update']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                ></el-button>\n                <el-button\n                  @click=\"jgDetails(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n                <el-button\n                  v-hasPermi=\"['jgdy:button:delete']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"删除\"\n                  class=\"el-icon-delete\"\n                  @click=\"removeAll(scope.row)\"\n                >\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n          <!--设备-->\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo3\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"61vh\"\n            v-show=\"znsbdataShow\"\n            @getMethod=\"getZnsbData\"\n            ref=\"znsbTable\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"updateAsset(scope.row)\"\n                  v-hasPermi=\"['jgdy:button:update']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                ></el-button>\n                <el-button\n                  @click=\"assetDetails(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n                <el-button\n                  v-hasPermi=\"['jgdy:button:delete']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"删除\"\n                  class=\"el-icon-delete\"\n                  @click=\"removeAll(scope.row)\"\n                >\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!--变电站所用弹出框开始-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"bdzDialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"60%\"\n      v-dialogDrag\n      :before-close=\"removeForm\"\n    >\n      <el-form :model=\"jbxxForm\" label-width=\"130px\" :rules=\"rules\" ref=\"form\">\n        <div\n          class=\"block\"\n          style=\"width: 50%;height: 50%;margin-bottom: 2%;float: right\"\n        >\n          <span class=\"demonstration\">变电站图片</span>\n          <el-carousel\n            trigger=\"click\"\n            class=\"imgCls\"\n            indicator-position=\"none\"\n            :interval=\"2000\"\n            type=\"card\"\n            id=\"imgId\"\n          >\n            <el-carousel-item v-for=\"(img, index) in imgList\" :key=\"index\">\n              <viewer :images=\"imgList\" style=\"z-index: 999\">\n                <img :src=\"img.url\" width=\"100%\" height=\"100%\" />\n              </viewer>\n            </el-carousel-item>\n          </el-carousel>\n        </div>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属单位\" prop=\"ssdwbm\">\n              <el-select\n                v-model=\"jbxxForm.ssdwbm\"\n                :placeholder=\"isDisabled ? '' : '所属单位'\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option\n                  v-for=\"item in OrganizationSelectedList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"变电站数字编号\" prop=\"bdzszbh\">\n              <el-input\n                v-model=\"jbxxForm.bdzszbh\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入变电站数字编号'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"变电站名称\" prop=\"bdzmc\">\n              <el-input\n                v-model=\"jbxxForm.bdzmc\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入变电站名称'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属电网\" prop=\"ssdw\">\n              <el-input\n                v-model=\"jbxxForm.ssdw\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入所属电网'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属基地站\" prop=\"ssjdz\">\n              <el-select\n                v-model=\"jbxxForm.ssjdz\"\n                :placeholder=\"isDisabled ? '' : '请选择所属基地站'\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option\n                  v-for=\"item in ssjdzList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电压等级\" prop=\"dydjbm\">\n              <el-select\n                v-model=\"jbxxForm.dydjbm\"\n                :placeholder=\"isDisabled ? '' : '请选择电压等级'\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option\n                  v-for=\"item in VoltageLevelSelectedList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"设备状态\" prop=\"sbzt\">\n              <el-select\n                v-model=\"jbxxForm.sbzt\"\n                :placeholder=\"isDisabled ? '' : '请选择设备状态'\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option\n                  v-for=\"item in sbztOptionsDataList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"投运日期\" class=\"add_sy_tyrq\" prop=\"tyrq\">\n              <el-date-picker\n                v-model=\"jbxxForm.tyrq\"\n                type=\"date\"\n                :placeholder=\"isDisabled ? '' : '选择日期'\"\n                format=\"yyyy-MM-dd\"\n                value-format=\"yyyy-MM-dd\"\n                :picker-options=\"pickerOptions\"\n                :disabled=\"isDisabled\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"电站类型\" prop=\"dzlx\">\n              <el-select v-model=\"jbxxForm.dzlx\" :disabled=\"isDisabled\">\n                <el-option value=\"变电站\" label=\"变电站\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"是否综合自动化站\" prop=\"sfzhzdh\">\n              <el-select v-model=\"jbxxForm.sfzhzdh\" :disabled=\"isDisabled\">\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否数字化变电站\" prop=\"sfszhbdz\">\n              <el-select\n                v-model=\"jbxxForm.sfszhbdz\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请选择'\"\n              >\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否枢纽站\" prop=\"sfsnz\">\n              <el-select\n                v-model=\"jbxxForm.sfsnz\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请选择'\"\n              >\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item\n              label=\"退运日期\"\n              class=\"add_sy_tyrq\"\n              prop=\"returnDate\"\n            >\n              <el-date-picker\n                v-model=\"jbxxForm.returnDate\"\n                type=\"date\"\n                :placeholder=\"isDisabled ? '' : '选择日期'\"\n                format=\"yyyy-MM-dd\"\n                value-format=\"yyyy-MM-dd\"\n                :picker-options=\"pickerOptions\"\n                :disabled=\"isDisabled\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"占地面积\" prop=\"zymj\">\n              <el-input\n                v-model=\"jbxxForm.zymj\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入占地面积'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"污秽等级\" prop=\"whdj\">\n              <el-select v-model=\"jbxxForm.whdj\" :disabled=\"isDisabled\">\n                <el-option value=\"a级\" label=\"a级\"></el-option>\n                <el-option value=\"b级\" label=\"b级\"></el-option>\n                <el-option value=\"c级\" label=\"c级\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"值班方式\" prop=\"zbfs\">\n              <el-select\n                v-model=\"jbxxForm.zbfs\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请选择'\"\n              >\n                <el-option value=\"有人值班\" label=\"有人值班\"></el-option>\n                <el-option value=\"无人值班\" label=\"无人值班\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否光纤通讯\" prop=\"sfgqtx\">\n              <el-select\n                v-model=\"jbxxForm.sfgqtx\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请选择'\"\n              >\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"海拔\" prop=\"hb\">\n              <el-input v-model=\"jbxxForm.hb\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col> -->\n\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"工程编号\" prop=\"gcbh\">\n              <el-input v-model=\"jbxxForm.gcbh\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"8\">\n            <el-form-item label=\"设计单位\" prop=\"sjdw\">\n              <el-input v-model=\"jbxxForm.sjdw\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"8\">\n            <el-form-item label=\"监理单位\" prop=\"jldw\">\n              <el-input v-model=\"jbxxForm.jldw\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"电站重要级别\" prop=\"zyjb\">\n              <el-select\n                v-model=\"jbxxForm.zyjb\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请选择'\"\n              >\n                <el-option value=\"A\" label=\"A\"></el-option>\n                <el-option value=\"B\" label=\"B\"></el-option>\n                <el-option value=\"C\" label=\"C\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"布置方式\" prop=\"bzfs\">\n              <el-select\n                v-model=\"jbxxForm.bzfs\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请选择'\"\n              >\n                <el-option value=\"户内\" label=\"户内\"></el-option>\n                <el-option value=\"户外\" label=\"户外\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"站址\" prop=\"bdzdz\">\n              <el-input\n                v-model=\"jbxxForm.bdzdz\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入站址'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"建筑面积\" prop=\"jzmj\">\n              <el-input\n                v-model=\"jbxxForm.jzmj\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入建筑面积'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"联系电话\" prop=\"phone\">\n              <el-input\n                v-model=\"jbxxForm.phone\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入联系电话'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"工程名称\" prop=\"gcmc\">\n              <el-input v-model=\"jbxxForm.gcmc\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"施工单位\" prop=\"sgdw\">\n              <el-input v-model=\"jbxxForm.sgdw\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"地区特征\" prop=\"dqtz\">\n              <el-select v-model=\"jbxxForm.dqtz\" :disabled=\"isDisabled\">\n                <el-option value=\"户内\" label=\"户内\"></el-option>\n                <el-option value=\"户外\" label=\"户外\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"最高调度管辖权\" prop=\"zgddgxq\">\n              <el-input\n                v-model=\"jbxxForm.zgddgxq\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入最高调度管辖权'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"是否满足N-1\" prop=\"sfmzn\">\n              <el-select v-model=\"jbxxForm.sfmzn\" :disabled=\"isDisabled\">\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>-->\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否接入故障系统\" prop=\"sfjrgzxt\">\n              <el-select\n                v-model=\"jbxxForm.sfjrgzxt\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请选择'\"\n              >\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否接入AVC\" prop=\"sfjravc\">\n              <el-select\n                v-model=\"jbxxForm.sfjravc\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请选择'\"\n              >\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否集中监控\" prop=\"sfjzjk\">\n              <el-select\n                v-model=\"jbxxForm.sfjzjk\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请选择'\"\n              >\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"接入的监控中心\" prop=\"jkzxmc\">\n              <el-input\n                v-model=\"jbxxForm.jkzxmc\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入监控中心'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"经度\" prop=\"jd\">\n              <el-input\n                v-model=\"jbxxForm.jd\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入经度'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"纬度\" prop=\"wd\">\n              <el-input\n                v-model=\"jbxxForm.wd\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入纬度'\"\n              >\n              </el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\" prop=\"bz\">\n              <el-input\n                v-model=\"jbxxForm.bz\"\n                :disabled=\"isDisabled\"\n                type=\"textarea\"\n                rows=\"2\"\n                :placeholder=\"isDisabled ? '' : '请输入备注'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-form-item\n            label=\"已上传图片：\"\n            prop=\"attachment\"\n            v-if=\"jbxxForm.attachment.length > 0\"\n            id=\"pic_form\"\n          >\n            <el-col\n              :span=\"24\"\n              v-for=\"(item, index) in jbxxForm.attachment\"\n              style=\"margin-left: 0\"\n            >\n              <el-form-item :label=\"(index + 1).toString()\">\n                {{ item.fileOldName }}\n                <el-button\n                  v-if=\"!isDisabled\"\n                  type=\"error\"\n                  size=\"mini\"\n                  @click=\"deleteFileById(item.fileId)\"\n                  >删除\n                </el-button>\n              </el-form-item>\n            </el-col>\n          </el-form-item>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-form-item label=\"上传图片：\" v-if=\"!isDisabled\">\n            <el-upload\n              list-type=\"picture-card\"\n              class=\"upload-demo\"\n              accept=\".jpg,.png\"\n              ref=\"upload\"\n              :headers=\"header\"\n              action=\"/isc-api/file/upload\"\n              :before-upload=\"beforeUpload\"\n              :data=\"uploadData\"\n              single\n              :auto-upload=\"false\"\n              multiple\n            >\n              <el-button slot=\"trigger\" type=\"primary\">选取文件</el-button>\n              <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png文件</div>\n            </el-upload>\n          </el-form-item>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"removeForm\">取 消</el-button>\n        <el-button\n          v-if=\"title == '变电站台账修改' || title == '变电站台账新增'\"\n          type=\"primary\"\n          @click=\"addBdz\"\n          class=\"pmyBtn\"\n          >确 定\n        </el-button>\n      </div>\n    </el-dialog>\n    <!--  变电站新增  -->\n    <el-dialog\n      title=\"设备详情\"\n      :visible.sync=\"znsbDialogForm\"\n      width=\"68%\"\n      :before-close=\"resetForm1\"\n      v-dialogDrag\n      v-if=\"znsbDialogForm\"\n    >\n      <el-tabs v-model=\"activeTabName\">\n        <el-tab-pane label=\"基本信息\" name=\"sbDesc\">\n          <el-form\n            :model=\"sbxxForm\"\n            label-width=\"130px\"\n            ref=\"sbxxForm\"\n            :rules=\"sbxxRules\"\n            :disabled=\"assetIsDisable\"\n          >\n            <el-card class=\"box-cont\">\n              <div slot=\"header\" class=\"clearfix\">\n                <span>基本信息</span>\n              </div>\n              <el-row :gutter=\"20\" class=\"cont_top\">\n                <el-col :span=\"8\">\n                  <el-form-item label=\"所属公司\" prop=\"ssgs\">\n                    <el-select\n                      v-model=\"sbxxForm.ssgs\"\n                      :placeholder=\"assetIsDisable ? '' : '请选择所属公司'\"\n                      filterable\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in OrganizationSelectedList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"String(item.value)\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"所属电站\" prop=\"ssbdz\">\n                    <el-select\n                      v-model=\"sbxxForm.ssbdz\"\n                      :placeholder=\"assetIsDisable ? '' : '请选择所属电站'\"\n                      filterable\n                      @change=\"bdzOptionsChangeClick\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in bdzOptionsDataList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"所属间隔\" prop=\"ssjg\">\n                    <el-select\n                      v-model=\"sbxxForm.ssjg\"\n                      :placeholder=\"assetIsDisable ? '' : '请选择所属间隔'\"\n                      filterable\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in jgOptionsDataList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"设备代码\" prop=\"sbdm\">\n                    <el-input\n                      v-model=\"sbxxForm.sbdm\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写设备代码'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"设备名称\" prop=\"sbmc\">\n                    <el-input\n                      v-model=\"sbxxForm.sbmc\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写设备名称'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"设备类型\" prop=\"assetTypeCode\">\n                    <el-select\n                      v-model=\"sbxxForm.assetTypeCode\"\n                      :placeholder=\"assetIsDisable ? '' : '请选择设备类型'\"\n                      @change=\"showParams\"\n                      filterable\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in sblxOptionsDataSelected\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"电压等级\" prop=\"dydjbm\">\n                    <el-select\n                      v-model=\"sbxxForm.dydjbm\"\n                      :placeholder=\"assetIsDisable ? '' : '请选择电压等级'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in VoltageLevelSelectedList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"设备状态\" prop=\"sbzt\">\n                    <el-select\n                      v-model=\"sbxxForm.sbzt\"\n                      :placeholder=\"assetIsDisable ? '' : '请选择设备状态'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in sbztOptionsDataList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"8\">\n                  <el-form-item label=\"相数\">\n                    <el-select\n                      v-model=\"sbxxForm.xs\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写相数'\"\n                      @change=\"xsChangeFunc\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in xsOptions\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"8\">\n                  <el-form-item label=\"相别\">\n                    <el-select\n                      v-model=\"sbxxForm.xb\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写相别'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in xbOptions\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"8\">\n                  <el-form-item label=\"安装位置\" prop=\"azwz\">\n                    <el-select\n                      v-model=\"sbxxForm.azwz\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写安装位置'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in placeOptions\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"用途\">\n                    <el-input\n                      v-model=\"sbxxForm.yt\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写用途'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"型号\" prop=\"xh\">\n                    <el-autocomplete\n                      v-model=\"sbxxForm.xh\"\n                      :fetch-suggestions=\"querySearchAsync\"\n                      :placeholder=\"assetIsDisable ? '' : '请选择或输入型号'\"\n                      @select=\"handleSelect\"\n                      @change=\"handleChange\"\n                    ></el-autocomplete>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"使用环境\" prop=\"syhj\">\n                    <el-select\n                      v-model=\"sbxxForm.syhj\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写使用环境'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in placeOptions\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"生产厂家\" prop=\"sccj\">\n                    <el-input\n                      v-model=\"sbxxForm.sccj\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写生产厂家'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"制造国家\">\n                    <el-input\n                      v-model=\"sbxxForm.zzgj\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写制造国家'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"产品代号\">\n                    <el-input\n                      v-model=\"sbxxForm.cpdh\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写产品代号'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"8\">\n                  <el-form-item label=\"组合设备类型\">\n                    <el-input\n                      v-model=\"sbxxForm.zhsblxbm\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写组合设备类型'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"组合设备类型名称\">\n                    <el-input\n                      v-model=\"sbxxForm.zhsblx\"\n                      :placeholder=\"\n                        assetIsDisable ? '' : '请填写组合设备类型名称'\n                      \"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"8\">\n                  <el-form-item label=\"运行编号\" prop=\"yxbh\">\n                    <el-input\n                      v-model=\"sbxxForm.yxbh\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写运行编号'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"出厂编号\" prop=\"ccbh\">\n                    <el-input\n                      v-model=\"sbxxForm.ccbh\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写出厂编号'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item\n                    label=\"出厂日期\"\n                    class=\"add_sy_tyrq\"\n                    prop=\"ccrq\"\n                  >\n                    <el-date-picker\n                      v-model=\"sbxxForm.ccrq\"\n                      type=\"date\"\n                      :placeholder=\"assetIsDisable ? '' : '选择日期'\"\n                      format=\"yyyy-MM-dd\"\n                      value-format=\"yyyy-MM-dd\"\n                    >\n                    </el-date-picker>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item\n                    label=\"投运日期\"\n                    class=\"add_sy_tyrq\"\n                    prop=\"tyrq\"\n                  >\n                    <el-date-picker\n                      v-model=\"sbxxForm.tyrq\"\n                      type=\"date\"\n                      :placeholder=\"assetIsDisable ? '' : '选择日期'\"\n                      format=\"yyyy-MM-dd\"\n                      :picker-options=\"pickerOptions\"\n                      value-format=\"yyyy-MM-dd\"\n                    >\n                    </el-date-picker>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row :gutter=\"20\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"备注\" prop=\"bz\">\n                    <el-input\n                      v-model=\"sbxxForm.bz\"\n                      type=\"textarea\"\n                      rows=\"2\"\n                      :placeholder=\"assetIsDisable ? '' : '请输入备注'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n            </el-card>\n            <el-card class=\"box-cont\">\n              <div slot=\"header\" class=\"clearfix\">\n                <span>技术参数</span>\n              </div>\n              <el-row :gutter=\"20\" class=\"cont_top\">\n                <!--  一次设备 改为都要 -->\n                <el-col :span=\"8\">\n                  <el-form-item label=\"额定电压\" prop=\"eddy\">\n                    <el-input\n                      v-model=\"sbxxForm.eddy\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写额定电压'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"额定频率\" prop=\"edpl\">\n                    <el-input\n                      v-model=\"sbxxForm.edpl\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写额定频率'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"额定电流\" prop=\"eddl\">\n                    <el-input\n                      v-model=\"sbxxForm.eddl\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写额定电流'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"额定容量\" prop=\"edrl\">\n                    <el-input\n                      v-model=\"sbxxForm.edrl\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写额定容量'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"联结组标号\" prop=\"ljzbh\">\n                    <el-input\n                      v-model=\"sbxxForm.ljzbh\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写联结组标号'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"器身质量\" prop=\"qszl\">\n                    <el-input\n                      v-model=\"sbxxForm.qszl\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写器身质量'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"油质量\" prop=\"yzl\">\n                    <el-input\n                      v-model=\"sbxxForm.yzl\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写油质量'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"档位个数\" prop=\"dwgs\">\n                    <el-input\n                      v-model=\"sbxxForm.dwgs\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写档位个数'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"空载损耗\" prop=\"kzsh\">\n                    <el-input\n                      v-model=\"sbxxForm.kzsh\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写空载损耗'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"空载电流\" prop=\"kzdl\">\n                    <el-input\n                      v-model=\"sbxxForm.kzdl\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写空载电流'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"负载损耗\" prop=\"fzsh\">\n                    <el-input\n                      v-model=\"sbxxForm.fzsh\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写负载损耗'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"冷却方式\" prop=\"lqfs\">\n                    <el-input\n                      v-model=\"sbxxForm.lqfs\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写冷却方式'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"短路阻抗\" prop=\"dlkz\">\n                    <el-input\n                      v-model=\"sbxxForm.dlkz\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"变比/精度1\" prop=\"jddj1\">\n                    <el-input\n                      v-model=\"sbxxForm.jddj1\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写变比/精度'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"变比/精度2\" prop=\"jddj2\">\n                    <el-input\n                      v-model=\"sbxxForm.jddj2\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写变比/精度'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"变比/精度3\" prop=\"jddj3\">\n                    <el-input\n                      v-model=\"sbxxForm.jddj3\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写变比/精度'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"变比/精度4\" prop=\"jddj4\">\n                    <el-input\n                      v-model=\"sbxxForm.jddj4\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写变比/精度'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <!--  二次设备 -->\n                <el-col v-if=\"jgQueryParams.jgdl == 'ec'\" :span=\"8\">\n                  <el-form-item label=\"版本号\" prop=\"bbh\">\n                    <el-input\n                      v-model=\"sbxxForm.bbh\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写版本号'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n            </el-card>\n          </el-form>\n        </el-tab-pane>\n        <el-tab-pane label=\"设备履历\" name=\"sbRecord\" v-if=\"showFsss\">\n          <el-tabs\n            v-model=\"sbllDescTabName\"\n            @tab-click=\"handleSbllDescTabNameClick\"\n            type=\"card\"\n          >\n            <el-tab-pane label=\"状态变更\" name=\"ztbgjl\">\n              <comp-table\n                :table-and-page-info=\"resumPageInfo\"\n                @getMethod=\"getResumList\"\n                @update:multipleSelection=\"handleSelectionChange\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"隐患\" name=\"yh\">\n              <comp-table\n                :table-and-page-info=\"yhPageInfo\"\n                @getMethod=\"getYhList\"\n                v-loading=\"yhloading\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"试验\" name=\"sy\">\n              <comp-table\n                :table-and-page-info=\"syPageInfo\"\n                @getMethod=\"getSyList\"\n                v-loading=\"syloading\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"不良工况\" name=\"blgk\">\n              <comp-table\n                :table-and-page-info=\"blgkPageInfo\"\n                @getMethod=\"getBlgkList\"\n                v-loading=\"blgkloading\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"其它设备问题\" name=\"qtsbwt\">\n              <comp-table\n                :table-and-page-info=\"qtsbwtPageInfo\"\n                @getMethod=\"getqtsbwtList\"\n                v-loading=\"qtsbwtloading\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"设备检修\" name=\"jx\">\n              <comp-table\n                :table-and-page-info=\"jxPageInfo\"\n                @getMethod=\"getJxList\"\n                v-loading=\"jxloading\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"继电保护\" name=\"jdbh\">\n              <comp-table\n                :table-and-page-info=\"jdbhPageInfo\"\n                @getMethod=\"getJdbhList\"\n                v-loading=\"jdbhLoading\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"跳闸记录\" name=\"tzjl\">\n              <comp-table\n                :table-and-page-info=\"tzjlPageInfo\"\n                @getMethod=\"getTzjlList\"\n                v-loading=\"tzjlLoading\"\n              />\n            </el-tab-pane>\n          </el-tabs>\n        </el-tab-pane>\n        <el-tab-pane label=\"附属设施\" name=\"fsss\" v-if=\"showFsss\">\n          <Fsss\n            v-if=\"znsbDialogForm\"\n            :sb-info=\"this.sbxxForm\"\n            :can-edit=\"!this.assetIsDisable\"\n          ></Fsss>\n        </el-tab-pane>\n      </el-tabs>\n      <div slot=\"footer\" class=\"dialog-footer\" v-show=\"!assetIsDisable\">\n        <el-button @click=\"resetForm1\">取 消</el-button>\n        <el-button\n          v-show=\"activeTabName === 'sbDesc'\"\n          type=\"primary\"\n          @click=\"submit\"\n          :loading=\"assetSubmitLoading\"\n          class=\"pmyBtn\"\n          >确 定</el-button\n        >\n      </div>\n    </el-dialog>\n    <!--变电站所用弹出框结束-->\n\n    <!--间隔详情弹出框展示开始-->\n    <el-dialog\n      title=\"间隔信息\"\n      :visible.sync=\"jgDialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"60%\"\n      v-dialogDrag\n      :before-close=\"resetForm\"\n      @open=\"fillBdz\"\n    >\n      <el-form\n        ref=\"jgxxForm\"\n        :model=\"jgxxForm\"\n        :rules=\"rules\"\n        label-width=\"130px\"\n      >\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属电站\" prop=\"ssbdz\">\n              <el-select\n                v-model=\"jgxxForm.ssbdz\"\n                placeholder=\"请选择所属电站\"\n                filterable\n                :disabled=\"jgShow\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in bdzOptionsDataList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"间隔名称\" prop=\"jgmc\">\n              <el-input\n                v-model=\"jgxxForm.jgmc\"\n                placeholder=\"请输入间隔名称\"\n                :disabled=\"jgShow\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"间隔类型\" prop=\"jglx\">\n              <el-select\n                v-model=\"jgxxForm.jglx\"\n                placeholder=\"请选择间隔类型\"\n                filterable\n                :disabled=\"jgShow\"\n              >\n                <el-option\n                  v-for=\"item in jglxOptionsDataList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电压等级\" prop=\"dydjbm\">\n              <el-select\n                v-model=\"jgxxForm.dydjbm\"\n                :disabled=\"jgShow\"\n                placeholder=\"请选择电压等级\"\n              >\n                <el-option\n                  v-for=\"item in jgDydOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"投运日期\" class=\"add_sy_tyrq\" prop=\"tyrq\">\n              <el-date-picker\n                v-model=\"jgxxForm.tyrq\"\n                type=\"date\"\n                placeholder=\"选择日期\"\n                format=\"yyyy-MM-dd\"\n                value-format=\"yyyy-MM-dd\"\n                :disabled=\"jgShow\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\" prop=\"bz\">\n              <el-input\n                v-model=\"jgxxForm.bz\"\n                :disabled=\"jgShow\"\n                type=\"textarea\"\n                rows=\"2\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\" v-show=\"!jgShow\">\n        <el-button @click=\"resetForm\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"addJg\" class=\"pmyBtn\"\n          >确 定</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <!-- 设备数据copy -->\n    <el-dialog\n      title=\"设备复制\"\n      :visible.sync=\"isShowCopy\"\n      width=\"50%\"\n      v-dialogDrag\n    >\n      <el-form\n        label-width=\"130px\"\n        ref=\"formCopy\"\n        :model=\"formCopy\"\n        :rules=\"rulesCopy\"\n      >\n        <div>\n          <!--基本信息-->\n          <el-row>\n            <el-col :span=\"12\">\n              <el-form-item label=\"分公司：\" prop=\"ssdwbm\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formCopy.ssdwbm\"\n                  @change=\"handleFgsChange\"\n                  placeholder=\"请选择分公司\"\n                >\n                  <el-option\n                    v-for=\"item in OrganizationSelectedList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item label=\"变电站\" prop=\"bdz\">\n                <el-select\n                  style=\"width:100%\"\n                  v-model=\"formCopy.bdz\"\n                  @change=\"getSbDataListGroup\"\n                  placeholder=\"请选择变电站\"\n                  filterable\n                >\n                  <el-option\n                    v-for=\"item in wzDataListOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item label=\"所属间隔\" prop=\"ssjg\">\n                <el-select\n                  style=\"width:100%\"\n                  v-model=\"formCopy.ssjg\"\n                  placeholder=\"请选择所属间隔\"\n                  filterable\n                >\n                  <el-option\n                    v-for=\"(item, i) in sbDataList\"\n                    :key=\"item.i\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <p>\n            <span style=\"color: red;font-size:23px\">*</span>\n            <span style=\"color: red;font-size:23px\"\n              >功能描述:将该列表中的所有设备 复制一份到 表单选择的间隔下</span\n            >\n          </p>\n          <div align=\"right\" slot=\"footer\">\n            <el-button @click=\"isShowCopy = false\">取 消</el-button>\n            <el-button type=\"primary\" @click=\"copyForAsset\">复 制 </el-button>\n          </div>\n        </div>\n      </el-form>\n    </el-dialog>\n    <import-file\n      ref=\"importExcel\"\n      :export-url=\"importExcelUrl\"\n      :params=\"importExtraInfo\"\n      :valid-string=\"fileName\"\n      @getDataAfterUploading=\"getUploadData\"\n    ></import-file>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport {\n  addAsset,\n  addBdz,\n  addJg,\n  getFgsByBdzId,\n  getJgInfoList,\n  getNewTreeInfo,\n  getOrganizationSelected,\n  getTreeInfo,\n  removeAsset,\n  removeBdz,\n  removeJg,\n  adddwzyfstz,\n  exportExcel,\n  copyAsset\n} from \"@/api/dagangOilfield/asset/jgtz\";\nimport {\n  getBdAsesetListPage,\n  getBdzDataListSelected,\n  getJgDataListSelected,\n  getSbxhList,\n  getSblxDataListSelected,\n  addSbxh\n} from \"@/api/dagangOilfield/asset/bdsbtz\";\nimport { getBdzList } from \"@/api/dagangOilfield/asset/bdztz\";\nimport { deleteById, getListByBusinessId } from \"@/api/tool/file\";\nimport {\n  getParamDataList,\n  getParamsValue\n} from \"@/api/dagangOilfield/asset/parameters\";\nimport { getResumDataList } from \"@/api/dagangOilfield/asset/sdsb\";\nimport { getUsers } from \"@/api/system/usergroup\";\nimport { getDictTypeData } from \"@/api/system/dict/data\";\nimport Fsss from \"../bdgl/fsss.vue\";\nimport { getData, getQtwtlrData } from \"@/api/blgk/blgk\";\nimport { getSybgjlDataByPage } from \"@/api/dagangOilfield/bzgl/sybglr\";\nimport { getListFirst } from \"@/api/yxgl/bdyxgl/qxgl\";\nimport {\n  getFgsOptions,\n  getSelectOptionsByOrgType,\n  getListFour,\n  getListSecond,\n  getListSeven\n} from \"@/api/yxgl/bdyxgl/zbgl\";\nimport importFile from \"@/components/ExportExcel/importExcel\";\n\nexport default {\n  name: \"jgtz\",\n  components: { Fsss, importFile },\n  data() {\n    return {\n      wzDataListOptions: [],\n      sbDataList: [],\n      formCopy: {},\n      isShowCopy: false,\n      loading: false,\n      icons: {\n        bdzList: \"categoryTreeIcons\",\n        bdz: \"tableIcon\",\n        jg: \"classIcon\",\n        jgdl: \"classIcon\" //间隔大类\n      },\n\n      //新增设备时变电站下拉框\n      bdzOptionsDataList: [],\n      //间隔类型下拉数据\n      jglxOptionsDataList: [],\n      //树结构监听属性\n      filterText: \"\",\n      //组织结构下拉数据\n      OrganizationSelectedList: [],\n      //树结构上面得筛选框参数\n      treeForm: {},\n      //电压等级下拉框数据\n      VoltageLevelSelectedList: [],\n      //间隔特殊电压等级\n      jgDydOptions: [],\n      //带字母的电压等级\n      dydjOptionsWithString: [],\n      //新增设备时设备状态下拉框数据\n      sbztOptionsDataList: [],\n      //新增设备下拉框数据\n      placeOptions: [],\n      xsOptions: [],\n      xbOptions: [],\n      singleClickData: undefined,\n      //间隔信息是否显示\n      jgShow: false,\n      filterInfo: {},\n      //通用列表参数\n      tableAndPageInfo: {},\n      jgQueryParams: {\n        ssbdz: undefined,\n        dydj: undefined,\n        pageSize: 10,\n        pageNum: 1\n      },\n      jgxxForm: {\n        objId: undefined,\n        wzbm: undefined,\n        wzid: undefined,\n        sccj: undefined,\n        jgmc: undefined,\n        tyrq: undefined,\n        jglx: undefined,\n        dydj: undefined,\n        zt: undefined,\n        ccrq: undefined,\n        ggxh: undefined,\n        jd: undefined,\n        wd: undefined,\n        ssdd: undefined,\n        ssbdz: undefined,\n        bdzmc: undefined,\n        bz: undefined\n      },\n      //间隔展示\n      jgShowTable: false,\n      //间隔添加按钮弹出框\n      jgDialogFormVisible: false,\n      //弹出框表单\n      form: {},\n      pickerOptions: {\n        shortcuts: [\n          {\n            text: \"今天\",\n            onClick(picker) {\n              picker.$emit(\"pick\", new Date());\n            }\n          },\n          {\n            text: \"昨天\",\n            onClick(picker) {\n              const date = new Date();\n              date.setTime(date.getTime() - 3600 * 1000 * 24);\n              picker.$emit(\"pick\", date);\n            }\n          },\n          {\n            text: \"明天\",\n            onClick(picker) {\n              const date = new Date();\n              date.setTime(date.getTime() + 3600 * 1000 * 24);\n              picker.$emit(\"pick\", date);\n            }\n          }\n        ]\n      },\n      //组织树\n      treeOptions: [],\n      //删除是否可用\n      multipleSensor: true,\n      //查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        roleKey: \"\",\n        roleName: \"\",\n        status: \"\"\n      },\n      showSearch: true,\n      rules: {\n        // wzbm:[{  required: true, message: '请填写位置编码', trigger: 'blur' }],\n        // wzid:[{required:true,message:'请填写位置id',trigger:'blur'}],\n        jgmc: [{ required: true, message: \"请填写间隔名称\", trigger: \"blur\" }],\n        // zt:[{required:true,message:'请填写状态',trigger:'blur'}],\n        ssbdz: [\n          { required: true, message: \"请选择所属变电站\", trigger: \"change\" }\n        ],\n        dydj: [{ required: true, message: \"请选择电压等级\", trigger: \"change\" }]\n      },\n      //新\n      //控制变电站表格是否展示\n      bdzdataShow: true,\n      importExcelUrl: \"\",\n      fileName: \"\",\n      //控制变电站表格是否展示\n      jgdataShow: false,\n      //控制变电站表格是否展示\n      znsbdataShow: false,\n      bdzqueryParams: {\n        ssgs: undefined,\n        dydjbm: undefined,\n        ssbdz: undefined,\n        ssjg: undefined,\n        sbmc: undefined,\n        assetTypeCode: undefined,\n        sbzt: undefined,\n        pageNum: 1,\n        pageSize: 10\n      },\n      params: {\n        //bm:undefined,\n        // ssdwbm: undefined,\n        pageNum: 1,\n        pageSize: 10\n      },\n      filterInfo1: {\n        data: {\n          ywdwArr: [],\n          jhnyArr: [],\n          xlArr: \"\",\n          jhlxArr: [],\n          jhztArr: \"\",\n          sfdd: \"\"\n        },\n        fieldList: [\n          { label: \"变电站名称\", type: \"input\", value: \"bdzmc\" },\n          {\n            label: \"投运日期\",\n            type: \"date\",\n            value: \"tyrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          {\n            label: \"所属公司\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"ssdwbm\",\n            options: []\n          },\n          {\n            label: \"是否枢纽站\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"sfsnz\",\n            options: [\n              {\n                value: \"是\",\n                label: \"是\"\n              },\n              {\n                value: \"否\",\n                label: \"否\"\n              }\n            ]\n          },\n          {\n            label: \"设备状态\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"sbzt\",\n            options: []\n          },\n          {\n            label: \"电压等级\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"dydj\",\n            options: []\n          }\n        ]\n      },\n      //变电站table数据\n      tableAndPageInfo1: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n          pageResize: \"\"\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"ssdwmc\", label: \"所属公司\", minWidth: \"120\" },\n          { prop: \"bdzmc\", label: \"变电站名称\", minWidth: \"180\" },\n          { prop: \"dydj\", label: \"电压等级\", minWidth: \"80\" },\n          { prop: \"sbzt\", label: \"设备状态\", minWidth: \"80\" },\n          { prop: \"sfsnz\", label: \"是否枢纽站\", minWidth: \"140\" },\n          // {prop: 'sblx', label: '设备类型', minWidth: '120'},\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"120\" }\n          /* {\n             fixed: \"right\",\n             prop: 'operation',\n             label: '操作',\n             minWidth: '130px',\n             style: {display: 'block'},\n             operation: [\n               {name: '修改', clickFun: this.updatebdz},\n               {name: '详情', clickFun: this.bdzDetails},\n             ]\n           },*/\n        ]\n        /* [\n         {prop: 'jgmc', label: '间隔名称', minWidth: '120'},\n         {prop: 'jglx', label: '间隔类型', minWidth: '120'},\n         {prop: 'dydj', label: '电压等级', minWidth: '120'},\n         {prop: 'tyrq', label: '投运日期', minWidth: '120'},\n\n         // {prop: 'zt', label: '状态', minWidth: '120'},\n         // {prop: 'jd', label: '经度', minWidth: '140'},\n         // {prop: 'wd', label: '纬度', minWidth: '120'},\n         // {prop: 'ssdd', label: '所属调度', minWidth: '120'},\n         {\n           fixed: \"right\",\n           prop: 'operation',\n           label: '操作',\n           minWidth: '130px',\n           style: {display: 'block'},\n           operation: [\n             {name: '修改', clickFun: this.updateJg},\n             {name: '详情', clickFun: this.jgDetails},\n           ]\n         },\n       ]*/\n      },\n      filterInfo2: {\n        data: {\n          ywdwArr: [],\n          jhnyArr: [],\n          xlArr: \"\",\n          jhlxArr: [],\n          jhztArr: \"\",\n          sfdd: \"\"\n        },\n        fieldList: [\n          { label: \"间隔名称\", type: \"input\", value: \"jgmc\", options: [] },\n          { label: \"间隔类型\", type: \"input\", value: \"jglx\", options: [] },\n          {\n            label: \"电压等级\",\n            type: \"select\",\n            value: \"dydj\",\n            options: []\n          },\n          {\n            label: \"投运日期\",\n            type: \"date\",\n            value: \"tyrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          }\n        ]\n      },\n      //间隔数据\n      tableAndPageInfo2: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n          pageResize: \"\"\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"jgmc\", label: \"间隔名称\", minWidth: \"120\" },\n          { prop: \"jglx\", label: \"间隔类型\", minWidth: \"120\" },\n          { prop: \"dydj\", label: \"电压等级\", minWidth: \"120\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"120\" }\n          /*{\n            fixed: \"right\",\n            prop: 'operation',\n            label: '操作',\n            minWidth: '130px',\n            style: {display: 'block'},\n            operation: [\n              {name: '修改', clickFun: this.updateJg},\n              {name: '详情', clickFun: this.jgDetails},\n            ]\n          },*/\n        ]\n      },\n      filterInfo3: {\n        data: {\n          ywdwArr: [],\n          jhnyArr: [],\n          xlArr: \"\",\n          jhlxArr: [],\n          jhztArr: \"\",\n          sfdd: \"\"\n        },\n        fieldList: [\n          { label: \"设备类型\", type: \"input\", value: \"sblxmc\", options: [] },\n          { label: \"设备名称\", type: \"input\", value: \"sbmc\", options: [] },\n          {\n            label: \"电压等级\",\n            type: \"select\",\n            value: \"dydjName\",\n            options: []\n          },\n          {\n            label: \"设备状态\",\n            type: \"select\",\n            value: \"sbzt\",\n            options: []\n          },\n          {\n            label: \"投运日期\",\n            type: \"date\",\n            value: \"tyrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"型号\", type: \"input\", value: \"ggxh\", options: [] },\n          { label: \"额定电压\", type: \"input\", value: \"eddy\", options: [] },\n          { label: \"额定电流\", type: \"input\", value: \"eddl\", options: [] },\n          { label: \"额定频率\", type: \"input\", value: \"edpl\", options: [] },\n          { label: \"生产厂家\", type: \"input\", value: \"sccj\", options: [] }\n        ]\n      },\n      //站内设备台账\n      tableAndPageInfo3: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n          pageResize: \"\"\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"deptname\", label: \"所属公司\", minWidth: \"120\" },\n          { prop: \"bdzmc\", label: \"变电站名称\", minWidth: \"180\" },\n          { prop: \"wzmc\", label: \"所属间隔\", minWidth: \"180\" },\n          { prop: \"sblxmc\", label: \"设备类型\", minWidth: \"180\" },\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"180\" },\n          { prop: \"dydjName\", label: \"电压等级\", minWidth: \"80\" },\n          { prop: \"sbzt\", label: \"设备状态\", minWidth: \"80\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"120\" },\n          { prop: \"ggxh\", label: \"型号\", minWidth: \"120\" },\n          { prop: \"eddy\", label: \"额定电压\", minWidth: \"120\" },\n          { prop: \"eddl\", label: \"额定电流\", minWidth: \"120\" },\n          { prop: \"edpl\", label: \"额定频率\", minWidth: \"120\" },\n          { prop: \"sccj\", label: \"生产厂家\", minWidth: \"120\" }\n          /*{\n            fixed: \"right\",\n            prop: 'operation',\n            label: '操作',\n            minWidth: '120px',\n            style: {display: 'block'},\n            operation: [\n              /!*{name: \"状态变更\", clickFun: this.updateStatus},\n              {name: \"流程查看\", clickFun: this.ztbglcSay},*!/\n              {name: '修改', clickFun: this.updateAsset},\n              {name: '详情', clickFun: this.assetDetails},\n            ]\n          },*/\n        ]\n      },\n      //设备基本信息\n      jbxxForm: {\n        attachment: [],\n        objId: undefined,\n        ssdwmc: undefined,\n        ssdwbm: undefined,\n        sbdm: undefined,\n        ddsbh: undefined,\n        dydj: undefined,\n        tyrq: undefined,\n        jgdy: undefined,\n        xb: undefined,\n        xs: undefined,\n        ccrq: undefined,\n        azwz: undefined,\n        yt: undefined,\n        fzr: undefined,\n        cpdh: undefined,\n        eddy: undefined,\n        edpl: undefined,\n        sbzt: undefined,\n        syhj: undefined,\n        sccj: undefined,\n        zzgj: undefined,\n        zhsblx: undefined,\n        zhsblxmc: undefined,\n        eddl: undefined,\n        yxbh: undefined,\n        ccbh: undefined,\n        bdzmc: undefined,\n        bdzszbh: undefined, //变电站数字编号\n        ssdw: undefined, //所属电网\n        dzlx: undefined, //电站类型\n        sfzhzdh: undefined, //是否综合自动化站\n        sfszhbdz: undefined, //是否数字化变电站\n        returnDate: undefined, //退运日期\n        zymj: undefined, //占地面积\n        whdj: undefined, //污秽等级\n        zbfs: undefined, //值班方式\n        sfgqtx: undefined, //是否光纤通讯\n        hb: undefined, //海拔\n        gcbh: undefined, //工程编号\n        sjdw: undefined, //设计单位\n        jldw: undefined, //监理单位\n        zyjb: undefined, // 电站重要级别\n        bzfs: undefined, //布置方式\n        bdzdz: undefined, //变电站地址\n        jzmj: undefined, // 建筑面积\n        phone: undefined, //联系电话\n        gcmc: undefined, // 工程名称\n        sgdw: undefined, //施工单位\n        dqtz: undefined, //地区特征\n        zgddgxq: undefined, // 最高调度管辖权\n        sfmzn: undefined, // 是否满足n-1\n        sfjrgzxt: undefined, //是否接入故障信息远传系统\n        sfjravc: undefined, //是否接入avc\n        sfjzjk: undefined, //是否集中监控\n        jkzxmc: undefined, //接入得监控中心\n        bz: undefined //备注\n      },\n      //变电站添加按钮弹出框\n      bdzDialogFormVisible: false,\n      //标题\n      title: \"\",\n      imgList: [],\n      currUser: \"\",\n      //变电站信息是否可编辑\n      isDisabled: false,\n      //上传图片时的请求头\n      header: {},\n      //所属基地站\n      ssjdzList: [],\n      uploadData: {\n        type: \"\",\n        businessId: undefined\n      },\n      importExtraInfo: {},\n      //变电站新增弹框\n      bdzDidalogForm: false,\n      paramQuery: {\n        sblxbm: undefined\n      },\n      resumeQuery: {\n        foreignNum: undefined,\n        sblx: undefined\n      },\n      znsbDialogForm: false,\n      assetSubmitLoading: false,\n      //设备表单\n      sbxxForm: {},\n      //设备信息展示\n      assetIsDisable: false,\n      //弹出框tab页\n      activeTabName: \"sbDesc\",\n      //查询间隔下拉框数据的参数\n      selectJgOptionsParam: {},\n      //新增设备时所属间隔下拉框列表\n      jgOptionsDataList: [],\n      //设备类型下拉框数据\n      sblxOptionsDataSelected: [],\n      //技术参数绑定\n      jscsForm: {},\n      //技术参数动态展示集合\n      jscsLabelList: [],\n      //设备履历tab页\n      sbllDescTabName: \"ztbgjl\",\n      //设备履历试验记录数据\n      sblvsyjlList: [],\n      //设备履历缺陷记录数据集合\n      sbllqxjlList: [],\n      resumPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: false,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"foreignNum\", label: \"设备名称\", minWidth: \"120\" },\n          // { prop: \"sblx\", label: \"设备类型\", minWidth: \"180\" },\n          { prop: \"bglx\", label: \"变更类型\", minWidth: \"120\" },\n          { prop: \"ms\", label: \"描述\", minWidth: \"250\" },\n          { prop: \"bgr\", label: \"变更人\", minWidth: \"140\" },\n          { prop: \"bgsj\", label: \"变更时间\", minWidth: \"140\" }\n        ]\n      },\n      //设备附属设施list\n      fsssList: [],\n      //设备详情校验规则\n      sbxxRules: {\n        ssgs: [\n          { required: true, message: \"所属公司不能为空\", trigger: \"select\" }\n        ],\n        ssbdz: [\n          { required: true, message: \"所属电站不能为空\", trigger: \"select\" }\n        ],\n        ssjg: [\n          { required: true, message: \"所属间隔不能为空\", trigger: \"select\" }\n        ],\n        sbmc: [\n          { required: true, message: \"设备名称不能为空\", trigger: \"blur\" }\n        ],\n        assetTypeCode: [\n          { required: true, message: \"设备类型不能为空\", trigger: \"select\" }\n        ],\n        // dydjbm: [\n        //   {required: true, message: \"电压等级不能为空\", trigger: \"select\"},\n        // ],\n        sbzt: [\n          { required: true, message: \"设备状态不能为空\", trigger: \"select\" }\n        ],\n        // azwz: [\n        //   {required: true, message: \"安装位置不能为空\", trigger: \"blur\"},\n        // ],\n        // xh: [\n        //   {required: true, message: \"型号不能为空\", trigger: \"select\"},\n        // ],\n        // eddy: [\n        //   {required: true, message: \"额定电压不能为空\", trigger: \"blur\"},\n        // ],\n        // edpl: [\n        //   {required: true, message: \"额定频率不能为空\", trigger: \"blur\"},\n        // ],\n        syhj: [\n          { required: true, message: \"使用环境不能为空\", trigger: \"blur\" }\n        ],\n        sccj: [\n          { required: true, message: \"生产厂家不能为空\", trigger: \"blur\" }\n        ],\n        // yxbh: [\n        //   {required: true, message: \"运行编号不能为空\", trigger: \"blur\"},\n        // ],\n        // ccbh: [\n        //   {required: true, message: \"出厂编号不能为空\", trigger: \"blur\"},\n        // ],\n        // ccrq: [\n        //   {required: true, message: \"出厂日期不能为空\", trigger: \"change\"},\n        // ],\n        tyrq: [\n          { required: true, message: \"投运日期不能为空\", trigger: \"change\" }\n        ]\n      },\n      rulesCopy: {\n        ssdwbm: [\n          { required: true, message: \"所属公司不能为空\", trigger: \"select\" }\n        ],\n        bdz: [\n          { required: true, message: \"所属电站不能为空\", trigger: \"select\" }\n        ],\n        ssjg: [\n          { required: true, message: \"所属间隔不能为空\", trigger: \"select\" }\n        ]\n      },\n      ggxhList: [], //型号list\n      jglxcx: \"\", //所属间隔类型\n      ssbdz: \"\", //所属变电站,\n      ssbdzmc: \"\",\n      ssjg: \"\", //所属间隔\n      znsbParams: {\n        //站内设备分页参数\n        pageSize: 10,\n        pageNum: 1\n      },\n      fgsArr: [],\n      jgdlMap: new Map(), //存间隔大类数据\n      currentBdz: \"\", //当前变电站\n      showFsss: false, //是否显示附属设施tab页\n      bdsbid: \"\", //变电设备ID\n      sbmc: \"\", //设备名称\n      //不良工况列表\n      blgkPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: { checkBox: false, serialNumber: true },\n        tableData: [],\n        tableHeader: [\n          // { prop: \"fgsmc\", label: \"分公司\", minWidth: \"140\" },\n          // { prop: \"bdzmc\", label: \"变电站\", minWidth: \"140\" },\n          // { prop: \"jgmc\", label: \"间隔名称\", minWidth: \"100\" },\n          // { prop: \"sblxmc\", label: \"设备类型\", minWidth: \"140\" },\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"140\" },\n          { prop: \"ms\", label: \"不良工况描述\", minWidth: \"160\", showPop: true },\n          { prop: \"flyjCn\", label: \"分类依据\", minWidth: \"160\", showPop: true },\n          { prop: \"lrrmc\", label: \"录入人\", minWidth: \"100\" },\n          { prop: \"fxsj\", label: \"发现时间\", minWidth: \"100\" },\n          { prop: \"xcsj\", label: \"消除时间\", minWidth: \"140\" }\n        ]\n      },\n\n      //其它设备问题\n      qtsbwtPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: { checkBox: false, serialNumber: true },\n        tableData: [],\n        tableHeader: [\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"140\" },\n          { prop: \"ms\", label: \"问题描述\", minWidth: \"160\", showPop: true },\n          { prop: \"lrrmc\", label: \"录入人\", minWidth: \"100\" },\n          { prop: \"fxsj\", label: \"发现时间\", minWidth: \"100\" }\n        ]\n      },\n\n      //试验列表\n      syPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 0]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"试验专业\", prop: \"syzy\" },\n          { label: \"试验性质\", prop: \"syxz\" },\n          { label: \"试验名称\", prop: \"symc\", minWidth: \"200\", showPop: true },\n          // { label: \"设备地点\", prop: \"sydd\", minWidth: \"120\" },\n          { label: \"试验设备\", prop: \"sbmc\", minWidth: \"100\", showPop: true },\n          { label: \"试验模板名称\", prop: \"symb\", minWidth: \"120\" },\n          { label: \"天气\", prop: \"tq\" },\n          { label: \"试验日期\", prop: \"syrq\" },\n          { label: \"试验人员\", prop: \"syryid\" },\n          { label: \"流程状态\", prop: \"ztmc\" }\n        ],\n        option: { checkBox: false, serialNumber: true }\n      },\n      //隐患列表\n      yhPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          pageResize: \"\",\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: false,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          // { prop: \"ssgs\", label: \"所属公司\", minWidth: \"120\" },\n          // { prop: \"ssdz\", label: \"所属位置\", minWidth: \"120\" },\n          { prop: \"sb\", label: \"主设备\", minWidth: \"120\" },\n          // { prop: \"sblx\", label: \"设备类型\", minWidth: \"100\" },\n          // {prop: 'dydj', label: '电压等级', minWidth: '100'},\n          { prop: \"sbxh\", label: \"设备型号\", minWidth: \"120\" },\n          { prop: \"sccj\", label: \"生产厂家\", minWidth: \"120\" },\n          { prop: \"bzqxQxdj\", label: \"隐患性质\", minWidth: \"120\" },\n          { prop: \"qxnr\", label: \"隐患内容\", minWidth: \"120\", showPop: true },\n          { prop: \"jxlbCn\", label: \"是否触发状态评价\", minWidth: \"80\" },\n          { prop: \"ztmc\", label: \"状态\", minWidth: \"120\" },\n          { prop: \"fxrq\", label: \"发现时间\", minWidth: \"100\", custom: true }\n        ]\n      },\n      //检修列表\n      jxPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          pageResize: \"\",\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: false,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          // { prop: \"fgsmc\", label: \"分公司\", minWidth: \"150\" },\n          // { prop: \"bdzmc\", label: \"变电站\", minWidth: \"150\" },\n          // { prop: \"ssjg\", label: \"所属间隔\", minWidth: \"150\" },\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"100\" },\n          { prop: \"rq\", label: \"日期\", minWidth: \"120\" },\n          { prop: \"xslb\", label: \"修试类别\", minWidth: \"100\" },\n          { prop: \"nr\", label: \"内容\", minWidth: \"100\" },\n          { prop: \"jl\", label: \"结论\", minWidth: \"80\" },\n          { prop: \"xsfzr\", label: \"修试负责人\", minWidth: \"90\" },\n          { prop: \"ysfzr\", label: \"验收负责人\", minWidth: \"90\" },\n          { prop: \"jlr\", label: \"记录人\", minWidth: \"90\" }\n        ]\n      },\n      //继电保护列表\n      jdbhPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          pageResize: \"\",\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: false,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"rq\", label: \"日期\", minWidth: \"80\" },\n          { prop: \"nr\", label: \"内容\", minWidth: \"180\" },\n          { prop: \"jl\", label: \"结论\", minWidth: \"80\" },\n          { prop: \"sygzfzr\", label: \"试验工作负责人\", minWidth: \"110\" },\n          { prop: \"ysfzr\", label: \"验收负责人\", minWidth: \"100\" },\n          { prop: \"jlr\", label: \"记录人\", minWidth: \"100\" }\n        ]\n      },\n      //跳闸记录列表\n      tzjlPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          pageResize: \"\",\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: false,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"rqsj\", label: \"日期时间\", minWidth: \"160\" },\n          {\n            prop: \"bhdzqk\",\n            label: \"保护动作情况\",\n            minWidth: \"180\",\n            isShowProp: true\n          },\n          { prop: \"dlqjcqk\", label: \"断路器检查情况\", minWidth: \"120\" },\n          { prop: \"gzdl\", label: \"故障电流\", minWidth: \"100\" },\n          { prop: \"gztzcs\", label: \"故障跳闸次数\", minWidth: \"100\" },\n          { prop: \"jltzjl\", label: \"累计跳闸次数\", minWidth: \"100\" },\n          { prop: \"zhdxrq\", label: \"最后大修日期\", minWidth: \"120\" },\n          { prop: \"jlr\", label: \"记录人\", minWidth: \"120\" }\n        ]\n      },\n      yhloading: false,\n      syloading: false,\n      blgkloading: false,\n      qtsbwtloading: false,\n      jxloading: false,\n      jdbhLoading: false,\n      tzjlLoading: false,\n      isSelectingFromDropdown: false, // 添加标记变量\n    };\n  },\n  watch: {\n    //监听筛选框值发生变化进而筛选树结构\n    filterText(val) {\n      this.$refs.tree.filter(val);\n    }\n  },\n  async created() {\n    //获取组织结构下拉数据\n    this.getFgsOptions();\n    this.getJdzOptions();\n    //获取变电站下拉框数据\n    this.getBdzDataListSelected();\n    //获取选择框数据\n    this.getSelectDataInfo();\n    //获取新的设备拓扑树\n    await this.getNewTreeInfo();\n    this.isShow1 = true;\n    //初始化加载时加载所有变电站信息\n    await this.getData();\n    await this.getJglxList();\n    //初始化时加载页面内容\n    this.tableAndPageInfo = { ...this.tableAndPageInfo1 };\n  },\n  mounted() {\n    //获取token\n    this.header.token = getToken();\n    this.currUser = this.$store.getters.name;\n  },\n  methods: {\n    //所属公司change事件\n    handleFgsChange(fgsValue) {\n      //清空之前得选中值\n      this.wzDataListOptions = [];\n      this.$set(this.formCopy, \"bdz\", \"\");\n      //获取变电站方法\n      getBdzDataListSelected({ ssdwbm: fgsValue }).then(res => {\n        this.wzDataListOptions = res.data;\n      });\n    },\n    async getSbDataListGroup(val) {\n      this.$set(this.formCopy, \"ssjg\", \"\");\n      let res = await getJgDataListSelected({ ssbdz: val + \"\" });\n      if (res.code === \"0000\") {\n        this.sbDataList = res.data;\n      } else {\n        this.$message({\n          type: \"error\",\n          message: \"间隔数据获取失败!\"\n        });\n      }\n    },\n    xsChangeFunc(val) {\n      if (val === \"三相\") {\n        this.$set(this.sbxxForm, \"xb\", \"ABC\");\n      } else {\n        this.$set(this.sbxxForm, \"xb\", \"\");\n      }\n    },\n    /**\n     * 获取基地站下拉数据\n     */\n    getJdzOptions() {\n      getSelectOptionsByOrgType(JSON.stringify(\"07\")).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.ssjdzList = res.data;\n      });\n    },\n    //查询检修数据\n    getJxList(params) {\n      this.jxloading = true;\n      const param = {\n        ...{ pageSize: 10, pageNum: 1 },\n        ...params,\n        ...{ sbmc: this.sbmc, bdz: this.ssbdz },\n        ...{ mySorts: [{ prop: \"rq\", asc: false }] }\n      };\n      getListFour(param).then(res => {\n        this.jxPageInfo.tableData = res.data.records;\n        this.jxPageInfo.pager.total = res.data.total;\n        this.jxloading = false;\n      });\n    },\n    getJdbhList(params) {\n      this.jdbhLoading = true;\n      const param = {\n        ...{ pageSize: 10, pageNum: 1 },\n        ...params,\n        ...{ sbmc: this.sbmc, bdz: this.ssbdz },\n        ...{ mySorts: [{ prop: \"rq\", asc: false }] }\n      };\n      getListSecond(param).then(res => {\n        this.jdbhPageInfo.tableData = res.data.records;\n        this.jdbhPageInfo.pager.total = res.data.total;\n        this.jdbhLoading = false;\n      });\n    },\n    getTzjlList(params) {\n      this.tzjlLoading = true;\n      const param = {\n        ...{ pageSize: 10, pageNum: 1 },\n        ...params,\n        ...{ dlqbh: this.sbmc, bdz: this.ssbdz },\n        ...{ mySorts: [{ prop: \"rqsj\", asc: false }] }\n      };\n      getListSeven(param).then(res => {\n        this.tzjlPageInfo.tableData = res.data.records;\n        this.tzjlPageInfo.pager.total = res.data.total;\n        this.tzjlLoading = false;\n      });\n    },\n    //查询不良工况数据\n    getBlgkList(params) {\n      this.blgkloading = true;\n      const param = {\n        ...{ pageSize: 10, pageNum: 1 },\n        ...params,\n        ...{ sbid: this.bdsbid }\n      };\n      getData(param).then(res => {\n        this.blgkPageInfo.tableData = res.data.records;\n        this.blgkPageInfo.pager.total = res.data.total;\n        this.blgkloading = false;\n      });\n    },\n\n    //其它设备录入问题\n    getqtsbwtList(params) {\n      this.qtsbwtloading = true;\n      const param = {\n        ...{ pageSize: 10, pageNum: 1 },\n        ...params,\n        ...{ sbid: this.bdsbid }\n      };\n      getQtwtlrData(param).then(res => {\n        this.qtsbwtPageInfo.tableData = res.data.records;\n        this.qtsbwtPageInfo.pager.total = res.data.total;\n        this.qtsbwtloading = false;\n      });\n    },\n\n    //试验数据\n    getSyList(params) {\n      this.syloading = true;\n      const param = {\n        ...{ pageSize: 10, pageNum: 1 },\n        ...params,\n        ...{ sysbid: this.bdsbid }\n      };\n      getSybgjlDataByPage(param).then(res => {\n        if (res.code === \"0000\") {\n          this.syPageInfo.tableData = res.data.records;\n          this.syPageInfo.pager.total = res.data.total;\n        }\n        this.syloading = false;\n      });\n    },\n    //隐患数据\n    getYhList(params) {\n      this.yhloading = true;\n      const param = {\n        ...{ pageSize: 10, pageNum: 1 },\n        ...params,\n        ...{ sbid: this.bdsbid }\n      };\n      getListFirst(param).then(res => {\n        if (res.code === \"0000\") {\n          this.yhPageInfo.tableData = res.data.records;\n          this.yhPageInfo.pager.total = res.data.total;\n        }\n        this.yhloading = false;\n      });\n    },\n    //获取间隔类型字典\n    getJglxList() {\n      getDictTypeData(\"dwzy_jglx\").then(res => {\n        this.jglxOptionsDataList = res.data;\n        res.data.forEach(item => {\n          if (!this.jgdlMap.has(item.value)) {\n            this.jgdlMap.set(item.value, item.remark);\n          }\n        });\n      });\n    },\n    getSelectDataInfo() {\n      //110 不带字母\n      getDictTypeData(\"dg_dydj\").then(res => {\n        this.VoltageLevelSelectedList = res.data;\n      });\n      //间隔特殊等级\n      getDictTypeData(\"jgtz_gjDydj\").then(res => {\n        this.jgDydOptions = res.data;\n        this.filterInfo2.fieldList.map(item => {\n          if (item.value == \"dydj\") {\n            return (item.options = this.jgDydOptions);\n          }\n        });\n      });\n      // 110kV 带字母\n      getDictTypeData(\"gttz-dydj\").then(res => {\n        this.dydjOptionsWithString = res.data;\n        this.filterInfo1.fieldList.map(item => {\n          if (item.value == \"dydj\") {\n            return (item.options = this.dydjOptionsWithString);\n          }\n        });\n\n        this.filterInfo3.fieldList.map(item => {\n          if (item.value == \"dydjName\") {\n            return (item.options = this.dydjOptionsWithString);\n          }\n        });\n      });\n      //设备状态\n      getDictTypeData(\"jgtz_sbzt\").then(res => {\n        this.sbztOptionsDataList = res.data;\n        this.filterInfo1.fieldList.map(item => {\n          if (item.value == \"sbzt\") {\n            return (item.options = this.sbztOptionsDataList);\n          }\n        });\n        this.filterInfo3.fieldList.map(item => {\n          if (item.value == \"sbzt\") {\n            return (item.options = this.sbztOptionsDataList);\n          }\n        });\n      });\n      //安装位置\n      getDictTypeData(\"jgtz_azwz\").then(res => {\n        this.placeOptions = res.data;\n      });\n      //相别\n      getDictTypeData(\"jgtz_xb\").then(res => {\n        this.xbOptions = res.data;\n      });\n      //相数\n      getDictTypeData(\"jgtz_xs\").then(res => {\n        this.xsOptions = res.data;\n      });\n    },\n    // 新增信息所属电站要自动带入\n    fillBdz() {\n      this.$set(this.jgxxForm, \"ssbdz\", this.currentBdz);\n    },\n    async deleteFileById(id) {\n      let { code } = await deleteById(id);\n      if (code === \"0000\") {\n        await this.getFileList();\n        this.$message({\n          type: \"success\",\n          message: \"文件删除成功!\"\n        });\n      }\n    },\n    //树监听事件\n    filterNode(value, data) {\n      if (!value) return true;\n      return data.label.indexOf(value) !== -1;\n    },\n    //获取新的设备拓扑树\n    getNewTreeInfo() {\n      getUsers({ personGroupId: 76, deptId: 0, deptName: \"\" }).then(res => {\n        let deptId = this.$store.getters.deptId.toString();\n        res.data.forEach(item => {\n          if (item.userName === this.currUser) {\n            //如果人员组里面有需要排除的人，则不需要用deptId进行过滤\n            deptId = \"\";\n            return false;\n          }\n        });\n        if (this.fgsArr.includes(deptId)) {\n          this.treeForm.ssdwbm = deptId;\n        }\n        getNewTreeInfo(this.treeForm).then(res => {\n          this.treeOptions = res.data;\n        });\n      });\n    },\n    /**\n     * 获取分公司下拉数据\n     */\n    getFgsOptions() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n          this.fgsArr.push(item.value.toString());\n        });\n        this.OrganizationSelectedList = res.data;\n        this.filterInfo1.fieldList.map(item => {\n          if (item.value == \"ssdwbm\") {\n            return (item.options = this.OrganizationSelectedList);\n          }\n        });\n      });\n    },\n    //旧树形数据获取\n    getTreeInfoList() {\n      getTreeInfo().then(res => {\n        this.treeOptions = res.data;\n      });\n    },\n    //列表查询\n    async getData(param) {\n      this.loading = true;\n      this.params = param;\n      await getSblxDataListSelected({ type: \"变电设备\" }).then(res => {\n        this.sblxOptionsDataSelected = res.data;\n        this.loading = false;\n      });\n      //判断翻页是执行的哪个表格的数据\n      if (this.bdzdataShow) {\n        //初始进来请求变电站台账\n        await this.getbdzData(param);\n      }\n      if (this.jgdataShow) {\n        await this.getJgData(param);\n      }\n      if (this.znsbdataShow) {\n        await this.getZnsbData(param);\n      }\n    },\n    /**\n     * 表格多选框\n     */\n    handleSelectionChange(selection) {\n      // this.ids = selection.map(item => item.objId);\n      // this.single = selection.length !== 1;\n      this.singleClickData =\n        selection.length > 0 ? { ...selection[0] } : undefined;\n      // this.multiple = !selection.length;\n    },\n\n    //间隔添加按钮\n    jgAddjgButton() {\n      this.jgShow = false;\n      this.jgDialogFormVisible = true;\n    },\n    addJg() {\n      this.$refs[\"jgxxForm\"].validate(valid => {\n        if (valid) {\n          let jglx = this.jgxxForm.jglx;\n          if (jglx) {\n            //保存时设置间隔大类\n            this.jgxxForm.jgdl = this.jgdlMap.get(jglx);\n          }\n          addJg(this.jgxxForm).then(res => {\n            if (res.code == \"0000\") {\n              this.$message.success(\"操作成功！\");\n              this.jgDialogFormVisible = false;\n              // this.tableAndPageInfo1.pager.pageResize = \"Y\";\n              this.getJgData();\n              //获取新的设备拓扑树\n              this.getNewTreeInfo();\n            }\n          });\n        } else {\n          return false;\n        }\n      });\n    },\n    getJgListInfo() {\n      getJgInfoList(this.jgQueryParams).then(res => {\n        this.tableAndPageInfo1.tableData = res.data.records;\n        this.tableAndPageInfo1.pager.total = res.data.total;\n        // this.tableAndPageInfo = {...this.tableAndPageInfo2}\n      });\n    },\n    /**\n     * 删除间隔\n     */\n    removeAll(row) {\n      if (this.bdzdataShow) {\n        this.deleteBdz(row);\n      }\n      if (this.jgdataShow) {\n        this.removeAsset(row);\n      }\n      if (this.znsbdataShow) {\n        this.deleteJg(row);\n      }\n    },\n    removeAsset(row) {\n      this.form = row;\n\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          removeAsset([this.form.objId]).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getJgData();\n              //获取新的设备拓扑树\n              this.getNewTreeInfo();\n              // this.tableAndPageInfo3.pager.pageResize = \"Y\";\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    deleteBdz(row) {\n      this.form = row;\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(() => {\n        removeBdz([this.form.objId]).then(res => {\n          this.$message({\n            type: \"success\",\n            message: \"删除成功!\"\n          });\n          // this.tableAndPageInfo1.pager.pageResize = \"Y\";\n          this.getbdzData();\n        });\n      });\n    },\n    deleteJg(row) {\n      this.form = row;\n\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(() => {\n        removeJg([this.form.objId]).then(res => {\n          this.$message({\n            type: \"success\",\n            message: \"删除成功!\"\n          });\n          // this.tableAndPageInfo2.pager.pageResize = \"Y\";\n          this.getZnsbData();\n        });\n      });\n    },\n    async updateJg(row) {\n      this.jgDialogFormVisible = true;\n      this.jgxxForm = { ...row };\n      this.jgShow = false;\n    },\n\n    async jgDetails(row) {\n      this.jgDialogFormVisible = true;\n      this.jgxxForm = { ...row };\n      this.jgShow = true;\n    },\n\n    //每页展示数量点击事件\n    handleSizeChange() {},\n    //页码改变事件\n    handleCurrentChange() {},\n    //树点击事件\n    handleNodeClick(data, e) {\n      //根目录变电站列表\n      if (data.identifier == \"0\") {\n        //点击根节点，获取变电站数据\n        //间隔\n        this.isShow2 = false;\n        //重置pageNum\n        this.bdzqueryParams = {\n          pageNum: 1,\n          pageSize: 10\n        };\n        this.$refs.bdzTable.currentPage = 1;\n        //获取列表数据\n        this.getbdzData();\n        this.bdzdataShow = true;\n        this.znsbdataShow = this.jgdataShow = false;\n      }\n      //二级目录变电站名称\n      else if (data.identifier == \"1\") {\n        //点击变电站，获取间隔数据\n        this.currentBdz = data.id;\n        this.ssbdzmc = data.label;\n        this.ssbdz = data.id;\n        //重置页码\n        this.$refs.jgTable.currentPage = 1;\n        this.jgQueryParams.pageNum = 1;\n        this.jgQueryParams.jgdl = \"\"; //清空间隔大类\n        this.getFgsByBdzId();\n        this.getJgData({ ssbdz: data.id });\n        this.jgdataShow = true;\n        this.znsbdataShow = this.bdzdataShow = false;\n      } //二级目录变电站名称\n      else if (data.identifier == \"2\") {\n        //点击间隔大类，过滤间隔数据\n        //重新设置所属电站\n        this.currentBdz = data.ssbdz;\n        this.ssbdz = data.ssbdz;\n        //重置页码\n        this.$refs.jgTable.currentPage = 1;\n        this.jgQueryParams.pageNum = 1;\n        this.jgQueryParams.jgdl = data.id;\n        this.jglxcx = data.id;\n        this.getFgsByBdzId();\n        this.getJgData({ ssbdz: this.ssbdz, jgdl: data.id });\n        this.jgdataShow = true;\n        this.znsbdataShow = this.bdzdataShow = false;\n      } else if (data.identifier == \"3\") {\n        //点击间隔，获取站内设备\n        this.jglxcx = data.jglx;\n        this.sbxxForm.ssbdz = this.ssbdz;\n        //重置页码\n        this.$refs.znsbTable.currentPage = 1;\n        this.znsbParams.pageNum = 1;\n        this.bdzOptionsChangeClick();\n        this.ssjg = data.id;\n        this.sbxxForm.ssjg = this.ssjg;\n        //间隔\n        this.bdzqueryParams.ssbdz = \"\";\n        this.bdzqueryParams.ssjg = data.id;\n        this.getZnsbData();\n        this.znsbdataShow = true;\n        this.jgdataShow = this.bdzdataShow = false;\n      }\n    },\n    async getFgsByBdzId() {\n      let { data, code } = await getFgsByBdzId({ sbdm: this.currentBdz });\n      if (code === \"0000\") {\n        this.ssgs = data.value;\n        this.sbxxForm.ssgs = data.value;\n      }\n    },\n    //请求变电站数据\n    async getbdzData(param) {\n      this.bdzqueryParams = { ...this.bdzqueryParams, ...param };\n      const par = { ...this.bdzqueryParams, ...param };\n      if (this.treeForm.ssdwbm) {\n        par.ssdwbm = this.treeForm.ssdwbm;\n      }\n      await getBdzList(par).then(res => {\n        this.bdzdataShow = true;\n        this.jgdataShow = this.znsbdataShow = false;\n        this.tableAndPageInfo1.tableData = res.data.records;\n        this.tableAndPageInfo1.pager.total = res.data.total;\n      });\n    },\n    //日期格式化  yyyy-MM-dd\n    dateFormatter(d) {\n      let year = d.getFullYear();\n      let month =\n        d.getMonth() < 9 ? \"0\" + (d.getMonth() + 1) : \"\" + (d.getMonth() + 1);\n      let day = d.getDate() < 10 ? \"0\" + d.getDate() : \"\" + d.getDate();\n      return year + \"-\" + month + \"-\" + day;\n    },\n    //请求间隔数据\n    async getJgData(param) {\n      this.jgQueryParams = { ...this.jgQueryParams, ...param };\n      try {\n        let { data, code } = await getJgInfoList(this.jgQueryParams);\n        if (code === \"0000\") {\n          this.jgdataShow = true;\n          this.bdzdataShow = this.znsbdataShow = false;\n          this.tableAndPageInfo2.tableData = data.records;\n          this.tableAndPageInfo2.pager.total = data.total;\n        }\n      } catch (e) {}\n    },\n    //请求站内设备数据\n    async getZnsbData(params) {\n      try {\n        this.znsbParams = { ...this.znsbParams, ...params };\n        const param = this.znsbParams;\n        param.ssjg = this.ssjg;\n        const { data, code } = await getBdAsesetListPage(param);\n        if (code === \"0000\") {\n          this.znsbdataShow = true;\n          this.jgdataShow = this.bdzdataShow = false;\n          this.tableAndPageInfo3.tableData = data.records;\n          this.tableAndPageInfo3.pager.total = data.total;\n          // this.sbxxForm.ssgs=data.records[0].deptname\n          // this.sbxxForm.ssbdz=data.records[0].bdzmc\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //变电站弹框开始\n    //变电站修改按钮\n    updatebdz(row) {\n      this.$nextTick(async function() {\n        this.clearUpload();\n        this.jbxxForm = { ...row };\n        this.jbxxForm.attachment = [];\n        await this.getFileList();\n        this.isDisabled = false;\n        this.title = \"变电站台账修改\";\n        this.bdzDialogFormVisible = true;\n      });\n    },\n    clearUpload() {\n      if (this.$refs.upload) {\n        this.$refs.upload.clearFiles();\n      }\n    },\n    //关闭弹框\n    removeForm() {\n      this.jbxxForm = {\n        attachment: []\n      };\n      this.$nextTick(function() {\n        this.$refs[\"form\"].clearValidate();\n      });\n      this.bdzDialogFormVisible = false;\n    },\n    beforeUpload(file) {\n      const fileSize = file.size < 1024 * 1024 * 50;\n      if (!fileSize) {\n        this.$message.error(\"上传文件大小不能超过 50MB!\");\n      }\n    },\n    async getFileList() {\n      let { code, data } = await getListByBusinessId({\n        businessId: this.jbxxForm.objId\n      });\n      if (code === \"0000\") {\n        this.jbxxForm.attachment = data;\n        this.imgList = data.map(item => {\n          let item1 = {};\n          item1.name = item.fileName;\n          item1.url = this.$store.getters.currHost + item.fileUrl;\n          return item1;\n        });\n      }\n    },\n    //变电站详情方法\n    bdzDetails(row) {\n      this.$nextTick(async function() {\n        this.clearUpload();\n        this.jbxxForm = { ...row };\n        let temp = this.jbxxForm.ssdwmc;\n        this.jbxxForm.ssdwmc = this.jbxxForm.ssdwbm;\n        this.jbxxForm.ssdwbm = temp;\n        this.jbxxForm.attachment = [];\n        await this.getFileList();\n        this.bdzDialogFormVisible = true;\n        this.isDisabled = true;\n        this.title = \"变电站台账详情\";\n      });\n    },\n    addBdz() {\n      let params = {\n        lx: \"变电设备\",\n        ssdw: this.jbxxForm.ssdwbm,\n        mc: this.jbxxForm.bdzmc\n      };\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          addBdz(this.jbxxForm).then(res => {\n            if (res.code === \"0000\") {\n              //新增成功后发送通知\n              adddwzyfstz(params).then(res => {\n                if (res.code === \"0000\") {\n                }\n              });\n              this.uploadData.businessId = res.data.objId;\n              this.submitUpload();\n              this.bdzDialogFormVisible = false;\n              this.$message.success(\"操作成功,通知已分发\");\n              // this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getNewTreeInfo();\n              this.getData();\n            } else {\n              this.bdzDialogFormVisible = false;\n            }\n          });\n        } else {\n          return false;\n        }\n      });\n    },\n\n    submitUpload() {\n      this.$refs.upload.submit();\n    },\n    //变电站弹框结束\n    //新增按钮\n    AddButton() {\n      this.showFsss = false;\n      if (this.bdzdataShow) {\n        this.clearUpload();\n        this.imgList = [];\n        this.isDisabled = false;\n        this.bdzDialogFormVisible = true;\n        this.title = \"变电站台账新增\";\n      }\n      if (this.jgdataShow) {\n        this.jgxxForm = {};\n        this.fillBdz(); //设置间隔所属变电站\n        this.jgShow = false;\n        this.jgDialogFormVisible = true;\n      }\n      if (this.znsbdataShow) {\n        this.activeTabName = \"sbDesc\";\n        // this.sbxxForm = {};\n        if (this.singleClickData) {\n          this.sbxxForm = { ...this.singleClickData };\n          this.sbxxForm.objId = undefined;\n        }\n        this.sbxxForm.ssgs = this.ssgs;\n        this.sbxxForm.ssbdz = this.ssbdz;\n        this.sbxxForm.ssjg = this.ssjg;\n        //打开弹出框\n        this.znsbDialogForm = true;\n        //按钮和表单是否可编辑控制\n        this.assetIsDisable = false;\n      }\n    },\n    exportExcel() {\n      let fileName = this.ssbdzmc + \"设备信息表\";\n      let exportUrl = \"/bdsb/exportExcel/assetInfo\";\n      exportExcel(exportUrl, this.jgQueryParams, fileName);\n    },\n    exportExcelOfBdz() {\n      let fileName = \"变电站信息表\";\n      let exportUrl = \"/equipList/exportExcelOfBdz\";\n      exportExcel(exportUrl, this.jgQueryParams, fileName);\n    },\n    importExcelOfBdz() {\n      this.importExcelUrl = \"/manager-api/equipList/importExcelOfBdz\";\n      this.fileName = \"变电站信息表\";\n      this.$refs.importExcel.importExcel();\n    },\n    getUploadData(fileName) {\n      switch (fileName) {\n        case \"变电站信息表\":\n          this.getbdzData();\n          break;\n        case \"间隔信息表\":\n          this.getJgData();\n          break;\n        case \"设备信息表\":\n          this.getZnsbData();\n          break;\n        default:\n          break;\n      }\n      this.getNewTreeInfo();\n    },\n    exportExcelOfJg() {\n      let fileName = \"间隔信息表\";\n      let exportUrl = \"/equipList/exportExcelOfJg\";\n      exportExcel(exportUrl, this.jgQueryParams, fileName);\n    },\n    importExcelOfJg() {\n      this.importExtraInfo.ssbdz = this.currentBdz;\n      this.importExcelUrl = \"/manager-api/equipList/importExcelOfJg\";\n      this.fileName = \"间隔信息表\";\n      this.$refs.importExcel.importExcel();\n    },\n    exportExcelOfAsset() {\n      let fileName = \"设备信息表\";\n      let exportUrl = \"/equipList/exportExcelOfAsset\";\n      let param = {};\n      param.ssjg = this.ssjg;\n      exportExcel(exportUrl, param, fileName);\n    },\n    importExcelOfAsset() {\n      this.importExtraInfo.ssgs = this.ssgs;\n      this.importExtraInfo.ssbdz = this.ssbdz;\n      this.importExtraInfo.ssjg = this.ssjg;\n      this.importExcelUrl = \"/manager-api/equipList/importExcelOfAsset\";\n      this.fileName = \"设备信息表\";\n      this.$refs.importExcel.importExcel();\n    },\n    copyForAsset() {\n      this.$refs[\"formCopy\"].validate(valid => {\n        if (valid) {\n          this.formCopy.sourceSsjg = this.ssjg;\n          copyAsset(this.formCopy).then(res => {\n            if (res.code === \"0000\") {\n              this.$message.success(\"操作成功\");\n              this.isShowCopy = false;\n              this.$refs.formCopy.resetFields();\n            }\n          });\n        } else {\n          return false;\n        }\n      });\n    },\n    //根据设备类型获取设备型号list 数据\n    getSbxhBySblx(sblx) {\n      this.ggxhList = [];\n      getSbxhList({ dysblx: sblx }).then(res => {\n        if (res.code == \"0000\") {\n          // 保证每项都包含label字段\n          this.ggxhList = (res.data || []).map(item => {\n            return {\n              ...item,\n              label: item.label || item.lebel || item.xh || \"\"\n            };\n          });\n        }\n      });\n    },\n\n    /*站内设备开始*/\n    //设备修改操作\n    async updateAsset(row) {\n      row.xh = row.ggxh; //规格型号赋值\n      this.technicalParameters(row);\n      this.paramQuery.sblxbm = row.assetTypeCode;\n      await this.getParameters();\n      this.resumeQuery.foreignNum = row.sbmc;\n      this.resumeQuery.sblx = row.sblx;\n      this.bdsbid = row.objId;\n      this.sbmc = row.sbmc;\n      this.ssbdz = row.ssbdz;\n      this.getResumList();\n      await this.getSbxhBySblx(row.assetTypeCode); //根据设备类型获取设备型号下拉框\n      //给表单赋值\n      this.sbxxForm = { ...row };\n      //按钮和表单是否可编辑控制\n      this.assetIsDisable = false;\n      this.showFsss = true;\n      //打开设备弹出框\n      this.znsbDialogForm = true;\n    },\n    //点击新增，修改，详情时，重新获取对应的技术参数信息\n    technicalParameters(row) {\n      this.jscsForm = {};\n      this.paramQuery.sblxbm = row.assetTypeCode;\n      this.jscsForm.sblxbm = row.assetTypeCode;\n      this.jscsForm.sbbm = row.objId;\n      this.getParameters();\n    },\n    //获取技术参数对照信息，展示对应得技术参数label信息\n    async getParameters() {\n      this.jscsLabelList = [];\n      getParamDataList(this.paramQuery).then(res => {\n        this.jscsLabelList = res.data;\n        this.getParamValue();\n      });\n    },\n    //获取技术参数值信息\n    getParamValue() {\n      getParamsValue(this.jscsForm).then(res => {\n        if (res.data != \"\") {\n          this.jscsForm = { ...res.data[0] };\n        }\n      });\n    },\n    /**\n     * 设备履历\n     */\n    getResumList(par) {\n      let param = { ...par, ...this.resumeQuery };\n      getResumDataList(param).then(res => {\n        this.resumPageInfo.tableData = res.data.records;\n        this.resumPageInfo.pager.total = res.data.total;\n      });\n    },\n    //变电站下拉框中的change事件\n    bdzOptionsChangeClick() {\n      //当发生change事件时先清空之前的间隔信息\n      this.$set(this.sbxxForm, \"ssjg\", \"\");\n      //调用查询间隔方法\n      this.getJgDataListSelected();\n    },\n    //获取间隔下拉框数据\n    getJgDataListSelected() {\n      //给获取间隔下拉框查询参数赋值\n      this.selectJgOptionsParam.ssbdz = this.sbxxForm.ssbdz;\n      getJgDataListSelected(this.selectJgOptionsParam).then(res => {\n        this.jgOptionsDataList = res.data;\n      });\n    },\n    //设备类型change事件。获取技术参数信息\n    async showParams(data) {\n      this.paramQuery.sblxbm = data;\n      await this.getParameters();\n      await this.getSbxhBySblx(data); //根据设备类型获取设备型号下拉框\n    },\n    //设备履历tab页点击事件\n    async handleSbllDescTabNameClick(tab, event) {\n      switch (tab.name) {\n        case \"ztbgjl\":\n          await this.getResumList();\n          break;\n        case \"yh\":\n          await this.getYhList();\n          break;\n        case \"sy\":\n          await this.getSyList();\n          break;\n        case \"blgk\":\n          await this.getBlgkList();\n          break;\n        case \"qtsbwt\":\n          await this.getqtsbwtList();\n          break;\n        case \"jx\":\n          await this.getJxList();\n          break;\n        case \"jdbh\":\n          await this.getJdbhList();\n          break;\n        case \"tzjl\":\n          await this.getTzjlList();\n          break;\n      }\n    },\n    //保存设备信息\n    submit() {\n      this.assetSubmitLoading = true;\n      this.$refs[\"sbxxForm\"].validate(valid => {\n        if (valid) {\n          this.addAsset();\n        } else {\n          this.assetSubmitLoading = false;\n          this.$message.error(\"校验未通过！\");\n          return false;\n        }\n      });\n    },\n    //站内设备弹框关闭\n    //清空表单\n    resetForm1() {\n      this.sbxxForm = this.$options.data().form;\n      this.jscsForm = this.$options.data().form;\n      this.$nextTick(function() {\n        this.$refs[\"sbxxForm\"].clearValidate();\n      });\n      this.sbllDescTabName = \"ztbgjl\";\n      this.znsbDialogForm = false;\n    },\n    /**\n     * 添加设备保存基本信息\n     */\n    addAsset() {\n      this.sbxxForm.sbClassCsValue = this.jscsForm;\n      addAsset(this.sbxxForm).then(res => {\n        if (res.code == \"0000\") {\n          this.$message({\n            type: \"success\",\n            message: \"操作成功!\"\n          });\n          this.znsbDialogForm = false;\n          // this.tableAndPageInfo3.pager.pageResize = \"Y\";\n          this.getZnsbData();\n        }\n        this.assetSubmitLoading = false;\n      });\n    },\n    //设备详情操作\n    async assetDetails(row) {\n      row.xh = row.ggxh; //规格型号赋值\n      this.technicalParameters(row);\n      this.resumeQuery.foreignNum = row.sbmc;\n      this.resumeQuery.sblx = row.sblx;\n      this.bdsbid = row.objId;\n      this.sbmc = row.sbmc;\n      this.ssbdz = row.ssbdz;\n      this.getResumList();\n      await this.getSbxhBySblx(row.assetTypeCode); //根据设备类型获取设备型号下拉框\n      //给表单赋值\n      this.sbxxForm = { ...row };\n      this.jgOptionsDataList = [\n        { label: this.sbxxForm.wzmc, value: this.sbxxForm.ssjg }\n      ];\n      //按钮和表单是否可编辑控制\n      this.assetIsDisable = true;\n      this.showFsss = true;\n      //打开设备弹出框\n      this.znsbDialogForm = true;\n    },\n    //获取变电站下拉框数据\n    getBdzDataListSelected() {\n      getBdzDataListSelected({}).then(res => {\n        this.bdzOptionsDataList = res.data;\n      });\n    },\n    resetForm() {\n      this.jgDialogFormVisible = false;\n      this.$refs.jgxxForm.resetFields();\n    },\n    //筛选条件重置\n    filterReset() {\n      // 重置变电站查询参数（保留树形选择的基本参数）\n      if (this.bdzdataShow) {\n        this.bdzqueryParams = {\n          pageNum: 1,\n          pageSize: 10\n        };\n      }\n\n      // 重置间隔查询参数（保留所属变电站和间隔大类）\n      if (this.jgdataShow) {\n        const ssbdz = this.jgQueryParams.ssbdz;\n        const jgdl = this.jgQueryParams.jgdl;\n        this.jgQueryParams = {\n          ssbdz: ssbdz,\n          jgdl: jgdl,\n          dydj: undefined,\n          pageSize: 10,\n          pageNum: 1\n        };\n      }\n\n      // 重置站内设备查询参数（保留所属间隔）\n      if (this.znsbdataShow) {\n        this.znsbParams = {\n          pageSize: 10,\n          pageNum: 1\n        };\n        // 保留间隔相关的查询条件\n        this.bdzqueryParams = {\n          ssbdz: \"\",\n          ssjg: this.ssjg,\n          pageNum: 1,\n          pageSize: 10\n        };\n      }\n\n      // 重置筛选条件的数据和复选框\n      this.filterInfo1.data = {\n        ywdwArr: [],\n        jhnyArr: [],\n        xlArr: \"\",\n        jhlxArr: [],\n        jhztArr: \"\",\n        sfdd: \"\"\n      };\n      this.filterInfo1.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n\n      this.filterInfo2.data = {\n        ywdwArr: [],\n        jhnyArr: [],\n        xlArr: \"\",\n        jhlxArr: [],\n        jhztArr: \"\",\n        sfdd: \"\"\n      };\n      this.filterInfo2.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n\n      this.filterInfo3.data = {\n        ywdwArr: [],\n        jhnyArr: [],\n        xlArr: \"\",\n        jhlxArr: [],\n        jhztArr: \"\",\n        sfdd: \"\"\n      };\n      this.filterInfo3.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n\n      // 重置表格页码\n      if (this.bdzdataShow && this.$refs.bdzTable) {\n        this.$refs.bdzTable.currentPage = 1;\n      }\n      if (this.jgdataShow && this.$refs.jgTable) {\n        this.$refs.jgTable.currentPage = 1;\n      }\n      if (this.znsbdataShow && this.$refs.znsbTable) {\n        this.$refs.znsbTable.currentPage = 1;\n      }\n\n      // 根据当前显示的表格重新加载数据（保持当前的树形选择状态）\n      if (this.bdzdataShow) {\n        this.getbdzData();\n      } else if (this.jgdataShow) {\n        this.getJgData();\n      } else if (this.znsbdataShow) {\n        this.getZnsbData();\n      }\n    },\n    async querySearchAsync(queryString, cb) {\n      const results = queryString\n        ? this.ggxhList.filter(\n            item =>\n              item.label.toLowerCase().indexOf(queryString.toLowerCase()) !== -1\n          )\n        : this.ggxhList;\n      // 确保每个选项都包含 label 和 value 字段\n      const formattedResults = results.map(item => ({\n        label: item.label,\n        value: item.value || item.label // 如果没有 value，使用 label 作为 value\n      }));\n      console.log(\"results:\", formattedResults);\n      cb(formattedResults);\n    },\n    handleSelect(item) {\n      this.isSelectingFromDropdown = true; // 设置标记\n      this.sbxxForm.xh = item.label;\n    },\n    async handleChange(value) {\n      // 如果是从下拉列表选择的，不执行新增操作\n      setTimeout(() => {\n        if (this.isSelectingFromDropdown) {\n          this.isSelectingFromDropdown = false; // 重置标记\n          return;\n        }\n        // 如果输入的值不在选项中，则添加到型号库\n      if (value && !this.ggxhList.some(item => item.label === value)) {\n        try {\n          const params = {\n            sbxh: value,\n            dysblx: this.sbxxForm.assetTypeCode // 从表单中获取设备类型\n          }\n          addSbxh(params).then(res => {\n            if (res.code === '0000') {\n              this.ggxhList.push({\n                label: value,\n                value: value // 添加 value 字段\n              })\n              this.$message.success('已添加到型号库')\n            } else {\n              this.$message.warning(res.msg || '添加型号失败')\n            }\n          })\n        } catch (error) {\n          console.warning('添加型号失败:', error)\n          this.$message.warning('添加型号失败')\n        }\n      }\n      }, 500);\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.imgCls {\n  height: 150px !important;\n}\n\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  height: 70.6vh;\n  overflow: auto;\n}\n\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.box-cardList {\n  height: 70%;\n}\n\n.item {\n  width: 200px;\n  float: left;\n}\n\n#main_container_dj {\n  height: calc(100vh - 84px);\n}\n\n.aside_height {\n  height: 96%;\n}\n\n.defect .el-form-item:nth-child(odd) {\n  margin-right: 70px;\n}\n\n/*背景颜色调整*/\n#main_container_dj,\n#main_container_dj .el-aside {\n  background-color: #b4caf1;\n}\n\n///deep/ .qxlr_dialog_insert .el-dialog__header {\n//  background-color: #8eb3f5;\n//}\n//\n///deep/ .pmyBtn {\n//  background: #8eb3f5;\n//}\n\n/*/deep/ .add_sy_tyrq .el-input--medium .el-input__inner {*/\n/*  width: 200px;*/\n/*}*/\n\n/*添加弹出框得宽度*/\n/*/deep/ .el-input--medium .el-input__inner {*/\n/*  width: 200px;*/\n/*}*/\n.el-select {\n  width: 100%;\n}\n\n.el-date-editor {\n  width: 100%;\n}\n\n.el-carousel__item h3 {\n  color: #475669;\n  font-size: 14px;\n  opacity: 0.75;\n  line-height: 150px;\n}\n\n.el-carousel__item:nth-child(2n) {\n  background-color: #99a9bf;\n}\n\n.el-carousel__item:nth-child(2n + 1) {\n  background-color: #d3dce6;\n}\n\n/* 设置滚动条的样式 */\n::-webkit-scrollbar {\n  width: 12px;\n}\n\n/* 滚动槽 */\n::-webkit-scrollbar-track {\n  //-webkit-box-shadow:inset006pxrgba(0,0,0,0.3);\n  border-radius: 10px;\n}\n\n/* 滚动条滑块 */\n::-webkit-scrollbar-thumb {\n  border-radius: 10px;\n  background: rgba(0, 0, 0, 0.1);\n  //-webkit-box-shadow:gba(0,0,0,0.5);\n}\n\n::-webkit-scrollbar-thumb:window-inactive {\n  background: rgba(0, 0, 0, 0.1);\n}\n\n[data-v-67a974b1]::-webkit-scrollbar {\n  width: 8px;\n}\n\n.item {\n  width: 225px;\n  float: left;\n}\n\n/deep/ .box-card {\n  margin: 0 6px;\n}\n\n//有子节点 且未展开\n.el-tree ::v-deep .el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/add.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n\n//有子节点 且已展开\n.el-tree\n  ::v-deep\n  .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/prep.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n\n//没有子节点\n.el-tree ::v-deep .el-tree-node__expand-icon.is-leaf::before {\n  background: transparent;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n\n.categoryTreeIcons {\n  background: url(\"../../../../assets/icons/icon/icon1.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n\n.tableIcon {\n  background: url(\"../../../../assets/icons/icon/icon2.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n\n.classIcon {\n  background: url(\"../../../../assets/icons/icon/icon3.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n</style>\n<style>\n#pic_form .el-form-item__content {\n  margin-left: 0 !important;\n}\n\n#imgId .el-carousel__container {\n  background-color: #ffffff !important;\n}\n</style>\n"], "sourceRoot": "src/views/dagangOilfield/dwzygl/bdgl"}]}