{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\gswh_edit.vue?vue&type=style&index=0&id=0259dd70&scoped=true&lang=scss&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\gswh_edit.vue", "mtime": 1706897323217}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoucm93IHsKICBwYWRkaW5nLXRvcDogMjBweDsKICBwYWRkaW5nLWJvdHRvbTogMjBweDsKICBmb250LXNpemU6IDIwcHg7Cn0KCi5idG4gewogIHBhZGRpbmc6IDE0cHg7CgogICY6aG92ZXIgewogICAgY29sb3I6ICMwMGMzOWE7CiAgfQp9Cg=="}, {"version": 3, "sources": ["gswh_edit.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuNA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA", "file": "gswh_edit.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk", "sourcesContent": ["<template>\n  <el-row :gutter=\"16\">\n    <!--  原始公式  -->\n    <el-col :span=\"24\">\n      <div>\n        <el-button type=\"primary\" @click=\"check_jb\">验证</el-button>\n        <el-button type=\"primary\" @click=\"save_jb\">保存</el-button>\n        <!--        <el-button type=\"primary\" @click=\"init_jb\">初始化</el-button>-->\n        <el-button type=\"primary\" @click=\"clear_jb\">清空</el-button>\n      </div>\n    </el-col>\n    <el-col :span=\"24\">\n      <div class=\"row\" style=\"border:1px;\" id=\"atChat\">\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' && ')\">&&</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' || ')\">||</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' > ')\">></a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' >= ')\">>=</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' < ')\"><</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' <= ')\"><=</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' == ')\">==</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' != ')\">!=</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' Math.abs() ')\">abs</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' ( ')\">(</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' ) ')\">)</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' { ')\">{</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' } ')\">}</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick('elseIf')\">else if</a>\n      </div>\n    </el-col>\n\n    <!--  规则解释  -->\n    <el-col :span=\"24\">\n      <el-form :model=\"editForm\" ref=\"editForm\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item prop=\"gs\">\n              <el-input id=\"jb_text\" name=\"pptjms\" type=\"textarea\" :rows=\"10\" v-model=\"editForm.gs\" placeholder=\"请输入公式\"\n                        v-on:input.native=\"jb_show\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <div>\n              <span>规则解释</span>\n            </div>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item prop=\"gzjs\">\n              <el-input id=\"jb_text_show\" name=\"pptjms\" type=\"textarea\" :rows=\"10\" v-model=\"editForm.gzjs\" disabled/>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n    </el-col>\n  </el-row>\n</template>\n\n<script>\nimport {verifyexpression} from '@/api/dagangOilfield/bzgl/sbztpjbzk/jbwh'\n\nexport default {\n  name: 'gswh_edit',\n  props: {\n    jb: {\n      type: String,\n      default: ''\n    },\n    tableData: {\n      type: Array,\n      default: ''\n    },\n  },\n  data() {\n    return {\n      editForm: {\n        gs: '',//公式\n        gzjs: '',//规则解释\n      },\n      checkJB: false,//是否验证脚本\n      jbVal: '',//返回给组件中的脚本字符串\n      parentMap: new Map(),\n    };\n  },\n  watch: {\n    tableData: {\n      handler(newVal, oldVal) {\n        newVal.forEach(item => {\n          let rowindex = parseInt(item.rowindex)+1;\n          let colindex = parseInt(item.colindex)+1;\n          this.parentMap.set(item.objId,rowindex+\"行\"+colindex+\"列\");\n        })\n        this.jb_show();\n      },\n      deep: true,\n      immediate: true\n    },\n    jb: {\n      handler(newVal, oldVal) {\n        this.editForm.gs = newVal;\n        this.jb_show();\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  mounted() {\n  },\n  methods: {\n    //放置光标数据\n    trTableClick(str) {\n      let obj = document.getElementById('jb_text');\n      if (document.selection) {\n        let sel = document.selection.createRange();\n        sel.text = str;\n      } else if (typeof obj.selectionStart === 'number' && typeof obj.selectionEnd === 'number') {\n        let startPos = obj.selectionStart;\n        let endPos = obj.selectionEnd;\n        let cursorPos = startPos;\n        let tmpStr = obj.value;\n        obj.value = tmpStr.substring(0, startPos) + str + tmpStr.substring(endPos, tmpStr.length);\n        this.editForm.gs = obj.value;//设置公式值\n        cursorPos += str.length;\n        obj.selectionStart = obj.selectionEnd = cursorPos;\n      } else {\n        obj.value += str;\n        this.editForm.gs = obj.value;//设置公式值\n      }\n      this.jb_show();\n    },\n    //绑定a标签点击事件\n    async btnClick(val) {\n      await this.trTableClick(val == 'elseIf' ? \"\\nelse if()\\n{\\n    return    ;\\n}\" : val);\n    },\n    //脚本翻译\n    jb_show() {\n      let ruleScriptStr = this.editForm.gs;\n      ruleScriptStr = ruleScriptStr.replace(/else if/g, \"如果\")\n          .replace(/if/g, \"如果\")\n          .replace(/else/g, \"否则\")\n          .replace(/getParameter/g, \"参数值\")\n          .replace(/getColValue/g, \"参数值\")\n          .replace(/getXxdData/g, \"信息点值\")\n          .replace(/getZxXxdData/g, \"字信息点值\")\n          .replace(/>=/g, \"大于等于\")\n          .replace(/>/g, \"大于\")\n          .replace(/<=/g, \"小于等于\")\n          .replace(/</g, \"小于\")\n          .replace(/==/g, \"等于\")\n          .replace(/!=/g, \"不等于\")\n          .replace(/\\|\\|/g, \"或者\")\n          .replace(/&&/g, \"并且\")\n          .replace(/return/g, \"返回\")\n          .replace(/SubItemValue/g, \"单元格值\")\n          .replace(/min/g, \"多个单元格最小值\")\n          .replace(/max/g, \"多个单元格最大值\")\n          .replace(/avg/g, \"多个单元格平均值\")\n          .replace(/(Math.abs)\\s*\\(/g, \"绝对值(\");\n\n      if (this.parentMap) {\n        this.parentMap.forEach((val, key) => {\n          if (ruleScriptStr.includes(key)){\n            this.$(\"#\"+key).css('backgroundColor','red')\n          }\n          else {\n            this.$(\"#\"+key).css('backgroundColor','white')\n          }\n          ruleScriptStr = ruleScriptStr.replaceAll(key, val);\n        })\n      }\n      this.editForm.gzjs = ruleScriptStr;\n    },\n    //脚本验证\n    async check_jb() {\n      this.checkJB = false;\n      let {code, data} = await verifyexpression(this.editForm.gs)\n      if (code === '0000') {\n        if (data) {\n          this.checkJB = true;\n          this.$message.success(\"脚本执行成功\")\n        } else {\n          this.$message.error(\"脚本定义错误\")\n          this.checkJB = false;\n        }\n      }\n    },\n    //脚本保存\n    save_jb() {\n      if (this.checkJB) {\n        this.jbVal = this.editForm.gs;\n        this.$emit('setJbVal', this.jbVal);//将脚本的值传递给父页面\n        this.$emit('jbClose');//关闭脚本弹框\n\n      } else {\n        this.$message({\n          type: 'error',\n          message: '脚本没有验证或脚本定义错误，请进行验证后或定义正确脚本在保存！'\n        })\n      }\n    },\n    //清空脚本\n    clear_jb() {\n      this.checkJB = false;\n      this.editForm.gs = \"\";\n      this.jb_show();\n    },\n    //初始化\n    init_jb() {\n      this.checkJB = false;\n      this.editForm.gs = \"if()\\n{\\n    return    1;\\n}\\nelse\\n{\\n    return     0;\\n}\";\n      this.jb_show();\n    },\n  },\n}\n</script>\n\n<style scoped lang=\"scss\">\n.row {\n  padding-top: 20px;\n  padding-bottom: 20px;\n  font-size: 20px;\n}\n\n.btn {\n  padding: 14px;\n\n  &:hover {\n    color: #00c39a;\n  }\n}\n</style>\n"]}]}