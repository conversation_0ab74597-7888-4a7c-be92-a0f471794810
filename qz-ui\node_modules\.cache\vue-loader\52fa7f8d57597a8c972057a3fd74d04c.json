{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\lpbzk\\xsdwpz_gf.vue?vue&type=style&index=0&id=a7434a7e&lang=css&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\lpbzk\\xsdwpz_gf.vue", "mtime": 1732021839756}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi8q5o6n5Yi2aW5wdXTovpPlhaXmoYbovrnmoYbmmK/lkKbmmL7npLoqLwouZWxJbnB1dCA+Pj4gLmVsLWlucHV0X19pbm5lciB7CiAgYm9yZGVyOiAwOwp9CgouYm94LWNhcmQgewogIG1hcmdpbi1ib3R0b206IDJ2aCAhaW1wb3J0YW50Owp9CgouZWwtc2VsZWN0IHsKICB3aWR0aDogMTAwJTsKfQoK"}, {"version": 3, "sources": ["xsdwpz_gf.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyoBA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "xsdwpz_gf.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/lpbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row>\n      <!--基本信息查询及显示-->\n      <el-col>\n        <!--搜索条件-->\n        <el-filter\n          ref=\"filter\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 80, itemWidth: 230 }\"\n          @handleReset=\"getReset\"\n          @onfocusEvent=\"inputFocusEvent\"\n          @handleEvent=\"handleEvent\"\n        />\n\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button type=\"primary\" v-hasPermi=\"['gfbzxsdwpe:button:add']\" icon=\"el-icon-plus\" @click=\"getInster\">新增\n            </el-button>\n\n          </div>\n          <comp-table :table-and-page-info=\"tableAndPageInfo\" @update:multipleSelection=\"selectChange\" height=\"70vh\" v-loading=\" loading\">\n            <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\"\n                             width=\"160\"\n                             :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button @click=\"getUpdate(scope.row)\" v-hasPermi=\"['gfbzxsdwpe:button:update']\" type=\"text\"\n                           size=\"small\"    title=\"修改\"  class='el-icon-edit'\n                >\n                </el-button>\n                <el-button @click=\"getDetail(scope.row)\" type=\"text\" size=\"small\" title=\"详情\" class=\"el-icon-view\"></el-button>\n                <el-button type=\"text\" size=\"small\" v-if=\"scope.row.createBy === $store.getters.name\" title=\"删除\" icon=\"el-icon-delete\" @click=\"deleteRow(scope.row.objId)\">\n            </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n\n    <!-- 详情/新增/修改 -->\n    <el-dialog :title=title :visible.sync=\"isShowDetails\" v-if=\"isShowDetails\" width=\"50%\" v-dialogDrag >\n      <el-form label-width=\"180px\" ref=\"form\" :model=\"form\">\n        <!--主表信息-->\n        <div>\n          <el-col :span=\"12\">\n            <el-form-item label=\"专业：\" prop=\"zy\">\n              <el-select v-model=\"form.zy\" disabled=\"disabled\" @change=\"getBdzAndPds\" placeholder=\"请输入内容\">\n                <el-option\n                  v-for=\"item in zyList\"\n                  :key=\"item.label\"\n                  :label=\"item.value\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"地点：\" prop=\"ddid\">\n              <el-select v-model=\"form.ddid\" ref=\"ddid\" :disabled=\"isDisabled\" @change=\"getJgList1\" placeholder=\"请输入内容\" filterable>\n                <el-option\n                  v-for=\"item in ddList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"点位名称：\" prop=\"dwmc\">\n              <el-input v-model=\"form.dwmc\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"点位描述：\" prop=\"dwms\">\n              <el-input v-model=\"form.dwms\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"标签绑定值：\" prop=\"bqbdz\">\n              <el-input v-model=\"form.bqbdz\" :disabled=\"isDisabled\" placeholder=\"请输入标签绑定值\"></el-input>\n            </el-form-item>\n          </el-col>\n        </div>\n\n        <!--子表信息-->\n        <div>\n          <el-table :data=\"propTableData.colFirst\" :disabled=\"isDisabled\" height=\"300\" border stripe\n                    style=\"width: 100%\"\n          >\n<!--            <el-table-column\n              type=\"index\"\n              width=\"50\"\n              align=\"center\"\n              label=\"序号\"\n            />-->\n            <!--子表列表-->\n            <el-table-column align=\"center\" prop=\"xh\" label=\"序号\">\n              <template slot-scope=\"scope\">\n                <span>\n                    <el-input-number size=\"small\" v-model=\"scope.row.xh\" :disabled=\"isDisabled\" :min=\"1\" :precision=\"0\" controls-position=\"right\"></el-input-number>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"sbid\" label=\"间隔名称\">\n              <template slot-scope=\"scope\">\n<!--                <el-input placeholder=\"请输入设备名称\" :disabled=\"isDisabled\"\n                          v-model=\"scope.row.sbmc\"\n                ></el-input>-->\n                <el-select v-model=\"scope.row.sbid\" placeholder=\"请输入间隔名称\" :disabled=\"isDisabled\" clearable filterable @change=\"sbmcChange($event,scope.row)\">\n                  <el-option\n                    v-for=\"item in jgmcList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"sblx\" label=\"设备类型\">\n              <template slot-scope=\"scope\">\n                <el-select v-model=\"scope.row.sblx\" placeholder=\"请选择设备类型\" :disabled=\"isDisabled\" clearable filterable multiple>\n                  <el-option\n                    v-for=\"item in sblxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" width=\"80\">\n              <!--子表添加按钮-->\n              <template slot=\"header\" slot-scope=\"scope\">\n                <el-button type=\"primary\" size=\"small\" :disabled=\"isDisabled\" icon=\"el-icon-plus\"\n                           @click=\"listFirstAdd(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n              <!--子表删除按钮-->\n              <template slot-scope=\"scope\">\n                <el-button type=\"text\" icon=\"el-icon-delete\" :disabled=\"isDisabled\"\n                           @click=\"listFirstDel(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button v-if=\"title=='巡视点位增加' || title=='巡视点位修改'\" type=\"primary\" @click=\"saveRow\">确 认\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!--设备名称弹框-->\n    <el-dialog\n      :append-to-body=\"true\"\n      title=\"设备名称\"\n      :visible.sync=\"ZbDialogFormVisible\"\n      width=\"400px\"\n      v-if=\"ZbDialogFormVisible\"\n    >\n      <device-tree\n        @getDeviceTypeData=\"getDeviceTypeData\"\n        @closeDeviceTypeDialog=\"closeDeviceTypeDialog\"\n      >\n      </device-tree>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getList, queryZb, remove, saveOrUpdate } from '@/api/dagangOilfield/bzgl/lpbzk/xsdwpz'\nimport DeviceTree from '@/views/dagangOilfield/bzgl/sbbzk/deviceTree'\nimport {\n  getDwSblxAll,\n  getDwGfSblxByJg,\n  getGfXsdwJgList,\n  getSblxDataListSelected,\n  getXsdwJdList\n} from '@/api/dagangOilfield/asset/bdsbtz'\nimport { getBdzSelectList, getGfBdzSelectList } from '@/api/yxgl/bdyxgl/bdxjzqpz'\nimport { getPdsTreeList } from '@/api/dagangOilfield/asset/pdg'\nimport { getDictTypeData } from '@/api/system/dict/data'\n\nexport default {\n  name: 'xsdwpz',\n  components: { DeviceTree },\n  data() {\n    return {\n      loading:false,\n      sblxList:[],//设备类型下拉框\n      jgmcList:[],//间隔名称下拉框\n      // 多选框选中的id\n      ids: [],\n      //弹出框中表格数据\n      propTableData: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //标题\n      title: '',\n      //子表标题\n      zbtitle: '',\n      //详情弹框是否显示\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //子表\n      ZbDialogFormVisible: false,\n      //子表弹框展示\n      isShowSbDetails: false,\n      //子表增加框是否展示\n      isShowZbAdd: false,\n      //子表删除框是否展示\n      isShowZbDelete: false,\n      //子表设备名称是否展示\n      isShowSbmc: false,\n      isFilter: false,\n      //专业下拉框\n      zyList: [{ label: '光伏', value: '光伏' }],\n      //地点下拉框\n      ddList: [],\n      //设备类型\n      sblxOptionsDataSelected: [],\n      //设备名称\n      sbmcList: [{ label: '一', value: '一' }, { label: '二', value: '二' }],\n      //form表单\n      form: {\n        zy: '',\n        dd: '',\n        dwmc: '',\n        dwms: '',\n        bqbdz: '',\n        colFirst: [],\n        sbmc: ''\n      },\n      //查询条件\n      filterInfo: {\n        data: {\n          // zy: '',\n          dd: '',\n          dwmc: '',\n          dwms: '',\n          bqbdz: ''\n        },\n        fieldList: [\n          {\n            label: '地点',\n            value: 'dd',\n            type: 'selectCn',\n            options: [],\n            clearable: true,\n            filterable:true,\n          },\n          { label: '点位名称', value: 'dwmc', type: 'input', clearable: true },\n          { label: '点位描述', value: 'dwms', type: 'input', clearable: true },\n          { label: '标签绑定值', value: 'bqbdz', type: 'input', clearable: true }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: '专业', prop: 'zy', minWidth: '120' },\n          { label: '地点', prop: 'dd', minWidth: '120' },\n          { label: '点位名称', prop: 'dwmc', minWidth: '120' },\n          { label: '点位描述', prop: 'dwms', minWidth: '160' },\n          { label: '标签绑定值', prop: 'bqbdz', minWidth: '160' }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        zy:'光伏',\n      },\n      ddMap:new Map(),\n      jgMap:new Map(),\n      jgDataMap:new Map(),\n    }\n  },\n  created() {\n    //列表查询\n    this.getData();\n    this.handleEvent({label:'zy',value:'光伏'},'');\n  },\n  methods: {\n    //设备名称下拉框change\n    async sbmcChange(val,row){\n      row.sblx = [];\n      if(this.jgMap.get(val) === 'jg'){//间隔类型\n        getDwGfSblxByJg({ssjg:val}).then(res=>{\n          this.sblxList = res.data;\n          let sblxs = [];\n          res.data.forEach(item=>{\n            sblxs.push(item.value);\n          })\n          this.$set(row,'sblx',sblxs);\n        })\n      }else if(this.jgMap.get(val) === 'qt'){//其他\n        this.sblxList = {label:'辅助系统',value:'bd37'};\n        this.$set(row,'sblx',['bd37']);\n      }\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.loading = true\n        this.params = { ...this.params, ...params,...{ zy:'光伏'} }\n        const { data, code } = await getList(this.params)\n        if (code === '0000') {\n          this.tableAndPageInfo.tableData = data.records\n          this.tableAndPageInfo.pager.total = data.total\n          this.loading = false\n        }\n      } catch (e) {\n      }\n    },\n    //根据主表查询子表方法\n    async getListZb(row) {\n      try {\n        const { data, code } = await queryZb({ objId: row.objId })\n        if (code === '0000') {\n          data.forEach(item=>{\n            item.sblx = item.sblx&&item.sblx.split(',');\n          })\n          this.propTableData.colFirst = data\n        }\n      } catch (e) {\n      }\n    },\n    /*----------------------主表-----------------------*/\n    //新增按钮\n    getInster() {\n      this.propTableData.colFirst = []\n      this.title = '巡视点位增加'\n      this.isDisabled = false\n      this.$nextTick(() => {\n        this.form = this.$options.data().form\n        this.resetForm('form')\n        this.form.zy = '光伏';\n        this.getBdzAndPds(this.form.zy)\n      })\n      this.ddList = []\n      this.sblxOptionsDataSelected = []\n      this.isShowDetails = true\n    },\n    //修改按钮\n    async getUpdate(row) {\n      await this.getListZb(row)\n      this.getBdzAndPds(row.zy)\n      await this.getJgList(row);\n      await this.getSblxAll(row);\n      this.title = '巡视点位修改'\n      this.isDisabled = false\n      this.form = { ...row }\n      this.isShowDetails = true\n      this.ZbDialogFormVisible = false\n    },\n    //获取所有设备类型用于回显\n    getSblxAll(row){\n      getDwSblxAll({ssbdz:row.ddid}).then(res=>{\n        this.sblxList = res.data;\n      })\n    },\n    //获取间隔名称下拉框数据\n    getJgList(row){\n      getGfXsdwJgList({ssbdz:row.ddid}).then(res=>{\n        this.jgmcList = res.data;\n        res.data.forEach(item=>{\n          this.jgMap.set(item.value,item.lx);\n          this.jgDataMap.set(item.value,item.label);\n        })\n      })\n    },\n    //获取间隔名称下拉框数据\n    getJgList1(val){\n      getGfXsdwJgList({ssbdz:val}).then(res=>{\n        this.jgmcList = res.data;\n        res.data.forEach(item=>{\n          this.jgMap.set(item.value,item.lx);\n          this.jgDataMap.set(item.value,item.label);\n        })\n      })\n    },\n    //详情按钮\n    async getDetail(row) {\n      await this.getListZb(row)\n      this.getBdzAndPds(row.zy)\n      await this.getJgList(row);\n      await this.getSblxAll(row);\n      this.title = '巡视点位详情查看'\n      this.form = { ...row }\n      this.isDisabled = true\n      this.isShowDetails = true\n      this.isShowSbmc = true\n      this.ZbDialogFormVisible = false\n    },\n    //保存按钮\n    async saveRow() {\n      this.propTableData.colFirst.forEach(item=>{\n        if(item.sblx){\n          item.sblx = item.sblx.join(',');\n        }\n        item.sbmc = this.jgDataMap.get(item.sbid);//手动设置设备名称\n      })\n      this.form.dd = this.ddMap.get(this.form.ddid);\n      this.form.colFirst = this.propTableData.colFirst\n      this.form.objIdList = this.ids\n      let { code } = await saveOrUpdate(this.form)\n      if (code === '0000') {\n        this.$message.success('操作成功')\n      }\n      this.isShowDetails = false\n      //重置page页从1开始\n      this.tableAndPageInfo.pager.pageResize = 'Y'\n      await this.getData()\n\n    },\n    //删除按钮\n    async deleteRow(id) {\n      // if (this.ids.length < 1) {\n      //   this.$message.warning('请选择正确的数据！！！')\n      //   return\n      // }\n      let obj=[];\n        obj.push(id);\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        remove(obj).then(({ code }) => {\n          if (code === '0000') {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            })\n            //重置page页从1开始\n            this.tableAndPageInfo.pager.pageResize = 'Y'\n            this.getData()\n          } else {\n            this.$message({\n              type: 'error',\n              message: '删除失败!'\n            })\n          }\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        })\n      })\n    },\n    /*-----------------------------子表----------------------------*/\n    //表格新增\n    listFirstAdd() {\n      let row = {\n        objId: '',\n        sbmc: ''\n      }\n      this.propTableData.colFirst.push(row)\n      this.propTableData.sel = row\n    },\n    //子表删除\n    listFirstDel(index, row) {\n      this.ids.push(row.objId)\n      this.propTableData.colFirst.splice(index, 1)\n    },\n    //子表增加弹框\n    ZbAdd() {\n      this.yxbh = this.$refs.dd.selected.value\n      this.zbtitle = '设备增加'\n      this.isDisabled = false\n      this.isShowSbmc = false\n      this.isFilter = false\n      this.ZbDialogFormVisible = true\n    },\n    //重置按钮\n    getReset() {\n\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false\n    },\n    //筛选条件\n    selectChange(selection) {\n      this.ids = selection.map(item => item.objId)\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n    },\n    /*搜索条件*/\n    inputFocusEvent(val) {\n      if (val.target.name === 'sbmc') {\n        this.ZbDialogFormVisible = true\n        this.isFilter = true\n      }\n    },\n    /*获取设备树*/\n    getDeviceTypeData(res) {\n      if (this.isFilter) {\n        this.filterInfo.data.sbmcArr = []\n        this.filterInfo.data.sbmc = ''\n        res.forEach(item => {\n          if (item.checked) {\n            this.filterInfo.data.sbmcArr.push(item.code)\n            this.filterInfo.data.sbmc += item.name + ','\n          }\n        })\n        this.filterInfo.data.sbmc = this.filterInfo.data.sbmc.substring(0, this.filterInfo.data.sbmc.length - 1)\n        this.ZbDialogFormVisible = false\n      } else {\n        let treeNodes = []\n        res.forEach(item => {\n          if (item.checked) {\n            treeNodes.push(item)\n          }\n        })\n        if (treeNodes.length === 1) {\n          this.form.sbmc = treeNodes[0].name\n          this.form.sbmc = treeNodes[0].code\n          this.ZbDialogFormVisible = false\n        } else {\n          this.$message.warning('请选择单条设备数据')\n        }\n      }\n    },\n    /*关闭对话框*/\n    closeDeviceTypeDialog() {\n      this.ZbDialogFormVisible = false\n    },\n    //下拉框change事件\n    handleEvent(val, val1) {\n      if (val.label === 'zy' && val.value && val.value !== '') {\n        if (val.value === '变电') {\n          getBdzSelectList({}).then(res => {\n            this.filterInfo.fieldList.map(item => {\n              if (item.value == 'dd') {\n                return item.options = res.data\n              }\n            })\n          })\n          getSblxDataListSelected({ type: '变电设备' }).then(res => {\n            this.filterInfo.fieldList.map(item => {\n              if (item.value == 'sblx') {\n                return item.options = res.data\n              }\n            })\n          })\n        } else if (val.value === '配电') {\n          getPdsTreeList({}).then(res => {\n            let pdzOption = res.data[0].children.map(item => {\n              let obj = {}\n              obj.label = item.label\n              obj.value = item.id\n              return obj\n            })\n            this.filterInfo.fieldList.map(item => {\n              if (item.value == 'dd') {\n                return item.options = pdzOption\n              }\n            })\n          })\n          getSblxDataListSelected({ type: '配电设备' }).then(res => {\n            this.filterInfo.fieldList.map(item => {\n              if (item.value == 'sblx') {\n                return item.options = res.data\n              }\n            })\n          })\n        }else if(val.value === '光伏'){\n          getGfBdzSelectList({}).then(res => {\n            this.filterInfo.fieldList.map(item => {\n              if (item.value == 'dd') {\n                return item.options = res.data\n              }\n            })\n          })\n          getSblxDataListSelected({ type: '变电设备' }).then(res => {\n            this.filterInfo.fieldList.map(item => {\n              if (item.value == 'sblx') {\n                return item.options = res.data\n              }\n            })\n          })\n        }\n      }\n    },\n    //获取变电站和配电室\n    getBdzAndPds(val) {\n      this.$set(this.form, 'dd', '')\n      this.$set(this.form, 'sblx', '')\n      if (val === '变电') {\n        getBdzSelectList({}).then(res => {\n          this.ddList = res.data;\n          res.data.forEach(item=>{\n            this.ddMap.set(item.value, item.label);\n          })\n        })\n        this.getSblxDataListSelected('变电设备')\n      } else if (val === '配电') {\n        getPdsTreeList({}).then(res => {\n          this.ddList = res.data[0].children.map(item => {\n            let obj = {}\n            obj.label = item.label\n            obj.value = item.id\n            return obj\n          })\n        })\n        this.getSblxDataListSelected('配电设备')\n      }else if(val === '光伏'){\n        getGfBdzSelectList({}).then(res => {\n          this.ddList = res.data;\n          res.data.forEach(item=>{\n            this.ddMap.set(item.value, item.label);\n          })\n        })\n        this.getSblxDataListSelected('光伏设备')\n      }\n    },\n    /**\n     * 获取设备类型下拉框数据\n     */\n    getSblxDataListSelected(value) {\n      getSblxDataListSelected({ type: value }).then(res => {\n        this.sblxOptionsDataSelected = res.data\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"css\" scoped>\n/*控制input输入框边框是否显示*/\n.elInput >>> .el-input__inner {\n  border: 0;\n}\n\n.box-card {\n  margin-bottom: 2vh !important;\n}\n\n.el-select {\n  width: 100%;\n}\n\n</style>\n\n"]}]}