{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\jxbzk\\jxxmwh.vue?vue&type=style&index=0&id=04596c93&scoped=true&lang=css&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\jxbzk\\jxxmwh.vue", "mtime": 1706897322435}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLnByb2R1Y3RHcm91cFNlbGVjdG9yLWdyb3VwIHsKICBwYWRkaW5nLWJvdHRvbTogMDsKICBwYWRkaW5nLXRvcDogMzJweDsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgZmxleC13cmFwOiB3cmFwOwogIHdpZHRoOiA0MDBweDsKfQoKL2RlZXAvIC5lbC1zZWxlY3QtZ3JvdXBfX3RpdGxlIHsKICBmb250LXNpemU6IDI0cHg7Cn0K"}, {"version": 3, "sources": ["jxxmwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4hBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "jxxmwh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/jxbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      @handleReset=\"getReset\"\n      @handleEvent=\"handleEvent\"\n    />\n    <div>\n      <el-white class=\"button-group\">\n        <div class=\"button_btn\">\n          <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"getInster\"\n            >新增</el-button\n          >\n        </div>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"selectChange\"\n          height=\"65vh\"\n        >\n          <el-table-column\n            slot=\"table_eight\"\n            align=\"center\"\n            fixed=\"right\"\n            style=\"display: block\"\n            label=\"操作\"\n            width=\"160\"\n            :resizable=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                @click=\"updateRow(scope.row)\"\n                v-show=\"scope.row.createBy == currentUser\"\n                type=\"text\"\n                size=\"small\"\n                title=\"修改\"\n                class=\"el-icon-edit\"\n              ></el-button>\n              <el-button\n                @click=\"getInfo(scope.row)\"\n                type=\"text\"\n                size=\"small\"\n                title=\"详情\"\n                class=\"el-icon-view\"\n              ></el-button>\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                v-show=\"scope.row.createBy == currentUser\"\n                @click=\"deleteRow(scope.row)\"\n                title=\"删除\"\n                class=\"el-icon-delete\"\n              ></el-button>\n            </template>\n          </el-table-column>\n        </comp-table>\n      </el-white>\n      <!-- 详情/新增/修改 -->\n      <el-dialog\n        :title=\"title\"\n        :visible.sync=\"isShowDetails\"\n        width=\"40%\"\n        @close=\"handleClose\"\n        v-dialogDrag\n      >\n        <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n          <el-row :gutter=\"8\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item v-show=\"false\" label=\"设备分类:\" prop=\"sbfl\">\n                <el-input v-model=\"form.sbfl\"></el-input>\n                <el-input v-model=\"form.objId\"></el-input>\n              </el-form-item>\n              <el-form-item label=\"设备分类：\" prop=\"sbfl\">\n                <el-select\n                  placeholder=\"请选择设备分类\"\n                  v-model=\"form.sbfl\"\n                  :disabled=\"isDisabled\"\n                  clearable\n                  filterable\n                  style=\"width: 100%\"\n                >\n                  <el-option-group\n                    v-for=\"group in DevicesListGroup\"\n                    :key=\"group.label\"\n                    :label=\"group.label\"\n                  >\n                    <el-option\n                      v-for=\"item in group.sbDataList\"\n                      :key=\"item.code\"\n                      :label=\"item.name\"\n                      :value=\"item.code\"\n                    >\n                    </el-option>\n                  </el-option-group>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"检修分类：\" prop=\"jxfl\">\n                <el-select\n                  placeholder=\"请选择检修分类\"\n                  v-model=\"form.jxfl\"\n                  clearable\n                  :disabled=\"isDisabled\"\n                  style=\"width: 100%\"\n                >\n                  <el-option\n                    v-for=\"item in jxflList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"项目编号：\" prop=\"xmbh\">\n                <el-input\n                  placeholder=\"请输入项目编号\"\n                  v-model=\"form.xmbh\"\n                  :disabled=\"isDisabled\"\n                />\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"24\">\n              <el-form-item label=\"检修项目：\" prop=\"jxxm\">\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"form.jxxm\"\n                  placeholder=\"请输入检修项目\"\n                  :disabled=\"isDisabled\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n        <div slot=\"footer\">\n          <el-button @click=\"close\">取 消</el-button>\n          <el-button\n            v-if=\"title == '检修项目新增' || title == '检修项目修改'\"\n            type=\"primary\"\n            @click=\"saveRow\"\n            >确 认</el-button\n          >\n        </div>\n      </el-dialog>\n\n      <!-- <el-dialog\n        :append-to-body=\"true\"\n        title=\"设备分类\"\n        :visible.sync=\"showDeviceTree\"\n        width=\"400px\"\n        v-if=\"showDeviceTree\">\n        <device-tree\n          @getDeviceTypeData=\"getDeviceTypeData\"\n          @closeDeviceTypeDialog=\"closeDeviceTypeDialog\">\n        </device-tree>\n      </el-dialog> -->\n    </div>\n  </div>\n</template>\n<script>\nimport {\n  getList,\n  remove,\n  saveOrUpdate\n} from \"@/api/dagangOilfield/bzgl/jxbzk/jxxmwh\";\n// import DeviceTree from '@/views/dagangOilfield/bzgl/sbbzk/deviceTree'\nimport { getDictTypeData } from \"@/api/system/dict/data\";\nimport { getDeviceClassGroup } from \"@/api/dagangOilfield/bzgl/sblxwh/sblxwh\";\nexport default {\n  name: \"jxxmwh\",\n  // components: { DeviceTree },\n  data() {\n    return {\n      currentUser: this.$store.getters.name,\n      //form表单\n      form: {},\n      //是否显示弹框\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //表单数据\n      dataTable: [],\n      options: [\n        { label: \"全部\", value: \"\" },\n        { label: \"测试1\", value: \"1\" },\n        { label: \"测试2\", value: \"0\" }\n      ],\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          sbfl: \"\",\n          jxfl: \"\",\n          jxflArr: [],\n          xmbh: \"\",\n          xmmc: \"\"\n        },\n        fieldList: [\n          {\n            label: \"设备分类\",\n            value: \"sbfl\",\n            type: \"selectGroupjxxmwh\",\n            clearable: true,\n            filterable: true,\n            multiple: true,\n            options: []\n          },\n          {\n            label: \"项目编号\",\n            value: \"xmbh\",\n            type: \"input\",\n            clearable: true\n          },\n          {\n            label: \"检修项目\",\n            value: \"jxxm\",\n            type: \"input\",\n            clearable: true\n          },\n          /*{\n            label: '检修分类',\n            value: 'jxflArr',\n            type: 'select',\n            // type: 'checkbox',\n            // checkboxValue: [],\n            multiple: true,\n            options: [\n              { label: '全部', value: '' },\n              { label: '测试1', value: '1' },\n              { label: '测试2', value: '0' }\n            ],\n            clearable: true\n          },*/\n          {\n            label: \"检修分类\",\n            value: \"jxfl\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            multiple: true,\n            options: [],\n            clearable: true\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"设备分类\", prop: \"sbflmc\", minWidth: \"80\" },\n          { label: \"检修分类\", prop: \"jxflName\", minWidth: \"80\" },\n          { label: \"项目编号\", prop: \"xmbh\", minWidth: \"100\" },\n          { label: \"检修项目\", prop: \"jxxm\", minWidth: \"150\" }\n          /*  {\n            prop: 'operation',\n            label: '操作',\n            minWidth: '130px',\n            style: { display: 'block' },\n            //操作列固定再右侧\n            fixed: 'right',\n            operation: [\n              { name: '修改', clickFun: this.updateRow },\n              { name: '详情', clickFun: this.getInfo }\n            ]\n          }*/\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        sbflArr: [],\n        jxflArr: [],\n        xmbh: \"\",\n        xmmc: \"\"\n      },\n      selectRows: [],\n      //检修分类下拉框数据\n      jxflList: [],\n      DevicesListGroup: [],\n      rules: {\n        sbfl: [\n          { required: true, message: \"设备分类不能为空\", trigger: \"change\" }\n        ],\n        jxfl: [\n          { required: true, message: \"检修分类不能为空\", trigger: \"change\" }\n        ],\n        xmbh: [\n          { required: true, message: \"项目编号不能为空\", trigger: \"blur\" }\n        ],\n        jxxm: [{ required: true, message: \"检修项目不能为空\", trigger: \"blur\" }]\n      },\n      showDeviceTree: false,\n      isFilter: false\n    };\n  },\n  mounted() {\n    this.initDomain();\n    this.getDeviceClassGroup();\n  },\n  watch: {\n    isShowDetails(val) {\n      if (val) {\n        const el = this.$el.querySelector(\".el-dialog\");\n        el.style.left = 0;\n        el.style.top = 0;\n      }\n    }\n  },\n  created() {\n    //列表查询\n    this.getData();\n  },\n  methods: {\n    //列表查询\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        this.params = param;\n        // param.sbflArr = param.sbfl === '' ? [] : param.sbflArr\n        const { data, code } = await getList(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n\n          this.tableAndPageInfo.tableData.forEach(item => {\n            this.jxflList.forEach(element => {\n              if (item.jxfl === element.value) {\n                item.jxflName = element.label;\n                return;\n              }\n            });\n          });\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //重置按钮\n    getReset() {\n      this.params = {\n        sbflArr: [],\n        jxflArr: [],\n        jxfl: [],\n        xmbh: \"\",\n        xmmc: \"\"\n      };\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n    },\n    //选中行\n    handleSelectionChange() {},\n    //详情\n    getDetails() {\n      this.title = \"检修项目详情\";\n      this.isDisabled = true;\n      this.isShowDetails = true;\n      this.form = { ...row };\n    },\n    //新增\n    getInster() {\n      this.title = \"检修项目新增\";\n      this.isDisabled = false;\n      this.form = {};\n      this.isShowDetails = true;\n    },\n    //编辑按钮\n    updateRow(row) {\n      this.title = \"检修项目修改\";\n      this.isDisabled = false;\n      this.form = { ...row };\n      this.isShowDetails = true;\n    },\n    //详情按钮\n    getInfo(row) {\n      this.title = \"详情查看\";\n      this.form = { ...row };\n      this.isDisabled = true;\n      this.isShowDetails = true;\n    },\n    async saveRow() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          saveOrUpdate(this.form).then(res => {\n            try {\n              if (res.code === \"0000\") {\n                this.$message.success(\"操作成功\");\n              }\n            } catch (e) {\n              console.log(e);\n            }\n            this.getData();\n          });\n        } else {\n          return false;\n        }\n        this.isShowDetails = false;\n      });\n    },\n    //删除按钮\n    async deleteRow(row) {\n      this.form = { ...row };\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove([this.form.objId]).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n      this.getData();\n    },\n    //filter_change事件\n    handleEvent(val, val1) {\n      //变电站查询下拉框选项根据所选分公司带出来\n      if (val.label === \"sbfl\" && val.value) {\n        this.params.sbflArr = val.value;\n        console.log(\"sbflArr\", this.params);\n      }\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false;\n    },\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n    showDeviceTreeDialog() {\n      this.isFilter = false;\n      this.showDeviceTree = true;\n    },\n    async initDomain() {\n      let { data: jxfl } = await getDictTypeData(\"jxfl\");\n      this.jxflList = jxfl;\n\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.value === \"jxfl\") {\n          item.options = jxfl;\n          return;\n        }\n      });\n    },\n    getDeviceClassGroup() {\n      getDeviceClassGroup([\"bdsb\", \"pdsb\", \"sdsb\"]).then(res => {\n        if (res.code === \"0000\") {\n          this.DevicesListGroup = res.data;\n          this.filterInfo.fieldList.forEach(item => {\n            if (item.value === \"sbfl\") {\n              item.options = res.data;\n              return;\n            }\n          });\n        } else {\n          this.$message.error(\"获取设备分类失败\");\n        }\n      });\n    },\n    inputFocusEvent(val) {\n      if (val.target.name === \"sbfl\") {\n        this.showDeviceTree = true;\n        this.isFilter = true;\n      }\n    },\n    getDeviceTypeData(res) {\n      if (this.isFilter) {\n        this.filterInfo.data.sbflArr = [];\n        this.filterInfo.data.sbfl = \"\";\n        res.forEach(item => {\n          if (item.checked) {\n            this.filterInfo.data.sbflArr.push(item.code);\n            this.filterInfo.data.sbfl += item.name + \",\";\n          }\n        });\n\n        this.filterInfo.data.sbfl = this.filterInfo.data.sbfl.substring(\n          0,\n          this.filterInfo.data.sbfl.length - 1\n        );\n        this.showDeviceTree = false;\n      } else {\n        let treeNodes = [];\n        res.forEach(item => {\n          if (item.checked) {\n            treeNodes.push(item);\n          }\n        });\n        if (treeNodes.length === 1) {\n          this.form.sbflmc = treeNodes[0].name;\n          this.form.sbfl = treeNodes[0].code;\n          this.showDeviceTree = false;\n        } else {\n          this.$message.warning(\"请选择单条设备数据\");\n        }\n      }\n    },\n    closeDeviceTypeDialog() {\n      this.showDeviceTree = false;\n    },\n    //清空表单数据\n    handleClose() {\n      this.form = {};\n      this.$nextTick(() => {\n        this.form = this.$options.data().form;\n        this.resetForm(\"form\");\n      });\n    }\n  }\n};\n</script>\n\n<style scoped>\n.productGroupSelector-group {\n  padding-bottom: 0;\n  padding-top: 32px;\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  width: 400px;\n}\n\n/deep/ .el-select-group__title {\n  font-size: 24px;\n}\n</style>\n"]}]}