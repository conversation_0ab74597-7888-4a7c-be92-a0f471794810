{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\asset\\fsss_gf.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\asset\\fsss_gf.js", "mtime": 1730102411342}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0UGFnZSA9IGdldFBhZ2U7CmV4cG9ydHMuc2F2ZU9yVXBkYXRlID0gc2F2ZU9yVXBkYXRlOwpleHBvcnRzLnJlbW92ZSA9IHJlbW92ZTsKCnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwoKdmFyIGJhc2VVcmwgPSAiL21hbmFnZXItYXBpL0dmZnNzcyI7IC8v5YiG6aG15p+l6K+i5YiX6KGoCgpmdW5jdGlvbiBnZXRQYWdlKHBhcmFtcykgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3RQb3N0KGJhc2VVcmwgKyAnL2dldFBhZ2UnLCBwYXJhbXMsIDEpOwp9IC8vIOa3u+WKoOaIluS/ruaUuQoKCmZ1bmN0aW9uIHNhdmVPclVwZGF0ZShwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy9zYXZlT3JVcGRhdGUnLCBwYXJhbXMsIDEpOwp9IC8vIOWIoOmZpAoKCmZ1bmN0aW9uIHJlbW92ZShwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy9kZWxCeUlkcycsIHBhcmFtcywgMSk7Cn0="}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagangOilfield/asset/fsss_gf.js"], "names": ["baseUrl", "getPage", "params", "api", "requestPost", "saveOrUpdate", "remove"], "mappings": ";;;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,qBAAhB,C,CAEA;;AACO,SAASC,OAAT,CAAiBC,MAAjB,EAAyB;AAC9B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,UAA1B,EAAsCE,MAAtC,EAA8C,CAA9C,CAAP;AACD,C,CACD;;;AACO,SAASG,YAAT,CAAsBH,MAAtB,EAA8B;AACnC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,eAAxB,EAAwCE,MAAxC,EAA+C,CAA/C,CAAP;AACD,C,CACD;;;AACO,SAASI,MAAT,CAAgBJ,MAAhB,EAAwB;AAC7B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,WAAxB,EAAoCE,MAApC,EAA2C,CAA3C,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\r\nconst baseUrl = \"/manager-api/Gffsss\";\r\n\r\n//分页查询列表\r\nexport function getPage(params) {\r\n  return api.requestPost(baseUrl + '/getPage', params, 1);\r\n}\r\n// 添加或修改\r\nexport function saveOrUpdate(params) {\r\n  return api.requestPost(baseUrl+'/saveOrUpdate',params,1)\r\n}\r\n// 删除\r\nexport function remove(params) {\r\n  return api.requestPost(baseUrl+'/delByIds',params,1)\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n"]}]}