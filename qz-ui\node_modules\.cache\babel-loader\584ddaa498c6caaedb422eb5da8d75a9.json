{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\tablePdf.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\tablePdf.vue", "mtime": 1706897323692}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IHsKICBuYW1lOiAndGFibGVQZGYnLAogIHByb3BzOiB7CiAgICBiYXNpY0RhdGE6IHsKICAgICAgdHlwZTogT2JqZWN0LAogICAgICByZXF1aXJlZDogdHJ1ZQogICAgfSwKICAgIHRhYmxlYm94MjogewogICAgICB0eXBlOiBTdHJpbmcsCiAgICAgIHJlcXVpcmVkOiBmYWxzZSwKICAgICAgZGVmYXVsdDogJycKICAgIH0sCiAgICB0YWJsZWJveDM6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICByZXF1aXJlZDogZmFsc2UsCiAgICAgIGRlZmF1bHQ6ICcnCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBnZXRQYXJhbXM6IGZ1bmN0aW9uIGdldFBhcmFtcygpIHsKICAgICAgdmFyIGlucHV0X2gyID0gZG9jdW1lbnQuZ2V0RWxlbWVudHNCeUNsYXNzTmFtZSgiaDJfdGFibGUiKTsKICAgICAgdmFyIGlucHV0X2gzID0gZG9jdW1lbnQuZ2V0RWxlbWVudHNCeUNsYXNzTmFtZSgiaDNfdGFibGUiKTsKICAgICAgdmFyIGgyRGF0YSA9IFtdOyAvL+esrOS6jOS4quihqOagvOaVsOaNrgoKICAgICAgdmFyIGgzRGF0YSA9IFtdOyAvL+esrOS4ieS4quihqOagvOaVsOaNrgoKICAgICAgZm9yICh2YXIgaSA9IDA7IGkgPCBpbnB1dF9oMi5sZW5ndGg7IGkrKykgewogICAgICAgIGgyRGF0YS5wdXNoKGlucHV0X2gyW2ldLmlkICsgInwiICsgaW5wdXRfaDJbaV0udmFsdWUpOwogICAgICB9CgogICAgICBmb3IgKHZhciBfaSA9IDA7IF9pIDwgaW5wdXRfaDMubGVuZ3RoOyBfaSsrKSB7CiAgICAgICAgaDNEYXRhLnB1c2goaW5wdXRfaDNbX2ldLmlkICsgInwiICsgaW5wdXRfaDNbX2ldLnZhbHVlKTsKICAgICAgfQoKICAgICAgcmV0dXJuIHsKICAgICAgICB0YWJsZTI6IGgyRGF0YSwKICAgICAgICB0YWJsZTM6IGgzRGF0YQogICAgICB9OwogICAgfQogIH0KfTsKZXhwb3J0cy5kZWZhdWx0ID0gX2RlZmF1bHQ7"}, {"version": 3, "sources": ["tablePdf.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAgEA;AACA,EAAA,IAAA,EAAA,UADA;AAEA,EAAA,KAAA,EAAA;AACA,IAAA,SAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,QAAA,EAAA;AAFA,KADA;AAKA,IAAA,SAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,QAAA,EAAA,KAFA;AAGA,MAAA,OAAA,EAAA;AAHA,KALA;AAUA,IAAA,SAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,QAAA,EAAA,KAFA;AAGA,MAAA,OAAA,EAAA;AAHA;AAVA,GAFA;AAkBA,EAAA,OAAA,EAAA;AACA,IAAA,SADA,uBACA;AACA,UAAA,QAAA,GAAA,QAAA,CAAA,sBAAA,CAAA,UAAA,CAAA;AACA,UAAA,QAAA,GAAA,QAAA,CAAA,sBAAA,CAAA,UAAA,CAAA;AACA,UAAA,MAAA,GAAA,EAAA,CAHA,CAGA;;AACA,UAAA,MAAA,GAAA,EAAA,CAJA,CAIA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,QAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,EAAA,GAAA,GAAA,GAAA,QAAA,CAAA,CAAA,CAAA,CAAA,KAAA;AACA;;AACA,WAAA,IAAA,EAAA,GAAA,CAAA,EAAA,EAAA,GAAA,QAAA,CAAA,MAAA,EAAA,EAAA,EAAA,EAAA;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,CAAA,EAAA,GAAA,GAAA,GAAA,QAAA,CAAA,EAAA,CAAA,CAAA,KAAA;AACA;;AACA,aAAA;AACA,QAAA,MAAA,EAAA,MADA;AAEA,QAAA,MAAA,EAAA;AAFA,OAAA;AAIA;AAhBA;AAlBA,C", "sourcesContent": ["<template>\n  <div v-loading=\"!basicData\">\n    <div class=\"saveCont\">\n      <h3 style=\"font-size: 26px;text-align: center\">{{ basicData.symb }}</h3>\n      <div class=\"saveCont1\">\n        <!--  第一个表格内容   -->\n        <div class=\"printTitle\">一、基本信息</div>\n        <table class=\"h1_table\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n          <tr>\n            <td>变电站</td>\n            <td>{{ this.basicData.sydd }}</td>\n            <td>委托单位</td>\n            <td>{{this.basicData.wtdw}}</td>\n            <td>试验单位</td>\n            <td>{{this.basicData.sydwmc}}</td>\n            <td>运行编号</td>\n            <td>{{ this.basicData.yxbh }}</td>\n          </tr>\n          <tr>\n            <td>试验性质</td>\n            <td>{{ this.basicData.syxz }}</td>\n            <td>试验日期</td>\n            <td>{{ this.basicData.syrq }}</td>\n            <td>试验人员</td>\n            <td>{{ this.basicData.syryid }}</td>\n            <td>试验地点</td>\n            <td>{{ this.basicData.sydd }}</td>\n          </tr>\n          <tr>\n            <td>报告日期</td>\n            <td>{{this.basicData.createTime.substring(0,10)}}</td>\n            <td>编写人</td>\n            <td>{{ this.basicData.bzrid }}</td>\n            <td>审核人</td>\n            <td>{{ this.basicData.bzzsp }}</td>\n            <td>批准人</td>\n            <td>{{ this.basicData.ssdwldmc }}</td>\n          </tr>\n          <tr>\n            <td>试验天气</td>\n            <td style=\"min-width: 60px;\">{{ this.basicData.tq }}</td>\n            <td>环境温度（℃）</td>\n            <td style=\"min-width: 60px;\">{{ this.basicData.wd }}</td>\n            <td>环境相对湿度（%）</td>\n            <td style=\"min-width: 60px;\">{{ this.basicData.sd }}</td>\n            <td>投运日期</td>\n            <td style=\"min-width: 60px;\">{{ this.basicData.tyrq }}</td>\n          </tr>\n        </table>\n        <!--  第二个表格内容   -->\n        <div class=\"printTitle\">二、设备铭牌</div>\n        <table class=\"h2_table\" style=\"width: 100%; margin-top: 2px; border-collapse: collapse;\"\n               v-html=\"tablebox2\">\n\n              </table>\n        <!--  第三个表格内容   -->\n        <div class=\"printTitle\">三、试验数据</div>\n        <table class=\"h3_table\" style=\"width: 100%; margin-top: 2px; border-collapse: collapse;\"\n               v-html=\"tablebox3\"></table>\n      </div>\n    </div>\n  </div>\n</template>\n<script>\nexport default {\n  name: 'tablePdf',\n  props: {\n    basicData: {\n      type: Object,\n      required: true\n    },\n    tablebox2: {\n      type: String,\n      required: false,\n      default: ''\n    },\n    tablebox3: {\n      type: String,\n      required: false,\n      default: ''\n    }\n  },\n  methods: {\n    getParams(){\n      let input_h2 = document.getElementsByClassName(\"h2_table\");\n      let input_h3 = document.getElementsByClassName(\"h3_table\");\n      let h2Data = []; //第二个表格数据\n      let h3Data = []; //第三个表格数据\n      for (let i = 0; i < input_h2.length; i++) {\n        h2Data.push(input_h2[i].id + \"|\" + input_h2[i].value);\n      }\n      for (let i = 0; i < input_h3.length; i++) {\n        h3Data.push(input_h3[i].id + \"|\" + input_h3[i].value);\n      }\n      return {\n        table2: h2Data,\n        table3: h3Data\n      };\n    },\n  },\n}\n\n</script>\n<style scoped lang=\"scss\">\n\n.saveCont {\n  width: 99.5%;\n  overflow: auto;\n  padding: 0 40px;\n  .printTitle {\n    line-height: 35px;\n\n  }\n\n  .h1_table {\n    width: 100%;\n    text-align: center;\n    border-right:1px solid #000;\n    border-bottom: 1px solid #000;\n\n    tr {\n      line-height: 35px;\n\n      td {\n        border-left: 1px solid #000;\n        border-top: 1px solid #000\n      }\n    }\n  }\n\n  .h2_table,\n  .h3_table {\n    border-bottom: 1px solid #000;\n\n    tr {\n\n      //td:nth-child(1){\n      //  border-left:none;\n      //}\n      td {\n        border-left: 1px solid #000;\n        border-top: 1px solid #000\n      }\n    }\n\n    text-align: center;\n  }\n\n\n  /deep/ .h2_table tr,\n  /deep/ .h3_table tr {\n    height: 35px;\n  }\n\n  /deep/ .h2_table td,\n  /deep/ .h3_table td {\n    border: 1px solid #000\n  }\n\n  /deep/ .h2_table input,\n  /deep/ .h3_table input {\n    display: inline-block;\n    height: 35px;\n  }\n}\n\n\n</style>>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk"}]}