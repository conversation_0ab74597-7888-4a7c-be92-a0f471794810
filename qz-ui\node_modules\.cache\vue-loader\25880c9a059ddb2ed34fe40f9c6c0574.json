{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\equipmentComponents.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\equipmentComponents.vue", "mtime": 1706897322893}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBEaWFsb2dGb3JtIGZyb20gJ2NvbS9kaWFsb2dGcm9tL2RpYWxvZ0Zvcm0nCmltcG9ydCB7CiAgZGVsZXRlRXF1aXBtZW50Q29tcG9uZW50cywKICBnZXRFcXVpcG1lbnRDb21wb25lbnRzLAogIHNhdmVFcXVpcG1lbnRDb21wb25lbnRzCn0gZnJvbSAnQC9hcGkvZGFnYW5nT2lsZmllbGQvYnpnbC9zYmx4d2gvc2JiaicKCmV4cG9ydCBkZWZhdWx0IHsKICBwcm9wczogewogICAgZGV2aWNlVHlwZURhdGE6IHsKICAgICAgdHlwZTogT2JqZWN0CiAgICB9CiAgfSwKICBuYW1lOiAnZXF1aXBtZW50Q29tcG9uZW50cycsCiAgY29tcG9uZW50czogeyBEaWFsb2dGb3JtIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHJlbWluZGVyOiAn5paw5aKeJywKICAgICAgcm93OiAyLAogICAgICB0YWJsZURhdGE6IFtdLAogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICB0b3RhbDogMCwKICAgICAgICBzYmx4Ym06ICcnCiAgICAgIH0sCiAgICAgIGZvcm1MaXN0OiBbCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICfpg6jku7bnvJbnoIHvvJonLAogICAgICAgICAgdmFsdWU6ICcnLAogICAgICAgICAgdHlwZTogJ2lucHV0JywKICAgICAgICAgIG5hbWU6ICdiamJtJywKICAgICAgICAgIGRlZmF1bHQ6IHRydWUsCiAgICAgICAgICBydWxlczogeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpemDqOS7tue8lueggScgfQogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICfpg6jku7blkI3np7DvvJonLAogICAgICAgICAgdmFsdWU6ICcnLAogICAgICAgICAgdHlwZTogJ2lucHV0JywKICAgICAgICAgIG5hbWU6ICdiam1jJywKICAgICAgICAgIGRlZmF1bHQ6IHRydWUsCiAgICAgICAgICBydWxlczogeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpemDqOS7tuWQjeensCcgfQogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICfljZXkvY3vvJonLAogICAgICAgICAgdmFsdWU6ICcnLAogICAgICAgICAgdHlwZTogJ2lucHV0JywKICAgICAgICAgIG5hbWU6ICdkdycsCiAgICAgICAgICBkZWZhdWx0OiB0cnVlLAogICAgICAgICAgcnVsZXM6IHsgcmVxdWlyZWQ6IGZhbHNlLCBtZXNzYWdlOiAn6K+36L6T5YWl5Y2V5L2NJyB9CiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+iuvuWkh+exu+Wei+WQjeensO+8micsCiAgICAgICAgICB2YWx1ZTogJycsCiAgICAgICAgICB0eXBlOiAnZGlzYWJsZWQnLAogICAgICAgICAgbmFtZTogJ3NibHhtYycsCiAgICAgICAgICBkZWZhdWx0OiB0cnVlLAogICAgICAgICAgcnVsZXM6IHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXlsZ7mgKflkI3np7AnIH0KICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5o6S5bqP77yaJywKICAgICAgICAgIHZhbHVlOiAnJywKICAgICAgICAgIHR5cGU6ICdpbnB1dCcsCiAgICAgICAgICBuYW1lOiAncHgnLAogICAgICAgICAgZGVmYXVsdDogdHJ1ZSwKICAgICAgICAgIHJ1bGVzOiB7IHJlcXVpcmVkOiBmYWxzZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeaOkuW6jycgfQogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICfmj4/ov7DvvJonLAogICAgICAgICAgdmFsdWU6ICcnLAogICAgICAgICAgdHlwZTogJ3RleHRhcmVhJywKICAgICAgICAgIG5hbWU6ICdtcycsCiAgICAgICAgICBkZWZhdWx0OiB0cnVlLAogICAgICAgICAgcnVsZXM6IHsgcmVxdWlyZWQ6IGZhbHNlLCBtZXNzYWdlOiAn6K+36L6T5YWl5o+P6L+wJyB9CiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+Wkh+azqO+8micsCiAgICAgICAgICB2YWx1ZTogJycsCiAgICAgICAgICB0eXBlOiAndGV4dGFyZWEnLAogICAgICAgICAgbmFtZTogJ2J6JywKICAgICAgICAgIGRlZmF1bHQ6IHRydWUsCiAgICAgICAgICBydWxlczogeyByZXF1aXJlZDogZmFsc2UsIG1lc3NhZ2U6ICfor7fovpPlhaXlpIfms6gnIH0KICAgICAgICB9LAoKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ2lk77yaJywKICAgICAgICAgIHZhbHVlOiAnJywKICAgICAgICAgIHR5cGU6ICdpbnB1dCcsCiAgICAgICAgICBuYW1lOiAnaWQnLAogICAgICAgICAgZGVmYXVsdDogdHJ1ZSwKICAgICAgICAgIGhpZGRlbjogZmFsc2UsCiAgICAgICAgICBydWxlczogeyByZXF1aXJlZDogZmFsc2UsIG1lc3NhZ2U6ICfor7fovpPlhaVpZCcgfQogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICforr7lpIfnsbvlnovnvJbnoIHvvJonLAogICAgICAgICAgdmFsdWU6ICcnLAogICAgICAgICAgdHlwZTogJ2lucHV0JywKICAgICAgICAgIG5hbWU6ICdzYmx4Ym0nLAogICAgICAgICAgZGVmYXVsdDogdHJ1ZSwKICAgICAgICAgIGhpZGRlbjogZmFsc2UsCiAgICAgICAgICBydWxlczogeyByZXF1aXJlZDogZmFsc2UsIG1lc3NhZ2U6ICfor7fovpPlhaXorr7lpIfnsbvlnovnvJbnoIEnIH0KICAgICAgICB9CgogICAgICBdLAogICAgICAvL+mAieS4reihjOaVsOaNrgogICAgICBzZWxlY3RlZFJvd0RhdGE6IFtdCiAgICB9CiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCkKICB9LAogIG1ldGhvZHM6IHsKICAgIC8v6I635Y+W6K6+5aSH6YOo5Lu25pWw5o2uCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQogICAgICBjb25zb2xlLmxvZygiLS0tdGhpcy5kZXZpY2VUeXBlRGF0YS0tIit0aGlzLmRldmljZVR5cGVEYXRhKTsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zYmx4Ym0gPSB0aGlzLmRldmljZVR5cGVEYXRhLnNibHhibQogICAgICBnZXRFcXVpcG1lbnRDb21wb25lbnRzKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLnRhYmxlRGF0YSA9IHJlcy5kYXRhLnJlY29yZHMKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnRvdGFsID0gcmVzLmRhdGEudG90YWwKICAgICAgfSkKICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UKICAgIH0sCiAgICAvL+aWsOWinuiuvuWkh+mDqOS7tgogICAgYWRkRXF1aXBtZW50Q29tcG9uZW50cygpIHsKICAgICAgdGhpcy5yZW1pbmRlciA9ICfmlrDlop4nCiAgICAgIHRoaXMuZm9ybUxpc3QgPSB0aGlzLiRvcHRpb25zLmRhdGEoKS5mb3JtTGlzdAogICAgICBjb25zdCBhZGRGb3JtID0gdGhpcy5mb3JtTGlzdC5tYXAoaXRlbSA9PiB7CiAgICAgICAgaWYgKGl0ZW0ubmFtZSA9PT0gJ3NibHhibScpIHsKICAgICAgICAgIGl0ZW0udmFsdWUgPSB0aGlzLmRldmljZVR5cGVEYXRhLnNibHhibQogICAgICAgIH0KICAgICAgICBpZiAoaXRlbS5uYW1lID09PSAnc2JseG1jJykgewogICAgICAgICAgaXRlbS52YWx1ZSA9IHRoaXMuZGV2aWNlVHlwZURhdGEuc2JseAogICAgICAgIH0KICAgICAgICByZXR1cm4gaXRlbQogICAgICB9KQogICAgICB0aGlzLiRyZWZzLmRpYWxvZ0Zvcm0uc2hvd3p6YyhhZGRGb3JtKQogICAgfSwKICAgIC8v5L+u5pS56K6+5aSH6YOo5Lu2CiAgICB1cGRhdGVFcXVpcG1lbnRDb21wb25lbnRzKHJvdykgewogICAgICBjb25zdCB1cGRhdGVMaXN0ID0gdGhpcy5mb3JtTGlzdC5tYXAoaXRlbSA9PiB7CiAgICAgICAgaXRlbS52YWx1ZSA9IHJvd1tpdGVtLm5hbWVdCiAgICAgICAgcmV0dXJuIGl0ZW0KICAgICAgfSkKICAgICAgdGhpcy5yZW1pbmRlciA9ICfkv67mlLknCiAgICAgIHRoaXMuJHJlZnMuZGlhbG9nRm9ybS5zaG93enpjKHVwZGF0ZUxpc3QpCiAgICB9LAogICAgLy/mn6XnnIvor6bmg4UKICAgIHNob3dEZXRhaWwocm93KSB7CiAgICAgIGNvbnN0IGluZm9MaXN0ID0gdGhpcy5mb3JtTGlzdC5tYXAoaXRlbSA9PiB7CiAgICAgICAgaXRlbS52YWx1ZSA9IHJvd1tpdGVtLm5hbWVdCiAgICAgICAgcmV0dXJuIGl0ZW0KICAgICAgfSkKICAgICAgdGhpcy5yZW1pbmRlciA9ICfor6bmg4UnCiAgICAgIHRoaXMuJHJlZnMuZGlhbG9nRm9ybS5zaG93eHEoaW5mb0xpc3QpCiAgICB9LAogICAgLy/mibnph4/liKDpmaTorr7lpIfpg6jku7YKICAgIGRlbGV0ZUVxdWlwbWVudENvbXBvbmVudHMoKSB7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruiupOWIoOmZpOmAieS4reaVsOaNriwg5piv5ZCm57un57utPycsICfmj5DnpLonLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKCgpID0+IHsKCiAgICAgICAgbGV0IGlkcyA9IFtdCiAgICAgICAgdGhpcy5zZWxlY3RlZFJvd0RhdGEuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIGlkcy5wdXNoKGl0ZW0uaWQpCiAgICAgICAgfSkKCiAgICAgICAgZGVsZXRlRXF1aXBtZW50Q29tcG9uZW50cyhpZHMpLnRoZW4ocmVzID0+IHsKICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gJzAwMDAnKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Yig6Zmk5oiQ5YqfJykKICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aTjeS9nOWksei0pScpCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ2luZm8nLAogICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iOWIoOmZpCcKICAgICAgICB9KQogICAgICB9KQogICAgfSwKICAgIC8v5L+d5a2Y6K6+5aSH6YOo5Lu25pWw5o2uCiAgICBzYXZlRXF1aXBtZW50Q29tcG9uZW50cyhmb3JtRGF0YSkgewogICAgICBsZXQgbWVzc2FnZSA9ICcnCiAgICAgIGlmIChmb3JtRGF0YS5pZCA9PT0gJycgfHwgIWZvcm1EYXRhLmlkKSB7CiAgICAgICAgbWVzc2FnZSA9ICfmlrDlop7miJDlip8nCiAgICAgIH0gZWxzZSB7CiAgICAgICAgbWVzc2FnZSA9ICfkv67mlLnmiJDlip8nCiAgICAgIH0KICAgICAgc2F2ZUVxdWlwbWVudENvbXBvbmVudHMoZm9ybURhdGEpLnRoZW4ocmVzID0+IHsKICAgICAgICBpZiAocmVzLmNvZGUgPT09ICcwMDAwJykgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKG1lc3NhZ2UpCiAgICAgICAgICB0aGlzLmdldExpc3QoKQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmk43kvZzlpLHotKUnKQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICAvL+ihjOmAieS4reS6i+S7tgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHJvdykgewogICAgICB0aGlzLnNlbGVjdGVkUm93RGF0YSA9IHJvdwogICAgfQoKICB9Cn0K"}, {"version": 3, "sources": ["equipmentComponents.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "equipmentComponents.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbbzk", "sourcesContent": ["<template>\n  <div>\n    <el-white>\n      <el-white class=\"button-group\">\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"addEquipmentComponents\">新增</el-button>\n        <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"deleteEquipmentComponents\">删除</el-button>\n      </el-white>\n      <el-table\n        stripe\n        border\n        v-loading=\"loading\"\n        :data=\"tableData\"\n        @selection-change=\"handleSelectionChange\">\n        <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n        <el-table-column label=\"部件编码\" align=\"center\" prop=\"bjbm\"/>\n        <el-table-column label=\"部件名称\" align=\"center\" prop=\"bjmc\"/>\n        <el-table-column label=\"描述\" align=\"center\" prop=\"ms\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"设备类型名称\" align=\"center\" prop=\"sblxmc\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"操作\" fixed=\"right\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"180\">\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" @click=\"updateEquipmentComponents(scope.row)\">修改</el-button>\n            <el-button type=\"text\" @click=\"showDetail(scope.row)\">详情</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      <pagination\n        v-show=\"queryParams.total>0\"\n        :total=\"queryParams.total\"\n        :page.sync=\"queryParams.pageNum\"\n        :limit.sync=\"queryParams.pageSize\"\n        @pagination=\"getList\"/>\n    </el-white>\n\n    <dialog-form\n      ref=\"dialogForm\"\n      :append-to-body=\"true\"\n      label-width=\"150px\"\n      out-width=\"50%\"\n      :reminder=\"reminder\"\n      :rows=\"row\"\n      @save=\"saveEquipmentComponents\"\n    />\n  </div>\n</template>\n\n<script>\nimport DialogForm from 'com/dialogFrom/dialogForm'\nimport {\n  deleteEquipmentComponents,\n  getEquipmentComponents,\n  saveEquipmentComponents\n} from '@/api/dagangOilfield/bzgl/sblxwh/sbbj'\n\nexport default {\n  props: {\n    deviceTypeData: {\n      type: Object\n    }\n  },\n  name: 'equipmentComponents',\n  components: { DialogForm },\n  data() {\n    return {\n      reminder: '新增',\n      row: 2,\n      tableData: [],\n      loading: false,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        total: 0,\n        sblxbm: ''\n      },\n      formList: [\n        {\n          label: '部件编码：',\n          value: '',\n          type: 'input',\n          name: 'bjbm',\n          default: true,\n          rules: { required: true, message: '请输入部件编码' }\n        },\n        {\n          label: '部件名称：',\n          value: '',\n          type: 'input',\n          name: 'bjmc',\n          default: true,\n          rules: { required: true, message: '请输入部件名称' }\n        },\n        {\n          label: '单位：',\n          value: '',\n          type: 'input',\n          name: 'dw',\n          default: true,\n          rules: { required: false, message: '请输入单位' }\n        },\n        {\n          label: '设备类型名称：',\n          value: '',\n          type: 'disabled',\n          name: 'sblxmc',\n          default: true,\n          rules: { required: true, message: '请输入属性名称' }\n        },\n        {\n          label: '排序：',\n          value: '',\n          type: 'input',\n          name: 'px',\n          default: true,\n          rules: { required: false, message: '请输入排序' }\n        },\n        {\n          label: '描述：',\n          value: '',\n          type: 'textarea',\n          name: 'ms',\n          default: true,\n          rules: { required: false, message: '请输入描述' }\n        },\n        {\n          label: '备注：',\n          value: '',\n          type: 'textarea',\n          name: 'bz',\n          default: true,\n          rules: { required: false, message: '请输入备注' }\n        },\n\n        {\n          label: 'id：',\n          value: '',\n          type: 'input',\n          name: 'id',\n          default: true,\n          hidden: false,\n          rules: { required: false, message: '请输入id' }\n        },\n        {\n          label: '设备类型编码：',\n          value: '',\n          type: 'input',\n          name: 'sblxbm',\n          default: true,\n          hidden: false,\n          rules: { required: false, message: '请输入设备类型编码' }\n        }\n\n      ],\n      //选中行数据\n      selectedRowData: []\n    }\n  },\n  mounted() {\n    this.getList()\n  },\n  methods: {\n    //获取设备部件数据\n    getList() {\n      this.loading = false\n      console.log(\"---this.deviceTypeData--\"+this.deviceTypeData);\n      this.queryParams.sblxbm = this.deviceTypeData.sblxbm\n      getEquipmentComponents(this.queryParams).then(res => {\n        this.tableData = res.data.records\n        this.queryParams.total = res.data.total\n      })\n      this.loading = false\n    },\n    //新增设备部件\n    addEquipmentComponents() {\n      this.reminder = '新增'\n      this.formList = this.$options.data().formList\n      const addForm = this.formList.map(item => {\n        if (item.name === 'sblxbm') {\n          item.value = this.deviceTypeData.sblxbm\n        }\n        if (item.name === 'sblxmc') {\n          item.value = this.deviceTypeData.sblx\n        }\n        return item\n      })\n      this.$refs.dialogForm.showzzc(addForm)\n    },\n    //修改设备部件\n    updateEquipmentComponents(row) {\n      const updateList = this.formList.map(item => {\n        item.value = row[item.name]\n        return item\n      })\n      this.reminder = '修改'\n      this.$refs.dialogForm.showzzc(updateList)\n    },\n    //查看详情\n    showDetail(row) {\n      const infoList = this.formList.map(item => {\n        item.value = row[item.name]\n        return item\n      })\n      this.reminder = '详情'\n      this.$refs.dialogForm.showxq(infoList)\n    },\n    //批量删除设备部件\n    deleteEquipmentComponents() {\n      this.$confirm('确认删除选中数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n\n        let ids = []\n        this.selectedRowData.forEach(item => {\n          ids.push(item.id)\n        })\n\n        deleteEquipmentComponents(ids).then(res => {\n          if (res.code === '0000') {\n            this.$message.success('删除成功')\n            this.getList()\n          } else {\n            this.$message.error('操作失败')\n          }\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        })\n      })\n    },\n    //保存设备部件数据\n    saveEquipmentComponents(formData) {\n      let message = ''\n      if (formData.id === '' || !formData.id) {\n        message = '新增成功'\n      } else {\n        message = '修改成功'\n      }\n      saveEquipmentComponents(formData).then(res => {\n        if (res.code === '0000') {\n          this.$message.success(message)\n          this.getList()\n        } else {\n          this.$message.error('操作失败')\n        }\n      })\n    },\n    //行选中事件\n    handleSelectionChange(row) {\n      this.selectedRowData = row\n    }\n\n  }\n}\n</script>\n\n<style scoped>\n.pagination-container {\n  position: relative;\n  height: 25px;\n  margin-bottom: 20px !important;\n  margin-top: 15px;\n  padding: 10px 20px !important;\n}\n</style>\n"]}]}