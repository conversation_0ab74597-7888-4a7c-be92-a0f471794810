{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\sybwk.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\sybwk.js", "mtime": 1706897314369}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuc2F2ZU9yVXBkYXRlID0gc2F2ZU9yVXBkYXRlOwpleHBvcnRzLmdldFNibHhUcmVlID0gZ2V0U2JseFRyZWU7CmV4cG9ydHMuZ2V0UGFnZURhdGFMaXN0ID0gZ2V0UGFnZURhdGFMaXN0OwpleHBvcnRzLnJlbW92ZSA9IHJlbW92ZTsKCnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwoKdmFyIGJhc2VVcmwgPSAiL21hbmFnZXItYXBpIjsKLyoqCiAqIOa3u+WKoOaIluS/ruaUueaWueazlQogKiBAcGFyYW0gcXVlcnkKICogQHJldHVybnMge1Byb21pc2UgfCBQcm9taXNlPHVua25vd24+fQogKi8KCmZ1bmN0aW9uIHNhdmVPclVwZGF0ZShxdWVyeSkgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3RQb3N0KGJhc2VVcmwgKyAnL3N5Yncvc2F2ZU9yVXBkYXRlJywgcXVlcnksIDIpOwp9Ci8qKgogKgogKiDojrflj5bor5Xpqozpg6jkvY3lt6bkvqfmoJHnu5PmnoQKICogQHBhcmFtIHF1ZXJ5CiAqIEByZXR1cm5zIHtQcm9taXNlIHwgUHJvbWlzZTxhbnk+fQogKi8KCgpmdW5jdGlvbiBnZXRTYmx4VHJlZShxdWVyeSkgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3RHZXQoYmFzZVVybCArICcvc3lidy9nZXRTYmx4VHJlZUluZm8nLCBxdWVyeSwgMik7Cn0KLyoqCiAqIOafpeivouaWueazlQogKiBAcGFyYW0gcXVlcnkKICogQHJldHVybnMge1Byb21pc2UgfCBQcm9taXNlPHVua25vd24+fQogKi8KCgpmdW5jdGlvbiBnZXRQYWdlRGF0YUxpc3QocXVlcnkpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy9zeWJ3L2dldFN5YndEYXRhTGlzdEJ5UGFnZScsIHF1ZXJ5LCAyKTsKfQovKioqCiAqIOWIoOmZpAogKiBAcGFyYW0gcXVlcnkKICogQHJldHVybnMge1Byb21pc2UgfCBQcm9taXNlPHVua25vd24+fQogKi8KCgpmdW5jdGlvbiByZW1vdmUocXVlcnkpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy9zeWJ3L3JlbW92ZVN5YndEYXRhJywgcXVlcnksIDIpOwp9"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagang<PERSON>ilfield/bzgl/sybwk.js"], "names": ["baseUrl", "saveOrUpdate", "query", "api", "requestPost", "getSblxTree", "requestGet", "getPageDataList", "remove"], "mappings": ";;;;;;;;;;;;AAAA;;AAEA,IAAMA,OAAO,GAAG,cAAhB;AAGA;;;;;;AAKO,SAASC,YAAT,CAAsBC,KAAtB,EAA6B;AAClC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,oBAA1B,EAAgDE,KAAhD,EAAuD,CAAvD,CAAP;AACD;AAED;;;;;;;;AAOO,SAASG,WAAT,CAAqBH,KAArB,EAA4B;AACjC,SAAOC,iBAAIG,UAAJ,CAAeN,OAAO,GAAG,uBAAzB,EAAkDE,KAAlD,EAAyD,CAAzD,CAAP;AACD;AAGD;;;;;;;AAKO,SAASK,eAAT,CAAyBL,KAAzB,EAAgC;AACrC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,6BAA1B,EAAyDE,KAAzD,EAAgE,CAAhE,CAAP;AACD;AAID;;;;;;;AAKO,SAASM,MAAT,CAAgBN,KAAhB,EAAuB;AAC5B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,sBAA1B,EAAkDE,KAAlD,EAAyD,CAAzD,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\n\nconst baseUrl = \"/manager-api\";\n\n\n/**\n * 添加或修改方法\n * @param query\n * @returns {Promise | Promise<unknown>}\n */\nexport function saveOrUpdate(query) {\n  return api.requestPost(baseUrl + '/sybw/saveOrUpdate', query, 2);\n}\n\n/**\n *\n * 获取试验部位左侧树结构\n * @param query\n * @returns {Promise | Promise<any>}\n */\n\nexport function getSblxTree(query) {\n  return api.requestGet(baseUrl + '/sybw/getSblxTreeInfo', query, 2);\n}\n\n\n/**\n * 查询方法\n * @param query\n * @returns {Promise | Promise<unknown>}\n */\nexport function getPageDataList(query) {\n  return api.requestPost(baseUrl + '/sybw/getSybwDataListByPage', query, 2);\n}\n\n\n\n/***\n * 删除\n * @param query\n * @returns {Promise | Promise<unknown>}\n */\nexport function remove(query) {\n  return api.requestPost(baseUrl + '/sybw/removeSybwData', query, 2)\n}\n\n"]}]}