{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\asset\\assetGqj.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\asset\\assetGqj.js", "mtime": 1730275668825}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagangOilfield/asset/assetGqj.js"], "names": ["baseUrl", "getList", "params", "api", "requestPost", "saveOrUpdate", "remove", "exportExcel", "fileName", "saveOrUpdateYxSyRecords", "deleteYxSyRecords", "ids", "getYxSyRecords", "saveOrUpdateAssetGqjJxRecords", "deleteAssetGqjJxRecords", "getAssetGqjJxRecords", "getGfBdzSelectList", "param"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;;AAEA,IAAMA,OAAO,GAAG,cAAhB,C,CAEA;;AACO,SAASC,OAAT,CAAiBC,MAAjB,EAAyB;AAC9B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,wBAA1B,EAAoDE,MAApD,EAA4D,CAA5D,CAAP;AACD,C,CAED;;;AACO,SAASG,YAAT,CAAsBH,MAAtB,EAA8B;AACnC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,mBAA1B,EAA+CE,MAA/C,EAAuD,CAAvD,CAAP;AACD,C,CAED;;;AACO,SAASI,MAAT,CAAgBJ,MAAhB,EAAwB;AAC7B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,aAA1B,EAAyCE,MAAzC,EAAiD,CAAjD,CAAP;AACD;;AAEM,SAASK,WAAT,CAAqBL,MAArB,EAA4BM,QAA5B,EAAqC;AAC1C,SAAOL,iBAAII,WAAJ,CAAgBP,OAAO,GAAC,kBAAxB,EAA2CE,MAA3C,EAAkDM,QAAlD,EAA2D,IAA3D,CAAP;AACD;AAGD;;;;;;;;AAMO,SAASC,uBAAT,CAAiCP,MAAjC,EAAyC;AAC9C,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,sCAA1B,EAAkEE,MAAlE,EAA0E,CAA1E,CAAP;AACD;AAED;;;;;;;;AAMO,SAASQ,iBAAT,CAA2BC,GAA3B,EAAgC;AACrC,SAAOR,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,gCAA1B,EAA4DW,GAA5D,EAAiE,CAAjE,CAAP;AACD;AAED;;;;;;;;AAMO,SAASC,cAAT,CAAwBV,MAAxB,EAAgC;AACrC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,6BAA1B,EAAyDE,MAAzD,EAAiE,CAAjE,CAAP;AACD;AAED;;;;;;;;AAMO,SAASW,6BAAT,CAAuCX,MAAvC,EAA+C;AACpD,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,kDAA1B,EAA8EE,MAA9E,EAAsF,CAAtF,CAAP;AACD;AAED;;;;;;;;AAMO,SAASY,uBAAT,CAAiCH,GAAjC,EAAsC;AAC3C,SAAOR,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,4CAA1B,EAAwEW,GAAxE,EAA6E,CAA7E,CAAP;AACD;AAED;;;;;;;;AAMO,SAASI,oBAAT,CAA8Bb,MAA9B,EAAsC;AAC3C,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,yCAA1B,EAAqEE,MAArE,EAA6E,CAA7E,CAAP;AACD;AAED;;;;;AAGO,SAASc,kBAAT,CAA4BC,KAA5B,EAAmC;AACxC,SAAOd,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,+BAAxB,EAAwDiB,KAAxD,EAA8D,CAA9D,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\n\nconst baseUrl = '/manager-api'\n\n// 查询\nexport function getList(params) {\n  return api.requestPost(baseUrl + '/gqj/getDataListByPage', params, 1)\n}\n\n// 添加或修改\nexport function saveOrUpdate(params) {\n  return api.requestPost(baseUrl + '/gqj/saveOrUpdate', params, 1)\n}\n\n// 删除\nexport function remove(params) {\n  return api.requestPost(baseUrl + '/gqj/remove', params, 1)\n}\n\nexport function exportExcel(params,fileName){\n  return api.exportExcel(baseUrl+'/gqj/exportExcel',params,fileName,true)\n}\n\n\n/**\n * 新增,修改运行试验记录表数据\n * @param params 运行试验记录表数据\n * @returns {Promise | Promise<unknown>}\n * @createBy jiazw\n */\nexport function saveOrUpdateYxSyRecords(params) {\n  return api.requestPost(baseUrl + '/yxSyRecords/saveOrUpdateYxSyRecords', params, 1)\n}\n\n/**\n * 批量删除运行试验记录表数据\n * @param ids id集合\n * @returns {Promise | Promise<unknown>}\n * @createBy jiazw\n */\nexport function deleteYxSyRecords(ids) {\n  return api.requestPost(baseUrl + '/yxSyRecords/deleteYxSyRecords', ids, 1)\n}\n\n/**\n * 获取运行试验记录表数据\n * @param params 查询条件\n * @returns {Promise | Promise<unknown>}\n * @createBy jiazw\n */\nexport function getYxSyRecords(params) {\n  return api.requestPost(baseUrl + '/yxSyRecords/getYxSyRecords', params, 1)\n}\n\n/**\n * 新增或修改工器具检修记录数据\n * @param params 工器具检修记录数据\n * @returns {Promise | Promise<unknown>}\n * @createBy jiazw\n */\nexport function saveOrUpdateAssetGqjJxRecords(params) {\n  return api.requestPost(baseUrl + '/assetGqjJxRecords/saveOrUpdateAssetGqjJxRecords', params, 1)\n}\n\n/**\n * 批量删除工器具检修记录数据\n * @param ids 工器具检修记录id集合\n * @returns {Promise | Promise<unknown>}\n * @createBy jiazw\n */\nexport function deleteAssetGqjJxRecords(ids) {\n  return api.requestPost(baseUrl + '/assetGqjJxRecords/deleteAssetGqjJxRecords', ids, 1)\n}\n\n/**\n * 获取工器具检修记录数据集合\n * @param params 查询条件\n * @returns {Promise | Promise<unknown>}\n * @createBy jiazw\n */\nexport function getAssetGqjJxRecords(params) {\n  return api.requestPost(baseUrl + '/assetGqjJxRecords/getAssetGqjJxRecords', params, 1)\n}\n\n/**\n * 光伏变电站下拉框数据查询\n * */\nexport function getGfBdzSelectList(param) {\n  return api.requestPost(baseUrl+'/equipList/getGfBdzSelectList',param,1)\n}\n"]}]}