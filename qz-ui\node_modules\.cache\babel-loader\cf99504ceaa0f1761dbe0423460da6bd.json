{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\sbztpjbzk\\jxyzwh.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\sbztpjbzk\\jxyzwh.js", "mtime": 1706897314223}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0RGF0YUxpc3QgPSBnZXREYXRhTGlzdDsKZXhwb3J0cy5zYXZlT3JVcGRhdGUgPSBzYXZlT3JVcGRhdGU7CmV4cG9ydHMucmVtb3ZlID0gcmVtb3ZlOwoKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Cgp2YXIgYmFzZVVybCA9ICIvY29uZGl0aW9uLW1haW50ZW5hbmNlLWFwaSI7IC8vIOafpeivogoKZnVuY3Rpb24gZ2V0RGF0YUxpc3QocGFyYW1zKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcvanhjbHdoL2dldEp4Y2x3aExpc3RQYWdlJywgcGFyYW1zLCAxKTsKfSAvL+S/neWtmOaIluiAheS/ruaUuQoKCmZ1bmN0aW9uIHNhdmVPclVwZGF0ZShwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy9qeGNsd2gvc2F2ZU9yVXBkYXRlJywgcGFyYW1zLCAxKTsKfSAvLyDliKDpmaQKCgpmdW5jdGlvbiByZW1vdmUocGFyYW1zKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcvanhjbHdoL2RlbGV0ZUpYY2x3aCcsIEpTT04uc3RyaW5naWZ5KHBhcmFtcyksIDEpOwp9"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagang<PERSON>field/bzgl/sbztpjbzk/jxyzwh.js"], "names": ["baseUrl", "getDataList", "params", "api", "requestPost", "saveOrUpdate", "remove", "JSON", "stringify"], "mappings": ";;;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,4BAAhB,C,CAEA;;AACO,SAASC,WAAT,CAAqBC,MAArB,EAA6B;AAChC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,2BAAxB,EAAoDE,MAApD,EAA2D,CAA3D,CAAP;AACH,C,CAED;;;AACO,SAASG,YAAT,CAAsBH,MAAtB,EAA6B;AAChC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,sBAAxB,EAA+CE,MAA/C,EAAsD,CAAtD,CAAP;AACH,C,CAED;;;AACO,SAASI,MAAT,CAAgBJ,MAAhB,EAAwB;AAC3B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,sBAAxB,EAA+CO,IAAI,CAACC,SAAL,CAAeN,MAAf,CAA/C,EAAsE,CAAtE,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\r\nconst baseUrl = \"/condition-maintenance-api\";\r\n\r\n// 查询\r\nexport function getDataList(params) {\r\n    return api.requestPost(baseUrl+'/jxclwh/getJxclwhListPage',params,1)\r\n}\r\n\r\n//保存或者修改\r\nexport function saveOrUpdate(params){\r\n    return api.requestPost(baseUrl+'/jxclwh/saveOrUpdate',params,1)\r\n}\r\n\r\n// 删除\r\nexport function remove(params) {\r\n    return api.requestPost(baseUrl+'/jxclwh/deleteJXclwh',JSON.stringify(params),1)\r\n  }\r\n  "]}]}