{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\hskwh.vue?vue&type=style&index=0&lang=scss&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\hskwh.vue", "mtime": 1706897323218}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5lbC10cmVlLW5vZGU6Zm9jdXMgPiAuZWwtdHJlZS1ub2RlX19jb250ZW50IHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjNjZiMWZmODcgIWltcG9ydGFudDsKfQouZWwtdHJlZS1ub2RlX19jb250ZW50OmhvdmVyIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjNjZiMWZmODc7Cn0KLmVsLXRyZWUtLWhpZ2hsaWdodC1jdXJyZW50IC5lbC10cmVlLW5vZGUuaXMtY3VycmVudCA+IC5lbC10cmVlLW5vZGVfX2NvbnRlbnQgewogIGJhY2tncm91bmQtY29sb3I6ICM2NmIxZmY4NzsKfQoKLmJveC1jYXJkIHsKICBtYXJnaW4tYm90dG9tOiAxNXB4OwoKICAuZWwtY2FyZF9faGVhZGVyIHsKICAgIGJhY2tncm91bmQtY29sb3I6IHJnYigyMzUsIDI0NSwgMjU1KSAhaW1wb3J0YW50OwogIH0KfQoKLml0ZW0gewogIHdpZHRoOiAyMDBweDsKICBoZWlnaHQ6IDE0OHB4OwogIGZsb2F0OiBsZWZ0Owp9CgouaGVhZC1jb250YWluZXIgewogIG1hcmdpbjogMCBhdXRvOwogIHdpZHRoOiA5OCU7CiAgbWF4LWhlaWdodDogNzl2aDsKICBvdmVyZmxvdzogYXV0bzsKfQo="}, {"version": 3, "sources": ["hskwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwdA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "hskwh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row>\n      <el-col :span=\"4\">\n        <el-card class=\"aside_height\" shadow=\"never\">\n          <div style=\"overflow: auto; height: 90vh\">\n            <el-col>\n              <el-tree\n                :expand-on-click-node=\"false\"\n                id=\"tree\"\n                :data=\"treeOptions\"\n                :default-expanded-keys=\"['1']\"\n                @node-click=\"handleNodeClick\"\n                node-key=\"nodeId\"\n              />\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"20\">\n        <el-white>\n          <el-white class=\"button-group\">\n            <el-filter\n              :data=\"filterInfo.data\"\n              :field-list=\"filterInfo.fieldList\"\n              :width=\"{ labelWidth: 180, itemWidth: 160 }\"\n              @handleReset=\"getReset\"\n            />\n            <el-button\n              type=\"primary\"\n              icon=\"el-icon-plus\"\n              @click=\"addSensorButton\"\n              >新增</el-button\n            >\n            <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"getDelete\"\n              >删除</el-button\n            >\n            <!-- <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleExport\"\n              >导出</el-button\n            > -->\n          </el-white>\n\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"69.6vh\"\n          />\n        </el-white>\n      </el-col>\n    </el-row>\n    <!-- 详情/新增/修改 -->\n    <el-dialog :title=\"title\" :visible.sync=\"isShowDetails\" width=\"60%\" v-dialogDrag>\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row :gutter=\"15\" class=\"pull-left\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"函数分类名称：\" prop=\"hsflmc\">\n              <el-input\n                placeholder=\"请输入函数分类名称：\"\n                v-model=\"form.hsflmc\"\n                :disabled=\"true\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"函数名称：\" prop=\"hsmc\">\n              <el-input\n                placeholder=\"请输入函数名称：\"\n                v-model=\"form.hsmc\"\n                :disabled=\"isDisabled\"\n              />\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"8\">\n            <el-form-item label=\"参数个数：\" prop=\"csgs\">\n              <el-input\n                placeholder=\"请输入参数个数\"\n                v-model=\"form.csgs\"\n                :disabled=\"isDisabled\"\n                type=\"number\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"15\" class=\"pull-left\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否试验函数：\" prop=\"issyhs\">\n              <el-select v-model=\"form.issyhs\" style=\"width: 100%\"  :disabled=\"isDisabled\">\n                <el-option\n                  v-for=\"item in issyhsList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否分析函数：\" prop=\"isfxhs\">\n              <el-select v-model=\"form.isfxhs\" style=\"width: 100%\" :disabled=\"isDisabled\">\n                <el-option\n                  v-for=\"item in isfxhsLisr\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"8\">\n            <el-form-item label=\"SQL函数：\" prop=\"issqlhs\">\n              <el-select v-model=\"form.issqlhs\" style=\"width: 100%\"  :disabled=\"isDisabled\">\n                <el-option\n                  v-for=\"item in issqlhsList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"15\" class=\"pull-left\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"自定义表达式：\" prop=\"zdybds\">\n              <el-input\n                placeholder=\"请输入自定义表达式\"\n                v-model=\"form.zdybds\"\n                :disabled=\"isDisabled\"\n              />\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"8\">\n            <el-form-item label=\"函数描述：\" prop=\"hsms\">\n              <el-input\n                placeholder=\"函数描述\"\n                v-model=\"form.hsms\"\n                :disabled=\"isDisabled\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"close\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"commitAdd\">保 存</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  getPageDataList,\n  remove,\n  saveOrUpdate,\n  ExportHskWH,\n  getTreeData,\n  getHsflkList,\n} from \"@/api/dagangOilfield/bzgl/hskwh\";\n\nexport default {\n  name: \"hskwh\",\n  data() {\n    return {\n      //form表单\n      form: {\n        hsflid: undefined,\n        hsflmc:undefined,\n        hsmc:undefined,\n        issyhs:undefined,\n      },\n      //是否显示弹框\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //表单数据\n      dataTable: [],\n      //删除选择列\n      selectRows: [],\n      //标题\n      title: \"\",\n      //函数分类名称\n      // hsflmclist: [],\n      //搜索\n      filterInfo: {\n        data: {\n          hsflmc: \"\",\n          hsmc: \"\",\n        },\n        fieldList: [\n          {\n            label: \"函数分类名称\",\n            value: \"hsflmc\",\n            type: \"input\",\n            clearable: true,\n          },\n          {\n            label: \"函数名称\",\n            value: \"hsmc\",\n            type: \"input\",\n            clearable: true,\n          },\n        ],\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"函数分类名称\", prop: \"hsflmc\" },\n          { label: \"函数名称\", prop: \"hsmc\" },\n          { label: \"函数描述\", prop: \"hsms\" },\n          { label: \"参数个数\", prop: \"csgs\" },\n          { label: \"试验函数\", prop: \"issyhss\" },\n          { label: \"分析函数\", prop: \"isfxhss\" },\n          { label: \"SQL函数\", prop: \"issqlhss\" },\n          { label: \"自定义表达式\", prop: \"zdybds\" },\n          {\n            prop: \"operation\",\n            label: \"操作\",\n            minWidth: \"100px\",\n            style: { display: \"block\" },\n            //操作列固定再右侧\n            fixed: \"right\",\n            operation: [\n              { name: \"修改\", clickFun: this.undateDetails },\n              { name: \"详情\", clickFun: this.detailsInfo },\n            ],\n          },\n        ],\n        option: { checkBox: true, serialNumber: true },\n      },\n      issyhsList: [\n        { label: \"是\", value: 1 },\n        { label: \"否\", value: 2 },\n      ],\n      issqlhsList: [\n        { label: \"是\", value: 1 },\n        { label: \"否\", value: 2 },\n      ],\n      isfxhsLisr: [\n        { label: \"是\", value: 1 },\n        { label: \"否\", value: 2 },\n      ],\n      queryHskwhParam: {\n        hsflmc: \"\",\n        pageNum: 1,\n        pageSize: 10,\n      },\n\n      //组织树\n      treeOptions: [],\n      //删除是否可用\n      multipleSensor: true,\n      //查询参数\n      queryParams: {\n        hsflmc: \"\",\n        pageNum: 1,\n        pageSize: 10,\n      },\n      //填入数据校验\n      rules: {\n        hsmc: [\n          { required: true, message: \"函数名称不能为空\", trigger: \"blur\" },\n        ],\n        issyhs: [\n          { required: true, message: \"试验函数不能为空\", trigger: \"blur\" },\n        ],\n        isfxhs: [\n          { required: true, message: \"分析函数不能为空\", trigger: \"blur\" },\n        ],\n        issqlhs: [\n          { required: true, message: \"SQL函数不能为空\", trigger: \"blur\" },\n        ],\n      },\n      //表单开关\n      isSearchShow: false,\n    };\n  },\n  watch: {},\n  created() {\n    //查询table列表\n    this.getData();\n    this.getTreeOption();\n  },\n  methods: {\n    //查询树方法\n    getTreeOption() {\n      getTreeData().then((res) => {\n        this.treeOptions = res.data;\n      });\n      // getHsflkList().then((res) => {\n      //   this.hsflmclist = res.data;\n      // });\n    },\n    //树节点点击事件\n    handleNodeClick(data) {\n      if (data.id != undefined && data.id != '0000') {\n        this.form.hsflid = data.id;\n        this.form.hsflmc = data.label;\n        this.queryHskwhParam.hsflmc = data.label;\n        this.queryParams.hsflmc = data.label;\n        this.getData();\n      }else {\n        this.queryHskwhParam.hsflmc = ''\n        this.getData();\n      }\n    },\n\n    //添加按钮\n    addSensorButton() {\n      if (this.form.hsflid === undefined) {\n        this.$message.warning(\"请在选择函数分类库后新增记录\");\n        return;\n      }\n      //打开弹窗\n      this.isShowDetails = true;\n      //表单可编辑\n      this.isDisabled = false;\n      //设置弹出框标题\n      this.title = \"新增\";\n    },\n    //修改按钮\n    undateDetails(row) {\n      //打开弹窗\n      this.isShowDetails = true;\n      //给表单赋值\n      this.form = row;\n      //表单可编辑\n      this.isDisabled = false;\n      //设置弹出框标题\n      this.title = \"修改\";\n    },\n\n    //确认提交\n    commitAdd() {\n      console.log(\"开始\" + this.form.hsflid);\n      this.$refs[\"form\"].validate((valid) => {\n        if (valid) {\n          saveOrUpdate(this.form).then((res) => {\n            if (res.code === \"0000\") {\n              this.$message.success(res.msg);\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n              this.isShowDetails = false;\n              this.form = {};\n            } else {\n              this.$message.error(res.msg);\n            }\n          });\n        }\n      });\n    },\n\n    close() {\n      this.isShowDetails = false;\n    },\n    //定义重置方法\n    getReset() {},\n    //编辑按钮\n    updateSensorButton() {\n      //打开弹窗\n      this.isShowDetails = true;\n      //给表单赋值\n      this.form = row;\n      //表单可编辑\n      this.isDisabled = false;\n      //设置弹出框标题\n      this.title = \"修改\";\n    },\n    //详情按钮\n    detailsInfo(row) {\n      this.title = \"详情\";\n      //打开弹窗\n      this.isShowDetails = true;\n      //把行数据给弹出框表单\n      this.form = { ...row };\n      //将表单不可编辑\n      this.isDisabled = true;\n    },\n\n    //删除按钮\n    getDelete() {\n      if (this.selectRows.length < 1) {\n        this.$message.warning(\"请选择正确的数据！！！\");\n        return;\n      }\n      let ids = this.selectRows.map((item) => {\n        return item.objId;\n      });\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      })\n        .then(() => {\n          remove(ids).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\",\n              });\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\",\n              });\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\",\n          });\n        });\n    },\n\n    //导出按钮\n    handleExport() {},\n\n    //行选中\n    handleSelectionChange(rows) {\n      this.selectRows = rows;\n      this.whmjzButtonDisabled = rows.length != 1;\n      //获取到当前行对象\n      this.mjzRowForm = rows[0];\n    },\n    //查询列表\n    async getData(params) {\n      try {\n        const param = { ...this.queryHskwhParam, ...params };\n        const { data, code } = await getPageDataList(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n\n    //搜索\n    handleQuery() {},\n    //重置\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n    },\n  },\n};\n</script>\n\n<style lang='scss'>\n.el-tree-node:focus > .el-tree-node__content {\n  background-color: #66b1ff87 !important;\n}\n.el-tree-node__content:hover {\n  background-color: #66b1ff87;\n}\n.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {\n  background-color: #66b1ff87;\n}\n\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  max-height: 79vh;\n  overflow: auto;\n}\n</style>\n"]}]}