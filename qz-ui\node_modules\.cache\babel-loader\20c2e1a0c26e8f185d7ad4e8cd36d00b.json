{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\pddzcz\\dzczp.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\pddzcz\\dzczp.vue", "mtime": 1748604805608}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["dzczp.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwpBA;;AACA;;AAMA;;AAMA;;AAEA;;AACA;;AACA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AANA;eAOA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,QAAA,EAAA,qBAAA;AAAA,IAAA,QAAA,EAAA,iBAAA;AAAA,IAAA,aAAA,EAAA;AAAA,GAFA;AAGA,EAAA,KAAA,EAAA;AACA,IAAA,IAAA,EAAA;AACA,MAAA,IAAA,EAAA,OADA;AAEA,MAAA,OAAA,EAAA;AAFA;AADA,GAHA;AASA,EAAA,IATA,kBASA;AACA,WAAA;AACA,MAAA,YAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,YADA;AAEA,MAAA,kBAAA,EAAA,EAFA;AAGA,MAAA,YAAA,EAAA,IAHA;AAIA,MAAA,aAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA;AACA;AACA;AACA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,EAaA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAbA,EAiBA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAjBA,EAoBA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OApBA,CAJA;AA0BA,MAAA,cAAA,EAAA,KA1BA;AA2BA,MAAA,UAAA,EAAA,EA3BA;AA4BA,MAAA,KAAA,EAAA;AACA,QAAA,GAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,QAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CADA;AAEA,QAAA,KAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,QAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAFA;AAGA;AACA;AACA;AACA;AACA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CARA,CAWA;AACA;AACA;AACA;;AAdA,OA5BA;AA4CA,MAAA,GAAA,EAAA,EA5CA;AA6CA;AACA,MAAA,SAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,CA9CA;AAkDA;AACA,MAAA,WAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,IAnDA;AAoDA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,oBAAA,EAAA,OADA;AAEA,QAAA,WAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,OAHA;AAIA,QAAA,SAAA,EAAA,EAJA;AAKA,QAAA,WAAA,EAAA,IALA;AAMA,QAAA,QAAA,EAAA,EANA;AAOA,QAAA,WAAA,EAAA;AAPA,OArDA;AA8DA;AACA,MAAA,MAAA,EAAA,KA/DA;AAgEA;AACA,MAAA,cAAA,EAAA,KAjEA;AAkEA,MAAA,MAAA,EAAA,EAlEA;AAkEA;AACA,MAAA,QAAA,EAAA,EAnEA;AAoEA,MAAA,YAAA,EAAA,KApEA;AAqEA;AACA,MAAA,cAAA,EAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAtEA;AAuEA,MAAA,OAAA,EAAA,EAvEA;AAwEA;AACA,MAAA,cAAA,EAAA,EAzEA;AA0EA;AACA,MAAA,gBAAA,EAAA,KA3EA;AA4EA;AACA,MAAA,aAAA,EAAA;AACA,QAAA,UAAA,EAAA,EADA,CACA;;AADA,OA7EA;AAgFA;AACA,MAAA,MAAA,EAAA,EAjFA;AAkFA;AACA,MAAA,OAAA,EAAA,EAnFA;AAoFA,MAAA,SAAA,EAAA;AACA,QAAA,QAAA,EAAA,EADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,UAAA,EAAA,KAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,IAAA,EAAA;AALA,OApFA;AA2FA,MAAA,SAAA,EAAA,EA3FA;AA4FA,MAAA,EAAA,EAAA,KA5FA;AA6FA;AACA,MAAA,GAAA,EAAA,EA9FA;AA+FA,MAAA,MAAA,EAAA,IA/FA;AAgGA,MAAA,QAAA,EAAA,IAhGA;AAiGA;AACA,MAAA,aAAA,EAAA;AACA,QAAA,GAAA,EAAA,IADA;AACA;AACA,QAAA,QAAA,EAAA;AAFA,OAlGA;AAsGA,MAAA,UAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA,SAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAAA,CAtGA;AAuGA;AACA,MAAA,IAAA,EAAA;AACA,QAAA,MAAA,EAAA,EADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA,EAJA;AAKA,QAAA,GAAA,EAAA,EALA;AAMA,QAAA,GAAA,EAAA,EANA;AAOA,QAAA,IAAA,EAAA,EAPA;AAQA,QAAA,EAAA,EAAA,CARA;AAQA;AACA,QAAA,QAAA,EAAA,EATA;AAUA,QAAA,KAAA,EAAA,EAVA;AAWA,QAAA,IAAA,EAAA,CAXA;AAYA,QAAA,OAAA,EAAA,CAZA;AAaA,QAAA,OAAA,EAAA;AAbA,OAxGA;AAuHA,MAAA,OAAA,EAAA;AACA,QAAA,KAAA,EAAA,EADA;AAEA,QAAA,EAAA,EAAA;AAFA,OAvHA;AA2HA;AACA,MAAA,aAAA,EAAA,KA5HA;AA6HA;AACA,MAAA,UAAA,EAAA,KA9HA;AA+HA;AACA,MAAA,KAAA,EAAA,EAhIA;AAiIA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,EAAA,EAAA,EADA;AAEA,UAAA,GAAA,EAAA,EAFA;AAGA,UAAA,GAAA,EAAA,EAHA;AAIA,UAAA,OAAA,EAAA,EAJA;AAKA,UAAA,OAAA,EAAA;AALA,SADA;AAQA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SADA,EAEA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,SAFA;AAGA,UAAA,IAAA,EAAA,MAHA;AAIA,UAAA,QAAA,EAAA,WAJA;AAKA,UAAA,MAAA,EAAA;AALA,SAFA,EASA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SATA,EAUA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SAVA,EAWA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,SAFA;AAGA,UAAA,IAAA,EAAA,MAHA;AAIA,UAAA,QAAA,EAAA,WAJA;AAKA,UAAA,MAAA,EAAA;AALA,SAXA,EAkBA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,KAAA,EAAA,QAFA;AAGA,UAAA,IAAA,EAAA,UAHA;AAIA,UAAA,aAAA,EAAA,EAJA;AAKA,UAAA,SAAA,EAAA,IALA;AAMA,UAAA,OAAA,EAAA,CACA;AACA,YAAA,KAAA,EAAA,GADA;AAEA,YAAA,KAAA,EAAA;AAFA,WADA;AAKA;;;;AAIA;AACA,YAAA,KAAA,EAAA,GADA;AAEA,YAAA,KAAA,EAAA;AAFA,WATA,EAaA;AACA,YAAA,KAAA,EAAA,GADA;AAEA,YAAA,KAAA,EAAA;AAFA,WAbA,EAgBA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAhBA;AANA,SAlBA;AARA,OAjIA;AAsLA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AACA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SARA,EASA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SATA,EAUA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAVA;AAZA,OAtLA;AA+MA,MAAA,UAAA,EAAA,EA/MA;AAgNA,MAAA,MAAA,EAAA;AACA;AACA,QAAA,EAAA,EAAA,CAFA;AAGA,QAAA,MAAA,EAAA,WAHA,CAGA;;AAHA,OAhNA;AAqNA;AACA,MAAA,QAAA,EAAA,SAtNA;AAuNA,MAAA,QAAA,EAAA,SAvNA;AAwNA,MAAA,IAAA,EAAA;AAxNA,KAAA;AA0NA,GApOA;AAqOA,EAAA,OArOA,qBAqOA;AACA;AACA,SAAA,MAAA,CAAA,KAAA,GAAA,qBAAA;AACA,SAAA,qBAAA,GAHA,CAIA;;AACA,SAAA,OAAA,CAAA,KAAA,MAAA,CAAA,KAAA;AACA,GA3OA;AA4OA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,UAFA,sBAEA,KAFA,EAEA;AAAA;;AACA,WAAA,QAAA,CAAA,0BAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAKA,IALA,qFAKA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,yBAAA;AAAA,kBAAA,MAAA,EAAA,CAAA;AAAA,kBAAA,KAAA,EAAA;AAAA,iBAAA,CADA;;AAAA;AAAA;AACA,gBAAA,IADA,qBACA,IADA;;AAEA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,KAAA,CAAA,QAAA,CAAA,OAAA,CAAA,QAAA;;AACA,kBAAA,KAAA,CAAA,OAAA;AACA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OALA,IAYA,KAZA,CAYA,YAAA,CAAA,CAZA;AAaA,KAhBA;AAiBA,IAAA,WAjBA,yBAiBA;AACA,UAAA,SAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA;AAAA,kBAAA,IAAA,CAAA;AAAA,SAAA;AAAA,OAAA,CAAA;AACA,gCAAA,SAAA,EAAA,WAAA;AACA,KApBA;AAqBA,IAAA,WArBA,uBAqBA,IArBA,EAqBA,QArBA,EAqBA;AAAA;;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,IAAA;;AACA,UAAA,CAAA,QAAA,CAAA,QAAA,CAAA,MAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,UAAA;AACA,aAAA,KAAA,CAAA,MAAA,CAAA,UAAA;AACA;AACA;;AACA,kCAAA,IAAA,EACA,IADA,CACA,UAAA,IAAA,EAAA;AACA,QAAA,MAAA,CAAA,GAAA,GAAA,MAAA,CAAA,aAAA,CAAA,QAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,iBAAA,IAAA,CAAA,KAAA;AAAA,SAAA,CAAA;AACA,QAAA,MAAA,CAAA,aAAA,CAAA,QAAA,GAAA,IAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,iBAAA;AAAA,YAAA,EAAA,EAAA,IAAA,CAAA,UAAA;AAAA,YAAA,IAAA,EAAA,IAAA,CAAA,MAAA;AAAA,WAAA;AAAA,SAAA,CAAA;AACA,OAJA,EAKA,KALA,CAKA,UAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,KAAA;AACA,OAPA;AAQA,WAAA,KAAA,CAAA,MAAA,CAAA,UAAA;AACA,KArCA;AAsCA;AACA,IAAA,qBAvCA,mCAuCA;AAAA;;AACA,wCAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,kBAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KA3CA;AA4CA,IAAA,OA5CA,qBA4CA;AACA,WAAA,cAAA,GAAA,KAAA;;AACA,cAAA,KAAA,IAAA,CAAA,MAAA;AACA,aAAA,GAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,WAAA,KAAA,KAAA,IAAA,CAAA,QAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA;;AACA;;AACA,aAAA,GAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,IAAA,CAAA,KAAA,KAAA,KAAA,WAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA;;AACA;;AACA,aAAA,GAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,IAAA,CAAA,MAAA,KAAA,KAAA,WAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA;;AACA;;AACA,aAAA,GAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,IAAA,CAAA,GAAA,KAAA,KAAA,WAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA,iBAAA,YAAA,GAAA,KAAA;AACA;;AACA;AAzBA;AA2BA,KAzEA;AA0EA,IAAA,WA1EA,yBA0EA;AACA,WAAA,MAAA,GAAA;AACA;AACA,QAAA,EAAA,EAAA,CAFA;AAGA,QAAA,MAAA,EAAA,WAHA,CAGA;;AAHA,OAAA;AAKA,KAhFA;AAiFA;AACA,IAAA,WAlFA,uBAkFA,GAlFA,EAkFA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,UAFA,mCAEA,GAFA;AAAA;AAAA,uBAGA,0BAAA,UAAA,EAAA,UAAA,CAHA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAKA,gBAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,OAAA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,KAzFA;AA0FA;AACA,IAAA,MA3FA,kBA2FA,CA3FA,EA2FA;AACA,WAAA,YAAA;AACA,KA7FA;AA8FA;AACA,IAAA,aA/FA,2BA+FA;AACA,UAAA,KAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,EAAA;AACA,aAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,CAAA,WAAA;AACA;;AACA,WAAA,MAAA,GAAA,KAAA;AACA,KApGA;AAqGA;AACA,IAAA,aAtGA,2BAsGA;AACA,WAAA,YAAA,GAAA,KAAA;AACA,KAxGA;AAyGA,IAAA,YAzGA,wBAyGA,GAzGA,EAyGA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AADA;AAAA,uBAEA,8BAAA,MAAA,CAAA,WAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,sBAEA,IAFA;AAEA,gBAAA,IAFA,sBAEA,IAFA;AAGA,gBAAA,MAAA,CAAA,QAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,YAAA,GAAA,IAAA;;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,KA9GA;AA+GA;AACA,IAAA,cAhHA,0BAgHA,GAhHA,EAgHA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,WAAA,MAAA,GACA,gFACA,GAAA,CAAA,KADA,GAEA,KAFA,GAGA,IAAA,IAAA,GAAA,OAAA,EAJA;AAKA,KAxHA;AAyHA;AACA,IAAA,UA1HA,wBA0HA;AAAA;;AACA,UAAA,MAAA,GAAA,EAAA,CADA,CACA;AACA;;AACA,UAAA,SAAA,GAAA,CAAA,KAAA,EAAA,KAAA,CAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,QAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,KAAA,OAAA;AACA,UAAA,QAAA,GAAA,IAAA,QAAA,EAAA,CANA,CAOA;;AACA,WAAA,OAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,GAAA,IAAA,SAAA,EAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA;AACA;;AACA,QAAA,QAAA,CAAA,MAAA,CAAA,OAAA,EAAA,IAAA,CAAA,GAAA;AACA,OANA;AAOA,MAAA,QAAA,CAAA,MAAA,CAAA,YAAA,EAAA,KAAA,aAAA,CAAA,UAAA,EAfA,CAeA;;AACA,MAAA,QAAA,CAAA,MAAA,CAAA,QAAA,EAAA,MAAA,EAhBA,CAgBA;;AACA,MAAA,QAAA,CAAA,MAAA,CAAA,MAAA,EAAA,SAAA,EAjBA,CAiBA;;AACA,uBACA,WADA,CACA,2BADA,EACA,QADA,EACA,CADA,EAEA,IAFA,CAEA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,YAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA,EAFA,CAGA;;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,EAAA,CAJA,CAKA;;AACA,QAAA,MAAA,CAAA,OAAA;AACA,OATA,EAUA,KAVA,CAUA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,SAAA;AACA,OAZA;AAaA,KAzJA;AA0JA;AACA,IAAA,YA3JA,wBA2JA,IA3JA,EA2JA,QA3JA,EA2JA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,KA7JA;AA8JA,IAAA,cA9JA,0BA8JA,KA9JA,EA8JA,IA9JA,EA8JA,QA9JA,EA8JA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,KAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,MAAA,EAAA,IAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,QAAA;AACA,KAlKA;AAmKA;AACA,IAAA,YApKA,wBAoKA,IApKA,EAoKA,QApKA,EAoKA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,KAtKA;AAuKA;AACA,IAAA,wBAxKA,oCAwKA,IAxKA,EAwKA;AACA,WAAA,cAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA;AACA,WAAA,gBAAA,GAAA,IAAA;AACA,KA3KA;AA4KA,IAAA,OA5KA,qBA4KA;AACA,WAAA,gBAAA,GAAA,KAAA;AACA,KA9KA;AA+KA;AACA,IAAA,cAhLA,4BAgLA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,WAAA,EAAA,GAAA,IAAA;AACA,KAnLA;AAoLA;AACA,IAAA,oBArLA,gCAqLA,GArLA,EAqLA;AACA,UAAA,GAAA,EAAA;AACA,aAAA,IAAA,CAAA,OAAA,GAAA,KAAA,IAAA,CAAA,IAAA;AACA,aAAA,IAAA,CAAA,OAAA,GAAA,CAAA;AACA,OAHA,MAGA;AACA,aAAA,IAAA,CAAA,OAAA,GAAA,KAAA,IAAA,CAAA,IAAA;AACA,aAAA,IAAA,CAAA,OAAA,GAAA,CAAA;AACA;;AACA,WAAA,aAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,IAAA,GAAA,GAAA;AACA,OAFA;AAGA,KAhMA;AAiMA,IAAA,mBAjMA,+BAiMA,KAjMA,EAiMA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,KAAA;;AACA,UAAA,KAAA,IAAA,KAAA,OAAA,EAAA;AACA,aAAA,QAAA,GAAA,KAAA;AACA,aAAA,IAAA,GAAA,KAAA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,YAAA;AACA,OAJA,MAIA,IAAA,KAAA,IAAA,IAAA,KAAA,EAAA;AACA,aAAA,QAAA,GAAA,KAAA;AACA,aAAA,IAAA,GAAA,OAAA;AACA,YAAA,aAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA,CACA,KAAA,QADA,EAEA,CAFA,EAGA,CAHA,CAAA;AAIA,aAAA,aAAA,CAAA,QAAA,CAAA,MAAA,CAAA,KAAA,QAAA,EAAA,CAAA,EAAA,aAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,QAAA;AACA;AACA,KAjNA;AAkNA,IAAA,iBAlNA,6BAkNA,GAlNA,EAkNA;AACA,UAAA,GAAA,EAAA;AACA,UAAA,KAAA,IAAA,CAAA,OAAA;AACA,OAFA,MAEA;AACA,UAAA,KAAA,IAAA,CAAA,OAAA;AACA;;AACA,WAAA,IAAA,CAAA,OAAA,GAAA,KAAA,IAAA,CAAA,IAAA,GAAA,KAAA,IAAA,CAAA,OAAA;AACA,KAzNA;AA0NA;AACA,IAAA,UA3NA,sBA2NA,IA3NA,EA2NA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,GADA,GACA;AACA,kBAAA,KAAA,EAAA,IAAA,CAAA,WADA;AAEA,kBAAA,OAAA,EAAA,CAFA;AAGA,kBAAA,MAAA,EAAA,IAAA,CAAA,WAAA,KAAA,UAAA,GAAA,CAAA,GAAA;AAHA,iBADA;;AAAA,sBAMA,IAAA,CAAA,WAAA,KAAA,UANA;AAAA;AAAA;AAAA;;AAAA,+BAOA,IAAA,CAAA,cAPA;AAAA,kDAQA,OARA;AAAA;;AAAA;AASA,gBAAA,GAAA,CAAA,MAAA,GAAA,GAAA;AATA;;AAAA;AAAA;AAAA;;AAAA;AAAA,+BAaA,IAAA,CAAA,cAbA;AAAA,kDAcA,OAdA,yBAkBA,OAlBA,yBAsBA,IAtBA;AAAA;;AAAA;AAeA,gBAAA,GAAA,CAAA,MAAA,GAAA,GAAA;AACA,gBAAA,GAAA,CAAA,MAAA,GAAA,IAAA,CAAA,QAAA;AAhBA;;AAAA;AAmBA,gBAAA,GAAA,CAAA,MAAA,GAAA,GAAA;AACA,gBAAA,GAAA,CAAA,GAAA,GAAA,IAAA,CAAA,QAAA;AApBA;;AAAA;AAuBA,gBAAA,GAAA,CAAA,MAAA,GAAA,GAAA;AAvBA;;AAAA;AAAA;AAAA,uBA2BA,yBAAA,GAAA,CA3BA;;AAAA;AAAA;AA2BA,gBAAA,IA3BA,sBA2BA,IA3BA;;AAAA,sBA4BA,IAAA,KAAA,MA5BA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBA6BA,MAAA,CAAA,OAAA,EA7BA;;AAAA;AAAA;AAAA;;AAAA;AA+BA,gBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AA/BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiCA,KA5PA;AA6PA;AACA,IAAA,SA9PA,qBA8PA,IA9PA,EA8PA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,GADA,mCACA,MAAA,CAAA,IADA;;AAAA,sBAEA,IAAA,KAAA,UAFA;AAAA;AAAA;AAAA;;AAAA,+BAGA,GAAA,CAAA,MAHA;AAAA,kDAIA,GAJA,wBAgBA,GAhBA,yBA4BA,GA5BA;AAAA;;AAAA;AAKA,gBAAA,MAAA,CAAA,aAAA,GAAA,KAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA,CARA,CASA;;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,IAAA,GAAA,QAAA;AACA,gBAAA,MAAA,CAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,aAAA,GAAA,EAAA;AACA,gBAAA,MAAA,CAAA,MAAA,GAAA,IAAA;AAdA;;AAAA;AAiBA,gBAAA,MAAA,CAAA,aAAA,GAAA,KAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,aAAA,GAAA,EAAA,CAvBA,CAwBA;;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,IAAA,GAAA,KAAA;AACA,gBAAA,MAAA,CAAA,MAAA,GAAA,IAAA;AA1BA;;AAAA;AA6BA,gBAAA,MAAA,CAAA,IAAA,CAAA,QAAA,GAAA,MAAA,CAAA,aAAA,CAAA,QAAA;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,SAAA,GAAA,MAAA,CAAA,GAAA;AA9BA;AAAA,uBA+BA,MAAA,CAAA,KAAA,CAAA,MAAA,EAAA,QAAA;AAAA,oGAAA,kBAAA,KAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iCACA,KADA;AAAA;AAAA;AAAA;;AAEA,4BAAA,MAAA,CAAA,aAAA,CAAA,UAAA,GAAA,MAAA,CAAA,IAAA,CAAA,KAAA;;AACA,4BAAA,MAAA,CAAA,UAAA;;AAHA;AAAA,mCAIA,2BAAA,MAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kCAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,gCAAA,MAAA,CAAA,OAAA;;AACA,gCAAA,MAAA,CAAA,aAAA,GAAA,KAAA;AACA,gCAAA,MAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,gCAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AACA,gCAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gCAAA,MAAA,CAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,gCAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,KAAA;AACA,gCAAA,MAAA,CAAA,MAAA,GAAA,IAAA;AACA,+BATA,MASA;AACA,gCAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA;AACA;AACA,6BAbA,CAJA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAA;;AAAA;AAAA;AAAA;AAAA,oBA/BA;;AAAA;AAAA;AAAA;;AAAA;AAqDA,gBAAA,MAAA,CAAA,aAAA,GAAA,KAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,cAAA,CAAA,KAAA,GAAA,QAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,KAAA;AACA,gBAAA,MAAA,CAAA,MAAA,GAAA,IAAA;;AA3DA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA6DA,KA3TA;AA4TA;AACA,IAAA,OA7TA,mBA6TA,MA7TA,EA6TA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,MAAA,CAAA,MAAA,+DAAA,MAAA,CAAA,MAAA,GAAA,MAAA;AACA,gBAAA,KAHA,GAGA,MAAA,CAAA,MAHA;AAIA,gBAAA,KAAA,CAAA,OAAA,GAAA,CAAA;AAAA,kBAAA,IAAA,EAAA,YAAA;AAAA,kBAAA,GAAA,EAAA;AAAA,iBAAA,CAAA;;AACA,oBAAA,CAAA,KAAA,CAAA,MAAA,EAAA;AACA,kBAAA,KAAA,CAAA,MAAA,GAAA,WAAA;AACA;;AAPA;AAAA,uBAQA,sBAAA,KAAA,CARA;;AAAA;AAAA;AAQA,gBAAA,IARA,kBAQA,IARA;AAQA,gBAAA,IARA,kBAQA,IARA;;AASA,oBAAA,IAAA,KAAA,MAAA,EAAA;AAAA,uEACA,IAAA,CAAA,OADA;;AAAA;AAAA;AAAA,0BACA,CADA;AAEA,sBAAA,CAAA,CAAA,KAAA,GAAA,SAAA;;AACA,sBAAA,MAAA,CAAA,aAAA,CAAA,OAAA,CAAA,UAAA,OAAA,EAAA;AACA,4BAAA,CAAA,CAAA,MAAA,KAAA,OAAA,CAAA,KAAA,EAAA;AACA,0BAAA,CAAA,CAAA,QAAA,GAAA,OAAA,CAAA,KAAA;AACA;AACA,uBAJA;AAHA;;AACA,wEAAA;AAAA;AAOA;AARA;AAAA;AAAA;AAAA;AAAA;;AASA,kBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AApBA;AAAA;;AAAA;AAAA;AAAA;AAsBA,gBAAA,OAAA,CAAA,GAAA;;AAtBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAwBA,KArVA;AAsVA;AACA,IAAA,OAvVA,qBAuVA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,QAAA;AAAA,oGAAA,kBAAA,KAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA,iCACA,KADA;AAAA;AAAA;AAAA;;AAAA;AAGA;AACA,4BAAA,QAJA,GAIA,OAAA,CAAA,aAAA,CAAA,QAJA;;AAKA,iCAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,QAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,8BAAA,QAAA,CAAA,CAAA,CAAA,CAAA,EAAA,GAAA,CAAA,GAAA,CAAA;AACA;;AACA,4BAAA,OAAA,CAAA,IAAA,CAAA,QAAA,GAAA,QAAA;AACA,4BAAA,OAAA,CAAA,IAAA,CAAA,SAAA,GAAA,OAAA,CAAA,GAAA;AACA,4BAAA,OAAA,CAAA,IAAA,CAAA,MAAA,GAAA,GAAA;AAVA;AAAA,mCAWA,2BAAA,OAAA,CAAA,IAAA,CAXA;;AAAA;AAAA;AAWA,4BAAA,IAXA,uBAWA,IAXA;AAWA,4BAAA,IAXA,uBAWA,IAXA;;AAAA,kCAYA,IAAA,KAAA,MAZA;AAAA;AAAA;AAAA;;AAaA,4BAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA,EAbA,CAcA;;;AACA,4BAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;AAfA;AAAA,mCAgBA,OAAA,CAAA,OAAA,EAhBA;;AAAA;AAiBA,4BAAA,OAAA,CAAA,aAAA,GAAA,KAAA;;AAjBA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAoBA,4BAAA,OAAA,CAAA,GAAA;;AApBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAA;;AAAA;AAAA;AAAA;AAAA,oBADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyBA,KAhXA;AAiXA;AACA,IAAA,SAlXA,qBAkXA,GAlXA,EAkXA;AACA,WAAA,QAAA,CAAA,GAAA;AACA,WAAA,KAAA,GAAA,WAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,IAAA,mCAAA,GAAA,EAJA,CAKA;;AACA,WAAA,OAAA,GAAA,KAAA,IAAA,CAAA,OAAA;AACA,WAAA,YAAA,GAAA,KAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,OAAA;AACA,KA5XA;AA6XA;AACA,IAAA,OA9XA,mBA8XA,GA9XA,EA8XA;AACA,WAAA,QAAA,CAAA,GAAA;AACA,WAAA,KAAA,GAAA,WAAA;AACA,WAAA,IAAA,mCAAA,GAAA,EAHA,CAIA;;AACA,WAAA,OAAA,GAAA,KAAA,IAAA,CAAA,OAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,YAAA,GAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,OAAA;AACA,KAxYA;AAyYA;AACA,IAAA,SA1YA,uBA0YA;AACA,WAAA,aAAA,CAAA,QAAA,GAAA,EAAA;AACA,WAAA,KAAA,GAAA,WAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,IAAA,GAAA;AAAA,QAAA,GAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,QAAA;AAAA,OAAA;AACA,WAAA,IAAA,CAAA,MAAA,GAAA,IAAA,CALA,CAMA;;AACA,WAAA,IAAA,CAAA,EAAA,GAAA,CAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,KAnZA;AAoZA;AACA,IAAA,SArZA,qBAqZA,GArZA,EAqZA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,KADA,GACA,EADA;;AAEA,oBAAA,GAAA,CAAA,KAAA,EAAA;AACA,kBAAA,KAAA,GAAA,GAAA,CAAA,KAAA;AACA,iBAFA,MAEA;AACA,kBAAA,KAAA,GAAA,OAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACA;;AACA,gBAAA,OAAA,CAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAKA,IALA,CAKA,YAAA;AACA,uCAAA,KAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,wBAAA,IAAA,SAAA,IAAA;;AACA,wBAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,OAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,SADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA,EADA,CAKA;AACA;;;AACA,sBAAA,OAAA,CAAA,OAAA;AACA,qBARA,MAQA;AACA,sBAAA,OAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,OADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;AAIA;AACA,mBAfA;AAgBA,iBAtBA,EAuBA,KAvBA,CAuBA,YAAA;AACA,kBAAA,OAAA,CAAA,QAAA,CAAA;AACA,oBAAA,IAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;AAIA,iBA5BA;;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoCA,KAzbA;AA0bA;AACA,IAAA,YA3bA,wBA2bA,KA3bA,EA2bA;AACA,UAAA,GAAA,GAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,IAAA,EAAA;AAFA,OAAA;AAIA,WAAA,aAAA,CAAA,QAAA,CAAA,IAAA,CAAA,GAAA;AACA,WAAA,aAAA,CAAA,GAAA,GAAA,GAAA;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,WAAA,IAAA,CAAA,OAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,KApcA;AAqcA;AACA,IAAA,YAtcA,wBAscA,KAtcA,EAscA,GAtcA,EAscA;AACA,WAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA,KAAA;AACA,WAAA,aAAA,CAAA,QAAA,CAAA,MAAA,CAAA,KAAA,EAAA,CAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,kBAAA,EAAA,KAAA;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,WAAA,IAAA,CAAA,OAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,KA5cA;AA6cA;AACA,IAAA,KA9cA,mBA8cA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,KAhdA;AAidA;AACA,IAAA,YAldA,wBAkdA,SAldA,EAkdA;AACA,WAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,KAAA;AAAA,OAAA,CAAA;AACA,WAAA,SAAA,GAAA,SAAA;AACA,WAAA,MAAA,GAAA,SAAA,CAAA,MAAA,KAAA,CAAA;AACA,WAAA,IAAA,GAAA,KAAA,SAAA,CAAA,CAAA,CAAA;AACA,KAvdA;;AAwdA;;;AAGA,IAAA,QA3dA,oBA2dA,GA3dA,EA2dA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAEA,2BAAA;AAAA,kBAAA,KAAA,EAAA,GAAA,CAAA;AAAA,iBAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,uBAEA,IAFA;AAEA,gBAAA,IAFA,uBAEA,IAFA;;AAGA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA;AACA,kBAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,oBAAA,OAAA,CAAA,IAAA,CAAA,IAAA,EAAA,OAAA,EAAA,IAAA;AACA,mBAFA;AAGA,kBAAA,OAAA,CAAA,aAAA,CAAA,QAAA,GAAA,IAAA;AACA;;AATA;AAAA;;AAAA;AAAA;AAAA;AAWA,gBAAA,OAAA,CAAA,GAAA;;AAXA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA,KAxeA;AAyeA;AACA,IAAA,UA1eA,sBA0eA,GA1eA,EA0eA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,UAFA,mCAEA,GAFA;AAAA;AAAA,uBAGA,yBAAA,UAAA,EAAA,UAAA,EAAA,cAAA,CAHA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAKA,gBAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,OAAA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,KAjfA;AAkfA;AACA,IAAA,SAnfA,qBAmfA,GAnfA,EAmfA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,UAFA,mCAEA,GAFA;AAAA;AAAA,uBAGA,wBAAA,UAAA,EAAA,UAAA,EAAA,aAAA,CAHA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAKA,gBAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,OAAA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA;AA1fA;AA5OA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 180 }\"\n      @handleReset=\"filterReset\"\n    />\n    <el-white class=\"button-group\">\n      <div class=\"button_btn\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          @click=\"getInster\"\n          v-hasPermi=\"['pddzczp:button:add']\"\n          >新增\n        </el-button>\n        <el-button\n          type=\"danger\"\n          icon=\"el-icon-delete\"\n          v-if=\"hasSuperRole\"\n          @click=\"deleteRow\"\n          :disabled=\"single\"\n          >删除</el-button\n        >\n      </div>\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"selectChange\"\n        height=\"65vh\"\n      >\n        <!-- <el-table-column\n          prop=\"statusCn\"\n          slot=\"table_start\"\n          align=\"center\"\n          style=\"display: block\"\n          label=\"流程状态\"\n          min-width=\"120\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-badge\n              v-if=\"scope.row.isBack === 1\"\n              value=\"退回\"\n              class=\"item\"\n              type=\"danger\"\n            >\n            </el-badge>\n            <span>{{ scope.row.statusCn }}</span>\n          </template>\n        </el-table-column> -->\n        <el-table-column\n          slot=\"table_eight\"\n          align=\"center\"\n          fixed=\"right\"\n          style=\"display: block\"\n          label=\"操作\"\n          width=\"160\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              @click=\"getInfo(scope.row)\"\n              type=\"text\"\n              size=\"small\"\n              title=\"详情\"\n              class=\"el-icon-view\"\n            />\n            <el-button\n              @click=\"getUpdate(scope.row)\"\n              class=\"el-icon-edit\"\n              title=\"编辑\"\n              v-if=\"\n                (scope.row.status === '0' &&\n                  scope.row.createBy === currentUser) ||\n                  hasSuperRole\n              \"\n              type=\"text\"\n              size=\"small\"\n            />\n            <el-button\n              @click=\"deleteRow(scope.row)\"\n              class=\"el-icon-delete\"\n              title=\"删除\"\n              v-if=\"\n                scope.row.status === '0' &&\n                  scope.row.createBy === currentUser &&\n                  scope.row.isStart === 0\n              \"\n              type=\"text\"\n              size=\"small\"\n            />\n            <el-button\n                type=\"text\"\n                size=\"small\"\n                @click=\"nullifyGzp(scope.row.objId)\"\n                v-if=\"scope.row.status < 4 && scope.row.status > 0 &&\n                ( scope.row.createBy === currentUser || (scope.row.bzspr && scope.row.bzspr === currentUser) \n                || (scope.row.fgsspr && scope.row.fgsspr === currentUser) \n                || (scope.row.bjr && scope.row.bjr === currentUser)\n                || hasSuperRole )\n                \"\n                title=\"作废\"\n                class=\"el-icon-close\"\n              >\n              </el-button>\n            <el-button\n              @click=\"showTimeLine(scope.row)\"\n              v-if=\"scope.row.isStart === 1\"\n              type=\"text\"\n              size=\"small\"\n              class=\"el-icon-lcck commonIcon\"\n              title=\"流程查看\"\n            />\n            <el-button\n              @click=\"showProcessImg(scope.row)\"\n              v-if=\"scope.row.isStart === 1\"\n              type=\"text\"\n              size=\"small\"\n              class=\"el-icon-lct commonIcon\"\n              title=\"流程图\"\n            />\n            <!--              <el-button @click=\"exportWord(scope.row)\" v-if=\"scope.row.status == '待办结'\" type=\"text\" size=\"small\">\n                              导出Word\n                            </el-button>-->\n            <el-button\n              @click=\"previewFile(scope.row)\"\n              v-if=\"scope.row.status > 2\"\n              type=\"text\"\n              size=\"small\"\n              class=\"el-icon-zoom-in\"\n              title=\"预览\"\n            />\n            <el-button\n              @click=\"exportPdf(scope.row)\"\n              v-if=\"scope.row.status > 2\"\n              type=\"text\"\n              size=\"small\"\n              class=\"el-icon-pdf-export commonIcon\"\n              title=\"导出pdf\"\n            />\n          </template>\n        </el-table-column>\n      </comp-table>\n    </el-white>\n\n    <!-- 详情/新增/修改 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <div>\n          <el-row>\n            <el-col :span=\"8\">\n              <el-form-item label=\"单位：\" prop=\"fgs\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.fgs\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择单位\"\n                  disabled\n                >\n                  <el-option\n                    v-for=\"item in dwSelected\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  />\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"配电站名称：\" prop=\"bdzmc\">\n                <el-select\n                  style=\"width: 100%\"\n                  filterable\n                  v-model=\"form.bdzmc\"\n                  :disabled=\"isDisabled\"\n                  :placeholder=\"isDisabled ? '' : '请选择'\"\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in pdsOptionsDataList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"编号：\" prop=\"bm\">\n                <el-input\n                  v-model=\"form.bm\"\n                  disabled\n                  placeholder=\"确认后编号自动生成\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"发令人：\"\n                prop=\"flr\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-input\n                  v-model=\"form.flr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请输入发令人\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"受令人：\"\n                prop=\"slr\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-input\n                  v-model=\"form.slr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请输入受令人\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"发令时间：\"\n                prop=\"flsj\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-date-picker\n                  v-model=\"form.flsj\"\n                  :disabled=\"isDisabledBj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  :placeholder=\"isDisabled ? '' : '选择日期时间'\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"操作开始时间：\"\n                prop=\"kssj\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-date-picker\n                  v-model=\"form.kssj\"\n                  :disabled=\"isDisabledBj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  :placeholder=\"isDisabled ? '' : '选择日期时间'\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"操作结束时间：\"\n                prop=\"jssj\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-date-picker\n                  v-model=\"form.jssj\"\n                  :disabled=\"isDisabledBj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  :placeholder=\"isDisabled ? '' : '选择日期时间'\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"操作人：\"\n                prop=\"czr\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-input\n                  v-model=\"form.czr\"\n                  :disabled=\"isDisabledBj\"\n                  :placeholder=\"isDisabledBj ? '' : '请输入操作人'\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"监护人：\"\n                prop=\"jhr\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-input\n                  v-model=\"form.jhr\"\n                  :disabled=\"isDisabledBj\"\n                  :placeholder=\"isDisabledBj ? '' : '请输入监护人'\"\n                />\n              </el-form-item>\n            </el-col>\n            <!--            <el-col :span=\"8\">-->\n            <!--              <el-form-item label=\"审核人：\" prop=\"pdshr\">-->\n            <!--                <el-input style=\"width:100%\" :disabled=\"true\" v-model=\"form.pdshr\" placeholder=\"请输入审核人\"/>-->\n            <!--              </el-form-item>-->\n            <!--            </el-col>-->\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作项数：\" prop=\"czxs\">\n                <el-input-number\n                  v-model=\"form.czxs\"\n                  disabled\n                  placeholder=\"请输入操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"已执行项数：\" prop=\"yzxczxs\">\n                <el-input-number\n                  v-model=\"form.yzxczxs\"\n                  disabled\n                  placeholder=\"请输入已执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"未执行项数：\" prop=\"wzxczxs\">\n                <el-input-number\n                  v-model=\"form.wzxczxs\"\n                  disabled\n                  placeholder=\"请输入未执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"是否已执行：\"\n                prop=\"sfyzx\"\n                :rules=\"\n                  form.status === '3'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-select\n                  v-model=\"form.sfyzx\"\n                  :placeholder=\"isDisabled ? '' : '请选择'\"\n                  :disabled=\"isDisabledBj\"\n                >\n                  <el-option\n                    v-for=\"item in sfyzxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"操作任务：\" prop=\"czrw\">\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"form.czrw\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请输入操作任务\"\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"备注：\" prop=\"bz\">\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"form.bz\"\n                  :disabled=\"isDisabled\"\n                  :placeholder=\"isDisabled ? '' : '请输入内容'\"\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <el-col :span=\"24\">\n              <el-form-item label=\"上传图片：\" v-if=\"\">\n                <el-upload\n                  :disabled=\"isDisabledBj\"\n                  action=\"\"\n                  ref=\"uploadImg\"\n                  accept=\"image/jpeg,image/jpg,image/png\"\n                  :headers=\"header\"\n                  :multiple=\"true\"\n                  :on-change=\"handleChange\"\n                  :data=\"uploadImgData\"\n                  :file-list=\"imgList\"\n                  :auto-upload=\"false\"\n                  list-type=\"picture-card\"\n                  :on-preview=\"handlePictureCardPreview\"\n                  :on-progress=\"handleProgress\"\n                  :on-remove=\"handleRemove\"\n                >\n                  <i class=\"el-icon-plus\"></i>\n                </el-upload>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n        <!--预览的内容-->\n        <div>\n          <div align=\"left\">\n            <el-upload\n              v-if=\"!isDisabled\"\n              action=\"\"\n              ref=\"upload\"\n              accept=\".xlsx\"\n              :limit=\"1\"\n              :auto-upload=\"false\"\n              :show-file-list=\"false\"\n              :on-change=\"importExcel\"\n            >\n              <el-button type=\"info\" @click.stop=\"handleYlChange\"\n                >预览</el-button\n              >\n              <el-button\n                type=\"success\"\n                icon=\"el-icon-download\"\n                @click.stop=\"exportExcel\"\n                >导出</el-button\n              >\n              <el-button type=\"success\" icon=\"el-icon-upload\">导入</el-button>\n              <div\n                slot=\"tip\"\n                class=\"el-upload__tip\"\n                style=\"width: 220px;color: red\"\n              >\n                先导出，再导入，只能上传当前页面导出的Excel文件\n              </div>\n            </el-upload>\n          </div>\n          <el-dialog :title=\"titleyl\" :visible.sync=\"yl\" append-to-body>\n            <!--第一个表格-->\n            <el-table\n              :data=\"propTableData.colFirst\"\n              disabled\n              border\n              stripe\n              style=\"width: 100%\"\n              :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n            >\n              <el-table-column\n                width=\"80\"\n                align=\"center\"\n                prop=\"xh\"\n                label=\"序号\"\n                sortable\n              >\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"序号\"\n                      disabled\n                      v-model=\"scope.row.xh\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"请输入操作项目\"\n                      disabled\n                      v-model=\"scope.row.czrw\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-dialog>\n        </div>\n        <!--列表-->\n        <div>\n          <div align=\"right\">\n            <el-checkbox @change=\"handleCheckAllChange\" :disabled=\"isDisabledBj\"\n              >全选</el-checkbox\n            >\n          </div>\n          <!--第一个表格-->\n          <el-table\n            :data=\"propTableData.colFirst\"\n            :disabled=\"isDisabled\"\n            height=\"300\"\n            border\n            stripe\n            style=\"width: 100%\"\n          >\n            <el-table-column width=\"70\" align=\"center\" label=\"换行操作\">\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"handleCurrentChange(scope.$index)\"\n                  type=\"text\"\n                  size=\"small\"\n                  :disabled=\"isDisabled\"\n                  >{{ flag === \"start\" ? \"点击换行\" : \"换到此行\" }}</el-button\n                >\n              </template>\n            </el-table-column>\n\n            <el-table-column\n              type=\"index\"\n              width=\"60\"\n              align=\"center\"\n              label=\"顺序号\"\n            >\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入操作项目\"\n                    :disabled=\"isDisabled\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              type=\"sfwc\"\n              width=\"120\"\n              label=\"是否完成\"\n              align=\"center\"\n            >\n              <template slot-scope=\"scope\">\n                <el-checkbox\n                  v-model=\"scope.row.sfwc\"\n                  :disabled=\"isDisabledBj\"\n                  @change=\"handleCheckChange\"\n                ></el-checkbox>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" width=\"80\">\n              <template slot=\"header\" slot-scope=\"scope\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  :disabled=\"isDisabled\"\n                  icon=\"el-icon-plus\"\n                  @click=\"listFirstAdd(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n              <template slot-scope=\"scope\">\n                <el-button\n                  type=\"text\"\n                  icon=\"el-icon-delete\"\n                  :disabled=\"isDisabled\"\n                  @click=\"listFirstDel(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button\n          v-if=\"\n            isDisabled &&\n              buttonNameShow &&\n              form.status > 0 &&\n              form.status !== '3'\n          \"\n          type=\"info\"\n          @click=\"getSbFsBj('rollback')\"\n          >退 回\n        </el-button>\n        <el-button\n          v-if=\"isDisabled && buttonNameShow\"\n          type=\"success\"\n          @click=\"getSbFsBj('complete')\"\n          >{{ buttonName }}\n        </el-button>\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"saveRow\"\n          >保 存</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <!--图片放大展示弹出框-->\n    <el-image-viewer\n      v-if=\"imgDialogVisible\"\n      :on-close=\"onClose\"\n      :url-list=\"dialogImageUrl\"\n      zIndex=\"3000\"\n    />\n\n    <!--  工作流  -->\n    <activiti\n      ref=\"activiti\"\n      :processData=\"processData\"\n      :isShow=\"isShow\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n    ></activiti>\n    <time-line\n      :value=\"timeLineShow\"\n      :timeData=\"timeData\"\n      @closeTimeLine=\"colseTimeLine\"\n    />\n    <!--流程图查看-->\n    <el-dialog\n      title=\"审批流程图\"\n      :visible.sync=\"openLoadingImg\"\n      width=\"56%\"\n      append-to-body\n      :close-on-click-modal=\"false\"\n      v-dialogDrag\n    >\n      <div>\n        <img :src=\"imgSrc\" style=\"width: 100%\" />\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport {\n  getCzpmxList,\n  getList,\n  remove,\n  saveOrUpdate\n} from \"@/api/yxgl/pdyxgl/pddzczp\";\nimport {\n  exportPdf,\n  exportWord,\n  previewFile,\n  updateById\n} from \"@/api/yxgl/bdyxgl/bddzczp\";\nimport api from \"@/utils/request\";\n//流程\nimport activiti from \"com/activiti_czp\";\nimport timeLine from \"com/timeLine\";\nimport { HistoryList } from \"@/api/activiti/processTask\";\nimport { getPdsOptionsDataList } from \"@/api/dagangOilfield/asset/pdsgl\";\nimport ElImageViewer from \"element-ui/packages/image/src/image-viewer\";\nimport { exportToExcel, importFromExcel } from \"@/components/common/excel.js\";\nexport default {\n  name: \"dzczp\",\n  components: { activiti, timeLine, ElImageViewer },\n  props: {\n    isTj: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      hasSuperRole: this.$store.getters.hasSuperRole,\n      pdsOptionsDataList: [],\n      isDisabledBj: true,\n      statusOptions: [\n        {\n          value: \"0\",\n          label: \"操作票填报\"\n        },\n        // {\n        //   value: \"1\",\n        //   label: \"班组审核\"\n        // },\n        {\n          value: \"2\",\n          label: \"分公司审核\"\n        },\n        {\n          value: \"3\",\n          label: \"操作票办结\"\n        },\n        {\n          value: \"4\",\n          label: \"结束\"\n        },{ label: \"作废\", value: \"7\" }\n      ],\n      buttonNameShow: false,\n      buttonName: \"\",\n      rules: {\n        fgs: [{ required: true, message: \"单位不能为空\", trigger: \"select\" }],\n        pdzmc: [{ required: true, message: \"单位不能为空\", trigger: \"select\" }],\n        // flr: [{ required: true, message: \"发令人不能为空\", trigger: \"change\" }],\n        // slr: [{ required: true, message: \"受令人不能为空\", trigger: \"change\" }],\n        // flsj: [\n        //   { required: true, message: \"发令时间不能为空\", trigger: \"change\" }\n        // ],\n        czrw: [\n          { required: true, message: \"操作任务不能为空\", trigger: \"change\" }\n        ]\n        // czr: [{ message: \"操作人不能为空\", trigger: \"blur\" }],\n        // jhr: [{ message: \"监护人不能为空\", trigger: \"blur\" }],\n        // kssj: [{ message: \"操作开始时间不能为空\", trigger: \"change\" }],\n        // jssj: [{ message: \"操作结束时间不能为空\", trigger: \"change\" }]\n      },\n      bjr: \"\",\n      // 是否已执行下拉框\n      sfyzxList: [\n        { label: \"已执行\", value: \"已执行\" },\n        { label: \"未执行\", value: \"未执行\" }\n      ],\n      // 获取当前登录人账号\n      currentUser: this.$store.getters.name,\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"czplc\",\n        businessKey: \"\",\n        businessType: \"倒闸操作票\",\n        variables: {},\n        defaultFrom: true,\n        nextUser: \"\",\n        processType: \"complete\"\n      },\n      //工作流弹窗\n      isShow: false,\n      //流程图查看\n      openLoadingImg: false,\n      imgSrc: \"\", //流程图查看地址\n      timeData: [],\n      timeLineShow: false,\n      //弹出框标题\n      activitiOption: { title: \"\" },\n      titleyl: \"\",\n      //图片地址url\n      dialogImageUrl: \"\",\n      //展示图片dialog控制\n      imgDialogVisible: false,\n      //上传图片时的携带的其他参数\n      uploadImgData: {\n        businessId: \"\" //携带的表单主键id\n      },\n      //上传图片时的请求头\n      header: {},\n      //图片list\n      imgList: [],\n      loginForm: {\n        userName: \"\",\n        password: \"\",\n        rememberMe: false,\n        code: \"6666\",\n        uuid: \"\"\n      },\n      selection: [],\n      yl: false,\n      // 多选框选中的id\n      ids: [],\n      single: true,\n      multiple: true,\n      //弹出框中表格数据\n      propTableData: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      dwSelected: [{ label: \"配电运维分公司\", value: \"3013\" }],\n      //form表单\n      form: {\n        status: \"\",\n        kssj: \"\",\n        jssj: \"\",\n        czrw: \"\",\n        flr: \"\",\n        slr: \"\",\n        flsj: \"\",\n        lx: 3, //配电\n        colFirst: [],\n        pdzmc: \"\",\n        czxs: 0,\n        yzxczxs: 0,\n        wzxczxs: 0\n      },\n      formCzp: {\n        pdshr: \"\",\n        yj: \"\"\n      },\n      //详情弹框是否显示\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          bm: \"\",\n          slr: \"\",\n          flr: \"\",\n          czsjArr: [],\n          flsjArr: [],\n        },\n        fieldList: [\n          { label: \"编号\", value: \"bm\", type: \"input\", clearable: true },\n          {\n            label: \"操作时间\",\n            value: \"czsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"受令人\", type: \"input\", value: \"slr\", clearable: true },\n          { label: \"发令人\", type: \"input\", value: \"flr\", clearable: true },\n          {\n            label: \"发令时间\",\n            value: \"flsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          {\n            label: \"状态\",\n            value: \"status\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            clearable: true,\n            options: [\n              {\n                value: \"0\",\n                label: \"操作票填报\"\n              },\n              /*{\n                value: '1',\n                label: '班组审核'\n              },*/\n              {\n                value: \"2\",\n                label: \"分公司审核\"\n              },\n              {\n                value: \"3\",\n                label: \"操作票办结\"\n              },{ label: \"作废\", value: \"7\" }\n            ]\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"编号\", prop: \"bm\", minWidth: \"80\" },\n          { label: \"状态\", prop: \"statusCn\", minWidth: \"80\" },\n          { label: \"配电站名称\", prop: \"pdzCn\", minWidth: \"120\" },\n          { label: \"操作开始时间\", prop: \"kssj\", minWidth: \"120\" },\n          { label: \"操作结束时间\", prop: \"jssj\", minWidth: \"120\" },\n          { label: \"操作任务\", prop: \"czrw\", minWidth: \"120\" },\n          // {label: '审核人', prop: 'pdshr', minWidth: '100'},\n          { label: \"发令人\", prop: \"flr\", minWidth: \"100\" },\n          { label: \"受令人\", prop: \"slr\", minWidth: \"100\" },\n          { label: \"发令时间\", prop: \"flsj\", minWidth: \"120\" }\n        ]\n      },\n      selectRows: [],\n      params: {\n        //配电\n        lx: 3,\n        status: \"0,1,2,3,4\" //状态。将结束状态的过滤掉\n      },\n      //换行操作\n      oldIndex: undefined,\n      newIndex: undefined,\n      flag: \"start\"\n    };\n  },\n  mounted() {\n    //获取token\n    this.header.token = getToken();\n    this.getPdsOptionsDataList();\n    //获取列表数据\n    this.getData(this.$route.query);\n  },\n  methods: {\n    //作废票\n    nullifyGzp(objId) {\n      this.$confirm(\"票作废后只能查看，不能进行任何操作，确认作废吗?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(async () => {\n          let { code } = await updateById({ status: 7, objId: objId });\n          if (code === \"0000\") {\n            this.$message.success(\"操作成功!!\");\n            this.getData();\n          }\n        })\n        .catch(() => {});\n    },\n    exportExcel() {\n      let excelData = this.propTableData.colFirst.map(item => ({ \"操作项目\": item.czrw}));\n      exportToExcel(excelData, \"操作项目.xlsx\");\n    },\n    importExcel(file, fileList) {\n      let fileName = file.name\n      if (!fileName.includes(\"操作项目\")) {\n        this.msgError(\"文件有误，请检查\")\n        this.$refs.upload.clearFiles()\n        return\n      }\n      importFromExcel(file)\n        .then(data => {\n          this.ids = this.propTableData.colFirst.map(item => item.objId)\n          this.propTableData.colFirst = data.map(item => ({xh: item.__rowNum__ , czrw: item[\"操作项目\"]}));\n        })\n        .catch(error => {\n          console.error(\"导入失败\", error);\n        });\n      this.$refs.upload.clearFiles()\n    },\n    //获取配电室下拉框数据\n    getPdsOptionsDataList() {\n      getPdsOptionsDataList({}).then(res => {\n        this.pdsOptionsDataList = res.data;\n      });\n    },\n    getShow() {\n      this.buttonNameShow = false;\n      switch (this.form.status) {\n        case \"0\":\n          this.buttonName = \"上 报\";\n          if (this.currentUser === this.form.createBy) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case \"1\":\n          this.buttonName = \"提 交\";\n          if (this.form.bzspr === this.currentUser) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case \"2\":\n          this.buttonName = \"提 交\";\n          if (this.form.fgsspr === this.currentUser) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case \"3\":\n          this.buttonName = \"办 结\";\n          if (this.form.bjr === this.currentUser) {\n            this.buttonNameShow = true;\n            this.isDisabledBj = false;\n          }\n          break;\n      }\n    },\n    filterReset() {\n      this.params = {\n        //配电\n        lx: 3,\n        status: \"0,1,2,3,4\" //状态。将结束状态的过滤掉\n      };\n    },\n    //预览文件\n    async previewFile(row) {\n      try {\n        let exportData = { ...row };\n        await previewFile(exportData, \"pdzdzczp\");\n      } catch (e) {\n        this.$message.error(\"预览失败！\");\n      }\n    },\n    //清空\n    change(e) {\n      this.$forceUpdate();\n    },\n    //关闭弹窗\n    closeActiviti() {\n      if (this.$refs.activiti.$refs.form) {\n        this.$refs.activiti.$refs.form.resetFields();\n      }\n      this.isShow = false;\n    },\n    //关闭流程查看页面\n    colseTimeLine() {\n      this.timeLineShow = false;\n    },\n    async showTimeLine(row) {\n      this.processData.businessKey = row.objId;\n      let { code, data } = await HistoryList(this.processData);\n      this.timeData = data;\n      this.timeLineShow = true;\n    },\n    //流程图片查看\n    showProcessImg(row) {\n      console.log(row);\n      this.openLoadingImg = true;\n      this.imgSrc =\n        \"/activiti-api/process/read-resource?processDefinitionKey=czplc&businessKey=\" +\n        row.objId +\n        \"&t=\" +\n        new Date().getTime();\n    },\n    //改造后的上传多个图片文件\n    uploadForm() {\n      var newUrl = []; //用来存放当前未改动过的图片url,此图片不进行删除处理，其余当前业务id下面的图片数据将进行删除\n      // console.log(this.$refs.uploadImg.fileList)\n      var imageType = [\"png\", \"jpg\"];\n      console.log(\"开始上传图片\");\n      console.log(this.imgList);\n      const formData = new FormData();\n      // 因为要传一个文件数组过去，所以要循环append\n      this.imgList.forEach(file => {\n        if (file.raw == undefined) {\n          console.log(file.url);\n          newUrl.push(file.url);\n        }\n        formData.append(\"files\", file.raw);\n      });\n      formData.append(\"businessId\", this.uploadImgData.businessId); // 自定义参数\n      formData.append(\"newUrl\", newUrl); // 未改动过的图片url\n      formData.append(\"type\", imageType); // 未改动过的图片url\n      api\n        .requestPost(\"/isc-api/file/uploadFiles\", formData, 1)\n        .then(res => {\n          console.log(\"上传图片成功的反回值\");\n          console.log(res);\n          // 清空图片列表（一定要清空，否则上传成功后还是会调用handleChange（）函数，上传成功后列表中还存在图片）\n          this.imgList = [];\n          //查询接口数据\n          this.getData();\n        })\n        .catch(res => {\n          this.$message.success(\"图片上传失败！\");\n        });\n    },\n    // 选择文件时，往fileList里添加\n    handleChange(file, fileList) {\n      this.imgList = fileList;\n    },\n    handleProgress(event, file, fileList) {\n      console.log(\"event\", event);\n      console.log(\"file\", file);\n      console.log(\"fileList\", fileList);\n    },\n    //图片移除\n    handleRemove(file, fileList) {\n      this.imgList = fileList;\n    },\n    //图片放大\n    handlePictureCardPreview(file) {\n      this.dialogImageUrl = [file.url];\n      this.imgDialogVisible = true;\n    },\n    onClose() {\n      this.imgDialogVisible = false;\n    },\n    // 预览弹框\n    handleYlChange() {\n      this.titleyl = \"查看操作项目\";\n      this.yl = true;\n    },\n    // 全选框\n    handleCheckAllChange(val) {\n      if (val) {\n        this.form.yzxczxs = this.form.czxs;\n        this.form.wzxczxs = 0;\n      } else {\n        this.form.wzxczxs = this.form.czxs;\n        this.form.yzxczxs = 0;\n      }\n      this.propTableData.colFirst.forEach(item => {\n        item.sfwc = val;\n      });\n    },\n    handleCurrentChange(index) {\n      console.log(index);\n      if (this.flag === \"start\") {\n        this.oldIndex = index;\n        this.flag = \"end\";\n        this.$message.info(\"请点击需要更换到的行\");\n      } else if (this.flag == \"end\") {\n        this.newIndex = index;\n        this.flag = \"start\";\n        const oldCurrentRow = this.propTableData.colFirst.splice(\n          this.oldIndex,\n          1\n        )[0];\n        this.propTableData.colFirst.splice(this.newIndex, 0, oldCurrentRow);\n        this.$message.success(\"更换顺序成功\");\n      }\n    },\n    handleCheckChange(val) {\n      if (val) {\n        ++this.form.yzxczxs;\n      } else {\n        --this.form.yzxczxs;\n      }\n      this.form.wzxczxs = this.form.czxs - this.form.yzxczxs;\n    },\n    //工作流回传数据\n    async todoResult(data) {\n      let row = {\n        objId: data.businessKey,\n        isStart: 1,\n        isBack: data.processType === \"rollback\" ? 1 : 0\n      };\n      if (data.processType === \"rollback\") {\n        switch (data.activeTaskName) {\n          case \"操作票填报\":\n            row.status = \"0\";\n            break;\n        }\n      } else {\n        switch (data.activeTaskName) {\n          case \"分公司审核\":\n            row.status = \"2\";\n            row.fgsspr = data.nextUser;\n            break;\n          case \"操作票办结\":\n            row.status = \"3\";\n            row.bjr = data.nextUser;\n            break;\n          case \"结束\":\n            row.status = \"4\";\n            break;\n        }\n      }\n      let { code } = await updateById(row);\n      if (code === \"0000\") {\n        await this.getData();\n      } else {\n        this.$message.warning(\"操作失败\");\n      }\n    },\n    //上报发送办结\n    async getSbFsBj(type) {\n      let row = { ...this.form };\n      if (type === \"complete\") {\n        switch (row.status) {\n          case \"0\":\n            this.isShowDetails = false;\n            this.processData.variables.pass = true;\n            this.processData.businessKey = row.objId;\n            this.processData.processType = type;\n            // this.processData.dw = row.fgs;\n            this.processData.rylx = \"分公司审核人\";\n            this.activitiOption.title = \"提交\";\n            this.processData.defaultFrom = true;\n            this.processData.personGroupId = 14;\n            this.isShow = true;\n            break;\n          case \"2\":\n            this.isShowDetails = false;\n            this.processData.variables.pass = true;\n            this.processData.businessKey = row.objId;\n            this.processData.processType = type;\n            this.activitiOption.title = \"提交\";\n            this.processData.defaultFrom = true;\n            this.processData.personGroupId = 15;\n            // this.processData.dw = row.fgs;\n            this.processData.rylx = \"办结人\";\n            this.isShow = true;\n            break;\n          case \"3\":\n            this.form.colFirst = this.propTableData.colFirst;\n            this.form.objIdList = this.ids;\n            await this.$refs[\"form\"].validate(async valid => {\n              if (valid) {\n                this.uploadImgData.businessId = this.form.objId;\n                this.uploadForm();\n                await saveOrUpdate(this.form).then(res => {\n                  if (res.code === \"0000\") {\n                    this.getData();\n                    this.isShowDetails = false;\n                    this.processData.variables.pass = true;\n                    this.processData.businessKey = row.objId;\n                    this.processData.processType = type;\n                    this.activitiOption.title = \"办结\";\n                    this.processData.defaultFrom = false;\n                    this.isShow = true;\n                  } else {\n                    this.$message.error(\"失败\");\n                  }\n                });\n              }\n            });\n        }\n      } else {\n        this.isShowDetails = false;\n        this.processData.businessKey = row.objId;\n        this.processData.processType = type;\n        this.activitiOption.title = \"回退原因填写\";\n        this.processData.defaultFrom = true;\n        this.processData.variables.pass = false;\n        this.isShow = true;\n      }\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        param.mySorts = [{ prop: \"updateTime\", asc: false }];\n        if (!param.status) {\n          param.status = \"0,1,2,3,4\";\n        }\n        const { data, code } = await getList(param);\n        if (code === \"0000\") {\n          for (let i of data.records) {\n            i.fgsmc = \"配电运维分公司\";\n            this.statusOptions.forEach(element => {\n              if (i.status === element.value) {\n                i.statusCn = element.label;\n              }\n            });\n          }\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //确定按钮\n    async saveRow() {\n      await this.$refs[\"form\"].validate(async valid => {\n        if (valid) {\n          try {\n            //保存列表序号\n            let tableArr = this.propTableData.colFirst;\n            for (var i = 0; i < tableArr.length; i++) {\n              tableArr[i].xh = i + 1;\n            }\n            this.form.colFirst = tableArr;\n            this.form.objIdList = this.ids;\n            this.form.status = \"0\";\n            let { code, data } = await saveOrUpdate(this.form);\n            if (code === \"0000\") {\n              this.$message.success(\"操作成功\");\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              await this.getData();\n              this.isShowDetails = false;\n            }\n          } catch (e) {\n            console.log(e);\n          }\n        }\n      });\n    },\n    //修改按钮\n    getUpdate(row) {\n      this.getCzpmx(row);\n      this.title = \"配电倒闸操作票修改\";\n      this.isDisabled = false;\n      this.form = { ...row };\n      //图片结合\n      this.imgList = this.form.imgList;\n      this.isDisabledBj = false;\n      this.isShowDetails = true;\n      this.getShow();\n    },\n    //详情\n    getInfo(row) {\n      this.getCzpmx(row);\n      this.title = \"配电操作票详情查看\";\n      this.form = { ...row };\n      //图片结合\n      this.imgList = this.form.imgList;\n      this.isDisabled = true;\n      this.isDisabledBj = true;\n      this.isShowDetails = true;\n      this.getShow();\n    },\n    //新增\n    getInster() {\n      this.propTableData.colFirst = [];\n      this.title = \"配电倒闸操作票增加\";\n      this.isDisabled = false;\n      this.form = { fgs: this.$store.getters.deptId.toString() };\n      this.form.status = \"新建\";\n      //配电\n      this.form.lx = 3;\n      this.isShowDetails = true;\n    },\n    //删除按钮\n    async deleteRow(row) {\n      let objId = \"\";\n      if (row.objId) {\n        objId = row.objId;\n      } else {\n        objId = this.ids[0];\n      }\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove(objId).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              //重置page页从1开始\n              // this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    //表格新增\n    listFirstAdd(index) {\n      let row = {\n        czxm: \"\",\n        sfwc: \"\"\n      };\n      this.propTableData.colFirst.push(row);\n      this.propTableData.sel = row;\n      this.form.czxs = this.propTableData.colFirst.length;\n      this.form.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //表格删除\n    listFirstDel(index, row) {\n      this.ids.push(row.objId);\n      this.propTableData.colFirst.splice(index, 1);\n      console.log(\"splice index == \", index);\n      this.form.czxs = this.propTableData.colFirst.length;\n      this.form.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false;\n    },\n    //选择行\n    selectChange(selection) {\n      this.ids = selection.map(item => item.objId);\n      this.selection = selection;\n      this.single = selection.length !== 1;\n      this.form = this.selection[0];\n    },\n    /**\n     *获取操作票明细\n     */\n    async getCzpmx(row) {\n      try {\n        const { data, code } = await getCzpmxList({ objId: row.objId });\n        if (code === \"0000\") {\n          //给list添加字段\n          data.forEach(item => {\n            this.$set(item, \"isSet\", true);\n          });\n          this.propTableData.colFirst = data;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //导出word\n    async exportWord(row) {\n      try {\n        let exportData = { ...row };\n        await exportWord(exportData, \"pdzdzczp\", \"配电倒闸操作票.docx\");\n      } catch (e) {\n        this.$message.error(\"导出失败！\");\n      }\n    },\n    //导出Pdf\n    async exportPdf(row) {\n      try {\n        let exportData = { ...row };\n        await exportPdf(exportData, \"pdzdzczp\", \"配电倒闸操作票.pdf\");\n      } catch (e) {\n        this.$message.error(\"导出失败！\");\n      }\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.item {\n  width: 8.5rem;\n  height: 1.25rem;\n}\n</style>\n"], "sourceRoot": "src/views/dagangOilfield/czpgl/pddzcz"}]}