{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\pddzcz\\components\\czp_tj.vue?vue&type=style&index=0&id=d2fd2f76&scoped=true&lang=css&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\pddzcz\\components\\czp_tj.vue", "mtime": 1748605462598}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLyrliJfooajpopzoibLorr7nva4qLwovZGVlcC8gLmVsLXRhYmxlIHRoIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZThmN2YwOwp9Cg=="}, {"version": 3, "sources": ["czp_tj.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuIA;AACA;AACA;AACA", "file": "czp_tj.vue", "sourceRoot": "src/views/dagangOilfield/czpgl/pddzcz/components", "sourcesContent": ["<template>\n  <div className=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 110, itemWidth: 220 }\"\n      @handleEvent=\"handleEvent\"\n    />\n\n    <el-white className=\"button-group\">\n      <div>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"selectChange\"\n          height=\"68vh\"\n        />\n      </div>\n    </el-white>\n  </div>\n</template>\n\n<script>\nimport { getListTj } from \"@/api/yxgl/bdyxgl/bddzczp\";\nimport { getPdsOptionsDataList } from \"@/api/dagangOilfield/asset/pdsgl\";\nexport default {\n  name: \"czp_tj\",\n  mounted() {\n    //列表查询\n    this.getData();\n    //获取配电室下拉框数据\n    this.getPdsOptionsDataList();\n  },\n  data() {\n    return {\n      // 查询变电站列表\n      pdsOptionsDataList: [],\n      //查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      // 查询数据总条数\n      total: 0,\n      filterInfo: {\n        data: {\n          bdzmc: \"\"\n        }, //查询条件\n        fieldList: [\n          {\n            label: \"配电站名称\",\n            value: \"bdzmc\",\n            type: \"select\",\n            clearable: true,\n            filterable: true,\n            options: []\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"配电站名称\", prop: \"pdzCn\", minWidth: \"150\" },\n          { label: \"操作任务\", prop: \"czrw\", minWidth: \"100\" },\n          { label: \"操作项数\", prop: \"czxs\", minWidth: \"100\" },\n          { label: \"已执行项数\", prop: \"yzxczxs\", minWidth: \"100\" },\n          { label: \"未执行项数\", prop: \"wzxczxs\", minWidth: \"100\" }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        lx: 3,\n        status: \"4\"\n      }\n    };\n  },\n  methods: {\n    //统计\n    async getData(params) {\n      try {\n        const param = { ...this.params, ...params };\n        const { data, code } = await getListTj(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    /**\n     * 列表选中\n     * */\n    selectChange(selection) {\n      this.ids = selection.map(item => item.objId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    //获取配电室下拉框数据\n    getPdsOptionsDataList() {\n      getPdsOptionsDataList({}).then(res => {\n        this.pdsOptionsDataList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"bdzmc\") {\n            return (item.options = this.pdsOptionsDataList);\n          }\n        });\n      });\n    },\n    //下拉框change事件\n    handleEvent(val) {\n      //变电站查询下拉框选项根据所选分公司带出来\n      if (val.label === \"fgs\" && val.value && val.value !== \"\") {\n        let form = {\n          ssdwbm: val.value\n        };\n        getBdzSelectList(form).then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value == \"bdzmc\") {\n              return (item.options = res.data);\n            }\n          });\n        });\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n/*列表颜色设置*/\n/deep/ .el-table th {\n  background-color: #e8f7f0;\n}\n</style>\n"]}]}