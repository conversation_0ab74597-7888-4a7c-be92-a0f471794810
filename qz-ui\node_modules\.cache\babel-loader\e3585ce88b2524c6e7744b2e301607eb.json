{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\lpbzk\\dqgzzqpz.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\lpbzk\\dqgzzqpz.vue", "mtime": 1706897322614}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON>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"}, {"version": 3, "sources": ["dqgzzqpz.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwLA;;AASA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;AACA,EAAA,IAAA,EAAA,UADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,OAAA,EAAA,IADA;AAEA,MAAA,OAAA,EAAA,EAFA;AAGA;AACA,MAAA,wBAAA,EAAA,EAJA;AAKA,MAAA,KAAA,EAAA;AACA,QAAA,GAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CADA;AAEA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAFA;AAKA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CALA;AAQA,QAAA,EAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,QAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AARA,OALA;AAeA;AACA,MAAA,IAAA,EAAA,EAhBA;AAiBA;AACA,MAAA,aAAA,EAAA,KAlBA;AAmBA;AACA,MAAA,UAAA,EAAA,KApBA;AAqBA;AACA,MAAA,SAAA,EAAA,EAtBA;AAuBA;AACA,MAAA,QAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,EAaA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAbA,EAiBA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAjBA,EAqBA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OArBA,EAyBA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAzBA,EA6BA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OA7BA,EAiCA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAjCA,EAqCA;AACA,QAAA,KAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA;AAFA,OArCA,EAyCA;AACA,QAAA,KAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAzCA,EA6CA;AACA,QAAA,KAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA;AAFA,OA7CA,EAiDA;AACA,QAAA,KAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAjDA,EAqDA;AACA,QAAA,KAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA;AAFA,OArDA,EAyDA;AACA,QAAA,KAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAzDA,EA6DA;AACA,QAAA,KAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA;AAFA,OA7DA,EAiEA;AACA,QAAA,KAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAjEA,EAqEA;AACA,QAAA,KAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA;AAFA,OArEA,CAxBA;AAkGA;AACA,MAAA,KAAA,EAAA,EAnGA;AAoGA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,OAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA,EAFA;AAGA,UAAA,OAAA,EAAA,EAHA;AAIA,UAAA,IAAA,EAAA,EAJA;AAKA,UAAA,IAAA,EAAA;AALA,SADA;AAQA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,KADA;AAEA,UAAA,KAAA,EAAA,MAFA;AAGA,UAAA,IAAA,EAAA,QAHA;AAIA,UAAA,OAAA,EAAA,EAJA;AAKA,UAAA,SAAA,EAAA;AALA,SADA,EAQA;AACA,UAAA,KAAA,EAAA,KADA;AAEA,UAAA,KAAA,EAAA,KAFA;AAGA,UAAA,IAAA,EAAA,QAHA;AAIA,UAAA,OAAA,EAAA,EAJA;AAKA,UAAA,SAAA,EAAA;AALA,SARA,EAeA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,MAFA;AAGA,UAAA,IAAA,EAAA,QAHA;AAIA,UAAA,OAAA,EAAA,CACA;AACA,YAAA,KAAA,EAAA,CADA;AAEA,YAAA,KAAA,EAAA;AAFA,WADA,EAKA;AACA,YAAA,KAAA,EAAA,CADA;AAEA,YAAA,KAAA,EAAA;AAFA,WALA,EASA;AACA,YAAA,KAAA,EAAA,CADA;AAEA,YAAA,KAAA,EAAA;AAFA,WATA,EAaA;AACA,YAAA,KAAA,EAAA,CADA;AAEA,YAAA,KAAA,EAAA;AAFA,WAbA,EAiBA;AACA,YAAA,KAAA,EAAA,CADA;AAEA,YAAA,KAAA,EAAA;AAFA,WAjBA,EAqBA;AACA,YAAA,KAAA,EAAA,CADA;AAEA,YAAA,KAAA,EAAA;AAFA,WArBA,EAyBA;AACA,YAAA,KAAA,EAAA,CADA;AAEA,YAAA,KAAA,EAAA;AAFA,WAzBA,EA6BA;AACA,YAAA,KAAA,EAAA,CADA;AAEA,YAAA,KAAA,EAAA;AAFA,WA7BA,EAiCA;AACA,YAAA,KAAA,EAAA,CADA;AAEA,YAAA,KAAA,EAAA;AAFA,WAjCA,EAqCA;AACA,YAAA,KAAA,EAAA,EADA;AAEA,YAAA,KAAA,EAAA;AAFA,WArCA,EAyCA;AACA,YAAA,KAAA,EAAA,EADA;AAEA,YAAA,KAAA,EAAA;AAFA,WAzCA,EA6CA;AACA,YAAA,KAAA,EAAA,EADA;AAEA,YAAA,KAAA,EAAA;AAFA,WA7CA,EAiDA;AACA,YAAA,KAAA,EAAA,EADA;AAEA,YAAA,KAAA,EAAA;AAFA,WAjDA,EAqDA;AACA,YAAA,KAAA,EAAA,EADA;AAEA,YAAA,KAAA,EAAA;AAFA,WArDA,EAyDA;AACA,YAAA,KAAA,EAAA,EADA;AAEA,YAAA,KAAA,EAAA;AAFA,WAzDA,EA6DA;AACA,YAAA,KAAA,EAAA,EADA;AAEA,YAAA,KAAA,EAAA;AAFA,WA7DA,EAiEA;AACA,YAAA,KAAA,EAAA,EADA;AAEA,YAAA,KAAA,EAAA;AAFA,WAjEA,EAqEA;AACA,YAAA,KAAA,EAAA,EADA;AAEA,YAAA,KAAA,EAAA;AAFA,WArEA,CAJA;AA8EA,UAAA,SAAA,EAAA;AA9EA,SAfA,EA+FA;AACA,UAAA,KAAA,EAAA,QADA;AAEA,UAAA,KAAA,EAAA,WAFA;AAGA,UAAA,IAAA,EAAA,QAHA;AAIA,UAAA,OAAA,EAAA,CACA;AACA,YAAA,KAAA,EAAA,CADA;AAEA,YAAA,KAAA,EAAA;AAFA,WADA,EAKA;AACA,YAAA,KAAA,EAAA,CADA;AAEA,YAAA,KAAA,EAAA;AAFA,WALA,CAJA;AAcA,UAAA,SAAA,EAAA;AAdA,SA/FA,EA+GA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,KAAA,EAAA,IAFA;AAGA,UAAA,IAAA,EAAA;AAHA,SA/GA;AARA,OApGA;AAkOA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,CARA;AAeA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAfA,OAlOA;AAmPA,MAAA,MAAA,EAAA,EAnPA;AAoPA,MAAA,UAAA,EAAA,EApPA;AAqPA,MAAA,cAAA,EAAA,KArPA;AAsPA,MAAA,QAAA,EAAA;AAtPA,KAAA;AAwPA,GA3PA;AA4PA,EAAA,OA5PA,qBA4PA;AACA,SAAA,aAAA;AACA,SAAA,OAAA;AACA,GA/PA;AAgQA,EAAA,KAAA,EAAA;AACA,IAAA,aADA,yBACA,GADA,EACA;AACA,UAAA,GAAA,EAAA;AACA,YAAA,EAAA,GAAA,KAAA,GAAA,CAAA,aAAA,CAAA,YAAA,CAAA;AACA,QAAA,EAAA,CAAA,KAAA,CAAA,IAAA,GAAA,CAAA;AACA,QAAA,EAAA,CAAA,KAAA,CAAA,GAAA,GAAA,CAAA;AACA;AACA;AAPA,GAhQA;AAyQA,EAAA,OAAA,EAAA;AACA;;;AAGA,IAAA,aAJA,2BAIA;AAAA;;AACA,+BAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAAA,QAAA,EAAA;AACA,SAFA;AAGA,QAAA,KAAA,CAAA,wBAAA,GAAA,GAAA,CAAA,IAAA;;AACA,QAAA,KAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,IAAA,MAAA,EAAA;AACA,mBAAA,IAAA,CAAA,OAAA,GAAA,KAAA,CAAA,wBAAA;AACA;AACA,SAJA;AAKA,OAVA;AAWA,KAhBA;AAiBA;AACA,IAAA,QAlBA,oBAkBA,KAlBA,EAkBA;AAAA;;AACA,8BAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,MAAA;;AACA,UAAA,MAAA,CAAA,OAAA;AACA,SAHA,MAGA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA;AACA,YAAA,IAAA,EAAA,OADA;AAEA,YAAA,OAAA,EAAA;AAFA,WAAA;AAIA;AACA,OAVA;AAWA,KA9BA;AA+BA,IAAA,QA/BA,oBA+BA,KA/BA,EA+BA;AAAA;;AACA,8BAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,MAAA;AACA,SAFA,MAEA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA;AACA,YAAA,IAAA,EAAA,OADA;AAEA,YAAA,OAAA,EAAA;AAFA,WAAA;AAIA;AACA,OATA;AAUA,KA1CA;AA2CA;AACA,IAAA,OA5CA,mBA4CA,KA5CA,EA4CA;AAAA;;AACA,4BAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,MAAA;;AACA,UAAA,MAAA,CAAA,OAAA;AACA,SAHA,MAGA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA;AACA,YAAA,IAAA,EAAA,OADA;AAEA,YAAA,OAAA,EAAA;AAFA,WAAA;AAIA;AACA,OAVA;AAWA,KAxDA;AAyDA;AACA,IAAA,WA1DA,uBA0DA,GA1DA,EA0DA,UA1DA,EA0DA;AAAA;;AACA,UAAA,GAAA,CAAA,KAAA,KAAA,MAAA,EAAA;AACA,wCAAA;AAAA,UAAA,MAAA,EAAA,GAAA,CAAA,KAAA,CAAA,QAAA;AAAA,SAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,gBAAA,IAAA,CAAA,KAAA,IAAA,KAAA,EAAA;AACA,cAAA,MAAA,CAAA,IAAA,CAAA,UAAA,EAAA,KAAA,EAAA,EAAA;;AACA,qBAAA,IAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;AACA;AACA,WALA;AAMA,SAPA;AAQA;AACA,KArEA;AAsEA;AACA,IAAA,OAvEA,mBAuEA,MAvEA,EAuEA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,MAAA,CAAA,OAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,MAAA,+DAAA,MAAA,CAAA,MAAA,GAAA,MAAA;AACA,gBAAA,KAJA,GAIA,MAAA,CAAA,MAJA;AAAA;AAAA,uBAKA,uBAAA,KAAA,CALA;;AAAA;AAAA;AAKA,gBAAA,IALA,kBAKA,IALA;AAKA,gBAAA,IALA,kBAKA,IALA;;AAMA,oBAAA,IAAA,KAAA,MAAA,EAAA;AAAA,uEACA,IAAA,CAAA,OADA;;AAAA;AACA,wEAAA;AAAA,sBAAA,CAAA;AACA,sBAAA,CAAA,CAAA,MAAA,GAAA,CAAA,CAAA,SAAA,KAAA,CAAA,GAAA,IAAA,GAAA,IAAA;AACA,sBAAA,CAAA,CAAA,KAAA,GAAA,MAAA,CAAA,UAAA,CAAA,CAAA,CAAA,IAAA,CAAA;AACA,sBAAA,CAAA,CAAA,MAAA,GAAA,MAAA,CAAA,UAAA,CAAA,CAAA,CAAA,IAAA,CAAA;AACA;AALA;AAAA;AAAA;AAAA;AAAA;;AAMA,kBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA,kBAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA;;AAfA;AAAA;;AAAA;AAAA;AAAA;AAiBA,gBAAA,OAAA,CAAA,GAAA;;AAjBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmBA,KA1FA;AA2FA;AACA,IAAA,QA5FA,sBA4FA;AACA,WAAA,MAAA,GAAA,EAAA;AACA,KA9FA;AA+FA;AACA,IAAA,qBAhGA,mCAgGA,CAAA,CAhGA;AAiGA;AACA,IAAA,SAlGA,uBAkGA;AACA,WAAA,KAAA,GAAA,UAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,IAAA,GAAA,EAAA;AACA,KAvGA;AAwGA;AACA,IAAA,SAzGA,qBAyGA,GAzGA,EAyGA;AACA,WAAA,KAAA,GAAA,UAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,UAAA;AACA,KA/GA;AAgHA;AACA,IAAA,OAjHA,mBAiHA,GAjHA,EAiHA;AACA,WAAA,KAAA,GAAA,UAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,UAAA;AACA,KAvHA;AAyHA,IAAA,UAzHA,sBAyHA,IAzHA,EAyHA;AACA,UAAA,IAAA,EAAA;AACA,eAAA,KAAA,QAAA,CAAA,MAAA,CAAA,UAAA,CAAA;AAAA,iBAAA,CAAA,CAAA,KAAA,IAAA,IAAA;AAAA,SAAA,EAAA,CAAA,EAAA,KAAA;AACA,OAFA,MAEA;AACA,eAAA,EAAA;AACA;AACA,KA/HA;AAgIA,IAAA,UAhIA,sBAgIA,IAhIA,EAgIA;AACA,UAAA,4BAAA,GAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,SAAA,CAAA,KAAA,wBAAA,CAAA,CAAA;AACA,MAAA,4BAAA,CAAA,IAAA,CAAA;AAAA,QAAA,KAAA,EAAA,SAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAAA;AACA,MAAA,4BAAA,CAAA,IAAA,CAAA;AAAA,QAAA,KAAA,EAAA,SAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAAA;;AACA,UAAA,IAAA,EAAA;AACA,eAAA,4BAAA,CAAA,MAAA,CAAA,UAAA,CAAA;AAAA,iBAAA,CAAA,CAAA,KAAA,KAAA,IAAA;AAAA,SAAA,EAAA,CAAA,EACA,KADA;AAEA,OAHA,MAGA;AACA,eAAA,EAAA;AACA;AACA,KA1IA;AA2IA,IAAA,OA3IA,qBA2IA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAEA,4BAAA,MAAA,CAAA,IAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,uBAEA,IAFA;;AAGA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA;;AALA;AAAA;;AAAA;AAAA;AAAA;AAOA,gBAAA,OAAA,CAAA,GAAA;;AAPA;AASA;AACA,gBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;AAVA;AAAA,uBAWA,MAAA,CAAA,OAAA,EAXA;;AAAA;AAYA,gBAAA,MAAA,CAAA,aAAA,GAAA,KAAA;;AAZA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA,KAxJA;AAyJA;AACA,IAAA,SA1JA,qBA0JA,KA1JA,EA0JA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAKA,IALA,CAKA,YAAA;AACA,wCAAA,KAAA,EAAA,IAAA,CAAA,gBAAA;AAAA,wBAAA,IAAA,QAAA,IAAA;;AACA,wBAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,SADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;;AAIA,sBAAA,MAAA,CAAA,OAAA;AACA,qBANA,MAMA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,OADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;AAIA;AACA,mBAbA;AAcA,iBApBA,EAqBA,KArBA,CAqBA,YAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA;AACA,oBAAA,IAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;AAIA,iBA1BA;;AADA;AAAA,uBA4BA,MAAA,CAAA,OAAA,EA5BA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA6BA,KAvLA;AAwLA;AACA,IAAA,KAzLA,mBAyLA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,KA3LA;AA4LA,IAAA,YA5LA,wBA4LA,IA5LA,EA4LA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KA9LA;AAgMA,IAAA,eAhMA,2BAgMA,GAhMA,EAgMA;AACA,UAAA,GAAA,CAAA,MAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,QAAA,KAAA,CAAA,MAAA,CAAA;AACA,aAAA,cAAA,GAAA,IAAA;AACA,aAAA,QAAA,GAAA,IAAA;AACA;AACA,KAtMA;AAuMA;AACA,IAAA,WAxMA,yBAwMA;AAAA;;AACA,WAAA,IAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,YAAA;AACA,QAAA,MAAA,CAAA,IAAA,GAAA,MAAA,CAAA,QAAA,CAAA,IAAA,GAAA,IAAA;;AACA,QAAA,MAAA,CAAA,SAAA,CAAA,MAAA;AACA,OAHA;AAIA,KA9MA;AA+MA;AACA,IAAA,UAhNA,wBAgNA;AAAA;;AACA,UAAA,KAAA,KAAA,KAAA,UAAA,EAAA;AACA,aAAA,IAAA,CAAA,KAAA,IAAA,EAAA,KAAA,EAAA,EAAA;AACA;;AACA,WAAA,OAAA,GAAA,EAAA;AACA,sCAAA;AAAA,QAAA,MAAA,EAAA,KAAA,IAAA,CAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA;AAxNA;AAzQA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      @handleReset=\"getReset\"\n      @onfocusEvent=\"inputFocusEvent\"\n      @handleEvent=\"handleEvent\"\n      :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n    />\n    <div>\n      <el-white class=\"button-group\">\n        <div class=\"button_btn\">\n          <el-button\n            type=\"primary\"\n            v-hasPermi=\"['bzdqgzpz:button:add']\"\n            icon=\"el-icon-plus\"\n            @click=\"getInsert\"\n            >新增</el-button\n          >\n        </div>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"selectChange\"\n          height=\"65vh\"\n          v-loading=\"loading\"\n        >\n          <el-table-column\n            slot=\"table_eight\"\n            align=\"center\"\n            fixed=\"right\"\n            style=\"display: block\"\n            label=\"操作\"\n            width=\"200\"\n            :resizable=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                v-if=\"scope.row.taskState === 0\"\n                @click=\"updateRow(scope.row)\"\n                v-hasPermi=\"['bzdqgzpz:button:update']\"\n                type=\"text\"\n                size=\"small\"\n                title=\"修改\"\n                class=\"el-icon-edit\"\n              ></el-button>\n              <el-button\n                @click=\"getInfo(scope.row)\"\n                type=\"text\"\n                size=\"small\"\n                title=\"详情\"\n                class=\"el-icon-view\"\n              ></el-button>\n              <el-button\n                v-if=\"scope.row.taskState === 0\"\n                @click=\"deleteRow(scope.row.objId)\"\n                type=\"danger\"\n                size=\"small\"\n                title=\"删除\"\n                class=\"el-icon-delete\"\n              ></el-button>\n              <el-button\n                v-if=\"scope.row.taskState === 0\"\n                size=\"small\"\n                type=\"success\"\n                title=\"开启消息提醒\"\n                icon=\"el-icon-caret-right\"\n                @click=\"startJob(scope.row.objId)\"\n              ></el-button>\n              <el-button\n                v-if=\"scope.row.taskState === 1\"\n                size=\"small\"\n                type=\"danger\"\n                title=\"关闭消息提醒\"\n                icon=\"el-icon-switch-button\"\n                @click=\"stopJob(scope.row.objId)\"\n              ></el-button>\n              <el-button\n                v-if=\"'admin' === $store.getters.name\"\n                @click=\"chackJob(scope.row.objId)\"\n                type=\"success\"\n                size=\"small\"\n                title=\"启动一次\"\n                class=\"el-icon-caret-right\"\n              ></el-button>\n            </template>\n          </el-table-column>\n        </comp-table>\n      </el-white>\n    </div>\n    <!-- 详情/新增/修改 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"40%\"\n      @close=\"handleClose\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"分公司:\" prop=\"ssgs\">\n              <el-select\n                placeholder=\"请选择分公司\"\n                @change=\"getBdzList\"\n                clearable\n                v-model=\"form.ssgs\"\n                :disabled=\"isDisabled\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in organizationSelectedList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"变电站:\" prop=\"bdz\">\n              <el-select\n                placeholder=\"请选择变电站\"\n                clearable\n                v-model=\"form.bdz\"\n                :disabled=\"isDisabled\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in bdzList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"工作类型：\" prop=\"gzlx\">\n              <el-select\n                placeholder=\"请选择工作类型\"\n                clearable\n                v-model=\"form.gzlx\"\n                :disabled=\"isDisabled\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in gzlxList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"周期(月)：\" clearable prop=\"zq\">\n              <el-input-number\n                :min=\"0\"\n                placeholder=\"请输入周期\"\n                v-model=\"form.zq\"\n                :disabled=\"isDisabled\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button\n          v-if=\"title === '定期工作配置新增' || title === '定期工作配置修改'\"\n          type=\"primary\"\n          @click=\"saveRow\"\n          >确 认</el-button\n        >\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  getList,\n  remove,\n  saveOrUpdate,\n  getBdzSelectList,\n  endJob,\n  chackJob,\n  startJob\n} from \"@/api/dagangOilfield/bzgl/lpbzk/dqgzzqpz\";\nimport { getFgsOptions } from \"@/api/yxgl/bdyxgl/zbgl\";\nexport default {\n  name: \"dqgzzqpz\",\n  data() {\n    return {\n      loading: true,\n      bdzList: [],\n      //组织结构下拉数据\n      organizationSelectedList: [],\n      rules: {\n        bdz: [{ required: true, message: \"变电站不能为空\", trigger: \"change\" }],\n        gzlx: [\n          { required: true, message: \"工作类型不能为空\", trigger: \"change\" }\n        ],\n        ssgs: [\n          { required: true, message: \"分公司不能为空\", trigger: \"change\" }\n        ],\n        zq: [{ required: true, message: \"周期不能为空\", trigger: \"blur\" }]\n      },\n      //form表单\n      form: {},\n      //是否显示弹框\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //表单数据\n      dataTable: [],\n      //工作类型\n      gzlxList: [\n        {\n          value: 1,\n          label: \"安全工器具检查\"\n        },\n        {\n          value: 2,\n          label: \"仪器仪表检查\"\n        },\n        {\n          value: 3,\n          label: \"防小动物设施检查\"\n        },\n        {\n          value: 4,\n          label: \"交流电源切换检查\"\n        },\n        {\n          value: 5,\n          label: \"排水、通风检查\"\n        },\n        {\n          value: 6,\n          label: \"设备标识检查\"\n        },\n        {\n          value: 7,\n          label: \"照明系统检查\"\n        },\n        {\n          value: 8,\n          label: \"SF6氧量告警仪检查\"\n        },\n        {\n          value: 9,\n          label: \"加热器及照明检查\"\n        },\n        {\n          value: 10,\n          label: \"备用站用变启动试验\"\n        },\n        {\n          value: 11,\n          label: \"UPS系统试验\"\n        },\n        {\n          value: 12,\n          label: \"电源箱检查\"\n        },\n        {\n          value: 13,\n          label: \"接地标志检查\"\n        },\n        {\n          value: 14,\n          label: \"防误装置校验\"\n        },\n        {\n          value: 15,\n          label: \"电缆沟检查\"\n        },\n        {\n          value: 16,\n          label: \"防汛设施检查\"\n        },\n        {\n          value: 17,\n          label: \"保温设施检查\"\n        },\n        {\n          value: 18,\n          label: \"事故油池检查\"\n        }\n      ],\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          sbflArr: [],\n          sbfl: \"\",\n          jxflArr: [],\n          jxzq: \"\",\n          zqdw: \"\"\n        },\n        fieldList: [\n          {\n            label: \"分公司\",\n            value: \"ssgs\",\n            type: \"select\",\n            options: [],\n            clearable: true\n          },\n          {\n            label: \"变电站\",\n            value: \"bdz\",\n            type: \"select\",\n            options: [],\n            clearable: true\n          },\n          {\n            label: \"工作类型\",\n            value: \"gzlx\",\n            type: \"select\",\n            options: [\n              {\n                value: 1,\n                label: \"安全工器具检查\"\n              },\n              {\n                value: 2,\n                label: \"仪器仪表检查\"\n              },\n              {\n                value: 3,\n                label: \"防小动物设施检查\"\n              },\n              {\n                value: 4,\n                label: \"交流电源切换检查\"\n              },\n              {\n                value: 5,\n                label: \"排水、通风检查\"\n              },\n              {\n                value: 6,\n                label: \"设备标识检查\"\n              },\n              {\n                value: 7,\n                label: \"照明系统检查\"\n              },\n              {\n                value: 8,\n                label: \"SF6氧量告警仪检查\"\n              },\n              {\n                value: 9,\n                label: \"加热器及照明检查\"\n              },\n              {\n                value: 10,\n                label: \"备用站用变启动试验\"\n              },\n              {\n                value: 11,\n                label: \"UPS系统试验\"\n              },\n              {\n                value: 12,\n                label: \"电源箱检查\"\n              },\n              {\n                value: 13,\n                label: \"接地标志检查\"\n              },\n              {\n                value: 14,\n                label: \"防误装置校验\"\n              },\n              {\n                value: 15,\n                label: \"电缆沟检查\"\n              },\n              {\n                value: 16,\n                label: \"防汛设施检查\"\n              },\n              {\n                value: 17,\n                label: \"保温设施检查\"\n              },\n              {\n                value: 18,\n                label: \"事故油池检查\"\n              }\n            ],\n            clearable: true\n          },\n          {\n            label: \"消息提醒状态\",\n            value: \"taskState\",\n            type: \"select\",\n            options: [\n              {\n                value: 0,\n                label: \"关闭\"\n              },\n              {\n                value: 1,\n                label: \"开启\"\n              }\n            ],\n            clearable: true\n          },\n          {\n            label: \"周期(月)\",\n            value: \"zq\",\n            type: \"input\"\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 120]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"分公司\", prop: \"fgsmc\", minWidth: \"100\" },\n          { label: \"变电站\", prop: \"bdzmc\", minWidth: \"100\" },\n          { label: \"工作类型\", prop: \"gzlxmc\", minWidth: \"100\" },\n          { label: \"周期(月)\", prop: \"zq\", minWidth: \"60\" },\n          { label: \"消息提醒状态\", prop: \"status\", minWidth: \"80\" }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {},\n      selectRows: [],\n      showDeviceTree: false,\n      isFilter: false\n    };\n  },\n  mounted() {\n    this.getFgsOptions();\n    this.getData();\n  },\n  watch: {\n    isShowDetails(val) {\n      if (val) {\n        const el = this.$el.querySelector(\".el-dialog\");\n        el.style.left = 0;\n        el.style.top = 0;\n      }\n    }\n  },\n  methods: {\n    /**\n     * 获取分公司下拉数据\n     */\n    getFgsOptions() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.organizationSelectedList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"ssgs\") {\n            return (item.options = this.organizationSelectedList);\n          }\n        });\n      });\n    },\n    //启动任务\n    startJob(jobId) {\n      startJob(jobId).then(res => {\n        if (res.code === \"0000\") {\n          this.msgSuccess(\"启动成功\");\n          this.getData();\n        } else {\n          this.$message({\n            type: \"error\",\n            message: \"启动失败!\"\n          });\n        }\n      });\n    },\n    chackJob(jobId) {\n      chackJob(jobId).then(res => {\n        if (res.code === \"0000\") {\n          this.msgSuccess(\"启动成功\");\n        } else {\n          this.$message({\n            type: \"error\",\n            message: \"启动失败!\"\n          });\n        }\n      });\n    },\n    //停止任务\n    stopJob(jobId) {\n      endJob(jobId).then(res => {\n        if (res.code === \"0000\") {\n          this.msgSuccess(\"停止成功\");\n          this.getData();\n        } else {\n          this.$message({\n            type: \"error\",\n            message: \"停止失败!\"\n          });\n        }\n      });\n    },\n    //下拉框change事件\n    handleEvent(val, eventValue) {\n      if (val.label === \"ssgs\") {\n        getBdzSelectList({ ssdwbm: val.value.toString() }).then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value == \"bdz\") {\n              this.$set(eventValue, \"bdz\", \"\");\n              return (item.options = res.data);\n            }\n          });\n        });\n      }\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.loading = true;\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        const { data, code } = await getList(param);\n        if (code === \"0000\") {\n          for (let i of data.records) {\n            i.status = i.taskState === 1 ? \"开启\" : \"关闭\";\n            i.fgsmc = this.formatSsgs(i.ssgs);\n            i.gzlxmc = this.formatGzlx(i.gzlx);\n          }\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          this.loading = false;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //重置按钮\n    getReset() {\n      this.params = {};\n    },\n    //选中行\n    handleSelectionChange() {},\n    //新增\n    getInsert() {\n      this.title = \"定期工作配置新增\";\n      this.isDisabled = false;\n      this.isShowDetails = true;\n      this.form = {};\n    },\n    //编辑按钮\n    updateRow(row) {\n      this.title = \"定期工作配置修改\";\n      this.isDisabled = false;\n      this.form = { ...row };\n      this.isShowDetails = true;\n      this.getBdzList();\n    },\n    //详情按钮\n    getInfo(row) {\n      this.title = \"定期工作配置详情\";\n      this.form = { ...row };\n      this.isDisabled = true;\n      this.isShowDetails = true;\n      this.getBdzList();\n    },\n\n    formatGzlx(gzlx) {\n      if (gzlx) {\n        return this.gzlxList.filter(g => g.value == gzlx)[0].label;\n      } else {\n        return \"\";\n      }\n    },\n    formatSsgs(ssgs) {\n      let pageOrganizationSelectedList = JSON.parse(JSON.stringify(this.organizationSelectedList))\n      pageOrganizationSelectedList.push({label: '港东变电分公司', value: '3002'})\n      pageOrganizationSelectedList.push({label: '港中变电分公司', value: '3003'})\n      if (ssgs) {\n        return pageOrganizationSelectedList.filter(g => g.value === ssgs)[0]\n          .label;\n      } else {\n        return \"\";\n      }\n    },\n    async saveRow() {\n      try {\n        let { code } = await saveOrUpdate(this.form);\n        if (code === \"0000\") {\n          this.$message.success(\"操作成功\");\n        }\n      } catch (e) {\n        console.log(e);\n      }\n      //重置page页从1开始\n      this.tableAndPageInfo.pager.pageResize = \"Y\";\n      await this.getData();\n      this.isShowDetails = false;\n    },\n    //删除按钮\n    async deleteRow(objId) {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove(objId).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n      await this.getData();\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false;\n    },\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n\n    inputFocusEvent(val) {\n      if (val.target.name === \"sbfl\") {\n        alter(\"test\");\n        this.showDeviceTree = true;\n        this.isFilter = true;\n      }\n    },\n    //清空表单数据\n    handleClose() {\n      this.form = {};\n      this.$nextTick(() => {\n        this.form = this.$options.data().form;\n        this.resetForm(\"form\");\n      });\n    },\n    //获取变电站下拉框\n    getBdzList() {\n      if (this.title === \"定期工作配置新增\") {\n        this.$set(this.form, \"bdz\", \"\");\n      }\n      this.bdzList = [];\n      getBdzSelectList({ ssdwbm: this.form.ssgs }).then(res => {\n        this.bdzList = res.data;\n      });\n    }\n  }\n};\n</script>\n\n<style scoped></style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/lpbzk"}]}