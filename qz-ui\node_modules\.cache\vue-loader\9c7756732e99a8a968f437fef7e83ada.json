{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sympk\\mpxqInfo.vue?vue&type=style&index=0&id=6f2af6cc&scoped=true&lang=scss&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sympk\\mpxqInfo.vue", "mtime": 1706897323738}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5tcHhxX2luZm8gewogIGRpc3BsYXk6IGZsZXg7Cn0KI21weHFfbGVmdCB7CiAgbWFyZ2luLXJpZ2h0OiAyMHB4OwogIHVsIHsKICAgIGxpc3Qtc3R5bGUtdHlwZTogbm9uZTsKICAgIG1hcmdpbjogMDsKICAgIHBhZGRpbmc6IDhweDsKICB9CiAgYm9yZGVyOiAxcHggc29saWQgIzBjYzI4MzsKICB3aWR0aDogMjglOwogIGxpOm50aC1jaGlsZCgxKSB7CiAgICBmb250LXdlaWdodDogNzAwOwogIH0KICBsaSB7CiAgICBsaW5lLWhlaWdodDogNDhweDsKICAgIHBhZGRpbmctbGVmdDogOHB4OwogICAgLmVsLWlucHV0IHsKICAgICAgd2lkdGg6IDcwJTsKICAgIH0KICB9Cn0KLmNoYW5nZV9idG4gewogIG1hcmdpbi10b3A6IDEwcHggIWltcG9ydGFudDsKICBoZWlnaHQ6IDM2cHggIWltcG9ydGFudDsKfQouY2hhbmdlX2J0bjpudGgtY2hpbGQoMSkgewogIG1hcmdpbi1sZWZ0OiAyOSU7Cn0KLmNoYW5nZV9idG46bnRoLWNoaWxkKDIpIHsKICBtYXJnaW4tbGVmdDogMjAlOwp9CiNtcHhxX3JpZ2h0IHsKICB3aWR0aDogNzIlOwogIGhlaWdodDogMTgwcHg7CiAgYm9yZGVyOiAxcHggc29saWQgIzAwMDsKfQo="}, {"version": 3, "sources": ["mpxqInfo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyrBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "mpxqInfo.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sympk", "sourcesContent": ["<template>\n  <div class=\"mpxq_info\">\n    <div id=\"mpxq_left\">\n      <ul class=\"ul1_cont\">\n        <li>表格</li>\n        <li>\n          行：<el-input\n            id=\"hs\"\n            v-model=\"hs\"\n            placeholder=\"\"\n            class=\"inp1\"\n            @input=\"(val) => checkInput(val, 'hs')\"\n          ></el-input>\n        </li>\n        <li>\n          列：<el-input\n            id=\"ls\"\n            v-model=\"ls\"\n            placeholder=\"\"\n            class=\"inp1\"\n            @input=\"(val) => checkInput(val, 'ls')\"\n          ></el-input>\n        </li>\n        <el-button type=\"success\" @click=\"createTable\" class=\"change_btn\"\n          >创建表格</el-button\n        >\n      </ul>\n      <ul class=\"ul2_cont\">\n        <li>单元格操作</li>\n        <li>\n          行跨度：<el-input\n            id=\"addhs\"\n            v-model=\"addhs\"\n            placeholder=\"\"\n            class=\"inp2\"\n            @input=\"(val) => checkInput(val, 'addhs')\"\n          ></el-input>\n        </li>\n        <li>\n          列跨度：<el-input\n            id=\"addls\"\n            v-model=\"addls\"\n            placeholder=\"\"\n            class=\"inp2\"\n            @input=\"(val) => checkInput(val, 'addls')\"\n          ></el-input>\n        </li>\n        <el-button type=\"success\" @click=\"saveChangeTable\" class=\"change_btn\"\n          >保存</el-button\n        >\n        <el-button @click=\"clearChangeTable\" class=\"change_btn\">清除</el-button>\n      </ul>\n      <ul class=\"ul3_cont\">\n        <el-button type=\"warning\" @click=\"saveTdValue\">编辑单元格</el-button>\n        <el-button type=\"warning\" @click=\"resetTable\">重置单元格</el-button>\n      </ul>\n    </div>\n    <table id=\"mpxq_right\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\"></table>\n    <!--    <table class=\"mpxq_right\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n      <tr>\n        <td>变电站</td>\n        <td>委托单位</td>\n        <td>试验单位</td>\n        <td>运行编号</td>\n      </tr>\n      <tr>\n        <td>试验性质</td>\n        <td>试验日期</td>\n        <td>试验人员</td>\n        <td>试验地点</td>\n      </tr>\n      <tr>\n        <td>报告日期</td>\n        <td>编写人</td>\n        <td>审核人</td>\n        <td>批准人</td>\n      </tr>\n      <tr>\n        <td>试验天气</td>\n        <td>环境温度（℃）</td>\n        <td>环境相对湿度（%）</td>\n        <td>投运日期</td>\n      </tr>\n    </table>-->\n\n    <el-dialog\n      :title=\"title\"\n      v-dialogDrag\n      :visible.sync=\"show\"\n      width=\"50%\"\n      append-to-body\n      @close=\"getInsterClose\"\n    >\n      <el-form label-width=\"130px\" ref=\"form\" :model=\"form\">\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否只读：\" prop=\"readonly\">\n              <el-select\n                placeholder=\"请选择是否只读\"\n                v-model=\"form.readonly\"\n                style=\"width: 100%\"\n                @change=\"selectvalue\"\n              >\n                <el-option\n                  v-for=\"item in readonlyList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"内容类型：：\" prop=\"nrlx\">\n              <el-select\n                placeholder=\"请选择内容类型\"\n                v-model=\"form.nrlx\"\n                style=\"width: 100%\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option\n                  v-for=\"item in nrlxList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"属性名称：\" prop=\"nrbs\">\n              <el-input\n                v-model=\"form.nrbs\"\n                placeholder=\"请输入属性名称\"\n                :disabled=\"isDisabledN\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"getInsterClose\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"saveRow\">保 存</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { Loading } from \"element-ui\";\nimport {\n  resetCells,\n  createTable,\n  mergeCells,\n  editCells,\n  getCells,\n} from \"@/api/dagangOilfield/bzgl/sympk/sympInfo\";\nimport { getTable } from \"@/api/dagangOilfield/bzgl/sympk/sympk\";\n\nexport default {\n  props: {\n    mpData: {\n      type: Object,\n    },\n    mxData: {\n      type: Array,\n    },\n  },\n  name: \"mpxqInfo\",\n  inject: [\"reload\"], //inject注入根组件的reload方法\n  data() {\n    return {\n      //初始表格的行数 列数\n      hs: \"\",\n      ls: \"\",\n      //初始合并行数 列数\n      addhs: \"\",\n      addls: \"\",\n      //选中合并行、列的tr\n      changeTr: \"\",\n      //查询条件\n      params: {\n        pageNum: 1,\n        pageSize: 10,\n        mpid: \"\",\n        zy: \"\",\n        sblxbm: \"\",\n      },\n      title: \"单元格属性定义\",\n      form: {\n        objId: undefined,\n        readonly: undefined,\n        nrlx: undefined,\n        nrbs: undefined,\n        mpid: undefined,\n        readonlyMc: undefined,\n      },\n      loading: null, //遮罩层\n      tdWidth: 0, //一个单元格所占宽度\n      tdMap: new Map(), //用于存放被合并或拆分的单元格（key:当前点击的单元格,value:被处理过的单元格数组）\n      tableData: this.mxData, //表格数据\n      show: false,\n      cellData: \"\",\n      isDisabled: false,\n      isDisabledN: false,\n      nrlxList: [\n        { label: \"静态文本\", value: \"静态文本\" },\n        { label: \"铭牌实验数据\", value: \"铭牌实验数据\" },\n      ],\n      readonlyList: [\n        { label: \"是\", value: \"Y\" },\n        { label: \"否\", value: \"N\" },\n      ],\n      readonlyvalu: \"\", //选择值\n      readonlySelect: \"\", //选择值显示值\n    };\n  },\n  mounted() {\n    //获取表格初始行数和列数\n    this.initTableData();\n  },\n  methods: {\n    //获取铭牌内容数据\n    initTableData() {\n      //初始化遮罩层\n      this.loading = Loading.service({\n        text: \"加载中，请稍后...\",\n        background: \"rgba(109,106,106,0.35)\",\n      });\n      this.hs = typeof (this.mpData.AHs) != 'undefined'?this.mpData.AHs:this.mpData.aHs;\n      this.ls = typeof (this.mpData.ALs) != 'undefined'?this.mpData.ALs:this.mpData.aLs;\n\n      //更新输入框的值\n      this.updateInputValue([\"hs\", \"ls\"]);\n      this.processTable();\n      this.loading.close(); //关闭遮罩层\n    },\n    //根据行数和列数创建表格\n    processTable() {\n      var tbody = document.getElementById(\"mpxq_right\");\n      if (tbody != null) {\n        tbody.innerHTML = \"\";\n        let hs = this.hs;\n        let ls = this.ls;\n        this.tdWidth = 100 / Number(ls);\n        let str = \"\";\n        for (let i = 0; i < hs; i++) {\n          let temp = \"<tr>\";\n          for (let j = 0; j < this.tableData.length; j++) {\n            let item = this.tableData[j];\n            let nrbs = item.nrbs == null ? \"-\" : item.nrbs;\n            if (item.rowindex === i.toString()) {\n              temp +=\n                \"<td class='trName' id='\" +\n                item.objId +\n                \"' style='min-width:60px;max-width:180px;width: \" +\n                this.tdWidth * item.colspan +\n                \"%' rowspan='\" +\n                item.rowspan +\n                \"' colspan='\" +\n                item.colspan +\n                \"'>\" +\n                nrbs +\n                \"</td>\";\n            }\n          }\n          temp += \"</tr>\";\n          str += temp;\n        }\n        tbody.innerHTML = str;\n        // //给循环出来的单元格加上点击事件\n        this.addClickEvent();\n      }\n    },\n    //手动创建表格\n    createTable() {\n      //初始化遮罩层\n      this.loading = Loading.service({\n        text: \"加载中，请稍后...\",\n        background: \"rgba(109,106,106,0.35)\",\n      });\n      let params = JSON.stringify({\n        objId: this.mpData.objId, //铭牌id\n        aHs: Number(this.hs), //行数\n        aLs: Number(this.ls), //列数\n        lbbs: \"A\", //类别标识，表示修改的A表格\n      });\n      createTable(params).then((res) => {\n        console.log('手动创建表格',res)\n        if (res.code === \"0000\") {\n          this.updateTable();\n        } else {\n          this.$message.error(\"操作失败\");\n          this.loading.close(); //关闭遮罩层\n        }\n      });\n    },\n    //给循环出来的单元格加上点击事件\n    addClickEvent() {\n      let trArr = document.getElementsByClassName(\"trName\"); //不可编辑的单元格\n      let inputArr = document.getElementsByClassName(\"input_cls\"); //可编辑的单元格\n      let that = this;\n      if (trArr != null) {\n        //循环所有的tr\n        for (let i = 0; i < trArr.length; i++) {\n          trArr[i].onclick = function () {\n            that.changeTr = this;\n            that.addhs = that.changeTr.rowSpan;\n            that.addls = that.changeTr.colSpan;\n            that.cellData = that.getCellEle(that.changeTr.id);\n          };\n        }\n      }\n      if (inputArr != null) {\n        //循环所有的tr\n        for (let i = 0; i < inputArr.length; i++) {\n          inputArr[i].onclick = function () {\n            that.changeTr = this;\n            that.addhs = that.changeTr.rowSpan;\n            that.addls = that.changeTr.colSpan;\n          };\n        }\n      }\n    },\n    //合并拆分保存\n    saveChangeTable() {\n      //初始化遮罩层\n      this.loading = Loading.service({\n        text: \"加载中，请稍后...\",\n        background: \"rgba(109,106,106,0.35)\",\n      });\n      //看要合并的是第几行几列的tr   id格式  0|0 0|1\n      /*let id = this.changeTr.id;\n      let addh = Number(id.split('|')[0]);  //取当前元素的行\n      let addl = Number(id.split('|')[1]);  //取当前元素的列\n      let hs = Number(this.addhs);//行数\n      let ls = Number(this.addls);//列数*/\n\n      let params = JSON.stringify({\n        objId: this.changeTr.id,\n        rowspan: this.addhs,\n        colspan: this.addls,\n      });\n      //先请求接口，如果后台可以执行合并或拆分，则将最新的表格数据请求回来进行前端展示\n      mergeCells(params).then((res) => {\n        if (res.code === \"0000\") {\n          this.updateTable();\n        } else {\n          this.$message.error(\"操作失败\");\n          this.loading.close(); //关闭遮罩层\n        }\n      });\n    },\n    //处理合并或拆分\n    processTr(ids) {\n      //点击的单元格id\n      let clickId = this.changeTr.id;\n      let arr1 = []; //需要重新设置map的数组\n      //如果之前已经处理过该单元格,则先将其还原\n      if (this.tdMap.has(clickId)) {\n        this.tdMap.get(clickId).forEach((item) => {\n          if (item != null) {\n            this.resetCell(item);\n            item.style.display = \"table-cell\";\n          }\n        });\n        //操作完后将数据从map中删除\n        this.tdMap.delete(clickId);\n      }\n      let processEle = []; //被处理的元素\n\n      //现将连带受影响的单元格还原，再进行隐藏处理\n      if (ids.length > 0) {\n        //执行还原\n        ids.forEach((id1) => {\n          let ele = document.getElementById(id1);\n          //如果此次处理的单元格中有已经被处理过的，先将其还原\n          if (this.tdMap.has(id1)) {\n            this.tdMap.get(id1).forEach((item) => {\n              this.resetCell(item);\n              item.style.display = \"table-cell\";\n            });\n            //操作完后将数据从map中删除\n            this.tdMap.delete(id1);\n          }\n          //处理被连带的已经合并过的单元格\n          if (ele) {\n            let className = ele.className;\n            if (this.tdMap.has(className)) {\n              let mergeCell = document.getElementById(className); //被连带的已经合并过的cell\n              if (mergeCell) {\n                this.resetCell(mergeCell);\n              }\n              this.tdMap.get(className).forEach((item) => {\n                //需要把此次要隐藏的单元格排除掉，不然隐藏完下次循环又会放出来\n                if (!ids.includes(item)) {\n                  item.style.display = \"table-cell\";\n                } else {\n                  arr1.push(item);\n                }\n              });\n              //处理完成后，更新map中的值，将处理过的排除掉\n              if (arr1.length > 0) {\n                this.tdMap.set(className, arr1);\n              } else {\n                //操作完后将数据从map中删除\n                this.tdMap.delete(className);\n              }\n            }\n          }\n        });\n        //执行隐藏\n        ids.forEach((id) => {\n          let ele = document.getElementById(id);\n          //将多余的单元格隐藏\n          if (ele) {\n            processEle.push(ele); //添加数据保存到map中\n\n            document.getElementById(id).style.display = \"none\";\n            //将className设置给被操作的单元格，方便下次有连带操作时对单元格进行处理\n            document.getElementById(id).className = clickId;\n          }\n        });\n        //重新设置map中的值\n        this.tdMap.set(clickId, processEle);\n      }\n    },\n    //取消更改的合并行、列数\n    clearChangeTable() {},\n\n    selectvalue(val) {\n      let obj = {};\n      obj = this.readonlyList.find((item) => {\n        return item.value === val;\n      });\n      if (obj.value === \"N\") {\n        this.form.nrlx = \"铭牌实验数据\";\n        this.isDisabledN = true;\n      } else {\n        this.form.nrlx = \"静态文本\";\n        this.isDisabledN = false;\n      }\n      this.isDisabled = true;\n      // this.form.readonly = +obj.value;\n    },\n\n    //关闭弹窗\n    getInsterClose() {\n      this.show = false;\n      this.form = {};\n    },\n\n    //进行合并或拆分操作\n    mergeTable(hs, ls, addh, addl) {\n      if (hs === 1) {\n        //合并列\n        if (ls >= 1) {\n          this.mergeCells(addh, addl, hs, ls);\n        }\n      } else {\n        if (hs > 1) {\n          //多行\n          //合并行\n          if (ls === 1) {\n            this.mergeRows(addh, addl, hs, ls);\n          } else if (ls > 1) {\n            //合并多行多列\n            this.mergeRowsAndCells(addh, addl, hs, ls);\n          }\n        }\n      }\n      //要合并的单元格进行合并\n      this.changeTr.style.width = this.tdWidth * ls + \"%\"; //设置合并后的单元格宽度\n      this.changeTr.rowSpan = this.addhs;\n      this.changeTr.colSpan = this.addls;\n    },\n    /**\n     * 第一种情况，合并列（一行多列）\n     * @param h 当前元素所在行\n     * @param l 当前元素所在列\n     * @param hs 要合并的行数\n     * @param ls 要合并的列数\n     */\n    mergeCells(h, l, hs, ls) {\n      let removeIds = []; //要删除的元素的id数组\n      let ls_xh = ls; //要循环的列数\n      if (ls > this.ls - l) {\n        //不能超过剩余可操作的列数\n        ls_xh = this.ls - l;\n      }\n      for (let i = 1; i < ls_xh; i++) {\n        removeIds.push(h + \"|\" + (l + i));\n      }\n      //删除多余单元格\n      this.processTr(removeIds);\n    },\n    /**\n     * 第二种情况，合并行（多行一列）\n     * @param h 当前元素所在行\n     * @param l 当前元素所在列\n     * @param hs 要合并的行数\n     * @param ls 要合并的列数\n     */\n    mergeRows(h, l, hs, ls) {\n      let removeIds = []; //要删除的元素的id数组\n      let hs_xh = hs; //要循环的行数\n      if (hs > this.hs - h) {\n        //不能超过剩余可操作的行数\n        hs_xh = this.hs - h;\n      }\n      console.log(\"hs_xh\", hs_xh);\n      for (let i = 1; i < hs_xh; i++) {\n        removeIds.push(h + i + \"|\" + l);\n      }\n      //删除多余单元格\n      this.processTr(removeIds);\n    },\n    /**\n     * 第三种情况，合并多行多列\n     * @param h 当前元素所在行\n     * @param l 当前元素所在列\n     * @param hs 要合并的行数\n     * @param ls 要合并的列数\n     */\n    mergeRowsAndCells(h, l, hs, ls) {\n      let removeIds = []; //要删除的元素的id数组\n      let removeId = \"\";\n      //先循环行（从当前行开始循环）\n      for (let j = 0; j < hs; j++) {\n        //循环列\n        for (let i = 0; i < ls; i++) {\n          //从当前列循环\n          removeId = h + j + \"|\" + (l + i);\n          //将当前单元格排除掉\n          if (removeId !== h + \"|\" + l) {\n            removeIds.push(removeId);\n          }\n        }\n      }\n      //删除多余单元格\n      this.processTr(removeIds);\n    },\n    //更新输入框的值\n    updateInputValue(arrs) {\n      for (let i = 0; i < arrs.length; i++) {\n        let ele = document.getElementById(arrs[i]);\n        if (ele != null && typeof ele != \"undefined\") {\n          switch (arrs[i]) {\n            case \"hs\":\n              ele.value = this.hs;\n              break;\n            case \"ls\":\n              ele.value = this.ls;\n              break;\n            case \"addhs\":\n              ele.value = this.addhs;\n              break;\n            case \"addls\":\n              ele.value = this.addls;\n              break;\n          }\n        }\n      }\n    },\n    //重置单元格内容\n    resetTable() {\n      //初始化遮罩层\n      this.loading = Loading.service({\n        text: \"加载中，请稍后...\",\n        background: \"rgba(109,106,106,0.35)\",\n      });\n      let objId = this.changeTr.id;\n      let params = this.getCellEle(objId);\n      resetCells(params).then((res) => {\n        if (res.code === \"0000\") {\n          this.updateTable();\n        } else {\n          this.$message.error(\"操作失败\");\n        }\n        this.loading.close(); //关闭遮罩层\n      });\n    },\n    //单元格属性编辑并保存\n    saveTdValue() {\n      this.show = true;\n      //初始化遮罩层\n      // this.loading = Loading.service({\n      //   text:\"加载中，请稍后...\",\n      //   background:'rgba(109,106,106,0.35)',\n      // })\n      // let objId = this.changeTr.id;\n      // let val = this.changeTr.getElementsByTagName(\"input\")[0].value;\n      // let params = this.getCellEle(objId);\n      // params.nrbs = val;\n      // editCells(params).then(res=>{\n      //   if(res.code==='0000'){\n      //     this.updateTable();\n      //   }else{\n      //     this.$message.error('操作失败');\n      //     this.loading .close();//关闭遮罩层\n      //   }\n      // })\n    },\n\n    //单元格属性编辑\n    async saveRow() {\n      try {\n        this.form.objId = this.cellData.objId;\n        this.form.mpid = this.cellData.mpid;\n        let { code } = await editCells(this.form);\n        if (code === \"0000\") {\n          this.updateTable();\n          this.$message.success(\"操作成功\");\n        }\n      } catch (e) {}\n      this.show = false;\n    },\n\n    //重置单元格属性（宽度，合并行数，合并列数）\n    resetCell(ele) {\n      if (ele) {\n        ele.style.width = this.tdWidth + \"%\";\n        ele.rowSpan = \"1\";\n        ele.colSpan = \"1\";\n      }\n    },\n    //输入框校验\n    checkInput(val, changeType) {\n      switch (changeType) {\n        case \"hs\":\n          this.hs = val;\n          break;\n        case \"ls\":\n          this.ls = val;\n          break;\n        case \"addhs\":\n          this.addhs = val;\n          break;\n        case \"addls\":\n          this.addls = val;\n          break;\n      }\n    },\n    //获取单元格明细数据\n    getCellDetail(hs, ls) {\n      let result = null;\n      this.tableData.forEach((item) => {\n        if (\n          item.rowindex === hs.toString() &&\n          item.colindex === ls.toString()\n        ) {\n          result = item;\n          if (result.nrbs == null) {\n            result.nrbs = \"\";\n          }\n        }\n      });\n      return result;\n    },\n    //获取某个单元格对象\n    getCellEle(objId) {\n      let result = null;\n      this.tableData.forEach((item) => {\n        if (item.objId === objId) {\n          result = item;\n        }\n      });\n      return result;\n    },\n    //获取最新的表格并重新渲染\n    updateTable() {\n      let param = JSON.stringify({\n        obj_id: this.mpData.objId,\n        lbbs: \"A\",\n      });\n      //获取最新的表格数据\n      getTable(param).then((res1) => {\n        console.log('res表格数据',res1);\n        if (res1.code === \"0000\") {\n          this.tableData = res1.data;\n          //根据最新的表格数据重新画\n          this.processTable();\n          this.$message.success(res1.msg);\n        } else {\n          this.$message.error(\"无法获取更新后的表格数据！\");\n        }\n        this.loading.close(); //关闭遮罩层\n      });\n    },\n  },\n};\n</script>\n\n<style scoped lang=\"scss\">\n.mpxq_info {\n  display: flex;\n}\n#mpxq_left {\n  margin-right: 20px;\n  ul {\n    list-style-type: none;\n    margin: 0;\n    padding: 8px;\n  }\n  border: 1px solid #0cc283;\n  width: 28%;\n  li:nth-child(1) {\n    font-weight: 700;\n  }\n  li {\n    line-height: 48px;\n    padding-left: 8px;\n    .el-input {\n      width: 70%;\n    }\n  }\n}\n.change_btn {\n  margin-top: 10px !important;\n  height: 36px !important;\n}\n.change_btn:nth-child(1) {\n  margin-left: 29%;\n}\n.change_btn:nth-child(2) {\n  margin-left: 20%;\n}\n#mpxq_right {\n  width: 72%;\n  height: 180px;\n  border: 1px solid #000;\n}\n</style>\n<style>\n#mpxq_right td {\n  border: 1px solid #000;\n  height: 35px;\n  line-height: 35px;\n  text-align: center;\n}\n#mpxq_right tr {\n  height: 35px;\n}\n#mpxq_right .atc {\n  background-color: #11ba6d;\n}\n#mpxq_right .input_cls {\n  text-align: center;\n  border: none;\n  width: 99%;\n  height: 99%;\n}\n</style>\n"]}]}