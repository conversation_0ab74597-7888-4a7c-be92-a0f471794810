{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\pjdzwh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\pjdzwh.vue", "mtime": 1706897323226}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwIik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKCnZhciBfb2JqZWN0U3ByZWFkMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovU2hhbW1wb29sL3dvcmsvY29kZS9kZ3l0LzAxXHU0RUUzXHU3ODAxL3F6LXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL29iamVjdFNwcmVhZDIiKSk7CgpyZXF1aXJlKCJyZWdlbmVyYXRvci1ydW50aW1lL3J1bnRpbWUiKTsKCnZhciBfYXN5bmNUb0dlbmVyYXRvcjIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkQ6L1NoYW1tcG9vbC93b3JrL2NvZGUvZGd5dC8wMVx1NEVFM1x1NzgwMS9xei11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9hc3luY1RvR2VuZXJhdG9yIikpOwoKdmFyIF9wamR6d2ggPSByZXF1aXJlKCJAL2FwaS9kYWdhbmdPaWxmaWVsZC9iemdsL3NienRwamJ6ay9wamR6d2giKTsKCnZhciBfenRsbXh3aCA9IHJlcXVpcmUoIkAvYXBpL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvc2J6dHBqYnprL3p0bG14d2giKTsKCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IHsKICBuYW1lOiAicGpkendoIiwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgdHJlZWRhdGE6IFtdLAogICAgICAvL+agkee7k+aehAogICAgICBkZWZhdWx0UHJvcHM6IHsKICAgICAgICBjaGlsZHJlbjogImNoaWxkcmVuIiwKICAgICAgICBsYWJlbDogImxhYmVsIgogICAgICB9LAogICAgICBmb3JtOiB7fSwKICAgICAgLy/mn6Xor6Lor4Tku7fpg6jkvY3lkozorr7lpIfnp43nsbsKICAgICAgcXVlcnlwamR6d2hQYXJhbTogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwCiAgICAgIH0sCiAgICAgIC8v5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy/orr7lpIfpg6jku7bliKDpmaTpgInmi6nliJcKICAgICAgc2VsZWN0Um93czogW10sCiAgICAgIC8v5paw5aKe5oyJ6ZKu5o6n5Yi2CiAgICAgIGFkZERpc2FibGVkOiB0cnVlLAogICAgICAvL+eCueWHu+agkeiKgueCuei1i+WAvAogICAgICB0cmVlRm9ybToge30sCiAgICAgIC8v5piv5ZCm56aB55SoCiAgICAgIGlzRGlzYWJsZWQ6IGZhbHNlLAogICAgICAvL+ivhOS7t+mDqOS7tuWIl+ihqAogICAgICB0YWJsZUFuZFBhZ2VJbmZvOiB7CiAgICAgICAgcGFnZXI6IHsKICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgIHNpemVzOiBbMTAsIDIwLCA1MCwgMTAwXQogICAgICAgIH0sCiAgICAgICAgdGFibGVEYXRhOiBbXSwKICAgICAgICB0YWJsZUhlYWRlcjogW3sKICAgICAgICAgIGxhYmVsOiAi6K+E5Lu36K6+5aSHIiwKICAgICAgICAgIHByb3A6ICJzYmx4IiwKICAgICAgICAgIGFsaWduOiAiY2VudGVyIgogICAgICAgIH0sIHsKICAgICAgICAgIGxhYmVsOiAi6YOo5Lu25ZCN56ewIiwKICAgICAgICAgIHByb3A6ICJiam1jIiwKICAgICAgICAgIGFsaWduOiAiY2VudGVyIgogICAgICAgIH0sIC8vIHtsYWJlbDogIuadg+mHjSIsIHByb3A6ICJxeiIsIGFsaWduOiAiY2VudGVyIn0sCiAgICAgICAgLy8gICB7IGxhYmVsOiAi6K6+5aSH56eN57G7IiwgcHJvcDogInNiemxtYyIsIGFsaWduOiAiY2VudGVyIiB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAi6K+E5Lu36IyD5Zu0IiwKICAgICAgICAgIHByb3A6ICJwamZ3IiwKICAgICAgICAgIGFsaWduOiAiY2VudGVyIgogICAgICAgIH0sIHsKICAgICAgICAgIGxhYmVsOiAi6K+E5Lu35YaF5a65IiwKICAgICAgICAgIHByb3A6ICJwam5yIiwKICAgICAgICAgIGFsaWduOiAiY2VudGVyIgogICAgICAgIH0sIHsKICAgICAgICAgIHByb3A6ICJvcGVyYXRpb24iLAogICAgICAgICAgbGFiZWw6ICLmk43kvZwiLAogICAgICAgICAgbWluV2lkdGg6ICIxMDBweCIsCiAgICAgICAgICBzdHlsZTogewogICAgICAgICAgICBkaXNwbGF5OiAiYmxvY2siCiAgICAgICAgICB9LAogICAgICAgICAgLy/mk43kvZzliJflm7rlrprlho3lj7PkvqcKICAgICAgICAgIGZpeGVkOiAicmlnaHQiLAogICAgICAgICAgb3BlcmF0aW9uOiBbewogICAgICAgICAgICBuYW1lOiAi5L+u5pS5IiwKICAgICAgICAgICAgY2xpY2tGdW46IHRoaXMudXBkYXRlRGV0YWlscwogICAgICAgICAgfSwgewogICAgICAgICAgICBuYW1lOiAi6K+m5oOFIiwKICAgICAgICAgICAgY2xpY2tGdW46IHRoaXMuZ2V0RGV0YWlscwogICAgICAgICAgfV0KICAgICAgICB9XSwKICAgICAgICBvcHRpb246IHsKICAgICAgICAgIGNoZWNrQm94OiB0cnVlLAogICAgICAgICAgc2VyaWFsTnVtYmVyOiB0cnVlCiAgICAgICAgfQogICAgICB9LAogICAgICBydWxlczogewogICAgICAgIGJqbWM6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLpg6jku7blkI3np7DkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgcXo6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLmnYPph43kuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgcGpmdzogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuivhOS7t+iMg+WbtOS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBwam5yOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6K+E5Lu35YaF5a655LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIC8v5piv5ZCm5pi+56S65by55qGGCiAgICAgIGlzU2hvd0RldGFpbHM6IGZhbHNlLAogICAgICAvL+e7hOe7h+agkQogICAgICB0cmVlT3B0aW9uczogW10sCiAgICAgIC8v6YOo5Lu25ZCN56ew5YiX6KGoCiAgICAgIGJqbWNMaXN0OiBbXQogICAgfTsKICB9LAogIHdhdGNoOiB7fSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkge30sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHRoaXMuZ2V0VHJlZU5vZGUoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8v6I635Y+W5qCR6IqC54K55pWw5o2uCiAgICBnZXRUcmVlTm9kZTogZnVuY3Rpb24gZ2V0VHJlZU5vZGUoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CgogICAgICAoMCwgX3p0bG14d2guZ2V0U2JseEFuZFNiYmpUcmVlKSgpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIGlmIChyZXMuY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICBfdGhpcy50cmVlZGF0YSA9IHJlcy5kYXRhOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy/ojrflj5blr7zliJnpg6jku7bliJfooagKICAgIGdldERhdGFCSjogZnVuY3Rpb24gZ2V0RGF0YUJKKHBhcmFtcykgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKCiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKCAvKiNfX1BVUkVfXyovcmVnZW5lcmF0b3JSdW50aW1lLm1hcmsoZnVuY3Rpb24gX2NhbGxlZSgpIHsKICAgICAgICB2YXIgcGFyYW0sIF95aWVsZCRnZXRQYWdlRGF0YUxpcywgZGF0YSwgY29kZTsKCiAgICAgICAgcmV0dXJuIHJlZ2VuZXJhdG9yUnVudGltZS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUkKF9jb250ZXh0KSB7CiAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0LnByZXYgPSBfY29udGV4dC5uZXh0KSB7CiAgICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgICAgX2NvbnRleHQucHJldiA9IDA7CiAgICAgICAgICAgICAgICBwYXJhbSA9ICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSgoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIF90aGlzMi5xdWVyeXBqZHp3aFBhcmFtKSwgcGFyYW1zKTsKICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSA0OwogICAgICAgICAgICAgICAgcmV0dXJuICgwLCBfcGpkendoLmdldFBhZ2VEYXRhTGlzdEJKKShwYXJhbSk7CgogICAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgICAgIF95aWVsZCRnZXRQYWdlRGF0YUxpcyA9IF9jb250ZXh0LnNlbnQ7CiAgICAgICAgICAgICAgICBkYXRhID0gX3lpZWxkJGdldFBhZ2VEYXRhTGlzLmRhdGE7CiAgICAgICAgICAgICAgICBjb2RlID0gX3lpZWxkJGdldFBhZ2VEYXRhTGlzLmNvZGU7CgogICAgICAgICAgICAgICAgaWYgKGNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgICAgICBfdGhpczIudGFibGVBbmRQYWdlSW5mby50YWJsZURhdGEgPSBkYXRhLnJlY29yZHM7CiAgICAgICAgICAgICAgICAgIF90aGlzMi50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnRvdGFsID0gZGF0YS50b3RhbDsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gMTI7CiAgICAgICAgICAgICAgICBicmVhazsKCiAgICAgICAgICAgICAgY2FzZSAxMDoKICAgICAgICAgICAgICAgIF9jb250ZXh0LnByZXYgPSAxMDsKICAgICAgICAgICAgICAgIF9jb250ZXh0LnQwID0gX2NvbnRleHRbImNhdGNoIl0oMCk7CgogICAgICAgICAgICAgIGNhc2UgMTI6CiAgICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5zdG9wKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlLCBudWxsLCBbWzAsIDEwXV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvL+agkeiKgueCueeCueWHu+S6i+S7tgogICAgaGFuZGxlTm9kZUNsaWNrOiBmdW5jdGlvbiBoYW5kbGVOb2RlQ2xpY2soZGF0YSwgbm9kZSkgewogICAgICB0aGlzLmFkZERpc2FibGVkID0gdHJ1ZTsKICAgICAgdGhpcy50cmVlRm9ybS5zYmx4ID0gIiI7CiAgICAgIHRoaXMudHJlZUZvcm0uc2JseGlkID0gIiI7CiAgICAgIHRoaXMucXVlcnlwamR6d2hQYXJhbS5vYmpJZCA9ICIiOwogICAgICB0aGlzLnF1ZXJ5cGpkendoUGFyYW0uc2JseGlkID0gIiI7CgogICAgICBpZiAoZGF0YS5ub2RlTGV2ZWwgPT09ICIxIikgewogICAgICAgIHRoaXMudHJlZUZvcm0uc2JseCA9IGRhdGEubGFiZWw7CiAgICAgICAgdGhpcy50cmVlRm9ybS5zYmx4aWQgPSBkYXRhLmlkOwogICAgICAgIHRoaXMucXVlcnlwamR6d2hQYXJhbS5zYmx4aWQgPSBkYXRhLmlkOwogICAgICAgIHRoaXMuYWRkRGlzYWJsZWQgPSBmYWxzZTsKICAgICAgICB0aGlzLmdldERhdGFCSigpOwogICAgICB9CiAgICAgIC8qIGlmIChkYXRhLm5vZGVMZXZlbCA9PT0gIjIiKSB7CiAgICAgICAgIHRoaXMudHJlZUZvcm0uc2JseCA9IG5vZGUucGFyZW50LmRhdGEubGFiZWw7CiAgICAgICAgIHRoaXMudHJlZUZvcm0uc2JseGlkID0gbm9kZS5wYXJlbnQuZGF0YS5pZDsKICAgICAgICAgdGhpcy5xdWVyeXBqZHp3aFBhcmFtLm9iaklkID0gZGF0YS5pZDsKICAgICAgICAgdGhpcy5hZGREaXNhYmxlZCA9IGZhbHNlOwogICAgICAgICB0aGlzLmdldERhdGFCSigpOwogICAgICAgfSovCgogICAgfSwKICAgIC8v6YOo5Lu25L+u5pS5CiAgICB1cGRhdGVEZXRhaWxzOiBmdW5jdGlvbiB1cGRhdGVEZXRhaWxzKHJvdykgewogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlOwogICAgICB0aGlzLmZvcm0gPSByb3c7CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICB0aGlzLnRpdGxlID0gIuS/ruaUuSI7CiAgICB9LAogICAgLy/pg6jku7bor6bmg4UKICAgIGdldERldGFpbHM6IGZ1bmN0aW9uIGdldERldGFpbHMocm93KSB7CiAgICAgIHRoaXMudGl0bGUgPSAi6K+m5oOFIjsgLy/miZPlvIDlvLnnqpcKCiAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IHRydWU7IC8v5oqK6KGM5pWw5o2u57uZ5by55Ye65qGG6KGo5Y2VCgogICAgICB0aGlzLmZvcm0gPSAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIHJvdyk7IC8v5bCG6KGo5Y2V5LiN5Y+v57yW6L6RCgogICAgICB0aGlzLmlzRGlzYWJsZWQgPSB0cnVlOwogICAgfSwKICAgIC8v6K6+5aSH6YOo5Lu25o+Q5LqkCiAgICBjb21taXRBZGQ6IGZ1bmN0aW9uIGNvbW1pdEFkZCgpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CgogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICAoMCwgX3BqZHp3aC5zYXZlT3JVcGRhdGVCSikoX3RoaXMzLmZvcm0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgIF90aGlzMy4kbWVzc2FnZS5zdWNjZXNzKHJlcy5tc2cpOwoKICAgICAgICAgICAgICBfdGhpczMudGFibGVBbmRQYWdlSW5mby5wYWdlci5wYWdlUmVzaXplID0gIlkiOwoKICAgICAgICAgICAgICBfdGhpczMuZ2V0RGF0YUJKKCk7CgogICAgICAgICAgICAgIF90aGlzMy5pc1Nob3dEZXRhaWxzID0gZmFsc2U7CiAgICAgICAgICAgICAgX3RoaXMzLmZvcm0gPSB7fTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICBfdGhpczMuJG1lc3NhZ2UuZXJyb3IocmVzLm1zZyk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy/orr7lpIfpg6jku7blhbPpl60KICAgIGNsb3NlOiBmdW5jdGlvbiBjbG9zZSgpIHsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gZmFsc2U7CiAgICAgIHRoaXMuZm9ybSA9IHt9OwogICAgfSwKICAgIC8v5aKe5YqgCiAgICBhZGRTZW5zb3JCdXR0b246IGZ1bmN0aW9uIGFkZFNlbnNvckJ1dHRvbigpIHsKICAgICAgdGhpcy5mb3JtLnNibHggPSB0aGlzLnRyZWVGb3JtLnNibHg7CiAgICAgIHRoaXMuZm9ybS5zYmx4aWQgPSB0aGlzLnRyZWVGb3JtLnNibHhpZDsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gdHJ1ZTsKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gZmFsc2U7CiAgICAgIHRoaXMudGl0bGUgPSAi5paw5aKeIjsKICAgIH0sCiAgICAvL+WIoOmZpAogICAgZGVsZXRlU2Vuc29yQnV0dG9uOiBmdW5jdGlvbiBkZWxldGVTZW5zb3JCdXR0b24oKSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwoKICAgICAgaWYgKHRoaXMuc2VsZWN0Um93cy5sZW5ndGggPCAxKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7fpgInmi6nmraPnoa7nmoTmlbDmja7vvIHvvIHvvIEiKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIHZhciBpZHMgPSB0aGlzLnNlbGVjdFJvd3MubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgcmV0dXJuIGl0ZW0ub2JqSWQ7CiAgICAgIH0pOwogICAgICB0aGlzLiRjb25maXJtKCLmraTmk43kvZzlsIbmsLjkuYXliKDpmaTor6XmlbDmja4sIOaYr+WQpue7p+e7rT8iLCAi5o+Q56S6IiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgKDAsIF9wamR6d2gucmVtb3ZlQkopKGlkcykudGhlbihmdW5jdGlvbiAoX3JlZikgewogICAgICAgICAgdmFyIGNvZGUgPSBfcmVmLmNvZGU7CgogICAgICAgICAgaWYgKGNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICBfdGhpczQuJG1lc3NhZ2UoewogICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgICAgICBtZXNzYWdlOiAi5Yig6Zmk5oiQ5YqfISIKICAgICAgICAgICAgfSk7CgogICAgICAgICAgICBfdGhpczQudGFibGVBbmRQYWdlSW5mby5wYWdlci5wYWdlUmVzaXplID0gIlkiOwoKICAgICAgICAgICAgX3RoaXM0LmdldERhdGFCSigpOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgX3RoaXM0LiRtZXNzYWdlKHsKICAgICAgICAgICAgICB0eXBlOiAiZXJyb3IiLAogICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTlpLHotKUhIgogICAgICAgICAgICB9KTsKCiAgICAgICAgICAgIF90aGlzNC50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnBhZ2VSZXNpemUgPSAiWSI7CgogICAgICAgICAgICBfdGhpczQuZ2V0RGF0YUJKKCk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczQuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogImluZm8iLAogICAgICAgICAgbWVzc2FnZTogIuW3suWPlua2iOWIoOmZpCIKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgLy/ojrflj5booYzmlbDmja4KICAgIHNlbGVjdENoYW5nZTogZnVuY3Rpb24gc2VsZWN0Q2hhbmdlKHJvd3MpIHsKICAgICAgdGhpcy5zZWxlY3RSb3dzID0gcm93czsKICAgIH0KICB9Cn07CmV4cG9ydHMuZGVmYXVsdCA9IF9kZWZhdWx0Ow=="}, {"version": 3, "sources": ["pjdzwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAwHA;;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,QADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,QAAA,EAAA,EADA;AAEA;AACA,MAAA,YAAA,EAAA;AACA,QAAA,QAAA,EAAA,UADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAHA;AAOA,MAAA,IAAA,EAAA,EAPA;AAQA;AACA,MAAA,gBAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA;AAFA,OATA;AAaA;AACA,MAAA,KAAA,EAAA,EAdA;AAeA;AACA,MAAA,UAAA,EAAA,EAhBA;AAiBA;AACA,MAAA,WAAA,EAAA,IAlBA;AAmBA;AACA,MAAA,QAAA,EAAA,EApBA;AAqBA;AACA,MAAA,UAAA,EAAA,KAtBA;AAuBA;AACA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA,EAGA;AACA;AACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SANA,EAOA;AACA,UAAA,IAAA,EAAA,WADA;AAEA,UAAA,KAAA,EAAA,IAFA;AAGA,UAAA,QAAA,EAAA,OAHA;AAIA,UAAA,KAAA,EAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WAJA;AAKA;AACA,UAAA,KAAA,EAAA,OANA;AAOA,UAAA,SAAA,EAAA,CACA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WADA,EAEA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WAFA;AAPA,SAPA,CARA;AA4BA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AA5BA,OAxBA;AAuDA,MAAA,KAAA,EAAA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,EAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,QAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAJA;AAKA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CALA;AAQA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AARA,OAvDA;AAmEA;AACA,MAAA,aAAA,EAAA,KApEA;AAqEA;AACA,MAAA,WAAA,EAAA,EAtEA;AAuEA;AACA,MAAA,QAAA,EAAA;AAxEA,KAAA;AA0EA,GA7EA;AA8EA,EAAA,KAAA,EAAA,EA9EA;AA+EA,EAAA,OA/EA,qBA+EA,CACA,CAhFA;AAiFA,EAAA,OAjFA,qBAiFA;AACA,SAAA,WAAA;AACA,GAnFA;AAoFA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,WAFA,yBAEA;AAAA;;AACA,yCAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,KAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA;AACA,OAJA;AAKA,KARA;AASA;AACA,IAAA,SAVA,qBAUA,MAVA,EAUA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,KAFA,+DAEA,MAAA,CAAA,gBAFA,GAEA,MAFA;AAAA;AAAA,uBAGA,+BAAA,KAAA,CAHA;;AAAA;AAAA;AAGA,gBAAA,IAHA,yBAGA,IAHA;AAGA,gBAAA,IAHA,yBAGA,IAHA;;AAIA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AAPA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KArBA;AAsBA;AACA,IAAA,eAvBA,2BAuBA,IAvBA,EAuBA,IAvBA,EAuBA;AACA,WAAA,WAAA,GAAA,IAAA;AACA,WAAA,QAAA,CAAA,IAAA,GAAA,EAAA;AACA,WAAA,QAAA,CAAA,MAAA,GAAA,EAAA;AACA,WAAA,gBAAA,CAAA,KAAA,GAAA,EAAA;AACA,WAAA,gBAAA,CAAA,MAAA,GAAA,EAAA;;AACA,UAAA,IAAA,CAAA,SAAA,KAAA,GAAA,EAAA;AACA,aAAA,QAAA,CAAA,IAAA,GAAA,IAAA,CAAA,KAAA;AACA,aAAA,QAAA,CAAA,MAAA,GAAA,IAAA,CAAA,EAAA;AACA,aAAA,gBAAA,CAAA,MAAA,GAAA,IAAA,CAAA,EAAA;AACA,aAAA,WAAA,GAAA,KAAA;AACA,aAAA,SAAA;AACA;AAEA;;;;;;;;AAOA,KA5CA;AA8CA;AACA,IAAA,aA/CA,yBA+CA,GA/CA,EA+CA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,IAAA,GAAA,GAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,KAAA,GAAA,IAAA;AACA,KApDA;AAqDA;AACA,IAAA,UAtDA,sBAsDA,GAtDA,EAsDA;AACA,WAAA,KAAA,GAAA,IAAA,CADA,CAEA;;AACA,WAAA,aAAA,GAAA,IAAA,CAHA,CAIA;;AACA,WAAA,IAAA,mCAAA,GAAA,EALA,CAMA;;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KA9DA;AAgEA;AACA,IAAA,SAjEA,uBAiEA;AAAA;;AACA,WAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,sCAAA,MAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,GAAA,CAAA,GAAA;;AACA,cAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,cAAA,MAAA,CAAA,SAAA;;AACA,cAAA,MAAA,CAAA,aAAA,GAAA,KAAA;AACA,cAAA,MAAA,CAAA,IAAA,GAAA,EAAA;AACA,aANA,MAMA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA;AACA;AACA,WAVA;AAWA;AACA,OAdA;AAeA,KAjFA;AAmFA;AACA,IAAA,KApFA,mBAoFA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,WAAA,IAAA,GAAA,EAAA;AACA,KAvFA;AAyFA;AACA,IAAA,eA1FA,6BA0FA;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,KAAA,QAAA,CAAA,IAAA;AACA,WAAA,IAAA,CAAA,MAAA,GAAA,KAAA,QAAA,CAAA,MAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,KAAA,GAAA,IAAA;AACA,KAhGA;AAkGA;AACA,IAAA,kBAnGA,gCAmGA;AAAA;;AACA,UAAA,KAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,aAAA;AACA;AACA;;AACA,UAAA,GAAA,GAAA,KAAA,UAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA;AACA,OAFA,CAAA;AAGA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,8BAAA,GAAA,EAAA,IAAA,CAAA,gBAAA;AAAA,cAAA,IAAA,QAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,YAAA,MAAA,CAAA,SAAA;AACA,WAPA,MAOA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,YAAA,MAAA,CAAA,SAAA;AACA;AACA,SAhBA;AAiBA,OAtBA,EAuBA,KAvBA,CAuBA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OA5BA;AA6BA,KAxIA;AA0IA;AACA,IAAA,YA3IA,wBA2IA,IA3IA,EA2IA;AACA,WAAA,UAAA,GAAA,IAAA;AACA;AA7IA;AApFA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row :gutter=1>\n      <el-col :span=\"4\">\n        <el-card class=\"aside_height\" shadow=\"never\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>评价设备</span>\n          </div>\n          <div style=\"overflow: auto; height: 84vh\">\n            <el-tree\n                highlight-current\n                :data=\"treedata\"\n                :props=\"defaultProps\"\n                @node-click=\"handleNodeClick\"\n            ></el-tree>\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-white>\n          <el-white class=\"button-group\">\n            <el-button\n                class=\"mb8\"\n                @click=\"addSensorButton\"\n                type=\"primary\"\n                icon=\"el-icon-plus\"\n                :disabled=\"addDisabled\"\n            >新增\n            </el-button>\n            <el-button\n                class=\"mb8\"\n                @click=\"deleteSensorButton\"\n                type=\"danger\"\n                icon=\"el-icon-delete\"\n            >删除\n            </el-button>\n          </el-white>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"selectChange\"\n            height=\"77.2vh\"\n          />\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!-- 设备部件新增、修改、详情、弹出-->\n    <el-dialog\n        :title=\"title\"\n        v-dialogDrag\n        :visible.sync=\"isShowDetails\"\n        width=\"50%\"\n        @close=\"close\"\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row :gutter=\"25\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"评价设备类型：\" prop=\"sblx\">\n              <el-input\n                  placeholder=\"请输入评价设备类型：\"\n                  v-model=\"form.sblx\"\n                  :disabled=\"true\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"部件名称：\" prop=\"bjmc\">\n              <el-input\n                  placeholder=\"请输入部件名称：\"\n                  v-model=\"form.bjmc\"\n              />\n            </el-form-item>\n          </el-col>\n<!--          <el-col :span=\"12\">\n            <el-form-item label=\"权重：\" prop=\"qz\" v-if=\"form.sblxid&&form.sblxid.indexOf('bd')>-1\">\n              <el-input\n                  placeholder=\"请输入权重：\"\n                  v-model=\"form.qz\"\n                  :disabled=\"isDisabled\"\n                  clearable\n              />\n            </el-form-item>\n          </el-col>-->\n          <el-col :span=\"12\">\n            <el-form-item label=\"评价范围：\" prop=\"pjfw\">\n              <el-input\n                  placeholder=\"评价范围：\"\n                  v-model=\"form.pjfw\"\n                  :disabled=\"isDisabled\"\n                  clearable\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"24\" class=\"pull-left\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"评价内容：\" prop=\"pjnr\">\n              <el-input\n                  type=\"textarea\"\n                  placeholder=\"评价内容：\"\n                  v-model=\"form.pjnr\"\n                  :disabled=\"isDisabled\"\n                  clearable\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"close\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"commitAdd\">保 存</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  getPageDataListBJ, removeBJ, saveOrUpdateBJ,\n} from \"@/api/dagangOilfield/bzgl/sbztpjbzk/pjdzwh\";\nimport {getSblxAndSbbjTree} from \"@/api/dagangOilfield/bzgl/sbztpjbzk/ztlmxwh\";\n\nexport default {\n  name: \"pjdzwh\",\n  data() {\n    return {\n      treedata: [],\n      //树结构\n      defaultProps: {\n        children: \"children\",\n        label: \"label\",\n      },\n      form: {},\n      //查询评价部位和设备种类\n      querypjdzwhParam: {\n        pageNum: 1,\n        pageSize: 10,\n      },\n      //标题\n      title: \"\",\n      //设备部件删除选择列\n      selectRows: [],\n      //新增按钮控制\n      addDisabled: true,\n      //点击树节点赋值\n      treeForm: {},\n      //是否禁用\n      isDisabled: false,\n      //评价部件列表\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n        },\n        tableData: [],\n        tableHeader: [\n          {label: \"评价设备\", prop: \"sblx\", align: \"center\"},\n          {label: \"部件名称\", prop: \"bjmc\", align: \"center\"},\n          // {label: \"权重\", prop: \"qz\", align: \"center\"},\n          //   { label: \"设备种类\", prop: \"sbzlmc\", align: \"center\" },\n          {label: \"评价范围\", prop: \"pjfw\", align: \"center\"},\n          {label: \"评价内容\", prop: \"pjnr\", align: \"center\"},\n          {\n            prop: \"operation\",\n            label: \"操作\",\n            minWidth: \"100px\",\n            style: {display: \"block\"},\n            //操作列固定再右侧\n            fixed: \"right\",\n            operation: [\n              {name: \"修改\", clickFun: this.updateDetails},\n              {name: \"详情\", clickFun: this.getDetails},\n            ],\n          },\n        ],\n        option: {checkBox: true, serialNumber: true},\n      },\n\n      rules: {\n        bjmc: [\n          {required: true, message: \"部件名称不能为空\", trigger: \"blur\"},\n        ],\n        qz: [{required: true, message: \"权重不能为空\", trigger: \"blur\"}],\n        pjfw: [\n          {required: true, message: \"评价范围不能为空\", trigger: \"blur\"},\n        ],\n        pjnr: [\n          {required: true, message: \"评价内容不能为空\", trigger: \"blur\"},\n        ],\n      },\n      //是否显示弹框\n      isShowDetails: false,\n      //组织树\n      treeOptions: [],\n      //部件名称列表\n      bjmcList: [],\n    };\n  },\n  watch: {},\n  created() {\n  },\n  mounted() {\n    this.getTreeNode();\n  },\n  methods: {\n    //获取树节点数据\n    getTreeNode() {\n      getSblxAndSbbjTree().then((res) => {\n        if (res.code === \"0000\") {\n          this.treedata = res.data;\n        }\n      });\n    },\n    //获取导则部件列表\n    async getDataBJ(params) {\n      try {\n        const param = {...this.querypjdzwhParam, ...params};\n        const {data, code} = await getPageDataListBJ(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n        }\n      } catch (e) {\n\n      }\n    },\n    //树节点点击事件\n    handleNodeClick(data,node) {\n      this.addDisabled = true;\n      this.treeForm.sblx = \"\";\n      this.treeForm.sblxid = \"\";\n      this.querypjdzwhParam.objId = \"\";\n      this.querypjdzwhParam.sblxid = \"\";\n      if (data.nodeLevel === \"1\") {\n        this.treeForm.sblx = data.label;\n        this.treeForm.sblxid = data.id;\n        this.querypjdzwhParam.sblxid = data.id;\n        this.addDisabled = false;\n        this.getDataBJ();\n      }\n\n     /* if (data.nodeLevel === \"2\") {\n        this.treeForm.sblx = node.parent.data.label;\n        this.treeForm.sblxid = node.parent.data.id;\n        this.querypjdzwhParam.objId = data.id;\n        this.addDisabled = false;\n        this.getDataBJ();\n      }*/\n    },\n\n    //部件修改\n    updateDetails(row) {\n      this.isShowDetails = true;\n      this.form = row;\n      this.isDisabled = false;\n      this.title = \"修改\";\n    },\n    //部件详情\n    getDetails(row) {\n      this.title = \"详情\";\n      //打开弹窗\n      this.isShowDetails = true;\n      //把行数据给弹出框表单\n      this.form = {...row};\n      //将表单不可编辑\n      this.isDisabled = true;\n    },\n\n    //设备部件提交\n    commitAdd() {\n      this.$refs[\"form\"].validate((valid) => {\n        if (valid) {\n          saveOrUpdateBJ(this.form).then((res) => {\n            if (res.code === \"0000\") {\n              this.$message.success(res.msg);\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getDataBJ();\n              this.isShowDetails = false;\n              this.form = {};\n            } else {\n              this.$message.error(res.msg);\n            }\n          });\n        }\n      });\n    },\n\n    //设备部件关闭\n    close() {\n      this.isShowDetails = false;\n      this.form = {};\n    },\n\n    //增加\n    addSensorButton() {\n      this.form.sblx = this.treeForm.sblx;\n      this.form.sblxid = this.treeForm.sblxid;\n      this.isShowDetails = true;\n      this.isDisabled = false;\n      this.title = \"新增\";\n    },\n\n    //删除\n    deleteSensorButton() {\n      if (this.selectRows.length < 1) {\n        this.$message.warning(\"请选择正确的数据！！！\");\n        return;\n      }\n      let ids = this.selectRows.map((item) => {\n        return item.objId;\n      });\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      }).then(() => {\n            removeBJ(ids).then(({code}) => {\n              if (code === \"0000\") {\n                this.$message({\n                  type: \"success\",\n                  message: \"删除成功!\",\n                });\n                this.tableAndPageInfo.pager.pageResize = \"Y\";\n                this.getDataBJ();\n              } else {\n                this.$message({\n                  type: \"error\",\n                  message: \"删除失败!\",\n                });\n                this.tableAndPageInfo.pager.pageResize = \"Y\";\n                this.getDataBJ();\n              }\n            });\n          })\n          .catch(() => {\n            this.$message({\n              type: \"info\",\n              message: \"已取消删除\",\n            });\n          });\n    },\n\n    //获取行数据\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n  },\n};\n</script>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk"}]}