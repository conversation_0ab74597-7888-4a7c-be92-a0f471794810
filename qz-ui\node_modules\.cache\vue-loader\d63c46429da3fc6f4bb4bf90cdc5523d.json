{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\gfdzcz\\components\\fwzz_fgssh.vue?vue&type=template&id=ea6f8de6&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\gfdzcz\\components\\fwzz_fgssh.vue", "mtime": 1751373004029}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}