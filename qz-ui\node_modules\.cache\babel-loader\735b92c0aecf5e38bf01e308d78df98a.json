{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\jxbzk.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\jxbzk.vue", "mtime": 1706897322614}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["jxbzk.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAuFA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA;AACA,MAAA,WAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA;AADA,OADA,EAGA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,QAAA,EAAA,CAAA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,QAAA,EAAA,CAAA;AACA,YAAA,KAAA,EAAA;AADA,WAAA,EAGA;AACA,YAAA,KAAA,EAAA;AADA,WAHA,EAMA;AACA,YAAA,KAAA,EAAA;AADA,WANA,EASA;AACA,YAAA,KAAA,EAAA;AADA,WATA;AAFA,SAAA;AAFA,OAHA,CAFA;AAwBA;AACA,MAAA,cAAA,EAAA,IAzBA;AA0BA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,SAAA,EAAA;AAHA,OA3BA;AAgCA;AACA,MAAA,KAAA,EAAA;AACA,QAAA,SAAA,EAAA;AADA,OAjCA;AAqCA;AACA,MAAA,YAAA,EAAA,KAtCA;AAuCA;AACA,MAAA,cAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAIA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAJA;AAxCA,KAAA;AAoDA,GAvDA;AAwDA,EAAA,KAAA,EAAA,EAxDA;AA0DA,EAAA,OA1DA,qBA0DA;AACA,SAAA,OAAA;AAEA,GA7DA;AA8DA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,eAFA,2BAEA,IAFA,EAEA,CAEA,CAJA;AAKA;AACA,IAAA,eANA,6BAMA,CAEA,CARA;AASA;AACA,IAAA,kBAVA,gCAUA,CAEA,CAZA;AAaA;AACA,IAAA,kBAdA,gCAcA,CAEA,CAhBA;AAiBA;AACA,IAAA,YAlBA,0BAkBA,CAEA,CApBA;AAqBA;AACA,IAAA,OAtBA,qBAsBA,CAEA,CAxBA;AAyBA;AACA,IAAA,WA1BA,yBA0BA,CAEA,CA5BA;AA6BA;AACA,IAAA,UA9BA,wBA8BA;AACA,WAAA,SAAA,CAAA,WAAA;AACA;AAhCA;AA9DA,C", "sourcesContent": ["<template>\n  <el-container id=\"main_container_dj\">\n    <el-container>\n      <el-main>\n        <el-card class=\"box-card\" shadow=\"never\" ref=\"search\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>监测项与阀值</span>\n          </div>\n          <div>\n            <el-col :span=\"24\" :xs=\"24\">\n              <div class=\"search-condition\">\n                <el-row :gutter=\"10\" class=\"mb8 pull-right\">\n                  <el-col :span=\"1.5\">\n                    <el-button type=\"primary\"\n                               @click=\"isSearchShow = isSearchShow?false:true\"\n                    >筛选</el-button>\n                  </el-col>\n                  <el-col :span=\"1.5\">\n                    <el-button class=\"mb8\" @click=\"addSensorButton\"\n                               type=\"primary \"\n                    >添加\n                    </el-button>\n                  </el-col>\n                  <el-col :span=\"1.5\">\n                    <el-button  class=\"mb8\" @click=\"updateSensorButton\"\n                                type=\"warning \"\n                    >修改</el-button>\n                  </el-col>\n                  <el-col :span=\"1.5\">\n                    <el-button class=\"mb8\" @click=\"deleteSensorButton\"\n                               type=\"danger\"\n                               :disabled=\"multipleSensor\">删除\n                    </el-button>\n                  </el-col>\n                </el-row>\n              <div class=\"clearfix\" />\n            </div>\n            </el-col>\n          </div>\n          <div>\n            <el-form :model=\"queryParams\" ref=\"queryForm\" v-show=\"isSearchShow\"  class=\"searchForm\" :inline=\"true\" label-width=\"120px\">\n              <el-form-item label=\"监测项目：\" prop=\"gzpType\">\n                <el-select v-model=\"queryParams.gzpType\"  placeholder=\"请选择监测项目\" clearable >\n                  <el-option v-for=\"item in gzpTypeOptions\"\n                             :key=\"item.value\"\n                             :label=\"item.label\"\n                             :value=\"item.value\"></el-option>\n                </el-select>\n              </el-form-item>\n              <el-form-item label=\"监测项：\" prop=\"ancuoTerm\">\n                <el-input v-model=\"queryParams.ancuoTerm\" placeholder=\"请输入监测项\" clearable class=\"common-width\"\n                          @keyup.enter.native=\"handleQuery\"/>\n              </el-form-item>\n              <el-form-item>\n                <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\n                <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\n              </el-form-item>\n            </el-form>\n          </div>\n        </el-card>\n\n        <el-card class=\"box-card\" shadow=\"never\">\n          <el-table stripe border v-loading=\"loading\" :data=\"userList\" @selection-change=\"handleSelectionChange\">\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n            <el-table-column label=\"监测项目\" align=\"center\" prop=\"userId\"/>\n            <el-table-column label=\"监测项\" align=\"center\" prop=\"userName\" :show-overflow-tooltip=\"true\"/>\n            <el-table-column label=\"结果上下线\" align=\"center\" prop=\"userName\" :show-overflow-tooltip=\"true\"/>\n            <el-table-column label=\"逻辑符\" align=\"center\" prop=\"userName\" :show-overflow-tooltip=\"true\"/>\n          </el-table>\n\n          <pagination v-show=\"total>0\"\n                      :total=\"total\"\n                      :page.sync=\"queryParams.pageNum\"\n                      :limit.sync=\"queryParams.pageSize\"\n                      @pagination=\"getList\"/>\n        </el-card>\n        <el-footer>\n        </el-footer>\n      </el-main>\n    </el-container>\n\n  </el-container>\n\n</template>\n\n<script>\n\n  export default {\n    name: \"jcbzk\",\n    data() {\n      return {\n        //组织树\n        treeOptions:[\n          {\n          label: '断路器',\n        }, {\n          label: '变压器',\n          children: [{\n            label: '冷却系统',\n            children: [{\n              label: '温控运行情况',\n\n            }, {\n              label: '油箱',\n\n            }, {\n              label: '铁芯',\n\n            }, {\n              label: '绕组',\n\n            }]\n          }]\n        }],\n        //删除是否可用\n        multipleSensor: true,\n        //查询参数\n        queryParams:{\n          pageNum: 1,\n          pageSize: 10,\n          ancuoTerm:''\n        },\n        //填入数据校验\n        rules: {\n          ancuoTerm: [\n          ]\n        },\n        //表单开关\n        isSearchShow:false,\n        //工作票类型下拉菜单\n        gzpTypeOptions:[\n          {\n            value: 'type1',\n            label: '类型1'\n          }, {\n            value: 'type2',\n            label: '类型2'\n          }\n        ]\n\n\n      };\n    },\n    watch: {\n    },\n    created() {\n      this.getList();\n\n    },\n    methods: {\n      //树节点点击事件\n      handleNodeClick(data) {\n\n      },\n      //添加按钮\n      addSensorButton(){\n\n      },\n      //编辑按钮\n      updateSensorButton(){\n\n      },\n      //删除按钮\n      deleteSensorButton(){\n\n      },\n      //导出按钮\n      handleExport(){\n\n      },\n      //查询列表\n      getList(){\n\n      },\n      //搜索\n      handleQuery(){\n\n      },\n      //重置\n      resetQuery(){\n        this.resetForm(\"queryForm\");\n      }\n    }\n  };\n</script>\n\n<style lang='scss' scoped>\n.box-card{\n  margin-bottom: 15px;\n  .el-card__header{\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n.item{\n  width: 200px;height: 148px; float: left;\n}\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl"}]}