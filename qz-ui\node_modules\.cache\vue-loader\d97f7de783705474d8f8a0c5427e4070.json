{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\sbxhwh.vue?vue&type=style&index=0&id=09d35f90&scoped=true&lang=css&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\sbxhwh.vue", "mtime": 1706897322893}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmJ1dHRvbi1ncm91cCB7CiAgcGFkZGluZy1sZWZ0OiAzMHB4OwogIHBhZGRpbmctcmlnaHQ6IDMwcHg7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kOwp9CgovKuaOp+WItmlucHV06L6T5YWl5qGG6L655qGG5piv5ZCm5pi+56S6Ki8KLmVsSW5wdXQgPj4+IC5lbC1pbnB1dF9faW5uZXIgewogIGJvcmRlcjogMDsKfQoKLmJveC1jYXJkIHsKICBtYXJnaW4tYm90dG9tOiAydmggIWltcG9ydGFudDsKfQovZGVlcC8gLmVsLXNlbGVjdC1ncm91cF9fdGl0bGUgewogIGZvbnQtc2l6ZTogMjRweDsKfQo="}, {"version": 3, "sources": ["sbxhwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6aA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "sbxhwh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 120, itemWidth: 230 }\"\n      @handleReset=\"getReset\"\n    />\n\n    <el-white class=\"button-group1\">\n      <div class=\"button_btn\">\n        <el-button\n          type=\"primary\"\n          v-hasPermi=\"['bzsbxhk:button:add']\"\n          icon=\"el-icon-plus\"\n          @click=\"getInsert\"\n          >新增</el-button\n        >\n      </div>\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"selectChange\"\n        height=\"68vh\"\n        ref=\"sbxhk\"\n      >\n        <el-table-column\n          slot=\"table_eight\"\n          align=\"center\"\n          fixed=\"right\"\n          style=\"display: block\"\n          label=\"操作\"\n          width=\"160\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              @click=\"getUpdate(scope.row)\"\n              v-hasPermi=\"['bzsbxhk:button:update']\"\n              type=\"text\"\n              size=\"small\"\n              class=\"el-icon-edit\"\n              title=\"修改\"\n            ></el-button>\n            <el-button\n              @click=\"getDetails(scope.row)\"\n              type=\"text\"\n              size=\"small\"\n              title=\"详情\"\n              class=\"el-icon-view\"\n            ></el-button>\n            <el-button\n              @click=\"deleteRow\"\n              type=\"text\"\n              v-if=\"scope.row.createBy === $store.getters.name\"\n              icon=\"el-icon-delete\"\n            ></el-button>\n          </template>\n        </el-table-column>\n      </comp-table>\n    </el-white>\n\n    <!-- 详情/新增/修改 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"30%\"\n      @close=\"handleClose\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"130px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row class=\"box-card\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"设备分类：\" prop=\"dysblx\">\n              <el-select\n                placeholder=\"请选择设备分类\"\n                v-model=\"form.dysblx\"\n                :disabled=\"isDisabled\"\n                clearable\n                filterable\n                style=\"width: 100%\"\n              >\n                <el-option-group\n                  v-for=\"group in DevicesListGroup\"\n                  :key=\"group.label\"\n                  :label=\"group.label\"\n                >\n                  <el-option\n                    v-for=\"item in group.sbDataList\"\n                    :key=\"item.code\"\n                    :label=\"item.name\"\n                    :value=\"item.code\"\n                  >\n                  </el-option>\n                </el-option-group>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"设备型号：\" prop=\"sbxh\">\n              <el-input\n                placeholder=\"请添加设备型号\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.sbxh\"\n              >\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注：\" prop=\"bz\">\n              <el-input\n                placeholder=\"请填写备注\"\n                type=\"textarea\"\n                :rows=\"3\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.bz\"\n              >\n              </el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\" size=\"small\">取 消</el-button>\n        <el-button\n          v-if=\"title === '设备型号库增加' || title === '设备型号库修改'\"\n          type=\"primary\"\n          size=\"small\"\n          @click=\"saveRow\"\n          >确 认\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  getList,\n  remove,\n  saveOrUpdate\n} from \"@/api/dagangOilfield/bzgl/sbbzk/sbxhk\";\nimport { getDeviceClassGroup } from \"@/api/dagangOilfield/bzgl/sblxwh/sblxwh\";\n\nexport default {\n  name: \"index\",\n  data() {\n    return {\n      DevicesListGroup: [],\n      selectRows: [],\n      sbxhList: [{ label: \"\", value: \"\" }],\n      dysblxList: [{ label: \"\", value: \"\" }],\n      form: {\n        sbxh: \"\",\n        dysblx: \"\"\n      },\n      //详情弹框是否显示\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          sbxh: \"\",\n          sblxArr: []\n        }, //查询条件\n        fieldList: [\n          {\n            label: \"设备分类\",\n            value: \"sblxArr\",\n            type: \"selectGroupjxxmwh\",\n            clearable: true,\n            filterable: true,\n            multiple: true,\n            options: []\n          },\n          { label: \"设备型号\", type: \"input\", value: \"sbxh\" }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 120]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"对应设备类型\", prop: \"sblxmc\", minWidth: \"180\" },\n          { label: \"设备型号\", prop: \"sbxh\", minWidth: \"180\" },\n          { label: \"备注\", prop: \"bz\", minWidth: \"140\" }\n          /* {\n            prop: 'operation',\n            label: '操作',\n            minWidth: '130px',\n            style: { display: 'block' },\n            //操作列固定再右侧\n            fixed: 'right',\n            operation: [\n              { name: '修改', clickFun: this.getUpdate },\n              { name: '详情', clickFun: this.getDetails }\n            ]\n          }*/\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        sbxh: \"\",\n        sblxArr: []\n      },\n      showDeviceTree: false,\n      isFilter: false,\n      rules: {\n        dysblx: [\n          { required: true, message: \"设备分类不能为空\", trigger: \"change\" }\n        ],\n        sblxmc: [\n          { required: true, message: \"设备分类不能为空\", trigger: \"change\" }\n        ],\n        sbxh: [{ required: true, message: \"设备型号不能为空\", trigger: \"blur\" }]\n      }\n    };\n  },\n  created() {\n    \n  },\n  mounted() {\n    //列表查询\n    this.getData();\n    this.getDeviceClassGroup();\n  },\n  methods: {\n    getDeviceClassGroup() {\n      getDeviceClassGroup([\"bdsb\", \"pdsb\", \"sdsb\"]).then(res => {\n        if (res.code === \"0000\") {\n          this.DevicesListGroup = res.data;\n          this.filterInfo.fieldList.forEach(item => {\n            if (item.value === \"sblxArr\") {\n              item.options = res.data;\n              return;\n            }\n          });\n        } else {\n          this.$message.error(\"获取设备分类失败\");\n        }\n      });\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.$refs.sbxhk.loading = true;\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        const { data, code } = await getList(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          this.$nextTick(() => {\n            // 以服务的方式调用的 Loading 需要异步关闭\n            this.$refs.sbxhk.loading = false;\n          });\n        }\n      } catch (e) {\n        console.log(e);\n        this.$nextTick(() => {\n          // 以服务的方式调用的 Loading 需要异步关闭\n          this.$refs.sbxhk.loading = false;\n        });\n      }\n    },\n    //重置按钮\n    getReset() {\n    },\n    //选中行\n    handleSelectionChange() {},\n    //新增按钮\n    getInsert() {\n      this.title = \"设备型号库增加\";\n      this.isDisabled = false;\n      this.form = {};\n      this.isShowDetails = true;\n    },\n    //修改按钮\n    getUpdate(row) {\n      this.title = \"设备型号库修改\";\n      this.isDisabled = false;\n      this.form = { ...row };\n      this.isShowDetails = true;\n    },\n    //详情按钮\n    getDetails(row) {\n      this.title = \"设备型号库详情查看\";\n      this.form = { ...row };\n      this.isDisabled = true;\n      this.isShowDetails = true;\n    },\n    /*//附件查看\n    getFjInfoList() {\n      this.title = '附件查看'\n      this.isDisabled = true\n      this.isShowDetails = true\n      this.form={...row}\n    },*/\n    async saveRow() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          saveOrUpdate(this.form).then(res => {\n            try {\n              if (res.code === \"0000\") {\n                this.$message.success(\"操作成功\");\n              }\n            } catch (e) {\n              console.log(e);\n            }\n            this.getData();\n          });\n        } else {\n          return false;\n        }\n        this.isShowDetails = false;\n      });\n    },\n    //删除按钮\n    async deleteRow() {\n      if (this.selectRows.length < 1) {\n        this.$message.warning(\"请选择正确的数据！！！\");\n        return;\n      }\n      let ids = this.selectRows.map(item => {\n        return item.sbxhkId;\n      });\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove(ids).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n      this.getData();\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false;\n    },\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n    //显示设备类型树弹窗\n    showDeviceTreeDialog() {\n      this.isFilter = false;\n      this.showDeviceTree = true;\n    },\n    //鼠标聚焦事件\n    inputFocusEvent(val) {\n      if (val.target.name === \"dysblx\") {\n        this.showDeviceTree = true;\n        this.isFilter = true;\n      }\n    },\n    //获取设备分类数据\n    getDeviceTypeData(res) {\n      if (this.isFilter) {\n        this.filterInfo.data.sblxArr = [];\n        this.filterInfo.data.dysblx = \"\";\n        res.forEach(item => {\n          if (item.checked) {\n            this.filterInfo.data.sblxArr.push(item.code);\n            this.filterInfo.data.dysblx += item.name + \",\";\n          }\n        });\n\n        this.filterInfo.data.dysblx = this.filterInfo.data.dysblx.substring(\n          0,\n          this.filterInfo.data.dysblx.length - 1\n        );\n        this.showDeviceTree = false;\n      } else {\n        let treeNodes = [];\n        res.forEach(item => {\n          if (item.checked) {\n            treeNodes.push(item);\n          }\n        });\n        if (treeNodes.length === 1) {\n          this.form.sblxmc = treeNodes[0].name;\n          this.form.dysblx = treeNodes[0].code;\n          this.showDeviceTree = false;\n        } else {\n          this.$message.warning(\"请选择单条设备数据\");\n        }\n      }\n    },\n    closeDeviceTypeDialog() {\n      this.showDeviceTree = false;\n    },\n    //清空表单数据\n    handleClose() {\n      this.form = {};\n      this.$nextTick(() => {\n        this.form = this.$options.data().form;\n        this.resetForm(\"form\");\n      });\n    }\n  }\n};\n</script>\n\n<style scoped>\n.button-group {\n  padding-left: 30px;\n  padding-right: 30px;\n  display: flex;\n  justify-content: flex-end;\n}\n\n/*控制input输入框边框是否显示*/\n.elInput >>> .el-input__inner {\n  border: 0;\n}\n\n.box-card {\n  margin-bottom: 2vh !important;\n}\n/deep/ .el-select-group__title {\n  font-size: 24px;\n}\n</style>\n"]}]}