{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\dgTodoItem\\noticeTodo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\dgTodoItem\\noticeTodo.vue", "mtime": 1706897322042}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["noticeTodo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAsGA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;AACA,EAAA,IAAA,EAAA,kBADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA;AAFA,OADA;AAQA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,YAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,kBAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,CARA;AAaA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAbA,OARA;AAuBA,MAAA,UAAA,EAAA,EAvBA;AAwBA,MAAA,MAAA,EAAA;AACA,QAAA,IAAA,EAAA;AADA,OAxBA;AA2BA,MAAA,UAAA,EAAA,IA3BA;AA4BA,MAAA,UAAA,EAAA;AACA,QAAA,EAAA,EAAA,CADA;AAEA,QAAA,EAAA,EAAA;AAFA,OA5BA;AAgCA,MAAA,QAAA,EAAA,KAhCA;AAiCA,MAAA,QAAA,EAAA;AACA,QAAA,QAAA,EAAA;AADA,OAjCA;AAoCA,MAAA,UAAA,EAAA;AApCA,KAAA;AAsCA,GAzCA;AA0CA,EAAA,OA1CA,qBA0CA;AACA,SAAA,OAAA,CAAA,KAAA,MAAA,CAAA,KAAA;AACA,GA5CA;AA6CA,EAAA,OA7CA,qBA6CA,CACA;AACA,GA/CA;AAgDA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,cAFA,0BAEA,EAFA,EAEA;AACA,sCAAA,EAAA;AACA,KAJA;AAKA,IAAA,OALA,mBAKA,KALA,EAKA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,GADA,+DACA,KADA,GACA,KAAA,CAAA,MADA;AAEA,gBAAA,GAAA,CAAA,QAAA,GAAA,KAAA,CAAA,MAAA,CAAA,OAAA,CAAA,IAAA;AAFA;AAAA,uBAGA,+BAAA,GAAA,CAHA;;AAAA;AAAA;AAGA,gBAAA,IAHA,wBAGA,IAHA;AAGA,gBAAA,IAHA,wBAGA,IAHA;;AAIA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,KAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,KAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA,KAbA;AAcA,IAAA,YAdA,wBAcA,IAdA,EAcA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KAhBA;AAiBA,IAAA,WAjBA,uBAiBA,IAjBA,EAiBA,CAAA,CAjBA;AAmBA,IAAA,UAnBA,sBAmBA,GAnBA,EAmBA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,QAAA,mCAAA,GAAA;AACA,gBAAA,MAAA,CAAA,UAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,QAAA,GAAA,IAAA,CAHA,CAIA;;AAJA,sBAKA,GAAA,CAAA,MAAA,IAAA,CALA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAMA,kCAAA;AACA,kBAAA,QAAA,EAAA,GAAA,CAAA,EADA;AAEA,kBAAA,QAAA,EAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA;AAFA,iBAAA,CANA;;AAAA;AAAA;AAAA,uBAUA,MAAA,CAAA,OAAA,EAVA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,KA/BA;AAgCA,IAAA,SAhCA,uBAgCA;AACA,WAAA,QAAA,GAAA;AACA,QAAA,QAAA,EAAA;AADA,OAAA;AAGA,KApCA;AAqCA,IAAA,WArCA,uBAqCA,GArCA,EAqCA;AACA,WAAA,IAAA,GAAA,IAAA,KAAA,UAAA,EAAA;AACA,YAAA,GAAA,KAAA,GAAA,CAAA,IAAA,EAAA;AACA,eAAA,MAAA,CAAA,QAAA,GAAA,KAAA,UAAA,CAAA,GAAA,CAAA;AACA,eAAA,OAAA;AACA;AACA;AACA;AA5CA;AAhDA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-white>\n      <el-filter\n        ref=\"filter1\"\n        :data=\"filterInfo.data\"\n        :field-list=\"filterInfo.fieldList\"\n        :width=\"{ labelWidth: 180, itemWidth: 200 }\"\n        @handleReset=\"filterReset\"\n      ></el-filter>\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"selectChange\"\n        height=\"75vh\"\n      >\n        <el-table-column\n          slot=\"table_six\"\n          align=\"center\"\n          style=\"display: block;height: auto\"\n          label=\"状态\"\n          min-width=\"180\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-badge\n              :value=\"scope.row.isRead == 0 ? '未读' : '已读'\"\n              :type=\"scope.row.isRead == 0 ? 'danger' : 'primary'\"\n              class=\"item\"\n            >\n            </el-badge>\n          </template>\n        </el-table-column>\n        <el-table-column slot=\"table_seven\" label=\"附件\" prop=\"attachment\">\n          <template slot-scope=\"scope\">\n            <span v-if=\"scope.row.fileList.length > 0\">有附件</span>\n            <span v-else>无附件</span>\n          </template>\n        </el-table-column>\n        <el-table-column\n          slot=\"table_eight\"\n          align=\"center\"\n          fixed=\"right\"\n          style=\"display: block\"\n          label=\"操作\"\n          min-width=\"70\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" size=\"small\" @click=\"getDetails(scope.row)\"\n              >查看</el-button\n            >\n          </template>\n        </el-table-column>\n      </comp-table>\n    </el-white>\n    <el-dialog\n      title=\"通知详情\"\n      :visible.sync=\"openInfo\"\n      width=\"50%\"\n      append-to-body\n      :close-on-click-modal=\"false\"\n      v-dialogDrag\n    >\n      <el-form ref=\"formInfo\" :model=\"formInfo\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"标题：\" prop=\"title\">\n              <el-input v-model=\"formInfo.title\" :disabled=\"isDisabled\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"内容：\" prop=\"content\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"3\"\n                v-model=\"formInfo.content\"\n                :disabled=\"isDisabled\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-form-item\n            label=\"附件：\"\n            prop=\"attachment\"\n            v-if=\"formInfo.fileList.length > 0\"\n          >\n            <template slot-scope=\"scope\">\n              <span v-for=\"it in formInfo.fileList\">{{ it.fileOldName }}</span>\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"downloadHandle(formInfo.id)\"\n                >下载</el-button\n              >\n            </template>\n          </el-form-item>\n        </el-row>\n      </el-form>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getNoticePage, updateReadStatus } from \"@/api/activiti/DgTodoItem\";\nimport { downloadByBusinessId } from \"@/api/tool/file\";\nexport default {\n  name: \"proclamationTodo\",\n  data() {\n    return {\n      filterInfo: {\n        data: {},\n        fieldList: [\n          { label: \"通知标题\", type: \"input\", value: \"title\" },\n          { label: \"发送人\", type: \"input\", value: \"senderName\" }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"title\", label: \"通知标题\", minWidth: \"140\" },\n          { prop: \"senderName\", label: \"发送人\", minWidth: \"120\" },\n          { prop: \"publishStartTime\", label: \"发送时间\", minWidth: \"120\" }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      selectRows: [],\n      params: {\n        type: 1\n      },\n      activeName: \"db\",\n      tabRefresh: {\n        db: 0,\n        yb: 1\n      },\n      openInfo: false,\n      formInfo: {\n        fileList: []\n      },\n      isDisabled: false\n    };\n  },\n  created() {\n    this.getData(this.$route.query);\n  },\n  mounted() {\n    //this.getData(this.$route.query)\n  },\n  methods: {\n    /**下载附件*/\n    downloadHandle(id) {\n      downloadByBusinessId(id);\n    },\n    async getData(param) {\n      let par = { ...param, ...this.params };\n      par.userName = this.$store.getters.name;\n      let { code, data } = await getNoticePage(par);\n      if (code === \"0000\") {\n        this.tableAndPageInfo.tableData = data.records;\n        this.tableAndPageInfo.pager.total = data.total;\n      }\n    },\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n    filterReset(data) {},\n\n    async getDetails(row) {\n      this.formInfo = { ...row };\n      this.isDisabled = true;\n      this.openInfo = true;\n      //如果是未查看状态，点击查看时变成已查看\n      if (row.isRead == 0) {\n        await updateReadStatus({\n          noticeId: row.id,\n          userName: this.$store.getters.name\n        });\n        await this.getData();\n      }\n    },\n    closeForm() {\n      this.formInfo = {\n        fileList: []\n      };\n    },\n    handleClick(tab) {\n      for (let key in this.tabRefresh) {\n        if (key === tab.name) {\n          this.params.isHandle = this.tabRefresh[key];\n          this.getData();\n        }\n      }\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n/*  .el-badge {\n\n  /deep/.el-badge__content\n  {\n    !*&.is-fixed {\n      position: absolute;\n      right: 10px;\n      top: 0;\n      transform: translateX(100%) translateY(-50%);\n    }*!\n    margin-top: 0.45vh;\n    font-size: 10px;\n  }\n\n  }*/\n.item {\n  width: 8.5rem;\n  height: 1.25rem;\n}\n</style>\n"], "sourceRoot": "src/views/activiti/dgTodoItem"}]}