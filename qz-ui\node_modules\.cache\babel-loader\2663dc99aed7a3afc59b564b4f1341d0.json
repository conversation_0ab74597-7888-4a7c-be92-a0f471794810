{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\bddzcz\\dzczp.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\bddzcz\\dzczp.vue", "mtime": 1749014635777}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["dzczp.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAspBA;;AACA;;AACA;;AACA;;AAWA;;AAEA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAPA;eASA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,oBAAA,EAAA,6BAAA;AAAA,IAAA,QAAA,EAAA,qBAAA;AAAA,IAAA,QAAA,EAAA,iBAAA;AAAA,IAAA,aAAA,EAAA;AAAA,GAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA,MAAA,OAAA,EAAA,KADA;AAEA,MAAA,YAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,YAFA;AAGA,MAAA,OAAA,EAAA,EAHA;AAIA;AACA,MAAA,aAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,EAaA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAbA,EAiBA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAjBA,EAoBA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OApBA,CALA;AA2BA,MAAA,cAAA,EAAA,KA3BA;AA4BA,MAAA,UAAA,EAAA,EA5BA;AA6BA,MAAA,YAAA,EAAA,IA7BA;AA8BA;AACA,MAAA,wBAAA,EAAA,EA/BA;AAgCA,MAAA,GAAA,EAAA,EAhCA;AAiCA,MAAA,WAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,IAjCA;AAkCA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,oBAAA,EAAA,OADA;AAEA,QAAA,WAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,OAHA;AAIA,QAAA,SAAA,EAAA,EAJA;AAKA,QAAA,WAAA,EAAA,IALA;AAMA,QAAA,QAAA,EAAA,EANA;AAOA,QAAA,WAAA,EAAA;AAPA,OAnCA;AA4CA;AACA,MAAA,SAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,CA7CA;AAiDA;AACA,MAAA,MAAA,EAAA,KAlDA;AAmDA;AACA,MAAA,cAAA,EAAA,KApDA;AAqDA,MAAA,MAAA,EAAA,EArDA;AAqDA;AACA,MAAA,QAAA,EAAA,EAtDA;AAuDA,MAAA,YAAA,EAAA,KAvDA;AAwDA;AACA,MAAA,cAAA,EAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAzDA;AA0DA,MAAA,OAAA,EAAA,EA1DA;AA2DA,MAAA,QAAA,EAAA,KA3DA;AA4DA,MAAA,eAAA,EAAA,KA5DA;AA6DA,MAAA,EAAA,EAAA,KA7DA;AA8DA;AACA,MAAA,cAAA,EAAA,EA/DA;AAgEA;AACA,MAAA,gBAAA,EAAA,KAjEA;AAkEA,MAAA,OAAA,EAAA,EAlEA;AAmEA,MAAA,eAAA,EAAA,IAnEA;AAoEA;AACA,MAAA,GAAA,EAAA,EArEA;AAsEA,MAAA,MAAA,EAAA,IAtEA;AAuEA,MAAA,QAAA,EAAA,IAvEA;AAwEA,MAAA,UAAA,EAAA,EAxEA;AAyEA;AACA,MAAA,aAAA,EAAA;AACA,QAAA,GAAA,EAAA,IADA;AACA;AACA,QAAA,QAAA,EAAA;AAFA,OA1EA;AA8EA,MAAA,KAAA,EAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAA,GAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAPA;AAQA,QAAA,KAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CARA;AAWA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAXA;AAcA;AACA;AACA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAjBA;AAoBA,QAAA,OAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,WAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CApBA;AAuBA,QAAA,OAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,WAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAvBA,OA9EA;AAyGA;AACA,MAAA,IAAA,EAAA;AACA,QAAA,EAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA,EAJA;AAKA,QAAA,IAAA,EAAA,EALA;AAMA,QAAA,GAAA,EAAA,EANA;AAOA,QAAA,GAAA,EAAA,EAPA;AAQA,QAAA,GAAA,EAAA,EARA;AASA;AACA,QAAA,MAAA,EAAA,EAVA;AAWA,QAAA,EAAA,EAAA,CAXA;AAWA;AACA,QAAA,QAAA,EAAA;AAZA,OA1GA;AAwHA;AACA,MAAA,aAAA,EAAA;AACA,QAAA,UAAA,EAAA,EADA,CACA;;AADA,OAzHA;AA4HA;AACA,MAAA,MAAA,EAAA,EA7HA;AA8HA;AACA,MAAA,OAAA,EAAA,EA/HA;AAgIA;AACA,MAAA,aAAA,EAAA,KAjIA;AAkIA;AACA,MAAA,UAAA,EAAA,KAnIA;AAoIA,MAAA,UAAA,EAAA,KApIA;AAqIA;AACA,MAAA,KAAA,EAAA,EAtIA;AAuIA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,EAAA,EAAA,EADA;AAEA,UAAA,KAAA,EAAA,EAFA;AAGA,UAAA,OAAA,EAAA,EAHA;AAIA,UAAA,OAAA,EAAA,EAJA;AAKA;AACA,UAAA,IAAA,EAAA,EANA;AAOA,UAAA,GAAA,EAAA,EAPA;AAQA,UAAA,GAAA,EAAA,EARA;AASA,UAAA,KAAA,EAAA,EATA;AAUA,UAAA,MAAA,EAAA,EAVA,CAWA;;AAXA,SADA;AAaA;AACA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SADA,EAGA;AACA,UAAA,KAAA,EAAA,KADA;AAEA,UAAA,KAAA,EAAA,KAFA;AAGA,UAAA,IAAA,EAAA,QAHA;AAIA,UAAA,OAAA,EAAA,EAJA;AAKA,UAAA,SAAA,EAAA;AALA,SAHA,EAUA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,KAAA,EAAA,OAFA;AAGA,UAAA,IAAA,EAAA,QAHA;AAIA,UAAA,SAAA,EAAA,IAJA;AAKA,UAAA,OAAA,EAAA;AALA,SAVA,EAiBA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SAjBA,EAkBA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SAlBA,EAmBA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SAnBA,EAoBA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SApBA,EAqBA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,KAAA,EAAA,QAFA;AAGA,UAAA,IAAA,EAAA,UAHA;AAIA,UAAA,aAAA,EAAA,EAJA;AAKA,UAAA,SAAA,EAAA,IALA;AAMA,UAAA,OAAA,EAAA,CACA;AACA,YAAA,KAAA,EAAA,GADA;AAEA,YAAA,KAAA,EAAA;AAFA,WADA,EAKA;AACA,YAAA,KAAA,EAAA,GADA;AAEA,YAAA,KAAA,EAAA;AAFA,WALA,EASA;AACA,YAAA,KAAA,EAAA,GADA;AAEA,YAAA,KAAA,EAAA;AAFA,WATA,EAaA;AACA,YAAA,KAAA,EAAA,GADA;AAEA,YAAA,KAAA,EAAA;AAFA,WAbA,EAgBA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAhBA;AANA,SArBA,CA8CA;AA9CA;AAdA,OAvIA;AAsMA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SARA,EASA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SATA,EAUA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAVA,EAWA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,SAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAXA,CARA;AAqBA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AArBA,OAtMA;AA6NA,MAAA,MAAA,EAAA;AACA;AACA,QAAA,EAAA,EAAA,CAFA;AAGA;AACA,QAAA,IAAA,EAAA;AAJA,OA7NA;AAmOA,MAAA,OAAA,EAAA,EAnOA;AAoOA,MAAA,OAAA,EAAA;AApOA,KAAA;AAsOA,GA1OA;AA2OA,EAAA,OA3OA,qBA2OA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,cAAA,KAAA,CAAA,aAAA,GADA,CAEA;;;AACA,cAAA,KAAA,CAAA,gBAAA,GAHA,CAIA;;;AACA,cAAA,KAAA,CAAA,MAAA,CAAA,KAAA,GAAA,qBAAA;AALA;AAAA,qBAMA,KAAA,CAAA,aAAA,CAAA,EAAA,EAAA,EAAA,CANA;;AAAA;AAMA,cAAA,KAAA,CAAA,OANA;AAAA;AAAA,qBAOA,KAAA,CAAA,aAAA,CAAA,EAAA,EAAA,EAAA,CAPA;;AAAA;AAOA,cAAA,KAAA,CAAA,OAPA;AAAA;AAAA,qBASA,KAAA,CAAA,OAAA,CAAA,KAAA,CAAA,MAAA,CAAA,KAAA,CATA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,GArPA;AAsPA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,UAFA,sBAEA,KAFA,EAEA;AAAA;;AACA,WAAA,QAAA,CAAA,0BAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAKA,IALA,qFAKA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,yBAAA;AAAA,kBAAA,MAAA,EAAA,CAAA;AAAA,kBAAA,KAAA,EAAA;AAAA,iBAAA,CADA;;AAAA;AAAA;AACA,gBAAA,IADA,qBACA,IADA;;AAEA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,QAAA;;AACA,kBAAA,MAAA,CAAA,OAAA;AACA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OALA,IAYA,KAZA,CAYA,YAAA,CAAA,CAZA;AAaA,KAhBA;AAiBA,IAAA,WAjBA,yBAiBA;AACA,UAAA,SAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA;AAAA,kBAAA,IAAA,CAAA;AAAA,SAAA;AAAA,OAAA,CAAA;AACA,gCAAA,SAAA,EAAA,WAAA;AACA,KApBA;AAqBA,IAAA,WArBA,uBAqBA,IArBA,EAqBA,QArBA,EAqBA;AAAA;;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,IAAA;;AACA,UAAA,CAAA,QAAA,CAAA,QAAA,CAAA,MAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,UAAA;AACA,aAAA,KAAA,CAAA,MAAA,CAAA,UAAA;AACA;AACA;;AACA,kCAAA,IAAA,EACA,IADA,CACA,UAAA,IAAA,EAAA;AACA,QAAA,MAAA,CAAA,GAAA,GAAA,MAAA,CAAA,aAAA,CAAA,QAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,iBAAA,IAAA,CAAA,KAAA;AAAA,SAAA,CAAA;AACA,QAAA,MAAA,CAAA,aAAA,CAAA,QAAA,GAAA,IAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,iBAAA;AAAA,YAAA,EAAA,EAAA,IAAA,CAAA,UAAA;AAAA,YAAA,IAAA,EAAA,IAAA,CAAA,MAAA;AAAA,WAAA;AAAA,SAAA,CAAA;AACA,OAJA,EAKA,KALA,CAKA,UAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,KAAA;AACA,OAPA;AAQA,WAAA,KAAA,CAAA,MAAA,CAAA,UAAA;AACA,KArCA;;AAsCA;;;AAGA,IAAA,aAzCA,2BAyCA;AAAA;;AACA,+BAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAAA,QAAA,EAAA;AACA,SAFA;AAGA,QAAA,MAAA,CAAA,wBAAA,GAAA,GAAA,CAAA,IAAA;;AACA,QAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,IAAA,KAAA,EAAA;AACA,mBAAA,IAAA,CAAA,OAAA,GAAA,MAAA,CAAA,wBAAA;AACA;AACA,SAJA;AAKA,OAVA;AAWA,KArDA;AAsDA,IAAA,WAtDA,uBAsDA,GAtDA,EAsDA;AACA,WAAA,MAAA,GAAA;AACA;AACA,QAAA,EAAA,EAAA,CAFA;AAGA;AACA,QAAA,IAAA,EAAA;AAJA,OAAA,EAMA,KAAA,UAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,KAAA,UAAA,EAAA;AACA,UAAA,IAAA,CAAA,aAAA,GAAA,EAAA;AACA;AACA,OAJA,CANA;AAWA,KAlEA;AAmEA,IAAA,OAnEA,qBAmEA;AACA,WAAA,cAAA,GAAA,KAAA;;AACA,cAAA,KAAA,IAAA,CAAA,MAAA;AACA,aAAA,GAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,WAAA,KAAA,KAAA,IAAA,CAAA,QAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA;;AACA;;AACA,aAAA,GAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,IAAA,CAAA,KAAA,KAAA,KAAA,WAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA;;AACA;;AACA,aAAA,GAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,IAAA,CAAA,MAAA,KAAA,KAAA,WAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA;;AACA;;AACA,aAAA,GAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,IAAA,CAAA,GAAA,KAAA,KAAA,WAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA,iBAAA,YAAA,GAAA,KAAA;AACA;;AACA;AAzBA;AA2BA,KAhGA;AAkGA,IAAA,aAlGA,yBAkGA,aAlGA,EAkGA,MAlGA,EAkGA;AACA,aAAA,yBAAA;AACA,QAAA,aAAA,EAAA,aADA;AAEA,QAAA,MAAA,EAAA,MAFA;AAGA,QAAA,QAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,UAAA,GAAA,EAAA;AACA,eAAA,GAAA,CAAA,IAAA;AACA,OANA,CAAA;AAOA,KA1GA;AA2GA;AACA,IAAA,WA5GA,uBA4GA,GA5GA,EA4GA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,UAFA,mCAEA,GAFA;AAAA;AAAA,uBAGA,0BAAA,UAAA,EAAA,UAAA,CAHA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAKA,gBAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,OAAA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,KAnHA;AAoHA;AACA,IAAA,MArHA,kBAqHA,CArHA,EAqHA;AACA,WAAA,YAAA;AACA,KAvHA;AAwHA;AACA,IAAA,aAzHA,2BAyHA;AACA,UAAA,KAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,EAAA;AACA,aAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,CAAA,WAAA;AACA;;AACA,WAAA,MAAA,GAAA,KAAA;AACA,KA9HA;AA+HA;AACA,IAAA,aAhIA,2BAgIA;AACA,WAAA,YAAA,GAAA,KAAA;AACA,KAlIA;AAmIA,IAAA,YAnIA,wBAmIA,GAnIA,EAmIA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AADA;AAAA,uBAEA,8BAAA,MAAA,CAAA,WAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,sBAEA,IAFA;AAEA,gBAAA,IAFA,sBAEA,IAFA;AAGA,gBAAA,MAAA,CAAA,QAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,YAAA,GAAA,IAAA;;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,KAxIA;AAyIA;AACA,IAAA,cA1IA,0BA0IA,GA1IA,EA0IA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,WAAA,MAAA,GACA,gFACA,GAAA,CAAA,KADA,GAEA,KAFA,GAGA,IAAA,IAAA,GAAA,OAAA,EAJA;AAKA,KAjJA;AAkJA;AACA,IAAA,UAnJA,sBAmJA,IAnJA,EAmJA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,GADA,GACA;AACA,kBAAA,KAAA,EAAA,IAAA,CAAA,WADA;AAEA,kBAAA,OAAA,EAAA,CAFA;AAGA,kBAAA,MAAA,EAAA,IAAA,CAAA,WAAA,KAAA,UAAA,GAAA,CAAA,GAAA;AAHA,iBADA;;AAAA,sBAMA,IAAA,CAAA,WAAA,KAAA,UANA;AAAA;AAAA;AAAA;;AAAA,+BAOA,IAAA,CAAA,cAPA;AAAA,kDAQA,OARA,wBAWA,MAXA;AAAA;;AAAA;AASA,gBAAA,GAAA,CAAA,MAAA,GAAA,GAAA;AATA;;AAAA;AAYA,gBAAA,GAAA,CAAA,MAAA,GAAA,GAAA;AAZA;;AAAA;AAAA;AAAA;;AAAA;AAAA,+BAgBA,IAAA,CAAA,cAhBA;AAAA,kDAiBA,MAjBA,yBAqBA,OArBA,yBAyBA,OAzBA,yBA6BA,IA7BA;AAAA;;AAAA;AAkBA,gBAAA,GAAA,CAAA,MAAA,GAAA,GAAA;AACA,gBAAA,GAAA,CAAA,KAAA,GAAA,IAAA,CAAA,QAAA;AAnBA;;AAAA;AAsBA,gBAAA,GAAA,CAAA,MAAA,GAAA,GAAA;AACA,gBAAA,GAAA,CAAA,MAAA,GAAA,IAAA,CAAA,QAAA;AAvBA;;AAAA;AA0BA,gBAAA,GAAA,CAAA,MAAA,GAAA,GAAA;AACA,gBAAA,GAAA,CAAA,GAAA,GAAA,IAAA,CAAA,QAAA;AA3BA;;AAAA;AA8BA,gBAAA,GAAA,CAAA,MAAA,GAAA,GAAA;AA9BA;;AAAA;AAAA;AAAA,uBAkCA,yBAAA,GAAA,CAlCA;;AAAA;AAAA;AAkCA,gBAAA,IAlCA,sBAkCA,IAlCA;;AAAA,sBAmCA,IAAA,KAAA,MAnCA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAoCA,MAAA,CAAA,OAAA,EApCA;;AAAA;AAAA;AAAA;;AAAA;AAsCA,gBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AAtCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAwCA,KA3LA;AA4LA;AACA,IAAA,SA7LA,qBA6LA,IA7LA,EA6LA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,GADA,mCACA,MAAA,CAAA,IADA;;AAAA,sBAEA,IAAA,KAAA,UAFA;AAAA;AAAA;AAAA;;AAAA,+BAGA,GAAA,CAAA,MAHA;AAAA,kDAIA,GAJA,wBAgBA,GAhBA,yBA+CA,GA/CA,yBA2DA,GA3DA;AAAA;;AAAA;AAKA,gBAAA,MAAA,CAAA,aAAA,GAAA,KAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,IAAA,GAAA,OAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,EAAA,GAAA,GAAA,CAAA,GAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,aAAA,GAAA,EAAA;AACA,gBAAA,MAAA,CAAA,MAAA,GAAA,IAAA;AAdA;;AAAA;AAiBA,gBAAA,MAAA,CAAA,aAAA,GAAA,KAAA;;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,cAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,GADA;AAEA,kBAAA,gBAAA,EAAA,GAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAKA,IALA,qFAKA;AAAA;AAAA;AAAA;AAAA;AACA,0BAAA,MAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,0BAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AACA,0BAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,0BAAA,MAAA,CAAA,WAAA,CAAA,EAAA,GAAA,GAAA,CAAA,GAAA;AACA,0BAAA,MAAA,CAAA,WAAA,CAAA,IAAA,GAAA,QAAA;AACA,0BAAA,MAAA,CAAA,WAAA,CAAA,SAAA,CAAA,KAAA,GAAA,IAAA;AACA,0BAAA,MAAA,CAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,0BAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,0BAAA,MAAA,CAAA,WAAA,CAAA,aAAA,GAAA,EAAA;AACA,0BAAA,MAAA,CAAA,MAAA,GAAA,IAAA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBALA,IAiBA,KAjBA,CAiBA,YAAA;AACA,kBAAA,MAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,kBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AACA,kBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,kBAAA,MAAA,CAAA,WAAA,CAAA,EAAA,GAAA,GAAA,CAAA,GAAA;AACA,kBAAA,MAAA,CAAA,WAAA,CAAA,IAAA,GAAA,KAAA;AACA,kBAAA,MAAA,CAAA,WAAA,CAAA,SAAA,CAAA,KAAA,GAAA,KAAA;AACA,kBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,kBAAA,MAAA,CAAA,WAAA,CAAA,aAAA,GAAA,EAAA;AACA,kBAAA,MAAA,CAAA,MAAA,GAAA,IAAA;AACA,iBA3BA;;AAlBA;;AAAA;AAgDA,gBAAA,MAAA,CAAA,aAAA,GAAA,KAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,aAAA,GAAA,EAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,EAAA,GAAA,GAAA,CAAA,GAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,IAAA,GAAA,KAAA;AACA,gBAAA,MAAA,CAAA,MAAA,GAAA,IAAA;AAzDA;;AAAA;AAAA;AAAA,uBA4DA,MAAA,CAAA,KAAA,CAAA,MAAA,EAAA,QAAA;AAAA,oGAAA,kBAAA,KAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iCACA,KADA;AAAA;AAAA;AAAA;;AAAA;AAGA,4BAAA,MAAA,CAAA,IAAA,CAAA,QAAA,GAAA,MAAA,CAAA,aAAA,CAAA,QAAA;AACA,4BAAA,MAAA,CAAA,IAAA,CAAA,SAAA,GAAA,MAAA,CAAA,GAAA;AACA,4BAAA,MAAA,CAAA,aAAA,CAAA,UAAA,GAAA,MAAA,CAAA,IAAA,CAAA,KAAA;;AACA,4BAAA,MAAA,CAAA,UAAA;;AANA;AAAA,mCAOA,2BAAA,MAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kCAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,gCAAA,MAAA,CAAA,aAAA,GAAA,KAAA;;AACA,gCAAA,MAAA,CAAA,OAAA;;AACA,gCAAA,MAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,gCAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AACA,gCAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gCAAA,MAAA,CAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,gCAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,KAAA;AACA,gCAAA,MAAA,CAAA,MAAA,GAAA,IAAA;AACA,+BATA,MASA;AACA,gCAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA;AACA;AACA,6BAbA,CAPA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAA;;AAAA;AAAA;AAAA;AAAA,oBA5DA;;AAAA;AAAA;AAAA;;AAAA;AAsFA,gBAAA,MAAA,CAAA,aAAA,GAAA,KAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,cAAA,CAAA,KAAA,GAAA,QAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,KAAA;AACA,gBAAA,MAAA,CAAA,MAAA,GAAA,IAAA;;AA5FA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA8FA,KA3RA;AA6RA;AACA,IAAA,UA9RA,wBA8RA;AAAA;;AACA,UAAA,MAAA,GAAA,EAAA,CADA,CACA;;AACA,UAAA,SAAA,GAAA,CAAA,KAAA,EAAA,KAAA,CAAA;AACA,UAAA,QAAA,GAAA,IAAA,QAAA,EAAA,CAHA,CAIA;;AACA,WAAA,OAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,GAAA,IAAA,SAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA;AACA;;AACA,QAAA,QAAA,CAAA,MAAA,CAAA,OAAA,EAAA,IAAA,CAAA,GAAA;AACA,OALA;AAMA,MAAA,QAAA,CAAA,MAAA,CAAA,YAAA,EAAA,KAAA,aAAA,CAAA,UAAA,EAXA,CAWA;;AACA,MAAA,QAAA,CAAA,MAAA,CAAA,QAAA,EAAA,MAAA,EAZA,CAYA;;AACA,MAAA,QAAA,CAAA,MAAA,CAAA,MAAA,EAAA,SAAA,EAbA,CAaA;;AACA,uBACA,WADA,CACA,2BADA,EACA,QADA,EACA,CADA,EAEA,IAFA,CAEA,UAAA,GAAA,EAAA;AACA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,EAAA,CAFA,CAGA;;AACA,QAAA,MAAA,CAAA,OAAA;AACA,OAPA,EAQA,KARA,CAQA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,SAAA;AACA,OAVA;AAWA,KAvTA;AAwTA;AACA,IAAA,YAzTA,wBAyTA,IAzTA,EAyTA,QAzTA,EAyTA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,KA3TA;AA4TA,IAAA,cA5TA,0BA4TA,KA5TA,EA4TA,IA5TA,EA4TA,QA5TA,EA4TA,CAAA,CA5TA;AA6TA;AACA,IAAA,YA9TA,wBA8TA,IA9TA,EA8TA,QA9TA,EA8TA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,KAhUA;AAiUA;AACA,IAAA,wBAlUA,oCAkUA,IAlUA,EAkUA;AACA,WAAA,cAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA;AACA,WAAA,gBAAA,GAAA,IAAA;AACA,KArUA;AAsUA,IAAA,OAtUA,qBAsUA;AACA,WAAA,gBAAA,GAAA,KAAA;AACA,KAxUA;AAyUA;AACA,IAAA,oBA1UA,gCA0UA,GA1UA,EA0UA;AACA,UAAA,GAAA,EAAA;AACA,aAAA,IAAA,CAAA,OAAA,GAAA,KAAA,IAAA,CAAA,IAAA;AACA,aAAA,IAAA,CAAA,OAAA,GAAA,CAAA;AACA,OAHA,MAGA;AACA,aAAA,IAAA,CAAA,OAAA,GAAA,KAAA,IAAA,CAAA,IAAA;AACA,aAAA,IAAA,CAAA,OAAA,GAAA,CAAA;AACA;;AACA,WAAA,aAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,IAAA,GAAA,GAAA;AACA,OAFA;AAGA,KArVA;AAsVA;AACA,IAAA,kBAvVA,8BAuVA,GAvVA,EAuVA;AACA,UAAA,GAAA,KAAA,KAAA,EAAA;AACA,aAAA,IAAA,CAAA,OAAA,GAAA,KAAA,IAAA,CAAA,IAAA;AACA,aAAA,IAAA,CAAA,OAAA,GAAA,CAAA;AACA,aAAA,aAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,IAAA,GAAA,IAAA;AACA,SAFA;AAGA,OANA,MAMA;AACA,aAAA,IAAA,CAAA,OAAA,GAAA,KAAA,IAAA,CAAA,IAAA;AACA,aAAA,IAAA,CAAA,OAAA,GAAA,CAAA;AACA,aAAA,aAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,IAAA,GAAA,KAAA;AACA,SAFA;AAGA;AACA,KArWA;AAsWA,IAAA,iBAtWA,6BAsWA,GAtWA,EAsWA;AACA,UAAA,GAAA,EAAA;AACA,UAAA,KAAA,IAAA,CAAA,OAAA;AACA,OAFA,MAEA;AACA,UAAA,KAAA,IAAA,CAAA,OAAA;AACA;;AACA,WAAA,IAAA,CAAA,OAAA,GAAA,KAAA,IAAA,CAAA,IAAA,GAAA,KAAA,IAAA,CAAA,OAAA;AACA,KA7WA;AA8WA;AACA,IAAA,cA/WA,4BA+WA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,WAAA,EAAA,GAAA,IAAA;AACA,KAlXA;AAmXA;AACA,IAAA,OApXA,mBAoXA,MApXA,EAoXA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,KAFA,+DAEA,OAAA,CAAA,MAFA,GAEA,MAFA;AAGA,gBAAA,OAAA,CAAA,MAAA,GAAA,KAAA;AACA,gBAAA,OAAA,CAAA,OAAA,GAAA,IAAA;;AACA,oBAAA,CAAA,KAAA,CAAA,MAAA,EAAA;AACA,kBAAA,KAAA,CAAA,MAAA,GAAA,WAAA;AACA;;AACA,gBAAA,KAAA,CAAA,OAAA,GAAA,CAAA;AAAA,kBAAA,IAAA,EAAA,YAAA;AAAA,kBAAA,GAAA,EAAA;AAAA,iBAAA,CAAA;AARA;AAAA,uBASA,yBAAA,KAAA,CATA;;AAAA;AAAA;AASA,gBAAA,IATA,qBASA,IATA;AASA,gBAAA,IATA,qBASA,IATA;;AAUA,oBAAA,IAAA,KAAA,MAAA,EAAA;AAAA,uEACA,IAAA,CAAA,OADA;;AAAA;AAAA;AAAA,0BACA,CADA;AAEA,sBAAA,CAAA,CAAA,KAAA,GAAA,OAAA,CAAA,UAAA,CAAA,CAAA,CAAA,GAAA,CAAA;;AACA,sBAAA,OAAA,CAAA,aAAA,CAAA,OAAA,CAAA,UAAA,OAAA,EAAA;AACA,4BAAA,CAAA,CAAA,MAAA,KAAA,OAAA,CAAA,KAAA,EAAA;AACA,0BAAA,CAAA,CAAA,QAAA,GAAA,OAAA,CAAA,KAAA;AACA;AACA,uBAJA;AAHA;;AACA,wEAAA;AAAA;AAOA;AARA;AAAA;AAAA;AAAA;AAAA;;AASA,kBAAA,OAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA,kBAAA,OAAA,CAAA,OAAA,GAAA,KAAA;AACA;;AAtBA;AAAA;;AAAA;AAAA;AAAA;AAwBA,gBAAA,OAAA,CAAA,GAAA;;AAxBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA0BA,KA9YA;;AA+YA;;;AAGA,IAAA,QAlZA,oBAkZA,GAlZA,EAkZA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAEA,2BAAA;AAAA,kBAAA,KAAA,EAAA,GAAA,CAAA;AAAA,iBAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,uBAEA,IAFA;AAEA,gBAAA,IAFA,uBAEA,IAFA;;AAGA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,aAAA,CAAA,QAAA,GAAA,IAAA;;AACA,kBAAA,OAAA,CAAA,aAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,CAAA,EAAA;AACA,oBAAA,CAAA,CAAA,IAAA,GAAA,qBAAA;AACA,mBAFA;AAGA;;AARA;AAAA;;AAAA;AAAA;AAAA;AAUA,gBAAA,OAAA,CAAA,GAAA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,KA9ZA;AAgaA;AACA,IAAA,SAjaA,qBAiaA,GAjaA,EAiaA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,OAAA,CAAA,QAAA,CAAA,GAAA,CADA;;AAAA;AAEA,gBAAA,OAAA,CAAA,KAAA,GAAA,WAAA;AACA,gBAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,gBAAA,OAAA,CAAA,YAAA,GAAA,KAAA;AACA,gBAAA,OAAA,CAAA,IAAA,mCAAA,GAAA,EALA,CAMA;;AACA,gBAAA,OAAA,CAAA,OAAA,GAAA,OAAA,CAAA,IAAA,CAAA,OAAA;AAPA;AAAA,uBAQA,OAAA,CAAA,aAAA,CAAA,EAAA,EAAA,EAAA,CARA;;AAAA;AAQA,gBAAA,OAAA,CAAA,OARA;AASA,gBAAA,OAAA,CAAA,aAAA,GAAA,IAAA;;AACA,gBAAA,OAAA,CAAA,OAAA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KA5aA;AA6aA;AACA,IAAA,UA9aA,sBA8aA,GA9aA,EA8aA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,OAAA,CAAA,QAAA,CAAA,GAAA,CADA;;AAAA;AAEA,gBAAA,OAAA,CAAA,KAAA,GAAA,WAAA;AACA,gBAAA,OAAA,CAAA,IAAA,mCAAA,GAAA;AAHA;AAAA,uBAIA,OAAA,CAAA,aAAA,CAAA,EAAA,EAAA,EAAA,CAJA;;AAAA;AAIA,gBAAA,OAAA,CAAA,OAJA;AAKA;AACA,gBAAA,OAAA,CAAA,OAAA,GAAA,OAAA,CAAA,IAAA,CAAA,OAAA;AACA,gBAAA,OAAA,CAAA,UAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,YAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,aAAA,GAAA,IAAA;;AACA,gBAAA,OAAA,CAAA,OAAA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KAzbA;AA0bA;AACA,IAAA,OA3bA,qBA2bA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,sBAAA,KAAA,EAAA;AACA,wBAAA;AACA,sBAAA,OAAA,CAAA,IAAA,CAAA,QAAA,GAAA,OAAA,CAAA,aAAA,CAAA,QAAA;AACA,sBAAA,OAAA,CAAA,IAAA,CAAA,SAAA,GAAA,OAAA,CAAA,GAAA;AACA,iDAAA,OAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,4BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,0BAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA,yBAHA,CAIA;;;AACA,wBAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,wBAAA,OAAA,CAAA,OAAA;;AACA,wBAAA,OAAA,CAAA,aAAA,GAAA,KAAA;AACA,uBARA;AASA,qBAZA,CAYA,OAAA,CAAA,EAAA,CAAA;AACA,mBAdA,MAcA;AACA,oBAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;;AACA,2BAAA,KAAA;AACA;AACA,iBAnBA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBA,KAhdA;AAkdA;AACA,IAAA,SAndA,qBAmdA,GAndA,EAmdA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,KADA,GACA,EADA;;AAEA,oBAAA,GAAA,CAAA,KAAA,EAAA;AACA,kBAAA,KAAA,GAAA,GAAA,CAAA,KAAA;AACA,iBAFA,MAEA;AACA,kBAAA,KAAA,GAAA,OAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACA;;AACA,gBAAA,OAAA,CAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAKA,IALA,CAKA,YAAA;AACA,uCAAA,KAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,wBAAA,IAAA,SAAA,IAAA;;AACA,wBAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,OAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,SADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;;AAIA,sBAAA,OAAA,CAAA,OAAA;AACA,qBANA,MAMA;AACA,sBAAA,OAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,OADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;AAIA;AACA,mBAbA;AAcA,iBApBA,EAqBA,KArBA,CAqBA,YAAA;AACA,kBAAA,OAAA,CAAA,QAAA,CAAA;AACA,oBAAA,IAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;AAIA,iBA1BA;;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkCA,KArfA;AAsfA;AACA,IAAA,YAvfA,0BAufA;AACA,UAAA,GAAA,GAAA;AACA,QAAA,EAAA,EAAA,EADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA;AAJA,OAAA;AAMA,WAAA,aAAA,CAAA,QAAA,CAAA,IAAA,CAAA,GAAA;AACA,WAAA,aAAA,CAAA,GAAA,GAAA,GAAA;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,WAAA,IAAA,CAAA,OAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,KAlgBA;AAmgBA;AACA,IAAA,YApgBA,wBAogBA,GApgBA,EAogBA;AACA,UAAA,GAAA,CAAA,KAAA,EAAA;AACA,aAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA,KAAA;AACA;;AACA,WAAA,aAAA,CAAA,QAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA,CACA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,IAAA,KAAA,GAAA,CAAA,IAAA;AAAA,OADA,CAAA;AAGA,WAAA,IAAA,CAAA,IAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,WAAA,IAAA,CAAA,OAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,KA7gBA;AA+gBA;AACA,IAAA,KAhhBA,mBAghBA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,KAlhBA;AAmhBA,IAAA,YAnhBA,wBAmhBA,SAnhBA,EAmhBA;AACA,WAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,KAAA;AAAA,OAAA,CAAA;AACA,WAAA,MAAA,GAAA,SAAA,CAAA,MAAA,KAAA,CAAA;AACA,WAAA,QAAA,GAAA,CAAA,SAAA,CAAA,MAAA;AACA,WAAA,UAAA,GAAA,SAAA;AACA,KAxhBA;;AAyhBA;;;AAGA,IAAA,gBA5hBA,8BA4hBA;AAAA;;AACA,qCAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;;AACA,QAAA,OAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,IAAA,OAAA,EAAA;AACA,mBAAA,IAAA,CAAA,OAAA,GAAA,OAAA,CAAA,OAAA;AACA;AACA,SAJA;AAKA,OAPA;AAQA,KAriBA;AAsiBA;AACA,IAAA,UAviBA,sBAuiBA,GAviBA,EAuiBA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,UAFA,mCAEA,GAFA;AAAA;AAAA,uBAGA,yBAAA,UAAA,EAAA,UAAA,EAAA,eAAA,CAHA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAKA,gBAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,OAAA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,KA9iBA;AA+iBA;AACA,IAAA,SAhjBA,qBAgjBA,GAhjBA,EAgjBA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,UAFA,mCAEA,GAFA;AAAA;AAAA,uBAGA,wBAAA,UAAA,EAAA,UAAA,EAAA,cAAA,CAHA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAKA,gBAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,OAAA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,KAvjBA;AAwjBA;AACA,IAAA,WAzjBA,uBAyjBA,GAzjBA,EAyjBA,UAzjBA,EAyjBA;AAAA;;AACA,UAAA,GAAA,CAAA,KAAA,KAAA,KAAA,EAAA;AACA,uCAAA;AAAA,UAAA,MAAA,EAAA,GAAA,CAAA,KAAA,CAAA,QAAA;AAAA,SAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,gBAAA,IAAA,CAAA,KAAA,KAAA,OAAA,EAAA;AACA,cAAA,OAAA,CAAA,IAAA,CAAA,UAAA,EAAA,OAAA,EAAA,EAAA;;AACA,qBAAA,IAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;AACA;AACA,WALA;AAMA,SAPA;AAQA;AACA,KApkBA;AAqkBA,IAAA,UArkBA,sBAqkBA,IArkBA,EAqkBA;AACA,UAAA,4BAAA,GAAA,IAAA,CAAA,KAAA,CACA,IAAA,CAAA,SAAA,CAAA,KAAA,wBAAA,CADA,CAAA;AAGA,MAAA,4BAAA,CAAA,IAAA,CAAA;AACA,QAAA,KAAA,EAAA,SADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAAA;AAIA,MAAA,4BAAA,CAAA,IAAA,CAAA;AACA,QAAA,KAAA,EAAA,SADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAAA;;AAIA,UAAA,IAAA,EAAA;AACA,YAAA,MAAA,GAAA,4BAAA,CAAA,MAAA,CAAA,UAAA,CAAA;AAAA,iBAAA,CAAA,CAAA,KAAA,KAAA,IAAA;AAAA,SAAA,CAAA;;AACA,YAAA,MAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,iBAAA,MAAA,CAAA,CAAA,CAAA,CAAA,KAAA;AACA,SAFA,MAEA;AACA,iBAAA,EAAA;AACA;AACA,OAPA,MAOA;AACA,eAAA,EAAA;AACA;AACA;AA3lBA;AAtPA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n      @handleEvent=\"handleEvent\"\n      @handleReset=\"filterReset\"\n    />\n\n    <div>\n      <el-white class=\"button-group\">\n        <div class=\"button_btn\">\n          <el-button\n            type=\"danger\"\n            icon=\"el-icon-delete\"\n            v-if=\"hasSuperRole\"\n            @click=\"deleteRow\"\n            :disabled=\"single\"\n            >删除</el-button\n          >\n        </div>\n        <!-- <el-table-column\n          prop=\"statusCn\"\n          slot=\"table_start\"\n          align=\"center\"\n          style=\"display: block\"\n          label=\"流程状态\"\n          min-width=\"120\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-badge\n              v-if=\"scope.row.isBack === 1\"\n              value=\"退回\"\n              class=\"item\"\n              type=\"danger\"\n            >\n            </el-badge>\n            <span>{{ scope.row.statusCn }}</span>\n          </template>\n        </el-table-column> -->\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"selectChange\"\n          height=\"69vh\"\n          v-loading=\"loading\"\n        >\n          <el-table-column\n            slot=\"table_eight\"\n            align=\"center\"\n            fixed=\"right\"\n            style=\"display: block\"\n            label=\"操作\"\n            width=\"160\"\n            :resizable=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                @click=\"getDetails(scope.row)\"\n                type=\"text\"\n                size=\"small\"\n                title=\"详情\"\n                icon=\"el-icon-view\"\n              />\n              <el-button\n                @click=\"getUpdate(scope.row)\"\n                v-if=\"\n                  (scope.row.status === '0' &&\n                    scope.row.createBy === currentUser) ||\n                    hasSuperRole\n                \"\n                type=\"text\"\n                size=\"small\"\n                title=\"编辑\"\n                icon=\"el-icon-edit\"\n              />\n              <el-button\n                @click=\"deleteRow(scope.row)\"\n                v-if=\"\n                  scope.row.status === '0' && scope.row.createBy === currentUser\n                \"\n                type=\"text\"\n                size=\"small\"\n                title=\"删除\"\n                icon=\"el-icon-delete\"\n              />\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                @click=\"nullifyGzp(scope.row.objId)\"\n                v-if=\"scope.row.status < 4 && scope.row.status > 0 &&\n                ( scope.row.createBy === currentUser || (scope.row.bzspr && scope.row.bzspr === currentUser) \n                || (scope.row.fgsspr && scope.row.fgsspr === currentUser) \n                || (scope.row.bjr && scope.row.bjr === currentUser)\n                || hasSuperRole )\n                \"\n                title=\"作废\"\n                class=\"el-icon-close\"\n              >\n              </el-button>\n              <el-button\n                @click=\"showTimeLine(scope.row)\"\n                v-if=\"scope.row.isStart === 1\"\n                type=\"text\"\n                size=\"small\"\n                title=\"流程查看\"\n                icon=\"el-icon-lcck commonIcon\"\n              />\n              <el-button\n                @click=\"showProcessImg(scope.row)\"\n                v-if=\"scope.row.isStart === 1\"\n                type=\"text\"\n                size=\"small\"\n                title=\"流程图\"\n                icon=\"el-icon-lct commonIcon\"\n              />\n              <!--              <el-button @click=\"exportWord(scope.row)\" v-if=\"scope.row.status == '待办结'\" type=\"text\" size=\"small\">\n                              导出Word\n                            </el-button>-->\n              <el-button\n                @click=\"previewFile(scope.row)\"\n                v-if=\"scope.row.status > 2\"\n                type=\"text\"\n                size=\"small\"\n                title=\"预览\"\n                class=\"el-icon-zoom-in\"\n              />\n              <el-button\n                @click=\"exportPdf(scope.row)\"\n                v-if=\"scope.row.status > 2\"\n                type=\"text\"\n                size=\"small\"\n                title=\"导出pdf\"\n                icon=\"el-icon-pdf-export commonIcon\"\n              />\n            </template>\n          </el-table-column>\n        </comp-table>\n      </el-white>\n    </div>\n    <!-- 详情/新增/修改 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      v-if=\"isShowDetails\"\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <div>\n          <el-row>\n            <el-col :span=\"8\">\n              <el-form-item label=\"编号：\" prop=\"bm\">\n                <el-input\n                  v-model=\"form.bm\"\n                  disabled\n                  placeholder=\"确认后编号自动生成\"\n                >\n                </el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"分公司：\" prop=\"fgs\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.fgs\"\n                  disabled\n                  placeholder=\"请选择分公司\"\n                >\n                  <el-option\n                    v-for=\"item in organizationSelectedList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"变电站名称：\" prop=\"bdzmc\">\n                <el-select\n                  style=\"width: 100%\"\n                  filterable\n                  v-model=\"form.bdzmc\"\n                  disabled\n                  placeholder=\"请选择变电站\"\n                >\n                  <el-option\n                    v-for=\"item in bdzList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"下令人：\"\n                prop=\"xlr\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.xlr\"\n                  :disabled=\"isDisabled && isDisabledBj\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in xlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"审票人：\" prop=\"bzspr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.bzspr\"\n                  :disabled=\"isDisabledBj\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in sprList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"操作开始时间：\"\n                prop=\"kssj\"\n                label-width=\"140px\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-date-picker\n                  :disabled=\"isDisabledBj\"\n                  v-model=\"form.kssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  :placeholder=\"isDisabledBj ? '' : '选择日期时间'\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"操作结束时间：\"\n                prop=\"jssj\"\n                label-width=\"140px\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-date-picker\n                  :disabled=\"isDisabledBj\"\n                  v-model=\"form.jssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  :placeholder=\"isDisabledBj ? '' : '选择日期时间'\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"操作人：\"\n                prop=\"czr\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-input\n                  v-model=\"form.czr\"\n                  :disabled=\"isDisabledBj\"\n                  :placeholder=\"isDisabledBj ? '' : '请输入内容'\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"监护人：\"\n                prop=\"jhr\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.jhr\"\n                  :disabled=\"isDisabledBj\"\n                  :placeholder=\"isDisabledBj ? '' : '请选择'\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in jlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作项数：\" prop=\"czxs\">\n                <el-input-number\n                  v-model=\"form.czxs\"\n                  placeholder=\"请输入操作项数\"\n                  disabled\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"已执行项数：\" prop=\"yzxczxs\">\n                <el-input-number\n                  v-model=\"form.yzxczxs\"\n                  placeholder=\"请输入已执行操作项数\"\n                  disabled\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"未执行项数：\" prop=\"wzxczxs\">\n                <el-input-number\n                  v-model=\"form.wzxczxs\"\n                  placeholder=\"请输入未执行操作项数\"\n                  disabled\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"是否已执行：\"\n                prop=\"sfyzx\"\n                @change=\"handleChangeOfSfzx\"\n                :rules=\"\n                  form.status === '3'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-select\n                  v-model=\"form.sfyzx\"\n                  :placeholder=\"isDisabledBj ? '' : '请选择'\"\n                  :disabled=\"isDisabledBj\"\n                >\n                  <el-option\n                    v-for=\"item in sfyzxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item\n                label=\"未执行原因：\"\n                prop=\"wzxyy\"\n                v-if=\"form.sfyzx === '未执行'\"\n              >\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"form.wzxyy\"\n                  :disabled=\"isDisabledBj\"\n                  placeholder=\"请输入内容\"\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"操作任务：\" prop=\"czrw\">\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"form.czrw\"\n                  :disabled=\"isDisabledBj\"\n                  placeholder=\"请输入内容\"\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"上传图片：\">\n                <el-upload\n                  :disabled=\"isDisabledBj\"\n                  action=\"\"\n                  ref=\"uploadImg\"\n                  accept=\"image/jpeg,image/jpg,image/png\"\n                  :headers=\"header\"\n                  :multiple=\"true\"\n                  :on-change=\"handleChange\"\n                  :data=\"uploadImgData\"\n                  :file-list=\"imgList\"\n                  :auto-upload=\"false\"\n                  list-type=\"picture-card\"\n                  :on-preview=\"handlePictureCardPreview\"\n                  :on-progress=\"handleProgress\"\n                  :on-remove=\"handleRemove\"\n                >\n                  <i class=\"el-icon-plus\"></i>\n                </el-upload>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n\n        <!--预览，查看全部操作项-->\n        <div>\n          <div align=\"left\">\n            <el-upload\n              v-if=\"!isDisabled\"\n              action=\"\"\n              ref=\"upload\"\n              accept=\".xlsx\"\n              :limit=\"1\"\n              :auto-upload=\"false\"\n              :show-file-list=\"false\"\n              :on-change=\"importExcel\"\n            >\n              <el-button type=\"info\" @click.stop=\"handleYlChange\"\n                >预览</el-button\n              >\n              <el-button\n                type=\"success\"\n                icon=\"el-icon-download\"\n                @click.stop=\"exportExcel\"\n                >导出</el-button\n              >\n              <el-button type=\"success\" icon=\"el-icon-upload\">导入</el-button>\n              <div\n                slot=\"tip\"\n                class=\"el-upload__tip\"\n                style=\"width: 220px;color: red\"\n              >\n                先导出，再导入，只能上传当前页面导出的Excel文件\n              </div>\n            </el-upload>\n            <!-- <input type=\"file\" @change=\"importExcel\" v-if=\"(isDisabled && buttonNameShow) || hasSuperRole\"/> -->\n          </div>\n          <el-dialog :title=\"titleyl\" :visible.sync=\"yl\" append-to-body>\n            <!--第一个表格-->\n            <el-table\n              :data=\"propTableData.colFirst\"\n              ref=\"propTable\"\n              disabled\n              border\n              stripe\n              style=\"width: 100%\"\n              :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n            >\n              <el-table-column\n                align=\"center\"\n                width=\"120\"\n                prop=\"xh\"\n                label=\"顺序号\"\n                sortable\n              >\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"请输入顺序号\"\n                      disabled\n                      v-model=\"scope.row.xh\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"请输入操作项目\"\n                      disabled\n                      v-model=\"scope.row.czrw\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-dialog>\n        </div>\n\n        <!--列表-->\n        <div>\n          <div align=\"right\">\n            <el-checkbox @change=\"handleCheckAllChange\" :disabled=\"isDisabledBj\"\n              >全选</el-checkbox\n            >\n          </div>\n          <!--第一个表格-->\n          <el-table\n            :data=\"propTableData.colFirst\"\n            ref=\"propTable\"\n            :disabled=\"isDisabled\"\n            height=\"300\"\n            border\n            stripe\n            style=\"width: 100%\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <el-table-column\n              align=\"center\"\n              width=\"120\"\n              prop=\"xh\"\n              label=\"顺序号\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input-number\n                    size=\"small\"\n                    v-model=\"scope.row.xh\"\n                    :min=\"1\"\n                    :precision=\"0\"\n                    controls-position=\"right\"\n                    :disabled=\"isDisabled\"\n                  ></el-input-number>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入操作项目\"\n                    :disabled=\"isDisabled\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" width=\"80\" label=\"是否完成\">\n              <template slot-scope=\"scope\">\n                <el-checkbox\n                  v-model=\"scope.row.sfwc\"\n                  :disabled=\"isDisabledBj\"\n                  @change=\"handleCheckChange\"\n                ></el-checkbox>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" width=\"80\">\n              <template slot=\"header\" slot-scope=\"scope\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  :disabled=\"isDisabled\"\n                  icon=\"el-icon-plus\"\n                  @click=\"listFirstAdd(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n              <template slot-scope=\"scope\">\n                <el-button\n                  type=\"text\"\n                  icon=\"el-icon-delete\"\n                  :disabled=\"isDisabled\"\n                  @click=\"listFirstDel(scope.row)\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button\n          v-if=\"\n            isDisabled &&\n              buttonNameShow &&\n              form.status > 0 &&\n              form.status !== '3'\n          \"\n          type=\"info\"\n          @click=\"getSbFsBj('rollback')\"\n          >退 回\n        </el-button>\n        <el-button\n          v-if=\"isDisabled && buttonNameShow\"\n          type=\"success\"\n          @click=\"getSbFsBj('complete')\"\n          >{{ buttonName }}\n        </el-button>\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"saveRow\"\n          >保 存</el-button\n        >\n      </div>\n    </el-dialog>\n    <!--  工作流  -->\n    <activiti\n      ref=\"activiti\"\n      :processData=\"processData\"\n      :isShow=\"isShow\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n    ></activiti>\n    <!-- 流程详情 -->\n    <time-line\n      :value=\"timeLineShow\"\n      :timeData=\"timeData\"\n      @closeTimeLine=\"colseTimeLine\"\n    />\n    <!--流程图查看-->\n    <el-dialog\n      title=\"审批流程图\"\n      :visible.sync=\"openLoadingImg\"\n      width=\"56%\"\n      append-to-body\n      :close-on-click-modal=\"false\"\n    >\n      <div>\n        <img :src=\"imgSrc\" style=\"width: 100%\" />\n      </div>\n    </el-dialog>\n    <!--图片放大展示弹出框-->\n    <el-image-viewer\n      v-if=\"imgDialogVisible\"\n      :on-close=\"onClose\"\n      :url-list=\"dialogImageUrl\"\n      zIndex=\"3000\"\n    />\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport api from \"@/utils/request\";\nimport ElImageViewer from \"element-ui/packages/image/src/image-viewer\";\nimport {\n  exportPdf,\n  exportWord,\n  getBdzSelectList,\n  getCzpmxList,\n  getListLsp,\n  previewFile,\n  remove,\n  saveOrUpdate,\n  updateById\n} from \"@/api/yxgl/bdyxgl/bddzczp\";\nimport ElectronicAuthDialog from \"com/ElectronicAuthDialog\";\n//流程\nimport activiti from \"com/activiti_czp\";\nimport timeLine from \"com/timeLine\";\nimport { HistoryList } from \"@/api/activiti/processTask\";\nimport { getUsers } from \"@/api/system/usergroup\";\nimport { getUUID } from \"@/utils/ruoyi\";\nimport { getFgsOptions } from \"@/api/yxgl/bdyxgl/zbgl\";\nimport { exportToExcel, importFromExcel } from \"@/components/common/excel.js\";\n\nexport default {\n  name: \"dzczp\",\n  components: { ElectronicAuthDialog, activiti, timeLine, ElImageViewer },\n  data() {\n    return {\n      loading: false,\n      hasSuperRole: this.$store.getters.hasSuperRole,\n      jlrList: [],\n      //状态下拉框数据\n      statusOptions: [\n        {\n          value: \"0\",\n          label: \"操作票填报\"\n        },\n        {\n          value: \"1\",\n          label: \"班组审核\"\n        },\n        {\n          value: \"2\",\n          label: \"分公司审核\"\n        },\n        {\n          value: \"3\",\n          label: \"操作票办结\"\n        },\n        {\n          value: \"4\",\n          label: \"结束\"\n        },{ label: \"作废\", value: \"7\" }\n      ],\n      buttonNameShow: false,\n      buttonName: \"\",\n      isDisabledBj: true,\n      //组织结构下拉数据\n      organizationSelectedList: [],\n      bjr: \"\",\n      currentUser: this.$store.getters.name,\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"czpsh\",\n        businessKey: \"\",\n        businessType: \"倒闸操作票\",\n        variables: {},\n        defaultFrom: true,\n        nextUser: \"\",\n        processType: \"complete\"\n      },\n      // 是否已执行下拉框\n      sfyzxList: [\n        { label: \"已执行\", value: \"已执行\" },\n        { label: \"未执行\", value: \"未执行\" }\n      ],\n      //工作流弹窗\n      isShow: false,\n      //流程图查看\n      openLoadingImg: false,\n      imgSrc: \"\", //流程图查看地址\n      timeData: [],\n      timeLineShow: false,\n      //弹出框标题\n      activitiOption: { title: \"上报\" },\n      titleyl: \"\",\n      isShowSh: false,\n      isShShowDetails: false,\n      yl: false,\n      //图片地址url\n      dialogImageUrl: \"\",\n      //展示图片dialog控制\n      imgDialogVisible: false,\n      bdzList: [],\n      isIndeterminate: true,\n      // 多选框选中的id\n      ids: [],\n      single: true,\n      multiple: true,\n      selectData: [],\n      //弹出框中表格数据\n      propTableData: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      rules: {\n        // kssj: [\n        //   {required: true, message: '操作开始时间不能为空', trigger: 'blur'}\n        // ],\n        // jssj: [\n        //   {required: true, message: '操作结束时间不能为空', trigger: 'change'}\n        // ],\n        fgs: [{ required: true, message: \"分公司不能为空\", trigger: \"select\" }],\n        bdzmc: [\n          { required: true, message: \"变电站不能为空\", trigger: \"select\" }\n        ],\n        czrw: [\n          { required: true, message: \"操作任务不能为空\", trigger: \"blur\" }\n        ],\n        // xlr: [\n        //   {required: true, message: '下令人不能为空', trigger: 'blur'}\n        // ],\n        czxs: [\n          { required: true, message: \"操作项数不能为空\", trigger: \"blur\" }\n        ],\n        yzxczxs: [\n          { required: true, message: \"已执行项数不能为空\", trigger: \"blur\" }\n        ],\n        wzxczxs: [\n          { required: true, message: \"未执行项数不能为空\", trigger: \"blur\" }\n        ]\n      },\n      //form表单\n      form: {\n        bh: \"\",\n        bdzmc: \"\",\n        kssj: \"\",\n        jssj: \"\",\n        czrw: \"\",\n        czr: \"\",\n        jhr: \"\",\n        xlr: \"\",\n        // spr: '',\n        status: \"\",\n        lx: 2, //变电\n        colFirst: []\n      },\n      //上传图片时的携带的其他参数\n      uploadImgData: {\n        businessId: \"\" //携带的表单主键id\n      },\n      //上传图片时的请求头\n      header: {},\n      //图片list\n      imgList: [],\n      //详情弹框是否显示\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      checkedAll: false,\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          bm: \"\",\n          bdzmc: \"\",\n          czsjArr: [],\n          kssjArr: [],\n          // jssjArr: [],\n          czrw: \"\",\n          czr: \"\",\n          jhr: \"\",\n          xlrmc: \"\",\n          status: \"\"\n          // spr: ''\n        }, //查询条件\n        fieldList: [\n          { label: \"编号\", value: \"bm\", type: \"input\", clearable: true },\n\n          {\n            label: \"分公司\",\n            value: \"fgs\",\n            type: \"select\",\n            options: [],\n            clearable: true\n          },\n          {\n            label: \"变电站名称\",\n            value: \"bdzmc\",\n            type: \"select\",\n            clearable: true,\n            options: []\n          },\n          { label: \"操作任务\", value: \"czrw\", type: \"input\", clearable: true },\n          { label: \"操作人\", value: \"czr\", type: \"input\", clearable: true },\n          { label: \"监护人\", value: \"jhrmc\", type: \"input\", clearable: true },\n          { label: \"下令人\", value: \"xlrmc\", type: \"input\", clearable: true },\n          {\n            label: \"状态\",\n            value: \"status\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            clearable: true,\n            options: [\n              {\n                value: \"0\",\n                label: \"操作票填报\"\n              },\n              {\n                value: \"1\",\n                label: \"班组审核\"\n              },\n              {\n                value: \"2\",\n                label: \"分公司审核\"\n              },\n              {\n                value: \"3\",\n                label: \"操作票办结\"\n              },{ label: \"作废\", value: \"7\" }\n            ]\n          }\n          // {label: '审票人', value: 'spr', type: 'input', clearable: true}\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"编号\", prop: \"bm\", minWidth: \"80\" },\n          { label: \"状态\", prop: \"statusCn\", minWidth: \"80\" },\n          { label: \"分公司\", prop: \"fgsmc\", minWidth: \"100\" },\n          { label: \"变电站名称\", prop: \"bdzmcs\", minWidth: \"120\" },\n          { label: \"操作开始时间\", prop: \"kssj\", minWidth: \"110\" },\n          { label: \"操作结束时间\", prop: \"jssj\", minWidth: \"140\" },\n          { label: \"操作任务\", prop: \"czrw\", minWidth: \"160\" },\n          { label: \"操作人\", prop: \"czr\", minWidth: \"60\" },\n          { label: \"监护人\", prop: \"jhrmc\", minWidth: \"60\" },\n          { label: \"下令人\", prop: \"xlrmc\", minWidth: \"60\" },\n          { label: \"审票人\", prop: \"bzsprmc\", minWidth: \"60\" }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        //变电\n        lx: 2,\n        //用来区分历史票库，1-已办结，2-未办结\n        sfbj: 2\n      },\n      xlrList: [],\n      sprList: []\n    };\n  },\n  async mounted() {\n    this.getFgsOptions();\n    //获取变电站下拉框数据\n    this.getBdzSelectList();\n    //获取token\n    this.header.token = getToken();\n    this.xlrList = await this.getGroupUsers(61, \"\");\n    this.sprList = await this.getGroupUsers(13, \"\");\n\n    await this.getData(this.$route.query);\n  },\n  methods: {\n    //作废票\n    nullifyGzp(objId) {\n      this.$confirm(\"票作废后只能查看，不能进行任何操作，确认作废吗?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(async () => {\n          let { code } = await updateById({ status: 7, objId: objId });\n          if (code === \"0000\") {\n            this.$message.success(\"操作成功!!\");\n            this.getData();\n          }\n        })\n        .catch(() => {});\n    },\n    exportExcel() {\n      let excelData = this.propTableData.colFirst.map(item => ({ \"操作项目\": item.czrw}));\n      exportToExcel(excelData, \"操作项目.xlsx\");\n    },\n    importExcel(file, fileList) {\n      let fileName = file.name\n      if (!fileName.includes(\"操作项目\")) {\n        this.msgError(\"文件有误，请检查\")\n        this.$refs.upload.clearFiles()\n        return\n      }\n      importFromExcel(file)\n        .then(data => {\n          this.ids = this.propTableData.colFirst.map(item => item.objId)\n          this.propTableData.colFirst = data.map(item => ({xh: item.__rowNum__ , czrw: item[\"操作项目\"]}));\n        })\n        .catch(error => {\n          console.error(\"导入失败\", error);\n        });\n      this.$refs.upload.clearFiles()\n    },\n    /**\n     * 获取分公司下拉数据\n     */\n    getFgsOptions() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.organizationSelectedList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"fgs\") {\n            return (item.options = this.organizationSelectedList);\n          }\n        });\n      });\n    },\n    filterReset(val) {\n      (this.params = {\n        //变电\n        lx: 2,\n        //用来区分历史票库，1-已办结，2-未办结\n        sfbj: 2\n      }),\n        this.filterInfo.fieldList.forEach(item => {\n          if (item.type === \"checkbox\") {\n            item.checkboxValue = [];\n          }\n        });\n    },\n    getShow() {\n      this.buttonNameShow = false;\n      switch (this.form.status) {\n        case \"0\":\n          this.buttonName = \"上 报\";\n          if (this.currentUser === this.form.createBy) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case \"1\":\n          this.buttonName = \"提 交\";\n          if (this.form.bzspr === this.currentUser) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case \"2\":\n          this.buttonName = \"提 交\";\n          if (this.form.fgsspr === this.currentUser) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case \"3\":\n          this.buttonName = \"办 结\";\n          if (this.form.bjr === this.currentUser) {\n            this.buttonNameShow = true;\n            this.isDisabledBj = false;\n          }\n          break;\n      }\n    },\n\n    getGroupUsers(personGroupId, deptId) {\n      return getUsers({\n        personGroupId: personGroupId,\n        deptId: deptId,\n        deptName: \"\"\n      }).then(res => {\n        return res.data;\n      });\n    },\n    //预览文件\n    async previewFile(row) {\n      try {\n        let exportData = { ...row };\n        await previewFile(exportData, \"bdzdzczp\");\n      } catch (e) {\n        this.$message.error(\"预览失败！\");\n      }\n    },\n    //清空\n    change(e) {\n      this.$forceUpdate();\n    },\n    //关闭弹窗\n    closeActiviti() {\n      if (this.$refs.activiti.$refs.form) {\n        this.$refs.activiti.$refs.form.resetFields();\n      }\n      this.isShow = false;\n    },\n    //关闭流程查看页面\n    colseTimeLine() {\n      this.timeLineShow = false;\n    },\n    async showTimeLine(row) {\n      this.processData.businessKey = row.objId;\n      let { code, data } = await HistoryList(this.processData);\n      this.timeData = data;\n      this.timeLineShow = true;\n    },\n    //流程图片查看\n    showProcessImg(row) {\n      this.openLoadingImg = true;\n      this.imgSrc =\n        \"/activiti-api/process/read-resource?processDefinitionKey=czpsh&businessKey=\" +\n        row.objId +\n        \"&t=\" +\n        new Date().getTime();\n    },\n    //工作流回传数据\n    async todoResult(data) {\n      let row = {\n        objId: data.businessKey,\n        isStart: 1,\n        isBack: data.processType === \"rollback\" ? 1 : 0\n      };\n      if (data.processType === \"rollback\") {\n        switch (data.activeTaskName) {\n          case \"操作票填报\":\n            row.status = \"0\";\n            break;\n          case \"班组审核\":\n            row.status = \"1\";\n            break;\n        }\n      } else {\n        switch (data.activeTaskName) {\n          case \"班组审核\":\n            row.status = \"1\";\n            row.bzspr = data.nextUser;\n            break;\n          case \"分公司审核\":\n            row.status = \"2\";\n            row.fgsspr = data.nextUser;\n            break;\n          case \"操作票办结\":\n            row.status = \"3\";\n            row.bjr = data.nextUser;\n            break;\n          case \"结束\":\n            row.status = \"4\";\n            break;\n        }\n      }\n      let { code } = await updateById(row);\n      if (code === \"0000\") {\n        await this.getData();\n      } else {\n        this.$message.warning(\"操作失败\");\n      }\n    },\n    //上报发送办结\n    async getSbFsBj(type) {\n      let row = { ...this.form };\n      if (type === \"complete\") {\n        switch (row.status) {\n          case \"0\":\n            this.isShowDetails = false;\n            this.processData.variables.pass = true;\n            this.processData.businessKey = row.objId;\n            this.processData.processType = type;\n            this.activitiOption.title = \"提交\";\n            this.processData.defaultFrom = true;\n            this.processData.rylx = \"班组审核人\";\n            this.processData.dw = row.fgs;\n            this.processData.personGroupId = 13;\n            this.isShow = true;\n            break;\n          case \"1\":\n            this.isShowDetails = false;\n            this.$confirm(\"是否需要提交分公司审核?\", \"选择\", {\n              confirmButtonText: \"是\",\n              cancelButtonText: \"否\",\n              type: \"warning\"\n            })\n              .then(async () => {\n                this.processData.variables.pass = true;\n                this.processData.businessKey = row.objId;\n                this.processData.processType = type;\n                this.processData.dw = row.fgs;\n                this.processData.rylx = \"分公司审核人\";\n                this.processData.variables.isFgs = true;\n                this.activitiOption.title = \"提交\";\n                this.processData.defaultFrom = true;\n                this.processData.personGroupId = 14;\n                this.isShow = true;\n              })\n              .catch(() => {\n                this.processData.variables.pass = true;\n                this.processData.businessKey = row.objId;\n                this.processData.processType = type;\n                this.processData.dw = row.fgs;\n                this.processData.rylx = \"办结人\";\n                this.processData.variables.isFgs = false;\n                this.processData.defaultFrom = true;\n                this.processData.personGroupId = 15;\n                this.isShow = true;\n              });\n            break;\n          case \"2\":\n            this.isShowDetails = false;\n            this.processData.variables.pass = true;\n            this.processData.businessKey = row.objId;\n            this.processData.processType = type;\n            this.activitiOption.title = \"提交\";\n            this.processData.defaultFrom = true;\n            this.processData.personGroupId = 15;\n            this.processData.dw = row.fgs;\n            this.processData.rylx = \"办结人\";\n            this.isShow = true;\n            break;\n          case \"3\":\n            await this.$refs[\"form\"].validate(async valid => {\n              if (valid) {\n                try {\n                  this.form.colFirst = this.propTableData.colFirst;\n                  this.form.objIdList = this.ids;\n                  this.uploadImgData.businessId = this.form.objId;\n                  this.uploadForm();\n                  await saveOrUpdate(this.form).then(res => {\n                    if (res.code === \"0000\") {\n                      this.isShowDetails = false;\n                      this.getData();\n                      this.processData.variables.pass = true;\n                      this.processData.businessKey = row.objId;\n                      this.processData.processType = type;\n                      this.activitiOption.title = \"办结\";\n                      this.processData.defaultFrom = false;\n                      this.isShow = true;\n                    } else {\n                      this.$message.error(\"失败\");\n                    }\n                  });\n                } catch (e) {}\n              }\n            });\n        }\n      } else {\n        this.isShowDetails = false;\n        this.processData.businessKey = row.objId;\n        this.processData.processType = type;\n        this.activitiOption.title = \"回退原因填写\";\n        this.processData.defaultFrom = true;\n        this.processData.variables.pass = false;\n        this.isShow = true;\n      }\n    },\n\n    //改造后的上传多个图片文件\n    uploadForm() {\n      var newUrl = []; //用来存放当前未改动过的图片url,此图片不进行删除处理，其余当前业务id下面的图片数据将进行删除\n      var imageType = [\"png\", \"jpg\"];\n      const formData = new FormData();\n      // 因为要传一个文件数组过去，所以要循环append\n      this.imgList.forEach(file => {\n        if (file.raw == undefined) {\n          newUrl.push(file.url);\n        }\n        formData.append(\"files\", file.raw);\n      });\n      formData.append(\"businessId\", this.uploadImgData.businessId); // 自定义参数\n      formData.append(\"newUrl\", newUrl); // 未改动过的图片url\n      formData.append(\"type\", imageType); // 未改动过的图片url\n      api\n        .requestPost(\"/isc-api/file/uploadFiles\", formData, 1)\n        .then(res => {\n          // 清空图片列表（一定要清空，否则上传成功后还是会调用handleChange（）函数，上传成功后列表中还存在图片）\n          this.imgList = [];\n          //查询接口数据\n          this.getData();\n        })\n        .catch(res => {\n          this.$message.success(\"图片上传失败！\");\n        });\n    },\n    // 选择文件时，往fileList里添加\n    handleChange(file, fileList) {\n      this.imgList = fileList;\n    },\n    handleProgress(event, file, fileList) {},\n    //图片移除\n    handleRemove(file, fileList) {\n      this.imgList = fileList;\n    },\n    //图片放大\n    handlePictureCardPreview(file) {\n      this.dialogImageUrl = [file.url];\n      this.imgDialogVisible = true;\n    },\n    onClose() {\n      this.imgDialogVisible = false;\n    },\n    // 全选框\n    handleCheckAllChange(val) {\n      if (val) {\n        this.form.yzxczxs = this.form.czxs;\n        this.form.wzxczxs = 0;\n      } else {\n        this.form.wzxczxs = this.form.czxs;\n        this.form.yzxczxs = 0;\n      }\n      this.propTableData.colFirst.forEach(item => {\n        item.sfwc = val;\n      });\n    },\n    //选择已执行时，操作项目默认默认全选\n    handleChangeOfSfzx(val) {\n      if (val === \"已执行\") {\n        this.form.yzxczxs = this.form.czxs;\n        this.form.wzxczxs = 0;\n        this.propTableData.colFirst.forEach(item => {\n          item.sfwc = true;\n        });\n      } else {\n        this.form.wzxczxs = this.form.czxs;\n        this.form.yzxczxs = 0;\n        this.propTableData.colFirst.forEach(item => {\n          item.sfwc = false;\n        });\n      }\n    },\n    handleCheckChange(val) {\n      if (val) {\n        ++this.form.yzxczxs;\n      } else {\n        --this.form.yzxczxs;\n      }\n      this.form.wzxczxs = this.form.czxs - this.form.yzxczxs;\n    },\n    // 预览按钮\n    handleYlChange() {\n      this.titleyl = \"查看操作项目\";\n      this.yl = true;\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        const param = { ...this.params, ...params };\n        this.params = param;\n        this.loading = true;\n        if (!param.status) {\n          param.status = \"0,1,2,3,4\";\n        }\n        param.mySorts = [{ prop: \"updateTime\", asc: false }];\n        const { data, code } = await getListLsp(param);\n        if (code === \"0000\") {\n          for (let i of data.records) {\n            i.fgsmc = this.formatSsgs(i.fgs);\n            this.statusOptions.forEach(element => {\n              if (i.status === element.value) {\n                i.statusCn = element.label;\n              }\n            });\n          }\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          this.loading = false;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    /**\n     *获取操作票明细\n     */\n    async getCzpmx(row) {\n      try {\n        const { data, code } = await getCzpmxList({ objId: row.objId });\n        if (code === \"0000\") {\n          this.propTableData.colFirst = data;\n          this.propTableData.colFirst.forEach(e => {\n            e.uuid = getUUID();\n          });\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n\n    //修改按钮\n    async getUpdate(row) {\n      await this.getCzpmx(row);\n      this.title = \"变电倒闸操作票修改\";\n      this.isDisabled = false;\n      this.isDisabledBj = false;\n      this.form = { ...row };\n      //图片结合\n      this.imgList = this.form.imgList;\n      this.jlrList = await this.getGroupUsers(62, \"\");\n      this.isShowDetails = true;\n      this.getShow();\n    },\n    //详情按钮\n    async getDetails(row) {\n      await this.getCzpmx(row);\n      this.title = \"变电操作票详情查看\";\n      this.form = { ...row };\n      this.jlrList = await this.getGroupUsers(62, \"\");\n      //图片结合\n      this.imgList = this.form.imgList;\n      this.isDisabled = true;\n      this.isDisabledBj = true;\n      this.isShowDetails = true;\n      this.getShow();\n    },\n    // 保存按钮\n    async saveRow() {\n      await this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          try {\n            this.form.colFirst = this.propTableData.colFirst;\n            this.form.objIdList = this.ids;\n            saveOrUpdate(this.form).then(res => {\n              if (res.code === \"0000\") {\n                this.$message.success(\"操作成功\");\n              }\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n              this.isShowDetails = false;\n            });\n          } catch (e) {}\n        } else {\n          this.$message.error(\"校验未通过！\");\n          return false;\n        }\n      });\n    },\n\n    //删除按钮\n    async deleteRow(row) {\n      let objId = \"\";\n      if (row.objId) {\n        objId = row.objId;\n      } else {\n        objId = this.ids[0];\n      }\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove(objId).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    //表格新增\n    listFirstAdd() {\n      let row = {\n        xh: \"\",\n        czrw: \"\",\n        sfwc: \"\",\n        uuid: getUUID()\n      };\n      this.propTableData.colFirst.push(row);\n      this.propTableData.sel = row;\n      this.form.czxs = this.propTableData.colFirst.length;\n      this.form.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //表格删除\n    listFirstDel(row) {\n      if (row.objId) {\n        this.ids.push(row.objId);\n      }\n      this.propTableData.colFirst = this.propTableData.colFirst.filter(\n        item => item.uuid !== row.uuid\n      );\n      this.form.czxs = this.propTableData.colFirst.length;\n      this.form.wzxczxs = this.propTableData.colFirst.length;\n    },\n\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false;\n    },\n    selectChange(selection) {\n      this.ids = selection.map(item => item.objId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n      this.selectData = selection;\n    },\n    /**\n     * 获取变电站下拉框数据\n     */\n    getBdzSelectList() {\n      getBdzSelectList({}).then(res => {\n        this.bdzList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"bdzmc\") {\n            return (item.options = this.bdzList);\n          }\n        });\n      });\n    },\n    //导出word\n    async exportWord(row) {\n      try {\n        let exportData = { ...row };\n        await exportWord(exportData, \"bdzdzczp\", \"变电站倒闸操作票.docx\");\n      } catch (e) {\n        this.$message.error(\"导出失败！\");\n      }\n    },\n    //导出Pdf\n    async exportPdf(row) {\n      try {\n        let exportData = { ...row };\n        await exportPdf(exportData, \"bdzdzczp\", \"变电站倒闸操作票.pdf\");\n      } catch (e) {\n        this.$message.error(\"导出失败！\");\n      }\n    },\n    //下拉框change事件\n    handleEvent(val, eventValue) {\n      if (val.label === \"fgs\") {\n        getBdzSelectList({ ssdwbm: val.value.toString() }).then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value === \"bdzmc\") {\n              this.$set(eventValue, \"bdzmc\", \"\");\n              return (item.options = res.data);\n            }\n          });\n        });\n      }\n    },\n    formatSsgs(ssgs) {\n      let pageOrganizationSelectedList = JSON.parse(\n        JSON.stringify(this.organizationSelectedList)\n      );\n      pageOrganizationSelectedList.push({\n        label: \"港东变电分公司\",\n        value: \"3002\"\n      });\n      pageOrganizationSelectedList.push({\n        label: \"港中变电分公司\",\n        value: \"3003\"\n      });\n      if (ssgs) {\n        let filter = pageOrganizationSelectedList.filter(g => g.value === ssgs);\n        if (filter.length > 0) {\n          return filter[0].label;\n        } else {\n          return \"\";\n        }\n      } else {\n        return \"\";\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.card-style {\n  background-color: #e0eff2;\n  line-height: 3vh;\n  border-radius: 3px;\n  font-weight: 800;\n  padding-left: 1vw;\n  margin-bottom: 3vh;\n}\n\n/*控制input输入框边框是否显示*/\n.elInput >>> .el-input__inner {\n  border: 0;\n}\n\n.center {\n  text-align: center; /*让div内部文字居中*/\n}\n</style>\n"], "sourceRoot": "src/views/dagangOilfield/czpgl/bddzcz"}]}