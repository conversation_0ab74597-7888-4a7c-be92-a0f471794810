{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\hskwh.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\hskwh.js", "mtime": 1706897313923}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagang<PERSON>ilfield/bzgl/hskwh.js"], "names": ["baseUrl", "getPageDataList", "query", "api", "requestPost", "remove", "saveOrUpdate", "ExportHskWH", "getTreeData", "getHsflkList", "getTreeHsk"], "mappings": ";;;;;;;;;;;;;;;AAAA;;AAEA,IAAMA,OAAO,GAAG,4BAAhB;AAEA;;;;;;AAKO,SAASC,eAAT,CAAyBC,KAAzB,EAA+B;AAClC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,mBAAxB,EAA4CE,KAA5C,EAAmD,CAAnD,CAAP;AACH;AAED;;;;;;;AAKO,SAASG,MAAT,CAAgBH,KAAhB,EAAsB;AACzB,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,kBAAxB,EAA2CE,KAA3C,EAAkD,CAAlD,CAAP;AACH;AAED;;;;;;;AAKO,SAASI,YAAT,CAAsBJ,KAAtB,EAA4B;AAC/B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,qBAAxB,EAA8CE,KAA9C,EAAqD,CAArD,CAAP;AACH;AAED;;;;;;;AAKO,SAAUK,WAAV,CAAsBL,KAAtB,EAA4B;AAC/B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,oBAAxB,EAA6CE,KAA7C,EAAoD,CAApD,CAAP;AACH;AAED;;;;;;;AAKQ,SAASM,WAAT,CAAqBN,KAArB,EAA2B;AAC/B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,qBAAxB,EAA8CE,KAA9C,EAAqD,CAArD,CAAP;AACH;AAED;;;;;;;AAKO,SAASO,YAAT,CAAsBP,KAAtB,EAA4B;AAC/B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,qBAAxB,EAA8CE,KAA9C,EAAqD,CAArD,CAAP;AACH;;AACM,SAASQ,UAAT,CAAoBR,KAApB,EAA0B;AAC7B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,mBAAxB,EAA4CE,KAA5C,EAAmD,CAAnD,CAAP;AACH", "sourcesContent": ["import api from '@/utils/request'\r\n\r\nconst baseUrl = \"/condition-maintenance-api\";\r\n\r\n/**\r\n * 查询函数库分类列表\r\n * @param query \r\n * @returns \r\n */\r\nexport function getPageDataList(query){\r\n    return api.requestPost(baseUrl+'/hskwh/getHskList',query, 2)\r\n}\r\n\r\n/**\r\n * 删除一条或者多条函数库分类\r\n * @param query \r\n * @returns \r\n */\r\nexport function remove(query){\r\n    return api.requestPost(baseUrl+'/hskwh/deleteHsk',query, 2)\r\n}\r\n\r\n/**\r\n * 添加或者修改函数库分类\r\n * @param  query \r\n * @returns \r\n */\r\nexport function saveOrUpdate(query){\r\n    return api.requestPost(baseUrl+'/hskwh/saveOrUpdate',query, 2)\r\n}\r\n\r\n/**\r\n * 导出\r\n * @param {*} query \r\n * @returns \r\n */\r\nexport function  ExportHskWH(query){\r\n    return api.requestPost(baseUrl+'/hskwh/ExportHskWH',query, 2)\r\n}\r\n\r\n/**\r\n * 查询函数库分类树形\r\n * @param {*} query \r\n * @returns \r\n */\r\n export function getTreeData(query){\r\n    return api.requestPost(baseUrl+'/hsflk/getTreeHsflk',query, 2)\r\n}\r\n\r\n/**\r\n * 查询函数库分类所有数据\r\n * @param {*} query \r\n * @returns \r\n */\r\nexport function getHsflkList(query){\r\n    return api.requestPost(baseUrl+'/hsflk/getHsflkList',query, 2)\r\n}\r\nexport function getTreeHsk(query){\r\n    return api.requestPost(baseUrl+'/hsflk/getTreeHsk',query, 2)\r\n}\r\n\r\n"]}]}