{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\jbwh_csxx.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\jbwh_csxx.vue", "mtime": 1706897323219}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["jbwh_csxx.vue"], "names": [], "mappings": ";;;;;;;;;;AAUA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "jbwh_csxx.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk", "sourcesContent": ["<template>\n  <comp-table\n    :table-and-page-info=\"tableAndPageInfo\"\n    @rowDbClick=\"clickMainTable\"\n    height=\"58vh\"\n    id=\"table_csxx\"\n  />\n</template>\n\n<script>\nimport { getPage } from '@/api/dagangOilfield/bzgl/sbztpjbzk/sbcs'\nimport { Loading } from 'element-ui'\n\nexport default {\n  name: 'jbwh_csxx',\n  props: {\n    sblx:{\n      type:String,\n      default:'',\n    },\n  },\n  data() {\n    return {\n      loading: null,//遮罩层\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n        },\n        option: {\n          serialNumber: true,\n        },\n        tableData: [],\n        tableHeader: [\n          // { prop: \"csbm\", label: \"参数编码\"},\n          { prop: \"id\", label: \"参数id\"},\n          { prop: \"csmc\", label: \"参数名称\" },\n        ],\n      },\n    };\n  },\n  mounted() {\n    this.getData();\n  },\n  methods:{\n    clickMainTable(val){\n      let map = new Map();\n      map.set(val.id,val.csmc);\n      // map.set(val.csbm,val.csmc);\n      // this.$emit('dbClickRow','getColValue(' + val.csbm + ')',map);\n      this.$emit('dbClickRow','getColValue(' + val.id + ')',map);\n    },\n    async getData(){\n      //开启遮罩层\n      this.loading = Loading.service({\n        lock: true, //lock的修改符--默认是false\n        text: \"加载中，请稍后\", //显示在加载图标下方的加载文案\n        spinner: \"el-icon-loading\", //自定义加载图标类名\n        background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n        target: document.querySelector(\"#table_csxx\"),\n      });\n      await getPage({sblx:this.sblx}).then(res=>{\n        this.tableAndPageInfo.tableData = res.data.records\n        this.tableAndPageInfo.pager.total = res.data.total\n        this.loading.close();//关闭遮罩层\n      })\n    }\n  },\n}\n</script>\n\n<style scoped>\n\n</style>\n"]}]}