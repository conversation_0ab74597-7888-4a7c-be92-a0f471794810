{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\ysbzkgl\\acceptanceDetail1.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\ysbzkgl\\acceptanceDetail1.vue", "mtime": 1706897323967}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["acceptanceDetail1.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0DA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "acceptanceDetail1.vue", "sourceRoot": "src/views/dagangOilfield/bzgl/ysbzkgl", "sourcesContent": ["<template>\n  <div>\n    <el-white class=\"button-group\">\n      <div class=\"button_btn\">临时验收项</div>\n      <el-table\n        ref=\"detailTable\"\n        stripe\n        border\n        v-loading=\"detailLoading\"\n        :data=\"detailTableData\"\n        @selection-change=\"detailSelectionChange\"\n        @row-click=\"detailRowClick\">\n        <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n        <el-table-column label=\"验收项目\" align=\"center\" prop=\"ysx\"/>\n        <el-table-column label=\"验收标准\" align=\"center\" prop=\"ysbz\" :show-overflow-tooltip=\"true\">\n          <template slot-scope=\"scope\">\n            <el-popover v-if=\"scope.row.ysbz.length>15\" trigger=\"hover\" placement=\"top\"\n              style=\"overflow: hidden;text-overflow: ellipsis;white-space: nowrap;\">\n              {{ scope.row.ysbz }}\n              <div slot=\"reference\">\n                {{ scope.row.ysbz.substring(0,15)+'...' }}\n              </div>\n            </el-popover>\n            <span v-else>\n              {{ scope.row.ysbz}}\n            </span>\n          </template>\n          </el-table-column>\n        \n        <el-table-column label=\"检查方式\" align=\"center\" prop=\"jcfs\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"验收结论\" align=\"center\" prop=\"ysjl\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"备注\" align=\"center\" prop=\"bz\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"操作\" fixed=\"right\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"180\">\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" @click=\"showDetailData(scope.row)\" title=\"详情\" class=\"el-icon-view\"></el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <pagination v-show=\"detailParams.total>0\"\n                  :total=\"detailParams.total\"\n                  :page.sync=\"detailParams.pageNum\"\n                  :limit.sync=\"detailParams.pageSize\"\n                  @pagination=\"getDetailData\"/>\n    </el-white>\n\n    <dialogForm\n      ref=\"detailForm\"\n      label-width=\"150px\"\n      out-width=\"50%\"\n      :reminder=\"reminder\"\n      :rows=\"rows\"\n      @save=\"saveDetailData\"\n    />\n  </div>\n</template>\n\n<script>\nimport { deleteBzYsbzmx, getBzYsbzmx, saveOrUpdateBzYsbzmx } from '@/api/bzgl/ysbzk/ysbzk'\nimport dialogForm from 'com/dialogFrom/dialogForm'\n\nexport default {\n  components: { dialogForm },\n  name: 'acceptanceDetail',\n  data() {\n    return {\n      //弹出框form表单每行显示几个\n      rows: 2,\n      //新增或修改标题\n      reminder: '新增',\n      //明细按钮加载\n      detailLoading: false,\n      //验收标准明细表查询条件\n      detailParams: {\n        pageNum: 1,\n        pageSize: 10,\n        total: 0,\n        //验收标准主表id\n        ysbzid: ''\n      },\n      //明细表新增表单数据\n      formList: [\n        {\n          label: '验收项：',\n          value: '',\n          type: 'input',\n          name: 'ysx',\n          default: true,\n          rules: { required: true, message: '请输入验收项' }\n        },\n        {\n          label: '验收标准：',\n          value: '',\n          type: 'textarea',\n          name: 'ysbz',\n          default: true,\n          rules: { required: true, message: '请输入验收标准' }\n        },\n        {\n          label: '检查方式：',\n          value: '',\n          type: 'input',\n          name: 'jcfs',\n          default: true,\n          rules: { required: true, message: '请输入检查方式' }\n        },\n        {\n          label: '验收结论：',\n          value: '',\n          type: 'textarea',\n          name: 'ysjl',\n          default: true,\n          rules: { required: true, message: '请输入验收类型' }\n        },\n        {\n          label: '备注：',\n          value: '',\n          name: 'bz',\n          default: true,\n          type: 'textarea',\n          rules: { required: false, message: '请输入验收问题说明' }\n        },\n        {\n          label: '主键id：',\n          value: '',\n          name: 'id',\n          default: false,\n          type: 'input',\n          hidden: false,\n          rules: { required: false }\n        },\n        {\n          label: '验收标准id：',\n          value: '',\n          name: 'ysbzid',\n          default: false,\n          type: 'input',\n          hidden: false,\n          rules: { required: false }\n        }\n      ],\n      //验收标准明细表数据\n      detailTableData: [],\n      //新增按钮是否可用\n      isCanAdd: false,\n      //主表选中数据\n      mainTableSelectRows: [],\n      //详情表选中数据\n      selectedRowData: []\n    }\n  },\n  mounted() {\n  },\n  methods: {\n    //接受父组件传值方法\n    getMainTableSelectedRow(row) {\n      this.mainTableSelectRows = row\n      if (row.length === 1) {\n        this.isCanAdd = true\n        this.getDetailData()\n      } else {\n        this.isCanAdd = false\n        this.detailLoading = true\n        this.detailTableData = []\n        this.detailLoading = false\n      }\n    },\n    //获取验收标准明细表数据\n    getDetailData() {\n      this.detailLoading = true\n      this.detailParams.ysbzid = this.mainTableSelectRows[0].id\n      this.detailParams.type = 1;\n      getBzYsbzmx(this.detailParams).then(res => {\n        this.detailTableData = res.data.records\n        this.detailParams.total = res.data.total\n        this.detailLoading = false\n      })\n    },\n\n    detailSelectionChange(row) {\n      this.selectedRowData = row\n    },\n\n    detailRowClick(val) {\n      this.$refs.detailTable.toggleRowSelection(val)\n    },\n\n    //保存明细表数据\n    saveDetailData(formData) {\n      let message = ''\n      if (formData.id === '' || !formData.id) {\n        message = '新增成功'\n        formData.ysbzid = this.mainTableSelectRows[0].id\n      } else {\n        message = '修改成功'\n      }\n      saveOrUpdateBzYsbzmx(formData).then(res => {\n        if (res.code === '0000') {\n          this.$message.success(message)\n          this.getDetailData()\n        } else {\n          this.$message.error('操作失败')\n        }\n      })\n    },\n\n    //新增明细数据\n    addDetailData() {\n      this.reminder = '新增'\n\n      //初始话formList数据\n      this.formList = this.$options.data().formList\n\n      const addForm = this.formList.map(item => {\n        return item\n      })\n      this.$refs.detailForm.showzzc(addForm)\n    },\n\n    //修改明细数据\n    updateDetailData(row) {\n      const updateList = this.formList.map(item => {\n        item.value = row[item.name]\n        return item\n      })\n      this.reminder = '修改'\n      this.$refs.detailForm.showzzc(updateList)\n    },\n\n    //明细数据详情\n    showDetailData(row) {\n      const infoList = this.formList.map(item => {\n        item.value = row[item.name]\n        return item\n      })\n      this.reminder = '详情'\n      this.$refs.detailForm.showzzc(infoList)\n    },\n\n    //删除明细信息\n    deleteDetailData() {\n      this.$confirm('确认删除选中数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        let ids = []\n        this.selectedRowData.forEach(item => {\n          ids.push(item.id)\n        })\n\n        deleteBzYsbzmx(ids).then(res => {\n          if (res.code === '0000') {\n            this.$message({\n              message: '删除成功',\n              type: 'success'\n            })\n          } else {\n            this.$message.error('操作失败')\n          }\n          this.getDetailData()\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n\n</style>\n"]}]}