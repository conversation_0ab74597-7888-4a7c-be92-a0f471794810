{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\xldzcz\\dzczcxtj.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\xldzcz\\dzczcxtj.vue", "mtime": 1706897324378}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKCnZhciBfY3pwX3RqID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuL2NvbXBvbmVudHMvY3pwX3RqIikpOwoKdmFyIF9jenBfY3ggPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4vY29tcG9uZW50cy9jenBfY3giKSk7CgovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSB7CiAgbmFtZTogJ2R6Y3pjeHRqJywKICBjb21wb25lbnRzOiB7CiAgICBjenBfdGo6IF9jenBfdGouZGVmYXVsdCwKICAgIGN6cF9jeDogX2N6cF9jeC5kZWZhdWx0CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgYWN0aXZlTmFtZTogJ2N4JwogICAgfTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8vdGFi54K55Ye75LqL5Lu2CiAgICBoYW5kbGVDbGljazogZnVuY3Rpb24gaGFuZGxlQ2xpY2sodGFiLCBldmVudCkgewogICAgICBjb25zb2xlLmxvZyh0YWIpOwogICAgICBjb25zb2xlLmxvZyhldmVudCk7CiAgICB9CiAgfQp9OwpleHBvcnRzLmRlZmF1bHQgPSBfZGVmYXVsdDs="}, {"version": 3, "sources": ["dzczcxtj.vue"], "names": [], "mappings": ";;;;;;;;;AAiBA;;AACA;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,UADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,MAAA,EAAA,eAAA;AAAA,IAAA,MAAA,EAAA;AAAA,GAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA,MAAA,UAAA,EAAA;AADA,KAAA;AAGA,GAPA;AAQA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,WAFA,uBAEA,GAFA,EAEA,KAFA,EAEA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,KAAA;AACA;AALA;AARA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-white>\n      <el-tabs v-model=\"activeName\" type=\"card\" @tab-click=\"handleClick\">\n        <el-tab-pane label=\"查询\" name=\"cx\">\n          <czp_cx></czp_cx>\n        </el-tab-pane>\n        <el-tab-pane label=\"统计\" name=\"tj\">\n          <czp_tj></czp_tj>\n        </el-tab-pane>\n      </el-tabs>\n    </el-white>\n  </div>\n</template>\n\n\n<script>\n  import czp_tj from './components/czp_tj'\n  import czp_cx from './components/czp_cx'\n\n  export default {\n    name: 'dzczcxtj',\n    components: { czp_tj, czp_cx },\n    data() {\n      return {\n        activeName: 'cx'\n      }\n    },\n    methods: {\n      //tab点击事件\n      handleClick(tab, event) {\n        console.log(tab)\n        console.log(event)\n      }\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>\n\n"], "sourceRoot": "src/views/dagangOilfield/czpgl/xldzcz"}]}