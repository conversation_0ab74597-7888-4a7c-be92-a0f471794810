{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\dwzygl\\sdsbgl\\sdgtjyz.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\dwzygl\\sdsbgl\\sdgtjyz.js", "mtime": 1706897314647}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0TGlzdEp5eiA9IGdldExpc3RKeXo7CmV4cG9ydHMuc2F2ZU9yVXBkYXRlSnl6ID0gc2F2ZU9yVXBkYXRlSnl6OwpleHBvcnRzLnJlbW92ZUp5eiA9IHJlbW92ZUp5ejsKCnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwoKdmFyIGJhc2VVcmwgPSAiL21hbmFnZXItYXBpIjsgLy8g5p+l6K+iCgpmdW5jdGlvbiBnZXRMaXN0Snl6KHBhcmFtcykgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3RQb3N0KGJhc2VVcmwgKyAnL2R3enlTZGd0anl6L3BhZ2UnLCBwYXJhbXMsIDEpOwp9IC8vIOa3u+WKoOaIluS/ruaUuQoKCmZ1bmN0aW9uIHNhdmVPclVwZGF0ZUp5eihwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy9kd3p5U2RndGp5ei9zYXZlT3JVcGRhdGUnLCBwYXJhbXMsIDEpOwp9IC8vIOWIoOmZpAoKCmZ1bmN0aW9uIHJlbW92ZUp5eihwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy9kd3p5U2RndGp5ei9yZW1vdmUnLCBwYXJhbXMsIDEpOwp9"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagang<PERSON>ilfield/dwzygl/sdsbgl/sdgtjyz.js"], "names": ["baseUrl", "getListJyz", "params", "api", "requestPost", "saveOrUpdateJyz", "removeJyz"], "mappings": ";;;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,cAAhB,C,CAEA;;AACO,SAASC,UAAT,CAAoBC,MAApB,EAA4B;AACjC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,mBAAxB,EAA4CE,MAA5C,EAAmD,CAAnD,CAAP;AACD,C,CAED;;;AACO,SAASG,eAAT,CAAyBH,MAAzB,EAAiC;AACtC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,2BAAxB,EAAoDE,MAApD,EAA2D,CAA3D,CAAP;AACD,C,CACD;;;AACO,SAASI,SAAT,CAAmBJ,MAAnB,EAA2B;AAChC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,qBAAxB,EAA8CE,MAA9C,EAAqD,CAArD,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\nconst baseUrl = \"/manager-api\";\n\n// 查询\nexport function getListJyz(params) {\n  return api.requestPost(baseUrl+'/dwzySdgtjyz/page',params,1)\n}\n\n// 添加或修改\nexport function saveOrUpdateJyz(params) {\n  return api.requestPost(baseUrl+'/dwzySdgtjyz/saveOrUpdate',params,1)\n}\n// 删除\nexport function removeJyz(params) {\n  return api.requestPost(baseUrl+'/dwzySdgtjyz/remove',params,1)\n}\n\n"]}]}