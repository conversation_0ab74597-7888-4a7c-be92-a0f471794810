{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\App.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\App.vue", "mtime": 1735911653806}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCAkIGZyb20gImpxdWVyeSI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiQXBwIiwKICBwcm92aWRlKCkgewogICAgLy/niLbnu4Tku7bkuK3pgJrov4dwcm92aWRl5p2l5o+Q5L6b5Y+Y6YeP77yM5Zyo5a2Q57uE5Lu25Lit6YCa6L+HaW5qZWN05p2l5rOo5YWl5Y+Y6YePCiAgICByZXR1cm4gewogICAgICByZWxvYWQ6IHRoaXMucmVsb2FkCiAgICB9OwogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGlzU2hvdzogdHJ1ZSAvL+aOp+WItuinhuWbvuaYr+WQpuaYvuekuueahOWPmOmHjwogICAgfTsKICB9LAogIG1ldGhvZHM6IHsKICAgIHJlbG9hZCgpIHsKICAgICAgdGhpcy5pc1Nob3cgPSBmYWxzZTsgLy/lhYjlhbPpl60KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIHRoaXMuaXNTaG93ID0gdHJ1ZTsgLy/lho3miZPlvIAKICAgICAgfSk7CiAgICB9CiAgfSwKfTsKCnZhciBub3dUaW1lID0gbmV3IERhdGUoKS5nZXRUaW1lKCk7Ci8v57uT5p2f5pe26Ze0CnZhciBlbmRUaW1lID0gbmV3IERhdGUoIjIwMjIvMTIvMDMgMDA6MDA6MDAiKS5nZXRUaW1lKCk7CgovLyBpZihub3dUaW1lIDwgZW5kVGltZSl7Ci8v5re75Yqg5YWo5bGA54Gw6Imy5ruk6ZWcCi8vICQoImh0bWwiKS5jc3MoewovLyAgICctd2Via2l0LWZpbHRlcic6ICdncmF5c2NhbGUoMTAwJSknLAovLyAgICctbW96LWZpbHRlcic6ICdncmF5c2NhbGUoMTAwJSknLAovLyAgICctbXMtZmlsdGVyJzogJ2dyYXlzY2FsZSgxMDAlKScsCi8vICAgJy1vLWZpbHRlcic6ICdncmF5c2NhbGUoMTAwJSknLAovLyAgIC8vIGll5ruk6ZWcCi8vICAgJ2ZpbHRlcic6ICdwcm9naWQ6RFhJbWFnZVRyYW5zZm9ybS5NaWNyb3NvZnQuQmFzaWNJbWFnZShncmF5c2NhbGU9MSknLAovLyAgIC8vIGllNiDnrYnkvY7niYjmnKzmtY/op4jlmajkuI3pnIDopoHliqDmu6TplZwKLy8gICAnX2ZpbHRlcic6ICdub25lJwovLyB9KQovLyB9Cg=="}, {"version": 3, "sources": ["App.vue"], "names": [], "mappings": ";;;;;;;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "App.vue", "sourceRoot": "src", "sourcesContent": ["<template>\n  <div id=\"app\">\n    <router-view v-if=\"isShow\" />\n  </div>\n</template>\n\n<script>\nimport $ from \"jquery\";\nexport default {\n  name: \"App\",\n  provide() {\n    //父组件中通过provide来提供变量，在子组件中通过inject来注入变量\n    return {\n      reload: this.reload\n    };\n  },\n  data() {\n    return {\n      isShow: true //控制视图是否显示的变量\n    };\n  },\n  methods: {\n    reload() {\n      this.isShow = false; //先关闭\n      this.$nextTick(() => {\n        this.isShow = true; //再打开\n      });\n    }\n  },\n};\n\nvar nowTime = new Date().getTime();\n//结束时间\nvar endTime = new Date(\"2022/12/03 00:00:00\").getTime();\n\n// if(nowTime < endTime){\n//添加全局灰色滤镜\n// $(\"html\").css({\n//   '-webkit-filter': 'grayscale(100%)',\n//   '-moz-filter': 'grayscale(100%)',\n//   '-ms-filter': 'grayscale(100%)',\n//   '-o-filter': 'grayscale(100%)',\n//   // ie滤镜\n//   'filter': 'progid:DXImageTransform.Microsoft.BasicImage(grayscale=1)',\n//   // ie6 等低版本浏览器不需要加滤镜\n//   '_filter': 'none'\n// })\n// }\n</script>\n"]}]}