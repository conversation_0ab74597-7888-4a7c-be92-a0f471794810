{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\jbwh_edit.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\jbwh_edit.vue", "mtime": 1706897323219}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["jbwh_edit.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4DA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "jbwh_edit.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk", "sourcesContent": ["<template>\n  <el-row :gutter=\"16\">\n    <!--  原始公式  -->\n    <el-col :span=\"24\">\n      <div>\n        <el-button type=\"primary\" @click=\"check_jb\">验证</el-button>\n        <el-button type=\"primary\" @click=\"save_jb\">保存</el-button>\n        <el-button type=\"primary\" @click=\"init_jb\">初始化</el-button>\n        <el-button type=\"primary\" @click=\"clear_jb\">清空</el-button>\n        <el-button type=\"success\" @click=\"init_yb\">一般C</el-button>\n        <el-button type=\"success\" @click=\"init_yz\">一般B</el-button>\n        <el-button type=\"success\" @click=\"init_wj\">一般A</el-button>\n      </div>\n    </el-col>\n    <el-col :span=\"24\">\n      <div class=\"row\" style=\"border:1px;\" id=\"atChat\">\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' && ')\">&&</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' || ')\">||</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' > ')\">></a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' >= ')\">>=</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' < ')\"><</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' <= ')\"><=</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' == ')\">==</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' != ')\">!=</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' Math.abs() ')\">abs</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' ( ')\">(</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' ) ')\">)</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' { ')\">{</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' } ')\">}</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick('elseIf')\">else if</a>\n      </div>\n    </el-col>\n\n    <!--  规则解释  -->\n    <el-col :span=\"24\">\n      <el-form :model=\"editForm\" ref=\"editForm\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item prop=\"gs\">\n              <el-input id=\"jb_text\" name=\"pptjms\" type=\"textarea\" :rows=\"10\" v-model=\"editForm.gs\" placeholder=\"请输入脚本\"\n                        v-on:input.native=\"jb_show\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <div>\n              <span>规则解释</span>\n            </div>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item prop=\"gzjs\">\n              <el-input id=\"jb_text_show\" name=\"pptjms\" type=\"textarea\" :rows=\"10\" v-model=\"editForm.gzjs\" disabled/>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n    </el-col>\n  </el-row>\n</template>\n\n<script>\nimport {checkJb} from '@/api/dagangOilfield/bzgl/sbztpjbzk/jbwh'\n\nexport default {\n  name: 'jbwh_edit',\n  props: {\n    jb: {\n      type: String,\n      default: ''\n    },\n  },\n  data() {\n    return {\n      editForm: {\n        gs: '',//公式\n        gzjs: '',//规则解释\n      },\n      checkJB: false,//是否验证脚本\n      jbVal: '',//返回给组件中的脚本字符串\n      parentMap: new Map(),\n    };\n  },\n  watch: {\n    jb: {\n      handler(newVal, oldVal) {\n        this.editForm.gs = newVal;\n        this.jb_show();\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  mounted() {\n  },\n  methods: {\n    processParentVal(val, map) {\n      map.forEach((val, key) => {\n        if (!this.parentMap.has(key)) {\n          this.parentMap.set(key, val);\n        }\n      })\n      this.trTableClick(val);\n    },\n    //放置光标数据\n    trTableClick(str,) {\n      let obj = document.getElementById('jb_text');\n      if (document.selection) {\n        let sel = document.selection.createRange();\n        sel.text = str;\n      } else if (typeof obj.selectionStart === 'number' && typeof obj.selectionEnd === 'number') {\n        let startPos = obj.selectionStart;\n        let endPos = obj.selectionEnd;\n        let cursorPos = startPos;\n        let tmpStr = obj.value;\n        obj.value = tmpStr.substring(0, startPos) + str + tmpStr.substring(endPos, tmpStr.length);\n        this.editForm.gs = obj.value;//设置公式值\n        cursorPos += str.length;\n        obj.selectionStart = obj.selectionEnd = cursorPos;\n      } else {\n        obj.value += str;\n        this.editForm.gs = obj.value;//设置公式值\n      }\n      this.jb_show();\n    },\n    //绑定a标签点击事件\n    async btnClick(val) {\n      await this.trTableClick(val == 'elseIf' ? \"\\nelse if()\\n{\\n    return    ;\\n}\" : val);\n    },\n    //脚本翻译\n    jb_show() {\n      let ruleScriptStr = this.editForm.gs;\n      ruleScriptStr = ruleScriptStr.replace(/else if/g, \"如果\")\n          .replace(/if/g, \"如果\")\n          .replace(/else/g, \"否则\")\n          .replace(/getParameter/g, \"参数值\")\n          .replace(/getColValue/g, \"参数值\")\n          .replace(/getXxdData/g, \"信息点值\")\n          .replace(/getZxXxdData/g, \"字信息点值\")\n          .replace(/>=/g, \"大于等于\")\n          .replace(/>/g, \"大于\")\n          .replace(/<=/g, \"小于等于\")\n          .replace(/</g, \"小于\")\n          .replace(/==/g, \"等于\")\n          .replace(/!=/g, \"不等于\")\n          .replace(/\\|\\|/g, \"或者\")\n          .replace(/&&/g, \"并且\")\n          .replace(/return/g, \"返回\")\n          .replace(/(Math.abs)\\s*\\(/g, \"绝对值(\")\n          .replace(/getQxdj/g, \"隐患等级\");\n\n      if (this.parentMap) {\n        this.parentMap.forEach((val, key) => {\n          ruleScriptStr = ruleScriptStr.replaceAll(key, val);\n        })\n      }\n      this.editForm.gzjs = ruleScriptStr;\n    },\n    //脚本验证\n    check_jb() {\n      this.checkJB = false;\n      let str = this.editForm.gs;\n      checkJb(str).then(res => {\n        if (res !== '脚本定义错误！') {\n          this.checkJB = true;\n          this.$message({\n            type: 'success',\n            message: '脚本执行成功!'\n          })\n        } else {\n          this.checkJB = false;\n          this.$message({\n            type: 'error',\n            message: '脚本定义错误!'\n          })\n        }\n      })\n    },\n    //脚本保存\n    save_jb() {\n      if (this.checkJB) {\n        this.jbVal = this.editForm.gs;\n        this.$emit('setJbVal', this.jbVal);//将脚本的值传递给父页面\n        this.$emit('jbClose');//关闭脚本弹框\n\n      } else {\n        this.$message({\n          type: 'error',\n          message: '脚本没有验证或脚本定义错误，请进行验证后或定义正确脚本在保存！'\n        })\n      }\n    },\n    //清空脚本\n    clear_jb() {\n      this.checkJB = false;\n      this.editForm.gs = \"\";\n      this.jb_show();\n    },\n    //初始化\n    init_jb() {\n      this.checkJB = false;\n      this.editForm.gs = \"if()\\n{\\n    return    1;\\n}\\nelse\\n{\\n    return     0;\\n}\";\n      this.jb_show();\n    },\n    //一般\n    init_yb() {\n      this.checkJB = false;\n      this.editForm.gs = \"if(getQxdj()>=0)\\n\" +\n          \"{\\n\" +\n          \"    return    1;\\n\" +\n          \"}\\n\" +\n          \"else\\n\" +\n          \"{\\n\" +\n          \"    return    0;\\n\" +\n          \"}\";\n      this.jb_show();\n    },\n    //严重\n    init_yz() {\n      this.checkJB = false;\n      this.editForm.gs = \"if(getQxdj()>=1)\\n\" +\n          \"{\\n\" +\n          \"    return    1;\\n\" +\n          \"}\\n\" +\n          \"else\\n\" +\n          \"{\\n\" +\n          \"    return    0;\\n\" +\n          \"}\";\n      this.jb_show();\n    },\n    //危急\n    init_wj() {\n      this.checkJB = false;\n      this.editForm.gs = \"if(getQxdj()>=2)\\n\" +\n          \"{\\n\" +\n          \"    return    1;\\n\" +\n          \"}\\n\" +\n          \"else\\n\" +\n          \"{\\n\" +\n          \"    return    0;\\n\" +\n          \"}\";\n      this.jb_show();\n    },\n  },\n}\n</script>\n\n<style scoped lang=\"scss\">\n.row {\n  padding-top: 20px;\n  padding-bottom: 20px;\n  font-size: 20px;\n}\n\n.btn {\n  padding: 14px;\n\n  &:hover {\n    color: #00c39a;\n  }\n}\n</style>\n"]}]}