{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\ztxxsjglpz.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\ztxxsjglpz.vue", "mtime": 1706897323231}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7Z2V0U2JseEFuZFNiYmpUcmVlfSBmcm9tICJAL2FwaS9kYWdhbmdPaWxmaWVsZC9iemdsL3NienRwamJ6ay96dGxteHdoIjsKaW1wb3J0IHtnZXRGbHlqVHJlZSwgZ2V0UGFnZUxpc3QsIGdldFN5Ym1PcHRpb25zLCByZW1vdmUsIHNhdmVPclVwZGF0ZX0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYnpnbC9zYnp0cGpiemsvenR4eHNqZ2wiOwppbXBvcnQgZ3N3aCBmcm9tICJAL3ZpZXdzL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvc2J6dHBqYnprL2dzd2giOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogInp0eHhzamdscHoiLAogIGNvbXBvbmVudHM6IHtnc3dofSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgaXNTaG93SmI6ZmFsc2UsLy/mmK/lkKbmmL7npLrohJrmnKznu7TmiqTmoYYKICAgICAgc3l4bU9wdGlvbnM6IFtdLAogICAgICBmbHlqT3B0aW9uczogW10sCiAgICAgIC8vdGFi6aG15ZCN56ewCiAgICAgIGFjdGl2ZU5hbWU6ICdxeCcsCiAgICAgIC8v5qCR57uT5p6ECiAgICAgIGRlZmF1bHRQcm9wczogewogICAgICAgIGNoaWxkcmVuOiAiY2hpbGRyZW4iLAogICAgICAgIGxhYmVsOiAibGFiZWwiLAogICAgICB9LAogICAgICAvL+iuvuWkh+exu+Wei+e8lueggQogICAgICBzYmx4Ym06ICIiLAogICAgICAvL+aWsOWinuaMiemSrmZvcm3ooajljZUKICAgICAgZm9ybToge30sCiAgICAgIHRpdGxlOiAiIiwKICAgICAgc2hvdzogZmFsc2UsCiAgICAgIGZpbHRlckluZm86IHsKICAgICAgICBkYXRhOiB7CiAgICAgICAgICB5d2R3QXJyOiBbXSwKICAgICAgICB9LAogICAgICAgIGZpZWxkTGlzdDogWwogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIuiuvuWkh+exu+WeiyIsCiAgICAgICAgICAgIHR5cGU6ICJzZWxlY3QiLAogICAgICAgICAgICB2YWx1ZTogInJvbGVOYW1lIiwKICAgICAgICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgICAgICAgIG9wdGlvbnM6IFtdLAogICAgICAgICAgfSwKICAgICAgICBdLAogICAgICB9LAogICAgICB0YWJsZUFuZFBhZ2VJbmZvOiB7CiAgICAgICAgcGFnZXI6IHsKICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgIHNpemVzOiBbMTAsIDIwLCA1MCwgMTAwXSwKICAgICAgICB9LAogICAgICAgIG9wdGlvbjogewogICAgICAgICAgY2hlY2tCb3g6IHRydWUsCiAgICAgICAgICBzZXJpYWxOdW1iZXI6IHRydWUsCiAgICAgICAgfSwKICAgICAgICB0YWJsZURhdGE6IFtdLAogICAgICAgIHRhYmxlSGVhZGVyOiBbCiAgICAgICAgICB7cHJvcDogInNibHhtYyIsIGxhYmVsOiAi6K+E5Lu35a+85YiZ6K6+5aSH57G75Z6LIiwgbWluV2lkdGg6ICcxMDAnfSwKICAgICAgICAgIHtwcm9wOiAic2Jiam1jIiwgbGFiZWw6ICLpg6jku7blkI3np7AifSwKICAgICAgICAgIHtwcm9wOiAienRsbWMiLCBsYWJlbDogIueKtuaAgemHj+WQjeensCJ9LAogICAgICAgICAge3Byb3A6ICJ6dGx4eGRtYyIsIGxhYmVsOiAi54q25oCB6YeP5L+h5oGv54K5In0sCiAgICAgICAgICB7cHJvcDogInp0eHhibW1jIiwgbGFiZWw6ICLlhbPogZTpobkiLCBtaW5XaWR0aDogJzI0MCd9LAogICAgICAgICAgewogICAgICAgICAgICBwcm9wOiAib3BlcmF0aW9uIiwKICAgICAgICAgICAgbGFiZWw6ICLmk43kvZwiLAogICAgICAgICAgICBtaW5XaWR0aDogIjEzMHB4IiwKICAgICAgICAgICAgc3R5bGU6IHtkaXNwbGF5OiAiYmxvY2sifSwKICAgICAgICAgICAgLy/mk43kvZzliJflm7rlrprlho3lj7PkvqcKICAgICAgICAgICAgZml4ZWQ6ICJyaWdodCIsCiAgICAgICAgICAgIG9wZXJhdGlvbjogWwogICAgICAgICAgICAgIHtuYW1lOiAi5L+u5pS5IiwgY2xpY2tGdW46IHRoaXMudXBkYXRlUm93fSwKICAgICAgICAgICAgICB7bmFtZTogIuivpuaDhSIsIGNsaWNrRnVuOiB0aGlzLmdldEluZm99LAogICAgICAgICAgICBdLAogICAgICAgICAgfSwKICAgICAgICBdLAogICAgICB9LAogICAgICBxdWVyeXp0bG14d2hQYXJhbToge30sCiAgICAgIHNlbGVjdFJvd3M6IFtdLAogICAgICAvL+eCueWHu+agkeiKgueCuei1i+WAvAogICAgICB0cmVlRm9ybToge30sCiAgICAgIC8v57uE57uH5qCRCiAgICAgIHRyZWVkYXRhOiBbXSwKICAgICAgLy/mlrDlop7mjInpkq7mjqfliLYKICAgICAgYWRkRGlzYWJsZWQ6IHRydWUsCiAgICAgIGJqbWNMaXN0OiBbXSwKICAgICAgcnVsZXM6IHsKICAgICAgICB6dHh4Ym06IFsKICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIn0sCiAgICAgICAgXSwKICAgICAgfSwKCiAgICAgIGlzRGlzYWJsZWQ6IGZhbHNlLAogICAgICAvL+mAieS4reihjOaVsOaNrgogICAgICByb3dEYXRhOiB7fSwKICAgICAgLy/ooajljZXpgInkuK3mlbDmja4KICAgICAgc2VsZWN0ZWRSb3dEYXRhQXJyOiBbXSwKICAgICAgLy/nirbmgIHph4/kv6Hmga/ngrnmlrDlop4KICAgICAgaXNTaG93UGFyYW1zQW5kUGFydHM6IGZhbHNlLAogICAgfTsKICB9LAogIGNyZWF0ZSgpIHsKICB9LAoKICBtb3VudGVkKCkgewogICAgLy/liJfooajmn6Xor6IKICAgIHRoaXMuZ2V0VHJlZU5vZGUoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8v6K6+572u6ISa5pys5YC8CiAgICBzZXRKYlZhbCh2YWwpewogICAgICB0aGlzLmZvcm0uY2xneiA9IHZhbDsKICAgIH0sCiAgICBqYkNsb3NlKCl7CiAgICAgIHRoaXMuaXNTaG93SmIgPSBmYWxzZTsKICAgIH0sCiAgICB0ZXh0YXJlYUNsaWNrKCl7CiAgICAgIGlmICh0aGlzLmZvcm0uenR4eGJtKXsKICAgICAgICB0aGlzLmlzU2hvd0piID0gdHJ1ZTsKICAgICAgfWVsc2UgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuivt+WFiOmAieaLqeivlemqjOmhueebriIpCiAgICAgIH0KCiAgICB9LAogICAgLy/ojrflj5bmoJHoioLngrnmlbDmja4KICAgIGdldFRyZWVOb2RlKCkgewogICAgICBnZXRTYmx4QW5kU2JialRyZWUoe3R5cGU6ICJ6dGx4eGdscHoifSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIHRoaXMudHJlZWRhdGEgPSByZXMuZGF0YTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIGFzeW5jIGdldEZseWpUcmVlKCkgewogICAgICBpZiAodGhpcy5zYmx4Ym0pIHsKICAgICAgICBsZXQgcXhsYiA9ICczJzsKICAgICAgICBpZiAodGhpcy5zYmx4Ym0uaW5kZXhPZigiYmQiKSA+IC0xKSB7CiAgICAgICAgICBxeGxiID0gJzEnCiAgICAgICAgfQogICAgICAgIGF3YWl0IGdldEZseWpUcmVlKHtzYmx4OiB0aGlzLnNibHhibSwgcXhsYjogcXhsYn0pLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgdGhpcy5mbHlqT3B0aW9ucyA9IHJlcy5kYXRhOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9CiAgICB9LAoKICAgIGFzeW5jIGdldFN5Ym1PcHRpb25zKCkgewogICAgICBpZiAodGhpcy5zYmx4Ym0pIHsKICAgICAgICBhd2FpdCBnZXRTeWJtT3B0aW9ucyh7c2JseGJtOiB0aGlzLnNibHhibSwgaXNNcFN5eG06IDF9KS50aGVuKChyZXMpID0+IHsKICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgIHRoaXMuc3l4bU9wdGlvbnMgPSByZXMuZGF0YTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfQogICAgfSwKCiAgICAvL+agkeiKgueCueeCueWHu+S6i+S7tgogICAgYXN5bmMgaGFuZGxlTm9kZUNsaWNrKGRhdGEsIG5vZGUpIHsKICAgICAgdGhpcy5hZGREaXNhYmxlZCA9IHRydWUKICAgICAgdGhpcy5xdWVyeXp0bG14d2hQYXJhbS5zYmx4ID0gIiI7CiAgICAgIHRoaXMucXVlcnl6dGxteHdoUGFyYW0uc2JiaklkID0gIiI7CiAgICAgIHRoaXMucXVlcnl6dGxteHdoUGFyYW0uenRsemJtID0gIiI7CiAgICAgIHRoaXMucXVlcnl6dGxteHdoUGFyYW0uenRseHhkSWQgPSAiIjsKICAgICAgdGhpcy5xdWVyeXp0bG14d2hQYXJhbS54eGx4ID0gIiI7CgogICAgICB0aGlzLnRyZWVGb3JtLnp0bHh4ZElkID0gIiI7CiAgICAgIHRoaXMudHJlZUZvcm0uenRsemJtID0gIiI7CiAgICAgIHRoaXMudHJlZUZvcm0uc2JiaklkID0gIiI7CiAgICAgIHRoaXMudHJlZUZvcm0uc2JseCA9ICIiOwogICAgICBpZiAoZGF0YS5ub2RlTGV2ZWwgPT09ICIxIikgewogICAgICAgIHRoaXMuc2JseGJtID0gZGF0YS5pZDsKICAgICAgICBhd2FpdCB0aGlzLmdldEZseWpUcmVlKCkKICAgICAgICBhd2FpdCB0aGlzLmdldFN5Ym1PcHRpb25zKCkKICAgICAgICBhd2FpdCB0aGlzLmdldERhdGEoKTsKICAgICAgfQoKICAgICAgaWYgKGRhdGEubm9kZUxldmVsID09PSAiMiIpIHsKICAgICAgICB0aGlzLnF1ZXJ5enRsbXh3aFBhcmFtLnNiYmpJZCA9IGRhdGEuaWQ7CiAgICAgICAgdGhpcy5zYmx4Ym0gPSBub2RlLnBhcmVudC5kYXRhLmlkOwogICAgICAgIGF3YWl0IHRoaXMuZ2V0Rmx5alRyZWUoKQogICAgICAgIGF3YWl0IHRoaXMuZ2V0U3libU9wdGlvbnMoKQogICAgICAgIGF3YWl0IHRoaXMuZ2V0RGF0YSgpOwogICAgICB9CgogICAgICBpZiAoZGF0YS5ub2RlTGV2ZWwgPT09ICIzIikgewogICAgICAgIHRoaXMucXVlcnl6dGxteHdoUGFyYW0ueHhseCA9IGRhdGEuaWQ7CiAgICAgICAgdGhpcy5xdWVyeXp0bG14d2hQYXJhbS5zYmJqSWQgPSBub2RlLnBhcmVudC5kYXRhLmlkOwogICAgICAgIHRoaXMuc2JseGJtID0gbm9kZS5wYXJlbnQucGFyZW50LmRhdGEuaWQ7CiAgICAgICAgYXdhaXQgdGhpcy5nZXRGbHlqVHJlZSgpCiAgICAgICAgYXdhaXQgdGhpcy5nZXRTeWJtT3B0aW9ucygpCiAgICAgICAgYXdhaXQgdGhpcy5nZXREYXRhKCk7CiAgICAgIH0KCiAgICAgIGlmIChkYXRhLm5vZGVMZXZlbCA9PT0gIjQiKSB7CiAgICAgICAgdGhpcy5xdWVyeXp0bG14d2hQYXJhbS54eGx4ID0gbm9kZS5wYXJlbnQuZGF0YS5pZDsKICAgICAgICB0aGlzLnF1ZXJ5enRsbXh3aFBhcmFtLnp0bHpibSA9IGRhdGEuaWQ7CiAgICAgICAgdGhpcy5xdWVyeXp0bG14d2hQYXJhbS5zYmJqSWQgPSBub2RlLnBhcmVudC5wYXJlbnQuZGF0YS5pZDsKICAgICAgICB0aGlzLnNibHhibSA9IG5vZGUucGFyZW50LnBhcmVudC5wYXJlbnQuZGF0YS5pZAogICAgICAgIGF3YWl0IHRoaXMuZ2V0Rmx5alRyZWUoKQogICAgICAgIGF3YWl0IHRoaXMuZ2V0U3libU9wdGlvbnMoKQogICAgICAgIGF3YWl0IHRoaXMuZ2V0RGF0YSgpOwogICAgICB9CgogICAgICBpZiAoZGF0YS5ub2RlTGV2ZWwgPT09ICI1IikgewogICAgICAgIHRoaXMucXVlcnl6dGxteHdoUGFyYW0ueHhseCA9IG5vZGUucGFyZW50LnBhcmVudC5kYXRhLmlkOwogICAgICAgIHRoaXMucXVlcnl6dGxteHdoUGFyYW0uenRseHhkSWQgPSBkYXRhLmlkOwogICAgICAgIHRoaXMucXVlcnl6dGxteHdoUGFyYW0uc2JiaklkID0gbm9kZS5wYXJlbnQucGFyZW50LnBhcmVudC5kYXRhLmlkOwogICAgICAgIHRoaXMucXVlcnl6dGxteHdoUGFyYW0uenRsemJtID0gbm9kZS5wYXJlbnQuZGF0YS5pZDsKICAgICAgICB0aGlzLnNibHhibSA9IG5vZGUucGFyZW50LnBhcmVudC5wYXJlbnQucGFyZW50LmRhdGEuaWQKICAgICAgICBhd2FpdCB0aGlzLmdldEZseWpUcmVlKCkKICAgICAgICBhd2FpdCB0aGlzLmdldFN5Ym1PcHRpb25zKCkKICAgICAgICB0aGlzLnRyZWVGb3JtLnp0bHh4ZElkID0gZGF0YS5pZDsKICAgICAgICB0aGlzLnRyZWVGb3JtLnp0bHh4ZG1jID0gZGF0YS5sYWJlbDsKICAgICAgICB0aGlzLnRyZWVGb3JtLnp0bHpibSA9IG5vZGUucGFyZW50LmRhdGEuaWQ7CiAgICAgICAgdGhpcy50cmVlRm9ybS56dGxtYyA9IG5vZGUucGFyZW50LmRhdGEubGFiZWw7CiAgICAgICAgdGhpcy50cmVlRm9ybS54eGx4ID0gbm9kZS5wYXJlbnQucGFyZW50LmRhdGEuaWQ7CiAgICAgICAgdGhpcy50cmVlRm9ybS5zYmJqSWQgPSBub2RlLnBhcmVudC5wYXJlbnQucGFyZW50LmRhdGEuaWQ7CiAgICAgICAgdGhpcy50cmVlRm9ybS5zYmJqbWMgPSBub2RlLnBhcmVudC5wYXJlbnQucGFyZW50LmRhdGEubGFiZWw7CiAgICAgICAgdGhpcy50cmVlRm9ybS5zYmx4ID0gbm9kZS5wYXJlbnQucGFyZW50LnBhcmVudC5wYXJlbnQuZGF0YS5pZDsKICAgICAgICB0aGlzLnRyZWVGb3JtLnNibHhtYyA9IG5vZGUucGFyZW50LnBhcmVudC5wYXJlbnQucGFyZW50LmRhdGEubGFiZWw7CiAgICAgICAgdGhpcy5hZGREaXNhYmxlZCA9IGZhbHNlOwogICAgICAgIGF3YWl0IHRoaXMuZ2V0RGF0YSgpOwogICAgICB9CiAgICB9CiAgICAsCgogICAgLy/liJfooajmn6Xor6IKICAgIGFzeW5jIGdldERhdGEocGFyYW1zKSB7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcGFyYW0gPSB7Li4udGhpcy5xdWVyeXp0bG14d2hQYXJhbSwgLi4ucGFyYW1zfTsKICAgICAgICBwYXJhbS5zYmx4ID0gdGhpcy5zYmx4Ym07CiAgICAgICAgY29uc3Qge2RhdGEsIGNvZGV9ID0gYXdhaXQgZ2V0UGFnZUxpc3QocGFyYW0pOwogICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIGRhdGEucmVjb3Jkcy5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgICBpZiAoaXRlbS54eGx4ID09PSAncXgnKSB7CiAgICAgICAgICAgICAgaXRlbS56dHh4Ym0gPSBKU09OLnBhcnNlKGl0ZW0uenR4eGJtLnJlcGxhY2UoLycvZywgIlwiIikpOwogICAgICAgICAgICAgIGl0ZW0uenR4eGJtbWMgPSB0aGlzLmdldFp0eHhibU1jKGl0ZW0uenR4eGJtKTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICBpdGVtLnp0eHhibW1jID0gdGhpcy5nZXRadHh4Ym1TeU1jKGl0ZW0uenR4eGJtKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSkKICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby50YWJsZURhdGEgPSBkYXRhLnJlY29yZHM7CiAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIudG90YWwgPSBkYXRhLnRvdGFsOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgIGNvbnNvbGUubG9nKGUpOwogICAgICB9CiAgICB9CiAgICAsCgogICAgLy/mlrDlop4KICAgIGFzeW5jIGdldEluc3RlcigpIHsKICAgICAgaWYgKCF0aGlzLnRyZWVGb3JtLnp0bHh4ZElkKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7fpgInmi6nlt6bkvqflr7nlupTnirbmgIHph4/kv6Hmga/ngrnvvIHvvIHvvIEiKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgdGhpcy5mb3JtLnp0bHh4ZElkID0gdGhpcy50cmVlRm9ybS56dGx4eGRJZAogICAgICB0aGlzLmZvcm0uenRseHhkbWMgPSB0aGlzLnRyZWVGb3JtLnp0bHh4ZG1jCiAgICAgIHRoaXMuZm9ybS56dGx6Ym0gPSB0aGlzLnRyZWVGb3JtLnp0bHpibQogICAgICB0aGlzLmZvcm0uenRsbWMgPSB0aGlzLnRyZWVGb3JtLnp0bG1jCiAgICAgIHRoaXMuZm9ybS5zYmJqSWQgPSB0aGlzLnRyZWVGb3JtLnNiYmpJZAogICAgICB0aGlzLmZvcm0uc2Jiam1jID0gdGhpcy50cmVlRm9ybS5zYmJqbWMKICAgICAgdGhpcy5mb3JtLnNibHggPSB0aGlzLnRyZWVGb3JtLnNibHgKICAgICAgdGhpcy5mb3JtLnNibHhtYyA9IHRoaXMudHJlZUZvcm0uc2JseG1jCiAgICAgIHRoaXMuZm9ybS54eGx4ID0gdGhpcy50cmVlRm9ybS54eGx4CiAgICAgIHRoaXMudGl0bGUgPSAi5paw5aKeIjsKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gZmFsc2U7CiAgICAgIHRoaXMuc2hvdyA9IHRydWU7CiAgICB9CiAgICAsCiAgICAvL+e8lui+keaMiemSrgogICAgYXN5bmMgdXBkYXRlUm93KHJvdykgewogICAgICB0aGlzLnRpdGxlID0gIueKtuaAgemHj+aooeWei+S/ruaUuSI7CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICB0aGlzLmZvcm0gPSB7Li4ucm93fTsKICAgICAgdGhpcy5zaG93ID0gdHJ1ZTsKICAgIH0KICAgICwKICAgIC8v6K+m5oOF5oyJ6ZKuCiAgICBhc3luYyBnZXRJbmZvKHJvdykgewogICAgICB0aGlzLnRpdGxlID0gIuivpuaDheafpeeciyI7CiAgICAgIHRoaXMuZm9ybSA9IHsuLi5yb3d9OwogICAgICB0aGlzLmlzRGlzYWJsZWQgPSB0cnVlOwogICAgICB0aGlzLnNob3cgPSB0cnVlOwogICAgfQogICAgLAoKICAgIGFzeW5jIHNhdmVSb3coKSB7CiAgICAgIGF3YWl0IHRoaXMuJHJlZnNbJ2Zvcm0nXS52YWxpZGF0ZShhc3luYyB2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB0cnkgewogICAgICAgICAgICBpZiAodGhpcy5mb3JtLnh4bHggPT09ICJxeCIpIHsKICAgICAgICAgICAgICBsZXQgY2hlY2tlZE5vZGVzID0gdGhpcy4kcmVmcy5teUNhc2NhZGVyLmdldENoZWNrZWROb2RlcygpWzBdOwogICAgICAgICAgICAgIHRoaXMuZm9ybS56dHh4Ym1JZCA9IGNoZWNrZWROb2Rlcy52YWx1ZTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGlzLmZvcm0uenR4eGJtSWQgPSB0aGlzLmZvcm0uenR4eGJtOwogICAgICAgICAgICB9CgogICAgICAgICAgICBsZXQge2NvZGV9ID0gYXdhaXQgc2F2ZU9yVXBkYXRlKHRoaXMuZm9ybSk7CiAgICAgICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKnyIpOwogICAgICAgICAgICB9CiAgICAgICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgICAgIGNvbnNvbGUubG9nKGUpOwogICAgICAgICAgfQogICAgICAgICAgYXdhaXQgdGhpcy5nZXREYXRhKCk7CiAgICAgICAgICAvLyB0aGlzLnNob3cgPSBmYWxzZTsKICAgICAgICB9CiAgICAgIH0pCiAgICB9CiAgICAsCiAgICAvL+WIoOmZpOaMiemSrgogICAgYXN5bmMgZGVsZXRlUm93KCkgewogICAgICBpZiAodGhpcy5zZWxlY3RSb3dzLmxlbmd0aCA8IDEpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+mAieaLqeato+ehrueahOaVsOaNru+8ge+8ge+8gSIpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBsZXQgaWRzID0gdGhpcy5zZWxlY3RSb3dzLm1hcCgoaXRlbSkgPT4gewogICAgICAgIHJldHVybiBpdGVtLm9iaklkOwogICAgICB9KTsKICAgICAgdGhpcy4kY29uZmlybSgi5q2k5pON5L2c5bCG5rC45LmF5Yig6Zmk6K+l5pWw5o2uLCDmmK/lkKbnu6fnu60/IiwgIuaPkOekuiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciLAogICAgICB9KQogICAgICAgICAgLnRoZW4oKCkgPT4gewogICAgICAgICAgICByZW1vdmUoaWRzKS50aGVuKCh7Y29kZX0pID0+IHsKICAgICAgICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLAogICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5Yig6Zmk5oiQ5YqfISIsCiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIuWIoOmZpOWksei0pSEiLAogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KTsKICAgICAgICAgIH0pCiAgICAgICAgICAuY2F0Y2goKCkgPT4gewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICB0eXBlOiAiaW5mbyIsCiAgICAgICAgICAgICAgbWVzc2FnZTogIuW3suWPlua2iOWIoOmZpCIsCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSk7CiAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgfQogICAgLAogICAgLy/lhbPpl63lvLnnqpcKICAgIGdldEluc3RlckNsb3NlKCkgewogICAgICB0aGlzLnNob3cgPSBmYWxzZTsKICAgICAgdGhpcy5mb3JtID0ge307CiAgICB9CiAgICAsCiAgICBzZWxlY3RDaGFuZ2Uocm93cykgewogICAgICB0aGlzLnNlbGVjdFJvd3MgPSByb3dzOwogICAgfQogICAgLAogICAgZ2V0WnR4eGJtTWMoenR4eGJtKSB7CiAgICAgIGxldCByZXR1cm5TdHIgPSAiIjsgLy8g5a2Y5pS+57uT5p6cCiAgICAgIGZ1bmN0aW9uIGNoaWxkcmVuRWFjaChjaGlsZHJlbkRhdGEsIGRlcHRoKSB7CiAgICAgICAgY2hpbGRyZW5EYXRhLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICBpZiAoaXRlbS52YWx1ZSA9PT0genR4eGJtW2RlcHRoXSkgewogICAgICAgICAgICByZXR1cm5TdHIgKz0gaXRlbS5sYWJlbCArICIvIjsKICAgICAgICAgICAgZGVwdGgrKzsKICAgICAgICAgICAgaWYgKGl0ZW0uY2hpbGRyZW4pIHsKICAgICAgICAgICAgICBjaGlsZHJlbkVhY2goaXRlbS5jaGlsZHJlbiwgZGVwdGgpOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSkKCiAgICAgIH0KCiAgICAgIGNoaWxkcmVuRWFjaCh0aGlzLmZseWpPcHRpb25zLCAwKTsKICAgICAgcmV0dXJuIHJldHVyblN0ci5zdWJzdHIoMCwgcmV0dXJuU3RyLmxlbmd0aCAtIDEpOwogICAgfQogICAgLAoKICAgIGdldFp0eHhibVN5TWMoenR4eGJtKSB7CiAgICAgIGxldCBmaWx0ZXIgPSB0aGlzLnN5eG1PcHRpb25zLmZpbHRlcihpdGVtID0+IGl0ZW0udmFsdWUgPT09IHp0eHhibSk7CiAgICAgIGlmIChmaWx0ZXIubGVuZ3RoID4gMCkgewogICAgICAgIHJldHVybiBmaWx0ZXJbMF0ubGFiZWwKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gIiIKICAgICAgfQogICAgfQogICAgLAogIH0sCn07Cg=="}, {"version": 3, "sources": ["ztxxsjglpz.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ztxxsjglpz.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row :gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card class=\"aside_height\" shadow=\"never\">\n          <div slot=\"header\" class=\"clearfix\" style=\"text-align: center\">\n            <span>评价设备</span>\n          </div>\n          <div style=\"overflow: auto; height: 84vh\">\n            <el-tree\n                highlight-current\n                :data=\"treedata\"\n                :props=\"defaultProps\"\n                @node-click=\"handleNodeClick\"\n            ></el-tree>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"20\">\n        <el-white>\n          <!--          <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\" type=\"card\">-->\n          <!--            <el-tab-pane label=\"缺陷\" name=\"qx\">-->\n          <!--            </el-tab-pane>-->\n          <!--            <el-tab-pane label=\"试验\" name=\"sy\">-->\n          <!--            </el-tab-pane>-->\n          <!--          </el-tabs>-->\n          <el-white class=\"button-group\">\n            <el-button\n                type=\"primary\"\n                icon=\"el-icon-plus\"\n                @click=\"getInster\"\n                :disabled=\"addDisabled\"\n            >新增\n            </el-button\n            >\n            <el-button\n                type=\"danger\"\n                icon=\"el-icon-delete\"\n                @click=\"deleteRow\"\n            >删除\n            </el-button\n            >\n          </el-white>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"selectChange\"\n            height=\"78vh\"\n          />\n        </el-white>\n        <!--新增、修改、详情弹框-->\n        <el-dialog\n            :title=\"title\"\n            v-dialogDrag\n            :visible.sync=\"show\"\n            width=\"50%\"\n            append-to-body\n            @close=\"getInsterClose\"\n        >\n          <el-form label-width=\"130px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n            <el-row :gutter=\"24\" class=\"pull-left\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"评价设备类型：\" prop=\"sblxmc\">\n                  <el-input v-model=\"form.sblxmc\" disabled/>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"部件名称：\" prop=\"sbbjmc\">\n                  <el-input v-model=\"form.sbbjmc\" disabled/>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"状态量名称：\" prop=\"ztlmc\">\n                  <el-input v-model=\"form.ztlmc\" disabled/>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"状态量信息点：\" prop=\"ztlxxdmc\">\n                  <el-input v-model=\"form.ztlxxdmc\" disabled/>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row :gutter=\"24\">\n              <el-col :span=\"24\">\n                <el-form-item label=\"关联分类依据：\" prop=\"ztxxbm\" v-if=\"form.xxlx==='qx'\">\n                  <el-cascader ref=\"myCascader\" :options=\"flyjOptions\" v-model=\"form.ztxxbm\" style=\"width: 100%\"\n                               filterable\n                               clearable></el-cascader>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row :gutter=\"24\">\n              <el-col :span=\"24\">\n                <el-form-item label=\"关联试验项目：\" prop=\"ztxxbm\" v-if=\"form.xxlx==='sy'\">\n                  <el-select v-model=\"form.ztxxbm\" style=\"width: 100%\" filterable clearable>\n                    <el-option\n                        v-for=\"item in syxmOptions\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\">\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row :gutter=\"24\">\n              <el-col :span=\"24\">\n                <el-form-item label=\"处理规则：\" prop=\"clgz\" v-if=\"form.xxlx==='sy'\">\n                  <el-input  type=\"textarea\" v-model=\"form.clgz\" v-on:click.native=\"textareaClick\"/>\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </el-form>\n          <div slot=\"footer\" class=\"dialog-footer\">\n            <el-button @click=\"getInsterClose\">关 闭</el-button>\n            <el-button type=\"primary\" @click=\"saveRow\">保 存</el-button>\n          </div>\n        </el-dialog>\n\n        <el-dialog title=\"脚本维护\" :visible.sync=\"isShowJb\" width=\"80%\" append-to-body @close=\"jbClose\" v-dialogDrag>\n          <!--  脚本维护框  -->\n          <gswh @setJbVal=\"setJbVal\" :jb=\"form.clgz\" :syxm=\"form.ztxxbm\" @jbClose=\"jbClose\"></gswh>\n        </el-dialog>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n<script>\nimport {getSblxAndSbbjTree} from \"@/api/dagangOilfield/bzgl/sbztpjbzk/ztlmxwh\";\nimport {getFlyjTree, getPageList, getSybmOptions, remove, saveOrUpdate} from \"@/api/dagangOilfield/bzgl/sbztpjbzk/ztxxsjgl\";\nimport gswh from \"@/views/dagangOilfield/bzgl/sbztpjbzk/gswh\";\nexport default {\n  name: \"ztxxsjglpz\",\n  components: {gswh},\n  data() {\n    return {\n      isShowJb:false,//是否显示脚本维护框\n      syxmOptions: [],\n      flyjOptions: [],\n      //tab页名称\n      activeName: 'qx',\n      //树结构\n      defaultProps: {\n        children: \"children\",\n        label: \"label\",\n      },\n      //设备类型编码\n      sblxbm: \"\",\n      //新增按钮form表单\n      form: {},\n      title: \"\",\n      show: false,\n      filterInfo: {\n        data: {\n          ywdwArr: [],\n        },\n        fieldList: [\n          {\n            label: \"设备类型\",\n            type: \"select\",\n            value: \"roleName\",\n            multiple: true,\n            options: [],\n          },\n        ],\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true,\n        },\n        tableData: [],\n        tableHeader: [\n          {prop: \"sblxmc\", label: \"评价导则设备类型\", minWidth: '100'},\n          {prop: \"sbbjmc\", label: \"部件名称\"},\n          {prop: \"ztlmc\", label: \"状态量名称\"},\n          {prop: \"ztlxxdmc\", label: \"状态量信息点\"},\n          {prop: \"ztxxbmmc\", label: \"关联项\", minWidth: '240'},\n          {\n            prop: \"operation\",\n            label: \"操作\",\n            minWidth: \"130px\",\n            style: {display: \"block\"},\n            //操作列固定再右侧\n            fixed: \"right\",\n            operation: [\n              {name: \"修改\", clickFun: this.updateRow},\n              {name: \"详情\", clickFun: this.getInfo},\n            ],\n          },\n        ],\n      },\n      queryztlmxwhParam: {},\n      selectRows: [],\n      //点击树节点赋值\n      treeForm: {},\n      //组织树\n      treedata: [],\n      //新增按钮控制\n      addDisabled: true,\n      bjmcList: [],\n      rules: {\n        ztxxbm: [\n          {required: true, message: \"不能为空\", trigger: \"blur\"},\n        ],\n      },\n\n      isDisabled: false,\n      //选中行数据\n      rowData: {},\n      //表单选中数据\n      selectedRowDataArr: [],\n      //状态量信息点新增\n      isShowParamsAndParts: false,\n    };\n  },\n  create() {\n  },\n\n  mounted() {\n    //列表查询\n    this.getTreeNode();\n  },\n  methods: {\n    //设置脚本值\n    setJbVal(val){\n      this.form.clgz = val;\n    },\n    jbClose(){\n      this.isShowJb = false;\n    },\n    textareaClick(){\n      if (this.form.ztxxbm){\n        this.isShowJb = true;\n      }else {\n        this.$message.error(\"请先选择试验项目\")\n      }\n\n    },\n    //获取树节点数据\n    getTreeNode() {\n      getSblxAndSbbjTree({type: \"ztlxxglpz\"}).then((res) => {\n        if (res.code === \"0000\") {\n          this.treedata = res.data;\n        }\n      });\n    },\n    async getFlyjTree() {\n      if (this.sblxbm) {\n        let qxlb = '3';\n        if (this.sblxbm.indexOf(\"bd\") > -1) {\n          qxlb = '1'\n        }\n        await getFlyjTree({sblx: this.sblxbm, qxlb: qxlb}).then((res) => {\n          if (res.code === \"0000\") {\n            this.flyjOptions = res.data;\n          }\n        });\n      }\n    },\n\n    async getSybmOptions() {\n      if (this.sblxbm) {\n        await getSybmOptions({sblxbm: this.sblxbm, isMpSyxm: 1}).then((res) => {\n          if (res.code === \"0000\") {\n            this.syxmOptions = res.data;\n          }\n        });\n      }\n    },\n\n    //树节点点击事件\n    async handleNodeClick(data, node) {\n      this.addDisabled = true\n      this.queryztlmxwhParam.sblx = \"\";\n      this.queryztlmxwhParam.sbbjId = \"\";\n      this.queryztlmxwhParam.ztlzbm = \"\";\n      this.queryztlmxwhParam.ztlxxdId = \"\";\n      this.queryztlmxwhParam.xxlx = \"\";\n\n      this.treeForm.ztlxxdId = \"\";\n      this.treeForm.ztlzbm = \"\";\n      this.treeForm.sbbjId = \"\";\n      this.treeForm.sblx = \"\";\n      if (data.nodeLevel === \"1\") {\n        this.sblxbm = data.id;\n        await this.getFlyjTree()\n        await this.getSybmOptions()\n        await this.getData();\n      }\n\n      if (data.nodeLevel === \"2\") {\n        this.queryztlmxwhParam.sbbjId = data.id;\n        this.sblxbm = node.parent.data.id;\n        await this.getFlyjTree()\n        await this.getSybmOptions()\n        await this.getData();\n      }\n\n      if (data.nodeLevel === \"3\") {\n        this.queryztlmxwhParam.xxlx = data.id;\n        this.queryztlmxwhParam.sbbjId = node.parent.data.id;\n        this.sblxbm = node.parent.parent.data.id;\n        await this.getFlyjTree()\n        await this.getSybmOptions()\n        await this.getData();\n      }\n\n      if (data.nodeLevel === \"4\") {\n        this.queryztlmxwhParam.xxlx = node.parent.data.id;\n        this.queryztlmxwhParam.ztlzbm = data.id;\n        this.queryztlmxwhParam.sbbjId = node.parent.parent.data.id;\n        this.sblxbm = node.parent.parent.parent.data.id\n        await this.getFlyjTree()\n        await this.getSybmOptions()\n        await this.getData();\n      }\n\n      if (data.nodeLevel === \"5\") {\n        this.queryztlmxwhParam.xxlx = node.parent.parent.data.id;\n        this.queryztlmxwhParam.ztlxxdId = data.id;\n        this.queryztlmxwhParam.sbbjId = node.parent.parent.parent.data.id;\n        this.queryztlmxwhParam.ztlzbm = node.parent.data.id;\n        this.sblxbm = node.parent.parent.parent.parent.data.id\n        await this.getFlyjTree()\n        await this.getSybmOptions()\n        this.treeForm.ztlxxdId = data.id;\n        this.treeForm.ztlxxdmc = data.label;\n        this.treeForm.ztlzbm = node.parent.data.id;\n        this.treeForm.ztlmc = node.parent.data.label;\n        this.treeForm.xxlx = node.parent.parent.data.id;\n        this.treeForm.sbbjId = node.parent.parent.parent.data.id;\n        this.treeForm.sbbjmc = node.parent.parent.parent.data.label;\n        this.treeForm.sblx = node.parent.parent.parent.parent.data.id;\n        this.treeForm.sblxmc = node.parent.parent.parent.parent.data.label;\n        this.addDisabled = false;\n        await this.getData();\n      }\n    }\n    ,\n\n    //列表查询\n    async getData(params) {\n      try {\n        const param = {...this.queryztlmxwhParam, ...params};\n        param.sblx = this.sblxbm;\n        const {data, code} = await getPageList(param);\n        if (code === \"0000\") {\n          data.records.forEach(item => {\n            if (item.xxlx === 'qx') {\n              item.ztxxbm = JSON.parse(item.ztxxbm.replace(/'/g, \"\\\"\"));\n              item.ztxxbmmc = this.getZtxxbmMc(item.ztxxbm);\n            } else {\n              item.ztxxbmmc = this.getZtxxbmSyMc(item.ztxxbm);\n            }\n          })\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    }\n    ,\n\n    //新增\n    async getInster() {\n      if (!this.treeForm.ztlxxdId) {\n        this.$message.warning(\"请选择左侧对应状态量信息点！！！\");\n        return;\n      }\n      this.form.ztlxxdId = this.treeForm.ztlxxdId\n      this.form.ztlxxdmc = this.treeForm.ztlxxdmc\n      this.form.ztlzbm = this.treeForm.ztlzbm\n      this.form.ztlmc = this.treeForm.ztlmc\n      this.form.sbbjId = this.treeForm.sbbjId\n      this.form.sbbjmc = this.treeForm.sbbjmc\n      this.form.sblx = this.treeForm.sblx\n      this.form.sblxmc = this.treeForm.sblxmc\n      this.form.xxlx = this.treeForm.xxlx\n      this.title = \"新增\";\n      this.isDisabled = false;\n      this.show = true;\n    }\n    ,\n    //编辑按钮\n    async updateRow(row) {\n      this.title = \"状态量模型修改\";\n      this.isDisabled = false;\n      this.form = {...row};\n      this.show = true;\n    }\n    ,\n    //详情按钮\n    async getInfo(row) {\n      this.title = \"详情查看\";\n      this.form = {...row};\n      this.isDisabled = true;\n      this.show = true;\n    }\n    ,\n\n    async saveRow() {\n      await this.$refs['form'].validate(async valid => {\n        if (valid) {\n          try {\n            if (this.form.xxlx === \"qx\") {\n              let checkedNodes = this.$refs.myCascader.getCheckedNodes()[0];\n              this.form.ztxxbmId = checkedNodes.value;\n            } else {\n              this.form.ztxxbmId = this.form.ztxxbm;\n            }\n\n            let {code} = await saveOrUpdate(this.form);\n            if (code === \"0000\") {\n              this.$message.success(\"操作成功\");\n            }\n          } catch (e) {\n            console.log(e);\n          }\n          await this.getData();\n          // this.show = false;\n        }\n      })\n    }\n    ,\n    //删除按钮\n    async deleteRow() {\n      if (this.selectRows.length < 1) {\n        this.$message.warning(\"请选择正确的数据！！！\");\n        return;\n      }\n      let ids = this.selectRows.map((item) => {\n        return item.objId;\n      });\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      })\n          .then(() => {\n            remove(ids).then(({code}) => {\n              if (code === \"0000\") {\n                this.$message({\n                  type: \"success\",\n                  message: \"删除成功!\",\n                });\n                this.getData();\n              } else {\n                this.$message({\n                  type: \"error\",\n                  message: \"删除失败!\",\n                });\n              }\n            });\n          })\n          .catch(() => {\n            this.$message({\n              type: \"info\",\n              message: \"已取消删除\",\n            });\n          });\n      this.getData();\n    }\n    ,\n    //关闭弹窗\n    getInsterClose() {\n      this.show = false;\n      this.form = {};\n    }\n    ,\n    selectChange(rows) {\n      this.selectRows = rows;\n    }\n    ,\n    getZtxxbmMc(ztxxbm) {\n      let returnStr = \"\"; // 存放结果\n      function childrenEach(childrenData, depth) {\n        childrenData.forEach(item => {\n          if (item.value === ztxxbm[depth]) {\n            returnStr += item.label + \"/\";\n            depth++;\n            if (item.children) {\n              childrenEach(item.children, depth);\n            }\n          }\n        })\n\n      }\n\n      childrenEach(this.flyjOptions, 0);\n      return returnStr.substr(0, returnStr.length - 1);\n    }\n    ,\n\n    getZtxxbmSyMc(ztxxbm) {\n      let filter = this.syxmOptions.filter(item => item.value === ztxxbm);\n      if (filter.length > 0) {\n        return filter[0].label\n      } else {\n        return \"\"\n      }\n    }\n    ,\n  },\n};\n</script>\n\n<style lang='scss' scoped>\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  max-height: 79vh;\n  overflow: auto;\n}\n"]}]}