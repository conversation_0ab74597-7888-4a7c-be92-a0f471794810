{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\gqjgl\\pdgqj.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\gqjgl\\pdgqj.vue", "mtime": 1706897324701}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["pdgqj.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAoYA;;AACA;;AAWA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,UAAA,EAAA;AAAA,IAAA,SAAA,EAAA,kBAAA;AAAA,IAAA,QAAA,EAAA;AAAA,GADA;AAEA,EAAA,IAAA,EAAA,OAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA,MAAA,QAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,IADA;AAEA;AACA,MAAA,KAAA,EAAA;AACA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CADA;AAEA,QAAA,EAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAFA;AAGA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAHA;AAIA,QAAA,GAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AAJA,OAHA;AASA,MAAA,MAAA,EAAA;AACA,QAAA,IAAA,EAAA;AADA,OATA;AAYA;AACA,MAAA,UAAA,EAAA,KAbA;AAcA;AACA,MAAA,QAAA,EAAA,OAfA;AAiBA;AACA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SARA,EASA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SATA,CAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AAxBA;AAZA,OAlBA;AA0DA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,GAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA,EAFA;AAGA,UAAA,IAAA,EAAA,EAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SAHA,CAIA;AAJA;AAPA,OA3DA;AAyEA;AACA,MAAA,oBAAA,EAAA,KA1EA;AA2EA;AACA,MAAA,2BAAA,EAAA,KA5EA;AA6EA;AACA,MAAA,iBAAA,EAAA,KA9EA;AA+EA;AACA,MAAA,IAAA,EAAA,EAhFA;AAiFA,MAAA,OAAA,EAAA,EAjFA;AAkFA;AACA,MAAA,qBAAA,EAAA,KAnFA;AAoFA;AACA,MAAA,wBAAA,EAAA,KArFA;AAsFA;AACA,MAAA,IAAA,EAAA;AACA,QAAA,IAAA,EAAA;AADA,OAvFA;AA0FA,MAAA,OAAA,EAAA,KA1FA;AA2FA;AACA,MAAA,SAAA,EAAA,EA5FA;AA6FA;AACA,MAAA,SAAA,EAAA,EA9FA;AA+FA;AACA,MAAA,cAAA,EAAA,IAhGA;AAiGA,MAAA,UAAA,EAAA,IAjGA;AAkGA;AACA,MAAA,UAAA,EAAA,EAnGA;AAoGA;AACA,MAAA,iBAAA,EAAA;AACA,QAAA,UAAA,EAAA;AADA,OArGA;AAwGA;AACA,MAAA,eAAA,EAAA,EAzGA;AA2GA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,KAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA,CAFA;AAGA,QAAA,QAAA,EAAA,EAHA;AAIA,QAAA,OAAA,EAAA;AAJA,OA5GA;AAmHA;AACA,MAAA,MAAA,EAAA;AACA,QAAA,EAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,MAAA,EAAA,EAHA;AAIA,QAAA,QAAA,EAAA,EAJA;AAKA,QAAA,MAAA,EAAA,EALA;AAMA,QAAA,QAAA,EAAA,EANA;AAOA,QAAA,IAAA,EAAA,EAPA;AAQA,QAAA,QAAA,EAAA,EARA;AASA,QAAA,QAAA,EAAA,EATA;AAUA,QAAA,MAAA,EAAA;AAVA,OApHA;AAiIA,MAAA,UAAA,EAAA,KAjIA;AAmIA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,KAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA,CAFA;AAGA,QAAA,QAAA,EAAA,EAHA;AAIA,QAAA,OAAA,EAAA;AAJA,OApIA;AA0IA;AACA,MAAA,MAAA,EAAA;AACA,QAAA,EAAA,EAAA,EADA;AAEA,QAAA,MAAA,EAAA,EAFA;AAGA,QAAA,QAAA,EAAA,EAHA;AAIA,QAAA,MAAA,EAAA,EAJA;AAKA,QAAA,QAAA,EAAA,EALA;AAMA,QAAA,IAAA,EAAA,EANA;AAOA,QAAA,IAAA,EAAA,EAPA;AAQA,QAAA,MAAA,EAAA,EARA;AASA,QAAA,KAAA,EAAA;AATA,OA3IA;AAuJA;AACA,MAAA,WAAA,EAAA,EAxJA;AAyJA;AACA,MAAA,SAAA,EAAA,KA1JA;AA2JA;AACA,MAAA,YAAA,EAAA,EA5JA;AA6JA;AACA,MAAA,SAAA,EAAA,KA9JA;AA+JA;AACA,MAAA,YAAA,EAAA;AAhKA,KAAA;AAkKA,GAtKA;AAuKA,EAAA,KAAA,EAAA,EAvKA;AAwKA,EAAA,OAxKA,qBAwKA,CAEA,CA1KA;AA2KA,EAAA,OA3KA,qBA2KA;AACA,SAAA,OAAA;AACA,GA7KA;AA8KA,EAAA,OAAA,EAAA;AACA,IAAA,WADA,yBACA;AACA,iCAAA,KAAA,MAAA,EAAA,OAAA;AACA,KAHA;;AAIA;;;;AAIA,IAAA,mBARA,+BAQA,IARA,EAQA;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,IAAA,GAAA,OAAA,IAAA,GAAA,EAAA,CADA,CACA;;AACA,UAAA,CAAA,QAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,kBAAA;AACA;;AACA,UAAA,IAAA,GAAA,IAAA,CAAA,IAAA,GAAA,IAAA;AACA,KAdA;;AAeA;;;;;;AAMA,IAAA,gBArBA,4BAqBA,QArBA,EAqBA,IArBA,EAqBA,QArBA,EAqBA;AACA;AACA,WAAA,IAAA,CAAA,YAAA,GAAA,QAAA,CAAA,IAAA,CAAA,UAAA,CAFA,CAGA;;AACA,WAAA,IAAA,CAAA,cAAA,GAAA,QAAA,CAAA,IAAA,CAAA,OAAA,CAAA,WAAA;AACA,KA1BA;;AA4BA;;;;;AAKA,IAAA,mBAjCA,+BAiCA,IAjCA,EAiCA,QAjCA,EAiCA,CAEA,CAnCA;;AAoCA;;;AAGA,IAAA,mBAvCA,iCAuCA;AACA;AACA,WAAA,iBAAA,CAAA,UAAA,GAAA,qBAAA;AACA,WAAA,KAAA,CAAA,aAAA,CAAA,MAAA;AACA,KA3CA;AA6CA;AACA,IAAA,OA9CA,mBA8CA,MA9CA,EA8CA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,KAFA,+DAEA,KAAA,CAAA,MAFA,GAEA,MAFA;AAAA;AAAA,uBAGA,uBAAA,KAAA,CAHA;;AAAA;AAAA;AAGA,gBAAA,IAHA,kBAGA,IAHA;AAGA,gBAAA,IAHA,kBAGA,IAHA;;AAIA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,KAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,KAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AAPA;AAAA;;AAAA;AAAA;AAAA;AASA,gBAAA,OAAA,CAAA,GAAA;;AATA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KAzDA;AA2DA;AACA,IAAA,eA5DA,6BA4DA;AACA,WAAA,UAAA,GAAA,KAAA,CADA,CAEA;;AACA,WAAA,iBAAA,GAAA,IAAA,CAHA,CAIA;;AACA,WAAA,QAAA,GAAA,OAAA,CALA,CAMA;;AACA,WAAA,IAAA,GAAA;AACA,QAAA,IAAA,EAAA;AADA,OAAA;AAGA,KAtEA;AAuEA;AACA,IAAA,UAxEA,sBAwEA,GAxEA,EAwEA;AACA;AACA,WAAA,iBAAA,GAAA,IAAA,CAFA,CAGA;;AACA,WAAA,QAAA,GAAA,OAAA,CAJA,CAKA;;AACA,WAAA,UAAA,GAAA,IAAA,CANA,CAOA;;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,KAjFA;AAkFA;AACA,IAAA,aAnFA,yBAmFA,GAnFA,EAmFA;AACA;AACA,WAAA,iBAAA,GAAA,IAAA,CAFA,CAGA;;AACA,WAAA,QAAA,GAAA,OAAA,CAJA,CAKA;;AACA,WAAA,UAAA,GAAA,KAAA,CANA,CAOA;;AACA,WAAA,IAAA,mCAAA,GAAA;AAEA,KA7FA;AA8FA;AACA,IAAA,QA/FA,sBA+FA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,MAAA,CAAA,KAAA,CAAA,MAAA,EAAA,QAAA;AAAA,mGAAA,kBAAA,KAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA,iCACA,KADA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA,mCAGA,4BAAA,MAAA,CAAA,IAAA,CAHA;;AAAA;AAAA;AAGA,4BAAA,IAHA,uBAGA,IAHA;;AAIA,gCAAA,IAAA,KAAA,MAAA,EAAA;AACA,8BAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA;;AANA;AAAA;;AAAA;AAAA;AAAA;AAQA,4BAAA,OAAA,CAAA,GAAA;;AARA;AAUA;AACA,4BAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,4BAAA,MAAA,CAAA,OAAA;;AACA,4BAAA,MAAA,CAAA,iBAAA,GAAA,KAAA;;AAbA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAA;;AAAA;AAAA;AAAA;AAAA,oBADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA,KAhHA;AAiHA;AACA,IAAA,SAlHA,qBAkHA,EAlHA,EAkHA;AAAA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,MAAA,GAAA,CAAA,IAAA,CAAA,EAAA;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,8BAAA,GAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,cAAA,IAAA,SAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,OAAA;AACA,WANA,MAMA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAIA;AACA,SAbA;AAcA,OAnBA,EAmBA,KAnBA,CAmBA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OAxBA;AAyBA,WAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;AACA,WAAA,OAAA;AACA,KAvJA;AAyJA;AACA,IAAA,mBA1JA,+BA0JA,GA1JA,EA0JA;AACA,WAAA,YAAA,GAAA,EAAA;AACA,WAAA,WAAA,GAAA,GAAA;AACA,WAAA,WAAA,CAAA,KAAA,GAAA,GAAA,CAAA,KAAA;AACA,WAAA,qBAAA,GAAA,IAAA;AACA,WAAA,WAAA;AACA,KAhKA;AAkKA;AACA,IAAA,mBAnKA,+BAmKA,GAnKA,EAmKA;AACA,WAAA,WAAA,GAAA,GAAA;AACA,WAAA,WAAA,CAAA,KAAA,GAAA,GAAA,CAAA,KAAA;AACA,WAAA,oBAAA,GAAA,IAAA;AACA,WAAA,YAAA;AACA,KAxKA;AAyKA;AACA,IAAA,WA1KA,yBA0KA;AACA,WAAA,MAAA,GAAA,KAAA,QAAA,CAAA,IAAA,GAAA,MAAA;AACA,WAAA,MAAA,CAAA,KAAA,GAAA,KAAA,WAAA,CAAA,KAAA;AACA,WAAA,2BAAA,GAAA,IAAA;AACA,KA9KA;AA+KA,IAAA,QA/KA,oBA+KA,GA/KA,EA+KA;AACA,WAAA,MAAA,GAAA,GAAA;AACA,WAAA,2BAAA,GAAA,IAAA;AACA,KAlLA;AAmLA;AACA,IAAA,WApLA,yBAoLA;AACA,WAAA,MAAA,GAAA,KAAA,QAAA,CAAA,IAAA,GAAA,MAAA;AACA,WAAA,MAAA,CAAA,KAAA,GAAA,KAAA,WAAA,CAAA,KAAA;AACA,WAAA,wBAAA,GAAA,IAAA;AACA,KAxLA;AAyLA,IAAA,QAzLA,oBAyLA,GAzLA,EAyLA;AACA,WAAA,MAAA,GAAA,GAAA;AACA,WAAA,wBAAA,GAAA,IAAA;AACA,KA5LA;AA6LA;AACA,IAAA,gBA9LA,8BA8LA,CAEA,CAhMA;AAiMA;AACA,IAAA,mBAlMA,iCAkMA,CAEA,CApMA;AAqMA;AACA,IAAA,eAtMA,6BAsMA,CAEA,CAxMA;AA0MA,IAAA,WA1MA,yBA0MA,CAEA,CA5MA;AA6MA;AACA,IAAA,YA9MA,wBA8MA,IA9MA,EA8MA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KAhNA;AAkNA;AACA,IAAA,WAnNA,yBAmNA;AAAA;;AACA,WAAA,SAAA,GAAA,IAAA;AACA,oCAAA,KAAA,WAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,MAAA,CAAA,WAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,MAAA,CAAA,SAAA,GAAA,KAAA;AACA,OAJA;AAKA,KA1NA;AA4NA;AACA,IAAA,cA7NA,4BA6NA;AAAA;;AACA,6CAAA,KAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,WAAA;;AACA,QAAA,MAAA,CAAA,wBAAA,GAAA,KAAA;AACA,OAHA;AAIA,KAlOA;AAmOA;AACA,IAAA,UApOA,wBAoOA;AAAA;;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,YAAA,GAAA,GAAA,EAAA;;AACA,QAAA,MAAA,CAAA,YAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA,SAFA;;AAGA,yCAAA,GAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,cAAA,IAAA,SAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAKA,WANA,MAMA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAIA;;AACA,UAAA,MAAA,CAAA,WAAA;AACA,SAdA;AAeA,OAxBA,EAwBA,KAxBA,CAwBA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OA7BA;AA8BA,KAnQA;AAqQA;AACA,IAAA,YAtQA,0BAsQA;AAAA;;AACA,WAAA,SAAA,GAAA,IAAA;AACA,0CAAA,KAAA,WAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,MAAA,CAAA,WAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,MAAA,CAAA,SAAA,GAAA,KAAA;AACA,OAJA;AAKA,KA7QA;AA8QA;AACA,IAAA,cA/QA,4BA+QA;AAAA;;AACA,mDAAA,KAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,YAAA;;AACA,QAAA,MAAA,CAAA,2BAAA,GAAA,KAAA;AACA,OAHA;AAIA,KApRA;AAqRA,IAAA,YArRA,0BAqRA;AAAA;;AAEA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,YAAA,GAAA,GAAA,EAAA;;AACA,QAAA,MAAA,CAAA,YAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA,SAFA;;AAGA,+CAAA,GAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,cAAA,IAAA,SAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAKA,WANA,MAMA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAIA;;AACA,UAAA,MAAA,CAAA,YAAA;AACA,SAdA;AAeA,OAxBA,EAwBA,KAxBA,CAwBA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OA7BA;AA8BA,KArTA;AAuTA,IAAA,UAvTA,sBAuTA,IAvTA,EAuTA;AACA,WAAA,KAAA,CAAA,OAAA,CAAA,kBAAA,CAAA,IAAA;AACA,KAzTA;AA0TA,IAAA,eA1TA,2BA0TA,GA1TA,EA0TA;AACA,WAAA,YAAA,GAAA,GAAA;AACA,KA5TA;AA6TA,IAAA,UA7TA,sBA6TA,IA7TA,EA6TA;AACA,WAAA,KAAA,CAAA,OAAA,CAAA,kBAAA,CAAA,IAAA;AACA,KA/TA;AAgUA,IAAA,eAhUA,2BAgUA,GAhUA,EAgUA;AACA,WAAA,YAAA,GAAA,GAAA;AACA;AAlUA;AA9KA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      ref=\"filter1\"\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      @handleReset=\"filterReset\"\n    />\n    <el-white class=\"button-group1\">\n      <div class=\"button_btn\">\n        <el-button @click=\"addSensorButton\" icon=\"el-icon-plus\" v-hasPermi=\"['pdgql:button:add']\"  type=\"primary\">新增\n        </el-button>\n        <el-button type=\"primary\" icon=\"el-icon-download\" @click=\"exportExcel()\">导出</el-button>\n      </div>\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"selectChange\"\n        height=\"67vh\"\n        >\n      <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\" width=\"180\" :resizable=\"false\">\n        <template slot-scope=\"scope\">\n           <el-button  type=\"text\" size=\"small\" @click=\"getGqjInfo(scope.row)\" class=\"el-icon-view\" title=\"详情\"></el-button>\n           <el-button  type=\"text\" size=\"small\" v-if=\"scope.row.createBy==currUser\"  @click=\"updateGqjInfo(scope.row)\" class='el-icon-edit' title=\"编辑\"></el-button>\n           <el-button  type=\"text\" size=\"small\" v-if=\"scope.row.createBy==currUser\"  @click=\"deleteRow(scope.row.objId)\" class=\"el-icon-delete\" title=\"删除\"></el-button>\n        </template>\n      </el-table-column>\n      </comp-table>\n    </el-white>\n    <!-- 工器具台账详情修改新增 -->\n    <el-dialog :title=\"gqjTital\" :visible.sync=\"dialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"60%\" v-dialogDrag>\n      <el-form :model=\"form\" label-width=\"80px\" :disabled=\"isDisabled\" :rules=\"rules\" ref=\"form\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"名称\" prop=\"sbmc\">\n              <el-input v-model=\"form.sbmc\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"数量\" prop=\"sl\">\n              <el-input-number :min=\"1\" v-model=\"form.sl\" :disabled=\"isDisabled\"></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"管理单位\" prop=\"ssgs\">\n              <el-input v-model=\"form.ssgs\" placeholder=\"\" :disabled=\"isDisabled\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"负责人\" prop=\"fzr\">\n              <el-input v-model=\"form.fzr\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"存放地点\">\n              <el-input v-model=\"form.cfdd\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"使用情况\">\n              <el-input v-model=\"form.sytj\" placeholder=\"\" :disabled=\"isDisabled\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"检验时间\">\n              <el-date-picker\n                  v-model=\"form.jysj\"\n                  type=\"date\"\n                  value-format=\"yyyy-MM-dd\"\n                  placeholder=\"选择日期\"\n                  :disabled=\"isDisabled\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"出厂日期\" class=\"add_sy_tyrq\">\n              <el-date-picker\n                  v-model=\"form.ccrq\"\n                  type=\"date\"\n                  value-format=\"yyyy-MM-dd\"\n                  placeholder=\"选择日期\"\n                  :disabled=\"isDisabled\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"购入日期\" class=\"add_sy_tyrq\">\n              <el-date-picker\n                  v-model=\"form.grrq\"\n                  type=\"date\"\n                  value-format=\"yyyy-MM-dd\"\n                  placeholder=\"选择日期\"\n                  :disabled=\"isDisabled\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"有效期\">\n              <el-input v-model=\"form.yxq\" placeholder=\"\" :disabled=\"isDisabled\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"有效期限\" class=\"add_sy_tyrq\">\n              <el-date-picker\n                  v-model=\"form.yxqx\"\n                  type=\"date\"\n                  value-format=\"yyyy-MM-dd\"\n                  placeholder=\"选择日期\"\n                  :disabled=\"isDisabled\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"校验单位\">\n              <el-input v-model=\"form.jydw\" placeholder=\"\" :disabled=\"isDisabled\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"校验结果\">\n              <el-input v-model=\"form.jyjg\" placeholder=\"\" :disabled=\"isDisabled\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"截至日期\" class=\"add_sy_tyrq\">\n              <el-date-picker\n                  v-model=\"form.jzrq\"\n                  type=\"date\"\n                  value-format=\"yyyy-MM-dd\"\n                  placeholder=\"选择日期\"\n                  :disabled=\"isDisabled\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\">\n              <el-input type=\"textarea\" v-model=\"form.bz\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"qxcommit\" v-if=\"gqjTital === '工器具新增' || gqjTital === '工器具修改' \" class=\"pmyBtn\">\n          确 定\n        </el-button>\n      </div>\n    </el-dialog>\n    <!--试验报告弹出框-->\n    <el-dialog title=\"试验报告记录\" :visible.sync=\"sybgDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"60%\">\n      <el-form :model=\"form\" label-width=\"80px\">\n        <el-form-item style=\"float: right\">\n          <el-button class=\"mb8\" @click=\"addSyButton\" type=\"primary\" icon=\"el-icon-plus\"\n                     v-hasPermi=\"['gqjgl:button:sydialog:insert']\">添加\n          </el-button>\n          <el-button class=\"mb8\" @click=\"deleteYxSy\" type=\"danger\" icon=\"el-icon-delete\" v-hasPermi=\"['gqjgl:button:sydialog:delete']\">删除\n          </el-button>\n        </el-form-item>\n      </el-form>\n      <el-table\n        ref=\"syTable\"\n        stripe\n        border\n        v-loading=\"syLoading\"\n        :data=\"gqjsyList\"\n        @row-click=\"syRowClick\"\n        @selection-change=\"syCurrentChange\"\n        max-height=\"550\">\n        <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n        <el-table-column label=\"试验单位\" align=\"center\" prop=\"sydwName\"></el-table-column>\n        <el-table-column label=\"试验人员\" align=\"center\" prop=\"syryName\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"试验结论\" align=\"center\" prop=\"syjlName\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"试验时间\" align=\"center\" prop=\"sysj\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column\n          align=\"center\"\n          label=\"操作\"\n          width=\"150\">\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" size=\"small\">下载附件</el-button>\n            <el-button type=\"text\" @click=\"updateSy(scope.row)\" size=\"small\">修改</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      <pagination\n        v-show=\"syQueryForm.total>0\"\n        :total=\"syQueryForm.total\"\n        :page.sync=\"syQueryForm.pageNum\"\n        :limit.sync=\"syQueryForm.pageSize\"\n        @pagination=\"getYxSyData\"\n      />\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"sybgDialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"sybgDialogFormVisible = false\" class=\"pmyBtn\">确 定</el-button>\n      </div>\n    </el-dialog>\n    <!--添加试验报告-->\n    <el-dialog title=\"添加试验报告\" :visible.sync=\"addSybgDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"50%\">\n      <el-form label-width=\"80px\" v-model=\"syFrom\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item hidden>\n              <el-input v-model=\"syFrom.id\"></el-input>\n              <el-input v-model=\"syFrom.gqjId\"></el-input>\n              <el-input v-model=\"syFrom.sydwId\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"试验单位\">\n<!--              <el-select v-model=\"syFrom.sydwName\" placeholder=\"\" :disabled=\"isSyDetail\"></el-select>-->\n              <el-input v-model=\"syFrom.sydwName\" placeholder=\"\" :disabled=\"isSyDetail\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item hidden label=\"试验人员id\">\n              <el-input v-model=\"syFrom.syryId\" hidden></el-input>\n            </el-form-item>\n            <el-form-item label=\"试验人员\">\n              <!--<el-select v-model=\"syFrom.syryName\" placeholder=\"\">\n              </el-select>-->\n              <el-input v-model=\"syFrom.syryName\" :disabled=\"isSyDetail\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"试验时间\" class=\"add_sy_tyrq\">\n              <el-date-picker\n                :disabled=\"isSyDetail\"\n                v-model=\"syFrom.sysj\"\n                type=\"date\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"选择日期时间\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item hidden>\n              <el-input type=\"textarea\" v-model=\"syFrom.syjlCode\" hidden :disabled=\"isSyDetail\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"试验结论\">\n              <el-input type=\"textarea\" v-model=\"syFrom.syjlName\" :disabled=\"isSyDetail\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"20\">\n            <el-form-item label=\"备注\">\n              <el-input type=\"textarea\" v-model=\"syFrom.remark\" :disabled=\"isSyDetail\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n<!--        <el-row :gutter=\"20\">-->\n<!--          <el-upload-->\n<!--            style=\"float: right\"-->\n<!--            class=\"upload-demo\"-->\n<!--            action=\"https://jsonplaceholder.typicode.com/posts/\"-->\n<!--            :on-preview=\"handlePreview\"-->\n<!--            :on-remove=\"handleRemove\"-->\n<!--            :before-remove=\"beforeRemove\"-->\n<!--            multiple-->\n<!--            :limit=\"3\"-->\n<!--            :on-exceed=\"handleExceed\"-->\n<!--            :file-list=\"fileList\">-->\n<!--            <el-button size=\"small\" type=\"primary\">点击上传</el-button>-->\n<!--            <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png文件，且不超过500kb</div>-->\n<!--          </el-upload>-->\n<!--        </el-row>-->\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"addSybgDialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"saveOrUpdateSy\" class=\"pmyBtn\">确 定</el-button>\n      </div>\n    </el-dialog>\n    <!--检修记录-->\n    <!--检修记录弹出框-->\n    <el-dialog title=\"检修维护记录\" :visible.sync=\"jwxDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"60%\">\n      <el-form :model=\"form\" label-width=\"80px\">\n        <el-form-item style=\"float: right\">\n          <el-button class=\"mb8\" @click=\"addJxButton\" type=\"primary\" icon=\"el-icon-plus\"\n                     v-hasPermi=\"['gqjgl:button:sydialog:insert']\">\n            添加\n          </el-button>\n          <el-button class=\"mb8\" @click=\"deleteJxData\" type=\"danger\" icon=\"el-icon-delete\" v-hasPermi=\"['gqjgl:button:sydialog:delete']\">删除\n          </el-button>\n        </el-form-item>\n      </el-form>\n      <el-table\n        ref=\"jxTable\"\n        stripe\n        border\n        v-loading=\"jxLoading\"\n        :data=\"gqjJxList\"\n        @row-click=\"jxRowClick\"\n        @selection-change=\"jxCurrentChange\"\n        max-height=\"550\">\n        <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n        <el-table-column label=\"检修单位\" align=\"center\" prop=\"jxdwName\"></el-table-column>\n        <el-table-column label=\"检修人员\" align=\"center\" prop=\"jxryName\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"检修结果\" align=\"center\" prop=\"jxjg\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"检修时间\" align=\"center\" prop=\"jxsj\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column\n          align=\"center\"\n          label=\"操作\"\n          width=\"150\">\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" size=\"small\">下载附件</el-button>\n            <el-button type=\"text\" @click=\"updateJx(scope.row)\" size=\"small\">修改</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      <pagination\n        v-show=\"jxQueryForm.total>0\"\n        :total=\"jxQueryForm.total\"\n        :page.sync=\"jxQueryForm.pageNum\"\n        :limit.sync=\"jxQueryForm.pageSize\"\n        @pagination=\"getJxRecords\"\n      />\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"jwxDialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"jwxDialogFormVisible = false\" class=\"pmyBtn\">确 定</el-button>\n      </div>\n    </el-dialog>\n    <!--添加检修记录-->\n    <el-dialog title=\"添加检修维护记录\" :visible.sync=\"addJwxSybgDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"50%\">\n      <el-form label-width=\"80px\" :model=\"jxForm\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item hidden>\n              <el-input v-model=\"jxForm.id\"></el-input>\n              <el-input v-model=\"jxForm.gqjId\"></el-input>\n              <el-input v-model=\"jxForm.jxdwId\"></el-input>\n              <el-input v-model=\"jxForm.jxryId\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"检修单位\">\n<!--              <el-select v-model=\"jxForm.jxdwName\" placeholder=\"\"></el-select>-->\n              <el-input v-model=\"jxForm.jxdwName\" placeholder=\"\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"检修人员\">\n<!--              <el-select v-model=\"jxForm.jxryName\" placeholder=\"\"></el-select>-->\n              <el-input v-model=\"jxForm.jxryName\" placeholder=\"\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"检修时间\" class=\"add_sy_tyrq\">\n              <el-date-picker\n                v-model=\"jxForm.jxsj\"\n                type=\"date\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"选择日期时间\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"检修结果\">\n              <el-input type=\"textarea\" v-model=\"jxForm.jxjg\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\">\n              <el-input type=\"textarea\" v-model=\"jxForm.remark\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"addJwxSybgDialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"saveOrUpdateJx\" class=\"pmyBtn\">确 定</el-button>\n      </div>\n    </el-dialog>\n    <!-- 弹出框结束 -->\n  </div>\n\n</template>\n\n<script>\nimport { getUUID } from '@/utils/ruoyi'\nimport {\n  deleteAssetGqjJxRecords,\n  deleteYxSyRecords, exportExcel,\n  getAssetGqjJxRecords,\n  getList,\n  getYxSyRecords,\n  remove,\n  saveOrUpdate,\n  saveOrUpdateAssetGqjJxRecords,\n  saveOrUpdateYxSyRecords\n} from '@/api/dagangOilfield/asset/assetGqj'\nimport CompTable from 'com/CompTable'\nimport ElFilter from 'com/ElFilter'\n\nexport default {\n    components: {CompTable, ElFilter},\n    name: \"gqjgl\",\n    data() {\n      return {\n        currUser:this.$store.getters.name,\n        // 表单校验\n        rules: {\n          ssgs: [{ required: true, message: '请选择', trigger: 'blur' }],\n          sl: [{ required: true, message: '请选择', trigger: 'blur' }],\n          sbmc: [{ required: true, message: '请输入', trigger: 'blur' }],\n          fzr: [{ required: true, message: '请输入', trigger: 'blur' }],\n        },\n        params:{\n          type:\"pd\"\n        },\n        //工器具详情框字段控制\n        isDisabled: false,\n        //工器具弹出框表头\n        gqjTital: \"工器具新增\",\n\n        //表格内容\n        tableAndPageInfo: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n            {prop: 'sbmc', label: '名称', minWidth: '180'},\n            {prop: 'ssgs', label: '管理单位', minWidth: '120'},\n            {prop: 'sl', label: '数量', minWidth: '120'},\n            {prop: 'fzr', label: '负责人', minWidth: '120'},\n            {prop: 'cfdd', label: '存放地点', minWidth: '250'},\n            {prop: 'sytj', label: '使用情况', minWidth: '120'},\n            {prop: 'jysj', label: '校验时间', minWidth: '120'},\n            {prop: 'ccrq', label: '出厂日期', minWidth: '120'},\n            {prop: 'grrq', label: '购入日期', minWidth: '120'},\n            // {\n            //   fixed: \"right\",\n            //   prop: 'operation',\n            //   label: '操作',\n            //   minWidth: '150px',\n            //   style: {display: 'block'},\n            //   operation: [\n            //     // {name: '试验', clickFun: this.handleSearchSYClick},\n            //     // {name: '检修', clickFun: this.handleSerchJWXClick},\n            //     {name: '修改', clickFun: this.updateGqjInfo},\n            //     {name: '详情', clickFun: this.getGqjInfo},\n\n            //   ],\n\n            // },\n\n          ]\n        },\n        //筛选条件\n        filterInfo: {\n          data: {\n            fzr: '',\n            ssgs: '',\n            yxbz: '',\n            phone: '',\n          },\n          fieldList: [\n            {label: '名称', type: 'input', value: 'sbmc'},\n            {label: '负责人', type: 'input', value: 'fzr'},\n            {value: 'cfdd', label: '存放地点', type: 'input'},\n            // {label: '投运日期', type: 'date', value: 'tyrqArr',dateType: 'daterange',format: 'yyyy-MM-dd'},\n          ]\n        },\n        //检修记录弹出框\n        jwxDialogFormVisible: false,\n        //添加检修记录弹出框\n        addJwxSybgDialogFormVisible: false,\n        //工器具弹出框\n        dialogFormVisible: false,\n        //试验时间\n        sysj: '',\n        fildtps: [],\n        //试验弹出框\n        sybgDialogFormVisible: false,\n        //添加试验报告\n        addSybgDialogFormVisible: false,\n        //弹出框表单\n        form: {\n          type:\"pd\"\n        },\n        loading: false,\n        //工器具试验数据集合\n        gqjsyList: [],\n        //检修数据集合\n        gqjJxList:[],\n        //删除是否可用\n        multipleSensor: true,\n        showSearch: true,\n        //删除选择列\n        selectRows: [],\n        //工器具文件上传参数\n        gqjInfoUploadData: {\n          businessId: undefined,\n        },\n        //工器具文件上传请求头\n        gqjInfoUpHeader: {},\n\n        //试验查询条件\n        syQueryForm: {\n          gqjId: '',\n          total: 0,\n          pageSize: 10,\n          pageNum: 1\n        },\n\n        //试验新增表单数据\n        syFrom: {\n          id: '',\n          gqjId: '',\n          sydwId: '',\n          sydwName: '',\n          syryId: '',\n          syryName: '',\n          sysj: '',\n          syjlCode: '',\n          syjlName: '',\n          remark: ''\n        },\n\n        isSyDetail:false,\n\n        //检修查询条件\n        jxQueryForm: {\n          gqjId: '',\n          total: 0,\n          pageSize: 10,\n          pageNum: 1\n        },\n        //检修表单\n        jxForm: {\n          id: '',\n          jxdwId: '',\n          jxdwName: '',\n          jxryId: '',\n          jxryName: '',\n          jxjg: '',\n          jxsj: '',\n          remark: '',\n          gqjId: ''\n        },\n\n        //主表选中行数据\n        mainRowData: {},\n        //试验table加载\n        syLoading: false,\n        //试验选中行\n        sySelectRows: [],\n        //检修table加载\n        jxLoading: false,\n        //检修选中行\n        jxSelectRows: []\n      };\n    },\n    watch: {},\n    created() {\n\n    },\n    mounted() {\n      this.getData();\n    },\n    methods: {\n      exportExcel(){\n        exportExcel(this.params,'配电工器具')\n      },\n      /**\n       * 上传附附件之前的处理函数\n       * @param file\n       */\n      gqjInfoBeforeUpload(file) {\n        const fileSize = file.size < 1024 * 1024 * 50 //10M\n        if (!fileSize) {\n          this.$message.error('上传文件大小不能超过 50MB!')\n        }\n        let size = file.size / 1024\n      },\n      /**\n       * 上传附件成功调用的函数\n       * @param response\n       * @param file\n       * @param fileList\n       */\n      gqjInfoonSuccess(response, file, fileList) {\n        //文件id\n        this.form.attachmentid = response.data.businessId\n        //文件名称\n        this.form.attachmentname = response.data.sysFile.fileOldName\n      },\n\n      /**\n       * 移除文件\n       * @param file\n       * @param fileList\n       */\n      gqjInfohandleRemove(file, fileList) {\n\n      },\n      /**\n       * 工器具上传文件到服务器\n       */\n      gqjInfoSubmitUpload(){\n        debugger\n        this.gqjInfoUploadData.businessId = getUUID()\n        this.$refs.uploadGqjInfo.submit();\n      },\n\n      //工器具列表查询\n      async getData(params) {\n        try {\n          const param = {...this.params, ...params}\n          const {data, code} = await getList(param);\n          if (code === '0000') {\n            this.tableAndPageInfo.tableData = data.records\n            this.tableAndPageInfo.pager.total = data.total\n          }\n        } catch (e) {\n          console.log(e)\n        }\n      },\n\n      //工器具列表新增按钮\n      addSensorButton() {\n        this.isDisabled = false;\n        //打开弹出框\n        this.dialogFormVisible = true;\n        //设置弹出框表头\n        this.gqjTital = \"工器具新增\";\n        //清空弹出框内容\n        this.form = {\n          type:\"pd\"\n        };\n      },\n      //工器具列表详情按钮\n      getGqjInfo(row) {\n        //打开弹出框\n        this.dialogFormVisible = true;\n        //设置弹出框表头\n        this.gqjTital = \"工器具详情\";\n        //禁用所有输入框\n        this.isDisabled = true;\n        //给弹出框赋值\n        this.form = {...row}\n      },\n      //工器具修改按钮\n      updateGqjInfo(row) {\n        //打开弹出框\n        this.dialogFormVisible = true;\n        //设置弹出框表头\n        this.gqjTital = \"工器具修改\";\n        //开启弹出框内输入框编辑权限\n        this.isDisabled = false;\n        //给弹出框内赋值\n        this.form = {...row};\n\n      },\n      //工器具列表新增修改保存\n      async qxcommit() {\n        await this.$refs['form'].validate(async (valid) => {\n          if (valid) {\n            try {\n              let {code} = await saveOrUpdate(this.form)\n              if (code === '0000') {\n                this.$message.success(\"操作成功\")\n              }\n            } catch (e) {\n              console.log(e)\n            }\n            //恢复分页\n            this.tableAndPageInfo.pager.pageResize = 'Y';\n            this.getData();\n            this.dialogFormVisible = false;\n          }\n        })\n      },\n      //删除工器具列表\n      deleteRow(id) {\n        // if (this.selectRows.length < 1) {\n        //   this.$message.warning(\"请选择正确的数据！！！\")\n        //   return\n        // }\n        // let ids = this.selectRows.map(item => {\n        //   return item.objId\n        // });\n        let obj=[];\n        obj.push(id);\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove(obj).then(({code}) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              });\n              this.getData()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              });\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          });\n        });\n        this.tableAndPageInfo.pager.pageResize = 'Y';\n        this.getData()\n      },\n\n      //查看试验\n      handleSearchSYClick(row) {\n        this.sySelectRows = []\n        this.mainRowData = row\n        this.syQueryForm.gqjId = row.objId\n        this.sybgDialogFormVisible = true\n        this.getYxSyData()\n      },\n\n      //查看检修\n      handleSerchJWXClick(row) {\n        this.mainRowData = row\n        this.jxQueryForm.gqjId = row.objId\n        this.jwxDialogFormVisible = true\n        this.getJxRecords()\n      },\n      //添加检修\n      addJxButton() {\n        this.jxForm = this.$options.data().jxForm\n        this.jxForm.gqjId = this.mainRowData.objId\n        this.addJwxSybgDialogFormVisible = true\n      },\n      updateJx(row) {\n        this.jxForm = row\n        this.addJwxSybgDialogFormVisible = true\n      },\n      //添加试验\n      addSyButton() {\n        this.syFrom = this.$options.data().syFrom\n        this.syFrom.gqjId = this.mainRowData.objId\n        this.addSybgDialogFormVisible = true\n      },\n      updateSy(row) {\n        this.syFrom = row\n        this.addSybgDialogFormVisible = true\n      },\n      //每页展示数量点击事件\n      handleSizeChange() {\n\n      },\n      //页码改变事件\n      handleCurrentChange() {\n\n      },\n      //树点击事件\n      handleNodeClick() {\n\n      },\n\n      filterReset() {\n\n      },\n      //选择每一行\n      selectChange(rows) {\n        this.selectRows = rows\n      },\n\n      //获取试验记录数据\n      getYxSyData() {\n        this.syLoading = true\n        getYxSyRecords(this.syQueryForm).then(res => {\n          this.gqjsyList = res.data.records\n          this.syQueryForm.total = res.data.total\n          this.syLoading = false\n        })\n      },\n\n      //新增修改试验记录数据\n      saveOrUpdateSy() {\n        saveOrUpdateYxSyRecords(this.syFrom).then(res => {\n          this.getYxSyData()\n          this.addSybgDialogFormVisible = false\n        })\n      },\n      //批量删除试验数据\n      deleteYxSy() {\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          let ids = []\n          this.sySelectRows.forEach(item => {\n            ids.push(item.id)\n          })\n          deleteYxSyRecords(ids).then(({ code }) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n            }\n            this.getYxSyData()\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n\n      //获取检修记录数据\n      getJxRecords() {\n        this.jxLoading = true\n        getAssetGqjJxRecords(this.jxQueryForm).then(res => {\n          this.gqjJxList = res.data.records\n          this.jxQueryForm.total = res.data.total\n          this.jxLoading = false\n        })\n      },\n      //新增修改检修记录数据\n      saveOrUpdateJx() {\n        saveOrUpdateAssetGqjJxRecords(this.jxForm).then(res => {\n          this.getJxRecords()\n          this.addJwxSybgDialogFormVisible = false\n        })\n      },\n      deleteJxData() {\n\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          let ids = []\n          this.jxSelectRows.forEach(item => {\n            ids.push(item.id)\n          })\n          deleteAssetGqjJxRecords(ids).then(({ code }) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n            }\n            this.getJxRecords()\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n\n      syRowClick(rows) {\n        this.$refs.syTable.toggleRowSelection(rows)\n      },\n      syCurrentChange(val) {\n        this.sySelectRows = val\n      },\n      jxRowClick(rows) {\n        this.$refs.jxTable.toggleRowSelection(rows)\n      },\n      jxCurrentChange(val) {\n        this.jxSelectRows = val\n      }\n    }\n  };\n</script>\n\n<style lang='scss' scoped>\n\n  .button-group {\n    padding-left: 30px;\n    padding-right: 30px;\n    display: flex;\n    justify-content: flex-end;\n  }\n\n  .qxlr_dialog_insert {\n    margin-top: 6vh !important\n  }\n\n  /*/deep/ .qxlr_dialog_insert .el-input--medium .el-input__inner{*/\n  /*  width: 100%;*/\n  /*}*/\n  .el-select {\n    width: 100%;\n  }\n\n  .el-date-editor {\n    width: 100%;\n  }\n</style>\n"], "sourceRoot": "src/views/dagangOilfield/dwzygl/gqjgl"}]}