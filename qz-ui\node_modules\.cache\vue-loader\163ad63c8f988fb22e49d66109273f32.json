{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\lpbzk\\gzpszrwh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\lpbzk\\gzpszrwh.vue", "mtime": 1706897322614}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgZ2V0UGFnZURhdGFMaXN0LAogIHNhdmVPclVwZGF0ZSwKICByZW1vdmUsCiAgZ2V0VHJlZURhdGEsCiAgZ2V0T3JnYW5hdGlvblNlbGVjdERhdGEsCiAgZ2V0VXNlckxpc3RCeURlcHRJZAp9IGZyb20gIkAvYXBpL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvbHBiemsvZ3pwc3pyd2giOwppbXBvcnQgeyBnZXRCZHpTZWxlY3RMaXN0IH0gZnJvbSAiQC9hcGkveXhnbC9iZHl4Z2wvYmR4anpxcHoiOwppbXBvcnQgeyBnZXRTYmx4RGF0YUxpc3RTZWxlY3RlZCB9IGZyb20gIkAvYXBpL2RhZ2FuZ09pbGZpZWxkL2Fzc2V0L2Jkc2J0eiI7CmltcG9ydCB7IGdldFBkc1RyZWVMaXN0IH0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYXNzZXQvcGRnIjsKaW1wb3J0IHsgbGlzdERlcHQsIGRlcHRUcmVlc2VsZWN0IH0gZnJvbSAiQC9hcGkvc3lzdGVtL2RlcHQiOwppbXBvcnQgdHJlZXNlbGVjdCBmcm9tICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdCI7CmltcG9ydCAiQHJpb3BoYWUvdnVlLXRyZWVzZWxlY3QvZGlzdC92dWUtdHJlZXNlbGVjdC5jc3MiOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJnenBzenJ3aCIsCiAgLy9jb21wb25lbnRzOiB7dHJlZXNlbGVjdH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICBpZHM6IFtdLAogICAgICBydWxlczogewogICAgICAgIGR3OiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgICBtZXNzYWdlOiAi6K+36YCJ5oup5Y2V5L2NIiwKICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgICB9CiAgICAgICAgXSwKICAgICAgICBwZXJzb25JZDogWwogICAgICAgICAgewogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgICAgbWVzc2FnZTogIuivt+mAieaLqeS6uuWRmCIsCiAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgICAgfQogICAgICAgIF0sCiAgICAgICAgcnlseDogWwogICAgICAgICAgewogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgICAgbWVzc2FnZTogIuivt+mAieaLqeS6uuWRmOexu+WeiyIsCiAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgICAgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgZm9ybToge30sCiAgICAgIGlzU2hvd0RldGFpbHM6IGZhbHNlLAogICAgICB0aXRsZTogIiIsCiAgICAgIGZpbHRlckluZm86IHsKICAgICAgICBkYXRhOiB7CiAgICAgICAgICBkdzogIiIsCiAgICAgICAgICByeWx4OiAiIiwKICAgICAgICAgIHBlcnNvbk5hbWU6ICIiCiAgICAgICAgfSwKICAgICAgICBmaWVsZExpc3Q6IFsKICAgICAgICAgIC8vIHtsYWJlbDogJ+WFrOWPuOWQjeensCcsIHR5cGU6ICdzZWxlY3QnLCB2YWx1ZTogJ2R3Jywgb3B0aW9uczogW10sY2xlYXJhYmxlOiB0cnVlfSwKICAgICAgICAgIHsgbGFiZWw6ICLkurrlkZjlkI3np7AiLCB0eXBlOiAiaW5wdXQiLCB2YWx1ZTogInBlcnNvbk5hbWUiIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi5Lq65ZGY57G75Z6LIiwKICAgICAgICAgICAgdHlwZTogImNoZWNrYm94IiwKICAgICAgICAgICAgY2hlY2tib3hWYWx1ZTogW10sCiAgICAgICAgICAgIHZhbHVlOiAicnlseCIsCiAgICAgICAgICAgIG9wdGlvbnM6IFsKICAgICAgICAgICAgICB7IGxhYmVsOiAi6LSf6LSj5Lq6IiwgdmFsdWU6ICLotJ/otKPkuroiIH0sCiAgICAgICAgICAgICAgeyBsYWJlbDogIuetvuWPkeS6uiIsIHZhbHVlOiAi562+5Y+R5Lq6IiB9LAogICAgICAgICAgICAgIHsgbGFiZWw6ICLorrjlj6/kuroiLCB2YWx1ZTogIuiuuOWPr+S6uiIgfQogICAgICAgICAgICBdLAogICAgICAgICAgICBjbGVhcmFibGU6IHRydWUKICAgICAgICAgIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIHRhYmxlQW5kUGFnZUluZm86IHsKICAgICAgICBwYWdlcjogewogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMDBdCiAgICAgICAgfSwKICAgICAgICB0YWJsZURhdGE6IFtdLAogICAgICAgIHRhYmxlSGVhZGVyOiBbCiAgICAgICAgICB7IGxhYmVsOiAi5Y2V5L2N5ZCN56ewIiwgcHJvcDogImR3bWMiLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLkurrlkZjnsbvlnosiLCBwcm9wOiAicnlseCIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBsYWJlbDogIuS6uuWRmOWQjeensCIsIHByb3A6ICJwZXJzb25OYW1lIiwgbWluV2lkdGg6ICIxMjAiIH0KICAgICAgICBdLAogICAgICAgIG9wdGlvbjogeyBjaGVja0JveDogdHJ1ZSwgc2VyaWFsTnVtYmVyOiB0cnVlIH0KICAgICAgfSwKICAgICAgLy/nu4Tnu4fmoJEKICAgICAgdHJlZU9wdGlvbnM6IFtdLAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIGR3OiB1bmRlZmluZWQsCiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAKICAgICAgfSwKICAgICAgaXNEaXNhYmxlZDogZmFsc2UsCiAgICAgIGlzU2hvdzogdHJ1ZSwKICAgICAgLy/nu4Tnu4fnu5PmnoTkuIvmi4nmlbDmja4KICAgICAgb3B0aW9uc1NlbGVjdGVkRGF0YTogW10sCiAgICAgIHBlcnNvbkxpc3Q6IFtdCiAgICAgIC8vIC8vIOmDqOmXqOWQjeensAogICAgICAvLyBkZXB0TmFtZTogJycsCiAgICAgIC8vIC8vIOmDqOmXqOagkemAiemhuQogICAgICAvLyBkZXB0T3B0aW9uczogW10sCiAgICAgIC8vIGRlZmF1bHRQcm9wczogewogICAgICAvLyAgIGNoaWxkcmVuOiAiY2hpbGRyZW4iLAogICAgICAvLyAgIGxhYmVsOiAibGFiZWwiLAogICAgICAvLyB9LAogICAgICAvLyBzZWxlY3REZXB0T3B0aW9uczpbXSwKICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXREYXRhKCk7CiAgICB0aGlzLmdldFRyZWVPcHRpb25zKCk7CiAgICB0aGlzLmdldE9yZ2FuYXRpb25TZWxlY3REYXRhKCk7CiAgICAvLyB0aGlzLmdldFRyZWVzZWxlY3QoKTsKICAgIC8vIHRoaXMuZ2V0VHJlZXNlbGVjdE9wdGlvbnMoKTsKICB9LAogIG1vdW50ZWQoKSB7fSwKICB3YXRjaDogewogICAgaXNTaG93RGV0YWlscyh2YWwpIHsKICAgICAgaWYgKHZhbCkgewogICAgICAgIGNvbnN0IGVsID0gdGhpcy4kZWwucXVlcnlTZWxlY3RvcigiLmVsLWRpYWxvZyIpOwogICAgICAgIGVsLnN0eWxlLmxlZnQgPSAwOwogICAgICAgIGVsLnN0eWxlLnRvcCA9IDA7CiAgICAgIH0KICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDovazmjaLpg6jpl6jmlbDmja7nu5PmnoQgKi8KICAgIC8vIG5vcm1hbGl6ZXIobm9kZSkgewogICAgLy8gICBpZiAobm9kZS5jaGlsZHJlbiAmJiAhbm9kZS5jaGlsZHJlbi5sZW5ndGgpIHsKICAgIC8vICAgICBkZWxldGUgbm9kZS5jaGlsZHJlbjsKICAgIC8vICAgfQogICAgLy8gICByZXR1cm4gewogICAgLy8gICAgIGlkOiBub2RlLmRlcHRpZCwKICAgIC8vICAgICBsYWJlbDogbm9kZS5kZXB0bmFtZSwKICAgIC8vICAgICBjaGlsZHJlbjogbm9kZS5jaGlsZHJlbgogICAgLy8gICB9OwogICAgLy8gfSwKCiAgICAvL+S4i+aLieahhmNoYW5nZeS6i+S7tgogICAgaGFuZGxlRXZlbnQodmFsLCB2YWwxKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMgPSB2YWwxOwogICAgfSwKCiAgICBmaWx0ZXJSZXNldCh2YWwpIHsKICAgICAgdGhpcy5maWx0ZXJJbmZvLmZpZWxkTGlzdC5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgIGlmIChpdGVtLnR5cGUgPT09ICJjaGVja2JveCIpIHsKICAgICAgICAgIGl0ZW0uY2hlY2tib3hWYWx1ZSA9IFtdOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy/ojrflj5bnu4Tnu4fnu5PmnoTkuIvmi4nmlbDmja4KICAgIGdldE9yZ2FuYXRpb25TZWxlY3REYXRhKCkgewogICAgICBnZXRPcmdhbmF0aW9uU2VsZWN0RGF0YSgiIikudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMub3B0aW9uc1NlbGVjdGVkRGF0YSA9IHJlcy5kYXRhOwogICAgICAgIHRoaXMuZmlsdGVySW5mby5maWVsZExpc3QubWFwKGl0ZW0gPT4gewogICAgICAgICAgaWYgKGl0ZW0udmFsdWUgPT09ICJkdyIpIHsKICAgICAgICAgICAgaXRlbS5vcHRpb25zID0gdGhpcy5vcHRpb25zU2VsZWN0ZWREYXRhOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICBkZXB0U2VsZWN0Q2hhbmdlKGl0ZW0pIHsKICAgICAgdGhpcy5nZXRVc2VyTGlzdEJ5RGVwdElkKGl0ZW0pOwogICAgfSwKICAgIC8v6I635Y+W5qCR57uT5p6E5pWw5o2uCiAgICBnZXRUcmVlT3B0aW9ucygpIHsKICAgICAgZ2V0VHJlZURhdGEoIiIpLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLnRyZWVPcHRpb25zID0gcmVzLmRhdGE7CiAgICAgIH0pOwogICAgfSwKICAgIC8v6I635Y+W5qCR57uT5p6E5pWw5o2uCiAgICBnZXRVc2VyTGlzdEJ5RGVwdElkKHBhcmFtKSB7CiAgICAgIGdldFVzZXJMaXN0QnlEZXB0SWQocGFyYW0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLnBlcnNvbkxpc3QgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLy/moJHngrnlh7vkuovku7YKICAgIGhhbmRsZU5vZGVDbGljayhkYXRhKSB7CiAgICAgIGlmIChkYXRhLmxhYmVsICE9ICLmiYDlsZ7lhazlj7giKSB7CiAgICAgICAgdGhpcy5mb3JtLmR3ID0gZGF0YS5pZDsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmR3ID0gZGF0YS5pZDsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmR3ID0gIiI7CiAgICAgIH0KICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICB9LAogICAgLy/mn6Xor6LliJfooagKICAgIGdldERhdGEocGFyYW1zKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIGNvbnN0IHBhcmFtID0geyAuLi50aGlzLnF1ZXJ5UGFyYW1zLCAuLi5wYXJhbXMgfTsKICAgICAgdGhpcy5xdWVyeVBhcmFtcyA9IHBhcmFtOwogICAgICBwYXJhbS5teVNvcnRzID0gWwogICAgICAgIHsgcHJvcDogImR3bWMiLCBhc2M6IHRydWUgfSwKICAgICAgICB7IHByb3A6ICJyeWx4IiwgYXNjOiB0cnVlIH0KICAgICAgXTsKICAgICAgZ2V0UGFnZURhdGFMaXN0KHBhcmFtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnRhYmxlRGF0YSA9IHJlcy5kYXRhLnJlY29yZHM7CiAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnRvdGFsID0gcmVzLmRhdGEudG90YWw7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8v5re75Yqg5oyJ6ZKuCiAgICBhZGRTZW5zb3JCdXR0b24oKSB7CiAgICAgIHRoaXMuZm9ybSA9IHt9OwogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlOwogICAgICB0aGlzLmlzU2hvdyA9IHRydWU7CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICB0aGlzLnRpdGxlID0gIuaWsOWiniI7CiAgICB9LAogICAgLy/kv53lrZjmjInpkq4KICAgIGFzeW5jIHNhdmUoKSB7CiAgICAgIGF3YWl0IHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZShhc3luYyB2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB0cnkgewogICAgICAgICAgICBsZXQgeyBjb2RlIH0gPSBhd2FpdCBzYXZlT3JVcGRhdGUodGhpcy5mb3JtKTsKICAgICAgICAgICAgaWYgKGNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5pON5L2c5oiQ5YqfIik7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICAgICAgY29uc29sZS5sb2coZSk7CiAgICAgICAgICB9CiAgICAgICAgICAvL+mHjee9rnBhZ2XpobXku44x5byA5aeLCiAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIucGFnZVJlc2l6ZSA9ICJZIjsKICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gZmFsc2U7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvL+ihqOagvOWkmumAieahhgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLm9iaklkKTsKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOwogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7CiAgICB9LAogICAgLy/lhbPpl63lvLnnqpcKICAgIGNsb3NlKCkgewogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSBmYWxzZTsKICAgIH0sCiAgICAvL+S/ruaUueaMiemSrgogICAgdXBkYXRlSW5mbyhyb3cpIHsKICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLkiOwogICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZTsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gdHJ1ZTsKICAgICAgdGhpcy5pc1Nob3cgPSB0cnVlOwogICAgICB0aGlzLmZvcm0gPSB7IC4uLnJvdyB9OwogICAgICB0aGlzLmdldFVzZXJMaXN0QnlEZXB0SWQocm93LmR3KTsKICAgIH0sCiAgICAvL+ivpuaDheaMiemSrgogICAgZGV0YWlsc0luZm8ocm93KSB7CiAgICAgIHRoaXMudGl0bGUgPSAi6K+m5oOFIjsKICAgICAgdGhpcy5mb3JtID0geyAuLi5yb3cgfTsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gdHJ1ZTsKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gdHJ1ZTsKICAgICAgdGhpcy5pc1Nob3cgPSBmYWxzZTsKICAgICAgdGhpcy5nZXRVc2VyTGlzdEJ5RGVwdElkKHJvdy5kdyk7CiAgICB9LAogICAgLy/liKDpmaTmjInpkq4KICAgIGhhbmRsZURlbGV0ZShpZCkgewogICAgICBsZXQgb2JqID0gW107CiAgICAgIG9iai5wdXNoKGlkKTsKICAgICAgdGhpcy4kY29uZmlybSgi5q2k5pON5L2c5bCG5rC45LmF5Yig6Zmk6K+l5pWw5o2u77yM5piv5ZCm57un57ut77yfIiwgIuaPkOekuiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pCiAgICAgICAgLnRoZW4oKCkgPT4gewogICAgICAgICAgcmVtb3ZlKG9iaikudGhlbihyZXMgPT4gewogICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLAogICAgICAgICAgICAgICAgbWVzc2FnZTogIuWIoOmZpOaIkOWKn++8gSIKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICJlcnJvciIsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5Yig6Zmk5aSx6LSl77yBIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgfSk7CiAgICAgICAgfSkKICAgICAgICAuY2F0Y2goKCkgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICJpbmZvIiwKICAgICAgICAgICAgbWVzc2FnZTogIuW3suWPlua2iOWIoOmZpCIKICAgICAgICAgIH0pOwogICAgICAgIH0pOwogICAgICAvLyB9IGVsc2UgewogICAgICAvLyAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAvLyAgICAgdHlwZTogJ2luZm8nLAogICAgICAvLyAgICAgbWVzc2FnZTogJ+ivt+mAieaLqeiHs+WwkeS4gOadoeaVsOaNru+8gScKICAgICAgLy8gICB9KTsKICAgICAgLy8gfQogICAgfQogICAgLyoKICAgICAgICAvISoqIOafpeivoumDqOmXqOS4i+aLieagkee7k+aehCAqIS8KICAgICAgZ2V0VHJlZXNlbGVjdCgpIHsKICAgICAgICBkZXB0VHJlZXNlbGVjdCgpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgICAgICBjb25zb2xlLmxvZygicmVzcG9uc2XmoJHnu5PmnoQiLHJlc3BvbnNlKQogICAgICAgIHRoaXMuZGVwdE9wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgICAgIC8vIHRoaXMuc2VsZWN0RGVwdE9wdGlvbnMgPSB0aGlzLmhhbmRsZVRyZWUocmVzcG9uc2UuZGF0YSwgImRlcHRpZCIsInBhcmVudElkIiwiY2hpbGRyZW4iLDEwMDApOwogICAgICAgIH0pOwogICAgICB9LAogICAgICAvL+aWsOWinuahhumDqOmXqOWIl+ihqAogICAgICBnZXRUcmVlc2VsZWN0T3B0aW9ucygpewogICAgICAgIGxpc3REZXB0KHt9KS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgIGNvbnNvbGUubG9nKCJyZXNwb25zZSIscmVzcG9uc2UpCiAgICAgICAgICB0aGlzLnNlbGVjdERlcHRPcHRpb25zID0gdGhpcy5oYW5kbGVUcmVlKHJlc3BvbnNlLmRhdGEsICJkZXB0aWQiLCJwYXJlbnRJZCIsImNoaWxkcmVuIiwxMDAwKTsKICAgICAgICAgIC8vIHRoaXMuZ2V0VXNlckxpc3RCeURlcHRJZCgpOwoKICAgICAgICB9KTsKICAgICAgfSwKICAgICAgICAvLyDnrZvpgInoioLngrkKICAgICAgZmlsdGVyTm9kZSh2YWx1ZSwgZGF0YSkgewogICAgICAgIGlmICghdmFsdWUpIHJldHVybiB0cnVlOwogICAgICAgIHJldHVybiBkYXRhLmxhYmVsLmluZGV4T2YodmFsdWUpICE9PSAtMTsKICAgICAgfSwKICAgICAgLy8g6IqC54K55Y2V5Ye75LqL5Lu2CiAgICAgIGhhbmRsZU5vZGVDbGljayhkYXRhKSB7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5kdyA9IGRhdGEuaWQ7CiAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgIH0sCgogICAgICAvLyAvL+aWsOWinuWNleWHu+S6i+S7tgogICAgICAvLyBoYW5kbGVOb2RlQ2xpY2tzdChkYXRhKXsKICAgICAgLy8gICBjb25zb2xlLmxvZygiMTExIikKICAgICAgLy8gIHRoaXMuZ2V0VXNlckxpc3RCeURlcHRJZChkYXRhLmlkKTsKICAgICAgLy8gfSwKCiAgICBwZmR3RnVuKHZhbCl7CiAgICAgIGNvbnNvbGUubG9nKCJ2YWwiLHZhbCkKICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgJ3BlcnNvbklkJywgIiIpCiAgICAgIHRoaXMucGVyc29uTGlzdCA9IFtdOwogICAgICB0aGlzLmdldFVzZXJMaXN0QnlEZXB0SWQodmFsKTsKICAgIH0sCiovCiAgfQogIC8qIHdhdGNoOiB7CiAgICAgLy8g5qC55o2u5ZCN56ew562b6YCJ6YOo6Zeo5qCRCiAgICAgIGRlcHROYW1lKHZhbCkgewogICAgICAgIHRoaXMuJHJlZnMudHJlZS5maWx0ZXIodmFsKTsKICAgICAgfSwKICAgICB9Ki8KfTsK"}, {"version": 3, "sources": ["gzpszrwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4MA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "gzpszrwh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/lpbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row :gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card shadown=\"never\">\n          <div style=\"overflow: auto;height: 90vh\">\n            <el-tree\n              :expand-on-click-node=\"true\"\n              id=\"tree\"\n              highlight-current\n              :data=\"treeOptions\"\n              :default-expanded-keys=\"['1001']\"\n              @node-click=\"handleNodeClick\"\n              node-key=\"id\"\n            />\n          </div>\n        </el-card>\n      </el-col>\n      <!--部门数据-->\n      <!--      <el-col :span=\"4\" :xs=\"24\">\n        <div class=\"head-container\">\n          <el-input style=\"height: 90%;padding:0 10px 0\" v-model=\"deptName\" placeholder=\"请输入部门名称\" clearable size=\"small\" prefix-icon=\"el-icon-search\"/>\n          <el-tree :data=\"deptOptions\" :props=\"defaultProps\" :expand-on-click-node=\"false\"\n                   :filter-node-method=\"filterNode\" ref=\"tree\" default-expand-all @node-click=\"handleNodeClick\"\n                   highlight-current\n          />\n        </div>\n      </el-col>-->\n\n      <!--左侧列表-->\n      <el-col :span=\"20\" :xs=\"24\">\n        <el-row :gutter=\"4\" class=\"style-bottom\">\n          <el-col :span=\"24\">\n            <el-filter\n              ref=\"filter1\"\n              :data=\"filterInfo.data\"\n              :field-list=\"filterInfo.fieldList\"\n              @handleReset=\"filterReset\"\n              @handleEvent=\"handleEvent\"\n            ></el-filter>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-white class=\"button-group\">\n            <div class=\"button_btn\">\n              <el-button\n                @click=\"addSensorButton\"\n                v-hasPermi=\"['gzprywh:records:add']\"\n                type=\"primary\"\n                icon=\"el-icon-plus\"\n                >新增\n              </el-button>\n            </div>\n            <comp-table\n              :table-and-page-info=\"tableAndPageInfo\"\n              @update:multipleSelection=\"handleSelectionChange\"\n              v-loading=\"loading\"\n              height=\"69.8vh\"\n            >\n              <el-table-column\n                slot=\"table_eight\"\n                align=\"center\"\n                fixed=\"right\"\n                style=\"display: block\"\n                label=\"操作\"\n                min-width=\"120\"\n                :resizable=\"false\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-button\n                    type=\"text\"\n                    size=\"small\"\n                    @click=\"detailsInfo(scope.row)\"\n                    title=\"详情\"\n                    class=\"el-icon-view\"\n                  ></el-button>\n                  <el-button\n                    type=\"text\"\n                    size=\"small\"\n                    @click=\"updateInfo(scope.row)\"\n                    v-hasPermi=\"['gzprywh:records:edit']\"\n                    title=\"编辑\"\n                    class=\"el-icon-edit\"\n                  >\n                  </el-button>\n                  <el-button\n                    title=\"删除\"\n                    @click=\"handleDelete(scope.row.objId)\"\n                    v-hasPermi=\"['gzprywh:records:del']\"\n                    type=\"text\"\n                    icon=\"el-icon-delete\"\n                  ></el-button>\n                </template>\n              </el-table-column>\n            </comp-table>\n          </el-white>\n        </el-row>\n      </el-col>\n    </el-row>\n\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"50%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"所属公司：\" prop=\"dw\">\n              <el-select\n                placeholder=\"请选择所属公司\"\n                v-model=\"form.dw\"\n                style=\"width: 100%\"\n                :disabled=\"isDisabled\"\n                clearable\n                filterable\n              >\n                <el-option\n                  v-for=\"item in optionsSelectedData\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n              <!--               <treeselect :normalizer=\"normalizer\"  v-model=\"form.dw\"  @input=\"pfdwFun\" :options=\"selectDeptOptions\"   :show-count=\"true\" placeholder=\"请选择所属公司\"  />-->\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"人员所在单位：\" prop=\"selectDw\">\n              <el-select\n                placeholder=\"请选择要添加人员的所在单位\"\n                v-model=\"form.selectDw\"\n                style=\"width: 100%\"\n                :disabled=\"isDisabled\"\n                @change=\"deptSelectChange\"\n                clearable\n                filterable\n              >\n                <el-option\n                  v-for=\"item in optionsSelectedData\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n              <!--               <treeselect :normalizer=\"normalizer\"  v-model=\"form.dw\"  @input=\"pfdwFun\" :options=\"selectDeptOptions\"   :show-count=\"true\" placeholder=\"请选择所属公司\"  />-->\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"人员名称：\" prop=\"personId\">\n              <el-select\n                placeholder=\"请选择人员\"\n                v-model=\"form.personId\"\n                style=\"width: 100%\"\n                :disabled=\"isDisabled\"\n                clearable\n                filterable\n              >\n                <el-option\n                  v-for=\"item in personList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"人员类型：\" prop=\"rylx\">\n              <el-select\n                v-model=\"form.rylx\"\n                placeholder=\"请选择人员类型\"\n                :disabled=\"isDisabled\"\n                style=\"width: 100%\"\n              >\n                <el-option label=\"负责人\" value=\"负责人\"></el-option>\n                <el-option label=\"签发人\" value=\"签发人\"></el-option>\n                <el-option label=\"许可人\" value=\"许可人\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button\n          v-if=\"title === '新增' || title === '修改'\"\n          type=\"primary\"\n          @click=\"save\"\n          >确 认</el-button\n        >\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  getPageDataList,\n  saveOrUpdate,\n  remove,\n  getTreeData,\n  getOrganationSelectData,\n  getUserListByDeptId\n} from \"@/api/dagangOilfield/bzgl/lpbzk/gzpszrwh\";\nimport { getBdzSelectList } from \"@/api/yxgl/bdyxgl/bdxjzqpz\";\nimport { getSblxDataListSelected } from \"@/api/dagangOilfield/asset/bdsbtz\";\nimport { getPdsTreeList } from \"@/api/dagangOilfield/asset/pdg\";\nimport { listDept, deptTreeselect } from \"@/api/system/dept\";\nimport treeselect from \"@riophae/vue-treeselect\";\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\n\nexport default {\n  name: \"gzpszrwh\",\n  //components: {treeselect},\n  data() {\n    return {\n      loading: false,\n      ids: [],\n      rules: {\n        dw: [\n          {\n            required: true,\n            message: \"请选择单位\",\n            trigger: \"blur\"\n          }\n        ],\n        personId: [\n          {\n            required: true,\n            message: \"请选择人员\",\n            trigger: \"blur\"\n          }\n        ],\n        rylx: [\n          {\n            required: true,\n            message: \"请选择人员类型\",\n            trigger: \"blur\"\n          }\n        ]\n      },\n      form: {},\n      isShowDetails: false,\n      title: \"\",\n      filterInfo: {\n        data: {\n          dw: \"\",\n          rylx: \"\",\n          personName: \"\"\n        },\n        fieldList: [\n          // {label: '公司名称', type: 'select', value: 'dw', options: [],clearable: true},\n          { label: \"人员名称\", type: \"input\", value: \"personName\" },\n          {\n            label: \"人员类型\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"rylx\",\n            options: [\n              { label: \"负责人\", value: \"负责人\" },\n              { label: \"签发人\", value: \"签发人\" },\n              { label: \"许可人\", value: \"许可人\" }\n            ],\n            clearable: true\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"单位名称\", prop: \"dwmc\", minWidth: \"120\" },\n          { label: \"人员类型\", prop: \"rylx\", minWidth: \"120\" },\n          { label: \"人员名称\", prop: \"personName\", minWidth: \"120\" }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      //组织树\n      treeOptions: [],\n      queryParams: {\n        dw: undefined,\n        pageNum: 1,\n        pageSize: 10\n      },\n      isDisabled: false,\n      isShow: true,\n      //组织结构下拉数据\n      optionsSelectedData: [],\n      personList: []\n      // // 部门名称\n      // deptName: '',\n      // // 部门树选项\n      // deptOptions: [],\n      // defaultProps: {\n      //   children: \"children\",\n      //   label: \"label\",\n      // },\n      // selectDeptOptions:[],\n    };\n  },\n  created() {\n    this.getData();\n    this.getTreeOptions();\n    this.getOrganationSelectData();\n    // this.getTreeselect();\n    // this.getTreeselectOptions();\n  },\n  mounted() {},\n  watch: {\n    isShowDetails(val) {\n      if (val) {\n        const el = this.$el.querySelector(\".el-dialog\");\n        el.style.left = 0;\n        el.style.top = 0;\n      }\n    }\n  },\n  methods: {\n    /** 转换部门数据结构 */\n    // normalizer(node) {\n    //   if (node.children && !node.children.length) {\n    //     delete node.children;\n    //   }\n    //   return {\n    //     id: node.deptid,\n    //     label: node.deptname,\n    //     children: node.children\n    //   };\n    // },\n\n    //下拉框change事件\n    handleEvent(val, val1) {\n      this.queryParams = val1;\n    },\n\n    filterReset(val) {\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n    },\n    //获取组织结构下拉数据\n    getOrganationSelectData() {\n      getOrganationSelectData(\"\").then(res => {\n        this.optionsSelectedData = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value === \"dw\") {\n            item.options = this.optionsSelectedData;\n          }\n        });\n      });\n    },\n    deptSelectChange(item) {\n      this.getUserListByDeptId(item);\n    },\n    //获取树结构数据\n    getTreeOptions() {\n      getTreeData(\"\").then(res => {\n        this.treeOptions = res.data;\n      });\n    },\n    //获取树结构数据\n    getUserListByDeptId(param) {\n      getUserListByDeptId(param).then(res => {\n        this.personList = res.data;\n      });\n    },\n    //树点击事件\n    handleNodeClick(data) {\n      if (data.label != \"所属公司\") {\n        this.form.dw = data.id;\n        this.queryParams.dw = data.id;\n      } else {\n        this.queryParams.dw = \"\";\n      }\n      this.getData();\n    },\n    //查询列表\n    getData(params) {\n      this.loading = true;\n      const param = { ...this.queryParams, ...params };\n      this.queryParams = param;\n      param.mySorts = [\n        { prop: \"dwmc\", asc: true },\n        { prop: \"rylx\", asc: true }\n      ];\n      getPageDataList(param).then(res => {\n        this.tableAndPageInfo.tableData = res.data.records;\n        this.tableAndPageInfo.pager.total = res.data.total;\n        this.loading = false;\n      });\n    },\n    //添加按钮\n    addSensorButton() {\n      this.form = {};\n      this.isShowDetails = true;\n      this.isShow = true;\n      this.isDisabled = false;\n      this.title = \"新增\";\n    },\n    //保存按钮\n    async save() {\n      await this.$refs[\"form\"].validate(async valid => {\n        if (valid) {\n          try {\n            let { code } = await saveOrUpdate(this.form);\n            if (code === \"0000\") {\n              this.$message.success(\"操作成功\");\n            }\n          } catch (e) {\n            console.log(e);\n          }\n          //重置page页从1开始\n          this.tableAndPageInfo.pager.pageResize = \"Y\";\n          this.getData();\n          this.isShowDetails = false;\n        }\n      });\n    },\n    //表格多选框\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.objId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false;\n    },\n    //修改按钮\n    updateInfo(row) {\n      this.title = \"修改\";\n      this.isDisabled = false;\n      this.isShowDetails = true;\n      this.isShow = true;\n      this.form = { ...row };\n      this.getUserListByDeptId(row.dw);\n    },\n    //详情按钮\n    detailsInfo(row) {\n      this.title = \"详情\";\n      this.form = { ...row };\n      this.isShowDetails = true;\n      this.isDisabled = true;\n      this.isShow = false;\n      this.getUserListByDeptId(row.dw);\n    },\n    //删除按钮\n    handleDelete(id) {\n      let obj = [];\n      obj.push(id);\n      this.$confirm(\"此操作将永久删除该数据，是否继续？\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove(obj).then(res => {\n            if (res.code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功！\"\n              });\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败！\"\n              });\n            }\n            this.getData();\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n      // } else {\n      //   this.$message({\n      //     type: 'info',\n      //     message: '请选择至少一条数据！'\n      //   });\n      // }\n    }\n    /*\n        /!** 查询部门下拉树结构 *!/\n      getTreeselect() {\n        deptTreeselect().then((response) => {\n          console.log(\"response树结构\",response)\n        this.deptOptions = response.data;\n        // this.selectDeptOptions = this.handleTree(response.data, \"deptid\",\"parentId\",\"children\",1000);\n        });\n      },\n      //新增框部门列表\n      getTreeselectOptions(){\n        listDept({}).then(response => {\n          console.log(\"response\",response)\n          this.selectDeptOptions = this.handleTree(response.data, \"deptid\",\"parentId\",\"children\",1000);\n          // this.getUserListByDeptId();\n\n        });\n      },\n        // 筛选节点\n      filterNode(value, data) {\n        if (!value) return true;\n        return data.label.indexOf(value) !== -1;\n      },\n      // 节点单击事件\n      handleNodeClick(data) {\n        this.queryParams.dw = data.id;\n        this.getData();\n      },\n\n      // //新增单击事件\n      // handleNodeClickst(data){\n      //   console.log(\"111\")\n      //  this.getUserListByDeptId(data.id);\n      // },\n\n    pfdwFun(val){\n      console.log(\"val\",val)\n      this.$set(this.form, 'personId', \"\")\n      this.personList = [];\n      this.getUserListByDeptId(val);\n    },\n*/\n  }\n  /* watch: {\n     // 根据名称筛选部门树\n      deptName(val) {\n        this.$refs.tree.filter(val);\n      },\n     }*/\n};\n</script>\n\n<style scoped></style>\n"]}]}