{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\sblxwh\\jscs.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\sblxwh\\jscs.js", "mtime": 1706897314008}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0VGVjaG5pY2FsUGFyYW1ldGVyID0gZ2V0VGVjaG5pY2FsUGFyYW1ldGVyOwpleHBvcnRzLnNhdmVPclVwZGF0ZVRlY2huaWNhbFBhcmFtZXRlciA9IHNhdmVPclVwZGF0ZVRlY2huaWNhbFBhcmFtZXRlcjsKZXhwb3J0cy5kZWxldGVUZWNobmljYWxQYXJhbWV0ZXIgPSBkZWxldGVUZWNobmljYWxQYXJhbWV0ZXI7Cgp2YXIgX3JlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdXRpbHMvcmVxdWVzdCIpKTsKCnZhciBiYXNlVXJsID0gJy9tYW5hZ2VyLWFwaSc7Ci8qKgogKiDojrflj5bmioDmnK/lj4LmlbDmlbDmja4KICogQHBhcmFtIHBhcmFtcwogKiBAcmV0dXJucyB7UHJvbWlzZSB8IFByb21pc2U8dW5rbm93bj59CiAqIEBjcmVhdGVCeSBqaWF6dwogKi8KCmZ1bmN0aW9uIGdldFRlY2huaWNhbFBhcmFtZXRlcihwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy9zYkNsYXNzQ3MvZ2V0VGVjaG5pY2FsUGFyYW1ldGVyJywgcGFyYW1zLCAxKTsKfQovKioKICog5paw5aKe5L+u5pS55oqA5pyv5Y+C5pWw5pWw5o2uCiAqIEBwYXJhbSBwYXJhbXMKICogQHJldHVybnMge1Byb21pc2U8dW5rbm93bj59CiAqLwoKCmZ1bmN0aW9uIHNhdmVPclVwZGF0ZVRlY2huaWNhbFBhcmFtZXRlcihwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy9zYkNsYXNzQ3Mvc2F2ZU9yVXBkYXRlVGVjaG5pY2FsUGFyYW1ldGVyJywgcGFyYW1zLCAxKTsKfQovKioKICog5om56YeP5Yig6Zmk5oqA5pyv5Y+C5pWw5pWw5o2uCiAqIEBwYXJhbSBpZHMgaWTpm4blkIgKICogQHJldHVybnMge1Byb21pc2UgfCBQcm9taXNlPHVua25vd24+fQogKi8KCgpmdW5jdGlvbiBkZWxldGVUZWNobmljYWxQYXJhbWV0ZXIoaWRzKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcvc2JDbGFzc0NzL2RlbGV0ZVRlY2huaWNhbFBhcmFtZXRlcnMnLCBpZHMsIDEpOwp9"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagang<PERSON>field/bzgl/sblxwh/jscs.js"], "names": ["baseUrl", "getTechnicalParameter", "params", "api", "requestPost", "saveOrUpdateTechnicalParameter", "deleteTechnicalParameter", "ids"], "mappings": ";;;;;;;;;;;AAAA;;AAEA,IAAMA,OAAO,GAAG,cAAhB;AAEA;;;;;;;AAMO,SAASC,qBAAT,CAA+BC,MAA/B,EAAuC;AAC5C,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,kCAA1B,EAA8DE,MAA9D,EAAsE,CAAtE,CAAP;AACD;AAED;;;;;;;AAKO,SAASG,8BAAT,CAAwCH,MAAxC,EAAgD;AACrD,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,2CAA1B,EAAuEE,MAAvE,EAA+E,CAA/E,CAAP;AACD;AAED;;;;;;;AAKO,SAASI,wBAAT,CAAkCC,GAAlC,EAAuC;AAC5C,SAAOJ,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,sCAA1B,EAAkEO,GAAlE,EAAuE,CAAvE,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\n\nconst baseUrl = '/manager-api'\n\n/**\n * 获取技术参数数据\n * @param params\n * @returns {Promise | Promise<unknown>}\n * @createBy jiazw\n */\nexport function getTechnicalParameter(params) {\n  return api.requestPost(baseUrl + '/sbClassCs/getTechnicalParameter', params, 1)\n}\n\n/**\n * 新增修改技术参数数据\n * @param params\n * @returns {Promise<unknown>}\n */\nexport function saveOrUpdateTechnicalParameter(params) {\n  return api.requestPost(baseUrl + '/sbClassCs/saveOrUpdateTechnicalParameter', params, 1)\n}\n\n/**\n * 批量删除技术参数数据\n * @param ids id集合\n * @returns {Promise | Promise<unknown>}\n */\nexport function deleteTechnicalParameter(ids) {\n  return api.requestPost(baseUrl + '/sbClassCs/deleteTechnicalParameters', ids, 1)\n}\n"]}]}