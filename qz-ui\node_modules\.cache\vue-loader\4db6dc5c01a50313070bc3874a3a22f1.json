{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\activitimodel\\leave.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\activitimodel\\leave.vue", "mtime": 1706897321992}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["leave.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "leave.vue", "sourceRoot": "src/views/activiti/activitimodel", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n      <el-form class=\"searchForm1\" :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"120px\">\n        <el-form-item label=\"key：\" prop=\"key\">\n          <el-input v-model=\"queryParams.key\" placeholder=\"请输入key\" clearable class=\"common-width\"\n                    @keyup.enter.native=\"handleQuery\"/>\n        </el-form-item>\n        <el-form-item label=\"名称：\" prop=\"name\">\n          <el-input v-model=\"queryParams.name\" placeholder=\"名称\" clearable class=\"common-width\"\n                    @keyup.enter.native=\"handleQuery\"/>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"cyan\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\n          <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\" type=\"warning\">重置</el-button>\n        </el-form-item>\n      </el-form>\n\n    <el-row :gutter=\"20\">\n      <el-col :span=\"24\">\n        <el-white class=\"button-group\">\n          <el-row :gutter=\"10\" class=\"mb8 pull-right\">\n            <el-col :span=\"1.5\">\n              <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd\"\n              >新增<!-- v-hasPermi=\"['activiti:model:add']\"-->\n              </el-button>\n            </el-col>\n          </el-row>\n          <el-table style=\"margin-top: 15px;\" stripe border v-loading=\"loading\" :data=\"leaveList\" @selection-change=\"handleSelectionChange\">\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n            <el-table-column label=\"id\" align=\"center\" prop=\"id\" width=\"60\"/>\n            <el-table-column label=\"请假类型\" align=\"center\" prop=\"type\"  />\n            <el-table-column label=\"标题\" align=\"center\" prop=\"title\" />\n            <el-table-column label=\"原因\" align=\"center\" prop=\"reason\" width=\"120\"/>\n            <el-table-column label=\"开始时间\" align=\"center\" prop=\"startTime\"  />\n            <el-table-column label=\"结束时间\" align=\"center\" prop=\"endTime\"  />\n            <el-table-column label=\"流程实例ID\" align=\"center\" prop=\"instanceId\"  />\n            <el-table-column label=\"创建人\" align=\"center\" prop=\"createBy\"  />\n            <el-table-column label=\"申请人\" align=\"center\" prop=\"applyUser\"  />\n            <el-table-column label=\"申请时间\" align=\"center\" prop=\"applyTime\"  />\n            <el-table-column label=\"当前任务名称\" align=\"center\" prop=\"taskName\"  />\n            <el-table-column label=\"操作\" align=\"center\"   class-name=\"small-padding fixed-width\">\n              <template slot-scope=\"scope\">\n                <slot v-if=\"!scope.row.instanceId\">\n                  <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleCommit(scope.row)\" >提交申请</el-button>\n                  <el-button size=\"mini\" type=\"text\" icon=\"el-icon-upload\" @click=\"handleDeploy(scope.row)\" >编辑</el-button>\n                  <el-button  size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\"  >删除</el-button>\n                </slot>\n                <slot v-else>\n                  <el-button size=\"mini\" type=\"text\"   @click=\"handleHistory(scope.row)\" >审批历史</el-button>\n                  <el-button size=\"mini\" type=\"text\"   @click=\"handlePlan(scope.row)\" >进度查看</el-button>\n                  <slot v-if=\"scope.row.taskName.indexOf('已结束')==-1\">\n                    <el-button size=\"mini\" type=\"text\"   @click=\"handleCancelApply(scope.row)\" >撤销</el-button>\n                    <slot v-if=\"scope.row.suspendState==2\">\n                      <el-button size=\"mini\" type=\"text\" icon=\"el-icon-check\" @click=\"handleUpdate(scope.row)\" >激活</el-button>\n                    </slot>\n                    <slot  v-else>\n                      <el-button size=\"mini\" type=\"text\" icon=\"el-icon-time\" @click=\"handleUpdate(scope.row)\" >挂起</el-button>\n                    </slot>\n                  </slot>\n                </slot>\n              </template>\n            </el-table-column>\n          </el-table>\n          <pagination v-show=\"total>0\"\n                      :total=\"total\"\n                      :page.sync=\"queryParams.pageNum\"\n                      :limit.sync=\"queryParams.pageSize\"\n                      @pagination=\"getList\"\n          />\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!-- 添加或修改参数配置对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"480px\" append-to-body :close-on-click-modal=false v-dialogDrag>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"60px\">\n            <el-form-item label=\"标题\" prop=\"name\">\n              <el-input v-model=\"form.title\" placeholder=\"请输入名称\" maxlength=\"30\"/>\n            </el-form-item>\n            <el-form-item label=\"原因\" prop=\"reason\">\n              <el-input v-model=\"form.reason\" placeholder=\"请输入key\" maxlength=\"30\"/>\n            </el-form-item>\n            <el-form-item label=\"开始时间\">\n              <el-date-picker\n                v-model=\"form.startTime\"\n                type=\"datetime\"\n                format=\"yyyy-MM-dd HH:mm:ss\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n                placeholder=\"选择日期时间\">\n              </el-date-picker>\n            </el-form-item>\n          <el-form-item label=\"结束时间\">\n            <el-date-picker\n              v-model=\"form.endTime\"\n              type=\"datetime\"\n              format=\"yyyy-MM-dd HH:mm:ss\"\n              value-format=\"yyyy-MM-dd HH:mm:ss\"\n              placeholder=\"选择日期时间\">\n            </el-date-picker>\n          </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog title=\"审批历史\" :visible.sync=\"openHistory\" width=\"800px\" append-to-body :close-on-click-modal=false v-dialogDrag>\n      <slot v-if=\"instanceId!=null\">\n        <activiti-history :instance-id=\"instanceId\" />\n      </slot>\n    </el-dialog>\n\n    <el-dialog title=\"审批进度\" :visible.sync=\"openLoadingImg\" width=\"800px\" append-to-body :close-on-click-modal=false v-dialogDrag>\n      <img :src=\"imgSrc\"/>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  import {\n    list,save,del,commit\n  } from \"@/api/activiti/leave\";\n  import {\n    suspendOrActiveApply\n  } from \"@/api/activiti/processdefinition\";\n  import ActivitiHistory from \"../todoitem/history\";\n  export default {\n    name: \"Leave\",\n    components: {ActivitiHistory},\n    data() {\n      return {\n        instanceId:null,\n        // 遮罩层\n        loading: true,\n        openLoadingImg:false,\n        openHistory:false,\n        // 选中数组\n        ids: [],\n        // 非单个禁用\n        single: true,\n        // 非多个禁用\n        multiple: true,\n        // 显示搜索条件\n        showSearch: true,\n        // 总条数\n        total: 0,\n        imgSrc:'',\n        // 用户表格数据\n        leaveList: null,\n        // 弹出层标题\n        title: \"请假\",\n        // 部门树选项\n        deptOptions: [],\n        // 是否显示弹出层\n        open: false,\n        // 部门名称\n        // 表单参数\n        form: {},\n        // 查询参数\n        queryParams: {\n          pageNum: 1,\n          pageSize: 10,\n        },\n        // 表单校验\n        rules: {\n          title: [\n            {required: true, message: \"key不能为空\", trigger: \"blur\"},\n          ],\n          reason: [\n            {required: true, message: \"名称不能为空\", trigger: \"blur\"},\n          ],\n          startTime: [\n            {required: true, message: \"开始时间不能为空\", trigger: \"blur\"},\n          ],\n          endTime: [\n            {required: true, message: \"结束时间不能为空\", trigger: \"blur\"},\n          ],\n        },\n      };\n    },\n    created() {\n      this.getList();\n    },\n    methods: {\n      // 测试数据字典方法结束\n      /** 查询用户列表 */\n      getList() {\n        this.loading = true;\n        list(this.queryParams).then(\n          (response) => {\n            this.leaveList = response.data.records;\n            this.total = response.data.total;\n            this.loading = false;\n          }\n        );\n      },\n     /**\n      * 取消按钮\n      * */\n      cancel() {\n        this.open = false;\n        this.reset();\n      },\n      /**\n       * 表单重置\n       * */\n      reset() {\n        this.form = {\n          key: undefined,\n          name: undefined,\n          description:undefined\n        };\n        this.resetForm(\"form\");\n      },\n      /** 搜索按钮操作 */\n      handleQuery() {\n        this.queryParams.pageNum = 1;\n        this.getList();\n      },\n      /** 重置按钮操作 */\n      resetQuery() {\n        this.dateRange = [];\n        this.resetForm(\"queryForm\");\n        this.handleQuery();\n      },\n      /***多选框选中数据***/\n      handleSelectionChange(selection) {\n        this.ids = selection.map((item) => item.userId);\n        this.single = selection.length != 1;\n        this.multiple = !selection.length;\n      },\n      /** 新增按钮操作 */\n      handleAdd() {\n        this.reset();\n        this.open = true;\n      },\n      /** 编辑按钮操作 */\n      handleCommit(row) {\n        const id = row.id;\n        this.$confirm(\n          '确认要提交申请吗?',\n          \"警告\",\n          {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\",\n          }\n        )\n          .then(function () {\n            return commit(id);\n          })\n          .then(() => {\n            this.getList();\n            this.msgSuccess(\"提交成功\");\n          })\n          .catch(function () {\n          });\n      },\n      /** 提交按钮 */\n      submitForm: function () {\n        this.$refs[\"form\"].validate((valid) => {\n          if (valid) {\n            save(this.form).then((response) => {\n              if (response.code === '0000') {\n                this.msgSuccess(\"新增成功\");\n                this.open = false;\n                this.getList();\n              }\n            });\n          }\n        });\n      },\n      /** 删除按钮操作 */\n      handleDelete(row) {\n        const id = row.id;\n        this.$confirm(\n          '是否确认删除id为\"' + id + '\"的数据项?',\n          \"警告\",\n          {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\",\n          }\n        )\n          .then(function () {\n            return del(id);\n          })\n          .then(() => {\n            this.getList();\n            this.msgSuccess(\"删除成功\");\n          })\n          .catch(function () {\n          });\n      },\n      /***  导出bpm文件 ***/\n      handleExport(row){\n        exportModel(row.id);\n      },\n      /** 部署 **/\n      handleDeploy(row){\n        deploy(row.id).then(res =>{\n          if(res.code=='0000'){\n            this.msgSuccess(res.msg);\n          }else{\n            this.msgError(res.msg);\n          }\n        })\n      },\n      /***  审批历史 ***/\n      handleHistory(row){\n        this.openHistory = true;\n        this.instanceId = row.instanceId\n      },\n      /**** 进度查看  ****/\n      handlePlan(row){\n        this.openLoadingImg = true\n        this.imgSrc =\"/activiti-api/process/read-resource?instanceId=\"+row.instanceId+\"&t=\"+new Date().getTime();\n      },\n      /** 编辑按钮操作 */\n      handleUpdate(row) {\n        let suspendState = row.suspendState;\n        suspendOrActiveApply({id:row.id,suspendState:row.suspendState}).then(res =>{\n          if(res.code==\"0000\"){\n            this.msgSuccess(suspendState==\"2\"?\"激活成功\":\"挂起成功\");\n            this.getList();\n          }\n        })\n      },\n    },\n  };\n</script>\n"]}]}