{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\jbwh.vue?vue&type=style&index=0&id=4e0ea53d&scoped=true&lang=scss&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\jbwh.vue", "mtime": 1706897323219}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouamJ3aF9ib3h7CiAgcGFkZGluZzogMjBweCAwIDAgMjBweDsKfQouamJ3aF9ib3gxewogIG1hcmdpbi10b3A6IC0xOHB4Owp9Ci50YWJBY3RpdmV7CiAgLy93aWR0aDogMTAlOwogIGZsb2F0OiBsZWZ0OwogIGNvbG9yOiNmZmY7CiAgYmFja2dyb3VuZDogIzAyYjk4ODsKICBib3JkZXItdG9wOjA7Cn0KLm5vQWN0aXZlewogIC8vd2lkdGg6IDEwJTsKICBmbG9hdDogbGVmdDsKICBiYWNrZ3JvdW5kOiAjZmZmOwogIGNvbG9yOiM1NDUyNTI7CiAgJjpob3ZlciB7CiAgICBiYWNrZ3JvdW5kOiAjRkZGRkZGOwogICAgY29sb3I6ICMzNTkwNzY7CiAgfQp9Ci5vbmVCdG57CiAgbWFyZ2luLXJpZ2h0OiAtMTVweDsKfQoudHdvQnRuewogIHRyYW5zZm9ybTogc2tld1goMzNkZWcpOwogIGJvcmRlci1yaWdodDogIDFweCBzb2xpZCAjOWE5ODk4Njk7CiAgLmFsbEJ0bnsKICAgIHRyYW5zZm9ybTogc2tld1goLTMzZGVnKTsKICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICB9Cn0KCg=="}, {"version": 3, "sources": ["jbwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4FA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "jbwh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk", "sourcesContent": ["<template>\n  <el-row :gutter=\"16\">\n    <!--   Tab页签   -->\n    <el-col :span=\"12\">\n      <div class=\"txtTitle\">\n        <span @click=\"click('jbxx')\" :class=\"this.flag === 'jbxx'?'tabActive':'noActive'\" class=\"oneBtn\">\n          <span class=\"allBtn\">基本信息列表</span>\n        </span>\n        <span @click=\"click('csxx')\" :class=\"this.flag === 'csxx'?'tabActive':'noActive'\" class=\"twoBtn\">\n          <span class=\"allBtn\">参数信息列表</span>\n        </span>\n        <span @click=\"click('xxd')\" :class=\"this.flag === 'xxd'?'tabActive':'noActive'\" class=\"twoBtn\">\n          <span class=\"allBtn\">信息点列表</span>\n        </span>\n      </div>\n    </el-col>\n    <el-col :span=\"12\">\n      <div class=\"txtTitle\">\n        <span class=\"oneBtn tabActive\">\n          <span class=\"allBtn\">脚本维护</span>\n        </span>\n      </div>\n    </el-col>\n\n    <!--  基本信息  -->\n    <el-col :span=\"12\" v-if=\"this.flag === 'jbxx'\">\n      <Jbxx :sblx=\"sblx\" @dbClickRow=\"dbClickRow\"></Jbxx>\n    </el-col>\n    <!--  参数信息  -->\n    <el-col :span=\"12\" v-if=\"this.flag === 'csxx'\">\n      <Csxx :sblx=\"sblx\" @dbClickRow=\"dbClickRow\"></Csxx>\n    </el-col>\n    <!--  信息点  -->\n    <el-col :span=\"12\" v-if=\"this.flag === 'xxd'\">\n      <Xxd :ssztl-id=\"mpData.objId\" @dbClickRow=\"dbClickRow\"></Xxd>\n    </el-col>\n    <!--  脚本编辑  -->\n    <el-col :span=\"12\">\n      <JbEdit ref=\"editJb\" :jb=\"jb\" @setJbVal=\"setJbVal\" @jbClose=\"jbClose\"></JbEdit>\n    </el-col>\n  </el-row>\n</template>\n<script>\nimport Jbxx from \"@/views/dagangOilfield/bzgl/sbztpjbzk/jbwh_jbxx\";\nimport Csxx from \"@/views/dagangOilfield/bzgl/sbztpjbzk/jbwh_csxx\";\nimport Xxd from \"@/views/dagangOilfield/bzgl/sbztpjbzk/jbwh_xxd\";\nimport JbEdit from \"@/views/dagangOilfield/bzgl/sbztpjbzk/jbwh_edit\";\n\nexport default {\n  name: 'jbwh',\n  components: {Jbxx,Csxx,Xxd,JbEdit},\n  props: {\n    sblx:{\n      type:String,\n      default:'',\n    },\n    mpData: {\n      type: Object,\n    },\n    jb: {\n      type: String,\n      default:'',\n    },\n  },\n  data() {\n    return {\n      flag:'jbxx', //默认展示基本信息列表\n    };\n  },\n  mounted() {\n  },\n  methods:{\n    //tab切换\n    click(mainTab){\n      this.flag = mainTab;\n    },\n    //表格双击事件\n    dbClickRow(val,map){\n      this.$refs.editJb.processParentVal(val,map);\n    },\n    setJbVal(val){\n      this.$emit('setJbVal',val);\n    },\n    //关闭脚本弹框\n    jbClose(){\n      this.$emit('jbClose');\n    },\n  },\n}\n</script>\n\n<style scoped lang=\"scss\">\n.jbwh_box{\n  padding: 20px 0 0 20px;\n}\n.jbwh_box1{\n  margin-top: -18px;\n}\n.tabActive{\n  //width: 10%;\n  float: left;\n  color:#fff;\n  background: #02b988;\n  border-top:0;\n}\n.noActive{\n  //width: 10%;\n  float: left;\n  background: #fff;\n  color:#545252;\n  &:hover {\n    background: #FFFFFF;\n    color: #359076;\n  }\n}\n.oneBtn{\n  margin-right: -15px;\n}\n.twoBtn{\n  transform: skewX(33deg);\n  border-right:  1px solid #9a989869;\n  .allBtn{\n    transform: skewX(-33deg);\n    display: inline-block;\n  }\n}\n\n</style>\n"]}]}