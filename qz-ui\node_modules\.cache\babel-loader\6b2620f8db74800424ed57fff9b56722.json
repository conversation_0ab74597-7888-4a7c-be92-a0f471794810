{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\technicalParameter.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\technicalParameter.vue", "mtime": 1706897322897}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["technicalParameter.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AA4CA;;AAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,UAAA,EAAA;AAAA,IAAA,UAAA,EAAA;AAAA,GADA;AAEA,EAAA,KAAA,EAAA;AACA,IAAA,cAAA,EAAA;AACA,MAAA,IAAA,EAAA;AADA;AADA,GAFA;AAOA,EAAA,IAAA,EAAA,oBAPA;AAQA,EAAA,IARA,kBAQA;AACA,WAAA;AACA,MAAA,QAAA,EAAA,IADA;AAEA,MAAA,IAAA,EAAA,CAFA;AAGA;AACA,MAAA,SAAA,EAAA,EAJA;AAKA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA,CAHA;AAIA,QAAA,MAAA,EAAA;AAJA,OANA;AAYA,MAAA,OAAA,EAAA,KAZA;AAaA,MAAA,QAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,SADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,UAHA;AAIA,QAAA,IAAA,EAAA,QAJA;AAKA,QAAA,OAAA,EAAA,IALA;AAMA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AANA,OADA,EASA;AACA,QAAA,KAAA,EAAA,SADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,QAHA;AAIA,QAAA,OAAA,EAAA,IAJA;AAKA,QAAA,IAAA,EAAA,OALA;AAMA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AANA,OATA,EAiBA;AACA,QAAA,KAAA,EAAA,SADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,QAHA;AAIA,QAAA,OAAA,EAAA,IAJA;AAKA,QAAA,IAAA,EAAA,eALA;AAMA,QAAA,OAAA,EAAA,CAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAAA,EAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAAA,CANA;AAOA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AAPA,OAjBA,EA0BA;AACA,QAAA,KAAA,EAAA,SADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,QAHA;AAIA,QAAA,OAAA,EAAA,IAJA;AAKA,QAAA,IAAA,EAAA,QALA;AAMA,QAAA,OAAA,EAAA,EANA;AAOA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AAPA,OA1BA,EAmCA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,OAHA;AAIA,QAAA,IAAA,EAAA,IAJA;AAKA,QAAA,OAAA,EAAA,IALA;AAMA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AANA,OAnCA,EA2CA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,QAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,OAAA,EAAA,IALA;AAMA,QAAA,OAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,UAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAJA,CANA;AAYA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AAZA,OA3CA,EAyDA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,OAHA;AAIA,QAAA,IAAA,EAAA,IAJA;AAKA,QAAA,OAAA,EAAA,IALA;AAMA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AANA,OAzDA,EAiEA;AACA,QAAA,KAAA,EAAA,SADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,OAHA;AAIA,QAAA,IAAA,EAAA,QAJA;AAKA,QAAA,OAAA,EAAA,IALA;AAMA,QAAA,MAAA,EAAA,KANA;AAOA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AAPA,OAjEA,EA0EA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,OAHA;AAIA,QAAA,OAAA,EAAA,IAJA;AAKA,QAAA,MAAA,EAAA,KALA;AAMA,QAAA,IAAA,EAAA,OANA;AAOA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AAPA,OA1EA,CAbA;AAiGA;AACA,MAAA,gBAAA,EAAA;AAlGA,KAAA;AAqGA,GA9GA;AA+GA,EAAA,OA/GA,qBA+GA;AACA,SAAA,OAAA;AACA,GAjHA;AAkHA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,OAFA,qBAEA;AAAA;;AACA,WAAA,OAAA,GAAA,IAAA;AACA,WAAA,WAAA,CAAA,MAAA,GAAA,KAAA,cAAA,CAAA,MAAA;AACA,uCAAA,KAAA,WAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,KAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,KAAA,CAAA,WAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,KAAA,CAAA,OAAA,GAAA,KAAA;AACA,OAJA;AAKA,KAVA;AAWA;AACA,IAAA,qBAZA,mCAYA;AAAA;;AACA,WAAA,QAAA,GAAA,IAAA,CADA,CAEA;;AACA,WAAA,QAAA,GAAA,KAAA,QAAA,CAAA,IAAA,GAAA,QAAA;AACA,UAAA,OAAA,GAAA,KAAA,QAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,KAAA,QAAA,EAAA;AACA,UAAA,IAAA,CAAA,KAAA,GAAA,MAAA,CAAA,cAAA,CAAA,MAAA;AACA;;AACA,YAAA,IAAA,CAAA,IAAA,KAAA,QAAA,EAAA;AACA,UAAA,IAAA,CAAA,KAAA,GAAA,MAAA,CAAA,cAAA,CAAA,IAAA;AACA;;AACA,eAAA,IAAA;AACA,OARA,CAAA;AASA,WAAA,KAAA,CAAA,UAAA,CAAA,OAAA,CAAA,OAAA;AACA,KA1BA;AA2BA;AACA,IAAA,wBA5BA,sCA4BA;AAAA;;AAEA,WAAA,QAAA,CAAA,iBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AAEA,YAAA,GAAA,GAAA,EAAA;;AACA,QAAA,MAAA,CAAA,gBAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,KAAA;AACA,SAFA;;AAIA,4CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,YAAA,MAAA,CAAA,OAAA;AACA,WAHA,MAGA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,SAPA;AAQA,OAnBA,EAmBA,KAnBA,CAmBA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OAxBA;AAyBA,KAvDA;AAwDA;AACA,IAAA,wBAzDA,oCAyDA,GAzDA,EAyDA;AACA,UAAA,UAAA,GAAA,KAAA,QAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,eAAA,IAAA;AACA,OAHA,CAAA;AAIA,WAAA,QAAA,GAAA,IAAA;AACA,WAAA,KAAA,CAAA,UAAA,CAAA,OAAA,CAAA,UAAA;AACA,KAhEA;AAiEA;AACA,IAAA,UAlEA,sBAkEA,GAlEA,EAkEA;AACA,UAAA,QAAA,GAAA,KAAA,QAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,eAAA,IAAA;AACA,OAHA,CAAA;AAIA,WAAA,QAAA,GAAA,IAAA;AACA,WAAA,KAAA,CAAA,UAAA,CAAA,MAAA,CAAA,QAAA;AACA,KAzEA;AA0EA;AACA,IAAA,sBA3EA,kCA2EA,QA3EA,EA2EA;AAAA;;AACA,UAAA,OAAA,GAAA,EAAA;;AACA,UAAA,QAAA,CAAA,KAAA,KAAA,EAAA,IAAA,CAAA,QAAA,CAAA,KAAA,EAAA;AACA,QAAA,OAAA,GAAA,MAAA;AACA,OAFA,MAEA;AACA,QAAA,OAAA,GAAA,MAAA;AACA;;AACA,gDAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,OAAA;;AACA,UAAA,MAAA,CAAA,OAAA;AACA,SAHA,MAGA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,OAPA;AASA,KA3FA;AA4FA;AACA,IAAA,qBA7FA,iCA6FA,GA7FA,EA6FA;AACA,WAAA,gBAAA,GAAA,GAAA;AACA,KA/FA;AAiGA,IAAA,gBAjGA,4BAiGA,GAjGA,EAiGA;AACA;AACA,UAAA,qBAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,EAAA;;AACA,UAAA,GAAA,KAAA,IAAA,EAAA;AACA,QAAA,IAAA,GAAA,UAAA;AACA,aAAA,4BAAA,CAAA,IAAA,EAAA,qBAAA;AACA,OAHA,MAGA,IAAA,GAAA,KAAA,IAAA,EAAA;AACA,QAAA,IAAA,GAAA,UAAA;AACA,aAAA,4BAAA,CAAA,IAAA,EAAA,qBAAA;AACA;;AACA,WAAA,KAAA,CAAA,UAAA,CAAA,aAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,KAAA,QAAA,EAAA;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,qBAAA;AACA,UAAA,IAAA,CAAA,KAAA,GAAA,EAAA;AACA;AACA;AACA,OANA;AAOA,KAnHA;AAoHA;AACA,IAAA,4BArHA,wCAqHA,IArHA,EAqHA,qBArHA,EAqHA;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,EAAA,EAAA,CAAA,EAAA,EAAA;AACA,YAAA,KAAA,GAAA,CAAA,GAAA,CAAA;AACA,QAAA,qBAAA,CAAA,IAAA,CAAA;AACA,UAAA,KAAA,EAAA,IAAA,GAAA,KADA;AAEA,UAAA,KAAA,EAAA,IAAA,GAAA;AAFA,SAAA;AAIA;AAEA;AA9HA;AAlHA,C", "sourcesContent": ["<template>\n  <div>\n    <el-white class=\"button-group\">\n      <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"addTechnicalParameter\">新增</el-button>\n      <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"deleteTechnicalParameter\">删除</el-button>\n    </el-white>\n    <el-table\n      stripe\n      border\n      v-loading=\"loading\"\n      :data=\"tableData\"\n      @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n      <el-table-column label=\"技术参数编码\" align=\"center\" prop=\"jscsbm\"/>\n      <el-table-column label=\"技术参数名称\" align=\"center\" prop=\"jscsmc\" :show-overflow-tooltip=\"true\"/>\n      <el-table-column label=\"单位\" align=\"center\" prop=\"dw\" :show-overflow-tooltip=\"true\"/>\n      <el-table-column label=\"显示类型\" align=\"center\" prop=\"cslx\" :show-overflow-tooltip=\"true\"/>\n      <el-table-column label=\"操作\" fixed=\"right\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <el-button type=\"text\" @click=\"updateTechnicalParameter(scope.row)\" class=\"updateBtn\">修改</el-button>\n          <el-button type=\"text\" @click=\"getDetails(scope.row)\">详情</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <pagination\n      v-show=\"queryParams.total > 0\"\n      :total=\"queryParams.total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"/>\n    <dialog-form\n      ref=\"dialogForm\"\n      :append-to-body=\"true\"\n      label-width=\"150px\"\n      out-width=\"50%\"\n      :reminder=\"reminder\"\n      :rows=\"rows\"\n      @inputChange1=\"paramsTypeChange\"\n      @save=\"saveTechnicalParameter\"\n    />\n  </div>\n</template>\n\n<script>\nimport {\n  deleteTechnicalParameter,\n  getTechnicalParameter,\n  saveOrUpdateTechnicalParameter\n} from '@/api/dagangOilfield/bzgl/sblxwh/jscs'\nimport DialogForm from 'com/dialogFrom/dialogForm'\n\nexport default {\n  components: { DialogForm },\n  props: {\n    deviceTypeData: {\n      type: Object\n    }\n  },\n  name: 'technicalParameter',\n  data() {\n    return {\n      reminder: '新增',\n      rows: 2,\n      //表单数据\n      tableData: [],\n      //查询条件\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        total: 0,\n        sblxbm: ''\n      },\n      loading: false,\n      formList: [\n        {\n          label: '设备类型名称：',\n          value: '',\n          type: 'disabled',\n          name: 'sblxmc',\n          default: true,\n          rules: { required: true, message: '请选择设备类型名称' }\n        },\n        {\n          label: '技术参数名称：',\n          value: '',\n          name: 'jscsmc',\n          default: true,\n          type: 'input',\n          rules: { required: true, message: '请输入技术参数名称' }\n        },\n        {\n          label: '技术参数类型：',\n          value: '',\n          name: 'jscslx',\n          default: true,\n          type: 'selectChange1',\n          options: [{ label: '字符', value: '字符' }, { label: '数值', value: '数值' }],\n          rules: { required: true, message: '请选择技术参数类型' }\n        },\n        {\n          label: '技术参数编码：',\n          value: '',\n          name: 'jscsbm',\n          default: true,\n          type: 'select',\n          options: [],\n          rules: { required: true, message: '请选择技术参数编码' }\n        },\n        {\n          label: '单位：',\n          value: '',\n          type: 'input',\n          name: 'dw',\n          default: true,\n          rules: { required: false, message: '请输入单位' }\n        },\n        {\n          label: '显示类型：',\n          value: '',\n          type: 'select',\n          name: 'cslx',\n          default: true,\n          options: [\n            { label: 'input', value: 'input' },\n            { label: 'select', value: 'select' },\n            { label: 'date', value: 'date' },\n            { label: 'datetime', value: 'datetime' }\n          ],\n          rules: { required: false, message: '请选择显示类型' }\n        },\n        {\n          label: '排序：',\n          value: '',\n          type: 'input',\n          name: 'px',\n          default: true,\n          rules: { required: false, message: '请输入排序' }\n        },\n        {\n          label: '设备类型编码：',\n          value: '',\n          type: 'input',\n          name: 'sblxbm',\n          default: true,\n          hidden: false,\n          rules: { required: true, message: '请选择设备类型编码' }\n        },\n        {\n          label: 'id：',\n          value: '',\n          name: 'objId',\n          default: true,\n          hidden: false,\n          type: 'input',\n          rules: { required: false, message: '请输入编码' }\n        }\n      ],\n      //选中行数据\n      selectedRowDatas: []\n\n    }\n  },\n  mounted() {\n    this.getList()\n  },\n  methods: {\n    //获取技术参数数据\n    getList() {\n      this.loading = true\n      this.queryParams.sblxbm = this.deviceTypeData.sblxbm\n      getTechnicalParameter(this.queryParams).then(res => {\n        this.tableData = res.data.records\n        this.queryParams.total = res.data.total\n        this.loading = false\n      })\n    },\n    //新增技术参数\n    addTechnicalParameter() {\n      this.reminder = '新增'\n      //初始化formList数据\n      this.formList = this.$options.data().formList\n      const addForm = this.formList.map(item => {\n        if (item.name === 'sblxbm') {\n          item.value = this.deviceTypeData.sblxbm\n        }\n        if (item.name === 'sblxmc') {\n          item.value = this.deviceTypeData.sblx\n        }\n        return item\n      })\n      this.$refs.dialogForm.showzzc(addForm)\n    },\n    //批量删除技术参数数据\n    deleteTechnicalParameter() {\n\n      this.$confirm('确认删除选中数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n\n        let ids = []\n        this.selectedRowDatas.forEach(item => {\n          ids.push(item.objId)\n        })\n\n        deleteTechnicalParameter(ids).then(res => {\n          if (res.code === '0000') {\n            this.$message.success('删除成功')\n            this.getList()\n          } else {\n            this.$message.error('操作失败')\n          }\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        })\n      })\n    },\n    //修改基础参数数据\n    updateTechnicalParameter(row) {\n      const updateList = this.formList.map(item => {\n        item.value = row[item.name]\n        return item\n      })\n      this.reminder = '修改'\n      this.$refs.dialogForm.showzzc(updateList)\n    },\n    //技术参数详情\n    getDetails(row) {\n      const infoList = this.formList.map(item => {\n        item.value = row[item.name]\n        return item\n      })\n      this.reminder = '详情'\n      this.$refs.dialogForm.showxq(infoList)\n    },\n    //保存技术参数\n    saveTechnicalParameter(formData) {\n      let message = ''\n      if (formData.objId === '' || !formData.objId) {\n        message = '新增成功'\n      } else {\n        message = '修改成功'\n      }\n      saveOrUpdateTechnicalParameter(formData).then(res => {\n        if (res.code === '0000') {\n          this.$message.success(message)\n          this.getList()\n        } else {\n          this.$message.error('操作失败')\n        }\n      })\n\n    },\n    //行选择事件\n    handleSelectionChange(row) {\n      this.selectedRowDatas = row\n    },\n\n    paramsTypeChange(val) {\n      //技术参数编码下拉框数据\n      let tenhnicalParamOptions = []\n      let type = ''\n      if (val === '字符') {\n        type = 'zf_value'\n        this.getTechnicalParameterOptions(type, tenhnicalParamOptions)\n      } else if (val === '数值') {\n        type = 'sz_value'\n        this.getTechnicalParameterOptions(type, tenhnicalParamOptions)\n      }\n      this.$refs.dialogForm.addUpdateList.list.forEach(item => {\n        if (item.name === 'jscsbm') {\n          item.options = tenhnicalParamOptions\n          item.value = ''\n          return\n        }\n      })\n    },\n    //设置技术参数编码下拉框数据\n    getTechnicalParameterOptions(type, tenhnicalParamOptions) {\n      for (let i = 0; i < 20; i++) {\n        let index = i + 1\n        tenhnicalParamOptions.push({\n          label: type + index,\n          value: type + index\n        })\n      }\n\n    }\n  }\n}\n</script>\n\n<style scoped>\n.pagination-container {\n  position: relative;\n  height: 25px;\n  margin-bottom: 20px !important;\n  margin-top: 15px;\n  padding: 10px 20px !important;\n}\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbbzk"}]}