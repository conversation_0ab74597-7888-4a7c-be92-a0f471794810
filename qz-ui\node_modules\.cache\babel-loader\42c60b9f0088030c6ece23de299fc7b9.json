{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\pjbbkwh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\pjbbkwh.vue", "mtime": 1706897323222}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["pjbbkwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAsGA;AACA,EAAA,IAAA,EAAA,SADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA;AACA,MAAA,IAAA,EAAA,EAFA;AAIA,MAAA,KAAA,EAAA,EAJA;AAKA,MAAA,IAAA,EAAA,KALA;AAMA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,OAAA,EAAA;AADA,SADA;AAIA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,UAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAJA,OANA;AAcA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AACA,UAAA,IAAA,EAAA,WADA;AAEA,UAAA,KAAA,EAAA,IAFA;AAGA,UAAA,QAAA,EAAA,OAHA;AAIA,UAAA,KAAA,EAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WAJA;AAKA;AACA,UAAA,KAAA,EAAA,OANA;AAOA,UAAA,SAAA,EAAA,CACA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WADA,EAEA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WAFA;AAPA,SAPA;AAZA;AAdA,KAAA;AAgDA,GAnDA;AAoDA,EAAA,MApDA,oBAoDA,CAEA,CAtDA;AAwDA,EAAA,OAxDA,qBAwDA,CAEA,CA1DA;AA2DA,EAAA,OAAA,EAAA;AACA,IAAA,UADA,wBACA;AACA,WAAA,KAAA,GAAA,IAAA;AACA,WAAA,IAAA,GAAA,IAAA;AACA,KAJA;AAMA,IAAA,cANA,4BAMA;AACA,WAAA,IAAA,GAAA,KAAA,CADA,CAEA;;AACA,WAAA,UAAA,GAAA,OAAA;AACA,KAVA;AAWA;AACA,IAAA,SAZA,uBAYA;AACA,WAAA,IAAA,GAAA,IAAA;AACA,WAAA,KAAA,GAAA,IAAA;AACA,KAfA;AAgBA;AACA,IAAA,cAjBA,4BAiBA;AACA,WAAA,IAAA,GAAA,KAAA;AACA;AAnBA;AA3DA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n<!--    <el-white>-->\n<!--      <el-filter-->\n<!--        :data=\"filterInfo.data\"-->\n<!--        :field-list=\"filterInfo.fieldList\"-->\n<!--        @handleReset=\"getReset\"-->\n<!--      />-->\n<!--    </el-white>-->\n    <div>\n      <el-white class=\"button-group\">\n        <div class=\"button_btn\">\n          <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"getInster\"\n          >新增</el-button>\n          <el-button type=\"danger\" icon=\"el-icon-delete\"  @click=\"getDelete\"\n          >删除</el-button>\n          <!--              <el-button type=\"warning\" icon=\"el-icon-edit\" @click=\"getUpdate\"-->\n          <!--              >修改</el-button>-->\n          <!--              <el-button type=\"cyan\" icon=\"el-icon-download\" @click=\"getDetails\"-->\n          <!--              >导出</el-button>-->\n        </div>\n        <comp-table :table-and-page-info=\"tableAndPageInfo\" @multipleSelection=\"handleSelectionChange\"/>\n      </el-white>\n    </div>\n\n    <!--新增、修改、详情弹框-->\n    <el-dialog :title=\"title\" :visible.sync=\"show\" width=\"50%\" append-to-body @close=\"getInsterClose\" v-dialogDrag>\n      <el-form label-width=\"130px\" ref=\"form\" :model=\"form\">\n        <el-row :gutter=\"8\" class=\"pull-left\" >\n          <el-col :span=\"12\">\n            <el-form-item  label=\"评价导则：\" prop=\"scjxlb\">\n              <el-select placeholder=\"请选择评价导则\" v-model=\"form.scjxlb\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item  label=\"版本名称：\" prop=\"jyjxlb\">\n              <el-select placeholder=\"请选择版本名称\" v-model=\"form.jyjxlb\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item  label=\"版本号：\" prop=\"cljy\">\n              <el-input v-model=\"form.cljy\" placeholder=\"请输入版本号\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item  label=\"是否默认版本：\" prop=\"jyjxlb\">\n              <el-select placeholder=\"请选择是否默认版本\" v-model=\"form.jyjxlb\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item  label=\"是否启用：\" prop=\"jyjxlb\">\n              <el-select placeholder=\"请选择是否启用\" v-model=\"form.jyjxlb\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item  label=\"创建时间：\" prop=\"jyjxlb\">\n              <el-date-picker\n                type=\"datetime\"\n                placeholder=\"选择日期时间\"\n                align=\"right\" style=\"width: 100%\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"getInsterClose\">关 闭</el-button>\n        <el-button type=\"primary\" >保 存</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n    export default {\n      name: \"pjbbkwh\",\n      data(){\n        return{\n          //新增按钮form表单\n          form:{\n          },\n          title:'',\n          show:false,\n          filterInfo: {\n            data: {\n              ywdwArr: [],\n            },\n            fieldList: [\n              {label: '设备类型', type: 'select', value: 'roleName', multiple: true, options: []},\n            ]\n          },\n          tableAndPageInfo: {\n            pager: {\n              pageSize: 10,\n              pageNum: 1,\n              total: 0,\n              sizes: [10, 20, 50, 100]\n            },\n            option: {\n              checkBox: true,\n              serialNumber: true\n            },\n            tableData: [],\n            tableHeader: [\n              {prop: 'ssgs', label: '评价导则', minWidth: '120'},\n              {prop: 'bdzmc', label: '版本名称', minWidth: '180'},\n              {prop: 'dydj', label: '版本号', minWidth: '120'},\n              {prop: 'sbzt', label: '是否默认版本', minWidth: '250'},\n              {prop: 'sbzt', label: '是否启用', minWidth: '250'},\n              {prop: 'sbzt', label: '创建时间', minWidth: '250'},\n              {\n                prop: 'operation',\n                label: '操作',\n                minWidth: '130px',\n                style: {display: 'block'},\n                //操作列固定再右侧\n                fixed:'right',\n                operation: [\n                  {name: '修改', clickFun: this.getDetails},\n                  {name: '详情', clickFun: this.getDetails},\n                ]\n              },\n            ]\n          },\n        }\n      },\n      create(){\n\n      },\n\n      mounted(){\n\n      },\n      methods:{\n        getDetails(){\n          this.title='详情'\n          this.show=true\n        },\n\n        getXzsbpjClose(){\n          this.show=false\n          //设置table切换默认进来显示那个table\n          this.activeName = 'first'\n        },\n        //新增按钮\n        getInster(){\n          this.show=true\n          this.title = '新增'\n        },\n        //新增弹框关闭\n        getInsterClose(){\n          this.show=false\n        },\n\n      }\n    }\n</script>\n\n<style scoped>\n\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk"}]}