{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\utils\\scroll-to.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\utils\\scroll-to.js", "mtime": 1706897321946}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/utils/scroll-to.js"], "names": ["Math", "easeInOutQuad", "t", "b", "c", "d", "requestAnimFrame", "window", "requestAnimationFrame", "webkitRequestAnimationFrame", "mozRequestAnimationFrame", "callback", "setTimeout", "move", "amount", "document", "documentElement", "scrollTop", "body", "parentNode", "position", "scrollTo", "to", "duration", "start", "change", "increment", "currentTime", "animateScroll", "val"], "mappings": ";;;;;;;AAAAA,IAAI,CAACC,aAAL,GAAqB,UAASC,CAAT,EAAYC,CAAZ,EAAeC,CAAf,EAAkBC,CAAlB,EAAqB;AACxCH,EAAAA,CAAC,IAAIG,CAAC,GAAG,CAAT;;AACA,MAAIH,CAAC,GAAG,CAAR,EAAW;AACT,WAAOE,CAAC,GAAG,CAAJ,GAAQF,CAAR,GAAYA,CAAZ,GAAgBC,CAAvB;AACD;;AACDD,EAAAA,CAAC;AACD,SAAO,CAACE,CAAD,GAAK,CAAL,IAAUF,CAAC,IAAIA,CAAC,GAAG,CAAR,CAAD,GAAc,CAAxB,IAA6BC,CAApC;AACD,CAPD,C,CASA;;;AACA,IAAIG,gBAAgB,GAAI,YAAW;AACjC,SAAOC,MAAM,CAACC,qBAAP,IAAgCD,MAAM,CAACE,2BAAvC,IAAsEF,MAAM,CAACG,wBAA7E,IAAyG,UAASC,QAAT,EAAmB;AAAEJ,IAAAA,MAAM,CAACK,UAAP,CAAkBD,QAAlB,EAA4B,OAAO,EAAnC;AAAwC,GAA7K;AACD,CAFsB,EAAvB;AAIA;;;;;;AAIA,SAASE,IAAT,CAAcC,MAAd,EAAsB;AACpBC,EAAAA,QAAQ,CAACC,eAAT,CAAyBC,SAAzB,GAAqCH,MAArC;AACAC,EAAAA,QAAQ,CAACG,IAAT,CAAcC,UAAd,CAAyBF,SAAzB,GAAqCH,MAArC;AACAC,EAAAA,QAAQ,CAACG,IAAT,CAAcD,SAAd,GAA0BH,MAA1B;AACD;;AAED,SAASM,QAAT,GAAoB;AAClB,SAAOL,QAAQ,CAACC,eAAT,CAAyBC,SAAzB,IAAsCF,QAAQ,CAACG,IAAT,CAAcC,UAAd,CAAyBF,SAA/D,IAA4EF,QAAQ,CAACG,IAAT,CAAcD,SAAjG;AACD;AAED;;;;;;;AAKO,SAASI,QAAT,CAAkBC,EAAlB,EAAsBC,QAAtB,EAAgCZ,QAAhC,EAA0C;AAC/C,MAAMa,KAAK,GAAGJ,QAAQ,EAAtB;AACA,MAAMK,MAAM,GAAGH,EAAE,GAAGE,KAApB;AACA,MAAME,SAAS,GAAG,EAAlB;AACA,MAAIC,WAAW,GAAG,CAAlB;AACAJ,EAAAA,QAAQ,GAAI,OAAQA,QAAR,KAAsB,WAAvB,GAAsC,GAAtC,GAA4CA,QAAvD;;AACA,MAAIK,aAAa,GAAG,SAAhBA,aAAgB,GAAW;AAC7B;AACAD,IAAAA,WAAW,IAAID,SAAf,CAF6B,CAG7B;;AACA,QAAIG,GAAG,GAAG7B,IAAI,CAACC,aAAL,CAAmB0B,WAAnB,EAAgCH,KAAhC,EAAuCC,MAAvC,EAA+CF,QAA/C,CAAV,CAJ6B,CAK7B;;AACAV,IAAAA,IAAI,CAACgB,GAAD,CAAJ,CAN6B,CAO7B;;AACA,QAAIF,WAAW,GAAGJ,QAAlB,EAA4B;AAC1BjB,MAAAA,gBAAgB,CAACsB,aAAD,CAAhB;AACD,KAFD,MAEO;AACL,UAAIjB,QAAQ,IAAI,OAAQA,QAAR,KAAsB,UAAtC,EAAkD;AAChD;AACAA,QAAAA,QAAQ;AACT;AACF;AACF,GAhBD;;AAiBAiB,EAAAA,aAAa;AACd", "sourcesContent": ["Math.easeInOutQuad = function(t, b, c, d) {\r\n  t /= d / 2\r\n  if (t < 1) {\r\n    return c / 2 * t * t + b\r\n  }\r\n  t--\r\n  return -c / 2 * (t * (t - 2) - 1) + b\r\n}\r\n\r\n// requestAnimationFrame for Smart Animating http://goo.gl/sx5sts\r\nvar requestAnimFrame = (function() {\r\n  return window.requestAnimationFrame || window.webkitRequestAnimationFrame || window.mozRequestAnimationFrame || function(callback) { window.setTimeout(callback, 1000 / 60) }\r\n})()\r\n\r\n/**\r\n * Because it's so fucking difficult to detect the scrolling element, just move them all\r\n * @param {number} amount\r\n */\r\nfunction move(amount) {\r\n  document.documentElement.scrollTop = amount\r\n  document.body.parentNode.scrollTop = amount\r\n  document.body.scrollTop = amount\r\n}\r\n\r\nfunction position() {\r\n  return document.documentElement.scrollTop || document.body.parentNode.scrollTop || document.body.scrollTop\r\n}\r\n\r\n/**\r\n * @param {number} to\r\n * @param {number} duration\r\n * @param {Function} callback\r\n */\r\nexport function scrollTo(to, duration, callback) {\r\n  const start = position()\r\n  const change = to - start\r\n  const increment = 20\r\n  let currentTime = 0\r\n  duration = (typeof (duration) === 'undefined') ? 500 : duration\r\n  var animateScroll = function() {\r\n    // increment the time\r\n    currentTime += increment\r\n    // find the value with the quadratic in-out easing function\r\n    var val = Math.easeInOutQuad(currentTime, start, change, duration)\r\n    // move the document.body\r\n    move(val)\r\n    // do the animation unless its over\r\n    if (currentTime < duration) {\r\n      requestAnimFrame(animateScroll)\r\n    } else {\r\n      if (callback && typeof (callback) === 'function') {\r\n        // the animation is done so lets callback\r\n        callback()\r\n      }\r\n    }\r\n  }\r\n  animateScroll()\r\n}\r\n"]}]}