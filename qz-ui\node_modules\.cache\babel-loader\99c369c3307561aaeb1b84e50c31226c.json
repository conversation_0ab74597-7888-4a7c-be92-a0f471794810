{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\pddzcz\\components\\czp_tj.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\pddzcz\\components\\czp_tj.vue", "mtime": 1748605462598}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["czp_tj.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAsBA;;AACA;;;;;;;;;;;;;;;;;;;;;;;eACA;AACA,EAAA,IAAA,EAAA,QADA;AAEA,EAAA,OAFA,qBAEA;AACA;AACA,SAAA,OAAA,GAFA,CAGA;;AACA,SAAA,qBAAA;AACA,GAPA;AAQA,EAAA,IARA,kBAQA;AACA,WAAA;AACA;AACA,MAAA,kBAAA,EAAA,EAFA;AAGA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA;AAFA,OAJA;AAQA;AACA,MAAA,KAAA,EAAA,CATA;AAUA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,KAAA,EAAA;AADA,SADA;AAGA;AACA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,KAAA,EAAA,OAFA;AAGA,UAAA,IAAA,EAAA,QAHA;AAIA,UAAA,SAAA,EAAA,IAJA;AAKA,UAAA,UAAA,EAAA,IALA;AAMA,UAAA,OAAA,EAAA;AANA,SADA;AAJA,OAVA;AAyBA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,IAAA,EAAA,SAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,IAAA,EAAA,SAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,CARA;AAeA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAfA,OAzBA;AA0CA,MAAA,MAAA,EAAA;AACA,QAAA,EAAA,EAAA,CADA;AAEA,QAAA,MAAA,EAAA;AAFA;AA1CA,KAAA;AA+CA,GAxDA;AAyDA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,OAFA,mBAEA,MAFA,EAEA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,KAFA,+DAEA,KAAA,CAAA,MAFA,GAEA,MAFA;AAAA;AAAA,uBAGA,wBAAA,KAAA,CAHA;;AAAA;AAAA;AAGA,gBAAA,IAHA,oBAGA,IAHA;AAGA,gBAAA,IAHA,oBAGA,IAHA;;AAIA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,KAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,KAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AAPA;AAAA;;AAAA;AAAA;AAAA;AASA,gBAAA,OAAA,CAAA,GAAA;;AATA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KAbA;;AAcA;;;AAGA,IAAA,YAjBA,wBAiBA,SAjBA,EAiBA;AACA,WAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,KAAA;AAAA,OAAA,CAAA;AACA,WAAA,MAAA,GAAA,SAAA,CAAA,MAAA,KAAA,CAAA;AACA,WAAA,QAAA,GAAA,CAAA,SAAA,CAAA,MAAA;AACA,KArBA;AAsBA;AACA,IAAA,qBAvBA,mCAuBA;AAAA;;AACA,wCAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,kBAAA,GAAA,GAAA,CAAA,IAAA;;AACA,QAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,IAAA,OAAA,EAAA;AACA,mBAAA,IAAA,CAAA,OAAA,GAAA,MAAA,CAAA,kBAAA;AACA;AACA,SAJA;AAKA,OAPA;AAQA,KAhCA;AAiCA;AACA,IAAA,WAlCA,uBAkCA,GAlCA,EAkCA;AAAA;;AACA;AACA,UAAA,GAAA,CAAA,KAAA,KAAA,KAAA,IAAA,GAAA,CAAA,KAAA,IAAA,GAAA,CAAA,KAAA,KAAA,EAAA,EAAA;AACA,YAAA,IAAA,GAAA;AACA,UAAA,MAAA,EAAA,GAAA,CAAA;AADA,SAAA;AAGA,QAAA,gBAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,gBAAA,IAAA,CAAA,KAAA,IAAA,OAAA,EAAA;AACA,qBAAA,IAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;AACA;AACA,WAJA;AAKA,SANA;AAOA;AACA;AAhDA;AAzDA,C", "sourcesContent": ["<template>\n  <div className=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 110, itemWidth: 220 }\"\n      @handleEvent=\"handleEvent\"\n    />\n\n    <el-white className=\"button-group\">\n      <div>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"selectChange\"\n          height=\"68vh\"\n        />\n      </div>\n    </el-white>\n  </div>\n</template>\n\n<script>\nimport { getListTj } from \"@/api/yxgl/bdyxgl/bddzczp\";\nimport { getPdsOptionsDataList } from \"@/api/dagangOilfield/asset/pdsgl\";\nexport default {\n  name: \"czp_tj\",\n  mounted() {\n    //列表查询\n    this.getData();\n    //获取配电室下拉框数据\n    this.getPdsOptionsDataList();\n  },\n  data() {\n    return {\n      // 查询变电站列表\n      pdsOptionsDataList: [],\n      //查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      // 查询数据总条数\n      total: 0,\n      filterInfo: {\n        data: {\n          bdzmc: \"\"\n        }, //查询条件\n        fieldList: [\n          {\n            label: \"配电站名称\",\n            value: \"bdzmc\",\n            type: \"select\",\n            clearable: true,\n            filterable: true,\n            options: []\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"配电站名称\", prop: \"pdzCn\", minWidth: \"150\" },\n          { label: \"操作任务\", prop: \"czrw\", minWidth: \"100\" },\n          { label: \"操作项数\", prop: \"czxs\", minWidth: \"100\" },\n          { label: \"已执行项数\", prop: \"yzxczxs\", minWidth: \"100\" },\n          { label: \"未执行项数\", prop: \"wzxczxs\", minWidth: \"100\" }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        lx: 3,\n        status: \"4\"\n      }\n    };\n  },\n  methods: {\n    //统计\n    async getData(params) {\n      try {\n        const param = { ...this.params, ...params };\n        const { data, code } = await getListTj(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    /**\n     * 列表选中\n     * */\n    selectChange(selection) {\n      this.ids = selection.map(item => item.objId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    //获取配电室下拉框数据\n    getPdsOptionsDataList() {\n      getPdsOptionsDataList({}).then(res => {\n        this.pdsOptionsDataList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"bdzmc\") {\n            return (item.options = this.pdsOptionsDataList);\n          }\n        });\n      });\n    },\n    //下拉框change事件\n    handleEvent(val) {\n      //变电站查询下拉框选项根据所选分公司带出来\n      if (val.label === \"fgs\" && val.value && val.value !== \"\") {\n        let form = {\n          ssdwbm: val.value\n        };\n        getBdzSelectList(form).then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value == \"bdzmc\") {\n              return (item.options = res.data);\n            }\n          });\n        });\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n/*列表颜色设置*/\n/deep/ .el-table th {\n  background-color: #e8f7f0;\n}\n</style>\n"], "sourceRoot": "src/views/dagangOilfield/czpgl/pddzcz/components"}]}