{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\sbztpjbzk\\pjgzwh.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\sbztpjbzk\\pjgzwh.js", "mtime": 1706897314226}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0UGFnZUxpc3QgPSBnZXRQYWdlTGlzdDsKZXhwb3J0cy5zYXZlT3JVcGRhdGUgPSBzYXZlT3JVcGRhdGU7CmV4cG9ydHMucmVtb3ZlID0gcmVtb3ZlOwoKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Cgp2YXIgYmFzZVVybCA9ICIvY29uZGl0aW9uLW1haW50ZW5hbmNlLWFwaSI7CgpmdW5jdGlvbiBnZXRQYWdlTGlzdChwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy9wamd6L3NlbGVjdENibVBqZ3onLCBwYXJhbXMsIDEpOwp9IC8vIOa3u+WKoOaIluS/ruaUuQoKCmZ1bmN0aW9uIHNhdmVPclVwZGF0ZShwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy9wamd6L3BqZ3pBZGRPcnVwZGF0ZScsIHBhcmFtcywgMSk7Cn0gLy8g5Yig6ZmkCgoKZnVuY3Rpb24gcmVtb3ZlKHBhcmFtcykgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3RQb3N0KGJhc2VVcmwgKyAnL3BqZ3ovZGVsZXBqZ3onLCBwYXJhbXMsIDEpOwp9"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagang<PERSON>field/bzgl/sbztpjbzk/pjgzwh.js"], "names": ["baseUrl", "getPageList", "params", "api", "requestPost", "saveOrUpdate", "remove"], "mappings": ";;;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,4BAAhB;;AAEO,SAASC,WAAT,CAAqBC,MAArB,EAA6B;AAChC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,qBAAxB,EAA8CE,MAA9C,EAAqD,CAArD,CAAP;AACD,C,CAEH;;;AACO,SAASG,YAAT,CAAsBH,MAAtB,EAA8B;AACjC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,uBAAxB,EAAgDE,MAAhD,EAAuD,CAAvD,CAAP;AACD,C,CACA;;;AACM,SAASI,MAAT,CAAgBJ,MAAhB,EAAwB;AAC7B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,gBAAxB,EAAyCE,MAAzC,EAAgD,CAAhD,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\r\nconst baseUrl = \"/condition-maintenance-api\";\r\n\r\nexport function getPageList(params) {\r\n    return api.requestPost(baseUrl+'/pjgz/selectCbmPjgz',params,1)\r\n  }\r\n\r\n// 添加或修改\r\nexport function saveOrUpdate(params) {\r\n    return api.requestPost(baseUrl+'/pjgz/pjgzAddOrupdate',params,1)\r\n  }\r\n   // 删除\r\n  export function remove(params) {\r\n    return api.requestPost(baseUrl+'/pjgz/delepjgz',params,1)\r\n  }\r\n  "]}]}