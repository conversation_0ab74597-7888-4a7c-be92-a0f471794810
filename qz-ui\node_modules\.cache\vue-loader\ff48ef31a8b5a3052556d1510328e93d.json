{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\pdgl\\pdsbgl.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\pdgl\\pdsbgl.vue", "mtime": 1706897324785}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsKaW1wb3J0IHsKICBnZXRMaXN0LAogIHNhdmVPclVwZGF0ZSwKICByZW1vdmUsCiAgZ2V0UGRzYk9uZSwKfSBmcm9tICJAL2FwaS9kYWdhbmdPaWxmaWVsZC9kd3p5Z2wvcGRzYmdsL3Bkc2JqYnh4IjsKaW1wb3J0IHsKICB1cGRhdGVTdGF0dXMsCiAgZ2V0UmVzdW1EYXRhTGlzdCwKfSBmcm9tICJAL2FwaS9kYWdhbmdPaWxmaWVsZC9kd3p5Z2wvcGRzYmdsL3Bkc2J6dGJnIjsKaW1wb3J0IHsKICBhZGRQZGcsCiAgZ2V0UGRnTGlzdCwKICBnZXRQZGdMaXN0U2VsZWN0ZWQsCiAgZ2V0UGRnT25lLAogIGdldFBkc1RyZWVMaXN0LAogIHJlbW92ZVBkZywKICBnZXRNZXJnZVBkZ0luZm8sCn0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYXNzZXQvcGRnIjsKaW1wb3J0IHsgZ2V0U2JseERhdGFMaXN0U2VsZWN0ZWQgfSBmcm9tICJAL2FwaS9kYWdhbmdPaWxmaWVsZC9hc3NldC9iZHNidHoiOwppbXBvcnQgewogIGdldFBhcmFtRGF0YUxpc3QsCiAgZ2V0UGFyYW1zVmFsdWUsCiAgc2F2ZVBhcmFtVmFsdWUsCn0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYXNzZXQvcGFyYW1ldGVycyI7CmltcG9ydCB7CiAgYWRkUGRzLAogIGdldFBkc0xpc3QsCiAgZ2V0UGRzT3B0aW9uc0RhdGFMaXN0LAogIHJlbW92ZVBkcywKfSBmcm9tICJAL2FwaS9kYWdhbmdPaWxmaWVsZC9hc3NldC9wZHNnbCI7CmltcG9ydCB7IGdldERpY3RUeXBlRGF0YSB9IGZyb20gIkAvYXBpL3N5c3RlbS9kaWN0L2RhdGEiOwppbXBvcnQgeyBzZWxlY3REZXB0T25lQW5kVHdvIH0gZnJvbSAiQC9hcGkvc3lzdGVtL2RlcHQiOwppbXBvcnQgeyBkZWxldGVCeUlkLCBnZXRMaXN0QnlCdXNpbmVzc0lkIH0gZnJvbSAiQC9hcGkvdG9vbC9maWxlIjsKaW1wb3J0IHthZGRkd3p5ZnN0en0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYXNzZXQvamd0eiI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogInBkc2JnbCIsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHNza2dnOiIiLAogICAgICB0cmVlTm9kZTonJywvL+agkeiKgueCueagh+iusAogICAgICBoZWFkRm9ybToge30sIC8v5aS06YOo6KGo5Y2V5L+h5oGvCiAgICAgIHVwbG9hZERhdGE6IHsKICAgICAgICB0eXBlOiAiIiwKICAgICAgICBidXNpbmVzc0lkOiB1bmRlZmluZWQsCiAgICAgIH0sCiAgICAgIHNzenM6ICIiLAogICAgICBpY29uczogewogICAgICAgIHBkc0xpc3Q6ICJjYXRlZ29yeVRyZWVJY29ucyIsCiAgICAgICAgcGRzOiAidGFibGVJY29uIiwKICAgICAgICBwZGc6ICJjbGFzc0ljb24iLAogICAgICB9LAogICAgICBwZHpzaG93OiB0cnVlLAogICAgICBzYnNob3c6IGZhbHNlLAogICAgICBwZGdzaG93OiBmYWxzZSwKICAgICAgLy/phY3nlLXnq5nnm7jlhbMKICAgICAgLy/lvLnlh7rmoYbooajljZUKICAgICAgcGR6Zm9ybTogewogICAgICAgIGF0dGFjaG1lbnQ6IFtdLAogICAgICAgIC8vc3NnczogdW5kZWZpbmVkLAogICAgICAgIHNzeGxtYzogdW5kZWZpbmVkLAogICAgICAgIHNzeGxiaDogdW5kZWZpbmVkLAogICAgICAgIHNzeGRtYzogdW5kZWZpbmVkLAogICAgICAgIHBkc21jOiB1bmRlZmluZWQsCiAgICAgICAgeXhiaDogdW5kZWZpbmVkLAogICAgICAgIGVycEJtOiB1bmRlZmluZWQsCiAgICAgICAgYmdyOiB1bmRlZmluZWQsCiAgICAgICAgemNiZGZzOiB1bmRlZmluZWQsCiAgICAgICAgemNzeDogdW5kZWZpbmVkLAogICAgICAgIHpjYmg6IHVuZGVmaW5lZCwKICAgICAgICB3YnNZczogdW5kZWZpbmVkLAogICAgICAgIHpjeHo6IHVuZGVmaW5lZCwKICAgICAgICB0eXJxOiB1bmRlZmluZWQsCiAgICAgICAgc2ZqeWh3OiB1bmRlZmluZWQsCiAgICAgICAgenQ6IHVuZGVmaW5lZCwKICAgICAgICBkcXR6OiB1bmRlZmluZWQsCiAgICAgICAgc2NjajogdW5kZWZpbmVkLAogICAgICAgIHNnZHc6IHVuZGVmaW5lZCwKICAgICAgICBqendjYzogdW5kZWZpbmVkLAogICAgICAgIGp6d2N6OiB1bmRlZmluZWQsCiAgICAgICAgdGZmczogdW5kZWZpbmVkLAogICAgICAgIGRscWdzbDogdW5kZWZpbmVkLAogICAgICAgIGR5aGdxZ3NsOiB1bmRlZmluZWQsCiAgICAgICAgZHJnc2w6IHVuZGVmaW5lZCwKICAgICAgICBkeWp4ZnM6IHVuZGVmaW5lZCwKICAgICAgICBkeXB4Z3NsOiB1bmRlZmluZWQsCiAgICAgICAgZmhrZ2dzbDogdW5kZWZpbmVkLAogICAgICAgIGZoa2dyZHF6aGdzbDogdW5kZWZpbmVkLAogICAgICAgIGpsZ3NsOiB1bmRlZmluZWQsCiAgICAgICAgbWxnc2w6IHVuZGVmaW5lZCwKICAgICAgICBwYnNsOiB1bmRlZmluZWQsCiAgICAgICAgcGJ6cmw6IHVuZGVmaW5lZCwKICAgICAgICB3ejogdW5kZWZpbmVkLAogICAgICAgIHhkY2dzbDogdW5kZWZpbmVkLAogICAgICAgIHpsZ3NsOiB1bmRlZmluZWQsCiAgICAgICAgemJ5OiB1bmRlZmluZWQsCiAgICAgICAgZ3lqeGdzbDogdW5kZWZpbmVkLAogICAgICAgIGd5amxnc2w6IHVuZGVmaW5lZCwKICAgICAgICBneWN4Z3NsOiB1bmRlZmluZWQsCiAgICAgICAgcHRnc2w6IHVuZGVmaW5lZCwKICAgICAgICBkeWp4Z3NsOiB1bmRlZmluZWQsCiAgICAgICAgZHljeGdzbDogdW5kZWZpbmVkLAogICAgICAgIGR5YmNnc2w6IHVuZGVmaW5lZCwKICAgICAgICBkeWpsZ3NsOiB1bmRlZmluZWQsCiAgICAgICAgZHlsbGdzbDogdW5kZWZpbmVkLAogICAgICAgIGRnc2w6IHVuZGVmaW5lZCwKICAgICAgICBkZ2dkOiB1bmRlZmluZWQsCiAgICAgICAgeHNieGg6IHVuZGVmaW5lZCwKICAgICAgICBkeXBkZ3NsOiB1bmRlZmluZWQsCiAgICAgICAgbHg6IHVuZGVmaW5lZCwKICAgICAgfSwKICAgICAgcGRzRGlhbG9nRm9ybVZpc2libGU6IGZhbHNlLAogICAgICBwZHpmaWx0ZXJJbmZvOiB7CiAgICAgICAgZGF0YTogewogICAgICAgICAgc3NnczogW10sCiAgICAgICAgICBzc3hsbWM6ICIiLAogICAgICAgICAgeXhiaDogIiIsCiAgICAgICAgICB6dDogW10sCiAgICAgICAgfSwKICAgICAgICBmaWVsZExpc3Q6IFsKICAgICAgICAgIC8vIHtsYWJlbDogJ+aJgOWxnuWFrOWPuCcsIHR5cGU6ICdzZWxlY3QnLCB2YWx1ZTogJ3NzZ3MnLCBtdWx0aXBsZTogdHJ1ZSwgb3B0aW9uczogW119LAogICAgICAgICAgeyBsYWJlbDogIuaJgOWxnue6v+i3r+WQjeensCIsIHR5cGU6ICJpbnB1dCIsIHZhbHVlOiAic3N4bG1jIiB9LAogICAgICAgICAgeyBsYWJlbDogIumFjeeUteWupOWQjeensCIsIHR5cGU6ICJpbnB1dCIsIHZhbHVlOiAicGRzbWMiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5omA5bGe57q/6Lev57yW5Y+3IiwgdHlwZTogImlucHV0IiwgdmFsdWU6ICJzc3hsYmgiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5omA5bGe57q/5q615ZCN56ewIiwgdHlwZTogImlucHV0IiwgdmFsdWU6ICJzc3hkbWMiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi6L+Q6KGM57yW5Y+3IiwgdHlwZTogImlucHV0IiwgdmFsdWU6ICJ5eGJoIiB9LAogICAgICAgICAgeyBsYWJlbDogIui/kOe7tOePree7hCIsIHR5cGU6ICJpbnB1dCIsIHZhbHVlOiAieXdiem1jIiB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIuaKlei/kOaXpeacnyIsCiAgICAgICAgICAgIHR5cGU6ICJkYXRlIiwKICAgICAgICAgICAgdmFsdWU6ICJ0eXJxQXJyIiwKICAgICAgICAgICAgZGF0ZVR5cGU6ICJkYXRlcmFuZ2UiLAogICAgICAgICAgICBmb3JtYXQ6ICJ5eXl5LU1NLWRkIiwKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi5piv5ZCm5YW35pyJ546v572RIiwKICAgICAgICAgICAgdHlwZTogInNlbGVjdCIsCiAgICAgICAgICAgIHZhbHVlOiAic2ZqeWh3IiwKICAgICAgICAgICAgb3B0aW9uczogWwogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIHZhbHVlOiAi5pivIiwKICAgICAgICAgICAgICAgIGxhYmVsOiAi5pivIiwKICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIHZhbHVlOiAi5ZCmIiwKICAgICAgICAgICAgICAgIGxhYmVsOiAi5ZCmIiwKICAgICAgICAgICAgICB9LAogICAgICAgICAgICBdLAogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLnirbmgIEiLAogICAgICAgICAgICB0eXBlOiAic2VsZWN0IiwKICAgICAgICAgICAgdmFsdWU6ICJ6dCIsCiAgICAgICAgICAgIG9wdGlvbnM6IFsKICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICB2YWx1ZTogIuWcqOi/kCIsCiAgICAgICAgICAgICAgICBsYWJlbDogIuWcqOi/kCIsCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICB2YWx1ZTogIuWBnOatouS9v+eUqCIsCiAgICAgICAgICAgICAgICBsYWJlbDogIuWBnOatouS9v+eUqCIsCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICB2YWx1ZTogIuacquWwsee7qiIsCiAgICAgICAgICAgICAgICBsYWJlbDogIuacquWwsee7qiIsCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICB2YWx1ZTogIuaKpeW6nyIsCiAgICAgICAgICAgICAgICBsYWJlbDogIuaKpeW6nyIsCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgXSwKICAgICAgICAgIH0sCiAgICAgICAgICB7IGxhYmVsOiAi55Sf5Lqn5Y6C5a62IiwgdHlwZTogImlucHV0IiwgdmFsdWU6ICJzY2NqIiB9LAogICAgICAgICAgeyBsYWJlbDogIumAmumjjuaWueW8jyIsIHR5cGU6ICJpbnB1dCIsIHZhbHVlOiAidGZmcyIgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLphY3nlLXlrqTnsbvlnosiLAogICAgICAgICAgICB0eXBlOiAic2VsZWN0IiwKICAgICAgICAgICAgdmFsdWU6ICJseCIsCiAgICAgICAgICAgIG9wdGlvbnM6IFsKICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICB2YWx1ZTogIueuseW8j+WPmOeUteermSIsCiAgICAgICAgICAgICAgICBsYWJlbDogIueuseW8j+WPmOeUteermSIsCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICB2YWx1ZTogIuafseS4iuWPmOWPsOWPmCIsCiAgICAgICAgICAgICAgICBsYWJlbDogIuafseS4iuWPmOWPsOWPmCIsCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICB2YWx1ZTogIumFjeeUteWupCIsCiAgICAgICAgICAgICAgICBsYWJlbDogIumFjeeUteWupCIsCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgXSwKICAgICAgICAgIH0sCiAgICAgICAgXSwKICAgICAgfSwKICAgICAgeXdiekxpc3Q6IFtdLAogICAgICBwZHpQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgfSwKICAgICAgdGFibGVBbmRQYWdlSW5mbzE6IHsKICAgICAgICBwYWdlcjogewogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMDBdLAogICAgICAgICAgcGFnZVJlc2l6ZTogIiIsCiAgICAgICAgfSwKICAgICAgICBvcHRpb246IHsKICAgICAgICAgIGNoZWNrQm94OiB0cnVlLAogICAgICAgICAgc2VyaWFsTnVtYmVyOiB0cnVlLAogICAgICAgIH0sCiAgICAgICAgdGFibGVEYXRhOiBbXSwKICAgICAgICB0YWJsZUhlYWRlcjogWwogICAgICAgICAgeyBwcm9wOiAic3N4bG1jIiwgbGFiZWw6ICLmiYDlsZ7nur/ot6/lkI3np7AiLCBtaW5XaWR0aDogIjE4MCIgfSwKICAgICAgICAgIHsgcHJvcDogInNzeGxiaCIsIGxhYmVsOiAi5omA5bGe57q/6Lev57yW5Y+3IiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJzc3hkbWMiLCBsYWJlbDogIuaJgOWxnue6v+auteWQjeensCIsIG1pbldpZHRoOiAiMTgwIiB9LAogICAgICAgICAgeyBwcm9wOiAicGRzbWMiLCBsYWJlbDogIumFjeeUteWupOWQjeensCIsIG1pbldpZHRoOiAiMTQwIiB9LAogICAgICAgICAgeyBwcm9wOiAieXhiaCIsIGxhYmVsOiAi6L+Q6KGM57yW5Y+3IiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJ5d2J6bWMiLCBsYWJlbDogIui/kOe7tOePree7hCIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBwcm9wOiAidHlycSIsIGxhYmVsOiAi5oqV6L+Q5pel5pyfIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJzZmp5aHciLCBsYWJlbDogIuaYr+WQpuWFt+acieeOr+e9kSIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBwcm9wOiAienQiLCBsYWJlbDogIueKtuaAgSIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBwcm9wOiAic2NjaiIsIGxhYmVsOiAi55Sf5Lqn5Y6C5a62IiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJ0ZmZzIiwgbGFiZWw6ICLpgJrpo47mlrnlvI8iLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgcHJvcDogImx4IiwgbGFiZWw6ICLphY3nlLXlrqTnsbvlnosiLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIC8qewogICAgICAgICAgICAgIGZpeGVkOiAicmlnaHQiLAogICAgICAgICAgICAgIHByb3A6ICdvcGVyYXRpb24nLAogICAgICAgICAgICAgIGxhYmVsOiAn5pON5L2cJywKICAgICAgICAgICAgICBtaW5XaWR0aDogJzEzMHB4JywKICAgICAgICAgICAgICBzdHlsZToge2Rpc3BsYXk6ICdibG9jayd9LAogICAgICAgICAgICAgIG9wZXJhdGlvbjogWwogICAgICAgICAgICAgICAge25hbWU6ICfkv67mlLknLCBjbGlja0Z1bjogdGhpcy5wZHpnZXRVcGRhdGV9LAogICAgICAgICAgICAgICAge25hbWU6ICfor6bmg4UnLCBjbGlja0Z1bjogdGhpcy5wZHpnZXRYcX0sCiAgICAgICAgICAgICAgXQogICAgICAgICAgICB9LCovCiAgICAgICAgXSwKICAgICAgfSwKICAgICAgcGR6c2J6dDogWwogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAi5Zyo6L+QIiwKICAgICAgICAgIGxhYmVsOiAi5Zyo6L+QIiwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAi5YGc5q2i5L2/55SoIiwKICAgICAgICAgIGxhYmVsOiAi5YGc5q2i5L2/55SoIiwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAi5pyq5bCx57uqIiwKICAgICAgICAgIGxhYmVsOiAi5pyq5bCx57uqIiwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAi5oql5bqfIiwKICAgICAgICAgIGxhYmVsOiAi5oql5bqfIiwKICAgICAgICB9LAogICAgICBdLAogICAgICBwZHNseDogWwogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAi566x5byP5Y+Y55S156uZIiwKICAgICAgICAgIGxhYmVsOiAi566x5byP5Y+Y55S156uZIiwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAi5p+x5LiK5Y+Y5Y+w5Y+YIiwKICAgICAgICAgIGxhYmVsOiAi5p+x5LiK5Y+Y5Y+w5Y+YIiwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAi6YWN55S15a6kIiwKICAgICAgICAgIGxhYmVsOiAi6YWN55S15a6kIiwKICAgICAgICB9LAogICAgICBdLAoKICAgICAgLy/phY3nlLXmn5znm7jlhbMKICAgICAgYmRnRGlhbG9nRm9ybVZpc2libGU6IGZhbHNlLAogICAgICBzaG93QnV0dG9uOiB0cnVlLAoKICAgICAgcGRnZGlzYWJsZTpmYWxzZSwKICAgICAgLy/phY3nlLXlrqTkuIvmi4nmoYYKICAgICAgcGRnT3B0aW9uc0RhdGFMaXN0OiBbXSwKICAgICAgcGRna2d5dExpc3Q6IFtdLAogICAgICBwZGd6dExpc3Q6IFtdLAogICAgICAvL+W8ueWHuuahhuihqOWNlQogICAgICBwZGdmb3JtOiB7fSwKICAgICAgcGRnUGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgIH0sCiAgICAgIHBkZ2ZpbHRlckluZm86IHsKICAgICAgICBkYXRhOiB7CiAgICAgICAgICBrZ2dtYzogIiIsCiAgICAgICAgICB5eGJoOiAiIiwKICAgICAgICB9LAogICAgICAgIGZpZWxkTGlzdDogWwogICAgICAgICAgeyBsYWJlbDogIuaJgOWxnuermeWupOWQjeensCIsIHR5cGU6ICJpbnB1dCIsIHZhbHVlOiAic3N6c21jIiB9LAogICAgICAgICAgeyBsYWJlbDogIuaJgOWxnuermeWupOi/kOihjOe8luWPtyIsIHR5cGU6ICJpbnB1dCIsIHZhbHVlOiAic3N6c3l4YmgiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5byA5YWz5p+c5ZCN56ewIiwgdHlwZTogImlucHV0IiwgdmFsdWU6ICJrZ2dtYyIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLov5DooYznvJblj7ciLCB0eXBlOiAiaW5wdXQiLCB2YWx1ZTogInl4YmgiIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi5oqV6L+Q5pel5pyfIiwKICAgICAgICAgICAgdHlwZTogImRhdGUiLAogICAgICAgICAgICB2YWx1ZTogInR5cnFBcnIiLAogICAgICAgICAgICBkYXRlVHlwZTogImRhdGVyYW5nZSIsCiAgICAgICAgICAgIGZvcm1hdDogInl5eXktTU0tZGQiLAogICAgICAgICAgfSwKICAgICAgICAgIHsgbGFiZWw6ICLnlJ/kuqfljoLlrrYiLCB0eXBlOiAiaW5wdXQiLCB2YWx1ZTogInNjY2oiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5byA5YWz5p+c5Z6L5Y+3IiwgdHlwZTogImlucHV0IiwgdmFsdWU6ICJrZ2d4aCIgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLlh7rljoLml6XmnJ8iLAogICAgICAgICAgICB0eXBlOiAiZGF0ZSIsCiAgICAgICAgICAgIHZhbHVlOiAidHlycUFyciIsCiAgICAgICAgICAgIGRhdGVUeXBlOiAiZGF0ZXJhbmdlIiwKICAgICAgICAgICAgZm9ybWF0OiAieXl5eS1NTS1kZCIsCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIueKtuaAgSIsCiAgICAgICAgICAgIHR5cGU6ICJzZWxlY3QiLAogICAgICAgICAgICB2YWx1ZTogInp0IiwKICAgICAgICAgICAgb3B0aW9uczogWwogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIHZhbHVlOiAi5Zyo6L+QIiwKICAgICAgICAgICAgICAgIGxhYmVsOiAi5Zyo6L+QIiwKICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIHZhbHVlOiAi5YGc5q2i5L2/55SoIiwKICAgICAgICAgICAgICAgIGxhYmVsOiAi5YGc5q2i5L2/55SoIiwKICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIHZhbHVlOiAi5pyq5bCx57uqIiwKICAgICAgICAgICAgICAgIGxhYmVsOiAi5pyq5bCx57uqIiwKICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIHZhbHVlOiAi5oql5bqfIiwKICAgICAgICAgICAgICAgIGxhYmVsOiAi5oql5bqfIiwKICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIHZhbHVlOiAi5aSH55SoIiwKICAgICAgICAgICAgICAgIGxhYmVsOiAi5aSH55SoIiwKICAgICAgICAgICAgICB9LAogICAgICAgICAgICBdLAogICAgICAgICAgfSwKICAgICAgICAgIHsgbGFiZWw6ICLnlLXljovnrYnnuqciLCB0eXBlOiAiaW5wdXQiLCB2YWx1ZTogImR5ZGoiIH0sCiAgICAgICAgXSwKICAgICAgfSwKICAgICAgdGFibGVBbmRQYWdlSW5mbzI6IHsKICAgICAgICBwYWdlcjogewogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMDBdLAogICAgICAgICAgcGFnZVJlc2l6ZTogIiIsCiAgICAgICAgfSwKICAgICAgICBvcHRpb246IHsKICAgICAgICAgIGNoZWNrQm94OiB0cnVlLAogICAgICAgICAgc2VyaWFsTnVtYmVyOiB0cnVlLAogICAgICAgIH0sCiAgICAgICAgdGFibGVEYXRhOiBbXSwKICAgICAgICB0YWJsZUhlYWRlcjogWwogICAgICAgICAgeyBwcm9wOiAic3N6c21jIiwgbGFiZWw6ICLmiYDlsZ7nq5nlrqTlkI3np7AiLCBtaW5XaWR0aDogIjE4MCIgfSwKICAgICAgICAgIHsgcHJvcDogInNzenN5eGJoIiwgbGFiZWw6ICLmiYDlsZ7nq5nlrqTov5DooYznvJblj7ciLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgcHJvcDogImtnZ21jIiwgbGFiZWw6ICLlvIDlhbPmn5zlkI3np7AiLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgcHJvcDogInl4YmgiLCBsYWJlbDogIui/kOihjOe8luWPtyIsIG1pbldpZHRoOiAiMTQwIiB9LAogICAgICAgICAgeyBwcm9wOiAidHlycSIsIGxhYmVsOiAi5oqV6L+Q5pel5pyfIiwgbWluV2lkdGg6ICIxNDAiIH0sCiAgICAgICAgICB7IHByb3A6ICJzY2NqIiwgbGFiZWw6ICLnlJ/kuqfljoLlrrYiLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgcHJvcDogImtnZ3hoIiwgbGFiZWw6ICLlvIDlhbPmn5zlnovlj7ciLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgcHJvcDogImNjcnEiLCBsYWJlbDogIuWHuuWOguaXpeacnyIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBwcm9wOiAienQiLCBsYWJlbDogIueKtuaAgSIsIG1pbldpZHRoOiAiMTgwIiB9LAogICAgICAgICAgeyBwcm9wOiAiZHlkaiIsIGxhYmVsOiAi55S15Y6L562J57qnIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICAvKnsKICAgICAgICAgICAgICBmaXhlZDogInJpZ2h0IiwKICAgICAgICAgICAgICBwcm9wOiAnb3BlcmF0aW9uJywKICAgICAgICAgICAgICBsYWJlbDogJ+aTjeS9nCcsCiAgICAgICAgICAgICAgbWluV2lkdGg6ICcxMzBweCcsCiAgICAgICAgICAgICAgc3R5bGU6IHtkaXNwbGF5OiAnYmxvY2snfSwKICAgICAgICAgICAgICBvcGVyYXRpb246IFsKICAgICAgICAgICAgICAgIHtuYW1lOiAn5L+u5pS5JywgY2xpY2tGdW46IHRoaXMucGRnVXBkYXRlfSwKICAgICAgICAgICAgICAgIHtuYW1lOiAn6K+m5oOFJywgY2xpY2tGdW46IHRoaXMucGRnRGV0YWlsc30sCiAgICAgICAgICAgICAgXQogICAgICAgICAgICB9LCovCiAgICAgICAgXSwKICAgICAgfSwKICAgICAgc2VsZWN0Um93czogW10sCiAgICAgIC8v6YWN55S15a6k5LiL5ouJ5qGGCiAgICAgIHBkc09wdGlvbnNEYXRhTGlzdDogW10sCiAgICAgIC8v6YWN55S15p+c5LiL5ouJ5qGGCiAgICAgIHNiT3B0aW9uc0RhdGFMaXN0OiBbXSwKICAgICAgLy/orr7lpIfnsbvlnosKICAgICAgc2JseE9wdGlvbnNEYXRhU2VsZWN0ZWQ6IFtdLAogICAgICAvL+W3puS+p+agkeetm+mAieadoeS7tgogICAgICB0cmVlRm9ybToge30sCiAgICAgIC8v6K6+5aSH5by55Ye65qGG5qCH6aKYCiAgICAgIHNidGl0bGU6ICIiLAogICAgICAvL+eKtuaAgeWPmOabtOS/oeaBrwogICAgICB1cGRhdGVMaXN0OiB7CiAgICAgICAgenQ6ICIiLAogICAgICAgIGlkOiAiIiwKICAgICAgfSwKICAgICAgLy/nirbmgIHkv6Hmga/mn6Xor6IKICAgICAgcmVzdW1lUXVlcnk6IHsKICAgICAgICBmb3JlaWduTnVtOiB1bmRlZmluZWQsCiAgICAgIH0sCiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLAogICAgICAvL+aYr+WQpuemgeeUqAogICAgICBpc0Rpc2FibGVkOiBmYWxzZSwKICAgICAgLy/kuIrkvKDlm77niYfml7bnmoTor7fmsYLlpLQKICAgICAgaGVhZGVyOiB7fSwKICAgICAgLy8KICAgICAgdGFibGVEaXNhYmxlZDpmYWxzZSwKICAgICAgLy/ln7rmnKzkv6Hmga/nmoTnirbmgIEKICAgICAgamJ4eHp0TGlzdDogWwogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAi5Zyo6L+QIiwKICAgICAgICAgIGxhYmVsOiAi5Zyo6L+QIiwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAi5YGc5q2i5L2/55SoIiwKICAgICAgICAgIGxhYmVsOiAi5YGc5q2i5L2/55SoIiwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAi5pyq5bCx57uqIiwKICAgICAgICAgIGxhYmVsOiAi5pyq5bCx57uqIiwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAi5oql5bqfIiwKICAgICAgICAgIGxhYmVsOiAi5oql5bqfIiwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAi5aSH55SoIiwKICAgICAgICAgIGxhYmVsOiAi5aSH55SoIiwKICAgICAgICB9LAogICAgICBdLAogICAgICAvL+W8gOWFs+eUqOmAlAogICAgICBqYnh4a2d5dExpc3Q6IFsKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIui/m+e6vyIsCiAgICAgICAgICBsYWJlbDogIui/m+e6vyIsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIuWHuue6vyIsCiAgICAgICAgICBsYWJlbDogIuWHuue6vyIsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIuiBlOe7nCIsCiAgICAgICAgICBsYWJlbDogIuiBlOe7nCIsCiAgICAgICAgfSwKICAgICAgXSwKCiAgICAgIC8v5pON5L2c5py65p6E5Z6L5byPCiAgICAgIGx6amd4c0xpc3Q6IFsKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIuW8ueewpyIsCiAgICAgICAgICBsYWJlbDogIuW8ueewpyIsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIuawuOejgSIsCiAgICAgICAgICBsYWJlbDogIuawuOejgSIsCiAgICAgICAgfSwKICAgICAgXSwKCiAgICAgIC8v54Gt5byn5LuL6LSoCiAgICAgIG1oanpMaXN0OiBbCiAgICAgICAgewogICAgICAgICAgdmFsdWU6ICLnqbrmsJQiLAogICAgICAgICAgbGFiZWw6ICLnqbrmsJQiLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdmFsdWU6ICLlhYXmsrkiLAogICAgICAgICAgbGFiZWw6ICLlhYXmsrkiLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdmFsdWU6ICLnnJ/nqboiLAogICAgICAgICAgbGFiZWw6ICLnnJ/nqboiLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdmFsdWU6ICJTRjYiLAogICAgICAgICAgbGFiZWw6ICJTRjYiLAogICAgICAgIH0sCiAgICAgIF0sCgogICAgICAvL+e7nee8mOS7i+i0qAogICAgICBqeWp6TGlzdDogWwogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAi56m65rCUIiwKICAgICAgICAgIGxhYmVsOiAi56m65rCUIiwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAi5YWF5rK5IiwKICAgICAgICAgIGxhYmVsOiAi5YWF5rK5IiwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAi55yf56m6IiwKICAgICAgICAgIGxhYmVsOiAi55yf56m6IiwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAiU0Y2IiwKICAgICAgICAgIGxhYmVsOiAiU0Y2IiwKICAgICAgICB9LAogICAgICBdLAogICAgICAvL+e7nee8mOadkOi0qAogICAgICBqeWN6TGlzdDogWwogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAi55O3IiwKICAgICAgICAgIGxhYmVsOiAi55O3IiwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAi5aSN5ZCIIiwKICAgICAgICAgIGxhYmVsOiAi5aSN5ZCIIiwKICAgICAgICB9LAogICAgICBdLAoKICAgICAgLy/nu53nvJjmlrnlvI8KICAgICAganlmc0xpc3Q6IFsKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIuayuea1uOW8jyIsCiAgICAgICAgICBsYWJlbDogIuayuea1uOW8jyIsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIuW5suW8jyIsCiAgICAgICAgICBsYWJlbDogIuW5suW8jyIsCiAgICAgICAgfSwKICAgICAgXSwKICAgICAgLy/mk43kvZzmlrnlvI8KICAgICAgY3pmc0xpc3Q6IFsKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIuaJi+WKqCIsCiAgICAgICAgICBsYWJlbDogIuaJi+WKqCIsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIuiHquWKqCIsCiAgICAgICAgICBsYWJlbDogIuiHquWKqCIsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIuaJi+WKqC/oh6rliqgiLAogICAgICAgICAgbGFiZWw6ICLmiYvliqgv6Ieq5YqoIiwKICAgICAgICB9LAogICAgICBdLAogICAgICAvL+aTjeS9nOaWueW8jwogICAgICB0dGZzTGlzdDogWwogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAi5omL5YqoIiwKICAgICAgICAgIGxhYmVsOiAi5omL5YqoIiwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAi6Ieq5Yqo5byPIiwKICAgICAgICAgIGxhYmVsOiAi6Ieq5Yqo5byPIiwKICAgICAgICB9LAogICAgICBdLAogICAgICAvL+i1hOS6p+aAp+i0qAogICAgICB6enh6TGlzdDogWwogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAi5YWs55SoIiwKICAgICAgICAgIGxhYmVsOiAi5YWs55SoIiwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAi5LiT55SoIiwKICAgICAgICAgIGxhYmVsOiAi5LiT55SoIiwKICAgICAgICB9LAogICAgICBdLAogICAgICAvL+avjee6v+Wei+W8jwogICAgICBteHhzTGlzdDogWwogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAi5o6S5byPIiwKICAgICAgICAgIGxhYmVsOiAi5o6S5byPIiwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAi57q/5byPIiwKICAgICAgICAgIGxhYmVsOiAi57q/5byPIiwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAiVE1ZIiwKICAgICAgICAgIGxhYmVsOiAiVE1ZIiwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAiTE1ZIiwKICAgICAgICAgIGxhYmVsOiAiTE1ZIiwKICAgICAgICB9LAogICAgICBdLAogICAgICAvL+mFjeeUteWPmOWOi+WZqOexu+WeiwogICAgICBkeXFseExpc3Q6IFsKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIumZjeWPmOWOiyIsCiAgICAgICAgICBsYWJlbDogIumZjeWPmOWOiyIsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIuWNh+WOiyIsCiAgICAgICAgICBsYWJlbDogIuWNh+WOiyIsCiAgICAgICAgfSwKICAgICAgXSwKICAgICAgLy/phY3nlLXlj5jljovlmajnsbvlnosKICAgICAganlkakxpc3Q6IFsKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIkEiLAogICAgICAgICAgbGFiZWw6ICJBIiwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAiQiIsCiAgICAgICAgICBsYWJlbDogIkIiLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdmFsdWU6ICJDIiwKICAgICAgICAgIGxhYmVsOiAiQyIsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIkQiLAogICAgICAgICAgbGFiZWw6ICJEIiwKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAiRSIsCiAgICAgICAgICBsYWJlbDogIkUiLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdmFsdWU6ICJGIiwKICAgICAgICAgIGxhYmVsOiAiRiIsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIkgiLAogICAgICAgICAgbGFiZWw6ICJIIiwKICAgICAgICB9LAogICAgICBdLAogICAgICBydWxlczogewogICAgICAgIHNibHg6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6norr7lpIfnsbvlnosiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9LAogICAgICAgIF0sCiAgICAgICAgLypzc3pzOiBbe3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn5omA5bGe56uZ5a6kJywgdHJpZ2dlcjogJ2NoYW5nZSd9XSwqLwogICAgICAgIC8vc3NrZ2c6IFt7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfmiYDlsZ7lvIDlhbPmn5wnLCB0cmlnZ2VyOiAnY2hhbmdlJ31dLAogICAgICAgIHNibWM6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K6+5aSH5ZCN56ewIiwgdHJpZ2dlcjogImJsdXIiIH1dLAogICAgICAgIHl4Ymg6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl6L+Q6KGM57yW5Y+3IiwgdHJpZ2dlcjogImJsdXIiIH1dLAogICAgICAgIHR5cnE6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5oqV6L+Q5pel5pyfIiwgdHJpZ2dlcjogImNoYW5nZSIgfV0sCiAgICAgICAgenQ6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi54q25oCBIiwgdHJpZ2dlcjogImNoYW5nZSIgfV0sCiAgICAgICAgZHlkajogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnlLXljovnrYnnuqciLCB0cmlnZ2VyOiAiYmx1ciIgfV0sCiAgICAgIH0sCiAgICAgIC8v6K6+5aSH5Z+65pys5L+h5oGvCiAgICAgIGZpbHRlckluZm86IHsKICAgICAgICBkYXRhOiB7fSwKICAgICAgICBmaWVsZExpc3Q6IFsKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLmiYDlsZ7nq5nlrqTlkI3np7AiLAogICAgICAgICAgICB0eXBlOiAic2VsZWN0IiwKICAgICAgICAgICAgdmFsdWU6ICJwZHNMaXN0IiwKICAgICAgICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgICAgICAgIG9wdGlvbnM6IFtdLAogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLmiYDlsZ7nq5nlrqTov5DooYznvJblj7ciLAogICAgICAgICAgICB0eXBlOiAiaW5wdXQiLAogICAgICAgICAgICB2YWx1ZTogInNzenN5eGJoIiwKICAgICAgICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgICAgICAgIG9wdGlvbnM6IFtdLAogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLmiYDlsZ7lvIDlhbPmn5zlkI3np7AiLAogICAgICAgICAgICB0eXBlOiAiaW5wdXQiLAogICAgICAgICAgICB2YWx1ZTogInNza2dnbWMiLAogICAgICAgICAgICBvcHRpb25zOiBbXSwKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi5omA5bGe5byA5YWz5p+c6L+Q6KGM57yW5Y+3IiwKICAgICAgICAgICAgdHlwZTogImlucHV0IiwKICAgICAgICAgICAgdmFsdWU6ICJzc2tnZ3l4YmgiLAogICAgICAgICAgICBvcHRpb25zOiBbXSwKICAgICAgICAgIH0sCiAgICAgICAgICB7IGxhYmVsOiAi6K6+5aSH5ZCN56ewIiwgdHlwZTogImlucHV0IiwgdmFsdWU6ICJzYm1jIiB9LAogICAgICAgICAgeyBsYWJlbDogIui/kOihjOe8luWPtyIsIHR5cGU6ICJpbnB1dCIsIHZhbHVlOiAieXhiaCIgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLnirbmgIEiLAogICAgICAgICAgICB0eXBlOiAic2VsZWN0IiwKICAgICAgICAgICAgdmFsdWU6ICJ6dExpc3QiLAogICAgICAgICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgICAgICAgb3B0aW9uczogWwogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIHZhbHVlOiAi5Zyo6L+QIiwKICAgICAgICAgICAgICAgIGxhYmVsOiAi5Zyo6L+QIiwKICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIHZhbHVlOiAi5YGc5q2i5L2/55SoIiwKICAgICAgICAgICAgICAgIGxhYmVsOiAi5YGc5q2i5L2/55SoIiwKICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIHZhbHVlOiAi5pyq5bCx57uqIiwKICAgICAgICAgICAgICAgIGxhYmVsOiAi5pyq5bCx57uqIiwKICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIHZhbHVlOiAi5oql5bqfIiwKICAgICAgICAgICAgICAgIGxhYmVsOiAi5oql5bqfIiwKICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIHZhbHVlOiAi5aSH55SoIiwKICAgICAgICAgICAgICAgIGxhYmVsOiAi5aSH55SoIiwKICAgICAgICAgICAgICB9LAogICAgICAgICAgICBdLAogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLmipXov5Dml6XmnJ8iLAogICAgICAgICAgICB0eXBlOiAiZGF0ZSIsCiAgICAgICAgICAgIHZhbHVlOiAidHlycUFyciIsCiAgICAgICAgICAgIGRhdGVUeXBlOiAiZGF0ZXJhbmdlIiwKICAgICAgICAgICAgZm9ybWF0OiAieXl5eS1NTS1kZCIsCiAgICAgICAgICB9LAogICAgICAgICAgeyBsYWJlbDogIuinhOagvOWei+WPtyIsIHR5cGU6ICJpbnB1dCIsIHZhbHVlOiAiZ2d4aCIgfSwKICAgICAgICBdLAogICAgICB9LAogICAgICB0YWJsZUFuZFBhZ2VJbmZvMzogewogICAgICAgIHBhZ2VyOiB7CiAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgdG90YWw6IDAsCiAgICAgICAgICBzaXplczogWzEwLCAyMCwgNTAsIDEwMF0sCiAgICAgICAgICBwYWdlUmVzaXplOiAiIgogICAgICAgIH0sCiAgICAgICAgb3B0aW9uOiB7CiAgICAgICAgICBjaGVja0JveDogdHJ1ZSwKICAgICAgICAgIHNlcmlhbE51bWJlcjogdHJ1ZSwKICAgICAgICB9LAogICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgdGFibGVIZWFkZXI6IFsKICAgICAgICAgIHsgcHJvcDogInNzenNtYyIsIGxhYmVsOiAi5omA5bGe56uZ5a6k5ZCN56ewIiwgbWluV2lkdGg6ICIxNTAiIH0sCiAgICAgICAgICB7IHByb3A6ICJzc3pzeXhiaCIsIGxhYmVsOiAi5omA5bGe56uZ5a6k6L+Q6KGM57yW5Y+3IiwgbWluV2lkdGg6ICIxNTAiIH0sCiAgICAgICAgICB7IHByb3A6ICJzc2tnZ21jIiwgbGFiZWw6ICLmiYDlsZ7lvIDlhbPmn5zlkI3np7AiLCBtaW5XaWR0aDogIjE1MCIgfSwKICAgICAgICAgIHsgcHJvcDogInNza2dneXhiaCIsIGxhYmVsOiAi5omA5bGe5byA5YWz5p+c6L+Q6KGM57yW5Y+3IiwgbWluV2lkdGg6ICIxNTAiIH0sCiAgICAgICAgICB7IHByb3A6ICJzYm1jIiwgbGFiZWw6ICLorr7lpIflkI3np7AiLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgcHJvcDogInl4YmgiLCBsYWJlbDogIui/kOihjOe8luWPtyIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBwcm9wOiAienQiLCBsYWJlbDogIueKtuaAgSIsIG1pbldpZHRoOiAiODAiIH0sCiAgICAgICAgICB7IHByb3A6ICJ0eXJxIiwgbGFiZWw6ICLmipXov5Dml6XmnJ8iLCBtaW5XaWR0aDogIjEwMCIgfSwKICAgICAgICAgIHsgcHJvcDogImdneGgiLCBsYWJlbDogIuinhOagvOWei+WPtyIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgLyp7CiAgICAgICAgICAgICAgZml4ZWQ6ICdyaWdodCcsCiAgICAgICAgICAgICAgcHJvcDogJ29wZXJhdGlvbicsCiAgICAgICAgICAgICAgbGFiZWw6ICfmk43kvZwnLAogICAgICAgICAgICAgIG1pbldpZHRoOiAnMTIwcHgnLAogICAgICAgICAgICAgIHN0eWxlOiB7ZGlzcGxheTogJ2Jsb2NrJ30sCiAgICAgICAgICAgICAgb3BlcmF0aW9uOiBbCiAgICAgICAgICAgICAgICB7bmFtZTogJ+S/ruaUuScsIGNsaWNrRnVuOiB0aGlzLmdldFNiWGdCdXR0b259LAogICAgICAgICAgICAgICAge25hbWU6ICfor6bmg4UnLCBjbGlja0Z1bjogdGhpcy5nZXRTYlhxQnV0dG9ufQogICAgICAgICAgICAgIF0KICAgICAgICAgICAgfSovCiAgICAgICAgXSwKICAgICAgfSwKICAgICAgLy/nirbmgIHlj5jmm7TorrDlvZXkv6Hmga8KICAgICAgcmVzdW1QYWdlSW5mbzogewogICAgICAgIHBhZ2VyOiB7CiAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgdG90YWw6IDAsCiAgICAgICAgICBzaXplczogWzEwLCAyMCwgNTAsIDEwMF0sCiAgICAgICAgfSwKICAgICAgICBvcHRpb246IHsKICAgICAgICAgIGNoZWNrQm94OiB0cnVlLAogICAgICAgICAgc2VyaWFsTnVtYmVyOiB0cnVlLAogICAgICAgIH0sCiAgICAgICAgdGFibGVEYXRhOiBbXSwKICAgICAgICB0YWJsZUhlYWRlcjogWwogICAgICAgICAgeyBwcm9wOiAiZm9yZWlnbk51bSIsIGxhYmVsOiAi6K6+5aSH5ZCN56ewIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJzYmx4IiwgbGFiZWw6ICLorr7lpIfnsbvlnosiLCBtaW5XaWR0aDogIjE4MCIgfSwKICAgICAgICAgIHsgcHJvcDogImJnbHgiLCBsYWJlbDogIuWPmOabtOexu+WeiyIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBwcm9wOiAibXMiLCBsYWJlbDogIuaPj+i/sCIsIG1pbldpZHRoOiAiMjMwIiB9LAogICAgICAgICAgeyBwcm9wOiAiYmdyIiwgbGFiZWw6ICLlj5jmm7TkuroiLCBtaW5XaWR0aDogIjE0MCIgfSwKICAgICAgICAgIHsgcHJvcDogImJnc2oiLCBsYWJlbDogIuWPmOabtOaXtumXtCIsIG1pbldpZHRoOiAiMTYwIiB9LAogICAgICAgIF0sCiAgICAgIH0sCiAgICAgIHBhcmFtczogewogICAgICAgIGlkOiAiIiwKICAgICAgICBzYm1jOiAiIiwKICAgICAgICBnZ3hoOiAiIiwKICAgICAgICBkeWRqOiAiIiwKICAgICAgICB0eXJxU3RyOiBbXSwKICAgICAgICBzY2NqOiAiIiwKICAgICAgICB5eGJoOiAiIiwKICAgICAgICBiejogIiIsCiAgICAgICAgc3N6c21jOiAiIiwKICAgICAgICBzc3pzeXhiaDogIiIsCiAgICAgICAgc3NrZ2dtYzogIiIsCiAgICAgICAgc3NrZ2d5eGJoOiAiIiwKICAgICAgICB6dDogIiIsCiAgICAgICAga2d5dDogIiIsCiAgICAgIH0sCgogICAgICAvL+aKgOacr+WPguaVsOWfuuacrOS/oeaBrwogICAgICBqc2NzRm9ybToge30sCgogICAgICAvL+iuvuWkh+WxpeWOhue8uumZt+iusOW9leaVsOaNrumbhuWQiAogICAgICBzYmxscXhqbExpc3Q6IFsKICAgICAgICAvKnsKICAgICAgICAgICAgc3NnczogJ+a4r+S4nOWIhuWFrOWPuCcsCiAgICAgICAgICAgIGR6bWM6ICcx5Y+35Y+Y55S156uZJywKICAgICAgICAgICAgc2JseDogJ+S4u+WPmOWOi+WZqCcsCiAgICAgICAgICAgIHF4eHo6ICfkuKXph40nLAogICAgICAgICAgICBkeWRqOiAnMzVrVicsCiAgICAgICAgICAgIHNieGg6ICdYWFjlnovlj7cnLAogICAgICAgICAgICBzY2NqOiAnWFhY5Y6C5a62JwogICAgICAgICAgfSovCiAgICAgIF0sCiAgICAgIC8v6K6+5aSH5bGl5Y6G6K+V6aqM6K6w5b2V5pWw5o2uCiAgICAgIHNibHZzeWpsTGlzdDogWwogICAgICAgIC8qewogICAgICAgICAgICBzeXp5OiAn5bim55S1JywKICAgICAgICAgICAgc3l4ejogJ+S+i+ihjOivlemqjCcsCiAgICAgICAgICAgIHN5bWM6ICdYWFhYWCcsCiAgICAgICAgICAgIGd6ZGQ6ICdYWFjlubPlj7AnLAogICAgICAgICAgICBzeXNiOiAn5Li75Y+Y5Y6L5ZmoJywKICAgICAgICAgICAgc3liZzogJycsCiAgICAgICAgICAgIHRxOiAn5pm0JywKICAgICAgICAgICAgc3lycTogJzIwMjItMDEtMDEnLAogICAgICAgICAgICBscnI6ICflvKDkuIknLAogICAgICAgICAgICBzeWpsOiAnWFhYWFgnCiAgICAgICAgICB9Ki8KICAgICAgXSwKICAgICAgLy/orr7lpIflsaXljoZ0YWLpobUKICAgICAgc2JsbERlc2NUYWJOYW1lOiAic3lqbCIsCgogICAgICAvL+i9ruaSreWbvueJhwogICAgICBpbWdMaXN0OiBbXSwKICAgICAgLy/phY3nlLXorr7lpIfln7rmnKzkv6Hmga8KICAgICAgamJ4eEZvcm06IHt9LAogICAgICAvL+W8ueWHuuahhnRhYumhtQogICAgICBhY3RpdmVUYWJOYW1lOiAic2JEZXNjIiwKICAgICAgLy/orr7lpIflvLnlh7rmoYYKICAgICAgZGlhbG9nRm9ybVZpc2libGU6IGZhbHNlLAogICAgICAvL+WKoOi9veS/oeaBrwogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgLy/orr7lpIflsaXljoYKICAgICAgaGFuZGxlU2JsbERlc2NUYWJOYW1lQ2xpY2s6IHt9LAogICAgICAvLyDljZXlh7vkuIvmi4nmoJHpgInkuK3nmoToioLngrkKICAgICAgc2VsZWN0Tm9kZTogIiIsCiAgICAgIC8v57uE57uH5qCRCiAgICAgIHRyZWVPcHRpb25zOiBbXSwKCiAgICAgIC8v5Y+Y55S156uZ5oyC5o6l5pWw5o2uCiAgICAgIG5ld1Rlc3REYXRhOiBbXSwKICAgICAganNjc0xhYmVsTGlzdDogW10sCiAgICAgIHBhcmFtUXVlcnk6IHsKICAgICAgICBzYmx4Ym06IHVuZGVmaW5lZCwKICAgICAgfSwKICAgICAgc2JQYXJhbXM6IHt9LAogICAgICBwZGdTc3pzOicnLC8v6YWN55S15p+c5omA5bGe56uZ5a6kaWQKICAgICAgcGRnU3N6c21jOicnLC8v6YWN55S15p+c5omA5bGe56uZ5a6k5ZCN56ewCiAgICB9OwogIH0sCiAgd2F0Y2g6IHt9LAogIGFzeW5jIGNyZWF0ZWQoKSB7CiAgICBhd2FpdCB0aGlzLmdldFl3YnpMaXN0KCk7CiAgICAvL+WIneWni+WMluWKoOi9veaXtuWKoOi9veaJgOacieWPmOeUteermeS/oeaBrwogICAgdGhpcy5uZXdUZXN0RGF0YSA9IHRoaXMuYmR6TGlzdDsKICAgIC8vIHRoaXMuZ2V0RGF0YSgpOwogICAgYXdhaXQgdGhpcy5nZXRwZHpEYXRhKCk7IC8v6K+35rGC6YWN55S156uZ5pWw5o2uCiAgICBhd2FpdCB0aGlzLmluaXREaWN0RGF0YSgpOwogICAgYXdhaXQgdGhpcy5nZXRPcHRpb25zKCk7Ly/ojrflj5bkuIvmi4nmoYblrZflhbgKICAgIHRoaXMuZ2V0TmV3VHJlZUluZm8oKTsKICAgIHRoaXMuaW5pdCgpOwogICAgdGhpcy5nZXRQZHNPcHRpb25zRGF0YUxpc3QoKTsKICAgIGdldFBkZ0xpc3RTZWxlY3RlZCh7fSkudGhlbigocmVzKSA9PiB7CiAgICAgIC8v5omA5bGe6YWN55S15p+c562b5p+l5p2h5Lu2CiAgICAgIHRoaXMuZmlsdGVySW5mby5maWVsZExpc3RbMV0ub3B0aW9ucyA9IHJlcy5kYXRhOwogICAgfSk7CiAgfSwKICBtb3VudGVkKCkgewogICAgLy/ojrflj5Z0b2tlbgogICAgdGhpcy5oZWFkZXIudG9rZW4gPSBnZXRUb2tlbigpOwogIH0sCiAgbWV0aG9kczogewogICAgLy/ojrflj5bkuIvmi4nmoYblrZflhbjlgLwKICAgIGFzeW5jIGdldE9wdGlvbnMoKXsKICAgICAgYXdhaXQgdGhpcy5nZXRQZGdMeCgpOy8v6I635Y+W6YWN55S15p+c57G75Z6LCiAgICB9LAogICAgLy/ojrflj5bphY3nlLXmn5znsbvlnosKICAgIGFzeW5jIGdldFBkZ0x4KCl7CiAgICAgIGF3YWl0IGdldERpY3RUeXBlRGF0YSgnZHd6eV9wZGdfa2t5dCcpLnRoZW4ocmVzPT57CiAgICAgICAgcmVzLmRhdGEuZm9yRWFjaChpdGVtPT57CiAgICAgICAgICB0aGlzLnBkZ2tneXRMaXN0LnB1c2goe2xhYmVsOml0ZW0ubGFiZWwsdmFsdWU6aXRlbS52YWx1ZX0pCiAgICAgICAgfSkKICAgICAgfSkKICAgIH0sCiAgICB2aWV3SGVhZERldGFpbCgpewogICAgICBzd2l0Y2ggKHRoaXMudHJlZU5vZGUuaWRlbnRpZmllcil7CiAgICAgICAgY2FzZSAiMSI6Ly/phY3nlLXlrqQKICAgICAgICAgIHRoaXMucGR6Z2V0WHEodGhpcy5oZWFkRm9ybSk7CiAgICAgICAgICBicmVhazsKICAgICAgICBjYXNlICIyIjovL+mFjeeUteafnAogICAgICAgICAgdGhpcy5wZGdEZXRhaWxzKHRoaXMuaGVhZEZvcm0pOwogICAgICAgICAgYnJlYWs7CiAgICAgIH0KICAgIH0sCiAgICBiZWZvcmVVcGxvYWQoZmlsZSkgewogICAgICBjb25zdCBmaWxlU2l6ZSA9IGZpbGUuc2l6ZSA8IDEwMjQgKiAxMDI0ICogNTA7CiAgICAgIGlmICghZmlsZVNpemUpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLkuIrkvKDmlofku7blpKflsI/kuI3og73otoXov4cgNTBNQiEiKTsKICAgICAgfQogICAgfSwKICAgIGFzeW5jIGRlbGV0ZUZpbGVCeUlkKGlkKSB7CiAgICAgIGxldCB7IGNvZGUgfSA9IGF3YWl0IGRlbGV0ZUJ5SWQoaWQpOwogICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgYXdhaXQgdGhpcy5nZXRGaWxlTGlzdCgpOwogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLAogICAgICAgICAgbWVzc2FnZTogIuaWh+S7tuWIoOmZpOaIkOWKnyEiLAogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgc3VibWl0VXBsb2FkKCkgewogICAgICB0aGlzLiRyZWZzLnVwbG9hZC5zdWJtaXQoKTsKICAgIH0sCiAgICBjbGVhclVwbG9hZCgpIHsKICAgICAgaWYgKHRoaXMuJHJlZnMudXBsb2FkKSB7CiAgICAgICAgdGhpcy4kcmVmcy51cGxvYWQuY2xlYXJGaWxlcygpOwogICAgICB9CiAgICB9LAogICAgYXN5bmMgZ2V0RmlsZUxpc3QoKSB7CiAgICAgIGxldCB7IGNvZGUsIGRhdGEgfSA9IGF3YWl0IGdldExpc3RCeUJ1c2luZXNzSWQoewogICAgICAgIGJ1c2luZXNzSWQ6IHRoaXMucGR6Zm9ybS5vYmpJZCwKICAgICAgfSk7CiAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICB0aGlzLnBkemZvcm0uYXR0YWNobWVudCA9IGRhdGE7CiAgICAgICAgdGhpcy5pbWdMaXN0ID0gZGF0YS5tYXAoKGl0ZW0pID0+IHsKICAgICAgICAgIGxldCBpdGVtMSA9IHt9OwogICAgICAgICAgaXRlbTEubmFtZSA9IGl0ZW0uZmlsZU5hbWU7CiAgICAgICAgICBpdGVtMS51cmwgPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLmN1cnJIb3N0ICsgaXRlbS5maWxlVXJsOwogICAgICAgICAgcmV0dXJuIGl0ZW0xOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgYXN5bmMgZ2V0WXdiekxpc3QoKSB7CiAgICAgIGF3YWl0IHNlbGVjdERlcHRPbmVBbmRUd28oeyBwYXJlbnRJZDogMzAxMyB9KS50aGVuKChyZXMpID0+IHsKICAgICAgICB0aGlzLnl3YnpMaXN0ID0gcmVzLmRhdGE7CiAgICAgIH0pOwogICAgfSwKICAgIC8v5YiX6KGo5p+l6K+iCiAgICBhc3luYyBnZXREYXRhKHBhcmFtcykgewogICAgICBpZiAodGhpcy5wZHpzaG93KSB7CiAgICAgICAgdGhpcy5nZXRwZHpEYXRhKHBhcmFtcyk7CiAgICAgIH0KICAgICAgaWYgKHRoaXMucGRnc2hvdykgewogICAgICAgIHRoaXMuZ2V0cGRnRGF0YShwYXJhbXMpOwogICAgICB9CiAgICAgIGlmICh0aGlzLnNic2hvdykgewogICAgICAgIHRoaXMuZ2V0c2JEYXRhKHBhcmFtcyk7CiAgICAgIH0KICAgIH0sCiAgICAvL+aWsOWinuaMiemSrgogICAgQWRkU2Vuc29yQnV0dG9uKCkgewogICAgICBpZiAodGhpcy5wZHpzaG93KSB7CiAgICAgICAgdGhpcy5wZHpmb3JtID0geyBhdHRhY2htZW50OiBbXSB9OwogICAgICAgIHRoaXMuY2xlYXJVcGxvYWQoKTsKICAgICAgICB0aGlzLmltZ0xpc3QgPSBbXTsKICAgICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZTsKICAgICAgICB0aGlzLnBkc0RpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZTsKICAgICAgfQogICAgICBpZiAodGhpcy5wZGdzaG93KSB7CiAgICAgICAgdGhpcy5wZGdmb3JtLnNzenMgPSB0aGlzLnBkZ1NzenNtYzsvL+aJgOWxnuermeWupOWbnuaYvumXrumimAogICAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICAgIHRoaXMuc2hvd0J1dHRvbiA9IHRydWU7CiAgICAgICAgdGhpcy5iZGdEaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CiAgICAgICAgdGhpcy5wZGdkaXNhYmxlID0gZmFsc2U7Ly/phY3nlLXmn5zooajljZXlj6/nvJbovpEKICAgICAgfQogICAgICBpZiAodGhpcy5zYnNob3cpIHsKICAgICAgICB0aGlzLnNidGl0bGUgPSAi6K6+5aSH5paw5aKeIjsKICAgICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZTsKICAgICAgICB0aGlzLmpieHhGb3JtLnNzenMgPSB0aGlzLnNzenMKICAgICAgICB0aGlzLmdldFBkZ0xpc3RTZWxlY3RlZCgpOwogICAgICAgIHRoaXMuamJ4eEZvcm0uc3NrZ2cgPSB0aGlzLnNza2dnCiAgICAgICAgdGhpcy5pc0Rpc2FibGVkID0gZmFsc2U7CiAgICAgIH0KICAgIH0sCiAgICAvKi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0t6YWN55S156uZ55u45YWz5byA5aeLLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qLwogICAgLy/ooajmoLzlpJrpgInmoYYKICAgIHBkemhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKChpdGVtKSA9PiBpdGVtLm9iaklkKTsKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOwogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7CiAgICB9LAogICAgLy/phY3nlLXnq5nooajmoLzmlbDmja4KICAgIGFzeW5jIGdldHBkekRhdGEocGFyYW1zKSB7CiAgICAgIHRoaXMucGR6UGFyYW1zID0geyAuLi50aGlzLnBkelBhcmFtcywgLi4ucGFyYW1zIH07CiAgICAgIGNvbnN0IHBhcmFtID0gdGhpcy5wZHpQYXJhbXMKICAgICAgYXdhaXQgZ2V0UGRzTGlzdChwYXJhbSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgdGhpcy5wZHpzaG93ID0gdHJ1ZTsKICAgICAgICB0aGlzLnBkZ3Nob3cgPSB0aGlzLnNic2hvdyA9IGZhbHNlOwogICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mbzEudGFibGVEYXRhID0gcmVzLmRhdGEucmVjb3JkczsKICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8xLnRhYmxlRGF0YS5mb3JFYWNoKChpdGVtKSA9PiB7CiAgICAgICAgICB0aGlzLnl3YnpMaXN0LmZvckVhY2goKGVsZW1lbnQpID0+IHsKICAgICAgICAgICAgaWYgKGl0ZW0ueXdieiA9PSBlbGVtZW50LnZhbHVlKSB7CiAgICAgICAgICAgICAgaXRlbS55d2J6bWMgPSBlbGVtZW50LmxhYmVsOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9KTsKICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8xLnBhZ2VyLnRvdGFsID0gcmVzLmRhdGEudG90YWw7CiAgICAgIH0pOwogICAgfSwKICAgIC8v5L+d5a2Y56Gu5a6a5oyJ6ZKuCiAgICBhc3luYyBnZXREZXRlcm1pbmUoKSB7CiAgICAgIGxldCBwYXJhbXM9ewogICAgICAgIGx4OiLphY3nlLXorr7lpIciLAogICAgICAgIG1jOnRoaXMucGR6Zm9ybS5wZHNtYywKICAgICAgfQogICAgICB0aGlzLiRyZWZzWyJwZHpmb3JtIl0udmFsaWRhdGUoKHZhbGlkKSA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBhZGRQZHModGhpcy5wZHpmb3JtKS50aGVuKChyZXMpID0+IHsKICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICAgLy/mlrDlop7miJDlip/lkI7lj5HpgIHpgJrnn6UKICAgICAgICAgICAgICAgYWRkZHd6eWZzdHoocGFyYW1zKS50aGVuKHJlcyA9PnsKICAgICAgICAgICAgICAgICAgICBpZihyZXMuY29kZSA9PT0gIjAwMDAiKXsKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIHRoaXMudXBsb2FkRGF0YS5idXNpbmVzc0lkID0gcmVzLmRhdGEub2JqSWQ7CiAgICAgICAgICAgICAgdGhpcy5zdWJtaXRVcGxvYWQoKTsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKn++8jOmAmuefpeW3suWPkemAge+8ge+8gSIpOwogICAgICAgICAgICAgIHRoaXMucGRzRGlhbG9nRm9ybVZpc2libGUgPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldHBkekRhdGEoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgICB2YXIgaXNFcnJvciA9IGRvY3VtZW50LmdldEVsZW1lbnRzQnlDbGFzc05hbWUoImlzLWVycm9yIik7CiAgICAgICAgICAgIGlmIChpc0Vycm9yWzBdLnF1ZXJ5U2VsZWN0b3IoImlucHV0IikpIHsKICAgICAgICAgICAgICBpc0Vycm9yWzBdLnF1ZXJ5U2VsZWN0b3IoImlucHV0IikuZm9jdXMoKTsKICAgICAgICAgICAgfSBlbHNlIGlmIChpc0Vycm9yWzBdLnF1ZXJ5U2VsZWN0b3IoInRleHRhcmVhIikpIHsKICAgICAgICAgICAgICBpc0Vycm9yWzBdLnF1ZXJ5U2VsZWN0b3IoInRleHRhcmVhIikuZm9jdXMoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSwgMSk7CiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvL+WFs+mXremFjeeUteermeW8ueahhgogICAgaGFuZGxlQ2xvc2UoKSB7CiAgICAgIGlmICh0aGlzLnBkenNob3cpIHsKICAgICAgICB0aGlzLnBkemZvcm0gPSB7CiAgICAgICAgICBhdHRhY2htZW50OiBbXSwKICAgICAgICB9OwogICAgICAgIC8vIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAvLyAgIHRoaXMucGR6Zm9ybSA9IHRoaXMuJG9wdGlvbnMuZGF0YSgpLmZvcm0KICAgICAgICAvLyAgIHRoaXMucmVzZXRGb3JtKCdwZHpmb3JtJykKICAgICAgICAvLyB9KQogICAgICAgIHRoaXMucGRzRGlhbG9nRm9ybVZpc2libGUgPSBmYWxzZTsKICAgICAgfQogICAgICBpZiAodGhpcy5wZGdzaG93KSB7CiAgICAgICAgdGhpcy5wZGdmb3JtID0ge307CiAgICAgICAgLy8gdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIC8vICAgdGhpcy5wZGdmb3JtID0gdGhpcy4kb3B0aW9ucy5kYXRhKCkuZm9ybQogICAgICAgIC8vICAgdGhpcy5yZXNldEZvcm0oJ3BkZ2Zvcm0nKQogICAgICAgIC8vIH0pCiAgICAgICAgdGhpcy5iZGdEaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlOwogICAgICB9CiAgICB9LAogICAgLy/kv67mlLkKICAgIGFzeW5jIHBkemdldFVwZGF0ZShyb3cpIHsKICAgICAgdGhpcy5jbGVhclVwbG9hZCgpOwogICAgICB0aGlzLnBkemZvcm0gPSB7IC4uLnJvdyB9OwogICAgICB0aGlzLnBkemZvcm0uYXR0YWNobWVudCA9IFtdOwogICAgICBhd2FpdCB0aGlzLmdldEZpbGVMaXN0KCk7CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICB0aGlzLnBkc0RpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICAvL+ivpuaDhQogICAgYXN5bmMgcGR6Z2V0WHEocm93KSB7CiAgICAgIHRoaXMuY2xlYXJVcGxvYWQoKTsKICAgICAgdGhpcy5wZHpmb3JtID0geyAuLi5yb3cgfTsKICAgICAgdGhpcy5wZHpmb3JtLmF0dGFjaG1lbnQgPSBbXTsKICAgICAgYXdhaXQgdGhpcy5nZXRGaWxlTGlzdCgpOwogICAgICB0aGlzLmlzRGlzYWJsZWQgPSB0cnVlOwogICAgICB0aGlzLnBkc0RpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICAvKi0tLS0tLS0tLS0tLS0tLS0tLS0tLemFjeeUteermeebuOWFs+e7k+adny0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qLwoKICAgIC8qLS0tLS0tLS0tLS0tLS0tLS0tLS0t6YWN55S15p+c55u45YWz5byA5aeLLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qLwogICAgYXN5bmMgaW5pdERpY3REYXRhKCkgewogICAgICBsZXQgeyBkYXRhOiBwZGd6dCB9ID0gYXdhaXQgZ2V0RGljdFR5cGVEYXRhKCJwZGd6dCIpOwogICAgICB0aGlzLnBkZ3p0TGlzdCA9IHBkZ3p0OwogICAgfSwKICAgIGFzeW5jIGdldHBkZ0RhdGEocGFyYW0pIHsKICAgICAgdGhpcy5wZGdQYXJhbXMgPSB7IC4uLnRoaXMucGRnUGFyYW1zLCAuLi5wYXJhbSB9OwogICAgICBjb25zdCBwYXIgPSB0aGlzLnBkZ1BhcmFtczsKICAgICAgdHJ5IHsKICAgICAgICBsZXQgeyBkYXRhLCBjb2RlIH0gPSBhd2FpdCBnZXRNZXJnZVBkZ0luZm8ocGFyKTsKICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICB0aGlzLnBkZ3Nob3cgPSB0cnVlOwogICAgICAgICAgdGhpcy5wZHpzaG93ID0gdGhpcy5zYnNob3cgPSBmYWxzZTsKICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mbzIudGFibGVEYXRhID0gZGF0YS5yZWNvcmRzOwogICAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvMi5wYWdlci50b3RhbCA9IGRhdGEudG90YWw7CiAgICAgICAgICAvLyB0aGlzLnRhYmxlQW5kUGFnZUluZm8yLnBhZ2VyLnBhZ2VSZXNpemUgPSAnWSc7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgY29uc29sZS5sb2coZSk7CiAgICAgIH0KICAgIH0sCiAgICAvL+S/ruaUuQogICAgcGRnVXBkYXRlKHJvdykgewogICAgICBpZiAocm93LnNqbHggPT09ICJrZ2ciKSB7CiAgICAgICAgZ2V0UGRnT25lKHsgb2JqSWQ6IHJvdy5vYmpJZCB9KS50aGVuKChyZXMpID0+IHsKICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgIHRoaXMuYmRnRGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgICAgICAgICB0aGlzLnBkZ2Zvcm0gPSByZXMuZGF0YTsKICAgICAgICAgICAgdGhpcy5wZGdmb3JtLnNzenMgPSB0aGlzLnBkZ1NzenNtYzsvL+WkhOeQhuWbnuaYvgogICAgICAgICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZTsKICAgICAgICAgICAgdGhpcy5zaG93QnV0dG9uID0gdHJ1ZTsKICAgICAgICAgICAgdGhpcy5wZGdkaXNhYmxlID0gZmFsc2U7Ly/phY3nlLXmn5zooajljZXlj6/nvJbovpEKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuaTjeS9nOWksei0pSIpOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9IGVsc2UgaWYgKHJvdy5zamx4ID09PSAiYnlxIikgewogICAgICAgIGdldFBkc2JPbmUoeyBpZDogcm93Lm9iaklkIH0pLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgdGhpcy5zYnRpdGxlID0gIuiuvuWkh+S/ruaUuSI7CiAgICAgICAgICAgIHRoaXMuZGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgICAgICAgICB0aGlzLmpieHhGb3JtID0gcmVzLmRhdGE7CiAgICAgICAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICAgICAgICB0aGlzLmdldFBkZ0xpc3RTZWxlY3RlZCgpOwogICAgICAgICAgICB0aGlzLnRlY2huaWNhbFBhcmFtZXRlcnMocm93KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuaTjeS9nOWksei0pSIpOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgLy/or6bmg4UKICAgIHBkZ0RldGFpbHMocm93KSB7CiAgICAgIGlmIChyb3cuc2pseCA9PT0gImtnZyIpIHsKICAgICAgICBnZXRQZGdPbmUoeyBvYmpJZDogcm93Lm9iaklkIH0pLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgdGhpcy5iZGdEaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CiAgICAgICAgICAgIHRoaXMucGRnZm9ybSA9IHJlcy5kYXRhOwogICAgICAgICAgICB0aGlzLnBkZ2Zvcm0uc3N6cyA9IHRoaXMucGRnU3N6c21jOy8v5Zue5pi+5aSE55CGCiAgICAgICAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IHRydWU7CiAgICAgICAgICAgIHRoaXMuc2hvd0J1dHRvbiA9IGZhbHNlOwogICAgICAgICAgICB0aGlzLnBkZ2Rpc2FibGUgPSB0cnVlCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLmk43kvZzlpLHotKUiKTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSBlbHNlIGlmIChyb3cuc2pseCA9PT0gImJ5cSIpIHsKICAgICAgICBnZXRQZHNiT25lKHsgaWQ6IHJvdy5vYmpJZCB9KS50aGVuKChyZXMpID0+IHsKICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgIHRoaXMuc2J0aXRsZSA9ICLorr7lpIfor6bmg4Xmn6XnnIsiOwogICAgICAgICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZTsKICAgICAgICAgICAgdGhpcy5qYnh4Rm9ybSA9IHJlcy5kYXRhOwogICAgICAgICAgICB0aGlzLmlzRGlzYWJsZWQgPSB0cnVlOwogICAgICAgICAgICB0aGlzLmdldFBkZ0xpc3RTZWxlY3RlZCgpOwogICAgICAgICAgICB0aGlzLnRlY2huaWNhbFBhcmFtZXRlcnMocm93KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuaTjeS9nOWksei0pSIpOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgLy/kv53lrZjkv67mlLnlhoXlrrkKICAgIHNhdmUoKSB7CiAgICAgIHRoaXMuJHJlZnMucGRnZm9ybS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHRoaXMucGRnZm9ybS5zc3pzID0gdGhpcy5wZGdTc3pzOwogICAgICAgICAgYWRkUGRnKHRoaXMucGRnZm9ybSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PSAiMDAwMCIpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMuYmRnRGlhbG9nRm9ybVZpc2libGUgPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLmdldHBkZ0RhdGEoKTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLmk43kvZzlpLHotKUiKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgICB2YXIgaXNFcnJvciA9IGRvY3VtZW50LmdldEVsZW1lbnRzQnlDbGFzc05hbWUoImlzLWVycm9yIik7CiAgICAgICAgICAgIGlmIChpc0Vycm9yWzBdLnF1ZXJ5U2VsZWN0b3IoImlucHV0IikpIHsKICAgICAgICAgICAgICBpc0Vycm9yWzBdLnF1ZXJ5U2VsZWN0b3IoImlucHV0IikuZm9jdXMoKTsKICAgICAgICAgICAgfSBlbHNlIGlmIChpc0Vycm9yWzBdLnF1ZXJ5U2VsZWN0b3IoInRleHRhcmVhIikpIHsKICAgICAgICAgICAgICBpc0Vycm9yWzBdLnF1ZXJ5U2VsZWN0b3IoInRleHRhcmVhIikuZm9jdXMoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSwgMSk7CiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCgogICAgLyotLS0tLS0tLS0tLS0tLS0tLS0tLS3phY3nlLXmn5znm7jlhbPnu5PmnZ8tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovCgogICAgLy/orr7lpIfooajmoLzmlbDmja4KICAgIGFzeW5jIGdldHNiRGF0YShwYXJhbXMpIHsKICAgICAgdGhpcy5wYXJhbXMgPSB7IC4uLnRoaXMucGFyYW1zLCAuLi5wYXJhbXMgfTsKICAgICAgY29uc3QgcGFyYW0gPSB0aGlzLnBhcmFtczsKICAgICAgLy/mipXnlKjml6XmnJ/ojIPlm7Tmn6Xor6IKICAgICAgaWYgKHBhcmFtLnR5cnFTdHIgJiYgcGFyYW0udHlycVN0ci5sZW5ndGggPiAwKSB7CiAgICAgICAgcGFyYW0udHlCZWdpbkRhdGUgPSB0aGlzLmRhdGVGb3JtYXR0ZXIocGFyYW0udHlycVN0clswXSk7CiAgICAgICAgcGFyYW0udHlFbmREYXRlID0gdGhpcy5kYXRlRm9ybWF0dGVyKHBhcmFtLnR5cnFTdHJbMV0pOwogICAgICB9CiAgICAgIGNvbnN0IHsgZGF0YSwgY29kZSB9ID0gYXdhaXQgZ2V0TGlzdChwYXJhbSk7CiAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICB0aGlzLnNic2hvdyA9IHRydWU7CiAgICAgICAgdGhpcy5wZGdzaG93ID0gdGhpcy5wZHpzaG93ID0gZmFsc2U7CiAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvMy50YWJsZURhdGEgPSBkYXRhLnJlY29yZHM7CiAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvMy5wYWdlci50b3RhbCA9IGRhdGEudG90YWw7CiAgICAgICAgLy8gdGhpcy50YWJsZUFuZFBhZ2VJbmZvMy5wYWdlci5wYWdlUmVzaXplID0gJ1knOwogICAgICB9CiAgICB9LAogICAgLy/ojrflj5bphY3nlLXlrqTkuIvmi4nmoYbmlbDmja4KICAgIGdldFBkc09wdGlvbnNEYXRhTGlzdCgpIHsKICAgICAgZ2V0UGRzT3B0aW9uc0RhdGFMaXN0KHt9KS50aGVuKChyZXMpID0+IHsKICAgICAgICB0aGlzLnBkc09wdGlvbnNEYXRhTGlzdCA9IHJlcy5kYXRhCiAgICAgICAgLy/miYDlsZ7phY3nlLXlrqTnrZvmn6XmnaHku7YKICAgICAgICB0aGlzLmZpbHRlckluZm8uZmllbGRMaXN0WzBdLm9wdGlvbnMgPSB0aGlzLnBkc09wdGlvbnNEYXRhTGlzdDsKICAgICAgfSk7CiAgICB9LAogICAgLy/ojrflj5bphY3nlLXmn5zkuIvmi4nmoYbmlbDmja4KICAgIGdldFBkZ0xpc3RTZWxlY3RlZCgpIHsKICAgICAgZ2V0UGRnTGlzdFNlbGVjdGVkKHsgc3N6czogdGhpcy5qYnh4Rm9ybS5zc3pzIH0pLnRoZW4oKHJlcykgPT4gewogICAgICAgIHRoaXMuc2JPcHRpb25zRGF0YUxpc3QgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLy/phY3nlLXlrqTkuIvmi4nmoYbkuK3nmoRjaGFuZ2Xkuovku7YKICAgIHBkc09wdGlvbnNDaGFuZ2VDbGljaygpIHsKICAgICAgLy/lvZPlj5HnlJ9jaGFuZ2Xkuovku7bml7blhYjmuIXnqbrkuYvliY3nmoTkv6Hmga8KICAgICAgdGhpcy4kc2V0KHRoaXMuamJ4eEZvcm0sICJzc2tnZyIsICIiKTsKICAgICAgdGhpcy5nZXRQZGdMaXN0U2VsZWN0ZWQoKTsKICAgIH0sCiAgICAvL+aXpeacn+agvOW8j+WMliAgeXl5eS1NTS1kZAogICAgZGF0ZUZvcm1hdHRlcihkKSB7CiAgICAgIGxldCB5ZWFyID0gZC5nZXRGdWxsWWVhcigpOwogICAgICBsZXQgbW9udGggPQogICAgICAgIGQuZ2V0TW9udGgoKSA8IDkgPyAiMCIgKyAoZC5nZXRNb250aCgpICsgMSkgOiAiIiArIChkLmdldE1vbnRoKCkgKyAxKTsKICAgICAgbGV0IGRheSA9IGQuZ2V0RGF0ZSgpIDwgMTAgPyAiMCIgKyBkLmdldERhdGUoKSA6ICIiICsgZC5nZXREYXRlKCk7CiAgICAgIHJldHVybiB5ZWFyICsgIi0iICsgbW9udGggKyAiLSIgKyBkYXk7CiAgICB9LAoKICAgIC8qLS0tLS0tLS0tLS0tLS0tLS0tLS0tLeiuvuWkhy0tLS0tLS0tLS0tLS0tLS0tLS0tLSovCiAgICAvL+iuvuWkh+a3u+WKoOaMiemSrgogICAgc2JBZGRTZW5zb3JCdXR0b24oKSB7CiAgICAgIHRoaXMuc2J0aXRsZSA9ICLorr7lpIfmlrDlop4iOwogICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZTsKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gZmFsc2U7CiAgICB9LAogICAgLy/orr7lpIfln7rmnKzkv6Hmga/kv67mlLkKICAgIGdldFNiWGdCdXR0b24ocm93KSB7CiAgICAgIGdldFBkc2JPbmUoeyBpZDogcm93LmlkIH0pLnRoZW4oKHJlcykgPT4gewogICAgICAgIGlmIChyZXMuY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICB0aGlzLnNidGl0bGUgPSAi6K6+5aSH5L+u5pS5IjsKICAgICAgICAgIHRoaXMuZGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgICAgICAgdGhpcy5qYnh4Rm9ybSA9IHJlcy5kYXRhOwogICAgICAgICAgdGhpcy5pc0Rpc2FibGVkID0gZmFsc2U7CiAgICAgICAgICB0aGlzLmdldFBkZ0xpc3RTZWxlY3RlZCgpOwogICAgICAgICAgdGhpcy50ZWNobmljYWxQYXJhbWV0ZXJzKHJvdyk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuaTjeS9nOWksei0pSIpOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy/orr7lpIfor6bmg4XmjInpkq4KICAgIGdldFNiWHFCdXR0b24ocm93KSB7CiAgICAgIGdldFBkc2JPbmUoeyBpZDogcm93LmlkIH0pLnRoZW4oKHJlcykgPT4gewogICAgICAgIGlmIChyZXMuY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICB0aGlzLnNidGl0bGUgPSAi6K6+5aSH6K+m5oOF5p+l55yLIjsKICAgICAgICAgIHRoaXMuZGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgICAgICAgdGhpcy5qYnh4Rm9ybSA9IHJlcy5kYXRhOwogICAgICAgICAgdGhpcy5pc0Rpc2FibGVkID0gdHJ1ZTsKICAgICAgICAgIHRoaXMuZ2V0UGRnTGlzdFNlbGVjdGVkKCk7CiAgICAgICAgICB0aGlzLnRlY2huaWNhbFBhcmFtZXRlcnMocm93KTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5pON5L2c5aSx6LSlIik7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCgogICAgLyotLS0tLS0tLS0tLS0tLS0tLS0tLS0t6YWN55S15a6kLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi8KICAgIC8v5Yig6Zmk5oyJ6ZKuCiAgICAvL+WIoOmZpOmFjeeUteafnAogICAgcmVtb3ZlcGRnKGlkKSB7CiAgICAgIC8vIGlmICh0aGlzLmlkcy5sZW5ndGggIT09IDApIHsKICAgICAgICBsZXQgb2JqPVtdOwogICAgICBvYmoucHVzaChpZCk7CiAgICAgICAgdGhpcy4kY29uZmlybSgi5q2k5pON5L2c5bCG5rC45LmF5Yig6Zmk6K+l5pWw5o2uLCDmmK/lkKbnu6fnu60/IiwgIuaPkOekuiIsIHsKICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgICAgdHlwZTogIndhcm5pbmciLAogICAgICAgIH0pCiAgICAgICAgLnRoZW4oKCkgPT4gewogICAgICAgICAgcmVtb3ZlUGRnKG9iaikudGhlbigoeyBjb2RlIH0pID0+IHsKICAgICAgICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLAogICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5Yig6Zmk5oiQ5YqfISIsCiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIHRoaXMuZ2V0cGRnRGF0YSgpOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIuWIoOmZpOWksei0pSEiLAogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KTsKICAgICAgICAgIH0pCiAgICAgICAgIC5jYXRjaCgoKSA9PiB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgIHR5cGU6ICJpbmZvIiwKICAgICAgICAgICAgICBtZXNzYWdlOiAi5bey5Y+W5raI5Yig6ZmkIiwKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9KTsKICAgICAgLy8gfSBlbHNlIHsKICAgICAgLy8gICB0aGlzLiRtZXNzYWdlKHsKICAgICAgLy8gICAgIHR5cGU6ICJpbmZvIiwKICAgICAgLy8gICAgIG1lc3NhZ2U6ICLor7fpgInmi6noh7PlsJHkuIDmnaHmlbDmja4hIiwKICAgICAgLy8gICB9KTsKICAgICAgLy8gfQogICAgfSwKICAgIC8v5Yig6Zmk6YWN55S15a6kCiAgICBhc3luYyBkZWxldGVQZHMoaWQpIHsKICAgICAgbGV0IG9iaj1bXTsKICAgICAgb2JqLnB1c2goaWQpOwogICAgICAvLyBpZiAodGhpcy5pZHMubGVuZ3RoICE9IDApIHsKICAgICAgICB0aGlzLiRjb25maXJtKCLmraTmk43kvZzlsIbmsLjkuYXliKDpmaTor6XmlbDmja4sIOaYr+WQpue7p+e7rT8iLCAi5o+Q56S6IiwgewogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgICB0eXBlOiAid2FybmluZyIsCiAgICAgICAgfSkKICAgICAgICAgIC50aGVuKCgpID0+IHsKICAgICAgICAgICAgcmVtb3ZlUGRzKG9iaikudGhlbigoeyBjb2RlIH0pID0+IHsKICAgICAgICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLAogICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5Yig6Zmk5oiQ5YqfISIsCiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIHRoaXMuZ2V0cGR6RGF0YSgpOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIuWIoOmZpOWksei0pSEiLAogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KTsKICAgICAgICAgIH0pCiAgICAgICAgICAuY2F0Y2goKCkgPT4gewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICB0eXBlOiAiaW5mbyIsCiAgICAgICAgICAgICAgbWVzc2FnZTogIuW3suWPlua2iOWIoOmZpCIsCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSk7CiAgICAgIC8vIH0KICAgIH0sCiAgICAvL+WIoOmZpOiuvuWkhwogICAgcmVtb3Zlc2IoaWQpIHsKICAgICAgLy8gaWYgKHRoaXMuc2VsZWN0Um93cy5sZW5ndGggPCAxKSB7CiAgICAgIC8vICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7fpgInmi6nmraPnoa7nmoTmlbDmja7vvIHvvIHvvIEiKTsKICAgICAgLy8gICByZXR1cm47CiAgICAgIC8vIH0KICAgICAgLy8gbGV0IGlkcyA9IHRoaXMuc2VsZWN0Um93cy5tYXAoKGl0ZW0pID0+IHsKICAgICAgLy8gICByZXR1cm4gaXRlbS5pZDsKICAgICAgLy8gfSk7CiAgICAgIGxldCBvYmo9W107CiAgICAgIG9iai5wdXNoKGlkKTsKICAgICAgdGhpcy4kY29uZmlybSgi5q2k5pON5L2c5bCG5rC45LmF5Yig6Zmk6K+l5pWw5o2uLCDmmK/lkKbnu6fnu60/IiwgIuaPkOekuiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciLAogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICByZW1vdmUob2JqKQogICAgICAgICAgLnRoZW4oKHsgY29kZSB9KSA9PiB7CiAgICAgICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTmiJDlip8hIiwKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICJlcnJvciIsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5Yig6Zmk5aSx6LSlISIsCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pCiAgICAgICAgICAuY2F0Y2goKCkgPT4gewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICB0eXBlOiAiaW5mbyIsCiAgICAgICAgICAgICAgbWVzc2FnZTogIuW3suWPlua2iOWIoOmZpCIsCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSk7CiAgICAgICAgdGhpcy5nZXRzYkRhdGEoKTsKICAgICAgfSk7CiAgICB9LAogICAgYXN5bmMgZGVsZXRlUm93KHJvdykgewogICAgICBpZiAodGhpcy5wZHpzaG93KSB7CiAgICAgICAgdGhpcy5kZWxldGVQZHMocm93KTsKICAgICAgfQogICAgICBpZiAodGhpcy5wZGdzaG93KSB7CiAgICAgICAgdGhpcy5yZW1vdmVwZGcocm93KTsKICAgICAgfQogICAgICBpZiAodGhpcy5zYnNob3cpIHsKICAgICAgICB0aGlzLnJlbW92ZXNiKHJvdyk7CiAgICAgIH0KICAgIH0sCiAgICAvL+iOt+WPlkhlYWRGb3Jt5pWw5o2uCiAgICBhc3luYyBnZXRIZWFkRm9ybURhdGEoc2JJZCl7CiAgICAgIGlmKHRoaXMudHJlZU5vZGUuaWRlbnRpZmllciA9PT0gIjEiKXsvL+mFjeeUteermQogICAgICAgIGF3YWl0IGdldFBkc0xpc3Qoe29iaklkOnNiSWR9KS50aGVuKChyZXMpID0+IHsKICAgICAgICAgIHRoaXMuaGVhZEZvcm0gPSByZXMuZGF0YS5yZWNvcmRzWzBdOwogICAgICAgICAgdGhpcy55d2J6TGlzdC5mb3JFYWNoKChlbGVtZW50KSA9PiB7CiAgICAgICAgICAgIGlmICh0aGlzLmhlYWRGb3JtLnl3YnogPT0gZWxlbWVudC52YWx1ZSkgewogICAgICAgICAgICAgIHRoaXMuaGVhZEZvcm0ueXdiem1jID0gZWxlbWVudC5sYWJlbDsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICAgIH1lbHNlIGlmKHRoaXMudHJlZU5vZGUuaWRlbnRpZmllciA9PT0gIjIiKXsvL+mFjeeUteafnAogICAgICAgIGxldCB7IGRhdGEsIGNvZGUgfSA9IGF3YWl0IGdldE1lcmdlUGRnSW5mbyh7b2JqSWQ6c2JJZH0pOwogICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIHRoaXMuaGVhZEZvcm0gPSBkYXRhLnJlY29yZHNbMF07CiAgICAgICAgICBpZiAodGhpcy5oZWFkRm9ybS5zamx4ID09PSAia2dnIil7CiAgICAgICAgICAgIHRoaXMuc3NrZ2cgPSB0aGlzLmhlYWRGb3JtLm9iaklkCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgLy/moJHngrnlh7vkuovku7YKICAgIGhhbmRsZU5vZGVDbGljayhkYXRhLCBlKSB7CiAgICAgIHRoaXMudHJlZU5vZGUgPSBkYXRhOwogICAgICAvL+eCueWHu+agueiKgueCueaYvuekuuaJgOaciQogICAgICBpZiAoZGF0YS5pZGVudGlmaWVyID09PSAiMCIpIHsKICAgICAgICB0aGlzLnBkelBhcmFtcyA9IHsKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgfQogICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mbzEucGFnZXIucGFnZVJlc2l6ZSA9ICdZJzsKICAgICAgICB0aGlzLmdldHBkekRhdGEoKTsKICAgICAgfQogICAgICAvL+eCueWHu+mFjeeUteWupOiKgueCueaYvuekuumZhOWxnuiuvuWkh++8iOWPquW9kuWxnuS4jumFjeeUteWupOS4jeW9kuWxnuS7u+S9lemFjeeUteafnOeahOiuvuWkh++8iQogICAgICBpZiAoZGF0YS5pZGVudGlmaWVyID09PSAiMSIpIHsKICAgICAgICAvL+mHjee9rkN1cnJlbnRwYWdl5p+l6K+iCiAgICAgICAgdGhpcy5wZGdQYXJhbXMgPSB7CiAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIH0KICAgICAgICB0aGlzLnNzenMgPSBkYXRhLmlkOwogICAgICAgIHRoaXMucGRnU3N6cyA9IGRhdGEuaWQ7CiAgICAgICAgdGhpcy5wZGdTc3pzbWMgPSBkYXRhLmxhYmVsOwogICAgICAgIHRoaXMuZ2V0SGVhZEZvcm1EYXRhKGRhdGEuaWQpOwogICAgICAgIHRoaXMucGRnUGFyYW1zLnNzenMgPSB0aGlzLnNzenM7CiAgICAgICAgLy/ph43nva5wYWdlTnVt5pi+56S6CiAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvMi5wYWdlci5wYWdlUmVzaXplID0gJ1knOwogICAgICAgIHRoaXMuZ2V0cGRnRGF0YSgpOwogICAgICB9CiAgICAgIC8v54K55Ye76YWN55S15p+c6IqC54K55pi+56S65omA5bGe6YWN55S16K6+5aSHCiAgICAgIGlmIChkYXRhLmlkZW50aWZpZXIgPT09ICIyIikgewogICAgICAgIHRoaXMucGFyYW1zID0gewogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICB9CiAgICAgICAgdGhpcy5wYXJhbXMucGRnTGlzdCA9IFtkYXRhLmlkXTsKICAgICAgICB0aGlzLnBhcmFtcy5zc3pzID0gdGhpcy5zc3pzOwogICAgICAgIHRoaXMucGFyYW1zLnNza2dnID0gZGF0YS5pZDsKICAgICAgICB0aGlzLnNza2dnPSIiCiAgICAgICAgdGhpcy5nZXRIZWFkRm9ybURhdGEoZGF0YS5pZCk7CiAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvMy5wYWdlci5wYWdlUmVzaXplID0gJ1knOwogICAgICAgIHRoaXMuZ2V0c2JEYXRhKCk7CiAgICAgIH0KICAgIH0sCiAgICAvL+mHjee9ruaMiemSrgogICAgZmlsdGVyUmVzZXQoKSB7fSwKICAgIC8qLS0tLS0tLS0tLS0tLS0tLS0tLS0tLeeKtuaAgeWPmOabtC0tLS0tLS0tLS0tLS0tLS0tLS0tLSovCiAgICAvL+S/ruaUueiuvuWkh+eKtuaAgQogICAgdXBkYXRlU3RhdHVzKHJvdykgewogICAgICB0aGlzLnVwZGF0ZUxpc3QuenQgPSByb3cuenQ7CiAgICAgIHRoaXMudXBkYXRlTGlzdC5pZCA9IHJvdy5pZDsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICAvL+eKtuaAgeWPmOabtOaPkOS6pOS/oeaBrwogICAgc3VibWl0U3RhdHVzKCkgewogICAgICB0aGlzLiRjb25maXJtKCLnoa7orqTlsIborr7lpIfnirbmgIHkv67mlLnkuLoiICsgdGhpcy51cGRhdGVMaXN0Lnp0ICsgIj8iLCAiIiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIsCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gewogICAgICAgIHVwZGF0ZVN0YXR1cyh0aGlzLnVwZGF0ZUxpc3QpLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgaWYgKHJlcy5jb2RlID09ICIwMDAwIikgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuiuvuWkh+eKtuaAgeW3suWPmOabtO+8gSIpOwogICAgICAgICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgICAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIC8v5p+l6K+i54q25oCB5Y+Y5pu06K6w5b2V5L+h5oGvCiAgICBnZXRSZXN1bUxpc3QocGFyKSB7CiAgICAgIGxldCBwYXJhbXMgPSB7IC4uLnBhciwgLi4udGhpcy5yZXN1bWVRdWVyeSB9OwogICAgICBnZXRSZXN1bURhdGFMaXN0KHBhcmFtcykudGhlbigocmVzKSA9PiB7CiAgICAgICAgdGhpcy5yZXN1bVBhZ2VJbmZvLnRhYmxlRGF0YSA9IHJlcy5kYXRhLnJlY29yZHM7CiAgICAgICAgdGhpcy5yZXN1bVBhZ2VJbmZvLnBhZ2VyLnRvdGFsID0gcmVzLmRhdGEudG90YWw7CiAgICAgIH0pOwogICAgfSwKICAgIC8v5bem5L6n5qCR5b2i5pWw5o2u6I635Y+WCiAgICBnZXROZXdUcmVlSW5mbygpIHsKICAgICAgZ2V0UGRzVHJlZUxpc3QodGhpcy50cmVlRm9ybSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgdGhpcy50cmVlT3B0aW9ucyA9IHJlcy5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICAvL+etm+mAieadoeS7tgogICAgc2VsZWN0Q2hhbmdlKHJvd3MpIHsKICAgICAgdGhpcy5zZWxlY3RSb3dzID0gcm93czsKICAgIH0sCiAgICAvL+mHjee9ruihqOWNlQogICAgcmVzZXRGb3JtKCkgewogICAgICB0aGlzLmpieHhGb3JtID0ge307CiAgICAgIHRoaXMuanNjc0Zvcm0gPSB7fTsKICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgIHRoaXMuJHJlZnNbImpieHhGb3JtIl0uY2xlYXJWYWxpZGF0ZSgpOwogICAgICB9KTsKICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlOwogICAgfSwKICAgIGdldFNibHhEYXRhTGlzdFNlbGVjdGVkKCkgewogICAgICBsZXQgc2JseFBhcmFtID0gewogICAgICAgIHR5cGU6ICLphY3nlLXorr7lpIciLAogICAgICB9OwogICAgICBnZXRTYmx4RGF0YUxpc3RTZWxlY3RlZChzYmx4UGFyYW0pLnRoZW4oKHJlcykgPT4gewogICAgICAgIHRoaXMuc2JseE9wdGlvbnNEYXRhU2VsZWN0ZWQgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgc2hvd1BhcmFtcyhkYXRhKSB7CiAgICAgIHRoaXMucGFyYW1RdWVyeS5zYmx4Ym0gPSBkYXRhOwogICAgICB0aGlzLmdldFBhcmFtZXRlcnMoKTsKICAgIH0sCiAgICB0ZWNobmljYWxQYXJhbWV0ZXJzKHJvdykgewogICAgICAvL+iuvuWkh+exu+WeiwogICAgICB0aGlzLmpzY3NGb3JtID0ge307CiAgICAgIHRoaXMucGFyYW1RdWVyeS5zYmx4Ym0gPSByb3cuc2JseDsKICAgICAgdGhpcy5qc2NzRm9ybS5zYmx4Ym0gPSByb3cuc2JseDsKICAgICAgdGhpcy5qc2NzRm9ybS5zYmJtID0gcm93LnNzenN5eGJoOwogICAgICB0aGlzLmdldFBhcmFtZXRlcnMoKTsKICAgIH0sCiAgICBnZXRQYXJhbWV0ZXJzKCkgewogICAgICB0aGlzLmpzY3NMYWJlbExpc3QgPSBbXTsKICAgICAgZ2V0UGFyYW1EYXRhTGlzdCh0aGlzLnBhcmFtUXVlcnkpLnRoZW4oKHJlcykgPT4gewogICAgICAgIHRoaXMuanNjc0xhYmVsTGlzdCA9IHJlcy5kYXRhOwogICAgICAgIHRoaXMuZ2V0UGFyYW1WYWx1ZSgpOwogICAgICB9KTsKICAgIH0sCgogICAgZ2V0UGFyYW1WYWx1ZSgpIHsKICAgICAgZ2V0UGFyYW1zVmFsdWUodGhpcy5qc2NzRm9ybSkudGhlbigocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5kYXRhICE9ICIiKSB7CiAgICAgICAgICB0aGlzLmpzY3NGb3JtID0geyAuLi5yZXMuZGF0YVswXSB9OwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgc3VibWl0KCkgewogICAgICB0cnkgewogICAgICAgIHRoaXMuamJ4eEZvcm0uc2JDbGFzc0NzVmFsdWUgPSB0aGlzLmpzY3NGb3JtOwogICAgICAgIGxldCB7IGNvZGUgfSA9IHNhdmVPclVwZGF0ZSh0aGlzLmpieHhGb3JtKS50aGVuKChyZXMpID0+IHsKICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgICAgICBtZXNzYWdlOiAi5pON5L2c5oiQ5YqfISIsCiAgICAgICAgICAgIH0pOwogICAgICAgICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgY29uc29sZS5sb2coZSk7CiAgICAgIH0KICAgIH0sCiAgICBpbml0KCkgewogICAgICB0aGlzLmdldFNibHhEYXRhTGlzdFNlbGVjdGVkKCk7CiAgICB9LAogIH0sCn07Cg=="}, {"version": 3, "sources": ["pdsbgl.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsuDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "pdsbgl.vue", "sourceRoot": "src/views/dagangOilfield/dwzygl/pdgl", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"1\">\n      <el-col :span=\"5\">\n        <el-card shadow=\"never\" style=\"background: #e0f8ed; padding-top: 10px\">\n          <div style=\"overflow: auto; height: 89vh\">\n            <el-col>\n              <el-tree\n                :expand-on-click-node=\"true\"\n                highlight-current\n                ref=\"tree\"\n                id=\"tree\"\n                :data=\"treeOptions\"\n                :default-expanded-keys=\"['0']\"\n                @node-click=\"handleNodeClick\"\n                node-key=\"id\"\n                accordion\n              >\n                <span slot-scope=\"{ node, data }\">\n                  <i :class=\"icons[data.icon]\" />\n                  <span style=\"margin-left: 5px\" :title=\"data.label\">{{\n                    data.label\n                  }}</span>\n                </span>\n              </el-tree>\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--右侧列表-->\n      <el-col :span=\"19\" v-if=\"treeNode.identifier && treeNode.identifier !== '0'\" style=\"height: 160px;\">\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button\n              @click=\"viewHeadDetail\"\n              type=\"primary\"\n            >\n              详情\n            </el-button>\n          </div>\n          <div>\n            <!--     配电室基本信息     -->\n            <el-form\n              :model=\"headForm\"\n              label-width=\"120px\"\n              v-if=\"treeNode.identifier === '1'\"\n            >\n              <el-row :gutter=\"20\">\n                <el-col :span=\"6\">\n                  <el-form-item label=\"所属线路名称:\">\n                    <el-input\n                      v-model=\"headForm.ssxlmc\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"所属线路编号:\">\n                    <el-input\n                      v-model=\"headForm.ssxlbh\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"所属线段名称:\">\n                    <el-input\n                      v-model=\"headForm.ssxdmc\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"配电室名称:\">\n                    <el-input\n                      v-model=\"headForm.pdsmc\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n              <el-row :gutter=\"15\">\n                <el-col :span=\"6\">\n                  <el-form-item label=\"运行编号：\">\n                    <el-input\n                      v-model=\"headForm.yxbh\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"运维班组：\">\n                    <el-input\n                      v-model=\"headForm.ywbzmc\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"投运日期：\">\n                    <el-input\n                      v-model=\"headForm.tyrq\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"是否具有环网：\">\n                    <el-input\n                      v-model=\"headForm.sfjyhw\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-row>\n            </el-form>\n            <!--     配电柜基本信息     -->\n            <el-form\n              :model=\"headForm\"\n              label-width=\"140px\"\n              v-if=\"treeNode.identifier === '2'\"\n            >\n              <el-row :gutter=\"20\">\n                <el-col :span=\"6\">\n                  <el-form-item label=\"所属站室名称:\">\n                    <el-input\n                      v-model=\"headForm.sszsmc\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"所属站室运行编号:\">\n                    <el-input\n                      v-model=\"headForm.sszsyxbh\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"开关柜名称:\">\n                    <el-input\n                      v-model=\"headForm.kggmc\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"运行编号:\">\n                    <el-input\n                      v-model=\"headForm.yxbh\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n              <el-row :gutter=\"15\">\n                <el-col :span=\"6\">\n                  <el-form-item label=\"投运日期：\">\n                    <el-input\n                      v-model=\"headForm.tyrq\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"生产厂家：\">\n                    <el-input\n                      v-model=\"headForm.sccj\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"开关柜型号：\">\n                    <el-input\n                      v-model=\"headForm.kggxh\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"出厂日期：\">\n                    <el-input\n                      v-model=\"headForm.ccrq\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-row>\n            </el-form>\n          </div>\n        </el-white>\n      </el-col>\n        <!-- <el-filter\n          ref=\"filter1\"\n          :data=\"pdzfilterInfo.data\"\n          :field-list=\"pdzfilterInfo.fieldList\"\n          :width=\"{ labelWidth: 120, itemWidth: 180 }\"\n          @handleReset=\"filterReset\"\n          v-show=\"pdzshow\"\n        />\n        <el-filter\n          ref=\"filter1\"\n          :data=\"pdgfilterInfo.data\"\n          :field-list=\"pdgfilterInfo.fieldList\"\n          :width=\"{ labelWidth: 120, itemWidth: 180 }\"\n          v-show=\"pdgshow\"\n        />\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 140, itemWidth: 190 }\"\n          @handleReset=\"filterReset\"\n          v-show=\"sbshow\"\n        /> -->\n      <el-col :span=\"19\">\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button\n              icon=\"el-icon-plus\"\n              @click=\"AddSensorButton\"\n              v-hasPermi=\"['pdsbtz:button:add']\"\n              type=\"primary\"\n            >\n              新增\n            </el-button>\n\n          </div>\n\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo1\"\n            @update:multipleSelection=\"pdzhandleSelectionChange\"\n            height=\"78.2vh\"\n            v-show=\"pdzshow\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"pdzgetUpdate(scope.row)\"\n                  v-hasPermi=\"['pdsbtz:button:update']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                >\n                </el-button>\n                <el-button\n                  @click=\"pdzgetXq(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n                <el-button title=\"删除\" icon=\"el-icon-delete\" v-if=\"scope.row.createBy === $store.getters.name\"\n                  v-hasPermi=\"['pdsbtz:button:delete']\" type=\"text\" @click=\"deleteRow(scope.row.objId)\">\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo2\"\n            @update:multipleSelection=\"pdzhandleSelectionChange\"\n            height=\"61.4vh\"\n            v-show=\"pdgshow\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"pdgUpdate(scope.row)\"\n                  v-hasPermi=\"['pdsbtz:button:update']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                >\n                </el-button>\n                <el-button\n                  @click=\"pdgDetails(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n                <el-button title=\"删除\" icon=\"el-icon-delete\"\n                  v-hasPermi=\"['pdsbtz:button:delete']\" type=\"text\" @click=\"deleteRow(scope.row.objId)\">\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo3\"\n            @update:multipleSelection=\"selectChange\"\n            height=\"61.4vh\"\n            v-show=\"sbshow\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"getSbXgButton(scope.row)\"\n                  v-hasPermi=\"['pdsbtz:button:update']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                >\n                </el-button>\n                <el-button\n                  @click=\"getSbXqButton(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n                <el-button title=\"删除\" icon=\"el-icon-delete\" v-if=\"scope.row.createBy === $store.getters.name\"\n                  v-hasPermi=\"['pdsbtz:button:delete']\" type=\"text\" @click=\"deleteRow(scope.row.objId)\"></el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!--配电室新增、修改、详情弹框-->\n    <el-dialog\n    v-dialogDrag\n      title=\"配电站\"\n      :visible.sync=\"pdsDialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"70%\"\n      @close=\"handleClose\"\n    >\n      <el-form\n        ref=\"pdzform\"\n        :model=\"pdzform\"\n        :disabled=\"isDisabled\"\n        :rules=\"rules\"\n        label-width=\"130px\"\n      >\n        <div\n          class=\"block\"\n          style=\"width: 50%; height: 50%; margin-bottom: 2%; float: right\"\n        >\n          <span class=\"demonstration\">配电站图片</span>\n          <el-carousel\n            trigger=\"click\"\n            class=\"imgCls\"\n            indicator-position=\"none\"\n            :interval=\"2000\"\n            type=\"card\"\n            id=\"imgId\"\n          >\n            <el-carousel-item v-for=\"(img, index) in imgList\" :key=\"index\">\n              <viewer :images=\"imgList\">\n                <img :src=\"img.url\" width=\"100%\" height=\"100%\"/>\n              </viewer>\n            </el-carousel-item>\n          </el-carousel>\n        </div>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item  label=\"配电室类型：\" prop=\"lx\">\n              <el-select\n                v-model=\"pdzform.lx\"\n                placeholder=\"请选择配电室类型\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in pdslx\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item  label=\"所属线路名称：\" prop=\"ssxlmc\">\n              <el-input\n                v-model=\"pdzform.ssxlmc\"\n                placeholder=\"请输入所属线路名称\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属线路编号：\" prop=\"ssxlbh\">\n              <el-input\n                v-model=\"pdzform.ssxlbh\"\n                placeholder=\"请输入所属线路编号\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属线段名称：\" prop=\"ssxdmc\">\n              <el-input\n                v-model=\"pdzform.ssxdmc\"\n                placeholder=\"请输入所属线段名称\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"配电室名称：\" prop=\"pdsmc\">\n              <el-input\n                v-model=\"pdzform.pdsmc\"\n                placeholder=\"请输入配电室名称\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"运行编号：\" prop=\"yxbh\">\n              <el-input\n                v-model=\"pdzform.yxbh\"\n                placeholder=\"请输入运行编号\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"运维班组：\" prop=\"ywbz\">\n              <el-select\n                v-model=\"pdzform.ywbz\"\n                placeholder=\"请选择运维班组\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in ywbzList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"投运日期：\" prop=\"tyrq\">\n              <el-date-picker\n                type=\"date\"\n                placeholder=\"选择日期\"\n                value-format=\"yyyy-MM-dd\"\n                v-model=\"pdzform.tyrq\"\n                style=\"width: 100%\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"状态：\" prop=\"zt\">\n              <el-select v-model=\"pdzform.zt\" placeholder=\"请选择状态\">\n                <el-option\n                  v-for=\"item in pdzsbzt\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item v-if=\"pdzform.lx == '箱式变电站'\"  label=\"生产厂家：\" prop=\"sccj\">\n              <el-input v-model=\"pdzform.sccj\" placeholder=\"请输入生产厂家\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"通风方式：\" prop=\"tffs\">\n              <el-input\n                v-model=\"pdzform.tffs\"\n                placeholder=\"请输入通风方式\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"站变用：\" prop=\"zby\">\n              <el-input-number\n                v-model=\"pdzform.zby\"\n                :min=\"0\"\n                placeholder=\"请输入站变用\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item v-if=\"!(pdzform.lx == '柱上变台变')\"  label=\"高压进线柜数量：\" prop=\"gyjxgsl\">\n              <el-input-number\n                v-model=\"pdzform.gyjxgsl\"\n                :min=\"0\"\n                placeholder=\"请输入高压进线柜数量\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"高压计量柜数量：\" prop=\"gyjlgsl\">\n              <el-input-number\n                v-model=\"pdzform.gyjlgsl\"\n                :min=\"0\"\n                placeholder=\"请输入高压计量柜数量\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item v-if=\"!(pdzform.lx == '柱上变台变')\" label=\"高压出线柜数量：\" prop=\"gycxgsl\">\n              <el-input-number\n                v-model=\"pdzform.gycxgsl\"\n                :min=\"0\"\n                placeholder=\"请输入高压出线柜数量\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"PT柜数量：\" prop=\"ptgsl\">\n              <el-input-number\n                v-model=\"pdzform.ptgsl\"\n                :min=\"0\"\n                placeholder=\"请输入PT柜数量\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"母联柜数量：\" prop=\"mlgsl\">\n              <el-input-number\n                v-model=\"pdzform.mlgsl\"\n                :min=\"0\"\n                placeholder=\"请输入母联柜数量\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item v-if=\"!(pdzform.lx == '柱上变台变')\" label=\"低压进线柜数量：\" prop=\"dyjxgsl\">\n              <el-input-number\n                v-model=\"pdzform.dyjxgsl\"\n                :min=\"0\"\n                placeholder=\"低压进线柜数量\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"低压出线柜数量：\" prop=\"dycxgsl\">\n              <el-input-number\n                v-model=\"pdzform.dycxgsl\"\n                :min=\"0\"\n                placeholder=\"请输入低压出线柜数量\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item v-if=\"!(pdzform.lx == '柱上变台变')\" label=\"低压补偿柜数量：\" prop=\"dybcgsl\">\n              <el-input-number\n                v-model=\"pdzform.dybcgsl\"\n                :min=\"0\"\n                placeholder=\"请输入低压补偿柜数量\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item v-if=\"!(pdzform.lx == '柱上变台变')\" label=\"低压计量柜数量：\" prop=\"dyjlgsl\">\n              <el-input-number\n                v-model=\"pdzform.dyjlgsl\"\n                :min=\"0\"\n                placeholder=\"请输入低压计量柜数量\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item v-if=\"!(pdzform.lx == '柱上变台变')\" label=\"低压联络柜数量：\" prop=\"dyllgsl\">\n              <el-input-number\n                v-model=\"pdzform.dyllgsl\"\n                :min=\"0\"\n                placeholder=\"请输入低压联络柜数量\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"配变数量：\" prop=\"pbsl\">\n              <el-input-number\n                v-model=\"pdzform.pbsl\"\n                :min=\"0\"\n                placeholder=\"请输入配变数量\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"配变总容量：\" prop=\"pbzrl\">\n              <el-input-number\n                v-model=\"pdzform.pbzrl\"\n                :min=\"0\"\n                placeholder=\"请输入配变总容量(KVA)\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"蓄电池柜数量：\" prop=\"xdcgsl\">\n              <el-input-number\n                v-model=\"pdzform.xdcgsl\"\n                :min=\"0\"\n                placeholder=\"请输入蓄电池柜数量\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"直流柜数量：\" prop=\"zlgsl\">\n              <el-input-number\n                v-model=\"pdzform.zlgsl\"\n                :min=\"0\"\n                placeholder=\"请输入直流柜数量\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"电杆数量：\" prop=\"dgsl\">\n              <el-input-number\n                v-model=\"pdzform.dgsl\"\n                :min=\"0\"\n                placeholder=\"请输入电杆数量\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"电杆高度(m)：\" prop=\"dggd\">\n              <el-input-number\n                v-model=\"pdzform.dggd\"\n                :min=\"0\"\n                placeholder=\"请输入电杆高度\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item v-if=\"pdzform.lx == '箱式变电站'\"  label=\"箱式变型号：\" prop=\"xsbxh\">\n              <el-input\n                v-model=\"pdzform.xsbxh\"\n                placeholder=\"请输入箱式变型号\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"低压配电柜数量：\" prop=\"dypdgsl\">\n              <el-input-number\n                v-model=\"pdzform.dypdgsl\"\n                :min=\"0\"\n                placeholder=\"请输入低压配电柜数量\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"计量柜数量：\" prop=\"jlgsl\">\n              <el-input-number\n                v-model=\"pdzform.jlgsl\"\n                :min=\"0\"\n                placeholder=\"请输入计量柜数量\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"经度\" prop=\"jd\">\n              <el-input\n                v-model=\"pdzform.jd\"\n                placeholder=\"请输入经度\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"纬度\" prop=\"wd\">\n              <el-input v-model=\"pdzform.wd\" placeholder=\"请输入纬度\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否具有环网：\" prop=\"sfjyhw\">\n              <el-select\n                v-model=\"pdzform.sfjyhw\"\n                placeholder=\"请选择是否具有环网\"\n                :disabled=\"isDisabled\"\n                clearable\n                style=\"width: 80%\"\n              >\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\" prop=\"bz\">\n              <el-input\n                v-model=\"pdzform.bz\"\n                type=\"textarea\"\n                rows=\"2\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-form-item\n            label=\"已上传图片：\"\n            v-if=\"pdzform.attachment.length > 0\"\n            id=\"pic_form\"\n          >\n            <el-col\n              :span=\"24\"\n              v-for=\"(item, index) in pdzform.attachment\"\n              style=\"margin-left: 0\"\n            >\n              <el-form-item :label=\"(index + 1).toString()\">\n                {{ item.fileOldName }}\n                <el-button\n                  v-if=\"!isDisabled\"\n                  type=\"error\"\n                  size=\"mini\"\n                  @click=\"deleteFileById(item.fileId)\"\n                  >删除\n                </el-button>\n              </el-form-item>\n            </el-col>\n          </el-form-item>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-form-item label=\"上传图片：\" v-if=\"!isDisabled\">\n            <el-upload\n              list-type=\"picture-card\"\n              class=\"upload-demo\"\n              accept=\".jpg,.png\"\n              ref=\"upload\"\n              :headers=\"header\"\n              action=\"/isc-api/file/upload\"\n              :before-upload=\"beforeUpload\"\n              :data=\"uploadData\"\n              single\n              :auto-upload=\"false\"\n              multiple\n            >\n              <el-button slot=\"trigger\" type=\"primary\">选取文件</el-button>\n              <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png文件</div>\n            </el-upload>\n          </el-form-item>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\" v-show=\"!isDisabled\">\n        <el-button @click=\"pdsDialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"getDetermine\">确 定</el-button>\n      </div>\n    </el-dialog>\n\n    <!--配电柜新增、修改、详情弹框-->\n    <el-dialog\n    v-dialogDrag\n      title=\"配电柜详情\"\n      :visible.sync=\"bdgDialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"60%\"\n      @close=\"handleClose\"\n    >\n      <el-form\n        ref=\"pdgform\"\n        :model=\"pdgform\"\n        :rules=\"rules\"\n        :disabled=\"pdgdisable\"\n        label-width=\"130px\"\n      >\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属站室\" prop=\"sszs\">\n              <el-input\n                v-model=\"pdgform.sszs\"\n                placeholder=\"请输入所属站室名称\"\n                disabled=\"disabled\"\n              ></el-input>\n<!--              <el-select\n                v-model=\"pdgform.sszs\"\n                placeholder=\"请选择所属电站\"\n                filterable\n                disabled=\"disabled\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in pdgOptionsDataList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>-->\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"开关柜名称\" prop=\"kggmc\">\n              <el-input\n                v-model=\"pdgform.kggmc\"\n                placeholder=\"请输入开关柜名称\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"运行编号\" prop=\"yxbh\">\n              <el-input\n                v-model=\"pdgform.yxbh\"\n                placeholder=\"请输入运行编号\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"投运日期：\" prop=\"tyrq\">\n              <el-date-picker\n                type=\"date\"\n                :disabled=\"isDisabled\"\n                placeholder=\"选择日期\"\n                value-format=\"yyyy-MM-dd\"\n                v-model=\"pdgform.tyrq\"\n                style=\"width: 100%\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"生产厂家\" prop=\"sccj\">\n              <el-input\n                v-model=\"pdgform.sccj\"\n                :placeholder=\"pdgdisable?'':'请输入生产厂家'\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"开关柜型号\" prop=\"kggxh\">\n              <el-input\n                v-model=\"pdgform.kggxh\"\n                placeholder=\"请输入开关柜型号\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"出厂日期：\" prop=\"ccrq\">\n              <el-date-picker\n                type=\"date\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"pdgdisable?'':'选择日期'\"\n                value-format=\"yyyy-MM-dd\"\n                v-model=\"pdgform.ccrq\"\n                style=\"width: 100%\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"状态\" prop=\"zt\">\n              <el-select\n                v-model=\"pdgform.zt\"\n                placeholder=\"请选择状态\"\n                :disabled=\"isDisabled\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in pdgztList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.label\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电压等级(KV)\" prop=\"dydj\">\n              <el-input-number\n                :min=\"0\"\n                :precision=\"2\"\n                v-model=\"pdgform.dydj\"\n                placeholder=\"请输入电压等级\"\n                :disabled=\"isDisabled\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"开关用途\" prop=\"kgyt\">\n              <el-select\n                v-model=\"pdgform.kgyt\"\n                placeholder=\"请选择开关用途\"\n                :disabled=\"isDisabled\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in pdgkgytList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.label\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"开关柜类型\" prop=\"kgglx\">\n              <el-input\n                v-model=\"pdgform.kgglx\"\n                placeholder=\"请输入开关柜类型\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"闭锁型式\" prop=\"bsxs\">\n              <el-input\n                v-model=\"pdgform.bsxs\"\n                placeholder=\"请输入闭锁型式\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\" prop=\"bz\">\n              <el-input\n                v-model=\"pdgform.bz\"\n                type=\"textarea\"\n                rows=\"2\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\" v-show=\"showButton\">\n        <el-button @click=\"bdgDialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"save\" class=\"pmyBtn\">确 定</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 设备基本信息、参数、履历弹出框-->\n    <el-dialog\n      :title=\"sbtitle\"\n      :visible.sync=\"dialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"70%\"\n      v-dialogDrag\n      :before-close=\"resetForm\"\n    >\n      <div>\n        <el-tabs v-model=\"activeTabName\">\n          <!--基本信息-->\n          <el-tab-pane label=\"基本信息\" name=\"sbDesc\">\n            <!--添加信息-->\n            <el-form\n              :model=\"jbxxForm\"\n              :rules=\"rules\"\n              label-width=\"140px\"\n              ref=\"jbxxForm\"\n              :disabled=\"isDisabled\"\n            >\n              <el-row>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"设备类型\" prop=\"sblx\">\n                    <el-select\n                      v-model=\"jbxxForm.sblx\"\n                      placeholder=\"请输入内容\"\n                      @change=\"showParams\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in sblxOptionsDataSelected\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"所属站室\" prop=\"sszs\">\n                    <el-select\n                      v-model=\"jbxxForm.sszs\"\n                      placeholder=\"请输入内容\"\n                      @change=\"pdsOptionsChangeClick()\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in pdsOptionsDataList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"所属开关柜\" prop=\"sskgg\">\n                    <el-select\n                      v-model=\"jbxxForm.sskgg\"\n                      placeholder=\"请输入内容\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in sbOptionsDataList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"设备名称\" prop=\"sbmc\">\n                    <el-input v-model=\"jbxxForm.sbmc\" placeholder=\"请输入内容\">\n                    </el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"运行编号\" prop=\"yxbh\">\n                    <el-input v-model=\"jbxxForm.yxbh\" placeholder=\"请输入内容\">\n                    </el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"投运日期\" prop=\"tyrq\">\n                    <el-date-picker\n                      v-model=\"jbxxForm.tyrq\"\n                      type=\"date\"\n                      value-format=\"yyyy-MM-dd\"\n                      placeholder=\"选择日期\"\n                    >\n                    </el-date-picker>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"状态\" prop=\"zt\">\n                    <el-select\n                      v-model=\"jbxxForm.zt\"\n                      placeholder=\"请输入内容\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in jbxxztList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"电压等级\" prop=\"dydj\">\n                    <el-input v-model=\"jbxxForm.dydj\" placeholder=\"请输入内容\">\n                    </el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"规格型号\" prop=\"ggxh\">\n                    <el-input v-model=\"jbxxForm.ggxh\" placeholder=\"请输入内容\">\n                    </el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"生产厂家\" prop=\"sccj\">\n                    <el-input v-model=\"jbxxForm.sccj\"  :placeholder=\"isDisabled?'':'请输入内容'\">\n                    </el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"出厂日期\" prop=\"ccrq\">\n                    <el-date-picker\n                      v-model=\"jbxxForm.ccrq\"\n                      type=\"date\"\n                      value-format=\"yyyy-MM-dd\"\n                      :placeholder=\"isDisabled?'':'选择日期'\"\n                    >\n                    </el-date-picker>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"开关用途\" prop=\"kgyt\">\n                    <el-select\n                      v-model=\"jbxxForm.kgyt\"\n                      :placeholder=\"isDisabled?'':'请输入内容'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in jbxxkgytList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"灭弧介质\" prop=\"mhjz\">\n                    <el-select\n                      v-model=\"jbxxForm.mhjz\"\n                      :placeholder=\"isDisabled?'':'请输入灭弧介质'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in mhjzList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"操作机构型\" prop=\"czjgxs\">\n                    <el-select\n                      v-model=\"jbxxForm.czjgxs\"\n                      :placeholder=\"isDisabled?'':'请输入操作机构型式'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in lzjgxsList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"操作方式\" prop=\"czfs\">\n                    <el-select\n                      v-model=\"jbxxForm.czfs\"\n                      :placeholder=\"isDisabled?'':'请输入操作方式'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in czfsList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"绝缘介质\" prop=\"jyjz\">\n                    <el-select\n                      v-model=\"jbxxForm.jyjz\"\n                      :placeholder=\"isDisabled?'':'请输入绝缘介质'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in jyjzList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"额定电压   \" prop=\"eddy\">\n                    <el-input\n                      v-model=\"jbxxForm.eddy\"\n                      :placeholder=\"isDisabled?'':'请输入额定电压'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"额定电流   \" prop=\"eddl\">\n                    <el-input-number\n                      v-model=\"jbxxForm.eddl\"\n                      :min=\"0\"\n                      placeholder=\"请输入额定电流\"\n                    ></el-input-number>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"熔丝规格   \" prop=\"rsgg\">\n                    <el-input\n                      v-model=\"jbxxForm.rsgg\"\n                      :placeholder=\"isDisabled?'':'请输入熔丝规格'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"绝缘材质   \" prop=\"jycz\">\n                    <el-select\n                      v-model=\"jbxxForm.jycz\"\n                      :placeholder=\"isDisabled?'':'请输入绝缘材质'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in jyczList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"数量\" prop=\"sl\">\n                    <el-input-number\n                      :min=\"0\"\n                      v-model=\"jbxxForm.sl\"\n                      placeholder=\"请输入数量\"\n                    ></el-input-number>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"额定电流比  \" prop=\"eddlb\">\n                    <el-input\n                      v-model=\"jbxxForm.eddlb\"\n                      :placeholder=\"isDisabled?'':'请输入额定电流比'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"额定电压比  \" prop=\"eddyb\">\n                    <el-input\n                      v-model=\"jbxxForm.eddyb\"\n                      :placeholder=\"isDisabled?'':'请输入额定电压比'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"绝缘方式\" prop=\"jyfs\">\n                    <el-select\n                      v-model=\"jbxxForm.jyfs\"\n                      :placeholder=\"isDisabled?'':'请输入绝缘方式'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in jyfsList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"总容量\" prop=\"zrl\">\n                    <el-input-number\n                      :min=\"0\"\n                      v-model=\"jbxxForm.zrl\"\n                      placeholder=\"请输入总容量\"\n                    ></el-input-number>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"投退方式\" prop=\"ttfs\">\n                    <el-select\n                      v-model=\"jbxxForm.ttfs\"\n                      :placeholder=\"isDisabled?'':'请输入投退方式'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in ttfsList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"单只容量\" prop=\"dzrl\">\n                    <el-input-number\n                      :min=\"0\"\n                      v-model=\"jbxxForm.dzrl\"\n                      placeholder=\"请输入单只容量\"\n                    ></el-input-number>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"资产性质\" prop=\"zcxx\">\n                    <el-select\n                      v-model=\"jbxxForm.zcxx\"\n                      :placeholder=\"isDisabled?'':'请输入资产性质'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in zzxzList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"母线型式\" prop=\"mxxs\">\n                    <el-select\n                      v-model=\"jbxxForm.mxxs\"\n                      :placeholder=\"isDisabled?'':'请输入母线型式'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in mxxsList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"母线材质\" prop=\"mxcz\">\n                    <el-input\n                      v-model=\"jbxxForm.mxcz\"\n                      :placeholder=\"isDisabled?'':'请输入母线材质'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"长度\" prop=\"cd\">\n                    <el-input-number\n                      :min=\"0\"\n                      v-model=\"jbxxForm.cd\"\n                      placeholder=\"请输入长度\"\n                    ></el-input-number>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"额定容量\" prop=\"edrl\">\n                    <el-input-number\n                      :min=\"0\"\n                      v-model=\"jbxxForm.edrl\"\n                      placeholder=\"请输入额定容量\"\n                    ></el-input-number>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"配电变压器类型\" prop=\"byqlx\">\n                    <el-select\n                      v-model=\"jbxxForm.byqlx\"\n                      :placeholder=\"isDisabled?'':'请输入配电变压器类型'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in dyqlxList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"阻抗电压\" prop=\"zkdy\">\n                    <el-input\n                      v-model=\"jbxxForm.zkdy\"\n                      :placeholder=\"isDisabled?'':'请输入阻抗电压'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"短路阻抗\" prop=\"dlzk\">\n                    <el-input\n                      v-model=\"jbxxForm.dlzk\"\n                      :placeholder=\"isDisabled?'':'请输入短路阻抗'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"短路损耗\" prop=\"dlsh\">\n                    <el-input\n                      v-model=\"jbxxForm.dlsh\"\n                      :placeholder=\"isDisabled?'':'请输入短路损耗'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"接线组别\" prop=\"jxzb\">\n                    <el-input\n                      v-model=\"jbxxForm.jxzb\"\n                      :placeholder=\"isDisabled?'':'请输入接线组别'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"调压范围\" prop=\"tyfw\">\n                    <el-input\n                      v-model=\"jbxxForm.tyfw\"\n                      :placeholder=\"isDisabled?'':'请输入调压范围'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"油号\" prop=\"yh\">\n                    <el-input\n                      v-model=\"jbxxForm.yh\"\n                      :placeholder=\"isDisabled?'':'请输入油号'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"油重\" prop=\"yz\">\n                    <el-input-number\n                      :min=\"0\"\n                      v-model=\"jbxxForm.yz\"\n                      placeholder=\"请输入油重\"\n                    ></el-input-number>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"总重\" prop=\"zz\">\n                    <el-input-number\n                      :min=\"0\"\n                      v-model=\"jbxxForm.zz\"\n                      placeholder=\"请输入总重\"\n                    ></el-input-number>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"绝缘等级\" prop=\"jydj\">\n                    <el-select\n                      v-model=\"jbxxForm.jydj\"\n                      :placeholder=\"isDisabled?'':'请输入绝缘等级'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in jydjList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"调压方式\" prop=\"tyfs\">\n                    <el-input\n                      v-model=\"jbxxForm.tyfs\"\n                      :placeholder=\"isDisabled?'':'请输入调压方式'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"铁芯材质\" prop=\"txcz\">\n                    <el-input\n                      v-model=\"jbxxForm.txcz\"\n                      :placeholder=\"isDisabled?'':'请输入铁芯材质'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"芯数及截面\" prop=\"xsjjm\">\n                    <el-input\n                      v-model=\"jbxxForm.xsjjm\"\n                      :placeholder=\"isDisabled?'':'请输入芯数及截面'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"负载名称\" prop=\"fzmc\">\n                    <el-input\n                      v-model=\"jbxxForm.fzmc\"\n                      :placeholder=\"isDisabled?'':'请输入负载名称'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"电缆材质\" prop=\"dlcz\">\n                    <el-input\n                      v-model=\"jbxxForm.dlcz\"\n                      :placeholder=\"isDisabled?'':'请输入电缆材质'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"备注\" prop=\"bz\">\n                    <el-input\n                      v-model=\"jbxxForm.bz\"\n                      :placeholder=\"isDisabled?'':'请输入内容'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n            </el-form>\n          </el-tab-pane>\n\n          <!--技术参数-->\n          <el-tab-pane label=\"技术参数\" name=\"sbjscs\">\n            <el-form :model=\"jscsForm\" label-width=\"130px\">\n              <el-col :span=\"8\" v-for=\"item in jscsLabelList\">\n                <el-form-item\n                  :label=\"\n                    item.dw != ''\n                      ? item.label + '(' + item.dw + ')'\n                      : item.label\n                  \"\n                >\n                  <el-input\n                    v-if=\"item.type === 'input'\"\n                    v-model=\"jscsForm[item.jscsbm]\"\n                    placeholder=\"\"\n                  >\n                  </el-input>\n                  <el-select\n                    clearable\n                    v-if=\"item.type === 'select'\"\n                    v-model=\"jscsForm[item.jscsbm]\"\n                    placeholder=\"\"\n                  >\n                    <el-option\n                      v-for=\"(childItem, key) in item.options\"\n                      :key=\"key\"\n                      :label=\"childItem.label\"\n                      :value=\"childItem.value\"\n                      :disabled=\"childItem.disabled\"\n                      style=\"display: flex; align-items: center\"\n                      clearable\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </el-form>\n          </el-tab-pane>\n\n          <!--设备履历-->\n          <el-tab-pane label=\"设备履历\" name=\"sbRecord\">\n            <el-tabs\n              v-model=\"sbllDescTabName\"\n              @tab-click=\"handleSbllDescTabNameClick\"\n              type=\"card\"\n            >\n              <el-tab-pane label=\"试验记录\" name=\"syjl\">\n                <el-table\n                  stripe\n                  border\n                  v-loading=\"loading\"\n                  :data=\"sblvsyjlList\"\n                  max-height=\"550\"\n                >\n                  <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n                  <el-table-column\n                    label=\"试验专业\"\n                    align=\"center\"\n                    prop=\"syzy\"\n                  />\n                  <el-table-column\n                    label=\"试验性质\"\n                    align=\"center\"\n                    prop=\"syxz\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"试验名称\"\n                    align=\"center\"\n                    prop=\"symc\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"工作地点\"\n                    align=\"center\"\n                    prop=\"gzdd\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"试验设备\"\n                    align=\"center\"\n                    prop=\"sysb\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"试验报告\"\n                    align=\"center\"\n                    prop=\"sybg\"\n                  />\n                  <el-table-column\n                    label=\"天气\"\n                    align=\"center\"\n                    prop=\"tq\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"试验日期\"\n                    align=\"center\"\n                    prop=\"syrq\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"录入人\"\n                    align=\"center\"\n                    prop=\"lrr\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"试验结论\"\n                    align=\"center\"\n                    prop=\"syjl\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                </el-table>\n              </el-tab-pane>\n              <!-- <el-tab-pane label=\"缺陷记录\" name=\"qxjl\">\n                <el-table\n                  stripe\n                  border\n                  v-loading=\"loading\"\n                  :data=\"sbllqxjlList\"\n                  max-height=\"550\"\n                >\n                  <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n                  <el-table-column\n                    label=\"所属公司\"\n                    align=\"center\"\n                    prop=\"ssgs\"\n                  />\n                  <el-table-column\n                    label=\"变电站名称\"\n                    align=\"center\"\n                    prop=\"bdzmc\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"设备类型\"\n                    align=\"center\"\n                    prop=\"sblx\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"缺陷性质\"\n                    align=\"center\"\n                    prop=\"qxxz\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"电压等级\"\n                    align=\"center\"\n                    prop=\"dydj\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"设备型号\"\n                    align=\"center\"\n                    prop=\"sbxh\"\n                  />\n                  <el-table-column\n                    label=\"生产厂家\"\n                    align=\"center\"\n                    prop=\"sccj\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                </el-table>\n              </el-tab-pane>\n              <el-tab-pane label=\"状态变更记录\" name=\"ztbgjl\">\n                <comp-table\n                  :table-and-page-info=\"resumPageInfo\"\n                  @getMethod=\"getResumList\"\n                  @update:multipleSelection=\"selectChange\"\n                  height=\"500\"\n                />\n              </el-tab-pane> -->\n            </el-tabs>\n          </el-tab-pane>\n        </el-tabs>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\" v-show=\"!isDisabled\">\n        <el-button @click=\"resetForm\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submit\" class=\"submit\"\n          >确 定\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!--状态变更弹出框展示-->\n    <el-dialog\n      title=\"设备状态变更\"\n      :visible.sync=\"dialogVisible\"\n      width=\"30%\"\n      append-to-body\n      v-dialogDrag\n    >\n      <el-form :inline=\"true\" label-width=\"100px\" class=\"qxlr_dialog_insert\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"设备状态:\">\n              <el-select v-model=\"updateList.zt\">\n                <el-option\n                  v-for=\"item in jbxxztList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitStatus\">确 定</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport {\n  getList,\n  saveOrUpdate,\n  remove,\n  getPdsbOne,\n} from \"@/api/dagangOilfield/dwzygl/pdsbgl/pdsbjbxx\";\nimport {\n  updateStatus,\n  getResumDataList,\n} from \"@/api/dagangOilfield/dwzygl/pdsbgl/pdsbztbg\";\nimport {\n  addPdg,\n  getPdgList,\n  getPdgListSelected,\n  getPdgOne,\n  getPdsTreeList,\n  removePdg,\n  getMergePdgInfo,\n} from \"@/api/dagangOilfield/asset/pdg\";\nimport { getSblxDataListSelected } from \"@/api/dagangOilfield/asset/bdsbtz\";\nimport {\n  getParamDataList,\n  getParamsValue,\n  saveParamValue,\n} from \"@/api/dagangOilfield/asset/parameters\";\nimport {\n  addPds,\n  getPdsList,\n  getPdsOptionsDataList,\n  removePds,\n} from \"@/api/dagangOilfield/asset/pdsgl\";\nimport { getDictTypeData } from \"@/api/system/dict/data\";\nimport { selectDeptOneAndTwo } from \"@/api/system/dept\";\nimport { deleteById, getListByBusinessId } from \"@/api/tool/file\";\nimport {adddwzyfstz} from \"@/api/dagangOilfield/asset/jgtz\";\n\nexport default {\n  name: \"pdsbgl\",\n  data() {\n    return {\n      sskgg:\"\",\n      treeNode:'',//树节点标记\n      headForm: {}, //头部表单信息\n      uploadData: {\n        type: \"\",\n        businessId: undefined,\n      },\n      sszs: \"\",\n      icons: {\n        pdsList: \"categoryTreeIcons\",\n        pds: \"tableIcon\",\n        pdg: \"classIcon\",\n      },\n      pdzshow: true,\n      sbshow: false,\n      pdgshow: false,\n      //配电站相关\n      //弹出框表单\n      pdzform: {\n        attachment: [],\n        //ssgs: undefined,\n        ssxlmc: undefined,\n        ssxlbh: undefined,\n        ssxdmc: undefined,\n        pdsmc: undefined,\n        yxbh: undefined,\n        erpBm: undefined,\n        bgr: undefined,\n        zcbdfs: undefined,\n        zcsx: undefined,\n        zcbh: undefined,\n        wbsYs: undefined,\n        zcxz: undefined,\n        tyrq: undefined,\n        sfjyhw: undefined,\n        zt: undefined,\n        dqtz: undefined,\n        sccj: undefined,\n        sgdw: undefined,\n        jzwcc: undefined,\n        jzwcz: undefined,\n        tffs: undefined,\n        dlqgsl: undefined,\n        dyhgqgsl: undefined,\n        drgsl: undefined,\n        dyjxfs: undefined,\n        dypxgsl: undefined,\n        fhkggsl: undefined,\n        fhkgrdqzhgsl: undefined,\n        jlgsl: undefined,\n        mlgsl: undefined,\n        pbsl: undefined,\n        pbzrl: undefined,\n        wz: undefined,\n        xdcgsl: undefined,\n        zlgsl: undefined,\n        zby: undefined,\n        gyjxgsl: undefined,\n        gyjlgsl: undefined,\n        gycxgsl: undefined,\n        ptgsl: undefined,\n        dyjxgsl: undefined,\n        dycxgsl: undefined,\n        dybcgsl: undefined,\n        dyjlgsl: undefined,\n        dyllgsl: undefined,\n        dgsl: undefined,\n        dggd: undefined,\n        xsbxh: undefined,\n        dypdgsl: undefined,\n        lx: undefined,\n      },\n      pdsDialogFormVisible: false,\n      pdzfilterInfo: {\n        data: {\n          ssgs: [],\n          ssxlmc: \"\",\n          yxbh: \"\",\n          zt: [],\n        },\n        fieldList: [\n          // {label: '所属公司', type: 'select', value: 'ssgs', multiple: true, options: []},\n          { label: \"所属线路名称\", type: \"input\", value: \"ssxlmc\" },\n          { label: \"配电室名称\", type: \"input\", value: \"pdsmc\" },\n          { label: \"所属线路编号\", type: \"input\", value: \"ssxlbh\" },\n          { label: \"所属线段名称\", type: \"input\", value: \"ssxdmc\" },\n          { label: \"运行编号\", type: \"input\", value: \"yxbh\" },\n          { label: \"运维班组\", type: \"input\", value: \"ywbzmc\" },\n          {\n            label: \"投运日期\",\n            type: \"date\",\n            value: \"tyrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\",\n          },\n          {\n            label: \"是否具有环网\",\n            type: \"select\",\n            value: \"sfjyhw\",\n            options: [\n              {\n                value: \"是\",\n                label: \"是\",\n              },\n              {\n                value: \"否\",\n                label: \"否\",\n              },\n            ],\n          },\n          {\n            label: \"状态\",\n            type: \"select\",\n            value: \"zt\",\n            options: [\n              {\n                value: \"在运\",\n                label: \"在运\",\n              },\n              {\n                value: \"停止使用\",\n                label: \"停止使用\",\n              },\n              {\n                value: \"未就绪\",\n                label: \"未就绪\",\n              },\n              {\n                value: \"报废\",\n                label: \"报废\",\n              },\n            ],\n          },\n          { label: \"生产厂家\", type: \"input\", value: \"sccj\" },\n          { label: \"通风方式\", type: \"input\", value: \"tffs\" },\n          {\n            label: \"配电室类型\",\n            type: \"select\",\n            value: \"lx\",\n            options: [\n              {\n                value: \"箱式变电站\",\n                label: \"箱式变电站\",\n              },\n              {\n                value: \"柱上变台变\",\n                label: \"柱上变台变\",\n              },\n              {\n                value: \"配电室\",\n                label: \"配电室\",\n              },\n            ],\n          },\n        ],\n      },\n      ywbzList: [],\n      pdzParams: {\n        pageNum: 1,\n        pageSize: 10,\n      },\n      tableAndPageInfo1: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n          pageResize: \"\",\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true,\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"ssxlmc\", label: \"所属线路名称\", minWidth: \"180\" },\n          { prop: \"ssxlbh\", label: \"所属线路编号\", minWidth: \"120\" },\n          { prop: \"ssxdmc\", label: \"所属线段名称\", minWidth: \"180\" },\n          { prop: \"pdsmc\", label: \"配电室名称\", minWidth: \"140\" },\n          { prop: \"yxbh\", label: \"运行编号\", minWidth: \"120\" },\n          { prop: \"ywbzmc\", label: \"运维班组\", minWidth: \"120\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"120\" },\n          { prop: \"sfjyhw\", label: \"是否具有环网\", minWidth: \"120\" },\n          { prop: \"zt\", label: \"状态\", minWidth: \"120\" },\n          { prop: \"sccj\", label: \"生产厂家\", minWidth: \"120\" },\n          { prop: \"tffs\", label: \"通风方式\", minWidth: \"120\" },\n          { prop: \"lx\", label: \"配电室类型\", minWidth: \"120\" },\n          /*{\n              fixed: \"right\",\n              prop: 'operation',\n              label: '操作',\n              minWidth: '130px',\n              style: {display: 'block'},\n              operation: [\n                {name: '修改', clickFun: this.pdzgetUpdate},\n                {name: '详情', clickFun: this.pdzgetXq},\n              ]\n            },*/\n        ],\n      },\n      pdzsbzt: [\n        {\n          value: \"在运\",\n          label: \"在运\",\n        },\n        {\n          value: \"停止使用\",\n          label: \"停止使用\",\n        },\n        {\n          value: \"未就绪\",\n          label: \"未就绪\",\n        },\n        {\n          value: \"报废\",\n          label: \"报废\",\n        },\n      ],\n      pdslx: [\n        {\n          value: \"箱式变电站\",\n          label: \"箱式变电站\",\n        },\n        {\n          value: \"柱上变台变\",\n          label: \"柱上变台变\",\n        },\n        {\n          value: \"配电室\",\n          label: \"配电室\",\n        },\n      ],\n\n      //配电柜相关\n      bdgDialogFormVisible: false,\n      showButton: true,\n\n      pdgdisable:false,\n      //配电室下拉框\n      pdgOptionsDataList: [],\n      pdgkgytList: [],\n      pdgztList: [],\n      //弹出框表单\n      pdgform: {},\n      pdgParams: {\n        pageNum: 1,\n        pageSize: 10,\n      },\n      pdgfilterInfo: {\n        data: {\n          kggmc: \"\",\n          yxbh: \"\",\n        },\n        fieldList: [\n          { label: \"所属站室名称\", type: \"input\", value: \"sszsmc\" },\n          { label: \"所属站室运行编号\", type: \"input\", value: \"sszsyxbh\" },\n          { label: \"开关柜名称\", type: \"input\", value: \"kggmc\" },\n          { label: \"运行编号\", type: \"input\", value: \"yxbh\" },\n          {\n            label: \"投运日期\",\n            type: \"date\",\n            value: \"tyrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\",\n          },\n          { label: \"生产厂家\", type: \"input\", value: \"sccj\" },\n          { label: \"开关柜型号\", type: \"input\", value: \"kggxh\" },\n          {\n            label: \"出厂日期\",\n            type: \"date\",\n            value: \"tyrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\",\n          },\n          {\n            label: \"状态\",\n            type: \"select\",\n            value: \"zt\",\n            options: [\n              {\n                value: \"在运\",\n                label: \"在运\",\n              },\n              {\n                value: \"停止使用\",\n                label: \"停止使用\",\n              },\n              {\n                value: \"未就绪\",\n                label: \"未就绪\",\n              },\n              {\n                value: \"报废\",\n                label: \"报废\",\n              },\n              {\n                value: \"备用\",\n                label: \"备用\",\n              },\n            ],\n          },\n          { label: \"电压等级\", type: \"input\", value: \"dydj\" },\n        ],\n      },\n      tableAndPageInfo2: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n          pageResize: \"\",\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true,\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"sszsmc\", label: \"所属站室名称\", minWidth: \"180\" },\n          { prop: \"sszsyxbh\", label: \"所属站室运行编号\", minWidth: \"120\" },\n          { prop: \"kggmc\", label: \"开关柜名称\", minWidth: \"120\" },\n          { prop: \"yxbh\", label: \"运行编号\", minWidth: \"140\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"140\" },\n          { prop: \"sccj\", label: \"生产厂家\", minWidth: \"120\" },\n          { prop: \"kggxh\", label: \"开关柜型号\", minWidth: \"120\" },\n          { prop: \"ccrq\", label: \"出厂日期\", minWidth: \"120\" },\n          { prop: \"zt\", label: \"状态\", minWidth: \"180\" },\n          { prop: \"dydj\", label: \"电压等级\", minWidth: \"120\" },\n          /*{\n              fixed: \"right\",\n              prop: 'operation',\n              label: '操作',\n              minWidth: '130px',\n              style: {display: 'block'},\n              operation: [\n                {name: '修改', clickFun: this.pdgUpdate},\n                {name: '详情', clickFun: this.pdgDetails},\n              ]\n            },*/\n        ],\n      },\n      selectRows: [],\n      //配电室下拉框\n      pdsOptionsDataList: [],\n      //配电柜下拉框\n      sbOptionsDataList: [],\n      //设备类型\n      sblxOptionsDataSelected: [],\n      //左侧树筛选条件\n      treeForm: {},\n      //设备弹出框标题\n      sbtitle: \"\",\n      //状态变更信息\n      updateList: {\n        zt: \"\",\n        id: \"\",\n      },\n      //状态信息查询\n      resumeQuery: {\n        foreignNum: undefined,\n      },\n      dialogVisible: false,\n      //是否禁用\n      isDisabled: false,\n      //上传图片时的请求头\n      header: {},\n      //\n      tableDisabled:false,\n      //基本信息的状态\n      jbxxztList: [\n        {\n          value: \"在运\",\n          label: \"在运\",\n        },\n        {\n          value: \"停止使用\",\n          label: \"停止使用\",\n        },\n        {\n          value: \"未就绪\",\n          label: \"未就绪\",\n        },\n        {\n          value: \"报废\",\n          label: \"报废\",\n        },\n        {\n          value: \"备用\",\n          label: \"备用\",\n        },\n      ],\n      //开关用途\n      jbxxkgytList: [\n        {\n          value: \"进线\",\n          label: \"进线\",\n        },\n        {\n          value: \"出线\",\n          label: \"出线\",\n        },\n        {\n          value: \"联络\",\n          label: \"联络\",\n        },\n      ],\n\n      //操作机构型式\n      lzjgxsList: [\n        {\n          value: \"弹簧\",\n          label: \"弹簧\",\n        },\n        {\n          value: \"永磁\",\n          label: \"永磁\",\n        },\n      ],\n\n      //灭弧介质\n      mhjzList: [\n        {\n          value: \"空气\",\n          label: \"空气\",\n        },\n        {\n          value: \"充油\",\n          label: \"充油\",\n        },\n        {\n          value: \"真空\",\n          label: \"真空\",\n        },\n        {\n          value: \"SF6\",\n          label: \"SF6\",\n        },\n      ],\n\n      //绝缘介质\n      jyjzList: [\n        {\n          value: \"空气\",\n          label: \"空气\",\n        },\n        {\n          value: \"充油\",\n          label: \"充油\",\n        },\n        {\n          value: \"真空\",\n          label: \"真空\",\n        },\n        {\n          value: \"SF6\",\n          label: \"SF6\",\n        },\n      ],\n      //绝缘材质\n      jyczList: [\n        {\n          value: \"瓷\",\n          label: \"瓷\",\n        },\n        {\n          value: \"复合\",\n          label: \"复合\",\n        },\n      ],\n\n      //绝缘方式\n      jyfsList: [\n        {\n          value: \"油浸式\",\n          label: \"油浸式\",\n        },\n        {\n          value: \"干式\",\n          label: \"干式\",\n        },\n      ],\n      //操作方式\n      czfsList: [\n        {\n          value: \"手动\",\n          label: \"手动\",\n        },\n        {\n          value: \"自动\",\n          label: \"自动\",\n        },\n        {\n          value: \"手动/自动\",\n          label: \"手动/自动\",\n        },\n      ],\n      //操作方式\n      ttfsList: [\n        {\n          value: \"手动\",\n          label: \"手动\",\n        },\n        {\n          value: \"自动式\",\n          label: \"自动式\",\n        },\n      ],\n      //资产性质\n      zzxzList: [\n        {\n          value: \"公用\",\n          label: \"公用\",\n        },\n        {\n          value: \"专用\",\n          label: \"专用\",\n        },\n      ],\n      //母线型式\n      mxxsList: [\n        {\n          value: \"排式\",\n          label: \"排式\",\n        },\n        {\n          value: \"线式\",\n          label: \"线式\",\n        },\n        {\n          value: \"TMY\",\n          label: \"TMY\",\n        },\n        {\n          value: \"LMY\",\n          label: \"LMY\",\n        },\n      ],\n      //配电变压器类型\n      dyqlxList: [\n        {\n          value: \"降变压\",\n          label: \"降变压\",\n        },\n        {\n          value: \"升压\",\n          label: \"升压\",\n        },\n      ],\n      //配电变压器类型\n      jydjList: [\n        {\n          value: \"A\",\n          label: \"A\",\n        },\n        {\n          value: \"B\",\n          label: \"B\",\n        },\n        {\n          value: \"C\",\n          label: \"C\",\n        },\n        {\n          value: \"D\",\n          label: \"D\",\n        },\n        {\n          value: \"E\",\n          label: \"E\",\n        },\n        {\n          value: \"F\",\n          label: \"F\",\n        },\n        {\n          value: \"H\",\n          label: \"H\",\n        },\n      ],\n      rules: {\n        sblx: [\n          { required: true, message: \"请选择设备类型\", trigger: \"change\" },\n        ],\n        /*sszs: [{required: true, message: '所属站室', trigger: 'change'}],*/\n        //sskgg: [{required: true, message: '所属开关柜', trigger: 'change'}],\n        sbmc: [{ required: true, message: \"设备名称\", trigger: \"blur\" }],\n        yxbh: [{ required: true, message: \"请输入运行编号\", trigger: \"blur\" }],\n        tyrq: [{ required: true, message: \"投运日期\", trigger: \"change\" }],\n        zt: [{ required: true, message: \"状态\", trigger: \"change\" }],\n        dydj: [{ required: true, message: \"电压等级\", trigger: \"blur\" }],\n      },\n      //设备基本信息\n      filterInfo: {\n        data: {},\n        fieldList: [\n          {\n            label: \"所属站室名称\",\n            type: \"select\",\n            value: \"pdsList\",\n            multiple: true,\n            options: [],\n          },\n          {\n            label: \"所属站室运行编号\",\n            type: \"input\",\n            value: \"sszsyxbh\",\n            multiple: true,\n            options: [],\n          },\n          {\n            label: \"所属开关柜名称\",\n            type: \"input\",\n            value: \"sskggmc\",\n            options: [],\n          },\n          {\n            label: \"所属开关柜运行编号\",\n            type: \"input\",\n            value: \"sskggyxbh\",\n            options: [],\n          },\n          { label: \"设备名称\", type: \"input\", value: \"sbmc\" },\n          { label: \"运行编号\", type: \"input\", value: \"yxbh\" },\n          {\n            label: \"状态\",\n            type: \"select\",\n            value: \"ztList\",\n            multiple: true,\n            options: [\n              {\n                value: \"在运\",\n                label: \"在运\",\n              },\n              {\n                value: \"停止使用\",\n                label: \"停止使用\",\n              },\n              {\n                value: \"未就绪\",\n                label: \"未就绪\",\n              },\n              {\n                value: \"报废\",\n                label: \"报废\",\n              },\n              {\n                value: \"备用\",\n                label: \"备用\",\n              },\n            ],\n          },\n          {\n            label: \"投运日期\",\n            type: \"date\",\n            value: \"tyrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\",\n          },\n          { label: \"规格型号\", type: \"input\", value: \"ggxh\" },\n        ],\n      },\n      tableAndPageInfo3: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n          pageResize: \"\"\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true,\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"sszsmc\", label: \"所属站室名称\", minWidth: \"150\" },\n          { prop: \"sszsyxbh\", label: \"所属站室运行编号\", minWidth: \"150\" },\n          { prop: \"sskggmc\", label: \"所属开关柜名称\", minWidth: \"150\" },\n          { prop: \"sskggyxbh\", label: \"所属开关柜运行编号\", minWidth: \"150\" },\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"120\" },\n          { prop: \"yxbh\", label: \"运行编号\", minWidth: \"120\" },\n          { prop: \"zt\", label: \"状态\", minWidth: \"80\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"100\" },\n          { prop: \"ggxh\", label: \"规格型号\", minWidth: \"120\" },\n          /*{\n              fixed: 'right',\n              prop: 'operation',\n              label: '操作',\n              minWidth: '120px',\n              style: {display: 'block'},\n              operation: [\n                {name: '修改', clickFun: this.getSbXgButton},\n                {name: '详情', clickFun: this.getSbXqButton}\n              ]\n            }*/\n        ],\n      },\n      //状态变更记录信息\n      resumPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true,\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"foreignNum\", label: \"设备名称\", minWidth: \"120\" },\n          { prop: \"sblx\", label: \"设备类型\", minWidth: \"180\" },\n          { prop: \"bglx\", label: \"变更类型\", minWidth: \"120\" },\n          { prop: \"ms\", label: \"描述\", minWidth: \"230\" },\n          { prop: \"bgr\", label: \"变更人\", minWidth: \"140\" },\n          { prop: \"bgsj\", label: \"变更时间\", minWidth: \"160\" },\n        ],\n      },\n      params: {\n        id: \"\",\n        sbmc: \"\",\n        ggxh: \"\",\n        dydj: \"\",\n        tyrqStr: [],\n        sccj: \"\",\n        yxbh: \"\",\n        bz: \"\",\n        sszsmc: \"\",\n        sszsyxbh: \"\",\n        sskggmc: \"\",\n        sskggyxbh: \"\",\n        zt: \"\",\n        kgyt: \"\",\n      },\n\n      //技术参数基本信息\n      jscsForm: {},\n\n      //设备履历缺陷记录数据集合\n      sbllqxjlList: [\n        /*{\n            ssgs: '港东分公司',\n            dzmc: '1号变电站',\n            sblx: '主变压器',\n            qxxz: '严重',\n            dydj: '35kV',\n            sbxh: 'XXX型号',\n            sccj: 'XXX厂家'\n          }*/\n      ],\n      //设备履历试验记录数据\n      sblvsyjlList: [\n        /*{\n            syzy: '带电',\n            syxz: '例行试验',\n            symc: 'XXXXX',\n            gzdd: 'XXX平台',\n            sysb: '主变压器',\n            sybg: '',\n            tq: '晴',\n            syrq: '2022-01-01',\n            lrr: '张三',\n            syjl: 'XXXXX'\n          }*/\n      ],\n      //设备履历tab页\n      sbllDescTabName: \"syjl\",\n\n      //轮播图片\n      imgList: [],\n      //配电设备基本信息\n      jbxxForm: {},\n      //弹出框tab页\n      activeTabName: \"sbDesc\",\n      //设备弹出框\n      dialogFormVisible: false,\n      //加载信息\n      loading: false,\n      //设备履历\n      handleSbllDescTabNameClick: {},\n      // 单击下拉树选中的节点\n      selectNode: \"\",\n      //组织树\n      treeOptions: [],\n\n      //变电站挂接数据\n      newTestData: [],\n      jscsLabelList: [],\n      paramQuery: {\n        sblxbm: undefined,\n      },\n      sbParams: {},\n      pdgSszs:'',//配电柜所属站室id\n      pdgSszsmc:'',//配电柜所属站室名称\n    };\n  },\n  watch: {},\n  async created() {\n    await this.getYwbzList();\n    //初始化加载时加载所有变电站信息\n    this.newTestData = this.bdzList;\n    // this.getData();\n    await this.getpdzData(); //请求配电站数据\n    await this.initDictData();\n    await this.getOptions();//获取下拉框字典\n    this.getNewTreeInfo();\n    this.init();\n    this.getPdsOptionsDataList();\n    getPdgListSelected({}).then((res) => {\n      //所属配电柜筛查条件\n      this.filterInfo.fieldList[1].options = res.data;\n    });\n  },\n  mounted() {\n    //获取token\n    this.header.token = getToken();\n  },\n  methods: {\n    //获取下拉框字典值\n    async getOptions(){\n      await this.getPdgLx();//获取配电柜类型\n    },\n    //获取配电柜类型\n    async getPdgLx(){\n      await getDictTypeData('dwzy_pdg_kkyt').then(res=>{\n        res.data.forEach(item=>{\n          this.pdgkgytList.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    viewHeadDetail(){\n      switch (this.treeNode.identifier){\n        case \"1\"://配电室\n          this.pdzgetXq(this.headForm);\n          break;\n        case \"2\"://配电柜\n          this.pdgDetails(this.headForm);\n          break;\n      }\n    },\n    beforeUpload(file) {\n      const fileSize = file.size < 1024 * 1024 * 50;\n      if (!fileSize) {\n        this.$message.error(\"上传文件大小不能超过 50MB!\");\n      }\n    },\n    async deleteFileById(id) {\n      let { code } = await deleteById(id);\n      if (code === \"0000\") {\n        await this.getFileList();\n        this.$message({\n          type: \"success\",\n          message: \"文件删除成功!\",\n        });\n      }\n    },\n    submitUpload() {\n      this.$refs.upload.submit();\n    },\n    clearUpload() {\n      if (this.$refs.upload) {\n        this.$refs.upload.clearFiles();\n      }\n    },\n    async getFileList() {\n      let { code, data } = await getListByBusinessId({\n        businessId: this.pdzform.objId,\n      });\n      if (code === \"0000\") {\n        this.pdzform.attachment = data;\n        this.imgList = data.map((item) => {\n          let item1 = {};\n          item1.name = item.fileName;\n          item1.url = this.$store.getters.currHost + item.fileUrl;\n          return item1;\n        });\n      }\n    },\n    async getYwbzList() {\n      await selectDeptOneAndTwo({ parentId: 3013 }).then((res) => {\n        this.ywbzList = res.data;\n      });\n    },\n    //列表查询\n    async getData(params) {\n      if (this.pdzshow) {\n        this.getpdzData(params);\n      }\n      if (this.pdgshow) {\n        this.getpdgData(params);\n      }\n      if (this.sbshow) {\n        this.getsbData(params);\n      }\n    },\n    //新增按钮\n    AddSensorButton() {\n      if (this.pdzshow) {\n        this.pdzform = { attachment: [] };\n        this.clearUpload();\n        this.imgList = [];\n        this.isDisabled = false;\n        this.pdsDialogFormVisible = true;\n      }\n      if (this.pdgshow) {\n        this.pdgform.sszs = this.pdgSszsmc;//所属站室回显问题\n        this.isDisabled = false;\n        this.showButton = true;\n        this.bdgDialogFormVisible = true;\n        this.pdgdisable = false;//配电柜表单可编辑\n      }\n      if (this.sbshow) {\n        this.sbtitle = \"设备新增\";\n        this.dialogFormVisible = true;\n        this.jbxxForm.sszs = this.sszs\n        this.getPdgListSelected();\n        this.jbxxForm.sskgg = this.sskgg\n        this.isDisabled = false;\n      }\n    },\n    /*-----------------------配电站相关开始--------------------------*/\n    //表格多选框\n    pdzhandleSelectionChange(selection) {\n      this.ids = selection.map((item) => item.objId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    //配电站表格数据\n    async getpdzData(params) {\n      this.pdzParams = { ...this.pdzParams, ...params };\n      const param = this.pdzParams\n      await getPdsList(param).then((res) => {\n        this.pdzshow = true;\n        this.pdgshow = this.sbshow = false;\n        this.tableAndPageInfo1.tableData = res.data.records;\n        this.tableAndPageInfo1.tableData.forEach((item) => {\n          this.ywbzList.forEach((element) => {\n            if (item.ywbz == element.value) {\n              item.ywbzmc = element.label;\n            }\n          });\n        });\n        this.tableAndPageInfo1.pager.total = res.data.total;\n      });\n    },\n    //保存确定按钮\n    async getDetermine() {\n      let params={\n        lx:\"配电设备\",\n        mc:this.pdzform.pdsmc,\n      }\n      this.$refs[\"pdzform\"].validate((valid) => {\n        if (valid) {\n          addPds(this.pdzform).then((res) => {\n            if (res.code === \"0000\") {\n               //新增成功后发送通知\n               adddwzyfstz(params).then(res =>{\n                    if(res.code === \"0000\"){\n                  }\n                 });\n              this.uploadData.businessId = res.data.objId;\n              this.submitUpload();\n              this.$message.success(\"操作成功，通知已发送！！\");\n              this.pdsDialogFormVisible = false;\n              this.getpdzData();\n            }\n          });\n        } else {\n          setTimeout(() => {\n            var isError = document.getElementsByClassName(\"is-error\");\n            if (isError[0].querySelector(\"input\")) {\n              isError[0].querySelector(\"input\").focus();\n            } else if (isError[0].querySelector(\"textarea\")) {\n              isError[0].querySelector(\"textarea\").focus();\n            }\n          }, 1);\n          return false;\n        }\n      });\n    },\n    //关闭配电站弹框\n    handleClose() {\n      if (this.pdzshow) {\n        this.pdzform = {\n          attachment: [],\n        };\n        // this.$nextTick(() => {\n        //   this.pdzform = this.$options.data().form\n        //   this.resetForm('pdzform')\n        // })\n        this.pdsDialogFormVisible = false;\n      }\n      if (this.pdgshow) {\n        this.pdgform = {};\n        // this.$nextTick(() => {\n        //   this.pdgform = this.$options.data().form\n        //   this.resetForm('pdgform')\n        // })\n        this.bdgDialogFormVisible = false;\n      }\n    },\n    //修改\n    async pdzgetUpdate(row) {\n      this.clearUpload();\n      this.pdzform = { ...row };\n      this.pdzform.attachment = [];\n      await this.getFileList();\n      this.isDisabled = false;\n      this.pdsDialogFormVisible = true;\n    },\n    //详情\n    async pdzgetXq(row) {\n      this.clearUpload();\n      this.pdzform = { ...row };\n      this.pdzform.attachment = [];\n      await this.getFileList();\n      this.isDisabled = true;\n      this.pdsDialogFormVisible = true;\n    },\n    /*---------------------配电站相关结束----------------------*/\n\n    /*---------------------配电柜相关开始-----------------------*/\n    async initDictData() {\n      let { data: pdgzt } = await getDictTypeData(\"pdgzt\");\n      this.pdgztList = pdgzt;\n    },\n    async getpdgData(param) {\n      this.pdgParams = { ...this.pdgParams, ...param };\n      const par = this.pdgParams;\n      try {\n        let { data, code } = await getMergePdgInfo(par);\n        if (code === \"0000\") {\n          this.pdgshow = true;\n          this.pdzshow = this.sbshow = false;\n          this.tableAndPageInfo2.tableData = data.records;\n          this.tableAndPageInfo2.pager.total = data.total;\n          // this.tableAndPageInfo2.pager.pageResize = 'Y';\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //修改\n    pdgUpdate(row) {\n      if (row.sjlx === \"kgg\") {\n        getPdgOne({ objId: row.objId }).then((res) => {\n          if (res.code === \"0000\") {\n            this.bdgDialogFormVisible = true;\n            this.pdgform = res.data;\n            this.pdgform.sszs = this.pdgSszsmc;//处理回显\n            this.isDisabled = false;\n            this.showButton = true;\n            this.pdgdisable = false;//配电柜表单可编辑\n          } else {\n            this.$message.error(\"操作失败\");\n          }\n        });\n      } else if (row.sjlx === \"byq\") {\n        getPdsbOne({ id: row.objId }).then((res) => {\n          if (res.code === \"0000\") {\n            this.sbtitle = \"设备修改\";\n            this.dialogFormVisible = true;\n            this.jbxxForm = res.data;\n            this.isDisabled = false;\n            this.getPdgListSelected();\n            this.technicalParameters(row);\n          } else {\n            this.$message.error(\"操作失败\");\n          }\n        });\n      }\n    },\n    //详情\n    pdgDetails(row) {\n      if (row.sjlx === \"kgg\") {\n        getPdgOne({ objId: row.objId }).then((res) => {\n          if (res.code === \"0000\") {\n            this.bdgDialogFormVisible = true;\n            this.pdgform = res.data;\n            this.pdgform.sszs = this.pdgSszsmc;//回显处理\n            this.isDisabled = true;\n            this.showButton = false;\n            this.pdgdisable = true\n          } else {\n            this.$message.error(\"操作失败\");\n          }\n        });\n      } else if (row.sjlx === \"byq\") {\n        getPdsbOne({ id: row.objId }).then((res) => {\n          if (res.code === \"0000\") {\n            this.sbtitle = \"设备详情查看\";\n            this.dialogFormVisible = true;\n            this.jbxxForm = res.data;\n            this.isDisabled = true;\n            this.getPdgListSelected();\n            this.technicalParameters(row);\n          } else {\n            this.$message.error(\"操作失败\");\n          }\n        });\n      }\n    },\n    //保存修改内容\n    save() {\n      this.$refs.pdgform.validate((valid) => {\n        if (valid) {\n          this.pdgform.sszs = this.pdgSszs;\n          addPdg(this.pdgform).then((res) => {\n            if (res.code == \"0000\") {\n              this.$message.success(\"操作成功\");\n              this.bdgDialogFormVisible = false;\n              this.getpdgData();\n            } else {\n              this.$message.error(\"操作失败\");\n            }\n          });\n        } else {\n          setTimeout(() => {\n            var isError = document.getElementsByClassName(\"is-error\");\n            if (isError[0].querySelector(\"input\")) {\n              isError[0].querySelector(\"input\").focus();\n            } else if (isError[0].querySelector(\"textarea\")) {\n              isError[0].querySelector(\"textarea\").focus();\n            }\n          }, 1);\n          return false;\n        }\n      });\n    },\n\n    /*---------------------配电柜相关结束-----------------------*/\n\n    //设备表格数据\n    async getsbData(params) {\n      this.params = { ...this.params, ...params };\n      const param = this.params;\n      //投用日期范围查询\n      if (param.tyrqStr && param.tyrqStr.length > 0) {\n        param.tyBeginDate = this.dateFormatter(param.tyrqStr[0]);\n        param.tyEndDate = this.dateFormatter(param.tyrqStr[1]);\n      }\n      const { data, code } = await getList(param);\n      if (code === \"0000\") {\n        this.sbshow = true;\n        this.pdgshow = this.pdzshow = false;\n        this.tableAndPageInfo3.tableData = data.records;\n        this.tableAndPageInfo3.pager.total = data.total;\n        // this.tableAndPageInfo3.pager.pageResize = 'Y';\n      }\n    },\n    //获取配电室下拉框数据\n    getPdsOptionsDataList() {\n      getPdsOptionsDataList({}).then((res) => {\n        this.pdsOptionsDataList = res.data\n        //所属配电室筛查条件\n        this.filterInfo.fieldList[0].options = this.pdsOptionsDataList;\n      });\n    },\n    //获取配电柜下拉框数据\n    getPdgListSelected() {\n      getPdgListSelected({ sszs: this.jbxxForm.sszs }).then((res) => {\n        this.sbOptionsDataList = res.data;\n      });\n    },\n    //配电室下拉框中的change事件\n    pdsOptionsChangeClick() {\n      //当发生change事件时先清空之前的信息\n      this.$set(this.jbxxForm, \"sskgg\", \"\");\n      this.getPdgListSelected();\n    },\n    //日期格式化  yyyy-MM-dd\n    dateFormatter(d) {\n      let year = d.getFullYear();\n      let month =\n        d.getMonth() < 9 ? \"0\" + (d.getMonth() + 1) : \"\" + (d.getMonth() + 1);\n      let day = d.getDate() < 10 ? \"0\" + d.getDate() : \"\" + d.getDate();\n      return year + \"-\" + month + \"-\" + day;\n    },\n\n    /*----------------------设备---------------------*/\n    //设备添加按钮\n    sbAddSensorButton() {\n      this.sbtitle = \"设备新增\";\n      this.dialogFormVisible = true;\n      this.isDisabled = false;\n    },\n    //设备基本信息修改\n    getSbXgButton(row) {\n      getPdsbOne({ id: row.id }).then((res) => {\n        if (res.code === \"0000\") {\n          this.sbtitle = \"设备修改\";\n          this.dialogFormVisible = true;\n          this.jbxxForm = res.data;\n          this.isDisabled = false;\n          this.getPdgListSelected();\n          this.technicalParameters(row);\n        } else {\n          this.$message.error(\"操作失败\");\n        }\n      });\n    },\n    //设备详情按钮\n    getSbXqButton(row) {\n      getPdsbOne({ id: row.id }).then((res) => {\n        if (res.code === \"0000\") {\n          this.sbtitle = \"设备详情查看\";\n          this.dialogFormVisible = true;\n          this.jbxxForm = res.data;\n          this.isDisabled = true;\n          this.getPdgListSelected();\n          this.technicalParameters(row);\n        } else {\n          this.$message.error(\"操作失败\");\n        }\n      });\n    },\n\n    /*----------------------配电室---------------------*/\n    //删除按钮\n    //删除配电柜\n    removepdg(id) {\n      // if (this.ids.length !== 0) {\n        let obj=[];\n      obj.push(id);\n        this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\",\n        })\n        .then(() => {\n          removePdg(obj).then(({ code }) => {\n              if (code === \"0000\") {\n                this.$message({\n                  type: \"success\",\n                  message: \"删除成功!\",\n                });\n                this.getpdgData();\n              } else {\n                this.$message({\n                  type: \"error\",\n                  message: \"删除失败!\",\n                });\n              }\n            });\n          })\n         .catch(() => {\n            this.$message({\n              type: \"info\",\n              message: \"已取消删除\",\n            });\n          });\n      // } else {\n      //   this.$message({\n      //     type: \"info\",\n      //     message: \"请选择至少一条数据!\",\n      //   });\n      // }\n    },\n    //删除配电室\n    async deletePds(id) {\n      let obj=[];\n      obj.push(id);\n      // if (this.ids.length != 0) {\n        this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\",\n        })\n          .then(() => {\n            removePds(obj).then(({ code }) => {\n              if (code === \"0000\") {\n                this.$message({\n                  type: \"success\",\n                  message: \"删除成功!\",\n                });\n                this.getpdzData();\n              } else {\n                this.$message({\n                  type: \"error\",\n                  message: \"删除失败!\",\n                });\n              }\n            });\n          })\n          .catch(() => {\n            this.$message({\n              type: \"info\",\n              message: \"已取消删除\",\n            });\n          });\n      // }\n    },\n    //删除设备\n    removesb(id) {\n      // if (this.selectRows.length < 1) {\n      //   this.$message.warning(\"请选择正确的数据！！！\");\n      //   return;\n      // }\n      // let ids = this.selectRows.map((item) => {\n      //   return item.id;\n      // });\n      let obj=[];\n      obj.push(id);\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      }).then(() => {\n        remove(obj)\n          .then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\",\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\",\n              });\n            }\n          })\n          .catch(() => {\n            this.$message({\n              type: \"info\",\n              message: \"已取消删除\",\n            });\n          });\n        this.getsbData();\n      });\n    },\n    async deleteRow(row) {\n      if (this.pdzshow) {\n        this.deletePds(row);\n      }\n      if (this.pdgshow) {\n        this.removepdg(row);\n      }\n      if (this.sbshow) {\n        this.removesb(row);\n      }\n    },\n    //获取HeadForm数据\n    async getHeadFormData(sbId){\n      if(this.treeNode.identifier === \"1\"){//配电站\n        await getPdsList({objId:sbId}).then((res) => {\n          this.headForm = res.data.records[0];\n          this.ywbzList.forEach((element) => {\n            if (this.headForm.ywbz == element.value) {\n              this.headForm.ywbzmc = element.label;\n            }\n          });\n        });\n      }else if(this.treeNode.identifier === \"2\"){//配电柜\n        let { data, code } = await getMergePdgInfo({objId:sbId});\n        if (code === \"0000\") {\n          this.headForm = data.records[0];\n          if (this.headForm.sjlx === \"kgg\"){\n            this.sskgg = this.headForm.objId\n          }\n        }\n      }\n    },\n    //树点击事件\n    handleNodeClick(data, e) {\n      this.treeNode = data;\n      //点击根节点显示所有\n      if (data.identifier === \"0\") {\n        this.pdzParams = {\n          pageNum: 1,\n          pageSize: 10,\n        }\n        this.tableAndPageInfo1.pager.pageResize = 'Y';\n        this.getpdzData();\n      }\n      //点击配电室节点显示附属设备（只归属与配电室不归属任何配电柜的设备）\n      if (data.identifier === \"1\") {\n        //重置Currentpage查询\n        this.pdgParams = {\n          pageNum: 1,\n          pageSize: 10,\n        }\n        this.sszs = data.id;\n        this.pdgSszs = data.id;\n        this.pdgSszsmc = data.label;\n        this.getHeadFormData(data.id);\n        this.pdgParams.sszs = this.sszs;\n        //重置pageNum显示\n        this.tableAndPageInfo2.pager.pageResize = 'Y';\n        this.getpdgData();\n      }\n      //点击配电柜节点显示所属配电设备\n      if (data.identifier === \"2\") {\n        this.params = {\n          pageNum: 1,\n          pageSize: 10,\n        }\n        this.params.pdgList = [data.id];\n        this.params.sszs = this.sszs;\n        this.params.sskgg = data.id;\n        this.sskgg=\"\"\n        this.getHeadFormData(data.id);\n        this.tableAndPageInfo3.pager.pageResize = 'Y';\n        this.getsbData();\n      }\n    },\n    //重置按钮\n    filterReset() {},\n    /*----------------------状态变更---------------------*/\n    //修改设备状态\n    updateStatus(row) {\n      this.updateList.zt = row.zt;\n      this.updateList.id = row.id;\n      this.dialogVisible = true;\n    },\n    //状态变更提交信息\n    submitStatus() {\n      this.$confirm(\"确认将设备状态修改为\" + this.updateList.zt + \"?\", \"\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      }).then((res) => {\n        updateStatus(this.updateList).then((res) => {\n          if (res.code == \"0000\") {\n            this.$message.success(\"设备状态已变更！\");\n            this.dialogVisible = false;\n            this.getData();\n          }\n        });\n      });\n    },\n    //查询状态变更记录信息\n    getResumList(par) {\n      let params = { ...par, ...this.resumeQuery };\n      getResumDataList(params).then((res) => {\n        this.resumPageInfo.tableData = res.data.records;\n        this.resumPageInfo.pager.total = res.data.total;\n      });\n    },\n    //左侧树形数据获取\n    getNewTreeInfo() {\n      getPdsTreeList(this.treeForm).then((res) => {\n        this.treeOptions = res.data;\n      });\n    },\n    //筛选条件\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n    //重置表单\n    resetForm() {\n      this.jbxxForm = {};\n      this.jscsForm = {};\n      this.$nextTick(function () {\n        this.$refs[\"jbxxForm\"].clearValidate();\n      });\n      this.dialogFormVisible = false;\n    },\n    getSblxDataListSelected() {\n      let sblxParam = {\n        type: \"配电设备\",\n      };\n      getSblxDataListSelected(sblxParam).then((res) => {\n        this.sblxOptionsDataSelected = res.data;\n      });\n    },\n    showParams(data) {\n      this.paramQuery.sblxbm = data;\n      this.getParameters();\n    },\n    technicalParameters(row) {\n      //设备类型\n      this.jscsForm = {};\n      this.paramQuery.sblxbm = row.sblx;\n      this.jscsForm.sblxbm = row.sblx;\n      this.jscsForm.sbbm = row.sszsyxbh;\n      this.getParameters();\n    },\n    getParameters() {\n      this.jscsLabelList = [];\n      getParamDataList(this.paramQuery).then((res) => {\n        this.jscsLabelList = res.data;\n        this.getParamValue();\n      });\n    },\n\n    getParamValue() {\n      getParamsValue(this.jscsForm).then((res) => {\n        if (res.data != \"\") {\n          this.jscsForm = { ...res.data[0] };\n        }\n      });\n    },\n    submit() {\n      try {\n        this.jbxxForm.sbClassCsValue = this.jscsForm;\n        let { code } = saveOrUpdate(this.jbxxForm).then((res) => {\n          if (res.code === \"0000\") {\n            this.$message({\n              type: \"success\",\n              message: \"操作成功!\",\n            });\n            this.dialogFormVisible = false;\n            this.getData();\n          }\n        });\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    init() {\n      this.getSblxDataListSelected();\n    },\n  },\n};\n</script>\n\n<style lang='scss' scoped>\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  height: calc(100vh - 225px);\n  overflow: auto;\n}\n\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.box-cardList {\n  height: 70%;\n}\n\n.item {\n  width: 200px;\n  float: left;\n}\n\n#main_container_dj {\n  height: calc(100vh - 84px);\n}\n\n.aside_height {\n  height: 81vh;\n}\n\n.defect .el-form-item:nth-child(odd) {\n  margin-right: 70px;\n}\n\n/*背景颜色调整*/\n#main_container_dj,\n#main_container_dj .el-aside {\n  background-color: #b4caf1;\n}\n\n/deep/ .qxlr_dialog_insert .el-dialog__header {\n  background-color: #0cc283;\n}\n\n/deep/ .pmyBtn {\n  background: #0cc283;\n}\n\n/*!*弹出框内宽度设置*!*/\n.el-select {\n  width: 100%;\n}\n\n.el-date-editor {\n  width: 100%;\n}\n\n.el-carousel__item h3 {\n  color: #475669;\n  font-size: 14px;\n  opacity: 0.75;\n  line-height: 150px;\n}\n\n.el-carousel__item:nth-child(2n) {\n  background-color: #99a9bf;\n}\n\n.el-carousel__item:nth-child(2n + 1) {\n  background-color: #d3dce6;\n}\n\n//有子节点 且未展开\n.el-tree ::v-deep .el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/add.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n\n//有子节点 且已展开\n.el-tree\n  ::v-deep\n  .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/prep.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n\n//没有子节点\n.el-tree ::v-deep .el-tree-node__expand-icon.is-leaf::before {\n  background: transparent;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n\n.categoryTreeIcons {\n  background: url(\"../../../../assets/icons/icon/icon1.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n\n.tableIcon {\n  background: url(\"../../../../assets/icons/icon/icon2.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n\n.classIcon {\n  background: url(\"../../../../assets/icons/icon/icon5.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n\n.imgCls {\n  height: 150px !important;\n}\n\n#pic_form .el-form-item__content {\n  margin-left: 0 !important;\n}\n</style>\n<style>\n#imgId .el-carousel__container {\n  background-color: #ffffff !important;\n}\n</style>\n"]}]}