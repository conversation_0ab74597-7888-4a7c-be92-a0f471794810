{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\jbwh_jbxx.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\jbwh_jbxx.vue", "mtime": 1706897323219}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IExvYWRpbmcgfSBmcm9tICdlbGVtZW50LXVpJwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdqYndoX2pieHgnLAogIHByb3BzOiB7CiAgICBzYmx4OnsKICAgICAgdHlwZTpTdHJpbmcsCiAgICAgIGRlZmF1bHQ6JycsCiAgICB9LAogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGxvYWRpbmc6IG51bGwsLy/pga7nvanlsYIKICAgICAgdGFibGVBbmRQYWdlSW5mbzogewogICAgICAgIHBhZ2VyOiB7CiAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgdG90YWw6IDAsCiAgICAgICAgICBzaXplczogWzEwLCAyMCwgNTAsIDEwMF0sCiAgICAgICAgfSwKICAgICAgICBvcHRpb246IHsKICAgICAgICAgIHNlcmlhbE51bWJlcjogdHJ1ZSwKICAgICAgICB9LAogICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgdGFibGVIZWFkZXI6IFsKICAgICAgICAgIC8vIHsgcHJvcDogImJtIiwgbGFiZWw6ICLnvJbnoIEifSwKICAgICAgICAgIHsgcHJvcDogIm9iaklkIiwgbGFiZWw6ICLnvJbnoIEifSwKICAgICAgICAgIHsgcHJvcDogIm1jIiwgbGFiZWw6ICLlkI3np7AiIH0sCiAgICAgICAgXSwKICAgICAgfSwKICAgIH07CiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy5nZXREYXRhKCk7CiAgfSwKICBtZXRob2RzOnsKICAgIGFzeW5jIGdldERhdGEoKSB7CiAgICAgIC8v5byA5ZCv6YGu572p5bGCCiAgICAgIHRoaXMubG9hZGluZyA9IExvYWRpbmcuc2VydmljZSh7CiAgICAgICAgbG9jazogdHJ1ZSwgLy9sb2Nr55qE5L+u5pS556ymLS3pu5jorqTmmK9mYWxzZQogICAgICAgIHRleHQ6ICLliqDovb3kuK3vvIzor7fnqI3lkI4iLCAvL+aYvuekuuWcqOWKoOi9veWbvuagh+S4i+aWueeahOWKoOi9veaWh+ahiAogICAgICAgIHNwaW5uZXI6ICJlbC1pY29uLWxvYWRpbmciLCAvL+iHquWumuS5ieWKoOi9veWbvuagh+exu+WQjQogICAgICAgIGJhY2tncm91bmQ6ICJyZ2JhKDAsIDAsIDAsIDAuNykiLCAvL+mBrue9qeWxguminOiJsgogICAgICAgIHRhcmdldDogZG9jdW1lbnQucXVlcnlTZWxlY3RvcigiI3RhYmxlX2pieHgiKSwKICAgICAgfSk7CgogICAgICB0aGlzLmxvYWRpbmcuY2xvc2UoKTsvL+WFs+mXremBrue9qeWxggogICAgfSwKICAgIGNsaWNrTWFpblRhYmxlKHZhbCl7CiAgICAgIGxldCBtYXAgPSBuZXcgTWFwKCk7CiAgICAgIG1hcC5zZXQodmFsLm9iaklkLHZhbC5tYyk7CiAgICAgIHRoaXMuJGVtaXQoJ2RiQ2xpY2tSb3cnLCdnZXRQYXJhbWV0ZXIoJyArIHZhbC5vYmpJZCArICcpJyxtYXApOwogICAgfSwKICB9LAp9Cg=="}, {"version": 3, "sources": ["jbwh_jbxx.vue"], "names": [], "mappings": ";;;;;;;;;;AAUA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "jbwh_jbxx.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk", "sourcesContent": ["<template>\n  <comp-table\n    :table-and-page-info=\"tableAndPageInfo\"\n    @rowDbClick=\"clickMainTable\"\n    height=\"58vh\"\n    id=\"table_jbxx\"\n  />\n</template>\n\n<script>\nimport { Loading } from 'element-ui'\n\nexport default {\n  name: 'jbwh_jbxx',\n  props: {\n    sblx:{\n      type:String,\n      default:'',\n    },\n  },\n  data() {\n    return {\n      loading: null,//遮罩层\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n        },\n        option: {\n          serialNumber: true,\n        },\n        tableData: [],\n        tableHeader: [\n          // { prop: \"bm\", label: \"编码\"},\n          { prop: \"objId\", label: \"编码\"},\n          { prop: \"mc\", label: \"名称\" },\n        ],\n      },\n    };\n  },\n  mounted() {\n    this.getData();\n  },\n  methods:{\n    async getData() {\n      //开启遮罩层\n      this.loading = Loading.service({\n        lock: true, //lock的修改符--默认是false\n        text: \"加载中，请稍后\", //显示在加载图标下方的加载文案\n        spinner: \"el-icon-loading\", //自定义加载图标类名\n        background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n        target: document.querySelector(\"#table_jbxx\"),\n      });\n\n      this.loading.close();//关闭遮罩层\n    },\n    clickMainTable(val){\n      let map = new Map();\n      map.set(val.objId,val.mc);\n      this.$emit('dbClickRow','getParameter(' + val.objId + ')',map);\n    },\n  },\n}\n</script>\n\n<style scoped>\n\n</style>\n"]}]}