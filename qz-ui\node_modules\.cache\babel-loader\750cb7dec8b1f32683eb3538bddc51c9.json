{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\activiti_sybg\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\activiti_sybg\\index.vue", "mtime": 1706897321038}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AA6HA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBACA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,KAAA,EAAA;AACA;;;;;;;;;;;;AAYA,IAAA,WAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAFA,sBAEA;AACA,eAAA;AACA,UAAA,WAAA,EAAA,IADA;AAEA,UAAA,WAAA,EAAA;AAFA,SAAA;AAIA;AAPA,KAbA;AAuBA;AACA,IAAA,MAAA,EAAA;AACA,MAAA,IAAA,EAAA,OADA;AAEA,MAAA,OAAA,EAAA;AAFA,KAxBA;AA4BA;AACA,IAAA,MAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAFA,sBAEA;AACA,eAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAAA;AACA;AAJA;AA7BA,GAFA;AAuCA,EAAA,IAvCA,kBAuCA;AACA,WAAA;AACA,MAAA,QAAA,EAAA,KADA;AAEA,MAAA,IAAA,EAAA,EAFA;AAGA,MAAA,KAAA,EAAA,EAHA;AAIA,MAAA,OAAA,EAAA,IAJA;AAKA,MAAA,YAAA,EAAA;AALA,KAAA;AAOA,GA/CA;AAgDA,EAAA,KAAA,EAAA;AACA,IAAA,WAAA,EAAA;AACA,MAAA,OADA,mBACA,MADA,EACA,MADA,EACA;AAAA;;AACA,aAAA,SAAA,CAAA,YAAA;AACA,UAAA,KAAA,CAAA,KAAA,mCAAA,MAAA;AACA,cAAA,IAAA,GAAA,KAAA,CAAA,KAAA,CAAA,SAAA,CAAA,IAAA;AACA,cAAA,EAAA,GAAA,KAAA,CAAA,KAAA,CAAA,SAAA,CAAA,EAAA;AACA,cAAA,aAAA,GAAA,EAAA;;AACA,kBAAA,IAAA;AACA,iBAAA,CAAA;AACA,cAAA,aAAA,GAAA,EAAA;AACA;;AACA,iBAAA,CAAA;AACA,cAAA,aAAA,GAAA,EAAA;AACA;;AACA,iBAAA,CAAA;AACA,sBAAA,EAAA;AACA,qBAAA,MAAA;AAAA;AACA,kBAAA,aAAA,GAAA,GAAA;AACA;;AACA,qBAAA,MAAA;AAAA;AACA,kBAAA,aAAA,GAAA,GAAA;AACA;;AACA,qBAAA,MAAA;AAAA;AACA,kBAAA,aAAA,GAAA,GAAA;AACA;AATA;;AAWA;AAnBA;;AAqBA,mCAAA;AACA,YAAA,aAAA,EAAA,aADA;AAEA,YAAA,MAAA,EAAA,CAFA;AAGA,YAAA,QAAA,EAAA;AAHA,WAAA,EAIA,IAJA,CAIA,UAAA,GAAA,EAAA;AACA,YAAA,KAAA,CAAA,YAAA,GAAA,GAAA,CAAA,IAAA;AACA,WANA;AAOA,SAjCA;AAkCA,OApCA;AAqCA,MAAA,IAAA,EAAA,IArCA;AAsCA,MAAA,SAAA,EAAA;AAtCA;AADA,GAhDA;AA0FA,EAAA,OA1FA,qBA0FA,CAAA,CA1FA;AA2FA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,UAFA,wBAEA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,SAAA,GAAA,MAAA,CAAA,MAAA,CAAA,IAAA,CADA,CAEA;;AACA,oBAAA,MAAA,CAAA,IAAA,CAAA,QAAA,EAAA;AACA;AACA,sBAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,oBAAA,QADA,GACA,EADA;;AAEA,oBAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,CAAA,EAAA;AACA,sBAAA,QAAA,IAAA,CAAA,CAAA,QAAA,GAAA,GAAA;AACA,qBAFA,EAFA,CAKA;;;AACA,wBAAA,QAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,sBAAA,QAAA,GAAA,QAAA,CAAA,MAAA,CAAA,CAAA,EAAA,QAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA;;AACA,oBAAA,MAAA,CAAA,KAAA,CAAA,QAAA,GAAA,QAAA;AACA,mBAVA,MAUA;AACA,oBAAA,MAAA,CAAA,KAAA,CAAA,QAAA,GAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,QAAA;AACA,oBAAA,MAAA,CAAA,KAAA,CAAA,gBAAA,GAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,QAAA;AACA;AACA,iBAhBA,MAgBA;AACA,sBAAA,MAAA,CAAA,WAAA,CAAA,YAAA,EAAA;AACA;AACA,wBAAA,MAAA,CAAA,WAAA,CAAA,YAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,sBAAA,SADA,GACA,EADA;AAEA,sBAAA,QAFA,GAEA,EAFA;;AAGA,sBAAA,MAAA,CAAA,WAAA,CAAA,YAAA,CAAA,OAAA,CAAA,UAAA,CAAA,EAAA;AACA,wBAAA,SAAA,IAAA,CAAA,CAAA,QAAA,GAAA,GAAA;AACA,wBAAA,QAAA,IAAA,CAAA,CAAA,QAAA,GAAA,GAAA;AACA,uBAHA,EAHA,CAOA;;;AACA,0BAAA,SAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,wBAAA,SAAA,GAAA,SAAA,CAAA,MAAA,CAAA,CAAA,EAAA,SAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA;;AACA,0BAAA,QAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,wBAAA,QAAA,GAAA,QAAA,CAAA,MAAA,CAAA,CAAA,EAAA,QAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA;;AACA,sBAAA,MAAA,CAAA,KAAA,CAAA,QAAA,GAAA,SAAA;AACA,sBAAA,MAAA,CAAA,KAAA,CAAA,gBAAA,GAAA,QAAA;AACA,qBAhBA,MAgBA;AACA,sBAAA,MAAA,CAAA,KAAA,CAAA,QAAA,GAAA,MAAA,CAAA,WAAA,CAAA,YAAA,CAAA,QAAA;AACA,sBAAA,MAAA,CAAA,KAAA,CAAA,gBAAA,GAAA,MAAA,CAAA,WAAA,CAAA,YAAA,CAAA,QAAA;AACA;AACA,mBAtBA,MAsBA;AACA,oBAAA,MAAA,CAAA,KAAA,CAAA,QAAA,GAAA,SAAA;AACA,oBAAA,MAAA,CAAA,KAAA,CAAA,gBAAA,GAAA,SAAA;AACA;AACA;;AA9CA,sBAgDA,CAAA,MAAA,CAAA,KAAA,CAAA,QAAA,IACA,MAAA,CAAA,KAAA,CAAA,WAAA,KAAA,UADA,IAEA,MAAA,CAAA,KAAA,CAAA,WAlDA;AAAA;AAAA;AAAA;;AAoDA,gBAAA,MAAA,CAAA,QAAA,CAAA;AACA,kBAAA,IAAA,EAAA,OADA;AAEA,kBAAA,OAAA,EAAA;AAFA,iBAAA;;AApDA;;AAAA;AA0DA,oBAAA,MAAA,CAAA,IAAA,CAAA,OAAA,EAAA;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,SAAA,CAAA,OAAA,GAAA,MAAA,CAAA,IAAA,CAAA,OAAA;AACA;;AACA,gBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,kBAAA,MAAA,CAAA,OAAA,GAAA,mBAAA,OAAA,CAAA;AACA,oBAAA,IAAA,EAAA,IADA;AACA;AACA,oBAAA,IAAA,EAAA,WAFA;AAEA;AACA,oBAAA,OAAA,EAAA,iBAHA;AAGA;AACA,oBAAA,UAAA,EAAA,oBAJA;AAIA;AACA,oBAAA,MAAA,EAAA,QAAA,CAAA,aAAA,CAAA,YAAA;AALA,mBAAA,CAAA;AAOA,iBATA;;AA7DA;AAAA;AAAA,uBAyEA,+BAAA,MAAA,CAAA,KAAA,CAzEA;;AAAA;AAAA;AAyEA,gBAAA,IAzEA,uBAyEA,IAzEA;AAyEA,gBAAA,IAzEA,uBAyEA,IAzEA;;AA0EA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,UAAA,GAAA,IAAA;AACA;;AACA,oBAAA,IAAA,EAAA;AACA,kBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,oBAAA,MAAA,CAAA,OAAA,CAAA,KAAA;AACA,mBAHA;AAIA;;AAlFA;AAAA;;AAAA;AAAA;AAAA;;AAoFA,gBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,kBAAA,MAAA,CAAA,OAAA,CAAA,KAAA;AACA,iBAHA;;AApFA;AAyFA,oBAAA,UAAA,EAAA;AACA,kBAAA,UAAA,CAAA,WAAA,GAAA,MAAA,CAAA,KAAA,CAAA,WAAA;;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,UAAA,EAAA,UAAA;AACA;;AACA,gBAAA,MAAA,CAAA,OAAA;;AA7FA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA8FA,KAhGA;AAiGA,IAAA,OAjGA,qBAiGA;AACA,WAAA,IAAA,CAAA,KAAA,IAAA,EAAA,UAAA,EAAA,EAAA;AACA,WAAA,IAAA,CAAA,KAAA,IAAA,EAAA,SAAA,EAAA,EAAA;AACA,WAAA,KAAA,CAAA,SAAA,EAAA,OAAA;AACA;AArGA;AA3FA,C", "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      id=\"dialogAct\"\n      class=\"tree\"\n      :title=\"\n        datas.variables\n          ? datas.variables.title\n            ? datas.variables.title\n            : ''\n          : ''\n      \"\n      :top=\"option.top ? option.top : '10vh'\"\n      v-dialogDrag\n      :visible.sync=\"isShow\"\n      :width=\"option.width ? option.width : '25%'\"\n      :close-on-click-modal=\"false\"\n      @close=\"toClose\"\n      :destroy-on-colse=\"true\"\n      :modal-append-to-body=\"false\"\n    >\n      <slot name=\"Dialog_content\" />\n      <el-form\n        label-width=\"120px\"\n        ref=\"form\"\n        :model=\"form\"\n        v-if=\"datas.defaultFrom ? true : false\"\n      >\n        <div>\n          <el-row>\n            <div v-if=\"datas.processType === 'complete'\">\n              <el-col :span=\"24\" v-if=\"datas.nextUser ? datas.nextUser : true\">\n                <el-form-item prop=\"nextUser\" label=\"审批人:\">\n                  <el-select\n                    v-model=\"form.nextUser\"\n                    placeholder=\"请选择人员\"\n                    style=\"width: 100%\"\n                    value-key=\"userName\"\n                    :disabled=\"disabled\"\n                    clearable\n                    filterable\n                  >\n                    <el-option\n                      v-for=\"item in gzfzrOptions\"\n                      :key=\"item.userName\"\n                      :label=\"item.nickName\"\n                      :value=\"{\n                        userName: item.userName,\n                        nickName: item.nickName\n                      }\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </div>\n            <!--    根据人员组选人(多选)   -->\n            <div v-if=\"datas.processType === 'completeMany'\">\n              <el-col :span=\"24\">\n                <el-form-item prop=\"nextUser\" label=\"人员选择：\">\n                  <el-select\n                    v-model=\"form.nextUser\"\n                    placeholder=\"请选择人员\"\n                    style=\"width: 100%;\"\n                    value-key=\"userName\"\n                    multiple\n                    clearable\n                    filterable\n                  >\n                    <el-option\n                      v-for=\"item in gzfzrOptions\"\n                      :key=\"item.userName\"\n                      :label=\"item.nickName\"\n                      :value=\"{\n                        userName: item.userName,\n                        nickName: item.nickName\n                      }\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </div>\n            <el-col :span=\"24\">\n              <el-form-item\n                prop=\"comment\"\n                label=\"原因填报：\"\n                v-if=\"datas.processType === 'rollback'\"\n              >\n                <el-input\n                  style=\"width: 100%\"\n                  v-model=\"form.comment\"\n                  type=\"textarea\"\n                  placeholder=\"请输入原因\"\n                />\n              </el-form-item>\n            </el-col>\n            <!--\n            <el-col :span=\"24\">\n              <el-form-item\n                prop=\"comment\"\n                label=\"挂起审核：\"\n                v-if=\"datas.processType === 'gqrollback'\"\n              >\n                <el-input\n                  style=\"width: 100%\"\n                  v-model=\"form.comment\"\n                  type=\"textarea\"\n                  placeholder=\"请输入挂起审核原因\"\n                />\n              </el-form-item>\n            </el-col> -->\n          </el-row>\n        </div>\n      </el-form>\n      <span v-else>请确定是否提交</span>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"info\" @click=\"toClose\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"todoSubmit('form')\">确定</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { Loading } from \"element-ui\";\nimport { getUsers } from \"@/api/system/usergroup\";\nimport { completeTask } from \"@/api/activiti/processTask\";\nexport default {\n  name: \"index\",\n  props: {\n    /*流程发起必填数据\n        processData:{\n          processDefinitionKey:\"流程定义的key\",//必填\n          taskId:\"任务id，如果任务在代办列表时，会根据代办列表获取taskid，否则需传入业务id来确定task\",\n          businessKey:\"业务id，对应业务的主键\",//taskid和businessKey两者必须有其中一个才可以确定一个task\n          businessType:\"业务类型，用于区分不同的业务\"//必填\n          variables:\"拓展参数\"//流程定义中设置的参数,\n          nextUser:\"如果流程实例中并未配置每一个节点的处理人，则需要用户手动选择每一个节点的处理人\",\n          nextUserInfo:{},//手动传的审核人信息\n          processType:'complete,rollback',\n          defaultFrom:true,false 是否需要默认表单\n        }*/\n    processData: {\n      type: Object,\n      default() {\n        return {\n          defaultFrom: true,\n          processType: \"complete\"\n        };\n      }\n    },\n\n    //显示隐藏\n    isShow: {\n      type: Boolean,\n      default: false\n    },\n    //子组件默认参数\n    option: {\n      type: Object,\n      default() {\n        return { title: \"审批\" };\n      }\n    }\n  },\n\n  data() {\n    return {\n      disabled: false,\n      form: {},\n      datas: {},\n      loading: null,\n      gzfzrOptions: []\n    };\n  },\n  watch: {\n    processData: {\n      handler(newVal, oldVal) {\n        this.$nextTick(() => {\n          this.datas = { ...newVal };\n          let ishg = this.datas.variables.ishg;\n          let zy = this.datas.variables.zy;\n          let personGroupId = 59;\n          switch (ishg) {\n            case 2:\n              personGroupId = 51;\n              break;\n            case 3:\n              personGroupId = 57;\n              break;\n            case 7:\n              switch (zy) {\n                case \"变电设备\": //变电\n                  personGroupId = 123;\n                  break;\n                case \"配电设备\": //配电\n                  personGroupId = 128;\n                  break;\n                case \"输电设备\": //输电\n                  personGroupId = 129;\n                  break;\n              }\n              break;\n          }\n          getUsers({\n            personGroupId: personGroupId,\n            deptId: 0,\n            deptName: \"\"\n          }).then(res => {\n            this.gzfzrOptions = res.data;\n          });\n        });\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  mounted() {},\n  methods: {\n    //流程提交\n    async todoSubmit() {\n      this.datas.routePath = this.$route.path;\n      // debugger;\n      if (this.form.nextUser) {\n        //处理人多选\n        if (this.form.nextUser.length > 0) {\n          let nextUser = \"\";\n          this.form.nextUser.forEach(e => {\n            nextUser += e.userName + \",\";\n          });\n          //去掉最后一个逗号\n          if (nextUser.length > 0) {\n            nextUser = nextUser.substr(0, nextUser.length - 1);\n          }\n          this.datas.nextUser = nextUser;\n        } else {\n          this.datas.nextUser = this.form.nextUser.userName;\n          this.datas.nextUserNickName = this.form.nextUser.nickName;\n        }\n      } else {\n        if (this.processData.nextUserInfo) {\n          //手动传的用户信息\n          if (this.processData.nextUserInfo.length > 0) {\n            let nextUser = \"\";\n            let nextNick = \"\";\n            this.processData.nextUserInfo.forEach(e => {\n              nextUser += e.userName + \",\";\n              nextNick += e.nickName + \",\";\n            });\n            //去掉最后一个逗号\n            if (nextUser.length > 0) {\n              nextUser = nextUser.substr(0, nextUser.length - 1);\n            }\n            if (nextNick.length > 0) {\n              nextNick = nextNick.substr(0, nextNick.length - 1);\n            }\n            this.datas.nextUser = nextUser;\n            this.datas.nextUserNickName = nextNick;\n          } else {\n            this.datas.nextUser = this.processData.nextUserInfo.userName;\n            this.datas.nextUserNickName = this.processData.nextUserInfo.nickName;\n          }\n        } else {\n          this.datas.nextUser = undefined;\n          this.datas.nextUserNickName = undefined;\n        }\n      }\n      if (\n        !this.datas.nextUser &&\n        this.datas.processType === \"complete\" &&\n        this.datas.defaultFrom\n      ) {\n        this.$message({\n          type: \"error\",\n          message: \"请选择人员!\"\n        });\n        return;\n      }\n      if (this.form.comment) {\n        this.datas.variables.comment = this.form.comment;\n      }\n      this.$nextTick(() => {\n        // 以服务的方式调用的 Loading 需要异步关闭\n        this.loading = Loading.service({\n          lock: true, //lock的修改符--默认是false\n          text: \"流程进行中，请稍后\", //显示在加载图标下方的加载文案\n          spinner: \"el-icon-loading\", //自定义加载图标类名\n          background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n          target: document.querySelector(\"#dialogAct\")\n        });\n      });\n      let resultData;\n      try {\n        let { code, data } = await completeTask(this.datas);\n        if (code === \"0000\") {\n          resultData = data;\n        }\n        if (code) {\n          this.$nextTick(() => {\n            // 以服务的方式调用的 Loading 需要异步关闭\n            this.loading.close();\n          });\n        }\n      } catch (e) {\n        this.$nextTick(() => {\n          // 以服务的方式调用的 Loading 需要异步关闭\n          this.loading.close();\n        });\n      }\n      if (resultData) {\n        resultData.processType = this.datas.processType;\n        this.$emit(\"todoData\", resultData);\n      }\n      this.toClose();\n    },\n    toClose() {\n      this.$set(this.form,'nextUser',\"\")\n      this.$set(this.form,'comment',\"\")\n      this.$emit(\"toClose\", \"close\");\n    }\n  }\n};\n</script>\n"], "sourceRoot": "src/components/activiti_sybg"}]}