{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\jxbzk\\jxxmwh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\jxbzk\\jxxmwh.vue", "mtime": 1706897322435}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgZ2V0TGlzdCwKICByZW1vdmUsCiAgc2F2ZU9yVXBkYXRlCn0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYnpnbC9qeGJ6ay9qeHhtd2giOwovLyBpbXBvcnQgRGV2aWNlVHJlZSBmcm9tICdAL3ZpZXdzL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvc2JiemsvZGV2aWNlVHJlZScKaW1wb3J0IHsgZ2V0RGljdFR5cGVEYXRhIH0gZnJvbSAiQC9hcGkvc3lzdGVtL2RpY3QvZGF0YSI7CmltcG9ydCB7IGdldERldmljZUNsYXNzR3JvdXAgfSBmcm9tICJAL2FwaS9kYWdhbmdPaWxmaWVsZC9iemdsL3NibHh3aC9zYmx4d2giOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogImp4eG13aCIsCiAgLy8gY29tcG9uZW50czogeyBEZXZpY2VUcmVlIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGN1cnJlbnRVc2VyOiB0aGlzLiRzdG9yZS5nZXR0ZXJzLm5hbWUsCiAgICAgIC8vZm9ybeihqOWNlQogICAgICBmb3JtOiB7fSwKICAgICAgLy/mmK/lkKbmmL7npLrlvLnmoYYKICAgICAgaXNTaG93RGV0YWlsczogZmFsc2UsCiAgICAgIC8v5piv5ZCm56aB55SoCiAgICAgIGlzRGlzYWJsZWQ6IGZhbHNlLAogICAgICAvL+ihqOWNleaVsOaNrgogICAgICBkYXRhVGFibGU6IFtdLAogICAgICBvcHRpb25zOiBbCiAgICAgICAgeyBsYWJlbDogIuWFqOmDqCIsIHZhbHVlOiAiIiB9LAogICAgICAgIHsgbGFiZWw6ICLmtYvor5UxIiwgdmFsdWU6ICIxIiB9LAogICAgICAgIHsgbGFiZWw6ICLmtYvor5UyIiwgdmFsdWU6ICIwIiB9CiAgICAgIF0sCiAgICAgIC8v5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgZmlsdGVySW5mbzogewogICAgICAgIGRhdGE6IHsKICAgICAgICAgIHNiZmw6ICIiLAogICAgICAgICAganhmbDogIiIsCiAgICAgICAgICBqeGZsQXJyOiBbXSwKICAgICAgICAgIHhtYmg6ICIiLAogICAgICAgICAgeG1tYzogIiIKICAgICAgICB9LAogICAgICAgIGZpZWxkTGlzdDogWwogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIuiuvuWkh+WIhuexuyIsCiAgICAgICAgICAgIHZhbHVlOiAic2JmbCIsCiAgICAgICAgICAgIHR5cGU6ICJzZWxlY3RHcm91cGp4eG13aCIsCiAgICAgICAgICAgIGNsZWFyYWJsZTogdHJ1ZSwKICAgICAgICAgICAgZmlsdGVyYWJsZTogdHJ1ZSwKICAgICAgICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgICAgICAgIG9wdGlvbnM6IFtdCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIumhueebrue8luWPtyIsCiAgICAgICAgICAgIHZhbHVlOiAieG1iaCIsCiAgICAgICAgICAgIHR5cGU6ICJpbnB1dCIsCiAgICAgICAgICAgIGNsZWFyYWJsZTogdHJ1ZQogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLmo4Dkv67pobnnm64iLAogICAgICAgICAgICB2YWx1ZTogImp4eG0iLAogICAgICAgICAgICB0eXBlOiAiaW5wdXQiLAogICAgICAgICAgICBjbGVhcmFibGU6IHRydWUKICAgICAgICAgIH0sCiAgICAgICAgICAvKnsKICAgICAgICAgICAgbGFiZWw6ICfmo4Dkv67liIbnsbsnLAogICAgICAgICAgICB2YWx1ZTogJ2p4ZmxBcnInLAogICAgICAgICAgICB0eXBlOiAnc2VsZWN0JywKICAgICAgICAgICAgLy8gdHlwZTogJ2NoZWNrYm94JywKICAgICAgICAgICAgLy8gY2hlY2tib3hWYWx1ZTogW10sCiAgICAgICAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAgICAgICBvcHRpb25zOiBbCiAgICAgICAgICAgICAgeyBsYWJlbDogJ+WFqOmDqCcsIHZhbHVlOiAnJyB9LAogICAgICAgICAgICAgIHsgbGFiZWw6ICfmtYvor5UxJywgdmFsdWU6ICcxJyB9LAogICAgICAgICAgICAgIHsgbGFiZWw6ICfmtYvor5UyJywgdmFsdWU6ICcwJyB9CiAgICAgICAgICAgIF0sCiAgICAgICAgICAgIGNsZWFyYWJsZTogdHJ1ZQogICAgICAgICAgfSwqLwogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIuajgOS/ruWIhuexuyIsCiAgICAgICAgICAgIHZhbHVlOiAianhmbCIsCiAgICAgICAgICAgIHR5cGU6ICJjaGVja2JveCIsCiAgICAgICAgICAgIGNoZWNrYm94VmFsdWU6IFtdLAogICAgICAgICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgICAgICAgb3B0aW9uczogW10sCiAgICAgICAgICAgIGNsZWFyYWJsZTogdHJ1ZQogICAgICAgICAgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgdGFibGVBbmRQYWdlSW5mbzogewogICAgICAgIHBhZ2VyOiB7CiAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgdG90YWw6IDAsCiAgICAgICAgICBzaXplczogWzEwLCAyMCwgNTAsIDEwMF0KICAgICAgICB9LAogICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgdGFibGVIZWFkZXI6IFsKICAgICAgICAgIHsgbGFiZWw6ICLorr7lpIfliIbnsbsiLCBwcm9wOiAic2JmbG1jIiwgbWluV2lkdGg6ICI4MCIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLmo4Dkv67liIbnsbsiLCBwcm9wOiAianhmbE5hbWUiLCBtaW5XaWR0aDogIjgwIiB9LAogICAgICAgICAgeyBsYWJlbDogIumhueebrue8luWPtyIsIHByb3A6ICJ4bWJoIiwgbWluV2lkdGg6ICIxMDAiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5qOA5L+u6aG555uuIiwgcHJvcDogImp4eG0iLCBtaW5XaWR0aDogIjE1MCIgfQogICAgICAgICAgLyogIHsKICAgICAgICAgICAgcHJvcDogJ29wZXJhdGlvbicsCiAgICAgICAgICAgIGxhYmVsOiAn5pON5L2cJywKICAgICAgICAgICAgbWluV2lkdGg6ICcxMzBweCcsCiAgICAgICAgICAgIHN0eWxlOiB7IGRpc3BsYXk6ICdibG9jaycgfSwKICAgICAgICAgICAgLy/mk43kvZzliJflm7rlrprlho3lj7PkvqcKICAgICAgICAgICAgZml4ZWQ6ICdyaWdodCcsCiAgICAgICAgICAgIG9wZXJhdGlvbjogWwogICAgICAgICAgICAgIHsgbmFtZTogJ+S/ruaUuScsIGNsaWNrRnVuOiB0aGlzLnVwZGF0ZVJvdyB9LAogICAgICAgICAgICAgIHsgbmFtZTogJ+ivpuaDhScsIGNsaWNrRnVuOiB0aGlzLmdldEluZm8gfQogICAgICAgICAgICBdCiAgICAgICAgICB9Ki8KICAgICAgICBdLAogICAgICAgIG9wdGlvbjogeyBjaGVja0JveDogdHJ1ZSwgc2VyaWFsTnVtYmVyOiB0cnVlIH0KICAgICAgfSwKICAgICAgcGFyYW1zOiB7CiAgICAgICAgc2JmbEFycjogW10sCiAgICAgICAganhmbEFycjogW10sCiAgICAgICAgeG1iaDogIiIsCiAgICAgICAgeG1tYzogIiIKICAgICAgfSwKICAgICAgc2VsZWN0Um93czogW10sCiAgICAgIC8v5qOA5L+u5YiG57G75LiL5ouJ5qGG5pWw5o2uCiAgICAgIGp4ZmxMaXN0OiBbXSwKICAgICAgRGV2aWNlc0xpc3RHcm91cDogW10sCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgc2JmbDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuiuvuWkh+WIhuexu+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0KICAgICAgICBdLAogICAgICAgIGp4Zmw6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmo4Dkv67liIbnsbvkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9CiAgICAgICAgXSwKICAgICAgICB4bWJoOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6aG555uu57yW5Y+35LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIGp4eG06IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5qOA5L+u6aG555uu5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH1dCiAgICAgIH0sCiAgICAgIHNob3dEZXZpY2VUcmVlOiBmYWxzZSwKICAgICAgaXNGaWx0ZXI6IGZhbHNlCiAgICB9OwogIH0sCiAgbW91bnRlZCgpIHsKICAgIHRoaXMuaW5pdERvbWFpbigpOwogICAgdGhpcy5nZXREZXZpY2VDbGFzc0dyb3VwKCk7CiAgfSwKICB3YXRjaDogewogICAgaXNTaG93RGV0YWlscyh2YWwpIHsKICAgICAgaWYgKHZhbCkgewogICAgICAgIGNvbnN0IGVsID0gdGhpcy4kZWwucXVlcnlTZWxlY3RvcigiLmVsLWRpYWxvZyIpOwogICAgICAgIGVsLnN0eWxlLmxlZnQgPSAwOwogICAgICAgIGVsLnN0eWxlLnRvcCA9IDA7CiAgICAgIH0KICAgIH0KICB9LAogIGNyZWF0ZWQoKSB7CiAgICAvL+WIl+ihqOafpeivogogICAgdGhpcy5nZXREYXRhKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvL+WIl+ihqOafpeivogogICAgYXN5bmMgZ2V0RGF0YShwYXJhbXMpIHsKICAgICAgdHJ5IHsKICAgICAgICB0aGlzLnBhcmFtcyA9IHsgLi4udGhpcy5wYXJhbXMsIC4uLnBhcmFtcyB9OwogICAgICAgIGNvbnN0IHBhcmFtID0gdGhpcy5wYXJhbXM7CiAgICAgICAgdGhpcy5wYXJhbXMgPSBwYXJhbTsKICAgICAgICAvLyBwYXJhbS5zYmZsQXJyID0gcGFyYW0uc2JmbCA9PT0gJycgPyBbXSA6IHBhcmFtLnNiZmxBcnIKICAgICAgICBjb25zdCB7IGRhdGEsIGNvZGUgfSA9IGF3YWl0IGdldExpc3QocGFyYW0pOwogICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby50YWJsZURhdGEgPSBkYXRhLnJlY29yZHM7CiAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIudG90YWwgPSBkYXRhLnRvdGFsOwoKICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby50YWJsZURhdGEuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgICAgdGhpcy5qeGZsTGlzdC5mb3JFYWNoKGVsZW1lbnQgPT4gewogICAgICAgICAgICAgIGlmIChpdGVtLmp4ZmwgPT09IGVsZW1lbnQudmFsdWUpIHsKICAgICAgICAgICAgICAgIGl0ZW0uanhmbE5hbWUgPSBlbGVtZW50LmxhYmVsOwogICAgICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSk7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICBjb25zb2xlLmxvZyhlKTsKICAgICAgfQogICAgfSwKICAgIC8v6YeN572u5oyJ6ZKuCiAgICBnZXRSZXNldCgpIHsKICAgICAgdGhpcy5wYXJhbXMgPSB7CiAgICAgICAgc2JmbEFycjogW10sCiAgICAgICAganhmbEFycjogW10sCiAgICAgICAganhmbDogW10sCiAgICAgICAgeG1iaDogIiIsCiAgICAgICAgeG1tYzogIiIKICAgICAgfTsKICAgICAgdGhpcy5maWx0ZXJJbmZvLmZpZWxkTGlzdC5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgIGlmIChpdGVtLnR5cGUgPT09ICJjaGVja2JveCIpIHsKICAgICAgICAgIGl0ZW0uY2hlY2tib3hWYWx1ZSA9IFtdOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy/pgInkuK3ooYwKICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZSgpIHt9LAogICAgLy/or6bmg4UKICAgIGdldERldGFpbHMoKSB7CiAgICAgIHRoaXMudGl0bGUgPSAi5qOA5L+u6aG555uu6K+m5oOFIjsKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gdHJ1ZTsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gdHJ1ZTsKICAgICAgdGhpcy5mb3JtID0geyAuLi5yb3cgfTsKICAgIH0sCiAgICAvL+aWsOWingogICAgZ2V0SW5zdGVyKCkgewogICAgICB0aGlzLnRpdGxlID0gIuajgOS/rumhueebruaWsOWiniI7CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICB0aGlzLmZvcm0gPSB7fTsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gdHJ1ZTsKICAgIH0sCiAgICAvL+e8lui+keaMiemSrgogICAgdXBkYXRlUm93KHJvdykgewogICAgICB0aGlzLnRpdGxlID0gIuajgOS/rumhueebruS/ruaUuSI7CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICB0aGlzLmZvcm0gPSB7IC4uLnJvdyB9OwogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlOwogICAgfSwKICAgIC8v6K+m5oOF5oyJ6ZKuCiAgICBnZXRJbmZvKHJvdykgewogICAgICB0aGlzLnRpdGxlID0gIuivpuaDheafpeeciyI7CiAgICAgIHRoaXMuZm9ybSA9IHsgLi4ucm93IH07CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IHRydWU7CiAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IHRydWU7CiAgICB9LAogICAgYXN5bmMgc2F2ZVJvdygpIHsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHNhdmVPclVwZGF0ZSh0aGlzLmZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLmk43kvZzmiJDlip8iKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICAgICAgICBjb25zb2xlLmxvZyhlKTsKICAgICAgICAgICAgfQogICAgICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICAgIH0pOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgfQogICAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvL+WIoOmZpOaMiemSrgogICAgYXN5bmMgZGVsZXRlUm93KHJvdykgewogICAgICB0aGlzLmZvcm0gPSB7IC4uLnJvdyB9OwogICAgICB0aGlzLiRjb25maXJtKCLmraTmk43kvZzlsIbmsLjkuYXliKDpmaTor6XmlbDmja4sIOaYr+WQpue7p+e7rT8iLCAi5o+Q56S6IiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkKICAgICAgICAudGhlbigoKSA9PiB7CiAgICAgICAgICByZW1vdmUoW3RoaXMuZm9ybS5vYmpJZF0pLnRoZW4oKHsgY29kZSB9KSA9PiB7CiAgICAgICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTmiJDlip8hIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTlpLHotKUhIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoKSA9PiB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogImluZm8iLAogICAgICAgICAgICBtZXNzYWdlOiAi5bey5Y+W5raI5Yig6ZmkIgogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgfSwKICAgIC8vZmlsdGVyX2NoYW5nZeS6i+S7tgogICAgaGFuZGxlRXZlbnQodmFsLCB2YWwxKSB7CiAgICAgIC8v5Y+Y55S156uZ5p+l6K+i5LiL5ouJ5qGG6YCJ6aG55qC55o2u5omA6YCJ5YiG5YWs5Y+45bim5Ye65p2lCiAgICAgIGlmICh2YWwubGFiZWwgPT09ICJzYmZsIiAmJiB2YWwudmFsdWUpIHsKICAgICAgICB0aGlzLnBhcmFtcy5zYmZsQXJyID0gdmFsLnZhbHVlOwogICAgICAgIGNvbnNvbGUubG9nKCJzYmZsQXJyIiwgdGhpcy5wYXJhbXMpOwogICAgICB9CiAgICB9LAogICAgLy/lhbPpl63lvLnnqpcKICAgIGNsb3NlKCkgewogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSBmYWxzZTsKICAgIH0sCiAgICBzZWxlY3RDaGFuZ2Uocm93cykgewogICAgICB0aGlzLnNlbGVjdFJvd3MgPSByb3dzOwogICAgfSwKICAgIHNob3dEZXZpY2VUcmVlRGlhbG9nKCkgewogICAgICB0aGlzLmlzRmlsdGVyID0gZmFsc2U7CiAgICAgIHRoaXMuc2hvd0RldmljZVRyZWUgPSB0cnVlOwogICAgfSwKICAgIGFzeW5jIGluaXREb21haW4oKSB7CiAgICAgIGxldCB7IGRhdGE6IGp4ZmwgfSA9IGF3YWl0IGdldERpY3RUeXBlRGF0YSgianhmbCIpOwogICAgICB0aGlzLmp4ZmxMaXN0ID0ganhmbDsKCiAgICAgIHRoaXMuZmlsdGVySW5mby5maWVsZExpc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICBpZiAoaXRlbS52YWx1ZSA9PT0gImp4ZmwiKSB7CiAgICAgICAgICBpdGVtLm9wdGlvbnMgPSBqeGZsOwogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgZ2V0RGV2aWNlQ2xhc3NHcm91cCgpIHsKICAgICAgZ2V0RGV2aWNlQ2xhc3NHcm91cChbImJkc2IiLCAicGRzYiIsICJzZHNiIl0pLnRoZW4ocmVzID0+IHsKICAgICAgICBpZiAocmVzLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgdGhpcy5EZXZpY2VzTGlzdEdyb3VwID0gcmVzLmRhdGE7CiAgICAgICAgICB0aGlzLmZpbHRlckluZm8uZmllbGRMaXN0LmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICAgIGlmIChpdGVtLnZhbHVlID09PSAic2JmbCIpIHsKICAgICAgICAgICAgICBpdGVtLm9wdGlvbnMgPSByZXMuZGF0YTsKICAgICAgICAgICAgICByZXR1cm47CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLojrflj5borr7lpIfliIbnsbvlpLHotKUiKTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIGlucHV0Rm9jdXNFdmVudCh2YWwpIHsKICAgICAgaWYgKHZhbC50YXJnZXQubmFtZSA9PT0gInNiZmwiKSB7CiAgICAgICAgdGhpcy5zaG93RGV2aWNlVHJlZSA9IHRydWU7CiAgICAgICAgdGhpcy5pc0ZpbHRlciA9IHRydWU7CiAgICAgIH0KICAgIH0sCiAgICBnZXREZXZpY2VUeXBlRGF0YShyZXMpIHsKICAgICAgaWYgKHRoaXMuaXNGaWx0ZXIpIHsKICAgICAgICB0aGlzLmZpbHRlckluZm8uZGF0YS5zYmZsQXJyID0gW107CiAgICAgICAgdGhpcy5maWx0ZXJJbmZvLmRhdGEuc2JmbCA9ICIiOwogICAgICAgIHJlcy5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgaWYgKGl0ZW0uY2hlY2tlZCkgewogICAgICAgICAgICB0aGlzLmZpbHRlckluZm8uZGF0YS5zYmZsQXJyLnB1c2goaXRlbS5jb2RlKTsKICAgICAgICAgICAgdGhpcy5maWx0ZXJJbmZvLmRhdGEuc2JmbCArPSBpdGVtLm5hbWUgKyAiLCI7CiAgICAgICAgICB9CiAgICAgICAgfSk7CgogICAgICAgIHRoaXMuZmlsdGVySW5mby5kYXRhLnNiZmwgPSB0aGlzLmZpbHRlckluZm8uZGF0YS5zYmZsLnN1YnN0cmluZygKICAgICAgICAgIDAsCiAgICAgICAgICB0aGlzLmZpbHRlckluZm8uZGF0YS5zYmZsLmxlbmd0aCAtIDEKICAgICAgICApOwogICAgICAgIHRoaXMuc2hvd0RldmljZVRyZWUgPSBmYWxzZTsKICAgICAgfSBlbHNlIHsKICAgICAgICBsZXQgdHJlZU5vZGVzID0gW107CiAgICAgICAgcmVzLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICBpZiAoaXRlbS5jaGVja2VkKSB7CiAgICAgICAgICAgIHRyZWVOb2Rlcy5wdXNoKGl0ZW0pOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICAgIGlmICh0cmVlTm9kZXMubGVuZ3RoID09PSAxKSB7CiAgICAgICAgICB0aGlzLmZvcm0uc2JmbG1jID0gdHJlZU5vZGVzWzBdLm5hbWU7CiAgICAgICAgICB0aGlzLmZvcm0uc2JmbCA9IHRyZWVOb2Rlc1swXS5jb2RlOwogICAgICAgICAgdGhpcy5zaG93RGV2aWNlVHJlZSA9IGZhbHNlOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+mAieaLqeWNleadoeiuvuWkh+aVsOaNriIpOwogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIGNsb3NlRGV2aWNlVHlwZURpYWxvZygpIHsKICAgICAgdGhpcy5zaG93RGV2aWNlVHJlZSA9IGZhbHNlOwogICAgfSwKICAgIC8v5riF56m66KGo5Y2V5pWw5o2uCiAgICBoYW5kbGVDbG9zZSgpIHsKICAgICAgdGhpcy5mb3JtID0ge307CiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICB0aGlzLmZvcm0gPSB0aGlzLiRvcHRpb25zLmRhdGEoKS5mb3JtOwogICAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICAgIH0pOwogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["jxxmwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "jxxmwh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/jxbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      @handleReset=\"getReset\"\n      @handleEvent=\"handleEvent\"\n    />\n    <div>\n      <el-white class=\"button-group\">\n        <div class=\"button_btn\">\n          <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"getInster\"\n            >新增</el-button\n          >\n        </div>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"selectChange\"\n          height=\"65vh\"\n        >\n          <el-table-column\n            slot=\"table_eight\"\n            align=\"center\"\n            fixed=\"right\"\n            style=\"display: block\"\n            label=\"操作\"\n            width=\"160\"\n            :resizable=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                @click=\"updateRow(scope.row)\"\n                v-show=\"scope.row.createBy == currentUser\"\n                type=\"text\"\n                size=\"small\"\n                title=\"修改\"\n                class=\"el-icon-edit\"\n              ></el-button>\n              <el-button\n                @click=\"getInfo(scope.row)\"\n                type=\"text\"\n                size=\"small\"\n                title=\"详情\"\n                class=\"el-icon-view\"\n              ></el-button>\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                v-show=\"scope.row.createBy == currentUser\"\n                @click=\"deleteRow(scope.row)\"\n                title=\"删除\"\n                class=\"el-icon-delete\"\n              ></el-button>\n            </template>\n          </el-table-column>\n        </comp-table>\n      </el-white>\n      <!-- 详情/新增/修改 -->\n      <el-dialog\n        :title=\"title\"\n        :visible.sync=\"isShowDetails\"\n        width=\"40%\"\n        @close=\"handleClose\"\n        v-dialogDrag\n      >\n        <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n          <el-row :gutter=\"8\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item v-show=\"false\" label=\"设备分类:\" prop=\"sbfl\">\n                <el-input v-model=\"form.sbfl\"></el-input>\n                <el-input v-model=\"form.objId\"></el-input>\n              </el-form-item>\n              <el-form-item label=\"设备分类：\" prop=\"sbfl\">\n                <el-select\n                  placeholder=\"请选择设备分类\"\n                  v-model=\"form.sbfl\"\n                  :disabled=\"isDisabled\"\n                  clearable\n                  filterable\n                  style=\"width: 100%\"\n                >\n                  <el-option-group\n                    v-for=\"group in DevicesListGroup\"\n                    :key=\"group.label\"\n                    :label=\"group.label\"\n                  >\n                    <el-option\n                      v-for=\"item in group.sbDataList\"\n                      :key=\"item.code\"\n                      :label=\"item.name\"\n                      :value=\"item.code\"\n                    >\n                    </el-option>\n                  </el-option-group>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"检修分类：\" prop=\"jxfl\">\n                <el-select\n                  placeholder=\"请选择检修分类\"\n                  v-model=\"form.jxfl\"\n                  clearable\n                  :disabled=\"isDisabled\"\n                  style=\"width: 100%\"\n                >\n                  <el-option\n                    v-for=\"item in jxflList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"项目编号：\" prop=\"xmbh\">\n                <el-input\n                  placeholder=\"请输入项目编号\"\n                  v-model=\"form.xmbh\"\n                  :disabled=\"isDisabled\"\n                />\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"24\">\n              <el-form-item label=\"检修项目：\" prop=\"jxxm\">\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"form.jxxm\"\n                  placeholder=\"请输入检修项目\"\n                  :disabled=\"isDisabled\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n        <div slot=\"footer\">\n          <el-button @click=\"close\">取 消</el-button>\n          <el-button\n            v-if=\"title == '检修项目新增' || title == '检修项目修改'\"\n            type=\"primary\"\n            @click=\"saveRow\"\n            >确 认</el-button\n          >\n        </div>\n      </el-dialog>\n\n      <!-- <el-dialog\n        :append-to-body=\"true\"\n        title=\"设备分类\"\n        :visible.sync=\"showDeviceTree\"\n        width=\"400px\"\n        v-if=\"showDeviceTree\">\n        <device-tree\n          @getDeviceTypeData=\"getDeviceTypeData\"\n          @closeDeviceTypeDialog=\"closeDeviceTypeDialog\">\n        </device-tree>\n      </el-dialog> -->\n    </div>\n  </div>\n</template>\n<script>\nimport {\n  getList,\n  remove,\n  saveOrUpdate\n} from \"@/api/dagangOilfield/bzgl/jxbzk/jxxmwh\";\n// import DeviceTree from '@/views/dagangOilfield/bzgl/sbbzk/deviceTree'\nimport { getDictTypeData } from \"@/api/system/dict/data\";\nimport { getDeviceClassGroup } from \"@/api/dagangOilfield/bzgl/sblxwh/sblxwh\";\nexport default {\n  name: \"jxxmwh\",\n  // components: { DeviceTree },\n  data() {\n    return {\n      currentUser: this.$store.getters.name,\n      //form表单\n      form: {},\n      //是否显示弹框\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //表单数据\n      dataTable: [],\n      options: [\n        { label: \"全部\", value: \"\" },\n        { label: \"测试1\", value: \"1\" },\n        { label: \"测试2\", value: \"0\" }\n      ],\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          sbfl: \"\",\n          jxfl: \"\",\n          jxflArr: [],\n          xmbh: \"\",\n          xmmc: \"\"\n        },\n        fieldList: [\n          {\n            label: \"设备分类\",\n            value: \"sbfl\",\n            type: \"selectGroupjxxmwh\",\n            clearable: true,\n            filterable: true,\n            multiple: true,\n            options: []\n          },\n          {\n            label: \"项目编号\",\n            value: \"xmbh\",\n            type: \"input\",\n            clearable: true\n          },\n          {\n            label: \"检修项目\",\n            value: \"jxxm\",\n            type: \"input\",\n            clearable: true\n          },\n          /*{\n            label: '检修分类',\n            value: 'jxflArr',\n            type: 'select',\n            // type: 'checkbox',\n            // checkboxValue: [],\n            multiple: true,\n            options: [\n              { label: '全部', value: '' },\n              { label: '测试1', value: '1' },\n              { label: '测试2', value: '0' }\n            ],\n            clearable: true\n          },*/\n          {\n            label: \"检修分类\",\n            value: \"jxfl\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            multiple: true,\n            options: [],\n            clearable: true\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"设备分类\", prop: \"sbflmc\", minWidth: \"80\" },\n          { label: \"检修分类\", prop: \"jxflName\", minWidth: \"80\" },\n          { label: \"项目编号\", prop: \"xmbh\", minWidth: \"100\" },\n          { label: \"检修项目\", prop: \"jxxm\", minWidth: \"150\" }\n          /*  {\n            prop: 'operation',\n            label: '操作',\n            minWidth: '130px',\n            style: { display: 'block' },\n            //操作列固定再右侧\n            fixed: 'right',\n            operation: [\n              { name: '修改', clickFun: this.updateRow },\n              { name: '详情', clickFun: this.getInfo }\n            ]\n          }*/\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        sbflArr: [],\n        jxflArr: [],\n        xmbh: \"\",\n        xmmc: \"\"\n      },\n      selectRows: [],\n      //检修分类下拉框数据\n      jxflList: [],\n      DevicesListGroup: [],\n      rules: {\n        sbfl: [\n          { required: true, message: \"设备分类不能为空\", trigger: \"change\" }\n        ],\n        jxfl: [\n          { required: true, message: \"检修分类不能为空\", trigger: \"change\" }\n        ],\n        xmbh: [\n          { required: true, message: \"项目编号不能为空\", trigger: \"blur\" }\n        ],\n        jxxm: [{ required: true, message: \"检修项目不能为空\", trigger: \"blur\" }]\n      },\n      showDeviceTree: false,\n      isFilter: false\n    };\n  },\n  mounted() {\n    this.initDomain();\n    this.getDeviceClassGroup();\n  },\n  watch: {\n    isShowDetails(val) {\n      if (val) {\n        const el = this.$el.querySelector(\".el-dialog\");\n        el.style.left = 0;\n        el.style.top = 0;\n      }\n    }\n  },\n  created() {\n    //列表查询\n    this.getData();\n  },\n  methods: {\n    //列表查询\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        this.params = param;\n        // param.sbflArr = param.sbfl === '' ? [] : param.sbflArr\n        const { data, code } = await getList(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n\n          this.tableAndPageInfo.tableData.forEach(item => {\n            this.jxflList.forEach(element => {\n              if (item.jxfl === element.value) {\n                item.jxflName = element.label;\n                return;\n              }\n            });\n          });\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //重置按钮\n    getReset() {\n      this.params = {\n        sbflArr: [],\n        jxflArr: [],\n        jxfl: [],\n        xmbh: \"\",\n        xmmc: \"\"\n      };\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n    },\n    //选中行\n    handleSelectionChange() {},\n    //详情\n    getDetails() {\n      this.title = \"检修项目详情\";\n      this.isDisabled = true;\n      this.isShowDetails = true;\n      this.form = { ...row };\n    },\n    //新增\n    getInster() {\n      this.title = \"检修项目新增\";\n      this.isDisabled = false;\n      this.form = {};\n      this.isShowDetails = true;\n    },\n    //编辑按钮\n    updateRow(row) {\n      this.title = \"检修项目修改\";\n      this.isDisabled = false;\n      this.form = { ...row };\n      this.isShowDetails = true;\n    },\n    //详情按钮\n    getInfo(row) {\n      this.title = \"详情查看\";\n      this.form = { ...row };\n      this.isDisabled = true;\n      this.isShowDetails = true;\n    },\n    async saveRow() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          saveOrUpdate(this.form).then(res => {\n            try {\n              if (res.code === \"0000\") {\n                this.$message.success(\"操作成功\");\n              }\n            } catch (e) {\n              console.log(e);\n            }\n            this.getData();\n          });\n        } else {\n          return false;\n        }\n        this.isShowDetails = false;\n      });\n    },\n    //删除按钮\n    async deleteRow(row) {\n      this.form = { ...row };\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove([this.form.objId]).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n      this.getData();\n    },\n    //filter_change事件\n    handleEvent(val, val1) {\n      //变电站查询下拉框选项根据所选分公司带出来\n      if (val.label === \"sbfl\" && val.value) {\n        this.params.sbflArr = val.value;\n        console.log(\"sbflArr\", this.params);\n      }\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false;\n    },\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n    showDeviceTreeDialog() {\n      this.isFilter = false;\n      this.showDeviceTree = true;\n    },\n    async initDomain() {\n      let { data: jxfl } = await getDictTypeData(\"jxfl\");\n      this.jxflList = jxfl;\n\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.value === \"jxfl\") {\n          item.options = jxfl;\n          return;\n        }\n      });\n    },\n    getDeviceClassGroup() {\n      getDeviceClassGroup([\"bdsb\", \"pdsb\", \"sdsb\"]).then(res => {\n        if (res.code === \"0000\") {\n          this.DevicesListGroup = res.data;\n          this.filterInfo.fieldList.forEach(item => {\n            if (item.value === \"sbfl\") {\n              item.options = res.data;\n              return;\n            }\n          });\n        } else {\n          this.$message.error(\"获取设备分类失败\");\n        }\n      });\n    },\n    inputFocusEvent(val) {\n      if (val.target.name === \"sbfl\") {\n        this.showDeviceTree = true;\n        this.isFilter = true;\n      }\n    },\n    getDeviceTypeData(res) {\n      if (this.isFilter) {\n        this.filterInfo.data.sbflArr = [];\n        this.filterInfo.data.sbfl = \"\";\n        res.forEach(item => {\n          if (item.checked) {\n            this.filterInfo.data.sbflArr.push(item.code);\n            this.filterInfo.data.sbfl += item.name + \",\";\n          }\n        });\n\n        this.filterInfo.data.sbfl = this.filterInfo.data.sbfl.substring(\n          0,\n          this.filterInfo.data.sbfl.length - 1\n        );\n        this.showDeviceTree = false;\n      } else {\n        let treeNodes = [];\n        res.forEach(item => {\n          if (item.checked) {\n            treeNodes.push(item);\n          }\n        });\n        if (treeNodes.length === 1) {\n          this.form.sbflmc = treeNodes[0].name;\n          this.form.sbfl = treeNodes[0].code;\n          this.showDeviceTree = false;\n        } else {\n          this.$message.warning(\"请选择单条设备数据\");\n        }\n      }\n    },\n    closeDeviceTypeDialog() {\n      this.showDeviceTree = false;\n    },\n    //清空表单数据\n    handleClose() {\n      this.form = {};\n      this.$nextTick(() => {\n        this.form = this.$options.data().form;\n        this.resetForm(\"form\");\n      });\n    }\n  }\n};\n</script>\n\n<style scoped>\n.productGroupSelector-group {\n  padding-bottom: 0;\n  padding-top: 32px;\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  width: 400px;\n}\n\n/deep/ .el-select-group__title {\n  font-size: 24px;\n}\n</style>\n"]}]}