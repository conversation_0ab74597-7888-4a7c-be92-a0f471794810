{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\activiti\\processdefinition.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\activiti\\processdefinition.js", "mtime": 1706897313850}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMubGlzdCA9IGxpc3Q7CmV4cG9ydHMuZGVsID0gZGVsOwpleHBvcnRzLnN1c3BlbmRPckFjdGl2ZUFwcGx5ID0gc3VzcGVuZE9yQWN0aXZlQXBwbHk7CmV4cG9ydHMuY29udmVydFRvTW9kZWwgPSBjb252ZXJ0VG9Nb2RlbDsKCnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwoKdmFyIGJhc2VVcmwgPSAiL2FjdGl2aXRpLWFwaSI7Ci8qKgogKiDmn6Xor6LmtYHnqIvlrprkuYnliJfooagKICogQHBhcmFtIHF1ZXJ5IOivt+axguWPguaVsAogKiBAcmV0dXJucyB7UHJvbWlzZTxhbnk+fQogKi8KCmZ1bmN0aW9uIGxpc3QocXVlcnkpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgIi9kZWZpbml0aW9uL2xpc3QiLCBxdWVyeSwgMyk7Cn0KLyoqCiAqIOWIoOmZpOa1geeoi+WumuS5iQogKiBAcGFyYW0gaWQKICogQHJldHVybnMge1Byb21pc2UgfCBQcm9taXNlPGFueT59CiAqLwoKCmZ1bmN0aW9uIGRlbChkZXBsb3ltZW50SWQpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0R2V0KGJhc2VVcmwgKyAiL2RlZmluaXRpb24vZGVsZXRlLyIgKyBkZXBsb3ltZW50SWQsIG51bGwsIDMpOwp9Ci8qKgogKiDmjILotbflkozmv4DmtLvmtYHnqIsKICogQHBhcmFtIGRhdGEKICogQHJldHVybnMge1Byb21pc2UgfCBQcm9taXNlPGFueT59CiAqLwoKCmZ1bmN0aW9uIHN1c3BlbmRPckFjdGl2ZUFwcGx5KGRhdGEpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0R2V0KGJhc2VVcmwgKyAiL2RlZmluaXRpb24vc3VzcGVuZE9yQWN0aXZlQXBwbHkiLCBkYXRhLCAzKTsKfQovKioKICog6L2s5o2i5qih5Z6LCiAqIEBwYXJhbSBtb2RlbElkCiAqIEByZXR1cm5zIHtQcm9taXNlPGFueT59CiAqLwoKCmZ1bmN0aW9uIGNvbnZlcnRUb01vZGVsKGRhdGEpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgIi9kZWZpbml0aW9uL2NvbnZlcnQyTW9kZWwiLCBkYXRhLCAzKTsKfQ=="}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/activiti/processdefinition.js"], "names": ["baseUrl", "list", "query", "api", "requestPost", "del", "deploymentId", "requestGet", "suspendOrActiveApply", "data", "convertToModel"], "mappings": ";;;;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,eAAhB;AACA;;;;;;AAKO,SAASC,IAAT,CAAcC,KAAd,EAAqB;AAC1B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,kBAAxB,EAA2CE,KAA3C,EAAiD,CAAjD,CAAP;AACD;AAGD;;;;;;;AAKO,SAASG,GAAT,CAAaC,YAAb,EAA2B;AAChC,SAAOH,iBAAII,UAAJ,CAAeP,OAAO,GAAC,qBAAR,GAA8BM,YAA7C,EAA0D,IAA1D,EAA+D,CAA/D,CAAP;AACD;AAED;;;;;;;AAKO,SAASE,oBAAT,CAA8BC,IAA9B,EAAoC;AACzC,SAAON,iBAAII,UAAJ,CAAeP,OAAO,GAAC,kCAAvB,EAA0DS,IAA1D,EAA+D,CAA/D,CAAP;AACD;AAED;;;;;;;AAKO,SAASC,cAAT,CAAwBD,IAAxB,EAA8B;AACnC,SAAON,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,2BAAxB,EAAoDS,IAApD,EAAyD,CAAzD,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\nconst baseUrl = \"/activiti-api\";\n/**\n * 查询流程定义列表\n * @param query 请求参数\n * @returns {Promise<any>}\n */\nexport function list(query) {\n  return api.requestPost(baseUrl+\"/definition/list\",query,3)\n}\n\n\n/**\n * 删除流程定义\n * @param id\n * @returns {Promise | Promise<any>}\n */\nexport function del(deploymentId) {\n  return api.requestGet(baseUrl+\"/definition/delete/\"+deploymentId,null,3)\n}\n\n/**\n * 挂起和激活流程\n * @param data\n * @returns {Promise | Promise<any>}\n */\nexport function suspendOrActiveApply(data) {\n  return api.requestGet(baseUrl+\"/definition/suspendOrActiveApply\",data,3)\n}\n\n/**\n * 转换模型\n * @param modelId\n * @returns {Promise<any>}\n */\nexport function convertToModel(data) {\n  return api.requestPost(baseUrl+\"/definition/convert2Model\",data,3)\n}\n"]}]}