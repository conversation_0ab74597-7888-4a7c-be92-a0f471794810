{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\sympk\\sympk.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\sympk\\sympk.js", "mtime": 1706897314370}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagang<PERSON>field/bzgl/sympk/sympk.js"], "names": ["baseUrl", "getDeviceClassTreeNodeByPid", "params", "api", "requestPost", "getPageDataList", "query", "getPageNoDataList", "saveOrUpdate", "remove", "updateTableNum", "getTable", "getById", "JSON", "stringify"], "mappings": ";;;;;;;;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,cAAhB;AAGA;;;;;;AAKO,SAASC,2BAAT,CAAqCC,MAArC,EAA6C;AAClD,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,sCAA1B,EAAkEE,MAAlE,EAA0E,CAA1E,CAAP;AACD;AACD;;;;;;;AAKO,SAASG,eAAT,CAAyBC,KAAzB,EAA+B;AACpC,SAAQH,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,YAAxB,EAAqCM,KAArC,EAA2C,CAA3C,CAAR;AACD;AAED;;;;;;AAIO,SAASC,iBAAT,CAA2BD,KAA3B,EAAiC;AACtC,SAAQH,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,sBAAxB,EAA+CM,KAA/C,EAAqD,CAArD,CAAR;AACD;AAED;;;;;;;AAKO,SAASE,YAAT,CAAsBF,KAAtB,EAA4B;AACjC,SAAQH,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,WAAxB,EAAoCM,KAApC,EAA0C,CAA1C,CAAR;AACD;AAED;;;;;;;AAKO,SAASG,MAAT,CAAgBH,KAAhB,EAAsB;AAC3B,SAAOH,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,cAAxB,EAAuCM,KAAvC,EAA6C,CAA7C,CAAP;AACD;AAED;;;;;;;AAKO,SAASI,cAAT,CAAwBJ,KAAxB,EAA8B;AACnC,SAAOH,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,sBAAxB,EAA+CM,KAA/C,EAAqD,CAArD,CAAP;AACD;AACD;;;;;;;AAKO,SAASK,QAAT,CAAkBL,KAAlB,EAAwB;AAC7B,SAAOH,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,gBAAxB,EAAyCM,KAAzC,EAA+C,CAA/C,CAAP;AACD;AAED;;;;;;;AAKO,SAASM,OAAT,CAAiBN,KAAjB,EAAuB;AAC5B,SAAQH,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,eAAxB,EAAwCa,IAAI,CAACC,SAAL,CAAeR,KAAf,CAAxC,EAA8D,CAA9D,CAAR;AACD", "sourcesContent": ["import api from '@/utils/request'\nconst baseUrl = \"/manager-api\";\n\n\n/**\n * 根据父节点编码获取设备分类树节点数据\n * @param params 父节点编码\n * @returns {Promise | Promise<unknown>}\n */\nexport function getDeviceClassTreeNodeByPid(params) {\n  return api.requestPost(baseUrl + '/sblx/getDeviceClassifyTreeNodeByPid', params, 2)\n}\n/**\n * 查询方法\n * @param query\n * @returns {Promise | Promise<unknown>}\n */\nexport function getPageDataList(query){\n  return  api.requestPost(baseUrl+'/symp/page',query,2);\n}\n\n/**\n * 查询模板未关联的子项目（铭牌）\n * @param {*} query \n */\nexport function getPageNoDataList(query){\n  return  api.requestPost(baseUrl+'/symp/getmbmpAndxmNO',query,2);\n}\n\n/**\n * 添加或修改方法\n * @param query\n * @returns {Promise | Promise<unknown>}\n */\nexport function saveOrUpdate(query){\n  return  api.requestPost(baseUrl+'/symp/add',query,2);\n}\n\n/***\n * 删除\n * @param query\n * @returns {Promise | Promise<unknown>}\n */\nexport function remove(query){\n  return api.requestPost(baseUrl+'/symp/remove',query,2)\n}\n\n/***\n * 更新A B表格行列参数\n * @param query\n * @returns {Promise | Promise<unknown>}\n */\nexport function updateTableNum(query){\n  return api.requestPost(baseUrl+'/symp/updateTableNum',query,2)\n}\n/***\n * 获取A B表格最新数据\n * @param query\n * @returns {Promise | Promise<unknown>}\n */\nexport function getTable(query){\n  return api.requestPost(baseUrl+'/symp/getTable',query,2)\n}\n\n/**\n * 查询方法\n * @param query\n * @returns {Promise | Promise<unknown>}\n */\nexport function getById(query){\n  return  api.requestPost(baseUrl+'/symp/getById',JSON.stringify(query),2);\n}\n"]}]}