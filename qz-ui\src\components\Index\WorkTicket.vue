<template>
  <div :workTSpanNum="workTSpanNum" class="borderCls" :class="wkoTDivClass">
    <div>
      <div class="txtTitle">
        <span class="txtContent">工作票</span>
        <!-- <el-select  v-model="value" class="selectBtn" @change="changeYear">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select> -->
        <el-date-picker type="year" value-format="yyyy" v-model="value" class="selectBtn" @change="changeYear"/>
      </div>
      <div  ref="gdchart" class="tjHeight">
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { mapState } from 'vuex'
import { fontSize} from '@/layout/mixin/publicFun'
import {countGzp} from "@/api/yxgl/gzpgl/gzpgl";
import {getDictTypeData} from '@/api/system/dict/data.js'
import { parseTime } from '@/utils/ruoyi'

export default {
  name: 'WorkTicket',//工单完成情况
  props:{
    workTSpanNum:{
      type:Number,
      default:7,
    },
    wkoTDivClass:'',
  },
  data() {
    return {
      //用于布局动态设置高度
      activeClass:1,
      tjCharts:null,//统计图对象
      //默认值
      noFinNum:[],
      finNum:[],
      contNum:[],
      value: parseTime(new Date(),'{y}'),
      options: [],
    }
  },
  mounted() {
   this.getData(this.value)
   window.addEventListener('resize',()=>{
      this.reloadCharts();
    })
    // this.getYears();//获取年度下拉框
  },
  methods: {
    getYears(){
      getDictTypeData('shouye_nd').then(res=>{
        res.data.forEach(item=>{
          this.options.push({label:item.label,value:item.numvalue})
        })
      })
    },
    changeYear(val){
      if(!val){
        return false;
      }
      this.getData(val)
    },
    getData(year){
      countGzp(year).then(res => {
        this.noFinNum = res.data[2];
        this.finNum = res.data[1];
        this.contNum = res.data[0];
        this.showGdCharts();
      })
    },
    //工单完成情况
    showGdCharts(){
      let bar_dv = this.$refs.gdchart;
      let myChart = echarts.init(bar_dv);
      this.tjCharts = myChart;

      let option;
      option = {
        title: {
          subtext: '单位：个',
          left: '4%'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        legend: {
          itemWidth: fontSize(14),// 设置图例图形的宽
          itemHeight: fontSize(14),
          top:'3%',
          right: '6%',
          textStyle:{
            fontSize:fontSize(16),
          }
        },
        grid: {
          left: '4%',
          right: '4%',
          bottom: '1%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data:['变电一种', '变电二种', '线路一种', '线路二种', '电缆一种', '电缆二种', '配电一种','配电二种','新能源一种','新能源二种'],
          axisLabel:{interval: 0,rotate:30}
        },
        yAxis: {},
        series: [
          {
            type: 'bar' ,
            stack: 'Ad',
            name:'完成数',
            barWidth:fontSize(24),
            itemStyle: {
              color: new echarts.graphic.LinearGradient(
                0, 0, 0, 1,
                [
                  {offset: 0, color: '#00D6B1'},
                  {offset: 1, color: '#00D6B1'}
                ]
              )
            },
            data:this.finNum,
          },
          {
            type: 'bar',
            stack: 'Ad',
            name:'执行数',
            barWidth:fontSize(24),
            itemStyle: {
              color: new echarts.graphic.LinearGradient(
                0, 0, 0, 1,
                [
                  {offset: 0, color: '#76eed2'},
                  {offset: 1, color: '#76eed2'}
                ]
              )
            },
            data:this.noFinNum
          },
          {
            type: 'line' ,
            name:'开票总数',
            barWidth:fontSize(24),
            itemStyle: {
               normal: {label : {show: true},color: '#00C994',}
            },
            data:this.contNum,
          },
        ]
      };
      option && myChart.setOption(option);
    },
    //重新加载eCharts图表
    reloadCharts(){
      this.tjCharts.resize();
    },
  },
  computed: {
    ...mapState(["settings","app"]),
    //工作票完成情况
    workOrder() {
      return this.$store.state.settings.workOrder;
    },
    //菜单伸缩状态
    opened() {
      return this.$store.state.app.sidebar.opened;
    },
  },
  watch:{
    workOrder(newVal){
      if(newVal){
        this.reloadCharts();
      }
    },
    wkoSpanNum(newVal){
      this.reloadCharts();
    },
    opened(newVal) {
      //重新加载统计图
      /*setTimeout(()=>{
        this.tjCharts.resize();
      },200)*/
    }
  }
}
</script>
<style>
.spanTxt{
  background: #fff;
  height: 35px;
  line-height: 35px;
  margin-top: 6px;
  margin-right: 9px;
  padding-left: 12px;
}
.selectBtn{
  font-size: 24px;
}
</style>

