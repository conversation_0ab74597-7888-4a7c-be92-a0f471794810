{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sdqxwh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sdqxwh.vue", "mtime": 1706897323432}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmlsdGVyIik7CgpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZm9yLWVhY2giKTsKCnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5pbmRleC1vZiIpOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmpvaW4iKTsKCnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAiKTsKCnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYyIpOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5zcGxpdCIpOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuZm9yLWVhY2giKTsKCk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwOwoKdmFyIF9vYmplY3RTcHJlYWQyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvb2JqZWN0U3ByZWFkMiIpKTsKCnJlcXVpcmUoInJlZ2VuZXJhdG9yLXJ1bnRpbWUvcnVudGltZSIpOwoKdmFyIF9hc3luY1RvR2VuZXJhdG9yMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovU2hhbW1wb29sL3dvcmsvY29kZS9kZ3l0LzAxXHU0RUUzXHU3ODAxL3F6LXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2FzeW5jVG9HZW5lcmF0b3IiKSk7Cgp2YXIgX3NicXh3aCA9IHJlcXVpcmUoIkAvYXBpL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvc2JxeHdoL3NicXh3aCIpOwoKdmFyIF9kYXRhID0gcmVxdWlyZSgiQC9hcGkvc3lzdGVtL2RpY3QvZGF0YSIpOwoKdmFyIF9lbGVtZW50VWkgPSByZXF1aXJlKCJlbGVtZW50LXVpIik7Cgp2YXIgX3lzYnprID0gcmVxdWlyZSgiQC9hcGkvYnpnbC95c2J6ay95c2J6ayIpOwoKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gewogIG5hbWU6ICdzYmx4d2gnLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBsb2FkOiBmYWxzZSwKICAgICAgYWRkRmx5ajogZmFsc2UsCiAgICAgIC8v5piv5ZCm5paw5aKe5YiG57G75L6d5o2uCiAgICAgIGZpbHRlckluZm86IHsKICAgICAgICBkYXRhOiB7CiAgICAgICAgICBzYmJqOiAnJywKICAgICAgICAgIHNiYnc6ICcnLAogICAgICAgICAgcXhtczogJycsCiAgICAgICAgICBmbHlqOiAnJywKICAgICAgICAgIHF4ZGo6ICcnLAogICAgICAgICAganN5eTogJycKICAgICAgICB9LAogICAgICAgIGZpZWxkTGlzdDogW3sKICAgICAgICAgIGxhYmVsOiAn6K6+5aSH6YOo5Lu2JywKICAgICAgICAgIHR5cGU6ICdpbnB1dCcsCiAgICAgICAgICB2YWx1ZTogJ3NiYmonCiAgICAgICAgfSwgewogICAgICAgICAgbGFiZWw6ICforr7lpIfpg6jkvY0nLAogICAgICAgICAgdHlwZTogJ2lucHV0JywKICAgICAgICAgIHZhbHVlOiAnc2JidycKICAgICAgICB9LCB7CiAgICAgICAgICBsYWJlbDogJ+makOa<PERSON>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"}, {"version": 3, "sources": ["sdqxwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+nBA;;AAeA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,QADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,IAAA,EAAA,KADA;AAEA,MAAA,OAAA,EAAA,KAFA;AAEA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,IAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA,EAFA;AAGA,UAAA,IAAA,EAAA,EAHA;AAIA,UAAA,IAAA,EAAA,EAJA;AAKA,UAAA,IAAA,EAAA,EALA;AAMA,UAAA,IAAA,EAAA;AANA,SADA;AASA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SANA;AATA,OAHA;AAqBA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA;AAZA,OArBA;AA2CA,MAAA,WAAA,EAAA,EA3CA;AA4CA,MAAA,WAAA,EAAA,EA5CA;AA4CA;AACA,MAAA,YAAA,EAAA,EA7CA;AA6CA;AACA,MAAA,YAAA,EAAA,KA9CA;AA+CA,MAAA,UAAA,EAAA,KA/CA;AA+CA;AACA,MAAA,UAAA,EAAA,KAhDA;AAiDA,MAAA,UAAA,EAAA,KAjDA;AAkDA,MAAA,UAAA,EAAA,KAlDA;AAmDA,MAAA,QAAA,EAAA,EAnDA;AAmDA;AACA,MAAA,SAAA,EAAA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,UAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,UAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAVA;AAaA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAbA;AAgBA,QAAA,UAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAhBA;AAmBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAnBA;AAsBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAtBA,OApDA;AA6EA;AACA,MAAA,QAAA,EAAA,EA9EA;AA8EA;AACA,MAAA,SAAA,EAAA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,UAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,UAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAVA;AAaA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAbA;AAgBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAhBA;AAmBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAnBA;AAsBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAtBA,OA/EA;AAwGA;AACA,MAAA,QAAA,EAAA,EAzGA;AAyGA;AACA,MAAA,SAAA,EAAA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,UAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAVA;AAaA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAbA;AAgBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAhBA;AAmBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAnBA;AAsBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAtBA,OA1GA;AAmIA;AACA,MAAA,QAAA,EAAA,EApIA;AAoIA;AACA,MAAA,SAAA,EAAA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAVA;AAaA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAbA;AAgBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAhBA;AAmBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAnBA;AAsBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAtBA,OArIA;AA8JA;AACA,MAAA,QAAA,EAAA,EA/JA;AA+JA;AACA,MAAA,QAAA,EAAA,EAhKA;AAgKA;AACA,MAAA,QAAA,EAAA,EAjKA;AAiKA;AACA,MAAA,QAAA,EAAA,EAlKA;AAkKA;AACA,MAAA,QAAA,EAAA,EAnKA;AAmKA;AACA,MAAA,QAAA,EAAA,EApKA;AAoKA;AACA,MAAA,IAAA,EAAA,GArKA;AAqKA;AACA,MAAA,UAAA,EAAA,EAtKA;AAsKA;AACA,MAAA,QAAA,EAAA,EAvKA;AAuKA;AACA,MAAA,OAAA,EAAA,IAxKA;AAyKA,MAAA,QAAA,EAAA,EAzKA,CAyKA;;AAzKA,KAAA;AA2KA,GA9KA;AA+KA,EAAA,KAAA,EAAA;AACA,IAAA,UADA,sBACA,GADA,EACA;AACA,WAAA,KAAA,CAAA,IAAA,CAAA,MAAA,CAAA,GAAA;AACA;AAHA,GA/KA;AAoLA,EAAA,OApLA,qBAoLA;AACA,SAAA,WAAA,CAAA,IAAA,GAAA,KAAA,IAAA;AACA,SAAA,OAAA;AACA,SAAA,WAAA,GAHA,CAIA;;AACA,SAAA,WAAA,GALA,CAMA;;AACA,SAAA,WAAA,GAPA,CAQA;;AACA,SAAA,WAAA;AACA,GA9LA;AA+LA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,WAFA,yBAEA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,2BAAA,aAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,oBAAA,KAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AAAA,sBAAA,KAAA,EAAA,IAAA,CAAA,KAAA;AAAA,sBAAA,KAAA,EAAA,IAAA,CAAA;AAAA,qBAAA;AACA,mBAFA;AAGA,iBAJA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,KARA;AASA;AACA,IAAA,WAVA,yBAUA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,yBAAA;AAAA,kBAAA,IAAA,EAAA,MAAA,CAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,KAdA;AAeA;AACA,IAAA,WAhBA,uBAgBA,IAhBA,EAgBA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,yBAAA;AAAA,kBAAA,IAAA,EAAA,MAAA,CAAA,IAAA;AAAA,kBAAA,IAAA,EAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,KApBA;AAqBA;AACA,IAAA,WAtBA,uBAsBA,IAtBA,EAsBA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,yBAAA;AAAA,kBAAA,IAAA,EAAA,MAAA,CAAA,IAAA;AAAA,kBAAA,IAAA,EAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,KA1BA;AA2BA;AACA,IAAA,WA5BA,uBA4BA,IA5BA,EA4BA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,yBAAA;AAAA,kBAAA,IAAA,EAAA,MAAA,CAAA,IAAA;AAAA,kBAAA,IAAA,EAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,KAhCA;AAiCA;AACA,IAAA,WAlCA,uBAkCA,IAlCA,EAkCA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,yBAAA;AAAA,kBAAA,IAAA,EAAA,MAAA,CAAA,IAAA;AAAA,kBAAA,IAAA,EAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,KAtCA;AAuCA;AACA,IAAA,WAxCA,yBAwCA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,2BAAA,aAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA,CADA,CAEA;;AACA,kBAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,wBAAA,IAAA,CAAA,KAAA,IAAA,MAAA,EAAA;AACA,sBAAA,IAAA,CAAA,OAAA,GAAA,MAAA,CAAA,QAAA;AACA;AACA,mBAJA;AAKA,iBARA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,KAlDA;AAmDA;AACA,IAAA,SApDA,qBAoDA,GApDA,EAoDA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA,gBAAA,MAAA,CAAA,OAAA,GAAA,mBAAA,OAAA,CAAA;AACA,kBAAA,IAAA,EAAA,IADA;AACA;AACA,kBAAA,IAAA,EAAA,SAFA;AAEA;AACA,kBAAA,OAAA,EAAA,iBAHA;AAGA;AACA,kBAAA,UAAA,EAAA,oBAJA;AAIA;AACA,kBAAA,MAAA,EAAA,QAAA,CAAA,aAAA,CAAA,UAAA;AALA,iBAAA,CAAA;AAOA,gBAAA,MAAA,CAAA,QAAA,mCAAA,GAAA;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA,EAAA,CAVA,CAWA;;AAXA;AAAA,uBAYA,MAAA,CAAA,WAAA,CAAA,GAAA,CAAA,MAAA,CAZA;;AAAA;AAAA;AAAA,uBAaA,MAAA,CAAA,WAAA,CAAA,GAAA,CAAA,UAAA,CAbA;;AAAA;AAAA;AAAA,uBAcA,MAAA,CAAA,WAAA,CAAA,GAAA,CAAA,UAAA,CAdA;;AAAA;AAeA,gBAAA,MAAA,CAAA,YAAA,GAAA,KAAA;AACA,gBAAA,MAAA,CAAA,OAAA,GAAA,KAAA,CAhBA,CAgBA;;AACA,gBAAA,MAAA,CAAA,UAAA,GAAA,IAAA;;AACA,gBAAA,MAAA,CAAA,OAAA,CAAA,KAAA,GAlBA,CAkBA;;;AAlBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmBA,KAvEA;AAwEA;AACA,IAAA,SAzEA,qBAyEA,GAzEA,EAyEA;AAAA;;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,oCAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,OAAA;AACA,WANA,MAMA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAIA;AACA,SAbA;AAcA,OAnBA;AAoBA,KA9FA;AA+FA;AACA,IAAA,OAhGA,mBAgGA,GAhGA,EAgGA;AACA,WAAA,QAAA,mCAAA,GAAA;AACA,WAAA,QAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA,EAAA;AACA,WAAA,YAAA,GAAA,IAAA;AACA,KApGA;AAqGA;AACA,IAAA,OAtGA,mBAsGA,QAtGA,EAsGA;AACA;AACA,WAAA,QAAA,GAAA,EAAA;AACA,WAAA,QAAA,GAAA,EAAA;AACA,WAAA,QAAA,GAAA,EAAA,CAJA,CAKA;;AACA,UAAA,IAAA,GAAA,KAAA,WAAA,CAAA,MAAA,GAAA,KAAA,WAAA,CAAA,MAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,KAAA,WAAA,CAAA,UAAA,GAAA,KAAA,WAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,KAAA,WAAA,CAAA,UAAA,GAAA,KAAA,WAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,YAAA,GAAA,KAAA;;AACA,cAAA,QAAA;AACA,aAAA,MAAA;AAAA;AACA,eAAA,QAAA,GAAA,EAAA,CADA,CAEA;AACA;AACA;;AACA,eAAA,UAAA,GAAA,IAAA;AACA;;AACA,aAAA,MAAA;AAAA;AACA,eAAA,QAAA,GAAA,EAAA,CADA,CAEA;AACA;AACA;;AACA,eAAA,UAAA,GAAA,IAAA;AACA;;AACA,aAAA,MAAA;AAAA;AACA,eAAA,QAAA,GAAA,EAAA,CADA,CAEA;AACA;AACA;;AACA,eAAA,UAAA,GAAA,IAAA;AACA;;AACA,aAAA,MAAA;AAAA;AACA,eAAA,QAAA,GAAA,EAAA,CADA,CAEA;AACA;AACA;;AACA,eAAA,OAAA,GAAA,IAAA;AACA,eAAA,UAAA,GAAA,IAAA;AACA;;AACA;AACA;AA/BA;AAiCA,KAjJA;AAkJA;AACA,IAAA,QAnJA,oBAmJA,QAnJA,EAmJA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,OAAA,CAAA,KAAA,CAAA,QAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,sBAAA,KAAA,EAAA;AACA,wBAAA,QAAA,mCAAA;AAAA,sBAAA,IAAA,EAAA,OAAA,CAAA;AAAA,qBAAA,CAAA;;AACA,4BAAA,QAAA;AACA,2BAAA,UAAA;AAAA;AACA,wBAAA,QAAA,+DAAA,QAAA,GAAA,OAAA,CAAA,QAAA,CAAA;;AACA,wBAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,8BAAA,IAAA,CAAA,KAAA,KAAA,QAAA,CAAA,UAAA,EAAA;AACA,4BAAA,QAAA,CAAA,IAAA,GAAA,IAAA,CAAA,KAAA;AACA;AACA,yBAJA;;AAKA,4BAAA,OAAA,CAAA,OAAA,EAAA;AAAA;AACA,+CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gCAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,8BAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,8BAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,8BAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,8BAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,8BAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,8BAAA,OAAA,CAAA,YAAA,GAAA,KAAA;;AACA,8BAAA,OAAA,CAAA,OAAA,GAPA,CAQA;;AACA,6BATA,MASA;AACA,8BAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,2BAbA;AAcA,yBAfA,MAeA;AACA,kDAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AAAA;AACA,gCAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,8BAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,8BAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,8BAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,8BAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,8BAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,8BAAA,OAAA,CAAA,YAAA,GAAA,KAAA;;AACA,8BAAA,OAAA,CAAA,OAAA,GAPA,CAQA;;AACA,6BATA,MASA;AACA,8BAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,2BAbA;AAcA;;AACA;;AACA,2BAAA,UAAA;AAAA;AACA,wBAAA,QAAA,+DAAA,QAAA,GAAA,OAAA,CAAA,QAAA,CAAA;;AACA,wBAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,8BAAA,IAAA,CAAA,KAAA,KAAA,QAAA,CAAA,UAAA,EAAA;AACA,4BAAA,QAAA,CAAA,IAAA,GAAA,IAAA,CAAA,KAAA;AACA;AACA,yBAJA;;AAKA,6CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,8BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,4BAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,4BAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,OAAA,CAAA,YAAA,GAAA,KAAA;;AACA,4BAAA,OAAA,CAAA,OAAA,GAPA,CAQA;;AACA,2BATA,MASA;AACA,4BAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,yBAbA;AAcA;;AACA,2BAAA,UAAA;AAAA;AACA,wBAAA,QAAA,+DAAA,QAAA,GAAA,OAAA,CAAA,QAAA,CAAA;;AACA,wBAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,8BAAA,IAAA,CAAA,KAAA,KAAA,QAAA,CAAA,UAAA,EAAA;AACA,4BAAA,QAAA,CAAA,IAAA,GAAA,IAAA,CAAA,KAAA;AACA;AACA,yBAJA;;AAKA,6CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,8BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,4BAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,4BAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,OAAA,CAAA,YAAA,GAAA,KAAA;;AACA,4BAAA,OAAA,CAAA,OAAA,GAPA,CAQA;;AACA,2BATA,MASA;AACA,4BAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,yBAbA;AAcA;;AACA,2BAAA,UAAA;AAAA;AACA,wBAAA,QAAA,+DAAA,QAAA,GAAA,OAAA,CAAA,QAAA,CAAA;;AACA,wBAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,8BAAA,IAAA,CAAA,KAAA,KAAA,QAAA,CAAA,MAAA,EAAA;AACA,4BAAA,QAAA,CAAA,IAAA,GAAA,IAAA,CAAA,KAAA;AACA;AACA,yBAJA;;AAKA,wBAAA,QAAA,CAAA,IAAA,GAAA,QAAA,CAAA,IAAA,GAAA,QAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,GAAA,QAAA,CAAA,IAAA;AACA,6CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,8BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,4BAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,4BAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,OAAA,CAAA,YAAA,GAAA,KAAA;;AACA,4BAAA,OAAA,CAAA,OAAA,GAPA,CAQA;;AACA,2BATA,MASA;AACA,4BAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,yBAbA;AAcA;;AACA;AACA;AA5GA;AA8GA,mBAhHA,MAgHA;AACA,oBAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;;AACA,2BAAA,KAAA;AACA;AACA,iBArHA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuHA,KA1QA;AA2QA;AACA,IAAA,aA5QA,yBA4QA,GA5QA,EA4QA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,cAAA,CAAA,MAAA;;AADA;AAAA,uBAEA,OAAA,CAAA,WAAA,CAAA,GAAA,CAFA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,KA/QA;AAgRA;AACA,IAAA,aAjRA,yBAiRA,GAjRA,EAiRA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,cAAA,CAAA,MAAA;;AADA;AAAA,uBAEA,OAAA,CAAA,OAAA,CAAA,GAAA,CAFA;;AAAA;AAAA;AAAA,uBAGA,OAAA,CAAA,WAAA,CAAA,GAAA,CAHA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,KArRA;AAsRA,IAAA,OAtRA,mBAsRA,GAtRA,EAsRA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,2BAAA;AAAA,kBAAA,IAAA,EAAA,OAAA,CAAA,IAAA;AAAA,kBAAA,KAAA,EAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,sBAAA,GAAA,CAAA,IAAA,CAAA,CAAA,KAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,IAAA,EAAA;AACA,wBAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,GAAA,CAAA;;AACA,oBAAA,OAAA,CAAA,IAAA,CAAA,OAAA,CAAA,QAAA,EAAA,MAAA,EAAA,IAAA;;AACA,oBAAA,OAAA,CAAA,IAAA,CAAA,OAAA,CAAA,QAAA,EAAA,MAAA,EAAA,IAAA;;AACA,oBAAA,OAAA,CAAA,IAAA,CAAA,OAAA,CAAA,QAAA,EAAA,MAAA,EAAA,IAAA;AACA;AACA,iBAPA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,KA/RA;AAgSA;AACA,IAAA,aAjSA,yBAiSA,GAjSA,EAiSA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,cAAA,CAAA,MAAA;;AADA;AAAA,uBAEA,OAAA,CAAA,WAAA,CAAA,GAAA,CAFA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,KApSA;AAqSA;AACA,IAAA,aAtSA,yBAsSA,GAtSA,EAsSA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,cAAA,CAAA,MAAA;;AADA;AAAA,uBAEA,OAAA,CAAA,WAAA,CAAA,GAAA,CAFA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,KAzSA;AA0SA;AACA,IAAA,cA3SA,0BA2SA,IA3SA,EA2SA;AACA,cAAA,IAAA;AACA,aAAA,MAAA;AAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,MAAA,EAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,YAAA,EAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,YAAA,EAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,YAAA,EAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,MAAA,EAAA,EAAA;AACA,eAAA,cAAA,CAAA,MAAA;AACA;;AACA,aAAA,MAAA;AAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,MAAA,EAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,YAAA,EAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,YAAA,EAAA,EAAA;AACA,eAAA,cAAA,CAAA,MAAA;AACA;;AACA,aAAA,MAAA;AAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,MAAA,EAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,YAAA,EAAA,EAAA;AACA,eAAA,cAAA,CAAA,MAAA;AACA;;AACA,aAAA,MAAA;AAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,MAAA,EAAA,EAAA;AACA;;AACA;AACA;AAxBA;AA0BA,KAtUA;AAuUA;AACA,IAAA,QAxUA,oBAwUA,IAxUA,EAwUA;AACA,WAAA,YAAA,GAAA,KAAA;;AACA,cAAA,IAAA;AACA,aAAA,MAAA;AACA,eAAA,UAAA,GAAA,KAAA;AACA;;AACA,aAAA,MAAA;AACA,eAAA,UAAA,GAAA,KAAA;AACA;;AACA,aAAA,MAAA;AACA,eAAA,UAAA,GAAA,KAAA;AACA;;AACA,aAAA,MAAA;AACA,eAAA,UAAA,GAAA,KAAA;AACA;;AACA,aAAA,MAAA;AACA,eAAA,YAAA,GAAA,KAAA;AACA;;AACA;AACA,eAAA,UAAA,GAAA,KAAA;AACA,eAAA,UAAA,GAAA,KAAA;AACA,eAAA,UAAA,GAAA,KAAA;AACA,eAAA,UAAA,GAAA,KAAA;AACA,eAAA,YAAA,GAAA,KAAA;AACA;AAtBA;AAwBA,KAlWA;AAmWA;AACA,IAAA,WApWA,yBAoWA;AACA,WAAA,WAAA,GAAA;AAAA,QAAA,IAAA,EAAA,KAAA;AAAA,OAAA,CADA,CACA;AACA,KAtWA;AAwWA;AACA,IAAA,UAzWA,sBAyWA,KAzWA,EAyWA,IAzWA,EAyWA;AACA,UAAA,CAAA,KAAA,EAAA,OAAA,IAAA;AACA,aAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,KAAA,MAAA,CAAA,CAAA;AACA,KA5WA;AA6WA,IAAA,WA7WA,yBA6WA;AAAA;;AACA,+BAAA;AAAA,QAAA,IAAA,EAAA,KAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KAjXA;AAkXA;AACA,IAAA,eAnXA,2BAmXA,IAnXA,EAmXA;AACA,WAAA,YAAA,GAAA,IAAA;;AACA,UAAA,IAAA,CAAA,UAAA,KAAA,GAAA,EAAA;AAAA;AACA,aAAA,WAAA,CAAA,IAAA,GAAA,IAAA,CAAA,EAAA;AACA,aAAA,WAAA,CAAA,MAAA,GAAA,EAAA;AACA,aAAA,WAAA,CAAA,UAAA,GAAA,EAAA;AACA,aAAA,WAAA,CAAA,UAAA,GAAA,EAAA;AACA,OALA,MAKA,IAAA,IAAA,CAAA,UAAA,KAAA,GAAA,EAAA;AAAA;AACA,aAAA,WAAA,CAAA,MAAA,GAAA,IAAA,CAAA,EAAA;AACA,aAAA,WAAA,CAAA,UAAA,GAAA,EAAA;AACA,aAAA,WAAA,CAAA,UAAA,GAAA,EAAA;AACA,OAJA,MAIA,IAAA,IAAA,CAAA,UAAA,KAAA,GAAA,EAAA;AAAA;AACA,aAAA,WAAA,CAAA,MAAA,GAAA,EAAA;AACA,aAAA,WAAA,CAAA,UAAA,GAAA,IAAA,CAAA,EAAA;AACA,aAAA,WAAA,CAAA,UAAA,GAAA,EAAA;AACA,OAJA,MAIA,IAAA,IAAA,CAAA,UAAA,KAAA,GAAA,EAAA;AAAA;AACA,aAAA,WAAA,CAAA,MAAA,GAAA,EAAA;AACA,aAAA,WAAA,CAAA,UAAA,GAAA,EAAA;AACA,aAAA,WAAA,CAAA,UAAA,GAAA,IAAA,CAAA,EAAA;AACA,OAJA,MAIA;AACA,aAAA,WAAA,GAAA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,SAAA;AACA;;AACA,WAAA,OAAA;AACA,KA1YA;AA2YA;AACA,IAAA,OA5YA,mBA4YA,MA5YA,EA4YA;AAAA;;AACA,WAAA,IAAA,GAAA,IAAA;AACA,WAAA,WAAA,+DAAA,KAAA,WAAA,GAAA,MAAA;AACA,6BAAA,KAAA,WAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,OAAA,CAAA,IAAA,GAAA,KAAA;AACA,OAJA;AAKA,KApZA;AAqZA;AACA,IAAA,WAtZA,yBAsZA;AACA;AACA;AACA;AACA;AACA,UAAA,QAAA,GAAA,OAAA;AACA,UAAA,SAAA,GAAA,WAAA;AACA,8BAAA,SAAA,EAAA,KAAA,WAAA,EAAA,QAAA;AACA;AA9ZA;AA/LA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\" id=\"sbqxDiv\">\n    <!--左侧树组件-->\n    <el-row :gutter=\"1\">\n      <!--   左侧树   -->\n      <el-col :span=\"4\">\n        <el-card style=\"background:#e0f8ed;padding-top:10px;\" shadow=\"never\">\n          <div>\n            <el-col>\n              <el-form label-width=\"62px\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"快速查询:\" label-width=\"80px\">\n                    <el-input\n                      placeholder=\"输入关键字过滤\"\n                      v-model=\"filterText\"\n                      clearable\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-form>\n            </el-col>\n          </div>\n          <div class=\"text head-container\">\n            <el-col style=\"padding:0;\">\n              <el-tree\n                id=\"tree\"\n                :data=\"treeOptions\"\n                @node-click=\"handleNodeClick\"\n                :highlight-current=\"true\"\n                ref=\"tree\"\n                :filter-node-method=\"filterNode\"\n                node-key=\"id\"\n                :default-checked-keys=\"['0']\"\n                :default-expanded-keys=\"['0']\"\n                accordion\n              />\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :btnHidden=\"false\"\n          :width=\"{ labelWidth: 80, itemWidth: 165 }\"\n          @handleReset=\"filterReset\"\n        />\n        <el-white class=\"button-group1\">\n          <div class=\"button_btn\">\n            <el-button type=\"primary\" icon=\"el-icon-plus\" v-hasPermi=\"['sdqxwh:button:add']\" @click=\"addForm('sbbj')\">新增部件</el-button>\n            <el-button type=\"primary\" icon=\"el-icon-plus\" v-hasPermi=\"['sdqxwh:button:add']\" @click=\"addForm('sbbw')\">新增部位</el-button>\n            <el-button type=\"primary\" icon=\"el-icon-plus\" v-hasPermi=\"['sdqxwh:button:add']\" @click=\"addForm('qxms')\">新增隐患描述</el-button>\n            <el-button type=\"primary\" icon=\"el-icon-plus\" v-hasPermi=\"['sdqxwh:button:add']\" @click=\"addForm('flyj')\">新增分类依据</el-button>\n            <el-button type=\"success\" icon=\"el-icon-download\" @click=\"exportExcel\">导出</el-button>\n          </div>\n          <comp-table\n            v-loading=\"load\"\n            :table-and-page-info=\"tableAndPageInfo\"\n            height=\"62.2vh\"\n          >\n            <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\"\n                             width=\"200\" :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button type=\"text\" size=\"small\"\n                           @click=\"updateRow(scope.row)\" v-hasPermi=\"['sdqxwh:button:update']\" title=\"修改\"  class='el-icon-edit'\n                >\n                </el-button>\n                <el-button type=\"text\" size=\"small\"\n                           @click=\"deleteRow(scope.row)\" v-hasPermi=\"['sdqxwh:button:delete']\" title=\"删除\"  class='el-icon-delete'\n                >\n                </el-button>\n                <el-button type=\"text\" size=\"small\"\n                           @click=\"viewFun(scope.row)\" title=\"查看\" class='el-icon-view'\n                >\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!--  新增设备部件  -->\n    <el-dialog title=\"新增设备部件\" :visible.sync=\"isShowSbbj\" width=\"58%\" @close=\"closeFun('sbbj')\" v-dialogDrag>\n      <el-form label-width=\"140px\" :rules=\"sbbjRules\" :model=\"sbbjForm\" ref=\"sbbjForm\">\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备隐患</span>\n          </div>\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"8\">\n              <el-form-item prop=\"dydj\" label=\"电压等级\">\n                <el-select placeholder=\"电压等级\" v-model=\"sbbjForm.dydj\" style=\"width:100%\"\n                           filterable clearable multiple\n                >\n                  <el-option\n                    v-for=\"item in dydjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item prop=\"sblxbm\" label=\"设备类型\">\n                <el-select placeholder=\"设备类型\" v-model=\"sbbjForm.sblxbm\" style=\"width:80%\"\n                           @change=\"sblxChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in sblxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item prop=\"sbbj\" label=\"设备部件\">\n                <el-input v-model=\"sbbjForm.sbbj\" placeholder=\"请输入设备部件\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sbbw\" label=\"设备部位\">\n                <el-input v-model=\"sbbjForm.sbbw\" placeholder=\"请输入设备部位\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\n                <el-select placeholder=\"隐患等级\" v-model=\"sbbjForm.qxdj\" style=\"width:80%\"\n                           filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in qxdjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"隐患描述:\" prop=\"qxms\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"sbbjForm.qxms\" placeholder=\"请输入隐患描述\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"sbbjForm.flyj\" placeholder=\"请输入分类依据\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"sbbjForm.jsyy\" placeholder=\"请输入技术原因\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun('sbbj')\" size=\"small\">取 消</el-button>\n        <el-button type=\"primary\" size=\"small\" @click=\"saveForm('sbbjForm')\">保存</el-button>\n      </div>\n    </el-dialog>\n\n    <!--  新增设备部位  -->\n    <el-dialog title=\"新增设备部位\" :visible.sync=\"isShowSbbw\" width=\"58%\" @close=\"closeFun('sbbw')\" v-dialogDrag>\n      <el-form label-width=\"140px\" :rules=\"sbbwRules\" :model=\"sbbwForm\" ref=\"sbbwForm\">\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备隐患</span>\n          </div>\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"8\">\n              <el-form-item prop=\"dydj\" label=\"电压等级\">\n                <el-select placeholder=\"电压等级\" v-model=\"sbbwForm.dydj\" style=\"width:100%\"\n                           filterable clearable multiple\n                           :disabled=\"true\"\n                >\n                  <el-option\n                    v-for=\"item in dydjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item prop=\"sblxbm\" label=\"设备类型\">\n                <el-select placeholder=\"设备类型\" v-model=\"sbbwForm.sblxbm\" style=\"width:80%\"\n                           @change=\"sblxChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in sblxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item prop=\"parentSbbj\" label=\"设备部件\">\n                <el-select placeholder=\"设备部件\" v-model=\"sbbwForm.parentSbbj\" style=\"width:80%\"\n                           @change=\"sbbjChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in sbbjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sbbw\" label=\"设备部位\">\n                <el-input v-model=\"sbbwForm.sbbw\" placeholder=\"请输入设备部位\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\n                <el-select placeholder=\"隐患等级\" v-model=\"sbbwForm.qxdj\" style=\"width:80%\"\n                           filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in qxdjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"隐患描述:\" prop=\"qxms\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"sbbwForm.qxms\" placeholder=\"请输入隐患描述\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"sbbwForm.flyj\" placeholder=\"请输入分类依据\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"sbbwForm.jsyy\" placeholder=\"请输入技术原因\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun('sbbw')\" size=\"small\">取 消</el-button>\n        <el-button type=\"primary\" size=\"small\" @click=\"saveForm('sbbwForm')\">保存</el-button>\n      </div>\n    </el-dialog>\n\n    <!--  新增隐患描述  -->\n    <el-dialog title=\"新增隐患描述\" :visible.sync=\"isShowQxms\" width=\"58%\" @close=\"closeFun('qxms')\" v-dialogDrag>\n      <el-form label-width=\"140px\" :rules=\"qxmsRules\" :model=\"qxmsForm\" ref=\"qxmsForm\">\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备隐患</span>\n          </div>\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"8\">\n              <el-form-item prop=\"dydj\" label=\"电压等级\">\n                <el-select placeholder=\"电压等级\" v-model=\"qxmsForm.dydj\" style=\"width:100%\"\n                           filterable clearable multiple\n                           :disabled=\"true\"\n                >\n                  <el-option\n                    v-for=\"item in dydjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item prop=\"sblxbm\" label=\"设备类型\">\n                <el-select placeholder=\"设备类型\" v-model=\"qxmsForm.sblxbm\" style=\"width:80%\"\n                           @change=\"sblxChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in sblxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item prop=\"parentSbbj\" label=\"设备部件\">\n                <el-select placeholder=\"设备部件\" v-model=\"qxmsForm.parentSbbj\" style=\"width:80%\"\n                           @change=\"sbbjChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in sbbjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"parentSbbw\" label=\"设备部位\">\n                <el-select placeholder=\"设备部位\" v-model=\"qxmsForm.parentSbbw\" style=\"width:80%\"\n                           @change=\"sbbwChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in sbbwList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\n                <el-select placeholder=\"隐患等级\" v-model=\"qxmsForm.qxdj\" style=\"width:80%\"\n                           filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in qxdjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"隐患描述:\" prop=\"qxms\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"qxmsForm.qxms\" placeholder=\"请输入隐患描述\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"qxmsForm.flyj\" placeholder=\"请输入分类依据\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"qxmsForm.jsyy\" placeholder=\"请输入技术原因\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun('qxms')\" size=\"small\">取 消</el-button>\n        <el-button type=\"primary\" size=\"small\" @click=\"saveForm('qxmsForm')\">保存</el-button>\n      </div>\n    </el-dialog>\n\n    <!--  新增分类依据  -->\n    <el-dialog title=\"新增分类依据\" :visible.sync=\"isShowFlyj\" width=\"58%\" @close=\"closeFun('flyj')\" v-dialogDrag>\n      <el-form label-width=\"140px\" :rules=\"flyjRules\" :model=\"flyjForm\" ref=\"flyjForm\">\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备隐患</span>\n          </div>\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"8\">\n              <el-form-item prop=\"dydj\" label=\"电压等级\">\n                <el-select placeholder=\"电压等级\" v-model=\"flyjForm.dydj\" style=\"width:100%\"\n                           filterable clearable multiple\n                           :disabled=\"true\"\n                >\n                  <el-option\n                    v-for=\"item in dydjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item prop=\"sblxbm\" label=\"设备类型\">\n                <el-select placeholder=\"设备类型\" v-model=\"flyjForm.sblxbm\" style=\"width:80%\"\n                           @change=\"sblxChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in sblxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item prop=\"parentSbbj\" label=\"设备部件\">\n                <el-select placeholder=\"设备部件\" v-model=\"flyjForm.parentSbbj\" style=\"width:80%\"\n                           @change=\"sbbjChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in sbbjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"parentSbbw\" label=\"设备部位\">\n                <el-select placeholder=\"设备部位\" v-model=\"flyjForm.parentSbbw\" style=\"width:80%\"\n                           @change=\"sbbwChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in sbbwList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\n                <el-select placeholder=\"隐患等级\" v-model=\"flyjForm.qxdj\" style=\"width:80%\"\n                           filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in qxdjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"隐患描述:\" prop=\"parentQxms\">\n                <el-select placeholder=\"隐患描述\" v-model=\"flyjForm.parentQxms\" style=\"width:80%\"\n                           @change=\"qxmsChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in qxmsList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"flyjForm.flyj\" placeholder=\"请输入分类依据\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"flyjForm.jsyy\" placeholder=\"请输入技术原因\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun('flyj')\" size=\"small\">取 消</el-button>\n        <el-button v-if=\"addFlyj\" type=\"primary\" size=\"small\" @click=\"saveForm('flyjForm')\">保存</el-button>\n        <el-button v-if=\"!addFlyj\" type=\"primary\" size=\"small\" @click=\"saveForm('flyjForm')\">保存</el-button>\n      </div>\n    </el-dialog>\n\n    <!--  设备隐患查看  -->\n    <el-dialog title=\"设备隐患查看\" :visible.sync=\"isShowDetail\" width=\"58%\" @close=\"closeFun('view')\" v-dialogDrag>\n      <el-form label-width=\"140px\" :model=\"viewForm\" ref=\"viewForm\">\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备隐患</span>\n          </div>\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"8\">\n              <el-form-item prop=\"dydj\" label=\"电压等级\">\n                <el-select placeholder=\"电压等级\" v-model=\"viewForm.dydj\" style=\"width:100%\"\n                           filterable clearable multiple\n                           :disabled=\"true\"\n                >\n                  <el-option\n                    v-for=\"item in dydjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item prop=\"sblx\" label=\"设备类型\">\n                <el-input v-model=\"viewForm.sblx\" placeholder=\"请输入设备类型\" style=\"width: 92%\"\n                          :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item prop=\"sbbj\" label=\"设备部件\">\n                <el-input v-model=\"viewForm.sbbj\" placeholder=\"请输入设备部件\" style=\"width: 92%\"\n                          :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sbbw\" label=\"设备部位\">\n                <el-input v-model=\"viewForm.sbbw\" placeholder=\"请输入设备部位\" style=\"width: 92%\"\n                          :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\n                <el-input v-model=\"viewForm.qxdj\" placeholder=\"请输入隐患等级\" style=\"width: 92%\"\n                          :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"隐患描述:\" prop=\"qxms\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"viewForm.qxms\" placeholder=\"请输入隐患描述\" style=\"width: 92%\"\n                          :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"viewForm.flyj\" placeholder=\"请输入分类依据\" style=\"width: 92%\"\n                          :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"viewForm.jsyy\" placeholder=\"请输入技术原因\" style=\"width: 92%\"\n                          :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n    </el-dialog>\n  </div>\n\n</template>\n\n<script>\nimport {getQxList,\n  getQxsbTree,\n  getSblxList,\n  getSbbjList,\n  getSbbwList,\n  getQxmsList,\n  getFlyjList,\n  addFlyj,\n  updateFlyj,\n  deleteFlyjById,\n  addQxms,\n  addSbbw,\n  addSbbj,\n  getDydjByBjId\n} from '@/api/dagangOilfield/bzgl/sbqxwh/sbqxwh'\nimport {getDictTypeData} from '@/api/system/dict/data'\nimport { Loading } from 'element-ui'\nimport {exportExcel} from '@/api/bzgl/ysbzk/ysbzk'\n\nexport default {\n  name: 'sblxwh',\n  data() {\n    return {\n      load:false,\n      addFlyj:false,//是否新增分类依据\n      filterInfo: {\n        data: {\n          sbbj: '',\n          sbbw: '',\n          qxms: '',\n          flyj: '',\n          qxdj: '',\n          jsyy: '',\n        },\n        fieldList: [\n          { label: '设备部件', type: 'input', value: 'sbbj' },\n          { label: '设备部位', type: 'input', value: 'sbbw'},\n          { label: '隐患描述', type: 'input', value: 'qxms'},\n          { label: '隐患等级', type: 'select', value: 'qxdj', options: []},\n          { label: '分类依据', type: 'input', value: 'flyj'},\n          { label: '技术原因', type: 'input', value: 'jsyy'}\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: 'sblx', label: '设备类型', minWidth: '140' },\n          { prop: 'sbbj', label: '设备部件', minWidth: '180' },\n          { prop: 'sbbw', label: '设备部位', minWidth: '130' },\n          { prop: 'qxms', label: '隐患描述', minWidth: '180',showPop:true},\n          { prop: 'flyj', label: '分类依据', minWidth: '220',showPop:true},\n          { prop: 'qxdj', label: '隐患等级', minWidth: '80'},\n          { prop: 'jsyy', label: '技术原因', minWidth: '120' }\n        ]\n      },\n      queryParams:{},\n      treeOptions: [], //组织树\n      treeNodeData:{},//点击后的树节点数据\n      isShowDetail:false,\n      isShowSbbj:false,//新增弹框\n      isShowSbbw:false,\n      isShowQxms:false,\n      isShowFlyj:false,\n      flyjForm:{},//表单\n      flyjRules:{\n        dydj: [\n          { required: true, message: '电压等级不能为空', trigger: 'select' }\n        ],\n        sblxbm: [\n          { required: true, message: '设备类型不能为空', trigger: 'select' }\n        ],\n        parentSbbj: [\n          { required: true, message: '设备部件不能为空', trigger: 'select' }\n        ],\n        parentSbbw: [\n          { required: true, message: '设备部位不能为空', trigger: 'select' }\n        ],\n        qxdj: [\n          { required: true, message: '隐患等级不能为空', trigger: 'select' }\n        ],\n        parentQxms: [\n          { required: true, message: '隐患描述不能为空', trigger: 'select' }\n        ],\n        flyj: [\n          { required: true, message: '分类依据不能为空', trigger: 'blur' }\n        ],\n        jsyy: [\n          { required: true, message: '技术原因不能为空', trigger: 'blur' }\n        ],\n      },//校验规则\n      qxmsForm:{},//表单\n      qxmsRules:{\n        dydj: [\n          { required: true, message: '电压等级不能为空', trigger: 'select' }\n        ],\n        sblxbm: [\n          { required: true, message: '设备类型不能为空', trigger: 'select' }\n        ],\n        parentSbbj: [\n          { required: true, message: '设备部件不能为空', trigger: 'select' }\n        ],\n        parentSbbw: [\n          { required: true, message: '设备部位不能为空', trigger: 'select' }\n        ],\n        qxdj: [\n          { required: true, message: '隐患等级不能为空', trigger: 'select' }\n        ],\n        qxms: [\n          { required: true, message: '隐患描述不能为空', trigger: 'blur' }\n        ],\n        flyj: [\n          { required: true, message: '分类依据不能为空', trigger: 'blur' }\n        ],\n        jsyy: [\n          { required: true, message: '技术原因不能为空', trigger: 'blur' }\n        ],\n      },//校验规则\n      sbbwForm:{},//表单\n      sbbwRules:{\n        dydj: [\n          { required: true, message: '电压等级不能为空', trigger: 'select' }\n        ],\n        sblxbm: [\n          { required: true, message: '设备类型不能为空', trigger: 'select' }\n        ],\n        parentSbbj: [\n          { required: true, message: '设备部件不能为空', trigger: 'select' }\n        ],\n        sbbw: [\n          { required: true, message: '设备部位不能为空', trigger: 'blur' }\n        ],\n        qxdj: [\n          { required: true, message: '隐患等级不能为空', trigger: 'select' }\n        ],\n        qxms: [\n          { required: true, message: '隐患描述不能为空', trigger: 'blur' }\n        ],\n        flyj: [\n          { required: true, message: '分类依据不能为空', trigger: 'blur' }\n        ],\n        jsyy: [\n          { required: true, message: '技术原因不能为空', trigger: 'blur' }\n        ],\n      },//校验规则\n      sbbjForm:{},//表单\n      sbbjRules:{\n        dydj: [\n          { required: true, message: '电压等级不能为空', trigger: 'select' }\n        ],\n        sblxbm: [\n          { required: true, message: '设备类型不能为空', trigger: 'select' }\n        ],\n        sbbj: [\n          { required: true, message: '设备部件不能为空', trigger: 'blur' }\n        ],\n        sbbw: [\n          { required: true, message: '设备部位不能为空', trigger: 'blur' }\n        ],\n        qxdj: [\n          { required: true, message: '隐患等级不能为空', trigger: 'select' }\n        ],\n        qxms: [\n          { required: true, message: '隐患描述不能为空', trigger: 'blur' }\n        ],\n        flyj: [\n          { required: true, message: '分类依据不能为空', trigger: 'blur' }\n        ],\n        jsyy: [\n          { required: true, message: '技术原因不能为空', trigger: 'blur' }\n        ],\n      },//校验规则\n      sblxList:[],//设备类型下拉框选项\n      sbbjList:[],//设备部件下拉框选项\n      sbbwList:[],//设备部位下拉框选项\n      qxmsList:[],//隐患描述下拉框选项\n      flyjList:[],//分类依据下拉框选项\n      qxdjList:[],//隐患等级下拉框选项\n      qxlb:'3',//隐患类别（输电）\n      filterText:'',//过滤\n      viewForm:{},//查看表单\n      loading: null,\n      dydjList:[],//电压等级下拉框\n    }\n  },\n  watch: {\n    filterText(val) {\n      this.$refs.tree.filter(val);\n    }\n  },\n  created() {\n    this.queryParams.qxlb = this.qxlb;\n    this.getData();\n    this.getTreeData();\n    //设备类型下拉框\n    this.getSblxList();\n    //隐患等级下拉框\n    this.getQxdjList();\n    //获取电压等级下拉框\n    this.getDydjList();\n  },\n  methods: {\n    //获取电压等级下拉框\n    async getDydjList(){\n      await getDictTypeData('xlsbqx_dydj').then(res=>{\n        res.data.forEach(item=>{\n          this.dydjList.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    //获取设备类型下拉框\n    async getSblxList(){\n      await getSblxList({qxlb:this.qxlb}).then(res=>{\n        this.sblxList = res.data;\n      })\n    },\n    //获取设备部件下拉框\n    async getSbbjList(sblx){\n      await getSbbjList({qxlb:this.qxlb,sblx:sblx}).then(res=>{\n        this.sbbjList = res.data;\n      })\n    },\n    //获取设备部位下拉框\n    async getSbbwList(sbbj){\n      await getSbbwList({qxlb:this.qxlb,sbbj:sbbj}).then(res=>{\n        this.sbbwList = res.data;\n      })\n    },\n    //获取隐患描述下拉框\n    async getQxmsList(sbbw){\n      await getQxmsList({qxlb:this.qxlb,sbbw:sbbw}).then(res=>{\n        this.qxmsList = res.data;\n      })\n    },\n    //获取分类依据下拉框\n    async getFlyjList(qxms){\n      await getFlyjList({qxlb:this.qxlb,qxms:qxms}).then(res=>{\n        this.flyjList = res.data;\n      })\n    },\n    //获取隐患等级字典数据\n    async getQxdjList(){//查询隐患等级字典\n      await getDictTypeData('sbqxwh_qxdj').then(res=>{\n        this.qxdjList = res.data;\n        //给筛选条件赋值\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == 'qxdj') {\n            item.options = this.qxdjList\n          }\n        })\n      })\n    },\n    //编辑\n    async updateRow(row){\n      //开启遮罩层\n      this.loading = Loading.service({\n        lock: true, //lock的修改符--默认是false\n        text: \"加载中，请稍后\", //显示在加载图标下方的加载文案\n        spinner: \"el-icon-loading\", //自定义加载图标类名\n        background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n        target: document.querySelector(\"#sbqxDiv\"),\n      });\n      this.flyjForm = {...row};\n      this.flyjForm.dydj = row.dydj?row.dydj.split(\",\"):[];\n      //下拉框回显\n      await this.getSbbjList(row.sblxbm);\n      await this.getSbbwList(row.parentSbbj);\n      await this.getQxmsList(row.parentSbbw);\n      this.isShowDetail = false;\n      this.addFlyj = false;//不是新增\n      this.isShowFlyj = true;\n      this.loading.close();//关闭遮罩层\n    },\n    //删除\n    deleteRow(row){\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        deleteFlyjById(row).then(res => {\n          if(res.code === '0000'){\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            });\n            this.getData();\n          }else{\n            this.$message({\n              type: 'error',\n              message: '删除失败!'\n            });\n          }\n        })\n      })\n    },\n    //查看\n    viewFun(row){\n      this.viewForm = {...row};\n      this.viewForm.dydj = row.dydj?row.dydj.split(\",\"):[];\n      this.isShowDetail = true;\n    },\n    //新增\n    addForm(formType){\n      //先清空下拉框的值\n      this.sbbjList = [];\n      this.sbbwList = [];\n      this.qxmsList = [];\n      //如果树节点有值，则带过来\n      let sblx = this.queryParams.sblxbm?this.queryParams.sblxbm:'';\n      let sbbj = this.queryParams.parentSbbj?this.queryParams.parentSbbj:'';\n      let sbbw = this.queryParams.parentSbbw?this.queryParams.parentSbbw:'';\n      this.isShowDetail = false;\n      switch (formType){\n        case 'sbbj'://设备部件\n          this.sbbjForm = {};\n          // this.$set(this.sbbjForm,'sblx',sblx);\n          // this.$set(this.sbbjForm,'sbbj',sbbj);\n          // this.$set(this.sbbjForm,'sbbw',sbbw);\n          this.isShowSbbj = true;\n          break;\n        case 'sbbw'://设备部位\n          this.sbbwForm = {};\n          // this.$set(this.sbbwForm,'sblx',sblx);\n          // this.$set(this.sbbwForm,'sbbj',sbbj);\n          // this.$set(this.sbbwForm,'sbbw',sbbw);\n          this.isShowSbbw = true;\n          break;\n        case 'qxms'://隐患描述\n          this.qxmsForm = {};\n          // this.$set(this.qxmsForm,'sblx',sblx);\n          // this.$set(this.qxmsForm,'sbbj',sbbj);\n          // this.$set(this.qxmsForm,'sbbw',sbbw);\n          this.isShowQxms = true;\n          break;\n        case 'flyj'://分类依据\n          this.flyjForm = {};\n          // this.$set(this.flyjForm,'sblx',sblx);\n          // this.$set(this.flyjForm,'sbbj',sbbj);\n          // this.$set(this.flyjForm,'sbbw',sbbw);\n          this.addFlyj = true;\n          this.isShowFlyj = true;\n          break;\n        default:\n          break;\n      }\n    },\n    //保存\n    async saveForm(formType){\n      await this.$refs[formType].validate((valid) => {\n        if (valid) {\n          let saveForm = {...{qxlb:this.qxlb}};\n          switch (formType){\n            case 'flyjForm'://新增分类依据\n              saveForm = {...saveForm,...this.flyjForm};\n              this.qxmsList.forEach(item=>{\n                if(item.value === saveForm.parentQxms){\n                  saveForm.qxms = item.label;\n                }\n              })\n              if(this.addFlyj){//新增\n                addFlyj(saveForm).then(res=>{\n                  if (res.code === '0000') {\n                    this.$message.success('操作成功');\n                    this.isShowFlyj = false;\n                    this.isShowQxms = false;\n                    this.isShowSbbw = false;\n                    this.isShowSbbj = false;\n                    this.isShowDetail = false;\n                    this.getData();\n                    //关闭弹框\n                  } else {\n                    this.$message.error('操作失败')\n                  }\n                });\n              }else{\n                updateFlyj(saveForm).then(res=> {//编辑\n                  if (res.code === '0000') {\n                    this.$message.success('操作成功');\n                    this.isShowFlyj = false;\n                    this.isShowQxms = false;\n                    this.isShowSbbw = false;\n                    this.isShowSbbj = false;\n                    this.isShowDetail = false;\n                    this.getData();\n                    //关闭弹框\n                  } else {\n                    this.$message.error('操作失败')\n                  }\n                })\n              }\n              break;\n            case 'qxmsForm'://新增隐患描述\n              saveForm = {...saveForm,...this.qxmsForm};\n              this.sbbwList.forEach(item=>{\n                if(item.value === saveForm.parentSbbw){\n                  saveForm.sbbw = item.label;\n                }\n              })\n              addQxms(saveForm).then(res=>{\n                if (res.code === '0000') {\n                  this.$message.success('操作成功');\n                  this.isShowFlyj = false;\n                  this.isShowQxms = false;\n                  this.isShowSbbw = false;\n                  this.isShowSbbj = false;\n                  this.isShowDetail = false;\n                  this.getData();\n                  //关闭弹框\n                } else {\n                  this.$message.error('操作失败')\n                }\n              });\n              break;\n            case 'sbbwForm'://新增隐患描述\n              saveForm = {...saveForm,...this.sbbwForm};\n              this.sbbjList.forEach(item=>{\n                if(item.value === saveForm.parentSbbj){\n                  saveForm.sbbj = item.label;\n                }\n              })\n              addSbbw(saveForm).then(res=>{\n                if (res.code === '0000') {\n                  this.$message.success('操作成功');\n                  this.isShowFlyj = false;\n                  this.isShowQxms = false;\n                  this.isShowSbbw = false;\n                  this.isShowSbbj = false;\n                  this.isShowDetail = false;\n                  this.getData();\n                  //关闭弹框\n                } else {\n                  this.$message.error('操作失败')\n                }\n              });\n              break;\n            case 'sbbjForm'://新增隐患描述\n              saveForm = {...saveForm,...this.sbbjForm};\n              this.sblxList.forEach(item=>{\n                if(item.value === saveForm.sblxbm){\n                  saveForm.sblx = item.label;\n                }\n              })\n              saveForm.dydj = saveForm.dydj?saveForm.dydj.join(\",\"):saveForm.dydj;\n              addSbbj(saveForm).then(res=>{\n                if (res.code === '0000') {\n                  this.$message.success('操作成功');\n                  this.isShowFlyj = false;\n                  this.isShowQxms = false;\n                  this.isShowSbbw = false;\n                  this.isShowSbbj = false;\n                  this.isShowDetail = false;\n                  this.getData();\n                  //关闭弹框\n                } else {\n                  this.$message.error('操作失败')\n                }\n              });\n              break;\n            default:\n              break;\n          }\n        } else {\n          this.$message.error('校验未通过！')\n          return false\n        }\n      })\n    },\n    //设备类型下拉框事件\n    async sblxChangeFun(val){\n      this.clearFormField('sblx');\n      await this.getSbbjList(val);\n    },\n    //设备部件下拉框事件\n    async sbbjChangeFun(val){\n      this.clearFormField('sbbj');\n      await this.getDydj(val);//电压等级\n      await this.getSbbwList(val);\n    },\n    async getDydj(val){\n      await getDydjByBjId({qxlb:this.qxlb,objId:val}).then(res=>{\n        if(res.data[0] && res.data[0].dydj){\n          let dydj = res.data[0].dydj.split(\",\");\n          this.$set(this.flyjForm,'dydj',dydj);\n          this.$set(this.qxmsForm,'dydj',dydj);\n          this.$set(this.sbbwForm,'dydj',dydj);\n        }\n      })\n    },\n    //设备部位下拉框事件\n    async sbbwChangeFun(val){\n      this.clearFormField('sbbw');\n      await this.getQxmsList(val);\n    },\n    //隐患描述下拉框事件\n    async qxmsChangeFun(val){\n      this.clearFormField('qxms');\n      await this.getFlyjList(val);\n    },\n    //清空字段值\n    clearFormField(type){\n      switch (type){\n        case 'sblx'://设备类型\n          this.$set(this.sbbjForm,'sbbj','');\n          this.$set(this.sbbwForm,'parentSbbj','');\n          this.$set(this.qxmsForm,'parentSbbj','');\n          this.$set(this.flyjForm,'parentSbbj','');\n          this.$set(this.flyjForm,'dydj','');\n          this.clearFormField('sbbj');\n          break;\n        case 'sbbj'://设备部件\n          this.$set(this.sbbwForm,'sbbw','');\n          this.$set(this.qxmsForm,'parentSbbw','');\n          this.$set(this.flyjForm,'parentSbbw','');\n          this.clearFormField('sbbw');\n          break;\n        case 'sbbw'://设备部位\n          this.$set(this.qxmsForm,'qxms','');\n          this.$set(this.flyjForm,'parentQxms','');\n          this.clearFormField('qxms');\n          break;\n        case 'qxms'://隐患描述\n          this.$set(this.flyjForm,'flyj','');\n          break;\n          default:\n            break;\n      }\n    },\n    //关闭\n    closeFun(type){\n      this.isShowDetail = false;\n      switch (type){\n        case 'sbbj':\n          this.isShowSbbj = false;\n          break;\n        case 'sbbw':\n          this.isShowSbbw = false;\n          break;\n        case 'qxms':\n          this.isShowQxms = false;\n          break;\n        case 'flyj':\n          this.isShowFlyj = false;\n          break;\n        case 'view':\n          this.isShowDetail = false;\n          break;\n        default:\n          this.isShowSbbj = false;\n          this.isShowSbbw = false;\n          this.isShowQxms = false;\n          this.isShowFlyj = false;\n          this.isShowDetail = false;\n          break;\n      }\n    },\n    //重置按钮\n    filterReset() {\n      this.queryParams = {qxlb:this.qxlb};//重置条件\n    },\n\n    //树监听事件\n    filterNode(value, data) {\n      if (!value) return true\n      return data.label.indexOf(value) !== -1\n    },\n    getTreeData(){\n      getQxsbTree({qxlb:this.qxlb}).then(res=>{\n        this.treeOptions = res.data;\n      })\n    },\n    //树节点点击事件\n    handleNodeClick(node) {\n      this.treeNodeData = node;\n      if(node.identifier === '1'){//电压等级\n        this.queryParams.dydj = node.id;\n        this.queryParams.sblxbm = '';\n        this.queryParams.parentSbbj = '';\n        this.queryParams.parentSbbw = '';\n      }else if (node.identifier === '2') {//设备类型\n        this.queryParams.sblxbm = node.id;\n        this.queryParams.parentSbbj = '';\n        this.queryParams.parentSbbw = '';\n      } else if (node.identifier === '3') {//设备部件\n        this.queryParams.sblxbm = '';\n        this.queryParams.parentSbbj = node.id;\n        this.queryParams.parentSbbw = '';\n      } else if (node.identifier === '4') {//设备部位\n        this.queryParams.sblxbm = '';\n        this.queryParams.parentSbbj = '';\n        this.queryParams.parentSbbw = node.id;\n      }else {\n        this.queryParams = {qxlb:this.qxlb}\n      }\n      this.getData()\n    },\n    //查询列表\n    getData(params) {\n      this.load = true\n      this.queryParams = {...this.queryParams, ...params}\n      getQxList(this.queryParams).then(res => {\n        this.tableAndPageInfo.tableData = res.data.records\n        this.tableAndPageInfo.pager.total = res.data.total\n        this.load = false\n      })\n    },\n    //导出excel\n    exportExcel() {\n      // if(!this.selectData.length > 0){\n      //   this.$message.warning('请在左侧勾选要导出的数据')\n      //   return\n      // }\n      let fileName = \"隐患标准库\";\n      let exportUrl = \"/bzqxFlyj\";\n      exportExcel(exportUrl, this.queryParams, fileName);\n    }\n  }\n}\n</script>\n\n<style lang='scss' scoped>\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  height: 82.6vh;\n  max-height: 82.6vh;\n  overflow: auto;\n}\n/*给左侧数结构header加颜色*/\n.box-card .el-card__header {\n  background: #11ba6d !important;\n}\n.box-card {\n  margin:0;\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.tree {\n  overflow-y: hidden;\n  overflow-x: scroll;\n  width: 80px;\n  height: 500px;\n}\n\n.el-tree {\n  min-width: 100%;\n  display: inline-block !important;\n}\n\n/deep/ .el-dialog:not(.is-fullscreen) {\n  margin-top: 8vh !important;\n}\n/* 设置滚动条的样式 */\n::-webkit-scrollbar {\n  width: 12px;\n}\n\n/* 滚动槽 */\n::-webkit-scrollbar-track {\n  //-webkit-box-shadow:inset006pxrgba(0,0,0,0.3);\n  border-radius: 10px;\n}\n\n/* 滚动条滑块 */\n::-webkit-scrollbar-thumb {\n  border-radius: 10px;\n  background: rgba(0, 0, 0, 0.1);\n  //-webkit-box-shadow:gba(0,0,0,0.5);\n}\n\n::-webkit-scrollbar-thumb:window-inactive {\n  background: rgba(0, 0, 0, 0.1);\n}\n</style>\n<style>\n\n</style>\n\n\n\n\n\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl"}]}