{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\dataChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\dataChart.vue", "mtime": 1706897323432}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["dataChart.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAoBA;;AACA;;;;;;;;;;;;;;;;;;;;;eACA;AACA,EAAA,IAAA,EAAA,WADA;AAEA,EAAA,KAAA,EAAA;AACA,IAAA,MAAA,EAAA;AACA,MAAA,IAAA,EAAA;AADA;AADA,GAFA;AAOA,EAAA,IAPA,kBAOA;AACA,WAAA;AACA,MAAA,KAAA,EAAA,QADA;AAEA,MAAA,MAAA,EAAA,EAFA;AAGA,MAAA,GAAA,EAAA,EAHA;AAGA;AACA,MAAA,KAAA,EAAA,IAJA,CAIA;;AAJA,KAAA;AAMA,GAdA;AAeA,EAAA,OAfA,qBAeA,CAAA,CAfA;AAgBA,EAAA,OAhBA,qBAgBA;AACA,SAAA,SAAA,CAAA,YAAA,CAAA,CAAA;AACA,GAlBA;AAmBA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,SAFA,uBAEA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,KAAA,CAAA,GAAA,GAAA,EAAA;AACA,gBAAA,KAAA,CAAA,MAAA,GAAA,EAAA,CAFA,CAGA;;AACA,gBAAA,MAJA,GAIA;AACA,kBAAA,MAAA,EAAA,KAAA,CAAA,MADA;AAEA,kBAAA,QAAA,EAAA;AAFA,iBAJA;AAQA,yCAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,sBAAA,GAAA,CAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,oBAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,0BAAA,KAAA,GAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAA,CAAA,CAAA,CAAA,CAAA;;AACA,0BAAA,KAAA,EAAA;AACA,wBAAA,KAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA;;AACA,wBAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,IAAA,EAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA,EAAA,EAAA,CAAA;AACA;AACA,qBANA;;AAOA,wBAAA,GAAA,CAAA,IAAA,CAAA,MAAA,IAAA,KAAA,CAAA,MAAA,CAAA,MAAA,EAAA;AACA,sBAAA,KAAA,CAAA,QAAA,CAAA,OAAA,CAAA,iBAAA;AACA;;AACA,oBAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,KAAA,CAAA,MAAA,EAXA,CAYA;;AACA,oBAAA,KAAA,CAAA,UAAA;AACA,mBAdA,MAcA;AACA,oBAAA,KAAA,CAAA,QAAA,CAAA,IAAA,CAAA,aAAA;AACA;AACA,iBAlBA;;AARA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA2BA,KA7BA;AA8BA;AACA,IAAA,UA/BA,wBA+BA;AACA,UAAA,MAAA,GAAA,KAAA,KAAA,CAAA,SAAA;AACA,UAAA,OAAA,GAAA,OAAA,CAAA,IAAA,CAAA,MAAA,CAAA;AACA,WAAA,KAAA,GAAA,OAAA;AAEA,UAAA,MAAA;AACA,MAAA,MAAA,GAAA;AACA,QAAA,KAAA,EAAA,CACA,SADA,EAEA,SAFA,EAGA,SAHA,EAIA,SAJA,EAKA,SALA,EAMA,SANA,EAOA,SAPA,EAQA,SARA,EASA,SATA,CADA;AAYA,QAAA,OAAA,EAAA;AACA,UAAA,OAAA,EAAA,MADA;AAEA,UAAA,WAAA,EAAA;AACA,YAAA,IAAA,EAAA,OADA;AAEA,YAAA,UAAA,EAAA;AACA,cAAA,KAAA,EAAA;AADA;AAFA;AAFA,SAZA;AAqBA,QAAA,OAAA,EAAA;AACA,UAAA,OAAA,EAAA;AACA,YAAA,SAAA,EAAA;AAAA,cAAA,IAAA,EAAA,IAAA;AAAA,cAAA,IAAA,EAAA,CAAA,MAAA,EAAA,KAAA;AAAA,aADA;AAEA,YAAA,OAAA,EAAA;AAAA,cAAA,IAAA,EAAA;AAAA,aAFA;AAGA,YAAA,WAAA,EAAA;AAAA,cAAA,IAAA,EAAA;AAAA;AAHA;AADA,SArBA;AA4BA,QAAA,MAAA,EAAA;AACA,UAAA,GAAA,EAAA;AADA,SA5BA;AA+BA,QAAA,KAAA,EAAA,CACA;AACA,UAAA,IAAA,EAAA,UADA;AAEA,UAAA,IAAA,EAAA,KAAA,GAFA;AAGA,UAAA,WAAA,EAAA;AACA,YAAA,IAAA,EAAA;AADA,WAHA;AAMA,UAAA,SAAA,EAAA;AAAA,YAAA,QAAA,EAAA,CAAA;AAAA,YAAA,MAAA,EAAA;AAAA;AANA,SADA,CA/BA;AAyCA,QAAA,KAAA,EAAA,CACA;AACA,UAAA,IAAA,EAAA,OADA;AAEA,UAAA,GAAA,EAAA,CAFA;AAGA,UAAA,SAAA,EAAA;AACA,YAAA,SAAA,EAAA;AADA;AAHA,SADA,CAzCA;AAkDA,QAAA,MAAA,EAAA,CACA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,QAAA,EAAA,EAHA;AAIA,UAAA,KAAA,EAAA;AACA,YAAA,IAAA,EAAA,IADA;AAEA,YAAA,QAAA,EAAA;AAFA,WAJA;AAQA,UAAA,OAAA,EAAA;AACA,YAAA,cAAA,EAAA,wBAAA,KAAA,EAAA;AACA,qBAAA,KAAA;AACA;AAHA,WARA;AAaA,UAAA,IAAA,EAAA,KAAA,MAbA;AAcA,UAAA,SAAA,EAAA;AACA,YAAA,YAAA,EAAA,CADA;AAEA,YAAA,KAAA,EAAA,IAAA,OAAA,CAAA,OAAA,CAAA,cAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CACA;AAAA,cAAA,MAAA,EAAA,CAAA;AAAA,cAAA,KAAA,EAAA;AAAA,aADA,EAEA;AAAA,cAAA,MAAA,EAAA,CAAA;AAAA,cAAA,KAAA,EAAA;AAAA,aAFA,CAAA;AAFA;AAdA,SADA;AAlDA,OAAA;AA2EA,MAAA,MAAA,IAAA,OAAA,CAAA,SAAA,CAAA,MAAA,CAAA;AACA;AAjHA,GAnBA;AAsIA,EAAA,KAAA,EAAA;AACA,IAAA,MADA,kBACA,MADA,EACA;AACA,UAAA,MAAA,EAAA;AACA,aAAA,SAAA;AACA;AACA;AALA;AAtIA,C", "sourcesContent": ["<template>\r\n  <div class=\"defaultCls\">\r\n    <el-white class=\"titleCls_1\">\r\n      <el-row>\r\n        <el-col>\r\n          <div>\r\n            <el-divider direction=\"vertical\" class=\"vertical_d\"></el-divider>\r\n            <span class=\"tjfx_title\">{{ title }}</span>\r\n            <el-divider></el-divider>\r\n          </div>\r\n        </el-col>\r\n        <el-col>\r\n          <div ref=\"thisChart\" class=\"tjHeight3\"></div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-white>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from \"echarts\";\r\nimport { getCellData } from \"@/api/dagangOilfield/bzgl/sybglr\";\r\nexport default {\r\n  name: \"dataChart\",\r\n  props: {\r\n    cellId: {\r\n      type: String\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      title: \"试验数据对比\",\r\n      tjData: [],\r\n      tjx: [], //统计项\r\n      chart: null //统计图对象\r\n    };\r\n  },\r\n  created() {},\r\n  mounted() {\r\n    this.$nextTick(() => {});\r\n  },\r\n  methods: {\r\n    //查询统计数据\r\n    async getTjData() {\r\n      this.tjx = [];\r\n      this.tjData = [];\r\n      //查询数据\r\n      let params = {\r\n        cellId: this.cellId,\r\n        queryNum: \"10\"\r\n      };\r\n      getCellData(params).then(res => {\r\n        if (res.data.length > 1) {\r\n          res.data.forEach(item => {\r\n            let value = Number(item[Object.keys(item)[1]]);\r\n            if (value) {\r\n              this.tjData.push(value);\r\n              this.tjx.push(item[Object.keys(item)[0]].substring(0, 10));\r\n            }\r\n          });\r\n          if (res.data.length != this.tjData.length) {\r\n            this.$message.warning(\"存在空或异常的试验数值，已忽略\");\r\n          }\r\n          console.log(\"处理后的data\",this.tjData);\r\n          //展示统计图\r\n          this.showCharts();\r\n        } else {\r\n          this.$message.info(\"试验次数不足，无法对比\");\r\n        }\r\n      });\r\n    },\r\n    //初始化统计图\r\n    showCharts() {\r\n      let bar_dv = this.$refs.thisChart;\r\n      let myChart = echarts.init(bar_dv);\r\n      this.chart = myChart;\r\n\r\n      let option;\r\n      option = {\r\n        color: [\r\n          \"#4992ff\",\r\n          \"#7cffb2\",\r\n          \"#fddd60\",\r\n          \"#ff6e76\",\r\n          \"#58d9f9\",\r\n          \"#05c091\",\r\n          \"#ff8a45\",\r\n          \"#8d48e3\",\r\n          \"#dd79ff\"\r\n        ],\r\n        tooltip: {\r\n          trigger: \"axis\",\r\n          axisPointer: {\r\n            type: \"cross\",\r\n            crossStyle: {\r\n              color: \"#999\"\r\n            }\r\n          }\r\n        },\r\n        toolbox: {\r\n          feature: {\r\n            magicType: { show: true, type: [\"line\", \"bar\"] },\r\n            restore: { show: true },\r\n            saveAsImage: { show: true }\r\n          }\r\n        },\r\n        legend: {\r\n          top: \"left\"\r\n        },\r\n        xAxis: [\r\n          {\r\n            type: \"category\",\r\n            data: this.tjx,\r\n            axisPointer: {\r\n              type: \"shadow\"\r\n            },\r\n            axisLabel: { interval: 0, rotate: 25 }\r\n          }\r\n        ],\r\n        yAxis: [\r\n          {\r\n            type: \"value\",\r\n            min: 0,\r\n            axisLabel: {\r\n              formatter: \"{value}\"\r\n            }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            name: \"试验数值\",\r\n            type: \"line\",\r\n            barWidth: 25,\r\n            label: {\r\n              show: true,\r\n              position: \"top\"\r\n            },\r\n            tooltip: {\r\n              valueFormatter: function(value) {\r\n                return value;\r\n              }\r\n            },\r\n            data: this.tjData,\r\n            itemStyle: {\r\n              borderRadius: 5,\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: \"#abf1e0\" },\r\n                { offset: 1, color: \"#14c8d4\" }\r\n              ])\r\n            }\r\n          }\r\n        ]\r\n      };\r\n      option && myChart.setOption(option);\r\n    }\r\n  },\r\n  watch: {\r\n    cellId(newVal) {\r\n      if (newVal) {\r\n        this.getTjData();\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.defaultCls {\r\n  padding-top: 5px;\r\n  padding-bottom: 10px;\r\n}\r\n</style>\r\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk"}]}