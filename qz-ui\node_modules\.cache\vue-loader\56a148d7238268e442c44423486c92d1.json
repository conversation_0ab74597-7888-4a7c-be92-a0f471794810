{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\ztlmxwh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\ztlmxwh.vue", "mtime": 1706897323231}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgZ2V0UGFnZUxpc3QsCiAgc2F2ZU9yVXBkYXRlLAogIHJlbW92ZSwKICBnZXRTYmx4QW5kU2JialRyZWUsCn0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYnpnbC9zYnp0cGpiemsvenRsbXh3aCI7CmltcG9ydCBadGx4eGRBbmRQZHlqIGZyb20gIkAvdmlld3MvZGFnYW5nT2lsZmllbGQvYnpnbC9zYnp0cGpiemsvenRseHhkQW5kUGR5aiI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogInp0bG14d2giLAogIGNvbXBvbmVudHM6IHtadGx4eGRBbmRQZHlqfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy90YWLpobXlkI3np7AKICAgICAgYWN0aXZlTmFtZTogJ3F4JywKICAgICAgLy/moJHnu5PmnoQKICAgICAgZGVmYXVsdFByb3BzOiB7CiAgICAgICAgY2hpbGRyZW46ICJjaGlsZHJlbiIsCiAgICAgICAgbGFiZWw6ICJsYWJlbCIsCiAgICAgIH0sCiAgICAgIC8v6K6+5aSH57G75Z6L57yW56CBCiAgICAgIHNibHhJZDogdW5kZWZpbmVkLAogICAgICBzYmx4Ym06IHVuZGVmaW5lZCwKCiAgICAgIC8v5paw5aKe5oyJ6ZKuZm9ybeihqOWNlQogICAgICBmb3JtOiB7CiAgICAgICAgc2JseDogdW5kZWZpbmVkLAogICAgICAgIHNibHhibTogdW5kZWZpbmVkLAogICAgICAgIHR3b3NibHg6IHVuZGVmaW5lZCwKICAgICAgICB0d29zYmx4Ym06IHVuZGVmaW5lZCwKICAgICAgICAvL+iuvuWkh+exu+Wei+e8lueggQogICAgICAgIHNibHhJZDogdW5kZWZpbmVkLAogICAgICAgIC8v6K6+5aSH6YOo5Lu2aWQKICAgICAgICBzYmJqSWQ6IHVuZGVmaW5lZCwKICAgICAgfSwKICAgICAgdGl0bGU6ICIiLAogICAgICBzaG93OiBmYWxzZSwKICAgICAgZmlsdGVySW5mbzogewogICAgICAgIGRhdGE6IHsKICAgICAgICAgIHl3ZHdBcnI6IFtdLAogICAgICAgIH0sCiAgICAgICAgZmllbGRMaXN0OiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi6K6+5aSH57G75Z6LIiwKICAgICAgICAgICAgdHlwZTogInNlbGVjdCIsCiAgICAgICAgICAgIHZhbHVlOiAicm9sZU5hbWUiLAogICAgICAgICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgICAgICAgb3B0aW9uczogW10sCiAgICAgICAgICB9LAogICAgICAgIF0sCiAgICAgIH0sCgogICAgICB0YWJsZUFuZFBhZ2VJbmZvOiB7CiAgICAgICAgcGFnZXI6IHsKICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgIHNpemVzOiBbMTAsIDIwLCA1MCwgMTAwXSwKICAgICAgICB9LAogICAgICAgIG9wdGlvbjogewogICAgICAgICAgY2hlY2tCb3g6IHRydWUsCiAgICAgICAgICBzZXJpYWxOdW1iZXI6IHRydWUsCiAgICAgICAgfSwKICAgICAgICB0YWJsZURhdGE6IFtdLAogICAgICAgIHRhYmxlSGVhZGVyOiBbCiAgICAgICAgICB7cHJvcDogInNibHhtYyIsIGxhYmVsOiAi6K+E5Lu35a+85YiZ6K6+5aSH57G75Z6LIiwgbWluV2lkdGg6ICcxMzAnfSwKICAgICAgICAgIHtwcm9wOiAic2Jiam1jIiwgbGFiZWw6ICLpg6jku7blkI3np7AifSwKICAgICAgICAgIHtwcm9wOiAienRsbWMiLCBsYWJlbDogIueKtuaAgemHj+WQjeensCJ9LAogICAgICAgICAge3Byb3A6ICJ6ZGtmeiIsIGxhYmVsOiAi5pyA5aSn5omj5YiG5YC8In0sCiAgICAgICAgICB7cHJvcDogInp0bGx5bWMiLCBsYWJlbDogIueKtuaAgemHj+adpea6kCJ9LAogICAgICAgICAge3Byb3A6ICJ6dGxneiIsIGxhYmVsOiAi54q25oCB6YeP6KeE5YiZIn0sCiAgICAgICAgICB7cHJvcDogImJ6IiwgbGFiZWw6ICLlpIfms6gifSwKICAgICAgICAgIHsKICAgICAgICAgICAgcHJvcDogIm9wZXJhdGlvbiIsCiAgICAgICAgICAgIGxhYmVsOiAi5pON5L2cIiwKICAgICAgICAgICAgbWluV2lkdGg6ICIxMzBweCIsCiAgICAgICAgICAgIHN0eWxlOiB7ZGlzcGxheTogImJsb2NrIn0sCiAgICAgICAgICAgIC8v5pON5L2c5YiX5Zu65a6a5YaN5Y+z5L6nCiAgICAgICAgICAgIGZpeGVkOiAicmlnaHQiLAogICAgICAgICAgICBvcGVyYXRpb246IFsKICAgICAgICAgICAgICB7bmFtZTogIueKtuaAgemHj+S/oeaBr+eCuS/liKTmlq3kvp3mja4iLCBjbGlja0Z1bjogdGhpcy5pbnNlcnR6dGx9LAogICAgICAgICAgICAgIHtuYW1lOiAi5L+u5pS5IiwgY2xpY2tGdW46IHRoaXMudXBkYXRlUm93fSwKICAgICAgICAgICAgICB7bmFtZTogIuivpuaDhSIsIGNsaWNrRnVuOiB0aGlzLmdldEluZm99LAogICAgICAgICAgICBdLAogICAgICAgICAgfSwKICAgICAgICBdLAogICAgICB9LAogICAgICBxdWVyeXp0bG14d2hQYXJhbTogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHNiYmpJZDogIiIsCiAgICAgIH0sCiAgICAgIHNlbGVjdFJvd3M6IFtdLAogICAgICAvL+eCueWHu+agkeiKgueCuei1i+WAvAogICAgICB0cmVlRm9ybToge30sCiAgICAgIC8v57uE57uH5qCRCiAgICAgIHRyZWVkYXRhOiBbXSwKICAgICAgLy/mlrDlop7mjInpkq7mjqfliLYKICAgICAgYWRkRGlzYWJsZWQ6IHRydWUsCiAgICAgIGJqbWNMaXN0OiBbXSwKICAgICAgenRsbHlMaXN0OiBbCiAgICAgICAge2xhYmVsOiAi6ZqQ5oKjIiwgdmFsdWU6ICJxeCJ9LAogICAgICAgIHtsYWJlbDogIuivlemqjCIsIHZhbHVlOiAic3kifSwKICAgICAgXSwKICAgICAgenRsemx4TGlzdDogWwogICAgICAgIHtsYWJlbDogIumAu+i+keWeiyIsIHZhbHVlOiAi6YC76L6R5Z6LIn0sCiAgICAgICAge2xhYmVsOiAi5pWw5YC85Z6LIiwgdmFsdWU6ICLmlbDlgLzlnosifSwKICAgICAgXSwKCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgenRsbHk6IFsKICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIueKtuaAgemHj+adpea6kOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIn0sCiAgICAgICAgXSwKICAgICAgICB6dGxtYzogWwogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi54q25oCB6YeP5ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIifSwKICAgICAgICBdLAogICAgICAgIC8vIHpka2Z6OiBbCiAgICAgICAgLy8gICB7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmnIDlpKfmiaPliIblgLzkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciJ9LAogICAgICAgIC8vIF0sCiAgICAgICAgenRscXo6IFsKICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIn0sCiAgICAgICAgXSwKICAgICAgfSwKCiAgICAgIGlzRGlzYWJsZWQ6IGZhbHNlLAogICAgICAvL+mAieS4reihjOaVsOaNrgogICAgICByb3dEYXRhOiB7fSwKICAgICAgLy/ooajljZXpgInkuK3mlbDmja4KICAgICAgc2VsZWN0ZWRSb3dEYXRhQXJyOiBbXSwKICAgICAgLy/nirbmgIHph4/kv6Hmga/ngrnmlrDlop4KICAgICAgaXNTaG93UGFyYW1zQW5kUGFydHM6IGZhbHNlLAogICAgfTsKICB9LAogIGNyZWF0ZSgpIHsKICB9LAoKICBtb3VudGVkKCkgewogICAgLy/liJfooajmn6Xor6IKICAgIHRoaXMuZ2V0VHJlZU5vZGUoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGFzeW5jIGhhbmRsZUNsaWNrKCkgewogICAgICBhd2FpdCB0aGlzLmdldERhdGEoKTsKICAgIH0sCiAgICAvL+iOt+WPluagkeiKgueCueaVsOaNrgogICAgZ2V0VHJlZU5vZGUoKSB7CiAgICAgIGdldFNibHhBbmRTYmJqVHJlZSh7dHlwZTogInp0bG14In0pLnRoZW4oKHJlcykgPT4gewogICAgICAgIGlmIChyZXMuY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICB0aGlzLnRyZWVkYXRhID0gcmVzLmRhdGE7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCgogICAgLy/moJHoioLngrnngrnlh7vkuovku7YKICAgIGhhbmRsZU5vZGVDbGljayhkYXRhLCBub2RlKSB7CiAgICAgIHRoaXMuYWRkRGlzYWJsZWQgPSB0cnVlCiAgICAgIHRoaXMudHJlZUZvcm0uc2JiaklkID0gIiI7CiAgICAgIHRoaXMudHJlZUZvcm0uc2Jiam1jID0gIiI7CiAgICAgIHRoaXMudHJlZUZvcm0uc2JseElkID0gIiI7CiAgICAgIHRoaXMudHJlZUZvcm0uc2JseG1jID0gIiI7CiAgICAgIHRoaXMucXVlcnl6dGxteHdoUGFyYW0uc2JiaklkID0gIiI7CiAgICAgIHRoaXMucXVlcnl6dGxteHdoUGFyYW0uc2JseElkID0gIiI7CiAgICAgIGlmIChkYXRhLm5vZGVMZXZlbCA9PT0gIjIiKSB7CiAgICAgICAgdGhpcy5hZGREaXNhYmxlZCA9IGZhbHNlOwogICAgICAgIHRoaXMudHJlZUZvcm0uc2JiaklkID0gZGF0YS5pZDsKICAgICAgICB0aGlzLnRyZWVGb3JtLnNiYmptYyA9IGRhdGEubGFiZWw7CiAgICAgICAgdGhpcy50cmVlRm9ybS5zYmx4SWQgPSBub2RlLnBhcmVudC5kYXRhLmlkOwogICAgICAgIHRoaXMudHJlZUZvcm0uc2JseG1jID0gbm9kZS5wYXJlbnQuZGF0YS5sYWJlbDsKICAgICAgICB0aGlzLnF1ZXJ5enRsbXh3aFBhcmFtLnNiYmpJZCA9IGRhdGEuaWQ7CiAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgIH0KICAgICAgaWYgKGRhdGEubm9kZUxldmVsID09PSAiMSIpIHsKICAgICAgICB0aGlzLnF1ZXJ5enRsbXh3aFBhcmFtLnNibHhJZCA9IGRhdGEuaWQ7CiAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgIH0KICAgIH0sCgogICAgLy/liJfooajmn6Xor6IKICAgIGFzeW5jIGdldERhdGEocGFyYW1zKSB7CiAgICAgIHRyeSB7CiAgICAgICAgdGhpcy5xdWVyeXp0bG14d2hQYXJhbSA9IHsuLi50aGlzLnF1ZXJ5enRsbXh3aFBhcmFtLCAuLi5wYXJhbXMsLi4ue3p0bGx5OnRoaXMuYWN0aXZlTmFtZX19OwogICAgICAgIGNvbnN0IHtkYXRhLCBjb2RlfSA9IGF3YWl0IGdldFBhZ2VMaXN0KHRoaXMucXVlcnl6dGxteHdoUGFyYW0pOwogICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIGZvciAobGV0IGkgb2YgZGF0YS5yZWNvcmRzKSB7CiAgICAgICAgICAgIGkuenRsbHltYyA9IGkuenRsbHkgPT09ICJxeCIgPyAn6ZqQ5oKjJyA6ICfor5XpqownCiAgICAgICAgICB9CiAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8udGFibGVEYXRhID0gZGF0YS5yZWNvcmRzOwogICAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnRvdGFsID0gZGF0YS50b3RhbDsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICBjb25zb2xlLmxvZyhlKTsKICAgICAgfQogICAgfSwKICAgIC8v6YeN572u5oyJ6ZKuCiAgICBnZXRSZXNldCgpIHsKICAgIH0sCiAgICAvL+mAieS4reihjAogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHJvdykgewogICAgICB0aGlzLnNlbGVjdGVkUm93RGF0YUFyciA9IHJvdzsKICAgIH0sCiAgICAvL+ivpuaDhQogICAgZ2V0RGV0YWlscygpIHsKICAgICAgdGhpcy50aXRsZSA9ICLnirbmgIHph4/mqKHlnovor6bmg4UiOwogICAgICB0aGlzLmlzRGlzYWJsZWQgPSB0cnVlOwogICAgICB0aGlzLnNob3cgPSB0cnVlOwogICAgICB0aGlzLmZvcm0gPSB7Li4ucm93fTsKICAgIH0sCiAgICAvL+aWsOWingogICAgZ2V0SW5zdGVyKCkgewogICAgICBpZiAoIXRoaXMudHJlZUZvcm0uc2JiaklkKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7fpgInmi6nlt6bkvqflr7nlupTorr7lr7zliJnvvIHvvIHvvIEiKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIHRoaXMuZm9ybS5zYmx4SWQgPSB0aGlzLnRyZWVGb3JtLnNibHhJZDsKICAgICAgdGhpcy5mb3JtLnNibHhtYyA9IHRoaXMudHJlZUZvcm0uc2JseG1jOwogICAgICB0aGlzLmZvcm0uc2JiaklkID0gdGhpcy50cmVlRm9ybS5zYmJqSWQ7CiAgICAgIHRoaXMuZm9ybS5zYmJqbWMgPSB0aGlzLnRyZWVGb3JtLnNiYmptYzsKICAgICAgdGhpcy50aXRsZSA9ICLnirbmgIHph4/mqKHlnovmlrDlop4iOwogICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZTsKICAgICAgdGhpcy5zaG93ID0gdHJ1ZTsKICAgIH0sCiAgICAvL+e8lui+keaMiemSrgogICAgdXBkYXRlUm93KHJvdykgewogICAgICB0aGlzLnRpdGxlID0gIueKtuaAgemHj+aooeWei+S/ruaUuSI7CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICB0aGlzLmZvcm0gPSB7Li4ucm93fTsKICAgICAgdGhpcy5zaG93ID0gdHJ1ZTsKICAgIH0sCiAgICAvL+ivpuaDheaMiemSrgogICAgZ2V0SW5mbyhyb3cpIHsKICAgICAgdGhpcy50aXRsZSA9ICLor6bmg4Xmn6XnnIsiOwogICAgICB0aGlzLmZvcm0gPSB7Li4ucm93fTsKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gdHJ1ZTsKICAgICAgdGhpcy5zaG93ID0gdHJ1ZTsKICAgIH0sCiAgICAvL+eKtuaAgemHj+S/oeaBr+aWsOWinu+8mwogICAgaW5zZXJ0enRsKHJvdykgewogICAgICB0aGlzLnJvd0RhdGEgPSByb3c7CiAgICAgIHRoaXMuaXNTaG93UGFyYW1zQW5kUGFydHMgPSB0cnVlOwogICAgfSwKICAgIC8v54q25oCB6YeP5L+h5oGv54K55YWz6ZetCiAgICBjbG9zZVBhcmFtRGlhbG9nKCkgewogICAgICB0aGlzLmlzU2hvd1BhcmFtc0FuZFBhcnRzID0gZmFsc2U7CiAgICB9LAoKICAgIGFzeW5jIHNhdmVSb3coKSB7CiAgICAgIGF3YWl0IHRoaXMuJHJlZnNbJ2Zvcm0nXS52YWxpZGF0ZShhc3luYyB2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB0cnkgewogICAgICAgICAgICBsZXQge2NvZGV9ID0gYXdhaXQgc2F2ZU9yVXBkYXRlKHRoaXMuZm9ybSk7CiAgICAgICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKnyIpOwogICAgICAgICAgICB9CiAgICAgICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgICAgIGNvbnNvbGUubG9nKGUpOwogICAgICAgICAgfQogICAgICAgICAgYXdhaXQgdGhpcy5nZXREYXRhKCk7CiAgICAgICAgICB0aGlzLnNob3cgPSBmYWxzZTsKICAgICAgICB9fSkKICAgIH0sCiAgICAvL+WIoOmZpOaMiemSrgogICAgYXN5bmMgZGVsZXRlUm93KCkgewogICAgICBpZiAodGhpcy5zZWxlY3RSb3dzLmxlbmd0aCA8IDEpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+mAieaLqeato+ehrueahOaVsOaNru+8ge+8ge+8gSIpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBsZXQgaWRzID0gdGhpcy5zZWxlY3RSb3dzLm1hcCgoaXRlbSkgPT4gewogICAgICAgIHJldHVybiBpdGVtLm9iaklkOwogICAgICB9KTsKICAgICAgdGhpcy4kY29uZmlybSgi5q2k5pON5L2c5bCG5rC45LmF5Yig6Zmk6K+l5pWw5o2uLCDmmK/lkKbnu6fnu60/IiwgIuaPkOekuiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciLAogICAgICB9KQogICAgICAgICAgLnRoZW4oKCkgPT4gewogICAgICAgICAgICByZW1vdmUoaWRzKS50aGVuKCh7Y29kZX0pID0+IHsKICAgICAgICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLAogICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5Yig6Zmk5oiQ5YqfISIsCiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgICAgICAgbWVzc2FnZTogIuWIoOmZpOWksei0pSEiLAogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KTsKICAgICAgICAgIH0pCiAgICAgICAgICAuY2F0Y2goKCkgPT4gewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICB0eXBlOiAiaW5mbyIsCiAgICAgICAgICAgICAgbWVzc2FnZTogIuW3suWPlua2iOWIoOmZpCIsCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSk7CiAgICAgIGF3YWl0IHRoaXMuZ2V0RGF0YSgpOwogICAgfSwKICAgIC8v5YWz6Zet5by556qXCiAgICBnZXRJbnN0ZXJDbG9zZSgpIHsKICAgICAgdGhpcy5zaG93ID0gZmFsc2U7CiAgICAgIHRoaXMuZm9ybSA9IHt9OwogICAgfSwKICAgIHNlbGVjdENoYW5nZShyb3dzKSB7CiAgICAgIHRoaXMuc2VsZWN0Um93cyA9IHJvd3M7CiAgICB9LAogIH0sCn07Cg=="}, {"version": 3, "sources": ["ztlmxwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2JA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ztlmxwh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row :gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card class=\"aside_height\" shadow=\"never\">\n          <div slot=\"header\" class=\"clearfix\" style=\"text-align: center\">\n            <span>评价设备</span>\n          </div>\n          <div style=\"overflow: auto; height: 84vh\">\n            <el-tree\n                highlight-current\n                :data=\"treedata\"\n                :props=\"defaultProps\"\n                @node-click=\"handleNodeClick\"\n            ></el-tree>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"20\">\n        <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\" type=\"card\">\n          <el-tab-pane label=\"隐患\" name=\"qx\">\n          </el-tab-pane>\n          <el-tab-pane label=\"试验\" name=\"sy\">\n          </el-tab-pane>\n        </el-tabs>\n        <el-white>\n          <el-white class=\"button-group\">\n            <el-button\n                type=\"primary\"\n                icon=\"el-icon-plus\"\n                @click=\"getInster\"\n                :disabled=\"addDisabled\"\n            >新增\n            </el-button\n            >\n            <el-button\n                type=\"danger\"\n                icon=\"el-icon-delete\"\n                @click=\"deleteRow\"\n            >删除\n            </el-button\n            >\n          </el-white>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"selectChange\"\n            height=\"72.2vh\"\n          />\n        </el-white>\n        <!--新增、修改、详情弹框-->\n        <el-dialog\n            :title=\"title\"\n            v-dialogDrag\n            :visible.sync=\"show\"\n            width=\"50%\"\n            append-to-body\n            @close=\"getInsterClose\"\n        >\n          <el-form label-width=\"130px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n            <el-row :gutter=\"8\" class=\"pull-left\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"评价设备类型：\" prop=\"sblx\">\n                  <el-input v-model=\"form.sblxmc\" :disabled=\"true\"/>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"部件名称：\" prop=\"bjmc\">\n                  <el-input v-model=\"form.sbbjmc\" :disabled=\"true\"/>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"状态量名称：\" prop=\"ztlmc\">\n                  <el-input\n                      v-model=\"form.ztlmc\"\n                      placeholder=\"请输入状态量名称\"\n                      :disabled=\"isDisabled\"\n                  />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"状态量权重：\" prop=\"ztlqz\"  v-if=\"form.sblxId&&form.sblxId.indexOf('bd')>-1\">\n                  <el-input-number v-model=\"form.ztlqz\" placeholder=\"请输入状态量权重\" :min=\"1\" :precision=\"0\" :max=\"4\" :disabled=\"isDisabled\"/>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"最大扣分值：\" prop=\"zdkfz\">\n                  <el-input\n                      v-model=\"form.zdkfz\"\n                      placeholder=\"最大扣分值\"\n                      type=\"number\"\n                      disabled\n                  />\n                </el-form-item>\n              </el-col>\n\n              <el-col :span=\"12\">\n                <el-form-item label=\"状态量来源：\" prop=\"ztlly\">\n                  <el-select v-model=\"form.ztlly\" style=\"width: 100%\" placeholder=\"状态量来源\" :disabled=\"isDisabled\">\n                    <el-option\n                        v-for=\"item in ztllyList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n\n<!--              <el-col :span=\"24\">\n                <el-form-item label=\"状态量规则：\" prop=\"ztlgz\">\n                  <el-input\n                      type=\"textarea\"\n                      v-model=\"form.ztlgz\"\n                      placeholder=\"状态量规则\"\n                      :disabled=\"isDisabled\"\n                  />\n                </el-form-item>\n              </el-col>-->\n              <el-col :span=\"24\">\n                <el-form-item label=\"备注：\" prop=\"bz\">\n                  <el-input\n                      type=\"textarea\"\n                      v-model=\"form.bz\"\n                      placeholder=\"备注\"\n                      :disabled=\"isDisabled\"\n                  />\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </el-form>\n          <div slot=\"footer\" class=\"dialog-footer\">\n            <el-button @click=\"getInsterClose\">关 闭</el-button>\n            <el-button type=\"primary\" @click=\"saveRow\">保 存</el-button>\n          </div>\n        </el-dialog>\n      </el-col>\n    </el-row>\n\n    <el-dialog\n        :visible.sync=\"isShowParamsAndParts\"\n        v-dialogDrag\n        v-if=\"isShowParamsAndParts\"\n        width=\"70%\"\n        title=\"状态量信息点/判断依据维护\"\n        :append-to-body=\"true\"\n    >\n      <ZtlxxdAndPdyj :mp-data=\"rowData\" @closeParamDialog=\"closeParamDialog\">\n      </ZtlxxdAndPdyj>\n    </el-dialog>\n  </div>\n</template>\n<script>\nimport {\n  getPageList,\n  saveOrUpdate,\n  remove,\n  getSblxAndSbbjTree,\n} from \"@/api/dagangOilfield/bzgl/sbztpjbzk/ztlmxwh\";\nimport ZtlxxdAndPdyj from \"@/views/dagangOilfield/bzgl/sbztpjbzk/ztlxxdAndPdyj\";\n\nexport default {\n  name: \"ztlmxwh\",\n  components: {ZtlxxdAndPdyj},\n  data() {\n    return {\n      //tab页名称\n      activeName: 'qx',\n      //树结构\n      defaultProps: {\n        children: \"children\",\n        label: \"label\",\n      },\n      //设备类型编码\n      sblxId: undefined,\n      sblxbm: undefined,\n\n      //新增按钮form表单\n      form: {\n        sblx: undefined,\n        sblxbm: undefined,\n        twosblx: undefined,\n        twosblxbm: undefined,\n        //设备类型编码\n        sblxId: undefined,\n        //设备部件id\n        sbbjId: undefined,\n      },\n      title: \"\",\n      show: false,\n      filterInfo: {\n        data: {\n          ywdwArr: [],\n        },\n        fieldList: [\n          {\n            label: \"设备类型\",\n            type: \"select\",\n            value: \"roleName\",\n            multiple: true,\n            options: [],\n          },\n        ],\n      },\n\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true,\n        },\n        tableData: [],\n        tableHeader: [\n          {prop: \"sblxmc\", label: \"评价导则设备类型\", minWidth: '130'},\n          {prop: \"sbbjmc\", label: \"部件名称\"},\n          {prop: \"ztlmc\", label: \"状态量名称\"},\n          {prop: \"zdkfz\", label: \"最大扣分值\"},\n          {prop: \"ztllymc\", label: \"状态量来源\"},\n          {prop: \"ztlgz\", label: \"状态量规则\"},\n          {prop: \"bz\", label: \"备注\"},\n          {\n            prop: \"operation\",\n            label: \"操作\",\n            minWidth: \"130px\",\n            style: {display: \"block\"},\n            //操作列固定再右侧\n            fixed: \"right\",\n            operation: [\n              {name: \"状态量信息点/判断依据\", clickFun: this.insertztl},\n              {name: \"修改\", clickFun: this.updateRow},\n              {name: \"详情\", clickFun: this.getInfo},\n            ],\n          },\n        ],\n      },\n      queryztlmxwhParam: {\n        pageNum: 1,\n        pageSize: 10,\n        sbbjId: \"\",\n      },\n      selectRows: [],\n      //点击树节点赋值\n      treeForm: {},\n      //组织树\n      treedata: [],\n      //新增按钮控制\n      addDisabled: true,\n      bjmcList: [],\n      ztllyList: [\n        {label: \"隐患\", value: \"qx\"},\n        {label: \"试验\", value: \"sy\"},\n      ],\n      ztlzlxList: [\n        {label: \"逻辑型\", value: \"逻辑型\"},\n        {label: \"数值型\", value: \"数值型\"},\n      ],\n\n      rules: {\n        ztlly: [\n          {required: true, message: \"状态量来源不能为空\", trigger: \"blur\"},\n        ],\n        ztlmc: [\n          {required: true, message: \"状态量名称不能为空\", trigger: \"blur\"},\n        ],\n        // zdkfz: [\n        //   {required: true, message: \"最大扣分值不能为空\", trigger: \"blur\"},\n        // ],\n        ztlqz: [\n          {required: true, message: \"不能为空\", trigger: \"blur\"},\n        ],\n      },\n\n      isDisabled: false,\n      //选中行数据\n      rowData: {},\n      //表单选中数据\n      selectedRowDataArr: [],\n      //状态量信息点新增\n      isShowParamsAndParts: false,\n    };\n  },\n  create() {\n  },\n\n  mounted() {\n    //列表查询\n    this.getTreeNode();\n  },\n  methods: {\n    async handleClick() {\n      await this.getData();\n    },\n    //获取树节点数据\n    getTreeNode() {\n      getSblxAndSbbjTree({type: \"ztlmx\"}).then((res) => {\n        if (res.code === \"0000\") {\n          this.treedata = res.data;\n        }\n      });\n    },\n\n    //树节点点击事件\n    handleNodeClick(data, node) {\n      this.addDisabled = true\n      this.treeForm.sbbjId = \"\";\n      this.treeForm.sbbjmc = \"\";\n      this.treeForm.sblxId = \"\";\n      this.treeForm.sblxmc = \"\";\n      this.queryztlmxwhParam.sbbjId = \"\";\n      this.queryztlmxwhParam.sblxId = \"\";\n      if (data.nodeLevel === \"2\") {\n        this.addDisabled = false;\n        this.treeForm.sbbjId = data.id;\n        this.treeForm.sbbjmc = data.label;\n        this.treeForm.sblxId = node.parent.data.id;\n        this.treeForm.sblxmc = node.parent.data.label;\n        this.queryztlmxwhParam.sbbjId = data.id;\n        this.getData();\n      }\n      if (data.nodeLevel === \"1\") {\n        this.queryztlmxwhParam.sblxId = data.id;\n        this.getData();\n      }\n    },\n\n    //列表查询\n    async getData(params) {\n      try {\n        this.queryztlmxwhParam = {...this.queryztlmxwhParam, ...params,...{ztlly:this.activeName}};\n        const {data, code} = await getPageList(this.queryztlmxwhParam);\n        if (code === \"0000\") {\n          for (let i of data.records) {\n            i.ztllymc = i.ztlly === \"qx\" ? '隐患' : '试验'\n          }\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //重置按钮\n    getReset() {\n    },\n    //选中行\n    handleSelectionChange(row) {\n      this.selectedRowDataArr = row;\n    },\n    //详情\n    getDetails() {\n      this.title = \"状态量模型详情\";\n      this.isDisabled = true;\n      this.show = true;\n      this.form = {...row};\n    },\n    //新增\n    getInster() {\n      if (!this.treeForm.sbbjId) {\n        this.$message.warning(\"请选择左侧对应设导则！！！\");\n        return;\n      }\n\n      this.form.sblxId = this.treeForm.sblxId;\n      this.form.sblxmc = this.treeForm.sblxmc;\n      this.form.sbbjId = this.treeForm.sbbjId;\n      this.form.sbbjmc = this.treeForm.sbbjmc;\n      this.title = \"状态量模型新增\";\n      this.isDisabled = false;\n      this.show = true;\n    },\n    //编辑按钮\n    updateRow(row) {\n      this.title = \"状态量模型修改\";\n      this.isDisabled = false;\n      this.form = {...row};\n      this.show = true;\n    },\n    //详情按钮\n    getInfo(row) {\n      this.title = \"详情查看\";\n      this.form = {...row};\n      this.isDisabled = true;\n      this.show = true;\n    },\n    //状态量信息新增；\n    insertztl(row) {\n      this.rowData = row;\n      this.isShowParamsAndParts = true;\n    },\n    //状态量信息点关闭\n    closeParamDialog() {\n      this.isShowParamsAndParts = false;\n    },\n\n    async saveRow() {\n      await this.$refs['form'].validate(async valid => {\n        if (valid) {\n          try {\n            let {code} = await saveOrUpdate(this.form);\n            if (code === \"0000\") {\n              this.$message.success(\"操作成功\");\n            }\n          } catch (e) {\n            console.log(e);\n          }\n          await this.getData();\n          this.show = false;\n        }})\n    },\n    //删除按钮\n    async deleteRow() {\n      if (this.selectRows.length < 1) {\n        this.$message.warning(\"请选择正确的数据！！！\");\n        return;\n      }\n      let ids = this.selectRows.map((item) => {\n        return item.objId;\n      });\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      })\n          .then(() => {\n            remove(ids).then(({code}) => {\n              if (code === \"0000\") {\n                this.$message({\n                  type: \"success\",\n                  message: \"删除成功!\",\n                });\n                this.getData();\n              } else {\n                this.$message({\n                  type: \"error\",\n                  message: \"删除失败!\",\n                });\n              }\n            });\n          })\n          .catch(() => {\n            this.$message({\n              type: \"info\",\n              message: \"已取消删除\",\n            });\n          });\n      await this.getData();\n    },\n    //关闭弹窗\n    getInsterClose() {\n      this.show = false;\n      this.form = {};\n    },\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n  },\n};\n</script>\n\n<style lang='scss' scoped>\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  max-height: 79vh;\n  overflow: auto;\n}\n"]}]}