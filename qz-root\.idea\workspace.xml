<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="7adfa91c-0b44-4413-8ea0-c966f63e158b" name="Changes" comment="backend-用户管理-修改部门时，不在清除用户的权限">
      <change beforePath="$PROJECT_DIR$/qz-manager/src/main/java/com/qz/mapper/asset/AssetBdMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/qz-manager/src/main/java/com/qz/mapper/asset/AssetBdMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/qz-manager/src/main/java/com/qz/service/asset/impl/AssetBdServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/qz-manager/src/main/java/com/qz/service/asset/impl/AssetBdServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/qz-manager/src/main/resources/mapper/asset/AssetBdMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/qz-manager/src/main/resources/mapper/asset/AssetBdMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/qz-operation/src/main/java/com/qz/service/impl/gzpgl/YxGzpServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/qz-operation/src/main/java/com/qz/service/impl/gzpgl/YxGzpServiceImpl.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="MAKE_PROJECT_ON_SAVE" value="true" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="InvalidFacetManager">
    <ignored-facets>
      <facet id="qz-activiti/invalid/JRebel" />
      <facet id="qz-base-common/invalid/JRebel" />
      <facet id="qz-condition-maintenance/invalid/JRebel" />
      <facet id="qz-eureka/invalid/JRebel" />
      <facet id="qz-feign-api/invalid/JRebel" />
      <facet id="qz-isc/invalid/JRebel" />
      <facet id="qz-log/invalid/JRebel" />
      <facet id="qz-manager/invalid/JRebel" />
      <facet id="qz-operation/invalid/JRebel" />
      <facet id="qz-repair-schedule/invalid/JRebel" />
      <facet id="qz-security/invalid/JRebel" />
    </ignored-facets>
  </component>
  <component name="JRebelWorkspace">
    <option name="jrebelEnabledAutocompile" value="true" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\JavaDevelopment\apache-maven-3.6.1" />
        <option name="localRepository" value="D:\JavaDevelopment\apache-maven-3.6.1\mvn_resp" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\JavaDevelopment\apache-maven-3.6.1\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="QODANA_PROBLEMS_VIEW_TAB" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2iPG5h7WbXYWn2LkUu8QdzpyH3k" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.AesEncryptUtil.executor&quot;: &quot;Run&quot;,
    &quot;Application.CodeGenerator (1).executor&quot;: &quot;Run&quot;,
    &quot;Application.CodeGenerator.executor&quot;: &quot;Run&quot;,
    &quot;Application.weekToDateStringTest.executor&quot;: &quot;Run&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder0&quot;: &quot;0&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder1&quot;: &quot;1&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder2&quot;: &quot;2&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder3&quot;: &quot;3&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder4&quot;: &quot;4&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder5&quot;: &quot;5&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth0&quot;: &quot;265&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth1&quot;: &quot;265&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth2&quot;: &quot;264&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth3&quot;: &quot;264&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth4&quot;: &quot;264&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth5&quot;: &quot;264&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder0&quot;: &quot;0&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder1&quot;: &quot;1&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder2&quot;: &quot;2&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder3&quot;: &quot;3&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder4&quot;: &quot;4&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder5&quot;: &quot;5&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth0&quot;: &quot;265&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth1&quot;: &quot;265&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth2&quot;: &quot;264&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth3&quot;: &quot;264&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth4&quot;: &quot;264&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth5&quot;: &quot;264&quot;,
    &quot;Maven.qz-activiti [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.qz-manager [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.qz-operation [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.qz-root [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.qz-root [org.apache.maven.plugins:maven-clean-plugin:2.5:clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.qz-root [package].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.ActivitiApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.ConditionMaintenanceApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.EurekaApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.IscApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.MangerApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.OperationApplication.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.RepairScheduleApplication.executor&quot;: &quot;Run&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;junie.onboarding.icon.badge.shown&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/Shammpool/work/code/dgyt/01代码/qz-root/qz-operation/src/main/resources/docx/czp&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Global Libraries&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;run.code.analysis.last.selected.profile&quot;: &quot;pProject Default&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;settings.editor.splitter.proportion&quot;: &quot;0.21158691&quot;,
    &quot;spring.configuration.checksum&quot;: &quot;ad30cf175f1d438082ce4a4410164080&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ],
    &quot;com.intellij.ide.scratch.LRUPopupBuilder$1/SQL Dialect&quot;: [
      &quot;MySQL&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\Shammpool\work\code\dgyt\01代码\qz-root\qz-operation\src\main\resources\docx\czp" />
      <recent name="D:\Shammpool\work\code\dgyt\01代码\qz-root\qz-operation\src\main\resources\mapper\gf" />
      <recent name="D:\Shammpool\work\code\dgyt\01代码\qz-root\qz-operation\src\main\resources\docx\gzp" />
      <recent name="D:\Shammpool\work\code\dgyt\01代码\qz-root\qz-operation\src\main\java\com\qz\entity\gfgzrz" />
      <recent name="D:\Shammpool\work\code\dgyt\01代码\qz-root\qz-operation\src\main\java\com\qz\entity" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.qz.controller.gf" />
      <recent name="com.qz.service.impl.gf" />
      <recent name="com.qz.service.gf" />
      <recent name="com.qz.mapper.gf" />
      <recent name="com.qz.entity.gf" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Application.CodeGenerator">
    <configuration name="AesEncryptUtil" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.qz.utils.AesEncryptUtil" />
      <module name="qz-base-common" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.qz.utils.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CodeGenerator (1)" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.qz.generator.CodeGenerator" />
      <module name="qz-manager" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.qz.generator.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CodeGenerator" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.qz.generator.CodeGenerator" />
      <module name="qz-operation" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.qz.generator.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="weekToDateStringTest" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.qz.weekToDateStringTest" />
      <module name="qz-repair-schedule" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.qz.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ActivitiApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ACTIVE_PROFILES" />
      <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <module name="qz-activiti" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.qz.ActivitiApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ConditionMaintenanceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ACTIVE_PROFILES" />
      <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <module name="qz-condition-maintenance" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.qz.ConditionMaintenanceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="EurekaApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ACTIVE_PROFILES" />
      <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <module name="qz-eureka" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.qz.EurekaApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="IscApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ACTIVE_PROFILES" />
      <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <module name="qz-isc" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.qz.IscApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="MangerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ACTIVE_PROFILES" />
      <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <module name="qz-manager" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.qz.MangerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="OperationApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ACTIVE_PROFILES" />
      <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <module name="qz-operation" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.qz.OperationApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="RepairScheduleApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ACTIVE_PROFILES" />
      <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <module name="qz-repair-schedule" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.qz.RepairScheduleApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="ACTIVE_PROFILES" />
      <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <module name="qz-activiti" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.qz.ActivitiApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Application.AesEncryptUtil" />
      <item itemvalue="Application.CodeGenerator (1)" />
      <item itemvalue="Application.CodeGenerator" />
      <item itemvalue="Application.weekToDateStringTest" />
      <item itemvalue="Spring Boot.ActivitiApplication" />
      <item itemvalue="Spring Boot.ConditionMaintenanceApplication" />
      <item itemvalue="Spring Boot.EurekaApplication" />
      <item itemvalue="Spring Boot.IscApplication" />
      <item itemvalue="Spring Boot.MangerApplication" />
      <item itemvalue="Spring Boot.OperationApplication" />
      <item itemvalue="Spring Boot.RepairScheduleApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Application.weekToDateStringTest" />
        <item itemvalue="Application.CodeGenerator" />
        <item itemvalue="Application.CodeGenerator (1)" />
        <item itemvalue="Application.AesEncryptUtil" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="StructureViewState">
    <option name="selectedTab" value="Logical" />
  </component>
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
  </component>
  <component name="SvnFileUrlMappingImpl">
    <option name="myMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Shammpool\work\code\dgyt\01代码\qz-root" />
          <option name="myCopyRoot" value="D:\Shammpool\work\code\dgyt\01代码\qz-root" />
        </SvnCopyRootSimple>
      </list>
    </option>
    <option name="myMoreRealMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="D:\Shammpool\work\code\dgyt\01代码\qz-root" />
          <option name="myCopyRoot" value="D:\Shammpool\work\code\dgyt\01代码\qz-root" />
        </SvnCopyRootSimple>
      </list>
    </option>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="7adfa91c-0b44-4413-8ea0-c966f63e158b" name="Changes" comment="" />
      <created>1719384631039</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1719384631039</updated>
      <workItem from="1719384632439" duration="105000" />
      <workItem from="1719384766641" duration="29340000" />
      <workItem from="1719626898194" duration="14261000" />
      <workItem from="1719904553077" duration="12811000" />
      <workItem from="1720070821360" duration="11450000" />
      <workItem from="1720507955718" duration="813000" />
      <workItem from="1720578874416" duration="1314000" />
      <workItem from="1720591052960" duration="11606000" />
      <workItem from="1721381893512" duration="34000" />
      <workItem from="1722758092548" duration="4571000" />
      <workItem from="1722780223827" duration="1024000" />
      <workItem from="1722864333559" duration="9000" />
      <workItem from="1723102218023" duration="624000" />
      <workItem from="1724321613247" duration="1761000" />
      <workItem from="1724502879064" duration="209000" />
      <workItem from="1724507271748" duration="3197000" />
      <workItem from="1724655832502" duration="12215000" />
      <workItem from="1724724082481" duration="2215000" />
      <workItem from="1724817400861" duration="48000" />
      <workItem from="1725858961153" duration="2248000" />
      <workItem from="1725862598038" duration="225000" />
      <workItem from="1725864050168" duration="2286000" />
      <workItem from="1725866542589" duration="351000" />
      <workItem from="1725866908576" duration="1494000" />
      <workItem from="1725868420339" duration="2100000" />
      <workItem from="1725870547689" duration="763000" />
      <workItem from="1725871326646" duration="187000" />
      <workItem from="1725871522400" duration="1001000" />
      <workItem from="1725873100057" duration="4116000" />
      <workItem from="1726036231176" duration="1882000" />
      <workItem from="1726038146147" duration="1714000" />
      <workItem from="1726041013810" duration="515000" />
      <workItem from="1726041541759" duration="8000" />
      <workItem from="1726041555245" duration="207000" />
      <workItem from="1726041845472" duration="924000" />
      <workItem from="1726042786420" duration="782000" />
      <workItem from="1726043584245" duration="16633000" />
      <workItem from="1726162165302" duration="2575000" />
      <workItem from="1726413451688" duration="3834000" />
      <workItem from="1727356380116" duration="2924000" />
      <workItem from="1727402115903" duration="18107000" />
      <workItem from="1727538997044" duration="242000" />
      <workItem from="1727539268154" duration="198000" />
      <workItem from="1727539477637" duration="282000" />
      <workItem from="1727539770498" duration="729000" />
      <workItem from="1727568757784" duration="2529000" />
      <workItem from="1727581147626" duration="8047000" />
      <workItem from="1727658659890" duration="3682000" />
      <workItem from="1728347805425" duration="2385000" />
      <workItem from="1728359438093" duration="16592000" />
      <workItem from="1728519671144" duration="9730000" />
      <workItem from="1728635390974" duration="6035000" />
      <workItem from="1728810303601" duration="7393000" />
      <workItem from="1728985573804" duration="11764000" />
      <workItem from="1729174575975" duration="7243000" />
      <workItem from="1729235247238" duration="13064000" />
      <workItem from="1729673219055" duration="2638000" />
      <workItem from="1729761348168" duration="12525000" />
      <workItem from="1729858060407" duration="622000" />
      <workItem from="1729922557081" duration="8262000" />
      <workItem from="1730098993944" duration="14496000" />
      <workItem from="1730216378355" duration="811000" />
      <workItem from="1730285473196" duration="3596000" />
      <workItem from="1730447871140" duration="2231000" />
      <workItem from="1730455692474" duration="785000" />
      <workItem from="1730544694853" duration="19000" />
      <workItem from="1730714146494" duration="6042000" />
      <workItem from="1730862724785" duration="6438000" />
      <workItem from="1731645868823" duration="13890000" />
      <workItem from="1732019481734" duration="16285000" />
      <workItem from="1732260156687" duration="6670000" />
      <workItem from="1732278712774" duration="1104000" />
      <workItem from="1732279830813" duration="293000" />
      <workItem from="1732281069805" duration="500000" />
      <workItem from="1733123949277" duration="15014000" />
      <workItem from="1733391144361" duration="1194000" />
      <workItem from="1733482011794" duration="10523000" />
      <workItem from="1733842303733" duration="2094000" />
      <workItem from="1733860362371" duration="4461000" />
      <workItem from="1734984240065" duration="2428000" />
      <workItem from="1735096764406" duration="14000" />
      <workItem from="1735864262788" duration="699000" />
      <workItem from="1736829261194" duration="9735000" />
      <workItem from="1737309172080" duration="8202000" />
      <workItem from="1737359745922" duration="5333000" />
      <workItem from="1737444684941" duration="14508000" />
      <workItem from="1737468615482" duration="434000" />
      <workItem from="1739074884421" duration="502000" />
      <workItem from="1739240345100" duration="288000" />
      <workItem from="1739290275895" duration="1726000" />
      <workItem from="1739797785889" duration="11106000" />
      <workItem from="1740570818015" duration="7580000" />
      <workItem from="1740654612533" duration="60485000" />
      <workItem from="1741176043324" duration="5648000" />
      <workItem from="1741437894964" duration="2034000" />
      <workItem from="1741527163802" duration="226000" />
      <workItem from="1741693439232" duration="5463000" />
      <workItem from="1741868524882" duration="7363000" />
      <workItem from="1741940382083" duration="2152000" />
      <workItem from="1742373897077" duration="8707000" />
      <workItem from="1742410632072" duration="1624000" />
      <workItem from="1742463291723" duration="10251000" />
      <workItem from="1742547507343" duration="3817000" />
      <workItem from="1742713662677" duration="3902000" />
      <workItem from="1742729111839" duration="3519000" />
      <workItem from="1742790571840" duration="1341000" />
      <workItem from="1742830955583" duration="457000" />
      <workItem from="1744287225149" duration="23636000" />
      <workItem from="1744377468548" duration="43000" />
      <workItem from="1744606636723" duration="6947000" />
      <workItem from="1744701583381" duration="3771000" />
      <workItem from="1744769580952" duration="1582000" />
      <workItem from="1744782268270" duration="7000" />
      <workItem from="1744976678970" duration="3408000" />
      <workItem from="1745669956257" duration="1452000" />
      <workItem from="1745744187494" duration="7122000" />
      <workItem from="1745912122501" duration="10425000" />
      <workItem from="1747042383315" duration="5012000" />
      <workItem from="1747159079582" duration="309000" />
      <workItem from="1747159403131" duration="294000" />
      <workItem from="1747159712119" duration="488000" />
      <workItem from="1747160213456" duration="4256000" />
      <workItem from="1747215173639" duration="11943000" />
      <workItem from="1747661715336" duration="33267000" />
      <workItem from="1748402905864" duration="3985000" />
      <workItem from="1748419821515" duration="770000" />
      <workItem from="1748569421160" duration="10613000" />
      <workItem from="1749005214215" duration="18058000" />
      <workItem from="1749542938820" duration="13850000" />
      <workItem from="1749572734030" duration="142000" />
      <workItem from="1749620471533" duration="3440000" />
      <workItem from="1749637295549" duration="1932000" />
      <workItem from="1750579401896" duration="56000" />
      <workItem from="1750665382847" duration="184000" />
      <workItem from="1750665579057" duration="15793000" />
      <workItem from="1751366088551" duration="8865000" />
      <workItem from="1751379224168" duration="724000" />
      <workItem from="1752053584599" duration="4342000" />
      <workItem from="1752461774037" duration="5726000" />
      <workItem from="1755528195034" duration="2467000" />
    </task>
    <task id="LOCAL-00001" summary="变电交接班新增导出功能">
      <created>1719482605267</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1719482605267</updated>
    </task>
    <task id="LOCAL-00002" summary="避雷器、不良工况、变电设备表单、变电三级评价报告">
      <created>1719575603081</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1719575603081</updated>
    </task>
    <task id="LOCAL-00003" summary="三级评价报告、fgsmc都改为从dept表中获取、检修计划任务的导出导入、线路接地电阻测量子表排序">
      <created>1719667745283</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1719667745283</updated>
    </task>
    <task id="LOCAL-00004" summary="不良工况表单-修改后兼容设备异常新增&#10;操作票新增作废功能">
      <created>1719920707837</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1719920707837</updated>
    </task>
    <task id="LOCAL-00005" summary="变电电网资源-设备表单新增字段&#10;解决不良工况导致的变电运维工作日志保存错误">
      <created>1720087895002</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1720087895002</updated>
    </task>
    <task id="LOCAL-00006" summary="变电电网资源-导出api修改">
      <created>1722767575457</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1722767575457</updated>
    </task>
    <task id="LOCAL-00007" summary="流程变量改枚举">
      <created>1725860373077</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1725860373077</updated>
    </task>
    <task id="LOCAL-00008" summary="流程变量改枚举">
      <created>1725861050799</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1725861050799</updated>
    </task>
    <task id="LOCAL-00009" summary="变电三级评价、设备类型等">
      <option name="closed" value="true" />
      <created>1727431961090</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1727431961090</updated>
    </task>
    <task id="LOCAL-00010" summary="fix：其他问题录入-表单新增图片上传及问题处理情况、修改变电三级评价带出条件；activity文件上传失败">
      <option name="closed" value="true" />
      <created>1728558243620</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1728558243620</updated>
    </task>
    <task id="LOCAL-00011" summary="fix：其他问题录入等三级评价内容带出">
      <option name="closed" value="true" />
      <created>1729183887439</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1729183887439</updated>
    </task>
    <task id="LOCAL-00012" summary="add:统一身份认证接入">
      <option name="closed" value="true" />
      <created>1729769310243</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1729769310243</updated>
    </task>
    <task id="LOCAL-00013" summary="add：光伏设备管理server">
      <option name="closed" value="true" />
      <created>1730105758662</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1730105758662</updated>
    </task>
    <task id="LOCAL-00014" summary="add：光伏隐患标准库server">
      <option name="closed" value="true" />
      <created>1730116941401</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1730116941401</updated>
    </task>
    <task id="LOCAL-00015" summary="add：光伏gzp、czp server">
      <option name="closed" value="true" />
      <created>1730714499436</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1730714499436</updated>
    </task>
    <task id="LOCAL-00016" summary="fix：光伏czp关联ml修改与haojie一致 server">
      <option name="closed" value="true" />
      <created>1730783932563</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1730783932563</updated>
    </task>
    <task id="LOCAL-00017" summary="fix：光伏功能测试 server">
      <option name="closed" value="true" />
      <created>1730872185565</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1730872185565</updated>
    </task>
    <task id="LOCAL-00018" summary="opt: server端添加debug日志文件记录">
      <option name="closed" value="true" />
      <created>1733124034551</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1733124034551</updated>
    </task>
    <task id="LOCAL-00019" summary="opt: server 主要光伏功能修改">
      <option name="closed" value="true" />
      <created>1736829385353</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1736829385353</updated>
    </task>
    <task id="LOCAL-00020" summary="opt: server 线路专项记录-接地电阻测试改子表">
      <option name="closed" value="true" />
      <created>1737378666893</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1737378666893</updated>
    </task>
    <task id="LOCAL-00021" summary="opt: server 光伏台账新增整站设备导入功能">
      <option name="closed" value="true" />
      <created>1737469041413</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1737469041413</updated>
    </task>
    <task id="LOCAL-00022" summary="opt: server 接地电阻测试-导入导出功能修改">
      <option name="closed" value="true" />
      <created>1739821755980</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1739821755980</updated>
    </task>
    <task id="LOCAL-00023" summary="opt: server 代码安全优化">
      <option name="closed" value="true" />
      <created>1741180418652</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1741180418652</updated>
    </task>
    <task id="LOCAL-00024" summary="opt: server 获取分公司接口新增部门类型">
      <option name="closed" value="true" />
      <created>1741884289010</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1741884289010</updated>
    </task>
    <task id="LOCAL-00025" summary="opt: server 登录逻辑去除统一认证标识">
      <option name="closed" value="true" />
      <created>1742412810020</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1742412810020</updated>
    </task>
    <task id="LOCAL-00026" summary="fix：front-end 检修计划汇总日期延后一个周">
      <option name="closed" value="true" />
      <created>1742831218183</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1742831218183</updated>
    </task>
    <task id="LOCAL-00027" summary="opt：back-end：首次在工作流中自定义任务触发器，实现光伏隐患在绿电发展审批通过时，发给生产科通知">
      <option name="closed" value="true" />
      <created>1744607022016</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1744607022016</updated>
    </task>
    <task id="LOCAL-00028" summary="1新增光伏日常工作记录及设备运维记录&#10;2调整部分docx">
      <option name="closed" value="true" />
      <created>1747249529233</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1747249529233</updated>
    </task>
    <task id="LOCAL-00029" summary="1去除多余事务注解&#10;2新增光伏日常工作及设备运维">
      <option name="closed" value="true" />
      <created>1748410535857</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1748410535857</updated>
    </task>
    <task id="LOCAL-00030" summary="backend 操作票功能优化">
      <option name="closed" value="true" />
      <created>1748609389142</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1748609389142</updated>
    </task>
    <task id="LOCAL-00031" summary="backend 光伏新增操作票查询统计">
      <option name="closed" value="true" />
      <created>1749543040588</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1749543040588</updated>
    </task>
    <task id="LOCAL-00032" summary="backend 光伏隐患调整及流程人员选择改分组">
      <option name="closed" value="true" />
      <created>1749572321348</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1749572321348</updated>
    </task>
    <task id="LOCAL-00033" summary="backend 光伏电网资源日期解析问题处理">
      <option name="closed" value="true" />
      <created>1749572866832</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1749572866832</updated>
    </task>
    <task id="LOCAL-00034" summary="backend-光伏典型操作票新增导入功能">
      <option name="closed" value="true" />
      <created>1750696306732</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1750696306732</updated>
    </task>
    <task id="LOCAL-00035" summary="backend-光伏新增防误触装置解锁功能">
      <option name="closed" value="true" />
      <created>1751375348150</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1751375348150</updated>
    </task>
    <task id="LOCAL-00036" summary="backend-光伏操作票文档编码修改">
      <option name="closed" value="true" />
      <created>1752468880332</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1752468880332</updated>
    </task>
    <task id="LOCAL-00037" summary="backend-用户管理-修改部门时，不在清除用户的权限">
      <option name="closed" value="true" />
      <created>1752488150496</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1752488150496</updated>
    </task>
    <option name="localTasksCounter" value="38" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-core" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-websocket" />
    <option featureType="dependencySupport" implementationName="java:io.projectreactor:reactor-core" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.data:spring-data-commons" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.cloud:spring-cloud-context" />
    <option featureType="dependencySupport" implementationName="java:org.hibernate.validator:hibernate-validator" />
    <option featureType="dependencySupport" implementationName="java:org.thymeleaf:thymeleaf" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-messaging" />
    <option featureType="dependencySupport" implementationName="java:javax.validation:validation-api" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.boot:spring-boot" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-webmvc" />
    <option featureType="dependencySupport" implementationName="java:org.projectlombok:lombok" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="fix：光伏czp关联ml修改与haojie一致 server" />
    <MESSAGE value="fix：光伏功能测试 server" />
    <MESSAGE value="add:运维工作日志 server" />
    <MESSAGE value="opt: server端添加debug日志文件记录" />
    <MESSAGE value="opt: server 主要光伏功能修改" />
    <MESSAGE value="opt: server 线路专项记录-接地电阻测试改子表" />
    <MESSAGE value="opt: server 光伏台账新增" />
    <MESSAGE value="opt: server 光伏台账新增整站设备导入功能" />
    <MESSAGE value="opt: server 接地电阻测试-导入导出功能修改" />
    <MESSAGE value="opt: server 代码" />
    <MESSAGE value="opt: server 代码安全优化" />
    <MESSAGE value="opt: server 获取分公司接口新增部门类型" />
    <MESSAGE value="opt: server 登录逻辑去除统一认证标识" />
    <MESSAGE value="fix：front-end 检修计划汇总日期延后一个周" />
    <MESSAGE value="opt：back-end：首次在工作流中自定义任务触发器，实现光伏隐患在绿电发展审批通过时，发给生产科通知" />
    <MESSAGE value="1新增光伏日常工作记录及设备运维记录&#10;2调整部分docx" />
    <MESSAGE value="1去除多余事务注解&#10;2新增光伏日常工作及设备运维" />
    <MESSAGE value="backend 操作票功能优化" />
    <MESSAGE value="backend 光伏新增操作票查询统计" />
    <MESSAGE value="backend 光伏隐患调整及流程人员选择改分组" />
    <MESSAGE value="backend 光伏电网资源日期解析问题处理" />
    <MESSAGE value="backend-光伏典型操作票新增导入功能" />
    <MESSAGE value="backend-光伏新增防误触装置解锁功能" />
    <MESSAGE value="backend-光伏操作票文档编码修改" />
    <MESSAGE value="backend-用户管理-修改部门时，不在清除用户的权限" />
    <option name="LAST_COMMIT_MESSAGE" value="backend-用户管理-修改部门时，不在清除用户的权限" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/qz-operation/src/main/java/com/qz/service/impl/bd/YxBdjjbServiceImpl.java</url>
          <line>493</line>
          <option name="timeStamp" value="5" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/qz-operation/src/main/java/com/qz/controller/sd/YxGhglkgController.java</url>
          <line>43</line>
          <option name="timeStamp" value="7" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="SpringBootApplicationConfigurationType">
        <watch expression="工作票管理" language="JAVA" />
      </configuration>
    </watches-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>