{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\sybgjlcomment\\sysbSelectedbg.vue?vue&type=style&index=0&id=9933812e&scoped=true&lang=css&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\sybgjlcomment\\sysbSelectedbg.vue", "mtime": 1706897323436}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmFzc2V0LXNlbGVjdC1kaWFsb2cgewogIG1hcmdpbi10b3A6IDEwdmg7Cn0KCi5lbC1tYWluIHsKICBwYWRkaW5nOiA4dmg7CiAgbWFyZ2luLXRvcDogLTh2aDsKfQoKLyog6K6+572u5rua5Yqo5p2h55qE5qC35byPICovCjo6LXdlYmtpdC1zY3JvbGxiYXIgewogIHdpZHRoOiAxMnB4Owp9CgovKiDmu5rliqjmp70gKi8KOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7Ci8vLXdlYmtpdC1ib3gtc2hhZG93OiBpbnNldDAwNnB4cmdiYSgwLCAwLCAwLCAwLjMpOwogIGJvcmRlci1yYWRpdXM6IDEwcHg7Cn0KCi8qIOa7muWKqOadoea7keWdlyAqLwo6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHsKICBib3JkZXItcmFkaXVzOiAxMHB4OwogIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC4xKTsKLy8td2Via2l0LWJveC1zaGFkb3c6IGdiYSgwLCAwLCAwLCAwLjUpOwp9Cgo6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iOndpbmRvdy1pbmFjdGl2ZSB7CiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjEpOwp9Cgo6Oi13ZWJraXQtc2Nyb2xsYmFyIHsKICB3aWR0aDogOHB4Owp9CgouaXRlbSB7CiAgd2lkdGg6IDIyNXB4OwogIGZsb2F0OiBsZWZ0Owp9CgovZGVlcC8gLmVsLWRpYWxvZ19fYm9keSB7CiAgcGFkZGluZzogMHB4ICFpbXBvcnRhbnQ7Cn0KCi9kZWVwLyBhc2lkZSB7CgogIHBhZGRpbmc6IDRweCA4cHggIWltcG9ydGFudDsKCn0KCg=="}, {"version": 3, "sources": ["sysbSelectedbg.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqQA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;;AAEA", "file": "sysbSelectedbg.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk/sybgjlcomment", "sourcesContent": ["<template>\n  <div>\n    <!--    <el-container style=\"height: 50vh; border: 1px solid #eee\">-->\n    <el-row>\n      <el-col :span=\"6\">\n        <el-aside width=\"15vw\" style=\"background-color: rgb(238, 241, 246)\">\n          <el-tree :data=\"treeData\" :props=\"defaultProps\" @node-click=\"handleNodeClick\"\n                   style=\"line-height: 2vh;height: 47vh; padding:10px;\"></el-tree>\n        </el-aside>\n      </el-col>\n      <el-col :span=\"18\">\n        <el-container>\n          <el-main>\n            <el-table :data=\"tableData\" @selection-change=\"handleSelectionChange\" ref=\"sbTable\" highlight-current-row  \n            style=\"max-height: 400px;overflow: hidden;overflow-y: scroll;\"\n            \n            >\n              <el-table-column\n                type=\"selection\"\n                width=\"55\">\n              </el-table-column>\n              <el-table-column prop=\"sbmc\" label=\"设备\"></el-table-column>\n              <el-table-column prop=\"sblxmc\" label=\"设备类型\"></el-table-column>\n              <el-table-column prop=\"tyrq\" label=\"投运日期\"></el-table-column>\n              <el-table-column prop=\"sbzt\" label=\"状态\"></el-table-column>\n            </el-table>\n            <!-- <pagination\n              :total=\"sbTotal\"\n              :page.sync=\"querySyParam.pageNum\"\n              :limit.sync=\"querySyParam.pageSize\"\n              @pagination=\"getSbDataByTreeNode\"\n            /> -->\n          </el-main>\n        </el-container>\n      </el-col>\n    </el-row>\n    <!--    </el-container>-->\n    <div style=\"text-align: right;margin-top: 2vh\">\n      <el-button @click=\"cancel\">取 消</el-button>\n      <el-button type=\"primary\" @click=\"save\">确 定</el-button>\n    </div>\n  </div>\n\n  <!--      </el-dialog>-->\n</template>\n\n<script>\n  import {getTreeList} from '@/api/component/assetselect'\n  import {getSbDataByTreeNode} from \"@/api/dagangOilfield/bzgl/sybglr\";\n\n\n  export default {\n    name: 'assetSelect',\n    props: {\n      selectedSbParam:{\n      type:Object,\n        default: () => ({\n          lx:'bd',  //变电\n          sbmc:'',\n          fsss:'',\n        })\n      }\n    },\n    data() {\n\n      return {\n        //查询设备参数\n        querySyParam: {\n          treeNodeParam: {},\n          pageNum: 1,\n          pageSize: 1000\n        },\n        //设备数量\n        sbTotal: 0,\n        tableData: [],\n        //树数据\n        treeData: [],\n        defaultProps: {\n          children: 'children',\n          label: 'label'\n        },\n        //设备列表选中后\n        assetInfo: [],\n        sblxbms:[],\n\n      }\n    },\n    created() {\n    },\n    mounted() {\n      //获取树结构\n      this.getTreeInfoList();\n    },\n    methods: {\n      //取消按钮\n      cancel() {\n        //关闭弹窗\n        this.$emit(\"closeSysbSelectDialog\",false);\n      },\n      //确认按钮\n      save() {\n        console.log(\"save保存\",this.assetInfo);\n        console.log(\"save保存\",this.assetInfo.length);\n       if (this.assetInfo != undefined &&  this.assetInfo.length > 0){\n         //发送数据\n         this.$emit(\"handleAcceptSbData\",this.assetInfo);\n         //关闭弹窗\n         this.$emit(\"closeSysbSelectDialog\",false);\n       }else {\n        console.log(\"save推出\");\n       }\n\n      },\n      \n      //多行选择数据\n     handleSelectionChange(rows){\n        this.assetInfo=[];\n        console.log(\"rows\",rows);\n       //输电\n      if(this.selectedSbParam.lx==\"sd\"){\n        if (rows.length > 1) {\n          this.$message.warning(\"只能选中一条数据！！！\")\n          this.$refs.sbTable.clearSelection();\n        }else{\n          this.assetInfo.push(rows[0]);\n        }\n      }\n\n        //配电\n        if(this.selectedSbParam.lx==\"pd\"){\n        if (rows.length > 1) {\n          this.$message.warning(\"只能选中一条数据！！！\")\n          this.$refs.sbTable.clearSelection();\n        }else{\n          this.assetInfo.push(rows[0]);\n        }\n      }\n\n      //变电\n      if(this.selectedSbParam.lx==\"bd\"){\n        if(rows.length< 4){\n          this.sblxbms=[];\n          rows.forEach(item => {\n            let assetD=item;\n            this.assetInfo.push(assetD);\n            console.log(\"item\",item.sblxbm);\n            this.sblxbms.push(item.sblxbm);\n          });\n          let b=0;\n          let sblx=this.sblxbms\n          for(var i=0;i<sblx.length;i++){\n              if(sblx[i]!==sblx[0]){\n                b++;\n              }\n          }\n         if(b!==0){\n          this.assetInfo=[];\n            this.$message.warning(\"只能选择同一设备下的3条数据！！！\")\n            this.$refs.sbTable.clearSelection();\n          }\n        }else if(rows.length==1){\n          console.log(\"1进入\");\n          this.assetInfo.push(rows[0]);\n        }else{\n          this.$message.warning(\"只能选择同一设备下的3条数据！！！\")\n          //清空表格多选框\n          this.$refs.sbTable.clearSelection();\n        }\n      // if(rows.length >1 && rows.length< 4){\n      //      rows.forEach(item => {\n      //       console.log(\"item\",item.xb);\n      //       console.log(\"item\",item);\n      //         if(item.xb!=\"A\" && item.xb!=\"B\" && item.xb!=\"C\" ){\n      //           this.$message.warning(\"只有A、B、C三相才能多选！！！\")\n      //             //清空选中方法\n      //         if (rows) {\n      //             rows.forEach(row => {\n      //               this.$refs.sbTable.toggleRowSelection(row);\n      //             });\n      //           } else {\n      //             this.$refs.sbTable.clearSelection();\n      //           }\n      //     }else if(this.sblxbm!=\"\" && this.sblxbm!=item.sblxbm){\n      //         console.log(\"进入\");\n      //         this.$message.warning(\"请选择同一设备类型设备！！！\")\n      //             //清空选中方法\n      //         if (rows) {\n      //             rows.forEach(row => {\n      //               this.$refs.sbTable.toggleRowSelection(row);\n      //             });\n      //           } else {\n      //             this.$refs.sbTable.clearSelection();\n      //           }\n      //      }else{\n      //       this.sblxbm=item.sblxbm;\n      //       console.log(\"item.sblxbm\",item.sblxbm)\n      //       console.log(\"item\",item)\n      //       let assetD=item;\n      //       this.assetInfo.push(assetD);\n      //       console.log(\"this.assetInfo\",this.assetInfo);\n      //     }\n      //   });\n      // }\n    //  if(rows.length==1){\n    //     this.assetInfo.push(rows[0]);\n    //     console.log(\"this.assetInfo[0]\",rows[0]);\n    //     console.log(\"this.assetInfo[1]\",this.assetInfo.length);\n    //  };\n    //  if(rows.length> 3){\n    //   this.$message.warning(\"不能多选！！！\")\n    //     //清空选中方法\n    //     if (rows) {\n    //         rows.forEach(row => {\n    //           this.$refs.sbTable.toggleRowSelection(row);\n    //         });\n    //       } else {\n    //         this.$refs.sbTable.clearSelection();\n    //       }\n    //  }\n    }\n\n    },\n\n      //树点击方法\n      handleNodeClick(treeNode) {\n        if (treeNode.bsf != undefined) {\n          this.querySyParam.treeNodeParam = treeNode;\n          this.querySyParam.treeNodeParam.fsss=this.selectedSbParam.fsss;\n          this.getSbDataByTreeNode();\n        }\n      },\n      //获取具体设备\n      getSbDataByTreeNode() {\n        getSbDataByTreeNode(this.querySyParam).then(res => {\n          console.log(\"this.querySyParam，\",this.querySyParam)\n          console.log(\"res\",res)\n\n          this.tableData = res.data.records\n          this.sbTotal = res.data.total\n        })\n      },\n\n      //获取左侧设备树\n      getTreeInfoList() {\n        console.log(this.selectedSbParam + \"获取设备\")\n        getTreeList(this.selectedSbParam).then(res => {\n          console.log(\"树结构数据:\",res)\n          this.treeData = res.data;\n        });\n      },\n      getAssetListInfo() {\n        // this.tableData = null;\n        // listAsset(this.query).then(res => {\n        //   this.tableData = res.data;\n        // })\n      }\n\n    },\n  }\n</script>\n<style scoped>\n  .asset-select-dialog {\n    margin-top: 10vh;\n  }\n\n  .el-main {\n    padding: 8vh;\n    margin-top: -8vh;\n  }\n\n  /* 设置滚动条的样式 */\n  ::-webkit-scrollbar {\n    width: 12px;\n  }\n\n  /* 滚动槽 */\n  ::-webkit-scrollbar-track {\n  //-webkit-box-shadow: inset006pxrgba(0, 0, 0, 0.3);\n    border-radius: 10px;\n  }\n\n  /* 滚动条滑块 */\n  ::-webkit-scrollbar-thumb {\n    border-radius: 10px;\n    background: rgba(0, 0, 0, 0.1);\n  //-webkit-box-shadow: gba(0, 0, 0, 0.5);\n  }\n\n  ::-webkit-scrollbar-thumb:window-inactive {\n    background: rgba(0, 0, 0, 0.1);\n  }\n\n  ::-webkit-scrollbar {\n    width: 8px;\n  }\n\n  .item {\n    width: 225px;\n    float: left;\n  }\n\n  /deep/ .el-dialog__body {\n    padding: 0px !important;\n  }\n\n  /deep/ aside {\n\n    padding: 4px 8px !important;\n\n  }\n\n</style>\n"]}]}