{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\asset\\powercheck.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\asset\\powercheck.js", "mtime": 1706897313905}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0TGlzdCA9IGdldExpc3Q7CmV4cG9ydHMuc2F2ZU9yVXBkYXRlID0gc2F2ZU9yVXBkYXRlOwpleHBvcnRzLmV4YW1pbmUgPSBleGFtaW5lOwpleHBvcnRzLnJlbW92ZUJhdGNoID0gcmVtb3ZlQmF0Y2g7Cgp2YXIgX3JlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdXRpbHMvcmVxdWVzdCIpKTsKCnZhciBiYXNlVXJsID0gIi9tYW5hZ2VyLWFwaSI7IC8vIOafpeivogoKZnVuY3Rpb24gZ2V0TGlzdChwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy90b3dlci9jaGVjay9wYWdlJywgcGFyYW1zLCAxKTsKfSAvLyDmt7vliqDmiJbkv67mlLkKCgpmdW5jdGlvbiBzYXZlT3JVcGRhdGUocGFyYW1zKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcvdG93ZXIvY2hlY2svYWRkJywgcGFyYW1zLCAxKTsKfSAvLyDlrqHmoLgKCgpmdW5jdGlvbiBleGFtaW5lKHBhcmFtcykgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3RQb3N0KGJhc2VVcmwgKyAnL3Rvd2VyL2NoZWNrL2V4YW1pbmUnLCBwYXJhbXMsIDEpOwp9IC8vIOWuoeaguAoKCmZ1bmN0aW9uIHJlbW92ZUJhdGNoKHBhcmFtcykgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3RQb3N0KGJhc2VVcmwgKyAnL3Rvd2VyL2NoZWNrL3JlbW92ZUJhdGNoJywgcGFyYW1zLCAxKTsKfQ=="}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagangOilfield/asset/powercheck.js"], "names": ["baseUrl", "getList", "params", "api", "requestPost", "saveOrUpdate", "examine", "removeBatch"], "mappings": ";;;;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,cAAhB,C,CAEA;;AACO,SAASC,OAAT,CAAiBC,MAAjB,EAAyB;AAC9B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,mBAAxB,EAA4CE,MAA5C,EAAmD,CAAnD,CAAP;AACD,C,CAED;;;AACO,SAASG,YAAT,CAAsBH,MAAtB,EAA8B;AACnC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,kBAAxB,EAA2CE,MAA3C,EAAkD,CAAlD,CAAP;AACD,C,CACD;;;AACO,SAASI,OAAT,CAAiBJ,MAAjB,EAAyB;AAC9B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,sBAAxB,EAA+CE,MAA/C,EAAsD,CAAtD,CAAP;AACD,C,CACD;;;AACO,SAASK,WAAT,CAAqBL,MAArB,EAA6B;AAClC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,0BAAxB,EAAmDE,MAAnD,EAA0D,CAA1D,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\nconst baseUrl = \"/manager-api\";\n\n// 查询\nexport function getList(params) {\n  return api.requestPost(baseUrl+'/tower/check/page',params,1)\n}\n\n// 添加或修改\nexport function saveOrUpdate(params) {\n  return api.requestPost(baseUrl+'/tower/check/add',params,1)\n}\n// 审核\nexport function examine(params) {\n  return api.requestPost(baseUrl+'/tower/check/examine',params,1)\n}\n// 审核\nexport function removeBatch(params) {\n  return api.requestPost(baseUrl+'/tower/check/removeBatch',params,1)\n}\n\n"]}]}