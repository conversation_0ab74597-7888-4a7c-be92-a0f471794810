{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\pdgl\\pdggl.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\pdgl\\pdggl.vue", "mtime": 1706897324785}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["pdggl.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAyTA;;AAGA;;AACA;;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA;AACA,MAAA,kBAAA,EAAA,EAFA;AAGA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,KAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA;AAFA,SADA;AAKA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA;AALA,OAHA;AAaA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AACA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,KAAA,EAAA,UAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA;AAMA;;;;;;AAMA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAZA,EAaA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAbA,EAcA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAdA,EAeA;AACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAhBA,EAiBA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAjBA,EAkBA;AACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA;AACA;;;;;;;;;;AASA;;;;;;;;;;;AA7BA;AAZA,OAbA;AAmEA,MAAA,wBAAA,EAAA,EAnEA;AAqEA,MAAA,oBAAA,EAAA,KArEA;AAsEA;AACA,MAAA,IAAA,EAAA,EAvEA;AAyEA,MAAA,OAAA,EAAA,KAzEA;AA0EA;AACA,MAAA,UAAA,EAAA,EA3EA;AA4EA;AACA,MAAA,WAAA,EAAA,EA7EA;AA8EA,MAAA,WAAA,EAAA,EA9EA;AA+EA;AACA,MAAA,cAAA,EAAA,IAhFA;AAiFA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,OAAA,EAAA,EAHA;AAIA,QAAA,QAAA,EAAA,EAJA;AAKA,QAAA,MAAA,EAAA;AALA,OAlFA;AAyFA,MAAA,UAAA,EAAA,IAzFA;AA2FA,MAAA,KAAA,EAAA;AACA;AACA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAFA;AAGA,QAAA,KAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAHA;AAIA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAJA;;AAKA;;;;;;AAMA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAXA;AAYA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAZA;AAaA,QAAA,KAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAbA;AAcA;AACA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAfA;AAgBA,QAAA,EAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,OAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAhBA;AAiBA;AACA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AACA;;;;;;;;;;AAnBA,OA3FA;AAyHA;AACA,MAAA,QAAA,EAAA,EA1HA;AA2HA,MAAA,WAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,IAAA;AACA,QAAA,KAAA,EAAA;AADA,OADA,EAGA;AAAA,QAAA,KAAA,EAAA,IAAA;AACA,QAAA,KAAA,EAAA;AADA,OAHA,EAKA;AAAA,QAAA,KAAA,EAAA,IAAA;AACA,QAAA,KAAA,EAAA;AADA,OALA,CA3HA;AAmIA,MAAA,SAAA,EAAA,EAnIA;AAoIA,MAAA,SAAA,EAAA,EApIA;AAqIA,MAAA,QAAA,EAAA,EArIA;AAsIA,MAAA,UAAA,EAAA,IAtIA;AAuIA,MAAA,cAAA,EAAA,EAvIA;AAwIA,MAAA,UAAA,EAAA;AAxIA,KAAA;AA0IA,GA7IA;AA8IA,EAAA,KAAA,EAAA,EA9IA;AA+IA,EAAA,OA/IA,qBA+IA;AACA,SAAA,OAAA,GAAA,IAAA,CADA,CAEA;;AACA,SAAA,YAAA;AACA,SAAA,uBAAA;AACA,SAAA,cAAA;AACA,SAAA,OAAA;AACA,SAAA,qBAAA;AACA,GAvJA;AAwJA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,qBAFA,mCAEA;AAAA;;AACA,wCAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,KAAA,CAAA,kBAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KANA;AAOA;AACA,IAAA,OARA,mBAQA,KARA,EAQA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,GADA,+DACA,MAAA,CAAA,MADA,GACA,KADA;AAAA;AAAA;AAAA,uBAGA,qBAAA,GAAA,CAHA;;AAAA;AAAA;AAGA,gBAAA,IAHA,qBAGA,IAHA;AAGA,gBAAA,IAHA,qBAGA,IAHA;;AAIA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AAPA;AAAA;;AAAA;AAAA;AAAA;AASA,gBAAA,OAAA,CAAA,GAAA;;AATA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,KApBA;AAqBA,IAAA,YArBA,0BAqBA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,2BAAA,SAAA,CADA;;AAAA;AAAA;AACA,gBAAA,OADA,yBACA,IADA;AAAA;AAAA,uBAEA,2BAAA,OAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,KAFA,0BAEA,IAFA;AAAA;AAAA,uBAGA,2BAAA,OAAA,CAHA;;AAAA;AAAA;AAGA,gBAAA,KAHA,0BAGA,IAHA;AAAA;AAAA,uBAIA,2BAAA,SAAA,CAJA;;AAAA;AAAA;AAIA,gBAAA,OAJA,0BAIA,IAJA;AAKA;AACA,gBAAA,MAAA,CAAA,SAAA,GAAA,KAAA;AACA,gBAAA,MAAA,CAAA,SAAA,GAAA,KAAA;AACA,gBAAA,MAAA,CAAA,QAAA,GAAA,OAAA;;AARA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,KA9BA;AA+BA,IAAA,cA/BA,4BA+BA;AAAA;;AACA,+BAAA,KAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KAnCA;AAoCA;AACA,IAAA,UArCA,sBAqCA,KArCA,EAqCA,IArCA,EAqCA;AACA,UAAA,CAAA,KAAA,EAAA,OAAA,IAAA;AACA,aAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,KAAA,MAAA,CAAA,CAAA;AACA,KAxCA;AAyCA;AACA,IAAA,kBA1CA,gCA0CA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,oBAAA,GAAA,IAAA;AACA,KA9CA;AA+CA;AACA,IAAA,gBAhDA,8BAgDA,CAEA,CAlDA;AAmDA,IAAA,YAnDA,wBAmDA,IAnDA,EAmDA;AACA,WAAA,GAAA,GAAA,IAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,KAAA;AAAA,OAAA,CAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KAtDA;AAwDA;AACA,IAAA,mBAzDA,iCAyDA,CAEA,CA3DA;AA4DA;AACA,IAAA,uBA7DA,qCA6DA;AAAA;;AACA,UAAA,QAAA,GAAA,MAAA;AACA,2CAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,wBAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KAlEA;AAmEA;AACA,IAAA,eApEA,2BAoEA,IApEA,EAoEA,CApEA,EAoEA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,cAAA,GAAA,IAAA;;AADA,sBAEA,IAAA,CAAA,UAAA,IAAA,GAFA;AAAA;AAAA;AAAA;;AAGA;AACA,gBAAA,MAAA,CAAA,kBAAA,GAAA,EAAA;AACA,gBAAA,MAAA,CAAA,kBAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA,gBAAA,MAAA,CAAA,kBAAA,CAAA,KAAA,GAAA,IAAA,CAAA,EAAA;AANA;AAAA,uBAOA,MAAA,CAAA,OAAA,CAAA;AAAA,kBAAA,IAAA,EAAA,IAAA,CAAA;AAAA,iBAAA,CAPA;;AAAA;AAAA;AAAA;;AAAA;AAAA,sBAQA,IAAA,CAAA,UAAA,IAAA,GARA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBASA,MAAA,CAAA,OAAA,CAAA;AAAA,kBAAA,KAAA,EAAA,IAAA,CAAA;AAAA,iBAAA,CATA;;AAAA;AAAA;AAAA,uBAUA,MAAA,CAAA,UAAA,CAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAVA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA,uBAYA,MAAA,CAAA,OAAA,EAZA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA,KAlFA;AAmFA,IAAA,SAnFA,qBAmFA,GAnFA,EAmFA;AAAA;;AACA,0BAAA;AAAA,QAAA,KAAA,EAAA,GAAA,CAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,oBAAA,GAAA,IAAA;AACA,UAAA,MAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA;AACA,UAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,UAAA,MAAA,CAAA,UAAA,GAAA,IAAA;AACA,SALA,MAKA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,OATA;AAUA,KA9FA;AA+FA;AACA,IAAA,UAhGA,sBAgGA,GAhGA,EAgGA;AAAA;;AACA,0BAAA;AAAA,QAAA,KAAA,EAAA,GAAA,CAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,oBAAA,GAAA,IAAA;AACA,UAAA,MAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA;AACA,UAAA,MAAA,CAAA,UAAA,GAAA,IAAA;AACA,UAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,SALA,MAKA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,OATA;AAUA,KA3GA;AA4GA;AACA,IAAA,IA7GA,kBA6GA;AAAA;;AACA,WAAA,KAAA,CAAA,IAAA,CAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,2BAAA,MAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,cAAA,MAAA,CAAA,oBAAA,GAAA,KAAA;;AACA,cAAA,MAAA,CAAA,cAAA;;AACA,cAAA,MAAA,CAAA,OAAA;AACA,aALA,MAKA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,WATA;AAUA,SAXA,MAWA;AACA,UAAA,UAAA,CAAA,YAAA;AACA,gBAAA,OAAA,GAAA,QAAA,CAAA,sBAAA,CAAA,UAAA,CAAA;;AACA,gBAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,OAAA,CAAA,EAAA;AACA,cAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,OAAA,EAAA,KAAA;AACA,aAFA,MAEA,IAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,UAAA,CAAA,EAAA;AACA,cAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,UAAA,EAAA,KAAA;AACA;AACA,WAPA,EAOA,CAPA,CAAA;AAQA,iBAAA,KAAA;AACA;AACA,OAvBA;AAwBA,KAtIA;AAwIA,IAAA,WAxIA,yBAwIA;AAAA;;AACA,WAAA,IAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,YAAA;AACA,QAAA,OAAA,CAAA,IAAA,GAAA,OAAA,CAAA,QAAA,CAAA,IAAA,GAAA,IAAA;;AACA,QAAA,OAAA,CAAA,SAAA,CAAA,MAAA;AACA,OAHA;AAIA,KA9IA;;AAgJA;;;AAGA,IAAA,MAnJA,oBAmJA;AAAA;;AACA,UAAA,KAAA,GAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,UAAA,iBAAA,EAAA,IADA;AAEA,UAAA,gBAAA,EAAA,IAFA;AAGA,UAAA,IAAA,EAAA;AAHA,SAAA,EAIA,IAJA,CAIA,YAAA;AACA,8BAAA,OAAA,CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,OAAA,CAAA,cAAA;;AACA,YAAA,OAAA,CAAA,OAAA;AACA,WAPA;AAQA,SAbA;AAcA,OAfA,MAeA;AACA,aAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA;AAEA;AA1KA;AAxJA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card shadow=\"never\" style=\"background:#e0f8ed\">\n          <div>\n            <el-col>\n              <el-form label-width=\"80px\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"所属公司:\">\n                    <el-select v-model=\"treeForm.ssgs\" placeholder=\"请选择所属公司\" @change=\"getNewTreeInfo\" clearable>\n                      <el-option v-for=\"item in OrganizationSelectedList\"\n                                 :key=\"item.value\"\n                                 :label=\"item.label\"\n                                 :value=\"item.value\"></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"快速查询:\">\n                    <el-input\n                      placeholder=\"输入关键字进行过滤\"\n                      v-model=\"treeForm.pdsmc\"\n                      @change=\"getNewTreeInfo\"\n                      clearable/>\n                  </el-form-item>\n                </el-col>\n              </el-form>\n            </el-col>\n          </div>\n          <div class=\"text item head-container\">\n            <el-col>\n              <el-tree :expand-on-click-node=\"true\"\n                       id=\"tree\"\n                       :data=\"treeOptions\"\n                       @node-click=\"handleNodeClick\"\n                       :highlight-current=\"true\"\n                       ref=\"tree\"\n                       :filter-node-method=\"filterNode\"\n                       node-key=\"id\"\n                       :default-expanded-keys=\"['0']\"\n                       :default-checked-keys=\"['0']\"\n                       accordion\n              />\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 120, itemWidth: 180 }\"\n        />\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button icon=\"el-icon-plus\" @click=\"bdzAddSensorButton\" v-hasPermi=\"['pdgtz:button:add']\" type=\"primary\">新增</el-button>\n            <el-button icon=\"el-icon-delete\" v-hasPermi=\"['pdgtz:button:delete']\" type=\"danger\" @click=\"remove\">删除</el-button>\n          </div>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"selectChange\"\n            height=\"70vh\"\n          >\n          <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\" width=\"160\"\n                           :resizable=\"false\">\n            <template slot-scope=\"scope\">\n              <el-button @click=\"pdgUpdate(scope.row)\" v-hasPermi=\"['pdgtz:button:update']\" type=\"text\" size=\"small\" title=\"修改\"  class='el-icon-edit'></el-button>\n              <el-button @click=\"pdgDetails(scope.row)\" type=\"text\" size=\"small\" title=\"详情\" class=\"el-icon-view\"></el-button>\n            </template>\n          </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!-- 一次设备弹出框结束展示设备履历 -->\n    <!--变电站所用弹出框开始-->\n    <el-dialog title=\"配电柜详情\" :visible.sync=\"bdzDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"60%\" @close=\"handleClose\">\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"130px\">\n        <el-row :gutter=\"20\">\n<!--          <el-col :span=\"8\">\n            <el-form-item label=\"所属公司\" prop=\"ssgs\">\n              <el-select v-model=\"form.ssgs\" placeholder=\"请选择所属公司\" :disabled=\"isDisabled\" clearable>\n                <el-option v-for=\"item in OrganizationSelectedList\"\n                           :key=\"item.value\"\n                           :label=\"item.label\"\n                           :value=\"item.label\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>-->\n<!--          <el-col :span=\"8\">\n            <el-form-item label=\"所属站室名称\" prop=\"sszsmc\">\n              <el-input v-model=\"form.sszsmc\" placeholder=\"请输入所属站室名称\" :disabled=\"true\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属站室运行编号\" prop=\"sszsyxbh\">\n              <el-input v-model=\"form.sszsyxbh\" placeholder=\"请输入所属站室运行编号\" :disabled=\"true\"></el-input>\n            </el-form-item>\n          </el-col>-->\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属站室\" prop=\"sszs\">\n              <el-select v-model=\"form.sszs\" placeholder=\"请选择所属电站\"  :disabled=\"isDisabled\" clearable>\n                <el-option\n                  v-for=\"item in pdsOptionsDataList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"开关柜名称\" prop=\"kggmc\">\n              <el-input v-model=\"form.kggmc\" placeholder=\"请输入开关柜名称\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"运行编号\" prop=\"yxbh\">\n              <el-input v-model=\"form.yxbh\" placeholder=\"请输入运行编号\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"投运日期：\" prop=\"tyrq\">\n              <el-date-picker\n                type=\"date\"\n                :disabled=\"isDisabled\"\n                placeholder=\"选择日期\"\n                value-format=\"yyyy-MM-dd\"\n                v-model=\"form.tyrq\"\n                style=\"width: 100%\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"生产厂家\" prop=\"sccj\">\n              <el-input v-model=\"form.sccj\" placeholder=\"请输入生产厂家\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n<!--          <el-col :span=\"8\">\n            <el-form-item label=\"ERP编码\" prop=\"erpBm\">\n              <el-input v-model=\"form.erpBm\" placeholder=\"请输入ERP编码\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>-->\n<!--        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"保管人\" prop=\"bgr\">\n              <el-input v-model=\"form.bgr\" placeholder=\"请输入保管人\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"资产变动方式\" prop=\"zcbdfs\">\n              <el-input v-model=\"form.zcbdfs\" placeholder=\"请输入资产变动方式\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"资产属性\" prop=\"zcsx\">\n              <el-input v-model=\"form.zcsx\" placeholder=\"请输入资产属性\"  :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-col :span=\"8\">\n            <el-form-item label=\"资产编号\" prop=\"zcbh\">\n              <el-input v-model=\"form.zcbh\" placeholder=\"请输入资产编号\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"WBS元素\" prop=\"wbsYs\">\n              <el-input v-model=\"form.wbsYs\" placeholder=\"请输入WBS元素\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          -->\n         <el-col :span=\"8\">\n            <el-form-item label=\"开关柜型号\" prop=\"kggxh\">\n              <el-input v-model=\"form.kggxh\" placeholder=\"请输入开关柜型号\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"出厂日期：\" prop=\"ccrq\">\n              <el-date-picker\n                type=\"date\"\n                :disabled=\"isDisabled\"\n                placeholder=\"选择日期\"\n                value-format=\"yyyy-MM-dd\"\n                v-model=\"form.ccrq\"\n                style=\"width: 100%\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"状态\" prop=\"zt\">\n              <el-select v-model=\"form.zt\" placeholder=\"请选择状态\" :disabled=\"isDisabled\" clearable>\n                <el-option v-for=\"item in pdgztList\"\n                           :key=\"item.value\"\n                           :label=\"item.label\"\n                           :value=\"item.label\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"资产性质\" prop=\"zcxz\">\n              <el-input v-model=\"form.zcxz\" placeholder=\"请输入资产性质\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>-->\n<!--          <el-col :span=\"8\">\n            <el-form-item label=\"地区特征\" prop=\"dqtz\">\n              <el-input v-model=\"form.dqtz\" placeholder=\"请输入地区特征\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>-->\n          <el-col :span=\"8\">\n            <el-form-item label=\"电压等级(KV)\" prop=\"dydj\">\n              <el-input-number :min=\"0\" :precision=\"2\" v-model=\"form.dydj\" placeholder=\"请输入电压等级\" :disabled=\"isDisabled\"></el-input-number>\n<!--              <el-select v-model=\"form.dydj\" placeholder=\"请选择电压等级\" :disabled=\"isDisabled\"  clearable>\n                <el-option v-for=\"item in dydjList\"\n                           :key=\"item.value\"\n                           :label=\"item.label\"\n                           :value=\"item.label\"></el-option>\n              </el-select>-->\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"开关用途\" prop=\"kgyt\">\n              <el-select v-model=\"form.kgyt\" placeholder=\"请选择开关用途\" :disabled=\"isDisabled\"  clearable>\n                <el-option v-for=\"item in pdgkgytList\"\n                           :key=\"item.value\"\n                           :label=\"item.label\"\n                           :value=\"item.label\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"开关柜类型\" prop=\"kgglx\">\n              <el-input v-model=\"form.kgglx\" placeholder=\"请输入开关柜类型\" :disabled=\"isDisabled\"></el-input>\n<!--              <el-select v-model=\"form.kgglx\" placeholder=\"请选择开关柜类型\" :disabled=\"isDisabled\"  clearable>\n                <el-option v-for=\"item in kgglxList\"\n                           :key=\"item.value\"\n                           :label=\"item.label\"\n                           :value=\"item.label\"></el-option>\n              </el-select>-->\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n<!--          <el-col :span=\"8\">\n            <el-form-item label=\"防护等级\" prop=\"fhdj\">\n              <el-input v-model=\"form.fhdj\" placeholder=\"请输入防护等级\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"柜体尺寸\" prop=\"gtcc\">\n              <el-input v-model=\"form.gtcc\" placeholder=\"请输入柜体尺寸\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>-->\n          <el-col :span=\"8\">\n            <el-form-item label=\"闭锁型式\" prop=\"bsxs\">\n              <el-input v-model=\"form.bsxs\" placeholder=\"请输入闭锁型式\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\" prop=\"bz\">\n              <el-input v-model=\"form.bz\" type=\"textarea\" rows=\"2\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n<!--        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"额定热稳定电流\" prop=\"edrwddl\">\n              <el-input v-model=\"form.edrwddl\" placeholder=\"请输入额定热稳定电流\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"额定热稳定时间：\" prop=\"edrwdsj\">\n              <el-date-picker\n                type=\"datetime\"\n                :disabled=\"isDisabled\"\n                placeholder=\"选择日期\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n                v-model=\"form.edrwdsj\"\n                style=\"width: 100%\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"额定动稳定电流\" prop=\"eddwddl\">\n              <el-input v-model=\"form.eddwddl\" placeholder=\"请输入额定动稳定电流\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"额定工频耐受电压\" prop=\"edgpnsdy\">\n              <el-input v-model=\"form.edgpnsdy\" placeholder=\"请输入额定工频耐受电压\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>-->\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\" v-show=\"showButton\">\n        <el-button @click=\"bdzDialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"save\" class=\"pmyBtn\">确 定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n\n</template>\n\n<script>\nimport {\n  getPdsTreeList, getPdgList, addPdg, removePdg, getPdgOne,\n} from '@/api/dagangOilfield/asset/pdg'\n  import {getDictTypeData} from \"@/api/system/dict/data\";\n  import {\n    getOrganizationSelected,\n  } from '@/api/dagangOilfield/asset/bdsbtz'\n  import {getPdsOptionsDataList} from \"@/api/dagangOilfield/asset/pdsgl\";\n  export default {\n    name: \"qxbzk\",\n    data() {\n      return {\n        //配电室下拉框\n        pdsOptionsDataList:[],\n        filterInfo: {\n          data: {\n            kggmc: '',\n            yxbh: '',\n          },\n          fieldList: [\n            {label: '开关柜名称', type: 'input', value: 'kggmc'},\n            {label: '运行编号', type: 'input', value: 'yxbh'},\n          ]\n        },\n        tableAndPageInfo: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n            //{prop: 'ssgs', label: '所属公司', minWidth: '120'},\n            {prop: 'sszsmc', label: '所属站室名称', minWidth: '180'},\n            {prop: 'sszsyxbh', label: '所属站室运行编号', minWidth: '120'},\n            {prop: 'kggmc', label: '开关柜名称', minWidth: '120'},\n            {prop: 'yxbh', label: '运行编号', minWidth: '140'},\n            /*{prop: 'erpBm', label: 'ERP编码', minWidth: '120'},\n            {prop: 'bgr', label: '保管人', minWidth: '120'},\n            {prop: 'zcbdfs', label: '资产变动方式', minWidth: '120'},\n            {prop: 'zcsx', label: '资产属性', minWidth: '180'},\n            {prop: 'zcbh', label: '资产编号', minWidth: '120'},\n            {prop: 'wbsYs', label: 'WBS元素', minWidth: '120'},*/\n            {prop: 'tyrq', label: '投运日期', minWidth: '140'},\n            {prop: 'sccj', label: '生产厂家', minWidth: '120'},\n            {prop: 'kggxh', label: '开关柜型号', minWidth: '120'},\n            //{prop: 'zcxz', label: '资产性质', minWidth: '120'},\n            {prop: 'ccrq', label: '出厂日期', minWidth: '120'},\n            {prop: 'zt', label: '状态', minWidth: '180'},\n            //{prop: 'dqtz', label: '地区特征', minWidth: '120'},\n            {prop: 'dydj', label: '电压等级', minWidth: '120'},\n            /*{prop: 'kgyt', label: '开关用途', minWidth: '140'},\n            {prop: 'kgglx', label: '开关柜类型', minWidth: '120'},\n            {prop: 'fhdj', label: '防护等级', minWidth: '120'},\n            {prop: 'gtcc', label: '柜体尺寸', minWidth: '120'},\n            {prop: 'bsxs', label: '闭锁型式', minWidth: '120'},\n            {prop: 'edrwddl', label: '额定热稳定电流', minWidth: '180'},\n            {prop: 'edrwdsj', label: '额定热稳定时间', minWidth: '120'},\n            {prop: 'eddwddl', label: '额定动稳定电流', minWidth: '120'},\n            {prop: 'edgpnsdy', label: '额定工频耐受电压', minWidth: '140'},*/\n            /*{\n              fixed: \"right\",\n              prop: 'operation',\n              label: '操作',\n              minWidth: '130px',\n              style: {display: 'block'},\n              operation: [\n                {name: '修改', clickFun: this.pdgUpdate},\n                {name: '详情', clickFun: this.pdgDetails},\n              ]\n            },*/\n          ]\n        },\n        OrganizationSelectedList:[],\n\n        bdzDialogFormVisible: false,\n        //弹出框表单\n        form: {},\n\n        loading: false,\n        //组织树\n        selectRows:[],\n        //变电站挂接数据\n        newTestData: [],\n        treeOptions:[],\n        //删除是否可用\n        multipleSensor: true,\n        //查询参数\n        queryParams: {\n          pageNum: 1,\n          pageSize: 10,\n          roleKey: '',\n          roleName: '',\n          status: '',\n        },\n        showSearch: true,\n\n        rules:{\n          //ssgs:[{required:true,message:'请选择所属公司',trigger:'change'}],\n          sszs:[{required:true,message:'请选择所属站室',trigger:'change'}],\n          kggmc:[{required:true,message:'请输入开关柜名称',trigger:'blur'}],\n          yxbh:[{required:true,message:'请输入运行编号',trigger:'blur'}],\n          /*erpBm:[{required:true,message:'请输入ERP编码',trigger:'blur'}],\n          bgr:[{required:true,message:'请输入保管人',trigger:'blur'}],\n          zcbdfs:[{required:true,message:'请输入资产变动方式',trigger:'blur'}],\n          zcsx:[{required:true,message:'请输入资产属性',trigger:'blur'}],\n          zcbh:[{required:true,message:'请输入资产编号',trigger:'blur'}],\n          wbsYs:[{required:true,message:'请输入WBS元素',trigger:'blur'}],*/\n          tyrq:[{required:true,message:'请选择投运日期',trigger:'change'}],\n          sccj:[{required:true,message:'请输入生产厂家',trigger:'blur'}],\n          kggxh:[{required:true,message:'请输入开关柜型号',trigger:'blur'}],\n          //zcxz:[{required:true,message:'请输入资产性质',trigger:'blur'}],\n          ccrq:[{required:true,message:'请选择出厂日期',trigger:'change'}],\n          zt:[{required:true,message:'请选择状态',trigger:'change'}],\n          //dqtz:[{required:true,message:'请输入地区特征',trigger:'blur'}],\n          dydj:[{required:true,message:'请选择电压等级',trigger:'change'}],\n          /*kgyt:[{required:true,message:'请选择开关用途',trigger:'change'}],\n          kgglx:[{required:true,message:'请选择开关柜类型',trigger:'change'}],\n          fhdj:[{required:true,message:'请输入防护等级',trigger:'blur'}],\n          gtcc:[{required:true,message:'请输入柜体尺寸',trigger:'blur'}],\n          bsxs:[{required:true,message:'请输入闭锁型式',triger:'blur'}],\n          edrwddl:[{required:true,message:'请输入额定热稳定电流',trigger:'blur'}],\n          edrwdsj:[{required:true,message:'请选择额定热稳定时间',trigger:'change'}],\n          eddwddl:[{required:true,message:'请输入额定动稳定电流',trigger:'blur'}],\n          edgpnsdy:[{required:true,message:'请输入额定公频耐受电压',trigger:'blur'}]*/\n        },\n\n        //树结构上面得筛选框参数\n        treeForm: {},\n        pdgkgytList:[\n          {value: '出线',\n            label: '出线'},\n          {value: '进线',\n            label: '进线'},\n          {value: '联络',\n            label: '联络'}\n        ],\n        pdgztList:[],\n        kgglxList:[],\n        dydjList:[],\n        showButton:true,\n        selectTreeData:{},\n        isDisabled:false,\n      };\n    },\n    watch: {},\n    created() {\n      this.isShow1 = true\n      //初始化加载时加载所有变电站信息\n      this.initDictData()\n      this.getOrganizationSelected()\n      this.getNewTreeInfo()\n      this.getData()\n      this.getPdsOptionsDataList()\n    },\n    methods: {\n      //获取配电室下拉框数据\n      getPdsOptionsDataList() {\n        getPdsOptionsDataList({}).then(res => {\n          this.pdsOptionsDataList = res.data;\n        })\n      },\n      //列表查询\n      async getData(param) {\n        const par={...this.params,...param}\n        try {\n          let {data,code}=await getPdgList(par)\n          if(code==='0000'){\n            this.tableAndPageInfo.tableData = data.records;\n            this.tableAndPageInfo.pager.total = data.total;\n          }\n        }catch (e) {\n          console.log(e)\n        }\n\n      },\n      async initDictData(){\n        let {data:pdgkgyt}=await getDictTypeData('pdgkgyt')\n        let {data:pdgzt}=await getDictTypeData('pdgzt')\n        let {data:kgglx}=await getDictTypeData('kgglx')\n        let {data:dg_dydj}=await getDictTypeData('dg_dydj')\n        //this.pdgkgytList=pdgkgyt\n        this.pdgztList=pdgzt\n        this.kgglxList=kgglx\n        this.dydjList=dg_dydj\n      },\n      getNewTreeInfo() {\n        getPdsTreeList(this.treeForm).then(res => {\n          this.treeOptions = res.data;\n        })\n      },\n      //树监听事件\n      filterNode(value, data) {\n        if (!value) return true;\n        return data.label.indexOf(value) !== -1;\n      },\n      //配电柜添加按钮\n      bdzAddSensorButton() {\n        this.isDisabled = false;\n        this.showButton = true;\n        this.bdzDialogFormVisible = true\n      },\n      //每页展示数量点击事件\n      handleSizeChange() {\n\n      },\n      selectChange(rows) {\n        this.ids = rows.map(item => item.objId)\n        this.selectRows = rows\n      },\n\n      //页码改变事件\n      handleCurrentChange() {\n\n      },\n      //获取组织结构下拉框数据\n      getOrganizationSelected() {\n        let parentId = '1001';\n        getOrganizationSelected({parentId: parentId}).then(res => {\n          this.OrganizationSelectedList = res.data\n        })\n      },\n      //树点击事件\n      async handleNodeClick(data, e) {\n        this.selectTreeData=data\n        if (data.identifier == '1'){\n          //配电柜详情\n          this.pdsOptionsDataList=[];\n          this.pdsOptionsDataList.label=data.label;\n          this.pdsOptionsDataList.value=data.id;\n          await this.getData({sszs:data.id})\n        }else if(data.identifier == '2'){\n          await this.getData({objId:data.id})\n          await this.pdgDetails(this.tableAndPageInfo.tableData[0])\n        }else{\n          await this.getData()\n        }\n      },\n       pdgUpdate(row) {\n         getPdgOne({objId:row.objId}).then(res => {\n           if (res.code === \"0000\") {\n             this.bdzDialogFormVisible = true;\n             this.form = res.data;\n             this.isDisabled = false;\n             this.showButton = true;\n           } else {\n             this.$message.error(\"操作失败\");\n           }\n         })\n      },\n      ///form弹窗\n       pdgDetails(row) {\n         getPdgOne({objId:row.objId}).then(res => {\n          if (res.code === \"0000\") {\n            this.bdzDialogFormVisible = true;\n            this.form = res.data;\n            this.isDisabled = true;\n            this.showButton = false;\n          } else {\n            this.$message.error(\"操作失败\");\n          }\n        })\n      },\n      //新增\n      save() {\n        this.$refs.form.validate((valid) => {\n          if(valid){\n            addPdg(this.form).then(res => {\n              if (res.code == \"0000\") {\n                this.$message.success(\"操作成功\");\n                this.bdzDialogFormVisible = false;\n                this.getNewTreeInfo();\n                this.getData();\n              } else {\n                this.$message.error(\"操作失败\");\n              }\n            });\n          }else{\n            setTimeout(() => {\n              var isError = document.getElementsByClassName(\"is-error\");\n              if (isError[0].querySelector('input')) {\n                isError[0].querySelector('input').focus();\n              } else if (isError[0].querySelector('textarea')) {\n                isError[0].querySelector('textarea').focus();\n              }\n            }, 1)\n            return false;\n          }\n        });\n      },\n\n      handleClose(){\n        this.form={};\n        this.$nextTick(() => {\n          this.form = this.$options.data().form;\n          this.resetForm(\"form\");\n        });\n      },\n\n      /**\n       * 删除配电柜\n       */\n      remove() {\n        if (this.ids.length !== 0) {\n          this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }).then(() => {\n            removePdg(this.ids).then(res => {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              });\n              this.getNewTreeInfo();\n              this.getData();\n            })\n          })\n        } else {\n          this.$message({\n            type: 'info',\n            message: '请选择至少一条数据!'\n          });\n        }\n\n      },\n    }\n  };\n</script>\n\n<style lang='scss' scoped>\n  .head-container {\n    margin: 0 auto;\n    width: 98%;\n    height: calc(100vh - 225px);\n    overflow: auto;\n  }\n\n  .box-card {\n    margin-bottom: 15px;\n\n    .el-card__header {\n      background-color: rgb(235, 245, 255) !important;\n    }\n  }\n\n  .box-cardList {\n    height: 70%;\n  }\n\n  .item {\n    width: 200px;\n    float: left;\n  }\n\n  #main_container_dj {\n    height: calc(100vh - 84px);\n  }\n\n  .aside_height {\n    height: 96%;\n  }\n\n  .defect .el-form-item:nth-child(odd) {\n    margin-right: 70px;\n  }\n\n  /*!*弹出框内宽度设置*!*/\n  .el-select {\n    width: 100%;\n  }\n\n  .el-date-editor{\n    width: 100%;\n  }\n\n  .el-carousel__item h3 {\n    color: #475669;\n    font-size: 14px;\n    opacity: 0.75;\n    line-height: 150px;\n\n  }\n\n  .el-carousel__item:nth-child(2n) {\n    background-color: #99a9bf;\n  }\n\n  .el-carousel__item:nth-child(2n+1) {\n    background-color: #d3dce6;\n  }\n  //有子节点 且未展开\n  .el-tree ::v-deep .el-icon-caret-right:before {\n    background: url(\"../../../../assets/image/add.png\") no-repeat 0;\n    content: \"\";\n    display: block;\n    width: 16px;\n    height: 16px;\n    font-size: 16px;\n    background-size: 16px;\n  }\n  //有子节点 且已展开\n  .el-tree\n  ::v-deep\n  .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {\n    background: url(\"../../../../assets/image/prep.png\") no-repeat 0;\n    content: \"\";\n    display: block;\n    width: 16px;\n    height: 16px;\n    font-size: 16px;\n    background-size: 16px;\n  }\n  //没有子节点\n  .el-tree ::v-deep .el-tree-node__expand-icon.is-leaf::before {\n    background: transparent;\n    content: \"\";\n    display: block;\n    width: 16px;\n    height: 16px;\n    font-size: 16px;\n    background-size: 16px;\n  }\n</style>\n"], "sourceRoot": "src/views/dagangOilfield/dwzygl/pdgl"}]}