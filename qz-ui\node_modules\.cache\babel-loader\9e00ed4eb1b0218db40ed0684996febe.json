{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\canvg\\node_modules\\@babel\\runtime\\helpers\\asyncToGenerator.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\canvg\\node_modules\\@babel\\runtime\\helpers\\asyncToGenerator.js", "mtime": 456789000000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:cmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmciKTsKCmZ1bmN0aW9uIGFzeW5jR2VuZXJhdG9yU3RlcChnZW4sIHJlc29sdmUsIHJlamVjdCwgX25leHQsIF90aHJvdywga2V5LCBhcmcpIHsKICB0cnkgewogICAgdmFyIGluZm8gPSBnZW5ba2V5XShhcmcpOwogICAgdmFyIHZhbHVlID0gaW5mby52YWx1ZTsKICB9IGNhdGNoIChlcnJvcikgewogICAgcmVqZWN0KGVycm9yKTsKICAgIHJldHVybjsKICB9CgogIGlmIChpbmZvLmRvbmUpIHsKICAgIHJlc29sdmUodmFsdWUpOwogIH0gZWxzZSB7CiAgICBQcm9taXNlLnJlc29sdmUodmFsdWUpLnRoZW4oX25leHQsIF90aHJvdyk7CiAgfQp9CgpmdW5jdGlvbiBfYXN5bmNUb0dlbmVyYXRvcihmbikgewogIHJldHVybiBmdW5jdGlvbiAoKSB7CiAgICB2YXIgc2VsZiA9IHRoaXMsCiAgICAgICAgYXJncyA9IGFyZ3VtZW50czsKICAgIHJldHVybiBuZXcgUHJvbWlzZShmdW5jdGlvbiAocmVzb2x2ZSwgcmVqZWN0KSB7CiAgICAgIHZhciBnZW4gPSBmbi5hcHBseShzZWxmLCBhcmdzKTsKCiAgICAgIGZ1bmN0aW9uIF9uZXh0KHZhbHVlKSB7CiAgICAgICAgYXN5bmNHZW5lcmF0b3JTdGVwKGdlbiwgcmVzb2x2ZSwgcmVqZWN0LCBfbmV4dCwgX3Rocm93LCAibmV4dCIsIHZhbHVlKTsKICAgICAgfQoKICAgICAgZnVuY3Rpb24gX3Rocm93KGVycikgewogICAgICAgIGFzeW5jR2VuZXJhdG9yU3RlcChnZW4sIHJlc29sdmUsIHJlamVjdCwgX25leHQsIF90aHJvdywgInRocm93IiwgZXJyKTsKICAgICAgfQoKICAgICAgX25leHQodW5kZWZpbmVkKTsKICAgIH0pOwogIH07Cn0KCm1vZHVsZS5leHBvcnRzID0gX2FzeW5jVG9HZW5lcmF0b3IsIG1vZHVsZS5leHBvcnRzLl9fZXNNb2R1bGUgPSB0cnVlLCBtb2R1bGUuZXhwb3J0c1siZGVmYXVsdCJdID0gbW9kdWxlLmV4cG9ydHM7"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/node_modules/canvg/node_modules/@babel/runtime/helpers/asyncToGenerator.js"], "names": ["asyncGeneratorStep", "gen", "resolve", "reject", "_next", "_throw", "key", "arg", "info", "value", "error", "done", "Promise", "then", "_asyncToGenerator", "fn", "self", "args", "arguments", "apply", "err", "undefined", "module", "exports", "__esModule"], "mappings": ";;AAAA,SAASA,kBAAT,CAA4BC,GAA5B,EAAiCC,OAAjC,EAA0CC,MAA1C,EAAkDC,KAAlD,EAAyDC,MAAzD,EAAiEC,GAAjE,EAAsEC,GAAtE,EAA2E;AACzE,MAAI;AACF,QAAIC,IAAI,GAAGP,GAAG,CAACK,GAAD,CAAH,CAASC,GAAT,CAAX;AACA,QAAIE,KAAK,GAAGD,IAAI,CAACC,KAAjB;AACD,GAHD,CAGE,OAAOC,KAAP,EAAc;AACdP,IAAAA,MAAM,CAACO,KAAD,CAAN;AACA;AACD;;AAED,MAAIF,IAAI,CAACG,IAAT,EAAe;AACbT,IAAAA,OAAO,CAACO,KAAD,CAAP;AACD,GAFD,MAEO;AACLG,IAAAA,OAAO,CAACV,OAAR,CAAgBO,KAAhB,EAAuBI,IAAvB,CAA4BT,KAA5B,EAAmCC,MAAnC;AACD;AACF;;AAED,SAASS,iBAAT,CAA2BC,EAA3B,EAA+B;AAC7B,SAAO,YAAY;AACjB,QAAIC,IAAI,GAAG,IAAX;AAAA,QACIC,IAAI,GAAGC,SADX;AAEA,WAAO,IAAIN,OAAJ,CAAY,UAAUV,OAAV,EAAmBC,MAAnB,EAA2B;AAC5C,UAAIF,GAAG,GAAGc,EAAE,CAACI,KAAH,CAASH,IAAT,EAAeC,IAAf,CAAV;;AAEA,eAASb,KAAT,CAAeK,KAAf,EAAsB;AACpBT,QAAAA,kBAAkB,CAACC,GAAD,EAAMC,OAAN,EAAeC,MAAf,EAAuBC,KAAvB,EAA8BC,MAA9B,EAAsC,MAAtC,EAA8CI,KAA9C,CAAlB;AACD;;AAED,eAASJ,MAAT,CAAgBe,GAAhB,EAAqB;AACnBpB,QAAAA,kBAAkB,CAACC,GAAD,EAAMC,OAAN,EAAeC,MAAf,EAAuBC,KAAvB,EAA8BC,MAA9B,EAAsC,OAAtC,EAA+Ce,GAA/C,CAAlB;AACD;;AAEDhB,MAAAA,KAAK,CAACiB,SAAD,CAAL;AACD,KAZM,CAAP;AAaD,GAhBD;AAiBD;;AAEDC,MAAM,CAACC,OAAP,GAAiBT,iBAAjB,EAAoCQ,MAAM,CAACC,OAAP,CAAeC,UAAf,GAA4B,IAAhE,EAAsEF,MAAM,CAACC,OAAP,CAAe,SAAf,IAA4BD,MAAM,CAACC,OAAzG", "sourcesContent": ["function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {\n  try {\n    var info = gen[key](arg);\n    var value = info.value;\n  } catch (error) {\n    reject(error);\n    return;\n  }\n\n  if (info.done) {\n    resolve(value);\n  } else {\n    Promise.resolve(value).then(_next, _throw);\n  }\n}\n\nfunction _asyncToGenerator(fn) {\n  return function () {\n    var self = this,\n        args = arguments;\n    return new Promise(function (resolve, reject) {\n      var gen = fn.apply(self, args);\n\n      function _next(value) {\n        asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value);\n      }\n\n      function _throw(err) {\n        asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err);\n      }\n\n      _next(undefined);\n    });\n  };\n}\n\nmodule.exports = _asyncToGenerator, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"]}]}