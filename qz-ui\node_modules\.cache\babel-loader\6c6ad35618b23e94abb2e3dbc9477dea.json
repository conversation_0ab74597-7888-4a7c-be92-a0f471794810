{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\activiti_nyjxjh\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\activiti_nyjxjh\\index.vue", "mtime": 1706897320920}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZm9yLWVhY2giKTsKCnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLmZvci1lYWNoIik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKCnJlcXVpcmUoInJlZ2VuZXJhdG9yLXJ1bnRpbWUvcnVudGltZSIpOwoKdmFyIF9hc3luY1RvR2VuZXJhdG9yMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovU2hhbW1wb29sL3dvcmsvY29kZS9kZ3l0LzAxXHU0RUUzXHU3ODAxL3F6LXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2FzeW5jVG9HZW5lcmF0b3IiKSk7Cgp2YXIgX29iamVjdFNwcmVhZDIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkQ6L1NoYW1tcG9vbC93b3JrL2NvZGUvZGd5dC8wMVx1NEVFM1x1NzgwMS9xei11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9vYmplY3RTcHJlYWQyIikpOwoKdmFyIF9wcm9jZXNzVGFzayA9IHJlcXVpcmUoIkAvYXBpL2FjdGl2aXRpL3Byb2Nlc3NUYXNrIik7Cgp2YXIgX2VsZW1lbnRVaSA9IHJlcXVpcmUoImVsZW1lbnQtdWkiKTsKCnZhciBfdXNlcmdyb3VwID0gcmVxdWlyZSgiQC9hcGkvc3lzdGVtL3VzZXJncm91cCIpOwoKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0MiA9IHsKICBuYW1lOiAiaW5kZXgiLAogIHByb3BzOiB7CiAgICAvKua1geeoi+WPkei1t+W/heWhq+aVsOaNrgogICAgICAgIHByb2Nlc3NEYXRhOnsKICAgICAgICAgIHByb2Nlc3NEZWZpbml0aW9uS2V5OiLmtYHnqIvlrprkuYnnmoRrZXkiLC8v5b+F5aGrCiAgICAgICAgICB0YXNrSWQ6IuS7u+WKoWlk77yM5aaC5p6c5Lu75Yqh5Zyo5Luj5Yqe5YiX6KGo5pe277yM5Lya5qC55o2u5Luj5Yqe5YiX6KGo6I635Y+WdGFza2lk77yM5ZCm5YiZ6ZyA5Lyg5YWl5Lia5YqhaWTmnaXnoa7lrpp0YXNrIiwKICAgICAgICAgIGJ1c2luZXNzS2V5OiLkuJrliqFpZO+8jOWvueW6lOS4muWKoeeahOS4u+mUriIsLy90YXNraWTlkoxidXNpbmVzc0tleeS4pOiAheW/hemhu+acieWFtuS4reS4gOS4quaJjeWPr+S7peehruWumuS4gOS4qnRhc2sKICAgICAgICAgIGJ1c2luZXNzVHlwZToi5Lia5Yqh57G75Z6L77yM55So5LqO5Yy65YiG5LiN5ZCM55qE5Lia5YqhIi8v5b+F5aGrCiAgICAgICAgICB2YXJpYWJsZXM6IuaLk+WxleWPguaVsCIvL+a1geeoi+WumuS5ieS4reiuvue9rueahOWPguaVsCwKICAgICAgICAgIG5leHRVc2VyOiLlpoLmnpzmtYHnqIvlrp7kvovkuK3lubbmnKrphY3nva7mr4/kuIDkuKroioLngrnnmoTlpITnkIbkurrvvIzliJnpnIDopoHnlKjmiLfmiYvliqjpgInmi6nmr4/kuIDkuKroioLngrnnmoTlpITnkIbkuroiLAogICAgICAgICAgcHJvY2Vzc1R5cGU6J2NvbXBsZXRlLHJvbGxiYWNrJywKICAgICAgICAgIGRlZmF1bHRGcm9tOnRydWUsZmFsc2Ug5piv5ZCm6ZyA6KaB6buY6K6k6KGo5Y2VCiAgICAgICAgfSovCiAgICBwcm9jZXNzRGF0YTogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQ6IGZ1bmN0aW9uIF9kZWZhdWx0KCkgewogICAgICAgIHJldHVybiB7CiAgICAgICAgICBkZWZhdWx0RnJvbTogdHJ1ZSwKICAgICAgICAgIHByb2Nlc3NUeXBlOiAiY29tcGxldGUiCiAgICAgICAgfTsKICAgICAgfQogICAgfSwKICAgIC8v5pi+56S66ZqQ6JePCiAgICBpc1Nob3c6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgZGVmYXVsdDogZmFsc2UKICAgIH0sCiAgICAvL+WtkOe7hOS7tum7mOiupOWPguaVsAogICAgb3B0aW9uOiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdDogZnVuY3Rpb24gX2RlZmF1bHQoKSB7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIHRpdGxlOiAi5a6h5om5IgogICAgICAgIH07CiAgICAgIH0KICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBkaXNhYmxlZDogZmFsc2UsCiAgICAgIGZvcm06IHt9LAogICAgICBkYXRhczoge30sCiAgICAgIGxvYWRpbmc6IG51bGwsCiAgICAgIGd6ZnpyT3B0aW9uczogW10KICAgIH07CiAgfSwKICB3YXRjaDogewogICAgcHJvY2Vzc0RhdGE6IHsKICAgICAgaGFuZGxlcjogZnVuY3Rpb24gaGFuZGxlcihuZXdWYWwsIG9sZFZhbCkgewogICAgICAgIHZhciBfdGhpcyA9IHRoaXM7CgogICAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICAgIF90aGlzLmRhdGFzID0gKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCBuZXdWYWwpOwogICAgICAgICAgY29uc29sZS5sb2coImRhdGFzIiwgX3RoaXMuZGF0YXMpOwogICAgICAgICAgdmFyIHN0YXR1cyA9IF90aGlzLmRhdGFzLnZhcmlhYmxlcy5zdGF0dXM7CiAgICAgICAgICB2YXIgcGVyc29uR3JvdXBJZCA9IDY1OwoKICAgICAgICAgIHN3aXRjaCAoc3RhdHVzKSB7CiAgICAgICAgICAgIGNhc2UgIjEiOgogICAgICAgICAgICAgIHBlcnNvbkdyb3VwSWQgPSA2NTsKICAgICAgICAgICAgICBicmVhazsKCiAgICAgICAgICAgIGNhc2UgIjIiOgogICAgICAgICAgICAgIHBlcnNvbkdyb3VwSWQgPSA2NjsKICAgICAgICAgICAgICBicmVhazsKCiAgICAgICAgICAgIGNhc2UgIjMiOgogICAgICAgICAgICAgIHBlcnNvbkdyb3VwSWQgPSA2NzsKICAgICAgICAgICAgICBicmVhazsKCiAgICAgICAgICAgIGNhc2UgIjQiOgogICAgICAgICAgICAgIHBlcnNvbkdyb3VwSWQgPSA2ODsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgIH0KCiAgICAgICAgICBfdGhpcy5nemZ6ck9wdGlvbnMgPSBbXTsKICAgICAgICAgICgwLCBfdXNlcmdyb3VwLmdldFVzZXJzKSh7CiAgICAgICAgICAgIHBlcnNvbkdyb3VwSWQ6IHBlcnNvbkdyb3VwSWQsCiAgICAgICAgICAgIGRlcHRJZDogMCwKICAgICAgICAgICAgZGVwdE5hbWU6ICIiCiAgICAgICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgX3RoaXMuZ3pmenJPcHRpb25zID0gcmVzLmRhdGE7CiAgICAgICAgICB9KTsKICAgICAgICB9KTsKICAgICAgfSwKICAgICAgZGVlcDogdHJ1ZSwKICAgICAgaW1tZWRpYXRlOiB0cnVlCiAgICB9CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkge30sCiAgbWV0aG9kczogewogICAgLy/mtYHnqIvmj5DkuqTvvIzmtYHnqIvmi5Lnu50KICAgIHRvZG9TdWJtaXQ6IGZ1bmN0aW9uIHRvZG9TdWJtaXQoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwoKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoIC8qI19fUFVSRV9fKi9yZWdlbmVyYXRvclJ1bnRpbWUubWFyayhmdW5jdGlvbiBfY2FsbGVlKCkgewogICAgICAgIHZhciBuZXh0VXNlciwgcmVzdWx0RGF0YSwgX3lpZWxkJGNvbXBsZVRhc2taanhqLCBjb2RlLCBkYXRhOwoKICAgICAgICByZXR1cm4gcmVnZW5lcmF0b3JSdW50aW1lLndyYXAoZnVuY3Rpb24gX2NhbGxlZSQoX2NvbnRleHQpIHsKICAgICAgICAgIHdoaWxlICgxKSB7CiAgICAgICAgICAgIHN3aXRjaCAoX2NvbnRleHQucHJldiA9IF9jb250ZXh0Lm5leHQpIHsKICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICBfdGhpczIuZGF0YXMucm91dGVQYXRoID0gX3RoaXMyLiRyb3V0ZS5wYXRoOwoKICAgICAgICAgICAgICAgIGlmIChfdGhpczIuZm9ybS5uZXh0VXNlciAmJiBfdGhpczIuZm9ybS5uZXh0VXNlci5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgICAgICAgIG5leHRVc2VyID0gIiI7CgogICAgICAgICAgICAgICAgICBfdGhpczIuZm9ybS5uZXh0VXNlci5mb3JFYWNoKGZ1bmN0aW9uIChlKSB7CiAgICAgICAgICAgICAgICAgICAgbmV4dFVzZXIgKz0gZS51c2VyTmFtZSArICIsIjsKICAgICAgICAgICAgICAgICAgfSk7IC8v5Y675o6J5pyA5ZCO5LiA5Liq6YCX5Y+3CgoKICAgICAgICAgICAgICAgICAgaWYgKG5leHRVc2VyLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgICAgICAgICBuZXh0VXNlciA9IG5leHRVc2VyLnN1YnN0cigwLCBuZXh0VXNlci5sZW5ndGggLSAxKTsKICAgICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgICAgX3RoaXMyLmRhdGFzLm5leHRVc2VyID0gbmV4dFVzZXI7CiAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICBfdGhpczIuZGF0YXMubmV4dFVzZXIgPSB1bmRlZmluZWQ7CiAgICAgICAgICAgICAgICAgIF90aGlzMi5kYXRhcy5uZXh0VXNlck5pY2tOYW1lID0gdW5kZWZpbmVkOwogICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgIGlmICghKCFfdGhpczIuZGF0YXMubmV4dFVzZXIgJiYgX3RoaXMyLmRhdGFzLnByb2Nlc3NUeXBlID09PSAiY29tcGxldGUiICYmIF90aGlzMi5kYXRhcy5kZWZhdWx0RnJvbSkpIHsKICAgICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDU7CiAgICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgIF90aGlzMi4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICAgIHR5cGU6ICJlcnJvciIsCiAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLor7fpgInmi6nkurrlkZghIgogICAgICAgICAgICAgICAgfSk7CgogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LmFicnVwdCgicmV0dXJuIik7CgogICAgICAgICAgICAgIGNhc2UgNToKICAgICAgICAgICAgICAgIGlmIChfdGhpczIuZm9ybS5jb21tZW50KSB7CiAgICAgICAgICAgICAgICAgIF90aGlzMi5kYXRhcy52YXJpYWJsZXMuY29tbWVudCA9IF90aGlzMi5mb3JtLmNvbW1lbnQ7CiAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgX3RoaXMyLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgICAgICAgIC8vIOS7peacjeWKoeeahOaWueW8j+iwg+eUqOeahCBMb2FkaW5nIOmcgOimgeW8guatpeWFs+mXrQogICAgICAgICAgICAgICAgICBfdGhpczIubG9hZGluZyA9IF9lbGVtZW50VWkuTG9hZGluZy5zZXJ2aWNlKHsKICAgICAgICAgICAgICAgICAgICBsb2NrOiB0cnVlLAogICAgICAgICAgICAgICAgICAgIC8vbG9ja+eahOS/ruaUueespi0t6buY6K6k5pivZmFsc2UKICAgICAgICAgICAgICAgICAgICB0ZXh0OiAi5rWB56iL6L+b6KGM5Lit77yM6K+356iN5ZCOIiwKICAgICAgICAgICAgICAgICAgICAvL+aYvuekuuWcqOWKoOi9veWbvuagh+S4i+aWueeahOWKoOi9veaWh+ahiAogICAgICAgICAgICAgICAgICAgIHNwaW5uZXI6ICJlbC1pY29uLWxvYWRpbmciLAogICAgICAgICAgICAgICAgICAgIC8v6Ieq5a6a5LmJ5Yqg6L295Zu+5qCH57G75ZCNCiAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogInJnYmEoMCwgMCwgMCwgMC43KSIsCiAgICAgICAgICAgICAgICAgICAgLy/pga7nvanlsYLpopzoibIKICAgICAgICAgICAgICAgICAgICB0YXJnZXQ6IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoIiNkaWFsb2dBY3QiKQogICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICAgIF9jb250ZXh0LnByZXYgPSA3OwogICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDEwOwogICAgICAgICAgICAgICAgcmV0dXJuICgwLCBfcHJvY2Vzc1Rhc2suY29tcGxlVGFza1pqeGpoKShfdGhpczIuZGF0YXMpOwoKICAgICAgICAgICAgICBjYXNlIDEwOgogICAgICAgICAgICAgICAgX3lpZWxkJGNvbXBsZVRhc2taanhqID0gX2NvbnRleHQuc2VudDsKICAgICAgICAgICAgICAgIGNvZGUgPSBfeWllbGQkY29tcGxlVGFza1pqeGouY29kZTsKICAgICAgICAgICAgICAgIGRhdGEgPSBfeWllbGQkY29tcGxlVGFza1pqeGouZGF0YTsKCiAgICAgICAgICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgICAgICAgIHJlc3VsdERhdGEgPSBkYXRhOwogICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgIGlmIChjb2RlKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzMi4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgICAgICAgICAgICAgIC8vIOS7peacjeWKoeeahOaWueW8j+iwg+eUqOeahCBMb2FkaW5nIOmcgOimgeW8guatpeWFs+mXrQogICAgICAgICAgICAgICAgICAgIF90aGlzMi5sb2FkaW5nLmNsb3NlKCk7CiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAyMDsKICAgICAgICAgICAgICAgIGJyZWFrOwoKICAgICAgICAgICAgICBjYXNlIDE3OgogICAgICAgICAgICAgICAgX2NvbnRleHQucHJldiA9IDE3OwogICAgICAgICAgICAgICAgX2NvbnRleHQudDAgPSBfY29udGV4dFsiY2F0Y2giXSg3KTsKCiAgICAgICAgICAgICAgICBfdGhpczIuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgICAgICAgLy8g5Lul5pyN5Yqh55qE5pa55byP6LCD55So55qEIExvYWRpbmcg6ZyA6KaB5byC5q2l5YWz6ZetCiAgICAgICAgICAgICAgICAgIF90aGlzMi5sb2FkaW5nLmNsb3NlKCk7CiAgICAgICAgICAgICAgICB9KTsKCiAgICAgICAgICAgICAgY2FzZSAyMDoKICAgICAgICAgICAgICAgIGlmIChyZXN1bHREYXRhKSB7CiAgICAgICAgICAgICAgICAgIHJlc3VsdERhdGEucHJvY2Vzc1R5cGUgPSBfdGhpczIuZGF0YXMucHJvY2Vzc1R5cGU7CgogICAgICAgICAgICAgICAgICBfdGhpczIuJGVtaXQoInRvZG9EYXRhIiwgcmVzdWx0RGF0YSk7CiAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgX3RoaXMyLnRvQ2xvc2UoKTsKCiAgICAgICAgICAgICAgY2FzZSAyMjoKICAgICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LnN0b3AoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUsIG51bGwsIFtbNywgMTddXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIHRvQ2xvc2U6IGZ1bmN0aW9uIHRvQ2xvc2UoKSB7CiAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICduZXh0VXNlcicsICIiKTsKICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgJ2NvbW1lbnQnLCAiIik7CiAgICAgIHRoaXMuJGVtaXQoInRvQ2xvc2UiLCAiY2xvc2UiKTsKICAgIH0KICB9Cn07CmV4cG9ydHMuZGVmYXVsdCA9IF9kZWZhdWx0Mjs="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AA6EA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBACA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,KAAA,EAAA;AACA;;;;;;;;;;;AAWA,IAAA,WAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAFA,sBAEA;AACA,eAAA;AACA,UAAA,WAAA,EAAA,IADA;AAEA,UAAA,WAAA,EAAA;AAFA,SAAA;AAIA;AAPA,KAZA;AAqBA;AACA,IAAA,MAAA,EAAA;AACA,MAAA,IAAA,EAAA,OADA;AAEA,MAAA,OAAA,EAAA;AAFA,KAtBA;AA0BA;AACA,IAAA,MAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAFA,sBAEA;AACA,eAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAAA;AACA;AAJA;AA3BA,GAFA;AAoCA,EAAA,IApCA,kBAoCA;AACA,WAAA;AACA,MAAA,QAAA,EAAA,KADA;AAEA,MAAA,IAAA,EAAA,EAFA;AAGA,MAAA,KAAA,EAAA,EAHA;AAIA,MAAA,OAAA,EAAA,IAJA;AAKA,MAAA,YAAA,EAAA;AALA,KAAA;AAOA,GA5CA;AA6CA,EAAA,KAAA,EAAA;AACA,IAAA,WAAA,EAAA;AACA,MAAA,OADA,mBACA,MADA,EACA,MADA,EACA;AAAA;;AACA,aAAA,SAAA,CAAA,YAAA;AACA,UAAA,KAAA,CAAA,KAAA,mCAAA,MAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,KAAA,CAAA,KAAA;AACA,cAAA,MAAA,GAAA,KAAA,CAAA,KAAA,CAAA,SAAA,CAAA,MAAA;AACA,cAAA,aAAA,GAAA,EAAA;;AACA,kBAAA,MAAA;AACA,iBAAA,GAAA;AACA,cAAA,aAAA,GAAA,EAAA;AACA;;AACA,iBAAA,GAAA;AACA,cAAA,aAAA,GAAA,EAAA;AACA;;AACA,iBAAA,GAAA;AACA,cAAA,aAAA,GAAA,EAAA;AACA;;AACA,iBAAA,GAAA;AACA,cAAA,aAAA,GAAA,EAAA;AACA;AAZA;;AAcA,UAAA,KAAA,CAAA,YAAA,GAAA,EAAA;AACA,mCAAA;AACA,YAAA,aAAA,EAAA,aADA;AAEA,YAAA,MAAA,EAAA,CAFA;AAGA,YAAA,QAAA,EAAA;AAHA,WAAA,EAIA,IAJA,CAIA,UAAA,GAAA,EAAA;AACA,YAAA,KAAA,CAAA,YAAA,GAAA,GAAA,CAAA,IAAA;AACA,WANA;AAOA,SA3BA;AA4BA,OA9BA;AA+BA,MAAA,IAAA,EAAA,IA/BA;AAgCA,MAAA,SAAA,EAAA;AAhCA;AADA,GA7CA;AAiFA,EAAA,OAjFA,qBAiFA,CAAA,CAjFA;AAkFA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,UAFA,wBAEA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,SAAA,GAAA,MAAA,CAAA,MAAA,CAAA,IAAA;;AACA,oBAAA,MAAA,CAAA,IAAA,CAAA,QAAA,IAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,kBAAA,QADA,GACA,EADA;;AAEA,kBAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,CAAA,EAAA;AACA,oBAAA,QAAA,IAAA,CAAA,CAAA,QAAA,GAAA,GAAA;AACA,mBAFA,EAFA,CAKA;;;AACA,sBAAA,QAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,oBAAA,QAAA,GAAA,QAAA,CAAA,MAAA,CAAA,CAAA,EAAA,QAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA;;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,QAAA,GAAA,QAAA;AACA,iBAVA,MAUA;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,QAAA,GAAA,SAAA;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,gBAAA,GAAA,SAAA;AACA;;AAfA,sBAiBA,CAAA,MAAA,CAAA,KAAA,CAAA,QAAA,IACA,MAAA,CAAA,KAAA,CAAA,WAAA,KAAA,UADA,IAEA,MAAA,CAAA,KAAA,CAAA,WAnBA;AAAA;AAAA;AAAA;;AAqBA,gBAAA,MAAA,CAAA,QAAA,CAAA;AACA,kBAAA,IAAA,EAAA,OADA;AAEA,kBAAA,OAAA,EAAA;AAFA,iBAAA;;AArBA;;AAAA;AA2BA,oBAAA,MAAA,CAAA,IAAA,CAAA,OAAA,EAAA;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,SAAA,CAAA,OAAA,GAAA,MAAA,CAAA,IAAA,CAAA,OAAA;AACA;;AACA,gBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,kBAAA,MAAA,CAAA,OAAA,GAAA,mBAAA,OAAA,CAAA;AACA,oBAAA,IAAA,EAAA,IADA;AACA;AACA,oBAAA,IAAA,EAAA,WAFA;AAEA;AACA,oBAAA,OAAA,EAAA,iBAHA;AAGA;AACA,oBAAA,UAAA,EAAA,oBAJA;AAIA;AACA,oBAAA,MAAA,EAAA,QAAA,CAAA,aAAA,CAAA,YAAA;AALA,mBAAA,CAAA;AAOA,iBATA;;AA9BA;AAAA;AAAA,uBA0CA,kCAAA,MAAA,CAAA,KAAA,CA1CA;;AAAA;AAAA;AA0CA,gBAAA,IA1CA,yBA0CA,IA1CA;AA0CA,gBAAA,IA1CA,yBA0CA,IA1CA;;AA2CA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,UAAA,GAAA,IAAA;AACA;;AACA,oBAAA,IAAA,EAAA;AACA,kBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,oBAAA,MAAA,CAAA,OAAA,CAAA,KAAA;AACA,mBAHA;AAIA;;AAnDA;AAAA;;AAAA;AAAA;AAAA;;AAqDA,gBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,kBAAA,MAAA,CAAA,OAAA,CAAA,KAAA;AACA,iBAHA;;AArDA;AA0DA,oBAAA,UAAA,EAAA;AACA,kBAAA,UAAA,CAAA,WAAA,GAAA,MAAA,CAAA,KAAA,CAAA,WAAA;;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,UAAA,EAAA,UAAA;AACA;;AACA,gBAAA,MAAA,CAAA,OAAA;;AA9DA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA+DA,KAjEA;AAkEA,IAAA,OAlEA,qBAkEA;AACA,WAAA,IAAA,CAAA,KAAA,IAAA,EAAA,UAAA,EAAA,EAAA;AACA,WAAA,IAAA,CAAA,KAAA,IAAA,EAAA,SAAA,EAAA,EAAA;AACA,WAAA,KAAA,CAAA,SAAA,EAAA,OAAA;AACA;AAtEA;AAlFA,C", "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      id=\"dialogAct\"\n      class=\"tree\"\n      :title=\"option.title ? option.title : ''\"\n      :top=\"option.top ? option.top : '10vh'\"\n      v-dialogDrag\n      :visible.sync=\"isShow\"\n      :width=\"option.width ? option.width : '25%'\"\n      :close-on-click-modal=\"false\"\n      @close=\"toClose\"\n      :destroy-on-colse=\"true\"\n      :modal-append-to-body=\"false\"\n    >\n      <slot name=\"Dialog_content\" />\n      <el-form\n        label-width=\"120px\"\n        ref=\"form\"\n        :model=\"form\"\n        v-if=\"datas.defaultFrom ? true : false\"\n      >\n        <div>\n          <el-row>\n            <div v-if=\"datas.processType === 'complete'\">\n              <el-col :span=\"24\" v-if=\"datas.nextUser ? datas.nextUser : true\">\n                <el-form-item prop=\"nextUser\" label=\"审批人\">\n                  <el-select\n                    v-model=\"form.nextUser\"\n                    placeholder=\"请选择人员\"\n                    style=\"width: 100%;\"\n                    value-key=\"userName\"\n                    clearable\n                    filterable\n                    multiple\n                  >\n                    <el-option\n                      v-for=\"item in gzfzrOptions\"\n                      :key=\"item.userName\"\n                      :label=\"item.nickName\"\n                      :value=\"{\n                        userName: item.userName,\n                        nickName: item.nickName\n                      }\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </div>\n            <el-col :span=\"24\">\n              <el-form-item\n                prop=\"comment\"\n                label=\"回退原因：\"\n                v-if=\"datas.processType === 'rollback'\"\n              >\n                <el-input\n                  style=\"width: 100%\"\n                  v-model=\"form.comment\"\n                  type=\"textarea\"\n                  placeholder=\"请输入原因\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n      </el-form>\n      <span v-else>请确定是否提交</span>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"info\" @click=\"toClose\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"todoSubmit('form')\">确定</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { compleTaskZjxjh } from \"@/api/activiti/processTask\";\nimport { Loading } from \"element-ui\";\nimport { getUsers } from \"@/api/system/usergroup\";\nexport default {\n  name: \"index\",\n  props: {\n    /*流程发起必填数据\n        processData:{\n          processDefinitionKey:\"流程定义的key\",//必填\n          taskId:\"任务id，如果任务在代办列表时，会根据代办列表获取taskid，否则需传入业务id来确定task\",\n          businessKey:\"业务id，对应业务的主键\",//taskid和businessKey两者必须有其中一个才可以确定一个task\n          businessType:\"业务类型，用于区分不同的业务\"//必填\n          variables:\"拓展参数\"//流程定义中设置的参数,\n          nextUser:\"如果流程实例中并未配置每一个节点的处理人，则需要用户手动选择每一个节点的处理人\",\n          processType:'complete,rollback',\n          defaultFrom:true,false 是否需要默认表单\n        }*/\n    processData: {\n      type: Object,\n      default() {\n        return {\n          defaultFrom: true,\n          processType: \"complete\"\n        };\n      }\n    },\n    //显示隐藏\n    isShow: {\n      type: Boolean,\n      default: false\n    },\n    //子组件默认参数\n    option: {\n      type: Object,\n      default() {\n        return { title: \"审批\" };\n      }\n    }\n  },\n  data() {\n    return {\n      disabled: false,\n      form: {},\n      datas: {},\n      loading: null,\n      gzfzrOptions: []\n    };\n  },\n  watch: {\n    processData: {\n      handler(newVal, oldVal) {\n        this.$nextTick(() => {\n          this.datas = { ...newVal };\n          console.log(\"datas\", this.datas);\n          let status = this.datas.variables.status;\n          let personGroupId = 65;\n          switch (status) {\n            case \"1\":\n              personGroupId = 65;\n              break;\n            case \"2\":\n              personGroupId = 66;\n              break;\n            case \"3\":\n              personGroupId = 67;\n              break;\n            case \"4\":\n              personGroupId = 68;\n              break;\n          }\n          this.gzfzrOptions = [];\n          getUsers({\n            personGroupId: personGroupId,\n            deptId: 0,\n            deptName: \"\"\n          }).then(res => {\n            this.gzfzrOptions = res.data;\n          });\n        });\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  mounted() {},\n  methods: {\n    //流程提交，流程拒绝\n    async todoSubmit() {\n      this.datas.routePath = this.$route.path;\n      if (this.form.nextUser && this.form.nextUser.length > 0) {\n        let nextUser = \"\";\n        this.form.nextUser.forEach(e => {\n          nextUser += e.userName + \",\";\n        });\n        //去掉最后一个逗号\n        if (nextUser.length > 0) {\n          nextUser = nextUser.substr(0, nextUser.length - 1);\n        }\n        this.datas.nextUser = nextUser;\n      } else {\n        this.datas.nextUser = undefined;\n        this.datas.nextUserNickName = undefined;\n      }\n      if (\n        !this.datas.nextUser &&\n        this.datas.processType === \"complete\" &&\n        this.datas.defaultFrom\n      ) {\n        this.$message({\n          type: \"error\",\n          message: \"请选择人员!\"\n        });\n        return;\n      }\n      if (this.form.comment) {\n        this.datas.variables.comment = this.form.comment;\n      }\n      this.$nextTick(() => {\n        // 以服务的方式调用的 Loading 需要异步关闭\n        this.loading = Loading.service({\n          lock: true, //lock的修改符--默认是false\n          text: \"流程进行中，请稍后\", //显示在加载图标下方的加载文案\n          spinner: \"el-icon-loading\", //自定义加载图标类名\n          background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n          target: document.querySelector(\"#dialogAct\")\n        });\n      });\n      let resultData;\n      try {\n        let { code, data } = await compleTaskZjxjh(this.datas);\n        if (code === \"0000\") {\n          resultData = data;\n        }\n        if (code) {\n          this.$nextTick(() => {\n            // 以服务的方式调用的 Loading 需要异步关闭\n            this.loading.close();\n          });\n        }\n      } catch (e) {\n        this.$nextTick(() => {\n          // 以服务的方式调用的 Loading 需要异步关闭\n          this.loading.close();\n        });\n      }\n      if (resultData) {\n        resultData.processType = this.datas.processType;\n        this.$emit(\"todoData\", resultData);\n      }\n      this.toClose();\n    },\n    toClose() {\n      this.$set(this.form,'nextUser',\"\")\n      this.$set(this.form,'comment',\"\")\n      this.$emit(\"toClose\", \"close\");\n    }\n  }\n};\n</script>\n\n<style scoped></style>\n"], "sourceRoot": "src/components/activiti_nyjxjh"}]}