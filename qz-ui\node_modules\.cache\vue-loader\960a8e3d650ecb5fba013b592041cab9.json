{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\pdyj.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\pdyj.vue", "mtime": 1706897323222}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["pdyj.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4DA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "pdyj.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk", "sourcesContent": ["<template>\n  <div>\n    <el-white>\n      <el-white class=\"button-group\">\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"addpdyj\">新增</el-button>\n        <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"deletepdyj\">删除</el-button>\n      </el-white>\n      <el-table\n          stripe\n          border\n          v-loading=\"loading\"\n          :data=\"tableData\"\n          @selection-change=\"handleSelectionChange\">\n        <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n        <el-table-column label=\"所属状态量\" align=\"center\" prop=\"ztlmc\"/>\n        <el-table-column label=\"判断依据\" align=\"center\" prop=\"clgzms\"/>\n        <!--        <el-table-column label=\"处理方式\" align=\"center\" prop=\"clfs\" />-->\n        <el-table-column label=\"基本扣分值\" align=\"center\" prop=\"jbkfz\"/>\n        <el-table-column label=\"脚本\" align=\"center\" prop=\"jb\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"操作\" fixed=\"right\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"180\">\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" @click=\"updatePdyj(scope.row)\">修改</el-button>\n            <el-button type=\"text\" @click=\"showDetail(scope.row)\">详情</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      <pagination\n          v-show=\"queryParams.total>0\"\n          :total=\"queryParams.total\"\n          :page.sync=\"queryParams.pageNum\"\n          :limit.sync=\"queryParams.pageSize\"\n          @pagination=\"getListPdyj\"/>\n    </el-white>\n\n    <dialog-form\n        ref=\"dialogForm\"\n        :append-to-body=\"true\"\n        label-width=\"150px\"\n        out-width=\"50%\"\n        :reminder=\"reminder\"\n        :rows=\"rows\"\n        @save=\"savePdyj\"\n        @textareaClick=\"textareaClick\"\n    />\n\n    <el-dialog\n        title=\"脚本维护\"\n        :visible.sync=\"isShowJb\"\n        v-dialogDrag\n        width=\"80%\"\n        append-to-body\n        @close=\"jbClose\"\n    >\n      <!--  脚本维护框  -->\n      <JBwh :sblx=\"sblx\" :mp-data=\"mpData\" :jb=\"formList[5].value\" @setJbVal=\"setJbVal\" @jbClose=\"jbClose\"></JBwh>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  getPagePdyj,\n  saveOrUpdatePdyj,\n  removePdyj,\n} from \"@/api/dagangOilfield/bzgl/sbztpjbzk/pdyj\";\nimport DialogForm from \"com/dialogFrom/dialogForm\";\nimport JBwh from \"@/views/dagangOilfield/bzgl/sbztpjbzk/jbwh\";\n\nexport default {\n  name: \"pdyj\",\n  components: {DialogForm, JBwh},\n  props: {\n    mpData: {\n      type: Object,\n    },\n    sblx: {\n      type: String,\n      default: ''\n    },\n  },\n\n  data() {\n    return {\n      isShowJb: false,//是否显示脚本维护框\n      //新增或修改标题\n      reminder: \"新增\",\n      rows: 2,\n      tableData: [],\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        total: 0,\n        ssztl: \"\",\n      },\n      loading: false,\n      jbkfz: {\n        label: \"基本扣分值：\",\n        value: \"\",\n        type: \"selectChange1\",\n        name: \"jbkfz\",\n        default: true,\n        options: [{label: \"2\", value: \"2\"}, {label: \"4\", value: \"4\"}, {label: \"8\", value: \"8\"}, {label: \"10\", value: \"10\"}],\n        rules: {required: true, message: \"请选择基本扣分值\"},\n      },\n      formList: [\n        {\n          label: 'objId',\n          value: '',\n          type: 'input',\n          name: 'objId',\n          hidden: false,\n        },\n        {\n          label: 'ssztl：',\n          value: '',\n          type: 'input',\n          name: 'ssztl',\n          hidden: false,\n        },\n        {\n          label: \"所属状态量：\",\n          value: \"\",\n          type: \"disabled\",\n          name: \"ztlmc\",\n          default: true,\n          rules: {required: false, message: \"请输入所属状态量\"},\n        },\n        {\n          label: \"判断依据：\",\n          value: \"\",\n          type: \"input\",\n          name: \"clgzms\",\n          default: true,\n          rules: {required: true, message: \"请输入判断依据\"},\n        },\n        {\n          label: \"基本扣分值：\",\n          value: 0,\n          type: \"input\",\n          name: \"jbkfz\",\n          default: true,\n          rules: {required: true, message: \"请输入基本扣分值\"},\n        },\n        {\n          label: \"脚本：\",\n          value: \"\",\n          type: \"textarea\",\n          name: \"jb\",\n          default: true,\n          rules: {required: true, message: \"请输入脚本\"},\n        }\n      ],\n      //选中行数据\n      selectedRowData: [],\n    };\n  },\n  mounted() {\n    if (this.sblx && this.sblx.indexOf('bd') > -1) {\n      this.formList[4] = this.jbkfz;\n    }\n    this.getListPdyj();\n  },\n  methods: {\n    jbClose() {\n      this.isShowJb = false;\n    },\n    textareaClick(val, isDisabled) {\n      if (!isDisabled) {\n        this.isShowJb = true;\n      }\n    },\n    //获取判断依据\n    getListPdyj() {\n      this.loading = true;\n      this.queryParams.ssztl = this.mpData.objId;\n      getPagePdyj(this.queryParams).then((res) => {\n        this.tableData = res.data.records;\n        this.queryParams.total = res.data.total;\n        this.loading = false;\n      });\n\n    },\n\n    //新增判断依据\n    addpdyj() {\n      this.reminder = \"新增\";\n      this.formList.ssztl = this.mpData.objId;\n      this.formList = this.$options.data().formList;\n      if (this.sblx && this.sblx.indexOf('bd') > -1) {\n        this.formList[4] = this.jbkfz\n      }\n      const addForm = this.formList.map((item) => {\n        if (item.name === 'ztlmc') {\n          item.value = this.mpData.ztlmc\n        }\n        if (item.name === 'ssztl') {\n          item.value = this.mpData.objId\n        }\n        return item;\n      });\n      this.$refs.dialogForm.showzzc(addForm);\n    },\n\n    //修改判断依据\n    updatePdyj(row) {\n      const updateList = this.formList.map((item) => {\n        item.value = row[item.name];\n        return item;\n      });\n      this.reminder = \"修改\";\n      this.$refs.dialogForm.showzzc(updateList);\n    },\n\n    //查看详情\n    showDetail(row) {\n      const infoList = this.formList.map((item) => {\n        item.value = row[item.name];\n        return item;\n      });\n      this.reminder = \"详情\";\n      this.$refs.dialogForm.showxq(infoList);\n    },\n\n    //批量删除判断依据\n    deletepdyj() {\n      this.$confirm(\"确认删除选中数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      })\n          .then(() => {\n            let ids = [];\n            this.selectedRowData.forEach((item) => {\n              ids.push(item.objId);\n            });\n\n            removePdyj(ids).then((res) => {\n              if (res.code === \"0000\") {\n                this.$message.success(\"删除成功\");\n                this.getListPdyj();\n              } else {\n                this.$message.error(\"操作失败\");\n              }\n            });\n          })\n          .catch(() => {\n            this.$message({\n              type: \"info\",\n              message: \"已取消删除\",\n            });\n          });\n    },\n\n    //保存数据\n    savePdyj(formData) {\n      saveOrUpdatePdyj(formData).then(res => {\n        if (res.code === '0000') {\n          this.$message.success('操作成功')\n          this.getListPdyj()\n        } else {\n          this.$message.error('操作失败')\n        }\n      })\n    },\n    //行选中事件\n    handleSelectionChange(row) {\n      this.selectedRowData = row\n    },\n    //设置脚本值\n    setJbVal(val) {\n      this.$refs.dialogForm.setFieldVal('jb', val);\n    },\n  },\n};\n</script>\n\n<style scoped>\n.pagination-container {\n  position: relative;\n  height: 25px;\n  margin-bottom: 20px !important;\n  margin-top: 15px;\n  padding: 10px 20px !important;\n}\n</style>\n\n\n"]}]}