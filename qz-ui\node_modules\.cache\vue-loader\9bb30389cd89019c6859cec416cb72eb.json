{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\hslxwh.vue?vue&type=style&index=0&id=4856119c&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\hslxwh.vue", "mtime": 1706897323218}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouYm94LWNhcmQgewogIG1hcmdpbi1ib3R0b206IDE1cHg7CiAgLmVsLWNhcmRfX2hlYWRlciB7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjM1LCAyNDUsIDI1NSkgIWltcG9ydGFudDsKICB9Cn0KLml0ZW0gewogIHdpZHRoOiAyMDBweDsKICBoZWlnaHQ6IDE0OHB4OwogIGZsb2F0OiBsZWZ0Owp9Cg=="}, {"version": 3, "sources": ["hslxwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8SA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "hslxwh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-white>\n      <el-filter\n        :data=\"filterInfo.data\"\n        :field-list=\"filterInfo.fieldList\"\n        :width=\"{ labelWidth: 180, itemWidth: 160 }\"\n        @handleReset=\"getReset\"\n      />\n    </el-white>\n    <div>\n      <el-white class=\"button-group\">\n        <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n          <el-col\n            style=\"\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\n            \"\n          >\n            <div>\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-plus\"\n                @click=\"addSensorButton\"\n                >新增</el-button\n              >\n              <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"getDelete\"\n                >删除</el-button\n              >\n            </div>\n          </el-col>\n        </el-row>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"handleSelectionChange\"\n          height=\"69vh\"\n        />\n      </el-white>\n      <!-- 详情/新增/修改 -->\n      <el-dialog :title=\"title\" :visible.sync=\"isShowDetails\" width=\"50%\" v-dialogDrag>\n        <el-form label-width=\"120px\" ref=\"form\" :model=\"form\"  :rules=\"rules\">\n          <el-row :gutter=\"8\" class=\"pull-left\">\n            <el-col :span=\"12\">\n              <el-form-item label=\"函数分类名称：\" prop=\"hsflmc\">\n                <el-input\n                  placeholder=\"请输入函数分类名称\"\n                  v-model=\"form.hsflmc\"\n                  :disabled=\"isDisabled\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"函数分类编码：\" prop=\"hsflbm\">\n                <el-input\n                  placeholder=\"请输入分类编码\"\n                  v-model=\"form.hsflbm\"\n                  :disabled=\"isDisabled\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n        <div slot=\"footer\">\n          <el-button @click=\"close\">关 闭</el-button>\n          <el-button type=\"primary\" @click=\"commitAdd\">保 存</el-button>\n        </div>\n      </el-dialog>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {\n  getPageDataList,\n  remove,\n  saveOrUpdate,\n} from \"@/api/dagangOilfield/bzgl/hsflkwh\";\n\nexport default {\n  name: \"syhskwh\",\n  data() {\n    return {\n      //form表单\n      form: {\n        hsflmc:undefined,\n        hsflbm:undefined,\n      },\n      //是否显示弹框\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //表单数据\n      dataTable: [],\n      //删除选择列\n      selectRows: [],\n      //标题\n      title: \"\",\n      //筛选框\n      filterInfo: {\n        data: {\n          syzyid: \"\",\n          syxmmc: \"\",\n        },\n        fieldList: [\n          {\n            label: \"函数分类名称\",\n            value: \"hsflmc\",\n            type: \"input\",\n            clearable: true,\n          },\n          {\n            label: \"函数分类编码\",\n            value: \"hsflbm\",\n            type: \"input\",\n            clearable: true,\n          },\n        ],\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"函数分类名称\", prop: \"hsflmc\", minWidth: \"100\" },\n          { label: \"函数分类编码\", prop: \"hsflbm\", minWidth: \"100\" },\n          {\n            prop: \"operation\",\n            label: \"操作\",\n            minWidth: \"100px\",\n            style: { display: \"block\" },\n            //操作列固定再右侧\n            fixed: \"right\",\n            operation: [{ name: \"修改\", clickFun: this.undateDetails }],\n          },\n        ],\n        option: { checkBox: true, serialNumber: true },\n      },\n\n      //删除是否可用\n      multipleSensor: true,\n      //查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n      },\n      //填入数据校验\n       rules: {\n        hsflmc: [\n          { required: true, message: \"函数分类名称不能为空\", trigger: \"blur\" },\n        ],\n        // hsflbm: [\n        //   { required: true, message: \"函数分类编码不能为空\", trigger: \"blur\" },\n        // ],\n\n      },\n      //表单开关\n      isSearchShow: false,\n    };\n  },\n  watch: {},\n  created() {\n    this.getData();\n  },\n  methods: {\n    //添加按钮\n    addSensorButton() {\n      //打开弹窗\n      this.isShowDetails = true;\n      //给表单置空\n      this.form = {};\n      //表单可编辑\n      this.isDisabled = false;\n      //设置弹出框标题\n      this.title = \"新增\";\n    },\n\n    //修改按钮\n    undateDetails(row) {\n      //打开弹窗\n      this.isShowDetails = true;\n      //给表单赋值\n      this.form = row;\n      //表单可编辑\n      this.isDisabled = false;\n      //设置弹出框标题\n      this.title = \"修改\";\n    },\n\n    //确认提交\n    commitAdd() {\n      this.$refs[\"form\"].validate((valid) => {\n        if (valid) {\n          saveOrUpdate(this.form).then((res) => {\n            if (res.code === \"0000\") {\n              this.$message.success(res.msg);\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n              this.isShowDetails = false;\n            } else {\n              this.$message.error(res.msg);\n            }\n          });\n        }\n      });\n    },\n\n    close() {\n      this.isShowDetails = false;\n    },\n    //定义重置方法\n    getReset() {},\n    //编辑按钮\n    updateSensorButton() {\n      //打开弹窗\n      this.isShowDetails = true;\n      //给表单赋值\n      this.form = row;\n      //表单可编辑\n      this.isDisabled = false;\n      //设置弹出框标题\n      this.title = \"修改\";\n    },\n    //删除按钮\n    getDelete() {\n      if (this.selectRows.length < 1) {\n        this.$message.warning(\"请选择正确的数据！！！\");\n        return;\n      }\n      let ids = this.selectRows.map((item) => {\n        return item.objId;\n      });\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      })\n        .then(() => {\n          remove(ids).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\",\n              });\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\",\n              });\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\",\n          });\n        });\n    },\n\n    //导出按钮\n    handleExport() {},\n    //行选中\n    handleSelectionChange(rows) {\n      this.selectRows = rows;\n      this.whmjzButtonDisabled = rows.length != 1;\n      //获取到当前行对象\n      this.mjzRowForm = rows[0];\n    },\n\n    //查询列表\n    async getData(params) {\n      try {\n        const param = { ...this.querySyzxmParam, ...params };\n        const { data, code } = await getPageDataList(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n        }\n      } catch (e) {\n\n      }\n    },\n    //搜索\n    handleQuery() {},\n    //重置\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n    },\n  },\n};\n</script>\n\n<style lang='scss' scoped>\n.box-card {\n  margin-bottom: 15px;\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n</style>\n"]}]}