{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\activiti_sybg\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\activiti_sybg\\index.vue", "mtime": 1706897321038}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IExvYWRpbmcgfSBmcm9tICJlbGVtZW50LXVpIjsKaW1wb3J0IHsgZ2V0VXNlcnMgfSBmcm9tICJAL2FwaS9zeXN0ZW0vdXNlcmdyb3VwIjsKaW1wb3J0IHsgY29tcGxldGVUYXNrIH0gZnJvbSAiQC9hcGkvYWN0aXZpdGkvcHJvY2Vzc1Rhc2siOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogImluZGV4IiwKICBwcm9wczogewogICAgLyrmtYHnqIvlj5Hotbflv4XloavmlbDmja4KICAgICAgICBwcm9jZXNzRGF0YTp7CiAgICAgICAgICBwcm9jZXNzRGVmaW5pdGlvbktleToi5rWB56iL5a6a5LmJ55qEa2V5IiwvL+W/heWhqwogICAgICAgICAgdGFza0lkOiLku7vliqFpZO+8jOWmguaenOS7u+WKoeWcqOS7o+WKnuWIl+ihqOaXtu+8jOS8muagueaNruS7o+WKnuWIl+ihqOiOt+WPlnRhc2tpZO+8jOWQpuWImemcgOS8oOWFpeS4muWKoWlk5p2l56Gu5a6adGFzayIsCiAgICAgICAgICBidXNpbmVzc0tleToi5Lia5YqhaWTvvIzlr7nlupTkuJrliqHnmoTkuLvplK4iLC8vdGFza2lk5ZKMYnVzaW5lc3NLZXnkuKTogIXlv4XpobvmnInlhbbkuK3kuIDkuKrmiY3lj6/ku6Xnoa7lrprkuIDkuKp0YXNrCiAgICAgICAgICBidXNpbmVzc1R5cGU6IuS4muWKoeexu+Wei++8jOeUqOS6juWMuuWIhuS4jeWQjOeahOS4muWKoSIvL+W/heWhqwogICAgICAgICAgdmFyaWFibGVzOiLmi5PlsZXlj4LmlbAiLy/mtYHnqIvlrprkuYnkuK3orr7nva7nmoTlj4LmlbAsCiAgICAgICAgICBuZXh0VXNlcjoi5aaC5p6c5rWB56iL5a6e5L6L5Lit5bm25pyq6YWN572u5q+P5LiA5Liq6IqC54K555qE5aSE55CG5Lq677yM5YiZ6ZyA6KaB55So5oi35omL5Yqo6YCJ5oup5q+P5LiA5Liq6IqC54K555qE5aSE55CG5Lq6IiwKICAgICAgICAgIG5leHRVc2VySW5mbzp7fSwvL+aJi+WKqOS8oOeahOWuoeaguOS6uuS/oeaBrwogICAgICAgICAgcHJvY2Vzc1R5cGU6J2NvbXBsZXRlLHJvbGxiYWNrJywKICAgICAgICAgIGRlZmF1bHRGcm9tOnRydWUsZmFsc2Ug5piv5ZCm6ZyA6KaB6buY6K6k6KGo5Y2VCiAgICAgICAgfSovCiAgICBwcm9jZXNzRGF0YTogewogICAgICB0eXBlOiBPYmplY3QsCiAgICAgIGRlZmF1bHQoKSB7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIGRlZmF1bHRGcm9tOiB0cnVlLAogICAgICAgICAgcHJvY2Vzc1R5cGU6ICJjb21wbGV0ZSIKICAgICAgICB9OwogICAgICB9CiAgICB9LAoKICAgIC8v5pi+56S66ZqQ6JePCiAgICBpc1Nob3c6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgZGVmYXVsdDogZmFsc2UKICAgIH0sCiAgICAvL+WtkOe7hOS7tum7mOiupOWPguaVsAogICAgb3B0aW9uOiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdCgpIHsKICAgICAgICByZXR1cm4geyB0aXRsZTogIuWuoeaJuSIgfTsKICAgICAgfQogICAgfQogIH0sCgogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBkaXNhYmxlZDogZmFsc2UsCiAgICAgIGZvcm06IHt9LAogICAgICBkYXRhczoge30sCiAgICAgIGxvYWRpbmc6IG51bGwsCiAgICAgIGd6ZnpyT3B0aW9uczogW10KICAgIH07CiAgfSwKICB3YXRjaDogewogICAgcHJvY2Vzc0RhdGE6IHsKICAgICAgaGFuZGxlcihuZXdWYWwsIG9sZFZhbCkgewogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgIHRoaXMuZGF0YXMgPSB7IC4uLm5ld1ZhbCB9OwogICAgICAgICAgbGV0IGlzaGcgPSB0aGlzLmRhdGFzLnZhcmlhYmxlcy5pc2hnOwogICAgICAgICAgbGV0IHp5ID0gdGhpcy5kYXRhcy52YXJpYWJsZXMuenk7CiAgICAgICAgICBsZXQgcGVyc29uR3JvdXBJZCA9IDU5OwogICAgICAgICAgc3dpdGNoIChpc2hnKSB7CiAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICBwZXJzb25Hcm91cElkID0gNTE7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICBwZXJzb25Hcm91cElkID0gNTc7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgNzoKICAgICAgICAgICAgICBzd2l0Y2ggKHp5KSB7CiAgICAgICAgICAgICAgICBjYXNlICLlj5jnlLXorr7lpIciOiAvL+WPmOeUtQogICAgICAgICAgICAgICAgICBwZXJzb25Hcm91cElkID0gMTIzOwogICAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICAgIGNhc2UgIumFjeeUteiuvuWkhyI6IC8v6YWN55S1CiAgICAgICAgICAgICAgICAgIHBlcnNvbkdyb3VwSWQgPSAxMjg7CiAgICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgICAgY2FzZSAi6L6T55S16K6+5aSHIjogLy/ovpPnlLUKICAgICAgICAgICAgICAgICAgcGVyc29uR3JvdXBJZCA9IDEyOTsKICAgICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgfQogICAgICAgICAgZ2V0VXNlcnMoewogICAgICAgICAgICBwZXJzb25Hcm91cElkOiBwZXJzb25Hcm91cElkLAogICAgICAgICAgICBkZXB0SWQ6IDAsCiAgICAgICAgICAgIGRlcHROYW1lOiAiIgogICAgICAgICAgfSkudGhlbihyZXMgPT4gewogICAgICAgICAgICB0aGlzLmd6ZnpyT3B0aW9ucyA9IHJlcy5kYXRhOwogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICAgIH0sCiAgICAgIGRlZXA6IHRydWUsCiAgICAgIGltbWVkaWF0ZTogdHJ1ZQogICAgfQogIH0sCiAgbW91bnRlZCgpIHt9LAogIG1ldGhvZHM6IHsKICAgIC8v5rWB56iL5o+Q5LqkCiAgICBhc3luYyB0b2RvU3VibWl0KCkgewogICAgICB0aGlzLmRhdGFzLnJvdXRlUGF0aCA9IHRoaXMuJHJvdXRlLnBhdGg7CiAgICAgIC8vIGRlYnVnZ2VyOwogICAgICBpZiAodGhpcy5mb3JtLm5leHRVc2VyKSB7CiAgICAgICAgLy/lpITnkIbkurrlpJrpgIkKICAgICAgICBpZiAodGhpcy5mb3JtLm5leHRVc2VyLmxlbmd0aCA+IDApIHsKICAgICAgICAgIGxldCBuZXh0VXNlciA9ICIiOwogICAgICAgICAgdGhpcy5mb3JtLm5leHRVc2VyLmZvckVhY2goZSA9PiB7CiAgICAgICAgICAgIG5leHRVc2VyICs9IGUudXNlck5hbWUgKyAiLCI7CiAgICAgICAgICB9KTsKICAgICAgICAgIC8v5Y675o6J5pyA5ZCO5LiA5Liq6YCX5Y+3CiAgICAgICAgICBpZiAobmV4dFVzZXIubGVuZ3RoID4gMCkgewogICAgICAgICAgICBuZXh0VXNlciA9IG5leHRVc2VyLnN1YnN0cigwLCBuZXh0VXNlci5sZW5ndGggLSAxKTsKICAgICAgICAgIH0KICAgICAgICAgIHRoaXMuZGF0YXMubmV4dFVzZXIgPSBuZXh0VXNlcjsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5kYXRhcy5uZXh0VXNlciA9IHRoaXMuZm9ybS5uZXh0VXNlci51c2VyTmFtZTsKICAgICAgICAgIHRoaXMuZGF0YXMubmV4dFVzZXJOaWNrTmFtZSA9IHRoaXMuZm9ybS5uZXh0VXNlci5uaWNrTmFtZTsKICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgaWYgKHRoaXMucHJvY2Vzc0RhdGEubmV4dFVzZXJJbmZvKSB7CiAgICAgICAgICAvL+aJi+WKqOS8oOeahOeUqOaIt+S/oeaBrwogICAgICAgICAgaWYgKHRoaXMucHJvY2Vzc0RhdGEubmV4dFVzZXJJbmZvLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgbGV0IG5leHRVc2VyID0gIiI7CiAgICAgICAgICAgIGxldCBuZXh0TmljayA9ICIiOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLm5leHRVc2VySW5mby5mb3JFYWNoKGUgPT4gewogICAgICAgICAgICAgIG5leHRVc2VyICs9IGUudXNlck5hbWUgKyAiLCI7CiAgICAgICAgICAgICAgbmV4dE5pY2sgKz0gZS5uaWNrTmFtZSArICIsIjsKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIC8v5Y675o6J5pyA5ZCO5LiA5Liq6YCX5Y+3CiAgICAgICAgICAgIGlmIChuZXh0VXNlci5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgICAgbmV4dFVzZXIgPSBuZXh0VXNlci5zdWJzdHIoMCwgbmV4dFVzZXIubGVuZ3RoIC0gMSk7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgaWYgKG5leHROaWNrLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgICBuZXh0TmljayA9IG5leHROaWNrLnN1YnN0cigwLCBuZXh0Tmljay5sZW5ndGggLSAxKTsKICAgICAgICAgICAgfQogICAgICAgICAgICB0aGlzLmRhdGFzLm5leHRVc2VyID0gbmV4dFVzZXI7CiAgICAgICAgICAgIHRoaXMuZGF0YXMubmV4dFVzZXJOaWNrTmFtZSA9IG5leHROaWNrOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy5kYXRhcy5uZXh0VXNlciA9IHRoaXMucHJvY2Vzc0RhdGEubmV4dFVzZXJJbmZvLnVzZXJOYW1lOwogICAgICAgICAgICB0aGlzLmRhdGFzLm5leHRVc2VyTmlja05hbWUgPSB0aGlzLnByb2Nlc3NEYXRhLm5leHRVc2VySW5mby5uaWNrTmFtZTsKICAgICAgICAgIH0KICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5kYXRhcy5uZXh0VXNlciA9IHVuZGVmaW5lZDsKICAgICAgICAgIHRoaXMuZGF0YXMubmV4dFVzZXJOaWNrTmFtZSA9IHVuZGVmaW5lZDsKICAgICAgICB9CiAgICAgIH0KICAgICAgaWYgKAogICAgICAgICF0aGlzLmRhdGFzLm5leHRVc2VyICYmCiAgICAgICAgdGhpcy5kYXRhcy5wcm9jZXNzVHlwZSA9PT0gImNvbXBsZXRlIiAmJgogICAgICAgIHRoaXMuZGF0YXMuZGVmYXVsdEZyb20KICAgICAgKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICB0eXBlOiAiZXJyb3IiLAogICAgICAgICAgbWVzc2FnZTogIuivt+mAieaLqeS6uuWRmCEiCiAgICAgICAgfSk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGlmICh0aGlzLmZvcm0uY29tbWVudCkgewogICAgICAgIHRoaXMuZGF0YXMudmFyaWFibGVzLmNvbW1lbnQgPSB0aGlzLmZvcm0uY29tbWVudDsKICAgICAgfQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgLy8g5Lul5pyN5Yqh55qE5pa55byP6LCD55So55qEIExvYWRpbmcg6ZyA6KaB5byC5q2l5YWz6ZetCiAgICAgICAgdGhpcy5sb2FkaW5nID0gTG9hZGluZy5zZXJ2aWNlKHsKICAgICAgICAgIGxvY2s6IHRydWUsIC8vbG9ja+eahOS/ruaUueespi0t6buY6K6k5pivZmFsc2UKICAgICAgICAgIHRleHQ6ICLmtYHnqIvov5vooYzkuK3vvIzor7fnqI3lkI4iLCAvL+aYvuekuuWcqOWKoOi9veWbvuagh+S4i+aWueeahOWKoOi9veaWh+ahiAogICAgICAgICAgc3Bpbm5lcjogImVsLWljb24tbG9hZGluZyIsIC8v6Ieq5a6a5LmJ5Yqg6L295Zu+5qCH57G75ZCNCiAgICAgICAgICBiYWNrZ3JvdW5kOiAicmdiYSgwLCAwLCAwLCAwLjcpIiwgLy/pga7nvanlsYLpopzoibIKICAgICAgICAgIHRhcmdldDogZG9jdW1lbnQucXVlcnlTZWxlY3RvcigiI2RpYWxvZ0FjdCIpCiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgICBsZXQgcmVzdWx0RGF0YTsKICAgICAgdHJ5IHsKICAgICAgICBsZXQgeyBjb2RlLCBkYXRhIH0gPSBhd2FpdCBjb21wbGV0ZVRhc2sodGhpcy5kYXRhcyk7CiAgICAgICAgaWYgKGNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgcmVzdWx0RGF0YSA9IGRhdGE7CiAgICAgICAgfQogICAgICAgIGlmIChjb2RlKSB7CiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICAgIC8vIOS7peacjeWKoeeahOaWueW8j+iwg+eUqOeahCBMb2FkaW5nIOmcgOimgeW8guatpeWFs+mXrQogICAgICAgICAgICB0aGlzLmxvYWRpbmcuY2xvc2UoKTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgIC8vIOS7peacjeWKoeeahOaWueW8j+iwg+eUqOeahCBMb2FkaW5nIOmcgOimgeW8guatpeWFs+mXrQogICAgICAgICAgdGhpcy5sb2FkaW5nLmNsb3NlKCk7CiAgICAgICAgfSk7CiAgICAgIH0KICAgICAgaWYgKHJlc3VsdERhdGEpIHsKICAgICAgICByZXN1bHREYXRhLnByb2Nlc3NUeXBlID0gdGhpcy5kYXRhcy5wcm9jZXNzVHlwZTsKICAgICAgICB0aGlzLiRlbWl0KCJ0b2RvRGF0YSIsIHJlc3VsdERhdGEpOwogICAgICB9CiAgICAgIHRoaXMudG9DbG9zZSgpOwogICAgfSwKICAgIHRvQ2xvc2UoKSB7CiAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sJ25leHRVc2VyJywiIikKICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwnY29tbWVudCcsIiIpCiAgICAgIHRoaXMuJGVtaXQoInRvQ2xvc2UiLCAiY2xvc2UiKTsKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6HA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/activiti_sybg", "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      id=\"dialogAct\"\n      class=\"tree\"\n      :title=\"\n        datas.variables\n          ? datas.variables.title\n            ? datas.variables.title\n            : ''\n          : ''\n      \"\n      :top=\"option.top ? option.top : '10vh'\"\n      v-dialogDrag\n      :visible.sync=\"isShow\"\n      :width=\"option.width ? option.width : '25%'\"\n      :close-on-click-modal=\"false\"\n      @close=\"toClose\"\n      :destroy-on-colse=\"true\"\n      :modal-append-to-body=\"false\"\n    >\n      <slot name=\"Dialog_content\" />\n      <el-form\n        label-width=\"120px\"\n        ref=\"form\"\n        :model=\"form\"\n        v-if=\"datas.defaultFrom ? true : false\"\n      >\n        <div>\n          <el-row>\n            <div v-if=\"datas.processType === 'complete'\">\n              <el-col :span=\"24\" v-if=\"datas.nextUser ? datas.nextUser : true\">\n                <el-form-item prop=\"nextUser\" label=\"审批人:\">\n                  <el-select\n                    v-model=\"form.nextUser\"\n                    placeholder=\"请选择人员\"\n                    style=\"width: 100%\"\n                    value-key=\"userName\"\n                    :disabled=\"disabled\"\n                    clearable\n                    filterable\n                  >\n                    <el-option\n                      v-for=\"item in gzfzrOptions\"\n                      :key=\"item.userName\"\n                      :label=\"item.nickName\"\n                      :value=\"{\n                        userName: item.userName,\n                        nickName: item.nickName\n                      }\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </div>\n            <!--    根据人员组选人(多选)   -->\n            <div v-if=\"datas.processType === 'completeMany'\">\n              <el-col :span=\"24\">\n                <el-form-item prop=\"nextUser\" label=\"人员选择：\">\n                  <el-select\n                    v-model=\"form.nextUser\"\n                    placeholder=\"请选择人员\"\n                    style=\"width: 100%;\"\n                    value-key=\"userName\"\n                    multiple\n                    clearable\n                    filterable\n                  >\n                    <el-option\n                      v-for=\"item in gzfzrOptions\"\n                      :key=\"item.userName\"\n                      :label=\"item.nickName\"\n                      :value=\"{\n                        userName: item.userName,\n                        nickName: item.nickName\n                      }\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </div>\n            <el-col :span=\"24\">\n              <el-form-item\n                prop=\"comment\"\n                label=\"原因填报：\"\n                v-if=\"datas.processType === 'rollback'\"\n              >\n                <el-input\n                  style=\"width: 100%\"\n                  v-model=\"form.comment\"\n                  type=\"textarea\"\n                  placeholder=\"请输入原因\"\n                />\n              </el-form-item>\n            </el-col>\n            <!--\n            <el-col :span=\"24\">\n              <el-form-item\n                prop=\"comment\"\n                label=\"挂起审核：\"\n                v-if=\"datas.processType === 'gqrollback'\"\n              >\n                <el-input\n                  style=\"width: 100%\"\n                  v-model=\"form.comment\"\n                  type=\"textarea\"\n                  placeholder=\"请输入挂起审核原因\"\n                />\n              </el-form-item>\n            </el-col> -->\n          </el-row>\n        </div>\n      </el-form>\n      <span v-else>请确定是否提交</span>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"info\" @click=\"toClose\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"todoSubmit('form')\">确定</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { Loading } from \"element-ui\";\nimport { getUsers } from \"@/api/system/usergroup\";\nimport { completeTask } from \"@/api/activiti/processTask\";\nexport default {\n  name: \"index\",\n  props: {\n    /*流程发起必填数据\n        processData:{\n          processDefinitionKey:\"流程定义的key\",//必填\n          taskId:\"任务id，如果任务在代办列表时，会根据代办列表获取taskid，否则需传入业务id来确定task\",\n          businessKey:\"业务id，对应业务的主键\",//taskid和businessKey两者必须有其中一个才可以确定一个task\n          businessType:\"业务类型，用于区分不同的业务\"//必填\n          variables:\"拓展参数\"//流程定义中设置的参数,\n          nextUser:\"如果流程实例中并未配置每一个节点的处理人，则需要用户手动选择每一个节点的处理人\",\n          nextUserInfo:{},//手动传的审核人信息\n          processType:'complete,rollback',\n          defaultFrom:true,false 是否需要默认表单\n        }*/\n    processData: {\n      type: Object,\n      default() {\n        return {\n          defaultFrom: true,\n          processType: \"complete\"\n        };\n      }\n    },\n\n    //显示隐藏\n    isShow: {\n      type: Boolean,\n      default: false\n    },\n    //子组件默认参数\n    option: {\n      type: Object,\n      default() {\n        return { title: \"审批\" };\n      }\n    }\n  },\n\n  data() {\n    return {\n      disabled: false,\n      form: {},\n      datas: {},\n      loading: null,\n      gzfzrOptions: []\n    };\n  },\n  watch: {\n    processData: {\n      handler(newVal, oldVal) {\n        this.$nextTick(() => {\n          this.datas = { ...newVal };\n          let ishg = this.datas.variables.ishg;\n          let zy = this.datas.variables.zy;\n          let personGroupId = 59;\n          switch (ishg) {\n            case 2:\n              personGroupId = 51;\n              break;\n            case 3:\n              personGroupId = 57;\n              break;\n            case 7:\n              switch (zy) {\n                case \"变电设备\": //变电\n                  personGroupId = 123;\n                  break;\n                case \"配电设备\": //配电\n                  personGroupId = 128;\n                  break;\n                case \"输电设备\": //输电\n                  personGroupId = 129;\n                  break;\n              }\n              break;\n          }\n          getUsers({\n            personGroupId: personGroupId,\n            deptId: 0,\n            deptName: \"\"\n          }).then(res => {\n            this.gzfzrOptions = res.data;\n          });\n        });\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  mounted() {},\n  methods: {\n    //流程提交\n    async todoSubmit() {\n      this.datas.routePath = this.$route.path;\n      // debugger;\n      if (this.form.nextUser) {\n        //处理人多选\n        if (this.form.nextUser.length > 0) {\n          let nextUser = \"\";\n          this.form.nextUser.forEach(e => {\n            nextUser += e.userName + \",\";\n          });\n          //去掉最后一个逗号\n          if (nextUser.length > 0) {\n            nextUser = nextUser.substr(0, nextUser.length - 1);\n          }\n          this.datas.nextUser = nextUser;\n        } else {\n          this.datas.nextUser = this.form.nextUser.userName;\n          this.datas.nextUserNickName = this.form.nextUser.nickName;\n        }\n      } else {\n        if (this.processData.nextUserInfo) {\n          //手动传的用户信息\n          if (this.processData.nextUserInfo.length > 0) {\n            let nextUser = \"\";\n            let nextNick = \"\";\n            this.processData.nextUserInfo.forEach(e => {\n              nextUser += e.userName + \",\";\n              nextNick += e.nickName + \",\";\n            });\n            //去掉最后一个逗号\n            if (nextUser.length > 0) {\n              nextUser = nextUser.substr(0, nextUser.length - 1);\n            }\n            if (nextNick.length > 0) {\n              nextNick = nextNick.substr(0, nextNick.length - 1);\n            }\n            this.datas.nextUser = nextUser;\n            this.datas.nextUserNickName = nextNick;\n          } else {\n            this.datas.nextUser = this.processData.nextUserInfo.userName;\n            this.datas.nextUserNickName = this.processData.nextUserInfo.nickName;\n          }\n        } else {\n          this.datas.nextUser = undefined;\n          this.datas.nextUserNickName = undefined;\n        }\n      }\n      if (\n        !this.datas.nextUser &&\n        this.datas.processType === \"complete\" &&\n        this.datas.defaultFrom\n      ) {\n        this.$message({\n          type: \"error\",\n          message: \"请选择人员!\"\n        });\n        return;\n      }\n      if (this.form.comment) {\n        this.datas.variables.comment = this.form.comment;\n      }\n      this.$nextTick(() => {\n        // 以服务的方式调用的 Loading 需要异步关闭\n        this.loading = Loading.service({\n          lock: true, //lock的修改符--默认是false\n          text: \"流程进行中，请稍后\", //显示在加载图标下方的加载文案\n          spinner: \"el-icon-loading\", //自定义加载图标类名\n          background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n          target: document.querySelector(\"#dialogAct\")\n        });\n      });\n      let resultData;\n      try {\n        let { code, data } = await completeTask(this.datas);\n        if (code === \"0000\") {\n          resultData = data;\n        }\n        if (code) {\n          this.$nextTick(() => {\n            // 以服务的方式调用的 Loading 需要异步关闭\n            this.loading.close();\n          });\n        }\n      } catch (e) {\n        this.$nextTick(() => {\n          // 以服务的方式调用的 Loading 需要异步关闭\n          this.loading.close();\n        });\n      }\n      if (resultData) {\n        resultData.processType = this.datas.processType;\n        this.$emit(\"todoData\", resultData);\n      }\n      this.toClose();\n    },\n    toClose() {\n      this.$set(this.form,'nextUser',\"\")\n      this.$set(this.form,'comment',\"\")\n      this.$emit(\"toClose\", \"close\");\n    }\n  }\n};\n</script>\n"]}]}