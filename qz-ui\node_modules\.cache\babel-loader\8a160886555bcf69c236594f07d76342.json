{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\gfdzcz\\components\\fwzz_dj.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\gfdzcz\\components\\fwzz_dj.vue", "mtime": 1751368578944}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["fwzz_dj.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4WA;;AACA;;AACA;;AACA;;AACA;;AACA;;AAKA;;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAbA;AAYA;eAIA;AACA,EAAA,IAAA,EAAA,SADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,UAAA,EAAA,mBAAA;AAAA,IAAA,YAAA,EAAA,qBAAA;AAAA,IAAA,QAAA,EAAA,iBAAA;AAAA,IAAA,QAAA,EAAA;AAAA,GAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA,MAAA,OAAA,EAAA,EADA;AACA;AACA,MAAA,WAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,IAFA;AAGA,MAAA,gBAAA,EAAA,KAHA;AAIA,MAAA,gBAAA,EAAA;AACA,QAAA,QAAA,EAAA;AADA,OAJA;AAOA,MAAA,aAAA,EAAA,KAPA;AAQA;AACA,MAAA,eAAA,EAAA;AACA,QAAA,EAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA;AAFA,OATA;AAaA;AACA,MAAA,gBAAA,EAAA,KAdA;AAeA;AACA,MAAA,MAAA,EAAA,KAhBA;AAiBA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,oBAAA,EAAA,UADA;AAEA,QAAA,WAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,YAHA;AAIA,QAAA,SAAA,EAAA,EAJA;AAKA,QAAA,WAAA,EAAA,IALA;AAMA,QAAA,QAAA,EAAA,EANA;AAOA,QAAA,WAAA,EAAA;AAPA,OAlBA;AA2BA;AACA,MAAA,cAAA,EAAA,KA5BA;AA6BA,MAAA,MAAA,EAAA,EA7BA;AA6BA;AACA,MAAA,QAAA,EAAA,EA9BA;AA+BA,MAAA,YAAA,EAAA,KA/BA;AAgCA;AACA,MAAA,cAAA,EAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAjCA;AAkCA,MAAA,QAAA,EAAA,IAlCA;AAmCA,MAAA,mBAAA,EAAA,KAnCA;AAqCA,MAAA,OAAA,EAAA,EArCA;AAsCA,MAAA,UAAA,EAAA,KAtCA;AAuCA,MAAA,IAAA,EAAA;AACA,QAAA,EAAA,EAAA,EADA;AAEA,QAAA,MAAA,EAAA;AAFA,OAvCA;AA2CA,MAAA,MAAA,EAAA;AACA,QAAA,KAAA,EAAA;AADA,OA3CA;AA8CA;AACA,MAAA,GAAA,EAAA,EA/CA;AAgDA;AACA,MAAA,UAAA,EAAA,EAjDA;AAkDA;AACA,MAAA,MAAA,EAAA,IAnDA;AAoDA;AACA,MAAA,QAAA,EAAA,IArDA;AAsDA;AACA,MAAA,KAAA,EAAA,EAvDA;AAwDA,MAAA,MAAA,EAAA,EAxDA;AAyDA,MAAA,aAAA,EAAA,KAzDA;AA0DA,MAAA,aAAA,EAAA,KA1DA;AA2DA,MAAA,iBAAA,EAAA,KA3DA;AA4DA;AACA,MAAA,SAAA,EAAA,EA7DA;;AA8DA;;;AAGA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,KAAA,EAAA,EADA;AAEA,UAAA,KAAA,EAAA,EAFA;AAGA,UAAA,GAAA,EAAA,EAHA;AAIA,UAAA,GAAA,EAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,SAAA,EAAA,IAJA;AAKA,UAAA,OAAA,EAAA,CACA;AAAA,YAAA,KAAA,EAAA,KAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WADA,EAEA;AAAA,YAAA,KAAA,EAAA,KAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAFA;AALA,SADA,EAWA;AACA,UAAA,KAAA,EAAA,KADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,KAHA;AAIA,UAAA,aAAA,EAAA,EAJA;AAKA,UAAA,OAAA,EAAA;AALA,SAXA,EAkBA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAlBA,EAmBA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,KAAA,EAAA,OAFA;AAGA,UAAA,IAAA,EAAA,MAHA;AAIA,UAAA,QAAA,EAAA,WAJA;AAKA,UAAA,MAAA,EAAA;AALA,SAnBA,EA0BA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA;AACA;AA3BA;AAPA,OAjEA;AAsGA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA;AACA;;AACA;;;;;;;;;;;;;AAPA;AAZA,OAtGA;AAwIA,MAAA,MAAA,EAAA;AACA,QAAA,EAAA,EAAA;AADA,OAxIA;AA2IA,MAAA,SAAA,EAAA;AACA,QAAA,EAAA,EAAA,CADA;;AAEA;AACA,QAAA,IAAA,EAAA;AAHA,OA3IA;AAgJA,MAAA,KAAA,EAAA;AACA,QAAA,GAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CADA;AAEA,QAAA,GAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAFA;AAGA,QAAA,EAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,QAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAHA;AAIA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAVA;AAaA,QAAA,GAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAbA;AAcA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAdA,OAhJA;AAkKA,MAAA,OAAA,EAAA,EAlKA,CAkKA;;AAlKA,KAAA;AAoKA,GAxKA;AAyKA,EAAA,OAzKA,qBAyKA;AACA;AACA,SAAA,OAAA,GAFA,CAGA;AACA;AACA;;AACA,SAAA,UAAA;AACA,GAhLA;AAkLA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,WAFA,uBAEA,GAFA,EAEA;AAAA;;AACA;AACA,UAAA,GAAA,CAAA,KAAA,KAAA,KAAA,IAAA,GAAA,CAAA,KAAA,IAAA,GAAA,CAAA,KAAA,KAAA,EAAA,EAAA;AACA,YAAA,IAAA,GAAA;AACA,UAAA,MAAA,EAAA,GAAA,CAAA;AADA,SAAA;AAGA,4CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,KAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,gBAAA,IAAA,CAAA,KAAA,KAAA,KAAA,EAAA;AACA,qBAAA,IAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;AACA;AACA,WAJA;AAKA,SANA;AAOA;AACA,KAhBA;;AAiBA;;;AAGA,IAAA,UApBA,sBAoBA,GApBA,EAoBA;AAAA;;AACA,0CAAA;AAAA,QAAA,MAAA,EAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KAxBA;AAyBA;AACA,IAAA,UA1BA,wBA0BA;AAAA;;AACA,iCAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAAA,QAAA,EAAA;AACA,SAFA;AAGA,QAAA,MAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;;AACA,QAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,IAAA,KAAA,EAAA;AACA,mBAAA,IAAA,CAAA,OAAA,GAAA,MAAA,CAAA,OAAA;AACA;AACA,SAJA;AAKA,OAVA;AAWA,KAtCA;AAuCA,IAAA,YAvCA,wBAuCA,GAvCA,EAuCA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,EAAA,KAAA,EAAA,EAAA;;AADA;AAAA,uBAEA,MAAA,CAAA,UAAA,CAAA,GAAA,CAFA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,KA1CA;AA2CA;AACA,IAAA,UA5CA,wBA4CA;AACA,WAAA,gBAAA,GAAA,KAAA;AACA,WAAA,IAAA,CAAA,GAAA,GAAA,KAAA,SAAA,CAAA,KAAA;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,KAAA,SAAA,CAAA,IAAA;AACA,WAAA,OAAA,GAAA,CACA;AAAA,QAAA,KAAA,EAAA,KAAA,SAAA,CAAA,MAAA;AAAA,QAAA,KAAA,EAAA,KAAA,SAAA,CAAA;AAAA,OADA,CAAA;AAGA,KAnDA;AAoDA;AACA,IAAA,sBArDA,kCAqDA,IArDA,EAqDA;AAAA;;AACA,WAAA,QAAA,CAAA,OAAA,CAAA,OAAA;;AACA,UAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,aAAA,EADA,CAEA;;AACA,YAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,MAAA,CAAA,KAAA,CAAA,QAAA,CAAA,kBAAA,CAAA,GAAA;AACA,WAFA;AAGA,SAJA,MAIA;AACA,eAAA,KAAA,CAAA,QAAA,CAAA,cAAA;AACA;AACA,OAVA,MAUA;AACA;AACA,aAAA,SAAA,GAAA,IAAA,CAAA,CAAA,CAAA;AACA;AACA,KArEA;AAsEA,IAAA,eAtEA,6BAsEA;AAAA;;AACA;AACA,UAAA,KAAA,IAAA,CAAA,GAAA,KAAA,EAAA,IAAA,KAAA,IAAA,CAAA,GAAA,KAAA,SAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,WAAA;AACA;AACA,OALA,CAMA;;;AACA,UAAA,MAAA,GAAA,EAAA;AACA,WAAA,OAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,KAAA,IAAA,MAAA,CAAA,IAAA,CAAA,GAAA,EAAA;AACA,UAAA,MAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AACA,eAAA,KAAA;AACA,OALA;AAMA,WAAA,eAAA,CAAA,IAAA,GAAA,MAAA,CAdA,CAcA;;AACA,WAAA,gBAAA,GAAA,IAAA;AACA,KAtFA;AAuFA,IAAA,gBAvFA,8BAuFA;AACA,WAAA,gBAAA,GAAA,KAAA;AACA,KAzFA;AA0FA,IAAA,YA1FA,wBA0FA,IA1FA,EA0FA;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA,IAAA,CADA,CACA;AACA,KA5FA;AA6FA;AACA,IAAA,MA9FA,kBA8FA,CA9FA,EA8FA;AACA,WAAA,YAAA;AACA,KAhGA;AAiGA;AACA,IAAA,aAlGA,2BAkGA;AACA,WAAA,MAAA,GAAA,KAAA;AACA,KApGA;AAqGA;AACA,IAAA,QAtGA,oBAsGA,IAtGA,EAsGA;AACA,WAAA,SAAA,CAAA;AAAA,QAAA,IAAA,EAAA,IAAA;AAAA,QAAA,IAAA,EAAA,KAAA;AAAA,OAAA,EAAA;AAAA,QAAA,WAAA,EAAA;AAAA,OAAA;AACA,KAxGA;AAyGA;AACA,IAAA,aA1GA,2BA0GA;AACA,WAAA,YAAA,GAAA,KAAA;AACA,KA5GA;AA6GA;AACA,IAAA,YA9GA,wBA8GA,GA9GA,EA8GA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AADA;AAAA,uBAEA,8BAAA,MAAA,CAAA,WAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,sBAEA,IAFA;AAEA,gBAAA,IAFA,sBAEA,IAFA;AAGA,gBAAA,MAAA,CAAA,QAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,YAAA,GAAA,IAAA;;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,KAnHA;AAoHA;AACA,IAAA,cArHA,0BAqHA,GArHA,EAqHA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,WAAA,MAAA,GACA,mFACA,GAAA,CAAA,KADA,GAEA,KAFA,GAGA,IAAA,IAAA,GAAA,OAAA,EAJA;AAKA,KA5HA;AA6HA;AACA,IAAA,UA9HA,sBA8HA,IA9HA,EA8HA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,GADA,GACA,EADA;AAEA,gBAAA,GAAA,GAAA;AAAA,kBAAA,KAAA,EAAA,IAAA,CAAA,WAAA;AAAA,kBAAA,MAAA,EAAA,YAAA;AAAA,kBAAA,EAAA,EAAA;AAAA,iBAAA;AACA,gDAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,sBAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,oBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA,EADA,CAEA;;;AACA,oBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,oBAAA,MAAA,CAAA,OAAA;AACA;AACA,iBAPA;;AAHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KAzIA;;AA0IA;;;AAGA,IAAA,OA7IA,mBA6IA,MA7IA,EA6IA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,MAAA,CAAA,MAAA,+DAAA,MAAA,CAAA,MAAA,GAAA,MAAA;AACA,gBAAA,KAHA,GAGA,MAAA,CAAA,MAHA;AAAA;AAAA,uBAIA,2BAAA,KAAA,CAJA;;AAAA;AAAA;AAIA,gBAAA,IAJA,wBAIA,IAJA;AAIA,gBAAA,IAJA,wBAIA,IAJA;;AAKA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AARA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,KAvJA;;AAwJA;;;AAGA,IAAA,MA3JA,oBA2JA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;;AAMA,gBAAA,OAAA,CAAA,gBAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,aAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,KAAA,GAAA,OAAA;AACA,gBAAA,KAVA,GAUA,OAAA,CAAA,SAVA;AAAA;AAAA,uBAWA,yBAAA,KAAA,CAXA;;AAAA;AAWA,gBAAA,IAXA;AAYA,gBAAA,OAAA,CAAA,gBAAA,CAAA,QAAA,GAAA,IAAA,CAAA,IAAA,CAAA,OAAA;;AAZA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA,KAxKA;;AAyKA;;;AAGA,IAAA,MA5KA,oBA4KA;AACA,WAAA,IAAA,GAAA,EAAA;AACA,WAAA,IAAA,CAAA,MAAA,GAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,MAAA,GAAA,cAAA;AACA,WAAA,QAAA,GAAA,KAAA,CANA,CAOA;;AACA,UAAA,GAAA,GAAA,IAAA,IAAA,EAAA;AACA,UAAA,IAAA,GAAA,GAAA,CAAA,WAAA,EAAA,CATA,CASA;;AACA,UAAA,KAAA,GAAA,GAAA,CAAA,QAAA,EAAA,CAVA,CAUA;;AACA,UAAA,IAAA,GAAA,GAAA,CAAA,OAAA,EAAA,CAXA,CAWA;;AACA,UAAA,IAAA,GAAA,GAAA,CAAA,QAAA,EAAA;AACA,UAAA,OAAA,GAAA,GAAA,CAAA,UAAA,EAAA;AACA,UAAA,OAAA,GAAA,GAAA,CAAA,UAAA,EAAA;AACA,MAAA,KAAA,GAAA,KAAA,GAAA,CAAA;AACA,MAAA,KAAA,GAAA,KAAA,CAAA,QAAA,GAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA;AACA,MAAA,IAAA,GAAA,IAAA,CAAA,QAAA,GAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA;;AACA,UAAA,IAAA,CAAA,QAAA,GAAA,MAAA,IAAA,CAAA,EAAA;AACA;AACA,QAAA,IAAA,GAAA,MAAA,IAAA;AACA;;AACA,UAAA,OAAA,CAAA,QAAA,GAAA,MAAA,IAAA,CAAA,EAAA;AACA;AACA,QAAA,OAAA,GAAA,MAAA,OAAA;AACA;;AACA,UAAA,OAAA,CAAA,QAAA,GAAA,MAAA,IAAA,CAAA,EAAA;AACA;AACA,QAAA,OAAA,GAAA,MAAA,OAAA;AACA;;AACA,UAAA,WAAA,aAAA,IAAA,cAAA,KAAA,cAAA,IAAA,cAAA,IAAA,cAAA,OAAA,cAAA,OAAA,CAAA;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,WAAA,CA/BA,CAgCA;;AACA,UAAA,SAAA,GAAA,KAAA,MAAA,CAAA,OAAA;AACA,WAAA,IAAA,CAAA,GAAA,GAAA,SAAA,CAAA,IAAA;AACA,WAAA,OAAA,GAAA,CAAA;AAAA,QAAA,KAAA,EAAA,SAAA,CAAA,QAAA;AAAA,QAAA,KAAA,EAAA,SAAA,CAAA;AAAA,OAAA,CAAA;AACA,KAhNA;;AAiNA;;;AAGA,IAAA,SApNA,qBAoNA,GApNA,EAoNA;AACA,WAAA,MAAA,GAAA,gBAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,IAAA,mCAAA,GAAA;;AACA,UAAA,KAAA,IAAA,CAAA,MAAA,IAAA,KAAA,EAAA;AACA,aAAA,QAAA,GAAA,IAAA;AACA,OAFA,MAEA;AACA,aAAA,QAAA,GAAA,KAAA;AACA;;AACA,WAAA,OAAA,GAAA,CAAA;AAAA,QAAA,KAAA,EAAA,GAAA,CAAA,KAAA;AAAA,QAAA,KAAA,EAAA,GAAA,CAAA;AAAA,OAAA,CAAA;AACA,WAAA,OAAA,GAAA,CAAA;AAAA,QAAA,KAAA,EAAA,GAAA,CAAA,KAAA;AAAA,QAAA,KAAA,EAAA,GAAA,CAAA;AAAA,OAAA,CAAA;AACA,KAhOA;;AAiOA;;;AAGA,IAAA,UApOA,sBAoOA,GApOA,EAoOA;AACA,WAAA,MAAA,GAAA,kBAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,IAAA,mCAAA,GAAA;;AACA,UAAA,KAAA,IAAA,CAAA,MAAA,IAAA,KAAA,EAAA;AACA,aAAA,QAAA,GAAA,IAAA;AACA,OAFA,MAEA;AACA,aAAA,QAAA,GAAA,KAAA;AACA;;AACA,WAAA,OAAA,GAAA,CAAA;AAAA,QAAA,KAAA,EAAA,GAAA,CAAA,KAAA;AAAA,QAAA,KAAA,EAAA,GAAA,CAAA;AAAA,OAAA,CAAA;AACA,WAAA,OAAA,GAAA,CAAA;AAAA,QAAA,KAAA,EAAA,GAAA,CAAA,KAAA;AAAA,QAAA,KAAA,EAAA,GAAA,CAAA;AAAA,OAAA,CAAA;AACA,KAhPA;;AAiPA;;;AAGA,IAAA,SApPA,uBAoPA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,sBAAA,KAAA,EAAA;AACA,wBAAA;AACA,sBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,sBAAA,OAAA,CAAA,WAAA,CAAA,IAAA,GAAA,KAAA;AACA,sDAAA,OAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,4BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,0BAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,0BAAA,OAAA,CAAA,SAAA,CACA;AAAA,4BAAA,IAAA,EAAA,GAAA,CAAA,IAAA;AAAA,4BAAA,IAAA,EAAA;AAAA,2BADA,EAEA;AACA,4BAAA,WAAA,EAAA,OAAA,CAAA,WAAA,CAAA,WADA;AAEA,4BAAA,IAAA,EAAA,OAAA,CAAA,WAAA,CAAA,IAFA;AAGA,4BAAA,aAAA,EAAA;AAHA,2BAFA;AAQA,yBAXA,CAYA;;;AACA,wBAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA,CAbA,CAcA;;AACA,wBAAA,OAAA,CAAA,OAAA;;AACA,wBAAA,OAAA,CAAA,aAAA,GAAA,KAAA;AACA,wBAAA,OAAA,CAAA,aAAA,GAAA,KAAA;AACA,wBAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,uBAnBA;AAoBA,qBAvBA,CAuBA,OAAA,CAAA,EAAA,CAAA;AACA,mBAzBA,MAyBA;AACA,oBAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;;AACA,2BAAA,KAAA;AACA;AACA,iBA9BA;;AADA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgCA,KApRA;AAqRA;AACA,IAAA,SAtRA,qBAsRA,IAtRA,EAsRA,MAtRA,EAsRA;AACA,UAAA,GAAA,mCAAA,IAAA,CAAA,IAAA,CAAA;;AACA,UAAA,IAAA,CAAA,IAAA,KAAA,UAAA,EAAA;AACA,aAAA,cAAA,CAAA,KAAA,GAAA,cAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,MAAA,CAAA,WAAA,CAFA,CAEA;;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,aAAA,WAAA,CAAA,IAAA,GAAA,MAAA,CAAA,IAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AACA,aAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,OAPA,MAOA,IAAA,IAAA,CAAA,IAAA,KAAA,iBAAA,EAAA;AACA,YAAA,KAAA,IAAA,CAAA,MAAA,IAAA,IAAA,EAAA;AACA,eAAA,cAAA,CAAA,KAAA,GAAA,cAAA;AACA;;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,MAAA,CAAA,WAAA,CAJA,CAIA;;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,iBAAA;AACA,aAAA,WAAA,CAAA,IAAA,GAAA,MAAA,CAAA,IAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AACA,aAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,aAAA,WAAA,CAAA,SAAA,CAAA,aAAA,GAAA,MAAA,CAAA,aAAA;AACA,OAVA,MAUA;AACA,aAAA,cAAA,CAAA,KAAA,GAAA,QAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AACA,aAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA;;AACA,WAAA,MAAA,GAAA,IAAA;AACA,KAjTA;;AAkTA;;;AAGA,IAAA,YArTA,wBAqTA,GArTA,EAqTA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAKA,IALA,CAKA,YAAA;AACA;AACA,4CAAA,GAAA,CAAA,KAAA,EAAA,IAAA,CAAA,gBAAA;AAAA,wBAAA,IAAA,QAAA,IAAA;;AACA,wBAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,OAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,SADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;;AAIA,sBAAA,OAAA,CAAA,OAAA;AACA,qBANA,MAMA;AACA,sBAAA,OAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,OADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;AAIA;AACA,mBAbA;AAcA,iBArBA,EAsBA,KAtBA,CAsBA,YAAA;AACA,kBAAA,OAAA,CAAA,QAAA,CAAA;AACA,oBAAA,IAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;AAIA,iBA3BA;;AADA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA6BA,KAlVA;;AAmVA;;;AAGA,IAAA,gBAtVA,8BAsVA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,sBAAA,KAAA,EAAA;AACA,wBAAA;AACA,sBAAA,OAAA,CAAA,IAAA,CAAA,KAAA,GAAA,OAAA,CAAA,MAAA,CAAA,KAAA;AACA,sBAAA,OAAA,CAAA,IAAA,CAAA,EAAA,GAAA,CAAA;AACA,sBAAA,OAAA,CAAA,IAAA,CAAA,MAAA,GAAA,KAAA;AACA,sDAAA,OAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,4BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,0BAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,0BAAA,OAAA,CAAA,OAAA;AACA;;AACA,wBAAA,OAAA,CAAA,aAAA,GAAA,KAAA;AACA,uBANA;AAOA,qBAXA,CAWA,OAAA,CAAA,EAAA,CAAA;AACA,mBAbA,MAaA;AACA,oBAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;;AACA,2BAAA,KAAA;AACA;AACA,iBAlBA;;AADA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoBA,KA1WA;AA2WA;AACA,IAAA,WA5WA,yBA4WA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,WAAA,gBAAA,GAAA,KAAA;AACA,KAhXA;;AAiXA;;;;AAIA,IAAA,qBArXA,iCAqXA,SArXA,EAqXA;AACA,WAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,KAAA;AAAA,OAAA,CAAA;AACA,WAAA,MAAA,GAAA,SAAA,CAAA,MAAA,KAAA,CAAA;AACA,WAAA,QAAA,GAAA,CAAA,SAAA,CAAA,MAAA;AACA,WAAA,UAAA,GAAA,SAAA;AACA,KA1XA;AA2XA,IAAA,WA3XA,yBA2XA;AACA,WAAA,MAAA,GAAA;AACA,QAAA,EAAA,EAAA;AADA,OAAA;AAGA,KA/XA;AAgYA;AACA,IAAA,UAjYA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA,kBAiYA;AACA,UAAA,CAAA,KAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,YAAA;AACA;AACA;;AACA,UAAA;AACA,YAAA,QAAA,GAAA,cAAA;AACA,YAAA,SAAA,GAAA,gBAAA;AACA,YAAA,MAAA,GAAA;AACA,UAAA,IAAA,EAAA,KAAA,UADA;AAEA,UAAA,GAAA,EAAA;AAFA,SAAA;AAIA,QAAA,UAAA,CAAA,MAAA,EAAA,QAAA,CAAA;AACA,OARA,CAQA,OAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,OAAA;AACA;AACA,KAjZA;;AAkZA;;;AAGA,IAAA,gBArZA,8BAqZA;AAAA;;AACA,0CAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;;AACA,QAAA,OAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,IAAA,KAAA,EAAA;AACA,mBAAA,IAAA,CAAA,OAAA,GAAA,OAAA,CAAA,OAAA;AACA;AACA,SAJA;AAKA,OAPA;AAQA,KA9ZA;AA+ZA;AACA,IAAA,iBAhaA,+BAgaA;AACA,WAAA,gBAAA,GAAA,IAAA;AACA,KAlaA;AAmaA;AACA,IAAA,kBApaA,8BAoaA,MApaA,EAoaA;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,MAAA,CAAA,IAAA;AACA,WAAA,IAAA,CAAA,MAAA,GAAA,MAAA,CAAA,KAAA;AACA,KAvaA;AAwaA;AACA,IAAA,6BAzaA,yCAyaA,MAzaA,EAyaA;AACA,WAAA,gBAAA,GAAA,MAAA;AACA;AA3aA;AAlLA,C", "sourcesContent": ["<template>\n  <div>\n    <!--搜索条件-->\n    <el-filter\n      ref=\"filter1\"\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n      @handleReset=\"filterReset\"\n      @handleEvent=\"handleEvent\"\n    />\n\n    <el-white class=\"button-group\">\n      <div style=\"height: 50px\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          v-hasPermi=\"['fwzz:button:add']\"\n          @click=\"addRow\"\n          >新增</el-button\n        >\n        <!--<el-button type=\"danger\" icon=\"el-icon-plus\" @click=\"handleDelete\">删除</el-button>-->\n        <el-button type=\"success\" icon=\"el-icon-download\" @click=\"exportWord\"\n          >导出</el-button\n        >\n      </div>\n      <div>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"handleSelectionChange\"\n          height=\"58vh\"\n        >\n          <el-table-column\n            slot=\"table_eight\"\n            align=\"center\"\n            fixed=\"right\"\n            style=\"display: block\"\n            label=\"操作\"\n            width=\"160\"\n            :resizable=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                @click=\"getDetails(scope.row)\"\n                title=\"详情\"\n                class=\"el-icon-view\"\n              ></el-button>\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                v-show=\"scope.row.createBy == currentUser\"\n                @click=\"updateRow(scope.row)\"\n                title=\"编辑\"\n                class=\"el-icon-edit\"\n              ></el-button>\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                v-show=\"\n                  scope.row.createBy == currentUser ||\n                    'admin' === $store.getters.name\n                \"\n                @click=\"handleDelete(scope.row)\"\n                title=\"删除\"\n                class=\"el-icon-delete\"\n              ></el-button>\n              <!--<el-button type=\"text\" size=\"small\" @click=\"showTimeLine(scope.row)\">流程查看</el-button>\n              <el-button type=\"text\" size=\"small\" @click=\"showProcessImg(scope.row)\">流程图</el-button>-->\n            </template>\n          </el-table-column>\n        </comp-table>\n      </div>\n    </el-white>\n\n    <!--防误装置解锁工具使用登记-->\n    <el-dialog\n      :title=\"titles\"\n      :visible.sync=\"isShowDetails\"\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"130px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row>\n          <el-col :span=\"8\">\n            <el-form-item label=\"状态\" prop=\"status\">\n              <el-input\n                style=\"width:100%\"\n                :disabled=\"true\"\n                v-model=\"form.status\"\n              >\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"分公司\" prop=\"fgs\">\n              <el-select\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.fgs\"\n                placeholder=\"请选择分公司\"\n                @change=\"fgsChangeFun\"\n              >\n                <el-option\n                  v-for=\"item in fgsList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"光伏电站\" prop=\"bdz\">\n              <el-select\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.bdz\"\n                clearable\n                placeholder=\"请选择光伏电站\"\n              >\n                <el-option\n                  v-for=\"item in bdzList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"时间\" prop=\"sj\">\n              <el-date-picker\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.sj\"\n                type=\"datetime\"\n                placeholder=\"选择日期时间\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\"\n            ><!--@focus=\"sbmcDialog\"-->\n            <el-form-item label=\"设备名称\" prop=\"sbmc\">\n              <el-input\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.sbmc\"\n                placeholder=\"请选择或输入设备\"\n                @input=\"change($event)\"\n              >\n                <i\n                  slot=\"suffix\"\n                  class=\"el-input__icon el-icon-search\"\n                  @click=\"showAssetSelect\"\n                ></i>\n              </el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"21\">\n            <el-form-item label=\"工作任务\" prop=\"gzrw\">\n              <el-input\n                style=\"width:100%\"\n                type=\"textarea\"\n                :rows=\"3\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.gzrw\"\n                placeholder=\"请输入内容\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"3\">\n            <el-white class=\"mb8 pull-right\">\n              <el-button type=\"primary\" @click=\"getCzp\" :disabled=\"isDisabled\"\n                >关联操作票</el-button\n              >\n            </el-white>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"使用原因\" prop=\"syyy\">\n              <el-input\n                style=\"width:100%\"\n                type=\"textarea\"\n                :rows=\"3\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.syyy\"\n                placeholder=\"请输入内容\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"记录人\" prop=\"jlr\">\n              <el-select\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.jlr\"\n                clearable\n                placeholder=\"请选择记录人\"\n              >\n                <el-option\n                  v-for=\"item in jlrList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"记录时间\" prop=\"jlsj\">\n              <el-date-picker\n                v-model=\"form.jlsj\"\n                :disabled=\"isDisabled\"\n                type=\"datetime\"\n                style=\"width:100%\"\n                placeholder=\"选择日期时间\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <!--<div v-if=\"form.status == '待修改'\">\n          <el-row>\n            <el-col :span=\"24\">\n              <el-form-item label=\"意见\" prop=\"bz\">\n                <el-input style=\"width:100%\" type=\"textarea\" :rows=\"2\" :disabled=\"true\" v-model=\"form.bz\"\n                          placeholder=\"(审核退回意见)\"/>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>-->\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"handleClose\">取 消</el-button>\n        <el-button\n          v-if=\"\n            titles == '防误装置解锁工具使用登记' ||\n              titles == '修改防误装置解锁工具使用登记'\n          \"\n          type=\"primary\"\n          @click=\"submitFormFwzzjs\"\n          >保 存\n        </el-button>\n        <el-button\n          v-if=\"\n            titles == '防误装置解锁工具使用登记' ||\n              titles == '详情查看防误装置解锁工具使用登记'\n          \"\n          type=\"primary\"\n          @click=\"getSbForm\"\n          >提 交\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!--试验设备选择弹框-->\n    <el-dialog\n      title=\"设备选择\"\n      :visible.sync=\"isShowSysbDialog\"\n      width=\"60%\"\n      v-if=\"isShowSysbDialog\"\n      v-dialogDrag\n    >\n      <sysb-selected\n        @handleAcceptSbData=\"handleAcceptSbData\"\n        :selectedSbParam=\"selectedSbParam\"\n        @closeSysbSelectDialog=\"handleControlSysbSelectDialog\"\n      ></sysb-selected>\n    </el-dialog>\n\n    <!--工作流需要-->\n    <activiti\n      :processData=\"processData\"\n      :isShow=\"isShow\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n    ></activiti>\n    <time-line\n      :value=\"timeLineShow\"\n      :timeData=\"timeData\"\n      @closeTimeLine=\"colseTimeLine\"\n    />\n    <!--流程图查看-->\n    <el-dialog\n      title=\"审批流程图\"\n      :visible.sync=\"openLoadingImg\"\n      width=\"56%\"\n      append-to-body\n      :close-on-click-modal=\"false\"\n    >\n      <div>\n        <img :src=\"imgSrc\" style=\"width: 100%\" />\n      </div>\n    </el-dialog>\n\n    <!--关联操作票-->\n    <el-dialog :title=\"title\" :visible.sync=\"isShowDetailsCzp\" width=\"50%\">\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\">\n        <el-row>\n          <el-col>\n            <!--主表信息-->\n            <el-table\n              :data=\"propTableDataCzp.colFirst\"\n              :disabled=\"isDisabledCzp\"\n              height=\"300\"\n              border\n              stripe\n              style=\"width: 100%\"\n              max-height=\"40vh\"\n              @selection-change=\"handleSelectionChanges\"\n              ref=\"czpTable\"\n            >\n              <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n              <el-table-column\n                align=\"center\"\n                prop=\"bdzmc\"\n                label=\"光伏电站名称\"\n                width=\"200\"\n              >\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"请选择光伏电站名称\"\n                      :disabled=\"isDisabledCzp\"\n                      v-model=\"scope.row.bdzmcs\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" prop=\"czrw\" label=\"操作任务\">\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      type=\"textarea\"\n                      :rows=\"2\"\n                      placeholder=\"请输入操作任务\"\n                      :disabled=\"isDisabledCzp\"\n                      v-model=\"scope.row.czrw\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"handleClose\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"saveRowCzp\">确 认 </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n//流程\nimport activiti from \"com/activiti\";\nimport timeLine from \"com/timeLine\";\nimport { HistoryList } from \"@/api/activiti/processTask\";\nimport { getListLsp } from \"@/api/yxgl/gfyxgl/gfdzczp\";\nimport { getBdzDataListSelected as getBdzSelectList} from \"@/api/dagangOilfield/asset/gfsbtz\";\nimport {\n  getListFwzzjs,\n  saveOrUpdateFwzzjs,\n  removeFwzzjs\n} from \"@/api/yxgl/gfyxgl/gfzbgl\";\nimport DeviceTree from \"@/views/dagangOilfield/bzgl/sbbzk/deviceTree\";\n//新选择设备\nimport sysbSelected from \"@/views/dagangOilfield/bzgl/sybzk/sybgjlcomment/sysbSelected\";\nimport { getFgsOptions } from \"@/api/yxgl/gfyxgl/gfzbgl\";\n\nexport default {\n  name: \"fwzz_dj\",\n  components: { DeviceTree, sysbSelected, activiti, timeLine },\n  data() {\n    return {\n      fgsList: [], //分公司下拉框\n      currentUser: this.$store.getters.name,\n      isShowDetailsCzp: false,\n      propTableDataCzp: {\n        colFirst: []\n      },\n      isDisabledCzp: false,\n      //主设备选择传递子组件参数\n      selectedSbParam: {\n        lx: \"gf\",\n        sbmc: \"\"\n      },\n      //主设备弹出框\n      isShowSysbDialog: false,\n      //工作流弹窗\n      isShow: false,\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"fwzzjsgj\",\n        businessKey: \"\",\n        businessType: \"防误装置解锁工具使用\",\n        variables: {},\n        defaultFrom: true,\n        nextUser: \"\",\n        processType: \"complete\"\n      },\n      //流程图查看\n      openLoadingImg: false,\n      imgSrc: \"\", //流程图查看地址\n      timeData: [],\n      timeLineShow: false,\n      //弹出框标题\n      activitiOption: { title: \"上报\" },\n      isShowYj: true,\n      ZbDialogFormVisible: false,\n\n      bdzList: [],\n      isDisabled: false,\n      form: {\n        lx: \"\",\n        status: \"\"\n      },\n      formSh: {\n        wfzz1: \"\"\n      },\n      // 多选框选中的id\n      ids: [],\n      // 多选框选选中的数据\n      selectData: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 对话框标题\n      title: \"\",\n      titles: \"\",\n      isShowDetails: false,\n      isShowBdfgssh: false,\n      isShowBdfgsshForm: false,\n      //操作票列表选中后\n      assetInfo: {},\n      /**\n       *  防误装置解锁工具使用登记\n       *  */\n      filterInfo: {\n        data: {\n          dlqbh: \"\",\n          sjArr: [],\n          djr: \"\",\n          bdz: \"\"\n        },\n        fieldList: [\n          {\n            label: \"状态\",\n            type: \"select\",\n            value: \"status\",\n            clearable: true,\n            options: [\n              { label: \"待上报\", value: \"待上报\" },\n              { label: \"待修改\", value: \"待修改\" }\n            ]\n          },\n          {\n            label: \"分公司\",\n            type: \"select\",\n            value: \"fgs\",\n            checkboxValue: [],\n            options: []\n          },\n          { label: \"光伏电站\", type: \"select\", value: \"bdz\", options: [] },\n          {\n            label: \"时间\",\n            value: \"sjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"设备名称\", type: \"input\", value: \"sbmc\" }\n          /*{ label: '工作任务', type: 'input', value: 'gzrw' }*/\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"status\", label: \"状态\", minWidth: \"60\" },\n          { prop: \"fgsCn\", label: \"分公司\", minWidth: \"80\" },\n          { prop: \"bdzmc\", label: \"光伏电站\", minWidth: \"80\" },\n          { prop: \"sj\", label: \"时间\", minWidth: \"120\" },\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"100\" }\n          /*{ prop: 'gzrw', label: '工作任务', minWidth: '130' },*/\n          /*{\n            prop: 'operation',\n            label: '操作',\n            fixed: 'right',\n            minWidth: '110px',\n            style: { display: 'block' },\n            operation: [\n              { name: '修改', clickFun: this.updateRow },\n              { name: '详情', clickFun: this.getDetails },\n              { name: '提交', clickFun: this.getSb }\n              /!*{ name: '附件查看', clickFun: this.FjInfoList },*!/\n            ]\n          }*/\n        ]\n      },\n      params: {\n        lx: 1\n      },\n      paramsCzp: {\n        lx: 2,\n        /*status: '已办结',*/\n        sfbj: 1\n      },\n      rules: {\n        fgs: [{ required: true, message: \"分公司不能为空\", trigger: \"select\" }],\n        bdz: [{ required: true, message: \"光伏电站不能为空\", trigger: \"select\" }],\n        sj: [{ required: true, message: \"时间不能为空\", trigger: \"change\" }],\n        sbmc: [\n          { required: true, message: \"巡视类别不能为空\", trigger: \"blur\" }\n        ],\n        gzrw: [\n          { required: true, message: \"工作任务不能为空\", trigger: \"blur\" }\n        ],\n        syyy: [\n          { required: true, message: \"使用原因不能为空\", trigger: \"blur\" }\n        ],\n        jlr: [{ required: true, message: \"记录人不能为空\", trigger: \"blur\" }],\n        jlsj: [\n          { required: true, message: \"记录时间不能为空\", trigger: \"change\" }\n        ]\n      },\n      jlrList: [] //记录人下拉框\n    };\n  },\n  created() {\n    //列表查询\n    this.getData();\n    //获取光伏电站下拉框数据\n    // this.getBdzSelectList()\n    //获取分公司下拉框\n    this.getFgsList();\n  },\n\n  methods: {\n    //下拉框change事件\n    handleEvent(val) {\n      //光伏电站查询下拉框选项根据所选分公司带出来\n      if (val.label === \"fgs\" && val.value && val.value !== \"\") {\n        let form = {\n          ssdwbm: val.value\n        };\n        getBdzSelectList(form).then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value === \"bdz\") {\n              return (item.options = res.data);\n            }\n          });\n        });\n      }\n    },\n    /**\n     * 获取光伏电站下拉框数据\n     */\n    getBdzList(val) {\n      getBdzSelectList({ ssdwbm: val }).then(res => {\n        this.bdzList = res.data;\n      });\n    },\n    //获取分公司下拉框\n    getFgsList() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.fgsList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"fgs\") {\n            return (item.options = this.fgsList);\n          }\n        });\n      });\n    },\n    async fgsChangeFun(val) {\n      this.$set(this.form, \"bdz\", \"\");\n      await this.getBdzList(val);\n    },\n    //保存选中的操作票\n    saveRowCzp() {\n      this.isShowDetailsCzp = false;\n      this.form.bdz = this.assetInfo.bdzmc;\n      this.form.gzrw = this.assetInfo.czrw;\n      this.bdzList = [\n        { label: this.assetInfo.bdzmcs, value: this.assetInfo.bdzmc }\n      ];\n    },\n    //行选中数据\n    handleSelectionChanges(rows) {\n      this.$message.success(\"选中的数据\");\n      if (rows.length > 1) {\n        this.$message.warning(\"只能选中一条数据！！！\");\n        //清空选中方法\n        if (rows) {\n          rows.forEach(row => {\n            this.$refs.czpTable.toggleRowSelection(row);\n          });\n        } else {\n          this.$refs.czpTable.clearSelection();\n        }\n      } else {\n        //获取当前选中的单条数据\n        this.assetInfo = rows[0];\n      }\n    },\n    showAssetSelect() {\n      // this.assetSelect = true;\n      if (this.form.bdz === \"\" || this.form.bdz === undefined) {\n        this.$message.warning(\"请选择光伏电站名称\");\n        return;\n      }\n      // 拿到光伏电站汉字\n      let sbmcmc = \"\";\n      this.bdzList.forEach(item => {\n        if (item.value == this.form.bdz) {\n          sbmcmc = item.label;\n        }\n        return false;\n      });\n      this.selectedSbParam.sbmc = sbmcmc; //所属位置\n      this.isShowSysbDialog = true;\n    },\n    closeAssetSelect() {\n      this.isShowSysbDialog = false;\n    },\n    getAssetInfo(info) {\n      this.form.sbmc = info.sbmc; //主设备名称\n    },\n    //清空\n    change(e) {\n      this.$forceUpdate();\n    },\n    //关闭弹窗\n    closeActiviti() {\n      this.isShow = false;\n    },\n    //回退按钮\n    handleTh(type) {\n      this.getSbFsBj({ type: type, data: this.form }, { defaultForm: false });\n    },\n    //关闭流程查看页面\n    colseTimeLine() {\n      this.timeLineShow = false;\n    },\n    //流程查看\n    async showTimeLine(row) {\n      this.processData.businessKey = row.objId;\n      let { code, data } = await HistoryList(this.processData);\n      this.timeData = data;\n      this.timeLineShow = true;\n    },\n    //流程图片查看\n    showProcessImg(row) {\n      this.openLoadingImg = true;\n      this.imgSrc =\n        \"/activiti-api/process/read-resource?processDefinitionKey=fwzzjsgj&businessKey=\" +\n        row.objId +\n        \"&t=\" +\n        new Date().getTime();\n    },\n    //工作流回传数据\n    async todoResult(data) {\n      let row = {};\n      row = { objId: data.businessKey, status: \"待分公司五防专责审核\", lx: 2 };\n      saveOrUpdateFwzzjs(row).then(res => {\n        if (res.code === \"0000\") {\n          this.$message.success(\"操作成功\");\n          //重置page页从1开始\n          this.tableAndPageInfo.pager.pageResize = \"Y\";\n          this.getData();\n        }\n      });\n    },\n    /**\n     * 根据表格名称获取对应的数据\n     */\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        const { data, code } = await getListFwzzjs(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n        }\n      } catch (e) {}\n    },\n    /**\n     * 关联操作票\n     * */\n    async getCzp() {\n      /*if (this.form.bdz) {\n        this.isShowDetailsCzp = true\n      } else {\n        this.isShowDetailsCzp = false\n        this.$message.warning(\"请先选择光伏电站！！！\")\n      }*/\n      this.isShowDetailsCzp = true;\n      this.isDisabledCzp = true;\n      this.title = \"选择操作票\";\n      let param = this.paramsCzp;\n      let data = await getListLsp(param);\n      this.propTableDataCzp.colFirst = data.data.records;\n    },\n    /**\n     * 新增\n     */\n    addRow() {\n      this.form = {};\n      this.form.status = \"新建\";\n      this.isShowDetails = true;\n      this.isDisabled = false;\n      this.titles = \"防误装置解锁工具使用登记\";\n      this.isShowYj = false;\n      // 拿到日期、星期、天气\n      let now = new Date();\n      let year = now.getFullYear(); //得到年份\n      let month = now.getMonth(); //得到月份\n      let date = now.getDate(); //得到日期\n      let hour = now.getHours();\n      let minutes = now.getMinutes();\n      let seconds = now.getSeconds();\n      month = month + 1;\n      month = month.toString().padStart(2, \"0\");\n      date = date.toString().padStart(2, \"0\");\n      if (hour.toString().length != 2) {\n        // 拼接时，如果长度为1，保存报错\n        hour = \"0\" + hour;\n      }\n      if (minutes.toString().length != 2) {\n        // 拼接分钟，如果长度为1，保存报错\n        minutes = \"0\" + minutes;\n      }\n      if (seconds.toString().length != 2) {\n        // 拼接秒，如果长度为1，保存报错\n        seconds = \"0\" + seconds;\n      }\n      let defaultDate = `${year}-${month}-${date} ${hour}:${minutes}:${seconds}`;\n      this.form.jlsj = defaultDate;\n      //获取当前登录用户对象\n      let loginUser = this.$store.getters;\n      this.form.jlr = loginUser.name;\n      this.jlrList = [{ label: loginUser.nickName, value: loginUser.name }];\n    },\n    /**\n     * 修改\n     */\n    updateRow(row) {\n      this.titles = \"修改防误装置解锁工具使用登记\";\n      this.isDisabled = false;\n      this.isShowDetails = true;\n      this.form = { ...row };\n      if (this.form.status == \"已修改\") {\n        this.isShowYj = true;\n      } else {\n        this.isShowYj = false;\n      }\n      this.bdzList = [{ label: row.bdzmc, value: row.bdz }];\n      this.jlrList = [{ label: row.jlrCn, value: row.jlr }];\n    },\n    /**\n     * 详情\n     */\n    getDetails(row) {\n      this.titles = \"详情查看防误装置解锁工具使用登记\";\n      this.isDisabled = true;\n      this.isShowDetails = true;\n      this.form = { ...row };\n      if (this.form.status == \"已修改\") {\n        this.isShowYj = true;\n      } else {\n        this.isShowYj = false;\n      }\n      this.bdzList = [{ label: row.bdzmc, value: row.bdz }];\n      this.jlrList = [{ label: row.jlrCn, value: row.jlr }];\n    },\n    /**\n     * 新增框上报\n     * */\n    async getSbForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          try {\n            this.processData.defaultFrom = true;\n            this.processData.jxgs = false;\n            saveOrUpdateFwzzjs(this.form).then(res => {\n              if (res.code === \"0000\") {\n                this.$message.success(\"操作成功\");\n                this.getSbFsBj(\n                  { data: res.data, type: \"completeByGroup\" },\n                  {\n                    defaultForm: this.processData.defaultFrom,\n                    jxgs: this.processData.jxgs,\n                    personGroupId: 18\n                  }\n                );\n              }\n              //恢复分页\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              //重新查数据\n              this.getData();\n              this.isShowBdfgssh = false;\n              this.isShowDetails = false;\n              this.isDisabled = false;\n            });\n          } catch (e) {}\n        } else {\n          this.$message.error(\"校验未通过！\");\n          return false;\n        }\n      });\n    },\n    //上报发送办结\n    getSbFsBj(args, isShow) {\n      let row = { ...args.data };\n      if (args.type === \"complete\") {\n        this.activitiOption.title = \"发送给分公司五防专责审核\";\n        this.processData.defaultFrom = isShow.defaultForm; //是否显示选人员\n        this.processData.processType = \"complete\";\n        this.processData.jxgs = isShow.jxgs;\n        this.processData.businessKey = row.objId;\n        this.processData.variables.pass = true;\n      } else if (args.type === \"completeByGroup\") {\n        if (this.form.status == \"新建\") {\n          this.activitiOption.title = \"发送给分公司五防专责审核\";\n        }\n        this.processData.defaultFrom = isShow.defaultForm; //是否显示选人员\n        this.processData.processType = \"completeByGroup\";\n        this.processData.jxgs = isShow.jxgs;\n        this.processData.businessKey = row.objId;\n        this.processData.variables.pass = true;\n        this.processData.variables.personGroupId = isShow.personGroupId;\n      } else {\n        this.activitiOption.title = \"回退原因提报\";\n        this.processData.defaultFrom = true;\n        this.processData.processType = \"rollback\";\n        this.processData.businessKey = row.objId;\n        this.processData.variables.pass = true;\n      }\n      this.isShow = true;\n    },\n    /**\n     * 删除按钮\n     */\n    async handleDelete(row) {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          //防误装置解锁工具使用登记\n          removeFwzzjs(row.objId).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    /**\n     * 提交 防误装置解锁工具使用登记 表单\n     */\n    async submitFormFwzzjs() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          try {\n            this.form.wfzz1 = this.formSh.wfzz1;\n            this.form.lx = 1;\n            this.form.status = \"待上报\";\n            saveOrUpdateFwzzjs(this.form).then(res => {\n              if (res.code === \"0000\") {\n                this.$message.success(\"操作成功\");\n                this.getData();\n              }\n              this.isShowDetails = false;\n            });\n          } catch (e) {}\n        } else {\n          this.$message.error(\"校验未通过！\");\n          return false;\n        }\n      });\n    },\n    //关闭按钮\n    handleClose() {\n      this.isShowDetails = false;\n      this.isShowBdfgssh = false;\n      this.isShowDetailsCzp = false;\n    },\n    /**\n     * 多选款选中数据\n     * @param row\n     */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.objId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n      this.selectData = selection;\n    },\n    filterReset() {\n      this.params = {\n        lx: 1\n      };\n    },\n    //导出word\n    exportWord() {\n      if (!this.selectData.length > 0) {\n        this.$message.warning(\"请先选中要导出的数据\");\n        return;\n      }\n      try {\n        let fileName = \"防误装置解锁工具使用记录\";\n        let exportUrl = \"yxFwzzjsgjsyjl\";\n        let params = {\n          data: this.selectData,\n          url: exportUrl\n        };\n        exportWord(params, fileName);\n      } catch (e) {\n        this.$message.error(\"导出失败！\");\n      }\n    },\n    /**\n     * 获取光伏电站下拉框数据\n     */\n    getBdzSelectList() {\n      getBdzSelectList({}).then(res => {\n        this.bdzList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"bdz\") {\n            return (item.options = this.bdzList);\n          }\n        });\n      });\n    },\n    //试验设备选择事件\n    sysbSelectedClick() {\n      this.isShowSysbDialog = true;\n    },\n    //组件接受设备参数数据\n    handleAcceptSbData(sbData) {\n      this.form.sbmc = sbData.sbmc;\n      this.form.sysbid = sbData.objId;\n    },\n    //控制关闭试验设备弹出框\n    handleControlSysbSelectDialog(isShow) {\n      this.isShowSysbDialog = isShow;\n    }\n  }\n};\n</script>\n\n<style scoped></style>\n"], "sourceRoot": "src/views/dagangOilfield/czpgl/gfdzcz/components"}]}