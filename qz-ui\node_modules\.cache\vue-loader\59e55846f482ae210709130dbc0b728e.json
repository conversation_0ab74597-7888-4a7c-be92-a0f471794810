{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\sblxwh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\sblxwh.vue", "mtime": 1727417331943}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["sblxwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkHA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "sblxwh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row :gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card class=\"aside_height\" shadow=\"never\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备类型</span>\n          </div>\n          <div style=\" overflow: auto;height: 84.5vh\">\n            <el-tree\n              ref=\"elTree\"\n              :data=\"treeData\"\n              node-key=\"code\"\n              highlight-current\n              default-expand-all\n              :props=\"defaultProps\"\n              @node-click=\"handleNodeClick\"\n            />\n          </div>\n        </el-card>\n      </el-col>\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 120, itemWidth: 180 }\"\n          @handleReset=\"filterReset\"\n        />\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button\n              type=\"primary\"\n              v-hasPermi=\"['bzsbflbzgl:button:add']\"\n              icon=\"el-icon-plus\"\n              @click=\"addDeviceClassify\"\n              >新增</el-button\n            >\n          </div>\n          <comp-table\n            ref=\"deviceTypeTable\"\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            v-loading=\"loading\"\n            height=\"70.1vh\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  type=\"text\"\n                  v-hasPermi=\"['bzsbflbzgl:button:update']\"\n                  @click=\"updateDeviceClassify(scope.row)\"\n                  class=\"updateBtn el-icon-edit\"\n                  title=\"修改\"\n                ></el-button>\n                <el-button\n                  type=\"text\"\n                  @click=\"getDeviceClassifyDetails(scope.row)\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n                <el-button\n                  type=\"text\"\n                  v-if=\"\n                    scope.row.createBy === $store.getters.name ||\n                      $store.getters.hasSuperRole\n                  \"\n                  title=\"删除\"\n                  icon=\"el-icon-delete\"\n                  @click=\"deleteDeviceClassify(scope.row)\"\n                >\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <dialogForm\n      v-dialogDrag\n      ref=\"dialogForm\"\n      label-width=\"150px\"\n      out-width=\"50%\"\n      :reminder=\"reminder\"\n      :rows=\"rows\"\n      @save=\"saveDeviceClassify\"\n    />\n\n    <el-dialog\n      v-dialogDrag\n      :append-to-body=\"true\"\n      title=\"技术参数/设备部件维护\"\n      :visible.sync=\"isShowParamsAndParts\"\n    >\n      <technical-and-part\n        :device-type-data=\"selectedRowData\"\n        @closeParamDialog=\"closeParamDialog\"\n      >\n      </technical-and-part>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport TechnicalAndPart from \"@/views/dagangOilfield/bzgl/sbbzk/technicalAndPart\";\nimport deviceClassifyMixin from \"@/mixins/deviceClassifyMixin\";\nimport dialogForm from \"com/dialogFrom/dialogForm\";\nimport {\n  saveOrUpdateMwtUdGySblx,\n  deleteDeviceClassify\n} from \"@/api/dagangOilfield/bzgl/sblxwh/sblxwh\";\n\nexport default {\n  components: { TechnicalAndPart, dialogForm },\n  name: \"sblxwh\",\n  mixins: [deviceClassifyMixin],\n  data() {\n    return {\n      resolve: {},\n      filterInfo: {\n        data: {},\n        fieldList: [\n          { label: \"设备类型编码\", type: \"input\", value: \"sblxbm\" },\n          { label: \"设备类型名称\", type: \"input\", value: \"sblx\" }\n        ]\n      },\n      reminder: \"修改\",\n      rows: 2,\n      formList: [\n        {\n          label: \"设备类型编码：\",\n          value: \"\",\n          name: \"sblxbm\",\n          default: true,\n          type: \"input\",\n          rules: {\n            required: true,\n            message: \"请输入设备类型编码\",\n            trigger: \"blur\"\n          }\n        },\n        {\n          label: \"设备类型名称：\",\n          value: \"\",\n          name: \"sblx\",\n          default: true,\n          type: \"input\",\n          rules: {\n            required: true,\n            message: \"请输入设备类型名称\",\n            trigger: \"blur\"\n          }\n        },\n        {\n          label: \"父类：\",\n          value: \"\",\n          name: \"fsblx\",\n          default: true,\n          type: \"disabled\",\n          rules: { required: true, message: \"请输入父类\", trigger: \"blur\" }\n        },\n        {\n          label: \"排序：\",\n          value: \"\",\n          name: \"px\",\n          default: true,\n          type: \"input\",\n          rules: { required: true, message: \"输入排序\", trigger: \"blur\" }\n        },\n        {\n          label: \"备注：\",\n          value: \"\",\n          name: \"bz\",\n          default: true,\n          type: \"textarea\",\n          rules: { required: false, message: \"请输入备注\" }\n        },\n        {\n          label: \"id：\",\n          value: \"\",\n          name: \"objId\",\n          default: true,\n          hidden: false,\n          type: \"input\",\n          rules: { required: false, message: \"请输入编码\" }\n        },\n        {\n          label: \"专业性质：\",\n          value: \"\",\n          type: \"input\",\n          name: \"zyxz\",\n          hidden: false,\n          default: true,\n          rules: { required: false, message: \"请选择专业性质\" }\n        },\n        {\n          label: \"父设备类型编码：\",\n          value: \"\",\n          type: \"input\",\n          name: \"fsblxid\",\n          hidden: false,\n          default: true,\n          rules: { required: false, message: \"请选择父设备类型编码\" }\n        },\n        {\n          label: \"设备类型全路径：\",\n          value: \"\",\n          type: \"input\",\n          name: \"fullpath\",\n          hidden: false,\n          default: true,\n          rules: { required: false, message: \"请选择设备类型全路径\" }\n        }\n      ],\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        total: 0,\n        fsblxid: \"\",\n        sblxbm: \"\"\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageResize: \"\",\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: false,\n          serialNumber: false\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"sblxbm\", label: \"设备类型编码\" },\n          { prop: \"sblx\", label: \"设备类型名称\" },\n          { prop: \"fsblx\", label: \"父类\" }\n        ]\n      },\n      loading: false,\n      treeNodeData: {},\n      isShowParamsAndParts: false,\n      selectedRowData: {},\n      selectedRowDataArr: [],\n      dialogVisible: false,\n      dialogTitle: \"\",\n      form: {},\n      defaultProps: {\n        children: \"children\",\n        label: \"name\",\n        isLeaf: (data, node) => {\n          if (node.level === 1) {\n            return true;\n          }\n        }\n      },\n      treeData: []\n    };\n  },\n  watch: {\n    isShowParamsAndParts(val) {\n      if (val) {\n        const el = this.$el.querySelector(\".el-dialog\");\n        el.style.left = 0;\n        el.style.top = 0;\n      }\n    }\n  },\n  created() {\n    this.loadNode();\n    this.getData();\n  },\n  methods: {\n    filterReset() {\n      this.queryParams = {\n        pageNum: 1,\n        pageSize: 10,\n        total: 0,\n        fsblxid: \"\",\n        sblxbm: \"\"\n      };\n      this.treeNodeData = {};\n      this.$refs.elTree.setCurrentKey(null);\n    },\n    handleNodeClick(node) {\n      this.treeNodeData = node;\n      const { code } = node;\n      this.queryParams.fsblxid = code;\n      this.queryParams.sblxbm = \"\";\n      this.getData();\n    },\n    handleSelectionChange(row) {\n      this.selectedRowDataArr = row;\n    },\n    async getData(params) {\n      this.loading = true;\n      if (this.queryParams.sblxbm == \"\") {\n        this.queryParams.fsblxid === \"\"\n          ? (this.queryParams.fsblxid = \"sb\")\n          : this.queryParams.fsblxid;\n      } else {\n        this.queryParams.fsblxid = \"\";\n      }\n      this.queryParams = { ...this.queryParams, ...params };\n      const param = this.queryParams;\n      const data = await this.fetchDeviceClassifyData(param);\n      if (data) {\n        this.tableAndPageInfo.tableData = data.records;\n        this.tableAndPageInfo.pager.total = data.total;\n      }\n      this.loading = false;\n    },\n    addDeviceClassify() {\n      if (this.treeNodeData.jscsNum > 0 || this.treeNodeData.sbbjNum > 0) {\n        this.$message({\n          message: \"该设备已维护技术参数或部件,不可新增!\",\n          type: \"warning\"\n        });\n      } else {\n        this.reminder = \"新增\";\n        //初始化formList数据\n        this.formList = this.$options.data().formList;\n        const addForm = this.formList.map(item => {\n          if (item.name === \"fsblxid\") {\n            item.value = this.treeNodeData.code || \"sb\";\n          }\n          if (item.name === \"fsblx\") {\n            item.value = this.treeNodeData.name || \"设备\";\n          }\n          return item;\n        });\n        this.$refs.dialogForm.showzzc(addForm);\n      }\n    },\n    updateDeviceClassify(row) {\n      const updateList = this.formList.map(item => {\n        item.value = row[item.name];\n        return item;\n      });\n      this.reminder = \"修改\";\n      this.$refs.dialogForm.showzzc(updateList);\n    },\n    async deleteDeviceClassify(row) {\n      this.$confirm(\"确认删除选中数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(async () => {\n          let deleteAble = true;\n          let message = \"\";\n          let deviceTypeCode = [];\n          deviceTypeCode.push(row.sblxbm);\n          if (row.childNum > 0) {\n            deleteAble = false;\n            message += row.sblx;\n          }\n          if (deleteAble) {\n            const res = await deleteDeviceClassify({\n              ids: [row.objId],\n              sblxbmArr: deviceTypeCode\n            });\n            if (res.code === \"0000\") {\n              this.$message.success(\"删除成功\");\n              this.treeData = await this.fetchTreeNodeData();\n              this.getData();\n            } else {\n              this.$message.error(\"操作失败\");\n            }\n          } else {\n            this.$message.warning(\n              message + \"包含已维护子级设备,请优先删除子级设备后在删除该设备!\"\n            );\n          }\n        })\n        .catch(() => {\n        });\n    },\n    getDeviceClassifyDetails(row) {\n      const infoList = this.formList.map(item => {\n        item.value = row[item.name];\n        return item;\n      });\n      this.reminder = \"详情\";\n      this.$refs.dialogForm.showxq(infoList);\n    },\n    async saveDeviceClassify(formData) {\n      if (formData.fsblxid === \"sb\") {\n        formData.fsblx = \"设备\";\n        formData.cj = '0'\n      }\n      try {\n        const res = await saveOrUpdateMwtUdGySblx(formData);\n        if (res.code === \"0000\") {\n          this.$message.success(formData.objId ? \"修改成功\" : \"新增成功\");\n          this.treeData = await this.fetchTreeNodeData();\n          this.getData();\n        } else {\n          this.$message.error(\"操作失败\");\n        }\n      } catch (error) {\n        console.error(\"保存设备分类失败:\", error);\n        this.$message.error(\"保存失败，请稍后重试\");\n      }\n    },\n    async loadNode() {\n      // if (node.level === 0) {\n      //   resolve([{ name: \"设备\", code: \"sb\" }]);\n      // } else {\n      // const { code } = node.data;\n      this.treeData = await this.fetchTreeNodeData();\n      // resolve(children);\n      // }\n    },\n    isShowButton(row) {\n      return row.childNum === 0;\n    },\n    showParamDialog(row) {\n      this.selectedRowData = row;\n      this.isShowParamsAndParts = true;\n    },\n    closeParamDialog() {\n      this.isShowParamsAndParts = false;\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.box-card .el-card__header {\n  background: #11ba6d !important;\n}\n.box-card {\n  margin: 0;\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.tree {\n  overflow-y: hidden;\n  overflow-x: scroll;\n  width: 80px;\n  height: 500px;\n}\n\n.el-tree {\n  min-width: 100%;\n  display: inline-block !important;\n}\n\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  max-height: 79vh;\n  overflow: auto;\n}\n</style>\n<style></style>\n"]}]}