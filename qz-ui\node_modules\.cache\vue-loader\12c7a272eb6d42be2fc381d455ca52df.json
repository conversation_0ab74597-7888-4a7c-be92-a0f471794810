{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\lpbzk\\xsxmpz_pd.vue?vue&type=style&index=0&lang=css&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\lpbzk\\xsxmpz_pd.vue", "mtime": 1706897322623}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi8q5o6n5Yi2aW5wdXTovpPlhaXmoYbovrnmoYbmmK/lkKbmmL7npLoqLwouZWxJbnB1dD4+Pi5lbC1pbnB1dF9faW5uZXIgewogIGJvcmRlcjogMDsKfQoKLmJveC1jYXJkIHsKICBtYXJnaW4tYm90dG9tOiAydmggIWltcG9ydGFudDsKfQoKLmVsLXNlbGVjdCB7CiAgd2lkdGg6IDEwMCU7Cn0K"}, {"version": 3, "sources": ["xsxmpz_pd.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8dA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "xsxmpz_pd.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/lpbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row>\n      <!--基本信息查询及显示-->\n      <el-col>\n        <!--搜索条件-->\n        <el-filter :data=\"filterInfo.data\" :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 120, itemWidth: 230 }\" @handleReset=\"getReset\" @handleEvent=\"handleEvent\" />\n\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button type=\"primary\" v-hasPermi=\"['bzxsxmpe:button:add']\" icon=\"el-icon-plus\" @click=\"getInster\">新增\n            </el-button>\n            <el-button type=\"success\" icon=\"el-icon-download\" @click=\"exportExcel\">导出</el-button>\n          </div>\n          <comp-table :table-and-page-info=\"tableAndPageInfo\" @update:multipleSelection=\"selectChange\" height=\"69.8vh\"\n            v-loading=\"loading\">\n            <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\"\n              width=\"160\" :resizable=\"false\">\n              <template slot-scope=\"scope\">\n                <el-button @click=\"getUpdate(scope.row)\" v-hasPermi=\"['bzxsxmpe:button:update']\" type=\"text\"\n                  size=\"small\" title=\"修改\" class='el-icon-edit'></el-button>\n                <el-button @click=\"getDetails(scope.row)\" type=\"text\" size=\"small\" title=\"详情\" class=\"el-icon-view\">\n                </el-button>\n                <el-button type=\"text\"  size=\"small\" title=\"删除\" v-if=\"scope.row.createBy === $store.getters.name\" v-hasPermi=\"['bzxsxmpe:button:delete']\" icon=\"el-icon-delete\" @click=\"deleteRow(scope.row.objId)\">\n            </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n\n      </el-col>\n    </el-row>\n\n\n    <!-- 详情/新增/修改 -->\n    <el-dialog :title=title :visible.sync=\"isShowDetails\" width=\"60% \" v-dialogDrag>\n      <el-form label-width=\"180px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n\n        <!--主表信息-->\n        <div>\n          <!--巡视项目基本信息-->\n          <el-col :span=\"12\">\n            <el-form-item label=\"专业：\" prop=\"zy\">\n              <el-select v-model=\"form.zy\" disabled=\"disabled\" @change=\"getBdzAndPds\" placeholder=\"请输入内容\">\n                <el-option v-for=\"item in zyList\" :key=\"item.label\" :label=\"item.value\" :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"设备类型：\" prop=\"sblx\">\n              <el-select v-model=\"form.sblx\" :disabled=\"isDisabled\" placeholder=\"请输入内容\">\n                <el-option v-for=\"item in sblxList\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"巡视类别：\" prop=\"xslb\">\n              <el-select style=\"width: 100%\" v-model=\"form.xslb\" :disabled=\"isDisabled\" placeholder=\"请选择巡视类别\">\n                <el-option v-for=\"item in xslbList\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </div>\n\n        <!--子表信息-->\n        <div>\n          <el-table :data=\"propTableData.colFirst\" :disabled=\"isDisabled\" height=\"230\" border stripe\n            style=\"width: 100%\">\n            <el-table-column type=\"index\" width=\"50\" align=\"center\" label=\"序号\" />\n            <el-table-column align=\"center\" prop=\"xsbzx\" label=\"巡视标准项\">\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input placeholder=\"请输入巡视标准项\" :disabled=\"isDisabled\" type=\"textarea\" v-model=\"scope.row.xsbzx\">\n                  </el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" width=\"80\">\n              <!--子表新增按钮-->\n              <template slot=\"header\" slot-scope=\"scope\">\n                <el-button type=\"primary\" size=\"small\" :disabled=\"isDisabled\" icon=\"el-icon-plus\"\n                  @click=\"listFirstAdd(scope.$index, scope.row)\"></el-button>\n              </template>\n              <!--子表删除按钮-->\n              <template slot-scope=\"scope\">\n                <el-button type=\"text\" icon=\"el-icon-delete\" :disabled=\"isDisabled\"\n                  @click=\"listFirstDel(scope.$index, scope.row)\"></el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button v-if=\"title=='巡视项目增加' || title=='巡视项目修改'\" type=\"primary\" @click=\"saveRow\">确 认\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n\n<script>\nimport { getList, queryZb, saveOrUpdate, remove } from '@/api/dagangOilfield/bzgl/lpbzk/xsxmpz'\nimport { getSblxDataListSelected, } from '@/api/dagangOilfield/asset/bdsbtz'\nimport {exportExcel} from '@/api/bzgl/ysbzk/ysbzk'\n\nexport default {\n  name: 'xsxmpz',\n  watch: {\n    isShowDetails(val) {\n      if (val) {\n        const el = this.$el.querySelector('.el-dialog')\n        el.style.left = 0\n        el.style.top = 0\n      }\n    }\n  },\n  data() {\n    return {\n      loading: true,\n      //巡视类别\n      xslbListAll: {\n        pd: [\n          { label: '正常巡视', value: '正常巡视' },\n          { label: '特殊巡视', value: '特殊巡视' },\n        ],\n        bd: [\n          { label: '全面巡视', value: '全面巡视' },\n          { label: '特殊巡视', value: '特殊巡视' },\n        ],\n        sd: [\n          { label: '精细巡检', value: '精细巡检' },\n          { label: '电缆巡检', value: '电缆巡检' },\n          { label: '特殊巡视', value: '特殊巡视' },\n          { label: '通道巡检', value: '通道巡检' },\n        ],\n      },\n      xslbList: [],\n      // 多选框选中的id\n      ids: [],\n      //弹出框中表格数据\n      propTableData: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //标题\n      title: '',\n      //子标标题\n      zbtitle: '',\n      //详情弹框是否显示\n      isShowDetails: false,\n      //子表\n      ZbDialogFormVisible: false,\n      //是否禁用\n      isDisabled: false,\n      //专业下拉框\n      zyList: [{ label: '配电', value: '配电' }],\n      //地点下拉框\n      ddList: [{ label: 'xx变电站', value: 'xx变电站' }, { label: 'xx线路', value: 'xx线路' }, {\n        label: 'xx配电室',\n        value: 'xx配电室'\n      }],\n      //设备类型下拉框\n      sblxList: [],\n      //form表单\n      form: {\n        zy: '',\n        dd: '',\n        sblx: '',\n        bz: '',\n        colFirst: []\n      },\n      //子表表单\n      zbForm: {\n        //sxh: '',\n        xsbzx: ''\n      },\n      //列表及检索\n      filterInfo: {\n        data: {\n          zy: '',\n          dd: '',\n          sblx: '',\n          sxh: '',\n          xsbzx: '',\n          bz: ''\n        },//查询条件\n        fieldList: [\n          { label: '设备类型', value: 'sblx', type: 'selectCn', options: [], clearable: true, filterable: true },\n          {\n            label: '巡视类别',\n            value: 'xslb',\n            type: 'checkbox',\n            checkboxValue: [],\n            options: [],\n            clearable: true\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 3,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: '专业', prop: 'zy', minWidth: '120' },\n          { label: '设备类型', prop: 'sblxmc', minWidth: '120' },\n          { label: '巡视类别', prop: 'xslb', minWidth: '180' },\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        zy: '',\n        dd: '',\n        sblx: '',\n        bz: ''\n      },\n      rules: {\n        zy: [\n          { required: true, message: \"专业不能为空\", trigger: \"select\" },\n        ],\n        sblx: [\n          { required: true, message: \"设备类型不能为空\", trigger: \"select\" },\n        ],\n        xslb: [\n          { required: true, message: \"巡视类别不能为空\", trigger: \"select\" },\n        ]\n      },\n    }\n  },\n  created() {\n    //列表查询\n    this.getData();\n    this.handleEvent({ value: '配电', label: 'zy' });\n  },\n  methods: {\n    //列表查询\n    async getData(params) {\n      try {\n        this.loading = true\n        this.params = { ...this.params, ...params, ...{ zy: '配电' } }\n        const param = this.params\n        const { data, code } = await getList(param)\n        if (code === '0000') {\n          this.tableAndPageInfo.tableData = data.records\n          this.tableAndPageInfo.pager.total = data.total\n          this.loading = false\n        }\n      } catch (e) {\n      }\n    },\n    //根据主表查询子表方法\n    async getListZb(row) {\n      try {\n        const { data, code } = await queryZb({ objId: row.objId })\n        if (code === '0000') {\n          this.propTableData.colFirst = data\n        }\n      } catch (e) {\n      }\n    },\n\n    //新增按钮\n    getInster() {\n      this.propTableData.colFirst = []\n      this.title = '巡视项目增加'\n      this.isDisabled = false\n      this.form = { zy: '配电' }\n      this.getBdzAndPds(this.form.zy);\n      this.isShowDetails = true\n    },\n    //修改按钮\n    getUpdate(row) {\n      this.getListZb(row)\n      this.title = '巡视项目修改'\n      this.isDisabled = false\n      this.form = { ...row }\n      this.isShowDetails = true\n    },\n    //详情按钮\n    getDetails(row) {\n      this.getListZb(row)\n      this.title = '巡视项目详情查看'\n      this.form = { ...row }\n      this.isDisabled = true\n      this.isShowDetails = true\n    },\n    //保存按钮\n    async saveRow() {\n      this.$refs['form'].validate((valid) => {\n        if (valid) {\n          this.form.colFirst = this.propTableData.colFirst\n          this.form.objIdList = this.ids\n          saveOrUpdate(this.form).then(res => {\n            if (res.code === '0000') {\n              this.$message.success('操作成功')\n            }\n            //重置page页从1开始\n            this.tableAndPageInfo.pager.pageResize = 'Y'\n            this.getData()\n            this.isShowDetails = false\n          })\n        } else {\n          this.$message.error(\"校验未通过！\");\n          return false;\n        }\n      })\n    },\n    //删除按钮\n    async deleteRow(id) {\n      // if (this.ids.length < 1) {\n      //   this.$message.warning('请选择正确的数据！！！')\n      //   return\n      // }\n      let obj=[];\n        obj.push(id);\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        remove(obj).then(({ code }) => {\n          if (code === '0000') {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            })\n            //重置page页从1开始\n            this.tableAndPageInfo.pager.pageResize = 'Y'\n            this.getData()\n          } else {\n            this.$message({\n              type: 'error',\n              message: '删除失败!'\n            })\n          }\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        })\n      })\n    },\n    /*-----------------------------子表----------------------------*/\n    //表格新增\n    listFirstAdd() {\n      let row = {\n        objId: '',\n        //sxh: '',\n        xsbzx: ''\n      }\n      this.propTableData.colFirst.push(row)\n      this.propTableData.sel = row\n    },\n    //表格删除\n    listFirstDel(index, row) {\n      this.ids.push(row.objId)\n      console.log('this.ids.push(row.objId):' + index + '-' + this.ids)\n      this.propTableData.colFirst.splice(index, 1)\n    },\n    //重置按钮\n    getReset() {\n      this.params = {}\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.type === 'checkbox') {\n          item.checkboxValue = [];\n        }\n      })\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false\n    },\n    //筛选条件\n    selectChange(selection) {\n      this.ids = selection.map(item => item.objId)\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    //获取变电站和配电室\n    getBdzAndPds(val) {\n      // this.$set(this.form,'dd','')\n      this.$set(this.form, 'sblx', '')\n      if (val === '变电') {\n        // getBdzSelectList({}).then(res=>{\n        //   this.ddList = res.data\n        // })\n        this.getSblxDataListSelected(\"变电设备\")\n        //巡视类别\n        this.getXslbList('bd');\n      } else if (val === '配电') {\n        // getPdsTreeList({}).then(res=>{\n        //   let pdzOption=res.data[0].children.map(item=>{\n        //     let obj={}\n        //     obj.label=item.label\n        //     obj.value=item.id\n        //     return obj\n        //   })\n        //   this.ddList = pdzOption\n        // })\n        this.getSblxDataListSelected(\"配电设备\")\n        //巡视类别\n        this.getXslbList('pd');\n      } else if (val === '输电') {\n        // getPdsTreeList({}).then(res=>{\n        //   let pdzOption=res.data[0].children.map(item=>{\n        //     let obj={}\n        //     obj.label=item.label\n        //     obj.value=item.id\n        //     return obj\n        //   })\n        //   this.ddList = pdzOption\n        // })\n        this.getSblxDataListSelected(\"输电设备\")\n        //巡视类别\n        this.getXslbList('sd');\n      }\n    },\n    //获取巡视类别\n    getXslbList(value) {\n      this.xslbList = this.xslbListAll[value];\n    },\n    /**\n     * 获取设备类型下拉框数据\n     */\n    getSblxDataListSelected(value) {\n      getSblxDataListSelected({ type: value }).then(res => {\n        this.sblxList = res.data;\n      })\n    },\n    //下拉框change事件\n    handleEvent(val) {\n      if (val.label === 'zy' && val.value && val.value !== '') {\n        this.params.zy = val.value\n        if (val.value === '配电') {\n          getSblxDataListSelected({ type: \"配电设备\" }).then(res => {\n            this.sblxList = res.data;\n            this.filterInfo.fieldList.map(item => {\n              if (item.value == 'sblx') {\n                return item.options = res.data\n              }\n            })\n          })\n\n          //获取巡视类别\n          this.filterInfo.fieldList.map(item => {\n            if (item.value === 'xslb') {\n              return item.options = this.xslbListAll.pd;\n            }\n          })\n        }\n      }\n    },\n    //导出excel\n    exportExcel() {\n      // if(!this.selectData.length > 0){\n      //   this.$message.warning('请在左侧勾选要导出的数据')\n      //   return\n      // }\n      let fileName = \"配电巡视项目配置\";\n      let exportUrl = \"/bzXsxmpz\";\n      exportExcel(exportUrl, this.params, fileName);\n    }\n  }\n}\n</script>\n\n<style>\n/*控制input输入框边框是否显示*/\n.elInput>>>.el-input__inner {\n  border: 0;\n}\n\n.box-card {\n  margin-bottom: 2vh !important;\n}\n\n.el-select {\n  width: 100%;\n}\n</style>\n\n"]}]}