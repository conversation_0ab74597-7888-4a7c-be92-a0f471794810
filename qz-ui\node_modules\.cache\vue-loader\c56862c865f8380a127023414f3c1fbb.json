{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\dataChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\dataChart.vue", "mtime": 1706897323432}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["dataChart.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "dataChart.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk", "sourcesContent": ["<template>\r\n  <div class=\"defaultCls\">\r\n    <el-white class=\"titleCls_1\">\r\n      <el-row>\r\n        <el-col>\r\n          <div>\r\n            <el-divider direction=\"vertical\" class=\"vertical_d\"></el-divider>\r\n            <span class=\"tjfx_title\">{{ title }}</span>\r\n            <el-divider></el-divider>\r\n          </div>\r\n        </el-col>\r\n        <el-col>\r\n          <div ref=\"thisChart\" class=\"tjHeight3\"></div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-white>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from \"echarts\";\r\nimport { getCellData } from \"@/api/dagangOilfield/bzgl/sybglr\";\r\nexport default {\r\n  name: \"dataChart\",\r\n  props: {\r\n    cellId: {\r\n      type: String\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      title: \"试验数据对比\",\r\n      tjData: [],\r\n      tjx: [], //统计项\r\n      chart: null //统计图对象\r\n    };\r\n  },\r\n  created() {},\r\n  mounted() {\r\n    this.$nextTick(() => {});\r\n  },\r\n  methods: {\r\n    //查询统计数据\r\n    async getTjData() {\r\n      this.tjx = [];\r\n      this.tjData = [];\r\n      //查询数据\r\n      let params = {\r\n        cellId: this.cellId,\r\n        queryNum: \"10\"\r\n      };\r\n      getCellData(params).then(res => {\r\n        if (res.data.length > 1) {\r\n          res.data.forEach(item => {\r\n            let value = Number(item[Object.keys(item)[1]]);\r\n            if (value) {\r\n              this.tjData.push(value);\r\n              this.tjx.push(item[Object.keys(item)[0]].substring(0, 10));\r\n            }\r\n          });\r\n          if (res.data.length != this.tjData.length) {\r\n            this.$message.warning(\"存在空或异常的试验数值，已忽略\");\r\n          }\r\n          console.log(\"处理后的data\",this.tjData);\r\n          //展示统计图\r\n          this.showCharts();\r\n        } else {\r\n          this.$message.info(\"试验次数不足，无法对比\");\r\n        }\r\n      });\r\n    },\r\n    //初始化统计图\r\n    showCharts() {\r\n      let bar_dv = this.$refs.thisChart;\r\n      let myChart = echarts.init(bar_dv);\r\n      this.chart = myChart;\r\n\r\n      let option;\r\n      option = {\r\n        color: [\r\n          \"#4992ff\",\r\n          \"#7cffb2\",\r\n          \"#fddd60\",\r\n          \"#ff6e76\",\r\n          \"#58d9f9\",\r\n          \"#05c091\",\r\n          \"#ff8a45\",\r\n          \"#8d48e3\",\r\n          \"#dd79ff\"\r\n        ],\r\n        tooltip: {\r\n          trigger: \"axis\",\r\n          axisPointer: {\r\n            type: \"cross\",\r\n            crossStyle: {\r\n              color: \"#999\"\r\n            }\r\n          }\r\n        },\r\n        toolbox: {\r\n          feature: {\r\n            magicType: { show: true, type: [\"line\", \"bar\"] },\r\n            restore: { show: true },\r\n            saveAsImage: { show: true }\r\n          }\r\n        },\r\n        legend: {\r\n          top: \"left\"\r\n        },\r\n        xAxis: [\r\n          {\r\n            type: \"category\",\r\n            data: this.tjx,\r\n            axisPointer: {\r\n              type: \"shadow\"\r\n            },\r\n            axisLabel: { interval: 0, rotate: 25 }\r\n          }\r\n        ],\r\n        yAxis: [\r\n          {\r\n            type: \"value\",\r\n            min: 0,\r\n            axisLabel: {\r\n              formatter: \"{value}\"\r\n            }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            name: \"试验数值\",\r\n            type: \"line\",\r\n            barWidth: 25,\r\n            label: {\r\n              show: true,\r\n              position: \"top\"\r\n            },\r\n            tooltip: {\r\n              valueFormatter: function(value) {\r\n                return value;\r\n              }\r\n            },\r\n            data: this.tjData,\r\n            itemStyle: {\r\n              borderRadius: 5,\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: \"#abf1e0\" },\r\n                { offset: 1, color: \"#14c8d4\" }\r\n              ])\r\n            }\r\n          }\r\n        ]\r\n      };\r\n      option && myChart.setOption(option);\r\n    }\r\n  },\r\n  watch: {\r\n    cellId(newVal) {\r\n      if (newVal) {\r\n        this.getTjData();\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.defaultCls {\r\n  padding-top: 5px;\r\n  padding-bottom: 10px;\r\n}\r\n</style>\r\n"]}]}