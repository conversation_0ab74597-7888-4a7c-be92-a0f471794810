{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sdsbgl\\zxwh_edit.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sdsbgl\\zxwh_edit.vue", "mtime": 1706897324964}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldEd0c0J5WGwsIHNhdmVPclVwZGF0ZSB9IGZyb20gIkAvYXBpL2RhZ2FuZ09pbGZpZWxkL2Fzc2V0L3p4d2giOwppbXBvcnQgeyBMb2FkaW5nIH0gZnJvbSAiZWxlbWVudC11aSI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAienh3aF9lZGl0IiwKICBwcm9wczogewogICAgeGREYXRhOiB7CiAgICAgIHR5cGU6IE9iamVjdAogICAgfQogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGZvcm06IHt9LAogICAgICBndERhdGFzOiBbXSwgLy/lt6bkvqfmnYbloZTmlbDmja4KICAgICAgY3Vyckd0RGF0YXM6IFtdLCAvL+WPs+S+p+adhuWhlOaVsOaNrgogICAgICBzZWxlY3RSb3dzMTogW10sIC8v5bem5L6n5Yu+6YCJ5pWw5o2uCiAgICAgIHNlbGVjdFJvd3MyOiBbXSwgLy/lj7Pkvqfli77pgInmlbDmja4KICAgICAgbG9hZGluZzogbnVsbCwgLy/pga7nvanlsYIKICAgICAgb2xkSW5kZXg6IHVuZGVmaW5lZCwKICAgICAgbmV3SW5kZXg6IHVuZGVmaW5lZCwKICAgICAgZmxhZzogInN0YXJ0IgogICAgfTsKICB9LAogIG1vdW50ZWQoKSB7CiAgICB0aGlzLmZvcm0gPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMueGREYXRhKSk7IC8v5rex5bqm5ou36LSd5pWw5o2uCiAgICB0aGlzLmdldEd0RGF0YSgpOwogICAgdGhpcy5pbml0UmlnaHREYXRhKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvL+ihqOagvOihjOagt+W8jwogICAgdGFibGVSb3dDbGFzc05hbWUoeyByb3csIHJvd0luZGV4IH0pIHsKICAgICAgcm93LmluZGV4ID0gcm93SW5kZXg7CiAgICAgIHJldHVybiByb3cucm93Q2xzID8gcm93LnJvd0NscyA6ICIiOwogICAgfSwKICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2Uocm93LCBjb2x1bW4sIGV2ZW50KSB7CiAgICAgIGlmICh0aGlzLmZsYWcgPT09ICJzdGFydCIpIHsKICAgICAgICB0aGlzLm9sZEluZGV4ID0gcm93LmluZGV4OwogICAgICAgIHRoaXMuZmxhZyA9ICJlbmQiOwogICAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygi6K+354K55Ye76ZyA6KaB5pu05o2i5Yiw55qE6KGMIik7CiAgICAgIH0gZWxzZSBpZiAodGhpcy5mbGFnID09ICJlbmQiKSB7CiAgICAgICAgdGhpcy5uZXdJbmRleCA9IHJvdy5pbmRleDsKICAgICAgICB0aGlzLmZsYWcgPSAic3RhcnQiOwogICAgICAgIGNvbnN0IG9sZEN1cnJlbnRSb3cgPSB0aGlzLmN1cnJHdERhdGFzLnNwbGljZSh0aGlzLm9sZEluZGV4LCAxKVswXTsKICAgICAgICB0aGlzLmN1cnJHdERhdGFzLnNwbGljZSh0aGlzLm5ld0luZGV4LCAwLCBvbGRDdXJyZW50Um93KTsKICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuabtOaNoumhuuW6j+aIkOWKnyIpOwogICAgICB9CiAgICB9LAogICAgLy/mibnph4/lhbPogZTmnYbloZQKICAgIGNob29zZUZ1bigpIHsKICAgICAgaWYgKHRoaXMuc2VsZWN0Um93czEubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6K+36YCJ5oup6KaB5YWz6IGU55qE5p2G5aGU5pWw5o2uIik7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgbGV0IGxlZnRJZHMgPSBbXTsKICAgICAgICAvL+mHjeaWsOi1i+WAvOWPs+S+p+WIl+ihqAogICAgICAgIHRoaXMuc2VsZWN0Um93czEuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIGl0ZW0ucm93Q2xzID0gInN1Y2Nlc3Mtcm93IjsKICAgICAgICAgIGxlZnRJZHMucHVzaChpdGVtLmd0aWQpOwogICAgICAgICAgdGhpcy5jdXJyR3REYXRhcy5wdXNoKGl0ZW0pOwogICAgICAgIH0pOwogICAgICAgIGxldCBndERhdGFzID0gW107CiAgICAgICAgLy/ph43mlrDotYvlgLzlt6bkvqfliJfooagKICAgICAgICB0aGlzLmd0RGF0YXMuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIGlmICghbGVmdElkcy5pbmNsdWRlcyhpdGVtLmd0aWQpKSB7CiAgICAgICAgICAgIGl0ZW0ucm93Q2xzID0gIiI7CiAgICAgICAgICAgIGd0RGF0YXMucHVzaChpdGVtKTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgICB0aGlzLmd0RGF0YXMgPSBndERhdGFzOwogICAgICB9CiAgICB9LAogICAgLy/lj5bmtojli77pgInmnYbloZQKICAgIGNhbmNlbEZ1bigpIHsKICAgICAgaWYgKHRoaXMuc2VsZWN0Um93czIubGVuZ3RoID09PSAwKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6K+36YCJ5oup6KaB5Y+W5raI55qE5p2G5aGU5pWw5o2uIik7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgbGV0IHJpZ2h0SWRzID0gW107CiAgICAgICAgLy/ph43mlrDotYvlgLzlj7PkvqfliJfooagKICAgICAgICB0aGlzLnNlbGVjdFJvd3MyLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICBpdGVtLnJvd0NscyA9ICJzdWNjZXNzLXJvdyI7CiAgICAgICAgICByaWdodElkcy5wdXNoKGl0ZW0uZ3RpZCk7CiAgICAgICAgICB0aGlzLmd0RGF0YXMucHVzaChpdGVtKTsKICAgICAgICB9KTsKICAgICAgICBsZXQgZ3REYXRhcyA9IFtdOwogICAgICAgIC8v6YeN5paw6LWL5YC85bem5L6n5YiX6KGoCiAgICAgICAgdGhpcy5jdXJyR3REYXRhcy5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgaWYgKCFyaWdodElkcy5pbmNsdWRlcyhpdGVtLmd0aWQpKSB7CiAgICAgICAgICAgIGl0ZW0ucm93Q2xzID0gIiI7CiAgICAgICAgICAgIGd0RGF0YXMucHVzaChpdGVtKTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgICB0aGlzLmN1cnJHdERhdGFzID0gZ3REYXRhczsKICAgICAgfQogICAgfSwKICAgIC8v5Yid5aeL5YyW5Y+z5L6n5p2G5aGUCiAgICBpbml0UmlnaHREYXRhKCkgewogICAgICB0aGlzLmN1cnJHdERhdGFzID0gdGhpcy5mb3JtLnRvd2VycyA/IHRoaXMuZm9ybS50b3dlcnMgOiBbXTsKICAgIH0sCiAgICAvL+iOt+WPluWIl+ihqAogICAgYXN5bmMgZ2V0R3REYXRhKCkgewogICAgICBhd2FpdCBnZXRHdHNCeVhsKHRoaXMuZm9ybSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuZ3REYXRhcyA9IHJlcy5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICAvL+WFs+mXreW8ueahhgogICAgY2xvc2VGdW4oKSB7CiAgICAgIHRoaXMuJGVtaXQoImNsb3NlRWRpdCIpOwogICAgfSwKICAgIC8v5L+d5a2Y5pa55rOVCiAgICBzYXZlRnVuKCkgewogICAgICAvL+WFiOWIpOaWreaYr+WQpumAmui/h+agoemqjAogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgICAvLyDku6XmnI3liqHnmoTmlrnlvI/osIPnlKjnmoQgTG9hZGluZyDpnIDopoHlvILmraXlhbPpl60KICAgICAgICAgICAgdGhpcy5sb2FkaW5nID0gTG9hZGluZy5zZXJ2aWNlKHsKICAgICAgICAgICAgICBsb2NrOiB0cnVlLCAvL2xvY2vnmoTkv67mlLnnrKYtLem7mOiupOaYr2ZhbHNlCiAgICAgICAgICAgICAgdGV4dDogIuS/neWtmOS4re+8jOivt+eojeWQjsK3wrfCtyIsIC8v5pi+56S65Zyo5Yqg6L295Zu+5qCH5LiL5pa555qE5Yqg6L295paH5qGICiAgICAgICAgICAgICAgc3Bpbm5lcjogImVsLWljb24tbG9hZGluZyIsIC8v6Ieq5a6a5LmJ5Yqg6L295Zu+5qCH57G75ZCNCiAgICAgICAgICAgICAgYmFja2dyb3VuZDogInJnYmEoMCwgMCwgMCwgMC43KSIsIC8v6YGu572p5bGC6aKc6ImyCiAgICAgICAgICAgICAgdGFyZ2V0OiBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCIjenh3aERpdiIpCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSk7CiAgICAgICAgICB0aGlzLmZvcm0udG93ZXJzID0gdGhpcy5jdXJyR3REYXRhczsKICAgICAgICAgIHNhdmVPclVwZGF0ZSh0aGlzLmZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLmk43kvZzmiJDlip8hIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIHRoaXMubG9hZGluZy5jbG9zZSgpOwogICAgICAgICAgICAgIHRoaXMuY2xvc2VGdW4oKTsKICAgICAgICAgICAgICB0aGlzLiRlbWl0KCJyZWZyZXNoRnVuIik7IC8v5Yi35paw5YiX6KGo5pa55rOVCiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLmoKHpqozmnKrpgJrov4fvvIEiKTsKICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZTEoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuc2VsZWN0Um93czEgPSBzZWxlY3Rpb247CiAgICB9LAogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlMihzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5zZWxlY3RSb3dzMiA9IHNlbGVjdGlvbjsKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["zxwh_edit.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "zxwh_edit.vue", "sourceRoot": "src/views/dagangOilfield/dwzygl/sdsbgl", "sourcesContent": ["<template>\n  <div class=\"app-container\" id=\"zxwhDiv\">\n    <el-row :gutter=\"16\">\n      <el-col :span=\"12\">\n        <el-form label-width=\"120px\" ref=\"form\" :model=\"form\">\n          <!--主表信息-->\n          <div>\n            <el-row>\n              <el-col :span=\"24\">\n                <el-form-item\n                  label=\"线段名称：\"\n                  prop=\"xdmc\"\n                  style=\"width: 80%\"\n                  :rules=\"[\n                    { required: true, message: '名称不能为空', trigger: 'blur' }\n                  ]\"\n                >\n                  <el-input v-model=\"form.xdmc\" />\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </div>\n        </el-form>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"16\">\n      <!--   未勾选的杆塔信息   -->\n      <el-col :span=\"12\">\n        <el-white class=\"button-group1\">\n          <el-row class=\"button_btn pull-right \" :gutter=\"4\">\n            <el-col :span=\"1.5\">\n              <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"chooseFun\"\n                >关联</el-button\n              >\n            </el-col>\n          </el-row>\n          <el-table\n            :data=\"gtDatas\"\n            style=\"width: 100%\"\n            size=\"small\"\n            :fit=\"true\"\n            highlight-current-row\n            @selection-change=\"handleSelectionChange1\"\n            height=\"520\"\n            border\n            :row-class-name=\"tableRowClassName\"\n          >\n            <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n            <el-table-column\n              type=\"index\"\n              label=\"序号\"\n              width=\"50\"\n              align=\"center\"\n            />\n            <el-table-column\n              prop=\"gtmc\"\n              label=\"杆塔名称\"\n              min-width=\"100\"\n              align=\"center\"\n            />\n            <el-table-column\n              prop=\"gtbh\"\n              label=\"杆塔编号\"\n              min-width=\"80\"\n              align=\"center\"\n            />\n            <el-table-column\n              prop=\"jd\"\n              label=\"经度\"\n              min-width=\"100\"\n              align=\"center\"\n            />\n            <el-table-column\n              prop=\"wd\"\n              label=\"纬度\"\n              min-width=\"100\"\n              align=\"center\"\n            />\n          </el-table>\n        </el-white>\n      </el-col>\n      <!--   现有杆塔信息   -->\n      <el-col :span=\"12\">\n        <el-white class=\"button-group1\">\n          <el-row class=\"button_btn pull-right \" :gutter=\"4\">\n            <el-col :span=\"1.5\">\n              <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"cancelFun\"\n                >取消关联</el-button\n              >\n            </el-col>\n          </el-row>\n          <el-table\n            :data=\"currGtDatas\"\n            class=\"draggable-table\"\n            row-key=\"gtid\"\n            style=\"width: 100%\"\n            size=\"small\"\n            :fit=\"true\"\n            highlight-current-row\n            @selection-change=\"handleSelectionChange2\"\n            height=\"520\"\n            border\n            :row-class-name=\"tableRowClassName\"\n            @row-click=\"handleCurrentChange\"\n          >\n            <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n            <el-table-column\n              type=\"index\"\n              label=\"序号\"\n              width=\"50\"\n              align=\"center\"\n            />\n            <el-table-column\n              prop=\"gtmc\"\n              label=\"杆塔名称\"\n              min-width=\"100\"\n              align=\"center\"\n            />\n            <el-table-column\n              prop=\"gtbh\"\n              label=\"杆塔编号\"\n              min-width=\"80\"\n              align=\"center\"\n            />\n            <el-table-column\n              prop=\"jd\"\n              label=\"经度\"\n              min-width=\"100\"\n              align=\"center\"\n            />\n            <el-table-column\n              prop=\"wd\"\n              label=\"纬度\"\n              min-width=\"100\"\n              align=\"center\"\n            />\n          </el-table>\n        </el-white>\n      </el-col>\n    </el-row>\n    <div align=\"right\" slot=\"footer\">\n      <el-button @click=\"closeFun\">取 消</el-button>\n      <el-button @click=\"saveFun\" type=\"primary\">确 定</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getGtsByXl, saveOrUpdate } from \"@/api/dagangOilfield/asset/zxwh\";\nimport { Loading } from \"element-ui\";\nexport default {\n  name: \"zxwh_edit\",\n  props: {\n    xdData: {\n      type: Object\n    }\n  },\n  data() {\n    return {\n      form: {},\n      gtDatas: [], //左侧杆塔数据\n      currGtDatas: [], //右侧杆塔数据\n      selectRows1: [], //左侧勾选数据\n      selectRows2: [], //右侧勾选数据\n      loading: null, //遮罩层\n      oldIndex: undefined,\n      newIndex: undefined,\n      flag: \"start\"\n    };\n  },\n  mounted() {\n    this.form = JSON.parse(JSON.stringify(this.xdData)); //深度拷贝数据\n    this.getGtData();\n    this.initRightData();\n  },\n  methods: {\n    //表格行样式\n    tableRowClassName({ row, rowIndex }) {\n      row.index = rowIndex;\n      return row.rowCls ? row.rowCls : \"\";\n    },\n    handleCurrentChange(row, column, event) {\n      if (this.flag === \"start\") {\n        this.oldIndex = row.index;\n        this.flag = \"end\";\n        this.$message.info(\"请点击需要更换到的行\");\n      } else if (this.flag == \"end\") {\n        this.newIndex = row.index;\n        this.flag = \"start\";\n        const oldCurrentRow = this.currGtDatas.splice(this.oldIndex, 1)[0];\n        this.currGtDatas.splice(this.newIndex, 0, oldCurrentRow);\n        this.$message.success(\"更换顺序成功\");\n      }\n    },\n    //批量关联杆塔\n    chooseFun() {\n      if (this.selectRows1.length === 0) {\n        this.$message.error(\"请选择要关联的杆塔数据\");\n      } else {\n        let leftIds = [];\n        //重新赋值右侧列表\n        this.selectRows1.forEach(item => {\n          item.rowCls = \"success-row\";\n          leftIds.push(item.gtid);\n          this.currGtDatas.push(item);\n        });\n        let gtDatas = [];\n        //重新赋值左侧列表\n        this.gtDatas.forEach(item => {\n          if (!leftIds.includes(item.gtid)) {\n            item.rowCls = \"\";\n            gtDatas.push(item);\n          }\n        });\n        this.gtDatas = gtDatas;\n      }\n    },\n    //取消勾选杆塔\n    cancelFun() {\n      if (this.selectRows2.length === 0) {\n        this.$message.error(\"请选择要取消的杆塔数据\");\n      } else {\n        let rightIds = [];\n        //重新赋值右侧列表\n        this.selectRows2.forEach(item => {\n          item.rowCls = \"success-row\";\n          rightIds.push(item.gtid);\n          this.gtDatas.push(item);\n        });\n        let gtDatas = [];\n        //重新赋值左侧列表\n        this.currGtDatas.forEach(item => {\n          if (!rightIds.includes(item.gtid)) {\n            item.rowCls = \"\";\n            gtDatas.push(item);\n          }\n        });\n        this.currGtDatas = gtDatas;\n      }\n    },\n    //初始化右侧杆塔\n    initRightData() {\n      this.currGtDatas = this.form.towers ? this.form.towers : [];\n    },\n    //获取列表\n    async getGtData() {\n      await getGtsByXl(this.form).then(res => {\n        this.gtDatas = res.data;\n      });\n    },\n    //关闭弹框\n    closeFun() {\n      this.$emit(\"closeEdit\");\n    },\n    //保存方法\n    saveFun() {\n      //先判断是否通过校验\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          this.$nextTick(() => {\n            // 以服务的方式调用的 Loading 需要异步关闭\n            this.loading = Loading.service({\n              lock: true, //lock的修改符--默认是false\n              text: \"保存中，请稍后···\", //显示在加载图标下方的加载文案\n              spinner: \"el-icon-loading\", //自定义加载图标类名\n              background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n              target: document.querySelector(\"#zxwhDiv\")\n            });\n          });\n          this.form.towers = this.currGtDatas;\n          saveOrUpdate(this.form).then(res => {\n            if (res.code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"操作成功!\"\n              });\n              this.loading.close();\n              this.closeFun();\n              this.$emit(\"refreshFun\"); //刷新列表方法\n            }\n          });\n        } else {\n          this.$message.error(\"校验未通过！\");\n          return false;\n        }\n      });\n    },\n    handleSelectionChange1(selection) {\n      this.selectRows1 = selection;\n    },\n    handleSelectionChange2(selection) {\n      this.selectRows2 = selection;\n    }\n  }\n};\n</script>\n\n<style scoped>\n/deep/.el-table .success-row {\n  background: #f0f9eb;\n}\n/* 设置滚动条的样式 */\n::-webkit-scrollbar {\n  width: 8px;\n}\n\n/* 滚动槽 */\n::-webkit-scrollbar-track {\n  //-webkit-box-shadow:inset006pxrgba(0,0,0,0.3);\n  border-radius: 10px;\n}\n\n/* 滚动条滑块 */\n::-webkit-scrollbar-thumb {\n  border-radius: 10px;\n  background: rgba(0, 0, 0, 0.1);\n  //-webkit-box-shadow:gba(0,0,0,0.5);\n}\n\n::-webkit-scrollbar-thumb:window-inactive {\n  background: rgba(0, 0, 0, 0.1);\n}\n</style>\n"]}]}