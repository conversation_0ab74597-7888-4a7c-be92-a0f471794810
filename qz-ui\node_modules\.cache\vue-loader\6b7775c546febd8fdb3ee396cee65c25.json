{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\gfqxwh.vue?vue&type=style&index=0&id=3c07bd68&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\gfqxwh.vue", "mtime": 1726318090035}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQovKiDorr7nva7mu5rliqjmnaHnmoTmoLflvI8gKi8NCjo6LXdlYmtpdC1zY3JvbGxiYXIgew0KICB3aWR0aDogMTJweDsNCn0NCg0KLyog5rua5Yqo5qe9ICovDQo6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHsNCiAgLy8td2Via2l0LWJveC1zaGFkb3c6aW5zZXQwMDZweHJnYmEoMCwwLDAsMC4zKTsNCiAgYm9yZGVyLXJhZGl1czogMTBweDsNCn0NCg0KLyog5rua5Yqo5p2h5ruR5Z2XICovDQo6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHsNCiAgYm9yZGVyLXJhZGl1czogMTBweDsNCiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjEpOw0KICAvLy13ZWJraXQtYm94LXNoYWRvdzpnYmEoMCwwLDAsMC41KTsNCn0NCg0KOjotd2Via2l0LXNjcm9sbGJhci10aHVtYjp3aW5kb3ctaW5hY3RpdmUgew0KICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuMSk7DQp9DQouaGVhZC1jb250YWluZXIgew0KICBtYXJnaW46IDAgYXV0bzsNCiAgd2lkdGg6IDk4JTsNCiAgaGVpZ2h0OiA4Mi42dmg7DQogIG1heC1oZWlnaHQ6IDgyLjZ2aDsNCiAgb3ZlcmZsb3c6IGF1dG87DQp9DQovKue7meW3puS+p+aVsOe7k+aehGhlYWRlcuWKoOminOiJsiovDQouYm94LWNhcmQgLmVsLWNhcmRfX2hlYWRlciB7DQogIGJhY2tncm91bmQ6ICMxMWJhNmQgIWltcG9ydGFudDsNCn0NCi5ib3gtY2FyZCB7DQogIG1hcmdpbjogMDsNCn0NCg0KLml0ZW0gew0KICB3aWR0aDogMjAwcHg7DQogIGhlaWdodDogMTQ4cHg7DQogIGZsb2F0OiBsZWZ0Ow0KfQ0KDQoudHJlZSB7DQogIG92ZXJmbG93LXk6IGhpZGRlbjsNCiAgb3ZlcmZsb3cteDogc2Nyb2xsOw0KICB3aWR0aDogODBweDsNCiAgaGVpZ2h0OiA1MDBweDsNCn0NCg0KLmVsLXRyZWUgew0KICBtaW4td2lkdGg6IDEwMCU7DQogIGRpc3BsYXk6IGlubGluZS1ibG9jayAhaW1wb3J0YW50Ow0KfQ0KL2RlZXAvIC5lbC1kaWFsb2c6bm90KC5pcy1mdWxsc2NyZWVuKSB7DQogIG1hcmdpbi10b3A6IDh2aCAhaW1wb3J0YW50Ow0KfQ0K"}, {"version": 3, "sources": ["gfqxwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAy4CA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "gfqxwh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl", "sourcesContent": ["<template>\r\n  <div class=\"app-container\" id=\"sbqxDiv\">\r\n    <!--左侧树组件-->\r\n    <el-row :gutter=\"1\">\r\n      <!--   左侧树   -->\r\n      <el-col :span=\"4\">\r\n        <el-card style=\"background:#e0f8ed;padding-top:10px;\" shadow=\"never\">\r\n          <div>\r\n            <el-col>\r\n              <el-form label-width=\"62px\">\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"快速查询:\" label-width=\"80px\">\r\n                    <el-input\r\n                      placeholder=\"输入关键字过滤\"\r\n                      v-model=\"filterText\"\r\n                      clearable\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-form>\r\n            </el-col>\r\n          </div>\r\n          <div class=\"text head-container\">\r\n            <el-col style=\"padding:0;\">\r\n              <el-tree\r\n                id=\"tree\"\r\n                :data=\"treeOptions\"\r\n                @node-click=\"handleNodeClick\"\r\n                :highlight-current=\"true\"\r\n                ref=\"tree\"\r\n                :filter-node-method=\"filterNode\"\r\n                node-key=\"id\"\r\n                :default-checked-keys=\"['0']\"\r\n                :default-expanded-keys=\"['0']\"\r\n                accordion\r\n              />\r\n            </el-col>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n      <!--右侧列表-->\r\n      <el-col :span=\"20\">\r\n        <el-filter\r\n          ref=\"filter1\"\r\n          :data=\"filterInfo.data\"\r\n          :field-list=\"filterInfo.fieldList\"\r\n          :btnHidden=\"false\"\r\n          :width=\"{ labelWidth: 80, itemWidth: 165 }\"\r\n          @handleReset=\"filterReset\"\r\n        />\r\n        <el-white class=\"button-group1\">\r\n          <div class=\"button_btn\">\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-plus\"\r\n              v-hasPermi=\"['gfqxwh:button:add']\"\r\n              @click=\"addForm('sbbj')\"\r\n              >新增部件</el-button\r\n            >\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-plus\"\r\n              v-hasPermi=\"['gfqxwh:button:add']\"\r\n              @click=\"addForm('sbbw')\"\r\n              >新增部位</el-button\r\n            >\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-plus\"\r\n              v-hasPermi=\"['gfqxwh:button:add']\"\r\n              @click=\"addForm('qxms')\"\r\n              >新增隐患描述</el-button\r\n            >\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-plus\"\r\n              v-hasPermi=\"['gfqxwh:button:add']\"\r\n              @click=\"addForm('flyj')\"\r\n              >新增分类依据</el-button\r\n            >\r\n            <el-button\r\n              type=\"success\"\r\n              icon=\"el-icon-download\"\r\n              @click=\"exportExcel\"\r\n              >导出</el-button\r\n            >\r\n          </div>\r\n          <comp-table\r\n            :table-and-page-info=\"tableAndPageInfo\"\r\n            height=\"62.2vh\"\r\n            v-loading=\"load\"\r\n          >\r\n            <el-table-column\r\n              slot=\"table_eight\"\r\n              align=\"center\"\r\n              fixed=\"right\"\r\n              style=\"display: block\"\r\n              label=\"操作\"\r\n              width=\"200\"\r\n              :resizable=\"false\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  @click=\"updateRow(scope.row)\"\r\n                  v-hasPermi=\"['gfqxwh:button:update']\"\r\n                  title=\"修改\"\r\n                  class=\"el-icon-edit\"\r\n                >\r\n                </el-button>\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  @click=\"deleteRow(scope.row)\"\r\n                  v-hasPermi=\"['gfqxwh:button:delete']\"\r\n                  title=\"删除\"\r\n                  class=\"el-icon-delete\"\r\n                >\r\n                </el-button>\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  @click=\"viewFun(scope.row)\"\r\n                  title=\"查看\"\r\n                  class=\"el-icon-view\"\r\n                >\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </comp-table>\r\n        </el-white>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!--  新增设备部件  -->\r\n    <el-dialog\r\n      title=\"新增设备部件\"\r\n      :visible.sync=\"isShowSbbj\"\r\n      width=\"58%\"\r\n      @close=\"closeFun('sbbj')\"\r\n      v-dialogDrag\r\n    >\r\n      <el-form\r\n        label-width=\"140px\"\r\n        :rules=\"sbbjRules\"\r\n        :model=\"sbbjForm\"\r\n        ref=\"sbbjForm\"\r\n      >\r\n        <el-card shadow=\"never\" class=\"box-cont\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>设备隐患</span>\r\n          </div>\r\n          <el-row :gutter=\"15\" class=\"cont_top\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"sblxbm\" label=\"设备类型\">\r\n                <el-select\r\n                  placeholder=\"设备类型\"\r\n                  v-model=\"sbbjForm.sblxbm\"\r\n                  style=\"width:80%\"\r\n                  @change=\"sblxChangeFun\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in sblxList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"sbbj\" label=\"设备部件\">\r\n                <el-input\r\n                  v-model=\"sbbjForm.sbbj\"\r\n                  placeholder=\"请输入设备部件\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"sbbw\" label=\"设备部位\">\r\n                <el-input\r\n                  v-model=\"sbbjForm.sbbw\"\r\n                  placeholder=\"请输入设备部位\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\r\n                <el-select\r\n                  placeholder=\"隐患等级\"\r\n                  v-model=\"sbbjForm.qxdj\"\r\n                  style=\"width:80%\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in qxdjList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"隐患描述:\" prop=\"qxms\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"sbbjForm.qxms\"\r\n                  placeholder=\"请输入隐患描述\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"sbbjForm.flyj\"\r\n                  placeholder=\"请输入分类依据\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"sbbjForm.jsyy\"\r\n                  placeholder=\"请输入技术原因\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n      </el-form>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"closeFun('sbbj')\" size=\"small\">取 消</el-button>\r\n        <el-button type=\"primary\" size=\"small\" @click=\"saveForm('sbbjForm')\"\r\n          >保存</el-button\r\n        >\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!--  新增设备部位  -->\r\n    <el-dialog\r\n      title=\"新增设备部位\"\r\n      :visible.sync=\"isShowSbbw\"\r\n      width=\"58%\"\r\n      @close=\"closeFun('sbbw')\"\r\n      v-dialogDrag\r\n    >\r\n      <el-form\r\n        label-width=\"140px\"\r\n        :rules=\"sbbwRules\"\r\n        :model=\"sbbwForm\"\r\n        ref=\"sbbwForm\"\r\n      >\r\n        <el-card shadow=\"never\" class=\"box-cont\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>设备隐患</span>\r\n          </div>\r\n          <el-row :gutter=\"15\" class=\"cont_top\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"sblxbm\" label=\"设备类型\">\r\n                <el-select\r\n                  placeholder=\"设备类型\"\r\n                  v-model=\"sbbwForm.sblxbm\"\r\n                  style=\"width:80%\"\r\n                  @change=\"sblxChangeFun\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in sblxList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"parentSbbj\" label=\"设备部件\">\r\n                <el-select\r\n                  placeholder=\"设备部件\"\r\n                  v-model=\"sbbwForm.parentSbbj\"\r\n                  style=\"width:80%\"\r\n                  @change=\"sbbjChangeFun\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in sbbjList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"sbbw\" label=\"设备部位\">\r\n                <el-input\r\n                  v-model=\"sbbwForm.sbbw\"\r\n                  placeholder=\"请输入设备部位\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\r\n                <el-select\r\n                  placeholder=\"隐患等级\"\r\n                  v-model=\"sbbwForm.qxdj\"\r\n                  style=\"width:80%\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in qxdjList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"隐患描述:\" prop=\"qxms\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"sbbwForm.qxms\"\r\n                  placeholder=\"请输入隐患描述\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"sbbwForm.flyj\"\r\n                  placeholder=\"请输入分类依据\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"sbbwForm.jsyy\"\r\n                  placeholder=\"请输入技术原因\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n      </el-form>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"closeFun('sbbw')\" size=\"small\">取 消</el-button>\r\n        <el-button type=\"primary\" size=\"small\" @click=\"saveForm('sbbwForm')\"\r\n          >保存</el-button\r\n        >\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!--  新增隐患描述  -->\r\n    <el-dialog\r\n      title=\"新增隐患描述\"\r\n      :visible.sync=\"isShowQxms\"\r\n      width=\"58%\"\r\n      @close=\"closeFun('qxms')\"\r\n      v-dialogDrag\r\n    >\r\n      <el-form\r\n        label-width=\"140px\"\r\n        :rules=\"qxmsRules\"\r\n        :model=\"qxmsForm\"\r\n        ref=\"qxmsForm\"\r\n      >\r\n        <el-card shadow=\"never\" class=\"box-cont\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>设备隐患</span>\r\n          </div>\r\n          <el-row :gutter=\"15\" class=\"cont_top\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"sblxbm\" label=\"设备类型\">\r\n                <el-select\r\n                  placeholder=\"设备类型\"\r\n                  v-model=\"qxmsForm.sblxbm\"\r\n                  style=\"width:80%\"\r\n                  @change=\"sblxChangeFun\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in sblxList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"parentSbbj\" label=\"设备部件\">\r\n                <el-select\r\n                  placeholder=\"设备部件\"\r\n                  v-model=\"qxmsForm.parentSbbj\"\r\n                  style=\"width:80%\"\r\n                  @change=\"sbbjChangeFun\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in sbbjList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"parentSbbw\" label=\"设备部位\">\r\n                <el-select\r\n                  placeholder=\"设备部位\"\r\n                  v-model=\"qxmsForm.parentSbbw\"\r\n                  style=\"width:80%\"\r\n                  @change=\"sbbwChangeFun\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in sbbwList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\r\n                <el-select\r\n                  placeholder=\"隐患等级\"\r\n                  v-model=\"qxmsForm.qxdj\"\r\n                  style=\"width:80%\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in qxdjList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"隐患描述:\" prop=\"qxms\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"qxmsForm.qxms\"\r\n                  placeholder=\"请输入隐患描述\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"qxmsForm.flyj\"\r\n                  placeholder=\"请输入分类依据\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"qxmsForm.jsyy\"\r\n                  placeholder=\"请输入技术原因\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n      </el-form>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"closeFun('qxms')\" size=\"small\">取 消</el-button>\r\n        <el-button type=\"primary\" size=\"small\" @click=\"saveForm('qxmsForm')\"\r\n          >保存</el-button\r\n        >\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!--  新增分类依据  -->\r\n    <el-dialog\r\n      title=\"新增分类依据\"\r\n      :visible.sync=\"isShowFlyj\"\r\n      width=\"58%\"\r\n      @close=\"closeFun('flyj')\"\r\n      v-dialogDrag\r\n    >\r\n      <el-form\r\n        label-width=\"140px\"\r\n        :rules=\"flyjRules\"\r\n        :model=\"flyjForm\"\r\n        ref=\"flyjForm\"\r\n      >\r\n        <el-card shadow=\"never\" class=\"box-cont\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>设备隐患</span>\r\n          </div>\r\n          <el-row :gutter=\"15\" class=\"cont_top\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"sblxbm\" label=\"设备类型\">\r\n                <el-select\r\n                  placeholder=\"设备类型\"\r\n                  v-model=\"flyjForm.sblxbm\"\r\n                  style=\"width:80%\"\r\n                  @change=\"sblxChangeFun\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in sblxList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"parentSbbj\" label=\"设备部件\">\r\n                <el-select\r\n                  placeholder=\"设备部件\"\r\n                  v-model=\"flyjForm.parentSbbj\"\r\n                  style=\"width:80%\"\r\n                  @change=\"sbbjChangeFun\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in sbbjList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"parentSbbw\" label=\"设备部位\">\r\n                <el-select\r\n                  placeholder=\"设备部位\"\r\n                  v-model=\"flyjForm.parentSbbw\"\r\n                  style=\"width:80%\"\r\n                  @change=\"sbbwChangeFun\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in sbbwList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\r\n                <el-select\r\n                  placeholder=\"隐患等级\"\r\n                  v-model=\"flyjForm.qxdj\"\r\n                  style=\"width:80%\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in qxdjList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"隐患描述:\" prop=\"parentQxms\">\r\n                <el-select\r\n                  placeholder=\"隐患描述\"\r\n                  v-model=\"flyjForm.parentQxms\"\r\n                  style=\"width:80%\"\r\n                  @change=\"qxmsChangeFun\"\r\n                  filterable\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"item in qxmsList\"\r\n                    :key=\"item.value\"\r\n                    :label=\"item.label\"\r\n                    :value=\"String(item.value)\"\r\n                  >\r\n                  </el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"flyjForm.flyj\"\r\n                  placeholder=\"请输入分类依据\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"flyjForm.jsyy\"\r\n                  placeholder=\"请输入技术原因\"\r\n                  style=\"width: 92%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n      </el-form>\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"closeFun('flyj')\" size=\"small\">取 消</el-button>\r\n        <el-button\r\n          v-if=\"addFlyj\"\r\n          type=\"primary\"\r\n          size=\"small\"\r\n          @click=\"saveForm('flyjForm')\"\r\n          >保存</el-button\r\n        >\r\n        <el-button\r\n          v-if=\"!addFlyj\"\r\n          type=\"primary\"\r\n          size=\"small\"\r\n          @click=\"saveForm('flyjForm')\"\r\n          >保存</el-button\r\n        >\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!--  设备隐患查看  -->\r\n    <el-dialog\r\n      title=\"设备隐患查看\"\r\n      :visible.sync=\"isShowDetail\"\r\n      v-if=\"isShowDetail\"\r\n      width=\"58%\"\r\n      @close=\"closeFun('view')\"\r\n      v-dialogDrag\r\n    >\r\n      <el-form label-width=\"140px\" :model=\"viewForm\" ref=\"viewForm\">\r\n        <el-card shadow=\"never\" class=\"box-cont\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span>设备隐患</span>\r\n          </div>\r\n          <el-row :gutter=\"15\" class=\"cont_top\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"sblx\" label=\"设备类型\">\r\n                <el-input\r\n                  v-model=\"viewForm.sblx\"\r\n                  placeholder=\"请输入设备类型\"\r\n                  style=\"width: 92%\"\r\n                  :disabled=\"true\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"sbbj\" label=\"设备部件\">\r\n                <el-input\r\n                  v-model=\"viewForm.sbbj\"\r\n                  placeholder=\"请输入设备部件\"\r\n                  style=\"width: 92%\"\r\n                  :disabled=\"true\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"sbbw\" label=\"设备部位\">\r\n                <el-input\r\n                  v-model=\"viewForm.sbbw\"\r\n                  placeholder=\"请输入设备部位\"\r\n                  style=\"width: 92%\"\r\n                  :disabled=\"true\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\r\n                <el-input\r\n                  v-model=\"viewForm.qxdj\"\r\n                  placeholder=\"请输入隐患等级\"\r\n                  style=\"width: 92%\"\r\n                  :disabled=\"true\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"隐患描述:\" prop=\"qxms\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"viewForm.qxms\"\r\n                  placeholder=\"请输入隐患描述\"\r\n                  style=\"width: 92%\"\r\n                  :disabled=\"true\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"viewForm.flyj\"\r\n                  placeholder=\"请输入分类依据\"\r\n                  style=\"width: 92%\"\r\n                  :disabled=\"true\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"15\" class=\"pull-left\">\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\r\n                <el-input\r\n                  type=\"textarea\"\r\n                  :rows=\"5\"\r\n                  v-model=\"viewForm.jsyy\"\r\n                  placeholder=\"请输入技术原因\"\r\n                  style=\"width: 92%\"\r\n                  :disabled=\"true\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n      </el-form>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  getQxList,\r\n  getQxsbTree,\r\n  getSblxList,\r\n  getSbbjList,\r\n  getSbbwList,\r\n  getQxmsList,\r\n  getFlyjList,\r\n  addFlyj,\r\n  updateFlyj,\r\n  deleteFlyjById,\r\n  addQxms,\r\n  addSbbw,\r\n  addSbbj\r\n} from \"@/api/dagangOilfield/bzgl/sbqxwh/sbqxwh\";\r\nimport { getDictTypeData } from \"@/api/system/dict/data\";\r\nimport { Loading } from \"element-ui\";\r\nimport { exportExcel } from \"@/api/bzgl/ysbzk/ysbzk\";\r\n\r\nexport default {\r\n  name: \"sblxwh\",\r\n  data() {\r\n    return {\r\n      load: false,\r\n      addFlyj: false, //是否新增分类依据\r\n      filterInfo: {\r\n        data: {\r\n          sbbj: \"\",\r\n          sbbw: \"\",\r\n          qxms: \"\",\r\n          flyj: \"\",\r\n          qxdj: \"\",\r\n          jsyy: \"\"\r\n        },\r\n        fieldList: [\r\n          { label: \"设备部件\", type: \"input\", value: \"sbbj\" },\r\n          { label: \"设备部位\", type: \"input\", value: \"sbbw\" },\r\n          { label: \"隐患描述\", type: \"input\", value: \"qxms\" },\r\n          { label: \"隐患等级\", type: \"select\", value: \"qxdj\", options: [] },\r\n          { label: \"分类依据\", type: \"input\", value: \"flyj\" },\r\n          { label: \"技术原因\", type: \"input\", value: \"jsyy\" }\r\n        ]\r\n      },\r\n      tableAndPageInfo: {\r\n        pager: {\r\n          pageSize: 10,\r\n          pageNum: 1,\r\n          total: 0,\r\n          sizes: [10, 20, 50, 100]\r\n        },\r\n        option: {\r\n          checkBox: true,\r\n          serialNumber: true\r\n        },\r\n        tableData: [],\r\n        tableHeader: [\r\n          { prop: \"sblx\", label: \"设备类型\", minWidth: \"140\" },\r\n          { prop: \"sbbj\", label: \"设备部件\", minWidth: \"180\" },\r\n          { prop: \"sbbw\", label: \"设备部位\", minWidth: \"130\" },\r\n          { prop: \"qxms\", label: \"隐患描述\", minWidth: \"200\" },\r\n          { prop: \"flyj\", label: \"分类依据\", minWidth: \"220\", showPop: true },\r\n          { prop: \"qxdj\", label: \"隐患等级\", minWidth: \"80\" },\r\n          { prop: \"jsyy\", label: \"技术原因\", minWidth: \"120\" }\r\n        ]\r\n      },\r\n      queryParams: {},\r\n      treeOptions: [], //组织树\r\n      treeNodeData: {}, //点击后的树节点数据\r\n      isShowDetail: false,\r\n      isShowSbbj: false, //新增弹框\r\n      isShowSbbw: false,\r\n      isShowQxms: false,\r\n      isShowFlyj: false,\r\n      flyjForm: {}, //表单\r\n      flyjRules: {\r\n        sblxbm: [\r\n          { required: true, message: \"设备类型不能为空\", trigger: \"select\" }\r\n        ],\r\n        parentSbbj: [\r\n          { required: true, message: \"设备部件不能为空\", trigger: \"select\" }\r\n        ],\r\n        parentSbbw: [\r\n          { required: true, message: \"设备部位不能为空\", trigger: \"select\" }\r\n        ],\r\n        qxdj: [\r\n          { required: true, message: \"隐患等级不能为空\", trigger: \"select\" }\r\n        ],\r\n        parentQxms: [\r\n          { required: true, message: \"隐患描述不能为空\", trigger: \"select\" }\r\n        ],\r\n        flyj: [\r\n          { required: true, message: \"分类依据不能为空\", trigger: \"blur\" }\r\n        ],\r\n        jsyy: [{ required: true, message: \"技术原因不能为空\", trigger: \"blur\" }]\r\n      }, //校验规则\r\n      qxmsForm: {}, //表单\r\n      qxmsRules: {\r\n        sblxbm: [\r\n          { required: true, message: \"设备类型不能为空\", trigger: \"select\" }\r\n        ],\r\n        parentSbbj: [\r\n          { required: true, message: \"设备部件不能为空\", trigger: \"select\" }\r\n        ],\r\n        parentSbbw: [\r\n          { required: true, message: \"设备部位不能为空\", trigger: \"select\" }\r\n        ],\r\n        qxdj: [\r\n          { required: true, message: \"隐患等级不能为空\", trigger: \"select\" }\r\n        ],\r\n        qxms: [\r\n          { required: true, message: \"隐患描述不能为空\", trigger: \"blur\" }\r\n        ],\r\n        flyj: [\r\n          { required: true, message: \"分类依据不能为空\", trigger: \"blur\" }\r\n        ],\r\n        jsyy: [{ required: true, message: \"技术原因不能为空\", trigger: \"blur\" }]\r\n      }, //校验规则\r\n      sbbwForm: {}, //表单\r\n      sbbwRules: {\r\n        sblxbm: [\r\n          { required: true, message: \"设备类型不能为空\", trigger: \"select\" }\r\n        ],\r\n        parentSbbj: [\r\n          { required: true, message: \"设备部件不能为空\", trigger: \"select\" }\r\n        ],\r\n        sbbw: [\r\n          { required: true, message: \"设备部位不能为空\", trigger: \"blur\" }\r\n        ],\r\n        qxdj: [\r\n          { required: true, message: \"隐患等级不能为空\", trigger: \"select\" }\r\n        ],\r\n        qxms: [\r\n          { required: true, message: \"隐患描述不能为空\", trigger: \"blur\" }\r\n        ],\r\n        flyj: [\r\n          { required: true, message: \"分类依据不能为空\", trigger: \"blur\" }\r\n        ],\r\n        jsyy: [{ required: true, message: \"技术原因不能为空\", trigger: \"blur\" }]\r\n      }, //校验规则\r\n      sbbjForm: {}, //表单\r\n      sbbjRules: {\r\n        sblxbm: [\r\n          { required: true, message: \"设备类型不能为空\", trigger: \"select\" }\r\n        ],\r\n        sbbj: [\r\n          { required: true, message: \"设备部件不能为空\", trigger: \"blur\" }\r\n        ],\r\n        sbbw: [\r\n          { required: true, message: \"设备部位不能为空\", trigger: \"blur\" }\r\n        ],\r\n        qxdj: [\r\n          { required: true, message: \"隐患等级不能为空\", trigger: \"select\" }\r\n        ],\r\n        qxms: [\r\n          { required: true, message: \"隐患描述不能为空\", trigger: \"blur\" }\r\n        ],\r\n        flyj: [\r\n          { required: true, message: \"分类依据不能为空\", trigger: \"blur\" }\r\n        ],\r\n        jsyy: [{ required: true, message: \"技术原因不能为空\", trigger: \"blur\" }]\r\n      }, //校验规则\r\n      sblxList: [], //设备类型下拉框选项\r\n      sbbjList: [], //设备部件下拉框选项\r\n      sbbwList: [], //设备部位下拉框选项\r\n      qxmsList: [], //隐患描述下拉框选项\r\n      flyjList: [], //分类依据下拉框选项\r\n      qxdjList: [], //隐患等级下拉框选项\r\n      qxlb: \"4\", //隐患类别（光伏）\r\n      filterText: \"\", //过滤\r\n      viewForm: {}, //查看表单\r\n      loading: null\r\n    };\r\n  },\r\n  watch: {\r\n    filterText(val) {\r\n      this.$refs.tree.filter(val);\r\n    }\r\n  },\r\n  created() {\r\n    this.queryParams.qxlb = this.qxlb;\r\n    this.getData();\r\n    this.getTreeData();\r\n    //设备类型下拉框\r\n    this.getSblxList();\r\n    //隐患等级下拉框\r\n    this.getQxdjList();\r\n  },\r\n  methods: {\r\n    //获取设备类型下拉框\r\n    async getSblxList() {\r\n      await getSblxList({ qxlb: this.qxlb }).then(res => {\r\n        this.sblxList = res.data;\r\n      });\r\n    },\r\n    //获取设备部件下拉框\r\n    async getSbbjList(sblx) {\r\n      await getSbbjList({ qxlb: this.qxlb, sblx: sblx }).then(res => {\r\n        this.sbbjList = res.data;\r\n      });\r\n    },\r\n    //获取设备部位下拉框\r\n    async getSbbwList(sbbj) {\r\n      await getSbbwList({ qxlb: this.qxlb, sbbj: sbbj }).then(res => {\r\n        this.sbbwList = res.data;\r\n      });\r\n    },\r\n    //获取隐患描述下拉框\r\n    async getQxmsList(sbbw) {\r\n      await getQxmsList({ qxlb: this.qxlb, sbbw: sbbw }).then(res => {\r\n        this.qxmsList = res.data;\r\n      });\r\n    },\r\n    //获取分类依据下拉框\r\n    async getFlyjList(qxms) {\r\n      await getFlyjList({ qxlb: this.qxlb, qxms: qxms }).then(res => {\r\n        this.flyjList = res.data;\r\n      });\r\n    },\r\n    //获取隐患等级字典数据\r\n    async getQxdjList() {\r\n      //查询隐患等级字典\r\n      await getDictTypeData(\"sbqxwh_qxdj\").then(res => {\r\n        this.qxdjList = res.data;\r\n        //给筛选条件赋值\r\n        this.filterInfo.fieldList.map(item => {\r\n          if (item.value == \"qxdj\") {\r\n            item.options = this.qxdjList;\r\n          }\r\n        });\r\n      });\r\n    },\r\n    //编辑\r\n    async updateRow(row) {\r\n      //开启遮罩层\r\n      this.loading = Loading.service({\r\n        lock: true, //lock的修改符--默认是false\r\n        text: \"加载中，请稍后\", //显示在加载图标下方的加载文案\r\n        spinner: \"el-icon-loading\", //自定义加载图标类名\r\n        background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\r\n        target: document.querySelector(\"#sbqxDiv\")\r\n      });\r\n      this.flyjForm = { ...row };\r\n      //下拉框回显\r\n      await this.getSbbjList(row.sblxbm);\r\n      await this.getSbbwList(row.parentSbbj);\r\n      await this.getQxmsList(row.parentSbbw);\r\n      this.isShowDetail = false;\r\n      this.addFlyj = false; //不是新增\r\n      this.isShowFlyj = true;\r\n      this.loading.close(); //关闭遮罩层\r\n    },\r\n    //删除\r\n    deleteRow(row) {\r\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        deleteFlyjById(row).then(res => {\r\n          if (res.code === \"0000\") {\r\n            this.$message({\r\n              type: \"success\",\r\n              message: \"删除成功!\"\r\n            });\r\n            this.getData();\r\n          } else {\r\n            this.$message({\r\n              type: \"error\",\r\n              message: \"删除失败!\"\r\n            });\r\n          }\r\n        });\r\n      });\r\n    },\r\n    //查看\r\n    viewFun(row) {\r\n      this.viewForm = { ...row };\r\n      this.isShowDetail = true;\r\n    },\r\n    //新增\r\n    addForm(formType) {\r\n      //先清空下拉框的值\r\n      this.sbbjList = [];\r\n      this.sbbwList = [];\r\n      this.qxmsList = [];\r\n      //如果树节点有值，则带过来\r\n      let sblx = this.queryParams.sblxbm ? this.queryParams.sblxbm : \"\";\r\n      let sbbj = this.queryParams.parentSbbj ? this.queryParams.parentSbbj : \"\";\r\n      let sbbw = this.queryParams.parentSbbw ? this.queryParams.parentSbbw : \"\";\r\n      this.isShowDetail = false;\r\n      switch (formType) {\r\n        case \"sbbj\": //设备部件\r\n          this.sbbjForm = {};\r\n          // this.$set(this.sbbjForm,'sblxbm',sblx);\r\n          this.isShowSbbj = true;\r\n          break;\r\n        case \"sbbw\": //设备部位\r\n          this.sbbwForm = {};\r\n          // this.$set(this.sbbwForm,'sblxbm',sblx);\r\n          // this.$set(this.sbbwForm,'parentSbbj',sbbj);\r\n          this.isShowSbbw = true;\r\n          break;\r\n        case \"qxms\": //隐患描述\r\n          this.qxmsForm = {};\r\n          // this.$set(this.qxmsForm,'sblxbm',sblx);\r\n          // this.$set(this.qxmsForm,'parentSbbj',sbbj);\r\n          // this.$set(this.qxmsForm,'parentSbbw',sbbw);\r\n          this.isShowQxms = true;\r\n          break;\r\n        case \"flyj\": //分类依据\r\n          this.flyjForm = {};\r\n          // this.$set(this.flyjForm,'sblxbm',sblx);\r\n          // this.$set(this.flyjForm,'parentSbbj',sbbj);\r\n          // this.$set(this.flyjForm,'parentSbbw',sbbw);\r\n          this.addFlyj = true;\r\n          this.isShowFlyj = true;\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n    //保存\r\n    async saveForm(formType) {\r\n      await this.$refs[formType].validate(valid => {\r\n        if (valid) {\r\n          let saveForm = { ...{ qxlb: this.qxlb } };\r\n          switch (formType) {\r\n            case \"flyjForm\": //新增分类依据\r\n              saveForm = { ...saveForm, ...this.flyjForm };\r\n              this.qxmsList.forEach(item => {\r\n                if (item.value === saveForm.parentQxms) {\r\n                  saveForm.qxms = item.label;\r\n                }\r\n              });\r\n              if (this.addFlyj) {\r\n                //新增\r\n                addFlyj(saveForm).then(res => {\r\n                  if (res.code === \"0000\") {\r\n                    this.$message.success(\"操作成功\");\r\n                    this.isShowFlyj = false;\r\n                    this.isShowQxms = false;\r\n                    this.isShowSbbw = false;\r\n                    this.isShowSbbj = false;\r\n                    this.isShowDetail = false;\r\n                    this.getData();\r\n                    //关闭弹框\r\n                  } else {\r\n                    this.$message.error(\"操作失败\");\r\n                  }\r\n                });\r\n              } else {\r\n                updateFlyj(saveForm).then(res => {\r\n                  //编辑\r\n                  if (res.code === \"0000\") {\r\n                    this.$message.success(\"操作成功\");\r\n                    this.isShowFlyj = false;\r\n                    this.isShowQxms = false;\r\n                    this.isShowSbbw = false;\r\n                    this.isShowSbbj = false;\r\n                    this.isShowDetail = false;\r\n                    this.getData();\r\n                    //关闭弹框\r\n                  } else {\r\n                    this.$message.error(\"操作失败\");\r\n                  }\r\n                });\r\n              }\r\n              break;\r\n            case \"qxmsForm\": //新增隐患描述\r\n              saveForm = { ...saveForm, ...this.qxmsForm };\r\n              this.sbbwList.forEach(item => {\r\n                if (item.value === saveForm.parentSbbw) {\r\n                  saveForm.sbbw = item.label;\r\n                }\r\n              });\r\n              addQxms(saveForm).then(res => {\r\n                if (res.code === \"0000\") {\r\n                  this.$message.success(\"操作成功\");\r\n                  this.isShowFlyj = false;\r\n                  this.isShowQxms = false;\r\n                  this.isShowSbbw = false;\r\n                  this.isShowSbbj = false;\r\n                  this.isShowDetail = false;\r\n                  this.getData();\r\n                  //关闭弹框\r\n                } else {\r\n                  this.$message.error(\"操作失败\");\r\n                }\r\n              });\r\n              break;\r\n            case \"sbbwForm\": //新增隐患描述\r\n              saveForm = { ...saveForm, ...this.sbbwForm };\r\n              this.sbbjList.forEach(item => {\r\n                if (item.value === saveForm.parentSbbj) {\r\n                  saveForm.sbbj = item.label;\r\n                }\r\n              });\r\n              addSbbw(saveForm).then(res => {\r\n                if (res.code === \"0000\") {\r\n                  this.$message.success(\"操作成功\");\r\n                  this.isShowFlyj = false;\r\n                  this.isShowQxms = false;\r\n                  this.isShowSbbw = false;\r\n                  this.isShowSbbj = false;\r\n                  this.isShowDetail = false;\r\n                  this.getData();\r\n                  //关闭弹框\r\n                } else {\r\n                  this.$message.error(\"操作失败\");\r\n                }\r\n              });\r\n              break;\r\n            case \"sbbjForm\": //新增隐患描述\r\n              saveForm = { ...saveForm, ...this.sbbjForm };\r\n              this.sblxList.forEach(item => {\r\n                if (item.value === saveForm.sblxbm) {\r\n                  saveForm.sblx = item.label;\r\n                }\r\n              });\r\n              addSbbj(saveForm).then(res => {\r\n                if (res.code === \"0000\") {\r\n                  this.$message.success(\"操作成功\");\r\n                  this.isShowFlyj = false;\r\n                  this.isShowQxms = false;\r\n                  this.isShowSbbw = false;\r\n                  this.isShowSbbj = false;\r\n                  this.isShowDetail = false;\r\n                  this.getData();\r\n                  //关闭弹框\r\n                } else {\r\n                  this.$message.error(\"操作失败\");\r\n                }\r\n              });\r\n              break;\r\n            default:\r\n              break;\r\n          }\r\n        } else {\r\n          this.$message.error(\"校验未通过！\");\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    //设备类型下拉框事件\r\n    async sblxChangeFun(val) {\r\n      this.clearFormField(\"sblx\");\r\n      await this.getSbbjList(val);\r\n    },\r\n    //设备部件下拉框事件\r\n    async sbbjChangeFun(val) {\r\n      this.clearFormField(\"sbbj\");\r\n      await this.getSbbwList(val);\r\n    },\r\n    //设备部位下拉框事件\r\n    async sbbwChangeFun(val) {\r\n      this.clearFormField(\"sbbw\");\r\n      await this.getQxmsList(val);\r\n    },\r\n    //隐患描述下拉框事件\r\n    async qxmsChangeFun(val) {\r\n      this.clearFormField(\"qxms\");\r\n      await this.getFlyjList(val);\r\n    },\r\n    //清空字段值\r\n    clearFormField(type) {\r\n      switch (type) {\r\n        case \"sblx\": //设备类型\r\n          this.$set(this.sbbjForm, \"sbbj\", \"\");\r\n          this.$set(this.sbbwForm, \"parentSbbj\", \"\");\r\n          this.$set(this.qxmsForm, \"parentSbbj\", \"\");\r\n          this.$set(this.flyjForm, \"parentSbbj\", \"\");\r\n          this.clearFormField(\"sbbj\");\r\n          break;\r\n        case \"sbbj\": //设备部件\r\n          this.$set(this.sbbwForm, \"sbbw\", \"\");\r\n          this.$set(this.qxmsForm, \"parentSbbw\", \"\");\r\n          this.$set(this.flyjForm, \"parentSbbw\", \"\");\r\n          this.clearFormField(\"sbbw\");\r\n          break;\r\n        case \"sbbw\": //设备部位\r\n          this.$set(this.qxmsForm, \"qxms\", \"\");\r\n          this.$set(this.flyjForm, \"parentQxms\", \"\");\r\n          this.clearFormField(\"qxms\");\r\n          break;\r\n        case \"qxms\": //隐患描述\r\n          this.$set(this.flyjForm, \"flyj\", \"\");\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n    //关闭\r\n    closeFun(type) {\r\n      this.isShowDetail = false;\r\n      switch (type) {\r\n        case \"sbbj\":\r\n          this.isShowSbbj = false;\r\n          break;\r\n        case \"sbbw\":\r\n          this.isShowSbbw = false;\r\n          break;\r\n        case \"qxms\":\r\n          this.isShowQxms = false;\r\n          break;\r\n        case \"flyj\":\r\n          this.isShowFlyj = false;\r\n          break;\r\n        case \"view\":\r\n          this.isShowDetail = false;\r\n          break;\r\n        default:\r\n          this.isShowSbbj = false;\r\n          this.isShowSbbw = false;\r\n          this.isShowQxms = false;\r\n          this.isShowFlyj = false;\r\n          this.isShowDetail = false;\r\n          break;\r\n      }\r\n    },\r\n    //重置按钮\r\n    filterReset() {\r\n      this.queryParams = { qxlb: this.qxlb }; //重置条件\r\n    },\r\n\r\n    //树监听事件\r\n    filterNode(value, data) {\r\n      if (!value) return true;\r\n      return data.label.indexOf(value) !== -1;\r\n    },\r\n    getTreeData() {\r\n      getQxsbTree({ qxlb: this.qxlb }).then(res => {\r\n        this.treeOptions = res.data;\r\n      });\r\n    },\r\n    //树节点点击事件\r\n    handleNodeClick(node) {\r\n      this.treeNodeData = node;\r\n      if (node.identifier === \"1\") {\r\n        //设备类型\r\n        this.queryParams.sblxbm = node.id;\r\n        this.queryParams.parentSbbj = \"\";\r\n        this.queryParams.parentSbbw = \"\";\r\n      } else if (node.identifier === \"2\") {\r\n        //设备部件\r\n        this.queryParams.sblxbm = \"\";\r\n        this.queryParams.parentSbbj = node.id;\r\n        this.queryParams.parentSbbw = \"\";\r\n      } else if (node.identifier === \"3\") {\r\n        //设备部位\r\n        this.queryParams.sblxbm = \"\";\r\n        this.queryParams.parentSbbj = \"\";\r\n        this.queryParams.parentSbbw = node.id;\r\n      } else {\r\n        this.queryParams = { qxlb: this.qxlb };\r\n      }\r\n      this.getData();\r\n    },\r\n    //查询列表\r\n    getData(params) {\r\n      this.load = true;\r\n      this.queryParams = { ...this.queryParams, ...params };\r\n      getQxList(this.queryParams).then(res => {\r\n        this.tableAndPageInfo.tableData = res.data.records;\r\n        this.tableAndPageInfo.pager.total = res.data.total;\r\n        this.load = false;\r\n      });\r\n    },\r\n    //导出excel\r\n    exportExcel() {\r\n      let fileName = \"隐患标准库\";\r\n      let exportUrl = \"/bzqxFlyj\";\r\n      // if(this.selectData.length > 0){\r\n      //   // this.$message.warning('请在左侧勾选要导出的数据')\r\n      //   // return\r\n      //   exportExcel(exportUrl, this.queryParams, fileName);\r\n      // }\r\n      exportExcel(exportUrl, this.queryParams, fileName);\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* 设置滚动条的样式 */\r\n::-webkit-scrollbar {\r\n  width: 12px;\r\n}\r\n\r\n/* 滚动槽 */\r\n::-webkit-scrollbar-track {\r\n  //-webkit-box-shadow:inset006pxrgba(0,0,0,0.3);\r\n  border-radius: 10px;\r\n}\r\n\r\n/* 滚动条滑块 */\r\n::-webkit-scrollbar-thumb {\r\n  border-radius: 10px;\r\n  background: rgba(0, 0, 0, 0.1);\r\n  //-webkit-box-shadow:gba(0,0,0,0.5);\r\n}\r\n\r\n::-webkit-scrollbar-thumb:window-inactive {\r\n  background: rgba(0, 0, 0, 0.1);\r\n}\r\n.head-container {\r\n  margin: 0 auto;\r\n  width: 98%;\r\n  height: 82.6vh;\r\n  max-height: 82.6vh;\r\n  overflow: auto;\r\n}\r\n/*给左侧数结构header加颜色*/\r\n.box-card .el-card__header {\r\n  background: #11ba6d !important;\r\n}\r\n.box-card {\r\n  margin: 0;\r\n}\r\n\r\n.item {\r\n  width: 200px;\r\n  height: 148px;\r\n  float: left;\r\n}\r\n\r\n.tree {\r\n  overflow-y: hidden;\r\n  overflow-x: scroll;\r\n  width: 80px;\r\n  height: 500px;\r\n}\r\n\r\n.el-tree {\r\n  min-width: 100%;\r\n  display: inline-block !important;\r\n}\r\n/deep/ .el-dialog:not(.is-fullscreen) {\r\n  margin-top: 8vh !important;\r\n}\r\n</style>\r\n<style></style>\r\n"]}]}