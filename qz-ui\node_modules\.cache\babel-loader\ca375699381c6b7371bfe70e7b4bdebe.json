{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\zxmInfo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\zxmInfo.vue", "mtime": 1706897323692}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmluZCIpOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmZvci1lYWNoIik7CgpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuaW5jbHVkZXMiKTsKCnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5tYXAiKTsKCnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5udW1iZXIuY29uc3RydWN0b3IiKTsKCnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nIik7CgpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLnRvLXN0cmluZyIpOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pbmNsdWRlcyIpOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pdGVyYXRvciIpOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuZm9yLWVhY2giKTsKCnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLml0ZXJhdG9yIik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKCnJlcXVpcmUoInJlZ2VuZXJhdG9yLXJ1bnRpbWUvcnVudGltZSIpOwoKdmFyIF9hc3luY1RvR2VuZXJhdG9yMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovU2hhbW1wb29sL3dvcmsvY29kZS9kZ3l0LzAxXHU0RUUzXHU3ODAxL3F6LXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2FzeW5jVG9HZW5lcmF0b3IiKSk7Cgp2YXIgX2VsZW1lbnRVaSA9IHJlcXVpcmUoImVsZW1lbnQtdWkiKTsKCnZhciBfc3ltcEluZm8gPSByZXF1aXJlKCJAL2FwaS9kYWdhbmdPaWxmaWVsZC9iemdsL3N5bXBrL3N5bXBJbmZvIik7Cgp2YXIgX3N5bXBrID0gcmVxdWlyZSgiQC9hcGkvZGFnYW5nT2lsZmllbGQvYnpnbC9zeW1way9zeW1wayIpOwoKdmFyIF9zeXhtID0gcmVxdWlyZSgiQC9hcGkvZGFnYW5nT2lsZmllbGQvYnpnbC9zeXhtIik7Cgp2YXIgX2RhdGFDaGFydCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC92aWV3cy9kYWdhbmdPaWxmaWVsZC9iemdsL3N5YnprL2RhdGFDaGFydC52dWUiKSk7CgovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSB7CiAgY29tcG9uZW50czogewogICAgZGF0YUNoYXJ0OiBfZGF0YUNoYXJ0LmRlZmF1bHQKICB9LAogIHByb3BzOiB7CiAgICBtcERhdGE6IHsKICAgICAgdHlwZTogT2JqZWN0CiAgICB9LAogICAgbXhEYXRhOiB7CiAgICAgIHR5cGU6IEFycmF5CiAgICB9CiAgfSwKICBuYW1lOiAienhtSW5mbyIsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8v5Yid5aeL6KGo5qC855qE6KGM5pWwIOWIl+aVsAogICAgICBoczogIiIsCiAgICAgIGxzOiAiIiwKICAgICAgLy/liJ3lp4vlkIjlubbooYzmlbAg5YiX5pWwCiAgICAgIGFkZGhzOiAiIiwKICAgICAgYWRkbHM6ICIiLAogICAgICBucmx4OiAiIiwKICAgICAgdGV4dDogIiIsCiAgICAgIC8v5LiA6KGM55qE5pWw5o2uCiAgICAgIGNlbGxEYXRhOiAiIiwKICAgICAgLy/pgInkuK3lkIjlubbooYzjgIHliJfnmoR0cgogICAgICBjaGFuZ2VUcjogIiIsCiAgICAgIC8v5p+l6K+i5p2h5Lu2CiAgICAgIHBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIG1waWQ6ICIiLAogICAgICAgIHp5OiAiIiwKICAgICAgICBzYmx4Ym06ICIiLAogICAgICAgIHp4bUlkOiAiIgogICAgICB9LAogICAgICBsb2FkaW5nOiBudWxsLAogICAgICAvL+mBrue9qeWxggogICAgICB0ZFdpZHRoOiAwLAogICAgICAvL+S4gOS4quWNleWFg+agvOaJgOWNoOWuveW6pgogICAgICB0ZE1hcDogbmV3IE1hcCgpLAogICAgICAvL+eUqOS6juWtmOaUvuiiq+WQiOW5tuaIluaLhuWIhueahOWNleWFg+agvO+8iGtleTrlvZPliY3ngrnlh7vnmoTljZXlhYPmoLwsdmFsdWU66KKr5aSE55CG6L+H55qE5Y2V5YWD5qC85pWw57uE77yJCiAgICAgIHRhYmxlRGF0YTogdGhpcy5teERhdGEsCiAgICAgIC8v6KGo5qC85pWw5o2uCiAgICAgIHNibHhibTogdW5kZWZpbmVkLAogICAgICAvL+iuvuWkh+exu+Wei+e8lueggQogICAgICB0aXRsZTogIuWNleWFg+agvOWxnuaAp+WumuS5iSIsCiAgICAgIGZvcm06IHsKICAgICAgICBvYmpJZDogdW5kZWZpbmVkLAogICAgICAgIHJlYWRvbmx5OiB1bmRlZmluZWQsCiAgICAgICAgbnJseDogdW5kZWZpbmVkLAogICAgICAgIGJ3SWQ6IHVuZGVmaW5lZCwKICAgICAgICB6eG1JZDogdW5kZWZpbmVkLAogICAgICAgIG5yYnM6IHVuZGVmaW5lZAogICAgICB9LAogICAgICBzaG93OiBmYWxzZSwKICAgICAgYndMaXN0OiBbXSwKICAgICAgLy/or5Xpqozpg6jkvY0KICAgICAgenhtbURhdGFMaXN0OiBbXSwKICAgICAgLy/lrZDpobnnm67miYDmnInnu5PmnpzmlbDmja4KICAgICAgenhtbWNMaXN0OiBbXSwKICAgICAgLy/lrZDpobnnm64KICAgICAgaGlkZGU6IGZhbHNlLAogICAgICBoaWRkZWJ3OiBmYWxzZSwKICAgICAgaGlkZGV6eG06IGZhbHNlLAogICAgICBucmx4TGlzdDogW3sKICAgICAgICBsYWJlbDogIumdmeaAgeaWh+acrCIsCiAgICAgICAgdmFsdWU6ICLpnZnmgIHmlofmnKwiCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogIuivlemqjOWtkOmhueebriIsCiAgICAgICAgdmFsdWU6ICLor5XpqozlrZDpobnnm64iCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogIuivlemqjOmDqOS9jSIsCiAgICAgICAgdmFsdWU6ICLor5Xpqozpg6jkvY0iCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogIuivlemqjOaVsOaNriIsCiAgICAgICAgdmFsdWU6ICLor5XpqozmlbDmja4iCiAgICAgIH1dLAogICAgICByZWFkb25seUxpc3Q6IFt7CiAgICAgICAgbGFiZWw6ICLmmK8iLAogICAgICAgIHZhbHVlOiAiWSIKICAgICAgfSwgewogICAgICAgIGxhYmVsOiAi5ZCmIiwKICAgICAgICB2YWx1ZTogIk4iCiAgICAgIH1dCiAgICB9OwogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIC8v6I635Y+W6KGo5qC85Yid5aeL6KGM5pWw5ZKM5YiX5pWwCiAgICB0aGlzLmluaXRUYWJsZURhdGEoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8v6I635Y+W6ZOt54mM5YaF5a655pWw5o2uCiAgICBpbml0VGFibGVEYXRhOiBmdW5jdGlvbiBpbml0VGFibGVEYXRhKCkgewogICAgICAvL+WIneWni+WMlumBrue9qeWxggogICAgICB0aGlzLmxvYWRpbmcgPSBfZWxlbWVudFVpLkxvYWRpbmcuc2VydmljZSh7CiAgICAgICAgdGV4dDogIuWKoOi9veS4re+8jOivt+eojeWQji4uLiIsCiAgICAgICAgYmFja2dyb3VuZDogInJnYmEoMTA5LDEwNiwxMDYsMC4zNSkiCiAgICAgIH0pOwogICAgICB0aGlzLmhzID0gdHlwZW9mIHRoaXMubXBEYXRhLkFIcyAhPSAidW5kZWZpbmVkIiA/IHRoaXMubXBEYXRhLkFIcyA6IHRoaXMubXBEYXRhLmFIczsKICAgICAgdGhpcy5scyA9IHR5cGVvZiB0aGlzLm1wRGF0YS5BTHMgIT0gInVuZGVmaW5lZCIgPyB0aGlzLm1wRGF0YS5BTHMgOiB0aGlzLm1wRGF0YS5hTHM7CiAgICAgIHRoaXMuc2JseGJtID0gdGhpcy5tcERhdGEuc2JseGJtOyAvL+abtOaWsOi+k+WFpeahhueahOWAvAoKICAgICAgdGhpcy51cGRhdGVJbnB1dFZhbHVlKFsiaHMiLCAibHMiXSk7CiAgICAgIHRoaXMucHJvY2Vzc1RhYmxlKCk7CiAgICAgIHRoaXMubG9hZGluZy5jbG9zZSgpOyAvL+WFs+mXremBrue9qeWxggogICAgfSwKICAgIHNlbGVjdHp4bXZhbHVlOiBmdW5jdGlvbiBzZWxlY3R6eG12YWx1ZSh2YWwpIHsKICAgICAgdmFyIG9iaiA9IHt9OwogICAgICBvYmogPSB0aGlzLnp4bW1jTGlzdC5maW5kKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgcmV0dXJuIGl0ZW0udmFsdWUgPT09IHZhbDsKICAgICAgfSk7CiAgICAgIHRoaXMuZm9ybS5ucmJzID0gb2JqLmxhYmVyOwogICAgfSwKICAgIHNlbGVjdGJ3dmFsdWU6IGZ1bmN0aW9uIHNlbGVjdGJ3dmFsdWUodmFsKSB7CiAgICAgIHZhciBvYmogPSB7fTsKICAgICAgb2JqID0gdGhpcy5id0xpc3QuZmluZChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLnZhbHVlID09PSB2YWw7CiAgICAgIH0pOwogICAgICB0aGlzLmZvcm0ubnJicyA9IG9iai5sYWJlcjsKICAgIH0sCiAgICBzZWxlY3R2YWx1ZTogZnVuY3Rpb24gc2VsZWN0dmFsdWUodmFsKSB7CiAgICAgIHZhciB2YWx1ZSA9IHZhbC52YWx1ZSwKICAgICAgICAgIGxhYmVsID0gdmFsLmxhYmVsOwoKICAgICAgaWYgKGxhYmVsID09ICLpnZnmgIHmlofmnKwiKSB7CiAgICAgICAgdGhpcy5oaWRkZSA9IHRydWU7CiAgICAgICAgdGhpcy5oaWRkZWJ3ID0gZmFsc2U7CiAgICAgICAgdGhpcy5oaWRkZXp4bSA9IGZhbHNlOwogICAgICAgIHRoaXMuZm9ybS5ucmx4ID0gIumdmeaAgeaWh+acrCI7CiAgICAgIH0KCiAgICAgIGlmIChsYWJlbCA9PSAi6K+V6aqM5a2Q6aG555uuIikgewogICAgICAgIHRoaXMuaGlkZGV6eG0gPSB0cnVlOwogICAgICAgIHRoaXMuaGlkZGVidyA9IGZhbHNlOwogICAgICAgIHRoaXMuaGlkZGUgPSBmYWxzZTsKICAgICAgICB0aGlzLmZvcm0ubnJseCA9ICLor5XpqozlrZDpobnnm64iOwogICAgICB9CgogICAgICBpZiAobGFiZWwgPT0gIuivlemqjOmDqOS9jSIpIHsKICAgICAgICB0aGlzLmhpZGRlYncgPSB0cnVlOwogICAgICAgIHRoaXMuaGlkZGV6eG0gPSBmYWxzZTsKICAgICAgICB0aGlzLmhpZGRlID0gZmFsc2U7CiAgICAgICAgdGhpcy5mb3JtLm5ybHggPSAi6K+V6aqM6YOo5L2NIjsKICAgICAgfQoKICAgICAgaWYgKGxhYmVsID09ICLor5XpqozmlbDmja4iKSB7CiAgICAgICAgdGhpcy5oaWRkZSA9IGZhbHNlOwogICAgICAgIHRoaXMuaGlkZGVidyA9IGZhbHNlOwogICAgICAgIHRoaXMuaGlkZGV6eG0gPSBmYWxzZTsKICAgICAgICB0aGlzLmZvcm0ubnJseCA9ICLor5XpqozmlbDmja4iOwogICAgICB9CiAgICB9LAogICAgLy/ojrflj5bpg6jkvY3kuIvmi4nmoYYKICAgIGdldFN5Ync6IGZ1bmN0aW9uIGdldFN5YncoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CgogICAgICAoMCwgX3N5eG0uZ2V0QndTZWxlY3QpKHsKICAgICAgICBzYmx4Ym06IHRoaXMuc2JseGJtCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzLmJ3TGlzdCA9IHJlcy5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDojrflj5bor5XpqozlrZDpobnnm67kuIvmi4nmoYbmlbDmja4KICAgIGdldFN5enhtOiBmdW5jdGlvbiBnZXRTeXp4bSgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CgogICAgICAoMCwgX3N5eG0uZ2V0WnhtU2VsZWN0KSgpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzMi56eG1tY0xpc3QgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLy/moLnmja7ooYzmlbDlkozliJfmlbDliJvlu7rooajmoLwKICAgIHByb2Nlc3NUYWJsZTogZnVuY3Rpb24gcHJvY2Vzc1RhYmxlKCkgewogICAgICB2YXIgdGJvZHkgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgibXB4cV9yaWdodCIpOwoKICAgICAgaWYgKHRib2R5ICE9IG51bGwpIHsKICAgICAgICB0Ym9keS5pbm5lckhUTUwgPSAiIjsKICAgICAgICB2YXIgaHMgPSB0aGlzLmhzOwogICAgICAgIHZhciBscyA9IHRoaXMubHM7CiAgICAgICAgdGhpcy50ZFdpZHRoID0gMTAwIC8gTnVtYmVyKGxzKTsKICAgICAgICB2YXIgc3RyID0gIiI7CgogICAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgaHM7IGkrKykgewogICAgICAgICAgdmFyIHRlbXAgPSAiPHRyPiI7CgogICAgICAgICAgZm9yICh2YXIgaiA9IDA7IGogPCB0aGlzLnRhYmxlRGF0YS5sZW5ndGg7IGorKykgewogICAgICAgICAgICB2YXIgaXRlbSA9IHRoaXMudGFibGVEYXRhW2pdOwogICAgICAgICAgICB2YXIgc2pseCA9IGl0ZW0uc2pseDsgLy/mlbDmja7nsbvlnosKCiAgICAgICAgICAgIHZhciB0ZXh0ID0gaXRlbS50ZXh0OwogICAgICAgICAgICB2YXIgbnJicyA9IGl0ZW0ubnJicyA9PSBudWxsID8gdGV4dCA/IHRleHQgOiAiIiA6IGl0ZW0ubnJiczsKCiAgICAgICAgICAgIGlmIChpdGVtLnJvd2luZGV4ID09PSBpLnRvU3RyaW5nKCkpIHsKICAgICAgICAgICAgICB0ZW1wICs9ICI8dGQgY2xhc3M9J3RyTmFtZScgaWQ9JyIgKyBpdGVtLm9iaklkICsgIicgc3R5bGU9J3dpZHRoOiAiICsgdGhpcy50ZFdpZHRoICogaXRlbS5jb2xzcGFuICsgIiUnIHJvd3NwYW49JyIgKyBpdGVtLnJvd3NwYW4gKyAiJyBjb2xzcGFuPSciICsgaXRlbS5jb2xzcGFuICsgIic+IiArIG5yYnMgKyAiPC90ZD4iOwogICAgICAgICAgICB9CiAgICAgICAgICB9CgogICAgICAgICAgdGVtcCArPSAiPC90cj4iOwogICAgICAgICAgc3RyICs9IHRlbXA7CiAgICAgICAgfQoKICAgICAgICB0Ym9keS5pbm5lckhUTUwgPSBzdHI7IC8vIC8v57uZ5b6q546v5Ye65p2l55qE5Y2V5YWD5qC85Yqg5LiK54K55Ye75LqL5Lu2CgogICAgICAgIHRoaXMuYWRkQ2xpY2tFdmVudCgpOwogICAgICB9CiAgICB9LAogICAgLy/miYvliqjliJvlu7rooajmoLwKICAgIGNyZWF0ZVRhYmxlOiBmdW5jdGlvbiBjcmVhdGVUYWJsZSgpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CgogICAgICAvL+WIneWni+WMlumBrue9qeWxggogICAgICB0aGlzLmxvYWRpbmcgPSBfZWxlbWVudFVpLkxvYWRpbmcuc2VydmljZSh7CiAgICAgICAgdGV4dDogIuWKoOi9veS4re+8jOivt+eojeWQji4uLiIsCiAgICAgICAgYmFja2dyb3VuZDogInJnYmEoMTA5LDEwNiwxMDYsMC4zNSkiCiAgICAgIH0pOwogICAgICB2YXIgcGFyYW1zID0gSlNPTi5zdHJpbmdpZnkoewogICAgICAgIG9iaklkOiB0aGlzLm1wRGF0YS5vYmpJZCwKICAgICAgICAvL+mTreeJjGlkCiAgICAgICAgYUhzOiBOdW1iZXIodGhpcy5ocyksCiAgICAgICAgLy/ooYzmlbAKICAgICAgICBhTHM6IE51bWJlcih0aGlzLmxzKSwKICAgICAgICAvL+WIl+aVsAogICAgICAgIGxiYnM6ICJBIiAvL+exu+WIq+agh+ivhu+8jOihqOekuuS/ruaUueeahEHooajmoLwKCiAgICAgIH0pOwogICAgICAoMCwgX3N5bXBJbmZvLmNyZWF0ZVRhYmxlKShwYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIGlmIChyZXMuY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICBfdGhpczMudXBkYXRlVGFibGUoKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgX3RoaXMzLiRtZXNzYWdlLmVycm9yKCLmk43kvZzlpLHotKUiKTsKCiAgICAgICAgICBfdGhpczMubG9hZGluZy5jbG9zZSgpOyAvL+WFs+mXremBrue9qeWxggoKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8v57uZ5b6q546v5Ye65p2l55qE5Y2V5YWD5qC85Yqg5LiK54K55Ye75LqL5Lu2CiAgICBhZGRDbGlja0V2ZW50OiBmdW5jdGlvbiBhZGRDbGlja0V2ZW50KCkgewogICAgICB2YXIgdHJBcnIgPSBkb2N1bWVudC5nZXRFbGVtZW50c0J5Q2xhc3NOYW1lKCJ0ck5hbWUiKTsgLy/kuI3lj6/nvJbovpHnmoTljZXlhYPmoLwKCiAgICAgIHZhciBpbnB1dEFyciA9IGRvY3VtZW50LmdldEVsZW1lbnRzQnlDbGFzc05hbWUoImlucHV0X2NscyIpOyAvL+WPr+e8lui+keeahOWNleWFg+agvAoKICAgICAgdmFyIHRoYXQgPSB0aGlzOwoKICAgICAgaWYgKHRyQXJyICE9IG51bGwpIHsKICAgICAgICAvL+W+queOr+aJgOacieeahHRyCiAgICAgICAgZm9yICh2YXIgaSA9IDA7IGkgPCB0ckFyci5sZW5ndGg7IGkrKykgewogICAgICAgICAgdHJBcnJbaV0ub25jbGljayA9IGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgdGhhdC5jaGFuZ2VUciA9IHRoaXM7CiAgICAgICAgICAgIHRoYXQuYWRkaHMgPSB0aGF0LmNoYW5nZVRyLnJvd1NwYW47CiAgICAgICAgICAgIHRoYXQuYWRkbHMgPSB0aGF0LmNoYW5nZVRyLmNvbFNwYW47CiAgICAgICAgICAgIHRoYXQuY2VsbERhdGEgPSB0aGF0LmdldENlbGxFbGUodGhhdC5jaGFuZ2VUci5pZCk7CiAgICAgICAgICAgIHRoYXQubnJseCA9IHRoYXQuY2VsbERhdGEubnJseDsKICAgICAgICAgICAgdGhhdC50ZXh0ID0gdGhhdC5jZWxsRGF0YS50ZXh0OwogICAgICAgICAgfTsKICAgICAgICB9CiAgICAgIH0KCiAgICAgIGlmIChpbnB1dEFyciAhPSBudWxsKSB7CiAgICAgICAgLy/lvqrnjq/miYDmnInnmoR0cgogICAgICAgIGZvciAodmFyIF9pID0gMDsgX2kgPCBpbnB1dEFyci5sZW5ndGg7IF9pKyspIHsKICAgICAgICAgIGlucHV0QXJyW19pXS5vbmNsaWNrID0gZnVuY3Rpb24gKCkgewogICAgICAgICAgICB0aGF0LmNoYW5nZVRyID0gdGhpczsKICAgICAgICAgICAgdGhhdC5hZGRocyA9IHRoYXQuY2hhbmdlVHIucm93U3BhbjsKICAgICAgICAgICAgdGhhdC5hZGRscyA9IHRoYXQuY2hhbmdlVHIuY29sU3BhbjsKICAgICAgICAgICAgdGhhdC5jZWxsRGF0YSA9IHRoYXQuZ2V0Q2VsbEVsZSgpOwogICAgICAgICAgICB0aGF0Lm5ybHggPSB0aGF0LmNlbGxEYXRhLm5ybHg7CiAgICAgICAgICAgIHRoYXQudGV4dCA9IHRoYXQuY2VsbERhdGEudGV4dDsKICAgICAgICAgICAgY29uc29sZS5sb2codGhhdC5jZWxsRGF0YSk7CiAgICAgICAgICB9OwogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIC8v5ZCI5bm25ouG5YiG5L+d5a2YCiAgICBzYXZlQ2hhbmdlVGFibGU6IGZ1bmN0aW9uIHNhdmVDaGFuZ2VUYWJsZSgpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CgogICAgICAvL+WIneWni+WMlumBrue9qeWxggogICAgICB0aGlzLmxvYWRpbmcgPSBfZWxlbWVudFVpLkxvYWRpbmcuc2VydmljZSh7CiAgICAgICAgdGV4dDogIuWKoOi9veS4re+8jOivt+eojeWQji4uLiIsCiAgICAgICAgYmFja2dyb3VuZDogInJnYmEoMTA5LDEwNiwxMDYsMC4zNSkiCiAgICAgIH0pOwogICAgICB2YXIgcGFyYW1zID0gSlNPTi5zdHJpbmdpZnkoewogICAgICAgIG9iaklkOiB0aGlzLmNoYW5nZVRyLmlkLAogICAgICAgIHJvd3NwYW46IHRoaXMuYWRkaHMsCiAgICAgICAgY29sc3BhbjogdGhpcy5hZGRscwogICAgICB9KTsgLy/lhYjor7fmsYLmjqXlj6PvvIzlpoLmnpzlkI7lj7Dlj6/ku6XmiafooYzlkIjlubbmiJbmi4bliIbvvIzliJnlsIbmnIDmlrDnmoTooajmoLzmlbDmja7or7fmsYLlm57mnaXov5vooYzliY3nq6/lsZXnpLoKCiAgICAgICgwLCBfc3ltcEluZm8ubWVyZ2VDZWxscykocGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBpZiAocmVzLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgX3RoaXM0LnVwZGF0ZVRhYmxlKCk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIF90aGlzNC4kbWVzc2FnZS5lcnJvcigi5pON5L2c5aSx6LSlIik7CgogICAgICAgICAgX3RoaXM0LmxvYWRpbmcuY2xvc2UoKTsgLy/lhbPpl63pga7nvanlsYIKCiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvL+WkhOeQhuWQiOW5tuaIluaLhuWIhgogICAgcHJvY2Vzc1RyOiBmdW5jdGlvbiBwcm9jZXNzVHIoaWRzKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwoKICAgICAgLy/ngrnlh7vnmoTljZXlhYPmoLxpZAogICAgICB2YXIgY2xpY2tJZCA9IHRoaXMuY2hhbmdlVHIuaWQ7CiAgICAgIHZhciBhcnIxID0gW107IC8v6ZyA6KaB6YeN5paw6K6+572ubWFw55qE5pWw57uECiAgICAgIC8v5aaC5p6c5LmL5YmN5bey57uP5aSE55CG6L+H6K+l5Y2V5YWD5qC8LOWImeWFiOWwhuWFtui/mOWOnwoKICAgICAgaWYgKHRoaXMudGRNYXAuaGFzKGNsaWNrSWQpKSB7CiAgICAgICAgdGhpcy50ZE1hcC5nZXQoY2xpY2tJZCkuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgaWYgKGl0ZW0gIT0gbnVsbCkgewogICAgICAgICAgICBfdGhpczUucmVzZXRDZWxsKGl0ZW0pOwoKICAgICAgICAgICAgaXRlbS5zdHlsZS5kaXNwbGF5ID0gInRhYmxlLWNlbGwiOwogICAgICAgICAgfQogICAgICAgIH0pOyAvL+aTjeS9nOWujOWQjuWwhuaVsOaNruS7jm1hcOS4reWIoOmZpAoKICAgICAgICB0aGlzLnRkTWFwLmRlbGV0ZShjbGlja0lkKTsKICAgICAgfQoKICAgICAgdmFyIHByb2Nlc3NFbGUgPSBbXTsgLy/ooqvlpITnkIbnmoTlhYPntKAKICAgICAgLy/njrDlsIbov57luKblj5flvbHlk43nmoTljZXlhYPmoLzov5jljp/vvIzlho3ov5vooYzpmpDol4/lpITnkIYKCiAgICAgIGlmIChpZHMubGVuZ3RoID4gMCkgewogICAgICAgIC8v5omn6KGM6L+Y5Y6fCiAgICAgICAgaWRzLmZvckVhY2goZnVuY3Rpb24gKGlkMSkgewogICAgICAgICAgdmFyIGVsZSA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKGlkMSk7IC8v5aaC5p6c5q2k5qyh5aSE55CG55qE5Y2V5YWD5qC85Lit5pyJ5bey57uP6KKr5aSE55CG6L+H55qE77yM5YWI5bCG5YW26L+Y5Y6fCgogICAgICAgICAgaWYgKF90aGlzNS50ZE1hcC5oYXMoaWQxKSkgewogICAgICAgICAgICBfdGhpczUudGRNYXAuZ2V0KGlkMSkuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICAgIF90aGlzNS5yZXNldENlbGwoaXRlbSk7CgogICAgICAgICAgICAgIGl0ZW0uc3R5bGUuZGlzcGxheSA9ICJ0YWJsZS1jZWxsIjsKICAgICAgICAgICAgfSk7IC8v5pON5L2c5a6M5ZCO5bCG5pWw5o2u5LuObWFw5Lit5Yig6ZmkCgoKICAgICAgICAgICAgX3RoaXM1LnRkTWFwLmRlbGV0ZShpZDEpOwogICAgICAgICAgfSAvL+WkhOeQhuiiq+i/nuW4pueahOW3sue7j+WQiOW5tui/h+eahOWNleWFg+agvAoKCiAgICAgICAgICBpZiAoZWxlKSB7CiAgICAgICAgICAgIHZhciBjbGFzc05hbWUgPSBlbGUuY2xhc3NOYW1lOwoKICAgICAgICAgICAgaWYgKF90aGlzNS50ZE1hcC5oYXMoY2xhc3NOYW1lKSkgewogICAgICAgICAgICAgIHZhciBtZXJnZUNlbGwgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChjbGFzc05hbWUpOyAvL+iiq+i/nuW4pueahOW3sue7j+WQiOW5tui/h+eahGNlbGwKCiAgICAgICAgICAgICAgaWYgKG1lcmdlQ2VsbCkgewogICAgICAgICAgICAgICAgX3RoaXM1LnJlc2V0Q2VsbChtZXJnZUNlbGwpOwogICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgX3RoaXM1LnRkTWFwLmdldChjbGFzc05hbWUpLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgICAgIC8v6ZyA6KaB5oqK5q2k5qyh6KaB6ZqQ6JeP55qE5Y2V5YWD5qC85o6S6Zmk5o6J77yM5LiN54S26ZqQ6JeP5a6M5LiL5qyh5b6q546v5Y+I5Lya5pS+5Ye65p2lCiAgICAgICAgICAgICAgICBpZiAoIWlkcy5pbmNsdWRlcyhpdGVtKSkgewogICAgICAgICAgICAgICAgICBpdGVtLnN0eWxlLmRpc3BsYXkgPSAidGFibGUtY2VsbCI7CiAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICBhcnIxLnB1c2goaXRlbSk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSk7IC8v5aSE55CG5a6M5oiQ5ZCO77yM5pu05pawbWFw5Lit55qE5YC877yM5bCG5aSE55CG6L+H55qE5o6S6Zmk5o6JCgoKICAgICAgICAgICAgICBpZiAoYXJyMS5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgICAgICBfdGhpczUudGRNYXAuc2V0KGNsYXNzTmFtZSwgYXJyMSk7CiAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIC8v5pON5L2c5a6M5ZCO5bCG5pWw5o2u5LuObWFw5Lit5Yig6ZmkCiAgICAgICAgICAgICAgICBfdGhpczUudGRNYXAuZGVsZXRlKGNsYXNzTmFtZSk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSk7IC8v5omn6KGM6ZqQ6JePCgogICAgICAgIGlkcy5mb3JFYWNoKGZ1bmN0aW9uIChpZCkgewogICAgICAgICAgdmFyIGVsZSA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKGlkKTsgLy/lsIblpJrkvZnnmoTljZXlhYPmoLzpmpDol48KCiAgICAgICAgICBpZiAoZWxlKSB7CiAgICAgICAgICAgIHByb2Nlc3NFbGUucHVzaChlbGUpOyAvL+a3u+WKoOaVsOaNruS/neWtmOWIsG1hcOS4rQoKICAgICAgICAgICAgZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoaWQpLnN0eWxlLmRpc3BsYXkgPSAibm9uZSI7IC8v5bCGY2xhc3NOYW1l6K6+572u57uZ6KKr5pON5L2c55qE5Y2V5YWD5qC877yM5pa55L6/5LiL5qyh5pyJ6L+e5bim5pON5L2c5pe25a+55Y2V5YWD5qC86L+b6KGM5aSE55CGCgogICAgICAgICAgICBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChpZCkuY2xhc3NOYW1lID0gY2xpY2tJZDsKICAgICAgICAgIH0KICAgICAgICB9KTsgLy/ph43mlrDorr7nva5tYXDkuK3nmoTlgLwKCiAgICAgICAgdGhpcy50ZE1hcC5zZXQoY2xpY2tJZCwgcHJvY2Vzc0VsZSk7CiAgICAgIH0KICAgIH0sCiAgICAvL+WPlua2iOabtOaUueeahOWQiOW5tuihjOOAgeWIl+aVsAogICAgY2xlYXJDaGFuZ2VUYWJsZTogZnVuY3Rpb24gY2xlYXJDaGFuZ2VUYWJsZSgpIHt9LAogICAgLy/lhbPpl63lvLnnqpcKICAgIGdldEluc3RlckNsb3NlOiBmdW5jdGlvbiBnZXRJbnN0ZXJDbG9zZSgpIHsKICAgICAgdGhpcy5zaG93ID0gZmFsc2U7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICBvYmpJZDogdW5kZWZpbmVkLAogICAgICAgIHJlYWRvbmx5OiB1bmRlZmluZWQsCiAgICAgICAgbnJseDogdW5kZWZpbmVkLAogICAgICAgIG5yYnM6IHVuZGVmaW5lZCwKICAgICAgICB6eG1JZDogdW5kZWZpbmVkLAogICAgICAgIGJ3SWQ6IHVuZGVmaW5lZAogICAgICB9OwogICAgfSwKICAgIC8v6L+b6KGM5ZCI5bm25oiW5ouG5YiG5pON5L2cCiAgICBtZXJnZVRhYmxlOiBmdW5jdGlvbiBtZXJnZVRhYmxlKGhzLCBscywgYWRkaCwgYWRkbCkgewogICAgICBpZiAoaHMgPT09IDEpIHsKICAgICAgICAvL+WQiOW5tuWIlwogICAgICAgIGlmIChscyA+PSAxKSB7CiAgICAgICAgICB0aGlzLm1lcmdlQ2VsbHMoYWRkaCwgYWRkbCwgaHMsIGxzKTsKICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgaWYgKGhzID4gMSkgewogICAgICAgICAgLy/lpJrooYwKICAgICAgICAgIC8v5ZCI5bm26KGMCiAgICAgICAgICBpZiAobHMgPT09IDEpIHsKICAgICAgICAgICAgdGhpcy5tZXJnZVJvd3MoYWRkaCwgYWRkbCwgaHMsIGxzKTsKICAgICAgICAgIH0gZWxzZSBpZiAobHMgPiAxKSB7CiAgICAgICAgICAgIC8v5ZCI5bm25aSa6KGM5aSa5YiXCiAgICAgICAgICAgIHRoaXMubWVyZ2VSb3dzQW5kQ2VsbHMoYWRkaCwgYWRkbCwgaHMsIGxzKTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0gLy/opoHlkIjlubbnmoTljZXlhYPmoLzov5vooYzlkIjlubYKCgogICAgICB0aGlzLmNoYW5nZVRyLnN0eWxlLndpZHRoID0gdGhpcy50ZFdpZHRoICogbHMgKyAiJSI7IC8v6K6+572u5ZCI5bm25ZCO55qE5Y2V5YWD5qC85a695bqmCgogICAgICB0aGlzLmNoYW5nZVRyLnJvd1NwYW4gPSB0aGlzLmFkZGhzOwogICAgICB0aGlzLmNoYW5nZVRyLmNvbFNwYW4gPSB0aGlzLmFkZGxzOwogICAgfSwKCiAgICAvKioNCiAgICAgKiDnrKzkuIDnp43mg4XlhrXvvIzlkIjlubbliJfvvIjkuIDooYzlpJrliJfvvIkNCiAgICAgKiBAcGFyYW0gaCDlvZPliY3lhYPntKDmiYDlnKjooYwNCiAgICAgKiBAcGFyYW0gbCDlvZPliY3lhYPntKDmiYDlnKjliJcNCiAgICAgKiBAcGFyYW0gaHMg6KaB5ZCI5bm255qE6KGM5pWwDQogICAgICogQHBhcmFtIGxzIOimgeWQiOW5tueahOWIl+aVsA0KICAgICAqLwogICAgbWVyZ2VDZWxsczogZnVuY3Rpb24gbWVyZ2VDZWxscyhoLCBsLCBocywgbHMpIHsKICAgICAgdmFyIHJlbW92ZUlkcyA9IFtdOyAvL+imgeWIoOmZpOeahOWFg+e0oOeahGlk5pWw57uECgogICAgICB2YXIgbHNfeGggPSBsczsgLy/opoHlvqrnjq/nmoTliJfmlbAKCiAgICAgIGlmIChscyA+IHRoaXMubHMgLSBsKSB7CiAgICAgICAgLy/kuI3og73otoXov4fliankvZnlj6/mk43kvZznmoTliJfmlbAKICAgICAgICBsc194aCA9IHRoaXMubHMgLSBsOwogICAgICB9CgogICAgICBmb3IgKHZhciBpID0gMTsgaSA8IGxzX3hoOyBpKyspIHsKICAgICAgICByZW1vdmVJZHMucHVzaChoICsgInwiICsgKGwgKyBpKSk7CiAgICAgIH0gLy/liKDpmaTlpJrkvZnljZXlhYPmoLwKCgogICAgICB0aGlzLnByb2Nlc3NUcihyZW1vdmVJZHMpOwogICAgfSwKCiAgICAvKioNCiAgICAgKiDnrKzkuoznp43mg4XlhrXvvIzlkIjlubbooYzvvIjlpJrooYzkuIDliJfvvIkNCiAgICAgKiBAcGFyYW0gaCDlvZPliY3lhYPntKDmiYDlnKjooYwNCiAgICAgKiBAcGFyYW0gbCDlvZPliY3lhYPntKDmiYDlnKjliJcNCiAgICAgKiBAcGFyYW0gaHMg6KaB5ZCI5bm255qE6KGM5pWwDQogICAgICogQHBhcmFtIGxzIOimgeWQiOW5tueahOWIl+aVsA0KICAgICAqLwogICAgbWVyZ2VSb3dzOiBmdW5jdGlvbiBtZXJnZVJvd3MoaCwgbCwgaHMsIGxzKSB7CiAgICAgIHZhciByZW1vdmVJZHMgPSBbXTsgLy/opoHliKDpmaTnmoTlhYPntKDnmoRpZOaVsOe7hAoKICAgICAgdmFyIGhzX3hoID0gaHM7IC8v6KaB5b6q546v55qE6KGM5pWwCgogICAgICBpZiAoaHMgPiB0aGlzLmhzIC0gaCkgewogICAgICAgIC8v5LiN6IO96LaF6L+H5Ymp5L2Z5Y+v5pON5L2c55qE6KGM5pWwCiAgICAgICAgaHNfeGggPSB0aGlzLmhzIC0gaDsKICAgICAgfQoKICAgICAgY29uc29sZS5sb2coImhzX3hoIiwgaHNfeGgpOwoKICAgICAgZm9yICh2YXIgaSA9IDE7IGkgPCBoc194aDsgaSsrKSB7CiAgICAgICAgcmVtb3ZlSWRzLnB1c2goaCArIGkgKyAifCIgKyBsKTsKICAgICAgfSAvL+WIoOmZpOWkmuS9meWNleWFg+agvAoKCiAgICAgIHRoaXMucHJvY2Vzc1RyKHJlbW92ZUlkcyk7CiAgICB9LAoKICAgIC8qKg0KICAgICAqIOesrOS4ieenjeaDheWGte+8jOWQiOW5tuWkmuihjOWkmuWIlw0KICAgICAqIEBwYXJhbSBoIOW9k+WJjeWFg+e0oOaJgOWcqOihjA0KICAgICAqIEBwYXJhbSBsIOW9k+WJjeWFg+e0oOaJgOWcqOWIlw0KICAgICAqIEBwYXJhbSBocyDopoHlkIjlubbnmoTooYzmlbANCiAgICAgKiBAcGFyYW0gbHMg6KaB5ZCI5bm255qE5YiX5pWwDQogICAgICovCiAgICBtZXJnZVJvd3NBbmRDZWxsczogZnVuY3Rpb24gbWVyZ2VSb3dzQW5kQ2VsbHMoaCwgbCwgaHMsIGxzKSB7CiAgICAgIHZhciByZW1vdmVJZHMgPSBbXTsgLy/opoHliKDpmaTnmoTlhYPntKDnmoRpZOaVsOe7hAoKICAgICAgdmFyIHJlbW92ZUlkID0gIiI7IC8v5YWI5b6q546v6KGM77yI5LuO5b2T5YmN6KGM5byA5aeL5b6q546v77yJCgogICAgICBmb3IgKHZhciBqID0gMDsgaiA8IGhzOyBqKyspIHsKICAgICAgICAvL+W+queOr+WIlwogICAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgbHM7IGkrKykgewogICAgICAgICAgLy/ku47lvZPliY3liJflvqrnjq8KICAgICAgICAgIHJlbW92ZUlkID0gaCArIGogKyAifCIgKyAobCArIGkpOyAvL+WwhuW9k+WJjeWNleWFg+agvOaOkumZpOaOiQoKICAgICAgICAgIGlmIChyZW1vdmVJZCAhPT0gaCArICJ8IiArIGwpIHsKICAgICAgICAgICAgcmVtb3ZlSWRzLnB1c2gocmVtb3ZlSWQpOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSAvL+WIoOmZpOWkmuS9meWNleWFg+agvAoKCiAgICAgIHRoaXMucHJvY2Vzc1RyKHJlbW92ZUlkcyk7CiAgICB9LAogICAgLy/mm7TmlrDovpPlhaXmoYbnmoTlgLwKICAgIHVwZGF0ZUlucHV0VmFsdWU6IGZ1bmN0aW9uIHVwZGF0ZUlucHV0VmFsdWUoYXJycykgewogICAgICBmb3IgKHZhciBpID0gMDsgaSA8IGFycnMubGVuZ3RoOyBpKyspIHsKICAgICAgICB2YXIgZWxlID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoYXJyc1tpXSk7CgogICAgICAgIGlmIChlbGUgIT0gbnVsbCAmJiB0eXBlb2YgZWxlICE9ICJ1bmRlZmluZWQiKSB7CiAgICAgICAgICBzd2l0Y2ggKGFycnNbaV0pIHsKICAgICAgICAgICAgY2FzZSAiaHMiOgogICAgICAgICAgICAgIGVsZS52YWx1ZSA9IHRoaXMuaHM7CiAgICAgICAgICAgICAgYnJlYWs7CgogICAgICAgICAgICBjYXNlICJscyI6CiAgICAgICAgICAgICAgZWxlLnZhbHVlID0gdGhpcy5sczsKICAgICAgICAgICAgICBicmVhazsKCiAgICAgICAgICAgIGNhc2UgImFkZGhzIjoKICAgICAgICAgICAgICBlbGUudmFsdWUgPSB0aGlzLmFkZGhzOwogICAgICAgICAgICAgIGJyZWFrOwoKICAgICAgICAgICAgY2FzZSAiYWRkbHMiOgogICAgICAgICAgICAgIGVsZS52YWx1ZSA9IHRoaXMuYWRkbHM7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICB9LAogICAgLy/ph43nva7ljZXlhYPmoLzlhoXlrrkKICAgIHJlc2V0VGFibGU6IGZ1bmN0aW9uIHJlc2V0VGFibGUoKSB7CiAgICAgIHZhciBfdGhpczYgPSB0aGlzOwoKICAgICAgLy/liJ3lp4vljJbpga7nvanlsYIKICAgICAgdGhpcy5sb2FkaW5nID0gX2VsZW1lbnRVaS5Mb2FkaW5nLnNlcnZpY2UoewogICAgICAgIHRleHQ6ICLliqDovb3kuK3vvIzor7fnqI3lkI4uLi4iLAogICAgICAgIGJhY2tncm91bmQ6ICJyZ2JhKDEwOSwxMDYsMTA2LDAuMzUpIgogICAgICB9KTsKICAgICAgdmFyIG9iaklkID0gdGhpcy5jaGFuZ2VUci5pZDsKICAgICAgdmFyIHBhcmFtcyA9IHRoaXMuZ2V0Q2VsbEVsZShvYmpJZCk7CiAgICAgICgwLCBfc3ltcEluZm8ucmVzZXRDZWxscykocGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBpZiAocmVzLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgX3RoaXM2LnVwZGF0ZVRhYmxlKCk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIF90aGlzNi4kbWVzc2FnZS5lcnJvcigi5pON5L2c5aSx6LSlIik7CiAgICAgICAgfQoKICAgICAgICBfdGhpczYubG9hZGluZy5jbG9zZSgpOyAvL+WFs+mXremBrue9qeWxggoKICAgICAgfSk7CiAgICB9LAogICAgLy/ljZXlhYPmoLzlsZ7mgKfnvJbovpHlubbkv53lrZgKICAgIHNhdmVUZFZhbHVlOiBmdW5jdGlvbiBzYXZlVGRWYWx1ZSgpIHsKICAgICAgdGhpcy5zaG93ID0gdHJ1ZTsKICAgICAgdGhpcy5nZXRTeWJ3KCk7CiAgICAgIHRoaXMuZ2V0U3l6eG0oKTsgLy8gICB0aGlzLmZvcm0ucmVhZG9ubHkgPSB0aGlzLmNlbGxEYXRhLnJlYWRvbmx5OwogICAgICAvLyAgIHRoaXMuZm9ybS5ucmx4ID0gdGhpcy5jZWxsRGF0YS5ucmx4OwogICAgICAvLyAgIHRoaXMuZm9ybS5ucmJzID10aGlzLmNlbGxEYXRhLm5yYnM7CiAgICAgIC8vICAgdGhpcy5mb3JtLm9iaklkID10aGlzLmNlbGxEYXRhLm9iaklkOwogICAgICAvL+WIneWni+WMlumBrue9qeWxggogICAgICAvLyB0aGlzLmxvYWRpbmcgPSBMb2FkaW5nLnNlcnZpY2UoewogICAgICAvLyAgIHRleHQ6IuWKoOi9veS4re+8jOivt+eojeWQji4uLiIsCiAgICAgIC8vICAgYmFja2dyb3VuZDoncmdiYSgxMDksMTA2LDEwNiwwLjM1KScsCiAgICAgIC8vIH0pCiAgICAgIC8vIGxldCBvYmpJZCA9IHRoaXMuY2hhbmdlVHIuaWQ7CiAgICAgIC8vIGxldCB2YWwgPSB0aGlzLmNoYW5nZVRyLmdldEVsZW1lbnRzQnlUYWdOYW1lKCJpbnB1dCIpWzBdLnZhbHVlOwogICAgICAvLyBsZXQgcGFyYW1zID0gdGhpcy5nZXRDZWxsRWxlKG9iaklkKTsKICAgICAgLy8gcGFyYW1zLm5yYnMgPSB2YWw7CiAgICAgIC8vIGVkaXRDZWxscyhwYXJhbXMpLnRoZW4ocmVzPT57CiAgICAgIC8vICAgaWYocmVzLmNvZGU9PT0nMDAwMCcpewogICAgICAvLyAgICAgdGhpcy51cGRhdGVUYWJsZSgpOwogICAgICAvLyAgIH1lbHNlewogICAgICAvLyAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5pON5L2c5aSx6LSlJyk7CiAgICAgIC8vICAgICB0aGlzLmxvYWRpbmcgLmNsb3NlKCk7Ly/lhbPpl63pga7nvanlsYIKICAgICAgLy8gICB9CiAgICAgIC8vIH0pCiAgICB9LAogICAgLy/ljZXlhYPmoLzlsZ7mgKfnvJbovpEKICAgIHNhdmVSb3c6IGZ1bmN0aW9uIHNhdmVSb3coKSB7CiAgICAgIHZhciBfdGhpczcgPSB0aGlzOwoKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoIC8qI19fUFVSRV9fKi9yZWdlbmVyYXRvclJ1bnRpbWUubWFyayhmdW5jdGlvbiBfY2FsbGVlKCkgewogICAgICAgIHZhciBfeWllbGQkZWRpdENlbGxzLCBjb2RlOwoKICAgICAgICByZXR1cm4gcmVnZW5lcmF0b3JSdW50aW1lLndyYXAoZnVuY3Rpb24gX2NhbGxlZSQoX2NvbnRleHQpIHsKICAgICAgICAgIHdoaWxlICgxKSB7CiAgICAgICAgICAgIHN3aXRjaCAoX2NvbnRleHQucHJldiA9IF9jb250ZXh0Lm5leHQpIHsKICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICBfY29udGV4dC5wcmV2ID0gMDsKICAgICAgICAgICAgICAgIF90aGlzNy5mb3JtLm9iaklkID0gX3RoaXM3LmNoYW5nZVRyLmlkOwogICAgICAgICAgICAgICAgX3RoaXM3LmZvcm0ubXBpZCA9IF90aGlzNy5jZWxsRGF0YS5tcGlkOwogICAgICAgICAgICAgICAgY29uc29sZS5sb2coIi0tZm9ybS0tIiArIF90aGlzNy5mb3JtKTsKICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSA2OwogICAgICAgICAgICAgICAgcmV0dXJuICgwLCBfc3ltcEluZm8uZWRpdENlbGxzKShfdGhpczcuZm9ybSk7CgogICAgICAgICAgICAgIGNhc2UgNjoKICAgICAgICAgICAgICAgIF95aWVsZCRlZGl0Q2VsbHMgPSBfY29udGV4dC5zZW50OwogICAgICAgICAgICAgICAgY29kZSA9IF95aWVsZCRlZGl0Q2VsbHMuY29kZTsKCiAgICAgICAgICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzNy51cGRhdGVUYWJsZSgpOwoKICAgICAgICAgICAgICAgICAgX3RoaXM3LiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKnyIpOwogICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSAxNDsKICAgICAgICAgICAgICAgIGJyZWFrOwoKICAgICAgICAgICAgICBjYXNlIDExOgogICAgICAgICAgICAgICAgX2NvbnRleHQucHJldiA9IDExOwogICAgICAgICAgICAgICAgX2NvbnRleHQudDAgPSBfY29udGV4dFsiY2F0Y2giXSgwKTsKICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKF9jb250ZXh0LnQwKTsKCiAgICAgICAgICAgICAgY2FzZSAxNDoKICAgICAgICAgICAgICAgIF90aGlzNy5zaG93ID0gZmFsc2U7CgogICAgICAgICAgICAgIGNhc2UgMTU6CiAgICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5zdG9wKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlLCBudWxsLCBbWzAsIDExXV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvL+mHjee9ruWNleWFg+agvOWxnuaAp++8iOWuveW6pu+8jOWQiOW5tuihjOaVsO+8jOWQiOW5tuWIl+aVsO+8iQogICAgcmVzZXRDZWxsOiBmdW5jdGlvbiByZXNldENlbGwoZWxlKSB7CiAgICAgIGlmIChlbGUpIHsKICAgICAgICBlbGUuc3R5bGUud2lkdGggPSB0aGlzLnRkV2lkdGggKyAiJSI7CiAgICAgICAgZWxlLnJvd1NwYW4gPSAiMSI7CiAgICAgICAgZWxlLmNvbFNwYW4gPSAiMSI7CiAgICAgIH0KICAgIH0sCiAgICAvL+i+k+WFpeahhuagoemqjAogICAgY2hlY2tJbnB1dDogZnVuY3Rpb24gY2hlY2tJbnB1dCh2YWwsIGNoYW5nZVR5cGUpIHsKICAgICAgc3dpdGNoIChjaGFuZ2VUeXBlKSB7CiAgICAgICAgY2FzZSAiaHMiOgogICAgICAgICAgdGhpcy5ocyA9IHZhbDsKICAgICAgICAgIGJyZWFrOwoKICAgICAgICBjYXNlICJscyI6CiAgICAgICAgICB0aGlzLmxzID0gdmFsOwogICAgICAgICAgYnJlYWs7CgogICAgICAgIGNhc2UgImFkZGhzIjoKICAgICAgICAgIHRoaXMuYWRkaHMgPSB2YWw7CiAgICAgICAgICBicmVhazsKCiAgICAgICAgY2FzZSAiYWRkbHMiOgogICAgICAgICAgdGhpcy5hZGRscyA9IHZhbDsKICAgICAgICAgIGJyZWFrOwoKICAgICAgICBjYXNlICJucmx4IjoKICAgICAgICAgIHRoaXMubnJseCA9IHZhbDsKICAgICAgICAgIGJyZWFrOwogICAgICB9CiAgICB9LAogICAgLy/ojrflj5bljZXlhYPmoLzmmI7nu4bmlbDmja4KICAgIGdldENlbGxEZXRhaWw6IGZ1bmN0aW9uIGdldENlbGxEZXRhaWwoaHMsIGxzKSB7CiAgICAgIHZhciByZXN1bHQgPSBudWxsOwogICAgICB0aGlzLnRhYmxlRGF0YS5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgaWYgKGl0ZW0ucm93aW5kZXggPT09IGhzLnRvU3RyaW5nKCkgJiYgaXRlbS5jb2xpbmRleCA9PT0gbHMudG9TdHJpbmcoKSkgewogICAgICAgICAgcmVzdWx0ID0gaXRlbTsKCiAgICAgICAgICBpZiAocmVzdWx0Lm5yYnMgPT0gbnVsbCkgewogICAgICAgICAgICByZXN1bHQubnJicyA9ICIiOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICAgIHJldHVybiByZXN1bHQ7CiAgICB9LAogICAgLy/ojrflj5bmn5DkuKrljZXlhYPmoLzlr7nosaEKICAgIGdldENlbGxFbGU6IGZ1bmN0aW9uIGdldENlbGxFbGUob2JqSWQpIHsKICAgICAgdmFyIHJlc3VsdCA9IG51bGw7CiAgICAgIHRoaXMudGFibGVEYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICBpZiAoaXRlbS5vYmpJZCA9PT0gb2JqSWQpIHsKICAgICAgICAgIHJlc3VsdCA9IGl0ZW07CiAgICAgICAgfQogICAgICB9KTsKICAgICAgcmV0dXJuIHJlc3VsdDsKICAgIH0sCiAgICAvL+iOt+WPluacgOaWsOeahOihqOagvOW5tumHjeaWsOa4suafkwogICAgdXBkYXRlVGFibGU6IGZ1bmN0aW9uIHVwZGF0ZVRhYmxlKCkgewogICAgICB2YXIgX3RoaXM4ID0gdGhpczsKCiAgICAgIHZhciBwYXJhbSA9IEpTT04uc3RyaW5naWZ5KHsKICAgICAgICBvYmpfaWQ6IHRoaXMubXBEYXRhLm9iaklkLAogICAgICAgIGxiYnM6ICJBIgogICAgICB9KTsgLy/ojrflj5bmnIDmlrDnmoTooajmoLzmlbDmja4KCiAgICAgICgwLCBfc3ltcGsuZ2V0VGFibGUpKHBhcmFtKS50aGVuKGZ1bmN0aW9uIChyZXMxKSB7CiAgICAgICAgaWYgKHJlczEuY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICBfdGhpczgudGFibGVEYXRhID0gcmVzMS5kYXRhOyAvL+agueaNruacgOaWsOeahOihqOagvOaVsOaNrumHjeaWsOeUuwoKICAgICAgICAgIF90aGlzOC5wcm9jZXNzVGFibGUoKTsKCiAgICAgICAgICBfdGhpczguJG1lc3NhZ2Uuc3VjY2VzcyhyZXMxLm1zZyk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIF90aGlzOC4kbWVzc2FnZS5lcnJvcigi5peg5rOV6I635Y+W5pu05paw5ZCO55qE6KGo5qC85pWw5o2u77yBIik7CiAgICAgICAgfQoKICAgICAgICBfdGhpczgubG9hZGluZy5jbG9zZSgpOyAvL+WFs+mXremBrue9qeWxggoKICAgICAgfSk7CiAgICB9CiAgfQp9OwpleHBvcnRzLmRlZmF1bHQgPSBfZGVmYXVsdDs="}, {"version": 3, "sources": ["zxmInfo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA;;AACA;;AAOA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;AACA,EAAA,UAAA,EAAA;AAAA,IAAA,SAAA,EAAA;AAAA,GADA;AAEA,EAAA,KAAA,EAAA;AACA,IAAA,MAAA,EAAA;AACA,MAAA,IAAA,EAAA;AADA,KADA;AAIA,IAAA,MAAA,EAAA;AACA,MAAA,IAAA,EAAA;AADA;AAJA,GAFA;AAUA,EAAA,IAAA,EAAA,SAVA;AAWA,EAAA,IAXA,kBAWA;AACA,WAAA;AACA;AACA,MAAA,EAAA,EAAA,EAFA;AAGA,MAAA,EAAA,EAAA,EAHA;AAIA;AACA,MAAA,KAAA,EAAA,EALA;AAMA,MAAA,KAAA,EAAA,EANA;AAOA,MAAA,IAAA,EAAA,EAPA;AAQA,MAAA,IAAA,EAAA,EARA;AASA;AACA,MAAA,QAAA,EAAA,EAVA;AAWA;AACA,MAAA,QAAA,EAAA,EAZA;AAaA;AACA,MAAA,MAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,EAAA,EAAA,EAJA;AAKA,QAAA,MAAA,EAAA,EALA;AAMA,QAAA,KAAA,EAAA;AANA,OAdA;AAsBA,MAAA,OAAA,EAAA,IAtBA;AAsBA;AACA,MAAA,OAAA,EAAA,CAvBA;AAuBA;AACA,MAAA,KAAA,EAAA,IAAA,GAAA,EAxBA;AAwBA;AACA,MAAA,SAAA,EAAA,KAAA,MAzBA;AAyBA;AACA,MAAA,MAAA,EAAA,SA1BA;AA0BA;AAEA,MAAA,KAAA,EAAA,SA5BA;AA6BA,MAAA,IAAA,EAAA;AACA,QAAA,KAAA,EAAA,SADA;AAEA,QAAA,QAAA,EAAA,SAFA;AAGA,QAAA,IAAA,EAAA,SAHA;AAIA,QAAA,IAAA,EAAA,SAJA;AAKA,QAAA,KAAA,EAAA,SALA;AAMA,QAAA,IAAA,EAAA;AANA,OA7BA;AAqCA,MAAA,IAAA,EAAA,KArCA;AAsCA,MAAA,MAAA,EAAA,EAtCA;AAsCA;AACA,MAAA,YAAA,EAAA,EAvCA;AAuCA;AACA,MAAA,SAAA,EAAA,EAxCA;AAwCA;AACA,MAAA,KAAA,EAAA,KAzCA;AA0CA,MAAA,OAAA,EAAA,KA1CA;AA2CA,MAAA,QAAA,EAAA,KA3CA;AA4CA,MAAA,QAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,OAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAJA,CA5CA;AAkDA,MAAA,YAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA;AAlDA,KAAA;AAuDA,GAnEA;AAqEA,EAAA,OArEA,qBAqEA;AACA;AACA,SAAA,aAAA;AACA,GAxEA;AAyEA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,aAFA,2BAEA;AACA;AACA,WAAA,OAAA,GAAA,mBAAA,OAAA,CAAA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,UAAA,EAAA;AAFA,OAAA,CAAA;AAIA,WAAA,EAAA,GACA,OAAA,KAAA,MAAA,CAAA,GAAA,IAAA,WAAA,GACA,KAAA,MAAA,CAAA,GADA,GAEA,KAAA,MAAA,CAAA,GAHA;AAIA,WAAA,EAAA,GACA,OAAA,KAAA,MAAA,CAAA,GAAA,IAAA,WAAA,GACA,KAAA,MAAA,CAAA,GADA,GAEA,KAAA,MAAA,CAAA,GAHA;AAIA,WAAA,MAAA,GAAA,KAAA,MAAA,CAAA,MAAA,CAdA,CAeA;;AACA,WAAA,gBAAA,CAAA,CAAA,IAAA,EAAA,IAAA,CAAA;AACA,WAAA,YAAA;AACA,WAAA,OAAA,CAAA,KAAA,GAlBA,CAkBA;AACA,KArBA;AAuBA,IAAA,cAvBA,0BAuBA,GAvBA,EAuBA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,MAAA,GAAA,GAAA,KAAA,SAAA,CAAA,IAAA,CAAA,UAAA,IAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA,KAAA,GAAA;AACA,OAFA,CAAA;AAGA,WAAA,IAAA,CAAA,IAAA,GAAA,GAAA,CAAA,KAAA;AACA,KA7BA;AA+BA,IAAA,aA/BA,yBA+BA,GA/BA,EA+BA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,MAAA,GAAA,GAAA,KAAA,MAAA,CAAA,IAAA,CAAA,UAAA,IAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA,KAAA,GAAA;AACA,OAFA,CAAA;AAGA,WAAA,IAAA,CAAA,IAAA,GAAA,GAAA,CAAA,KAAA;AACA,KArCA;AAuCA,IAAA,WAvCA,uBAuCA,GAvCA,EAuCA;AAAA,UACA,KADA,GACA,GADA,CACA,KADA;AAAA,UACA,KADA,GACA,GADA,CACA,KADA;;AAEA,UAAA,KAAA,IAAA,MAAA,EAAA;AACA,aAAA,KAAA,GAAA,IAAA;AACA,aAAA,OAAA,GAAA,KAAA;AACA,aAAA,QAAA,GAAA,KAAA;AACA,aAAA,IAAA,CAAA,IAAA,GAAA,MAAA;AACA;;AACA,UAAA,KAAA,IAAA,OAAA,EAAA;AACA,aAAA,QAAA,GAAA,IAAA;AACA,aAAA,OAAA,GAAA,KAAA;AACA,aAAA,KAAA,GAAA,KAAA;AACA,aAAA,IAAA,CAAA,IAAA,GAAA,OAAA;AACA;;AACA,UAAA,KAAA,IAAA,MAAA,EAAA;AACA,aAAA,OAAA,GAAA,IAAA;AACA,aAAA,QAAA,GAAA,KAAA;AACA,aAAA,KAAA,GAAA,KAAA;AACA,aAAA,IAAA,CAAA,IAAA,GAAA,MAAA;AACA;;AACA,UAAA,KAAA,IAAA,MAAA,EAAA;AACA,aAAA,KAAA,GAAA,KAAA;AACA,aAAA,OAAA,GAAA,KAAA;AACA,aAAA,QAAA,GAAA,KAAA;AACA,aAAA,IAAA,CAAA,IAAA,GAAA,MAAA;AACA;AACA,KAjEA;AAmEA;AACA,IAAA,OApEA,qBAoEA;AAAA;;AACA,6BAAA;AAAA,QAAA,MAAA,EAAA,KAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,KAAA,CAAA,MAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KAxEA;AA0EA;AACA,IAAA,QA3EA,sBA2EA;AAAA;;AACA,gCAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KA/EA;AAiFA;AACA,IAAA,YAlFA,0BAkFA;AACA,UAAA,KAAA,GAAA,QAAA,CAAA,cAAA,CAAA,YAAA,CAAA;;AACA,UAAA,KAAA,IAAA,IAAA,EAAA;AACA,QAAA,KAAA,CAAA,SAAA,GAAA,EAAA;AACA,YAAA,EAAA,GAAA,KAAA,EAAA;AACA,YAAA,EAAA,GAAA,KAAA,EAAA;AACA,aAAA,OAAA,GAAA,MAAA,MAAA,CAAA,EAAA,CAAA;AACA,YAAA,GAAA,GAAA,EAAA;;AAEA,aAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,EAAA,EAAA,CAAA,EAAA,EAAA;AACA,cAAA,IAAA,GAAA,MAAA;;AACA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,SAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,gBAAA,IAAA,GAAA,KAAA,SAAA,CAAA,CAAA,CAAA;AACA,gBAAA,IAAA,GAAA,IAAA,CAAA,IAAA,CAFA,CAEA;;AACA,gBAAA,IAAA,GAAA,IAAA,CAAA,IAAA;AACA,gBAAA,IAAA,GAAA,IAAA,CAAA,IAAA,IAAA,IAAA,GAAA,IAAA,GAAA,IAAA,GAAA,EAAA,GAAA,IAAA,CAAA,IAAA;;AACA,gBAAA,IAAA,CAAA,QAAA,KAAA,CAAA,CAAA,QAAA,EAAA,EAAA;AACA,cAAA,IAAA,IACA,4BACA,IAAA,CAAA,KADA,GAEA,kBAFA,GAGA,KAAA,OAAA,GAAA,IAAA,CAAA,OAHA,GAIA,cAJA,GAKA,IAAA,CAAA,OALA,GAMA,aANA,GAOA,IAAA,CAAA,OAPA,GAQA,IARA,GASA,IATA,GAUA,OAXA;AAYA;AACA;;AACA,UAAA,IAAA,IAAA,OAAA;AACA,UAAA,GAAA,IAAA,IAAA;AACA;;AACA,QAAA,KAAA,CAAA,SAAA,GAAA,GAAA,CAhCA,CAiCA;;AACA,aAAA,aAAA;AACA;AACA,KAxHA;AA0HA;AACA,IAAA,WA3HA,yBA2HA;AAAA;;AACA;AACA,WAAA,OAAA,GAAA,mBAAA,OAAA,CAAA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,UAAA,EAAA;AAFA,OAAA,CAAA;AAIA,UAAA,MAAA,GAAA,IAAA,CAAA,SAAA,CAAA;AACA,QAAA,KAAA,EAAA,KAAA,MAAA,CAAA,KADA;AACA;AACA,QAAA,GAAA,EAAA,MAAA,CAAA,KAAA,EAAA,CAFA;AAEA;AACA,QAAA,GAAA,EAAA,MAAA,CAAA,KAAA,EAAA,CAHA;AAGA;AACA,QAAA,IAAA,EAAA,GAJA,CAIA;;AAJA,OAAA,CAAA;AAMA,iCAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,WAAA;AACA,SAFA,MAEA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;;AACA,UAAA,MAAA,CAAA,OAAA,CAAA,KAAA,GAFA,CAEA;;AACA;AACA,OAPA;AAQA,KA/IA;AAgJA;AACA,IAAA,aAjJA,2BAiJA;AACA,UAAA,KAAA,GAAA,QAAA,CAAA,sBAAA,CAAA,QAAA,CAAA,CADA,CACA;;AACA,UAAA,QAAA,GAAA,QAAA,CAAA,sBAAA,CAAA,WAAA,CAAA,CAFA,CAEA;;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,UAAA,KAAA,IAAA,IAAA,EAAA;AACA;AACA,aAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,UAAA,KAAA,CAAA,CAAA,CAAA,CAAA,OAAA,GAAA,YAAA;AACA,YAAA,IAAA,CAAA,QAAA,GAAA,IAAA;AACA,YAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,QAAA,CAAA,OAAA;AACA,YAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,QAAA,CAAA,OAAA;AACA,YAAA,IAAA,CAAA,QAAA,GAAA,IAAA,CAAA,UAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA;AACA,YAAA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA,QAAA,CAAA,IAAA;AACA,YAAA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA,QAAA,CAAA,IAAA;AACA,WAPA;AAQA;AACA;;AACA,UAAA,QAAA,IAAA,IAAA,EAAA;AACA;AACA,aAAA,IAAA,EAAA,GAAA,CAAA,EAAA,EAAA,GAAA,QAAA,CAAA,MAAA,EAAA,EAAA,EAAA,EAAA;AACA,UAAA,QAAA,CAAA,EAAA,CAAA,CAAA,OAAA,GAAA,YAAA;AACA,YAAA,IAAA,CAAA,QAAA,GAAA,IAAA;AACA,YAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,QAAA,CAAA,OAAA;AACA,YAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,QAAA,CAAA,OAAA;AACA,YAAA,IAAA,CAAA,QAAA,GAAA,IAAA,CAAA,UAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA,QAAA,CAAA,IAAA;AACA,YAAA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA,QAAA,CAAA,IAAA;AACA,YAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,QAAA;AACA,WARA;AASA;AACA;AACA,KAhLA;AAiLA;AACA,IAAA,eAlLA,6BAkLA;AAAA;;AACA;AACA,WAAA,OAAA,GAAA,mBAAA,OAAA,CAAA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,UAAA,EAAA;AAFA,OAAA,CAAA;AAKA,UAAA,MAAA,GAAA,IAAA,CAAA,SAAA,CAAA;AACA,QAAA,KAAA,EAAA,KAAA,QAAA,CAAA,EADA;AAEA,QAAA,OAAA,EAAA,KAAA,KAFA;AAGA,QAAA,OAAA,EAAA,KAAA;AAHA,OAAA,CAAA,CAPA,CAYA;;AACA,gCAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,WAAA;AACA,SAFA,MAEA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;;AACA,UAAA,MAAA,CAAA,OAAA,CAAA,KAAA,GAFA,CAEA;;AACA;AACA,OAPA;AAQA,KAvMA;AAwMA;AACA,IAAA,SAzMA,qBAyMA,GAzMA,EAyMA;AAAA;;AACA;AACA,UAAA,OAAA,GAAA,KAAA,QAAA,CAAA,EAAA;AACA,UAAA,IAAA,GAAA,EAAA,CAHA,CAGA;AACA;;AACA,UAAA,KAAA,KAAA,CAAA,GAAA,CAAA,OAAA,CAAA,EAAA;AACA,aAAA,KAAA,CAAA,GAAA,CAAA,OAAA,EAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,IAAA,IAAA,EAAA;AACA,YAAA,MAAA,CAAA,SAAA,CAAA,IAAA;;AACA,YAAA,IAAA,CAAA,KAAA,CAAA,OAAA,GAAA,YAAA;AACA;AACA,SALA,EADA,CAOA;;AACA,aAAA,KAAA,CAAA,MAAA,CAAA,OAAA;AACA;;AACA,UAAA,UAAA,GAAA,EAAA,CAfA,CAeA;AAEA;;AACA,UAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA;AACA,QAAA,GAAA,CAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,GAAA,QAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CADA,CAEA;;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA,CAAA,EAAA;AACA,YAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA,EAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,MAAA,CAAA,SAAA,CAAA,IAAA;;AACA,cAAA,IAAA,CAAA,KAAA,CAAA,OAAA,GAAA,YAAA;AACA,aAHA,EADA,CAKA;;;AACA,YAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,GAAA;AACA,WAVA,CAWA;;;AACA,cAAA,GAAA,EAAA;AACA,gBAAA,SAAA,GAAA,GAAA,CAAA,SAAA;;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,SAAA,CAAA,EAAA;AACA,kBAAA,SAAA,GAAA,QAAA,CAAA,cAAA,CAAA,SAAA,CAAA,CADA,CACA;;AACA,kBAAA,SAAA,EAAA;AACA,gBAAA,MAAA,CAAA,SAAA,CAAA,SAAA;AACA;;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,SAAA,EAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA;AACA,oBAAA,CAAA,GAAA,CAAA,QAAA,CAAA,IAAA,CAAA,EAAA;AACA,kBAAA,IAAA,CAAA,KAAA,CAAA,OAAA,GAAA,YAAA;AACA,iBAFA,MAEA;AACA,kBAAA,IAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,eAPA,EALA,CAaA;;;AACA,kBAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,SAAA,EAAA,IAAA;AACA,eAFA,MAEA;AACA;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,SAAA;AACA;AACA;AACA;AACA,SApCA,EAFA,CAuCA;;AACA,QAAA,GAAA,CAAA,OAAA,CAAA,UAAA,EAAA,EAAA;AACA,cAAA,GAAA,GAAA,QAAA,CAAA,cAAA,CAAA,EAAA,CAAA,CADA,CAEA;;AACA,cAAA,GAAA,EAAA;AACA,YAAA,UAAA,CAAA,IAAA,CAAA,GAAA,EADA,CACA;;AAEA,YAAA,QAAA,CAAA,cAAA,CAAA,EAAA,EAAA,KAAA,CAAA,OAAA,GAAA,MAAA,CAHA,CAIA;;AACA,YAAA,QAAA,CAAA,cAAA,CAAA,EAAA,EAAA,SAAA,GAAA,OAAA;AACA;AACA,SAVA,EAxCA,CAmDA;;AACA,aAAA,KAAA,CAAA,GAAA,CAAA,OAAA,EAAA,UAAA;AACA;AACA,KAjRA;AAkRA;AACA,IAAA,gBAnRA,8BAmRA,CAAA,CAnRA;AAqRA;AACA,IAAA,cAtRA,4BAsRA;AACA,WAAA,IAAA,GAAA,KAAA;AACA,WAAA,IAAA,GAAA;AACA,QAAA,KAAA,EAAA,SADA;AAEA,QAAA,QAAA,EAAA,SAFA;AAGA,QAAA,IAAA,EAAA,SAHA;AAIA,QAAA,IAAA,EAAA,SAJA;AAKA,QAAA,KAAA,EAAA,SALA;AAMA,QAAA,IAAA,EAAA;AANA,OAAA;AAQA,KAhSA;AAkSA;AACA,IAAA,UAnSA,sBAmSA,EAnSA,EAmSA,EAnSA,EAmSA,IAnSA,EAmSA,IAnSA,EAmSA;AACA,UAAA,EAAA,KAAA,CAAA,EAAA;AACA;AACA,YAAA,EAAA,IAAA,CAAA,EAAA;AACA,eAAA,UAAA,CAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,EAAA;AACA;AACA,OALA,MAKA;AACA,YAAA,EAAA,GAAA,CAAA,EAAA;AACA;AACA;AACA,cAAA,EAAA,KAAA,CAAA,EAAA;AACA,iBAAA,SAAA,CAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,EAAA;AACA,WAFA,MAEA,IAAA,EAAA,GAAA,CAAA,EAAA;AACA;AACA,iBAAA,iBAAA,CAAA,IAAA,EAAA,IAAA,EAAA,EAAA,EAAA,EAAA;AACA;AACA;AACA,OAjBA,CAkBA;;;AACA,WAAA,QAAA,CAAA,KAAA,CAAA,KAAA,GAAA,KAAA,OAAA,GAAA,EAAA,GAAA,GAAA,CAnBA,CAmBA;;AACA,WAAA,QAAA,CAAA,OAAA,GAAA,KAAA,KAAA;AACA,WAAA,QAAA,CAAA,OAAA,GAAA,KAAA,KAAA;AACA,KAzTA;;AA0TA;;;;;;;AAOA,IAAA,UAjUA,sBAiUA,CAjUA,EAiUA,CAjUA,EAiUA,EAjUA,EAiUA,EAjUA,EAiUA;AACA,UAAA,SAAA,GAAA,EAAA,CADA,CACA;;AACA,UAAA,KAAA,GAAA,EAAA,CAFA,CAEA;;AACA,UAAA,EAAA,GAAA,KAAA,EAAA,GAAA,CAAA,EAAA;AACA;AACA,QAAA,KAAA,GAAA,KAAA,EAAA,GAAA,CAAA;AACA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,EAAA,CAAA,EAAA,EAAA;AACA,QAAA,SAAA,CAAA,IAAA,CAAA,CAAA,GAAA,GAAA,IAAA,CAAA,GAAA,CAAA,CAAA;AACA,OATA,CAUA;;;AACA,WAAA,SAAA,CAAA,SAAA;AACA,KA7UA;;AA8UA;;;;;;;AAOA,IAAA,SArVA,qBAqVA,CArVA,EAqVA,CArVA,EAqVA,EArVA,EAqVA,EArVA,EAqVA;AACA,UAAA,SAAA,GAAA,EAAA,CADA,CACA;;AACA,UAAA,KAAA,GAAA,EAAA,CAFA,CAEA;;AACA,UAAA,EAAA,GAAA,KAAA,EAAA,GAAA,CAAA,EAAA;AACA;AACA,QAAA,KAAA,GAAA,KAAA,EAAA,GAAA,CAAA;AACA;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,KAAA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,EAAA,CAAA,EAAA,EAAA;AACA,QAAA,SAAA,CAAA,IAAA,CAAA,CAAA,GAAA,CAAA,GAAA,GAAA,GAAA,CAAA;AACA,OAVA,CAWA;;;AACA,WAAA,SAAA,CAAA,SAAA;AACA,KAlWA;;AAmWA;;;;;;;AAOA,IAAA,iBA1WA,6BA0WA,CA1WA,EA0WA,CA1WA,EA0WA,EA1WA,EA0WA,EA1WA,EA0WA;AACA,UAAA,SAAA,GAAA,EAAA,CADA,CACA;;AACA,UAAA,QAAA,GAAA,EAAA,CAFA,CAGA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,EAAA,EAAA,CAAA,EAAA,EAAA;AACA;AACA,aAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,EAAA,EAAA,CAAA,EAAA,EAAA;AACA;AACA,UAAA,QAAA,GAAA,CAAA,GAAA,CAAA,GAAA,GAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAFA,CAGA;;AACA,cAAA,QAAA,KAAA,CAAA,GAAA,GAAA,GAAA,CAAA,EAAA;AACA,YAAA,SAAA,CAAA,IAAA,CAAA,QAAA;AACA;AACA;AACA,OAdA,CAeA;;;AACA,WAAA,SAAA,CAAA,SAAA;AACA,KA3XA;AA4XA;AACA,IAAA,gBA7XA,4BA6XA,IA7XA,EA6XA;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,YAAA,GAAA,GAAA,QAAA,CAAA,cAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;;AACA,YAAA,GAAA,IAAA,IAAA,IAAA,OAAA,GAAA,IAAA,WAAA,EAAA;AACA,kBAAA,IAAA,CAAA,CAAA,CAAA;AACA,iBAAA,IAAA;AACA,cAAA,GAAA,CAAA,KAAA,GAAA,KAAA,EAAA;AACA;;AACA,iBAAA,IAAA;AACA,cAAA,GAAA,CAAA,KAAA,GAAA,KAAA,EAAA;AACA;;AACA,iBAAA,OAAA;AACA,cAAA,GAAA,CAAA,KAAA,GAAA,KAAA,KAAA;AACA;;AACA,iBAAA,OAAA;AACA,cAAA,GAAA,CAAA,KAAA,GAAA,KAAA,KAAA;AACA;AAZA;AAcA;AACA;AACA,KAjZA;AAkZA;AACA,IAAA,UAnZA,wBAmZA;AAAA;;AACA;AACA,WAAA,OAAA,GAAA,mBAAA,OAAA,CAAA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,UAAA,EAAA;AAFA,OAAA,CAAA;AAIA,UAAA,KAAA,GAAA,KAAA,QAAA,CAAA,EAAA;AACA,UAAA,MAAA,GAAA,KAAA,UAAA,CAAA,KAAA,CAAA;AACA,gCAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,WAAA;AACA,SAFA,MAEA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;;AACA,QAAA,MAAA,CAAA,OAAA,CAAA,KAAA,GANA,CAMA;;AACA,OAPA;AAQA,KAnaA;AAoaA;AACA,IAAA,WAraA,yBAqaA;AACA,WAAA,IAAA,GAAA,IAAA;AACA,WAAA,OAAA;AACA,WAAA,QAAA,GAHA,CAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KA9bA;AAgcA;AACA,IAAA,OAjcA,qBAicA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,MAAA,CAAA,IAAA,CAAA,KAAA,GAAA,MAAA,CAAA,QAAA,CAAA,EAAA;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,IAAA,GAAA,MAAA,CAAA,QAAA,CAAA,IAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,aAAA,MAAA,CAAA,IAAA;AAJA;AAAA,uBAKA,yBAAA,MAAA,CAAA,IAAA,CALA;;AAAA;AAAA;AAKA,gBAAA,IALA,oBAKA,IALA;;AAMA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,WAAA;;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA;;AATA;AAAA;;AAAA;AAAA;AAAA;AAWA,gBAAA,OAAA,CAAA,GAAA;;AAXA;AAaA,gBAAA,MAAA,CAAA,IAAA,GAAA,KAAA;;AAbA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA,KA/cA;AAidA;AACA,IAAA,SAldA,qBAkdA,GAldA,EAkdA;AACA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,KAAA,CAAA,KAAA,GAAA,KAAA,OAAA,GAAA,GAAA;AACA,QAAA,GAAA,CAAA,OAAA,GAAA,GAAA;AACA,QAAA,GAAA,CAAA,OAAA,GAAA,GAAA;AACA;AACA,KAxdA;AAydA;AACA,IAAA,UA1dA,sBA0dA,GA1dA,EA0dA,UA1dA,EA0dA;AACA,cAAA,UAAA;AACA,aAAA,IAAA;AACA,eAAA,EAAA,GAAA,GAAA;AACA;;AACA,aAAA,IAAA;AACA,eAAA,EAAA,GAAA,GAAA;AACA;;AACA,aAAA,OAAA;AACA,eAAA,KAAA,GAAA,GAAA;AACA;;AACA,aAAA,OAAA;AACA,eAAA,KAAA,GAAA,GAAA;AACA;;AACA,aAAA,MAAA;AACA,eAAA,IAAA,GAAA,GAAA;AACA;AAfA;AAiBA,KA5eA;AA6eA;AACA,IAAA,aA9eA,yBA8eA,EA9eA,EA8eA,EA9eA,EA8eA;AACA,UAAA,MAAA,GAAA,IAAA;AACA,WAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YACA,IAAA,CAAA,QAAA,KAAA,EAAA,CAAA,QAAA,EAAA,IACA,IAAA,CAAA,QAAA,KAAA,EAAA,CAAA,QAAA,EAFA,EAGA;AACA,UAAA,MAAA,GAAA,IAAA;;AACA,cAAA,MAAA,CAAA,IAAA,IAAA,IAAA,EAAA;AACA,YAAA,MAAA,CAAA,IAAA,GAAA,EAAA;AACA;AACA;AACA,OAVA;AAWA,aAAA,MAAA;AACA,KA5fA;AA6fA;AACA,IAAA,UA9fA,sBA8fA,KA9fA,EA8fA;AACA,UAAA,MAAA,GAAA,IAAA;AACA,WAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,KAAA,KAAA,KAAA,EAAA;AACA,UAAA,MAAA,GAAA,IAAA;AACA;AACA,OAJA;AAKA,aAAA,MAAA;AACA,KAtgBA;AAugBA;AACA,IAAA,WAxgBA,yBAwgBA;AAAA;;AACA,UAAA,KAAA,GAAA,IAAA,CAAA,SAAA,CAAA;AACA,QAAA,MAAA,EAAA,KAAA,MAAA,CAAA,KADA;AAEA,QAAA,IAAA,EAAA;AAFA,OAAA,CAAA,CADA,CAKA;;AACA,2BAAA,KAAA,EAAA,IAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,SAAA,GAAA,IAAA,CAAA,IAAA,CADA,CAEA;;AACA,UAAA,MAAA,CAAA,YAAA;;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA;AACA,SALA,MAKA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,eAAA;AACA;;AACA,QAAA,MAAA,CAAA,OAAA,CAAA,KAAA,GATA,CASA;;AACA,OAVA;AAWA;AAzhBA;AAzEA,C", "sourcesContent": ["<template>\r\n  <div class=\"syxmxq_info\">\r\n    <table id=\"mpxq_right\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\"></table>\r\n\r\n    <div id=\"syxmxq_left\">\r\n      <ul class=\"ul2_cont\">\r\n        <li>单元格信息</li>\r\n        <li>\r\n          单元格类型：<el-input\r\n            v-model=\"nrlx\"\r\n            placeholder=\"\"\r\n            class=\"inp1\"\r\n            disabled\r\n            @input=\"val => checkInput(val, 'nrlx')\"\r\n          ></el-input>\r\n        </li>\r\n        <li>\r\n          本次试验数值：<el-input\r\n            v-model=\"text\"\r\n            placeholder=\"\"\r\n            class=\"inp1\"\r\n            disabled\r\n            @input=\"val => checkInput(val, 'text')\"\r\n          ></el-input>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n\r\n    <div id=\"syxmxq_button\" v-show=\"changeTr.id\">\r\n      <dataChart :cell-id=\"changeTr.id\"></dataChart>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { Loading } from \"element-ui\";\r\nimport {\r\n  resetCells,\r\n  createTable,\r\n  mergeCells,\r\n  editCells,\r\n  getCells\r\n} from \"@/api/dagangOilfield/bzgl/sympk/sympInfo\";\r\nimport { getTable } from \"@/api/dagangOilfield/bzgl/sympk/sympk\";\r\nimport { getBwSelect, getZxmSelect } from \"@/api/dagangOilfield/bzgl/syxm\";\r\nimport dataChart from \"@/views/dagangOilfield/bzgl/sybzk/dataChart.vue\";\r\nexport default {\r\n  components: { dataChart },\r\n  props: {\r\n    mpData: {\r\n      type: Object\r\n    },\r\n    mxData: {\r\n      type: Array\r\n    }\r\n  },\r\n  name: \"zxmInfo\",\r\n  data() {\r\n    return {\r\n      //初始表格的行数 列数\r\n      hs: \"\",\r\n      ls: \"\",\r\n      //初始合并行数 列数\r\n      addhs: \"\",\r\n      addls: \"\",\r\n      nrlx: \"\",\r\n      text: \"\",\r\n      //一行的数据\r\n      cellData: \"\",\r\n      //选中合并行、列的tr\r\n      changeTr: \"\",\r\n      //查询条件\r\n      params: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        mpid: \"\",\r\n        zy: \"\",\r\n        sblxbm: \"\",\r\n        zxmId: \"\"\r\n      },\r\n      loading: null, //遮罩层\r\n      tdWidth: 0, //一个单元格所占宽度\r\n      tdMap: new Map(), //用于存放被合并或拆分的单元格（key:当前点击的单元格,value:被处理过的单元格数组）\r\n      tableData: this.mxData, //表格数据\r\n      sblxbm: undefined, //设备类型编码\r\n\r\n      title: \"单元格属性定义\",\r\n      form: {\r\n        objId: undefined,\r\n        readonly: undefined,\r\n        nrlx: undefined,\r\n        bwId: undefined,\r\n        zxmId: undefined,\r\n        nrbs: undefined\r\n      },\r\n      show: false,\r\n      bwList: [], //试验部位\r\n      zxmmDataList: [], //子项目所有结果数据\r\n      zxmmcList: [], //子项目\r\n      hidde: false,\r\n      hiddebw: false,\r\n      hiddezxm: false,\r\n      nrlxList: [\r\n        { label: \"静态文本\", value: \"静态文本\" },\r\n        { label: \"试验子项目\", value: \"试验子项目\" },\r\n        { label: \"试验部位\", value: \"试验部位\" },\r\n        { label: \"试验数据\", value: \"试验数据\" }\r\n      ],\r\n      readonlyList: [\r\n        { label: \"是\", value: \"Y\" },\r\n        { label: \"否\", value: \"N\" }\r\n      ]\r\n    };\r\n  },\r\n\r\n  mounted() {\r\n    //获取表格初始行数和列数\r\n    this.initTableData();\r\n  },\r\n  methods: {\r\n    //获取铭牌内容数据\r\n    initTableData() {\r\n      //初始化遮罩层\r\n      this.loading = Loading.service({\r\n        text: \"加载中，请稍后...\",\r\n        background: \"rgba(109,106,106,0.35)\"\r\n      });\r\n      this.hs =\r\n        typeof this.mpData.AHs != \"undefined\"\r\n          ? this.mpData.AHs\r\n          : this.mpData.aHs;\r\n      this.ls =\r\n        typeof this.mpData.ALs != \"undefined\"\r\n          ? this.mpData.ALs\r\n          : this.mpData.aLs;\r\n      this.sblxbm = this.mpData.sblxbm;\r\n      //更新输入框的值\r\n      this.updateInputValue([\"hs\", \"ls\"]);\r\n      this.processTable();\r\n      this.loading.close(); //关闭遮罩层\r\n    },\r\n\r\n    selectzxmvalue(val) {\r\n      let obj = {};\r\n      obj = this.zxmmcList.find(item => {\r\n        return item.value === val;\r\n      });\r\n      this.form.nrbs = obj.laber;\r\n    },\r\n\r\n    selectbwvalue(val) {\r\n      let obj = {};\r\n      obj = this.bwList.find(item => {\r\n        return item.value === val;\r\n      });\r\n      this.form.nrbs = obj.laber;\r\n    },\r\n\r\n    selectvalue(val) {\r\n      const { value, label } = val;\r\n      if (label == \"静态文本\") {\r\n        this.hidde = true;\r\n        this.hiddebw = false;\r\n        this.hiddezxm = false;\r\n        this.form.nrlx = \"静态文本\";\r\n      }\r\n      if (label == \"试验子项目\") {\r\n        this.hiddezxm = true;\r\n        this.hiddebw = false;\r\n        this.hidde = false;\r\n        this.form.nrlx = \"试验子项目\";\r\n      }\r\n      if (label == \"试验部位\") {\r\n        this.hiddebw = true;\r\n        this.hiddezxm = false;\r\n        this.hidde = false;\r\n        this.form.nrlx = \"试验部位\";\r\n      }\r\n      if (label == \"试验数据\") {\r\n        this.hidde = false;\r\n        this.hiddebw = false;\r\n        this.hiddezxm = false;\r\n        this.form.nrlx = \"试验数据\";\r\n      }\r\n    },\r\n\r\n    //获取部位下拉框\r\n    getSybw() {\r\n      getBwSelect({ sblxbm: this.sblxbm }).then(res => {\r\n        this.bwList = res.data;\r\n      });\r\n    },\r\n\r\n    // 获取试验子项目下拉框数据\r\n    getSyzxm() {\r\n      getZxmSelect().then(res => {\r\n        this.zxmmcList = res.data;\r\n      });\r\n    },\r\n\r\n    //根据行数和列数创建表格\r\n    processTable() {\r\n      var tbody = document.getElementById(\"mpxq_right\");\r\n      if (tbody != null) {\r\n        tbody.innerHTML = \"\";\r\n        let hs = this.hs;\r\n        let ls = this.ls;\r\n        this.tdWidth = 100 / Number(ls);\r\n        let str = \"\";\r\n\r\n        for (let i = 0; i < hs; i++) {\r\n          let temp = \"<tr>\";\r\n          for (let j = 0; j < this.tableData.length; j++) {\r\n            let item = this.tableData[j];\r\n            let sjlx = item.sjlx; //数据类型\r\n            let text = item.text;\r\n            let nrbs = item.nrbs == null ? (text ? text : \"\") : item.nrbs;\r\n            if (item.rowindex === i.toString()) {\r\n              temp +=\r\n                \"<td class='trName' id='\" +\r\n                item.objId +\r\n                \"' style='width: \" +\r\n                this.tdWidth * item.colspan +\r\n                \"%' rowspan='\" +\r\n                item.rowspan +\r\n                \"' colspan='\" +\r\n                item.colspan +\r\n                \"'>\" +\r\n                nrbs +\r\n                \"</td>\";\r\n            }\r\n          }\r\n          temp += \"</tr>\";\r\n          str += temp;\r\n        }\r\n        tbody.innerHTML = str;\r\n        // //给循环出来的单元格加上点击事件\r\n        this.addClickEvent();\r\n      }\r\n    },\r\n\r\n    //手动创建表格\r\n    createTable() {\r\n      //初始化遮罩层\r\n      this.loading = Loading.service({\r\n        text: \"加载中，请稍后...\",\r\n        background: \"rgba(109,106,106,0.35)\"\r\n      });\r\n      let params = JSON.stringify({\r\n        objId: this.mpData.objId, //铭牌id\r\n        aHs: Number(this.hs), //行数\r\n        aLs: Number(this.ls), //列数\r\n        lbbs: \"A\" //类别标识，表示修改的A表格\r\n      });\r\n      createTable(params).then(res => {\r\n        if (res.code === \"0000\") {\r\n          this.updateTable();\r\n        } else {\r\n          this.$message.error(\"操作失败\");\r\n          this.loading.close(); //关闭遮罩层\r\n        }\r\n      });\r\n    },\r\n    //给循环出来的单元格加上点击事件\r\n    addClickEvent() {\r\n      let trArr = document.getElementsByClassName(\"trName\"); //不可编辑的单元格\r\n      let inputArr = document.getElementsByClassName(\"input_cls\"); //可编辑的单元格\r\n      let that = this;\r\n      if (trArr != null) {\r\n        //循环所有的tr\r\n        for (let i = 0; i < trArr.length; i++) {\r\n          trArr[i].onclick = function() {\r\n            that.changeTr = this;\r\n            that.addhs = that.changeTr.rowSpan;\r\n            that.addls = that.changeTr.colSpan;\r\n            that.cellData = that.getCellEle(that.changeTr.id);\r\n            that.nrlx = that.cellData.nrlx;\r\n            that.text = that.cellData.text;\r\n          };\r\n        }\r\n      }\r\n      if (inputArr != null) {\r\n        //循环所有的tr\r\n        for (let i = 0; i < inputArr.length; i++) {\r\n          inputArr[i].onclick = function() {\r\n            that.changeTr = this;\r\n            that.addhs = that.changeTr.rowSpan;\r\n            that.addls = that.changeTr.colSpan;\r\n            that.cellData = that.getCellEle();\r\n            that.nrlx = that.cellData.nrlx;\r\n            that.text = that.cellData.text;\r\n            console.log(that.cellData);\r\n          };\r\n        }\r\n      }\r\n    },\r\n    //合并拆分保存\r\n    saveChangeTable() {\r\n      //初始化遮罩层\r\n      this.loading = Loading.service({\r\n        text: \"加载中，请稍后...\",\r\n        background: \"rgba(109,106,106,0.35)\"\r\n      });\r\n\r\n      let params = JSON.stringify({\r\n        objId: this.changeTr.id,\r\n        rowspan: this.addhs,\r\n        colspan: this.addls\r\n      });\r\n      //先请求接口，如果后台可以执行合并或拆分，则将最新的表格数据请求回来进行前端展示\r\n      mergeCells(params).then(res => {\r\n        if (res.code === \"0000\") {\r\n          this.updateTable();\r\n        } else {\r\n          this.$message.error(\"操作失败\");\r\n          this.loading.close(); //关闭遮罩层\r\n        }\r\n      });\r\n    },\r\n    //处理合并或拆分\r\n    processTr(ids) {\r\n      //点击的单元格id\r\n      let clickId = this.changeTr.id;\r\n      let arr1 = []; //需要重新设置map的数组\r\n      //如果之前已经处理过该单元格,则先将其还原\r\n      if (this.tdMap.has(clickId)) {\r\n        this.tdMap.get(clickId).forEach(item => {\r\n          if (item != null) {\r\n            this.resetCell(item);\r\n            item.style.display = \"table-cell\";\r\n          }\r\n        });\r\n        //操作完后将数据从map中删除\r\n        this.tdMap.delete(clickId);\r\n      }\r\n      let processEle = []; //被处理的元素\r\n\r\n      //现将连带受影响的单元格还原，再进行隐藏处理\r\n      if (ids.length > 0) {\r\n        //执行还原\r\n        ids.forEach(id1 => {\r\n          let ele = document.getElementById(id1);\r\n          //如果此次处理的单元格中有已经被处理过的，先将其还原\r\n          if (this.tdMap.has(id1)) {\r\n            this.tdMap.get(id1).forEach(item => {\r\n              this.resetCell(item);\r\n              item.style.display = \"table-cell\";\r\n            });\r\n            //操作完后将数据从map中删除\r\n            this.tdMap.delete(id1);\r\n          }\r\n          //处理被连带的已经合并过的单元格\r\n          if (ele) {\r\n            let className = ele.className;\r\n            if (this.tdMap.has(className)) {\r\n              let mergeCell = document.getElementById(className); //被连带的已经合并过的cell\r\n              if (mergeCell) {\r\n                this.resetCell(mergeCell);\r\n              }\r\n              this.tdMap.get(className).forEach(item => {\r\n                //需要把此次要隐藏的单元格排除掉，不然隐藏完下次循环又会放出来\r\n                if (!ids.includes(item)) {\r\n                  item.style.display = \"table-cell\";\r\n                } else {\r\n                  arr1.push(item);\r\n                }\r\n              });\r\n              //处理完成后，更新map中的值，将处理过的排除掉\r\n              if (arr1.length > 0) {\r\n                this.tdMap.set(className, arr1);\r\n              } else {\r\n                //操作完后将数据从map中删除\r\n                this.tdMap.delete(className);\r\n              }\r\n            }\r\n          }\r\n        });\r\n        //执行隐藏\r\n        ids.forEach(id => {\r\n          let ele = document.getElementById(id);\r\n          //将多余的单元格隐藏\r\n          if (ele) {\r\n            processEle.push(ele); //添加数据保存到map中\r\n\r\n            document.getElementById(id).style.display = \"none\";\r\n            //将className设置给被操作的单元格，方便下次有连带操作时对单元格进行处理\r\n            document.getElementById(id).className = clickId;\r\n          }\r\n        });\r\n        //重新设置map中的值\r\n        this.tdMap.set(clickId, processEle);\r\n      }\r\n    },\r\n    //取消更改的合并行、列数\r\n    clearChangeTable() {},\r\n\r\n    //关闭弹窗\r\n    getInsterClose() {\r\n      this.show = false;\r\n      this.form = {\r\n        objId: undefined,\r\n        readonly: undefined,\r\n        nrlx: undefined,\r\n        nrbs: undefined,\r\n        zxmId: undefined,\r\n        bwId: undefined\r\n      };\r\n    },\r\n\r\n    //进行合并或拆分操作\r\n    mergeTable(hs, ls, addh, addl) {\r\n      if (hs === 1) {\r\n        //合并列\r\n        if (ls >= 1) {\r\n          this.mergeCells(addh, addl, hs, ls);\r\n        }\r\n      } else {\r\n        if (hs > 1) {\r\n          //多行\r\n          //合并行\r\n          if (ls === 1) {\r\n            this.mergeRows(addh, addl, hs, ls);\r\n          } else if (ls > 1) {\r\n            //合并多行多列\r\n            this.mergeRowsAndCells(addh, addl, hs, ls);\r\n          }\r\n        }\r\n      }\r\n      //要合并的单元格进行合并\r\n      this.changeTr.style.width = this.tdWidth * ls + \"%\"; //设置合并后的单元格宽度\r\n      this.changeTr.rowSpan = this.addhs;\r\n      this.changeTr.colSpan = this.addls;\r\n    },\r\n    /**\r\n     * 第一种情况，合并列（一行多列）\r\n     * @param h 当前元素所在行\r\n     * @param l 当前元素所在列\r\n     * @param hs 要合并的行数\r\n     * @param ls 要合并的列数\r\n     */\r\n    mergeCells(h, l, hs, ls) {\r\n      let removeIds = []; //要删除的元素的id数组\r\n      let ls_xh = ls; //要循环的列数\r\n      if (ls > this.ls - l) {\r\n        //不能超过剩余可操作的列数\r\n        ls_xh = this.ls - l;\r\n      }\r\n      for (let i = 1; i < ls_xh; i++) {\r\n        removeIds.push(h + \"|\" + (l + i));\r\n      }\r\n      //删除多余单元格\r\n      this.processTr(removeIds);\r\n    },\r\n    /**\r\n     * 第二种情况，合并行（多行一列）\r\n     * @param h 当前元素所在行\r\n     * @param l 当前元素所在列\r\n     * @param hs 要合并的行数\r\n     * @param ls 要合并的列数\r\n     */\r\n    mergeRows(h, l, hs, ls) {\r\n      let removeIds = []; //要删除的元素的id数组\r\n      let hs_xh = hs; //要循环的行数\r\n      if (hs > this.hs - h) {\r\n        //不能超过剩余可操作的行数\r\n        hs_xh = this.hs - h;\r\n      }\r\n      console.log(\"hs_xh\", hs_xh);\r\n      for (let i = 1; i < hs_xh; i++) {\r\n        removeIds.push(h + i + \"|\" + l);\r\n      }\r\n      //删除多余单元格\r\n      this.processTr(removeIds);\r\n    },\r\n    /**\r\n     * 第三种情况，合并多行多列\r\n     * @param h 当前元素所在行\r\n     * @param l 当前元素所在列\r\n     * @param hs 要合并的行数\r\n     * @param ls 要合并的列数\r\n     */\r\n    mergeRowsAndCells(h, l, hs, ls) {\r\n      let removeIds = []; //要删除的元素的id数组\r\n      let removeId = \"\";\r\n      //先循环行（从当前行开始循环）\r\n      for (let j = 0; j < hs; j++) {\r\n        //循环列\r\n        for (let i = 0; i < ls; i++) {\r\n          //从当前列循环\r\n          removeId = h + j + \"|\" + (l + i);\r\n          //将当前单元格排除掉\r\n          if (removeId !== h + \"|\" + l) {\r\n            removeIds.push(removeId);\r\n          }\r\n        }\r\n      }\r\n      //删除多余单元格\r\n      this.processTr(removeIds);\r\n    },\r\n    //更新输入框的值\r\n    updateInputValue(arrs) {\r\n      for (let i = 0; i < arrs.length; i++) {\r\n        let ele = document.getElementById(arrs[i]);\r\n        if (ele != null && typeof ele != \"undefined\") {\r\n          switch (arrs[i]) {\r\n            case \"hs\":\r\n              ele.value = this.hs;\r\n              break;\r\n            case \"ls\":\r\n              ele.value = this.ls;\r\n              break;\r\n            case \"addhs\":\r\n              ele.value = this.addhs;\r\n              break;\r\n            case \"addls\":\r\n              ele.value = this.addls;\r\n              break;\r\n          }\r\n        }\r\n      }\r\n    },\r\n    //重置单元格内容\r\n    resetTable() {\r\n      //初始化遮罩层\r\n      this.loading = Loading.service({\r\n        text: \"加载中，请稍后...\",\r\n        background: \"rgba(109,106,106,0.35)\"\r\n      });\r\n      let objId = this.changeTr.id;\r\n      let params = this.getCellEle(objId);\r\n      resetCells(params).then(res => {\r\n        if (res.code === \"0000\") {\r\n          this.updateTable();\r\n        } else {\r\n          this.$message.error(\"操作失败\");\r\n        }\r\n        this.loading.close(); //关闭遮罩层\r\n      });\r\n    },\r\n    //单元格属性编辑并保存\r\n    saveTdValue() {\r\n      this.show = true;\r\n      this.getSybw();\r\n      this.getSyzxm();\r\n      //   this.form.readonly = this.cellData.readonly;\r\n      //   this.form.nrlx = this.cellData.nrlx;\r\n      //   this.form.nrbs =this.cellData.nrbs;\r\n      //   this.form.objId =this.cellData.objId;\r\n      //初始化遮罩层\r\n      // this.loading = Loading.service({\r\n      //   text:\"加载中，请稍后...\",\r\n      //   background:'rgba(109,106,106,0.35)',\r\n      // })\r\n      // let objId = this.changeTr.id;\r\n      // let val = this.changeTr.getElementsByTagName(\"input\")[0].value;\r\n      // let params = this.getCellEle(objId);\r\n      // params.nrbs = val;\r\n      // editCells(params).then(res=>{\r\n      //   if(res.code==='0000'){\r\n      //     this.updateTable();\r\n      //   }else{\r\n      //     this.$message.error('操作失败');\r\n      //     this.loading .close();//关闭遮罩层\r\n      //   }\r\n      // })\r\n    },\r\n\r\n    //单元格属性编辑\r\n    async saveRow() {\r\n      try {\r\n        this.form.objId = this.changeTr.id;\r\n        this.form.mpid = this.cellData.mpid;\r\n        console.log(\"--form--\" + this.form);\r\n        let { code } = await editCells(this.form);\r\n        if (code === \"0000\") {\r\n          this.updateTable();\r\n          this.$message.success(\"操作成功\");\r\n        }\r\n      } catch (e) {\r\n        console.log(e);\r\n      }\r\n      this.show = false;\r\n    },\r\n\r\n    //重置单元格属性（宽度，合并行数，合并列数）\r\n    resetCell(ele) {\r\n      if (ele) {\r\n        ele.style.width = this.tdWidth + \"%\";\r\n        ele.rowSpan = \"1\";\r\n        ele.colSpan = \"1\";\r\n      }\r\n    },\r\n    //输入框校验\r\n    checkInput(val, changeType) {\r\n      switch (changeType) {\r\n        case \"hs\":\r\n          this.hs = val;\r\n          break;\r\n        case \"ls\":\r\n          this.ls = val;\r\n          break;\r\n        case \"addhs\":\r\n          this.addhs = val;\r\n          break;\r\n        case \"addls\":\r\n          this.addls = val;\r\n          break;\r\n        case \"nrlx\":\r\n          this.nrlx = val;\r\n          break;\r\n      }\r\n    },\r\n    //获取单元格明细数据\r\n    getCellDetail(hs, ls) {\r\n      let result = null;\r\n      this.tableData.forEach(item => {\r\n        if (\r\n          item.rowindex === hs.toString() &&\r\n          item.colindex === ls.toString()\r\n        ) {\r\n          result = item;\r\n          if (result.nrbs == null) {\r\n            result.nrbs = \"\";\r\n          }\r\n        }\r\n      });\r\n      return result;\r\n    },\r\n    //获取某个单元格对象\r\n    getCellEle(objId) {\r\n      let result = null;\r\n      this.tableData.forEach(item => {\r\n        if (item.objId === objId) {\r\n          result = item;\r\n        }\r\n      });\r\n      return result;\r\n    },\r\n    //获取最新的表格并重新渲染\r\n    updateTable() {\r\n      let param = JSON.stringify({\r\n        obj_id: this.mpData.objId,\r\n        lbbs: \"A\"\r\n      });\r\n      //获取最新的表格数据\r\n      getTable(param).then(res1 => {\r\n        if (res1.code === \"0000\") {\r\n          this.tableData = res1.data;\r\n          //根据最新的表格数据重新画\r\n          this.processTable();\r\n          this.$message.success(res1.msg);\r\n        } else {\r\n          this.$message.error(\"无法获取更新后的表格数据！\");\r\n        }\r\n        this.loading.close(); //关闭遮罩层\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.syxmxq_info {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n#syxmxq_left {\r\n  margin-left: 20px;\r\n  ul {\r\n    list-style-type: none;\r\n    margin: 0;\r\n    padding: 8px;\r\n  }\r\n  border: 1px solid #0cc283;\r\n  width: 28%;\r\n  li:nth-child(1) {\r\n    font-weight: 700;\r\n  }\r\n  li {\r\n    line-height: 48px;\r\n    padding-left: 8px;\r\n    .el-input {\r\n      width: 70%;\r\n    }\r\n  }\r\n}\r\n#syxmxq_button {\r\n  margin-top: 20px;\r\n  ul {\r\n    list-style-type: none;\r\n    margin: 0;\r\n    padding: 8px;\r\n  }\r\n  border: 1px solid #0cc283;\r\n  width: 99.5%;\r\n}\r\n.change_btn {\r\n  margin-top: 10px !important;\r\n  height: 36px !important;\r\n}\r\n.change_btn:nth-child(1) {\r\n  margin-left: 29%;\r\n}\r\n.change_btn:nth-child(2) {\r\n  margin-left: 20%;\r\n}\r\n#mpxq_right {\r\n  width: 70%;\r\n  height: 180px;\r\n  border: 1px solid #000;\r\n}\r\n</style>\r\n<style>\r\n#mpxq_right td {\r\n  border: 1px solid #000;\r\n  height: 35px;\r\n  line-height: 35px;\r\n  text-align: center;\r\n}\r\n#mpxq_right tr {\r\n  height: 35px;\r\n}\r\n#mpxq_right .atc {\r\n  background-color: #11ba6d;\r\n}\r\n#mpxq_right .input_cls {\r\n  text-align: center;\r\n  border: none;\r\n  width: 99%;\r\n  height: 99%;\r\n}\r\n</style>\r\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk"}]}