{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\gswh_edit.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\gswh_edit.vue", "mtime": 1706897323217}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZm9yLWVhY2giKTsKCnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5pbmNsdWRlcyIpOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm1hcCIpOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmciKTsKCnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYyIpOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pbmNsdWRlcyIpOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pdGVyYXRvciIpOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5yZXBsYWNlIik7CgpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5mb3ItZWFjaCIpOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuaXRlcmF0b3IiKTsKCk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwOwoKcmVxdWlyZSgicmVnZW5lcmF0b3ItcnVudGltZS9ydW50aW1lIik7Cgp2YXIgX2FzeW5jVG9HZW5lcmF0b3IyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvYXN5bmNUb0dlbmVyYXRvciIpKTsKCnZhciBfamJ3aCA9IHJlcXVpcmUoIkAvYXBpL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvc2J6dHBqYnprL2pid2giKTsKCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IHsKICBuYW1lOiAnZ3N3aF9lZGl0JywKICBwcm9wczogewogICAgamI6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICBkZWZhdWx0OiAnJwogICAgfSwKICAgIHRhYmxlRGF0YTogewogICAgICB0eXBlOiBBcnJheSwKICAgICAgZGVmYXVsdDogJycKICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBlZGl0Rm9ybTogewogICAgICAgIGdzOiAnJywKICAgICAgICAvL+WFrOW8jwogICAgICAgIGd6anM6ICcnIC8v6KeE5YiZ6Kej6YeKCgogICAgICB9LAogICAgICBjaGVja0pCOiBmYWxzZSwKICAgICAgLy/mmK/lkKbpqozor4HohJrmnKwKICAgICAgamJWYWw6ICcnLAogICAgICAvL+i/lOWbnue7mee7hOS7tuS4reeahOiEmuacrOWtl+espuS4sgogICAgICBwYXJlbnRNYXA6IG5ldyBNYXAoKQogICAgfTsKICB9LAogIHdhdGNoOiB7CiAgICB0YWJsZURhdGE6IHsKICAgICAgaGFuZGxlcjogZnVuY3Rpb24gaGFuZGxlcihuZXdWYWwsIG9sZFZhbCkgewogICAgICAgIHZhciBfdGhpcyA9IHRoaXM7CgogICAgICAgIG5ld1ZhbC5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgICB2YXIgcm93aW5kZXggPSBwYXJzZUludChpdGVtLnJvd2luZGV4KSArIDE7CiAgICAgICAgICB2YXIgY29saW5kZXggPSBwYXJzZUludChpdGVtLmNvbGluZGV4KSArIDE7CgogICAgICAgICAgX3RoaXMucGFyZW50TWFwLnNldChpdGVtLm9iaklkLCByb3dpbmRleCArICLooYwiICsgY29saW5kZXggKyAi5YiXIik7CiAgICAgICAgfSk7CiAgICAgICAgdGhpcy5qYl9zaG93KCk7CiAgICAgIH0sCiAgICAgIGRlZXA6IHRydWUsCiAgICAgIGltbWVkaWF0ZTogdHJ1ZQogICAgfSwKICAgIGpiOiB7CiAgICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIobmV3VmFsLCBvbGRWYWwpIHsKICAgICAgICB0aGlzLmVkaXRGb3JtLmdzID0gbmV3VmFsOwogICAgICAgIHRoaXMuamJfc2hvdygpOwogICAgICB9LAogICAgICBkZWVwOiB0cnVlLAogICAgICBpbW1lZGlhdGU6IHRydWUKICAgIH0KICB9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7fSwKICBtZXRob2RzOiB7CiAgICAvL+aUvue9ruWFieagh+aVsOaNrgogICAgdHJUYWJsZUNsaWNrOiBmdW5jdGlvbiB0clRhYmxlQ2xpY2soc3RyKSB7CiAgICAgIHZhciBvYmogPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnamJfdGV4dCcpOwoKICAgICAgaWYgKGRvY3VtZW50LnNlbGVjdGlvbikgewogICAgICAgIHZhciBzZWwgPSBkb2N1bWVudC5zZWxlY3Rpb24uY3JlYXRlUmFuZ2UoKTsKICAgICAgICBzZWwudGV4dCA9IHN0cjsKICAgICAgfSBlbHNlIGlmICh0eXBlb2Ygb2JqLnNlbGVjdGlvblN0YXJ0ID09PSAnbnVtYmVyJyAmJiB0eXBlb2Ygb2JqLnNlbGVjdGlvbkVuZCA9PT0gJ251bWJlcicpIHsKICAgICAgICB2YXIgc3RhcnRQb3MgPSBvYmouc2VsZWN0aW9uU3RhcnQ7CiAgICAgICAgdmFyIGVuZFBvcyA9IG9iai5zZWxlY3Rpb25FbmQ7CiAgICAgICAgdmFyIGN1cnNvclBvcyA9IHN0YXJ0UG9zOwogICAgICAgIHZhciB0bXBTdHIgPSBvYmoudmFsdWU7CiAgICAgICAgb2JqLnZhbHVlID0gdG1wU3RyLnN1YnN0cmluZygwLCBzdGFydFBvcykgKyBzdHIgKyB0bXBTdHIuc3Vic3RyaW5nKGVuZFBvcywgdG1wU3RyLmxlbmd0aCk7CiAgICAgICAgdGhpcy5lZGl0Rm9ybS5ncyA9IG9iai52YWx1ZTsgLy/orr7nva7lhazlvI/lgLwKCiAgICAgICAgY3Vyc29yUG9zICs9IHN0ci5sZW5ndGg7CiAgICAgICAgb2JqLnNlbGVjdGlvblN0YXJ0ID0gb2JqLnNlbGVjdGlvbkVuZCA9IGN1cnNvclBvczsKICAgICAgfSBlbHNlIHsKICAgICAgICBvYmoudmFsdWUgKz0gc3RyOwogICAgICAgIHRoaXMuZWRpdEZvcm0uZ3MgPSBvYmoudmFsdWU7IC8v6K6+572u5YWs5byP5YC8CiAgICAgIH0KCiAgICAgIHRoaXMuamJfc2hvdygpOwogICAgfSwKICAgIC8v57uR5a6aYeagh+etvueCueWHu+S6i+S7tgogICAgYnRuQ2xpY2s6IGZ1bmN0aW9uIGJ0bkNsaWNrKHZhbCkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKCiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKCAvKiNfX1BVUkVfXyovcmVnZW5lcmF0b3JSdW50aW1lLm1hcmsoZnVuY3Rpb24gX2NhbGxlZSgpIHsKICAgICAgICByZXR1cm4gcmVnZW5lcmF0b3JSdW50aW1lLndyYXAoZnVuY3Rpb24gX2NhbGxlZSQoX2NvbnRleHQpIHsKICAgICAgICAgIHdoaWxlICgxKSB7CiAgICAgICAgICAgIHN3aXRjaCAoX2NvbnRleHQucHJldiA9IF9jb250ZXh0Lm5leHQpIHsKICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gMjsKICAgICAgICAgICAgICAgIHJldHVybiBfdGhpczIudHJUYWJsZUNsaWNrKHZhbCA9PSAnZWxzZUlmJyA/ICJcbmVsc2UgaWYoKVxue1xuICAgIHJldHVybiAgICA7XG59IiA6IHZhbCk7CgogICAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LnN0b3AoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvL+iEmuacrOe/u+ivkQogICAgamJfc2hvdzogZnVuY3Rpb24gamJfc2hvdygpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CgogICAgICB2YXIgcnVsZVNjcmlwdFN0ciA9IHRoaXMuZWRpdEZvcm0uZ3M7CiAgICAgIHJ1bGVTY3JpcHRTdHIgPSBydWxlU2NyaXB0U3RyLnJlcGxhY2UoL2Vsc2UgaWYvZywgIuWmguaenCIpLnJlcGxhY2UoL2lmL2csICLlpoLmnpwiKS5yZXBsYWNlKC9lbHNlL2csICLlkKbliJkiKS5yZXBsYWNlKC9nZXRQYXJhbWV0ZXIvZywgIuWPguaVsOWAvCIpLnJlcGxhY2UoL2dldENvbFZhbHVlL2csICLlj4LmlbDlgLwiKS5yZXBsYWNlKC9nZXRYeGREYXRhL2csICLkv6Hmga/ngrnlgLwiKS5yZXBsYWNlKC9nZXRaeFh4ZERhdGEvZywgIuWtl+S/oeaBr+eCueWAvCIpLnJlcGxhY2UoLz49L2csICLlpKfkuo7nrYnkuo4iKS5yZXBsYWNlKC8+L2csICLlpKfkuo4iKS5yZXBsYWNlKC88PS9nLCAi5bCP5LqO562J5LqOIikucmVwbGFjZSgvPC9nLCAi5bCP5LqOIikucmVwbGFjZSgvPT0vZywgIuetieS6jiIpLnJlcGxhY2UoLyE9L2csICLkuI3nrYnkuo4iKS5yZXBsYWNlKC9cfFx8L2csICLmiJbogIUiKS5yZXBsYWNlKC8mJi9nLCAi5bm25LiUIikucmVwbGFjZSgvcmV0dXJuL2csICLov5Tlm54iKS5yZXBsYWNlKC9TdWJJdGVtVmFsdWUvZywgIuWNleWFg+agvOWAvCIpLnJlcGxhY2UoL21pbi9nLCAi5aSa5Liq5Y2V5YWD5qC85pyA5bCP5YC8IikucmVwbGFjZSgvbWF4L2csICLlpJrkuKrljZXlhYPmoLzmnIDlpKflgLwiKS5yZXBsYWNlKC9hdmcvZywgIuWkmuS4quWNleWFg+agvOW5s+Wdh+WAvCIpLnJlcGxhY2UoLyhNYXRoLmFicylccypcKC9nLCAi57ud5a+55YC8KCIpOwoKICAgICAgaWYgKHRoaXMucGFyZW50TWFwKSB7CiAgICAgICAgdGhpcy5wYXJlbnRNYXAuZm9yRWFjaChmdW5jdGlvbiAodmFsLCBrZXkpIHsKICAgICAgICAgIGlmIChydWxlU2NyaXB0U3RyLmluY2x1ZGVzKGtleSkpIHsKICAgICAgICAgICAgX3RoaXMzLiQoIiMiICsga2V5KS5jc3MoJ2JhY2tncm91bmRDb2xvcicsICdyZWQnKTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIF90aGlzMy4kKCIjIiArIGtleSkuY3NzKCdiYWNrZ3JvdW5kQ29sb3InLCAnd2hpdGUnKTsKICAgICAgICAgIH0KCiAgICAgICAgICBydWxlU2NyaXB0U3RyID0gcnVsZVNjcmlwdFN0ci5yZXBsYWNlQWxsKGtleSwgdmFsKTsKICAgICAgICB9KTsKICAgICAgfQoKICAgICAgdGhpcy5lZGl0Rm9ybS5nempzID0gcnVsZVNjcmlwdFN0cjsKICAgIH0sCiAgICAvL+iEmuacrOmqjOivgQogICAgY2hlY2tfamI6IGZ1bmN0aW9uIGNoZWNrX2piKCkgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKCiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKCAvKiNfX1BVUkVfXyovcmVnZW5lcmF0b3JSdW50aW1lLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTIoKSB7CiAgICAgICAgdmFyIF95aWVsZCR2ZXJpZnlleHByZXNzaSwgY29kZSwgZGF0YTsKCiAgICAgICAgcmV0dXJuIHJlZ2VuZXJhdG9yUnVudGltZS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUyJChfY29udGV4dDIpIHsKICAgICAgICAgIHdoaWxlICgxKSB7CiAgICAgICAgICAgIHN3aXRjaCAoX2NvbnRleHQyLnByZXYgPSBfY29udGV4dDIubmV4dCkgewogICAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICAgIF90aGlzNC5jaGVja0pCID0gZmFsc2U7CiAgICAgICAgICAgICAgICBfY29udGV4dDIubmV4dCA9IDM7CiAgICAgICAgICAgICAgICByZXR1cm4gKDAsIF9qYndoLnZlcmlmeWV4cHJlc3Npb24pKF90aGlzNC5lZGl0Rm9ybS5ncyk7CgogICAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICAgIF95aWVsZCR2ZXJpZnlleHByZXNzaSA9IF9jb250ZXh0Mi5zZW50OwogICAgICAgICAgICAgICAgY29kZSA9IF95aWVsZCR2ZXJpZnlleHByZXNzaS5jb2RlOwogICAgICAgICAgICAgICAgZGF0YSA9IF95aWVsZCR2ZXJpZnlleHByZXNzaS5kYXRhOwoKICAgICAgICAgICAgICAgIGlmIChjb2RlID09PSAnMDAwMCcpIHsKICAgICAgICAgICAgICAgICAgaWYgKGRhdGEpIHsKICAgICAgICAgICAgICAgICAgICBfdGhpczQuY2hlY2tKQiA9IHRydWU7CgogICAgICAgICAgICAgICAgICAgIF90aGlzNC4kbWVzc2FnZS5zdWNjZXNzKCLohJrmnKzmiafooYzmiJDlip8iKTsKICAgICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICBfdGhpczQuJG1lc3NhZ2UuZXJyb3IoIuiEmuacrOWumuS5iemUmeivryIpOwoKICAgICAgICAgICAgICAgICAgICBfdGhpczQuY2hlY2tKQiA9IGZhbHNlOwogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgIGNhc2UgNzoKICAgICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Mi5zdG9wKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMik7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8v6ISa5pys5L+d5a2YCiAgICBzYXZlX2piOiBmdW5jdGlvbiBzYXZlX2piKCkgewogICAgICBpZiAodGhpcy5jaGVja0pCKSB7CiAgICAgICAgdGhpcy5qYlZhbCA9IHRoaXMuZWRpdEZvcm0uZ3M7CiAgICAgICAgdGhpcy4kZW1pdCgnc2V0SmJWYWwnLCB0aGlzLmpiVmFsKTsgLy/lsIbohJrmnKznmoTlgLzkvKDpgJLnu5nniLbpobXpnaIKCiAgICAgICAgdGhpcy4kZW1pdCgnamJDbG9zZScpOyAvL+WFs+mXreiEmuacrOW8ueahhgogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgICAgIG1lc3NhZ2U6ICfohJrmnKzmsqHmnInpqozor4HmiJbohJrmnKzlrprkuYnplJnor6/vvIzor7fov5vooYzpqozor4HlkI7miJblrprkuYnmraPnoa7ohJrmnKzlnKjkv53lrZjvvIEnCiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0sCiAgICAvL+a4heepuuiEmuacrAogICAgY2xlYXJfamI6IGZ1bmN0aW9uIGNsZWFyX2piKCkgewogICAgICB0aGlzLmNoZWNrSkIgPSBmYWxzZTsKICAgICAgdGhpcy5lZGl0Rm9ybS5ncyA9ICIiOwogICAgICB0aGlzLmpiX3Nob3coKTsKICAgIH0sCiAgICAvL+WIneWni+WMlgogICAgaW5pdF9qYjogZnVuY3Rpb24gaW5pdF9qYigpIHsKICAgICAgdGhpcy5jaGVja0pCID0gZmFsc2U7CiAgICAgIHRoaXMuZWRpdEZvcm0uZ3MgPSAiaWYoKVxue1xuICAgIHJldHVybiAgICAxO1xufVxuZWxzZVxue1xuICAgIHJldHVybiAgICAgMDtcbn0iOwogICAgICB0aGlzLmpiX3Nob3coKTsKICAgIH0KICB9Cn07CmV4cG9ydHMuZGVmYXVsdCA9IF9kZWZhdWx0Ow=="}, {"version": 3, "sources": ["gswh_edit.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,WADA;AAEA,EAAA,KAAA,EAAA;AACA,IAAA,EAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAAA,EAAA;AAFA,KADA;AAKA,IAAA,SAAA,EAAA;AACA,MAAA,IAAA,EAAA,KADA;AAEA,MAAA,OAAA,EAAA;AAFA;AALA,GAFA;AAYA,EAAA,IAZA,kBAYA;AACA,WAAA;AACA,MAAA,QAAA,EAAA;AACA,QAAA,EAAA,EAAA,EADA;AACA;AACA,QAAA,IAAA,EAAA,EAFA,CAEA;;AAFA,OADA;AAKA,MAAA,OAAA,EAAA,KALA;AAKA;AACA,MAAA,KAAA,EAAA,EANA;AAMA;AACA,MAAA,SAAA,EAAA,IAAA,GAAA;AAPA,KAAA;AASA,GAtBA;AAuBA,EAAA,KAAA,EAAA;AACA,IAAA,SAAA,EAAA;AACA,MAAA,OADA,mBACA,MADA,EACA,MADA,EACA;AAAA;;AACA,QAAA,MAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,QAAA,GAAA,QAAA,CAAA,IAAA,CAAA,QAAA,CAAA,GAAA,CAAA;AACA,cAAA,QAAA,GAAA,QAAA,CAAA,IAAA,CAAA,QAAA,CAAA,GAAA,CAAA;;AACA,UAAA,KAAA,CAAA,SAAA,CAAA,GAAA,CAAA,IAAA,CAAA,KAAA,EAAA,QAAA,GAAA,GAAA,GAAA,QAAA,GAAA,GAAA;AACA,SAJA;AAKA,aAAA,OAAA;AACA,OARA;AASA,MAAA,IAAA,EAAA,IATA;AAUA,MAAA,SAAA,EAAA;AAVA,KADA;AAaA,IAAA,EAAA,EAAA;AACA,MAAA,OADA,mBACA,MADA,EACA,MADA,EACA;AACA,aAAA,QAAA,CAAA,EAAA,GAAA,MAAA;AACA,aAAA,OAAA;AACA,OAJA;AAKA,MAAA,IAAA,EAAA,IALA;AAMA,MAAA,SAAA,EAAA;AANA;AAbA,GAvBA;AA6CA,EAAA,OA7CA,qBA6CA,CACA,CA9CA;AA+CA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,YAFA,wBAEA,GAFA,EAEA;AACA,UAAA,GAAA,GAAA,QAAA,CAAA,cAAA,CAAA,SAAA,CAAA;;AACA,UAAA,QAAA,CAAA,SAAA,EAAA;AACA,YAAA,GAAA,GAAA,QAAA,CAAA,SAAA,CAAA,WAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,GAAA,GAAA;AACA,OAHA,MAGA,IAAA,OAAA,GAAA,CAAA,cAAA,KAAA,QAAA,IAAA,OAAA,GAAA,CAAA,YAAA,KAAA,QAAA,EAAA;AACA,YAAA,QAAA,GAAA,GAAA,CAAA,cAAA;AACA,YAAA,MAAA,GAAA,GAAA,CAAA,YAAA;AACA,YAAA,SAAA,GAAA,QAAA;AACA,YAAA,MAAA,GAAA,GAAA,CAAA,KAAA;AACA,QAAA,GAAA,CAAA,KAAA,GAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,QAAA,IAAA,GAAA,GAAA,MAAA,CAAA,SAAA,CAAA,MAAA,EAAA,MAAA,CAAA,MAAA,CAAA;AACA,aAAA,QAAA,CAAA,EAAA,GAAA,GAAA,CAAA,KAAA,CANA,CAMA;;AACA,QAAA,SAAA,IAAA,GAAA,CAAA,MAAA;AACA,QAAA,GAAA,CAAA,cAAA,GAAA,GAAA,CAAA,YAAA,GAAA,SAAA;AACA,OATA,MASA;AACA,QAAA,GAAA,CAAA,KAAA,IAAA,GAAA;AACA,aAAA,QAAA,CAAA,EAAA,GAAA,GAAA,CAAA,KAAA,CAFA,CAEA;AACA;;AACA,WAAA,OAAA;AACA,KArBA;AAsBA;AACA,IAAA,QAvBA,oBAuBA,GAvBA,EAuBA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,MAAA,CAAA,YAAA,CAAA,GAAA,IAAA,QAAA,GAAA,oCAAA,GAAA,GAAA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,KAzBA;AA0BA;AACA,IAAA,OA3BA,qBA2BA;AAAA;;AACA,UAAA,aAAA,GAAA,KAAA,QAAA,CAAA,EAAA;AACA,MAAA,aAAA,GAAA,aAAA,CAAA,OAAA,CAAA,UAAA,EAAA,IAAA,EACA,OADA,CACA,KADA,EACA,IADA,EAEA,OAFA,CAEA,OAFA,EAEA,IAFA,EAGA,OAHA,CAGA,eAHA,EAGA,KAHA,EAIA,OAJA,CAIA,cAJA,EAIA,KAJA,EAKA,OALA,CAKA,aALA,EAKA,MALA,EAMA,OANA,CAMA,eANA,EAMA,OANA,EAOA,OAPA,CAOA,KAPA,EAOA,MAPA,EAQA,OARA,CAQA,IARA,EAQA,IARA,EASA,OATA,CASA,KATA,EASA,MATA,EAUA,OAVA,CAUA,IAVA,EAUA,IAVA,EAWA,OAXA,CAWA,KAXA,EAWA,IAXA,EAYA,OAZA,CAYA,KAZA,EAYA,KAZA,EAaA,OAbA,CAaA,OAbA,EAaA,IAbA,EAcA,OAdA,CAcA,KAdA,EAcA,IAdA,EAeA,OAfA,CAeA,SAfA,EAeA,IAfA,EAgBA,OAhBA,CAgBA,eAhBA,EAgBA,MAhBA,EAiBA,OAjBA,CAiBA,MAjBA,EAiBA,UAjBA,EAkBA,OAlBA,CAkBA,MAlBA,EAkBA,UAlBA,EAmBA,OAnBA,CAmBA,MAnBA,EAmBA,UAnBA,EAoBA,OApBA,CAoBA,kBApBA,EAoBA,MApBA,CAAA;;AAsBA,UAAA,KAAA,SAAA,EAAA;AACA,aAAA,SAAA,CAAA,OAAA,CAAA,UAAA,GAAA,EAAA,GAAA,EAAA;AACA,cAAA,aAAA,CAAA,QAAA,CAAA,GAAA,CAAA,EAAA;AACA,YAAA,MAAA,CAAA,CAAA,CAAA,MAAA,GAAA,EAAA,GAAA,CAAA,iBAAA,EAAA,KAAA;AACA,WAFA,MAGA;AACA,YAAA,MAAA,CAAA,CAAA,CAAA,MAAA,GAAA,EAAA,GAAA,CAAA,iBAAA,EAAA,OAAA;AACA;;AACA,UAAA,aAAA,GAAA,aAAA,CAAA,UAAA,CAAA,GAAA,EAAA,GAAA,CAAA;AACA,SARA;AASA;;AACA,WAAA,QAAA,CAAA,IAAA,GAAA,aAAA;AACA,KA/DA;AAgEA;AACA,IAAA,QAjEA,sBAiEA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AADA;AAAA,uBAEA,4BAAA,MAAA,CAAA,QAAA,CAAA,EAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,yBAEA,IAFA;AAEA,gBAAA,IAFA,yBAEA,IAFA;;AAGA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,IAAA,EAAA;AACA,oBAAA,MAAA,CAAA,OAAA,GAAA,IAAA;;AACA,oBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,QAAA;AACA,mBAHA,MAGA;AACA,oBAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;;AACA,oBAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA;AACA;;AAXA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,KA7EA;AA8EA;AACA,IAAA,OA/EA,qBA+EA;AACA,UAAA,KAAA,OAAA,EAAA;AACA,aAAA,KAAA,GAAA,KAAA,QAAA,CAAA,EAAA;AACA,aAAA,KAAA,CAAA,UAAA,EAAA,KAAA,KAAA,EAFA,CAEA;;AACA,aAAA,KAAA,CAAA,SAAA,EAHA,CAGA;AAEA,OALA,MAKA;AACA,aAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,OADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA;AACA,KA3FA;AA4FA;AACA,IAAA,QA7FA,sBA6FA;AACA,WAAA,OAAA,GAAA,KAAA;AACA,WAAA,QAAA,CAAA,EAAA,GAAA,EAAA;AACA,WAAA,OAAA;AACA,KAjGA;AAkGA;AACA,IAAA,OAnGA,qBAmGA;AACA,WAAA,OAAA,GAAA,KAAA;AACA,WAAA,QAAA,CAAA,EAAA,GAAA,6DAAA;AACA,WAAA,OAAA;AACA;AAvGA;AA/CA,C", "sourcesContent": ["<template>\n  <el-row :gutter=\"16\">\n    <!--  原始公式  -->\n    <el-col :span=\"24\">\n      <div>\n        <el-button type=\"primary\" @click=\"check_jb\">验证</el-button>\n        <el-button type=\"primary\" @click=\"save_jb\">保存</el-button>\n        <!--        <el-button type=\"primary\" @click=\"init_jb\">初始化</el-button>-->\n        <el-button type=\"primary\" @click=\"clear_jb\">清空</el-button>\n      </div>\n    </el-col>\n    <el-col :span=\"24\">\n      <div class=\"row\" style=\"border:1px;\" id=\"atChat\">\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' && ')\">&&</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' || ')\">||</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' > ')\">></a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' >= ')\">>=</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' < ')\"><</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' <= ')\"><=</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' == ')\">==</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' != ')\">!=</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' Math.abs() ')\">abs</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' ( ')\">(</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' ) ')\">)</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' { ')\">{</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' } ')\">}</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick('elseIf')\">else if</a>\n      </div>\n    </el-col>\n\n    <!--  规则解释  -->\n    <el-col :span=\"24\">\n      <el-form :model=\"editForm\" ref=\"editForm\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item prop=\"gs\">\n              <el-input id=\"jb_text\" name=\"pptjms\" type=\"textarea\" :rows=\"10\" v-model=\"editForm.gs\" placeholder=\"请输入公式\"\n                        v-on:input.native=\"jb_show\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <div>\n              <span>规则解释</span>\n            </div>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item prop=\"gzjs\">\n              <el-input id=\"jb_text_show\" name=\"pptjms\" type=\"textarea\" :rows=\"10\" v-model=\"editForm.gzjs\" disabled/>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n    </el-col>\n  </el-row>\n</template>\n\n<script>\nimport {verifyexpression} from '@/api/dagangOilfield/bzgl/sbztpjbzk/jbwh'\n\nexport default {\n  name: 'gswh_edit',\n  props: {\n    jb: {\n      type: String,\n      default: ''\n    },\n    tableData: {\n      type: Array,\n      default: ''\n    },\n  },\n  data() {\n    return {\n      editForm: {\n        gs: '',//公式\n        gzjs: '',//规则解释\n      },\n      checkJB: false,//是否验证脚本\n      jbVal: '',//返回给组件中的脚本字符串\n      parentMap: new Map(),\n    };\n  },\n  watch: {\n    tableData: {\n      handler(newVal, oldVal) {\n        newVal.forEach(item => {\n          let rowindex = parseInt(item.rowindex)+1;\n          let colindex = parseInt(item.colindex)+1;\n          this.parentMap.set(item.objId,rowindex+\"行\"+colindex+\"列\");\n        })\n        this.jb_show();\n      },\n      deep: true,\n      immediate: true\n    },\n    jb: {\n      handler(newVal, oldVal) {\n        this.editForm.gs = newVal;\n        this.jb_show();\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  mounted() {\n  },\n  methods: {\n    //放置光标数据\n    trTableClick(str) {\n      let obj = document.getElementById('jb_text');\n      if (document.selection) {\n        let sel = document.selection.createRange();\n        sel.text = str;\n      } else if (typeof obj.selectionStart === 'number' && typeof obj.selectionEnd === 'number') {\n        let startPos = obj.selectionStart;\n        let endPos = obj.selectionEnd;\n        let cursorPos = startPos;\n        let tmpStr = obj.value;\n        obj.value = tmpStr.substring(0, startPos) + str + tmpStr.substring(endPos, tmpStr.length);\n        this.editForm.gs = obj.value;//设置公式值\n        cursorPos += str.length;\n        obj.selectionStart = obj.selectionEnd = cursorPos;\n      } else {\n        obj.value += str;\n        this.editForm.gs = obj.value;//设置公式值\n      }\n      this.jb_show();\n    },\n    //绑定a标签点击事件\n    async btnClick(val) {\n      await this.trTableClick(val == 'elseIf' ? \"\\nelse if()\\n{\\n    return    ;\\n}\" : val);\n    },\n    //脚本翻译\n    jb_show() {\n      let ruleScriptStr = this.editForm.gs;\n      ruleScriptStr = ruleScriptStr.replace(/else if/g, \"如果\")\n          .replace(/if/g, \"如果\")\n          .replace(/else/g, \"否则\")\n          .replace(/getParameter/g, \"参数值\")\n          .replace(/getColValue/g, \"参数值\")\n          .replace(/getXxdData/g, \"信息点值\")\n          .replace(/getZxXxdData/g, \"字信息点值\")\n          .replace(/>=/g, \"大于等于\")\n          .replace(/>/g, \"大于\")\n          .replace(/<=/g, \"小于等于\")\n          .replace(/</g, \"小于\")\n          .replace(/==/g, \"等于\")\n          .replace(/!=/g, \"不等于\")\n          .replace(/\\|\\|/g, \"或者\")\n          .replace(/&&/g, \"并且\")\n          .replace(/return/g, \"返回\")\n          .replace(/SubItemValue/g, \"单元格值\")\n          .replace(/min/g, \"多个单元格最小值\")\n          .replace(/max/g, \"多个单元格最大值\")\n          .replace(/avg/g, \"多个单元格平均值\")\n          .replace(/(Math.abs)\\s*\\(/g, \"绝对值(\");\n\n      if (this.parentMap) {\n        this.parentMap.forEach((val, key) => {\n          if (ruleScriptStr.includes(key)){\n            this.$(\"#\"+key).css('backgroundColor','red')\n          }\n          else {\n            this.$(\"#\"+key).css('backgroundColor','white')\n          }\n          ruleScriptStr = ruleScriptStr.replaceAll(key, val);\n        })\n      }\n      this.editForm.gzjs = ruleScriptStr;\n    },\n    //脚本验证\n    async check_jb() {\n      this.checkJB = false;\n      let {code, data} = await verifyexpression(this.editForm.gs)\n      if (code === '0000') {\n        if (data) {\n          this.checkJB = true;\n          this.$message.success(\"脚本执行成功\")\n        } else {\n          this.$message.error(\"脚本定义错误\")\n          this.checkJB = false;\n        }\n      }\n    },\n    //脚本保存\n    save_jb() {\n      if (this.checkJB) {\n        this.jbVal = this.editForm.gs;\n        this.$emit('setJbVal', this.jbVal);//将脚本的值传递给父页面\n        this.$emit('jbClose');//关闭脚本弹框\n\n      } else {\n        this.$message({\n          type: 'error',\n          message: '脚本没有验证或脚本定义错误，请进行验证后或定义正确脚本在保存！'\n        })\n      }\n    },\n    //清空脚本\n    clear_jb() {\n      this.checkJB = false;\n      this.editForm.gs = \"\";\n      this.jb_show();\n    },\n    //初始化\n    init_jb() {\n      this.checkJB = false;\n      this.editForm.gs = \"if()\\n{\\n    return    1;\\n}\\nelse\\n{\\n    return     0;\\n}\";\n      this.jb_show();\n    },\n  },\n}\n</script>\n\n<style scoped lang=\"scss\">\n.row {\n  padding-top: 20px;\n  padding-bottom: 20px;\n  font-size: 20px;\n}\n\n.btn {\n  padding: 14px;\n\n  &:hover {\n    color: #00c39a;\n  }\n}\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk"}]}