{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\dwzygl\\sdsbgl\\sdgthd.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\dwzygl\\sdsbgl\\sdgthd.js", "mtime": 1706897314647}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0TGlzdEhkID0gZ2V0TGlzdEhkOwpleHBvcnRzLnNhdmVPclVwZGF0ZUhkID0gc2F2ZU9yVXBkYXRlSGQ7CmV4cG9ydHMucmVtb3ZlSGQgPSByZW1vdmVIZDsKCnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwoKdmFyIGJhc2VVcmwgPSAiL21hbmFnZXItYXBpIjsgLy8g5p+l6K+iCgpmdW5jdGlvbiBnZXRMaXN0SGQocGFyYW1zKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcvZHd6eVNkZ3RoZC9wYWdlJywgcGFyYW1zLCAxKTsKfSAvLyDmt7vliqDmiJbkv67mlLkKCgpmdW5jdGlvbiBzYXZlT3JVcGRhdGVIZChwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy9kd3p5U2RndGhkL3NhdmVPclVwZGF0ZScsIHBhcmFtcywgMSk7Cn0gLy8g5Yig6ZmkCgoKZnVuY3Rpb24gcmVtb3ZlSGQocGFyYW1zKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcvZHd6eVNkZ3RoZC9yZW1vdmUnLCBwYXJhbXMsIDEpOwp9"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagangOilfield/dwzygl/sdsbgl/sdgthd.js"], "names": ["baseUrl", "getListHd", "params", "api", "requestPost", "saveOrUpdateHd", "removeHd"], "mappings": ";;;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,cAAhB,C,CAEA;;AACO,SAASC,SAAT,CAAmBC,MAAnB,EAA2B;AAChC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,kBAAxB,EAA2CE,MAA3C,EAAkD,CAAlD,CAAP;AACD,C,CAED;;;AACO,SAASG,cAAT,CAAwBH,MAAxB,EAAgC;AACrC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,0BAAxB,EAAmDE,MAAnD,EAA0D,CAA1D,CAAP;AACD,C,CACD;;;AACO,SAASI,QAAT,CAAkBJ,MAAlB,EAA0B;AAC/B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,oBAAxB,EAA6CE,MAA7C,EAAoD,CAApD,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\nconst baseUrl = \"/manager-api\";\n\n// 查询\nexport function getListHd(params) {\n  return api.requestPost(baseUrl+'/dwzySdgthd/page',params,1)\n}\n\n// 添加或修改\nexport function saveOrUpdateHd(params) {\n  return api.requestPost(baseUrl+'/dwzySdgthd/saveOrUpdate',params,1)\n}\n// 删除\nexport function removeHd(params) {\n  return api.requestPost(baseUrl+'/dwzySdgthd/remove',params,1)\n}\n\n"]}]}