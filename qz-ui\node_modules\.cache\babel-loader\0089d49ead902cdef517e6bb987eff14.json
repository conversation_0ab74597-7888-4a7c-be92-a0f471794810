{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\syzyxz.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\syzyxz.js", "mtime": 1706897314376}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0UGFnZURhdGFMaXN0ID0gZ2V0UGFnZURhdGFMaXN0OwpleHBvcnRzLnNhdmVPclVwZGF0ZSA9IHNhdmVPclVwZGF0ZTsKZXhwb3J0cy5yZW1vdmUgPSByZW1vdmU7CmV4cG9ydHMuZ2V0VHJlZURhdGEgPSBnZXRUcmVlRGF0YTsKCnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwoKdmFyIGJhc2VVcmwgPSAiL21hbmFnZXItYXBpIjsKLyoqCiAqIOafpeivouaWueazlQogKiBAcGFyYW0gcXVlcnkKICogQHJldHVybnMge1Byb21pc2UgfCBQcm9taXNlPHVua25vd24+fQogKi8KCmZ1bmN0aW9uIGdldFBhZ2VEYXRhTGlzdChxdWVyeSkgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3RQb3N0KGJhc2VVcmwgKyAnL3N5eHovZ2V0U3l4ekRhdGFMaXN0QnlQYWdlJywgcXVlcnksIDIpOwp9Ci8qKgogKiDmt7vliqDmiJbkv67mlLnmlrnms5UKICogQHBhcmFtIHF1ZXJ5CiAqIEByZXR1cm5zIHtQcm9taXNlIHwgUHJvbWlzZTx1bmtub3duPn0KICovCgoKZnVuY3Rpb24gc2F2ZU9yVXBkYXRlKHF1ZXJ5KSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcvc3l4ei9hZGREYXRhJywgcXVlcnksIDIpOwp9Ci8qKioKICog5Yig6ZmkCiAqIEBwYXJhbSBxdWVyeQogKiBAcmV0dXJucyB7UHJvbWlzZSB8IFByb21pc2U8dW5rbm93bj59CiAqLwoKCmZ1bmN0aW9uIHJlbW92ZShxdWVyeSkgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3RQb3N0KGJhc2VVcmwgKyAnL3N5eHovcmVtb3ZlRGF0YScsIHF1ZXJ5LCAyKTsKfQovKioKICog6K+V6aqM5LiT5Lia5qCR5p+l6K+i5pa55rOVCiAqIEBwYXJhbSBwYXJhbXMKICogQHJldHVybnMge1Byb21pc2UgfCBQcm9taXNlPGFueT59CiAqLwoKCmZ1bmN0aW9uIGdldFRyZWVEYXRhKHBhcmFtcykgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3RQb3N0KGJhc2VVcmwgKyAiL3N5eHovZ2V0VHJlZUxpc3QiLCBwYXJhbXMsIDIpOwp9"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagang<PERSON>ilfield/bzgl/syzyxz.js"], "names": ["baseUrl", "getPageDataList", "query", "api", "requestPost", "saveOrUpdate", "remove", "getTreeData", "params"], "mappings": ";;;;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,cAAhB;AAEA;;;;;;AAKO,SAASC,eAAT,CAAyBC,KAAzB,EAA+B;AACpC,SAAQC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,6BAAxB,EAAsDE,KAAtD,EAA4D,CAA5D,CAAR;AACD;AAED;;;;;;;AAKO,SAASG,YAAT,CAAsBH,KAAtB,EAA4B;AACjC,SAAQC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,eAAxB,EAAwCE,KAAxC,EAA8C,CAA9C,CAAR;AACD;AAED;;;;;;;AAKO,SAAUI,MAAV,CAAiBJ,KAAjB,EAAuB;AAC5B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,kBAAxB,EAA2CE,KAA3C,EAAiD,CAAjD,CAAP;AACD;AAED;;;;;;;AAKO,SAASK,WAAT,CAAqBC,MAArB,EAA4B;AACjC,SAAOL,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,mBAAxB,EAA4CQ,MAA5C,EAAmD,CAAnD,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\nconst baseUrl = \"/manager-api\";\n\n/**\n * 查询方法\n * @param query\n * @returns {Promise | Promise<unknown>}\n */\nexport function getPageDataList(query){\n  return  api.requestPost(baseUrl+'/syxz/getSyxzDataListByPage',query,2);\n}\n\n/**\n * 添加或修改方法\n * @param query\n * @returns {Promise | Promise<unknown>}\n */\nexport function saveOrUpdate(query){\n  return  api.requestPost(baseUrl+'/syxz/addData',query,2);\n}\n\n/***\n * 删除\n * @param query\n * @returns {Promise | Promise<unknown>}\n */\nexport function  remove(query){\n  return api.requestPost(baseUrl+'/syxz/removeData',query,2)\n}\n\n/**\n * 试验专业树查询方法\n * @param params\n * @returns {Promise | Promise<any>}\n */\nexport function getTreeData(params){\n  return api.requestPost(baseUrl+\"/syxz/getTreeList\",params,2);\n}\n"]}]}