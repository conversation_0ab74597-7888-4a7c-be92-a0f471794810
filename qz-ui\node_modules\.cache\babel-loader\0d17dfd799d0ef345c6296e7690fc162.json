{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\store\\modules\\todoList.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\store\\modules\\todoList.js", "mtime": 1755545325941}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/store/modules/todoList.js"], "names": ["state", "activeName", "filterInfo", "module", "todoUserName", "handleUserName", "applyUserName", "pagination", "pageSize", "pageNum", "scrollPosition", "tableData", "total", "needRefresh", "mutations", "SET_ACTIVE_NAME", "SET_FILTER_INFO", "SET_PAGINATION", "SET_SCROLL_POSITION", "position", "SET_TABLE_DATA", "SET_TOTAL", "SET_NEED_REFRESH", "RESET_STATE", "actions", "savePageState", "commit", "undefined", "saveFilterInfo", "savePagination", "saveScrollPosition", "saveTableData", "resetState", "mark<PERSON>eedRefresh", "clearRefreshFlag", "namespaced"], "mappings": ";;;;;;;;;;;AAAA,IAAMA,KAAK,GAAG;AACZC,EAAAA,UAAU,EAAE,IADA;AACM;AAClBC,EAAAA,UAAU,EAAE;AACVC,IAAAA,MAAM,EAAE,EADE;AAEVC,IAAAA,YAAY,EAAE,EAFJ;AAGVC,IAAAA,cAAc,EAAE,EAHN;AAIVC,IAAAA,aAAa,EAAE;AAJL,GAFA;AAOT;AACHC,EAAAA,UAAU,EAAE;AACVC,IAAAA,QAAQ,EAAE,EADA;AAEVC,IAAAA,OAAO,EAAE;AAFC,GARA;AAYZC,EAAAA,cAAc,EAAE,CAZJ;AAYO;AACnBC,EAAAA,SAAS,EAAE,EAbC;AAaG;AACfC,EAAAA,KAAK,EAAE,CAdK;AAcF;AACVC,EAAAA,WAAW,EAAE,KAfD,CAeO;;AAfP,CAAd;AAkBA,IAAMC,SAAS,GAAG;AAChBC,EAAAA,eAAe,EAAE,yBAACf,KAAD,EAAQC,UAAR,EAAuB;AACtCD,IAAAA,KAAK,CAACC,UAAN,GAAmBA,UAAnB;AACD,GAHe;AAIhBe,EAAAA,eAAe,EAAE,yBAAChB,KAAD,EAAQE,UAAR,EAAuB;AACtCF,IAAAA,KAAK,CAACE,UAAN,+DAAwBF,KAAK,CAACE,UAA9B,GAA6CA,UAA7C;AACD,GANe;AAOhBe,EAAAA,cAAc,EAAE,wBAACjB,KAAD,EAAQO,UAAR,EAAuB;AACrCP,IAAAA,KAAK,CAACO,UAAN,+DAAwBP,KAAK,CAACO,UAA9B,GAA6CA,UAA7C;AACD,GATe;AAUhBW,EAAAA,mBAAmB,EAAE,6BAAClB,KAAD,EAAQmB,QAAR,EAAqB;AACxCnB,IAAAA,KAAK,CAACU,cAAN,GAAuBS,QAAvB;AACD,GAZe;AAahBC,EAAAA,cAAc,EAAE,wBAACpB,KAAD,EAAQW,SAAR,EAAsB;AACpCX,IAAAA,KAAK,CAACW,SAAN,GAAkBA,SAAlB;AACD,GAfe;AAgBhBU,EAAAA,SAAS,EAAE,mBAACrB,KAAD,EAAQY,KAAR,EAAkB;AAC3BZ,IAAAA,KAAK,CAACY,KAAN,GAAcA,KAAd;AACD,GAlBe;AAmBhBU,EAAAA,gBAAgB,EAAE,0BAACtB,KAAD,EAAQa,WAAR,EAAwB;AACxCb,IAAAA,KAAK,CAACa,WAAN,GAAoBA,WAApB;AACD,GArBe;AAsBhB;AACAU,EAAAA,WAAW,EAAE,qBAACvB,KAAD,EAAW;AACtBA,IAAAA,KAAK,CAACC,UAAN,GAAmB,IAAnB;AACAD,IAAAA,KAAK,CAACE,UAAN,GAAmB;AACjBC,MAAAA,MAAM,EAAE,EADS;AAEjBC,MAAAA,YAAY,EAAE,EAFG;AAGjBC,MAAAA,cAAc,EAAE,EAHC;AAIjBC,MAAAA,aAAa,EAAE;AAJE,KAAnB;AAMAN,IAAAA,KAAK,CAACO,UAAN,GAAmB;AACjBC,MAAAA,QAAQ,EAAE,EADO;AAEjBC,MAAAA,OAAO,EAAE;AAFQ,KAAnB;AAIAT,IAAAA,KAAK,CAACU,cAAN,GAAuB,CAAvB;AACAV,IAAAA,KAAK,CAACW,SAAN,GAAkB,EAAlB;AACAX,IAAAA,KAAK,CAACY,KAAN,GAAc,CAAd;AACAZ,IAAAA,KAAK,CAACa,WAAN,GAAoB,KAApB;AACD;AAvCe,CAAlB;AA0CA,IAAMW,OAAO,GAAG;AACd;AACAC,EAAAA,aAFc,sCAEsF;AAAA,QAApFC,MAAoF,QAApFA,MAAoF;AAAA,QAAxEzB,UAAwE,SAAxEA,UAAwE;AAAA,QAA5DC,UAA4D,SAA5DA,UAA4D;AAAA,QAAhDK,UAAgD,SAAhDA,UAAgD;AAAA,QAApCG,cAAoC,SAApCA,cAAoC;AAAA,QAApBC,SAAoB,SAApBA,SAAoB;AAAA,QAATC,KAAS,SAATA,KAAS;AAClG,QAAIX,UAAU,KAAK0B,SAAnB,EAA8BD,MAAM,CAAC,iBAAD,EAAoBzB,UAApB,CAAN;AAC9B,QAAIC,UAAU,KAAKyB,SAAnB,EAA8BD,MAAM,CAAC,iBAAD,EAAoBxB,UAApB,CAAN;AAC9B,QAAIK,UAAU,KAAKoB,SAAnB,EAA8BD,MAAM,CAAC,gBAAD,EAAmBnB,UAAnB,CAAN;AAC9B,QAAIG,cAAc,KAAKiB,SAAvB,EAAkCD,MAAM,CAAC,qBAAD,EAAwBhB,cAAxB,CAAN;AAClC,QAAIC,SAAS,KAAKgB,SAAlB,EAA6BD,MAAM,CAAC,gBAAD,EAAmBf,SAAnB,CAAN;AAC7B,QAAIC,KAAK,KAAKe,SAAd,EAAyBD,MAAM,CAAC,WAAD,EAAcd,KAAd,CAAN;AAC1B,GATa;AAWd;AACAgB,EAAAA,cAZc,iCAYa1B,UAZb,EAYyB;AAAA,QAAtBwB,MAAsB,SAAtBA,MAAsB;AACrCA,IAAAA,MAAM,CAAC,iBAAD,EAAoBxB,UAApB,CAAN;AACD,GAda;AAgBd;AACA2B,EAAAA,cAjBc,iCAiBatB,UAjBb,EAiByB;AAAA,QAAtBmB,MAAsB,SAAtBA,MAAsB;AACrCA,IAAAA,MAAM,CAAC,gBAAD,EAAmBnB,UAAnB,CAAN;AACD,GAnBa;AAqBd;AACAuB,EAAAA,kBAtBc,qCAsBiBX,QAtBjB,EAsB2B;AAAA,QAApBO,MAAoB,SAApBA,MAAoB;AACvCA,IAAAA,MAAM,CAAC,qBAAD,EAAwBP,QAAxB,CAAN;AACD,GAxBa;AA0Bd;AACAY,EAAAA,aA3Bc,uCA2BkC;AAAA,QAAhCL,MAAgC,SAAhCA,MAAgC;AAAA,QAApBf,SAAoB,SAApBA,SAAoB;AAAA,QAATC,KAAS,SAATA,KAAS;AAC9Cc,IAAAA,MAAM,CAAC,gBAAD,EAAmBf,SAAnB,CAAN;AACAe,IAAAA,MAAM,CAAC,WAAD,EAAcd,KAAd,CAAN;AACD,GA9Ba;AAgCd;AACAoB,EAAAA,UAjCc,6BAiCS;AAAA,QAAVN,MAAU,SAAVA,MAAU;AACrBA,IAAAA,MAAM,CAAC,aAAD,CAAN;AACD,GAnCa;AAqCd;AACAO,EAAAA,eAtCc,kCAsCc;AAAA,QAAVP,MAAU,SAAVA,MAAU;AAC1BA,IAAAA,MAAM,CAAC,kBAAD,EAAqB,IAArB,CAAN;AACD,GAxCa;AA0Cd;AACAQ,EAAAA,gBA3Cc,oCA2Ce;AAAA,QAAVR,MAAU,UAAVA,MAAU;AAC3BA,IAAAA,MAAM,CAAC,kBAAD,EAAqB,KAArB,CAAN;AACD;AA7Ca,CAAhB;eAgDe;AACbS,EAAAA,UAAU,EAAE,IADC;AAEbnC,EAAAA,KAAK,EAALA,KAFa;AAGbc,EAAAA,SAAS,EAATA,SAHa;AAIbU,EAAAA,OAAO,EAAPA;AAJa,C", "sourcesContent": ["const state = {\n  activeName: 'db', // 当前选中的标签页\n  filterInfo: {\n    module: \"\",\n    todoUserName: \"\",\n    handleUserName: \"\",\n    applyUserName: \"\"\n  }, // 筛选条件\n  pagination: {\n    pageSize: 10,\n    pageNum: 1\n  },\n  scrollPosition: 0, // 滚动位置\n  tableData: [], // 表格数据缓存（仅用于性能优化，不用于显示）\n  total: 0, // 总数缓存\n  needRefresh: false // 是否需要刷新数据\n}\n\nconst mutations = {\n  SET_ACTIVE_NAME: (state, activeName) => {\n    state.activeName = activeName\n  },\n  SET_FILTER_INFO: (state, filterInfo) => {\n    state.filterInfo = { ...state.filterInfo, ...filterInfo }\n  },\n  SET_PAGINATION: (state, pagination) => {\n    state.pagination = { ...state.pagination, ...pagination }\n  },\n  SET_SCROLL_POSITION: (state, position) => {\n    state.scrollPosition = position\n  },\n  SET_TABLE_DATA: (state, tableData) => {\n    state.tableData = tableData\n  },\n  SET_TOTAL: (state, total) => {\n    state.total = total\n  },\n  SET_NEED_REFRESH: (state, needRefresh) => {\n    state.needRefresh = needRefresh\n  },\n  // 重置所有状态\n  RESET_STATE: (state) => {\n    state.activeName = 'db'\n    state.filterInfo = {\n      module: \"\",\n      todoUserName: \"\",\n      handleUserName: \"\",\n      applyUserName: \"\"\n    }\n    state.pagination = {\n      pageSize: 10,\n      pageNum: 1\n    }\n    state.scrollPosition = 0\n    state.tableData = []\n    state.total = 0\n    state.needRefresh = false\n  }\n}\n\nconst actions = {\n  // 保存整个页面状态\n  savePageState({ commit }, { activeName, filterInfo, pagination, scrollPosition, tableData, total }) {\n    if (activeName !== undefined) commit('SET_ACTIVE_NAME', activeName)\n    if (filterInfo !== undefined) commit('SET_FILTER_INFO', filterInfo)\n    if (pagination !== undefined) commit('SET_PAGINATION', pagination)\n    if (scrollPosition !== undefined) commit('SET_SCROLL_POSITION', scrollPosition)\n    if (tableData !== undefined) commit('SET_TABLE_DATA', tableData)\n    if (total !== undefined) commit('SET_TOTAL', total)\n  },\n\n  // 保存筛选条件\n  saveFilterInfo({ commit }, filterInfo) {\n    commit('SET_FILTER_INFO', filterInfo)\n  },\n\n  // 保存分页信息\n  savePagination({ commit }, pagination) {\n    commit('SET_PAGINATION', pagination)\n  },\n\n  // 保存滚动位置\n  saveScrollPosition({ commit }, position) {\n    commit('SET_SCROLL_POSITION', position)\n  },\n\n  // 保存表格数据\n  saveTableData({ commit }, { tableData, total }) {\n    commit('SET_TABLE_DATA', tableData)\n    commit('SET_TOTAL', total)\n  },\n\n  // 重置状态\n  resetState({ commit }) {\n    commit('RESET_STATE')\n  },\n\n  // 标记需要刷新数据\n  markNeedRefresh({ commit }) {\n    commit('SET_NEED_REFRESH', true)\n  },\n\n  // 清除刷新标记\n  clearRefreshFlag({ commit }) {\n    commit('SET_NEED_REFRESH', false)\n  }\n}\n\nexport default {\n  namespaced: true,\n  state,\n  mutations,\n  actions\n}"]}]}