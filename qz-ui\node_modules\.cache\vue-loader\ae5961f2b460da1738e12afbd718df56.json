{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\pdgl\\pdsbgl.vue?vue&type=template&id=e589811a&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\pdgl\\pdsbgl.vue", "mtime": 1706897324785}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}