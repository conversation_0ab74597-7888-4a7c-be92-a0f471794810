{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\mixins\\deviceClassifyMixin.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\mixins\\deviceClassifyMixin.js", "mtime": 1727416529399}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/mixins/deviceClassifyMixin.js"], "names": ["methods", "fetchDeviceClassifyData", "params", "res", "data", "console", "error", "$message", "fetchTreeNodeData", "map", "item", "name", "level", "id", "pid", "leaf", "jscsNum", "sbbjNum", "code"], "mappings": ";;;;;;;;;;;;;;;;;AAAA;;eAEe;AACbA,EAAAA,OAAO,EAAE;AACDC,IAAAA,uBADC,mCACuBC,MADvB,EAC+B;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAEhB,wCAA2BA,MAA3B,CAFgB;;AAAA;AAE5BC,gBAAAA,GAF4B;AAAA,iDAG3BA,GAAG,CAACC,IAHuB;;AAAA;AAAA;AAAA;AAKlCC,gBAAAA,OAAO,CAACC,KAAR,CAAc,aAAd;;AACA,gBAAA,KAAI,CAACC,QAAL,CAAcD,KAAd,CAAoB,cAApB;;AANkC,iDAO3B,IAP2B;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASrC,KAVM;AAYDE,IAAAA,iBAZC,+BAY8B;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAbN,gBAAAA,MAAa,0EAAJ,EAAI;AAAA;AAAA;AAAA,uBAEf,yCAA4BA,MAA5B,CAFe;;AAAA;AAE3BC,gBAAAA,GAF2B;AAAA,kDAG1BA,GAAG,CAACC,IAAJ,CAASK,GAAT,CAAa,UAAAC,IAAI;AAAA,yBAAK;AAC3BC,oBAAAA,IAAI,EAAED,IAAI,CAACC,IADgB;AAE3BC,oBAAAA,KAAK,EAAEF,IAAI,CAACE,KAFe;AAG3BC,oBAAAA,EAAE,EAAEH,IAAI,CAACG,EAHkB;AAI3BC,oBAAAA,GAAG,EAAEJ,IAAI,CAACI,GAJiB;AAK3BC,oBAAAA,IAAI,EAAEL,IAAI,CAACM,OAAL,GAAe,CAAf,IAAoBN,IAAI,CAACO,OAAL,GAAe,CALd;AAM3BD,oBAAAA,OAAO,EAAEN,IAAI,CAACM,OANa;AAO3BC,oBAAAA,OAAO,EAAEP,IAAI,CAACO,OAPa;AAQ3BC,oBAAAA,IAAI,EAAER,IAAI,CAACQ;AARgB,mBAAL;AAAA,iBAAjB,CAH0B;;AAAA;AAAA;AAAA;AAcjCb,gBAAAA,OAAO,CAACC,KAAR,CAAc,YAAd;;AACA,gBAAA,MAAI,CAACC,QAAL,CAAcD,KAAd,CAAoB,iBAApB;;AAfiC,kDAgB1B,EAhB0B;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBpC;AA9BM;AADI,C", "sourcesContent": ["import { getDeviceClassifyDataByPid, getDeviceClassTreeNodeByPid } from '@/api/dagangOilfield/bzgl/sblxwh/sblxwh'\r\n\r\nexport default {\r\n  methods: {\r\n    async fetchDeviceClassifyData(params) {\r\n      try {\r\n        const res = await getDeviceClassifyDataByPid(params)\r\n        return res.data\r\n      } catch (error) {\r\n        console.error('获取设备分类数据失败:', error)\r\n        this.$message.error('获取数据失败，请稍后重试')\r\n        return null\r\n      }\r\n    },\r\n\r\n    async fetchTreeNodeData(params = {}) {\r\n      try {\r\n        const res = await getDeviceClassTreeNodeByPid(params)\r\n        return res.data.map(item => ({\r\n          name: item.name,\r\n          level: item.level,\r\n          id: item.id,\r\n          pid: item.pid,\r\n          leaf: item.jscsNum > 0 || item.sbbjNum > 0,\r\n          jscsNum: item.jscsNum,\r\n          sbbjNum: item.sbbjNum,\r\n          code: item.code\r\n        }))\r\n      } catch (error) {\r\n        console.error('获取树节点数据失败:', error)\r\n        this.$message.error('获取树节点数据失败，请稍后重试')\r\n        return []\r\n      }\r\n    }\r\n  }\r\n}"]}]}