{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\syxmzq.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\syxmzq.vue", "mtime": 1706897323691}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgZ2V0UGFnZURhdGFMaXN0LAogIGdldFN5enlPcHRpb25zU2VsZWN0ZWQsCiAgZ2V0R2xTeXp4bURhdGFMaXN0QnlQYWdlLAogIGdldFN5enhtTGlicmFyeURhdGFMaXN0QnlQYWdlLAogIGFkZEJhdGNoU3l6eG1Ub1htenhtLAogIHJlbW92ZSwKICBzYXZlT3JVcGRhdGUsCiAgZ2V0UGFnZUt4ekRhdGFMaXN0LAogIHJlbW92ZUt4ekRhdGEsCiAgc2F2ZU9yVXBkYXRlS3h6RGF0YSwKICBnZXRTYmx4VHJlZQp9IGZyb20gJ0AvYXBpL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvc3l4bScKCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogImxwYnprIiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy/or5XpqozlrZDpobnnm67lupPmlbDmja4KICAgICAgenhtTGlicmFyeURhdGFMaXN0OiBbXSwKICAgICAgLy/or5XpqozkuJPkuJrkuIvmi4nmoYbmlbDmja4KICAgICAgc3l6eU9wdGlvbnNTZWxlY3RlZExpc3Q6IFtdLAogICAgICAvL+aemuS4vuWAvOaWsOWinuW8ueWHuuahhuW6lemDqOaMiemSruaOp+WItuaYvuekugogICAgICBhZGRNanpEaWFsb2dCdXR0b25TaG93OiB0cnVlLAogICAgICAvL+aOp+WItuaemuS4vuWAvOaWsOWinuW8ueWHuuahhuWGheWuueaYr+WQpuWPr+e8lui+kQogICAgICBtanpBZGREaWFsb2dEaXNhYmxlZDogZmFsc2UsCiAgICAgIC8v5p6a5Li+5YC85paw5aKeZm9ybeihqOWNlQogICAgICBtanpBZGRGb3JtOiB7CiAgICAgICAgc3l6eG1pZDogdW5kZWZpbmVkLAogICAgICAgIGt4ejogdW5kZWZpbmVkCiAgICAgIH0sCiAgICAgIC8v5p6a5Li+5YC85paw5aKe5by55Ye65qGG5qCH6aKYCiAgICAgIG1qekFkZERpYWxvZ1RpdGxlOiAi5a2Q6aG555uu5YiX6KGoIiwKICAgICAgLy/mnprkuL7lgLzmlrDlop7lvLnlh7rmoYbmjqfliLYKICAgICAgaXNTaG93TWp6QWRkRGlhbG9nOiBmYWxzZSwKICAgICAgLy/pgInkuK3lrZDpobnnm67ml7bojrflj5bliLDnmoTnrKzkuIDooYzmlbDmja7nlKjmnaXmn6Xor6LmnprkuL7lgLwKICAgICAgbWp6Um93Rm9ybToge30sCiAgICAgIC8v57u05oqk5p6a5Li+5YC8YnV0dG9u5oyJ6ZKuCiAgICAgIHdobWp6QnV0dG9uRGlzYWJsZWQ6IHRydWUsCiAgICAgIC8v5p6a5Li+5YC85pWw5o2uCiAgICAgIGdsenhtRGF0YUxpc3Q6IFtdLAogICAgICAvL+aemuS4vuWAvOWPguaVsAogICAgICBnbHp4bVF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgc3l4bWlkOiB1bmRlZmluZWQsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgIH0sCiAgICAgIC8v5p6a5Li+5YC85oC75pWwCiAgICAgIGdsenhtVG90YWw6IDAsCiAgICAgIC8v5p6a5Li+6aG55by55Ye65qGG5qCH6aKYCiAgICAgIG1qeERpYWxvZ1RpdGxlOiAi5YWz6IGU5a2Q6aG555uuIiwKICAgICAgLy/mnprkuL7pobnlvLnlh7rmoYYKICAgICAgaXNTaG93TWp6RGlhbG9nOiBmYWxzZSwKCiAgICAgIC8v5Yig6Zmk6YCJ5oup5YiXCiAgICAgIHNlbGVjdFJvd3M6IFtdLAogICAgICAvL+ihqOWNlemqjOivgQogICAgICBydWxlczogewogICAgICAgIHp4bW1jOiBbCiAgICAgICAgICB7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXlrZDpobnnm67lkI3np7AnLCB0cmlnZ2VyOiAnYmx1cid9LAogICAgICAgIF0sCiAgICAgIH0sCiAgICAgIC8v5by55Ye65qGG5Y+W5raI56Gu6K6k5oyJ6ZKu5pi+56S6CiAgICAgIGRpYWxvZ0J1dHRvblNob3c6IHRydWUsCiAgICAgIC8v562b6YCJ5qGGCiAgICAgIGZpbHRlckluZm86IHsKICAgICAgICBkYXRhOiB7CiAgICAgICAgICBzeXp5aWQ6ICIiLAogICAgICAgICAgc3l4bW1jOiAiIgogICAgICAgIH0sCiAgICAgICAgZmllbGRMaXN0OiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAn6K+V6aqM5LiT5LiaJywKICAgICAgICAgICAgdmFsdWU6ICdzeXp5aWQnLAogICAgICAgICAgICB0eXBlOiAnc2VsZWN0JywKICAgICAgICAgICAgb3B0aW9uczogW10sCiAgICAgICAgICAgIGNsZWFyYWJsZTogdHJ1ZSwKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAn6aG555uu5ZCN56ewJywKICAgICAgICAgICAgdmFsdWU6ICdzeXhtbWMnLAogICAgICAgICAgICB0eXBlOiAnaW5wdXQnLAogICAgICAgICAgICBjbGVhcmFibGU6IHRydWUsCiAgICAgICAgICB9LAogICAgICAgIF0KICAgICAgfSwKICAgICAgLy/liJfooajpobUKICAgICAgdGFibGVBbmRQYWdlSW5mbzogewogICAgICAgIHBhZ2VyOiB7CiAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgdG90YWw6IDAsCiAgICAgICAgICBzaXplczogWzEwLCAyMCwgNTAsIDEwMF0KICAgICAgICB9LAogICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgdGFibGVIZWFkZXI6IFsKICAgICAgICAgIHtsYWJlbDogJ+S4k+S4muWQjeensCcsIHByb3A6ICd6eW1jJywgbWluV2lkdGg6ICcxMDAnfSwKICAgICAgICAgIHtsYWJlbDogJ+mhueebruWQjeensCcsIHByb3A6ICdzeXhtbWMnLCBtaW5XaWR0aDogJzE1MCd9LAogICAgICAgICAge2xhYmVsOiAn5b2V5YWl5rip5bqmJywgcHJvcDogJ2xyd2QnLCBtaW5XaWR0aDogJzgwJ30sCiAgICAgICAgICB7bGFiZWw6ICflvZXlhaXmub/luqYnLCBwcm9wOiAnbHJzZCcsIG1pbldpZHRoOiAnODAnfSwKICAgICAgICAgIHtsYWJlbDogJ+W9leWFpeayuea4qScsIHByb3A6ICdscnl3JywgbWluV2lkdGg6ICc4MCd9LAogICAgICAgICAge2xhYmVsOiAn5b2V5YWl6LSf6I23JywgcHJvcDogJ2xyZmgnLCBtaW5XaWR0aDogJzgwJ30sCiAgICAgICAgICB7bGFiZWw6ICfmmL7npLrmlrnlvI8nLCBwcm9wOiAneHNmcycsIG1pbldpZHRoOiAnODAnfSwKICAgICAgICAgIHtsYWJlbDogJ+ivlemqjOmhueebruaPj+i/sCcsIHByb3A6ICdzeXhtbXMnLCBtaW5XaWR0aDogJzE1MCd9LAogICAgICAgICAge2xhYmVsOiAn5piv5ZCm5Y+v6LCD5pW05Yid5YC8JywgcHJvcDogJ3Nma3R6Y3onLCBtaW5XaWR0aDogJzE1MCd9LAogICAgICAgICAge2xhYmVsOiAn6YOo5L2N5Y+v5omp5bGVJywgcHJvcDogJ2J3a2t6JywgbWluV2lkdGg6ICcxNTAnfSwKICAgICAgICAgIHtsYWJlbDogJ+WtkOmhueebruWumuS9jScsIHByb3A6ICd6eG1kdycsIG1pbldpZHRoOiAnMTUwJ30sCiAgICAgICAgICB7CiAgICAgICAgICAgIHByb3A6ICdvcGVyYXRpb24nLAogICAgICAgICAgICBsYWJlbDogJ+aTjeS9nCcsCiAgICAgICAgICAgIG1pbldpZHRoOiAnMTUwcHgnLAogICAgICAgICAgICBzdHlsZToge2Rpc3BsYXk6ICdibG9jayd9LAogICAgICAgICAgICAvL+aTjeS9nOWIl+WbuuWumuWGjeWPs+S+pwogICAgICAgICAgICBmaXhlZDogJ3JpZ2h0JywKICAgICAgICAgICAgb3BlcmF0aW9uOiBbCiAgICAgICAgICAgICAge25hbWU6ICfkv67mlLknLCBjbGlja0Z1bjogdGhpcy51bmRhdGVEZXRhaWxzfSwKICAgICAgICAgICAgICB7bmFtZTogJ+ivpuaDhScsIGNsaWNrRnVuOiB0aGlzLmdldERldGFpbHNJbmZvfSwKICAgICAgICAgICAgICB7bmFtZTogJ+WFs+iBlOWtkOmhueebricsIGNsaWNrRnVuOiB0aGlzLmdldFp4bURhdGFJbmZvfSwKICAgICAgICAgICAgXQogICAgICAgICAgfSwKICAgICAgICBdLAogICAgICAgIG9wdGlvbjoge2NoZWNrQm94OiB0cnVlLCBzZXJpYWxOdW1iZXI6IHRydWV9LAogICAgICB9LAogICAgICAvL+afpeivouivlemqjOWtkOmhueebruWPguaVsAogICAgICBxdWVyeVN5enhtUGFyYW06IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMAogICAgICB9LAogICAgICAvL+e7k+aenOexu+Wei+S4i+aLieahhuaVsOaNrgogICAgICBqZ2x4T3B0aW9uc1NlbGVjdGVkTGlzdDogWwogICAgICAgIHtsYWJlbDogJ+WbvueJhycsIHZhbHVlOiAn5Zu+54mHJ30sCiAgICAgICAge2xhYmVsOiAn5pWw5a2XJywgdmFsdWU6ICfmlbDlrZcnfSwKICAgICAgICB7bGFiZWw6ICfml6XmnJ8nLCB2YWx1ZTogJ+aXpeacnyd9LAogICAgICAgIHtsYWJlbDogJ+WNlemAiScsIHZhbHVlOiAn5Y2V6YCJJ30sCiAgICAgICAge2xhYmVsOiAn5p6a5Li+JywgdmFsdWU6ICfmnprkuL4nfSwKICAgICAgICB7bGFiZWw6ICflrZfnrKYnLCB2YWx1ZTogJ+Wtl+espid9LAogICAgICBdLAogICAgICAvL+iuoeeul+WIlwogICAgICBqc2xPcHRpb25zU2VsZWN0ZWRMaXN0OiBbCiAgICAgICAge2xhYmVsOiAn5pivJywgdmFsdWU6ICfmmK8nfSwKICAgICAgICB7bGFiZWw6ICflkKYnLCB2YWx1ZTogJ+WQpid9LAogICAgICBdLAogICAgICAvL+aYr+WQpuS4uuepugogICAgICBzZmt3a09wdGlvbnNTZWxlY3RlZExpc3Q6IFsKICAgICAgICB7bGFiZWw6ICfmmK8nLCB2YWx1ZTogJ+aYryd9LAogICAgICAgIHtsYWJlbDogJ+WQpicsIHZhbHVlOiAn5ZCmJ30sCiAgICAgIF0sCiAgICAgIC8v5piv5ZCm5pi+56S6CiAgICAgIHNmeHNPcHRpb25zU2VsZWN0ZWRMaXN0OiBbCiAgICAgICAge2xhYmVsOiAn5pivJywgdmFsdWU6ICfmmK8nfSwKICAgICAgICB7bGFiZWw6ICflkKYnLCB2YWx1ZTogJ+WQpid9LAogICAgICBdLAoKICAgICAgLy9mb3Jt6KGo5Y2VCiAgICAgIGZvcm06IHt9LAogICAgICAvL+aYr+WQpuaYvuekuuW8ueahhgogICAgICBpc1Nob3dEZXRhaWxzOiBmYWxzZSwKICAgICAgLy/mmK/lkKbnpoHnlKgKICAgICAgaXNEaXNhYmxlZDogZmFsc2UsCiAgICAgIC8v6KGo5Y2V5pWw5o2uCiAgICAgIGRhdGFUYWJsZTogW10sCiAgICAgIC8v5qCH6aKYCiAgICAgIHRpdGxlOiAnJywKCgogICAgICAvL+e7hOe7h+agkQogICAgICB0cmVlT3B0aW9uczogWwogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5pat6Lev5ZmoJywKICAgICAgICB9LCB7CiAgICAgICAgICBsYWJlbDogJ+WPmOWOi+WZqCcsCiAgICAgICAgICBjaGlsZHJlbjogW3sKICAgICAgICAgICAgbGFiZWw6ICflhrfljbTns7vnu58nLAogICAgICAgICAgICBjaGlsZHJlbjogW3sKICAgICAgICAgICAgICBsYWJlbDogJ+a4qeaOp+i/kOihjOaDheWGtScsCgogICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgbGFiZWw6ICfmsrnnrrEnLAoKICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIGxhYmVsOiAn6ZOB6IqvJywKCiAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICBsYWJlbDogJ+e7lee7hCcsCgogICAgICAgICAgICB9XQogICAgICAgICAgfV0KICAgICAgICB9XSwKICAgICAgLy/liKDpmaTmmK/lkKblj6/nlKgKICAgICAgbXVsdGlwbGVTZW5zb3I6IHRydWUsCiAgICAgIC8v5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgYW5jdW9UZXJtOiAnJwogICAgICB9LAogICAgICAvL+WIoOmZpOWFs+iBlOWtkOmhueebrueahGlk5pWw57uECiAgICAgIHNlbGVjdGVkS3h6RGF0YVJvdzogW10sCiAgICAgIC8v5a2Q6aG555uu5bqT5oC75pWwCiAgICAgIHp4bUxpYnJhcnlUb3RhbDogMCwKICAgICAgLy/lrZDpobnnm67lupPmn6Xor6Llj4LmlbAKICAgICAgenhtTGlicmFyeVF1ZXJ5Rm9ybTogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIC8v6K+V6aqM6aG555uuaWQKICAgICAgICBzeXhtaWQ6IHVuZGVmaW5lZCwKICAgICAgICBzeXp4bW1jOiB1bmRlZmluZWQsCiAgICAgIH0sCiAgICAgIC8v6K+V6aqM5a2Q6aG555uu5bqT5Lit6YCJ5Lit5paw5aKe5YWz6IGU5a2Q6aG555uu5pe255qE5pWw5o2u5a+56LGhCiAgICAgIHp4bVNlbGVjdGVkRm9ybTogewogICAgICAgIC8v6K+V6aqM6aG555uuaWQKICAgICAgICBzeXhtaWQ6IHVuZGVmaW5lZCwKICAgICAgICAvL+ivlemqjOWtkOmhueebruaVsOaNrumbhuWQiAogICAgICAgIHp4bURhdGFSb3dzOiBbXQogICAgICB9LAoKCiAgICB9OwogIH0sCiAgd2F0Y2g6IHt9LAogIGNyZWF0ZWQoKSB7CiAgICAvL+iOt+WPluivlemqjOS4k+S4muS4i+aLieahhuaVsOaNrgogICAgdGhpcy5nZXRTeXp5T3B0aW9uc1NlbGVjdGVkKCk7CiAgICAvL+iOt+WPluWIl+ihqOaVsOaNrgogICAgdGhpcy5nZXREYXRhKCk7CgoKICB9LAogIG1ldGhvZHM6IHsKICAgIC8v5a2Q6aG555uu5bqT5Lit5p+l6K+i5oyJ6ZKuCiAgICBzZWxlY3RaeG1MaWJyYXJ5KCkgewogICAgICB0aGlzLmdldFp4bUxpYnJhcnlMaXN0KCk7CiAgICB9LAogICAgLy/lrZDpobnnm67lupPkuK3ph43nva7mjInpkq4KICAgIHJlc2V0WnhtU2VhcmNoKCkgewogICAgICB0aGlzLnp4bUxpYnJhcnlRdWVyeUZvcm0uc3l6eG1tYyA9ICIiOwogICAgfSwKICAgIC8v5YWz6IGU5a2Q6aG555uu5Lit5paw5aKe5oyJ6ZKuCiAgICBhZGRNanooKSB7CiAgICAgIHRoaXMuaXNTaG93TWp6QWRkRGlhbG9nID0gdHJ1ZTsKICAgICAgdGhpcy5nZXRaeG1MaWJyYXJ5TGlzdCgpOwogICAgfSwKICAgIC8v6I635Y+W6K+V6aqM5a2Q6aG555uu5bqT5pWw5o2u5pa55rOVCiAgICBnZXRaeG1MaWJyYXJ5TGlzdCgpIHsKICAgICAgZ2V0U3l6eG1MaWJyYXJ5RGF0YUxpc3RCeVBhZ2UodGhpcy56eG1MaWJyYXJ5UXVlcnlGb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy56eG1MaWJyYXJ5VG90YWwgPSByZXMuZGF0YS50b3RhbDsKICAgICAgICB0aGlzLnp4bUxpYnJhcnlEYXRhTGlzdCA9IHJlcy5kYXRhLnJlY29yZHM7CiAgICAgIH0pCiAgICB9LAogICAgLy/or5XpqozlrZDpobnnm67lupPpgInkuK3mjInpkq4KICAgIGhhbmRsZVNlbGVjdGVkWnhtTGlicmFyeUNoYW5nZShyb3dzKSB7CiAgICAgIHRoaXMuenhtU2VsZWN0ZWRGb3JtLnp4bURhdGFSb3dzID0gcm93czsKICAgIH0sCiAgICAvL+aPkOS6pOS7juWtkOmhueebruW6k+S4reiwg+eUqOaVsOaNruaPkuWFpeWIsOWFs+iBlOWtkOmhueebruihqOS4rQogICAgY29tbWl0QWRkTWp6Rm9ybSgpIHsKICAgICAgaWYgKHRoaXMuenhtU2VsZWN0ZWRGb3JtLnp4bURhdGFSb3dzLmxlbmd0aCA8IDEpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmluZm8oIuacquWFs+iBlOWtkOmhueebru+8ge+8ge+8geW3suWPlua2iCIpCiAgICAgICAgLy/lpoLmnpzmnKrpgInkuK3mlbDmja4s5YiZ55u05o6l5YWz6Zet5by556qXCiAgICAgICAgdGhpcy5pc1Nob3dNanpBZGREaWFsb2cgPSBmYWxzZTsKICAgICAgfSBlbHNlIHsKICAgICAgICAvL+iLpemAieaLqeaVsOaNruWQjgogICAgICAgIGFkZEJhdGNoU3l6eG1Ub1htenhtKHRoaXMuenhtU2VsZWN0ZWRGb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICBpZiAocmVzLmNvZGUgPT09ICcwMDAwJykgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuWFs+iBlOaIkOWKnyIpOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5YWz6IGU5aSx6LSl77yB77yBIik7CiAgICAgICAgICB9CiAgICAgICAgICAvL+WFs+mXreW8ueeqlwogICAgICAgICAgdGhpcy5pc1Nob3dNanpBZGREaWFsb2cgPSBmYWxzZQogICAgICAgICAgLy/osIPnlKjojrflj5blhbPogZTlrZDpobnnm67liJfooagKICAgICAgICAgIHRoaXMuZ2V0TWp6RGF0YUxpc3QoKTsKICAgICAgICB9KQoKICAgICAgfQoKICAgIH0sCiAgICAvL+WFs+iBlOWtkOmhueebruaMiemSrgogICAgZ2V0WnhtRGF0YUluZm8ocm93KSB7CiAgICAgIHRoaXMuZ2x6eG1RdWVyeVBhcmFtcy5zeXhtaWQgPSByb3cub2JqSWQ7CiAgICAgIC8v57uZ5a2Q6aG555uu5bqT5p+l6K+i5Y+C5pWw6LWL5YC8CiAgICAgIHRoaXMuenhtTGlicmFyeVF1ZXJ5Rm9ybS5zeXhtaWQgPSByb3cub2JqSWQ7CiAgICAgIC8v57uZ5om56YeP5paw5aKe5YWz6IGU5a2Q6aG555uu5pe255qE5Li76aG555uuaWTotYvlgLwKICAgICAgdGhpcy56eG1TZWxlY3RlZEZvcm0uc3l4bWlkID0gcm93Lm9iaklkOwogICAgICAvL+aJk+W8gOWtkOmhueebruWIl+ihqOW8ueWHuuahhgogICAgICB0aGlzLmlzU2hvd01qekRpYWxvZyA9IHRydWU7CiAgICAgIC8v6I635Y+W5YWz6IGU5a2Q6aG555uu5YiX6KGo5pWw5o2uCiAgICAgIHRoaXMuZ2V0TWp6RGF0YUxpc3QoKTsKICAgIH0sCgogICAgLy/ojrflj5blhbPogZTlrZDliJfooajmlrnms5UKICAgIGdldE1qekRhdGFMaXN0KCkgewogICAgICBnZXRHbFN5enhtRGF0YUxpc3RCeVBhZ2UodGhpcy5nbHp4bVF1ZXJ5UGFyYW1zKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5nbHp4bVRvdGFsID0gcmVzLmRhdGEudG90YWw7CiAgICAgICAgdGhpcy5nbHp4bURhdGFMaXN0ID0gcmVzLmRhdGEucmVjb3JkczsKICAgICAgfSkKICAgIH0sCiAgICAvL+WIoOmZpOWFs+iBlOWtkOWIl+ihqOaWueazlQogICAgZGVsZXRlTWp6KCkgewogICAgICBpZiAodGhpcy5zZWxlY3RlZEt4ekRhdGFSb3cubGVuZ3RoIDwgMSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+36YCJ5oup5q2j56Gu55qE5pWw5o2u77yB77yB77yBIikKICAgICAgICByZXR1cm4KICAgICAgfQogICAgICBsZXQgaWRzID0gdGhpcy5zZWxlY3RlZEt4ekRhdGFSb3cubWFwKGl0ZW0gPT4gewogICAgICAgIHJldHVybiBpdGVtLm9iaklkCiAgICAgIH0pOwogICAgICB0aGlzLiRjb25maXJtKCfmraTmk43kvZzlsIbmsLjkuYXliKDpmaTor6XmlbDmja4sIOaYr+WQpue7p+e7rT8nLCAn5o+Q56S6JywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgcmVtb3ZlS3h6RGF0YShpZHMpLnRoZW4oKHtjb2RlfSkgPT4gewogICAgICAgICAgaWYgKGNvZGUgPT09ICcwMDAwJykgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOaIkOWKnyEnCiAgICAgICAgICAgIH0pOwogICAgICAgICAgICB0aGlzLmdldE1qekRhdGFMaXN0KCkKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOWksei0pSEnCiAgICAgICAgICAgIH0pOwogICAgICAgICAgICB0aGlzLmdldE1qekRhdGFMaXN0KCkKICAgICAgICAgIH0KICAgICAgICB9KQogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICB0eXBlOiAnaW5mbycsCiAgICAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raI5Yig6ZmkJwogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCgogICAgLy/liJfooajmn6Xor6Lpobnnm67liJfooagKICAgIGFzeW5jIGdldERhdGEocGFyYW1zKSB7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcGFyYW0gPSB7Li4udGhpcy5xdWVyeVN5enhtUGFyYW0sIC4uLnBhcmFtc30KICAgICAgICBjb25zdCB7ZGF0YSwgY29kZX0gPSBhd2FpdCBnZXRQYWdlRGF0YUxpc3QocGFyYW0pOwogICAgICAgIGlmIChjb2RlID09PSAnMDAwMCcpIHsKICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby50YWJsZURhdGEgPSBkYXRhLnJlY29yZHMKICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci50b3RhbCA9IGRhdGEudG90YWwKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICBjb25zb2xlLmxvZyhlKQogICAgICB9CiAgICB9LAogICAgLy/ojrflj5bor5XpqozkuJPkuJrkuIvmi4nmoYbmlbDmja4KICAgIGdldFN5enlPcHRpb25zU2VsZWN0ZWQoKSB7CiAgICAgIGdldFN5enlPcHRpb25zU2VsZWN0ZWQoKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5maWx0ZXJJbmZvLmZpZWxkTGlzdC5tYXAoaXRlbSA9PiB7CiAgICAgICAgICBpZiAoaXRlbS52YWx1ZSA9PT0gJ3N5enlpZCcpIHsKICAgICAgICAgICAgaXRlbS5vcHRpb25zID0gcmVzLmRhdGE7CiAgICAgICAgICAgIHRoaXMuc3l6eU9wdGlvbnNTZWxlY3RlZExpc3QgPSByZXMuZGF0YTsKICAgICAgICAgIH0KICAgICAgICB9KQogICAgICB9KTsKICAgIH0sCgogICAgLy/lj5bmtojmjInpkq4o5p6a5Li+5YC85paw5aKe5by55Ye65qGGKQogICAgY2xvc2VBZGRNanpEaWFsb2coKSB7CiAgICAgIHRoaXMuaXNTaG93TWp6QWRkRGlhbG9nID0gZmFsc2UKICAgIH0sCgogICAgLy/lhbPogZTlrZDliJfooajooYzpgInkuK3kuovku7YKICAgIGhhbmRsZVNlbGVjdGlvbk1qekNoYW5nZShyb3dzKSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRLeHpEYXRhUm93ID0gcm93czsKICAgIH0sCgogICAgLy/nu7TmiqTmnprkuL7lgLzmjInpkq4KICAgIGFkZFp4bUt4eigpIHsKICAgICAgaWYgKHRoaXMubWp6Um93Rm9ybS5qZ2x4ICE9ICfmnprkuL4nKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7fpgInmi6nnu5PmnpznsbvlnovkuLrmnprkuL7nsbvlnovnmoTmlbDmja7vvIEiKQogICAgICB9IGVsc2UgewogICAgICAgIC8v5omT5byA5by556qXCiAgICAgICAgdGhpcy5pc1Nob3dNanpEaWFsb2cgPSB0cnVlOwogICAgICAgIHRoaXMubWp6UXVlcnlQYXJhbXMuc3l6eG1pZCA9IHRoaXMubWp6Um93Rm9ybS5vYmpJZDsKICAgICAgICAvL+iOt+WPluaemuS4vuWAvOWIl+ihqAogICAgICAgIHRoaXMuZ2V0TWp6RGF0YUxpc3QoKTsKICAgICAgfQogICAgfSwKICAgIC8v6KGM6YCJ5LitCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uocm93cykgewogICAgICB0aGlzLnNlbGVjdFJvd3MgPSByb3dzOwogICAgfSwKCiAgICAvL+iOt+WPluivpuaDhQogICAgZ2V0RGV0YWlsc0luZm8ocm93KSB7CiAgICAgIC8v5omT5byA5by556qXCiAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IHRydWU7CiAgICAgIC8v5by55Ye65qGG5oyJ6ZKu5LiN5pi+56S6CiAgICAgIHRoaXMuZGlhbG9nQnV0dG9uU2hvdyA9IGZhbHNlOwogICAgICAvL+e7meihqOWNlei1i+WAvAogICAgICB0aGlzLmZvcm0gPSByb3c7CiAgICAgIC8v6KGo5Y2V5LiN5Y+v57yW6L6RCiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IHRydWU7CiAgICAgIC8v6K6+572u5by55Ye65qGG5qCH6aKYCiAgICAgIHRoaXMudGl0bGUgPSAn6K+m5oOFJzsKICAgIH0sCiAgICAvL+S/ruaUueaMiemSrgogICAgdW5kYXRlRGV0YWlscyhyb3cpIHsKICAgICAgLy/miZPlvIDlvLnnqpcKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gdHJ1ZTsKICAgICAgLy/lvLnlh7rmoYbmjInpkq7mmL7npLoKICAgICAgdGhpcy5kaWFsb2dCdXR0b25TaG93ID0gdHJ1ZTsKICAgICAgLy/nu5nooajljZXotYvlgLwKICAgICAgdGhpcy5mb3JtID0gcm93OwogICAgICAvL+ihqOWNleWPr+e8lui+kQogICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZTsKICAgICAgLy/orr7nva7lvLnlh7rmoYbmoIfpopgKICAgICAgdGhpcy50aXRsZSA9ICfkv67mlLknOwogICAgfSwKICAgIC8v5re75Yqg5oyJ6ZKuCiAgICBhZGRTZW5zb3JCdXR0b24oKSB7CiAgICAgIC8v5omT5byA5by556qXCiAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IHRydWU7CiAgICAgIC8v5by55Ye65qGG5oyJ6ZKu5pi+56S6CiAgICAgIHRoaXMuZGlhbG9nQnV0dG9uU2hvdyA9IHRydWU7CiAgICAgIC8v57uZ6KGo5Y2V572u56m6CiAgICAgIHRoaXMuZm9ybSA9IHt9OwogICAgICAvL+ihqOWNleWPr+e8lui+kQogICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZTsKICAgICAgLy/orr7nva7lvLnlh7rmoYbmoIfpopgKICAgICAgdGhpcy50aXRsZSA9ICfmlrDlop4nOwogICAgfSwKICAgIC8v5Yig6Zmk5oyJ6ZKuCiAgICBnZXREZWxldGUoKSB7CiAgICAgIGlmICh0aGlzLnNlbGVjdFJvd3MubGVuZ3RoIDwgMSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+36YCJ5oup5q2j56Gu55qE5pWw5o2u77yB77yB77yBIikKICAgICAgICByZXR1cm4KICAgICAgfQogICAgICBsZXQgaWRzID0gdGhpcy5zZWxlY3RSb3dzLm1hcChpdGVtID0+IHsKICAgICAgICByZXR1cm4gaXRlbS5vYmpJZAogICAgICB9KTsKICAgICAgdGhpcy4kY29uZmlybSgn5q2k5pON5L2c5bCG5rC45LmF5Yig6Zmk6K+l5pWw5o2uLCDmmK/lkKbnu6fnu60/JywgJ+aPkOekuicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHJlbW92ZShpZHMpLnRoZW4oKHtjb2RlfSkgPT4gewogICAgICAgICAgaWYgKGNvZGUgPT09ICcwMDAwJykgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOaIkOWKnyEnCiAgICAgICAgICAgIH0pOwogICAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIucGFnZVJlc2l6ZSA9ICdZJzsKICAgICAgICAgICAgdGhpcy5nZXREYXRhKCkKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOWksei0pSEnCiAgICAgICAgICAgIH0pOwogICAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIucGFnZVJlc2l6ZSA9ICdZJzsKICAgICAgICAgICAgdGhpcy5nZXREYXRhKCkKICAgICAgICAgIH0KICAgICAgICB9KQogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICB0eXBlOiAnaW5mbycsCiAgICAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raI5Yig6ZmkJwogICAgICAgIH0pOwogICAgICB9KTsKCiAgICB9LAogICAgLy/noa7orqTmj5DkuqTooajljZUKICAgIGNvbW1pdEZvcm0oKSB7CiAgICAgIHNhdmVPclVwZGF0ZSh0aGlzLmZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICBpZiAocmVzLmNvZGUgPT09ICcwMDAwJykgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKHJlcy5tc2cpCiAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIucGFnZVJlc2l6ZSA9ICdZJzsKICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gZmFsc2U7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1zZykKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8v5YWz6Zet5by556qXCiAgICBjbG9zZSgpIHsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gZmFsc2UKICAgIH0sCiAgICAvL+WumuS5iemHjee9ruaWueazlQogICAgZ2V0UmVzZXQoKSB7CgogICAgfSwKICAgIC8v5Yig6Zmk5oyJ6ZKuCiAgICBkZWxldGVTZW5zb3JCdXR0b24oKSB7CgogICAgfSwKICAgIC8v5a+85Ye65oyJ6ZKuCiAgICBoYW5kbGVFeHBvcnQoKSB7CgogICAgfSwKCgogICAgLy/mkJzntKIKICAgIGhhbmRsZVF1ZXJ5KCkgewoKICAgIH0sCiAgICAvL+mHjee9rgogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["syxmzq.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+LA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;;AAGA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "syxmzq.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk", "sourcesContent": ["<template>\n\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 180, itemWidth: 300 }\"\n      @handleReset=\"getReset\"\n    />\n    <div>\n      <el-white class=\"button-group\">\n        <div class=\"button_btn\">\n          <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"addSensorButton\"\n          >新增\n          </el-button>\n          <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"getDelete\"\n          >删除\n          </el-button>\n        </div>\n        <comp-table :table-and-page-info=\"tableAndPageInfo\"\n                    @update:multipleSelection=\"handleSelectionChange\"\n                    height=\"57.2vh\"/>\n      </el-white>\n      <!-- 详情/新增/修改 -->\n      <el-dialog :title=title :visible.sync=\"isShowDetails\" width=\"60%\" v-dialogDrag>\n        <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :disabled=\"isDisabled\" :rules=\"rules\">\n          <el-row :gutter=\"8\" class=\"pull-left\">\n            <el-col :span=\"8\">\n              <el-form-item label=\"试验专业：\" prop=\"jglx\">\n                <el-select v-model=\"form.syzyid\" style=\"width: 100%\">\n                  <el-option\n                    v-for=\"item in syzyOptionsSelectedList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"项目名称：\" prop=\"syxmmc\">\n                <el-input v-model=\"form.syxmmc\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"项目描述：\" prop=\"zxmms\">\n                <el-input v-model=\"form.syxmms\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"录入温度：\" prop=\"lrwd\">\n                <el-select v-model=\"form.lrwd\" style=\"width: 100%\">\n                  <el-option label=\"是\" value=\"是\"></el-option>\n                  <el-option label=\"否\" value=\"否\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"录入湿度：\" prop=\"lrsd\">\n                <el-select v-model=\"form.lrsd\" style=\"width: 100%\">\n                  <el-option label=\"是\" value=\"是\"></el-option>\n                  <el-option label=\"否\" value=\"否\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"录入油温：\" prop=\"lryw\">\n                <el-select v-model=\"form.lryw\" style=\"width: 100%\">\n                  <el-option label=\"是\" value=\"是\"></el-option>\n                  <el-option label=\"否\" value=\"否\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"录入负荷：\" prop=\"lrfh\">\n                <el-select v-model=\"form.lrfh\" style=\"width: 100%\">\n                  <el-option label=\"是\" value=\"是\"></el-option>\n                  <el-option label=\"否\" value=\"否\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"可调整初值：\" prop=\"sfktzcz\">\n                <el-select v-model=\"form.sfktzcz\" style=\"width: 100%\">\n                  <el-option label=\"是\" value=\"是\"></el-option>\n                  <el-option label=\"否\" value=\"否\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"部位可拓展：\" prop=\"bwkkz\">\n                <el-select v-model=\"form.bwkkz\" style=\"width: 100%\">\n                  <el-option label=\"是\" value=\"是\"></el-option>\n                  <el-option label=\"否\" value=\"否\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"显示方式：\" prop=\"xsfs\">\n                <el-select v-model=\"form.xsfs\" style=\"width: 100%\">\n                  <el-option label=\"列为子项目\" value=\"列为子项目\"></el-option>\n                  <el-option label=\"竖排\" value=\"竖排\"></el-option>\n                  <el-option label=\"行为部位\" value=\"行为部位\"></el-option>\n                  <el-option label=\"列为部位\" value=\"列为部位\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"子项目定位：\" prop=\"zxmdw\">\n                <el-select v-model=\"form.zxmdw\" style=\"width: 100%\">\n                  <el-option label=\"相别\" value=\"相别\"></el-option>\n                  <el-option label=\"设备\" value=\"设备\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n        <div slot=\"footer\" v-show=\"dialogButtonShow\">\n          <el-button @click=\"close\">取 消</el-button>\n          <el-button type=\"primary\" @click=\"commitForm\">确 认</el-button>\n        </div>\n      </el-dialog>\n      <!--子项目列表弹出框-->\n      <el-dialog :title=\"mjxDialogTitle\" :visible.sync=\"isShowMjzDialog\" width=\"50%\" v-dialogDrag>\n        <el-white class=\"button-group\">\n          <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n            <el-col\n              style=\"display: flex;justify-content: space-between;align-items: center;\">\n              <div>\n                <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"addMjz\">新增</el-button>\n                <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"deleteMjz\">删除</el-button>\n              </div>\n            </el-col>\n          </el-row>\n        </el-white>\n        <el-table stripe border :data=\"glzxmDataList\" @selection-change=\"handleSelectionMjzChange\"\n                  :header-cell-style=\"{'text-align':'center'}\" :cell-style=\"{'text-align':'center'}\">\n          <el-table-column type=\"selection\" width=\"55\"/>\n          <el-table-column label=\"序号\" type=\"index\" width=\"50\">\n          </el-table-column>\n          <el-table-column label=\"试验子项目\" prop=\"syzxmmc\" :show-overflow-tooltip=\"true\"/>\n          <el-table-column label=\"子项目描述\" prop=\"syzxmms\" :show-overflow-tooltip=\"true\"/>\n        </el-table>\n        <pagination\n          v-show=\"glzxmTotal>0\"\n          :total=\"glzxmTotal\"\n          :page.sync=\"glzxmQueryParams.pageNum\"\n          :limit.sync=\"glzxmQueryParams.pageSize\"\n          @pagination=\"getMjzDataList\"\n        />\n      </el-dialog>\n      <!--子列表新增弹窗调用-->\n      <el-dialog :title=\"mjzAddDialogTitle\" :visible.sync=\"isShowMjzAddDialog\" width=\"50%\" v-dialogDrag>\n        <el-form label-width=\"120px\">\n          <el-row :gutter=\"3\">\n            <el-col :span=\"10\">\n              <el-form-item label=\"子项目名称：\">\n                <el-input v-model=\"zxmLibraryQueryForm.syzxmmc\"/>\n              </el-form-item>\n            </el-col>\n            <div class=\"mb8 pull-right\">\n              <el-button type=\"cyan\" size=\"mini\" icon=\"el-icon-search\" @click=\"selectZxmLibrary\">查询</el-button>\n              <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetZxmSearch\">重置</el-button>\n            </div>\n          </el-row>\n        </el-form>\n        <el-table stripe border :data=\"zxmLibraryDataList\" @selection-change=\"handleSelectedZxmLibraryChange\"\n                  :header-cell-style=\"{'text-align':'center'}\" :cell-style=\"{'text-align':'center'}\">\n          <el-table-column type=\"selection\" width=\"55\"/>\n          <el-table-column label=\"序号\" type=\"index\" width=\"50\">\n          </el-table-column>\n          <el-table-column label=\"试验子项目\" prop=\"zxmmc\" :show-overflow-tooltip=\"true\"/>\n          <el-table-column label=\"子项目描述\" prop=\"zxmms\" :show-overflow-tooltip=\"true\"/>\n        </el-table>\n        <pagination\n          v-show=\"zxmLibraryTotal>0\"\n          :total=\"zxmLibraryTotal\"\n          :page.sync=\"zxmLibraryQueryForm.pageNum\"\n          :limit.sync=\"zxmLibraryQueryForm.pageSize\"\n          @pagination=\"getZxmLibraryList\"\n        />\n        <div slot=\"footer\">\n          <el-button @click=\"closeAddMjzDialog\">取 消</el-button>\n          <el-button type=\"primary\" @click=\"commitAddMjzForm\">确 认</el-button>\n        </div>\n      </el-dialog>\n    </div>\n  </div>\n</template>\n\n<script>\n  import {\n    getPageDataList,\n    getSyzyOptionsSelected,\n    getGlSyzxmDataListByPage,\n    getSyzxmLibraryDataListByPage,\n    addBatchSyzxmToXmzxm,\n    remove,\n    saveOrUpdate,\n    getPageKxzDataList,\n    removeKxzData,\n    saveOrUpdateKxzData,\n    getSblxTree\n  } from '@/api/dagangOilfield/bzgl/syxm'\n\n\n  export default {\n    name: \"lpbzk\",\n    data() {\n      return {\n        //试验子项目库数据\n        zxmLibraryDataList: [],\n        //试验专业下拉框数据\n        syzyOptionsSelectedList: [],\n        //枚举值新增弹出框底部按钮控制显示\n        addMjzDialogButtonShow: true,\n        //控制枚举值新增弹出框内容是否可编辑\n        mjzAddDialogDisabled: false,\n        //枚举值新增form表单\n        mjzAddForm: {\n          syzxmid: undefined,\n          kxz: undefined\n        },\n        //枚举值新增弹出框标题\n        mjzAddDialogTitle: \"子项目列表\",\n        //枚举值新增弹出框控制\n        isShowMjzAddDialog: false,\n        //选中子项目时获取到的第一行数据用来查询枚举值\n        mjzRowForm: {},\n        //维护枚举值button按钮\n        whmjzButtonDisabled: true,\n        //枚举值数据\n        glzxmDataList: [],\n        //枚举值参数\n        glzxmQueryParams: {\n          syxmid: undefined,\n          pageSize: 10,\n          pageNum: 1,\n        },\n        //枚举值总数\n        glzxmTotal: 0,\n        //枚举项弹出框标题\n        mjxDialogTitle: \"关联子项目\",\n        //枚举项弹出框\n        isShowMjzDialog: false,\n\n        //删除选择列\n        selectRows: [],\n        //表单验证\n        rules: {\n          zxmmc: [\n            {required: true, message: '请输入子项目名称', trigger: 'blur'},\n          ],\n        },\n        //弹出框取消确认按钮显示\n        dialogButtonShow: true,\n        //筛选框\n        filterInfo: {\n          data: {\n            syzyid: \"\",\n            syxmmc: \"\"\n          },\n          fieldList: [\n            {\n              label: '试验专业',\n              value: 'syzyid',\n              type: 'select',\n              options: [],\n              clearable: true,\n            },\n            {\n              label: '项目名称',\n              value: 'syxmmc',\n              type: 'input',\n              clearable: true,\n            },\n          ]\n        },\n        //列表页\n        tableAndPageInfo: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          tableData: [],\n          tableHeader: [\n            {label: '专业名称', prop: 'zymc', minWidth: '100'},\n            {label: '项目名称', prop: 'syxmmc', minWidth: '150'},\n            {label: '录入温度', prop: 'lrwd', minWidth: '80'},\n            {label: '录入湿度', prop: 'lrsd', minWidth: '80'},\n            {label: '录入油温', prop: 'lryw', minWidth: '80'},\n            {label: '录入负荷', prop: 'lrfh', minWidth: '80'},\n            {label: '显示方式', prop: 'xsfs', minWidth: '80'},\n            {label: '试验项目描述', prop: 'syxmms', minWidth: '150'},\n            {label: '是否可调整初值', prop: 'sfktzcz', minWidth: '150'},\n            {label: '部位可扩展', prop: 'bwkkz', minWidth: '150'},\n            {label: '子项目定位', prop: 'zxmdw', minWidth: '150'},\n            {\n              prop: 'operation',\n              label: '操作',\n              minWidth: '150px',\n              style: {display: 'block'},\n              //操作列固定再右侧\n              fixed: 'right',\n              operation: [\n                {name: '修改', clickFun: this.undateDetails},\n                {name: '详情', clickFun: this.getDetailsInfo},\n                {name: '关联子项目', clickFun: this.getZxmDataInfo},\n              ]\n            },\n          ],\n          option: {checkBox: true, serialNumber: true},\n        },\n        //查询试验子项目参数\n        querySyzxmParam: {\n          pageNum: 1,\n          pageSize: 10\n        },\n        //结果类型下拉框数据\n        jglxOptionsSelectedList: [\n          {label: '图片', value: '图片'},\n          {label: '数字', value: '数字'},\n          {label: '日期', value: '日期'},\n          {label: '单选', value: '单选'},\n          {label: '枚举', value: '枚举'},\n          {label: '字符', value: '字符'},\n        ],\n        //计算列\n        jslOptionsSelectedList: [\n          {label: '是', value: '是'},\n          {label: '否', value: '否'},\n        ],\n        //是否为空\n        sfkwkOptionsSelectedList: [\n          {label: '是', value: '是'},\n          {label: '否', value: '否'},\n        ],\n        //是否显示\n        sfxsOptionsSelectedList: [\n          {label: '是', value: '是'},\n          {label: '否', value: '否'},\n        ],\n\n        //form表单\n        form: {},\n        //是否显示弹框\n        isShowDetails: false,\n        //是否禁用\n        isDisabled: false,\n        //表单数据\n        dataTable: [],\n        //标题\n        title: '',\n\n\n        //组织树\n        treeOptions: [\n          {\n            label: '断路器',\n          }, {\n            label: '变压器',\n            children: [{\n              label: '冷却系统',\n              children: [{\n                label: '温控运行情况',\n\n              }, {\n                label: '油箱',\n\n              }, {\n                label: '铁芯',\n\n              }, {\n                label: '绕组',\n\n              }]\n            }]\n          }],\n        //删除是否可用\n        multipleSensor: true,\n        //查询参数\n        queryParams: {\n          pageNum: 1,\n          pageSize: 10,\n          ancuoTerm: ''\n        },\n        //删除关联子项目的id数组\n        selectedKxzDataRow: [],\n        //子项目库总数\n        zxmLibraryTotal: 0,\n        //子项目库查询参数\n        zxmLibraryQueryForm: {\n          pageNum: 1,\n          pageSize: 10,\n          //试验项目id\n          syxmid: undefined,\n          syzxmmc: undefined,\n        },\n        //试验子项目库中选中新增关联子项目时的数据对象\n        zxmSelectedForm: {\n          //试验项目id\n          syxmid: undefined,\n          //试验子项目数据集合\n          zxmDataRows: []\n        },\n\n\n      };\n    },\n    watch: {},\n    created() {\n      //获取试验专业下拉框数据\n      this.getSyzyOptionsSelected();\n      //获取列表数据\n      this.getData();\n\n\n    },\n    methods: {\n      //子项目库中查询按钮\n      selectZxmLibrary() {\n        this.getZxmLibraryList();\n      },\n      //子项目库中重置按钮\n      resetZxmSearch() {\n        this.zxmLibraryQueryForm.syzxmmc = \"\";\n      },\n      //关联子项目中新增按钮\n      addMjz() {\n        this.isShowMjzAddDialog = true;\n        this.getZxmLibraryList();\n      },\n      //获取试验子项目库数据方法\n      getZxmLibraryList() {\n        getSyzxmLibraryDataListByPage(this.zxmLibraryQueryForm).then(res => {\n          this.zxmLibraryTotal = res.data.total;\n          this.zxmLibraryDataList = res.data.records;\n        })\n      },\n      //试验子项目库选中按钮\n      handleSelectedZxmLibraryChange(rows) {\n        this.zxmSelectedForm.zxmDataRows = rows;\n      },\n      //提交从子项目库中调用数据插入到关联子项目表中\n      commitAddMjzForm() {\n        if (this.zxmSelectedForm.zxmDataRows.length < 1) {\n          this.$message.info(\"未关联子项目！！！已取消\")\n          //如果未选中数据,则直接关闭弹窗\n          this.isShowMjzAddDialog = false;\n        } else {\n          //若选择数据后\n          addBatchSyzxmToXmzxm(this.zxmSelectedForm).then(res => {\n            if (res.code === '0000') {\n              this.$message.success(\"关联成功\");\n            } else {\n              this.$message.error(\"关联失败！！\");\n            }\n            //关闭弹窗\n            this.isShowMjzAddDialog = false\n            //调用获取关联子项目列表\n            this.getMjzDataList();\n          })\n\n        }\n\n      },\n      //关联子项目按钮\n      getZxmDataInfo(row) {\n        this.glzxmQueryParams.syxmid = row.objId;\n        //给子项目库查询参数赋值\n        this.zxmLibraryQueryForm.syxmid = row.objId;\n        //给批量新增关联子项目时的主项目id赋值\n        this.zxmSelectedForm.syxmid = row.objId;\n        //打开子项目列表弹出框\n        this.isShowMjzDialog = true;\n        //获取关联子项目列表数据\n        this.getMjzDataList();\n      },\n\n      //获取关联子列表方法\n      getMjzDataList() {\n        getGlSyzxmDataListByPage(this.glzxmQueryParams).then(res => {\n          this.glzxmTotal = res.data.total;\n          this.glzxmDataList = res.data.records;\n        })\n      },\n      //删除关联子列表方法\n      deleteMjz() {\n        if (this.selectedKxzDataRow.length < 1) {\n          this.$message.warning(\"请选择正确的数据！！！\")\n          return\n        }\n        let ids = this.selectedKxzDataRow.map(item => {\n          return item.objId\n        });\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          removeKxzData(ids).then(({code}) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              });\n              this.getMjzDataList()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              });\n              this.getMjzDataList()\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          });\n        });\n      },\n\n      //列表查询项目列表\n      async getData(params) {\n        try {\n          const param = {...this.querySyzxmParam, ...params}\n          const {data, code} = await getPageDataList(param);\n          if (code === '0000') {\n            this.tableAndPageInfo.tableData = data.records\n            this.tableAndPageInfo.pager.total = data.total\n          }\n        } catch (e) {\n          console.log(e)\n        }\n      },\n      //获取试验专业下拉框数据\n      getSyzyOptionsSelected() {\n        getSyzyOptionsSelected().then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value === 'syzyid') {\n              item.options = res.data;\n              this.syzyOptionsSelectedList = res.data;\n            }\n          })\n        });\n      },\n\n      //取消按钮(枚举值新增弹出框)\n      closeAddMjzDialog() {\n        this.isShowMjzAddDialog = false\n      },\n\n      //关联子列表行选中事件\n      handleSelectionMjzChange(rows) {\n        this.selectedKxzDataRow = rows;\n      },\n\n      //维护枚举值按钮\n      addZxmKxz() {\n        if (this.mjzRowForm.jglx != '枚举') {\n          this.$message.warning(\"请选择结果类型为枚举类型的数据！\")\n        } else {\n          //打开弹窗\n          this.isShowMjzDialog = true;\n          this.mjzQueryParams.syzxmid = this.mjzRowForm.objId;\n          //获取枚举值列表\n          this.getMjzDataList();\n        }\n      },\n      //行选中\n      handleSelectionChange(rows) {\n        this.selectRows = rows;\n      },\n\n      //获取详情\n      getDetailsInfo(row) {\n        //打开弹窗\n        this.isShowDetails = true;\n        //弹出框按钮不显示\n        this.dialogButtonShow = false;\n        //给表单赋值\n        this.form = row;\n        //表单不可编辑\n        this.isDisabled = true;\n        //设置弹出框标题\n        this.title = '详情';\n      },\n      //修改按钮\n      undateDetails(row) {\n        //打开弹窗\n        this.isShowDetails = true;\n        //弹出框按钮显示\n        this.dialogButtonShow = true;\n        //给表单赋值\n        this.form = row;\n        //表单可编辑\n        this.isDisabled = false;\n        //设置弹出框标题\n        this.title = '修改';\n      },\n      //添加按钮\n      addSensorButton() {\n        //打开弹窗\n        this.isShowDetails = true;\n        //弹出框按钮显示\n        this.dialogButtonShow = true;\n        //给表单置空\n        this.form = {};\n        //表单可编辑\n        this.isDisabled = false;\n        //设置弹出框标题\n        this.title = '新增';\n      },\n      //删除按钮\n      getDelete() {\n        if (this.selectRows.length < 1) {\n          this.$message.warning(\"请选择正确的数据！！！\")\n          return\n        }\n        let ids = this.selectRows.map(item => {\n          return item.objId\n        });\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove(ids).then(({code}) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              });\n              this.tableAndPageInfo.pager.pageResize = 'Y';\n              this.getData()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              });\n              this.tableAndPageInfo.pager.pageResize = 'Y';\n              this.getData()\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          });\n        });\n\n      },\n      //确认提交表单\n      commitForm() {\n        saveOrUpdate(this.form).then(res => {\n          if (res.code === '0000') {\n            this.$message.success(res.msg)\n            this.tableAndPageInfo.pager.pageResize = 'Y';\n            this.getData();\n            this.isShowDetails = false;\n          } else {\n            this.$message.error(res.msg)\n          }\n        });\n      },\n      //关闭弹窗\n      close() {\n        this.isShowDetails = false\n      },\n      //定义重置方法\n      getReset() {\n\n      },\n      //删除按钮\n      deleteSensorButton() {\n\n      },\n      //导出按钮\n      handleExport() {\n\n      },\n\n\n      //搜索\n      handleQuery() {\n\n      },\n      //重置\n      resetQuery() {\n        this.resetForm(\"queryForm\");\n      }\n    }\n  };\n</script>\n\n<style lang='scss' scoped>\n  .box-card {\n    margin-bottom: 15px;\n\n    .el-card__header {\n      background-color: rgb(235, 245, 255) !important;\n    }\n  }\n\n  .item {\n    width: 200px;\n    height: 148px;\n    float: left;\n  }\n</style>\n"]}]}