{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\xldzcz\\components\\czp_tj.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\xldzcz\\components\\czp_tj.vue", "mtime": 1748603097178}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["czp_tj.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAkBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "czp_tj.vue", "sourceRoot": "src/views/dagangOilfield/czpgl/xldzcz/components", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 110, itemWidth: 220 }\"\n      @handleEvent=\"handleEvent\"\n    />\n\n    <el-white class=\"button-group\">\n      <div>\n        <comp-table :table-and-page-info=\"tableAndPageInfo\" @update:multipleSelection=\"selectChange\" height=\"60vh\"/>\n      </div>\n    </el-white>\n  </div>\n</template>\n\n<script>\nimport { getListTj, getBdzSelectList } from '@/api/yxgl/bdyxgl/bddzczp'\n\nexport default {\n  name: 'czp_tj',\n  mounted() {\n    //列表查询\n    this.getData()\n    //获取变电站下拉框数据\n    this.getBdzSelectList()\n  },\n  data() {\n    return {\n      // 查询变电站列表\n      bdzList: {},\n      //查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      // 查询数据总条数\n      total: 0,\n      filterInfo: {\n        data: {\n          xlmc: ''\n        },//查询条件\n        fieldList: [\n          { label: '线路名称', value: 'xlmc', type: 'input', clearable: true, filterable: true, options: [] }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: '线路名称', prop: 'xlmc', minWidth: '150' },\n          { label: '操作项数', prop: 'czxs', minWidth: '100' },\n          { label: '已执行项数', prop: 'yzxczxs', minWidth: '100' },\n          { label: '未执行项数', prop: 'wzxczxs', minWidth: '100' }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        lx: 1,\n        status: '4'\n      }\n    }\n  },\n  methods: {\n    //统计\n    async getData(params) {\n      try {\n        const param = { ...this.params, ...params }\n        const { data, code } = await getListTj(param)\n        console.log('统计到的数据：',data)\n        if (code === '0000') {\n          this.tableAndPageInfo.tableData = data.records\n          this.tableAndPageInfo.pager.total = data.total\n        }\n      } catch (e) {\n        console.log(e)\n      }\n    },\n    /**\n     * 列表选中\n     * */\n    selectChange(selection) {\n      this.ids = selection.map(item => item.objId)\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n    },\n    /**\n     * 获取变电站下拉框数据\n     */\n    getBdzSelectList() {\n      getBdzSelectList({}).then(res => {\n        this.bdzList = res.data\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == 'bdzmc') {\n            return item.options = this.bdzList\n          }\n        })\n      })\n    },\n    //下拉框change事件\n    handleEvent(val) {\n      //变电站查询下拉框选项根据所选分公司带出来\n      if (val.label === 'fgs' && val.value && val.value !== '') {\n        let form ={\n          ssdwbm: val.value\n        }\n        getBdzSelectList(form).then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value == 'bdzmc') {\n              return item.options = res.data;\n            }\n          })\n        })\n      }\n    },\n  }\n}\n</script>\n\n<style scoped>\n\n/*列表颜色设置*/\n/deep/ .el-table th {\n  background-color: #e8f7f0;\n}\n</style>\n"]}]}