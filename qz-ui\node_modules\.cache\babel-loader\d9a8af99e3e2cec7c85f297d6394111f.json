{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\sybgjlcomment\\symbSyxmSelect.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\sybgjlcomment\\symbSyxmSelect.vue", "mtime": 1706897323435}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["symbSyxmSelect.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AA4bA;;AACA;;AAUA;;AACA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAhBA;gBAkBA;AACA,EAAA,IAAA,EAAA,gBADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,MAAA,EAAA,eAAA;AAAA,IAAA,YAAA,EAAA;AAAA,GAFA;AAGA,EAAA,KAAA,EAAA;AACA;AACA,IAAA,QAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAAA,EAAA;AAAA,eAAA;AACA,UAAA,MAAA,EAAA;AADA,SAAA;AAAA;AAFA;AAFA,GAHA;AAaA,EAAA,IAbA,kBAaA;AACA,WAAA;AACA;AACA,MAAA,cAAA,EAAA,CACA;AACA,oBAAA,MADA;AAEA,oBAAA,MAFA;AAGA,oBAAA,MAHA;AAIA,oBAAA,MAJA;AAKA,oBAAA,EALA;AAMA,oBAAA,EANA;AAOA,oBAAA,EAPA;AAQA,oBAAA;AARA,OADA,EAWA;AACA,oBAAA,MADA;AAEA,oBAAA,KAFA;AAGA,oBAAA,KAHA;AAIA,oBAAA,KAJA;AAKA,oBAAA,EALA;AAMA,oBAAA,EANA;AAOA,oBAAA,EAPA;AAQA,oBAAA;AARA,OAXA,EAqBA;AACA,oBAAA,MADA;AAEA,oBAAA,SAFA;AAGA,oBAAA,WAHA;AAIA,oBAAA,MAJA;AAKA,oBAAA,EALA;AAMA,oBAAA,EANA;AAOA,oBAAA,EAPA;AAQA,oBAAA;AARA,OArBA,CAFA;AAkCA;AACA,MAAA,cAAA,EAAA,CAAA;AACA,oBAAA,MADA;AAEA,oBAAA,MAFA;AAGA,oBAAA,EAHA;AAIA,oBAAA,EAJA;AAKA,oBAAA,EALA;AAMA,oBAAA;AANA,OAAA,CAnCA;AA2CA;AACA,MAAA,GAAA,EAAA,CAAA;AACA,QAAA,KAAA,EAAA,EADA;AACA;AACA,QAAA,OAAA,EAAA,EAFA;AAEA;AACA,QAAA,MAAA,EAAA,EAHA,CAGA;;AAHA,OAAA,CA5CA;AAiDA;AACA,MAAA,oBAAA,EAAA,KAlDA;AAmDA,MAAA,QAAA,EAAA;AACA,QAAA,EAAA,EAAA,WADA;AACA;AACA,QAAA,QAAA,EAAA,QAFA;AAEA;AACA,QAAA,YAAA,EAAA,QAHA;AAIA,QAAA,OAAA,EAAA;AAJA,OAnDA;AAyDA,MAAA,MAAA,EAAA,EAzDA;AA0DA;AACA,MAAA,SAAA,EAAA,EA3DA;AA4DA;AACA,MAAA,SAAA,EAAA,EA7DA;AA8DA;AACA,MAAA,kBAAA,EAAA,KA/DA;AAgEA,MAAA,cAAA,EAAA;AACA;AACA,QAAA,MAAA,EAAA,SAFA;AAGA;AACA,QAAA,UAAA,EAAA;AAJA,OAhEA;AAsEA;AACA,MAAA,uBAAA,EAAA,KAvEA;AAwEA;AACA,MAAA,mBAAA,EAAA,KAzEA;AA0EA;AACA,MAAA,kBAAA,EAAA;AACA,QAAA,MAAA,EAAA,SADA;AAEA,QAAA,MAAA,EAAA,EAFA;AAGA,QAAA,OAAA,EAAA,CAHA;AAIA,QAAA,QAAA,EAAA;AAJA,OA3EA;AAiFA;AACA,MAAA,iBAAA,EAAA,EAlFA;AAmFA;AACA,MAAA,cAAA,EAAA,CApFA;AAqFA;AACA,MAAA,SAAA,EAAA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AADA,OAtFA;AA2FA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,IAAA,EAAA;AADA,SADA;AAIA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA;AAJA,OA5FA;AAoGA;AACA,MAAA,WAAA,EAAA,IArGA;AAsGA;AACA,MAAA,UAAA,EAAA,EAvGA;AAwGA;AACA,MAAA,aAAA,EAAA,EAzGA;AA0GA;AACA,MAAA,IAAA,EAAA,EA3GA;AA4GA;AACA,MAAA,cAAA,EAAA;AACA,QAAA,MAAA,EAAA,SADA;AAEA,QAAA,IAAA,EAAA,SAFA;AAGA,QAAA,OAAA,EAAA,CAHA;AAIA,QAAA,QAAA,EAAA;AAJA,OA7GA;AAmHA;AACA,MAAA,QAAA,EAAA,EApHA;AAqHA;AACA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,CARA;AAaA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAbA,OAtHA;AAqIA;AACA,MAAA,WAAA,EAAA,EAtIA;AAwIA,MAAA,aAAA,EAAA,KAxIA;AAyIA,MAAA,KAAA,EAAA,EAzIA;AA2IA;AACA,MAAA,cAAA,EAAA,IA5IA;AA6IA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,EAAA,EAAA,SADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,QAAA,EAAA;AAHA,OA9IA;AAmJA,MAAA,UAAA,EAAA,KAnJA;AAoJA,MAAA,MAAA,EAAA,IApJA;AAsJA;AACA,MAAA,eAAA,EAAA,MAvJA;AAwJA;AACA,MAAA,gBAAA,EAAA,KAzJA;AA2JA;AACA,MAAA,SAAA,EAAA,CA5JA;AA6JA;AACA,MAAA,eAAA,EAAA;AACA,QAAA,MAAA,EAAA,SADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,QAAA,EAAA;AAHA,OA9JA;AAoKA;AACA,MAAA,UAAA,EAAA,CArKA;AAsKA;AACA,MAAA,gBAAA,EAAA;AACA,QAAA,MAAA,EAAA,SADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,OAAA,EAAA;AAHA,OAvKA;AA6KA;AACA,MAAA,cAAA,EAAA,EA9KA;AA+KA;AACA,MAAA,eAAA,EAAA,EAhLA;AAiLA;AACA,MAAA,qBAAA,EAAA,EAlLA;AAmLA;AACA,MAAA,YAAA,EAAA,KApLA;AAqLA;AACA,MAAA,OAAA,EAAA,EAtLA;AAwLA;AACA,MAAA,MAAA,EAAA,EAzLA;AA2LA;AACA,MAAA,YAAA,EAAA,EA5LA;AA6LA;AACA,MAAA,UAAA,EAAA,CAAA,MAAA,EAAA,IAAA,EAAA,IAAA;AA9LA,KAAA;AAgMA,GA9MA;AA+MA,EAAA,KAAA,EAAA,EA/MA;AAgNA,EAAA,OAhNA,qBAgNA;AACA;AACA,SAAA,OAAA;AACA,GAnNA;AAoNA,EAAA,OApNA,qBAoNA,CACA,CArNA;AAsNA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,eAFA,iCAEA;AAAA,UAAA,GAAA,QAAA,GAAA;AAAA,UAAA,MAAA,QAAA,MAAA;AAAA,UAAA,QAAA,QAAA,QAAA;AAAA,UAAA,WAAA,QAAA,WAAA;;AACA,UAAA,KAAA,UAAA,CAAA,QAAA,CAAA,GAAA,CAAA,IAAA,CAAA,EAAA;AACA,YAAA,WAAA,GAAA,CAAA,EAAA;AACA,iBAAA,CAAA,CAAA,EAAA,GAAA,CAAA,QAAA,CAAA;AACA;AACA;AACA,KARA;AASA;AACA,IAAA,cAVA,iCAUA;AAAA,UAAA,GAAA,SAAA,GAAA;AAAA,UAAA,MAAA,SAAA,MAAA;AAAA,UAAA,QAAA,SAAA,QAAA;AAAA,UAAA,WAAA,SAAA,WAAA;;AACA,UAAA,WAAA,GAAA,CAAA,EAAA;AACA,eAAA,CAAA,CAAA,EAAA,CAAA,CAAA;AACA;AACA,KAdA;AAeA;AACA,IAAA,YAhBA,wBAgBA,GAhBA,EAgBA;AACA;AACA,WAAA,iBAAA,CAAA,GAAA,EAFA,CAGA;;AACA,WAAA,cAAA,CAAA,GAAA;AACA,KArBA;AAsBA;AACA,IAAA,WAvBA,yBAuBA;AACA,yBAAA,WAAA,CAAA,QAAA,CAAA,aAAA,CAAA,iBAAA,CAAA,EAAA,KAAA,MAAA,CAAA,IAAA;AACA,KAzBA;AA0BA;AACA,IAAA,iBA3BA,6BA2BA,KA3BA,EA2BA;AAAA;;AACA,qCAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,KAAA,CAAA,MAAA,GAAA,GAAA,CAAA,IAAA,CADA,CAEA;AACA;AACA;;AACA,QAAA,KAAA,CAAA,oBAAA,GAAA,IAAA;AACA,OANA;AAQA,KApCA;AAqCA;AACA,IAAA,cAtCA,0BAsCA,OAtCA,EAsCA;AAAA;;AACA;AACA,WAAA,YAAA,GAAA,EAAA;AACA,kCAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,MAAA,GAAA,GAAA,CAAA,IAAA,CADA,CAEA;;AACA,aAAA,IAAA,GAAA,IAAA,MAAA,EAAA;AACA;AACA,UAAA,MAAA,CAAA,cAAA,CAAA,GAAA,EAAA,MAAA,CAAA,GAAA,CAAA;AACA,SANA,CAOA;;;AACA,QAAA,MAAA,CAAA,mBAAA;AACA,OATA;AAUA,KAnDA;AAoDA;AACA,IAAA,cArDA,0BAqDA,MArDA,EAqDA,YArDA,EAqDA;AACA,UAAA,QAAA,GAAA,EAAA;AACA,MAAA,QAAA,CAAA,MAAA,GAAA,MAAA;;AACA,WAAA,IAAA,GAAA,IAAA,YAAA,CAAA,CAAA,CAAA,EAAA;AACA,QAAA,QAAA,CAAA,GAAA,CAAA,GAAA,YAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA;AACA;;AACA,WAAA,YAAA,CAAA,IAAA,CAAA,QAAA;AACA,KA5DA;AA6DA;AACA,IAAA,mBA9DA,iCA8DA;AAAA;;AACA,WAAA,GAAA,GAAA,EAAA,CADA,CAEA;AACA;;AACA,UAAA,IAAA,GAAA,KAAA,YAAA;;AACA,UAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AAAA,mCACA,CADA;AAEA,cAAA,SAAA,GAAA,IAAA,CAAA,CAAA,CAAA;AACA,cAAA,KAAA,GAAA,SAAA,CAAA,MAAA,CAHA,CAGA;;AACA,cAAA,MAAA,GAAA,SAAA,CAAA,MAAA,CAJA,CAIA;;AACA,cAAA,OAAA,GAAA,SAAA,CAAA,OAAA,CALA,CAKA;;AACA,cAAA,EAAA,GAAA,CACA;AACA,qBAAA,KADA;AACA;AACA,2BAAA,MAFA,CAEA;;AAFA,WADA,CAAA;AAMA,UAAA,OAAA,CAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,EAAA,CAAA,IAAA,CAAA;AACA,uBAAA,GAAA,CAAA,OADA;AACA;AACA,6BAAA,EAFA,CAEA;;AAFA,aAAA;AAIA,WALA;AAMA,cAAA,EAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,OAAA,CAAA,UAAA,EAAA,EAAA;AACA,YAAA,EAAA,CAAA,IAAA,CAAA;AACA,sBAAA,EAAA,CAAA,IADA;AAEA,0BAAA,OAAA,CAAA,MAFA,CAEA;;AAFA,aAAA;AAIA,WALA,EAnBA,CAyBA;;AACA,UAAA,EAAA,CAAA,IAAA,CACA;AACA,oBAAA,IADA;AAEA,wBAAA,OAAA,CAAA,MAFA,CAEA;;AAFA,WADA,EAKA;AACA,oBAAA,MADA;AAEA,wBAAA,OAAA,CAAA,MAFA,CAEA;;AAFA,WALA,EASA;AACA,oBAAA,IADA;AAEA,wBAAA,OAAA,CAAA,MAFA,CAEA;;AAFA,WATA,EAaA;AACA,oBAAA,IADA;AAEA,wBAAA,OAAA,CAAA,MAFA,CAEA;;AAFA,WAbA;;AAkBA,UAAA,MAAA,CAAA,GAAA,CAAA,IAAA,CAAA;AACA,YAAA,KAAA,EAAA,KADA;AAEA,YAAA,OAAA,EAAA,EAFA;AAGA,YAAA,MAAA,EAAA;AAHA,WAAA;AA5CA;;AACA,aAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AAAA,gBAAA,CAAA;AAgDA;AACA,OAvDA,CAwDA;;AACA;;;;;;;;;;;;;;;AAeA,KAtIA;AAuIA;AACA,IAAA,WAxIA,uBAwIA,MAxIA,EAwIA,GAxIA,EAwIA;AACA;AACA,2BAAA,cAAA,EAAA,IAAA,CAAA,EAAA,EAFA,CAGA;;AACA,WAAA,MAAA,GAAA,EAAA;AACA,WAAA,MAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CALA,CAMA;;AACA,UAAA,GAAA,GAAA,EAAA,CAPA,CAQA;;AACA,UAAA,MAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,YAAA,MAAA,CAAA,CAAA,CAAA,CAAA,IAAA,IAAA,GAAA,EAAA;AAAA;AACA;AACA,UAAA,GAAA,IAAA,2DACA,mDADA,GAEA,mDAFA,GAGA,mDAHA,GAIA,OAJA,CAFA,CAOA;;AACA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,MAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,YAAA,GAAA,IAAA,MAAA;AACA,YAAA,GAAA,IAAA,6CAAA;AACA,YAAA,GAAA,IAAA,MAAA,CAAA,CAAA,CAAA,CAAA,KAAA,GAAA,OAAA;AACA,YAAA,GAAA,IAAA,YAAA;AACA,YAAA,GAAA,IAAA,YAAA;AACA,YAAA,GAAA,IAAA,YAAA;AACA,YAAA,GAAA,IAAA,OAAA;AACA;AACA,SAjBA,MAiBA;AAAA;AACA;AACA;AACA,cAAA,GAAA,GAAA,CAAA,CAHA,CAIA;;AACA,cAAA,KAAA,GAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAAA,MAAA,GAAA,GAAA,CAAA,CALA,CAMA;;AACA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,EAAA,CAAA,EAAA,EAAA;AACA,YAAA,GAAA,IAAA,MAAA,CADA,CAEA;;AACA,iBAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,GAAA,EAAA,CAAA,EAAA,EAAA;AACA,kBAAA,CAAA,GAAA,GAAA,GAAA,CAAA,GAAA,MAAA,CAAA,MAAA,EAAA;AACA,gBAAA,GAAA,IAAA,6CAAA,CADA,CAEA;;AACA,gBAAA,GAAA,IAAA,MAAA,CAAA,CAAA,GAAA,GAAA,GAAA,CAAA,CAAA,CAAA,KAAA,GAAA,OAAA,CAHA,CAIA;;AACA,gBAAA,GAAA,IAAA,MAAA,CAAA,CAAA,GAAA,GAAA,GAAA,CAAA,CAAA,CAAA,IAAA,IAAA,CAAA,GAAA,4DAAA,GAAA,SAAA,MAAA,CAAA,CAAA,GAAA,GAAA,GAAA,CAAA,CAAA,CAAA,WAAA,GAAA,OAAA;AACA;AACA;;AACA,YAAA,GAAA,IAAA,OAAA;AACA;AACA;AACA,OAjDA,CAkDA;;;AACA,WAAA,SAAA,CAAA,YAAA;AACA,6BAAA,cAAA,EAAA,MAAA,CAAA,GAAA;AACA,OAFA,EAnDA,CAsDA;;AACA,WAAA,oBAAA,GAAA,IAAA;AACA,KAhMA;AAiMA;AACA,IAAA,aAlMA,2BAkMA;AACA;AACA,WAAA,MAAA,GAAA,EAAA,CAFA,CAGA;;AACA,WAAA,oBAAA,GAAA,KAAA;AACA,KAvMA;AAyMA;AACA,IAAA,eA1MA,2BA0MA,GA1MA,EA0MA;AACA;AACA,WAAA,kBAAA,GAAA,IAAA,CAFA,CAGA;;AACA,WAAA,SAAA,GAAA,GAAA;AACA,KA/MA;AAiNA;AACA,IAAA,eAlNA,6BAkNA;AAAA;;AACA,mCAAA,KAAA,kBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,iBAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,MAAA,CAAA,cAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,OAHA;AAIA,KAvNA;AAyNA;AACA,IAAA,SA1NA,uBA0NA;AACA,WAAA,eAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,KA7NA;AA+NA;AACA,IAAA,iBAhOA,+BAgOA;AACA,WAAA,mBAAA,GAAA,KAAA;AACA,KAlOA;AAoOA;AACA,IAAA,gBArOA,8BAqOA;AAAA;;AACA,UAAA,KAAA,cAAA,CAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,aAAA,EADA,CAEA;;AACA,aAAA,mBAAA,GAAA,KAAA;AACA,OAJA,MAIA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,KAAA,cAAA,EADA,CAEA;;AACA,0CAAA,KAAA,cAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA,WAFA,MAEA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;AACA,WALA,CAMA;;;AACA,UAAA,MAAA,CAAA,mBAAA,GAAA,KAAA,CAPA,CAQA;;AACA,UAAA,MAAA,CAAA,2BAAA;AACA,SAVA;AAWA;AACA,KAzPA;AA2PA;AACA,IAAA,6BA5PA,yCA4PA,IA5PA,EA4PA;AACA,WAAA,cAAA,CAAA,UAAA,GAAA,IAAA;AACA,KA9PA;AAgQA;AACA,IAAA,eAjQA,6BAiQA;AACA,WAAA,eAAA;AACA,KAnQA;AAqQA;AACA,IAAA,aAtQA,2BAsQA;AACA,WAAA,kBAAA,CAAA,MAAA,GAAA,EAAA;AACA,WAAA,eAAA;AACA,KAzQA;AA2QA;AACA,IAAA,cA5QA,4BA4QA;AAAA;;AACA,0CAAA,KAAA,gBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,MAAA,CAAA,eAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,OAHA;AAIA,KAjRA;AAoRA;AACA,IAAA,eArRA,2BAqRA,GArRA,EAqRA;AACA;AACA,WAAA,eAAA,GAAA,EAAA,CAFA,CAGA;;AACA,WAAA,gBAAA,GAAA,IAAA,CAJA,CAKA;;AACA,WAAA,eAAA,CAAA,MAAA,GAAA,GAAA,CAAA,KAAA,CANA,CAOA;;AACA,WAAA,kBAAA,CAAA,MAAA,GAAA,GAAA,CAAA,KAAA,CARA,CASA;;AACA,WAAA,cAAA,CAAA,MAAA,GAAA,GAAA,CAAA,KAAA,CAVA,CAWA;;AACA,WAAA,2BAAA;AACA,KAlSA;AAoSA;AACA,IAAA,2BArSA,yCAqSA;AAAA;;AACA,+CAAA,KAAA,eAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,cAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,MAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,OAHA;AAIA,KA1SA;AA4SA;AACA,IAAA,wBA7SA,oCA6SA,IA7SA,EA6SA;AACA,WAAA,qBAAA,GAAA,IAAA;AACA,KA/SA;AAiTA;AACA,IAAA,YAlTA,0BAkTA;AAAA;;AACA,UAAA,KAAA,qBAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,aAAA;AACA;AACA;;AACA,UAAA,GAAA,GAAA,KAAA,qBAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA;AACA,OAFA,CAAA;AAGA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,4BAAA,GAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,cAAA,IAAA,SAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,2BAAA;AACA,WANA,MAMA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,2BAAA;AACA;AACA,SAdA;AAeA,OApBA,EAoBA,KApBA,CAoBA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OAzBA;AA0BA,KApVA;AAqVA;AACA,IAAA,oBAtVA,gCAsVA,GAtVA,EAsVA;AACA,WAAA,gBAAA,CAAA,MAAA,GAAA,GAAA,CAAA,MAAA;AACA,WAAA,cAAA;AACA,KAzVA;AA0VA;AACA,IAAA,QA3VA,oBA2VA,IA3VA,EA2VA,OA3VA,EA2VA;AAAA;;AACA,UAAA,YAAA,GAAA;AACA,QAAA,GAAA,EAAA,EADA;AAEA,QAAA,OAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,MAAA;AAFA,OAAA;;AAIA,UAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA;AACA,QAAA,YAAA,CAAA,GAAA,GAAA,IAAA;AACA,eAAA,KAAA,WAAA,CAAA,YAAA,EAAA,OAAA,CAAA;AACA;;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,YAAA,CAAA,GAAA,GAAA,IAAA,CAAA,IAAA,CAAA,IAAA;;AACA,QAAA,MAAA,CAAA,WAAA,CAAA,YAAA,EAAA,OAAA;AACA,OAHA,EAGA,GAHA,CAAA;AAKA,KAzWA;AA0WA;AACA,IAAA,WA3WA,uBA2WA,QA3WA,EA2WA,OA3WA,EA2WA;AACA,+CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,SAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,GAAA;AACA,YAAA,IAAA,EAAA,IAAA,CAAA,IADA;AAEA,YAAA,KAAA,EAAA,IAAA,CAAA,KAFA;AAGA,YAAA,EAAA,EAAA,IAAA,CAAA,EAHA;AAIA,YAAA,GAAA,EAAA,IAAA,CAAA,GAJA;AAKA,YAAA,IAAA,EAAA,KALA;AAMA,YAAA,IAAA,EAAA,IAAA,CAAA;AANA,WAAA;AAQA,UAAA,SAAA,CAAA,IAAA,CAAA,IAAA;AACA,SAVA;AAWA,QAAA,OAAA,CAAA,SAAA,CAAA;AACA,OAdA;AAeA,KA3XA;AA4XA;AACA,IAAA,IA7XA,kBA6XA;AAAA;;AACA,WAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,oCAAA,OAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,cAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,GAAA,CAAA,GAAA;;AACA,cAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,cAAA,OAAA,CAAA,OAAA;;AACA,cAAA,OAAA,CAAA,aAAA,GAAA,KAAA;AACA,aALA,MAKA;AACA,cAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA;AACA;AACA,WATA;AAWA;AACA,OAdA;AAeA,KA7YA;AA8YA;AACA,IAAA,eA/YA,2BA+YA,IA/YA,EA+YA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,OAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA;;AACA,UAAA,IAAA,CAAA,KAAA,IAAA,GAAA,IAAA,IAAA,CAAA,KAAA,IAAA,GAAA,EAAA;AACA;AACA,aAAA,WAAA,GAAA,KAAA;AACA,aAAA,QAAA,GAAA,IAAA;AACA,aAAA,cAAA,CAAA,MAAA,GAAA,IAAA,CAAA,IAAA;AACA,aAAA,OAAA;AACA,OANA,MAMA;AACA,aAAA,WAAA,GAAA,IAAA;AACA;AACA,KA3ZA;AA4ZA;AACA,IAAA,eA7ZA,6BA6ZA;AACA,WAAA,IAAA,GAAA,EAAA;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,KAAA,QAAA,CAAA,IAAA;AACA,WAAA,IAAA,CAAA,MAAA,GAAA,KAAA,QAAA,CAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,KAAA,GAAA,IAAA;AACA,KAnaA;AAoaA;AACA,IAAA,OAraA,mBAqaA,MAraA,EAqaA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,KAFA,+DAEA,OAAA,CAAA,cAFA,GAEA,MAFA;AAGA,gBAAA,KAAA,CAAA,MAAA,GAAA,OAAA,CAAA,QAAA,CAAA,MAAA;AAHA;AAAA,uBAIA,mCAAA,KAAA,CAJA;;AAAA;AAAA;AAIA,gBAAA,IAJA,yBAIA,IAJA;AAIA,gBAAA,IAJA,yBAIA,IAJA;;AAKA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AARA;AAAA;;AAAA;AAAA;AAAA;AAUA,gBAAA,OAAA,CAAA,GAAA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,KAjbA;;AAmbA;;;AAGA,IAAA,qBAtbA,iCAsbA,IAtbA,EAsbA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KAxbA;AA0bA,IAAA,KA1bA,mBA0bA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,KA5bA;AA8bA;AACA,IAAA,aA/bA,yBA+bA,GA/bA,EA+bA;AACA,WAAA,KAAA,GAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,IAAA,GAAA,GAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,MAAA,GAAA,IAAA;AACA,KArcA;AAwcA,IAAA,cAxcA,0BAwcA,GAxcA,EAwcA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,KA1cA;AA4cA;AACA,IAAA,UA7cA,sBA6cA,GA7cA,EA6cA;AACA,WAAA,KAAA,GAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,IAAA,GAAA,GAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,MAAA,GAAA,KAAA;AACA,KAndA;AAsdA;AACA,IAAA,kBAvdA,gCAudA;AAAA;;AACA,UAAA,KAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,aAAA;AACA;AACA;;AACA,UAAA,GAAA,GAAA,KAAA,UAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA;AACA,OAFA,CAAA;AAGA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,4BAAA,GAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,cAAA,IAAA,SAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,YAAA,OAAA,CAAA,OAAA;AACA,WAPA,MAOA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,YAAA,OAAA,CAAA,OAAA;AACA;AACA,SAhBA;AAiBA,OAtBA,EAsBA,KAtBA,CAsBA,YAAA;AACA,QAAA,OAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OA3BA;AA6BA,KA5fA;AA8fA;AACA,IAAA,YA/fA,0BA+fA,CAEA,CAjgBA;AAogBA;AACA,IAAA,eArgBA,2BAqgBA,GArgBA,EAqgBA;AACA,WAAA,YAAA,GAAA,IAAA;AACA,WAAA,OAAA,GAAA,GAAA;AACA,KAxgBA;AA0gBA;AACA,IAAA,aA3gBA,2BA2gBA;AACA,WAAA,YAAA,GAAA,KAAA;AACA,KA7gBA;AAghBA,IAAA,WAhhBA,yBAghBA,CACA,CAjhBA;AAmhBA;AACA,IAAA,gBAphBA,8BAohBA;AACA,WAAA,KAAA,CAAA,uBAAA,EAAA,KAAA;AACA,KAthBA;AAuhBA;AACA;AACA,IAAA,YAzhBA,0BAyhBA;AACA,UAAA,KAAA,UAAA,CAAA,MAAA,IAAA,CAAA,IAAA,KAAA,UAAA,IAAA,SAAA,EAAA;AACA,aAAA,KAAA,CAAA,oBAAA,EAAA,KAAA,UAAA,CAAA,CAAA,CAAA;AACA,aAAA,KAAA,CAAA,uBAAA,EAAA,KAAA;AACA,OAHA,MAGA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,SAAA;AACA;AACA;AAhiBA;AAtNA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row>\n      <!--右侧列表-->\n      <el-col :span=\"24\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 140, itemWidth: 190 }\"\n          @handleReset=\"filterReset\"\n        />\n        <el-white>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            :is-single=\"true\"\n            height=\"61.5vh\">\n\n            <!--            <el-table-column slot=\"table_six\" align=\"center\" label=\"关联铭牌\" min-width=\"150\" :resizable=\"false\">-->\n            <!--              <template slot-scope=\"scope\">-->\n            <!--                <el-button @click=\"handleClickGlMp(scope.row)\" type=\"text\" size=\"small\" v-if=\"scope.row.sfty == '是'\">-->\n            <!--                  查看铭牌-->\n            <!--                </el-button>-->\n            <!--                <el-button @click=\"handleClickGlMp(scope.row)\" type=\"text\" size=\"small\" v-else>关联铭牌</el-button>-->\n            <!--              </template>-->\n            <!--            </el-table-column>-->\n\n            <!--            <el-table-column slot=\"table_seven\" align=\"center\" label=\"关联项目\" min-width=\"150\" :resizable=\"false\">-->\n            <!--              <template slot-scope=\"scope\">-->\n            <!--                <el-button @click=\"handleClickGlxm(scope.row)\" type=\"text\" size=\"small\" v-if=\"scope.row.sfty == '是'\">-->\n            <!--                  查看项目-->\n            <!--                </el-button>-->\n            <!--                <el-button @click=\"handleClickGlxm(scope.row)\" type=\"text\" size=\"small\" v-else>关联项目</el-button>-->\n            <!--              </template>-->\n            <!--            </el-table-column>-->\n\n            <!--            <el-table-column slot=\"table_eight\" align=\"center\" label=\"定义模板内容\" min-width=\"150\" :resizable=\"false\">-->\n            <!--              <template slot-scope=\"scope\">-->\n            <!--                <el-button @click=\"handleClickMbnr(scope.row)\" type=\"text\" size=\"small\" v-if=\"scope.row.sfty == '是'\">-->\n            <!--                  查看模板内容-->\n            <!--                </el-button>-->\n            <!--                <el-button @click=\"handleClickMbnr(scope.row)\" type=\"text\" size=\"small\" v-else>定义模板内容</el-button>-->\n            <!--              </template>-->\n            <!--            </el-table-column>-->\n\n            <el-table-column slot=\"table_eight\" align=\"center\" label=\"模板详情\" min-width=\"150\" :resizable=\"false\">\n              <template slot-scope=\"scope\">\n                <el-button v-print=\"printObj\" type=\"text\" size=\"small\" @click=\"handleMbInfo(scope.row)\">模板详情</el-button>\n              </template>\n            </el-table-column>\n\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n    <el-row>\n      <div style=\"text-align: right;margin-top: 2vh\">\n        <el-button @click=\"closeSymbComment\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"commitMbdata\">确 定</el-button>\n      </div>\n    </el-row>\n    <!--新增\\修改\\详情弹出-->\n    <el-dialog :title=title :visible.sync=\"isShowDetails\" width=\"60%\" v-dialogDrag>\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"mbzbRules\">\n        <el-row :gutter=\"8\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"设备类型：\" prop=\"sblx\">\n              <el-input v-model=\"form.sblx\" disabled style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"设备类型编码：\" prop=\"sblxbm\">\n              <el-input v-model=\"form.sblxid\" disabled style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"模板名称：\" prop=\"mbmc\">\n              <el-input placeholder=\"请输入试验部位名称\" v-model=\"form.mbmc\" :disabled=\"isDisabled\"\n                        style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否默认：\" prop=\"sfmr\">\n              <el-select v-model=\"form.sfmr\" style=\"width: 100%\" :disabled=\"isDisabled\">\n                <el-option label=\"是\" value=\"是\"></el-option>\n                <el-option label=\"否\" value=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否停用：\" prop=\"sfty\">\n              <el-select v-model=\"form.sfty\" style=\"width: 100%\" :disabled=\"isDisabled\">\n                <el-option label=\"是\" value=\"是\"></el-option>\n                <el-option label=\"否\" value=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" v-show=\"isShow\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"save\">确 认</el-button>\n      </div>\n    </el-dialog>\n\n    <!--关联铭牌弹框-->\n    <el-dialog :visible.sync=\"showMpDialog\" title=\"已关联铭牌\" v-if=\"showMpDialog\" v-dialogDrag>\n      <glsymp :main-data=\"rowData\" :tree-data=\"treeForm\" @closeMpDialog=\"closeMpDialog\"></glsymp>\n    </el-dialog>\n\n    <!--关联试验项目弹出框-->\n    <el-dialog :title=\"glxmDialogTitle\" :visible.sync=\"isGlxmDialogShow\" width=\"60%\" v-dialogDrag>\n      <el-row :gutter=\"3\">\n        <div class=\"mb8 pull-right\">\n          <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"mini\" @click=\"addMbGlXm\">新增项目</el-button>\n          <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"mini\" @click=\"deleteMbGlXm\">删除项目</el-button>\n        </div>\n      </el-row>\n      <el-row :gutter=\"20\">\n        <el-col :span=\"12\">\n          <el-table :data=\"mbGlxmDataList\" @selection-change=\"handleGlxmSelectedChange\"\n                    @row-click=\"handleMbGlxmRowClick\">\n            <el-table-column label=\"试验项目\" align=\"center\">\n              <el-table-column type=\"selection\" width=\"55\" align=\"center\"/>\n              <el-table-column label=\"序号\" type=\"index\" width=\"50\" align=\"center\"></el-table-column>\n              <el-table-column prop=\"syxmmc\" label=\"项目名称\" width=\"180\" align=\"center\"></el-table-column>\n              <el-table-column prop=\"syxmms\" label=\"项目描述\" align=\"center\"></el-table-column>\n            </el-table-column>\n          </el-table>\n          <pagination\n            :total=\"glxmTotal\"\n            :page.sync=\"glxmQueryParams.pageNum\"\n            :limit.sync=\"glxmQueryParams.pageSize\"\n            @pagination=\"getSymbGlsyxmDataListByPage\"\n          />\n        </el-col>\n        <el-col :span=\"12\">\n          <el-table :data=\"zxmGlmbDataList\">\n            <el-table-column label=\"试验子项目\" align=\"center\">\n              <el-table-column prop=\"syzxmmc\" label=\"子项目名称\" width=\"180\" align=\"center\"></el-table-column>\n              <el-table-column prop=\"syzxmms\" label=\"子项目描述\" align=\"center\"></el-table-column>\n            </el-table-column>\n          </el-table>\n          <pagination\n            :total=\"glzxmTotal\"\n            :page.sync=\"glzxmQueryParams.pageNum\"\n            :limit.sync=\"glzxmQueryParams.pageSize\"\n            @pagination=\"getZxmDataList\"\n          />\n        </el-col>\n      </el-row>\n\n    </el-dialog>\n    <!--列表新增关联项目弹窗调用-->\n    <el-dialog :title=\"xmLibraryAddDialogTitle\" :visible.sync=\"isShowAddGlxmDialog\" width=\"50%\" v-dialogDrag>\n      <el-form label-width=\"120px\">\n        <el-row :gutter=\"3\">\n          <el-col :span=\"10\">\n            <el-form-item label=\"项目名称：\">\n              <el-input v-model=\"xmLibraryQueryForm.syxmmc\"/>\n            </el-form-item>\n          </el-col>\n          <div class=\"mb8 pull-right\">\n            <el-button type=\"cyan\" size=\"mini\" icon=\"el-icon-search\" @click=\"selectxmLibrary\">查询</el-button>\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetxmSearch\">重置</el-button>\n          </div>\n        </el-row>\n      </el-form>\n      <el-table stripe border :data=\"xmLibraryDataList\" @selection-change=\"handleSelectedXmLibraryChange\"\n                :header-cell-style=\"{'text-align':'center'}\" :cell-style=\"{'text-align':'center'}\">\n        <el-table-column type=\"selection\" width=\"55\"/>\n        <el-table-column label=\"序号\" type=\"index\" width=\"50\">\n        </el-table-column>\n        <el-table-column label=\"试验项目\" prop=\"syxmmc\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"项目描述\" prop=\"syxmms\" :show-overflow-tooltip=\"true\"/>\n      </el-table>\n      <pagination\n        v-show=\"xmLibraryTotal>0\"\n        :total=\"xmLibraryTotal\"\n        :page.sync=\"xmLibraryQueryForm.pageNum\"\n        :limit.sync=\"xmLibraryQueryForm.pageSize\"\n        @pagination=\"getXmLiraryData\"\n      />\n      <div slot=\"footer\">\n        <el-button @click=\"closeAddMjzDialog\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"commitAddMjzForm\">确 认</el-button>\n      </div>\n    </el-dialog>\n    <!--子组件定义模板内容-->\n    <el-dialog title=\"项目关联部位\" :visible.sync=\"isShowXmGlbwDialog\" v-if=\"isShowXmGlbwDialog\" v-dialogDrag>\n      <symbwh-dymbnr ref=\"symbwhDymbnrRef\" :mb-data=\"mbRowData\"></symbwh-dymbnr>\n    </el-dialog>\n    <!--  打印vue print nb插件-->\n    <div v-show=\"false\">\n      <div id=\"printHtmlId\" style=\"background:white;\">\n        <!--模板-->\n        <div style=\"text-align: center\">\n          <p>{{mbInfo.mbmc}}</p>\n        </div>\n        <p>葫芦娃，葫芦娃</p>\n        <p>一根藤上七朵花 </p>\n        <p>小小树藤是我家 啦啦啦啦 </p>\n        <p>叮当当咚咚当当　浇不大</p>\n        <p> 叮当当咚咚当当 是我家</p>\n        <p> 啦啦啦啦</p>\n        <p>...</p>\n        <div class=\"describle\">\n          <el-form :model=\"mbInfo\" :rules=\"mbzbRules\" ref=\"from\" class=\"demo-ruleForm\">\n            <el-form-item label=\"姓名:\" prop=\"name\">\n              <el-input v-model=\"mbInfo.mbmc\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"描述:\" prop=\"describle\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"4\"\n                :maxlength=\"2000\"\n                placeholder=\"\"\n                v-model=\"mbInfo.mbmc\">\n              </el-input>\n            </el-form-item>\n          </el-form>\n        </div>\n      </div>\n    </div>\n    <!--htmlToPdf插件-->\n    <el-dialog title=\"预览\" :visible.sync=\"isShowDownLoadDialog\" width=\"60%\" append-to-body v-dialogDrag>\n      <el-button @click=\"downloadPdf\">导出</el-button>\n      <div style=\"width: 100%;height:60vh;overflow: auto\">\n        <div id=\"printContentId\">\n          <div style=\"text-align: center\">\n            <h2>{{mbInfo.mbmc}}</h2></div>\n          <div>\n            <div\n              style=\"font-size: 20px;float: left;width:100%;border-left: 1px solid #000;border-right: 1px solid #000;border-top: 1px solid #000;\">\n              一、基本信息\n            </div>\n            <el-table\n              :data=\"tableData_jbxx\"\n              border\n              style=\"border: 1px solid #000;\"\n            >\n              <el-table-column\n                prop=\"column_1\"\n                label=\"变电站\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_5\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_2\"\n                label=\"委托单位\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_6\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_3\"\n                label=\"试验单位\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_7\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_4\"\n                label=\"运行编号\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_8\"\n                label=\"\"\n              >\n              </el-table-column>\n            </el-table>\n            <!--            <tbody>\n                        <tr>\n                          <td>变电站</td>\n                          <td></td>\n                          <td>委托单位</td>\n                          <td></td>\n                          <td>试验单位</td>\n                          <td></td>\n                          <td>运行编号</td>\n                          <td></td>\n                        </tr>\n                        <tr>\n                          <td>试验性质</td>\n                          <td></td>\n                          <td>试验日期</td>\n                          <td></td>\n                          <td>试验人员</td>\n                          <td></td>\n                          <td>试验地点</td>\n                          <td></td>\n                        </tr>\n                        <tr>\n                          <td>报告日期</td>\n                          <td></td>\n                          <td>编写人</td>\n                          <td></td>\n                          <td>审核人</td>\n                          <td></td>\n                          <td>批准人</td>\n                          <td></td>\n                        </tr>\n                        <tr>\n                          <td>试验天气</td>\n                          <td></td>\n                          <td>环境温度（℃）</td>\n                          <td></td>\n                          <td>环境相对湿度（%）</td>\n                          <td></td>\n                          <td>投运日期</td>\n                          <td></td>\n                        </tr>\n                        </tbody>-->\n          </div>\n          <div>\n            <div\n              style=\"font-size: 20px;float: left;width:100%;border-left: 1px solid #000;border-right: 1px solid #000;\">\n              二、设备铭牌\n            </div>\n            <el-table\n              :data=\"tableData_sbmp\"\n              border\n              :span-method=\"sbmpSpanMethod\"\n              style=\"border: 1px solid #000;\"\n            >\n              <el-table-column\n                prop=\"column_1\"\n                label=\"生产厂家\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_4\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_2\"\n                label=\"出厂编号\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_5\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_3\"\n                label=\"出厂日期\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_6\"\n                label=\"\"\n              >\n              </el-table-column>\n            </el-table>\n            <!--            <tbody id=\"sbmpTbodyId\">\n                        </tbody>-->\n          </div>\n          <div>\n            <div\n              style=\"font-size: 20px;float: left;width:100%;border-left: 1px solid #000;border-right: 1px solid #000;border-bottom: 1px solid #000;\">\n              三、试验数据\n            </div>\n            <div v-for=\"item in arr\" style=\"width: 100%\">\n              <div class=\"printTitle\">{{item.title}}</div>\n              <el-table :data=\"item.bwList\" style=\"width:100%;border: 1px solid #000;\" border\n                        :span-method=\"arraySpanMethod\">\n                <template v-for='(val) in item.zxmList'>\n                  <el-table-column\n                    :prop=\"val.column_name\"\n                    width=\"auto\"\n                    :label=\"val.label\"\n                  >\n                  </el-table-column>\n\n                </template>\n\n              </el-table>\n            </div>\n\n            <!--            <tbody id=\"sysjTableId\">\n                        <tr>\n                          <td colspan=\"5\" style=\"text-align: left;font-weight: bold\">12121212</td>\n                        </tr>\n                        <tr>\n                          <td>部位</td>\n                          <td>回路电阻初值(μΩ)</td>\n                          <td>回路电阻(μΩ)</td>\n                          <td>主回路电阻初值差(%)</td>\n                          <td>是否合格</td>\n                        </tr>\n                        <tr>\n                          <td>部位1</td>\n                          <td></td>\n                          <td></td>\n                          <td></td>\n                          <td></td>\n                        </tr>\n                        <tr>\n                          <td>部位2</td>\n                          <td></td>\n                          <td></td>\n                          <td></td>\n                          <td></td>\n                        </tr>\n                        <tr>\n                          <td>仪器型号</td>\n                          <td colspan=\"4\"></td>\n                        </tr>\n                        <tr>\n                          <td>结论</td>\n                          <td colspan=\"4\"></td>\n                        </tr>\n                        <tr>\n                          <td>备注</td>\n                          <td colspan=\"4\"></td>\n                        </tr>\n                        </tbody>-->\n          </div>\n        </div>\n      </div>\n      <div slot=\"footer\">\n        <el-button @click=\"closeYlDialog\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  //引入jquery,暂时没用\n  import $ from \"jquery\"\n  import {\n    addMbGlxmBatchToMbxm,\n    getPageDataListTosymb,\n    getSymbGlsyxmDataListByPage,\n    getXmLiraryData,\n    remove,\n    saveOrUpdate,\n    getMbGlMpinfoData,\n    getMbGlXmAndBw\n  } from '@/api/dagangOilfield/bzgl/symbwh'\n  import {getDeviceClassTreeNodeByPid} from '@/api/dagangOilfield/bzgl/sblxwh/sblxwh'\n  import {getGlSyzxmDataListByPage} from '@/api/dagangOilfield/bzgl/syxm'\n  import Glsymp from '@/views/dagangOilfield/bzgl/sybzk/glsymp'\n  import symbwhDymbnr from '@/views/dagangOilfield/bzgl/sybzk/symbwhDymbnr'\n  import htmlToPdf from '@/utils/print/htmlToPdf'\n\n  export default {\n    name: 'symbSyxmSelect',\n    components: {Glsymp, symbwhDymbnr},\n     props: {\n      //组件传值\n      symbData: {\n        type: Object,\n        default: () => ({\n          sblxid:'',  \n        })\n      },\n\n    },\n    data() {\n      return {\n        //基本信息表格数据\n        tableData_jbxx: [\n          {\n            'column_1': '试验性质',\n            'column_2': '试验日期',\n            'column_3': '试验人员',\n            'column_4': '试验地点',\n            'column_5': '',\n            'column_6': '',\n            'column_7': '',\n            'column_8': '',\n          },\n          {\n            'column_1': '报告日期',\n            'column_2': '编写人',\n            'column_3': '审核人',\n            'column_4': '批准人',\n            'column_5': '',\n            'column_6': '',\n            'column_7': '',\n            'column_8': '',\n          },\n          {\n            'column_1': '试验天气',\n            'column_2': '环境温度（℃）',\n            'column_3': '环境相对湿度（%）',\n            'column_4': '投运日期',\n            'column_5': '',\n            'column_6': '',\n            'column_7': '',\n            'column_8': '',\n          }\n        ],\n        //设备铭牌表格数据\n        tableData_sbmp: [{\n          'column_1': '额定电压',\n          'column_2': '设备型号',\n          'column_3': '',\n          'column_4': '',\n          'column_5': '',\n          'column_6': '',\n        },],\n        //要循环的试验表格数据\n        arr: [{\n          title: \"\",//试验名称\n          zxmList: [],//子项目数据（表头）\n          bwList: [],//部位数据（第一列开头）\n        }],\n        //下载弹出框控制\n        isShowDownLoadDialog: false,\n        printObj: {\n          id: \"previewId\", // 必填，渲染打印的内容使用\n          popTitle: \"&nbsp;\", //\n          previewTitle: \"&nbsp;\",\n          preview: false,\n        },\n        mbInfo: {},\n        //打印内容div中id值\n        previewId: \"\",\n        //定义模板内容弹出框传递参数\n        mbRowData: {},\n        //定义模板内容弹出框\n        isShowXmGlbwDialog: false,\n        xmSelectedForm: {\n          //试验模板id\n          symbid: undefined,\n          //试验项目数据集合\n          xmDataRows: []\n        },\n        //项目库弹出框标题\n        xmLibraryAddDialogTitle: '项目库',\n        //项目库弹出框控制\n        isShowAddGlxmDialog: false,\n        //项目库查询参数\n        xmLibraryQueryForm: {\n          symbid: undefined,\n          syxmmc: '',\n          pageNum: 1,\n          pageSize: 10\n        },\n        //项目库数据\n        xmLibraryDataList: [],\n        //项目库项目总数\n        xmLibraryTotal: 0,\n        //表单验证\n        mbzbRules: {\n          mbmc: [\n            {required: true, message: '请输入模板名称', trigger: 'blur'}\n          ]\n        },\n        // 筛选条件\n        filterInfo: {\n          data: {\n            mbmc: ''\n          },\n          fieldList: [\n            {label: '模板名称', type: 'input', value: 'mbmc', multiple: true}\n          ]\n        },\n        //新增按钮控制\n        addDisabled: true,\n        //删除选择列\n        selectRows: [],\n        //选中的单条对象\n        selectRowData: {},\n        //弹出框表单\n        form: {},\n        //查询试验部位参数\n        querySyBwParam: {\n          sblxid: undefined,\n          mbmc: undefined,\n          pageNum: 1,\n          pageSize: 10\n        },\n        //点击树节点赋值\n        treeForm: {},\n        //试验部位列表\n        tableAndPageInfo: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          tableData: [],\n          tableHeader: [\n            {label: '模板名称', prop: 'mbmc', minWidth: '100'},\n            {label: '是否默认', prop: 'sfmr', minWidth: '100'},\n            {label: '是否停用', prop: 'sfty', minWidth: '100'},\n          ],\n          option: {checkBox: true, serialNumber: true}\n        },\n        //组织树\n        treeOptions: [],\n\n        isShowDetails: false,\n        title: '',\n\n        //删除是否可用\n        multipleSensor: true,\n        //查询参数\n        queryParams: {\n          bm: undefined,\n          pageNum: 1,\n          pageSize: 10\n        },\n        isDisabled: false,\n        isShow: true,\n\n        //关联项目弹出框title\n        glxmDialogTitle: '关联项目',\n        //关联项目弹出框控制展开\n        isGlxmDialogShow: false,\n\n        //关联项目total\n        glxmTotal: 0,\n        //关联项目查询参数\n        glxmQueryParams: {\n          symbid: undefined,\n          pageNum: 1,\n          pageSize: 10\n        },\n\n        //关联子项目total\n        glzxmTotal: 0,\n        //关联子项目查询参数\n        glzxmQueryParams: {\n          syxmid: undefined,\n          pageSize: 10,\n          pageNum: 1\n        },\n\n        //模板关联项目数据\n        mbGlxmDataList: [],\n        //项目关联的子项目数据\n        zxmGlmbDataList: [],\n        //模板关联项目选中框数据\n        selectedRowDataChange: [],\n        //显示铭牌弹框\n        showMpDialog: false,\n        //选中行数据\n        rowData: {},\n\n        //关联名牌便利\n        mpList: [],\n\n        //试验数据\n        sysjDataList: [],\n        //试验表格默认固定的行\n        defaultRow: [\"仪器型号\", \"结论\", \"备注\"],\n      }\n    },\n    watch: {},\n    created() {\n      //获取数据列表\n      this.getData()\n    },\n    mounted() {\n    },\n    methods: {\n      //试验数据表格合并方法\n      arraySpanMethod({row, column, rowIndex, columnIndex}) {\n        if (this.defaultRow.includes(row.SYBW)) {\n          if (columnIndex > 0) {\n            return [1, row.totalNum]\n          }\n        }\n      },\n      //设备铭牌表格合并方法\n      sbmpSpanMethod({row, column, rowIndex, columnIndex}) {\n        if (columnIndex > 3) {\n          return [1, 2]\n        }\n      },\n      //模板详情按钮\n      handleMbInfo(row) {\n        //获取当前模板id加载页面信息\n        this.getMbGlMpinfoData(row);\n        //获取试验数据\n        this.getMbGlXmAndBw(row);\n      },\n      //导出pdf操作\n      downloadPdf() {\n        htmlToPdf.downloadPDF(document.querySelector('#printContentId'), this.mbInfo.mbmc)\n      },\n      //获取当前模板id加载页面信息\n      getMbGlMpinfoData(param) {\n        getMbGlMpinfoData(param).then(res => {\n          this.mpList = res.data;\n          //调用渲染铭牌页面开始\n          // this.applyMpHtml(this.mpList, param);\n          //打开弹出框\n          this.isShowDownLoadDialog = true;\n        })\n\n      },\n      //获取试验数据\n      getMbGlXmAndBw(rowData) {\n        //每次获取数据前先清空，再添加，否则多次进入页面时会获取重复数据\n        this.sysjDataList = [];\n        getMbGlXmAndBw(rowData).then(res => {\n          let resMap = res.data;\n          //遍历返回结果\n          for (let key in resMap) {\n            //解析试验数据\n            this.analysisSyData(key, resMap[key]);\n          }\n          //画试验数据页面\n          this.applySysjDataToHtml();\n        })\n      },\n      //解析后台试验数据\n      analysisSyData(syxmmc, zxmAndBwData) {\n        let sysjData = {}\n        sysjData.syxmmc = syxmmc;\n        for (let key in zxmAndBwData[0]) {\n          sysjData[key] = zxmAndBwData[0][key]\n        }\n        this.sysjDataList.push(sysjData);\n      },\n      //渲染实验数据到页面\n      applySysjDataToHtml() {\n        this.arr = [];\n        // $('#sysjTableId').html(\"\");\n        //进行数据处理重组\n        let data = this.sysjDataList;\n        if (data.length > 0) {\n          for (let i = 0; i < data.length; i++) {\n            let dataChild = data[i];\n            let title = dataChild.syxmmc;//试验项目名称\n            let bwList = dataChild.bwList; //部位list\n            let zxmList = dataChild.zxmList; //子项目list\n            let hx = [\n              {\n                \"label\": title, //第一个表头为试验项目名称\n                \"column_name\": \"SYBW\", //第一列对应的字段名（试验部位）\n              },\n            ];\n            zxmList.forEach(zxm => {\n              hx.push({\n                \"label\": zxm.syzxmmc, //每列的表头\n                \"column_name\": \"\", //每列对应的数值暂时设置为空白\n              })\n            })\n            let sx = [];\n            bwList.forEach(bw => {\n              sx.push({\n                \"SYBW\": bw.SYBW,\n                \"totalNum\": zxmList.length//记录总列数，用于固定行的合并事件\n              })\n            })\n            //后四行固定\n            sx.push(\n              {\n                \"SYBW\": \"结果\",\n                \"totalNum\": zxmList.length//记录总列数，用于固定行的合并事件\n              },\n              {\n                \"SYBW\": \"仪器型号\",\n                \"totalNum\": zxmList.length//记录总列数，用于固定行的合并事件\n              },\n              {\n                \"SYBW\": \"结论\",\n                \"totalNum\": zxmList.length//记录总列数，用于固定行的合并事件\n              },\n              {\n                \"SYBW\": \"备注\",\n                \"totalNum\": zxmList.length//记录总列数，用于固定行的合并事件\n              }\n            )\n            this.arr.push({\n              title: title,\n              zxmList: hx,\n              bwList: sx,\n            });\n          }\n        }\n        //拼接的铭牌表格\n        /*     let str = \"\";\n             if (this.sysjDataList.length > 0) {\n               for (let i = 0; i < this.sysjDataList.length; i++) {\n                 //拼接项目序号\n                 let xmIndex = i + 1;\n                 str += \"<tr><td colspan='5' style='text-align: left;font-weight: bold;font-size: 15px'>\" + xmIndex + \"、\" + this.sysjDataList[i].syxmmc + \"</td></tr>\";\n                 // this.sysjDataList[i].bwList;\n                 // this.sysjDataList[i].zxmList;\n                 // str += \"<tr><td>\"+this.sysjDataList[i].syxmmc+\"</td><td v-for=item in this.sysjDataList[i].bwList></td></tr>\"\n\n               }\n               this.$nextTick(() => {\n                 $('#sysjTableId').append(str)\n               })\n             }*/\n      },\n      //渲染铭牌页面开始mpList:反回的铭牌列表  row：模板行对象\n      applyMpHtml(mpList, row) {\n        //每次打开需要重新渲染一次,先将置空\n        $('#sbmpTbodyId').html(\"\");\n        //清空重新赋值\n        this.mbInfo = {}\n        this.mbInfo.mbmc = row.mbmc;\n        //拼接的铭牌表格\n        let str = \"\";\n        //先判断是否分相铭牌\n        if (mpList.length > 0) {\n          if (mpList[0].SFFX == '1') { //当前铭牌为分相铭牌时\n            //写死第一行\n            str += \"<tr><td style='padding: 10px;font-size: 15px;'>相别</td>\" +\n              \"<td style='padding: 10px;font-size: 15px;'>A</td>\" +\n              \"<td style='padding: 10px;font-size: 15px;'>B</td>\" +\n              \"<td style='padding: 10px;font-size: 15px;'>C</td>\" +\n              \"</tr>\";\n            //开始遍历展示\n            for (let a = 0; a < mpList.length; a++) {\n              str += \"<tr>\"\n              str += \"<td style='padding: 10px;font-size: 15px;'>\";\n              str += mpList[a].title + \"</td>\";\n              str += \"<td></td>>\"\n              str += \"<td></td>>\"\n              str += \"<td></td>>\"\n              str += \"</tr>\"\n            }\n          } else {  //铭牌不分相\n            //当前铭牌不属于分相铭牌\n            //每列展示单元格数量\n            let col = 3;\n            //展示行数\n            var lines = Math.ceil(mpList.length / col);\n            //遍历展示行数\n            for (var i = 0; i < lines; i++) {\n              str += \"<tr>\";\n              //遍历列\n              for (var j = 0; j < col; j++) {\n                if (i * col + j < mpList.length) {\n                  str += \"<td style='padding: 10px;font-size: 15px;'>\";\n                  //铭牌标题赋值\n                  str += mpList[i * col + j].title + \"</td>\";\n                  //铭牌值赋值\n                  str += mpList[i * col + j].sfmb == 1 ? \"<td> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>\" : \"<td>\" + mpList[i * col + j].column_name + \"</td>\"\n                }\n              }\n              str += \"</tr>\";\n            }\n          }\n        }\n        //渲染铭牌页面\n        this.$nextTick(() => {\n          $('#sbmpTbodyId').append(str)\n        })\n        //打开弹出框\n        this.isShowDownLoadDialog = true;\n      },\n      //关闭预览弹出框\n      closeYlDialog() {\n        //清空表单\n        this.mbInfo = {};\n        //赋值完关闭弹窗\n        this.isShowDownLoadDialog = false;\n      }\n      ,\n      //定义模板内容\n      handleClickMbnr(row) {\n        //打开组件弹出框\n        this.isShowXmGlbwDialog = true;\n        //给子组件传递数据\n        this.mbRowData = row;\n      }\n      ,\n      //获取项目库项目数据\n      getXmLiraryData() {\n        getXmLiraryData(this.xmLibraryQueryForm).then(res => {\n          this.xmLibraryDataList = res.data.records\n          this.xmLibraryTotal = res.data.total\n        })\n      }\n      ,\n      //项目弹出框新增按钮\n      addMbGlXm() {\n        this.getXmLiraryData()\n        this.isShowAddGlxmDialog = true\n      }\n      ,\n      //项目库弹出框取消按钮\n      closeAddMjzDialog() {\n        this.isShowAddGlxmDialog = false\n      }\n      ,\n      //项目库弹窗确认按钮\n      commitAddMjzForm() {\n        if (this.xmSelectedForm.xmDataRows.length < 1) {\n          this.$message.info('未关联项目！！！已取消')\n          //如果未选中数据,则直接关闭弹窗\n          this.isShowAddGlxmDialog = false\n        } else {\n          console.log(this.xmSelectedForm)\n          //若选择数据后\n          addMbGlxmBatchToMbxm(this.xmSelectedForm).then(res => {\n            if (res.code === '0000') {\n              this.$message.success('关联成功')\n            } else {\n              this.$message.error('关联失败！！')\n            }\n            //关闭弹窗\n            this.isShowAddGlxmDialog = false\n            //调用获取关联子项目列表\n            this.getSymbGlsyxmDataListByPage()\n          })\n        }\n      }\n      ,\n      //项目库行选中事件\n      handleSelectedXmLibraryChange(rows) {\n        this.xmSelectedForm.xmDataRows = rows\n      }\n      ,\n      //项目库查询按钮\n      selectxmLibrary() {\n        this.getXmLiraryData()\n      }\n      ,\n      //项目库重置按钮\n      resetxmSearch() {\n        this.xmLibraryQueryForm.syxmmc = ''\n        this.getXmLiraryData()\n      }\n      ,\n      //获取关联子列表方法\n      getZxmDataList() {\n        getGlSyzxmDataListByPage(this.glzxmQueryParams).then(res => {\n          this.glzxmTotal = res.data.total\n          this.zxmGlmbDataList = res.data.records\n        })\n      }\n      ,\n\n      //关联项目\n      handleClickGlxm(row) {\n        //清空原来子项目数据\n        this.zxmGlmbDataList = []\n        //打开关联项目弹出框\n        this.isGlxmDialogShow = true\n        //给参数赋值\n        this.glxmQueryParams.symbid = row.objId\n        //查询项目库数据时参数\n        this.xmLibraryQueryForm.symbid = row.objId\n        //给试验项目库添加时使用\n        this.xmSelectedForm.symbid = row.objId\n        //获取模板关联项目数据\n        this.getSymbGlsyxmDataListByPage()\n      }\n      ,\n      //获取关联项目弹出框数据\n      getSymbGlsyxmDataListByPage() {\n        getSymbGlsyxmDataListByPage(this.glxmQueryParams).then(res => {\n          this.mbGlxmDataList = res.data.records\n          this.glxmTotal = res.data.total\n        })\n      }\n      ,\n      //试验项目复选框点击时间点击操作\n      handleGlxmSelectedChange(rows) {\n        this.selectedRowDataChange = rows\n      }\n      ,\n      //删除模板关联项目\n      deleteMbGlXm() {\n        if (this.selectedRowDataChange.length < 1) {\n          this.$message.warning('请选择正确的数据！！！')\n          return\n        }\n        let ids = this.selectedRowDataChange.map(item => {\n          return item.objId\n        })\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove(ids).then(({code}) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getSymbGlsyxmDataListByPage()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n              this.getSymbGlsyxmDataListByPage()\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n      //试验项目点击行数据时的单机操作\n      handleMbGlxmRowClick(row) {\n        this.glzxmQueryParams.syxmid = row.syxmid\n        this.getZxmDataList()\n      },\n      //懒加载函数\n      loadNode(node, resolve) {\n        let TreeparamMap = {\n          pid: '',\n          spbLogo: ['输电设备', '变电设备','配电设备']\n        }\n        if (node.level === 0) {\n          TreeparamMap.pid = 'sb'\n          return this.getTreeNode(TreeparamMap, resolve)\n        }\n        setTimeout(() => {\n          TreeparamMap.pid = node.data.code\n          this.getTreeNode(TreeparamMap, resolve)\n        }, 500)\n\n      },\n      //获取树节点数据\n      getTreeNode(paramMap, resolve) {\n        getDeviceClassTreeNodeByPid(paramMap).then(res => {\n          let treeNodes = []\n          res.data.forEach(item => {\n            let node = {\n              name: item.name,\n              level: item.level,\n              id: item.id,\n              pid: item.pid,\n              leaf: false,\n              code: item.code\n            }\n            treeNodes.push(node)\n          })\n          resolve(treeNodes)\n        })\n      },\n      //添加后确认保存按钮\n      save() {\n        this.$refs['form'].validate((valid) => {\n          if (valid) {\n            saveOrUpdate(this.form).then(res => {\n              if (res.code === '0000') {\n                this.$message.success(res.msg)\n                this.tableAndPageInfo.pager.pageResize = 'Y'\n                this.getData()\n                this.isShowDetails = false\n              } else {\n                this.$message.error(res.msg)\n              }\n            })\n\n          }\n        })\n      },\n      //树节点点击事件\n      handleNodeClick(data) {\n        console.log('树节点点击')\n        console.log(data)\n        if (data.level != '0' && data.level != '1') {\n          //新增按钮可点击\n          this.addDisabled = false\n          this.treeForm = data\n          this.querySyBwParam.sblxid = data.code\n          this.getData()\n        } else {\n          this.addDisabled = true\n        }\n      },\n      //添加按钮\n      addSensorButton() {\n        this.form = {}\n        this.form.sblx = this.treeForm.name\n        this.form.sblxid = this.treeForm.code\n        this.isShowDetails = true\n        this.title = '新增'\n      },\n      //列表查询\n      async getData(params) {\n        try {\n          const param = {...this.querySyBwParam, ...params}\n          param.sblxid=this.symbData.sblxid;\n          const {data, code} = await getPageDataListTosymb(param)\n          if (code === '0000') {\n            this.tableAndPageInfo.tableData = data.records\n            this.tableAndPageInfo.pager.total = data.total\n          }\n        } catch (e) {\n          console.log(e)\n        }\n      }\n      ,\n      /**\n       * 表格多选框\n       */\n      handleSelectionChange(rows) {\n        this.selectRows = rows;\n      },\n\n      close() {\n        this.isShowDetails = false\n      }\n      ,\n      //修改模板主表内容\n      updateDetails(row) {\n        this.title = '修改'\n        this.isShowDetails = true\n        this.form = row\n        this.isDisabled = false\n        this.isShow = true\n      }\n      ,\n\n      createTemplate(row) {\n        console.log(row)\n      }\n      ,\n      //查看模板主表详情按钮\n      getDetails(row) {\n        this.title = '详情'\n        this.isShowDetails = true\n        this.form = row\n        this.isDisabled = true\n        this.isShow = false\n      }\n      ,\n\n      //删除按钮\n      deleteSensorButton() {\n        if (this.selectRows.length < 1) {\n          this.$message.warning('请选择正确的数据！！！')\n          return\n        }\n        let ids = this.selectRows.map(item => {\n          return item.objId\n        })\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove(ids).then(({code}) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.tableAndPageInfo.pager.pageResize = 'Y'\n              this.getData()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n              this.tableAndPageInfo.pager.pageResize = 'Y'\n              this.getData()\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n\n      }\n      ,\n      //导出按钮\n      handleExport() {\n\n      }\n      ,\n\n      //关联铭牌点击事件\n      handleClickGlMp(row) {\n        this.showMpDialog = true\n        this.rowData = row\n      }\n      ,\n      //关闭试验铭牌弹窗\n      closeMpDialog() {\n        this.showMpDialog = false\n      }\n      ,\n\n      filterReset() {\n      },\n\n      //关闭试验模板弹窗\n      closeSymbComment() {\n        this.$emit(\"closeSymbSelectDialog\", false)\n      },\n      //点击确认后给父组件传递数据\n      // this.selectRowData != undefined && JSON.stringify(this.selectRowData) != \"{}\"\n      commitMbdata() {\n        if (this.selectRows.length == 1 && this.selectRows != undefined) {\n         this.$emit(\"handleAcceptMbData\",this.selectRows[0]);\n          this.$emit(\"closeSymbSelectDialog\", false)\n        } else {\n          this.$message.warning(\"请选择一条数据\")\n        }\n      },\n\n    }\n  }\n</script>\n\n<style lang='scss' scoped>118\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n/*导出pdf格式设置开始*/\n#printContentId {\n  background-color: #fff;\n  width: 100%;\n  /* height: 400px; */\n  margin: auto;\n  padding: 16px;\n  box-sizing: border-box;\n  //试验数据样式\n  .printTitle {\n    text-align: left;\n    line-height: 40px;\n    border-left: 1px solid #000;\n    border-right: 1px solid #000;\n    //border-bottom: 1px solid #000;\n    padding-left: 10px;\n  }\n}\n\n//修改table表头颜色\n/deep/ #printContentId .el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th {\n  background: #fff;\n  border-color: #000;\n  font-weight: inherit;\n}\n\n/deep/ #printContentId .el-table--enable-row-transition .el-table__body td {\n  border-color: #000;\n}\n\n.table_style td, th {\n  padding: 10px;\n  font-size: 15px;\n}\n\n.table_style {\n  border-collapse: collapse;\n  width: 100%;\n  text-align: center;\n  /* border-bottom: 0;\n   border-left: 0;\n   border-right: 0;*/\n}\n\n/* 设置滚动条的样式 */\n::-webkit-scrollbar {\n  width: 12px;\n}\n\n/* 滚动槽 */\n::-webkit-scrollbar-track {\n  //-webkit-box-shadow:inset006pxrgba(0,0,0,0.3);\n  border-radius: 10px;\n}\n\n/* 滚动条滑块 */\n::-webkit-scrollbar-thumb {\n  border-radius: 10px;\n  background: rgba(0, 0, 0, 0.1);\n  //-webkit-box-shadow:gba(0,0,0,0.5);\n}\n\n::-webkit-scrollbar-thumb:window-inactive {\n  background: rgba(0, 0, 0, 0.1);\n}\n\n[data-v-67a974b1]::-webkit-scrollbar {\n  width: 8px;\n}\n\n.item {\n  width: 225px;\n  float: left;\n}\n\n/*导出pdf格式设置结束*/\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  max-height: 79vh;\n  overflow: auto;\n}\n\n.app-container {\n  padding: 3px;\n}\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk/sybgjlcomment"}]}