{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\bddzcz\\components\\czp_cx.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\bddzcz\\components\\czp_cx.vue", "mtime": 1749014418040}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["czp_cx.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AA2YA;;AACA;;AAOA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,UADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,aAAA,EAAA;AAAA,GAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA;AACA,MAAA,SAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,CAFA;AAMA,MAAA,OAAA,EAAA,EANA;AAOA,MAAA,OAAA,EAAA,EAPA;AAQA,MAAA,OAAA,EAAA,EARA;AASA;AACA,MAAA,mBAAA,EAAA,EAVA;AAWA,MAAA,OAAA,EAAA,KAXA;AAYA,MAAA,OAAA,EAAA,EAZA;AAaA,MAAA,EAAA,EAAA,KAbA;AAcA;AACA,MAAA,cAAA,EAAA,EAfA;AAgBA;AACA,MAAA,gBAAA,EAAA,KAjBA;AAkBA;AACA,MAAA,aAAA,EAAA;AACA,QAAA,UAAA,EAAA,EADA,CACA;;AADA,OAnBA;AAsBA;AACA,MAAA,MAAA,EAAA,EAvBA;AAwBA;AACA,MAAA,OAAA,EAAA,EAzBA;AA0BA,MAAA,OAAA,EAAA,EA1BA;AA2BA;AACA,MAAA,GAAA,EAAA,EA5BA;AA6BA;AACA,MAAA,aAAA,EAAA;AACA,QAAA,GAAA,EAAA,IADA;AACA;AACA,QAAA,QAAA,EAAA;AAFA,OA9BA;AAkCA;AACA,MAAA,IAAA,EAAA;AACA,QAAA,EAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA,EAJA;AAKA,QAAA,IAAA,EAAA,EALA;AAMA,QAAA,GAAA,EAAA,EANA;AAOA,QAAA,GAAA,EAAA,EAPA;AAQA,QAAA,GAAA,EAAA,EARA;AASA,QAAA,GAAA,EAAA,EATA;AAUA,QAAA,EAAA,EAAA,CAVA;AAUA;AACA,QAAA,QAAA,EAAA;AAXA,OAnCA;AAgDA;AACA,MAAA,aAAA,EAAA,KAjDA;AAkDA;AACA,MAAA,UAAA,EAAA,KAnDA;AAoDA;AACA,MAAA,KAAA,EAAA,EArDA;AAsDA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,EAAA,EAAA,EADA;AAEA,UAAA,GAAA,EAAA,EAFA;AAGA,UAAA,KAAA,EAAA,EAHA;AAIA,UAAA,OAAA,EAAA,EAJA;AAKA,UAAA,GAAA,EAAA,EALA;AAMA,UAAA,IAAA,EAAA,EANA;AAOA,UAAA,KAAA,EAAA,EAPA;AAQA,UAAA,KAAA,EAAA,EARA;AASA,UAAA,OAAA,EAAA;AATA,SADA;AAWA;AACA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SADA,EAEA;AACA,UAAA,KAAA,EAAA,KADA;AAEA,UAAA,KAAA,EAAA,KAFA;AAGA,UAAA,IAAA,EAAA,QAHA;AAIA,UAAA,SAAA,EAAA,IAJA;AAKA,UAAA,OAAA,EAAA;AALA,SAFA,EASA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,KAAA,EAAA,OAFA;AAGA,UAAA,IAAA,EAAA,QAHA;AAIA,UAAA,SAAA,EAAA,IAJA;AAKA,UAAA,OAAA,EAAA;AALA,SATA,EAgBA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SAhBA,EAiBA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,SAFA;AAGA,UAAA,IAAA,EAAA,MAHA;AAIA,UAAA,QAAA,EAAA,WAJA;AAKA,UAAA,MAAA,EAAA;AALA,SAjBA,EAwBA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SAxBA,EAyBA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SAzBA,EA0BA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SA1BA,EA2BA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SA3BA,CA4BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAxCA;AAZA,OAtDA;AA6GA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA;AACA,QAAA,SAAA,EAAA,EARA;AASA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SARA,EASA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SATA,EAUA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,SAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAVA,EAWA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAXA,EAYA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,IAAA,EAAA,SAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAZA,EAaA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,IAAA,EAAA,SAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAbA,EAcA;AACA,UAAA,IAAA,EAAA,WADA;AAEA,UAAA,KAAA,EAAA,IAFA;AAGA,UAAA,QAAA,EAAA,MAHA;AAIA,UAAA,KAAA,EAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WAJA;AAKA;AACA,UAAA,KAAA,EAAA,OANA;AAOA,UAAA,SAAA,EAAA,CAAA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WAAA;AAPA,SAdA,CATA;AAiCA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAjCA,OA7GA;AAgJA,MAAA,MAAA,EAAA;AACA;AACA,QAAA,EAAA,EAAA,CAFA;AAGA;AACA,QAAA,MAAA,EAAA,GAJA;AAKA,QAAA,OAAA,EAAA,CAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAAA;AALA;AAhJA,KAAA;AAwJA,GA5JA;AA6JA,EAAA,OA7JA,qBA6JA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBACA,KAAA,CAAA,aAAA,CAAA,EAAA,EAAA,EAAA,CADA;;AAAA;AACA,cAAA,KAAA,CAAA,OADA;AAAA;AAAA,qBAEA,KAAA,CAAA,aAAA,CAAA,EAAA,EAAA,EAAA,CAFA;;AAAA;AAEA,cAAA,KAAA,CAAA,OAFA;AAAA;AAAA,qBAGA,KAAA,CAAA,aAAA,CAAA,EAAA,EAAA,EAAA,CAHA;;AAAA;AAGA,cAAA,KAAA,CAAA,OAHA;AAIA;AACA,cAAA,KAAA,CAAA,MAAA,CAAA,KAAA,GAAA,qBAAA;;AACA,cAAA,KAAA,CAAA,OAAA,CAAA;AAAA,gBAAA,KAAA,EAAA,KAAA,CAAA;AAAA,eAAA;;AACA,cAAA,KAAA,CAAA,aAAA,GAPA,CAQA;;;AACA,cAAA,KAAA,CAAA,gBAAA;;AACA,kBAAA,YAAA,KAAA,CAAA,MAAA,CAAA,OAAA,CAAA,IAAA,EAAA;AACA,gBAAA,MADA,GACA;AAAA,kBAAA,IAAA,EAAA,IAAA;AAAA,kBAAA,QAAA,EAAA,KAAA,CAAA;AAAA,iBADA;;AAEA,gBAAA,KAAA,CAAA,gBAAA,CAAA,WAAA,CAAA,EAAA,EAAA,SAAA,CAAA,IAAA,CAAA,MAAA;AACA;;AAbA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA,GA3KA;AA4KA,EAAA,OAAA,EAAA;AACA;;;AAGA,IAAA,aAJA,2BAIA;AAAA;;AACA,+BAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAAA,QAAA,EAAA;AACA,SAFA;AAGA,QAAA,MAAA,CAAA,mBAAA,GAAA,GAAA,CAAA,IAAA;;AACA,QAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,IAAA,KAAA,EAAA;AACA,mBAAA,IAAA,CAAA,OAAA,GAAA,MAAA,CAAA,mBAAA;AACA;AACA,SAJA;AAKA,OAVA;AAWA,KAhBA;AAiBA;AACA,IAAA,SAlBA,uBAkBA;AACA,qCAAA,KAAA,MAAA,EAAA,OAAA;AACA,KApBA;AAqBA;AACA,IAAA,SAtBA,qBAsBA,GAtBA,EAsBA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAKA,IALA,CAKA,YAAA;AACA,uCAAA,GAAA,CAAA,KAAA,EAAA,IAAA,CAAA,gBAAA;AAAA,wBAAA,IAAA,QAAA,IAAA;;AACA,wBAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,SADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA,EADA,CAKA;;;AACA,sBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,sBAAA,MAAA,CAAA,OAAA;AACA,qBARA,MAQA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,OADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;AAIA;AACA,mBAfA;AAgBA,iBAtBA,EAuBA,KAvBA,CAuBA,YAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA;AACA,oBAAA,IAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;AAIA,iBA5BA;;AADA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA8BA,KApDA;AAqDA,IAAA,aArDA,yBAqDA,aArDA,EAqDA,MArDA,EAqDA;AACA,aAAA,yBAAA;AACA,QAAA,aAAA,EAAA,aADA;AAEA,QAAA,MAAA,EAAA,MAFA;AAGA,QAAA,QAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,UAAA,GAAA,EAAA;AACA,eAAA,GAAA,CAAA,IAAA;AACA,OANA,CAAA;AAOA,KA7DA;AA8DA;AACA,IAAA,cA/DA,4BA+DA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,WAAA,EAAA,GAAA,IAAA;AACA,KAlEA;AAmEA;AACA,IAAA,YApEA,wBAoEA,IApEA,EAoEA,QApEA,EAoEA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,KAtEA;AAuEA,IAAA,cAvEA,0BAuEA,KAvEA,EAuEA,IAvEA,EAuEA,QAvEA,EAuEA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,KAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,MAAA,EAAA,IAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,QAAA;AACA,KA3EA;AA4EA;AACA,IAAA,YA7EA,wBA6EA,IA7EA,EA6EA,QA7EA,EA6EA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,KA/EA;AAgFA;AACA,IAAA,wBAjFA,oCAiFA,IAjFA,EAiFA;AACA,WAAA,cAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA;AACA,WAAA,gBAAA,GAAA,IAAA;AACA,KApFA;AAqFA,IAAA,OArFA,qBAqFA;AACA,WAAA,gBAAA,GAAA,KAAA;AACA,KAvFA;AAwFA;AACA,IAAA,OAzFA,mBAyFA,MAzFA,EAyFA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,MAAA,CAAA,MAAA,+DAAA,MAAA,CAAA,MAAA,GAAA,MAAA;AACA,gBAAA,MAAA,CAAA,OAAA,GAAA,IAAA;AAHA;AAAA,uBAIA,yBAAA,MAAA,CAAA,MAAA,CAJA;;AAAA;AAAA;AAIA,gBAAA,IAJA,qBAIA,IAJA;AAIA,gBAAA,IAJA,qBAIA,IAJA;;AAKA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA,kBAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA;;AATA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KApGA;AAqGA;AACA,IAAA,QAtGA,sBAsGA,CAAA,CAtGA;AAuGA;AACA,IAAA,qBAxGA,mCAwGA,CAAA,CAxGA;;AAyGA;;;AAGA,IAAA,QA5GA,oBA4GA,GA5GA,EA4GA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAEA,2BAAA;AAAA,kBAAA,KAAA,EAAA,GAAA,CAAA;AAAA,iBAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,uBAEA,IAFA;AAEA,gBAAA,IAFA,uBAEA,IAFA;;AAGA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,aAAA,CAAA,QAAA,GAAA,IAAA;AACA;;AALA;AAAA;;AAAA;AAAA;AAAA;AAOA,gBAAA,OAAA,CAAA,GAAA;;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,KArHA;AAuHA;AACA,IAAA,UAxHA,sBAwHA,GAxHA,EAwHA;AACA,WAAA,QAAA,CAAA,GAAA;AACA,WAAA,KAAA,GAAA,WAAA;AACA,WAAA,IAAA,mCAAA,GAAA,EAHA,CAIA;;AACA,WAAA,OAAA,GAAA,KAAA,IAAA,CAAA,OAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,KAhIA;AAiIA;AACA,IAAA,KAlIA,mBAkIA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,KApIA;;AAsIA;;;AAGA,IAAA,gBAzIA,8BAyIA;AAAA;;AACA,qCAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;;AACA,QAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,IAAA,OAAA,EAAA;AACA,mBAAA,IAAA,CAAA,OAAA,GAAA,MAAA,CAAA,OAAA;AACA;AACA,SAJA;AAKA,OAPA;AAQA,KAlJA;AAmJA;AACA,IAAA,WApJA,uBAoJA,GApJA,EAoJA;AAAA;;AACA;AACA,UAAA,GAAA,CAAA,KAAA,KAAA,KAAA,IAAA,GAAA,CAAA,KAAA,IAAA,GAAA,CAAA,KAAA,KAAA,EAAA,EAAA;AACA,YAAA,IAAA,GAAA;AACA,UAAA,MAAA,EAAA,GAAA,CAAA;AADA,SAAA;AAGA,uCAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,gBAAA,IAAA,CAAA,KAAA,IAAA,OAAA,EAAA;AACA,qBAAA,IAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;AACA;AACA,WAJA;AAKA,SANA;AAOA;AACA;AAlKA;AA5KA,C", "sourcesContent": ["<template>\n  <div>\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n      @handleReset=\"getReset\"\n      @handleEvent=\"handleEvent\"\n    />\n    <el-white class=\"button-group\">\n      <el-row class=\"pull-right button_btn\" :gutter=\"20\">\n        <el-col :gutter=\"4\" :span=\"1.5\">\n          <el-button type=\"primary\" @click=\"exportFun\">导出 </el-button>\n        </el-col>\n      </el-row>\n      <div>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          height=\"53vh\"\n          v-loading=\"loading\"\n        />\n      </div>\n    </el-white>\n\n    <!-- 详情/新增/修改 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\">\n        <div>\n          <el-row>\n            <el-col :span=\"8\">\n              <el-form-item label=\"编号：\" prop=\"bm\">\n                <el-input\n                  v-model=\"form.bm\"\n                  placeholder=\"确认后编号自动生成\"\n                  disabled\n                >\n                </el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"分公司：\" prop=\"fgs\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.fgs\"\n                  placeholder=\"请选择分公司\"\n                  disabled\n                >\n                  <el-option\n                    v-for=\"item in ssgsOptionsDataList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"变电站名称：\" prop=\"bdzmc\">\n                <el-select\n                  style=\"width: 100%\"\n                  filterable\n                  v-model=\"form.bdzmc\"\n                  disabled\n                  placeholder=\"请选择变电站\"\n                >\n                  <el-option\n                    v-for=\"item in bdzList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"下令人：\" prop=\"xlr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.xlr\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                  disabled\n                >\n                  <el-option\n                    v-for=\"item in xlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"审票人：\" prop=\"bzspr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.bzspr\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                  disabled\n                >\n                  <el-option\n                    v-for=\"item in sprList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"操作开始时间：\"\n                prop=\"kssj\"\n                label-width=\"140px\"\n              >\n                <el-date-picker\n                  disabled\n                  v-model=\"form.kssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"操作结束时间：\"\n                prop=\"jssj\"\n                label-width=\"140px\"\n              >\n                <el-date-picker\n                  disabled\n                  v-model=\"form.jssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作人：\" prop=\"czr\">\n                <el-input\n                  v-model=\"form.czr\"\n                  placeholder=\"请输入内容\"\n                  disabled\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"监护人：\" prop=\"jhr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.jhr\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                  disabled\n                >\n                  <el-option\n                    v-for=\"item in jlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作项数：\" prop=\"czxs\">\n                <el-input-number\n                  v-model=\"form.czxs\"\n                  placeholder=\"请输入操作项数\"\n                  disabled\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"已执行项数：\" prop=\"yzxczxs\">\n                <el-input-number\n                  v-model=\"form.yzxczxs\"\n                  placeholder=\"请输入已执行操作项数\"\n                  disabled\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"未执行项数：\" prop=\"wzxczxs\">\n                <el-input-number\n                  v-model=\"form.wzxczxs\"\n                  placeholder=\"请输入未执行操作项数\"\n                  disabled\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"是否已执行：\" prop=\"sfyzx\">\n                <el-select v-model=\"form.sfyzx\" placeholder=\"请选择\" disabled>\n                  <el-option\n                    v-for=\"item in sfyzxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item\n                label=\"未执行原因：\"\n                prop=\"wzxyy\"\n                v-if=\"form.sfyzx === '未执行'\"\n              >\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"form.wzxyy\"\n                  placeholder=\"请输入内容\"\n                  style=\"width: 100%\"\n                  disabled\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"操作任务：\" prop=\"czrw\">\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"form.czrw\"\n                  placeholder=\"请输入内容\"\n                  style=\"width: 100%\"\n                  disabled\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"上传图片：\">\n                <el-upload\n                  disabled\n                  action=\"\"\n                  ref=\"uploadImg\"\n                  accept=\"image/jpeg,image/jpg,image/png\"\n                  :headers=\"header\"\n                  :multiple=\"true\"\n                  :on-change=\"handleChange\"\n                  :data=\"uploadImgData\"\n                  :file-list=\"imgList\"\n                  :auto-upload=\"false\"\n                  list-type=\"picture-card\"\n                  :on-preview=\"handlePictureCardPreview\"\n                  :on-progress=\"handleProgress\"\n                  :on-remove=\"handleRemove\"\n                >\n                  <i class=\"el-icon-plus\"></i>\n                </el-upload>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n\n        <!--预览，查看全部操作项-->\n        <div>\n          <div align=\"left\">\n            <el-button type=\"info\" @click=\"handleYlChange\">预览</el-button>\n          </div>\n          <el-dialog :title=\"titleyl\" :visible.sync=\"yl\" append-to-body>\n            <!--第一个表格-->\n            <el-table\n              :data=\"propTableData.colFirst\"\n              ref=\"propTable\"\n              disabled\n              border\n              stripe\n              style=\"width: 100%\"\n              :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n            >\n              <el-table-column\n                align=\"center\"\n                width=\"120\"\n                prop=\"xh\"\n                label=\"顺序号\"\n                sortable\n              >\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"请输入顺序号\"\n                      disabled\n                      v-model=\"scope.row.xh\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"请输入操作项目\"\n                      disabled\n                      v-model=\"scope.row.czrw\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-dialog>\n        </div>\n\n        <!--列表-->\n        <div>\n          <!--第一个表格-->\n          <el-table\n            :data=\"propTableData.colFirst\"\n            ref=\"propTable\"\n            height=\"300\"\n            border\n            stripe\n            disabled\n            style=\"width: 100%\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <el-table-column\n              align=\"center\"\n              width=\"120\"\n              prop=\"xh\"\n              label=\"顺序号\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input-number\n                    size=\"small\"\n                    v-model=\"scope.row.xh\"\n                    :min=\"1\"\n                    :precision=\"0\"\n                    controls-position=\"right\"\n                    disabled\n                  ></el-input-number>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入操作项目\"\n                    v-model=\"scope.row.czrw\"\n                    disabled\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" width=\"80\" label=\"是否完成\">\n              <template slot-scope=\"scope\">\n                <el-checkbox v-model=\"scope.row.sfwc\" disabled></el-checkbox>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!--图片放大展示弹出框-->\n    <el-image-viewer\n      v-if=\"imgDialogVisible\"\n      :on-close=\"onClose\"\n      :url-list=\"dialogImageUrl\"\n      zIndex=\"3000\"\n    />\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport {\n  exportBdCzpExcel,\n  getBdzSelectList,\n  getCzpmxList,\n  getListLsp,\n  remove\n} from \"@/api/yxgl/bdyxgl/bddzczp\";\nimport { getUsers } from \"@/api/system/usergroup\";\nimport ElImageViewer from \"element-ui/packages/image/src/image-viewer\";\nimport { getFgsOptions } from \"@/api/yxgl/bdyxgl/zbgl\";\n\nexport default {\n  name: \"dzczcxtj\",\n  components: { ElImageViewer },\n  data() {\n    return {\n      // 是否已执行下拉框\n      sfyzxList: [\n        { label: \"已执行\", value: \"已执行\" },\n        { label: \"未执行\", value: \"未执行\" }\n      ],\n      jlrList: [],\n      xlrList: [],\n      sprList: [],\n      //所属分公司下拉框数据\n      ssgsOptionsDataList: [],\n      loading: false,\n      titleyl: \"\",\n      yl: false,\n      //图片地址url\n      dialogImageUrl: \"\",\n      //展示图片dialog控制\n      imgDialogVisible: false,\n      //上传图片时的携带的其他参数\n      uploadImgData: {\n        businessId: \"\" //携带的表单主键id\n      },\n      //上传图片时的请求头\n      header: {},\n      //图片list\n      imgList: [],\n      bdzList: [],\n      // 多选框选中的id\n      ids: [],\n      //弹出框中表格数据\n      propTableData: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //form表单\n      form: {\n        bh: \"\",\n        bdzmc: \"\",\n        kssj: \"\",\n        jssj: \"\",\n        czrw: \"\",\n        czr: \"\",\n        jhr: \"\",\n        xlr: \"\",\n        spr: \"\",\n        lx: 2, //变电\n        colFirst: []\n      },\n      //详情弹框是否显示\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          bh: \"\",\n          fgs: \"\",\n          bdzmc: \"\",\n          czsjArr: [],\n          czr: \"\",\n          czrw: \"\",\n          jhrmc: \"\",\n          xlrmc: \"\",\n          bzsprmc: \"\"\n        }, //查询条件\n        fieldList: [\n          { label: \"编号\", value: \"bm\", type: \"input\", clearable: true },\n          {\n            label: \"分公司\",\n            value: \"fgs\",\n            type: \"select\",\n            clearable: true,\n            options: []\n          },\n          {\n            label: \"变电站名称\",\n            value: \"bdzmc\",\n            type: \"select\",\n            clearable: true,\n            options: []\n          },\n          { label: \"操作任务\", value: \"czrw\", type: \"input\", clearable: true },\n          {\n            label: \"操作时间\",\n            value: \"czsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"操作人\", value: \"czr\", type: \"input\", clearable: true },\n          { label: \"监护人\", value: \"jhrmc\", type: \"input\", clearable: true },\n          { label: \"下令人\", value: \"xlrmc\", type: \"input\", clearable: true },\n          { label: \"审票人\", value: \"bzsprmc\", type: \"input\", clearable: true },\n          // { label: \"操作项数\", value: \"czxs\", type: \"input\", clearable: true },\n          // {\n          //   label: \"已执行项数\",\n          //   value: \"yzxczxs\",\n          //   type: \"input\",\n          //   clearable: true\n          // },\n          // {\n          //   label: \"未执行项数\",\n          //   value: \"wzxczxs\",\n          //   type: \"input\",\n          //   clearable: true\n          // }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        //{ czrw: '任务一', czr: '张三', dzxl: '中山路102号线', gzbz: 'pzl', zpbm: '部门一', whbz: '班组一' }\n        tableData: [],\n        tableHeader: [\n          { label: \"编号\", prop: \"bm\", minWidth: \"80\" },\n          { label: \"分公司\", prop: \"fgsmc\", minWidth: \"100\" },\n          { label: \"变电站名称\", prop: \"bdzmcs\", minWidth: \"130\" },\n          { label: \"操作任务\", prop: \"czrw\", minWidth: \"160\" },\n          { label: \"操作开始时间\", prop: \"kssj\", minWidth: \"130\" },\n          { label: \"操作结束时间\", prop: \"jssj\", minWidth: \"130\" },\n          { label: \"操作人\", prop: \"czr\", minWidth: \"70\" },\n          { label: \"监护人\", prop: \"jhrmc\", minWidth: \"70\" },\n          { label: \"下令人\", prop: \"xlrmc\", minWidth: \"70\" },\n          { label: \"审票人\", prop: \"bzsprmc\", minWidth: \"120\" },\n          { label: \"操作项数\", prop: \"czxs\", minWidth: \"80\" },\n          { label: \"已执行项数\", prop: \"yzxczxs\", minWidth: \"90\" },\n          { label: \"未执行项数\", prop: \"wzxczxs\", minWidth: \"90\" },\n          {\n            prop: \"operation\",\n            label: \"操作\",\n            minWidth: \"80px\",\n            style: { display: \"block\" },\n            //操作列固定再右侧\n            fixed: \"right\",\n            operation: [{ name: \"详情\", clickFun: this.getDetails }]\n          }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        //变电\n        lx: 2,\n        //用来区分历史票库，1-已办结，2-未办结\n        status: \"4\",\n        mySorts: [{ prop: \"kssj\", asc: false }]\n      }\n    };\n  },\n  async mounted() {\n    this.xlrList = await this.getGroupUsers(61, \"\");\n    this.sprList = await this.getGroupUsers(13, \"\");\n    this.jlrList = await this.getGroupUsers(62, \"\");\n    //获取token\n    this.header.token = getToken();\n    this.getData({ objId: this.qxjlObjId });\n    this.getFgsOptions();\n    //获取变电站下拉框数据\n    this.getBdzSelectList();\n    if (\"admin\" === this.$store.getters.name) {\n      let option = { name: \"删除\", clickFun: this.deleteRow };\n      this.tableAndPageInfo.tableHeader[13].operation.push(option);\n    }\n  },\n  methods: {\n    /**\n     * 获取分公司下拉数据\n     */\n    getFgsOptions() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.ssgsOptionsDataList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"fgs\") {\n            return (item.options = this.ssgsOptionsDataList);\n          }\n        });\n      });\n    },\n    //导出\n    exportFun() {\n      exportBdCzpExcel(this.params, \"操作票信息\");\n    },\n    //删除按钮\n    async deleteRow(row) {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove(row.objId).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    getGroupUsers(personGroupId, deptId) {\n      return getUsers({\n        personGroupId: personGroupId,\n        deptId: deptId,\n        deptName: \"\"\n      }).then(res => {\n        return res.data;\n      });\n    },\n    // 预览按钮\n    handleYlChange() {\n      this.titleyl = \"查看操作项目\";\n      this.yl = true;\n    },\n    // 选择文件时，往fileList里添加\n    handleChange(file, fileList) {\n      this.imgList = fileList;\n    },\n    handleProgress(event, file, fileList) {\n      console.log(\"event\", event);\n      console.log(\"file\", file);\n      console.log(\"fileList\", fileList);\n    },\n    //图片移除\n    handleRemove(file, fileList) {\n      this.imgList = fileList;\n    },\n    //图片放大\n    handlePictureCardPreview(file) {\n      this.dialogImageUrl = [file.url];\n      this.imgDialogVisible = true;\n    },\n    onClose() {\n      this.imgDialogVisible = false;\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params };\n        this.loading = true;\n        const { data, code } = await getListLsp(this.params);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          this.loading = false;\n        }\n      } catch (e) {}\n    },\n    //重置按钮\n    getReset() {},\n    //选中行\n    handleSelectionChange() {},\n    /**\n     *获取操作票明细\n     */\n    async getCzpmx(row) {\n      try {\n        const { data, code } = await getCzpmxList({ objId: row.objId });\n        if (code === \"0000\") {\n          this.propTableData.colFirst = data;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n\n    //详情按钮\n    getDetails(row) {\n      this.getCzpmx(row);\n      this.title = \"变电操作票详情查看\";\n      this.form = { ...row };\n      //图片结合\n      this.imgList = this.form.imgList;\n      this.isDisabled = true;\n      this.isShowDetails = true;\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false;\n    },\n\n    /**\n     * 获取变电站下拉框数据\n     */\n    getBdzSelectList() {\n      getBdzSelectList({}).then(res => {\n        this.bdzList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"bdzmc\") {\n            return (item.options = this.bdzList);\n          }\n        });\n      });\n    },\n    //下拉框change事件\n    handleEvent(val) {\n      //变电站查询下拉框选项根据所选分公司带出来\n      if (val.label === \"fgs\" && val.value && val.value !== \"\") {\n        let form = {\n          ssdwbm: val.value\n        };\n        getBdzSelectList(form).then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value == \"bdzmc\") {\n              return (item.options = res.data);\n            }\n          });\n        });\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.card-style {\n  background-color: #e0eff2;\n  line-height: 3vh;\n  border-radius: 3px;\n  font-weight: 800;\n  padding-left: 1vw;\n  margin-bottom: 3vh;\n}\n\n/*控制input输入框边框是否显示*/\n.elInput >>> .el-input__inner {\n  border: 0;\n}\n\n.center {\n  text-align: center; /*让div内部文字居中*/\n}\n</style>\n"], "sourceRoot": "src/views/dagangOilfield/czpgl/bddzcz/components"}]}