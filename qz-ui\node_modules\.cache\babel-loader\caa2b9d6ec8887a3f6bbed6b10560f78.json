{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\activiti_tzgl\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\activiti_tzgl\\index.vue", "mtime": 1706897321038}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAmIA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBACA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,KAAA,EAAA;AACA;;;;;;;;;;;AAWA,IAAA,WAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAFA,sBAEA;AACA,eAAA;AACA,UAAA,WAAA,EAAA,IADA;AAEA,UAAA,WAAA,EAAA;AAFA,SAAA;AAIA;AAPA,KAZA;AAqBA;AACA,IAAA,MAAA,EAAA;AACA,MAAA,IAAA,EAAA,OADA;AAEA,MAAA,OAAA,EAAA;AAFA,KAtBA;AA0BA;AACA,IAAA,MAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAFA,sBAEA;AACA,eAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAAA;AACA;AAJA;AA3BA,GAFA;AAoCA,EAAA,IApCA,kBAoCA;AACA,WAAA;AACA,MAAA,QAAA,EAAA,KADA;AAEA,MAAA,IAAA,EAAA,EAFA;AAGA,MAAA,KAAA,EAAA,EAHA;AAIA,MAAA,OAAA,EAAA,IAJA;AAKA,MAAA,cAAA,EAAA,EALA;AAMA,MAAA,cAAA,EAAA,EANA;AAOA,MAAA,YAAA,EAAA;AAPA,KAAA;AASA,GA9CA;AA+CA,EAAA,KAAA,EAAA;AACA,IAAA,WAAA,EAAA;AACA,MAAA,OADA,mBACA,MADA,EACA,MADA,EACA;AAAA;;AACA,aAAA,SAAA,CAAA,YAAA;AACA,UAAA,KAAA,CAAA,KAAA,mCAAA,MAAA;;AACA,kBAAA,KAAA,CAAA,KAAA,CAAA,MAAA;AACA,iBAAA,CAAA;AACA,qCAAA;AACA,gBAAA,IAAA,EAAA,KADA;AAEA,gBAAA,EAAA,EAAA;AAFA,eAAA,EAGA,IAHA,CAGA,UAAA,GAAA,EAAA;AACA,gBAAA,KAAA,CAAA,YAAA,GAAA,GAAA,CAAA,IAAA;AACA,eALA;AAMA;;AACA,iBAAA,CAAA;AACA,uCAAA;AAAA,gBAAA,aAAA,EAAA,EAAA;AAAA,gBAAA,MAAA,EAAA,EAAA;AAAA,gBAAA,QAAA,EAAA;AAAA,eAAA,EAAA,IAAA,CACA,UAAA,GAAA,EAAA;AACA,gBAAA,KAAA,CAAA,YAAA,GAAA,GAAA,CAAA,IAAA;AACA,eAHA;AAKA;;AACA,iBAAA,CAAA;AACA,uCAAA;AAAA,gBAAA,aAAA,EAAA,EAAA;AAAA,gBAAA,MAAA,EAAA,EAAA;AAAA,gBAAA,QAAA,EAAA;AAAA,eAAA,EAAA,IAAA,CACA,UAAA,GAAA,EAAA;AACA,gBAAA,KAAA,CAAA,YAAA,GAAA,GAAA,CAAA,IAAA;AACA,eAHA;AAKA;;AACA,iBAAA,CAAA;AACA,uCAAA;AAAA,gBAAA,aAAA,EAAA,GAAA;AAAA,gBAAA,MAAA,EAAA,EAAA;AAAA,gBAAA,QAAA,EAAA;AAAA,eAAA,EAAA,IAAA,CACA,UAAA,GAAA,EAAA;AACA,gBAAA,KAAA,CAAA,cAAA,GAAA,GAAA,CAAA,IAAA;AACA,eAHA;AAKA,uCAAA;AAAA,gBAAA,aAAA,EAAA,GAAA;AAAA,gBAAA,MAAA,EAAA,EAAA;AAAA,gBAAA,QAAA,EAAA;AAAA,eAAA,EAAA,IAAA,CACA,UAAA,GAAA,EAAA;AACA,gBAAA,KAAA,CAAA,cAAA,GAAA,GAAA,CAAA,IAAA;AACA,eAHA;AAKA;AAlCA;AAoCA,SAtCA;AAuCA,OAzCA;AA0CA,MAAA,IAAA,EAAA,IA1CA;AA2CA,MAAA,SAAA,EAAA;AA3CA;AADA,GA/CA;AA8FA,EAAA,OA9FA,qBA8FA,CAAA,CA9FA;AA+FA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,UAFA,wBAEA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,SAAA,GAAA,MAAA,CAAA,MAAA,CAAA,IAAA;;AACA,oBAAA,MAAA,CAAA,IAAA,CAAA,QAAA,EAAA;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,QAAA,GAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,QAAA;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,gBAAA,GAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,QAAA;AACA;;AAEA,oBAAA,MAAA,CAAA,IAAA,CAAA,SAAA,EAAA;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,SAAA,GAAA,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA,QAAA;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,iBAAA,GAAA,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA,QAAA;AACA;;AAVA,sBAWA,MAAA,CAAA,KAAA,CAAA,MAAA,KAAA,SAXA;AAAA;AAAA;AAAA;;AAAA,sBAYA,CAAA,MAAA,CAAA,KAAA,CAAA,QAAA,IAAA,CAAA,MAAA,CAAA,KAAA,CAAA,SAZA;AAAA;AAAA;AAAA;;AAaA,gBAAA,MAAA,CAAA,QAAA,CAAA;AACA,kBAAA,IAAA,EAAA,OADA;AAEA,kBAAA,OAAA,EAAA;AAFA,iBAAA;;AAbA;;AAAA;AAAA,sBAqBA,CAAA,MAAA,CAAA,KAAA,CAAA,QAAA,IACA,MAAA,CAAA,KAAA,CAAA,WAAA,KAAA,UADA,IAEA,MAAA,CAAA,KAAA,CAAA,WAvBA;AAAA;AAAA;AAAA;;AAyBA,gBAAA,MAAA,CAAA,QAAA,CAAA;AACA,kBAAA,IAAA,EAAA,OADA;AAEA,kBAAA,OAAA,EAAA;AAFA,iBAAA;;AAzBA;;AAAA;AA+BA,oBAAA,MAAA,CAAA,IAAA,CAAA,OAAA,EAAA;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,SAAA,CAAA,OAAA,GAAA,MAAA,CAAA,IAAA,CAAA,OAAA;AACA;;AACA,gBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,kBAAA,MAAA,CAAA,OAAA,GAAA,mBAAA,OAAA,CAAA;AACA,oBAAA,IAAA,EAAA,IADA;AACA;AACA,oBAAA,IAAA,EAAA,WAFA;AAEA;AACA,oBAAA,OAAA,EAAA,iBAHA;AAGA;AACA,oBAAA,UAAA,EAAA,oBAJA;AAIA;AACA,oBAAA,MAAA,EAAA,QAAA,CAAA,aAAA,CAAA,YAAA;AALA,mBAAA,CAAA;AAOA,iBATA;;AAlCA;AAAA;AAAA,uBA8CA,mCAAA,MAAA,CAAA,KAAA,CA9CA;;AAAA;AAAA;AA8CA,gBAAA,IA9CA,yBA8CA,IA9CA;AA8CA,gBAAA,IA9CA,yBA8CA,IA9CA;;AA+CA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,UAAA,GAAA,IAAA;AACA;;AACA,oBAAA,IAAA,EAAA;AACA,kBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,oBAAA,MAAA,CAAA,OAAA,CAAA,KAAA;AACA,mBAHA;AAIA;;AAvDA;AAAA;;AAAA;AAAA;AAAA;;AAyDA,gBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,kBAAA,MAAA,CAAA,OAAA,CAAA,KAAA;AACA,iBAHA;;AAzDA;AA8DA,oBAAA,UAAA,EAAA;AACA,kBAAA,UAAA,CAAA,WAAA,GAAA,MAAA,CAAA,KAAA,CAAA,WAAA;;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,UAAA,EAAA,UAAA;AACA;;AACA,gBAAA,MAAA,CAAA,OAAA;;AAlEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmEA,KArEA;AAsEA,IAAA,OAtEA,qBAsEA;AACA,WAAA,IAAA,CAAA,KAAA,IAAA,EAAA,UAAA,EAAA,EAAA;AACA,WAAA,IAAA,CAAA,KAAA,IAAA,EAAA,WAAA,EAAA,EAAA;AACA,WAAA,IAAA,CAAA,KAAA,IAAA,EAAA,SAAA,EAAA,EAAA;AACA,WAAA,KAAA,CAAA,SAAA,EAAA,OAAA;AACA;AA3EA;AA/FA,C", "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      id=\"dialogAct\"\n      class=\"tree\"\n      :title=\"option.title ? option.title : ''\"\n      :top=\"option.top ? option.top : '10vh'\"\n      v-dialogDrag\n      :visible.sync=\"isShow\"\n      :width=\"option.width ? option.width : '25%'\"\n      :close-on-click-modal=\"false\"\n      @close=\"toClose\"\n      :destroy-on-colse=\"true\"\n      :modal-append-to-body=\"false\"\n    >\n      <slot name=\"Dialog_content\" />\n      <div v-if=\"datas.defaultFrom\">\n        <el-form\n          label-width=\"120px\"\n          ref=\"form\"\n          :model=\"form\"\n          v-if=\"datas.taskId === 'sckldsp'\"\n        >\n          <div>\n            <el-row>\n              <div v-if=\"datas.processType === 'complete'\">\n                <el-col :span=\"24\">\n                  <el-form-item prop=\"nextUser2\" label=\"电力调度方式岗\">\n                    <el-select\n                      v-model=\"form.nextUser2\"\n                      placeholder=\"请选择人员\"\n                      style=\"width: 100%;\"\n                      value-key=\"userName\"\n                      clearable\n                      filterable\n                    >\n                      <el-option\n                        v-for=\"item in dlddshrOptions\"\n                        :key=\"item.userName\"\n                        :label=\"item.nickName\"\n                        :value=\"item\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"24\">\n                  <el-form-item prop=\"nextUser\" label=\"科技信息中心远动岗\">\n                    <el-select\n                      v-model=\"form.nextUser\"\n                      placeholder=\"请选择人员\"\n                      style=\"width: 100%;\"\n                      value-key=\"userName\"\n                      clearable\n                      filterable\n                    >\n                      <el-option\n                        v-for=\"item in kjzxshrOptions\"\n                        :key=\"item.userName\"\n                        :label=\"item.nickName\"\n                        :value=\"{\n                          userName: item.userName,\n                          nickName: item.nickName\n                        }\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n              </div>\n            </el-row>\n          </div>\n        </el-form>\n        <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" v-else>\n          <div>\n            <el-row>\n              <div v-if=\"datas.processType === 'complete'\">\n                <el-col :span=\"24\">\n                  <el-form-item prop=\"nextUser\" label=\"审核人\">\n                    <el-select\n                      v-model=\"form.nextUser\"\n                      placeholder=\"请选择人员\"\n                      style=\"width: 100%;\"\n                      value-key=\"userName\"\n                      clearable\n                      filterable\n                    >\n                      <el-option\n                        v-for=\"item in usersOptions\"\n                        :key=\"item.userName\"\n                        :label=\"item.nickName\"\n                        :value=\"{\n                          userName: item.userName,\n                          nickName: item.nickName\n                        }\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n              </div>\n              <el-col :span=\"24\">\n                <el-form-item\n                  prop=\"comment\"\n                  label=\"回退原因：\"\n                  v-if=\"datas.processType === 'rollback'\"\n                >\n                  <el-input\n                    style=\"width: 100%\"\n                    v-model=\"form.comment\"\n                    type=\"textarea\"\n                    placeholder=\"请输入原因\"\n                  />\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </div>\n        </el-form>\n      </div>\n      <div v-else>\n        <span>请确定是否提交</span>\n      </div>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"info\" @click=\"toClose\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"todoSubmit('form')\">确定</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { completeTzglTask } from \"@/api/activiti/processTask\";\nimport { Loading } from \"element-ui\";\nimport { getUsers } from \"@/api/system/usergroup\";\nimport { getGzpUser } from \"@/api/yxgl/gzpgl/gzpgl\";\nexport default {\n  name: \"index\",\n  props: {\n    /*流程发起必填数据\n        processData:{\n          processDefinitionKey:\"流程定义的key\",//必填\n          taskId:\"任务id，如果任务在代办列表时，会根据代办列表获取taskid，否则需传入业务id来确定task\",\n          businessKey:\"业务id，对应业务的主键\",//taskid和businessKey两者必须有其中一个才可以确定一个task\n          businessType:\"业务类型，用于区分不同的业务\"//必填\n          variables:\"拓展参数\"//流程定义中设置的参数,\n          nextUser:\"如果流程实例中并未配置每一个节点的处理人，则需要用户手动选择每一个节点的处理人\",\n          processType:'complete,rollback',\n          defaultFrom:true,false 是否需要默认表单\n        }*/\n    processData: {\n      type: Object,\n      default() {\n        return {\n          defaultFrom: true,\n          processType: \"complete\"\n        };\n      }\n    },\n    //显示隐藏\n    isShow: {\n      type: Boolean,\n      default: false\n    },\n    //子组件默认参数\n    option: {\n      type: Object,\n      default() {\n        return { title: \"审批\" };\n      }\n    }\n  },\n  data() {\n    return {\n      disabled: false,\n      form: {},\n      datas: {},\n      loading: null,\n      dlddshrOptions: [],\n      kjzxshrOptions: [],\n      usersOptions: []\n    };\n  },\n  watch: {\n    processData: {\n      handler(newVal, oldVal) {\n        this.$nextTick(() => {\n          this.datas = { ...newVal };\n          switch (this.datas.status) {\n            case 0:\n              getGzpUser({\n                rylx: \"签发人\",\n                dw: \"3010\"\n              }).then(res => {\n                this.usersOptions = res.data;\n              });\n              break;\n            case 1:\n              getUsers({ personGroupId: 52, deptId: \"\", deptName: \"\" }).then(\n                res => {\n                  this.usersOptions = res.data;\n                }\n              );\n              break;\n            case 2:\n              getUsers({ personGroupId: 53, deptId: \"\", deptName: \"\" }).then(\n                res => {\n                  this.usersOptions = res.data;\n                }\n              );\n              break;\n            case 3:\n              getUsers({ personGroupId: 108, deptId: \"\", deptName: \"\" }).then(\n                res => {\n                  this.dlddshrOptions = res.data;\n                }\n              );\n              getUsers({ personGroupId: 109, deptId: \"\", deptName: \"\" }).then(\n                res => {\n                  this.kjzxshrOptions = res.data;\n                }\n              );\n              break;\n          }\n        });\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  mounted() {},\n  methods: {\n    //流程提交，流程拒绝\n    async todoSubmit() {\n      this.datas.routePath = this.$route.path;\n      if (this.form.nextUser) {\n        this.datas.nextUser = this.form.nextUser.userName;\n        this.datas.nextUserNickName = this.form.nextUser.nickName;\n      }\n\n      if (this.form.nextUser2) {\n        this.datas.nextUser2 = this.form.nextUser2.userName;\n        this.datas.nextUserNickName2 = this.form.nextUser2.nickName;\n      }\n      if (this.datas.taskId === \"sckldsp\") {\n        if (!this.datas.nextUser || !this.datas.nextUser2) {\n          this.$message({\n            type: \"error\",\n            message: \"请选择人员!\"\n          });\n          return;\n        }\n      }\n      if (\n        !this.datas.nextUser &&\n        this.datas.processType === \"complete\" &&\n        this.datas.defaultFrom\n      ) {\n        this.$message({\n          type: \"error\",\n          message: \"请选择人员!\"\n        });\n        return;\n      }\n      if (this.form.comment) {\n        this.datas.variables.comment = this.form.comment;\n      }\n      this.$nextTick(() => {\n        // 以服务的方式调用的 Loading 需要异步关闭\n        this.loading = Loading.service({\n          lock: true, //lock的修改符--默认是false\n          text: \"流程进行中，请稍后\", //显示在加载图标下方的加载文案\n          spinner: \"el-icon-loading\", //自定义加载图标类名\n          background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n          target: document.querySelector(\"#dialogAct\")\n        });\n      });\n      let resultData;\n      try {\n        let { code, data } = await completeTzglTask(this.datas);\n        if (code === \"0000\") {\n          resultData = data;\n        }\n        if (code) {\n          this.$nextTick(() => {\n            // 以服务的方式调用的 Loading 需要异步关闭\n            this.loading.close();\n          });\n        }\n      } catch (e) {\n        this.$nextTick(() => {\n          // 以服务的方式调用的 Loading 需要异步关闭\n          this.loading.close();\n        });\n      }\n      if (resultData) {\n        resultData.processType = this.datas.processType;\n        this.$emit(\"todoData\", resultData);\n      }\n      this.toClose();\n    },\n    toClose() {\n      this.$set(this.form,'nextUser',\"\")\n      this.$set(this.form,'nextUser2',\"\")\n      this.$set(this.form,'comment',\"\")\n      this.$emit(\"toClose\", \"close\");\n    }\n  }\n};\n</script>\n\n<style scoped></style>\n"], "sourceRoot": "src/components/activiti_tzgl"}]}