{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\syxm.vue?vue&type=style&index=0&id=f1e4061c&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\syxm.vue", "mtime": 1706897323690}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouYm94LWNhcmQgewogIG1hcmdpbi1ib3R0b206IDE1cHg7CgogIC5lbC1jYXJkX19oZWFkZXIgewogICAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDIzNSwgMjQ1LCAyNTUpICFpbXBvcnRhbnQ7CiAgfQp9CgouaXRlbSB7CiAgd2lkdGg6IDIwMHB4OwogIGhlaWdodDogMTQ4cHg7CiAgZmxvYXQ6IGxlZnQ7Cn0KCi5oZWFkLWNvbnRhaW5lciB7CiAgbWFyZ2luOiAwIGF1dG87CiAgd2lkdGg6IDk4JTsKICBtYXgtaGVpZ2h0OiA3OXZoOwogIG92ZXJmbG93OiBhdXRvOwp9Cg=="}, {"version": 3, "sources": ["syxm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAocA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "syxm.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row :gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card class=\"aside_height\" shadow=\"never\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备类型</span>\n          </div>\n          <div style=\"overflow: auto; height: 84vh\">\n            <el-tree\n              highlight-current\n              id=\"tree\"\n              :props=\"props\"\n              :load=\"loadNode\"\n              lazy\n              @node-click=\"handleNodeClick\"\n              :default-expanded-keys=\"['1']\"\n            />\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-white>\n          <el-white class=\"button-group\">\n            <el-button\n              class=\"mb8\"\n              @click=\"addButton\"\n              type=\"primary\"\n              icon=\"el-icon-plus\"\n            >\n              新增\n            </el-button>\n            <!--<el-button-->\n            <!--  class=\"mb8\"-->\n            <!--  @click=\"deleteButton\"-->\n            <!--  type=\"danger\"-->\n            <!--  icon=\"el-icon-delete\"-->\n            <!--&gt;-->\n            <!--  删除-->\n            <!--</el-button>-->\n          </el-white>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"77.4vh\"\n          >\n            <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\"\n                             width=\"160\"\n                             :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button @click=\"updateDetails(scope.row)\"  type=\"text\"\n                           size=\"small\" title=\"修改\"  class='el-icon-edit'\n                >\n                </el-button>\n                <el-button @click=\"getDetails(scope.row)\" type=\"text\" size=\"small\" title=\"详情\" class=\"el-icon-view\"></el-button>\n                <el-button @click=\"getNameplateInfo(scope.row)\"  type=\"text\"\n                           size=\"small\" title=\"定义详情\" class=\"el-icon-edit-outline\"\n                >\n                </el-button>\n                <el-button @click=\"deleteButton(scope.row)\" v-show=\"scope.row.createUser == currentUser\" type=\"text\"\n                           size=\"small\" title=\"删除\" class=\"el-icon-delete\"\n                >\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n    <!-- 新增、修改、详情界面 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"40%\"\n      @close=\"handleClose\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"项目名称：\" prop=\"title\">\n              <el-input\n                placeholder=\"项目名称\"\n                v-model=\"form.title\"\n                :disabled=\"isDisabled\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"专业：\" prop=\"zy\">\n              <el-select\n                placeholder=\"专业\"\n                v-model=\"form.zy\"\n                :disabled=\"isDisabled\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否分相：\" prop=\"sffx\">\n              <el-select\n                placeholder=\"是否分相\"\n                v-model=\"form.sffx\"\n                :disabled=\"isDisabled\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in sffxOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button\n          v-if=\"title === '新增' || title === '修改'\"\n          type=\"primary\"\n          @click=\"save\"\n        >确 认\n        </el-button\n        >\n      </div>\n    </el-dialog>\n\n    <el-dialog\n      :visible.sync=\"isShowMpInfo\"\n      v-dialogDrag\n      v-if=\"isShowMpInfo\"\n      width=\"80%\"\n      title=\"试验项目内容\"\n      :append-to-body=\"true\"\n      @close=\"closeInfoDialog\"\n    >\n      <syxmxq-info\n        :mp-data=\"rowData\"\n        :mx-data.sync=\"mxData\"\n        @closeInfoDialog=\"closeInfoDialog\"\n      />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  getDeviceClassTreeNodeByPid,\n  getPageDataList,\n  getTable,\n  remove,\n  saveOrUpdate\n} from '@/api/dagangOilfield/bzgl/sympk/sympk'\nimport syxmxqInfo from '@/views/dagangOilfield/bzgl/sybzk/syxmxqInfo'\n\nexport default {\n  name: 'syxm',\n  components: { syxmxqInfo },\n  inject: ['reload'], //inject注入根组件的reload方法\n  data() {\n    return {\n      currentUser: this.$store.getters.name,\n      ids: [],\n      props: {\n        label: 'name',\n        children: 'zones',\n        isLeaf: 'leaf'\n      },\n      //专业下拉框数据\n      options: [\n        { label: '输电', value: 'SD' },\n        { label: '变电', value: 'BD' },\n        { label: '配电', value: 'PD' }\n      ],\n      //是否分相下拉框数据\n      sffxOptions: [\n        { label: '是', value: '1' },\n        { label: '否', value: '0' }\n      ],\n      form: {\n        objId: undefined,\n        sblxbm: undefined,\n        title: undefined,\n        zy: undefined,\n        sffx: '',\n        isMpSyxm: 1,\n        mpmc: ''\n      },\n      dyForm: {\n        obj_id: undefined,\n        a_hs: 0,\n        a_hsOld: 0,\n        a_ls: 0,\n        a_lsOld: 0,\n        b_hs: 0,\n        b_hsOld: 0,\n        b_ls: 0,\n        b_lsOld: 0,\n        rowSpanNum: 1,\n        colSpanNum: 1,\n        lbbs: undefined,\n        title: undefined\n      },\n      isShowDetails: false,\n      isShowDyDetails: false,\n      title: '',\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: '项目名称', prop: 'title', minWidth: '180' },\n          { label: '专业', prop: 'zyName', minWidth: '200' },\n          { label: '是否分相', prop: 'sffxName', minWidth: '200' },\n          // {\n          //   prop: 'operation',\n          //   label: '操作',\n          //   minWidth: '100px',\n          //   style: { display: 'block' },\n          //   //操作列固定再右侧\n          //   fixed: 'right',\n          //   operation: [\n          //     { name: '修改', clickFun: this.updateDetails },\n          //     { name: '详情', clickFun: this.getDetails },\n          //     { name: '定义详情', clickFun: this.getNameplateInfo }\n          //   ]\n          // }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      //删除是否可用\n      multipleSensor: true,\n      //查询参数\n      queryParams: {\n        sblxbm: undefined,\n        pageNum: 1,\n        pageSize: 10,\n        isMpSyxm: 1\n      },\n      isDisabled: false,\n      rules: {\n        title: [{ required: true, message: '请填写项目名称', trigger: 'blur' }],\n        zy: [{ required: true, message: '请选择专业', trigger: 'blur' }],\n        sffx: [\n          { required: true, message: '请选择是否分相', trigger: 'change' }\n        ]\n      },\n      selection: [], //记录最后一次选中的行数据\n      isShowMpInfo: false,\n      //选中行数据\n      rowData: {},\n      //设备类型编码\n      sblxbm: '',\n      mxData: [] //表格明细数据\n    }\n  },\n  watch: {},\n  created() {\n    this.getData()\n  },\n  methods: {\n    //树节点点击事件\n    handleNodeClick(data) {\n      this.form.sblxbm = data.code\n      this.sblxbm = data.code\n      this.queryParams.sblxbm = data.code\n      this.getData()\n    },\n    /**\n     * 表格多选框\n     */\n    handleSelectionChange(selection) {\n      this.ids = selection.map((item) => item.objId)\n      this.selection = selection\n    },\n    //添加按钮\n    addButton() {\n      if (this.sblxbm === '') {\n        this.$message.warning('请选择左侧树节点新增数据！')\n        return\n      } else {\n        this.isShowDetails = true\n        this.isDisabled = false\n        this.form = {}\n        this.form.sblxbm = this.sblxbm\n        this.form.isMpSyxm = 1\n        this.title = '新增'\n      }\n    },\n\n    close() {\n      this.isShowDetails = false\n    },\n    updateDetails(row) {\n      this.title = '修改'\n      this.isShowDetails = true\n      this.form = row\n      this.isDisabled = false\n    },\n    getDetails(row) {\n      this.title = '详情'\n      this.isShowDetails = true\n      this.form = row\n      this.isDisabled = true\n    },\n    save() {\n      this.$refs.form.validate((valid) => {\n        if (valid) {\n          this.form.mpmc = this.form.title\n          saveOrUpdate(this.form).then((res) => {\n            if (res.code == '0000') {\n              this.$message.success('保存成功！')\n              this.isShowDetails = false\n              this.getData()\n            }\n          })\n        } else {\n          this.$message.error('请输入所有必填字段！')\n          return false\n        }\n      })\n    },\n    //删除按钮\n    deleteButton(row) {\n      this.form = row\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        remove([this.form.objId]).then((res) => {\n          this.$message({\n            type: 'success',\n            message: '删除成功!'\n          })\n          this.getData()\n        })\n      })\n    },\n\n    //查询列表\n    async getData(params) {\n      this.queryParams = { ...this.queryParams, ...params }\n      const param = { ...this.queryParams, ...params }\n      getPageDataList(param).then((res) => {\n        this.tableAndPageInfo.tableData = res.data.records\n        this.tableAndPageInfo.pager.total = res.data.total\n\n        this.tableAndPageInfo.tableData.forEach((item) => {\n          this.options.forEach((element) => {\n            if (item.zy === element.value) {\n              item.zyName = element.label\n            }\n          })\n          this.sffxOptions.forEach((element) => {\n            if (item.sffx === element.value) {\n              item.sffxName = element.label\n            }\n          })\n        })\n      })\n    },\n    //懒加载函数\n    loadNode(node, resolve) {\n      let TreeparamMap = {\n        pid: '',\n        spbLogo: ['输电设备', '变电设备','配电设备']\n      }\n      if (node.level === 0) {\n        TreeparamMap.pid = 'sb'\n        return this.getTreeNode(TreeparamMap, resolve)\n      }\n      setTimeout(() => {\n        TreeparamMap.pid = node.data.code\n        this.getTreeNode(TreeparamMap, resolve)\n      }, 500)\n    },\n    //获取树节点数据\n    getTreeNode(paramMap, resolve) {\n      getDeviceClassTreeNodeByPid(paramMap).then((res) => {\n        let treeNodes = []\n        res.data.forEach((item) => {\n          let node = {\n            name: item.name,\n            level: item.level,\n            id: item.id,\n            pid: item.pid,\n            leaf: false,\n            code: item.code\n          }\n          treeNodes.push(node)\n        })\n        resolve(treeNodes)\n      })\n    },\n\n    //获取铭牌信息\n    getNameplateInfo(row) {\n      this.rowData = row\n      // this.rowData.sblxbm = this.sblxbm;\n      let params = JSON.stringify({\n        obj_id: row.objId,\n        lbbs: 'A'\n      })\n      getTable(params).then((res) => {\n        if (res.code === '0000') {\n          this.mxData = res.data //需要先设置数据再弹框，否则数据传不过去\n          this.isShowMpInfo = true\n        }\n      })\n    },\n    //关闭铭牌内容弹框\n    closeInfoDialog() {\n      this.isShowMpInfo = false\n      //刷新父页面\n      // this.reload()\n      this.getData()\n    },\n\n    //清空表单数据\n    handleClose() {\n      this.form = {}\n      this.$nextTick(() => {\n        this.form = this.$options.data().form\n        this.resetForm('form')\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  max-height: 79vh;\n  overflow: auto;\n}\n</style>\n"]}]}