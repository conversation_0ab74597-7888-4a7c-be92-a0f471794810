{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\jxbzk\\jxxmwh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\jxbzk\\jxxmwh.vue", "mtime": 1706897322435}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["jxxmwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAoKA;;AAMA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAFA;eAGA;AACA,EAAA,IAAA,EAAA,QADA;AAEA;AACA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA,MAAA,WAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,IADA;AAEA;AACA,MAAA,IAAA,EAAA,EAHA;AAIA;AACA,MAAA,aAAA,EAAA,KALA;AAMA;AACA,MAAA,UAAA,EAAA,KAPA;AAQA;AACA,MAAA,SAAA,EAAA,EATA;AAUA,MAAA,OAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAHA,CAVA;AAeA;AACA,MAAA,KAAA,EAAA,EAhBA;AAiBA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,IAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA,EAFA;AAGA,UAAA,OAAA,EAAA,EAHA;AAIA,UAAA,IAAA,EAAA,EAJA;AAKA,UAAA,IAAA,EAAA;AALA,SADA;AAQA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,MAFA;AAGA,UAAA,IAAA,EAAA,mBAHA;AAIA,UAAA,SAAA,EAAA,IAJA;AAKA,UAAA,UAAA,EAAA,IALA;AAMA,UAAA,QAAA,EAAA,IANA;AAOA,UAAA,OAAA,EAAA;AAPA,SADA,EAUA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,MAFA;AAGA,UAAA,IAAA,EAAA,OAHA;AAIA,UAAA,SAAA,EAAA;AAJA,SAVA,EAgBA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,MAFA;AAGA,UAAA,IAAA,EAAA,OAHA;AAIA,UAAA,SAAA,EAAA;AAJA,SAhBA;AAsBA;;;;;;;;;;;;;;AAcA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,MAFA;AAGA,UAAA,IAAA,EAAA,UAHA;AAIA,UAAA,aAAA,EAAA,EAJA;AAKA,UAAA,QAAA,EAAA,IALA;AAMA,UAAA,OAAA,EAAA,EANA;AAOA,UAAA,SAAA,EAAA;AAPA,SApCA;AARA,OAjBA;AAwEA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA;AACA;;;;;;;;;;;;AALA,SARA;AA0BA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AA1BA,OAxEA;AAoGA,MAAA,MAAA,EAAA;AACA,QAAA,OAAA,EAAA,EADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA;AAJA,OApGA;AA0GA,MAAA,UAAA,EAAA,EA1GA;AA2GA;AACA,MAAA,QAAA,EAAA,EA5GA;AA6GA,MAAA,gBAAA,EAAA,EA7GA;AA8GA,MAAA,KAAA,EAAA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AAVA,OA9GA;AA0HA,MAAA,cAAA,EAAA,KA1HA;AA2HA,MAAA,QAAA,EAAA;AA3HA,KAAA;AA6HA,GAjIA;AAkIA,EAAA,OAlIA,qBAkIA;AACA,SAAA,UAAA;AACA,SAAA,mBAAA;AACA,GArIA;AAsIA,EAAA,KAAA,EAAA;AACA,IAAA,aADA,yBACA,GADA,EACA;AACA,UAAA,GAAA,EAAA;AACA,YAAA,EAAA,GAAA,KAAA,GAAA,CAAA,aAAA,CAAA,YAAA,CAAA;AACA,QAAA,EAAA,CAAA,KAAA,CAAA,IAAA,GAAA,CAAA;AACA,QAAA,EAAA,CAAA,KAAA,CAAA,GAAA,GAAA,CAAA;AACA;AACA;AAPA,GAtIA;AA+IA,EAAA,OA/IA,qBA+IA;AACA;AACA,SAAA,OAAA;AACA,GAlJA;AAmJA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,OAFA,mBAEA,MAFA,EAEA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,KAAA,CAAA,MAAA,+DAAA,KAAA,CAAA,MAAA,GAAA,MAAA;AACA,gBAAA,KAHA,GAGA,KAAA,CAAA,MAHA;AAIA,gBAAA,KAAA,CAAA,MAAA,GAAA,KAAA,CAJA,CAKA;;AALA;AAAA,uBAMA,qBAAA,KAAA,CANA;;AAAA;AAAA;AAMA,gBAAA,IANA,kBAMA,IANA;AAMA,gBAAA,IANA,kBAMA,IANA;;AAOA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,KAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,KAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;;AAEA,kBAAA,KAAA,CAAA,gBAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,oBAAA,KAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,OAAA,EAAA;AACA,0BAAA,IAAA,CAAA,IAAA,KAAA,OAAA,CAAA,KAAA,EAAA;AACA,wBAAA,IAAA,CAAA,QAAA,GAAA,OAAA,CAAA,KAAA;AACA;AACA;AACA,qBALA;AAMA,mBAPA;AAQA;;AAnBA;AAAA;;AAAA;AAAA;AAAA;AAqBA,gBAAA,OAAA,CAAA,GAAA;;AArBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuBA,KAzBA;AA0BA;AACA,IAAA,QA3BA,sBA2BA;AACA,WAAA,MAAA,GAAA;AACA,QAAA,OAAA,EAAA,EADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA,EAJA;AAKA,QAAA,IAAA,EAAA;AALA,OAAA;AAOA,WAAA,UAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,KAAA,UAAA,EAAA;AACA,UAAA,IAAA,CAAA,aAAA,GAAA,EAAA;AACA;AACA,OAJA;AAKA,KAxCA;AAyCA;AACA,IAAA,qBA1CA,mCA0CA,CAAA,CA1CA;AA2CA;AACA,IAAA,UA5CA,wBA4CA;AACA,WAAA,KAAA,GAAA,QAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,KAjDA;AAkDA;AACA,IAAA,SAnDA,uBAmDA;AACA,WAAA,KAAA,GAAA,QAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,IAAA,GAAA,EAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,KAxDA;AAyDA;AACA,IAAA,SA1DA,qBA0DA,GA1DA,EA0DA;AACA,WAAA,KAAA,GAAA,QAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,KA/DA;AAgEA;AACA,IAAA,OAjEA,mBAiEA,GAjEA,EAiEA;AACA,WAAA,KAAA,GAAA,MAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,KAtEA;AAuEA,IAAA,OAvEA,qBAuEA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,sBAAA,KAAA,EAAA;AACA,8CAAA,MAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,0BAAA;AACA,4BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,0BAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA;AACA,uBAJA,CAIA,OAAA,CAAA,EAAA;AACA,wBAAA,OAAA,CAAA,GAAA,CAAA,CAAA;AACA;;AACA,sBAAA,MAAA,CAAA,OAAA;AACA,qBATA;AAUA,mBAXA,MAWA;AACA,2BAAA,KAAA;AACA;;AACA,kBAAA,MAAA,CAAA,aAAA,GAAA,KAAA;AACA,iBAhBA;;AADA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA,KAzFA;AA0FA;AACA,IAAA,SA3FA,qBA2FA,GA3FA,EA2FA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,IAAA,mCAAA,GAAA;;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAKA,IALA,CAKA,YAAA;AACA,sCAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,EAAA,IAAA,CAAA,gBAAA;AAAA,wBAAA,IAAA,QAAA,IAAA;;AACA,wBAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,SADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;;AAIA,sBAAA,MAAA,CAAA,OAAA;AACA,qBANA,MAMA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,OADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;AAIA;AACA,mBAbA;AAcA,iBApBA,EAqBA,KArBA,CAqBA,YAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA;AACA,oBAAA,IAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;AAIA,iBA1BA;;AA2BA,gBAAA,MAAA,CAAA,OAAA;;AA7BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA8BA,KAzHA;AA0HA;AACA,IAAA,WA3HA,uBA2HA,GA3HA,EA2HA,IA3HA,EA2HA;AACA;AACA,UAAA,GAAA,CAAA,KAAA,KAAA,MAAA,IAAA,GAAA,CAAA,KAAA,EAAA;AACA,aAAA,MAAA,CAAA,OAAA,GAAA,GAAA,CAAA,KAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,KAAA,MAAA;AACA;AACA,KAjIA;AAkIA;AACA,IAAA,KAnIA,mBAmIA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,KArIA;AAsIA,IAAA,YAtIA,wBAsIA,IAtIA,EAsIA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KAxIA;AAyIA,IAAA,oBAzIA,kCAyIA;AACA,WAAA,QAAA,GAAA,KAAA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,KA5IA;AA6IA,IAAA,UA7IA,wBA6IA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,2BAAA,MAAA,CADA;;AAAA;AAAA;AACA,gBAAA,IADA,yBACA,IADA;AAEA,gBAAA,MAAA,CAAA,QAAA,GAAA,IAAA;;AAEA,gBAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,sBAAA,IAAA,CAAA,KAAA,KAAA,MAAA,EAAA;AACA,oBAAA,IAAA,CAAA,OAAA,GAAA,IAAA;AACA;AACA;AACA,iBALA;;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,KAvJA;AAwJA,IAAA,mBAxJA,iCAwJA;AAAA;;AACA,uCAAA,CAAA,MAAA,EAAA,MAAA,EAAA,MAAA,CAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,gBAAA,GAAA,GAAA,CAAA,IAAA;;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,gBAAA,IAAA,CAAA,KAAA,KAAA,MAAA,EAAA;AACA,cAAA,IAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;AACA;AACA;AACA,WALA;AAMA,SARA,MAQA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,UAAA;AACA;AACA,OAZA;AAaA,KAtKA;AAuKA,IAAA,eAvKA,2BAuKA,GAvKA,EAuKA;AACA,UAAA,GAAA,CAAA,MAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,aAAA,cAAA,GAAA,IAAA;AACA,aAAA,QAAA,GAAA,IAAA;AACA;AACA,KA5KA;AA6KA,IAAA,iBA7KA,6BA6KA,GA7KA,EA6KA;AAAA;;AACA,UAAA,KAAA,QAAA,EAAA;AACA,aAAA,UAAA,CAAA,IAAA,CAAA,OAAA,GAAA,EAAA;AACA,aAAA,UAAA,CAAA,IAAA,CAAA,IAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,OAAA,EAAA;AACA,YAAA,MAAA,CAAA,UAAA,CAAA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA;;AACA,YAAA,MAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA,IAAA,IAAA,CAAA,IAAA,GAAA,GAAA;AACA;AACA,SALA;AAOA,aAAA,UAAA,CAAA,IAAA,CAAA,IAAA,GAAA,KAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,SAAA,CACA,CADA,EAEA,KAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,MAAA,GAAA,CAFA,CAAA;AAIA,aAAA,cAAA,GAAA,KAAA;AACA,OAfA,MAeA;AACA,YAAA,SAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,OAAA,EAAA;AACA,YAAA,SAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,SAJA;;AAKA,YAAA,SAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,eAAA,IAAA,CAAA,MAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA,eAAA,IAAA,CAAA,IAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA,eAAA,cAAA,GAAA,KAAA;AACA,SAJA,MAIA;AACA,eAAA,QAAA,CAAA,OAAA,CAAA,WAAA;AACA;AACA;AACA,KA5MA;AA6MA,IAAA,qBA7MA,mCA6MA;AACA,WAAA,cAAA,GAAA,KAAA;AACA,KA/MA;AAgNA;AACA,IAAA,WAjNA,yBAiNA;AAAA;;AACA,WAAA,IAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,YAAA;AACA,QAAA,MAAA,CAAA,IAAA,GAAA,MAAA,CAAA,QAAA,CAAA,IAAA,GAAA,IAAA;;AACA,QAAA,MAAA,CAAA,SAAA,CAAA,MAAA;AACA,OAHA;AAIA;AAvNA;AAnJA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      @handleReset=\"getReset\"\n      @handleEvent=\"handleEvent\"\n    />\n    <div>\n      <el-white class=\"button-group\">\n        <div class=\"button_btn\">\n          <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"getInster\"\n            >新增</el-button\n          >\n        </div>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"selectChange\"\n          height=\"65vh\"\n        >\n          <el-table-column\n            slot=\"table_eight\"\n            align=\"center\"\n            fixed=\"right\"\n            style=\"display: block\"\n            label=\"操作\"\n            width=\"160\"\n            :resizable=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                @click=\"updateRow(scope.row)\"\n                v-show=\"scope.row.createBy == currentUser\"\n                type=\"text\"\n                size=\"small\"\n                title=\"修改\"\n                class=\"el-icon-edit\"\n              ></el-button>\n              <el-button\n                @click=\"getInfo(scope.row)\"\n                type=\"text\"\n                size=\"small\"\n                title=\"详情\"\n                class=\"el-icon-view\"\n              ></el-button>\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                v-show=\"scope.row.createBy == currentUser\"\n                @click=\"deleteRow(scope.row)\"\n                title=\"删除\"\n                class=\"el-icon-delete\"\n              ></el-button>\n            </template>\n          </el-table-column>\n        </comp-table>\n      </el-white>\n      <!-- 详情/新增/修改 -->\n      <el-dialog\n        :title=\"title\"\n        :visible.sync=\"isShowDetails\"\n        width=\"40%\"\n        @close=\"handleClose\"\n        v-dialogDrag\n      >\n        <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n          <el-row :gutter=\"8\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item v-show=\"false\" label=\"设备分类:\" prop=\"sbfl\">\n                <el-input v-model=\"form.sbfl\"></el-input>\n                <el-input v-model=\"form.objId\"></el-input>\n              </el-form-item>\n              <el-form-item label=\"设备分类：\" prop=\"sbfl\">\n                <el-select\n                  placeholder=\"请选择设备分类\"\n                  v-model=\"form.sbfl\"\n                  :disabled=\"isDisabled\"\n                  clearable\n                  filterable\n                  style=\"width: 100%\"\n                >\n                  <el-option-group\n                    v-for=\"group in DevicesListGroup\"\n                    :key=\"group.label\"\n                    :label=\"group.label\"\n                  >\n                    <el-option\n                      v-for=\"item in group.sbDataList\"\n                      :key=\"item.code\"\n                      :label=\"item.name\"\n                      :value=\"item.code\"\n                    >\n                    </el-option>\n                  </el-option-group>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"检修分类：\" prop=\"jxfl\">\n                <el-select\n                  placeholder=\"请选择检修分类\"\n                  v-model=\"form.jxfl\"\n                  clearable\n                  :disabled=\"isDisabled\"\n                  style=\"width: 100%\"\n                >\n                  <el-option\n                    v-for=\"item in jxflList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"项目编号：\" prop=\"xmbh\">\n                <el-input\n                  placeholder=\"请输入项目编号\"\n                  v-model=\"form.xmbh\"\n                  :disabled=\"isDisabled\"\n                />\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"24\">\n              <el-form-item label=\"检修项目：\" prop=\"jxxm\">\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"form.jxxm\"\n                  placeholder=\"请输入检修项目\"\n                  :disabled=\"isDisabled\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n        <div slot=\"footer\">\n          <el-button @click=\"close\">取 消</el-button>\n          <el-button\n            v-if=\"title == '检修项目新增' || title == '检修项目修改'\"\n            type=\"primary\"\n            @click=\"saveRow\"\n            >确 认</el-button\n          >\n        </div>\n      </el-dialog>\n\n      <!-- <el-dialog\n        :append-to-body=\"true\"\n        title=\"设备分类\"\n        :visible.sync=\"showDeviceTree\"\n        width=\"400px\"\n        v-if=\"showDeviceTree\">\n        <device-tree\n          @getDeviceTypeData=\"getDeviceTypeData\"\n          @closeDeviceTypeDialog=\"closeDeviceTypeDialog\">\n        </device-tree>\n      </el-dialog> -->\n    </div>\n  </div>\n</template>\n<script>\nimport {\n  getList,\n  remove,\n  saveOrUpdate\n} from \"@/api/dagangOilfield/bzgl/jxbzk/jxxmwh\";\n// import DeviceTree from '@/views/dagangOilfield/bzgl/sbbzk/deviceTree'\nimport { getDictTypeData } from \"@/api/system/dict/data\";\nimport { getDeviceClassGroup } from \"@/api/dagangOilfield/bzgl/sblxwh/sblxwh\";\nexport default {\n  name: \"jxxmwh\",\n  // components: { DeviceTree },\n  data() {\n    return {\n      currentUser: this.$store.getters.name,\n      //form表单\n      form: {},\n      //是否显示弹框\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //表单数据\n      dataTable: [],\n      options: [\n        { label: \"全部\", value: \"\" },\n        { label: \"测试1\", value: \"1\" },\n        { label: \"测试2\", value: \"0\" }\n      ],\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          sbfl: \"\",\n          jxfl: \"\",\n          jxflArr: [],\n          xmbh: \"\",\n          xmmc: \"\"\n        },\n        fieldList: [\n          {\n            label: \"设备分类\",\n            value: \"sbfl\",\n            type: \"selectGroupjxxmwh\",\n            clearable: true,\n            filterable: true,\n            multiple: true,\n            options: []\n          },\n          {\n            label: \"项目编号\",\n            value: \"xmbh\",\n            type: \"input\",\n            clearable: true\n          },\n          {\n            label: \"检修项目\",\n            value: \"jxxm\",\n            type: \"input\",\n            clearable: true\n          },\n          /*{\n            label: '检修分类',\n            value: 'jxflArr',\n            type: 'select',\n            // type: 'checkbox',\n            // checkboxValue: [],\n            multiple: true,\n            options: [\n              { label: '全部', value: '' },\n              { label: '测试1', value: '1' },\n              { label: '测试2', value: '0' }\n            ],\n            clearable: true\n          },*/\n          {\n            label: \"检修分类\",\n            value: \"jxfl\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            multiple: true,\n            options: [],\n            clearable: true\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"设备分类\", prop: \"sbflmc\", minWidth: \"80\" },\n          { label: \"检修分类\", prop: \"jxflName\", minWidth: \"80\" },\n          { label: \"项目编号\", prop: \"xmbh\", minWidth: \"100\" },\n          { label: \"检修项目\", prop: \"jxxm\", minWidth: \"150\" }\n          /*  {\n            prop: 'operation',\n            label: '操作',\n            minWidth: '130px',\n            style: { display: 'block' },\n            //操作列固定再右侧\n            fixed: 'right',\n            operation: [\n              { name: '修改', clickFun: this.updateRow },\n              { name: '详情', clickFun: this.getInfo }\n            ]\n          }*/\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        sbflArr: [],\n        jxflArr: [],\n        xmbh: \"\",\n        xmmc: \"\"\n      },\n      selectRows: [],\n      //检修分类下拉框数据\n      jxflList: [],\n      DevicesListGroup: [],\n      rules: {\n        sbfl: [\n          { required: true, message: \"设备分类不能为空\", trigger: \"change\" }\n        ],\n        jxfl: [\n          { required: true, message: \"检修分类不能为空\", trigger: \"change\" }\n        ],\n        xmbh: [\n          { required: true, message: \"项目编号不能为空\", trigger: \"blur\" }\n        ],\n        jxxm: [{ required: true, message: \"检修项目不能为空\", trigger: \"blur\" }]\n      },\n      showDeviceTree: false,\n      isFilter: false\n    };\n  },\n  mounted() {\n    this.initDomain();\n    this.getDeviceClassGroup();\n  },\n  watch: {\n    isShowDetails(val) {\n      if (val) {\n        const el = this.$el.querySelector(\".el-dialog\");\n        el.style.left = 0;\n        el.style.top = 0;\n      }\n    }\n  },\n  created() {\n    //列表查询\n    this.getData();\n  },\n  methods: {\n    //列表查询\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        this.params = param;\n        // param.sbflArr = param.sbfl === '' ? [] : param.sbflArr\n        const { data, code } = await getList(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n\n          this.tableAndPageInfo.tableData.forEach(item => {\n            this.jxflList.forEach(element => {\n              if (item.jxfl === element.value) {\n                item.jxflName = element.label;\n                return;\n              }\n            });\n          });\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //重置按钮\n    getReset() {\n      this.params = {\n        sbflArr: [],\n        jxflArr: [],\n        jxfl: [],\n        xmbh: \"\",\n        xmmc: \"\"\n      };\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n    },\n    //选中行\n    handleSelectionChange() {},\n    //详情\n    getDetails() {\n      this.title = \"检修项目详情\";\n      this.isDisabled = true;\n      this.isShowDetails = true;\n      this.form = { ...row };\n    },\n    //新增\n    getInster() {\n      this.title = \"检修项目新增\";\n      this.isDisabled = false;\n      this.form = {};\n      this.isShowDetails = true;\n    },\n    //编辑按钮\n    updateRow(row) {\n      this.title = \"检修项目修改\";\n      this.isDisabled = false;\n      this.form = { ...row };\n      this.isShowDetails = true;\n    },\n    //详情按钮\n    getInfo(row) {\n      this.title = \"详情查看\";\n      this.form = { ...row };\n      this.isDisabled = true;\n      this.isShowDetails = true;\n    },\n    async saveRow() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          saveOrUpdate(this.form).then(res => {\n            try {\n              if (res.code === \"0000\") {\n                this.$message.success(\"操作成功\");\n              }\n            } catch (e) {\n              console.log(e);\n            }\n            this.getData();\n          });\n        } else {\n          return false;\n        }\n        this.isShowDetails = false;\n      });\n    },\n    //删除按钮\n    async deleteRow(row) {\n      this.form = { ...row };\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove([this.form.objId]).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n      this.getData();\n    },\n    //filter_change事件\n    handleEvent(val, val1) {\n      //变电站查询下拉框选项根据所选分公司带出来\n      if (val.label === \"sbfl\" && val.value) {\n        this.params.sbflArr = val.value;\n        console.log(\"sbflArr\", this.params);\n      }\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false;\n    },\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n    showDeviceTreeDialog() {\n      this.isFilter = false;\n      this.showDeviceTree = true;\n    },\n    async initDomain() {\n      let { data: jxfl } = await getDictTypeData(\"jxfl\");\n      this.jxflList = jxfl;\n\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.value === \"jxfl\") {\n          item.options = jxfl;\n          return;\n        }\n      });\n    },\n    getDeviceClassGroup() {\n      getDeviceClassGroup([\"bdsb\", \"pdsb\", \"sdsb\"]).then(res => {\n        if (res.code === \"0000\") {\n          this.DevicesListGroup = res.data;\n          this.filterInfo.fieldList.forEach(item => {\n            if (item.value === \"sbfl\") {\n              item.options = res.data;\n              return;\n            }\n          });\n        } else {\n          this.$message.error(\"获取设备分类失败\");\n        }\n      });\n    },\n    inputFocusEvent(val) {\n      if (val.target.name === \"sbfl\") {\n        this.showDeviceTree = true;\n        this.isFilter = true;\n      }\n    },\n    getDeviceTypeData(res) {\n      if (this.isFilter) {\n        this.filterInfo.data.sbflArr = [];\n        this.filterInfo.data.sbfl = \"\";\n        res.forEach(item => {\n          if (item.checked) {\n            this.filterInfo.data.sbflArr.push(item.code);\n            this.filterInfo.data.sbfl += item.name + \",\";\n          }\n        });\n\n        this.filterInfo.data.sbfl = this.filterInfo.data.sbfl.substring(\n          0,\n          this.filterInfo.data.sbfl.length - 1\n        );\n        this.showDeviceTree = false;\n      } else {\n        let treeNodes = [];\n        res.forEach(item => {\n          if (item.checked) {\n            treeNodes.push(item);\n          }\n        });\n        if (treeNodes.length === 1) {\n          this.form.sbflmc = treeNodes[0].name;\n          this.form.sbfl = treeNodes[0].code;\n          this.showDeviceTree = false;\n        } else {\n          this.$message.warning(\"请选择单条设备数据\");\n        }\n      }\n    },\n    closeDeviceTypeDialog() {\n      this.showDeviceTree = false;\n    },\n    //清空表单数据\n    handleClose() {\n      this.form = {};\n      this.$nextTick(() => {\n        this.form = this.$options.data().form;\n        this.resetForm(\"form\");\n      });\n    }\n  }\n};\n</script>\n\n<style scoped>\n.productGroupSelector-group {\n  padding-bottom: 0;\n  padding-top: 32px;\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  width: 400px;\n}\n\n/deep/ .el-select-group__title {\n  font-size: 24px;\n}\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/jxbzk"}]}