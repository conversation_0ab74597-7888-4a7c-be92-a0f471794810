{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\xldzcz\\components\\czp_tj.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\xldzcz\\components\\czp_tj.vue", "mtime": 1748603097178}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["czp_tj.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAkBA;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,QADA;AAEA,EAAA,OAFA,qBAEA;AACA;AACA,SAAA,OAAA,GAFA,CAGA;;AACA,SAAA,gBAAA;AACA,GAPA;AAQA,EAAA,IARA,kBAQA;AACA,WAAA;AACA;AACA,MAAA,OAAA,EAAA,EAFA;AAGA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA;AAFA,OAJA;AAQA;AACA,MAAA,KAAA,EAAA,CATA;AAUA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,IAAA,EAAA;AADA,SADA;AAGA;AACA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA,IAAA;AAAA,UAAA,UAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAJA,OAVA;AAkBA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,IAAA,EAAA,SAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,IAAA,EAAA,SAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,CARA;AAcA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAdA,OAlBA;AAkCA,MAAA,MAAA,EAAA;AACA,QAAA,EAAA,EAAA,CADA;AAEA,QAAA,MAAA,EAAA;AAFA;AAlCA,KAAA;AAuCA,GAhDA;AAiDA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,OAFA,mBAEA,MAFA,EAEA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,KAFA,+DAEA,KAAA,CAAA,MAFA,GAEA,MAFA;AAAA;AAAA,uBAGA,wBAAA,KAAA,CAHA;;AAAA;AAAA;AAGA,gBAAA,IAHA,oBAGA,IAHA;AAGA,gBAAA,IAHA,oBAGA,IAHA;AAIA,gBAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,IAAA;;AACA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,KAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,KAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AARA;AAAA;;AAAA;AAAA;AAAA;AAUA,gBAAA,OAAA,CAAA,GAAA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,KAdA;;AAeA;;;AAGA,IAAA,YAlBA,wBAkBA,SAlBA,EAkBA;AACA,WAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,KAAA;AAAA,OAAA,CAAA;AACA,WAAA,MAAA,GAAA,SAAA,CAAA,MAAA,KAAA,CAAA;AACA,WAAA,QAAA,GAAA,CAAA,SAAA,CAAA,MAAA;AACA,KAtBA;;AAuBA;;;AAGA,IAAA,gBA1BA,8BA0BA;AAAA;;AACA,qCAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;;AACA,QAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,IAAA,OAAA,EAAA;AACA,mBAAA,IAAA,CAAA,OAAA,GAAA,MAAA,CAAA,OAAA;AACA;AACA,SAJA;AAKA,OAPA;AAQA,KAnCA;AAoCA;AACA,IAAA,WArCA,uBAqCA,GArCA,EAqCA;AAAA;;AACA;AACA,UAAA,GAAA,CAAA,KAAA,KAAA,KAAA,IAAA,GAAA,CAAA,KAAA,IAAA,GAAA,CAAA,KAAA,KAAA,EAAA,EAAA;AACA,YAAA,IAAA,GAAA;AACA,UAAA,MAAA,EAAA,GAAA,CAAA;AADA,SAAA;AAGA,uCAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,gBAAA,IAAA,CAAA,KAAA,IAAA,OAAA,EAAA;AACA,qBAAA,IAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;AACA;AACA,WAJA;AAKA,SANA;AAOA;AACA;AAnDA;AAjDA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 110, itemWidth: 220 }\"\n      @handleEvent=\"handleEvent\"\n    />\n\n    <el-white class=\"button-group\">\n      <div>\n        <comp-table :table-and-page-info=\"tableAndPageInfo\" @update:multipleSelection=\"selectChange\" height=\"60vh\"/>\n      </div>\n    </el-white>\n  </div>\n</template>\n\n<script>\nimport { getListTj, getBdzSelectList } from '@/api/yxgl/bdyxgl/bddzczp'\n\nexport default {\n  name: 'czp_tj',\n  mounted() {\n    //列表查询\n    this.getData()\n    //获取变电站下拉框数据\n    this.getBdzSelectList()\n  },\n  data() {\n    return {\n      // 查询变电站列表\n      bdzList: {},\n      //查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      // 查询数据总条数\n      total: 0,\n      filterInfo: {\n        data: {\n          xlmc: ''\n        },//查询条件\n        fieldList: [\n          { label: '线路名称', value: 'xlmc', type: 'input', clearable: true, filterable: true, options: [] }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: '线路名称', prop: 'xlmc', minWidth: '150' },\n          { label: '操作项数', prop: 'czxs', minWidth: '100' },\n          { label: '已执行项数', prop: 'yzxczxs', minWidth: '100' },\n          { label: '未执行项数', prop: 'wzxczxs', minWidth: '100' }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        lx: 1,\n        status: '4'\n      }\n    }\n  },\n  methods: {\n    //统计\n    async getData(params) {\n      try {\n        const param = { ...this.params, ...params }\n        const { data, code } = await getListTj(param)\n        console.log('统计到的数据：',data)\n        if (code === '0000') {\n          this.tableAndPageInfo.tableData = data.records\n          this.tableAndPageInfo.pager.total = data.total\n        }\n      } catch (e) {\n        console.log(e)\n      }\n    },\n    /**\n     * 列表选中\n     * */\n    selectChange(selection) {\n      this.ids = selection.map(item => item.objId)\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n    },\n    /**\n     * 获取变电站下拉框数据\n     */\n    getBdzSelectList() {\n      getBdzSelectList({}).then(res => {\n        this.bdzList = res.data\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == 'bdzmc') {\n            return item.options = this.bdzList\n          }\n        })\n      })\n    },\n    //下拉框change事件\n    handleEvent(val) {\n      //变电站查询下拉框选项根据所选分公司带出来\n      if (val.label === 'fgs' && val.value && val.value !== '') {\n        let form ={\n          ssdwbm: val.value\n        }\n        getBdzSelectList(form).then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value == 'bdzmc') {\n              return item.options = res.data;\n            }\n          })\n        })\n      }\n    },\n  }\n}\n</script>\n\n<style scoped>\n\n/*列表颜色设置*/\n/deep/ .el-table th {\n  background-color: #e8f7f0;\n}\n</style>\n"], "sourceRoot": "src/views/dagangOilfield/czpgl/xldzcz/components"}]}