<template>
  <div class="" id="dialogActstdiv">
    <el-row class="sbqx">
      <!--   Tab页签   -->
      <el-col :span="24" class="sbqx_box">
        <div class="txtTitle">
          <span
            @click="click('bdqx')"
            :class="this.flag === 'bdqx' ? 'tabActive' : 'noActive'"
            class="oneBtn"
          >
            <span class="allBtn">变电隐患</span>
          </span>
          <span
            @click="click('xlqx')"
            :class="this.flag === 'xlqx' ? 'tabActive' : 'noActive'"
            class="twoBtn"
          >
            <span class="allBtn">线路隐患</span>
          </span>
          <span
            @click="click('pdqx')"
            :class="this.flag === 'pdqx' ? 'tabActive' : 'noActive'"
            class="twoBtn"
          >
            <span class="allBtn">配电隐患</span>
          </span>
          <span
            @click="click('gfqx')"
            :class="this.flag === 'gfqx' ? 'tabActive' : 'noActive'"
            class="twoBtn"
          >
            <span class="allBtn">新能源隐患</span>
          </span>
        </div>
      </el-col>
      <!--   变电缺陷   -->
      <el-col :span="24" v-if="this.flag === 'bdqx'" class="sbqx_boxT">
        <qxgl_ys></qxgl_ys>
      </el-col>
      <!--   线路缺陷   -->
      <el-col :span="24" v-if="this.flag === 'xlqx'" class="sbqx_boxT">
        <qxgl_xl></qxgl_xl>
      </el-col>
      <!--   配电缺陷   -->
      <el-col :span="24" v-if="this.flag === 'pdqx'" class="sbqx_boxT">
        <qxgl_pd></qxgl_pd>
      </el-col>
      <!--   供服缺陷   -->
      <el-col :span="24" v-if="this.flag === 'gfqx'" class="sbqx_boxT">
        <qxgl_gf></qxgl_gf>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import Qxgl_ys from "@/views/dagangOilfield/yxgl/bdyxgl/components/qxgl_ys";
import Qxgl_xl from "@/views/dagangOilfield/yxgl/bdyxgl/components/qxgl_xl";
import Qxgl_pd from "@/views/dagangOilfield/yxgl/bdyxgl/components/qxgl_pd";
import Qxgl_gf from "@/views/dagangOilfield/yxgl/gfyxgl/components/qxgl_gf";

export default {
  name: "sbqx",
  components: {
    Qxgl_pd,
    Qxgl_xl,
    Qxgl_ys,
    Qxgl_gf
  },

  data() {
    return {
      flag: "bdqx", //默认展示变电缺陷的内容
      flyjMap: new Map(),
      sbbjList: [], //设备部件list
      sbbwList: [], //设备部位list
      qxmsList: [], //缺陷描述list
      qxflList: [], //缺陷分类list
      qxflData: [], //缺陷分类所有数据
      jsyyList: [],
      //滚动条高度
      scroll: "",
      //当前显示的菜单区域
      istyle: -1,
      deptId: undefined,
      jsbutten: false,
      currUser: "",
      currUserdw: this.$store.getters.deptId,
      //提交审核按钮
      buttonTjshShow: false,
      zt: "",
      // 消项验收结论
      isShowYs: false,
      // 消除处理三个复选框
      isShowXqcl: false,
      // 生产科处理建议
      isShowScksh: false,
      // 分公司处理建议
      isShowFgssh: false,
      tableDatas: [],
      //巡视点位弹框
      isShowXsdw: false,
      //上传图片时的携带的其他参数
      uploadImgData: {
        businessId: "" //携带的表单主键id
      },
      //上传图片时的请求头
      header: {},
      //缺陷标准库dialog
      isShowQxbzDialog: false,
      //工作票
      isShowGzp: false,
      //使用当前设备类型编码查询缺陷标准库

      //主设备选择传递子组件参数
      selectedSbParam: {
        lx: "bd",
        sbmc: ""
      },
      //主设备弹出框
      isShowSysbDialog: false,
      //操作审核按钮
      shButtonControl: true,
      videoForm: {
        showVideoPath: ""
      },
      //展示图片dialog控制
      imgDialogVisible: false,
      //图片地址url
      dialogImageUrl: "",
      //图片list
      imgList: [],
      //消项验收
      xqimgList: [],
      //缺陷类别
      qxlbOptions: [
        { label: "变电", value: "变电" },
        { label: "配电", value: "配电" },
        { label: "输电", value: "输电" }
      ],
      //验收结论
      ysjlDisabled: false,
      //隐藏部分card数据
      fgsShCardShow: false,
      //弹出框中表格数据
      propTableData: {
        sel: null, // 选中行
        colFirst: []
      },
      //锚点跳转按钮名称
      // buttonArr: ['缺陷上报', '缺陷描述信息', '监控应急措施','消项处理', '消项验收'],
      buttonArr: [
        "缺陷上报",
        "缺陷描述信息",
        "监控应急措施",
        "班组审核",
        "分公司审核",
        "生产科审核",
        "消项处理",
        "消项验收"
      ],
      activeBtn: 0,
      assetSelect: false,
      //设备部位
      sbbwOptions: [
        { label: "本体端子箱", value: "本体端子箱" },
        { label: "储油柜", value: "储油柜" },
        { label: "呼吸器", value: "呼吸器" }
      ],
      //弹出框内新增时下拉框变电站数据
      bdzDataListOptions: [
        { label: "35kV然气站变电站", value: "35kV然气站变电站" },
        { label: "110kV然气站变电站", value: "110kV然气站变电站" },
        { label: "10kV然气站变电站", value: "10kV然气站变电站" }
      ],
      //弹出框内新增时归属下拉框数据
      gsOptionsDataList: [
        { label: "运行", value: "运行" },
        { label: "分公司", value: "分公司" },
        { label: "班组", value: "班组" }
      ],
      // 发现方式选项
      findWayOptions: [
        { value: "在线监测", label: "在线监测" },
        { value: "人工发现", label: "人工发现" },
        { value: "日常巡视", label: "日常巡视" }
      ],
      // 检测技术选项
      detectingOptions: [
        { value: "视频监控", label: "视频监控" },
        { value: "人工判断", label: "人工判断" },
        { value: "红外识别", label: "红外识别" }
      ],
      // 设备部件选项
      partsOptions: [
        { value: "本体", label: "本体" },
        { value: "非电量保护", label: "非电量保护" },
        { value: "基础", label: "基础" },
        { value: "冷却器系统", label: "冷却器系统" },
        { value: "分接开关", label: "分接开关" },
        { value: "套管", label: "套管" }
      ],
      // 缺陷性质选项
      defectQualityOptions: [
        { value: "一般", label: "一般" },
        { value: "严重", label: "严重" },
        { value: "危急", label: "危急" }
      ],
      //所属分公司下拉框数据
      allFgsList: [],
      //所属分公司下拉框数据
      ssgsOptionsDataList: [],
      //变电分公司对象
      bdfgsObjArr: [],
      //弹出框控制内容disabled
      dialogFormDisabled: false,
      dialogFormDisabledst: false,
      dialogFormDisabledbz: false, //班组审核意见
      //是否禁用后续编辑,默认禁用
      isHistoryDisabled: true,
      // 下拉树筛选文字
      filterText: "",
      zsb: "",
      defaultProps: {
        children: "children",
        label: "label"
      },
      //选中得行数
      selectRows: [],
      // 查询数据总条数
      total: 0,
      // 对话框标题
      title: "",
      // 对话框是否打开
      open: false,
      // 新增/修改表单
      form: {
        id: undefined,
        substation: undefined,
        mainDevice: undefined,
        defectId: undefined,
        deviceType: undefined,
        deviceModel: undefined,
        manufacturer: undefined,
        runNumber: undefined,
        findDate: undefined,
        attribution: undefined,
        enterPerson: undefined,
        enterDept: undefined,
        findWay: undefined,
        detecting: undefined,
        findPerson: undefined,
        parts: undefined,
        defectDescription: undefined,
        classifyGist: undefined,
        defectQuality: undefined,
        defectContent: undefined,
        reason: undefined,
        condition: undefined,
        remark: undefined,
        aqyxcqcs: undefined,
        xsjcyq: undefined,
        gzqk: undefined,
        yjczfa: undefined,
        qtyq: undefined,
        fgsshr: undefined,
        fgsshsj: undefined,
        qrdqxfl: undefined,
        fgscljy: undefined,
        sckshr: undefined,
        sckshsj: undefined,
        sckcljy: undefined,
        xqfzr: undefined,
        xqfzrdw: undefined,
        xqclsj: undefined,
        xqcljg: undefined,
        xqylwt: undefined,
        xqysr: undefined,
        xqyssj: undefined,
        ysjl: undefined,
        ysjg: undefined,
        bzfzr: "",
        bz: "",
        //变电
        lx: 2,
        xqfzrldsj: ""
      },
      lrrdw: undefined,
      xqfzrdw: undefined,
      // 多选框选中的数据id
      ids: [],
      // 是否单选
      single: true,
      // 是否多选
      multiple: true,
      // 选中的数据
      selectData: [],
      // 归属选项
      attributionOptions: [{ value: "a1", label: "a1" }],

      // 缺陷描述选项
      defectDescriptionOptions: [{ value: "a1", label: "a1" }],
      // 分类依据
      classifyGistOptions: [{ value: "a1", label: "a1" }],

      // 表单校验
      rules: {
        qxnr: [{ required: true, message: "请输入缺陷内容", trigger: "blur" }],
        qxlb: [
          { required: true, message: "请选择缺陷类别", trigger: "select" }
        ],
        ssgs: [{ required: true, message: "请选择分公司", trigger: "select" }],
        ssdz: [{ required: true, message: "请选择所属位置", trigger: "blur" }],
        sbxh: [{ required: true, message: "请输入设备型号", trigger: "blur" }],
        sccj: [{ required: true, message: "请输入生产厂家", trigger: "blur" }],
        bzqxBj: [
          { required: true, message: "请输入设备部件", trigger: "blur" }
        ],
        bzqxBw: [
          { required: true, message: "请输入设备部位", trigger: "blur" }
        ],
        bzqxQxms: [
          { required: true, message: "请输入缺陷描述", trigger: "blur" }
        ],
        bzqxFlyj: [
          { required: true, message: "请输入分类依据", trigger: "blur" }
        ],
        // xsdw: [{ required: true, message: "请输入巡视点位", trigger: "blur" }],
        sb: [{ required: true, message: "请选择主设备", trigger: "blur" }],
        sblx: [{ required: true, message: "请选择设备类型", trigger: "blur" }],
        fxrq: [
          { required: true, message: "请选择发现日期", trigger: "change" }
        ],
        // xsdw: [
        //   { required: true, message: "请选择巡视点位", trigger: "change" },
        // ],
        fxr: [{ required: true, message: "请输入发现人", trigger: "change" }],
        fxfs: [
          { required: true, message: "请选择发现方式", trigger: "change" }
        ],
        jcjs: [
          { required: true, message: "请选择检测技术", trigger: "change" }
        ],
        sbbj: [
          { required: true, message: "请选择获取标准库", trigger: "change" }
        ]
      },
      // 表单是否可编辑
      isEditable: true,
      filterInfo: {
        data: {
          ssgs: [],
          ssdz: "",
          qxxz: "",
          sbxh: "",
          sb: "",
          sblx: "",
          lczt: []
        },
        fieldList: [
          {
            label: "所属公司",
            type: "select",
            value: "ssgs",
            multiple: true,
            options: [
              { value: "港东变电分公司", label: "港东变电分公司" },
              { value: "港中变电分公司", label: "港中变电分公司" },
              { value: "南部变电分公司", label: "南部变电分公司" },
              { value: "线路分公司", label: "线路分公司" },
              { value: "配电运维分公司", label: "配电运维分公司" }
            ]
          },
          { label: "所属位置", type: "input", value: "ssdz" },
          { label: "设备名称", type: "input", value: "sb" },
          { label: "设备类型", type: "input", value: "sblx" },
          {
            label: "缺陷性质",
            type: "select",
            value: "qxxz",
            options: [
              { label: "一般", value: "一般" },
              { label: "严重", value: "严重" },
              { label: "危急", value: "危急" }
            ]
          },
          {
            label: "状态",
            type: "select",
            value: "lczt",
            multiple: true,
            options: [
              { label: "待上报", value: "1" },
              { label: "历史录入", value: "0" },
              { label: "分公司审核", value: "2" },
              { label: "生产科审核", value: "3" },
              { label: "检修安排", value: "4" },
              { label: "待处理", value: "5" },
              { label: "待验收", value: "6" },
              { label: "已消项", value: "7" }
            ]
          },
          { label: "设备型号", type: "input", value: "sbxh" }
        ]
      },
      tableAndPageInfo: {
        pager: {
          pageSize: 10,
          pageNum: 1,
          total: 0,
          pageResize: "",
          sizes: [10, 20, 50, 100]
        },
        option: {
          checkBox: true,
          serialNumber: true
        },
        tableData: [],
        tableHeader: [
          { prop: "ssgs", label: "所属公司", minWidth: "120" },
          { prop: "ssdz", label: "所属位置", minWidth: "120" },
          { prop: "sb", label: "主设备", minWidth: "120" },
          { prop: "sblx", label: "设备类型", minWidth: "100" },
          { prop: "bzqxQxdj", label: "缺陷性质", minWidth: "120" },
          { prop: "ztmc", label: "状态", minWidth: "120" },
          // {prop: 'dydj', label: '电压等级', minWidth: '100'},
          { prop: "sbxh", label: "设备型号", minWidth: "120" },
          { prop: "sccj", label: "生产厂家", minWidth: "120" },
          // { prop: "gzpbh", label: "工作票编号", minWidth: "120" },
          { prop: "createTime", label: "创建时间", minWidth: "150" }
        ]
      },
      params: {
        //变电
        lx: 2,
        lczt: "1,2,3,4,5,6,7,8,9,11" //查看所有状态数据
      },
      loading: null,
      openSb: false,
      form1: {},
      //工作流传入参数
      processData: {
        processDefinitionKey: "qxlccs",
        businessKey: "",
        businessType: "缺陷管理",
        variables: {},
        defaultFrom: true,
        nextUser: "",
        processType: "complete"
      },
      activitiOption: { title: "上报" },
      timeData: [],
      timeLineShow: false,
      isShow: false,
      openLoadingImg: false,
      imgSrc: "",

      //线路分公司数组
      lineFgsArr: ["线路分公司"],
      //配电分公司数组
      pdFgsArr: ["配电运维分公司"],
      //弹出框内新增时下拉框所属位置数据
      wzDataListOptions: [],
      //获取设备类型弹出框
      showSblxTree: false,
      //获取设备类型弹出框传递参数
      selectSbp: [],
      //判断从哪点击的设备类型弹出框
      isFilter: false,
      //巡视点位下拉框数据
      xsdwOptions: [],

      tempFileName: "设备缺陷表单",

      //视频上传进度条
      videoFlag: false,
      //是否显示视频进度条
      videoUploadPercent: "",
      videoList: [],
      paramss: {},
      xsdwparams: {
        sswz: "",
        sbmc: "",
        xsdw: ""
      },
      isDz: false,
      qxlb: "变电"
    };
  },
  methods: {
    click(mainTab) {
      this.flag = mainTab;
    }
  }
};
</script>

<style scoped lang="scss">
.sbqx_box {
  padding: 20px 0 0 20px;
}
.sbqx_boxT {
  margin-top: -18px;
}
.tabActive {
  width: 10%;
  float: left;
  color: #fff;
  background: #02b988;
  border-top: 0;
}
.noActive {
  width: 10%;
  float: left;
  background: #fff;
  color: #545252;
  &:hover {
    background: #ffffff;
    color: #359076;
  }
}
.oneBtn {
  margin-right: -15px;
}
.twoBtn {
  transform: skewX(33deg);
  border-right: 1px solid #9a989869;
  .allBtn {
    transform: skewX(-33deg);
    display: inline-block;
  }
}
</style>
