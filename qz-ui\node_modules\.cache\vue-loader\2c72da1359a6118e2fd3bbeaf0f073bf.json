{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\pjbbkwh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\pjbbkwh.vue", "mtime": 1706897323222}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["pjbbkwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "pjbbkwh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n<!--    <el-white>-->\n<!--      <el-filter-->\n<!--        :data=\"filterInfo.data\"-->\n<!--        :field-list=\"filterInfo.fieldList\"-->\n<!--        @handleReset=\"getReset\"-->\n<!--      />-->\n<!--    </el-white>-->\n    <div>\n      <el-white class=\"button-group\">\n        <div class=\"button_btn\">\n          <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"getInster\"\n          >新增</el-button>\n          <el-button type=\"danger\" icon=\"el-icon-delete\"  @click=\"getDelete\"\n          >删除</el-button>\n          <!--              <el-button type=\"warning\" icon=\"el-icon-edit\" @click=\"getUpdate\"-->\n          <!--              >修改</el-button>-->\n          <!--              <el-button type=\"cyan\" icon=\"el-icon-download\" @click=\"getDetails\"-->\n          <!--              >导出</el-button>-->\n        </div>\n        <comp-table :table-and-page-info=\"tableAndPageInfo\" @multipleSelection=\"handleSelectionChange\"/>\n      </el-white>\n    </div>\n\n    <!--新增、修改、详情弹框-->\n    <el-dialog :title=\"title\" :visible.sync=\"show\" width=\"50%\" append-to-body @close=\"getInsterClose\" v-dialogDrag>\n      <el-form label-width=\"130px\" ref=\"form\" :model=\"form\">\n        <el-row :gutter=\"8\" class=\"pull-left\" >\n          <el-col :span=\"12\">\n            <el-form-item  label=\"评价导则：\" prop=\"scjxlb\">\n              <el-select placeholder=\"请选择评价导则\" v-model=\"form.scjxlb\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item  label=\"版本名称：\" prop=\"jyjxlb\">\n              <el-select placeholder=\"请选择版本名称\" v-model=\"form.jyjxlb\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item  label=\"版本号：\" prop=\"cljy\">\n              <el-input v-model=\"form.cljy\" placeholder=\"请输入版本号\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item  label=\"是否默认版本：\" prop=\"jyjxlb\">\n              <el-select placeholder=\"请选择是否默认版本\" v-model=\"form.jyjxlb\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item  label=\"是否启用：\" prop=\"jyjxlb\">\n              <el-select placeholder=\"请选择是否启用\" v-model=\"form.jyjxlb\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item  label=\"创建时间：\" prop=\"jyjxlb\">\n              <el-date-picker\n                type=\"datetime\"\n                placeholder=\"选择日期时间\"\n                align=\"right\" style=\"width: 100%\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"getInsterClose\">关 闭</el-button>\n        <el-button type=\"primary\" >保 存</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n    export default {\n      name: \"pjbbkwh\",\n      data(){\n        return{\n          //新增按钮form表单\n          form:{\n          },\n          title:'',\n          show:false,\n          filterInfo: {\n            data: {\n              ywdwArr: [],\n            },\n            fieldList: [\n              {label: '设备类型', type: 'select', value: 'roleName', multiple: true, options: []},\n            ]\n          },\n          tableAndPageInfo: {\n            pager: {\n              pageSize: 10,\n              pageNum: 1,\n              total: 0,\n              sizes: [10, 20, 50, 100]\n            },\n            option: {\n              checkBox: true,\n              serialNumber: true\n            },\n            tableData: [],\n            tableHeader: [\n              {prop: 'ssgs', label: '评价导则', minWidth: '120'},\n              {prop: 'bdzmc', label: '版本名称', minWidth: '180'},\n              {prop: 'dydj', label: '版本号', minWidth: '120'},\n              {prop: 'sbzt', label: '是否默认版本', minWidth: '250'},\n              {prop: 'sbzt', label: '是否启用', minWidth: '250'},\n              {prop: 'sbzt', label: '创建时间', minWidth: '250'},\n              {\n                prop: 'operation',\n                label: '操作',\n                minWidth: '130px',\n                style: {display: 'block'},\n                //操作列固定再右侧\n                fixed:'right',\n                operation: [\n                  {name: '修改', clickFun: this.getDetails},\n                  {name: '详情', clickFun: this.getDetails},\n                ]\n              },\n            ]\n          },\n        }\n      },\n      create(){\n\n      },\n\n      mounted(){\n\n      },\n      methods:{\n        getDetails(){\n          this.title='详情'\n          this.show=true\n        },\n\n        getXzsbpjClose(){\n          this.show=false\n          //设置table切换默认进来显示那个table\n          this.activeName = 'first'\n        },\n        //新增按钮\n        getInster(){\n          this.show=true\n          this.title = '新增'\n        },\n        //新增弹框关闭\n        getInsterClose(){\n          this.show=false\n        },\n\n      }\n    }\n</script>\n\n<style scoped>\n\n</style>\n"]}]}