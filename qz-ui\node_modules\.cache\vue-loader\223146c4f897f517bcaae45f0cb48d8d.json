{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\jbwh_xxd.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\jbwh_xxd.vue", "mtime": 1706897323219}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["jbwh_xxd.vue"], "names": [], "mappings": ";;;;;;;;;;AAUA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "jbwh_xxd.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk", "sourcesContent": ["<template>\n  <comp-table\n    :table-and-page-info=\"tableAndPageInfo\"\n    @rowDbClick=\"clickMainTable\"\n    height=\"58vh\"\n    id=\"table_xxd\"\n  />\n</template>\n\n<script>\nimport { Loading } from 'element-ui'\nimport { getPageZtlxxd } from '@/api/dagangOilfield/bzgl/sbztpjbzk/ztlxxd'\n\nexport default {\n  name: 'jbwh_xxd',\n  props: {\n    ssztlId:{\n      type:String,\n      default:'',\n    },\n  },\n  data() {\n    return {\n      params:{\n\n      },\n      loading: null,//遮罩层\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n        },\n        option: {\n          serialNumber: true,\n        },\n        tableData: [],\n        tableHeader: [\n          // { prop: \"xxdbm\", label: \"信息点编码\"},\n          { prop: \"objId\", label: \"信息点id\"},\n          { prop: \"xxdmc\", label: \"信息点名称\" },\n        ],\n      },\n    };\n  },\n  mounted() {\n    this.getData();\n  },\n  methods:{\n    async getData(param) {\n      //开启遮罩层\n      this.loading = Loading.service({\n        lock: true, //lock的修改符--默认是false\n        text: \"加载中，请稍后\", //显示在加载图标下方的加载文案\n        spinner: \"el-icon-loading\", //自定义加载图标类名\n        background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n        target: document.querySelector(\"#table_xxd\"),\n      });\n      this.params = {...this.params,...param}\n      this.params.ssztlId = this.ssztlId\n      getPageZtlxxd( this.params).then(res => {\n        this.tableAndPageInfo.tableData = res.data.records\n        this.tableAndPageInfo.pager.total = res.data.total\n        this.loading.close();//关闭遮罩层\n      })\n    },\n    clickMainTable(val){\n      let map = new Map();\n      // map.set(val.xxdbm,val.xxdmc);\n      // this.$emit('dbClickRow','getXxdData(' + val.xxdbm + ')',map);\n      map.set(val.objId,val.xxdmc);\n      this.$emit('dbClickRow','getXxdData(' + val.objId + ')',map);\n    },\n  },\n}\n</script>\n\n<style scoped>\n\n</style>\n"]}]}