{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\ysbzkgl\\acceptanceDetail.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\ysbzkgl\\acceptanceDetail.vue", "mtime": 1706897323967}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["acceptanceDetail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "acceptanceDetail.vue", "sourceRoot": "src/views/dagangOilfield/bzgl/ysbzkgl", "sourcesContent": ["<template>\n  <div>\n    <el-white class=\"button-group\">\n      <div class=\"button_btn\">\n        <el-button type=\"primary\" icon=\"el-icon-plus\" v-hasPermi=\"['bzysbzk:button:add']\" @click=\"addDetailData\" :disabled=\"!isCanAdd\">新增</el-button>\n        <el-button type=\"danger\" icon=\"el-icon-delete\" v-hasPermi=\"['bzysbzk:button:delete']\" @click=\"deleteDetailData\" :disabled=\"selectedRowData.length===0\">\n          删除\n        </el-button>\n      </div>\n      <div class=\"button_btn\">验收项</div>\n      <!-- <el-table ref=\"detailTable\" stripe border v-loading=\"detailLoading\" :data=\"detailTableData\"\n        @selection-change=\"detailSelectionChange\" @row-click=\"detailRowClick\">\n        <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n        <el-table-column label=\"验收项目\" align=\"center\" prop=\"ysx\" width=\"200\"/>\n        <el-table-column label=\"验收标准\" align=\"center\" prop=\"ysbz\"\n          style=\"overflow: hidden;text-overflow: ellipsis;white-space: nowrap\" :show-overflow-tooltip=\"true\">\n          <template slot-scope=\"scope\">\n            <el-popover v-if=\"scope.row.ysbz.length>55\" trigger=\"hover\" placement=\"top\"\n              style=\"overflow: hidden;text-overflow: ellipsis;white-space: nowrap;\">\n              {{ scope.row.ysbz }}\n              <div slot=\"reference\">\n                {{ scope.row.ysbz.substring(0,55)+'...' }}\n              </div>\n            </el-popover>\n            <span v-else>\n              {{ scope.row.ysbz}}\n            </span>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"检查方式\" align=\"center\" prop=\"jcfs\" :show-overflow-tooltip=\"true\" width=\"120\"/>\n        <el-table-column label=\"验收结论\" align=\"center\" prop=\"ysjl\" :show-overflow-tooltip=\"true\" width=\"120\"/>\n        <el-table-column label=\"备注\" align=\"center\" prop=\"bz\" :show-overflow-tooltip=\"true\" width=\"120\"/> -->\n      <comp-table\n            ref=\"detailTable\"\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"detailSelectionChange\"\n            v-loading=\"detailLoading\"\n      >\n        <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              :resizable=\"false\"\n              width=\"120\"\n            >\n        <!-- <el-table-column label=\"操作\" fixed=\"right\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"180\"> -->\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" v-hasPermi=\"['bzysbzk:button:update']\" @click=\"updateDetailData(scope.row)\" title=\"修改\" class='el-icon-edit'></el-button>\n            <el-button type=\"text\" @click=\"showDetailData(scope.row)\" title=\"详情\" class=\"el-icon-view\"></el-button>\n          </template>\n        </el-table-column>\n      <!-- </el-table> -->\n      </comp-table>\n\n      <!-- <pagination v-show=\"detailParams.total>0\" :total=\"detailParams.total\" :page.sync=\"detailParams.pageNum\"\n        :limit.sync=\"detailParams.pageSize\" @pagination=\"getData\" /> -->\n    </el-white>\n\n    <dialogForm ref=\"detailForm\" label-width=\"150px\" out-width=\"50%\" :reminder=\"reminder\" :rows=\"rows\"\n      @save=\"saveDetailData\" />\n  </div>\n</template>\n\n<script>\nimport { deleteBzYsbzmx, getBzYsbzmx, saveOrUpdateBzYsbzmx } from '@/api/bzgl/ysbzk/ysbzk'\nimport dialogForm from 'com/dialogFrom/dialogForm'\n\nexport default {\n  components: { dialogForm },\n  name: 'acceptanceDetail',\n  data() {\n    return {\n      //弹出框form表单每行显示几个\n      rows: 2,\n      //新增或修改标题\n      reminder: '新增',\n      //明细按钮加载\n      detailLoading: false,\n      //验收标准明细表查询条件\n      detailParams: {\n        pageNum: 1,\n        pageSize: 10,\n        total: 0,\n        //验收标准主表id\n        ysbzid: ''\n      },\n      //明细表新增表单数据\n      formList: [\n        {\n          label: '验收项：',\n          value: '',\n          type: 'input',\n          name: 'ysx',\n          default: true,\n          rules: { required: true, message: '请输入',trigger: 'blur' }\n        },\n        {\n          label: '验收标准：',\n          value: '',\n          type: 'textarea',\n          name: 'ysbz',\n          default: true,\n          rules: { required: true, message: '请输入',trigger: 'blur' }\n        },\n        {\n          label: '检查方式：',\n          value: '',\n          type: 'input',\n          name: 'jcfs',\n          default: true,\n          rules: { required: true, message: '请输入',trigger: 'blur' }\n        },\n        {\n          label: '验收结论：',\n          value: '',\n          type: 'textarea',\n          name: 'ysjl',\n          default: true,\n          rules: { required: true, message: '请输入',trigger: 'blur' }\n        },\n        {\n          label: '备注：',\n          value: '',\n          name: 'bz',\n          default: true,\n          type: 'textarea',\n        },\n        {\n          label: '主键id：',\n          value: '',\n          name: 'id',\n          default: false,\n          type: 'input',\n          hidden: false,\n        },\n        {\n          label: '验收标准id：',\n          value: '',\n          name: 'ysbzid',\n          default: false,\n          type: 'input',\n          hidden: false,\n          rules: { required: false }\n        }\n      ],\n      //验收标准明细表数据\n      // detailTableData: [],\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageResize: '',\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: false,\n          serialNumber: false\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: 'ysx', label: '验收项目',width: '200'},\n          { prop: 'ysbz', label: '验收标准', showPop: true},\n          { prop: 'jcfs', label: '检查方式',width: '200'},\n          { prop: 'ysjl', label: '验收结论',width: '100'},\n          { prop: 'bz', label: '备注',width: '100'},\n        ]\n      },\n      //新增按钮是否可用\n      isCanAdd: false,\n      //主表选中数据\n      mainTableSelectRows: [],\n      //详情表选中数据\n      selectedRowData: []\n    }\n  },\n  mounted() {\n  },\n  methods: {\n    //接受父组件传值方法\n    getMainTableSelectedRow(row) {\n      this.mainTableSelectRows = row\n      if (row.length === 1) {\n        this.isCanAdd = true\n        this.getData()\n      } else {\n        this.isCanAdd = false\n        this.detailLoading = true\n        this.tableAndPageInfo.tableData\n        this.detailLoading = false\n      }\n    },\n    //重置按钮\n    getReset() {\n      this.detailParams.pageNum = 1\n      this.detailParams.pageSize = 10\n    },\n    //获取验收标准明细表数据\n    getData(params) {\n      this.detailLoading = true\n      this.detailParams = { ...this.detailParams, ...params };\n      this.detailParams.ysbzid = this.mainTableSelectRows[0].id\n      this.detailParams.type = 0;\n      getBzYsbzmx(this.detailParams).then(res => {\n        // this.detailTableData = res.data.records\n        // this.detailParams.total = res.data.total\n        this.tableAndPageInfo.tableData = res.data.records;\n        this.tableAndPageInfo.pager.total = res.data.total\n        this.detailLoading = false\n      })\n    },\n\n    detailSelectionChange(row) {\n      this.selectedRowData = row\n    },\n\n    detailRowClick(val) {\n      this.$refs.detailTable.toggleRowSelection(val)\n    },\n\n    //保存明细表数据\n    saveDetailData(formData) {\n      let message = ''\n      if (formData.id === '' || !formData.id) {\n        message = '新增成功'\n        formData.ysbzid = this.mainTableSelectRows[0].id\n      } else {\n        message = '修改成功'\n      }\n      saveOrUpdateBzYsbzmx(formData).then(res => {\n        if (res.code === '0000') {\n          this.$message.success(message)\n          this.getData()\n        } else {\n          this.$message.error('操作失败')\n        }\n      })\n    },\n\n    //新增明细数据\n    addDetailData() {\n      this.reminder = '新增'\n\n      //初始话formList数据\n      // this.formList = this.$options.data().formList\n\n      const addForm = this.formList.map(item => {\n        return item\n      })\n      this.$refs.detailForm.showzzc(addForm)\n    },\n\n    //修改明细数据\n    updateDetailData(row) {\n      const updateList = this.formList.map(item => {\n        item.value = row[item.name]\n        return item\n      })\n      this.reminder = '修改'\n      this.$refs.detailForm.showzzc(updateList)\n    },\n\n    //明细数据详情\n    showDetailData(row) {\n      const infoList = this.formList.map(item => {\n        item.value = row[item.name]\n        return item\n      })\n      this.reminder = '详情'\n      this.$refs.detailForm.showzzc(infoList)\n    },\n\n    //删除明细信息\n    deleteDetailData() {\n      this.$confirm('确认删除选中数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        let ids = []\n        this.selectedRowData.forEach(item => {\n          ids.push(item.id)\n        })\n\n        deleteBzYsbzmx(ids).then(res => {\n          if (res.code === '0000') {\n            this.$message({\n              message: '删除成功',\n              type: 'success'\n            })\n          } else {\n            this.$message.error('操作失败')\n          }\n          this.getData()\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        })\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n\n</style>\n"]}]}