{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\syzxm.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\syzxm.js", "mtime": 1706897314376}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagang<PERSON>ilfield/bzgl/syzxm.js"], "names": ["baseUrl", "getPageKxzDataList", "query", "api", "requestPost", "remove", "removeKxzData", "getPageDataList", "saveOrUpdate", "getSblxTree", "requestGet", "saveOrUpdateKxzData"], "mappings": ";;;;;;;;;;;;;;;AAAA;;AAEA,IAAMA,OAAO,GAAG,cAAhB;AAGA;;;;;;AAKO,SAASC,kBAAT,CAA4BC,KAA5B,EAAmC;AACxC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,kCAA1B,EAA8DE,KAA9D,EAAqE,CAArE,CAAP;AACD;AAGD;;;;;;;AAKO,SAASG,MAAT,CAAgBH,KAAhB,EAAuB;AAC5B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,mBAA1B,EAA+CE,KAA/C,EAAsD,CAAtD,CAAP;AACD;AAED;;;;;;;AAKO,SAASI,aAAT,CAAuBJ,KAAvB,EAA8B;AACnC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,sBAA1B,EAAkDE,KAAlD,EAAyD,CAAzD,CAAP;AACD;AAED;;;;;;;AAKO,SAASK,eAAT,CAAyBL,KAAzB,EAAgC;AACrC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,+BAA1B,EAA2DE,KAA3D,EAAkE,CAAlE,CAAP;AACD;AAGD;;;;;;;AAKO,SAASM,YAAT,CAAsBN,KAAtB,EAA6B;AAClC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,qBAA1B,EAAiDE,KAAjD,EAAwD,CAAxD,CAAP;AACD;AAED;;;;;;;;AAOO,SAASO,WAAT,CAAqBP,KAArB,EAA4B;AACjC,SAAOC,iBAAIO,UAAJ,CAAeV,OAAO,GAAG,uBAAzB,EAAkDE,KAAlD,EAAyD,CAAzD,CAAP;AACD;AAGD;;;;;;;AAKO,SAASS,mBAAT,CAA6BT,KAA7B,EAAoC;AACzC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,4BAA1B,EAAwDE,KAAxD,EAA+D,CAA/D,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\n\nconst baseUrl = \"/manager-api\";\n\n\n/**\n * 分页获取枚举类试验子项目的枚举值列表信息\n * @param query\n * @returns {Promise | Promise<any>}\n */\nexport function getPageKxzDataList(query) {\n  return api.requestPost(baseUrl + '/syzxm/getSyzxmKxzDataListByPage', query, 2);\n}\n\n\n/***\n * 删除子项目\n * @param query\n * @returns {Promise | Promise<unknown>}\n */\nexport function remove(query) {\n  return api.requestPost(baseUrl + '/syzxm/removeData', query, 2)\n}\n\n/**\n * 删除枚举值数据\n * @param query\n * @returns {Promise | Promise<any>}\n */\nexport function removeKxzData(query) {\n  return api.requestPost(baseUrl + '/syzxm/removeKxzData', query, 2)\n}\n\n/**\n * 查询方法\n * @param query\n * @returns {Promise | Promise<unknown>}\n */\nexport function getPageDataList(query) {\n  return api.requestPost(baseUrl + '/syzxm/getSyzxmDataListByPage', query, 2);\n}\n\n\n/**\n * 添加或修改方法\n * @param query\n * @returns {Promise | Promise<unknown>}\n */\nexport function saveOrUpdate(query) {\n  return api.requestPost(baseUrl + '/syzxm/saveOrUpdate', query, 2);\n}\n\n/**\n *\n * 获取试验部位左侧树结构\n * @param query\n * @returns {Promise | Promise<any>}\n */\n\nexport function getSblxTree(query) {\n  return api.requestGet(baseUrl + '/sybw/getSblxTreeInfo', query, 2);\n}\n\n\n/**\n * 添加或修改方法\n * @param query\n * @returns {Promise | Promise<unknown>}\n */\nexport function saveOrUpdateKxzData(query) {\n  return api.requestPost(baseUrl + '/syzxm/saveOrUpdateKxzData', query, 2);\n}\n\n\n\n\n\n"]}]}