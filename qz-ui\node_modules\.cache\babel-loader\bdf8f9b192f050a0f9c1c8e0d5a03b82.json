{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\activiti\\leave.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\activiti\\leave.js", "mtime": 1706897313850}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMubGlzdCA9IGxpc3Q7CmV4cG9ydHMuc2F2ZSA9IHNhdmU7CmV4cG9ydHMuZGVsID0gZGVsOwpleHBvcnRzLmNvbW1pdCA9IGNvbW1pdDsKCnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwoKdmFyIGJhc2VVcmwgPSAiL2FjdGl2aXRpLWFwaSI7Ci8qKgogKiDliIbpobXmn6Xor6IKICogQHBhcmFtIHF1ZXJ5IOivt+axguWPguaVsAogKiBAcmV0dXJucyB7UHJvbWlzZTxhbnk+fQogKi8KCmZ1bmN0aW9uIGxpc3QocXVlcnkpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgIi9sZWF2ZS9saXN0IiwgcXVlcnksIDMpOwp9Ci8qKgogKiDkv53lrZgKICogQHBhcmFtIGRhdGEKICogQHJldHVybnMge1Byb21pc2UgfCBQcm9taXNlPGFueT59CiAqLwoKCmZ1bmN0aW9uIHNhdmUoZGF0YSkgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3RQb3N0KGJhc2VVcmwgKyAiL2xlYXZlL3NhdmUiLCBkYXRhLCAzKTsKfQovKioKICog5Yig6Zmk5pON5L2cCiAqIEBwYXJhbSBpZAogKiBAcmV0dXJucyB7UHJvbWlzZSB8IFByb21pc2U8YW55Pn0KICovCgoKZnVuY3Rpb24gZGVsKGlkKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdEdldChiYXNlVXJsICsgIi9sZWF2ZS9kZWxldGUvIiArIGlkLCBudWxsLCAzKTsKfQovKioKICog5o+Q5Lqk55Sz6K+3CiAqIEBwYXJhbSBpZAogKiBAcmV0dXJucyB7UHJvbWlzZSB8IFByb21pc2U8YW55Pn0KICovCgoKZnVuY3Rpb24gY29tbWl0KGlkKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdEdldChiYXNlVXJsICsgIi9sZWF2ZS9jb21taXQvIiArIGlkLCBudWxsLCAzKTsKfQ=="}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/activiti/leave.js"], "names": ["baseUrl", "list", "query", "api", "requestPost", "save", "data", "del", "id", "requestGet", "commit"], "mappings": ";;;;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,eAAhB;AACA;;;;;;AAKO,SAASC,IAAT,CAAcC,KAAd,EAAqB;AAC1B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,aAAxB,EAAsCE,KAAtC,EAA4C,CAA5C,CAAP;AACD;AAED;;;;;;;AAKQ,SAASG,IAAT,CAAcC,IAAd,EAAoB;AAC1B,SAAOH,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,aAAxB,EAAsCM,IAAtC,EAA2C,CAA3C,CAAP;AACD;AAED;;;;;;;AAKQ,SAASC,GAAT,CAAaC,EAAb,EAAiB;AACvB,SAAOL,iBAAIM,UAAJ,CAAeT,OAAO,GAAC,gBAAR,GAAyBQ,EAAxC,EAA2C,IAA3C,EAAgD,CAAhD,CAAP;AACD;AAED;;;;;;;AAKO,SAASE,MAAT,CAAgBF,EAAhB,EAAoB;AACzB,SAAOL,iBAAIM,UAAJ,CAAeT,OAAO,GAAC,gBAAR,GAAyBQ,EAAxC,EAA2C,IAA3C,EAAgD,CAAhD,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\nconst baseUrl = \"/activiti-api\";\n/**\n * 分页查询\n * @param query 请求参数\n * @returns {Promise<any>}\n */\nexport function list(query) {\n  return api.requestPost(baseUrl+\"/leave/list\",query,3)\n}\n\n/**\n * 保存\n * @param data\n * @returns {Promise | Promise<any>}\n */\nexport  function save(data) {\n  return api.requestPost(baseUrl+\"/leave/save\",data,3)\n}\n\n/**\n * 删除操作\n * @param id\n * @returns {Promise | Promise<any>}\n */\nexport  function del(id) {\n  return api.requestGet(baseUrl+\"/leave/delete/\"+id,null,3)\n}\n\n/**\n * 提交申请\n * @param id\n * @returns {Promise | Promise<any>}\n */\nexport function commit(id) {\n  return api.requestGet(baseUrl+\"/leave/commit/\"+id,null,3)\n}\n"]}]}