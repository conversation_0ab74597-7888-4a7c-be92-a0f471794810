{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\equipmentComponents.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\equipmentComponents.vue", "mtime": 1706897322893}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["equipmentComponents.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AA8CA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAMA;AACA,EAAA,KAAA,EAAA;AACA,IAAA,cAAA,EAAA;AACA,MAAA,IAAA,EAAA;AADA;AADA,GADA;AAMA,EAAA,IAAA,EAAA,qBANA;AAOA,EAAA,UAAA,EAAA;AAAA,IAAA,UAAA,EAAA;AAAA,GAPA;AAQA,EAAA,IARA,kBAQA;AACA,WAAA;AACA,MAAA,QAAA,EAAA,IADA;AAEA,MAAA,GAAA,EAAA,CAFA;AAGA,MAAA,SAAA,EAAA,EAHA;AAIA,MAAA,OAAA,EAAA,KAJA;AAKA,MAAA,WAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA,CAHA;AAIA,QAAA,MAAA,EAAA;AAJA,OALA;AAWA,MAAA,QAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,OAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,OAAA,EAAA,IALA;AAMA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AANA,OADA,EASA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,OAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,OAAA,EAAA,IALA;AAMA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AANA,OATA,EAiBA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,OAHA;AAIA,QAAA,IAAA,EAAA,IAJA;AAKA,QAAA,OAAA,EAAA,IALA;AAMA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AANA,OAjBA,EAyBA;AACA,QAAA,KAAA,EAAA,SADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,UAHA;AAIA,QAAA,IAAA,EAAA,QAJA;AAKA,QAAA,OAAA,EAAA,IALA;AAMA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AANA,OAzBA,EAiCA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,OAHA;AAIA,QAAA,IAAA,EAAA,IAJA;AAKA,QAAA,OAAA,EAAA,IALA;AAMA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AANA,OAjCA,EAyCA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,UAHA;AAIA,QAAA,IAAA,EAAA,IAJA;AAKA,QAAA,OAAA,EAAA,IALA;AAMA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AANA,OAzCA,EAiDA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,UAHA;AAIA,QAAA,IAAA,EAAA,IAJA;AAKA,QAAA,OAAA,EAAA,IALA;AAMA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AANA,OAjDA,EA0DA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,OAHA;AAIA,QAAA,IAAA,EAAA,IAJA;AAKA,QAAA,OAAA,EAAA,IALA;AAMA,QAAA,MAAA,EAAA,KANA;AAOA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AAPA,OA1DA,EAmEA;AACA,QAAA,KAAA,EAAA,SADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,OAHA;AAIA,QAAA,IAAA,EAAA,QAJA;AAKA,QAAA,OAAA,EAAA,IALA;AAMA,QAAA,MAAA,EAAA,KANA;AAOA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA;AAPA,OAnEA,CAXA;AAyFA;AACA,MAAA,eAAA,EAAA;AA1FA,KAAA;AA4FA,GArGA;AAsGA,EAAA,OAtGA,qBAsGA;AACA,SAAA,OAAA;AACA,GAxGA;AAyGA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,OAFA,qBAEA;AAAA;;AACA,WAAA,OAAA,GAAA,KAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,6BAAA,KAAA,cAAA;AACA,WAAA,WAAA,CAAA,MAAA,GAAA,KAAA,cAAA,CAAA,MAAA;AACA,wCAAA,KAAA,WAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,KAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,KAAA,CAAA,WAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,OAHA;AAIA,WAAA,OAAA,GAAA,KAAA;AACA,KAXA;AAYA;AACA,IAAA,sBAbA,oCAaA;AAAA;;AACA,WAAA,QAAA,GAAA,IAAA;AACA,WAAA,QAAA,GAAA,KAAA,QAAA,CAAA,IAAA,GAAA,QAAA;AACA,UAAA,OAAA,GAAA,KAAA,QAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,KAAA,QAAA,EAAA;AACA,UAAA,IAAA,CAAA,KAAA,GAAA,MAAA,CAAA,cAAA,CAAA,MAAA;AACA;;AACA,YAAA,IAAA,CAAA,IAAA,KAAA,QAAA,EAAA;AACA,UAAA,IAAA,CAAA,KAAA,GAAA,MAAA,CAAA,cAAA,CAAA,IAAA;AACA;;AACA,eAAA,IAAA;AACA,OARA,CAAA;AASA,WAAA,KAAA,CAAA,UAAA,CAAA,OAAA,CAAA,OAAA;AACA,KA1BA;AA2BA;AACA,IAAA,yBA5BA,qCA4BA,GA5BA,EA4BA;AACA,UAAA,UAAA,GAAA,KAAA,QAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,eAAA,IAAA;AACA,OAHA,CAAA;AAIA,WAAA,QAAA,GAAA,IAAA;AACA,WAAA,KAAA,CAAA,UAAA,CAAA,OAAA,CAAA,UAAA;AACA,KAnCA;AAoCA;AACA,IAAA,UArCA,sBAqCA,GArCA,EAqCA;AACA,UAAA,QAAA,GAAA,KAAA,QAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA;AACA,eAAA,IAAA;AACA,OAHA,CAAA;AAIA,WAAA,QAAA,GAAA,IAAA;AACA,WAAA,KAAA,CAAA,UAAA,CAAA,MAAA,CAAA,QAAA;AACA,KA5CA;AA6CA;AACA,IAAA,yBA9CA,uCA8CA;AAAA;;AACA,WAAA,QAAA,CAAA,iBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AAEA,YAAA,GAAA,GAAA,EAAA;;AACA,QAAA,MAAA,CAAA,eAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA,SAFA;;AAIA,6CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,YAAA,MAAA,CAAA,OAAA;AACA,WAHA,MAGA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,SAPA;AAQA,OAnBA,EAmBA,KAnBA,CAmBA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OAxBA;AAyBA,KAxEA;AAyEA;AACA,IAAA,uBA1EA,mCA0EA,QA1EA,EA0EA;AAAA;;AACA,UAAA,OAAA,GAAA,EAAA;;AACA,UAAA,QAAA,CAAA,EAAA,KAAA,EAAA,IAAA,CAAA,QAAA,CAAA,EAAA,EAAA;AACA,QAAA,OAAA,GAAA,MAAA;AACA,OAFA,MAEA;AACA,QAAA,OAAA,GAAA,MAAA;AACA;;AACA,yCAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,OAAA;;AACA,UAAA,MAAA,CAAA,OAAA;AACA,SAHA,MAGA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,OAPA;AAQA,KAzFA;AA0FA;AACA,IAAA,qBA3FA,iCA2FA,GA3FA,EA2FA;AACA,WAAA,eAAA,GAAA,GAAA;AACA;AA7FA;AAzGA,C", "sourcesContent": ["<template>\n  <div>\n    <el-white>\n      <el-white class=\"button-group\">\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"addEquipmentComponents\">新增</el-button>\n        <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"deleteEquipmentComponents\">删除</el-button>\n      </el-white>\n      <el-table\n        stripe\n        border\n        v-loading=\"loading\"\n        :data=\"tableData\"\n        @selection-change=\"handleSelectionChange\">\n        <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n        <el-table-column label=\"部件编码\" align=\"center\" prop=\"bjbm\"/>\n        <el-table-column label=\"部件名称\" align=\"center\" prop=\"bjmc\"/>\n        <el-table-column label=\"描述\" align=\"center\" prop=\"ms\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"设备类型名称\" align=\"center\" prop=\"sblxmc\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"操作\" fixed=\"right\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"180\">\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" @click=\"updateEquipmentComponents(scope.row)\">修改</el-button>\n            <el-button type=\"text\" @click=\"showDetail(scope.row)\">详情</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      <pagination\n        v-show=\"queryParams.total>0\"\n        :total=\"queryParams.total\"\n        :page.sync=\"queryParams.pageNum\"\n        :limit.sync=\"queryParams.pageSize\"\n        @pagination=\"getList\"/>\n    </el-white>\n\n    <dialog-form\n      ref=\"dialogForm\"\n      :append-to-body=\"true\"\n      label-width=\"150px\"\n      out-width=\"50%\"\n      :reminder=\"reminder\"\n      :rows=\"row\"\n      @save=\"saveEquipmentComponents\"\n    />\n  </div>\n</template>\n\n<script>\nimport DialogForm from 'com/dialogFrom/dialogForm'\nimport {\n  deleteEquipmentComponents,\n  getEquipmentComponents,\n  saveEquipmentComponents\n} from '@/api/dagangOilfield/bzgl/sblxwh/sbbj'\n\nexport default {\n  props: {\n    deviceTypeData: {\n      type: Object\n    }\n  },\n  name: 'equipmentComponents',\n  components: { DialogForm },\n  data() {\n    return {\n      reminder: '新增',\n      row: 2,\n      tableData: [],\n      loading: false,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        total: 0,\n        sblxbm: ''\n      },\n      formList: [\n        {\n          label: '部件编码：',\n          value: '',\n          type: 'input',\n          name: 'bjbm',\n          default: true,\n          rules: { required: true, message: '请输入部件编码' }\n        },\n        {\n          label: '部件名称：',\n          value: '',\n          type: 'input',\n          name: 'bjmc',\n          default: true,\n          rules: { required: true, message: '请输入部件名称' }\n        },\n        {\n          label: '单位：',\n          value: '',\n          type: 'input',\n          name: 'dw',\n          default: true,\n          rules: { required: false, message: '请输入单位' }\n        },\n        {\n          label: '设备类型名称：',\n          value: '',\n          type: 'disabled',\n          name: 'sblxmc',\n          default: true,\n          rules: { required: true, message: '请输入属性名称' }\n        },\n        {\n          label: '排序：',\n          value: '',\n          type: 'input',\n          name: 'px',\n          default: true,\n          rules: { required: false, message: '请输入排序' }\n        },\n        {\n          label: '描述：',\n          value: '',\n          type: 'textarea',\n          name: 'ms',\n          default: true,\n          rules: { required: false, message: '请输入描述' }\n        },\n        {\n          label: '备注：',\n          value: '',\n          type: 'textarea',\n          name: 'bz',\n          default: true,\n          rules: { required: false, message: '请输入备注' }\n        },\n\n        {\n          label: 'id：',\n          value: '',\n          type: 'input',\n          name: 'id',\n          default: true,\n          hidden: false,\n          rules: { required: false, message: '请输入id' }\n        },\n        {\n          label: '设备类型编码：',\n          value: '',\n          type: 'input',\n          name: 'sblxbm',\n          default: true,\n          hidden: false,\n          rules: { required: false, message: '请输入设备类型编码' }\n        }\n\n      ],\n      //选中行数据\n      selectedRowData: []\n    }\n  },\n  mounted() {\n    this.getList()\n  },\n  methods: {\n    //获取设备部件数据\n    getList() {\n      this.loading = false\n      console.log(\"---this.deviceTypeData--\"+this.deviceTypeData);\n      this.queryParams.sblxbm = this.deviceTypeData.sblxbm\n      getEquipmentComponents(this.queryParams).then(res => {\n        this.tableData = res.data.records\n        this.queryParams.total = res.data.total\n      })\n      this.loading = false\n    },\n    //新增设备部件\n    addEquipmentComponents() {\n      this.reminder = '新增'\n      this.formList = this.$options.data().formList\n      const addForm = this.formList.map(item => {\n        if (item.name === 'sblxbm') {\n          item.value = this.deviceTypeData.sblxbm\n        }\n        if (item.name === 'sblxmc') {\n          item.value = this.deviceTypeData.sblx\n        }\n        return item\n      })\n      this.$refs.dialogForm.showzzc(addForm)\n    },\n    //修改设备部件\n    updateEquipmentComponents(row) {\n      const updateList = this.formList.map(item => {\n        item.value = row[item.name]\n        return item\n      })\n      this.reminder = '修改'\n      this.$refs.dialogForm.showzzc(updateList)\n    },\n    //查看详情\n    showDetail(row) {\n      const infoList = this.formList.map(item => {\n        item.value = row[item.name]\n        return item\n      })\n      this.reminder = '详情'\n      this.$refs.dialogForm.showxq(infoList)\n    },\n    //批量删除设备部件\n    deleteEquipmentComponents() {\n      this.$confirm('确认删除选中数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n\n        let ids = []\n        this.selectedRowData.forEach(item => {\n          ids.push(item.id)\n        })\n\n        deleteEquipmentComponents(ids).then(res => {\n          if (res.code === '0000') {\n            this.$message.success('删除成功')\n            this.getList()\n          } else {\n            this.$message.error('操作失败')\n          }\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        })\n      })\n    },\n    //保存设备部件数据\n    saveEquipmentComponents(formData) {\n      let message = ''\n      if (formData.id === '' || !formData.id) {\n        message = '新增成功'\n      } else {\n        message = '修改成功'\n      }\n      saveEquipmentComponents(formData).then(res => {\n        if (res.code === '0000') {\n          this.$message.success(message)\n          this.getList()\n        } else {\n          this.$message.error('操作失败')\n        }\n      })\n    },\n    //行选中事件\n    handleSelectionChange(row) {\n      this.selectedRowData = row\n    }\n\n  }\n}\n</script>\n\n<style scoped>\n.pagination-container {\n  position: relative;\n  height: 25px;\n  margin-bottom: 20px !important;\n  margin-top: 15px;\n  padding: 10px 20px !important;\n}\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbbzk"}]}