{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\hskwh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\hskwh.vue", "mtime": 1706897323218}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwIik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKCnJlcXVpcmUoInJlZ2VuZXJhdG9yLXJ1bnRpbWUvcnVudGltZSIpOwoKdmFyIF9hc3luY1RvR2VuZXJhdG9yMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovU2hhbW1wb29sL3dvcmsvY29kZS9kZ3l0LzAxXHU0RUUzXHU3ODAxL3F6LXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2FzeW5jVG9HZW5lcmF0b3IiKSk7Cgp2YXIgX29iamVjdFNwcmVhZDIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkQ6L1NoYW1tcG9vbC93b3JrL2NvZGUvZGd5dC8wMVx1NEVFM1x1NzgwMS9xei11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9vYmplY3RTcHJlYWQyIikpOwoKdmFyIF9oc2t3aCA9IHJlcXVpcmUoIkAvYXBpL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvaHNrd2giKTsKCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IHsKICBuYW1lOiAiaHNrd2giLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvL2Zvcm3ooajljZUKICAgICAgZm9ybTogewogICAgICAgIGhzZmxpZDogdW5kZWZpbmVkLAogICAgICAgIGhzZmxtYzogdW5kZWZpbmVkLAogICAgICAgIGhzbWM6IHVuZGVmaW5lZCwKICAgICAgICBpc3N5aHM6IHVuZGVmaW5lZAogICAgICB9LAogICAgICAvL+aYr+WQpuaYvuekuuW8ueahhgogICAgICBpc1Nob3dEZXRhaWxzOiBmYWxzZSwKICAgICAgLy/mmK/lkKbnpoHnlKgKICAgICAgaXNEaXNhYmxlZDogZmFsc2UsCiAgICAgIC8v6KGo5Y2V5pWw5o2uCiAgICAgIGRhdGFUYWJsZTogW10sCiAgICAgIC8v5Yig6Zmk6YCJ5oup5YiXCiAgICAgIHNlbGVjdFJvd3M6IFtdLAogICAgICAvL+agh+mimAogICAgICB0aXRsZTogIiIsCiAgICAgIC8v5Ye95pWw5YiG57G75ZCN56ewCiAgICAgIC8vIGhzZmxtY2xpc3Q6IFtdLAogICAgICAvL+aQnOe0ogogICAgICBmaWx0ZXJJbmZvOiB7CiAgICAgICAgZGF0YTogewogICAgICAgICAgaHNmbG1jOiAiIiwKICAgICAgICAgIGhzbWM6ICIiCiAgICAgICAgfSwKICAgICAgICBmaWVsZExpc3Q6IFt7CiAgICAgICAgICBsYWJlbDogIuWHveaVsOWIhuexu+WQjeensCIsCiAgICAgICAgICB2YWx1ZTogImhzZmxtYyIsCiAgICAgICAgICB0eXBlOiAiaW5wdXQiLAogICAgICAgICAgY2xlYXJhYmxlOiB0cnVlCiAgICAgICAgfSwgewogICAgICAgICAgbGFiZWw6ICLlh73mlbDlkI3np7AiLAogICAgICAgICAgdmFsdWU6ICJoc21jIiwKICAgICAgICAgIHR5cGU6ICJpbnB1dCIsCiAgICAgICAgICBjbGVhcmFibGU6IHRydWUKICAgICAgICB9XQogICAgICB9LAogICAgICB0YWJsZUFuZFBhZ2VJbmZvOiB7CiAgICAgICAgcGFnZXI6IHsKICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgIHNpemVzOiBbMTAsIDIwLCA1MCwgMTAwXQogICAgICAgIH0sCiAgICAgICAgdGFibGVEYXRhOiBbXSwKICAgICAgICB0YWJsZUhlYWRlcjogW3sKICAgICAgICAgIGxhYmVsOiAi5Ye95pWw5YiG57G75ZCN56ewIiwKICAgICAgICAgIHByb3A6ICJoc2ZsbWMiCiAgICAgICAgfSwgewogICAgICAgICAgbGFiZWw6ICLlh73mlbDlkI3np7AiLAogICAgICAgICAgcHJvcDogImhzbWMiCiAgICAgICAgfSwgewogICAgICAgICAgbGFiZWw6ICLlh73mlbDmj4/ov7AiLAogICAgICAgICAgcHJvcDogImhzbXMiCiAgICAgICAgfSwgewogICAgICAgICAgbGFiZWw6ICLlj4LmlbDkuKrmlbAiLAogICAgICAgICAgcHJvcDogImNzZ3MiCiAgICAgICAgfSwgewogICAgICAgICAgbGFiZWw6ICLor5Xpqozlh73mlbAiLAogICAgICAgICAgcHJvcDogImlzc3loc3MiCiAgICAgICAgfSwgewogICAgICAgICAgbGFiZWw6ICLliIbmnpDlh73mlbAiLAogICAgICAgICAgcHJvcDogImlzZnhoc3MiCiAgICAgICAgfSwgewogICAgICAgICAgbGFiZWw6ICJTUUzlh73mlbAiLAogICAgICAgICAgcHJvcDogImlzc3FsaHNzIgogICAgICAgIH0sIHsKICAgICAgICAgIGxhYmVsOiAi6Ieq5a6a5LmJ6KGo6L6+5byPIiwKICAgICAgICAgIHByb3A6ICJ6ZHliZHMiCiAgICAgICAgfSwgewogICAgICAgICAgcHJvcDogIm9wZXJhdGlvbiIsCiAgICAgICAgICBsYWJlbDogIuaTjeS9nCIsCiAgICAgICAgICBtaW5XaWR0aDogIjEwMHB4IiwKICAgICAgICAgIHN0eWxlOiB7CiAgICAgICAgICAgIGRpc3BsYXk6ICJibG9jayIKICAgICAgICAgIH0sCiAgICAgICAgICAvL+aTjeS9nOWIl+WbuuWumuWGjeWPs+S+pwogICAgICAgICAgZml4ZWQ6ICJyaWdodCIsCiAgICAgICAgICBvcGVyYXRpb246IFt7CiAgICAgICAgICAgIG5hbWU6ICLkv67mlLkiLAogICAgICAgICAgICBjbGlja0Z1bjogdGhpcy51bmRhdGVEZXRhaWxzCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIG5hbWU6ICLor6bmg4UiLAogICAgICAgICAgICBjbGlja0Z1bjogdGhpcy5kZXRhaWxzSW5mbwogICAgICAgICAgfV0KICAgICAgICB9XSwKICAgICAgICBvcHRpb246IHsKICAgICAgICAgIGNoZWNrQm94OiB0cnVlLAogICAgICAgICAgc2VyaWFsTnVtYmVyOiB0cnVlCiAgICAgICAgfQogICAgICB9LAogICAgICBpc3N5aHNMaXN0OiBbewogICAgICAgIGxhYmVsOiAi5pivIiwKICAgICAgICB2YWx1ZTogMQogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICLlkKYiLAogICAgICAgIHZhbHVlOiAyCiAgICAgIH1dLAogICAgICBpc3NxbGhzTGlzdDogW3sKICAgICAgICBsYWJlbDogIuaYryIsCiAgICAgICAgdmFsdWU6IDEKICAgICAgfSwgewogICAgICAgIGxhYmVsOiAi5ZCmIiwKICAgICAgICB2YWx1ZTogMgogICAgICB9XSwKICAgICAgaXNmeGhzTGlzcjogW3sKICAgICAgICBsYWJlbDogIuaYryIsCiAgICAgICAgdmFsdWU6IDEKICAgICAgfSwgewogICAgICAgIGxhYmVsOiAi5ZCmIiwKICAgICAgICB2YWx1ZTogMgogICAgICB9XSwKICAgICAgcXVlcnlIc2t3aFBhcmFtOiB7CiAgICAgICAgaHNmbG1jOiAiIiwKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMAogICAgICB9LAogICAgICAvL+e7hOe7h+agkQogICAgICB0cmVlT3B0aW9uczogW10sCiAgICAgIC8v5Yig6Zmk5piv5ZCm5Y+v55SoCiAgICAgIG11bHRpcGxlU2Vuc29yOiB0cnVlLAogICAgICAvL+afpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIGhzZmxtYzogIiIsCiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAKICAgICAgfSwKICAgICAgLy/loavlhaXmlbDmja7moKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICBoc21jOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5Ye95pWw5ZCN56ew5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGlzc3loczogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuivlemqjOWHveaVsOS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBpc2Z4aHM6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLliIbmnpDlh73mlbDkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgaXNzcWxoczogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIlNRTOWHveaVsOS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XQogICAgICB9LAogICAgICAvL+ihqOWNleW8gOWFswogICAgICBpc1NlYXJjaFNob3c6IGZhbHNlCiAgICB9OwogIH0sCiAgd2F0Y2g6IHt9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICAvL+afpeivonRhYmxl5YiX6KGoCiAgICB0aGlzLmdldERhdGEoKTsKICAgIHRoaXMuZ2V0VHJlZU9wdGlvbigpOwogIH0sCiAgbWV0aG9kczogewogICAgLy/mn6Xor6LmoJHmlrnms5UKICAgIGdldFRyZWVPcHRpb246IGZ1bmN0aW9uIGdldFRyZWVPcHRpb24oKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CgogICAgICAoMCwgX2hza3doLmdldFRyZWVEYXRhKSgpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzLnRyZWVPcHRpb25zID0gcmVzLmRhdGE7CiAgICAgIH0pOyAvLyBnZXRIc2Zsa0xpc3QoKS50aGVuKChyZXMpID0+IHsKICAgICAgLy8gICB0aGlzLmhzZmxtY2xpc3QgPSByZXMuZGF0YTsKICAgICAgLy8gfSk7CiAgICB9LAogICAgLy/moJHoioLngrnngrnlh7vkuovku7YKICAgIGhhbmRsZU5vZGVDbGljazogZnVuY3Rpb24gaGFuZGxlTm9kZUNsaWNrKGRhdGEpIHsKICAgICAgaWYgKGRhdGEuaWQgIT0gdW5kZWZpbmVkICYmIGRhdGEuaWQgIT0gJzAwMDAnKSB7CiAgICAgICAgdGhpcy5mb3JtLmhzZmxpZCA9IGRhdGEuaWQ7CiAgICAgICAgdGhpcy5mb3JtLmhzZmxtYyA9IGRhdGEubGFiZWw7CiAgICAgICAgdGhpcy5xdWVyeUhza3doUGFyYW0uaHNmbG1jID0gZGF0YS5sYWJlbDsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmhzZmxtYyA9IGRhdGEubGFiZWw7CiAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5xdWVyeUhza3doUGFyYW0uaHNmbG1jID0gJyc7CiAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgIH0KICAgIH0sCiAgICAvL+a3u+WKoOaMiemSrgogICAgYWRkU2Vuc29yQnV0dG9uOiBmdW5jdGlvbiBhZGRTZW5zb3JCdXR0b24oKSB7CiAgICAgIGlmICh0aGlzLmZvcm0uaHNmbGlkID09PSB1bmRlZmluZWQpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+WcqOmAieaLqeWHveaVsOWIhuexu+W6k+WQjuaWsOWinuiusOW9lSIpOwogICAgICAgIHJldHVybjsKICAgICAgfSAvL+aJk+W8gOW8ueeqlwoKCiAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IHRydWU7IC8v6KGo5Y2V5Y+v57yW6L6RCgogICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZTsgLy/orr7nva7lvLnlh7rmoYbmoIfpopgKCiAgICAgIHRoaXMudGl0bGUgPSAi5paw5aKeIjsKICAgIH0sCiAgICAvL+S/ruaUueaMiemSrgogICAgdW5kYXRlRGV0YWlsczogZnVuY3Rpb24gdW5kYXRlRGV0YWlscyhyb3cpIHsKICAgICAgLy/miZPlvIDlvLnnqpcKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gdHJ1ZTsgLy/nu5nooajljZXotYvlgLwKCiAgICAgIHRoaXMuZm9ybSA9IHJvdzsgLy/ooajljZXlj6/nvJbovpEKCiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOyAvL+iuvue9ruW8ueWHuuahhuagh+mimAoKICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLkiOwogICAgfSwKICAgIC8v56Gu6K6k5o+Q5LqkCiAgICBjb21taXRBZGQ6IGZ1bmN0aW9uIGNvbW1pdEFkZCgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CgogICAgICBjb25zb2xlLmxvZygi5byA5aeLIiArIHRoaXMuZm9ybS5oc2ZsaWQpOwogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICAoMCwgX2hza3doLnNhdmVPclVwZGF0ZSkoX3RoaXMyLmZvcm0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgIF90aGlzMi4kbWVzc2FnZS5zdWNjZXNzKHJlcy5tc2cpOwoKICAgICAgICAgICAgICBfdGhpczIudGFibGVBbmRQYWdlSW5mby5wYWdlci5wYWdlUmVzaXplID0gIlkiOwoKICAgICAgICAgICAgICBfdGhpczIuZ2V0RGF0YSgpOwoKICAgICAgICAgICAgICBfdGhpczIuaXNTaG93RGV0YWlscyA9IGZhbHNlOwogICAgICAgICAgICAgIF90aGlzMi5mb3JtID0ge307CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgX3RoaXMyLiRtZXNzYWdlLmVycm9yKHJlcy5tc2cpOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIGNsb3NlOiBmdW5jdGlvbiBjbG9zZSgpIHsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gZmFsc2U7CiAgICB9LAogICAgLy/lrprkuYnph43nva7mlrnms5UKICAgIGdldFJlc2V0OiBmdW5jdGlvbiBnZXRSZXNldCgpIHt9LAogICAgLy/nvJbovpHmjInpkq4KICAgIHVwZGF0ZVNlbnNvckJ1dHRvbjogZnVuY3Rpb24gdXBkYXRlU2Vuc29yQnV0dG9uKCkgewogICAgICAvL+aJk+W8gOW8ueeqlwogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlOyAvL+e7meihqOWNlei1i+WAvAoKICAgICAgdGhpcy5mb3JtID0gcm93OyAvL+ihqOWNleWPr+e8lui+kQoKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gZmFsc2U7IC8v6K6+572u5by55Ye65qGG5qCH6aKYCgogICAgICB0aGlzLnRpdGxlID0gIuS/ruaUuSI7CiAgICB9LAogICAgLy/or6bmg4XmjInpkq4KICAgIGRldGFpbHNJbmZvOiBmdW5jdGlvbiBkZXRhaWxzSW5mbyhyb3cpIHsKICAgICAgdGhpcy50aXRsZSA9ICLor6bmg4UiOyAvL+aJk+W8gOW8ueeqlwoKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gdHJ1ZTsgLy/miorooYzmlbDmja7nu5nlvLnlh7rmoYbooajljZUKCiAgICAgIHRoaXMuZm9ybSA9ICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7fSwgcm93KTsgLy/lsIbooajljZXkuI3lj6/nvJbovpEKCiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IHRydWU7CiAgICB9LAogICAgLy/liKDpmaTmjInpkq4KICAgIGdldERlbGV0ZTogZnVuY3Rpb24gZ2V0RGVsZXRlKCkgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKCiAgICAgIGlmICh0aGlzLnNlbGVjdFJvd3MubGVuZ3RoIDwgMSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+36YCJ5oup5q2j56Gu55qE5pWw5o2u77yB77yB77yBIik7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICB2YXIgaWRzID0gdGhpcy5zZWxlY3RSb3dzLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLm9iaklkOwogICAgICB9KTsKICAgICAgdGhpcy4kY29uZmlybSgi5q2k5pON5L2c5bCG5rC45LmF5Yig6Zmk6K+l5pWw5o2uLCDmmK/lkKbnu6fnu60/IiwgIuaPkOekuiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgICgwLCBfaHNrd2gucmVtb3ZlKShpZHMpLnRoZW4oZnVuY3Rpb24gKF9yZWYpIHsKICAgICAgICAgIHZhciBjb2RlID0gX3JlZi5jb2RlOwoKICAgICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgX3RoaXMzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsCiAgICAgICAgICAgICAgbWVzc2FnZTogIuWIoOmZpOaIkOWKnyEiCiAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgX3RoaXMzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIucGFnZVJlc2l6ZSA9ICJZIjsKCiAgICAgICAgICAgIF90aGlzMy5nZXREYXRhKCk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBfdGhpczMuJG1lc3NhZ2UoewogICAgICAgICAgICAgIHR5cGU6ICJlcnJvciIsCiAgICAgICAgICAgICAgbWVzc2FnZTogIuWIoOmZpOWksei0pSEiCiAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgX3RoaXMzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIucGFnZVJlc2l6ZSA9ICJZIjsKCiAgICAgICAgICAgIF90aGlzMy5nZXREYXRhKCk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogImluZm8iLAogICAgICAgICAgbWVzc2FnZTogIuW3suWPlua2iOWIoOmZpCIKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgLy/lr7zlh7rmjInpkq4KICAgIGhhbmRsZUV4cG9ydDogZnVuY3Rpb24gaGFuZGxlRXhwb3J0KCkge30sCiAgICAvL+ihjOmAieS4rQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uocm93cykgewogICAgICB0aGlzLnNlbGVjdFJvd3MgPSByb3dzOwogICAgICB0aGlzLndobWp6QnV0dG9uRGlzYWJsZWQgPSByb3dzLmxlbmd0aCAhPSAxOyAvL+iOt+WPluWIsOW9k+WJjeihjOWvueixoQoKICAgICAgdGhpcy5tanpSb3dGb3JtID0gcm93c1swXTsKICAgIH0sCiAgICAvL+afpeivouWIl+ihqAogICAgZ2V0RGF0YTogZnVuY3Rpb24gZ2V0RGF0YShwYXJhbXMpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CgogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSggLyojX19QVVJFX18qL3JlZ2VuZXJhdG9yUnVudGltZS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUoKSB7CiAgICAgICAgdmFyIHBhcmFtLCBfeWllbGQkZ2V0UGFnZURhdGFMaXMsIGRhdGEsIGNvZGU7CgogICAgICAgIHJldHVybiByZWdlbmVyYXRvclJ1bnRpbWUud3JhcChmdW5jdGlvbiBfY2FsbGVlJChfY29udGV4dCkgewogICAgICAgICAgd2hpbGUgKDEpIHsKICAgICAgICAgICAgc3dpdGNoIChfY29udGV4dC5wcmV2ID0gX2NvbnRleHQubmV4dCkgewogICAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICAgIF9jb250ZXh0LnByZXYgPSAwOwogICAgICAgICAgICAgICAgcGFyYW0gPSAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCBfdGhpczQucXVlcnlIc2t3aFBhcmFtKSwgcGFyYW1zKTsKICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSA0OwogICAgICAgICAgICAgICAgcmV0dXJuICgwLCBfaHNrd2guZ2V0UGFnZURhdGFMaXN0KShwYXJhbSk7CgogICAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgICAgIF95aWVsZCRnZXRQYWdlRGF0YUxpcyA9IF9jb250ZXh0LnNlbnQ7CiAgICAgICAgICAgICAgICBkYXRhID0gX3lpZWxkJGdldFBhZ2VEYXRhTGlzLmRhdGE7CiAgICAgICAgICAgICAgICBjb2RlID0gX3lpZWxkJGdldFBhZ2VEYXRhTGlzLmNvZGU7CgogICAgICAgICAgICAgICAgaWYgKGNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgICAgICBfdGhpczQudGFibGVBbmRQYWdlSW5mby50YWJsZURhdGEgPSBkYXRhLnJlY29yZHM7CiAgICAgICAgICAgICAgICAgIF90aGlzNC50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnRvdGFsID0gZGF0YS50b3RhbDsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gMTM7CiAgICAgICAgICAgICAgICBicmVhazsKCiAgICAgICAgICAgICAgY2FzZSAxMDoKICAgICAgICAgICAgICAgIF9jb250ZXh0LnByZXYgPSAxMDsKICAgICAgICAgICAgICAgIF9jb250ZXh0LnQwID0gX2NvbnRleHRbImNhdGNoIl0oMCk7CiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhfY29udGV4dC50MCk7CgogICAgICAgICAgICAgIGNhc2UgMTM6CiAgICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5zdG9wKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlLCBudWxsLCBbWzAsIDEwXV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvL+aQnOe0ogogICAgaGFuZGxlUXVlcnk6IGZ1bmN0aW9uIGhhbmRsZVF1ZXJ5KCkge30sCiAgICAvL+mHjee9rgogICAgcmVzZXRRdWVyeTogZnVuY3Rpb24gcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgfQogIH0KfTsKZXhwb3J0cy5kZWZhdWx0ID0gX2RlZmF1bHQ7"}, {"version": 3, "sources": ["hskwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAkKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eASA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA;AACA,MAAA,IAAA,EAAA;AACA,QAAA,MAAA,EAAA,SADA;AAEA,QAAA,MAAA,EAAA,SAFA;AAGA,QAAA,IAAA,EAAA,SAHA;AAIA,QAAA,MAAA,EAAA;AAJA,OAFA;AAQA;AACA,MAAA,aAAA,EAAA,KATA;AAUA;AACA,MAAA,UAAA,EAAA,KAXA;AAYA;AACA,MAAA,SAAA,EAAA,EAbA;AAcA;AACA,MAAA,UAAA,EAAA,EAfA;AAgBA;AACA,MAAA,KAAA,EAAA,EAjBA;AAkBA;AACA;AACA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,MAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA;AAFA,SADA;AAKA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,QADA;AAEA,UAAA,KAAA,EAAA,QAFA;AAGA,UAAA,IAAA,EAAA,OAHA;AAIA,UAAA,SAAA,EAAA;AAJA,SADA,EAOA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,MAFA;AAGA,UAAA,IAAA,EAAA,OAHA;AAIA,UAAA,SAAA,EAAA;AAJA,SAPA;AALA,OArBA;AAyCA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SARA,EASA;AACA,UAAA,IAAA,EAAA,WADA;AAEA,UAAA,KAAA,EAAA,IAFA;AAGA,UAAA,QAAA,EAAA,OAHA;AAIA,UAAA,KAAA,EAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WAJA;AAKA;AACA,UAAA,KAAA,EAAA,OANA;AAOA,UAAA,SAAA,EAAA,CACA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WADA,EAEA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WAFA;AAPA,SATA,CARA;AA8BA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AA9BA,OAzCA;AAyEA,MAAA,UAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,CAzEA;AA6EA,MAAA,WAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,CA7EA;AAiFA,MAAA,UAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,CAjFA;AAqFA,MAAA,eAAA,EAAA;AACA,QAAA,MAAA,EAAA,EADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,QAAA,EAAA;AAHA,OArFA;AA2FA;AACA,MAAA,WAAA,EAAA,EA5FA;AA6FA;AACA,MAAA,cAAA,EAAA,IA9FA;AA+FA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,MAAA,EAAA,EADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,QAAA,EAAA;AAHA,OAhGA;AAqGA;AACA,MAAA,KAAA,EAAA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,OAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,WAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAVA,OAtGA;AAoHA;AACA,MAAA,YAAA,EAAA;AArHA,KAAA;AAuHA,GA1HA;AA2HA,EAAA,KAAA,EAAA,EA3HA;AA4HA,EAAA,OA5HA,qBA4HA;AACA;AACA,SAAA,OAAA;AACA,SAAA,aAAA;AACA,GAhIA;AAiIA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,aAFA,2BAEA;AAAA;;AACA,gCAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,KAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA,EADA,CAIA;AACA;AACA;AACA,KATA;AAUA;AACA,IAAA,eAXA,2BAWA,IAXA,EAWA;AACA,UAAA,IAAA,CAAA,EAAA,IAAA,SAAA,IAAA,IAAA,CAAA,EAAA,IAAA,MAAA,EAAA;AACA,aAAA,IAAA,CAAA,MAAA,GAAA,IAAA,CAAA,EAAA;AACA,aAAA,IAAA,CAAA,MAAA,GAAA,IAAA,CAAA,KAAA;AACA,aAAA,eAAA,CAAA,MAAA,GAAA,IAAA,CAAA,KAAA;AACA,aAAA,WAAA,CAAA,MAAA,GAAA,IAAA,CAAA,KAAA;AACA,aAAA,OAAA;AACA,OANA,MAMA;AACA,aAAA,eAAA,CAAA,MAAA,GAAA,EAAA;AACA,aAAA,OAAA;AACA;AACA,KAtBA;AAwBA;AACA,IAAA,eAzBA,6BAyBA;AACA,UAAA,KAAA,IAAA,CAAA,MAAA,KAAA,SAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,gBAAA;AACA;AACA,OAJA,CAKA;;;AACA,WAAA,aAAA,GAAA,IAAA,CANA,CAOA;;AACA,WAAA,UAAA,GAAA,KAAA,CARA,CASA;;AACA,WAAA,KAAA,GAAA,IAAA;AACA,KApCA;AAqCA;AACA,IAAA,aAtCA,yBAsCA,GAtCA,EAsCA;AACA;AACA,WAAA,aAAA,GAAA,IAAA,CAFA,CAGA;;AACA,WAAA,IAAA,GAAA,GAAA,CAJA,CAKA;;AACA,WAAA,UAAA,GAAA,KAAA,CANA,CAOA;;AACA,WAAA,KAAA,GAAA,IAAA;AACA,KA/CA;AAiDA;AACA,IAAA,SAlDA,uBAkDA;AAAA;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,OAAA,KAAA,IAAA,CAAA,MAAA;AACA,WAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,mCAAA,MAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,GAAA,CAAA,GAAA;;AACA,cAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,cAAA,MAAA,CAAA,OAAA;;AACA,cAAA,MAAA,CAAA,aAAA,GAAA,KAAA;AACA,cAAA,MAAA,CAAA,IAAA,GAAA,EAAA;AACA,aANA,MAMA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA;AACA;AACA,WAVA;AAWA;AACA,OAdA;AAeA,KAnEA;AAqEA,IAAA,KArEA,mBAqEA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,KAvEA;AAwEA;AACA,IAAA,QAzEA,sBAyEA,CAAA,CAzEA;AA0EA;AACA,IAAA,kBA3EA,gCA2EA;AACA;AACA,WAAA,aAAA,GAAA,IAAA,CAFA,CAGA;;AACA,WAAA,IAAA,GAAA,GAAA,CAJA,CAKA;;AACA,WAAA,UAAA,GAAA,KAAA,CANA,CAOA;;AACA,WAAA,KAAA,GAAA,IAAA;AACA,KApFA;AAqFA;AACA,IAAA,WAtFA,uBAsFA,GAtFA,EAsFA;AACA,WAAA,KAAA,GAAA,IAAA,CADA,CAEA;;AACA,WAAA,aAAA,GAAA,IAAA,CAHA,CAIA;;AACA,WAAA,IAAA,mCAAA,GAAA,EALA,CAMA;;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KA9FA;AAgGA;AACA,IAAA,SAjGA,uBAiGA;AAAA;;AACA,UAAA,KAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,aAAA;AACA;AACA;;AACA,UAAA,GAAA,GAAA,KAAA,UAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA;AACA,OAFA,CAAA;AAGA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAKA,IALA,CAKA,YAAA;AACA,2BAAA,GAAA,EAAA,IAAA,CAAA,gBAAA;AAAA,cAAA,IAAA,QAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,YAAA,MAAA,CAAA,OAAA;AACA,WAPA,MAOA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,YAAA,MAAA,CAAA,OAAA;AACA;AACA,SAhBA;AAiBA,OAvBA,EAwBA,KAxBA,CAwBA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OA7BA;AA8BA,KAvIA;AAyIA;AACA,IAAA,YA1IA,0BA0IA,CAAA,CA1IA;AA4IA;AACA,IAAA,qBA7IA,iCA6IA,IA7IA,EA6IA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,mBAAA,GAAA,IAAA,CAAA,MAAA,IAAA,CAAA,CAFA,CAGA;;AACA,WAAA,UAAA,GAAA,IAAA,CAAA,CAAA,CAAA;AACA,KAlJA;AAmJA;AACA,IAAA,OApJA,mBAoJA,MApJA,EAoJA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,KAFA,+DAEA,MAAA,CAAA,eAFA,GAEA,MAFA;AAAA;AAAA,uBAGA,4BAAA,KAAA,CAHA;;AAAA;AAAA;AAGA,gBAAA,IAHA,yBAGA,IAHA;AAGA,gBAAA,IAHA,yBAGA,IAHA;;AAIA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AAPA;AAAA;;AAAA;AAAA;AAAA;AASA,gBAAA,OAAA,CAAA,GAAA;;AATA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KA/JA;AAiKA;AACA,IAAA,WAlKA,yBAkKA,CAAA,CAlKA;AAmKA;AACA,IAAA,UApKA,wBAoKA;AACA,WAAA,SAAA,CAAA,WAAA;AACA;AAtKA;AAjIA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row>\n      <el-col :span=\"4\">\n        <el-card class=\"aside_height\" shadow=\"never\">\n          <div style=\"overflow: auto; height: 90vh\">\n            <el-col>\n              <el-tree\n                :expand-on-click-node=\"false\"\n                id=\"tree\"\n                :data=\"treeOptions\"\n                :default-expanded-keys=\"['1']\"\n                @node-click=\"handleNodeClick\"\n                node-key=\"nodeId\"\n              />\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"20\">\n        <el-white>\n          <el-white class=\"button-group\">\n            <el-filter\n              :data=\"filterInfo.data\"\n              :field-list=\"filterInfo.fieldList\"\n              :width=\"{ labelWidth: 180, itemWidth: 160 }\"\n              @handleReset=\"getReset\"\n            />\n            <el-button\n              type=\"primary\"\n              icon=\"el-icon-plus\"\n              @click=\"addSensorButton\"\n              >新增</el-button\n            >\n            <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"getDelete\"\n              >删除</el-button\n            >\n            <!-- <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleExport\"\n              >导出</el-button\n            > -->\n          </el-white>\n\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"69.6vh\"\n          />\n        </el-white>\n      </el-col>\n    </el-row>\n    <!-- 详情/新增/修改 -->\n    <el-dialog :title=\"title\" :visible.sync=\"isShowDetails\" width=\"60%\" v-dialogDrag>\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row :gutter=\"15\" class=\"pull-left\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"函数分类名称：\" prop=\"hsflmc\">\n              <el-input\n                placeholder=\"请输入函数分类名称：\"\n                v-model=\"form.hsflmc\"\n                :disabled=\"true\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"函数名称：\" prop=\"hsmc\">\n              <el-input\n                placeholder=\"请输入函数名称：\"\n                v-model=\"form.hsmc\"\n                :disabled=\"isDisabled\"\n              />\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"8\">\n            <el-form-item label=\"参数个数：\" prop=\"csgs\">\n              <el-input\n                placeholder=\"请输入参数个数\"\n                v-model=\"form.csgs\"\n                :disabled=\"isDisabled\"\n                type=\"number\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"15\" class=\"pull-left\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否试验函数：\" prop=\"issyhs\">\n              <el-select v-model=\"form.issyhs\" style=\"width: 100%\"  :disabled=\"isDisabled\">\n                <el-option\n                  v-for=\"item in issyhsList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否分析函数：\" prop=\"isfxhs\">\n              <el-select v-model=\"form.isfxhs\" style=\"width: 100%\" :disabled=\"isDisabled\">\n                <el-option\n                  v-for=\"item in isfxhsLisr\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"8\">\n            <el-form-item label=\"SQL函数：\" prop=\"issqlhs\">\n              <el-select v-model=\"form.issqlhs\" style=\"width: 100%\"  :disabled=\"isDisabled\">\n                <el-option\n                  v-for=\"item in issqlhsList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"15\" class=\"pull-left\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"自定义表达式：\" prop=\"zdybds\">\n              <el-input\n                placeholder=\"请输入自定义表达式\"\n                v-model=\"form.zdybds\"\n                :disabled=\"isDisabled\"\n              />\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"8\">\n            <el-form-item label=\"函数描述：\" prop=\"hsms\">\n              <el-input\n                placeholder=\"函数描述\"\n                v-model=\"form.hsms\"\n                :disabled=\"isDisabled\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"close\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"commitAdd\">保 存</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  getPageDataList,\n  remove,\n  saveOrUpdate,\n  ExportHskWH,\n  getTreeData,\n  getHsflkList,\n} from \"@/api/dagangOilfield/bzgl/hskwh\";\n\nexport default {\n  name: \"hskwh\",\n  data() {\n    return {\n      //form表单\n      form: {\n        hsflid: undefined,\n        hsflmc:undefined,\n        hsmc:undefined,\n        issyhs:undefined,\n      },\n      //是否显示弹框\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //表单数据\n      dataTable: [],\n      //删除选择列\n      selectRows: [],\n      //标题\n      title: \"\",\n      //函数分类名称\n      // hsflmclist: [],\n      //搜索\n      filterInfo: {\n        data: {\n          hsflmc: \"\",\n          hsmc: \"\",\n        },\n        fieldList: [\n          {\n            label: \"函数分类名称\",\n            value: \"hsflmc\",\n            type: \"input\",\n            clearable: true,\n          },\n          {\n            label: \"函数名称\",\n            value: \"hsmc\",\n            type: \"input\",\n            clearable: true,\n          },\n        ],\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"函数分类名称\", prop: \"hsflmc\" },\n          { label: \"函数名称\", prop: \"hsmc\" },\n          { label: \"函数描述\", prop: \"hsms\" },\n          { label: \"参数个数\", prop: \"csgs\" },\n          { label: \"试验函数\", prop: \"issyhss\" },\n          { label: \"分析函数\", prop: \"isfxhss\" },\n          { label: \"SQL函数\", prop: \"issqlhss\" },\n          { label: \"自定义表达式\", prop: \"zdybds\" },\n          {\n            prop: \"operation\",\n            label: \"操作\",\n            minWidth: \"100px\",\n            style: { display: \"block\" },\n            //操作列固定再右侧\n            fixed: \"right\",\n            operation: [\n              { name: \"修改\", clickFun: this.undateDetails },\n              { name: \"详情\", clickFun: this.detailsInfo },\n            ],\n          },\n        ],\n        option: { checkBox: true, serialNumber: true },\n      },\n      issyhsList: [\n        { label: \"是\", value: 1 },\n        { label: \"否\", value: 2 },\n      ],\n      issqlhsList: [\n        { label: \"是\", value: 1 },\n        { label: \"否\", value: 2 },\n      ],\n      isfxhsLisr: [\n        { label: \"是\", value: 1 },\n        { label: \"否\", value: 2 },\n      ],\n      queryHskwhParam: {\n        hsflmc: \"\",\n        pageNum: 1,\n        pageSize: 10,\n      },\n\n      //组织树\n      treeOptions: [],\n      //删除是否可用\n      multipleSensor: true,\n      //查询参数\n      queryParams: {\n        hsflmc: \"\",\n        pageNum: 1,\n        pageSize: 10,\n      },\n      //填入数据校验\n      rules: {\n        hsmc: [\n          { required: true, message: \"函数名称不能为空\", trigger: \"blur\" },\n        ],\n        issyhs: [\n          { required: true, message: \"试验函数不能为空\", trigger: \"blur\" },\n        ],\n        isfxhs: [\n          { required: true, message: \"分析函数不能为空\", trigger: \"blur\" },\n        ],\n        issqlhs: [\n          { required: true, message: \"SQL函数不能为空\", trigger: \"blur\" },\n        ],\n      },\n      //表单开关\n      isSearchShow: false,\n    };\n  },\n  watch: {},\n  created() {\n    //查询table列表\n    this.getData();\n    this.getTreeOption();\n  },\n  methods: {\n    //查询树方法\n    getTreeOption() {\n      getTreeData().then((res) => {\n        this.treeOptions = res.data;\n      });\n      // getHsflkList().then((res) => {\n      //   this.hsflmclist = res.data;\n      // });\n    },\n    //树节点点击事件\n    handleNodeClick(data) {\n      if (data.id != undefined && data.id != '0000') {\n        this.form.hsflid = data.id;\n        this.form.hsflmc = data.label;\n        this.queryHskwhParam.hsflmc = data.label;\n        this.queryParams.hsflmc = data.label;\n        this.getData();\n      }else {\n        this.queryHskwhParam.hsflmc = ''\n        this.getData();\n      }\n    },\n\n    //添加按钮\n    addSensorButton() {\n      if (this.form.hsflid === undefined) {\n        this.$message.warning(\"请在选择函数分类库后新增记录\");\n        return;\n      }\n      //打开弹窗\n      this.isShowDetails = true;\n      //表单可编辑\n      this.isDisabled = false;\n      //设置弹出框标题\n      this.title = \"新增\";\n    },\n    //修改按钮\n    undateDetails(row) {\n      //打开弹窗\n      this.isShowDetails = true;\n      //给表单赋值\n      this.form = row;\n      //表单可编辑\n      this.isDisabled = false;\n      //设置弹出框标题\n      this.title = \"修改\";\n    },\n\n    //确认提交\n    commitAdd() {\n      console.log(\"开始\" + this.form.hsflid);\n      this.$refs[\"form\"].validate((valid) => {\n        if (valid) {\n          saveOrUpdate(this.form).then((res) => {\n            if (res.code === \"0000\") {\n              this.$message.success(res.msg);\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n              this.isShowDetails = false;\n              this.form = {};\n            } else {\n              this.$message.error(res.msg);\n            }\n          });\n        }\n      });\n    },\n\n    close() {\n      this.isShowDetails = false;\n    },\n    //定义重置方法\n    getReset() {},\n    //编辑按钮\n    updateSensorButton() {\n      //打开弹窗\n      this.isShowDetails = true;\n      //给表单赋值\n      this.form = row;\n      //表单可编辑\n      this.isDisabled = false;\n      //设置弹出框标题\n      this.title = \"修改\";\n    },\n    //详情按钮\n    detailsInfo(row) {\n      this.title = \"详情\";\n      //打开弹窗\n      this.isShowDetails = true;\n      //把行数据给弹出框表单\n      this.form = { ...row };\n      //将表单不可编辑\n      this.isDisabled = true;\n    },\n\n    //删除按钮\n    getDelete() {\n      if (this.selectRows.length < 1) {\n        this.$message.warning(\"请选择正确的数据！！！\");\n        return;\n      }\n      let ids = this.selectRows.map((item) => {\n        return item.objId;\n      });\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      })\n        .then(() => {\n          remove(ids).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\",\n              });\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\",\n              });\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\",\n          });\n        });\n    },\n\n    //导出按钮\n    handleExport() {},\n\n    //行选中\n    handleSelectionChange(rows) {\n      this.selectRows = rows;\n      this.whmjzButtonDisabled = rows.length != 1;\n      //获取到当前行对象\n      this.mjzRowForm = rows[0];\n    },\n    //查询列表\n    async getData(params) {\n      try {\n        const param = { ...this.queryHskwhParam, ...params };\n        const { data, code } = await getPageDataList(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n\n    //搜索\n    handleQuery() {},\n    //重置\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n    },\n  },\n};\n</script>\n\n<style lang='scss'>\n.el-tree-node:focus > .el-tree-node__content {\n  background-color: #66b1ff87 !important;\n}\n.el-tree-node__content:hover {\n  background-color: #66b1ff87;\n}\n.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {\n  background-color: #66b1ff87;\n}\n\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  max-height: 79vh;\n  overflow: auto;\n}\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk"}]}