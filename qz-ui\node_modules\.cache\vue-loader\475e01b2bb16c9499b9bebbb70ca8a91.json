{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sdsbgl\\sdxd.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sdsbgl\\sdxd.vue", "mtime": 1706897324964}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldExpc3RzLCBzYXZlT3JVcGRhdGUsIHJlbW92ZSB9IGZyb20gJ0AvYXBpL2RhZ2FuZ09pbGZpZWxkL2R3enlnbC9zZHNiZ2wvc2R4ZCcKaW1wb3J0IHsgZ2V0VHJlZUxpc3QgfSBmcm9tICdAL2FwaS9kYWdhbmdPaWxmaWVsZC9hc3NldC9zZGd0JwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdzZHhkJywKICB3YXRjaDogewogICAgLyoqCiAgICAgKiDnm5HlkKzkuIvmi4nmoJHnrZvpgIkKICAgICAqLwogICAgZmlsdGVyVGV4dCh2YWwpIHsKICAgICAgdGhpcy4kcmVmcy50cmVlLmZpbHRlcih2YWwpCiAgICB9CgogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8v5p+l6K+i57q/6Lev55qE5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAKICAgICAgfSwKICAgICAgLy/miYDlsZ7nur/ot6/lkI3np7AKICAgICAgc3N4bG1jTGlzdDogW3sgbGFiZWw6ICcnLCBsaW5lTmFtZTogJycgfV0sCiAgICAgIC8v5Li75bmy57q/57q/5q615ZCN56ewCiAgICAgIHpneHhkbWNMaXN0OiBbXSwKICAgICAgLy/mlK/nur/nur/mrrXlkI3np7AKICAgICAgc3N6Z3htY0xpc3Q6IFtdLAogICAgICAvL+aYr+WQpuS4uuS4u+W5sue6v+e6v+autQogICAgICBzZnd6Z3h4ZExpc3Q6IFt7IGxhYmVsOiAn5pivJywgdmFsdWU6ICfmmK8nIH0sIHsgbGFiZWw6ICflkKYnLCB2YWx1ZTogJ+WQpicgfV0sCiAgICAgIC8v5piv5ZCm6IGU57uc57q/6LevCiAgICAgIHNmbGx4bExpc3Q6IFt7IGxhYmVsOiAn5pivJywgdmFsdWU6ICfmmK8nIH0sIHsgbGFiZWw6ICflkKYnLCB2YWx1ZTogJ+WQpicgfV0sCiAgICAgIC8v5piv5ZCm5Li65Ye657q/5byA5YWzCiAgICAgIHNmd2N4a2dMaXN0OiBbeyBsYWJlbDogJ+aYrycsIHZhbHVlOiAn5pivJyB9LCB7IGxhYmVsOiAn5ZCmJywgdmFsdWU6ICflkKYnIH1dLAogICAgICAvL2Zvcm3ooajljZUKICAgICAgZm9ybTogewogICAgICAgIGJoOiAnJywKICAgICAgICBsaW5lTmFtZTogJycsCiAgICAgICAgeGxibTogJycsCiAgICAgICAgc2Z3emd4eGQ6ICcnLAogICAgICAgIHpneHhkbWM6ICcnLAogICAgICAgIHNzemd4bWM6ICcnLAogICAgICAgIHp4eGRibTogJycsCiAgICAgICAgZXJwYm06ICcnLAogICAgICAgIGJncjogJycsCiAgICAgICAgemNiZGZzOiAnJywKICAgICAgICB6Y2JoOiAnJywKICAgICAgICB3YnN5czogJycsCiAgICAgICAgZHhseDogJycsCiAgICAgICAgZHh4aDogJycsCiAgICAgICAgeGRibTogJycsCiAgICAgICAgemNzeDogJycsCiAgICAgICAgZHF0ejogJycsCiAgICAgICAgeGRseDogJycsCiAgICAgICAgcWQ6ICcnLAogICAgICAgIHpkOiAnJywKICAgICAgICB0eXJxOiAnJywKICAgICAgICB4ZGNkOiAnJywKICAgICAgICBzZmxseGw6ICcnLAogICAgICAgIGxsbHg6ICcnLAogICAgICAgIGtnbHg6ICcnLAogICAgICAgIGtnYmg6ICcnLAogICAgICAgIHNmd2N4a2c6ICcnLAogICAgICAgIGR4am06ICcnLAogICAgICAgIHNjY2o6ICcnLAogICAgICAgIGRsanljejogJycsCiAgICAgICAgZGx4aDogJycsCiAgICAgICAgeHM6ICcnLAogICAgICAgIGZzZnM6ICcnLAogICAgICAgIHhkeHo6ICcnLAogICAgICAgIGJ6OiAnJwogICAgICB9LAogICAgICAvL+aYr+WQpuWxleekuue6v+i3r+WQjeensAogICAgICBpc1Nob3dYbG1jOiBmYWxzZSwKICAgICAgLy/or6bmg4XlvLnmoYbmmK/lkKbmmL7npLoKICAgICAgaXNTaG93RGV0YWlsczogZmFsc2UsCiAgICAgIC8v5piv5ZCm56aB55SoCiAgICAgIGlzRGlzYWJsZWQ6IGZhbHNlLAogICAgICAvL+aYr+WQpuWxleekuuS4uuaUr+e6vwogICAgICBpc1Nob3daeDogZmFsc2UsCiAgICAgIC8v5piv5ZCm5bGV56S66IGU57uc57q/6LevCiAgICAgIGlzU2hvd1hsOiBmYWxzZSwKICAgICAgLy/mmK/lkKblsZXnpLrkuLrlh7rnur/lvIDlhbMKICAgICAgaXNTaG93S2c6IGZhbHNlLAogICAgICAvL+ebkeWQrOS4i+aLieagkeetm+mAiQogICAgICBmaWx0ZXJUZXh0OiAnJywKICAgICAgLy/moIfpopgKICAgICAgdGl0bGU6ICcnLAogICAgICAvLyDlt6bkvqfkuIvmi4nmoJHmlbDmja4KICAgICAgdHJlZURhdGE6IFtdLAogICAgICBkZWZhdWx0UHJvcHM6IHsKICAgICAgICBjaGlsZHJlbjogJ2NoaWxkcmVuJywKICAgICAgICBsYWJlbDogJ2xhYmVsJwogICAgICB9LAogICAgICBmaWx0ZXJJbmZvOiB7CiAgICAgICAgZGF0YTogewogICAgICAgICAgYmg6ICcnLAogICAgICAgICAgbGluZU5hbWU6ICcnLAogICAgICAgICAgeGxibTonJywKICAgICAgICAgIHpjc3g6JycsCiAgICAgICAgICBkcXR6OicnLAogICAgICAgIH0sLy/mn6Xor6LmnaHku7YKICAgICAgICBmaWVsZExpc3Q6IFsKICAgICAgICAgIHsgbGFiZWw6ICfmiYDlsZ7nur/ot6/lkI3np7AnLCB2YWx1ZTogJ2xpbmVOYW1lJywgdHlwZTogJ3NlbGVjdCcsIGNsZWFyYWJsZTogdHJ1ZSB9LAogICAgICAgICAgeyBsYWJlbDogJ+e6v+auteWQjeensCcsIHZhbHVlOiAnemd4eGRtYycsIHR5cGU6ICdzZWxlY3QnLCBjbGVhcmFibGU6IHRydWUgfSwKICAgICAgICAgIHsgbGFiZWw6ICfotYTkuqflsZ7mgKcnLCB2YWx1ZTogJ3pjc3gnLCB0eXBlOiAnaW5wdXQnLCBjbGVhcmFibGU6IHRydWUgfSwKICAgICAgICAgIHsgbGFiZWw6ICflnLDljLrnibnlvoEnLCB2YWx1ZTogJ2RxdHonLCB0eXBlOiAnaW5wdXQnLCBjbGVhcmFibGU6IHRydWUgfSwKICAgICAgICAgIHsgbGFiZWw6ICfnur/mrrXnsbvlnosnLCB2YWx1ZTogJ3hkbHgnLCB0eXBlOiAnaW5wdXQnLCBjbGVhcmFibGU6IHRydWUgfSwKICAgICAgICAgIHsgbGFiZWw6ICfotbfngrknLCB2YWx1ZTogJ3FkJywgdHlwZTogJ2lucHV0JywgY2xlYXJhYmxlOiB0cnVlIH0sCiAgICAgICAgICB7IGxhYmVsOiAn57uI54K5JywgdmFsdWU6ICd6ZCcsIHR5cGU6ICdpbnB1dCcsIGNsZWFyYWJsZTogdHJ1ZSB9LAogICAgICAgICAgeyBsYWJlbDogJ+aKlei/kOaXpeacnycsIHZhbHVlOiAndHlycScsIHR5cGU6ICdkYXRlJywgZGF0ZVR5cGU6ICdkYXRlcmFuZ2UnLCBmb3JtYXQ6ICd5eXl5LU1NLWRkJyB9LAogICAgICAgICAgeyBsYWJlbDogJ+e6v+autemVv+W6pihrbSknLCB2YWx1ZTogJ3hkY2QnLCB0eXBlOiAnaW5wdXQnLCBjbGVhcmFibGU6IHRydWUgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgdGFibGVBbmRQYWdlSW5mbzogewogICAgICAgIHBhZ2VyOiB7CiAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgdG90YWw6IDAsCiAgICAgICAgICBzaXplczogWzEwLCAyMCwgNTAsIDEwMF0KICAgICAgICB9LAogICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgdGFibGVIZWFkZXI6IFsKICAgICAgICAgIHsgbGFiZWw6ICfmiYDlsZ7nur/ot6/lkI3np7AnLCBwcm9wOiAnbGluZU5hbWUnLCBtaW5XaWR0aDogJzkwJyB9LAogICAgICAgICAgeyBsYWJlbDogJ+aYr+WQpuS4uuS4u+W5sue6v+e6v+autScsIHByb3A6ICdzZnd6Z3h4ZCcsIG1pbldpZHRoOiAnNzAnIH0sCiAgICAgICAgICB7IGxhYmVsOiAn57q/5q615ZCN56ewJywgcHJvcDogJ3pneHhkbWMnLCBtaW5XaWR0aDogJzEyMCcgfSwKICAgICAgICAgIHsgbGFiZWw6ICfotYTkuqflsZ7mgKcnLCBwcm9wOiAnemNzeCcsIG1pbldpZHRoOiAnOTAnIH0sCiAgICAgICAgICB7IGxhYmVsOiAn5Zyw5Yy654m55b6BJywgcHJvcDogJ2RxdHonLCBtaW5XaWR0aDogJzc1JyB9LAogICAgICAgICAgeyBsYWJlbDogJ+e6v+auteexu+WeiycsIHByb3A6ICd4ZGx4JywgbWluV2lkdGg6ICc4MCcgfSwKICAgICAgICAgIHsgbGFiZWw6ICfotbfngrknLCBwcm9wOiAncWQnLCBtaW5XaWR0aDogJzYwJyB9LAogICAgICAgICAgeyBsYWJlbDogJ+e7iOeCuScsIHByb3A6ICd6ZCcsIG1pbldpZHRoOiAnNjAnIH0sCiAgICAgICAgICB7IGxhYmVsOiAn5oqV6L+Q5pel5pyfJywgcHJvcDogJ3R5cnEnLCBtaW5XaWR0aDogJzg3JyB9LAogICAgICAgICAgeyBsYWJlbDogJ+e6v+autemVv+W6pihrbSknLCBwcm9wOiAneGRjZCcsIG1pbldpZHRoOiAnNzAnIH0sCiAgICAgICAgICB7IGxhYmVsOiAn5piv5ZCm6IGU57uc57q/6LevJywgcHJvcDogJ3NmbGx4bCcsIG1pbldpZHRoOiAnNjAnIH0sCiAgICAgICAgICB7IGxhYmVsOiAn5piv5ZCm5Li65Ye657q/5byA5YWzJywgcHJvcDogJ3Nmd2N4a2cnLCBtaW5XaWR0aDogJzYwJyB9LAogICAgICAgICAgewogICAgICAgICAgICBwcm9wOiAnb3BlcmF0aW9uJywKICAgICAgICAgICAgbGFiZWw6ICfmk43kvZwnLAogICAgICAgICAgICBtaW5XaWR0aDogJzEwMHB4JywKICAgICAgICAgICAgc3R5bGU6IHsgZGlzcGxheTogJ2Jsb2NrJyB9LAogICAgICAgICAgICAvL+aTjeS9nOWIl+WbuuWumuWGjeWPs+S+pwogICAgICAgICAgICBmaXhlZDogJ3JpZ2h0JywKICAgICAgICAgICAgb3BlcmF0aW9uOiBbCiAgICAgICAgICAgICAgeyBuYW1lOiAn5L+u5pS5JywgY2xpY2tGdW46IHRoaXMuZ2V0VXBkYXRlIH0sCiAgICAgICAgICAgICAgeyBuYW1lOiAn6K+m5oOFJywgY2xpY2tGdW46IHRoaXMuZ2V0RGV0YWlscyB9CiAgICAgICAgICAgICAgLyp7IG5hbWU6ICfpmYTku7bmn6XnnIsnLCBjbGlja0Z1bjogdGhpcy5nZXRGakluZm9MaXN0IH0sKi8KICAgICAgICAgICAgXQogICAgICAgICAgfQogICAgICAgIF0sCiAgICAgICAgb3B0aW9uOiB7IGNoZWNrQm94OiB0cnVlLCBzZXJpYWxOdW1iZXI6IHRydWUgfQogICAgICB9LAogICAgICBwYXJhbXM6IHsKICAgICAgICBsaW5lTmFtZTogJycsCiAgICAgICAgeGxibTogJycsCiAgICAgICAgemNzeDonJywKICAgICAgICBkcXR6OicnLAogICAgICB9LAogICAgICBzZWxlY3RSb3dzOiBbXSwKICAgICAgcnVsZXM6ewogICAgICAgIGxpbmVOYW1lOlt7cmVxdWlyZWQ6dHJ1ZSxtZXNzYWdlOifor7fpgInmi6nmiYDlsZ7nur/ot68nLHRyaWdnZXI6J2NoYW5nZSd9XSwKICAgICAgICB4bGJtOlt7cmVxdWlyZWQ6dHJ1ZSxtZXNzYWdlOifor7fpgInmi6nmiYDlsZ7nur/ot6/ov5DooYznvJblj7cnLHRyaWdnZXI6J2JsdXInfV0sCiAgICAgICAgemd4eGRtYzpbe3JlcXVpcmVkOnRydWUsbWVzc2FnZTon6K+36L6T5YWl57q/5q615ZCN56ewJyx0cmlnZ2VyOidibHVyJ31dLAogICAgICAgIHNmd3pneHhkOlt7cmVxdWlyZWQ6dHJ1ZSxtZXNzYWdlOifor7fpgInmi6nmmK/lkKbkuLvlubLnur/mrrUnLHRyaWdnZXI6J2NoYW5nZSd9XSwKICAgICAgICB4ZGJtOlt7cmVxdWlyZWQ6dHJ1ZSxtZXNzYWdlOifor7fovpPlhaXnur/mrrXnvJbnoIEnLHRyaWdnZXI6J2JsdXInfV0sCiAgICAgICAgc3N6Z3htYzpbe3JlcXVpcmVkOnRydWUsbWVzc2FnZTon6K+36L6T5YWl5omA5bGe5Li75bmy57q/5q615ZCN56ewJyx0cmlnZ2VyOidibHVyJ31dLAogICAgICAgIHpjc3g6W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+i+k+WFpei1hOS6p+WxnuaApycsdHJpZ2dlcjonYmx1cid9XSwKICAgICAgICBxZDpbe3JlcXVpcmVkOnRydWUsbWVzc2FnZTon6K+36L6T5YWl57q/5q616LW354K5Jyx0cmlnZ2VyOidibHVyJ31dLAogICAgICAgIHpkOlt7cmVxdWlyZWQ6dHJ1ZSxtZXNzYWdlOifor7fovpPlhaXnur/mrrXnu4jngrknLHRyaWdnZXI6J2JsdXInfV0sCiAgICAgIH0sCiAgICB9CiAgfSwKICBjcmVhdGVkKCkgewogICAgLy/liJfooajmn6Xor6IKICAgIHRoaXMuZ2V0RGF0YSgpCiAgICAvL3RoaXMuZ2V0VGFibGVMaXN0KHRoaXMucXVlcnlQYXJhbXMpOwogICAgdGhpcy50cmVlTGlzdCgpOwogIH0sCiAgbWV0aG9kczogewogICAgLy/mmK/lkKbkuLrmlK/nur8KICAgIGNoYW5nZUNsaWNrWngodmFsKSB7CiAgICAgIGlmICh2YWwgPT0gJ+aYrycpIHsKICAgICAgICB0aGlzLmlzU2hvd1p4ID0gdHJ1ZQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuaXNTaG93WnggPSBmYWxzZQogICAgICB9CiAgICB9LAogICAgLy/mmK/lkKbkuLrogZTnu5znur/ot68KICAgIGNoYW5nZUNsaWNrWGwodmFsKSB7CiAgICAgIGlmICh2YWwgPT0gJ+aYrycpIHsKICAgICAgICB0aGlzLmlzU2hvd1hsID0gZmFsc2UKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmlzU2hvd1hsID0gdHJ1ZQogICAgICB9CiAgICB9LAogICAgLy/mmK/lkKbkuLrlh7rnur/lvIDlhbMKICAgIGNoYW5nZUNsaWNrS2codmFsKSB7CiAgICAgIGlmICh2YWwgPT0gJ+aYrycpIHsKICAgICAgICB0aGlzLmlzU2hvd0tnID0gZmFsc2UKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmlzU2hvd0tnID0gdHJ1ZQogICAgICB9CiAgICB9LAogICAgLy/liJfooajmn6Xor6IKICAgIGFzeW5jIGdldERhdGEocGFyYW1zKSB7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgcGFyYW0gPSB7Li4udGhpcy5wYXJhbXMsIC4uLnBhcmFtc30KICAgICAgICBjb25zdCB7IGRhdGEsIGNvZGUgfSA9IGF3YWl0IGdldExpc3RzKHBhcmFtKQogICAgICAgIGlmIChjb2RlID09PSAnMDAwMCcpIHsKICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby50YWJsZURhdGEgPSBkYXRhLnJlY29yZHMKICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci50b3RhbCA9IGRhdGEudG90YWwKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgfQogICAgfSwKCiAgICAvL+afpeivoui+k+eUtee6v+i3r+S/oeaBr1AKICAgIGdldFRhYmxlTGlzdDogZnVuY3Rpb24ocGFyYW1zKSB7CiAgICAgIGdldExpc3QocGFyYW1zKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5zc3hsbWNMaXN0ID0gcmVzLmRhdGEucmVjb3JkcwogICAgICAgIC8qdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnRhYmxlRGF0YSA9IHJlcy5kYXRhLnJlY29yZHMqLwogICAgICB9KQogICAgfSwKICAgIC8v5o+Q5Lqk5p+l6K+i5Yiw55qE6L6T55S157q/6Lev5L+h5oGvCiAgICBjb21taXQoKSB7CiAgICAgIHRoaXMuaXNTaG93WGxtYyA9IGZhbHNlCiAgICB9LAoKICAgIC8v6YeN572u5oyJ6ZKuCiAgICBnZXRSZXNldCgpIHsKICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICB9LAogICAgLy/pgInkuK3ooYwKICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZSgpIHsKCiAgICB9LAogICAgLy/nur/ot6/lkI3np7DlvLnmoYYKICAgIGdldFhsbWMoKSB7CiAgICAgIHRoaXMuaXNTaG93WGxtYyA9IHRydWUKICAgIH0sCiAgICAvL+aWsOWinuaMiemSrgogICAgZ2V0SW5zdGVyKCkgewogICAgICBpZih0aGlzLmZvcm0ubGluZU5hbWUhPScnKXsKICAgICAgICB0aGlzLnRpdGxlID0gJ+aWsOWinui+k+eUtee6v+autScKICAgICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZQogICAgICAgIC8vdGhpcy5mb3JtID0ge30KICAgICAgICAvKnRoaXMuc3N4bG1jTGlzdD0gJycqLwogICAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IHRydWUKICAgICAgfWVsc2V7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7flhYjpgInmi6nnur/ot6/lkI7mlrDlop7nur/mrrUiKTsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KCiAgICB9LAogICAgLy/kv67mlLnmjInpkq4KICAgIGdldFVwZGF0ZShyb3cpIHsKICAgICAgdGhpcy50aXRsZSA9ICfkv67mlLnovpPnlLXnur/mrrUnCiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlCiAgICAgIHRoaXMuZm9ybSA9IHsgLi4ucm93IH0KICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gdHJ1ZQogICAgICAvL+iOt+WPluaYr+aIluWQpui/m+ihjOWIpOaWre+8jOaYr+WQpuWPr+S7pee8lui+kQogICAgICBpZiAocm93LnNmd3pneHhkID09ICfmmK8nKSB7CiAgICAgICAgdGhpcy5pc1Nob3daeCA9IHRydWUKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmlzU2hvd1p4ID0gZmFsc2UKICAgICAgfQoKICAgICAgaWYgKHJvdy5zZmxseGwgPT0gJ+aYrycpIHsKICAgICAgICB0aGlzLmlzU2hvd1hsID0gZmFsc2UKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmlzU2hvd1hsID0gdHJ1ZQogICAgICB9CgogICAgICBpZiAocm93LnNmd2N4a2cgPT0gJ+WQpicpIHsKICAgICAgICB0aGlzLmlzU2hvd0tnID0gdHJ1ZQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuaXNTaG93S2cgPSBmYWxzZQogICAgICB9CiAgICB9LAogICAgLy/or6bmg4XmjInpkq4KICAgIGdldERldGFpbHMocm93KSB7CiAgICAgIHRoaXMudGl0bGUgPSAn6L6T55S157q/5q616K+m5oOFJwogICAgICB0aGlzLmZvcm0gPSB7IC4uLnJvdyB9CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IHRydWUKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gdHJ1ZQogICAgICB0aGlzLmlzU2hvd1p4ID0gdHJ1ZQogICAgICB0aGlzLmlzU2hvd1hsID0gdHJ1ZQogICAgICB0aGlzLmlzU2hvd0tnID0gdHJ1ZQogICAgfSwKICAgIC8qLy/pmYTku7bmn6XnnIsKICAgIGdldEZqSW5mb0xpc3QoKSB7CiAgICAgIHRoaXMudGl0bGUgPSAn6ZmE5Lu25p+l55yLJwogICAgICB0aGlzLmlzRGlzYWJsZWQgPSB0cnVlCiAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IHRydWUKICAgICAgdGhpcy5mb3JtPXsuLi5yb3d9CiAgICB9LCovCiAgICAvL+S/neWtmOaMiemSrgogICAgYXN5bmMgc2F2ZVJvdygpIHsKICAgICAgdHJ5IHsKICAgICAgICAgIHRoaXMuJHJlZnNbJ2Zvcm0nXS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICAgICAgICBsZXQgeyBjb2RlIH0gPSAgc2F2ZU9yVXBkYXRlKHRoaXMuZm9ybSkKICAgICAgICAgICAgICAgIGlmIChjb2RlID09PSAnMDAwMCcpIHsKICAgICAgICAgICAgICAgICAgdGhpcy5nZXREYXRhKCkKICAgICAgICAgICAgICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gZmFsc2UKICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmk43kvZzmiJDlip8nKQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgICAgICAgdmFyIGlzRXJyb3IgPSBkb2N1bWVudC5nZXRFbGVtZW50c0J5Q2xhc3NOYW1lKCJpcy1lcnJvciIpOwogICAgICAgICAgICAgICAgaWYgKGlzRXJyb3JbMF0ucXVlcnlTZWxlY3RvcignaW5wdXQnKSkgewogICAgICAgICAgICAgICAgICBpc0Vycm9yWzBdLnF1ZXJ5U2VsZWN0b3IoJ2lucHV0JykuZm9jdXMoKTsKICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoaXNFcnJvclswXS5xdWVyeVNlbGVjdG9yKCd0ZXh0YXJlYScpKSB7CiAgICAgICAgICAgICAgICAgIGlzRXJyb3JbMF0ucXVlcnlTZWxlY3RvcigndGV4dGFyZWEnKS5mb2N1cygpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0sIDEpCiAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgICAgY29uc29sZS5sb2coZSkKICAgICAgfQoKICAgIH0sCiAgICAvL+WIoOmZpOaMiemSrgogICAgYXN5bmMgZGVsZXRlUm93KCkgewogICAgICBpZiAodGhpcy5zZWxlY3RSb3dzLmxlbmd0aCA8IDEpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+mAieaLqeato+ehrueahOaVsOaNru+8ge+8ge+8gScpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KICAgICAgbGV0IGlkcyA9IHRoaXMuc2VsZWN0Um93cy5tYXAoaXRlbSA9PiB7CiAgICAgICAgcmV0dXJuIGl0ZW0uaWQKICAgICAgfSkKICAgICAgdGhpcy4kY29uZmlybSgn5q2k5pON5L2c5bCG5rC45LmF5Yig6Zmk6K+l5pWw5o2uLCDmmK/lkKbnu6fnu60/JywgJ+aPkOekuicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHJlbW92ZShpZHMpLnRoZW4oKHsgY29kZSB9KSA9PiB7CiAgICAgICAgICBpZiAoY29kZSA9PT0gJzAwMDAnKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICAgICAgICBtZXNzYWdlOiAn5Yig6Zmk5oiQ5YqfIScKICAgICAgICAgICAgfSkKICAgICAgICAgICAgdGhpcy5nZXREYXRhKCkKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOWksei0pSEnCiAgICAgICAgICAgIH0pCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ2luZm8nLAogICAgICAgICAgbWVzc2FnZTogJ+W3suWPlua2iOWIoOmZpCcKICAgICAgICB9KQogICAgICB9KQogICAgICBhd2FpdCB0aGlzLmdldERhdGEoKQogICAgfSwKICAgIHRyZWVMaXN0KCl7CiAgICAgICAgZ2V0VHJlZUxpc3QoKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICB0aGlzLnRyZWVEYXRhID0gcmVzLmRhdGEKICAgICAgICB9KTsKICAgIH0sCiAgICAvL+WFs+mXreW8ueeqlwogICAgY2xvc2UoKSB7CiAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IGZhbHNlCiAgICB9LAogICAgcmVzZXRGb3JtKCl7CiAgICAgIHRoaXMuZm9ybSA9IHt9CiAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICB0aGlzLiRyZWZzWydmb3JtJ10uY2xlYXJWYWxpZGF0ZSgpOwogICAgICB9KTsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gZmFsc2U7CgogICAgfSwKICAgIC8v562b6YCJ5p2h5Lu2CiAgICBzZWxlY3RDaGFuZ2Uocm93cykgewogICAgICB0aGlzLnNlbGVjdFJvd3MgPSByb3dzCiAgICB9LAogICAgLy/lt6bkvqfmoJHngrnlh7vkuovku7YKICAgIGhhbmRsZU5vZGVDbGljayhkYXRhKXsKICAgICAgaWYoZGF0YS5pZGVudGlmaWVyPT0nMycpewogICAgICAgICAgdGhpcy5mb3JtLmxpbmVOYW1lPWRhdGEubGFiZWw7CiAgICAgICAgICB0aGlzLmZvcm0ueGxibT1kYXRhLmlkOwogICAgICAgICAgdGhpcy5maWx0ZXJJbmZvLmRhdGEueGxibT1kYXRhLmlkOwogICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICB9ZWxzZSBpZihkYXRhLmlkZW50aWZpZXI9PScxJyl7CiAgICAgICAgdGhpcy5maWx0ZXJJbmZvLmRhdGEueGxibT0nJzsKICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgfQoKICAgIH0sCiAgICBxdWVyeSgpewogICAgIHRoaXMuZmlsdGVySW5mby5kYXRhPXsuLi50aGlzLmZpbHRlckluZm8uZGF0YX0KICAgIH0sCiAgfSwKCgp9Cg=="}, {"version": 3, "sources": ["sdxd.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsSA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA", "file": "sdxd.vue", "sourceRoot": "src/views/dagangOilfield/dwzygl/sdsbgl", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row gutter=\"16\">\n      <!--左侧菜单树-->\n      <el-col :span=\"4\">\n        <el-card>\n          <div style=\"overflow: auto;height: 80vh\">\n            <el-tree highlight-current class=\"filter-tree\" :data=\"treeData\"\n                     :props=\"defaultProps\" ref=\"tree\" @node-click=\"handleNodeClick\" accordion><!--@node-click=\"handleNodeClick\"-->\n            </el-tree>\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--基本信息查询及显示-->\n      <el-col :span=\"20\">\n        <!--搜索条件-->\n        <el-filter\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 120, itemWidth: 230 }\"\n          @getMethod=\"query\"\n          @handleReset=\"getReset\"\n        />\n\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"getInster\">新增</el-button>\n            <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"deleteRow\">删除</el-button>\n          </div>\n          <comp-table  @getMethod=\"query\" :table-and-page-info=\"tableAndPageInfo\" @update:multipleSelection=\"selectChange\" height=\"61vh\"/>\n\n        </el-white>\n\n      </el-col>\n    </el-row>\n\n\n    <!-- 详情/新增/修改 -->\n    <el-dialog :title=title :visible.sync=\"isShowDetails\" width=\"50%\" :before-close=\"resetForm\">\n      <el-form label-width=\"180px\" ref=\"form\" :model=\"form\" :rules=\"rules\" >\n        <div>\n          <!--线段基本信息-->\n          <el-card class=\"box-card\">\n            <div slot=\"header\">\n              <span>基本信息</span>\n            </div>\n            <el-col :span=\"12\">\n              <el-form-item label=\"所属线路名称：\" prop=\"lineName\">\n                <el-input v-model=\"form.lineName\" :disabled=\"isDisabled\"  placeholder=\"请输入内容\">\n                </el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"所属线路运行编号：\" prop=\"xlbm\">\n                <el-input v-model=\"form.xlbm\" :disabled=\"true\" placeholder=\"请输入内容\"></el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"线段名称：\" prop=\"zgxxdmc\">\n                <el-input v-model=\"form.zgxxdmc\" :disabled=\"isDisabled\" placeholder=\"请输入内容\">\n                  <!--<el-option\n                    v-for=\"item in zgxxdmcList\"\n                    :key=\"item.value\"\n                    :label=\"item.bdz\"\n                    :value=\"item.bdz\">\n                  </el-option>-->\n                </el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"线段编码：\" prop=\"xdbm\">\n                <el-input v-model=\"form.xdbm\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"是否为主干线线段：\" prop=\"sfwzgxxd\" class=\"red\">\n                <el-select @change=\"changeClickZx\" v-model=\"form.sfwzgxxd\" :disabled=\"isDisabled\" placeholder=\"必填\">\n                  <el-option\n                    v-for=\"item in sfwzgxxdList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"所属主干线名称：\" prop=\"sszgxmc\">\n                <el-input v-model=\"form.sszgxmc\" :disabled=\"isShowZx\" placeholder=\"请输入内容\">\n                  <!--<el-option\n                    v-for=\"item in sszgxmcList\"\n                    :key=\"item.value\"\n                    :label=\"item.bdz\"\n                    :value=\"item.bdz\">\n                  </el-option>-->\n                </el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"资产属性：\" prop=\"zcsx\">\n                <el-input v-model=\"form.zcsx\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item label=\"地区特征：\" prop=\"dqtz\">\n                <el-input v-model=\"form.dqtz\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"线段类型：\" prop=\"xdlx\">\n                <el-select v-model=\"form.xdlx\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item label=\"起点：\" prop=\"qd\">\n                <el-input v-model=\"form.qd\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"终点：\" prop=\"zd\">\n                <el-input v-model=\"form.zd\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item label=\"投运日期：\" prop=\"tyrq\">\n                <el-date-picker\n                  v-model=\"form.tyrq\"\n                  :disabled=\"isDisabled\"\n                  type=\"date\"\n                  placeholder=\"选择日期时间\"\n                  style=\"width: 100%\">\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"线段长度(Km)：\" prop=\"xdcd\">\n                <el-input v-model=\"form.xdcd\" @input=\"(v) => (form.xdcd = v.replace(/[^\\d]/g, ''))\"\n                          :disabled=\"isDisabled\" placeholder=\"请输入数字\"/>\n              </el-form-item>\n            </el-col>\n          </el-card>\n          <!--其它信息-->\n          <el-card class=\"box-card\">\n            <div slot=\"header\">\n              <span>其它信息</span>\n            </div>\n            <el-col :span=\"12\">\n              <el-form-item label=\"导线类型：\" prop=\"dxlx\">\n                <el-input v-model=\"form.dxlx\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"导线型号：\" prop=\"dxxh\">\n                <el-input v-model=\"form.dxxh\" :disabled=\"isDisabled\" placeholder=\"如果为导线时填写\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"是否联络线路：\" prop=\"sfllxl\">\n                <el-select @change=\"changeClickXl\" v-model=\"form.sfllxl\" :disabled=\"isDisabled\" placeholder=\"请输入内容\">\n                  <el-option\n                    v-for=\"item in sfllxlList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"联络类型：\" prop=\"lllx\">\n                <el-input v-model=\"form.lllx\" :disabled=\"isShowXl\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"是否为出线开关：\" prop=\"sfwcxkg\">\n                <el-select @change=\"changeClickKg\" v-model=\"form.sfwcxkg\" :disabled=\"isDisabled\" placeholder=\"请输入内容\">\n                  <el-option\n                    v-for=\"item in sfwcxkgList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"开关类型：\" prop=\"kglx\">\n                <el-input v-model=\"form.kglx\" :disabled=\"isShowKg\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"开关编号：\" prop=\"kgbh\">\n                <el-input v-model=\"form.kgbh\" :disabled=\"isShowKg\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"导线截面（mm2）：\" prop=\"dxjm\">\n                <el-input v-model=\"form.dxjm\" @input=\"(v) => (form.dxjm = v.replace(/[^\\d]/g, ''))\"\n                          :disabled=\"isDisabled\" placeholder=\"请输入数字\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"生产厂家：\" prop=\"sccj\">\n                <el-input v-model=\"form.sccj\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"电缆绝缘材质：\" prop=\"dljycz\">\n                <el-input v-model=\"form.dljycz\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"电缆型号：\" prop=\"dlxh\">\n                <el-input v-model=\"form.dlxh\" :disabled=\"isDisabled\" placeholder=\"如果为电缆填写\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"芯数：\" prop=\"xs\">\n                <el-input v-model=\"form.xs\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"敷设方式：\" prop=\"fsfs\">\n                <el-input v-model=\"form.fsfs\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"线段性质：\" prop=\"xdxz\">\n                <el-input v-model=\"form.xdxz\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"备注：\" prop=\"bz\">\n                <el-input type=\"textarea\" v-model=\"form.bz\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"/>\n              </el-form-item>\n            </el-col>\n          </el-card>\n\n        </div>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"resetForm\" size=\"small\">关 闭</el-button>\n        <el-button v-if=\"title=='新增输电线段' || title=='修改输电线段'\" type=\"primary\" size=\"small\" @click=\"saveRow\">保 存\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!--线路名称弹窗-->\n    <el-dialog title=\"请选择线路名称\" :visible.sync=\"isShowXlmc\" width=\"25%\" append-to-body>\n      <el-form :inline=\"true\" label-width=\"100px\" class=\"qxlr_dialog_insert\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"线路名称：\">\n              <el-select placeholder=\"请选择\" v-model=\"form.lineName\">\n                <el-option\n                  v-for=\"item in ssxlmcList\"\n                  :key=\"item.label\"\n                  :label=\"item.lineName\"\n                  :value=\"item.lineName\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"线路编码：\">\n              <el-select placeholder=\"请选择\" v-model=\"form.xlbm\">\n                <el-option\n                  v-for=\"item in ssxlmcList\"\n                  :key=\"item.label\"\n                  :label=\"item.xlbm\"\n                  :value=\"item.xlbm\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n\n      <span slot=\"footer\" class=\"dialog-footer\">\n    <el-button @click=\"isShowXlmc = false\">取 消</el-button>\n    <el-button type=\"primary\" @click=\"commit\">确 定</el-button>\n  </span>\n    </el-dialog>\n\n  </div>\n</template>\n\n\n<script>\n  import { getLists, saveOrUpdate, remove } from '@/api/dagangOilfield/dwzygl/sdsbgl/sdxd'\n  import { getTreeList } from '@/api/dagangOilfield/asset/sdgt'\n\n  export default {\n    name: 'sdxd',\n    watch: {\n      /**\n       * 监听下拉树筛选\n       */\n      filterText(val) {\n        this.$refs.tree.filter(val)\n      }\n\n    },\n    data() {\n      return {\n        //查询线路的参数\n        queryParams: {\n          pageNum: 1,\n          pageSize: 10\n        },\n        //所属线路名称\n        ssxlmcList: [{ label: '', lineName: '' }],\n        //主干线线段名称\n        zgxxdmcList: [],\n        //支线线段名称\n        sszgxmcList: [],\n        //是否为主干线线段\n        sfwzgxxdList: [{ label: '是', value: '是' }, { label: '否', value: '否' }],\n        //是否联络线路\n        sfllxlList: [{ label: '是', value: '是' }, { label: '否', value: '否' }],\n        //是否为出线开关\n        sfwcxkgList: [{ label: '是', value: '是' }, { label: '否', value: '否' }],\n        //form表单\n        form: {\n          bh: '',\n          lineName: '',\n          xlbm: '',\n          sfwzgxxd: '',\n          zgxxdmc: '',\n          sszgxmc: '',\n          zxxdbm: '',\n          erpbm: '',\n          bgr: '',\n          zcbdfs: '',\n          zcbh: '',\n          wbsys: '',\n          dxlx: '',\n          dxxh: '',\n          xdbm: '',\n          zcsx: '',\n          dqtz: '',\n          xdlx: '',\n          qd: '',\n          zd: '',\n          tyrq: '',\n          xdcd: '',\n          sfllxl: '',\n          lllx: '',\n          kglx: '',\n          kgbh: '',\n          sfwcxkg: '',\n          dxjm: '',\n          sccj: '',\n          dljycz: '',\n          dlxh: '',\n          xs: '',\n          fsfs: '',\n          xdxz: '',\n          bz: ''\n        },\n        //是否展示线路名称\n        isShowXlmc: false,\n        //详情弹框是否显示\n        isShowDetails: false,\n        //是否禁用\n        isDisabled: false,\n        //是否展示为支线\n        isShowZx: false,\n        //是否展示联络线路\n        isShowXl: false,\n        //是否展示为出线开关\n        isShowKg: false,\n        //监听下拉树筛选\n        filterText: '',\n        //标题\n        title: '',\n        // 左侧下拉树数据\n        treeData: [],\n        defaultProps: {\n          children: 'children',\n          label: 'label'\n        },\n        filterInfo: {\n          data: {\n            bh: '',\n            lineName: '',\n            xlbm:'',\n            zcsx:'',\n            dqtz:'',\n          },//查询条件\n          fieldList: [\n            { label: '所属线路名称', value: 'lineName', type: 'select', clearable: true },\n            { label: '线段名称', value: 'zgxxdmc', type: 'select', clearable: true },\n            { label: '资产属性', value: 'zcsx', type: 'input', clearable: true },\n            { label: '地区特征', value: 'dqtz', type: 'input', clearable: true },\n            { label: '线段类型', value: 'xdlx', type: 'input', clearable: true },\n            { label: '起点', value: 'qd', type: 'input', clearable: true },\n            { label: '终点', value: 'zd', type: 'input', clearable: true },\n            { label: '投运日期', value: 'tyrq', type: 'date', dateType: 'daterange', format: 'yyyy-MM-dd' },\n            { label: '线段长度(km)', value: 'xdcd', type: 'input', clearable: true }\n          ]\n        },\n        tableAndPageInfo: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          tableData: [],\n          tableHeader: [\n            { label: '所属线路名称', prop: 'lineName', minWidth: '90' },\n            { label: '是否为主干线线段', prop: 'sfwzgxxd', minWidth: '70' },\n            { label: '线段名称', prop: 'zgxxdmc', minWidth: '120' },\n            { label: '资产属性', prop: 'zcsx', minWidth: '90' },\n            { label: '地区特征', prop: 'dqtz', minWidth: '75' },\n            { label: '线段类型', prop: 'xdlx', minWidth: '80' },\n            { label: '起点', prop: 'qd', minWidth: '60' },\n            { label: '终点', prop: 'zd', minWidth: '60' },\n            { label: '投运日期', prop: 'tyrq', minWidth: '87' },\n            { label: '线段长度(km)', prop: 'xdcd', minWidth: '70' },\n            { label: '是否联络线路', prop: 'sfllxl', minWidth: '60' },\n            { label: '是否为出线开关', prop: 'sfwcxkg', minWidth: '60' },\n            {\n              prop: 'operation',\n              label: '操作',\n              minWidth: '100px',\n              style: { display: 'block' },\n              //操作列固定再右侧\n              fixed: 'right',\n              operation: [\n                { name: '修改', clickFun: this.getUpdate },\n                { name: '详情', clickFun: this.getDetails }\n                /*{ name: '附件查看', clickFun: this.getFjInfoList },*/\n              ]\n            }\n          ],\n          option: { checkBox: true, serialNumber: true }\n        },\n        params: {\n          lineName: '',\n          xlbm: '',\n          zcsx:'',\n          dqtz:'',\n        },\n        selectRows: [],\n        rules:{\n          lineName:[{required:true,message:'请选择所属线路',trigger:'change'}],\n          xlbm:[{required:true,message:'请选择所属线路运行编号',trigger:'blur'}],\n          zgxxdmc:[{required:true,message:'请输入线段名称',trigger:'blur'}],\n          sfwzgxxd:[{required:true,message:'请选择是否主干线段',trigger:'change'}],\n          xdbm:[{required:true,message:'请输入线段编码',trigger:'blur'}],\n          sszgxmc:[{required:true,message:'请输入所属主干线段名称',trigger:'blur'}],\n          zcsx:[{required:true,message:'请输入资产属性',trigger:'blur'}],\n          qd:[{required:true,message:'请输入线段起点',trigger:'blur'}],\n          zd:[{required:true,message:'请输入线段终点',trigger:'blur'}],\n        },\n      }\n    },\n    created() {\n      //列表查询\n      this.getData()\n      //this.getTableList(this.queryParams);\n      this.treeList();\n    },\n    methods: {\n      //是否为支线\n      changeClickZx(val) {\n        if (val == '是') {\n          this.isShowZx = true\n        } else {\n          this.isShowZx = false\n        }\n      },\n      //是否为联络线路\n      changeClickXl(val) {\n        if (val == '是') {\n          this.isShowXl = false\n        } else {\n          this.isShowXl = true\n        }\n      },\n      //是否为出线开关\n      changeClickKg(val) {\n        if (val == '是') {\n          this.isShowKg = false\n        } else {\n          this.isShowKg = true\n        }\n      },\n      //列表查询\n      async getData(params) {\n        try {\n          const param = {...this.params, ...params}\n          const { data, code } = await getLists(param)\n          if (code === '0000') {\n            this.tableAndPageInfo.tableData = data.records\n            this.tableAndPageInfo.pager.total = data.total\n          }\n        } catch (e) {\n        }\n      },\n\n      //查询输电线路信息P\n      getTableList: function(params) {\n        getList(params).then(res => {\n          this.ssxlmcList = res.data.records\n          /*this.tableAndPageInfo.tableData = res.data.records*/\n        })\n      },\n      //提交查询到的输电线路信息\n      commit() {\n        this.isShowXlmc = false\n      },\n\n      //重置按钮\n      getReset() {\n        this.getData();\n      },\n      //选中行\n      handleSelectionChange() {\n\n      },\n      //线路名称弹框\n      getXlmc() {\n        this.isShowXlmc = true\n      },\n      //新增按钮\n      getInster() {\n        if(this.form.lineName!=''){\n          this.title = '新增输电线段'\n          this.isDisabled = false\n          //this.form = {}\n          /*this.ssxlmcList= ''*/\n          this.isShowDetails = true\n        }else{\n          this.$message.warning(\"请先选择线路后新增线段\");\n          return false;\n        }\n\n      },\n      //修改按钮\n      getUpdate(row) {\n        this.title = '修改输电线段'\n        this.isDisabled = false\n        this.form = { ...row }\n        this.isShowDetails = true\n        //获取是或否进行判断，是否可以编辑\n        if (row.sfwzgxxd == '是') {\n          this.isShowZx = true\n        } else {\n          this.isShowZx = false\n        }\n\n        if (row.sfllxl == '是') {\n          this.isShowXl = false\n        } else {\n          this.isShowXl = true\n        }\n\n        if (row.sfwcxkg == '否') {\n          this.isShowKg = true\n        } else {\n          this.isShowKg = false\n        }\n      },\n      //详情按钮\n      getDetails(row) {\n        this.title = '输电线段详情'\n        this.form = { ...row }\n        this.isDisabled = true\n        this.isShowDetails = true\n        this.isShowZx = true\n        this.isShowXl = true\n        this.isShowKg = true\n      },\n      /*//附件查看\n      getFjInfoList() {\n        this.title = '附件查看'\n        this.isDisabled = true\n        this.isShowDetails = true\n        this.form={...row}\n      },*/\n      //保存按钮\n      async saveRow() {\n        try {\n            this.$refs['form'].validate((valid) => {\n              if (valid) {\n                  let { code } =  saveOrUpdate(this.form)\n                  if (code === '0000') {\n                    this.getData()\n                    this.isShowDetails = false\n                    this.$message.success('操作成功')\n                  }\n              } else {\n                setTimeout(() => {\n                  var isError = document.getElementsByClassName(\"is-error\");\n                  if (isError[0].querySelector('input')) {\n                    isError[0].querySelector('input').focus();\n                  } else if (isError[0].querySelector('textarea')) {\n                    isError[0].querySelector('textarea').focus();\n                  }\n                }, 1)\n                return false;\n              }\n            });\n        } catch (e) {\n            console.log(e)\n        }\n\n      },\n      //删除按钮\n      async deleteRow() {\n        if (this.selectRows.length < 1) {\n          this.$message.warning('请选择正确的数据！！！')\n          return\n        }\n        let ids = this.selectRows.map(item => {\n          return item.id\n        })\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove(ids).then(({ code }) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getData()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n        await this.getData()\n      },\n      treeList(){\n          getTreeList().then(res => {\n            this.treeData = res.data\n          });\n      },\n      //关闭弹窗\n      close() {\n        this.isShowDetails = false\n      },\n      resetForm(){\n        this.form = {}\n        this.$nextTick(function () {\n          this.$refs['form'].clearValidate();\n        });\n        this.isShowDetails = false;\n\n      },\n      //筛选条件\n      selectChange(rows) {\n        this.selectRows = rows\n      },\n      //左侧树点击事件\n      handleNodeClick(data){\n        if(data.identifier=='3'){\n            this.form.lineName=data.label;\n            this.form.xlbm=data.id;\n            this.filterInfo.data.xlbm=data.id;\n          this.getData();\n        }else if(data.identifier=='1'){\n          this.filterInfo.data.xlbm='';\n          this.getData();\n        }\n\n      },\n      query(){\n       this.filterInfo.data={...this.filterInfo.data}\n      },\n    },\n\n\n  }\n</script>\n\n<style>\n  /*控制input输入框边框是否显示*/\n  .elInput >>> .el-input__inner {\n    border: 0;\n  }\n\n  .box-card {\n    margin-bottom: 2vh !important;\n  }\n\n  .red .el-form-item__label {\n    color: red;\n  }\n</style>\n\n"]}]}