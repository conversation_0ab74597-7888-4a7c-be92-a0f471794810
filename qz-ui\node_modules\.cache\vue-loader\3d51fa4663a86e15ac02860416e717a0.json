{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\lpbzk\\dqgzzqpz.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\lpbzk\\dqgzzqpz.vue", "mtime": 1706897322614}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgZ2V0TGlzdCwKICByZW1vdmUsCiAgc2F2ZU9yVXBkYXRlLAogIGdldEJkelNlbGVjdExpc3QsCiAgZW5kSm9iLAogIGNoYWNrSm9iLAogIHN0YXJ0Sm9iCn0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYnpnbC9scGJ6ay9kcWd6enFweiI7CmltcG9ydCB7IGdldEZnc09wdGlvbnMgfSBmcm9tICJAL2FwaS95eGdsL2JkeXhnbC96YmdsIjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJkcWd6enFweiIsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGxvYWRpbmc6IHRydWUsCiAgICAgIGJkekxpc3Q6IFtdLAogICAgICAvL+e7hOe7h+e7k+aehOS4i+aLieaVsOaNrgogICAgICBvcmdhbml6YXRpb25TZWxlY3RlZExpc3Q6IFtdLAogICAgICBydWxlczogewogICAgICAgIGJkejogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlj5jnlLXnq5nkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9XSwKICAgICAgICBnemx4OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5bel5L2c57G75Z6L5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfQogICAgICAgIF0sCiAgICAgICAgc3NnczogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWIhuWFrOWPuOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0KICAgICAgICBdLAogICAgICAgIHpxOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWRqOacn+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9XQogICAgICB9LAogICAgICAvL2Zvcm3ooajljZUKICAgICAgZm9ybToge30sCiAgICAgIC8v5piv5ZCm5pi+56S65by55qGGCiAgICAgIGlzU2hvd0RldGFpbHM6IGZhbHNlLAogICAgICAvL+aYr+WQpuemgeeUqAogICAgICBpc0Rpc2FibGVkOiBmYWxzZSwKICAgICAgLy/ooajljZXmlbDmja4KICAgICAgZGF0YVRhYmxlOiBbXSwKICAgICAgLy/lt6XkvZznsbvlnosKICAgICAgZ3pseExpc3Q6IFsKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogMSwKICAgICAgICAgIGxhYmVsOiAi5a6J5YWo5bel5Zmo5YW35qOA5p+lIgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdmFsdWU6IDIsCiAgICAgICAgICBsYWJlbDogIuS7quWZqOS7quihqOajgOafpSIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAzLAogICAgICAgICAgbGFiZWw6ICLpmLLlsI/liqjnianorr7mlr3mo4Dmn6UiCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogNCwKICAgICAgICAgIGxhYmVsOiAi5Lqk5rWB55S15rqQ5YiH5o2i5qOA5p+lIgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdmFsdWU6IDUsCiAgICAgICAgICBsYWJlbDogIuaOkuawtOOAgemAmumjjuajgOafpSIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiA2LAogICAgICAgICAgbGFiZWw6ICLorr7lpIfmoIfor4bmo4Dmn6UiCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogNywKICAgICAgICAgIGxhYmVsOiAi54Wn5piO57O757uf5qOA5p+lIgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdmFsdWU6IDgsCiAgICAgICAgICBsYWJlbDogIlNGNuawp+mHj+WRiuitpuS7quajgOafpSIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiA5LAogICAgICAgICAgbGFiZWw6ICLliqDng63lmajlj4rnhafmmI7mo4Dmn6UiCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogMTAsCiAgICAgICAgICBsYWJlbDogIuWkh+eUqOermeeUqOWPmOWQr+WKqOivlemqjCIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAxMSwKICAgICAgICAgIGxhYmVsOiAiVVBT57O757uf6K+V6aqMIgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdmFsdWU6IDEyLAogICAgICAgICAgbGFiZWw6ICLnlLXmupDnrrHmo4Dmn6UiCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogMTMsCiAgICAgICAgICBsYWJlbDogIuaOpeWcsOagh+W/l+ajgOafpSIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAxNCwKICAgICAgICAgIGxhYmVsOiAi6Ziy6K+v6KOF572u5qCh6aqMIgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdmFsdWU6IDE1LAogICAgICAgICAgbGFiZWw6ICLnlLXnvIbmsp/mo4Dmn6UiCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogMTYsCiAgICAgICAgICBsYWJlbDogIumYsuaxm+iuvuaWveajgOafpSIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHZhbHVlOiAxNywKICAgICAgICAgIGxhYmVsOiAi5L+d5rip6K6+5pa95qOA5p+lIgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdmFsdWU6IDE4LAogICAgICAgICAgbGFiZWw6ICLkuovmlYXmsrnmsaDmo4Dmn6UiCiAgICAgICAgfQogICAgICBdLAogICAgICAvL+agh+mimAogICAgICB0aXRsZTogIiIsCiAgICAgIGZpbHRlckluZm86IHsKICAgICAgICBkYXRhOiB7CiAgICAgICAgICBzYmZsQXJyOiBbXSwKICAgICAgICAgIHNiZmw6ICIiLAogICAgICAgICAganhmbEFycjogW10sCiAgICAgICAgICBqeHpxOiAiIiwKICAgICAgICAgIHpxZHc6ICIiCiAgICAgICAgfSwKICAgICAgICBmaWVsZExpc3Q6IFsKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLliIblhazlj7giLAogICAgICAgICAgICB2YWx1ZTogInNzZ3MiLAogICAgICAgICAgICB0eXBlOiAic2VsZWN0IiwKICAgICAgICAgICAgb3B0aW9uczogW10sCiAgICAgICAgICAgIGNsZWFyYWJsZTogdHJ1ZQogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLlj5jnlLXnq5kiLAogICAgICAgICAgICB2YWx1ZTogImJkeiIsCiAgICAgICAgICAgIHR5cGU6ICJzZWxlY3QiLAogICAgICAgICAgICBvcHRpb25zOiBbXSwKICAgICAgICAgICAgY2xlYXJhYmxlOiB0cnVlCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIuW3peS9nOexu+WeiyIsCiAgICAgICAgICAgIHZhbHVlOiAiZ3pseCIsCiAgICAgICAgICAgIHR5cGU6ICJzZWxlY3QiLAogICAgICAgICAgICBvcHRpb25zOiBbCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgdmFsdWU6IDEsCiAgICAgICAgICAgICAgICBsYWJlbDogIuWuieWFqOW3peWZqOWFt+ajgOafpSIKICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIHZhbHVlOiAyLAogICAgICAgICAgICAgICAgbGFiZWw6ICLku6rlmajku6rooajmo4Dmn6UiCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICB2YWx1ZTogMywKICAgICAgICAgICAgICAgIGxhYmVsOiAi6Ziy5bCP5Yqo54mp6K6+5pa95qOA5p+lIgogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgdmFsdWU6IDQsCiAgICAgICAgICAgICAgICBsYWJlbDogIuS6pOa1geeUtea6kOWIh+aNouajgOafpSIKICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIHZhbHVlOiA1LAogICAgICAgICAgICAgICAgbGFiZWw6ICLmjpLmsLTjgIHpgJrpo47mo4Dmn6UiCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICB2YWx1ZTogNiwKICAgICAgICAgICAgICAgIGxhYmVsOiAi6K6+5aSH5qCH6K+G5qOA5p+lIgogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgdmFsdWU6IDcsCiAgICAgICAgICAgICAgICBsYWJlbDogIueFp+aYjuezu+e7n+ajgOafpSIKICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIHZhbHVlOiA4LAogICAgICAgICAgICAgICAgbGFiZWw6ICJTRjbmsKfph4/lkYrorabku6rmo4Dmn6UiCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICB2YWx1ZTogOSwKICAgICAgICAgICAgICAgIGxhYmVsOiAi5Yqg54Ot5Zmo5Y+K54Wn5piO5qOA5p+lIgogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgdmFsdWU6IDEwLAogICAgICAgICAgICAgICAgbGFiZWw6ICLlpIfnlKjnq5nnlKjlj5jlkK/liqjor5XpqowiCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICB2YWx1ZTogMTEsCiAgICAgICAgICAgICAgICBsYWJlbDogIlVQU+ezu+e7n+ivlemqjCIKICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIHZhbHVlOiAxMiwKICAgICAgICAgICAgICAgIGxhYmVsOiAi55S15rqQ566x5qOA5p+lIgogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgdmFsdWU6IDEzLAogICAgICAgICAgICAgICAgbGFiZWw6ICLmjqXlnLDmoIflv5fmo4Dmn6UiCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICB2YWx1ZTogMTQsCiAgICAgICAgICAgICAgICBsYWJlbDogIumYsuivr+ijhee9ruagoemqjCIKICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIHZhbHVlOiAxNSwKICAgICAgICAgICAgICAgIGxhYmVsOiAi55S157yG5rKf5qOA5p+lIgogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgdmFsdWU6IDE2LAogICAgICAgICAgICAgICAgbGFiZWw6ICLpmLLmsZvorr7mlr3mo4Dmn6UiCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICB2YWx1ZTogMTcsCiAgICAgICAgICAgICAgICBsYWJlbDogIuS/nea4qeiuvuaWveajgOafpSIKICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIHZhbHVlOiAxOCwKICAgICAgICAgICAgICAgIGxhYmVsOiAi5LqL5pWF5rK55rGg5qOA5p+lIgogICAgICAgICAgICAgIH0KICAgICAgICAgICAgXSwKICAgICAgICAgICAgY2xlYXJhYmxlOiB0cnVlCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIua2iOaBr+aPkOmGkueKtuaAgSIsCiAgICAgICAgICAgIHZhbHVlOiAidGFza1N0YXRlIiwKICAgICAgICAgICAgdHlwZTogInNlbGVjdCIsCiAgICAgICAgICAgIG9wdGlvbnM6IFsKICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICB2YWx1ZTogMCwKICAgICAgICAgICAgICAgIGxhYmVsOiAi5YWz6ZetIgogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgdmFsdWU6IDEsCiAgICAgICAgICAgICAgICBsYWJlbDogIuW8gOWQryIKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIF0sCiAgICAgICAgICAgIGNsZWFyYWJsZTogdHJ1ZQogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLlkajmnJ8o5pyIKSIsCiAgICAgICAgICAgIHZhbHVlOiAienEiLAogICAgICAgICAgICB0eXBlOiAiaW5wdXQiCiAgICAgICAgICB9CiAgICAgICAgXQogICAgICB9LAogICAgICB0YWJsZUFuZFBhZ2VJbmZvOiB7CiAgICAgICAgcGFnZXI6IHsKICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgIHNpemVzOiBbMTAsIDIwLCA1MCwgMTIwXQogICAgICAgIH0sCiAgICAgICAgdGFibGVEYXRhOiBbXSwKICAgICAgICB0YWJsZUhlYWRlcjogWwogICAgICAgICAgeyBsYWJlbDogIuWIhuWFrOWPuCIsIHByb3A6ICJmZ3NtYyIsIG1pbldpZHRoOiAiMTAwIiB9LAogICAgICAgICAgeyBsYWJlbDogIuWPmOeUteermSIsIHByb3A6ICJiZHptYyIsIG1pbldpZHRoOiAiMTAwIiB9LAogICAgICAgICAgeyBsYWJlbDogIuW3peS9nOexu+WeiyIsIHByb3A6ICJnemx4bWMiLCBtaW5XaWR0aDogIjEwMCIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLlkajmnJ8o5pyIKSIsIHByb3A6ICJ6cSIsIG1pbldpZHRoOiAiNjAiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5raI5oGv5o+Q6YaS54q25oCBIiwgcHJvcDogInN0YXR1cyIsIG1pbldpZHRoOiAiODAiIH0KICAgICAgICBdLAogICAgICAgIG9wdGlvbjogeyBjaGVja0JveDogdHJ1ZSwgc2VyaWFsTnVtYmVyOiB0cnVlIH0KICAgICAgfSwKICAgICAgcGFyYW1zOiB7fSwKICAgICAgc2VsZWN0Um93czogW10sCiAgICAgIHNob3dEZXZpY2VUcmVlOiBmYWxzZSwKICAgICAgaXNGaWx0ZXI6IGZhbHNlCiAgICB9OwogIH0sCiAgbW91bnRlZCgpIHsKICAgIHRoaXMuZ2V0RmdzT3B0aW9ucygpOwogICAgdGhpcy5nZXREYXRhKCk7CiAgfSwKICB3YXRjaDogewogICAgaXNTaG93RGV0YWlscyh2YWwpIHsKICAgICAgaWYgKHZhbCkgewogICAgICAgIGNvbnN0IGVsID0gdGhpcy4kZWwucXVlcnlTZWxlY3RvcigiLmVsLWRpYWxvZyIpOwogICAgICAgIGVsLnN0eWxlLmxlZnQgPSAwOwogICAgICAgIGVsLnN0eWxlLnRvcCA9IDA7CiAgICAgIH0KICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKgogICAgICog6I635Y+W5YiG5YWs5Y+45LiL5ouJ5pWw5o2uCiAgICAgKi8KICAgIGdldEZnc09wdGlvbnMoKSB7CiAgICAgIGdldEZnc09wdGlvbnMoe30pLnRoZW4ocmVzID0+IHsKICAgICAgICByZXMuZGF0YS5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgaXRlbS52YWx1ZSA9IGl0ZW0udmFsdWUudG9TdHJpbmcoKTsKICAgICAgICB9KTsKICAgICAgICB0aGlzLm9yZ2FuaXphdGlvblNlbGVjdGVkTGlzdCA9IHJlcy5kYXRhOwogICAgICAgIHRoaXMuZmlsdGVySW5mby5maWVsZExpc3QubWFwKGl0ZW0gPT4gewogICAgICAgICAgaWYgKGl0ZW0udmFsdWUgPT0gInNzZ3MiKSB7CiAgICAgICAgICAgIHJldHVybiAoaXRlbS5vcHRpb25zID0gdGhpcy5vcmdhbml6YXRpb25TZWxlY3RlZExpc3QpOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICAvL+WQr+WKqOS7u+WKoQogICAgc3RhcnRKb2Ioam9iSWQpIHsKICAgICAgc3RhcnRKb2Ioam9iSWQpLnRoZW4ocmVzID0+IHsKICAgICAgICBpZiAocmVzLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLlkK/liqjmiJDlip8iKTsKICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgbWVzc2FnZTogIuWQr+WKqOWksei0pSEiCiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIGNoYWNrSm9iKGpvYklkKSB7CiAgICAgIGNoYWNrSm9iKGpvYklkKS50aGVuKHJlcyA9PiB7CiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5ZCv5Yqo5oiQ5YqfIik7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAiZXJyb3IiLAogICAgICAgICAgICBtZXNzYWdlOiAi5ZCv5Yqo5aSx6LSlISIKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy/lgZzmraLku7vliqEKICAgIHN0b3BKb2Ioam9iSWQpIHsKICAgICAgZW5kSm9iKGpvYklkKS50aGVuKHJlcyA9PiB7CiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5YGc5q2i5oiQ5YqfIik7CiAgICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICJlcnJvciIsCiAgICAgICAgICAgIG1lc3NhZ2U6ICLlgZzmraLlpLHotKUhIgogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvL+S4i+aLieahhmNoYW5nZeS6i+S7tgogICAgaGFuZGxlRXZlbnQodmFsLCBldmVudFZhbHVlKSB7CiAgICAgIGlmICh2YWwubGFiZWwgPT09ICJzc2dzIikgewogICAgICAgIGdldEJkelNlbGVjdExpc3QoeyBzc2R3Ym06IHZhbC52YWx1ZS50b1N0cmluZygpIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgIHRoaXMuZmlsdGVySW5mby5maWVsZExpc3QubWFwKGl0ZW0gPT4gewogICAgICAgICAgICBpZiAoaXRlbS52YWx1ZSA9PSAiYmR6IikgewogICAgICAgICAgICAgIHRoaXMuJHNldChldmVudFZhbHVlLCAiYmR6IiwgIiIpOwogICAgICAgICAgICAgIHJldHVybiAoaXRlbS5vcHRpb25zID0gcmVzLmRhdGEpOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9KTsKICAgICAgfQogICAgfSwKICAgIC8v5YiX6KGo5p+l6K+iCiAgICBhc3luYyBnZXREYXRhKHBhcmFtcykgewogICAgICB0cnkgewogICAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgICAgdGhpcy5wYXJhbXMgPSB7IC4uLnRoaXMucGFyYW1zLCAuLi5wYXJhbXMgfTsKICAgICAgICBjb25zdCBwYXJhbSA9IHRoaXMucGFyYW1zOwogICAgICAgIGNvbnN0IHsgZGF0YSwgY29kZSB9ID0gYXdhaXQgZ2V0TGlzdChwYXJhbSk7CiAgICAgICAgaWYgKGNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgZm9yIChsZXQgaSBvZiBkYXRhLnJlY29yZHMpIHsKICAgICAgICAgICAgaS5zdGF0dXMgPSBpLnRhc2tTdGF0ZSA9PT0gMSA/ICLlvIDlkK8iIDogIuWFs+mXrSI7CiAgICAgICAgICAgIGkuZmdzbWMgPSB0aGlzLmZvcm1hdFNzZ3MoaS5zc2dzKTsKICAgICAgICAgICAgaS5nemx4bWMgPSB0aGlzLmZvcm1hdEd6bHgoaS5nemx4KTsKICAgICAgICAgIH0KICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby50YWJsZURhdGEgPSBkYXRhLnJlY29yZHM7CiAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIudG90YWwgPSBkYXRhLnRvdGFsOwogICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgY29uc29sZS5sb2coZSk7CiAgICAgIH0KICAgIH0sCiAgICAvL+mHjee9ruaMiemSrgogICAgZ2V0UmVzZXQoKSB7CiAgICAgIHRoaXMucGFyYW1zID0ge307CiAgICB9LAogICAgLy/pgInkuK3ooYwKICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZSgpIHt9LAogICAgLy/mlrDlop4KICAgIGdldEluc2VydCgpIHsKICAgICAgdGhpcy50aXRsZSA9ICLlrprmnJ/lt6XkvZzphY3nva7mlrDlop4iOwogICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZTsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gdHJ1ZTsKICAgICAgdGhpcy5mb3JtID0ge307CiAgICB9LAogICAgLy/nvJbovpHmjInpkq4KICAgIHVwZGF0ZVJvdyhyb3cpIHsKICAgICAgdGhpcy50aXRsZSA9ICLlrprmnJ/lt6XkvZzphY3nva7kv67mlLkiOwogICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZTsKICAgICAgdGhpcy5mb3JtID0geyAuLi5yb3cgfTsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gdHJ1ZTsKICAgICAgdGhpcy5nZXRCZHpMaXN0KCk7CiAgICB9LAogICAgLy/or6bmg4XmjInpkq4KICAgIGdldEluZm8ocm93KSB7CiAgICAgIHRoaXMudGl0bGUgPSAi5a6a5pyf5bel5L2c6YWN572u6K+m5oOFIjsKICAgICAgdGhpcy5mb3JtID0geyAuLi5yb3cgfTsKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gdHJ1ZTsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gdHJ1ZTsKICAgICAgdGhpcy5nZXRCZHpMaXN0KCk7CiAgICB9LAoKICAgIGZvcm1hdEd6bHgoZ3pseCkgewogICAgICBpZiAoZ3pseCkgewogICAgICAgIHJldHVybiB0aGlzLmd6bHhMaXN0LmZpbHRlcihnID0+IGcudmFsdWUgPT0gZ3pseClbMF0ubGFiZWw7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuICIiOwogICAgICB9CiAgICB9LAogICAgZm9ybWF0U3Nncyhzc2dzKSB7CiAgICAgIGxldCBwYWdlT3JnYW5pemF0aW9uU2VsZWN0ZWRMaXN0ID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLm9yZ2FuaXphdGlvblNlbGVjdGVkTGlzdCkpCiAgICAgIHBhZ2VPcmdhbml6YXRpb25TZWxlY3RlZExpc3QucHVzaCh7bGFiZWw6ICfmuK/kuJzlj5jnlLXliIblhazlj7gnLCB2YWx1ZTogJzMwMDInfSkKICAgICAgcGFnZU9yZ2FuaXphdGlvblNlbGVjdGVkTGlzdC5wdXNoKHtsYWJlbDogJ+a4r+S4reWPmOeUteWIhuWFrOWPuCcsIHZhbHVlOiAnMzAwMyd9KQogICAgICBpZiAoc3NncykgewogICAgICAgIHJldHVybiBwYWdlT3JnYW5pemF0aW9uU2VsZWN0ZWRMaXN0LmZpbHRlcihnID0+IGcudmFsdWUgPT09IHNzZ3MpWzBdCiAgICAgICAgICAubGFiZWw7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuICIiOwogICAgICB9CiAgICB9LAogICAgYXN5bmMgc2F2ZVJvdygpIHsKICAgICAgdHJ5IHsKICAgICAgICBsZXQgeyBjb2RlIH0gPSBhd2FpdCBzYXZlT3JVcGRhdGUodGhpcy5mb3JtKTsKICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKnyIpOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgIGNvbnNvbGUubG9nKGUpOwogICAgICB9CiAgICAgIC8v6YeN572ucGFnZemhteS7jjHlvIDlp4sKICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnBhZ2VSZXNpemUgPSAiWSI7CiAgICAgIGF3YWl0IHRoaXMuZ2V0RGF0YSgpOwogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSBmYWxzZTsKICAgIH0sCiAgICAvL+WIoOmZpOaMiemSrgogICAgYXN5bmMgZGVsZXRlUm93KG9iaklkKSB7CiAgICAgIHRoaXMuJGNvbmZpcm0oIuatpOaTjeS9nOWwhuawuOS5heWIoOmZpOivpeaVsOaNriwg5piv5ZCm57un57utPyIsICLmj5DnpLoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KQogICAgICAgIC50aGVuKCgpID0+IHsKICAgICAgICAgIHJlbW92ZShvYmpJZCkudGhlbigoeyBjb2RlIH0pID0+IHsKICAgICAgICAgICAgaWYgKGNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLAogICAgICAgICAgICAgICAgbWVzc2FnZTogIuWIoOmZpOaIkOWKnyEiCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICB0eXBlOiAiZXJyb3IiLAogICAgICAgICAgICAgICAgbWVzc2FnZTogIuWIoOmZpOWksei0pSEiCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0pCiAgICAgICAgLmNhdGNoKCgpID0+IHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAiaW5mbyIsCiAgICAgICAgICAgIG1lc3NhZ2U6ICLlt7Llj5bmtojliKDpmaQiCiAgICAgICAgICB9KTsKICAgICAgICB9KTsKICAgICAgYXdhaXQgdGhpcy5nZXREYXRhKCk7CiAgICB9LAogICAgLy/lhbPpl63lvLnnqpcKICAgIGNsb3NlKCkgewogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSBmYWxzZTsKICAgIH0sCiAgICBzZWxlY3RDaGFuZ2Uocm93cykgewogICAgICB0aGlzLnNlbGVjdFJvd3MgPSByb3dzOwogICAgfSwKCiAgICBpbnB1dEZvY3VzRXZlbnQodmFsKSB7CiAgICAgIGlmICh2YWwudGFyZ2V0Lm5hbWUgPT09ICJzYmZsIikgewogICAgICAgIGFsdGVyKCJ0ZXN0Iik7CiAgICAgICAgdGhpcy5zaG93RGV2aWNlVHJlZSA9IHRydWU7CiAgICAgICAgdGhpcy5pc0ZpbHRlciA9IHRydWU7CiAgICAgIH0KICAgIH0sCiAgICAvL+a4heepuuihqOWNleaVsOaNrgogICAgaGFuZGxlQ2xvc2UoKSB7CiAgICAgIHRoaXMuZm9ybSA9IHt9OwogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgdGhpcy5mb3JtID0gdGhpcy4kb3B0aW9ucy5kYXRhKCkuZm9ybTsKICAgICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgICB9KTsKICAgIH0sCiAgICAvL+iOt+WPluWPmOeUteermeS4i+aLieahhgogICAgZ2V0QmR6TGlzdCgpIHsKICAgICAgaWYgKHRoaXMudGl0bGUgPT09ICLlrprmnJ/lt6XkvZzphY3nva7mlrDlop4iKSB7CiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgImJkeiIsICIiKTsKICAgICAgfQogICAgICB0aGlzLmJkekxpc3QgPSBbXTsKICAgICAgZ2V0QmR6U2VsZWN0TGlzdCh7IHNzZHdibTogdGhpcy5mb3JtLnNzZ3MgfSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuYmR6TGlzdCA9IHJlcy5kYXRhOwogICAgICB9KTsKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["dqgzzqpz.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "dqgzzqpz.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/lpbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      @handleReset=\"getReset\"\n      @onfocusEvent=\"inputFocusEvent\"\n      @handleEvent=\"handleEvent\"\n      :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n    />\n    <div>\n      <el-white class=\"button-group\">\n        <div class=\"button_btn\">\n          <el-button\n            type=\"primary\"\n            v-hasPermi=\"['bzdqgzpz:button:add']\"\n            icon=\"el-icon-plus\"\n            @click=\"getInsert\"\n            >新增</el-button\n          >\n        </div>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"selectChange\"\n          height=\"65vh\"\n          v-loading=\"loading\"\n        >\n          <el-table-column\n            slot=\"table_eight\"\n            align=\"center\"\n            fixed=\"right\"\n            style=\"display: block\"\n            label=\"操作\"\n            width=\"200\"\n            :resizable=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                v-if=\"scope.row.taskState === 0\"\n                @click=\"updateRow(scope.row)\"\n                v-hasPermi=\"['bzdqgzpz:button:update']\"\n                type=\"text\"\n                size=\"small\"\n                title=\"修改\"\n                class=\"el-icon-edit\"\n              ></el-button>\n              <el-button\n                @click=\"getInfo(scope.row)\"\n                type=\"text\"\n                size=\"small\"\n                title=\"详情\"\n                class=\"el-icon-view\"\n              ></el-button>\n              <el-button\n                v-if=\"scope.row.taskState === 0\"\n                @click=\"deleteRow(scope.row.objId)\"\n                type=\"danger\"\n                size=\"small\"\n                title=\"删除\"\n                class=\"el-icon-delete\"\n              ></el-button>\n              <el-button\n                v-if=\"scope.row.taskState === 0\"\n                size=\"small\"\n                type=\"success\"\n                title=\"开启消息提醒\"\n                icon=\"el-icon-caret-right\"\n                @click=\"startJob(scope.row.objId)\"\n              ></el-button>\n              <el-button\n                v-if=\"scope.row.taskState === 1\"\n                size=\"small\"\n                type=\"danger\"\n                title=\"关闭消息提醒\"\n                icon=\"el-icon-switch-button\"\n                @click=\"stopJob(scope.row.objId)\"\n              ></el-button>\n              <el-button\n                v-if=\"'admin' === $store.getters.name\"\n                @click=\"chackJob(scope.row.objId)\"\n                type=\"success\"\n                size=\"small\"\n                title=\"启动一次\"\n                class=\"el-icon-caret-right\"\n              ></el-button>\n            </template>\n          </el-table-column>\n        </comp-table>\n      </el-white>\n    </div>\n    <!-- 详情/新增/修改 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"40%\"\n      @close=\"handleClose\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"分公司:\" prop=\"ssgs\">\n              <el-select\n                placeholder=\"请选择分公司\"\n                @change=\"getBdzList\"\n                clearable\n                v-model=\"form.ssgs\"\n                :disabled=\"isDisabled\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in organizationSelectedList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"变电站:\" prop=\"bdz\">\n              <el-select\n                placeholder=\"请选择变电站\"\n                clearable\n                v-model=\"form.bdz\"\n                :disabled=\"isDisabled\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in bdzList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"工作类型：\" prop=\"gzlx\">\n              <el-select\n                placeholder=\"请选择工作类型\"\n                clearable\n                v-model=\"form.gzlx\"\n                :disabled=\"isDisabled\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in gzlxList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"周期(月)：\" clearable prop=\"zq\">\n              <el-input-number\n                :min=\"0\"\n                placeholder=\"请输入周期\"\n                v-model=\"form.zq\"\n                :disabled=\"isDisabled\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button\n          v-if=\"title === '定期工作配置新增' || title === '定期工作配置修改'\"\n          type=\"primary\"\n          @click=\"saveRow\"\n          >确 认</el-button\n        >\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  getList,\n  remove,\n  saveOrUpdate,\n  getBdzSelectList,\n  endJob,\n  chackJob,\n  startJob\n} from \"@/api/dagangOilfield/bzgl/lpbzk/dqgzzqpz\";\nimport { getFgsOptions } from \"@/api/yxgl/bdyxgl/zbgl\";\nexport default {\n  name: \"dqgzzqpz\",\n  data() {\n    return {\n      loading: true,\n      bdzList: [],\n      //组织结构下拉数据\n      organizationSelectedList: [],\n      rules: {\n        bdz: [{ required: true, message: \"变电站不能为空\", trigger: \"change\" }],\n        gzlx: [\n          { required: true, message: \"工作类型不能为空\", trigger: \"change\" }\n        ],\n        ssgs: [\n          { required: true, message: \"分公司不能为空\", trigger: \"change\" }\n        ],\n        zq: [{ required: true, message: \"周期不能为空\", trigger: \"blur\" }]\n      },\n      //form表单\n      form: {},\n      //是否显示弹框\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //表单数据\n      dataTable: [],\n      //工作类型\n      gzlxList: [\n        {\n          value: 1,\n          label: \"安全工器具检查\"\n        },\n        {\n          value: 2,\n          label: \"仪器仪表检查\"\n        },\n        {\n          value: 3,\n          label: \"防小动物设施检查\"\n        },\n        {\n          value: 4,\n          label: \"交流电源切换检查\"\n        },\n        {\n          value: 5,\n          label: \"排水、通风检查\"\n        },\n        {\n          value: 6,\n          label: \"设备标识检查\"\n        },\n        {\n          value: 7,\n          label: \"照明系统检查\"\n        },\n        {\n          value: 8,\n          label: \"SF6氧量告警仪检查\"\n        },\n        {\n          value: 9,\n          label: \"加热器及照明检查\"\n        },\n        {\n          value: 10,\n          label: \"备用站用变启动试验\"\n        },\n        {\n          value: 11,\n          label: \"UPS系统试验\"\n        },\n        {\n          value: 12,\n          label: \"电源箱检查\"\n        },\n        {\n          value: 13,\n          label: \"接地标志检查\"\n        },\n        {\n          value: 14,\n          label: \"防误装置校验\"\n        },\n        {\n          value: 15,\n          label: \"电缆沟检查\"\n        },\n        {\n          value: 16,\n          label: \"防汛设施检查\"\n        },\n        {\n          value: 17,\n          label: \"保温设施检查\"\n        },\n        {\n          value: 18,\n          label: \"事故油池检查\"\n        }\n      ],\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          sbflArr: [],\n          sbfl: \"\",\n          jxflArr: [],\n          jxzq: \"\",\n          zqdw: \"\"\n        },\n        fieldList: [\n          {\n            label: \"分公司\",\n            value: \"ssgs\",\n            type: \"select\",\n            options: [],\n            clearable: true\n          },\n          {\n            label: \"变电站\",\n            value: \"bdz\",\n            type: \"select\",\n            options: [],\n            clearable: true\n          },\n          {\n            label: \"工作类型\",\n            value: \"gzlx\",\n            type: \"select\",\n            options: [\n              {\n                value: 1,\n                label: \"安全工器具检查\"\n              },\n              {\n                value: 2,\n                label: \"仪器仪表检查\"\n              },\n              {\n                value: 3,\n                label: \"防小动物设施检查\"\n              },\n              {\n                value: 4,\n                label: \"交流电源切换检查\"\n              },\n              {\n                value: 5,\n                label: \"排水、通风检查\"\n              },\n              {\n                value: 6,\n                label: \"设备标识检查\"\n              },\n              {\n                value: 7,\n                label: \"照明系统检查\"\n              },\n              {\n                value: 8,\n                label: \"SF6氧量告警仪检查\"\n              },\n              {\n                value: 9,\n                label: \"加热器及照明检查\"\n              },\n              {\n                value: 10,\n                label: \"备用站用变启动试验\"\n              },\n              {\n                value: 11,\n                label: \"UPS系统试验\"\n              },\n              {\n                value: 12,\n                label: \"电源箱检查\"\n              },\n              {\n                value: 13,\n                label: \"接地标志检查\"\n              },\n              {\n                value: 14,\n                label: \"防误装置校验\"\n              },\n              {\n                value: 15,\n                label: \"电缆沟检查\"\n              },\n              {\n                value: 16,\n                label: \"防汛设施检查\"\n              },\n              {\n                value: 17,\n                label: \"保温设施检查\"\n              },\n              {\n                value: 18,\n                label: \"事故油池检查\"\n              }\n            ],\n            clearable: true\n          },\n          {\n            label: \"消息提醒状态\",\n            value: \"taskState\",\n            type: \"select\",\n            options: [\n              {\n                value: 0,\n                label: \"关闭\"\n              },\n              {\n                value: 1,\n                label: \"开启\"\n              }\n            ],\n            clearable: true\n          },\n          {\n            label: \"周期(月)\",\n            value: \"zq\",\n            type: \"input\"\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 120]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"分公司\", prop: \"fgsmc\", minWidth: \"100\" },\n          { label: \"变电站\", prop: \"bdzmc\", minWidth: \"100\" },\n          { label: \"工作类型\", prop: \"gzlxmc\", minWidth: \"100\" },\n          { label: \"周期(月)\", prop: \"zq\", minWidth: \"60\" },\n          { label: \"消息提醒状态\", prop: \"status\", minWidth: \"80\" }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {},\n      selectRows: [],\n      showDeviceTree: false,\n      isFilter: false\n    };\n  },\n  mounted() {\n    this.getFgsOptions();\n    this.getData();\n  },\n  watch: {\n    isShowDetails(val) {\n      if (val) {\n        const el = this.$el.querySelector(\".el-dialog\");\n        el.style.left = 0;\n        el.style.top = 0;\n      }\n    }\n  },\n  methods: {\n    /**\n     * 获取分公司下拉数据\n     */\n    getFgsOptions() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.organizationSelectedList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"ssgs\") {\n            return (item.options = this.organizationSelectedList);\n          }\n        });\n      });\n    },\n    //启动任务\n    startJob(jobId) {\n      startJob(jobId).then(res => {\n        if (res.code === \"0000\") {\n          this.msgSuccess(\"启动成功\");\n          this.getData();\n        } else {\n          this.$message({\n            type: \"error\",\n            message: \"启动失败!\"\n          });\n        }\n      });\n    },\n    chackJob(jobId) {\n      chackJob(jobId).then(res => {\n        if (res.code === \"0000\") {\n          this.msgSuccess(\"启动成功\");\n        } else {\n          this.$message({\n            type: \"error\",\n            message: \"启动失败!\"\n          });\n        }\n      });\n    },\n    //停止任务\n    stopJob(jobId) {\n      endJob(jobId).then(res => {\n        if (res.code === \"0000\") {\n          this.msgSuccess(\"停止成功\");\n          this.getData();\n        } else {\n          this.$message({\n            type: \"error\",\n            message: \"停止失败!\"\n          });\n        }\n      });\n    },\n    //下拉框change事件\n    handleEvent(val, eventValue) {\n      if (val.label === \"ssgs\") {\n        getBdzSelectList({ ssdwbm: val.value.toString() }).then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value == \"bdz\") {\n              this.$set(eventValue, \"bdz\", \"\");\n              return (item.options = res.data);\n            }\n          });\n        });\n      }\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.loading = true;\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        const { data, code } = await getList(param);\n        if (code === \"0000\") {\n          for (let i of data.records) {\n            i.status = i.taskState === 1 ? \"开启\" : \"关闭\";\n            i.fgsmc = this.formatSsgs(i.ssgs);\n            i.gzlxmc = this.formatGzlx(i.gzlx);\n          }\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          this.loading = false;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //重置按钮\n    getReset() {\n      this.params = {};\n    },\n    //选中行\n    handleSelectionChange() {},\n    //新增\n    getInsert() {\n      this.title = \"定期工作配置新增\";\n      this.isDisabled = false;\n      this.isShowDetails = true;\n      this.form = {};\n    },\n    //编辑按钮\n    updateRow(row) {\n      this.title = \"定期工作配置修改\";\n      this.isDisabled = false;\n      this.form = { ...row };\n      this.isShowDetails = true;\n      this.getBdzList();\n    },\n    //详情按钮\n    getInfo(row) {\n      this.title = \"定期工作配置详情\";\n      this.form = { ...row };\n      this.isDisabled = true;\n      this.isShowDetails = true;\n      this.getBdzList();\n    },\n\n    formatGzlx(gzlx) {\n      if (gzlx) {\n        return this.gzlxList.filter(g => g.value == gzlx)[0].label;\n      } else {\n        return \"\";\n      }\n    },\n    formatSsgs(ssgs) {\n      let pageOrganizationSelectedList = JSON.parse(JSON.stringify(this.organizationSelectedList))\n      pageOrganizationSelectedList.push({label: '港东变电分公司', value: '3002'})\n      pageOrganizationSelectedList.push({label: '港中变电分公司', value: '3003'})\n      if (ssgs) {\n        return pageOrganizationSelectedList.filter(g => g.value === ssgs)[0]\n          .label;\n      } else {\n        return \"\";\n      }\n    },\n    async saveRow() {\n      try {\n        let { code } = await saveOrUpdate(this.form);\n        if (code === \"0000\") {\n          this.$message.success(\"操作成功\");\n        }\n      } catch (e) {\n        console.log(e);\n      }\n      //重置page页从1开始\n      this.tableAndPageInfo.pager.pageResize = \"Y\";\n      await this.getData();\n      this.isShowDetails = false;\n    },\n    //删除按钮\n    async deleteRow(objId) {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove(objId).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n      await this.getData();\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false;\n    },\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n\n    inputFocusEvent(val) {\n      if (val.target.name === \"sbfl\") {\n        alter(\"test\");\n        this.showDeviceTree = true;\n        this.isFilter = true;\n      }\n    },\n    //清空表单数据\n    handleClose() {\n      this.form = {};\n      this.$nextTick(() => {\n        this.form = this.$options.data().form;\n        this.resetForm(\"form\");\n      });\n    },\n    //获取变电站下拉框\n    getBdzList() {\n      if (this.title === \"定期工作配置新增\") {\n        this.$set(this.form, \"bdz\", \"\");\n      }\n      this.bdzList = [];\n      getBdzSelectList({ ssdwbm: this.form.ssgs }).then(res => {\n        this.bdzList = res.data;\n      });\n    }\n  }\n};\n</script>\n\n<style scoped></style>\n"]}]}