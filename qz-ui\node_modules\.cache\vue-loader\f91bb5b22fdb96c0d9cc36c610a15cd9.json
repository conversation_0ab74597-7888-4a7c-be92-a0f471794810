{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\sybglr.vue?vue&type=template&id=3a376a48&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\sybglr.vue", "mtime": 1706897323683}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImFwcC1jb250YWluZXIiPgogIDwhLS0gaWQ9ImFwcCIgLS0+CiAgPGVsLWZpbHRlcgogICAgOmRhdGE9ImZpbHRlckluZm8uZGF0YSIKICAgIDpmaWVsZC1saXN0PSJmaWx0ZXJJbmZvLmZpZWxkTGlzdCIKICAgIDp3aWR0aD0ieyBsYWJlbFdpZHRoOiAxMDAsIGl0ZW1XaWR0aDogMjAwIH0iCiAgICBAaGFuZGxlUmVzZXQ9ImdldFJlc2V0IgogICAgQGhhbmRsZUV2ZW50PSJoYW5kbGVFdmVudCIKICAvPgogIDxlbC13aGl0ZSBjbGFzcz0iYnV0dG9uLWdyb3VwMSI+CiAgICA8ZGl2IGNsYXNzPSJidXR0b25fYnRuIj4KICAgICAgPGVsLWJ1dHRvbgogICAgICAgIHR5cGU9InByaW1hcnkiCiAgICAgICAgaWNvbj0iZWwtaWNvbi1wbHVlIgogICAgICAgIEBjbGljaz0iZ2V0SW5zZXJ0IgogICAgICAgIHYtaGFzUGVybWk9Ilsnc3liZ2xyOmJ1dHRvbjphZGQnXSIKICAgICAgICA+5paw5aKeCiAgICAgIDwvZWwtYnV0dG9uPgogICAgICA8ZWwtYnV0dG9uCiAgICAgICAgdHlwZT0icHJpbWFyeSIKICAgICAgICBpY29uPSJlbC1pY29uLXBsdWUiCiAgICAgICAgQGNsaWNrPSJnZXRJbnNlcnRscyIKICAgICAgICB2LWhhc1Blcm1pPSJbJ3N5YmdscmxzOmJ1dHRvbjphZGQnXSIKICAgICAgICA+5Y6G5Y+y5pWw5o2u5paw5aKeCiAgICAgIDwvZWwtYnV0dG9uPgogICAgICA8ZWwtYnV0dG9uIHR5cGU9InByaW1hcnkiIGljb249ImVsLWljb24tZG93bmxvYWQiIEBjbGljaz0iZGNwZGYiCiAgICAgICAgPuaJuemHj+WvvOWHujwvZWwtYnV0dG9uCiAgICAgID4KICAgICAgPCEtLSA8ZWwtYnV0dG9uIHR5cGU9ImRhbmdlciIgaWNvbj0iZWwtaWNvbi1kZWxldGUiIEBjbGljaz0iZGVsZXRlUm93Ij7liKDpmaQ8L2VsLWJ1dHRvbj4gLS0+CiAgICA8L2Rpdj4KICAgIDxjb21wLXRhYmxlCiAgICAgIDp0YWJsZS1hbmQtcGFnZS1pbmZvPSJ0YWJsZUFuZFBhZ2VJbmZvIgogICAgICBAdXBkYXRlOm11bHRpcGxlU2VsZWN0aW9uPSJoYW5kZWxTZWxlY3RDaGFuZ2UiCiAgICAgIGhlaWdodD0iNjN2aCIKICAgICAgcmVmPSJzeWJnbHIiCiAgICA+CiAgICAgIDxlbC10YWJsZS1jb2x1bW4KICAgICAgICBzbG90PSJ0YWJsZV9laWdodCIKICAgICAgICBhbGlnbj0iY2VudGVyIgogICAgICAgIGZpeGVkPSJyaWdodCIKICAgICAgICBzdHlsZT0iZGlzcGxheTogYmxvY2siCiAgICAgICAgbGFiZWw9IuaTjeS9nCIKICAgICAgICB3aWR0aD0iMjAwIgogICAgICAgIDpyZXNpemFibGU9ImZhbHNlIgogICAgICA+CiAgICAgICAgPHRlbXBsYXRlIHNsb3Qtc2NvcGU9InNjb3BlIj4KICAgICAgICAgIDwhLS0gICAg5YWs5YWx5oyJ6ZKuICAgICAgLS0+CiAgICAgICAgICA8ZWwtYnV0dG9uCiAgICAgICAgICAgIHR5cGU9InRleHQiCiAgICAgICAgICAgIHNpemU9InNtYWxsIgogICAgICAgICAgICBAY2xpY2s9ImdldERldGFpbHNJbmZvKHNjb3BlLnJvdykiCiAgICAgICAgICAgIHRpdGxlPSLor6bmg4UiCiAgICAgICAgICAgIGNsYXNzPSJlbC1pY29uLXZpZXciCiAgICAgICAgICA+CiAgICAgICAgICA8L2VsLWJ1dHRvbj4KICAgICAgICAgIDxlbC1idXR0b24KICAgICAgICAgICAgdHlwZT0idGV4dCIKICAgICAgICAgICAgc2l6ZT0ic21hbGwiCiAgICAgICAgICAgIHYtaWY9IgogICAgICAgICAgICAgIChzY29wZS5yb3cuaXNoZyA9PSAxIHx8IHNjb3BlLnJvdy5pc2hnID09IDYpICYmCiAgICAgICAgICAgICAgICBzY29wZS5yb3cuY3JlYXRlQnkgPT0gY3VyclVzZXIKICAgICAgICAgICAgIgogICAgICAgICAgICBAY2xpY2s9InVwZGF0ZURldGFpbHMoc2NvcGUucm93KSIKICAgICAgICAgICAgdGl0bGU9Iue8lui+kSIKICAgICAgICAgICAgY2xhc3M9ImVsLWljb24tZWRpdCIKICAgICAgICAgID48L2VsLWJ1dHRvbj4KICAgICAgICAgICA8ZWwtYnV0dG9uCiAgICAgICAgICAgIHR5cGU9InRleHQiCiAgICAgICAgICAgIHNpemU9InNtYWxsIgogICAgICAgICAgICB2LWhhc1Blcm1pPSJbJ3N5YmdscmxzOmJ1dHRvbjp1cGRhdGUnXSIKICAgICAgICAgICAgQGNsaWNrPSJ1cGRhdGVEZXRhaWxzKHNjb3BlLnJvdykiCiAgICAgICAgICAgIHRpdGxlPSLnvJbovpEiCiAgICAgICAgICAgIGNsYXNzPSJlbC1pY29uLWVkaXQiCiAgICAgICAgICA+PC9lbC1idXR0b24+CiAgICAgICAgICA8ZWwtYnV0dG9uCiAgICAgICAgICAgIHR5cGU9InRleHQiCiAgICAgICAgICAgIHNpemU9InNtYWxsIgogICAgICAgICAgICB2LWlmPSJzY29wZS5yb3cuaXNoZyAhPSAxICYmIHNjb3BlLnJvdy5pc2hnICE9IDYiCiAgICAgICAgICAgIEBjbGljaz0ic2hvd1RpbWVMaW5lKHNjb3BlLnJvdykiCiAgICAgICAgICAgIHRpdGxlPSLmtYHnqIvmn6XnnIsiCiAgICAgICAgICAgIGNsYXNzPSJlbC1pY29uLWxjY2sgY29tbW9uSWNvbiIKICAgICAgICAgID48L2VsLWJ1dHRvbj4KICAgICAgICAgIDxlbC1idXR0b24KICAgICAgICAgICAgdHlwZT0idGV4dCIKICAgICAgICAgICAgc2l6ZT0ic21hbGwiCiAgICAgICAgICAgIHYtaWY9InNjb3BlLnJvdy5pc2hnICE9IDEgJiYgc2NvcGUucm93LmlzaGcgIT0gNiIKICAgICAgICAgICAgQGNsaWNrPSJzaG93UHJvY2Vzc0ltZyhzY29wZS5yb3cpIgogICAgICAgICAgICB0aXRsZT0i5rWB56iL5Zu+IgogICAgICAgICAgICBjbGFzcz0iZWwtaWNvbi1sY3QgY29tbW9uSWNvbiIKICAgICAgICAgID48L2VsLWJ1dHRvbj4KICAgICAgICAgIDxlbC1idXR0b24KICAgICAgICAgICAgdHlwZT0idGV4dCIKICAgICAgICAgICAgc2l6ZT0ic21hbGwiCiAgICAgICAgICAgIHYtaWY9InNjb3BlLnJvdy5jcmVhdGVCeSA9PSBjdXJyVXNlciAmJiBzY29wZS5yb3cuaXNoZyA9PSA2IgogICAgICAgICAgICBAY2xpY2s9InNhdmVTeWJnSW5mbyhzY29wZS5yb3cpIgogICAgICAgICAgICB0aXRsZT0i57yW6L6R5oql5ZGKIgogICAgICAgICAgICBjbGFzcz0iZWwtaWNvbi1lZGl0LW91dGxpbmUiCiAgICAgICAgICA+PC9lbC1idXR0b24+CiAgICAgICAgICA8ZWwtYnV0dG9uCiAgICAgICAgICAgIHR5cGU9InRleHQiCiAgICAgICAgICAgIHNpemU9InNtYWxsIgogICAgICAgICAgICBAY2xpY2s9IlN5YmdJbmZvKHNjb3BlLnJvdykiCiAgICAgICAgICAgIHRpdGxlPSLmn6XnnIvmiqXlkYoiCiAgICAgICAgICAgIGNsYXNzPSJlbC1pY29uLXRpY2tldHMiCiAgICAgICAgICA+CiAgICAgICAgICA8L2VsLWJ1dHRvbj4KICAgICAgICAgIDxlbC1idXR0b24KICAgICAgICAgICAgdHlwZT0idGV4dCIKICAgICAgICAgICAgc2l6ZT0ic21hbGwiCiAgICAgICAgICAgIHYtaWY9InNjb3BlLnJvdy5pc2hnID09IDEgJiYgc2hvd0J1dHRvbiIKICAgICAgICAgICAgQGNsaWNrPSJzYXZlU3liZ0luZm8oc2NvcGUucm93KSIKICAgICAgICAgICAgdGl0bGU9Iue8lui+keaKpeWRiiIKICAgICAgICAgICAgY2xhc3M9ImVsLWljb24tZWRpdC1vdXRsaW5lIgogICAgICAgICAgPjwvZWwtYnV0dG9uPgogICAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgICBAY2xpY2s9ImdldFp4bW1wSW5mbyhzY29wZS5yb3cpIgogICAgICAgICAgICB0eXBlPSJ0ZXh0IgogICAgICAgICAgICBzaXplPSJzbWFsbCIKICAgICAgICAgICAgdGl0bGU9IuaVsOaNruWvueavlCIKICAgICAgICAgICAgY2xhc3M9ImVsLWljb24tZWRpdC1vdXRsaW5lIgogICAgICAgICAgPjwvZWwtYnV0dG9uPgogICAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgICB0eXBlPSJ0ZXh0IgogICAgICAgICAgICBzaXplPSJzbWFsbCIKICAgICAgICAgICAgdi1pZj0iCiAgICAgICAgICAgICAgKChzY29wZS5yb3cuaXNoZyA9PSAxIHx8IHNjb3BlLnJvdy5pc2hnID09IDYpICYmCiAgICAgICAgICAgICAgICBzY29wZS5yb3cuY3JlYXRlQnkgPT0gY3VyclVzZXIpIHx8CiAgICAgICAgICAgICAgICBjdXJyVXNlciA9PT0gJ2FkbWluJwogICAgICAgICAgICAiCiAgICAgICAgICAgIEBjbGljaz0iZGVsZXRlUm93KHNjb3BlLnJvdy5vYmpJZCkiCiAgICAgICAgICAgIHRpdGxlPSLliKDpmaQiCiAgICAgICAgICAgIGNsYXNzPSJlbC1pY29uLWRlbGV0ZSIKICAgICAgICAgID48L2VsLWJ1dHRvbj4KICAgICAgICA8L3RlbXBsYXRlPgogICAgICA8L2VsLXRhYmxlLWNvbHVtbj4KICAgIDwvY29tcC10YWJsZT4KICA8L2VsLXdoaXRlPgoKICA8IS0t6K+m5oOFL+aWsOWini/kv67mlLktLT4KICA8ZWwtZGlhbG9nCiAgICA6dGl0bGU9InRpdGxlIgogICAgOnZpc2libGUuc3luYz0iaXNTaG93RGV0YWlscyIKICAgIHdpZHRoPSI1MCUiCiAgICB2LWRpYWxvZ0RyYWcKICA+CiAgICA8ZWwtZm9ybQogICAgICBsYWJlbC13aWR0aD0iMTMwcHgiCiAgICAgIHJlZj0iZm9ybSIKICAgICAgOm1vZGVsPSJmb3JtIgogICAgICA6ZGlzYWJsZWQ9ImlzRGlzYWJsZWQiCiAgICAgIDpydWxlcz0icnVsZXMiCiAgICA+CiAgICAgIDxlbC1yb3c+CiAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i6K6+5aSH5LiT5Lia77yaIiBwcm9wPSJzanp5Ij4KICAgICAgICAgICAgPGVsLXNlbGVjdAogICAgICAgICAgICAgIEBjaGFuZ2U9InNlbGVjdHNienkiCiAgICAgICAgICAgICAgdi1tb2RlbD0iZm9ybS5zanp5IgogICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogMTAwJSIKICAgICAgICAgICAgPgogICAgICAgICAgICAgIDxlbC1vcHRpb24KICAgICAgICAgICAgICAgIHYtZm9yPSJpdGVtIGluIHNienlMaXN0IgogICAgICAgICAgICAgICAgOmtleT0iaXRlbS52YWx1ZSIKICAgICAgICAgICAgICAgIDpsYWJlbD0iaXRlbS5sYWJlbCIKICAgICAgICAgICAgICAgIDp2YWx1ZT0iaXRlbS52YWx1ZSIKICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgPC9lbC1vcHRpb24+CiAgICAgICAgICAgIDwvZWwtc2VsZWN0PgogICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i6K+V6aqM5LiT5Lia77yaIiBwcm9wPSJzeXp5Ym0iPgogICAgICAgICAgICA8ZWwtc2VsZWN0CiAgICAgICAgICAgICAgQGNoYW5nZT0ic2VsZWN0U3l4ekRhdGFCeVN5enlibSIKICAgICAgICAgICAgICB2LW1vZGVsPSJmb3JtLnN5enlibSIKICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36YCJ5oup6K+V6aqM5LiT5LiaIgogICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogMTAwJSIKICAgICAgICAgICAgPgogICAgICAgICAgICAgIDxlbC1vcHRpb24KICAgICAgICAgICAgICAgIHYtZm9yPSJpdGVtIGluIHN5enlMaXN0IgogICAgICAgICAgICAgICAgOmtleT0iaXRlbS52YWx1ZSIKICAgICAgICAgICAgICAgIDpsYWJlbD0iaXRlbS5sYWJlbCIKICAgICAgICAgICAgICAgIDp2YWx1ZT0iaXRlbS52YWx1ZSIKICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgPC9lbC1vcHRpb24+CiAgICAgICAgICAgIDwvZWwtc2VsZWN0PgogICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i6K+V6aqM5oCn6LSo77yaIiBwcm9wPSJzeXh6Ym0iPgogICAgICAgICAgICA8ZWwtc2VsZWN0CiAgICAgICAgICAgICAgdi1tb2RlbD0iZm9ybS5zeXh6Ym0iCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+mAieaLqeivlemqjOaAp+i0qCIKICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDEwMCUiCiAgICAgICAgICAgID4KICAgICAgICAgICAgICA8ZWwtb3B0aW9uCiAgICAgICAgICAgICAgICB2LWZvcj0iaXRlbSBpbiBzeXh6TGlzdCIKICAgICAgICAgICAgICAgIDprZXk9Iml0ZW0udmFsdWUiCiAgICAgICAgICAgICAgICA6bGFiZWw9Iml0ZW0ubGFiZWwiCiAgICAgICAgICAgICAgICA6dmFsdWU9Iml0ZW0udmFsdWUiCiAgICAgICAgICAgICAgPgogICAgICAgICAgICAgIDwvZWwtb3B0aW9uPgogICAgICAgICAgICA8L2VsLXNlbGVjdD4KICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgIDwvZWwtY29sPgogICAgICAgIDxlbC1jb2wgOnNwYW49IjEyIj4KICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuiuvuWkh+WcsOeCue+8miIgcHJvcD0ic3lkZCI+CiAgICAgICAgICAgIDwhLS0gPGVsLWlucHV0IHYtbW9kZWw9ImZvcm0uc3lkZCIgICBwbGFjZWhvbGRlcj0i6K+36L6T5YWl6K6+5aSH5Zyw54K5IiB2LW9uOmNsaWNrLm5hdGl2ZT0ic3lzYmRkQ2xpY2soKSIKICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogMTAwJSI+PC9lbC1pbnB1dD4gLS0+CiAgICAgICAgICAgIDxlbC1zZWxlY3QKICAgICAgICAgICAgICB2LW1vZGVsPSJmb3JtLnN5ZGQiCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+mAieaLqeiuvuWkh+WcsOeCuSIKICAgICAgICAgICAgICBAY2hhbmdlPSJvbmNsY2tkZCIKICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDEwMCUiCiAgICAgICAgICAgICAgZmlsdGVyYWJsZQogICAgICAgICAgICAgIGNsZWFyYWJsZQogICAgICAgICAgICA+CiAgICAgICAgICAgICAgPGVsLW9wdGlvbgogICAgICAgICAgICAgICAgdi1mb3I9Iml0ZW0gaW4gc2JkZExpc3QiCiAgICAgICAgICAgICAgICA6a2V5PSJpdGVtLnZhbHVlIgogICAgICAgICAgICAgICAgOmxhYmVsPSJpdGVtLmxhYmVsIgogICAgICAgICAgICAgICAgOnZhbHVlPSJpdGVtIgogICAgICAgICAgICAgID4KICAgICAgICAgICAgICA8L2VsLW9wdGlvbj4KICAgICAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICA8L2VsLWNvbD4KICAgICAgICA8ZWwtY29sIDpzcGFuPSIxMiI+CiAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLor5Xpqozorr7lpIfvvJoiIHByb3A9InNibWMiPgogICAgICAgICAgICA8ZWwtaW5wdXQKICAgICAgICAgICAgICB2LW1vZGVsPSJmb3JtLnNibWMiCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeivlemqjOiuvuWkhyIKICAgICAgICAgICAgICB2LW9uOmNsaWNrLm5hdGl2ZT0ic3lzYlNlbGVjdGVkQ2xpY2soKSIKICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDEwMCUiCiAgICAgICAgICAgID48L2VsLWlucHV0PgogICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgPC9lbC1jb2w+CgogICAgICAgIDxlbC1jb2wgOnNwYW49IjEyIj4KICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuWnlOaJmOWNleS9je+8miIgcHJvcD0id3RkdyI+CiAgICAgICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgICAgIHYtbW9kZWw9ImZvcm0ud3RkdyIKICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36L6T5YWl5aeU5omY5Y2V5L2NIgogICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogMTAwJSIKICAgICAgICAgICAgPgogICAgICAgICAgICA8L2VsLWlucHV0PgogICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgPC9lbC1jb2w+CgogICAgICAgIDxlbC1jb2wgOnNwYW49IjEyIj4KICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuivlemqjOWNleS9je+8miIgcHJvcD0ic3lkdyI+CiAgICAgICAgICAgIDxlbC1zZWxlY3QKICAgICAgICAgICAgICB2LW1vZGVsPSJmb3JtLnN5ZHciCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+mAieaLqeivlemqjOWNleS9jSIKICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDEwMCUiCiAgICAgICAgICAgID4KICAgICAgICAgICAgICA8ZWwtb3B0aW9uCiAgICAgICAgICAgICAgICB2LWZvcj0iaXRlbSBpbiBzeWR3TGlzdCIKICAgICAgICAgICAgICAgIDprZXk9Iml0ZW0udmFsdWUiCiAgICAgICAgICAgICAgICA6bGFiZWw9Iml0ZW0ubGFiZWwiCiAgICAgICAgICAgICAgICA6dmFsdWU9IlN0cmluZyhpdGVtLnZhbHVlKSIKICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgPC9lbC1vcHRpb24+CiAgICAgICAgICAgIDwvZWwtc2VsZWN0PgogICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgPC9lbC1jb2w+CgogICAgICAgIDxlbC1jb2wgOnNwYW49IjEyIj4KICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuivlemqjOaXpeacn++8miIgcHJvcD0ic3lycSI+CiAgICAgICAgICAgIDxlbC1kYXRlLXBpY2tlcgogICAgICAgICAgICAgIHYtbW9kZWw9ImZvcm0uc3lycSIKICAgICAgICAgICAgICB0eXBlPSJkYXRlIgogICAgICAgICAgICAgIHZhbHVlLWZvcm1hdD0ieXl5eS1NTS1kZCIKICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36YCJ5oup6K+V6aqM5pel5pyfIgogICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogMTAwJSIKICAgICAgICAgICAgPgogICAgICAgICAgICA8L2VsLWRhdGUtcGlja2VyPgogICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgPC9lbC1jb2w+CgogICAgICAgIDxlbC1jb2wgOnNwYW49IjEyIj4KICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuivlemqjOWQjeensO+8miIgcHJvcD0ic3ltYyI+CiAgICAgICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgICAgIHYtbW9kZWw9ImZvcm0uc3ltYyIKICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36L6T5YWl6K+V6aqM5ZCN56ewIgogICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogMTAwJSIKICAgICAgICAgICAgPjwvZWwtaW5wdXQ+CiAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICA8L2VsLWNvbD4KICAgICAgICA8ZWwtY29sIDpzcGFuPSIxMiI+CiAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLmuKnluqYo4oSDKe+8miIgcHJvcD0id2QiPgogICAgICAgICAgICA8ZWwtaW5wdXQKICAgICAgICAgICAgICB2LW1vZGVsPSJmb3JtLndkIgogICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaXmuKnluqYiCiAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOiAxMDAlIgogICAgICAgICAgICA+PC9lbC1pbnB1dD4KICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgIDwvZWwtY29sPgoKICAgICAgICA8ZWwtY29sIDpzcGFuPSIxMiI+CiAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLmub/luqYoJSnvvJoiIHByb3A9InNkIj4KICAgICAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICAgICAgdi1tb2RlbD0iZm9ybS5zZCIKICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36L6T5YWl5rm/5bqmIgogICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogMTAwJSIKICAgICAgICAgICAgPjwvZWwtaW5wdXQ+CiAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICA8L2VsLWNvbD4KICAgICAgICA8ZWwtY29sIDpzcGFuPSIxMiI+CiAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLmsrnmuKko4oSDKe+8miIgcHJvcD0ieXciPgogICAgICAgICAgICA8ZWwtaW5wdXQKICAgICAgICAgICAgICB2LW1vZGVsPSJmb3JtLnl3IgogICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaXmsrnmuKkiCiAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOiAxMDAlIgogICAgICAgICAgICA+PC9lbC1pbnB1dD4KICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgIDwvZWwtY29sPgoKICAgICAgICA8ZWwtY29sIDpzcGFuPSIxMiI+CiAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLor5XpqozlpKnmsJTvvJoiIHByb3A9InRxIj4KICAgICAgICAgICAgPGVsLXNlbGVjdAogICAgICAgICAgICAgIHYtbW9kZWw9ImZvcm0udHEiCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+mAieaLqeivlemqjOWkqeawlCIKICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDEwMCUiCiAgICAgICAgICAgID4KICAgICAgICAgICAgICA8ZWwtb3B0aW9uCiAgICAgICAgICAgICAgICB2LWZvcj0iaXRlbSBpbiB0cUxpc3QiCiAgICAgICAgICAgICAgICA6a2V5PSJpdGVtLnZhbHVlIgogICAgICAgICAgICAgICAgOmxhYmVsPSJpdGVtLmxhYmVsIgogICAgICAgICAgICAgICAgOnZhbHVlPSJpdGVtLnZhbHVlIgogICAgICAgICAgICAgID4KICAgICAgICAgICAgICA8L2VsLW9wdGlvbj4KICAgICAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICA8L2VsLWNvbD4KICAgICAgICA8ZWwtY29sIDpzcGFuPSIxMiI+CiAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLor5XpqozmqKHmnb/vvJoiIHByb3A9InN5bWIiPgogICAgICAgICAgICA8ZWwtaW5wdXQKICAgICAgICAgICAgICB2LW1vZGVsPSJmb3JtLnN5bWIiCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeivlemqjOaooeadvyIKICAgICAgICAgICAgICB2LW9uOmNsaWNrLm5hdGl2ZT0ic3ltYlNlbGVjdGVkQ2xpY2soKSIKICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDEwMCUiCiAgICAgICAgICAgID48L2VsLWlucHV0PgogICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgPC9lbC1jb2w+CgogICAgICAgIDwhLS0gPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5a6h5qC45Lq677yaIiBwcm9wPSJzaHJpZCI+CiAgICAgICAgICAgIDxlbC1pbnB1dCB2LW1vZGVsPSJmb3JtLnNocmlkIiBwbGFjZWhvbGRlcj0i6K+36L6T5YWl5a6h5qC45Lq6IiBzdHlsZT0id2lkdGg6IDEwMCUiPjwvZWwtaW5wdXQ+CiAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICA8L2VsLWNvbD4gLS0+CiAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i57yW5Yi25Lq677yaIiBwcm9wPSJienJpZCI+CiAgICAgICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgICAgIHYtbW9kZWw9ImZvcm0uYnpyaWQiCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpee8luWItuS6uiIKICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDEwMCUiCiAgICAgICAgICAgID48L2VsLWlucHV0PgogICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgPC9lbC1jb2w+CgogICAgICAgIDxlbC1jb2wgOnNwYW49IjEyIj4KICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuivlemqjOS6uuWRmO+8miIgcHJvcD0ic3lyeWlkIj4KICAgICAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICAgICAgdi1tb2RlbD0iZm9ybS5zeXJ5aWQiCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeivlemqjOS6uuWRmCIKICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDEwMCUiCiAgICAgICAgICAgID48L2VsLWlucHV0PgogICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgPC9lbC1jb2w+CgogICAgICAgIDxlbC1jb2wgOnNwYW49IjEyIj4KICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuWuoeaguOS6uu+8miIgcHJvcD0iYnp6c3AiPgogICAgICAgICAgICA8ZWwtaW5wdXQKICAgICAgICAgICAgICB2LW1vZGVsPSJmb3JtLmJ6enNwIgogICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaXlrqHmoLjkuroiCiAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOiAxMDAlIgogICAgICAgICAgICAgIDpkaXNhYmxlZD0ic3lyeURpYWxvZyIKICAgICAgICAgICAgPjwvZWwtaW5wdXQ+CiAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICA8L2VsLWNvbD4KCiAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5om55YeG5Lq677yaIiBwcm9wPSJzc2R3bGRtYyI+CiAgICAgICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgICAgIHYtbW9kZWw9ImZvcm0uc3Nkd2xkbWMiCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeaJueWHhuS6uiIKICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDEwMCUiCiAgICAgICAgICAgICAgOmRpc2FibGVkPSJzeXJ5RGlhbG9nIgogICAgICAgICAgICA+CiAgICAgICAgICAgIDwvZWwtaW5wdXQ+CiAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICA8L2VsLWNvbD4KICAgICAgPC9lbC1yb3c+CiAgICAgIDwhLS0gPGVsLXJvdz4KICAgICAgICA8ZWwtY29sIDpzcGFuPSIyNCI+CiAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLor5Xpqoznu5PorrrvvJoiIHByb3A9ImJ6Ij4KICAgICAgICAgICAgPGVsLWlucHV0IHR5cGU9InRleHRhcmVhIiB2LW1vZGVsPSJmb3JtLnN5amwiIHBsYWNlaG9sZGVyPSLor7fovpPlhaXor5Xpqoznu5PorroiCiAgICAgICAgICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDEwMCUiPjwvZWwtaW5wdXQ+CiAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICA8L2VsLWNvbD4KICAgICAgPC9lbC1yb3c+IC0tPgogICAgICA8ZWwtcm93PgogICAgICAgIDxlbC1jb2wgOnNwYW49IjI0Ij4KICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuWkh+azqO+8miIgcHJvcD0iYnoiPgogICAgICAgICAgICA8ZWwtaW5wdXQKICAgICAgICAgICAgICB0eXBlPSJ0ZXh0YXJlYSIKICAgICAgICAgICAgICB2LW1vZGVsPSJmb3JtLmJ6IgogICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaXlpIfms6giCiAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOiAxMDAlIgogICAgICAgICAgICA+PC9lbC1pbnB1dD4KICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgIDwvZWwtY29sPgogICAgICA8L2VsLXJvdz4KICAgIDwvZWwtZm9ybT4KICAgIDxkaXYgc2xvdD0iZm9vdGVyIiB2LXNob3c9ImlzU2hvdyI+CiAgICAgIDxlbC1idXR0b24gQGNsaWNrPSJjbG9zZSI+5Y+WIOa2iDwvZWwtYnV0dG9uPgogICAgICA8ZWwtYnV0dG9uIHR5cGU9InByaW1hcnkiIEBjbGljaz0ic2F2ZSI+56GuIOWumjwvZWwtYnV0dG9uPgogICAgPC9kaXY+CiAgPC9lbC1kaWFsb2c+CiAgPCEtLeivlemqjOiuvuWkh+WcsOeCueW8ueeqly0tPgogIDxlbC1kaWFsb2cKICAgIHRpdGxlPSLor5Xpqozorr7lpIflnLDngrkiCiAgICA6dmlzaWJsZS5zeW5jPSJpc1Nob3dTeXNiZGREaWFsb2ciCiAgICB3aWR0aD0iMjAlIgogICAgdi1pZj0iaXNTaG93U3lzYmRkRGlhbG9nIgogICAgdi1kaWFsb2dEcmFnCiAgPgogICAgPHN5c2JkZAogICAgICA6bWFpbkRhdGE9Im1haW5EYXRhIgogICAgICBAYWNjZXNzVHJlZURhdGE9ImhhbmRsZUFjY2Vzc1RyZWVEYXRhIgogICAgICBAY2xvc2VTeWRkRGlhbG9nPSJoYW5kbGVDbG9zZVN5ZGREaWFsb2ciCiAgICA+CiAgICA8L3N5c2JkZD4KICA8L2VsLWRpYWxvZz4KICA8IS0t6K+V6aqM6K6+5aSH6YCJ5oup5by55qGGLS0+CiAgPGVsLWRpYWxvZwogICAgdGl0bGU9IuiuvuWkh+mAieaLqSIKICAgIDp2aXNpYmxlLnN5bmM9ImlzU2hvd1N5c2JEaWFsb2ciCiAgICB3aWR0aD0iNjAlIgogICAgdi1pZj0iaXNTaG93U3lzYkRpYWxvZyIKICAgIHYtZGlhbG9nRHJhZwogID4KICAgIDxzeXNiLXNlbGVjdGVkYmcKICAgICAgQGhhbmRsZUFjY2VwdFNiRGF0YT0iaGFuZGxlQWNjZXB0U2JEYXRhIgogICAgICA6c2VsZWN0ZWRTYlBhcmFtPSJzZWxlY3RlZFNiUGFyYW0iCiAgICAgIEBjbG9zZVN5c2JTZWxlY3REaWFsb2c9ImhhbmRsZUNvbnRyb2xTeXNiU2VsZWN0RGlhbG9nIgogICAgPjwvc3lzYi1zZWxlY3RlZGJnPgogIDwvZWwtZGlhbG9nPgogIDwhLS3or5XpqozmqKHmnb/pgInmi6nnu4Tku7YtLT4KICA8ZWwtZGlhbG9nCiAgICB0aXRsZT0i5qih5p2/6YCJ5oupIgogICAgOnZpc2libGUuc3luYz0iaXNTaG93U3ltYkRpYWxvZyIKICAgIHdpZHRoPSI3MCUiCiAgICB2LWlmPSJpc1Nob3dTeW1iRGlhbG9nIgogICAgdi1kaWFsb2dEcmFnCiAgPgogICAgPHN5bWJTeXhtLXNlbGVjdAogICAgICA6c3ltYkRhdGE9InN5bWJEYXRhIgogICAgICBAaGFuZGxlQWNjZXB0TWJEYXRhPSJoYW5kbGVBY2NlcHRNYkRhdGEiCiAgICAgIEBjbG9zZVN5bWJTZWxlY3REaWFsb2c9ImhhbmRsZUNvbnRyb2xTeW1iU2VsZWN0RGlhbG9nIgogICAgPjwvc3ltYlN5eG0tc2VsZWN0PgogIDwvZWwtZGlhbG9nPgogIDwhLS3loavlhpnmiqXlkYrmqKHmnb8tLT4KICA8IS0taHRtbFRvUGRm5o+S5Lu2LS0+CiAgPGVsLWRpYWxvZwogICAgdGl0bGU9IumihOiniCIKICAgIDp2aXNpYmxlLnN5bmM9ImlzU2hvd0Rvd25Mb2FkRGlhbG9nIgogICAgd2lkdGg9IjcwJSIKICAgIGNsYXNzPSJvdXRQdXQiCiAgICBpZD0iZGlhbG9nQWN0c3QiCiAgICB2LWRpYWxvZ0RyYWcKICA+CiAgICA8ZGl2IHN0eWxlPSJoZWlnaHQ6IDUwMHB4O292ZXJmbG93LXk6IHNjcm9sbDsiPgogICAgICA8dGFibGVQZGYKICAgICAgICByZWY9InRhYmxlUGRmIgogICAgICAgIDpiYXNpYy1kYXRhPSJiYXNpY0RhdGEiCiAgICAgICAgOnRhYmxlYm94Mz0idGFibGVib3gzIgogICAgICAgIDp0YWJsZWJveDI9InRhYmxlYm94MiIKICAgICAgPjwvdGFibGVQZGY+CiAgICA8L2Rpdj4KICAgIDxkaXYgc2xvdD0iZm9vdGVyIj4KICAgICAgPGVsLWJ1dHRvbgogICAgICAgIEBjbGljaz0iY2xvc2VZbERpYWxvZyIKICAgICAgICB2LWlmPSJ0aGlzLmZvcm0uaXNoZyA9PSAxIHx8IHRoaXMuZm9ybS5pc2hnID09IDYiCiAgICAgICAgPuWPliDmtog8L2VsLWJ1dHRvbgogICAgICA+CiAgICAgIDxlbC1idXR0b24KICAgICAgICB0eXBlPSJwcmltYXJ5IgogICAgICAgIEBjbGljaz0ic2F2ZVlsRGlhbG9nIgogICAgICAgIHYtaWY9IgogICAgICAgICAgKHRoaXMuZm9ybS5pc2hnID09IDEgfHwKICAgICAgICAgICAgdGhpcy5mb3JtLmlzaGcgPT0gNiB8fAogICAgICAgICAgICB0aGlzLmZvcm0uaXNoZyA9PSA1KSAmJgogICAgICAgICAgICB0aGlzLmJjRGlzYWJsZWQKICAgICAgICAiCiAgICAgICAgPuS/neWtmAogICAgICA8L2VsLWJ1dHRvbj4KICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBAY2xpY2s9ImV4cG9ydFBkZigndGFibGVQZGYnKSIKICAgICAgICA+5a+8IOWHujwvZWwtYnV0dG9uCiAgICAgID4KICAgICAgPGVsLWJ1dHRvbgogICAgICAgIHR5cGU9InByaW1hcnkiCiAgICAgICAgQGNsaWNrPSJjb21taXRTaCIKICAgICAgICB2LWlmPSJ0aGlzLmZvcm0uaXNoZyA9PSAxICYmIHRoaXMuZm9ybS5jcmVhdGVCeSA9PSBjdXJyVXNlciIKICAgICAgICA+5LiK5oqlCiAgICAgIDwvZWwtYnV0dG9uPgogICAgICA8ZWwtYnV0dG9uCiAgICAgICAgdHlwZT0icHJpbWFyeSIKICAgICAgICBAY2xpY2s9ImNvbW1pdFNoIgogICAgICAgIHYtaWY9InRoaXMuZm9ybS5pc2hnID09IDIgJiYgdGhpcy5mb3JtLnNzZHdsZCA9PSBjdXJyVXNlciIKICAgICAgICA+5a6h5om5CiAgICAgIDwvZWwtYnV0dG9uPgogICAgICA8ZWwtYnV0dG9uCiAgICAgICAgdHlwZT0icHJpbWFyeSIKICAgICAgICBAY2xpY2s9ImNvbW1pdFNoIgogICAgICAgIHYtaWY9IgogICAgICAgICAgdGhpcy5mb3JtLmlzaGcgPT0gNyAmJgogICAgICAgICAgICB0aGlzLmZvcm0uc2Rkd3NociAmJgogICAgICAgICAgICB0aGlzLmZvcm0uc2Rkd3Noci5pbmNsdWRlcyhjdXJyVXNlcikKICAgICAgICAiCiAgICAgICAgPuWuoeaJuQogICAgICA8L2VsLWJ1dHRvbj4KICAgICAgPGVsLWJ1dHRvbgogICAgICAgIHR5cGU9InByaW1hcnkiCiAgICAgICAgQGNsaWNrPSJjb21taXRTaCIKICAgICAgICB2LWlmPSJ0aGlzLmZvcm0uaXNoZyA9PSAzICYmIHRoaXMuZm9ybS5zY2t6ZyA9PSBjdXJyVXNlciIKICAgICAgICA+5a6h5om5CiAgICAgIDwvZWwtYnV0dG9uPgogICAgICA8ZWwtYnV0dG9uCiAgICAgICAgdHlwZT0icHJpbWFyeSIKICAgICAgICBAY2xpY2s9ImNvbW1pdFNoIgogICAgICAgIHYtaWY9InRoaXMuZm9ybS5pc2hnID09IDUgJiYgdGhpcy5mb3JtLmJ6enNwID09IGN1cnJVc2VyTmFtZSIKICAgICAgICA+5a6h5om5CiAgICAgIDwvZWwtYnV0dG9uPgogICAgICA8ZWwtYnV0dG9uCiAgICAgICAgdHlwZT0icHJpbWFyeSIKICAgICAgICBAY2xpY2s9ImhhbmRsZVRoKCdyb2xsYmFjaycpIgogICAgICAgIHYtaWY9InRoaXMuZm9ybS5pc2hnID09IDIgJiYgdGhpcy5mb3JtLnNzZHdsZCA9PSBjdXJyVXNlciIKICAgICAgPgogICAgICAgIOWbnumAgDwvZWwtYnV0dG9uCiAgICAgID4KICAgICAgPGVsLWJ1dHRvbgogICAgICAgIHR5cGU9InByaW1hcnkiCiAgICAgICAgQGNsaWNrPSJoYW5kbGVUaCgncm9sbGJhY2snKSIKICAgICAgICB2LWlmPSJ0aGlzLmZvcm0uaXNoZyA9PSAzICYmIHRoaXMuZm9ybS5zY2t6ZyA9PSBjdXJyVXNlciIKICAgICAgPgogICAgICAgIOWbnumAgDwvZWwtYnV0dG9uCiAgICAgID4KICAgICAgPGVsLWJ1dHRvbgogICAgICAgIHR5cGU9InByaW1hcnkiCiAgICAgICAgQGNsaWNrPSJoYW5kbGVUaCgncm9sbGJhY2snKSIKICAgICAgICB2LWlmPSJ0aGlzLmZvcm0uaXNoZyA9PSA1ICYmIHRoaXMuZm9ybS5ienpzcCA9PSBjdXJyVXNlck5hbWUiCiAgICAgICAgPuWbnumAgDwvZWwtYnV0dG9uCiAgICAgID4KICAgICAgPGVsLWJ1dHRvbgogICAgICAgIHR5cGU9InByaW1hcnkiCiAgICAgICAgQGNsaWNrPSJoYW5kbGVUaCgncm9sbGJhY2snKSIKICAgICAgICB2LWlmPSJ0aGlzLmZvcm0uaXNoZyA9PSA3ICYmIHRoaXMuZm9ybS5zZGR3c2hyID09IGN1cnJVc2VyIgogICAgICAgID7lm57pgIA8L2VsLWJ1dHRvbgogICAgICA+CiAgICA8L2Rpdj4KICA8L2VsLWRpYWxvZz4KICA8ZWwtZGlhbG9nCiAgICB0aXRsZT0i5om56YeP5a+85Ye6IgogICAgOnZpc2libGUuc3luYz0iaXNTaG93RG93bkxvYWREaWFsb2dBIgogICAgd2lkdGg9IjcwJSIKICAgIGNsYXNzPSJvdXRQdXQiCiAgICBpZD0iZGlhbG9nQWN0c3QiCiAgICB2LWRpYWxvZ0RyYWcKICA+CiAgICA8ZGl2IHN0eWxlPSJoZWlnaHQ6IDQwMHB4O292ZXJmbG93LXk6IHNjcm9sbDsiPgogICAgICA8dGFibGVQZGYKICAgICAgICA6cmVmPSJgdGFibGVQZGYke2luZGV4fWAiCiAgICAgICAgdi1mb3I9IihpdGVtLCBpbmRleCkgaW4gc2VsZWN0QSIKICAgICAgICA6YmFzaWMtZGF0YT0iaXRlbSIKICAgICAgICA6dGFibGVib3gzPSJpdGVtLnRhYmxlYm94MyIKICAgICAgICA6dGFibGVib3gyPSJpdGVtLnRhYmxlYm94MiIKICAgICAgPjwvdGFibGVQZGY+CiAgICA8L2Rpdj4KICAgIDxlbC1wcm9ncmVzcwogICAgICA6cGVyY2VudGFnZT0iaW5wdXRwZGYiCiAgICAgIDpmb3JtYXQ9InByb2dyZXNzZm9ybWF0ZSIKICAgID48L2VsLXByb2dyZXNzPgogICAgPCEtLSA8ZWwtcHJvZ3Jlc3MgOnBlcmNlbnRhZ2U9InNoZW5ncGRmIiBzdGF0dXM9InN1Y2Nlc3MiPjwvZWwtcHJvZ3Jlc3M+CiAgICA8ZWwtcHJvZ3Jlc3MgOnBlcmNlbnRhZ2U9ImRvd25sb2FkcGRmIiBzdGF0dXM9Indhcm5pbmciPjwvZWwtcHJvZ3Jlc3M+IC0tPgogIDwvZWwtZGlhbG9nPgoKICA8IS0tICDlt6XkvZzmtYEgIC0tPgogIDxhY3Rpdml0aQogICAgOnByb2Nlc3NEYXRhPSJwcm9jZXNzRGF0YSIKICAgIDppc1Nob3c9ImlzU2hvd0FjdGl2aXRpIgogICAgOm9wdGlvbj0iYWN0aXZpdGlPcHRpb24iCiAgICBAdG9kb0RhdGE9InRvZG9SZXN1bHQiCiAgICBAdG9DbG9zZT0iY2xvc2VBY3Rpdml0aSIKICAgIHYtaWY9ImlzU2hvd0FjdGl2aXRpIgogID48L2FjdGl2aXRpPgogIDx0aW1lLWxpbmUKICAgIDp2YWx1ZT0idGltZUxpbmVTaG93IgogICAgOnRpbWVEYXRhPSJ0aW1lRGF0YSIKICAgIEBjbG9zZVRpbWVMaW5lPSJjb2xzZVRpbWVMaW5lIgogIC8+CiAgPCEtLea1geeoi+Wbvuafpeeciy0tPgogIDxlbC1kaWFsb2cKICAgIHRpdGxlPSLlrqHmibnmtYHnqIvlm74iCiAgICA6dmlzaWJsZS5zeW5jPSJvcGVuTG9hZGluZ0ltZyIKICAgIHdpZHRoPSI1NiUiCiAgICBhcHBlbmQtdG8tYm9keQogICAgOmNsb3NlLW9uLWNsaWNrLW1vZGFsPSJmYWxzZSIKICAgIHYtZGlhbG9nRHJhZwogID4KICAgIDxkaXY+CiAgICAgIDxpbWcgOnNyYz0iaW1nU3JjIiBzdHlsZT0id2lkdGg6IDEwMCUiIC8+CiAgICA8L2Rpdj4KICA8L2VsLWRpYWxvZz4KICA8IS0tIOivlemqjOaVsOaNruWvueavlCAtLT4KICA8ZWwtZGlhbG9nCiAgICA6dmlzaWJsZS5zeW5jPSJpc1Nob3dNcEluZm8iCiAgICB2LWRpYWxvZ0RyYWcKICAgIHYtaWY9ImlzU2hvd01wSW5mbyIKICAgIHdpZHRoPSI4MCUiCiAgICB0aXRsZT0i6K+V6aqM6aG555uu5YaF5a65IgogICAgOmFwcGVuZC10by1ib2R5PSJ0cnVlIgogICAgQGNsb3NlPSJjbG9zZUluZm9EaWFsb2ciCiAgPgogICAgPHp4bS1pbmZvCiAgICAgIDptcC1kYXRhPSJyb3dEYXRhIgogICAgICA6bXgtZGF0YS5zeW5jPSJteERhdGEiCiAgICAgIEBjbG9zZUluZm9EaWFsb2c9ImNsb3NlSW5mb0RpYWxvZyIKICAgIC8+CiAgPC9lbC1kaWFsb2c+CjwvZGl2Pgo="}, null]}