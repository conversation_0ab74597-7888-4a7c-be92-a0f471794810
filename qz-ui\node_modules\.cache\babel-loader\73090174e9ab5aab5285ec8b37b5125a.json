{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\sybglr.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\sybglr.vue", "mtime": 1706897323683}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["sybglr.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4nBA;;AAcA;;AAOA;;AACA;;AACA;;AACA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AACA;;AAEA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAjBA;AAEA;AAEA;AAEA;AAEA;AAEA;AAGA;eAKA;AACA,EAAA,IAAA,EAAA,QADA;AAEA,EAAA,UAAA,EAAA;AACA,IAAA,MAAA,EAAA,eADA;AAEA,IAAA,cAAA,EAAA,uBAFA;AAGA,IAAA,cAAA,EAAA,uBAHA;AAIA,IAAA,SAAA,EAAA,kBAJA;AAKA,IAAA,QAAA,EAAA,sBALA;AAMA,IAAA,QAAA,EAAA,iBANA;AAOA,IAAA,QAAA,EAAA,iBAPA;AAQA,IAAA,OAAA,EAAA;AARA,GAFA;AAYA,EAAA,IAZA,kBAYA;AAAA;;AACA,QAAA,YAAA,GAAA,SAAA,YAAA,CAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA,UAAA,CAAA,KAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AACA,QAAA,QAAA,CAAA,IAAA,KAAA,EAAA,CAAA;AACA,OAFA,MAEA;AACA,QAAA,QAAA;AACA;AACA,KANA;;AAOA,QAAA,YAAA,GAAA,SAAA,YAAA,CAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA,UAAA,CAAA,KAAA,CAAA,IAAA,CAAA,IAAA,EAAA;AACA,QAAA,QAAA,CAAA,IAAA,KAAA,EAAA,CAAA;AACA,OAFA,MAEA;AACA,QAAA,QAAA;AACA;AACA,KANA;;AAOA,WAAA;AACA;AACA,MAAA,YAAA,EAAA,KAFA;AAGA,MAAA,OAAA,EAAA,EAHA;AAIA,MAAA,MAAA,EAAA,EAJA;AAKA;AACA,MAAA,UAAA,EAAA,KANA;AAOA,MAAA,UAAA,EAAA,IAPA;AAQA,MAAA,UAAA,EAAA,KARA;AASA,MAAA,SAAA,EAAA,EATA;AAUA,MAAA,SAAA,EAAA,EAVA;AAWA,MAAA,OAAA,EAAA,KAXA;AAYA,MAAA,QAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,IAZA;AAaA,MAAA,YAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,QAbA;AAcA,MAAA,MAAA,EAAA,SAdA;AAcA;AACA,MAAA,KAAA,EAAA,SAfA;AAeA;AACA,MAAA,QAAA,EAAA,SAhBA;AAgBA;AACA,MAAA,QAAA,EAAA,EAjBA;AAkBA;AACA,MAAA,cAAA,EAAA,KAnBA;AAoBA,MAAA,MAAA,EAAA,EApBA;AAoBA;AACA,MAAA,YAAA,EAAA,KArBA;AAsBA,MAAA,QAAA,EAAA,EAtBA;AAuBA;AACA,MAAA,cAAA,EAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAxBA;AAyBA;AACA,MAAA,cAAA,EAAA,KA1BA;AA2BA;AACA,MAAA,UAAA,EAAA,EA5BA;AA6BA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,oBAAA,EAAA,QADA;AAEA,QAAA,WAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,QAHA;AAIA,QAAA,SAAA,EAAA,EAJA;AAKA,QAAA,WAAA,EAAA,IALA;AAMA,QAAA,WAAA,EAAA;AANA,OA9BA;AAsCA;AACA,MAAA,SAAA,EAAA;AACA,QAAA,MAAA,EAAA,EADA;AACA;AACA,QAAA,IAAA,EAAA,EAFA;AAEA;AACA,QAAA,IAAA,EAAA,EAHA;AAGA;AACA,QAAA,MAAA,EAAA,EAJA;AAIA;AACA,QAAA,KAAA,EAAA,EALA;AAKA;AACA,QAAA,KAAA,EAAA,EANA;AAMA;AACA,QAAA,QAAA,EAAA,EAPA;AAOA;AACA,QAAA,EAAA,EAAA,EARA;AAQA;AACA,QAAA,EAAA,EAAA,EATA;AASA;AACA,QAAA,EAAA,EAAA,EAVA;AAUA;AACA,QAAA,IAAA,EAAA,EAXA;AAWA;AACA,QAAA,IAAA,EAAA,EAZA;AAYA;AACA,QAAA,IAAA,EAAA,EAbA;AAaA;AACA,QAAA,IAAA,EAAA,EAdA;AAcA;AACA,QAAA,IAAA,EAAA,EAfA;AAeA;AACA,QAAA,IAAA,EAAA,EAhBA;AAgBA;AACA,QAAA,IAAA,EAAA,EAjBA;AAiBA;AACA,QAAA,IAAA,EAAA,EAlBA;AAkBA;AACA,QAAA,IAAA,EAAA,EAnBA;AAmBA;AACA,QAAA,EAAA,EAAA,EApBA;AAoBA;AACA;AACA,QAAA,IAAA,EAAA,EAtBA;AAsBA;AACA;AACA;AACA;AACA,QAAA,IAAA,EAAA;AA1BA,OAvCA;AAmEA,MAAA,IAAA,EAAA,CACA;AACA,QAAA,GAAA,EAAA,KADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,MAHA;AAIA,QAAA,KAAA,EAAA,EAJA;AAKA,QAAA,IAAA,EAAA,MALA;AAMA,QAAA,KAAA,EAAA,EANA;AAOA,QAAA,IAAA,EAAA,MAPA;AAQA,QAAA,KAAA,EAAA;AARA,OADA,EAWA;AACA,QAAA,GAAA,EAAA,MADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,MAHA;AAIA,QAAA,KAAA,EAAA,EAJA;AAKA,QAAA,IAAA,EAAA,MALA;AAMA,QAAA,KAAA,EAAA,EANA;AAOA,QAAA,IAAA,EAAA,MAPA;AAQA,QAAA,KAAA,EAAA;AARA,OAXA,EAqBA;AACA,QAAA,GAAA,EAAA,MADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,KAHA;AAIA,QAAA,KAAA,EAAA,EAJA;AAKA,QAAA,IAAA,EAAA,KALA;AAMA,QAAA,KAAA,EAAA,EANA;AAOA,QAAA,IAAA,EAAA,KAPA;AAQA,QAAA,KAAA,EAAA;AARA,OArBA,EA+BA;AACA,QAAA,GAAA,EAAA,MADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,SAHA;AAIA,QAAA,KAAA,EAAA,EAJA;AAKA,QAAA,IAAA,EAAA,WALA;AAMA,QAAA,KAAA,EAAA,EANA;AAOA,QAAA,IAAA,EAAA,MAPA;AAQA,QAAA,KAAA,EAAA;AARA,OA/BA,CAnEA;AA6GA,MAAA,QAAA,EAAA,IA7GA;AA8GA,MAAA,SAAA,EAAA,EA9GA;AA8GA;AACA;AACA,MAAA,oBAAA,EAAA,KAhHA;AAkHA,MAAA,qBAAA,EAAA,KAlHA;AAmHA,MAAA,MAAA,EAAA,EAnHA;AAoHA;AACA,MAAA,eAAA,EAAA;AACA,QAAA,EAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA;AAFA,OArHA;AAyHA;AACA,MAAA,QAAA,EAAA;AACA,QAAA,EAAA,EAAA;AADA,OA1HA;AA6HA;AACA,MAAA,QAAA,EAAA;AACA,QAAA,MAAA,EAAA;AADA,OA9HA;AAkIA;AACA,MAAA,QAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,CAnIA;AAiJA;AACA,MAAA,gBAAA,EAAA,KAlJA;AAmJA;AACA,MAAA,gBAAA,EAAA,KApJA;AAqJA;AACA,MAAA,kBAAA,EAAA,KAtJA;AAuJA;AACA,MAAA,IAAA,EAAA;AACA;AACA,QAAA,IAAA,EAAA,EAFA;AAGA;AACA,QAAA,MAAA,EAAA,EAJA;AAKA;AACA,QAAA,IAAA,EAAA,EANA;AAOA;AACA,QAAA,MAAA,EAAA,EARA;AASA;AACA,QAAA,IAAA,EAAA,EAVA;AAWA;AACA,QAAA,IAAA,EAAA,EAZA;AAaA,QAAA,MAAA,EAAA,EAbA;AAaA;AACA;AACA,QAAA,MAAA,EAAA,EAfA;AAgBA,QAAA,IAAA,EAAA,EAhBA;AAgBA;AACA,QAAA,IAAA,EAAA,EAjBA;AAiBA;AACA,QAAA,IAAA,EAAA,SAlBA;AAkBA;AACA,QAAA,IAAA,EAAA,EAnBA;AAmBA;AACA,QAAA,EAAA,EAAA,EApBA;AAoBA;AACA,QAAA,EAAA,EAAA,EArBA;AAqBA;AACA,QAAA,EAAA,EAAA,EAtBA;AAsBA;AACA,QAAA,EAAA,EAAA,EAvBA;AAuBA;AACA,QAAA,IAAA,EAAA,EAxBA;AAwBA;AACA,QAAA,MAAA,EAAA,EAzBA;AAyBA;AACA,QAAA,KAAA,EAAA,EA1BA;AA0BA;AACA,QAAA,KAAA,EAAA,EA3BA;AA2BA;AACA,QAAA,MAAA,EAAA,EA5BA;AA4BA;AACA,QAAA,EAAA,EAAA,EA7BA,CA6BA;;AA7BA,OAxJA;AAuLA,MAAA,aAAA,EAAA,EAvLA;AAuLA;AACA;AACA,MAAA,aAAA,EAAA,KAzLA;AA0LA;AACA,MAAA,MAAA,EAAA,IA3LA;AA4LA;AACA,MAAA,UAAA,EAAA,KA7LA;AA8LA,MAAA,YAAA,EAAA,KA9LA;AA+LA;AACA,MAAA,QAAA,EAAA,EAhMA;AAiMA;AACA,MAAA,QAAA,EAAA,EAlMA;AAmMA;AACA,MAAA,QAAA,EAAA,EApMA;AAqMA;AACA,MAAA,QAAA,EAAA,EAtMA;AAuMA;AACA,MAAA,IAAA,EAAA,EAxMA;AAyMA,MAAA,QAAA,EAAA,EAzMA;AA0MA,MAAA,OAAA,EAAA,EA1MA;AA2MA,MAAA,QAAA,EAAA,CA3MA;AA4MA,MAAA,QAAA,EAAA,CA5MA;AA6MA,MAAA,WAAA,EAAA,CA7MA;AA8MA,MAAA,MAAA,EAAA,EA9MA;AA+MA,MAAA,MAAA,EAAA,EA/MA;AAgNA;AACA,MAAA,MAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAJA,EAKA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OALA,EAMA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OANA,EAOA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAPA,EAQA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OARA,EASA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OATA,EAUA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAVA,CAjNA;AA6NA;AACA,MAAA,KAAA,EAAA,EA9NA;AA+NA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,IAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA,EAFA;AAGA,UAAA,MAAA,EAAA,EAHA;AAIA,UAAA,IAAA,EAAA,EAJA;AAKA,UAAA,OAAA,EAAA,EALA;AAMA,UAAA,IAAA,EAAA,EANA;AAOA,UAAA,IAAA,EAAA,EAPA;AAQA,UAAA,IAAA,EAAA,EARA;AASA,UAAA,IAAA,EAAA;AATA,SADA;AAYA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA,CACA;AAAA,YAAA,KAAA,EAAA,MAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WADA,EAEA;AAAA,YAAA,KAAA,EAAA,MAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAFA,EAGA;AAAA,YAAA,KAAA,EAAA,MAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAHA,CAJA;AASA,UAAA,SAAA,EAAA;AATA,SAFA,EAaA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAbA,EAcA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAdA,EAeA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,KAAA,EAAA,SAHA;AAIA,UAAA,QAAA,EAAA,WAJA;AAKA,UAAA,MAAA,EAAA;AALA,SAfA,EAsBA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAtBA,EAuBA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAvBA,EAwBA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAxBA,EAyBA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAzBA;AAZA,OA/NA;AAuQA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,QAAA,EAAA,EADA;AAEA,QAAA,OAAA,EAAA,CAFA,CAGA;;AAHA,OAxQA;AA6QA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SARA,EASA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SATA,EAUA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA;AAAA,SAVA,CAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhCA,SARA;AA0CA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AA1CA,OA7QA;AAyTA;AACA,MAAA,UAAA,EAAA,EA1TA;AA2TA,MAAA,GAAA,EAAA,EA3TA;AA4TA,MAAA,QAAA,EAAA,EA5TA;AA4TA;AACA,MAAA,KAAA,EAAA;AACA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAPA;AAQA;AACA,QAAA,IAAA,EAAA,CACA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,OAAA,EAAA,SAFA;AAGA,UAAA,SAAA,EAAA,YAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SADA,CATA;AAiBA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAjBA;AAkBA,QAAA,IAAA,EAAA,CACA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,OAAA,EAAA,SAFA;AAGA,UAAA,SAAA,EAAA,YAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SADA,CAlBA;AA0BA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AA1BA,OA7TA;AAyVA,MAAA,MAAA,EAAA,EAzVA;AAyVA;AACA,MAAA,YAAA,EAAA,KA1VA,CA0VA;;AA1VA,KAAA;AA4VA,GAvXA;AAyXA,EAAA,OAzXA,qBAyXA;AAAA;;AACA;AACA,SAAA,UAAA,GAFA,CAGA;;AACA,SAAA,uBAAA,GAJA,CAKA;;AACA,SAAA,OAAA,CAAA,KAAA,MAAA,CAAA,KAAA,EANA,CAOA;;AACA,QAAA,MAAA,GAAA;AACA,MAAA,OAAA,EAAA,EADA;AAEA,MAAA,QAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA;AAFA,KAAA;AAIA,8BAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,QAAA,MAAA,CAAA,UAAA,GAAA,IAAA;AACA;AACA,KAJA;AAKA,GA1YA;AA2YA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,eAFA,2BAEA,OAFA,EAEA;AACA,aAAA,KAAA,QAAA,KAAA,GAAA,GAAA,MAAA,aAAA,OAAA,OAAA;AACA,KAJA;AAKA;AACA,IAAA,YANA,wBAMA,GANA,EAMA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA;AACA,gBAAA,MAFA,GAEA,GAAA,CAAA,MAFA,EAGA;AACA;;AAJA;AAAA,uBAKA,2BAAA;AAAA,kBAAA,MAAA,EAAA,GAAA,CAAA;AAAA,iBAAA,CALA;;AAAA;AAAA;AAKA,gBAAA,SALA,wBAKA,IALA;AAMA,gBAAA,MAAA,CAAA,OAAA,GAAA,SAAA,CAAA,MAAA,CAAA,IAAA,CAAA,SAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CANA,CAOA;;AACA,4CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,MAAA,EAAA;AACA,sBAAA,SAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA;AACA,kBAAA,MAAA,CAAA,MAAA,GAAA,SAAA,CAAA,MAAA,CAAA,IAAA,CAAA,SAAA,EAAA,CAAA,CAAA,CAAA;AACA,kBAAA,MAAA,CAAA,YAAA,GAAA,IAAA;AACA,iBAJA;;AARA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA,KAnBA;AAoBA;AACA,IAAA,eArBA,6BAqBA;AACA,WAAA,YAAA,GAAA,KAAA;AACA,KAvBA;AAwBA;AACA,IAAA,WAzBA,uBAyBA,GAzBA,EAyBA,IAzBA,EAyBA;AACA,UAAA,IAAA,KAAA,QAAA,EAAA;AACA,aAAA,QAAA,CAAA,EAAA,GAAA,EAAA;AACA,aAAA,kBAAA,GAAA,IAAA;AACA;AACA,KA9BA;AAgCA;AACA,IAAA,cAjCA,0BAiCA,GAjCA,EAiCA;AACA,WAAA,KAAA,GAAA,QAAA,CADA,CAEA;;AACA,WAAA,MAAA,GAAA,KAAA,CAHA,CAIA;;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,YAAA,GAAA,IAAA,CANA,CAOA;;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,IAAA,mCAAA,GAAA,EATA,CAUA;;AACA,WAAA,uBAAA;AACA,KA7CA;AA8CA;AACA,IAAA,aA/CA,yBA+CA,GA/CA,EA+CA;AACA,WAAA,KAAA,GAAA,QAAA,CADA,CAEA;;AACA,WAAA,MAAA,GAAA,IAAA;AACA,WAAA,YAAA,GAAA,IAAA,CAJA,CAKA;;AACA,WAAA,UAAA,GAAA,KAAA,CANA,CAOA;;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,IAAA,mCAAA,GAAA,EATA,CAUA;;AACA,WAAA,uBAAA;AACA,KA3DA;AA4DA;AACA,IAAA,YA7DA,wBA6DA,GA7DA,EA6DA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,IAAA,mCAAA,GAAA;AACA,gBAAA,MAAA,CAAA,QAAA,GAAA,GAAA;AACA,gBAAA,MAAA,CAAA,UAAA,GAAA,IAAA,EAAA,MAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA;AACA,gBAAA,MAAA,CAAA,QAAA,GAAA,GAAA,CAAA,QAAA;AACA,gBAAA,MAAA,CAAA,MAAA,GAAA,GAAA,CAAA,MAAA;AACA,gBAAA,MAAA,CAAA,KAAA,GAAA,GAAA,CAAA,KAAA;AACA,gBAAA,MAAA,CAAA,QAAA,GAAA,GAAA;AACA,gBAAA,MAAA,CAAA,SAAA,GAAA,GAAA;AACA,gBAAA,MAAA,CAAA,QAAA,GAAA,GAAA,CAAA,KAAA;AACA,gBAAA,MAAA,CAAA,MAAA,GAAA,GAAA,CAAA,MAAA;AACA,gBAAA,MAAA,CAAA,MAAA,GAAA,GAAA,CAAA,MAAA;AACA,gBAAA,MAAA,CAAA,oBAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAbA,CAcA;;AACA,gBAAA,IAfA,GAeA,MAfA;AAAA;AAAA,uBAgBA,2BAAA;AAAA,kBAAA,MAAA,EAAA,GAAA,CAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,GAAA,GAAA,GAAA,CAAA,IAAA;AACA,8CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,MAAA,EAAA;AACA,wBAAA,SAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA;AACA,wBAAA,SAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA;AACA,wBAAA,GAAA,GAAA,EAAA;;AACA,yBAAA,IAAA,IAAA,IAAA,SAAA,EAAA;AACA,sBAAA,GAAA,CAAA,IAAA,mCAAA,IAAA,EAAA,SAAA,CAAA,IAAA,CAAA;AACA;;AACA,wBAAA,IAAA,GAAA,EAAA;;AACA,yBAAA,IAAA,GAAA,IAAA,SAAA,EAAA;AACA,sBAAA,IAAA,CAAA,IAAA,mCAAA,GAAA,EAAA,SAAA,CAAA,GAAA,CAAA;AACA;;AACA,oBAAA,IAAA,CAAA,UAAA,CAAA,GAAA,CAAA,MAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,EAAA,GAAA,EAAA,EAAA,EAAA,UAAA;AACA,oBAAA,IAAA,CAAA,UAAA,CAAA,GAAA,CAAA,MAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAA,UAAA;AACA,mBAbA;AAcA,iBAhBA,CAhBA;;AAAA;AAiCA,gBAAA,MAAA,CAAA,IAAA,mCAAA,GAAA;;AAjCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkCA,KA/FA;AAiGA;AACA,IAAA,QAlGA,oBAkGA,GAlGA,EAkGA;AAAA;;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,QAAA,GAAA,GAAA;AACA,WAAA,IAAA,GAAA,GAAA,CAAA,IAAA;AACA,WAAA,KAAA,GAAA,GAAA,CAAA,KAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,QAAA,GAAA,GAAA,CAAA,QAAA;AACA,WAAA,MAAA,GAAA,GAAA,CAAA,MAAA;AACA,WAAA,KAAA,GAAA,GAAA,CAAA,KAAA;AACA,WAAA,QAAA,GAAA,GAAA;AACA,WAAA,SAAA,GAAA,GAAA;AACA,WAAA,QAAA,GAAA,GAAA,CAAA,KAAA;AACA,WAAA,MAAA,GAAA,GAAA,CAAA,MAAA;AACA,WAAA,MAAA,GAAA,GAAA,CAAA,MAAA;AACA,WAAA,oBAAA,GAAA,IAAA;AACA,WAAA,SAAA,GAAA,GAAA,CAAA,IAAA;AACA,UAAA,MAAA,GAAA,QAAA,CAAA,cAAA,CAAA,QAAA,CAAA;AACA,MAAA,MAAA,IAAA,MAAA,CAAA,MAAA,EAAA;AACA,UAAA,MAAA,GAAA,QAAA,CAAA,cAAA,CAAA,QAAA,CAAA;AACA,MAAA,MAAA,IAAA,MAAA,CAAA,MAAA,EAAA,CAnBA,CAoBA;;AACA,UAAA,IAAA,GAAA,IAAA;AACA,iCAAA;AAAA,QAAA,MAAA,EAAA,GAAA,CAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,GAAA,GAAA,CAAA,IAAA;AACA,oCAAA,GAAA,EAAA,IAAA,CAAA,UAAA,MAAA,EAAA;AACA,cAAA,SAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA;AACA,cAAA,SAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA;AACA,cAAA,GAAA,GAAA,EAAA;;AACA,eAAA,IAAA,IAAA,IAAA,SAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,mCAAA,IAAA,EAAA,SAAA,CAAA,IAAA,CAAA;AACA;;AACA,cAAA,IAAA,GAAA,EAAA;;AACA,eAAA,IAAA,GAAA,IAAA,SAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,mCAAA,GAAA,EAAA,SAAA,CAAA,GAAA,CAAA;AACA;;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,GAAA,CAAA,MAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,EAAA,GAAA,EAAA,EAAA,EAAA,UAAA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,GAAA,CAAA,MAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAA,UAAA;AACA,SAbA;AAcA,OAhBA;AAiBA,MAAA,UAAA,qFAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,QAAA;;AADA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAAA,IAEA,IAFA,CAAA;AAGA,KA5IA;AA8IA,IAAA,UA9IA,sBA8IA,OA9IA,EA8IA,OA9IA,EA8IA,GA9IA,EA8IA,QA9IA,EA8IA,YA9IA,EA8IA;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,OAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,YAAA,EAAA,GAAA,OAAA,CAAA,CAAA,CAAA,CAAA,GAAA;AACA,YAAA,EAAA,GAAA,OAAA,CAAA,CAAA,CAAA,CAAA,GAAA;AACA,YAAA,IAAA,GAAA,OAAA,CAAA,CAAA,CAAA;;AACA,aAAA,IAAA,IAAA,IAAA,IAAA,EAAA;AACA;AACA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,EAAA,EAAA,CAAA,EAAA,EAAA;AACA;AACA,gBAAA,IAAA,GAAA,yBAAA;;AACA,iBAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,IAAA,CAAA,IAAA,CAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA;AACA,kBAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,QAAA,EAAA;AACA,oBAAA,IAAA,GAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,IAAA;AACA,oBAAA,IAAA,GAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAFA,CAEA;;AACA,oBAAA,KAAA,GAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,KAAA;AACA,oBAAA,GAAA,GAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,IAAA;AACA,oBAAA,EAAA,GAAA,EAAA;;AACA,oBAAA,IAAA,KAAA,IAAA,EAAA;AACA,uBAAA,MAAA,GAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CADA,CACA;AACA;;AACA,oBAAA,IAAA,IAAA,QAAA,EAAA;AACA;AACA,sBAAA,CAAA,GAAA,EAAA;AACA,oBAAA,GAAA,GAAA,EAAA;AACA;;AACA,kBAAA,EAAA,GACA,kFACA,QADA,GAEA,MAFA,GAGA,KAHA,GAIA,SAJA,GAKA,GALA,GAMA,GAPA;AAQA,iBAbA,MAaA;AACA,kBAAA,EAAA,GAAA,IAAA;AACA;;AACA,oBAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,OAAA,IAAA,GAAA,EAAA;AACA;AACA,sBAAA,KAAA,MAAA,IAAA,KAAA,MAAA,CAAA,KAAA,KAAA,KAAA,EAAA;AACA;AACA,wBAAA,KAAA,IAAA,CAAA,IAAA,IAAA,CAAA,IAAA,KAAA,IAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA;AACA,sBAAA,IAAA,IACA,gCACA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,OADA,GAEA,aAFA,GAGA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,OAHA,GAIA,KAJA,GAKA,EALA,GAMA,OAPA;AAQA,2BAAA,YAAA,GAAA,KAAA;AACA,qBAXA,MAWA;AACA;AACA,sBAAA,IAAA,IACA,gCACA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,OADA,GAEA,aAFA,GAGA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,OAHA,GAIA,QAJA,GAKA,KALA,GAMA,kBANA,GAOA,IAPA,GAQA,GARA,GASA,OAVA;AAWA,2BAAA,YAAA,GAAA,IAAA;AACA;AACA,mBA5BA,MA4BA;AACA,oBAAA,IAAA,IACA,gCACA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,OADA,GAEA,aAFA,GAGA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,OAHA,GAIA,KAJA,GAKA,EALA,GAMA,OAPA;AAQA;AACA,iBAxCA,MAwCA;AACA,kBAAA,IAAA,IAAA,uBAAA,EAAA,GAAA,OAAA;AACA;AACA;AACA;;AACA,YAAA,IAAA,IAAA,OAAA;AACA,YAAA,GAAA,IAAA,IAAA;AACA;AACA;AACA;;AACA,UAAA,YAAA,EAAA;AACA,eAAA,GAAA;AACA;;AACA,UAAA,QAAA,KAAA,UAAA,EAAA;AACA,aAAA,SAAA,GAAA,GAAA;AACA,OAFA,MAEA,IAAA,QAAA,KAAA,UAAA,EAAA;AACA,aAAA,SAAA,GAAA,GAAA;AACA,OAFA,MAEA;AACA,aAAA,QAAA,IAAA,GAAA;AACA;AACA,KA/OA;AAgPA;AACA,IAAA,aAjPA,2BAiPA;AACA,WAAA,oBAAA,GAAA,KAAA;AACA,KAnPA;AAqPA,IAAA,KArPA,mBAqPA;AACA,UAAA,IAAA,GAAA,IAAA;;AACA,UAAA,CAAA,IAAA,CAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,QAAA,IAAA,CAAA,QAAA,CAAA,OAAA,CAAA,eAAA;AACA;AACA;;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,CAAA;AACA,UAAA,UAAA,GAAA,IAAA,CAAA,UAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,QAAA;AACA,MAAA,IAAA,CAAA,OAAA,GAAA,UAAA,CAAA,GAAA,CAAA,UAAA,CAAA,EAAA,CAAA,EAAA;AACA,mCAAA;AAAA,UAAA,MAAA,EAAA,CAAA,CAAA;AAAA,SAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,GAAA,GAAA,GAAA,CAAA,IAAA;AACA,sCAAA,CAAA,EAAA,IAAA,CAAA,UAAA,MAAA,EAAA;AACA,gBAAA,SAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA;AACA,gBAAA,SAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA;AACA,gBAAA,GAAA,GAAA,EAAA;;AACA,iBAAA,IAAA,IAAA,IAAA,SAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,mCAAA,IAAA,EAAA,SAAA,CAAA,IAAA,CAAA;AACA;;AACA,gBAAA,IAAA,GAAA,EAAA;;AACA,iBAAA,IAAA,GAAA,IAAA,SAAA,EAAA;AACA,cAAA,IAAA,CAAA,IAAA,mCAAA,GAAA,EAAA,SAAA,CAAA,GAAA,CAAA;AACA;;AACA,YAAA,CAAA,CAAA,SAAA,GAAA,IAAA,CAAA,UAAA,CACA,GAAA,CAAA,MAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CADA,EAEA,GAFA,EAGA,EAHA,EAIA,EAJA,EAKA,IALA,CAAA;AAOA,YAAA,CAAA,CAAA,SAAA,GAAA,IAAA,CAAA,UAAA,CACA,GAAA,CAAA,MAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CADA,EAEA,IAFA,EAGA,EAHA,EAIA,EAJA,EAKA,IALA,CAAA;AAOA,YAAA,CAAA,CAAA,IAAA,GAAA,SAAA;AACA,WA1BA;AA2BA,SA7BA,EADA,CA+BA;AACA;;AACA,eAAA,CAAA;AACA,OAlCA,CAAA;AAmCA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,OAAA,EAAA,QAAA;AACA,MAAA,IAAA,CAAA,qBAAA,GAAA,IAAA;AACA,MAAA,IAAA,CAAA,QAAA,GAAA,EAAA,CA9CA,CA+CA;;AACA,UAAA,MAAA,GAAA,IAAA,CAAA,OAAA,CAAA,MAAA;AACA,MAAA,UAAA,qFAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,IAAA,CAAA,KAAA,mBAAA,MAAA,GAAA,CAAA,MACA,IAAA,CAAA,KAAA,mBAAA,MAAA,GAAA,CAAA,GAAA,CAAA,EAAA,SAAA,CAAA,YAAA;AACA,kBAAA,UAAA,qFAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mCACA,OAAA,CAAA,GAAA,CACA,IAAA,CAAA,OAAA,CAAA,GAAA;AAAA,gHAAA,kBAAA,CAAA,EAAA,CAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,wCAAA,OAAA,CAAA,GAAA,CAAA,CAAA,EAAA,QAAA;AADA;AAAA,+CAEA,IAAA,CAAA,SAAA,mBACA,CADA,GAEA,IAFA,YAGA,CAAA,CAAA,IAHA,cAGA,CAHA,GAIA,IAJA,EAKA,CAAA,CAAA,IALA,CAFA;;AAAA;AAEA,wCAAA,MAFA;AASA,wCAAA,IAAA,CAAA,QAAA,GAAA,CAAA,CAAA,CAAA,GAAA,IAAA,MAAA,IAAA,GAAA,EAAA,OAAA,CAAA,CAAA,CAAA;AATA,0EAUA,MAVA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,+BAAA;;AAAA;AAAA;AAAA;AAAA,gCADA,CADA;;AAAA;AACA,4BAAA,MADA;;AAeA,+CAAA,SAAA,CACA,MADA,EAEA,UAAA,IAAA,IAAA,GAAA,OAAA,EAFA,EAGA,UAAA,IAAA,EAAA,CAAA,EAAA;AACA,8BAAA,IAAA,CAAA,WAAA,GAAA,CAAA,CAAA,CAAA,GAAA,IAAA,MAAA,IAAA,GAAA,EAAA,OAAA,CAAA,CAAA,CAAA;AACA,6BALA;;AAOA,4BAAA,OAAA,CAAA,GAAA,CAAA,KAAA,EAAA,IAAA;AACA,4BAAA,IAAA,CAAA,QAAA,GAAA,GAAA;;AAvBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAA,IAwBA,IAxBA,CAAA;AAyBA,iBA1BA,CADA;AA4BA,gBAAA,IAAA,CAAA,QAAA,GAAA,EAAA,CA7BA,CA8BA;;AA9BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAAA,IA+BA,IA/BA,CAAA;AAgCA,KAtUA;AAuUA;AACA,IAAA,SAxUA,uBAwUA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAAA,QAAA,0EAAA,UAAA;AAAA,gBAAA,QAAA;AAAA,gBAAA,IAAA;AAAA,gBAAA,IAAA,0EAAA,KAAA;AAAA,gBAAA,IAAA;AACA,gBAAA,QADA,GACA,MAAA,CAAA,KAAA,CAAA,QAAA,CADA;;AAEA,oBAAA,QAAA,YAAA,KAAA,EAAA;AACA,kBAAA,QAAA,GAAA,QAAA,CAAA,CAAA,CAAA;AACA;;AACA,gBAAA,OALA,GAKA,QAAA,CAAA,GALA;AAOA,gBAAA,OAAA,CAAA,KAAA,CAAA,QAAA,GAAA,UAAA;AACA,gBAAA,OARA,GAQA,EARA,EASA;;AATA,qBAUA,IAVA;AAAA;AAAA;AAAA;;AAAA,uDAWA,IAXA;;AAAA;AAAA;AAAA;AAAA;AAAA;;AAWA,gBAAA,IAXA;AAYA,gBAAA,CAZA,GAYA,CAZA;;AAAA;AAAA,sBAYA,CAAA,GAAA,IAAA,CAAA,IAAA,CAAA,CAAA,MAZA;AAAA;AAAA;AAAA;;AAAA,sBAaA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,IAAA,KAAA,IAbA;AAAA;AAAA;AAAA;;AAcA,gBAAA,OAAA,GAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,GAAA,CAAA,EAAA,IAAA;AAdA;;AAAA;AAYA,gBAAA,CAAA,EAZA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAoBA,gBAAA,IApBA,GAoBA,EApBA;AAqBA,gBAAA,IArBA,GAqBA,EArBA;AAsBA,gBAAA,KAtBA,GAsBA,QAAA,CAAA,cAAA,CAAA,MAAA,CAAA,MAAA,CAAA,KAAA,CAtBA;;AAuBA,oBAAA,KAAA,EAAA;AACA,kBAAA,OAAA,GAAA,MAAA,CAAA,YAAA,GAAA,KAAA,CAAA,SAAA,GAAA,KAAA,CAAA,KAAA;AACA;;AAzBA;AA4BA,oBAAA,QAAA,IAAA,CAAA,OAAA,CAAA,EAAA;AACA,kBAAA,IAAA,GAAA,sBAAA;AACA,kBAAA,IAAA,GAAA,sBAAA;AACA,iBAHA,MAGA,IAAA,OAAA,IAAA,CAAA,OAAA,CAAA,EAAA;AACA,kBAAA,IAAA,GAAA,wBAAA;AACA,kBAAA,IAAA,GAAA,sBAAA;AACA,iBAlCA,CAmCA;;;AACA,gBAAA,OApCA,GAoCA,QAAA,CAAA,aAAA,CAAA,KAAA,CApCA;AAqCA,gBAAA,OAAA,CAAA,KAAA,CAAA,QAAA,GAAA,UAAA;AACA,gBAAA,OAAA,CAAA,EAAA,GAAA,QAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,MAAA,GAAA,GAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,GAAA,GAAA,GAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA,GAAA,MAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,SAAA,GAAA,MAAA;AACA,gBAAA,YA5CA,GA4CA,QAAA,CAAA,aAAA,CAAA,KAAA,CA5CA;AA6CA,gBAAA,YAAA,CAAA,YAAA,CAAA,KAAA,EAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,YAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,OAAA,EA/CA,CAgDA;;AACA,gBAAA,OAjDA,GAiDA,QAAA,CAAA,aAAA,CAAA,KAAA,CAjDA;AAkDA,gBAAA,OAAA,CAAA,KAAA,CAAA,QAAA,GAAA,UAAA;AACA,gBAAA,OAAA,CAAA,EAAA,GAAA,QAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,MAAA,GAAA,GAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,GAAA,GAAA,GAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA,GAAA,OAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,SAAA,GAAA,MAAA;AACA,gBAAA,YAzDA,GAyDA,QAAA,CAAA,aAAA,CAAA,KAAA,CAzDA;AA0DA,gBAAA,YAAA,CAAA,YAAA,CAAA,KAAA,EAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,YAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,OAAA;AA5DA;AAAA,uBA8DA,mBAAA,WAAA,CAAA,OAAA,EAAA,YAAA,CA9DA;;AAAA;AAAA;AAAA,uBA+DA,mBAAA,WAAA,CAAA,OAAA,EAAA,IAAA,IAAA,MAAA,CAAA,SAAA,CA/DA;;AAAA;AA+DA,gBAAA,GA/DA;;AAAA,qBAgEA,QAhEA;AAAA;AAAA;AAAA;;AAAA,kDAiEA,GAjEA;;AAAA;AAmEA,gBAAA,GAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA,IAAA;;AACA,gBAAA,MAAA,CAAA,aAAA,GApEA,CAqEA;;;AACA,gBAAA,MAtEA,GAsEA,QAAA,CAAA,cAAA,CAAA,QAAA,CAtEA;AAuEA,gBAAA,MAAA,IAAA,MAAA,CAAA,MAAA,EAAA;AACA,gBAAA,MAxEA,GAwEA,QAAA,CAAA,cAAA,CAAA,QAAA,CAxEA;AAyEA,gBAAA,MAAA,IAAA,MAAA,CAAA,MAAA,EAAA;;AAzEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA0EA,KAlZA;AAmZA,IAAA,QAnZA,sBAmZA;AACA,UAAA,QAAA,GAAA,KAAA,KAAA,CAAA,UAAA,CAAA;;AACA,UAAA,QAAA,YAAA,KAAA,EAAA;AACA,QAAA,QAAA,GAAA,QAAA,CAAA,CAAA,CAAA;AACA;;AACA,UAAA,OAAA,GAAA,QAAA,CAAA,GAAA;AAEA,MAAA,OAAA,CAAA,KAAA,CAAA,QAAA,GAAA,UAAA;AACA,UAAA,OAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,EAAA;AACA,UAAA,KAAA,GAAA,QAAA,CAAA,cAAA,CAAA,KAAA,MAAA,CAAA,KAAA,CAAA;;AACA,UAAA,KAAA,EAAA;AACA,QAAA,OAAA,GAAA,KAAA,YAAA,GAAA,KAAA,CAAA,SAAA,GAAA,KAAA,CAAA,KAAA;AACA;;AACA,UAAA,QAAA,IAAA,CAAA,OAAA,CAAA,EAAA;AACA,QAAA,IAAA,GAAA,sBAAA;AACA,QAAA,IAAA,GAAA,sBAAA;AACA,OAHA,MAGA,IAAA,OAAA,IAAA,CAAA,OAAA,CAAA,EAAA;AACA,QAAA,IAAA,GAAA,wBAAA;AACA,QAAA,IAAA,GAAA,sBAAA;AACA,OAHA,MAGA;AACA,eAAA,KAAA;AACA,OAvBA,CAwBA;;;AACA,UAAA,OAAA,GAAA,QAAA,CAAA,aAAA,CAAA,KAAA,CAAA;AACA,MAAA,OAAA,CAAA,KAAA,CAAA,QAAA,GAAA,UAAA;AACA,MAAA,OAAA,CAAA,EAAA,GAAA,QAAA;AACA,MAAA,OAAA,CAAA,KAAA,CAAA,MAAA,GAAA,GAAA;AACA,MAAA,OAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA;AACA,MAAA,OAAA,CAAA,KAAA,CAAA,GAAA,GAAA,GAAA;AACA,MAAA,OAAA,CAAA,KAAA,CAAA,WAAA,GAAA,MAAA;AACA,MAAA,OAAA,CAAA,KAAA,CAAA,SAAA,GAAA,MAAA;AACA,UAAA,YAAA,GAAA,QAAA,CAAA,aAAA,CAAA,KAAA,CAAA;AACA,MAAA,YAAA,CAAA,YAAA,CAAA,KAAA,EAAA,IAAA;AACA,MAAA,OAAA,CAAA,WAAA,CAAA,YAAA;AACA,MAAA,OAAA,CAAA,WAAA,CAAA,OAAA,EApCA,CAqCA;;AACA,UAAA,OAAA,GAAA,QAAA,CAAA,aAAA,CAAA,KAAA,CAAA;AACA,MAAA,OAAA,CAAA,KAAA,CAAA,QAAA,GAAA,UAAA;AACA,MAAA,OAAA,CAAA,EAAA,GAAA,QAAA;AACA,MAAA,OAAA,CAAA,KAAA,CAAA,MAAA,GAAA,GAAA;AACA,MAAA,OAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA;AACA,MAAA,OAAA,CAAA,KAAA,CAAA,GAAA,GAAA,GAAA;AACA,MAAA,OAAA,CAAA,KAAA,CAAA,WAAA,GAAA,OAAA;AACA,MAAA,OAAA,CAAA,KAAA,CAAA,SAAA,GAAA,MAAA;AACA,UAAA,YAAA,GAAA,QAAA,CAAA,aAAA,CAAA,KAAA,CAAA;AACA,MAAA,YAAA,CAAA,YAAA,CAAA,KAAA,EAAA,IAAA;AACA,MAAA,OAAA,CAAA,WAAA,CAAA,YAAA;AACA,MAAA,OAAA,CAAA,WAAA,CAAA,OAAA;AACA,KArcA;AAscA;AACA,IAAA,YAvcA,0BAucA;AAAA;;AACA,WAAA,SAAA,CAAA,YAAA;AACA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,mBAAA,OAAA,CAAA;AACA,UAAA,IAAA,EAAA,IADA;AACA;AACA,UAAA,IAAA,EAAA,WAFA;AAEA;AACA,UAAA,OAAA,EAAA,iBAHA;AAGA;AACA,UAAA,UAAA,EAAA,oBAJA;AAIA;AACA,UAAA,MAAA,EAAA,QAAA,CAAA,aAAA,CAAA,cAAA;AALA,SAAA,CAAA;AAOA,OATA,EADA,CAWA;;AACA,UAAA,MAAA,GAAA,KAAA,KAAA,CAAA,QAAA,CAAA,SAAA,EAAA;AACA,mCAAA;AACA,QAAA,MAAA,EAAA,KAAA,QAAA,CAAA,MADA;AAEA,QAAA,KAAA,EAAA,KAAA,QAAA,CAAA,KAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA;AACA,YAAA,IAAA,EAAA,SADA;AAEA,YAAA,OAAA,EAAA;AAFA,WAAA;AAIA;AACA,OAXA,EAbA,CAyBA;;AACA,gCAAA,KAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,YAAA,MAAA,CAAA,OAAA,CAAA,KAAA;AACA,WAHA;;AAIA,UAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,OAAA;;AACA,UAAA,MAAA,CAAA,aAAA,GAAA,KAAA;AACA,UAAA,MAAA,CAAA,oBAAA,GAAA,KAAA;;AACA,UAAA,MAAA,CAAA,OAAA;AACA;AACA,OAXA;AAYA,KA7eA;AA8eA;AACA,IAAA,OA/eA,mBA+eA,MA/eA,EA+eA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,MAAA,CAAA,UAAA,+DAAA,MAAA,CAAA,UAAA,GAAA,MAAA;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,OAAA,GAAA,IAAA;AACA,gBAAA,KAJA,+DAIA,MAAA,CAAA,UAJA,GAIA,MAJA;AAAA;AAAA,uBAKA,iCAAA,KAAA,CALA;;AAAA;AAAA;AAKA,gBAAA,IALA,yBAKA,IALA;AAKA,gBAAA,IALA,yBAKA,IALA;;AAMA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;;AACA,kBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,oBAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA,mBAHA;AAIA;;AAbA;AAAA;;AAAA;AAAA;AAAA;;AAeA,gBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA,iBAHA;;AAfA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoBA,KAngBA;AAogBA;AACA,IAAA,iBArgBA,+BAqgBA;AACA,UAAA,KAAA,IAAA,CAAA,IAAA,KAAA,EAAA,IAAA,KAAA,IAAA,CAAA,IAAA,KAAA,SAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,SAAA;AACA;AACA;;AACA,WAAA,gBAAA,GAAA,IAAA;AACA,KA3gBA;AA4gBA;AACA,IAAA,kBA7gBA,8BA6gBA,MA7gBA,EA6gBA;AACA;AACA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,QAAA,EAAA,MAAA;AACA,WAAA,IAAA,CAAA,KAAA,IAAA,EAAA,MAAA,EAAA,MAAA,CAAA,IAAA,EAJA,CAKA;;AACA,WAAA,IAAA,CAAA,MAAA,GAAA,MAAA,CAAA,KAAA;AACA,KAphBA;AAqhBA;AACA,IAAA,6BAthBA,yCAshBA,MAthBA,EAshBA;AACA,WAAA,gBAAA,GAAA,MAAA;AACA,KAxhBA;AAyhBA;AACA,IAAA,kBA1hBA,8BA0hBA,MA1hBA,EA0hBA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,QAAA,EAAA,MAAA;AACA,UAAA,KAAA,GAAA,EAAA;AACA,UAAA,KAAA,GAAA,EAAA;AACA,MAAA,MAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,KAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA;AACA,QAAA,KAAA,CAAA,IAAA,CAAA,IAAA,CAAA,KAAA;AACA,OAHA;AAIA,WAAA,IAAA,CAAA,IAAA,GAAA,KAAA,CAAA,IAAA,CAAA,GAAA,CAAA;AACA,WAAA,IAAA,CAAA,MAAA,GAAA,KAAA,CAAA,IAAA,CAAA,GAAA,CAAA;AACA,WAAA,QAAA,CAAA,MAAA,GAAA,MAAA,CAAA,CAAA,CAAA,CAAA,MAAA;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,MAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,MAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAZA,CAaA;AACA;AACA;AACA;AACA;AACA;AACA,KA7iBA;AA8iBA;AACA,IAAA,6BA/iBA,yCA+iBA,MA/iBA,EA+iBA;AACA,WAAA,gBAAA,GAAA,MAAA;AACA,KAjjBA;AAkjBA;AACA,IAAA,uBAnjBA,qCAmjBA;AAAA;;AACA,UAAA,QAAA,GAAA,MAAA;AACA,2CAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA,KAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,KAAA,IAAA,EAAA;AACA,gBAAA,IAAA,GAAA,EAAA;AACA,YAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA,YAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,SAPA;AAQA,OATA;AAUA,KA/jBA;AAgkBA;AACA,IAAA,oBAjkBA,gCAikBA,QAjkBA,EAikBA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,QAAA;;AACA,UAAA,KAAA,QAAA,CAAA,EAAA,KAAA,EAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,kBAAA,EAAA,KAAA;AACA,YAAA,MAAA,GAAA,QAAA,CAAA,iBAAA,CAAA,QAAA,CAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,QAAA,EAAA,MAAA;;AACA,YAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,CAAA,CAAA,CAAA,KAAA,GAAA,QAAA,CAAA,KAAA;AACA,eAAA,UAAA,CAAA,MAAA,GAAA,QAAA,CAAA,EAAA;AACA;AACA,OAVA,CAWA;;;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,QAAA,CAAA,KAAA;AACA,WAAA,SAAA,CAAA,IAAA,GAAA,QAAA,CAAA,KAAA,CAbA,CAcA;;AACA,WAAA,IAAA,CAAA,MAAA,GAAA,QAAA,CAAA,EAAA;AACA,WAAA,IAAA,CAAA,KAAA,IAAA,EAAA,MAAA,EAAA,EAAA;AACA,KAllBA;AAmlBA;AACA,IAAA,qBAplBA,iCAolBA,iBAplBA,EAolBA;AACA,WAAA,kBAAA,GAAA,iBAAA;AACA,KAtlBA;AAulBA;AACA,IAAA,UAxlBA,wBAwlBA;AAAA;;AACA,4CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;;AACA,QAAA,OAAA,CAAA,UAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,IAAA,MAAA,EAAA;AACA,YAAA,IAAA,CAAA,OAAA,GAAA,OAAA,CAAA,QAAA;AACA;AACA,SAJA;AAKA,OAPA;AAQA,iCAAA,WAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,UAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,IAAA,MAAA,EAAA;AACA,YAAA,IAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA,UAAA,CAAA,EAAA;AACA,cAAA,CAAA,CAAA,KAAA,GAAA,CAAA,CAAA,QAAA;AACA,qBAAA,CAAA;AACA,aAHA,CAAA;AAIA;AACA,SAPA;AAQA,OATA;AAUA,KA3mBA;AA4mBA;AACA,IAAA,sBA7mBA,oCA6mBA;AAAA;;AACA;AACA,WAAA,IAAA,CAAA,KAAA,IAAA,EAAA,QAAA,EAAA,EAAA;AACA,WAAA,QAAA,GAAA,EAAA;AACA,UAAA,MAAA,GAAA,KAAA,IAAA,CAAA,MAAA;AACA,0CAAA;AAAA,QAAA,MAAA,EAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA,QAAA,OAAA,CAAA,QAAA,GAAA,QAAA,CAAA,IAAA;AACA,OAFA;AAGA,KArnBA;AAsnBA;AACA,IAAA,uBAvnBA,qCAunBA;AAAA;;AACA,WAAA,QAAA,GAAA,EAAA;AACA,UAAA,MAAA,GAAA,KAAA,IAAA,CAAA,MAAA;AACA,0CAAA;AAAA,QAAA,MAAA,EAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA,QAAA,OAAA,CAAA,QAAA,GAAA,QAAA,CAAA,IAAA;AACA,OAFA;AAGA,KA7nBA;AA+nBA;AACA,IAAA,UAhoBA,sBAgoBA,GAhoBA,EAgoBA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,IAAA,CAAA,OAAA,CAAA,IAAA,EAAA,MAAA,EAAA,EAAA;;AACA,gBAAA,OAFA,GAEA,EAFA;AAGA,gBAAA,OAAA,CAAA,QAAA,GAAA,EAAA;AAHA,+BAIA,GAJA;AAAA,kDAKA,MALA,wBAcA,MAdA,yBAkBA,MAlBA;AAAA;;AAAA;AAAA;AAAA,uBAMA,wBAAA,EAAA,CANA;;AAAA;AAMA,gBAAA,OANA;AAOA,gBAAA,OAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA,KAAA,EAAA;AACA,sBAAA,GAAA,GAAA,EAAA;AACA,kBAAA,GAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA,kBAAA,GAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;;AACA,kBAAA,OAAA,CAAA,QAAA,CAAA,IAAA,CAAA,GAAA;AACA,iBALA;AAPA;;AAAA;AAAA;AAAA,uBAeA,6BAAA,EAAA,CAfA;;AAAA;AAAA;AAeA,gBAAA,IAfA,yBAeA,IAfA;AAgBA,gBAAA,OAAA,CAAA,QAAA,GAAA,IAAA;AAhBA;;AAAA;AAAA;AAAA,uBAmBA,OAAA,CAAA,cAAA,EAnBA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsBA,KAtpBA;AAwpBA,IAAA,QAxpBA,oBAwpBA,GAxpBA,EAwpBA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,WAAA,IAAA,CAAA,KAAA,IAAA,EAAA,MAAA,EAAA,GAAA,CAAA,KAAA;AACA,WAAA,IAAA,CAAA,KAAA,IAAA,EAAA,QAAA,EAAA,GAAA,CAAA,KAAA;AACA,KA5pBA;AA8pBA;AACA,IAAA,cA/pBA,4BA+pBA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,QAAA,GAAA,EAAA;AADA;AAAA,uBAEA,2BAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,OAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,CAFA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,KApqBA;AAsqBA;AACA,IAAA,WAvqBA,yBAuqBA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,QAAA;AACA,WAAA,IAAA,CAAA,KAAA,IAAA,EAAA,MAAA,EAAA,EAAA;AACA,KA1qBA;AA4qBA;AACA,IAAA,WA7qBA,yBA6qBA;AACA,UAAA,KAAA,IAAA,CAAA,IAAA,KAAA,EAAA,IAAA,KAAA,IAAA,CAAA,IAAA,KAAA,SAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,SAAA;AACA;AACA;;AACA,UAAA,KAAA,IAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,aAAA,QAAA,CAAA,EAAA,GAAA,GAAA;AACA,OAFA,MAEA,IAAA,KAAA,IAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,aAAA,QAAA,CAAA,EAAA,GAAA,GAAA;AACA,OAFA,MAEA,IAAA,KAAA,IAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,aAAA,QAAA,CAAA,EAAA,GAAA,GAAA;AACA;;AACA,WAAA,kBAAA,GAAA,IAAA;AACA,KA1rBA;AA2rBA;AACA,IAAA,iBA5rBA,+BA4rBA;AACA;AACA,WAAA,IAAA,CAAA,KAAA,IAAA,EAAA,MAAA,EAAA,EAAA;;AACA,UAAA,KAAA,IAAA,CAAA,IAAA,KAAA,EAAA,IAAA,KAAA,IAAA,CAAA,IAAA,KAAA,SAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,SAAA;AACA;AACA;;AACA,UAAA,KAAA,IAAA,CAAA,IAAA,KAAA,EAAA,IAAA,KAAA,IAAA,CAAA,IAAA,KAAA,SAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,SAAA;AACA;AACA;;AACA,UAAA,KAAA,IAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,aAAA,eAAA,CAAA,EAAA,GAAA,IAAA;AACA,aAAA,eAAA,CAAA,IAAA,GAAA,IAAA;AACA,OAHA,MAGA,IAAA,KAAA,IAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,aAAA,eAAA,CAAA,EAAA,GAAA,IAAA;AACA,OAFA,MAEA,IAAA,KAAA,IAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,aAAA,eAAA,CAAA,EAAA,GAAA,IAAA;AACA;;AACA,WAAA,eAAA,CAAA,IAAA,GAAA,KAAA,IAAA,CAAA,IAAA,CAnBA,CAmBA;;AACA,WAAA,gBAAA,GAAA,IAAA;AACA,KAjtBA;AAktBA;AACA,IAAA,QAntBA,oBAmtBA,IAntBA,EAmtBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAA,UAAA,GAAA,EAAA;AACA,KA3tBA;AA4tBA;AACA,IAAA,WA7tBA,uBA6tBA,GA7tBA,EA6tBA,IA7tBA,EA6tBA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAEA,GAAA,CAAA,KAAA,KAAA,MAAA,IAAA,GAAA,CAAA,KAAA,IAAA,GAAA,CAAA,KAAA,KAAA,EAFA;AAAA;AAAA;AAAA;;AAGA,gBAAA,OAAA,CAAA,IAAA,CAAA,IAAA,EAAA,QAAA,EAAA,EAAA;;AAHA;AAAA,uBAIA,OAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAJA;;AAAA;AAKA,gBAAA,OAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,sBAAA,IAAA,CAAA,KAAA,IAAA,QAAA,EAAA;AACA,oBAAA,OAAA,CAAA,GAAA,CAAA,gBAAA,EAAA,OAAA,CAAA,QAAA;AACA,2BAAA,IAAA,CAAA,OAAA,GAAA,OAAA,CAAA,QAAA;AACA;AACA,iBALA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,KAzuBA;AA0uBA;AACA,IAAA,SA3uBA,uBA2uBA;AACA;AACA,WAAA,QAAA,GAAA,EAAA;AACA,WAAA,KAAA,GAAA,QAAA,CAHA,CAIA;;AACA,WAAA,UAAA,GAAA,KAAA,CALA,CAMA;;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,MAAA,GAAA,IAAA,CARA,CAUA;;AACA,WAAA,IAAA,GAAA,EAAA;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,CAAA;AACA,KAxvBA;AA0vBA,IAAA,YA1vBA,0BA0vBA,CAEA,CA5vBA;AA8vBA;AACA;AACA;AACA;AACA,IAAA,WAlwBA,yBAkwBA;AACA;AACA,WAAA,QAAA,GAAA,EAAA;AACA,WAAA,KAAA,GAAA,QAAA,CAHA,CAIA;;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,YAAA,GAAA,IAAA,CANA,CAOA;;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,MAAA,GAAA,IAAA;AACA,WAAA,UAAA,GAAA,KAAA,CAVA,CAWA;;AACA,WAAA,IAAA,GAAA,EAAA;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,CAAA;AACA,KAhxBA;AAkxBA;AACA,IAAA,SAnxBA,qBAmxBA,KAnxBA,EAmxBA;AAAA;;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAKA,IALA,CAKA,YAAA;AACA,sCAAA,IAAA,CAAA,SAAA,CAAA,KAAA,CAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,cAAA,IAAA,SAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,YAAA,OAAA,CAAA,OAAA;AACA,WAPA,MAOA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,YAAA,OAAA,CAAA,OAAA;AACA;AACA,SAhBA;AAiBA,OAvBA,EAwBA,KAxBA,CAwBA,YAAA;AACA,QAAA,OAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OA7BA;AA8BA,KAlzBA;AAmzBA;AACA,IAAA,kBApzBA,8BAozBA,IApzBA,EAozBA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,KAAA,UAAA,EAAA,KAAA;AACA,KAvzBA;AAwzBA;AACA,IAAA,KAzzBA,mBAyzBA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,KA3zBA;AA4zBA;AACA,IAAA,IA7zBA,kBA6zBA;AAAA;;AACA,WAAA,KAAA,CAAA,IAAA,CAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,0CAAA,OAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,cAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,OAAA;;AACA,cAAA,OAAA,CAAA,aAAA,GAAA,KAAA;;AACA,cAAA,OAAA,CAAA,OAAA;AACA;AACA,WANA;AAOA;AACA,OAVA;AAWA,KAz0BA;AA20BA;AACA,IAAA,QA50BA,sBA40BA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,IADA,GACA,UADA;;AAAA,sBAEA,OAAA,CAAA,IAAA,IAAA,CAFA;AAAA;AAAA;AAAA;;AAGA;AACA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,CAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,KAAA,GAAA,OAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,IAAA,GAAA,KAAA;AARA;AAAA;;AAAA;AAAA,sBASA,OAAA,CAAA,IAAA,IAAA,CATA;AAAA;AAAA;AAAA;;AAUA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,CAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,KAAA,GAAA,OAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,IAAA,GAAA,KAAA;AAdA;AAAA;;AAAA;AAAA,sBAeA,OAAA,CAAA,IAAA,IAAA,CAfA;AAAA;AAAA;AAAA;;AAgBA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,CAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,KAAA,GAAA,QAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,IAAA,GAAA,KAAA;AACA,gBAAA,IAAA,GAAA,cAAA,CArBA,CAqBA;;AArBA;AAAA;;AAAA;AAAA,sBAsBA,OAAA,CAAA,IAAA,IAAA,CAtBA;AAAA;AAAA;AAAA;;AAuBA,gBAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,CAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,KAAA,GAAA,SAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,IAAA,GAAA,KAAA;AA1BA;AAAA;;AAAA;AAAA,sBA2BA,OAAA,CAAA,IAAA,IAAA,CA3BA;AAAA;AAAA;AAAA;;AA4BA,gBAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,CAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,KAAA,GAAA,MAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,KAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,IAAA,GAAA,KAAA;AA/BA;AAAA;;AAAA;AAAA,sBAgCA,OAAA,CAAA,IAAA,IAAA,CAhCA;AAAA;AAAA;AAAA;;AAiCA;AACA,gBAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,iBAAA;;AAlCA;;AAAA;AAqCA,gBAAA,OAAA,CAAA,GAAA,CAAA,eAAA,EAAA,OAAA,CAAA,QAAA,EArCA,CAsCA;;AACA,gBAAA,OAAA,CAAA,SAAA,CACA;AAAA,kBAAA,IAAA,EAAA,OAAA,CAAA,QAAA;AAAA,kBAAA,IAAA,EAAA;AAAA,iBADA,EAEA;AACA,kBAAA,WAAA,EAAA,OAAA,CAAA,WAAA,CAAA,WADA;AAEA,kBAAA,IAAA,EAAA,OAAA,CAAA,WAAA,CAAA;AAFA,iBAFA,EAvCA,CA8CA;;;AACA,gBAAA,OAAA,CAAA,aAAA,GA/CA,CAgDA;;;AACA,gBAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA,CAjDA,CAkDA;;AAlDA;AAAA,uBAmDA,OAAA,CAAA,OAAA,EAnDA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoDA,KAh4BA;AAi4BA;AACA,IAAA,cAl4BA,0BAk4BA,GAl4BA,EAk4BA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,WAAA,MAAA,GACA,iFACA,GAAA,CAAA,KADA,GAEA,KAFA,GAGA,IAAA,IAAA,GAAA,OAAA,EAJA;AAKA,KAz4BA;AA24BA;AACA,IAAA,SA54BA,qBA44BA,IA54BA,EA44BA,MA54BA,EA44BA;AACA,UAAA,GAAA,mCAAA,IAAA,CAAA,IAAA,CAAA;AACA,WAAA,cAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA,WAAA,WAAA,CAAA,SAAA,CAAA,EAAA,GAAA,GAAA,CAAA,IAAA;;AACA,UAAA,IAAA,CAAA,IAAA,KAAA,UAAA,EAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,MAAA,CAAA,WAAA,CADA,CACA;;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,aAAA,WAAA,CAAA,IAAA,GAAA,MAAA,CAAA,IAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AACA,aAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,OANA,MAMA,IAAA,IAAA,CAAA,IAAA,KAAA,cAAA,EAAA;AACA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,MAAA,CAAA,WAAA,CAFA,CAEA;;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,cAAA;AACA,aAAA,WAAA,CAAA,IAAA,GAAA,MAAA,CAAA,IAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AACA,aAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,OAPA,MAOA;AACA,aAAA,WAAA,CAAA,SAAA,CAAA,KAAA,GAAA,QAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AACA,aAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,KAAA;AACA;;AACA,WAAA,cAAA,GAAA,IAAA;AACA,KAr6BA;AAs6BA;AACA,IAAA,QAv6BA,oBAu6BA,IAv6BA,EAu6BA;AACA,WAAA,SAAA,CACA;AAAA,QAAA,IAAA,EAAA,IAAA;AAAA,QAAA,IAAA,EAAA,KAAA;AAAA,OADA,EAEA;AAAA,QAAA,WAAA,EAAA;AAAA,OAFA;AAIA,KA56BA;AA86BA,IAAA,YA96BA,wBA86BA,GA96BA,EA86BA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,8BAAA;AAAA,kBAAA,WAAA,EAAA,GAAA,CAAA;AAAA,iBAAA,CADA;;AAAA;AAAA;AACA,gBAAA,IADA,sBACA,IADA;AACA,gBAAA,IADA,sBACA,IADA;AAEA,gBAAA,OAAA,CAAA,QAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,YAAA,GAAA,IAAA;;AAHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,KAl7BA;AAo7BA;AACA,IAAA,aAr7BA,2BAq7BA;AACA,WAAA,cAAA,GAAA,KAAA;AACA,KAv7BA;AAy7BA;AACA,IAAA,aA17BA,2BA07BA;AACA,WAAA,YAAA,GAAA,KAAA;AACA,KA57BA;AA87BA;AACA,IAAA,UA/7BA,sBA+7BA,IA/7BA,EA+7BA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA,IAAA;AADA,gCAEA,IAAA,CAAA,cAFA;AAAA,oDAGA,QAHA,yBAMA,MANA,yBASA,QATA,yBAYA,SAZA,0BAeA,IAfA,0BAkBA,MAlBA;AAAA;;AAAA;AAIA,gBAAA,OAAA,CAAA,IAAA,GAAA,CAAA;AAJA;;AAAA;AAOA,gBAAA,OAAA,CAAA,IAAA,GAAA,CAAA;AAPA;;AAAA;AAUA,gBAAA,OAAA,CAAA,IAAA,GAAA,CAAA;AAVA;;AAAA;AAaA,gBAAA,OAAA,CAAA,IAAA,GAAA,CAAA;AAbA;;AAAA;AAgBA,gBAAA,OAAA,CAAA,IAAA,GAAA,CAAA;AAhBA;;AAAA;AAmBA,gBAAA,OAAA,CAAA,IAAA,GAAA,CAAA;AAnBA;;AAAA;AAsBA,gBAAA,GAtBA,GAsBA,EAtBA;;AAuBA,oBAAA,OAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,kBAAA,GAAA,GAAA;AACA,oBAAA,KAAA,EAAA,IAAA,CAAA,WADA;AAEA,oBAAA,IAAA,EAAA,OAAA,CAAA,IAFA;AAGA,oBAAA,MAAA,EAAA,IAAA,CAAA,QAHA;AAIA,oBAAA,IAAA,EAAA,mCAAA,IAAA,IAAA,EAAA,EAAA,YAAA,CAJA,CAIA;;AAJA,mBAAA;AAMA,iBAPA,MAOA,IAAA,OAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,kBAAA,GAAA,GAAA;AACA,oBAAA,KAAA,EAAA,IAAA,CAAA,WADA;AAEA,oBAAA,IAAA,EAAA,OAAA,CAAA,IAFA;AAGA,oBAAA,KAAA,EAAA,IAAA,CAAA;AAHA,mBAAA;AAKA,iBANA,MAMA,IAAA,OAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,kBAAA,GAAA,GAAA;AACA,oBAAA,KAAA,EAAA,IAAA,CAAA,WADA;AAEA,oBAAA,IAAA,EAAA,OAAA,CAAA,IAFA;AAGA,oBAAA,KAAA,EAAA,IAAA,CAAA;AAHA,mBAAA;AAKA,iBANA,MAMA,IAAA,OAAA,CAAA,IAAA,IAAA,CAAA,EAAA;AACA,kBAAA,GAAA,GAAA;AACA,oBAAA,KAAA,EAAA,IAAA,CAAA,WADA;AAEA,oBAAA,IAAA,EAAA,OAAA,CAAA,IAFA;AAGA,oBAAA,OAAA,EAAA,IAAA,CAAA;AAHA,mBAAA;AAKA,iBANA,MAMA;AACA,kBAAA,GAAA,GAAA;AAAA,oBAAA,KAAA,EAAA,IAAA,CAAA,WAAA;AAAA,oBAAA,IAAA,EAAA,OAAA,CAAA;AAAA,mBAAA;AACA;;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,KAAA,EAAA,GAAA;AACA,0CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,sBAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,oBAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA,EADA,CAEA;;;AACA,oBAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,oBAAA,OAAA,CAAA,OAAA;AACA;AACA,iBAPA;;AApDA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA4DA;AA3/BA;AA3YA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- id=\"app\" -->\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n      @handleReset=\"getReset\"\n      @handleEvent=\"handleEvent\"\n    />\n    <el-white class=\"button-group1\">\n      <div class=\"button_btn\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-plue\"\n          @click=\"getInsert\"\n          v-hasPermi=\"['sybglr:button:add']\"\n          >新增\n        </el-button>\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-plue\"\n          @click=\"getInsertls\"\n          v-hasPermi=\"['sybglrls:button:add']\"\n          >历史数据新增\n        </el-button>\n        <el-button type=\"primary\" icon=\"el-icon-download\" @click=\"dcpdf\"\n          >批量导出</el-button\n        >\n        <!-- <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"deleteRow\">删除</el-button> -->\n      </div>\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"handelSelectChange\"\n        height=\"63vh\"\n        ref=\"sybglr\"\n      >\n        <el-table-column\n          slot=\"table_eight\"\n          align=\"center\"\n          fixed=\"right\"\n          style=\"display: block\"\n          label=\"操作\"\n          width=\"200\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <!--    公共按钮      -->\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              @click=\"getDetailsInfo(scope.row)\"\n              title=\"详情\"\n              class=\"el-icon-view\"\n            >\n            </el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"\n                (scope.row.ishg == 1 || scope.row.ishg == 6) &&\n                  scope.row.createBy == currUser\n              \"\n              @click=\"updateDetails(scope.row)\"\n              title=\"编辑\"\n              class=\"el-icon-edit\"\n            ></el-button>\n             <el-button\n              type=\"text\"\n              size=\"small\"\n              v-hasPermi=\"['sybglrls:button:update']\"\n              @click=\"updateDetails(scope.row)\"\n              title=\"编辑\"\n              class=\"el-icon-edit\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"scope.row.ishg != 1 && scope.row.ishg != 6\"\n              @click=\"showTimeLine(scope.row)\"\n              title=\"流程查看\"\n              class=\"el-icon-lcck commonIcon\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"scope.row.ishg != 1 && scope.row.ishg != 6\"\n              @click=\"showProcessImg(scope.row)\"\n              title=\"流程图\"\n              class=\"el-icon-lct commonIcon\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"scope.row.createBy == currUser && scope.row.ishg == 6\"\n              @click=\"saveSybgInfo(scope.row)\"\n              title=\"编辑报告\"\n              class=\"el-icon-edit-outline\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              @click=\"SybgInfo(scope.row)\"\n              title=\"查看报告\"\n              class=\"el-icon-tickets\"\n            >\n            </el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"scope.row.ishg == 1 && showButton\"\n              @click=\"saveSybgInfo(scope.row)\"\n              title=\"编辑报告\"\n              class=\"el-icon-edit-outline\"\n            ></el-button>\n            <el-button\n              @click=\"getZxmmpInfo(scope.row)\"\n              type=\"text\"\n              size=\"small\"\n              title=\"数据对比\"\n              class=\"el-icon-edit-outline\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"\n                ((scope.row.ishg == 1 || scope.row.ishg == 6) &&\n                  scope.row.createBy == currUser) ||\n                  currUser === 'admin'\n              \"\n              @click=\"deleteRow(scope.row.objId)\"\n              title=\"删除\"\n              class=\"el-icon-delete\"\n            ></el-button>\n          </template>\n        </el-table-column>\n      </comp-table>\n    </el-white>\n\n    <!--详情/新增/修改-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"50%\"\n      v-dialogDrag\n    >\n      <el-form\n        label-width=\"130px\"\n        ref=\"form\"\n        :model=\"form\"\n        :disabled=\"isDisabled\"\n        :rules=\"rules\"\n      >\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"设备专业：\" prop=\"sjzy\">\n              <el-select\n                @change=\"selectsbzy\"\n                v-model=\"form.sjzy\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in sbzyList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验专业：\" prop=\"syzybm\">\n              <el-select\n                @change=\"selectSyxzDataBySyzybm\"\n                v-model=\"form.syzybm\"\n                placeholder=\"请选择试验专业\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in syzyList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验性质：\" prop=\"syxzbm\">\n              <el-select\n                v-model=\"form.syxzbm\"\n                placeholder=\"请选择试验性质\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in syxzList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"设备地点：\" prop=\"sydd\">\n              <!-- <el-input v-model=\"form.sydd\"   placeholder=\"请输入设备地点\" v-on:click.native=\"sysbddClick()\"\n                        style=\"width: 100%\"></el-input> -->\n              <el-select\n                v-model=\"form.sydd\"\n                placeholder=\"请选择设备地点\"\n                @change=\"onclckdd\"\n                style=\"width: 100%\"\n                filterable\n                clearable\n              >\n                <el-option\n                  v-for=\"item in sbddList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验设备：\" prop=\"sbmc\">\n              <el-input\n                v-model=\"form.sbmc\"\n                placeholder=\"请输入试验设备\"\n                v-on:click.native=\"sysbSelectedClick()\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"委托单位：\" prop=\"wtdw\">\n              <el-input\n                v-model=\"form.wtdw\"\n                placeholder=\"请输入委托单位\"\n                style=\"width: 100%\"\n              >\n              </el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验单位：\" prop=\"sydw\">\n              <el-select\n                v-model=\"form.sydw\"\n                placeholder=\"请选择试验单位\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in sydwList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验日期：\" prop=\"syrq\">\n              <el-date-picker\n                v-model=\"form.syrq\"\n                type=\"date\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"请选择试验日期\"\n                style=\"width: 100%\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验名称：\" prop=\"symc\">\n              <el-input\n                v-model=\"form.symc\"\n                placeholder=\"请输入试验名称\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"温度(℃)：\" prop=\"wd\">\n              <el-input\n                v-model=\"form.wd\"\n                placeholder=\"请输入温度\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"湿度(%)：\" prop=\"sd\">\n              <el-input\n                v-model=\"form.sd\"\n                placeholder=\"请输入湿度\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"油温(℃)：\" prop=\"yw\">\n              <el-input\n                v-model=\"form.yw\"\n                placeholder=\"请输入油温\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验天气：\" prop=\"tq\">\n              <el-select\n                v-model=\"form.tq\"\n                placeholder=\"请选择试验天气\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in tqList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验模板：\" prop=\"symb\">\n              <el-input\n                v-model=\"form.symb\"\n                placeholder=\"请输入试验模板\"\n                v-on:click.native=\"symbSelectedClick()\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n\n          <!-- <el-col :span=\"12\">\n            <el-form-item label=\"审核人：\" prop=\"shrid\">\n              <el-input v-model=\"form.shrid\" placeholder=\"请输入审核人\" style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"12\">\n            <el-form-item label=\"编制人：\" prop=\"bzrid\">\n              <el-input\n                v-model=\"form.bzrid\"\n                placeholder=\"请输入编制人\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验人员：\" prop=\"syryid\">\n              <el-input\n                v-model=\"form.syryid\"\n                placeholder=\"请输入试验人员\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"审核人：\" prop=\"bzzsp\">\n              <el-input\n                v-model=\"form.bzzsp\"\n                placeholder=\"请输入审核人\"\n                style=\"width: 100%\"\n                :disabled=\"syryDialog\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"批准人：\" prop=\"ssdwldmc\">\n              <el-input\n                v-model=\"form.ssdwldmc\"\n                placeholder=\"请输入批准人\"\n                style=\"width: 100%\"\n                :disabled=\"syryDialog\"\n              >\n              </el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <!-- <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"试验结论：\" prop=\"bz\">\n              <el-input type=\"textarea\" v-model=\"form.syjl\" placeholder=\"请输入试验结论\"\n                        style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row> -->\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注：\" prop=\"bz\">\n              <el-input\n                type=\"textarea\"\n                v-model=\"form.bz\"\n                placeholder=\"请输入备注\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" v-show=\"isShow\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"save\">确 定</el-button>\n      </div>\n    </el-dialog>\n    <!--试验设备地点弹窗-->\n    <el-dialog\n      title=\"试验设备地点\"\n      :visible.sync=\"isShowSysbddDialog\"\n      width=\"20%\"\n      v-if=\"isShowSysbddDialog\"\n      v-dialogDrag\n    >\n      <sysbdd\n        :mainData=\"mainData\"\n        @accessTreeData=\"handleAccessTreeData\"\n        @closeSyddDialog=\"handleCloseSyddDialog\"\n      >\n      </sysbdd>\n    </el-dialog>\n    <!--试验设备选择弹框-->\n    <el-dialog\n      title=\"设备选择\"\n      :visible.sync=\"isShowSysbDialog\"\n      width=\"60%\"\n      v-if=\"isShowSysbDialog\"\n      v-dialogDrag\n    >\n      <sysb-selectedbg\n        @handleAcceptSbData=\"handleAcceptSbData\"\n        :selectedSbParam=\"selectedSbParam\"\n        @closeSysbSelectDialog=\"handleControlSysbSelectDialog\"\n      ></sysb-selectedbg>\n    </el-dialog>\n    <!--试验模板选择组件-->\n    <el-dialog\n      title=\"模板选择\"\n      :visible.sync=\"isShowSymbDialog\"\n      width=\"70%\"\n      v-if=\"isShowSymbDialog\"\n      v-dialogDrag\n    >\n      <symbSyxm-select\n        :symbData=\"symbData\"\n        @handleAcceptMbData=\"handleAcceptMbData\"\n        @closeSymbSelectDialog=\"handleControlSymbSelectDialog\"\n      ></symbSyxm-select>\n    </el-dialog>\n    <!--填写报告模板-->\n    <!--htmlToPdf插件-->\n    <el-dialog\n      title=\"预览\"\n      :visible.sync=\"isShowDownLoadDialog\"\n      width=\"70%\"\n      class=\"outPut\"\n      id=\"dialogActst\"\n      v-dialogDrag\n    >\n      <div style=\"height: 500px;overflow-y: scroll;\">\n        <tablePdf\n          ref=\"tablePdf\"\n          :basic-data=\"basicData\"\n          :tablebox3=\"tablebox3\"\n          :tablebox2=\"tablebox2\"\n        ></tablePdf>\n      </div>\n      <div slot=\"footer\">\n        <el-button\n          @click=\"closeYlDialog\"\n          v-if=\"this.form.ishg == 1 || this.form.ishg == 6\"\n          >取 消</el-button\n        >\n        <el-button\n          type=\"primary\"\n          @click=\"saveYlDialog\"\n          v-if=\"\n            (this.form.ishg == 1 ||\n              this.form.ishg == 6 ||\n              this.form.ishg == 5) &&\n              this.bcDisabled\n          \"\n          >保存\n        </el-button>\n        <el-button type=\"primary\" @click=\"exportPdf('tablePdf')\"\n          >导 出</el-button\n        >\n        <el-button\n          type=\"primary\"\n          @click=\"commitSh\"\n          v-if=\"this.form.ishg == 1 && this.form.createBy == currUser\"\n          >上报\n        </el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"commitSh\"\n          v-if=\"this.form.ishg == 2 && this.form.ssdwld == currUser\"\n          >审批\n        </el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"commitSh\"\n          v-if=\"\n            this.form.ishg == 7 &&\n              this.form.sddwshr &&\n              this.form.sddwshr.includes(currUser)\n          \"\n          >审批\n        </el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"commitSh\"\n          v-if=\"this.form.ishg == 3 && this.form.sckzg == currUser\"\n          >审批\n        </el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"commitSh\"\n          v-if=\"this.form.ishg == 5 && this.form.bzzsp == currUserName\"\n          >审批\n        </el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"handleTh('rollback')\"\n          v-if=\"this.form.ishg == 2 && this.form.ssdwld == currUser\"\n        >\n          回退</el-button\n        >\n        <el-button\n          type=\"primary\"\n          @click=\"handleTh('rollback')\"\n          v-if=\"this.form.ishg == 3 && this.form.sckzg == currUser\"\n        >\n          回退</el-button\n        >\n        <el-button\n          type=\"primary\"\n          @click=\"handleTh('rollback')\"\n          v-if=\"this.form.ishg == 5 && this.form.bzzsp == currUserName\"\n          >回退</el-button\n        >\n        <el-button\n          type=\"primary\"\n          @click=\"handleTh('rollback')\"\n          v-if=\"this.form.ishg == 7 && this.form.sddwshr == currUser\"\n          >回退</el-button\n        >\n      </div>\n    </el-dialog>\n    <el-dialog\n      title=\"批量导出\"\n      :visible.sync=\"isShowDownLoadDialogA\"\n      width=\"70%\"\n      class=\"outPut\"\n      id=\"dialogActst\"\n      v-dialogDrag\n    >\n      <div style=\"height: 400px;overflow-y: scroll;\">\n        <tablePdf\n          :ref=\"`tablePdf${index}`\"\n          v-for=\"(item, index) in selectA\"\n          :basic-data=\"item\"\n          :tablebox3=\"item.tablebox3\"\n          :tablebox2=\"item.tablebox2\"\n        ></tablePdf>\n      </div>\n      <el-progress\n        :percentage=\"inputpdf\"\n        :format=\"progressformate\"\n      ></el-progress>\n      <!-- <el-progress :percentage=\"shengpdf\" status=\"success\"></el-progress>\n      <el-progress :percentage=\"downloadpdf\" status=\"warning\"></el-progress> -->\n    </el-dialog>\n\n    <!--  工作流  -->\n    <activiti\n      :processData=\"processData\"\n      :isShow=\"isShowActiviti\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n      v-if=\"isShowActiviti\"\n    ></activiti>\n    <time-line\n      :value=\"timeLineShow\"\n      :timeData=\"timeData\"\n      @closeTimeLine=\"colseTimeLine\"\n    />\n    <!--流程图查看-->\n    <el-dialog\n      title=\"审批流程图\"\n      :visible.sync=\"openLoadingImg\"\n      width=\"56%\"\n      append-to-body\n      :close-on-click-modal=\"false\"\n      v-dialogDrag\n    >\n      <div>\n        <img :src=\"imgSrc\" style=\"width: 100%\" />\n      </div>\n    </el-dialog>\n    <!-- 试验数据对比 -->\n    <el-dialog\n      :visible.sync=\"isShowMpInfo\"\n      v-dialogDrag\n      v-if=\"isShowMpInfo\"\n      width=\"80%\"\n      title=\"试验项目内容\"\n      :append-to-body=\"true\"\n      @close=\"closeInfoDialog\"\n    >\n      <zxm-info\n        :mp-data=\"rowData\"\n        :mx-data.sync=\"mxData\"\n        @closeInfoDialog=\"closeInfoDialog\"\n      />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  selectSyxzDataBySyzybm,\n  getSyzySelectedOptions,\n  getSybgjlDataByPage,\n  saveOrUpdateSybgjl,\n  removeSybgjlData,\n  getMouldValue,\n  getChildsValue,\n  saveChildsValue,\n  updateSybgjl,\n  getbdzList,\n  showButtenJS,\n  getTodoItemYd\n} from \"@/api/dagangOilfield/bzgl/sybglr\";\nimport {\n  getDeviceClassTreeNodeByPid,\n  getPageDataList,\n  getTable,\n  remove,\n  saveOrUpdate\n} from \"@/api/dagangOilfield/bzgl/sympk/sympk\";\nimport { Loading } from \"element-ui\";\nimport activiti from \"com/activiti_sybg\";\nimport timeLine from \"com/timeLine\";\nimport { HistoryList } from \"@/api/activiti/processTask\";\n//试验设备地点子组件\nimport sysbdd from \"@/views/dagangOilfield/bzgl/sybzk/sybgjlcomment/sysbdd\";\n//试验单位下拉框查询\nimport { getOrganizationSelected } from \"@/api/dagangOilfield/asset/bdsbtz\";\n//试验设备选择\nimport sysbSelectedbg from \"@/views/dagangOilfield/bzgl/sybzk/sybgjlcomment/sysbSelectedbg\";\n//试验模板选择\nimport symbSyxmSelect from \"@/views/dagangOilfield/bzgl/sybzk/sybgjlcomment/symbSyxmSelect\";\n// 试验数据对比\nimport zxmInfo from \"@/views/dagangOilfield/bzgl/sybzk/zxmInfo\";\n//获取线路下拉数据\nimport { getSdLineSelected } from \"@/api/dagangOilfield/asset/sdxl\";\nimport { getPdsSelected } from \"@/api/dagangOilfield/asset/pdsgl\";\n//pdf导出工具\nimport htmlToPdf from \"@/utils/print/htmlToPdf\";\nimport tablePdf from \"./tablePdf\";\nimport { formatterDateTime } from \"@/utils/handleData\";\nimport { getDictTypeData } from \"@/api/system/dict/data\";\nexport default {\n  name: \"sybglr\",\n  components: {\n    sysbdd,\n    sysbSelectedbg,\n    symbSyxmSelect,\n    htmlToPdf,\n    activiti,\n    timeLine,\n    tablePdf,\n    zxmInfo\n  },\n  data() {\n    var validateSbmc = (rule, value, callback) => {\n      if (!this.form.sbmc) {\n        callback(new Error());\n      } else {\n        callback();\n      }\n    };\n    var validateSymb = (rule, value, callback) => {\n      if (!this.form.symb) {\n        callback(new Error());\n      } else {\n        callback();\n      }\n    };\n    return {\n      // 试验数据对比用\n      isShowMpInfo: false,\n      rowData: {},\n      mxData: [],\n      //\n      showButton: false,\n      syryDialog: true,\n      bcDisabled: false,\n      tablebox3: \"\",\n      tablebox2: \"\",\n      loading: false,\n      currUser: this.$store.getters.name,\n      currUserName: this.$store.getters.nickName,\n      ssdwld: undefined, //所属单位领导\n      sckzg: undefined, //生产科专工\n      createBy: undefined, //新建人\n      clickRow: {},\n      //流程图查看\n      openLoadingImg: false,\n      imgSrc: \"\", //流程图查看地址\n      timeLineShow: false,\n      timeData: [],\n      //弹出框标题\n      activitiOption: { title: \"上报\" },\n      //工作流弹窗\n      isShowActiviti: false,\n      // 多选框选选中的数据\n      selectData: [],\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"sybgsh\",\n        businessKey: \"\",\n        businessType: \"试验报告审核\",\n        variables: {},\n        defaultFrom: true,\n        processType: \"complete\"\n      },\n      //基本信息表格数据\n      basicData: {\n        sydwmc: \"\", //试验单位\n        syxz: \"\", //试验性质\n        syrq: \"\", //  试验日期\n        syryid: \"\", // 试验人员\n        bzrid: \"\", // 编写人\n        shrid: \"\", // 审核人\n        updateBy: \"\", // 批准人\n        tq: \"\", // 试验天气\n        wd: \"\", // 温度\n        sd: \"\", // 湿度\n        sydd: \"\", //试验地点\n        wtdw: \"\", //委托单位\n        yxbh: \"\", //运行编号\n        sccj: \"\", //生产厂家\n        ccrq: \"\", //出厂日期，\n        ccbh: \"\", //出厂编号\n        sbxh: \"\", //设备型号\n        eddy: \"\", //额定电压(kV)\n        eddl: \"\", //额定电流(A)\n        xs: \"\", //相数\n        //相数接线组别\n        edrl: \"\" ,//额定容量(MVA)\n        //电压组合\n        //电流组合\n        //容量组合\n        tyrq:\"\"\n      },\n      Cont: [\n        {\n          bdz: \"变电站\",\n          name: \"\",\n          bdz1: \"委托单位\",\n          name1: \"\",\n          bdz2: \"试验单位\",\n          name2: \"\",\n          bdz3: \"运行编号\",\n          name3: \"\"\n        },\n        {\n          bdz: \"试验性质\",\n          name: \"\",\n          bdz1: \"试验日期\",\n          name1: \"\",\n          bdz2: \"试验人员\",\n          name2: \"\",\n          bdz3: \"试验地点\",\n          name3: \"\"\n        },\n        {\n          bdz: \"报告日期\",\n          name: \"\",\n          bdz1: \"编写人\",\n          name1: \"\",\n          bdz2: \"审核人\",\n          name2: \"\",\n          bdz3: \"批准人\",\n          name3: \"\"\n        },\n        {\n          bdz: \"试验天气\",\n          name: \"\",\n          bdz1: \"环境温度（℃）\",\n          name1: \"\",\n          bdz2: \"环境相对湿度（%）\",\n          name2: \"\",\n          bdz3: \"投运日期\",\n          name3: \"\"\n        }\n      ],\n      allAlign: null,\n      titleName: \"\", //填写模板标题\n      //下载弹出框控制\n      isShowDownLoadDialog: false,\n\n      isShowDownLoadDialogA: false,\n      mbInfo: {},\n      //主设备选择传递子组件参数\n      selectedSbParam: {\n        lx: \"bd\",\n        sbmc: \"\"\n      },\n      //主设备选择传递子组件参数\n      mainData: {\n        lx: \"\"\n      },\n      //试验设备选择时给试验模板子组件传递参数\n      symbData: {\n        sblxid: \"\"\n      },\n\n      //设备专业\n      sbzyList: [\n        {\n          label: \"输电设备\",\n          value: \"输电设备\"\n        },\n        {\n          label: \"变电设备\",\n          value: \"变电设备\"\n        },\n        {\n          label: \"配电设备\",\n          value: \"配电设备\"\n        }\n      ],\n      //试验模板弹出框控制\n      isShowSymbDialog: false,\n      //试验设备弹出框控制\n      isShowSysbDialog: false,\n      //试验设备地点弹出框控制\n      isShowSysbddDialog: false,\n      //试验报告记录新增弹出框表单\n      form: {\n        //固定不可清空\n        sjzy: \"\",\n        //试验专业编码\n        syzybm: \"\",\n        //试验专业名称\n        syzy: \"\",\n        //试验性质编码\n        syxzbm: \"\",\n        //试验性质名称\n        syxz: \"\",\n        //设备地点名称\n        sydd: \"\",\n        syddid: \"\", //试验地点id\n        //试验设备id\n        sysbid: \"\",\n        sbmc: \"\", //设备名称\n        sydw: \"\", //试验单位id\n        syrq: undefined, // 试验日期\n        symc: \"\", //试验名称\n        wd: \"\", //温度\n        sd: \"\", //湿度\n        yw: \"\", //油温\n        tq: \"\", //天气\n        symb: \"\", //试验模板名称\n        symbid: \"\", //试验模板id\n        bzrid: \"\", //编制人名称，后面改用下拉框\n        shrid: \"\", //审核人名称，后面改为下拉框\n        syryid: \"\", //试验人员。后面改为下拉框\n        bz: \"\" //备注\n      },\n      assetTypeCode: \"\", //设备类型编码\n      //详情弹框是否显示\n      isShowDetails: false,\n      //显示取消确认按钮\n      isShow: true,\n      //是否禁用\n      isDisabled: false,\n      zyisDisabled: false,\n      //试验专业\n      syzyList: [],\n      //试验性质\n      syxzList: [],\n      //设备地点\n      sbddList: [],\n      //试验单位\n      sydwList: [],\n      //流程状态\n      ishg: \"\",\n      sybgjlld: \"\",\n      selectA: [],\n      shengpdf: 0,\n      inputpdf: 0,\n      downloadpdf: 0,\n      syxmid: \"\",\n      sysbid: \"\",\n      //试验天气\n      tqList: [\n        { label: \"晴\", value: \"晴\" },\n        { label: \"阴\", value: \"阴\" },\n        { label: \"雾\", value: \"雾\" },\n        { label: \"小雨\", value: \"小雨\" },\n        { label: \"中雨\", value: \"中雨\" },\n        { label: \"大雨\", value: \"大雨\" },\n        { label: \"雷雨\", value: \"雷雨\" },\n        { label: \"小雪\", value: \"小雪\" },\n        { label: \"中雪\", value: \"中雪\" },\n        { label: \"大雪\", value: \"大雪\" }\n      ],\n      //试验报告记录新增弹窗框标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          symc: \"\",\n          sjzy: \"\",\n          syddid: \"\",\n          symb: \"\",\n          syrqArr: [],\n          syzy: \"\",\n          syxz: \"\",\n          sbmc: \"\",\n          ishg: \"\"\n        },\n        fieldList: [\n          { label: \"试验名称\", type: \"input\", value: \"symc\" },\n          {\n            label: \"设备专业\",\n            type: \"select\",\n            value: \"sjzy\",\n            options: [\n              { label: \"输电设备\", value: \"输电设备\" },\n              { label: \"变电设备\", value: \"变电设备\" },\n              { label: \"配电设备\", value: \"配电设备\" }\n            ],\n            clearable: false\n          },\n          { label: \"设备地点\", type: \"select\", value: \"syddid\", options: [] },\n          { label: \"试验模板名称\", type: \"input\", value: \"symb\" },\n          {\n            label: \"试验日期\",\n            type: \"date\",\n            value: \"syrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"试验专业\", type: \"select\", value: \"syzy\", options: [] },\n          { label: \"试验性质\", type: \"input\", value: \"syxz\" },\n          { label: \"试验设备\", type: \"input\", value: \"sbmc\" },\n          { label: \"流程状态\", type: \"select\", value: \"ishg\", options: [] }\n        ]\n      },\n      //查询报告记录参数\n      queryParam: {\n        pageSize: 10,\n        pageNum: 1\n        // syddid: \"\"\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 0]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"试验专业\", prop: \"syzy\" },\n          { label: \"试验性质\", prop: \"syxz\" },\n          { label: \"试验名称\", prop: \"symc\", minWidth: \"100\" },\n          { label: \"设备地点\", prop: \"sydd\", minWidth: \"120\" },\n          { label: \"试验设备\", prop: \"sbmc\", minWidth: \"200\" },\n          { label: \"试验模板名称\", prop: \"symb\", minWidth: \"120\" },\n          { label: \"天气\", prop: \"tq\" },\n          { label: \"试验日期\", prop: \"syrq\" },\n          { label: \"试验人员\", prop: \"syryid\" },\n          { label: \"流程状态\", prop: \"ztmc\" }\n          // {\n          //   prop: 'operation',\n          //   label: '试验报告',\n          //   minWidth: '120px',\n          //   style: {display: 'block'},\n          //   //操作列固定再右侧\n          //   operation: [\n          //     {name: '填写报告', clickFun: this.saveSybgInfo},\n          //   ]\n          // },\n          // {\n          //   prop: 'operation',\n          //   label: '操作',\n          //   minWidth: '120px',\n          //   style: {display: 'block'},\n          //   //操作列固定再右侧\n          //   fixed: 'right',\n          //   operation: [\n          //     {name: '修改', clickFun: this.updateDetails},\n          //     {name: '详情', clickFun: this.getDetailsInfo},\n          //   ]\n          // },\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      //试验报告记录选中\n      selectRows: [],\n      str: \"\",\n      saveData: {}, //选中填写报告的值\n      rules: {\n        syzybm: [\n          { required: true, message: \"请输入试验专业\", trigger: \"select\" }\n        ],\n        syxzbm: [\n          { required: true, message: \"请输入试验性质\", trigger: \"blur\" }\n        ],\n        sydd: [{ required: true, message: \"请输入设备地点\", trigger: \"blur\" }],\n        // syxzbm: [{ required: true, message: \"请输入设备地点\", trigger: \"blur\" }],\n        symb: [\n          {\n            required: true,\n            message: \"请选择试验模板\",\n            validator: validateSymb,\n            trigger: \"change\"\n          }\n        ],\n        sjzy: [{ required: true, message: \"请选择设备专业\", trigger: \"blur\" }],\n        sbmc: [\n          {\n            required: true,\n            message: \"请选择试验设备\",\n            validator: validateSbmc,\n            trigger: \"change\"\n          }\n        ],\n        symc: [{ required: true, message: \"请输入试验名称\", trigger: \"blur\" }]\n      },\n      jlData: \"\", //结论所在行标识\n      isJlDisabled: false //结论是否不可编辑\n    };\n  },\n\n  mounted() {\n    //获取试验专业下拉框数据\n    this.getOptions();\n    //获取试验单位下拉框\n    this.getOrganizationSelected();\n    //获取数据列表\n    this.getData(this.$route.query);\n    //查询当前登录人是否有试验报告填写权限\n    let params = {\n      groupId: 74,\n      userName: this.$store.getters.name\n    };\n    showButtenJS(params).then(res => {\n      if (res.data.length > 0) {\n        this.showButton = true;\n      }\n    });\n  },\n  methods: {\n    //进度条状态更新\n    progressformate(procent) {\n      return this.inputpdf === 100 ? \"导出完成\" : `${procent} %`;\n    },\n    //获取试验子项目信息\n    async getZxmmpInfo(row) {\n      // 试验报告中获取试验项目模板的symbid\n      let symbid = row.symbid;\n      // 试验项目模板中获取子项目模板数据\n      //rowData\n      let { data: zxmMbData } = await getMouldValue({ symbid: row.symbid });\n      this.rowData = zxmMbData[Object.keys(zxmMbData)[1]][0];\n      //mxData\n      getChildsValue(row).then(result => {\n        let sysj_data = result.data.syxm;\n        this.mxData = sysj_data[Object.keys(sysj_data)[0]];\n        this.isShowMpInfo = true;\n      });\n    },\n    //关闭试验数据对比弹框\n    closeInfoDialog() {\n      this.isShowMpInfo = false;\n    },\n    //搜索框sbddid 选择事件\n    sbddidClick(val, val1) {\n      if (val1 === \"syddmc\") {\n        this.mainData.lx = \"\";\n        this.isShowSysbddDialog = true;\n      }\n    },\n\n    //详情按钮\n    getDetailsInfo(row) {\n      this.title = \"试验报告详情\";\n      //不显示取消确认按钮\n      this.isShow = false;\n      //禁用表单\n      this.isDisabled = true;\n      this.zyisDisabled = true;\n      //打开弹窗\n      this.isShowDetails = true;\n      this.form = { ...row };\n      //调用获取试验性质方法渲染试验性质\n      this.selectSyxzDataBySyzybm1();\n    },\n    //修改按钮\n    updateDetails(row) {\n      this.title = \"试验报告修改\";\n      //显示取消确认按钮\n      this.isShow = true;\n      this.zyisDisabled = true;\n      //禁用表单\n      this.isDisabled = false;\n      //打开弹窗\n      this.isShowDetails = true;\n      this.form = { ...row };\n      //调用获取试验性质方法渲染试验性质\n      this.selectSyxzDataBySyzybm1();\n    },\n    //填写报告\n    async saveSybgInfo(row) {\n      this.form = { ...row };\n      this.clickRow = row;\n      (this.bcDisabled = true), (this.ishg = row.ishg);\n      this.createBy = row.createBy;\n      this.ssdwld = row.ssdwld;\n      this.sckzg = row.sckzg;\n      this.saveData = row;\n      this.basicData = row;\n      this.sybgjlld = row.objId;\n      this.symbid = row.symbid;\n      this.sysbid = row.sysbid;\n      this.isShowDownLoadDialog = true;\n      this.titleName = row.symb;\n      //在填写报告时，请求后台处理数据\n      const that = this;\n      await getMouldValue({ symbid: row.symbid }).then(res => {\n        res = res.data;\n        getChildsValue(row).then(result => {\n          let sbmp_data = result.data.sbmp;\n          let sysj_data = result.data.syxm;\n          let arr = [];\n          for (let item in sbmp_data) {\n            arr.push({ [item]: sbmp_data[item] });\n          }\n          let arr1 = [];\n          for (let val in sysj_data) {\n            arr1.push({ [val]: sysj_data[val] });\n          }\n          that.handleSbmp(res[Object.keys(res)[0]], arr, \"\", \"h2_table\");\n          that.handleSbmp(res[Object.keys(res)[1]], arr1, \"\", \"h3_table\");\n        });\n      });\n      this.form = { ...row };\n    },\n\n    //查看试验报告\n    SybgInfo(row) {\n      this.form = { ...row };\n      this.clickRow = row;\n      this.ishg = row.ishg;\n      this.bzzsp = row.bzzsp;\n      this.bcDisabled = true;\n      this.createBy = row.createBy;\n      this.ssdwld = row.ssdwld;\n      this.sckzg = row.sckzg;\n      this.saveData = row;\n      this.basicData = row;\n      this.sybgjlld = row.objId;\n      this.symbid = row.symbid;\n      this.sysbid = row.sysbid;\n      this.isShowDownLoadDialog = true;\n      this.titleName = row.symb;\n      let gzimg1 = document.getElementById(\"gzimg1\");\n      gzimg1 && gzimg1.remove();\n      let gzimg2 = document.getElementById(\"gzimg2\");\n      gzimg2 && gzimg2.remove();\n      //在填写报告时，请求后台处理数据\n      const that = this;\n      getMouldValue({ symbid: row.symbid }).then(res => {\n        res = res.data;\n        getChildsValue(row).then(result => {\n          let sbmp_data = result.data.sbmp;\n          let sysj_data = result.data.syxm;\n          let arr = [];\n          for (let item in sbmp_data) {\n            arr.push({ [item]: sbmp_data[item] });\n          }\n          let arr1 = [];\n          for (let val in sysj_data) {\n            arr1.push({ [val]: sysj_data[val] });\n          }\n          that.handleSbmp(res[Object.keys(res)[0]], arr, \"\", \"h2_table\");\n          that.handleSbmp(res[Object.keys(res)[1]], arr1, \"\", \"h3_table\");\n        });\n      });\n      setTimeout(async () => {\n        this.showTest();\n      }, 3000);\n    },\n\n    handleSbmp(dataNum, dataArr, str, tableBox, returnString) {\n      for (let k = 0; k < dataNum.length; k++) {\n        var hs = dataNum[k].aHs;\n        var ls = dataNum[k].aLs;\n        let data = dataArr[k];\n        for (var item in data) {\n          // str += \"<tr style='text-align:left;'><th colspan=\"+ ls +\">\"+ item +\"</th></tr>\";\n          for (let i = 0; i < hs; i++) {\n            //有几行就插入几行\n            let temp = \"<tr class='splitClass'>\";\n            for (let j = 0; j < data[item].length; j++) {\n              //循环数据看每行有几列\n              if (i == data[item][j].rowindex) {\n                var nrbs = data[item][j].nrbs;\n                var sjlx = data[item][j].sjlx; //数据类型\n                var objId = data[item][j].objId;\n                var txt = data[item][j].text;\n                var nr = \"\";\n                if (nrbs === \"结论\") {\n                  this.jlData = data[item][j + 1]; //保存下一个单元格内容\n                }\n                if (sjlx == \"STRING\") {\n                  //判断数据类型为string的表示为空格，可编辑\n                  if (!txt) {\n                    txt = \"\";\n                  }\n                  nr =\n                    \"<input type='text' style='border: none;width: 99%;text-align: center;' class=\" +\n                    tableBox +\n                    \" id=\" +\n                    objId +\n                    \" value=\" +\n                    txt +\n                    \">\";\n                } else {\n                  nr = nrbs;\n                }\n                if (data[item][j].colspan != \"1\") {\n                  //判断colspan不为1的话为可编辑的\n                  if (this.jlData && this.jlData.objId === objId) {\n                    //当前单元格是结论所在单元格\n                    if (this.form.ishg == 5 || this.form.ishg == 6) {\n                      //只有班组长审批和历史录入是可编辑的\n                      temp +=\n                        \"<td tabindex='-1' colspan='\" +\n                        data[item][j].colspan +\n                        \"' rowspan='\" +\n                        data[item][j].rowspan +\n                        \"' >\" +\n                        nr +\n                        \"</td>\";\n                      this.isJlDisabled = false;\n                    } else {\n                      //显示原值，不可编辑\n                      temp +=\n                        \"<td tabindex='-1' colspan='\" +\n                        data[item][j].colspan +\n                        \"' rowspan='\" +\n                        data[item][j].rowspan +\n                        \"' id='\" +\n                        objId +\n                        \"' class='_objId'\" +\n                        \" >\" +\n                        txt +\n                        \"</td>\";\n                      this.isJlDisabled = true;\n                    }\n                  } else {\n                    temp +=\n                      \"<td tabindex='-1' colspan='\" +\n                      data[item][j].colspan +\n                      \"' rowspan='\" +\n                      data[item][j].rowspan +\n                      \"' >\" +\n                      nr +\n                      \"</td>\";\n                  }\n                } else {\n                  temp += \"<td tabindex='-1'>\" + nr + \"</td>\";\n                }\n              }\n            }\n            temp += \"</tr>\";\n            str += temp;\n          }\n        }\n      }\n      if (returnString) {\n        return str;\n      }\n      if (tableBox === \"h2_table\") {\n        this.tablebox2 = str;\n      } else if (tableBox === \"h3_table\") {\n        this.tablebox3 = str;\n      } else {\n        this[tableBox] = str;\n      }\n    },\n    //关闭填写报告弹框\n    closeYlDialog() {\n      this.isShowDownLoadDialog = false;\n    },\n\n    dcpdf() {\n      let that = this;\n      if (!that.selectRows.length > 0) {\n        that.$message.warning(\"请先选中需要批量导出的数据\");\n        return;\n      }\n      that.inputpdf = 0;\n      let selectRows = that.selectRows;\n      console.log(selectRows, \"select\");\n      that.selectA = selectRows.map((e, i) => {\n        getMouldValue({ symbid: e.symbid }).then(res => {\n          res = res.data;\n          getChildsValue(e).then(result => {\n            let sbmp_data = result.data.sbmp;\n            let sysj_data = result.data.syxm;\n            let arr = [];\n            for (let item in sbmp_data) {\n              arr.push({ [item]: sbmp_data[item] });\n            }\n            let arr1 = [];\n            for (let val in sysj_data) {\n              arr1.push({ [val]: sysj_data[val] });\n            }\n            e.tablebox2 = that.handleSbmp(\n              res[Object.keys(res)[0]],\n              arr,\n              \"\",\n              \"\",\n              true\n            );\n            e.tablebox3 = that.handleSbmp(\n              res[Object.keys(res)[1]],\n              arr1,\n              \"\",\n              \"\",\n              true\n            );\n            e.sysj = sysj_data;\n          });\n        });\n        // that.inputpdf = ((i + 1 / selectRows.length) * 100).toFixed(2)\n        // console.log(that.inputpdf, 'inputpdf1...');\n        return e;\n      });\n      console.log(that.selectA, \"select\");\n      that.isShowDownLoadDialogA = true;\n      that.inputpdf = 30;\n      // console.log(that.inputpdf, 'inputpdf2...');\n      let length = that.selectA.length;\n      setTimeout(async () => {\n        that.$refs[`tablePdf${length - 1}`] &&\n          that.$refs[`tablePdf${length - 1}`][0].$nextTick(() => {\n            setTimeout(async () => {\n              let pdfall = await Promise.all(\n                that.selectA.map(async (e, i) => {\n                  console.log(e, \"item..\");\n                  let newVar = await that.exportPdf(\n                    `tablePdf${i}`,\n                    true,\n                    `${e.symb}_${i}`,\n                    true,\n                    e.sysj\n                  );\n                  that.shengpdf = ((i + 1 / length) * 100).toFixed(2);\n                  return newVar;\n                })\n              );\n              htmlToPdf.zipChange(\n                pdfall,\n                \"试验报告_\" + new Date().getTime(),\n                (item, i) => {\n                  that.downloadpdf = ((i + 1 / length) * 100).toFixed(2);\n                }\n              );\n              console.log(\"pdf\", \"!!\");\n              that.inputpdf = 100;\n            }, 3000);\n          });\n        that.inputpdf = 60;\n        // console.log('pdf', '@@')\n      }, 3000);\n    },\n    //导出pdf\n    async exportPdf(refsname = \"tablePdf\", tableall, name, flag = false, sysj) {\n      let tablesss = this.$refs[refsname];\n      if (tablesss instanceof Array) {\n        tablesss = tablesss[0];\n      }\n      let element = tablesss.$el;\n\n      element.style.position = \"relative\";\n      var jlinput = \"\";\n      //批量导出标记\n      if (flag) {\n        for (const item in sysj) {\n          for (let i = 0; i < sysj[item].length; i++) {\n            if (sysj[item][i].nrbs === \"结论\") {\n              jlinput = sysj[item][i + 1].text;\n              break;\n            }\n          }\n        }\n      } else {\n        var img1 = \"\";\n        var img2 = \"\";\n        let jlDom = document.getElementById(this.jlData.objId);\n        if (jlDom) {\n          jlinput = this.isJlDisabled ? jlDom.innerText : jlDom.value;\n        }\n      }\n\n      if (/不合格/gi.test(jlinput)) {\n        img1 = \"/image/qualified.png\";\n        img2 = \"/image/test_test.png\";\n      } else if (/合格/gi.test(jlinput)) {\n        img1 = \"/image/unqualified.png\";\n        img2 = \"/image/test_test.png\";\n      }\n      //合格 or 不合格 盖章\n      var imgbox1 = document.createElement(\"div\");\n      imgbox1.style.position = \"absolute\";\n      imgbox1.id = \"gzimg1\";\n      imgbox1.style.bottom = \"0\";\n      imgbox1.style.right = \"0\";\n      imgbox1.style.top = \"0\";\n      imgbox1.style.marginRight = \"10px\";\n      imgbox1.style.marginTop = \"90px\";\n      var imgDocument1 = document.createElement(\"img\");\n      imgDocument1.setAttribute(\"src\", img1);\n      imgbox1.appendChild(imgDocument1);\n      element.appendChild(imgbox1);\n      //盖公章\n      var imgbox2 = document.createElement(\"div\");\n      imgbox2.style.position = \"absolute\";\n      imgbox2.id = \"gzimg2\";\n      imgbox2.style.bottom = \"0\";\n      imgbox2.style.right = \"0\";\n      imgbox2.style.top = \"0\";\n      imgbox2.style.marginRight = \"300px\";\n      imgbox2.style.marginTop = \"10px\";\n      var imgDocument2 = document.createElement(\"img\");\n      imgDocument2.setAttribute(\"src\", img2);\n      imgbox2.appendChild(imgDocument2);\n      element.appendChild(imgbox2);\n\n      await htmlToPdf.outPutPdfFn(element, \"splitClass\");\n      let pdf = await htmlToPdf.downloadPDF(element, name || this.titleName);\n      if (tableall) {\n        return pdf;\n      }\n      pdf.pdf.save(pdf.name);\n      this.closeYlDialog();\n      // 恢复原表格样式\n      let gzimg1 = document.getElementById(\"gzimg1\");\n      gzimg1 && gzimg1.remove();\n      let gzimg2 = document.getElementById(\"gzimg2\");\n      gzimg2 && gzimg2.remove();\n    },\n    showTest() {\n      let tablesss = this.$refs[\"tablePdf\"];\n      if (tablesss instanceof Array) {\n        tablesss = tablesss[0];\n      }\n      let element = tablesss.$el;\n\n      element.style.position = \"relative\";\n      var jlinput = \"\";\n      var img1 = \"\";\n      var img2 = \"\";\n      let jlDom = document.getElementById(this.jlData.objId);\n      if (jlDom) {\n        jlinput = this.isJlDisabled ? jlDom.innerText : jlDom.value;\n      }\n      if (/不合格/gi.test(jlinput)) {\n        img1 = \"/image/qualified.png\";\n        img2 = \"/image/test_test.png\";\n      } else if (/合格/gi.test(jlinput)) {\n        img1 = \"/image/unqualified.png\";\n        img2 = \"/image/test_test.png\";\n      } else {\n        return false;\n      }\n      //合格 or 不合格 盖章\n      var imgbox1 = document.createElement(\"div\");\n      imgbox1.style.position = \"absolute\";\n      imgbox1.id = \"gzimg1\";\n      imgbox1.style.bottom = \"0\";\n      imgbox1.style.right = \"0\";\n      imgbox1.style.top = \"0\";\n      imgbox1.style.marginRight = \"10px\";\n      imgbox1.style.marginTop = \"90px\";\n      var imgDocument1 = document.createElement(\"img\");\n      imgDocument1.setAttribute(\"src\", img1);\n      imgbox1.appendChild(imgDocument1);\n      element.appendChild(imgbox1);\n      //盖公章\n      var imgbox2 = document.createElement(\"div\");\n      imgbox2.style.position = \"absolute\";\n      imgbox2.id = \"gzimg2\";\n      imgbox2.style.bottom = \"0\";\n      imgbox2.style.right = \"0\";\n      imgbox2.style.top = \"0\";\n      imgbox2.style.marginRight = \"300px\";\n      imgbox2.style.marginTop = \"10px\";\n      var imgDocument2 = document.createElement(\"img\");\n      imgDocument2.setAttribute(\"src\", img2);\n      imgbox2.appendChild(imgDocument2);\n      element.appendChild(imgbox2);\n    },\n    //保存填写报告数据\n    saveYlDialog() {\n      this.$nextTick(() => {\n        // 以服务的方式调用的 Loading 需要异步关闭\n        this.loading = Loading.service({\n          lock: true, //lock的修改符--默认是false\n          text: \"流程进行中，请稍后\", //显示在加载图标下方的加载文案\n          spinner: \"el-icon-loading\", //自定义加载图标类名\n          background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n          target: document.querySelector(\"#dialogActst\")\n        });\n      });\n      //调用子组件中的方法，获取单元格的值\n      let params = this.$refs.tablePdf.getParams();\n      saveChildsValue({\n        symbid: this.saveData.symbid,\n        objId: this.saveData.objId,\n        params: params\n      }).then(res => {\n        if (res.code === \"0000\") {\n          this.$message({\n            type: \"success\",\n            message: \"保存成功!\"\n          });\n        }\n      });\n      //更新试验人员\n      updateSybgjl(this.clickRow).then(res => {\n        if (res.code == \"0000\") {\n          this.$nextTick(() => {\n            // 以服务的方式调用的 Loading 需要异步关闭\n            this.loading.close();\n          });\n          this.$message.success(\"操作成功！\");\n          this.isShowDetails = false;\n          this.isShowDownLoadDialog = false;\n          this.getData();\n        }\n      });\n    },\n    //报告记录列表查询\n    async getData(params) {\n      try {\n        this.queryParam = { ...this.queryParam, ...params };\n        this.$refs.sybglr.loading = true;\n        const param = { ...this.queryParam, ...params };\n        const { data, code } = await getSybgjlDataByPage(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          this.$nextTick(() => {\n            // 以服务的方式调用的 Loading 需要异步关闭\n            this.$refs.sybglr.loading = false;\n          });\n        }\n      } catch (e) {\n        this.$nextTick(() => {\n          // 以服务的方式调用的 Loading 需要异步关闭\n          this.$refs.sybglr.loading = false;\n        });\n      }\n    },\n    //试验模板点击事件\n    symbSelectedClick() {\n      if (this.form.sbmc === \"\" || this.form.sbmc === undefined) {\n        this.$message.warning(\"请选择试验设备\");\n        return;\n      }\n      this.isShowSymbDialog = true;\n    },\n    //试验模板接收数据\n    handleAcceptMbData(mbData) {\n      //模板名称\n      // this.form.symb = mbData.mbmc;\n      console.log(\"mbData\", mbData);\n      this.$set(this.form, \"symb\", mbData.mbmc);\n      //模板id\n      this.form.symbid = mbData.objId;\n    },\n    //关闭模板弹窗\n    handleControlSymbSelectDialog(isShow) {\n      this.isShowSymbDialog = isShow;\n    },\n    //组件接受设备参数数据\n    handleAcceptSbData(sbData) {\n      console.log(\"sbData\", sbData);\n      let sbmcS = [];\n      let sysbS = [];\n      sbData.forEach(item => {\n        sbmcS.push(item.sbmc);\n        sysbS.push(item.objId);\n      });\n      this.form.sbmc = sbmcS.join(\",\");\n      this.form.sysbid = sysbS.join(\",\");\n      this.symbData.sblxid = sbData[0].sblxbm;\n      this.form.yxbh = sbData[0].wzmc;\n      this.form.tyrq = sbData[0].tyrq;\n      // this.form.sbmc = sbData.sbmc;\n      // this.form.sysbid = sbData.objId;\n      // console.log(\"sbData\",sbData);\n      // //设备类型编码\n      // //线路 sblxbm sblxbm  sblxbm\n      // this.symbData.sblxid=sbData.sblxbm;\n    },\n    //控制关闭试验设备弹出框\n    handleControlSysbSelectDialog(isShow) {\n      this.isShowSysbDialog = isShow;\n    },\n    //获取试验单位数据\n    getOrganizationSelected() {\n      let parentId = \"1001\";\n      getOrganizationSelected({ parentId: parentId }).then(res => {\n        res.data.forEach((item, index) => {\n          if (item.value === 4002) {\n            let sydw = {};\n            sydw.label = item.label;\n            sydw.value = item.value;\n            this.sydwList.push(sydw);\n          }\n        });\n      });\n    },\n    //接受试验设备地点树结构数据\n    handleAccessTreeData(treeNode) {\n      console.log(\"treeNode\", treeNode);\n      if (this.mainData.lx === \"\") {\n        console.log(\"this.mainData.lx\", \"123\");\n        let syddmc = document.getElementsByName(\"syddmc\");\n        console.log(\"syddmc\", syddmc);\n        if (syddmc) {\n          syddmc[0].value = treeNode.label;\n          this.queryParam.syddid = treeNode.id;\n        }\n      }\n      //给表单设备地点赋值\n      this.form.sydd = treeNode.label;\n      this.basicData.sydd = treeNode.label;\n      //数据库存储值\n      this.form.syddid = treeNode.id;\n      this.$set(this.form, \"sbmc\", \"\");\n    },\n    //子组件关闭试验设备地点弹窗\n    handleCloseSyddDialog(syddDialogControl) {\n      this.isShowSysbddDialog = syddDialogControl;\n    },\n    //获取试验专业下拉框数据\n    getOptions() {\n      getSyzySelectedOptions().then(res => {\n        this.syzyList = res.data;\n        this.filterInfo.fieldList.forEach(item => {\n          if (item.value == \"syzy\") {\n            item.options = this.syzyList;\n          }\n        });\n      });\n      getDictTypeData(\"sybg_ztcl\").then(res => {\n        this.filterInfo.fieldList.forEach(item => {\n          if (item.value == \"ishg\") {\n            item.options = res.data.map(e => {\n              e.value = e.numvalue\n              return e\n            });\n          }\n        });\n      });\n    },\n    //根据试验专业编码获取试验性质信息\n    selectSyxzDataBySyzybm() {\n      //change事件发生改变时，清空之前试验性质\n      this.$set(this.form, \"syxzbm\", \"\");\n      this.syxzList = [];\n      let syzybm = this.form.syzybm;\n      selectSyxzDataBySyzybm({ syzybm }).then(response => {\n        this.syxzList = response.data;\n      });\n    },\n    //点击修改详情按钮时回显试验性质时使用\n    selectSyxzDataBySyzybm1() {\n      this.syxzList = [];\n      let syzybm = this.form.syzybm;\n      selectSyxzDataBySyzybm({ syzybm }).then(response => {\n        this.syxzList = response.data;\n      });\n    },\n\n    //设备专业发生变化时，清空设备地点数据\n    async selectsbzy(val) {\n      this.$set(this.form, \"sydd\", \"\");\n      let sbddall = [];\n      this.sbddList = [];\n      switch (val) {\n        case \"变电设备\":\n          sbddall = await getbdzList({});\n          sbddall.forEach((item, index) => {\n            let bdz = {};\n            bdz.label = item.bdzmc;\n            bdz.value = item.objId;\n            this.sbddList.push(bdz);\n          });\n          break;\n        case \"输电设备\":\n          let { data } = await getSdLineSelected({});\n          this.sbddList = data;\n          break;\n        case \"配电设备\":\n          await this.getPdsSelected();\n          break;\n      }\n    },\n\n    onclckdd(val) {\n      console.log(val);\n      this.$set(this.form, \"sydd\", val.label);\n      this.$set(this.form, \"syddid\", val.value);\n    },\n\n    //获取配电室下拉数据\n    async getPdsSelected() {\n      this.sbddList = [];\n      await getPdsSelected({}).then(res => {\n        this.sbddList = res.data;\n      });\n    },\n\n    //设备地点发生变化时，清空试验设备的数据\n    changeInput() {\n      console.log(\"123123\");\n      this.$set(this.form, \"sbmc\", \"\");\n    },\n\n    //试验设备地点点击事件\n    sysbddClick() {\n      if (this.form.sjzy === \"\" || this.form.sjzy === undefined) {\n        this.$message.warning(\"请选择设备专业\");\n        return;\n      }\n      if (this.form.sjzy === \"变电设备\") {\n        this.mainData.lx = \"2\";\n      } else if (this.form.sjzy === \"配电设备\") {\n        this.mainData.lx = \"3\";\n      } else if (this.form.sjzy === \"输电设备\") {\n        this.mainData.lx = \"1\";\n      }\n      this.isShowSysbddDialog = true;\n    },\n    //试验设备选择事件\n    sysbSelectedClick() {\n      //试验设备发生变化时，清空试验模板得数据\n      this.$set(this.form, \"symb\", \"\");\n      if (this.form.sjzy === \"\" || this.form.sjzy === undefined) {\n        this.$message.warning(\"请选择设备专业\");\n        return;\n      }\n      if (this.form.sydd === \"\" || this.form.sydd === undefined) {\n        this.$message.warning(\"请选择设备地点\");\n        return;\n      }\n      if (this.form.sjzy === \"变电设备\") {\n        this.selectedSbParam.lx = \"bd\";\n        this.selectedSbParam.fsss = \"附属\";\n      } else if (this.form.sjzy === \"配电设备\") {\n        this.selectedSbParam.lx = \"pd\";\n      } else if (this.form.sjzy === \"输电设备\") {\n        this.selectedSbParam.lx = \"sd\";\n      }\n      this.selectedSbParam.sbmc = this.form.sydd; //所属位置\n      this.isShowSysbDialog = true;\n    },\n    //重置按钮\n    getReset(data) {\n      //不可以清空设备专业\n      // let syddmc = document.getElementsByName(\"syddmc\");\n      // console.log(\"syddmc\", syddmc);\n      // if (syddmc) {\n      //   syddmc[0].value = \"\";\n      // }\n      this.queryParam = {};\n    },\n    //下拉框change事件\n    async handleEvent(val, val1) {\n      //变电站查询下拉框选项根据所选分公司带出来\n      if (val.label === \"sjzy\" && val.value && val.value !== \"\") {\n        this.$set(val1, \"syddid\", \"\");\n        await this.selectsbzy(val.value);\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"syddid\") {\n            console.log(\"options_syddid\", this.sbddList);\n            return (item.options = this.sbddList);\n          }\n        });\n      }\n    },\n    //新增按钮\n    getInsert() {\n      //清空试验性质数据\n      this.syxzList = [];\n      this.title = \"新建试验报告\";\n      //表单不禁用\n      this.isDisabled = false;\n      //展示取消确认按钮\n      this.isShowDetails = true;\n      this.isShow = true;\n\n      //清空表单\n      this.form = {};\n      this.form.ishg = 1;\n    },\n\n    initFormData() {\n\n    },\n\n    //   handleSelectionChange(selection) {\n    //   this.selectData = selection\n    // },\n    //历史数据录入\n    getInsertls() {\n      //清空试验性质数据\n      this.syxzList = [];\n      this.title = \"新建试验报告\";\n      //表单不禁用\n      this.isDisabled = false;\n      this.zyisDisabled = true;\n      //展示取消确认按钮\n      this.isShowDetails = true;\n      this.isShow = true;\n      this.syryDialog = false;\n      //清空表单\n      this.form = {};\n      this.form.ishg = 6;\n    },\n\n    //删除按钮\n    deleteRow(objId) {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          removeSybgjlData(JSON.stringify(objId)).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    //表格多选框\n    handelSelectChange(rows) {\n      this.selectRows = rows;\n      console.log(this.selectRows, \"123\");\n    },\n    //取消按钮\n    close() {\n      this.isShowDetails = false;\n    },\n    //保存按钮\n    save() {\n      this.$refs.form.validate(valid => {\n        if (valid) {\n          saveOrUpdateSybgjl(this.form).then(res => {\n            if (res.code == \"0000\") {\n              this.$message.success(\"操作成功！\");\n              this.isShowDetails = false;\n              this.getData();\n            }\n          });\n        }\n      });\n    },\n\n    //上报、提交审核\n    async commitSh() {\n      let type = \"complete\";\n      if (this.ishg == 1) {\n        //上报\n        //当前状态为新建，下一环节班组长审批\n        this.processData.variables.ishg = 5;\n        this.processData.variables.title = \"班组长审批\";\n        this.processData.defaultFrom = true;\n        this.processData.jxgs = false;\n      } else if (this.ishg == 5) {\n        //当前状态为班组长审批，下一环节分公司审核\n        this.processData.variables.ishg = 2;\n        this.processData.variables.title = \"分公司审核\";\n        this.processData.defaultFrom = true;\n        this.processData.jxgs = false;\n      } else if (this.ishg == 2) {\n        //当前状态为分公司审核，下一环节属地单位审核\n        this.processData.variables.ishg = 7;\n        this.processData.variables.title = \"属地单位审核\";\n        this.processData.defaultFrom = true;\n        this.processData.jxgs = false;\n        type = \"completeMany\"; //一发多收\n      } else if (this.ishg == 7) {\n        this.processData.variables.ishg = 3;\n        this.processData.variables.title = \"生产科专工审核\";\n        this.processData.defaultFrom = true;\n        this.processData.jxgs = false;\n      } else if (this.ishg == 3) {\n        this.processData.variables.ishg = 3;\n        this.processData.variables.title = \"合格验收\";\n        this.processData.defaultFrom = false;\n        this.processData.jxgs = false;\n      } else if (this.ishg == 4) {\n        //当前状态关闭\n        this.$message.warning(\"当前流程已关闭！！无法提交审核\");\n        return;\n      }\n      console.log(\"this.clickRow\", this.clickRow);\n      //开始执行请求\n      this.getSbFsBj(\n        { data: this.clickRow, type: type },\n        {\n          defaultForm: this.processData.defaultFrom,\n          jxgs: this.processData.jxgs\n        }\n      );\n      //详情框关闭\n      this.closeYlDialog();\n      //恢复分页\n      this.tableAndPageInfo.pager.pageResize = \"Y\";\n      //重新查数据\n      await this.getData();\n    },\n    //流程图片查看\n    showProcessImg(row) {\n      this.openLoadingImg = true;\n      this.imgSrc =\n        \"/activiti-api/process/read-resource?processDefinitionKey=sybgsh&businessKey=\" +\n        row.objId +\n        \"&t=\" +\n        new Date().getTime();\n    },\n\n    //上报发送办结\n    getSbFsBj(args, isShow) {\n      let row = { ...args.data };\n      this.activitiOption.title = args.title;\n      this.processData.variables.zy = row.sjzy;\n      if (args.type === \"complete\") {\n        this.processData.defaultFrom = isShow.defaultForm; //是否显示选人员\n        this.processData.processType = \"complete\";\n        this.processData.jxgs = isShow.jxgs;\n        this.processData.businessKey = row.objId;\n        this.processData.variables.pass = true;\n      } else if (args.type === \"completeMany\") {\n        //一发多收\n        this.processData.defaultFrom = isShow.defaultForm; //是否显示选人员\n        this.processData.processType = \"completeMany\";\n        this.processData.jxgs = isShow.jxgs;\n        this.processData.businessKey = row.objId;\n        this.processData.variables.pass = true;\n      } else {\n        this.processData.variables.title = \"回退原因提报\";\n        this.processData.defaultFrom = true;\n        this.processData.processType = \"rollback\";\n        this.processData.businessKey = row.objId;\n        this.processData.variables.pass = false;\n      }\n      this.isShowActiviti = true;\n    },\n    //回退按钮\n    handleTh(type) {\n      this.getSbFsBj(\n        { type: type, data: this.clickRow },\n        { defaultForm: false }\n      );\n    },\n\n    async showTimeLine(row) {\n      let { code, data } = await HistoryList({ businessKey: row.objId });\n      this.timeData = data;\n      this.timeLineShow = true;\n    },\n\n    //关闭工作流弹窗\n    closeActiviti() {\n      this.isShowActiviti = false;\n    },\n\n    //关闭时间线查看页面\n    colseTimeLine() {\n      this.timeLineShow = false;\n    },\n\n    //工作流回传数据\n    async todoResult(data) {\n      console.log(\"this.data\", data);\n      switch (data.activeTaskName) {\n        case \"试验报告填写\":\n          this.ishg = 1;\n          break;\n        case \"领导审批\":\n          this.ishg = 2;\n          break;\n        case \"属地单位审批\":\n          this.ishg = 7;\n          break;\n        case \"生产科专工审批\":\n          this.ishg = 3;\n          break;\n        case \"结束\":\n          this.ishg = 4;\n          break;\n        case \"班组审批\":\n          this.ishg = 5;\n          break;\n      }\n      let row = {};\n      if (this.ishg == 2) {\n        row = {\n          objId: data.businessKey,\n          ishg: this.ishg,\n          ssdwld: data.nextUser,\n          bgrq: formatterDateTime(new Date(), \"yyyy-MM-dd\") //设置报告日期字段，为批准人审批时间\n        };\n      } else if (this.ishg == 3) {\n        row = {\n          objId: data.businessKey,\n          ishg: this.ishg,\n          sckzg: data.nextUser\n        };\n      } else if (this.ishg == 5) {\n        row = {\n          objId: data.businessKey,\n          ishg: this.ishg,\n          bzzsp: data.nextUserNickName\n        };\n      } else if (this.ishg == 7) {\n        row = {\n          objId: data.businessKey,\n          ishg: this.ishg,\n          sddwshr: data.nextUser\n        };\n      } else {\n        row = { objId: data.businessKey, ishg: this.ishg };\n      }\n      console.log(\"row\", row);\n      updateSybgjl(row).then(res => {\n        if (res.code === \"0000\") {\n          this.$message.success(\"操作成功\");\n          //重置page页从1开始\n          this.tableAndPageInfo.pager.pageResize = \"Y\";\n          this.getData();\n        }\n      });\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.button-group {\n  padding-left: 30px;\n  padding-right: 30px;\n  display: flex;\n  justify-content: flex-end;\n}\n\n// #h2_table,\n// #h3_table {\n//   border-bottom: 1px solid #000;\n//   tr {\n//     //td:nth-child(1){\n//     //  border-left:none;\n//     //}\n//     td {\n//       border-left: 1px solid #000;\n//       border-top: 1px solid #000\n//     }\n//   }\n//   text-align: center;\n// }\n\n// #h1_table {\n//   width: 100%;\n//   text-align: center;\n//   //border-right:1px solid #000;\n//   border-bottom: 1px solid #000;\n\n//   tr {\n//     line-height: 35px;\n\n//     //td:nth-child(1){\n//     //  border-left:none;\n//     //}\n//     td {\n//       border-left: 1px solid #000;\n//       border-top: 1px solid #000\n//     }\n//   }\n// }\n\n// #saveCont {\n//   border: 1px solid #000;\n//   width: 100%;\n//   height: 60vh;\n//   overflow: auto;\n// }\n\n// .printTitle {\n//   line-height: 35px;\n// }\n\n// /deep/ #h2_table tr,\n// /deep/ #h3_table tr {\n//   height: 35px;\n// }\n\n// /deep/ #h2_table td,\n// /deep/ #h3_table td {\n//   border: 1px solid #000\n// }\n\n// /deep/ #h2_table input,\n// /deep/ #h3_table input {\n//   display: inline-block;\n//   height: 35px;\n// }\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk"}]}