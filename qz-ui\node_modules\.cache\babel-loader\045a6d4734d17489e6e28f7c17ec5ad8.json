{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\sbztpjbzk\\ztlxxd.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\sbztpjbzk\\ztlxxd.js", "mtime": 1706897314226}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0UGFnZVp0bHh4ZCA9IGdldFBhZ2VadGx4eGQ7CmV4cG9ydHMuc2F2ZU9yVXBkYXRlID0gc2F2ZU9yVXBkYXRlOwpleHBvcnRzLnJlbW92ZSA9IHJlbW92ZTsKCnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwoKdmFyIGJhc2VVcmwgPSAiL2NvbmRpdGlvbi1tYWludGVuYW5jZS1hcGkiOwoKZnVuY3Rpb24gZ2V0UGFnZVp0bHh4ZChwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy96dGx4eGQvZ2V0UGFnZVp0bHh4ZCcsIHBhcmFtcywgMSk7Cn0gLy8g5re75Yqg5oiW5L+u5pS5CgoKZnVuY3Rpb24gc2F2ZU9yVXBkYXRlKHBhcmFtcykgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3RQb3N0KGJhc2VVcmwgKyAnL3p0bHh4ZC9zYXZlT3JVcGRhdGUnLCBwYXJhbXMsIDEpOwp9IC8vIOWIoOmZpAoKCmZ1bmN0aW9uIHJlbW92ZShwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy96dGx4eGQvZGVsZXRlWnRseHhkJywgcGFyYW1zLCAxKTsKfQ=="}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagang<PERSON>field/bzgl/sbztpjbzk/ztlxxd.js"], "names": ["baseUrl", "getPageZtlxxd", "params", "api", "requestPost", "saveOrUpdate", "remove"], "mappings": ";;;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,4BAAhB;;AAEO,SAASC,aAAT,CAAuBC,MAAvB,EAA+B;AAClC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,uBAAxB,EAAgDE,MAAhD,EAAuD,CAAvD,CAAP;AACD,C,CAED;;;AACO,SAASG,YAAT,CAAsBH,MAAtB,EAA8B;AACnC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,sBAAxB,EAA+CE,MAA/C,EAAsD,CAAtD,CAAP;AACD,C,CACA;;;AACM,SAASI,MAAT,CAAgBJ,MAAhB,EAAwB;AAC7B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,sBAAxB,EAA+CE,MAA/C,EAAsD,CAAtD,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\r\nconst baseUrl = \"/condition-maintenance-api\";\r\n\r\nexport function getPageZtlxxd(params) {\r\n    return api.requestPost(baseUrl+'/ztlxxd/getPageZtlxxd',params,1)\r\n  }\r\n  \r\n  // 添加或修改\r\n  export function saveOrUpdate(params) {\r\n    return api.requestPost(baseUrl+'/ztlxxd/saveOrUpdate',params,1)\r\n  }\r\n   // 删除\r\n  export function remove(params) {\r\n    return api.requestPost(baseUrl+'/ztlxxd/deleteZtlxxd',params,1)\r\n  }"]}]}