{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\activiti\\todoitem.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\activiti\\todoitem.js", "mtime": 1706897313850}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMubGlzdFRvZG8gPSBsaXN0VG9kbzsKZXhwb3J0cy5saXN0RG9uZSA9IGxpc3REb25lOwpleHBvcnRzLmdldEJ1c2luZXNzRGF0YSA9IGdldEJ1c2luZXNzRGF0YTsKZXhwb3J0cy5jb21wbGV0ZSA9IGNvbXBsZXRlOwoKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Cgp2YXIgYmFzZVVybCA9ICIvYWN0aXZpdGktYXBpIjsKLyoqCiAqIOafpeivouaIkeeahOW+heWKnuWIl+ihqAogKiBAcGFyYW0gcXVlcnkg6K+35rGC5Y+C5pWwCiAqIEByZXR1cm5zIHtQcm9taXNlPGFueT59CiAqLwoKZnVuY3Rpb24gbGlzdFRvZG8ocXVlcnkpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgIi90b2RvaXRlbS9saXN0IiwgcXVlcnksIDMpOwp9Ci8qKgogKiDmn6Xor6LmiJHnmoTlt7Llip7liJfooagKICogQHBhcmFtIHF1ZXJ5IOivt+axguWPguaVsAogKiBAcmV0dXJucyB7UHJvbWlzZTxhbnk+fQogKi8KCgpmdW5jdGlvbiBsaXN0RG9uZShxdWVyeSkgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3RQb3N0KGJhc2VVcmwgKyAiL3RvZG9pdGVtL2RvbmUiLCBxdWVyeSwgMyk7Cn0KLyoqCiAqIOiOt+WPluS4muWKoeaVsOaNrgogKiBAcGFyYW0gZGF0YQogKiBAcmV0dXJucyB7UHJvbWlzZSB8IFByb21pc2U8YW55Pn0KICovCgoKZnVuY3Rpb24gZ2V0QnVzaW5lc3NEYXRhKGRhdGEpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0R2V0KGJhc2VVcmwgKyAiL3RvZG9pdGVtL2dldEJ1c2luZXNzRGF0YSIsIGRhdGEsIDMpOwp9Ci8qKgogKiDlrqHmoLgKICogQHBhcmFtIGRhdGEg6KGo5Y2V5a6e5L2TCiAqIEBwYXJhbSB0YXNrSWQg5Lu75YqhaWQKICogQHJldHVybnMge1Byb21pc2UgfCBQcm9taXNlPGFueT59CiAqLwoKCmZ1bmN0aW9uIGNvbXBsZXRlKGRhdGEsIHRhc2tJZCkgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3RQb3N0KGJhc2VVcmwgKyAiL3RvZG9pdGVtL2NvbXBsZXRlLyIgKyB0YXNrSWQsIGRhdGEsIDMpOwp9"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/activiti/todoitem.js"], "names": ["baseUrl", "listTodo", "query", "api", "requestPost", "listDone", "getBusinessData", "data", "requestGet", "complete", "taskId"], "mappings": ";;;;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,eAAhB;AACA;;;;;;AAKO,SAASC,QAAT,CAAkBC,KAAlB,EAAyB;AAC9B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,gBAAxB,EAAyCE,KAAzC,EAA+C,CAA/C,CAAP;AACD;AAED;;;;;;;AAKO,SAASG,QAAT,CAAkBH,KAAlB,EAAyB;AAC9B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,gBAAxB,EAAyCE,KAAzC,EAA+C,CAA/C,CAAP;AACD;AAED;;;;;;;AAKO,SAASI,eAAT,CAAyBC,IAAzB,EAA+B;AACpC,SAAOJ,iBAAIK,UAAJ,CAAeR,OAAO,GAAC,2BAAvB,EAAmDO,IAAnD,EAAwD,CAAxD,CAAP;AACD;AAED;;;;;;;;AAMO,SAASE,QAAT,CAAkBF,IAAlB,EAAuBG,MAAvB,EAA+B;AACpC,SAAOP,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,qBAAR,GAA8BU,MAA9C,EAAqDH,IAArD,EAA0D,CAA1D,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\nconst baseUrl = \"/activiti-api\";\n/**\n * 查询我的待办列表\n * @param query 请求参数\n * @returns {Promise<any>}\n */\nexport function listTodo(query) {\n  return api.requestPost(baseUrl+\"/todoitem/list\",query,3)\n}\n\n/**\n * 查询我的已办列表\n * @param query 请求参数\n * @returns {Promise<any>}\n */\nexport function listDone(query) {\n  return api.requestPost(baseUrl+\"/todoitem/done\",query,3)\n}\n\n/**\n * 获取业务数据\n * @param data\n * @returns {Promise | Promise<any>}\n */\nexport function getBusinessData(data) {\n  return api.requestGet(baseUrl+\"/todoitem/getBusinessData\",data,3)\n}\n\n/**\n * 审核\n * @param data 表单实体\n * @param taskId 任务id\n * @returns {Promise | Promise<any>}\n */\nexport function complete(data,taskId) {\n  return api.requestPost(baseUrl+\"/todoitem/complete/\"+taskId,data,3)\n}\n"]}]}