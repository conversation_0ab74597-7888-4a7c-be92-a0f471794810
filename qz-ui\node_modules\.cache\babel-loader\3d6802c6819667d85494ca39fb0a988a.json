{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\xldzcz\\dzczp.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\xldzcz\\dzczp.vue", "mtime": 1719919561036}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["dzczp.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwnBA;;AACA;;AAMA;;AACA;;AAOA;;AAEA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAJA;eAKA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,YAAA,EAAA,qBAAA;AAAA,IAAA,QAAA,EAAA,qBAAA;AAAA,IAAA,QAAA,EAAA,iBAAA;AAAA,IAAA,aAAA,EAAA;AAAA,GAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA,MAAA,YAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,YADA;AAEA,MAAA,OAAA,EAAA,KAFA;AAGA,MAAA,YAAA,EAAA,IAHA;AAIA,MAAA,cAAA,EAAA,KAJA;AAKA,MAAA,UAAA,EAAA,EALA;AAMA,MAAA,KAAA,EAAA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,YAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,YAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,GAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAPA;AAQA,QAAA,KAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CARA;AAWA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAXA;AAcA;AACA;AACA,QAAA,GAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAhBA;AAiBA;AACA;AACA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CApBA;AAuBA,QAAA,OAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,WAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAvBA;AA0BA,QAAA,OAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,WAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CA1BA;AA6BA,QAAA,KAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,WAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AA7BA,OANA;AAuCA;AACA,MAAA,aAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA;AACA;AACA;AACA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,EAaA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAbA,EAiBA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAjBA,EAoBA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OApBA,CAxCA;AA8DA,MAAA,GAAA,EAAA,EA9DA;AA+DA;AACA,MAAA,SAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,CAhEA;AAoEA;AACA,MAAA,WAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,IArEA;AAsEA;AACA,MAAA,aAAA,EAAA;AACA,QAAA,UAAA,EAAA,EADA,CACA;;AADA,OAvEA;AA0EA;AACA,MAAA,MAAA,EAAA,EA3EA;AA4EA;AACA,MAAA,OAAA,EAAA,EA7EA;AA8EA;AACA,MAAA,cAAA,EAAA,EA/EA;AAgFA;AACA,MAAA,gBAAA,EAAA,KAjFA;AAkFA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,oBAAA,EAAA,OADA;AAEA,QAAA,WAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,OAHA;AAIA,QAAA,SAAA,EAAA,EAJA;AAKA,QAAA,WAAA,EAAA,IALA;AAMA,QAAA,QAAA,EAAA,EANA;AAOA,QAAA,WAAA,EAAA;AAPA,OAnFA;AA4FA;AACA,MAAA,MAAA,EAAA,KA7FA;AA8FA;AACA,MAAA,cAAA,EAAA,KA/FA;AAgGA,MAAA,MAAA,EAAA,EAhGA;AAgGA;AACA,MAAA,QAAA,EAAA,EAjGA;AAkGA,MAAA,YAAA,EAAA,KAlGA;AAmGA;AACA,MAAA,cAAA,EAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OApGA;AAqGA,MAAA,OAAA,EAAA,EArGA;AAsGA,MAAA,EAAA,EAAA,KAtGA;AAuGA,MAAA,SAAA,EAAA,KAvGA;AAwGA;AACA,MAAA,cAAA,EAAA,KAzGA;AA0GA;AACA,MAAA,GAAA,EAAA,EA3GA;AA4GA,MAAA,MAAA,EAAA,IA5GA;AA6GA,MAAA,QAAA,EAAA,IA7GA;AA8GA,MAAA,UAAA,EAAA,EA9GA;AA+GA;AACA,MAAA,aAAA,EAAA;AACA,QAAA,GAAA,EAAA,IADA;AACA;AACA,QAAA,QAAA,EAAA;AAFA,OAhHA;AAoHA;AACA,MAAA,IAAA,EAAA;AACA,QAAA,EAAA,EAAA,EADA;AAEA,QAAA,MAAA,EAAA,EAFA;AAGA,QAAA,GAAA,EAAA,EAHA;AAIA,QAAA,GAAA,EAAA,EAJA;AAKA,QAAA,KAAA,EAAA,EALA;AAMA,QAAA,EAAA,EAAA,CANA;AAMA;AACA,QAAA,QAAA,EAAA,EAPA;AAQA,QAAA,EAAA,EAAA;AARA,OArHA;AA+HA,MAAA,OAAA,EAAA;AACA,QAAA,KAAA,EAAA;AADA,OA/HA;AAkIA;AACA,MAAA,aAAA,EAAA,KAnIA;AAoIA;AACA,MAAA,UAAA,EAAA,KArIA;AAsIA;AACA,MAAA,KAAA,EAAA,EAvIA;AAwIA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,EAAA,EAAA,EADA;AAEA,UAAA,MAAA,EAAA,EAFA;AAGA,UAAA,IAAA,EAAA,EAHA;AAIA,UAAA,GAAA,EAAA,EAJA;AAKA,UAAA,IAAA,EAAA,EALA;AAMA,UAAA,IAAA,EAAA,EANA;AAOA,UAAA,GAAA,EAAA,EAPA;AAQA,UAAA,GAAA,EAAA,EARA;AASA,UAAA,KAAA,EAAA,EATA;AAUA,UAAA,KAAA,EAAA;AAVA,SADA;AAYA;AACA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SADA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SALA,EAMA;AACA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,KAAA,EAAA,OAFA;AAGA,UAAA,IAAA,EAAA,MAHA;AAIA,UAAA,QAAA,EAAA,WAJA;AAKA,UAAA,MAAA,EAAA;AALA,SAPA,EAcA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,KAAA,EAAA,QAFA;AAGA,UAAA,IAAA,EAAA,UAHA;AAIA,UAAA,aAAA,EAAA,EAJA;AAKA,UAAA,SAAA,EAAA,IALA;AAMA,UAAA,OAAA,EAAA,CACA;AACA,YAAA,KAAA,EAAA,GADA;AAEA,YAAA,KAAA,EAAA;AAFA,WADA;AAKA;;;;AAIA;AACA,YAAA,KAAA,EAAA,GADA;AAEA,YAAA,KAAA,EAAA;AAFA,WATA,EAaA;AACA,YAAA,KAAA,EAAA,GADA;AAEA,YAAA,KAAA,EAAA;AAFA,WAbA,EAgBA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAhBA;AANA,SAdA;AAbA,OAxIA;AA8LA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,YAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SARA,CARA;AAkBA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAlBA,OA9LA;AAkNA,MAAA,MAAA,EAAA;AACA;AACA,QAAA,EAAA,EAAA,CAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAlNA;AAuNA,MAAA,UAAA,EAAA;AAvNA,KAAA;AAyNA,GA7NA;AA8NA,EAAA,OA9NA,qBA8NA;AACA;AACA,SAAA,MAAA,CAAA,KAAA,GAAA,qBAAA,CAFA,CAGA;;AACA,SAAA,OAAA,CAAA,KAAA,MAAA,CAAA,KAAA;AACA,GAnOA;AAoOA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,UAFA,sBAEA,KAFA,EAEA;AAAA;;AACA,WAAA,QAAA,CAAA,0BAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAKA,IALA,qFAKA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,yBAAA;AAAA,kBAAA,MAAA,EAAA,CAAA;AAAA,kBAAA,KAAA,EAAA;AAAA,iBAAA,CADA;;AAAA;AAAA;AACA,gBAAA,IADA,qBACA,IADA;;AAEA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,KAAA,CAAA,QAAA,CAAA,OAAA,CAAA,QAAA;;AACA,kBAAA,KAAA,CAAA,OAAA;AACA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OALA,IAYA,KAZA,CAYA,YAAA,CAAA,CAZA;AAaA,KAhBA;AAiBA,IAAA,OAjBA,qBAiBA;AACA,WAAA,cAAA,GAAA,KAAA;;AACA,cAAA,KAAA,IAAA,CAAA,MAAA;AACA,aAAA,GAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,WAAA,KAAA,KAAA,IAAA,CAAA,QAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA;;AACA;;AACA,aAAA,GAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,IAAA,CAAA,KAAA,KAAA,KAAA,WAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA;;AACA;;AACA,aAAA,GAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,IAAA,CAAA,MAAA,KAAA,KAAA,WAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA;;AACA;;AACA,aAAA,GAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,IAAA,CAAA,GAAA,KAAA,KAAA,WAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA,iBAAA,YAAA,GAAA,KAAA;AACA;;AACA;AAzBA;AA2BA,KA9CA;AA+CA;AACA,IAAA,UAhDA,wBAgDA;AAAA;;AACA,UAAA,MAAA,GAAA,EAAA,CADA,CACA;AACA;;AACA,UAAA,SAAA,GAAA,CAAA,KAAA,EAAA,KAAA,CAAA;AAEA,UAAA,QAAA,GAAA,IAAA,QAAA,EAAA,CALA,CAMA;;AACA,WAAA,OAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,GAAA,IAAA,SAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA;AACA;;AACA,QAAA,QAAA,CAAA,MAAA,CAAA,OAAA,EAAA,IAAA,CAAA,GAAA;AACA,OALA;AAMA,MAAA,QAAA,CAAA,MAAA,CAAA,YAAA,EAAA,KAAA,aAAA,CAAA,UAAA,EAbA,CAaA;;AACA,MAAA,QAAA,CAAA,MAAA,CAAA,QAAA,EAAA,MAAA,EAdA,CAcA;;AACA,MAAA,QAAA,CAAA,MAAA,CAAA,MAAA,EAAA,SAAA,EAfA,CAeA;;AACA,uBACA,WADA,CACA,2BADA,EACA,QADA,EACA,CADA,EAEA,IAFA,CAEA,UAAA,GAAA,EAAA;AACA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,EAAA,CAFA,CAGA;;AACA,QAAA,MAAA,CAAA,OAAA;AACA,OAPA,EAQA,KARA,CAQA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,SAAA;AACA,OAVA;AAWA,KA3EA;AA4EA;AACA,IAAA,YA7EA,wBA6EA,IA7EA,EA6EA,QA7EA,EA6EA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,KA/EA;AAgFA,IAAA,cAhFA,0BAgFA,KAhFA,EAgFA,IAhFA,EAgFA,QAhFA,EAgFA,CAAA,CAhFA;AAiFA;AACA,IAAA,YAlFA,wBAkFA,IAlFA,EAkFA,QAlFA,EAkFA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,KApFA;AAqFA;AACA,IAAA,wBAtFA,oCAsFA,IAtFA,EAsFA;AACA,WAAA,cAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA;AACA,WAAA,gBAAA,GAAA,IAAA;AACA,KAzFA;AA0FA,IAAA,OA1FA,qBA0FA;AACA,WAAA,gBAAA,GAAA,KAAA;AACA,KA5FA;AA6FA;AACA,IAAA,MA9FA,kBA8FA,CA9FA,EA8FA;AACA,WAAA,YAAA;AACA,KAhGA;AAiGA;AACA,IAAA,aAlGA,2BAkGA;AACA,UAAA,KAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,EAAA;AACA,aAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,CAAA,WAAA;AACA;;AACA,WAAA,MAAA,GAAA,KAAA;AACA,KAvGA;AAwGA;AACA,IAAA,aAzGA,2BAyGA;AACA,WAAA,YAAA,GAAA,KAAA;AACA,KA3GA;AA4GA,IAAA,YA5GA,wBA4GA,GA5GA,EA4GA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AADA;AAAA,uBAEA,8BAAA,MAAA,CAAA,WAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,sBAEA,IAFA;AAEA,gBAAA,IAFA,sBAEA,IAFA;AAGA,gBAAA,MAAA,CAAA,QAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,YAAA,GAAA,IAAA;;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,KAjHA;AAkHA;AACA,IAAA,cAnHA,0BAmHA,GAnHA,EAmHA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,WAAA,MAAA,GACA,gFACA,GAAA,CAAA,KADA,GAEA,KAFA,GAGA,IAAA,IAAA,GAAA,OAAA,EAJA;AAKA,KA1HA;AA2HA;AACA,IAAA,UA5HA,sBA4HA,IA5HA,EA4HA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,GADA,GACA;AACA,kBAAA,KAAA,EAAA,IAAA,CAAA,WADA;AAEA,kBAAA,OAAA,EAAA,CAFA;AAGA,kBAAA,MAAA,EAAA,IAAA,CAAA,WAAA,KAAA,UAAA,GAAA,CAAA,GAAA;AAHA,iBADA;;AAAA,sBAMA,IAAA,CAAA,WAAA,KAAA,UANA;AAAA;AAAA;AAAA;;AAAA,+BAOA,IAAA,CAAA,cAPA;AAAA,kDAQA,OARA,wBAWA,OAXA;AAAA;;AAAA;AASA,gBAAA,GAAA,CAAA,MAAA,GAAA,GAAA;AATA;;AAAA;AAYA,gBAAA,GAAA,CAAA,MAAA,GAAA,GAAA;AAZA;;AAAA;AAAA;AAAA;;AAAA;AAAA,+BAgBA,IAAA,CAAA,cAhBA;AAAA,kDAiBA,OAjBA,yBAqBA,OArBA,yBAyBA,IAzBA;AAAA;;AAAA;AAkBA,gBAAA,GAAA,CAAA,MAAA,GAAA,GAAA;AACA,gBAAA,GAAA,CAAA,MAAA,GAAA,IAAA,CAAA,QAAA;AAnBA;;AAAA;AAsBA,gBAAA,GAAA,CAAA,MAAA,GAAA,GAAA;AACA,gBAAA,GAAA,CAAA,GAAA,GAAA,IAAA,CAAA,QAAA;AAvBA;;AAAA;AA0BA,gBAAA,GAAA,CAAA,MAAA,GAAA,GAAA;AA1BA;;AAAA;AAAA;AAAA,uBA8BA,yBAAA,GAAA,CA9BA;;AAAA;AAAA;AA8BA,gBAAA,IA9BA,sBA8BA,IA9BA;;AAAA,sBA+BA,IAAA,KAAA,MA/BA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAgCA,MAAA,CAAA,OAAA,EAhCA;;AAAA;AAAA;AAAA;;AAAA;AAkCA,gBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AAlCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoCA,KAhKA;AAiKA;AACA,IAAA,SAlKA,qBAkKA,IAlKA,EAkKA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,GADA,mCACA,MAAA,CAAA,IADA;;AAAA,sBAEA,IAAA,KAAA,UAFA;AAAA;AAAA;AAAA;;AAAA,+BAGA,GAAA,CAAA,MAHA;AAAA,kDAIA,GAJA,wBAgBA,GAhBA,yBA4BA,GA5BA;AAAA;;AAAA;AAKA,gBAAA,MAAA,CAAA,aAAA,GAAA,KAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,EAAA,GAAA,GAAA,CAAA,GAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,IAAA,GAAA,QAAA;AACA,gBAAA,MAAA,CAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,aAAA,GAAA,EAAA;AACA,gBAAA,MAAA,CAAA,MAAA,GAAA,IAAA;AAdA;;AAAA;AAiBA,gBAAA,MAAA,CAAA,aAAA,GAAA,KAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,aAAA,GAAA,EAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,EAAA,GAAA,GAAA,CAAA,GAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,IAAA,GAAA,KAAA;AACA,gBAAA,MAAA,CAAA,MAAA,GAAA,IAAA;AA1BA;;AAAA;AA6BA,gBAAA,MAAA,CAAA,IAAA,CAAA,QAAA,GAAA,MAAA,CAAA,aAAA,CAAA,QAAA;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,SAAA,GAAA,MAAA,CAAA,GAAA,CA9BA,CA+BA;;AACA,gBAAA,UAhCA,GAgCA,MAAA,CAAA,aAAA,CAAA,QAAA,CAAA,IAAA,CACA,UAAA,IAAA;AAAA,yBAAA,IAAA,CAAA,IAAA,IAAA,EAAA,IAAA,CAAA,GAAA,IAAA,IAAA,CAAA,IAAA,IAAA,IAAA,CAAA,IAAA,CAAA;AAAA,iBADA,CAhCA;AAAA;AAAA,uBAmCA,MAAA,CAAA,KAAA,CAAA,MAAA,EAAA,QAAA;AAAA,oGAAA,kBAAA,KAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kCACA,KAAA,IAAA,CAAA,UADA;AAAA;AAAA;AAAA;;AAEA,4BAAA,MAAA,CAAA,aAAA,GAAA,KAAA;AACA,4BAAA,MAAA,CAAA,aAAA,CAAA,UAAA,GAAA,MAAA,CAAA,IAAA,CAAA,KAAA;;AACA,4BAAA,MAAA,CAAA,UAAA;;AAJA;AAAA,mCAKA,2BAAA,MAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kCAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,gCAAA,MAAA,CAAA,OAAA;;AACA,gCAAA,MAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,gCAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AACA,gCAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gCAAA,MAAA,CAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,gCAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,KAAA;AACA,gCAAA,MAAA,CAAA,MAAA,GAAA,IAAA;AACA,+BARA,MAQA;AACA,gCAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA;AACA;AACA,6BAZA,CALA;;AAAA;AAAA;AAAA;;AAAA;AAmBA,4BAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,eAAA;;AAnBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAA;;AAAA;AAAA;AAAA;AAAA,oBAnCA;;AAAA;AAAA;AAAA;;AAAA;AA2DA,gBAAA,MAAA,CAAA,aAAA,GAAA,KAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,cAAA,CAAA,KAAA,GAAA,QAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,KAAA;AACA,gBAAA,MAAA,CAAA,MAAA,GAAA,IAAA;;AAjEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmEA,KArOA;AAsOA;AACA,IAAA,cAvOA,4BAuOA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,WAAA,EAAA,GAAA,IAAA;AACA,KA1OA;AA2OA;AACA,IAAA,kBA5OA,8BA4OA,MA5OA,EA4OA;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,MAAA,CAAA,KAAA;AACA,KA9OA;AA+OA;AACA,IAAA,oBAhPA,gCAgPA,GAhPA,EAgPA;AACA,UAAA,GAAA,EAAA;AACA,aAAA,IAAA,CAAA,OAAA,GAAA,KAAA,IAAA,CAAA,IAAA;AACA,aAAA,IAAA,CAAA,OAAA,GAAA,CAAA;AACA,OAHA,MAGA;AACA,aAAA,IAAA,CAAA,OAAA,GAAA,KAAA,IAAA,CAAA,IAAA;AACA,aAAA,IAAA,CAAA,OAAA,GAAA,CAAA;AACA;;AACA,WAAA,aAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,IAAA,GAAA,GAAA;AACA,OAFA;AAGA,KA3PA;AA4PA,IAAA,iBA5PA,6BA4PA,GA5PA,EA4PA;AACA,UAAA,GAAA,EAAA;AACA,UAAA,KAAA,IAAA,CAAA,OAAA;AACA,OAFA,MAEA;AACA,UAAA,KAAA,IAAA,CAAA,OAAA;AACA;;AACA,WAAA,IAAA,CAAA,OAAA,GAAA,KAAA,IAAA,CAAA,IAAA,GAAA,KAAA,IAAA,CAAA,OAAA;AACA,KAnQA;AAoQA,IAAA,WApQA,yBAoQA;AACA,WAAA,UAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,KAAA,UAAA,EAAA;AACA,UAAA,IAAA,CAAA,aAAA,GAAA,EAAA;AACA;AACA,OAJA;AAKA,WAAA,MAAA,GAAA;AACA;AACA,QAAA,EAAA,EAAA,CAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAAA;AAKA,KA/QA;AAgRA;AACA,IAAA,OAjRA,mBAiRA,MAjRA,EAiRA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,MAAA,CAAA,MAAA,+DAAA,MAAA,CAAA,MAAA,GAAA,MAAA;AACA,gBAAA,KAHA,GAGA,MAAA,CAAA,MAHA;AAIA,gBAAA,MAAA,CAAA,OAAA,GAAA,IAAA;AACA,gBAAA,KAAA,CAAA,OAAA,GAAA,CAAA;AAAA,kBAAA,IAAA,EAAA,YAAA;AAAA,kBAAA,GAAA,EAAA;AAAA,iBAAA,CAAA;;AACA,oBAAA,CAAA,KAAA,CAAA,MAAA,EAAA;AACA,kBAAA,KAAA,CAAA,MAAA,GAAA,WAAA;AACA;;AARA;AAAA,uBASA,sBAAA,KAAA,CATA;;AAAA;AAAA;AASA,gBAAA,IATA,kBASA,IATA;AASA,gBAAA,IATA,kBASA,IATA;;AAUA,oBAAA,IAAA,KAAA,MAAA,EAAA;AAAA,uEACA,IAAA,CAAA,OADA;;AAAA;AAAA;AAAA,0BACA,CADA;;AAEA,sBAAA,MAAA,CAAA,aAAA,CAAA,OAAA,CAAA,UAAA,OAAA,EAAA;AACA,4BAAA,CAAA,CAAA,MAAA,KAAA,OAAA,CAAA,KAAA,EAAA;AACA,0BAAA,CAAA,CAAA,QAAA,GAAA,OAAA,CAAA,KAAA;AACA;AACA,uBAJA;AAFA;;AACA,wEAAA;AAAA;AAMA;AAPA;AAAA;AAAA;AAAA;AAAA;;AAQA,kBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA,kBAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA;;AArBA;AAAA;;AAAA;AAAA;AAAA;AAuBA,gBAAA,OAAA,CAAA,GAAA;;AAvBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyBA,KA1SA;AA4SA;AACA,IAAA,SA7SA,qBA6SA,GA7SA,EA6SA;AACA,WAAA,QAAA,CAAA,GAAA;AACA,WAAA,KAAA,GAAA,SAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,OAAA,GAAA,KAAA,IAAA,CAAA,OAAA;AACA,WAAA,YAAA,GAAA,KAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,OAAA;AACA,KAtTA;AAuTA;AACA,IAAA,UAxTA,sBAwTA,GAxTA,EAwTA;AACA,WAAA,QAAA,CAAA,GAAA;AACA,WAAA,KAAA,GAAA,WAAA;AACA,WAAA,IAAA,mCAAA,GAAA,EAHA,CAIA;;AACA,WAAA,OAAA,GAAA,KAAA,IAAA,CAAA,OAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,YAAA,GAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,OAAA;AACA,KAlUA;AAoUA;AACA,IAAA,OArUA,qBAqUA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,MAAA,CAAA,IAAA,CAAA,QAAA,GAAA,MAAA,CAAA,aAAA,CAAA,QAAA;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,SAAA,GAAA,MAAA,CAAA,GAAA;AAHA;AAAA,uBAIA,4BAAA,MAAA,CAAA,IAAA,CAJA;;AAAA;AAAA;AAIA,gBAAA,IAJA,wBAIA,IAJA;;AAKA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA;;AAPA;AAAA;;AAAA;AAAA;AAAA;AASA,gBAAA,OAAA,CAAA,GAAA;;AATA;AAWA;AACA,gBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,gBAAA,MAAA,CAAA,OAAA;;AACA,gBAAA,MAAA,CAAA,aAAA,GAAA,KAAA;;AAdA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA,KApVA;AAsVA;AACA,IAAA,SAvVA,qBAuVA,GAvVA,EAuVA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,KADA,GACA,EADA;;AAEA,oBAAA,GAAA,CAAA,KAAA,EAAA;AACA,kBAAA,KAAA,GAAA,GAAA,CAAA,KAAA;AACA,iBAFA,MAEA;AACA,kBAAA,KAAA,GAAA,MAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACA;;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAKA,IALA,CAKA,YAAA;AACA,uCAAA,KAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,wBAAA,IAAA,SAAA,IAAA;;AACA,wBAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,SADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA,EADA,CAKA;AACA;;;AACA,sBAAA,MAAA,CAAA,OAAA;AACA,qBARA,MAQA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,OADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;AAIA;AACA,mBAfA;AAgBA,iBAtBA,EAuBA,KAvBA,CAuBA,YAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA;AACA,oBAAA,IAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;AAIA,iBA5BA;;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoCA,KA3XA;AA4XA;AACA,IAAA,YA7XA,0BA6XA;AACA,UAAA,GAAA,GAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,GAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA,EAJA;AAKA,QAAA,IAAA,EAAA;AALA,OAAA;AAOA,WAAA,aAAA,CAAA,QAAA,CAAA,IAAA,CAAA,GAAA;AACA,WAAA,aAAA,CAAA,GAAA,GAAA,GAAA;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,WAAA,IAAA,CAAA,OAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,KAzYA;AA0YA;AACA,IAAA,YA3YA,wBA2YA,KA3YA,EA2YA,GA3YA,EA2YA;AACA,WAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA,KAAA;AACA,WAAA,aAAA,CAAA,QAAA,CAAA,MAAA,CAAA,KAAA,EAAA,CAAA;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,WAAA,IAAA,CAAA,OAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,KAhZA;AAiZA;AACA,IAAA,kBAlZA,8BAkZA,GAlZA,EAkZA;AACA,UAAA,GAAA,KAAA,KAAA,EAAA;AACA,aAAA,IAAA,CAAA,OAAA,GAAA,KAAA,IAAA,CAAA,IAAA;AACA,aAAA,IAAA,CAAA,OAAA,GAAA,CAAA;AACA,aAAA,aAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,IAAA,GAAA,IAAA;AACA,SAFA;AAGA,OANA,MAMA;AACA,aAAA,IAAA,CAAA,OAAA,GAAA,KAAA,IAAA,CAAA,IAAA;AACA,aAAA,IAAA,CAAA,OAAA,GAAA,CAAA;AACA,aAAA,aAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,IAAA,GAAA,KAAA;AACA,SAFA;AAGA;AACA,KAhaA;AAiaA;AACA,IAAA,KAlaA,mBAkaA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,KApaA;AAqaA;AACA,IAAA,YAtaA,wBAsaA,SAtaA,EAsaA;AACA,WAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,KAAA;AAAA,OAAA,CAAA;AACA,WAAA,MAAA,GAAA,SAAA,CAAA,MAAA,KAAA,CAAA;AACA,WAAA,QAAA,GAAA,CAAA,SAAA,CAAA,MAAA;AACA,WAAA,UAAA,GAAA,SAAA;AACA,KA3aA;;AA4aA;;;AAGA,IAAA,QA/aA,oBA+aA,GA/aA,EA+aA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAEA,2BAAA;AAAA,kBAAA,KAAA,EAAA,GAAA,CAAA;AAAA,iBAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,uBAEA,IAFA;AAEA,gBAAA,IAFA,uBAEA,IAFA;;AAGA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,aAAA,CAAA,QAAA,GAAA,IAAA;AACA;;AALA;AAAA;;AAAA;AAAA;AAAA;AAOA,gBAAA,OAAA,CAAA,GAAA;;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,KAxbA;AAybA;AACA,IAAA,6BA1bA,yCA0bA,MA1bA,EA0bA;AACA,WAAA,cAAA,GAAA,MAAA;AACA,KA5bA;AA6bA;AACA,IAAA,iBA9bA,+BA8bA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,KAhcA;AAicA;AACA,IAAA,UAlcA,sBAkcA,GAlcA,EAkcA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,UAFA,mCAEA,GAFA;AAAA;AAAA,uBAGA,yBAAA,UAAA,EAAA,SAAA,EAAA,kBAAA,CAHA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAKA,gBAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,OAAA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,KAzcA;AA0cA;AACA,IAAA,SA3cA,qBA2cA,GA3cA,EA2cA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,UAFA,mCAEA,GAFA;AAAA;AAAA,uBAGA,wBAAA,UAAA,EAAA,SAAA,EAAA,iBAAA,CAHA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAKA,gBAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,OAAA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,KAldA;AAmdA;AACA,IAAA,WApdA,uBAodA,GApdA,EAodA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,UAFA,mCAEA,GAFA;AAAA;AAAA,uBAGA,0BAAA,UAAA,EAAA,SAAA,CAHA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAKA,gBAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,OAAA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA;AA3dA;AApOA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n      @handleReset=\"filterReset\"\n    />\n    <div>\n      <el-white class=\"button-group\">\n        <div class=\"button_btn\">\n          <el-button\n            type=\"danger\"\n            icon=\"el-icon-delete\"\n            v-if=\"hasSuperRole\"\n            @click=\"deleteRow\"\n            :disabled=\"single\"\n            >删除</el-button\n          >\n        </div>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"selectChange\"\n          height=\"69vh\"\n          v-loading=\"loading\"\n        >\n          <!-- <el-table-column\n            prop=\"statusCn\"\n            slot=\"table_six\"\n            align=\"center\"\n            style=\"display: block\"\n            label=\"流程状态\"\n            min-width=\"120\"\n            :resizable=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-badge\n                v-if=\"scope.row.isBack === 1\"\n                value=\"退回\"\n                class=\"item\"\n                type=\"danger\"\n              >\n              </el-badge>\n              <span>{{ scope.row.statusCn }}</span>\n            </template>\n          </el-table-column> -->\n          <el-table-column\n            slot=\"table_eight\"\n            align=\"center\"\n            fixed=\"right\"\n            style=\"display: block\"\n            label=\"操作\"\n            width=\"160\"\n            :resizable=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                @click=\"getDetails(scope.row)\"\n                type=\"text\"\n                size=\"small\"\n                title=\"详情\"\n                class=\"el-icon-view\"\n              />\n              <el-button\n                @click=\"getUpdate(scope.row)\"\n                class=\"el-icon-edit\"\n                title=\"编辑\"\n                v-if=\"\n                  (scope.row.status === '0' && scope.row.createBy === currentUser) || hasSuperRole\n                \"\n                type=\"text\"\n                size=\"small\"\n              />\n              <el-button\n                @click=\"deleteRow(scope.row)\"\n                class=\"el-icon-delete\"\n                title=\"删除\"\n                v-if=\"\n                  scope.row.status === '0' && scope.row.createBy === currentUser\n                \"\n                type=\"text\"\n                size=\"small\"\n              />\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                @click=\"nullifyGzp(scope.row.objId)\"\n                v-if=\"scope.row.status < 4 && scope.row.status > 0 &&\n                ( scope.row.createBy === currentUser || (scope.row.bzspr && scope.row.bzspr === currentUser) \n                || (scope.row.fgsspr && scope.row.fgsspr === currentUser) \n                || (scope.row.bjr && scope.row.bjr === currentUser)\n                || hasSuperRole )\n                \"\n                title=\"作废\"\n                class=\"el-icon-close\"\n              >\n              </el-button>\n              <el-button\n                @click=\"showTimeLine(scope.row)\"\n                v-if=\"scope.row.isStart === 1\"\n                type=\"text\"\n                size=\"small\"\n                class=\"el-icon-lcck commonIcon\"\n                title=\"流程查看\"\n              />\n              <el-button\n                @click=\"showProcessImg(scope.row)\"\n                v-if=\"scope.row.isStart === 1\"\n                type=\"text\"\n                size=\"small\"\n                class=\"el-icon-lct commonIcon\"\n                title=\"流程图\"\n              />\n              <!--              <el-button @click=\"exportWord(scope.row)\" v-if=\"scope.row.status == '待办结'\" type=\"text\" size=\"small\">\n                              导出Word\n                            </el-button>-->\n              <el-button\n                @click=\"previewFile(scope.row)\"\n                v-if=\"scope.row.status > 2\"\n                type=\"text\"\n                size=\"small\"\n                class=\"el-icon-zoom-in\"\n                title=\"预览\"\n              />\n              <el-button\n                @click=\"exportPdf(scope.row)\"\n                v-if=\"scope.row.status > 2\"\n                type=\"text\"\n                size=\"small\"\n                class=\"el-icon-pdf-export commonIcon\"\n                title=\"导出pdf\"\n              />\n            </template>\n          </el-table-column>\n        </comp-table>\n      </el-white>\n    </div>\n\n    <!-- 详情/新增/修改 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      v-if=\"isShowDetails\"\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <div>\n          <el-row>\n            <el-col :span=\"24\">\n              <el-form-item label=\"工作名称\" prop=\"gzmc\">\n                <el-input\n                  type=\"textarea\"\n                  :rows=\"2\"\n                  v-model=\"form.gzmc\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请输入工作名称\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <el-col :span=\"8\">\n              <el-form-item label=\"编号\" prop=\"bm\">\n                <el-input v-model=\"form.bm\" disabled placeholder=\"请输入\" />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"线路名称\" prop=\"xlmc\">\n                <el-input\n                  v-model=\"form.xlmc\"\n                  disabled\n                  placeholder=\"请输入线路名称\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"操作人\"\n                prop=\"czr\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-input\n                  v-model=\"form.czr\"\n                  :disabled=\"isDisabledBj\"\n                  placeholder=\"请输入操作人\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"监护人\"\n                prop=\"jhr\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-input\n                  v-model=\"form.jhr\"\n                  :disabled=\"isDisabledBj\"\n                  placeholder=\"请输入监护人\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"日期\" prop=\"rq\">\n                <el-date-picker\n                  v-model=\"form.rq\"\n                  :disabled=\"isDisabledBj\"\n                  type=\"date\"\n                  placeholder=\"选择日期\"\n                  format=\"yyyy-MM-dd\"\n                  value-format=\"yyyy-MM-dd\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作项数：\" prop=\"czxs\">\n                <el-input-number\n                  v-model=\"form.czxs\"\n                  disabled\n                  placeholder=\"请输入操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"已执行项数：\" prop=\"yzxczxs\">\n                <el-input-number\n                  v-model=\"form.yzxczxs\"\n                  disabled\n                  placeholder=\"请输入已执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"未执行项数：\" prop=\"wzxczxs\">\n                <el-input-number\n                  v-model=\"form.wzxczxs\"\n                  disabled\n                  placeholder=\"请输入未执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <el-col :span=\"8\">\n              <el-form-item label=\"是否已执行：\" prop=\"sfyzx\">\n                <el-select\n                  v-model=\"form.sfyzx\"\n                  placeholder=\"请选择\"\n                  :disabled=\"isDisabledBj\"\n                  @change=\"handleChangeOfSfzx\"\n                >\n                  <el-option\n                    v-for=\"item in sfyzxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item\n                label=\"未执行原因：\"\n                prop=\"wzxyy\"\n                v-if=\"form.sfyzx === '未执行'\"\n              >\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"form.wzxyy\"\n                  :disabled=\"isDisabledBj\"\n                  placeholder=\"请输入内容\"\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n\n          <el-row>\n            <el-col :span=\"24\">\n              <el-form-item label=\"上传图片：\">\n                <el-upload\n                  :disabled=\"isDisabledBj\"\n                  action=\"\"\n                  ref=\"uploadImg\"\n                  accept=\"image/jpeg,image/jpg,image/png\"\n                  :headers=\"header\"\n                  :multiple=\"true\"\n                  :on-change=\"handleChange\"\n                  :data=\"uploadImgData\"\n                  :file-list=\"imgList\"\n                  :auto-upload=\"false\"\n                  list-type=\"picture-card\"\n                  :on-preview=\"handlePictureCardPreview\"\n                  :on-progress=\"handleProgress\"\n                  :on-remove=\"handleRemove\"\n                >\n                  <i class=\"el-icon-plus\"></i>\n                </el-upload>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n        <!--预览信息-->\n        <div>\n          <el-dialog\n            :title=\"titleyl\"\n            :visible.sync=\"yl\"\n            append-to-body\n            width=\"60%\"\n          >\n            <el-table\n              :data=\"propTableData.colFirst\"\n              border\n              stripe\n              style=\"width: 100%\"\n              :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n            >\n              <el-table-column\n                align=\"center\"\n                prop=\"xh\"\n                width=\"100px\"\n                label=\"序号\"\n                sortable\n              >\n                <template slot-scope=\"scope\">\n                  <el-input\n                    placeholder=\"请输入序号\"\n                    v-model=\"scope.row.xh\"\n                    disabled\n                  ></el-input>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n                <template slot-scope=\"scope\">\n                  <el-input\n                    type=\"textarea\"\n                    :autosize=\"{ minRows: 1, maxRows: 4 }\"\n                    placeholder=\"请输入操作项目\"\n                    v-model=\"scope.row.czrw\"\n                    disabled\n                  ></el-input>\n                </template>\n              </el-table-column>\n\n              <el-table-column\n                align=\"center\"\n                prop=\"xlr\"\n                label=\"下令人\"\n                width=\"100\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input\n                    placeholder=\"请输入内容\"\n                    v-model=\"scope.row.xlr\"\n                    disabled\n                  ></el-input>\n                </template>\n              </el-table-column>\n\n              <el-table-column\n                align=\"center\"\n                prop=\"xlsj\"\n                label=\"下令时间\"\n                width=\"204\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-date-picker\n                    v-model=\"scope.row.xlsj\"\n                    disabled\n                    type=\"datetime\"\n                    placeholder=\"选择下令时间\"\n                    format=\"HH:mm\"\n                    value-format=\"yyyy-MM-dd HH:mm\"\n                    style=\"width: 100%\"\n                  >\n                  </el-date-picker>\n                </template>\n              </el-table-column>\n\n              <el-table-column\n                align=\"center\"\n                prop=\"hlsj\"\n                label=\"回令时间\"\n                width=\"204\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-date-picker\n                    v-model=\"scope.row.hlsj\"\n                    disabled\n                    type=\"datetime\"\n                    placeholder=\"选择回令时间\"\n                    format=\"HH:mm\"\n                    value-format=\"yyyy-MM-dd HH:mm\"\n                    style=\"width: 100%\"\n                  >\n                  </el-date-picker>\n                </template>\n              </el-table-column>\n              <el-table-column\n                type=\"sfwc\"\n                width=\"50\"\n                label=\"是否完成\"\n                align=\"center\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-checkbox v-model=\"scope.row.sfwc\" disabled></el-checkbox>\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-dialog>\n        </div>\n        <!--列表-->\n        <div>\n          <div align=\"left\">\n            <el-button type=\"info\" @click=\"handleYlChange\">预览</el-button>\n          </div>\n          <div align=\"right\">\n            <el-checkbox @change=\"handleCheckAllChange\" :disabled=\"isDisabledBj\"\n              >全选</el-checkbox\n            >\n          </div>\n          <!--第一个表格-->\n          <el-table\n            :data=\"propTableData.colFirst\"\n            height=\"200\"\n            border\n            stripe\n            style=\"width: 100%\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <el-table-column\n              align=\"center\"\n              prop=\"xh\"\n              width=\"100px\"\n              label=\"序号\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <el-input-number\n                  size=\"small\"\n                  v-model=\"scope.row.xh\"\n                  :min=\"1\"\n                  :precision=\"0\"\n                  controls-position=\"right\"\n                  :disabled=\"isDisabled\"\n                ></el-input-number>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n              <template slot-scope=\"scope\">\n                <el-input\n                  placeholder=\"请输入操作项目\"\n                  v-model=\"scope.row.czrw\"\n                  :disabled=\"isDisabled\"\n                ></el-input>\n              </template>\n            </el-table-column>\n\n            <el-table-column\n              align=\"center\"\n              prop=\"xlr\"\n              label=\"下令人\"\n              width=\"100\"\n            >\n              <template slot-scope=\"scope\">\n                <el-input\n                  placeholder=\"请输入内容\"\n                  v-model=\"scope.row.xlr\"\n                  :disabled=\"isDisabledBj\"\n                ></el-input>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"xlsj\"\n              label=\"下令时间\"\n              width=\"204\"\n            >\n              <template slot-scope=\"scope\">\n                <el-date-picker\n                  v-model=\"scope.row.xlsj\"\n                  :disabled=\"isDisabledBj\"\n                  type=\"datetime\"\n                  placeholder=\"选择下令时间\"\n                  format=\"HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"hlsj\"\n              label=\"回令时间\"\n              width=\"204\"\n            >\n              <template slot-scope=\"scope\">\n                <el-date-picker\n                  v-model=\"scope.row.hlsj\"\n                  :disabled=\"isDisabledBj\"\n                  type=\"datetime\"\n                  placeholder=\"选择回令时间\"\n                  format=\"HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </template>\n            </el-table-column>\n            <el-table-column\n              type=\"sfwc\"\n              width=\"50\"\n              label=\"是否完成\"\n              align=\"center\"\n            >\n              <template slot-scope=\"scope\">\n                <el-checkbox\n                  v-model=\"scope.row.sfwc\"\n                  :disabled=\"isDisabledBj\"\n                  @change=\"handleCheckChange\"\n                ></el-checkbox>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" width=\"80\">\n              <template slot=\"header\" slot-scope=\"scope\">\n                <el-button\n                  size=\"small\"\n                  type=\"primary\"\n                  icon=\"el-icon-plus\"\n                  :disabled=\"isDisabled\"\n                  @click=\"listFirstAdd(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n              <template slot-scope=\"scope\">\n                <el-button\n                  type=\"text\"\n                  icon=\"el-icon-delete\"\n                  :disabled=\"isDisabled\"\n                  @click=\"listFirstDel(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button\n          v-if=\"isDisabled && buttonNameShow && form.status > 0\"\n          type=\"info\"\n          @click=\"getSbFsBj('rollback')\"\n          >退 回\n        </el-button>\n        <el-button\n          v-if=\"isDisabled && buttonNameShow\"\n          type=\"success\"\n          @click=\"getSbFsBj('complete')\"\n          >{{ buttonName }}\n        </el-button>\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"saveRow\"\n          >保 存</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <!--线路选择组件-->\n    <el-dialog\n      title=\"线路选择\"\n      :visible.sync=\"isShowXlDialog\"\n      width=\"20%\"\n      v-if=\"isShowXlDialog\"\n      v-dialogDrag\n    >\n      <sdxl-selected\n        @closeSysbSelectDialog=\"handleControlSysbSelectDialog\"\n        @handleAcceptSbData=\"handleAcceptSbData\"\n      ></sdxl-selected>\n    </el-dialog>\n\n    <!--  工作流  -->\n    <activiti\n      ref=\"activiti\"\n      :processData=\"processData\"\n      :isShow=\"isShow\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n    ></activiti>\n    <time-line\n      :value=\"timeLineShow\"\n      :timeData=\"timeData\"\n      @closeTimeLine=\"colseTimeLine\"\n    />\n    <!--流程图查看-->\n    <el-dialog\n      title=\"审批流程图\"\n      :visible.sync=\"openLoadingImg\"\n      width=\"56%\"\n      append-to-body\n      :close-on-click-modal=\"false\"\n    >\n      <div>\n        <img :src=\"imgSrc\" style=\"width: 100%\" />\n      </div>\n    </el-dialog>\n\n    <!--图片放大展示弹出框-->\n    <el-image-viewer\n      v-if=\"imgDialogVisible\"\n      :on-close=\"onClose\"\n      :url-list=\"dialogImageUrl\"\n      zIndex=\"3000\"\n    />\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport {\n  getCzpmxList,\n  getList,\n  remove,\n  saveOrUpdates\n} from \"@/api/yxgl/sdyxgl/sddzczp\";\nimport sdxlSelected from \"@/views/dagangOilfield/xjgl/sdxj/sdxlSelected\";\nimport {\n  exportPdf,\n  exportWord,\n  previewFile,\n  saveOrUpdate,\n  updateById\n} from \"@/api/yxgl/bdyxgl/bddzczp\";\nimport api from \"@/utils/request\";\n//流程\nimport activiti from \"com/activiti_czp\";\nimport timeLine from \"com/timeLine\";\nimport { HistoryList } from \"@/api/activiti/processTask\";\nimport ElImageViewer from \"element-ui/packages/image/src/image-viewer\";\nexport default {\n  name: \"dzczp\",\n  components: { sdxlSelected, activiti, timeLine, ElImageViewer },\n  data() {\n    return {\n      hasSuperRole: this.$store.getters.hasSuperRole,\n      loading: false,\n      isDisabledBj: true,\n      buttonNameShow: false,\n      buttonName: \"\",\n      rules: {\n        kssj: [\n          { required: true, message: \"操作开始时间不能为空\", trigger: \"blur\" }\n        ],\n        jssj: [\n          { required: true, message: \"操作结束时间不能为空\", trigger: \"change\" }\n        ],\n        fgs: [{ required: true, message: \"分公司不能为空\", trigger: \"select\" }],\n        bdzmc: [\n          { required: true, message: \"变电站不能为空\", trigger: \"select\" }\n        ],\n        czrw: [\n          { required: true, message: \"操作任务不能为空\", trigger: \"blur\" }\n        ],\n        // czr: [{ required: true, message: \"操作人不能为空\", trigger: \"blur\" }],\n        // jhr: [{ required: true, message: \"监护人不能为空\", trigger: \"blur\" }],\n        xlr: [{ required: true, message: \"下令人不能为空\", trigger: \"blur\" }],\n        // spr: [\n        //   {required: true, message: '审票人不能为空', trigger: 'blur'}\n        // ],\n        czxs: [\n          { required: true, message: \"操作项数不能为空\", trigger: \"blur\" }\n        ],\n        yzxczxs: [\n          { required: true, message: \"已执行项数不能为空\", trigger: \"blur\" }\n        ],\n        wzxczxs: [\n          { required: true, message: \"未执行项数不能为空\", trigger: \"blur\" }\n        ],\n        sfyzx: [\n          { required: true, message: \"是否已执行不能为空\", trigger: \"select\" }\n        ]\n      },\n      //状态下拉框数据\n      statusOptions: [\n        {\n          value: \"0\",\n          label: \"操作票填报\"\n        },\n        // {\n        //   value: \"1\",\n        //   label: \"班组审核\"\n        // },\n        {\n          value: \"2\",\n          label: \"分公司审核\"\n        },\n        {\n          value: \"3\",\n          label: \"操作票办结\"\n        },\n        {\n          value: \"4\",\n          label: \"结束\"\n        },{ label: \"作废\", value: \"7\" }\n      ],\n      bjr: \"\",\n      // 是否已执行下拉框\n      sfyzxList: [\n        { label: \"已执行\", value: \"已执行\" },\n        { label: \"未执行\", value: \"未执行\" }\n      ],\n      // 获取当前登录人账号\n      currentUser: this.$store.getters.name,\n      //上传图片时的携带的其他参数\n      uploadImgData: {\n        businessId: \"\" //携带的表单主键id\n      },\n      //上传图片时的请求头\n      header: {},\n      //图片list\n      imgList: [],\n      //图片地址url\n      dialogImageUrl: \"\",\n      //展示图片dialog控制\n      imgDialogVisible: false,\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"czplc\",\n        businessKey: \"\",\n        businessType: \"倒闸操作票\",\n        variables: {},\n        defaultFrom: true,\n        nextUser: \"\",\n        processType: \"complete\"\n      },\n      //工作流弹窗\n      isShow: false,\n      //流程图查看\n      openLoadingImg: false,\n      imgSrc: \"\", //流程图查看地址\n      timeData: [],\n      timeLineShow: false,\n      //弹出框标题\n      activitiOption: { title: \"上报\" },\n      titleyl: \"\",\n      yl: false,\n      isSaveRow: false,\n      //线路选择弹出\n      isShowXlDialog: false,\n      // 多选框选中的id\n      ids: [],\n      single: true,\n      multiple: true,\n      selectData: [],\n      //弹出框中表格数据\n      propTableData: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //form表单\n      form: {\n        bh: \"\",\n        status: \"\",\n        czr: \"\",\n        jhr: \"\",\n        sdshr: \"\",\n        lx: 1, //输电\n        colFirst: [],\n        bz: \"\"\n      },\n      formCzp: {\n        sdshr: \"\"\n      },\n      //详情弹框是否显示\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          bm: \"\",\n          status: \"\",\n          czrw: \"\",\n          xlr: \"\",\n          xlsj: \"\",\n          hlsj: \"\",\n          czr: \"\",\n          jhr: \"\",\n          sdshr: \"\",\n          rqArr: []\n        }, //查询条件\n        fieldList: [\n          { label: \"编号\", value: \"bm\", type: \"input\", clearable: true },\n\n          { label: \"线路名称\", value: \"xlmc\", type: \"input\", clearable: true },\n          { label: \"操作人\", value: \"czr\", type: \"input\", clearable: true },\n          { label: \"监护人\", value: \"jhr\", type: \"input\", clearable: true },\n          //{ label: '审核人', value: 'sdshr', type: 'input', clearable: true },\n          {\n            label: \"日期\",\n            value: \"rqArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          {\n            label: \"状态\",\n            value: \"status\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            clearable: true,\n            options: [\n              {\n                value: \"0\",\n                label: \"操作票填报\"\n              },\n              /* {\n                value: '1',\n                label: '班组审核'\n              },*/\n              {\n                value: \"2\",\n                label: \"分公司审核\"\n              },\n              {\n                value: \"3\",\n                label: \"操作票办结\"\n              },{ label: \"作废\", value: \"7\" }\n            ]\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 1,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"编号\", prop: \"bm\", minWidth: \"80\" },\n          { label: \"状态\", prop: \"statusCn\", minWidth: \"80\" },\n          { label: \"线路名称\", prop: \"xlmc\", minWidth: \"120\" },\n          { label: \"工作名称\", prop: \"gzmc\", minWidth: \"120\" },\n          { label: \"开票时间\", prop: \"createTime\", minWidth: \"80\" },\n          { label: \"操作人\", prop: \"czr\", minWidth: \"60\" },\n          { label: \"监护人\", prop: \"jhr\", minWidth: \"60\" },\n          { label: \"日期\", prop: \"rq\", minWidth: \"80\" }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        //输电\n        lx: 1,\n        status: \"\"\n      },\n      selectRows: []\n    };\n  },\n  mounted() {\n    //获取token\n    this.header.token = getToken();\n    //列表查询\n    this.getData(this.$route.query);\n  },\n  methods: {\n    //作废票\n    nullifyGzp(objId) {\n      this.$confirm(\"票作废后只能查看，不能进行任何操作，确认作废吗?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(async () => {\n          let { code } = await updateById({ status: 7, objId: objId });\n          if (code === \"0000\") {\n            this.$message.success(\"操作成功!!\");\n            this.getData();\n          }\n        })\n        .catch(() => {});\n    },\n    getShow() {\n      this.buttonNameShow = false;\n      switch (this.form.status) {\n        case \"0\":\n          this.buttonName = \"上 报\";\n          if (this.currentUser === this.form.createBy) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case \"1\":\n          this.buttonName = \"提 交\";\n          if (this.form.bzspr === this.currentUser) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case \"2\":\n          this.buttonName = \"提 交\";\n          if (this.form.fgsspr === this.currentUser) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case \"3\":\n          this.buttonName = \"办 结\";\n          if (this.form.bjr === this.currentUser) {\n            this.buttonNameShow = true;\n            this.isDisabledBj = false;\n          }\n          break;\n      }\n    },\n    //改造后的上传多个图片文件\n    uploadForm() {\n      var newUrl = []; //用来存放当前未改动过的图片url,此图片不进行删除处理，其余当前业务id下面的图片数据将进行删除\n      // console.log(this.$refs.uploadImg.fileList)\n      var imageType = [\"png\", \"jpg\"];\n\n      const formData = new FormData();\n      // 因为要传一个文件数组过去，所以要循环append\n      this.imgList.forEach(file => {\n        if (file.raw == undefined) {\n          newUrl.push(file.url);\n        }\n        formData.append(\"files\", file.raw);\n      });\n      formData.append(\"businessId\", this.uploadImgData.businessId); // 自定义参数\n      formData.append(\"newUrl\", newUrl); // 未改动过的图片url\n      formData.append(\"type\", imageType); // 未改动过的图片url\n      api\n        .requestPost(\"/isc-api/file/uploadFiles\", formData, 1)\n        .then(res => {\n          // 清空图片列表（一定要清空，否则上传成功后还是会调用handleChange（）函数，上传成功后列表中还存在图片）\n          this.imgList = [];\n          //查询接口数据\n          this.getData();\n        })\n        .catch(res => {\n          this.$message.success(\"图片上传失败！\");\n        });\n    },\n    // 选择文件时，往fileList里添加\n    handleChange(file, fileList) {\n      this.imgList = fileList;\n    },\n    handleProgress(event, file, fileList) {},\n    //图片移除\n    handleRemove(file, fileList) {\n      this.imgList = fileList;\n    },\n    //图片放大\n    handlePictureCardPreview(file) {\n      this.dialogImageUrl = [file.url];\n      this.imgDialogVisible = true;\n    },\n    onClose() {\n      this.imgDialogVisible = false;\n    },\n    //清空\n    change(e) {\n      this.$forceUpdate();\n    },\n    //关闭弹窗\n    closeActiviti() {\n      if (this.$refs.activiti.$refs.form) {\n        this.$refs.activiti.$refs.form.resetFields();\n      }\n      this.isShow = false;\n    },\n    //关闭流程查看页面\n    colseTimeLine() {\n      this.timeLineShow = false;\n    },\n    async showTimeLine(row) {\n      this.processData.businessKey = row.objId;\n      let { code, data } = await HistoryList(this.processData);\n      this.timeData = data;\n      this.timeLineShow = true;\n    },\n    //流程图片查看\n    showProcessImg(row) {\n      this.openLoadingImg = true;\n      this.imgSrc =\n        \"/activiti-api/process/read-resource?processDefinitionKey=czplc&businessKey=\" +\n        row.objId +\n        \"&t=\" +\n        new Date().getTime();\n    },\n    //工作流回传数据\n    async todoResult(data) {\n      let row = {\n        objId: data.businessKey,\n        isStart: 1,\n        isBack: data.processType === \"rollback\" ? 1 : 0\n      };\n      if (data.processType === \"rollback\") {\n        switch (data.activeTaskName) {\n          case \"操作票填报\":\n            row.status = \"0\";\n            break;\n          case \"分公司审核\":\n            row.status = \"2\";\n            break;\n        }\n      } else {\n        switch (data.activeTaskName) {\n          case \"分公司审核\":\n            row.status = \"2\";\n            row.fgsspr = data.nextUser;\n            break;\n          case \"操作票办结\":\n            row.status = \"3\";\n            row.bjr = data.nextUser;\n            break;\n          case \"结束\":\n            row.status = \"4\";\n            break;\n        }\n      }\n      let { code } = await updateById(row);\n      if (code === \"0000\") {\n        await this.getData();\n      } else {\n        this.$message.warning(\"操作失败\");\n      }\n    },\n    //上报发送办结\n    async getSbFsBj(type) {\n      let row = { ...this.form };\n      if (type === \"complete\") {\n        switch (row.status) {\n          case \"0\":\n            this.isShowDetails = false;\n            this.processData.variables.pass = true;\n            this.processData.businessKey = row.objId;\n            this.processData.processType = type;\n            this.processData.dw = row.fgs;\n            this.processData.rylx = \"分公司审核人\";\n            this.activitiOption.title = \"提交\";\n            this.processData.defaultFrom = true;\n            this.processData.personGroupId = 14;\n            this.isShow = true;\n            break;\n          case \"2\":\n            this.isShowDetails = false;\n            this.processData.variables.pass = true;\n            this.processData.businessKey = row.objId;\n            this.processData.processType = type;\n            this.activitiOption.title = \"提交\";\n            this.processData.defaultFrom = true;\n            this.processData.personGroupId = 15;\n            this.processData.dw = row.fgs;\n            this.processData.rylx = \"办结人\";\n            this.isShow = true;\n            break;\n          case \"3\":\n            this.form.colFirst = this.propTableData.colFirst;\n            this.form.objIdList = this.ids;\n            //this.propTableData.colFirst 验证已打钩的数据中不能有空内容\n            let tableValid = this.propTableData.colFirst.some(\n              item => item.sfwc && !(item.xlr && item.xlsj && item.hlsj)\n            );\n            await this.$refs[\"form\"].validate(async valid => {\n              if (valid && !tableValid) {\n                this.isShowDetails = false;\n                this.uploadImgData.businessId = this.form.objId;\n                this.uploadForm();\n                await saveOrUpdate(this.form).then(res => {\n                  if (res.code === \"0000\") {\n                    this.getData();\n                    this.processData.variables.pass = true;\n                    this.processData.businessKey = row.objId;\n                    this.processData.processType = type;\n                    this.activitiOption.title = \"办结\";\n                    this.processData.defaultFrom = false;\n                    this.isShow = true;\n                  } else {\n                    this.$message.error(\"失败\");\n                  }\n                });\n              } else {\n                this.$message.error(\"请将完成的操作项目填写完整\");\n              }\n            });\n        }\n      } else {\n        this.isShowDetails = false;\n        this.processData.businessKey = row.objId;\n        this.processData.processType = type;\n        this.activitiOption.title = \"回退原因填写\";\n        this.processData.defaultFrom = true;\n        this.processData.variables.pass = false;\n        this.isShow = true;\n      }\n    },\n    // 预览按钮\n    handleYlChange() {\n      this.titleyl = \"查看操作项目\";\n      this.yl = true;\n    },\n    //线路选择接收数据\n    handleAcceptSbData(sbData) {\n      this.form.xlmc = sbData.label;\n    },\n    // 全选按钮\n    handleCheckAllChange(val) {\n      if (val) {\n        this.form.yzxczxs = this.form.czxs;\n        this.form.wzxczxs = 0;\n      } else {\n        this.form.wzxczxs = this.form.czxs;\n        this.form.yzxczxs = 0;\n      }\n      this.propTableData.colFirst.forEach(item => {\n        item.sfwc = val;\n      });\n    },\n    handleCheckChange(val) {\n      if (val) {\n        ++this.form.yzxczxs;\n      } else {\n        --this.form.yzxczxs;\n      }\n      this.form.wzxczxs = this.form.czxs - this.form.yzxczxs;\n    },\n    filterReset() {\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n      this.params = {\n        //输电\n        lx: 1,\n        status: \"\"\n      };\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        this.loading = true;\n        param.mySorts = [{ prop: \"updateTime\", asc: false }];\n        if (!param.status) {\n          param.status = \"0,1,2,3,4\";\n        }\n        const { data, code } = await getList(param);\n        if (code === \"0000\") {\n          for (let i of data.records) {\n            this.statusOptions.forEach(element => {\n              if (i.status === element.value) {\n                i.statusCn = element.label;\n              }\n            });\n          }\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          this.loading = false;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n\n    //修改按钮\n    getUpdate(row) {\n      this.getCzpmx(row);\n      this.title = \"输电操作票修改\";\n      this.form = { ...row };\n      this.imgList = this.form.imgList;\n      this.isDisabledBj = false;\n      this.isShowDetails = true;\n      this.isDisabled = false;\n      this.getShow();\n    },\n    //详情按钮\n    getDetails(row) {\n      this.getCzpmx(row);\n      this.title = \"输电操作票详情查看\";\n      this.form = { ...row };\n      //图片结合\n      this.imgList = this.form.imgList;\n      this.isDisabled = true;\n      this.isDisabledBj = true;\n      this.isShowDetails = true;\n      this.getShow();\n    },\n\n    // 新增修改 确认按钮\n    async saveRow() {\n      try {\n        this.form.colFirst = this.propTableData.colFirst;\n        this.form.objIdList = this.ids;\n        let { code } = await saveOrUpdates(this.form);\n        if (code === \"0000\") {\n          this.$message.success(\"操作成功\");\n        }\n      } catch (e) {\n        console.log(e);\n      }\n      //重置page页从1开始\n      this.tableAndPageInfo.pager.pageResize = \"Y\";\n      this.getData();\n      this.isShowDetails = false;\n    },\n\n    //删除按钮\n    async deleteRow(row) {\n      let objId = \"\";\n      if (row.objId) {\n        objId = row.objId;\n      } else {\n        objId = this.ids[0];\n      }\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove(objId).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              //重置page页从1开始\n              // this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    //表格新增\n    listFirstAdd() {\n      let row = {\n        czrw: \"\",\n        xlr: \"\",\n        xlsj: \"\",\n        hlsj: \"\",\n        sfwc: \"\"\n      };\n      this.propTableData.colFirst.push(row);\n      this.propTableData.sel = row;\n      this.form.czxs = this.propTableData.colFirst.length;\n      this.form.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //表格删除\n    listFirstDel(index, row) {\n      this.ids.push(row.objId);\n      this.propTableData.colFirst.splice(index, 1);\n      this.form.czxs = this.propTableData.colFirst.length;\n      this.form.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //选择已执行时，操作项目默认默认全选\n    handleChangeOfSfzx(val) {\n      if (val === \"已执行\") {\n        this.form.yzxczxs = this.form.czxs;\n        this.form.wzxczxs = 0;\n        this.propTableData.colFirst.forEach(item => {\n          item.sfwc = true;\n        });\n      } else {\n        this.form.wzxczxs = this.form.czxs;\n        this.form.yzxczxs = 0;\n        this.propTableData.colFirst.forEach(item => {\n          item.sfwc = false;\n        });\n      }\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false;\n    },\n    //选择行\n    selectChange(selection) {\n      this.ids = selection.map(item => item.objId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n      this.selectData = selection;\n    },\n    /**\n     *获取操作票明细\n     */\n    async getCzpmx(row) {\n      try {\n        const { data, code } = await getCzpmxList({ objId: row.objId });\n        if (code === \"0000\") {\n          this.propTableData.colFirst = data;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //关闭线路弹窗\n    handleControlSysbSelectDialog(isShow) {\n      this.isShowXlDialog = isShow;\n    },\n    //线路选择事件\n    sysbSelectedClick() {\n      this.isShowXlDialog = true;\n    },\n    //导出word\n    async exportWord(row) {\n      try {\n        let exportData = { ...row };\n        await exportWord(exportData, \"xldzczp\", \"电力系统线路倒闸操作票.docx\");\n      } catch (e) {\n        this.$message.error(\"导出失败！\");\n      }\n    },\n    //导出Pdf\n    async exportPdf(row) {\n      try {\n        let exportData = { ...row };\n        await exportPdf(exportData, \"xldzczp\", \"电力系统线路倒闸操作票.pdf\");\n      } catch (e) {\n        this.$message.error(\"导出失败！\");\n      }\n    },\n    //预览文件\n    async previewFile(row) {\n      try {\n        let exportData = { ...row };\n        await previewFile(exportData, \"xldzczp\");\n      } catch (e) {\n        this.$message.error(\"预览失败！\");\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.card-style {\n  background-color: #e0eff2;\n  line-height: 3vh;\n  border-radius: 3px;\n  font-weight: 800;\n  padding-left: 1vw;\n  margin-bottom: 3vh;\n}\n\n/*控制input输入框边框是否显示*/\n.elInput >>> .el-input__inner {\n  border: 0;\n}\n\n.center {\n  text-align: center; /*让div内部文字居中*/\n}\n\n.item {\n  width: 8.5rem;\n  height: 1.25rem;\n}\n</style>\n"], "sourceRoot": "src/views/dagangOilfield/czpgl/xldzcz"}]}