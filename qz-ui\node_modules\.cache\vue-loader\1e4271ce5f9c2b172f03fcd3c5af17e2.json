{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\gfdzcz\\dzczml.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\gfdzcz\\dzczml.vue", "mtime": 1751374208669}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsKaW1wb3J0IGFwaSBmcm9tICJAL3V0aWxzL3JlcXVlc3QiOwppbXBvcnQgewogIGV4cG9ydEV4Y2VsLAogIGdldEJkelNlbGVjdExpc3QsCiAgZ2V0TGlzdCwKICByZW1vdmUsCiAgc2F2ZU9yVXBkYXRlLAogIHNhdmVPclVwZGF0ZUN6cCwKICBleHBvcnRXb3JkQnlzZWxlY3Rpb24sCiAgZXhwb3J0V29yZEJ5cGFyYW1zCn0gZnJvbSAiQC9hcGkveXhnbC9nZnl4Z2wvZ2ZkemN6bWwiOwppbXBvcnQgeyBnZXREeHBrTGlzdCwgZ2V0TGlzdHMgfSBmcm9tICJAL2FwaS9iemdsL2dmZHhjenAiOwppbXBvcnQgeyBnZXRMaXN0THNwLCBnZXRMc3BrTGlzdCwgdXBkYXRlQnlJZCB9IGZyb20gIkAvYXBpL3l4Z2wvZ2Z5eGdsL2dmZHpjenAiOwppbXBvcnQgRWxlY3Ryb25pY0F1dGhEaWFsb2cgZnJvbSAiY29tL0VsZWN0cm9uaWNBdXRoRGlhbG9nIjsKaW1wb3J0IENvbXBUYWJsZSBmcm9tICJjb20vQ29tcFRhYmxlIjsKaW1wb3J0IEVsRmlsdGVyIGZyb20gImNvbS9FbEZpbHRlciI7CmltcG9ydCB7IGdldFVzZXJzIH0gZnJvbSAiQC9hcGkvc3lzdGVtL3VzZXJncm91cCI7CmltcG9ydCBhY3Rpdml0aSBmcm9tICJjb20vYWN0aXZpdGlfY3pwIjsKaW1wb3J0IHsgZ2V0VVVJRCB9IGZyb20gIkAvdXRpbHMvcnVveWkiOwppbXBvcnQgeyBnZXRGZ3NPcHRpb25zIH0gZnJvbSAiQC9hcGkveXhnbC9nZnl4Z2wvZ2Z6YmdsIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiZHpjem1sIiwKICBjb21wb25lbnRzOiB7IEVsZWN0cm9uaWNBdXRoRGlhbG9nLCBDb21wVGFibGUsIEVsRmlsdGVyLCBhY3Rpdml0aSB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBoYXNTdXBlclJvbGU6dGhpcy4kc3RvcmUuZ2V0dGVycy5oYXNTdXBlclJvbGUsCiAgICAgIC8vbG9hZGluZzpmYWxzZSwKICAgICAgLy/lt6XkvZzmtYHkvKDlhaXlj4LmlbAKICAgICAgcHJvY2Vzc0RhdGE6IHsKICAgICAgICBwcm9jZXNzRGVmaW5pdGlvbktleTogImN6cHNoIiwKICAgICAgICBidXNpbmVzc0tleTogIiIsCiAgICAgICAgYnVzaW5lc3NUeXBlOiAi5YCS6Ze45pON5L2c56WoIiwKICAgICAgICB2YXJpYWJsZXM6IHt9LAogICAgICAgIGRlZmF1bHRGcm9tOiB0cnVlLAogICAgICAgIG5leHRVc2VyOiAiIiwKICAgICAgICBwcm9jZXNzVHlwZTogImNvbXBsZXRlIgogICAgICB9LAogICAgICBpc1Nob3c6IGZhbHNlLAogICAgICAvL+W8ue<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>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"}, {"version": 3, "sources": ["dzczml.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAm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file": "dzczml.vue", "sourceRoot": "src/views/dagangOilfield/czpgl/gfdzcz", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n      @handleEvent=\"handleEvent\"\n      @handleReset=\"filterReset\"\n    />\n    <div>\n      <el-white class=\"button-group\">\n        <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n          <el-col\n            style=\"\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n          \"\n          >\n            <div>\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-plus\"\n                @click=\"getInster\"\n                v-hasPermi=\"['dzczml:button:add']\"\n                >新增\n              </el-button>\n              <!-- <el-button type=\"primary\" @click=\"exportFun\"\n              >导出\n              </el-button> -->\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-download\"\n                @click=\"exportWord\"\n                >导出</el-button\n              >\n            </div>\n          </el-col>\n        </el-row>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"selectChange\"\n          height=\"66vh\"\n          v-loading=\"loading\"\n        >\n          <el-table-column\n            slot=\"table_eight\"\n            align=\"center\"\n            fixed=\"right\"\n            style=\"display: block\"\n            label=\"操作\"\n            width=\"160\"\n            :resizable=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                @click=\"getDetails(scope.row)\"\n                type=\"text\"\n                size=\"small\"\n                title=\"详情\"\n                class=\"el-icon-view\"\n              ></el-button>\n              <el-button\n                @click=\"getUpdate(scope.row)\"\n                v-if=\"(scope.row.createBy === currentUser || hasSuperRole) && !scope.row.czp\"\n                type=\"text\"\n                size=\"small\"\n                title=\"编辑\"\n                class=\"el-icon-edit\"\n              >\n              </el-button>\n              <el-button\n                @click=\"deleteRow(scope.row)\"\n                v-if=\"\n                  (scope.row.createBy === currentUser ||\n                    hasSuperRole) &&\n                    !scope.row.czp\n                \"\n                type=\"text\"\n                size=\"small\"\n                title=\"删除\"\n                class=\"el-icon-delete\"\n              >\n              </el-button>\n            </template>\n          </el-table-column>\n        </comp-table>\n      </el-white>\n    </div>\n\n    <!-- 详情/新增/修改 操作命令页-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <div>\n          <el-row style=\"display: flex;flex-wrap: wrap;\">\n            <el-col :span=\"8\">\n              <el-form-item label=\"令类型：\" prop=\"zslOrYbl\">\n                <el-switch\n                  :disabled=\"isDisabled\"\n                  style=\"width: 100%\"\n                  v-model=\"form.zslOrYbl\"\n                  active-color=\"#13ce66\"\n                  inactive-color=\"#ff4949\"\n                  active-text=\"正式令\"\n                  inactive-text=\"预备令\"\n                  @change=\"changeZslOrYbl\"\n                >\n                </el-switch>\n              </el-form-item>\n            </el-col>\n            <!--            <el-col :span=\"8\">-->\n            <!--              <el-form-item label=\"编号：\" prop=\"bh\">-->\n            <!--                <el-input v-model=\"form.bh\" disabled placeholder=\"确认后编号自动生成\"/>-->\n            <!--              </el-form-item>-->\n            <!--            </el-col>-->\n            <el-col :span=\"8\">\n              <el-form-item label=\"分公司：\" prop=\"fgs\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.fgs\"\n                  @change=\"handleFgsChange\"\n                  disabled\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in ssgsOptionsDataList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"光伏电站：\" prop=\"bdzmc\">\n                <el-select\n                  style=\"width: 100%\"\n                  filterable\n                  clearable\n                  v-model=\"form.bdzmc\"\n                  ref=\"bdzmc\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择光伏电站\"\n                >\n                  <el-option\n                    v-for=\"item in wzDataListOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"!form.zslOrYbl\"\n                label=\"通知时间：\"\n                prop=\"tzsj\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: false }]\n                    : [{ required: true, message: '不能为空' }]\n                \"\n              >\n                <el-date-picker\n                  v-model=\"form.tzsj\"\n                  :disabled=\"isDisabled\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"!form.zslOrYbl\"\n                label=\"通知人：\"\n                prop=\"tzr\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: false }]\n                    : [{ required: true, message: '不能为空' }]\n                \"\n              >\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.tzr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in tzrOrXlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"!form.zslOrYbl\"\n                label=\"预备令接令人：\"\n                prop=\"ybljlr\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: false }]\n                    : [{ required: true, message: '不能为空' }]\n                \"\n              >\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.ybljlr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in jlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"form.zslOrYbl\"\n                label=\"下令时间：\"\n                prop=\"xlsj\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-date-picker\n                  v-model=\"form.xlsj\"\n                  :disabled=\"isDisabled\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"form.zslOrYbl\"\n                label=\"下令人：\"\n                prop=\"xlr\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.xlr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in tzrOrXlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"form.zslOrYbl\"\n                label=\"接令人：\"\n                prop=\"jlr\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.jlr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in jlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"回令时间：\" prop=\"hlsj\">\n                <el-date-picker\n                  v-model=\"form.hlsj\"\n                  :disabled=\"isDisabled\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  :placeholder=\"isDisabled ? '' : '选择日期时间'\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-form-item label=\"操作任务：\" prop=\"czrw\">\n            <el-input\n              type=\"textarea\"\n              v-model=\"form.czrw\"\n              :disabled=\"isDisabled\"\n              placeholder=\"请输入操作任务\"\n              :rows=\"3\"\n            />\n          </el-form-item>\n        </div>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">关 闭</el-button>\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"saveRow\"\n          >保 存</el-button\n        >\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"createCzp\"\n          >开操作票\n        </el-button>\n        <el-button\n          v-if=\"isDisabled && (form.createBy === currentUser || hasSuperRole)&& !form.czp\"\n          type=\"primary\"\n          @click=\"createBjp\"\n          >开办结票\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!--操作票开票页-->\n    <el-dialog\n      title=\"光伏倒闸操作票开票\"\n      :visible.sync=\"isCzpShowDetails\"\n      append-to-body\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form\n        label-width=\"120px\"\n        ref=\"formCzp\"\n        :model=\"formCzp\"\n        :rules=\"czpRules\"\n      >\n        <div>\n          <el-row>\n            <!--            <el-col :span=\"8\">-->\n            <!--              <el-form-item label=\"编号：\" prop=\"bm\">-->\n            <!--                <el-input v-model=\"formCzp.bm\" disabled placeholder=\"确认后编号自动生成\"/>-->\n            <!--              </el-form-item>-->\n            <!--            </el-col>-->\n            <el-col :span=\"8\">\n              <el-form-item label=\"分公司：\" prop=\"fgs\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formCzp.fgs\"\n                  @change=\"handleFgsChange\"\n                  disabled\n                  placeholder=\"请选择分公司\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in ssgsOptionsDataList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"光伏电站：\" prop=\"bdzmc\">\n                <el-select\n                  style=\"width: 100%\"\n                  filterable\n                  v-model=\"formCzp.bdzmc\"\n                  disabled\n                  placeholder=\"请输入内容\"\n                >\n                  <el-option\n                    v-for=\"item in wzDataListOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作开始时间：\" prop=\"kssj\">\n                <el-date-picker\n                  disabled\n                  v-model=\"formCzp.kssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作结束时间：\" prop=\"jssj\">\n                <el-date-picker\n                  disabled\n                  v-model=\"formCzp.jssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作人：\" prop=\"czr\">\n                <el-input\n                  v-model=\"formCzp.czr\"\n                  :disabled=\"isDisabledCzp\"\n                  placeholder=\"请输入内容\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"监护人：\" prop=\"jhr\">\n                <el-input\n                  v-model=\"formCzp.jhr\"\n                  :disabled=\"isDisabledCzp\"\n                  placeholder=\"请输入内容\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"下令人：\" prop=\"xlr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formCzp.xlr\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                  disabled\n                >\n                  <el-option\n                    v-for=\"item in tzrOrXlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作项数：\" prop=\"czxs\">\n                <el-input-number\n                  v-model=\"formCzp.czxs\"\n                  disabled\n                  placeholder=\"请输入操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"已执行项数：\" prop=\"yzxczxs\">\n                <el-input-number\n                  v-model=\"formCzp.yzxczxs\"\n                  disabled\n                  placeholder=\"请输入已执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"未执行项数：\" prop=\"wzxczxs\">\n                <el-input-number\n                  v-model=\"formCzp.wzxczxs\"\n                  disabled\n                  placeholder=\"请输入未执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"操作任务：\" prop=\"czrw\">\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"formCzp.czrw\"\n                  :disabled=\"isDisabledCzp\"\n                  placeholder=\"请输入内容\"\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"上传图片：\">\n                <el-upload\n                  :disabled=\"isDisabledCzp\"\n                  action=\"\"\n                  ref=\"uploadImg\"\n                  accept=\"image/jpeg,image/jpg,image/png\"\n                  :headers=\"header\"\n                  :multiple=\"true\"\n                  :on-change=\"handleChange\"\n                  :data=\"uploadImgData\"\n                  :file-list=\"imgList\"\n                  :auto-upload=\"false\"\n                  :on-preview=\"handlePictureCardPreview\"\n                  :on-progress=\"handleProgress\"\n                  :on-remove=\"handleRemove\"\n                >\n                  <el-button type=\"primary\" size=\"small\">选择文件</el-button>\n                </el-upload>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n\n        <div>\n          <el-dialog :title=\"titleyl\" :visible.sync=\"yl\" append-to-body>\n            <!--第一个表格-->\n            <div style=\"margin-bottom:10px;\">\n              <el-row>\n                <el-col :span=\"8\">\n                  <el-input\n                    v-model=\"replaceData.oldStr\"\n                    style=\"width:80%\"\n                    placeholder=\"查找字符串\"\n                  >\n                  </el-input>\n                </el-col>\n\n                <el-col :span=\"8\">\n                  <!-- <span> ---</span> -->\n                  <el-input\n                    v-model=\"replaceData.newStr\"\n                    style=\"width:80%\"\n                    placeholder=\"替换后字符串\"\n                  >\n                  </el-input>\n                </el-col>\n                <el-col :span=\"4\">\n                  <el-button\n                    type=\"primary\"\n                    icon=\"el-icon-edit\"\n                    @click=\"replaceStr\"\n                    >批量替换</el-button\n                  >\n                </el-col>\n              </el-row>\n            </div>\n\n            <el-table\n              :data=\"propTableData.colFirst\"\n              ref=\"propTable\"\n              disabled\n              border\n              stripe\n              style=\"width: 100%\"\n              :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n            >\n              <el-table-column\n                align=\"center\"\n                width=\"120\"\n                prop=\"xh\"\n                label=\"顺序号\"\n                sortable\n              >\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"请输入顺序号\"\n                      disabled\n                      v-model=\"scope.row.xh\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"请输入操作项目\"\n                      disabled\n                      v-model=\"scope.row.czrw\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-dialog>\n        </div>\n        <!--列表-->\n        <div>\n          <el-white class=\"mb8 pull-right\">\n            <el-button type=\"primary\" @click=\"getDxpkLists\">典型票库</el-button>\n            <el-button type=\"warning\" @click=\"getLspkList\">历史票库</el-button>\n            <el-button\n              type=\"info\"\n              @click=\"handleYlChange\"\n              style=\"text-align: right\"\n              >预览</el-button\n            >\n          </el-white>\n          <!--第一个表格-->\n          <el-table\n            :data=\"propTableData.colFirst\"\n            height=\"300\"\n            border\n            stripe\n            style=\"width: 100%\"\n            element-loading-text=\"正在获取数据\"\n            element-loading-spinner=\"el-icon-loading\"\n            v-loading=\"loading\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <el-table-column\n              align=\"center\"\n              width=\"120\"\n              prop=\"xh\"\n              label=\"顺序号\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input-number\n                    size=\"small\"\n                    v-model=\"scope.row.xh\"\n                    :min=\"1\"\n                    :precision=\"0\"\n                    controls-position=\"right\"\n                  ></el-input-number>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入操作项目\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" width=\"80\">\n              <template slot=\"header\" slot-scope=\"scope\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  icon=\"el-icon-plus\"\n                  @click=\"listFirstAdd(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n              <template slot-scope=\"scope\">\n                <el-button\n                  type=\"text\"\n                  icon=\"el-icon-delete\"\n                  @click=\"listFirstDel(scope.row)\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"closeCzp\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"saveRowCzp\">保 存</el-button>\n        <el-button type=\"primary\" @click=\"submitCzp\">上 报</el-button>\n      </div>\n    </el-dialog>\n\n    <!--开办结票-->\n    <el-dialog\n      title=\"开办结票\"\n      :visible.sync=\"isBjpShowDetails\"\n      append-to-body\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form\n        label-width=\"120px\"\n        ref=\"formBjp\"\n        :model=\"formBjp\"\n        :rules=\"rules2\"\n      >\n        <div>\n          <el-row>\n            <el-col :span=\"8\">\n              <el-form-item label=\"编号：\" prop=\"bm\">\n                <el-input\n                  v-model=\"formBjp.bm\"\n                  disabled\n                  placeholder=\"确认后编号自动生成\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"分公司：\" prop=\"fgs\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formBjp.fgs\"\n                  @change=\"handleFgsChange\"\n                  disabled\n                  placeholder=\"请选择分公司\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in ssgsOptionsDataList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"光伏电站：\" prop=\"bdzmc\">\n                <el-select\n                  style=\"width: 100%\"\n                  filterable\n                  v-model=\"formBjp.bdzmc\"\n                  placeholder=\"请输入内容\"\n                  disabled\n                >\n                  <el-option\n                    v-for=\"item in wzDataListOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作开始时间：\" prop=\"kssj\">\n                <el-date-picker\n                  v-model=\"formBjp.kssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作结束时间：\" prop=\"jssj\">\n                <el-date-picker\n                  v-model=\"formBjp.jssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作人：\" prop=\"czr\">\n                <el-input v-model=\"formBjp.czr\" placeholder=\"请输入内容\" />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"监护人：\" prop=\"jhr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formBjp.jhr\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in jlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"下令人：\" prop=\"xlr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formBjp.xlr\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in tzrOrXlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"审票人：\" prop=\"bzspr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formBjp.bzspr\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in sprList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作项数：\" prop=\"czxs\">\n                <el-input-number\n                  v-model=\"formBjp.czxs\"\n                  placeholder=\"请输入操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"已执行项数：\" prop=\"yzxczxs\">\n                <el-input-number\n                  v-model=\"formBjp.yzxczxs\"\n                  placeholder=\"请输入已执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"未执行项数：\" prop=\"wzxczxs\">\n                <el-input-number\n                  v-model=\"formBjp.wzxczxs\"\n                  placeholder=\"请输入未执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"操作任务：\" prop=\"czrw\">\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"formBjp.czrw\"\n                  placeholder=\"请输入内容\"\n                  disabled\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"上传图片：\">\n                <el-upload\n                  action=\"\"\n                  ref=\"uploadImg\"\n                  accept=\"image/jpeg,image/jpg,image/png\"\n                  :headers=\"header\"\n                  :multiple=\"true\"\n                  :on-change=\"handleChange\"\n                  :data=\"uploadImgData\"\n                  :file-list=\"imgList\"\n                  :auto-upload=\"false\"\n                  list-type=\"picture-card\"\n                  :on-preview=\"handlePictureCardPreview\"\n                  :on-progress=\"handleProgress\"\n                  :on-remove=\"handleRemove\"\n                >\n                  <i class=\"el-icon-plus\"></i>\n                </el-upload>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"closeCzp\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"saveRowBjp\">确 认</el-button>\n      </div>\n    </el-dialog>\n\n    <!--典型票库-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isDxpShowDetails\"\n      width=\"50%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"180px\" ref=\"form\" :model=\"form\">\n        <el-row>\n          <!--查询及列表-->\n          <el-col :span=\"24\">\n            <el-form\n              :model=\"queryParams\"\n              class=\"searchForm\"\n              ref=\"queryForm\"\n              label-width=\"100px\"\n              v-show=\"isShowSx\"\n            >\n              <el-row>\n                <el-col :span=\"12\">\n                  <el-form-item label=\"典型票名称：\" prop=\"dxpmc\">\n                    <el-input\n                      placeholder=\"请输入典型票名称\"\n                      v-model=\"queryParams.dxpmc\"\n                      clearable\n                    />\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"12\">\n                  <el-form-item label=\"操作任务：\" prop=\"czrw\">\n                    <el-input\n                      placeholder=\"请输入操作任务\"\n                      v-model=\"queryParams.czrw\"\n                      clearable\n                    />\n                  </el-form-item>\n                </el-col>\n                <div style=\"float: right;margin-bottom: 10px\">\n                  <el-button\n                    type=\"primary\"\n                    icon=\"el-icon-search\"\n                    @click=\"handleQuery\"\n                    >搜索</el-button\n                  >\n                  <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\"\n                    >重置</el-button\n                  >\n                </div>\n              </el-row>\n            </el-form>\n            <div style=\"float: left;margin-bottom: 10px\">\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-search\"\n                @click=\"isShowSx = isShowSx ? false : true\"\n                >筛 选</el-button\n              >\n            </div>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <!--主表信息-->\n            <comp-table\n              :table-and-page-info=\"propTableDataDxp\"\n              height=\"400\"\n              border\n              stripe\n              style=\"width: 100%\"\n              max-height=\"60vh\"\n              @getMethod=\"handleQuery\"\n              @rowClick=\"changeMx\"\n            >\n            </comp-table>\n          </el-col>\n        </el-row>\n        <!--子表信息-->\n        <el-row>\n          <el-table\n            :data=\"propTableDataDxpMx.colFirst\"\n            :disabled=\"isDisabled\"\n            height=\"300\"\n            border\n            stripe\n            style=\"width: 100%\"\n            max-height=\"40vh\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <!--子表列表-->\n            <el-table-column\n              align=\"center\"\n              prop=\"xh\"\n              label=\"序号\"\n              width=\"100\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"序号\"\n                    :disabled=\"isShowDxp\"\n                    v-model=\"scope.row.xh\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"操作项目\"\n                    :disabled=\"isShowDxp\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n          </el-table>\n        </el-row>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"saveRowDxp\">确 认 </el-button>\n      </div>\n    </el-dialog>\n\n    <!--历史票库-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isLspShowDetails\"\n      width=\"50%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"180px\" ref=\"form\" :model=\"form\">\n        <el-form\n          :model=\"queryParamsLsp\"\n          class=\"searchForm\"\n          ref=\"queryForm\"\n          label-width=\"100px\"\n          v-show=\"isShowSx\"\n        >\n          <el-row>\n            <el-col :span=\"12\">\n              <el-form-item label=\"操作时间\" prop=\"czsjArr\">\n                <el-date-picker\n                  type=\"daterange\"\n                  range-separator=\"-\"\n                  start-placeholder=\"开始时间\"\n                  end-placeholder=\"结束时间\"\n                  v-model=\"queryParamsLsp.czsjArr\"\n                  clearable\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <!--<el-col :span=\"12\">\n              <el-form-item label=\"操作结束时间\" prop=\"jssj\">\n                <el-input placeholder=\"请输入操作结束时间\" v-model=\"queryParamsLsp.jssj\" clearable primary style=\"width: 100%\"/>\n              </el-form-item>\n            </el-col>-->\n            <el-col :span=\"24\">\n              <el-form-item label=\"操作任务\" prop=\"czrw\">\n                <el-input\n                  type=\"textarea\"\n                  :rows=\"1\"\n                  placeholder=\"请输入操作任务\"\n                  v-model=\"queryParamsLsp.czrw\"\n                  clearable\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <el-col :span=\"12\">\n              <el-form-item label=\"操作人\" prop=\"czr\">\n                <el-input\n                  placeholder=\"请输入操作人\"\n                  v-model=\"queryParamsLsp.czr\"\n                  clearable\n                  primary\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n            <!-- <el-col :span=\"12\">\n              <el-form-item label=\"监护人\" prop=\"jhr\">\n                <el-input\n                  placeholder=\"请输入监护人\"\n                  v-model=\"queryParamsLsp.jhr\"\n                  clearable\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col> -->\n          </el-row>\n          <el-row>\n            <!-- <el-col :span=\"12\">\n              <el-form-item label=\"下令人\" prop=\"xlr\">\n                <el-input\n                  placeholder=\"请输入下令人\"\n                  v-model=\"queryParamsLsp.xlr\"\n                  clearable\n                  primary\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col> -->\n            <div style=\"float: right;margin-bottom: 10px\">\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-search\"\n                @click=\"handleQueryLsp\"\n                >搜索</el-button\n              >\n              <el-button icon=\"el-icon-refresh\" @click=\"resetQueryLsp\"\n                >重置</el-button\n              >\n            </div>\n          </el-row>\n        </el-form>\n        <!--主表信息-->\n        <div>\n          <div style=\"float: left;margin-bottom: 10px\">\n            <el-button\n              type=\"primary\"\n              icon=\"el-icon-search\"\n              @click=\"isShowSx = isShowSx ? false : true\"\n              >筛 选</el-button\n            >\n          </div>\n          <el-table\n            @row-click=\"changeLspMx\"\n            :data=\"propTableDataLsp.colFirst\"\n            :disabled=\"isDisabled\"\n            height=\"300\"\n            border\n            stripe\n            style=\"width: 100%\"\n            max-height=\"40vh\"\n          >\n            <el-table-column\n              align=\"center\"\n              prop=\"gfzmc\"\n              label=\"光伏电站\"\n              width=\"170\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入光伏电站\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.gfzmc\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"kssj\"\n              label=\"操作开始时间\"\n              width=\"170\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入开始时间\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.kssj\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"jssj\"\n              label=\"操作结束时间\"\n              width=\"170\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入结束时间\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.jssj\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"czrw\"\n              label=\"操作任务\"\n              width=\"230\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    type=\"textarea\"\n                    :rows=\"2\"\n                    placeholder=\"请输入操作任务\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"czr\"\n              label=\"操作人\"\n              width=\"120\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.czr\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"jhrmc\"\n              label=\"监护人\"\n              width=\"120\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.jhrmc\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"xlr\"\n              label=\"下令人\"\n              width=\"120\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.xlrmc\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"spr\"\n              label=\"审票人\"\n              width=\"150\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.bzsprmc\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n        <!--子表信息-->\n        <div>\n          <el-table\n            :data=\"propTableDataLspMx.colFirst\"\n            :disabled=\"isDisabled\"\n            height=\"300\"\n            border\n            stripe\n            style=\"width: 100%\"\n            max-height=\"40vh\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <!--子表列表-->\n            <el-table-column\n              align=\"center\"\n              width=\"120\"\n              prop=\"xh\"\n              label=\"顺序号\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入顺序号\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.xh\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入操作项目\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"saveRowLsp\">确 认 </el-button>\n      </div>\n    </el-dialog>\n\n    <!--图片放大展示弹出框-->\n    <el-dialog title=\"放大\" :visible.sync=\"imgDialogVisible\" v-dialogDrag>\n      <img width=\"100%\" :src=\"dialogImageUrl\" alt=\"\" />\n    </el-dialog>\n\n    <!--  工作流  -->\n    <activiti\n      ref=\"activiti\"\n      :processData=\"processData\"\n      :isShow=\"isShow\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n    ></activiti>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport api from \"@/utils/request\";\nimport {\n  exportExcel,\n  getBdzSelectList,\n  getList,\n  remove,\n  saveOrUpdate,\n  saveOrUpdateCzp,\n  exportWordByselection,\n  exportWordByparams\n} from \"@/api/yxgl/gfyxgl/gfdzczml\";\nimport { getDxpkList, getLists } from \"@/api/bzgl/gfdxczp\";\nimport { getListLsp, getLspkList, updateById } from \"@/api/yxgl/gfyxgl/gfdzczp\";\nimport ElectronicAuthDialog from \"com/ElectronicAuthDialog\";\nimport CompTable from \"com/CompTable\";\nimport ElFilter from \"com/ElFilter\";\nimport { getUsers } from \"@/api/system/usergroup\";\nimport activiti from \"com/activiti_czp\";\nimport { getUUID } from \"@/utils/ruoyi\";\nimport { getFgsOptions } from \"@/api/yxgl/gfyxgl/gfzbgl\";\n\nexport default {\n  name: \"dzczml\",\n  components: { ElectronicAuthDialog, CompTable, ElFilter, activiti },\n  data() {\n    return {\n      hasSuperRole:this.$store.getters.hasSuperRole,\n      //loading:false,\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"czpsh\",\n        businessKey: \"\",\n        businessType: \"倒闸操作票\",\n        variables: {},\n        defaultFrom: true,\n        nextUser: \"\",\n        processType: \"complete\"\n      },\n      isShow: false,\n      //弹出框标题\n      activitiOption: { title: \"上报\" },\n      czpRules: {\n        // xlr: [\n        //   {required: true, message: '不能为空', trigger: 'blur'}\n        // ],\n      },\n      rules: {\n        bdzmc: [{ required: true, message: \"不能为空\", trigger: \"select\" }],\n        fgs: [{ required: true, message: \"不能为空\", trigger: \"select\" }],\n        czrw: [{ required: true, message: \"不能为空\", trigger: \"blur\" }]\n      },\n      rules2: {\n        kssj: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        jssj: [{ required: true, message: \"不能为空\", trigger: \"select\" }],\n        czr: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        jhr: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        xlr: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        czxs: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        yzxczxs: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        wzxczxs: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        bzspr: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        czrw: [{ required: true, message: \"不能为空\", trigger: \"blur\" }]\n      },\n      currentUser: this.$store.getters.name,\n      yl: false,\n      replaceData: {},\n      titleyl: \"\",\n      //图片地址url\n      dialogImageUrl: \"\",\n      //展示图片dialog控制\n      imgDialogVisible: false,\n      //上传图片时的携带的其他参数\n      uploadImgData: {\n        businessId: \"\" //携带的表单主键id\n      },\n      //上传图片时的请求头\n      header: {},\n      //图片list\n      imgList: [],\n      isShowSx: false,\n      bdzList: [],\n      //弹出框内新增时下拉框所属位置数据\n      wzDataListOptions: [],\n      isShowDxp: false,\n      isShowLsp: false,\n      loading: false,\n      isDisabledCzp: false,\n      isDxpShowDetails: false,\n      isLspShowDetails: false,\n      //操作票弹框是否显示\n      isCzpShowDetails: false,\n      isBjpShowDetails: false,\n      // 多选框选中的id\n      ids: [],\n      selectData: [],\n      //倒闸操作票命令\n      params: {\n        //光伏\n        lx: 2\n      },\n      //典型票查询条件\n      queryParams: {},\n      //历史票查询条件\n      queryParamsLsp: {\n        status: \"4\",\n        lx: 4\n      },\n      //弹出框中表格数据\n      propTableData: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //弹出框中表格数据\n      propTableDataDxpMx: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //弹出框中表格数据\n      propTableDataDxp: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 1,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"光伏电站\", prop: \"sbmcms\", minWidth: \"200\" },\n          { label: \"典型操作票名称\", prop: \"dxpmc\", minWidth: \"200\" },\n          { label: \"操作任务\", prop: \"czrw\", minWidth: \"200\" }\n        ],\n        option: { checkBox: false, serialNumber: true },\n        sel: null // 选中行\n      },\n      //弹出框中表格数据\n      propTableDataLspMx: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //弹出框中表格数据\n      propTableDataLsp: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //form表单\n      form: {\n        bh: \"\",\n        bdzmc: \"\",\n        tzsj: \"\",\n        tzr: \"\",\n        xlsj: \"\",\n        xlr: \"\",\n        czrw: \"\",\n        jlr: \"\",\n        hlsj: \"\",\n        status: \"\"\n      },\n      // 操作票\n      formCzp: {\n        bh: \"\",\n        bdzmc: \"\",\n        kssj: \"\",\n        jssj: \"\",\n        czrw: \"\",\n        czr: \"\",\n        jhr: \"\",\n        xlr: \"\",\n        spr: \"\",\n        lx: 2, //变电\n        colFirst: [],\n        status: \"\"\n      },\n      // 办结票\n      formBjp: {\n        bh: \"\",\n        bdzmc: \"\",\n        kssj: \"\",\n        jssj: \"\",\n        czrw: \"\",\n        czr: \"\",\n        jhr: \"\",\n        xlr: \"\",\n        spr: \"\",\n        lx: 2, //变电\n        colFirst: [],\n        status: \"\"\n      },\n\n      //所属分公司下拉框数据\n      ssgsOptionsDataList: [],\n\n      //详情弹框是否显示\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          bh: \"\",\n          bdzmc: \"\",\n          tzsjArr: \"\",\n          tzr: \"\",\n          xlsjArr: \"\",\n          xlr: \"\",\n          czrw: \"\",\n          jlr: \"\",\n          hlsjArr: \"\"\n        }, //查询条件\n        fieldList: [\n          // {\n          //   label: '令',\n          //   type: 'switch',\n          //   value: 'yblOrZsl',\n          //   textStart:'预备令',\n          //   textEnd:'正式令'\n          // },\n          // {label: '编号', value: 'bh', type: 'input', clearable: true},\n          // /*{ label: '状态', value: 'status', type: 'select', clearable: true },*/\n          {\n            label: \"分公司\",\n            value: \"fgs\",\n            type: \"select\",\n            options: [],\n            clearable: true\n          },\n          {\n            label: \"光伏电站\",\n            value: \"bdzmc\",\n            type: \"select\",\n            options: [],\n            clearable: true,\n            filterable: true\n          },\n          {\n            label: \"通知时间\",\n            value: \"tzsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"通知人\", value: \"tzr\", type: \"input\", clearable: true },\n          {\n            label: \"下令时间\",\n            value: \"xlsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"下令人\", value: \"xlr\", type: \"input\", clearable: true },\n          { label: \"操作任务\", value: \"czrw\", type: \"input\", clearable: true },\n          { label: \"接令人\", value: \"jlr\", type: \"input\", clearable: true },\n          {\n            label: \"回令时间\",\n            value: \"hlsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          /* { label: '状态', prop: 'status', minWidth: '70' },*/\n          { label: \"分公司\", prop: \"fgsmc\", minWidth: \"120\" },\n          { label: \"光伏电站\", prop: \"bdzmcs\", minWidth: \"120\" },\n          { label: \"通知人\", prop: \"tzrxm\", minWidth: \"80\" },\n          { label: \"通知时间\", prop: \"tzsj\", minWidth: \"140\" },\n          { label: \"下令人\", prop: \"xlrmc\", minWidth: \"80\" },\n          { label: \"下令时间\", prop: \"xlsj\", minWidth: \"140\" },\n          { label: \"操作任务\", prop: \"czrw\", minWidth: \"160\" },\n          { label: \"接令人\", prop: \"jlrxm\", minWidth: \"80\" },\n          { label: \"预备令接令人\", prop: \"ybljlrxm\", minWidth: \"100\" },\n          { label: \"回令时间\", prop: \"hlsj\", minWidth: \"140\" }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      tzrOrXlrList: [],\n      jlrList: [],\n      alljlrList: [],\n      sprList: []\n    };\n  },\n  async mounted() {\n    //获取token\n    this.header.token = getToken();\n    //光伏电站下拉框\n    this.getFgsOptions();\n    this.getAllBdzSelectList();\n    this.tzrOrXlrList = await this.getGroupUsers(132, \"\");\n    this.alljlrList = await this.getGroupUsers(133, \"\");\n    this.sprList = await this.getGroupUsers(134, \"\");\n    await this.getData();\n  },\n  methods: {\n    /**\n     * 获取分公司下拉数据\n     */\n    getFgsOptions() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.ssgsOptionsDataList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"fgs\") {\n            return (item.options = this.ssgsOptionsDataList);\n          }\n        });\n      });\n    },\n    //导出\n    exportFun() {\n      exportExcel(this.params, \"操作命令信息\");\n    },\n    //工作流回传数据\n    async todoResult(data) {\n      let row = {\n        objId: data.businessKey,\n        isStart: 1,\n        isBack: data.processType === \"rollback\" ? 1 : 0\n      };\n      row.status = \"1\";\n      row.bzspr = data.nextUser;\n      let { code } = await updateById(row);\n      if (code === \"0000\") {\n        await this.getData();\n      } else {\n        this.$message.warning(\"操作失败\");\n      }\n    },\n    //关闭弹窗\n    closeActiviti() {\n      if (this.$refs.activiti.$refs.form) {\n        this.$refs.activiti.$refs.form.resetFields();\n      }\n      this.isShow = false;\n    },\n    getAllBdzSelectList() {\n      getBdzSelectList({}).then(res => {\n        this.bdzList = res.data;\n      });\n    },\n    //所属公司change事件\n    async handleFgsChange(fgsValue) {\n      //清空之前得选中值\n      this.wzDataListOptions = [];\n      this.$set(this.form, \"bdzmc\", \"\");\n      this.$set(this.form, \"tzr\", \"\");\n      this.jlrList = [];\n      this.$set(this.form, \"jlr\", \"\");\n      this.$set(this.form, \"ybljlr\", \"\");\n      //获取光伏电站方法\n      await this.fgsChange(fgsValue);\n    },\n    async fgsChange(fgsValue) {\n      //获取光伏电站方法\n      this.getBdzSelectList(fgsValue);\n      this.jlrList = await this.getGroupUsers(133, fgsValue);\n    },\n    handleYlChange() {\n      this.titleyl = \"查看操作项目\";\n      this.yl = true;\n    },\n    replaceStr() {\n      this.propTableData.colFirst.forEach(item => {\n        item.czrw = item.czrw.replaceAll(\n          this.replaceData.oldStr,\n          this.replaceData.newStr\n        );\n      });\n    },\n    //改造后的上传多个图片文件\n    uploadForm() {\n      var newUrl = []; //用来存放当前未改动过的图片url,此图片不进行删除处理，其余当前业务id下面的图片数据将进行删除\n      var imageType = [\"png\", \"jpg\"];\n      const formData = new FormData();\n      // 因为要传一个文件数组过去，所以要循环append\n      this.imgList.forEach(file => {\n        if (file.raw == undefined) {\n          newUrl.push(file.url);\n        }\n        formData.append(\"files\", file.raw);\n      });\n      formData.append(\"businessId\", this.uploadImgData.businessId); // 自定义参数\n      formData.append(\"newUrl\", newUrl); // 未改动过的图片url\n      formData.append(\"type\", imageType); // 未改动过的图片url\n      api\n        .requestPost(\"/isc-api/file/uploadFiles\", formData, 1)\n        .then(res => {\n          // 清空图片列表（一定要清空，否则上传成功后还是会调用handleChange（）函数，上传成功后列表中还存在图片）\n          this.imgList = [];\n          //查询接口数据\n          this.getData();\n        })\n        .catch(res => {\n          this.$message.success(\"图片上传失败！\");\n        });\n    },\n    // 选择文件时，往fileList里添加\n    handleChange(file, fileList) {\n      this.imgList = fileList;\n    },\n    handleProgress(event, file, fileList) {},\n    //图片移除\n    handleRemove(file, fileList) {\n      this.imgList = fileList;\n    },\n    //图片放大\n    handlePictureCardPreview(file) {\n      this.dialogImageUrl = file.url;\n      this.imgDialogVisible = true;\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        this.loading = true;\n        param.mySorts = [{ prop: \"updateTime\", asc: false }];\n        const { data, code } = await getList(param);\n        if (code === \"0000\") {\n          for (let i of data.records) {\n            i.fgsmc = this.formatSsgs(i.fgs);\n            i.tzrxm = this.formatXlrOrTzr(i.tzr);\n            i.xlrxm = this.formatXlrOrTzr(i.xlr);\n            i.jlrxm = this.formatJlr(i.jlr);\n            i.ybljlrxm = this.formatJlr(i.ybljlr);\n          }\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          this.loading = false;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n\n    //新增按钮\n    async getInster() {\n      this.title = \"光伏倒闸操作命令增加\";\n      this.isDisabled = false;\n      this.form = { fgs: this.$store.getters.deptId.toString() };\n      await this.fgsChange(this.form.fgs);\n      this.form.status = \"0\";\n      this.isShowDetails = true;\n    },\n    //修改按钮\n    async getUpdate(row) {\n      this.title = \"光伏倒闸操作命令修改\";\n      this.isDisabled = false;\n      this.form = { ...row };\n      await this.fgsChange(this.form.fgs);\n      this.isShowDetails = true;\n    },\n\n    //详情按钮\n    async getDetails(row) {\n      this.title = \"光伏倒闸操作命令详情\";\n      this.form = { ...row };\n      await this.fgsChange(this.form.fgs);\n      this.isDisabled = true;\n      this.isShowDetails = true;\n    },\n    /**\n     * 操作票开票按钮\n     * */\n    async createCzp() {\n      await this.$refs[\"form\"].validate(async valid => {\n        if (valid) {\n          try {\n            let { code, data } = await saveOrUpdate(this.form);\n            if (code === \"0000\") {\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.isShowDetails = false;\n              await this.getData();\n              //操作票弹出框\n              this.isDisabledCzp = true;\n              this.propTableData.colFirst = [];\n              //清除校验提示\n              this.$nextTick(function() {\n                this.$refs[\"formCzp\"].clearValidate();\n              });\n              this.formCzp = {};\n              //光伏操作票类型为4\n              this.formCzp.lx = 4;\n              this.formCzp.status = \"0\";\n              this.formCzp.czml = data.objId;\n              this.formCzp.bdzmc = data.bdzmc;\n              if (data.xlr) {\n                this.formCzp.xlr = data.xlr;\n              }\n              this.formCzp.czrw = data.czrw;\n              this.formCzp.fgs = data.fgs;\n              this.formCzp.bm = data.bh;\n              this.isDisabled = true;\n              this.isCzpShowDetails = true;\n            }\n          } catch (e) {\n            console.log(e);\n          }\n        }\n      });\n    },\n    /**\n     * 开办结票\n     * */\n    createBjp() {\n      this.isBjpShowDetails = true;\n      this.isShowDetails = false;\n      this.formBjp.bdzmc = this.form.bdzmc;\n      this.formBjp.xlr = this.form.xlr;\n      this.formBjp.czrw = this.form.czrw;\n      this.formBjp.fgs = this.form.fgs;\n      this.formBjp.status = \"4\";\n      this.formBjp.lx = 2;\n      this.formBjp.czml = this.form.objId;\n    },\n    async saveRow() {\n      await this.$refs[\"form\"].validate(async valid => {\n        if (valid) {\n          try {\n            let { code } = await saveOrUpdate(this.form);\n            if (code === \"0000\") {\n              this.$message.success(\"操作成功\");\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.isShowDetails = false;\n              await this.getData();\n            }\n          } catch (e) {\n            console.log(e);\n          }\n        }\n      });\n    },\n    //删除按钮\n    deleteRow(row) {\n      this.form = { ...row };\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove([this.form.objId]).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {});\n    },\n    //关闭弹窗\n    close() {\n      this.isDxpShowDetails = false;\n      this.isLspShowDetails = false;\n      this.isShowDetails = false;\n    },\n    //选择行\n    selectChange(selection) {\n      // this.ids = selection.map(item => item.objId);\n      // this.single = selection.length !== 1\n      // this.multiple = !selection.length\n      this.selectData = selection;\n    },\n    //表格新增\n    listFirstAdd() {\n      let row = {\n        xh: \"\",\n        czrw: \"\",\n        sfwc: \"\",\n        uuid: getUUID()\n      };\n      this.propTableData.colFirst.push(row);\n      this.propTableData.sel = row;\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //表格删除\n    listFirstDel(row) {\n      if (row.objId) {\n        this.ids.push(row.objId);\n      }\n      this.propTableData.colFirst = this.propTableData.colFirst.filter(\n        item => item.uuid !== row.uuid\n      );\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //操作票关闭弹窗\n    closeCzp() {\n      this.isCzpShowDetails = false;\n      this.isBjpShowDetails = false;\n    },\n\n    //操作票确定按钮\n    saveRowCzp() {\n      this.formCzp.colFirst = this.propTableData.colFirst;\n      this.formCzp.objIdList = this.ids;\n      let tableValid = this.propTableData.colFirst.some(item => !item.czrw);\n      if (tableValid) {\n        this.$message.error(\"操作项目存在空项，请检查\");\n      }\n      this.$refs[\"formCzp\"].validate(async valid => {\n        if (valid && !tableValid) {\n          console.log(this.formCzp,'111111111')\n          saveOrUpdateCzp(this.formCzp).then(res => {\n            if (res.code === \"0000\") {\n              this.$message.success(\"操作成功\");\n              this.isCzpShowDetails = false;\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            }\n          });\n        }\n      });\n    },\n\n    //直接上报操作票\n    submitCzp() {\n      this.formCzp.colFirst = this.propTableData.colFirst;\n      this.formCzp.objIdList = this.ids;\n      let tableValid = this.propTableData.colFirst.some(item => !item.czrw);\n      if (tableValid) {\n        this.$message.error(\"操作项目存在空项，请检查\");\n      }\n      this.$refs[\"formCzp\"].validate(async valid => {\n        if (valid && !tableValid) {\n          saveOrUpdateCzp(this.formCzp).then(res => {\n            if (res.code === \"0000\") {\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n              let data = res.data;\n              this.isCzpShowDetails = false;\n              this.processData.variables.pass = true;\n              this.processData.businessKey = data.objId;\n              this.processData.processType = \"complete\";\n              this.activitiOption.title = \"提交\";\n              this.processData.defaultFrom = true;\n              this.processData.rylx = \"班组审核人\";\n              this.processData.dw = data.fgs;\n              this.processData.personGroupId = 134;\n              this.processData.routePath =\n                \"/gfgl/bddzcz/czpgl/gfdzcz/dzczp\";\n              this.isShow = true;\n            }\n          });\n        }\n      });\n    },\n\n    //办结票确定按钮\n    saveRowBjp() {\n      this.$refs[\"formBjp\"].validate(async valid => {\n        if (valid) {\n          saveOrUpdateCzp(this.formBjp).then(res => {\n            if (res.code === \"0000\") {\n              this.uploadImgData.businessId = res.data.objId;\n              //开始上传图片\n              this.uploadForm();\n              this.$message.success(\"操作成功\");\n              this.isBjpShowDetails = false;\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            }\n          });\n        }\n      });\n    },\n    /**\n     * -----------------------------------典型票库---------------------------------------\n     * */\n    //获取典型操作票\n    getDxpkLists() {\n      this.title = \"典型票库查询\";\n      this.isDxpShowDetails = true;\n      this.isDisabled = false;\n      this.isShowDxp = true;\n      this.queryParams.sbmc = this.form.bdzmc;\n      this.handleQuery();\n    },\n    async changeMx(row) {\n      try {\n        const { data, code } = await getDxpkList({ objId: row.objId });\n        if (code === \"0000\") {\n          this.propTableDataDxpMx.colFirst = data;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //查询条件\n    async handleQuery(params) {\n      let param = { ...params, ...this.queryParams };\n      getLists(param).then(response => {\n        this.propTableDataDxp.tableData = response.data.records;\n        this.propTableDataDxp.pager.total = response.data.total;\n      });\n    },\n    //重置条件\n    resetQuery() {\n      this.queryParams = {};\n      this.handleQuery();\n    },\n    //典型票库确认按钮\n    saveRowDxp() {\n      this.propTableData.colFirst = this.propTableDataDxpMx.colFirst;\n      this.propTableData.colFirst.forEach(e => {\n        e.uuid = getUUID();\n      });\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n      this.isDxpShowDetails = false;\n    },\n    /**\n     * -----------------------------------历史票库---------------------------------------\n     * */\n    //获取历史操作票\n    getLspkList() {\n      this.title = \"历史票库查询\";\n      this.isLspShowDetails = true;\n      this.isDisabled = false;\n      this.isShowLsp = true;\n      this.queryParamsLsp.bdzmc = this.form.bdzmc;\n      this.handleQueryLsp();\n    },\n    // 当点击行时，传入参数查询\n    async changeLspMx(row) {\n      try {\n        const { data, code } = await getLspkList(row.objId);\n        if (code === \"0000\") {\n          this.propTableDataLspMx.colFirst = data;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //历史票库确认按钮\n    saveRowLsp() {\n      this.propTableData.colFirst = this.propTableDataLspMx.colFirst;\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n      this.propTableData.colFirst.forEach(e => {\n        e.sfwc = false;\n        e.uuid = getUUID();\n      });\n      this.isLspShowDetails = false;\n    },\n    //查询条件\n    handleQueryLsp() {\n      getListLsp(this.queryParamsLsp).then(response => {\n        this.propTableDataLsp.colFirst = response.data.records;\n      });\n    },\n    //重置条件\n    resetQueryLsp() {\n      this.queryParamsLsp = {\n        status: \"4\",\n        lx: 4\n      };\n      this.handleQueryLsp();\n    },\n    /**\n     * 获取光伏电站下拉框数据\n     */\n    getBdzSelectList(fgs) {\n      getBdzSelectList({ ssdwbm: fgs }).then(res => {\n        this.wzDataListOptions = res.data;\n      });\n    },\n    //下拉框change事件\n    handleEvent(val, eventValue) {\n      if (val.label === \"fgs\") {\n        getBdzSelectList({ ssdwbm: val.value.toString() }).then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value === \"bdzmc\") {\n              this.$set(eventValue, \"bdzmc\", \"\");\n              return (item.options = res.data);\n            }\n          });\n        });\n      }\n    },\n    filterReset() {\n      this.params = {\n        //光伏\n        lx: 2\n      };\n    },\n    formatSsgs(ssgs) {\n      let pageOrganizationSelectedList = JSON.parse(JSON.stringify(this.ssgsOptionsDataList))\n      pageOrganizationSelectedList.push({label: '港东变电分公司', value: '3002'})\n      pageOrganizationSelectedList.push({label: '港中变电分公司', value: '3003'})\n      if (ssgs) {\n        let filter = pageOrganizationSelectedList.filter(g => g.value === ssgs);\n        if (filter.length > 0) {\n          return filter[0].label;\n        } else {\n          return \"\";\n        }\n      } else {\n        return \"\";\n      }\n    },\n    getGroupUsers(personGroupId, deptId) {\n      return getUsers({\n        personGroupId: personGroupId,\n        deptId: deptId,\n        deptName: \"\"\n      }).then(res => {\n        return res.data;\n      });\n    },\n    formatXlrOrTzr(xlr) {\n      if (xlr) {\n        let filter = this.tzrOrXlrList.filter(g => g.userName === xlr);\n        if (filter.length > 0) {\n          return filter[0].nickName;\n        } else {\n          return \"\";\n        }\n      } else {\n        return \"\";\n      }\n    },\n\n    formatJlr(jlr) {\n      if (jlr) {\n        let filter = this.alljlrList.filter(g => g.userName === jlr);\n        if (filter.length > 0) {\n          return filter[0].nickName;\n        } else {\n          return \"\";\n        }\n      } else {\n        return \"\";\n      }\n    },\n    //导出word\n    exportWord() {\n      let params = {\n        data: this.params,\n        url: \"bzBddzczml\"\n      };\n      let fileName = \"光伏倒闸操作命令记录\";\n      if (!this.selectData.length > 0) {\n        params.data = this.params;\n        exportWordByparams(params, fileName);\n      } else {\n        params.data = this.selectData;\n        exportWordByselection(params, fileName);\n      }\n    },\n    changeZslOrYbl(val) {\n      if (val) {\n        this.$set(this.form, \"tzsj\", null);\n        this.$set(this.form, \"tzr\", \"\");\n        this.$set(this.form, \"ybljlr\", \"\");\n      } else {\n        this.$set(this.form, \"xlsj\", null);\n        this.$set(this.form, \"xlr\", \"\");\n        this.$set(this.form, \"jlr\", \"\");\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.card-style {\n  background-color: #e0eff2;\n  line-height: 3vh;\n  border-radius: 3px;\n  font-weight: 800;\n  padding-left: 1vw;\n  margin-bottom: 3vh;\n}\n\n/*控制input输入框边框是否显示*/\n.elInput >>> .el-input__inner {\n  border: 0;\n}\n\n.box-card {\n  margin-bottom: 2vh !important;\n}\n</style>\n"]}]}