{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\bdqxwh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\bdqxwh.vue", "mtime": 1706897322432}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7Z2V0UXhMaXN0LAogIGdldFF4c2JUcmVlLAogIGdldFNibHhMaXN0LAogIGdldFNiYmpMaXN0LAogIGdldFNiYndMaXN0LAogIGdldFF4bXNMaXN0LAogIGdldEZseWpMaXN0LAogIGFkZEZseWosCiAgdXBkYXRlRmx5aiwKICBkZWxldGVGbHlqQnlJZCwKICBhZGRReG1zLAogIGFkZFNiYncsCiAgYWRkU2JiaiwKfSBmcm9tICdAL2FwaS9kYWdhbmdPaWxmaWVsZC9iemdsL3NicXh3aC9zYnF4d2gnCmltcG9ydCB7Z2V0RGljdFR5cGVEYXRhfSBmcm9tICdAL2FwaS9zeXN0ZW0vZGljdC9kYXRhJwppbXBvcnQgeyBMb2FkaW5nIH0gZnJvbSAnZWxlbWVudC11aScKaW1wb3J0IHtleHBvcnRFeGNlbH0gZnJvbSAnQC9hcGkvYnpnbC95c2J6ay95c2J6aycKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnc2JseHdoJywKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbG9hZDpmYWxzZSwKICAgICAgYWRkRmx5ajpmYWxzZSwvL+aYr+WQpuaWsOWinuWIhuexu+S+neaNrgogICAgICBmaWx0ZXJJbmZvOiB7CiAgICAgICAgZGF0YTogewogICAgICAgICAgc2JiajogJycsCiAgICAgICAgICBzYmJ3OiAnJywKICAgICAgICAgIHF4bXM6ICcnLAogICAgICAgICAgZmx5ajogJycsCiAgICAgICAgICBxeGRqOiAnJywKICAgICAgICAgIGpzeXk6ICcnLAogICAgICAgIH0sCiAgICAgICAgZmllbGRMaXN0OiBbCiAgICAgICAgICB7IGxhYmVsOiAn6K6+5aSH6YOo5Lu2JywgdHlwZTogJ2lucHV0JywgdmFsdWU6ICdzYmJqJyB9LAogICAgICAgICAgeyBsYWJlbDogJ+iuvuWkh+mDqOS9jScsIHR5cGU6ICdpbnB1dCcsIHZhbHVlOiAnc2Jidyd9LAogICAgICAgICAgeyBsYWJlbDogJ+makOaCo+aPj+i/sCcsIHR5cGU6ICdpbnB1dCcsIHZhbHVlOiAncXhtcyd9LAogICAgICAgICAgeyBsYWJlbDogJ+makOaCo+etiee6pycsIHR5cGU6ICdzZWxlY3QnLCB2YWx1ZTogJ3F4ZGonLCBvcHRpb25zOiBbXX0sCiAgICAgICAgICB7IGxhYmVsOiAn5YiG57G75L6d5o2uJywgdHlwZTogJ2lucHV0JywgdmFsdWU6ICdmbHlqJ30sCiAgICAgICAgICB7IGxhYmVsOiAn5oqA5pyv5Y6f5ZugJywgdHlwZTogJ2lucHV0JywgdmFsdWU6ICdqc3l5J30KICAgICAgICBdCiAgICAgIH0sCiAgICAgIHRhYmxlQW5kUGFnZUluZm86IHsKICAgICAgICBwYWdlcjogewogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMDBdCiAgICAgICAgfSwKICAgICAgICBvcHRpb246IHsKICAgICAgICAgIGNoZWNrQm94OiB0cnVlLAogICAgICAgICAgc2VyaWFsTnVtYmVyOiB0cnVlCiAgICAgICAgfSwKICAgICAgICB0YWJsZURhdGE6IFtdLAogICAgICAgIHRhYmxlSGVhZGVyOiBbCiAgICAgICAgICB7IHByb3A6ICdzYmx4JywgbGFiZWw6ICforr7lpIfnsbvlnosnLCBtaW5XaWR0aDogJzE0MCcgfSwKICAgICAgICAgIHsgcHJvcDogJ3NiYmonLCBsYWJlbDogJ+iuvuWkh+mDqOS7ticsIG1pbldpZHRoOiAnMTgwJyB9LAogICAgICAgICAgeyBwcm9wOiAnc2JidycsIGxhYmVsOiAn6K6+5aSH6YOo5L2NJywgbWluV2lkdGg6ICcxMzAnIH0sCiAgICAgICAgICB7IHByb3A6ICdxeG1zJywgbGFiZWw6ICfpmpDmgqPmj4/ov7AnLCBtaW5XaWR0aDogJzIwMCcgfSwKICAgICAgICAgIHsgcHJvcDogJ2ZseWonLCBsYWJlbDogJ+WIhuexu+S+neaNricsIG1pbldpZHRoOiAnMjIwJyxzaG93UG9wOnRydWV9LAogICAgICAgICAgeyBwcm9wOiAncXhkaicsIGxhYmVsOiAn6ZqQ5oKj562J57qnJywgbWluV2lkdGg6ICc4MCd9LAogICAgICAgICAgeyBwcm9wOiAnanN5eScsIGxhYmVsOiAn5oqA5pyv5Y6f5ZugJywgbWluV2lkdGg6ICcxMjAnIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIHF1ZXJ5UGFyYW1zOnt9LAogICAgICB0cmVlT3B0aW9uczogW10sIC8v57uE57uH5qCRCiAgICAgIHRyZWVOb2RlRGF0YTp7fSwvL+eCueWHu+WQjueahOagkeiKgueCueaVsOaNrgogICAgICBpc1Nob3dEZXRhaWw6ZmFsc2UsCiAgICAgIGlzU2hvd1NiYmo6ZmFsc2UsLy/mlrDlop7lvLnmoYYKICAgICAgaXNTaG93U2JidzpmYWxzZSwKICAgICAgaXNTaG93UXhtczpmYWxzZSwKICAgICAgaXNTaG93Rmx5ajpmYWxzZSwKICAgICAgZmx5akZvcm06e30sLy/ooajljZUKICAgICAgZmx5alJ1bGVzOnsKICAgICAgICBzYmx4Ym06IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICforr7lpIfnsbvlnovkuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnc2VsZWN0JyB9CiAgICAgICAgXSwKICAgICAgICBwYXJlbnRTYmJqOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K6+5aSH6YOo5Lu25LiN6IO95Li656m6JywgdHJpZ2dlcjogJ3NlbGVjdCcgfQogICAgICAgIF0sCiAgICAgICAgcGFyZW50U2JidzogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+iuvuWkh+mDqOS9jeS4jeiDveS4uuepuicsIHRyaWdnZXI6ICdzZWxlY3QnIH0KICAgICAgICBdLAogICAgICAgIHF4ZGo6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfpmpDmgqPnrYnnuqfkuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnc2VsZWN0JyB9CiAgICAgICAgXSwKICAgICAgICBwYXJlbnRReG1zOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6ZqQ5oKj5o+P6L+w5LiN6IO95Li656m6JywgdHJpZ2dlcjogJ3NlbGVjdCcgfQogICAgICAgIF0sCiAgICAgICAgZmx5ajogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+WIhuexu+S+neaNruS4jeiDveS4uuepuicsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgXSwKICAgICAgICBqc3l5OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn5oqA5pyv5Y6f5Zug5LiN6IO95Li656m6JywgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICBdLAogICAgICB9LC8v5qCh6aqM6KeE5YiZCiAgICAgIHF4bXNGb3JtOnt9LC8v6KGo5Y2VCiAgICAgIHF4bXNSdWxlczp7CiAgICAgICAgc2JseGJtOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K6+5aSH57G75Z6L5LiN6IO95Li656m6JywgdHJpZ2dlcjogJ3NlbGVjdCcgfQogICAgICAgIF0sCiAgICAgICAgcGFyZW50U2JiajogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+iuvuWkh+mDqOS7tuS4jeiDveS4uuepuicsIHRyaWdnZXI6ICdzZWxlY3QnIH0KICAgICAgICBdLAogICAgICAgIHBhcmVudFNiYnc6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICforr7lpIfpg6jkvY3kuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnc2VsZWN0JyB9CiAgICAgICAgXSwKICAgICAgICBxeGRqOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6ZqQ5oKj562J57qn5LiN6IO95Li656m6JywgdHJpZ2dlcjogJ3NlbGVjdCcgfQogICAgICAgIF0sCiAgICAgICAgcXhtczogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+makOaCo+aPj+i/sOS4jeiDveS4uuepuicsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgXSwKICAgICAgICBmbHlqOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn5YiG57G75L6d5o2u5LiN6IO95Li656m6JywgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICBdLAogICAgICAgIGpzeXk6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfmioDmnK/ljp/lm6DkuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgIF0sCiAgICAgIH0sLy/moKHpqozop4TliJkKICAgICAgc2Jid0Zvcm06e30sLy/ooajljZUKICAgICAgc2Jid1J1bGVzOnsKICAgICAgICBzYmx4Ym06IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICforr7lpIfnsbvlnovkuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnc2VsZWN0JyB9CiAgICAgICAgXSwKICAgICAgICBwYXJlbnRTYmJqOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K6+5aSH6YOo5Lu25LiN6IO95Li656m6JywgdHJpZ2dlcjogJ3NlbGVjdCcgfQogICAgICAgIF0sCiAgICAgICAgc2JidzogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+iuvuWkh+mDqOS9jeS4jeiDveS4uuepuicsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgXSwKICAgICAgICBxeGRqOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6ZqQ5oKj562J57qn5LiN6IO95Li656m6JywgdHJpZ2dlcjogJ3NlbGVjdCcgfQogICAgICAgIF0sCiAgICAgICAgcXhtczogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+makOaCo+aPj+i/sOS4jeiDveS4uuepuicsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgXSwKICAgICAgICBmbHlqOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn5YiG57G75L6d5o2u5LiN6IO95Li656m6JywgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICBdLAogICAgICAgIGpzeXk6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfmioDmnK/ljp/lm6DkuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgIF0sCiAgICAgIH0sLy/moKHpqozop4TliJkKICAgICAgc2JiakZvcm06e30sLy/ooajljZUKICAgICAgc2JialJ1bGVzOnsKICAgICAgICBzYmx4Ym06IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICforr7lpIfnsbvlnovkuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnc2VsZWN0JyB9CiAgICAgICAgXSwKICAgICAgICBzYmJqOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K6+5aSH6YOo5Lu25LiN6IO95Li656m6JywgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICBdLAogICAgICAgIHNiYnc6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICforr7lpIfpg6jkvY3kuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgIF0sCiAgICAgICAgcXhkajogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+makOaCo+etiee6p+S4jeiDveS4uuepuicsIHRyaWdnZXI6ICdzZWxlY3QnIH0KICAgICAgICBdLAogICAgICAgIHF4bXM6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfpmpDmgqPmj4/ov7DkuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgIF0sCiAgICAgICAgZmx5ajogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+WIhuexu+S+neaNruS4jeiDveS4uuepuicsIHRyaWdnZXI6ICdibHVyJyB9CiAgICAgICAgXSwKICAgICAgICBqc3l5OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn5oqA5pyv5Y6f5Zug5LiN6IO95Li656m6JywgdHJpZ2dlcjogJ2JsdXInIH0KICAgICAgICBdLAogICAgICB9LC8v5qCh6aqM6KeE5YiZCiAgICAgIHNibHhMaXN0OltdLC8v6K6+5aSH57G75Z6L5LiL5ouJ5qGG6YCJ6aG5CiAgICAgIHNiYmpMaXN0OltdLC8v6K6+5aSH6YOo5Lu25LiL5ouJ5qGG6YCJ6aG5CiAgICAgIHNiYndMaXN0OltdLC8v6K6+5aSH6YOo5L2N5LiL5ouJ5qGG6YCJ6aG5CiAgICAgIHF4bXNMaXN0OltdLC8v6ZqQ5oKj5o+P6L+w5LiL5ouJ5qGG6YCJ6aG5CiAgICAgIGZseWpMaXN0OltdLC8v5YiG57G75L6d5o2u5LiL5ouJ5qGG6YCJ6aG5CiAgICAgIHF4ZGpMaXN0OltdLC8v6ZqQ5oKj562J57qn5LiL5ouJ5qGG6YCJ6aG5CiAgICAgIHF4bGI6JzEnLC8v6ZqQ5oKj57G75Yir77yI5Y+Y55S177yJCiAgICAgIGZpbHRlclRleHQ6JycsLy/ov4fmu6QKICAgICAgdmlld0Zvcm06e30sLy/mn6XnnIvooajljZUKICAgICAgbG9hZGluZzogbnVsbCwKICAgIH0KICB9LAogIHdhdGNoOiB7CiAgICBmaWx0ZXJUZXh0KHZhbCkgewogICAgICB0aGlzLiRyZWZzLnRyZWUuZmlsdGVyKHZhbCk7CiAgICB9LAogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMucXVlcnlQYXJhbXMucXhsYiA9IHRoaXMucXhsYjsKICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgdGhpcy5nZXRUcmVlRGF0YSgpOwogICAgLy/orr7lpIfnsbvlnovkuIvmi4nmoYYKICAgIHRoaXMuZ2V0U2JseExpc3QoKTsKICAgIC8v6ZqQ5oKj562J57qn5LiL5ouJ5qGGCiAgICB0aGlzLmdldFF4ZGpMaXN0KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvL+iOt+WPluiuvuWkh+exu+Wei+S4i+aLieahhgogICAgYXN5bmMgZ2V0U2JseExpc3QoKXsKICAgICAgYXdhaXQgZ2V0U2JseExpc3Qoe3F4bGI6dGhpcy5xeGxifSkudGhlbihyZXM9PnsKICAgICAgICB0aGlzLnNibHhMaXN0ID0gcmVzLmRhdGE7CiAgICAgIH0pCiAgICB9LAogICAgLy/ojrflj5borr7lpIfpg6jku7bkuIvmi4nmoYYKICAgIGFzeW5jIGdldFNiYmpMaXN0KHNibHgpewogICAgICBhd2FpdCBnZXRTYmJqTGlzdCh7cXhsYjp0aGlzLnF4bGIsc2JseDpzYmx4fSkudGhlbihyZXM9PnsKICAgICAgICB0aGlzLnNiYmpMaXN0ID0gcmVzLmRhdGE7CiAgICAgIH0pCiAgICB9LAogICAgLy/ojrflj5borr7lpIfpg6jkvY3kuIvmi4nmoYYKICAgIGFzeW5jIGdldFNiYndMaXN0KHNiYmopewogICAgICBhd2FpdCBnZXRTYmJ3TGlzdCh7cXhsYjp0aGlzLnF4bGIsc2JiajpzYmJqfSkudGhlbihyZXM9PnsKICAgICAgICB0aGlzLnNiYndMaXN0ID0gcmVzLmRhdGE7CiAgICAgIH0pCiAgICB9LAogICAgLy/ojrflj5bpmpDmgqPmj4/ov7DkuIvmi4nmoYYKICAgIGFzeW5jIGdldFF4bXNMaXN0KHNiYncpewogICAgICBhd2FpdCBnZXRReG1zTGlzdCh7cXhsYjp0aGlzLnF4bGIsc2JidzpzYmJ3fSkudGhlbihyZXM9PnsKICAgICAgICB0aGlzLnF4bXNMaXN0ID0gcmVzLmRhdGE7CiAgICAgIH0pCiAgICB9LAogICAgLy/ojrflj5bliIbnsbvkvp3mja7kuIvmi4nmoYYKICAgIGFzeW5jIGdldEZseWpMaXN0KHF4bXMpewogICAgICBhd2FpdCBnZXRGbHlqTGlzdCh7cXhsYjp0aGlzLnF4bGIscXhtczpxeG1zfSkudGhlbihyZXM9PnsKICAgICAgICB0aGlzLmZseWpMaXN0ID0gcmVzLmRhdGE7CiAgICAgIH0pCiAgICB9LAogICAgLy/ojrflj5bpmpDmgqPnrYnnuqflrZflhbjmlbDmja4KICAgIGFzeW5jIGdldFF4ZGpMaXN0KCl7Ly/mn6Xor6LpmpDmgqPnrYnnuqflrZflhbgKICAgICAgYXdhaXQgZ2V0RGljdFR5cGVEYXRhKCdzYnF4d2hfcXhkaicpLnRoZW4ocmVzPT57CiAgICAgICAgdGhpcy5xeGRqTGlzdCA9IHJlcy5kYXRhOwogICAgICAgIC8v57uZ562b6YCJ5p2h5Lu26LWL5YC8CiAgICAgICAgdGhpcy5maWx0ZXJJbmZvLmZpZWxkTGlzdC5tYXAoaXRlbSA9PiB7CiAgICAgICAgICBpZiAoaXRlbS52YWx1ZSA9PSAncXhkaicpIHsKICAgICAgICAgICAgaXRlbS5vcHRpb25zID0gdGhpcy5xeGRqTGlzdAogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgIH0pCiAgICB9LAogICAgLy/nvJbovpEKICAgIGFzeW5jIHVwZGF0ZVJvdyhyb3cpewogICAgICAvL+W8gOWQr+mBrue9qeWxggogICAgICB0aGlzLmxvYWRpbmcgPSBMb2FkaW5nLnNlcnZpY2UoewogICAgICAgIGxvY2s6IHRydWUsIC8vbG9ja+eahOS/ruaUueespi0t6buY6K6k5pivZmFsc2UKICAgICAgICB0ZXh0OiAi5Yqg6L295Lit77yM6K+356iN5ZCOIiwgLy/mmL7npLrlnKjliqDovb3lm77moIfkuIvmlrnnmoTliqDovb3mlofmoYgKICAgICAgICBzcGlubmVyOiAiZWwtaWNvbi1sb2FkaW5nIiwgLy/oh6rlrprkuYnliqDovb3lm77moIfnsbvlkI0KICAgICAgICBiYWNrZ3JvdW5kOiAicmdiYSgwLCAwLCAwLCAwLjcpIiwgLy/pga7nvanlsYLpopzoibIKICAgICAgICB0YXJnZXQ6IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoIiNzYnF4RGl2IiksCiAgICAgIH0pOwogICAgICB0aGlzLmZseWpGb3JtID0gey4uLnJvd307CiAgICAgIC8v5LiL5ouJ5qGG5Zue5pi+CiAgICAgIGF3YWl0IHRoaXMuZ2V0U2Jiakxpc3Qocm93LnNibHhibSk7CiAgICAgIGF3YWl0IHRoaXMuZ2V0U2Jid0xpc3Qocm93LnBhcmVudFNiYmopOwogICAgICBhd2FpdCB0aGlzLmdldFF4bXNMaXN0KHJvdy5wYXJlbnRTYmJ3KTsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWwgPSBmYWxzZTsKICAgICAgdGhpcy5hZGRGbHlqID0gZmFsc2U7Ly/kuI3mmK/mlrDlop4KICAgICAgdGhpcy5pc1Nob3dGbHlqID0gdHJ1ZTsKICAgICAgdGhpcy5sb2FkaW5nLmNsb3NlKCk7Ly/lhbPpl63pga7nvanlsYIKICAgIH0sCiAgICAvL+WIoOmZpAogICAgZGVsZXRlUm93KHJvdyl7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+atpOaTjeS9nOWwhuawuOS5heWIoOmZpOivpeaVsOaNriwg5piv5ZCm57un57utPycsICfmj5DnpLonLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICBkZWxldGVGbHlqQnlJZChyb3cpLnRoZW4ocmVzID0+IHsKICAgICAgICAgIGlmKHJlcy5jb2RlID09PSAnMDAwMCcpewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOaIkOWKnyEnCiAgICAgICAgICAgIH0pOwogICAgICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICAgIH1lbHNlewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InLAogICAgICAgICAgICAgIG1lc3NhZ2U6ICfliKDpmaTlpLHotKUhJwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9KQogICAgICB9KQogICAgfSwKICAgIC8v5p+l55yLCiAgICB2aWV3RnVuKHJvdyl7CiAgICAgIHRoaXMudmlld0Zvcm0gPSB7Li4ucm93fTsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWwgPSB0cnVlOwogICAgfSwKICAgIC8v5paw5aKeCiAgICBhZGRGb3JtKGZvcm1UeXBlKXsKICAgICAgLy/lhYjmuIXnqbrkuIvmi4nmoYbnmoTlgLwKICAgICAgdGhpcy5zYmJqTGlzdCA9IFtdOwogICAgICB0aGlzLnNiYndMaXN0ID0gW107CiAgICAgIHRoaXMucXhtc0xpc3QgPSBbXTsKICAgICAgLy/lpoLmnpzmoJHoioLngrnmnInlgLzvvIzliJnluKbov4fmnaUKICAgICAgbGV0IHNibHggPSB0aGlzLnF1ZXJ5UGFyYW1zLnNibHhibT90aGlzLnF1ZXJ5UGFyYW1zLnNibHhibTonJzsKICAgICAgbGV0IHNiYmogPSB0aGlzLnF1ZXJ5UGFyYW1zLnBhcmVudFNiYmo/dGhpcy5xdWVyeVBhcmFtcy5wYXJlbnRTYmJqOicnOwogICAgICBsZXQgc2JidyA9IHRoaXMucXVlcnlQYXJhbXMucGFyZW50U2Jidz90aGlzLnF1ZXJ5UGFyYW1zLnBhcmVudFNiYnc6Jyc7CiAgICAgIHRoaXMuaXNTaG93RGV0YWlsID0gZmFsc2U7CiAgICAgIHN3aXRjaCAoZm9ybVR5cGUpewogICAgICAgIGNhc2UgJ3NiYmonOi8v6K6+5aSH6YOo5Lu2CiAgICAgICAgICB0aGlzLnNiYmpGb3JtID0ge307CiAgICAgICAgICAvLyB0aGlzLiRzZXQodGhpcy5zYmJqRm9ybSwnc2JseGJtJyxzYmx4KTsKICAgICAgICAgIHRoaXMuaXNTaG93U2JiaiA9IHRydWU7CiAgICAgICAgICBicmVhazsKICAgICAgICBjYXNlICdzYmJ3JzovL+iuvuWkh+mDqOS9jQogICAgICAgICAgdGhpcy5zYmJ3Rm9ybSA9IHt9OwogICAgICAgICAgLy8gdGhpcy4kc2V0KHRoaXMuc2Jid0Zvcm0sJ3NibHhibScsc2JseCk7CiAgICAgICAgICAvLyB0aGlzLiRzZXQodGhpcy5zYmJ3Rm9ybSwncGFyZW50U2Jiaicsc2Jiaik7CiAgICAgICAgICB0aGlzLmlzU2hvd1NiYncgPSB0cnVlOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSAncXhtcyc6Ly/pmpDmgqPmj4/ov7AKICAgICAgICAgIHRoaXMucXhtc0Zvcm0gPSB7fTsKICAgICAgICAgIC8vIHRoaXMuJHNldCh0aGlzLnF4bXNGb3JtLCdzYmx4Ym0nLHNibHgpOwogICAgICAgICAgLy8gdGhpcy4kc2V0KHRoaXMucXhtc0Zvcm0sJ3BhcmVudFNiYmonLHNiYmopOwogICAgICAgICAgLy8gdGhpcy4kc2V0KHRoaXMucXhtc0Zvcm0sJ3BhcmVudFNiYncnLHNiYncpOwogICAgICAgICAgdGhpcy5pc1Nob3dReG1zID0gdHJ1ZTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgJ2ZseWonOi8v5YiG57G75L6d5o2uCiAgICAgICAgICB0aGlzLmZseWpGb3JtID0ge307CiAgICAgICAgICAvLyB0aGlzLiRzZXQodGhpcy5mbHlqRm9ybSwnc2JseGJtJyxzYmx4KTsKICAgICAgICAgIC8vIHRoaXMuJHNldCh0aGlzLmZseWpGb3JtLCdwYXJlbnRTYmJqJyxzYmJqKTsKICAgICAgICAgIC8vIHRoaXMuJHNldCh0aGlzLmZseWpGb3JtLCdwYXJlbnRTYmJ3JyxzYmJ3KTsKICAgICAgICAgIHRoaXMuYWRkRmx5aiA9IHRydWU7CiAgICAgICAgICB0aGlzLmlzU2hvd0ZseWogPSB0cnVlOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgZGVmYXVsdDoKICAgICAgICAgIGJyZWFrOwogICAgICB9CiAgICB9LAogICAgLy/kv53lrZgKICAgIGFzeW5jIHNhdmVGb3JtKGZvcm1UeXBlKXsKICAgICAgYXdhaXQgdGhpcy4kcmVmc1tmb3JtVHlwZV0udmFsaWRhdGUoKHZhbGlkKSA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBsZXQgc2F2ZUZvcm0gPSB7Li4ue3F4bGI6dGhpcy5xeGxifX07CiAgICAgICAgICBzd2l0Y2ggKGZvcm1UeXBlKXsKICAgICAgICAgICAgY2FzZSAnZmx5akZvcm0nOi8v5paw5aKe5YiG57G75L6d5o2uCiAgICAgICAgICAgICAgc2F2ZUZvcm0gPSB7Li4uc2F2ZUZvcm0sLi4udGhpcy5mbHlqRm9ybX07CiAgICAgICAgICAgICAgdGhpcy5xeG1zTGlzdC5mb3JFYWNoKGl0ZW09PnsKICAgICAgICAgICAgICAgIGlmKGl0ZW0udmFsdWUgPT09IHNhdmVGb3JtLnBhcmVudFF4bXMpewogICAgICAgICAgICAgICAgICBzYXZlRm9ybS5xeG1zID0gaXRlbS5sYWJlbDsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9KQogICAgICAgICAgICAgIGlmKHRoaXMuYWRkRmx5ail7Ly/mlrDlop4KICAgICAgICAgICAgICAgIGFkZEZseWooc2F2ZUZvcm0pLnRoZW4ocmVzPT57CiAgICAgICAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gJzAwMDAnKSB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmk43kvZzmiJDlip8nKTsKICAgICAgICAgICAgICAgICAgICB0aGlzLmlzU2hvd0ZseWogPSBmYWxzZTsKICAgICAgICAgICAgICAgICAgICB0aGlzLmlzU2hvd1F4bXMgPSBmYWxzZTsKICAgICAgICAgICAgICAgICAgICB0aGlzLmlzU2hvd1NiYncgPSBmYWxzZTsKICAgICAgICAgICAgICAgICAgICB0aGlzLmlzU2hvd1NiYmogPSBmYWxzZTsKICAgICAgICAgICAgICAgICAgICB0aGlzLmlzU2hvd0RldGFpbCA9IGZhbHNlOwogICAgICAgICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICAgICAgICAgIC8v5YWz6Zet5by55qGGCiAgICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5pON5L2c5aSx6LSlJykKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgfWVsc2V7CiAgICAgICAgICAgICAgICB1cGRhdGVGbHlqKHNhdmVGb3JtKS50aGVuKHJlcz0+IHsvL+e8lui+kQogICAgICAgICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09ICcwMDAwJykgewogICAgICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5pON5L2c5oiQ5YqfJyk7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5pc1Nob3dGbHlqID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5pc1Nob3dReG1zID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5pc1Nob3dTYmJ3ID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5pc1Nob3dTYmJqID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5pc1Nob3dEZXRhaWwgPSBmYWxzZTsKICAgICAgICAgICAgICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICAgICAgICAgICAgICAvL+WFs+mXreW8ueahhgogICAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aTjeS9nOWksei0pScpCiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlICdxeG1zRm9ybSc6Ly/mlrDlop7pmpDmgqPmj4/ov7AKICAgICAgICAgICAgICBzYXZlRm9ybSA9IHsuLi5zYXZlRm9ybSwuLi50aGlzLnF4bXNGb3JtfTsKICAgICAgICAgICAgICB0aGlzLnNiYndMaXN0LmZvckVhY2goaXRlbT0+ewogICAgICAgICAgICAgICAgaWYoaXRlbS52YWx1ZSA9PT0gc2F2ZUZvcm0ucGFyZW50U2Jidyl7CiAgICAgICAgICAgICAgICAgIHNhdmVGb3JtLnNiYncgPSBpdGVtLmxhYmVsOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgYWRkUXhtcyhzYXZlRm9ybSkudGhlbihyZXM9PnsKICAgICAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gJzAwMDAnKSB7CiAgICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5pON5L2c5oiQ5YqfJyk7CiAgICAgICAgICAgICAgICAgIHRoaXMuaXNTaG93Rmx5aiA9IGZhbHNlOwogICAgICAgICAgICAgICAgICB0aGlzLmlzU2hvd1F4bXMgPSBmYWxzZTsKICAgICAgICAgICAgICAgICAgdGhpcy5pc1Nob3dTYmJ3ID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgIHRoaXMuaXNTaG93U2JiaiA9IGZhbHNlOwogICAgICAgICAgICAgICAgICB0aGlzLmlzU2hvd0RldGFpbCA9IGZhbHNlOwogICAgICAgICAgICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICAgICAgICAgICAgLy/lhbPpl63lvLnmoYYKICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aTjeS9nOWksei0pScpCiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgJ3NiYndGb3JtJzovL+aWsOWinumakOaCo+aPj+i/sAogICAgICAgICAgICAgIHNhdmVGb3JtID0gey4uLnNhdmVGb3JtLC4uLnRoaXMuc2Jid0Zvcm19OwogICAgICAgICAgICAgIHRoaXMuc2Jiakxpc3QuZm9yRWFjaChpdGVtPT57CiAgICAgICAgICAgICAgICBpZihpdGVtLnZhbHVlID09PSBzYXZlRm9ybS5wYXJlbnRTYmJqKXsKICAgICAgICAgICAgICAgICAgc2F2ZUZvcm0uc2JiaiA9IGl0ZW0ubGFiZWw7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICBhZGRTYmJ3KHNhdmVGb3JtKS50aGVuKHJlcz0+ewogICAgICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAnMDAwMCcpIHsKICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfmk43kvZzmiJDlip8nKTsKICAgICAgICAgICAgICAgICAgdGhpcy5pc1Nob3dGbHlqID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgIHRoaXMuaXNTaG93UXhtcyA9IGZhbHNlOwogICAgICAgICAgICAgICAgICB0aGlzLmlzU2hvd1NiYncgPSBmYWxzZTsKICAgICAgICAgICAgICAgICAgdGhpcy5pc1Nob3dTYmJqID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgIHRoaXMuaXNTaG93RGV0YWlsID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICAgICAgICAvL+WFs+mXreW8ueahhgogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5pON5L2c5aSx6LSlJykKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSAnc2JiakZvcm0nOi8v5paw5aKe6ZqQ5oKj5o+P6L+wCiAgICAgICAgICAgICAgc2F2ZUZvcm0gPSB7Li4uc2F2ZUZvcm0sLi4udGhpcy5zYmJqRm9ybX07CiAgICAgICAgICAgICAgdGhpcy5zYmx4TGlzdC5mb3JFYWNoKGl0ZW09PnsKICAgICAgICAgICAgICAgIGlmKGl0ZW0udmFsdWUgPT09IHNhdmVGb3JtLnNibHhibSl7CiAgICAgICAgICAgICAgICAgIHNhdmVGb3JtLnNibHggPSBpdGVtLmxhYmVsOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgYWRkU2JiaihzYXZlRm9ybSkudGhlbihyZXM9PnsKICAgICAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gJzAwMDAnKSB7CiAgICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5pON5L2c5oiQ5YqfJyk7CiAgICAgICAgICAgICAgICAgIHRoaXMuaXNTaG93Rmx5aiA9IGZhbHNlOwogICAgICAgICAgICAgICAgICB0aGlzLmlzU2hvd1F4bXMgPSBmYWxzZTsKICAgICAgICAgICAgICAgICAgdGhpcy5pc1Nob3dTYmJ3ID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgIHRoaXMuaXNTaG93U2JiaiA9IGZhbHNlOwogICAgICAgICAgICAgICAgICB0aGlzLmlzU2hvd0RldGFpbCA9IGZhbHNlOwogICAgICAgICAgICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICAgICAgICAgICAgLy/lhbPpl63lvLnmoYYKICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aTjeS9nOWksei0pScpCiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGRlZmF1bHQ6CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+agoemqjOacqumAmui/h++8gScpCiAgICAgICAgICByZXR1cm4gZmFsc2UKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgLy/orr7lpIfnsbvlnovkuIvmi4nmoYbkuovku7YKICAgIGFzeW5jIHNibHhDaGFuZ2VGdW4odmFsKXsKICAgICAgdGhpcy5jbGVhckZvcm1GaWVsZCgnc2JseCcpOwogICAgICBhd2FpdCB0aGlzLmdldFNiYmpMaXN0KHZhbCk7CiAgICB9LAogICAgLy/orr7lpIfpg6jku7bkuIvmi4nmoYbkuovku7YKICAgIGFzeW5jIHNiYmpDaGFuZ2VGdW4odmFsKXsKICAgICAgdGhpcy5jbGVhckZvcm1GaWVsZCgnc2JiaicpOwogICAgICBhd2FpdCB0aGlzLmdldFNiYndMaXN0KHZhbCk7CiAgICB9LAogICAgLy/orr7lpIfpg6jkvY3kuIvmi4nmoYbkuovku7YKICAgIGFzeW5jIHNiYndDaGFuZ2VGdW4odmFsKXsKICAgICAgdGhpcy5jbGVhckZvcm1GaWVsZCgnc2JidycpOwogICAgICBhd2FpdCB0aGlzLmdldFF4bXNMaXN0KHZhbCk7CiAgICB9LAogICAgLy/pmpDmgqPmj4/ov7DkuIvmi4nmoYbkuovku7YKICAgIGFzeW5jIHF4bXNDaGFuZ2VGdW4odmFsKXsKICAgICAgdGhpcy5jbGVhckZvcm1GaWVsZCgncXhtcycpOwogICAgICBhd2FpdCB0aGlzLmdldEZseWpMaXN0KHZhbCk7CiAgICB9LAogICAgLy/muIXnqbrlrZfmrrXlgLwKICAgIGNsZWFyRm9ybUZpZWxkKHR5cGUpewogICAgICBzd2l0Y2ggKHR5cGUpewogICAgICAgIGNhc2UgJ3NibHgnOi8v6K6+5aSH57G75Z6LCiAgICAgICAgICB0aGlzLiRzZXQodGhpcy5zYmJqRm9ybSwnc2JiaicsJycpOwogICAgICAgICAgdGhpcy4kc2V0KHRoaXMuc2Jid0Zvcm0sJ3BhcmVudFNiYmonLCcnKTsKICAgICAgICAgIHRoaXMuJHNldCh0aGlzLnF4bXNGb3JtLCdwYXJlbnRTYmJqJywnJyk7CiAgICAgICAgICB0aGlzLiRzZXQodGhpcy5mbHlqRm9ybSwncGFyZW50U2JiaicsJycpOwogICAgICAgICAgdGhpcy5jbGVhckZvcm1GaWVsZCgnc2JiaicpOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSAnc2Jiaic6Ly/orr7lpIfpg6jku7YKICAgICAgICAgIHRoaXMuJHNldCh0aGlzLnNiYndGb3JtLCdzYmJ3JywnJyk7CiAgICAgICAgICB0aGlzLiRzZXQodGhpcy5xeG1zRm9ybSwncGFyZW50U2JidycsJycpOwogICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZmx5akZvcm0sJ3BhcmVudFNiYncnLCcnKTsKICAgICAgICAgIHRoaXMuY2xlYXJGb3JtRmllbGQoJ3NiYncnKTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgJ3NiYncnOi8v6K6+5aSH6YOo5L2NCiAgICAgICAgICB0aGlzLiRzZXQodGhpcy5xeG1zRm9ybSwncXhtcycsJycpOwogICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZmx5akZvcm0sJ3BhcmVudFF4bXMnLCcnKTsKICAgICAgICAgIHRoaXMuY2xlYXJGb3JtRmllbGQoJ3F4bXMnKTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgJ3F4bXMnOi8v6ZqQ5oKj5o+P6L+wCiAgICAgICAgICB0aGlzLiRzZXQodGhpcy5mbHlqRm9ybSwnZmx5aicsJycpOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgICBkZWZhdWx0OgogICAgICAgICAgICBicmVhazsKICAgICAgfQogICAgfSwKICAgIC8v5YWz6ZetCiAgICBjbG9zZUZ1bih0eXBlKXsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWwgPSBmYWxzZTsKICAgICAgc3dpdGNoICh0eXBlKXsKICAgICAgICBjYXNlICdzYmJqJzoKICAgICAgICAgIHRoaXMuaXNTaG93U2JiaiA9IGZhbHNlOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSAnc2Jidyc6CiAgICAgICAgICB0aGlzLmlzU2hvd1NiYncgPSBmYWxzZTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgJ3F4bXMnOgogICAgICAgICAgdGhpcy5pc1Nob3dReG1zID0gZmFsc2U7CiAgICAgICAgICBicmVhazsKICAgICAgICBjYXNlICdmbHlqJzoKICAgICAgICAgIHRoaXMuaXNTaG93Rmx5aiA9IGZhbHNlOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSAndmlldyc6CiAgICAgICAgICB0aGlzLmlzU2hvd0RldGFpbCA9IGZhbHNlOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgZGVmYXVsdDoKICAgICAgICAgIHRoaXMuaXNTaG93U2JiaiA9IGZhbHNlOwogICAgICAgICAgdGhpcy5pc1Nob3dTYmJ3ID0gZmFsc2U7CiAgICAgICAgICB0aGlzLmlzU2hvd1F4bXMgPSBmYWxzZTsKICAgICAgICAgIHRoaXMuaXNTaG93Rmx5aiA9IGZhbHNlOwogICAgICAgICAgdGhpcy5pc1Nob3dEZXRhaWwgPSBmYWxzZTsKICAgICAgICAgIGJyZWFrOwogICAgICB9CiAgICB9LAogICAgLy/ph43nva7mjInpkq4KICAgIGZpbHRlclJlc2V0KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zID0ge3F4bGI6dGhpcy5xeGxifTsvL+mHjee9ruadoeS7tgogICAgfSwKCiAgICAvL+agkeebkeWQrOS6i+S7tgogICAgZmlsdGVyTm9kZSh2YWx1ZSwgZGF0YSkgewogICAgICBpZiAoIXZhbHVlKSByZXR1cm4gdHJ1ZQogICAgICByZXR1cm4gZGF0YS5sYWJlbC5pbmRleE9mKHZhbHVlKSAhPT0gLTEKICAgIH0sCiAgICBnZXRUcmVlRGF0YSgpewogICAgICBnZXRReHNiVHJlZSh7cXhsYjp0aGlzLnF4bGJ9KS50aGVuKHJlcz0+ewogICAgICAgIHRoaXMudHJlZU9wdGlvbnMgPSByZXMuZGF0YTsKICAgICAgfSkKICAgIH0sCiAgICAvL+agkeiKgueCueeCueWHu+S6i+S7tgogICAgaGFuZGxlTm9kZUNsaWNrKG5vZGUpIHsKICAgICAgdGhpcy50cmVlTm9kZURhdGEgPSBub2RlCiAgICAgIGlmIChub2RlLmlkZW50aWZpZXIgPT09ICcxJykgey8v6K6+5aSH57G75Z6LCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zYmx4Ym0gPSBub2RlLmlkOwogICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFyZW50U2JiaiA9ICcnOwogICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFyZW50U2JidyA9ICcnOwogICAgICB9IGVsc2UgaWYgKG5vZGUuaWRlbnRpZmllciA9PT0gJzInKSB7Ly/orr7lpIfpg6jku7YKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnNibHhibSA9ICcnOwogICAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFyZW50U2JiaiA9IG5vZGUuaWQ7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYXJlbnRTYmJ3ID0gJyc7CiAgICAgIH0gZWxzZSBpZiAobm9kZS5pZGVudGlmaWVyID09PSAnMycpIHsvL+iuvuWkh+mDqOS9jQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuc2JseGJtID0gJyc7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYXJlbnRTYmJqID0gJyc7CiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYXJlbnRTYmJ3ID0gbm9kZS5pZDsKICAgICAgfWVsc2UgewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMgPSB7cXhsYjp0aGlzLnF4bGJ9CiAgICAgIH0KICAgICAgdGhpcy5nZXREYXRhKCkKICAgIH0sCiAgICAvL+afpeivouWIl+ihqAogICAgZ2V0RGF0YShwYXJhbXMpIHsKICAgICAgdGhpcy5sb2FkID0gdHJ1ZQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zID0gey4uLnRoaXMucXVlcnlQYXJhbXMsIC4uLnBhcmFtc30KICAgICAgZ2V0UXhMaXN0KHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8udGFibGVEYXRhID0gcmVzLmRhdGEucmVjb3JkcwogICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci50b3RhbCA9IHJlcy5kYXRhLnRvdGFsCiAgICAgICAgdGhpcy5sb2FkID0gZmFsc2UKICAgICAgfSkKICAgIH0sCiAgICAvL+WvvOWHumV4Y2VsCiAgICBleHBvcnRFeGNlbCgpIHsKICAgICAgbGV0IGZpbGVOYW1lID0gIumakOaCo+agh+WHhuW6kyI7CiAgICAgIGxldCBleHBvcnRVcmwgPSAiL2J6cXhGbHlqIjsKICAgICAgLy8gaWYodGhpcy5zZWxlY3REYXRhLmxlbmd0aCA+IDApewogICAgICAvLyAgIC8vIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+35Zyo5bem5L6n5Yu+6YCJ6KaB5a+85Ye655qE5pWw5o2uJykKICAgICAgLy8gICAvLyByZXR1cm4KICAgICAgLy8gICBleHBvcnRFeGNlbChleHBvcnRVcmwsIHRoaXMucXVlcnlQYXJhbXMsIGZpbGVOYW1lKTsKICAgICAgLy8gfQogICAgICBleHBvcnRFeGNlbChleHBvcnRVcmwsIHRoaXMucXVlcnlQYXJhbXMsIGZpbGVOYW1lKTsKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["bdqxwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgj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file": "bdqxwh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl", "sourcesContent": ["<template>\n  <div class=\"app-container\" id=\"sbqxDiv\">\n    <!--左侧树组件-->\n    <el-row :gutter=\"1\">\n      <!--   左侧树   -->\n      <el-col :span=\"4\">\n        <el-card style=\"background:#e0f8ed;padding-top:10px;\" shadow=\"never\">\n          <div>\n            <el-col>\n              <el-form label-width=\"62px\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"快速查询:\" label-width=\"80px\">\n                    <el-input\n                      placeholder=\"输入关键字过滤\"\n                      v-model=\"filterText\"\n                      clearable\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-form>\n            </el-col>\n          </div>\n          <div class=\"text head-container\">\n            <el-col style=\"padding:0;\">\n              <el-tree\n                id=\"tree\"\n                :data=\"treeOptions\"\n                @node-click=\"handleNodeClick\"\n                :highlight-current=\"true\"\n                ref=\"tree\"\n                :filter-node-method=\"filterNode\"\n                node-key=\"id\"\n                :default-checked-keys=\"['0']\"\n                :default-expanded-keys=\"['0']\"\n                accordion\n              />\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :btnHidden=\"false\"\n          :width=\"{ labelWidth: 80, itemWidth: 165 }\"\n          @handleReset=\"filterReset\"\n        />\n        <el-white class=\"button-group1\">\n          <div class=\"button_btn\">\n            <el-button type=\"primary\" icon=\"el-icon-plus\" v-hasPermi=\"['bdqxwh:button:add']\" @click=\"addForm('sbbj')\">新增部件</el-button>\n            <el-button type=\"primary\" icon=\"el-icon-plus\" v-hasPermi=\"['bdqxwh:button:add']\" @click=\"addForm('sbbw')\">新增部位</el-button>\n            <el-button type=\"primary\" icon=\"el-icon-plus\" v-hasPermi=\"['bdqxwh:button:add']\" @click=\"addForm('qxms')\">新增隐患描述</el-button>\n            <el-button type=\"primary\" icon=\"el-icon-plus\" v-hasPermi=\"['bdqxwh:button:add']\" @click=\"addForm('flyj')\">新增分类依据</el-button>\n            <el-button type=\"success\" icon=\"el-icon-download\" @click=\"exportExcel\">导出</el-button>\n          </div>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            height=\"62.2vh\"\n            v-loading=\"load\"\n          >\n            <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\"\n                             width=\"200\" :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button type=\"text\" size=\"small\"\n                           @click=\"updateRow(scope.row)\" v-hasPermi=\"['bdqxwh:button:update']\" title=\"修改\"  class='el-icon-edit'\n                >\n                </el-button>\n                <el-button type=\"text\" size=\"small\"\n                           @click=\"deleteRow(scope.row)\" v-hasPermi=\"['bdqxwh:button:delete']\" title=\"删除\" class='el-icon-delete'\n                >\n                </el-button>\n                <el-button type=\"text\" size=\"small\"\n                           @click=\"viewFun(scope.row)\" title=\"查看\" class='el-icon-view'\n                >\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!--  新增设备部件  -->\n    <el-dialog title=\"新增设备部件\" :visible.sync=\"isShowSbbj\" width=\"58%\" @close=\"closeFun('sbbj')\" v-dialogDrag>\n      <el-form label-width=\"140px\" :rules=\"sbbjRules\" :model=\"sbbjForm\" ref=\"sbbjForm\">\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备隐患</span>\n          </div>\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sblxbm\" label=\"设备类型\">\n                <el-select placeholder=\"设备类型\" v-model=\"sbbjForm.sblxbm\" style=\"width:80%\"\n                           @change=\"sblxChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in sblxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sbbj\" label=\"设备部件\">\n                <el-input v-model=\"sbbjForm.sbbj\" placeholder=\"请输入设备部件\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sbbw\" label=\"设备部位\">\n                <el-input v-model=\"sbbjForm.sbbw\" placeholder=\"请输入设备部位\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\n                <el-select placeholder=\"隐患等级\" v-model=\"sbbjForm.qxdj\" style=\"width:80%\"\n                           filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in qxdjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"隐患描述:\" prop=\"qxms\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"sbbjForm.qxms\" placeholder=\"请输入隐患描述\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"sbbjForm.flyj\" placeholder=\"请输入分类依据\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"sbbjForm.jsyy\" placeholder=\"请输入技术原因\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun('sbbj')\" size=\"small\">取 消</el-button>\n        <el-button type=\"primary\" size=\"small\" @click=\"saveForm('sbbjForm')\">保存</el-button>\n      </div>\n    </el-dialog>\n\n    <!--  新增设备部位  -->\n    <el-dialog title=\"新增设备部位\" :visible.sync=\"isShowSbbw\" width=\"58%\" @close=\"closeFun('sbbw')\" v-dialogDrag>\n      <el-form label-width=\"140px\" :rules=\"sbbwRules\" :model=\"sbbwForm\" ref=\"sbbwForm\">\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备隐患</span>\n          </div>\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sblxbm\" label=\"设备类型\">\n                <el-select placeholder=\"设备类型\" v-model=\"sbbwForm.sblxbm\" style=\"width:80%\"\n                           @change=\"sblxChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in sblxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"parentSbbj\" label=\"设备部件\">\n                <el-select placeholder=\"设备部件\" v-model=\"sbbwForm.parentSbbj\" style=\"width:80%\"\n                           @change=\"sbbjChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in sbbjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sbbw\" label=\"设备部位\">\n                <el-input v-model=\"sbbwForm.sbbw\" placeholder=\"请输入设备部位\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\n                <el-select placeholder=\"隐患等级\" v-model=\"sbbwForm.qxdj\" style=\"width:80%\"\n                           filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in qxdjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"隐患描述:\" prop=\"qxms\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"sbbwForm.qxms\" placeholder=\"请输入隐患描述\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"sbbwForm.flyj\" placeholder=\"请输入分类依据\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"sbbwForm.jsyy\" placeholder=\"请输入技术原因\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun('sbbw')\" size=\"small\">取 消</el-button>\n        <el-button type=\"primary\" size=\"small\" @click=\"saveForm('sbbwForm')\">保存</el-button>\n      </div>\n    </el-dialog>\n\n    <!--  新增隐患描述  -->\n    <el-dialog title=\"新增隐患描述\" :visible.sync=\"isShowQxms\" width=\"58%\" @close=\"closeFun('qxms')\" v-dialogDrag>\n      <el-form label-width=\"140px\" :rules=\"qxmsRules\" :model=\"qxmsForm\" ref=\"qxmsForm\">\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备隐患</span>\n          </div>\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sblxbm\" label=\"设备类型\">\n                <el-select placeholder=\"设备类型\" v-model=\"qxmsForm.sblxbm\" style=\"width:80%\"\n                           @change=\"sblxChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in sblxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"parentSbbj\" label=\"设备部件\">\n                <el-select placeholder=\"设备部件\" v-model=\"qxmsForm.parentSbbj\" style=\"width:80%\"\n                           @change=\"sbbjChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in sbbjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"parentSbbw\" label=\"设备部位\">\n                <el-select placeholder=\"设备部位\" v-model=\"qxmsForm.parentSbbw\" style=\"width:80%\"\n                           @change=\"sbbwChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in sbbwList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\n                <el-select placeholder=\"隐患等级\" v-model=\"qxmsForm.qxdj\" style=\"width:80%\"\n                           filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in qxdjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"隐患描述:\" prop=\"qxms\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"qxmsForm.qxms\" placeholder=\"请输入隐患描述\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"qxmsForm.flyj\" placeholder=\"请输入分类依据\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"qxmsForm.jsyy\" placeholder=\"请输入技术原因\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun('qxms')\" size=\"small\">取 消</el-button>\n        <el-button type=\"primary\" size=\"small\" @click=\"saveForm('qxmsForm')\">保存</el-button>\n      </div>\n    </el-dialog>\n\n    <!--  新增分类依据  -->\n    <el-dialog title=\"新增分类依据\" :visible.sync=\"isShowFlyj\"  width=\"58%\" @close=\"closeFun('flyj')\" v-dialogDrag>\n      <el-form label-width=\"140px\" :rules=\"flyjRules\" :model=\"flyjForm\" ref=\"flyjForm\">\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备隐患</span>\n          </div>\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sblxbm\" label=\"设备类型\">\n                <el-select placeholder=\"设备类型\" v-model=\"flyjForm.sblxbm\" style=\"width:80%\"\n                           @change=\"sblxChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in sblxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"parentSbbj\" label=\"设备部件\">\n                <el-select placeholder=\"设备部件\" v-model=\"flyjForm.parentSbbj\" style=\"width:80%\"\n                           @change=\"sbbjChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in sbbjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"parentSbbw\" label=\"设备部位\">\n                <el-select placeholder=\"设备部位\" v-model=\"flyjForm.parentSbbw\" style=\"width:80%\"\n                           @change=\"sbbwChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in sbbwList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\n                <el-select placeholder=\"隐患等级\" v-model=\"flyjForm.qxdj\" style=\"width:80%\"\n                           filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in qxdjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"隐患描述:\" prop=\"parentQxms\">\n                <el-select placeholder=\"隐患描述\" v-model=\"flyjForm.parentQxms\" style=\"width:80%\"\n                           @change=\"qxmsChangeFun\" filterable clearable\n                >\n                  <el-option\n                    v-for=\"item in qxmsList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"flyjForm.flyj\" placeholder=\"请输入分类依据\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"flyjForm.jsyy\" placeholder=\"请输入技术原因\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun('flyj')\" size=\"small\">取 消</el-button>\n        <el-button v-if=\"addFlyj\" type=\"primary\" size=\"small\" @click=\"saveForm('flyjForm')\">保存</el-button>\n        <el-button v-if=\"!addFlyj\" type=\"primary\" size=\"small\" @click=\"saveForm('flyjForm')\">保存</el-button>\n      </div>\n    </el-dialog>\n\n    <!--  设备隐患查看  -->\n    <el-dialog title=\"设备隐患查看\" :visible.sync=\"isShowDetail\" v-if=\"isShowDetail\"  width=\"58%\" @close=\"closeFun('view')\" v-dialogDrag >\n      <el-form label-width=\"140px\" :model=\"viewForm\" ref=\"viewForm\">\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备隐患</span>\n          </div>\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sblx\" label=\"设备类型\">\n                <el-input v-model=\"viewForm.sblx\" placeholder=\"请输入设备类型\" style=\"width: 92%\"\n                          :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sbbj\" label=\"设备部件\">\n                <el-input v-model=\"viewForm.sbbj\" placeholder=\"请输入设备部件\" style=\"width: 92%\"\n                          :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sbbw\" label=\"设备部位\">\n                <el-input v-model=\"viewForm.sbbw\" placeholder=\"请输入设备部位\" style=\"width: 92%\"\n                          :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\n                <el-input v-model=\"viewForm.qxdj\" placeholder=\"请输入隐患等级\" style=\"width: 92%\"\n                          :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"隐患描述:\" prop=\"qxms\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"viewForm.qxms\" placeholder=\"请输入隐患描述\" style=\"width: 92%\"\n                          :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"viewForm.flyj\" placeholder=\"请输入分类依据\" style=\"width: 92%\"\n                          :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"viewForm.jsyy\" placeholder=\"请输入技术原因\" style=\"width: 92%\"\n                          :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n    </el-dialog>\n  </div>\n\n</template>\n\n<script>\nimport {getQxList,\n  getQxsbTree,\n  getSblxList,\n  getSbbjList,\n  getSbbwList,\n  getQxmsList,\n  getFlyjList,\n  addFlyj,\n  updateFlyj,\n  deleteFlyjById,\n  addQxms,\n  addSbbw,\n  addSbbj,\n} from '@/api/dagangOilfield/bzgl/sbqxwh/sbqxwh'\nimport {getDictTypeData} from '@/api/system/dict/data'\nimport { Loading } from 'element-ui'\nimport {exportExcel} from '@/api/bzgl/ysbzk/ysbzk'\n\nexport default {\n  name: 'sblxwh',\n  data() {\n    return {\n      load:false,\n      addFlyj:false,//是否新增分类依据\n      filterInfo: {\n        data: {\n          sbbj: '',\n          sbbw: '',\n          qxms: '',\n          flyj: '',\n          qxdj: '',\n          jsyy: '',\n        },\n        fieldList: [\n          { label: '设备部件', type: 'input', value: 'sbbj' },\n          { label: '设备部位', type: 'input', value: 'sbbw'},\n          { label: '隐患描述', type: 'input', value: 'qxms'},\n          { label: '隐患等级', type: 'select', value: 'qxdj', options: []},\n          { label: '分类依据', type: 'input', value: 'flyj'},\n          { label: '技术原因', type: 'input', value: 'jsyy'}\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: 'sblx', label: '设备类型', minWidth: '140' },\n          { prop: 'sbbj', label: '设备部件', minWidth: '180' },\n          { prop: 'sbbw', label: '设备部位', minWidth: '130' },\n          { prop: 'qxms', label: '隐患描述', minWidth: '200' },\n          { prop: 'flyj', label: '分类依据', minWidth: '220',showPop:true},\n          { prop: 'qxdj', label: '隐患等级', minWidth: '80'},\n          { prop: 'jsyy', label: '技术原因', minWidth: '120' }\n        ]\n      },\n      queryParams:{},\n      treeOptions: [], //组织树\n      treeNodeData:{},//点击后的树节点数据\n      isShowDetail:false,\n      isShowSbbj:false,//新增弹框\n      isShowSbbw:false,\n      isShowQxms:false,\n      isShowFlyj:false,\n      flyjForm:{},//表单\n      flyjRules:{\n        sblxbm: [\n          { required: true, message: '设备类型不能为空', trigger: 'select' }\n        ],\n        parentSbbj: [\n          { required: true, message: '设备部件不能为空', trigger: 'select' }\n        ],\n        parentSbbw: [\n          { required: true, message: '设备部位不能为空', trigger: 'select' }\n        ],\n        qxdj: [\n          { required: true, message: '隐患等级不能为空', trigger: 'select' }\n        ],\n        parentQxms: [\n          { required: true, message: '隐患描述不能为空', trigger: 'select' }\n        ],\n        flyj: [\n          { required: true, message: '分类依据不能为空', trigger: 'blur' }\n        ],\n        jsyy: [\n          { required: true, message: '技术原因不能为空', trigger: 'blur' }\n        ],\n      },//校验规则\n      qxmsForm:{},//表单\n      qxmsRules:{\n        sblxbm: [\n          { required: true, message: '设备类型不能为空', trigger: 'select' }\n        ],\n        parentSbbj: [\n          { required: true, message: '设备部件不能为空', trigger: 'select' }\n        ],\n        parentSbbw: [\n          { required: true, message: '设备部位不能为空', trigger: 'select' }\n        ],\n        qxdj: [\n          { required: true, message: '隐患等级不能为空', trigger: 'select' }\n        ],\n        qxms: [\n          { required: true, message: '隐患描述不能为空', trigger: 'blur' }\n        ],\n        flyj: [\n          { required: true, message: '分类依据不能为空', trigger: 'blur' }\n        ],\n        jsyy: [\n          { required: true, message: '技术原因不能为空', trigger: 'blur' }\n        ],\n      },//校验规则\n      sbbwForm:{},//表单\n      sbbwRules:{\n        sblxbm: [\n          { required: true, message: '设备类型不能为空', trigger: 'select' }\n        ],\n        parentSbbj: [\n          { required: true, message: '设备部件不能为空', trigger: 'select' }\n        ],\n        sbbw: [\n          { required: true, message: '设备部位不能为空', trigger: 'blur' }\n        ],\n        qxdj: [\n          { required: true, message: '隐患等级不能为空', trigger: 'select' }\n        ],\n        qxms: [\n          { required: true, message: '隐患描述不能为空', trigger: 'blur' }\n        ],\n        flyj: [\n          { required: true, message: '分类依据不能为空', trigger: 'blur' }\n        ],\n        jsyy: [\n          { required: true, message: '技术原因不能为空', trigger: 'blur' }\n        ],\n      },//校验规则\n      sbbjForm:{},//表单\n      sbbjRules:{\n        sblxbm: [\n          { required: true, message: '设备类型不能为空', trigger: 'select' }\n        ],\n        sbbj: [\n          { required: true, message: '设备部件不能为空', trigger: 'blur' }\n        ],\n        sbbw: [\n          { required: true, message: '设备部位不能为空', trigger: 'blur' }\n        ],\n        qxdj: [\n          { required: true, message: '隐患等级不能为空', trigger: 'select' }\n        ],\n        qxms: [\n          { required: true, message: '隐患描述不能为空', trigger: 'blur' }\n        ],\n        flyj: [\n          { required: true, message: '分类依据不能为空', trigger: 'blur' }\n        ],\n        jsyy: [\n          { required: true, message: '技术原因不能为空', trigger: 'blur' }\n        ],\n      },//校验规则\n      sblxList:[],//设备类型下拉框选项\n      sbbjList:[],//设备部件下拉框选项\n      sbbwList:[],//设备部位下拉框选项\n      qxmsList:[],//隐患描述下拉框选项\n      flyjList:[],//分类依据下拉框选项\n      qxdjList:[],//隐患等级下拉框选项\n      qxlb:'1',//隐患类别（变电）\n      filterText:'',//过滤\n      viewForm:{},//查看表单\n      loading: null,\n    }\n  },\n  watch: {\n    filterText(val) {\n      this.$refs.tree.filter(val);\n    },\n  },\n  created() {\n    this.queryParams.qxlb = this.qxlb;\n    this.getData();\n    this.getTreeData();\n    //设备类型下拉框\n    this.getSblxList();\n    //隐患等级下拉框\n    this.getQxdjList();\n  },\n  methods: {\n    //获取设备类型下拉框\n    async getSblxList(){\n      await getSblxList({qxlb:this.qxlb}).then(res=>{\n        this.sblxList = res.data;\n      })\n    },\n    //获取设备部件下拉框\n    async getSbbjList(sblx){\n      await getSbbjList({qxlb:this.qxlb,sblx:sblx}).then(res=>{\n        this.sbbjList = res.data;\n      })\n    },\n    //获取设备部位下拉框\n    async getSbbwList(sbbj){\n      await getSbbwList({qxlb:this.qxlb,sbbj:sbbj}).then(res=>{\n        this.sbbwList = res.data;\n      })\n    },\n    //获取隐患描述下拉框\n    async getQxmsList(sbbw){\n      await getQxmsList({qxlb:this.qxlb,sbbw:sbbw}).then(res=>{\n        this.qxmsList = res.data;\n      })\n    },\n    //获取分类依据下拉框\n    async getFlyjList(qxms){\n      await getFlyjList({qxlb:this.qxlb,qxms:qxms}).then(res=>{\n        this.flyjList = res.data;\n      })\n    },\n    //获取隐患等级字典数据\n    async getQxdjList(){//查询隐患等级字典\n      await getDictTypeData('sbqxwh_qxdj').then(res=>{\n        this.qxdjList = res.data;\n        //给筛选条件赋值\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == 'qxdj') {\n            item.options = this.qxdjList\n          }\n        })\n      })\n    },\n    //编辑\n    async updateRow(row){\n      //开启遮罩层\n      this.loading = Loading.service({\n        lock: true, //lock的修改符--默认是false\n        text: \"加载中，请稍后\", //显示在加载图标下方的加载文案\n        spinner: \"el-icon-loading\", //自定义加载图标类名\n        background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n        target: document.querySelector(\"#sbqxDiv\"),\n      });\n      this.flyjForm = {...row};\n      //下拉框回显\n      await this.getSbbjList(row.sblxbm);\n      await this.getSbbwList(row.parentSbbj);\n      await this.getQxmsList(row.parentSbbw);\n      this.isShowDetail = false;\n      this.addFlyj = false;//不是新增\n      this.isShowFlyj = true;\n      this.loading.close();//关闭遮罩层\n    },\n    //删除\n    deleteRow(row){\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        deleteFlyjById(row).then(res => {\n          if(res.code === '0000'){\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            });\n            this.getData();\n          }else{\n            this.$message({\n              type: 'error',\n              message: '删除失败!'\n            });\n          }\n        })\n      })\n    },\n    //查看\n    viewFun(row){\n      this.viewForm = {...row};\n      this.isShowDetail = true;\n    },\n    //新增\n    addForm(formType){\n      //先清空下拉框的值\n      this.sbbjList = [];\n      this.sbbwList = [];\n      this.qxmsList = [];\n      //如果树节点有值，则带过来\n      let sblx = this.queryParams.sblxbm?this.queryParams.sblxbm:'';\n      let sbbj = this.queryParams.parentSbbj?this.queryParams.parentSbbj:'';\n      let sbbw = this.queryParams.parentSbbw?this.queryParams.parentSbbw:'';\n      this.isShowDetail = false;\n      switch (formType){\n        case 'sbbj'://设备部件\n          this.sbbjForm = {};\n          // this.$set(this.sbbjForm,'sblxbm',sblx);\n          this.isShowSbbj = true;\n          break;\n        case 'sbbw'://设备部位\n          this.sbbwForm = {};\n          // this.$set(this.sbbwForm,'sblxbm',sblx);\n          // this.$set(this.sbbwForm,'parentSbbj',sbbj);\n          this.isShowSbbw = true;\n          break;\n        case 'qxms'://隐患描述\n          this.qxmsForm = {};\n          // this.$set(this.qxmsForm,'sblxbm',sblx);\n          // this.$set(this.qxmsForm,'parentSbbj',sbbj);\n          // this.$set(this.qxmsForm,'parentSbbw',sbbw);\n          this.isShowQxms = true;\n          break;\n        case 'flyj'://分类依据\n          this.flyjForm = {};\n          // this.$set(this.flyjForm,'sblxbm',sblx);\n          // this.$set(this.flyjForm,'parentSbbj',sbbj);\n          // this.$set(this.flyjForm,'parentSbbw',sbbw);\n          this.addFlyj = true;\n          this.isShowFlyj = true;\n          break;\n        default:\n          break;\n      }\n    },\n    //保存\n    async saveForm(formType){\n      await this.$refs[formType].validate((valid) => {\n        if (valid) {\n          let saveForm = {...{qxlb:this.qxlb}};\n          switch (formType){\n            case 'flyjForm'://新增分类依据\n              saveForm = {...saveForm,...this.flyjForm};\n              this.qxmsList.forEach(item=>{\n                if(item.value === saveForm.parentQxms){\n                  saveForm.qxms = item.label;\n                }\n              })\n              if(this.addFlyj){//新增\n                addFlyj(saveForm).then(res=>{\n                  if (res.code === '0000') {\n                    this.$message.success('操作成功');\n                    this.isShowFlyj = false;\n                    this.isShowQxms = false;\n                    this.isShowSbbw = false;\n                    this.isShowSbbj = false;\n                    this.isShowDetail = false;\n                    this.getData();\n                    //关闭弹框\n                  } else {\n                    this.$message.error('操作失败')\n                  }\n                });\n              }else{\n                updateFlyj(saveForm).then(res=> {//编辑\n                  if (res.code === '0000') {\n                    this.$message.success('操作成功');\n                    this.isShowFlyj = false;\n                    this.isShowQxms = false;\n                    this.isShowSbbw = false;\n                    this.isShowSbbj = false;\n                    this.isShowDetail = false;\n                    this.getData();\n                    //关闭弹框\n                  } else {\n                    this.$message.error('操作失败')\n                  }\n                })\n              }\n              break;\n            case 'qxmsForm'://新增隐患描述\n              saveForm = {...saveForm,...this.qxmsForm};\n              this.sbbwList.forEach(item=>{\n                if(item.value === saveForm.parentSbbw){\n                  saveForm.sbbw = item.label;\n                }\n              })\n              addQxms(saveForm).then(res=>{\n                if (res.code === '0000') {\n                  this.$message.success('操作成功');\n                  this.isShowFlyj = false;\n                  this.isShowQxms = false;\n                  this.isShowSbbw = false;\n                  this.isShowSbbj = false;\n                  this.isShowDetail = false;\n                  this.getData();\n                  //关闭弹框\n                } else {\n                  this.$message.error('操作失败')\n                }\n              });\n              break;\n            case 'sbbwForm'://新增隐患描述\n              saveForm = {...saveForm,...this.sbbwForm};\n              this.sbbjList.forEach(item=>{\n                if(item.value === saveForm.parentSbbj){\n                  saveForm.sbbj = item.label;\n                }\n              })\n              addSbbw(saveForm).then(res=>{\n                if (res.code === '0000') {\n                  this.$message.success('操作成功');\n                  this.isShowFlyj = false;\n                  this.isShowQxms = false;\n                  this.isShowSbbw = false;\n                  this.isShowSbbj = false;\n                  this.isShowDetail = false;\n                  this.getData();\n                  //关闭弹框\n                } else {\n                  this.$message.error('操作失败')\n                }\n              });\n              break;\n            case 'sbbjForm'://新增隐患描述\n              saveForm = {...saveForm,...this.sbbjForm};\n              this.sblxList.forEach(item=>{\n                if(item.value === saveForm.sblxbm){\n                  saveForm.sblx = item.label;\n                }\n              })\n              addSbbj(saveForm).then(res=>{\n                if (res.code === '0000') {\n                  this.$message.success('操作成功');\n                  this.isShowFlyj = false;\n                  this.isShowQxms = false;\n                  this.isShowSbbw = false;\n                  this.isShowSbbj = false;\n                  this.isShowDetail = false;\n                  this.getData();\n                  //关闭弹框\n                } else {\n                  this.$message.error('操作失败')\n                }\n              });\n              break;\n            default:\n              break;\n          }\n        } else {\n          this.$message.error('校验未通过！')\n          return false\n        }\n      })\n    },\n    //设备类型下拉框事件\n    async sblxChangeFun(val){\n      this.clearFormField('sblx');\n      await this.getSbbjList(val);\n    },\n    //设备部件下拉框事件\n    async sbbjChangeFun(val){\n      this.clearFormField('sbbj');\n      await this.getSbbwList(val);\n    },\n    //设备部位下拉框事件\n    async sbbwChangeFun(val){\n      this.clearFormField('sbbw');\n      await this.getQxmsList(val);\n    },\n    //隐患描述下拉框事件\n    async qxmsChangeFun(val){\n      this.clearFormField('qxms');\n      await this.getFlyjList(val);\n    },\n    //清空字段值\n    clearFormField(type){\n      switch (type){\n        case 'sblx'://设备类型\n          this.$set(this.sbbjForm,'sbbj','');\n          this.$set(this.sbbwForm,'parentSbbj','');\n          this.$set(this.qxmsForm,'parentSbbj','');\n          this.$set(this.flyjForm,'parentSbbj','');\n          this.clearFormField('sbbj');\n          break;\n        case 'sbbj'://设备部件\n          this.$set(this.sbbwForm,'sbbw','');\n          this.$set(this.qxmsForm,'parentSbbw','');\n          this.$set(this.flyjForm,'parentSbbw','');\n          this.clearFormField('sbbw');\n          break;\n        case 'sbbw'://设备部位\n          this.$set(this.qxmsForm,'qxms','');\n          this.$set(this.flyjForm,'parentQxms','');\n          this.clearFormField('qxms');\n          break;\n        case 'qxms'://隐患描述\n          this.$set(this.flyjForm,'flyj','');\n          break;\n          default:\n            break;\n      }\n    },\n    //关闭\n    closeFun(type){\n      this.isShowDetail = false;\n      switch (type){\n        case 'sbbj':\n          this.isShowSbbj = false;\n          break;\n        case 'sbbw':\n          this.isShowSbbw = false;\n          break;\n        case 'qxms':\n          this.isShowQxms = false;\n          break;\n        case 'flyj':\n          this.isShowFlyj = false;\n          break;\n        case 'view':\n          this.isShowDetail = false;\n          break;\n        default:\n          this.isShowSbbj = false;\n          this.isShowSbbw = false;\n          this.isShowQxms = false;\n          this.isShowFlyj = false;\n          this.isShowDetail = false;\n          break;\n      }\n    },\n    //重置按钮\n    filterReset() {\n      this.queryParams = {qxlb:this.qxlb};//重置条件\n    },\n\n    //树监听事件\n    filterNode(value, data) {\n      if (!value) return true\n      return data.label.indexOf(value) !== -1\n    },\n    getTreeData(){\n      getQxsbTree({qxlb:this.qxlb}).then(res=>{\n        this.treeOptions = res.data;\n      })\n    },\n    //树节点点击事件\n    handleNodeClick(node) {\n      this.treeNodeData = node\n      if (node.identifier === '1') {//设备类型\n        this.queryParams.sblxbm = node.id;\n        this.queryParams.parentSbbj = '';\n        this.queryParams.parentSbbw = '';\n      } else if (node.identifier === '2') {//设备部件\n        this.queryParams.sblxbm = '';\n        this.queryParams.parentSbbj = node.id;\n        this.queryParams.parentSbbw = '';\n      } else if (node.identifier === '3') {//设备部位\n        this.queryParams.sblxbm = '';\n        this.queryParams.parentSbbj = '';\n        this.queryParams.parentSbbw = node.id;\n      }else {\n        this.queryParams = {qxlb:this.qxlb}\n      }\n      this.getData()\n    },\n    //查询列表\n    getData(params) {\n      this.load = true\n      this.queryParams = {...this.queryParams, ...params}\n      getQxList(this.queryParams).then(res => {\n        this.tableAndPageInfo.tableData = res.data.records\n        this.tableAndPageInfo.pager.total = res.data.total\n        this.load = false\n      })\n    },\n    //导出excel\n    exportExcel() {\n      let fileName = \"隐患标准库\";\n      let exportUrl = \"/bzqxFlyj\";\n      // if(this.selectData.length > 0){\n      //   // this.$message.warning('请在左侧勾选要导出的数据')\n      //   // return\n      //   exportExcel(exportUrl, this.queryParams, fileName);\n      // }\n      exportExcel(exportUrl, this.queryParams, fileName);\n    }\n  }\n}\n</script>\n\n<style lang='scss' scoped>\n/* 设置滚动条的样式 */\n::-webkit-scrollbar {\n  width: 12px;\n}\n\n/* 滚动槽 */\n::-webkit-scrollbar-track {\n  //-webkit-box-shadow:inset006pxrgba(0,0,0,0.3);\n  border-radius: 10px;\n}\n\n/* 滚动条滑块 */\n::-webkit-scrollbar-thumb {\n  border-radius: 10px;\n  background: rgba(0, 0, 0, 0.1);\n  //-webkit-box-shadow:gba(0,0,0,0.5);\n}\n\n::-webkit-scrollbar-thumb:window-inactive {\n  background: rgba(0, 0, 0, 0.1);\n}\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  height: 82.6vh;\n  max-height: 82.6vh;\n  overflow: auto;\n}\n/*给左侧数结构header加颜色*/\n.box-card .el-card__header {\n  background: #11ba6d !important;\n}\n.box-card {\n  margin:0;\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.tree {\n  overflow-y: hidden;\n  overflow-x: scroll;\n  width: 80px;\n  height: 500px;\n}\n\n.el-tree {\n  min-width: 100%;\n  display: inline-block !important;\n}\n/deep/ .el-dialog:not(.is-fullscreen) {\n  margin-top: 8vh !important;\n}\n</style>\n<style>\n\n</style>\n\n\n\n\n\n"]}]}