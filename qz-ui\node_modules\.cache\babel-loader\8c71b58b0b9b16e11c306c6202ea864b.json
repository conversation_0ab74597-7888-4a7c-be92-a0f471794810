{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\gfdzcz\\components\\fwzz_fgssh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\gfdzcz\\components\\fwzz_fgssh.vue", "mtime": 1751373004029}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["fwzz_fgssh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAgoBA;;AAMA;;AAEA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAHA;eAKA;AACA,EAAA,IAAA,EAAA,YADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,QAAA,EAAA,iBAAA;AAAA,IAAA,QAAA,EAAA;AAAA,GAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA,MAAA,GAAA,EAAA,EADA;AAEA,MAAA,WAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,IAFA;AAGA;AACA,MAAA,MAAA,EAAA,KAJA;AAKA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,oBAAA,EAAA,UADA;AAEA,QAAA,WAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,YAHA;AAIA,QAAA,SAAA,EAAA,EAJA;AAKA,QAAA,WAAA,EAAA,IALA;AAMA,QAAA,QAAA,EAAA,EANA;AAOA,QAAA,WAAA,EAAA;AAPA,OANA;AAeA;AACA,MAAA,cAAA,EAAA,KAhBA;AAiBA,MAAA,MAAA,EAAA,EAjBA;AAiBA;AACA,MAAA,QAAA,EAAA,EAlBA;AAmBA,MAAA,YAAA,EAAA,KAnBA;AAoBA;AACA,MAAA,cAAA,EAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OArBA;AAsBA,MAAA,QAAA,EAAA,IAtBA;AAuBA,MAAA,eAAA,EAAA,KAvBA;AAwBA,MAAA,gBAAA,EAAA,KAxBA;AAyBA,MAAA,OAAA,EAAA,EAzBA;AA0BA,MAAA,UAAA,EAAA,KA1BA;AA2BA,MAAA,QAAA,EAAA,KA3BA;AA4BA,MAAA,WAAA,EAAA,KA5BA;AA6BA,MAAA,SAAA,EAAA,KA7BA;AA8BA,MAAA,SAAA,EAAA,KA9BA;AA+BA,MAAA,SAAA,EAAA,KA/BA;AAgCA,MAAA,SAAA,EAAA,KAhCA;AAiCA,MAAA,YAAA,EAAA,KAjCA;AAkCA,MAAA,UAAA,EAAA,EAlCA;AAmCA,MAAA,OAAA,EAAA,EAnCA;AAoCA,MAAA,IAAA,EAAA;AACA,QAAA,EAAA,EAAA,CADA;AAEA,QAAA,MAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA,EAHA;AAIA,QAAA,KAAA,EAAA,EAJA;AAKA,QAAA,KAAA,EAAA,EALA;AAMA,QAAA,KAAA,EAAA;AANA,OApCA;AA4CA;AACA,MAAA,GAAA,EAAA,EA7CA;AA8CA;AACA,MAAA,UAAA,EAAA,EA/CA;AAgDA;AACA,MAAA,MAAA,EAAA,IAjDA;AAkDA;AACA,MAAA,QAAA,EAAA,IAnDA;AAoDA;AACA,MAAA,KAAA,EAAA,EArDA;AAsDA,MAAA,MAAA,EAAA,EAtDA;AAuDA,MAAA,aAAA,EAAA,KAvDA;AAwDA,MAAA,UAAA,EAAA,EAxDA;AAyDA,MAAA,gBAAA,EAAA,EAzDA;;AA0DA;;;AAGA,MAAA,WAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,KAAA,EAAA,EADA;AAEA,UAAA,KAAA,EAAA,EAFA;AAGA,UAAA,GAAA,EAAA;AAHA,SADA;AAMA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,KADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,KAHA;AAIA,UAAA,aAAA,EAAA,EAJA;AAKA,UAAA,OAAA,EAAA;AALA,SADA,EAQA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SARA,EASA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,KAAA,EAAA,OAFA;AAGA,UAAA,IAAA,EAAA,MAHA;AAIA,UAAA,QAAA,EAAA,WAJA;AAKA,UAAA,MAAA,EAAA;AALA,SATA,EAgBA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAhBA,EAiBA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,UAFA;AAGA,UAAA,aAAA,EAAA,EAHA;AAIA,UAAA,KAAA,EAAA,QAJA;AAKA,UAAA,SAAA,EAAA,IALA;AAMA,UAAA,OAAA,EAAA,CACA;AAAA,YAAA,KAAA,EAAA,YAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WADA,EAEA;AAAA,YAAA,KAAA,EAAA,YAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAFA,EAGA;AAAA,YAAA,KAAA,EAAA,YAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAHA,EAIA;AAAA,YAAA,KAAA,EAAA,YAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAJA,EAKA;AAAA,YAAA,KAAA,EAAA,KAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WALA;AANA;AAcA;;;;AA/BA;AANA,OA7DA;AAwGA,MAAA,iBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA;AACA;;;;;AAIA;;;;;;;;;;;;;AAVA;AAZA,OAxGA;AA6IA,MAAA,MAAA,EAAA;AACA,QAAA,EAAA,EAAA;AADA,OA7IA;AAgJA,MAAA,OAAA,EAAA,EAhJA,CAgJA;;AAhJA,KAAA;AAkJA,GAtJA;AAuJA,EAAA,OAvJA,qBAuJA;AACA,SAAA,UAAA,GAAA,KAAA,WAAA;AACA,SAAA,gBAAA,GAAA,KAAA,iBAAA,CAFA,CAGA;;AACA,SAAA,OAAA,GAJA,CAKA;AACA;AACA;;AACA,SAAA,UAAA;AACA,GAhKA;AAiKA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,WAFA,uBAEA,GAFA,EAEA;AAAA;;AACA;AACA,UAAA,GAAA,CAAA,KAAA,KAAA,KAAA,IAAA,GAAA,CAAA,KAAA,IAAA,GAAA,CAAA,KAAA,KAAA,EAAA,EAAA;AACA,YAAA,IAAA,GAAA;AACA,UAAA,MAAA,EAAA,GAAA,CAAA;AADA,SAAA;AAGA,4CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,KAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,gBAAA,IAAA,CAAA,KAAA,KAAA,KAAA,EAAA;AACA,qBAAA,IAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;AACA;AACA,WAJA;AAKA,SANA;AAOA;AACA,KAhBA;AAiBA;AACA,IAAA,UAlBA,wBAkBA;AAAA;;AACA,iCAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAAA,QAAA,EAAA;AACA,SAFA;AAGA,QAAA,MAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;;AACA,QAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,IAAA,KAAA,EAAA;AACA,mBAAA,IAAA,CAAA,OAAA,GAAA,MAAA,CAAA,OAAA;AACA;AACA,SAJA;AAKA,OAVA;AAWA,KA9BA;;AA+BA;;;AAGA,IAAA,KAlCA,iBAkCA,GAlCA,EAkCA;AACA,WAAA,MAAA,GAAA,IAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,YAAA,GAAA,KAAA;AACA,KAxCA;AAyCA;AACA,IAAA,aA1CA,2BA0CA;AACA,WAAA,MAAA,GAAA,KAAA;AACA,KA5CA;AA6CA;AACA,IAAA,QA9CA,oBA8CA,IA9CA,EA8CA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,SAAA,CAAA;AAAA,kBAAA,IAAA,EAAA,IAAA;AAAA,kBAAA,IAAA,EAAA,MAAA,CAAA;AAAA,iBAAA,EAAA;AAAA,kBAAA,WAAA,EAAA;AAAA,iBAAA;AACA;;;;;;;;;;;;;;;;;;;;;;AAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsBA,KApEA;AAqEA;AACA,IAAA,aAtEA,2BAsEA;AACA,WAAA,YAAA,GAAA,KAAA;AACA,KAxEA;AAyEA,IAAA,YAzEA,wBAyEA,GAzEA,EAyEA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AADA;AAAA,uBAEA,8BAAA,MAAA,CAAA,WAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,sBAEA,IAFA;AAEA,gBAAA,IAFA,sBAEA,IAFA;AAGA,gBAAA,MAAA,CAAA,QAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,YAAA,GAAA,IAAA;;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,KA9EA;AA+EA;AACA,IAAA,cAhFA,0BAgFA,GAhFA,EAgFA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,WAAA,MAAA,GACA,mFACA,GAAA,CAAA,KADA,GAEA,KAFA,GAGA,IAAA,IAAA,GAAA,OAAA,EAJA;AAKA,KAxFA;AAyFA;AACA,IAAA,UA1FA,sBA0FA,IA1FA,EA0FA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,cAAA,EAAA,IAAA;AADA,+BAEA,IAAA,CAAA,cAFA;AAAA,kDAGA,UAHA,wBAMA,WANA,wBASA,WATA,wBAYA,WAZA,yBAeA,WAfA,yBAkBA,IAlBA;AAAA;;AAAA;AAIA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,GAAA,KAAA;AAJA;;AAAA;AAOA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,GAAA,KAAA;AAPA;;AAAA;AAUA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,GAAA,YAAA;AAVA;;AAAA;AAaA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,GAAA,YAAA;AAbA;;AAAA;AAgBA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,GAAA,YAAA;AAhBA;;AAAA;AAmBA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,GAAA,KAAA;;AAnBA;AAqBA,gBAAA,GArBA,GAqBA,EArBA;;AAsBA,oBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,IAAA,KAAA,EAAA;AACA,kBAAA,GAAA,GAAA;AACA,oBAAA,KAAA,EAAA,IAAA,CAAA,WADA;AAEA,oBAAA,MAAA,EAAA,MAAA,CAAA,IAAA,CAAA,MAFA;AAGA,oBAAA,EAAA,EAAA;AAHA,mBAAA;AAKA,iBANA,MAMA,IAAA,MAAA,CAAA,IAAA,CAAA,MAAA,IAAA,YAAA,EAAA;AACA,kBAAA,GAAA,GAAA;AACA,oBAAA,KAAA,EAAA,IAAA,CAAA,WADA;AAEA,oBAAA,MAAA,EAAA,MAAA,CAAA,IAAA,CAAA,MAFA;AAGA,oBAAA,EAAA,EAAA;AAHA,mBAAA;AAKA,iBANA,MAMA,IAAA,MAAA,CAAA,IAAA,CAAA,MAAA,IAAA,YAAA,EAAA;AACA,kBAAA,GAAA,GAAA;AACA,oBAAA,KAAA,EAAA,IAAA,CAAA,WADA;AAEA,oBAAA,MAAA,EAAA,MAAA,CAAA,IAAA,CAAA,MAFA;AAGA,oBAAA,EAAA,EAAA;AAHA,mBAAA;AAKA,iBANA,MAMA,IAAA,MAAA,CAAA,IAAA,CAAA,MAAA,IAAA,YAAA,EAAA;AACA,kBAAA,GAAA,GAAA;AACA,oBAAA,KAAA,EAAA,IAAA,CAAA,WADA;AAEA,oBAAA,MAAA,EAAA,MAAA,CAAA,IAAA,CAAA,MAFA;AAGA,oBAAA,EAAA,EAAA;AAHA,mBAAA;AAKA,iBANA,MAMA,IAAA,MAAA,CAAA,IAAA,CAAA,MAAA,IAAA,KAAA,EAAA;AACA,kBAAA,GAAA,GAAA;AACA,oBAAA,KAAA,EAAA,IAAA,CAAA,WADA;AAEA,oBAAA,MAAA,EAAA,MAAA,CAAA,IAAA,CAAA,MAFA;AAGA,oBAAA,EAAA,EAAA;AAHA,mBAAA;AAKA;;AACA,gDAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,GAAA;;AACA,sBAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,oBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA,EADA,CAEA;;;AACA,oBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,oBAAA,MAAA,CAAA,OAAA;;AACA,oBAAA,MAAA,CAAA,SAAA;AACA;AACA,iBATA;;AArDA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA+DA,KAzJA;;AA0JA;;;AAGA,IAAA,OA7JA,mBA6JA,MA7JA,EA6JA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,MAAA,CAAA,MAAA,+DAAA,MAAA,CAAA,MAAA,GAAA,MAAA;AACA,gBAAA,KAHA,GAGA,MAAA,CAAA,MAHA;AAAA;AAAA,uBAIA,2BAAA,KAAA,CAJA;;AAAA;AAAA;AAIA,gBAAA,IAJA,wBAIA,IAJA;AAIA,gBAAA,IAJA,wBAIA,IAJA;AAKA,gBAAA,OAAA,CAAA,GAAA,CAAA,cAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,IAAA;;AACA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AAVA;AAAA;;AAAA;AAAA;AAAA;AAYA,gBAAA,OAAA,CAAA,GAAA;;AAZA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA,KA3KA;;AA4KA;;;AAGA,IAAA,MA/KA,oBA+KA;AACA,WAAA,MAAA,GAAA,EAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,KAlLA;;AAmLA;;;AAGA,IAAA,KAtLA,iBAsLA,GAtLA,EAsLA;AACA,WAAA,MAAA,GAAA,IAAA;AACA,WAAA,QAAA,GAAA,IAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,KA3LA;;AA4LA;;;AAGA,IAAA,KA/LA,iBA+LA,GA/LA,EA+LA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,KAAA,IAAA;;AACA,UAAA,KAAA,IAAA,CAAA,MAAA,IAAA,YAAA,EAAA;AACA,aAAA,MAAA,GAAA,YAAA;AACA,aAAA,WAAA,GAAA,IAAA;AACA;AACA,OAJA,MAIA,IAAA,KAAA,IAAA,CAAA,MAAA,IAAA,YAAA,EAAA;AACA,aAAA,MAAA,GAAA,YAAA;AACA,aAAA,SAAA,GAAA,IAAA;AACA;AACA,OAJA,MAIA,IAAA,KAAA,IAAA,CAAA,MAAA,IAAA,YAAA,EAAA;AACA,aAAA,MAAA,GAAA,YAAA;AACA,aAAA,SAAA,GAAA,IAAA;AACA;AACA,OAJA,MAIA,IAAA,KAAA,IAAA,CAAA,MAAA,IAAA,YAAA,EAAA;AACA,aAAA,MAAA,GAAA,YAAA;AACA,aAAA,SAAA,GAAA,IAAA;AACA;AACA,OAJA,MAIA,IACA,KAAA,IAAA,CAAA,KAAA,IAAA,IAAA,IACA,KAAA,IAAA,CAAA,KAAA,IAAA,IADA,IAEA,KAAA,IAAA,CAAA,KAAA,IAAA,IAFA,IAGA,KAAA,IAAA,CAAA,KAAA,IAAA,IAJA,EAKA;AACA,aAAA,aAAA;AACA;AACA;AACA,KA5NA;AA6NA;AACA,IAAA,SA9NA,qBA8NA,IA9NA,EA8NA,MA9NA,EA8NA;AACA,UAAA,GAAA,mCAAA,IAAA,CAAA,IAAA,CAAA;;AACA,UAAA,IAAA,CAAA,IAAA,KAAA,UAAA,EAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,MAAA,CAAA,WAAA,CADA,CACA;;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,aAAA,WAAA,CAAA,IAAA,GAAA,MAAA,CAAA,IAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AACA,aAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,OANA,MAMA,IAAA,IAAA,CAAA,IAAA,KAAA,iBAAA,EAAA;AACA,YAAA,KAAA,IAAA,CAAA,MAAA,IAAA,YAAA,EAAA;AACA,eAAA,cAAA,CAAA,KAAA,GAAA,cAAA;AACA,SAFA,MAEA,IAAA,KAAA,IAAA,CAAA,MAAA,IAAA,YAAA,EAAA;AACA,eAAA,cAAA,CAAA,KAAA,GAAA,cAAA;AACA,SAFA,MAEA,IAAA,KAAA,IAAA,CAAA,MAAA,IAAA,YAAA,EAAA;AACA,eAAA,cAAA,CAAA,KAAA,GAAA,cAAA;AACA,SAFA,MAEA,IAAA,KAAA,IAAA,CAAA,MAAA,IAAA,YAAA,EAAA;AACA,eAAA,cAAA,CAAA,KAAA,GAAA,MAAA;AACA;;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,MAAA,CAAA,WAAA,CAVA,CAUA;;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,iBAAA;AACA,aAAA,WAAA,CAAA,IAAA,GAAA,MAAA,CAAA,IAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AACA,aAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,aAAA,WAAA,CAAA,SAAA,CAAA,aAAA,GAAA,MAAA,CAAA,aAAA;AACA,OAhBA,MAgBA;AACA,aAAA,cAAA,CAAA,KAAA,GAAA,QAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AACA,aAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA;;AACA,WAAA,MAAA,GAAA,IAAA;AACA,KA9PA;;AA+PA;;;AAGA,IAAA,UAlQA,sBAkQA,GAlQA,EAkQA;AACA,WAAA,MAAA,GAAA,MAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,YAAA,GAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,OAAA,GAAA,CAAA;AAAA,QAAA,KAAA,EAAA,GAAA,CAAA,KAAA;AAAA,QAAA,KAAA,EAAA,GAAA,CAAA;AAAA,OAAA,CAAA;AACA,WAAA,OAAA,GAAA,CAAA;AAAA,QAAA,KAAA,EAAA,GAAA,CAAA,KAAA;AAAA,QAAA,KAAA,EAAA,GAAA,CAAA;AAAA,OAAA,CAAA;AACA,KA1QA;;AA2QA;;;AAGA,IAAA,YA9QA,0BA8QA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,MAAA,CAAA,IAAA,CAAA,EAAA,GAAA,CAAA;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,GAAA,KAAA;AAHA;AAAA,uBAIA,gCAAA,MAAA,CAAA,IAAA,CAJA;;AAAA;AAAA;AAIA,gBAAA,IAJA,yBAIA,IAJA;;AAAA,sBAKA,IAAA,KAAA,MALA;AAAA;AAAA;AAAA;;AAMA,gBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AANA;AAAA,uBAOA,MAAA,CAAA,OAAA,EAPA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAUA,gBAAA,OAAA,CAAA,GAAA;;AAVA;AAYA,gBAAA,MAAA,CAAA,QAAA,GAAA,KAAA;;AAZA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA,KA3RA;;AA4RA;;;AAGA,IAAA,aA/RA,2BA+RA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,IAAA,GAAA,KAAA;AAFA;AAAA;AAAA,uBAIA,gCAAA,MAAA,CAAA,IAAA,CAJA;;AAAA;AAAA;AAIA,gBAAA,IAJA,0BAIA,IAJA;AAIA,gBAAA,IAJA,0BAIA,IAJA;;AAKA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,kBAAA,MAAA,CAAA,SAAA,CACA;AAAA,oBAAA,IAAA,EAAA,IAAA;AAAA,oBAAA,IAAA,EAAA;AAAA,mBADA,EAEA;AACA,oBAAA,WAAA,EAAA,MAAA,CAAA,WAAA,CAAA,WADA;AAEA,oBAAA,IAAA,EAAA,MAAA,CAAA,WAAA,CAAA,IAFA;AAGA,oBAAA,aAAA,EAAA;AAHA,mBAFA;AAQA;;AAfA;AAAA;;AAAA;AAAA;AAAA;AAiBA,gBAAA,OAAA,CAAA,GAAA;;AAjBA;AAmBA,gBAAA,MAAA,CAAA,OAAA;;AACA,gBAAA,MAAA,CAAA,WAAA,GAAA,KAAA;;AApBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBA,KApTA;;AAqTA;;;AAGA,IAAA,YAxTA,0BAwTA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,IAAA,GAAA,KAAA;AAFA;AAAA;AAAA,uBAMA,gCAAA,MAAA,CAAA,IAAA,CANA;;AAAA;AAAA;AAMA,gBAAA,IANA,0BAMA,IANA;AAMA,gBAAA,IANA,0BAMA,IANA;;AAOA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,kBAAA,MAAA,CAAA,SAAA,CACA;AAAA,oBAAA,IAAA,EAAA,IAAA;AAAA,oBAAA,IAAA,EAAA;AAAA,mBADA,EAEA;AACA,oBAAA,WAAA,EAAA,MAAA,CAAA,WAAA,CAAA,WADA;AAEA,oBAAA,IAAA,EAAA,MAAA,CAAA,WAAA,CAAA,IAFA;AAGA,oBAAA,aAAA,EAAA;AAHA,mBAFA;AAQA;;AAjBA;AAAA;;AAAA;AAAA;AAAA;AAmBA,gBAAA,OAAA,CAAA,GAAA;;AAnBA;AAqBA,gBAAA,MAAA,CAAA,OAAA;;AACA,gBAAA,MAAA,CAAA,SAAA,GAAA,KAAA;;AAtBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuBA,KA/UA;;AAgVA;;;AAGA,IAAA,aAnVA,2BAmVA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,IAAA,GAAA,KAAA;AAFA;AAAA;AAAA,uBAMA,gCAAA,OAAA,CAAA,IAAA,CANA;;AAAA;AAAA;AAMA,gBAAA,IANA,0BAMA,IANA;AAMA,gBAAA,IANA,0BAMA,IANA;;AAOA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,kBAAA,OAAA,CAAA,SAAA,CACA;AAAA,oBAAA,IAAA,EAAA,IAAA;AAAA,oBAAA,IAAA,EAAA;AAAA,mBADA,EAEA;AACA,oBAAA,WAAA,EAAA,OAAA,CAAA,WAAA,CAAA,WADA;AAEA,oBAAA,IAAA,EAAA,OAAA,CAAA,WAAA,CAAA,IAFA;AAGA,oBAAA,aAAA,EAAA;AAHA,mBAFA;AAQA;;AAjBA;AAAA;;AAAA;AAAA;AAAA;AAmBA,gBAAA,OAAA,CAAA,GAAA;;AAnBA;AAqBA,gBAAA,OAAA,CAAA,OAAA;;AACA,gBAAA,OAAA,CAAA,SAAA,GAAA,KAAA;;AAtBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuBA,KA1WA;;AA2WA;;;AAGA,IAAA,aA9WA,2BA8WA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,KAAA;AACA,gBAAA,OAAA,CAAA,WAAA,CAAA,IAAA,GAAA,KAAA;AAFA;AAAA;AAAA,uBAIA,gCAAA,OAAA,CAAA,IAAA,CAJA;;AAAA;AAAA;AAIA,gBAAA,IAJA,0BAIA,IAJA;AAIA,gBAAA,IAJA,0BAIA,IAJA;;AAAA,sBAKA,IAAA,KAAA,MALA;AAAA;AAAA;AAAA;;AAMA,gBAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,gBAAA,OAAA,CAAA,SAAA,CACA;AAAA,kBAAA,IAAA,EAAA,IAAA;AAAA,kBAAA,IAAA,EAAA;AAAA,iBADA,EAEA;AACA,kBAAA,WAAA,EAAA,OAAA,CAAA,WAAA,CAAA,WADA;AAEA,kBAAA,IAAA,EAAA,OAAA,CAAA,WAAA,CAAA,IAFA;AAGA,kBAAA,aAAA,EAAA;AAHA,iBAFA;;AAPA;AAAA,uBAeA,OAAA,CAAA,OAAA,EAfA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAkBA,gBAAA,OAAA,CAAA,GAAA;;AAlBA;AAoBA,gBAAA,OAAA,CAAA,SAAA,GAAA,KAAA;;AApBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBA,KAnYA;;AAoYA;;;AAGA,IAAA,aAvYA,2BAuYA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,QAAA,CAAA,aAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAKA,IALA,CAKA,YAAA;AACA,kBAAA,OAAA,CAAA,IAAA,CAAA,EAAA,GAAA,CAAA;AACA,kBAAA,OAAA,CAAA,IAAA,CAAA,MAAA,GAAA,KAAA;AACA,kDAAA,OAAA,CAAA,IAAA,EAAA,IAAA,CAAA,gBAAA;AAAA,wBAAA,IAAA,QAAA,IAAA;;AACA,wBAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,OAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,SADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA,EADA,CAKA;;;AACA,sBAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,sBAAA,OAAA,CAAA,OAAA;AACA,qBARA,MAQA;AACA,sBAAA,OAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,OADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;AAIA;AACA,mBAfA;AAgBA,iBAxBA,EAyBA,KAzBA,CAyBA,YAAA;AACA,kBAAA,OAAA,CAAA,QAAA,CAAA;AACA,oBAAA,IAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;AAIA,iBA9BA;AA+BA;;;;;;;;;;;;;;AAhCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA4CA,KAnbA;;AAqbA;;;AAGA,IAAA,YAxbA,wBAwbA,GAxbA,EAwbA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAKA,IALA,CAKA,YAAA;AACA;AACA,4CAAA,IAAA,CAAA,SAAA,CAAA,GAAA,CAAA,KAAA,CAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,wBAAA,IAAA,SAAA,IAAA;;AACA,wBAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,OAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,SADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;;AAIA,sBAAA,OAAA,CAAA,OAAA;AACA,qBANA,MAMA;AACA,sBAAA,OAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,OADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;AAIA;AACA,mBAbA;AAcA,iBArBA,EAsBA,KAtBA,CAsBA,YAAA;AACA,kBAAA,OAAA,CAAA,QAAA,CAAA;AACA,oBAAA,IAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;AAIA,iBA3BA;;AADA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA6BA,KArdA;AAsdA,IAAA,YAtdA,0BAsdA,CAAA,CAtdA;AAudA;AACA,IAAA,UAxdA,wBAwdA;AACA,UAAA,CAAA,KAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,YAAA;AACA;AACA;;AACA,UAAA;AACA,YAAA,QAAA,GAAA,cAAA;AACA,YAAA,SAAA,GAAA,gBAAA;AACA,YAAA,MAAA,GAAA;AACA,UAAA,IAAA,EAAA,KAAA,UADA;AAEA,UAAA,GAAA,EAAA;AAFA,SAAA;AAIA,gCAAA,MAAA,EAAA,QAAA;AACA,OARA,CAQA,OAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,OAAA;AACA;AACA,KAxeA;AAyeA,IAAA,WAzeA,yBAyeA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,WAAA,WAAA,GAAA,KAAA;AACA,WAAA,QAAA,GAAA,KAAA;AACA,WAAA,SAAA,GAAA,KAAA;AACA,WAAA,SAAA,GAAA,KAAA;AACA,WAAA,SAAA,GAAA,KAAA;AACA,WAAA,SAAA,GAAA,KAAA;AACA,KAjfA;AAkfA,IAAA,gBAlfA,8BAkfA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,OAAA,CAAA,IAAA,CAAA,MAAA,GAAA,KAAA;AACA,gBAAA,OAAA,CAAA,IAAA,CAAA,EAAA,GAAA,CAAA;AAHA;AAAA,uBAIA,gCAAA,OAAA,CAAA,IAAA,CAJA;;AAAA;AAAA;AAIA,gBAAA,IAJA,0BAIA,IAJA;AAIA,gBAAA,IAJA,0BAIA,IAJA;;AAKA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA;;AAPA;AAAA;;AAAA;AAAA;AAAA;AASA,gBAAA,OAAA,CAAA,GAAA;;AATA;AAWA,gBAAA,OAAA,CAAA,OAAA;;AACA,gBAAA,OAAA,CAAA,aAAA,GAAA,KAAA;;AAZA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA,KA/fA;;AAggBA;;;;AAIA,IAAA,qBApgBA,iCAogBA,SApgBA,EAogBA;AACA,WAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,KAAA;AAAA,OAAA,CAAA;AACA,WAAA,MAAA,GAAA,SAAA,CAAA,MAAA,KAAA,CAAA;AACA,WAAA,QAAA,GAAA,CAAA,SAAA,CAAA,MAAA;AACA,WAAA,UAAA,GAAA,SAAA;AACA,KAzgBA;AA0gBA,IAAA,WA1gBA,yBA0gBA;AACA,WAAA,UAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,KAAA,UAAA,EAAA;AACA,UAAA,IAAA,CAAA,aAAA,GAAA,EAAA;AACA;AACA,OAJA;AAKA,WAAA,MAAA,GAAA;AACA,QAAA,EAAA,EAAA;AADA,OAAA;AAGA,KAnhBA;;AAohBA;;;AAGA,IAAA,gBAvhBA,8BAuhBA;AAAA;;AACA,0CAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;;AACA,QAAA,OAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,IAAA,KAAA,EAAA;AACA,mBAAA,IAAA,CAAA,OAAA,GAAA,OAAA,CAAA,OAAA;AACA;AACA,SAJA;AAKA,OAPA;AAQA;AAhiBA;AAjKA,C", "sourcesContent": ["<template>\n  <div>\n    <!--搜索条件-->\n    <el-filter\n      ref=\"filter1\"\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n      @handleReset=\"filterReset\"\n      @handleEvent=\"handleEvent\"\n    />\n\n    <!--<el-white class=\"button-group\">\n      <el-button\n        v-show=\"selectNode==='accident' || selectNode==='operations' || selectNode==='groundingFailureLogging' || selectNode==='tripFailureLogging' || selectNode==='DeviceUnlocks' || selectNode==='OnloadPressure'\"\n        type=\"primary\" icon=\"el-icon-plus\" @click=\"addRow\">新增\n      </el-button>-->\n    <el-white class=\"button-group\">\n      <div style=\"height: 50px\">\n        <el-button type=\"success\" icon=\"el-icon-download\" @click=\"exportWord\"\n          >导出</el-button\n        >\n      </div>\n      <div>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"handleSelectionChange\"\n          height=\"58vh\"\n        >\n          <el-table-column\n            slot=\"table_eight\"\n            align=\"center\"\n            fixed=\"right\"\n            style=\"display: block\"\n            label=\"操作\"\n            width=\"160\"\n            :resizable=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                @click=\"getDetails(scope.row)\"\n                title=\"详情\"\n                class=\"el-icon-view\"\n              ></el-button>\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                v-show=\"\n                  scope.row.createBy == currentUser ||\n                    'admin' === $store.getters.name\n                \"\n                @click=\"handleDelete(scope.row)\"\n                class=\"el-icon-delete\"\n              ></el-button>\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                v-show=\"scope.row.status != '待办结'\"\n                @click=\"getTg(scope.row)\"\n                title=\"通过\"\n                class=\"el-icon-circle-check\"\n              ></el-button>\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                v-show=\"\n                  scope.row.status == '待办结' &&\n                    scope.row.createBy == currentUser\n                \"\n                @click=\"getBj(scope.row)\"\n                title=\"办结\"\n                class=\"el-icon-s-promotion\"\n              ></el-button>\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                @click=\"showTimeLine(scope.row)\"\n                title=\"流程查看\"\n                class=\"el-icon-lcck commonIcon\"\n              ></el-button>\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                v-show=\"scope.row.status != '待办结'\"\n                @click=\"showProcessImg(scope.row)\"\n                title=\"流程图\"\n                class=\"el-icon-lct commonIcon\"\n              ></el-button>\n            </template>\n          </el-table-column>\n        </comp-table>\n      </div>\n    </el-white>\n\n    <!--光伏分公司审批弹框-->\n    <el-dialog\n      :title=\"titles\"\n      :visible.sync=\"isShowBdfgssh\"\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"130px\" ref=\"form\" :model=\"form\">\n        <!--防误装置解锁工具使用审批-->\n        <el-row>\n          <el-col :span=\"8\">\n            <el-form-item label=\"状态\" prop=\"status\">\n              <el-input\n                style=\"width:100%\"\n                :disabled=\"true\"\n                v-model=\"form.status\"\n              >\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"分公司\" prop=\"fgs\">\n              <el-select\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.fgs\"\n                placeholder=\"请选择分公司\"\n                @change=\"fgsChangeFun\"\n              >\n                <el-option\n                  v-for=\"item in fgsList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"光伏站\" prop=\"bdz\">\n              <el-select\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.bdz\"\n                placeholder=\"请选择光伏站\"\n              >\n                <el-option\n                  v-for=\"item in bdzList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"时间\" prop=\"sj\">\n              <el-date-picker\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.sj\"\n                type=\"datetime\"\n                placeholder=\"选择日期时间\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"设备名称\" prop=\"sbmc\">\n              <el-select\n                filterable\n                allow-create\n                default-first-option\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.sbmc\"\n                placeholder=\"请选择或输入设备\"\n              >\n                <el-option value=\"变压器\" label=\"变压器\"></el-option>\n                <el-option value=\"隔离开关\" label=\"隔离开关\"></el-option>\n                <el-option value=\"隔离刀闸\" label=\"隔离倒闸\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"工作任务\" prop=\"gzrw\">\n              <el-input\n                style=\"width:100%\"\n                type=\"textarea\"\n                :rows=\"2\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.gzrw\"\n                placeholder=\"请输入内容\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"使用原因\" prop=\"syyy\">\n              <el-input\n                style=\"width:100%\"\n                type=\"textarea\"\n                :rows=\"2\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.syyy\"\n                placeholder=\"请输入内容\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"记录人\" prop=\"jlr\">\n              <el-select\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.jlr\"\n                clearable\n                placeholder=\"请选择记录人\"\n              >\n                <el-option\n                  v-for=\"item in jlrList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"记录时间\" prop=\"jlsj\">\n              <el-date-picker\n                v-model=\"form.jlsj\"\n                :disabled=\"isDisabled\"\n                type=\"datetime\"\n                style=\"width:100%\"\n                placeholder=\"选择日期时间\"\n                default-time=\"12:00:00\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"分公司五防专责\" prop=\"wfzz1\">\n              <el-input\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.wfzz1\"\n                placeholder=\"请输入内容\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"审核意见\" prop=\"shyj1\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"2\"\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.shyj1\"\n                placeholder=\"请输入审核意见\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"分公司主管领导\" prop=\"zgld1\">\n              <el-input\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.zgld1\"\n                placeholder=\"请输入内容\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"审核意见\" prop=\"shyj2\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"2\"\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.shyj2\"\n                placeholder=\"请输入审核意见\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"生产科五防专责\" prop=\"wfzz2\">\n              <el-select\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.wfzz2\"\n                placeholder=\"请输入内容\"\n              >\n                <el-option label=\"王涛\" value=\"王涛\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"审核意见\" prop=\"shyj3\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"2\"\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.shyj3\"\n                placeholder=\"请输入审核意见\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"生产科主管领导\" prop=\"zgld2\">\n              <el-select\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.zgld2\"\n                placeholder=\"请输入内容\"\n              >\n                <el-option label=\"郑双健\" value=\"郑双健\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"审核意见\" prop=\"shyj4\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"2\"\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.shyj4\"\n                placeholder=\"请输入审核意见\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"监护人\" prop=\"jhr\">\n              <el-input\n                style=\"width:100%\"\n                :disabled=\"isDisabledBj\"\n                v-model=\"form.jhr\"\n                placeholder=\"请输入内容\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"操作人\" prop=\"czr\">\n              <el-input\n                style=\"width:100%\"\n                :disabled=\"isDisabledBj\"\n                v-model=\"form.czr\"\n                placeholder=\"请输入内容\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"操作解锁/事故解锁\" prop=\"czsgjs\">\n              <el-select\n                style=\"width:100%\"\n                :disabled=\"isDisabledBj\"\n                v-model=\"form.czsgjs\"\n                placeholder=\"请输入内容\"\n              >\n                <el-option label=\"操作解锁\" value=\"操作解锁\"></el-option>\n                <el-option label=\"事故解锁\" value=\"事故解锁\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"handleClose\">取 消</el-button>\n        <el-button\n          v-show=\"titles == '办结'\"\n          type=\"primary\"\n          @click=\"submitFormFwzzjs\"\n          >确 定\n        </el-button>\n        <el-button\n          v-show=\"titles != '办结'\"\n          type=\"primary\"\n          @click=\"handleTh('rollback')\"\n          >回 退</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <!--光伏分公司审批 回退 弹框-->\n    <el-dialog\n      :title=\"titles\"\n      :visible.sync=\"isShowHt\"\n      width=\"40%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"100px\" ref=\"form\" :model=\"form\">\n        <!--防误装置解锁工具使用审批-->\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"意见：\" prop=\"bz\">\n              <el-input\n                style=\"width:100%\"\n                type=\"textarea\"\n                :rows=\"2\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.bz\"\n                placeholder=\"请输入审核意见\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"handleClose\">取 消</el-button>\n        <el-button v-if=\"titles == '回退'\" type=\"primary\" @click=\"submitFormHt\"\n          >回 退\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!--光伏分公司审批 通过 分公司五防专责 弹框-->\n    <el-dialog\n      :title=\"titles\"\n      :visible.sync=\"isShowFgswf\"\n      width=\"30%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"130px\" ref=\"form\" :model=\"form\">\n        <!--防误装置解锁工具使用审批-->\n        <div>\n          <el-row>\n            <el-col :span=\"24\">\n              <el-form-item label=\"分公司五防专责：\" prop=\"wfzz1\">\n                <el-input\n                  style=\"width:100%\"\n                  :disabled=\"isDisabled\"\n                  v-model=\"form.wfzz1\"\n                  placeholder=\"请输入分公司五防专责人员\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <el-col :span=\"24\">\n              <el-form-item label=\"审核意见：\" prop=\"shyj1\">\n                <el-input\n                  type=\"textarea\"\n                  :rows=\"2\"\n                  style=\"width:100%\"\n                  :disabled=\"isDisabled\"\n                  v-model=\"form.shyj1\"\n                  placeholder=\"请输入审核意见\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <div align=\"right\" slot=\"footer\">\n            <el-button @click=\"handleClose\">取 消</el-button>\n            <el-button\n              v-if=\"titles == '待分公司五防专责审核'\"\n              type=\"primary\"\n              @click=\"submitFormSh1\"\n              >提 交\n            </el-button>\n          </div>\n        </div>\n      </el-form>\n    </el-dialog>\n\n    <!--光伏分公司审批 通过 分公司主管领导 弹框-->\n    <el-dialog\n      :title=\"titles\"\n      :visible.sync=\"isShowTg1\"\n      width=\"30%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"130px\" ref=\"form\" :model=\"form\">\n        <!--防误装置解锁工具使用审批-->\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"分公司主管领导：\" prop=\"zgld1\">\n              <el-input\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.zgld1\"\n                placeholder=\"请输入分公司主管领导\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"审核意见：\" prop=\"shyj2\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"2\"\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.shyj2\"\n                placeholder=\"请输入审核意见\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"handleClose\">取 消</el-button>\n        <el-button\n          v-if=\"titles == '待分公司主管领导审核'\"\n          type=\"primary\"\n          @click=\"submitFormTg\"\n          >提 交\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!--光伏分公司审批 通过 生产科五防专责 弹框-->\n    <el-dialog\n      :title=\"titles\"\n      :visible.sync=\"isShowTg2\"\n      width=\"30%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"130px\" ref=\"form\" :model=\"form\">\n        <!--防误装置解锁工具使用审批-->\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"生产科五防专责：\" prop=\"wfzz2\">\n              <el-input\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.wfzz2\"\n                placeholder=\"请输入生产科五防专责人员\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"审核意见：\" prop=\"shyj3\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"2\"\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.shyj3\"\n                placeholder=\"请输入审核意见\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"handleClose\">取 消</el-button>\n        <el-button\n          v-if=\"titles == '待生产科五防专责审核'\"\n          type=\"primary\"\n          @click=\"submitFormTg2\"\n          >提 交\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!--光伏分公司审批 通过 生产科主管领导 弹框-->\n    <el-dialog\n      :title=\"titles\"\n      :visible.sync=\"isShowTg3\"\n      width=\"30%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"130px\" ref=\"form\" :model=\"form\">\n        <!--防误装置解锁工具使用审批-->\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"生产科主管领导：\" prop=\"zgld2\">\n              <el-input\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.zgld2\"\n                placeholder=\"请输入生产科主管领导\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"审核意见：\" prop=\"shyj4\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"2\"\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.shyj4\"\n                placeholder=\"请输入审核意见\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"handleClose\">取 消</el-button>\n        <el-button\n          v-if=\"titles == '待生产科主管领导审核'\"\n          type=\"primary\"\n          @click=\"submitFormTg3\"\n          >提 交</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <!--工作流需要-->\n    <activiti\n      :processData=\"processData\"\n      :isShow=\"isShow\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n    ></activiti>\n    <time-line\n      :value=\"timeLineShow\"\n      :timeData=\"timeData\"\n      @closeTimeLine=\"colseTimeLine\"\n    />\n    <!--流程图查看-->\n    <el-dialog\n      title=\"审批流程图\"\n      :visible.sync=\"openLoadingImg\"\n      width=\"56%\"\n      append-to-body\n      :close-on-click-modal=\"false\"\n    >\n      <div>\n        <img :src=\"imgSrc\" style=\"width: 100%\" />\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  getListFwzzjs,\n  saveOrUpdateFwzzjs,\n  removeFwzzjs,\n  exportWord\n} from \"@/api/yxgl/gfyxgl/gfzbgl\";\nimport { getBdzDataListSelected as getBdzSelectList} from \"@/api/dagangOilfield/asset/gfsbtz\";\n//流程\nimport activiti from \"com/activiti\";\nimport timeLine from \"com/timeLine\";\nimport { HistoryList } from \"@/api/activiti/processTask\";\nimport { getFgsOptions } from \"@/api/yxgl/gfyxgl/gfzbgl\";\nexport default {\n  name: \"fwzz_fgssh\",\n  components: { activiti, timeLine },\n  data() {\n    return {\n      bjr: \"\",\n      currentUser: this.$store.getters.name,\n      //工作流弹窗\n      isShow: false,\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"fwzzjsgj\",\n        businessKey: \"\",\n        businessType: \"防误装置解锁工具使用\",\n        variables: {},\n        defaultFrom: true,\n        nextUser: \"\",\n        processType: \"complete\"\n      },\n      //流程图查看\n      openLoadingImg: false,\n      imgSrc: \"\", //流程图查看地址\n      timeData: [],\n      timeLineShow: false,\n      //弹出框标题\n      activitiOption: { title: \"上报\" },\n      isShowYj: true,\n      selectedSbParam: \"all\",\n      isShowSysbDialog: false,\n      bdzList: [],\n      isDisabled: false,\n      isShowHt: false,\n      isShowFgswf: false,\n      isShowTg1: false,\n      isShowTg2: false,\n      isShowTg3: false,\n      isShowTg4: false,\n      isDisabledBj: false,\n      selectNode: \"\",\n      fgsList: [],\n      form: {\n        lx: 2,\n        status: \"\",\n        wfzz1: \"\",\n        zgld1: \"\",\n        wfzz2: \"\",\n        zgld2: \"\"\n      },\n      // 多选框选中的id\n      ids: [],\n      // 多选框选选中的数据\n      selectData: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 对话框标题\n      title: \"\",\n      titles: \"\",\n      isShowBdfgssh: false,\n      filterInfo: {},\n      tableAndPageInfo: {},\n      /**\n       *  防误装置解锁工具使用登记\n       *  */\n      filterInfo1: {\n        data: {\n          dlqbh: \"\",\n          sjArr: [],\n          djr: \"\"\n        },\n        fieldList: [\n          {\n            label: \"分公司\",\n            type: \"select\",\n            value: \"fgs\",\n            checkboxValue: [],\n            options: []\n          },\n          { label: \"光伏电站\", type: \"select\", value: \"bdz\", options: [] },\n          {\n            label: \"时间\",\n            value: \"sjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"设备名称\", type: \"input\", value: \"sbmc\" },\n          {\n            label: \"状态\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"status\",\n            clearable: true,\n            options: [\n              { label: \"待分公司五防专责审核\", value: \"待分公司五防专责审核\" },\n              { label: \"待分公司主管领导审核\", value: \"待分公司主管领导审核\" },\n              { label: \"待生产科五防专责审核\", value: \"待生产科五防专责审核\" },\n              { label: \"待生产科主管领导审核\", value: \"待生产科主管领导审核\" },\n              { label: \"待办结\", value: \"待办结\" }\n            ]\n          }\n          /*{ label: '分公司五防专责', type: 'input', value: 'wfzz1' },\n            { label: '分公司主管领导', type: 'input', value: 'zgld1' },\n            { label: '生产科五防专责', type: 'input', value: 'wfzz2' },\n            { label: '生产科主管领导', type: 'input', value: 'zgld2' }*/\n        ]\n      },\n      tableAndPageInfo1: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"status\", label: \"状态\", minWidth: \"100\" },\n          { prop: \"fgsCn\", label: \"分公司\", minWidth: \"100\" },\n          { prop: \"bdzmc\", label: \"光伏电站\", minWidth: \"120\" },\n          { prop: \"sj\", label: \"时间\", minWidth: \"160\" },\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"120\" }\n          /*{ prop: 'wfzz1', label: '分公司五防专责', minWidth: '120' },\n            { prop: 'zgld1', label: '分公司主管领导', minWidth: '120' },\n            { prop: 'wfzz2', label: '生产科五防专责', minWidth: '120' },\n            { prop: 'zgld2', label: '生产科主管领导', minWidth: '120' },*/\n          /*{\n              prop: 'operation',\n              label: '操作',\n              fixed: 'right',\n              minWidth: '130px',\n              style: { display: 'block' },\n              operation: [\n                { name: '详情', clickFun: this.getDetails },\n                { name: '回退', clickFun: this.getHt },\n                { name: '通过', clickFun: this.getTg }\n                /!*{ name: '附件查看', clickFun: this.FjInfoList },*!/\n              ]\n            }*/\n        ]\n      },\n      params: {\n        lx: 2\n      },\n      jlrList: [] //记录人下拉框\n    };\n  },\n  created() {\n    this.filterInfo = this.filterInfo1;\n    this.tableAndPageInfo = this.tableAndPageInfo1;\n    //列表查询\n    this.getData();\n    //获取光伏站下拉框数据\n    // this.getBdzSelectList()\n    //获取分公司下拉框\n    this.getFgsList();\n  },\n  methods: {\n    //下拉框change事件\n    handleEvent(val) {\n      //光伏站查询下拉框选项根据所选分公司带出来\n      if (val.label === \"fgs\" && val.value && val.value !== \"\") {\n        let form = {\n          ssdwbm: val.value\n        };\n        getBdzSelectList(form).then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value === \"bdz\") {\n              return (item.options = res.data);\n            }\n          });\n        });\n      }\n    },\n    //获取分公司下拉框\n    getFgsList() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.fgsList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"fgs\") {\n            return (item.options = this.fgsList);\n          }\n        });\n      });\n    },\n    /**\n     * 办结\n     * */\n    getBj(row) {\n      this.titles = \"办结\";\n      this.form = { ...row };\n      this.isShowBdfgssh = true;\n      this.isDisabled = true;\n      this.isDisabledBj = false;\n    },\n    //关闭弹窗\n    closeActiviti() {\n      this.isShow = false;\n    },\n    //回退按钮\n    async handleTh(type) {\n      this.getSbFsBj({ type: type, data: this.form }, { defaultForm: false });\n      /*try {\n          if (this.form.status == '待分公司五防专责审核') {\n            this.form.status = '待上报'\n            this.form.lx = 1\n          } else if (this.form.status == '待分公司主管领导审核') {\n            this.form.status = '待分公司五防专责审核'\n          } else if (this.form.status == '待生产科五防专责审核') {\n            this.form.status = '待分公司主管领导审核'\n          } else if (this.form.status == '待生产科主管领导审核') {\n            this.form.status = '待生产科五防专责审核'\n          }\n          let { code } = await saveOrUpdateFwzzjs(this.form)\n          if (code === '0000') {\n            this.$message.success('操作成功')\n          }\n        } catch (e) {\n          console.log(e)\n        }\n        this.getData()\n        this.isShowBdfgssh = false*/\n    },\n    //关闭流程查看页面\n    colseTimeLine() {\n      this.timeLineShow = false;\n    },\n    async showTimeLine(row) {\n      this.processData.businessKey = row.objId;\n      let { code, data } = await HistoryList(this.processData);\n      this.timeData = data;\n      this.timeLineShow = true;\n    },\n    //流程图片查看\n    showProcessImg(row) {\n      console.log(row);\n      this.openLoadingImg = true;\n      this.imgSrc =\n        \"/activiti-api/process/read-resource?processDefinitionKey=fwzzjsgj&businessKey=\" +\n        row.objId +\n        \"&t=\" +\n        new Date().getTime();\n    },\n    //工作流回传数据\n    async todoResult(data) {\n      console.log(\"工作流回传数据data:\", data);\n      switch (data.activeTaskName) {\n        case \"防误装置信息填报\":\n          this.form.status = \"待修改\";\n          break;\n        case \"分公司五防专责审核\":\n          this.form.status = \"待修改\";\n          break;\n        case \"分公司主管领导审核\":\n          this.form.status = \"待分公司主管领导审核\";\n          break;\n        case \"生产科五防专责审核\":\n          this.form.status = \"待生产科五防专责审核\";\n          break;\n        case \"生产科主管领导审核\":\n          this.form.status = \"待生产科主管领导审核\";\n          break;\n        case \"结束\":\n          this.form.status = \"待办结\";\n      }\n      let row = {};\n      if (this.form.status == \"待修改\") {\n        row = {\n          objId: data.businessKey,\n          status: this.form.status,\n          lx: 1\n        };\n      } else if (this.form.status == \"待分公司主管领导审核\") {\n        row = {\n          objId: data.businessKey,\n          status: this.form.status,\n          lx: 2\n        };\n      } else if (this.form.status == \"待生产科五防专责审核\") {\n        row = {\n          objId: data.businessKey,\n          status: this.form.status,\n          lx: 2\n        };\n      } else if (this.form.status == \"待生产科主管领导审核\") {\n        row = {\n          objId: data.businessKey,\n          status: this.form.status,\n          lx: 2\n        };\n      } else if (this.form.status == \"待办结\") {\n        row = {\n          objId: data.businessKey,\n          status: this.form.status,\n          lx: 2\n        };\n      }\n      saveOrUpdateFwzzjs(row).then(res => {\n        console.log(\"res数据\", res);\n        if (res.code === \"0000\") {\n          this.$message.success(\"操作成功\");\n          //重置page页从1开始\n          this.tableAndPageInfo.pager.pageResize = \"Y\";\n          this.getData();\n          this.resetForm();\n        }\n      });\n    },\n    /**\n     * 根据表格名称获取对应的数据\n     */\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        const { data, code } = await getListFwzzjs(param);\n        console.log(\"防误装置解锁工具使用登记\");\n        console.log(data);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    /**\n     * 新增\n     */\n    addRow() {\n      this.titles = \"\";\n      this.isShowBdfgssh = true;\n    },\n    /**\n     * 回退\n     */\n    getHt(row) {\n      this.titles = \"回退\";\n      this.isShowHt = true;\n      this.form = { ...row };\n      this.isDisabled = false;\n    },\n    /**\n     * 通过1\n     */\n    getTg(row) {\n      this.form = { ...row };\n      this.isDisabled = false;\n      console.log(this.form);\n      if (this.form.status == \"待分公司五防专责审核\") {\n        this.titles = \"待分公司五防专责审核\";\n        this.isShowFgswf = true;\n        return;\n      } else if (this.form.status == \"待分公司主管领导审核\") {\n        this.titles = \"待分公司主管领导审核\";\n        this.isShowTg1 = true;\n        return;\n      } else if (this.form.status == \"待生产科五防专责审核\") {\n        this.titles = \"待生产科五防专责审核\";\n        this.isShowTg2 = true;\n        return;\n      } else if (this.form.status == \"待生产科主管领导审核\") {\n        this.titles = \"待生产科主管领导审核\";\n        this.isShowTg3 = true;\n        return;\n      } else if (\n        this.form.wfzz1 != null &&\n        this.form.zgld1 != null &&\n        this.form.wfzz2 != null &&\n        this.form.zgld2 != null\n      ) {\n        this.submitFormTg4();\n        return;\n      }\n    },\n    //上报发送办结  发送给分公司主管领导审核\n    getSbFsBj(args, isShow) {\n      let row = { ...args.data };\n      if (args.type === \"complete\") {\n        this.processData.defaultFrom = isShow.defaultForm; //是否显示选人员\n        this.processData.processType = \"complete\";\n        this.processData.jxgs = isShow.jxgs;\n        this.processData.businessKey = row.objId;\n        this.processData.variables.pass = true;\n      } else if (args.type === \"completeByGroup\") {\n        if (this.form.status == \"待分公司五防专责审核\") {\n          this.activitiOption.title = \"发送给分公司主管领导审核\";\n        } else if (this.form.status == \"待分公司主管领导审核\") {\n          this.activitiOption.title = \"发送给生产科五防专责审核\";\n        } else if (this.form.status == \"待生产科五防专责审核\") {\n          this.activitiOption.title = \"发送给生产科主管领导审核\";\n        } else if (this.form.status == \"待生产科主管领导审核\") {\n          this.activitiOption.title = \"审核完毕\";\n        }\n        this.processData.defaultFrom = isShow.defaultForm; //是否显示选人员\n        this.processData.processType = \"completeByGroup\";\n        this.processData.jxgs = isShow.jxgs;\n        this.processData.businessKey = row.objId;\n        this.processData.variables.pass = true;\n        this.processData.variables.personGroupId = isShow.personGroupId;\n      } else {\n        this.activitiOption.title = \"回退原因提报\";\n        this.processData.defaultFrom = true;\n        this.processData.processType = \"rollback\";\n        this.processData.businessKey = row.objId;\n        this.processData.variables.pass = true;\n      }\n      this.isShow = true;\n    },\n    /**\n     * 详情\n     */\n    getDetails(row) {\n      this.titles = \"详情查看\";\n      this.isDisabled = true;\n      this.isDisabledBj = true;\n      this.isShowBdfgssh = true;\n      this.form = { ...row };\n      this.bdzList = [{ label: row.bdzmc, value: row.bdz }];\n      this.jlrList = [{ label: row.jlrCn, value: row.jlr }];\n    },\n    /**\n     * 提交 防误装置解锁工具使用登记 回退 表单\n     */\n    async submitFormHt() {\n      try {\n        this.form.lx = 1;\n        this.form.status = \"待修改\";\n        let { code } = await saveOrUpdateFwzzjs(this.form);\n        if (code === \"0000\") {\n          this.$message.success(\"操作成功\");\n          await this.getData();\n        }\n      } catch (e) {\n        console.log(e);\n      }\n      this.isShowHt = false;\n    },\n    /**\n     * 提交 防误装置解锁工具使用登记 通过-分公司五防专责 表单\n     */\n    async submitFormSh1() {\n      this.processData.defaultFrom = true;\n      this.processData.jxgs = false;\n      try {\n        let { code, data } = await saveOrUpdateFwzzjs(this.form);\n        if (code === \"0000\") {\n          this.$message.success(\"操作成功\");\n          this.getSbFsBj(\n            { data: data, type: \"completeByGroup\" },\n            {\n              defaultForm: this.processData.defaultFrom,\n              jxgs: this.processData.jxgs,\n              personGroupId: 19\n            }\n          );\n        }\n      } catch (e) {\n        console.log(e);\n      }\n      this.getData();\n      this.isShowFgswf = false;\n    },\n    /**\n     * 提交 防误装置解锁工具使用登记 通过-分公司主管领导 表单\n     */\n    async submitFormTg() {\n      this.processData.defaultFrom = true;\n      this.processData.jxgs = false;\n      try {\n        /*this.form.lx = 2\n          this.form.status = '待生产科五防专责审核'*/\n        let { code, data } = await saveOrUpdateFwzzjs(this.form);\n        if (code === \"0000\") {\n          this.$message.success(\"操作成功\");\n          this.getSbFsBj(\n            { data: data, type: \"completeByGroup\" },\n            {\n              defaultForm: this.processData.defaultFrom,\n              jxgs: this.processData.jxgs,\n              personGroupId: 20\n            }\n          );\n        }\n      } catch (e) {\n        console.log(e);\n      }\n      this.getData();\n      this.isShowTg1 = false;\n    },\n    /**\n     * 提交 防误装置解锁工具使用登记 通过-生产科五防专责 表单\n     */\n    async submitFormTg2() {\n      this.processData.defaultFrom = true;\n      this.processData.jxgs = false;\n      try {\n        /*this.form.lx = 2\n          this.form.status = '待生产科主管领导审核'*/\n        let { code, data } = await saveOrUpdateFwzzjs(this.form);\n        if (code === \"0000\") {\n          this.$message.success(\"操作成功\");\n          this.getSbFsBj(\n            { data: data, type: \"completeByGroup\" },\n            {\n              defaultForm: this.processData.defaultFrom,\n              jxgs: this.processData.jxgs,\n              personGroupId: 21\n            }\n          );\n        }\n      } catch (e) {\n        console.log(e);\n      }\n      this.getData();\n      this.isShowTg2 = false;\n    },\n    /**\n     * 提交 防误装置解锁工具使用登记 通过-生产科主管领导 表单\n     */\n    async submitFormTg3() {\n      this.processData.defaultFrom = false;\n      this.processData.jxgs = false;\n      try {\n        let { code, data } = await saveOrUpdateFwzzjs(this.form);\n        if (code === \"0000\") {\n          this.$message.success(\"操作成功\");\n          this.getSbFsBj(\n            { data: data, type: \"completeByGroup\" },\n            {\n              defaultForm: this.processData.defaultFrom,\n              jxgs: this.processData.jxgs,\n              personGroupId: 0\n            }\n          );\n          await this.getData();\n        }\n      } catch (e) {\n        console.log(e);\n      }\n      this.isShowTg3 = false;\n    },\n    /**\n     * 提交 防误装置解锁工具使用登记 通过-全部 表单\n     */\n    async submitFormTg4() {\n      this.$confirm(\"确认审核通过该数据吗?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          this.form.lx = 3;\n          this.form.status = \"待办结\";\n          saveOrUpdateFwzzjs(this.form).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"审核通过!\"\n              });\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"审核不通过!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消审核\"\n          });\n        });\n      /*try {\n          this.form.lx = 3\n          this.form.status = '待办结'\n          let { code } = await saveOrUpdateFwzzjs(this.form)\n          if (code === '0000') {\n            this.$message.success('操作成功')\n            await this.getData()\n          }\n        } catch (e) {\n          console.log(e)\n        }\n        this.isShowTg3 = false*/\n    },\n\n    /**\n     * 删除按钮\n     */\n    async handleDelete(row) {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          //防误装置解锁工具使用登记\n          removeFwzzjs(JSON.stringify(row.objId)).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    fgsChangeFun() {},\n    //导出word\n    exportWord() {\n      if (!this.selectData.length > 0) {\n        this.$message.warning(\"请先选中要导出的数据\");\n        return;\n      }\n      try {\n        let fileName = \"防误装置解锁工具使用记录\";\n        let exportUrl = \"yxFwzzjsgjsyjl\";\n        let params = {\n          data: this.selectData,\n          url: exportUrl\n        };\n        exportWord(params, fileName);\n      } catch (e) {\n        this.$message.error(\"导出失败！\");\n      }\n    },\n    handleClose() {\n      this.isShowBdfgssh = false;\n      this.isShowFgswf = false;\n      this.isShowHt = false;\n      this.isShowTg1 = false;\n      this.isShowTg2 = false;\n      this.isShowTg3 = false;\n      this.isShowTg4 = false;\n    },\n    async submitFormFwzzjs() {\n      try {\n        this.form.status = \"已办结\";\n        this.form.lx = 4;\n        let { code, data } = await saveOrUpdateFwzzjs(this.form);\n        if (code === \"0000\") {\n          this.$message.success(\"操作成功\");\n        }\n      } catch (e) {\n        console.log(e);\n      }\n      this.getData();\n      this.isShowBdfgssh = false;\n    },\n    /**\n     * 多选款选中数据\n     * @param row\n     */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.objId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n      this.selectData = selection;\n    },\n    filterReset() {\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n      this.params = {\n        lx: 2\n      };\n    },\n    /**\n     * 获取光伏站下拉框数据\n     */\n    getBdzSelectList() {\n      getBdzSelectList({}).then(res => {\n        this.bdzList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"bdz\") {\n            return (item.options = this.bdzList);\n          }\n        });\n      });\n    }\n  }\n};\n</script>\n\n<style scoped></style>\n"], "sourceRoot": "src/views/dagangOilfield/czpgl/gfdzcz/components"}]}