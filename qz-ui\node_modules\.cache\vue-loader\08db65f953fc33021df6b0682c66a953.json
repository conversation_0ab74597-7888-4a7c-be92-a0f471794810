{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\gqjgl\\gfgqj.vue?vue&type=style&index=0&id=f4fa25a8&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\gqjgl\\gfgqj.vue", "mtime": 1752489170836}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5idXR0b24tZ3JvdXAgewogIHBhZGRpbmctbGVmdDogMzBweDsKICBwYWRkaW5nLXJpZ2h0OiAzMHB4OwogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsKfQoKLnF4bHJfZGlhbG9nX2luc2VydCB7CiAgbWFyZ2luLXRvcDogNnZoICFpbXBvcnRhbnQ7Cn0KCi8qL2RlZXAvIC5xeGxyX2RpYWxvZ19pbnNlcnQgLmVsLWlucHV0LS1tZWRpdW0gLmVsLWlucHV0X19pbm5lcnsqLwovKiAgd2lkdGg6IDEwMCU7Ki8KLyp9Ki8KLmVsLXNlbGVjdCB7CiAgd2lkdGg6IDEwMCU7Cn0KCi5lbC1kYXRlLWVkaXRvciB7CiAgd2lkdGg6IDEwMCU7Cn0K"}, {"version": 3, "sources": ["gfgqj.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkyBA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "gfgqj.vue", "sourceRoot": "src/views/dagangOilfield/dwzygl/gqjgl", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      ref=\"filter1\"\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      @handleReset=\"filterReset\"\n      @handleEvent=\"handleEvent\"\n    />\n    <el-white class=\"button-group1\">\n      <div class=\"button_btn\">\n        <el-button\n          @click=\"addSensorButton\"\n          v-hasPermi=\"['gfgql:button:add']\"\n          icon=\"el-icon-plus\"\n          type=\"primary\"\n          >新增\n        </el-button>\n        <el-button type=\"primary\" icon=\"el-icon-download\" @click=\"exportExcel()\"\n          >导出</el-button\n        >\n      </div>\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"selectChange\"\n        height=\"63vh\"\n      >\n        <el-table-column\n          slot=\"table_eight\"\n          align=\"center\"\n          fixed=\"right\"\n          style=\"display: block\"\n          label=\"操作\"\n          width=\"180\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              @click=\"getGqjInfo(scope.row)\"\n              class=\"el-icon-view\"\n              title=\"详情\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"scope.row.createBy == currUser\"\n              @click=\"updateGqjInfo(scope.row)\"\n              class=\"el-icon-edit\"\n              title=\"编辑\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"scope.row.createBy == currUser\"\n              @click=\"deleteRow(scope.row.objId)\"\n              class=\"el-icon-delete\"\n              title=\"删除\"\n            ></el-button>\n          </template>\n        </el-table-column>\n      </comp-table>\n    </el-white>\n    <!-- 工器具台账详情修改新增 -->\n    <el-dialog\n      :title=\"gqjTital\"\n      :visible.sync=\"dialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form\n        :model=\"form\"\n        label-width=\"80px\"\n        :disabled=\"isDisabled\"\n        :rules=\"rules\"\n        ref=\"form\"\n      >\n        <el-row>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属公司\" prop=\"ssgs\">\n              <el-select\n                v-model=\"form.ssgs\"\n                placeholder=\"所属公司\"\n                clearable\n                :disabled=\"isDisabled\"\n                @change=\"getBdzList\"\n              >\n                <el-option\n                  v-for=\"item in organizationSelectedList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属光伏电站\" prop=\"bdz\">\n              <el-select\n                placeholder=\"请选择光伏电站\"\n                clearable\n                v-model=\"form.bdz\"\n                :disabled=\"isDisabled\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in bdzList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"名称\" prop=\"sbmc\">\n              <el-input v-model=\"form.sbmc\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"设备分类\" prop=\"sblx\">\n              <el-select\n                placeholder=\"请选择\"\n                clearable\n                v-model=\"form.sblx\"\n                :disabled=\"isDisabled\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in gfgqjlx\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"规格型号\" prop=\"xh\">\n              <el-input v-model=\"form.xh\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <!--          <el-col :span=\"8\">\n                      <el-form-item label=\"编号\" style=\"width: 100%\">\n                        <el-input v-model=\"form.ccbh\" :disabled=\"isDisabled\"></el-input>\n                      </el-form-item>\n                    </el-col>-->\n          <el-col :span=\"8\">\n            <el-form-item label=\"数量\" prop=\"sl\">\n              <el-input-number\n                :min=\"1\"\n                v-model=\"form.sl\"\n                :disabled=\"isDisabled\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"使用年限\">\n              <el-input v-model=\"form.jyzq\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"领用人\" prop=\"fzr\">\n              <el-input v-model=\"form.fzr\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"批准人\" prop=\"cfdd\">\n              <el-input v-model=\"form.cfdd\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"领取时间\" prop=\"tyrq\">\n              <el-date-picker\n                v-model=\"form.tyrq\"\n                type=\"date\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"选择日期\"\n                :disabled=\"isDisabled\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\">\n              <el-input\n                type=\"textarea\"\n                v-model=\"form.bz\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"qxcommit\"\n          v-if=\"gqjTital === '工器具新增' || gqjTital === '工器具修改'\"\n          class=\"pmyBtn\"\n        >\n          确 定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getUUID } from \"@/utils/ruoyi\";\nimport {\n  deleteAssetGqjJxRecords,\n  deleteYxSyRecords,\n  getAssetGqjJxRecords,\n  getList,\n  getYxSyRecords,\n  remove,\n  saveOrUpdate,\n  saveOrUpdateAssetGqjJxRecords,\n  saveOrUpdateYxSyRecords,\n  exportExcel,\n  getGfBdzSelectList\n} from \"@/api/dagangOilfield/asset/assetGqj\";\nimport CompTable from \"com/CompTable\";\nimport ElFilter from \"com/ElFilter\";\nimport { getDictTypeData } from \"@/api/system/dict/data\";\nimport { getFgsOptions } from \"@/api/yxgl/gfyxgl/gfzbgl\";\n\n\nexport default {\n  components: { CompTable, ElFilter },\n  name: \"gqjgl\",\n  data() {\n    return {\n      gfgqjlx: [],\n      currUser: this.$store.getters.name,\n      params: {\n        type: \"gf\"\n      },\n      bdzAllList: [],\n      bdzList: [],\n      //组织结构下拉数据\n      organizationSelectedList: [],\n      // 表单校验\n      rules: {\n        sl: [{ required: true, message: \"请输入\", trigger: \"blur\" }],\n        ssgs: [{ required: true, message: \"请选择\", trigger: \"blur\" }],\n        // bdz: [{ required: true, message: '请选择', trigger: 'blur' }],\n        sbmc: [{ required: true, message: \"请输入\", trigger: \"blur\" }],\n        sblx: [{ required: true, message: \"请选择\", trigger: \"blur\" }],\n        fzr: [{ required: true, message: \"请输入\", trigger: \"blur\" }]\n      },\n      //工器具详情框字段控制\n      isDisabled: false,\n      //工器具弹出框表头\n      gqjTital: \"工器具新增\",\n      //表格内容\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"ssgsmc\", label: \"所属公司\", minWidth: \"120\" },\n          { prop: \"bdzmc\", label: \"所属光伏电站\", minWidth: \"120\" },\n          { prop: \"sbmc\", label: \"名称\", minWidth: \"180\" },\n          { prop: \"xh\", label: \"规格型号\", minWidth: \"180\" },\n          { prop: \"fzr\", label: \"领用人\", minWidth: \"180\" },\n          // {prop: 'ccbh', label: '编号', minWidth: '180'},\n          { prop: \"jyzq\", label: \"使用年限\", minWidth: \"180\" },\n          { prop: \"tyrq\", label: \"领取时间\", minWidth: \"250\" }\n          // {\n          //   fixed: \"right\",\n          //   prop: 'operation',\n          //   label: '操作',\n          //   minWidth: '150px',\n          //   style: {display: 'block'},\n          //   operation: [\n          //     {name: '修改', clickFun: this.updateGqjInfo},\n          //     {name: '详情', clickFun: this.getGqjInfo},\n          //   ],\n          // },\n        ]\n      },\n      //筛选条件\n      filterInfo: {\n        data: {\n          fzr: \"\",\n          ssgs: \"\",\n          yxbz: \"\",\n          phone: \"\"\n        },\n        fieldList: [\n          {\n            label: \"分公司\",\n            value: \"ssgs\",\n            type: \"select\",\n            options: [],\n            clearable: true\n          },\n          {\n            label: \"光伏电站\",\n            value: \"bdz\",\n            type: \"select\",\n            options: [],\n            clearable: true\n          },\n          { label: \"名称\", type: \"input\", value: \"sbmc\" },\n          { label: \"规格型号\", type: \"input\", value: \"xh\" },\n          { label: \"领用人\", type: \"input\", value: \"fzr\" }\n          // {label: '投运日期', type: 'date', value: 'tyrqArr',dateType: 'daterange',format: 'yyyy-MM-dd'},\n        ]\n      },\n      //检修记录弹出框\n      jwxDialogFormVisible: false,\n      //添加检修记录弹出框\n      addJwxSybgDialogFormVisible: false,\n      //工器具弹出框\n      dialogFormVisible: false,\n      //试验时间\n      sysj: \"\",\n      fildtps: [],\n      //试验弹出框\n      sybgDialogFormVisible: false,\n      //添加试验报告\n      addSybgDialogFormVisible: false,\n      //弹出框表单\n      form: {\n        type: \"gf\"\n      },\n      loading: false,\n      //工器具试验数据集合\n      gqjsyList: [],\n      //检修数据集合\n      gqjJxList: [],\n      //删除是否可用\n      multipleSensor: true,\n      showSearch: true,\n      //删除选择列\n      selectRows: [],\n      //工器具文件上传参数\n      gqjInfoUploadData: {\n        businessId: undefined\n      },\n      //工器具文件上传请求头\n      gqjInfoUpHeader: {},\n\n      //试验查询条件\n      syQueryForm: {\n        gqjId: \"\",\n        total: 0,\n        pageSize: 10,\n        pageNum: 1\n      },\n\n      //试验新增表单数据\n      syFrom: {\n        id: \"\",\n        gqjId: \"\",\n        sydwId: \"\",\n        sydwName: \"\",\n        syryId: \"\",\n        syryName: \"\",\n        sysj: \"\",\n        syjlCode: \"\",\n        syjlName: \"\",\n        remark: \"\"\n      },\n\n      isSyDetail: false,\n\n      //检修查询条件\n      jxQueryForm: {\n        gqjId: \"\",\n        total: 0,\n        pageSize: 10,\n        pageNum: 1\n      },\n      //检修表单\n      jxForm: {\n        id: \"\",\n        jxdwId: \"\",\n        jxdwName: \"\",\n        jxryId: \"\",\n        jxryName: \"\",\n        jxjg: \"\",\n        jxsj: \"\",\n        remark: \"\",\n        gqjId: \"\"\n      },\n\n      //主表选中行数据\n      mainRowData: {},\n      //试验table加载\n      syLoading: false,\n      //试验选中行\n      sySelectRows: [],\n      //检修table加载\n      jxLoading: false,\n      //检修选中行\n      jxSelectRows: []\n    };\n  },\n  watch: {},\n  created() {\n    //获取组织结构下拉数据\n    this.getFgsOptions();\n  },\n  mounted() {\n    getGfBdzSelectList({}).then(async res => {\n      this.bdzAllList = res.data;\n      let { data: gfgqjlx } = await getDictTypeData(\"gfgqjlx\");\n      this.gfgqjlx = gfgqjlx;\n      this.getData();\n    });\n  },\n  methods: {\n    /**\n     * 获取分公司下拉数据\n     */\n    getFgsOptions() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.organizationSelectedList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"ssgs\") {\n            return (item.options = this.organizationSelectedList);\n          }\n        });\n      });\n    },\n    exportExcel() {\n      exportExcel(this.params, \"光伏工器具\");\n    },\n    formatBdz(id) {\n      if (id) {\n        return this.bdzAllList.filter(g => g.value === id)[0].label;\n      } else {\n        return \"\";\n      }\n    },\n    //获取光伏电站下拉框\n    getBdzList() {\n      getGfBdzSelectList({ ssdwbm: this.form.ssgs }).then(res => {\n        this.bdzList = res.data;\n      });\n    },\n\n    handleEvent(val, eventValue) {\n      if (val.label === \"ssgs\") {\n        getGfBdzSelectList({ ssdwbm: val.value.toString() }).then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value == \"bdz\") {\n              this.$set(eventValue, \"bdz\", \"\");\n              return (item.options = res.data);\n            }\n          });\n        });\n      }\n    },\n    /**\n     * 上传附附件之前的处理函数\n     * @param file\n     */\n    gqjInfoBeforeUpload(file) {\n      const fileSize = file.size < 1024 * 1024 * 50; //10M\n      if (!fileSize) {\n        this.$message.error(\"上传文件大小不能超过 50MB!\");\n      }\n      let size = file.size / 1024;\n    },\n    /**\n     * 上传附件成功调用的函数\n     * @param response\n     * @param file\n     * @param fileList\n     */\n    gqjInfoonSuccess(response, file, fileList) {\n      //文件id\n      this.form.attachmentid = response.data.businessId;\n      //文件名称\n      this.form.attachmentname = response.data.sysFile.fileOldName;\n    },\n\n    /**\n     * 移除文件\n     * @param file\n     * @param fileList\n     */\n    gqjInfohandleRemove(file, fileList) {},\n    /**\n     * 工器具上传文件到服务器\n     */\n    gqjInfoSubmitUpload() {\n      debugger;\n      this.gqjInfoUploadData.businessId = getUUID();\n      this.$refs.uploadGqjInfo.submit();\n    },\n    formatSsgs(ssgs) {\n      if (ssgs && ssgs !== \"3002\") {\n        return this.organizationSelectedList.filter(g => g.value === ssgs)[0]\n          .label;\n      } else {\n        return \"港东光伏电分公司\";\n      }\n    },\n    //工器具列表查询\n    async getData(params) {\n        this.params = { ...this.params, ...params };\n        const { data, code } = await getList(this.params);\n        if (code === \"0000\") {\n          for (let i of data.records) {\n            i.ssgsmc = this.formatSsgs(i.ssgs);\n            i.bdzmc = this.formatBdz(i.bdz);\n          }\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n        }\n    },\n\n    //工器具列表新增按钮\n    addSensorButton() {\n      //开启弹出框内输入框编辑权限\n      this.isDisabled = false;\n      //打开弹出框\n      this.dialogFormVisible = true;\n      //设置弹出框表头\n      this.gqjTital = \"工器具新增\";\n      //清空弹出框内容\n      this.form = {\n        type: \"gf\"\n      };\n    },\n    //工器具列表详情按钮\n    getGqjInfo(row) {\n      //打开弹出框\n      this.dialogFormVisible = true;\n      //设置弹出框表头\n      this.gqjTital = \"工器具详情\";\n      //禁用所有输入框\n      this.isDisabled = true;\n      //给弹出框赋值\n      this.form = { ...row };\n      this.getBdzList();\n    },\n    //工器具修改按钮\n    updateGqjInfo(row) {\n      //打开弹出框\n      this.dialogFormVisible = true;\n      //设置弹出框表头\n      this.gqjTital = \"工器具修改\";\n      //开启弹出框内输入框编辑权限\n      this.isDisabled = false;\n      //给弹出框内赋值\n      this.form = { ...row };\n      this.getBdzList();\n    },\n    //工器具列表新增修改保存\n    async qxcommit() {\n      await this.$refs[\"form\"].validate(async valid => {\n        if (valid) {\n          try {\n            let { code } = await saveOrUpdate(this.form);\n            if (code === \"0000\") {\n              this.$message.success(\"操作成功\");\n            }\n          } catch (e) {\n            console.log(e);\n          }\n          //恢复分页\n          this.tableAndPageInfo.pager.pageResize = \"Y\";\n          this.getData();\n          this.dialogFormVisible = false;\n        }\n      });\n    },\n    //删除工器具列表\n    deleteRow(id) {\n      // if (this.selectRows.length < 1) {\n      //   this.$message.warning(\"请选择正确的数据！！！\")\n      //   return\n      // }\n      // let ids = this.selectRows.map(item => {\n      //   return item.objId\n      // });\n      let obj = [];\n      obj.push(id);\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove(obj).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n      this.tableAndPageInfo.pager.pageResize = \"Y\";\n      this.getData();\n    },\n\n    //查看试验\n    handleSearchSYClick(row) {\n      this.sySelectRows = [];\n      this.mainRowData = row;\n      this.syQueryForm.gqjId = row.objId;\n      this.sybgDialogFormVisible = true;\n      this.getYxSyData();\n    },\n\n    //查看检修\n    handleSerchJWXClick(row) {\n      this.mainRowData = row;\n      this.jxQueryForm.gqjId = row.objId;\n      this.jwxDialogFormVisible = true;\n      this.getJxRecords();\n    },\n    //添加检修\n    addJxButton() {\n      this.jxForm = this.$options.data().jxForm;\n      this.jxForm.gqjId = this.mainRowData.objId;\n      this.addJwxSybgDialogFormVisible = true;\n    },\n    updateJx(row) {\n      this.jxForm = row;\n      this.addJwxSybgDialogFormVisible = true;\n    },\n    //添加试验\n    addSyButton() {\n      this.syFrom = this.$options.data().syFrom;\n      this.syFrom.gqjId = this.mainRowData.objId;\n      this.addSybgDialogFormVisible = true;\n    },\n    updateSy(row) {\n      this.syFrom = row;\n      this.addSybgDialogFormVisible = true;\n    },\n    //每页展示数量点击事件\n    handleSizeChange() {},\n    //页码改变事件\n    handleCurrentChange() {},\n    //树点击事件\n    handleNodeClick() {},\n\n    filterReset() {},\n    //选择每一行\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n\n    //获取试验记录数据\n    getYxSyData() {\n      this.syLoading = true;\n      getYxSyRecords(this.syQueryForm).then(res => {\n        this.gqjsyList = res.data.records;\n        this.syQueryForm.total = res.data.total;\n        this.syLoading = false;\n      });\n    },\n\n    //新增修改试验记录数据\n    saveOrUpdateSy() {\n      saveOrUpdateYxSyRecords(this.syFrom).then(res => {\n        this.getYxSyData();\n        this.addSybgDialogFormVisible = false;\n      });\n    },\n    //批量删除试验数据\n    deleteYxSy() {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          let ids = [];\n          this.sySelectRows.forEach(item => {\n            ids.push(item.id);\n          });\n          deleteYxSyRecords(ids).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n            this.getYxSyData();\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n\n    //获取检修记录数据\n    getJxRecords() {\n      this.jxLoading = true;\n      getAssetGqjJxRecords(this.jxQueryForm).then(res => {\n        this.gqjJxList = res.data.records;\n        this.jxQueryForm.total = res.data.total;\n        this.jxLoading = false;\n      });\n    },\n    //新增修改检修记录数据\n    saveOrUpdateJx() {\n      saveOrUpdateAssetGqjJxRecords(this.jxForm).then(res => {\n        this.getJxRecords();\n        this.addJwxSybgDialogFormVisible = false;\n      });\n    },\n    deleteJxData() {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          let ids = [];\n          this.jxSelectRows.forEach(item => {\n            ids.push(item.id);\n          });\n          deleteAssetGqjJxRecords(ids).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n            this.getJxRecords();\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n\n    syRowClick(rows) {\n      this.$refs.syTable.toggleRowSelection(rows);\n    },\n    syCurrentChange(val) {\n      this.sySelectRows = val;\n    },\n    jxRowClick(rows) {\n      this.$refs.jxTable.toggleRowSelection(rows);\n    },\n    jxCurrentChange(val) {\n      this.jxSelectRows = val;\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.button-group {\n  padding-left: 30px;\n  padding-right: 30px;\n  display: flex;\n  justify-content: flex-end;\n}\n\n.qxlr_dialog_insert {\n  margin-top: 6vh !important;\n}\n\n/*/deep/ .qxlr_dialog_insert .el-input--medium .el-input__inner{*/\n/*  width: 100%;*/\n/*}*/\n.el-select {\n  width: 100%;\n}\n\n.el-date-editor {\n  width: 100%;\n}\n</style>\n"]}]}