{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\gfdzcz\\dzczp.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\gfdzcz\\dzczp.vue", "mtime": 1749015121390}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsKaW1wb3J0IGFwaSBmcm9tICJAL3V0aWxzL3JlcXVlc3QiOwppbXBvcnQgRWxJbWFnZVZpZXdlciBmcm9tICJlbGVtZW50LXVpL3BhY2thZ2VzL2ltYWdlL3NyYy9pbWFnZS12aWV3ZXIiOwppbXBvcnQgewogIGV4cG9ydFBkZiwKICBleHBvcnRXb3JkLAogIGdldEN6cG14TGlzdCwKICBnZXRMaXN0THNwLAogIHByZXZpZXdGaWxlLAogIHJlbW92ZSwKICBzYXZlT3JVcGRhdGUsCiAgdXBkYXRlQnlJZAp9IGZyb20gIkAvYXBpL3l4Z2wvYmR5eGdsL2JkZHpjenAiOwppbXBvcnQgeyBnZXRCZHpEYXRhTGlzdFNlbGVjdGVkIH0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYXNzZXQvZ2ZzYnR6IjsKaW1wb3J0IEVsZWN0cm9uaWNBdXRoRGlhbG9nIGZyb20gImNvbS9FbGVjdHJvbmljQXV0aERpYWxvZyI7Ci8v5rWB56iLCmltcG9ydCBhY3Rpdml0aSBmcm9tICJjb20vYWN0aXZpdGlfY3pwIjsKaW1wb3J0IHRpbWVMaW5lIGZyb20gImNvbS90aW1lTGluZSI7CmltcG9ydCB7IEhpc3RvcnlMaXN0IH0gZnJvbSAiQC9hcGkvYWN0aXZpdGkvcHJvY2Vzc1Rhc2siOwppbXBvcnQgeyBnZXRVc2VycyB9IGZyb20gIkAvYXBpL3N5c3RlbS91c2VyZ3JvdXAiOwppbXBvcnQgeyBnZXRVVUlEIH0gZnJvbSAiQC91dGlscy9ydW95aSI7CmltcG9ydCB7IGdldEZnc09wdGlvbnMgfSBmcm9tICJAL2FwaS95eGdsL2dmeXhnbC9nZnpiZ2wiOwppbXBvcnQgeyBleHBvcnRUb0V4Y2VsLCBpbXBvcnRGcm9tRXhjZWwgfSBmcm9tICJAL2NvbXBvbmVudHMvY29tbW9uL2V4Y2VsLmpzIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiZHpjenAiLAogIGNvbXBvbmVudHM6IHsgRWxlY3Ryb25pY0F1dGhEaWFsb2csIGFjdGl2aXRpLCB0aW1lTGluZSwgRWxJbWFnZVZpZXdlciB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgaGFzU3VwZXJSb2xlOiB0aGlzLiRzdG9yZS5nZXR0ZXJzLmhhc1N1cGVyUm9sZSwKICAgICAgamxyTGlzdDogW10sCiAgICAgIC8v54q25oCB5LiL5ouJ5qGG5pWw5o2uCiAgICAgIHN0YXR1c09wdGlvbnM6IFsKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIjAiLAogICAgICAgICAgbGFiZWw6ICLmk43kvZznpajloavmiqUiCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIjEiLAogICAgICAgICAgbGFiZWw6ICLnj63nu4TlrqHmoLgiCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIjIiLAogICAgICAgICAgbGFiZWw6ICLliIblhazlj7jlrqHmoLgiCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIjMiLAogICAgICAgICAgbGFiZWw6ICLmk43kvZznpajlip7nu5MiCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB2YWx1ZTogIjQiLAogICAgICAgICAgbGFiZWw6ICLnu5PmnZ8iCiAgICAgICAgfSx7IGxhYmVsOiAi5L2c5bqfIiwgdmFsdWU6ICI3IiB9CiAgICAgIF0sCiAgICAgIGJ1dHRvbk5hbWVTaG93OiBmYWxzZSwKICAgICAgYnV0dG9uTmFtZTogIiIsCiAgICAgIGlzRGlzYWJsZWRCajogdHJ1ZSwKICAgICAgLy/nu4Tnu4fnu5PmnoTkuIvmi4nmlbDmja4KICAgICAgb3JnYW5pemF0aW9uU2VsZWN0ZWRMaXN0OiBbXSwKICAgICAgYmpyOiAiIiwKICAgICAgY3VycmVudFVzZXI6IHRoaXMuJHN0b3JlLmdldHRlcnMubmFtZSwKICAgICAgLy/lt6XkvZzmtYHkvKDlhaXlj4LmlbAKICAgICAgcHJvY2Vzc0RhdGE6IHsKICAgICAgICBwcm9jZXNzRGVmaW5pdGlvbktleTogImN6cHNoIiwKICAgICAgICBidXNpbmVzc0tleTogIiIsCiAgICAgICAgYnVzaW5lc3NUeXBlOiAi5YCS6Ze45pON5L2c56WoIiwKICAgICAgICB2YXJpYWJsZXM6IHt9LAogICAgICAgIGRlZmF1bHRGcm9tOiB0cnVlLAogICAgICAgIG5leHRVc2VyOiAiIiwKICAgICAgICBwcm9jZXNzVHlwZTogImNvbXBsZXRlIgogICAgICB9LAogICAgICAvLyDmmK/lkKblt7LmiafooYzkuIvmi4nmoYYKICAgICAgc2Z5enhMaXN0OiBbCiAgICAgICAgeyBsYWJlbDogIuW3suaJp+ihjCIsIHZhbHVlOiAi5bey5omn6KGMIiB9LAogICAgICAgIHsgbGFiZWw6ICLmnKrmiafooYwiLCB2YWx1ZTogIuacquaJp+ihjCIgfQogICAgICBdLAogICAgICAvL+W3peS9nOa1geW8ueeqlwogICAgICBpc1Nob3c6IGZhbHNlLAogICAgICAvL+a1geeoi+WbvuafpeeciwogICAgICBvcGVuTG9hZGluZ0ltZzogZmFsc2UsCiAgICAgIGltZ1NyYzogIiIsIC8v5rWB56iL5Zu+5p+l55yL5Zyw5Z2ACiAgICAgIHRpbWVEYXRhOiBbXSwKICAgICAgdGltZUxpbmVTaG93OiBmYWxzZSwKICAgICAgLy/lvLnlh7rmoYbmoIfpopgKICAgICAgYWN0aXZpdGlPcHRpb246IHsgdGl0bGU6ICLkuIrmiqUiIH0sCiAgICAgIHRpdGxleWw6ICIiLAogICAgICBpc1Nob3dTaDogZmFsc2UsCiAgICAgIGlzU2hTaG93RGV0YWlsczogZmFsc2UsCiAgICAgIHlsOiBmYWxzZSwKICAgICAgLy/lm77niYflnLDlnYB1cmwKICAgICAgZGlhbG9nSW1hZ2VVcmw6ICIiLAogICAgICAvL+WxleekuuWbvueJh2RpYWxvZ+aOp+WItgogICAgICBpbWdEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgYmR6TGlzdDogW10sCiAgICAgIGlzSW5kZXRlcm1pbmF0ZTogdHJ1ZSwKICAgICAgLy8g5aSa6YCJ5qGG6YCJ5Lit55qEaWQKICAgICAgaWRzOiBbXSwKICAgICAgc2luZ2xlOiB0cnVlLAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgc2VsZWN0RGF0YTogW10sCiAgICAgIC8v5by55Ye65qGG5Lit6KGo5qC85pWw5o2uCiAgICAgIHByb3BUYWJsZURhdGE6IHsKICAgICAgICBzZWw6IG51bGwsIC8vIOmAieS4reihjAogICAgICAgIGNvbEZpcnN0OiBbXQogICAgICB9LAogICAgICBydWxlczogewogICAgICAgIC8vIGtzc2o6IFsKICAgICAgICAvLyAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+aTjeS9nOW8gOWni+aXtumXtOS4jeiDveS4uuepuicsIHRyaWdnZXI6ICdibHVyJ30KICAgICAgICAvLyBdLAogICAgICAgIC8vIGpzc2o6IFsKICAgICAgICAvLyAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+aTjeS9nOe7k+adn+aXtumXtOS4jeiDveS4uuepuicsIHRyaWdnZXI6ICdjaGFuZ2UnfQogICAgICAgIC8vIF0sCiAgICAgICAgZmdzOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWIhuWFrOWPuOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJzZWxlY3QiIH1dLAogICAgICAgIGJkem1jOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5YWJ5LyP55S156uZ5LiN6IO95Li656m6IiwgdHJpZ2dlcjogInNlbGVjdCIgfQogICAgICAgIF0sCiAgICAgICAgY3pydzogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaTjeS9nOS7u+WKoeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICAvLyB4bHI6IFsKICAgICAgICAvLyAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+S4i+S7pOS6uuS4jeiDveS4uuepuicsIHRyaWdnZXI6ICdibHVyJ30KICAgICAgICAvLyBdLAogICAgICAgIGN6eHM6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmk43kvZzpobnmlbDkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgeXp4Y3p4czogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuW3suaJp+ihjOmhueaVsOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICB3enhjenhzOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5pyq5omn6KGM6aG55pWw5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIC8vZm9ybeihqOWNlQogICAgICBmb3JtOiB7CiAgICAgICAgYmg6ICIiLAogICAgICAgIGJkem1jOiAiIiwKICAgICAgICBrc3NqOiAiIiwKICAgICAgICBqc3NqOiAiIiwKICAgICAgICBjenJ3OiAiIiwKICAgICAgICBjenI6ICIiLAogICAgICAgIGpocjogIiIsCiAgICAgICAgeGxyOiAiIiwKICAgICAgICAvLyBzcHI6ICcnLAogICAgICAgIHN0YXR1czogIiIsCiAgICAgICAgbHg6IDQsIC8v5YWJ5LyPCiAgICAgICAgY29sRmlyc3Q6IFtdCiAgICAgIH0sCiAgICAgIC8v5LiK5Lyg5Zu+54mH5pe255qE5pC65bim55qE5YW25LuW5Y+C5pWwCiAgICAgIHVwbG9hZEltZ0RhdGE6IHsKICAgICAgICBidXNpbmVzc0lkOiAiIiAvL+aQuuW4pueahOihqOWNleS4u+mUrmlkCiAgICAgIH0sCiAgICAgIC8v5LiK5Lyg5Zu+54mH5pe255qE6K+35rGC5aS0CiAgICAgIGhlYWRlcjoge30sCiAgICAgIC8v5Zu+54mHbGlzdAogICAgICBpbWdMaXN0OiBbXSwKICAgICAgLy/or6bmg4XlvLnmoYbmmK/lkKbmmL7npLoKICAgICAgaXNTaG93RGV0YWlsczogZmFsc2UsCiAgICAgIC8v5piv5ZCm56aB55SoCiAgICAgIGlzRGlzYWJsZWQ6IGZhbHNlLAogICAgICBjaGVja2VkQWxsOiBmYWxzZSwKICAgICAgLy/moIfpopgKICAgICAgdGl0bGU6ICIiLAogICAgICBmaWx0ZXJJbmZvOiB7CiAgICAgICAgZGF0YTogewogICAgICAgICAgYm06ICIiLAogICAgICAgICAgYmR6bWM6ICIiLAogICAgICAgICAgY3pzakFycjogW10sCiAgICAgICAgICBrc3NqQXJyOiBbXSwKICAgICAgICAgIC8vIGpzc2pBcnI6IFtdLAogICAgICAgICAgY3pydzogIiIsCiAgICAgICAgICBjenI6ICIiLAogICAgICAgICAgamhyOiAiIiwKICAgICAgICAgIHhscm1jOiAiIiwKICAgICAgICAgIHN0YXR1czogIiIKICAgICAgICAgIC8vIHNwcjogJycKICAgICAgICB9LCAvL+afpeivouadoeS7tgogICAgICAgIGZpZWxkTGlzdDogWwogICAgICAgICAgeyBsYWJlbDogIue8luWPtyIsIHZhbHVlOiAiYm0iLCB0eXBlOiAiaW5wdXQiLCBjbGVhcmFibGU6IHRydWUgfSwKCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi5YiG5YWs5Y+4IiwKICAgICAgICAgICAgdmFsdWU6ICJmZ3MiLAogICAgICAgICAgICB0eXBlOiAic2VsZWN0IiwKICAgICAgICAgICAgb3B0aW9uczogW10sCiAgICAgICAgICAgIGNsZWFyYWJsZTogdHJ1ZQogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLlhYnkvI/nlLXnq5nlkI3np7AiLAogICAgICAgICAgICB2YWx1ZTogImJkem1jIiwKICAgICAgICAgICAgdHlwZTogInNlbGVjdCIsCiAgICAgICAgICAgIGNsZWFyYWJsZTogdHJ1ZSwKICAgICAgICAgICAgb3B0aW9uczogW10KICAgICAgICAgIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5pON5L2c5Lu75YqhIiwgdmFsdWU6ICJjenJ3IiwgdHlwZTogImlucHV0IiwgY2xlYXJhYmxlOiB0cnVlIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5pON5L2c5Lq6IiwgdmFsdWU6ICJjenIiLCB0eXBlOiAiaW5wdXQiLCBjbGVhcmFibGU6IHRydWUgfSwKICAgICAgICAgIHsgbGFiZWw6ICLnm5HmiqTkuroiLCB2YWx1ZTogImpocm1jIiwgdHlwZTogImlucHV0IiwgY2xlYXJhYmxlOiB0cnVlIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5LiL5Luk5Lq6IiwgdmFsdWU6ICJ4bHJtYyIsIHR5cGU6ICJpbnB1dCIsIGNsZWFyYWJsZTogdHJ1ZSB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIueKtuaAgSIsCiAgICAgICAgICAgIHZhbHVlOiAic3RhdHVzIiwKICAgICAgICAgICAgdHlwZTogImNoZWNrYm94IiwKICAgICAgICAgICAgY2hlY2tib3hWYWx1ZTogW10sCiAgICAgICAgICAgIGNsZWFyYWJsZTogdHJ1ZSwKICAgICAgICAgICAgb3B0aW9uczogWwogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIHZhbHVlOiAiMCIsCiAgICAgICAgICAgICAgICBsYWJlbDogIuaTjeS9nOelqOWhq+aKpSIKICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIHZhbHVlOiAiMSIsCiAgICAgICAgICAgICAgICBsYWJlbDogIuePree7hOWuoeaguCIKICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIHZhbHVlOiAiMiIsCiAgICAgICAgICAgICAgICBsYWJlbDogIuWIhuWFrOWPuOWuoeaguCIKICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIHZhbHVlOiAiMyIsCiAgICAgICAgICAgICAgICBsYWJlbDogIuaTjeS9nOelqOWKnue7kyIKICAgICAgICAgICAgICB9LHsgbGFiZWw6ICLkvZzlup8iLCB2YWx1ZTogIjciIH0KICAgICAgICAgICAgXQogICAgICAgICAgfQogICAgICAgICAgLy8ge2xhYmVsOiAn5a6h56Wo5Lq6JywgdmFsdWU6ICdzcHInLCB0eXBlOiAnaW5wdXQnLCBjbGVhcmFibGU6IHRydWV9CiAgICAgICAgXQogICAgICB9LAogICAgICB0YWJsZUFuZFBhZ2VJbmZvOiB7CiAgICAgICAgcGFnZXI6IHsKICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgIHNpemVzOiBbMTAsIDIwLCA1MCwgMTAwXQogICAgICAgIH0sCiAgICAgICAgdGFibGVEYXRhOiBbXSwKICAgICAgICB0YWJsZUhlYWRlcjogWwogICAgICAgICAgeyBsYWJlbDogIue8luWPtyIsIHByb3A6ICJibSIsIG1pbldpZHRoOiAiODAiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi54q25oCBIiwgcHJvcDogInN0YXR1c0NuIiwgbWluV2lkdGg6ICI4MCIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLliIblhazlj7giLCBwcm9wOiAiZmdzbWMiLCBtaW5XaWR0aDogIjEwMCIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLlhYnkvI/nlLXnq5nlkI3np7AiLCBwcm9wOiAiZ2Z6bWMiLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLmk43kvZzlvIDlp4vml7bpl7QiLCBwcm9wOiAia3NzaiIsIG1pbldpZHRoOiAiMTEwIiB9LAogICAgICAgICAgeyBsYWJlbDogIuaTjeS9nOe7k+adn+aXtumXtCIsIHByb3A6ICJqc3NqIiwgbWluV2lkdGg6ICIxNDAiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5pON5L2c5Lu75YqhIiwgcHJvcDogImN6cnciLCBtaW5XaWR0aDogIjE2MCIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLmk43kvZzkuroiLCBwcm9wOiAiY3pyIiwgbWluV2lkdGg6ICI2MCIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLnm5HmiqTkuroiLCBwcm9wOiAiamhybWMiLCBtaW5XaWR0aDogIjYwIiB9LAogICAgICAgICAgeyBsYWJlbDogIuS4i+S7pOS6uiIsIHByb3A6ICJ4bHJtYyIsIG1pbldpZHRoOiAiNjAiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5a6h56Wo5Lq6IiwgcHJvcDogImJ6c3BybWMiLCBtaW5XaWR0aDogIjYwIiB9CiAgICAgICAgXSwKICAgICAgICBvcHRpb246IHsgY2hlY2tCb3g6IHRydWUsIHNlcmlhbE51bWJlcjogdHJ1ZSB9CiAgICAgIH0sCiAgICAgIHBhcmFtczogewogICAgICAgIC8v5YWJ5LyPCiAgICAgICAgbHg6IDQsCiAgICAgICAgLy/nlKjmnaXljLrliIbljoblj7LnpajlupPvvIwxLeW3suWKnue7k++8jDIt5pyq5Yqe57uTCiAgICAgICAgc2ZiajogMgogICAgICB9LAogICAgICB4bHJMaXN0OiBbXSwKICAgICAgc3ByTGlzdDogW10KICAgIH07CiAgfSwKICBhc3luYyBtb3VudGVkKCkgewogICAgdGhpcy5nZXRGZ3NPcHRpb25zKCk7CiAgICAvL+iOt+WPluWFieS8j+eUteermeS4i+aLieahhuaVsOaNrgogICAgdGhpcy5nZXRCZHpTZWxlY3RMaXN0KCk7CiAgICAvL+iOt+WPlnRva2VuCiAgICB0aGlzLmhlYWRlci50b2tlbiA9IGdldFRva2VuKCk7CiAgICB0aGlzLnhsckxpc3QgPSBhd2FpdCB0aGlzLmdldEdyb3VwVXNlcnMoMTMyLCAiIik7CiAgICB0aGlzLnNwckxpc3QgPSBhd2FpdCB0aGlzLmdldEdyb3VwVXNlcnMoMTM0LCAiIik7CgogICAgYXdhaXQgdGhpcy5nZXREYXRhKHRoaXMuJHJvdXRlLnF1ZXJ5KTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8v5L2c5bqf56WoCiAgICBudWxsaWZ5R3pwKG9iaklkKSB7CiAgICAgIHRoaXMuJGNvbmZpcm0oIuelqOS9nOW6n+WQjuWPquiDveafpeeci++8jOS4jeiDvei/m+ihjOS7u+S9leaTjeS9nO+8jOehruiupOS9nOW6n+WQlz8iLCAi5o+Q56S6IiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkKICAgICAgICAudGhlbihhc3luYyAoKSA9PiB7CiAgICAgICAgICBsZXQgeyBjb2RlIH0gPSBhd2FpdCB1cGRhdGVCeUlkKHsgc3RhdHVzOiA3LCBvYmpJZDogb2JqSWQgfSk7CiAgICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5pON5L2c5oiQ5YqfISEiKTsKICAgICAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgICAuY2F0Y2goKCkgPT4ge30pOwogICAgfSwKICAgIGV4cG9ydEV4Y2VsKCkgewogICAgICBsZXQgZXhjZWxEYXRhID0gdGhpcy5wcm9wVGFibGVEYXRhLmNvbEZpcnN0Lm1hcChpdGVtID0+ICh7ICLmk43kvZzpobnnm64iOiBpdGVtLmN6cnd9KSk7CiAgICAgIGV4cG9ydFRvRXhjZWwoZXhjZWxEYXRhLCAi5pON5L2c6aG555uuLnhsc3giKTsKICAgIH0sCiAgICBpbXBvcnRFeGNlbChmaWxlLCBmaWxlTGlzdCkgewogICAgICBsZXQgZmlsZU5hbWUgPSBmaWxlLm5hbWUKICAgICAgaWYgKCFmaWxlTmFtZS5pbmNsdWRlcygi5pON5L2c6aG555uuIikpIHsKICAgICAgICB0aGlzLm1zZ0Vycm9yKCLmlofku7bmnInor6/vvIzor7fmo4Dmn6UiKQogICAgICAgIHRoaXMuJHJlZnMudXBsb2FkLmNsZWFyRmlsZXMoKQogICAgICAgIHJldHVybgogICAgICB9CiAgICAgIGltcG9ydEZyb21FeGNlbChmaWxlKQogICAgICAgIC50aGVuKGRhdGEgPT4gewogICAgICAgICAgdGhpcy5pZHMgPSB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QubWFwKGl0ZW0gPT4gaXRlbS5vYmpJZCkKICAgICAgICAgIHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdCA9IGRhdGEubWFwKGl0ZW0gPT4gKHt4aDogaXRlbS5fX3Jvd051bV9fICwgY3pydzogaXRlbVsi5pON5L2c6aG555uuIl19KSk7CiAgICAgICAgfSkKICAgICAgICAuY2F0Y2goZXJyb3IgPT4gewogICAgICAgICAgY29uc29sZS5lcnJvcigi5a+85YWl5aSx6LSlIiwgZXJyb3IpOwogICAgICAgIH0pOwogICAgICB0aGlzLiRyZWZzLnVwbG9hZC5jbGVhckZpbGVzKCkKICAgIH0sCiAgICAvKioKICAgICAqIOiOt+WPluWIhuWFrOWPuOS4i+aLieaVsOaNrgogICAgICovCiAgICBnZXRGZ3NPcHRpb25zKCkgewogICAgICBnZXRGZ3NPcHRpb25zKHt9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgcmVzLmRhdGEuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIGl0ZW0udmFsdWUgPSBpdGVtLnZhbHVlLnRvU3RyaW5nKCk7CiAgICAgICAgfSk7CiAgICAgICAgdGhpcy5vcmdhbml6YXRpb25TZWxlY3RlZExpc3QgPSByZXMuZGF0YTsKICAgICAgICB0aGlzLmZpbHRlckluZm8uZmllbGRMaXN0Lm1hcChpdGVtID0+IHsKICAgICAgICAgIGlmIChpdGVtLnZhbHVlID09ICJmZ3MiKSB7CiAgICAgICAgICAgIHJldHVybiAoaXRlbS5vcHRpb25zID0gdGhpcy5vcmdhbml6YXRpb25TZWxlY3RlZExpc3QpOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICBmaWx0ZXJSZXNldCh2YWwpIHsKICAgICAgKHRoaXMucGFyYW1zID0gewogICAgICAgIC8v5YWJ5LyPCiAgICAgICAgbHg6IDQsCiAgICAgICAgLy/nlKjmnaXljLrliIbljoblj7LnpajlupPvvIwxLeW3suWKnue7k++8jDIt5pyq5Yqe57uTCiAgICAgICAgc2ZiajogMgogICAgICB9KSwKICAgICAgICB0aGlzLmZpbHRlckluZm8uZmllbGRMaXN0LmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICBpZiAoaXRlbS50eXBlID09PSAiY2hlY2tib3giKSB7CiAgICAgICAgICAgIGl0ZW0uY2hlY2tib3hWYWx1ZSA9IFtdOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgfSwKICAgIGdldFNob3coKSB7CiAgICAgIHRoaXMuYnV0dG9uTmFtZVNob3cgPSBmYWxzZTsKICAgICAgc3dpdGNoICh0aGlzLmZvcm0uc3RhdHVzKSB7CiAgICAgICAgY2FzZSAiMCI6CiAgICAgICAgICB0aGlzLmJ1dHRvbk5hbWUgPSAi5LiKIOaKpSI7CiAgICAgICAgICBpZiAodGhpcy5jdXJyZW50VXNlciA9PT0gdGhpcy5mb3JtLmNyZWF0ZUJ5KSB7CiAgICAgICAgICAgIHRoaXMuYnV0dG9uTmFtZVNob3cgPSB0cnVlOwogICAgICAgICAgfQogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSAiMSI6CiAgICAgICAgICB0aGlzLmJ1dHRvbk5hbWUgPSAi5o+QIOS6pCI7CiAgICAgICAgICBpZiAodGhpcy5mb3JtLmJ6c3ByID09PSB0aGlzLmN1cnJlbnRVc2VyKSB7CiAgICAgICAgICAgIHRoaXMuYnV0dG9uTmFtZVNob3cgPSB0cnVlOwogICAgICAgICAgfQogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSAiMiI6CiAgICAgICAgICB0aGlzLmJ1dHRvbk5hbWUgPSAi5o+QIOS6pCI7CiAgICAgICAgICBpZiAodGhpcy5mb3JtLmZnc3NwciA9PT0gdGhpcy5jdXJyZW50VXNlcikgewogICAgICAgICAgICB0aGlzLmJ1dHRvbk5hbWVTaG93ID0gdHJ1ZTsKICAgICAgICAgIH0KICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgIjMiOgogICAgICAgICAgdGhpcy5idXR0b25OYW1lID0gIuWKniDnu5MiOwogICAgICAgICAgaWYgKHRoaXMuZm9ybS5ianIgPT09IHRoaXMuY3VycmVudFVzZXIpIHsKICAgICAgICAgICAgdGhpcy5idXR0b25OYW1lU2hvdyA9IHRydWU7CiAgICAgICAgICAgIHRoaXMuaXNEaXNhYmxlZEJqID0gZmFsc2U7CiAgICAgICAgICB9CiAgICAgICAgICBicmVhazsKICAgICAgfQogICAgfSwKCiAgICBnZXRHcm91cFVzZXJzKHBlcnNvbkdyb3VwSWQsIGRlcHRJZCkgewogICAgICByZXR1cm4gZ2V0VXNlcnMoewogICAgICAgIHBlcnNvbkdyb3VwSWQ6IHBlcnNvbkdyb3VwSWQsCiAgICAgICAgZGVwdElkOiBkZXB0SWQsCiAgICAgICAgZGVwdE5hbWU6ICIiCiAgICAgIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICByZXR1cm4gcmVzLmRhdGE7CiAgICAgIH0pOwogICAgfSwKICAgIC8v6aKE6KeI5paH5Lu2CiAgICBhc3luYyBwcmV2aWV3RmlsZShyb3cpIHsKICAgICAgdHJ5IHsKICAgICAgICBsZXQgZXhwb3J0RGF0YSA9IHsgLi4ucm93IH07CiAgICAgICAgYXdhaXQgcHJldmlld0ZpbGUoZXhwb3J0RGF0YSwgImdmemR6Y3pwIik7CiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLpooTop4jlpLHotKXvvIEiKTsKICAgICAgfQogICAgfSwKICAgIC8v5riF56m6CiAgICBjaGFuZ2UoZSkgewogICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpOwogICAgfSwKICAgIC8v5YWz6Zet5by556qXCiAgICBjbG9zZUFjdGl2aXRpKCkgewogICAgICBpZiAodGhpcy4kcmVmcy5hY3Rpdml0aS4kcmVmcy5mb3JtKSB7CiAgICAgICAgdGhpcy4kcmVmcy5hY3Rpdml0aS4kcmVmcy5mb3JtLnJlc2V0RmllbGRzKCk7CiAgICAgIH0KICAgICAgdGhpcy5pc1Nob3cgPSBmYWxzZTsKICAgIH0sCiAgICAvL+WFs+mXrea1geeoi+afpeeci+mhtemdogogICAgY29sc2VUaW1lTGluZSgpIHsKICAgICAgdGhpcy50aW1lTGluZVNob3cgPSBmYWxzZTsKICAgIH0sCiAgICBhc3luYyBzaG93VGltZUxpbmUocm93KSB7CiAgICAgIHRoaXMucHJvY2Vzc0RhdGEuYnVzaW5lc3NLZXkgPSByb3cub2JqSWQ7CiAgICAgIGxldCB7IGNvZGUsIGRhdGEgfSA9IGF3YWl0IEhpc3RvcnlMaXN0KHRoaXMucHJvY2Vzc0RhdGEpOwogICAgICB0aGlzLnRpbWVEYXRhID0gZGF0YTsKICAgICAgdGhpcy50aW1lTGluZVNob3cgPSB0cnVlOwogICAgfSwKICAgIC8v5rWB56iL5Zu+54mH5p+l55yLCiAgICBzaG93UHJvY2Vzc0ltZyhyb3cpIHsKICAgICAgdGhpcy5vcGVuTG9hZGluZ0ltZyA9IHRydWU7CiAgICAgIHRoaXMuaW1nU3JjID0KICAgICAgICAiL2FjdGl2aXRpLWFwaS9wcm9jZXNzL3JlYWQtcmVzb3VyY2U/cHJvY2Vzc0RlZmluaXRpb25LZXk9Y3pwc2gmYnVzaW5lc3NLZXk9IiArCiAgICAgICAgcm93Lm9iaklkICsKICAgICAgICAiJnQ9IiArCiAgICAgICAgbmV3IERhdGUoKS5nZXRUaW1lKCk7CiAgICB9LAogICAgLy/lt6XkvZzmtYHlm57kvKDmlbDmja4KICAgIGFzeW5jIHRvZG9SZXN1bHQoZGF0YSkgewogICAgICBsZXQgcm93ID0gewogICAgICAgIG9iaklkOiBkYXRhLmJ1c2luZXNzS2V5LAogICAgICAgIGlzU3RhcnQ6IDEsCiAgICAgICAgaXNCYWNrOiBkYXRhLnByb2Nlc3NUeXBlID09PSAicm9sbGJhY2siID8gMSA6IDAKICAgICAgfTsKICAgICAgaWYgKGRhdGEucHJvY2Vzc1R5cGUgPT09ICJyb2xsYmFjayIpIHsKICAgICAgICBzd2l0Y2ggKGRhdGEuYWN0aXZlVGFza05hbWUpIHsKICAgICAgICAgIGNhc2UgIuaTjeS9nOelqOWhq+aKpSI6CiAgICAgICAgICAgIHJvdy5zdGF0dXMgPSAiMCI7CiAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgY2FzZSAi54+t57uE5a6h5qC4IjoKICAgICAgICAgICAgcm93LnN0YXR1cyA9ICIxIjsKICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIHN3aXRjaCAoZGF0YS5hY3RpdmVUYXNrTmFtZSkgewogICAgICAgICAgY2FzZSAi54+t57uE5a6h5qC4IjoKICAgICAgICAgICAgcm93LnN0YXR1cyA9ICIxIjsKICAgICAgICAgICAgcm93LmJ6c3ByID0gZGF0YS5uZXh0VXNlcjsKICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICBjYXNlICLliIblhazlj7jlrqHmoLgiOgogICAgICAgICAgICByb3cuc3RhdHVzID0gIjIiOwogICAgICAgICAgICByb3cuZmdzc3ByID0gZGF0YS5uZXh0VXNlcjsKICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICBjYXNlICLmk43kvZznpajlip7nu5MiOgogICAgICAgICAgICByb3cuc3RhdHVzID0gIjMiOwogICAgICAgICAgICByb3cuYmpyID0gZGF0YS5uZXh0VXNlcjsKICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICBjYXNlICLnu5PmnZ8iOgogICAgICAgICAgICByb3cuc3RhdHVzID0gIjQiOwogICAgICAgICAgICBicmVhazsKICAgICAgICB9CiAgICAgIH0KICAgICAgbGV0IHsgY29kZSB9ID0gYXdhaXQgdXBkYXRlQnlJZChyb3cpOwogICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgYXdhaXQgdGhpcy5nZXREYXRhKCk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLmk43kvZzlpLHotKUiKTsKICAgICAgfQogICAgfSwKICAgIC8v5LiK5oql5Y+R6YCB5Yqe57uTCiAgICBhc3luYyBnZXRTYkZzQmoodHlwZSkgewogICAgICBsZXQgcm93ID0geyAuLi50aGlzLmZvcm0gfTsKICAgICAgaWYgKHR5cGUgPT09ICJjb21wbGV0ZSIpIHsKICAgICAgICBzd2l0Y2ggKHJvdy5zdGF0dXMpIHsKICAgICAgICAgIGNhc2UgIjAiOgogICAgICAgICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSBmYWxzZTsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS52YXJpYWJsZXMucGFzcyA9IHRydWU7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuYnVzaW5lc3NLZXkgPSByb3cub2JqSWQ7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEucHJvY2Vzc1R5cGUgPSB0eXBlOwogICAgICAgICAgICB0aGlzLmFjdGl2aXRpT3B0aW9uLnRpdGxlID0gIuaPkOS6pCI7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuZGVmYXVsdEZyb20gPSB0cnVlOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnJ5bHggPSAi54+t57uE5a6h5qC45Lq6IjsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5kdyA9IHJvdy5mZ3M7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEucGVyc29uR3JvdXBJZCA9IDEzNDsKICAgICAgICAgICAgdGhpcy5pc1Nob3cgPSB0cnVlOwogICAgICAgICAgICBicmVhazsKICAgICAgICAgIGNhc2UgIjEiOgogICAgICAgICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSBmYWxzZTsKICAgICAgICAgICAgdGhpcy4kY29uZmlybSgi5piv5ZCm6ZyA6KaB5o+Q5Lqk5YiG5YWs5Y+45a6h5qC4PyIsICLpgInmi6kiLCB7CiAgICAgICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLmmK8iLAogICAgICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlkKYiLAogICAgICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICAgICAgICB9KQogICAgICAgICAgICAgIC50aGVuKGFzeW5jICgpID0+IHsKICAgICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnBhc3MgPSB0cnVlOwogICAgICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5idXNpbmVzc0tleSA9IHJvdy5vYmpJZDsKICAgICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEucHJvY2Vzc1R5cGUgPSB0eXBlOwogICAgICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5kdyA9IHJvdy5mZ3M7CiAgICAgICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnJ5bHggPSAi5YiG5YWs5Y+45a6h5qC45Lq6IjsKICAgICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLmlzRmdzID0gdHJ1ZTsKICAgICAgICAgICAgICAgIHRoaXMuYWN0aXZpdGlPcHRpb24udGl0bGUgPSAi5o+Q5LqkIjsKICAgICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuZGVmYXVsdEZyb20gPSB0cnVlOwogICAgICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5wZXJzb25Hcm91cElkID0gMTM1OwogICAgICAgICAgICAgICAgdGhpcy5pc1Nob3cgPSB0cnVlOwogICAgICAgICAgICAgIH0pCiAgICAgICAgICAgICAgLmNhdGNoKCgpID0+IHsKICAgICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnBhc3MgPSB0cnVlOwogICAgICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5idXNpbmVzc0tleSA9IHJvdy5vYmpJZDsKICAgICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEucHJvY2Vzc1R5cGUgPSB0eXBlOwogICAgICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5kdyA9IHJvdy5mZ3M7CiAgICAgICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnJ5bHggPSAi5Yqe57uT5Lq6IjsKICAgICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLmlzRmdzID0gZmFsc2U7CiAgICAgICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmRlZmF1bHRGcm9tID0gdHJ1ZTsKICAgICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEucGVyc29uR3JvdXBJZCA9IDEzNjsKICAgICAgICAgICAgICAgIHRoaXMuaXNTaG93ID0gdHJ1ZTsKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICBjYXNlICIyIjoKICAgICAgICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gZmFsc2U7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnBhc3MgPSB0cnVlOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmJ1c2luZXNzS2V5ID0gcm93Lm9iaklkOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnByb2Nlc3NUeXBlID0gdHlwZTsKICAgICAgICAgICAgdGhpcy5hY3Rpdml0aU9wdGlvbi50aXRsZSA9ICLmj5DkuqQiOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmRlZmF1bHRGcm9tID0gdHJ1ZTsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5wZXJzb25Hcm91cElkID0gMTM2OwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmR3ID0gcm93LmZnczsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5yeWx4ID0gIuWKnue7k+S6uiI7CiAgICAgICAgICAgIHRoaXMuaXNTaG93ID0gdHJ1ZTsKICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICBjYXNlICIzIjoKICAgICAgICAgICAgYXdhaXQgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKGFzeW5jIHZhbGlkID0+IHsKICAgICAgICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgICAgICAgIHRoaXMuZm9ybS5jb2xGaXJzdCA9IHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdDsKICAgICAgICAgICAgICAgICAgdGhpcy5mb3JtLm9iaklkTGlzdCA9IHRoaXMuaWRzOwogICAgICAgICAgICAgICAgICB0aGlzLnVwbG9hZEltZ0RhdGEuYnVzaW5lc3NJZCA9IHRoaXMuZm9ybS5vYmpJZDsKICAgICAgICAgICAgICAgICAgdGhpcy51cGxvYWRGb3JtKCk7CiAgICAgICAgICAgICAgICAgIGF3YWl0IHNhdmVPclVwZGF0ZSh0aGlzLmZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgICAgICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gZmFsc2U7CiAgICAgICAgICAgICAgICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICAgICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnBhc3MgPSB0cnVlOwogICAgICAgICAgICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5idXNpbmVzc0tleSA9IHJvdy5vYmpJZDsKICAgICAgICAgICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEucHJvY2Vzc1R5cGUgPSB0eXBlOwogICAgICAgICAgICAgICAgICAgICAgdGhpcy5hY3Rpdml0aU9wdGlvbi50aXRsZSA9ICLlip7nu5MiOwogICAgICAgICAgICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5kZWZhdWx0RnJvbSA9IGZhbHNlOwogICAgICAgICAgICAgICAgICAgICAgdGhpcy5pc1Nob3cgPSB0cnVlOwogICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLlpLHotKUiKTsKICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgfSBjYXRjaCAoZSkge30KICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSBmYWxzZTsKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmJ1c2luZXNzS2V5ID0gcm93Lm9iaklkOwogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEucHJvY2Vzc1R5cGUgPSB0eXBlOwogICAgICAgIHRoaXMuYWN0aXZpdGlPcHRpb24udGl0bGUgPSAi5Zue6YCA5Y6f5Zug5aGr5YaZIjsKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmRlZmF1bHRGcm9tID0gdHJ1ZTsKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnZhcmlhYmxlcy5wYXNzID0gZmFsc2U7CiAgICAgICAgdGhpcy5pc1Nob3cgPSB0cnVlOwogICAgICB9CiAgICB9LAoKICAgIC8v5pS56YCg5ZCO55qE5LiK5Lyg5aSa5Liq5Zu+54mH5paH5Lu2CiAgICB1cGxvYWRGb3JtKCkgewogICAgICB2YXIgbmV3VXJsID0gW107IC8v55So5p2l5a2Y5pS+5b2T5YmN5pyq5pS55Yqo6L+H55qE5Zu+54mHdXJsLOatpOWbvueJh+S4jei/m+ihjOWIoOmZpOWkhOeQhu+8jOWFtuS9meW9k+WJjeS4muWKoWlk5LiL6Z2i55qE5Zu+54mH5pWw5o2u5bCG6L+b6KGM5Yig6ZmkCiAgICAgIHZhciBpbWFnZVR5cGUgPSBbInBuZyIsICJqcGciXTsKICAgICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKTsKICAgICAgLy8g5Zug5Li66KaB5Lyg5LiA5Liq5paH5Lu25pWw57uE6L+H5Y6777yM5omA5Lul6KaB5b6q546vYXBwZW5kCiAgICAgIHRoaXMuaW1nTGlzdC5mb3JFYWNoKGZpbGUgPT4gewogICAgICAgIGlmIChmaWxlLnJhdyA9PSB1bmRlZmluZWQpIHsKICAgICAgICAgIG5ld1VybC5wdXNoKGZpbGUudXJsKTsKICAgICAgICB9CiAgICAgICAgZm9ybURhdGEuYXBwZW5kKCJmaWxlcyIsIGZpbGUucmF3KTsKICAgICAgfSk7CiAgICAgIGZvcm1EYXRhLmFwcGVuZCgiYnVzaW5lc3NJZCIsIHRoaXMudXBsb2FkSW1nRGF0YS5idXNpbmVzc0lkKTsgLy8g6Ieq5a6a5LmJ5Y+C5pWwCiAgICAgIGZvcm1EYXRhLmFwcGVuZCgibmV3VXJsIiwgbmV3VXJsKTsgLy8g5pyq5pS55Yqo6L+H55qE5Zu+54mHdXJsCiAgICAgIGZvcm1EYXRhLmFwcGVuZCgidHlwZSIsIGltYWdlVHlwZSk7IC8vIOacquaUueWKqOi/h+eahOWbvueJh3VybAogICAgICBhcGkKICAgICAgICAucmVxdWVzdFBvc3QoIi9pc2MtYXBpL2ZpbGUvdXBsb2FkRmlsZXMiLCBmb3JtRGF0YSwgMSkKICAgICAgICAudGhlbihyZXMgPT4gewogICAgICAgICAgLy8g5riF56m65Zu+54mH5YiX6KGo77yI5LiA5a6a6KaB5riF56m677yM5ZCm5YiZ5LiK5Lyg5oiQ5Yqf5ZCO6L+Y5piv5Lya6LCD55SoaGFuZGxlQ2hhbmdl77yI77yJ5Ye95pWw77yM5LiK5Lyg5oiQ5Yqf5ZCO5YiX6KGo5Lit6L+Y5a2Y5Zyo5Zu+54mH77yJCiAgICAgICAgICB0aGlzLmltZ0xpc3QgPSBbXTsKICAgICAgICAgIC8v5p+l6K+i5o6l5Y+j5pWw5o2uCiAgICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICB9KQogICAgICAgIC5jYXRjaChyZXMgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLlm77niYfkuIrkvKDlpLHotKXvvIEiKTsKICAgICAgICB9KTsKICAgIH0sCiAgICAvLyDpgInmi6nmlofku7bml7bvvIzlvoBmaWxlTGlzdOmHjOa3u+WKoAogICAgaGFuZGxlQ2hhbmdlKGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgIHRoaXMuaW1nTGlzdCA9IGZpbGVMaXN0OwogICAgfSwKICAgIGhhbmRsZVByb2dyZXNzKGV2ZW50LCBmaWxlLCBmaWxlTGlzdCkge30sCiAgICAvL+WbvueJh+enu+mZpAogICAgaGFuZGxlUmVtb3ZlKGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgIHRoaXMuaW1nTGlzdCA9IGZpbGVMaXN0OwogICAgfSwKICAgIC8v5Zu+54mH5pS+5aSnCiAgICBoYW5kbGVQaWN0dXJlQ2FyZFByZXZpZXcoZmlsZSkgewogICAgICB0aGlzLmRpYWxvZ0ltYWdlVXJsID0gW2ZpbGUudXJsXTsKICAgICAgdGhpcy5pbWdEaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICBvbkNsb3NlKCkgewogICAgICB0aGlzLmltZ0RpYWxvZ1Zpc2libGUgPSBmYWxzZTsKICAgIH0sCiAgICAvLyDlhajpgInmoYYKICAgIGhhbmRsZUNoZWNrQWxsQ2hhbmdlKHZhbCkgewogICAgICBpZiAodmFsKSB7CiAgICAgICAgdGhpcy5mb3JtLnl6eGN6eHMgPSB0aGlzLmZvcm0uY3p4czsKICAgICAgICB0aGlzLmZvcm0ud3p4Y3p4cyA9IDA7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5mb3JtLnd6eGN6eHMgPSB0aGlzLmZvcm0uY3p4czsKICAgICAgICB0aGlzLmZvcm0ueXp4Y3p4cyA9IDA7CiAgICAgIH0KICAgICAgdGhpcy5wcm9wVGFibGVEYXRhLmNvbEZpcnN0LmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgaXRlbS5zZndjID0gdmFsOwogICAgICB9KTsKICAgIH0sCiAgICAvL+mAieaLqeW3suaJp+ihjOaXtu+8jOaTjeS9nOmhueebrum7mOiupOm7mOiupOWFqOmAiQogICAgaGFuZGxlQ2hhbmdlT2ZTZnp4KHZhbCkgewogICAgICBpZiAodmFsID09PSAi5bey5omn6KGMIikgewogICAgICAgIHRoaXMuZm9ybS55enhjenhzID0gdGhpcy5mb3JtLmN6eHM7CiAgICAgICAgdGhpcy5mb3JtLnd6eGN6eHMgPSAwOwogICAgICAgIHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdC5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgaXRlbS5zZndjID0gdHJ1ZTsKICAgICAgICB9KTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmZvcm0ud3p4Y3p4cyA9IHRoaXMuZm9ybS5jenhzOwogICAgICAgIHRoaXMuZm9ybS55enhjenhzID0gMDsKICAgICAgICB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIGl0ZW0uc2Z3YyA9IGZhbHNlOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgaGFuZGxlQ2hlY2tDaGFuZ2UodmFsKSB7CiAgICAgIGlmICh2YWwpIHsKICAgICAgICArK3RoaXMuZm9ybS55enhjenhzOwogICAgICB9IGVsc2UgewogICAgICAgIC0tdGhpcy5mb3JtLnl6eGN6eHM7CiAgICAgIH0KICAgICAgdGhpcy5mb3JtLnd6eGN6eHMgPSB0aGlzLmZvcm0uY3p4cyAtIHRoaXMuZm9ybS55enhjenhzOwogICAgfSwKICAgIC8vIOmihOiniOaMiemSrgogICAgaGFuZGxlWWxDaGFuZ2UoKSB7CiAgICAgIHRoaXMudGl0bGV5bCA9ICLmn6XnnIvmk43kvZzpobnnm64iOwogICAgICB0aGlzLnlsID0gdHJ1ZTsKICAgIH0sCiAgICAvL+WIl+ihqOafpeivogogICAgYXN5bmMgZ2V0RGF0YShwYXJhbXMpIHsKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCBwYXJhbSA9IHsgLi4udGhpcy5wYXJhbXMsIC4uLnBhcmFtcyB9OwogICAgICAgIHRoaXMucGFyYW1zID0gcGFyYW07CiAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgICBpZiAoIXBhcmFtLnN0YXR1cykgewogICAgICAgICAgcGFyYW0uc3RhdHVzID0gIjAsMSwyLDMsNCI7CiAgICAgICAgfQogICAgICAgIHBhcmFtLm15U29ydHMgPSBbeyBwcm9wOiAidXBkYXRlVGltZSIsIGFzYzogZmFsc2UgfV07CiAgICAgICAgY29uc3QgeyBkYXRhLCBjb2RlIH0gPSBhd2FpdCBnZXRMaXN0THNwKHBhcmFtKTsKICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICBmb3IgKGxldCBpIG9mIGRhdGEucmVjb3JkcykgewogICAgICAgICAgICBpLmZnc21jID0gdGhpcy5mb3JtYXRTc2dzKGkuZmdzKTsKICAgICAgICAgICAgdGhpcy5zdGF0dXNPcHRpb25zLmZvckVhY2goZWxlbWVudCA9PiB7CiAgICAgICAgICAgICAgaWYgKGkuc3RhdHVzID09PSBlbGVtZW50LnZhbHVlKSB7CiAgICAgICAgICAgICAgICBpLnN0YXR1c0NuID0gZWxlbWVudC5sYWJlbDsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnRhYmxlRGF0YSA9IGRhdGEucmVjb3JkczsKICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci50b3RhbCA9IGRhdGEudG90YWw7CiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICBjb25zb2xlLmxvZyhlKTsKICAgICAgfQogICAgfSwKICAgIC8qKgogICAgICrojrflj5bmk43kvZznpajmmI7nu4YKICAgICAqLwogICAgYXN5bmMgZ2V0Q3pwbXgocm93KSB7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgeyBkYXRhLCBjb2RlIH0gPSBhd2FpdCBnZXRDenBteExpc3QoeyBvYmpJZDogcm93Lm9iaklkIH0pOwogICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdCA9IGRhdGE7CiAgICAgICAgICB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QuZm9yRWFjaChlID0+IHsKICAgICAgICAgICAgZS51dWlkID0gZ2V0VVVJRCgpOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgY29uc29sZS5sb2coZSk7CiAgICAgIH0KICAgIH0sCgogICAgLy/kv67mlLnmjInpkq4KICAgIGFzeW5jIGdldFVwZGF0ZShyb3cpIHsKICAgICAgYXdhaXQgdGhpcy5nZXRDenBteChyb3cpOwogICAgICB0aGlzLnRpdGxlID0gIuWFieS8j+eUteWAkumXuOaTjeS9nOelqOS/ruaUuSI7CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICB0aGlzLmlzRGlzYWJsZWRCaiA9IGZhbHNlOwogICAgICB0aGlzLmZvcm0gPSB7IC4uLnJvdyB9OwogICAgICAvL+WbvueJh+e7k+WQiAogICAgICB0aGlzLmltZ0xpc3QgPSB0aGlzLmZvcm0uaW1nTGlzdDsKICAgICAgdGhpcy5qbHJMaXN0ID0gYXdhaXQgdGhpcy5nZXRHcm91cFVzZXJzKDEzMywgIiIpOwogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlOwogICAgICB0aGlzLmdldFNob3coKTsKICAgIH0sCiAgICAvL+ivpuaDheaMiemSrgogICAgYXN5bmMgZ2V0RGV0YWlscyhyb3cpIHsKICAgICAgYXdhaXQgdGhpcy5nZXRDenBteChyb3cpOwogICAgICB0aGlzLnRpdGxlID0gIuWFieS8j+eUteaTjeS9nOelqOivpuaDheafpeeciyI7CiAgICAgIHRoaXMuZm9ybSA9IHsgLi4ucm93IH07CiAgICAgIHRoaXMuamxyTGlzdCA9IGF3YWl0IHRoaXMuZ2V0R3JvdXBVc2VycygxMzMsICIiKTsKICAgICAgLy/lm77niYfnu5PlkIgKICAgICAgdGhpcy5pbWdMaXN0ID0gdGhpcy5mb3JtLmltZ0xpc3Q7CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IHRydWU7CiAgICAgIHRoaXMuaXNEaXNhYmxlZEJqID0gdHJ1ZTsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gdHJ1ZTsKICAgICAgdGhpcy5nZXRTaG93KCk7CiAgICB9LAogICAgLy8g5L+d5a2Y5oyJ6ZKuCiAgICBhc3luYyBzYXZlUm93KCkgewogICAgICBhd2FpdCB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgdGhpcy5mb3JtLmNvbEZpcnN0ID0gdGhpcy5wcm9wVGFibGVEYXRhLmNvbEZpcnN0OwogICAgICAgICAgICB0aGlzLmZvcm0ub2JqSWRMaXN0ID0gdGhpcy5pZHM7CiAgICAgICAgICAgIHNhdmVPclVwZGF0ZSh0aGlzLmZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLmk43kvZzmiJDlip8iKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgLy/ph43nva5wYWdl6aG15LuOMeW8gOWniwogICAgICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci5wYWdlUmVzaXplID0gIlkiOwogICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IGZhbHNlOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0gY2F0Y2ggKGUpIHt9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuagoemqjOacqumAmui/h++8gSIpOwogICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAoKICAgIC8v5Yig6Zmk5oyJ6ZKuCiAgICBhc3luYyBkZWxldGVSb3cocm93KSB7CiAgICAgIGxldCBvYmpJZCA9ICIiOwogICAgICBpZiAocm93Lm9iaklkKSB7CiAgICAgICAgb2JqSWQgPSByb3cub2JqSWQ7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgb2JqSWQgPSB0aGlzLmlkc1swXTsKICAgICAgfQogICAgICB0aGlzLiRjb25maXJtKCLmraTmk43kvZzlsIbmsLjkuYXliKDpmaTor6XmlbDmja4sIOaYr+WQpue7p+e7rT8iLCAi5o+Q56S6IiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkKICAgICAgICAudGhlbigoKSA9PiB7CiAgICAgICAgICByZW1vdmUob2JqSWQpLnRoZW4oKHsgY29kZSB9KSA9PiB7CiAgICAgICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTmiJDlip8hIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTlpLHotKUhIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoKSA9PiB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogImluZm8iLAogICAgICAgICAgICBtZXNzYWdlOiAi5bey5Y+W5raI5Yig6ZmkIgogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICB9LAogICAgLy/ooajmoLzmlrDlop4KICAgIGxpc3RGaXJzdEFkZCgpIHsKICAgICAgbGV0IHJvdyA9IHsKICAgICAgICB4aDogIiIsCiAgICAgICAgY3pydzogIiIsCiAgICAgICAgc2Z3YzogIiIsCiAgICAgICAgdXVpZDogZ2V0VVVJRCgpCiAgICAgIH07CiAgICAgIHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdC5wdXNoKHJvdyk7CiAgICAgIHRoaXMucHJvcFRhYmxlRGF0YS5zZWwgPSByb3c7CiAgICAgIHRoaXMuZm9ybS5jenhzID0gdGhpcy5wcm9wVGFibGVEYXRhLmNvbEZpcnN0Lmxlbmd0aDsKICAgICAgdGhpcy5mb3JtLnd6eGN6eHMgPSB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QubGVuZ3RoOwogICAgfSwKICAgIC8v6KGo5qC85Yig6ZmkCiAgICBsaXN0Rmlyc3REZWwocm93KSB7CiAgICAgIGlmIChyb3cub2JqSWQpIHsKICAgICAgICB0aGlzLmlkcy5wdXNoKHJvdy5vYmpJZCk7CiAgICAgIH0KICAgICAgdGhpcy5wcm9wVGFibGVEYXRhLmNvbEZpcnN0ID0gdGhpcy5wcm9wVGFibGVEYXRhLmNvbEZpcnN0LmZpbHRlcigKICAgICAgICBpdGVtID0+IGl0ZW0udXVpZCAhPT0gcm93LnV1aWQKICAgICAgKTsKICAgICAgdGhpcy5mb3JtLmN6eHMgPSB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QubGVuZ3RoOwogICAgICB0aGlzLmZvcm0ud3p4Y3p4cyA9IHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdC5sZW5ndGg7CiAgICB9LAoKICAgIC8v5YWz6Zet5by556qXCiAgICBjbG9zZSgpIHsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gZmFsc2U7CiAgICB9LAogICAgc2VsZWN0Q2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLm9iaklkKTsKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOwogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7CiAgICAgIHRoaXMuc2VsZWN0RGF0YSA9IHNlbGVjdGlvbjsKICAgIH0sCiAgICAvKioKICAgICAqIOiOt+WPluWFieS8j+eUteermeS4i+aLieahhuaVsOaNrgogICAgICovCiAgICBnZXRCZHpTZWxlY3RMaXN0KCkgewogICAgICBnZXRCZHpEYXRhTGlzdFNlbGVjdGVkKHt9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5iZHpMaXN0ID0gcmVzLmRhdGE7CiAgICAgICAgdGhpcy5maWx0ZXJJbmZvLmZpZWxkTGlzdC5tYXAoaXRlbSA9PiB7CiAgICAgICAgICBpZiAoaXRlbS52YWx1ZSA9PSAiYmR6bWMiKSB7CiAgICAgICAgICAgIHJldHVybiAoaXRlbS5vcHRpb25zID0gdGhpcy5iZHpMaXN0KTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgLy/lr7zlh7p3b3JkCiAgICBhc3luYyBleHBvcnRXb3JkKHJvdykgewogICAgICB0cnkgewogICAgICAgIGxldCBleHBvcnREYXRhID0geyAuLi5yb3cgfTsKICAgICAgICBhd2FpdCBleHBvcnRXb3JkKGV4cG9ydERhdGEsICJnZnpkemN6cCIsICLlhYnkvI/nlLXnq5nlgJLpl7jmk43kvZznpaguZG9jeCIpOwogICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5a+85Ye65aSx6LSl77yBIik7CiAgICAgIH0KICAgIH0sCiAgICAvL+WvvOWHulBkZgogICAgYXN5bmMgZXhwb3J0UGRmKHJvdykgewogICAgICB0cnkgewogICAgICAgIGxldCBleHBvcnREYXRhID0geyAuLi5yb3cgfTsKICAgICAgICBhd2FpdCBleHBvcnRQZGYoZXhwb3J0RGF0YSwgImdmemR6Y3pwIiwgIuWFieS8j+eUteermeWAkumXuOaTjeS9nOelqC5wZGYiKTsKICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuWvvOWHuuWksei0pe+8gSIpOwogICAgICB9CiAgICB9LAogICAgLy/kuIvmi4nmoYZjaGFuZ2Xkuovku7YKICAgIGhhbmRsZUV2ZW50KHZhbCwgZXZlbnRWYWx1ZSkgewogICAgICAvLyBpZiAodmFsLmxhYmVsID09PSAiZmdzIikgewogICAgICAvLyAgIGdldEJkekRhdGFMaXN0U2VsZWN0ZWQoeyBzc2R3Ym06IHZhbC52YWx1ZS50b1N0cmluZygpIH0pLnRoZW4ocmVzID0+IHsKICAgICAgLy8gICAgIHRoaXMuZmlsdGVySW5mby5maWVsZExpc3QubWFwKGl0ZW0gPT4gewogICAgICAvLyAgICAgICBpZiAoaXRlbS52YWx1ZSA9PT0gImJkem1jIikgewogICAgICAvLyAgICAgICAgIHRoaXMuJHNldChldmVudFZhbHVlLCAiYmR6bWMiLCAiIik7CiAgICAgIC8vICAgICAgICAgcmV0dXJuIChpdGVtLm9wdGlvbnMgPSByZXMuZGF0YSk7CiAgICAgIC8vICAgICAgIH0KICAgICAgLy8gICAgIH0pOwogICAgICAvLyAgIH0pOwogICAgICAvLyB9CiAgICB9LAogICAgZm9ybWF0U3Nncyhzc2dzKSB7CiAgICAgIGxldCBwYWdlT3JnYW5pemF0aW9uU2VsZWN0ZWRMaXN0ID0gSlNPTi5wYXJzZSgKICAgICAgICBKU09OLnN0cmluZ2lmeSh0aGlzLm9yZ2FuaXphdGlvblNlbGVjdGVkTGlzdCkKICAgICAgKTsKICAgICAgcGFnZU9yZ2FuaXphdGlvblNlbGVjdGVkTGlzdC5wdXNoKHsKICAgICAgICBsYWJlbDogIua4r+S4nOWFieS8j+eUteWIhuWFrOWPuCIsCiAgICAgICAgdmFsdWU6ICIzMDAyIgogICAgICB9KTsKICAgICAgcGFnZU9yZ2FuaXphdGlvblNlbGVjdGVkTGlzdC5wdXNoKHsKICAgICAgICBsYWJlbDogIua4r+S4reWFieS8j+eUteWIhuWFrOWPuCIsCiAgICAgICAgdmFsdWU6ICIzMDAzIgogICAgICB9KTsKICAgICAgaWYgKHNzZ3MpIHsKICAgICAgICBsZXQgZmlsdGVyID0gcGFnZU9yZ2FuaXphdGlvblNlbGVjdGVkTGlzdC5maWx0ZXIoZyA9PiBnLnZhbHVlID09PSBzc2dzKTsKICAgICAgICBpZiAoZmlsdGVyLmxlbmd0aCA+IDApIHsKICAgICAgICAgIHJldHVybiBmaWx0ZXJbMF0ubGFiZWw7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHJldHVybiAiIjsKICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuICIiOwogICAgICB9CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["dzczp.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsp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file": "dzczp.vue", "sourceRoot": "src/views/dagangOilfield/czpgl/gfdzcz", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n      @handleEvent=\"handleEvent\"\n      @handleReset=\"filterReset\"\n    />\n\n    <div>\n      <el-white class=\"button-group\">\n        <div class=\"button_btn\">\n          <el-button\n            type=\"danger\"\n            icon=\"el-icon-delete\"\n            v-if=\"hasSuperRole\"\n            @click=\"deleteRow\"\n            :disabled=\"single\"\n            >删除</el-button\n          >\n        </div>\n        <!-- <el-table-column\n          prop=\"statusCn\"\n          slot=\"table_start\"\n          align=\"center\"\n          style=\"display: block\"\n          label=\"流程状态\"\n          min-width=\"120\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-badge\n              v-if=\"scope.row.isBack === 1\"\n              value=\"退回\"\n              class=\"item\"\n              type=\"danger\"\n            >\n            </el-badge>\n            <span>{{ scope.row.statusCn }}</span>\n          </template>\n        </el-table-column> -->\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"selectChange\"\n          height=\"69vh\"\n          v-loading=\"loading\"\n        >\n          <el-table-column\n            slot=\"table_eight\"\n            align=\"center\"\n            fixed=\"right\"\n            style=\"display: block\"\n            label=\"操作\"\n            width=\"160\"\n            :resizable=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                @click=\"getDetails(scope.row)\"\n                type=\"text\"\n                size=\"small\"\n                title=\"详情\"\n                icon=\"el-icon-view\"\n              />\n              <el-button\n                @click=\"getUpdate(scope.row)\"\n                v-if=\"\n                  (scope.row.status === '0' &&\n                    scope.row.createBy === currentUser) ||\n                    hasSuperRole\n                \"\n                type=\"text\"\n                size=\"small\"\n                title=\"编辑\"\n                icon=\"el-icon-edit\"\n              />\n              <el-button\n                @click=\"deleteRow(scope.row)\"\n                v-if=\"\n                  scope.row.status === '0' && scope.row.createBy === currentUser\n                \"\n                type=\"text\"\n                size=\"small\"\n                title=\"删除\"\n                icon=\"el-icon-delete\"\n              />\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                @click=\"nullifyGzp(scope.row.objId)\"\n                v-if=\"scope.row.status < 4 && scope.row.status > 0 &&\n                ( scope.row.createBy === currentUser || (scope.row.bzspr && scope.row.bzspr === currentUser) \n                || (scope.row.fgsspr && scope.row.fgsspr === currentUser) \n                || (scope.row.bjr && scope.row.bjr === currentUser)\n                || hasSuperRole )\n                \"\n                title=\"作废\"\n                class=\"el-icon-close\"\n              >\n              </el-button>\n              <el-button\n                @click=\"showTimeLine(scope.row)\"\n                v-if=\"scope.row.isStart === 1\"\n                type=\"text\"\n                size=\"small\"\n                title=\"流程查看\"\n                icon=\"el-icon-lcck commonIcon\"\n              />\n              <el-button\n                @click=\"showProcessImg(scope.row)\"\n                v-if=\"scope.row.isStart === 1\"\n                type=\"text\"\n                size=\"small\"\n                title=\"流程图\"\n                icon=\"el-icon-lct commonIcon\"\n              />\n              <!--              <el-button @click=\"exportWord(scope.row)\" v-if=\"scope.row.status == '待办结'\" type=\"text\" size=\"small\">\n                              导出Word\n                            </el-button>-->\n              <el-button\n                @click=\"previewFile(scope.row)\"\n                v-if=\"scope.row.status > 2\"\n                type=\"text\"\n                size=\"small\"\n                title=\"预览\"\n                class=\"el-icon-zoom-in\"\n              />\n              <el-button\n                @click=\"exportPdf(scope.row)\"\n                v-if=\"scope.row.status > 2\"\n                type=\"text\"\n                size=\"small\"\n                title=\"导出pdf\"\n                icon=\"el-icon-pdf-export commonIcon\"\n              />\n            </template>\n          </el-table-column>\n        </comp-table>\n      </el-white>\n    </div>\n    <!-- 详情/新增/修改 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      v-if=\"isShowDetails\"\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <div>\n          <el-row>\n            <el-col :span=\"8\">\n              <el-form-item label=\"编号：\" prop=\"bm\">\n                <el-input\n                  v-model=\"form.bm\"\n                  disabled\n                  placeholder=\"确认后编号自动生成\"\n                >\n                </el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"分公司：\" prop=\"fgs\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.fgs\"\n                  disabled\n                  placeholder=\"请选择分公司\"\n                >\n                  <el-option\n                    v-for=\"item in organizationSelectedList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"光伏电站名称：\" prop=\"bdzmc\">\n                <el-select\n                  style=\"width: 100%\"\n                  filterable\n                  v-model=\"form.bdzmc\"\n                  disabled\n                  placeholder=\"请选择光伏电站\"\n                >\n                  <el-option\n                    v-for=\"item in bdzList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"下令人：\"\n                prop=\"xlr\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.xlr\"\n                  :disabled=\"isDisabled && isDisabledBj\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in xlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"审票人：\" prop=\"bzspr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.bzspr\"\n                  :disabled=\"isDisabledBj\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in sprList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"操作开始时间：\"\n                prop=\"kssj\"\n                label-width=\"140px\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-date-picker\n                  :disabled=\"isDisabledBj\"\n                  v-model=\"form.kssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  :placeholder=\"isDisabledBj ? '' : '选择日期时间'\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"操作结束时间：\"\n                prop=\"jssj\"\n                label-width=\"140px\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-date-picker\n                  :disabled=\"isDisabledBj\"\n                  v-model=\"form.jssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  :placeholder=\"isDisabledBj ? '' : '选择日期时间'\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"操作人：\"\n                prop=\"czr\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-input\n                  v-model=\"form.czr\"\n                  :disabled=\"isDisabledBj\"\n                  :placeholder=\"isDisabledBj ? '' : '请输入内容'\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"监护人：\"\n                prop=\"jhr\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.jhr\"\n                  :disabled=\"isDisabledBj\"\n                  :placeholder=\"isDisabledBj ? '' : '请选择'\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in jlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作项数：\" prop=\"czxs\">\n                <el-input-number\n                  v-model=\"form.czxs\"\n                  placeholder=\"请输入操作项数\"\n                  disabled\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"已执行项数：\" prop=\"yzxczxs\">\n                <el-input-number\n                  v-model=\"form.yzxczxs\"\n                  placeholder=\"请输入已执行操作项数\"\n                  disabled\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"未执行项数：\" prop=\"wzxczxs\">\n                <el-input-number\n                  v-model=\"form.wzxczxs\"\n                  placeholder=\"请输入未执行操作项数\"\n                  disabled\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"是否已执行：\"\n                prop=\"sfyzx\"\n                @change=\"handleChangeOfSfzx\"\n                :rules=\"\n                  form.status === '3'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-select\n                  v-model=\"form.sfyzx\"\n                  :placeholder=\"isDisabledBj ? '' : '请选择'\"\n                  :disabled=\"isDisabledBj\"\n                >\n                  <el-option\n                    v-for=\"item in sfyzxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item\n                label=\"未执行原因：\"\n                prop=\"wzxyy\"\n                v-if=\"form.sfyzx === '未执行'\"\n              >\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"form.wzxyy\"\n                  :disabled=\"isDisabledBj\"\n                  placeholder=\"请输入内容\"\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"操作任务：\" prop=\"czrw\">\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"form.czrw\"\n                  :disabled=\"isDisabledBj\"\n                  placeholder=\"请输入内容\"\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"上传图片：\">\n                <el-upload\n                  :disabled=\"isDisabledBj\"\n                  action=\"\"\n                  ref=\"uploadImg\"\n                  accept=\"image/jpeg,image/jpg,image/png\"\n                  :headers=\"header\"\n                  :multiple=\"true\"\n                  :on-change=\"handleChange\"\n                  :data=\"uploadImgData\"\n                  :file-list=\"imgList\"\n                  :auto-upload=\"false\"\n                  list-type=\"picture-card\"\n                  :on-preview=\"handlePictureCardPreview\"\n                  :on-progress=\"handleProgress\"\n                  :on-remove=\"handleRemove\"\n                >\n                  <i class=\"el-icon-plus\"></i>\n                </el-upload>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n\n        <!--预览，查看全部操作项-->\n        <div>\n          <div align=\"left\">\n            <el-upload\n              v-if=\"!isDisabled\"\n              action=\"\"\n              ref=\"upload\"\n              accept=\".xlsx\"\n              :limit=\"1\"\n              :auto-upload=\"false\"\n              :show-file-list=\"false\"\n              :on-change=\"importExcel\"\n            >\n              <el-button type=\"info\" @click.stop=\"handleYlChange\"\n                >预览</el-button\n              >\n              <el-button\n                type=\"success\"\n                icon=\"el-icon-download\"\n                @click.stop=\"exportExcel\"\n                >导出</el-button\n              >\n              <el-button type=\"success\" icon=\"el-icon-upload\">导入</el-button>\n              <div\n                slot=\"tip\"\n                class=\"el-upload__tip\"\n                style=\"width: 220px;color: red\"\n              >\n                先导出，再导入，只能上传当前页面导出的Excel文件\n              </div>\n            </el-upload>\n            <!-- <input type=\"file\" @change=\"importExcel\" v-if=\"(isDisabled && buttonNameShow) || hasSuperRole\"/> -->\n          </div>\n          <el-dialog :title=\"titleyl\" :visible.sync=\"yl\" append-to-body>\n            <!--第一个表格-->\n            <el-table\n              :data=\"propTableData.colFirst\"\n              ref=\"propTable\"\n              disabled\n              border\n              stripe\n              style=\"width: 100%\"\n              :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n            >\n              <el-table-column\n                align=\"center\"\n                width=\"120\"\n                prop=\"xh\"\n                label=\"顺序号\"\n                sortable\n              >\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"请输入顺序号\"\n                      disabled\n                      v-model=\"scope.row.xh\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"请输入操作项目\"\n                      disabled\n                      v-model=\"scope.row.czrw\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-dialog>\n        </div>\n\n        <!--列表-->\n        <div>\n          <div align=\"right\">\n            <el-checkbox @change=\"handleCheckAllChange\" :disabled=\"isDisabledBj\"\n              >全选</el-checkbox\n            >\n          </div>\n          <!--第一个表格-->\n          <el-table\n            :data=\"propTableData.colFirst\"\n            ref=\"propTable\"\n            :disabled=\"isDisabled\"\n            height=\"300\"\n            border\n            stripe\n            style=\"width: 100%\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <el-table-column\n              align=\"center\"\n              width=\"120\"\n              prop=\"xh\"\n              label=\"顺序号\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input-number\n                    size=\"small\"\n                    v-model=\"scope.row.xh\"\n                    :min=\"1\"\n                    :precision=\"0\"\n                    controls-position=\"right\"\n                    :disabled=\"isDisabled\"\n                  ></el-input-number>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入操作项目\"\n                    :disabled=\"isDisabled\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" width=\"80\" label=\"是否完成\">\n              <template slot-scope=\"scope\">\n                <el-checkbox\n                  v-model=\"scope.row.sfwc\"\n                  :disabled=\"isDisabledBj\"\n                  @change=\"handleCheckChange\"\n                ></el-checkbox>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" width=\"80\">\n              <template slot=\"header\" slot-scope=\"scope\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  :disabled=\"isDisabled\"\n                  icon=\"el-icon-plus\"\n                  @click=\"listFirstAdd(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n              <template slot-scope=\"scope\">\n                <el-button\n                  type=\"text\"\n                  icon=\"el-icon-delete\"\n                  :disabled=\"isDisabled\"\n                  @click=\"listFirstDel(scope.row)\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button\n          v-if=\"\n            isDisabled &&\n              buttonNameShow &&\n              form.status > 0 &&\n              form.status !== '3'\n          \"\n          type=\"info\"\n          @click=\"getSbFsBj('rollback')\"\n          >退 回\n        </el-button>\n        <el-button\n          v-if=\"isDisabled && buttonNameShow\"\n          type=\"success\"\n          @click=\"getSbFsBj('complete')\"\n          >{{ buttonName }}\n        </el-button>\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"saveRow\"\n          >保 存</el-button\n        >\n      </div>\n    </el-dialog>\n    <!--  工作流  -->\n    <activiti\n      ref=\"activiti\"\n      :processData=\"processData\"\n      :isShow=\"isShow\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n    ></activiti>\n    <!-- 流程详情 -->\n    <time-line\n      :value=\"timeLineShow\"\n      :timeData=\"timeData\"\n      @closeTimeLine=\"colseTimeLine\"\n    />\n    <!--流程图查看-->\n    <el-dialog\n      title=\"审批流程图\"\n      :visible.sync=\"openLoadingImg\"\n      width=\"56%\"\n      append-to-body\n      :close-on-click-modal=\"false\"\n    >\n      <div>\n        <img :src=\"imgSrc\" style=\"width: 100%\" />\n      </div>\n    </el-dialog>\n    <!--图片放大展示弹出框-->\n    <el-image-viewer\n      v-if=\"imgDialogVisible\"\n      :on-close=\"onClose\"\n      :url-list=\"dialogImageUrl\"\n      zIndex=\"3000\"\n    />\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport api from \"@/utils/request\";\nimport ElImageViewer from \"element-ui/packages/image/src/image-viewer\";\nimport {\n  exportPdf,\n  exportWord,\n  getCzpmxList,\n  getListLsp,\n  previewFile,\n  remove,\n  saveOrUpdate,\n  updateById\n} from \"@/api/yxgl/bdyxgl/bddzczp\";\nimport { getBdzDataListSelected } from \"@/api/dagangOilfield/asset/gfsbtz\";\nimport ElectronicAuthDialog from \"com/ElectronicAuthDialog\";\n//流程\nimport activiti from \"com/activiti_czp\";\nimport timeLine from \"com/timeLine\";\nimport { HistoryList } from \"@/api/activiti/processTask\";\nimport { getUsers } from \"@/api/system/usergroup\";\nimport { getUUID } from \"@/utils/ruoyi\";\nimport { getFgsOptions } from \"@/api/yxgl/gfyxgl/gfzbgl\";\nimport { exportToExcel, importFromExcel } from \"@/components/common/excel.js\";\n\nexport default {\n  name: \"dzczp\",\n  components: { ElectronicAuthDialog, activiti, timeLine, ElImageViewer },\n  data() {\n    return {\n      loading: false,\n      hasSuperRole: this.$store.getters.hasSuperRole,\n      jlrList: [],\n      //状态下拉框数据\n      statusOptions: [\n        {\n          value: \"0\",\n          label: \"操作票填报\"\n        },\n        {\n          value: \"1\",\n          label: \"班组审核\"\n        },\n        {\n          value: \"2\",\n          label: \"分公司审核\"\n        },\n        {\n          value: \"3\",\n          label: \"操作票办结\"\n        },\n        {\n          value: \"4\",\n          label: \"结束\"\n        },{ label: \"作废\", value: \"7\" }\n      ],\n      buttonNameShow: false,\n      buttonName: \"\",\n      isDisabledBj: true,\n      //组织结构下拉数据\n      organizationSelectedList: [],\n      bjr: \"\",\n      currentUser: this.$store.getters.name,\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"czpsh\",\n        businessKey: \"\",\n        businessType: \"倒闸操作票\",\n        variables: {},\n        defaultFrom: true,\n        nextUser: \"\",\n        processType: \"complete\"\n      },\n      // 是否已执行下拉框\n      sfyzxList: [\n        { label: \"已执行\", value: \"已执行\" },\n        { label: \"未执行\", value: \"未执行\" }\n      ],\n      //工作流弹窗\n      isShow: false,\n      //流程图查看\n      openLoadingImg: false,\n      imgSrc: \"\", //流程图查看地址\n      timeData: [],\n      timeLineShow: false,\n      //弹出框标题\n      activitiOption: { title: \"上报\" },\n      titleyl: \"\",\n      isShowSh: false,\n      isShShowDetails: false,\n      yl: false,\n      //图片地址url\n      dialogImageUrl: \"\",\n      //展示图片dialog控制\n      imgDialogVisible: false,\n      bdzList: [],\n      isIndeterminate: true,\n      // 多选框选中的id\n      ids: [],\n      single: true,\n      multiple: true,\n      selectData: [],\n      //弹出框中表格数据\n      propTableData: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      rules: {\n        // kssj: [\n        //   {required: true, message: '操作开始时间不能为空', trigger: 'blur'}\n        // ],\n        // jssj: [\n        //   {required: true, message: '操作结束时间不能为空', trigger: 'change'}\n        // ],\n        fgs: [{ required: true, message: \"分公司不能为空\", trigger: \"select\" }],\n        bdzmc: [\n          { required: true, message: \"光伏电站不能为空\", trigger: \"select\" }\n        ],\n        czrw: [\n          { required: true, message: \"操作任务不能为空\", trigger: \"blur\" }\n        ],\n        // xlr: [\n        //   {required: true, message: '下令人不能为空', trigger: 'blur'}\n        // ],\n        czxs: [\n          { required: true, message: \"操作项数不能为空\", trigger: \"blur\" }\n        ],\n        yzxczxs: [\n          { required: true, message: \"已执行项数不能为空\", trigger: \"blur\" }\n        ],\n        wzxczxs: [\n          { required: true, message: \"未执行项数不能为空\", trigger: \"blur\" }\n        ]\n      },\n      //form表单\n      form: {\n        bh: \"\",\n        bdzmc: \"\",\n        kssj: \"\",\n        jssj: \"\",\n        czrw: \"\",\n        czr: \"\",\n        jhr: \"\",\n        xlr: \"\",\n        // spr: '',\n        status: \"\",\n        lx: 4, //光伏\n        colFirst: []\n      },\n      //上传图片时的携带的其他参数\n      uploadImgData: {\n        businessId: \"\" //携带的表单主键id\n      },\n      //上传图片时的请求头\n      header: {},\n      //图片list\n      imgList: [],\n      //详情弹框是否显示\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      checkedAll: false,\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          bm: \"\",\n          bdzmc: \"\",\n          czsjArr: [],\n          kssjArr: [],\n          // jssjArr: [],\n          czrw: \"\",\n          czr: \"\",\n          jhr: \"\",\n          xlrmc: \"\",\n          status: \"\"\n          // spr: ''\n        }, //查询条件\n        fieldList: [\n          { label: \"编号\", value: \"bm\", type: \"input\", clearable: true },\n\n          {\n            label: \"分公司\",\n            value: \"fgs\",\n            type: \"select\",\n            options: [],\n            clearable: true\n          },\n          {\n            label: \"光伏电站名称\",\n            value: \"bdzmc\",\n            type: \"select\",\n            clearable: true,\n            options: []\n          },\n          { label: \"操作任务\", value: \"czrw\", type: \"input\", clearable: true },\n          { label: \"操作人\", value: \"czr\", type: \"input\", clearable: true },\n          { label: \"监护人\", value: \"jhrmc\", type: \"input\", clearable: true },\n          { label: \"下令人\", value: \"xlrmc\", type: \"input\", clearable: true },\n          {\n            label: \"状态\",\n            value: \"status\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            clearable: true,\n            options: [\n              {\n                value: \"0\",\n                label: \"操作票填报\"\n              },\n              {\n                value: \"1\",\n                label: \"班组审核\"\n              },\n              {\n                value: \"2\",\n                label: \"分公司审核\"\n              },\n              {\n                value: \"3\",\n                label: \"操作票办结\"\n              },{ label: \"作废\", value: \"7\" }\n            ]\n          }\n          // {label: '审票人', value: 'spr', type: 'input', clearable: true}\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"编号\", prop: \"bm\", minWidth: \"80\" },\n          { label: \"状态\", prop: \"statusCn\", minWidth: \"80\" },\n          { label: \"分公司\", prop: \"fgsmc\", minWidth: \"100\" },\n          { label: \"光伏电站名称\", prop: \"gfzmc\", minWidth: \"120\" },\n          { label: \"操作开始时间\", prop: \"kssj\", minWidth: \"110\" },\n          { label: \"操作结束时间\", prop: \"jssj\", minWidth: \"140\" },\n          { label: \"操作任务\", prop: \"czrw\", minWidth: \"160\" },\n          { label: \"操作人\", prop: \"czr\", minWidth: \"60\" },\n          { label: \"监护人\", prop: \"jhrmc\", minWidth: \"60\" },\n          { label: \"下令人\", prop: \"xlrmc\", minWidth: \"60\" },\n          { label: \"审票人\", prop: \"bzsprmc\", minWidth: \"60\" }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        //光伏\n        lx: 4,\n        //用来区分历史票库，1-已办结，2-未办结\n        sfbj: 2\n      },\n      xlrList: [],\n      sprList: []\n    };\n  },\n  async mounted() {\n    this.getFgsOptions();\n    //获取光伏电站下拉框数据\n    this.getBdzSelectList();\n    //获取token\n    this.header.token = getToken();\n    this.xlrList = await this.getGroupUsers(132, \"\");\n    this.sprList = await this.getGroupUsers(134, \"\");\n\n    await this.getData(this.$route.query);\n  },\n  methods: {\n    //作废票\n    nullifyGzp(objId) {\n      this.$confirm(\"票作废后只能查看，不能进行任何操作，确认作废吗?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(async () => {\n          let { code } = await updateById({ status: 7, objId: objId });\n          if (code === \"0000\") {\n            this.$message.success(\"操作成功!!\");\n            this.getData();\n          }\n        })\n        .catch(() => {});\n    },\n    exportExcel() {\n      let excelData = this.propTableData.colFirst.map(item => ({ \"操作项目\": item.czrw}));\n      exportToExcel(excelData, \"操作项目.xlsx\");\n    },\n    importExcel(file, fileList) {\n      let fileName = file.name\n      if (!fileName.includes(\"操作项目\")) {\n        this.msgError(\"文件有误，请检查\")\n        this.$refs.upload.clearFiles()\n        return\n      }\n      importFromExcel(file)\n        .then(data => {\n          this.ids = this.propTableData.colFirst.map(item => item.objId)\n          this.propTableData.colFirst = data.map(item => ({xh: item.__rowNum__ , czrw: item[\"操作项目\"]}));\n        })\n        .catch(error => {\n          console.error(\"导入失败\", error);\n        });\n      this.$refs.upload.clearFiles()\n    },\n    /**\n     * 获取分公司下拉数据\n     */\n    getFgsOptions() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.organizationSelectedList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"fgs\") {\n            return (item.options = this.organizationSelectedList);\n          }\n        });\n      });\n    },\n    filterReset(val) {\n      (this.params = {\n        //光伏\n        lx: 4,\n        //用来区分历史票库，1-已办结，2-未办结\n        sfbj: 2\n      }),\n        this.filterInfo.fieldList.forEach(item => {\n          if (item.type === \"checkbox\") {\n            item.checkboxValue = [];\n          }\n        });\n    },\n    getShow() {\n      this.buttonNameShow = false;\n      switch (this.form.status) {\n        case \"0\":\n          this.buttonName = \"上 报\";\n          if (this.currentUser === this.form.createBy) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case \"1\":\n          this.buttonName = \"提 交\";\n          if (this.form.bzspr === this.currentUser) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case \"2\":\n          this.buttonName = \"提 交\";\n          if (this.form.fgsspr === this.currentUser) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case \"3\":\n          this.buttonName = \"办 结\";\n          if (this.form.bjr === this.currentUser) {\n            this.buttonNameShow = true;\n            this.isDisabledBj = false;\n          }\n          break;\n      }\n    },\n\n    getGroupUsers(personGroupId, deptId) {\n      return getUsers({\n        personGroupId: personGroupId,\n        deptId: deptId,\n        deptName: \"\"\n      }).then(res => {\n        return res.data;\n      });\n    },\n    //预览文件\n    async previewFile(row) {\n      try {\n        let exportData = { ...row };\n        await previewFile(exportData, \"gfzdzczp\");\n      } catch (e) {\n        this.$message.error(\"预览失败！\");\n      }\n    },\n    //清空\n    change(e) {\n      this.$forceUpdate();\n    },\n    //关闭弹窗\n    closeActiviti() {\n      if (this.$refs.activiti.$refs.form) {\n        this.$refs.activiti.$refs.form.resetFields();\n      }\n      this.isShow = false;\n    },\n    //关闭流程查看页面\n    colseTimeLine() {\n      this.timeLineShow = false;\n    },\n    async showTimeLine(row) {\n      this.processData.businessKey = row.objId;\n      let { code, data } = await HistoryList(this.processData);\n      this.timeData = data;\n      this.timeLineShow = true;\n    },\n    //流程图片查看\n    showProcessImg(row) {\n      this.openLoadingImg = true;\n      this.imgSrc =\n        \"/activiti-api/process/read-resource?processDefinitionKey=czpsh&businessKey=\" +\n        row.objId +\n        \"&t=\" +\n        new Date().getTime();\n    },\n    //工作流回传数据\n    async todoResult(data) {\n      let row = {\n        objId: data.businessKey,\n        isStart: 1,\n        isBack: data.processType === \"rollback\" ? 1 : 0\n      };\n      if (data.processType === \"rollback\") {\n        switch (data.activeTaskName) {\n          case \"操作票填报\":\n            row.status = \"0\";\n            break;\n          case \"班组审核\":\n            row.status = \"1\";\n            break;\n        }\n      } else {\n        switch (data.activeTaskName) {\n          case \"班组审核\":\n            row.status = \"1\";\n            row.bzspr = data.nextUser;\n            break;\n          case \"分公司审核\":\n            row.status = \"2\";\n            row.fgsspr = data.nextUser;\n            break;\n          case \"操作票办结\":\n            row.status = \"3\";\n            row.bjr = data.nextUser;\n            break;\n          case \"结束\":\n            row.status = \"4\";\n            break;\n        }\n      }\n      let { code } = await updateById(row);\n      if (code === \"0000\") {\n        await this.getData();\n      } else {\n        this.$message.warning(\"操作失败\");\n      }\n    },\n    //上报发送办结\n    async getSbFsBj(type) {\n      let row = { ...this.form };\n      if (type === \"complete\") {\n        switch (row.status) {\n          case \"0\":\n            this.isShowDetails = false;\n            this.processData.variables.pass = true;\n            this.processData.businessKey = row.objId;\n            this.processData.processType = type;\n            this.activitiOption.title = \"提交\";\n            this.processData.defaultFrom = true;\n            this.processData.rylx = \"班组审核人\";\n            this.processData.dw = row.fgs;\n            this.processData.personGroupId = 134;\n            this.isShow = true;\n            break;\n          case \"1\":\n            this.isShowDetails = false;\n            this.$confirm(\"是否需要提交分公司审核?\", \"选择\", {\n              confirmButtonText: \"是\",\n              cancelButtonText: \"否\",\n              type: \"warning\"\n            })\n              .then(async () => {\n                this.processData.variables.pass = true;\n                this.processData.businessKey = row.objId;\n                this.processData.processType = type;\n                this.processData.dw = row.fgs;\n                this.processData.rylx = \"分公司审核人\";\n                this.processData.variables.isFgs = true;\n                this.activitiOption.title = \"提交\";\n                this.processData.defaultFrom = true;\n                this.processData.personGroupId = 135;\n                this.isShow = true;\n              })\n              .catch(() => {\n                this.processData.variables.pass = true;\n                this.processData.businessKey = row.objId;\n                this.processData.processType = type;\n                this.processData.dw = row.fgs;\n                this.processData.rylx = \"办结人\";\n                this.processData.variables.isFgs = false;\n                this.processData.defaultFrom = true;\n                this.processData.personGroupId = 136;\n                this.isShow = true;\n              });\n            break;\n          case \"2\":\n            this.isShowDetails = false;\n            this.processData.variables.pass = true;\n            this.processData.businessKey = row.objId;\n            this.processData.processType = type;\n            this.activitiOption.title = \"提交\";\n            this.processData.defaultFrom = true;\n            this.processData.personGroupId = 136;\n            this.processData.dw = row.fgs;\n            this.processData.rylx = \"办结人\";\n            this.isShow = true;\n            break;\n          case \"3\":\n            await this.$refs[\"form\"].validate(async valid => {\n              if (valid) {\n                try {\n                  this.form.colFirst = this.propTableData.colFirst;\n                  this.form.objIdList = this.ids;\n                  this.uploadImgData.businessId = this.form.objId;\n                  this.uploadForm();\n                  await saveOrUpdate(this.form).then(res => {\n                    if (res.code === \"0000\") {\n                      this.isShowDetails = false;\n                      this.getData();\n                      this.processData.variables.pass = true;\n                      this.processData.businessKey = row.objId;\n                      this.processData.processType = type;\n                      this.activitiOption.title = \"办结\";\n                      this.processData.defaultFrom = false;\n                      this.isShow = true;\n                    } else {\n                      this.$message.error(\"失败\");\n                    }\n                  });\n                } catch (e) {}\n              }\n            });\n        }\n      } else {\n        this.isShowDetails = false;\n        this.processData.businessKey = row.objId;\n        this.processData.processType = type;\n        this.activitiOption.title = \"回退原因填写\";\n        this.processData.defaultFrom = true;\n        this.processData.variables.pass = false;\n        this.isShow = true;\n      }\n    },\n\n    //改造后的上传多个图片文件\n    uploadForm() {\n      var newUrl = []; //用来存放当前未改动过的图片url,此图片不进行删除处理，其余当前业务id下面的图片数据将进行删除\n      var imageType = [\"png\", \"jpg\"];\n      const formData = new FormData();\n      // 因为要传一个文件数组过去，所以要循环append\n      this.imgList.forEach(file => {\n        if (file.raw == undefined) {\n          newUrl.push(file.url);\n        }\n        formData.append(\"files\", file.raw);\n      });\n      formData.append(\"businessId\", this.uploadImgData.businessId); // 自定义参数\n      formData.append(\"newUrl\", newUrl); // 未改动过的图片url\n      formData.append(\"type\", imageType); // 未改动过的图片url\n      api\n        .requestPost(\"/isc-api/file/uploadFiles\", formData, 1)\n        .then(res => {\n          // 清空图片列表（一定要清空，否则上传成功后还是会调用handleChange（）函数，上传成功后列表中还存在图片）\n          this.imgList = [];\n          //查询接口数据\n          this.getData();\n        })\n        .catch(res => {\n          this.$message.success(\"图片上传失败！\");\n        });\n    },\n    // 选择文件时，往fileList里添加\n    handleChange(file, fileList) {\n      this.imgList = fileList;\n    },\n    handleProgress(event, file, fileList) {},\n    //图片移除\n    handleRemove(file, fileList) {\n      this.imgList = fileList;\n    },\n    //图片放大\n    handlePictureCardPreview(file) {\n      this.dialogImageUrl = [file.url];\n      this.imgDialogVisible = true;\n    },\n    onClose() {\n      this.imgDialogVisible = false;\n    },\n    // 全选框\n    handleCheckAllChange(val) {\n      if (val) {\n        this.form.yzxczxs = this.form.czxs;\n        this.form.wzxczxs = 0;\n      } else {\n        this.form.wzxczxs = this.form.czxs;\n        this.form.yzxczxs = 0;\n      }\n      this.propTableData.colFirst.forEach(item => {\n        item.sfwc = val;\n      });\n    },\n    //选择已执行时，操作项目默认默认全选\n    handleChangeOfSfzx(val) {\n      if (val === \"已执行\") {\n        this.form.yzxczxs = this.form.czxs;\n        this.form.wzxczxs = 0;\n        this.propTableData.colFirst.forEach(item => {\n          item.sfwc = true;\n        });\n      } else {\n        this.form.wzxczxs = this.form.czxs;\n        this.form.yzxczxs = 0;\n        this.propTableData.colFirst.forEach(item => {\n          item.sfwc = false;\n        });\n      }\n    },\n    handleCheckChange(val) {\n      if (val) {\n        ++this.form.yzxczxs;\n      } else {\n        --this.form.yzxczxs;\n      }\n      this.form.wzxczxs = this.form.czxs - this.form.yzxczxs;\n    },\n    // 预览按钮\n    handleYlChange() {\n      this.titleyl = \"查看操作项目\";\n      this.yl = true;\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        const param = { ...this.params, ...params };\n        this.params = param;\n        this.loading = true;\n        if (!param.status) {\n          param.status = \"0,1,2,3,4\";\n        }\n        param.mySorts = [{ prop: \"updateTime\", asc: false }];\n        const { data, code } = await getListLsp(param);\n        if (code === \"0000\") {\n          for (let i of data.records) {\n            i.fgsmc = this.formatSsgs(i.fgs);\n            this.statusOptions.forEach(element => {\n              if (i.status === element.value) {\n                i.statusCn = element.label;\n              }\n            });\n          }\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          this.loading = false;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    /**\n     *获取操作票明细\n     */\n    async getCzpmx(row) {\n      try {\n        const { data, code } = await getCzpmxList({ objId: row.objId });\n        if (code === \"0000\") {\n          this.propTableData.colFirst = data;\n          this.propTableData.colFirst.forEach(e => {\n            e.uuid = getUUID();\n          });\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n\n    //修改按钮\n    async getUpdate(row) {\n      await this.getCzpmx(row);\n      this.title = \"光伏电倒闸操作票修改\";\n      this.isDisabled = false;\n      this.isDisabledBj = false;\n      this.form = { ...row };\n      //图片结合\n      this.imgList = this.form.imgList;\n      this.jlrList = await this.getGroupUsers(133, \"\");\n      this.isShowDetails = true;\n      this.getShow();\n    },\n    //详情按钮\n    async getDetails(row) {\n      await this.getCzpmx(row);\n      this.title = \"光伏电操作票详情查看\";\n      this.form = { ...row };\n      this.jlrList = await this.getGroupUsers(133, \"\");\n      //图片结合\n      this.imgList = this.form.imgList;\n      this.isDisabled = true;\n      this.isDisabledBj = true;\n      this.isShowDetails = true;\n      this.getShow();\n    },\n    // 保存按钮\n    async saveRow() {\n      await this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          try {\n            this.form.colFirst = this.propTableData.colFirst;\n            this.form.objIdList = this.ids;\n            saveOrUpdate(this.form).then(res => {\n              if (res.code === \"0000\") {\n                this.$message.success(\"操作成功\");\n              }\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n              this.isShowDetails = false;\n            });\n          } catch (e) {}\n        } else {\n          this.$message.error(\"校验未通过！\");\n          return false;\n        }\n      });\n    },\n\n    //删除按钮\n    async deleteRow(row) {\n      let objId = \"\";\n      if (row.objId) {\n        objId = row.objId;\n      } else {\n        objId = this.ids[0];\n      }\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove(objId).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    //表格新增\n    listFirstAdd() {\n      let row = {\n        xh: \"\",\n        czrw: \"\",\n        sfwc: \"\",\n        uuid: getUUID()\n      };\n      this.propTableData.colFirst.push(row);\n      this.propTableData.sel = row;\n      this.form.czxs = this.propTableData.colFirst.length;\n      this.form.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //表格删除\n    listFirstDel(row) {\n      if (row.objId) {\n        this.ids.push(row.objId);\n      }\n      this.propTableData.colFirst = this.propTableData.colFirst.filter(\n        item => item.uuid !== row.uuid\n      );\n      this.form.czxs = this.propTableData.colFirst.length;\n      this.form.wzxczxs = this.propTableData.colFirst.length;\n    },\n\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false;\n    },\n    selectChange(selection) {\n      this.ids = selection.map(item => item.objId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n      this.selectData = selection;\n    },\n    /**\n     * 获取光伏电站下拉框数据\n     */\n    getBdzSelectList() {\n      getBdzDataListSelected({}).then(res => {\n        this.bdzList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"bdzmc\") {\n            return (item.options = this.bdzList);\n          }\n        });\n      });\n    },\n    //导出word\n    async exportWord(row) {\n      try {\n        let exportData = { ...row };\n        await exportWord(exportData, \"gfzdzczp\", \"光伏电站倒闸操作票.docx\");\n      } catch (e) {\n        this.$message.error(\"导出失败！\");\n      }\n    },\n    //导出Pdf\n    async exportPdf(row) {\n      try {\n        let exportData = { ...row };\n        await exportPdf(exportData, \"gfzdzczp\", \"光伏电站倒闸操作票.pdf\");\n      } catch (e) {\n        this.$message.error(\"导出失败！\");\n      }\n    },\n    //下拉框change事件\n    handleEvent(val, eventValue) {\n      // if (val.label === \"fgs\") {\n      //   getBdzDataListSelected({ ssdwbm: val.value.toString() }).then(res => {\n      //     this.filterInfo.fieldList.map(item => {\n      //       if (item.value === \"bdzmc\") {\n      //         this.$set(eventValue, \"bdzmc\", \"\");\n      //         return (item.options = res.data);\n      //       }\n      //     });\n      //   });\n      // }\n    },\n    formatSsgs(ssgs) {\n      let pageOrganizationSelectedList = JSON.parse(\n        JSON.stringify(this.organizationSelectedList)\n      );\n      pageOrganizationSelectedList.push({\n        label: \"港东光伏电分公司\",\n        value: \"3002\"\n      });\n      pageOrganizationSelectedList.push({\n        label: \"港中光伏电分公司\",\n        value: \"3003\"\n      });\n      if (ssgs) {\n        let filter = pageOrganizationSelectedList.filter(g => g.value === ssgs);\n        if (filter.length > 0) {\n          return filter[0].label;\n        } else {\n          return \"\";\n        }\n      } else {\n        return \"\";\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.card-style {\n  background-color: #e0eff2;\n  line-height: 3vh;\n  border-radius: 3px;\n  font-weight: 800;\n  padding-left: 1vw;\n  margin-bottom: 3vh;\n}\n\n/*控制input输入框边框是否显示*/\n.elInput >>> .el-input__inner {\n  border: 0;\n}\n\n.center {\n  text-align: center; /*让div内部文字居中*/\n}\n</style>\n"]}]}