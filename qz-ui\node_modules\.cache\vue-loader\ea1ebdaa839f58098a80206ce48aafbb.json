{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\pddzcz\\dzczcxtj.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\pddzcz\\dzczcxtj.vue", "mtime": 1706897324315}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBjenBfdGogZnJvbSAnLi9jb21wb25lbnRzL2N6cF90aicKaW1wb3J0IHBkY3pwX2N4IGZyb20gJy4vY29tcG9uZW50cy9wZGN6cF9jeCcKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnZHpjemN4dGonLAogIGNvbXBvbmVudHM6IHsgY3pwX3RqLCBwZGN6cF9jeCB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBhY3RpdmVOYW1lOiAnY3gnCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvL3RhYueCueWHu+S6i+S7tgogICAgaGFuZGxlQ2xpY2sodGFiLCBldmVudCkgewogICAgICBjb25zb2xlLmxvZyh0YWIpCiAgICAgIGNvbnNvbGUubG9nKGV2ZW50KQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["dzczcxtj.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAiBA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "dzczcxtj.vue", "sourceRoot": "src/views/dagangOilfield/czpgl/pddzcz", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-white>\n      <el-tabs v-model=\"activeName\" type=\"card\" @tab-click=\"handleClick\">\n        <el-tab-pane label=\"查询\" name=\"cx\">\n          <pdczp_cx></pdczp_cx>\n        </el-tab-pane>\n        <el-tab-pane label=\"统计\" name=\"tj\">\n          <czp_tj></czp_tj>\n        </el-tab-pane>\n      </el-tabs>\n    </el-white>\n  </div>\n</template>\n\n\n<script>\n  import czp_tj from './components/czp_tj'\n  import pdczp_cx from './components/pdczp_cx'\n\n  export default {\n    name: 'dzczcxtj',\n    components: { czp_tj, pdczp_cx },\n    data() {\n      return {\n        activeName: 'cx'\n      }\n    },\n    methods: {\n      //tab点击事件\n      handleClick(tab, event) {\n        console.log(tab)\n        console.log(event)\n      }\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>\n\n"]}]}