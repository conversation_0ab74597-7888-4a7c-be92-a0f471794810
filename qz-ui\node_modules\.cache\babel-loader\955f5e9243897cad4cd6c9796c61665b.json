{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\syxmzq.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\syxmzq.vue", "mtime": 1706897323691}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["syxmzq.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AA+LA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAeA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA;AACA,MAAA,kBAAA,EAAA,EAFA;AAGA;AACA,MAAA,uBAAA,EAAA,EAJA;AAKA;AACA,MAAA,sBAAA,EAAA,IANA;AAOA;AACA,MAAA,oBAAA,EAAA,KARA;AASA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,OAAA,EAAA,SADA;AAEA,QAAA,GAAA,EAAA;AAFA,OAVA;AAcA;AACA,MAAA,iBAAA,EAAA,OAfA;AAgBA;AACA,MAAA,kBAAA,EAAA,KAjBA;AAkBA;AACA,MAAA,UAAA,EAAA,EAnBA;AAoBA;AACA,MAAA,mBAAA,EAAA,IArBA;AAsBA;AACA,MAAA,aAAA,EAAA,EAvBA;AAwBA;AACA,MAAA,gBAAA,EAAA;AACA,QAAA,MAAA,EAAA,SADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,OAAA,EAAA;AAHA,OAzBA;AA8BA;AACA,MAAA,UAAA,EAAA,CA/BA;AAgCA;AACA,MAAA,cAAA,EAAA,OAjCA;AAkCA;AACA,MAAA,eAAA,EAAA,KAnCA;AAqCA;AACA,MAAA,UAAA,EAAA,EAtCA;AAuCA;AACA,MAAA,KAAA,EAAA;AACA,QAAA,KAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AADA,OAxCA;AA6CA;AACA,MAAA,gBAAA,EAAA,IA9CA;AA+CA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,MAAA,EAAA,EADA;AAEA,UAAA,MAAA,EAAA;AAFA,SADA;AAKA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,QAFA;AAGA,UAAA,IAAA,EAAA,QAHA;AAIA,UAAA,OAAA,EAAA,EAJA;AAKA,UAAA,SAAA,EAAA;AALA,SADA,EAQA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,QAFA;AAGA,UAAA,IAAA,EAAA,OAHA;AAIA,UAAA,SAAA,EAAA;AAJA,SARA;AALA,OAhDA;AAqEA;AACA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SARA,EASA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,IAAA,EAAA,SAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SATA,EAUA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAVA,EAWA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAXA,EAYA;AACA,UAAA,IAAA,EAAA,WADA;AAEA,UAAA,KAAA,EAAA,IAFA;AAGA,UAAA,QAAA,EAAA,OAHA;AAIA,UAAA,KAAA,EAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WAJA;AAKA;AACA,UAAA,KAAA,EAAA,OANA;AAOA,UAAA,SAAA,EAAA,CACA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WADA,EAEA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WAFA,EAGA;AAAA,YAAA,IAAA,EAAA,OAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WAHA;AAPA,SAZA,CARA;AAkCA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAlCA,OAtEA;AA0GA;AACA,MAAA,eAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA;AAFA,OA3GA;AA+GA;AACA,MAAA,uBAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAJA,EAKA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OALA,EAMA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OANA,CAhHA;AAwHA;AACA,MAAA,sBAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,CAzHA;AA6HA;AACA,MAAA,wBAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,CA9HA;AAkIA;AACA,MAAA,uBAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,CAnIA;AAwIA;AACA,MAAA,IAAA,EAAA,EAzIA;AA0IA;AACA,MAAA,aAAA,EAAA,KA3IA;AA4IA;AACA,MAAA,UAAA,EAAA,KA7IA;AA8IA;AACA,MAAA,SAAA,EAAA,EA/IA;AAgJA;AACA,MAAA,KAAA,EAAA,EAjJA;AAoJA;AACA,MAAA,WAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA;AADA,OADA,EAGA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,QAAA,EAAA,CAAA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,QAAA,EAAA,CAAA;AACA,YAAA,KAAA,EAAA;AADA,WAAA,EAGA;AACA,YAAA,KAAA,EAAA;AADA,WAHA,EAMA;AACA,YAAA,KAAA,EAAA;AADA,WANA,EASA;AACA,YAAA,KAAA,EAAA;AADA,WATA;AAFA,SAAA;AAFA,OAHA,CArJA;AA2KA;AACA,MAAA,cAAA,EAAA,IA5KA;AA6KA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,SAAA,EAAA;AAHA,OA9KA;AAmLA;AACA,MAAA,kBAAA,EAAA,EApLA;AAqLA;AACA,MAAA,eAAA,EAAA,CAtLA;AAuLA;AACA,MAAA,mBAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA;AACA,QAAA,MAAA,EAAA,SAJA;AAKA,QAAA,OAAA,EAAA;AALA,OAxLA;AA+LA;AACA,MAAA,eAAA,EAAA;AACA;AACA,QAAA,MAAA,EAAA,SAFA;AAGA;AACA,QAAA,WAAA,EAAA;AAJA;AAhMA,KAAA;AAyMA,GA5MA;AA6MA,EAAA,KAAA,EAAA,EA7MA;AA8MA,EAAA,OA9MA,qBA8MA;AACA;AACA,SAAA,sBAAA,GAFA,CAGA;;AACA,SAAA,OAAA;AAGA,GArNA;AAsNA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,gBAFA,8BAEA;AACA,WAAA,iBAAA;AACA,KAJA;AAKA;AACA,IAAA,cANA,4BAMA;AACA,WAAA,mBAAA,CAAA,OAAA,GAAA,EAAA;AACA,KARA;AASA;AACA,IAAA,MAVA,oBAUA;AACA,WAAA,kBAAA,GAAA,IAAA;AACA,WAAA,iBAAA;AACA,KAbA;AAcA;AACA,IAAA,iBAfA,+BAeA;AAAA;;AACA,+CAAA,KAAA,mBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,KAAA,CAAA,eAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,KAAA,CAAA,kBAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,OAHA;AAIA,KApBA;AAqBA;AACA,IAAA,8BAtBA,0CAsBA,IAtBA,EAsBA;AACA,WAAA,eAAA,CAAA,WAAA,GAAA,IAAA;AACA,KAxBA;AAyBA;AACA,IAAA,gBA1BA,8BA0BA;AAAA;;AACA,UAAA,KAAA,eAAA,CAAA,WAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,cAAA,EADA,CAEA;;AACA,aAAA,kBAAA,GAAA,KAAA;AACA,OAJA,MAIA;AACA;AACA,wCAAA,KAAA,eAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA,WAFA,MAEA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;AACA,WALA,CAMA;;;AACA,UAAA,MAAA,CAAA,kBAAA,GAAA,KAAA,CAPA,CAQA;;AACA,UAAA,MAAA,CAAA,cAAA;AACA,SAVA;AAYA;AAEA,KA/CA;AAgDA;AACA,IAAA,cAjDA,0BAiDA,GAjDA,EAiDA;AACA,WAAA,gBAAA,CAAA,MAAA,GAAA,GAAA,CAAA,KAAA,CADA,CAEA;;AACA,WAAA,mBAAA,CAAA,MAAA,GAAA,GAAA,CAAA,KAAA,CAHA,CAIA;;AACA,WAAA,eAAA,CAAA,MAAA,GAAA,GAAA,CAAA,KAAA,CALA,CAMA;;AACA,WAAA,eAAA,GAAA,IAAA,CAPA,CAQA;;AACA,WAAA,cAAA;AACA,KA3DA;AA6DA;AACA,IAAA,cA9DA,4BA8DA;AAAA;;AACA,0CAAA,KAAA,gBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,MAAA,CAAA,aAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,OAHA;AAIA,KAnEA;AAoEA;AACA,IAAA,SArEA,uBAqEA;AAAA;;AACA,UAAA,KAAA,kBAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,aAAA;AACA;AACA;;AACA,UAAA,GAAA,GAAA,KAAA,kBAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA;AACA,OAFA,CAAA;AAGA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,iCAAA,GAAA,EAAA,IAAA,CAAA,gBAAA;AAAA,cAAA,IAAA,QAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,cAAA;AACA,WANA,MAMA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,cAAA;AACA;AACA,SAdA;AAeA,OApBA,EAoBA,KApBA,CAoBA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OAzBA;AA0BA,KAvGA;AAyGA;AACA,IAAA,OA1GA,mBA0GA,MA1GA,EA0GA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,KAFA,+DAEA,MAAA,CAAA,eAFA,GAEA,MAFA;AAAA;AAAA,uBAGA,2BAAA,KAAA,CAHA;;AAAA;AAAA;AAGA,gBAAA,IAHA,yBAGA,IAHA;AAGA,gBAAA,IAHA,yBAGA,IAHA;;AAIA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AAPA;AAAA;;AAAA;AAAA;AAAA;AASA,gBAAA,OAAA,CAAA,GAAA;;AATA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KArHA;AAsHA;AACA,IAAA,sBAvHA,oCAuHA;AAAA;;AACA,0CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,KAAA,QAAA,EAAA;AACA,YAAA,IAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;AACA,YAAA,MAAA,CAAA,uBAAA,GAAA,GAAA,CAAA,IAAA;AACA;AACA,SALA;AAMA,OAPA;AAQA,KAhIA;AAkIA;AACA,IAAA,iBAnIA,+BAmIA;AACA,WAAA,kBAAA,GAAA,KAAA;AACA,KArIA;AAuIA;AACA,IAAA,wBAxIA,oCAwIA,IAxIA,EAwIA;AACA,WAAA,kBAAA,GAAA,IAAA;AACA,KA1IA;AA4IA;AACA,IAAA,SA7IA,uBA6IA;AACA,UAAA,KAAA,UAAA,CAAA,IAAA,IAAA,IAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,kBAAA;AACA,OAFA,MAEA;AACA;AACA,aAAA,eAAA,GAAA,IAAA;AACA,aAAA,cAAA,CAAA,OAAA,GAAA,KAAA,UAAA,CAAA,KAAA,CAHA,CAIA;;AACA,aAAA,cAAA;AACA;AACA,KAvJA;AAwJA;AACA,IAAA,qBAzJA,iCAyJA,IAzJA,EAyJA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KA3JA;AA6JA;AACA,IAAA,cA9JA,0BA8JA,GA9JA,EA8JA;AACA;AACA,WAAA,aAAA,GAAA,IAAA,CAFA,CAGA;;AACA,WAAA,gBAAA,GAAA,KAAA,CAJA,CAKA;;AACA,WAAA,IAAA,GAAA,GAAA,CANA,CAOA;;AACA,WAAA,UAAA,GAAA,IAAA,CARA,CASA;;AACA,WAAA,KAAA,GAAA,IAAA;AACA,KAzKA;AA0KA;AACA,IAAA,aA3KA,yBA2KA,GA3KA,EA2KA;AACA;AACA,WAAA,aAAA,GAAA,IAAA,CAFA,CAGA;;AACA,WAAA,gBAAA,GAAA,IAAA,CAJA,CAKA;;AACA,WAAA,IAAA,GAAA,GAAA,CANA,CAOA;;AACA,WAAA,UAAA,GAAA,KAAA,CARA,CASA;;AACA,WAAA,KAAA,GAAA,IAAA;AACA,KAtLA;AAuLA;AACA,IAAA,eAxLA,6BAwLA;AACA;AACA,WAAA,aAAA,GAAA,IAAA,CAFA,CAGA;;AACA,WAAA,gBAAA,GAAA,IAAA,CAJA,CAKA;;AACA,WAAA,IAAA,GAAA,EAAA,CANA,CAOA;;AACA,WAAA,UAAA,GAAA,KAAA,CARA,CASA;;AACA,WAAA,KAAA,GAAA,IAAA;AACA,KAnMA;AAoMA;AACA,IAAA,SArMA,uBAqMA;AAAA;;AACA,UAAA,KAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,aAAA;AACA;AACA;;AACA,UAAA,GAAA,GAAA,KAAA,UAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA;AACA,OAFA,CAAA;AAGA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,0BAAA,GAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,cAAA,IAAA,SAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,YAAA,MAAA,CAAA,OAAA;AACA,WAPA,MAOA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,YAAA,MAAA,CAAA,OAAA;AACA;AACA,SAhBA;AAiBA,OAtBA,EAsBA,KAtBA,CAsBA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OA3BA;AA6BA,KA1OA;AA2OA;AACA,IAAA,UA5OA,wBA4OA;AAAA;;AACA,8BAAA,KAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,GAAA,CAAA,GAAA;;AACA,UAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,UAAA,MAAA,CAAA,OAAA;;AACA,UAAA,MAAA,CAAA,aAAA,GAAA,KAAA;AACA,SALA,MAKA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA;AACA;AACA,OATA;AAUA,KAvPA;AAwPA;AACA,IAAA,KAzPA,mBAyPA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,KA3PA;AA4PA;AACA,IAAA,QA7PA,sBA6PA,CAEA,CA/PA;AAgQA;AACA,IAAA,kBAjQA,gCAiQA,CAEA,CAnQA;AAoQA;AACA,IAAA,YArQA,0BAqQA,CAEA,CAvQA;AA0QA;AACA,IAAA,WA3QA,yBA2QA,CAEA,CA7QA;AA8QA;AACA,IAAA,UA/QA,wBA+QA;AACA,WAAA,SAAA,CAAA,WAAA;AACA;AAjRA;AAtNA,C", "sourcesContent": ["<template>\n\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 180, itemWidth: 300 }\"\n      @handleReset=\"getReset\"\n    />\n    <div>\n      <el-white class=\"button-group\">\n        <div class=\"button_btn\">\n          <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"addSensorButton\"\n          >新增\n          </el-button>\n          <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"getDelete\"\n          >删除\n          </el-button>\n        </div>\n        <comp-table :table-and-page-info=\"tableAndPageInfo\"\n                    @update:multipleSelection=\"handleSelectionChange\"\n                    height=\"57.2vh\"/>\n      </el-white>\n      <!-- 详情/新增/修改 -->\n      <el-dialog :title=title :visible.sync=\"isShowDetails\" width=\"60%\" v-dialogDrag>\n        <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :disabled=\"isDisabled\" :rules=\"rules\">\n          <el-row :gutter=\"8\" class=\"pull-left\">\n            <el-col :span=\"8\">\n              <el-form-item label=\"试验专业：\" prop=\"jglx\">\n                <el-select v-model=\"form.syzyid\" style=\"width: 100%\">\n                  <el-option\n                    v-for=\"item in syzyOptionsSelectedList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"项目名称：\" prop=\"syxmmc\">\n                <el-input v-model=\"form.syxmmc\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"项目描述：\" prop=\"zxmms\">\n                <el-input v-model=\"form.syxmms\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"录入温度：\" prop=\"lrwd\">\n                <el-select v-model=\"form.lrwd\" style=\"width: 100%\">\n                  <el-option label=\"是\" value=\"是\"></el-option>\n                  <el-option label=\"否\" value=\"否\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"录入湿度：\" prop=\"lrsd\">\n                <el-select v-model=\"form.lrsd\" style=\"width: 100%\">\n                  <el-option label=\"是\" value=\"是\"></el-option>\n                  <el-option label=\"否\" value=\"否\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"录入油温：\" prop=\"lryw\">\n                <el-select v-model=\"form.lryw\" style=\"width: 100%\">\n                  <el-option label=\"是\" value=\"是\"></el-option>\n                  <el-option label=\"否\" value=\"否\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"录入负荷：\" prop=\"lrfh\">\n                <el-select v-model=\"form.lrfh\" style=\"width: 100%\">\n                  <el-option label=\"是\" value=\"是\"></el-option>\n                  <el-option label=\"否\" value=\"否\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"可调整初值：\" prop=\"sfktzcz\">\n                <el-select v-model=\"form.sfktzcz\" style=\"width: 100%\">\n                  <el-option label=\"是\" value=\"是\"></el-option>\n                  <el-option label=\"否\" value=\"否\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"部位可拓展：\" prop=\"bwkkz\">\n                <el-select v-model=\"form.bwkkz\" style=\"width: 100%\">\n                  <el-option label=\"是\" value=\"是\"></el-option>\n                  <el-option label=\"否\" value=\"否\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"显示方式：\" prop=\"xsfs\">\n                <el-select v-model=\"form.xsfs\" style=\"width: 100%\">\n                  <el-option label=\"列为子项目\" value=\"列为子项目\"></el-option>\n                  <el-option label=\"竖排\" value=\"竖排\"></el-option>\n                  <el-option label=\"行为部位\" value=\"行为部位\"></el-option>\n                  <el-option label=\"列为部位\" value=\"列为部位\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"子项目定位：\" prop=\"zxmdw\">\n                <el-select v-model=\"form.zxmdw\" style=\"width: 100%\">\n                  <el-option label=\"相别\" value=\"相别\"></el-option>\n                  <el-option label=\"设备\" value=\"设备\"></el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-form>\n        <div slot=\"footer\" v-show=\"dialogButtonShow\">\n          <el-button @click=\"close\">取 消</el-button>\n          <el-button type=\"primary\" @click=\"commitForm\">确 认</el-button>\n        </div>\n      </el-dialog>\n      <!--子项目列表弹出框-->\n      <el-dialog :title=\"mjxDialogTitle\" :visible.sync=\"isShowMjzDialog\" width=\"50%\" v-dialogDrag>\n        <el-white class=\"button-group\">\n          <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n            <el-col\n              style=\"display: flex;justify-content: space-between;align-items: center;\">\n              <div>\n                <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"addMjz\">新增</el-button>\n                <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"deleteMjz\">删除</el-button>\n              </div>\n            </el-col>\n          </el-row>\n        </el-white>\n        <el-table stripe border :data=\"glzxmDataList\" @selection-change=\"handleSelectionMjzChange\"\n                  :header-cell-style=\"{'text-align':'center'}\" :cell-style=\"{'text-align':'center'}\">\n          <el-table-column type=\"selection\" width=\"55\"/>\n          <el-table-column label=\"序号\" type=\"index\" width=\"50\">\n          </el-table-column>\n          <el-table-column label=\"试验子项目\" prop=\"syzxmmc\" :show-overflow-tooltip=\"true\"/>\n          <el-table-column label=\"子项目描述\" prop=\"syzxmms\" :show-overflow-tooltip=\"true\"/>\n        </el-table>\n        <pagination\n          v-show=\"glzxmTotal>0\"\n          :total=\"glzxmTotal\"\n          :page.sync=\"glzxmQueryParams.pageNum\"\n          :limit.sync=\"glzxmQueryParams.pageSize\"\n          @pagination=\"getMjzDataList\"\n        />\n      </el-dialog>\n      <!--子列表新增弹窗调用-->\n      <el-dialog :title=\"mjzAddDialogTitle\" :visible.sync=\"isShowMjzAddDialog\" width=\"50%\" v-dialogDrag>\n        <el-form label-width=\"120px\">\n          <el-row :gutter=\"3\">\n            <el-col :span=\"10\">\n              <el-form-item label=\"子项目名称：\">\n                <el-input v-model=\"zxmLibraryQueryForm.syzxmmc\"/>\n              </el-form-item>\n            </el-col>\n            <div class=\"mb8 pull-right\">\n              <el-button type=\"cyan\" size=\"mini\" icon=\"el-icon-search\" @click=\"selectZxmLibrary\">查询</el-button>\n              <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetZxmSearch\">重置</el-button>\n            </div>\n          </el-row>\n        </el-form>\n        <el-table stripe border :data=\"zxmLibraryDataList\" @selection-change=\"handleSelectedZxmLibraryChange\"\n                  :header-cell-style=\"{'text-align':'center'}\" :cell-style=\"{'text-align':'center'}\">\n          <el-table-column type=\"selection\" width=\"55\"/>\n          <el-table-column label=\"序号\" type=\"index\" width=\"50\">\n          </el-table-column>\n          <el-table-column label=\"试验子项目\" prop=\"zxmmc\" :show-overflow-tooltip=\"true\"/>\n          <el-table-column label=\"子项目描述\" prop=\"zxmms\" :show-overflow-tooltip=\"true\"/>\n        </el-table>\n        <pagination\n          v-show=\"zxmLibraryTotal>0\"\n          :total=\"zxmLibraryTotal\"\n          :page.sync=\"zxmLibraryQueryForm.pageNum\"\n          :limit.sync=\"zxmLibraryQueryForm.pageSize\"\n          @pagination=\"getZxmLibraryList\"\n        />\n        <div slot=\"footer\">\n          <el-button @click=\"closeAddMjzDialog\">取 消</el-button>\n          <el-button type=\"primary\" @click=\"commitAddMjzForm\">确 认</el-button>\n        </div>\n      </el-dialog>\n    </div>\n  </div>\n</template>\n\n<script>\n  import {\n    getPageDataList,\n    getSyzyOptionsSelected,\n    getGlSyzxmDataListByPage,\n    getSyzxmLibraryDataListByPage,\n    addBatchSyzxmToXmzxm,\n    remove,\n    saveOrUpdate,\n    getPageKxzDataList,\n    removeKxzData,\n    saveOrUpdateKxzData,\n    getSblxTree\n  } from '@/api/dagangOilfield/bzgl/syxm'\n\n\n  export default {\n    name: \"lpbzk\",\n    data() {\n      return {\n        //试验子项目库数据\n        zxmLibraryDataList: [],\n        //试验专业下拉框数据\n        syzyOptionsSelectedList: [],\n        //枚举值新增弹出框底部按钮控制显示\n        addMjzDialogButtonShow: true,\n        //控制枚举值新增弹出框内容是否可编辑\n        mjzAddDialogDisabled: false,\n        //枚举值新增form表单\n        mjzAddForm: {\n          syzxmid: undefined,\n          kxz: undefined\n        },\n        //枚举值新增弹出框标题\n        mjzAddDialogTitle: \"子项目列表\",\n        //枚举值新增弹出框控制\n        isShowMjzAddDialog: false,\n        //选中子项目时获取到的第一行数据用来查询枚举值\n        mjzRowForm: {},\n        //维护枚举值button按钮\n        whmjzButtonDisabled: true,\n        //枚举值数据\n        glzxmDataList: [],\n        //枚举值参数\n        glzxmQueryParams: {\n          syxmid: undefined,\n          pageSize: 10,\n          pageNum: 1,\n        },\n        //枚举值总数\n        glzxmTotal: 0,\n        //枚举项弹出框标题\n        mjxDialogTitle: \"关联子项目\",\n        //枚举项弹出框\n        isShowMjzDialog: false,\n\n        //删除选择列\n        selectRows: [],\n        //表单验证\n        rules: {\n          zxmmc: [\n            {required: true, message: '请输入子项目名称', trigger: 'blur'},\n          ],\n        },\n        //弹出框取消确认按钮显示\n        dialogButtonShow: true,\n        //筛选框\n        filterInfo: {\n          data: {\n            syzyid: \"\",\n            syxmmc: \"\"\n          },\n          fieldList: [\n            {\n              label: '试验专业',\n              value: 'syzyid',\n              type: 'select',\n              options: [],\n              clearable: true,\n            },\n            {\n              label: '项目名称',\n              value: 'syxmmc',\n              type: 'input',\n              clearable: true,\n            },\n          ]\n        },\n        //列表页\n        tableAndPageInfo: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          tableData: [],\n          tableHeader: [\n            {label: '专业名称', prop: 'zymc', minWidth: '100'},\n            {label: '项目名称', prop: 'syxmmc', minWidth: '150'},\n            {label: '录入温度', prop: 'lrwd', minWidth: '80'},\n            {label: '录入湿度', prop: 'lrsd', minWidth: '80'},\n            {label: '录入油温', prop: 'lryw', minWidth: '80'},\n            {label: '录入负荷', prop: 'lrfh', minWidth: '80'},\n            {label: '显示方式', prop: 'xsfs', minWidth: '80'},\n            {label: '试验项目描述', prop: 'syxmms', minWidth: '150'},\n            {label: '是否可调整初值', prop: 'sfktzcz', minWidth: '150'},\n            {label: '部位可扩展', prop: 'bwkkz', minWidth: '150'},\n            {label: '子项目定位', prop: 'zxmdw', minWidth: '150'},\n            {\n              prop: 'operation',\n              label: '操作',\n              minWidth: '150px',\n              style: {display: 'block'},\n              //操作列固定再右侧\n              fixed: 'right',\n              operation: [\n                {name: '修改', clickFun: this.undateDetails},\n                {name: '详情', clickFun: this.getDetailsInfo},\n                {name: '关联子项目', clickFun: this.getZxmDataInfo},\n              ]\n            },\n          ],\n          option: {checkBox: true, serialNumber: true},\n        },\n        //查询试验子项目参数\n        querySyzxmParam: {\n          pageNum: 1,\n          pageSize: 10\n        },\n        //结果类型下拉框数据\n        jglxOptionsSelectedList: [\n          {label: '图片', value: '图片'},\n          {label: '数字', value: '数字'},\n          {label: '日期', value: '日期'},\n          {label: '单选', value: '单选'},\n          {label: '枚举', value: '枚举'},\n          {label: '字符', value: '字符'},\n        ],\n        //计算列\n        jslOptionsSelectedList: [\n          {label: '是', value: '是'},\n          {label: '否', value: '否'},\n        ],\n        //是否为空\n        sfkwkOptionsSelectedList: [\n          {label: '是', value: '是'},\n          {label: '否', value: '否'},\n        ],\n        //是否显示\n        sfxsOptionsSelectedList: [\n          {label: '是', value: '是'},\n          {label: '否', value: '否'},\n        ],\n\n        //form表单\n        form: {},\n        //是否显示弹框\n        isShowDetails: false,\n        //是否禁用\n        isDisabled: false,\n        //表单数据\n        dataTable: [],\n        //标题\n        title: '',\n\n\n        //组织树\n        treeOptions: [\n          {\n            label: '断路器',\n          }, {\n            label: '变压器',\n            children: [{\n              label: '冷却系统',\n              children: [{\n                label: '温控运行情况',\n\n              }, {\n                label: '油箱',\n\n              }, {\n                label: '铁芯',\n\n              }, {\n                label: '绕组',\n\n              }]\n            }]\n          }],\n        //删除是否可用\n        multipleSensor: true,\n        //查询参数\n        queryParams: {\n          pageNum: 1,\n          pageSize: 10,\n          ancuoTerm: ''\n        },\n        //删除关联子项目的id数组\n        selectedKxzDataRow: [],\n        //子项目库总数\n        zxmLibraryTotal: 0,\n        //子项目库查询参数\n        zxmLibraryQueryForm: {\n          pageNum: 1,\n          pageSize: 10,\n          //试验项目id\n          syxmid: undefined,\n          syzxmmc: undefined,\n        },\n        //试验子项目库中选中新增关联子项目时的数据对象\n        zxmSelectedForm: {\n          //试验项目id\n          syxmid: undefined,\n          //试验子项目数据集合\n          zxmDataRows: []\n        },\n\n\n      };\n    },\n    watch: {},\n    created() {\n      //获取试验专业下拉框数据\n      this.getSyzyOptionsSelected();\n      //获取列表数据\n      this.getData();\n\n\n    },\n    methods: {\n      //子项目库中查询按钮\n      selectZxmLibrary() {\n        this.getZxmLibraryList();\n      },\n      //子项目库中重置按钮\n      resetZxmSearch() {\n        this.zxmLibraryQueryForm.syzxmmc = \"\";\n      },\n      //关联子项目中新增按钮\n      addMjz() {\n        this.isShowMjzAddDialog = true;\n        this.getZxmLibraryList();\n      },\n      //获取试验子项目库数据方法\n      getZxmLibraryList() {\n        getSyzxmLibraryDataListByPage(this.zxmLibraryQueryForm).then(res => {\n          this.zxmLibraryTotal = res.data.total;\n          this.zxmLibraryDataList = res.data.records;\n        })\n      },\n      //试验子项目库选中按钮\n      handleSelectedZxmLibraryChange(rows) {\n        this.zxmSelectedForm.zxmDataRows = rows;\n      },\n      //提交从子项目库中调用数据插入到关联子项目表中\n      commitAddMjzForm() {\n        if (this.zxmSelectedForm.zxmDataRows.length < 1) {\n          this.$message.info(\"未关联子项目！！！已取消\")\n          //如果未选中数据,则直接关闭弹窗\n          this.isShowMjzAddDialog = false;\n        } else {\n          //若选择数据后\n          addBatchSyzxmToXmzxm(this.zxmSelectedForm).then(res => {\n            if (res.code === '0000') {\n              this.$message.success(\"关联成功\");\n            } else {\n              this.$message.error(\"关联失败！！\");\n            }\n            //关闭弹窗\n            this.isShowMjzAddDialog = false\n            //调用获取关联子项目列表\n            this.getMjzDataList();\n          })\n\n        }\n\n      },\n      //关联子项目按钮\n      getZxmDataInfo(row) {\n        this.glzxmQueryParams.syxmid = row.objId;\n        //给子项目库查询参数赋值\n        this.zxmLibraryQueryForm.syxmid = row.objId;\n        //给批量新增关联子项目时的主项目id赋值\n        this.zxmSelectedForm.syxmid = row.objId;\n        //打开子项目列表弹出框\n        this.isShowMjzDialog = true;\n        //获取关联子项目列表数据\n        this.getMjzDataList();\n      },\n\n      //获取关联子列表方法\n      getMjzDataList() {\n        getGlSyzxmDataListByPage(this.glzxmQueryParams).then(res => {\n          this.glzxmTotal = res.data.total;\n          this.glzxmDataList = res.data.records;\n        })\n      },\n      //删除关联子列表方法\n      deleteMjz() {\n        if (this.selectedKxzDataRow.length < 1) {\n          this.$message.warning(\"请选择正确的数据！！！\")\n          return\n        }\n        let ids = this.selectedKxzDataRow.map(item => {\n          return item.objId\n        });\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          removeKxzData(ids).then(({code}) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              });\n              this.getMjzDataList()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              });\n              this.getMjzDataList()\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          });\n        });\n      },\n\n      //列表查询项目列表\n      async getData(params) {\n        try {\n          const param = {...this.querySyzxmParam, ...params}\n          const {data, code} = await getPageDataList(param);\n          if (code === '0000') {\n            this.tableAndPageInfo.tableData = data.records\n            this.tableAndPageInfo.pager.total = data.total\n          }\n        } catch (e) {\n          console.log(e)\n        }\n      },\n      //获取试验专业下拉框数据\n      getSyzyOptionsSelected() {\n        getSyzyOptionsSelected().then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value === 'syzyid') {\n              item.options = res.data;\n              this.syzyOptionsSelectedList = res.data;\n            }\n          })\n        });\n      },\n\n      //取消按钮(枚举值新增弹出框)\n      closeAddMjzDialog() {\n        this.isShowMjzAddDialog = false\n      },\n\n      //关联子列表行选中事件\n      handleSelectionMjzChange(rows) {\n        this.selectedKxzDataRow = rows;\n      },\n\n      //维护枚举值按钮\n      addZxmKxz() {\n        if (this.mjzRowForm.jglx != '枚举') {\n          this.$message.warning(\"请选择结果类型为枚举类型的数据！\")\n        } else {\n          //打开弹窗\n          this.isShowMjzDialog = true;\n          this.mjzQueryParams.syzxmid = this.mjzRowForm.objId;\n          //获取枚举值列表\n          this.getMjzDataList();\n        }\n      },\n      //行选中\n      handleSelectionChange(rows) {\n        this.selectRows = rows;\n      },\n\n      //获取详情\n      getDetailsInfo(row) {\n        //打开弹窗\n        this.isShowDetails = true;\n        //弹出框按钮不显示\n        this.dialogButtonShow = false;\n        //给表单赋值\n        this.form = row;\n        //表单不可编辑\n        this.isDisabled = true;\n        //设置弹出框标题\n        this.title = '详情';\n      },\n      //修改按钮\n      undateDetails(row) {\n        //打开弹窗\n        this.isShowDetails = true;\n        //弹出框按钮显示\n        this.dialogButtonShow = true;\n        //给表单赋值\n        this.form = row;\n        //表单可编辑\n        this.isDisabled = false;\n        //设置弹出框标题\n        this.title = '修改';\n      },\n      //添加按钮\n      addSensorButton() {\n        //打开弹窗\n        this.isShowDetails = true;\n        //弹出框按钮显示\n        this.dialogButtonShow = true;\n        //给表单置空\n        this.form = {};\n        //表单可编辑\n        this.isDisabled = false;\n        //设置弹出框标题\n        this.title = '新增';\n      },\n      //删除按钮\n      getDelete() {\n        if (this.selectRows.length < 1) {\n          this.$message.warning(\"请选择正确的数据！！！\")\n          return\n        }\n        let ids = this.selectRows.map(item => {\n          return item.objId\n        });\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove(ids).then(({code}) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              });\n              this.tableAndPageInfo.pager.pageResize = 'Y';\n              this.getData()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              });\n              this.tableAndPageInfo.pager.pageResize = 'Y';\n              this.getData()\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          });\n        });\n\n      },\n      //确认提交表单\n      commitForm() {\n        saveOrUpdate(this.form).then(res => {\n          if (res.code === '0000') {\n            this.$message.success(res.msg)\n            this.tableAndPageInfo.pager.pageResize = 'Y';\n            this.getData();\n            this.isShowDetails = false;\n          } else {\n            this.$message.error(res.msg)\n          }\n        });\n      },\n      //关闭弹窗\n      close() {\n        this.isShowDetails = false\n      },\n      //定义重置方法\n      getReset() {\n\n      },\n      //删除按钮\n      deleteSensorButton() {\n\n      },\n      //导出按钮\n      handleExport() {\n\n      },\n\n\n      //搜索\n      handleQuery() {\n\n      },\n      //重置\n      resetQuery() {\n        this.resetForm(\"queryForm\");\n      }\n    }\n  };\n</script>\n\n<style lang='scss' scoped>\n  .box-card {\n    margin-bottom: 15px;\n\n    .el-card__header {\n      background-color: rgb(235, 245, 255) !important;\n    }\n  }\n\n  .item {\n    width: 200px;\n    height: 148px;\n    float: left;\n  }\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk"}]}