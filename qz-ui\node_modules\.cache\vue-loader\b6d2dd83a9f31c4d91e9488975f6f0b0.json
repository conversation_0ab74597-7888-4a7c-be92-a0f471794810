{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sdsbgl\\sdsb.vue?vue&type=style&index=1&lang=css&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sdsbgl\\sdsb.vue", "mtime": 1706897324964}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5lbC1jYXJvdXNlbF9fY29udGFpbmVyIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmZmZmICFpbXBvcnRhbnQ7Cn0K"}, {"version": 3, "sources": ["sdsb.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA61GA;AACA;AACA", "file": "sdsb.vue", "sourceRoot": "src/views/dagangOilfield/dwzygl/sdsbgl", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card>\n          <div style=\"overflow: auto;height: 90vh\">\n            <el-tree\n              :expand-on-click-node=\"true\"\n              highlight-current\n              ref=\"tree\"\n              id=\"tree\"\n              :data=\"treeOptions\"\n              :default-expanded-keys=\"['1001']\"\n              @node-click=\"handleNodeClick\"\n              node-key=\"id\"\n              accordion\n            >\n              <span slot-scope=\"{ node, data }\">\n                <i :class=\"icons[data.identifier]\" />\n                <span style=\"margin-left:5px;\" :title=\"data.label\">{{\n                  data.label\n                }}</span>\n              </span>\n            </el-tree>\n          </div>\n        </el-card>\n      </el-col>\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <!--变电站查询-->\n        <el-filter\n          ref=\"filter1\"\n          :data=\"xlfilterInfo.data\"\n          :field-list=\"xlfilterInfo.fieldList\"\n          :width=\"{ labelWidth: 130, itemWidth: 165 }\"\n          comp-table=\"tableAndPageInfo1\"\n          @handleReset=\"filterReset($event, 'xl')\"\n          v-show=\"xltzData\"\n        />\n        <!--  杆塔查询 -->\n        <el-filter\n          ref=\"filter1\"\n          :data=\"gtfilterInfo.data\"\n          :field-list=\"gtfilterInfo.fieldList\"\n          :btnHidden=\"false\"\n          comp-table=\"tableAndPageInfo2\"\n          @handleReset=\"filterReset($event, 'gt')\"\n          v-show=\"gttzData\"\n        />\n        <!--设备查询-->\n        <el-filter\n          ref=\"filter1\"\n          :data=\"sbfilterInfo.data\"\n          :field-list=\"sbfilterInfo.fieldList\"\n          :btnHidden=\"false\"\n          comp-table=\"tableAndPageInfo3\"\n          @handleReset=\"filterReset($event, 'sb')\"\n          v-show=\"sbtzData\"\n        />\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button\n              type=\"primary\"\n              v-hasPermi=\"['sbtz:button:add']\"\n              icon=\"el-icon-plus\"\n              @click=\"AddSensorButton\"\n              >新增\n            </el-button>\n            <el-button\n              v-show=\"gttzData\"\n              type=\"primary\"\n              v-hasPermi=\"['sbtz:button:add']\"\n              icon=\"el-icon-plus\"\n              @click=\"AddTowerBatchButton\"\n              >批量新增\n            </el-button>\n            <el-button\n              v-show=\"!sbtzData\"\n              type=\"success\"\n              icon=\"el-icon-download\"\n              @click=\"exportExcel\"\n              >导出</el-button\n            >\n            <el-button\n              v-show=\"gttzData\"\n              type=\"success\"\n              icon=\"el-icon-download\"\n              @click=\"importExcel\"\n              >导入</el-button\n            >\n            <el-button\n              type=\"danger\"\n              v-hasPermi=\"['sbtz:button:delete']\"\n              icon=\"el-icon-delete\"\n              @click=\"deleteInfo\"\n              >删除\n            </el-button>\n          </div>\n\n          <!--变电站查询-->\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo1\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"61vh\"\n            v-show=\"xltzData\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"updateRow1(scope.row)\"\n                  v-hasPermi=\"['sbtz:button:updete1']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                >\n                </el-button>\n                <el-button\n                  @click=\"detailsInfo1(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n                <router-link\n                  :to=\"{\n                    path: '/jszlgl/tzgl',\n                    query: { wjbh: scope.row.xlbm, dydj: '' }\n                  }\"\n                  style=\"margin-left:10px;\"\n                >\n                  <el-button\n                    type=\"text\"\n                    size=\"small\"\n                    title=\"图纸跳转\"\n                    class=\"el-icon-discover\"\n                  ></el-button>\n                </router-link>\n                <!-- <el-button type=\"text\" size=\"small\" @click=\"jumpToTzgl(scope.row)\" title=\"图纸跳转\" class=\"el-icon-discover\"></el-button> -->\n                <el-button\n                  @click=\"zxwhFun(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"支线维护\"\n                  class=\"el-icon-setting\"\n                  style=\"margin-left:10px;\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n\n          <!--  杆塔查询 -->\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo2\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            @sort-events=\"sortChangeTowerData\"\n            height=\"65.4vh\"\n            v-show=\"gttzData\"\n            @getMethod=\"gtTzData\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"updateStatus(scope.row)\"\n                  v-hasPermi=\"['sbtz:button:ztbg']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"状态变更\"\n                  class=\"el-icon-set-up\"\n                >\n                </el-button>\n                <el-button\n                  @click=\"updateRow2(scope.row)\"\n                  v-hasPermi=\"['sbtz:button:update2']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                >\n                </el-button>\n                <el-button\n                  @click=\"detailsInfo2(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n\n          <!--设备查询-->\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo3\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"65.4vh\"\n            v-show=\"sbtzData\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"updateRow(scope.row)\"\n                  v-hasPermi=\"['sbtz:button:update3']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                >\n                </el-button>\n                <el-button\n                  @click=\"detailsInfo(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!-- 线路详情所用弹出框开始-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"xlDialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"60%\"\n      @close=\"resetxlForm\"\n      v-dialogDrag\n    >\n      <el-form\n        :model=\"xlForm\"\n        ref=\"xlForm\"\n        :disabled=\"xlshow\"\n        :rules=\"xlRules\"\n        label-width=\"130px\"\n      >\n        <!-- <div class=\"divHeader\">\n          <span>基本信息</span>\n        </div> -->\n        <div\n          class=\"block\"\n          style=\"width: 50%;height: 50%;margin-bottom: 2%;float: right\"\n        >\n          <span class=\"demonstration\">线路图</span>\n          <el-carousel\n            trigger=\"click\"\n            class=\"imgCls\"\n            height=\"150px\"\n            indicator-position=\"none\"\n            arrow=\"always\"\n            type=\"card\"\n          >\n            <el-carousel-item v-for=\"(img, index) in xlImgList\" :key=\"index\">\n              <viewer :images=\"xlImgList\" style=\"z-index: 999\">\n                <img :src=\"img.fileUrl\" width=\"100%\" height=\"100%\" />\n              </viewer>\n            </el-carousel-item>\n          </el-carousel>\n        </div>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路图编号\" prop=\"xlbm\">\n              <el-autocomplete\n                v-model=\"xlForm.xlbm\"\n                placeholder=\"请输入线路图编号\"\n                popper-class=\"my-autocomplete\"\n                :fetch-suggestions=\"querySearch\"\n                :trigger-on-focus=\"false\"\n                @select=\"handleSelect\"\n              >\n                <template slot-scope=\"{ item }\">\n                  <div class=\"name\">{{ item.wjbh }}</div>\n                  <span class=\"addr\">{{ item.wjmc }}</span>\n                </template>\n              </el-autocomplete>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路名称\" prop=\"lineName\">\n              <el-input\n                v-model=\"xlForm.lineName\"\n                placeholder=\"请输入线路名称\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路专业\" prop=\"lineType\">\n              <el-select\n                v-model=\"xlForm.lineType\"\n                placeholder=\"请选择线路专业\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in lineTypeOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电压等级\" prop=\"dydjbm\">\n              <el-select\n                v-model=\"xlForm.dydjbm\"\n                placeholder=\"请选择电压等级\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in xloptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"负责人\" prop=\"fzr\">\n              <el-input\n                v-model=\"xlForm.fzr\"\n                :placeholder=\"xlshow ? '' : '请输入负责人'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"运行班组\" prop=\"yxbz\">\n              <el-select\n                v-model=\"xlForm.yxbz\"\n                :placeholder=\"xlshow ? '' : '请选择运行班组'\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in deviceNameOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.label\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"线路性质\" prop=\"xlxz\">\n              <el-input v-model=\"xlForm.xlxz\" placeholder=\"请选择线路性质\" clearable></el-input>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路类型\" prop=\"xllx\">\n              <el-select\n                v-model=\"xlForm.xllx\"\n                :placeholder=\"xlshow ? '' : '请选择线路类型'\"\n                clearable\n              >\n                <el-option label=\"架空线路\" value=\"架空线路\"></el-option>\n                <el-option label=\"混合线路\" value=\"混合线路\"></el-option>\n                <el-option label=\"电缆线路\" value=\"电缆线路\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路起点\" prop=\"startLine\">\n              <el-input\n                v-model=\"xlForm.startLine\"\n                placeholder=\"请输入线路起点\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路终点\" prop=\"stopLine\">\n              <el-input\n                v-model=\"xlForm.stopLine\"\n                :placeholder=\"xlshow ? '' : '请输入线路终点'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"同杆并架长度\" prop=\"tgbjcd\">\n              <el-input v-model=\"xlForm.tgbjcd\" placeholder=\"请输入同杆并架长度\"></el-input>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路起始杆塔号\" prop=\"startTowerNum\">\n              <el-input\n                v-model=\"xlForm.startTowerNum\"\n                :placeholder=\"xlshow ? '' : '请选择线路起始杆塔号'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路终止杆塔号\" prop=\"stopTowerNum\">\n              <el-input\n                v-model=\"xlForm.stopTowerNum\"\n                :placeholder=\"xlshow ? '' : '请选择线路终止杆塔号'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"是否有光缆\" prop=\"sfygl\">\n              <el-select v-model=\"xlForm.sfygl\" placeholder=\"请选择是否有光缆\" clearable>\n                <el-option label=\"是\" value=\"是\"></el-option>\n                <el-option label=\"否\" value=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"光缆类别\" prop=\"gllb\">\n              <el-input\n                v-model=\"xlForm.gllb\"\n                :placeholder=\"xlshow ? '' : '请输入光缆类别'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"投运日期\" class=\"add_sy_tyrq\" prop=\"tyrq\">\n              <el-date-picker\n                v-model=\"xlForm.tyrq\"\n                type=\"date\"\n                :placeholder=\"xlshow ? '' : '选择投运日期'\"\n                format=\"yyyy-MM-dd\"\n                value-format=\"yyyy-MM-dd\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"设计单位\" class=\"add_sy_tyrq\" prop=\"sjdw\">\n              <el-input v-model=\"xlForm.sjdw\" placeholder=\"请输入设计单位\"></el-input>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"电缆型号\" prop=\"dlxh\">\n              <el-input\n                v-model=\"xlForm.dlxh\"\n                :placeholder=\"xlshow ? '' : '请输入电缆型号'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电缆长度(M)\" prop=\"dlLength\">\n              <el-input\n                v-model=\"xlForm.dlLength\"\n                placeholder=\"请输入电缆长度\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"检修单位\" prop=\"jxdw\">\n              <el-input v-model=\"xlForm.jxdw\" placeholder=\"请输入检修单位\"></el-input>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路全长（KM）\" prop=\"totalLength\">\n              <el-input\n                v-model=\"xlForm.totalLength\"\n                placeholder=\"请输入线路全长\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"线路状态\" prop=\"lineStatus\">\n              <el-select\n                v-model=\"xlForm.lineStatus\"\n                placeholder=\"请选择线路状态\"\n              >\n                <el-option\n                  v-for=\"item in xlztOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"负责人\" prop=\"fzr\">\n              <el-input v-model=\"xlForm.fzr\" placeholder=\"请输入负责人\"></el-input>\n            </el-form-item>\n          </el-col> -->\n          <!-- </el-row> -->\n          <!-- <div class=\"divHeader\">\n          <span>详细信息</span>\n        </div>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"资产性质\" class=\"add_sy_tyrq\" prop=\"zcxz\">\n              <el-select v-model=\"xlForm.zcxz\" placeholder=\"请选择资产性质\" clearable >\n                <el-option label=\"公用\" value=\"公用\"></el-option>\n                <el-option label=\"专用\" value=\"专用\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否为联络线路\" prop=\"sfllxl\">\n              <el-select v-model=\"xlForm.sfllxl\" placeholder=\"请选择是否为联络线路\" clearable>\n                <el-option label=\"是\" value=\"是\"></el-option>\n                <el-option label=\"否\" value=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"联络类型\" prop=\"lllx\">\n              <el-select v-model=\"xlForm.lllx\" placeholder=\"请选择联络类型\" clearable>\n                <el-option label=\"低压\" value=\"低压\"></el-option>\n                <el-option label=\"高压\" value=\"高压\"></el-option>\n                <el-option label=\"单环\" value=\"单环\"></el-option>\n                <el-option label=\"双环\" value=\"双环\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"运行班组\" prop=\"yxbz\">\n              <el-select v-model=\"xlForm.yxbz\" placeholder=\"请选择运行班组\" clearable>\n                <el-option\n                  v-for=\"item in deviceNameOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.label\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col> -->\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\" prop=\"bz\">\n              <el-input v-model=\"xlForm.bz\" type=\"textarea\" rows=\"2\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"xlDialogFormVisible = false\">取 消</el-button>\n        <el-button\n          v-if=\"title == '修改线路信息' || title == '新增线路信息'\"\n          type=\"primary\"\n          @click=\"addLineInfo\"\n          >确 定</el-button\n        >\n      </div>\n    </el-dialog>\n    <!-- 线路详情所用弹出框结束-->\n\n    <!-- 杆塔弹出框开始展示设备履历 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"gtDialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"60%\"\n      @close=\"gthandleClose\"\n      v-dialogDrag\n    >\n      <el-tabs v-model=\"gtactiveTabName\">\n        <!--基本信息-->\n        <el-tab-pane label=\"基本信息\" name=\"sbDesc\">\n          <!--          <div class=\"block\" style=\"width: 50%;height: 50%;margin-bottom: 2%;float: right\">\n            <span class=\"demonstration\">设备图片</span>\n            <el-carousel trigger=\"click\" height=\"150px\" indicator-position=\"none\" :interval=\"2000\" type=\"card\">\n              <el-carousel-item v-for=\"(img,index) in imgList\" :key=\"index\">\n                <viewer :images=\"imgList\">\n                  <img :src=\"img.url\" width=\"100%\" height=\"100%\"/>\n                </viewer>\n              </el-carousel-item>\n            </el-carousel>\n          </div>-->\n          <el-form\n            :model=\"gtForm\"\n            ref=\"gtForm\"\n            :disabled=\"gtshow\"\n            :rules=\"gtRules\"\n            label-width=\"130px\"\n          >\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔编号\" prop=\"gtbh\">\n                  <el-input\n                    v-model=\"gtForm.gtbh\"\n                    placeholder=\"请输入杆塔编号\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔名称\" prop=\"gtmc\">\n                  <el-input\n                    v-model=\"gtForm.gtmc\"\n                    placeholder=\"请输入杆塔名称\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"所属线路\" prop=\"lineName\">\n                  <el-input\n                    v-model=\"gtForm.lineName\"\n                    placeholder=\"请输入所属线路\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔材质\" prop=\"gtcz\">\n                  <el-select\n                    v-model=\"gtForm.gtcz\"\n                    placeholder=\"请选择杆塔材质\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in gtczOptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"运行班组\" prop=\"yxbz\">\n                  <el-select\n                    v-model=\"gtForm.yxbz\"\n                    placeholder=\"请选择运行班组\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in deviceNameOptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.label\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"所属部门\" prop=\"ssbm\">\n                  <el-input\n                    v-model=\"gtForm.ssbm\"\n                    placeholder=\"请输入所属部门\"\n                    disabled\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"电压等级\" prop=\"dydj\">\n                  <el-select\n                    v-model=\"gtForm.dydj\"\n                    placeholder=\"请选择电压等级\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in gtoptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔型式\" prop=\"gtxs\">\n                  <el-select\n                    v-model=\"gtForm.gtxs\"\n                    :placeholder=\"gtshow ? '' : '请选择杆塔型式'\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in gtxsOptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔性质\" prop=\"gtNature\">\n                  <el-select\n                    v-model=\"gtForm.gtNature\"\n                    placeholder=\"请选择杆塔性质\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in gtxzList\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item\n                  label=\"杆塔形状\"\n                  prop=\"gtxz\"\n                  v-if=\"this.gtForm.dydj !== '6kV'\"\n                >\n                  <el-select\n                    v-model=\"gtForm.gtxz\"\n                    placeholder=\"请选择杆塔形状\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in gtxzOptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"相序\" prop=\"xx\">\n                  <el-select\n                    v-model=\"gtForm.xx\"\n                    :placeholder=\"gtshow ? '' : '请选择相序'\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in xxOptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\" v-if=\"this.gtForm.dydj !== '6kV'\">\n                <el-form-item label=\"杆塔呼称高(m)\" prop=\"gthcg\">\n                  <el-input\n                    v-model=\"gtForm.gthcg\"\n                    type=\"number\"\n                    placeholder=\"请输入杆塔呼称高\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔高度(m)\" prop=\"towerHeight\">\n                  <el-input\n                    v-model=\"gtForm.towerHeight\"\n                    type=\"number\"\n                    placeholder=\"请输入杆塔高度\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n\n              <el-col :span=\"8\">\n                <el-form-item\n                  label=\"至上基塔档距(m)\"\n                  prop=\"zsjtdj\"\n                  v-if=\"this.gtForm.dydj !== '6kV'\"\n                >\n                  <el-input\n                    v-model=\"gtForm.zsjtdj\"\n                    type=\"number\"\n                    placeholder=\"请输入至上基塔档距\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"导线排列方式\" prop=\"dxplfs\">\n                  <el-select\n                    v-model=\"gtForm.dxplfs\"\n                    :placeholder=\"gtshow ? '' : '请选择导线排列方式'\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in dxplOptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"是否同杆并架\" prop=\"sftgbj\">\n                  <el-select\n                    v-model=\"gtForm.sftgbj\"\n                    :placeholder=\"gtshow ? '' : '请选择是否同杆并架'\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in sfOptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔规格型号\" prop=\"gtggxh\">\n                  <el-input\n                    v-model=\"gtForm.gtggxh\"\n                    :placeholder=\"gtshow ? '' : '请输入杆塔规格型号'\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"坐标经度\" prop=\"jd\">\n                  <el-input\n                    v-model=\"gtForm.jd\"\n                    placeholder=\"请输入坐标经度\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"坐标纬度\" prop=\"wd\">\n                  <el-input\n                    v-model=\"gtForm.wd\"\n                    placeholder=\"请输入坐标纬度\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"同杆架设回路数\" prop=\"tgjshls\">\n                  <el-input\n                    v-model=\"gtForm.tgjshls\"\n                    :placeholder=\"gtshow ? '' : '请输入同杆架设回路数'\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"同杆线路位置\" prop=\"tgxlwz\">\n                  <el-input\n                    v-model=\"gtForm.tgxlwz\"\n                    :placeholder=\"gtshow ? '' : '请输入同杆线路位置'\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item\n                  label=\"是否换相\"\n                  prop=\"sfhx\"\n                  v-if=\"this.gtForm.dydj !== '6kV'\"\n                >\n                  <el-select\n                    v-model=\"gtForm.sfhx\"\n                    placeholder=\"请选择是否换相\"\n                    clearable\n                  >\n                    <el-option\n                      v-for=\"item in sfOptions\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"投运日期\" class=\"add_sy_tyrq\" prop=\"tyrq\">\n                  <el-date-picker\n                    v-model=\"gtForm.tyrq\"\n                    type=\"date\"\n                    placeholder=\"选择日期\"\n                    format=\"yyyy-MM-dd\"\n                    value-format=\"yyyy-MM-dd\"\n                  >\n                  </el-date-picker>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"接地体材料\" prop=\"jdtcl\">\n                  <el-select\n                    v-model=\"gtForm.jdtcl\"\n                    :placeholder=\"gtshow ? '' : '请选择接地体材料'\"\n                    clearable\n                  >\n                    <el-option\n                    v-for=\"item in jdtclOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                    />\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"导线型号\" prop=\"dxxh\">\n                  <el-input\n                    v-model=\"gtForm.dxxh\"\n                    :placeholder=\"gtshow ? '' : '请输入导线型号'\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item\n                  label=\"地线型号\"\n                  prop=\"jdxh\"\n                  v-if=\"this.gtForm.dydj !== '6kV'\"\n                >\n                  <el-input\n                    v-model=\"gtForm.jdxh\"\n                    placeholder=\"请输入地线型号\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"设备状态\" prop=\"sbzk\">\n                  <el-select\n                    v-model=\"gtForm.sbzt\"\n                    :placeholder=\"gtshow ? '' : '请选择设备状况'\"\n                  >\n                    <el-option\n                      v-for=\"item in gtsbzt\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔排序\" prop=\"gtnum\">\n                  <el-input\n                    v-model=\"gtForm.gtnum\"\n                    type=\"number\"\n                    placeholder=\"请输入杆塔排序\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"电缆型号\" prop=\"dlxh\">\n                  <el-input\n                    v-model=\"gtForm.dlxh\"\n                    :placeholder=\"gtshow ? '' : '请输入电缆型号'\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-form-item label=\"备注\" prop=\"bz\">\n                <el-input\n                  v-model=\"gtForm.bz\"\n                  type=\"textarea\"\n                  :row=\"2\"\n                  :placeholder=\"gtshow ? '' : '备注'\"\n                ></el-input>\n              </el-form-item>\n            </el-row>\n\n            <el-row :gutter=\"20\">\n              <el-form-item\n                label=\"已上传图片：\"\n                prop=\"attachment\"\n                v-if=\"gtForm.attachment.length > 0\"\n                id=\"pic_form\"\n              >\n                <el-col\n                  :span=\"24\"\n                  v-for=\"(item, index) in gtForm.attachment\"\n                  style=\"margin-left: 0\"\n                >\n                  <el-form-item :label=\"(index + 1).toString()\">\n                    {{ item.fileOldName }}\n                    <el-button\n                      v-if=\"!show\"\n                      type=\"error\"\n                      size=\"mini\"\n                      @click=\"deleteFileById(item.fileId)\"\n                      >删除</el-button\n                    >\n                  </el-form-item>\n                </el-col>\n              </el-form-item>\n            </el-row>\n            <el-row :gutter=\"20\">\n              <el-form-item label=\"上传图片：\" v-if=\"!show\">\n                <el-upload\n                  list-type=\"picture-card\"\n                  class=\"upload-demo\"\n                  accept=\".jpg,.png\"\n                  ref=\"upload\"\n                  :headers=\"upHeader\"\n                  action=\"/isc-api/file/upload\"\n                  :before-upload=\"beforeUpload\"\n                  :data=\"uploadData\"\n                  single\n                  :auto-upload=\"false\"\n                  multiple\n                >\n                  <el-button slot=\"trigger\" type=\"primary\">选取文件</el-button>\n                  <div slot=\"tip\" class=\"el-upload__tip\">\n                    只能上传jpg/png文件\n                  </div>\n                </el-upload>\n              </el-form-item>\n            </el-row>\n          </el-form>\n        </el-tab-pane>\n        <!--设备履历-->\n        <el-tab-pane label=\"设备履历\" name=\"sbRecord\">\n          <el-tabs v-model=\"sbllDescTabName\" type=\"card\">\n            <el-tab-pane label=\"隐患记录\" name=\"qxjl\">\n              <el-table\n                stripe\n                border\n                v-loading=\"loading\"\n                :data=\"sbllqxjlList\"\n                max-height=\"550\"\n              >\n                <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n                <el-table-column\n                  label=\"所属线路\"\n                  align=\"center\"\n                  prop=\"lineName\"\n                />\n                <el-table-column\n                  label=\"杆塔号\"\n                  align=\"center\"\n                  prop=\"gth\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"设备类型\"\n                  align=\"center\"\n                  prop=\"sblx\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"隐患性质\"\n                  align=\"center\"\n                  prop=\"qxxz\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"电压等级\"\n                  align=\"center\"\n                  prop=\"dydj\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column label=\"设备型号\" align=\"center\" prop=\"sbxh\" />\n                <el-table-column\n                  label=\"生产厂家\"\n                  align=\"center\"\n                  prop=\"sccj\"\n                  :show-overflow-tooltip=\"true\"\n                />\n              </el-table>\n            </el-tab-pane>\n            <el-tab-pane label=\"状态变更记录\" name=\"ztbgjl\">\n              <comp-table\n                :table-and-page-info=\"gtresumPageInfo\"\n                @getMethod=\"getResumList\"\n                @update:multipleSelection=\"handleSelectionChange\"\n                height=\"30vh\"\n              />\n            </el-tab-pane>\n          </el-tabs>\n        </el-tab-pane>\n      </el-tabs>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"gtDialogFormVisible = false\">取 消</el-button>\n        <el-button\n          v-if=\"title == '修改杆塔信息' || title == '新增杆塔信息'\"\n          type=\"primary\"\n          @click=\"addGtInfo\"\n          >确 定</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <!-- 杆塔弹出框开始展示设备履历 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"sbDialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"60%\"\n      @close=\"handleClose\"\n    >\n      <el-tabs v-model=\"activeTabName\">\n        <el-tab-pane label=\"基本信息\" name=\"sbDesc\">\n          <el-form\n            :model=\"jbxxForm\"\n            ref=\"jbxxForm\"\n            :disabled=\"show\"\n            :rules=\"rules\"\n            label-width=\"130px\"\n          >\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-form-item label=\"所属线路\" prop=\"lineName\">\n                  <el-input\n                    v-model=\"jbxxForm.lineName\"\n                    placeholder=\"请输入所属线路\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"所属杆塔\" prop=\"gtmc\">\n                  <el-input\n                    v-model=\"jbxxForm.gtmc\"\n                    placeholder=\"请输入所属杆塔\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"设备类型\" prop=\"sbflbm\">\n                  <el-select\n                    v-model=\"jbxxForm.sbflbm\"\n                    placeholder=\"请输入选择设备类型\"\n                    @change=\"showParams\"\n                  >\n                    <el-option\n                      v-for=\"item in sblxOptionsDataSelected\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"设备名称\" prop=\"sbmc\">\n                  <el-input\n                    v-model=\"jbxxForm.sbmc\"\n                    placeholder=\"请输入设备名称\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"投运日期\" prop=\"tyrq\">\n                  <el-date-picker\n                    v-model=\"jbxxForm.tyrq\"\n                    type=\"date\"\n                    placeholder=\"选择日期\"\n                    format=\"yyyy-MM-dd\"\n                    value-format=\"yyyy-MM-dd\"\n                  >\n                  </el-date-picker>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"规格型号\" prop=\"ggxh\">\n                  <el-input\n                    v-model=\"jbxxForm.ggxh\"\n                    :placeholder=\"show ? '' : '请选择规格型号'\"\n                  >\n                  </el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"状态\" prop=\"yxzt\">\n                  <el-select v-model=\"jbxxForm.yxzt\" placeholder=\"请选择状态\">\n                    <el-option\n                      v-for=\"item in sbzt\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"生产厂家\" prop=\"sccj\">\n                  <el-input\n                    v-model=\"jbxxForm.sccj\"\n                    :placeholder=\"show ? '' : '请输入生产厂家'\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row :gutter=\"20\">\n              <el-col :span=\"24\">\n                <el-form-item label=\"备注\" prop=\"bz\">\n                  <el-input\n                    v-model=\"jbxxForm.bz\"\n                    type=\"textarea\"\n                    rows=\"2\"\n                  ></el-input>\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </el-form>\n        </el-tab-pane>\n        <el-tab-pane label=\"技术参数\" name=\"sbjscs\">\n          <el-form :model=\"jscsForm\" label-width=\"130px\">\n            <el-col :span=\"8\" v-for=\"item in jscsLabelList\">\n              <el-form-item\n                :label=\"\n                  item.dw != '' ? item.label + '(' + item.dw + ')' : item.label\n                \"\n              >\n                <el-input\n                  v-if=\"item.type === 'input'\"\n                  v-model=\"jscsForm[item.jscsbm]\"\n                  placeholder=\"\"\n                >\n                </el-input>\n                <el-select\n                  v-if=\"item.type === 'select'\"\n                  v-model=\"jscsForm[item.jscsbm]\"\n                  placeholder=\"\"\n                >\n                  <el-option\n                    v-for=\"(childItem, key) in item.options\"\n                    :key=\"key\"\n                    :label=\"childItem.label\"\n                    :value=\"childItem.value\"\n                    :disabled=\"childItem.disabled\"\n                    style=\"display: flex; align-items: center;\"\n                    clearable\n                  >\n                  </el-option>\n                </el-select>\n                <el-date-picker\n                  v-if=\"item.type === 'date'\"\n                  v-model=\"jscsForm[item.jscsbm]\"\n                  type=\"date\"\n                  placeholder=\"选择日期\"\n                  value-format=\"yyyy-MM-dd\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-form>\n        </el-tab-pane>\n        <el-tab-pane label=\"设备履历\" name=\"sbRecord\">\n          <el-tabs v-model=\"sbllDescTabName\" type=\"card\">\n            <el-tab-pane label=\"缺陷记录\" name=\"qxjl\">\n              <el-table\n                stripe\n                border\n                v-loading=\"loading\"\n                :data=\"sbllqxjlList\"\n                max-height=\"550\"\n              >\n                <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n                <el-table-column\n                  label=\"所属线路\"\n                  align=\"center\"\n                  prop=\"lineName\"\n                />\n                <el-table-column\n                  label=\"杆塔号\"\n                  align=\"center\"\n                  prop=\"gth\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"设备类型\"\n                  align=\"center\"\n                  prop=\"sblx\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"缺陷性质\"\n                  align=\"center\"\n                  prop=\"qxxz\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column\n                  label=\"电压等级\"\n                  align=\"center\"\n                  prop=\"dydj\"\n                  :show-overflow-tooltip=\"true\"\n                />\n                <el-table-column label=\"设备型号\" align=\"center\" prop=\"sbxh\" />\n                <el-table-column\n                  label=\"生产厂家\"\n                  align=\"center\"\n                  prop=\"sccj\"\n                  :show-overflow-tooltip=\"true\"\n                />\n              </el-table>\n            </el-tab-pane>\n            <el-tab-pane label=\"状态变更记录\" name=\"ztbgjl\">\n              <comp-table\n                :table-and-page-info=\"resumPageInfo\"\n                @getMethod=\"getResumList\"\n                @update:multipleSelection=\"handleSelectionChange\"\n                height=\"30vh\"\n              />\n            </el-tab-pane>\n          </el-tabs>\n        </el-tab-pane>\n      </el-tabs>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"handleClose\">取 消</el-button>\n        <el-button\n          v-if=\"title == '修改输电设备信息' || title == '新增输电设备信息'\"\n          type=\"primary\"\n          @click=\"submit\"\n          class=\"pmyBtn\"\n          >确 定</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <!-- 修改设备状态变更-->\n    <el-dialog\n      title=\"设备状态变更\"\n      :visible.sync=\"dialogVisible\"\n      width=\"30%\"\n      append-to-body\n    >\n      <el-form :inline=\"true\" label-width=\"100px\" class=\"qxlr_dialog_insert\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"设备状态:\">\n              <el-select v-model=\"updateList.sbzt\">\n                <el-option\n                  v-for=\"item in sbzt\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitStatus\">确 定</el-button>\n      </span>\n    </el-dialog>\n\n    <el-dialog\n      :title=\"ExcelImportTitle\"\n      :visible.sync=\"openExcelDialog\"\n      width=\"700px\"\n      :close-on-click-modal=\"false\"\n      v-dialogDrag\n    >\n      <el-form label-position=\"right\" label-width=\"180px\">\n        <el-row>\n          <el-col :span=\"17\">\n            <el-form-item label=\"上传文件\" prop=\"fileName\">\n              <el-input\n                v-model=\"fileName\"\n                :readonly=\"true\"\n                placeholder=\"请选择文件\"\n                style=\"width:200px;\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-upload\n              action=\"/manager-api/tower/importExcel\"\n              ref=\"upload\"\n              :data=\"uploadData\"\n              accept=\".xls,.xlsx\"\n              :limit=\"1\"\n              :file-list=\"fileList\"\n              :auto-upload=\"false\"\n              :on-success=\"uploadSuccess\"\n              :on-change=\"handleChange\"\n              :on-remove=\"handleRemove\"\n              :headers=\"upHeader\"\n              :on-exceed=\"handleExceed\"\n            >\n              <el-button slot=\"trigger\" type=\"primary\" plain\n                >选取文件</el-button\n              >\n              <div\n                slot=\"tip\"\n                class=\"el-upload__tip\"\n                style=\"width: 220px;color: red\"\n              >\n                只能上传excel文件，且不超过100MB\n              </div>\n            </el-upload>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\" style=\"margin-top: 30px\">\n        <el-button type=\"primary\" @click=\"submitExcelForm\" :loading=\"isloading\"\n          >确 定</el-button\n        >\n        <el-button @click=\"cancelImport\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"addTowerBatchDialogFormVisible\"\n      width=\"50%\"\n      v-dialogDrag\n    >\n      <el-form\n        label-width=\"120px\"\n        ref=\"addTowerBatchForm\"\n        :model=\"addTowerBatchForm\"\n        :rules=\"addTowerBatchRules\"\n      >\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"towerStartNum\" label=\"杆塔起始编号\">\n              <el-input-number\n                :min=\"1\"\n                :max=\"99\"\n                style=\"width: 100%\"\n                v-model=\"addTowerBatchForm.towerStartNum\"\n                placeholder=\"请输入杆塔起始编号\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"towerEndNum\" label=\"杆塔结束编号\">\n              <el-input-number\n                :min=\"this.addTowerBatchForm.towerStartNum\"\n                :max=\"99\"\n                style=\"width: 100%\"\n                v-model=\"addTowerBatchForm.towerEndNum\"\n                placeholder=\"请输入杆塔结束编号\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"towerNamePrefix\" label=\"杆塔名称前缀\">\n              <el-input\n                style=\"width: 100%\"\n                v-model=\"addTowerBatchForm.towerNamePrefix\"\n                placeholder=\"请输入杆塔名称前缀\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"towerNameSuffix\" label=\"杆塔名称后缀\">\n              <el-input\n                style=\"width: 100%\"\n                v-model=\"addTowerBatchForm.towerNameSuffix\"\n                placeholder=\"请输入杆塔名称后缀\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"towerNameLinkFlag\" label=\"杆塔名称连接符\">\n              <el-input\n                style=\"width: 100%\"\n                v-model=\"addTowerBatchForm.towerNameLinkFlag\"\n                placeholder=\"请输入杆塔名称连接符\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"towerNumberPrefix\" label=\"杆塔编号前缀\">\n              <el-input\n                style=\"width: 100%\"\n                v-model=\"addTowerBatchForm.towerNumberPrefix\"\n                placeholder=\"请输入杆塔编号前缀\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"towerNumberSuffix\" label=\"杆塔编号后缀\">\n              <el-input\n                style=\"width: 100%\"\n                v-model=\"addTowerBatchForm.towerNumberSuffix\"\n                placeholder=\"请输入编号后缀\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"lineName\" label=\"杆塔所属线路\">\n              <el-input\n                disabled\n                style=\"width: 100%\"\n                v-model=\"addTowerBatchForm.lineName\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <!-- <el-form-item prop=\"towerNameExample\" label=\"杆塔名称生成示例: \">\n              <el-input\n                disabled\n                style=\"width: 100%\"\n                v-model=\"towerNameExample\"\n              />\n            </el-form-item> -->\n            <h2>杆塔名称生成示例:{{ towerNameExample }}</h2>\n          </el-col>\n          <el-col :span=\"12\">\n            <!-- <el-form-item prop=\"towerNumberExample\" label=\"杆塔编号生成示例: \">\n              <el-input\n                disabled\n                style=\"width: 100%\"\n                v-model=\"towerNumberExample\"\n              />\n            </el-form-item> -->\n            <h2>杆塔编号生成示例:{{ towerNumberExample }}</h2>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"addTowerBatchDialogFormVisible = false\"\n          >关 闭</el-button\n        >\n        <el-button type=\"primary\" @click=\"saveTowerBatchButton\"\n          >确 认</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <el-dialog title=\"支线维护\" :visible.sync=\"showZxwh\" width=\"60%\" v-dialogDrag @close=\"closeZxwhFun\" v-if=\"showZxwh\">\n      <zxwh :xl-data=\"xlData\"></zxwh>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  getList,\n  getResumDataList,\n  getTreeList,\n  remove,\n  saveOrUpdate,\n  updateStatus,\n  adddwzyfstz,\n  exportExcel,\n  importExcel\n} from \"@/api/dagangOilfield/asset/sdsb\";\nimport {\n  getListxl,\n  saveOrUpdatexl,\n  xlremove\n} from \"@/api/dagangOilfield/asset/sdxl\";\nimport {\n  getListgt,\n  saveOrUpdategt,\n  gtremove,\n  saveTowerBatch\n} from \"@/api/dagangOilfield/asset/sdgt\";\nimport {\n  getParamDataList,\n  getParamsValue,\n  saveParamValue\n} from \"@/api/dagangOilfield/asset/parameters\";\nimport {\n  getSblxDataListSelected,\n  getOrganizationSelected\n} from \"@/api/dagangOilfield/asset/bdsbtz\";\nimport { getListTZ } from \"@/api/dagangOilfield/bzgl/bzgfgl/bzgfgl\";\nimport { getToken } from \"@/utils/auth\";\nimport zxwh from \"@/views/dagangOilfield/dwzygl/sdsbgl/zxwh\";\nimport {deleteById} from \"@/api/tool/file\";\nimport {getDictTypeData} from \"@/api/system/dict/data\";\n\nexport default {\n  name: \"qxbzk\",\n  components: {zxwh},\n  data() {\n    var validateTowerEndNum = (rule, value, callback) => {\n      if (!this.addTowerBatchForm.towerEndNum) {\n        callback(new Error(\"不能为空\"));\n      }\n      if (\n        this.addTowerBatchForm.towerEndNum <\n        this.addTowerBatchForm.towerStartNum\n      ) {\n        callback(new Error(\"杆塔结束编号不能小于起始编号\"));\n      } else {\n        callback();\n      }\n    };\n    return {\n      ids: [],\n\n      icons: {\n        1: \"categoryTreeIcons\",\n        2: \"tableIcon\",\n        3: \"classIcon\",\n        4: \"classIcon2\"\n      },\n      params: {\n        pageNum: 1,\n        pageSize: 10,\n      },\n      resumeQuery: {\n        foreignNum: undefined,\n        sblx: undefined\n      },\n      updateList: {\n        sbzt: \"\",\n        objId: \"\"\n      },\n      dialogVisible: false,\n      options: [\n        {\n          value: \"110kV\",\n          label: \"110kV\"\n        },\n        {\n          value: \"35kV\",\n          label: \"35kV\"\n        },\n        {\n          value: \"10kV\",\n          label: \"10kV\"\n        },\n        {\n          value: \"6kV\",\n          label: \"6kV\"\n        }\n      ],\n      sbzt: [\n        {\n          value: \"在运\",\n          label: \"在运\"\n        },\n        {\n          value: \"停运\",\n          label: \"停运\"\n        },\n        {\n          value: \"报废\",\n          label: \"报废\"\n        }\n      ],\n\n      //电压等级下拉框\n      voltageLevelListSelected: [],\n      deviceNameOptions: [],\n      sbfilterInfo: {\n        data: {\n          ssxl: [],\n          ssgs: [],\n          sbzt: \"\",\n          yxbz: []\n        },\n        fieldList: [\n          { label: \"所属杆塔名称\", type: \"input\", value: \"gtmc\" },\n          { label: \"设备类型\", type: \"input\", value: \"sblxmc\" },\n          { label: \"设备名称\", type: \"input\", value: \"sbmc\" },\n          {\n            label: \"运行状态\",\n            type: \"select\",\n            value: \"yxzt\",\n            options: [\n              { label: \"在运\", value: \"110kV\" },\n              { label: \"停运\", value: \"35kV\" }\n            ]\n          },\n          {\n            label: \"投运日期\",\n            type: \"date\",\n            value: \"tyrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"生产厂家\", type: \"input\", value: \"sscj\" }\n        ]\n      },\n\n      tableAndPageInfo3: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"lineName\", label: \"所属线路\", minWidth: \"120\" },\n          { prop: \"gtmc\", label: \"所属杆塔名称\", minWidth: \"120\" },\n          { prop: \"sblxmc\", label: \"设备类型\", minWidth: \"120\" },\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"180\" },\n          { prop: \"ggxh\", label: \"规格型号\", minWidth: \"180\" },\n          { prop: \"yxzt\", label: \"运行状态\", minWidth: \"120\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"250\" },\n          { prop: \"sccj\", label: \"生产厂家\", minWidth: \"140\" }\n          /*{\n            fixed:\"right\",\n            prop: 'operation',\n            label: '操作',\n            minWidth: '130px',\n            style: {display: 'block'},\n            operation: [\n              {name: '修改', clickFun: this.updateRow},\n              {name: '详情', clickFun: this.detailsInfo}\n            ]\n          },*/\n        ]\n      },\n\n      resumPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"foreignNum\", label: \"设备名称\", minWidth: \"120\" },\n          { prop: \"sblx\", label: \"设备类型\", minWidth: \"180\" },\n          { prop: \"bglx\", label: \"变更类型\", minWidth: \"120\" },\n          { prop: \"ms\", label: \"描述\", minWidth: \"250\" },\n          { prop: \"bgr\", label: \"变更人\", minWidth: \"140\" },\n          { prop: \"bgsj\", label: \"变更时间\", minWidth: \"140\" }\n        ]\n      },\n\n      //杆塔挂接设备tab页\n      gtgjsbTabName: \"jc\",\n      //杆塔详情弹出框\n      sbDialogFormVisible: false,\n      //设备履历状态变更记录\n      sbllztbgjlList: [],\n      //设备履历缺陷记录数据集合\n      sbllqxjlList: [\n        /*{\n          ssgs: '110kVXXX线路',\n          gth: '001#',\n          sblx: '杆塔',\n          qxxz: '严重',\n          dydj: '35kV',\n          sbxh: 'XXX型号',\n          sccj: 'XXX厂家'\n        }, {\n          ssgs: '110kVXXX线路',\n          gth: '001#',\n          sblx: '杆塔',\n          qxxz: '严重',\n          dydj: '35kV',\n          sbxh: 'XXX型号',\n          sccj: 'XXX厂家'\n        }, {\n          ssgs: '110kVXXX线路',\n          gth: '001#',\n          sblx: '杆塔',\n          qxxz: '严重',\n          dydj: '35kV',\n          sbxh: 'XXX型号',\n          sccj: 'XXX厂家'\n        }, {\n          ssgs: '110kVXXX线路',\n          gth: '001#',\n          sblx: '杆塔',\n          qxxz: '严重',\n          dydj: '35kV',\n          sbxh: 'XXX型号',\n          sccj: 'XXX厂家'\n        }, {\n          ssgs: '110kVXXX线路',\n          gth: '001#',\n          sblx: '杆塔',\n          qxxz: '严重',\n          dydj: '35kV',\n          sbxh: 'XXX型号',\n          sccj: 'XXX厂家'\n        }*/\n      ],\n      //设备履历试验记录数据\n      sblvsyjlList: [\n        /* {\n          syzy: '带电',\n          syxz: '例行试验',\n          symc: 'XXXXX',\n          gzdd: 'XXX平台',\n          sysb: '主变压器',\n          sybg: '',\n          tq: '晴',\n          syrq: '2022-01-01',\n          lrr: '张三',\n          syjl: 'XXXXX'\n        }, {\n          syzy: '带电',\n          syxz: '例行试验',\n          symc: 'XXXXX',\n          gzdd: 'XXX平台',\n          sysb: '主变压器',\n          sybg: '',\n          tq: '晴',\n          syrq: '2022-01-01',\n          lrr: '张三',\n          syjl: 'XXXXX'\n        }, {\n          syzy: '带电',\n          syxz: '例行试验',\n          symc: 'XXXXX',\n          gzdd: 'XXX平台',\n          sysb: '主变压器',\n          sybg: '',\n          tq: '晴',\n          syrq: '2022-01-01',\n          lrr: '张三',\n          syjl: 'XXXXX'\n        }, {\n          syzy: '带电',\n          syxz: '例行试验',\n          symc: 'XXXXX',\n          gzdd: 'XXX平台',\n          sysb: '主变压器',\n          sybg: '',\n          tq: '晴',\n          syrq: '2022-01-01',\n          lrr: '张三',\n          syjl: 'XXXXX'\n        }, {\n          syzy: '带电',\n          syxz: '例行试验',\n          symc: 'XXXXX',\n          gzdd: 'XXX平台',\n          sysb: '主变压器',\n          sybg: '',\n          tq: '晴',\n          syrq: '2022-01-01',\n          lrr: '张三',\n          syjl: 'XXXXX'\n        }, {\n          syzy: '带电',\n          syxz: '例行试验',\n          symc: 'XXXXX',\n          gzdd: 'XXX平台',\n          sysb: '主变压器',\n          sybg: '',\n          tq: '晴',\n          syrq: '2022-01-01',\n          lrr: '张三',\n          syjl: 'XXXXX'\n        }*/\n      ],\n      //设备履历tab页\n      sbllDescTabName: \"qxjl\",\n      show: false,\n      //设备基本信息表单\n      jbxxForm: {\n        objId: undefined,\n        ssxl: \"\",\n        ssgtbh: \"\"\n      },\n      //设备详情页底部确认取消按钮控制\n      sbCommitDialogCotrol: true,\n      //弹出框tab页\n      activeTabName: \"sbDesc\",\n      //变电站展示\n      bdzShowTable: true,\n      //间隔展示\n      jgShowTable: false,\n      //设备展示\n      sbShowTable: false,\n      //设备弹出框\n      dialogFormVisible: false,\n      //变电站添加按钮弹出框\n      bdzDialogFormVisible: false,\n      //间隔添加按钮弹出框\n      jgDialogFormVisible: false,\n      //弹出框表单\n      form: {},\n\n      loading: false,\n      //组织树\n      treeOptions: [],\n\n      selectRows: [],\n      //变电站挂接数据\n      newTestData: [],\n\n      //删除是否可用\n      multipleSensor: true,\n      //查询参数\n      /*       queryParams: {\n               pageNum: 1,\n               pageSize: 10,\n               roleKey: '',\n               roleName: '',\n               status: '',\n             },*/\n      showSearch: true,\n      //ssgtbh sbdm sbmc sbflbm bgr tyrq zcxz yxzt dydj sccj ccbh ccrq ggxh\n      rules: {\n        // ssgtbh:[{required:true,message:'请输入所属杆塔',trigger:'blur'}],\n        // sbdm:[{required:true,message:'请输入设备代码',trigger:'blur'}],\n        sbmc: [{ required: true, message: \"请输入设备名称\", trigger: \"blur\" }],\n        sbflbm: [\n          { required: true, message: \"请选择设备类型\", trigger: \"change\" }\n        ]\n        // bgr:[{required:true,message:'请输入保管人',trigger:'blur'}],\n        // yxzt:[{required:true,message:'请选择状态',trigger:'change'}],\n        // dydj:[{required:true,message:'请选择电压等级',trigger:'change'}],\n        // sccj:[{required:true,message:'请输入生产厂家',trigger:'blur'}],\n        // ccbh:[{required:true,message:'请输入出场编号',trigger:'blur'}]\n      },\n\n      jscsLabelList: [],\n      //技术参数绑定\n      jscsForm: {},\n      paramQuery: {\n        sblxbm: undefined\n      },\n      sblxOptionsDataSelected: {},\n      xltzData: true,\n      sbtzData: false,\n      gttzData: false,\n      title: \"\",\n      //线路数据相关\n      tableAndPageInfo1: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"lineName\", label: \"线路名称\", minWidth: \"120\" },\n          { prop: \"dydj\", label: \"电压等级\", minWidth: \"120\" },\n          { prop: \"lineStatus\", label: \"线路状态\", minWidth: \"120\" },\n          { prop: \"lineType\", label: \"线路类型\", minWidth: \"120\" },\n          // {prop: 'sfzgx', label: '是否主干线', minWidth: '140'},\n          { prop: \"totalLength\", label: \"线路全长(KM)\", minWidth: \"120\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"120\" }\n          /*{\n            fixed: \"right\",\n            prop: 'operation',\n            label: '操作',\n            minWidth: '130px',\n            style: {display: 'block'},\n            operation: [\n              {name: '修改', clickFun: this.updateRow1},\n              {name: '详情', clickFun: this.detailsInfo1}\n            ]\n          },*/\n        ]\n      },\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        dydjbm: \"\",\n        lineName: \"\",\n        lineType: \"\",\n        lineStatus: \"\"\n      },\n      //设备弹出框\n      xlDialogFormVisible: false,\n      xlshow: false,\n      //设备基本信息\n      xlForm: {\n        objId: undefined\n      },\n      //线路类型\n      lineTypeOptions: [\n        { label: \"输电线路\", value: \"输电线路\" },\n        { label: \"配电线路\", value: \"配电线路\" }\n      ],\n      //线路状态\n      xlztOptions: [\n        { label: \"在运\", value: \"在运\" },\n        { label: \"停运\", value: \"停运\" }\n      ],\n      xloptions: [\n        {\n          value: \"110\",\n          label: \"110kV\"\n        },\n        {\n          value: \"35\",\n          label: \"35kV\"\n        },\n        {\n          value: \"10\",\n          label: \"10kV\"\n        },\n        {\n          value: \"6\",\n          label: \"6kV\"\n        }\n      ],\n      xlfilterInfo: {\n        data: {\n          dydjbm: \"\",\n          lineName: \"\",\n          lineType: \"\",\n          // sfzgx: '',\n          lineStatus: \"\"\n        },\n        fieldList: [\n          { label: \"线路名称\", type: \"input\", value: \"lineName\" },\n          {\n            label: \"线路全长\",\n            type: \"input\",\n            value: \"totalLength\",\n            options: []\n          },\n          {\n            label: \"投运日期\",\n            type: \"date\",\n            format: \"yyyy-MM-dd\",\n            options: []\n          },\n          {\n            label: \"电压等级\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"dydjbm\",\n            options: [\n              { label: \"110kV\", value: \"110\" },\n              { label: \"35kV\", value: \"35\" },\n              { label: \"10kV\", value: \"10\" },\n              { label: \"6kV\", value: \"6\" }\n            ]\n          },\n          {\n            label: \"状态\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"lineStatus\",\n            options: [\n              { label: \"在运\", value: \"在运\" },\n              { label: \"停运\", value: \"停运\" }\n            ]\n          },\n          {\n            label: \"线路类型\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"lineType\",\n            options: [\n              { label: \"输电线路\", value: \"输电线路\" },\n              { label: \"配电线路\", value: \"配电线路\" }\n            ]\n          }\n          // {\n          //   label: '是否主干线',\n          //   type: 'select',\n          //   value: 'sfzgx',\n          //   options: [{label: \"是\", value: \"是\"}, {label: \"否\", value: \"否\"},]\n          // },\n        ]\n      },\n      //查询杆塔参数   杆塔数据相关开始\n      //杆塔详情弹出框\n      gtDialogFormVisible: false,\n      //弹出框tab页\n      gtactiveTabName: \"sbDesc\",\n      //轮播图片\n      imgList: [],\n      xlImgList: [\n        //         {\n        //   fileUrl: require('@/assets/image/bdz.png')\n        // },\n        // {\n        //   fileUrl: require('@/assets/image/bdz.png')\n        // },\n        // {\n        //   fileUrl: require('@/assets/image/bdz.png')\n        // }\n      ],\n      queryGtParam: {\n        sbzt: \"\",\n        lineName: \"\",\n        dydj: \"\",\n        pageNum: 1,\n        pageSize: 10,\n        // mySorts: [{ prop: \"get_number(gtbh) + 1\", asc: true }]\n      },\n      // 文件上传数据\n      // uploadData: {\n      //   type: \"\",\n      //   businessId: undefined,\n      //   lineName : this.lineName\n      // },\n      // 文件上传请求头\n      upHeader: { token: getToken() },\n      //  文件导入弹出框相关\n      ExcelImportTitle: \"Excel导入\",\n      openExcelDialog: false,\n      // 文件上传请求头\n      //文件名\n      fileName: \"\",\n      //上传得文件数组\n      fileList: [],\n      isloading: false,\n      tableAndPageInfo2: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"lineName\", label: \"所属线路\", minWidth: \"120\" },\n          { prop: \"gtmc\", label: \"杆塔名称\", minWidth: \"130\" },\n          { prop: \"gtbh\", label: \"杆塔编号\", minWidth: \"90\",  },\n          { prop: \"dydj\", label: \"电压等级\", minWidth: \"60\" },\n          { prop: \"sbzt\", label: \"设备状态\", minWidth: \"60\" },\n          { prop: \"gtcz\", label: \"杆塔材质\", minWidth: \"70\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"80\" },\n          { prop: \"gtnum\", label: \"杆塔排序\", minWidth: \"70\",  }\n          /*{\n            fixed: 'right',\n            prop: 'operation',\n            label: '操作',\n            minWidth: '130px',\n            style: { display: 'block' },\n            operation: [\n              { name: '状态变更', clickFun: this.updateStatus },\n              { name: '修改', clickFun: this.updateRow2 },\n              { name: '详情', clickFun: this.detailsInfo2 }\n            ]\n          }*/\n        ]\n      },\n      //设备基本信息\n      gtForm: {\n        attachment: [],\n        objId: undefined,\n        gtbh: \"\",\n        ssbm: \"线路分公司\"\n      },\n      gtfilterInfo: {\n        data: {\n          sbzt: \"\",\n          lineName: \"\",\n          dydj: \"\"\n        },\n        fieldList: [\n          {\n            label: \"杆塔名称\",\n            type: \"input\",\n            value: \"gtmc\",\n            options: []\n          },\n          {\n            label: \"杆塔编号\",\n            type: \"input\",\n            value: \"gtbh\",\n            options: []\n          },\n          {\n            label: \"电压等级\",\n            type: \"select\",\n            value: \"dydj\",\n            options: [\n              { label: \"6kV\", value: \"6kV\" },\n              { label: \"10kV\", value: \"10kV\" },\n              {\n                label: \"35kV\",\n                value: \"35kV\"\n              },\n              { label: \"110kV\", value: \"110kV\" }\n            ]\n          },\n          {\n            label: \"设备状态\",\n            type: \"select\",\n            value: \"sbzt\",\n            options: [\n              { label: \"在运\", value: \"在运\" },\n              { label: \"停运\", value: \"停运\" }\n            ]\n          },\n          {\n            label: \"杆塔材质\",\n            type: \"input\",\n            value: \"gtcz\",\n            options: []\n          },\n          {\n            label: \"投运日期\",\n            type: \"date\",\n            value: \"tyrq\",\n            format: \"yyyy-MM-dd\"\n          }\n        ]\n      },\n      gtshow: false,\n      gtoptions: [],\n      gtsbzt: [],\n      //杆塔性质\n      gtxzList: [],\n      //杆塔形状结合\n      gtxzOptions: [],\n      uploadData: {\n        businessId: undefined\n      },\n      gtresumeQuery: {\n        foreignNum: undefined,\n        sblx: undefined\n      },\n      gtresumPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"foreignNum\", label: \"设备名称\", minWidth: \"120\" },\n          { prop: \"sblx\", label: \"设备类型\", minWidth: \"180\" },\n          { prop: \"bglx\", label: \"变更类型\", minWidth: \"120\" },\n          { prop: \"ms\", label: \"描述\", minWidth: \"250\" },\n          { prop: \"bgr\", label: \"变更人\", minWidth: \"140\" },\n          { prop: \"bgsj\", label: \"变更时间\", minWidth: \"140\" }\n        ]\n      },\n      xlRules: {\n        xlbm: [\n          { required: true, message: \"线路编码不能为空\", trigger: \"blur\" }\n        ],\n        lineName: [\n          { required: true, message: \"线路名称不能为空\", trigger: \"blur\" }\n        ],\n        lineType: [\n          { required: true, message: \"线路专业不能为空\", trigger: \"select\" }\n        ],\n        dydjbm: [\n          { required: true, message: \"电压等级不能为空\", trigger: \"select\" }\n        ],\n        totalLength: [\n          { required: true, message: \"线路全长不能为空\", trigger: \"blur\" }\n        ],\n        lineStatus: [\n          { required: true, message: \"线路状态不能为空\", trigger: \"select\" }\n        ]\n      },\n      gtRules: {\n        gtbh: [\n          { required: true, message: \"杆塔编号不能为空\", trigger: \"blur\" }\n        ],\n        gtnum: [\n          { required: true, message: \"杆塔排序不能为空\", trigger: \"blur\" }\n        ],\n        gtmc: [\n          { required: true, message: \"杆塔名称不能为空\", trigger: \"blur\" }\n        ],\n        lineName: [\n          { required: true, message: \"所属线路不能为空\", trigger: \"blur\" }\n        ],\n        gtcz: [\n          { required: true, message: \"杆塔材质不能为空\", trigger: \"select\" }\n        ],\n        yxbz: [\n          { required: true, message: \"运行班组不能为空\", trigger: \"select\" }\n        ],\n        ssbm: [\n          { required: true, message: \"所属部门不能为空\", trigger: \"blur\" }\n        ],\n        dydj: [\n          { required: true, message: \"电压等级不能为空\", trigger: \"select\" }\n        ]\n      },\n      dydjbm: \"\",\n      lineName: \"\",\n      linedIdStore: \"\",\n      gtbhStore: \"\",\n      gtmcStore: \"\",\n      addTowerBatchForm: {\n        towerNamePrefix: \"\",\n        towerNameSuffix: \"\",\n        towerNameLinkFlag: \"\",\n        towerNumberPrefix: \"\",\n        towerNumberSuffix: \"\",\n        towerStartNum: 1\n      },\n      addTowerBatchDialogFormVisible: false,\n      addTowerBatchRules: {\n        lineName: [\n          { required: true, message: \"所属线路不能为空\", trigger: \"blur\" }\n        ],\n        towerNamePrefix: [\n          { required: true, message: \"不能为空\", trigger: \"blur\" }\n        ],\n        towerNameSuffix: [\n          { required: true, message: \"不能为空\", trigger: \"blur\" }\n        ],\n        towerStartNum: [\n          { required: true, message: \"不能为空\", trigger: \"change\" }\n        ],\n        towerEndNum: [\n          { required: true, trigger: \"change\", validator: validateTowerEndNum }\n        ]\n      },\n      xlData:{},//传给支线维护页面的线路数据\n      showZxwh:false,//是否显示支线维护弹框\n      gtczOptions:[],//杆塔材质下拉框\n      gtxsOptions:[],//杆塔形式下拉框\n      xxOptions:[],//相序下拉框\n      dxplOptions:[],//导线排列下拉框\n      sfOptions:[],//是/否\n      jdtclOptions:[],//接地体材料\n    };\n  },\n  watch: {},\n  computed: {\n    towerNumberExample: function() {\n      return (\n        this.addTowerBatchForm.towerNumberPrefix +\n        this.addTowerBatchForm.towerStartNum +\n        this.addTowerBatchForm.towerNumberSuffix\n      );\n    },\n    towerNameExample: function() {\n      return (\n        this.addTowerBatchForm.towerNamePrefix +\n        this.addTowerBatchForm.towerStartNum +\n        this.addTowerBatchForm.towerNameLinkFlag +\n        this.addTowerBatchForm.towerNameSuffix\n      );\n    }\n  },\n  created() {\n    //初始化加载时加载所有变电站信息\n    this.treeList();\n    this.getSblxDataListSelected();\n    this.lineTzData(this.$route.query); //初始请求线路台账\n    this.getOptions();//获取下拉框字典\n  },\n  mounted() {\n    this.getOrganizationSelected();\n  },\n  methods: {\n    async getOptions(){\n      await this.getDydjList();//电压等级\n      await this.getGtczList();//杆塔材质\n      await this.getGtxsList();//杆塔形式\n      await this.getGtxzList();//杆塔性质\n      await this.getGtxzList1();//杆塔形状\n      await this.getGtxxList();//杆塔相序\n      await this.getDxplList();//导线排列\n      await this.getSfList();//是/否\n      await this.getJdtclList();//接地体材料\n      await this.getGtztList();//杆塔状态\n    },\n    getDydjList(){\n      getDictTypeData('gttz-dydj').then(res=>{\n        res.data.forEach(item=>{\n          this.gtoptions.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    getGtczList(){\n      getDictTypeData('gttz-gtcz').then(res=>{\n        res.data.forEach(item=>{\n          this.gtczOptions.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    getGtxsList(){\n      getDictTypeData('gttz-gtxs').then(res=>{\n        res.data.forEach(item=>{\n          this.gtxsOptions.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    getGtxzList(){\n      getDictTypeData('gttz-gtxz').then(res=>{\n        res.data.forEach(item=>{\n          this.gtxzList.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    getGtxzList1(){\n      getDictTypeData('gttz-gtxz1').then(res=>{\n        res.data.forEach(item=>{\n          this.gtxzOptions.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    getGtxxList(){\n      getDictTypeData('gttz-gtxx').then(res=>{\n        res.data.forEach(item=>{\n          this.xxOptions.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    getDxplList(){\n      getDictTypeData('gttz-dxpl').then(res=>{\n        res.data.forEach(item=>{\n          this.dxplOptions.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    getSfList(){\n      getDictTypeData('sys_sf').then(res=>{\n        res.data.forEach(item=>{\n          this.sfOptions.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    getJdtclList(){\n      getDictTypeData('gttz-jdtcl').then(res=>{\n        res.data.forEach(item=>{\n          this.jdtclOptions.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    getGtztList(){\n      getDictTypeData('gttz-gtzt').then(res=>{\n        res.data.forEach(item=>{\n          this.gtsbzt.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    //关闭支线维护弹框\n    closeZxwhFun(){\n      this.xlData = {};\n      this.showZxwh = false;\n    },\n    //支线维护方法\n    zxwhFun(row){\n      this.xlData = {...row};\n      this.showZxwh = true;\n    },\n    getVoltageLeVelList() {\n      this.sbfilterInfo.fieldList.forEach(item => {\n        if (item.value == \"ssgs\") {\n          item.options = [\n            { label: \"110kV\", value: \"110kV\" },\n            { label: \"35kV\", value: \"35kV\" },\n            { label: \"10kV\", value: \"10kV\" },\n            { label: \"6kV\", value: \"6kV\" }\n          ];\n        }\n      });\n    },\n    //获取班组\n    getOrganizationSelected() {\n      let parentId = \"3010\"; //线路分公司\n      getOrganizationSelected({ parentId: parentId }).then(res => {\n        res.data.forEach(item => {\n          this.deviceNameOptions.push({\n            label: item.label,\n            value: item.value.toString()\n          });\n        });\n        this.sbfilterInfo.fieldList.forEach(item => {\n          if (item.value === \"bz\") {\n            item.options = this.deviceNameOptions;\n            return false;\n          }\n        });\n      });\n    },\n    //保存设备基本信息\n    addEquipInfo: function() {\n      this.jbxxForm.sbClassCsValue = this.jscsForm;\n\n      saveOrUpdate(this.jbxxForm).then(res => {\n        if (res.code == \"0000\") {\n          this.$message.success(\"操作成功\");\n          this.sbDialogFormVisible = false;\n          this.getData({ ssgtbh: this.jbxxForm.ssgtbh });\n          return;\n        } else {\n          this.$message.warning(\"操作失败！\");\n        }\n      });\n    },\n    //初始进来发请求线路台账接口\n    lineTzData(params) {\n      this.queryParams = { ...this.queryParams, ...params };\n      const param = { ...this.queryParams, ...params };\n      getListxl(param).then(res => {\n        this.xltzData = true;\n        this.gttzData = this.sbtzData = false;\n        this.tableAndPageInfo1.tableData = res.data.records;\n        this.tableAndPageInfo1.pager.total = res.data.total;\n      });\n    },\n    //图纸查询\n    querySearch(queryString, cb) {\n      let param = {\n        // wjbh: queryString,\n        wjmc: queryString,\n        dydj: this.xlForm.dydjbm,\n        islast: \"1\",\n        wjlx: \"1\",\n        mySorts: [{ prop: \"updateTime\", asc: false }]\n      };\n      getListTZ(param).then(res => {\n        if (res.code === \"0000\") {\n          // 调用 callback 返回建议列表的数据\n          let data = res.data.records;\n          data.forEach(record => {\n            record.value = record.wjbh;\n          });\n          cb(data);\n        }\n      });\n    },\n    handleSelect(item) {\n      this.xlImgList = item.fileList.filter(\n        record => record.fileType !== \"vsd\"\n      );\n    },\n    //杆塔台账\n    async gtTzData(params) {\n      try {\n        this.queryGtParam = {\n          ...this.queryGtParam,\n          ...params,\n          ...{ lineName: this.lineName }\n        };\n        getListgt(this.queryGtParam).then(res => {\n          this.gttzData = true;\n          this.xltzData = this.sbtzData = false;\n          this.tableAndPageInfo2.tableData = res.data.records;\n          this.tableAndPageInfo2.pager.total = res.data.total;\n        });\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    sortChangeTowerData({ column, prop, order }) {\n      if (order) {\n        if (order.indexOf(\"desc\") > -1) {\n          if (prop === \"gtbh\") {\n            this.queryGtParam.mySorts = [\n              { prop: \"get_number(gtbh) + 0\", asc: false }\n            ];\n          } else {\n            this.queryGtParam.mySorts = [{ prop: prop, asc: false }];\n          }\n        } else {\n          if (prop === \"gtbh\") {\n            this.queryGtParam.mySorts = [\n              { prop: \"get_number(gtbh) + 0\", asc: true }\n            ];\n          } else {\n            this.queryGtParam.mySorts = [{ prop: prop, asc: true }];\n          }\n        }\n      } else {\n        this.queryGtParam.mySorts = [{ prop: \"get_number(gtbh)\", asc: true }];\n      }\n      this.gtTzData(this.gtfilterInfo.data);\n    },\n    //设备台账\n    sbTzData(params) {\n      const param = { ...this.params, ...params,...{ ssgtbh: this.gtbhStore } };\n      getList(param).then(res => {\n        this.sbtzData = true;\n        this.gttzData = this.xltzData = false;\n        this.tableAndPageInfo3.tableData = res.data.records;\n        this.tableAndPageInfo3.pager.total = res.data.total;\n      });\n    },\n    //获取列表数据\n    getData: function(params) {\n      if (this.xltzData) {\n        this.lineTzData(params);\n      }\n      if (this.gttzData) {\n        this.gtTzData(params);\n      }\n      if (this.sbtzData) {\n        this.sbTzData(params);\n      }\n    },\n    //线路修改操作\n    updateRow1(row) {\n      this.title = \"修改线路信息\";\n      this.xlForm = { ...row };\n      this.getImgList(row.xlbm);\n      this.xlDialogFormVisible = true;\n      this.xlshow = false;\n    },\n    //线路查看详情\n    detailsInfo1(row) {\n      this.title = \"线路详情\";\n      this.xlForm = { ...row };\n      this.getImgList(row.xlbm);\n      this.xlDialogFormVisible = true;\n      this.xlshow = true;\n    },\n    jumpToTzgl(row) {\n      row;\n    },\n    getImgList(wjbh) {\n      this.xlImgList = [];\n      let param = {\n        wjbh: wjbh,\n        islast: \"1\",\n        wjlx: \"1\"\n      };\n      getListTZ(param).then(res => {\n        if (res.code === \"0000\") {\n          let data = res.data.records[0].fileList.filter(\n            record => record.fileType !== \"vsd\"\n          );\n          this.xlImgList = data;\n        }\n      });\n    },\n    //线路取消弹框\n    resetxlForm() {\n      this.xlForm = {};\n      this.$nextTick(function() {\n        this.$refs[\"xlForm\"].clearValidate();\n      });\n      this.xlDialogFormVisible = false;\n    },\n    //线路弹框内容保存\n    addLineInfo() {\n      let params = {\n        lx: \"输电设备\",\n        mc: this.xlForm.lineName\n      };\n      this.$refs[\"xlForm\"].validate(valid => {\n        if (valid) {\n          saveOrUpdatexl(this.xlForm).then(res => {\n            if (res.code == \"0000\") {\n              //新增成功后发送通知\n              adddwzyfstz(params).then(res => {\n                if (res.code === \"0000\") {\n                }\n              });\n              this.$message.success(\"操作成功,通知已发送\");\n              this.treeList();\n              this.xlDialogFormVisible = false;\n              this.lineTzData();\n              return;\n            } else {\n              this.$message.warning(\"操作失败！\");\n            }\n          });\n        } else {\n          setTimeout(() => {\n            var isError = document.getElementsByClassName(\"is-error\");\n            if (isError[0].querySelector(\"input\")) {\n              isError[0].querySelector(\"input\").focus();\n            } else if (isError[0].querySelector(\"textarea\")) {\n              isError[0].querySelector(\"textarea\").focus();\n            }\n          }, 1);\n          return false;\n        }\n      });\n    },\n    getTableList: function(params) {\n      this.queryGtParam = { ...this.queryGtParam, ...params };\n      const param = { ...this.queryGtParam, ...params };\n      getListgt(param).then(res => {\n        this.tableAndPageInfo2.tableData = res.data.records;\n        this.tableAndPageInfo2.pager.total = res.data.total;\n      });\n    },\n    /**\n     * 表格多选框\n     */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.objId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /**\n     * 删除\n     */\n    xldeleteInfo() {\n      if (this.ids.length != 0) {\n        this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(() => {\n          xlremove(this.ids).then(res => {\n            this.$message({\n              type: \"success\",\n              message: \"删除成功!\"\n            });\n            this.lineTzData();\n          });\n        });\n      } else {\n        this.$message({\n          type: \"info\",\n          message: \"请选择至少一条数据!\"\n        });\n      }\n    },\n    sbdeleteInfo() {\n      if (this.ids.length != 0) {\n        this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(() => {\n          remove(this.ids).then(res => {\n            this.$message({\n              type: \"success\",\n              message: \"删除成功!\"\n            });\n            this.sbTzData();\n          });\n        });\n      } else {\n        this.$message({\n          type: \"info\",\n          message: \"请选择至少一条数据!\"\n        });\n      }\n    },\n    gtdeleteInfo() {\n      if (this.ids.length != 0) {\n        this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(() => {\n          gtremove(this.ids).then(res => {\n            this.$message({\n              type: \"success\",\n              message: \"删除成功!\"\n            });\n            this.gtTzData();\n          });\n        });\n      } else {\n        this.$message({\n          type: \"info\",\n          message: \"请选择至少一条数据!\"\n        });\n      }\n    },\n    //导出excel\n    xlexportExcel() {\n      // if(!this.selectData.length > 0){\n      //   this.$message.warning('请在左侧勾选要导出的数据')\n      //   return\n      // }\n      let fileName = \"输电线路信息表\";\n      let exportUrl = \"/sdxl/exportExcel\";\n      exportExcel(exportUrl, this.queryParams, fileName);\n    },\n    //导出excel\n    gtexportExcel() {\n      // if(!this.selectData.length > 0){\n      //   this.$message.warning('请在左侧勾选要导出的数据')\n      //   return\n      // }\n      let fileName = this.lineName + \"杆塔信息表\";\n      let exportUrl = \"/tower/exportExcel\";\n      exportExcel(exportUrl, this.queryGtParam, fileName);\n    },\n    deleteInfo() {\n      if (this.xltzData) {\n        this.xldeleteInfo();\n      }\n      if (this.gttzData) {\n        this.gtdeleteInfo();\n      }\n      if (this.sbtzData) {\n        this.sbdeleteInfo();\n      }\n    },\n    exportExcel() {\n      if (this.xltzData) {\n        this.xlexportExcel();\n      }\n      if (this.gttzData) {\n        this.gtexportExcel();\n      }\n    },\n    //导入\n    importExcel() {\n      this.openExcelDialog = true;\n      this.fileName = \"\";\n    },\n    /**导入文件提交按钮*/\n    submitExcelForm() {\n      if (this.uploadData.lineName === this.lineName) {\n        this.isloading = true;\n        this.$refs.upload.submit();\n      } else {\n        this.$confirm(\n          \"上传的Excel线路名称与所在页面线路不一致，是否继续上传？\",\n          \"提示\",\n          {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\"\n          }\n        )\n          .then(() => {\n            //再次提交\n            this.isloading = true;\n            this.$refs.upload.submit();\n          })\n          .catch(() => {\n            this.$message({\n              type: \"info\",\n              message: \"已取消上传\"\n            });\n          });\n      }\n    },\n    /**导入文件取消按钮*/\n    cancelImport() {\n      this.isloading = false;\n      this.openExcelDialog = false;\n      //取消时清空上传文件列表\n      this.$refs.upload.clearFiles();\n    },\n    /**上传成功方法*/\n    uploadSuccess(res) {\n      if (res.code == \"0000\") {\n        this.msgSuccess(\"上传成功\");\n        this.fileName = \"\";\n        //清空上传的文件列表\n        this.$refs.upload.clearFiles();\n        this.isloading = false;\n      } else {\n        this.isloading = false;\n        this.msgError(res.msg);\n        this.$refs.upload.clearFiles();\n      }\n      //重新渲染\n      this.gtTzData(this.gtfilterInfo.data);\n      this.openExcelDialog = false;\n    },\n    /**文件改变时调用的函数*/\n    handleChange(file) {\n      this.uploadData.lineName = file.name.substring(\n        0,\n        file.name.indexOf(\"杆\")\n      );\n      this.fileName = \"\";\n      let testFileName = file.name;\n      this.fileName = testFileName;\n    },\n    /** 文件移除时函数*/\n    handleRemove() {\n      this.fileName = \"\";\n      this.msgSuccess(\"移除成功\");\n    },\n    /**文件超出限制时调用*/\n    handleExceed(file, fileList) {\n      this.msgWarning(\"只能添加一个文件，请先删除之前的文件\");\n    },\n    updateStatus(row) {\n      console.log(\"row\", row);\n      this.updateList.sbzt = row.sbzt;\n      this.updateList.objId = row.objId;\n      this.dialogVisible = true;\n    },\n    submitStatus() {\n      console.log(\"this.updateList\", this.updateList);\n      this.$confirm(\"确认将设备状态修改为\" + this.updateList.sbzt + \"?\", \"\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(res => {\n        updateStatus(this.updateList).then(res => {\n          if (res.code == \"0000\") {\n            this.$message.success(\"设备状态已变更！\");\n            this.dialogVisible = false;\n            this.getData();\n          }\n        });\n      });\n    },\n    // 修改操作\n    updateRow: function(row) {\n      this.title = \"修改输电设备信息\";\n      this.technicalParameters(row);\n      this.resumeQuery.foreignNum = row.sbmc;\n      this.resumeQuery.sblx = row.sblx;\n      this.getResumList();\n      this.sbDialogFormVisible = true;\n      this.show = false;\n      this.jbxxForm = row;\n    },\n    //详情\n    detailsInfo: function(row) {\n      this.title = \"输电设备信息\";\n      this.technicalParameters(row);\n      this.resumeQuery.foreignNum = row.sbmc;\n      this.resumeQuery.sblx = row.sblx;\n      this.getResumList();\n      this.sbDialogFormVisible = true;\n      this.show = true;\n      this.jbxxForm = row;\n    },\n    filterReset(val, type) {\n      if (type === \"gt\") {\n        this.queryGtParam = {\n          lineName: this.queryGtParam.lineName,\n          // mySorts: [{ prop: \"get_number(gtbh)\", asc: true }]\n        };\n        this.gtTzData(this.queryGtParam);\n      }\n      this.xlfilterInfo.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n    },\n    //获取左侧树节点\n    treeList() {\n      getTreeList().then(res => {\n        this.treeOptions = res.data;\n      });\n    },\n    handleClose() {\n      this.jbxxForm = {};\n      this.$nextTick(function() {\n        this.$refs[\"jbxxForm\"].clearValidate();\n      });\n\n      this.jscsForm = {};\n      this.$nextTick(function() {\n        this.$refs[\"jscsForm\"].clearValidate();\n      });\n\n      this.sbDialogFormVisible = false;\n    },\n    getResumList(par) {\n      if (this.gttzData) {\n        let params = { ...par, ...this.gtresumeQuery };\n        getResumDataList(params).then(res => {\n          this.gtresumPageInfo.tableData = res.data.records;\n          this.gtresumPageInfo.pager.total = res.data.total;\n        });\n      }\n      if (this.sbtzData) {\n        let params = { ...par, ...this.resumeQuery };\n        getResumDataList(params).then(res => {\n          this.resumPageInfo.tableData = res.data.records;\n          this.resumPageInfo.pager.total = res.data.total;\n        });\n      }\n    },\n    //设备添加按钮\n    sbAddSensorButton() {},\n    //新增按钮\n    AddSensorButton() {\n      if (this.xltzData) {\n        this.title = \"新增线路信息\";\n        this.xlImgList = [];\n        this.xlshow = false;\n        this.xlDialogFormVisible = true;\n      }\n      if (this.gttzData) {\n        if (this.gtForm.lineId != undefined) {\n          this.title = \"新增杆塔信息\";\n          this.imgList = [];\n          this.gtshow = false;\n          this.gtDialogFormVisible = true;\n        } else {\n          this.$message.info(\"请先选择所属线路再新增杆塔\");\n        }\n      }\n      if (this.sbtzData) {\n        if (this.jbxxForm.ssgtbh === \"\" && this.jbxxForm.ssxl === \"\") {\n          this.$message.info(\"请在左侧树选择具体线路或杆塔在尝试\");\n          return;\n        }\n        this.jbxxForm.ssxl = this.linedIdStore\n        this.jbxxForm.lineName = this.lineName\n        this.jbxxForm.ssgtbh = this.gtbhStore\n        this.jbxxForm.gtmc = this.gtmcStore\n        this.title = \"新增输电设备信息\";\n        //不禁用表单输入\n        this.show = false;\n        //打开弹出框\n        this.sbDialogFormVisible = true;\n        //新增时先置空去查询基数参数\n        let row = {};\n        //获取技术参数\n        this.technicalParameters(row);\n      }\n    },\n    AddTowerBatchButton() {\n      if (this.linedIdStore && this.lineName) {\n        this.addTowerBatchForm.lineName = this.lineName;\n        this.$set(this.addTowerBatchForm, \"towerNameLinkFlag\", \"#\");\n        this.$set(this.addTowerBatchForm, \"towerNumberSuffix\", \"#\");\n        this.addTowerBatchForm.towerInfo = {\n          lineId: this.linedIdStore,\n          lineName: this.lineName,\n          dydjbm: this.dydjbm,\n          ssbm: \"线路分公司\"\n        };\n        this.title = \"批量新增杆塔\";\n        this.addTowerBatchDialogFormVisible = true;\n      }\n    },\n    saveTowerBatchButton() {\n      this.$refs[\"addTowerBatchForm\"].validate(valid => {\n        if (valid) {\n          saveTowerBatch(this.addTowerBatchForm).then(res => {\n            if (res.code === \"0000\") {\n              this.$message.success(\"操作成功\");\n              this.addTowerBatchDialogFormVisible = false;\n              this.gtTzData(this.gtfilterInfo.data);\n            }\n          });\n        }\n      });\n    },\n    beforeUpload(file) {\n      const fileSize = file.size < 1024 * 1024 * 50;\n      if (!fileSize) {\n        this.$message.error(\"上传文件大小不能超过 50MB!\");\n      }\n    },\n    //杆塔修改操作\n    updateRow2(row) {\n      this.title = \"修改杆塔信息\";\n      this.clearUpload();\n      this.gtresumeQuery.foreignNum = row.ssbm;\n      this.gtresumeQuery.sblx = row.sblx;\n      this.getResumList();\n      this.gtForm = { ...row };\n      this.gtForm.attachment = [];\n      this.gtTzData();\n      this.gtDialogFormVisible = true;\n      this.gtshow = false;\n    },\n    //杆塔查看详情\n    detailsInfo2(row) {\n      this.title = \"杆塔信息\";\n      this.clearUpload();\n      this.gtresumeQuery.foreignNum = row.ssbm;\n      this.gtresumeQuery.sblx = row.sblx;\n      this.getResumList();\n      this.gtForm = { ...row };\n      this.gtForm.attachment = [];\n      this.gtTzData();\n      this.gtDialogFormVisible = true;\n      this.gtshow = true;\n    },\n    clearUpload() {\n      if (this.$refs.upload) {\n        this.$refs.upload.clearFiles();\n      }\n    },\n    //杆塔弹框关闭\n    gthandleClose() {\n      // this.gtForm = {\n      //   attachment: [],\n      //   ssbm : '线路分公司',\n      //   lineId : this.linedIdStore,\n      //   lineName : this.lineName\n      // };\n      this.gtForm.attachment = [];\n      this.gtForm.objId = undefined;\n      this.gtForm.lineId = this.linedIdStore;\n      this.$nextTick(function() {\n        this.$refs[\"gtForm\"].clearValidate();\n      });\n      this.gtDialogFormVisible = false;\n    },\n    //杆塔内容保存\n    addGtInfo() {\n      this.$refs[\"gtForm\"].validate(valid => {\n        if (valid) {\n          saveOrUpdategt(this.gtForm).then(res => {\n            if (res.code == \"0000\") {\n              this.uploadData.businessId = res.data.objId;\n              this.$refs.upload.submit();\n              this.$message.success(\"操作成功\");\n              this.gtDialogFormVisible = false;\n              this.gtTzData();\n            } else {\n              this.$message.warning(\"操作失败！\");\n              return false;\n            }\n          });\n        } else {\n          setTimeout(() => {\n            var isError = document.getElementsByClassName(\"is-error\");\n            if (isError[0].querySelector(\"input\")) {\n              isError[0].querySelector(\"input\").focus();\n            } else if (isError[0].querySelector(\"textarea\")) {\n              isError[0].querySelector(\"textarea\").focus();\n            }\n          }, 1);\n          return false;\n        }\n      });\n    },\n    //间隔添加按钮\n    jgAddjgButton() {\n      this.jgDialogFormVisible = true;\n    },\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n    //每页展示数量点击事件\n    handleSizeChange() {},\n    //页码改变事件\n    handleCurrentChange() {},\n    //树点击事件\n    handleNodeClick(data, node, nodeInfo) {\n      this.identifier = data.identifier;\n      this.queryParams.dydj = \"\"; //清空电压等级条件\n      //当前节点是线路节点\n      if (data.identifier == \"1\") {\n        //线路台账\n        this.queryParams.lineName = \"\";\n        this.lineTzData(this.queryParams);\n      } else if (data.identifier == \"2\") {\n        //电压等级节点\n        this.xloptions.forEach(item => {\n          if (item.label === data.id) {\n            this.dydjbm = item.value;\n            return false;\n          }\n        });\n        this.$set(this.xlForm, \"dydjbm\", this.dydjbm); //设置电压等级\n        this.queryParams.dydj = data.id;\n        this.lineTzData(this.queryParams); //请求线路数据\n      } else if (data.identifier == \"3\") {\n        this.gtForm.lineName = data.label;\n        this.gtForm.lineId = data.id;\n        this.linedIdStore = data.id;\n        this.gtfilterInfo.data.lineName = data.label;\n        this.lineName = data.label;\n        this.gtTzData(this.gtfilterInfo.data);\n      } else if (data.identifier == \"4\") {\n        this.gtbhStore = data.id; //杆塔编号\n        this.gtmcStore = data.label;\n        this.jbxxForm.ssxl = node.parent.data.id; //线路编码\n        this.jbxxForm.lineName = node.parent.data.label; //线路名称\n        this.jbxxForm.ssgtbh = data.id; //杆塔编号\n        this.jbxxForm.gtmc = data.label; //杆塔名称\n        let ssgtbh = data.id; //杆塔编号\n        this.sbTzData();\n      }\n    },\n    //缺陷标准库新增完成\n    qxcommit() {\n      this.dialogFormVisible = false;\n      this.$message.success(\"新增成功\");\n    },\n    submit() {\n      this.$refs[\"jbxxForm\"].validate(valid => {\n        if (valid) {\n          this.addEquipInfo();\n          //  this.submitParameter();\n        } else {\n          setTimeout(() => {\n            var isError = document.getElementsByClassName(\"is-error\");\n            if (isError[0].querySelector(\"input\")) {\n              isError[0].querySelector(\"input\").focus();\n            } else if (isError[0].querySelector(\"textarea\")) {\n              isError[0].querySelector(\"textarea\").focus();\n            }\n          }, 1);\n          return false;\n        }\n      });\n    },\n    //获取设备类型下拉框数据\n    getSblxDataListSelected() {\n      let sblxParam = {\n        type: \"输电设备\"\n      };\n      getSblxDataListSelected(sblxParam).then(res => {\n        this.sblxOptionsDataSelected = res.data;\n      });\n    },\n    //设备类型change事件\n    showParams(data) {\n      this.paramQuery.sblxbm = data;\n      this.getParameters();\n    },\n    //点击新增，修改，详情时，重新获取对应的技术参数信息\n    technicalParameters(row) {\n      //设备类型\n      this.jscsForm = {};\n      this.paramQuery.sblxbm = row.sbflbm;\n      this.jscsForm.sblxbm = row.sbflbm;\n      this.jscsForm.sbbm = row.objId;\n      this.getParameters();\n    },\n    //获取技术参数对照信息，展示对应得技术参数label信息\n    async getParameters() {\n      this.jscsLabelList = [];\n      getParamDataList(this.paramQuery).then(res => {\n        this.jscsLabelList = res.data;\n        this.getParamValue();\n      });\n    },\n    //获取值方法\n    getParamValue() {\n      getParamsValue(this.jscsForm).then(res => {\n        if (res.data != \"\") {\n          this.jscsForm = { ...res.data[0] };\n        }\n      });\n    },\n    //保存基数参数值信息\n    submitParameter() {\n      saveParamValue(this.jscsForm).then(res => {\n        this.sbDialogFormVisible = false;\n      });\n    },\n    async deleteFileById(id){\n      let {code}=await deleteById(id)\n      if(code==='0000'){\n        await this.getFileList();\n        this.$message({\n          type: 'success',\n          message: '文件删除成功!'\n        });\n      }\n    },\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  height: calc(100vh - 225px);\n  overflow: auto;\n}\n\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.box-cardList {\n  height: 70%;\n}\n\n.item {\n  width: 200px;\n  float: left;\n}\n\n#main_container_dj {\n  height: calc(100vh - 84px);\n}\n\n.aside_height {\n  height: 96%;\n}\n\n.defect .el-form-item:nth-child(odd) {\n  margin-right: 70px;\n}\n\n.imgCls {\n  height: 150px !important;\n}\n\n/*背景颜色调整*/\n#main_container_dj,\n#main_container_dj .el-aside {\n  background-color: #b4caf1;\n}\n\n.el-select {\n  width: 100%;\n}\n\n.el-date-editor {\n  width: 100%;\n}\n\n.el-carousel__item h3 {\n  color: #475669;\n  font-size: 14px;\n  opacity: 0.75;\n  line-height: 150px;\n}\n\n.el-carousel__item:nth-child(2n) {\n  background-color: #99a9bf;\n}\n\n.el-carousel__item:nth-child(2n + 1) {\n  background-color: #d3dce6;\n}\n\n/deep/ .box-card {\n  margin: 0 6px;\n}\n\n//有子节点 且未展开\n.el-tree ::v-deep .el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/add.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n\n//有子节点 且已展开\n.el-tree\n  ::v-deep\n  .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/prep.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n\n//没有子节点\n.el-tree ::v-deep .el-tree-node__expand-icon.is-leaf::before {\n  background: transparent;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n\n.divHeader {\n  width: 100%;\n  height: 30px;\n  background-color: #dddddd;\n  margin-bottom: 10px;\n  text-align: left;\n  line-height: 30px;\n  font-weight: bold;\n}\n\n.categoryTreeIcons {\n  background: url(\"../../../../assets/icons/icon/icon1.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n\n.tableIcon {\n  background: url(\"../../../../assets/icons/icon/icon2.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n\n.classIcon {\n  background: url(\"../../../../assets/icons/icon/icon4.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n\n.classIcon2 {\n  background: url(\"../../../../assets/icons/icon/icon4.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n\n.my-autocomplete {\n  li {\n    line-height: normal;\n    padding: 7px;\n\n    .name {\n      text-overflow: ellipsis;\n      overflow: hidden;\n      font-size: 18px;\n    }\n    .addr {\n      font-size: 12px;\n      color: #b4b4b4;\n    }\n\n    .highlighted .addr {\n      color: #ddd;\n    }\n  }\n}\n</style>\n\n<style>\n.el-carousel__container {\n  background-color: #ffffff !important;\n}\n</style>\n"]}]}