{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\bddzcz\\fwzzjsgzsy.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\bddzcz\\fwzzjsgzsy.vue", "mtime": 1706897324256}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCi8vNOS4qnRhYumhtQppbXBvcnQgZnd6el9mZ3NzaCBmcm9tICcuL2NvbXBvbmVudHMvZnd6el9mZ3NzaCcKaW1wb3J0IGZ3enpfcXogZnJvbSAnLi9jb21wb25lbnRzL2Z3enpfcXonCmltcG9ydCBmd3p6X2RqIGZyb20gJy4vY29tcG9uZW50cy9md3p6X2RqJwppbXBvcnQgZnd6el95YmogZnJvbSAnLi9jb21wb25lbnRzL2Z3enpfeWJqJwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdmd3p6anNnenN5JywKICBjb21wb25lbnRzOiB7IGZ3enpfZmdzc2gsIGZ3enpfcXosIGZ3enpfZGosIGZ3enpfeWJqIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGFjdGl2ZU5hbWU6ICdkaicsCiAgICAgIGRqQ29udHJvbGxlcjogZmFsc2UsCiAgICAgIHNoQ29udHJvbGxlcjogZmFsc2UsCiAgICAgIGRiakNvbnRyb2xsZXI6IGZhbHNlLAogICAgICBiakNvbnRyb2xsZXI6IGZhbHNlLAogICAgfQogIH0sCiAgbW91bnRlZCgpIHsKICAgIHRoaXMuZGpDb250cm9sbGVyID0gdHJ1ZQogIH0sCiAgbWV0aG9kczogewogICAgLy90YWLngrnlh7vkuovku7YKICAgIGhhbmRsZUNsaWNrKHRhYiwgZXZlbnQpIHsKICAgICAgaWYgKHRhYi5uYW1lID09PSAnZGonKSB7CiAgICAgICAgdGhpcy5kakNvbnRyb2xsZXIgPSB0cnVlCiAgICAgICAgdGhpcy5zaENvbnRyb2xsZXIgPSBmYWxzZQogICAgICAgIHRoaXMuZGJqQ29udHJvbGxlciA9IGZhbHNlCiAgICAgICAgdGhpcy5iakNvbnRyb2xsZXIgPSBmYWxzZQogICAgICB9IGVsc2UgaWYgKHRhYi5uYW1lID09PSAnc2gnKSB7CiAgICAgICAgdGhpcy5kakNvbnRyb2xsZXIgPSBmYWxzZQogICAgICAgIHRoaXMuc2hDb250cm9sbGVyID0gdHJ1ZQogICAgICAgIHRoaXMuZGJqQ29udHJvbGxlciA9IGZhbHNlCiAgICAgICAgdGhpcy5iakNvbnRyb2xsZXIgPSBmYWxzZQogICAgICB9IGVsc2UgaWYgKHRhYi5uYW1lID09PSAnZGJqJykgewogICAgICAgIHRoaXMuZGpDb250cm9sbGVyID0gZmFsc2UKICAgICAgICB0aGlzLnNoQ29udHJvbGxlciA9IGZhbHNlCiAgICAgICAgdGhpcy5kYmpDb250cm9sbGVyID0gdHJ1ZQogICAgICAgIHRoaXMuYmpDb250cm9sbGVyID0gZmFsc2UKICAgICAgfSBlbHNlIGlmICh0YWIubmFtZSA9PT0gJ2JqJykgewogICAgICAgIHRoaXMuZGpDb250cm9sbGVyID0gZmFsc2UKICAgICAgICB0aGlzLnNoQ29udHJvbGxlciA9IGZhbHNlCiAgICAgICAgdGhpcy5kYmpDb250cm9sbGVyID0gZmFsc2UKICAgICAgICB0aGlzLmJqQ29udHJvbGxlciA9IHRydWUKICAgICAgfQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["fwzzjsgzsy.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAsBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "fwzzjsgzsy.vue", "sourceRoot": "src/views/dagangOilfield/czpgl/gfdzcz", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-white>\n      <el-tabs v-model=\"activeName\" type=\"card\" @tab-click=\"handleClick\">\n        <el-tab-pane label=\"登记\" name=\"dj\">\n          <fwzz_dj v-if=\"djController\"></fwzz_dj>\n        </el-tab-pane>\n        <el-tab-pane label=\"审核\" name=\"sh\">\n          <fwzz_fgssh v-if=\"shController\"></fwzz_fgssh>\n        </el-tab-pane>\n        <!--<el-tab-pane label=\"待办结\" name=\"dbj\">\n          <fwzz_qz v-if=\"dbjController\"></fwzz_qz>\n        </el-tab-pane>-->\n        <el-tab-pane label=\"办结\" name=\"bj\">\n          <fwzz_ybj v-if=\"bjController\"></fwzz_ybj>\n        </el-tab-pane>\n      </el-tabs>\n    </el-white>\n  </div>\n</template>\n\n<script>\n  //4个tab页\n  import fwzz_fgssh from './components/fwzz_fgssh'\n  import fwzz_qz from './components/fwzz_qz'\n  import fwzz_dj from './components/fwzz_dj'\n  import fwzz_ybj from './components/fwzz_ybj'\n\n  export default {\n    name: 'fwzzjsgzsy',\n    components: { fwzz_fgssh, fwzz_qz, fwzz_dj, fwzz_ybj },\n    data() {\n      return {\n        activeName: 'dj',\n        djController: false,\n        shController: false,\n        dbjController: false,\n        bjController: false,\n      }\n    },\n    mounted() {\n      this.djController = true\n    },\n    methods: {\n      //tab点击事件\n      handleClick(tab, event) {\n        if (tab.name === 'dj') {\n          this.djController = true\n          this.shController = false\n          this.dbjController = false\n          this.bjController = false\n        } else if (tab.name === 'sh') {\n          this.djController = false\n          this.shController = true\n          this.dbjController = false\n          this.bjController = false\n        } else if (tab.name === 'dbj') {\n          this.djController = false\n          this.shController = false\n          this.dbjController = true\n          this.bjController = false\n        } else if (tab.name === 'bj') {\n          this.djController = false\n          this.shController = false\n          this.dbjController = false\n          this.bjController = true\n        }\n      }\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>\n"]}]}