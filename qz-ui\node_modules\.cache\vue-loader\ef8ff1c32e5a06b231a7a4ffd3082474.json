{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\bdgl\\jgtz.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\bdgl\\jgtz.vue", "mtime": 1752462716963}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsKaW1wb3J0IHsKICBhZGRBc3NldCwKICBhZGRCZHosCiAgYWRkSmcsCiAgZ2V0RmdzQnlCZHpJZCwKICBnZXRKZ0luZm9MaXN0LAogIGdldE5ld1RyZWVJbmZvLAogIGdldE9yZ2FuaXphdGlvblNlbGVjdGVkLAogIGdldFRyZWVJbmZvLAogIHJlbW92ZUFzc2V0LAogIHJlbW92ZUJkeiwKICByZW1vdmVKZywKICBhZGRkd3p5ZnN0eiwKICBleHBvcnRFeGNlbCwKICBjb3B5QXNzZXQKfSBmcm9tICJAL2FwaS9kYWdhbmdPaWxmaWVsZC9hc3NldC9qZ3R6IjsKaW1wb3J0IHsKICBnZXRCZEFzZXNldExpc3RQYWdlLAogIGdldEJkekRhdGFMaXN0U2VsZWN0ZWQsCiAgZ2V0SmdEYXRhTGlzdFNlbGVjdGVkLAogIGdldFNieGhMaXN0LAogIGdldFNibHhEYXRhTGlzdFNlbGVjdGVkLAogIGFkZFNieGgKfSBmcm9tICJAL2FwaS9kYWdhbmdPaWxmaWVsZC9hc3NldC9iZHNidHoiOwppbXBvcnQgeyBnZXRCZHpMaXN0IH0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYXNzZXQvYmR6dHoiOwppbXBvcnQgeyBkZWxldGVCeUlkLCBnZXRMaXN0QnlCdXNpbmVzc0lkIH0gZnJvbSAiQC9hcGkvdG9vbC9maWxlIjsKaW1wb3J0IHsKICBnZXRQYXJhbURhdGFMaXN0LAogIGdldFBhcmFtc1ZhbHVlCn0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYXNzZXQvcGFyYW1ldGVycyI7CmltcG9ydCB7IGdldFJlc3VtRGF0YUxpc3QgfSBmcm9tICJAL2FwaS9kYWdhbmdPaWxmaWVsZC9hc3NldC9zZHNiIjsKaW1wb3J0IHsgZ2V0VXNlcnMgfSBmcm9tICJAL2FwaS9zeXN0ZW0vdXNlcmdyb3VwIjsKaW1wb3J0IHsgZ2V0RGljdFR5cGVEYXRhIH0gZnJvbSAiQC9hcGkvc3lzdGVtL2RpY3QvZGF0YSI7CmltcG9ydCBGc3NzIGZyb20gIi4uL2JkZ2wvZnNzcy52dWUiOwppbXBvcnQgeyBnZXREYXRhLCBnZXRRdHd0bHJEYXRhIH0gZnJvbSAiQC9hcGkvYmxnay9ibGdrIjsKaW1wb3J0IHsgZ2V0U3liZ2psRGF0YUJ5UGFnZSB9IGZyb20gIkAvYXBpL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvc3liZ2xyIjsKaW1wb3J0IHsgZ2V0TGlzdEZpcnN0IH0gZnJvbSAiQC9hcGkveXhnbC9iZHl4Z2wvcXhnbCI7CmltcG9ydCB7CiAgZ2V0RmdzT3B0aW9ucywKICBnZXRTZWxlY3RPcHRpb25zQnlPcmdUeXBlLAogIGdldExpc3RGb3VyLAogIGdldExpc3RTZWNvbmQsCiAgZ2V0TGlzdFNldmVuCn0gZnJvbSAiQC9hcGkveXhnbC9iZHl4Z2wvemJnbCI7CmltcG9ydCBpbXBvcnRGaWxlIGZyb20gIkAvY29tcG9uZW50cy9FeHBvcnRFeGNlbC9pbXBvcnRFeGNlbCI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogImpndHoiLAogIGNvbXBvbmVudHM6IHsgRnNzcywgaW1wb3J0RmlsZSB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB3ekRhdGFMaXN0T3B0aW9uczogW10sCiAgICAgIHNiRGF0YUxpc3Q6IFtdLAogICAgICBmb3JtQ29weToge30sCiAgICAgIGlzU2hvd0NvcHk6IGZhbHNlLAogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgaWNvbnM6IHsKICAgICAgICBiZHpMaXN0OiAiY2F0ZWdvcnlUcmVlSWNvbnMiLAogICAgICAgIGJkejogInRhYmxlSWNvbiIsCiAgICAgICAgamc6ICJjbGFzc0ljb24iLAogICAgICAgIGpnZGw6ICJjbGFzc0ljb24iIC8v6Ze06ZqU5aSn57G7CiAgICAgIH0sCgogICAgICAvL+aWsOWinuiuvuWkh+aXtuWPmOeUteermeS4i+aLieahhgogICAgICBiZHpPcHRpb25zRGF0YUxpc3Q6IFtdLAogICAgICAvL+mXtOmalOexu+Wei+S4i+aLieaVsOaNrgogICAgICBqZ2x4T3B0aW9uc0RhdGFMaXN0OiBbXSwKICAgICAgLy/moJHnu5PmnoTnm5HlkKzlsZ7mgKcKICAgICAgZmlsdGVyVGV4dDogIiIsCiAgICAgIC8v57uE57uH57uT5p6E5LiL5ouJ5pWw5o2uCiAgICAgIE9yZ2FuaXphdGlvblNlbGVjdGVkTGlzdDogW10sCiAgICAgIC8v5qCR57uT5p6E5LiK6Z2i5b6X562b6YCJ5qGG5Y+C5pWwCiAgICAgIHRyZWVGb3JtOiB7fSwKICAgICAgLy/nlLXljovnrYnnuqfkuIvmi4nmoYbmlbDmja4KICAgICAgVm9sdGFnZUxldmVsU2VsZWN0ZWRMaXN0OiBbXSwKICAgICAgLy/pl7TpmpTnibnmrornlLXljovnrYnnuqcKICAgICAgamdEeWRPcHRpb25zOiBbXSwKICAgICAgLy/luKblrZfmr43nmoTnlLXljovnrYnnuqcKICAgICAgZHlkak9wdGlvbnNXaXRoU3RyaW5nOiBbXSwKICAgICAgLy/mlrDlop7orr7lpIfml7borr7lpIfnirbmgIHkuIvmi4nmoYbmlbDmja4KICAgICAgc2J6dE9wdGlvbnNEYXRhTGlzdDogW10sCiAgICAgIC8v5paw5aKe6K6+5aSH5LiL5ouJ5qGG5pWw5o2uCiAgICAgIHBsYWNlT3B0aW9uczogW10sCiAgICAgIHhzT3B0aW9uczogW10sCiAgICAgIHhiT3B0aW9uczogW10sCiAgICAgIHNpbmdsZUNsaWNrRGF0YTogdW5kZWZpbmVkLAogICAgICAvL+mXtOmalOS/oeaBr+aYr+WQpuaYvuekugogICAgICBqZ1Nob3c6IGZhbHNlLAogICAgICBmaWx0ZXJJbmZvOiB7fSwKICAgICAgLy/pgJrnlKjliJfooajlj4LmlbAKICAgICAgdGFibGVBbmRQYWdlSW5mbzoge30sCiAgICAgIGpnUXVlcnlQYXJhbXM6IHsKICAgICAgICBzc2JkejogdW5kZWZpbmVkLAogICAgICAgIGR5ZGo6IHVuZGVmaW5lZCwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgcGFnZU51bTogMQogICAgICB9LAogICAgICBqZ3h4Rm9ybTogewogICAgICAgIG9iaklkOiB1bmRlZmluZWQsCiAgICAgICAgd3pibTogdW5kZWZpbmVkLAogICAgICAgIHd6aWQ6IHVuZGVmaW5lZCwKICAgICAgICBzY2NqOiB1bmRlZmluZWQsCiAgICAgICAgamdtYzogdW5kZWZpbmVkLAogICAgICAgIHR5cnE6IHVuZGVmaW5lZCwKICAgICAgICBqZ2x4OiB1bmRlZmluZWQsCiAgICAgICAgZHlkajogdW5kZWZpbmVkLAogICAgICAgIHp0OiB1bmRlZmluZWQsCiAgICAgICAgY2NycTogdW5kZWZpbmVkLAogICAgICAgIGdneGg6IHVuZGVmaW5lZCwKICAgICAgICBqZDogdW5kZWZpbmVkLAogICAgICAgIHdkOiB1bmRlZmluZWQsCiAgICAgICAgc3NkZDogdW5kZWZpbmVkLAogICAgICAgIHNzYmR6OiB1bmRlZmluZWQsCiAgICAgICAgYmR6bWM6IHVuZGVmaW5lZCwKICAgICAgICBiejogdW5kZWZpbmVkCiAgICAgIH0sCiAgICAgIC8v6Ze06ZqU5bGV56S6CiAgICAgIGpnU2hvd1RhYmxlOiBmYWxzZSwKICAgICAgLy/pl7TpmpTmt7vliqDmjInpkq7lvLnlh7rmoYYKICAgICAgamdEaWFsb2dGb3JtVmlzaWJsZTogZmFsc2UsCiAgICAgIC8v5by55Ye65qGG6KGo5Y2VCiAgICAgIGZvcm06IHt9LAogICAgICBwaWNrZXJPcHRpb25zOiB7CiAgICAgICAgc2hvcnRjdXRzOiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIHRleHQ6ICLku4rlpKkiLAogICAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgewogICAgICAgICAgICAgIHBpY2tlci4kZW1pdCgicGljayIsIG5ldyBEYXRlKCkpOwogICAgICAgICAgICB9CiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICB0ZXh0OiAi5pio5aSpIiwKICAgICAgICAgICAgb25DbGljayhwaWNrZXIpIHsKICAgICAgICAgICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoKTsKICAgICAgICAgICAgICBkYXRlLnNldFRpbWUoZGF0ZS5nZXRUaW1lKCkgLSAzNjAwICogMTAwMCAqIDI0KTsKICAgICAgICAgICAgICBwaWNrZXIuJGVtaXQoInBpY2siLCBkYXRlKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgdGV4dDogIuaYjuWkqSIsCiAgICAgICAgICAgIG9uQ2xpY2socGlja2VyKSB7CiAgICAgICAgICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKCk7CiAgICAgICAgICAgICAgZGF0ZS5zZXRUaW1lKGRhdGUuZ2V0VGltZSgpICsgMzYwMCAqIDEwMDAgKiAyNCk7CiAgICAgICAgICAgICAgcGlja2VyLiRlbWl0KCJwaWNrIiwgZGF0ZSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIC8v57uE57uH5qCRCiAgICAgIHRyZWVPcHRpb25zOiBbXSwKICAgICAgLy/liKDpmaTmmK/lkKblj6/nlKgKICAgICAgbXVsdGlwbGVTZW5zb3I6IHRydWUsCiAgICAgIC8v5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgcm9sZUtleTogIiIsCiAgICAgICAgcm9sZU5hbWU6ICIiLAogICAgICAgIHN0YXR1czogIiIKICAgICAgfSwKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgcnVsZXM6IHsKICAgICAgICAvLyB3emJtOlt7ICByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+Whq+WGmeS9jee9rue8lueggScsIHRyaWdnZXI6ICdibHVyJyB9XSwKICAgICAgICAvLyB3emlkOlt7cmVxdWlyZWQ6dHJ1ZSxtZXNzYWdlOifor7floavlhpnkvY3nva5pZCcsdHJpZ2dlcjonYmx1cid9XSwKICAgICAgICBqZ21jOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+Whq+WGmemXtOmalOWQjeensCIsIHRyaWdnZXI6ICJibHVyIiB9XSwKICAgICAgICAvLyB6dDpbe3JlcXVpcmVkOnRydWUsbWVzc2FnZTon6K+35aGr5YaZ54q25oCBJyx0cmlnZ2VyOidibHVyJ31dLAogICAgICAgIHNzYmR6OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup5omA5bGe5Y+Y55S156uZIiwgdHJpZ2dlcjogImNoYW5nZSIgfQogICAgICAgIF0sCiAgICAgICAgZHlkajogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6nnlLXljovnrYnnuqciLCB0cmlnZ2VyOiAiY2hhbmdlIiB9XQogICAgICB9LAogICAgICAvL+aWsAogICAgICAvL+aOp+WItuWPmOeUteermeihqOagvOaYr+WQpuWxleekugogICAgICBiZHpkYXRhU2hvdzogdHJ1ZSwKICAgICAgaW1wb3J0RXhjZWxVcmw6ICIiLAogICAgICBmaWxlTmFtZTogIiIsCiAgICAgIC8v5o6n5Yi25Y+Y55S156uZ6KGo5qC85piv5ZCm5bGV56S6CiAgICAgIGpnZGF0YVNob3c6IGZhbHNlLAogICAgICAvL+aOp+WItuWPmOeUteermeihqOagvOaYr+WQpuWxleekugogICAgICB6bnNiZGF0YVNob3c6IGZhbHNlLAogICAgICBiZHpxdWVyeVBhcmFtczogewogICAgICAgIHNzZ3M6IHVuZGVmaW5lZCwKICAgICAgICBkeWRqYm06IHVuZGVmaW5lZCwKICAgICAgICBzc2JkejogdW5kZWZpbmVkLAogICAgICAgIHNzamc6IHVuZGVmaW5lZCwKICAgICAgICBzYm1jOiB1bmRlZmluZWQsCiAgICAgICAgYXNzZXRUeXBlQ29kZTogdW5kZWZpbmVkLAogICAgICAgIHNienQ6IHVuZGVmaW5lZCwKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMAogICAgICB9LAogICAgICBwYXJhbXM6IHsKICAgICAgICAvL2JtOnVuZGVmaW5lZCwKICAgICAgICAvLyBzc2R3Ym06IHVuZGVmaW5lZCwKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMAogICAgICB9LAogICAgICBmaWx0ZXJJbmZvMTogewogICAgICAgIGRhdGE6IHsKICAgICAgICAgIHl3ZHdBcnI6IFtdLAogICAgICAgICAgamhueUFycjogW10sCiAgICAgICAgICB4bEFycjogIiIsCiAgICAgICAgICBqaGx4QXJyOiBbXSwKICAgICAgICAgIGpoenRBcnI6ICIiLAogICAgICAgICAgc2ZkZDogIiIKICAgICAgICB9LAogICAgICAgIGZpZWxkTGlzdDogWwogICAgICAgICAgeyBsYWJlbDogIuWPmOeUteermeWQjeensCIsIHR5cGU6ICJpbnB1dCIsIHZhbHVlOiAiYmR6bWMiIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi5oqV6L+Q5pel5pyfIiwKICAgICAgICAgICAgdHlwZTogImRhdGUiLAogICAgICAgICAgICB2YWx1ZTogInR5cnFBcnIiLAogICAgICAgICAgICBkYXRlVHlwZTogImRhdGVyYW5nZSIsCiAgICAgICAgICAgIGZvcm1hdDogInl5eXktTU0tZGQiCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIuaJgOWxnuWFrOWPuCIsCiAgICAgICAgICAgIHR5cGU6ICJjaGVja2JveCIsCiAgICAgICAgICAgIGNoZWNrYm94VmFsdWU6IFtdLAogICAgICAgICAgICB2YWx1ZTogInNzZHdibSIsCiAgICAgICAgICAgIG9wdGlvbnM6IFtdCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIuaYr+WQpuaeoue6veermSIsCiAgICAgICAgICAgIHR5cGU6ICJjaGVja2JveCIsCiAgICAgICAgICAgIGNoZWNrYm94VmFsdWU6IFtdLAogICAgICAgICAgICB2YWx1ZTogInNmc256IiwKICAgICAgICAgICAgb3B0aW9uczogWwogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIHZhbHVlOiAi5pivIiwKICAgICAgICAgICAgICAgIGxhYmVsOiAi5pivIgogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgdmFsdWU6ICLlkKYiLAogICAgICAgICAgICAgICAgbGFiZWw6ICLlkKYiCiAgICAgICAgICAgICAgfQogICAgICAgICAgICBdCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIuiuvuWkh+eKtuaAgSIsCiAgICAgICAgICAgIHR5cGU6ICJjaGVja2JveCIsCiAgICAgICAgICAgIGNoZWNrYm94VmFsdWU6IFtdLAogICAgICAgICAgICB2YWx1ZTogInNienQiLAogICAgICAgICAgICBvcHRpb25zOiBbXQogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLnlLXljovnrYnnuqciLAogICAgICAgICAgICB0eXBlOiAiY2hlY2tib3giLAogICAgICAgICAgICBjaGVja2JveFZhbHVlOiBbXSwKICAgICAgICAgICAgdmFsdWU6ICJkeWRqIiwKICAgICAgICAgICAgb3B0aW9uczogW10KICAgICAgICAgIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIC8v5Y+Y55S156uZdGFibGXmlbDmja4KICAgICAgdGFibGVBbmRQYWdlSW5mbzE6IHsKICAgICAgICBwYWdlcjogewogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMDBdLAogICAgICAgICAgcGFnZVJlc2l6ZTogIiIKICAgICAgICB9LAogICAgICAgIG9wdGlvbjogewogICAgICAgICAgY2hlY2tCb3g6IHRydWUsCiAgICAgICAgICBzZXJpYWxOdW1iZXI6IHRydWUKICAgICAgICB9LAogICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgdGFibGVIZWFkZXI6IFsKICAgICAgICAgIHsgcHJvcDogInNzZHdtYyIsIGxhYmVsOiAi5omA5bGe5YWs5Y+4IiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJiZHptYyIsIGxhYmVsOiAi5Y+Y55S156uZ5ZCN56ewIiwgbWluV2lkdGg6ICIxODAiIH0sCiAgICAgICAgICB7IHByb3A6ICJkeWRqIiwgbGFiZWw6ICLnlLXljovnrYnnuqciLCBtaW5XaWR0aDogIjgwIiB9LAogICAgICAgICAgeyBwcm9wOiAic2J6dCIsIGxhYmVsOiAi6K6+5aSH54q25oCBIiwgbWluV2lkdGg6ICI4MCIgfSwKICAgICAgICAgIHsgcHJvcDogInNmc256IiwgbGFiZWw6ICLmmK/lkKbmnqLnur3nq5kiLCBtaW5XaWR0aDogIjE0MCIgfSwKICAgICAgICAgIC8vIHtwcm9wOiAnc2JseCcsIGxhYmVsOiAn6K6+5aSH57G75Z6LJywgbWluV2lkdGg6ICcxMjAnfSwKICAgICAgICAgIHsgcHJvcDogInR5cnEiLCBsYWJlbDogIuaKlei/kOaXpeacnyIsIG1pbldpZHRoOiAiMTIwIiB9CiAgICAgICAgICAvKiB7CiAgICAgICAgICAgICBmaXhlZDogInJpZ2h0IiwKICAgICAgICAgICAgIHByb3A6ICdvcGVyYXRpb24nLAogICAgICAgICAgICAgbGFiZWw6ICfmk43kvZwnLAogICAgICAgICAgICAgbWluV2lkdGg6ICcxMzBweCcsCiAgICAgICAgICAgICBzdHlsZToge2Rpc3BsYXk6ICdibG9jayd9LAogICAgICAgICAgICAgb3BlcmF0aW9uOiBbCiAgICAgICAgICAgICAgIHtuYW1lOiAn5L+u5pS5JywgY2xpY2tGdW46IHRoaXMudXBkYXRlYmR6fSwKICAgICAgICAgICAgICAge25hbWU6ICfor6bmg4UnLCBjbGlja0Z1bjogdGhpcy5iZHpEZXRhaWxzfSwKICAgICAgICAgICAgIF0KICAgICAgICAgICB9LCovCiAgICAgICAgXQogICAgICAgIC8qIFsKICAgICAgICAge3Byb3A6ICdqZ21jJywgbGFiZWw6ICfpl7TpmpTlkI3np7AnLCBtaW5XaWR0aDogJzEyMCd9LAogICAgICAgICB7cHJvcDogJ2pnbHgnLCBsYWJlbDogJ+mXtOmalOexu+WeiycsIG1pbldpZHRoOiAnMTIwJ30sCiAgICAgICAgIHtwcm9wOiAnZHlkaicsIGxhYmVsOiAn55S15Y6L562J57qnJywgbWluV2lkdGg6ICcxMjAnfSwKICAgICAgICAge3Byb3A6ICd0eXJxJywgbGFiZWw6ICfmipXov5Dml6XmnJ8nLCBtaW5XaWR0aDogJzEyMCd9LAoKICAgICAgICAgLy8ge3Byb3A6ICd6dCcsIGxhYmVsOiAn54q25oCBJywgbWluV2lkdGg6ICcxMjAnfSwKICAgICAgICAgLy8ge3Byb3A6ICdqZCcsIGxhYmVsOiAn57uP5bqmJywgbWluV2lkdGg6ICcxNDAnfSwKICAgICAgICAgLy8ge3Byb3A6ICd3ZCcsIGxhYmVsOiAn57qs5bqmJywgbWluV2lkdGg6ICcxMjAnfSwKICAgICAgICAgLy8ge3Byb3A6ICdzc2RkJywgbGFiZWw6ICfmiYDlsZ7osIPluqYnLCBtaW5XaWR0aDogJzEyMCd9LAogICAgICAgICB7CiAgICAgICAgICAgZml4ZWQ6ICJyaWdodCIsCiAgICAgICAgICAgcHJvcDogJ29wZXJhdGlvbicsCiAgICAgICAgICAgbGFiZWw6ICfmk43kvZwnLAogICAgICAgICAgIG1pbldpZHRoOiAnMTMwcHgnLAogICAgICAgICAgIHN0eWxlOiB7ZGlzcGxheTogJ2Jsb2NrJ30sCiAgICAgICAgICAgb3BlcmF0aW9uOiBbCiAgICAgICAgICAgICB7bmFtZTogJ+S/ruaUuScsIGNsaWNrRnVuOiB0aGlzLnVwZGF0ZUpnfSwKICAgICAgICAgICAgIHtuYW1lOiAn6K+m5oOFJywgY2xpY2tGdW46IHRoaXMuamdEZXRhaWxzfSwKICAgICAgICAgICBdCiAgICAgICAgIH0sCiAgICAgICBdKi8KICAgICAgfSwKICAgICAgZmlsdGVySW5mbzI6IHsKICAgICAgICBkYXRhOiB7CiAgICAgICAgICB5d2R3QXJyOiBbXSwKICAgICAgICAgIGpobnlBcnI6IFtdLAogICAgICAgICAgeGxBcnI6ICIiLAogICAgICAgICAgamhseEFycjogW10sCiAgICAgICAgICBqaHp0QXJyOiAiIiwKICAgICAgICAgIHNmZGQ6ICIiCiAgICAgICAgfSwKICAgICAgICBmaWVsZExpc3Q6IFsKICAgICAgICAgIHsgbGFiZWw6ICLpl7TpmpTlkI3np7AiLCB0eXBlOiAiaW5wdXQiLCB2YWx1ZTogImpnbWMiLCBvcHRpb25zOiBbXSB9LAogICAgICAgICAgeyBsYWJlbDogIumXtOmalOexu+WeiyIsIHR5cGU6ICJpbnB1dCIsIHZhbHVlOiAiamdseCIsIG9wdGlvbnM6IFtdIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi55S15Y6L562J57qnIiwKICAgICAgICAgICAgdHlwZTogInNlbGVjdCIsCiAgICAgICAgICAgIHZhbHVlOiAiZHlkaiIsCiAgICAgICAgICAgIG9wdGlvbnM6IFtdCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIuaKlei/kOaXpeacnyIsCiAgICAgICAgICAgIHR5cGU6ICJkYXRlIiwKICAgICAgICAgICAgdmFsdWU6ICJ0eXJxQXJyIiwKICAgICAgICAgICAgZGF0ZVR5cGU6ICJkYXRlcmFuZ2UiLAogICAgICAgICAgICBmb3JtYXQ6ICJ5eXl5LU1NLWRkIgogICAgICAgICAgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgLy/pl7TpmpTmlbDmja4KICAgICAgdGFibGVBbmRQYWdlSW5mbzI6IHsKICAgICAgICBwYWdlcjogewogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMDBdLAogICAgICAgICAgcGFnZVJlc2l6ZTogIiIKICAgICAgICB9LAogICAgICAgIG9wdGlvbjogewogICAgICAgICAgY2hlY2tCb3g6IHRydWUsCiAgICAgICAgICBzZXJpYWxOdW1iZXI6IHRydWUKICAgICAgICB9LAogICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgdGFibGVIZWFkZXI6IFsKICAgICAgICAgIHsgcHJvcDogImpnbWMiLCBsYWJlbDogIumXtOmalOWQjeensCIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBwcm9wOiAiamdseCIsIGxhYmVsOiAi6Ze06ZqU57G75Z6LIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJkeWRqIiwgbGFiZWw6ICLnlLXljovnrYnnuqciLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgcHJvcDogInR5cnEiLCBsYWJlbDogIuaKlei/kOaXpeacnyIsIG1pbldpZHRoOiAiMTIwIiB9CiAgICAgICAgICAvKnsKICAgICAgICAgICAgZml4ZWQ6ICJyaWdodCIsCiAgICAgICAgICAgIHByb3A6ICdvcGVyYXRpb24nLAogICAgICAgICAgICBsYWJlbDogJ+aTjeS9nCcsCiAgICAgICAgICAgIG1pbldpZHRoOiAnMTMwcHgnLAogICAgICAgICAgICBzdHlsZToge2Rpc3BsYXk6ICdibG9jayd9LAogICAgICAgICAgICBvcGVyYXRpb246IFsKICAgICAgICAgICAgICB7bmFtZTogJ+S/ruaUuScsIGNsaWNrRnVuOiB0aGlzLnVwZGF0ZUpnfSwKICAgICAgICAgICAgICB7bmFtZTogJ+ivpuaDhScsIGNsaWNrRnVuOiB0aGlzLmpnRGV0YWlsc30sCiAgICAgICAgICAgIF0KICAgICAgICAgIH0sKi8KICAgICAgICBdCiAgICAgIH0sCiAgICAgIGZpbHRlckluZm8zOiB7CiAgICAgICAgZGF0YTogewogICAgICAgICAgeXdkd0FycjogW10sCiAgICAgICAgICBqaG55QXJyOiBbXSwKICAgICAgICAgIHhsQXJyOiAiIiwKICAgICAgICAgIGpobHhBcnI6IFtdLAogICAgICAgICAgamh6dEFycjogIiIsCiAgICAgICAgICBzZmRkOiAiIgogICAgICAgIH0sCiAgICAgICAgZmllbGRMaXN0OiBbCiAgICAgICAgICB7IGxhYmVsOiAi6K6+5aSH57G75Z6LIiwgdHlwZTogImlucHV0IiwgdmFsdWU6ICJzYmx4bWMiLCBvcHRpb25zOiBbXSB9LAogICAgICAgICAgeyBsYWJlbDogIuiuvuWkh+WQjeensCIsIHR5cGU6ICJpbnB1dCIsIHZhbHVlOiAic2JtYyIsIG9wdGlvbnM6IFtdIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi55S15Y6L562J57qnIiwKICAgICAgICAgICAgdHlwZTogInNlbGVjdCIsCiAgICAgICAgICAgIHZhbHVlOiAiZHlkak5hbWUiLAogICAgICAgICAgICBvcHRpb25zOiBbXQogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLorr7lpIfnirbmgIEiLAogICAgICAgICAgICB0eXBlOiAic2VsZWN0IiwKICAgICAgICAgICAgdmFsdWU6ICJzYnp0IiwKICAgICAgICAgICAgb3B0aW9uczogW10KICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi5oqV6L+Q5pel5pyfIiwKICAgICAgICAgICAgdHlwZTogImRhdGUiLAogICAgICAgICAgICB2YWx1ZTogInR5cnFBcnIiLAogICAgICAgICAgICBkYXRlVHlwZTogImRhdGVyYW5nZSIsCiAgICAgICAgICAgIGZvcm1hdDogInl5eXktTU0tZGQiCiAgICAgICAgICB9LAogICAgICAgICAgeyBsYWJlbDogIuWei+WPtyIsIHR5cGU6ICJpbnB1dCIsIHZhbHVlOiAiZ2d4aCIsIG9wdGlvbnM6IFtdIH0sCiAgICAgICAgICB7IGxhYmVsOiAi6aKd5a6a55S15Y6LIiwgdHlwZTogImlucHV0IiwgdmFsdWU6ICJlZGR5Iiwgb3B0aW9uczogW10gfSwKICAgICAgICAgIHsgbGFiZWw6ICLpop3lrprnlLXmtYEiLCB0eXBlOiAiaW5wdXQiLCB2YWx1ZTogImVkZGwiLCBvcHRpb25zOiBbXSB9LAogICAgICAgICAgeyBsYWJlbDogIumineWumumikeeOhyIsIHR5cGU6ICJpbnB1dCIsIHZhbHVlOiAiZWRwbCIsIG9wdGlvbnM6IFtdIH0sCiAgICAgICAgICB7IGxhYmVsOiAi55Sf5Lqn5Y6C5a62IiwgdHlwZTogImlucHV0IiwgdmFsdWU6ICJzY2NqIiwgb3B0aW9uczogW10gfQogICAgICAgIF0KICAgICAgfSwKICAgICAgLy/nq5nlhoXorr7lpIflj7DotKYKICAgICAgdGFibGVBbmRQYWdlSW5mbzM6IHsKICAgICAgICBwYWdlcjogewogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMDBdLAogICAgICAgICAgcGFnZVJlc2l6ZTogIiIKICAgICAgICB9LAogICAgICAgIG9wdGlvbjogewogICAgICAgICAgY2hlY2tCb3g6IHRydWUsCiAgICAgICAgICBzZXJpYWxOdW1iZXI6IHRydWUKICAgICAgICB9LAogICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgdGFibGVIZWFkZXI6IFsKICAgICAgICAgIHsgcHJvcDogImRlcHRuYW1lIiwgbGFiZWw6ICLmiYDlsZ7lhazlj7giLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgcHJvcDogImJkem1jIiwgbGFiZWw6ICLlj5jnlLXnq5nlkI3np7AiLCBtaW5XaWR0aDogIjE4MCIgfSwKICAgICAgICAgIHsgcHJvcDogInd6bWMiLCBsYWJlbDogIuaJgOWxnumXtOmalCIsIG1pbldpZHRoOiAiMTgwIiB9LAogICAgICAgICAgeyBwcm9wOiAic2JseG1jIiwgbGFiZWw6ICLorr7lpIfnsbvlnosiLCBtaW5XaWR0aDogIjE4MCIgfSwKICAgICAgICAgIHsgcHJvcDogInNibWMiLCBsYWJlbDogIuiuvuWkh+WQjeensCIsIG1pbldpZHRoOiAiMTgwIiB9LAogICAgICAgICAgeyBwcm9wOiAiZHlkak5hbWUiLCBsYWJlbDogIueUteWOi+etiee6pyIsIG1pbldpZHRoOiAiODAiIH0sCiAgICAgICAgICB7IHByb3A6ICJzYnp0IiwgbGFiZWw6ICLorr7lpIfnirbmgIEiLCBtaW5XaWR0aDogIjgwIiB9LAogICAgICAgICAgeyBwcm9wOiAidHlycSIsIGxhYmVsOiAi5oqV6L+Q5pel5pyfIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJnZ3hoIiwgbGFiZWw6ICLlnovlj7ciLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgcHJvcDogImVkZHkiLCBsYWJlbDogIumineWumueUteWOiyIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBwcm9wOiAiZWRkbCIsIGxhYmVsOiAi6aKd5a6a55S15rWBIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJlZHBsIiwgbGFiZWw6ICLpop3lrprpopHnjociLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgcHJvcDogInNjY2oiLCBsYWJlbDogIueUn+S6p+WOguWutiIsIG1pbldpZHRoOiAiMTIwIiB9CiAgICAgICAgICAvKnsKICAgICAgICAgICAgZml4ZWQ6ICJyaWdodCIsCiAgICAgICAgICAgIHByb3A6ICdvcGVyYXRpb24nLAogICAgICAgICAgICBsYWJlbDogJ+aTjeS9nCcsCiAgICAgICAgICAgIG1pbldpZHRoOiAnMTIwcHgnLAogICAgICAgICAgICBzdHlsZToge2Rpc3BsYXk6ICdibG9jayd9LAogICAgICAgICAgICBvcGVyYXRpb246IFsKICAgICAgICAgICAgICAvISp7bmFtZTogIueKtuaAgeWPmOabtCIsIGNsaWNrRnVuOiB0aGlzLnVwZGF0ZVN0YXR1c30sCiAgICAgICAgICAgICAge25hbWU6ICLmtYHnqIvmn6XnnIsiLCBjbGlja0Z1bjogdGhpcy56dGJnbGNTYXl9LCohLwogICAgICAgICAgICAgIHtuYW1lOiAn5L+u5pS5JywgY2xpY2tGdW46IHRoaXMudXBkYXRlQXNzZXR9LAogICAgICAgICAgICAgIHtuYW1lOiAn6K+m5oOFJywgY2xpY2tGdW46IHRoaXMuYXNzZXREZXRhaWxzfSwKICAgICAgICAgICAgXQogICAgICAgICAgfSwqLwogICAgICAgIF0KICAgICAgfSwKICAgICAgLy/orr7lpIfln7rmnKzkv6Hmga8KICAgICAgamJ4eEZvcm06IHsKICAgICAgICBhdHRhY2htZW50OiBbXSwKICAgICAgICBvYmpJZDogdW5kZWZpbmVkLAogICAgICAgIHNzZHdtYzogdW5kZWZpbmVkLAogICAgICAgIHNzZHdibTogdW5kZWZpbmVkLAogICAgICAgIHNiZG06IHVuZGVmaW5lZCwKICAgICAgICBkZHNiaDogdW5kZWZpbmVkLAogICAgICAgIGR5ZGo6IHVuZGVmaW5lZCwKICAgICAgICB0eXJxOiB1bmRlZmluZWQsCiAgICAgICAgamdkeTogdW5kZWZpbmVkLAogICAgICAgIHhiOiB1bmRlZmluZWQsCiAgICAgICAgeHM6IHVuZGVmaW5lZCwKICAgICAgICBjY3JxOiB1bmRlZmluZWQsCiAgICAgICAgYXp3ejogdW5kZWZpbmVkLAogICAgICAgIHl0OiB1bmRlZmluZWQsCiAgICAgICAgZnpyOiB1bmRlZmluZWQsCiAgICAgICAgY3BkaDogdW5kZWZpbmVkLAogICAgICAgIGVkZHk6IHVuZGVmaW5lZCwKICAgICAgICBlZHBsOiB1bmRlZmluZWQsCiAgICAgICAgc2J6dDogdW5kZWZpbmVkLAogICAgICAgIHN5aGo6IHVuZGVmaW5lZCwKICAgICAgICBzY2NqOiB1bmRlZmluZWQsCiAgICAgICAgenpnajogdW5kZWZpbmVkLAogICAgICAgIHpoc2JseDogdW5kZWZpbmVkLAogICAgICAgIHpoc2JseG1jOiB1bmRlZmluZWQsCiAgICAgICAgZWRkbDogdW5kZWZpbmVkLAogICAgICAgIHl4Ymg6IHVuZGVmaW5lZCwKICAgICAgICBjY2JoOiB1bmRlZmluZWQsCiAgICAgICAgYmR6bWM6IHVuZGVmaW5lZCwKICAgICAgICBiZHpzemJoOiB1bmRlZmluZWQsIC8v5Y+Y55S156uZ5pWw5a2X57yW5Y+3CiAgICAgICAgc3NkdzogdW5kZWZpbmVkLCAvL+aJgOWxnueUtee9kQogICAgICAgIGR6bHg6IHVuZGVmaW5lZCwgLy/nlLXnq5nnsbvlnosKICAgICAgICBzZnpoemRoOiB1bmRlZmluZWQsIC8v5piv5ZCm57u85ZCI6Ieq5Yqo5YyW56uZCiAgICAgICAgc2ZzemhiZHo6IHVuZGVmaW5lZCwgLy/mmK/lkKbmlbDlrZfljJblj5jnlLXnq5kKICAgICAgICByZXR1cm5EYXRlOiB1bmRlZmluZWQsIC8v6YCA6L+Q5pel5pyfCiAgICAgICAgenltajogdW5kZWZpbmVkLCAvL+WNoOWcsOmdouenrwogICAgICAgIHdoZGo6IHVuZGVmaW5lZCwgLy/msaHnp73nrYnnuqcKICAgICAgICB6YmZzOiB1bmRlZmluZWQsIC8v5YC854+t5pa55byPCiAgICAgICAgc2ZncXR4OiB1bmRlZmluZWQsIC8v5piv5ZCm5YWJ57qk6YCa6K6vCiAgICAgICAgaGI6IHVuZGVmaW5lZCwgLy/mtbfmi5QKICAgICAgICBnY2JoOiB1bmRlZmluZWQsIC8v5bel56iL57yW5Y+3CiAgICAgICAgc2pkdzogdW5kZWZpbmVkLCAvL+iuvuiuoeWNleS9jQogICAgICAgIGpsZHc6IHVuZGVmaW5lZCwgLy/nm5HnkIbljZXkvY0KICAgICAgICB6eWpiOiB1bmRlZmluZWQsIC8vIOeUteermemHjeimgee6p+WIqwogICAgICAgIGJ6ZnM6IHVuZGVmaW5lZCwgLy/luIPnva7mlrnlvI8KICAgICAgICBiZHpkejogdW5kZWZpbmVkLCAvL+WPmOeUteermeWcsOWdgAogICAgICAgIGp6bWo6IHVuZGVmaW5lZCwgLy8g5bu6562R6Z2i56evCiAgICAgICAgcGhvbmU6IHVuZGVmaW5lZCwgLy/ogZTns7vnlLXor50KICAgICAgICBnY21jOiB1bmRlZmluZWQsIC8vIOW3peeoi+WQjeensAogICAgICAgIHNnZHc6IHVuZGVmaW5lZCwgLy/mlr3lt6XljZXkvY0KICAgICAgICBkcXR6OiB1bmRlZmluZWQsIC8v5Zyw5Yy654m55b6BCiAgICAgICAgemdkZGd4cTogdW5kZWZpbmVkLCAvLyDmnIDpq5josIPluqbnrqHovpbmnYMKICAgICAgICBzZm16bjogdW5kZWZpbmVkLCAvLyDmmK/lkKbmu6HotrNuLTEKICAgICAgICBzZmpyZ3p4dDogdW5kZWZpbmVkLCAvL+aYr+WQpuaOpeWFpeaVhemanOS/oeaBr+i/nOS8oOezu+e7nwogICAgICAgIHNmanJhdmM6IHVuZGVmaW5lZCwgLy/mmK/lkKbmjqXlhaVhdmMKICAgICAgICBzZmp6ams6IHVuZGVmaW5lZCwgLy/mmK/lkKbpm4bkuK3nm5HmjqcKICAgICAgICBqa3p4bWM6IHVuZGVmaW5lZCwgLy/mjqXlhaXlvpfnm5HmjqfkuK3lv4MKICAgICAgICBiejogdW5kZWZpbmVkIC8v5aSH5rOoCiAgICAgIH0sCiAgICAgIC8v5Y+Y55S156uZ5re75Yqg5oyJ6ZKu5by55Ye65qGGCiAgICAgIGJkekRpYWxvZ0Zvcm1WaXNpYmxlOiBmYWxzZSwKICAgICAgLy/moIfpopgKICAgICAgdGl0bGU6ICIiLAogICAgICBpbWdMaXN0OiBbXSwKICAgICAgY3VyclVzZXI6ICIiLAogICAgICAvL+WPmOeUteermeS/oeaBr+aYr+WQpuWPr+e8lui+kQogICAgICBpc0Rpc2FibGVkOiBmYWxzZSwKICAgICAgLy/kuIrkvKDlm77niYfml7bnmoTor7fmsYLlpLQKICAgICAgaGVhZGVyOiB7fSwKICAgICAgLy/miYDlsZ7ln7rlnLDnq5kKICAgICAgc3NqZHpMaXN0OiBbXSwKICAgICAgdXBsb2FkRGF0YTogewogICAgICAgIHR5cGU6ICIiLAogICAgICAgIGJ1c2luZXNzSWQ6IHVuZGVmaW5lZAogICAgICB9LAogICAgICBpbXBvcnRFeHRyYUluZm86IHt9LAogICAgICAvL+WPmOeUteermeaWsOWinuW8ueahhgogICAgICBiZHpEaWRhbG9nRm9ybTogZmFsc2UsCiAgICAgIHBhcmFtUXVlcnk6IHsKICAgICAgICBzYmx4Ym06IHVuZGVmaW5lZAogICAgICB9LAogICAgICByZXN1bWVRdWVyeTogewogICAgICAgIGZvcmVpZ25OdW06IHVuZGVmaW5lZCwKICAgICAgICBzYmx4OiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgem5zYkRpYWxvZ0Zvcm06IGZhbHNlLAogICAgICBhc3NldFN1Ym1pdExvYWRpbmc6IGZhbHNlLAogICAgICAvL+iuvuWkh+ihqOWNlQogICAgICBzYnh4Rm9ybToge30sCiAgICAgIC8v6K6+5aSH5L+h5oGv5bGV56S6CiAgICAgIGFzc2V0SXNEaXNhYmxlOiBmYWxzZSwKICAgICAgLy/lvLnlh7rmoYZ0YWLpobUKICAgICAgYWN0aXZlVGFiTmFtZTogInNiRGVzYyIsCiAgICAgIC8v5p+l6K+i6Ze06ZqU5LiL5ouJ5qGG5pWw5o2u55qE5Y+C5pWwCiAgICAgIHNlbGVjdEpnT3B0aW9uc1BhcmFtOiB7fSwKICAgICAgLy/mlrDlop7orr7lpIfml7bmiYDlsZ7pl7TpmpTkuIvmi4nmoYbliJfooagKICAgICAgamdPcHRpb25zRGF0YUxpc3Q6IFtdLAogICAgICAvL+iuvuWkh+exu+Wei+S4i+aLieahhuaVsOaNrgogICAgICBzYmx4T3B0aW9uc0RhdGFTZWxlY3RlZDogW10sCiAgICAgIC8v5oqA5pyv5Y+C5pWw57uR5a6aCiAgICAgIGpzY3NGb3JtOiB7fSwKICAgICAgLy/mioDmnK/lj4LmlbDliqjmgIHlsZXnpLrpm4blkIgKICAgICAganNjc0xhYmVsTGlzdDogW10sCiAgICAgIC8v6K6+5aSH5bGl5Y6GdGFi6aG1CiAgICAgIHNibGxEZXNjVGFiTmFtZTogInp0YmdqbCIsCiAgICAgIC8v6K6+5aSH5bGl5Y6G6K+V6aqM6K6w5b2V5pWw5o2uCiAgICAgIHNibHZzeWpsTGlzdDogW10sCiAgICAgIC8v6K6+5aSH5bGl5Y6G57y66Zm36K6w5b2V5pWw5o2u6ZuG5ZCICiAgICAgIHNibGxxeGpsTGlzdDogW10sCiAgICAgIHJlc3VtUGFnZUluZm86IHsKICAgICAgICBwYWdlcjogewogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMDBdCiAgICAgICAgfSwKICAgICAgICBvcHRpb246IHsKICAgICAgICAgIGNoZWNrQm94OiBmYWxzZSwKICAgICAgICAgIHNlcmlhbE51bWJlcjogdHJ1ZQogICAgICAgIH0sCiAgICAgICAgdGFibGVEYXRhOiBbXSwKICAgICAgICB0YWJsZUhlYWRlcjogWwogICAgICAgICAgeyBwcm9wOiAiZm9yZWlnbk51bSIsIGxhYmVsOiAi6K6+5aSH5ZCN56ewIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICAvLyB7IHByb3A6ICJzYmx4IiwgbGFiZWw6ICLorr7lpIfnsbvlnosiLCBtaW5XaWR0aDogIjE4MCIgfSwKICAgICAgICAgIHsgcHJvcDogImJnbHgiLCBsYWJlbDogIuWPmOabtOexu+WeiyIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBwcm9wOiAibXMiLCBsYWJlbDogIuaPj+i/sCIsIG1pbldpZHRoOiAiMjUwIiB9LAogICAgICAgICAgeyBwcm9wOiAiYmdyIiwgbGFiZWw6ICLlj5jmm7TkuroiLCBtaW5XaWR0aDogIjE0MCIgfSwKICAgICAgICAgIHsgcHJvcDogImJnc2oiLCBsYWJlbDogIuWPmOabtOaXtumXtCIsIG1pbldpZHRoOiAiMTQwIiB9CiAgICAgICAgXQogICAgICB9LAogICAgICAvL+iuvuWkh+mZhOWxnuiuvuaWvWxpc3QKICAgICAgZnNzc0xpc3Q6IFtdLAogICAgICAvL+iuvuWkh+ivpuaDheagoemqjOinhOWImQogICAgICBzYnh4UnVsZXM6IHsKICAgICAgICBzc2dzOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5omA5bGe5YWs5Y+45LiN6IO95Li656m6IiwgdHJpZ2dlcjogInNlbGVjdCIgfQogICAgICAgIF0sCiAgICAgICAgc3NiZHo6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmiYDlsZ7nlLXnq5nkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAic2VsZWN0IiB9CiAgICAgICAgXSwKICAgICAgICBzc2pnOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5omA5bGe6Ze06ZqU5LiN6IO95Li656m6IiwgdHJpZ2dlcjogInNlbGVjdCIgfQogICAgICAgIF0sCiAgICAgICAgc2JtYzogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuiuvuWkh+WQjeensOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBhc3NldFR5cGVDb2RlOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K6+5aSH57G75Z6L5LiN6IO95Li656m6IiwgdHJpZ2dlcjogInNlbGVjdCIgfQogICAgICAgIF0sCiAgICAgICAgLy8gZHlkamJtOiBbCiAgICAgICAgLy8gICB7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnlLXljovnrYnnuqfkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAic2VsZWN0In0sCiAgICAgICAgLy8gXSwKICAgICAgICBzYnp0OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K6+5aSH54q25oCB5LiN6IO95Li656m6IiwgdHJpZ2dlcjogInNlbGVjdCIgfQogICAgICAgIF0sCiAgICAgICAgLy8gYXp3ejogWwogICAgICAgIC8vICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5a6J6KOF5L2N572u5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIifSwKICAgICAgICAvLyBdLAogICAgICAgIC8vIHhoOiBbCiAgICAgICAgLy8gICB7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlnovlj7fkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAic2VsZWN0In0sCiAgICAgICAgLy8gXSwKICAgICAgICAvLyBlZGR5OiBbCiAgICAgICAgLy8gICB7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLpop3lrprnlLXljovkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciJ9LAogICAgICAgIC8vIF0sCiAgICAgICAgLy8gZWRwbDogWwogICAgICAgIC8vICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6aKd5a6a6aKR546H5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIifSwKICAgICAgICAvLyBdLAogICAgICAgIHN5aGo6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkvb/nlKjnjq/looPkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgc2NjajogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIueUn+S6p+WOguWutuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICAvLyB5eGJoOiBbCiAgICAgICAgLy8gICB7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLov5DooYznvJblj7fkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciJ9LAogICAgICAgIC8vIF0sCiAgICAgICAgLy8gY2NiaDogWwogICAgICAgIC8vICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5Ye65Y6C57yW5Y+35LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIifSwKICAgICAgICAvLyBdLAogICAgICAgIC8vIGNjcnE6IFsKICAgICAgICAvLyAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWHuuWOguaXpeacn+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UifSwKICAgICAgICAvLyBdLAogICAgICAgIHR5cnE6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmipXov5Dml6XmnJ/kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9CiAgICAgICAgXQogICAgICB9LAogICAgICBydWxlc0NvcHk6IHsKICAgICAgICBzc2R3Ym06IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmiYDlsZ7lhazlj7jkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAic2VsZWN0IiB9CiAgICAgICAgXSwKICAgICAgICBiZHo6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmiYDlsZ7nlLXnq5nkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAic2VsZWN0IiB9CiAgICAgICAgXSwKICAgICAgICBzc2pnOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5omA5bGe6Ze06ZqU5LiN6IO95Li656m6IiwgdHJpZ2dlcjogInNlbGVjdCIgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgZ2d4aExpc3Q6IFtdLCAvL+Wei+WPt2xpc3QKICAgICAgamdseGN4OiAiIiwgLy/miYDlsZ7pl7TpmpTnsbvlnosKICAgICAgc3NiZHo6ICIiLCAvL+aJgOWxnuWPmOeUteermSwKICAgICAgc3NiZHptYzogIiIsCiAgICAgIHNzamc6ICIiLCAvL+aJgOWxnumXtOmalAogICAgICB6bnNiUGFyYW1zOiB7CiAgICAgICAgLy/nq5nlhoXorr7lpIfliIbpobXlj4LmlbAKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgcGFnZU51bTogMQogICAgICB9LAogICAgICBmZ3NBcnI6IFtdLAogICAgICBqZ2RsTWFwOiBuZXcgTWFwKCksIC8v5a2Y6Ze06ZqU5aSn57G75pWw5o2uCiAgICAgIGN1cnJlbnRCZHo6ICIiLCAvL+W9k+WJjeWPmOeUteermQogICAgICBzaG93RnNzczogZmFsc2UsIC8v5piv5ZCm5pi+56S66ZmE5bGe6K6+5pa9dGFi6aG1CiAgICAgIGJkc2JpZDogIiIsIC8v5Y+Y55S16K6+5aSHSUQKICAgICAgc2JtYzogIiIsIC8v6K6+5aSH5ZCN56ewCiAgICAgIC8v5LiN6Imv5bel5Ya15YiX6KGoCiAgICAgIGJsZ2tQYWdlSW5mbzogewogICAgICAgIHBhZ2VyOiB7CiAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgdG90YWw6IDAsCiAgICAgICAgICBzaXplczogWzEwLCAyMCwgNTAsIDEwMF0KICAgICAgICB9LAogICAgICAgIG9wdGlvbjogeyBjaGVja0JveDogZmFsc2UsIHNlcmlhbE51bWJlcjogdHJ1ZSB9LAogICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgdGFibGVIZWFkZXI6IFsKICAgICAgICAgIC8vIHsgcHJvcDogImZnc21jIiwgbGFiZWw6ICLliIblhazlj7giLCBtaW5XaWR0aDogIjE0MCIgfSwKICAgICAgICAgIC8vIHsgcHJvcDogImJkem1jIiwgbGFiZWw6ICLlj5jnlLXnq5kiLCBtaW5XaWR0aDogIjE0MCIgfSwKICAgICAgICAgIC8vIHsgcHJvcDogImpnbWMiLCBsYWJlbDogIumXtOmalOWQjeensCIsIG1pbldpZHRoOiAiMTAwIiB9LAogICAgICAgICAgLy8geyBwcm9wOiAic2JseG1jIiwgbGFiZWw6ICLorr7lpIfnsbvlnosiLCBtaW5XaWR0aDogIjE0MCIgfSwKICAgICAgICAgIHsgcHJvcDogInNibWMiLCBsYWJlbDogIuiuvuWkh+WQjeensCIsIG1pbldpZHRoOiAiMTQwIiB9LAogICAgICAgICAgeyBwcm9wOiAibXMiLCBsYWJlbDogIuS4jeiJr+W3peWGteaPj+i/sCIsIG1pbldpZHRoOiAiMTYwIiwgc2hvd1BvcDogdHJ1ZSB9LAogICAgICAgICAgeyBwcm9wOiAiZmx5akNuIiwgbGFiZWw6ICLliIbnsbvkvp3mja4iLCBtaW5XaWR0aDogIjE2MCIsIHNob3dQb3A6IHRydWUgfSwKICAgICAgICAgIHsgcHJvcDogImxycm1jIiwgbGFiZWw6ICLlvZXlhaXkuroiLCBtaW5XaWR0aDogIjEwMCIgfSwKICAgICAgICAgIHsgcHJvcDogImZ4c2oiLCBsYWJlbDogIuWPkeeOsOaXtumXtCIsIG1pbldpZHRoOiAiMTAwIiB9LAogICAgICAgICAgeyBwcm9wOiAieGNzaiIsIGxhYmVsOiAi5raI6Zmk5pe26Ze0IiwgbWluV2lkdGg6ICIxNDAiIH0KICAgICAgICBdCiAgICAgIH0sCgogICAgICAvL+WFtuWug+iuvuWkh+mXrumimAogICAgICBxdHNid3RQYWdlSW5mbzogewogICAgICAgIHBhZ2VyOiB7CiAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgdG90YWw6IDAsCiAgICAgICAgICBzaXplczogWzEwLCAyMCwgNTAsIDEwMF0KICAgICAgICB9LAogICAgICAgIG9wdGlvbjogeyBjaGVja0JveDogZmFsc2UsIHNlcmlhbE51bWJlcjogdHJ1ZSB9LAogICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgdGFibGVIZWFkZXI6IFsKICAgICAgICAgIHsgcHJvcDogInNibWMiLCBsYWJlbDogIuiuvuWkh+WQjeensCIsIG1pbldpZHRoOiAiMTQwIiB9LAogICAgICAgICAgeyBwcm9wOiAibXMiLCBsYWJlbDogIumXrumimOaPj+i/sCIsIG1pbldpZHRoOiAiMTYwIiwgc2hvd1BvcDogdHJ1ZSB9LAogICAgICAgICAgeyBwcm9wOiAibHJybWMiLCBsYWJlbDogIuW9leWFpeS6uiIsIG1pbldpZHRoOiAiMTAwIiB9LAogICAgICAgICAgeyBwcm9wOiAiZnhzaiIsIGxhYmVsOiAi5Y+R546w5pe26Ze0IiwgbWluV2lkdGg6ICIxMDAiIH0KICAgICAgICBdCiAgICAgIH0sCgogICAgICAvL+ivlemqjOWIl+ihqAogICAgICBzeVBhZ2VJbmZvOiB7CiAgICAgICAgcGFnZXI6IHsKICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgIHNpemVzOiBbMTAsIDIwLCA1MCwgMF0KICAgICAgICB9LAogICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgdGFibGVIZWFkZXI6IFsKICAgICAgICAgIHsgbGFiZWw6ICLor5XpqozkuJPkuJoiLCBwcm9wOiAic3l6eSIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLor5XpqozmgKfotKgiLCBwcm9wOiAic3l4eiIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLor5XpqozlkI3np7AiLCBwcm9wOiAic3ltYyIsIG1pbldpZHRoOiAiMjAwIiwgc2hvd1BvcDogdHJ1ZSB9LAogICAgICAgICAgLy8geyBsYWJlbDogIuiuvuWkh+WcsOeCuSIsIHByb3A6ICJzeWRkIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi6K+V6aqM6K6+5aSHIiwgcHJvcDogInNibWMiLCBtaW5XaWR0aDogIjEwMCIsIHNob3dQb3A6IHRydWUgfSwKICAgICAgICAgIHsgbGFiZWw6ICLor5XpqozmqKHmnb/lkI3np7AiLCBwcm9wOiAic3ltYiIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBsYWJlbDogIuWkqeawlCIsIHByb3A6ICJ0cSIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLor5Xpqozml6XmnJ8iLCBwcm9wOiAic3lycSIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLor5XpqozkurrlkZgiLCBwcm9wOiAic3lyeWlkIiB9LAogICAgICAgICAgeyBsYWJlbDogIua1geeoi+eKtuaAgSIsIHByb3A6ICJ6dG1jIiB9CiAgICAgICAgXSwKICAgICAgICBvcHRpb246IHsgY2hlY2tCb3g6IGZhbHNlLCBzZXJpYWxOdW1iZXI6IHRydWUgfQogICAgICB9LAogICAgICAvL+makOaCo+WIl+ihqAogICAgICB5aFBhZ2VJbmZvOiB7CiAgICAgICAgcGFnZXI6IHsKICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgIHBhZ2VSZXNpemU6ICIiLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMDBdCiAgICAgICAgfSwKICAgICAgICBvcHRpb246IHsKICAgICAgICAgIGNoZWNrQm94OiBmYWxzZSwKICAgICAgICAgIHNlcmlhbE51bWJlcjogdHJ1ZQogICAgICAgIH0sCiAgICAgICAgdGFibGVEYXRhOiBbXSwKICAgICAgICB0YWJsZUhlYWRlcjogWwogICAgICAgICAgLy8geyBwcm9wOiAic3NncyIsIGxhYmVsOiAi5omA5bGe5YWs5Y+4IiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICAvLyB7IHByb3A6ICJzc2R6IiwgbGFiZWw6ICLmiYDlsZ7kvY3nva4iLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgcHJvcDogInNiIiwgbGFiZWw6ICLkuLvorr7lpIciLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIC8vIHsgcHJvcDogInNibHgiLCBsYWJlbDogIuiuvuWkh+exu+WeiyIsIG1pbldpZHRoOiAiMTAwIiB9LAogICAgICAgICAgLy8ge3Byb3A6ICdkeWRqJywgbGFiZWw6ICfnlLXljovnrYnnuqcnLCBtaW5XaWR0aDogJzEwMCd9LAogICAgICAgICAgeyBwcm9wOiAic2J4aCIsIGxhYmVsOiAi6K6+5aSH5Z6L5Y+3IiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJzY2NqIiwgbGFiZWw6ICLnlJ/kuqfljoLlrrYiLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgcHJvcDogImJ6cXhReGRqIiwgbGFiZWw6ICLpmpDmgqPmgKfotKgiLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgcHJvcDogInF4bnIiLCBsYWJlbDogIumakOaCo+WGheWuuSIsIG1pbldpZHRoOiAiMTIwIiwgc2hvd1BvcDogdHJ1ZSB9LAogICAgICAgICAgeyBwcm9wOiAianhsYkNuIiwgbGFiZWw6ICLmmK/lkKbop6blj5HnirbmgIHor4Tku7ciLCBtaW5XaWR0aDogIjgwIiB9LAogICAgICAgICAgeyBwcm9wOiAienRtYyIsIGxhYmVsOiAi54q25oCBIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJmeHJxIiwgbGFiZWw6ICLlj5HnjrDml7bpl7QiLCBtaW5XaWR0aDogIjEwMCIsIGN1c3RvbTogdHJ1ZSB9CiAgICAgICAgXQogICAgICB9LAogICAgICAvL+ajgOS/ruWIl+ihqAogICAgICBqeFBhZ2VJbmZvOiB7CiAgICAgICAgcGFnZXI6IHsKICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgIHBhZ2VSZXNpemU6ICIiLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMDBdCiAgICAgICAgfSwKICAgICAgICBvcHRpb246IHsKICAgICAgICAgIGNoZWNrQm94OiBmYWxzZSwKICAgICAgICAgIHNlcmlhbE51bWJlcjogdHJ1ZQogICAgICAgIH0sCiAgICAgICAgdGFibGVEYXRhOiBbXSwKICAgICAgICB0YWJsZUhlYWRlcjogWwogICAgICAgICAgLy8geyBwcm9wOiAiZmdzbWMiLCBsYWJlbDogIuWIhuWFrOWPuCIsIG1pbldpZHRoOiAiMTUwIiB9LAogICAgICAgICAgLy8geyBwcm9wOiAiYmR6bWMiLCBsYWJlbDogIuWPmOeUteermSIsIG1pbldpZHRoOiAiMTUwIiB9LAogICAgICAgICAgLy8geyBwcm9wOiAic3NqZyIsIGxhYmVsOiAi5omA5bGe6Ze06ZqUIiwgbWluV2lkdGg6ICIxNTAiIH0sCiAgICAgICAgICB7IHByb3A6ICJzYm1jIiwgbGFiZWw6ICLorr7lpIflkI3np7AiLCBtaW5XaWR0aDogIjEwMCIgfSwKICAgICAgICAgIHsgcHJvcDogInJxIiwgbGFiZWw6ICLml6XmnJ8iLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgcHJvcDogInhzbGIiLCBsYWJlbDogIuS/ruivleexu+WIqyIsIG1pbldpZHRoOiAiMTAwIiB9LAogICAgICAgICAgeyBwcm9wOiAibnIiLCBsYWJlbDogIuWGheWuuSIsIG1pbldpZHRoOiAiMTAwIiB9LAogICAgICAgICAgeyBwcm9wOiAiamwiLCBsYWJlbDogIue7k+iuuiIsIG1pbldpZHRoOiAiODAiIH0sCiAgICAgICAgICB7IHByb3A6ICJ4c2Z6ciIsIGxhYmVsOiAi5L+u6K+V6LSf6LSj5Lq6IiwgbWluV2lkdGg6ICI5MCIgfSwKICAgICAgICAgIHsgcHJvcDogInlzZnpyIiwgbGFiZWw6ICLpqozmlLbotJ/otKPkuroiLCBtaW5XaWR0aDogIjkwIiB9LAogICAgICAgICAgeyBwcm9wOiAiamxyIiwgbGFiZWw6ICLorrDlvZXkuroiLCBtaW5XaWR0aDogIjkwIiB9CiAgICAgICAgXQogICAgICB9LAogICAgICAvL+e7p+eUteS/neaKpOWIl+ihqAogICAgICBqZGJoUGFnZUluZm86IHsKICAgICAgICBwYWdlcjogewogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgcGFnZVJlc2l6ZTogIiIsCiAgICAgICAgICBzaXplczogWzEwLCAyMCwgNTAsIDEwMF0KICAgICAgICB9LAogICAgICAgIG9wdGlvbjogewogICAgICAgICAgY2hlY2tCb3g6IGZhbHNlLAogICAgICAgICAgc2VyaWFsTnVtYmVyOiB0cnVlCiAgICAgICAgfSwKICAgICAgICB0YWJsZURhdGE6IFtdLAogICAgICAgIHRhYmxlSGVhZGVyOiBbCiAgICAgICAgICB7IHByb3A6ICJycSIsIGxhYmVsOiAi5pel5pyfIiwgbWluV2lkdGg6ICI4MCIgfSwKICAgICAgICAgIHsgcHJvcDogIm5yIiwgbGFiZWw6ICLlhoXlrrkiLCBtaW5XaWR0aDogIjE4MCIgfSwKICAgICAgICAgIHsgcHJvcDogImpsIiwgbGFiZWw6ICLnu5PorroiLCBtaW5XaWR0aDogIjgwIiB9LAogICAgICAgICAgeyBwcm9wOiAic3lnemZ6ciIsIGxhYmVsOiAi6K+V6aqM5bel5L2c6LSf6LSj5Lq6IiwgbWluV2lkdGg6ICIxMTAiIH0sCiAgICAgICAgICB7IHByb3A6ICJ5c2Z6ciIsIGxhYmVsOiAi6aqM5pS26LSf6LSj5Lq6IiwgbWluV2lkdGg6ICIxMDAiIH0sCiAgICAgICAgICB7IHByb3A6ICJqbHIiLCBsYWJlbDogIuiusOW9leS6uiIsIG1pbldpZHRoOiAiMTAwIiB9CiAgICAgICAgXQogICAgICB9LAogICAgICAvL+i3s+mXuOiusOW9leWIl+ihqAogICAgICB0empsUGFnZUluZm86IHsKICAgICAgICBwYWdlcjogewogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgcGFnZVJlc2l6ZTogIiIsCiAgICAgICAgICBzaXplczogWzEwLCAyMCwgNTAsIDEwMF0KICAgICAgICB9LAogICAgICAgIG9wdGlvbjogewogICAgICAgICAgY2hlY2tCb3g6IGZhbHNlLAogICAgICAgICAgc2VyaWFsTnVtYmVyOiB0cnVlCiAgICAgICAgfSwKICAgICAgICB0YWJsZURhdGE6IFtdLAogICAgICAgIHRhYmxlSGVhZGVyOiBbCiAgICAgICAgICB7IHByb3A6ICJycXNqIiwgbGFiZWw6ICLml6XmnJ/ml7bpl7QiLCBtaW5XaWR0aDogIjE2MCIgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgcHJvcDogImJoZHpxayIsCiAgICAgICAgICAgIGxhYmVsOiAi5L+d5oqk5Yqo5L2c5oOF5Ya1IiwKICAgICAgICAgICAgbWluV2lkdGg6ICIxODAiLAogICAgICAgICAgICBpc1Nob3dQcm9wOiB0cnVlCiAgICAgICAgICB9LAogICAgICAgICAgeyBwcm9wOiAiZGxxamNxayIsIGxhYmVsOiAi5pat6Lev5Zmo5qOA5p+l5oOF5Ya1IiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJnemRsIiwgbGFiZWw6ICLmlYXpmpznlLXmtYEiLCBtaW5XaWR0aDogIjEwMCIgfSwKICAgICAgICAgIHsgcHJvcDogImd6dHpjcyIsIGxhYmVsOiAi5pWF6Zqc6Lez6Ze45qyh5pWwIiwgbWluV2lkdGg6ICIxMDAiIH0sCiAgICAgICAgICB7IHByb3A6ICJqbHR6amwiLCBsYWJlbDogIue0r+iuoei3s+mXuOasoeaVsCIsIG1pbldpZHRoOiAiMTAwIiB9LAogICAgICAgICAgeyBwcm9wOiAiemhkeHJxIiwgbGFiZWw6ICLmnIDlkI7lpKfkv67ml6XmnJ8iLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgcHJvcDogImpsciIsIGxhYmVsOiAi6K6w5b2V5Lq6IiwgbWluV2lkdGg6ICIxMjAiIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIHlobG9hZGluZzogZmFsc2UsCiAgICAgIHN5bG9hZGluZzogZmFsc2UsCiAgICAgIGJsZ2tsb2FkaW5nOiBmYWxzZSwKICAgICAgcXRzYnd0bG9hZGluZzogZmFsc2UsCiAgICAgIGp4bG9hZGluZzogZmFsc2UsCiAgICAgIGpkYmhMb2FkaW5nOiBmYWxzZSwKICAgICAgdHpqbExvYWRpbmc6IGZhbHNlLAogICAgICBpc1NlbGVjdGluZ0Zyb21Ecm9wZG93bjogZmFsc2UsIC8vIOa3u+WKoOagh+iusOWPmOmHjwogICAgfTsKICB9LAogIHdhdGNoOiB7CiAgICAvL+ebkeWQrOetm+mAieahhuWAvOWPkeeUn+WPmOWMlui/m+iAjOetm+mAieagkee7k+aehAogICAgZmlsdGVyVGV4dCh2YWwpIHsKICAgICAgdGhpcy4kcmVmcy50cmVlLmZpbHRlcih2YWwpOwogICAgfQogIH0sCiAgYXN5bmMgY3JlYXRlZCgpIHsKICAgIC8v6I635Y+W57uE57uH57uT5p6E5LiL5ouJ5pWw5o2uCiAgICB0aGlzLmdldEZnc09wdGlvbnMoKTsKICAgIHRoaXMuZ2V0SmR6T3B0aW9ucygpOwogICAgLy/ojrflj5blj5jnlLXnq5nkuIvmi4nmoYbmlbDmja4KICAgIHRoaXMuZ2V0QmR6RGF0YUxpc3RTZWxlY3RlZCgpOwogICAgLy/ojrflj5bpgInmi6nmoYbmlbDmja4KICAgIHRoaXMuZ2V0U2VsZWN0RGF0YUluZm8oKTsKICAgIC8v6I635Y+W5paw55qE6K6+5aSH5ouT5omR5qCRCiAgICBhd2FpdCB0aGlzLmdldE5ld1RyZWVJbmZvKCk7CiAgICB0aGlzLmlzU2hvdzEgPSB0cnVlOwogICAgLy/liJ3lp4vljJbliqDovb3ml7bliqDovb3miYDmnInlj5jnlLXnq5nkv6Hmga8KICAgIGF3YWl0IHRoaXMuZ2V0RGF0YSgpOwogICAgYXdhaXQgdGhpcy5nZXRKZ2x4TGlzdCgpOwogICAgLy/liJ3lp4vljJbml7bliqDovb3pobXpnaLlhoXlrrkKICAgIHRoaXMudGFibGVBbmRQYWdlSW5mbyA9IHsgLi4udGhpcy50YWJsZUFuZFBhZ2VJbmZvMSB9OwogIH0sCiAgbW91bnRlZCgpIHsKICAgIC8v6I635Y+WdG9rZW4KICAgIHRoaXMuaGVhZGVyLnRva2VuID0gZ2V0VG9rZW4oKTsKICAgIHRoaXMuY3VyclVzZXIgPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLm5hbWU7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvL+aJgOWxnuWFrOWPuGNoYW5nZeS6i+S7tgogICAgaGFuZGxlRmdzQ2hhbmdlKGZnc1ZhbHVlKSB7CiAgICAgIC8v5riF56m65LmL5YmN5b6X6YCJ5Lit5YC8CiAgICAgIHRoaXMud3pEYXRhTGlzdE9wdGlvbnMgPSBbXTsKICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybUNvcHksICJiZHoiLCAiIik7CiAgICAgIC8v6I635Y+W5Y+Y55S156uZ5pa55rOVCiAgICAgIGdldEJkekRhdGFMaXN0U2VsZWN0ZWQoeyBzc2R3Ym06IGZnc1ZhbHVlIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLnd6RGF0YUxpc3RPcHRpb25zID0gcmVzLmRhdGE7CiAgICAgIH0pOwogICAgfSwKICAgIGFzeW5jIGdldFNiRGF0YUxpc3RHcm91cCh2YWwpIHsKICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybUNvcHksICJzc2pnIiwgIiIpOwogICAgICBsZXQgcmVzID0gYXdhaXQgZ2V0SmdEYXRhTGlzdFNlbGVjdGVkKHsgc3NiZHo6IHZhbCArICIiIH0pOwogICAgICBpZiAocmVzLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgIHRoaXMuc2JEYXRhTGlzdCA9IHJlcy5kYXRhOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgIG1lc3NhZ2U6ICLpl7TpmpTmlbDmja7ojrflj5blpLHotKUhIgogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgeHNDaGFuZ2VGdW5jKHZhbCkgewogICAgICBpZiAodmFsID09PSAi5LiJ55u4IikgewogICAgICAgIHRoaXMuJHNldCh0aGlzLnNieHhGb3JtLCAieGIiLCAiQUJDIik7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kc2V0KHRoaXMuc2J4eEZvcm0sICJ4YiIsICIiKTsKICAgICAgfQogICAgfSwKICAgIC8qKgogICAgICog6I635Y+W5Z+65Zyw56uZ5LiL5ouJ5pWw5o2uCiAgICAgKi8KICAgIGdldEpkek9wdGlvbnMoKSB7CiAgICAgIGdldFNlbGVjdE9wdGlvbnNCeU9yZ1R5cGUoSlNPTi5zdHJpbmdpZnkoIjA3IikpLnRoZW4ocmVzID0+IHsKICAgICAgICByZXMuZGF0YS5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgaXRlbS52YWx1ZSA9IGl0ZW0udmFsdWUudG9TdHJpbmcoKTsKICAgICAgICB9KTsKICAgICAgICB0aGlzLnNzamR6TGlzdCA9IHJlcy5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICAvL+afpeivouajgOS/ruaVsOaNrgogICAgZ2V0SnhMaXN0KHBhcmFtcykgewogICAgICB0aGlzLmp4bG9hZGluZyA9IHRydWU7CiAgICAgIGNvbnN0IHBhcmFtID0gewogICAgICAgIC4uLnsgcGFnZVNpemU6IDEwLCBwYWdlTnVtOiAxIH0sCiAgICAgICAgLi4ucGFyYW1zLAogICAgICAgIC4uLnsgc2JtYzogdGhpcy5zYm1jLCBiZHo6IHRoaXMuc3NiZHogfSwKICAgICAgICAuLi57IG15U29ydHM6IFt7IHByb3A6ICJycSIsIGFzYzogZmFsc2UgfV0gfQogICAgICB9OwogICAgICBnZXRMaXN0Rm91cihwYXJhbSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuanhQYWdlSW5mby50YWJsZURhdGEgPSByZXMuZGF0YS5yZWNvcmRzOwogICAgICAgIHRoaXMuanhQYWdlSW5mby5wYWdlci50b3RhbCA9IHJlcy5kYXRhLnRvdGFsOwogICAgICAgIHRoaXMuanhsb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIGdldEpkYmhMaXN0KHBhcmFtcykgewogICAgICB0aGlzLmpkYmhMb2FkaW5nID0gdHJ1ZTsKICAgICAgY29uc3QgcGFyYW0gPSB7CiAgICAgICAgLi4ueyBwYWdlU2l6ZTogMTAsIHBhZ2VOdW06IDEgfSwKICAgICAgICAuLi5wYXJhbXMsCiAgICAgICAgLi4ueyBzYm1jOiB0aGlzLnNibWMsIGJkejogdGhpcy5zc2JkeiB9LAogICAgICAgIC4uLnsgbXlTb3J0czogW3sgcHJvcDogInJxIiwgYXNjOiBmYWxzZSB9XSB9CiAgICAgIH07CiAgICAgIGdldExpc3RTZWNvbmQocGFyYW0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmpkYmhQYWdlSW5mby50YWJsZURhdGEgPSByZXMuZGF0YS5yZWNvcmRzOwogICAgICAgIHRoaXMuamRiaFBhZ2VJbmZvLnBhZ2VyLnRvdGFsID0gcmVzLmRhdGEudG90YWw7CiAgICAgICAgdGhpcy5qZGJoTG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICBnZXRUempsTGlzdChwYXJhbXMpIHsKICAgICAgdGhpcy50empsTG9hZGluZyA9IHRydWU7CiAgICAgIGNvbnN0IHBhcmFtID0gewogICAgICAgIC4uLnsgcGFnZVNpemU6IDEwLCBwYWdlTnVtOiAxIH0sCiAgICAgICAgLi4ucGFyYW1zLAogICAgICAgIC4uLnsgZGxxYmg6IHRoaXMuc2JtYywgYmR6OiB0aGlzLnNzYmR6IH0sCiAgICAgICAgLi4ueyBteVNvcnRzOiBbeyBwcm9wOiAicnFzaiIsIGFzYzogZmFsc2UgfV0gfQogICAgICB9OwogICAgICBnZXRMaXN0U2V2ZW4ocGFyYW0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLnR6amxQYWdlSW5mby50YWJsZURhdGEgPSByZXMuZGF0YS5yZWNvcmRzOwogICAgICAgIHRoaXMudHpqbFBhZ2VJbmZvLnBhZ2VyLnRvdGFsID0gcmVzLmRhdGEudG90YWw7CiAgICAgICAgdGhpcy50empsTG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvL+afpeivouS4jeiJr+W3peWGteaVsOaNrgogICAgZ2V0Qmxna0xpc3QocGFyYW1zKSB7CiAgICAgIHRoaXMuYmxna2xvYWRpbmcgPSB0cnVlOwogICAgICBjb25zdCBwYXJhbSA9IHsKICAgICAgICAuLi57IHBhZ2VTaXplOiAxMCwgcGFnZU51bTogMSB9LAogICAgICAgIC4uLnBhcmFtcywKICAgICAgICAuLi57IHNiaWQ6IHRoaXMuYmRzYmlkIH0KICAgICAgfTsKICAgICAgZ2V0RGF0YShwYXJhbSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuYmxna1BhZ2VJbmZvLnRhYmxlRGF0YSA9IHJlcy5kYXRhLnJlY29yZHM7CiAgICAgICAgdGhpcy5ibGdrUGFnZUluZm8ucGFnZXIudG90YWwgPSByZXMuZGF0YS50b3RhbDsKICAgICAgICB0aGlzLmJsZ2tsb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKCiAgICAvL+WFtuWug+iuvuWkh+W9leWFpemXrumimAogICAgZ2V0cXRzYnd0TGlzdChwYXJhbXMpIHsKICAgICAgdGhpcy5xdHNid3Rsb2FkaW5nID0gdHJ1ZTsKICAgICAgY29uc3QgcGFyYW0gPSB7CiAgICAgICAgLi4ueyBwYWdlU2l6ZTogMTAsIHBhZ2VOdW06IDEgfSwKICAgICAgICAuLi5wYXJhbXMsCiAgICAgICAgLi4ueyBzYmlkOiB0aGlzLmJkc2JpZCB9CiAgICAgIH07CiAgICAgIGdldFF0d3RsckRhdGEocGFyYW0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLnF0c2J3dFBhZ2VJbmZvLnRhYmxlRGF0YSA9IHJlcy5kYXRhLnJlY29yZHM7CiAgICAgICAgdGhpcy5xdHNid3RQYWdlSW5mby5wYWdlci50b3RhbCA9IHJlcy5kYXRhLnRvdGFsOwogICAgICAgIHRoaXMucXRzYnd0bG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCgogICAgLy/or5XpqozmlbDmja4KICAgIGdldFN5TGlzdChwYXJhbXMpIHsKICAgICAgdGhpcy5zeWxvYWRpbmcgPSB0cnVlOwogICAgICBjb25zdCBwYXJhbSA9IHsKICAgICAgICAuLi57IHBhZ2VTaXplOiAxMCwgcGFnZU51bTogMSB9LAogICAgICAgIC4uLnBhcmFtcywKICAgICAgICAuLi57IHN5c2JpZDogdGhpcy5iZHNiaWQgfQogICAgICB9OwogICAgICBnZXRTeWJnamxEYXRhQnlQYWdlKHBhcmFtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIHRoaXMuc3lQYWdlSW5mby50YWJsZURhdGEgPSByZXMuZGF0YS5yZWNvcmRzOwogICAgICAgICAgdGhpcy5zeVBhZ2VJbmZvLnBhZ2VyLnRvdGFsID0gcmVzLmRhdGEudG90YWw7CiAgICAgICAgfQogICAgICAgIHRoaXMuc3lsb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8v6ZqQ5oKj5pWw5o2uCiAgICBnZXRZaExpc3QocGFyYW1zKSB7CiAgICAgIHRoaXMueWhsb2FkaW5nID0gdHJ1ZTsKICAgICAgY29uc3QgcGFyYW0gPSB7CiAgICAgICAgLi4ueyBwYWdlU2l6ZTogMTAsIHBhZ2VOdW06IDEgfSwKICAgICAgICAuLi5wYXJhbXMsCiAgICAgICAgLi4ueyBzYmlkOiB0aGlzLmJkc2JpZCB9CiAgICAgIH07CiAgICAgIGdldExpc3RGaXJzdChwYXJhbSkudGhlbihyZXMgPT4gewogICAgICAgIGlmIChyZXMuY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICB0aGlzLnloUGFnZUluZm8udGFibGVEYXRhID0gcmVzLmRhdGEucmVjb3JkczsKICAgICAgICAgIHRoaXMueWhQYWdlSW5mby5wYWdlci50b3RhbCA9IHJlcy5kYXRhLnRvdGFsOwogICAgICAgIH0KICAgICAgICB0aGlzLnlobG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvL+iOt+WPlumXtOmalOexu+Wei+Wtl+WFuAogICAgZ2V0SmdseExpc3QoKSB7CiAgICAgIGdldERpY3RUeXBlRGF0YSgiZHd6eV9qZ2x4IikudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuamdseE9wdGlvbnNEYXRhTGlzdCA9IHJlcy5kYXRhOwogICAgICAgIHJlcy5kYXRhLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICBpZiAoIXRoaXMuamdkbE1hcC5oYXMoaXRlbS52YWx1ZSkpIHsKICAgICAgICAgICAgdGhpcy5qZ2RsTWFwLnNldChpdGVtLnZhbHVlLCBpdGVtLnJlbWFyayk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIGdldFNlbGVjdERhdGFJbmZvKCkgewogICAgICAvLzExMCDkuI3luKblrZfmr40KICAgICAgZ2V0RGljdFR5cGVEYXRhKCJkZ19keWRqIikudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuVm9sdGFnZUxldmVsU2VsZWN0ZWRMaXN0ID0gcmVzLmRhdGE7CiAgICAgIH0pOwogICAgICAvL+mXtOmalOeJueauiuetiee6pwogICAgICBnZXREaWN0VHlwZURhdGEoImpndHpfZ2pEeWRqIikudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuamdEeWRPcHRpb25zID0gcmVzLmRhdGE7CiAgICAgICAgdGhpcy5maWx0ZXJJbmZvMi5maWVsZExpc3QubWFwKGl0ZW0gPT4gewogICAgICAgICAgaWYgKGl0ZW0udmFsdWUgPT0gImR5ZGoiKSB7CiAgICAgICAgICAgIHJldHVybiAoaXRlbS5vcHRpb25zID0gdGhpcy5qZ0R5ZE9wdGlvbnMpOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9KTsKICAgICAgLy8gMTEwa1Yg5bim5a2X5q+NCiAgICAgIGdldERpY3RUeXBlRGF0YSgiZ3R0ei1keWRqIikudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuZHlkak9wdGlvbnNXaXRoU3RyaW5nID0gcmVzLmRhdGE7CiAgICAgICAgdGhpcy5maWx0ZXJJbmZvMS5maWVsZExpc3QubWFwKGl0ZW0gPT4gewogICAgICAgICAgaWYgKGl0ZW0udmFsdWUgPT0gImR5ZGoiKSB7CiAgICAgICAgICAgIHJldHVybiAoaXRlbS5vcHRpb25zID0gdGhpcy5keWRqT3B0aW9uc1dpdGhTdHJpbmcpOwogICAgICAgICAgfQogICAgICAgIH0pOwoKICAgICAgICB0aGlzLmZpbHRlckluZm8zLmZpZWxkTGlzdC5tYXAoaXRlbSA9PiB7CiAgICAgICAgICBpZiAoaXRlbS52YWx1ZSA9PSAiZHlkak5hbWUiKSB7CiAgICAgICAgICAgIHJldHVybiAoaXRlbS5vcHRpb25zID0gdGhpcy5keWRqT3B0aW9uc1dpdGhTdHJpbmcpOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9KTsKICAgICAgLy/orr7lpIfnirbmgIEKICAgICAgZ2V0RGljdFR5cGVEYXRhKCJqZ3R6X3NienQiKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5zYnp0T3B0aW9uc0RhdGFMaXN0ID0gcmVzLmRhdGE7CiAgICAgICAgdGhpcy5maWx0ZXJJbmZvMS5maWVsZExpc3QubWFwKGl0ZW0gPT4gewogICAgICAgICAgaWYgKGl0ZW0udmFsdWUgPT0gInNienQiKSB7CiAgICAgICAgICAgIHJldHVybiAoaXRlbS5vcHRpb25zID0gdGhpcy5zYnp0T3B0aW9uc0RhdGFMaXN0KTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgICB0aGlzLmZpbHRlckluZm8zLmZpZWxkTGlzdC5tYXAoaXRlbSA9PiB7CiAgICAgICAgICBpZiAoaXRlbS52YWx1ZSA9PSAic2J6dCIpIHsKICAgICAgICAgICAgcmV0dXJuIChpdGVtLm9wdGlvbnMgPSB0aGlzLnNienRPcHRpb25zRGF0YUxpc3QpOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9KTsKICAgICAgLy/lronoo4XkvY3nva4KICAgICAgZ2V0RGljdFR5cGVEYXRhKCJqZ3R6X2F6d3oiKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5wbGFjZU9wdGlvbnMgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICAgIC8v55u45YirCiAgICAgIGdldERpY3RUeXBlRGF0YSgiamd0el94YiIpLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLnhiT3B0aW9ucyA9IHJlcy5kYXRhOwogICAgICB9KTsKICAgICAgLy/nm7jmlbAKICAgICAgZ2V0RGljdFR5cGVEYXRhKCJqZ3R6X3hzIikudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMueHNPcHRpb25zID0gcmVzLmRhdGE7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOaWsOWinuS/oeaBr+aJgOWxnueUteermeimgeiHquWKqOW4puWFpQogICAgZmlsbEJkeigpIHsKICAgICAgdGhpcy4kc2V0KHRoaXMuamd4eEZvcm0sICJzc2JkeiIsIHRoaXMuY3VycmVudEJkeik7CiAgICB9LAogICAgYXN5bmMgZGVsZXRlRmlsZUJ5SWQoaWQpIHsKICAgICAgbGV0IHsgY29kZSB9ID0gYXdhaXQgZGVsZXRlQnlJZChpZCk7CiAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICBhd2FpdCB0aGlzLmdldEZpbGVMaXN0KCk7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsCiAgICAgICAgICBtZXNzYWdlOiAi5paH5Lu25Yig6Zmk5oiQ5YqfISIKICAgICAgICB9KTsKICAgICAgfQogICAgfSwKICAgIC8v5qCR55uR5ZCs5LqL5Lu2CiAgICBmaWx0ZXJOb2RlKHZhbHVlLCBkYXRhKSB7CiAgICAgIGlmICghdmFsdWUpIHJldHVybiB0cnVlOwogICAgICByZXR1cm4gZGF0YS5sYWJlbC5pbmRleE9mKHZhbHVlKSAhPT0gLTE7CiAgICB9LAogICAgLy/ojrflj5bmlrDnmoTorr7lpIfmi5PmiZHmoJEKICAgIGdldE5ld1RyZWVJbmZvKCkgewogICAgICBnZXRVc2Vycyh7IHBlcnNvbkdyb3VwSWQ6IDc2LCBkZXB0SWQ6IDAsIGRlcHROYW1lOiAiIiB9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgbGV0IGRlcHRJZCA9IHRoaXMuJHN0b3JlLmdldHRlcnMuZGVwdElkLnRvU3RyaW5nKCk7CiAgICAgICAgcmVzLmRhdGEuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIGlmIChpdGVtLnVzZXJOYW1lID09PSB0aGlzLmN1cnJVc2VyKSB7CiAgICAgICAgICAgIC8v5aaC5p6c5Lq65ZGY57uE6YeM6Z2i5pyJ6ZyA6KaB5o6S6Zmk55qE5Lq677yM5YiZ5LiN6ZyA6KaB55SoZGVwdElk6L+b6KGM6L+H5rukCiAgICAgICAgICAgIGRlcHRJZCA9ICIiOwogICAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgICAgaWYgKHRoaXMuZmdzQXJyLmluY2x1ZGVzKGRlcHRJZCkpIHsKICAgICAgICAgIHRoaXMudHJlZUZvcm0uc3Nkd2JtID0gZGVwdElkOwogICAgICAgIH0KICAgICAgICBnZXROZXdUcmVlSW5mbyh0aGlzLnRyZWVGb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICB0aGlzLnRyZWVPcHRpb25zID0gcmVzLmRhdGE7CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKgogICAgICog6I635Y+W5YiG5YWs5Y+45LiL5ouJ5pWw5o2uCiAgICAgKi8KICAgIGdldEZnc09wdGlvbnMoKSB7CiAgICAgIGdldEZnc09wdGlvbnMoe30pLnRoZW4ocmVzID0+IHsKICAgICAgICByZXMuZGF0YS5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgaXRlbS52YWx1ZSA9IGl0ZW0udmFsdWUudG9TdHJpbmcoKTsKICAgICAgICAgIHRoaXMuZmdzQXJyLnB1c2goaXRlbS52YWx1ZS50b1N0cmluZygpKTsKICAgICAgICB9KTsKICAgICAgICB0aGlzLk9yZ2FuaXphdGlvblNlbGVjdGVkTGlzdCA9IHJlcy5kYXRhOwogICAgICAgIHRoaXMuZmlsdGVySW5mbzEuZmllbGRMaXN0Lm1hcChpdGVtID0+IHsKICAgICAgICAgIGlmIChpdGVtLnZhbHVlID09ICJzc2R3Ym0iKSB7CiAgICAgICAgICAgIHJldHVybiAoaXRlbS5vcHRpb25zID0gdGhpcy5Pcmdhbml6YXRpb25TZWxlY3RlZExpc3QpOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICAvL+aXp+agkeW9ouaVsOaNruiOt+WPlgogICAgZ2V0VHJlZUluZm9MaXN0KCkgewogICAgICBnZXRUcmVlSW5mbygpLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLnRyZWVPcHRpb25zID0gcmVzLmRhdGE7CiAgICAgIH0pOwogICAgfSwKICAgIC8v5YiX6KGo5p+l6K+iCiAgICBhc3luYyBnZXREYXRhKHBhcmFtKSB7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIHRoaXMucGFyYW1zID0gcGFyYW07CiAgICAgIGF3YWl0IGdldFNibHhEYXRhTGlzdFNlbGVjdGVkKHsgdHlwZTogIuWPmOeUteiuvuWkhyIgfSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuc2JseE9wdGlvbnNEYXRhU2VsZWN0ZWQgPSByZXMuZGF0YTsKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICAgIC8v5Yik5pat57+76aG15piv5omn6KGM55qE5ZOq5Liq6KGo5qC855qE5pWw5o2uCiAgICAgIGlmICh0aGlzLmJkemRhdGFTaG93KSB7CiAgICAgICAgLy/liJ3lp4vov5vmnaXor7fmsYLlj5jnlLXnq5nlj7DotKYKICAgICAgICBhd2FpdCB0aGlzLmdldGJkekRhdGEocGFyYW0pOwogICAgICB9CiAgICAgIGlmICh0aGlzLmpnZGF0YVNob3cpIHsKICAgICAgICBhd2FpdCB0aGlzLmdldEpnRGF0YShwYXJhbSk7CiAgICAgIH0KICAgICAgaWYgKHRoaXMuem5zYmRhdGFTaG93KSB7CiAgICAgICAgYXdhaXQgdGhpcy5nZXRabnNiRGF0YShwYXJhbSk7CiAgICAgIH0KICAgIH0sCiAgICAvKioKICAgICAqIOihqOagvOWkmumAieahhgogICAgICovCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIC8vIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0ub2JqSWQpOwogICAgICAvLyB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDE7CiAgICAgIHRoaXMuc2luZ2xlQ2xpY2tEYXRhID0KICAgICAgICBzZWxlY3Rpb24ubGVuZ3RoID4gMCA/IHsgLi4uc2VsZWN0aW9uWzBdIH0gOiB1bmRlZmluZWQ7CiAgICAgIC8vIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsKICAgIH0sCgogICAgLy/pl7TpmpTmt7vliqDmjInpkq4KICAgIGpnQWRkamdCdXR0b24oKSB7CiAgICAgIHRoaXMuamdTaG93ID0gZmFsc2U7CiAgICAgIHRoaXMuamdEaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgYWRkSmcoKSB7CiAgICAgIHRoaXMuJHJlZnNbImpneHhGb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgbGV0IGpnbHggPSB0aGlzLmpneHhGb3JtLmpnbHg7CiAgICAgICAgICBpZiAoamdseCkgewogICAgICAgICAgICAvL+S/neWtmOaXtuiuvue9rumXtOmalOWkp+exuwogICAgICAgICAgICB0aGlzLmpneHhGb3JtLmpnZGwgPSB0aGlzLmpnZGxNYXAuZ2V0KGpnbHgpOwogICAgICAgICAgfQogICAgICAgICAgYWRkSmcodGhpcy5qZ3h4Rm9ybSkudGhlbihyZXMgPT4gewogICAgICAgICAgICBpZiAocmVzLmNvZGUgPT0gIjAwMDAiKSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLmk43kvZzmiJDlip/vvIEiKTsKICAgICAgICAgICAgICB0aGlzLmpnRGlhbG9nRm9ybVZpc2libGUgPSBmYWxzZTsKICAgICAgICAgICAgICAvLyB0aGlzLnRhYmxlQW5kUGFnZUluZm8xLnBhZ2VyLnBhZ2VSZXNpemUgPSAiWSI7CiAgICAgICAgICAgICAgdGhpcy5nZXRKZ0RhdGEoKTsKICAgICAgICAgICAgICAvL+iOt+WPluaWsOeahOiuvuWkh+aLk+aJkeagkQogICAgICAgICAgICAgIHRoaXMuZ2V0TmV3VHJlZUluZm8oKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIGdldEpnTGlzdEluZm8oKSB7CiAgICAgIGdldEpnSW5mb0xpc3QodGhpcy5qZ1F1ZXJ5UGFyYW1zKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvMS50YWJsZURhdGEgPSByZXMuZGF0YS5yZWNvcmRzOwogICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mbzEucGFnZXIudG90YWwgPSByZXMuZGF0YS50b3RhbDsKICAgICAgICAvLyB0aGlzLnRhYmxlQW5kUGFnZUluZm8gPSB7Li4udGhpcy50YWJsZUFuZFBhZ2VJbmZvMn0KICAgICAgfSk7CiAgICB9LAogICAgLyoqCiAgICAgKiDliKDpmaTpl7TpmpQKICAgICAqLwogICAgcmVtb3ZlQWxsKHJvdykgewogICAgICBpZiAodGhpcy5iZHpkYXRhU2hvdykgewogICAgICAgIHRoaXMuZGVsZXRlQmR6KHJvdyk7CiAgICAgIH0KICAgICAgaWYgKHRoaXMuamdkYXRhU2hvdykgewogICAgICAgIHRoaXMucmVtb3ZlQXNzZXQocm93KTsKICAgICAgfQogICAgICBpZiAodGhpcy56bnNiZGF0YVNob3cpIHsKICAgICAgICB0aGlzLmRlbGV0ZUpnKHJvdyk7CiAgICAgIH0KICAgIH0sCiAgICByZW1vdmVBc3NldChyb3cpIHsKICAgICAgdGhpcy5mb3JtID0gcm93OwoKICAgICAgdGhpcy4kY29uZmlybSgi5q2k5pON5L2c5bCG5rC45LmF5Yig6Zmk6K+l5pWw5o2uLCDmmK/lkKbnu6fnu60/IiwgIuaPkOekuiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pCiAgICAgICAgLnRoZW4oKCkgPT4gewogICAgICAgICAgcmVtb3ZlQXNzZXQoW3RoaXMuZm9ybS5vYmpJZF0pLnRoZW4oKHsgY29kZSB9KSA9PiB7CiAgICAgICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTmiJDlip8hIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIHRoaXMuZ2V0SmdEYXRhKCk7CiAgICAgICAgICAgICAgLy/ojrflj5bmlrDnmoTorr7lpIfmi5PmiZHmoJEKICAgICAgICAgICAgICB0aGlzLmdldE5ld1RyZWVJbmZvKCk7CiAgICAgICAgICAgICAgLy8gdGhpcy50YWJsZUFuZFBhZ2VJbmZvMy5wYWdlci5wYWdlUmVzaXplID0gIlkiOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTlpLHotKUhIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoKSA9PiB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogImluZm8iLAogICAgICAgICAgICBtZXNzYWdlOiAi5bey5Y+W5raI5Yig6ZmkIgogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICB9LAogICAgZGVsZXRlQmR6KHJvdykgewogICAgICB0aGlzLmZvcm0gPSByb3c7CiAgICAgIHRoaXMuJGNvbmZpcm0oIuatpOaTjeS9nOWwhuawuOS5heWIoOmZpOivpeaVsOaNriwg5piv5ZCm57un57utPyIsICLmj5DnpLoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICByZW1vdmVCZHooW3RoaXMuZm9ybS5vYmpJZF0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsCiAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTmiJDlip8hIgogICAgICAgICAgfSk7CiAgICAgICAgICAvLyB0aGlzLnRhYmxlQW5kUGFnZUluZm8xLnBhZ2VyLnBhZ2VSZXNpemUgPSAiWSI7CiAgICAgICAgICB0aGlzLmdldGJkekRhdGEoKTsKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgZGVsZXRlSmcocm93KSB7CiAgICAgIHRoaXMuZm9ybSA9IHJvdzsKCiAgICAgIHRoaXMuJGNvbmZpcm0oIuatpOaTjeS9nOWwhuawuOS5heWIoOmZpOivpeaVsOaNriwg5piv5ZCm57un57utPyIsICLmj5DnpLoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICByZW1vdmVKZyhbdGhpcy5mb3JtLm9iaklkXSkudGhlbihyZXMgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgICAgbWVzc2FnZTogIuWIoOmZpOaIkOWKnyEiCiAgICAgICAgICB9KTsKICAgICAgICAgIC8vIHRoaXMudGFibGVBbmRQYWdlSW5mbzIucGFnZXIucGFnZVJlc2l6ZSA9ICJZIjsKICAgICAgICAgIHRoaXMuZ2V0Wm5zYkRhdGEoKTsKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgYXN5bmMgdXBkYXRlSmcocm93KSB7CiAgICAgIHRoaXMuamdEaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CiAgICAgIHRoaXMuamd4eEZvcm0gPSB7IC4uLnJvdyB9OwogICAgICB0aGlzLmpnU2hvdyA9IGZhbHNlOwogICAgfSwKCiAgICBhc3luYyBqZ0RldGFpbHMocm93KSB7CiAgICAgIHRoaXMuamdEaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CiAgICAgIHRoaXMuamd4eEZvcm0gPSB7IC4uLnJvdyB9OwogICAgICB0aGlzLmpnU2hvdyA9IHRydWU7CiAgICB9LAoKICAgIC8v5q+P6aG15bGV56S65pWw6YeP54K55Ye75LqL5Lu2CiAgICBoYW5kbGVTaXplQ2hhbmdlKCkge30sCiAgICAvL+mhteeggeaUueWPmOS6i+S7tgogICAgaGFuZGxlQ3VycmVudENoYW5nZSgpIHt9LAogICAgLy/moJHngrnlh7vkuovku7YKICAgIGhhbmRsZU5vZGVDbGljayhkYXRhLCBlKSB7CiAgICAgIC8v5qC555uu5b2V5Y+Y55S156uZ5YiX6KGoCiAgICAgIGlmIChkYXRhLmlkZW50aWZpZXIgPT0gIjAiKSB7CiAgICAgICAgLy/ngrnlh7vmoLnoioLngrnvvIzojrflj5blj5jnlLXnq5nmlbDmja4KICAgICAgICAvL+mXtOmalAogICAgICAgIHRoaXMuaXNTaG93MiA9IGZhbHNlOwogICAgICAgIC8v6YeN572ucGFnZU51bQogICAgICAgIHRoaXMuYmR6cXVlcnlQYXJhbXMgPSB7CiAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgcGFnZVNpemU6IDEwCiAgICAgICAgfTsKICAgICAgICB0aGlzLiRyZWZzLmJkelRhYmxlLmN1cnJlbnRQYWdlID0gMTsKICAgICAgICAvL+iOt+WPluWIl+ihqOaVsOaNrgogICAgICAgIHRoaXMuZ2V0YmR6RGF0YSgpOwogICAgICAgIHRoaXMuYmR6ZGF0YVNob3cgPSB0cnVlOwogICAgICAgIHRoaXMuem5zYmRhdGFTaG93ID0gdGhpcy5qZ2RhdGFTaG93ID0gZmFsc2U7CiAgICAgIH0KICAgICAgLy/kuoznuqfnm67lvZXlj5jnlLXnq5nlkI3np7AKICAgICAgZWxzZSBpZiAoZGF0YS5pZGVudGlmaWVyID09ICIxIikgewogICAgICAgIC8v54K55Ye75Y+Y55S156uZ77yM6I635Y+W6Ze06ZqU5pWw5o2uCiAgICAgICAgdGhpcy5jdXJyZW50QmR6ID0gZGF0YS5pZDsKICAgICAgICB0aGlzLnNzYmR6bWMgPSBkYXRhLmxhYmVsOwogICAgICAgIHRoaXMuc3NiZHogPSBkYXRhLmlkOwogICAgICAgIC8v6YeN572u6aG156CBCiAgICAgICAgdGhpcy4kcmVmcy5qZ1RhYmxlLmN1cnJlbnRQYWdlID0gMTsKICAgICAgICB0aGlzLmpnUXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgICAgdGhpcy5qZ1F1ZXJ5UGFyYW1zLmpnZGwgPSAiIjsgLy/muIXnqbrpl7TpmpTlpKfnsbsKICAgICAgICB0aGlzLmdldEZnc0J5QmR6SWQoKTsKICAgICAgICB0aGlzLmdldEpnRGF0YSh7IHNzYmR6OiBkYXRhLmlkIH0pOwogICAgICAgIHRoaXMuamdkYXRhU2hvdyA9IHRydWU7CiAgICAgICAgdGhpcy56bnNiZGF0YVNob3cgPSB0aGlzLmJkemRhdGFTaG93ID0gZmFsc2U7CiAgICAgIH0gLy/kuoznuqfnm67lvZXlj5jnlLXnq5nlkI3np7AKICAgICAgZWxzZSBpZiAoZGF0YS5pZGVudGlmaWVyID09ICIyIikgewogICAgICAgIC8v54K55Ye76Ze06ZqU5aSn57G777yM6L+H5ruk6Ze06ZqU5pWw5o2uCiAgICAgICAgLy/ph43mlrDorr7nva7miYDlsZ7nlLXnq5kKICAgICAgICB0aGlzLmN1cnJlbnRCZHogPSBkYXRhLnNzYmR6OwogICAgICAgIHRoaXMuc3NiZHogPSBkYXRhLnNzYmR6OwogICAgICAgIC8v6YeN572u6aG156CBCiAgICAgICAgdGhpcy4kcmVmcy5qZ1RhYmxlLmN1cnJlbnRQYWdlID0gMTsKICAgICAgICB0aGlzLmpnUXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgICAgdGhpcy5qZ1F1ZXJ5UGFyYW1zLmpnZGwgPSBkYXRhLmlkOwogICAgICAgIHRoaXMuamdseGN4ID0gZGF0YS5pZDsKICAgICAgICB0aGlzLmdldEZnc0J5QmR6SWQoKTsKICAgICAgICB0aGlzLmdldEpnRGF0YSh7IHNzYmR6OiB0aGlzLnNzYmR6LCBqZ2RsOiBkYXRhLmlkIH0pOwogICAgICAgIHRoaXMuamdkYXRhU2hvdyA9IHRydWU7CiAgICAgICAgdGhpcy56bnNiZGF0YVNob3cgPSB0aGlzLmJkemRhdGFTaG93ID0gZmFsc2U7CiAgICAgIH0gZWxzZSBpZiAoZGF0YS5pZGVudGlmaWVyID09ICIzIikgewogICAgICAgIC8v54K55Ye76Ze06ZqU77yM6I635Y+W56uZ5YaF6K6+5aSHCiAgICAgICAgdGhpcy5qZ2x4Y3ggPSBkYXRhLmpnbHg7CiAgICAgICAgdGhpcy5zYnh4Rm9ybS5zc2JkeiA9IHRoaXMuc3NiZHo7CiAgICAgICAgLy/ph43nva7pobXnoIEKICAgICAgICB0aGlzLiRyZWZzLnpuc2JUYWJsZS5jdXJyZW50UGFnZSA9IDE7CiAgICAgICAgdGhpcy56bnNiUGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICAgIHRoaXMuYmR6T3B0aW9uc0NoYW5nZUNsaWNrKCk7CiAgICAgICAgdGhpcy5zc2pnID0gZGF0YS5pZDsKICAgICAgICB0aGlzLnNieHhGb3JtLnNzamcgPSB0aGlzLnNzamc7CiAgICAgICAgLy/pl7TpmpQKICAgICAgICB0aGlzLmJkenF1ZXJ5UGFyYW1zLnNzYmR6ID0gIiI7CiAgICAgICAgdGhpcy5iZHpxdWVyeVBhcmFtcy5zc2pnID0gZGF0YS5pZDsKICAgICAgICB0aGlzLmdldFpuc2JEYXRhKCk7CiAgICAgICAgdGhpcy56bnNiZGF0YVNob3cgPSB0cnVlOwogICAgICAgIHRoaXMuamdkYXRhU2hvdyA9IHRoaXMuYmR6ZGF0YVNob3cgPSBmYWxzZTsKICAgICAgfQogICAgfSwKICAgIGFzeW5jIGdldEZnc0J5QmR6SWQoKSB7CiAgICAgIGxldCB7IGRhdGEsIGNvZGUgfSA9IGF3YWl0IGdldEZnc0J5QmR6SWQoeyBzYmRtOiB0aGlzLmN1cnJlbnRCZHogfSk7CiAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICB0aGlzLnNzZ3MgPSBkYXRhLnZhbHVlOwogICAgICAgIHRoaXMuc2J4eEZvcm0uc3NncyA9IGRhdGEudmFsdWU7CiAgICAgIH0KICAgIH0sCiAgICAvL+ivt+axguWPmOeUteermeaVsOaNrgogICAgYXN5bmMgZ2V0YmR6RGF0YShwYXJhbSkgewogICAgICB0aGlzLmJkenF1ZXJ5UGFyYW1zID0geyAuLi50aGlzLmJkenF1ZXJ5UGFyYW1zLCAuLi5wYXJhbSB9OwogICAgICBjb25zdCBwYXIgPSB7IC4uLnRoaXMuYmR6cXVlcnlQYXJhbXMsIC4uLnBhcmFtIH07CiAgICAgIGlmICh0aGlzLnRyZWVGb3JtLnNzZHdibSkgewogICAgICAgIHBhci5zc2R3Ym0gPSB0aGlzLnRyZWVGb3JtLnNzZHdibTsKICAgICAgfQogICAgICBhd2FpdCBnZXRCZHpMaXN0KHBhcikudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuYmR6ZGF0YVNob3cgPSB0cnVlOwogICAgICAgIHRoaXMuamdkYXRhU2hvdyA9IHRoaXMuem5zYmRhdGFTaG93ID0gZmFsc2U7CiAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvMS50YWJsZURhdGEgPSByZXMuZGF0YS5yZWNvcmRzOwogICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mbzEucGFnZXIudG90YWwgPSByZXMuZGF0YS50b3RhbDsKICAgICAgfSk7CiAgICB9LAogICAgLy/ml6XmnJ/moLzlvI/ljJYgIHl5eXktTU0tZGQKICAgIGRhdGVGb3JtYXR0ZXIoZCkgewogICAgICBsZXQgeWVhciA9IGQuZ2V0RnVsbFllYXIoKTsKICAgICAgbGV0IG1vbnRoID0KICAgICAgICBkLmdldE1vbnRoKCkgPCA5ID8gIjAiICsgKGQuZ2V0TW9udGgoKSArIDEpIDogIiIgKyAoZC5nZXRNb250aCgpICsgMSk7CiAgICAgIGxldCBkYXkgPSBkLmdldERhdGUoKSA8IDEwID8gIjAiICsgZC5nZXREYXRlKCkgOiAiIiArIGQuZ2V0RGF0ZSgpOwogICAgICByZXR1cm4geWVhciArICItIiArIG1vbnRoICsgIi0iICsgZGF5OwogICAgfSwKICAgIC8v6K+35rGC6Ze06ZqU5pWw5o2uCiAgICBhc3luYyBnZXRKZ0RhdGEocGFyYW0pIHsKICAgICAgdGhpcy5qZ1F1ZXJ5UGFyYW1zID0geyAuLi50aGlzLmpnUXVlcnlQYXJhbXMsIC4uLnBhcmFtIH07CiAgICAgIHRyeSB7CiAgICAgICAgbGV0IHsgZGF0YSwgY29kZSB9ID0gYXdhaXQgZ2V0SmdJbmZvTGlzdCh0aGlzLmpnUXVlcnlQYXJhbXMpOwogICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIHRoaXMuamdkYXRhU2hvdyA9IHRydWU7CiAgICAgICAgICB0aGlzLmJkemRhdGFTaG93ID0gdGhpcy56bnNiZGF0YVNob3cgPSBmYWxzZTsKICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mbzIudGFibGVEYXRhID0gZGF0YS5yZWNvcmRzOwogICAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvMi5wYWdlci50b3RhbCA9IGRhdGEudG90YWw7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlKSB7fQogICAgfSwKICAgIC8v6K+35rGC56uZ5YaF6K6+5aSH5pWw5o2uCiAgICBhc3luYyBnZXRabnNiRGF0YShwYXJhbXMpIHsKICAgICAgdHJ5IHsKICAgICAgICB0aGlzLnpuc2JQYXJhbXMgPSB7IC4uLnRoaXMuem5zYlBhcmFtcywgLi4ucGFyYW1zIH07CiAgICAgICAgY29uc3QgcGFyYW0gPSB0aGlzLnpuc2JQYXJhbXM7CiAgICAgICAgcGFyYW0uc3NqZyA9IHRoaXMuc3NqZzsKICAgICAgICBjb25zdCB7IGRhdGEsIGNvZGUgfSA9IGF3YWl0IGdldEJkQXNlc2V0TGlzdFBhZ2UocGFyYW0pOwogICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIHRoaXMuem5zYmRhdGFTaG93ID0gdHJ1ZTsKICAgICAgICAgIHRoaXMuamdkYXRhU2hvdyA9IHRoaXMuYmR6ZGF0YVNob3cgPSBmYWxzZTsKICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mbzMudGFibGVEYXRhID0gZGF0YS5yZWNvcmRzOwogICAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvMy5wYWdlci50b3RhbCA9IGRhdGEudG90YWw7CiAgICAgICAgICAvLyB0aGlzLnNieHhGb3JtLnNzZ3M9ZGF0YS5yZWNvcmRzWzBdLmRlcHRuYW1lCiAgICAgICAgICAvLyB0aGlzLnNieHhGb3JtLnNzYmR6PWRhdGEucmVjb3Jkc1swXS5iZHptYwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgIGNvbnNvbGUubG9nKGUpOwogICAgICB9CiAgICB9LAogICAgLy/lj5jnlLXnq5nlvLnmoYblvIDlp4sKICAgIC8v5Y+Y55S156uZ5L+u5pS55oyJ6ZKuCiAgICB1cGRhdGViZHoocm93KSB7CiAgICAgIHRoaXMuJG5leHRUaWNrKGFzeW5jIGZ1bmN0aW9uKCkgewogICAgICAgIHRoaXMuY2xlYXJVcGxvYWQoKTsKICAgICAgICB0aGlzLmpieHhGb3JtID0geyAuLi5yb3cgfTsKICAgICAgICB0aGlzLmpieHhGb3JtLmF0dGFjaG1lbnQgPSBbXTsKICAgICAgICBhd2FpdCB0aGlzLmdldEZpbGVMaXN0KCk7CiAgICAgICAgdGhpcy5pc0Rpc2FibGVkID0gZmFsc2U7CiAgICAgICAgdGhpcy50aXRsZSA9ICLlj5jnlLXnq5nlj7DotKbkv67mlLkiOwogICAgICAgIHRoaXMuYmR6RGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgICB9KTsKICAgIH0sCiAgICBjbGVhclVwbG9hZCgpIHsKICAgICAgaWYgKHRoaXMuJHJlZnMudXBsb2FkKSB7CiAgICAgICAgdGhpcy4kcmVmcy51cGxvYWQuY2xlYXJGaWxlcygpOwogICAgICB9CiAgICB9LAogICAgLy/lhbPpl63lvLnmoYYKICAgIHJlbW92ZUZvcm0oKSB7CiAgICAgIHRoaXMuamJ4eEZvcm0gPSB7CiAgICAgICAgYXR0YWNobWVudDogW10KICAgICAgfTsKICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24oKSB7CiAgICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLmNsZWFyVmFsaWRhdGUoKTsKICAgICAgfSk7CiAgICAgIHRoaXMuYmR6RGlhbG9nRm9ybVZpc2libGUgPSBmYWxzZTsKICAgIH0sCiAgICBiZWZvcmVVcGxvYWQoZmlsZSkgewogICAgICBjb25zdCBmaWxlU2l6ZSA9IGZpbGUuc2l6ZSA8IDEwMjQgKiAxMDI0ICogNTA7CiAgICAgIGlmICghZmlsZVNpemUpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLkuIrkvKDmlofku7blpKflsI/kuI3og73otoXov4cgNTBNQiEiKTsKICAgICAgfQogICAgfSwKICAgIGFzeW5jIGdldEZpbGVMaXN0KCkgewogICAgICBsZXQgeyBjb2RlLCBkYXRhIH0gPSBhd2FpdCBnZXRMaXN0QnlCdXNpbmVzc0lkKHsKICAgICAgICBidXNpbmVzc0lkOiB0aGlzLmpieHhGb3JtLm9iaklkCiAgICAgIH0pOwogICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgdGhpcy5qYnh4Rm9ybS5hdHRhY2htZW50ID0gZGF0YTsKICAgICAgICB0aGlzLmltZ0xpc3QgPSBkYXRhLm1hcChpdGVtID0+IHsKICAgICAgICAgIGxldCBpdGVtMSA9IHt9OwogICAgICAgICAgaXRlbTEubmFtZSA9IGl0ZW0uZmlsZU5hbWU7CiAgICAgICAgICBpdGVtMS51cmwgPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLmN1cnJIb3N0ICsgaXRlbS5maWxlVXJsOwogICAgICAgICAgcmV0dXJuIGl0ZW0xOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgLy/lj5jnlLXnq5nor6bmg4Xmlrnms5UKICAgIGJkekRldGFpbHMocm93KSB7CiAgICAgIHRoaXMuJG5leHRUaWNrKGFzeW5jIGZ1bmN0aW9uKCkgewogICAgICAgIHRoaXMuY2xlYXJVcGxvYWQoKTsKICAgICAgICB0aGlzLmpieHhGb3JtID0geyAuLi5yb3cgfTsKICAgICAgICBsZXQgdGVtcCA9IHRoaXMuamJ4eEZvcm0uc3Nkd21jOwogICAgICAgIHRoaXMuamJ4eEZvcm0uc3Nkd21jID0gdGhpcy5qYnh4Rm9ybS5zc2R3Ym07CiAgICAgICAgdGhpcy5qYnh4Rm9ybS5zc2R3Ym0gPSB0ZW1wOwogICAgICAgIHRoaXMuamJ4eEZvcm0uYXR0YWNobWVudCA9IFtdOwogICAgICAgIGF3YWl0IHRoaXMuZ2V0RmlsZUxpc3QoKTsKICAgICAgICB0aGlzLmJkekRpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZTsKICAgICAgICB0aGlzLmlzRGlzYWJsZWQgPSB0cnVlOwogICAgICAgIHRoaXMudGl0bGUgPSAi5Y+Y55S156uZ5Y+w6LSm6K+m5oOFIjsKICAgICAgfSk7CiAgICB9LAogICAgYWRkQmR6KCkgewogICAgICBsZXQgcGFyYW1zID0gewogICAgICAgIGx4OiAi5Y+Y55S16K6+5aSHIiwKICAgICAgICBzc2R3OiB0aGlzLmpieHhGb3JtLnNzZHdibSwKICAgICAgICBtYzogdGhpcy5qYnh4Rm9ybS5iZHptYwogICAgICB9OwogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgYWRkQmR6KHRoaXMuamJ4eEZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICAvL+aWsOWinuaIkOWKn+WQjuWPkemAgemAmuefpQogICAgICAgICAgICAgIGFkZGR3enlmc3R6KHBhcmFtcykudGhlbihyZXMgPT4gewogICAgICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICB0aGlzLnVwbG9hZERhdGEuYnVzaW5lc3NJZCA9IHJlcy5kYXRhLm9iaklkOwogICAgICAgICAgICAgIHRoaXMuc3VibWl0VXBsb2FkKCk7CiAgICAgICAgICAgICAgdGhpcy5iZHpEaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5pON5L2c5oiQ5YqfLOmAmuefpeW3suWIhuWPkSIpOwogICAgICAgICAgICAgIC8vIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci5wYWdlUmVzaXplID0gIlkiOwogICAgICAgICAgICAgIHRoaXMuZ2V0TmV3VHJlZUluZm8oKTsKICAgICAgICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGlzLmJkekRpYWxvZ0Zvcm1WaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCgogICAgc3VibWl0VXBsb2FkKCkgewogICAgICB0aGlzLiRyZWZzLnVwbG9hZC5zdWJtaXQoKTsKICAgIH0sCiAgICAvL+WPmOeUteermeW8ueahhue7k+adnwogICAgLy/mlrDlop7mjInpkq4KICAgIEFkZEJ1dHRvbigpIHsKICAgICAgdGhpcy5zaG93RnNzcyA9IGZhbHNlOwogICAgICBpZiAodGhpcy5iZHpkYXRhU2hvdykgewogICAgICAgIHRoaXMuY2xlYXJVcGxvYWQoKTsKICAgICAgICB0aGlzLmltZ0xpc3QgPSBbXTsKICAgICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZTsKICAgICAgICB0aGlzLmJkekRpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZTsKICAgICAgICB0aGlzLnRpdGxlID0gIuWPmOeUteermeWPsOi0puaWsOWiniI7CiAgICAgIH0KICAgICAgaWYgKHRoaXMuamdkYXRhU2hvdykgewogICAgICAgIHRoaXMuamd4eEZvcm0gPSB7fTsKICAgICAgICB0aGlzLmZpbGxCZHooKTsgLy/orr7nva7pl7TpmpTmiYDlsZ7lj5jnlLXnq5kKICAgICAgICB0aGlzLmpnU2hvdyA9IGZhbHNlOwogICAgICAgIHRoaXMuamdEaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CiAgICAgIH0KICAgICAgaWYgKHRoaXMuem5zYmRhdGFTaG93KSB7CiAgICAgICAgdGhpcy5hY3RpdmVUYWJOYW1lID0gInNiRGVzYyI7CiAgICAgICAgLy8gdGhpcy5zYnh4Rm9ybSA9IHt9OwogICAgICAgIGlmICh0aGlzLnNpbmdsZUNsaWNrRGF0YSkgewogICAgICAgICAgdGhpcy5zYnh4Rm9ybSA9IHsgLi4udGhpcy5zaW5nbGVDbGlja0RhdGEgfTsKICAgICAgICAgIHRoaXMuc2J4eEZvcm0ub2JqSWQgPSB1bmRlZmluZWQ7CiAgICAgICAgfQogICAgICAgIHRoaXMuc2J4eEZvcm0uc3NncyA9IHRoaXMuc3NnczsKICAgICAgICB0aGlzLnNieHhGb3JtLnNzYmR6ID0gdGhpcy5zc2JkejsKICAgICAgICB0aGlzLnNieHhGb3JtLnNzamcgPSB0aGlzLnNzamc7CiAgICAgICAgLy/miZPlvIDlvLnlh7rmoYYKICAgICAgICB0aGlzLnpuc2JEaWFsb2dGb3JtID0gdHJ1ZTsKICAgICAgICAvL+aMiemSruWSjOihqOWNleaYr+WQpuWPr+e8lui+keaOp+WItgogICAgICAgIHRoaXMuYXNzZXRJc0Rpc2FibGUgPSBmYWxzZTsKICAgICAgfQogICAgfSwKICAgIGV4cG9ydEV4Y2VsKCkgewogICAgICBsZXQgZmlsZU5hbWUgPSB0aGlzLnNzYmR6bWMgKyAi6K6+5aSH5L+h5oGv6KGoIjsKICAgICAgbGV0IGV4cG9ydFVybCA9ICIvYmRzYi9leHBvcnRFeGNlbC9hc3NldEluZm8iOwogICAgICBleHBvcnRFeGNlbChleHBvcnRVcmwsIHRoaXMuamdRdWVyeVBhcmFtcywgZmlsZU5hbWUpOwogICAgfSwKICAgIGV4cG9ydEV4Y2VsT2ZCZHooKSB7CiAgICAgIGxldCBmaWxlTmFtZSA9ICLlj5jnlLXnq5nkv6Hmga/ooagiOwogICAgICBsZXQgZXhwb3J0VXJsID0gIi9lcXVpcExpc3QvZXhwb3J0RXhjZWxPZkJkeiI7CiAgICAgIGV4cG9ydEV4Y2VsKGV4cG9ydFVybCwgdGhpcy5qZ1F1ZXJ5UGFyYW1zLCBmaWxlTmFtZSk7CiAgICB9LAogICAgaW1wb3J0RXhjZWxPZkJkeigpIHsKICAgICAgdGhpcy5pbXBvcnRFeGNlbFVybCA9ICIvbWFuYWdlci1hcGkvZXF1aXBMaXN0L2ltcG9ydEV4Y2VsT2ZCZHoiOwogICAgICB0aGlzLmZpbGVOYW1lID0gIuWPmOeUteermeS/oeaBr+ihqCI7CiAgICAgIHRoaXMuJHJlZnMuaW1wb3J0RXhjZWwuaW1wb3J0RXhjZWwoKTsKICAgIH0sCiAgICBnZXRVcGxvYWREYXRhKGZpbGVOYW1lKSB7CiAgICAgIHN3aXRjaCAoZmlsZU5hbWUpIHsKICAgICAgICBjYXNlICLlj5jnlLXnq5nkv6Hmga/ooagiOgogICAgICAgICAgdGhpcy5nZXRiZHpEYXRhKCk7CiAgICAgICAgICBicmVhazsKICAgICAgICBjYXNlICLpl7TpmpTkv6Hmga/ooagiOgogICAgICAgICAgdGhpcy5nZXRKZ0RhdGEoKTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgIuiuvuWkh+S/oeaBr+ihqCI6CiAgICAgICAgICB0aGlzLmdldFpuc2JEYXRhKCk7CiAgICAgICAgICBicmVhazsKICAgICAgICBkZWZhdWx0OgogICAgICAgICAgYnJlYWs7CiAgICAgIH0KICAgICAgdGhpcy5nZXROZXdUcmVlSW5mbygpOwogICAgfSwKICAgIGV4cG9ydEV4Y2VsT2ZKZygpIHsKICAgICAgbGV0IGZpbGVOYW1lID0gIumXtOmalOS/oeaBr+ihqCI7CiAgICAgIGxldCBleHBvcnRVcmwgPSAiL2VxdWlwTGlzdC9leHBvcnRFeGNlbE9mSmciOwogICAgICBleHBvcnRFeGNlbChleHBvcnRVcmwsIHRoaXMuamdRdWVyeVBhcmFtcywgZmlsZU5hbWUpOwogICAgfSwKICAgIGltcG9ydEV4Y2VsT2ZKZygpIHsKICAgICAgdGhpcy5pbXBvcnRFeHRyYUluZm8uc3NiZHogPSB0aGlzLmN1cnJlbnRCZHo7CiAgICAgIHRoaXMuaW1wb3J0RXhjZWxVcmwgPSAiL21hbmFnZXItYXBpL2VxdWlwTGlzdC9pbXBvcnRFeGNlbE9mSmciOwogICAgICB0aGlzLmZpbGVOYW1lID0gIumXtOmalOS/oeaBr+ihqCI7CiAgICAgIHRoaXMuJHJlZnMuaW1wb3J0RXhjZWwuaW1wb3J0RXhjZWwoKTsKICAgIH0sCiAgICBleHBvcnRFeGNlbE9mQXNzZXQoKSB7CiAgICAgIGxldCBmaWxlTmFtZSA9ICLorr7lpIfkv6Hmga/ooagiOwogICAgICBsZXQgZXhwb3J0VXJsID0gIi9lcXVpcExpc3QvZXhwb3J0RXhjZWxPZkFzc2V0IjsKICAgICAgbGV0IHBhcmFtID0ge307CiAgICAgIHBhcmFtLnNzamcgPSB0aGlzLnNzamc7CiAgICAgIGV4cG9ydEV4Y2VsKGV4cG9ydFVybCwgcGFyYW0sIGZpbGVOYW1lKTsKICAgIH0sCiAgICBpbXBvcnRFeGNlbE9mQXNzZXQoKSB7CiAgICAgIHRoaXMuaW1wb3J0RXh0cmFJbmZvLnNzZ3MgPSB0aGlzLnNzZ3M7CiAgICAgIHRoaXMuaW1wb3J0RXh0cmFJbmZvLnNzYmR6ID0gdGhpcy5zc2JkejsKICAgICAgdGhpcy5pbXBvcnRFeHRyYUluZm8uc3NqZyA9IHRoaXMuc3NqZzsKICAgICAgdGhpcy5pbXBvcnRFeGNlbFVybCA9ICIvbWFuYWdlci1hcGkvZXF1aXBMaXN0L2ltcG9ydEV4Y2VsT2ZBc3NldCI7CiAgICAgIHRoaXMuZmlsZU5hbWUgPSAi6K6+5aSH5L+h5oGv6KGoIjsKICAgICAgdGhpcy4kcmVmcy5pbXBvcnRFeGNlbC5pbXBvcnRFeGNlbCgpOwogICAgfSwKICAgIGNvcHlGb3JBc3NldCgpIHsKICAgICAgdGhpcy4kcmVmc1siZm9ybUNvcHkiXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB0aGlzLmZvcm1Db3B5LnNvdXJjZVNzamcgPSB0aGlzLnNzamc7CiAgICAgICAgICBjb3B5QXNzZXQodGhpcy5mb3JtQ29weSkudGhlbihyZXMgPT4gewogICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5pON5L2c5oiQ5YqfIik7CiAgICAgICAgICAgICAgdGhpcy5pc1Nob3dDb3B5ID0gZmFsc2U7CiAgICAgICAgICAgICAgdGhpcy4kcmVmcy5mb3JtQ29weS5yZXNldEZpZWxkcygpOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy/moLnmja7orr7lpIfnsbvlnovojrflj5borr7lpIflnovlj7dsaXN0IOaVsOaNrgogICAgZ2V0U2J4aEJ5U2JseChzYmx4KSB7CiAgICAgIHRoaXMuZ2d4aExpc3QgPSBbXTsKICAgICAgZ2V0U2J4aExpc3QoeyBkeXNibHg6IHNibHggfSkudGhlbihyZXMgPT4gewogICAgICAgIGlmIChyZXMuY29kZSA9PSAiMDAwMCIpIHsKICAgICAgICAgIC8vIOS/neivgeavj+mhuemDveWMheWQq2xhYmVs5a2X5q61CiAgICAgICAgICB0aGlzLmdneGhMaXN0ID0gKHJlcy5kYXRhIHx8IFtdKS5tYXAoaXRlbSA9PiB7CiAgICAgICAgICAgIHJldHVybiB7CiAgICAgICAgICAgICAgLi4uaXRlbSwKICAgICAgICAgICAgICBsYWJlbDogaXRlbS5sYWJlbCB8fCBpdGVtLmxlYmVsIHx8IGl0ZW0ueGggfHwgIiIKICAgICAgICAgICAgfTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAoKICAgIC8q56uZ5YaF6K6+5aSH5byA5aeLKi8KICAgIC8v6K6+5aSH5L+u5pS55pON5L2cCiAgICBhc3luYyB1cGRhdGVBc3NldChyb3cpIHsKICAgICAgcm93LnhoID0gcm93LmdneGg7IC8v6KeE5qC85Z6L5Y+36LWL5YC8CiAgICAgIHRoaXMudGVjaG5pY2FsUGFyYW1ldGVycyhyb3cpOwogICAgICB0aGlzLnBhcmFtUXVlcnkuc2JseGJtID0gcm93LmFzc2V0VHlwZUNvZGU7CiAgICAgIGF3YWl0IHRoaXMuZ2V0UGFyYW1ldGVycygpOwogICAgICB0aGlzLnJlc3VtZVF1ZXJ5LmZvcmVpZ25OdW0gPSByb3cuc2JtYzsKICAgICAgdGhpcy5yZXN1bWVRdWVyeS5zYmx4ID0gcm93LnNibHg7CiAgICAgIHRoaXMuYmRzYmlkID0gcm93Lm9iaklkOwogICAgICB0aGlzLnNibWMgPSByb3cuc2JtYzsKICAgICAgdGhpcy5zc2JkeiA9IHJvdy5zc2JkejsKICAgICAgdGhpcy5nZXRSZXN1bUxpc3QoKTsKICAgICAgYXdhaXQgdGhpcy5nZXRTYnhoQnlTYmx4KHJvdy5hc3NldFR5cGVDb2RlKTsgLy/moLnmja7orr7lpIfnsbvlnovojrflj5borr7lpIflnovlj7fkuIvmi4nmoYYKICAgICAgLy/nu5nooajljZXotYvlgLwKICAgICAgdGhpcy5zYnh4Rm9ybSA9IHsgLi4ucm93IH07CiAgICAgIC8v5oyJ6ZKu5ZKM6KGo5Y2V5piv5ZCm5Y+v57yW6L6R5o6n5Yi2CiAgICAgIHRoaXMuYXNzZXRJc0Rpc2FibGUgPSBmYWxzZTsKICAgICAgdGhpcy5zaG93RnNzcyA9IHRydWU7CiAgICAgIC8v5omT5byA6K6+5aSH5by55Ye65qGGCiAgICAgIHRoaXMuem5zYkRpYWxvZ0Zvcm0gPSB0cnVlOwogICAgfSwKICAgIC8v54K55Ye75paw5aKe77yM5L+u5pS577yM6K+m5oOF5pe277yM6YeN5paw6I635Y+W5a+55bqU55qE5oqA5pyv5Y+C5pWw5L+h5oGvCiAgICB0ZWNobmljYWxQYXJhbWV0ZXJzKHJvdykgewogICAgICB0aGlzLmpzY3NGb3JtID0ge307CiAgICAgIHRoaXMucGFyYW1RdWVyeS5zYmx4Ym0gPSByb3cuYXNzZXRUeXBlQ29kZTsKICAgICAgdGhpcy5qc2NzRm9ybS5zYmx4Ym0gPSByb3cuYXNzZXRUeXBlQ29kZTsKICAgICAgdGhpcy5qc2NzRm9ybS5zYmJtID0gcm93Lm9iaklkOwogICAgICB0aGlzLmdldFBhcmFtZXRlcnMoKTsKICAgIH0sCiAgICAvL+iOt+WPluaKgOacr+WPguaVsOWvueeFp+S/oeaBr++8jOWxleekuuWvueW6lOW+l+aKgOacr+WPguaVsGxhYmVs5L+h5oGvCiAgICBhc3luYyBnZXRQYXJhbWV0ZXJzKCkgewogICAgICB0aGlzLmpzY3NMYWJlbExpc3QgPSBbXTsKICAgICAgZ2V0UGFyYW1EYXRhTGlzdCh0aGlzLnBhcmFtUXVlcnkpLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmpzY3NMYWJlbExpc3QgPSByZXMuZGF0YTsKICAgICAgICB0aGlzLmdldFBhcmFtVmFsdWUoKTsKICAgICAgfSk7CiAgICB9LAogICAgLy/ojrflj5bmioDmnK/lj4LmlbDlgLzkv6Hmga8KICAgIGdldFBhcmFtVmFsdWUoKSB7CiAgICAgIGdldFBhcmFtc1ZhbHVlKHRoaXMuanNjc0Zvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICBpZiAocmVzLmRhdGEgIT0gIiIpIHsKICAgICAgICAgIHRoaXMuanNjc0Zvcm0gPSB7IC4uLnJlcy5kYXRhWzBdIH07CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKioKICAgICAqIOiuvuWkh+WxpeWOhgogICAgICovCiAgICBnZXRSZXN1bUxpc3QocGFyKSB7CiAgICAgIGxldCBwYXJhbSA9IHsgLi4ucGFyLCAuLi50aGlzLnJlc3VtZVF1ZXJ5IH07CiAgICAgIGdldFJlc3VtRGF0YUxpc3QocGFyYW0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLnJlc3VtUGFnZUluZm8udGFibGVEYXRhID0gcmVzLmRhdGEucmVjb3JkczsKICAgICAgICB0aGlzLnJlc3VtUGFnZUluZm8ucGFnZXIudG90YWwgPSByZXMuZGF0YS50b3RhbDsKICAgICAgfSk7CiAgICB9LAogICAgLy/lj5jnlLXnq5nkuIvmi4nmoYbkuK3nmoRjaGFuZ2Xkuovku7YKICAgIGJkek9wdGlvbnNDaGFuZ2VDbGljaygpIHsKICAgICAgLy/lvZPlj5HnlJ9jaGFuZ2Xkuovku7bml7blhYjmuIXnqbrkuYvliY3nmoTpl7TpmpTkv6Hmga8KICAgICAgdGhpcy4kc2V0KHRoaXMuc2J4eEZvcm0sICJzc2pnIiwgIiIpOwogICAgICAvL+iwg+eUqOafpeivoumXtOmalOaWueazlQogICAgICB0aGlzLmdldEpnRGF0YUxpc3RTZWxlY3RlZCgpOwogICAgfSwKICAgIC8v6I635Y+W6Ze06ZqU5LiL5ouJ5qGG5pWw5o2uCiAgICBnZXRKZ0RhdGFMaXN0U2VsZWN0ZWQoKSB7CiAgICAgIC8v57uZ6I635Y+W6Ze06ZqU5LiL5ouJ5qGG5p+l6K+i5Y+C5pWw6LWL5YC8CiAgICAgIHRoaXMuc2VsZWN0SmdPcHRpb25zUGFyYW0uc3NiZHogPSB0aGlzLnNieHhGb3JtLnNzYmR6OwogICAgICBnZXRKZ0RhdGFMaXN0U2VsZWN0ZWQodGhpcy5zZWxlY3RKZ09wdGlvbnNQYXJhbSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuamdPcHRpb25zRGF0YUxpc3QgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLy/orr7lpIfnsbvlnotjaGFuZ2Xkuovku7bjgILojrflj5bmioDmnK/lj4LmlbDkv6Hmga8KICAgIGFzeW5jIHNob3dQYXJhbXMoZGF0YSkgewogICAgICB0aGlzLnBhcmFtUXVlcnkuc2JseGJtID0gZGF0YTsKICAgICAgYXdhaXQgdGhpcy5nZXRQYXJhbWV0ZXJzKCk7CiAgICAgIGF3YWl0IHRoaXMuZ2V0U2J4aEJ5U2JseChkYXRhKTsgLy/moLnmja7orr7lpIfnsbvlnovojrflj5borr7lpIflnovlj7fkuIvmi4nmoYYKICAgIH0sCiAgICAvL+iuvuWkh+WxpeWOhnRhYumhteeCueWHu+S6i+S7tgogICAgYXN5bmMgaGFuZGxlU2JsbERlc2NUYWJOYW1lQ2xpY2sodGFiLCBldmVudCkgewogICAgICBzd2l0Y2ggKHRhYi5uYW1lKSB7CiAgICAgICAgY2FzZSAienRiZ2psIjoKICAgICAgICAgIGF3YWl0IHRoaXMuZ2V0UmVzdW1MaXN0KCk7CiAgICAgICAgICBicmVhazsKICAgICAgICBjYXNlICJ5aCI6CiAgICAgICAgICBhd2FpdCB0aGlzLmdldFloTGlzdCgpOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSAic3kiOgogICAgICAgICAgYXdhaXQgdGhpcy5nZXRTeUxpc3QoKTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgImJsZ2siOgogICAgICAgICAgYXdhaXQgdGhpcy5nZXRCbGdrTGlzdCgpOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSAicXRzYnd0IjoKICAgICAgICAgIGF3YWl0IHRoaXMuZ2V0cXRzYnd0TGlzdCgpOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSAiangiOgogICAgICAgICAgYXdhaXQgdGhpcy5nZXRKeExpc3QoKTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgImpkYmgiOgogICAgICAgICAgYXdhaXQgdGhpcy5nZXRKZGJoTGlzdCgpOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSAidHpqbCI6CiAgICAgICAgICBhd2FpdCB0aGlzLmdldFR6amxMaXN0KCk7CiAgICAgICAgICBicmVhazsKICAgICAgfQogICAgfSwKICAgIC8v5L+d5a2Y6K6+5aSH5L+h5oGvCiAgICBzdWJtaXQoKSB7CiAgICAgIHRoaXMuYXNzZXRTdWJtaXRMb2FkaW5nID0gdHJ1ZTsKICAgICAgdGhpcy4kcmVmc1sic2J4eEZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB0aGlzLmFkZEFzc2V0KCk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuYXNzZXRTdWJtaXRMb2FkaW5nID0gZmFsc2U7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLmoKHpqozmnKrpgJrov4fvvIEiKTsKICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8v56uZ5YaF6K6+5aSH5by55qGG5YWz6ZetCiAgICAvL+a4heepuuihqOWNlQogICAgcmVzZXRGb3JtMSgpIHsKICAgICAgdGhpcy5zYnh4Rm9ybSA9IHRoaXMuJG9wdGlvbnMuZGF0YSgpLmZvcm07CiAgICAgIHRoaXMuanNjc0Zvcm0gPSB0aGlzLiRvcHRpb25zLmRhdGEoKS5mb3JtOwogICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbigpIHsKICAgICAgICB0aGlzLiRyZWZzWyJzYnh4Rm9ybSJdLmNsZWFyVmFsaWRhdGUoKTsKICAgICAgfSk7CiAgICAgIHRoaXMuc2JsbERlc2NUYWJOYW1lID0gInp0YmdqbCI7CiAgICAgIHRoaXMuem5zYkRpYWxvZ0Zvcm0gPSBmYWxzZTsKICAgIH0sCiAgICAvKioKICAgICAqIOa3u+WKoOiuvuWkh+S/neWtmOWfuuacrOS/oeaBrwogICAgICovCiAgICBhZGRBc3NldCgpIHsKICAgICAgdGhpcy5zYnh4Rm9ybS5zYkNsYXNzQ3NWYWx1ZSA9IHRoaXMuanNjc0Zvcm07CiAgICAgIGFkZEFzc2V0KHRoaXMuc2J4eEZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICBpZiAocmVzLmNvZGUgPT0gIjAwMDAiKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLAogICAgICAgICAgICBtZXNzYWdlOiAi5pON5L2c5oiQ5YqfISIKICAgICAgICAgIH0pOwogICAgICAgICAgdGhpcy56bnNiRGlhbG9nRm9ybSA9IGZhbHNlOwogICAgICAgICAgLy8gdGhpcy50YWJsZUFuZFBhZ2VJbmZvMy5wYWdlci5wYWdlUmVzaXplID0gIlkiOwogICAgICAgICAgdGhpcy5nZXRabnNiRGF0YSgpOwogICAgICAgIH0KICAgICAgICB0aGlzLmFzc2V0U3VibWl0TG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvL+iuvuWkh+ivpuaDheaTjeS9nAogICAgYXN5bmMgYXNzZXREZXRhaWxzKHJvdykgewogICAgICByb3cueGggPSByb3cuZ2d4aDsgLy/op4TmoLzlnovlj7fotYvlgLwKICAgICAgdGhpcy50ZWNobmljYWxQYXJhbWV0ZXJzKHJvdyk7CiAgICAgIHRoaXMucmVzdW1lUXVlcnkuZm9yZWlnbk51bSA9IHJvdy5zYm1jOwogICAgICB0aGlzLnJlc3VtZVF1ZXJ5LnNibHggPSByb3cuc2JseDsKICAgICAgdGhpcy5iZHNiaWQgPSByb3cub2JqSWQ7CiAgICAgIHRoaXMuc2JtYyA9IHJvdy5zYm1jOwogICAgICB0aGlzLnNzYmR6ID0gcm93LnNzYmR6OwogICAgICB0aGlzLmdldFJlc3VtTGlzdCgpOwogICAgICBhd2FpdCB0aGlzLmdldFNieGhCeVNibHgocm93LmFzc2V0VHlwZUNvZGUpOyAvL+agueaNruiuvuWkh+exu+Wei+iOt+WPluiuvuWkh+Wei+WPt+S4i+aLieahhgogICAgICAvL+e7meihqOWNlei1i+WAvAogICAgICB0aGlzLnNieHhGb3JtID0geyAuLi5yb3cgfTsKICAgICAgdGhpcy5qZ09wdGlvbnNEYXRhTGlzdCA9IFsKICAgICAgICB7IGxhYmVsOiB0aGlzLnNieHhGb3JtLnd6bWMsIHZhbHVlOiB0aGlzLnNieHhGb3JtLnNzamcgfQogICAgICBdOwogICAgICAvL+aMiemSruWSjOihqOWNleaYr+WQpuWPr+e8lui+keaOp+WItgogICAgICB0aGlzLmFzc2V0SXNEaXNhYmxlID0gdHJ1ZTsKICAgICAgdGhpcy5zaG93RnNzcyA9IHRydWU7CiAgICAgIC8v5omT5byA6K6+5aSH5by55Ye65qGGCiAgICAgIHRoaXMuem5zYkRpYWxvZ0Zvcm0gPSB0cnVlOwogICAgfSwKICAgIC8v6I635Y+W5Y+Y55S156uZ5LiL5ouJ5qGG5pWw5o2uCiAgICBnZXRCZHpEYXRhTGlzdFNlbGVjdGVkKCkgewogICAgICBnZXRCZHpEYXRhTGlzdFNlbGVjdGVkKHt9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5iZHpPcHRpb25zRGF0YUxpc3QgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgcmVzZXRGb3JtKCkgewogICAgICB0aGlzLmpnRGlhbG9nRm9ybVZpc2libGUgPSBmYWxzZTsKICAgICAgdGhpcy4kcmVmcy5qZ3h4Rm9ybS5yZXNldEZpZWxkcygpOwogICAgfSwKICAgIC8v562b6YCJ5p2h5Lu26YeN572uCiAgICBmaWx0ZXJSZXNldCgpIHsKICAgICAgLy8g6YeN572u5Y+Y55S156uZ5p+l6K+i5Y+C5pWw77yI5L+d55WZ5qCR5b2i6YCJ5oup55qE5Z+65pys5Y+C5pWw77yJCiAgICAgIGlmICh0aGlzLmJkemRhdGFTaG93KSB7CiAgICAgICAgdGhpcy5iZHpxdWVyeVBhcmFtcyA9IHsKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICBwYWdlU2l6ZTogMTAKICAgICAgICB9OwogICAgICB9CgogICAgICAvLyDph43nva7pl7TpmpTmn6Xor6Llj4LmlbDvvIjkv53nlZnmiYDlsZ7lj5jnlLXnq5nlkozpl7TpmpTlpKfnsbvvvIkKICAgICAgaWYgKHRoaXMuamdkYXRhU2hvdykgewogICAgICAgIGNvbnN0IHNzYmR6ID0gdGhpcy5qZ1F1ZXJ5UGFyYW1zLnNzYmR6OwogICAgICAgIGNvbnN0IGpnZGwgPSB0aGlzLmpnUXVlcnlQYXJhbXMuamdkbDsKICAgICAgICB0aGlzLmpnUXVlcnlQYXJhbXMgPSB7CiAgICAgICAgICBzc2Jkejogc3NiZHosCiAgICAgICAgICBqZ2RsOiBqZ2RsLAogICAgICAgICAgZHlkajogdW5kZWZpbmVkLAogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMQogICAgICAgIH07CiAgICAgIH0KCiAgICAgIC8vIOmHjee9ruermeWGheiuvuWkh+afpeivouWPguaVsO+8iOS/neeVmeaJgOWxnumXtOmalO+8iQogICAgICBpZiAodGhpcy56bnNiZGF0YVNob3cpIHsKICAgICAgICB0aGlzLnpuc2JQYXJhbXMgPSB7CiAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICBwYWdlTnVtOiAxCiAgICAgICAgfTsKICAgICAgICAvLyDkv53nlZnpl7TpmpTnm7jlhbPnmoTmn6Xor6LmnaHku7YKICAgICAgICB0aGlzLmJkenF1ZXJ5UGFyYW1zID0gewogICAgICAgICAgc3NiZHo6ICIiLAogICAgICAgICAgc3NqZzogdGhpcy5zc2pnLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHBhZ2VTaXplOiAxMAogICAgICAgIH07CiAgICAgIH0KCiAgICAgIC8vIOmHjee9ruetm+mAieadoeS7tueahOaVsOaNruWSjOWkjemAieahhgogICAgICB0aGlzLmZpbHRlckluZm8xLmRhdGEgPSB7CiAgICAgICAgeXdkd0FycjogW10sCiAgICAgICAgamhueUFycjogW10sCiAgICAgICAgeGxBcnI6ICIiLAogICAgICAgIGpobHhBcnI6IFtdLAogICAgICAgIGpoenRBcnI6ICIiLAogICAgICAgIHNmZGQ6ICIiCiAgICAgIH07CiAgICAgIHRoaXMuZmlsdGVySW5mbzEuZmllbGRMaXN0LmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgaWYgKGl0ZW0udHlwZSA9PT0gImNoZWNrYm94IikgewogICAgICAgICAgaXRlbS5jaGVja2JveFZhbHVlID0gW107CiAgICAgICAgfQogICAgICB9KTsKCiAgICAgIHRoaXMuZmlsdGVySW5mbzIuZGF0YSA9IHsKICAgICAgICB5d2R3QXJyOiBbXSwKICAgICAgICBqaG55QXJyOiBbXSwKICAgICAgICB4bEFycjogIiIsCiAgICAgICAgamhseEFycjogW10sCiAgICAgICAgamh6dEFycjogIiIsCiAgICAgICAgc2ZkZDogIiIKICAgICAgfTsKICAgICAgdGhpcy5maWx0ZXJJbmZvMi5maWVsZExpc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICBpZiAoaXRlbS50eXBlID09PSAiY2hlY2tib3giKSB7CiAgICAgICAgICBpdGVtLmNoZWNrYm94VmFsdWUgPSBbXTsKICAgICAgICB9CiAgICAgIH0pOwoKICAgICAgdGhpcy5maWx0ZXJJbmZvMy5kYXRhID0gewogICAgICAgIHl3ZHdBcnI6IFtdLAogICAgICAgIGpobnlBcnI6IFtdLAogICAgICAgIHhsQXJyOiAiIiwKICAgICAgICBqaGx4QXJyOiBbXSwKICAgICAgICBqaHp0QXJyOiAiIiwKICAgICAgICBzZmRkOiAiIgogICAgICB9OwogICAgICB0aGlzLmZpbHRlckluZm8zLmZpZWxkTGlzdC5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgIGlmIChpdGVtLnR5cGUgPT09ICJjaGVja2JveCIpIHsKICAgICAgICAgIGl0ZW0uY2hlY2tib3hWYWx1ZSA9IFtdOwogICAgICAgIH0KICAgICAgfSk7CgogICAgICAvLyDph43nva7ooajmoLzpobXnoIEKICAgICAgaWYgKHRoaXMuYmR6ZGF0YVNob3cgJiYgdGhpcy4kcmVmcy5iZHpUYWJsZSkgewogICAgICAgIHRoaXMuJHJlZnMuYmR6VGFibGUuY3VycmVudFBhZ2UgPSAxOwogICAgICB9CiAgICAgIGlmICh0aGlzLmpnZGF0YVNob3cgJiYgdGhpcy4kcmVmcy5qZ1RhYmxlKSB7CiAgICAgICAgdGhpcy4kcmVmcy5qZ1RhYmxlLmN1cnJlbnRQYWdlID0gMTsKICAgICAgfQogICAgICBpZiAodGhpcy56bnNiZGF0YVNob3cgJiYgdGhpcy4kcmVmcy56bnNiVGFibGUpIHsKICAgICAgICB0aGlzLiRyZWZzLnpuc2JUYWJsZS5jdXJyZW50UGFnZSA9IDE7CiAgICAgIH0KCiAgICAgIC8vIOagueaNruW9k+WJjeaYvuekuueahOihqOagvOmHjeaWsOWKoOi9veaVsOaNru+8iOS/neaMgeW9k+WJjeeahOagkeW9oumAieaLqeeKtuaAge+8iQogICAgICBpZiAodGhpcy5iZHpkYXRhU2hvdykgewogICAgICAgIHRoaXMuZ2V0YmR6RGF0YSgpOwogICAgICB9IGVsc2UgaWYgKHRoaXMuamdkYXRhU2hvdykgewogICAgICAgIHRoaXMuZ2V0SmdEYXRhKCk7CiAgICAgIH0gZWxzZSBpZiAodGhpcy56bnNiZGF0YVNob3cpIHsKICAgICAgICB0aGlzLmdldFpuc2JEYXRhKCk7CiAgICAgIH0KICAgIH0sCiAgICBhc3luYyBxdWVyeVNlYXJjaEFzeW5jKHF1ZXJ5U3RyaW5nLCBjYikgewogICAgICBjb25zdCByZXN1bHRzID0gcXVlcnlTdHJpbmcKICAgICAgICA/IHRoaXMuZ2d4aExpc3QuZmlsdGVyKAogICAgICAgICAgICBpdGVtID0+CiAgICAgICAgICAgICAgaXRlbS5sYWJlbC50b0xvd2VyQ2FzZSgpLmluZGV4T2YocXVlcnlTdHJpbmcudG9Mb3dlckNhc2UoKSkgIT09IC0xCiAgICAgICAgICApCiAgICAgICAgOiB0aGlzLmdneGhMaXN0OwogICAgICAvLyDnoa7kv53mr4/kuKrpgInpobnpg73ljIXlkKsgbGFiZWwg5ZKMIHZhbHVlIOWtl+autQogICAgICBjb25zdCBmb3JtYXR0ZWRSZXN1bHRzID0gcmVzdWx0cy5tYXAoaXRlbSA9PiAoewogICAgICAgIGxhYmVsOiBpdGVtLmxhYmVsLAogICAgICAgIHZhbHVlOiBpdGVtLnZhbHVlIHx8IGl0ZW0ubGFiZWwgLy8g5aaC5p6c5rKh5pyJIHZhbHVl77yM5L2/55SoIGxhYmVsIOS9nOS4uiB2YWx1ZQogICAgICB9KSk7CiAgICAgIGNvbnNvbGUubG9nKCJyZXN1bHRzOiIsIGZvcm1hdHRlZFJlc3VsdHMpOwogICAgICBjYihmb3JtYXR0ZWRSZXN1bHRzKTsKICAgIH0sCiAgICBoYW5kbGVTZWxlY3QoaXRlbSkgewogICAgICB0aGlzLmlzU2VsZWN0aW5nRnJvbURyb3Bkb3duID0gdHJ1ZTsgLy8g6K6+572u5qCH6K6wCiAgICAgIHRoaXMuc2J4eEZvcm0ueGggPSBpdGVtLmxhYmVsOwogICAgfSwKICAgIGFzeW5jIGhhbmRsZUNoYW5nZSh2YWx1ZSkgewogICAgICAvLyDlpoLmnpzmmK/ku47kuIvmi4nliJfooajpgInmi6nnmoTvvIzkuI3miafooYzmlrDlop7mk43kvZwKICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgaWYgKHRoaXMuaXNTZWxlY3RpbmdGcm9tRHJvcGRvd24pIHsKICAgICAgICAgIHRoaXMuaXNTZWxlY3RpbmdGcm9tRHJvcGRvd24gPSBmYWxzZTsgLy8g6YeN572u5qCH6K6wCiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQogICAgICAgIC8vIOWmguaenOi+k+WFpeeahOWAvOS4jeWcqOmAiemhueS4re+8jOWImea3u+WKoOWIsOWei+WPt+W6kwogICAgICBpZiAodmFsdWUgJiYgIXRoaXMuZ2d4aExpc3Quc29tZShpdGVtID0+IGl0ZW0ubGFiZWwgPT09IHZhbHVlKSkgewogICAgICAgIHRyeSB7CiAgICAgICAgICBjb25zdCBwYXJhbXMgPSB7CiAgICAgICAgICAgIHNieGg6IHZhbHVlLAogICAgICAgICAgICBkeXNibHg6IHRoaXMuc2J4eEZvcm0uYXNzZXRUeXBlQ29kZSAvLyDku47ooajljZXkuK3ojrflj5borr7lpIfnsbvlnosKICAgICAgICAgIH0KICAgICAgICAgIGFkZFNieGgocGFyYW1zKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gJzAwMDAnKSB7CiAgICAgICAgICAgICAgdGhpcy5nZ3hoTGlzdC5wdXNoKHsKICAgICAgICAgICAgICAgIGxhYmVsOiB2YWx1ZSwKICAgICAgICAgICAgICAgIHZhbHVlOiB2YWx1ZSAvLyDmt7vliqAgdmFsdWUg5a2X5q61CiAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+W3sua3u+WKoOWIsOWei+WPt+W6kycpCiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKHJlcy5tc2cgfHwgJ+a3u+WKoOWei+WPt+Wksei0pScpCiAgICAgICAgICAgIH0KICAgICAgICAgIH0pCiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgIGNvbnNvbGUud2FybmluZygn5re75Yqg5Z6L5Y+35aSx6LSlOicsIGVycm9yKQogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfmt7vliqDlnovlj7flpLHotKUnKQogICAgICAgIH0KICAgICAgfQogICAgICB9LCA1MDApOwogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["jgtz.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAskDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "jgtz.vue", "sourceRoot": "src/views/dagangOilfield/dwzygl/bdgl", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card shadow=\"never\" style=\"background:#e0f8ed;padding-top:10px;\">\n          <div>\n            <el-col>\n              <el-form label-width=\"80px\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"所属公司:\">\n                    <el-select\n                      v-model=\"treeForm.ssdwbm\"\n                      placeholder=\"请选择所属公司\"\n                      @change=\"getNewTreeInfo\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in OrganizationSelectedList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"电压等级:\">\n                    <el-select\n                      v-model=\"treeForm.dydjbm\"\n                      placeholder=\"请选择电压等级\"\n                      @change=\"getNewTreeInfo\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in VoltageLevelSelectedList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"快速查询:\">\n                    <el-input\n                      placeholder=\"输入关键字进行过滤\"\n                      v-model=\"filterText\"\n                      clearable\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-form>\n            </el-col>\n          </div>\n          <div class=\"text head-container\">\n            <el-col style=\"padding:0;\">\n              <el-tree\n                :expand-on-click-node=\"true\"\n                id=\"tree\"\n                :data=\"treeOptions\"\n                @node-click=\"handleNodeClick\"\n                :highlight-current=\"true\"\n                ref=\"tree\"\n                :filter-node-method=\"filterNode\"\n                node-key=\"id\"\n                :default-checked-keys=\"['0']\"\n                :default-expanded-keys=\"['0']\"\n                accordion\n              >\n                <span slot-scope=\"{ node, data }\">\n                  <i :class=\"icons[data.icon]\" />\n                  <span style=\"margin-left:5px;\" :title=\"data.label\">{{\n                    data.label\n                  }}</span>\n                </span>\n              </el-tree>\n              <!--              :default-expanded-keys=\"['0']\"-->\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo1.data\"\n          :field-list=\"filterInfo1.fieldList\"\n          :width=\"{ labelWidth: 100, itemWidth: 170 }\"\n          @handleReset=\"filterReset\"\n          comp-table=\"tableAndPageInfo1\"\n          v-show=\"bdzdataShow\"\n        />\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo2.data\"\n          :field-list=\"filterInfo2.fieldList\"\n          :width=\"{ labelWidth: 100, itemWidth: 150 }\"\n          @handleReset=\"filterReset\"\n          comp-table=\"tableAndPageInfo2\"\n          v-show=\"jgdataShow\"\n        />\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo3.data\"\n          :field-list=\"filterInfo3.fieldList\"\n          :width=\"{ labelWidth: 100, itemWidth: 150 }\"\n          @handleReset=\"filterReset\"\n          comp-table=\"tableAndPageInfo3\"\n          v-show=\"znsbdataShow\"\n        />\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button\n              icon=\"el-icon-plus\"\n              @click=\"AddButton\"\n              v-hasPermi=\"['jgdy:button:add']\"\n              type=\"primary\"\n              >新增\n            </el-button>\n            <el-button\n              v-show=\"jgdataShow\"\n              type=\"success\"\n              icon=\"el-icon-download\"\n              @click=\"exportExcel\"\n              >导出整站设备</el-button\n            >\n            <el-button\n              v-show=\"bdzdataShow\"\n              type=\"success\"\n              icon=\"el-icon-download\"\n              @click=\"exportExcelOfBdz\"\n              >导出变电站</el-button\n            >\n            <el-button\n              v-hasPermi=\"['jgdy:button:add']\"\n              v-show=\"bdzdataShow\"\n              type=\"success\"\n              icon=\"el-icon-upload\"\n              @click=\"importExcelOfBdz\"\n              >导入变电站</el-button\n            >\n            <el-button\n              v-show=\"jgdataShow\"\n              type=\"success\"\n              icon=\"el-icon-download\"\n              @click=\"exportExcelOfJg\"\n              >导出间隔</el-button\n            >\n            <el-button\n              v-hasPermi=\"['jgdy:button:add']\"\n              v-show=\"jgdataShow\"\n              type=\"success\"\n              icon=\"el-icon-upload\"\n              @click=\"importExcelOfJg\"\n              >导入间隔</el-button\n            >\n            <el-button\n              v-hasPermi=\"['jgdy:button:add']\"\n              v-show=\"znsbdataShow\"\n              type=\"primary\"\n              icon=\"el-icon-plus\"\n              @click=\"isShowCopy = true\"\n              >设备复制</el-button\n            >\n            <el-button\n              v-show=\"znsbdataShow\"\n              type=\"success\"\n              icon=\"el-icon-download\"\n              @click=\"exportExcelOfAsset\"\n              >导出设备</el-button\n            >\n            <el-button\n              v-hasPermi=\"['jgdy:button:add']\"\n              v-show=\"znsbdataShow\"\n              type=\"success\"\n              icon=\"el-icon-upload\"\n              @click=\"importExcelOfAsset\"\n              >导入设备</el-button\n            >\n            <!--<el-button icon=\"el-icon-delete\" v-hasPermi=\"['jgdy:button:delete']\" type=\"danger\" @click=\"removeAll\">删除-->\n            <!--</el-button>-->\n          </div>\n          <!--变电站-->\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo1\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"61vh\"\n            v-show=\"bdzdataShow\"\n            @getMethod=\"getbdzData\"\n            v-loading=\"loading\"\n            ref=\"bdzTable\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"updatebdz(scope.row)\"\n                  v-hasPermi=\"['jgdy:button:update']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                >\n                </el-button>\n                <el-button\n                  @click=\"bdzDetails(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n                <el-button\n                  v-hasPermi=\"['jgdy:button:delete']\"\n                  type=\"text\"\n                  v-if=\"scope.row.createBy === $store.getters.name\"\n                  size=\"small\"\n                  @click=\"removeAll(scope.row)\"\n                  title=\"删除\"\n                  class=\"el-icon-delete\"\n                >\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n          <!--间隔-->\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo2\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"70vh\"\n            v-show=\"jgdataShow\"\n            @getMethod=\"getJgData\"\n            ref=\"jgTable\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"updateJg(scope.row)\"\n                  v-hasPermi=\"['jgdy:button:update']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                ></el-button>\n                <el-button\n                  @click=\"jgDetails(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n                <el-button\n                  v-hasPermi=\"['jgdy:button:delete']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"删除\"\n                  class=\"el-icon-delete\"\n                  @click=\"removeAll(scope.row)\"\n                >\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n          <!--设备-->\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo3\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"61vh\"\n            v-show=\"znsbdataShow\"\n            @getMethod=\"getZnsbData\"\n            ref=\"znsbTable\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"updateAsset(scope.row)\"\n                  v-hasPermi=\"['jgdy:button:update']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                ></el-button>\n                <el-button\n                  @click=\"assetDetails(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n                <el-button\n                  v-hasPermi=\"['jgdy:button:delete']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"删除\"\n                  class=\"el-icon-delete\"\n                  @click=\"removeAll(scope.row)\"\n                >\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!--变电站所用弹出框开始-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"bdzDialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"60%\"\n      v-dialogDrag\n      :before-close=\"removeForm\"\n    >\n      <el-form :model=\"jbxxForm\" label-width=\"130px\" :rules=\"rules\" ref=\"form\">\n        <div\n          class=\"block\"\n          style=\"width: 50%;height: 50%;margin-bottom: 2%;float: right\"\n        >\n          <span class=\"demonstration\">变电站图片</span>\n          <el-carousel\n            trigger=\"click\"\n            class=\"imgCls\"\n            indicator-position=\"none\"\n            :interval=\"2000\"\n            type=\"card\"\n            id=\"imgId\"\n          >\n            <el-carousel-item v-for=\"(img, index) in imgList\" :key=\"index\">\n              <viewer :images=\"imgList\" style=\"z-index: 999\">\n                <img :src=\"img.url\" width=\"100%\" height=\"100%\" />\n              </viewer>\n            </el-carousel-item>\n          </el-carousel>\n        </div>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属单位\" prop=\"ssdwbm\">\n              <el-select\n                v-model=\"jbxxForm.ssdwbm\"\n                :placeholder=\"isDisabled ? '' : '所属单位'\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option\n                  v-for=\"item in OrganizationSelectedList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"变电站数字编号\" prop=\"bdzszbh\">\n              <el-input\n                v-model=\"jbxxForm.bdzszbh\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入变电站数字编号'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"变电站名称\" prop=\"bdzmc\">\n              <el-input\n                v-model=\"jbxxForm.bdzmc\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入变电站名称'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属电网\" prop=\"ssdw\">\n              <el-input\n                v-model=\"jbxxForm.ssdw\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入所属电网'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属基地站\" prop=\"ssjdz\">\n              <el-select\n                v-model=\"jbxxForm.ssjdz\"\n                :placeholder=\"isDisabled ? '' : '请选择所属基地站'\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option\n                  v-for=\"item in ssjdzList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电压等级\" prop=\"dydjbm\">\n              <el-select\n                v-model=\"jbxxForm.dydjbm\"\n                :placeholder=\"isDisabled ? '' : '请选择电压等级'\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option\n                  v-for=\"item in VoltageLevelSelectedList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"设备状态\" prop=\"sbzt\">\n              <el-select\n                v-model=\"jbxxForm.sbzt\"\n                :placeholder=\"isDisabled ? '' : '请选择设备状态'\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option\n                  v-for=\"item in sbztOptionsDataList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"投运日期\" class=\"add_sy_tyrq\" prop=\"tyrq\">\n              <el-date-picker\n                v-model=\"jbxxForm.tyrq\"\n                type=\"date\"\n                :placeholder=\"isDisabled ? '' : '选择日期'\"\n                format=\"yyyy-MM-dd\"\n                value-format=\"yyyy-MM-dd\"\n                :picker-options=\"pickerOptions\"\n                :disabled=\"isDisabled\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"电站类型\" prop=\"dzlx\">\n              <el-select v-model=\"jbxxForm.dzlx\" :disabled=\"isDisabled\">\n                <el-option value=\"变电站\" label=\"变电站\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"是否综合自动化站\" prop=\"sfzhzdh\">\n              <el-select v-model=\"jbxxForm.sfzhzdh\" :disabled=\"isDisabled\">\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否数字化变电站\" prop=\"sfszhbdz\">\n              <el-select\n                v-model=\"jbxxForm.sfszhbdz\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请选择'\"\n              >\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否枢纽站\" prop=\"sfsnz\">\n              <el-select\n                v-model=\"jbxxForm.sfsnz\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请选择'\"\n              >\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item\n              label=\"退运日期\"\n              class=\"add_sy_tyrq\"\n              prop=\"returnDate\"\n            >\n              <el-date-picker\n                v-model=\"jbxxForm.returnDate\"\n                type=\"date\"\n                :placeholder=\"isDisabled ? '' : '选择日期'\"\n                format=\"yyyy-MM-dd\"\n                value-format=\"yyyy-MM-dd\"\n                :picker-options=\"pickerOptions\"\n                :disabled=\"isDisabled\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"占地面积\" prop=\"zymj\">\n              <el-input\n                v-model=\"jbxxForm.zymj\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入占地面积'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"污秽等级\" prop=\"whdj\">\n              <el-select v-model=\"jbxxForm.whdj\" :disabled=\"isDisabled\">\n                <el-option value=\"a级\" label=\"a级\"></el-option>\n                <el-option value=\"b级\" label=\"b级\"></el-option>\n                <el-option value=\"c级\" label=\"c级\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"值班方式\" prop=\"zbfs\">\n              <el-select\n                v-model=\"jbxxForm.zbfs\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请选择'\"\n              >\n                <el-option value=\"有人值班\" label=\"有人值班\"></el-option>\n                <el-option value=\"无人值班\" label=\"无人值班\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否光纤通讯\" prop=\"sfgqtx\">\n              <el-select\n                v-model=\"jbxxForm.sfgqtx\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请选择'\"\n              >\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"海拔\" prop=\"hb\">\n              <el-input v-model=\"jbxxForm.hb\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col> -->\n\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"工程编号\" prop=\"gcbh\">\n              <el-input v-model=\"jbxxForm.gcbh\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"8\">\n            <el-form-item label=\"设计单位\" prop=\"sjdw\">\n              <el-input v-model=\"jbxxForm.sjdw\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"8\">\n            <el-form-item label=\"监理单位\" prop=\"jldw\">\n              <el-input v-model=\"jbxxForm.jldw\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"电站重要级别\" prop=\"zyjb\">\n              <el-select\n                v-model=\"jbxxForm.zyjb\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请选择'\"\n              >\n                <el-option value=\"A\" label=\"A\"></el-option>\n                <el-option value=\"B\" label=\"B\"></el-option>\n                <el-option value=\"C\" label=\"C\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"布置方式\" prop=\"bzfs\">\n              <el-select\n                v-model=\"jbxxForm.bzfs\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请选择'\"\n              >\n                <el-option value=\"户内\" label=\"户内\"></el-option>\n                <el-option value=\"户外\" label=\"户外\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"站址\" prop=\"bdzdz\">\n              <el-input\n                v-model=\"jbxxForm.bdzdz\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入站址'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"建筑面积\" prop=\"jzmj\">\n              <el-input\n                v-model=\"jbxxForm.jzmj\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入建筑面积'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"联系电话\" prop=\"phone\">\n              <el-input\n                v-model=\"jbxxForm.phone\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入联系电话'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"工程名称\" prop=\"gcmc\">\n              <el-input v-model=\"jbxxForm.gcmc\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"施工单位\" prop=\"sgdw\">\n              <el-input v-model=\"jbxxForm.sgdw\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"地区特征\" prop=\"dqtz\">\n              <el-select v-model=\"jbxxForm.dqtz\" :disabled=\"isDisabled\">\n                <el-option value=\"户内\" label=\"户内\"></el-option>\n                <el-option value=\"户外\" label=\"户外\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"最高调度管辖权\" prop=\"zgddgxq\">\n              <el-input\n                v-model=\"jbxxForm.zgddgxq\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入最高调度管辖权'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"是否满足N-1\" prop=\"sfmzn\">\n              <el-select v-model=\"jbxxForm.sfmzn\" :disabled=\"isDisabled\">\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>-->\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否接入故障系统\" prop=\"sfjrgzxt\">\n              <el-select\n                v-model=\"jbxxForm.sfjrgzxt\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请选择'\"\n              >\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否接入AVC\" prop=\"sfjravc\">\n              <el-select\n                v-model=\"jbxxForm.sfjravc\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请选择'\"\n              >\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否集中监控\" prop=\"sfjzjk\">\n              <el-select\n                v-model=\"jbxxForm.sfjzjk\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请选择'\"\n              >\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"接入的监控中心\" prop=\"jkzxmc\">\n              <el-input\n                v-model=\"jbxxForm.jkzxmc\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入监控中心'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"经度\" prop=\"jd\">\n              <el-input\n                v-model=\"jbxxForm.jd\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入经度'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"纬度\" prop=\"wd\">\n              <el-input\n                v-model=\"jbxxForm.wd\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入纬度'\"\n              >\n              </el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\" prop=\"bz\">\n              <el-input\n                v-model=\"jbxxForm.bz\"\n                :disabled=\"isDisabled\"\n                type=\"textarea\"\n                rows=\"2\"\n                :placeholder=\"isDisabled ? '' : '请输入备注'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-form-item\n            label=\"已上传图片：\"\n            prop=\"attachment\"\n            v-if=\"jbxxForm.attachment.length > 0\"\n            id=\"pic_form\"\n          >\n            <el-col\n              :span=\"24\"\n              v-for=\"(item, index) in jbxxForm.attachment\"\n              style=\"margin-left: 0\"\n            >\n              <el-form-item :label=\"(index + 1).toString()\">\n                {{ item.fileOldName }}\n                <el-button\n                  v-if=\"!isDisabled\"\n                  type=\"error\"\n                  size=\"mini\"\n                  @click=\"deleteFileById(item.fileId)\"\n                  >删除\n                </el-button>\n              </el-form-item>\n            </el-col>\n          </el-form-item>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-form-item label=\"上传图片：\" v-if=\"!isDisabled\">\n            <el-upload\n              list-type=\"picture-card\"\n              class=\"upload-demo\"\n              accept=\".jpg,.png\"\n              ref=\"upload\"\n              :headers=\"header\"\n              action=\"/isc-api/file/upload\"\n              :before-upload=\"beforeUpload\"\n              :data=\"uploadData\"\n              single\n              :auto-upload=\"false\"\n              multiple\n            >\n              <el-button slot=\"trigger\" type=\"primary\">选取文件</el-button>\n              <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png文件</div>\n            </el-upload>\n          </el-form-item>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"removeForm\">取 消</el-button>\n        <el-button\n          v-if=\"title == '变电站台账修改' || title == '变电站台账新增'\"\n          type=\"primary\"\n          @click=\"addBdz\"\n          class=\"pmyBtn\"\n          >确 定\n        </el-button>\n      </div>\n    </el-dialog>\n    <!--  变电站新增  -->\n    <el-dialog\n      title=\"设备详情\"\n      :visible.sync=\"znsbDialogForm\"\n      width=\"68%\"\n      :before-close=\"resetForm1\"\n      v-dialogDrag\n      v-if=\"znsbDialogForm\"\n    >\n      <el-tabs v-model=\"activeTabName\">\n        <el-tab-pane label=\"基本信息\" name=\"sbDesc\">\n          <el-form\n            :model=\"sbxxForm\"\n            label-width=\"130px\"\n            ref=\"sbxxForm\"\n            :rules=\"sbxxRules\"\n            :disabled=\"assetIsDisable\"\n          >\n            <el-card class=\"box-cont\">\n              <div slot=\"header\" class=\"clearfix\">\n                <span>基本信息</span>\n              </div>\n              <el-row :gutter=\"20\" class=\"cont_top\">\n                <el-col :span=\"8\">\n                  <el-form-item label=\"所属公司\" prop=\"ssgs\">\n                    <el-select\n                      v-model=\"sbxxForm.ssgs\"\n                      :placeholder=\"assetIsDisable ? '' : '请选择所属公司'\"\n                      filterable\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in OrganizationSelectedList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"String(item.value)\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"所属电站\" prop=\"ssbdz\">\n                    <el-select\n                      v-model=\"sbxxForm.ssbdz\"\n                      :placeholder=\"assetIsDisable ? '' : '请选择所属电站'\"\n                      filterable\n                      @change=\"bdzOptionsChangeClick\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in bdzOptionsDataList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"所属间隔\" prop=\"ssjg\">\n                    <el-select\n                      v-model=\"sbxxForm.ssjg\"\n                      :placeholder=\"assetIsDisable ? '' : '请选择所属间隔'\"\n                      filterable\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in jgOptionsDataList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"设备代码\" prop=\"sbdm\">\n                    <el-input\n                      v-model=\"sbxxForm.sbdm\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写设备代码'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"设备名称\" prop=\"sbmc\">\n                    <el-input\n                      v-model=\"sbxxForm.sbmc\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写设备名称'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"设备类型\" prop=\"assetTypeCode\">\n                    <el-select\n                      v-model=\"sbxxForm.assetTypeCode\"\n                      :placeholder=\"assetIsDisable ? '' : '请选择设备类型'\"\n                      @change=\"showParams\"\n                      filterable\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in sblxOptionsDataSelected\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"电压等级\" prop=\"dydjbm\">\n                    <el-select\n                      v-model=\"sbxxForm.dydjbm\"\n                      :placeholder=\"assetIsDisable ? '' : '请选择电压等级'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in VoltageLevelSelectedList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"设备状态\" prop=\"sbzt\">\n                    <el-select\n                      v-model=\"sbxxForm.sbzt\"\n                      :placeholder=\"assetIsDisable ? '' : '请选择设备状态'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in sbztOptionsDataList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"8\">\n                  <el-form-item label=\"相数\">\n                    <el-select\n                      v-model=\"sbxxForm.xs\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写相数'\"\n                      @change=\"xsChangeFunc\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in xsOptions\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"8\">\n                  <el-form-item label=\"相别\">\n                    <el-select\n                      v-model=\"sbxxForm.xb\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写相别'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in xbOptions\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"8\">\n                  <el-form-item label=\"安装位置\" prop=\"azwz\">\n                    <el-select\n                      v-model=\"sbxxForm.azwz\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写安装位置'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in placeOptions\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"用途\">\n                    <el-input\n                      v-model=\"sbxxForm.yt\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写用途'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"型号\" prop=\"xh\">\n                    <el-autocomplete\n                      v-model=\"sbxxForm.xh\"\n                      :fetch-suggestions=\"querySearchAsync\"\n                      :placeholder=\"assetIsDisable ? '' : '请选择或输入型号'\"\n                      @select=\"handleSelect\"\n                      @change=\"handleChange\"\n                    ></el-autocomplete>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"使用环境\" prop=\"syhj\">\n                    <el-select\n                      v-model=\"sbxxForm.syhj\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写使用环境'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in placeOptions\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"生产厂家\" prop=\"sccj\">\n                    <el-input\n                      v-model=\"sbxxForm.sccj\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写生产厂家'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"制造国家\">\n                    <el-input\n                      v-model=\"sbxxForm.zzgj\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写制造国家'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"产品代号\">\n                    <el-input\n                      v-model=\"sbxxForm.cpdh\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写产品代号'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"8\">\n                  <el-form-item label=\"组合设备类型\">\n                    <el-input\n                      v-model=\"sbxxForm.zhsblxbm\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写组合设备类型'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"组合设备类型名称\">\n                    <el-input\n                      v-model=\"sbxxForm.zhsblx\"\n                      :placeholder=\"\n                        assetIsDisable ? '' : '请填写组合设备类型名称'\n                      \"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"8\">\n                  <el-form-item label=\"运行编号\" prop=\"yxbh\">\n                    <el-input\n                      v-model=\"sbxxForm.yxbh\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写运行编号'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"出厂编号\" prop=\"ccbh\">\n                    <el-input\n                      v-model=\"sbxxForm.ccbh\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写出厂编号'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item\n                    label=\"出厂日期\"\n                    class=\"add_sy_tyrq\"\n                    prop=\"ccrq\"\n                  >\n                    <el-date-picker\n                      v-model=\"sbxxForm.ccrq\"\n                      type=\"date\"\n                      :placeholder=\"assetIsDisable ? '' : '选择日期'\"\n                      format=\"yyyy-MM-dd\"\n                      value-format=\"yyyy-MM-dd\"\n                    >\n                    </el-date-picker>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item\n                    label=\"投运日期\"\n                    class=\"add_sy_tyrq\"\n                    prop=\"tyrq\"\n                  >\n                    <el-date-picker\n                      v-model=\"sbxxForm.tyrq\"\n                      type=\"date\"\n                      :placeholder=\"assetIsDisable ? '' : '选择日期'\"\n                      format=\"yyyy-MM-dd\"\n                      :picker-options=\"pickerOptions\"\n                      value-format=\"yyyy-MM-dd\"\n                    >\n                    </el-date-picker>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row :gutter=\"20\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"备注\" prop=\"bz\">\n                    <el-input\n                      v-model=\"sbxxForm.bz\"\n                      type=\"textarea\"\n                      rows=\"2\"\n                      :placeholder=\"assetIsDisable ? '' : '请输入备注'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n            </el-card>\n            <el-card class=\"box-cont\">\n              <div slot=\"header\" class=\"clearfix\">\n                <span>技术参数</span>\n              </div>\n              <el-row :gutter=\"20\" class=\"cont_top\">\n                <!--  一次设备 改为都要 -->\n                <el-col :span=\"8\">\n                  <el-form-item label=\"额定电压\" prop=\"eddy\">\n                    <el-input\n                      v-model=\"sbxxForm.eddy\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写额定电压'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"额定频率\" prop=\"edpl\">\n                    <el-input\n                      v-model=\"sbxxForm.edpl\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写额定频率'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"额定电流\" prop=\"eddl\">\n                    <el-input\n                      v-model=\"sbxxForm.eddl\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写额定电流'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"额定容量\" prop=\"edrl\">\n                    <el-input\n                      v-model=\"sbxxForm.edrl\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写额定容量'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"联结组标号\" prop=\"ljzbh\">\n                    <el-input\n                      v-model=\"sbxxForm.ljzbh\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写联结组标号'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"器身质量\" prop=\"qszl\">\n                    <el-input\n                      v-model=\"sbxxForm.qszl\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写器身质量'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"油质量\" prop=\"yzl\">\n                    <el-input\n                      v-model=\"sbxxForm.yzl\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写油质量'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"档位个数\" prop=\"dwgs\">\n                    <el-input\n                      v-model=\"sbxxForm.dwgs\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写档位个数'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"空载损耗\" prop=\"kzsh\">\n                    <el-input\n                      v-model=\"sbxxForm.kzsh\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写空载损耗'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"空载电流\" prop=\"kzdl\">\n                    <el-input\n                      v-model=\"sbxxForm.kzdl\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写空载电流'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"负载损耗\" prop=\"fzsh\">\n                    <el-input\n                      v-model=\"sbxxForm.fzsh\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写负载损耗'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"冷却方式\" prop=\"lqfs\">\n                    <el-input\n                      v-model=\"sbxxForm.lqfs\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写冷却方式'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"短路阻抗\" prop=\"dlkz\">\n                    <el-input\n                      v-model=\"sbxxForm.dlkz\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"变比/精度1\" prop=\"jddj1\">\n                    <el-input\n                      v-model=\"sbxxForm.jddj1\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写变比/精度'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"变比/精度2\" prop=\"jddj2\">\n                    <el-input\n                      v-model=\"sbxxForm.jddj2\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写变比/精度'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"变比/精度3\" prop=\"jddj3\">\n                    <el-input\n                      v-model=\"sbxxForm.jddj3\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写变比/精度'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"变比/精度4\" prop=\"jddj4\">\n                    <el-input\n                      v-model=\"sbxxForm.jddj4\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写变比/精度'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <!--  二次设备 -->\n                <el-col v-if=\"jgQueryParams.jgdl == 'ec'\" :span=\"8\">\n                  <el-form-item label=\"版本号\" prop=\"bbh\">\n                    <el-input\n                      v-model=\"sbxxForm.bbh\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写版本号'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n            </el-card>\n          </el-form>\n        </el-tab-pane>\n        <el-tab-pane label=\"设备履历\" name=\"sbRecord\" v-if=\"showFsss\">\n          <el-tabs\n            v-model=\"sbllDescTabName\"\n            @tab-click=\"handleSbllDescTabNameClick\"\n            type=\"card\"\n          >\n            <el-tab-pane label=\"状态变更\" name=\"ztbgjl\">\n              <comp-table\n                :table-and-page-info=\"resumPageInfo\"\n                @getMethod=\"getResumList\"\n                @update:multipleSelection=\"handleSelectionChange\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"隐患\" name=\"yh\">\n              <comp-table\n                :table-and-page-info=\"yhPageInfo\"\n                @getMethod=\"getYhList\"\n                v-loading=\"yhloading\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"试验\" name=\"sy\">\n              <comp-table\n                :table-and-page-info=\"syPageInfo\"\n                @getMethod=\"getSyList\"\n                v-loading=\"syloading\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"不良工况\" name=\"blgk\">\n              <comp-table\n                :table-and-page-info=\"blgkPageInfo\"\n                @getMethod=\"getBlgkList\"\n                v-loading=\"blgkloading\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"其它设备问题\" name=\"qtsbwt\">\n              <comp-table\n                :table-and-page-info=\"qtsbwtPageInfo\"\n                @getMethod=\"getqtsbwtList\"\n                v-loading=\"qtsbwtloading\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"设备检修\" name=\"jx\">\n              <comp-table\n                :table-and-page-info=\"jxPageInfo\"\n                @getMethod=\"getJxList\"\n                v-loading=\"jxloading\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"继电保护\" name=\"jdbh\">\n              <comp-table\n                :table-and-page-info=\"jdbhPageInfo\"\n                @getMethod=\"getJdbhList\"\n                v-loading=\"jdbhLoading\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"跳闸记录\" name=\"tzjl\">\n              <comp-table\n                :table-and-page-info=\"tzjlPageInfo\"\n                @getMethod=\"getTzjlList\"\n                v-loading=\"tzjlLoading\"\n              />\n            </el-tab-pane>\n          </el-tabs>\n        </el-tab-pane>\n        <el-tab-pane label=\"附属设施\" name=\"fsss\" v-if=\"showFsss\">\n          <Fsss\n            v-if=\"znsbDialogForm\"\n            :sb-info=\"this.sbxxForm\"\n            :can-edit=\"!this.assetIsDisable\"\n          ></Fsss>\n        </el-tab-pane>\n      </el-tabs>\n      <div slot=\"footer\" class=\"dialog-footer\" v-show=\"!assetIsDisable\">\n        <el-button @click=\"resetForm1\">取 消</el-button>\n        <el-button\n          v-show=\"activeTabName === 'sbDesc'\"\n          type=\"primary\"\n          @click=\"submit\"\n          :loading=\"assetSubmitLoading\"\n          class=\"pmyBtn\"\n          >确 定</el-button\n        >\n      </div>\n    </el-dialog>\n    <!--变电站所用弹出框结束-->\n\n    <!--间隔详情弹出框展示开始-->\n    <el-dialog\n      title=\"间隔信息\"\n      :visible.sync=\"jgDialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"60%\"\n      v-dialogDrag\n      :before-close=\"resetForm\"\n      @open=\"fillBdz\"\n    >\n      <el-form\n        ref=\"jgxxForm\"\n        :model=\"jgxxForm\"\n        :rules=\"rules\"\n        label-width=\"130px\"\n      >\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属电站\" prop=\"ssbdz\">\n              <el-select\n                v-model=\"jgxxForm.ssbdz\"\n                placeholder=\"请选择所属电站\"\n                filterable\n                :disabled=\"jgShow\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in bdzOptionsDataList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"间隔名称\" prop=\"jgmc\">\n              <el-input\n                v-model=\"jgxxForm.jgmc\"\n                placeholder=\"请输入间隔名称\"\n                :disabled=\"jgShow\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"间隔类型\" prop=\"jglx\">\n              <el-select\n                v-model=\"jgxxForm.jglx\"\n                placeholder=\"请选择间隔类型\"\n                filterable\n                :disabled=\"jgShow\"\n              >\n                <el-option\n                  v-for=\"item in jglxOptionsDataList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电压等级\" prop=\"dydjbm\">\n              <el-select\n                v-model=\"jgxxForm.dydjbm\"\n                :disabled=\"jgShow\"\n                placeholder=\"请选择电压等级\"\n              >\n                <el-option\n                  v-for=\"item in jgDydOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"投运日期\" class=\"add_sy_tyrq\" prop=\"tyrq\">\n              <el-date-picker\n                v-model=\"jgxxForm.tyrq\"\n                type=\"date\"\n                placeholder=\"选择日期\"\n                format=\"yyyy-MM-dd\"\n                value-format=\"yyyy-MM-dd\"\n                :disabled=\"jgShow\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\" prop=\"bz\">\n              <el-input\n                v-model=\"jgxxForm.bz\"\n                :disabled=\"jgShow\"\n                type=\"textarea\"\n                rows=\"2\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\" v-show=\"!jgShow\">\n        <el-button @click=\"resetForm\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"addJg\" class=\"pmyBtn\"\n          >确 定</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <!-- 设备数据copy -->\n    <el-dialog\n      title=\"设备复制\"\n      :visible.sync=\"isShowCopy\"\n      width=\"50%\"\n      v-dialogDrag\n    >\n      <el-form\n        label-width=\"130px\"\n        ref=\"formCopy\"\n        :model=\"formCopy\"\n        :rules=\"rulesCopy\"\n      >\n        <div>\n          <!--基本信息-->\n          <el-row>\n            <el-col :span=\"12\">\n              <el-form-item label=\"分公司：\" prop=\"ssdwbm\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formCopy.ssdwbm\"\n                  @change=\"handleFgsChange\"\n                  placeholder=\"请选择分公司\"\n                >\n                  <el-option\n                    v-for=\"item in OrganizationSelectedList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item label=\"变电站\" prop=\"bdz\">\n                <el-select\n                  style=\"width:100%\"\n                  v-model=\"formCopy.bdz\"\n                  @change=\"getSbDataListGroup\"\n                  placeholder=\"请选择变电站\"\n                  filterable\n                >\n                  <el-option\n                    v-for=\"item in wzDataListOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item label=\"所属间隔\" prop=\"ssjg\">\n                <el-select\n                  style=\"width:100%\"\n                  v-model=\"formCopy.ssjg\"\n                  placeholder=\"请选择所属间隔\"\n                  filterable\n                >\n                  <el-option\n                    v-for=\"(item, i) in sbDataList\"\n                    :key=\"item.i\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <p>\n            <span style=\"color: red;font-size:23px\">*</span>\n            <span style=\"color: red;font-size:23px\"\n              >功能描述:将该列表中的所有设备 复制一份到 表单选择的间隔下</span\n            >\n          </p>\n          <div align=\"right\" slot=\"footer\">\n            <el-button @click=\"isShowCopy = false\">取 消</el-button>\n            <el-button type=\"primary\" @click=\"copyForAsset\">复 制 </el-button>\n          </div>\n        </div>\n      </el-form>\n    </el-dialog>\n    <import-file\n      ref=\"importExcel\"\n      :export-url=\"importExcelUrl\"\n      :params=\"importExtraInfo\"\n      :valid-string=\"fileName\"\n      @getDataAfterUploading=\"getUploadData\"\n    ></import-file>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport {\n  addAsset,\n  addBdz,\n  addJg,\n  getFgsByBdzId,\n  getJgInfoList,\n  getNewTreeInfo,\n  getOrganizationSelected,\n  getTreeInfo,\n  removeAsset,\n  removeBdz,\n  removeJg,\n  adddwzyfstz,\n  exportExcel,\n  copyAsset\n} from \"@/api/dagangOilfield/asset/jgtz\";\nimport {\n  getBdAsesetListPage,\n  getBdzDataListSelected,\n  getJgDataListSelected,\n  getSbxhList,\n  getSblxDataListSelected,\n  addSbxh\n} from \"@/api/dagangOilfield/asset/bdsbtz\";\nimport { getBdzList } from \"@/api/dagangOilfield/asset/bdztz\";\nimport { deleteById, getListByBusinessId } from \"@/api/tool/file\";\nimport {\n  getParamDataList,\n  getParamsValue\n} from \"@/api/dagangOilfield/asset/parameters\";\nimport { getResumDataList } from \"@/api/dagangOilfield/asset/sdsb\";\nimport { getUsers } from \"@/api/system/usergroup\";\nimport { getDictTypeData } from \"@/api/system/dict/data\";\nimport Fsss from \"../bdgl/fsss.vue\";\nimport { getData, getQtwtlrData } from \"@/api/blgk/blgk\";\nimport { getSybgjlDataByPage } from \"@/api/dagangOilfield/bzgl/sybglr\";\nimport { getListFirst } from \"@/api/yxgl/bdyxgl/qxgl\";\nimport {\n  getFgsOptions,\n  getSelectOptionsByOrgType,\n  getListFour,\n  getListSecond,\n  getListSeven\n} from \"@/api/yxgl/bdyxgl/zbgl\";\nimport importFile from \"@/components/ExportExcel/importExcel\";\n\nexport default {\n  name: \"jgtz\",\n  components: { Fsss, importFile },\n  data() {\n    return {\n      wzDataListOptions: [],\n      sbDataList: [],\n      formCopy: {},\n      isShowCopy: false,\n      loading: false,\n      icons: {\n        bdzList: \"categoryTreeIcons\",\n        bdz: \"tableIcon\",\n        jg: \"classIcon\",\n        jgdl: \"classIcon\" //间隔大类\n      },\n\n      //新增设备时变电站下拉框\n      bdzOptionsDataList: [],\n      //间隔类型下拉数据\n      jglxOptionsDataList: [],\n      //树结构监听属性\n      filterText: \"\",\n      //组织结构下拉数据\n      OrganizationSelectedList: [],\n      //树结构上面得筛选框参数\n      treeForm: {},\n      //电压等级下拉框数据\n      VoltageLevelSelectedList: [],\n      //间隔特殊电压等级\n      jgDydOptions: [],\n      //带字母的电压等级\n      dydjOptionsWithString: [],\n      //新增设备时设备状态下拉框数据\n      sbztOptionsDataList: [],\n      //新增设备下拉框数据\n      placeOptions: [],\n      xsOptions: [],\n      xbOptions: [],\n      singleClickData: undefined,\n      //间隔信息是否显示\n      jgShow: false,\n      filterInfo: {},\n      //通用列表参数\n      tableAndPageInfo: {},\n      jgQueryParams: {\n        ssbdz: undefined,\n        dydj: undefined,\n        pageSize: 10,\n        pageNum: 1\n      },\n      jgxxForm: {\n        objId: undefined,\n        wzbm: undefined,\n        wzid: undefined,\n        sccj: undefined,\n        jgmc: undefined,\n        tyrq: undefined,\n        jglx: undefined,\n        dydj: undefined,\n        zt: undefined,\n        ccrq: undefined,\n        ggxh: undefined,\n        jd: undefined,\n        wd: undefined,\n        ssdd: undefined,\n        ssbdz: undefined,\n        bdzmc: undefined,\n        bz: undefined\n      },\n      //间隔展示\n      jgShowTable: false,\n      //间隔添加按钮弹出框\n      jgDialogFormVisible: false,\n      //弹出框表单\n      form: {},\n      pickerOptions: {\n        shortcuts: [\n          {\n            text: \"今天\",\n            onClick(picker) {\n              picker.$emit(\"pick\", new Date());\n            }\n          },\n          {\n            text: \"昨天\",\n            onClick(picker) {\n              const date = new Date();\n              date.setTime(date.getTime() - 3600 * 1000 * 24);\n              picker.$emit(\"pick\", date);\n            }\n          },\n          {\n            text: \"明天\",\n            onClick(picker) {\n              const date = new Date();\n              date.setTime(date.getTime() + 3600 * 1000 * 24);\n              picker.$emit(\"pick\", date);\n            }\n          }\n        ]\n      },\n      //组织树\n      treeOptions: [],\n      //删除是否可用\n      multipleSensor: true,\n      //查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        roleKey: \"\",\n        roleName: \"\",\n        status: \"\"\n      },\n      showSearch: true,\n      rules: {\n        // wzbm:[{  required: true, message: '请填写位置编码', trigger: 'blur' }],\n        // wzid:[{required:true,message:'请填写位置id',trigger:'blur'}],\n        jgmc: [{ required: true, message: \"请填写间隔名称\", trigger: \"blur\" }],\n        // zt:[{required:true,message:'请填写状态',trigger:'blur'}],\n        ssbdz: [\n          { required: true, message: \"请选择所属变电站\", trigger: \"change\" }\n        ],\n        dydj: [{ required: true, message: \"请选择电压等级\", trigger: \"change\" }]\n      },\n      //新\n      //控制变电站表格是否展示\n      bdzdataShow: true,\n      importExcelUrl: \"\",\n      fileName: \"\",\n      //控制变电站表格是否展示\n      jgdataShow: false,\n      //控制变电站表格是否展示\n      znsbdataShow: false,\n      bdzqueryParams: {\n        ssgs: undefined,\n        dydjbm: undefined,\n        ssbdz: undefined,\n        ssjg: undefined,\n        sbmc: undefined,\n        assetTypeCode: undefined,\n        sbzt: undefined,\n        pageNum: 1,\n        pageSize: 10\n      },\n      params: {\n        //bm:undefined,\n        // ssdwbm: undefined,\n        pageNum: 1,\n        pageSize: 10\n      },\n      filterInfo1: {\n        data: {\n          ywdwArr: [],\n          jhnyArr: [],\n          xlArr: \"\",\n          jhlxArr: [],\n          jhztArr: \"\",\n          sfdd: \"\"\n        },\n        fieldList: [\n          { label: \"变电站名称\", type: \"input\", value: \"bdzmc\" },\n          {\n            label: \"投运日期\",\n            type: \"date\",\n            value: \"tyrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          {\n            label: \"所属公司\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"ssdwbm\",\n            options: []\n          },\n          {\n            label: \"是否枢纽站\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"sfsnz\",\n            options: [\n              {\n                value: \"是\",\n                label: \"是\"\n              },\n              {\n                value: \"否\",\n                label: \"否\"\n              }\n            ]\n          },\n          {\n            label: \"设备状态\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"sbzt\",\n            options: []\n          },\n          {\n            label: \"电压等级\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"dydj\",\n            options: []\n          }\n        ]\n      },\n      //变电站table数据\n      tableAndPageInfo1: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n          pageResize: \"\"\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"ssdwmc\", label: \"所属公司\", minWidth: \"120\" },\n          { prop: \"bdzmc\", label: \"变电站名称\", minWidth: \"180\" },\n          { prop: \"dydj\", label: \"电压等级\", minWidth: \"80\" },\n          { prop: \"sbzt\", label: \"设备状态\", minWidth: \"80\" },\n          { prop: \"sfsnz\", label: \"是否枢纽站\", minWidth: \"140\" },\n          // {prop: 'sblx', label: '设备类型', minWidth: '120'},\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"120\" }\n          /* {\n             fixed: \"right\",\n             prop: 'operation',\n             label: '操作',\n             minWidth: '130px',\n             style: {display: 'block'},\n             operation: [\n               {name: '修改', clickFun: this.updatebdz},\n               {name: '详情', clickFun: this.bdzDetails},\n             ]\n           },*/\n        ]\n        /* [\n         {prop: 'jgmc', label: '间隔名称', minWidth: '120'},\n         {prop: 'jglx', label: '间隔类型', minWidth: '120'},\n         {prop: 'dydj', label: '电压等级', minWidth: '120'},\n         {prop: 'tyrq', label: '投运日期', minWidth: '120'},\n\n         // {prop: 'zt', label: '状态', minWidth: '120'},\n         // {prop: 'jd', label: '经度', minWidth: '140'},\n         // {prop: 'wd', label: '纬度', minWidth: '120'},\n         // {prop: 'ssdd', label: '所属调度', minWidth: '120'},\n         {\n           fixed: \"right\",\n           prop: 'operation',\n           label: '操作',\n           minWidth: '130px',\n           style: {display: 'block'},\n           operation: [\n             {name: '修改', clickFun: this.updateJg},\n             {name: '详情', clickFun: this.jgDetails},\n           ]\n         },\n       ]*/\n      },\n      filterInfo2: {\n        data: {\n          ywdwArr: [],\n          jhnyArr: [],\n          xlArr: \"\",\n          jhlxArr: [],\n          jhztArr: \"\",\n          sfdd: \"\"\n        },\n        fieldList: [\n          { label: \"间隔名称\", type: \"input\", value: \"jgmc\", options: [] },\n          { label: \"间隔类型\", type: \"input\", value: \"jglx\", options: [] },\n          {\n            label: \"电压等级\",\n            type: \"select\",\n            value: \"dydj\",\n            options: []\n          },\n          {\n            label: \"投运日期\",\n            type: \"date\",\n            value: \"tyrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          }\n        ]\n      },\n      //间隔数据\n      tableAndPageInfo2: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n          pageResize: \"\"\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"jgmc\", label: \"间隔名称\", minWidth: \"120\" },\n          { prop: \"jglx\", label: \"间隔类型\", minWidth: \"120\" },\n          { prop: \"dydj\", label: \"电压等级\", minWidth: \"120\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"120\" }\n          /*{\n            fixed: \"right\",\n            prop: 'operation',\n            label: '操作',\n            minWidth: '130px',\n            style: {display: 'block'},\n            operation: [\n              {name: '修改', clickFun: this.updateJg},\n              {name: '详情', clickFun: this.jgDetails},\n            ]\n          },*/\n        ]\n      },\n      filterInfo3: {\n        data: {\n          ywdwArr: [],\n          jhnyArr: [],\n          xlArr: \"\",\n          jhlxArr: [],\n          jhztArr: \"\",\n          sfdd: \"\"\n        },\n        fieldList: [\n          { label: \"设备类型\", type: \"input\", value: \"sblxmc\", options: [] },\n          { label: \"设备名称\", type: \"input\", value: \"sbmc\", options: [] },\n          {\n            label: \"电压等级\",\n            type: \"select\",\n            value: \"dydjName\",\n            options: []\n          },\n          {\n            label: \"设备状态\",\n            type: \"select\",\n            value: \"sbzt\",\n            options: []\n          },\n          {\n            label: \"投运日期\",\n            type: \"date\",\n            value: \"tyrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"型号\", type: \"input\", value: \"ggxh\", options: [] },\n          { label: \"额定电压\", type: \"input\", value: \"eddy\", options: [] },\n          { label: \"额定电流\", type: \"input\", value: \"eddl\", options: [] },\n          { label: \"额定频率\", type: \"input\", value: \"edpl\", options: [] },\n          { label: \"生产厂家\", type: \"input\", value: \"sccj\", options: [] }\n        ]\n      },\n      //站内设备台账\n      tableAndPageInfo3: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n          pageResize: \"\"\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"deptname\", label: \"所属公司\", minWidth: \"120\" },\n          { prop: \"bdzmc\", label: \"变电站名称\", minWidth: \"180\" },\n          { prop: \"wzmc\", label: \"所属间隔\", minWidth: \"180\" },\n          { prop: \"sblxmc\", label: \"设备类型\", minWidth: \"180\" },\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"180\" },\n          { prop: \"dydjName\", label: \"电压等级\", minWidth: \"80\" },\n          { prop: \"sbzt\", label: \"设备状态\", minWidth: \"80\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"120\" },\n          { prop: \"ggxh\", label: \"型号\", minWidth: \"120\" },\n          { prop: \"eddy\", label: \"额定电压\", minWidth: \"120\" },\n          { prop: \"eddl\", label: \"额定电流\", minWidth: \"120\" },\n          { prop: \"edpl\", label: \"额定频率\", minWidth: \"120\" },\n          { prop: \"sccj\", label: \"生产厂家\", minWidth: \"120\" }\n          /*{\n            fixed: \"right\",\n            prop: 'operation',\n            label: '操作',\n            minWidth: '120px',\n            style: {display: 'block'},\n            operation: [\n              /!*{name: \"状态变更\", clickFun: this.updateStatus},\n              {name: \"流程查看\", clickFun: this.ztbglcSay},*!/\n              {name: '修改', clickFun: this.updateAsset},\n              {name: '详情', clickFun: this.assetDetails},\n            ]\n          },*/\n        ]\n      },\n      //设备基本信息\n      jbxxForm: {\n        attachment: [],\n        objId: undefined,\n        ssdwmc: undefined,\n        ssdwbm: undefined,\n        sbdm: undefined,\n        ddsbh: undefined,\n        dydj: undefined,\n        tyrq: undefined,\n        jgdy: undefined,\n        xb: undefined,\n        xs: undefined,\n        ccrq: undefined,\n        azwz: undefined,\n        yt: undefined,\n        fzr: undefined,\n        cpdh: undefined,\n        eddy: undefined,\n        edpl: undefined,\n        sbzt: undefined,\n        syhj: undefined,\n        sccj: undefined,\n        zzgj: undefined,\n        zhsblx: undefined,\n        zhsblxmc: undefined,\n        eddl: undefined,\n        yxbh: undefined,\n        ccbh: undefined,\n        bdzmc: undefined,\n        bdzszbh: undefined, //变电站数字编号\n        ssdw: undefined, //所属电网\n        dzlx: undefined, //电站类型\n        sfzhzdh: undefined, //是否综合自动化站\n        sfszhbdz: undefined, //是否数字化变电站\n        returnDate: undefined, //退运日期\n        zymj: undefined, //占地面积\n        whdj: undefined, //污秽等级\n        zbfs: undefined, //值班方式\n        sfgqtx: undefined, //是否光纤通讯\n        hb: undefined, //海拔\n        gcbh: undefined, //工程编号\n        sjdw: undefined, //设计单位\n        jldw: undefined, //监理单位\n        zyjb: undefined, // 电站重要级别\n        bzfs: undefined, //布置方式\n        bdzdz: undefined, //变电站地址\n        jzmj: undefined, // 建筑面积\n        phone: undefined, //联系电话\n        gcmc: undefined, // 工程名称\n        sgdw: undefined, //施工单位\n        dqtz: undefined, //地区特征\n        zgddgxq: undefined, // 最高调度管辖权\n        sfmzn: undefined, // 是否满足n-1\n        sfjrgzxt: undefined, //是否接入故障信息远传系统\n        sfjravc: undefined, //是否接入avc\n        sfjzjk: undefined, //是否集中监控\n        jkzxmc: undefined, //接入得监控中心\n        bz: undefined //备注\n      },\n      //变电站添加按钮弹出框\n      bdzDialogFormVisible: false,\n      //标题\n      title: \"\",\n      imgList: [],\n      currUser: \"\",\n      //变电站信息是否可编辑\n      isDisabled: false,\n      //上传图片时的请求头\n      header: {},\n      //所属基地站\n      ssjdzList: [],\n      uploadData: {\n        type: \"\",\n        businessId: undefined\n      },\n      importExtraInfo: {},\n      //变电站新增弹框\n      bdzDidalogForm: false,\n      paramQuery: {\n        sblxbm: undefined\n      },\n      resumeQuery: {\n        foreignNum: undefined,\n        sblx: undefined\n      },\n      znsbDialogForm: false,\n      assetSubmitLoading: false,\n      //设备表单\n      sbxxForm: {},\n      //设备信息展示\n      assetIsDisable: false,\n      //弹出框tab页\n      activeTabName: \"sbDesc\",\n      //查询间隔下拉框数据的参数\n      selectJgOptionsParam: {},\n      //新增设备时所属间隔下拉框列表\n      jgOptionsDataList: [],\n      //设备类型下拉框数据\n      sblxOptionsDataSelected: [],\n      //技术参数绑定\n      jscsForm: {},\n      //技术参数动态展示集合\n      jscsLabelList: [],\n      //设备履历tab页\n      sbllDescTabName: \"ztbgjl\",\n      //设备履历试验记录数据\n      sblvsyjlList: [],\n      //设备履历缺陷记录数据集合\n      sbllqxjlList: [],\n      resumPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: false,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"foreignNum\", label: \"设备名称\", minWidth: \"120\" },\n          // { prop: \"sblx\", label: \"设备类型\", minWidth: \"180\" },\n          { prop: \"bglx\", label: \"变更类型\", minWidth: \"120\" },\n          { prop: \"ms\", label: \"描述\", minWidth: \"250\" },\n          { prop: \"bgr\", label: \"变更人\", minWidth: \"140\" },\n          { prop: \"bgsj\", label: \"变更时间\", minWidth: \"140\" }\n        ]\n      },\n      //设备附属设施list\n      fsssList: [],\n      //设备详情校验规则\n      sbxxRules: {\n        ssgs: [\n          { required: true, message: \"所属公司不能为空\", trigger: \"select\" }\n        ],\n        ssbdz: [\n          { required: true, message: \"所属电站不能为空\", trigger: \"select\" }\n        ],\n        ssjg: [\n          { required: true, message: \"所属间隔不能为空\", trigger: \"select\" }\n        ],\n        sbmc: [\n          { required: true, message: \"设备名称不能为空\", trigger: \"blur\" }\n        ],\n        assetTypeCode: [\n          { required: true, message: \"设备类型不能为空\", trigger: \"select\" }\n        ],\n        // dydjbm: [\n        //   {required: true, message: \"电压等级不能为空\", trigger: \"select\"},\n        // ],\n        sbzt: [\n          { required: true, message: \"设备状态不能为空\", trigger: \"select\" }\n        ],\n        // azwz: [\n        //   {required: true, message: \"安装位置不能为空\", trigger: \"blur\"},\n        // ],\n        // xh: [\n        //   {required: true, message: \"型号不能为空\", trigger: \"select\"},\n        // ],\n        // eddy: [\n        //   {required: true, message: \"额定电压不能为空\", trigger: \"blur\"},\n        // ],\n        // edpl: [\n        //   {required: true, message: \"额定频率不能为空\", trigger: \"blur\"},\n        // ],\n        syhj: [\n          { required: true, message: \"使用环境不能为空\", trigger: \"blur\" }\n        ],\n        sccj: [\n          { required: true, message: \"生产厂家不能为空\", trigger: \"blur\" }\n        ],\n        // yxbh: [\n        //   {required: true, message: \"运行编号不能为空\", trigger: \"blur\"},\n        // ],\n        // ccbh: [\n        //   {required: true, message: \"出厂编号不能为空\", trigger: \"blur\"},\n        // ],\n        // ccrq: [\n        //   {required: true, message: \"出厂日期不能为空\", trigger: \"change\"},\n        // ],\n        tyrq: [\n          { required: true, message: \"投运日期不能为空\", trigger: \"change\" }\n        ]\n      },\n      rulesCopy: {\n        ssdwbm: [\n          { required: true, message: \"所属公司不能为空\", trigger: \"select\" }\n        ],\n        bdz: [\n          { required: true, message: \"所属电站不能为空\", trigger: \"select\" }\n        ],\n        ssjg: [\n          { required: true, message: \"所属间隔不能为空\", trigger: \"select\" }\n        ]\n      },\n      ggxhList: [], //型号list\n      jglxcx: \"\", //所属间隔类型\n      ssbdz: \"\", //所属变电站,\n      ssbdzmc: \"\",\n      ssjg: \"\", //所属间隔\n      znsbParams: {\n        //站内设备分页参数\n        pageSize: 10,\n        pageNum: 1\n      },\n      fgsArr: [],\n      jgdlMap: new Map(), //存间隔大类数据\n      currentBdz: \"\", //当前变电站\n      showFsss: false, //是否显示附属设施tab页\n      bdsbid: \"\", //变电设备ID\n      sbmc: \"\", //设备名称\n      //不良工况列表\n      blgkPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: { checkBox: false, serialNumber: true },\n        tableData: [],\n        tableHeader: [\n          // { prop: \"fgsmc\", label: \"分公司\", minWidth: \"140\" },\n          // { prop: \"bdzmc\", label: \"变电站\", minWidth: \"140\" },\n          // { prop: \"jgmc\", label: \"间隔名称\", minWidth: \"100\" },\n          // { prop: \"sblxmc\", label: \"设备类型\", minWidth: \"140\" },\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"140\" },\n          { prop: \"ms\", label: \"不良工况描述\", minWidth: \"160\", showPop: true },\n          { prop: \"flyjCn\", label: \"分类依据\", minWidth: \"160\", showPop: true },\n          { prop: \"lrrmc\", label: \"录入人\", minWidth: \"100\" },\n          { prop: \"fxsj\", label: \"发现时间\", minWidth: \"100\" },\n          { prop: \"xcsj\", label: \"消除时间\", minWidth: \"140\" }\n        ]\n      },\n\n      //其它设备问题\n      qtsbwtPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: { checkBox: false, serialNumber: true },\n        tableData: [],\n        tableHeader: [\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"140\" },\n          { prop: \"ms\", label: \"问题描述\", minWidth: \"160\", showPop: true },\n          { prop: \"lrrmc\", label: \"录入人\", minWidth: \"100\" },\n          { prop: \"fxsj\", label: \"发现时间\", minWidth: \"100\" }\n        ]\n      },\n\n      //试验列表\n      syPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 0]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"试验专业\", prop: \"syzy\" },\n          { label: \"试验性质\", prop: \"syxz\" },\n          { label: \"试验名称\", prop: \"symc\", minWidth: \"200\", showPop: true },\n          // { label: \"设备地点\", prop: \"sydd\", minWidth: \"120\" },\n          { label: \"试验设备\", prop: \"sbmc\", minWidth: \"100\", showPop: true },\n          { label: \"试验模板名称\", prop: \"symb\", minWidth: \"120\" },\n          { label: \"天气\", prop: \"tq\" },\n          { label: \"试验日期\", prop: \"syrq\" },\n          { label: \"试验人员\", prop: \"syryid\" },\n          { label: \"流程状态\", prop: \"ztmc\" }\n        ],\n        option: { checkBox: false, serialNumber: true }\n      },\n      //隐患列表\n      yhPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          pageResize: \"\",\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: false,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          // { prop: \"ssgs\", label: \"所属公司\", minWidth: \"120\" },\n          // { prop: \"ssdz\", label: \"所属位置\", minWidth: \"120\" },\n          { prop: \"sb\", label: \"主设备\", minWidth: \"120\" },\n          // { prop: \"sblx\", label: \"设备类型\", minWidth: \"100\" },\n          // {prop: 'dydj', label: '电压等级', minWidth: '100'},\n          { prop: \"sbxh\", label: \"设备型号\", minWidth: \"120\" },\n          { prop: \"sccj\", label: \"生产厂家\", minWidth: \"120\" },\n          { prop: \"bzqxQxdj\", label: \"隐患性质\", minWidth: \"120\" },\n          { prop: \"qxnr\", label: \"隐患内容\", minWidth: \"120\", showPop: true },\n          { prop: \"jxlbCn\", label: \"是否触发状态评价\", minWidth: \"80\" },\n          { prop: \"ztmc\", label: \"状态\", minWidth: \"120\" },\n          { prop: \"fxrq\", label: \"发现时间\", minWidth: \"100\", custom: true }\n        ]\n      },\n      //检修列表\n      jxPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          pageResize: \"\",\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: false,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          // { prop: \"fgsmc\", label: \"分公司\", minWidth: \"150\" },\n          // { prop: \"bdzmc\", label: \"变电站\", minWidth: \"150\" },\n          // { prop: \"ssjg\", label: \"所属间隔\", minWidth: \"150\" },\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"100\" },\n          { prop: \"rq\", label: \"日期\", minWidth: \"120\" },\n          { prop: \"xslb\", label: \"修试类别\", minWidth: \"100\" },\n          { prop: \"nr\", label: \"内容\", minWidth: \"100\" },\n          { prop: \"jl\", label: \"结论\", minWidth: \"80\" },\n          { prop: \"xsfzr\", label: \"修试负责人\", minWidth: \"90\" },\n          { prop: \"ysfzr\", label: \"验收负责人\", minWidth: \"90\" },\n          { prop: \"jlr\", label: \"记录人\", minWidth: \"90\" }\n        ]\n      },\n      //继电保护列表\n      jdbhPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          pageResize: \"\",\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: false,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"rq\", label: \"日期\", minWidth: \"80\" },\n          { prop: \"nr\", label: \"内容\", minWidth: \"180\" },\n          { prop: \"jl\", label: \"结论\", minWidth: \"80\" },\n          { prop: \"sygzfzr\", label: \"试验工作负责人\", minWidth: \"110\" },\n          { prop: \"ysfzr\", label: \"验收负责人\", minWidth: \"100\" },\n          { prop: \"jlr\", label: \"记录人\", minWidth: \"100\" }\n        ]\n      },\n      //跳闸记录列表\n      tzjlPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          pageResize: \"\",\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: false,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"rqsj\", label: \"日期时间\", minWidth: \"160\" },\n          {\n            prop: \"bhdzqk\",\n            label: \"保护动作情况\",\n            minWidth: \"180\",\n            isShowProp: true\n          },\n          { prop: \"dlqjcqk\", label: \"断路器检查情况\", minWidth: \"120\" },\n          { prop: \"gzdl\", label: \"故障电流\", minWidth: \"100\" },\n          { prop: \"gztzcs\", label: \"故障跳闸次数\", minWidth: \"100\" },\n          { prop: \"jltzjl\", label: \"累计跳闸次数\", minWidth: \"100\" },\n          { prop: \"zhdxrq\", label: \"最后大修日期\", minWidth: \"120\" },\n          { prop: \"jlr\", label: \"记录人\", minWidth: \"120\" }\n        ]\n      },\n      yhloading: false,\n      syloading: false,\n      blgkloading: false,\n      qtsbwtloading: false,\n      jxloading: false,\n      jdbhLoading: false,\n      tzjlLoading: false,\n      isSelectingFromDropdown: false, // 添加标记变量\n    };\n  },\n  watch: {\n    //监听筛选框值发生变化进而筛选树结构\n    filterText(val) {\n      this.$refs.tree.filter(val);\n    }\n  },\n  async created() {\n    //获取组织结构下拉数据\n    this.getFgsOptions();\n    this.getJdzOptions();\n    //获取变电站下拉框数据\n    this.getBdzDataListSelected();\n    //获取选择框数据\n    this.getSelectDataInfo();\n    //获取新的设备拓扑树\n    await this.getNewTreeInfo();\n    this.isShow1 = true;\n    //初始化加载时加载所有变电站信息\n    await this.getData();\n    await this.getJglxList();\n    //初始化时加载页面内容\n    this.tableAndPageInfo = { ...this.tableAndPageInfo1 };\n  },\n  mounted() {\n    //获取token\n    this.header.token = getToken();\n    this.currUser = this.$store.getters.name;\n  },\n  methods: {\n    //所属公司change事件\n    handleFgsChange(fgsValue) {\n      //清空之前得选中值\n      this.wzDataListOptions = [];\n      this.$set(this.formCopy, \"bdz\", \"\");\n      //获取变电站方法\n      getBdzDataListSelected({ ssdwbm: fgsValue }).then(res => {\n        this.wzDataListOptions = res.data;\n      });\n    },\n    async getSbDataListGroup(val) {\n      this.$set(this.formCopy, \"ssjg\", \"\");\n      let res = await getJgDataListSelected({ ssbdz: val + \"\" });\n      if (res.code === \"0000\") {\n        this.sbDataList = res.data;\n      } else {\n        this.$message({\n          type: \"error\",\n          message: \"间隔数据获取失败!\"\n        });\n      }\n    },\n    xsChangeFunc(val) {\n      if (val === \"三相\") {\n        this.$set(this.sbxxForm, \"xb\", \"ABC\");\n      } else {\n        this.$set(this.sbxxForm, \"xb\", \"\");\n      }\n    },\n    /**\n     * 获取基地站下拉数据\n     */\n    getJdzOptions() {\n      getSelectOptionsByOrgType(JSON.stringify(\"07\")).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.ssjdzList = res.data;\n      });\n    },\n    //查询检修数据\n    getJxList(params) {\n      this.jxloading = true;\n      const param = {\n        ...{ pageSize: 10, pageNum: 1 },\n        ...params,\n        ...{ sbmc: this.sbmc, bdz: this.ssbdz },\n        ...{ mySorts: [{ prop: \"rq\", asc: false }] }\n      };\n      getListFour(param).then(res => {\n        this.jxPageInfo.tableData = res.data.records;\n        this.jxPageInfo.pager.total = res.data.total;\n        this.jxloading = false;\n      });\n    },\n    getJdbhList(params) {\n      this.jdbhLoading = true;\n      const param = {\n        ...{ pageSize: 10, pageNum: 1 },\n        ...params,\n        ...{ sbmc: this.sbmc, bdz: this.ssbdz },\n        ...{ mySorts: [{ prop: \"rq\", asc: false }] }\n      };\n      getListSecond(param).then(res => {\n        this.jdbhPageInfo.tableData = res.data.records;\n        this.jdbhPageInfo.pager.total = res.data.total;\n        this.jdbhLoading = false;\n      });\n    },\n    getTzjlList(params) {\n      this.tzjlLoading = true;\n      const param = {\n        ...{ pageSize: 10, pageNum: 1 },\n        ...params,\n        ...{ dlqbh: this.sbmc, bdz: this.ssbdz },\n        ...{ mySorts: [{ prop: \"rqsj\", asc: false }] }\n      };\n      getListSeven(param).then(res => {\n        this.tzjlPageInfo.tableData = res.data.records;\n        this.tzjlPageInfo.pager.total = res.data.total;\n        this.tzjlLoading = false;\n      });\n    },\n    //查询不良工况数据\n    getBlgkList(params) {\n      this.blgkloading = true;\n      const param = {\n        ...{ pageSize: 10, pageNum: 1 },\n        ...params,\n        ...{ sbid: this.bdsbid }\n      };\n      getData(param).then(res => {\n        this.blgkPageInfo.tableData = res.data.records;\n        this.blgkPageInfo.pager.total = res.data.total;\n        this.blgkloading = false;\n      });\n    },\n\n    //其它设备录入问题\n    getqtsbwtList(params) {\n      this.qtsbwtloading = true;\n      const param = {\n        ...{ pageSize: 10, pageNum: 1 },\n        ...params,\n        ...{ sbid: this.bdsbid }\n      };\n      getQtwtlrData(param).then(res => {\n        this.qtsbwtPageInfo.tableData = res.data.records;\n        this.qtsbwtPageInfo.pager.total = res.data.total;\n        this.qtsbwtloading = false;\n      });\n    },\n\n    //试验数据\n    getSyList(params) {\n      this.syloading = true;\n      const param = {\n        ...{ pageSize: 10, pageNum: 1 },\n        ...params,\n        ...{ sysbid: this.bdsbid }\n      };\n      getSybgjlDataByPage(param).then(res => {\n        if (res.code === \"0000\") {\n          this.syPageInfo.tableData = res.data.records;\n          this.syPageInfo.pager.total = res.data.total;\n        }\n        this.syloading = false;\n      });\n    },\n    //隐患数据\n    getYhList(params) {\n      this.yhloading = true;\n      const param = {\n        ...{ pageSize: 10, pageNum: 1 },\n        ...params,\n        ...{ sbid: this.bdsbid }\n      };\n      getListFirst(param).then(res => {\n        if (res.code === \"0000\") {\n          this.yhPageInfo.tableData = res.data.records;\n          this.yhPageInfo.pager.total = res.data.total;\n        }\n        this.yhloading = false;\n      });\n    },\n    //获取间隔类型字典\n    getJglxList() {\n      getDictTypeData(\"dwzy_jglx\").then(res => {\n        this.jglxOptionsDataList = res.data;\n        res.data.forEach(item => {\n          if (!this.jgdlMap.has(item.value)) {\n            this.jgdlMap.set(item.value, item.remark);\n          }\n        });\n      });\n    },\n    getSelectDataInfo() {\n      //110 不带字母\n      getDictTypeData(\"dg_dydj\").then(res => {\n        this.VoltageLevelSelectedList = res.data;\n      });\n      //间隔特殊等级\n      getDictTypeData(\"jgtz_gjDydj\").then(res => {\n        this.jgDydOptions = res.data;\n        this.filterInfo2.fieldList.map(item => {\n          if (item.value == \"dydj\") {\n            return (item.options = this.jgDydOptions);\n          }\n        });\n      });\n      // 110kV 带字母\n      getDictTypeData(\"gttz-dydj\").then(res => {\n        this.dydjOptionsWithString = res.data;\n        this.filterInfo1.fieldList.map(item => {\n          if (item.value == \"dydj\") {\n            return (item.options = this.dydjOptionsWithString);\n          }\n        });\n\n        this.filterInfo3.fieldList.map(item => {\n          if (item.value == \"dydjName\") {\n            return (item.options = this.dydjOptionsWithString);\n          }\n        });\n      });\n      //设备状态\n      getDictTypeData(\"jgtz_sbzt\").then(res => {\n        this.sbztOptionsDataList = res.data;\n        this.filterInfo1.fieldList.map(item => {\n          if (item.value == \"sbzt\") {\n            return (item.options = this.sbztOptionsDataList);\n          }\n        });\n        this.filterInfo3.fieldList.map(item => {\n          if (item.value == \"sbzt\") {\n            return (item.options = this.sbztOptionsDataList);\n          }\n        });\n      });\n      //安装位置\n      getDictTypeData(\"jgtz_azwz\").then(res => {\n        this.placeOptions = res.data;\n      });\n      //相别\n      getDictTypeData(\"jgtz_xb\").then(res => {\n        this.xbOptions = res.data;\n      });\n      //相数\n      getDictTypeData(\"jgtz_xs\").then(res => {\n        this.xsOptions = res.data;\n      });\n    },\n    // 新增信息所属电站要自动带入\n    fillBdz() {\n      this.$set(this.jgxxForm, \"ssbdz\", this.currentBdz);\n    },\n    async deleteFileById(id) {\n      let { code } = await deleteById(id);\n      if (code === \"0000\") {\n        await this.getFileList();\n        this.$message({\n          type: \"success\",\n          message: \"文件删除成功!\"\n        });\n      }\n    },\n    //树监听事件\n    filterNode(value, data) {\n      if (!value) return true;\n      return data.label.indexOf(value) !== -1;\n    },\n    //获取新的设备拓扑树\n    getNewTreeInfo() {\n      getUsers({ personGroupId: 76, deptId: 0, deptName: \"\" }).then(res => {\n        let deptId = this.$store.getters.deptId.toString();\n        res.data.forEach(item => {\n          if (item.userName === this.currUser) {\n            //如果人员组里面有需要排除的人，则不需要用deptId进行过滤\n            deptId = \"\";\n            return false;\n          }\n        });\n        if (this.fgsArr.includes(deptId)) {\n          this.treeForm.ssdwbm = deptId;\n        }\n        getNewTreeInfo(this.treeForm).then(res => {\n          this.treeOptions = res.data;\n        });\n      });\n    },\n    /**\n     * 获取分公司下拉数据\n     */\n    getFgsOptions() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n          this.fgsArr.push(item.value.toString());\n        });\n        this.OrganizationSelectedList = res.data;\n        this.filterInfo1.fieldList.map(item => {\n          if (item.value == \"ssdwbm\") {\n            return (item.options = this.OrganizationSelectedList);\n          }\n        });\n      });\n    },\n    //旧树形数据获取\n    getTreeInfoList() {\n      getTreeInfo().then(res => {\n        this.treeOptions = res.data;\n      });\n    },\n    //列表查询\n    async getData(param) {\n      this.loading = true;\n      this.params = param;\n      await getSblxDataListSelected({ type: \"变电设备\" }).then(res => {\n        this.sblxOptionsDataSelected = res.data;\n        this.loading = false;\n      });\n      //判断翻页是执行的哪个表格的数据\n      if (this.bdzdataShow) {\n        //初始进来请求变电站台账\n        await this.getbdzData(param);\n      }\n      if (this.jgdataShow) {\n        await this.getJgData(param);\n      }\n      if (this.znsbdataShow) {\n        await this.getZnsbData(param);\n      }\n    },\n    /**\n     * 表格多选框\n     */\n    handleSelectionChange(selection) {\n      // this.ids = selection.map(item => item.objId);\n      // this.single = selection.length !== 1;\n      this.singleClickData =\n        selection.length > 0 ? { ...selection[0] } : undefined;\n      // this.multiple = !selection.length;\n    },\n\n    //间隔添加按钮\n    jgAddjgButton() {\n      this.jgShow = false;\n      this.jgDialogFormVisible = true;\n    },\n    addJg() {\n      this.$refs[\"jgxxForm\"].validate(valid => {\n        if (valid) {\n          let jglx = this.jgxxForm.jglx;\n          if (jglx) {\n            //保存时设置间隔大类\n            this.jgxxForm.jgdl = this.jgdlMap.get(jglx);\n          }\n          addJg(this.jgxxForm).then(res => {\n            if (res.code == \"0000\") {\n              this.$message.success(\"操作成功！\");\n              this.jgDialogFormVisible = false;\n              // this.tableAndPageInfo1.pager.pageResize = \"Y\";\n              this.getJgData();\n              //获取新的设备拓扑树\n              this.getNewTreeInfo();\n            }\n          });\n        } else {\n          return false;\n        }\n      });\n    },\n    getJgListInfo() {\n      getJgInfoList(this.jgQueryParams).then(res => {\n        this.tableAndPageInfo1.tableData = res.data.records;\n        this.tableAndPageInfo1.pager.total = res.data.total;\n        // this.tableAndPageInfo = {...this.tableAndPageInfo2}\n      });\n    },\n    /**\n     * 删除间隔\n     */\n    removeAll(row) {\n      if (this.bdzdataShow) {\n        this.deleteBdz(row);\n      }\n      if (this.jgdataShow) {\n        this.removeAsset(row);\n      }\n      if (this.znsbdataShow) {\n        this.deleteJg(row);\n      }\n    },\n    removeAsset(row) {\n      this.form = row;\n\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          removeAsset([this.form.objId]).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getJgData();\n              //获取新的设备拓扑树\n              this.getNewTreeInfo();\n              // this.tableAndPageInfo3.pager.pageResize = \"Y\";\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    deleteBdz(row) {\n      this.form = row;\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(() => {\n        removeBdz([this.form.objId]).then(res => {\n          this.$message({\n            type: \"success\",\n            message: \"删除成功!\"\n          });\n          // this.tableAndPageInfo1.pager.pageResize = \"Y\";\n          this.getbdzData();\n        });\n      });\n    },\n    deleteJg(row) {\n      this.form = row;\n\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(() => {\n        removeJg([this.form.objId]).then(res => {\n          this.$message({\n            type: \"success\",\n            message: \"删除成功!\"\n          });\n          // this.tableAndPageInfo2.pager.pageResize = \"Y\";\n          this.getZnsbData();\n        });\n      });\n    },\n    async updateJg(row) {\n      this.jgDialogFormVisible = true;\n      this.jgxxForm = { ...row };\n      this.jgShow = false;\n    },\n\n    async jgDetails(row) {\n      this.jgDialogFormVisible = true;\n      this.jgxxForm = { ...row };\n      this.jgShow = true;\n    },\n\n    //每页展示数量点击事件\n    handleSizeChange() {},\n    //页码改变事件\n    handleCurrentChange() {},\n    //树点击事件\n    handleNodeClick(data, e) {\n      //根目录变电站列表\n      if (data.identifier == \"0\") {\n        //点击根节点，获取变电站数据\n        //间隔\n        this.isShow2 = false;\n        //重置pageNum\n        this.bdzqueryParams = {\n          pageNum: 1,\n          pageSize: 10\n        };\n        this.$refs.bdzTable.currentPage = 1;\n        //获取列表数据\n        this.getbdzData();\n        this.bdzdataShow = true;\n        this.znsbdataShow = this.jgdataShow = false;\n      }\n      //二级目录变电站名称\n      else if (data.identifier == \"1\") {\n        //点击变电站，获取间隔数据\n        this.currentBdz = data.id;\n        this.ssbdzmc = data.label;\n        this.ssbdz = data.id;\n        //重置页码\n        this.$refs.jgTable.currentPage = 1;\n        this.jgQueryParams.pageNum = 1;\n        this.jgQueryParams.jgdl = \"\"; //清空间隔大类\n        this.getFgsByBdzId();\n        this.getJgData({ ssbdz: data.id });\n        this.jgdataShow = true;\n        this.znsbdataShow = this.bdzdataShow = false;\n      } //二级目录变电站名称\n      else if (data.identifier == \"2\") {\n        //点击间隔大类，过滤间隔数据\n        //重新设置所属电站\n        this.currentBdz = data.ssbdz;\n        this.ssbdz = data.ssbdz;\n        //重置页码\n        this.$refs.jgTable.currentPage = 1;\n        this.jgQueryParams.pageNum = 1;\n        this.jgQueryParams.jgdl = data.id;\n        this.jglxcx = data.id;\n        this.getFgsByBdzId();\n        this.getJgData({ ssbdz: this.ssbdz, jgdl: data.id });\n        this.jgdataShow = true;\n        this.znsbdataShow = this.bdzdataShow = false;\n      } else if (data.identifier == \"3\") {\n        //点击间隔，获取站内设备\n        this.jglxcx = data.jglx;\n        this.sbxxForm.ssbdz = this.ssbdz;\n        //重置页码\n        this.$refs.znsbTable.currentPage = 1;\n        this.znsbParams.pageNum = 1;\n        this.bdzOptionsChangeClick();\n        this.ssjg = data.id;\n        this.sbxxForm.ssjg = this.ssjg;\n        //间隔\n        this.bdzqueryParams.ssbdz = \"\";\n        this.bdzqueryParams.ssjg = data.id;\n        this.getZnsbData();\n        this.znsbdataShow = true;\n        this.jgdataShow = this.bdzdataShow = false;\n      }\n    },\n    async getFgsByBdzId() {\n      let { data, code } = await getFgsByBdzId({ sbdm: this.currentBdz });\n      if (code === \"0000\") {\n        this.ssgs = data.value;\n        this.sbxxForm.ssgs = data.value;\n      }\n    },\n    //请求变电站数据\n    async getbdzData(param) {\n      this.bdzqueryParams = { ...this.bdzqueryParams, ...param };\n      const par = { ...this.bdzqueryParams, ...param };\n      if (this.treeForm.ssdwbm) {\n        par.ssdwbm = this.treeForm.ssdwbm;\n      }\n      await getBdzList(par).then(res => {\n        this.bdzdataShow = true;\n        this.jgdataShow = this.znsbdataShow = false;\n        this.tableAndPageInfo1.tableData = res.data.records;\n        this.tableAndPageInfo1.pager.total = res.data.total;\n      });\n    },\n    //日期格式化  yyyy-MM-dd\n    dateFormatter(d) {\n      let year = d.getFullYear();\n      let month =\n        d.getMonth() < 9 ? \"0\" + (d.getMonth() + 1) : \"\" + (d.getMonth() + 1);\n      let day = d.getDate() < 10 ? \"0\" + d.getDate() : \"\" + d.getDate();\n      return year + \"-\" + month + \"-\" + day;\n    },\n    //请求间隔数据\n    async getJgData(param) {\n      this.jgQueryParams = { ...this.jgQueryParams, ...param };\n      try {\n        let { data, code } = await getJgInfoList(this.jgQueryParams);\n        if (code === \"0000\") {\n          this.jgdataShow = true;\n          this.bdzdataShow = this.znsbdataShow = false;\n          this.tableAndPageInfo2.tableData = data.records;\n          this.tableAndPageInfo2.pager.total = data.total;\n        }\n      } catch (e) {}\n    },\n    //请求站内设备数据\n    async getZnsbData(params) {\n      try {\n        this.znsbParams = { ...this.znsbParams, ...params };\n        const param = this.znsbParams;\n        param.ssjg = this.ssjg;\n        const { data, code } = await getBdAsesetListPage(param);\n        if (code === \"0000\") {\n          this.znsbdataShow = true;\n          this.jgdataShow = this.bdzdataShow = false;\n          this.tableAndPageInfo3.tableData = data.records;\n          this.tableAndPageInfo3.pager.total = data.total;\n          // this.sbxxForm.ssgs=data.records[0].deptname\n          // this.sbxxForm.ssbdz=data.records[0].bdzmc\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //变电站弹框开始\n    //变电站修改按钮\n    updatebdz(row) {\n      this.$nextTick(async function() {\n        this.clearUpload();\n        this.jbxxForm = { ...row };\n        this.jbxxForm.attachment = [];\n        await this.getFileList();\n        this.isDisabled = false;\n        this.title = \"变电站台账修改\";\n        this.bdzDialogFormVisible = true;\n      });\n    },\n    clearUpload() {\n      if (this.$refs.upload) {\n        this.$refs.upload.clearFiles();\n      }\n    },\n    //关闭弹框\n    removeForm() {\n      this.jbxxForm = {\n        attachment: []\n      };\n      this.$nextTick(function() {\n        this.$refs[\"form\"].clearValidate();\n      });\n      this.bdzDialogFormVisible = false;\n    },\n    beforeUpload(file) {\n      const fileSize = file.size < 1024 * 1024 * 50;\n      if (!fileSize) {\n        this.$message.error(\"上传文件大小不能超过 50MB!\");\n      }\n    },\n    async getFileList() {\n      let { code, data } = await getListByBusinessId({\n        businessId: this.jbxxForm.objId\n      });\n      if (code === \"0000\") {\n        this.jbxxForm.attachment = data;\n        this.imgList = data.map(item => {\n          let item1 = {};\n          item1.name = item.fileName;\n          item1.url = this.$store.getters.currHost + item.fileUrl;\n          return item1;\n        });\n      }\n    },\n    //变电站详情方法\n    bdzDetails(row) {\n      this.$nextTick(async function() {\n        this.clearUpload();\n        this.jbxxForm = { ...row };\n        let temp = this.jbxxForm.ssdwmc;\n        this.jbxxForm.ssdwmc = this.jbxxForm.ssdwbm;\n        this.jbxxForm.ssdwbm = temp;\n        this.jbxxForm.attachment = [];\n        await this.getFileList();\n        this.bdzDialogFormVisible = true;\n        this.isDisabled = true;\n        this.title = \"变电站台账详情\";\n      });\n    },\n    addBdz() {\n      let params = {\n        lx: \"变电设备\",\n        ssdw: this.jbxxForm.ssdwbm,\n        mc: this.jbxxForm.bdzmc\n      };\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          addBdz(this.jbxxForm).then(res => {\n            if (res.code === \"0000\") {\n              //新增成功后发送通知\n              adddwzyfstz(params).then(res => {\n                if (res.code === \"0000\") {\n                }\n              });\n              this.uploadData.businessId = res.data.objId;\n              this.submitUpload();\n              this.bdzDialogFormVisible = false;\n              this.$message.success(\"操作成功,通知已分发\");\n              // this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getNewTreeInfo();\n              this.getData();\n            } else {\n              this.bdzDialogFormVisible = false;\n            }\n          });\n        } else {\n          return false;\n        }\n      });\n    },\n\n    submitUpload() {\n      this.$refs.upload.submit();\n    },\n    //变电站弹框结束\n    //新增按钮\n    AddButton() {\n      this.showFsss = false;\n      if (this.bdzdataShow) {\n        this.clearUpload();\n        this.imgList = [];\n        this.isDisabled = false;\n        this.bdzDialogFormVisible = true;\n        this.title = \"变电站台账新增\";\n      }\n      if (this.jgdataShow) {\n        this.jgxxForm = {};\n        this.fillBdz(); //设置间隔所属变电站\n        this.jgShow = false;\n        this.jgDialogFormVisible = true;\n      }\n      if (this.znsbdataShow) {\n        this.activeTabName = \"sbDesc\";\n        // this.sbxxForm = {};\n        if (this.singleClickData) {\n          this.sbxxForm = { ...this.singleClickData };\n          this.sbxxForm.objId = undefined;\n        }\n        this.sbxxForm.ssgs = this.ssgs;\n        this.sbxxForm.ssbdz = this.ssbdz;\n        this.sbxxForm.ssjg = this.ssjg;\n        //打开弹出框\n        this.znsbDialogForm = true;\n        //按钮和表单是否可编辑控制\n        this.assetIsDisable = false;\n      }\n    },\n    exportExcel() {\n      let fileName = this.ssbdzmc + \"设备信息表\";\n      let exportUrl = \"/bdsb/exportExcel/assetInfo\";\n      exportExcel(exportUrl, this.jgQueryParams, fileName);\n    },\n    exportExcelOfBdz() {\n      let fileName = \"变电站信息表\";\n      let exportUrl = \"/equipList/exportExcelOfBdz\";\n      exportExcel(exportUrl, this.jgQueryParams, fileName);\n    },\n    importExcelOfBdz() {\n      this.importExcelUrl = \"/manager-api/equipList/importExcelOfBdz\";\n      this.fileName = \"变电站信息表\";\n      this.$refs.importExcel.importExcel();\n    },\n    getUploadData(fileName) {\n      switch (fileName) {\n        case \"变电站信息表\":\n          this.getbdzData();\n          break;\n        case \"间隔信息表\":\n          this.getJgData();\n          break;\n        case \"设备信息表\":\n          this.getZnsbData();\n          break;\n        default:\n          break;\n      }\n      this.getNewTreeInfo();\n    },\n    exportExcelOfJg() {\n      let fileName = \"间隔信息表\";\n      let exportUrl = \"/equipList/exportExcelOfJg\";\n      exportExcel(exportUrl, this.jgQueryParams, fileName);\n    },\n    importExcelOfJg() {\n      this.importExtraInfo.ssbdz = this.currentBdz;\n      this.importExcelUrl = \"/manager-api/equipList/importExcelOfJg\";\n      this.fileName = \"间隔信息表\";\n      this.$refs.importExcel.importExcel();\n    },\n    exportExcelOfAsset() {\n      let fileName = \"设备信息表\";\n      let exportUrl = \"/equipList/exportExcelOfAsset\";\n      let param = {};\n      param.ssjg = this.ssjg;\n      exportExcel(exportUrl, param, fileName);\n    },\n    importExcelOfAsset() {\n      this.importExtraInfo.ssgs = this.ssgs;\n      this.importExtraInfo.ssbdz = this.ssbdz;\n      this.importExtraInfo.ssjg = this.ssjg;\n      this.importExcelUrl = \"/manager-api/equipList/importExcelOfAsset\";\n      this.fileName = \"设备信息表\";\n      this.$refs.importExcel.importExcel();\n    },\n    copyForAsset() {\n      this.$refs[\"formCopy\"].validate(valid => {\n        if (valid) {\n          this.formCopy.sourceSsjg = this.ssjg;\n          copyAsset(this.formCopy).then(res => {\n            if (res.code === \"0000\") {\n              this.$message.success(\"操作成功\");\n              this.isShowCopy = false;\n              this.$refs.formCopy.resetFields();\n            }\n          });\n        } else {\n          return false;\n        }\n      });\n    },\n    //根据设备类型获取设备型号list 数据\n    getSbxhBySblx(sblx) {\n      this.ggxhList = [];\n      getSbxhList({ dysblx: sblx }).then(res => {\n        if (res.code == \"0000\") {\n          // 保证每项都包含label字段\n          this.ggxhList = (res.data || []).map(item => {\n            return {\n              ...item,\n              label: item.label || item.lebel || item.xh || \"\"\n            };\n          });\n        }\n      });\n    },\n\n    /*站内设备开始*/\n    //设备修改操作\n    async updateAsset(row) {\n      row.xh = row.ggxh; //规格型号赋值\n      this.technicalParameters(row);\n      this.paramQuery.sblxbm = row.assetTypeCode;\n      await this.getParameters();\n      this.resumeQuery.foreignNum = row.sbmc;\n      this.resumeQuery.sblx = row.sblx;\n      this.bdsbid = row.objId;\n      this.sbmc = row.sbmc;\n      this.ssbdz = row.ssbdz;\n      this.getResumList();\n      await this.getSbxhBySblx(row.assetTypeCode); //根据设备类型获取设备型号下拉框\n      //给表单赋值\n      this.sbxxForm = { ...row };\n      //按钮和表单是否可编辑控制\n      this.assetIsDisable = false;\n      this.showFsss = true;\n      //打开设备弹出框\n      this.znsbDialogForm = true;\n    },\n    //点击新增，修改，详情时，重新获取对应的技术参数信息\n    technicalParameters(row) {\n      this.jscsForm = {};\n      this.paramQuery.sblxbm = row.assetTypeCode;\n      this.jscsForm.sblxbm = row.assetTypeCode;\n      this.jscsForm.sbbm = row.objId;\n      this.getParameters();\n    },\n    //获取技术参数对照信息，展示对应得技术参数label信息\n    async getParameters() {\n      this.jscsLabelList = [];\n      getParamDataList(this.paramQuery).then(res => {\n        this.jscsLabelList = res.data;\n        this.getParamValue();\n      });\n    },\n    //获取技术参数值信息\n    getParamValue() {\n      getParamsValue(this.jscsForm).then(res => {\n        if (res.data != \"\") {\n          this.jscsForm = { ...res.data[0] };\n        }\n      });\n    },\n    /**\n     * 设备履历\n     */\n    getResumList(par) {\n      let param = { ...par, ...this.resumeQuery };\n      getResumDataList(param).then(res => {\n        this.resumPageInfo.tableData = res.data.records;\n        this.resumPageInfo.pager.total = res.data.total;\n      });\n    },\n    //变电站下拉框中的change事件\n    bdzOptionsChangeClick() {\n      //当发生change事件时先清空之前的间隔信息\n      this.$set(this.sbxxForm, \"ssjg\", \"\");\n      //调用查询间隔方法\n      this.getJgDataListSelected();\n    },\n    //获取间隔下拉框数据\n    getJgDataListSelected() {\n      //给获取间隔下拉框查询参数赋值\n      this.selectJgOptionsParam.ssbdz = this.sbxxForm.ssbdz;\n      getJgDataListSelected(this.selectJgOptionsParam).then(res => {\n        this.jgOptionsDataList = res.data;\n      });\n    },\n    //设备类型change事件。获取技术参数信息\n    async showParams(data) {\n      this.paramQuery.sblxbm = data;\n      await this.getParameters();\n      await this.getSbxhBySblx(data); //根据设备类型获取设备型号下拉框\n    },\n    //设备履历tab页点击事件\n    async handleSbllDescTabNameClick(tab, event) {\n      switch (tab.name) {\n        case \"ztbgjl\":\n          await this.getResumList();\n          break;\n        case \"yh\":\n          await this.getYhList();\n          break;\n        case \"sy\":\n          await this.getSyList();\n          break;\n        case \"blgk\":\n          await this.getBlgkList();\n          break;\n        case \"qtsbwt\":\n          await this.getqtsbwtList();\n          break;\n        case \"jx\":\n          await this.getJxList();\n          break;\n        case \"jdbh\":\n          await this.getJdbhList();\n          break;\n        case \"tzjl\":\n          await this.getTzjlList();\n          break;\n      }\n    },\n    //保存设备信息\n    submit() {\n      this.assetSubmitLoading = true;\n      this.$refs[\"sbxxForm\"].validate(valid => {\n        if (valid) {\n          this.addAsset();\n        } else {\n          this.assetSubmitLoading = false;\n          this.$message.error(\"校验未通过！\");\n          return false;\n        }\n      });\n    },\n    //站内设备弹框关闭\n    //清空表单\n    resetForm1() {\n      this.sbxxForm = this.$options.data().form;\n      this.jscsForm = this.$options.data().form;\n      this.$nextTick(function() {\n        this.$refs[\"sbxxForm\"].clearValidate();\n      });\n      this.sbllDescTabName = \"ztbgjl\";\n      this.znsbDialogForm = false;\n    },\n    /**\n     * 添加设备保存基本信息\n     */\n    addAsset() {\n      this.sbxxForm.sbClassCsValue = this.jscsForm;\n      addAsset(this.sbxxForm).then(res => {\n        if (res.code == \"0000\") {\n          this.$message({\n            type: \"success\",\n            message: \"操作成功!\"\n          });\n          this.znsbDialogForm = false;\n          // this.tableAndPageInfo3.pager.pageResize = \"Y\";\n          this.getZnsbData();\n        }\n        this.assetSubmitLoading = false;\n      });\n    },\n    //设备详情操作\n    async assetDetails(row) {\n      row.xh = row.ggxh; //规格型号赋值\n      this.technicalParameters(row);\n      this.resumeQuery.foreignNum = row.sbmc;\n      this.resumeQuery.sblx = row.sblx;\n      this.bdsbid = row.objId;\n      this.sbmc = row.sbmc;\n      this.ssbdz = row.ssbdz;\n      this.getResumList();\n      await this.getSbxhBySblx(row.assetTypeCode); //根据设备类型获取设备型号下拉框\n      //给表单赋值\n      this.sbxxForm = { ...row };\n      this.jgOptionsDataList = [\n        { label: this.sbxxForm.wzmc, value: this.sbxxForm.ssjg }\n      ];\n      //按钮和表单是否可编辑控制\n      this.assetIsDisable = true;\n      this.showFsss = true;\n      //打开设备弹出框\n      this.znsbDialogForm = true;\n    },\n    //获取变电站下拉框数据\n    getBdzDataListSelected() {\n      getBdzDataListSelected({}).then(res => {\n        this.bdzOptionsDataList = res.data;\n      });\n    },\n    resetForm() {\n      this.jgDialogFormVisible = false;\n      this.$refs.jgxxForm.resetFields();\n    },\n    //筛选条件重置\n    filterReset() {\n      // 重置变电站查询参数（保留树形选择的基本参数）\n      if (this.bdzdataShow) {\n        this.bdzqueryParams = {\n          pageNum: 1,\n          pageSize: 10\n        };\n      }\n\n      // 重置间隔查询参数（保留所属变电站和间隔大类）\n      if (this.jgdataShow) {\n        const ssbdz = this.jgQueryParams.ssbdz;\n        const jgdl = this.jgQueryParams.jgdl;\n        this.jgQueryParams = {\n          ssbdz: ssbdz,\n          jgdl: jgdl,\n          dydj: undefined,\n          pageSize: 10,\n          pageNum: 1\n        };\n      }\n\n      // 重置站内设备查询参数（保留所属间隔）\n      if (this.znsbdataShow) {\n        this.znsbParams = {\n          pageSize: 10,\n          pageNum: 1\n        };\n        // 保留间隔相关的查询条件\n        this.bdzqueryParams = {\n          ssbdz: \"\",\n          ssjg: this.ssjg,\n          pageNum: 1,\n          pageSize: 10\n        };\n      }\n\n      // 重置筛选条件的数据和复选框\n      this.filterInfo1.data = {\n        ywdwArr: [],\n        jhnyArr: [],\n        xlArr: \"\",\n        jhlxArr: [],\n        jhztArr: \"\",\n        sfdd: \"\"\n      };\n      this.filterInfo1.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n\n      this.filterInfo2.data = {\n        ywdwArr: [],\n        jhnyArr: [],\n        xlArr: \"\",\n        jhlxArr: [],\n        jhztArr: \"\",\n        sfdd: \"\"\n      };\n      this.filterInfo2.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n\n      this.filterInfo3.data = {\n        ywdwArr: [],\n        jhnyArr: [],\n        xlArr: \"\",\n        jhlxArr: [],\n        jhztArr: \"\",\n        sfdd: \"\"\n      };\n      this.filterInfo3.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n\n      // 重置表格页码\n      if (this.bdzdataShow && this.$refs.bdzTable) {\n        this.$refs.bdzTable.currentPage = 1;\n      }\n      if (this.jgdataShow && this.$refs.jgTable) {\n        this.$refs.jgTable.currentPage = 1;\n      }\n      if (this.znsbdataShow && this.$refs.znsbTable) {\n        this.$refs.znsbTable.currentPage = 1;\n      }\n\n      // 根据当前显示的表格重新加载数据（保持当前的树形选择状态）\n      if (this.bdzdataShow) {\n        this.getbdzData();\n      } else if (this.jgdataShow) {\n        this.getJgData();\n      } else if (this.znsbdataShow) {\n        this.getZnsbData();\n      }\n    },\n    async querySearchAsync(queryString, cb) {\n      const results = queryString\n        ? this.ggxhList.filter(\n            item =>\n              item.label.toLowerCase().indexOf(queryString.toLowerCase()) !== -1\n          )\n        : this.ggxhList;\n      // 确保每个选项都包含 label 和 value 字段\n      const formattedResults = results.map(item => ({\n        label: item.label,\n        value: item.value || item.label // 如果没有 value，使用 label 作为 value\n      }));\n      console.log(\"results:\", formattedResults);\n      cb(formattedResults);\n    },\n    handleSelect(item) {\n      this.isSelectingFromDropdown = true; // 设置标记\n      this.sbxxForm.xh = item.label;\n    },\n    async handleChange(value) {\n      // 如果是从下拉列表选择的，不执行新增操作\n      setTimeout(() => {\n        if (this.isSelectingFromDropdown) {\n          this.isSelectingFromDropdown = false; // 重置标记\n          return;\n        }\n        // 如果输入的值不在选项中，则添加到型号库\n      if (value && !this.ggxhList.some(item => item.label === value)) {\n        try {\n          const params = {\n            sbxh: value,\n            dysblx: this.sbxxForm.assetTypeCode // 从表单中获取设备类型\n          }\n          addSbxh(params).then(res => {\n            if (res.code === '0000') {\n              this.ggxhList.push({\n                label: value,\n                value: value // 添加 value 字段\n              })\n              this.$message.success('已添加到型号库')\n            } else {\n              this.$message.warning(res.msg || '添加型号失败')\n            }\n          })\n        } catch (error) {\n          console.warning('添加型号失败:', error)\n          this.$message.warning('添加型号失败')\n        }\n      }\n      }, 500);\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.imgCls {\n  height: 150px !important;\n}\n\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  height: 70.6vh;\n  overflow: auto;\n}\n\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.box-cardList {\n  height: 70%;\n}\n\n.item {\n  width: 200px;\n  float: left;\n}\n\n#main_container_dj {\n  height: calc(100vh - 84px);\n}\n\n.aside_height {\n  height: 96%;\n}\n\n.defect .el-form-item:nth-child(odd) {\n  margin-right: 70px;\n}\n\n/*背景颜色调整*/\n#main_container_dj,\n#main_container_dj .el-aside {\n  background-color: #b4caf1;\n}\n\n///deep/ .qxlr_dialog_insert .el-dialog__header {\n//  background-color: #8eb3f5;\n//}\n//\n///deep/ .pmyBtn {\n//  background: #8eb3f5;\n//}\n\n/*/deep/ .add_sy_tyrq .el-input--medium .el-input__inner {*/\n/*  width: 200px;*/\n/*}*/\n\n/*添加弹出框得宽度*/\n/*/deep/ .el-input--medium .el-input__inner {*/\n/*  width: 200px;*/\n/*}*/\n.el-select {\n  width: 100%;\n}\n\n.el-date-editor {\n  width: 100%;\n}\n\n.el-carousel__item h3 {\n  color: #475669;\n  font-size: 14px;\n  opacity: 0.75;\n  line-height: 150px;\n}\n\n.el-carousel__item:nth-child(2n) {\n  background-color: #99a9bf;\n}\n\n.el-carousel__item:nth-child(2n + 1) {\n  background-color: #d3dce6;\n}\n\n/* 设置滚动条的样式 */\n::-webkit-scrollbar {\n  width: 12px;\n}\n\n/* 滚动槽 */\n::-webkit-scrollbar-track {\n  //-webkit-box-shadow:inset006pxrgba(0,0,0,0.3);\n  border-radius: 10px;\n}\n\n/* 滚动条滑块 */\n::-webkit-scrollbar-thumb {\n  border-radius: 10px;\n  background: rgba(0, 0, 0, 0.1);\n  //-webkit-box-shadow:gba(0,0,0,0.5);\n}\n\n::-webkit-scrollbar-thumb:window-inactive {\n  background: rgba(0, 0, 0, 0.1);\n}\n\n[data-v-67a974b1]::-webkit-scrollbar {\n  width: 8px;\n}\n\n.item {\n  width: 225px;\n  float: left;\n}\n\n/deep/ .box-card {\n  margin: 0 6px;\n}\n\n//有子节点 且未展开\n.el-tree ::v-deep .el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/add.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n\n//有子节点 且已展开\n.el-tree\n  ::v-deep\n  .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/prep.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n\n//没有子节点\n.el-tree ::v-deep .el-tree-node__expand-icon.is-leaf::before {\n  background: transparent;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n\n.categoryTreeIcons {\n  background: url(\"../../../../assets/icons/icon/icon1.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n\n.tableIcon {\n  background: url(\"../../../../assets/icons/icon/icon2.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n\n.classIcon {\n  background: url(\"../../../../assets/icons/icon/icon3.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n</style>\n<style>\n#pic_form .el-form-item__content {\n  margin-left: 0 !important;\n}\n\n#imgId .el-carousel__container {\n  background-color: #ffffff !important;\n}\n</style>\n"]}]}