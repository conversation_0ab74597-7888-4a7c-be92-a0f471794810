{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\activiti_tdsqd\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\activiti_tdsqd\\index.vue", "mtime": 1720689385854}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AA6EA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBACA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,KAAA,EAAA;AACA;;;;;;;;;;;;AAYA,IAAA,WAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAFA,sBAEA;AACA,eAAA;AACA,UAAA,WAAA,EAAA,IADA;AAEA,UAAA,WAAA,EAAA;AAFA,SAAA;AAIA;AAPA,KAbA;AAsBA;AACA,IAAA,MAAA,EAAA;AACA,MAAA,IAAA,EAAA,OADA;AAEA,MAAA,OAAA,EAAA;AAFA,KAvBA;AA2BA;AACA,IAAA,MAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAFA,sBAEA;AACA,eAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAAA;AACA;AAJA;AA5BA,GAFA;AAqCA,EAAA,IArCA,kBAqCA;AACA,WAAA;AACA,MAAA,QAAA,EAAA,KADA;AAEA,MAAA,IAAA,EAAA,EAFA;AAGA,MAAA,KAAA,EAAA,EAHA;AAIA,MAAA,OAAA,EAAA,IAJA;AAKA,MAAA,SAAA,EAAA;AALA,KAAA;AAOA,GA7CA;AA8CA,EAAA,KAAA,EAAA;AACA,IAAA,WAAA,EAAA;AACA,MAAA,OADA,mBACA,MADA,EACA,MADA,EACA;AAAA;;AACA,aAAA,SAAA,CAAA,YAAA;AACA,UAAA,KAAA,CAAA,KAAA,mCAAA,MAAA,EADA,CAEA;;AACA,cAAA,KAAA,CAAA,KAAA,CAAA,WAAA,KAAA,iBAAA,EAAA;AACA,qCAAA;AACA,cAAA,aAAA,EAAA,KAAA,CAAA,KAAA,CAAA,SAAA,CAAA,aADA;AAEA,cAAA,MAAA,EAAA,KAAA,CAAA,KAAA,CAAA,SAAA,CAAA,MAAA,GACA,KAAA,CAAA,KAAA,CAAA,SAAA,CAAA,MADA,GAEA,CAJA;AAKA,cAAA,QAAA,EAAA,KAAA,CAAA,KAAA,CAAA,SAAA,CAAA,QAAA,GACA,KAAA,CAAA,KAAA,CAAA,SAAA,CAAA,QADA,GAEA;AAPA,aAAA,EAQA,IARA,CAQA,UAAA,GAAA,EAAA;AACA,cAAA,KAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA;AACA,aAVA;AAWA;AACA,SAhBA;AAiBA,OAnBA;AAoBA,MAAA,IAAA,EAAA,IApBA;AAqBA,MAAA,SAAA,EAAA;AArBA;AADA,GA9CA;AAuEA,EAAA,OAvEA,qBAuEA,CAAA,CAvEA;AAwEA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,UAFA,wBAEA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,SAAA,GAAA,MAAA,CAAA,MAAA,CAAA,IAAA;;AACA,oBAAA,MAAA,CAAA,IAAA,CAAA,QAAA,EAAA;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,QAAA,GAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,2BAAA,IAAA,CAAA,QAAA;AAAA,mBAAA,EAAA,IAAA,CAAA,GAAA,CAAA;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,gBAAA,GAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,2BAAA,IAAA,CAAA,QAAA;AAAA,mBAAA,EAAA,IAAA,CAAA,GAAA,CAAA;AACA,iBAHA,MAGA;AACA,sBAAA,MAAA,CAAA,WAAA,CAAA,YAAA,EAAA;AACA;AACA,oBAAA,MAAA,CAAA,KAAA,CAAA,QAAA,GAAA,MAAA,CAAA,WAAA,CAAA,YAAA,CAAA,QAAA;AACA,oBAAA,MAAA,CAAA,KAAA,CAAA,gBAAA,GAAA,MAAA,CAAA,WAAA,CAAA,YAAA,CAAA,QAAA;AACA,mBAJA,MAIA;AACA,oBAAA,MAAA,CAAA,KAAA,CAAA,QAAA,GAAA,SAAA;AACA,oBAAA,MAAA,CAAA,KAAA,CAAA,gBAAA,GAAA,SAAA;AACA;AACA;;AAdA,sBAgBA,CAAA,MAAA,CAAA,KAAA,CAAA,QAAA,IACA,MAAA,CAAA,KAAA,CAAA,WAAA,KAAA,iBADA,IAEA,MAAA,CAAA,KAAA,CAAA,WAlBA;AAAA;AAAA;AAAA;;AAoBA,gBAAA,MAAA,CAAA,QAAA,CAAA;AACA,kBAAA,IAAA,EAAA,OADA;AAEA,kBAAA,OAAA,EAAA;AAFA,iBAAA;;AApBA;;AAAA;AA0BA,oBAAA,MAAA,CAAA,IAAA,CAAA,OAAA,EAAA;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,SAAA,CAAA,OAAA,GAAA,MAAA,CAAA,IAAA,CAAA,OAAA;AACA;;AACA,gBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,kBAAA,MAAA,CAAA,OAAA,GAAA,mBAAA,OAAA,CAAA;AACA,oBAAA,IAAA,EAAA,IADA;AACA;AACA,oBAAA,IAAA,EAAA,WAFA;AAEA;AACA,oBAAA,OAAA,EAAA,iBAHA;AAGA;AACA,oBAAA,UAAA,EAAA,oBAJA;AAIA;AACA,oBAAA,MAAA,EAAA,QAAA,CAAA,aAAA,CAAA,YAAA;AALA,mBAAA,CAAA;AAOA,iBATA;;AA7BA;AAAA;AAAA,uBA0CA,oCAAA,MAAA,CAAA,KAAA,CA1CA;;AAAA;AAAA;AA0CA,gBAAA,IA1CA,yBA0CA,IA1CA;AA0CA,gBAAA,IA1CA,yBA0CA,IA1CA;;AA2CA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,UAAA,GAAA,IAAA;AACA;;AACA,oBAAA,IAAA,EAAA;AACA,kBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,oBAAA,MAAA,CAAA,OAAA,CAAA,KAAA;AACA,mBAHA;AAIA;;AAnDA;AAAA;;AAAA;AAAA;AAAA;;AAqDA,gBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,kBAAA,MAAA,CAAA,OAAA,CAAA,KAAA;AACA,iBAHA;;AArDA;AA0DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAA,UAAA,EAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,UAAA;AACA,kBAAA,UAAA,CAAA,WAAA,GAAA,MAAA,CAAA,KAAA,CAAA,WAAA;;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,UAAA,EAAA,UAAA;AACA;;AACA,gBAAA,MAAA,CAAA,OAAA;;AAjFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkFA,KApFA;AAqFA,IAAA,OArFA,qBAqFA;AACA,WAAA,IAAA,CAAA,KAAA,IAAA,EAAA,UAAA,EAAA,EAAA;AACA,WAAA,IAAA,CAAA,KAAA,IAAA,EAAA,SAAA,EAAA,EAAA;AACA,WAAA,KAAA,CAAA,SAAA,EAAA,OAAA;AACA;AAzFA;AAxEA,C", "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      id=\"dialogAct\"\n      class=\"tree\"\n      :title=\"option.title ? option.title : ''\"\n      :top=\"option.top ? option.top : '10vh'\"\n      v-dialogDrag\n      :visible.sync=\"isShow\"\n      :width=\"option.width ? option.width : '25%'\"\n      :close-on-click-modal=\"false\"\n      @close=\"toClose\"\n      :destroy-on-colse=\"true\"\n      :modal-append-to-body=\"false\"\n    >\n      <slot name=\"Dialog_content\" />\n      <el-form\n        label-width=\"120px\"\n        ref=\"form\"\n        :model=\"form\"\n        v-if=\"datas.defaultFrom ? true : false\"\n      >\n        <div>\n          <el-row>\n            <!--    根据人员组选人   -->\n            <div v-if=\"datas.processType === 'completeByGroup'\">\n              <el-col :span=\"24\" v-if=\"datas.nextUser ? datas.nextUser : true\">\n                <el-form-item prop=\"nextUser\" label=\"处理人选择：\">\n                  <el-select\n                    v-model=\"form.nextUser\"\n                    placeholder=\"请选择人员\"\n                    filterable\n                    multiple\n                    style=\"width: 100%;\"\n                    value-key=\"userName\"\n                  >\n                    <el-option\n                      v-for=\"item in ryOptions\"\n                      :key=\"item.userName\"\n                      :label=\"item.nickName\"\n                      :value=\"{\n                        userName: item.userName,\n                        nickName: item.nickName\n                      }\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </div>\n            <el-col :span=\"24\">\n              <el-form-item\n                prop=\"comment\"\n                label=\"回退原因：\"\n                v-if=\"datas.processType === 'rollback'\"\n              >\n                <el-input\n                  style=\"width: 100%\"\n                  v-model=\"form.comment\"\n                  type=\"textarea\"\n                  placeholder=\"请输入原因\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n      </el-form>\n      <span v-else>请确定是否提交</span>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"info\" @click=\"toClose\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"todoSubmit('form')\">确定</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { completeYjrwdTask } from \"@/api/activiti/processTask\";\nimport { Loading } from \"element-ui\";\nimport { getUsers } from \"@/api/system/usergroup\";\nexport default {\n  name: \"index\",\n  props: {\n    /*流程发起必填数据\n        processData:{\n          processDefinitionKey:\"流程定义的key\",//必填\n          taskId:\"任务id，如果任务在代办列表时，会根据代办列表获取taskid，否则需传入业务id来确定task\",\n          businessKey:\"业务id，对应业务的主键\",//taskid和businessKey两者必须有其中一个才可以确定一个task\n          businessType:\"业务类型，用于区分不同的业务\"//必填\n          variables:\"拓展参数\"//流程定义中设置的参数,\n          nextUser:\"如果流程实例中并未配置每一个节点的处理人，则需要用户手动选择每一个节点的处理人\",\n          nextUserInfo:{},//手动传的审核人信息\n          processType:'complete,rollback',\n          defaultFrom:true,false 是否需要默认表单\n        }*/\n    processData: {\n      type: Object,\n      default() {\n        return {\n          defaultFrom: true,\n          processType: \"complete\"\n        };\n      }\n    },\n    //显示隐藏\n    isShow: {\n      type: Boolean,\n      default: false\n    },\n    //子组件默认参数\n    option: {\n      type: Object,\n      default() {\n        return { title: \"审批\" };\n      }\n    }\n  },\n  data() {\n    return {\n      disabled: false,\n      form: {},\n      datas: {},\n      loading: null,\n      ryOptions: []\n    };\n  },\n  watch: {\n    processData: {\n      handler(newVal, oldVal) {\n        this.$nextTick(() => {\n          this.datas = { ...newVal };\n          //如果是需要从人员组中选人的，则从人员组查人\n          if (this.datas.processType === \"completeByGroup\") {\n            getUsers({\n              personGroupId: this.datas.variables.personGroupId,\n              deptId: this.datas.variables.deptId\n                ? this.datas.variables.deptId\n                : 0,\n              deptName: this.datas.variables.deptName\n                ? this.datas.variables.deptName\n                : \"\"\n            }).then(res => {\n              this.ryOptions = res.data;\n            });\n          }\n        });\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  mounted() {},\n  methods: {\n    //流程提交，流程拒绝\n    async todoSubmit() {\n      this.datas.routePath = this.$route.path;\n      if (this.form.nextUser) {\n        this.datas.nextUser = this.form.nextUser.map(item => item.userName).join(\",\")\n        this.datas.nextUserNickName = this.form.nextUser.map(item => item.nickName).join(\",\")\n      } else {\n        if (this.processData.nextUserInfo) {\n          //手动传的用户信息\n          this.datas.nextUser = this.processData.nextUserInfo.userName;\n          this.datas.nextUserNickName = this.processData.nextUserInfo.nickName;\n        } else {\n          this.datas.nextUser = undefined;\n          this.datas.nextUserNickName = undefined;\n        }\n      }\n      if (\n        !this.datas.nextUser &&\n        this.datas.processType === \"completeByGroup\" &&\n        this.datas.defaultFrom\n      ) {\n        this.$message({\n          type: \"error\",\n          message: \"请选择人员!\"\n        });\n        return;\n      }\n      if (this.form.comment) {\n        this.datas.variables.comment = this.form.comment;\n      }\n      this.$nextTick(() => {\n        // 以服务的方式调用的 Loading 需要异步关闭\n        this.loading = Loading.service({\n          lock: true, //lock的修改符--默认是false\n          text: \"流程进行中，请稍后\", //显示在加载图标下方的加载文案\n          spinner: \"el-icon-loading\", //自定义加载图标类名\n          background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n          target: document.querySelector(\"#dialogAct\")\n        });\n      });\n      let resultData;\n      //if(this.datas.processType==='complete'){\n      try {\n        let { code, data } = await completeYjrwdTask(this.datas);\n        if (code === \"0000\") {\n          resultData = data;\n        }\n        if (code) {\n          this.$nextTick(() => {\n            // 以服务的方式调用的 Loading 需要异步关闭\n            this.loading.close();\n          });\n        }\n      } catch (e) {\n        this.$nextTick(() => {\n          // 以服务的方式调用的 Loading 需要异步关闭\n          this.loading.close();\n        });\n      }\n      // }else{\n      //   try {\n      //     console.log(this.datas)\n      //     let {code,data} =await rollbackTask(this.datas)\n      //     if(code==='0000'){\n      //       resultData=data\n      //     }\n      //     if(code){\n      //       this.$nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭\n      //         this.loading.close();\n      //       });\n      //     }\n      //   }catch (e) {\n      //     this.$nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭\n      //       this.loading.close();\n      //     });\n      //   }\n      // }\n      if (resultData) {\n        console.log(resultData);\n        resultData.processType = this.datas.processType;\n        this.$emit(\"todoData\", resultData);\n      }\n      this.toClose();\n    },\n    toClose() {\n      this.$set(this.form,'nextUser',\"\")\n      this.$set(this.form,'comment',\"\")\n      this.$emit(\"toClose\", \"close\");\n    }\n  }\n};\n</script>\n\n<style scoped></style>\n"], "sourceRoot": "src/components/activiti_tdsqd"}]}