{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\symbwhDymbnr.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\symbwhDymbnr.vue", "mtime": 1706897323686}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["symbwhDymbnr.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoFA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "symbwhDymbnr.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk", "sourcesContent": ["<template>\n  <!--列表新增关联项目弹窗调用-->\n  <div>\n    <el-row :gutter=\"3\">\n      <div class=\"mb8 pull-right\">\n        <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"mini\" @click=\"addBwToxmbw\" :disabled=\"addBwDisabled\">新增部位\n        </el-button>\n        <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"mini\" @click=\"deleteBwToxmbw\" :disabled=\"deleteBwDisabled\">\n          删除部位\n        </el-button>\n      </div>\n    </el-row>\n    <el-row :gutter=\"20\">\n      <el-col :span=\"12\">\n        <el-table :data=\"mbGlxmDataList\" @row-click=\"handleMbGlxmRowClick\"\n                  @selection-change=\"handleMbGlxmSelectedChange\">\n          <el-table-column label=\"试验项目\" align=\"center\">\n            <el-table-column type=\"selection\" align=\"center\"/>\n            <el-table-column prop=\"syxmmc\" label=\"项目名称\" width=\"180\" align=\"center\"></el-table-column>\n            <el-table-column prop=\"syxmms\" label=\"项目描述\" align=\"center\" :show-overflow-tooltip=\"true\"></el-table-column>\n          </el-table-column>\n        </el-table>\n        <pagination\n          :total=\"glxmTotal\"\n          :page.sync=\"glxmQueryParams.pageNum\"\n          :limit.sync=\"glxmQueryParams.pageSize\"\n          @pagination=\"getSymbGlsyxmDataListByPage\"\n        />\n      </el-col>\n      <el-col :span=\"12\">\n        <el-table :data=\"bwGlxmData\" @selection-change=\"handleXmBwSelectedChange\">\n          <el-table-column label=\"试验部位\" align=\"center\">\n            <el-table-column type=\"selection\" align=\"center\"/>\n            <el-table-column prop=\"sybw\" label=\"部位名称\" align=\"center\"></el-table-column>\n          </el-table-column>\n        </el-table>\n        <pagination\n          :total=\"bwglXmTotal\"\n          :page.sync=\"bwGlxmQueryParam.pageNum\"\n          :limit.sync=\"bwGlxmQueryParam.pageSize\"\n          @pagination=\"getXmBwDataByPage\"\n        />\n      </el-col>\n    </el-row>\n\n    <!--新增试验部位弹出框-->\n    <el-dialog title=\"部位库\" :visible.sync=\"isShowAddSybwDialog\" append-to-body width=\"50%\" v-dialogDrag>\n      <el-form label-width=\"120px\">\n        <el-row :gutter=\"3\">\n          <el-col :span=\"10\">\n            <el-form-item label=\"部位名称：\">\n              <el-input v-model=\"bwLibraryQueryForm.sybw\"/>\n            </el-form-item>\n          </el-col>\n          <div class=\"mb8 pull-right\">\n            <el-button type=\"cyan\" size=\"mini\" icon=\"el-icon-search\" @click=\"selectBwLibrary\">查询</el-button>\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetBwSearch\">重置</el-button>\n          </div>\n        </el-row>\n      </el-form>\n      <el-table stripe border :data=\"bwLibraryDataList\" @selection-change=\"handleSelectedBwLibraryChange\"\n                :header-cell-style=\"{'text-align':'center'}\" :cell-style=\"{'text-align':'center'}\">\n        <el-table-column type=\"selection\" width=\"55\"/>\n        <el-table-column label=\"序号\" type=\"index\" width=\"50\">\n        </el-table-column>\n        <el-table-column label=\"部位名称\" prop=\"sybw\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"备注\" prop=\"bz\" :show-overflow-tooltip=\"true\"/>\n      </el-table>\n      <pagination\n        v-show=\"bwLibraryTotal>0\"\n        :total=\"bwLibraryTotal\"\n        :page.sync=\"bwLibraryQueryForm.pageNum\"\n        :limit.sync=\"bwLibraryQueryForm.pageSize\"\n        @pagination=\"getBwLibraryBySblxAndSyxmid\"\n      />\n      <div slot=\"footer\">\n        <el-button @click=\"closeAddBwToXmbwDialog\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"commitAddBwToXmbwDialog\">确 认</el-button>\n      </div>\n    </el-dialog>\n  </div>\n\n</template>\n<script>\n  import {\n    getSymbGlsyxmDataListByPage,\n    getXmBwDataByPage,\n    getBwLibraryBySblxAndSyxmid,\n    addBatchBwToSyxm,\n    removeXmBw\n  } from '@/api/dagangOilfield/bzgl/symbwh'\n\n  export default {\n    name: \"symbwhDymbnr\",\n    props: {\n      mbData: {\n        type: Object\n      }\n    },\n    data() {\n      return {\n        //部位库选中数据参数\n        bwLibraryDataForm: {\n          //试验项目id\n          syxmid: \"\",\n          //选中的部位库数据集合\n          bwLibraryDataRows: []\n        },\n\n        //部位库查询参数\n        bwLibraryQueryForm: {\n          sybw: \"\",\n          syxmid: undefined,\n          sblxbm: undefined,\n          pageNum: 1,\n          pageSize: 10\n        },\n        //部位库数据总数\n        bwLibraryTotal: 0,\n        //部位库数据\n        bwLibraryDataList: [],\n        //新增试验部位弹出框控制\n        isShowAddSybwDialog: false,\n        //新增部位按钮控制\n        addBwDisabled: true,\n        //删除部位按钮控制\n        deleteBwDisabled: false,\n        //试验项目数据集合\n        mbGlxmDataList: [],\n        //关联项目总数\n        glxmTotal: 0,\n        //关联项目查询参数\n        glxmQueryParams: {\n          symbid: undefined,\n          pageNum: 1,\n          pageSize: 10\n        },\n        //项目关联部位数据\n        bwGlxmData: [],\n        //关联部位总数\n        bwglXmTotal: 0,\n        //部位查询参数\n        bwGlxmQueryParam: {\n          syxmid: undefined,\n          pageNum: 1,\n          pageSize: 10\n        },\n        //已关联的试验部位复选框选中数据\n        xmGlBwRows: []\n\n\n      };\n    },\n    watch: {},\n    created() {\n\n      //获取试验模板关联项目列表\n      this.getSymbGlsyxmDataListByPage();\n    },\n    methods: {\n      //获取关联项目弹出框数据\n      getSymbGlsyxmDataListByPage() {\n        this.glxmQueryParams.symbid = this.mbData.objId;\n        //设备类型赋值\n        this.bwLibraryQueryForm.sblxbm = this.mbData.sblxid;\n        getSymbGlsyxmDataListByPage(this.glxmQueryParams).then(res => {\n          this.mbGlxmDataList = res.data.records;\n          this.glxmTotal = res.data.total;\n        })\n      },\n      //试验项目行点击事件\n      handleMbGlxmRowClick(row) {\n        this.bwGlxmQueryParam.syxmid = row.syxmid;\n        this.getXmBwDataByPage();\n      },\n      //试验项目左侧复选框\n      handleMbGlxmSelectedChange(rows) {\n        this.addBwDisabled = rows.length != 1;\n        console.log(\"点击左侧复选框了\")\n        console.log(rows)\n        //给查询部位库赋值\n        this.bwLibraryQueryForm.syxmid = rows[0].syxmid;\n        //新增部位库时赋值\n        this.bwLibraryDataForm.syxmid = rows[0].syxmid;\n      },\n      //根据项目id获取试验部位数据方法\n      getXmBwDataByPage() {\n        getXmBwDataByPage(this.bwGlxmQueryParam).then(res => {\n          this.bwGlxmData = res.data.records;\n          this.bwglXmTotal = res.data.total;\n        })\n      },\n      //新增部位按钮\n      addBwToxmbw() {\n        //打开新增部位弹出框\n        this.isShowAddSybwDialog = true;\n        this.getBwLibraryBySblxAndSyxmid();\n      },\n      //获取部位库数据\n      getBwLibraryBySblxAndSyxmid() {\n        getBwLibraryBySblxAndSyxmid(this.bwLibraryQueryForm).then(res => {\n          console.log(\"部位库数据\")\n          console.log(res);\n          this.bwLibraryDataList = res.data.records;\n          this.bwLibraryTotal = res.data.total;\n        })\n      },\n      //部位库查询按钮\n      selectBwLibrary() {\n        this.getBwLibraryBySblxAndSyxmid();\n      },\n      //部位库重置按钮\n      resetBwSearch() {\n        this.bwLibraryQueryForm.sybw = \"\";\n        this.getBwLibraryBySblxAndSyxmid();\n      },\n      //部位库复选框选中事件\n      handleSelectedBwLibraryChange(rows) {\n        this.bwLibraryDataForm.bwLibraryDataRows = rows;\n\n      },\n      //部位库弹出框取消\n      closeAddBwToXmbwDialog() {\n        this.isShowAddSybwDialog = false;\n      },\n      //部位库弹出框确定\n      commitAddBwToXmbwDialog() {\n        if (this.bwLibraryDataForm.bwLibraryDataRows.length < 1) {\n          this.$message.info('未关联部位！！！已取消')\n          //如果未选中数据,则直接关闭弹窗\n          this.isShowAddSybwDialog = false\n        } else {\n          console.log(this.bwLibraryDataForm)\n          //若选择数据后\n          addBatchBwToSyxm(this.bwLibraryDataForm).then(res => {\n            if (res.code === '0000') {\n              this.$message.success('关联成功')\n            } else {\n              this.$message.error('关联失败！！')\n            }\n            //关闭弹窗\n            this.isShowAddSybwDialog = false\n            //调用获取部位列表\n            this.getXmBwDataByPage()\n          })\n        }\n      },\n      //已关联的试验部位左侧选中按钮\n      handleXmBwSelectedChange(rows) {\n        this.xmGlBwRows = rows;\n      },\n      //删除部位\n      deleteBwToxmbw() {\n        if (this.xmGlBwRows.length < 1) {\n          this.$message.warning('请选择要删除的试验部位！！！')\n          return\n        }\n        let ids = this.xmGlBwRows.map(item => {\n          return item.objId\n        })\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          removeXmBw(ids).then(({code}) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getXmBwDataByPage()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n              this.getXmBwDataByPage()\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n    }\n  };\n</script>\n\n<style lang='scss' scoped>118\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  max-height: 79vh;\n  overflow: auto;\n}\n</style>\n"]}]}