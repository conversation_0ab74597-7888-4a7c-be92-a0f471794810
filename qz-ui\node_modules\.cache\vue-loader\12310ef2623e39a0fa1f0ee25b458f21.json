{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\lpbzk\\czpzysyk.vue?vue&type=style&index=0&id=74ee78bc&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\lpbzk\\czpzysyk.vue", "mtime": 1706897322614}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5ib3gtY2FyZHsKICBtYXJnaW4tYm90dG9tOiAxNXB4OwogIC5lbC1jYXJkX19oZWFkZXJ7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjM1LCAyNDUsIDI1NSkgIWltcG9ydGFudDsKICB9Cn0KLml0ZW17CiAgd2lkdGg6IDIwMHB4O2hlaWdodDogMTQ4cHg7IGZsb2F0OiBsZWZ0Owp9Cg=="}, {"version": 3, "sources": ["czpzysyk.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiVA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "czpzysyk.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/lpbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n        />\n    <div>\n      <el-white class=\"button-group\">\n        <div class=\"button_btn\">\n          <el-button @click=\"addSensorButton\"\n                     type=\"primary \" icon=\"el-icon-plus\"\n          >新增\n          </el-button>\n\n          <el-button class=\"mb8\" @click=\"deleteRow\"\n                     type=\"danger\" icon=\"el-icon-delete\"\n          >删除\n          </el-button>\n        </div>\n      </el-white>\n      <comp-table :table-and-page-info=\"tableAndPageInfo\"  @update:multipleSelection=\"selectChange\"/>\n    </div>\n    <!--新增、修改、详情弹框-->\n    <el-dialog :title=title :visible.sync=\"show\" width=\"30%\" append-to-body @close=\"handleClose\" v-dialogDrag>\n      <el-form label-width=\"130px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row :gutter=\"8\" class=\"pull-left\" >\n          <el-col :span=\"24\">\n            <el-form-item  label=\"操作类型：\" prop=\"czlx\">\n              <el-select placeholder=\"请选择操作类型\" v-model=\"form.czlx\" :disabled=\"isDisabled\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item  label=\"操作描述：\" prop=\"czms\">\n              <el-input v-model=\"form.czms\" placeholder=\"请输入操作描述\"  :disabled=\"isDisabled\"/>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"getInsterClose\">取 消</el-button>\n        <el-button v-if=\"title==='新增' || title==='修改'\" type=\"primary\" @click=\"saveRow\">确 认</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  import {getList,saveOrUpdate,remove,exportExcel} from '@/api/dagangOilfield/bzgl/lpbzk/czpzysyk'\n  export default {\n    name: \"czpzysyk\",\n    data() {\n      return {\n        form:{\n          objId:'',\n          czlx:'',\n          czms:''\n        },\n        options:[{label:'类型1',value:'类型1'},{label:'类型2',value:'类型2'}],\n        title:'',\n        show:false,\n        filterInfo: {\n          data: {\n            czms: '',\n            czlx: [],\n          },\n          fieldList: [\n            {label: '操作类型', type: 'select', value: 'czlx', multiple: true, options:[{label:'类型1',value:'类型1'},{label:'类型2',value:'类型2'}]},\n            {label: '操作描述', type: 'input', value: 'czms'},\n          ]\n        },\n        isDisabled:false,\n        selectRows:[],\n        tableAndPageInfo: {\n\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n\n            {prop: 'czlx', label:'操作类型', minWidth: '180'},\n            {prop: 'czms', label: '操作描述', minWidth: '180'},\n            {\n              prop: 'operation',\n              label: '操作',\n              minWidth: '130px',\n              style: {display: 'block'},\n              //操作列固定再右侧\n              fixed:'right',\n              operation: [\n                {name: '修改', clickFun: this.updateRow},\n                {name: '详情', clickFun: this.getInfo},\n              ]\n            },\n          ]\n        },\n        //组织树\n        treeOptions:[\n          {\n            label: '断路器',\n          }, {\n            label: '变压器',\n            children: [{\n              label: '冷却系统',\n              children: [{\n                label: '温控运行情况',\n              }, {\n                label: '油箱',\n\n              }, {\n                label: '铁芯',\n\n              }, {\n                label: '绕组',\n\n              }]\n            }]\n          }],\n        //删除是否可用\n        multipleSensor: true,\n        //查询参数\n        queryParams:{\n          pageNum: 1,\n          pageSize: 10,\n          ancuoTerm:''\n        },\n        //填入数据校验\n        rules: {\n          czlx: [\n            { required: true, message: '操作类型不能为空', trigger: 'change' }\n          ],\n          czms:[\n            { required: true, message: '操作描述不能为空', trigger: 'change' }\n          ]\n        },\n        //表单开关\n        isSearchShow:false,\n        //工作票类型下拉菜单\n        gzpTypeOptions:[\n          {\n            value: 'type1',\n            label: '类型1'\n          }, {\n            value: 'type2',\n            label: '类型2'\n          }\n        ],\n\n        list: [\n          {\n            id: 1,\n\n            gzplx: \"合上、拉开\",\n\n            acdy: \"合上xx开关\",\n          },\n          {\n            id: 2,\n\n            gzplx: \"合上、拉开\",\n\n            acdy: \"拉开xx开关\",\n          },\n          {\n            id: 3,\n\n            gzplx: \"投入、退出\",\n\n            acdy: \"投入xx压板\",\n          },\n          {\n            id: 4,\n\n            gzplx: \"投入、退出\",\n\n            acdy: \"退出xx压板\",\n          }\n        ],\n      params:{\n\n      }\n      };\n    },\n    watch: {\n    },\n    created() {\n\n    },\n    mounted() {\n      this.getData();\n    },\n    methods: {\n      async getData(params){\n         if (params){\n           params.czlx = params.czlx.join(',')\n         }\n          const param={...this.params,...params}\n          const {data,code} = await getList(param);\n          if(code==='0000'){\n            this.tableAndPageInfo.tableData=data.records\n            this.tableAndPageInfo.pager.total=data.total\n          }\n\n      },\n      //树节点点击事件\n      handleNodeClick(data) {\n\n      },\n      //添加按钮\n      addSensorButton(){\n        this.show=true\n        this.isDisabled=false\n        this.form={\n            objId:'',\n            czlx:'',\n            czms:''\n        },\n        this.title = '新增'\n      },\n      getInsterClose(){\n        this.show=false\n      },\n      //编辑按钮\n      updateRow(row){\n        this.title = '修改'\n        this.isDisabled=false\n        this.form={...row}\n        this.show=true\n      },\n      //详情按钮\n      getInfo(row){\n        this.title = '详情'\n        this.form={...row}\n        this.isDisabled=true\n        this.show=true\n      },\n      async saveRow(){\n\n        this.$refs['form'].validate(valid => {\n          if (valid) {\n            saveOrUpdate(this.form).then(res => {\n              try {\n                if (res.code === '0000') {\n                  this.$message.success('操作成功')\n                }\n              } catch (e) {\n                console.log(e)\n              }\n              this.getData()\n            })\n          } else {\n            return false\n          }\n          this.show =  false\n        })\n      },\n      //删除按钮\n      async deleteRow(){\n        if(this.selectRows.length<1){\n          this.$message.warning(\"请选择正确的数据！！！\")\n          return\n        }\n        let ids=this.selectRows.map(item=>{return item.objId});\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove(ids).then(({code })=>{\n            if(code==='0000'){\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              });\n              this.getData()\n            }else{\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              });\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          });\n        });\n        this.getData()\n      },\n      selectChange(rows){\n        this.selectRows=rows\n      },\n      //导出按钮\n      handleExport(){\n\n      },\n      //查询列表\n      getList(){\n\n      },\n      //搜索\n      handleQuery(){\n\n      },\n      //重置\n      resetQuery(){\n        this.resetForm(\"queryForm\");\n      },\n      //清空表单数据\n      handleClose(){\n        this.form={};\n        this.$nextTick(() => {\n          this.form = this.$options.data().form;\n          this.resetForm(\"form\");\n        });\n      },\n    }\n  };\n</script>\n\n<style lang='scss' scoped>\n  .box-card{\n    margin-bottom: 15px;\n    .el-card__header{\n      background-color: rgb(235, 245, 255) !important;\n    }\n  }\n  .item{\n    width: 200px;height: 148px; float: left;\n  }\n</style>\n"]}]}