{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\sybwk.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\sybwk.vue", "mtime": 1706897323684}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["sybwk.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmGA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "sybwk.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card class=\"aside_height\" shadow=\"never\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备类型</span>\n          </div>\n          <div style=\" overflow: auto;height: 84vh\">\n              <el-tree id=\"tree\"\n                       highlight-current\n                       :props=\"props\"\n                       :load=\"loadNode\"\n                       lazy\n                       :default-expanded-keys=\"['1']\"\n                       @node-expand=\"handleNodeClick\"\n                       @node-click=\"handleNodeClick\"/>\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button @click=\"addSensorButton\"\n                       type=\"primary\" icon=\"el-icon-plus\"\n                       :disabled=\"addDisabled\"\n            >新增\n            </el-button>\n            <!--<el-button @click=\"deleteSensorButton\"-->\n            <!--           type=\"danger\" icon=\"el-icon-delete\"-->\n            <!--&gt;删除-->\n            <!--</el-button>-->\n          </div>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"78.2vh\">\n            <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\"\n                             width=\"160\"\n                             :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button @click=\"updateDetails(scope.row)\" v-show=\"scope.row.createBy == currentUser\" type=\"text\"\n                           size=\"small\"  title=\"修改\"  class='el-icon-edit'\n                >\n                </el-button>\n                <el-button @click=\"getDetails(scope.row)\" type=\"text\" size=\"small\" title=\"详情\" class=\"el-icon-view\"></el-button>\n                <el-button @click=\"deleteSensorButton(scope.row)\" v-show=\"scope.row.createBy == currentUser\" type=\"text\"\n                           size=\"small\" title=\"删除\" class=\"el-icon-delete\"\n                >\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n    <!--新增\\修改\\详情弹出-->\n    <el-dialog :title=title :visible.sync=\"isShowDetails\" width=\"60%\" v-dialogDrag>\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\">\n        <el-row :gutter=\"8\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"设备类型：\" prop=\"sblx\">\n              <el-input v-model=\"form.sblx\" disabled style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"设备类型编码：\" prop=\"sblxbm\">\n              <el-input v-model=\"form.sblxbm\" disabled style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"试验部位：\" prop=\"sybw\">\n              <el-input placeholder=\"请输入试验部位名称\" v-model=\"form.sybw\" :disabled=\"isDisabled\"\n                        style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注：\" prop=\"bz\">\n              <el-input type=\"textarea\" placeholder=\"请输入备注\" v-model=\"form.bz\" :disabled=\"isDisabled\"/>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" v-show=\"isShow\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"save\">确 认</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n\n  import {getPageDataList, remove, saveOrUpdate, getSblxTree} from '@/api/dagangOilfield/bzgl/sybwk'\n  import {getDeviceClassTreeNodeByPid} from '@/api/dagangOilfield/bzgl/sblxwh/sblxwh'\n\n\n  export default {\n    name: \"sybwk\",\n    data() {\n      return {\n        currentUser: this.$store.getters.name,\n        //新增按钮控制\n        addDisabled: true,\n        //树结构懒加载参数\n        props: {\n          label: 'name',\n          children: 'zones',\n          isLeaf: (data, node) => {\n            if (node.level === 2) {\n              return true\n            }\n          },\n        },\n        //删除选择列\n        selectRows: [],\n        //弹出框表单\n        form: {},\n        //查询试验部位参数\n        querySyBwParam: {\n          sblxbm: undefined,\n          pageNum: 1,\n          pageSize: 10\n        },\n        //点击树节点赋值\n        treeForm: {},\n        //试验部位列表\n        tableAndPageInfo: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          tableData: [],\n          tableHeader: [\n            {label: '设备类型', prop: 'sblx', minWidth: '200'},\n            {label: '设备类型编码', prop: 'sblxbm', minWidth: '200'},\n            {label: '试验部位', prop: 'sybw', minWidth: '180'},\n            {label: '备注', prop: 'bz', minWidth: '200'},\n            // {\n            //   prop: 'operation',\n            //   label: '操作',\n            //   minWidth: '100px',\n            //   style: {display: 'block'},\n            //   //操作列固定再右侧\n            //   fixed: 'right',\n            //   operation: [\n            //     {name: '修改', clickFun: this.updateDetails},\n            //     {name: '详情', clickFun: this.getDetails},\n            //   ]\n            // },\n          ],\n          option: {checkBox: true, serialNumber: true},\n        },\n        //组织树\n        treeOptions: [],\n\n        isShowDetails: false,\n        title: '',\n\n        //删除是否可用\n        multipleSensor: true,\n        //查询参数\n        queryParams: {\n          bm: undefined,\n          pageNum: 1,\n          pageSize: 10\n        },\n        isDisabled: false,\n        isShow: true,\n\n\n      };\n    },\n    watch: {},\n    created() {\n      //获取数据列表\n      this.getData();\n      //获取左侧树结构\n\n\n    },\n    methods: {\n      //懒加载函数\n      loadNode(node, resolve) {\n        let TreeparamMap = {\n          pid: \"\",\n          spbLogo: [\"输电设备\", \"变电设备\",\"配电设备\"]\n        };\n        if (node.level === 0) {\n          TreeparamMap.pid = \"sb\";\n          return this.getTreeNode(TreeparamMap, resolve)\n        }\n        setTimeout(() => {\n          TreeparamMap.pid = node.data.code;\n          this.getTreeNode(TreeparamMap, resolve)\n        }, 500)\n\n      },\n\n      //获取树节点数据\n      getTreeNode(paramMap, resolve) {\n        getDeviceClassTreeNodeByPid(paramMap).then(res => {\n          let treeNodes = []\n          res.data.forEach(item => {\n            let node = {\n              name: item.name,\n              level: item.level,\n              id: item.id,\n              pid: item.pid,\n              leaf: false,\n              code: item.code\n            }\n            treeNodes.push(node)\n          })\n          resolve(treeNodes)\n        })\n      },\n      //添加后确认保存按钮\n      save() {\n        saveOrUpdate(this.form).then(res => {\n          if (res.code == '0000') {\n            this.$message.success(\"操作成功！\");\n            this.isShowDetails = false;\n            this.getData();\n          }\n        });\n      },\n      //测试获取树结构\n      TextGetTree() {\n        getSblxTree({fsblxId: \"sb\"}).then(res => {\n          this.treeOptions = res.data;\n        })\n      },\n\n\n      //树节点点击事件\n      handleNodeClick(data) {\n        if (data.level === '1') {\n          //开放新增按钮\n          this.addDisabled = false;\n          this.treeForm = data;\n          this.querySyBwParam.sblxbm = data.code;\n          this.getData();\n        } else {\n          this.addDisabled = true;\n        }\n      },\n      //添加按钮\n      addSensorButton() {\n        this.isShowDetails = true;\n        this.isDisabled = false;\n        this.title = '新增'\n        this.form={};\n        this.form.sblx = this.treeForm.name;\n        this.form.sblxbm = this.treeForm.code;\n      },\n      //列表查询\n      async getData(params) {\n        try {\n          const param = {...this.querySyBwParam, ...params}\n          const {data, code} = await getPageDataList(param);\n          if (code === '0000') {\n            this.tableAndPageInfo.tableData = data.records\n            this.tableAndPageInfo.pager.total = data.total\n          }\n        } catch (e) {\n        }\n      },\n\n      /**\n       * 表格多选框\n       */\n      handleSelectionChange(rows) {\n        this.selectRows = rows\n\n      },\n\n\n      close() {\n        this.isShowDetails = false\n      },\n      updateDetails(row) {\n        this.title = '修改'\n        this.isShowDetails = true;\n        this.form = row;\n        this.isDisabled = false;\n        this.isShow = true;\n      },\n      getDetails(row) {\n        this.title = '详情'\n        this.isShowDetails = true;\n        this.form = row;\n        this.isDisabled = true;\n        this.isShow = false;\n      },\n\n      //删除按钮\n      deleteSensorButton(row) {\n        this.form = row\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove([this.form.objId]).then(({code}) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              });\n              this.tableAndPageInfo.pager.pageResize = 'Y';\n              this.getData()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              });\n              this.tableAndPageInfo.pager.pageResize = 'Y';\n              this.getData()\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          });\n        });\n\n      },\n      //导出按钮\n      handleExport() {\n\n      },\n    }\n  };\n</script>\n\n<style lang='scss' scoped>\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  max-height: 79vh;\n  overflow: auto;\n}\n</style>\n"]}]}