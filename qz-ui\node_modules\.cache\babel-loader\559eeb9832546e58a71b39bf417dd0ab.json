{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\asset\\sbbgsq.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\asset\\sbbgsq.js", "mtime": 1706897313905}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0TGlzdCA9IGdldExpc3Q7CmV4cG9ydHMuc2F2ZU9yVXBkYXRlID0gc2F2ZU9yVXBkYXRlOwpleHBvcnRzLnJlbW92ZSA9IHJlbW92ZTsKZXhwb3J0cy5kb3dubG9hZEJ5RmlsZUlkID0gZG93bmxvYWRCeUZpbGVJZDsKZXhwb3J0cy5nZXRPcmdhbml6YXRpb25TZWxlY3RlZCA9IGdldE9yZ2FuaXphdGlvblNlbGVjdGVkOwoKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Cgp2YXIgYmFzZVVybCA9ICcvbWFuYWdlci1hcGknOwp2YXIgYmFzZVVybEZvcklzYyA9ICIvaXNjLWFwaSI7IC8vIOafpeivogoKZnVuY3Rpb24gZ2V0TGlzdChwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy9hc3NldFNienRiZy9nZXRMaXN0QnlQYWdlJywgcGFyYW1zLCAxKTsKfSAvLyDmt7vliqDmiJbkv67mlLkKCgpmdW5jdGlvbiBzYXZlT3JVcGRhdGUocGFyYW1zKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcvYXNzZXRTYnp0Ymcvc2F2ZU9yVXBkYXRlJywgcGFyYW1zLCAxKTsKfSAvLyDliKDpmaQKCgpmdW5jdGlvbiByZW1vdmUocGFyYW1zKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcvYXNzZXRTYnp0YmcvcmVtb3ZlJywgcGFyYW1zLCAxKTsKfSAvL+agueaNruaWh+S7tmlk5LiL6L296ZmE5Lu2CgoKZnVuY3Rpb24gZG93bmxvYWRCeUZpbGVJZChkYXRhKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdERvd25sb2FkKGJhc2VVcmxGb3JJc2MsICIvZmlsZS9kb3dubG9hZEJ5RmlsZUlkIiwgSlNPTi5zdHJpbmdpZnkoZGF0YSkpOwp9Ci8qKgogKiDojrflj5bnu4Tnu4fnu5PmnoTkuIvmi4nmoYbmlbDmja4KICogQHBhcmFtIHBhcmFtcwogKiBAcmV0dXJucyB7UHJvbWlzZSB8IFByb21pc2U8YW55Pn0KICovCgoKZnVuY3Rpb24gZ2V0T3JnYW5pemF0aW9uU2VsZWN0ZWQocGFyYW1zKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdEdldChiYXNlVXJsICsgJy9zZWxlY3QvZ2V0T3JnYW5pemF0aW9uU2VsZWN0ZWQnLCBwYXJhbXMsIDIpOwp9"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagangOilfield/asset/sbbgsq.js"], "names": ["baseUrl", "baseUrlForIsc", "getList", "params", "api", "requestPost", "saveOrUpdate", "remove", "downloadByFileId", "data", "requestDownload", "JSON", "stringify", "getOrganizationSelected", "requestGet"], "mappings": ";;;;;;;;;;;;;AAAA;;AAEA,IAAMA,OAAO,GAAG,cAAhB;AAEA,IAAMC,aAAa,GAAG,UAAtB,C,CAEA;;AACO,SAASC,OAAT,CAAiBC,MAAjB,EAAyB;AAC9B,SAAOC,iBAAIC,WAAJ,CAAgBL,OAAO,GAAG,4BAA1B,EAAwDG,MAAxD,EAAgE,CAAhE,CAAP;AACD,C,CAED;;;AACO,SAASG,YAAT,CAAsBH,MAAtB,EAA8B;AACnC,SAAOC,iBAAIC,WAAJ,CAAgBL,OAAO,GAAG,2BAA1B,EAAuDG,MAAvD,EAA+D,CAA/D,CAAP;AACD,C,CAED;;;AACO,SAASI,MAAT,CAAgBJ,MAAhB,EAAwB;AAC7B,SAAOC,iBAAIC,WAAJ,CAAgBL,OAAO,GAAG,qBAA1B,EAAiDG,MAAjD,EAAyD,CAAzD,CAAP;AACD,C,CAED;;;AACO,SAASK,gBAAT,CAA0BC,IAA1B,EAAgC;AACrC,SAAOL,iBAAIM,eAAJ,CAAoBT,aAApB,EAAmC,wBAAnC,EAA6DU,IAAI,CAACC,SAAL,CAAeH,IAAf,CAA7D,CAAP;AACD;AACD;;;;;;;AAKO,SAASI,uBAAT,CAAiCV,MAAjC,EAAyC;AAC9C,SAAOC,iBAAIU,UAAJ,CAAed,OAAO,GAAG,iCAAzB,EAA4DG,MAA5D,EAAoE,CAApE,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\n\nconst baseUrl = '/manager-api'\n\nconst baseUrlForIsc = \"/isc-api\";\n\n// 查询\nexport function getList(params) {\n  return api.requestPost(baseUrl + '/assetSbztbg/getListByPage', params, 1)\n}\n\n// 添加或修改\nexport function saveOrUpdate(params) {\n  return api.requestPost(baseUrl + '/assetSbztbg/saveOrUpdate', params, 1)\n}\n\n// 删除\nexport function remove(params) {\n  return api.requestPost(baseUrl + '/assetSbztbg/remove', params, 1)\n}\n\n//根据文件id下载附件\nexport function downloadByFileId(data) {\n  return api.requestDownload(baseUrlForIsc, \"/file/downloadByFileId\", JSON.stringify(data))\n}\n/**\n * 获取组织结构下拉框数据\n * @param params\n * @returns {Promise | Promise<any>}\n */\nexport function getOrganizationSelected(params) {\n  return api.requestGet(baseUrl + '/select/getOrganizationSelected', params, 2);\n}\n\n"]}]}