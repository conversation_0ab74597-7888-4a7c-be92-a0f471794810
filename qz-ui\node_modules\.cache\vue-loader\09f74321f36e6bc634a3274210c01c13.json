{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\pddzcz\\components\\pdczp_cx.vue?vue&type=template&id=44a73b78&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\pddzcz\\components\\pdczp_cx.vue", "mtime": 1748604763458}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}