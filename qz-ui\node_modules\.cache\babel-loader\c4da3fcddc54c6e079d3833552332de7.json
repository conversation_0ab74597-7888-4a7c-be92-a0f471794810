{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\store\\modules\\user.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\store\\modules\\user.js", "mtime": 1740590835388}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/store/modules/user.js"], "names": ["user", "state", "token", "name", "nick<PERSON><PERSON>", "deptId", "deptName", "avatar", "roles", "permissions", "loginNum", "AppNum", "PcNum", "hasSuperRole", "timer", "mutations", "SET_TOKEN", "SET_NAME", "SET_NICK_NAME", "SET_DEPT_ID", "SET_DEPT_NAME", "SET_AVATAR", "SET_ROLES", "SET_PERMISSIONS", "SET_LOGINNUM", "SET_HAS_SUPERROLE", "INIT_TIMER", "clearTimeout", "CLEAR_TIMER", "actions", "<PERSON><PERSON>", "userInfo", "commit", "username", "userName", "trim", "password", "code", "uuid", "encryptedPassword", "isSSOLogin", "Promise", "resolve", "reject", "then", "res", "lastToken", "data", "window", "localStorage", "setItem", "catch", "error", "GetInfo", "all_permission", "process", "env", "VUE_APP_BASE_API", "length", "super_admin", "some", "role", "GetButtonList", "path", "LogOut", "FedLogOut", "InitTimer", "console", "log", "setTimeout", "ClearTimer", "CheckSSOToken", "dispatch", "response", "checkCode", "msg"], "mappings": ";;;;;;;;;;;;;;;AAAA;;AACA;;AACA;;AACA,IAAMA,IAAI,GAAG;AACXC,EAAAA,KAAK,EAAE;AACLC,IAAAA,KAAK,EAAE,qBADF;AAELC,IAAAA,IAAI,EAAE,EAFD;AAGLC,IAAAA,QAAQ,EAAC,EAHJ;AAILC,IAAAA,MAAM,EAAC,EAJF;AAKLC,IAAAA,QAAQ,EAAC,EALJ;AAMLC,IAAAA,MAAM,EAAE,EANH;AAOLC,IAAAA,KAAK,EAAE,EAPF;AAQLC,IAAAA,WAAW,EAAE,EARR;AASLC,IAAAA,QAAQ,EAAE;AACRC,MAAAA,MAAM,EAAC,CADC;AAERC,MAAAA,KAAK,EAAC;AAFE,KATL;AAaLC,IAAAA,YAAY,EAAC,KAbR;AAcLC,IAAAA,KAAK,EAAC,IAdD,CAcM;;AAdN,GADI;AAkBXC,EAAAA,SAAS,EAAE;AACTC,IAAAA,SAAS,EAAE,mBAACf,KAAD,EAAQC,KAAR,EAAkB;AAC3BD,MAAAA,KAAK,CAACC,KAAN,GAAcA,KAAd;AACD,KAHQ;AAITe,IAAAA,QAAQ,EAAE,kBAAChB,KAAD,EAAQE,IAAR,EAAiB;AACzBF,MAAAA,KAAK,CAACE,IAAN,GAAaA,IAAb;AACD,KANQ;AAOTe,IAAAA,aAAa,EAAE,uBAACjB,KAAD,EAAQG,QAAR,EAAqB;AAClCH,MAAAA,KAAK,CAACG,QAAN,GAAiBA,QAAjB;AACD,KATQ;AAUTe,IAAAA,WAAW,EAAE,qBAAClB,KAAD,EAAQI,MAAR,EAAmB;AAC9BJ,MAAAA,KAAK,CAACI,MAAN,GAAeA,MAAf;AACD,KAZQ;AAaTe,IAAAA,aAAa,EAAE,uBAACnB,KAAD,EAAQK,QAAR,EAAqB;AAClCL,MAAAA,KAAK,CAACK,QAAN,GAAiBA,QAAjB;AACD,KAfQ;AAgBTe,IAAAA,UAAU,EAAE,oBAACpB,KAAD,EAAQM,MAAR,EAAmB;AAC7BN,MAAAA,KAAK,CAACM,MAAN,GAAeA,MAAf;AACD,KAlBQ;AAmBTe,IAAAA,SAAS,EAAE,mBAACrB,KAAD,EAAQO,KAAR,EAAkB;AAC3BP,MAAAA,KAAK,CAACO,KAAN,GAAcA,KAAd;AACD,KArBQ;AAsBTe,IAAAA,eAAe,EAAE,yBAACtB,KAAD,EAAQQ,WAAR,EAAwB;AACvCR,MAAAA,KAAK,CAACQ,WAAN,GAAoBA,WAApB;AACD,KAxBQ;AAyBTe,IAAAA,YAAY,EAAE,sBAACvB,KAAD,EAAQS,QAAR,EAAqB;AACjCT,MAAAA,KAAK,CAACS,QAAN,GAAiBA,QAAjB;AACD,KA3BQ;AA4BTe,IAAAA,iBAAiB,EAAE,2BAACxB,KAAD,EAAQY,YAAR,EAAyB;AAC1CZ,MAAAA,KAAK,CAACY,YAAN,GAAqBA,YAArB;AACD,KA9BQ;AA+BT;AACAa,IAAAA,UAAU,EAAC,oBAACzB,KAAD,EAAOa,KAAP,EAAe;AACxBb,MAAAA,KAAK,CAACa,KAAN,IAAea,YAAY,CAAC1B,KAAK,CAACa,KAAP,CAA3B,CADwB,CACiB;;AACzCb,MAAAA,KAAK,CAACa,KAAN,GAAcA,KAAd;AACD,KAnCQ;AAoCT;AACAc,IAAAA,WAAW,EAAC,qBAAC3B,KAAD,EAAS;AACnBA,MAAAA,KAAK,CAACa,KAAN,IAAea,YAAY,CAAC1B,KAAK,CAACa,KAAP,CAA3B;AACD;AAvCQ,GAlBA;AA4DXe,EAAAA,OAAO,EAAE;AACP;AACAC,IAAAA,KAFO,uBAEWC,QAFX,EAEqB;AAAA,UAApBC,MAAoB,QAApBA,MAAoB;AAC1B,UAAMC,QAAQ,GAAGF,QAAQ,CAACG,QAAT,CAAkBC,IAAlB,EAAjB;AACA,UAAMC,QAAQ,GAAGL,QAAQ,CAACK,QAA1B;AACA,UAAMC,IAAI,GAAGN,QAAQ,CAACM,IAAtB;AACA,UAAMC,IAAI,GAAGP,QAAQ,CAACO,IAAtB,CAJ0B,CAK1B;;AACA,UAAMC,iBAAiB,GAAGR,QAAQ,CAACS,UAAT,GAAsBJ,QAAtB,GAAiC,kBAAQA,QAAR,CAA3D;AACA,aAAO,IAAIK,OAAJ,CAAY,UAACC,OAAD,EAAUC,MAAV,EAAqB;AACtC,0BAAMV,QAAN,EAAgBM,iBAAhB,EAAmCF,IAAnC,EAAyCC,IAAzC,EAA+CM,IAA/C,CAAoD,UAAAC,GAAG,EAAI;AACzD,cAAIC,SAAS,GAAG,yBAAhB;;AACA,cAAG,CAACA,SAAJ,EAAc;AACZ,oCAAaD,GAAG,CAACE,IAAjB;AACD;;AACD,8BAASF,GAAG,CAACE,IAAb;AACAf,UAAAA,MAAM,CAAC,WAAD,EAAca,GAAG,CAACE,IAAlB,CAAN;AACAC,UAAAA,MAAM,CAACC,YAAP,CAAoBC,OAApB,CAA4B,OAA5B,EAAqCL,GAAG,CAACE,IAAzC;AACAL,UAAAA,OAAO;AACR,SATD,EASGS,KATH,CASS,UAAAC,KAAK,EAAI;AAChBT,UAAAA,MAAM,CAACS,KAAD,CAAN;AACD,SAXD;AAYD,OAbM,CAAP;AAcD,KAvBM;AAyBP;AACAC,IAAAA,OA1BO,0BA0BoB;AAAA,UAAjBrB,MAAiB,SAAjBA,MAAiB;AAAA,UAAT/B,KAAS,SAATA,KAAS;AACzB,aAAO,IAAIwC,OAAJ,CAAY,UAACC,OAAD,EAAUC,MAAV,EAAqB;AACtC,4BAAQ1C,KAAK,CAACC,KAAd,EAAqB0C,IAArB,CAA0B,UAAAC,GAAG,EAAI;AAC/B,cAAMS,cAAc,GAAG,CAAC,OAAD,CAAvB;AACA,cAAMtD,IAAI,GAAG6C,GAAG,CAACE,IAAjB;AACA,cAAMxC,MAAM,GAAGP,IAAI,CAACO,MAAL,IAAe,EAAf,GAAoB,EAApB,GAAyBgD,OAAO,CAACC,GAAR,CAAYC,gBAAZ,GAA+BzD,IAAI,CAACO,MAA5E;;AACA,cAAIP,IAAI,CAACQ,KAAL,IAAcR,IAAI,CAACQ,KAAL,CAAWkD,MAAX,GAAoB,CAAtC,EAAyC;AAAE;AACzC1B,YAAAA,MAAM,CAAC,WAAD,EAAchC,IAAI,CAACQ,KAAnB,CAAN;AACAwB,YAAAA,MAAM,CAAC,iBAAD,EAAoBhC,IAAI,CAACS,WAAzB,CAAN,CAFuC,CAGvC;;AACA,gBAAMkD,WAAW,GAAG,aAApB;AACA,gBAAM9C,YAAY,GAAGb,IAAI,CAACQ,KAAL,CAAWoD,IAAX,CAAgB,UAAAC,IAAI;AAAA,qBAAIF,WAAW,KAAKE,IAApB;AAAA,aAApB,CAArB;;AACA,gBAAIhD,YAAJ,EAAkB;AAChBmB,cAAAA,MAAM,CAAC,mBAAD,EAAsB,IAAtB,CAAN;AACAA,cAAAA,MAAM,CAAC,iBAAD,EAAoBsB,cAApB,CAAN;AACD;AACF,WAVD,MAUO;AACLtB,YAAAA,MAAM,CAAC,WAAD,EAAc,CAAC,cAAD,CAAd,CAAN;AACD;;AACDA,UAAAA,MAAM,CAAC,UAAD,EAAahC,IAAI,CAACkC,QAAlB,CAAN;AACAF,UAAAA,MAAM,CAAC,eAAD,EAAkBhC,IAAI,CAACI,QAAvB,CAAN;AACA4B,UAAAA,MAAM,CAAC,aAAD,EAAgBhC,IAAI,CAACK,MAArB,CAAN;AACA2B,UAAAA,MAAM,CAAC,eAAD,EAAkBhC,IAAI,CAACM,QAAvB,CAAN;AACA0B,UAAAA,MAAM,CAAC,YAAD,EAAezB,MAAf,CAAN;AACAyB,UAAAA,MAAM,CAAC,cAAD,EAAiBhC,IAAI,CAACU,QAAtB,CAAN;AACAgC,UAAAA,OAAO,CAACG,GAAD,CAAP;AACD,SAxBD,EAwBGM,KAxBH,CAwBS,UAAAC,KAAK,EAAI;AAChBT,UAAAA,MAAM,CAACS,KAAD,CAAN;AACD,SA1BD;AA2BD,OA5BM,CAAP;AA6BD,KAxDM;AAyDPU,IAAAA,aAzDO,gCAyDiBC,IAzDjB,EAyDuB;AAAA,UAAd/B,MAAc,SAAdA,MAAc;AAC5B,aAAO,IAAIS,OAAJ,CAAY,UAACC,OAAD,EAAUC,MAAV,EAAqB;AACtC,kCAAc;AAACoB,UAAAA,IAAI,EAACA;AAAN,SAAd,EAA2BnB,IAA3B,CAAgC,UAAAC,GAAG,EAAI;AACrC,cAAME,IAAI,GAAGF,GAAG,CAACE,IAAjB;;AACA,cAAIA,IAAI,CAACW,MAAL,GAAc,CAAlB,EAAqB;AACnB1B,YAAAA,MAAM,CAAC,iBAAD,EAAoBe,IAApB,CAAN;AACD;;AACDL,UAAAA,OAAO,CAACG,GAAD,CAAP;AACD,SAND,EAMGM,KANH,CAMS,UAAAC,KAAK,EAAI;AAChBT,UAAAA,MAAM,CAACS,KAAD,CAAN;AACD,SARD;AASD,OAVM,CAAP;AAWD,KArEM;AAuEP;AACAY,IAAAA,MAxEO,yBAwEmB;AAAA,UAAjBhC,MAAiB,SAAjBA,MAAiB;AAAA,UAAT/B,KAAS,SAATA,KAAS;AACxB,aAAO,IAAIwC,OAAJ,CAAY,UAACC,OAAD,EAAUC,MAAV,EAAqB;AACtC,2BAAO1C,KAAK,CAACC,KAAb,EAAoB0C,IAApB,CAAyB,YAAM;AAC7BZ,UAAAA,MAAM,CAAC,WAAD,EAAc,EAAd,CAAN;AACAA,UAAAA,MAAM,CAAC,WAAD,EAAc,EAAd,CAAN;AACAA,UAAAA,MAAM,CAAC,iBAAD,EAAoB,EAApB,CAAN;AACAA,UAAAA,MAAM,CAAC,aAAD,CAAN,CAJ6B,CAIP;;AACtB,mCAL6B,CAM7B;;AACAU,UAAAA,OAAO;AACR,SARD,EAQGS,KARH,CAQS,UAAAC,KAAK,EAAI;AAChBT,UAAAA,MAAM,CAACS,KAAD,CAAN;AACD,SAVD;AAWD,OAZM,CAAP;AAaD,KAtFM;AAwFP;AACAa,IAAAA,SAzFO,4BAyFe;AAAA,UAAVjC,MAAU,SAAVA,MAAU;AACpB,aAAO,IAAIS,OAAJ,CAAY,UAAAC,OAAO,EAAI;AAC5BV,QAAAA,MAAM,CAAC,WAAD,EAAc,EAAd,CAAN;AACA,iCAF4B,CAG5B;;AACAU,QAAAA,OAAO;AACR,OALM,CAAP;AAMD,KAhGM;AAiGP;AACAwB,IAAAA,SAlGO,4BAkGc;AAAA,UAATlC,MAAS,SAATA,MAAS;AACnBmC,MAAAA,OAAO,CAACC,GAAR,CAAY,OAAZ;AACApC,MAAAA,MAAM,CAAC,YAAD,EAAcqC,UAAU,CAAC,YAAI;AACjC,mCADiC,CAClB;AAChB,OAF6B,EAE3B,KAAK,EAAL,GAAU,IAFiB,CAAxB,CAAN,CAFmB,CAIC;AACrB,KAvGM;AAwGP;AACAC,IAAAA,UAzGO,6BAyGe;AAAA,UAATtC,MAAS,SAATA,MAAS;AACpBA,MAAAA,MAAM,CAAC,aAAD,CAAN;AACD,KA3GM;AA4GP;AACAuC,IAAAA,aA7GO,gCA6GqBlC,IA7GrB,EA6G2B;AAAA,UAAlBmC,QAAkB,SAAlBA,QAAkB;AAChC,aAAO,IAAI/B,OAAJ,CAAY,UAACC,OAAD,EAAUC,MAAV,EAAqB;AACtC,kCAAcN,IAAd,EAAoBO,IAApB,CAAyB,UAAA6B,QAAQ,EAAI;AACnC,cAAIA,QAAQ,CAACpC,IAAT,KAAkB,GAAlB,IAAyBoC,QAAQ,CAACC,SAAtC,EAAiD;AAC/C;AACA,mBAAOF,QAAQ,CAAC,OAAD,EAAU;AAAEtC,cAAAA,QAAQ,EAAEuC,QAAQ,CAACvC,QAArB;AAA+BE,cAAAA,QAAQ,EAAE,eAAzC;AAA0DI,cAAAA,UAAU,EAAE;AAAtE,aAAV,CAAf;AACD,WAHD,MAGO;AACLG,YAAAA,MAAM,CAAC8B,QAAQ,CAACE,GAAT,IAAgB,aAAjB,CAAN;AACD;AACF,SAPD,EAOG/B,IAPH,CAOQ,YAAM;AACZF,UAAAA,OAAO,CAAC,IAAD,CAAP;AACD,SATD,EASGS,KATH,CASS,UAAAC,KAAK,EAAI;AAChBT,UAAAA,MAAM,CAACS,KAAD,CAAN;AACD,SAXD;AAYD,OAbM,CAAP;AAcD;AA5HM;AA5DE,CAAb;eA2LepD,I", "sourcesContent": ["import { login, logout, getInfo, getButtonList, refreshToken, checkSSOToken } from '@/api/login'\nimport {getToken, setToken, removeToken, getLastToken, setLastToken, removeLastToken} from '@/utils/auth'\nimport { Encrypt, Decrypt } from '@/utils/aes'\nconst user = {\n  state: {\n    token: getToken(),\n    name: '',\n    nickName:'',\n    deptId:'',\n    deptName:'',\n    avatar: '',\n    roles: [],\n    permissions: [],\n    loginNum: {\n      AppNum:0,\n      PcNum:0\n    },\n    hasSuperRole:false,\n    timer:null,//定时器（登录25分钟后刷新token）\n  },\n\n  mutations: {\n    SET_TOKEN: (state, token) => {\n      state.token = token\n    },\n    SET_NAME: (state, name) => {\n      state.name = name\n    },\n    SET_NICK_NAME: (state, nickName) => {\n      state.nickName = nickName\n    },\n    SET_DEPT_ID: (state, deptId) => {\n      state.deptId = deptId\n    },\n    SET_DEPT_NAME: (state, deptName) => {\n      state.deptName = deptName\n    },\n    SET_AVATAR: (state, avatar) => {\n      state.avatar = avatar\n    },\n    SET_ROLES: (state, roles) => {\n      state.roles = roles\n    },\n    SET_PERMISSIONS: (state, permissions) => {\n      state.permissions = permissions\n    },\n    SET_LOGINNUM: (state, loginNum) => {\n      state.loginNum = loginNum\n    },\n    SET_HAS_SUPERROLE: (state, hasSuperRole) => {\n      state.hasSuperRole = hasSuperRole\n    },\n    //初始化定时器\n    INIT_TIMER:(state,timer)=>{\n      state.timer && clearTimeout(state.timer);//先清除，后初始化\n      state.timer = timer;\n    },\n    //清除定时器\n    CLEAR_TIMER:(state)=>{\n      state.timer && clearTimeout(state.timer);\n    },\n  },\n\n  actions: {\n    // 登录\n    Login({ commit }, userInfo) {\n      const username = userInfo.userName.trim()\n      const password = userInfo.password\n      const code = userInfo.code\n      const uuid = userInfo.uuid\n      // 如果是单点登录，不需要加密密码\n      const encryptedPassword = userInfo.isSSOLogin ? password : Encrypt(password);\n      return new Promise((resolve, reject) => {\n        login(username, encryptedPassword, code, uuid).then(res => {\n          let lastToken = getLastToken();\n          if(!lastToken){\n            setLastToken(res.data);\n          }\n          setToken(res.data)\n          commit('SET_TOKEN', res.data)\n          window.localStorage.setItem(\"token\", res.data);\n          resolve()\n        }).catch(error => {\n          reject(error)\n        })\n      })\n    },\n\n    // 获取用户信息\n    GetInfo({ commit, state }) {\n      return new Promise((resolve, reject) => {\n        getInfo(state.token).then(res => {\n          const all_permission = [\"%:%:%\"];\n          const user = res.data\n          const avatar = user.avatar == \"\" ? '' : process.env.VUE_APP_BASE_API + user.avatar;\n          if (user.roles && user.roles.length > 0) { // 验证返回的roles是否是一个非空数组\n            commit('SET_ROLES', user.roles)\n            commit('SET_PERMISSIONS', user.permissions)\n            //判断登录用户是否在超级管理员组\n            const super_admin = \"ADMIN-GROUP\";\n            const hasSuperRole = user.roles.some(role => super_admin === role)\n            if (hasSuperRole) {\n              commit('SET_HAS_SUPERROLE', true)\n              commit('SET_PERMISSIONS', all_permission)\n            }\n          } else {\n            commit('SET_ROLES', ['ROLE_DEFAULT'])\n          }\n          commit('SET_NAME', user.userName)\n          commit('SET_NICK_NAME', user.nickName)\n          commit('SET_DEPT_ID', user.deptId)\n          commit('SET_DEPT_NAME', user.deptName)\n          commit('SET_AVATAR', avatar)\n          commit('SET_LOGINNUM', user.loginNum)\n          resolve(res)\n        }).catch(error => {\n          reject(error)\n        })\n      })\n    },\n    GetButtonList({ commit},path) {\n      return new Promise((resolve, reject) => {\n        getButtonList({path:path}).then(res => {\n          const data = res.data\n          if (data.length > 0) {\n            commit('SET_PERMISSIONS', data)\n          }\n          resolve(res)\n        }).catch(error => {\n          reject(error)\n        })\n      })\n    },\n\n    // 退出系统\n    LogOut({ commit, state }) {\n      return new Promise((resolve, reject) => {\n        logout(state.token).then(() => {\n          commit('SET_TOKEN', '')\n          commit('SET_ROLES', [])\n          commit('SET_PERMISSIONS', [])\n          commit('CLEAR_TIMER');//清除定时器\n          removeToken()\n          // removeLastToken()\n          resolve()\n        }).catch(error => {\n          reject(error)\n        })\n      })\n    },\n\n    // 前端 登出\n    FedLogOut({ commit }) {\n      return new Promise(resolve => {\n        commit('SET_TOKEN', '')\n        removeToken()\n        // removeLastToken()\n        resolve()\n      })\n    },\n    //初始化定时器\n    InitTimer({ commit }){\n      console.log('重置定时器');\n      commit('INIT_TIMER',setTimeout(()=>{\n        refreshToken();//弹框提示，并重置token\n      }, 55 * 60 * 1000));//设置定时器(55分钟后执行)\n    },\n    //清除定时器\n    ClearTimer({ commit }){\n      commit('CLEAR_TIMER');\n    },\n    // 检查SSO Token\n    CheckSSOToken({ dispatch }, code) {\n      return new Promise((resolve, reject) => {\n        checkSSOToken(code).then(response => {\n          if (response.code === 200 && response.checkCode) {\n            // 如果 SSO Token 有效，直接调用 Login action\n            return dispatch('Login', { userName: response.userName, password: 'dummyPassword', isSSOLogin: true });\n          } else {\n            reject(response.msg || '单点登录Token无效')\n          }\n        }).then(() => {\n          resolve(true);\n        }).catch(error => {\n          reject(error)\n        })\n      })\n    }\n  }\n}\nexport default user\n"]}]}