{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\xldzcz\\components\\czp_cx.vue?vue&type=style&index=0&id=6955e078&scoped=true&lang=css&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\xldzcz\\components\\czp_cx.vue", "mtime": 1706897324343}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmNhcmQtc3R5bGUgewogIGJhY2tncm91bmQtY29sb3I6ICNlMGVmZjI7CiAgbGluZS1oZWlnaHQ6IDN2aDsKICBib3JkZXItcmFkaXVzOiAzcHg7CiAgZm9udC13ZWlnaHQ6IDgwMDsKICBwYWRkaW5nLWxlZnQ6IDF2dzsKICBtYXJnaW4tYm90dG9tOiAzdmg7Cn0KCi8q5o6n5Yi2aW5wdXTovpPlhaXmoYbovrnmoYbmmK/lkKbmmL7npLoqLwouZWxJbnB1dCA+Pj4gLmVsLWlucHV0X19pbm5lciB7CiAgYm9yZGVyOiAwOwp9CgouY2VudGVyIHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7IC8q6K6pZGl25YaF6YOo5paH5a2X5bGF5LitKi8KfQo="}, {"version": 3, "sources": ["czp_cx.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqlBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "czp_cx.vue", "sourceRoot": "src/views/dagangOilfield/czpgl/xldzcz/components", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n      @handleReset=\"getReset\"\n    />\n\n    <div>\n      <el-white class=\"button-group\">\n        <el-row :gutter=\"3\" class=\"button_btn pull-right\">\n          <el-col\n            style=\"\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n          \"\n          >\n          </el-col>\n        </el-row>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"selectChange\"\n        />\n      </el-white>\n    </div>\n\n    <!-- 详情/新增/修改 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"180px\" ref=\"form\" :model=\"form\">\n        <div>\n          <div>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"线路名称\" prop=\"xlmc\">\n                  <el-input\n                    v-model=\"form.xlmc\"\n                    :disabled=\"isDisabledQt\"\n                    placeholder=\"请输入线路名称\"\n                  />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"编号\" prop=\"bm\">\n                  <el-input\n                    v-model=\"form.bm\"\n                    :disabled=\"true\"\n                    placeholder=\"请输入状态\"\n                  />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"操作人\" prop=\"czr\">\n                  <el-input\n                    v-model=\"form.czr\"\n                    :disabled=\"isDisabled\"\n                    placeholder=\"请输入操作人\"\n                  />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"监护人\" prop=\"jhr\">\n                  <el-input\n                    v-model=\"form.jhr\"\n                    :disabled=\"isDisabled\"\n                    placeholder=\"请输入监护人\"\n                  />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"审核人\" prop=\"fgssprmc\">\n                  <el-input\n                    v-model=\"form.fgssprmc\"\n                    :disabled=\"isDisabledQt\"\n                    placeholder=\"请输入审核人\"\n                  />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"日期\" prop=\"rq\">\n                  <el-date-picker\n                    v-model=\"form.rq\"\n                    :disabled=\"isCheckAll\"\n                    type=\"date\"\n                    placeholder=\"选择日期\"\n                    format=\"yyyy-MM-dd\"\n                    value-format=\"yyyy-MM-dd\"\n                    style=\"width: 100%\"\n                  >\n                  </el-date-picker>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"24\">\n                <el-form-item label=\"工作名称\" prop=\"gzmc\">\n                  <el-input\n                    type=\"textarea\"\n                    :rows=\"2\"\n                    v-model=\"form.gzmc\"\n                    :disabled=\"isDisabled\"\n                    placeholder=\"请输入工作名称\"\n                  />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"24\">\n                <el-form-item label=\"上传图片：\">\n                  <el-upload\n                    disabled\n                    action=\"\"\n                    ref=\"uploadImg\"\n                    accept=\"image/jpeg,image/jpg,image/png\"\n                    :headers=\"header\"\n                    :multiple=\"true\"\n                    :on-change=\"handleChange\"\n                    :data=\"uploadImgData\"\n                    :file-list=\"imgList\"\n                    :auto-upload=\"false\"\n                    list-type=\"picture-card\"\n                    :on-preview=\"handlePictureCardPreview\"\n                    :on-progress=\"handleProgress\"\n                    :on-remove=\"handleRemove\"\n                  >\n                    <i class=\"el-icon-plus\"></i>\n                  </el-upload>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"8\">\n                <el-form-item label=\"操作项数：\" prop=\"czxs\">\n                  <el-input-number\n                    v-model=\"form.czxs\"\n                    :disabled=\"isDisabled\"\n                    placeholder=\"请输入操作项数\"\n                  />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"已执行项数：\" prop=\"yzxczxs\">\n                  <el-input-number\n                    v-model=\"form.yzxczxs\"\n                    :disabled=\"isDisabled\"\n                    placeholder=\"请输入已执行操作项数\"\n                  />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"未执行项数：\" prop=\"wzxczxs\">\n                  <el-input-number\n                    v-model=\"form.wzxczxs\"\n                    :disabled=\"isDisabled\"\n                    placeholder=\"请输入未执行操作项数\"\n                  />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"是否已执行：\" prop=\"sfyzx\">\n                  <el-select\n                    v-model=\"form.sfyzx\"\n                    placeholder=\"请选择\"\n                    :disabled=\"isDisabled\"\n                  >\n                    <el-option\n                      v-for=\"item in sfyzxList\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </div>\n\n          <!--列表-->\n          <div>\n            <div align=\"right\">\n              <el-checkbox\n                v-model=\"checkAll\"\n                @change=\"handleCheckAllChange\"\n                :disabled=\"isCheckAll\"\n                >全选</el-checkbox\n              >\n            </div>\n            <!--第一个表格-->\n            <el-table\n              :data=\"propTableData.colFirst\"\n              height=\"200\"\n              border\n              stripe\n              style=\"width: 100%\"\n            >\n              <el-table-column\n                align=\"center\"\n                prop=\"xh\"\n                width=\"100px\"\n                label=\"序号\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input\n                    placeholder=\"请输入序号\"\n                    v-model=\"scope.row.xh\"\n                    :disabled=\"isDisabledQt\"\n                  ></el-input>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n                <template slot-scope=\"scope\">\n                  <el-input\n                    v-if=\"scope.row.isSet\"\n                    placeholder=\"请输入操作项目\"\n                    v-model=\"scope.row.czrw\"\n                    :disabled=\"isDisabledQt\"\n                  ></el-input>\n                </template>\n              </el-table-column>\n\n              <el-table-column\n                align=\"center\"\n                prop=\"xlr\"\n                label=\"下令人\"\n                width=\"100\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input\n                    v-if=\"scope.row.isSet\"\n                    :placeholder=\"isDisabled ? '' : '请输入内容'\"\n                    v-model=\"scope.row.xlr\"\n                    :disabled=\"isDisabled\"\n                  ></el-input>\n                </template>\n              </el-table-column>\n\n              <el-table-column\n                align=\"center\"\n                prop=\"xlsj\"\n                label=\"下令时间\"\n                width=\"204\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-date-picker\n                    v-if=\"scope.row.isSet\"\n                    v-model=\"scope.row.xlsj\"\n                    :disabled=\"isDisabled\"\n                    type=\"datetime\"\n                    :placeholder=\"isDisabled ? '' : '请选择下令时间'\"\n                    format=\"yyyy-MM-dd HH:mm\"\n                    value-format=\"yyyy-MM-dd HH:mm\"\n                    style=\"width: 100%\"\n                  >\n                  </el-date-picker>\n                </template>\n              </el-table-column>\n\n              <el-table-column\n                align=\"center\"\n                prop=\"hlsj\"\n                label=\"回令时间\"\n                width=\"204\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-date-picker\n                    v-if=\"scope.row.isSet\"\n                    v-model=\"scope.row.hlsj\"\n                    :disabled=\"isDisabled\"\n                    type=\"datetime\"\n                    :placeholder=\"isDisabled ? '' : '请选择回令时间'\"\n                    format=\"yyyy-MM-dd HH:mm\"\n                    value-format=\"yyyy-MM-dd HH:mm\"\n                    style=\"width: 100%\"\n                  >\n                  </el-date-picker>\n                </template>\n              </el-table-column>\n              <el-table-column\n                type=\"sfwc\"\n                width=\"50\"\n                label=\"是否完成\"\n                align=\"center\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-checkbox\n                    v-model=\"scope.row.sfwc\"\n                    :disabled=\"isCheckAll\"\n                  ></el-checkbox>\n                </template>\n              </el-table-column>\n            </el-table>\n          </div>\n        </div>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!--图片放大展示弹出框-->\n    <el-image-viewer\n      v-if=\"imgDialogVisible\"\n      :on-close=\"onClose\"\n      :url-list=\"dialogImageUrl\"\n      zIndex=\"3000\"\n    />\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport ElImageViewer from \"element-ui/packages/image/src/image-viewer\";\nimport { getList, getCzpmxList ,remove } from \"@/api/yxgl/sdyxgl/sddzczp\";\nexport default {\n  name: \"czp_cx\",\n  components: { ElImageViewer },\n  data() {\n    return {\n      // 是否已执行下拉框\n      sfyzxList: [\n        { label: \"已执行\", value: \"已执行\" },\n        { label: \"未执行\", value: \"未执行\" }\n      ],\n      //上传图片时的请求头\n      header: {},\n      //图片list\n      imgList: [],\n      //上传图片时的携带的其他参数\n      uploadImgData: {\n        businessId: \"\" //携带的表单主键id\n      },\n      //图片地址url\n      dialogImageUrl: \"\",\n      //展示图片dialog控制\n      imgDialogVisible: false,\n\n      // 是否全选\n      checkAll: false,\n      // 全选框 是否禁用\n      isCheckAll: false,\n      isShowShr: false,\n      isShowSb: false,\n      isShowSh: false,\n      isShowHt: false,\n      isShowHtyj: false,\n      // 多选框选中的id\n      ids: [],\n      //弹出框中表格数据\n      propTableData: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //form表单\n      form: {\n        bh: \"\",\n        status: \"\",\n        czr: \"\",\n        jhr: \"\",\n        sdshr: \"\",\n        lx: 6, //输电\n        colFirst: [],\n        bz: \"\"\n      },\n      formCzp: {\n        sdshr: \"\"\n      },\n      //详情弹框是否显示\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      // 是否禁用  线路名称、序号、操作项目\n      isDisabledQt: false,\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          xlmc: \"\",\n          czr: \"\",\n          jhr: \"\",\n          fgssprmc: \"\",\n          rqArr: []\n        }, //查询条件\n        fieldList: [\n          { label: \"线路名称\", value: \"xlmc\", type: \"input\", clearable: true },\n          { label: \"操作人\", value: \"czr\", type: \"input\", clearable: true },\n          { label: \"监护人\", value: \"jhr\", type: \"input\", clearable: true },\n          {\n            label: \"审核人\",\n            value: \"fgssprmc\",\n            type: \"input\",\n            clearable: true\n          },\n          {\n            label: \"日期\",\n            value: \"rqArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 1,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"线路名称\", prop: \"xlmc\", minWidth: \"120\" },\n          { label: \"工作名称\", prop: \"gzmc\", minWidth: \"200\" },\n          { label: \"操作人\", prop: \"czr\", minWidth: \"120\" },\n          { label: \"监护人\", prop: \"jhr\", minWidth: \"120\" },\n          { label: \"审核人\", prop: \"fgssprmc\", minWidth: \"120\" },\n          { label: \"日期\", prop: \"rq\", minWidth: \"100\" },\n          { label: \"操作项数\", prop: \"czxs\", minWidth: \"80\" },\n          { label: \"已执行项数\", prop: \"yzxczxs\", minWidth: \"90\" },\n          { label: \"未执行项数\", prop: \"wzxczxs\", minWidth: \"90\" },\n          {\n            prop: \"operation\",\n            label: \"操作\",\n            fixed: \"right\",\n            minWidth: \"130px\",\n            style: { display: \"block\" },\n            operation: [{ name: \"详情\", clickFun: this.getDetails }]\n          }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        //输电\n        lx: 1,\n        status: \"4\",\n        mySorts: [{ prop: \"rq\", asc: false }]\n      },\n      selectRows: []\n    };\n  },\n  mounted() {\n    //获取token\n    this.header.token = getToken();\n    //列表查询\n    this.getData();\n    if (\"admin\" === this.$store.getters.name) {\n      let option = { name: \"删除\", clickFun: this.deleteRow };\n      this.tableAndPageInfo.tableHeader[9].operation.push(option);\n    }\n  },\n  methods: {\n    // 选择文件时，往fileList里添加\n    handleChange(file, fileList) {\n      this.imgList = fileList;\n    },\n    handleProgress(event, file, fileList) {\n      console.log(\"event\", event);\n      console.log(\"file\", file);\n      console.log(\"fileList\", fileList);\n    },\n    //图片移除\n    handleRemove(file, fileList) {\n      this.imgList = fileList;\n    },\n    //图片放大\n    handlePictureCardPreview(file) {\n      this.dialogImageUrl = [file.url];\n      this.imgDialogVisible = true;\n    },\n    onClose() {\n      this.imgDialogVisible = false;\n    },\n    // 全选按钮\n    handleCheckAllChange(val) {\n      console.log(this.propTableData);\n      this.propTableData.colFirst.forEach(item => {\n        item.sfwc = val;\n      });\n      //this.$refs.propTable.toggleAllSelection()\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        const { data, code } = await getList(param);\n        console.log(data);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n     //删除按钮\n    async deleteRow(row) {\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        remove(row.objId).then(({code}) => {\n          if (code === '0000') {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            })\n            //重置page页从1开始\n            this.tableAndPageInfo.pager.pageResize = 'Y'\n            this.getData()\n          } else {\n            this.$message({\n              type: 'error',\n              message: '删除失败!'\n            })\n          }\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        })\n      })\n    },\n    //重置按钮\n    getReset() {\n      this.params = {\n        //输电\n        lx: 1,\n        status: \"4\",\n        mySorts: [{ prop: \"rq\", asc: false }]\n      };\n    },\n    //选中行\n    handleSelectionChange() {},\n    //详情按钮\n    getDetails(row) {\n      this.getCzpmx(row);\n      this.title = \"输电操作票详情查看\";\n      this.form = { ...row };\n      this.imgList = this.form.imgList;\n      this.isCheckAll = true;\n      this.isDisabled = true;\n      this.isDisabledQt = true;\n      this.isShowDetails = true;\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false;\n      this.isShowShr = false;\n      this.isShowHtyj = false;\n    },\n    //选择行\n    selectChange(selection) {\n      this.ids = selection.map(item => item.objId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n      this.selection = selection;\n    },\n    /**\n     *获取操作票明细\n     */\n    async getCzpmx(row) {\n      try {\n        const { data, code } = await getCzpmxList({ objId: row.objId });\n        console.log(data);\n\n        if (code === \"0000\") {\n          //给list添加字段\n          data.forEach(item => {\n            this.$set(item, \"isSet\", true);\n          });\n          this.propTableData.colFirst = data;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.card-style {\n  background-color: #e0eff2;\n  line-height: 3vh;\n  border-radius: 3px;\n  font-weight: 800;\n  padding-left: 1vw;\n  margin-bottom: 3vh;\n}\n\n/*控制input输入框边框是否显示*/\n.elInput >>> .el-input__inner {\n  border: 0;\n}\n\n.center {\n  text-align: center; /*让div内部文字居中*/\n}\n</style>\n"]}]}