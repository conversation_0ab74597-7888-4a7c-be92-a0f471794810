{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\pjgzwh.vue?vue&type=style&index=0&id=16a049b2&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\pjgzwh.vue", "mtime": 1706897323226}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi8qIOiuvue9rua7muWKqOadoeeahOagt+W8jyAqLwo6Oi13ZWJraXQtc2Nyb2xsYmFyIHsKICB3aWR0aDogMTJweDsKfQoKLyog5rua5Yqo5qe9ICovCjo6LXdlYmtpdC1zY3JvbGxiYXItdHJhY2sgewovLy13ZWJraXQtYm94LXNoYWRvdzppbnNldDAwNnB4cmdiYSgwLDAsMCwwLjMpOwogIGJvcmRlci1yYWRpdXM6IDEwcHg7Cn0KCi8qIOa7muWKqOadoea7keWdlyAqLwo6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHsKICBib3JkZXItcmFkaXVzOiAxMHB4OwogIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC4xKTsKLy8td2Via2l0LWJveC1zaGFkb3c6Z2JhKDAsMCwwLDAuNSk7Cn0KCjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWI6d2luZG93LWluYWN0aXZlIHsKICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuMSk7Cn0KLmhlYWQtY29udGFpbmVyIHsKICBtYXJnaW46IDAgYXV0bzsKICB3aWR0aDogOTglOwogIGhlaWdodDogODMuNHZoOwogIG1heC1oZWlnaHQ6IDgzLjR2aDsKICBvdmVyZmxvdzogYXV0bzsKfQovKue7meW3puS+p+aVsOe7k+aehGhlYWRlcuWKoOminOiJsiovCi5ib3gtY2FyZCAuZWwtY2FyZF9faGVhZGVyIHsKICBiYWNrZ3JvdW5kOiAjMTFiYTZkICFpbXBvcnRhbnQ7Cn0KLmJveC1jYXJkIHsKICBtYXJnaW46MDsKfQoKLml0ZW0gewogIHdpZHRoOiAyMDBweDsKICBoZWlnaHQ6IDE0OHB4OwogIGZsb2F0OiBsZWZ0Owp9CgoudHJlZSB7CiAgb3ZlcmZsb3cteTogaGlkZGVuOwogIG92ZXJmbG93LXg6IHNjcm9sbDsKICB3aWR0aDogODBweDsKICBoZWlnaHQ6IDUwMHB4Owp9CgouZWwtdHJlZSB7CiAgbWluLXdpZHRoOiAxMDAlOwogIGRpc3BsYXk6IGlubGluZS1ibG9jayAhaW1wb3J0YW50Owp9Ci9kZWVwLyAuZWwtZGlhbG9nOm5vdCguaXMtZnVsbHNjcmVlbikgewogIG1hcmdpbi10b3A6IDh2aCAhaW1wb3J0YW50Owp9Cg=="}, {"version": 3, "sources": ["pjgzwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6eA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "pjgzwh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row :gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card style=\"background:#e0f8ed;padding-top:10px;\" shadow=\"never\">\n          <div slot=\"header\" class=\"clearfix\" style=\"text-align: center\">\n            <span>评价设备</span>\n          </div>\n          <div class=\"text head-container\">\n            <el-tree\n              highlight-current\n              :data=\"treedata\"\n              :props=\"defaultProps\"\n              @node-click=\"handleNodeClick\"\n              @node-expand=\"handleNodeClick\"\n            ></el-tree>\n          </div>\n        </el-card>\n      </el-col>\n\n      <el-col :span=\"20\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :btnHidden=\"false\"\n          :width=\"{ labelWidth: 80, itemWidth: 165 }\"\n          @handleReset=\"filterReset\"\n        />\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"getInster\"  :disabled=\"addDisabled\">新增</el-button>\n            <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"deleteRow\">删除</el-button>\n          </div>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"selectChange\"\n            height=\"70vh\"\n          />\n        </el-white>\n\n        <!--新增、修改、详情弹框-->\n        <el-dialog\n          :title=\"title\"\n          v-dialogDrag\n          :visible.sync=\"show\"\n          width=\"50%\"\n          append-to-body\n          @close=\"getInsterClose\"\n        >\n          <el-form label-width=\"130px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n            <el-row :gutter=\"8\" class=\"pull-left\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"评价导则：\" prop=\"sblxmc\">\n                  <el-input\n                    placeholder=\"请选择评价导则\"\n                    v-model=\"form.sblxmc\"\n                    style=\"width: 100%\"\n                    :disabled=\"true\"\n                  />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"部件名称：\" prop=\"bjmc\">\n                  <el-input\n                    placeholder=\"请选择部件名称\"\n                    v-model=\"form.bjmc\"\n                    style=\"width: 100%\"\n                    :disabled=\"true\"\n                  />\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"合计/单项：\" prop=\"hjdx\">\n                  <el-select\n                    placeholder=\"合计/单项\"\n                    v-model=\"form.hjdx\"\n                    style=\"width: 100%\"\n                     :disabled=\"isDisabled\"\n                  >\n                    <el-option\n                      v-for=\"item in hjdxList\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"下限逻辑符：\" prop=\"xxljf\">\n                  <el-select\n                    v-model=\"form.xxljf\"\n                    style=\"width: 100%\"\n                    :disabled=\"isDisabled\"\n                  >\n                    <el-option\n                      v-for=\"item in sxljfList\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n\n              <el-col :span=\"12\">\n                <el-form-item label=\"部件扣分下限：\" prop=\"fxss\">\n                  <el-input-number size=\"small\" v-model=\"form.fxss\" :disabled=\"isDisabled\" :min=\"0\" :precision=\"0\" controls-position=\"right\"></el-input-number>\n<!--                  <el-input-->\n<!--                    placeholder=\"请输入扣分\"-->\n<!--                    v-model=\"form.fxss\"-->\n<!--                    :disabled=\"isDisabled\"-->\n<!--                  />-->\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"上限逻辑符：\" prop=\"sxljf\">\n                  <el-select\n                    v-model=\"form.sxljf\"\n                    style=\"width: 100%\"\n                    :disabled=\"isDisabled\"\n                  >\n                    <el-option\n                      v-for=\"item in sxljfList\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n\n              <el-col :span=\"12\">\n                <el-form-item label=\"部件扣分上限：\" prop=\"fssx\">\n                  <el-input-number size=\"small\" v-model=\"form.fssx\" :disabled=\"isDisabled\" :min=\"1\" :precision=\"0\" controls-position=\"right\"></el-input-number>\n<!--                  <el-input v-model=\"form.fssx\" placeholder=\"部件扣分上限\"   :disabled=\"isDisabled\"/>-->\n                </el-form-item>\n              </el-col>\n\n              <el-col :span=\"12\">\n                <el-form-item label=\"评价结果：\" prop=\"pjjg\">\n                  <el-select\n                    v-model=\"form.pjjg\"\n                    style=\"width: 100%\"\n                    :disabled=\"isDisabled\"\n                  >\n                    <el-option\n                      v-for=\"item in pjjgList\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </el-form>\n          <div slot=\"footer\" class=\"dialog-footer\" v-if=\"!isDisabled\">\n            <el-button @click=\"getInsterClose\">关 闭</el-button>\n            <el-button type=\"primary\"  @click=\"saveRow\">保 存</el-button>\n          </div>\n        </el-dialog>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport {\n  getPageList,\n  saveOrUpdate,\n  remove,\n} from \"@/api/dagangOilfield/bzgl/sbztpjbzk/pjgzwh\";\nimport { getSblxAndSbbjTree } from \"@/api/dagangOilfield/bzgl/sbztpjbzk/ztlmxwh\";\nimport { getDictTypeData } from '@/api/system/dict/data'\n\nexport default {\n  name: \"pjgzwh\",\n  data() {\n    return {\n      //树结构\n      defaultProps: {\n        children: \"children\",\n        label: \"label\",\n      },\n      queryztlmxwhParam: {\n        pageNum: 1,\n        pageSize: 10,\n        sbbjId: \"\",\n        sblxId: \"\",\n      },\n      sbbjId:undefined,\n      selectRows: [],\n      //点击树节点赋值\n      treeForm: {},\n      //组织树\n      treedata: [],\n      //新增按钮控制\n      addDisabled: true,\n      //选中行数据\n      rowData: {},\n      //表单选中数据\n      selectedRowDataArr: [],\n      form: {\n        sbbjId: undefined,\n        sblxbm: undefined,\n        sblxmc: undefined,\n      },\n      title: \"\",\n      show: false,\n      isDisabled: false,\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true,\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"sblxmc\", label: \"评价导则\" },\n          { prop: \"bjmc\", label: \"评价部件\" },\n          { prop: \"hjCn\", label: \"合计/单项\" },\n          { prop: \"xxljf\", label: \"下限逻辑符\" },\n          { prop: \"fxss\", label: \"部件扣分下限\" },\n          { prop: \"sxljf\", label: \"上限逻辑符\" },\n          { prop: \"fssx\", label: \"部件扣分上限\" },\n          { prop: \"pjjgCn\", label: \"评价结果\" },\n          {\n            prop: \"operation\",\n            label: \"操作\",\n            minWidth: \"130px\",\n            style: { display: \"block\" },\n            //操作列固定再右侧\n            fixed: \"right\",\n            operation: [\n              { name: \"修改\", clickFun: this.updateRow },\n              { name: \"详情\", clickFun: this.getDetails },\n            ],\n          },\n        ],\n      },\n      pjjgList: [],//评价结果\n      sxljfList: [],//逻辑符\n      hjdxList: [],//合计/单项\n      sblxmc:\"\",//设备类型；\n      sblxId:\"\",//设备类型id，\n      sbbjmc:\"\",//设备部件\n      rules: {\n        hjdx:{required: true, message: \"合计/单项不能为空\", trigger: \"select\" },\n        pjjg:{required: true, message: \"评价结果不能为空\", trigger: \"select\" },\n        sxljf:{required: true, message: \"上限逻辑符不能为空\", trigger: \"select\" },\n        xxljf:{required: true, message: \"下限逻辑符不能为空\", trigger: \"select\" },\n        fssx:{required: true, message: \"部件扣分上限不能为空\", trigger: \"blur\" },\n        fxss:{required: true, message: \"部件扣分下限不能为空\", trigger: \"blur\"}\n      },\n      filterInfo: {\n        data: {\n          pjjg:'',\n          bjmc:'',\n          sblxmc:''\n        },\n        fieldList: [\n          { label: '评价导则', type: 'input', value: 'sblxmc' },\n          { label: '评价部件', type: 'input', value: 'bjmc'},\n          { label: '评价结果', type: 'select', value: 'pjjg',options:[]},\n        ]\n      },\n    };\n  },\n  created() {\n    this.getOptions();//获取涉及到的下拉框字典值\n  },\n  mounted() {\n    //列表查询\n    this.getTreeNode();\n    this.getData();\n  },\n  methods: {\n    //获取下拉框字典值\n    async getOptions(){\n      await this.getHjList();//合计/单项\n      await this.getLjfList();//逻辑符\n      await this.getPjjgList();//评价结果\n    },\n    //合计/单项\n    async getHjList(){\n      getDictTypeData('pjgz_hj').then(res=>{\n        res.data.forEach(item=>{\n          this.hjdxList.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    //逻辑符\n    async getLjfList(){\n      getDictTypeData('pjgh_ljf').then(res=>{\n        res.data.forEach(item=>{\n          this.sxljfList.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    //评价结果\n    async getPjjgList(){\n      getDictTypeData('pjgz_pjjg').then(res=>{\n        res.data.forEach(item=>{\n          this.pjjgList.push({label:item.label,value:item.numvalue})\n        })\n        this.filterInfo.fieldList.forEach(item=>{\n          if(item.value === 'pjjg'){\n            item.options = this.pjjgList;\n            return false;\n          }\n        })\n      })\n    },\n    //重置按钮\n    filterReset() {\n\n    },\n    //获取树节点数据\n    getTreeNode() {\n      getSblxAndSbbjTree({type:'sbcs'}).then((res) => {\n        if (res.code === \"0000\") {\n          this.treedata = res.data;\n        }\n      });\n    },\n    //树节点点击事件\n    handleNodeClick(data) {\n      //点击部件\n      if (data.nodeLevel == \"2\") {\n        this.addDisabled = false;\n        this.sbbjmc= data.label;\n        this.sbbjId=data.id;\n        // this.form.bjmc = data.label;\n        // this.form.sbbjId = data.id;\n        // this.sbbjId=data.id;\n        this.queryztlmxwhParam.sbbjId = data.id;\n        this.getData();\n      }\n      //点击设备类型\n      if (data.nodeLevel == \"1\") {\n        this.addDisabled = true;\n         this.sblxmc= data.label;\n         this.sblxId=data.id;\n         this.sbbjId='';\n        // this.form.sblxmc = data.label;\n        // this.form.sblx = data.id;\n        this.queryztlmxwhParam.sblx = data.id;\n        this.queryztlmxwhParam.sbbjId = '';\n        this.getData();\n      }\n      //点击根节点\n      if (data.nodeLevel == \"0\") {\n        this.queryztlmxwhParam = {};\n        this.queryztlmxwhParam.zy = data.label;\n        this.getData();\n      }\n    },\n\n    //列表查询\n    async getData(params) {\n      try {\n        this.queryztlmxwhParam = { ...this.queryztlmxwhParam, ...params };\n        const { data, code } = await getPageList(this.queryztlmxwhParam);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n        }\n      } catch (e) {\n\n      }\n    },\n\n    //删除按钮\n    async deleteRow() {\n      if (this.selectRows.length < 1) {\n        this.$message.warning(\"请选择正确的数据！！！\");\n        return;\n      }\n      let ids = this.selectRows.map((item) => {\n        return item.id;\n      });\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      })\n        .then(() => {\n          remove(ids).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\",\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\",\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\",\n          });\n        });\n      await this.getData();\n    },\n\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n\n   updateRow(row) {\n      this.title = \"修改\";\n      this.show = true;\n      this.isDisabled = false;\n      this.form = { ...row };\n    },\n\n    //详情\n    getDetails(row) {\n      this.title = \"详情\";\n      this.show = true;\n      this.isDisabled = true;\n      this.form = { ...row };\n    },\n    //选中行\n    handleSelectionChange(row) {\n      this.selectedRowDataArr = row;\n    },\n\n    //新增按钮\n    getInster() {\n      // if (this.form.sbbjId == undefined) {\n      //   this.$message.warning(\"请选择左侧对应设导则！！！\");\n      //   return;\n      // }\n      this.show = true;\n      this.isDisabled = false;\n      this.title = \"新增\";\n      this.form.bjmc=this.sbbjmc;\n      this.form.sbbjId=this.sbbjId;\n      this.form.sblxmc = this.sblxmc;\n      this.form.sblx = this.sblxId;\n\n    },\n    //保存按钮\n    async saveRow() {\n      await this.$refs['form'].validate((valid) => {\n        if (valid) {\n          try {\n            saveOrUpdate(this.form).then(res=>{\n              if (res.code === \"0000\") {\n                this.$message.success(\"操作成功\");\n              }\n              this.getData();\n              this.show = false;\n            });\n          } catch (e) {\n\n          }\n        }else{\n          this.$message.error(\"校验未通过！\");\n          return false;\n        }\n      })\n    },\n\n    //新增弹框关闭\n    getInsterClose() {\n      this.show = false;\n      this.form = {};\n    },\n  },\n};\n</script>\n\n<style lang='scss' scoped>\n/* 设置滚动条的样式 */\n::-webkit-scrollbar {\n  width: 12px;\n}\n\n/* 滚动槽 */\n::-webkit-scrollbar-track {\n//-webkit-box-shadow:inset006pxrgba(0,0,0,0.3);\n  border-radius: 10px;\n}\n\n/* 滚动条滑块 */\n::-webkit-scrollbar-thumb {\n  border-radius: 10px;\n  background: rgba(0, 0, 0, 0.1);\n//-webkit-box-shadow:gba(0,0,0,0.5);\n}\n\n::-webkit-scrollbar-thumb:window-inactive {\n  background: rgba(0, 0, 0, 0.1);\n}\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  height: 83.4vh;\n  max-height: 83.4vh;\n  overflow: auto;\n}\n/*给左侧数结构header加颜色*/\n.box-card .el-card__header {\n  background: #11ba6d !important;\n}\n.box-card {\n  margin:0;\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.tree {\n  overflow-y: hidden;\n  overflow-x: scroll;\n  width: 80px;\n  height: 500px;\n}\n\n.el-tree {\n  min-width: 100%;\n  display: inline-block !important;\n}\n/deep/ .el-dialog:not(.is-fullscreen) {\n  margin-top: 8vh !important;\n}\n</style>\n"]}]}