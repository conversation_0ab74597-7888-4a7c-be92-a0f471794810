{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\tablePdf.vue?vue&type=style&index=0&id=01f5a14a&scoped=true&lang=scss&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\tablePdf.vue", "mtime": 1706897323692}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLnNhdmVDb250IHsKICB3aWR0aDogOTkuNSU7CiAgb3ZlcmZsb3c6IGF1dG87CiAgcGFkZGluZzogMCA0MHB4OwogIC5wcmludFRpdGxlIHsKICAgIGxpbmUtaGVpZ2h0OiAzNXB4OwoKICB9CgogIC5oMV90YWJsZSB7CiAgICB3aWR0aDogMTAwJTsKICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICAgIGJvcmRlci1yaWdodDoxcHggc29saWQgIzAwMDsKICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjMDAwOwoKICAgIHRyIHsKICAgICAgbGluZS1oZWlnaHQ6IDM1cHg7CgogICAgICB0ZCB7CiAgICAgICAgYm9yZGVyLWxlZnQ6IDFweCBzb2xpZCAjMDAwOwogICAgICAgIGJvcmRlci10b3A6IDFweCBzb2xpZCAjMDAwCiAgICAgIH0KICAgIH0KICB9CgogIC5oMl90YWJsZSwKICAuaDNfdGFibGUgewogICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICMwMDA7CgogICAgdHIgewoKICAgICAgLy90ZDpudGgtY2hpbGQoMSl7CiAgICAgIC8vICBib3JkZXItbGVmdDpub25lOwogICAgICAvL30KICAgICAgdGQgewogICAgICAgIGJvcmRlci1sZWZ0OiAxcHggc29saWQgIzAwMDsKICAgICAgICBib3JkZXItdG9wOiAxcHggc29saWQgIzAwMAogICAgICB9CiAgICB9CgogICAgdGV4dC1hbGlnbjogY2VudGVyOwogIH0KCgogIC9kZWVwLyAuaDJfdGFibGUgdHIsCiAgL2RlZXAvIC5oM190YWJsZSB0ciB7CiAgICBoZWlnaHQ6IDM1cHg7CiAgfQoKICAvZGVlcC8gLmgyX3RhYmxlIHRkLAogIC9kZWVwLyAuaDNfdGFibGUgdGQgewogICAgYm9yZGVyOiAxcHggc29saWQgIzAwMAogIH0KCiAgL2RlZXAvIC5oMl90YWJsZSBpbnB1dCwKICAvZGVlcC8gLmgzX3RhYmxlIGlucHV0IHsKICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICAgIGhlaWdodDogMzVweDsKICB9Cn0KCgo="}, {"version": 3, "sources": ["tablePdf.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyGA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "tablePdf.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk", "sourcesContent": ["<template>\n  <div v-loading=\"!basicData\">\n    <div class=\"saveCont\">\n      <h3 style=\"font-size: 26px;text-align: center\">{{ basicData.symb }}</h3>\n      <div class=\"saveCont1\">\n        <!--  第一个表格内容   -->\n        <div class=\"printTitle\">一、基本信息</div>\n        <table class=\"h1_table\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n          <tr>\n            <td>变电站</td>\n            <td>{{ this.basicData.sydd }}</td>\n            <td>委托单位</td>\n            <td>{{this.basicData.wtdw}}</td>\n            <td>试验单位</td>\n            <td>{{this.basicData.sydwmc}}</td>\n            <td>运行编号</td>\n            <td>{{ this.basicData.yxbh }}</td>\n          </tr>\n          <tr>\n            <td>试验性质</td>\n            <td>{{ this.basicData.syxz }}</td>\n            <td>试验日期</td>\n            <td>{{ this.basicData.syrq }}</td>\n            <td>试验人员</td>\n            <td>{{ this.basicData.syryid }}</td>\n            <td>试验地点</td>\n            <td>{{ this.basicData.sydd }}</td>\n          </tr>\n          <tr>\n            <td>报告日期</td>\n            <td>{{this.basicData.createTime.substring(0,10)}}</td>\n            <td>编写人</td>\n            <td>{{ this.basicData.bzrid }}</td>\n            <td>审核人</td>\n            <td>{{ this.basicData.bzzsp }}</td>\n            <td>批准人</td>\n            <td>{{ this.basicData.ssdwldmc }}</td>\n          </tr>\n          <tr>\n            <td>试验天气</td>\n            <td style=\"min-width: 60px;\">{{ this.basicData.tq }}</td>\n            <td>环境温度（℃）</td>\n            <td style=\"min-width: 60px;\">{{ this.basicData.wd }}</td>\n            <td>环境相对湿度（%）</td>\n            <td style=\"min-width: 60px;\">{{ this.basicData.sd }}</td>\n            <td>投运日期</td>\n            <td style=\"min-width: 60px;\">{{ this.basicData.tyrq }}</td>\n          </tr>\n        </table>\n        <!--  第二个表格内容   -->\n        <div class=\"printTitle\">二、设备铭牌</div>\n        <table class=\"h2_table\" style=\"width: 100%; margin-top: 2px; border-collapse: collapse;\"\n               v-html=\"tablebox2\">\n\n              </table>\n        <!--  第三个表格内容   -->\n        <div class=\"printTitle\">三、试验数据</div>\n        <table class=\"h3_table\" style=\"width: 100%; margin-top: 2px; border-collapse: collapse;\"\n               v-html=\"tablebox3\"></table>\n      </div>\n    </div>\n  </div>\n</template>\n<script>\nexport default {\n  name: 'tablePdf',\n  props: {\n    basicData: {\n      type: Object,\n      required: true\n    },\n    tablebox2: {\n      type: String,\n      required: false,\n      default: ''\n    },\n    tablebox3: {\n      type: String,\n      required: false,\n      default: ''\n    }\n  },\n  methods: {\n    getParams(){\n      let input_h2 = document.getElementsByClassName(\"h2_table\");\n      let input_h3 = document.getElementsByClassName(\"h3_table\");\n      let h2Data = []; //第二个表格数据\n      let h3Data = []; //第三个表格数据\n      for (let i = 0; i < input_h2.length; i++) {\n        h2Data.push(input_h2[i].id + \"|\" + input_h2[i].value);\n      }\n      for (let i = 0; i < input_h3.length; i++) {\n        h3Data.push(input_h3[i].id + \"|\" + input_h3[i].value);\n      }\n      return {\n        table2: h2Data,\n        table3: h3Data\n      };\n    },\n  },\n}\n\n</script>\n<style scoped lang=\"scss\">\n\n.saveCont {\n  width: 99.5%;\n  overflow: auto;\n  padding: 0 40px;\n  .printTitle {\n    line-height: 35px;\n\n  }\n\n  .h1_table {\n    width: 100%;\n    text-align: center;\n    border-right:1px solid #000;\n    border-bottom: 1px solid #000;\n\n    tr {\n      line-height: 35px;\n\n      td {\n        border-left: 1px solid #000;\n        border-top: 1px solid #000\n      }\n    }\n  }\n\n  .h2_table,\n  .h3_table {\n    border-bottom: 1px solid #000;\n\n    tr {\n\n      //td:nth-child(1){\n      //  border-left:none;\n      //}\n      td {\n        border-left: 1px solid #000;\n        border-top: 1px solid #000\n      }\n    }\n\n    text-align: center;\n  }\n\n\n  /deep/ .h2_table tr,\n  /deep/ .h3_table tr {\n    height: 35px;\n  }\n\n  /deep/ .h2_table td,\n  /deep/ .h3_table td {\n    border: 1px solid #000\n  }\n\n  /deep/ .h2_table input,\n  /deep/ .h3_table input {\n    display: inline-block;\n    height: 35px;\n  }\n}\n\n\n</style>>\n"]}]}