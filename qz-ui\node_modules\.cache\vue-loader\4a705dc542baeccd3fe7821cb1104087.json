{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\xqxbzk.vue?vue&type=style&index=0&id=6b15476e&scoped=true&lang=css&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\xqxbzk.vue", "mtime": 1706897323967}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5maWx0ZXItYm9yZGVyIHsKICBib3JkZXItYm90dG9tOiAjZjVmOGZkIHNvbGlkIDFweDsKfQoKLmZvcm0taXRlbSB7CiAgd2lkdGg6IDgwJTsKfQoKLmZvcm0taXRlbS1yb3cgewogIHdpZHRoOiA5MiU7Cn0K"}, {"version": 3, "sources": ["xqxbzk.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkmBA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "xqxbzk.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n      <el-filter\n        :data=\"filterInfo.data\"\n        :field-list=\"filterInfo.fieldList\"\n        @onfocusEvent=\"inputFocusEvent\"\n      />\n    <div>\n<!--      <el-white class=\"button-group\">\n        <el-row :gutter=\"3\" class=\"pull-right\">\n          <el-col style=\"\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n          \">-->\n            <el-white class=\"button-group\">\n              <div class=\"button_btn\">\n                <el-button type=\"primary\" v-hasPermi=\"['bzqxbzkxz:button:add']\" icon=\"el-icon-plus\" @click=\"handleAdd\">新增</el-button>\n                <el-button type=\"danger\" v-hasPermi=\"['bzqxbzkxz:button:delete']\" icon=\"el-icon-delete\" @click=\"handleDelete\">删除</el-button>\n              </div>\n              <comp-table :table-and-page-info=\"tableAndPageInfo\" @update:multipleSelection=\"handleSelectionChange\" height=\"68vh\">\n              <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\" width=\"160\"\n                               :resizable=\"false\">\n                <template slot-scope=\"scope\">\n                  <el-button @click=\"getDetails(scope.row)\" v-hasPermi=\"['bzqxbzkxz:button:update']\" type=\"text\" size=\"small\" class='el-icon-edit' title=\"修改\"></el-button>\n                  <el-button @click=\"getDetailsInfo(scope.row)\" type=\"text\" size=\"small\" title=\"详情\" class=\"el-icon-view\"></el-button>\n                </template>\n              </el-table-column>\n              </comp-table>\n            </el-white>\n    </div>\n\n\n    <!-- 新增/修改/详情 对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" append-to-body width=\"50%\" @close=\"handleClose\" v-dialogDrag>\n      <el-form ref=\"form\"  :model=\"form\" label-width=\"80px\" :rules=\"rules\">\n        <el-tabs tab-position=\"left\" style=\"height: 100%;\">\n          <el-tab-pane label=\"基本信息\">\n                <el-row :gutter=\"10\">\n                  <el-col :span=\"12\">\n<!--                    <el-form-item v-show=\"false\">-->\n<!--                      <el-input v-model=\"form.sblx\"></el-input>-->\n<!--                      <el-input v-model=\"form.objId\"></el-input>-->\n<!--                    </el-form-item>-->\n                    <el-form-item label=\"设备种类:\" prop=\"sblxmc\">\n                      <el-input @focus=\"showDeviceTreeDialog\" clearable v-model=\"form.sblxmc\" class=\"form-item\"\n                                :disabled=\"!formIsEditable\" placeholder=\"请选择设备种类\"></el-input>\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"12\">\n                    <el-form-item label=\"设备部件:\" prop=\"sbbm\">\n                      <el-input :disabled=\"!formIsEditable\" class=\"form-item\" v-model=\"form.sbbm\" clearable placeholder=\"请输入设备部件\"></el-input>\n                    </el-form-item>\n                  </el-col>\n                </el-row>\n                <el-row :gutter=\"10\">\n                  <el-col :span=\"12\">\n                    <el-form-item label=\"设备部位:\" prop=\"sbbw\">\n                      <el-input :disabled=\"!formIsEditable\" class=\"form-item\" v-model=\"form.sbbw\" clearable placeholder=\"请输入设备部位\"></el-input>\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"12\">\n                    <el-form-item label=\"隐患等级:\" prop=\"qxdj\">\n                      <el-select :disabled=\"!formIsEditable\" class=\"form-item\" v-model=\"form.qxdj\" clearable\n                                 placeholder=\"请输入隐患等级\">\n                        <el-option\n                          v-for=\"item in defectLevelOptions\"\n                          :key=\"item.value\"\n                          :label=\"item.label\"\n                          :value=\"item.value\">\n                        </el-option>\n                      </el-select>\n                    </el-form-item>\n                  </el-col>\n                </el-row>\n                <el-row :gutter=\"10\">\n\n                  <el-col :span=\"24\">\n                    <el-form-item label=\"缺陷描述:\" prop=\"qxms\">\n                      <el-input :disabled=\"!formIsEditable\" class=\"form-item-row\" v-model=\"form.qxms\" clearable\n                                placeholder=\"请输入缺陷描述\"></el-input>\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"24\">\n                    <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                      <el-input :disabled=\"!formIsEditable\" class=\"form-item-row\" v-model=\"form.flyj\" clearable\n                                placeholder=\"请输入分类依据\"></el-input>\n                    </el-form-item>\n                  </el-col>\n                </el-row>\n                <el-row :gutter=\"10\">\n                  <el-col :span=\"24\">\n                    <el-form-item label=\"技术原因:\" prop=\"jsyy\">\n                      <el-input class=\"form-item-row\" :disabled=\"!formIsEditable\" placeholder=\"请输入技术原因\"\n                                v-model=\"form.jsyy\"></el-input>\n                    </el-form-item>\n                  </el-col>\n                </el-row>\n          </el-tab-pane>\n        </el-tabs>\n\n      </el-form>\n      <div v-show=\"formIsEditable\" slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"handleClose\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitForm\">确 认</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 导入上传文件对话框 -->\n    <el-dialog title=\"上传附件\" :before-close=\"uploadClose\" :visible.sync=\"uploadDialogOpen\" width=\"50%\" v-dialogDrag>\n      <el-form ref=\"form\" :model=\"form\" label-width=\"80px\">\n        <el-form-item label=\"上传附件:\" prop=\"attachmentid\">\n          <el-upload\n            class=\"upload-demo\"\n            accept=\".jpg,.png,.rar,.txt,.zip,.doc,.ppt,.xls,.pdf,.docx,.xlsx,.mp4,.avi,.rmvb\"\n            ref=\"upload\"\n            :headers=\"header\"\n            action=\"/isc-api/file/upload\"\n            :before-upload=\"beforeUpload\"\n            :data=\"uploadData\"\n            multiple\n            :auto-upload=\"false\">\n            <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选取文件</el-button>\n            <el-button style=\"margin-left: 10px;\" size=\"small\" type=\"success\" @click=\"submitUpload\">上传到服务器</el-button>\n          </el-upload>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"uploadClose\" size=\"small\">关闭</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog\n      :append-to-body=\"true\"\n      title=\"设备分类\"\n      :visible.sync=\"showDeviceTree\"\n      width=\"400px\"\n      v-dialogDrag\n      v-if=\"showDeviceTree\">\n      <device-tree\n        @getDeviceTypeData=\"getDeviceTypeData\"\n        @closeDeviceTypeDialog=\"closeDeviceTypeDialog\">\n      </device-tree>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport { getPageDataList, remove, saveOrUpdate } from '@/api/dagangOilfield/bzgl/qxbzk'\nimport { getDictTypeData } from '@/api/system/dict/data'\nimport DeviceTree from '@/views/dagangOilfield/bzgl/sbbzk/deviceTree'\nimport { getEquipmentComponentsOptions } from '@/api/dagangOilfield/bzgl/sblxwh/sbbj'\n\nexport default {\n  name: 'xqxbzk',\n  components: { DeviceTree },\n  created() {\n    this.getData()\n  },\n  data() {\n    return {\n      //上传图片时的请求头\n      header: {},\n      filterInfo: {\n        data: {\n          sbbm:\"\",\n          sbbw:\"\",\n          qxms:\"\",\n          flyj:\"\",\n          jsyy:\"\",\n          dysblx: '',  //设备类型名称\n          sblx:\"\"\n        },\n        fieldList: [\n          { label: '设备种类', type: 'input', value: 'dysblx' },\n          { label: '设备部件', type: 'input', value: 'sbbm' },\n          { label: '设备部位', type: 'input', value: 'sbbw' },\n          { label: '缺陷描述', type: 'input', value: 'qxms' },\n          { label: '分类依据', type: 'input', value: 'flyj' },\n          {\n            label: '隐患等级',\n            type: 'select',\n            value: 'qxdjList',\n            multiple: true,\n            options: [{ label: '一般', value: '一般' }, { label: '严重', value: '严重' }, { label: '危急', value: '危急' }]\n          },\n          { label: '技术原因', type: 'input', value: 'jsyy' },\n\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          // { prop: 'zymc', label: '专业', minWidth: '120' },\n          { prop: 'sblxmc', label: '设备种类', minWidth: '180' },\n          { prop: 'sbbm', label: '设备部件', minWidth: '120' },\n          { prop: 'sbbw', label: '设备部位', minWidth: '120' },\n          { prop: 'qxms', label: '缺陷描述', minWidth: '120' },\n          { prop: 'flyj', label: '分类依据', minWidth: '120' },\n          { prop: 'qxdj', label: '隐患等级', minWidth: '120' },\n          { prop: 'jsyy', label: '技术原因', minWidth: '120' },\n         /* {\n            prop: 'operation',\n            label: '操作',\n            minWidth: '130px',\n            style: { display: 'block' },\n            //操作列固定再右侧\n            fixed: 'right',\n            operation: [\n              { name: '修改', clickFun: this.getDetails },\n              { name: '详情', clickFun: this.getDetailsInfo }\n\n            ]\n          }*/\n        ]\n      },\n      // 是否显示筛选条件\n      isSearchShow: false,\n      //查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        qxdjList: [],\n        sbbm:\"\",\n        sbbw:\"\",\n        qxms:\"\",\n        flyj:\"\",\n        jsyy:\"\",\n        dysblx: '',  //设备类型名称\n        sblx:\"\"\n\n      },\n      // 专业下拉框数据\n      specialtyOptions: [],\n      // 部件下拉框数据\n      partsOptions: [],\n\n      // 隐患等级下拉框数据\n      defectLevelOptions: [{ value: '一般', label: '一般' }, { value: '严重', label: '严重' }, { value: '危急', label: '危急' }],\n      // 多选框选中的数据id列表\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 标准缺陷库表格数据\n\n      // 对话框标题\n      title: '',\n      // 新增/修改/详情对话框是否打开\n      open: false,\n      // 上传附件对话框是否打开\n      uploadDialogOpen: false,\n      // 新增/修改提交的表单\n      form: {\n        objId: '',\n        zy: undefined,\n        sbzl: undefined,\n        sbbj: undefined,\n        qxbw: undefined,\n        qxms: undefined,\n        qxdj: undefined,\n        jsyy: undefined,\n        zryy: undefined,\n        createTime: undefined,\n        sblx: '',\n        sblxmc: '',\n        flyj: undefined,\n        sbbm: ''\n      },\n      // 新增/修改时对话框组件可编辑，详情时不可编辑\n      formIsEditable: false,\n      // 折叠面板展开的面板\n      activeNames: ['baseInfo'],\n      // 文件上传数据\n      uploadData: {},\n      // 文件上传请求头\n      upHeader: {},\n      // 表格共查询出的条数\n      total: 0,\n      //部件是否可编辑\n      partDisabled: false,\n      //展示设备分类树\n      showDeviceTree: false,\n      isFilter: false,\n      rules: {\n        sblxmc: [\n          {required: true, message: \"设备种类不能为空\", trigger: \"blur\"},\n        ],\n        sbbm: [\n          {required: true, message: \"设备部件\", trigger: \"blur\"},\n        ],\n        sbbw: [\n          {required: true, message: \"设备部位不能为空\", trigger: \"blur\"},\n        ],\n        qxdj: [\n          {required: true, message: \"隐患等级不能为空\", trigger: \"select\"},\n        ],\n        qxms: [\n          {required: true, message: \"缺陷描述不能为空\", trigger: \"blur\"},\n        ],\n        flyj: [\n          {required: true, message: \"分类依据不能为空\", trigger: \"blur\"},\n        ],\n        jsyy: [\n          {required: true, message: \"技术原因不能为空\", trigger: \"blur\"},\n        ],\n      },\n    }\n  },\n  mounted() {\n    //获取token\n    this.header.token = getToken();\n    this.initDomain()\n  },\n  methods: {\n    //筛选条件鼠标聚焦事件\n    inputFocusEvent(val) {\n      if (val.target.name === 'dysblx') {\n        this.showDeviceTree = true\n        this.isFilter = true\n      }\n    },\n\n    /**\n     * 查询数据\n     */\n    async getData(params) {\n      this.queryParams={...this.queryParams,...params}\n      const param = {...this.queryParams, ...params}\n      const {data, code} = await getPageDataList(param);\n      if (code === '0000') {\n        console.log(data)\n        this.tableAndPageInfo.tableData = data.records\n        this.tableAndPageInfo.pager.total = data.total\n      }\n    },\n    getDetails(row) {\n      this.open = true\n      this.formIsEditable = true\n      this.partDisabled = false\n      // if (row.sblx !== '' && row.sblx !== null) {\n      //   this.getPartOptions(row.sblx)\n      // }\n      this.form = row\n    },\n    getDetailsInfo(row) {\n      this.open = true\n      this.formIsEditable = false\n      this.partDisabled = true\n      this.form = row\n    },\n    /**\n     * 表格多选框\n     */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.objId)\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n    },\n\n    // /**\n    //  * 查询按钮\n    //  */\n    // handleQuery() {\n    //   this.queryParams.pageNum = 1\n    //   this.getData()\n    // },\n    //\n    // /**\n    //  * 重置按钮\n    //  */\n    // resetQuery() {\n    //   this.resetForm('queryForm')\n    //   this.handleQuery()\n    // },\n\n    /**\n     * 新增按钮\n     */\n    handleAdd() {\n      // 设置弹出框表单可编辑\n      this.formIsEditable = true\n      this.title = '新增标准缺陷'\n      this.open = true\n    },\n\n    /**\n     * 修改按钮\n     * @param row\n     */\n    handleEdit(row) {\n      // 设置弹出框表单可编辑\n      this.formIsEditable = true\n      // 将修改行的数据赋值到form上\n      for (let rowKey in row) {\n        this.form[rowKey] = row[rowKey]\n      }\n      this.title = '修改标准缺陷'\n      this.open = true\n    },\n\n    /**\n     * 处理批量删除\n     */\n    handleDelete() {\n      if (this.ids.length !== 0) {\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove(this.ids).then(res => {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            });\n            this.getData();\n          })\n        })\n      } else {\n        this.$message({\n          type: 'info',\n          message: '请选择至少一条数据!'\n        })\n      }\n\n    },\n\n    /**\n     * 导出按钮\n     */\n    handleExport() {\n      console.log('导出')\n    },\n\n    /**\n     * 导入按钮\n     */\n    handleImport() {\n      this.uploadDialogOpen = true\n    },\n\n    /**\n     * 查看详情\n     * @param row\n     */\n    handleDetails(row) {\n      this.formIsEditable = false\n      // 将行数据赋值到form上\n      for (let rowKey in row) {\n        this.form[rowKey] = row[rowKey]\n      }\n      this.title = '标准缺陷详情'\n      this.open = true\n    },\n\n    /**\n     * 关闭对话框\n     */\n    handleClose() {\n      this.reset()\n      this.open = false\n      this.title = ''\n    },\n\n    /**\n     * 关闭上传附件对话框\n     */\n    uploadClose() {\n      this.reset()\n      this.uploadDialogOpen = false\n    },\n\n    /**\n     * 提交表单\n     */\n    submitForm() {\n      this.$refs['form'].validate((valid) => {\n        if (valid) {\n          saveOrUpdate(this.form).then((response) => {\n            this.$message.success('保存成功！')\n            this.getData()\n            this.open = false\n          })\n        }else{\n          this.$message.error(\"校验未通过！\");\n          return false;\n        }\n      })\n    },\n\n    /**\n     * 表单重置\n     */\n    reset() {\n      this.form = {\n        id: undefined,\n        maintainType: undefined,\n        specialty: undefined,\n        deviceType: undefined,\n        parts: undefined,\n        defectPart: undefined,\n        defectDescription: undefined,\n        defectGist: undefined,\n        defectLevel: undefined,\n        reason: undefined,\n        createTime: undefined\n      }\n      this.resetForm('form')\n    },\n\n    /**\n     * 上传文件之前的处理\n     */\n    beforeUpload(file) {\n      const fileSize = file.size < 1024 * 1024 * 50 //10M\n      if (!fileSize) {\n        this.$message.error('上传文件大小不能超过 50MB!')\n      }\n      let size = file.size / 1024\n    },\n\n    /**\n     * 上传文件到服务器\n     */\n    submitUpload() {\n      this.$refs.upload.submit()\n    },\n\n    async initDomain() {\n\n      let { data: majorOptions } = await getDictTypeData('major')\n      this.specialtyOptions = majorOptions\n\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.value === 'specialty') {\n          item.options = majorOptions\n        }\n      })\n    },\n    //打开弹出开关\n    showDeviceTreeDialog() {\n      //从新增框内打开\n      this.isFilter = false\n      this.showDeviceTree = true\n    },\n    //反回设备类型选中的数据\n    getDeviceTypeData(res) {\n      console.log(\"res：\",res)\n      if (this.isFilter) {\n        console.log(\"筛选框选择设备类型,\",res)\n        let sblxArr = []\n        this.filterInfo.data.dysblx = ''\n        res.forEach(item => {\n          if (item.checked) {\n            sblxArr.push(item.code)\n            this.filterInfo.data.sblx = sblxArr.join(',')\n            this.filterInfo.data.dysblx += item.name + ','\n          }\n        })\n        this.filterInfo.data.dysblx = this.filterInfo.data.dysblx.substring(0, this.filterInfo.data.dysblx.length - 1)\n        this.showDeviceTree = false\n      } else {\n        console.log(\"新增框选择设备类型,\",res)\n        let treeNodes = []\n        res.forEach(item => {\n          if (item.checked) {\n            treeNodes.push(item)\n          }\n        })\n        if (treeNodes.length === 1) {\n          this.form.sblxmc = treeNodes[0].name\n          this.form.sblx = treeNodes[0].code\n          this.showDeviceTree = false\n        } else {\n          this.$message.warning('请选择单条设备类型')\n        }\n      }\n    },\n    //关闭设备类型选中框\n    closeDeviceTypeDialog() {\n      this.showDeviceTree = false\n    },\n    getPartOptions(sblx) {\n      getEquipmentComponentsOptions(sblx).then(res => {\n        this.partsOptions = res.data\n      })\n    }\n  },\n  // watch: {\n  //   'form.sblxmc'(val) {\n  //     this.partDisabled = val === '' || val === null\n  //   }\n  // }\n}\n</script>\n\n<style scoped>\n.filter-border {\n  border-bottom: #f5f8fd solid 1px;\n}\n\n.form-item {\n  width: 80%;\n}\n\n.form-item-row {\n  width: 92%;\n}\n</style>\n"]}]}