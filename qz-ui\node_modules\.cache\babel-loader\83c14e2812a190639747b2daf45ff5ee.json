{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\pdqxwh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\pdqxwh.vue", "mtime": 1726318105379}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["pdqxwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAi0BA;;AAeA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,QADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,IAAA,EAAA,KADA;AAEA,MAAA,OAAA,EAAA,KAFA;AAEA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,IAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA,EAFA;AAGA,UAAA,IAAA,EAAA,EAHA;AAIA,UAAA,IAAA,EAAA,EAJA;AAKA,UAAA,IAAA,EAAA,EALA;AAMA,UAAA,IAAA,EAAA;AANA,SADA;AASA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SANA;AATA,OAHA;AAqBA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA;AAZA,OArBA;AA2CA,MAAA,WAAA,EAAA,EA3CA;AA4CA,MAAA,WAAA,EAAA,EA5CA;AA4CA;AACA,MAAA,YAAA,EAAA,EA7CA;AA6CA;AACA,MAAA,YAAA,EAAA,KA9CA;AA+CA,MAAA,UAAA,EAAA,KA/CA;AA+CA;AACA,MAAA,UAAA,EAAA,KAhDA;AAiDA,MAAA,UAAA,EAAA,KAjDA;AAkDA,MAAA,UAAA,EAAA,KAlDA;AAmDA,MAAA,QAAA,EAAA,EAnDA;AAmDA;AACA,MAAA,SAAA,EAAA;AACA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,UAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,UAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAVA;AAaA,QAAA,UAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAbA;AAgBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAhBA;AAmBA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AAnBA,OApDA;AAwEA;AACA,MAAA,QAAA,EAAA,EAzEA;AAyEA;AACA,MAAA,SAAA,EAAA;AACA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,UAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,UAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAVA;AAaA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAbA;AAgBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAhBA;AAmBA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AAnBA,OA1EA;AA8FA;AACA,MAAA,QAAA,EAAA,EA/FA;AA+FA;AACA,MAAA,SAAA,EAAA;AACA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,UAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAVA;AAaA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAbA;AAgBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAhBA;AAmBA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AAnBA,OAhGA;AAoHA;AACA,MAAA,QAAA,EAAA,EArHA;AAqHA;AACA,MAAA,SAAA,EAAA;AACA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAVA;AAaA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAbA;AAgBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAhBA;AAmBA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AAnBA,OAtHA;AA0IA;AACA,MAAA,QAAA,EAAA,EA3IA;AA2IA;AACA,MAAA,QAAA,EAAA,EA5IA;AA4IA;AACA,MAAA,QAAA,EAAA,EA7IA;AA6IA;AACA,MAAA,QAAA,EAAA,EA9IA;AA8IA;AACA,MAAA,QAAA,EAAA,EA/IA;AA+IA;AACA,MAAA,QAAA,EAAA,EAhJA;AAgJA;AACA,MAAA,IAAA,EAAA,GAjJA;AAiJA;AACA,MAAA,UAAA,EAAA,EAlJA;AAkJA;AACA,MAAA,QAAA,EAAA,EAnJA;AAmJA;AACA,MAAA,OAAA,EAAA;AApJA,KAAA;AAsJA,GAzJA;AA0JA,EAAA,KAAA,EAAA;AACA,IAAA,UADA,sBACA,GADA,EACA;AACA,WAAA,KAAA,CAAA,IAAA,CAAA,MAAA,CAAA,GAAA;AACA;AAHA,GA1JA;AA+JA,EAAA,OA/JA,qBA+JA;AACA,SAAA,WAAA,CAAA,IAAA,GAAA,KAAA,IAAA;AACA,SAAA,OAAA;AACA,SAAA,WAAA,GAHA,CAIA;;AACA,SAAA,WAAA,GALA,CAMA;;AACA,SAAA,WAAA;AACA,GAvKA;AAwKA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,WAFA,yBAEA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,yBAAA;AAAA,kBAAA,IAAA,EAAA,KAAA,CAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,KAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,KANA;AAOA;AACA,IAAA,WARA,uBAQA,IARA,EAQA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,yBAAA;AAAA,kBAAA,IAAA,EAAA,MAAA,CAAA,IAAA;AAAA,kBAAA,IAAA,EAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,KAZA;AAaA;AACA,IAAA,WAdA,uBAcA,IAdA,EAcA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,yBAAA;AAAA,kBAAA,IAAA,EAAA,MAAA,CAAA,IAAA;AAAA,kBAAA,IAAA,EAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,KAlBA;AAmBA;AACA,IAAA,WApBA,uBAoBA,IApBA,EAoBA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,yBAAA;AAAA,kBAAA,IAAA,EAAA,MAAA,CAAA,IAAA;AAAA,kBAAA,IAAA,EAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,KAxBA;AAyBA;AACA,IAAA,WA1BA,uBA0BA,IA1BA,EA0BA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,yBAAA;AAAA,kBAAA,IAAA,EAAA,MAAA,CAAA,IAAA;AAAA,kBAAA,IAAA,EAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,KA9BA;AA+BA;AACA,IAAA,WAhCA,yBAgCA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAEA,2BAAA,aAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA,CADA,CAEA;;AACA,kBAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,wBAAA,IAAA,CAAA,KAAA,IAAA,MAAA,EAAA;AACA,sBAAA,IAAA,CAAA,OAAA,GAAA,MAAA,CAAA,QAAA;AACA;AACA,mBAJA;AAKA,iBARA,CAFA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KA3CA;AA4CA;AACA,IAAA,SA7CA,qBA6CA,GA7CA,EA6CA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA,gBAAA,MAAA,CAAA,OAAA,GAAA,mBAAA,OAAA,CAAA;AACA,kBAAA,IAAA,EAAA,IADA;AACA;AACA,kBAAA,IAAA,EAAA,SAFA;AAEA;AACA,kBAAA,OAAA,EAAA,iBAHA;AAGA;AACA,kBAAA,UAAA,EAAA,oBAJA;AAIA;AACA,kBAAA,MAAA,EAAA,QAAA,CAAA,aAAA,CAAA,UAAA;AALA,iBAAA,CAAA;AAOA,gBAAA,MAAA,CAAA,QAAA,mCAAA,GAAA,EATA,CAUA;;AAVA;AAAA,uBAWA,MAAA,CAAA,WAAA,CAAA,GAAA,CAAA,MAAA,CAXA;;AAAA;AAAA;AAAA,uBAYA,MAAA,CAAA,WAAA,CAAA,GAAA,CAAA,UAAA,CAZA;;AAAA;AAAA;AAAA,uBAaA,MAAA,CAAA,WAAA,CAAA,GAAA,CAAA,UAAA,CAbA;;AAAA;AAcA,gBAAA,MAAA,CAAA,YAAA,GAAA,KAAA;AACA,gBAAA,MAAA,CAAA,OAAA,GAAA,KAAA,CAfA,CAeA;;AACA,gBAAA,MAAA,CAAA,UAAA,GAAA,IAAA;;AACA,gBAAA,MAAA,CAAA,OAAA,CAAA,KAAA,GAjBA,CAiBA;;;AAjBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA,KA/DA;AAgEA;AACA,IAAA,SAjEA,qBAiEA,GAjEA,EAiEA;AAAA;;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,oCAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,OAAA;AACA,WANA,MAMA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAIA;AACA,SAbA;AAcA,OAnBA;AAoBA,KAtFA;AAuFA;AACA,IAAA,OAxFA,mBAwFA,GAxFA,EAwFA;AACA,WAAA,QAAA,mCAAA,GAAA;AACA,WAAA,YAAA,GAAA,IAAA;AACA,KA3FA;AA4FA;AACA,IAAA,OA7FA,mBA6FA,QA7FA,EA6FA;AACA;AACA,WAAA,QAAA,GAAA,EAAA;AACA,WAAA,QAAA,GAAA,EAAA;AACA,WAAA,QAAA,GAAA,EAAA,CAJA,CAKA;;AACA,UAAA,IAAA,GAAA,KAAA,WAAA,CAAA,MAAA,GAAA,KAAA,WAAA,CAAA,MAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,KAAA,WAAA,CAAA,UAAA,GAAA,KAAA,WAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,IAAA,GAAA,KAAA,WAAA,CAAA,UAAA,GAAA,KAAA,WAAA,CAAA,UAAA,GAAA,EAAA;AACA,WAAA,YAAA,GAAA,KAAA;;AACA,cAAA,QAAA;AACA,aAAA,MAAA;AAAA;AACA,eAAA,QAAA,GAAA,EAAA,CADA,CAEA;AACA;AACA;;AACA,eAAA,UAAA,GAAA,IAAA;AACA;;AACA,aAAA,MAAA;AAAA;AACA,eAAA,QAAA,GAAA,EAAA,CADA,CAEA;AACA;AACA;;AACA,eAAA,UAAA,GAAA,IAAA;AACA;;AACA,aAAA,MAAA;AAAA;AACA,eAAA,QAAA,GAAA,EAAA,CADA,CAEA;AACA;AACA;;AACA,eAAA,UAAA,GAAA,IAAA;AACA;;AACA,aAAA,MAAA;AAAA;AACA,eAAA,QAAA,GAAA,EAAA,CADA,CAEA;AACA;AACA;;AACA,eAAA,OAAA,GAAA,IAAA;AACA,eAAA,UAAA,GAAA,IAAA;AACA;;AACA;AACA;AA/BA;AAiCA,KAxIA;AAyIA;AACA,IAAA,QA1IA,oBA0IA,QA1IA,EA0IA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,MAAA,CAAA,KAAA,CAAA,QAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,sBAAA,KAAA,EAAA;AACA,wBAAA,QAAA,mCAAA;AAAA,sBAAA,IAAA,EAAA,MAAA,CAAA;AAAA,qBAAA,CAAA;;AACA,4BAAA,QAAA;AACA,2BAAA,UAAA;AAAA;AACA,wBAAA,QAAA,+DAAA,QAAA,GAAA,MAAA,CAAA,QAAA,CAAA;;AACA,wBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,8BAAA,IAAA,CAAA,KAAA,KAAA,QAAA,CAAA,UAAA,EAAA;AACA,4BAAA,QAAA,CAAA,IAAA,GAAA,IAAA,CAAA,KAAA;AACA;AACA,yBAJA;;AAKA,4BAAA,MAAA,CAAA,OAAA,EAAA;AACA;AACA,+CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gCAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,8BAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,8BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,8BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,8BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,8BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,8BAAA,MAAA,CAAA,YAAA,GAAA,KAAA;;AACA,8BAAA,MAAA,CAAA,OAAA,GAPA,CAQA;;AACA,6BATA,MASA;AACA,8BAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,2BAbA;AAcA,yBAhBA,MAgBA;AACA,kDAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,gCAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,8BAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,8BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,8BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,8BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,8BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,8BAAA,MAAA,CAAA,YAAA,GAAA,KAAA;;AACA,8BAAA,MAAA,CAAA,OAAA,GAPA,CAQA;;AACA,6BATA,MASA;AACA,8BAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,2BAdA;AAeA;;AACA;;AACA,2BAAA,UAAA;AAAA;AACA,wBAAA,QAAA,+DAAA,QAAA,GAAA,MAAA,CAAA,QAAA,CAAA;;AACA,wBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,8BAAA,IAAA,CAAA,KAAA,KAAA,QAAA,CAAA,UAAA,EAAA;AACA,4BAAA,QAAA,CAAA,IAAA,GAAA,IAAA,CAAA,KAAA;AACA;AACA,yBAJA;;AAKA,6CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,8BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,4BAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,4BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,MAAA,CAAA,YAAA,GAAA,KAAA;;AACA,4BAAA,MAAA,CAAA,OAAA,GAPA,CAQA;;AACA,2BATA,MASA;AACA,4BAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,yBAbA;AAcA;;AACA,2BAAA,UAAA;AAAA;AACA,wBAAA,QAAA,+DAAA,QAAA,GAAA,MAAA,CAAA,QAAA,CAAA;;AACA,wBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,8BAAA,IAAA,CAAA,KAAA,KAAA,QAAA,CAAA,UAAA,EAAA;AACA,4BAAA,QAAA,CAAA,IAAA,GAAA,IAAA,CAAA,KAAA;AACA;AACA,yBAJA;;AAKA,6CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,8BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,4BAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,4BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,MAAA,CAAA,YAAA,GAAA,KAAA;;AACA,4BAAA,MAAA,CAAA,OAAA,GAPA,CAQA;;AACA,2BATA,MASA;AACA,4BAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,yBAbA;AAcA;;AACA,2BAAA,UAAA;AAAA;AACA,wBAAA,QAAA,+DAAA,QAAA,GAAA,MAAA,CAAA,QAAA,CAAA;;AACA,wBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,8BAAA,IAAA,CAAA,KAAA,KAAA,QAAA,CAAA,MAAA,EAAA;AACA,4BAAA,QAAA,CAAA,IAAA,GAAA,IAAA,CAAA,KAAA;AACA;AACA,yBAJA;;AAKA,6CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,8BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,4BAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,4BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,4BAAA,MAAA,CAAA,YAAA,GAAA,KAAA;;AACA,4BAAA,MAAA,CAAA,OAAA,GAPA,CAQA;;AACA,2BATA,MASA;AACA,4BAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,yBAbA;AAcA;;AACA;AACA;AA7GA;AA+GA,mBAjHA,MAiHA;AACA,oBAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;;AACA,2BAAA,KAAA;AACA;AACA,iBAtHA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAwHA,KAlQA;AAmQA;AACA,IAAA,aApQA,yBAoQA,GApQA,EAoQA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,cAAA,CAAA,MAAA;;AADA;AAAA,uBAEA,OAAA,CAAA,WAAA,CAAA,GAAA,CAFA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,KAvQA;AAwQA;AACA,IAAA,aAzQA,yBAyQA,GAzQA,EAyQA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,cAAA,CAAA,MAAA;;AADA;AAAA,uBAEA,OAAA,CAAA,WAAA,CAAA,GAAA,CAFA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,KA5QA;AA6QA;AACA,IAAA,aA9QA,yBA8QA,GA9QA,EA8QA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,cAAA,CAAA,MAAA;;AADA;AAAA,uBAEA,OAAA,CAAA,WAAA,CAAA,GAAA,CAFA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,KAjRA;AAkRA;AACA,IAAA,aAnRA,yBAmRA,GAnRA,EAmRA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,cAAA,CAAA,MAAA;;AADA;AAAA,uBAEA,OAAA,CAAA,WAAA,CAAA,GAAA,CAFA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,KAtRA;AAuRA;AACA,IAAA,cAxRA,0BAwRA,IAxRA,EAwRA;AACA,cAAA,IAAA;AACA,aAAA,MAAA;AAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,MAAA,EAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,YAAA,EAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,YAAA,EAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,YAAA,EAAA,EAAA;AACA,eAAA,cAAA,CAAA,MAAA;AACA;;AACA,aAAA,MAAA;AAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,MAAA,EAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,YAAA,EAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,YAAA,EAAA,EAAA;AACA,eAAA,cAAA,CAAA,MAAA;AACA;;AACA,aAAA,MAAA;AAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,MAAA,EAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,YAAA,EAAA,EAAA;AACA,eAAA,cAAA,CAAA,MAAA;AACA;;AACA,aAAA,MAAA;AAAA;AACA,eAAA,IAAA,CAAA,KAAA,QAAA,EAAA,MAAA,EAAA,EAAA;AACA;;AACA;AACA;AAvBA;AAyBA,KAlTA;AAmTA;AACA,IAAA,QApTA,oBAoTA,IApTA,EAoTA;AACA,WAAA,YAAA,GAAA,KAAA;;AACA,cAAA,IAAA;AACA,aAAA,MAAA;AACA,eAAA,UAAA,GAAA,KAAA;AACA;;AACA,aAAA,MAAA;AACA,eAAA,UAAA,GAAA,KAAA;AACA;;AACA,aAAA,MAAA;AACA,eAAA,UAAA,GAAA,KAAA;AACA;;AACA,aAAA,MAAA;AACA,eAAA,UAAA,GAAA,KAAA;AACA;;AACA,aAAA,MAAA;AACA,eAAA,YAAA,GAAA,KAAA;AACA;;AACA;AACA,eAAA,UAAA,GAAA,KAAA;AACA,eAAA,UAAA,GAAA,KAAA;AACA,eAAA,UAAA,GAAA,KAAA;AACA,eAAA,UAAA,GAAA,KAAA;AACA,eAAA,YAAA,GAAA,KAAA;AACA;AAtBA;AAwBA,KA9UA;AA+UA;AACA,IAAA,WAhVA,yBAgVA;AACA,WAAA,WAAA,GAAA;AAAA,QAAA,IAAA,EAAA,KAAA;AAAA,OAAA,CADA,CACA;AACA,KAlVA;AAoVA;AACA,IAAA,UArVA,sBAqVA,KArVA,EAqVA,IArVA,EAqVA;AACA,UAAA,CAAA,KAAA,EAAA,OAAA,IAAA;AACA,aAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,KAAA,MAAA,CAAA,CAAA;AACA,KAxVA;AAyVA,IAAA,WAzVA,yBAyVA;AAAA;;AACA,+BAAA;AAAA,QAAA,IAAA,EAAA,KAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KA7VA;AA8VA;AACA,IAAA,eA/VA,2BA+VA,IA/VA,EA+VA;AACA,WAAA,YAAA,GAAA,IAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,MAAA,EAAA,IAAA;;AACA,UAAA,IAAA,CAAA,UAAA,KAAA,GAAA,EAAA;AACA;AACA,aAAA,WAAA,CAAA,MAAA,GAAA,IAAA,CAAA,EAAA;AACA,aAAA,WAAA,CAAA,UAAA,GAAA,EAAA;AACA,aAAA,WAAA,CAAA,UAAA,GAAA,EAAA;AACA,OALA,MAKA,IAAA,IAAA,CAAA,UAAA,KAAA,GAAA,EAAA;AACA;AACA,aAAA,WAAA,CAAA,MAAA,GAAA,EAAA;AACA,aAAA,WAAA,CAAA,UAAA,GAAA,IAAA,CAAA,EAAA;AACA,aAAA,WAAA,CAAA,UAAA,GAAA,EAAA;AACA,OALA,MAKA,IAAA,IAAA,CAAA,UAAA,KAAA,GAAA,EAAA;AACA;AACA,aAAA,WAAA,CAAA,MAAA,GAAA,EAAA;AACA,aAAA,WAAA,CAAA,UAAA,GAAA,EAAA;AACA,aAAA,WAAA,CAAA,UAAA,GAAA,IAAA,CAAA,EAAA;AACA,OALA,MAKA;AACA,aAAA,WAAA,GAAA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,SAAA;AACA;;AACA,WAAA,OAAA;AACA,KArXA;AAsXA;AACA,IAAA,OAvXA,mBAuXA,MAvXA,EAuXA;AAAA;;AACA,WAAA,IAAA,GAAA,IAAA;AACA,WAAA,WAAA,+DAAA,KAAA,WAAA,GAAA,MAAA;AACA,6BAAA,KAAA,WAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,OAAA,CAAA,IAAA,GAAA,KAAA;AACA,OAJA;AAKA,KA/XA;AAgYA;AACA,IAAA,WAjYA,yBAiYA;AACA;AACA;AACA;AACA;AACA,UAAA,QAAA,GAAA,OAAA;AACA,UAAA,SAAA,GAAA,WAAA;AACA,8BAAA,SAAA,EAAA,KAAA,WAAA,EAAA,QAAA;AACA;AAzYA;AAxKA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\" id=\"sbqxDiv\">\n    <!--左侧树组件-->\n    <el-row :gutter=\"1\">\n      <!--   左侧树   -->\n      <el-col :span=\"4\">\n        <el-card style=\"background:#e0f8ed;padding-top:10px;\" shadow=\"never\">\n          <div>\n            <el-col>\n              <el-form label-width=\"62px\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"快速查询:\" label-width=\"80px\">\n                    <el-input\n                      placeholder=\"输入关键字过滤\"\n                      v-model=\"filterText\"\n                      clearable\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-form>\n            </el-col>\n          </div>\n          <div class=\"text head-container\">\n            <el-col style=\"padding:0;\">\n              <el-tree\n                id=\"tree\"\n                :data=\"treeOptions\"\n                @node-click=\"handleNodeClick\"\n                :highlight-current=\"true\"\n                ref=\"tree\"\n                :filter-node-method=\"filterNode\"\n                node-key=\"id\"\n                :default-checked-keys=\"['0']\"\n                :default-expanded-keys=\"['0']\"\n                accordion\n              />\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :btnHidden=\"false\"\n          :width=\"{ labelWidth: 80, itemWidth: 165 }\"\n          @handleReset=\"filterReset\"\n        />\n        <el-white class=\"button-group1\">\n          <div class=\"button_btn\">\n            <el-button\n              type=\"primary\"\n              icon=\"el-icon-plus\"\n              v-hasPermi=\"['pdqxwh:button:add']\"\n              @click=\"addForm('sbbj')\"\n              >新增部件</el-button\n            >\n            <el-button\n              type=\"primary\"\n              icon=\"el-icon-plus\"\n              v-hasPermi=\"['pdqxwh:button:add']\"\n              @click=\"addForm('sbbw')\"\n              >新增部位</el-button\n            >\n            <el-button\n              type=\"primary\"\n              icon=\"el-icon-plus\"\n              v-hasPermi=\"['pdqxwh:button:add']\"\n              @click=\"addForm('qxms')\"\n              >新增隐患描述</el-button\n            >\n            <el-button\n              type=\"primary\"\n              icon=\"el-icon-plus\"\n              v-hasPermi=\"['pdqxwh:button:add']\"\n              @click=\"addForm('flyj')\"\n              >新增分类依据</el-button\n            >\n            <el-button\n              type=\"success\"\n              icon=\"el-icon-download\"\n              @click=\"exportExcel\"\n              >导出</el-button\n            >\n          </div>\n          <comp-table\n            v-loading=\"load\"\n            :table-and-page-info=\"tableAndPageInfo\"\n            height=\"62.2vh\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"200\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"updateRow(scope.row)\"\n                  v-hasPermi=\"['pdqxwh:button:update']\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                >\n                </el-button>\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"deleteRow(scope.row)\"\n                  v-hasPermi=\"['pdqxwh:button:delete']\"\n                  title=\"删除\"\n                  class=\"el-icon-delete\"\n                >\n                </el-button>\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"viewFun(scope.row)\"\n                  title=\"查看\"\n                  class=\"el-icon-view\"\n                >\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!--  新增设备部件  -->\n    <el-dialog\n      title=\"新增设备部件\"\n      :visible.sync=\"isShowSbbj\"\n      width=\"58%\"\n      @close=\"closeFun('sbbj')\"\n      v-dialogDrag\n    >\n      <el-form\n        label-width=\"140px\"\n        :rules=\"sbbjRules\"\n        :model=\"sbbjForm\"\n        ref=\"sbbjForm\"\n      >\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备隐患</span>\n          </div>\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sblxbm\" label=\"设备类型\">\n                <el-select\n                  placeholder=\"设备类型\"\n                  v-model=\"sbbjForm.sblxbm\"\n                  style=\"width:80%\"\n                  @change=\"sblxChangeFun\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in sblxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sbbj\" label=\"设备部件\">\n                <el-input\n                  v-model=\"sbbjForm.sbbj\"\n                  placeholder=\"请输入设备部件\"\n                  style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sbbw\" label=\"设备部位\">\n                <el-input\n                  v-model=\"sbbjForm.sbbw\"\n                  placeholder=\"请输入设备部位\"\n                  style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\n                <el-select\n                  placeholder=\"隐患等级\"\n                  v-model=\"sbbjForm.qxdj\"\n                  style=\"width:80%\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in qxdjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"隐患描述:\" prop=\"qxms\">\n                <el-input\n                  type=\"textarea\"\n                  :rows=\"5\"\n                  v-model=\"sbbjForm.qxms\"\n                  placeholder=\"请输入隐患描述\"\n                  style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input\n                  type=\"textarea\"\n                  :rows=\"5\"\n                  v-model=\"sbbjForm.flyj\"\n                  placeholder=\"请输入分类依据\"\n                  style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\n                <el-input\n                  type=\"textarea\"\n                  :rows=\"5\"\n                  v-model=\"sbbjForm.jsyy\"\n                  placeholder=\"请输入技术原因\"\n                  style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun('sbbj')\" size=\"small\">取 消</el-button>\n        <el-button type=\"primary\" size=\"small\" @click=\"saveForm('sbbjForm')\"\n          >保存</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <!--  新增设备部位  -->\n    <el-dialog\n      title=\"新增设备部位\"\n      :visible.sync=\"isShowSbbw\"\n      width=\"58%\"\n      @close=\"closeFun('sbbw')\"\n      v-dialogDrag\n    >\n      <el-form\n        label-width=\"140px\"\n        :rules=\"sbbwRules\"\n        :model=\"sbbwForm\"\n        ref=\"sbbwForm\"\n      >\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备隐患</span>\n          </div>\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sblxbm\" label=\"设备类型\">\n                <el-select\n                  placeholder=\"设备类型\"\n                  v-model=\"sbbwForm.sblxbm\"\n                  style=\"width:80%\"\n                  @change=\"sblxChangeFun\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in sblxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"parentSbbj\" label=\"设备部件\">\n                <el-select\n                  placeholder=\"设备部件\"\n                  v-model=\"sbbwForm.parentSbbj\"\n                  style=\"width:80%\"\n                  @change=\"sbbjChangeFun\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in sbbjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sbbw\" label=\"设备部位\">\n                <el-input\n                  v-model=\"sbbwForm.sbbw\"\n                  placeholder=\"请输入设备部位\"\n                  style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\n                <el-select\n                  placeholder=\"隐患等级\"\n                  v-model=\"sbbwForm.qxdj\"\n                  style=\"width:80%\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in qxdjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"隐患描述:\" prop=\"qxms\">\n                <el-input\n                  type=\"textarea\"\n                  :rows=\"5\"\n                  v-model=\"sbbwForm.qxms\"\n                  placeholder=\"请输入隐患描述\"\n                  style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input\n                  type=\"textarea\"\n                  :rows=\"5\"\n                  v-model=\"sbbwForm.flyj\"\n                  placeholder=\"请输入分类依据\"\n                  style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\n                <el-input\n                  type=\"textarea\"\n                  :rows=\"5\"\n                  v-model=\"sbbwForm.jsyy\"\n                  placeholder=\"请输入技术原因\"\n                  style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun('sbbw')\" size=\"small\">取 消</el-button>\n        <el-button type=\"primary\" size=\"small\" @click=\"saveForm('sbbwForm')\"\n          >保存</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <!--  新增隐患描述  -->\n    <el-dialog\n      title=\"新增隐患描述\"\n      :visible.sync=\"isShowQxms\"\n      width=\"58%\"\n      @close=\"closeFun('qxms')\"\n      v-dialogDrag\n    >\n      <el-form\n        label-width=\"140px\"\n        :rules=\"qxmsRules\"\n        :model=\"qxmsForm\"\n        ref=\"qxmsForm\"\n      >\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备隐患</span>\n          </div>\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sblxbm\" label=\"设备类型\">\n                <el-select\n                  placeholder=\"设备类型\"\n                  v-model=\"qxmsForm.sblxbm\"\n                  style=\"width:80%\"\n                  @change=\"sblxChangeFun\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in sblxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"parentSbbj\" label=\"设备部件\">\n                <el-select\n                  placeholder=\"设备部件\"\n                  v-model=\"qxmsForm.parentSbbj\"\n                  style=\"width:80%\"\n                  @change=\"sbbjChangeFun\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in sbbjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"parentSbbw\" label=\"设备部位\">\n                <el-select\n                  placeholder=\"设备部位\"\n                  v-model=\"qxmsForm.parentSbbw\"\n                  style=\"width:80%\"\n                  @change=\"sbbwChangeFun\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in sbbwList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\n                <el-select\n                  placeholder=\"隐患等级\"\n                  v-model=\"qxmsForm.qxdj\"\n                  style=\"width:80%\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in qxdjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"隐患描述:\" prop=\"qxms\">\n                <el-input\n                  type=\"textarea\"\n                  :rows=\"5\"\n                  v-model=\"qxmsForm.qxms\"\n                  placeholder=\"请输入隐患描述\"\n                  style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input\n                  type=\"textarea\"\n                  :rows=\"5\"\n                  v-model=\"qxmsForm.flyj\"\n                  placeholder=\"请输入分类依据\"\n                  style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\n                <el-input\n                  type=\"textarea\"\n                  :rows=\"5\"\n                  v-model=\"qxmsForm.jsyy\"\n                  placeholder=\"请输入技术原因\"\n                  style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun('qxms')\" size=\"small\">取 消</el-button>\n        <el-button type=\"primary\" size=\"small\" @click=\"saveForm('qxmsForm')\"\n          >保存</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <!--  新增分类依据  -->\n    <el-dialog\n      title=\"新增分类依据\"\n      :visible.sync=\"isShowFlyj\"\n      width=\"58%\"\n      @close=\"closeFun('flyj')\"\n      v-dialogDrag\n    >\n      <el-form\n        label-width=\"140px\"\n        :rules=\"flyjRules\"\n        :model=\"flyjForm\"\n        ref=\"flyjForm\"\n      >\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备隐患</span>\n          </div>\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sblxbm\" label=\"设备类型\">\n                <el-select\n                  placeholder=\"设备类型\"\n                  v-model=\"flyjForm.sblxbm\"\n                  style=\"width:80%\"\n                  @change=\"sblxChangeFun\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in sblxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"parentSbbj\" label=\"设备部件\">\n                <el-select\n                  placeholder=\"设备部件\"\n                  v-model=\"flyjForm.parentSbbj\"\n                  style=\"width:80%\"\n                  @change=\"sbbjChangeFun\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in sbbjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"parentSbbw\" label=\"设备部位\">\n                <el-select\n                  placeholder=\"设备部位\"\n                  v-model=\"flyjForm.parentSbbw\"\n                  style=\"width:80%\"\n                  @change=\"sbbwChangeFun\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in sbbwList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\n                <el-select\n                  placeholder=\"隐患等级\"\n                  v-model=\"flyjForm.qxdj\"\n                  style=\"width:80%\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in qxdjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"隐患描述:\" prop=\"parentQxms\">\n                <el-select\n                  placeholder=\"隐患描述\"\n                  v-model=\"flyjForm.parentQxms\"\n                  style=\"width:80%\"\n                  @change=\"qxmsChangeFun\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in qxmsList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input\n                  type=\"textarea\"\n                  :rows=\"5\"\n                  v-model=\"flyjForm.flyj\"\n                  placeholder=\"请输入分类依据\"\n                  style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\n                <el-input\n                  type=\"textarea\"\n                  :rows=\"5\"\n                  v-model=\"flyjForm.jsyy\"\n                  placeholder=\"请输入技术原因\"\n                  style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun('flyj')\" size=\"small\">取 消</el-button>\n        <el-button\n          v-if=\"addFlyj\"\n          type=\"primary\"\n          size=\"small\"\n          @click=\"saveForm('flyjForm')\"\n          >保存</el-button\n        >\n        <el-button\n          v-if=\"!addFlyj\"\n          type=\"primary\"\n          size=\"small\"\n          @click=\"saveForm('flyjForm')\"\n          >保存</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <!--  设备隐患查看  -->\n    <el-dialog\n      title=\"设备隐患查看\"\n      :visible.sync=\"isShowDetail\"\n      width=\"58%\"\n      @close=\"closeFun('view')\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"140px\" :model=\"viewForm\" ref=\"viewForm\">\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备隐患</span>\n          </div>\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sblx\" label=\"设备类型\">\n                <el-input\n                  v-model=\"viewForm.sblx\"\n                  placeholder=\"请输入设备类型\"\n                  style=\"width: 92%\"\n                  :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sbbj\" label=\"设备部件\">\n                <el-input\n                  v-model=\"viewForm.sbbj\"\n                  placeholder=\"请输入设备部件\"\n                  style=\"width: 92%\"\n                  :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sbbw\" label=\"设备部位\">\n                <el-input\n                  v-model=\"viewForm.sbbw\"\n                  placeholder=\"请输入设备部位\"\n                  style=\"width: 92%\"\n                  :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"qxdj\" label=\"隐患等级\">\n                <el-input\n                  v-model=\"viewForm.qxdj\"\n                  placeholder=\"请输入隐患等级\"\n                  style=\"width: 92%\"\n                  :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"隐患描述:\" prop=\"qxms\">\n                <el-input\n                  type=\"textarea\"\n                  :rows=\"5\"\n                  v-model=\"viewForm.qxms\"\n                  placeholder=\"请输入隐患描述\"\n                  style=\"width: 92%\"\n                  :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input\n                  type=\"textarea\"\n                  :rows=\"5\"\n                  v-model=\"viewForm.flyj\"\n                  placeholder=\"请输入分类依据\"\n                  style=\"width: 92%\"\n                  :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"技术原因:\" prop=\"jsyy\">\n                <el-input\n                  type=\"textarea\"\n                  :rows=\"5\"\n                  v-model=\"viewForm.jsyy\"\n                  placeholder=\"请输入技术原因\"\n                  style=\"width: 92%\"\n                  :disabled=\"true\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  getQxList,\n  getQxsbTree,\n  getSblxList,\n  getSbbjList,\n  getSbbwList,\n  getQxmsList,\n  getFlyjList,\n  addFlyj,\n  updateFlyj,\n  deleteFlyjById,\n  addQxms,\n  addSbbw,\n  addSbbj\n} from \"@/api/dagangOilfield/bzgl/sbqxwh/sbqxwh\";\nimport { getDictTypeData } from \"@/api/system/dict/data\";\nimport { Loading } from \"element-ui\";\nimport { exportExcel } from \"@/api/bzgl/ysbzk/ysbzk\";\n\nexport default {\n  name: \"sblxwh\",\n  data() {\n    return {\n      load: false,\n      addFlyj: false, //是否新增分类依据\n      filterInfo: {\n        data: {\n          sbbj: \"\",\n          sbbw: \"\",\n          qxms: \"\",\n          flyj: \"\",\n          qxdj: \"\",\n          jsyy: \"\"\n        },\n        fieldList: [\n          { label: \"设备部件\", type: \"input\", value: \"sbbj\" },\n          { label: \"设备部位\", type: \"input\", value: \"sbbw\" },\n          { label: \"隐患描述\", type: \"input\", value: \"qxms\" },\n          { label: \"隐患等级\", type: \"select\", value: \"qxdj\", options: [] },\n          { label: \"分类依据\", type: \"input\", value: \"flyj\" },\n          { label: \"技术原因\", type: \"input\", value: \"jsyy\" }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"sblx\", label: \"设备类型\", minWidth: \"140\" },\n          { prop: \"sbbj\", label: \"设备部件\", minWidth: \"180\" },\n          { prop: \"sbbw\", label: \"设备部位\", minWidth: \"130\" },\n          { prop: \"qxms\", label: \"隐患描述\", minWidth: \"200\" },\n          { prop: \"flyj\", label: \"分类依据\", minWidth: \"220\", showPop: true },\n          { prop: \"qxdj\", label: \"隐患等级\", minWidth: \"80\" },\n          { prop: \"jsyy\", label: \"技术原因\", minWidth: \"120\" }\n        ]\n      },\n      queryParams: {},\n      treeOptions: [], //组织树\n      treeNodeData: {}, //点击后的树节点数据\n      isShowDetail: false,\n      isShowSbbj: false, //新增弹框\n      isShowSbbw: false,\n      isShowQxms: false,\n      isShowFlyj: false,\n      flyjForm: {}, //表单\n      flyjRules: {\n        sblxbm: [\n          { required: true, message: \"设备类型不能为空\", trigger: \"select\" }\n        ],\n        parentSbbj: [\n          { required: true, message: \"设备部件不能为空\", trigger: \"select\" }\n        ],\n        parentSbbw: [\n          { required: true, message: \"设备部位不能为空\", trigger: \"select\" }\n        ],\n        qxdj: [\n          { required: true, message: \"隐患等级不能为空\", trigger: \"select\" }\n        ],\n        parentQxms: [\n          { required: true, message: \"隐患描述不能为空\", trigger: \"select\" }\n        ],\n        flyj: [\n          { required: true, message: \"分类依据不能为空\", trigger: \"blur\" }\n        ],\n        jsyy: [{ required: true, message: \"技术原因不能为空\", trigger: \"blur\" }]\n      }, //校验规则\n      qxmsForm: {}, //表单\n      qxmsRules: {\n        sblxbm: [\n          { required: true, message: \"设备类型不能为空\", trigger: \"select\" }\n        ],\n        parentSbbj: [\n          { required: true, message: \"设备部件不能为空\", trigger: \"select\" }\n        ],\n        parentSbbw: [\n          { required: true, message: \"设备部位不能为空\", trigger: \"select\" }\n        ],\n        qxdj: [\n          { required: true, message: \"隐患等级不能为空\", trigger: \"select\" }\n        ],\n        qxms: [\n          { required: true, message: \"隐患描述不能为空\", trigger: \"blur\" }\n        ],\n        flyj: [\n          { required: true, message: \"分类依据不能为空\", trigger: \"blur\" }\n        ],\n        jsyy: [{ required: true, message: \"技术原因不能为空\", trigger: \"blur\" }]\n      }, //校验规则\n      sbbwForm: {}, //表单\n      sbbwRules: {\n        sblxbm: [\n          { required: true, message: \"设备类型不能为空\", trigger: \"select\" }\n        ],\n        parentSbbj: [\n          { required: true, message: \"设备部件不能为空\", trigger: \"select\" }\n        ],\n        sbbw: [\n          { required: true, message: \"设备部位不能为空\", trigger: \"blur\" }\n        ],\n        qxdj: [\n          { required: true, message: \"隐患等级不能为空\", trigger: \"select\" }\n        ],\n        qxms: [\n          { required: true, message: \"隐患描述不能为空\", trigger: \"blur\" }\n        ],\n        flyj: [\n          { required: true, message: \"分类依据不能为空\", trigger: \"blur\" }\n        ],\n        jsyy: [{ required: true, message: \"技术原因不能为空\", trigger: \"blur\" }]\n      }, //校验规则\n      sbbjForm: {}, //表单\n      sbbjRules: {\n        sblxbm: [\n          { required: true, message: \"设备类型不能为空\", trigger: \"select\" }\n        ],\n        sbbj: [\n          { required: true, message: \"设备部件不能为空\", trigger: \"blur\" }\n        ],\n        sbbw: [\n          { required: true, message: \"设备部位不能为空\", trigger: \"blur\" }\n        ],\n        qxdj: [\n          { required: true, message: \"隐患等级不能为空\", trigger: \"select\" }\n        ],\n        qxms: [\n          { required: true, message: \"隐患描述不能为空\", trigger: \"blur\" }\n        ],\n        flyj: [\n          { required: true, message: \"分类依据不能为空\", trigger: \"blur\" }\n        ],\n        jsyy: [{ required: true, message: \"技术原因不能为空\", trigger: \"blur\" }]\n      }, //校验规则\n      sblxList: [], //设备类型下拉框选项\n      sbbjList: [], //设备部件下拉框选项\n      sbbwList: [], //设备部位下拉框选项\n      qxmsList: [], //隐患描述下拉框选项\n      flyjList: [], //分类依据下拉框选项\n      qxdjList: [], //隐患等级下拉框选项\n      qxlb: \"2\", //隐患类别（配电）\n      filterText: \"\", //过滤\n      viewForm: {}, //查看表单\n      loading: null\n    };\n  },\n  watch: {\n    filterText(val) {\n      this.$refs.tree.filter(val);\n    }\n  },\n  created() {\n    this.queryParams.qxlb = this.qxlb;\n    this.getData();\n    this.getTreeData();\n    //设备类型下拉框\n    this.getSblxList();\n    //隐患等级下拉框\n    this.getQxdjList();\n  },\n  methods: {\n    //获取设备类型下拉框\n    async getSblxList() {\n      await getSblxList({ qxlb: this.qxlb }).then(res => {\n        this.sblxList = res.data;\n      });\n    },\n    //获取设备部件下拉框\n    async getSbbjList(sblx) {\n      await getSbbjList({ qxlb: this.qxlb, sblx: sblx }).then(res => {\n        this.sbbjList = res.data;\n      });\n    },\n    //获取设备部位下拉框\n    async getSbbwList(sbbj) {\n      await getSbbwList({ qxlb: this.qxlb, sbbj: sbbj }).then(res => {\n        this.sbbwList = res.data;\n      });\n    },\n    //获取隐患描述下拉框\n    async getQxmsList(sbbw) {\n      await getQxmsList({ qxlb: this.qxlb, sbbw: sbbw }).then(res => {\n        this.qxmsList = res.data;\n      });\n    },\n    //获取分类依据下拉框\n    async getFlyjList(qxms) {\n      await getFlyjList({ qxlb: this.qxlb, qxms: qxms }).then(res => {\n        this.flyjList = res.data;\n      });\n    },\n    //获取隐患等级字典数据\n    async getQxdjList() {\n      //查询隐患等级字典\n      await getDictTypeData(\"sbqxwh_qxdj\").then(res => {\n        this.qxdjList = res.data;\n        //给筛选条件赋值\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"qxdj\") {\n            item.options = this.qxdjList;\n          }\n        });\n      });\n    },\n    //编辑\n    async updateRow(row) {\n      //开启遮罩层\n      this.loading = Loading.service({\n        lock: true, //lock的修改符--默认是false\n        text: \"加载中，请稍后\", //显示在加载图标下方的加载文案\n        spinner: \"el-icon-loading\", //自定义加载图标类名\n        background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n        target: document.querySelector(\"#sbqxDiv\")\n      });\n      this.flyjForm = { ...row };\n      //下拉框回显\n      await this.getSbbjList(row.sblxbm);\n      await this.getSbbwList(row.parentSbbj);\n      await this.getQxmsList(row.parentSbbw);\n      this.isShowDetail = false;\n      this.addFlyj = false; //不是新增\n      this.isShowFlyj = true;\n      this.loading.close(); //关闭遮罩层\n    },\n    //删除\n    deleteRow(row) {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(() => {\n        deleteFlyjById(row).then(res => {\n          if (res.code === \"0000\") {\n            this.$message({\n              type: \"success\",\n              message: \"删除成功!\"\n            });\n            this.getData();\n          } else {\n            this.$message({\n              type: \"error\",\n              message: \"删除失败!\"\n            });\n          }\n        });\n      });\n    },\n    //查看\n    viewFun(row) {\n      this.viewForm = { ...row };\n      this.isShowDetail = true;\n    },\n    //新增\n    addForm(formType) {\n      //先清空下拉框的值\n      this.sbbjList = [];\n      this.sbbwList = [];\n      this.qxmsList = [];\n      //如果树节点有值，则带过来\n      let sblx = this.queryParams.sblxbm ? this.queryParams.sblxbm : \"\";\n      let sbbj = this.queryParams.parentSbbj ? this.queryParams.parentSbbj : \"\";\n      let sbbw = this.queryParams.parentSbbw ? this.queryParams.parentSbbw : \"\";\n      this.isShowDetail = false;\n      switch (formType) {\n        case \"sbbj\": //设备部件\n          this.sbbjForm = {};\n          // this.$set(this.sbbjForm,'sblx',sblx);\n          // this.$set(this.sbbjForm,'sbbj',sbbj);\n          // this.$set(this.sbbjForm,'sbbw',sbbw);\n          this.isShowSbbj = true;\n          break;\n        case \"sbbw\": //设备部位\n          this.sbbwForm = {};\n          // this.$set(this.sbbwForm,'sblx',sblx);\n          // this.$set(this.sbbwForm,'sbbj',sbbj);\n          // this.$set(this.sbbwForm,'sbbw',sbbw);\n          this.isShowSbbw = true;\n          break;\n        case \"qxms\": //隐患描述\n          this.qxmsForm = {};\n          // this.$set(this.qxmsForm,'sblx',sblx);\n          // this.$set(this.qxmsForm,'sbbj',sbbj);\n          // this.$set(this.qxmsForm,'sbbw',sbbw);\n          this.isShowQxms = true;\n          break;\n        case \"flyj\": //分类依据\n          this.flyjForm = {};\n          // this.$set(this.flyjForm,'sblx',sblx);\n          // this.$set(this.flyjForm,'sbbj',sbbj);\n          // this.$set(this.flyjForm,'sbbw',sbbw);\n          this.addFlyj = true;\n          this.isShowFlyj = true;\n          break;\n        default:\n          break;\n      }\n    },\n    //保存\n    async saveForm(formType) {\n      await this.$refs[formType].validate(valid => {\n        if (valid) {\n          let saveForm = { ...{ qxlb: this.qxlb } };\n          switch (formType) {\n            case \"flyjForm\": //新增分类依据\n              saveForm = { ...saveForm, ...this.flyjForm };\n              this.qxmsList.forEach(item => {\n                if (item.value === saveForm.parentQxms) {\n                  saveForm.qxms = item.label;\n                }\n              });\n              if (this.addFlyj) {\n                //新增\n                addFlyj(saveForm).then(res => {\n                  if (res.code === \"0000\") {\n                    this.$message.success(\"操作成功\");\n                    this.isShowFlyj = false;\n                    this.isShowQxms = false;\n                    this.isShowSbbw = false;\n                    this.isShowSbbj = false;\n                    this.isShowDetail = false;\n                    this.getData();\n                    //关闭弹框\n                  } else {\n                    this.$message.error(\"操作失败\");\n                  }\n                });\n              } else {\n                updateFlyj(saveForm).then(res => {\n                  //编辑\n                  if (res.code === \"0000\") {\n                    this.$message.success(\"操作成功\");\n                    this.isShowFlyj = false;\n                    this.isShowQxms = false;\n                    this.isShowSbbw = false;\n                    this.isShowSbbj = false;\n                    this.isShowDetail = false;\n                    this.getData();\n                    //关闭弹框\n                  } else {\n                    this.$message.error(\"操作失败\");\n                  }\n                });\n              }\n              break;\n            case \"qxmsForm\": //新增隐患描述\n              saveForm = { ...saveForm, ...this.qxmsForm };\n              this.sbbwList.forEach(item => {\n                if (item.value === saveForm.parentSbbw) {\n                  saveForm.sbbw = item.label;\n                }\n              });\n              addQxms(saveForm).then(res => {\n                if (res.code === \"0000\") {\n                  this.$message.success(\"操作成功\");\n                  this.isShowFlyj = false;\n                  this.isShowQxms = false;\n                  this.isShowSbbw = false;\n                  this.isShowSbbj = false;\n                  this.isShowDetail = false;\n                  this.getData();\n                  //关闭弹框\n                } else {\n                  this.$message.error(\"操作失败\");\n                }\n              });\n              break;\n            case \"sbbwForm\": //新增隐患描述\n              saveForm = { ...saveForm, ...this.sbbwForm };\n              this.sbbjList.forEach(item => {\n                if (item.value === saveForm.parentSbbj) {\n                  saveForm.sbbj = item.label;\n                }\n              });\n              addSbbw(saveForm).then(res => {\n                if (res.code === \"0000\") {\n                  this.$message.success(\"操作成功\");\n                  this.isShowFlyj = false;\n                  this.isShowQxms = false;\n                  this.isShowSbbw = false;\n                  this.isShowSbbj = false;\n                  this.isShowDetail = false;\n                  this.getData();\n                  //关闭弹框\n                } else {\n                  this.$message.error(\"操作失败\");\n                }\n              });\n              break;\n            case \"sbbjForm\": //新增隐患描述\n              saveForm = { ...saveForm, ...this.sbbjForm };\n              this.sblxList.forEach(item => {\n                if (item.value === saveForm.sblxbm) {\n                  saveForm.sblx = item.label;\n                }\n              });\n              addSbbj(saveForm).then(res => {\n                if (res.code === \"0000\") {\n                  this.$message.success(\"操作成功\");\n                  this.isShowFlyj = false;\n                  this.isShowQxms = false;\n                  this.isShowSbbw = false;\n                  this.isShowSbbj = false;\n                  this.isShowDetail = false;\n                  this.getData();\n                  //关闭弹框\n                } else {\n                  this.$message.error(\"操作失败\");\n                }\n              });\n              break;\n            default:\n              break;\n          }\n        } else {\n          this.$message.error(\"校验未通过！\");\n          return false;\n        }\n      });\n    },\n    //设备类型下拉框事件\n    async sblxChangeFun(val) {\n      this.clearFormField(\"sblx\");\n      await this.getSbbjList(val);\n    },\n    //设备部件下拉框事件\n    async sbbjChangeFun(val) {\n      this.clearFormField(\"sbbj\");\n      await this.getSbbwList(val);\n    },\n    //设备部位下拉框事件\n    async sbbwChangeFun(val) {\n      this.clearFormField(\"sbbw\");\n      await this.getQxmsList(val);\n    },\n    //隐患描述下拉框事件\n    async qxmsChangeFun(val) {\n      this.clearFormField(\"qxms\");\n      await this.getFlyjList(val);\n    },\n    //清空字段值\n    clearFormField(type) {\n      switch (type) {\n        case \"sblx\": //设备类型\n          this.$set(this.sbbjForm, \"sbbj\", \"\");\n          this.$set(this.sbbwForm, \"parentSbbj\", \"\");\n          this.$set(this.qxmsForm, \"parentSbbj\", \"\");\n          this.$set(this.flyjForm, \"parentSbbj\", \"\");\n          this.clearFormField(\"sbbj\");\n          break;\n        case \"sbbj\": //设备部件\n          this.$set(this.sbbwForm, \"sbbw\", \"\");\n          this.$set(this.qxmsForm, \"parentSbbw\", \"\");\n          this.$set(this.flyjForm, \"parentSbbw\", \"\");\n          this.clearFormField(\"sbbw\");\n          break;\n        case \"sbbw\": //设备部位\n          this.$set(this.qxmsForm, \"qxms\", \"\");\n          this.$set(this.flyjForm, \"parentQxms\", \"\");\n          this.clearFormField(\"qxms\");\n          break;\n        case \"qxms\": //隐患描述\n          this.$set(this.flyjForm, \"flyj\", \"\");\n          break;\n        default:\n          break;\n      }\n    },\n    //关闭\n    closeFun(type) {\n      this.isShowDetail = false;\n      switch (type) {\n        case \"sbbj\":\n          this.isShowSbbj = false;\n          break;\n        case \"sbbw\":\n          this.isShowSbbw = false;\n          break;\n        case \"qxms\":\n          this.isShowQxms = false;\n          break;\n        case \"flyj\":\n          this.isShowFlyj = false;\n          break;\n        case \"view\":\n          this.isShowDetail = false;\n          break;\n        default:\n          this.isShowSbbj = false;\n          this.isShowSbbw = false;\n          this.isShowQxms = false;\n          this.isShowFlyj = false;\n          this.isShowDetail = false;\n          break;\n      }\n    },\n    //重置按钮\n    filterReset() {\n      this.queryParams = { qxlb: this.qxlb }; //重置条件\n    },\n\n    //树监听事件\n    filterNode(value, data) {\n      if (!value) return true;\n      return data.label.indexOf(value) !== -1;\n    },\n    getTreeData() {\n      getQxsbTree({ qxlb: this.qxlb }).then(res => {\n        this.treeOptions = res.data;\n      });\n    },\n    //树节点点击事件\n    handleNodeClick(node) {\n      this.treeNodeData = node;\n      console.log(\"node\", node);\n      if (node.identifier === \"1\") {\n        //设备类型\n        this.queryParams.sblxbm = node.id;\n        this.queryParams.parentSbbj = \"\";\n        this.queryParams.parentSbbw = \"\";\n      } else if (node.identifier === \"2\") {\n        //设备部件\n        this.queryParams.sblxbm = \"\";\n        this.queryParams.parentSbbj = node.id;\n        this.queryParams.parentSbbw = \"\";\n      } else if (node.identifier === \"3\") {\n        //设备部位\n        this.queryParams.sblxbm = \"\";\n        this.queryParams.parentSbbj = \"\";\n        this.queryParams.parentSbbw = node.id;\n      } else {\n        this.queryParams = { qxlb: this.qxlb };\n      }\n      this.getData();\n    },\n    //查询列表\n    getData(params) {\n      this.load = true;\n      this.queryParams = { ...this.queryParams, ...params };\n      getQxList(this.queryParams).then(res => {\n        this.tableAndPageInfo.tableData = res.data.records;\n        this.tableAndPageInfo.pager.total = res.data.total;\n        this.load = false;\n      });\n    },\n    //导出excel\n    exportExcel() {\n      // if(!this.selectData.length > 0){\n      //   this.$message.warning('请在左侧勾选要导出的数据')\n      //   return\n      // }\n      let fileName = \"隐患标准库\";\n      let exportUrl = \"/bzqxFlyj\";\n      exportExcel(exportUrl, this.queryParams, fileName);\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  height: 82.6vh;\n  max-height: 82.6vh;\n  overflow: auto;\n}\n/*给左侧数结构header加颜色*/\n.box-card .el-card__header {\n  background: #11ba6d !important;\n}\n.box-card {\n  margin: 0;\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.tree {\n  overflow-y: hidden;\n  overflow-x: scroll;\n  width: 80px;\n  height: 500px;\n}\n\n.el-tree {\n  min-width: 100%;\n  display: inline-block !important;\n}\n\n/deep/ .el-dialog:not(.is-fullscreen) {\n  margin-top: 8vh !important;\n}\n/* 设置滚动条的样式 */\n::-webkit-scrollbar {\n  width: 12px;\n}\n\n/* 滚动槽 */\n::-webkit-scrollbar-track {\n  //-webkit-box-shadow:inset006pxrgba(0,0,0,0.3);\n  border-radius: 10px;\n}\n\n/* 滚动条滑块 */\n::-webkit-scrollbar-thumb {\n  border-radius: 10px;\n  background: rgba(0, 0, 0, 0.1);\n  //-webkit-box-shadow:gba(0,0,0,0.5);\n}\n\n::-webkit-scrollbar-thumb:window-inactive {\n  background: rgba(0, 0, 0, 0.1);\n}\n</style>\n<style></style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl"}]}