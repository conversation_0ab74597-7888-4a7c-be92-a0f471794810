{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\userSelect\\index.vue?vue&type=style&index=0&lang=css&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\userSelect\\index.vue", "mtime": 1706897321243}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5wYy11c2VyLWluZm8tc2VsZWN0LWRpYWxvZyAuZWwtZGlhbG9nX19ib2R5IHsKICBwYWRkaW5nOiAwIDIwcHggMTBweCAyMHB4ICFpbXBvcnRhbnQ7CiAgY29sb3I6ICM2MDYyNjY7CiAgZm9udC1zaXplOiAxNHB4OwogIHdvcmQtYnJlYWs6IGJyZWFrLWFsbDsKfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8dA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/userSelect", "sourcesContent": ["<template>\n  <div ref=\"select\" class=\"pc-user-info-select \" @mouseenter=\"()=>showClear=true\" @mouseleave=\"()=>showClear=false\">\n\n    <div ref=\"tags\" class=\"el-select__tags\" v-if=\"multiple\" @click=\"showDialog\"\n         :style=\"{ 'max-width': inputWidth - 30 + 'px', width: '100%' }\">\n            <span v-if=\"hasVal\">\n                <el-tag class=\"inner-tag\" v-for=\"tag in currentValArr\" :key=\"tag.userId\" size=\"mini\" type=\"info\"\n                        @close=\"removeItem(tag)\" :closable=\"!disabled\"> {{tag.nickName}}\n                </el-tag>\n            </span>\n    </div>\n\n    <div class=\"el-input el-input--mini el-input--suffix\">\n      <input type=\"text\" readonly=\"readonly\" ref=\"reference\" @click=\"showDialog\"\n             autocomplete=\"off\" :placeholder=\"placeholder\"\n             :class=\"{'disabled':disabled}\" class=\"el-input__inner\" :value=\"currentText\" :disabled=\"disabled\"\n             :style=\"{'height':inputHeight}\"/>\n      <span class=\"el-input__suffix\"  @click.stop=\"clearHandle\"  v-show=\"showClear && hasVal\">\n                <span class=\"el-input__suffix-inner\" v-if=\"clearable && !disabled\">\n                 <i class=\"el-input__icon el-icon-circle-close el-input__clear\"/>\n                </span>\n            </span>\n    </div>\n\n    <el-dialog title=\"人员选择\" :close-on-press-escape=\"false\" append-to-body :close-on-click-modal=\"false\"\n               class=\"pc-user-info-select-dialog\" v-dialogDrag :visible.sync=\"dialogVisible\">\n      <template #title>\n        <svg-icon icon-class=\"user-add\"/>\n        <span style=\"font-size: 12px;font-weight: 900;color: #fff\">{{ dialogMate.title }}</span>\n      </template>\n\n      <gridvo-collapse-search-bar>\n        <el-form ref=\"searchForm\" v-model=\"searchForm\" :inline=\"true\">\n          <el-form-item prop=\"name\">\n            <el-input v-model=\"searchForm.nickName\" @keyup.enter.native=\"getData\"\n                      maxlength=\"16\" placeholder=\"请输入姓名\" clearable/>\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"getData\">查询</el-button>\n          </el-form-item>\n        </el-form>\n      </gridvo-collapse-search-bar>\n\n      <el-row v-if=\"multiple\" style=\"margin-top: -1px\">\n        <el-col>\n          <div class=\"select-panel\">\n            <el-tag\n              style=\"margin: 2px\"\n              v-for=\"tag in selectVal\"\n              :key=\"tag.userId\" size=\"mini\"\n              closable\n              @close=\"selectionChangeHandle(false,tag)\"\n              type=\"info\">\n              {{tag.nickName}}\n            </el-tag>\n          </div>\n        </el-col>\n      </el-row>\n\n      <el-row>\n        <el-col :span=\"6\">\n          <el-container style=\"max-height: 370px;border-right: 1px dashed #dedede; margin-right: 10px\">\n            <el-main style=\"padding: 5px 5px 5px 0\">\n              <el-scrollbar class=\"custom-scrollbar\" style=\"min-height: 360px;\">\n                <el-tree\n                  :data=\"treeData\"\n                  node-key=\"tree1\"\n                  ref=\"tree\"\n                  check-strictly\n                  :highlight-current='true'\n                  :check-on-click-node=\"false\"\n                  :accordion=\"false\"\n                  :default-checked-keys=\"[checkedkey]\"\n                  :default-expanded-keys=\"checkedkey\"\n                  :props=\"defaultProps\"\n                  :default-expand-all=\"false\"\n                  @node-click=\"nodeClick\"\n                ></el-tree>\n              </el-scrollbar>\n            </el-main>\n          </el-container>\n        </el-col>\n        <el-col :span=\"18\">\n          <div style=\"\">\n            <el-table\n              v-loading=\"loading\"\n              :data=\"tableData\"\n              row-key=\"userId\"\n              stripe\n              border\n              :highlight-current-row=\"true\"\n              @row-click=\"rowClickHandle\"\n              @select=\"selectionChangeHandle\"\n              style=\"width: 100%;\">\n              <el-table-column\n                align=\"center\"\n                property=\"nickName\"\n                label=\"姓名\" width=\"170\">\n              </el-table-column>\n              <el-table-column\n                align=\"center\"\n                property=\"sex\"\n                label=\"性别\" width=\"170\">\n                <template slot-scope=\"scope\">\n                  {{ scope.row.sex===1?'男':'女'}}\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"center\"\n                property=\"email\"\n                label=\"邮箱\" width=\"170\">\n              </el-table-column>\n              <!--<el-table-column\n                align=\"center\"\n                property=\"organization\"\n                label=\"所属组织\" width=\"155\">\n                <template slot-scope=\"scope\">\n                  {{ scope.row.organization.name || '-'}}\n                </template>\n              </el-table-column>-->\n              <!--<el-table-column\n                align=\"center\"\n                property=\"organization\"\n                label=\"用户角色\">\n                <template slot-scope=\"scope\">\n                  {{ scope.row.roles | rolesFilter}}\n                </template>\n              </el-table-column>-->\n\n              <el-table-column\n                label=\"选择\"\n                property=\"operate\"\n                align=\"center\"\n                width=\"55\">\n                <template slot-scope=\"scope\">\n                  <el-checkbox :value=\"isChecked(scope.row)\"\n                               @change=\"(val)=>selectionChangeHandle(val,scope.row)\"/>\n                </template>\n              </el-table-column>\n            </el-table>\n\n            <el-pagination\n              @current-change=\"paginationChange\"\n              @size-change=\"handleSizeChange\"\n              :page-size=\"pageSize\"\n              :page-sizes=\"pageSizes\"\n              layout=\"total,  prev, pager, next\"\n              :total=\"pageTotal\"\n              style=\"text-align: center;margin-top: 5px\"\n            >\n            </el-pagination>\n          </div>\n        </el-col>\n      </el-row>\n\n      <template slot=\"footer\">\n        <div style=\"text-align: center\">\n          <el-button @click=\"selectConfirm\" type=\"primary\">确定</el-button>\n        </div>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n\n  import {deptTreeSelect,groupTreeSelect,UsersByDeptOrGroup} from \"@/api/component/userSelect\";\n  import emitter from 'element-ui/src/mixins/emitter';\n\n  /**\n   * 用户选择器\n   */\n  export default {\n    name: 'UserSelect',\n    mounted() {\n      this.resetInputHeight();\n      if (this.clearable) {\n        this.inputWidth = this.$refs.select.clientWidth;\n      }\n      this.initTree()\n    },\n    updated() {\n      this.resetInputHeight();\n    },\n    mixins: [emitter],\n    components: {},\n    props: {\n      placeholder: {\n        type: String,\n        default: '请选择人员'\n      },\n      //是否多选\n      multiple: {\n        type: Boolean,\n        default: false,\n      },\n      //是否清空\n      clearable: {\n        type: Boolean,\n        default: false,\n      },\n      //是否禁用\n      disabled: {\n        type: Boolean,\n        default: false,\n      },\n      value: {\n        type: [Object, Array],\n        default() {\n          return {};\n        }\n      }\n    },\n    data() {\n      let selectArr = [];\n      if (this.value instanceof Array) {\n        selectArr = [].concat(this.value);\n      } else if (this.value && this.value.userId) {\n        selectArr.push(this.value);\n      }\n      return {\n        loadTimes: 0,\n        loading: false,\n        showClear: false,\n        currentVal: this.value,\n        selectVal: selectArr,\n        inputHeight: '28px',\n        inputWidth: 0,\n        dialogVisible: false,\n        searchForm: {\n          jobNumberPhoneLike: null,\n        },\n        pageTotal: 0,\n        pageSize: 9,\n        pageSizes: [5, 10, 15, 20],\n        pageNo: 1,\n        tableData: [],\n        organizationId: null,\n        dialogMate: {\n          title: '人员选择'\n        },\n        defaultProps: {\n          children: 'children',\n          label: 'label'\n        },\n        checkedkey:[],\n        treeData:[]\n      }\n    },\n    computed: {\n      currentValArr() {\n        if (!this.currentVal) {\n          return [];\n        }\n        if (this.currentVal instanceof Array) {\n          return this.currentVal;\n        }\n        return [this.currentVal];\n      },\n      currentText() {\n        if (!this.hasVal) {\n          return undefined;\n        }\n        if (this.currentVal instanceof Array) {\n          return ' ';\n        }\n        return this.currentValArr[0].nickName;\n      },\n      hasVal() {\n        if (!this.currentVal) {\n          return false;\n        }\n        if (this.currentVal instanceof Array) {\n          return this.currentVal.length > 0;\n        }\n        return this.currentVal.userId;\n      }\n    },\n    methods: {\n      async initTree(){\n        let {data,code}=await deptTreeSelect()\n        this.treeData=data\n      },\n      handleSizeChange(val) {\n        this.pageSize = val;\n        this.getData()\n      },\n      rowClickHandle(row, column) {\n        if (column.property === \"operate\") {\n          return;\n        }\n        let exist = this.selectVal.findIndex(item => item.userId === row.userId) !== -1;\n        this.selectionChangeHandle(!exist, row);\n      },\n      appendSelectVal(val) {\n        if (!val || !val.userId) {\n          return;\n        }\n        if (!this.multiple && (!this.selectVal || this.selectVal.length === 0 || this.selectVal[0].userId !== val.userId)) {  //单选\n          this.selectVal = [val];\n          return;\n        }\n        let exist = this.selectVal.find(item => item.userId === val.userId);\n        if (exist) {\n          return;\n        }\n        this.selectVal.push(val);\n      },\n      isChecked(val) {\n        return this.selectVal.filter(item => item.userId === val.userId).length > 0\n      },\n      /**\n       * 打开对话框\n       * @date 2020/5/19 11:15\n       */\n      showDialog() {\n        if (this.disabled) {\n          return;\n        }\n        if (this.loadTimes === 0) {\n          this.getData();\n        }\n        this.dialogVisible = true;\n      },\n      /**\n       * 确认\n       * @date 2020/5/19 11:15\n       */\n      selectConfirm() {\n        if (!this.multiple) {\n          this.currentVal = this.selectVal[0];\n        } else {\n          this.currentVal = [].concat(this.selectVal);\n        }\n        console.log(this.currentVal)\n        this.$emit('input', this.currentVal);\n        this.$emit('change', this.currentVal);\n        //this.dispatch('ElFormItem', 'el.form.change', [this.currentVal]);\n        this.dialogVisible = false\n      },\n      /**\n       * 清除某项\n       * @date 2020/7/31 10:48\n       */\n      removeItem(item) {\n        if (!item || !item.userId) {\n          return;\n        }\n        this.selectionChangeHandle(false, item);\n        this.selectConfirm();\n      },\n      /**\n       * 清空\n       * @date 2020/7/31 10:33\n       */\n      clearHandle() {\n        this.selectVal = [];\n        this.selectConfirm();\n        this.$emit('clear');\n      },\n      /**\n       * 调整调度\n       * @date 2020/7/30 20:08\n       */\n      resetInputHeight() {\n        if (!this.multiple) {\n          return;\n        }\n        if (this.currentValArr.length <= 0) {\n          this.inputHeight = '28px';\n          return;\n        }\n\n        this.$nextTick(() => {\n          if (!this.$refs.reference) return;\n          const tags = this.$refs.tags;\n          const sizeInMap = 28 || 40;\n          this.inputHeight = this.currentValArr.length === 0\n            ? sizeInMap + 'px'\n            : Math.max(\n            tags ? (tags.clientHeight + (tags.clientHeight > sizeInMap ? 6 : 0)) : 0,\n            sizeInMap\n          ) + 'px';\n        });\n\n        setTimeout(() => {\n          if (!this.$refs.reference) return;\n          const tags = this.$refs.tags;\n          const sizeInMap = 28 || 40;\n          this.inputHeight = this.currentValArr.length === 0\n            ? sizeInMap + 'px'\n            : Math.max(\n            tags ? (tags.clientHeight + (tags.clientHeight > sizeInMap ? 6 : 0)) : 0,\n            sizeInMap\n          ) + 'px';\n        }, 340)\n\n      },\n      selectionChangeHandle(isChecked, row) {\n        if (isChecked) {\n          this.appendSelectVal(row);\n          return;\n        }\n        let index = this.selectVal.findIndex(item => item.userId === row.userId);\n        if (index >= 0) {\n          this.selectVal.splice(index, 1);\n        }\n      },\n      nodeClick(data) {\n        if (data.id === '0') {\n          this.organizationId = '';\n        }\n        this.organizationId = data.id;\n        this.getData();\n      },\n      paginationChange (val) {\n        this.pageNo = val;\n        this.getData();\n      },\n      getData() {\n        let query = {\n          pageNum: this.pageNo,\n          pageSize: this.pageSize,\n        };\n\n        Object.assign(query, this.searchForm);\n        this.loading = true;\n\n        UsersByDeptOrGroup(query,'dept').then(res => {\n          this.loading = false;\n          if (res.code !== '0000') {\n            this.loadTimes = 0;\n            this.$message.error(res.msg);\n            return;\n          }\n          this.loadTimes++;\n          this.pageTotal = res.data.total;\n          this.tableData = res.data.records;\n        })\n      },\n    },\n    watch: {\n      clearable() {\n        this.inputWidth = this.$refs.select.clientWidth;\n      },\n      currentValArr() {\n        this.resetInputHeight();\n      },\n      value(val) {\n        let selectArr = [];\n        if (val instanceof Array) {\n          selectArr = [].concat(val);\n        } else if (val && val.userId) {\n          selectArr.push(val);\n        }\n        this.currentVal = val;\n        this.selectVal = selectArr;\n      }\n    },\n    filters: {\n      rolesFilter(roles) {\n        if (!roles || roles.length <= 0) {\n          return '-';\n        }\n        return roles.map(role => {\n          return role.name;\n        }).reduce((x, y) => {\n          return x + \"|\" + y;\n        });\n      }\n    },\n    beforeDestroy() {\n\n    }\n  }\n</script>\n\n<style>\n  .pc-user-info-select-dialog .el-dialog__body {\n    padding: 0 20px 10px 20px !important;\n    color: #606266;\n    font-size: 14px;\n    word-break: break-all;\n  }\n</style>\n\n<style scoped>\n  .pc-user-info-select {\n    display: inline-block;\n    position: relative;\n  }\n\n  .disabled {\n    background-color: #f5f7fa;\n    border-color: #e4e7ed;\n    color: #C0C4CC;\n    cursor: not-allowed;\n  }\n\n  .select-panel {\n    border: 1px dashed #e0e0e0;\n    margin-bottom: 10px;\n    min-height: 37px;\n    padding: 5px;\n  }\n\n  .inner-tag {\n    margin: 2px 0 2px 6px;\n  }\n</style>\n"]}]}