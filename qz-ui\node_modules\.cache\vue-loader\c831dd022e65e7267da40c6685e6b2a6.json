{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\gfgl\\gftz.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\gfgl\\gftz.vue", "mtime": 1752467888488}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsKaW1wb3J0IHsKICBhZGRBc3NldCwKICBhZGRCZHosCiAgYWRkSmcsCiAgZ2V0RmdzQnlCZHpJZCwKICBnZXRKZ0luZm9MaXN0LAogIGdldE5ld1RyZWVJbmZvLAogIGdldE9yZ2FuaXphdGlvblNlbGVjdGVkLAogIGdldFRyZWVJbmZvLAogIHJlbW92ZUFzc2V0LAogIHJlbW92ZUJkeiwKICByZW1vdmVKZywKICBhZGRkd3p5ZnN0eiwKICBleHBvcnRFeGNlbCwKICBjb3B5QXNzZXQKfSBmcm9tICJAL2FwaS9kYWdhbmdPaWxmaWVsZC9hc3NldC9nZmpndHoiOwppbXBvcnQgewogIGdldEJkQXNlc2V0TGlzdFBhZ2UsCiAgZ2V0QmR6RGF0YUxpc3RTZWxlY3RlZCwKICBnZXRKZ0RhdGFMaXN0U2VsZWN0ZWQsCiAgZ2V0U2J4aExpc3QsCiAgZ2V0U2JseERhdGFMaXN0U2VsZWN0ZWQsCiAgYWRkU2J4aAp9IGZyb20gIkAvYXBpL2RhZ2FuZ09pbGZpZWxkL2Fzc2V0L2dmc2J0eiI7CmltcG9ydCB7IGdldEJkekxpc3QgfSBmcm9tICJAL2FwaS9kYWdhbmdPaWxmaWVsZC9hc3NldC9nZnp0eiI7CmltcG9ydCB7IGRlbGV0ZUJ5SWQsIGdldExpc3RCeUJ1c2luZXNzSWQgfSBmcm9tICJAL2FwaS90b29sL2ZpbGUiOwppbXBvcnQgewogIGdldFBhcmFtRGF0YUxpc3QsCiAgZ2V0UGFyYW1zVmFsdWUKfSBmcm9tICJAL2FwaS9kYWdhbmdPaWxmaWVsZC9hc3NldC9wYXJhbWV0ZXJzIjsKaW1wb3J0IHsgZ2V0UmVzdW1EYXRhTGlzdCB9IGZyb20gIkAvYXBpL2RhZ2FuZ09pbGZpZWxkL2Fzc2V0L3Nkc2IiOwppbXBvcnQgeyBnZXRVc2VycyB9IGZyb20gIkAvYXBpL3N5c3RlbS91c2VyZ3JvdXAiOwppbXBvcnQgeyBnZXREaWN0VHlwZURhdGEgfSBmcm9tICJAL2FwaS9zeXN0ZW0vZGljdC9kYXRhIjsKaW1wb3J0IEZzc3MgZnJvbSAiLi9mc3NzLnZ1ZSI7CmltcG9ydCB7IGdldERhdGEsIGdldFF0d3RsckRhdGEgfSBmcm9tICJAL2FwaS9ibGdrL2JsZ2siOwppbXBvcnQgeyBnZXRTeWJnamxEYXRhQnlQYWdlIH0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYnpnbC9zeWJnbHIiOwppbXBvcnQgeyBnZXRMaXN0Rmlyc3QgfSBmcm9tICJAL2FwaS95eGdsL2JkeXhnbC9xeGdsIjsKaW1wb3J0IHsKICBnZXRGZ3NPcHRpb25zLAogIGdldFNlbGVjdE9wdGlvbnNCeU9yZ1R5cGUsCiAgZ2V0TGlzdEZvdXIsCiAgZ2V0TGlzdFNlY29uZCwKICBnZXRMaXN0U2V2ZW4KfSBmcm9tICJAL2FwaS95eGdsL2dmeXhnbC9nZnpiZ2wiOwppbXBvcnQgaW1wb3J0RmlsZSBmcm9tICJAL2NvbXBvbmVudHMvRXhwb3J0RXhjZWwvaW1wb3J0RXhjZWwiOwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJnZnR6IiwKICBjb21wb25lbnRzOiB7IEZzc3MsIGltcG9ydEZpbGUgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgd3pEYXRhTGlzdE9wdGlvbnM6IFtdLAogICAgICBzYkRhdGFMaXN0OiBbXSwKICAgICAgZm9ybUNvcHk6IHt9LAogICAgICBpc1Nob3dDb3B5OiBmYWxzZSwKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIGljb25zOiB7CiAgICAgICAgYmR6TGlzdDogImNhdGVnb3J5VHJlZUljb25zIiwKICAgICAgICBiZHo6ICJ0YWJsZUljb24iLAogICAgICAgIGpnOiAiY2xhc3NJY29uIiwKICAgICAgICBqZ2RsOiAiY2xhc3NJY29uIiAvL+mXtOmalOWkp+exuwogICAgICB9LAoKICAgICAgLy/mlrDlop7orr7lpIfml7blhYnkvI/nlLXnq5nkuIvmi4nmoYYKICAgICAgYmR6T3B0aW9uc0RhdGFMaXN0OiBbXSwKICAgICAgLy/pl7TpmpTnsbvlnovkuIvmi4nmlbDmja4KICAgICAgamdseE9wdGlvbnNEYXRhTGlzdDogW10sCiAgICAgIC8v5qCR57uT5p6E55uR5ZCs5bGe5oCnCiAgICAgIGZpbHRlclRleHQ6ICIiLAogICAgICAvL+e7hOe7h+e7k+aehOS4i+aLieaVsOaNrgogICAgICBPcmdhbml6YXRpb25TZWxlY3RlZExpc3Q6IFtdLAogICAgICAvL+agkee7k+aehOS4iumdouW+l+etm+mAieahhuWPguaVsAogICAgICB0cmVlRm9ybToge30sCiAgICAgIC8v55S15Y6L562J57qn5LiL5ouJ5qGG5pWw5o2uCiAgICAgIFZvbHRhZ2VMZXZlbFNlbGVjdGVkTGlzdDogW10sCiAgICAgIC8v6Ze06ZqU54m55q6K55S15Y6L562J57qnCiAgICAgIGpnRHlkT3B0aW9uczogW10sCiAgICAgIC8v5bim5a2X5q+N55qE55S15Y6L562J57qnCiAgICAgIGR5ZGpPcHRpb25zV2l0aFN0cmluZzogW10sCiAgICAgIC8v5paw5aKe6K6+5aSH5pe26K6+5aSH54q25oCB5LiL5ouJ5qGG5pWw5o2uCiAgICAgIHNienRPcHRpb25zRGF0YUxpc3Q6IFtdLAogICAgICAvL+aWsOWinuiuvuWkh+S4i+aLieahhuaVsOaNrgogICAgICBwbGFjZU9wdGlvbnM6IFtdLAogICAgICB4c09wdGlvbnM6IFtdLAogICAgICB4Yk9wdGlvbnM6IFtdLAogICAgICBzaW5nbGVDbGlja0RhdGE6IHVuZGVmaW5lZCwKICAgICAgLy/pl7TpmpTkv6Hmga/mmK/lkKbmmL7npLoKICAgICAgamdTaG93OiBmYWxzZSwKICAgICAgZmlsdGVySW5mbzoge30sCiAgICAgIC8v6YCa55So5YiX6KGo5Y+C5pWwCiAgICAgIHRhYmxlQW5kUGFnZUluZm86IHt9LAogICAgICBqZ1F1ZXJ5UGFyYW1zOiB7CiAgICAgICAgc3NiZHo6IHVuZGVmaW5lZCwKICAgICAgICBkeWRqOiB1bmRlZmluZWQsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHBhZ2VOdW06IDEKICAgICAgfSwKICAgICAgamd4eEZvcm06IHsKICAgICAgICBvYmpJZDogdW5kZWZpbmVkLAogICAgICAgIHd6Ym06IHVuZGVmaW5lZCwKICAgICAgICB3emlkOiB1bmRlZmluZWQsCiAgICAgICAgc2NjajogdW5kZWZpbmVkLAogICAgICAgIGpnbWM6IHVuZGVmaW5lZCwKICAgICAgICB0eXJxOiB1bmRlZmluZWQsCiAgICAgICAgamdseDogdW5kZWZpbmVkLAogICAgICAgIGR5ZGo6IHVuZGVmaW5lZCwKICAgICAgICB6dDogdW5kZWZpbmVkLAogICAgICAgIGNjcnE6IHVuZGVmaW5lZCwKICAgICAgICBnZ3hoOiB1bmRlZmluZWQsCiAgICAgICAgamQ6IHVuZGVmaW5lZCwKICAgICAgICB3ZDogdW5kZWZpbmVkLAogICAgICAgIHNzZGQ6IHVuZGVmaW5lZCwKICAgICAgICBzc2JkejogdW5kZWZpbmVkLAogICAgICAgIGJkem1jOiB1bmRlZmluZWQsCiAgICAgICAgYno6IHVuZGVmaW5lZAogICAgICB9LAogICAgICAvL+mXtOmalOWxleekugogICAgICBqZ1Nob3dUYWJsZTogZmFsc2UsCiAgICAgIC8v6Ze06ZqU5re75Yqg5oyJ6ZKu5by55Ye65qGGCiAgICAgIGpnRGlhbG9nRm9ybVZpc2libGU6IGZhbHNlLAogICAgICAvL+W8ueWHuuahhuihqOWNlQogICAgICBmb3JtOiB7fSwKICAgICAgcGlja2VyT3B0aW9uczogewogICAgICAgIHNob3J0Y3V0czogWwogICAgICAgICAgewogICAgICAgICAgICB0ZXh0OiAi5LuK5aSpIiwKICAgICAgICAgICAgb25DbGljayhwaWNrZXIpIHsKICAgICAgICAgICAgICBwaWNrZXIuJGVtaXQoInBpY2siLCBuZXcgRGF0ZSgpKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgdGV4dDogIuaYqOWkqSIsCiAgICAgICAgICAgIG9uQ2xpY2socGlja2VyKSB7CiAgICAgICAgICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKCk7CiAgICAgICAgICAgICAgZGF0ZS5zZXRUaW1lKGRhdGUuZ2V0VGltZSgpIC0gMzYwMCAqIDEwMDAgKiAyNCk7CiAgICAgICAgICAgICAgcGlja2VyLiRlbWl0KCJwaWNrIiwgZGF0ZSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIHRleHQ6ICLmmI7lpKkiLAogICAgICAgICAgICBvbkNsaWNrKHBpY2tlcikgewogICAgICAgICAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZSgpOwogICAgICAgICAgICAgIGRhdGUuc2V0VGltZShkYXRlLmdldFRpbWUoKSArIDM2MDAgKiAxMDAwICogMjQpOwogICAgICAgICAgICAgIHBpY2tlci4kZW1pdCgicGljayIsIGRhdGUpOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgXQogICAgICB9LAogICAgICAvL+e7hOe7h+agkQogICAgICB0cmVlT3B0aW9uczogW10sCiAgICAgIC8v5Yig6Zmk5piv5ZCm5Y+v55SoCiAgICAgIG11bHRpcGxlU2Vuc29yOiB0cnVlLAogICAgICAvL+afpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHJvbGVLZXk6ICIiLAogICAgICAgIHJvbGVOYW1lOiAiIiwKICAgICAgICBzdGF0dXM6ICIiCiAgICAgIH0sCiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgc3Nkd2JtOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqSIsIHRyaWdnZXI6ICJjaGFuZ2UiIH1dLAogICAgICAgIC8vIGJkenN6Ymg6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+35aGr5YaZIiwgdHJpZ2dlcjogImJsdXIiIH1dLAogICAgICAgIGJkem1jOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+Whq+WGmSIsIHRyaWdnZXI6ICJibHVyIiB9XSwKICAgICAgICBid2Q6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+35aGr5YaZIiwgdHJpZ2dlcjogImJsdXIiIH1dLAogICAgICAgIGR5ZGpibTogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6kiLCB0cmlnZ2VyOiAiYmx1ciIgfV0sCiAgICAgICAgYnd6OiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+Whq+WGmSIsIHRyaWdnZXI6ICJibHVyIiB9XSwKICAgICAgICB0eXJxOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqSIsIHRyaWdnZXI6ICJibHVyIiB9XSwKICAgICAgICAvLyBqbGNybDogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7floavlhpkiLCB0cmlnZ2VyOiAiYmx1ciIgfV0sCiAgICAgICAgLy8gemxjcmw6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+35aGr5YaZIiwgdHJpZ2dlcjogImJsdXIiIH1dLAogICAgICAgIC8vIGJkemR6OiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+Whq+WGmSIsIHRyaWdnZXI6ICJibHVyIiB9XSwKICAgICAgICAvLyBkemx4OiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqSIsIHRyaWdnZXI6ICJibHVyIiB9XSwKICAgICAgICAvLyBiemZzOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqSIsIHRyaWdnZXI6ICJibHVyIiB9XSwKCiAgICAgICAgamdtYzogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7floavlhpkiLCB0cmlnZ2VyOiAiYmx1ciIgfV0sCiAgICAgICAgc3NiZHo6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6kiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9CiAgICAgICAgXSwKICAgICAgICBkeWRqOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqSIsIHRyaWdnZXI6ICJjaGFuZ2UiIH1dCiAgICAgIH0sCiAgICAgIC8v5pawCiAgICAgIC8v5o6n5Yi25YWJ5LyP55S156uZ6KGo5qC85piv5ZCm5bGV56S6CiAgICAgIGJkemRhdGFTaG93OiB0cnVlLAogICAgICBpbXBvcnRFeGNlbFVybDogIiIsCiAgICAgIGZpbGVOYW1lOiAiIiwKICAgICAgLy/mjqfliLblhYnkvI/nlLXnq5nooajmoLzmmK/lkKblsZXnpLoKICAgICAgamdkYXRhU2hvdzogZmFsc2UsCiAgICAgIC8v5o6n5Yi25YWJ5LyP55S156uZ6KGo5qC85piv5ZCm5bGV56S6CiAgICAgIHpuc2JkYXRhU2hvdzogZmFsc2UsCiAgICAgIGJkenF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgc3NnczogdW5kZWZpbmVkLAogICAgICAgIGR5ZGpibTogdW5kZWZpbmVkLAogICAgICAgIHNzYmR6OiB1bmRlZmluZWQsCiAgICAgICAgc3NqZzogdW5kZWZpbmVkLAogICAgICAgIHNibWM6IHVuZGVmaW5lZCwKICAgICAgICBhc3NldFR5cGVDb2RlOiB1bmRlZmluZWQsCiAgICAgICAgc2J6dDogdW5kZWZpbmVkLAogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwCiAgICAgIH0sCiAgICAgIHBhcmFtczogewogICAgICAgIC8vYm06dW5kZWZpbmVkLAogICAgICAgIC8vIHNzZHdibTogdW5kZWZpbmVkLAogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwCiAgICAgIH0sCiAgICAgIGZpbHRlckluZm8xOiB7CiAgICAgICAgZGF0YTogewogICAgICAgICAgdHlycUFycjogW10sCiAgICAgICAgICBkeWRqOiAiIiwKICAgICAgICAgIGJkem1jOiAiIgogICAgICAgIH0sCiAgICAgICAgZmllbGRMaXN0OiBbCiAgICAgICAgICB7IGxhYmVsOiAi5YWJ5LyP55S156uZ5ZCN56ewIiwgdHlwZTogImlucHV0IiwgdmFsdWU6ICJiZHptYyIgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLmipXov5Dml6XmnJ8iLAogICAgICAgICAgICB0eXBlOiAiZGF0ZSIsCiAgICAgICAgICAgIHZhbHVlOiAidHlycUFyciIsCiAgICAgICAgICAgIGRhdGVUeXBlOiAiZGF0ZXJhbmdlIiwKICAgICAgICAgICAgZm9ybWF0OiAieXl5eS1NTS1kZCIKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi55S15Y6L562J57qnIiwKICAgICAgICAgICAgdHlwZTogImNoZWNrYm94IiwKICAgICAgICAgICAgY2hlY2tib3hWYWx1ZTogW10sCiAgICAgICAgICAgIHZhbHVlOiAiZHlkaiIsCiAgICAgICAgICAgIG9wdGlvbnM6IFtdCiAgICAgICAgICB9CiAgICAgICAgXQogICAgICB9LAogICAgICAvL+WFieS8j+eUteermXRhYmxl5pWw5o2uCiAgICAgIHRhYmxlQW5kUGFnZUluZm8xOiB7CiAgICAgICAgcGFnZXI6IHsKICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgIHNpemVzOiBbMTAsIDIwLCA1MCwgMTAwXSwKICAgICAgICAgIHBhZ2VSZXNpemU6ICIiCiAgICAgICAgfSwKICAgICAgICBvcHRpb246IHsKICAgICAgICAgIGNoZWNrQm94OiB0cnVlLAogICAgICAgICAgc2VyaWFsTnVtYmVyOiB0cnVlCiAgICAgICAgfSwKICAgICAgICB0YWJsZURhdGE6IFtdLAogICAgICAgIHRhYmxlSGVhZGVyOiBbCiAgICAgICAgICB7IHByb3A6ICJzc2R3bWMiLCBsYWJlbDogIuaJgOWxnuWNleS9jSIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBwcm9wOiAiYmR6bWMiLCBsYWJlbDogIuWFieS8j+eUteermeWQjeensCIsIG1pbldpZHRoOiAiMTgwIiB9LAogICAgICAgICAgeyBwcm9wOiAiZHlkaiIsIGxhYmVsOiAi55S15Y6L562J57qnIiwgbWluV2lkdGg6ICI4MCIgfSwKICAgICAgICAgIHsgcHJvcDogImpsY3JsIiwgbGFiZWw6ICLkuqTmtYHkvqflrrnph49NVyIsIG1pbldpZHRoOiAiODAiIH0sCiAgICAgICAgICB7IHByb3A6ICJ6bGNybCIsIGxhYmVsOiAi55u05rWB5L6n5a656YePTVciLCBtaW5XaWR0aDogIjgwIiB9LAogICAgICAgICAgeyBwcm9wOiAienltaiIsIGxhYmVsOiAi5Y2g5Zyw6Z2i56evIiwgbWluV2lkdGg6ICI5MCIgfSwKICAgICAgICAgIHsgcHJvcDogImJkemR6IiwgbGFiZWw6ICLnq5nlnYAiLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgcHJvcDogImJ3eiIsIGxhYmVsOiAi5bm2572R56uZIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJid2QiLCBsYWJlbDogIuW5tue9keeCuSIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBwcm9wOiAidHlycSIsIGxhYmVsOiAi5oqV6L+Q5pel5pyfIiwgbWluV2lkdGg6ICIxMjAiIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIGZpbHRlckluZm8yOiB7CiAgICAgICAgZGF0YTogewogICAgICAgICAgeXdkd0FycjogW10sCiAgICAgICAgICBqaG55QXJyOiBbXSwKICAgICAgICAgIHhsQXJyOiAiIiwKICAgICAgICAgIGpobHhBcnI6IFtdLAogICAgICAgICAgamh6dEFycjogIiIsCiAgICAgICAgICBzZmRkOiAiIgogICAgICAgIH0sCiAgICAgICAgZmllbGRMaXN0OiBbCiAgICAgICAgICB7IGxhYmVsOiAi6Ze06ZqU5ZCN56ewIiwgdHlwZTogImlucHV0IiwgdmFsdWU6ICJqZ21jIiwgb3B0aW9uczogW10gfSwKICAgICAgICAgIHsgbGFiZWw6ICLpl7TpmpTnsbvlnosiLCB0eXBlOiAiaW5wdXQiLCB2YWx1ZTogImpnbHgiLCBvcHRpb25zOiBbXSB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIueUteWOi+etiee6pyIsCiAgICAgICAgICAgIHR5cGU6ICJzZWxlY3QiLAogICAgICAgICAgICB2YWx1ZTogImR5ZGoiLAogICAgICAgICAgICBvcHRpb25zOiBbXQogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLmipXov5Dml6XmnJ8iLAogICAgICAgICAgICB0eXBlOiAiZGF0ZSIsCiAgICAgICAgICAgIHZhbHVlOiAidHlycUFyciIsCiAgICAgICAgICAgIGRhdGVUeXBlOiAiZGF0ZXJhbmdlIiwKICAgICAgICAgICAgZm9ybWF0OiAieXl5eS1NTS1kZCIKICAgICAgICAgIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIC8v6Ze06ZqU5pWw5o2uCiAgICAgIHRhYmxlQW5kUGFnZUluZm8yOiB7CiAgICAgICAgcGFnZXI6IHsKICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgIHNpemVzOiBbMTAsIDIwLCA1MCwgMTAwXSwKICAgICAgICAgIHBhZ2VSZXNpemU6ICIiCiAgICAgICAgfSwKICAgICAgICBvcHRpb246IHsKICAgICAgICAgIGNoZWNrQm94OiB0cnVlLAogICAgICAgICAgc2VyaWFsTnVtYmVyOiB0cnVlCiAgICAgICAgfSwKICAgICAgICB0YWJsZURhdGE6IFtdLAogICAgICAgIHRhYmxlSGVhZGVyOiBbCiAgICAgICAgICB7IHByb3A6ICJqZ21jIiwgbGFiZWw6ICLpl7TpmpTlkI3np7AiLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgcHJvcDogImpnbHgiLCBsYWJlbDogIumXtOmalOexu+WeiyIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBwcm9wOiAiZHlkaiIsIGxhYmVsOiAi55S15Y6L562J57qnIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJ0eXJxIiwgbGFiZWw6ICLmipXov5Dml6XmnJ8iLCBtaW5XaWR0aDogIjEyMCIgfQogICAgICAgICAgLyp7CiAgICAgICAgICAgIGZpeGVkOiAicmlnaHQiLAogICAgICAgICAgICBwcm9wOiAnb3BlcmF0aW9uJywKICAgICAgICAgICAgbGFiZWw6ICfmk43kvZwnLAogICAgICAgICAgICBtaW5XaWR0aDogJzEzMHB4JywKICAgICAgICAgICAgc3R5bGU6IHtkaXNwbGF5OiAnYmxvY2snfSwKICAgICAgICAgICAgb3BlcmF0aW9uOiBbCiAgICAgICAgICAgICAge25hbWU6ICfkv67mlLknLCBjbGlja0Z1bjogdGhpcy51cGRhdGVKZ30sCiAgICAgICAgICAgICAge25hbWU6ICfor6bmg4UnLCBjbGlja0Z1bjogdGhpcy5qZ0RldGFpbHN9LAogICAgICAgICAgICBdCiAgICAgICAgICB9LCovCiAgICAgICAgXQogICAgICB9LAogICAgICBmaWx0ZXJJbmZvMzogewogICAgICAgIGRhdGE6IHsKICAgICAgICAgIHl3ZHdBcnI6IFtdLAogICAgICAgICAgamhueUFycjogW10sCiAgICAgICAgICB4bEFycjogIiIsCiAgICAgICAgICBqaGx4QXJyOiBbXSwKICAgICAgICAgIGpoenRBcnI6ICIiLAogICAgICAgICAgc2ZkZDogIiIKICAgICAgICB9LAogICAgICAgIGZpZWxkTGlzdDogWwogICAgICAgICAgeyBsYWJlbDogIuiuvuWkh+exu+WeiyIsIHR5cGU6ICJpbnB1dCIsIHZhbHVlOiAic2JseG1jIiwgb3B0aW9uczogW10gfSwKICAgICAgICAgIHsgbGFiZWw6ICLorr7lpIflkI3np7AiLCB0eXBlOiAiaW5wdXQiLCB2YWx1ZTogInNibWMiLCBvcHRpb25zOiBbXSB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIueUteWOi+etiee6pyIsCiAgICAgICAgICAgIHR5cGU6ICJzZWxlY3QiLAogICAgICAgICAgICB2YWx1ZTogImR5ZGpOYW1lIiwKICAgICAgICAgICAgb3B0aW9uczogW10KICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi6K6+5aSH54q25oCBIiwKICAgICAgICAgICAgdHlwZTogInNlbGVjdCIsCiAgICAgICAgICAgIHZhbHVlOiAic2J6dCIsCiAgICAgICAgICAgIG9wdGlvbnM6IFtdCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIuaKlei/kOaXpeacnyIsCiAgICAgICAgICAgIHR5cGU6ICJkYXRlIiwKICAgICAgICAgICAgdmFsdWU6ICJ0eXJxQXJyIiwKICAgICAgICAgICAgZGF0ZVR5cGU6ICJkYXRlcmFuZ2UiLAogICAgICAgICAgICBmb3JtYXQ6ICJ5eXl5LU1NLWRkIgogICAgICAgICAgfSwKICAgICAgICAgIHsgbGFiZWw6ICLlnovlj7ciLCB0eXBlOiAiaW5wdXQiLCB2YWx1ZTogImdneGgiLCBvcHRpb25zOiBbXSB9LAogICAgICAgICAgeyBsYWJlbDogIumineWumueUteWOiyIsIHR5cGU6ICJpbnB1dCIsIHZhbHVlOiAiZWRkeSIsIG9wdGlvbnM6IFtdIH0sCiAgICAgICAgICB7IGxhYmVsOiAi6aKd5a6a55S15rWBIiwgdHlwZTogImlucHV0IiwgdmFsdWU6ICJlZGRsIiwgb3B0aW9uczogW10gfSwKICAgICAgICAgIHsgbGFiZWw6ICLpop3lrprpopHnjociLCB0eXBlOiAiaW5wdXQiLCB2YWx1ZTogImVkcGwiLCBvcHRpb25zOiBbXSB9LAogICAgICAgICAgeyBsYWJlbDogIueUn+S6p+WOguWutiIsIHR5cGU6ICJpbnB1dCIsIHZhbHVlOiAic2NjaiIsIG9wdGlvbnM6IFtdIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIC8v56uZ5YaF6K6+5aSH5Y+w6LSmCiAgICAgIHRhYmxlQW5kUGFnZUluZm8zOiB7CiAgICAgICAgcGFnZXI6IHsKICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgIHNpemVzOiBbMTAsIDIwLCA1MCwgMTAwXSwKICAgICAgICAgIHBhZ2VSZXNpemU6ICIiCiAgICAgICAgfSwKICAgICAgICBvcHRpb246IHsKICAgICAgICAgIGNoZWNrQm94OiB0cnVlLAogICAgICAgICAgc2VyaWFsTnVtYmVyOiB0cnVlCiAgICAgICAgfSwKICAgICAgICB0YWJsZURhdGE6IFtdLAogICAgICAgIHRhYmxlSGVhZGVyOiBbCiAgICAgICAgICB7IHByb3A6ICJkZXB0bmFtZSIsIGxhYmVsOiAi5omA5bGe5YWs5Y+4IiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJiZHptYyIsIGxhYmVsOiAi5YWJ5LyP55S156uZ5ZCN56ewIiwgbWluV2lkdGg6ICIxODAiIH0sCiAgICAgICAgICB7IHByb3A6ICJ3em1jIiwgbGFiZWw6ICLmiYDlsZ7pl7TpmpQiLCBtaW5XaWR0aDogIjE4MCIgfSwKICAgICAgICAgIHsgcHJvcDogInNibHhtYyIsIGxhYmVsOiAi6K6+5aSH57G75Z6LIiwgbWluV2lkdGg6ICIxODAiIH0sCiAgICAgICAgICB7IHByb3A6ICJzYm1jIiwgbGFiZWw6ICLorr7lpIflkI3np7AiLCBtaW5XaWR0aDogIjE4MCIgfSwKICAgICAgICAgIHsgcHJvcDogImR5ZGpOYW1lIiwgbGFiZWw6ICLnlLXljovnrYnnuqciLCBtaW5XaWR0aDogIjgwIiB9LAogICAgICAgICAgeyBwcm9wOiAic2J6dCIsIGxhYmVsOiAi6K6+5aSH54q25oCBIiwgbWluV2lkdGg6ICI4MCIgfSwKICAgICAgICAgIHsgcHJvcDogInR5cnEiLCBsYWJlbDogIuaKlei/kOaXpeacnyIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBwcm9wOiAiZ2d4aCIsIGxhYmVsOiAi5Z6L5Y+3IiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJlZGR5IiwgbGFiZWw6ICLpop3lrprnlLXljosiLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgcHJvcDogImVkZGwiLCBsYWJlbDogIumineWumueUtea1gSIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBwcm9wOiAiZWRwbCIsIGxhYmVsOiAi6aKd5a6a6aKR546HIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJzY2NqIiwgbGFiZWw6ICLnlJ/kuqfljoLlrrYiLCBtaW5XaWR0aDogIjEyMCIgfQogICAgICAgICAgLyp7CiAgICAgICAgICAgIGZpeGVkOiAicmlnaHQiLAogICAgICAgICAgICBwcm9wOiAnb3BlcmF0aW9uJywKICAgICAgICAgICAgbGFiZWw6ICfmk43kvZwnLAogICAgICAgICAgICBtaW5XaWR0aDogJzEyMHB4JywKICAgICAgICAgICAgc3R5bGU6IHtkaXNwbGF5OiAnYmxvY2snfSwKICAgICAgICAgICAgb3BlcmF0aW9uOiBbCiAgICAgICAgICAgICAgLyEqe25hbWU6ICLnirbmgIHlj5jmm7QiLCBjbGlja0Z1bjogdGhpcy51cGRhdGVTdGF0dXN9LAogICAgICAgICAgICAgIHtuYW1lOiAi5rWB56iL5p+l55yLIiwgY2xpY2tGdW46IHRoaXMuenRiZ2xjU2F5fSwqIS8KICAgICAgICAgICAgICB7bmFtZTogJ+S/ruaUuScsIGNsaWNrRnVuOiB0aGlzLnVwZGF0ZUFzc2V0fSwKICAgICAgICAgICAgICB7bmFtZTogJ+ivpuaDhScsIGNsaWNrRnVuOiB0aGlzLmFzc2V0RGV0YWlsc30sCiAgICAgICAgICAgIF0KICAgICAgICAgIH0sKi8KICAgICAgICBdCiAgICAgIH0sCiAgICAgIC8v6K6+5aSH5Z+65pys5L+h5oGvCiAgICAgIGpieHhGb3JtOiB7CiAgICAgICAgYXR0YWNobWVudDogW10sCiAgICAgICAgb2JqSWQ6IHVuZGVmaW5lZCwKICAgICAgICBzc2R3bWM6IHVuZGVmaW5lZCwKICAgICAgICBzc2R3Ym06IHVuZGVmaW5lZCwKICAgICAgICBzYmRtOiB1bmRlZmluZWQsCiAgICAgICAgZGRzYmg6IHVuZGVmaW5lZCwKICAgICAgICBkeWRqOiB1bmRlZmluZWQsCiAgICAgICAgdHlycTogdW5kZWZpbmVkLAogICAgICAgIGpnZHk6IHVuZGVmaW5lZCwKICAgICAgICB4YjogdW5kZWZpbmVkLAogICAgICAgIHhzOiB1bmRlZmluZWQsCiAgICAgICAgY2NycTogdW5kZWZpbmVkLAogICAgICAgIGF6d3o6IHVuZGVmaW5lZCwKICAgICAgICB5dDogdW5kZWZpbmVkLAogICAgICAgIGZ6cjogdW5kZWZpbmVkLAogICAgICAgIGNwZGg6IHVuZGVmaW5lZCwKICAgICAgICBlZGR5OiB1bmRlZmluZWQsCiAgICAgICAgZWRwbDogdW5kZWZpbmVkLAogICAgICAgIHNienQ6IHVuZGVmaW5lZCwKICAgICAgICBzeWhqOiB1bmRlZmluZWQsCiAgICAgICAgc2NjajogdW5kZWZpbmVkLAogICAgICAgIHp6Z2o6IHVuZGVmaW5lZCwKICAgICAgICB6aHNibHg6IHVuZGVmaW5lZCwKICAgICAgICB6aHNibHhtYzogdW5kZWZpbmVkLAogICAgICAgIGVkZGw6IHVuZGVmaW5lZCwKICAgICAgICB5eGJoOiB1bmRlZmluZWQsCiAgICAgICAgY2NiaDogdW5kZWZpbmVkLAogICAgICAgIGJkem1jOiB1bmRlZmluZWQsCiAgICAgICAgYmR6c3piaDogdW5kZWZpbmVkLCAvL+WFieS8j+ermeaVsOWtl+e8luWPtwogICAgICAgIHNzZHc6IHVuZGVmaW5lZCwgLy/miYDlsZ7nlLXnvZEKICAgICAgICBkemx4OiB1bmRlZmluZWQsIC8v55S156uZ57G75Z6LCiAgICAgICAgc2Z6aHpkaDogdW5kZWZpbmVkLCAvL+aYr+WQpue7vOWQiOiHquWKqOWMluermQogICAgICAgIHNmc3poYmR6OiB1bmRlZmluZWQsIC8v5piv5ZCm5pWw5a2X5YyW5YWJ5LyP56uZCiAgICAgICAgcmV0dXJuRGF0ZTogdW5kZWZpbmVkLCAvL+mAgOi/kOaXpeacnwogICAgICAgIHp5bWo6IHVuZGVmaW5lZCwgLy/ljaDlnLDpnaLnp68KICAgICAgICB3aGRqOiB1bmRlZmluZWQsIC8v5rGh56e9562J57qnCiAgICAgICAgemJmczogdW5kZWZpbmVkLCAvL+WAvOePreaWueW8jwogICAgICAgIHNmZ3F0eDogdW5kZWZpbmVkLCAvL+aYr+WQpuWFiee6pOmAmuiurwogICAgICAgIGhiOiB1bmRlZmluZWQsIC8v5rW35ouUCiAgICAgICAgZ2NiaDogdW5kZWZpbmVkLCAvL+W3peeoi+e8luWPtwogICAgICAgIHNqZHc6IHVuZGVmaW5lZCwgLy/orr7orqHljZXkvY0KICAgICAgICBqbGR3OiB1bmRlZmluZWQsIC8v55uR55CG5Y2V5L2NCiAgICAgICAgenlqYjogdW5kZWZpbmVkLCAvLyDnlLXnq5nph43opoHnuqfliKsKICAgICAgICBiemZzOiB1bmRlZmluZWQsIC8v5biD572u5pa55byPCiAgICAgICAgYmR6ZHo6IHVuZGVmaW5lZCwgLy/lhYnkvI/nq5nlnLDlnYAKICAgICAgICBqem1qOiB1bmRlZmluZWQsIC8vIOW7uuetkemdouenrwogICAgICAgIHBob25lOiB1bmRlZmluZWQsIC8v6IGU57O755S16K+dCiAgICAgICAgZ2NtYzogdW5kZWZpbmVkLCAvLyDlt6XnqIvlkI3np7AKICAgICAgICBzZ2R3OiB1bmRlZmluZWQsIC8v5pa95bel5Y2V5L2NCiAgICAgICAgZHF0ejogdW5kZWZpbmVkLCAvL+WcsOWMuueJueW+gQogICAgICAgIHpnZGRneHE6IHVuZGVmaW5lZCwgLy8g5pyA6auY6LCD5bqm566h6L6W5p2DCiAgICAgICAgc2Ztem46IHVuZGVmaW5lZCwgLy8g5piv5ZCm5ruh6Lazbi0xCiAgICAgICAgc2Zqcmd6eHQ6IHVuZGVmaW5lZCwgLy/mmK/lkKbmjqXlhaXmlYXpmpzkv6Hmga/ov5zkvKDns7vnu58KICAgICAgICBzZmpyYXZjOiB1bmRlZmluZWQsIC8v5piv5ZCm5o6l5YWlYXZjCiAgICAgICAgc2ZqemprOiB1bmRlZmluZWQsIC8v5piv5ZCm6ZuG5Lit55uR5o6nCiAgICAgICAgamt6eG1jOiB1bmRlZmluZWQsIC8v5o6l5YWl5b6X55uR5o6n5Lit5b+DCiAgICAgICAgYno6IHVuZGVmaW5lZCAvL+Wkh+azqAogICAgICB9LAogICAgICAvL+WFieS8j+eUteermea3u+WKoOaMiemSruW8ueWHuuahhgogICAgICBiZHpEaWFsb2dGb3JtVmlzaWJsZTogZmFsc2UsCiAgICAgIC8v5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgaW1nTGlzdDogW10sCiAgICAgIGN1cnJVc2VyOiAiIiwKICAgICAgLy/lhYnkvI/nlLXnq5nkv6Hmga/mmK/lkKblj6/nvJbovpEKICAgICAgaXNEaXNhYmxlZDogZmFsc2UsCiAgICAgIC8v5LiK5Lyg5Zu+54mH5pe255qE6K+35rGC5aS0CiAgICAgIGhlYWRlcjoge30sCiAgICAgIC8v5omA5bGe5Z+65Zyw56uZCiAgICAgIHNzamR6TGlzdDogW10sCiAgICAgIHVwbG9hZERhdGE6IHsKICAgICAgICB0eXBlOiAiIiwKICAgICAgICBidXNpbmVzc0lkOiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgaW1wb3J0RXh0cmFJbmZvOiB7fSwKICAgICAgLy/lhYnkvI/nlLXnq5nmlrDlop7lvLnmoYYKICAgICAgYmR6RGlkYWxvZ0Zvcm06IGZhbHNlLAogICAgICBwYXJhbVF1ZXJ5OiB7CiAgICAgICAgc2JseGJtOiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgcmVzdW1lUXVlcnk6IHsKICAgICAgICBmb3JlaWduTnVtOiB1bmRlZmluZWQsCiAgICAgICAgc2JseDogdW5kZWZpbmVkCiAgICAgIH0sCiAgICAgIHpuc2JEaWFsb2dGb3JtOiBmYWxzZSwKICAgICAgYXNzZXRTdWJtaXRMb2FkaW5nOiBmYWxzZSwKICAgICAgLy/orr7lpIfooajljZUKICAgICAgc2J4eEZvcm06IHt9LAogICAgICAvL+iuvuWkh+S/oeaBr+WxleekugogICAgICBhc3NldElzRGlzYWJsZTogZmFsc2UsCiAgICAgIC8v5by55Ye65qGGdGFi6aG1CiAgICAgIGFjdGl2ZVRhYk5hbWU6ICJzYkRlc2MiLAogICAgICAvL+afpeivoumXtOmalOS4i+aLieahhuaVsOaNrueahOWPguaVsAogICAgICBzZWxlY3RKZ09wdGlvbnNQYXJhbToge30sCiAgICAgIC8v5paw5aKe6K6+5aSH5pe25omA5bGe6Ze06ZqU5LiL5ouJ5qGG5YiX6KGoCiAgICAgIGpnT3B0aW9uc0RhdGFMaXN0OiBbXSwKICAgICAgLy/orr7lpIfnsbvlnovkuIvmi4nmoYbmlbDmja4KICAgICAgc2JseE9wdGlvbnNEYXRhU2VsZWN0ZWQ6IFtdLAogICAgICAvL+aKgOacr+WPguaVsOe7keWumgogICAgICBqc2NzRm9ybToge30sCiAgICAgIC8v5oqA5pyv5Y+C5pWw5Yqo5oCB5bGV56S66ZuG5ZCICiAgICAgIGpzY3NMYWJlbExpc3Q6IFtdLAogICAgICAvL+iuvuWkh+WxpeWOhnRhYumhtQogICAgICBzYmxsRGVzY1RhYk5hbWU6ICJ6dGJnamwiLAogICAgICAvL+iuvuWkh+WxpeWOhuivlemqjOiusOW9leaVsOaNrgogICAgICBzYmx2c3lqbExpc3Q6IFtdLAogICAgICAvL+iuvuWkh+WxpeWOhue8uumZt+iusOW9leaVsOaNrumbhuWQiAogICAgICBzYmxscXhqbExpc3Q6IFtdLAogICAgICByZXN1bVBhZ2VJbmZvOiB7CiAgICAgICAgcGFnZXI6IHsKICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgIHNpemVzOiBbMTAsIDIwLCA1MCwgMTAwXQogICAgICAgIH0sCiAgICAgICAgb3B0aW9uOiB7CiAgICAgICAgICBjaGVja0JveDogZmFsc2UsCiAgICAgICAgICBzZXJpYWxOdW1iZXI6IHRydWUKICAgICAgICB9LAogICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgdGFibGVIZWFkZXI6IFsKICAgICAgICAgIHsgcHJvcDogImZvcmVpZ25OdW0iLCBsYWJlbDogIuiuvuWkh+WQjeensCIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgLy8geyBwcm9wOiAic2JseCIsIGxhYmVsOiAi6K6+5aSH57G75Z6LIiwgbWluV2lkdGg6ICIxODAiIH0sCiAgICAgICAgICB7IHByb3A6ICJiZ2x4IiwgbGFiZWw6ICLlj5jmm7TnsbvlnosiLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgcHJvcDogIm1zIiwgbGFiZWw6ICLmj4/ov7AiLCBtaW5XaWR0aDogIjI1MCIgfSwKICAgICAgICAgIHsgcHJvcDogImJnciIsIGxhYmVsOiAi5Y+Y5pu05Lq6IiwgbWluV2lkdGg6ICIxNDAiIH0sCiAgICAgICAgICB7IHByb3A6ICJiZ3NqIiwgbGFiZWw6ICLlj5jmm7Tml7bpl7QiLCBtaW5XaWR0aDogIjE0MCIgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgLy/orr7lpIfpmYTlsZ7orr7mlr1saXN0CiAgICAgIGZzc3NMaXN0OiBbXSwKICAgICAgLy/orr7lpIfor6bmg4XmoKHpqozop4TliJkKICAgICAgc2J4eFJ1bGVzOiB7CiAgICAgICAgc3NnczogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaJgOWxnuWFrOWPuOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJzZWxlY3QiIH0KICAgICAgICBdLAogICAgICAgIHNzYmR6OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5omA5bGe55S156uZ5LiN6IO95Li656m6IiwgdHJpZ2dlcjogInNlbGVjdCIgfQogICAgICAgIF0sCiAgICAgICAgc3NqZzogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaJgOWxnumXtOmalOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJzZWxlY3QiIH0KICAgICAgICBdLAogICAgICAgIHNibWM6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLorr7lpIflkI3np7DkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgYXNzZXRUeXBlQ29kZTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuiuvuWkh+exu+Wei+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJzZWxlY3QiIH0KICAgICAgICBdLAogICAgICAgIC8vIGR5ZGpibTogWwogICAgICAgIC8vICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi55S15Y6L562J57qn5LiN6IO95Li656m6IiwgdHJpZ2dlcjogInNlbGVjdCJ9LAogICAgICAgIC8vIF0sCiAgICAgICAgc2J6dDogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuiuvuWkh+eKtuaAgeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJzZWxlY3QiIH0KICAgICAgICBdLAogICAgICAgIC8vIGF6d3o6IFsKICAgICAgICAvLyAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWuieijheS9jee9ruS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIn0sCiAgICAgICAgLy8gXSwKICAgICAgICAvLyB4aDogWwogICAgICAgIC8vICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5Z6L5Y+35LiN6IO95Li656m6IiwgdHJpZ2dlcjogInNlbGVjdCJ9LAogICAgICAgIC8vIF0sCiAgICAgICAgLy8gZWRkeTogWwogICAgICAgIC8vICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6aKd5a6a55S15Y6L5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIifSwKICAgICAgICAvLyBdLAogICAgICAgIC8vIGVkcGw6IFsKICAgICAgICAvLyAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIumineWumumikeeOh+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIn0sCiAgICAgICAgLy8gXSwKICAgICAgICBzeWhqOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5L2/55So546v5aKD5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHNjY2o6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnlJ/kuqfljoLlrrbkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgLy8geXhiaDogWwogICAgICAgIC8vICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6L+Q6KGM57yW5Y+35LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIifSwKICAgICAgICAvLyBdLAogICAgICAgIC8vIGNjYmg6IFsKICAgICAgICAvLyAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWHuuWOgue8luWPt+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIn0sCiAgICAgICAgLy8gXSwKICAgICAgICAvLyBjY3JxOiBbCiAgICAgICAgLy8gICB7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlh7rljoLml6XmnJ/kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlIn0sCiAgICAgICAgLy8gXSwKICAgICAgICB0eXJxOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5oqV6L+Q5pel5pyf5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgcnVsZXNDb3B5OiB7CiAgICAgICAgc3Nkd2JtOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5omA5bGe5YWs5Y+45LiN6IO95Li656m6IiwgdHJpZ2dlcjogInNlbGVjdCIgfQogICAgICAgIF0sCiAgICAgICAgYmR6OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5omA5bGe55S156uZ5LiN6IO95Li656m6IiwgdHJpZ2dlcjogInNlbGVjdCIgfQogICAgICAgIF0sCiAgICAgICAgc3NqZzogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaJgOWxnumXtOmalOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJzZWxlY3QiIH0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIGdneGhMaXN0OiBbXSwgLy/lnovlj7dsaXN0CiAgICAgIGZvcm1hdHRlZFJlc3VsdHM6W10sCiAgICAgIGpnbHhjeDogIiIsIC8v5omA5bGe6Ze06ZqU57G75Z6LCiAgICAgIHNzYmR6OiAiIiwgLy/miYDlsZ7lhYnkvI/nlLXnq5ksCiAgICAgIHNzYmR6bWM6ICIiLAogICAgICBzc2pnOiAiIiwgLy/miYDlsZ7pl7TpmpQKICAgICAgem5zYlBhcmFtczogewogICAgICAgIC8v56uZ5YaF6K6+5aSH5YiG6aG15Y+C5pWwCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHBhZ2VOdW06IDEKICAgICAgfSwKICAgICAgZmdzQXJyOiBbXSwKICAgICAgamdkbE1hcDogbmV3IE1hcCgpLCAvL+WtmOmXtOmalOWkp+exu+aVsOaNrgogICAgICBjdXJyZW50QmR6OiAiIiwgLy/lvZPliY3lhYnkvI/nlLXnq5kKICAgICAgc2hvd0Zzc3M6IGZhbHNlLCAvL+aYr+WQpuaYvuekuumZhOWxnuiuvuaWvXRhYumhtQogICAgICBiZHNiaWQ6ICIiLCAvL+WFieS8j+iuvuWkh0lECiAgICAgIHNibWM6ICIiLCAvL+iuvuWkh+WQjeensAogICAgICAvL+S4jeiJr+W3peWGteWIl+ihqAogICAgICBibGdrUGFnZUluZm86IHsKICAgICAgICBwYWdlcjogewogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMDBdCiAgICAgICAgfSwKICAgICAgICBvcHRpb246IHsgY2hlY2tCb3g6IGZhbHNlLCBzZXJpYWxOdW1iZXI6IHRydWUgfSwKICAgICAgICB0YWJsZURhdGE6IFtdLAogICAgICAgIHRhYmxlSGVhZGVyOiBbCiAgICAgICAgICAvLyB7IHByb3A6ICJmZ3NtYyIsIGxhYmVsOiAi5YiG5YWs5Y+4IiwgbWluV2lkdGg6ICIxNDAiIH0sCiAgICAgICAgICAvLyB7IHByb3A6ICJiZHptYyIsIGxhYmVsOiAi5YWJ5LyP55S156uZIiwgbWluV2lkdGg6ICIxNDAiIH0sCiAgICAgICAgICAvLyB7IHByb3A6ICJqZ21jIiwgbGFiZWw6ICLpl7TpmpTlkI3np7AiLCBtaW5XaWR0aDogIjEwMCIgfSwKICAgICAgICAgIC8vIHsgcHJvcDogInNibHhtYyIsIGxhYmVsOiAi6K6+5aSH57G75Z6LIiwgbWluV2lkdGg6ICIxNDAiIH0sCiAgICAgICAgICB7IHByb3A6ICJzYm1jIiwgbGFiZWw6ICLorr7lpIflkI3np7AiLCBtaW5XaWR0aDogIjE0MCIgfSwKICAgICAgICAgIHsgcHJvcDogIm1zIiwgbGFiZWw6ICLkuI3oia/lt6XlhrXmj4/ov7AiLCBtaW5XaWR0aDogIjE2MCIsIHNob3dQb3A6IHRydWUgfSwKICAgICAgICAgIHsgcHJvcDogImZseWpDbiIsIGxhYmVsOiAi5YiG57G75L6d5o2uIiwgbWluV2lkdGg6ICIxNjAiLCBzaG93UG9wOiB0cnVlIH0sCiAgICAgICAgICB7IHByb3A6ICJscnJtYyIsIGxhYmVsOiAi5b2V5YWl5Lq6IiwgbWluV2lkdGg6ICIxMDAiIH0sCiAgICAgICAgICB7IHByb3A6ICJmeHNqIiwgbGFiZWw6ICLlj5HnjrDml7bpl7QiLCBtaW5XaWR0aDogIjEwMCIgfSwKICAgICAgICAgIHsgcHJvcDogInhjc2oiLCBsYWJlbDogIua2iOmZpOaXtumXtCIsIG1pbldpZHRoOiAiMTQwIiB9CiAgICAgICAgXQogICAgICB9LAoKICAgICAgLy/lhbblroPorr7lpIfpl67popgKICAgICAgcXRzYnd0UGFnZUluZm86IHsKICAgICAgICBwYWdlcjogewogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMDBdCiAgICAgICAgfSwKICAgICAgICBvcHRpb246IHsgY2hlY2tCb3g6IGZhbHNlLCBzZXJpYWxOdW1iZXI6IHRydWUgfSwKICAgICAgICB0YWJsZURhdGE6IFtdLAogICAgICAgIHRhYmxlSGVhZGVyOiBbCiAgICAgICAgICB7IHByb3A6ICJzYm1jIiwgbGFiZWw6ICLorr7lpIflkI3np7AiLCBtaW5XaWR0aDogIjE0MCIgfSwKICAgICAgICAgIHsgcHJvcDogIm1zIiwgbGFiZWw6ICLpl67popjmj4/ov7AiLCBtaW5XaWR0aDogIjE2MCIsIHNob3dQb3A6IHRydWUgfSwKICAgICAgICAgIHsgcHJvcDogImxycm1jIiwgbGFiZWw6ICLlvZXlhaXkuroiLCBtaW5XaWR0aDogIjEwMCIgfSwKICAgICAgICAgIHsgcHJvcDogImZ4c2oiLCBsYWJlbDogIuWPkeeOsOaXtumXtCIsIG1pbldpZHRoOiAiMTAwIiB9CiAgICAgICAgXQogICAgICB9LAoKICAgICAgLy/or5XpqozliJfooagKICAgICAgc3lQYWdlSW5mbzogewogICAgICAgIHBhZ2VyOiB7CiAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgdG90YWw6IDAsCiAgICAgICAgICBzaXplczogWzEwLCAyMCwgNTAsIDBdCiAgICAgICAgfSwKICAgICAgICB0YWJsZURhdGE6IFtdLAogICAgICAgIHRhYmxlSGVhZGVyOiBbCiAgICAgICAgICB7IGxhYmVsOiAi6K+V6aqM5LiT5LiaIiwgcHJvcDogInN5enkiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi6K+V6aqM5oCn6LSoIiwgcHJvcDogInN5eHoiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi6K+V6aqM5ZCN56ewIiwgcHJvcDogInN5bWMiLCBtaW5XaWR0aDogIjIwMCIsIHNob3dQb3A6IHRydWUgfSwKICAgICAgICAgIC8vIHsgbGFiZWw6ICLorr7lpIflnLDngrkiLCBwcm9wOiAic3lkZCIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBsYWJlbDogIuivlemqjOiuvuWkhyIsIHByb3A6ICJzYm1jIiwgbWluV2lkdGg6ICIxMDAiLCBzaG93UG9wOiB0cnVlIH0sCiAgICAgICAgICB7IGxhYmVsOiAi6K+V6aqM5qih5p2/5ZCN56ewIiwgcHJvcDogInN5bWIiLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLlpKnmsJQiLCBwcm9wOiAidHEiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi6K+V6aqM5pel5pyfIiwgcHJvcDogInN5cnEiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi6K+V6aqM5Lq65ZGYIiwgcHJvcDogInN5cnlpZCIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLmtYHnqIvnirbmgIEiLCBwcm9wOiAienRtYyIgfQogICAgICAgIF0sCiAgICAgICAgb3B0aW9uOiB7IGNoZWNrQm94OiBmYWxzZSwgc2VyaWFsTnVtYmVyOiB0cnVlIH0KICAgICAgfSwKICAgICAgLy/pmpDmgqPliJfooagKICAgICAgeWhQYWdlSW5mbzogewogICAgICAgIHBhZ2VyOiB7CiAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgdG90YWw6IDAsCiAgICAgICAgICBwYWdlUmVzaXplOiAiIiwKICAgICAgICAgIHNpemVzOiBbMTAsIDIwLCA1MCwgMTAwXQogICAgICAgIH0sCiAgICAgICAgb3B0aW9uOiB7CiAgICAgICAgICBjaGVja0JveDogZmFsc2UsCiAgICAgICAgICBzZXJpYWxOdW1iZXI6IHRydWUKICAgICAgICB9LAogICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgdGFibGVIZWFkZXI6IFsKICAgICAgICAgIC8vIHsgcHJvcDogInNzZ3MiLCBsYWJlbDogIuaJgOWxnuWFrOWPuCIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgLy8geyBwcm9wOiAic3NkeiIsIGxhYmVsOiAi5omA5bGe5L2N572uIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJzYiIsIGxhYmVsOiAi5Li76K6+5aSHIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICAvLyB7IHByb3A6ICJzYmx4IiwgbGFiZWw6ICLorr7lpIfnsbvlnosiLCBtaW5XaWR0aDogIjEwMCIgfSwKICAgICAgICAgIC8vIHtwcm9wOiAnZHlkaicsIGxhYmVsOiAn55S15Y6L562J57qnJywgbWluV2lkdGg6ICcxMDAnfSwKICAgICAgICAgIHsgcHJvcDogInNieGgiLCBsYWJlbDogIuiuvuWkh+Wei+WPtyIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBwcm9wOiAic2NjaiIsIGxhYmVsOiAi55Sf5Lqn5Y6C5a62IiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJienF4UXhkaiIsIGxhYmVsOiAi6ZqQ5oKj5oCn6LSoIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJxeG5yIiwgbGFiZWw6ICLpmpDmgqPlhoXlrrkiLCBtaW5XaWR0aDogIjEyMCIsIHNob3dQb3A6IHRydWUgfSwKICAgICAgICAgIHsgcHJvcDogImp4bGJDbiIsIGxhYmVsOiAi5piv5ZCm6Kem5Y+R54q25oCB6K+E5Lu3IiwgbWluV2lkdGg6ICI4MCIgfSwKICAgICAgICAgIHsgcHJvcDogInp0bWMiLCBsYWJlbDogIueKtuaAgSIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBwcm9wOiAiZnhycSIsIGxhYmVsOiAi5Y+R546w5pe26Ze0IiwgbWluV2lkdGg6ICIxMDAiLCBjdXN0b206IHRydWUgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgLy/mo4Dkv67liJfooagKICAgICAganhQYWdlSW5mbzogewogICAgICAgIHBhZ2VyOiB7CiAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgdG90YWw6IDAsCiAgICAgICAgICBwYWdlUmVzaXplOiAiIiwKICAgICAgICAgIHNpemVzOiBbMTAsIDIwLCA1MCwgMTAwXQogICAgICAgIH0sCiAgICAgICAgb3B0aW9uOiB7CiAgICAgICAgICBjaGVja0JveDogZmFsc2UsCiAgICAgICAgICBzZXJpYWxOdW1iZXI6IHRydWUKICAgICAgICB9LAogICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgdGFibGVIZWFkZXI6IFsKICAgICAgICAgIC8vIHsgcHJvcDogImZnc21jIiwgbGFiZWw6ICLliIblhazlj7giLCBtaW5XaWR0aDogIjE1MCIgfSwKICAgICAgICAgIC8vIHsgcHJvcDogImJkem1jIiwgbGFiZWw6ICLlhYnkvI/nlLXnq5kiLCBtaW5XaWR0aDogIjE1MCIgfSwKICAgICAgICAgIC8vIHsgcHJvcDogInNzamciLCBsYWJlbDogIuaJgOWxnumXtOmalCIsIG1pbldpZHRoOiAiMTUwIiB9LAogICAgICAgICAgeyBwcm9wOiAic2JtYyIsIGxhYmVsOiAi6K6+5aSH5ZCN56ewIiwgbWluV2lkdGg6ICIxMDAiIH0sCiAgICAgICAgICB7IHByb3A6ICJycSIsIGxhYmVsOiAi5pel5pyfIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJ4c2xiIiwgbGFiZWw6ICLkv67or5XnsbvliKsiLCBtaW5XaWR0aDogIjEwMCIgfSwKICAgICAgICAgIHsgcHJvcDogIm5yIiwgbGFiZWw6ICLlhoXlrrkiLCBtaW5XaWR0aDogIjEwMCIgfSwKICAgICAgICAgIHsgcHJvcDogImpsIiwgbGFiZWw6ICLnu5PorroiLCBtaW5XaWR0aDogIjgwIiB9LAogICAgICAgICAgeyBwcm9wOiAieHNmenIiLCBsYWJlbDogIuS/ruivlei0n+i0o+S6uiIsIG1pbldpZHRoOiAiOTAiIH0sCiAgICAgICAgICB7IHByb3A6ICJ5c2Z6ciIsIGxhYmVsOiAi6aqM5pS26LSf6LSj5Lq6IiwgbWluV2lkdGg6ICI5MCIgfSwKICAgICAgICAgIHsgcHJvcDogImpsciIsIGxhYmVsOiAi6K6w5b2V5Lq6IiwgbWluV2lkdGg6ICI5MCIgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgLy/nu6fnlLXkv53miqTliJfooagKICAgICAgamRiaFBhZ2VJbmZvOiB7CiAgICAgICAgcGFnZXI6IHsKICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgIHBhZ2VSZXNpemU6ICIiLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMDBdCiAgICAgICAgfSwKICAgICAgICBvcHRpb246IHsKICAgICAgICAgIGNoZWNrQm94OiBmYWxzZSwKICAgICAgICAgIHNlcmlhbE51bWJlcjogdHJ1ZQogICAgICAgIH0sCiAgICAgICAgdGFibGVEYXRhOiBbXSwKICAgICAgICB0YWJsZUhlYWRlcjogWwogICAgICAgICAgeyBwcm9wOiAicnEiLCBsYWJlbDogIuaXpeacnyIsIG1pbldpZHRoOiAiODAiIH0sCiAgICAgICAgICB7IHByb3A6ICJuciIsIGxhYmVsOiAi5YaF5a65IiwgbWluV2lkdGg6ICIxODAiIH0sCiAgICAgICAgICB7IHByb3A6ICJqbCIsIGxhYmVsOiAi57uT6K66IiwgbWluV2lkdGg6ICI4MCIgfSwKICAgICAgICAgIHsgcHJvcDogInN5Z3pmenIiLCBsYWJlbDogIuivlemqjOW3peS9nOi0n+i0o+S6uiIsIG1pbldpZHRoOiAiMTEwIiB9LAogICAgICAgICAgeyBwcm9wOiAieXNmenIiLCBsYWJlbDogIumqjOaUtui0n+i0o+S6uiIsIG1pbldpZHRoOiAiMTAwIiB9LAogICAgICAgICAgeyBwcm9wOiAiamxyIiwgbGFiZWw6ICLorrDlvZXkuroiLCBtaW5XaWR0aDogIjEwMCIgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgLy/ot7Ppl7jorrDlvZXliJfooagKICAgICAgdHpqbFBhZ2VJbmZvOiB7CiAgICAgICAgcGFnZXI6IHsKICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgIHBhZ2VSZXNpemU6ICIiLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMDBdCiAgICAgICAgfSwKICAgICAgICBvcHRpb246IHsKICAgICAgICAgIGNoZWNrQm94OiBmYWxzZSwKICAgICAgICAgIHNlcmlhbE51bWJlcjogdHJ1ZQogICAgICAgIH0sCiAgICAgICAgdGFibGVEYXRhOiBbXSwKICAgICAgICB0YWJsZUhlYWRlcjogWwogICAgICAgICAgeyBwcm9wOiAicnFzaiIsIGxhYmVsOiAi5pel5pyf5pe26Ze0IiwgbWluV2lkdGg6ICIxNjAiIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIHByb3A6ICJiaGR6cWsiLAogICAgICAgICAgICBsYWJlbDogIuS/neaKpOWKqOS9nOaDheWGtSIsCiAgICAgICAgICAgIG1pbldpZHRoOiAiMTgwIiwKICAgICAgICAgICAgaXNTaG93UHJvcDogdHJ1ZQogICAgICAgICAgfSwKICAgICAgICAgIHsgcHJvcDogImRscWpjcWsiLCBsYWJlbDogIuaWrei3r+WZqOajgOafpeaDheWGtSIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBwcm9wOiAiZ3pkbCIsIGxhYmVsOiAi5pWF6Zqc55S15rWBIiwgbWluV2lkdGg6ICIxMDAiIH0sCiAgICAgICAgICB7IHByb3A6ICJnenR6Y3MiLCBsYWJlbDogIuaVhemanOi3s+mXuOasoeaVsCIsIG1pbldpZHRoOiAiMTAwIiB9LAogICAgICAgICAgeyBwcm9wOiAiamx0empsIiwgbGFiZWw6ICLntK/orqHot7Ppl7jmrKHmlbAiLCBtaW5XaWR0aDogIjEwMCIgfSwKICAgICAgICAgIHsgcHJvcDogInpoZHhycSIsIGxhYmVsOiAi5pyA5ZCO5aSn5L+u5pel5pyfIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJqbHIiLCBsYWJlbDogIuiusOW9leS6uiIsIG1pbldpZHRoOiAiMTIwIiB9CiAgICAgICAgXQogICAgICB9LAogICAgICB5aGxvYWRpbmc6IGZhbHNlLAogICAgICBzeWxvYWRpbmc6IGZhbHNlLAogICAgICBibGdrbG9hZGluZzogZmFsc2UsCiAgICAgIHF0c2J3dGxvYWRpbmc6IGZhbHNlLAogICAgICBqeGxvYWRpbmc6IGZhbHNlLAogICAgICBqZGJoTG9hZGluZzogZmFsc2UsCiAgICAgIHR6amxMb2FkaW5nOiBmYWxzZSwKICAgICAgaXNTZWxlY3RpbmdGcm9tRHJvcGRvd246IGZhbHNlLCAvLyDmt7vliqDmoIforrDlj5jph48KICAgIH07CiAgfSwKICB3YXRjaDogewogICAgLy/nm5HlkKznrZvpgInmoYblgLzlj5HnlJ/lj5jljJbov5vogIznrZvpgInmoJHnu5PmnoQKICAgIGZpbHRlclRleHQodmFsKSB7CiAgICAgIHRoaXMuJHJlZnMudHJlZS5maWx0ZXIodmFsKTsKICAgIH0KICB9LAogIGFzeW5jIGNyZWF0ZWQoKSB7CiAgICAvL+iOt+WPlue7hOe7h+e7k+aehOS4i+aLieaVsOaNrgogICAgdGhpcy5nZXRGZ3NPcHRpb25zKCk7CiAgICAvLyB0aGlzLmdldEpkek9wdGlvbnMoKTsKICAgIC8v6I635Y+W5YWJ5LyP55S156uZ5LiL5ouJ5qGG5pWw5o2uCiAgICB0aGlzLmdldEJkekRhdGFMaXN0U2VsZWN0ZWQoKTsKICAgIC8v6I635Y+W6YCJ5oup5qGG5pWw5o2uCiAgICB0aGlzLmdldFNlbGVjdERhdGFJbmZvKCk7CiAgICAvL+iOt+WPluaWsOeahOiuvuWkh+aLk+aJkeagkQogICAgYXdhaXQgdGhpcy5nZXROZXdUcmVlSW5mbygpOwogICAgdGhpcy5pc1Nob3cxID0gdHJ1ZTsKICAgIC8v5Yid5aeL5YyW5Yqg6L295pe25Yqg6L295omA5pyJ5YWJ5LyP55S156uZ5L+h5oGvCiAgICBhd2FpdCB0aGlzLmdldERhdGEoKTsKICAgIGF3YWl0IHRoaXMuZ2V0SmdseExpc3QoKTsKICAgIC8v5Yid5aeL5YyW5pe25Yqg6L296aG16Z2i5YaF5a65CiAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8gPSB7IC4uLnRoaXMudGFibGVBbmRQYWdlSW5mbzEgfTsKICB9LAogIG1vdW50ZWQoKSB7CiAgICAvL+iOt+WPlnRva2VuCiAgICB0aGlzLmhlYWRlci50b2tlbiA9IGdldFRva2VuKCk7CiAgICB0aGlzLmN1cnJVc2VyID0gdGhpcy4kc3RvcmUuZ2V0dGVycy5uYW1lOwogIH0sCiAgbWV0aG9kczogewogICAgLy/miYDlsZ7lhazlj7hjaGFuZ2Xkuovku7YKICAgIGhhbmRsZUZnc0NoYW5nZShmZ3NWYWx1ZSkgewogICAgICAvL+a4heepuuS5i+WJjeW+l+mAieS4reWAvAogICAgICB0aGlzLnd6RGF0YUxpc3RPcHRpb25zID0gW107CiAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm1Db3B5LCAiYmR6IiwgIiIpOwogICAgICAvL+iOt+WPluWFieS8j+eUteermeaWueazlQogICAgICBnZXRCZHpEYXRhTGlzdFNlbGVjdGVkKHsgc3Nkd2JtOiBmZ3NWYWx1ZSB9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy53ekRhdGFMaXN0T3B0aW9ucyA9IHJlcy5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICBhc3luYyBnZXRTYkRhdGFMaXN0R3JvdXAodmFsKSB7CiAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm1Db3B5LCAic3NqZyIsICIiKTsKICAgICAgbGV0IHJlcyA9IGF3YWl0IGdldEpnRGF0YUxpc3RTZWxlY3RlZCh7IHNzYmR6OiB2YWwgKyAiIiB9KTsKICAgICAgaWYgKHJlcy5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICB0aGlzLnNiRGF0YUxpc3QgPSByZXMuZGF0YTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgIHR5cGU6ICJlcnJvciIsCiAgICAgICAgICBtZXNzYWdlOiAi6Ze06ZqU5pWw5o2u6I635Y+W5aSx6LSlISIKICAgICAgICB9KTsKICAgICAgfQogICAgfSwKICAgIHhzQ2hhbmdlRnVuYyh2YWwpIHsKICAgICAgaWYgKHZhbCA9PT0gIuS4ieebuCIpIHsKICAgICAgICB0aGlzLiRzZXQodGhpcy5zYnh4Rm9ybSwgInhiIiwgIkFCQyIpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJHNldCh0aGlzLnNieHhGb3JtLCAieGIiLCAiIik7CiAgICAgIH0KICAgIH0sCiAgICAvKioKICAgICAqIOiOt+WPluWfuuWcsOermeS4i+aLieaVsOaNrgogICAgICovCiAgICBnZXRKZHpPcHRpb25zKCkgewogICAgICBnZXRTZWxlY3RPcHRpb25zQnlPcmdUeXBlKEpTT04uc3RyaW5naWZ5KCIwNyIpKS50aGVuKHJlcyA9PiB7CiAgICAgICAgcmVzLmRhdGEuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIGl0ZW0udmFsdWUgPSBpdGVtLnZhbHVlLnRvU3RyaW5nKCk7CiAgICAgICAgfSk7CiAgICAgICAgdGhpcy5zc2pkekxpc3QgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLy/mn6Xor6Lmo4Dkv67mlbDmja4KICAgIGdldEp4TGlzdChwYXJhbXMpIHsKICAgICAgdGhpcy5qeGxvYWRpbmcgPSB0cnVlOwogICAgICBjb25zdCBwYXJhbSA9IHsKICAgICAgICAuLi57IHBhZ2VTaXplOiAxMCwgcGFnZU51bTogMSB9LAogICAgICAgIC4uLnBhcmFtcywKICAgICAgICAuLi57IHNibWM6IHRoaXMuc2JtYywgYmR6OiB0aGlzLnNzYmR6IH0sCiAgICAgICAgLi4ueyBteVNvcnRzOiBbeyBwcm9wOiAicnEiLCBhc2M6IGZhbHNlIH1dIH0KICAgICAgfTsKICAgICAgZ2V0TGlzdEZvdXIocGFyYW0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmp4UGFnZUluZm8udGFibGVEYXRhID0gcmVzLmRhdGEucmVjb3JkczsKICAgICAgICB0aGlzLmp4UGFnZUluZm8ucGFnZXIudG90YWwgPSByZXMuZGF0YS50b3RhbDsKICAgICAgICB0aGlzLmp4bG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICBnZXRKZGJoTGlzdChwYXJhbXMpIHsKICAgICAgdGhpcy5qZGJoTG9hZGluZyA9IHRydWU7CiAgICAgIGNvbnN0IHBhcmFtID0gewogICAgICAgIC4uLnsgcGFnZVNpemU6IDEwLCBwYWdlTnVtOiAxIH0sCiAgICAgICAgLi4ucGFyYW1zLAogICAgICAgIC4uLnsgc2JtYzogdGhpcy5zYm1jLCBiZHo6IHRoaXMuc3NiZHogfSwKICAgICAgICAuLi57IG15U29ydHM6IFt7IHByb3A6ICJycSIsIGFzYzogZmFsc2UgfV0gfQogICAgICB9OwogICAgICBnZXRMaXN0U2Vjb25kKHBhcmFtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5qZGJoUGFnZUluZm8udGFibGVEYXRhID0gcmVzLmRhdGEucmVjb3JkczsKICAgICAgICB0aGlzLmpkYmhQYWdlSW5mby5wYWdlci50b3RhbCA9IHJlcy5kYXRhLnRvdGFsOwogICAgICAgIHRoaXMuamRiaExvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgZ2V0VHpqbExpc3QocGFyYW1zKSB7CiAgICAgIHRoaXMudHpqbExvYWRpbmcgPSB0cnVlOwogICAgICBjb25zdCBwYXJhbSA9IHsKICAgICAgICAuLi57IHBhZ2VTaXplOiAxMCwgcGFnZU51bTogMSB9LAogICAgICAgIC4uLnBhcmFtcywKICAgICAgICAuLi57IGRscWJoOiB0aGlzLnNibWMsIGJkejogdGhpcy5zc2JkeiB9LAogICAgICAgIC4uLnsgbXlTb3J0czogW3sgcHJvcDogInJxc2oiLCBhc2M6IGZhbHNlIH1dIH0KICAgICAgfTsKICAgICAgZ2V0TGlzdFNldmVuKHBhcmFtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy50empsUGFnZUluZm8udGFibGVEYXRhID0gcmVzLmRhdGEucmVjb3JkczsKICAgICAgICB0aGlzLnR6amxQYWdlSW5mby5wYWdlci50b3RhbCA9IHJlcy5kYXRhLnRvdGFsOwogICAgICAgIHRoaXMudHpqbExvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLy/mn6Xor6LkuI3oia/lt6XlhrXmlbDmja4KICAgIGdldEJsZ2tMaXN0KHBhcmFtcykgewogICAgICB0aGlzLmJsZ2tsb2FkaW5nID0gdHJ1ZTsKICAgICAgY29uc3QgcGFyYW0gPSB7CiAgICAgICAgLi4ueyBwYWdlU2l6ZTogMTAsIHBhZ2VOdW06IDEgfSwKICAgICAgICAuLi5wYXJhbXMsCiAgICAgICAgLi4ueyBzYmlkOiB0aGlzLmJkc2JpZCB9CiAgICAgIH07CiAgICAgIGdldERhdGEocGFyYW0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmJsZ2tQYWdlSW5mby50YWJsZURhdGEgPSByZXMuZGF0YS5yZWNvcmRzOwogICAgICAgIHRoaXMuYmxna1BhZ2VJbmZvLnBhZ2VyLnRvdGFsID0gcmVzLmRhdGEudG90YWw7CiAgICAgICAgdGhpcy5ibGdrbG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCgogICAgLy/lhbblroPorr7lpIflvZXlhaXpl67popgKICAgIGdldHF0c2J3dExpc3QocGFyYW1zKSB7CiAgICAgIHRoaXMucXRzYnd0bG9hZGluZyA9IHRydWU7CiAgICAgIGNvbnN0IHBhcmFtID0gewogICAgICAgIC4uLnsgcGFnZVNpemU6IDEwLCBwYWdlTnVtOiAxIH0sCiAgICAgICAgLi4ucGFyYW1zLAogICAgICAgIC4uLnsgc2JpZDogdGhpcy5iZHNiaWQgfQogICAgICB9OwogICAgICBnZXRRdHd0bHJEYXRhKHBhcmFtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5xdHNid3RQYWdlSW5mby50YWJsZURhdGEgPSByZXMuZGF0YS5yZWNvcmRzOwogICAgICAgIHRoaXMucXRzYnd0UGFnZUluZm8ucGFnZXIudG90YWwgPSByZXMuZGF0YS50b3RhbDsKICAgICAgICB0aGlzLnF0c2J3dGxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAoKICAgIC8v6K+V6aqM5pWw5o2uCiAgICBnZXRTeUxpc3QocGFyYW1zKSB7CiAgICAgIHRoaXMuc3lsb2FkaW5nID0gdHJ1ZTsKICAgICAgY29uc3QgcGFyYW0gPSB7CiAgICAgICAgLi4ueyBwYWdlU2l6ZTogMTAsIHBhZ2VOdW06IDEgfSwKICAgICAgICAuLi5wYXJhbXMsCiAgICAgICAgLi4ueyBzeXNiaWQ6IHRoaXMuYmRzYmlkIH0KICAgICAgfTsKICAgICAgZ2V0U3liZ2psRGF0YUJ5UGFnZShwYXJhbSkudGhlbihyZXMgPT4gewogICAgICAgIGlmIChyZXMuY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICB0aGlzLnN5UGFnZUluZm8udGFibGVEYXRhID0gcmVzLmRhdGEucmVjb3JkczsKICAgICAgICAgIHRoaXMuc3lQYWdlSW5mby5wYWdlci50b3RhbCA9IHJlcy5kYXRhLnRvdGFsOwogICAgICAgIH0KICAgICAgICB0aGlzLnN5bG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvL+makOaCo+aVsOaNrgogICAgZ2V0WWhMaXN0KHBhcmFtcykgewogICAgICB0aGlzLnlobG9hZGluZyA9IHRydWU7CiAgICAgIGNvbnN0IHBhcmFtID0gewogICAgICAgIC4uLnsgcGFnZVNpemU6IDEwLCBwYWdlTnVtOiAxIH0sCiAgICAgICAgLi4ucGFyYW1zLAogICAgICAgIC4uLnsgc2JpZDogdGhpcy5iZHNiaWQgfQogICAgICB9OwogICAgICBnZXRMaXN0Rmlyc3QocGFyYW0pLnRoZW4ocmVzID0+IHsKICAgICAgICBpZiAocmVzLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgdGhpcy55aFBhZ2VJbmZvLnRhYmxlRGF0YSA9IHJlcy5kYXRhLnJlY29yZHM7CiAgICAgICAgICB0aGlzLnloUGFnZUluZm8ucGFnZXIudG90YWwgPSByZXMuZGF0YS50b3RhbDsKICAgICAgICB9CiAgICAgICAgdGhpcy55aGxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLy/ojrflj5bpl7TpmpTnsbvlnovlrZflhbgKICAgIGdldEpnbHhMaXN0KCkgewogICAgICBnZXREaWN0VHlwZURhdGEoImR3enlfamdseCIpLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmpnbHhPcHRpb25zRGF0YUxpc3QgPSByZXMuZGF0YTsKICAgICAgICByZXMuZGF0YS5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgaWYgKCF0aGlzLmpnZGxNYXAuaGFzKGl0ZW0udmFsdWUpKSB7CiAgICAgICAgICAgIHRoaXMuamdkbE1hcC5zZXQoaXRlbS52YWx1ZSwgaXRlbS5yZW1hcmspOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICBnZXRTZWxlY3REYXRhSW5mbygpIHsKICAgICAgLy8xMTAg5LiN5bim5a2X5q+NCiAgICAgIGdldERpY3RUeXBlRGF0YSgiZGdfZHlkaiIpLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLlZvbHRhZ2VMZXZlbFNlbGVjdGVkTGlzdCA9IHJlcy5kYXRhOwogICAgICB9KTsKICAgICAgLy/pl7TpmpTnibnmrornrYnnuqcKICAgICAgZ2V0RGljdFR5cGVEYXRhKCJqZ3R6X2dqRHlkaiIpLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmpnRHlkT3B0aW9ucyA9IHJlcy5kYXRhOwogICAgICAgIHRoaXMuZmlsdGVySW5mbzIuZmllbGRMaXN0Lm1hcChpdGVtID0+IHsKICAgICAgICAgIGlmIChpdGVtLnZhbHVlID09ICJkeWRqIikgewogICAgICAgICAgICByZXR1cm4gKGl0ZW0ub3B0aW9ucyA9IHRoaXMuamdEeWRPcHRpb25zKTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSk7CiAgICAgIC8vIDExMGtWIOW4puWtl+avjQogICAgICBnZXREaWN0VHlwZURhdGEoImRnX2R5ZGoiKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5keWRqT3B0aW9uc1dpdGhTdHJpbmcgPSByZXMuZGF0YTsKICAgICAgICB0aGlzLmZpbHRlckluZm8xLmZpZWxkTGlzdC5tYXAoaXRlbSA9PiB7CiAgICAgICAgICBpZiAoaXRlbS52YWx1ZSA9PSAiZHlkaiIpIHsKICAgICAgICAgICAgcmV0dXJuIChpdGVtLm9wdGlvbnMgPSB0aGlzLmR5ZGpPcHRpb25zV2l0aFN0cmluZyk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CgogICAgICAgIHRoaXMuZmlsdGVySW5mbzMuZmllbGRMaXN0Lm1hcChpdGVtID0+IHsKICAgICAgICAgIGlmIChpdGVtLnZhbHVlID09ICJkeWRqTmFtZSIpIHsKICAgICAgICAgICAgcmV0dXJuIChpdGVtLm9wdGlvbnMgPSB0aGlzLmR5ZGpPcHRpb25zV2l0aFN0cmluZyk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgICAvL+iuvuWkh+eKtuaAgQogICAgICBnZXREaWN0VHlwZURhdGEoImpndHpfc2J6dCIpLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLnNienRPcHRpb25zRGF0YUxpc3QgPSByZXMuZGF0YTsKICAgICAgICB0aGlzLmZpbHRlckluZm8xLmZpZWxkTGlzdC5tYXAoaXRlbSA9PiB7CiAgICAgICAgICBpZiAoaXRlbS52YWx1ZSA9PSAic2J6dCIpIHsKICAgICAgICAgICAgcmV0dXJuIChpdGVtLm9wdGlvbnMgPSB0aGlzLnNienRPcHRpb25zRGF0YUxpc3QpOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICAgIHRoaXMuZmlsdGVySW5mbzMuZmllbGRMaXN0Lm1hcChpdGVtID0+IHsKICAgICAgICAgIGlmIChpdGVtLnZhbHVlID09ICJzYnp0IikgewogICAgICAgICAgICByZXR1cm4gKGl0ZW0ub3B0aW9ucyA9IHRoaXMuc2J6dE9wdGlvbnNEYXRhTGlzdCk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgICAvL+WuieijheS9jee9rgogICAgICBnZXREaWN0VHlwZURhdGEoImpndHpfYXp3eiIpLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLnBsYWNlT3B0aW9ucyA9IHJlcy5kYXRhOwogICAgICB9KTsKICAgICAgLy/nm7jliKsKICAgICAgZ2V0RGljdFR5cGVEYXRhKCJqZ3R6X3hiIikudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMueGJPcHRpb25zID0gcmVzLmRhdGE7CiAgICAgIH0pOwogICAgICAvL+ebuOaVsAogICAgICBnZXREaWN0VHlwZURhdGEoImpndHpfeHMiKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy54c09wdGlvbnMgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5paw5aKe5L+h5oGv5omA5bGe55S156uZ6KaB6Ieq5Yqo5bim5YWlCiAgICBmaWxsQmR6KCkgewogICAgICB0aGlzLiRzZXQodGhpcy5qZ3h4Rm9ybSwgInNzYmR6IiwgdGhpcy5jdXJyZW50QmR6KTsKICAgIH0sCiAgICBhc3luYyBkZWxldGVGaWxlQnlJZChpZCkgewogICAgICBsZXQgeyBjb2RlIH0gPSBhd2FpdCBkZWxldGVCeUlkKGlkKTsKICAgICAgaWYgKGNvZGUgPT09ICIwMDAwIikgewogICAgICAgIGF3YWl0IHRoaXMuZ2V0RmlsZUxpc3QoKTsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgIG1lc3NhZ2U6ICLmlofku7bliKDpmaTmiJDlip8hIgogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgLy/moJHnm5HlkKzkuovku7YKICAgIGZpbHRlck5vZGUodmFsdWUsIGRhdGEpIHsKICAgICAgaWYgKCF2YWx1ZSkgcmV0dXJuIHRydWU7CiAgICAgIHJldHVybiBkYXRhLmxhYmVsLmluZGV4T2YodmFsdWUpICE9PSAtMTsKICAgIH0sCiAgICAvL+iOt+WPluaWsOeahOiuvuWkh+aLk+aJkeagkQogICAgZ2V0TmV3VHJlZUluZm8oKSB7CiAgICAgIGdldFVzZXJzKHsgcGVyc29uR3JvdXBJZDogNzYsIGRlcHRJZDogMCwgZGVwdE5hbWU6ICIiIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICBsZXQgZGVwdElkID0gdGhpcy4kc3RvcmUuZ2V0dGVycy5kZXB0SWQudG9TdHJpbmcoKTsKICAgICAgICByZXMuZGF0YS5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgaWYgKGl0ZW0udXNlck5hbWUgPT09IHRoaXMuY3VyclVzZXIpIHsKICAgICAgICAgICAgLy/lpoLmnpzkurrlkZjnu4Tph4zpnaLmnInpnIDopoHmjpLpmaTnmoTkurrvvIzliJnkuI3pnIDopoHnlKhkZXB0SWTov5vooYzov4fmu6QKICAgICAgICAgICAgZGVwdElkID0gIiI7CiAgICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgICBpZiAodGhpcy5mZ3NBcnIuaW5jbHVkZXMoZGVwdElkKSkgewogICAgICAgICAgdGhpcy50cmVlRm9ybS5zc2R3Ym0gPSBkZXB0SWQ7CiAgICAgICAgfQogICAgICAgIGdldE5ld1RyZWVJbmZvKHRoaXMudHJlZUZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgIHRoaXMudHJlZU9wdGlvbnMgPSByZXMuZGF0YTsKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgLyoqCiAgICAgKiDojrflj5bliIblhazlj7jkuIvmi4nmlbDmja4KICAgICAqLwogICAgZ2V0RmdzT3B0aW9ucygpIHsKICAgICAgZ2V0RmdzT3B0aW9ucyh7fSkudGhlbihyZXMgPT4gewogICAgICAgIHJlcy5kYXRhLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICBpdGVtLnZhbHVlID0gaXRlbS52YWx1ZS50b1N0cmluZygpOwogICAgICAgICAgdGhpcy5mZ3NBcnIucHVzaChpdGVtLnZhbHVlLnRvU3RyaW5nKCkpOwogICAgICAgIH0pOwogICAgICAgIHRoaXMuT3JnYW5pemF0aW9uU2VsZWN0ZWRMaXN0ID0gcmVzLmRhdGE7CiAgICAgICAgdGhpcy5maWx0ZXJJbmZvMS5maWVsZExpc3QubWFwKGl0ZW0gPT4gewogICAgICAgICAgaWYgKGl0ZW0udmFsdWUgPT0gInNzZHdibSIpIHsKICAgICAgICAgICAgcmV0dXJuIChpdGVtLm9wdGlvbnMgPSB0aGlzLk9yZ2FuaXphdGlvblNlbGVjdGVkTGlzdCk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIC8v5pen5qCR5b2i5pWw5o2u6I635Y+WCiAgICBnZXRUcmVlSW5mb0xpc3QoKSB7CiAgICAgIGdldFRyZWVJbmZvKCkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMudHJlZU9wdGlvbnMgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLy/liJfooajmn6Xor6IKICAgIGFzeW5jIGdldERhdGEocGFyYW0pIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgdGhpcy5wYXJhbXMgPSBwYXJhbTsKICAgICAgYXdhaXQgZ2V0U2JseERhdGFMaXN0U2VsZWN0ZWQoe30pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLnNibHhPcHRpb25zRGF0YVNlbGVjdGVkID0gcmVzLmRhdGE7CiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgICAvL+WIpOaWree/u+mhteaYr+aJp+ihjOeahOWTquS4quihqOagvOeahOaVsOaNrgogICAgICBpZiAodGhpcy5iZHpkYXRhU2hvdykgewogICAgICAgIC8v5Yid5aeL6L+b5p2l6K+35rGC5YWJ5LyP55S156uZ5Y+w6LSmCiAgICAgICAgYXdhaXQgdGhpcy5nZXRiZHpEYXRhKHBhcmFtKTsKICAgICAgfQogICAgICBpZiAodGhpcy5qZ2RhdGFTaG93KSB7CiAgICAgICAgYXdhaXQgdGhpcy5nZXRKZ0RhdGEocGFyYW0pOwogICAgICB9CiAgICAgIGlmICh0aGlzLnpuc2JkYXRhU2hvdykgewogICAgICAgIGF3YWl0IHRoaXMuZ2V0Wm5zYkRhdGEocGFyYW0pOwogICAgICB9CiAgICB9LAogICAgLyoqCiAgICAgKiDooajmoLzlpJrpgInmoYYKICAgICAqLwogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICAvLyB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLm9iaklkKTsKICAgICAgLy8gdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOwogICAgICB0aGlzLnNpbmdsZUNsaWNrRGF0YSA9CiAgICAgICAgc2VsZWN0aW9uLmxlbmd0aCA+IDAgPyB7IC4uLnNlbGVjdGlvblswXSB9IDogdW5kZWZpbmVkOwogICAgICAvLyB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7CiAgICB9LAoKICAgIC8v6Ze06ZqU5re75Yqg5oyJ6ZKuCiAgICBqZ0FkZGpnQnV0dG9uKCkgewogICAgICB0aGlzLmpnU2hvdyA9IGZhbHNlOwogICAgICB0aGlzLmpnRGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgfSwKICAgIGFkZEpnKCkgewogICAgICB0aGlzLiRyZWZzWyJqZ3h4Rm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGxldCBqZ2x4ID0gdGhpcy5qZ3h4Rm9ybS5qZ2x4OwogICAgICAgICAgaWYgKGpnbHgpIHsKICAgICAgICAgICAgLy/kv53lrZjml7borr7nva7pl7TpmpTlpKfnsbsKICAgICAgICAgICAgdGhpcy5qZ3h4Rm9ybS5qZ2RsID0gdGhpcy5qZ2RsTWFwLmdldChqZ2x4KTsKICAgICAgICAgIH0KICAgICAgICAgIGFkZEpnKHRoaXMuamd4eEZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09ICIwMDAwIikgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5pON5L2c5oiQ5Yqf77yBIik7CiAgICAgICAgICAgICAgdGhpcy5qZ0RpYWxvZ0Zvcm1WaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICAgICAgLy8gdGhpcy50YWJsZUFuZFBhZ2VJbmZvMS5wYWdlci5wYWdlUmVzaXplID0gIlkiOwogICAgICAgICAgICAgIHRoaXMuZ2V0SmdEYXRhKCk7CiAgICAgICAgICAgICAgLy/ojrflj5bmlrDnmoTorr7lpIfmi5PmiZHmoJEKICAgICAgICAgICAgICB0aGlzLmdldE5ld1RyZWVJbmZvKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBnZXRKZ0xpc3RJbmZvKCkgewogICAgICBnZXRKZ0luZm9MaXN0KHRoaXMuamdRdWVyeVBhcmFtcykudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mbzEudGFibGVEYXRhID0gcmVzLmRhdGEucmVjb3JkczsKICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8xLnBhZ2VyLnRvdGFsID0gcmVzLmRhdGEudG90YWw7CiAgICAgICAgLy8gdGhpcy50YWJsZUFuZFBhZ2VJbmZvID0gey4uLnRoaXMudGFibGVBbmRQYWdlSW5mbzJ9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKgogICAgICog5Yig6Zmk6Ze06ZqUCiAgICAgKi8KICAgIHJlbW92ZUFsbChyb3cpIHsKICAgICAgaWYgKHRoaXMuYmR6ZGF0YVNob3cpIHsKICAgICAgICB0aGlzLmRlbGV0ZUJkeihyb3cpOwogICAgICB9CiAgICAgIGlmICh0aGlzLmpnZGF0YVNob3cpIHsKICAgICAgICB0aGlzLnJlbW92ZUFzc2V0KHJvdyk7CiAgICAgIH0KICAgICAgaWYgKHRoaXMuem5zYmRhdGFTaG93KSB7CiAgICAgICAgdGhpcy5kZWxldGVKZyhyb3cpOwogICAgICB9CiAgICB9LAogICAgcmVtb3ZlQXNzZXQocm93KSB7CiAgICAgIHRoaXMuZm9ybSA9IHJvdzsKCiAgICAgIHRoaXMuJGNvbmZpcm0oIuatpOaTjeS9nOWwhuawuOS5heWIoOmZpOivpeaVsOaNriwg5piv5ZCm57un57utPyIsICLmj5DnpLoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KQogICAgICAgIC50aGVuKCgpID0+IHsKICAgICAgICAgIHJlbW92ZUFzc2V0KFt0aGlzLmZvcm0ub2JqSWRdKS50aGVuKCh7IGNvZGUgfSkgPT4gewogICAgICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5Yig6Zmk5oiQ5YqfISIKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICB0aGlzLmdldEpnRGF0YSgpOwogICAgICAgICAgICAgIC8v6I635Y+W5paw55qE6K6+5aSH5ouT5omR5qCRCiAgICAgICAgICAgICAgdGhpcy5nZXROZXdUcmVlSW5mbygpOwogICAgICAgICAgICAgIC8vIHRoaXMudGFibGVBbmRQYWdlSW5mbzMucGFnZXIucGFnZVJlc2l6ZSA9ICJZIjsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICJlcnJvciIsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5Yig6Zmk5aSx6LSlISIKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfSkKICAgICAgICAuY2F0Y2goKCkgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICJpbmZvIiwKICAgICAgICAgICAgbWVzc2FnZTogIuW3suWPlua2iOWIoOmZpCIKICAgICAgICAgIH0pOwogICAgICAgIH0pOwogICAgfSwKICAgIGRlbGV0ZUJkeihyb3cpIHsKICAgICAgdGhpcy5mb3JtID0gcm93OwogICAgICB0aGlzLiRjb25maXJtKCLmraTmk43kvZzlsIbmsLjkuYXliKDpmaTor6XmlbDmja4sIOaYr+WQpue7p+e7rT8iLCAi5o+Q56S6IiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgcmVtb3ZlQmR6KFt0aGlzLmZvcm0ub2JqSWRdKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLAogICAgICAgICAgICBtZXNzYWdlOiAi5Yig6Zmk5oiQ5YqfISIKICAgICAgICAgIH0pOwogICAgICAgICAgLy8gdGhpcy50YWJsZUFuZFBhZ2VJbmZvMS5wYWdlci5wYWdlUmVzaXplID0gIlkiOwogICAgICAgICAgdGhpcy5nZXRiZHpEYXRhKCk7CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIGRlbGV0ZUpnKHJvdykgewogICAgICB0aGlzLmZvcm0gPSByb3c7CgogICAgICB0aGlzLiRjb25maXJtKCLmraTmk43kvZzlsIbmsLjkuYXliKDpmaTor6XmlbDmja4sIOaYr+WQpue7p+e7rT8iLCAi5o+Q56S6IiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgcmVtb3ZlSmcoW3RoaXMuZm9ybS5vYmpJZF0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsCiAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTmiJDlip8hIgogICAgICAgICAgfSk7CiAgICAgICAgICAvLyB0aGlzLnRhYmxlQW5kUGFnZUluZm8yLnBhZ2VyLnBhZ2VSZXNpemUgPSAiWSI7CiAgICAgICAgICB0aGlzLmdldFpuc2JEYXRhKCk7CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIGFzeW5jIHVwZGF0ZUpnKHJvdykgewogICAgICB0aGlzLmpnRGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgICB0aGlzLmpneHhGb3JtID0geyAuLi5yb3cgfTsKICAgICAgdGhpcy5qZ1Nob3cgPSBmYWxzZTsKICAgIH0sCgogICAgYXN5bmMgamdEZXRhaWxzKHJvdykgewogICAgICB0aGlzLmpnRGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgICB0aGlzLmpneHhGb3JtID0geyAuLi5yb3cgfTsKICAgICAgdGhpcy5qZ1Nob3cgPSB0cnVlOwogICAgfSwKCiAgICAvL+avj+mhteWxleekuuaVsOmHj+eCueWHu+S6i+S7tgogICAgaGFuZGxlU2l6ZUNoYW5nZSgpIHt9LAogICAgLy/pobXnoIHmlLnlj5jkuovku7YKICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2UoKSB7fSwKICAgIC8v5qCR54K55Ye75LqL5Lu2CiAgICBoYW5kbGVOb2RlQ2xpY2soZGF0YSwgZSkgewogICAgICAvL+agueebruW9leWFieS8j+eUteermeWIl+ihqAogICAgICBpZiAoZGF0YS5pZGVudGlmaWVyID09ICIwIikgewogICAgICAgIC8v54K55Ye75qC56IqC54K577yM6I635Y+W5YWJ5LyP55S156uZ5pWw5o2uCiAgICAgICAgLy/pl7TpmpQKICAgICAgICB0aGlzLmlzU2hvdzIgPSBmYWxzZTsKICAgICAgICAvL+mHjee9rnBhZ2VOdW0KICAgICAgICB0aGlzLmJkenF1ZXJ5UGFyYW1zID0gewogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHBhZ2VTaXplOiAxMAogICAgICAgIH07CiAgICAgICAgdGhpcy4kcmVmcy5iZHpUYWJsZS5jdXJyZW50UGFnZSA9IDE7CiAgICAgICAgLy/ojrflj5bliJfooajmlbDmja4KICAgICAgICB0aGlzLmdldGJkekRhdGEoKTsKICAgICAgICB0aGlzLmJkemRhdGFTaG93ID0gdHJ1ZTsKICAgICAgICB0aGlzLnpuc2JkYXRhU2hvdyA9IHRoaXMuamdkYXRhU2hvdyA9IGZhbHNlOwogICAgICB9CiAgICAgIC8v5LqM57qn55uu5b2V5YWJ5LyP55S156uZ5ZCN56ewCiAgICAgIGVsc2UgaWYgKGRhdGEuaWRlbnRpZmllciA9PSAiMSIpIHsKICAgICAgICAvL+eCueWHu+WFieS8j+eUteerme+8jOiOt+WPlumXtOmalOaVsOaNrgogICAgICAgIHRoaXMuY3VycmVudEJkeiA9IGRhdGEuaWQ7CiAgICAgICAgdGhpcy5zc2Jkem1jID0gZGF0YS5sYWJlbDsKICAgICAgICB0aGlzLnNzYmR6ID0gZGF0YS5pZDsKICAgICAgICAvL+mHjee9rumhteeggQogICAgICAgIHRoaXMuJHJlZnMuamdUYWJsZS5jdXJyZW50UGFnZSA9IDE7CiAgICAgICAgdGhpcy5qZ1F1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICAgIHRoaXMuamdRdWVyeVBhcmFtcy5qZ2RsID0gIiI7IC8v5riF56m66Ze06ZqU5aSn57G7CiAgICAgICAgdGhpcy5nZXRGZ3NCeUJkeklkKCk7CiAgICAgICAgdGhpcy5nZXRKZ0RhdGEoeyBzc2JkejogZGF0YS5pZCB9KTsKICAgICAgICB0aGlzLmpnZGF0YVNob3cgPSB0cnVlOwogICAgICAgIHRoaXMuem5zYmRhdGFTaG93ID0gdGhpcy5iZHpkYXRhU2hvdyA9IGZhbHNlOwogICAgICB9IC8v5LqM57qn55uu5b2V5YWJ5LyP55S156uZ5ZCN56ewCiAgICAgIGVsc2UgaWYgKGRhdGEuaWRlbnRpZmllciA9PSAiMiIpIHsKICAgICAgICAvL+eCueWHu+mXtOmalOWkp+exu++8jOi/h+a7pOmXtOmalOaVsOaNrgogICAgICAgIC8v6YeN5paw6K6+572u5omA5bGe55S156uZCiAgICAgICAgdGhpcy5jdXJyZW50QmR6ID0gZGF0YS5zc2JkejsKICAgICAgICB0aGlzLnNzYmR6ID0gZGF0YS5zc2JkejsKICAgICAgICAvL+mHjee9rumhteeggQogICAgICAgIHRoaXMuJHJlZnMuamdUYWJsZS5jdXJyZW50UGFnZSA9IDE7CiAgICAgICAgdGhpcy5qZ1F1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICAgIHRoaXMuamdRdWVyeVBhcmFtcy5qZ2RsID0gZGF0YS5pZDsKICAgICAgICB0aGlzLmpnbHhjeCA9IGRhdGEuaWQ7CiAgICAgICAgdGhpcy5nZXRGZ3NCeUJkeklkKCk7CiAgICAgICAgdGhpcy5nZXRKZ0RhdGEoeyBzc2JkejogdGhpcy5zc2JkeiwgamdkbDogZGF0YS5pZCB9KTsKICAgICAgICB0aGlzLmpnZGF0YVNob3cgPSB0cnVlOwogICAgICAgIHRoaXMuem5zYmRhdGFTaG93ID0gdGhpcy5iZHpkYXRhU2hvdyA9IGZhbHNlOwogICAgICB9IGVsc2UgaWYgKGRhdGEuaWRlbnRpZmllciA9PSAiMyIpIHsKICAgICAgICAvL+eCueWHu+mXtOmalO+8jOiOt+WPluermeWGheiuvuWkhwogICAgICAgIHRoaXMuamdseGN4ID0gZGF0YS5qZ2x4OwogICAgICAgIHRoaXMuc2J4eEZvcm0uc3NiZHogPSB0aGlzLnNzYmR6OwogICAgICAgIC8v6YeN572u6aG156CBCiAgICAgICAgdGhpcy4kcmVmcy56bnNiVGFibGUuY3VycmVudFBhZ2UgPSAxOwogICAgICAgIHRoaXMuem5zYlBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgICB0aGlzLmJkek9wdGlvbnNDaGFuZ2VDbGljaygpOwogICAgICAgIHRoaXMuc3NqZyA9IGRhdGEuaWQ7CiAgICAgICAgdGhpcy5zYnh4Rm9ybS5zc2pnID0gdGhpcy5zc2pnOwogICAgICAgIC8v6Ze06ZqUCiAgICAgICAgdGhpcy5iZHpxdWVyeVBhcmFtcy5zc2JkeiA9ICIiOwogICAgICAgIHRoaXMuYmR6cXVlcnlQYXJhbXMuc3NqZyA9IGRhdGEuaWQ7CiAgICAgICAgdGhpcy5nZXRabnNiRGF0YSgpOwogICAgICAgIHRoaXMuem5zYmRhdGFTaG93ID0gdHJ1ZTsKICAgICAgICB0aGlzLmpnZGF0YVNob3cgPSB0aGlzLmJkemRhdGFTaG93ID0gZmFsc2U7CiAgICAgIH0KICAgIH0sCiAgICBhc3luYyBnZXRGZ3NCeUJkeklkKCkgewogICAgICBsZXQgeyBkYXRhLCBjb2RlIH0gPSBhd2FpdCBnZXRGZ3NCeUJkeklkKHsgc2JkbTogdGhpcy5jdXJyZW50QmR6IH0pOwogICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgdGhpcy5zc2dzID0gZGF0YS52YWx1ZTsKICAgICAgICB0aGlzLnNieHhGb3JtLnNzZ3MgPSBkYXRhLnZhbHVlOwogICAgICB9CiAgICB9LAogICAgLy/or7fmsYLlhYnkvI/nlLXnq5nmlbDmja4KICAgIGFzeW5jIGdldGJkekRhdGEocGFyYW0pIHsKICAgICAgdGhpcy5iZHpxdWVyeVBhcmFtcyA9IHsgLi4udGhpcy5iZHpxdWVyeVBhcmFtcywgLi4ucGFyYW0gfTsKICAgICAgY29uc3QgcGFyID0geyAuLi50aGlzLmJkenF1ZXJ5UGFyYW1zLCAuLi5wYXJhbSB9OwogICAgICBpZiAodGhpcy50cmVlRm9ybS5zc2R3Ym0pIHsKICAgICAgICBwYXIuc3Nkd2JtID0gdGhpcy50cmVlRm9ybS5zc2R3Ym07CiAgICAgIH0KICAgICAgYXdhaXQgZ2V0QmR6TGlzdChwYXIpLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmJkemRhdGFTaG93ID0gdHJ1ZTsKICAgICAgICB0aGlzLmpnZGF0YVNob3cgPSB0aGlzLnpuc2JkYXRhU2hvdyA9IGZhbHNlOwogICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mbzEudGFibGVEYXRhID0gcmVzLmRhdGEucmVjb3JkczsKICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8xLnBhZ2VyLnRvdGFsID0gcmVzLmRhdGEudG90YWw7CiAgICAgIH0pOwogICAgfSwKICAgIC8v5pel5pyf5qC85byP5YyWICB5eXl5LU1NLWRkCiAgICBkYXRlRm9ybWF0dGVyKGQpIHsKICAgICAgbGV0IHllYXIgPSBkLmdldEZ1bGxZZWFyKCk7CiAgICAgIGxldCBtb250aCA9CiAgICAgICAgZC5nZXRNb250aCgpIDwgOSA/ICIwIiArIChkLmdldE1vbnRoKCkgKyAxKSA6ICIiICsgKGQuZ2V0TW9udGgoKSArIDEpOwogICAgICBsZXQgZGF5ID0gZC5nZXREYXRlKCkgPCAxMCA/ICIwIiArIGQuZ2V0RGF0ZSgpIDogIiIgKyBkLmdldERhdGUoKTsKICAgICAgcmV0dXJuIHllYXIgKyAiLSIgKyBtb250aCArICItIiArIGRheTsKICAgIH0sCiAgICAvL+ivt+axgumXtOmalOaVsOaNrgogICAgYXN5bmMgZ2V0SmdEYXRhKHBhcmFtKSB7CiAgICAgIHRoaXMuamdRdWVyeVBhcmFtcyA9IHsgLi4udGhpcy5qZ1F1ZXJ5UGFyYW1zLCAuLi5wYXJhbSB9OwogICAgICB0cnkgewogICAgICAgIGxldCB7IGRhdGEsIGNvZGUgfSA9IGF3YWl0IGdldEpnSW5mb0xpc3QodGhpcy5qZ1F1ZXJ5UGFyYW1zKTsKICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICB0aGlzLmpnZGF0YVNob3cgPSB0cnVlOwogICAgICAgICAgdGhpcy5iZHpkYXRhU2hvdyA9IHRoaXMuem5zYmRhdGFTaG93ID0gZmFsc2U7CiAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8yLnRhYmxlRGF0YSA9IGRhdGEucmVjb3JkczsKICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mbzIucGFnZXIudG90YWwgPSBkYXRhLnRvdGFsOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZSkge30KICAgIH0sCiAgICAvL+ivt+axguermeWGheiuvuWkh+aVsOaNrgogICAgYXN5bmMgZ2V0Wm5zYkRhdGEocGFyYW1zKSB7CiAgICAgIHRyeSB7CiAgICAgICAgdGhpcy56bnNiUGFyYW1zID0geyAuLi50aGlzLnpuc2JQYXJhbXMsIC4uLnBhcmFtcyB9OwogICAgICAgIGNvbnN0IHBhcmFtID0gdGhpcy56bnNiUGFyYW1zOwogICAgICAgIHBhcmFtLnNzamcgPSB0aGlzLnNzamc7CiAgICAgICAgY29uc3QgeyBkYXRhLCBjb2RlIH0gPSBhd2FpdCBnZXRCZEFzZXNldExpc3RQYWdlKHBhcmFtKTsKICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICB0aGlzLnpuc2JkYXRhU2hvdyA9IHRydWU7CiAgICAgICAgICB0aGlzLmpnZGF0YVNob3cgPSB0aGlzLmJkemRhdGFTaG93ID0gZmFsc2U7CiAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8zLnRhYmxlRGF0YSA9IGRhdGEucmVjb3JkczsKICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mbzMucGFnZXIudG90YWwgPSBkYXRhLnRvdGFsOwogICAgICAgICAgLy8gdGhpcy5zYnh4Rm9ybS5zc2dzPWRhdGEucmVjb3Jkc1swXS5kZXB0bmFtZQogICAgICAgICAgLy8gdGhpcy5zYnh4Rm9ybS5zc2Jkej1kYXRhLnJlY29yZHNbMF0uYmR6bWMKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICBjb25zb2xlLmxvZyhlKTsKICAgICAgfQogICAgfSwKICAgIC8v5YWJ5LyP55S156uZ5by55qGG5byA5aeLCiAgICAvL+WFieS8j+eUteermeS/ruaUueaMiemSrgogICAgdXBkYXRlYmR6KHJvdykgewogICAgICB0aGlzLiRuZXh0VGljayhhc3luYyBmdW5jdGlvbigpIHsKICAgICAgICB0aGlzLmNsZWFyVXBsb2FkKCk7CiAgICAgICAgdGhpcy5qYnh4Rm9ybSA9IHsgLi4ucm93IH07CiAgICAgICAgdGhpcy5qYnh4Rm9ybS5hdHRhY2htZW50ID0gW107CiAgICAgICAgYXdhaXQgdGhpcy5nZXRGaWxlTGlzdCgpOwogICAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICAgIHRoaXMudGl0bGUgPSAi5YWJ5LyP55S156uZ5Y+w6LSm5L+u5pS5IjsKICAgICAgICB0aGlzLmJkekRpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZTsKICAgICAgfSk7CiAgICB9LAogICAgY2xlYXJVcGxvYWQoKSB7CiAgICAgIGlmICh0aGlzLiRyZWZzLnVwbG9hZCkgewogICAgICAgIHRoaXMuJHJlZnMudXBsb2FkLmNsZWFyRmlsZXMoKTsKICAgICAgfQogICAgfSwKICAgIC8v5YWz6Zet5by55qGGCiAgICByZW1vdmVGb3JtKCkgewogICAgICB0aGlzLmpieHhGb3JtID0gewogICAgICAgIGF0dGFjaG1lbnQ6IFtdCiAgICAgIH07CiAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uKCkgewogICAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS5jbGVhclZhbGlkYXRlKCk7CiAgICAgIH0pOwogICAgICB0aGlzLmJkekRpYWxvZ0Zvcm1WaXNpYmxlID0gZmFsc2U7CiAgICB9LAogICAgYmVmb3JlVXBsb2FkKGZpbGUpIHsKICAgICAgY29uc3QgZmlsZVNpemUgPSBmaWxlLnNpemUgPCAxMDI0ICogMTAyNCAqIDUwOwogICAgICBpZiAoIWZpbGVTaXplKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5LiK5Lyg5paH5Lu25aSn5bCP5LiN6IO96LaF6L+HIDUwTUIhIik7CiAgICAgIH0KICAgIH0sCiAgICBhc3luYyBnZXRGaWxlTGlzdCgpIHsKICAgICAgbGV0IHsgY29kZSwgZGF0YSB9ID0gYXdhaXQgZ2V0TGlzdEJ5QnVzaW5lc3NJZCh7CiAgICAgICAgYnVzaW5lc3NJZDogdGhpcy5qYnh4Rm9ybS5vYmpJZAogICAgICB9KTsKICAgICAgaWYgKGNvZGUgPT09ICIwMDAwIikgewogICAgICAgIHRoaXMuamJ4eEZvcm0uYXR0YWNobWVudCA9IGRhdGE7CiAgICAgICAgdGhpcy5pbWdMaXN0ID0gZGF0YS5tYXAoaXRlbSA9PiB7CiAgICAgICAgICBsZXQgaXRlbTEgPSB7fTsKICAgICAgICAgIGl0ZW0xLm5hbWUgPSBpdGVtLmZpbGVOYW1lOwogICAgICAgICAgaXRlbTEudXJsID0gdGhpcy4kc3RvcmUuZ2V0dGVycy5jdXJySG9zdCArIGl0ZW0uZmlsZVVybDsKICAgICAgICAgIHJldHVybiBpdGVtMTsKICAgICAgICB9KTsKICAgICAgfQogICAgfSwKICAgIC8v5YWJ5LyP55S156uZ6K+m5oOF5pa55rOVCiAgICBiZHpEZXRhaWxzKHJvdykgewogICAgICB0aGlzLiRuZXh0VGljayhhc3luYyBmdW5jdGlvbigpIHsKICAgICAgICB0aGlzLmNsZWFyVXBsb2FkKCk7CiAgICAgICAgdGhpcy5qYnh4Rm9ybSA9IHsgLi4ucm93IH07CiAgICAgICAgbGV0IHRlbXAgPSB0aGlzLmpieHhGb3JtLnNzZHdtYzsKICAgICAgICB0aGlzLmpieHhGb3JtLnNzZHdtYyA9IHRoaXMuamJ4eEZvcm0uc3Nkd2JtOwogICAgICAgIHRoaXMuamJ4eEZvcm0uc3Nkd2JtID0gdGVtcDsKICAgICAgICB0aGlzLmpieHhGb3JtLmF0dGFjaG1lbnQgPSBbXTsKICAgICAgICBhd2FpdCB0aGlzLmdldEZpbGVMaXN0KCk7CiAgICAgICAgdGhpcy5iZHpEaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CiAgICAgICAgdGhpcy5pc0Rpc2FibGVkID0gdHJ1ZTsKICAgICAgICB0aGlzLnRpdGxlID0gIuWFieS8j+eUteermeWPsOi0puivpuaDhSI7CiAgICAgIH0pOwogICAgfSwKICAgIGFkZEJkeigpIHsKICAgICAgbGV0IHBhcmFtcyA9IHsKICAgICAgICBseDogIuWFieS8j+iuvuWkhyIsCiAgICAgICAgc3NkdzogdGhpcy5qYnh4Rm9ybS5zc2R3Ym0sCiAgICAgICAgbWM6IHRoaXMuamJ4eEZvcm0uYmR6bWMKICAgICAgfTsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGFkZEJkeih0aGlzLmpieHhGb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgICAgLy/mlrDlop7miJDlip/lkI7lj5HpgIHpgJrnn6UKICAgICAgICAgICAgICBhZGRkd3p5ZnN0eihwYXJhbXMpLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgdGhpcy51cGxvYWREYXRhLmJ1c2luZXNzSWQgPSByZXMuZGF0YS5vYmpJZDsKICAgICAgICAgICAgICB0aGlzLnN1Ym1pdFVwbG9hZCgpOwogICAgICAgICAgICAgIHRoaXMuYmR6RGlhbG9nRm9ybVZpc2libGUgPSBmYWxzZTsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKnyzpgJrnn6Xlt7LliIblj5EiKTsKICAgICAgICAgICAgICAvLyB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIucGFnZVJlc2l6ZSA9ICJZIjsKICAgICAgICAgICAgICB0aGlzLmdldE5ld1RyZWVJbmZvKCk7CiAgICAgICAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgdGhpcy5iZHpEaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAoKICAgIHN1Ym1pdFVwbG9hZCgpIHsKICAgICAgdGhpcy4kcmVmcy51cGxvYWQuc3VibWl0KCk7CiAgICB9LAogICAgLy/lhYnkvI/nlLXnq5nlvLnmoYbnu5PmnZ8KICAgIC8v5paw5aKe5oyJ6ZKuCiAgICBBZGRCdXR0b24oKSB7CiAgICAgIHRoaXMuc2hvd0Zzc3MgPSBmYWxzZTsKICAgICAgaWYgKHRoaXMuYmR6ZGF0YVNob3cpIHsKICAgICAgICB0aGlzLmNsZWFyVXBsb2FkKCk7CiAgICAgICAgdGhpcy5pbWdMaXN0ID0gW107CiAgICAgICAgdGhpcy5pc0Rpc2FibGVkID0gZmFsc2U7CiAgICAgICAgdGhpcy5iZHpEaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CiAgICAgICAgdGhpcy50aXRsZSA9ICLlhYnkvI/nlLXnq5nlj7DotKbmlrDlop4iOwogICAgICB9CiAgICAgIGlmICh0aGlzLmpnZGF0YVNob3cpIHsKICAgICAgICB0aGlzLmpneHhGb3JtID0ge307CiAgICAgICAgdGhpcy5maWxsQmR6KCk7IC8v6K6+572u6Ze06ZqU5omA5bGe5YWJ5LyP55S156uZCiAgICAgICAgdGhpcy5qZ1Nob3cgPSBmYWxzZTsKICAgICAgICB0aGlzLmpnRGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgICB9CiAgICAgIGlmICh0aGlzLnpuc2JkYXRhU2hvdykgewogICAgICAgIHRoaXMuYWN0aXZlVGFiTmFtZSA9ICJzYkRlc2MiOwogICAgICAgIC8vIHRoaXMuc2J4eEZvcm0gPSB7fTsKICAgICAgICBpZiAodGhpcy5zaW5nbGVDbGlja0RhdGEpIHsKICAgICAgICAgIHRoaXMuc2J4eEZvcm0gPSB7IC4uLnRoaXMuc2luZ2xlQ2xpY2tEYXRhIH07CiAgICAgICAgICB0aGlzLnNieHhGb3JtLm9iaklkID0gdW5kZWZpbmVkOwogICAgICAgIH0KICAgICAgICB0aGlzLnNieHhGb3JtLnNzZ3MgPSB0aGlzLnNzZ3M7CiAgICAgICAgdGhpcy5zYnh4Rm9ybS5zc2JkeiA9IHRoaXMuc3NiZHo7CiAgICAgICAgdGhpcy5zYnh4Rm9ybS5zc2pnID0gdGhpcy5zc2pnOwogICAgICAgIC8v5omT5byA5by55Ye65qGGCiAgICAgICAgdGhpcy56bnNiRGlhbG9nRm9ybSA9IHRydWU7CiAgICAgICAgLy/mjInpkq7lkozooajljZXmmK/lkKblj6/nvJbovpHmjqfliLYKICAgICAgICB0aGlzLmFzc2V0SXNEaXNhYmxlID0gZmFsc2U7CiAgICAgIH0KICAgIH0sCiAgICBleHBvcnRFeGNlbCgpIHsKICAgICAgbGV0IGZpbGVOYW1lID0gdGhpcy5zc2Jkem1jICsgIuiuvuWkh+S/oeaBr+ihqCI7CiAgICAgIGxldCBleHBvcnRVcmwgPSAiL2dmc2IvZXhwb3J0RXhjZWwvYXNzZXRJbmZvIjsKICAgICAgZXhwb3J0RXhjZWwoZXhwb3J0VXJsLCB0aGlzLmpnUXVlcnlQYXJhbXMsIGZpbGVOYW1lKTsKICAgIH0sCiAgICBleHBvcnRFeGNlbE9mQmR6KCkgewogICAgICBsZXQgZmlsZU5hbWUgPSAi5YWJ5LyP55S156uZ5L+h5oGv6KGoIjsKICAgICAgbGV0IGV4cG9ydFVybCA9ICIvZXF1aXBMaXN0T2ZHZnovZXhwb3J0RXhjZWxPZkJkeiI7CiAgICAgIGV4cG9ydEV4Y2VsKGV4cG9ydFVybCwgdGhpcy5qZ1F1ZXJ5UGFyYW1zLCBmaWxlTmFtZSk7CiAgICB9LAogICAgaW1wb3J0RXhjZWxPZkJkeigpIHsKICAgICAgdGhpcy5pbXBvcnRFeGNlbFVybCA9ICIvbWFuYWdlci1hcGkvZXF1aXBMaXN0T2ZHZnovaW1wb3J0RXhjZWxPZkJkeiI7CiAgICAgIHRoaXMuZmlsZU5hbWUgPSAi5YWJ5LyP55S156uZ5L+h5oGv6KGoIjsKICAgICAgdGhpcy4kcmVmcy5pbXBvcnRFeGNlbC5pbXBvcnRFeGNlbCgpOwogICAgfSwKICAgIGdldFVwbG9hZERhdGEoZmlsZU5hbWUpIHsKICAgICAgc3dpdGNoIChmaWxlTmFtZSkgewogICAgICAgIGNhc2UgIuWFieS8j+eUteermeS/oeaBr+ihqCI6CiAgICAgICAgICB0aGlzLmdldGJkekRhdGEoKTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgIumXtOmalOS/oeaBr+ihqCI6CiAgICAgICAgICB0aGlzLmdldEpnRGF0YSgpOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSAi6K6+5aSH5L+h5oGv6KGoIjoKICAgICAgICAgIHRoaXMuZ2V0Wm5zYkRhdGEoKTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGRlZmF1bHQ6CiAgICAgICAgICBicmVhazsKICAgICAgfQogICAgICB0aGlzLmdldE5ld1RyZWVJbmZvKCk7CiAgICB9LAogICAgZXhwb3J0RXhjZWxPZkpnKCkgewogICAgICBsZXQgZmlsZU5hbWUgPSAi6Ze06ZqU5L+h5oGv6KGoIjsKICAgICAgbGV0IGV4cG9ydFVybCA9ICIvZXF1aXBMaXN0T2ZHZnovZXhwb3J0RXhjZWxPZkpnIjsKICAgICAgZXhwb3J0RXhjZWwoZXhwb3J0VXJsLCB0aGlzLmpnUXVlcnlQYXJhbXMsIGZpbGVOYW1lKTsKICAgIH0sCiAgICBpbXBvcnRFeGNlbE9mSmcoKSB7CiAgICAgIHRoaXMuaW1wb3J0RXh0cmFJbmZvLnNzYmR6ID0gdGhpcy5jdXJyZW50QmR6OwogICAgICB0aGlzLmltcG9ydEV4Y2VsVXJsID0gIi9tYW5hZ2VyLWFwaS9lcXVpcExpc3RPZkdmei9pbXBvcnRFeGNlbE9mSmciOwogICAgICB0aGlzLmZpbGVOYW1lID0gIumXtOmalOS/oeaBr+ihqCI7CiAgICAgIHRoaXMuJHJlZnMuaW1wb3J0RXhjZWwuaW1wb3J0RXhjZWwoKTsKICAgIH0sCiAgICBleHBvcnRFeGNlbE9mQXNzZXQoKSB7CiAgICAgIGxldCBmaWxlTmFtZSA9ICLorr7lpIfkv6Hmga/ooagiOwogICAgICBsZXQgZXhwb3J0VXJsID0gIi9lcXVpcExpc3RPZkdmei9leHBvcnRFeGNlbE9mQXNzZXQiOwogICAgICBsZXQgcGFyYW0gPSB7fTsKICAgICAgcGFyYW0uc3NqZyA9IHRoaXMuc3NqZzsKICAgICAgZXhwb3J0RXhjZWwoZXhwb3J0VXJsLCBwYXJhbSwgZmlsZU5hbWUpOwogICAgfSwKICAgIGltcG9ydEV4Y2VsT2ZBc3NldCgpIHsKICAgICAgdGhpcy5pbXBvcnRFeHRyYUluZm8uc3NncyA9IHRoaXMuc3NnczsKICAgICAgdGhpcy5pbXBvcnRFeHRyYUluZm8uc3NiZHogPSB0aGlzLnNzYmR6OwogICAgICB0aGlzLmltcG9ydEV4dHJhSW5mby5zc2pnID0gdGhpcy5zc2pnOwogICAgICB0aGlzLmltcG9ydEV4Y2VsVXJsID0gIi9tYW5hZ2VyLWFwaS9lcXVpcExpc3RPZkdmei9pbXBvcnRFeGNlbE9mQXNzZXQiOwogICAgICB0aGlzLmZpbGVOYW1lID0gIuiuvuWkh+S/oeaBr+ihqCI7CiAgICAgIHRoaXMuJHJlZnMuaW1wb3J0RXhjZWwuaW1wb3J0RXhjZWwoKTsKICAgIH0sCiAgICBjb3B5Rm9yQXNzZXQoKSB7CiAgICAgIHRoaXMuJHJlZnNbImZvcm1Db3B5Il0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgdGhpcy5mb3JtQ29weS5zb3VyY2VTc2pnID0gdGhpcy5zc2pnOwogICAgICAgICAgY29weUFzc2V0KHRoaXMuZm9ybUNvcHkpLnRoZW4ocmVzID0+IHsKICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMuaXNTaG93Q29weSA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuJHJlZnMuZm9ybUNvcHkucmVzZXRGaWVsZHMoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8v5qC55o2u6K6+5aSH57G75Z6L6I635Y+W6K6+5aSH5Z6L5Y+3bGlzdCDmlbDmja4KICAgIGdldFNieGhCeVNibHgoc2JseCkgewogICAgICB0aGlzLmdneGhMaXN0ID0gW107CiAgICAgIGdldFNieGhMaXN0KHsgZHlzYmx4OiBzYmx4IH0pLnRoZW4ocmVzID0+IHsKICAgICAgICBpZiAocmVzLmNvZGUgPT0gIjAwMDAiKSB7CiAgICAgICAgICAvLyDkv53or4Hmr4/pobnpg73ljIXlkKtsYWJlbOWtl+autQogICAgICAgICAgdGhpcy5nZ3hoTGlzdCA9IChyZXMuZGF0YSB8fCBbXSkubWFwKGl0ZW0gPT4gewogICAgICAgICAgICByZXR1cm4gewogICAgICAgICAgICAgIC4uLml0ZW0sCiAgICAgICAgICAgICAgbGFiZWw6IGl0ZW0ubGFiZWwgfHwgaXRlbS5sZWJlbCB8fCBpdGVtLnhoIHx8ICcnCiAgICAgICAgICAgIH07CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKCiAgICAvKuermeWGheiuvuWkh+W8gOWniyovCiAgICAvL+iuvuWkh+S/ruaUueaTjeS9nAogICAgYXN5bmMgdXBkYXRlQXNzZXQocm93KSB7CiAgICAgIHJvdy54aCA9IHJvdy5nZ3hoOyAvL+inhOagvOWei+WPt+i1i+WAvAogICAgICB0aGlzLnRlY2huaWNhbFBhcmFtZXRlcnMocm93KTsKICAgICAgdGhpcy5wYXJhbVF1ZXJ5LnNibHhibSA9IHJvdy5hc3NldFR5cGVDb2RlOwogICAgICBhd2FpdCB0aGlzLmdldFBhcmFtZXRlcnMoKTsKICAgICAgdGhpcy5yZXN1bWVRdWVyeS5mb3JlaWduTnVtID0gcm93LnNibWM7CiAgICAgIHRoaXMucmVzdW1lUXVlcnkuc2JseCA9IHJvdy5zYmx4OwogICAgICB0aGlzLmJkc2JpZCA9IHJvdy5vYmpJZDsKICAgICAgdGhpcy5zYm1jID0gcm93LnNibWM7CiAgICAgIHRoaXMuc3NiZHogPSByb3cuc3NiZHo7CiAgICAgIHRoaXMuZ2V0UmVzdW1MaXN0KCk7CiAgICAgIGF3YWl0IHRoaXMuZ2V0U2J4aEJ5U2JseChyb3cuYXNzZXRUeXBlQ29kZSk7IC8v5qC55o2u6K6+5aSH57G75Z6L6I635Y+W6K6+5aSH5Z6L5Y+35LiL5ouJ5qGGCiAgICAgIC8v57uZ6KGo5Y2V6LWL5YC8CiAgICAgIHRoaXMuc2J4eEZvcm0gPSB7IC4uLnJvdyB9OwogICAgICAvL+aMiemSruWSjOihqOWNleaYr+WQpuWPr+e8lui+keaOp+WItgogICAgICB0aGlzLmFzc2V0SXNEaXNhYmxlID0gZmFsc2U7CiAgICAgIHRoaXMuc2hvd0Zzc3MgPSB0cnVlOwogICAgICAvL+aJk+W8gOiuvuWkh+W8ueWHuuahhgogICAgICB0aGlzLnpuc2JEaWFsb2dGb3JtID0gdHJ1ZTsKICAgIH0sCiAgICAvL+eCueWHu+aWsOWinu+8jOS/ruaUue+8jOivpuaDheaXtu+8jOmHjeaWsOiOt+WPluWvueW6lOeahOaKgOacr+WPguaVsOS/oeaBrwogICAgdGVjaG5pY2FsUGFyYW1ldGVycyhyb3cpIHsKICAgICAgdGhpcy5qc2NzRm9ybSA9IHt9OwogICAgICB0aGlzLnBhcmFtUXVlcnkuc2JseGJtID0gcm93LmFzc2V0VHlwZUNvZGU7CiAgICAgIHRoaXMuanNjc0Zvcm0uc2JseGJtID0gcm93LmFzc2V0VHlwZUNvZGU7CiAgICAgIHRoaXMuanNjc0Zvcm0uc2JibSA9IHJvdy5vYmpJZDsKICAgICAgdGhpcy5nZXRQYXJhbWV0ZXJzKCk7CiAgICB9LAogICAgLy/ojrflj5bmioDmnK/lj4LmlbDlr7nnhafkv6Hmga/vvIzlsZXnpLrlr7nlupTlvpfmioDmnK/lj4LmlbBsYWJlbOS/oeaBrwogICAgYXN5bmMgZ2V0UGFyYW1ldGVycygpIHsKICAgICAgdGhpcy5qc2NzTGFiZWxMaXN0ID0gW107CiAgICAgIGdldFBhcmFtRGF0YUxpc3QodGhpcy5wYXJhbVF1ZXJ5KS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5qc2NzTGFiZWxMaXN0ID0gcmVzLmRhdGE7CiAgICAgICAgdGhpcy5nZXRQYXJhbVZhbHVlKCk7CiAgICAgIH0pOwogICAgfSwKICAgIC8v6I635Y+W5oqA5pyv5Y+C5pWw5YC85L+h5oGvCiAgICBnZXRQYXJhbVZhbHVlKCkgewogICAgICBnZXRQYXJhbXNWYWx1ZSh0aGlzLmpzY3NGb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgaWYgKHJlcy5kYXRhICE9ICIiKSB7CiAgICAgICAgICB0aGlzLmpzY3NGb3JtID0geyAuLi5yZXMuZGF0YVswXSB9OwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqCiAgICAgKiDorr7lpIflsaXljoYKICAgICAqLwogICAgZ2V0UmVzdW1MaXN0KHBhcikgewogICAgICBsZXQgcGFyYW0gPSB7IC4uLnBhciwgLi4udGhpcy5yZXN1bWVRdWVyeSB9OwogICAgICBnZXRSZXN1bURhdGFMaXN0KHBhcmFtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5yZXN1bVBhZ2VJbmZvLnRhYmxlRGF0YSA9IHJlcy5kYXRhLnJlY29yZHM7CiAgICAgICAgdGhpcy5yZXN1bVBhZ2VJbmZvLnBhZ2VyLnRvdGFsID0gcmVzLmRhdGEudG90YWw7CiAgICAgIH0pOwogICAgfSwKICAgIC8v5YWJ5LyP55S156uZ5LiL5ouJ5qGG5Lit55qEY2hhbmdl5LqL5Lu2CiAgICBiZHpPcHRpb25zQ2hhbmdlQ2xpY2soKSB7CiAgICAgIC8v5b2T5Y+R55SfY2hhbmdl5LqL5Lu25pe25YWI5riF56m65LmL5YmN55qE6Ze06ZqU5L+h5oGvCiAgICAgIHRoaXMuJHNldCh0aGlzLnNieHhGb3JtLCAic3NqZyIsICIiKTsKICAgICAgLy/osIPnlKjmn6Xor6Lpl7TpmpTmlrnms5UKICAgICAgdGhpcy5nZXRKZ0RhdGFMaXN0U2VsZWN0ZWQoKTsKICAgIH0sCiAgICAvL+iOt+WPlumXtOmalOS4i+aLieahhuaVsOaNrgogICAgZ2V0SmdEYXRhTGlzdFNlbGVjdGVkKCkgewogICAgICAvL+e7meiOt+WPlumXtOmalOS4i+aLieahhuafpeivouWPguaVsOi1i+WAvAogICAgICB0aGlzLnNlbGVjdEpnT3B0aW9uc1BhcmFtLnNzYmR6ID0gdGhpcy5zYnh4Rm9ybS5zc2JkejsKICAgICAgZ2V0SmdEYXRhTGlzdFNlbGVjdGVkKHRoaXMuc2VsZWN0SmdPcHRpb25zUGFyYW0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmpnT3B0aW9uc0RhdGFMaXN0ID0gcmVzLmRhdGE7CiAgICAgIH0pOwogICAgfSwKICAgIC8v6K6+5aSH57G75Z6LY2hhbmdl5LqL5Lu244CC6I635Y+W5oqA5pyv5Y+C5pWw5L+h5oGvCiAgICBhc3luYyBzaG93UGFyYW1zKGRhdGEpIHsKICAgICAgdGhpcy5wYXJhbVF1ZXJ5LnNibHhibSA9IGRhdGE7CiAgICAgIGF3YWl0IHRoaXMuZ2V0UGFyYW1ldGVycygpOwogICAgICBhd2FpdCB0aGlzLmdldFNieGhCeVNibHgoZGF0YSk7IC8v5qC55o2u6K6+5aSH57G75Z6L6I635Y+W6K6+5aSH5Z6L5Y+35LiL5ouJ5qGGCiAgICB9LAogICAgLy/orr7lpIflsaXljoZ0YWLpobXngrnlh7vkuovku7YKICAgIGFzeW5jIGhhbmRsZVNibGxEZXNjVGFiTmFtZUNsaWNrKHRhYiwgZXZlbnQpIHsKICAgICAgc3dpdGNoICh0YWIubmFtZSkgewogICAgICAgIGNhc2UgInp0YmdqbCI6CiAgICAgICAgICBhd2FpdCB0aGlzLmdldFJlc3VtTGlzdCgpOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSAieWgiOgogICAgICAgICAgYXdhaXQgdGhpcy5nZXRZaExpc3QoKTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgInN5IjoKICAgICAgICAgIGF3YWl0IHRoaXMuZ2V0U3lMaXN0KCk7CiAgICAgICAgICBicmVhazsKICAgICAgICBjYXNlICJibGdrIjoKICAgICAgICAgIGF3YWl0IHRoaXMuZ2V0Qmxna0xpc3QoKTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgInF0c2J3dCI6CiAgICAgICAgICBhd2FpdCB0aGlzLmdldHF0c2J3dExpc3QoKTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgImp4IjoKICAgICAgICAgIGF3YWl0IHRoaXMuZ2V0SnhMaXN0KCk7CiAgICAgICAgICBicmVhazsKICAgICAgICBjYXNlICJqZGJoIjoKICAgICAgICAgIGF3YWl0IHRoaXMuZ2V0SmRiaExpc3QoKTsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgInR6amwiOgogICAgICAgICAgYXdhaXQgdGhpcy5nZXRUempsTGlzdCgpOwogICAgICAgICAgYnJlYWs7CiAgICAgIH0KICAgIH0sCiAgICAvL+S/neWtmOiuvuWkh+S/oeaBrwogICAgc3VibWl0KCkgewogICAgICB0aGlzLmFzc2V0U3VibWl0TG9hZGluZyA9IHRydWU7CiAgICAgIHRoaXMuJHJlZnNbInNieHhGb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgdGhpcy5hZGRBc3NldCgpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLmFzc2V0U3VibWl0TG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5qCh6aqM5pyq6YCa6L+H77yBIik7CiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvL+ermeWGheiuvuWkh+W8ueahhuWFs+mXrQogICAgLy/muIXnqbrooajljZUKICAgIHJlc2V0Rm9ybTEoKSB7CiAgICAgIHRoaXMuc2J4eEZvcm0gPSB0aGlzLiRvcHRpb25zLmRhdGEoKS5mb3JtOwogICAgICB0aGlzLmpzY3NGb3JtID0gdGhpcy4kb3B0aW9ucy5kYXRhKCkuZm9ybTsKICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24oKSB7CiAgICAgICAgdGhpcy4kcmVmc1sic2J4eEZvcm0iXS5jbGVhclZhbGlkYXRlKCk7CiAgICAgIH0pOwogICAgICB0aGlzLnNibGxEZXNjVGFiTmFtZSA9ICJ6dGJnamwiOwogICAgICB0aGlzLnpuc2JEaWFsb2dGb3JtID0gZmFsc2U7CiAgICB9LAogICAgLyoqCiAgICAgKiDmt7vliqDorr7lpIfkv53lrZjln7rmnKzkv6Hmga8KICAgICAqLwogICAgYWRkQXNzZXQoKSB7CiAgICAgIHRoaXMuc2J4eEZvcm0uc2JDbGFzc0NzVmFsdWUgPSB0aGlzLmpzY3NGb3JtOwogICAgICBhZGRBc3NldCh0aGlzLnNieHhGb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgaWYgKHJlcy5jb2RlID09ICIwMDAwIikgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgICAgbWVzc2FnZTogIuaTjeS9nOaIkOWKnyEiCiAgICAgICAgICB9KTsKICAgICAgICAgIHRoaXMuem5zYkRpYWxvZ0Zvcm0gPSBmYWxzZTsKICAgICAgICAgIC8vIHRoaXMudGFibGVBbmRQYWdlSW5mbzMucGFnZXIucGFnZVJlc2l6ZSA9ICJZIjsKICAgICAgICAgIHRoaXMuZ2V0Wm5zYkRhdGEoKTsKICAgICAgICB9CiAgICAgICAgdGhpcy5hc3NldFN1Ym1pdExvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLy/orr7lpIfor6bmg4Xmk43kvZwKICAgIGFzeW5jIGFzc2V0RGV0YWlscyhyb3cpIHsKICAgICAgcm93LnhoID0gcm93LmdneGg7IC8v6KeE5qC85Z6L5Y+36LWL5YC8CiAgICAgIHRoaXMudGVjaG5pY2FsUGFyYW1ldGVycyhyb3cpOwogICAgICB0aGlzLnJlc3VtZVF1ZXJ5LmZvcmVpZ25OdW0gPSByb3cuc2JtYzsKICAgICAgdGhpcy5yZXN1bWVRdWVyeS5zYmx4ID0gcm93LnNibHg7CiAgICAgIHRoaXMuYmRzYmlkID0gcm93Lm9iaklkOwogICAgICB0aGlzLnNibWMgPSByb3cuc2JtYzsKICAgICAgdGhpcy5zc2JkeiA9IHJvdy5zc2JkejsKICAgICAgdGhpcy5nZXRSZXN1bUxpc3QoKTsKICAgICAgYXdhaXQgdGhpcy5nZXRTYnhoQnlTYmx4KHJvdy5hc3NldFR5cGVDb2RlKTsgLy/moLnmja7orr7lpIfnsbvlnovojrflj5borr7lpIflnovlj7fkuIvmi4nmoYYKICAgICAgLy/nu5nooajljZXotYvlgLwKICAgICAgdGhpcy5zYnh4Rm9ybSA9IHsgLi4ucm93IH07CiAgICAgIHRoaXMuamdPcHRpb25zRGF0YUxpc3QgPSBbCiAgICAgICAgeyBsYWJlbDogdGhpcy5zYnh4Rm9ybS53em1jLCB2YWx1ZTogdGhpcy5zYnh4Rm9ybS5zc2pnIH0KICAgICAgXTsKICAgICAgLy/mjInpkq7lkozooajljZXmmK/lkKblj6/nvJbovpHmjqfliLYKICAgICAgdGhpcy5hc3NldElzRGlzYWJsZSA9IHRydWU7CiAgICAgIHRoaXMuc2hvd0Zzc3MgPSB0cnVlOwogICAgICAvL+aJk+W8gOiuvuWkh+W8ueWHuuahhgogICAgICB0aGlzLnpuc2JEaWFsb2dGb3JtID0gdHJ1ZTsKICAgIH0sCiAgICAvL+iOt+WPluWFieS8j+eUteermeS4i+aLieahhuaVsOaNrgogICAgZ2V0QmR6RGF0YUxpc3RTZWxlY3RlZCgpIHsKICAgICAgZ2V0QmR6RGF0YUxpc3RTZWxlY3RlZCh7fSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuYmR6T3B0aW9uc0RhdGFMaXN0ID0gcmVzLmRhdGE7CiAgICAgIH0pOwogICAgfSwKICAgIHJlc2V0Rm9ybSgpIHsKICAgICAgdGhpcy5qZ0RpYWxvZ0Zvcm1WaXNpYmxlID0gZmFsc2U7CiAgICAgIHRoaXMuJHJlZnMuamd4eEZvcm0ucmVzZXRGaWVsZHMoKTsKICAgIH0sCiAgICAvL+etm+mAieadoeS7tumHjee9rgogICAgZmlsdGVyUmVzZXQoKSB7Ly8g6YeN572u5Y+Y55S156uZ5p+l6K+i5Y+C5pWw77yI5L+d55WZ5qCR5b2i6YCJ5oup55qE5Z+65pys5Y+C5pWw77yJCiAgICAgIGlmICh0aGlzLmJkemRhdGFTaG93KSB7CiAgICAgICAgdGhpcy5iZHpxdWVyeVBhcmFtcyA9IHsKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICBwYWdlU2l6ZTogMTAKICAgICAgICB9OwogICAgICB9CgogICAgICAvLyDph43nva7pl7TpmpTmn6Xor6Llj4LmlbDvvIjkv53nlZnmiYDlsZ7lj5jnlLXnq5nlkozpl7TpmpTlpKfnsbvvvIkKICAgICAgaWYgKHRoaXMuamdkYXRhU2hvdykgewogICAgICAgIGNvbnN0IHNzYmR6ID0gdGhpcy5qZ1F1ZXJ5UGFyYW1zLnNzYmR6OwogICAgICAgIGNvbnN0IGpnZGwgPSB0aGlzLmpnUXVlcnlQYXJhbXMuamdkbDsKICAgICAgICB0aGlzLmpnUXVlcnlQYXJhbXMgPSB7CiAgICAgICAgICBzc2Jkejogc3NiZHosCiAgICAgICAgICBqZ2RsOiBqZ2RsLAogICAgICAgICAgZHlkajogdW5kZWZpbmVkLAogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMQogICAgICAgIH07CiAgICAgIH0KCiAgICAgIC8vIOmHjee9ruermeWGheiuvuWkh+afpeivouWPguaVsO+8iOS/neeVmeaJgOWxnumXtOmalO+8iQogICAgICBpZiAodGhpcy56bnNiZGF0YVNob3cpIHsKICAgICAgICB0aGlzLnpuc2JQYXJhbXMgPSB7CiAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICBwYWdlTnVtOiAxCiAgICAgICAgfTsKICAgICAgICAvLyDkv53nlZnpl7TpmpTnm7jlhbPnmoTmn6Xor6LmnaHku7YKICAgICAgICB0aGlzLmJkenF1ZXJ5UGFyYW1zID0gewogICAgICAgICAgc3NiZHo6ICIiLAogICAgICAgICAgc3NqZzogdGhpcy5zc2pnLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHBhZ2VTaXplOiAxMAogICAgICAgIH07CiAgICAgIH0KCiAgICAgIC8vIOmHjee9ruetm+mAieadoeS7tueahOaVsOaNruWSjOWkjemAieahhgogICAgICB0aGlzLmZpbHRlckluZm8xLmRhdGEgPSB7CiAgICAgICAgeXdkd0FycjogW10sCiAgICAgICAgamhueUFycjogW10sCiAgICAgICAgeGxBcnI6ICIiLAogICAgICAgIGpobHhBcnI6IFtdLAogICAgICAgIGpoenRBcnI6ICIiLAogICAgICAgIHNmZGQ6ICIiCiAgICAgIH07CiAgICAgIHRoaXMuZmlsdGVySW5mbzEuZmllbGRMaXN0LmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgaWYgKGl0ZW0udHlwZSA9PT0gImNoZWNrYm94IikgewogICAgICAgICAgaXRlbS5jaGVja2JveFZhbHVlID0gW107CiAgICAgICAgfQogICAgICB9KTsKCiAgICAgIHRoaXMuZmlsdGVySW5mbzIuZGF0YSA9IHsKICAgICAgICB5d2R3QXJyOiBbXSwKICAgICAgICBqaG55QXJyOiBbXSwKICAgICAgICB4bEFycjogIiIsCiAgICAgICAgamhseEFycjogW10sCiAgICAgICAgamh6dEFycjogIiIsCiAgICAgICAgc2ZkZDogIiIKICAgICAgfTsKICAgICAgdGhpcy5maWx0ZXJJbmZvMi5maWVsZExpc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICBpZiAoaXRlbS50eXBlID09PSAiY2hlY2tib3giKSB7CiAgICAgICAgICBpdGVtLmNoZWNrYm94VmFsdWUgPSBbXTsKICAgICAgICB9CiAgICAgIH0pOwoKICAgICAgdGhpcy5maWx0ZXJJbmZvMy5kYXRhID0gewogICAgICAgIHl3ZHdBcnI6IFtdLAogICAgICAgIGpobnlBcnI6IFtdLAogICAgICAgIHhsQXJyOiAiIiwKICAgICAgICBqaGx4QXJyOiBbXSwKICAgICAgICBqaHp0QXJyOiAiIiwKICAgICAgICBzZmRkOiAiIgogICAgICB9OwogICAgICB0aGlzLmZpbHRlckluZm8zLmZpZWxkTGlzdC5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgIGlmIChpdGVtLnR5cGUgPT09ICJjaGVja2JveCIpIHsKICAgICAgICAgIGl0ZW0uY2hlY2tib3hWYWx1ZSA9IFtdOwogICAgICAgIH0KICAgICAgfSk7CgogICAgICAvLyDph43nva7ooajmoLzpobXnoIEKICAgICAgaWYgKHRoaXMuYmR6ZGF0YVNob3cgJiYgdGhpcy4kcmVmcy5iZHpUYWJsZSkgewogICAgICAgIHRoaXMuJHJlZnMuYmR6VGFibGUuY3VycmVudFBhZ2UgPSAxOwogICAgICB9CiAgICAgIGlmICh0aGlzLmpnZGF0YVNob3cgJiYgdGhpcy4kcmVmcy5qZ1RhYmxlKSB7CiAgICAgICAgdGhpcy4kcmVmcy5qZ1RhYmxlLmN1cnJlbnRQYWdlID0gMTsKICAgICAgfQogICAgICBpZiAodGhpcy56bnNiZGF0YVNob3cgJiYgdGhpcy4kcmVmcy56bnNiVGFibGUpIHsKICAgICAgICB0aGlzLiRyZWZzLnpuc2JUYWJsZS5jdXJyZW50UGFnZSA9IDE7CiAgICAgIH0KCiAgICAgIC8vIOagueaNruW9k+WJjeaYvuekuueahOihqOagvOmHjeaWsOWKoOi9veaVsOaNru+8iOS/neaMgeW9k+WJjeeahOagkeW9oumAieaLqeeKtuaAge+8iQogICAgICBpZiAodGhpcy5iZHpkYXRhU2hvdykgewogICAgICAgIHRoaXMuZ2V0YmR6RGF0YSgpOwogICAgICB9IGVsc2UgaWYgKHRoaXMuamdkYXRhU2hvdykgewogICAgICAgIHRoaXMuZ2V0SmdEYXRhKCk7CiAgICAgIH0gZWxzZSBpZiAodGhpcy56bnNiZGF0YVNob3cpIHsKICAgICAgICB0aGlzLmdldFpuc2JEYXRhKCk7CiAgICAgIH0KICAgIH0sCiAgICBhc3luYyBxdWVyeVNlYXJjaEFzeW5jKHF1ZXJ5U3RyaW5nLCBjYikgewogICAgICBjb25zdCByZXN1bHRzID0gcXVlcnlTdHJpbmcKICAgICAgICA/IHRoaXMuZ2d4aExpc3QuZmlsdGVyKAogICAgICAgICAgICBpdGVtID0+CiAgICAgICAgICAgICAgaXRlbS5sYWJlbC50b0xvd2VyQ2FzZSgpLmluZGV4T2YocXVlcnlTdHJpbmcudG9Mb3dlckNhc2UoKSkgIT09IC0xCiAgICAgICAgICApCiAgICAgICAgOiB0aGlzLmdneGhMaXN0OwogICAgICAvLyDnoa7kv53mr4/kuKrpgInpobnpg73ljIXlkKsgbGFiZWwg5ZKMIHZhbHVlIOWtl+autQogICAgICBjb25zdCBmb3JtYXR0ZWRSZXN1bHRzID0gcmVzdWx0cy5tYXAoaXRlbSA9PiAoewogICAgICAgIGxhYmVsOiBpdGVtLmxhYmVsLAogICAgICAgIHZhbHVlOiBpdGVtLnZhbHVlIHx8IGl0ZW0ubGFiZWwgLy8g5aaC5p6c5rKh5pyJIHZhbHVl77yM5L2/55SoIGxhYmVsIOS9nOS4uiB2YWx1ZQogICAgICB9KSk7CiAgICAgIGNvbnNvbGUubG9nKCJyZXN1bHRzOiIsIGZvcm1hdHRlZFJlc3VsdHMpOwogICAgICBjYihmb3JtYXR0ZWRSZXN1bHRzKTsKICAgIH0sCiAgICBoYW5kbGVTZWxlY3QoaXRlbSkgewogICAgICB0aGlzLmlzU2VsZWN0aW5nRnJvbURyb3Bkb3duID0gdHJ1ZTsgLy8g6K6+572u5qCH6K6wCiAgICAgIHRoaXMuc2J4eEZvcm0ueGggPSBpdGVtLmxhYmVsOwogICAgfSwKICAgIGFzeW5jIGhhbmRsZUNoYW5nZSh2YWx1ZSkgewogICAgICAvLyDlpoLmnpzmmK/ku47kuIvmi4nliJfooajpgInmi6nnmoTvvIzkuI3miafooYzmlrDlop7mk43kvZwKICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgaWYgKHRoaXMuaXNTZWxlY3RpbmdGcm9tRHJvcGRvd24pIHsKICAgICAgICAgIHRoaXMuaXNTZWxlY3RpbmdGcm9tRHJvcGRvd24gPSBmYWxzZTsgLy8g6YeN572u5qCH6K6wCiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQogICAgICAgIC8vIOWmguaenOi+k+WFpeeahOWAvOS4jeWcqOmAiemhueS4re+8jOWImea3u+WKoOWIsOWei+WPt+W6kwogICAgICBpZiAodmFsdWUgJiYgIXRoaXMuZ2d4aExpc3Quc29tZShpdGVtID0+IGl0ZW0ubGFiZWwgPT09IHZhbHVlKSkgewogICAgICAgIHRyeSB7CiAgICAgICAgICBjb25zdCBwYXJhbXMgPSB7CiAgICAgICAgICAgIHNieGg6IHZhbHVlLAogICAgICAgICAgICBkeXNibHg6IHRoaXMuc2J4eEZvcm0uYXNzZXRUeXBlQ29kZSAvLyDku47ooajljZXkuK3ojrflj5borr7lpIfnsbvlnosKICAgICAgICAgIH0KICAgICAgICAgIGFkZFNieGgocGFyYW1zKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gJzAwMDAnKSB7CiAgICAgICAgICAgICAgdGhpcy5nZ3hoTGlzdC5wdXNoKHsKICAgICAgICAgICAgICAgIGxhYmVsOiB2YWx1ZSwKICAgICAgICAgICAgICAgIHZhbHVlOiB2YWx1ZSAvLyDmt7vliqAgdmFsdWUg5a2X5q61CiAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+W3sua3u+WKoOWIsOWei+WPt+W6kycpCiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKHJlcy5tc2cgfHwgJ+a3u+WKoOWei+WPt+Wksei0pScpCiAgICAgICAgICAgIH0KICAgICAgICAgIH0pCiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICAgIGNvbnNvbGUud2FybmluZygn5re75Yqg5Z6L5Y+35aSx6LSlOicsIGVycm9yKQogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfmt7vliqDlnovlj7flpLHotKUnKQogICAgICAgIH0KICAgICAgfQogICAgICB9LCA1MDApOwogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["gftz.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2nDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "gftz.vue", "sourceRoot": "src/views/dagangOilfield/dwzygl/gfgl", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card shadow=\"never\" style=\"background:#e0f8ed;padding-top:10px;\">\n          <div>\n            <el-col>\n              <el-form label-width=\"80px\">\n                <!-- <el-col :span=\"24\">\n                  <el-form-item label=\"所属公司:\">\n                    <el-select\n                      v-model=\"treeForm.ssdwbm\"\n                      placeholder=\"请选择所属公司\"\n                      @change=\"getNewTreeInfo\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in OrganizationSelectedList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col> -->\n                <el-col :span=\"24\">\n                  <el-form-item label=\"电压等级:\">\n                    <el-select\n                      v-model=\"treeForm.dydjbm\"\n                      placeholder=\"请选择电压等级\"\n                      @change=\"getNewTreeInfo\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in VoltageLevelSelectedList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"快速查询:\">\n                    <el-input\n                      placeholder=\"输入关键字进行过滤\"\n                      v-model=\"filterText\"\n                      clearable\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-form>\n            </el-col>\n          </div>\n          <div class=\"text head-container\">\n            <el-col style=\"padding:0;\">\n              <el-tree\n                :expand-on-click-node=\"true\"\n                id=\"tree\"\n                :data=\"treeOptions\"\n                @node-click=\"handleNodeClick\"\n                :highlight-current=\"true\"\n                ref=\"tree\"\n                :filter-node-method=\"filterNode\"\n                node-key=\"id\"\n                :default-checked-keys=\"['0']\"\n                :default-expanded-keys=\"['0']\"\n                accordion\n              >\n                <span slot-scope=\"{ node, data }\">\n                  <i :class=\"icons[data.icon]\" />\n                  <span style=\"margin-left:5px;\" :title=\"data.label\">{{\n                    data.label\n                  }}</span>\n                </span>\n              </el-tree>\n              <!--              :default-expanded-keys=\"['0']\"-->\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo1.data\"\n          :field-list=\"filterInfo1.fieldList\"\n          :width=\"{ labelWidth: 100, itemWidth: 170 }\"\n          @handleReset=\"filterReset\"\n          comp-table=\"tableAndPageInfo1\"\n          v-show=\"bdzdataShow\"\n        />\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo2.data\"\n          :field-list=\"filterInfo2.fieldList\"\n          :width=\"{ labelWidth: 100, itemWidth: 150 }\"\n          @handleReset=\"filterReset\"\n          comp-table=\"tableAndPageInfo2\"\n          v-show=\"jgdataShow\"\n        />\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo3.data\"\n          :field-list=\"filterInfo3.fieldList\"\n          :width=\"{ labelWidth: 100, itemWidth: 150 }\"\n          @handleReset=\"filterReset\"\n          comp-table=\"tableAndPageInfo3\"\n          v-show=\"znsbdataShow\"\n        />\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button\n              icon=\"el-icon-plus\"\n              @click=\"AddButton\"\n              v-hasPermi=\"['jgdy:button:add']\"\n              type=\"primary\"\n              >新增\n            </el-button>\n            <el-button\n              v-show=\"jgdataShow\"\n              type=\"success\"\n              icon=\"el-icon-download\"\n              @click=\"exportExcel\"\n              >导出整站设备</el-button\n            >\n            <el-button\n              v-show=\"bdzdataShow\"\n              type=\"success\"\n              icon=\"el-icon-download\"\n              @click=\"exportExcelOfBdz\"\n              >导出光伏电站</el-button\n            >\n            <el-button\n              v-hasPermi=\"['jgdy:button:add']\"\n              v-show=\"bdzdataShow\"\n              type=\"success\"\n              icon=\"el-icon-upload\"\n              @click=\"importExcelOfBdz\"\n              >导入光伏电站</el-button\n            >\n            <el-button\n              v-show=\"jgdataShow\"\n              type=\"success\"\n              icon=\"el-icon-download\"\n              @click=\"exportExcelOfJg\"\n              >导出间隔</el-button\n            >\n            <el-button\n              v-hasPermi=\"['jgdy:button:add']\"\n              v-show=\"jgdataShow\"\n              type=\"success\"\n              icon=\"el-icon-upload\"\n              @click=\"importExcelOfJg\"\n              >导入间隔</el-button\n            >\n            <el-button\n              v-hasPermi=\"['jgdy:button:add']\"\n              v-show=\"znsbdataShow\"\n              type=\"primary\"\n              icon=\"el-icon-plus\"\n              @click=\"isShowCopy = true\"\n              >设备复制</el-button\n            >\n            <el-button\n              v-show=\"znsbdataShow\"\n              type=\"success\"\n              icon=\"el-icon-download\"\n              @click=\"exportExcelOfAsset\"\n              >导出设备</el-button\n            >\n            <el-button\n              v-hasPermi=\"['jgdy:button:add']\"\n              v-show=\"znsbdataShow || jgdataShow\"\n              type=\"success\"\n              icon=\"el-icon-upload\"\n              @click=\"importExcelOfAsset\"\n              >导入整站设备</el-button\n            >\n            <!--<el-button icon=\"el-icon-delete\" v-hasPermi=\"['jgdy:button:delete']\" type=\"danger\" @click=\"removeAll\">删除-->\n            <!--</el-button>-->\n          </div>\n          <!--光伏电站-->\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo1\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"61vh\"\n            v-show=\"bdzdataShow\"\n            @getMethod=\"getbdzData\"\n            v-loading=\"loading\"\n            ref=\"bdzTable\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"updatebdz(scope.row)\"\n                  v-hasPermi=\"['jgdy:button:update']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                >\n                </el-button>\n                <el-button\n                  @click=\"bdzDetails(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n                <el-button\n                  v-hasPermi=\"['jgdy:button:delete']\"\n                  type=\"text\"\n                  v-if=\"scope.row.createBy === $store.getters.name\"\n                  size=\"small\"\n                  @click=\"removeAll(scope.row)\"\n                  title=\"删除\"\n                  class=\"el-icon-delete\"\n                >\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n          <!--间隔-->\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo2\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"70vh\"\n            v-show=\"jgdataShow\"\n            @getMethod=\"getJgData\"\n            ref=\"jgTable\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"updateJg(scope.row)\"\n                  v-hasPermi=\"['jgdy:button:update']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                ></el-button>\n                <el-button\n                  @click=\"jgDetails(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n                <el-button\n                  v-hasPermi=\"['jgdy:button:delete']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"删除\"\n                  class=\"el-icon-delete\"\n                  @click=\"removeAll(scope.row)\"\n                >\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n          <!--设备-->\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo3\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"61vh\"\n            v-show=\"znsbdataShow\"\n            @getMethod=\"getZnsbData\"\n            ref=\"znsbTable\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"updateAsset(scope.row)\"\n                  v-hasPermi=\"['jgdy:button:update']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                ></el-button>\n                <el-button\n                  @click=\"assetDetails(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n                <el-button\n                  v-hasPermi=\"['jgdy:button:delete']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"删除\"\n                  class=\"el-icon-delete\"\n                  @click=\"removeAll(scope.row)\"\n                >\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!--光伏电站所用弹出框开始-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"bdzDialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"60%\"\n      v-dialogDrag\n      :before-close=\"removeForm\"\n    >\n      <el-form :model=\"jbxxForm\" label-width=\"130px\" :rules=\"rules\" ref=\"form\">\n        <div\n          class=\"block\"\n          style=\"width: 50%;height: 50%;margin-bottom: 2%;float: right\"\n        >\n          <span class=\"demonstration\">光伏电站图片</span>\n          <el-carousel\n            trigger=\"click\"\n            class=\"imgCls\"\n            indicator-position=\"none\"\n            :interval=\"2000\"\n            type=\"card\"\n            id=\"imgId\"\n          >\n            <el-carousel-item v-for=\"(img, index) in imgList\" :key=\"index\">\n              <viewer :images=\"imgList\" style=\"z-index: 999\">\n                <img :src=\"img.url\" width=\"100%\" height=\"100%\" />\n              </viewer>\n            </el-carousel-item>\n          </el-carousel>\n        </div>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属单位\" prop=\"ssdwbm\">\n              <el-select\n                v-model=\"jbxxForm.ssdwbm\"\n                :placeholder=\"isDisabled ? '' : '所属单位'\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option\n                  v-for=\"item in OrganizationSelectedList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"光伏电站数字编号\" prop=\"bdzszbh\">\n              <el-input\n                v-model=\"jbxxForm.bdzszbh\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入光伏电站数字编号'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"光伏电站名称\" prop=\"bdzmc\">\n              <el-input\n                v-model=\"jbxxForm.bdzmc\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入光伏电站名称'\"\n                type=\"textarea\"\n                :autosize=\"{minRows:2,maxRows:4}\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"所属电网\" prop=\"ssdw\">\n              <el-input\n                v-model=\"jbxxForm.ssdw\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入所属电网'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属基地站\" prop=\"ssjdz\">\n              <el-select\n                v-model=\"jbxxForm.ssjdz\"\n                :placeholder=\"isDisabled ? '' : '请选择所属基地站'\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option\n                  v-for=\"item in ssjdzList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"电压等级\" prop=\"dydjbm\">\n              <el-select\n                v-model=\"jbxxForm.dydjbm\"\n                :placeholder=\"isDisabled ? '' : '请选择电压等级'\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option\n                  v-for=\"item in VoltageLevelSelectedList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"设备状态\" prop=\"sbzt\">\n              <el-select\n                v-model=\"jbxxForm.sbzt\"\n                :placeholder=\"isDisabled ? '' : '请选择设备状态'\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option\n                  v-for=\"item in sbztOptionsDataList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"投运日期\" class=\"add_sy_tyrq\" prop=\"tyrq\">\n              <el-date-picker\n                v-model=\"jbxxForm.tyrq\"\n                type=\"date\"\n                :placeholder=\"isDisabled ? '' : '选择日期'\"\n                format=\"yyyy-MM-dd\"\n                value-format=\"yyyy-MM-dd\"\n                :picker-options=\"pickerOptions\"\n                :disabled=\"isDisabled\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          \n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"是否综合自动化站\" prop=\"sfzhzdh\">\n              <el-select v-model=\"jbxxForm.sfzhzdh\" :disabled=\"isDisabled\">\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"是否数字化光伏电站\" prop=\"sfszhbdz\">\n              <el-select\n                v-model=\"jbxxForm.sfszhbdz\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请选择'\"\n              >\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否枢纽站\" prop=\"sfsnz\">\n              <el-select\n                v-model=\"jbxxForm.sfsnz\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请选择'\"\n              >\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item\n              label=\"退运日期\"\n              class=\"add_sy_tyrq\"\n              prop=\"returnDate\"\n            >\n              <el-date-picker\n                v-model=\"jbxxForm.returnDate\"\n                type=\"date\"\n                :placeholder=\"isDisabled ? '' : '选择日期'\"\n                format=\"yyyy-MM-dd\"\n                value-format=\"yyyy-MM-dd\"\n                :picker-options=\"pickerOptions\"\n                :disabled=\"isDisabled\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col> -->\n          <!-- 并网站-->\n          <el-col :span=\"8\">\n            <el-form-item label=\"并网站\" prop=\"bwz\">\n              <el-input\n                v-model=\"jbxxForm.bwz\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入并网站'\"\n                type=\"textarea\"\n                :autosize=\"{minRows:2,maxRows:4}\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <!-- 并网点-->\n          <el-col :span=\"8\">\n            <el-form-item label=\"并网点\" prop=\"bwd\">\n              <el-input\n                v-model=\"jbxxForm.bwd\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入并网点'\"\n                type=\"textarea\"\n                :autosize=\"{minRows:2,maxRows:4}\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"8\">\n            <el-form-item label=\"交流侧容量MW\" prop=\"jlcrl\">\n              <el-input\n                v-model=\"jbxxForm.jlcrl\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入交流侧容量'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"直流侧容量MWP\" prop=\"zlcrl\">\n              <el-input\n                v-model=\"jbxxForm.zlcrl\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入直流侧容量'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"占地面积\" prop=\"zymj\">\n              <el-input\n                v-model=\"jbxxForm.zymj\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入占地面积'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"污秽等级\" prop=\"whdj\">\n              <el-select v-model=\"jbxxForm.whdj\" :disabled=\"isDisabled\">\n                <el-option value=\"a级\" label=\"a级\"></el-option>\n                <el-option value=\"b级\" label=\"b级\"></el-option>\n                <el-option value=\"c级\" label=\"c级\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"值班方式\" prop=\"zbfs\">\n              <el-select\n                v-model=\"jbxxForm.zbfs\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请选择'\"\n              >\n                <el-option value=\"有人值班\" label=\"有人值班\"></el-option>\n                <el-option value=\"无人值班\" label=\"无人值班\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否光纤通讯\" prop=\"sfgqtx\">\n              <el-select\n                v-model=\"jbxxForm.sfgqtx\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请选择'\"\n              >\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"海拔\" prop=\"hb\">\n              <el-input v-model=\"jbxxForm.hb\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col> -->\n\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"工程编号\" prop=\"gcbh\">\n              <el-input v-model=\"jbxxForm.gcbh\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"8\">\n            <el-form-item label=\"设计单位\" prop=\"sjdw\">\n              <el-input v-model=\"jbxxForm.sjdw\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"8\">\n            <el-form-item label=\"监理单位\" prop=\"jldw\">\n              <el-input v-model=\"jbxxForm.jldw\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"电站重要级别\" prop=\"zyjb\">\n              <el-select\n                v-model=\"jbxxForm.zyjb\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请选择'\"\n              >\n                <el-option value=\"A\" label=\"A\"></el-option>\n                <el-option value=\"B\" label=\"B\"></el-option>\n                <el-option value=\"C\" label=\"C\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"站址\" prop=\"bdzdz\">\n              <el-input\n                v-model=\"jbxxForm.bdzdz\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入站址'\"\n                type=\"textarea\"\n                :autosize=\"{minRows:1,maxRows:4}\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电站形式\" prop=\"dzlx\">\n              <el-select v-model=\"jbxxForm.dzlx\" :disabled=\"isDisabled\">\n                <el-option value=\"分散式\" label=\"分散式\"></el-option>\n                <el-option value=\"分布式\" label=\"分布式\"></el-option>\n                <el-option value=\"集中式\" label=\"集中式\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电站类型\" prop=\"bzfs\">\n              <el-select\n                v-model=\"jbxxForm.bzfs\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请选择'\"\n              >\n                <el-option value=\"屋顶\" label=\"屋顶\"></el-option>\n                <el-option value=\"地面\" label=\"地面\"></el-option>\n                <el-option value=\"坑塘\" label=\"坑塘\"></el-option>\n                <el-option value=\"水面\" label=\"水面\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          \n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"建筑面积\" prop=\"jzmj\">\n              <el-input\n                v-model=\"jbxxForm.jzmj\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入建筑面积'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"联系电话\" prop=\"phone\">\n              <el-input\n                v-model=\"jbxxForm.phone\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入联系电话'\"\n              ></el-input>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"工程名称\" prop=\"gcmc\">\n              <el-input v-model=\"jbxxForm.gcmc\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"施工单位\" prop=\"sgdw\">\n              <el-input v-model=\"jbxxForm.sgdw\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"地区特征\" prop=\"dqtz\">\n              <el-select v-model=\"jbxxForm.dqtz\" :disabled=\"isDisabled\">\n                <el-option value=\"户内\" label=\"户内\"></el-option>\n                <el-option value=\"户外\" label=\"户外\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"最高调度管辖权\" prop=\"zgddgxq\">\n              <el-input\n                v-model=\"jbxxForm.zgddgxq\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入最高调度管辖权'\"\n              ></el-input>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"是否满足N-1\" prop=\"sfmzn\">\n              <el-select v-model=\"jbxxForm.sfmzn\" :disabled=\"isDisabled\">\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>-->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"是否接入故障系统\" prop=\"sfjrgzxt\">\n              <el-select\n                v-model=\"jbxxForm.sfjrgzxt\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请选择'\"\n              >\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否接入AVC\" prop=\"sfjravc\">\n              <el-select\n                v-model=\"jbxxForm.sfjravc\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请选择'\"\n              >\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否集中监控\" prop=\"sfjzjk\">\n              <el-select\n                v-model=\"jbxxForm.sfjzjk\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请选择'\"\n              >\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"接入的监控中心\" prop=\"jkzxmc\">\n              <el-input\n                v-model=\"jbxxForm.jkzxmc\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入监控中心'\"\n              ></el-input>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"经度\" prop=\"jd\">\n              <el-input\n                v-model=\"jbxxForm.jd\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入经度'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"纬度\" prop=\"wd\">\n              <el-input\n                v-model=\"jbxxForm.wd\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"isDisabled ? '' : '请输入纬度'\"\n              >\n              </el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\" prop=\"bz\">\n              <el-input\n                v-model=\"jbxxForm.bz\"\n                :disabled=\"isDisabled\"\n                type=\"textarea\"\n                rows=\"2\"\n                :placeholder=\"isDisabled ? '' : '请输入备注'\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-form-item\n            label=\"已上传图片：\"\n            prop=\"attachment\"\n            v-if=\"jbxxForm.attachment.length > 0\"\n            id=\"pic_form\"\n          >\n            <el-col\n              :span=\"24\"\n              v-for=\"(item, index) in jbxxForm.attachment\"\n              style=\"margin-left: 0\"\n            >\n              <el-form-item :label=\"(index + 1).toString()\">\n                {{ item.fileOldName }}\n                <el-button\n                  v-if=\"!isDisabled\"\n                  type=\"error\"\n                  size=\"mini\"\n                  @click=\"deleteFileById(item.fileId)\"\n                  >删除\n                </el-button>\n              </el-form-item>\n            </el-col>\n          </el-form-item>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-form-item label=\"上传图片：\" v-if=\"!isDisabled\">\n            <el-upload\n              list-type=\"picture-card\"\n              class=\"upload-demo\"\n              accept=\".jpg,.png\"\n              ref=\"upload\"\n              :headers=\"header\"\n              action=\"/isc-api/file/upload\"\n              :before-upload=\"beforeUpload\"\n              :data=\"uploadData\"\n              single\n              :auto-upload=\"false\"\n              multiple\n            >\n              <el-button slot=\"trigger\" type=\"primary\">选取文件</el-button>\n              <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png文件</div>\n            </el-upload>\n          </el-form-item>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"removeForm\">取 消</el-button>\n        <el-button\n          v-if=\"title == '光伏电站台账修改' || title == '光伏电站台账新增'\"\n          type=\"primary\"\n          @click=\"addBdz\"\n          class=\"pmyBtn\"\n          >确 定\n        </el-button>\n      </div>\n    </el-dialog>\n    <!--  光伏电站新增  -->\n    <el-dialog\n      title=\"设备详情\"\n      :visible.sync=\"znsbDialogForm\"\n      width=\"68%\"\n      :before-close=\"resetForm1\"\n      v-dialogDrag\n      v-if=\"znsbDialogForm\"\n    >\n      <el-tabs v-model=\"activeTabName\">\n        <el-tab-pane label=\"基本信息\" name=\"sbDesc\">\n          <el-form\n            :model=\"sbxxForm\"\n            label-width=\"130px\"\n            ref=\"sbxxForm\"\n            :rules=\"sbxxRules\"\n            :disabled=\"assetIsDisable\"\n          >\n            <el-card class=\"box-cont\">\n              <div slot=\"header\" class=\"clearfix\">\n                <span>基本信息</span>\n              </div>\n              <el-row :gutter=\"20\" class=\"cont_top\">\n                <el-col :span=\"8\">\n                  <el-form-item label=\"所属公司\" prop=\"ssgs\">\n                    <el-select\n                      v-model=\"sbxxForm.ssgs\"\n                      :placeholder=\"assetIsDisable ? '' : '请选择所属公司'\"\n                      filterable\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in OrganizationSelectedList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"String(item.value)\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"所属电站\" prop=\"ssbdz\">\n                    <el-select\n                      v-model=\"sbxxForm.ssbdz\"\n                      :placeholder=\"assetIsDisable ? '' : '请选择所属电站'\"\n                      filterable\n                      @change=\"bdzOptionsChangeClick\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in bdzOptionsDataList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"所属间隔\" prop=\"ssjg\">\n                    <el-select\n                      v-model=\"sbxxForm.ssjg\"\n                      :placeholder=\"assetIsDisable ? '' : '请选择所属间隔'\"\n                      filterable\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in jgOptionsDataList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"设备代码\" prop=\"sbdm\">\n                    <el-input\n                      v-model=\"sbxxForm.sbdm\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写设备代码'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"设备名称\" prop=\"sbmc\">\n                    <el-input\n                      v-model=\"sbxxForm.sbmc\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写设备名称'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"设备类型\" prop=\"assetTypeCode\">\n                    <el-select\n                      v-model=\"sbxxForm.assetTypeCode\"\n                      :placeholder=\"assetIsDisable ? '' : '请选择设备类型'\"\n                      @change=\"showParams\"\n                      filterable\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in sblxOptionsDataSelected\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"电压等级\" prop=\"dydjbm\">\n                    <el-select\n                      v-model=\"sbxxForm.dydjbm\"\n                      :placeholder=\"assetIsDisable ? '' : '请选择电压等级'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in VoltageLevelSelectedList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"设备状态\" prop=\"sbzt\">\n                    <el-select\n                      v-model=\"sbxxForm.sbzt\"\n                      :placeholder=\"assetIsDisable ? '' : '请选择设备状态'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in sbztOptionsDataList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"8\">\n                  <el-form-item label=\"相数\">\n                    <el-select\n                      v-model=\"sbxxForm.xs\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写相数'\"\n                      @change=\"xsChangeFunc\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in xsOptions\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"8\">\n                  <el-form-item label=\"相别\">\n                    <el-select\n                      v-model=\"sbxxForm.xb\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写相别'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in xbOptions\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"8\">\n                  <el-form-item label=\"安装位置\" prop=\"azwz\">\n                    <el-select\n                      v-model=\"sbxxForm.azwz\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写安装位置'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in placeOptions\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"用途\">\n                    <el-input\n                      v-model=\"sbxxForm.yt\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写用途'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"型号\" prop=\"xh\">\n                    <el-autocomplete\n                      v-model=\"sbxxForm.xh\"\n                      :fetch-suggestions=\"querySearchAsync\"\n                      :placeholder=\"assetIsDisable ? '' : '请选择或输入型号'\"\n                      @select=\"handleSelect\"\n                      @change=\"handleChange\"\n                    ></el-autocomplete>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"使用环境\" prop=\"syhj\">\n                    <el-select\n                      v-model=\"sbxxForm.syhj\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写使用环境'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in placeOptions\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"生产厂家\" prop=\"sccj\">\n                    <el-input\n                      v-model=\"sbxxForm.sccj\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写生产厂家'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"制造国家\">\n                    <el-input\n                      v-model=\"sbxxForm.zzgj\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写制造国家'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"产品代号\">\n                    <el-input\n                      v-model=\"sbxxForm.cpdh\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写产品代号'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"8\">\n                  <el-form-item label=\"组合设备类型\">\n                    <el-input\n                      v-model=\"sbxxForm.zhsblxbm\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写组合设备类型'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"组合设备类型名称\">\n                    <el-input\n                      v-model=\"sbxxForm.zhsblx\"\n                      :placeholder=\"\n                        assetIsDisable ? '' : '请填写组合设备类型名称'\n                      \"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"8\">\n                  <el-form-item label=\"运行编号\" prop=\"yxbh\">\n                    <el-input\n                      v-model=\"sbxxForm.yxbh\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写运行编号'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"出厂编号\" prop=\"ccbh\">\n                    <el-input\n                      v-model=\"sbxxForm.ccbh\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写出厂编号'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item\n                    label=\"出厂日期\"\n                    class=\"add_sy_tyrq\"\n                    prop=\"ccrq\"\n                  >\n                    <el-date-picker\n                      v-model=\"sbxxForm.ccrq\"\n                      type=\"date\"\n                      :placeholder=\"assetIsDisable ? '' : '选择日期'\"\n                      format=\"yyyy-MM\"\n                      value-format=\"yyyy-MM-dd\"\n                    >\n                    </el-date-picker>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item\n                    label=\"投运日期\"\n                    class=\"add_sy_tyrq\"\n                    prop=\"tyrq\"\n                  >\n                    <el-date-picker\n                      v-model=\"sbxxForm.tyrq\"\n                      type=\"date\"\n                      :placeholder=\"assetIsDisable ? '' : '选择日期'\"\n                      format=\"yyyy-MM-dd\"\n                      :picker-options=\"pickerOptions\"\n                      value-format=\"yyyy-MM-dd\"\n                    >\n                    </el-date-picker>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row :gutter=\"20\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"备注\" prop=\"bz\">\n                    <el-input\n                      v-model=\"sbxxForm.bz\"\n                      type=\"textarea\"\n                      rows=\"2\"\n                      :placeholder=\"assetIsDisable ? '' : '请输入备注'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n            </el-card>\n            <el-card class=\"box-cont\">\n              <div slot=\"header\" class=\"clearfix\">\n                <span>技术参数</span>\n              </div>\n              <el-row :gutter=\"20\" class=\"cont_top\">\n                <!--  一次设备 改为都要 -->\n                <el-col :span=\"8\">\n                  <el-form-item label=\"额定电压\" prop=\"eddy\">\n                    <el-input\n                      v-model=\"sbxxForm.eddy\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写额定电压'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"额定频率\" prop=\"edpl\">\n                    <el-input\n                      v-model=\"sbxxForm.edpl\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写额定频率'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"额定电流\" prop=\"eddl\">\n                    <el-input\n                      v-model=\"sbxxForm.eddl\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写额定电流'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"额定容量\" prop=\"edrl\">\n                    <el-input\n                      v-model=\"sbxxForm.edrl\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写额定容量'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"联结组标号\" prop=\"ljzbh\">\n                    <el-input\n                      v-model=\"sbxxForm.ljzbh\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写联结组标号'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"器身质量\" prop=\"qszl\">\n                    <el-input\n                      v-model=\"sbxxForm.qszl\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写器身质量'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"油质量\" prop=\"yzl\">\n                    <el-input\n                      v-model=\"sbxxForm.yzl\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写油质量'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"档位个数\" prop=\"dwgs\">\n                    <el-input\n                      v-model=\"sbxxForm.dwgs\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写档位个数'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"空载损耗\" prop=\"kzsh\">\n                    <el-input\n                      v-model=\"sbxxForm.kzsh\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写空载损耗'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"空载电流\" prop=\"kzdl\">\n                    <el-input\n                      v-model=\"sbxxForm.kzdl\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写空载电流'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"负载损耗\" prop=\"fzsh\">\n                    <el-input\n                      v-model=\"sbxxForm.fzsh\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写负载损耗'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"冷却方式\" prop=\"lqfs\">\n                    <el-input\n                      v-model=\"sbxxForm.lqfs\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写冷却方式'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"短路阻抗\" prop=\"dlkz\">\n                    <el-input\n                      v-model=\"sbxxForm.dlkz\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"变比/精度1\" prop=\"jddj1\">\n                    <el-input\n                      v-model=\"sbxxForm.jddj1\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写变比/精度'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"变比/精度2\" prop=\"jddj2\">\n                    <el-input\n                      v-model=\"sbxxForm.jddj2\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写变比/精度'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"变比/精度3\" prop=\"jddj3\">\n                    <el-input\n                      v-model=\"sbxxForm.jddj3\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写变比/精度'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"变比/精度4\" prop=\"jddj4\">\n                    <el-input\n                      v-model=\"sbxxForm.jddj4\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写变比/精度'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <!--  二次设备 -->\n                <el-col v-if=\"jgQueryParams.jgdl == 'ec'\" :span=\"8\">\n                  <el-form-item label=\"版本号\" prop=\"bbh\">\n                    <el-input\n                      v-model=\"sbxxForm.bbh\"\n                      :placeholder=\"assetIsDisable ? '' : '请填写版本号'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n            </el-card>\n          </el-form>\n        </el-tab-pane>\n        <el-tab-pane label=\"设备履历\" name=\"sbRecord\" v-if=\"showFsss\">\n          <el-tabs\n            v-model=\"sbllDescTabName\"\n            @tab-click=\"handleSbllDescTabNameClick\"\n            type=\"card\"\n          >\n            <el-tab-pane label=\"状态变更\" name=\"ztbgjl\">\n              <comp-table\n                :table-and-page-info=\"resumPageInfo\"\n                @getMethod=\"getResumList\"\n                @update:multipleSelection=\"handleSelectionChange\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"隐患\" name=\"yh\">\n              <comp-table\n                :table-and-page-info=\"yhPageInfo\"\n                @getMethod=\"getYhList\"\n                v-loading=\"yhloading\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"试验\" name=\"sy\">\n              <comp-table\n                :table-and-page-info=\"syPageInfo\"\n                @getMethod=\"getSyList\"\n                v-loading=\"syloading\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"不良工况\" name=\"blgk\">\n              <comp-table\n                :table-and-page-info=\"blgkPageInfo\"\n                @getMethod=\"getBlgkList\"\n                v-loading=\"blgkloading\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"其它设备问题\" name=\"qtsbwt\">\n              <comp-table\n                :table-and-page-info=\"qtsbwtPageInfo\"\n                @getMethod=\"getqtsbwtList\"\n                v-loading=\"qtsbwtloading\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"设备检修\" name=\"jx\">\n              <comp-table\n                :table-and-page-info=\"jxPageInfo\"\n                @getMethod=\"getJxList\"\n                v-loading=\"jxloading\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"继电保护\" name=\"jdbh\">\n              <comp-table\n                :table-and-page-info=\"jdbhPageInfo\"\n                @getMethod=\"getJdbhList\"\n                v-loading=\"jdbhLoading\"\n              />\n            </el-tab-pane>\n            <el-tab-pane label=\"跳闸记录\" name=\"tzjl\">\n              <comp-table\n                :table-and-page-info=\"tzjlPageInfo\"\n                @getMethod=\"getTzjlList\"\n                v-loading=\"tzjlLoading\"\n              />\n            </el-tab-pane>\n          </el-tabs>\n        </el-tab-pane>\n        <el-tab-pane label=\"附属设施\" name=\"fsss\" v-if=\"showFsss\">\n          <Fsss\n            v-if=\"znsbDialogForm\"\n            :sb-info=\"this.sbxxForm\"\n            :can-edit=\"!this.assetIsDisable\"\n          ></Fsss>\n        </el-tab-pane>\n      </el-tabs>\n      <div slot=\"footer\" class=\"dialog-footer\" v-show=\"!assetIsDisable\">\n        <el-button @click=\"resetForm1\">取 消</el-button>\n        <el-button\n          v-show=\"activeTabName === 'sbDesc'\"\n          type=\"primary\"\n          @click=\"submit\"\n          :loading=\"assetSubmitLoading\"\n          class=\"pmyBtn\"\n          >确 定</el-button\n        >\n      </div>\n    </el-dialog>\n    <!--光伏电站所用弹出框结束-->\n\n    <!--间隔详情弹出框展示开始-->\n    <el-dialog\n      title=\"间隔信息\"\n      :visible.sync=\"jgDialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"60%\"\n      v-dialogDrag\n      :before-close=\"resetForm\"\n      @open=\"fillBdz\"\n    >\n      <el-form\n        ref=\"jgxxForm\"\n        :model=\"jgxxForm\"\n        :rules=\"rules\"\n        label-width=\"130px\"\n      >\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属电站\" prop=\"ssbdz\">\n              <el-select\n                v-model=\"jgxxForm.ssbdz\"\n                placeholder=\"请选择所属电站\"\n                filterable\n                :disabled=\"jgShow\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in bdzOptionsDataList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"间隔名称\" prop=\"jgmc\">\n              <el-input\n                v-model=\"jgxxForm.jgmc\"\n                placeholder=\"请输入间隔名称\"\n                :disabled=\"jgShow\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"间隔类型\" prop=\"jglx\">\n              <el-select\n                v-model=\"jgxxForm.jglx\"\n                placeholder=\"请选择间隔类型\"\n                filterable\n                :disabled=\"jgShow\"\n              >\n                <el-option\n                  v-for=\"item in jglxOptionsDataList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电压等级\" prop=\"dydjbm\">\n              <el-select\n                v-model=\"jgxxForm.dydjbm\"\n                :disabled=\"jgShow\"\n                placeholder=\"请选择电压等级\"\n              >\n                <el-option\n                  v-for=\"item in jgDydOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"投运日期\" class=\"add_sy_tyrq\" prop=\"tyrq\">\n              <el-date-picker\n                v-model=\"jgxxForm.tyrq\"\n                type=\"date\"\n                placeholder=\"选择日期\"\n                format=\"yyyy-MM-dd\"\n                value-format=\"yyyy-MM-dd\"\n                :disabled=\"jgShow\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\" prop=\"bz\">\n              <el-input\n                v-model=\"jgxxForm.bz\"\n                :disabled=\"jgShow\"\n                type=\"textarea\"\n                rows=\"2\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\" v-show=\"!jgShow\">\n        <el-button @click=\"resetForm\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"addJg\" class=\"pmyBtn\"\n          >确 定</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <!-- 设备数据copy -->\n    <el-dialog\n      title=\"设备复制\"\n      :visible.sync=\"isShowCopy\"\n      width=\"50%\"\n      v-dialogDrag\n    >\n      <el-form\n        label-width=\"130px\"\n        ref=\"formCopy\"\n        :model=\"formCopy\"\n        :rules=\"rulesCopy\"\n      >\n        <div>\n          <!--基本信息-->\n          <el-row>\n            <el-col :span=\"12\">\n              <el-form-item label=\"分公司：\" prop=\"ssdwbm\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formCopy.ssdwbm\"\n                  @change=\"handleFgsChange\"\n                  placeholder=\"请选择分公司\"\n                >\n                  <el-option\n                    v-for=\"item in OrganizationSelectedList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item label=\"光伏电站\" prop=\"bdz\">\n                <el-select\n                  style=\"width:100%\"\n                  v-model=\"formCopy.bdz\"\n                  @change=\"getSbDataListGroup\"\n                  placeholder=\"请选择光伏电站\"\n                  filterable\n                >\n                  <el-option\n                    v-for=\"item in wzDataListOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item label=\"所属间隔\" prop=\"ssjg\">\n                <el-select\n                  style=\"width:100%\"\n                  v-model=\"formCopy.ssjg\"\n                  placeholder=\"请选择所属间隔\"\n                  filterable\n                >\n                  <el-option\n                    v-for=\"(item, i) in sbDataList\"\n                    :key=\"item.i\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <p>\n            <span style=\"color: red;font-size:23px\">*</span>\n            <span style=\"color: red;font-size:23px\"\n              >功能描述:将该列表中的所有设备 复制一份到 表单选择的间隔下</span\n            >\n          </p>\n          <div align=\"right\" slot=\"footer\">\n            <el-button @click=\"isShowCopy = false\">取 消</el-button>\n            <el-button type=\"primary\" @click=\"copyForAsset\">复 制 </el-button>\n          </div>\n        </div>\n      </el-form>\n    </el-dialog>\n    <import-file\n      ref=\"importExcel\"\n      :export-url=\"importExcelUrl\"\n      :params=\"importExtraInfo\"\n      :valid-string=\"fileName\"\n      @getDataAfterUploading=\"getUploadData\"\n    ></import-file>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport {\n  addAsset,\n  addBdz,\n  addJg,\n  getFgsByBdzId,\n  getJgInfoList,\n  getNewTreeInfo,\n  getOrganizationSelected,\n  getTreeInfo,\n  removeAsset,\n  removeBdz,\n  removeJg,\n  adddwzyfstz,\n  exportExcel,\n  copyAsset\n} from \"@/api/dagangOilfield/asset/gfjgtz\";\nimport {\n  getBdAsesetListPage,\n  getBdzDataListSelected,\n  getJgDataListSelected,\n  getSbxhList,\n  getSblxDataListSelected,\n  addSbxh\n} from \"@/api/dagangOilfield/asset/gfsbtz\";\nimport { getBdzList } from \"@/api/dagangOilfield/asset/gfztz\";\nimport { deleteById, getListByBusinessId } from \"@/api/tool/file\";\nimport {\n  getParamDataList,\n  getParamsValue\n} from \"@/api/dagangOilfield/asset/parameters\";\nimport { getResumDataList } from \"@/api/dagangOilfield/asset/sdsb\";\nimport { getUsers } from \"@/api/system/usergroup\";\nimport { getDictTypeData } from \"@/api/system/dict/data\";\nimport Fsss from \"./fsss.vue\";\nimport { getData, getQtwtlrData } from \"@/api/blgk/blgk\";\nimport { getSybgjlDataByPage } from \"@/api/dagangOilfield/bzgl/sybglr\";\nimport { getListFirst } from \"@/api/yxgl/bdyxgl/qxgl\";\nimport {\n  getFgsOptions,\n  getSelectOptionsByOrgType,\n  getListFour,\n  getListSecond,\n  getListSeven\n} from \"@/api/yxgl/gfyxgl/gfzbgl\";\nimport importFile from \"@/components/ExportExcel/importExcel\";\n\nexport default {\n  name: \"gftz\",\n  components: { Fsss, importFile },\n  data() {\n    return {\n      wzDataListOptions: [],\n      sbDataList: [],\n      formCopy: {},\n      isShowCopy: false,\n      loading: false,\n      icons: {\n        bdzList: \"categoryTreeIcons\",\n        bdz: \"tableIcon\",\n        jg: \"classIcon\",\n        jgdl: \"classIcon\" //间隔大类\n      },\n\n      //新增设备时光伏电站下拉框\n      bdzOptionsDataList: [],\n      //间隔类型下拉数据\n      jglxOptionsDataList: [],\n      //树结构监听属性\n      filterText: \"\",\n      //组织结构下拉数据\n      OrganizationSelectedList: [],\n      //树结构上面得筛选框参数\n      treeForm: {},\n      //电压等级下拉框数据\n      VoltageLevelSelectedList: [],\n      //间隔特殊电压等级\n      jgDydOptions: [],\n      //带字母的电压等级\n      dydjOptionsWithString: [],\n      //新增设备时设备状态下拉框数据\n      sbztOptionsDataList: [],\n      //新增设备下拉框数据\n      placeOptions: [],\n      xsOptions: [],\n      xbOptions: [],\n      singleClickData: undefined,\n      //间隔信息是否显示\n      jgShow: false,\n      filterInfo: {},\n      //通用列表参数\n      tableAndPageInfo: {},\n      jgQueryParams: {\n        ssbdz: undefined,\n        dydj: undefined,\n        pageSize: 10,\n        pageNum: 1\n      },\n      jgxxForm: {\n        objId: undefined,\n        wzbm: undefined,\n        wzid: undefined,\n        sccj: undefined,\n        jgmc: undefined,\n        tyrq: undefined,\n        jglx: undefined,\n        dydj: undefined,\n        zt: undefined,\n        ccrq: undefined,\n        ggxh: undefined,\n        jd: undefined,\n        wd: undefined,\n        ssdd: undefined,\n        ssbdz: undefined,\n        bdzmc: undefined,\n        bz: undefined\n      },\n      //间隔展示\n      jgShowTable: false,\n      //间隔添加按钮弹出框\n      jgDialogFormVisible: false,\n      //弹出框表单\n      form: {},\n      pickerOptions: {\n        shortcuts: [\n          {\n            text: \"今天\",\n            onClick(picker) {\n              picker.$emit(\"pick\", new Date());\n            }\n          },\n          {\n            text: \"昨天\",\n            onClick(picker) {\n              const date = new Date();\n              date.setTime(date.getTime() - 3600 * 1000 * 24);\n              picker.$emit(\"pick\", date);\n            }\n          },\n          {\n            text: \"明天\",\n            onClick(picker) {\n              const date = new Date();\n              date.setTime(date.getTime() + 3600 * 1000 * 24);\n              picker.$emit(\"pick\", date);\n            }\n          }\n        ]\n      },\n      //组织树\n      treeOptions: [],\n      //删除是否可用\n      multipleSensor: true,\n      //查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        roleKey: \"\",\n        roleName: \"\",\n        status: \"\"\n      },\n      showSearch: true,\n      rules: {\n        ssdwbm: [{ required: true, message: \"请选择\", trigger: \"change\" }],\n        // bdzszbh: [{ required: true, message: \"请填写\", trigger: \"blur\" }],\n        bdzmc: [{ required: true, message: \"请填写\", trigger: \"blur\" }],\n        bwd: [{ required: true, message: \"请填写\", trigger: \"blur\" }],\n        dydjbm: [{ required: true, message: \"请选择\", trigger: \"blur\" }],\n        bwz: [{ required: true, message: \"请填写\", trigger: \"blur\" }],\n        tyrq: [{ required: true, message: \"请选择\", trigger: \"blur\" }],\n        // jlcrl: [{ required: true, message: \"请填写\", trigger: \"blur\" }],\n        // zlcrl: [{ required: true, message: \"请填写\", trigger: \"blur\" }],\n        // bdzdz: [{ required: true, message: \"请填写\", trigger: \"blur\" }],\n        // dzlx: [{ required: true, message: \"请选择\", trigger: \"blur\" }],\n        // bzfs: [{ required: true, message: \"请选择\", trigger: \"blur\" }],\n\n        jgmc: [{ required: true, message: \"请填写\", trigger: \"blur\" }],\n        ssbdz: [\n          { required: true, message: \"请选择\", trigger: \"change\" }\n        ],\n        dydj: [{ required: true, message: \"请选择\", trigger: \"change\" }]\n      },\n      //新\n      //控制光伏电站表格是否展示\n      bdzdataShow: true,\n      importExcelUrl: \"\",\n      fileName: \"\",\n      //控制光伏电站表格是否展示\n      jgdataShow: false,\n      //控制光伏电站表格是否展示\n      znsbdataShow: false,\n      bdzqueryParams: {\n        ssgs: undefined,\n        dydjbm: undefined,\n        ssbdz: undefined,\n        ssjg: undefined,\n        sbmc: undefined,\n        assetTypeCode: undefined,\n        sbzt: undefined,\n        pageNum: 1,\n        pageSize: 10\n      },\n      params: {\n        //bm:undefined,\n        // ssdwbm: undefined,\n        pageNum: 1,\n        pageSize: 10\n      },\n      filterInfo1: {\n        data: {\n          tyrqArr: [],\n          dydj: \"\",\n          bdzmc: \"\"\n        },\n        fieldList: [\n          { label: \"光伏电站名称\", type: \"input\", value: \"bdzmc\" },\n          {\n            label: \"投运日期\",\n            type: \"date\",\n            value: \"tyrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          {\n            label: \"电压等级\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"dydj\",\n            options: []\n          }\n        ]\n      },\n      //光伏电站table数据\n      tableAndPageInfo1: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n          pageResize: \"\"\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"ssdwmc\", label: \"所属单位\", minWidth: \"120\" },\n          { prop: \"bdzmc\", label: \"光伏电站名称\", minWidth: \"180\" },\n          { prop: \"dydj\", label: \"电压等级\", minWidth: \"80\" },\n          { prop: \"jlcrl\", label: \"交流侧容量MW\", minWidth: \"80\" },\n          { prop: \"zlcrl\", label: \"直流侧容量MW\", minWidth: \"80\" },\n          { prop: \"zymj\", label: \"占地面积\", minWidth: \"90\" },\n          { prop: \"bdzdz\", label: \"站址\", minWidth: \"120\" },\n          { prop: \"bwz\", label: \"并网站\", minWidth: \"120\" },\n          { prop: \"bwd\", label: \"并网点\", minWidth: \"120\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"120\" }\n        ]\n      },\n      filterInfo2: {\n        data: {\n          ywdwArr: [],\n          jhnyArr: [],\n          xlArr: \"\",\n          jhlxArr: [],\n          jhztArr: \"\",\n          sfdd: \"\"\n        },\n        fieldList: [\n          { label: \"间隔名称\", type: \"input\", value: \"jgmc\", options: [] },\n          { label: \"间隔类型\", type: \"input\", value: \"jglx\", options: [] },\n          {\n            label: \"电压等级\",\n            type: \"select\",\n            value: \"dydj\",\n            options: []\n          },\n          {\n            label: \"投运日期\",\n            type: \"date\",\n            value: \"tyrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          }\n        ]\n      },\n      //间隔数据\n      tableAndPageInfo2: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n          pageResize: \"\"\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"jgmc\", label: \"间隔名称\", minWidth: \"120\" },\n          { prop: \"jglx\", label: \"间隔类型\", minWidth: \"120\" },\n          { prop: \"dydj\", label: \"电压等级\", minWidth: \"120\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"120\" }\n          /*{\n            fixed: \"right\",\n            prop: 'operation',\n            label: '操作',\n            minWidth: '130px',\n            style: {display: 'block'},\n            operation: [\n              {name: '修改', clickFun: this.updateJg},\n              {name: '详情', clickFun: this.jgDetails},\n            ]\n          },*/\n        ]\n      },\n      filterInfo3: {\n        data: {\n          ywdwArr: [],\n          jhnyArr: [],\n          xlArr: \"\",\n          jhlxArr: [],\n          jhztArr: \"\",\n          sfdd: \"\"\n        },\n        fieldList: [\n          { label: \"设备类型\", type: \"input\", value: \"sblxmc\", options: [] },\n          { label: \"设备名称\", type: \"input\", value: \"sbmc\", options: [] },\n          {\n            label: \"电压等级\",\n            type: \"select\",\n            value: \"dydjName\",\n            options: []\n          },\n          {\n            label: \"设备状态\",\n            type: \"select\",\n            value: \"sbzt\",\n            options: []\n          },\n          {\n            label: \"投运日期\",\n            type: \"date\",\n            value: \"tyrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"型号\", type: \"input\", value: \"ggxh\", options: [] },\n          { label: \"额定电压\", type: \"input\", value: \"eddy\", options: [] },\n          { label: \"额定电流\", type: \"input\", value: \"eddl\", options: [] },\n          { label: \"额定频率\", type: \"input\", value: \"edpl\", options: [] },\n          { label: \"生产厂家\", type: \"input\", value: \"sccj\", options: [] }\n        ]\n      },\n      //站内设备台账\n      tableAndPageInfo3: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n          pageResize: \"\"\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"deptname\", label: \"所属公司\", minWidth: \"120\" },\n          { prop: \"bdzmc\", label: \"光伏电站名称\", minWidth: \"180\" },\n          { prop: \"wzmc\", label: \"所属间隔\", minWidth: \"180\" },\n          { prop: \"sblxmc\", label: \"设备类型\", minWidth: \"180\" },\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"180\" },\n          { prop: \"dydjName\", label: \"电压等级\", minWidth: \"80\" },\n          { prop: \"sbzt\", label: \"设备状态\", minWidth: \"80\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"120\" },\n          { prop: \"ggxh\", label: \"型号\", minWidth: \"120\" },\n          { prop: \"eddy\", label: \"额定电压\", minWidth: \"120\" },\n          { prop: \"eddl\", label: \"额定电流\", minWidth: \"120\" },\n          { prop: \"edpl\", label: \"额定频率\", minWidth: \"120\" },\n          { prop: \"sccj\", label: \"生产厂家\", minWidth: \"120\" }\n          /*{\n            fixed: \"right\",\n            prop: 'operation',\n            label: '操作',\n            minWidth: '120px',\n            style: {display: 'block'},\n            operation: [\n              /!*{name: \"状态变更\", clickFun: this.updateStatus},\n              {name: \"流程查看\", clickFun: this.ztbglcSay},*!/\n              {name: '修改', clickFun: this.updateAsset},\n              {name: '详情', clickFun: this.assetDetails},\n            ]\n          },*/\n        ]\n      },\n      //设备基本信息\n      jbxxForm: {\n        attachment: [],\n        objId: undefined,\n        ssdwmc: undefined,\n        ssdwbm: undefined,\n        sbdm: undefined,\n        ddsbh: undefined,\n        dydj: undefined,\n        tyrq: undefined,\n        jgdy: undefined,\n        xb: undefined,\n        xs: undefined,\n        ccrq: undefined,\n        azwz: undefined,\n        yt: undefined,\n        fzr: undefined,\n        cpdh: undefined,\n        eddy: undefined,\n        edpl: undefined,\n        sbzt: undefined,\n        syhj: undefined,\n        sccj: undefined,\n        zzgj: undefined,\n        zhsblx: undefined,\n        zhsblxmc: undefined,\n        eddl: undefined,\n        yxbh: undefined,\n        ccbh: undefined,\n        bdzmc: undefined,\n        bdzszbh: undefined, //光伏站数字编号\n        ssdw: undefined, //所属电网\n        dzlx: undefined, //电站类型\n        sfzhzdh: undefined, //是否综合自动化站\n        sfszhbdz: undefined, //是否数字化光伏站\n        returnDate: undefined, //退运日期\n        zymj: undefined, //占地面积\n        whdj: undefined, //污秽等级\n        zbfs: undefined, //值班方式\n        sfgqtx: undefined, //是否光纤通讯\n        hb: undefined, //海拔\n        gcbh: undefined, //工程编号\n        sjdw: undefined, //设计单位\n        jldw: undefined, //监理单位\n        zyjb: undefined, // 电站重要级别\n        bzfs: undefined, //布置方式\n        bdzdz: undefined, //光伏站地址\n        jzmj: undefined, // 建筑面积\n        phone: undefined, //联系电话\n        gcmc: undefined, // 工程名称\n        sgdw: undefined, //施工单位\n        dqtz: undefined, //地区特征\n        zgddgxq: undefined, // 最高调度管辖权\n        sfmzn: undefined, // 是否满足n-1\n        sfjrgzxt: undefined, //是否接入故障信息远传系统\n        sfjravc: undefined, //是否接入avc\n        sfjzjk: undefined, //是否集中监控\n        jkzxmc: undefined, //接入得监控中心\n        bz: undefined //备注\n      },\n      //光伏电站添加按钮弹出框\n      bdzDialogFormVisible: false,\n      //标题\n      title: \"\",\n      imgList: [],\n      currUser: \"\",\n      //光伏电站信息是否可编辑\n      isDisabled: false,\n      //上传图片时的请求头\n      header: {},\n      //所属基地站\n      ssjdzList: [],\n      uploadData: {\n        type: \"\",\n        businessId: undefined\n      },\n      importExtraInfo: {},\n      //光伏电站新增弹框\n      bdzDidalogForm: false,\n      paramQuery: {\n        sblxbm: undefined\n      },\n      resumeQuery: {\n        foreignNum: undefined,\n        sblx: undefined\n      },\n      znsbDialogForm: false,\n      assetSubmitLoading: false,\n      //设备表单\n      sbxxForm: {},\n      //设备信息展示\n      assetIsDisable: false,\n      //弹出框tab页\n      activeTabName: \"sbDesc\",\n      //查询间隔下拉框数据的参数\n      selectJgOptionsParam: {},\n      //新增设备时所属间隔下拉框列表\n      jgOptionsDataList: [],\n      //设备类型下拉框数据\n      sblxOptionsDataSelected: [],\n      //技术参数绑定\n      jscsForm: {},\n      //技术参数动态展示集合\n      jscsLabelList: [],\n      //设备履历tab页\n      sbllDescTabName: \"ztbgjl\",\n      //设备履历试验记录数据\n      sblvsyjlList: [],\n      //设备履历缺陷记录数据集合\n      sbllqxjlList: [],\n      resumPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: false,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"foreignNum\", label: \"设备名称\", minWidth: \"120\" },\n          // { prop: \"sblx\", label: \"设备类型\", minWidth: \"180\" },\n          { prop: \"bglx\", label: \"变更类型\", minWidth: \"120\" },\n          { prop: \"ms\", label: \"描述\", minWidth: \"250\" },\n          { prop: \"bgr\", label: \"变更人\", minWidth: \"140\" },\n          { prop: \"bgsj\", label: \"变更时间\", minWidth: \"140\" }\n        ]\n      },\n      //设备附属设施list\n      fsssList: [],\n      //设备详情校验规则\n      sbxxRules: {\n        ssgs: [\n          { required: true, message: \"所属公司不能为空\", trigger: \"select\" }\n        ],\n        ssbdz: [\n          { required: true, message: \"所属电站不能为空\", trigger: \"select\" }\n        ],\n        ssjg: [\n          { required: true, message: \"所属间隔不能为空\", trigger: \"select\" }\n        ],\n        sbmc: [\n          { required: true, message: \"设备名称不能为空\", trigger: \"blur\" }\n        ],\n        assetTypeCode: [\n          { required: true, message: \"设备类型不能为空\", trigger: \"select\" }\n        ],\n        // dydjbm: [\n        //   {required: true, message: \"电压等级不能为空\", trigger: \"select\"},\n        // ],\n        sbzt: [\n          { required: true, message: \"设备状态不能为空\", trigger: \"select\" }\n        ],\n        // azwz: [\n        //   {required: true, message: \"安装位置不能为空\", trigger: \"blur\"},\n        // ],\n        // xh: [\n        //   {required: true, message: \"型号不能为空\", trigger: \"select\"},\n        // ],\n        // eddy: [\n        //   {required: true, message: \"额定电压不能为空\", trigger: \"blur\"},\n        // ],\n        // edpl: [\n        //   {required: true, message: \"额定频率不能为空\", trigger: \"blur\"},\n        // ],\n        syhj: [\n          { required: true, message: \"使用环境不能为空\", trigger: \"blur\" }\n        ],\n        sccj: [\n          { required: true, message: \"生产厂家不能为空\", trigger: \"blur\" }\n        ],\n        // yxbh: [\n        //   {required: true, message: \"运行编号不能为空\", trigger: \"blur\"},\n        // ],\n        // ccbh: [\n        //   {required: true, message: \"出厂编号不能为空\", trigger: \"blur\"},\n        // ],\n        // ccrq: [\n        //   {required: true, message: \"出厂日期不能为空\", trigger: \"change\"},\n        // ],\n        tyrq: [\n          { required: true, message: \"投运日期不能为空\", trigger: \"change\" }\n        ]\n      },\n      rulesCopy: {\n        ssdwbm: [\n          { required: true, message: \"所属公司不能为空\", trigger: \"select\" }\n        ],\n        bdz: [\n          { required: true, message: \"所属电站不能为空\", trigger: \"select\" }\n        ],\n        ssjg: [\n          { required: true, message: \"所属间隔不能为空\", trigger: \"select\" }\n        ]\n      },\n      ggxhList: [], //型号list\n      formattedResults:[],\n      jglxcx: \"\", //所属间隔类型\n      ssbdz: \"\", //所属光伏电站,\n      ssbdzmc: \"\",\n      ssjg: \"\", //所属间隔\n      znsbParams: {\n        //站内设备分页参数\n        pageSize: 10,\n        pageNum: 1\n      },\n      fgsArr: [],\n      jgdlMap: new Map(), //存间隔大类数据\n      currentBdz: \"\", //当前光伏电站\n      showFsss: false, //是否显示附属设施tab页\n      bdsbid: \"\", //光伏设备ID\n      sbmc: \"\", //设备名称\n      //不良工况列表\n      blgkPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: { checkBox: false, serialNumber: true },\n        tableData: [],\n        tableHeader: [\n          // { prop: \"fgsmc\", label: \"分公司\", minWidth: \"140\" },\n          // { prop: \"bdzmc\", label: \"光伏电站\", minWidth: \"140\" },\n          // { prop: \"jgmc\", label: \"间隔名称\", minWidth: \"100\" },\n          // { prop: \"sblxmc\", label: \"设备类型\", minWidth: \"140\" },\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"140\" },\n          { prop: \"ms\", label: \"不良工况描述\", minWidth: \"160\", showPop: true },\n          { prop: \"flyjCn\", label: \"分类依据\", minWidth: \"160\", showPop: true },\n          { prop: \"lrrmc\", label: \"录入人\", minWidth: \"100\" },\n          { prop: \"fxsj\", label: \"发现时间\", minWidth: \"100\" },\n          { prop: \"xcsj\", label: \"消除时间\", minWidth: \"140\" }\n        ]\n      },\n\n      //其它设备问题\n      qtsbwtPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: { checkBox: false, serialNumber: true },\n        tableData: [],\n        tableHeader: [\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"140\" },\n          { prop: \"ms\", label: \"问题描述\", minWidth: \"160\", showPop: true },\n          { prop: \"lrrmc\", label: \"录入人\", minWidth: \"100\" },\n          { prop: \"fxsj\", label: \"发现时间\", minWidth: \"100\" }\n        ]\n      },\n\n      //试验列表\n      syPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 0]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"试验专业\", prop: \"syzy\" },\n          { label: \"试验性质\", prop: \"syxz\" },\n          { label: \"试验名称\", prop: \"symc\", minWidth: \"200\", showPop: true },\n          // { label: \"设备地点\", prop: \"sydd\", minWidth: \"120\" },\n          { label: \"试验设备\", prop: \"sbmc\", minWidth: \"100\", showPop: true },\n          { label: \"试验模板名称\", prop: \"symb\", minWidth: \"120\" },\n          { label: \"天气\", prop: \"tq\" },\n          { label: \"试验日期\", prop: \"syrq\" },\n          { label: \"试验人员\", prop: \"syryid\" },\n          { label: \"流程状态\", prop: \"ztmc\" }\n        ],\n        option: { checkBox: false, serialNumber: true }\n      },\n      //隐患列表\n      yhPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          pageResize: \"\",\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: false,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          // { prop: \"ssgs\", label: \"所属公司\", minWidth: \"120\" },\n          // { prop: \"ssdz\", label: \"所属位置\", minWidth: \"120\" },\n          { prop: \"sb\", label: \"主设备\", minWidth: \"120\" },\n          // { prop: \"sblx\", label: \"设备类型\", minWidth: \"100\" },\n          // {prop: 'dydj', label: '电压等级', minWidth: '100'},\n          { prop: \"sbxh\", label: \"设备型号\", minWidth: \"120\" },\n          { prop: \"sccj\", label: \"生产厂家\", minWidth: \"120\" },\n          { prop: \"bzqxQxdj\", label: \"隐患性质\", minWidth: \"120\" },\n          { prop: \"qxnr\", label: \"隐患内容\", minWidth: \"120\", showPop: true },\n          { prop: \"jxlbCn\", label: \"是否触发状态评价\", minWidth: \"80\" },\n          { prop: \"ztmc\", label: \"状态\", minWidth: \"120\" },\n          { prop: \"fxrq\", label: \"发现时间\", minWidth: \"100\", custom: true }\n        ]\n      },\n      //检修列表\n      jxPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          pageResize: \"\",\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: false,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          // { prop: \"fgsmc\", label: \"分公司\", minWidth: \"150\" },\n          // { prop: \"bdzmc\", label: \"光伏电站\", minWidth: \"150\" },\n          // { prop: \"ssjg\", label: \"所属间隔\", minWidth: \"150\" },\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"100\" },\n          { prop: \"rq\", label: \"日期\", minWidth: \"120\" },\n          { prop: \"xslb\", label: \"修试类别\", minWidth: \"100\" },\n          { prop: \"nr\", label: \"内容\", minWidth: \"100\" },\n          { prop: \"jl\", label: \"结论\", minWidth: \"80\" },\n          { prop: \"xsfzr\", label: \"修试负责人\", minWidth: \"90\" },\n          { prop: \"ysfzr\", label: \"验收负责人\", minWidth: \"90\" },\n          { prop: \"jlr\", label: \"记录人\", minWidth: \"90\" }\n        ]\n      },\n      //继电保护列表\n      jdbhPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          pageResize: \"\",\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: false,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"rq\", label: \"日期\", minWidth: \"80\" },\n          { prop: \"nr\", label: \"内容\", minWidth: \"180\" },\n          { prop: \"jl\", label: \"结论\", minWidth: \"80\" },\n          { prop: \"sygzfzr\", label: \"试验工作负责人\", minWidth: \"110\" },\n          { prop: \"ysfzr\", label: \"验收负责人\", minWidth: \"100\" },\n          { prop: \"jlr\", label: \"记录人\", minWidth: \"100\" }\n        ]\n      },\n      //跳闸记录列表\n      tzjlPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          pageResize: \"\",\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: false,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"rqsj\", label: \"日期时间\", minWidth: \"160\" },\n          {\n            prop: \"bhdzqk\",\n            label: \"保护动作情况\",\n            minWidth: \"180\",\n            isShowProp: true\n          },\n          { prop: \"dlqjcqk\", label: \"断路器检查情况\", minWidth: \"120\" },\n          { prop: \"gzdl\", label: \"故障电流\", minWidth: \"100\" },\n          { prop: \"gztzcs\", label: \"故障跳闸次数\", minWidth: \"100\" },\n          { prop: \"jltzjl\", label: \"累计跳闸次数\", minWidth: \"100\" },\n          { prop: \"zhdxrq\", label: \"最后大修日期\", minWidth: \"120\" },\n          { prop: \"jlr\", label: \"记录人\", minWidth: \"120\" }\n        ]\n      },\n      yhloading: false,\n      syloading: false,\n      blgkloading: false,\n      qtsbwtloading: false,\n      jxloading: false,\n      jdbhLoading: false,\n      tzjlLoading: false,\n      isSelectingFromDropdown: false, // 添加标记变量\n    };\n  },\n  watch: {\n    //监听筛选框值发生变化进而筛选树结构\n    filterText(val) {\n      this.$refs.tree.filter(val);\n    }\n  },\n  async created() {\n    //获取组织结构下拉数据\n    this.getFgsOptions();\n    // this.getJdzOptions();\n    //获取光伏电站下拉框数据\n    this.getBdzDataListSelected();\n    //获取选择框数据\n    this.getSelectDataInfo();\n    //获取新的设备拓扑树\n    await this.getNewTreeInfo();\n    this.isShow1 = true;\n    //初始化加载时加载所有光伏电站信息\n    await this.getData();\n    await this.getJglxList();\n    //初始化时加载页面内容\n    this.tableAndPageInfo = { ...this.tableAndPageInfo1 };\n  },\n  mounted() {\n    //获取token\n    this.header.token = getToken();\n    this.currUser = this.$store.getters.name;\n  },\n  methods: {\n    //所属公司change事件\n    handleFgsChange(fgsValue) {\n      //清空之前得选中值\n      this.wzDataListOptions = [];\n      this.$set(this.formCopy, \"bdz\", \"\");\n      //获取光伏电站方法\n      getBdzDataListSelected({ ssdwbm: fgsValue }).then(res => {\n        this.wzDataListOptions = res.data;\n      });\n    },\n    async getSbDataListGroup(val) {\n      this.$set(this.formCopy, \"ssjg\", \"\");\n      let res = await getJgDataListSelected({ ssbdz: val + \"\" });\n      if (res.code === \"0000\") {\n        this.sbDataList = res.data;\n      } else {\n        this.$message({\n          type: \"error\",\n          message: \"间隔数据获取失败!\"\n        });\n      }\n    },\n    xsChangeFunc(val) {\n      if (val === \"三相\") {\n        this.$set(this.sbxxForm, \"xb\", \"ABC\");\n      } else {\n        this.$set(this.sbxxForm, \"xb\", \"\");\n      }\n    },\n    /**\n     * 获取基地站下拉数据\n     */\n    getJdzOptions() {\n      getSelectOptionsByOrgType(JSON.stringify(\"07\")).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.ssjdzList = res.data;\n      });\n    },\n    //查询检修数据\n    getJxList(params) {\n      this.jxloading = true;\n      const param = {\n        ...{ pageSize: 10, pageNum: 1 },\n        ...params,\n        ...{ sbmc: this.sbmc, bdz: this.ssbdz },\n        ...{ mySorts: [{ prop: \"rq\", asc: false }] }\n      };\n      getListFour(param).then(res => {\n        this.jxPageInfo.tableData = res.data.records;\n        this.jxPageInfo.pager.total = res.data.total;\n        this.jxloading = false;\n      });\n    },\n    getJdbhList(params) {\n      this.jdbhLoading = true;\n      const param = {\n        ...{ pageSize: 10, pageNum: 1 },\n        ...params,\n        ...{ sbmc: this.sbmc, bdz: this.ssbdz },\n        ...{ mySorts: [{ prop: \"rq\", asc: false }] }\n      };\n      getListSecond(param).then(res => {\n        this.jdbhPageInfo.tableData = res.data.records;\n        this.jdbhPageInfo.pager.total = res.data.total;\n        this.jdbhLoading = false;\n      });\n    },\n    getTzjlList(params) {\n      this.tzjlLoading = true;\n      const param = {\n        ...{ pageSize: 10, pageNum: 1 },\n        ...params,\n        ...{ dlqbh: this.sbmc, bdz: this.ssbdz },\n        ...{ mySorts: [{ prop: \"rqsj\", asc: false }] }\n      };\n      getListSeven(param).then(res => {\n        this.tzjlPageInfo.tableData = res.data.records;\n        this.tzjlPageInfo.pager.total = res.data.total;\n        this.tzjlLoading = false;\n      });\n    },\n    //查询不良工况数据\n    getBlgkList(params) {\n      this.blgkloading = true;\n      const param = {\n        ...{ pageSize: 10, pageNum: 1 },\n        ...params,\n        ...{ sbid: this.bdsbid }\n      };\n      getData(param).then(res => {\n        this.blgkPageInfo.tableData = res.data.records;\n        this.blgkPageInfo.pager.total = res.data.total;\n        this.blgkloading = false;\n      });\n    },\n\n    //其它设备录入问题\n    getqtsbwtList(params) {\n      this.qtsbwtloading = true;\n      const param = {\n        ...{ pageSize: 10, pageNum: 1 },\n        ...params,\n        ...{ sbid: this.bdsbid }\n      };\n      getQtwtlrData(param).then(res => {\n        this.qtsbwtPageInfo.tableData = res.data.records;\n        this.qtsbwtPageInfo.pager.total = res.data.total;\n        this.qtsbwtloading = false;\n      });\n    },\n\n    //试验数据\n    getSyList(params) {\n      this.syloading = true;\n      const param = {\n        ...{ pageSize: 10, pageNum: 1 },\n        ...params,\n        ...{ sysbid: this.bdsbid }\n      };\n      getSybgjlDataByPage(param).then(res => {\n        if (res.code === \"0000\") {\n          this.syPageInfo.tableData = res.data.records;\n          this.syPageInfo.pager.total = res.data.total;\n        }\n        this.syloading = false;\n      });\n    },\n    //隐患数据\n    getYhList(params) {\n      this.yhloading = true;\n      const param = {\n        ...{ pageSize: 10, pageNum: 1 },\n        ...params,\n        ...{ sbid: this.bdsbid }\n      };\n      getListFirst(param).then(res => {\n        if (res.code === \"0000\") {\n          this.yhPageInfo.tableData = res.data.records;\n          this.yhPageInfo.pager.total = res.data.total;\n        }\n        this.yhloading = false;\n      });\n    },\n    //获取间隔类型字典\n    getJglxList() {\n      getDictTypeData(\"dwzy_jglx\").then(res => {\n        this.jglxOptionsDataList = res.data;\n        res.data.forEach(item => {\n          if (!this.jgdlMap.has(item.value)) {\n            this.jgdlMap.set(item.value, item.remark);\n          }\n        });\n      });\n    },\n    getSelectDataInfo() {\n      //110 不带字母\n      getDictTypeData(\"dg_dydj\").then(res => {\n        this.VoltageLevelSelectedList = res.data;\n      });\n      //间隔特殊等级\n      getDictTypeData(\"jgtz_gjDydj\").then(res => {\n        this.jgDydOptions = res.data;\n        this.filterInfo2.fieldList.map(item => {\n          if (item.value == \"dydj\") {\n            return (item.options = this.jgDydOptions);\n          }\n        });\n      });\n      // 110kV 带字母\n      getDictTypeData(\"dg_dydj\").then(res => {\n        this.dydjOptionsWithString = res.data;\n        this.filterInfo1.fieldList.map(item => {\n          if (item.value == \"dydj\") {\n            return (item.options = this.dydjOptionsWithString);\n          }\n        });\n\n        this.filterInfo3.fieldList.map(item => {\n          if (item.value == \"dydjName\") {\n            return (item.options = this.dydjOptionsWithString);\n          }\n        });\n      });\n      //设备状态\n      getDictTypeData(\"jgtz_sbzt\").then(res => {\n        this.sbztOptionsDataList = res.data;\n        this.filterInfo1.fieldList.map(item => {\n          if (item.value == \"sbzt\") {\n            return (item.options = this.sbztOptionsDataList);\n          }\n        });\n        this.filterInfo3.fieldList.map(item => {\n          if (item.value == \"sbzt\") {\n            return (item.options = this.sbztOptionsDataList);\n          }\n        });\n      });\n      //安装位置\n      getDictTypeData(\"jgtz_azwz\").then(res => {\n        this.placeOptions = res.data;\n      });\n      //相别\n      getDictTypeData(\"jgtz_xb\").then(res => {\n        this.xbOptions = res.data;\n      });\n      //相数\n      getDictTypeData(\"jgtz_xs\").then(res => {\n        this.xsOptions = res.data;\n      });\n    },\n    // 新增信息所属电站要自动带入\n    fillBdz() {\n      this.$set(this.jgxxForm, \"ssbdz\", this.currentBdz);\n    },\n    async deleteFileById(id) {\n      let { code } = await deleteById(id);\n      if (code === \"0000\") {\n        await this.getFileList();\n        this.$message({\n          type: \"success\",\n          message: \"文件删除成功!\"\n        });\n      }\n    },\n    //树监听事件\n    filterNode(value, data) {\n      if (!value) return true;\n      return data.label.indexOf(value) !== -1;\n    },\n    //获取新的设备拓扑树\n    getNewTreeInfo() {\n      getUsers({ personGroupId: 76, deptId: 0, deptName: \"\" }).then(res => {\n        let deptId = this.$store.getters.deptId.toString();\n        res.data.forEach(item => {\n          if (item.userName === this.currUser) {\n            //如果人员组里面有需要排除的人，则不需要用deptId进行过滤\n            deptId = \"\";\n            return false;\n          }\n        });\n        if (this.fgsArr.includes(deptId)) {\n          this.treeForm.ssdwbm = deptId;\n        }\n        getNewTreeInfo(this.treeForm).then(res => {\n          this.treeOptions = res.data;\n        });\n      });\n    },\n    /**\n     * 获取分公司下拉数据\n     */\n    getFgsOptions() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n          this.fgsArr.push(item.value.toString());\n        });\n        this.OrganizationSelectedList = res.data;\n        this.filterInfo1.fieldList.map(item => {\n          if (item.value == \"ssdwbm\") {\n            return (item.options = this.OrganizationSelectedList);\n          }\n        });\n      });\n    },\n    //旧树形数据获取\n    getTreeInfoList() {\n      getTreeInfo().then(res => {\n        this.treeOptions = res.data;\n      });\n    },\n    //列表查询\n    async getData(param) {\n      this.loading = true;\n      this.params = param;\n      await getSblxDataListSelected({}).then(res => {\n        this.sblxOptionsDataSelected = res.data;\n        this.loading = false;\n      });\n      //判断翻页是执行的哪个表格的数据\n      if (this.bdzdataShow) {\n        //初始进来请求光伏电站台账\n        await this.getbdzData(param);\n      }\n      if (this.jgdataShow) {\n        await this.getJgData(param);\n      }\n      if (this.znsbdataShow) {\n        await this.getZnsbData(param);\n      }\n    },\n    /**\n     * 表格多选框\n     */\n    handleSelectionChange(selection) {\n      // this.ids = selection.map(item => item.objId);\n      // this.single = selection.length !== 1;\n      this.singleClickData =\n        selection.length > 0 ? { ...selection[0] } : undefined;\n      // this.multiple = !selection.length;\n    },\n\n    //间隔添加按钮\n    jgAddjgButton() {\n      this.jgShow = false;\n      this.jgDialogFormVisible = true;\n    },\n    addJg() {\n      this.$refs[\"jgxxForm\"].validate(valid => {\n        if (valid) {\n          let jglx = this.jgxxForm.jglx;\n          if (jglx) {\n            //保存时设置间隔大类\n            this.jgxxForm.jgdl = this.jgdlMap.get(jglx);\n          }\n          addJg(this.jgxxForm).then(res => {\n            if (res.code == \"0000\") {\n              this.$message.success(\"操作成功！\");\n              this.jgDialogFormVisible = false;\n              // this.tableAndPageInfo1.pager.pageResize = \"Y\";\n              this.getJgData();\n              //获取新的设备拓扑树\n              this.getNewTreeInfo();\n            }\n          });\n        } else {\n          return false;\n        }\n      });\n    },\n    getJgListInfo() {\n      getJgInfoList(this.jgQueryParams).then(res => {\n        this.tableAndPageInfo1.tableData = res.data.records;\n        this.tableAndPageInfo1.pager.total = res.data.total;\n        // this.tableAndPageInfo = {...this.tableAndPageInfo2}\n      });\n    },\n    /**\n     * 删除间隔\n     */\n    removeAll(row) {\n      if (this.bdzdataShow) {\n        this.deleteBdz(row);\n      }\n      if (this.jgdataShow) {\n        this.removeAsset(row);\n      }\n      if (this.znsbdataShow) {\n        this.deleteJg(row);\n      }\n    },\n    removeAsset(row) {\n      this.form = row;\n\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          removeAsset([this.form.objId]).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getJgData();\n              //获取新的设备拓扑树\n              this.getNewTreeInfo();\n              // this.tableAndPageInfo3.pager.pageResize = \"Y\";\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    deleteBdz(row) {\n      this.form = row;\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(() => {\n        removeBdz([this.form.objId]).then(res => {\n          this.$message({\n            type: \"success\",\n            message: \"删除成功!\"\n          });\n          // this.tableAndPageInfo1.pager.pageResize = \"Y\";\n          this.getbdzData();\n        });\n      });\n    },\n    deleteJg(row) {\n      this.form = row;\n\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      }).then(() => {\n        removeJg([this.form.objId]).then(res => {\n          this.$message({\n            type: \"success\",\n            message: \"删除成功!\"\n          });\n          // this.tableAndPageInfo2.pager.pageResize = \"Y\";\n          this.getZnsbData();\n        });\n      });\n    },\n    async updateJg(row) {\n      this.jgDialogFormVisible = true;\n      this.jgxxForm = { ...row };\n      this.jgShow = false;\n    },\n\n    async jgDetails(row) {\n      this.jgDialogFormVisible = true;\n      this.jgxxForm = { ...row };\n      this.jgShow = true;\n    },\n\n    //每页展示数量点击事件\n    handleSizeChange() {},\n    //页码改变事件\n    handleCurrentChange() {},\n    //树点击事件\n    handleNodeClick(data, e) {\n      //根目录光伏电站列表\n      if (data.identifier == \"0\") {\n        //点击根节点，获取光伏电站数据\n        //间隔\n        this.isShow2 = false;\n        //重置pageNum\n        this.bdzqueryParams = {\n          pageNum: 1,\n          pageSize: 10\n        };\n        this.$refs.bdzTable.currentPage = 1;\n        //获取列表数据\n        this.getbdzData();\n        this.bdzdataShow = true;\n        this.znsbdataShow = this.jgdataShow = false;\n      }\n      //二级目录光伏电站名称\n      else if (data.identifier == \"1\") {\n        //点击光伏电站，获取间隔数据\n        this.currentBdz = data.id;\n        this.ssbdzmc = data.label;\n        this.ssbdz = data.id;\n        //重置页码\n        this.$refs.jgTable.currentPage = 1;\n        this.jgQueryParams.pageNum = 1;\n        this.jgQueryParams.jgdl = \"\"; //清空间隔大类\n        this.getFgsByBdzId();\n        this.getJgData({ ssbdz: data.id });\n        this.jgdataShow = true;\n        this.znsbdataShow = this.bdzdataShow = false;\n      } //二级目录光伏电站名称\n      else if (data.identifier == \"2\") {\n        //点击间隔大类，过滤间隔数据\n        //重新设置所属电站\n        this.currentBdz = data.ssbdz;\n        this.ssbdz = data.ssbdz;\n        //重置页码\n        this.$refs.jgTable.currentPage = 1;\n        this.jgQueryParams.pageNum = 1;\n        this.jgQueryParams.jgdl = data.id;\n        this.jglxcx = data.id;\n        this.getFgsByBdzId();\n        this.getJgData({ ssbdz: this.ssbdz, jgdl: data.id });\n        this.jgdataShow = true;\n        this.znsbdataShow = this.bdzdataShow = false;\n      } else if (data.identifier == \"3\") {\n        //点击间隔，获取站内设备\n        this.jglxcx = data.jglx;\n        this.sbxxForm.ssbdz = this.ssbdz;\n        //重置页码\n        this.$refs.znsbTable.currentPage = 1;\n        this.znsbParams.pageNum = 1;\n        this.bdzOptionsChangeClick();\n        this.ssjg = data.id;\n        this.sbxxForm.ssjg = this.ssjg;\n        //间隔\n        this.bdzqueryParams.ssbdz = \"\";\n        this.bdzqueryParams.ssjg = data.id;\n        this.getZnsbData();\n        this.znsbdataShow = true;\n        this.jgdataShow = this.bdzdataShow = false;\n      }\n    },\n    async getFgsByBdzId() {\n      let { data, code } = await getFgsByBdzId({ sbdm: this.currentBdz });\n      if (code === \"0000\") {\n        this.ssgs = data.value;\n        this.sbxxForm.ssgs = data.value;\n      }\n    },\n    //请求光伏电站数据\n    async getbdzData(param) {\n      this.bdzqueryParams = { ...this.bdzqueryParams, ...param };\n      const par = { ...this.bdzqueryParams, ...param };\n      if (this.treeForm.ssdwbm) {\n        par.ssdwbm = this.treeForm.ssdwbm;\n      }\n      await getBdzList(par).then(res => {\n        this.bdzdataShow = true;\n        this.jgdataShow = this.znsbdataShow = false;\n        this.tableAndPageInfo1.tableData = res.data.records;\n        this.tableAndPageInfo1.pager.total = res.data.total;\n      });\n    },\n    //日期格式化  yyyy-MM-dd\n    dateFormatter(d) {\n      let year = d.getFullYear();\n      let month =\n        d.getMonth() < 9 ? \"0\" + (d.getMonth() + 1) : \"\" + (d.getMonth() + 1);\n      let day = d.getDate() < 10 ? \"0\" + d.getDate() : \"\" + d.getDate();\n      return year + \"-\" + month + \"-\" + day;\n    },\n    //请求间隔数据\n    async getJgData(param) {\n      this.jgQueryParams = { ...this.jgQueryParams, ...param };\n      try {\n        let { data, code } = await getJgInfoList(this.jgQueryParams);\n        if (code === \"0000\") {\n          this.jgdataShow = true;\n          this.bdzdataShow = this.znsbdataShow = false;\n          this.tableAndPageInfo2.tableData = data.records;\n          this.tableAndPageInfo2.pager.total = data.total;\n        }\n      } catch (e) {}\n    },\n    //请求站内设备数据\n    async getZnsbData(params) {\n      try {\n        this.znsbParams = { ...this.znsbParams, ...params };\n        const param = this.znsbParams;\n        param.ssjg = this.ssjg;\n        const { data, code } = await getBdAsesetListPage(param);\n        if (code === \"0000\") {\n          this.znsbdataShow = true;\n          this.jgdataShow = this.bdzdataShow = false;\n          this.tableAndPageInfo3.tableData = data.records;\n          this.tableAndPageInfo3.pager.total = data.total;\n          // this.sbxxForm.ssgs=data.records[0].deptname\n          // this.sbxxForm.ssbdz=data.records[0].bdzmc\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //光伏电站弹框开始\n    //光伏电站修改按钮\n    updatebdz(row) {\n      this.$nextTick(async function() {\n        this.clearUpload();\n        this.jbxxForm = { ...row };\n        this.jbxxForm.attachment = [];\n        await this.getFileList();\n        this.isDisabled = false;\n        this.title = \"光伏电站台账修改\";\n        this.bdzDialogFormVisible = true;\n      });\n    },\n    clearUpload() {\n      if (this.$refs.upload) {\n        this.$refs.upload.clearFiles();\n      }\n    },\n    //关闭弹框\n    removeForm() {\n      this.jbxxForm = {\n        attachment: []\n      };\n      this.$nextTick(function() {\n        this.$refs[\"form\"].clearValidate();\n      });\n      this.bdzDialogFormVisible = false;\n    },\n    beforeUpload(file) {\n      const fileSize = file.size < 1024 * 1024 * 50;\n      if (!fileSize) {\n        this.$message.error(\"上传文件大小不能超过 50MB!\");\n      }\n    },\n    async getFileList() {\n      let { code, data } = await getListByBusinessId({\n        businessId: this.jbxxForm.objId\n      });\n      if (code === \"0000\") {\n        this.jbxxForm.attachment = data;\n        this.imgList = data.map(item => {\n          let item1 = {};\n          item1.name = item.fileName;\n          item1.url = this.$store.getters.currHost + item.fileUrl;\n          return item1;\n        });\n      }\n    },\n    //光伏电站详情方法\n    bdzDetails(row) {\n      this.$nextTick(async function() {\n        this.clearUpload();\n        this.jbxxForm = { ...row };\n        let temp = this.jbxxForm.ssdwmc;\n        this.jbxxForm.ssdwmc = this.jbxxForm.ssdwbm;\n        this.jbxxForm.ssdwbm = temp;\n        this.jbxxForm.attachment = [];\n        await this.getFileList();\n        this.bdzDialogFormVisible = true;\n        this.isDisabled = true;\n        this.title = \"光伏电站台账详情\";\n      });\n    },\n    addBdz() {\n      let params = {\n        lx: \"光伏设备\",\n        ssdw: this.jbxxForm.ssdwbm,\n        mc: this.jbxxForm.bdzmc\n      };\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          addBdz(this.jbxxForm).then(res => {\n            if (res.code === \"0000\") {\n              //新增成功后发送通知\n              adddwzyfstz(params).then(res => {\n                if (res.code === \"0000\") {\n                }\n              });\n              this.uploadData.businessId = res.data.objId;\n              this.submitUpload();\n              this.bdzDialogFormVisible = false;\n              this.$message.success(\"操作成功,通知已分发\");\n              // this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getNewTreeInfo();\n              this.getData();\n            } else {\n              this.bdzDialogFormVisible = false;\n            }\n          });\n        } else {\n          return false;\n        }\n      });\n    },\n\n    submitUpload() {\n      this.$refs.upload.submit();\n    },\n    //光伏电站弹框结束\n    //新增按钮\n    AddButton() {\n      this.showFsss = false;\n      if (this.bdzdataShow) {\n        this.clearUpload();\n        this.imgList = [];\n        this.isDisabled = false;\n        this.bdzDialogFormVisible = true;\n        this.title = \"光伏电站台账新增\";\n      }\n      if (this.jgdataShow) {\n        this.jgxxForm = {};\n        this.fillBdz(); //设置间隔所属光伏电站\n        this.jgShow = false;\n        this.jgDialogFormVisible = true;\n      }\n      if (this.znsbdataShow) {\n        this.activeTabName = \"sbDesc\";\n        // this.sbxxForm = {};\n        if (this.singleClickData) {\n          this.sbxxForm = { ...this.singleClickData };\n          this.sbxxForm.objId = undefined;\n        }\n        this.sbxxForm.ssgs = this.ssgs;\n        this.sbxxForm.ssbdz = this.ssbdz;\n        this.sbxxForm.ssjg = this.ssjg;\n        //打开弹出框\n        this.znsbDialogForm = true;\n        //按钮和表单是否可编辑控制\n        this.assetIsDisable = false;\n      }\n    },\n    exportExcel() {\n      let fileName = this.ssbdzmc + \"设备信息表\";\n      let exportUrl = \"/gfsb/exportExcel/assetInfo\";\n      exportExcel(exportUrl, this.jgQueryParams, fileName);\n    },\n    exportExcelOfBdz() {\n      let fileName = \"光伏电站信息表\";\n      let exportUrl = \"/equipListOfGfz/exportExcelOfBdz\";\n      exportExcel(exportUrl, this.jgQueryParams, fileName);\n    },\n    importExcelOfBdz() {\n      this.importExcelUrl = \"/manager-api/equipListOfGfz/importExcelOfBdz\";\n      this.fileName = \"光伏电站信息表\";\n      this.$refs.importExcel.importExcel();\n    },\n    getUploadData(fileName) {\n      switch (fileName) {\n        case \"光伏电站信息表\":\n          this.getbdzData();\n          break;\n        case \"间隔信息表\":\n          this.getJgData();\n          break;\n        case \"设备信息表\":\n          this.getZnsbData();\n          break;\n        default:\n          break;\n      }\n      this.getNewTreeInfo();\n    },\n    exportExcelOfJg() {\n      let fileName = \"间隔信息表\";\n      let exportUrl = \"/equipListOfGfz/exportExcelOfJg\";\n      exportExcel(exportUrl, this.jgQueryParams, fileName);\n    },\n    importExcelOfJg() {\n      this.importExtraInfo.ssbdz = this.currentBdz;\n      this.importExcelUrl = \"/manager-api/equipListOfGfz/importExcelOfJg\";\n      this.fileName = \"间隔信息表\";\n      this.$refs.importExcel.importExcel();\n    },\n    exportExcelOfAsset() {\n      let fileName = \"设备信息表\";\n      let exportUrl = \"/equipListOfGfz/exportExcelOfAsset\";\n      let param = {};\n      param.ssjg = this.ssjg;\n      exportExcel(exportUrl, param, fileName);\n    },\n    importExcelOfAsset() {\n      this.importExtraInfo.ssgs = this.ssgs;\n      this.importExtraInfo.ssbdz = this.ssbdz;\n      this.importExtraInfo.ssjg = this.ssjg;\n      this.importExcelUrl = \"/manager-api/equipListOfGfz/importExcelOfAsset\";\n      this.fileName = \"设备信息表\";\n      this.$refs.importExcel.importExcel();\n    },\n    copyForAsset() {\n      this.$refs[\"formCopy\"].validate(valid => {\n        if (valid) {\n          this.formCopy.sourceSsjg = this.ssjg;\n          copyAsset(this.formCopy).then(res => {\n            if (res.code === \"0000\") {\n              this.$message.success(\"操作成功\");\n              this.isShowCopy = false;\n              this.$refs.formCopy.resetFields();\n            }\n          });\n        } else {\n          return false;\n        }\n      });\n    },\n    //根据设备类型获取设备型号list 数据\n    getSbxhBySblx(sblx) {\n      this.ggxhList = [];\n      getSbxhList({ dysblx: sblx }).then(res => {\n        if (res.code == \"0000\") {\n          // 保证每项都包含label字段\n          this.ggxhList = (res.data || []).map(item => {\n            return {\n              ...item,\n              label: item.label || item.lebel || item.xh || ''\n            };\n          });\n        }\n      });\n    },\n\n    /*站内设备开始*/\n    //设备修改操作\n    async updateAsset(row) {\n      row.xh = row.ggxh; //规格型号赋值\n      this.technicalParameters(row);\n      this.paramQuery.sblxbm = row.assetTypeCode;\n      await this.getParameters();\n      this.resumeQuery.foreignNum = row.sbmc;\n      this.resumeQuery.sblx = row.sblx;\n      this.bdsbid = row.objId;\n      this.sbmc = row.sbmc;\n      this.ssbdz = row.ssbdz;\n      this.getResumList();\n      await this.getSbxhBySblx(row.assetTypeCode); //根据设备类型获取设备型号下拉框\n      //给表单赋值\n      this.sbxxForm = { ...row };\n      //按钮和表单是否可编辑控制\n      this.assetIsDisable = false;\n      this.showFsss = true;\n      //打开设备弹出框\n      this.znsbDialogForm = true;\n    },\n    //点击新增，修改，详情时，重新获取对应的技术参数信息\n    technicalParameters(row) {\n      this.jscsForm = {};\n      this.paramQuery.sblxbm = row.assetTypeCode;\n      this.jscsForm.sblxbm = row.assetTypeCode;\n      this.jscsForm.sbbm = row.objId;\n      this.getParameters();\n    },\n    //获取技术参数对照信息，展示对应得技术参数label信息\n    async getParameters() {\n      this.jscsLabelList = [];\n      getParamDataList(this.paramQuery).then(res => {\n        this.jscsLabelList = res.data;\n        this.getParamValue();\n      });\n    },\n    //获取技术参数值信息\n    getParamValue() {\n      getParamsValue(this.jscsForm).then(res => {\n        if (res.data != \"\") {\n          this.jscsForm = { ...res.data[0] };\n        }\n      });\n    },\n    /**\n     * 设备履历\n     */\n    getResumList(par) {\n      let param = { ...par, ...this.resumeQuery };\n      getResumDataList(param).then(res => {\n        this.resumPageInfo.tableData = res.data.records;\n        this.resumPageInfo.pager.total = res.data.total;\n      });\n    },\n    //光伏电站下拉框中的change事件\n    bdzOptionsChangeClick() {\n      //当发生change事件时先清空之前的间隔信息\n      this.$set(this.sbxxForm, \"ssjg\", \"\");\n      //调用查询间隔方法\n      this.getJgDataListSelected();\n    },\n    //获取间隔下拉框数据\n    getJgDataListSelected() {\n      //给获取间隔下拉框查询参数赋值\n      this.selectJgOptionsParam.ssbdz = this.sbxxForm.ssbdz;\n      getJgDataListSelected(this.selectJgOptionsParam).then(res => {\n        this.jgOptionsDataList = res.data;\n      });\n    },\n    //设备类型change事件。获取技术参数信息\n    async showParams(data) {\n      this.paramQuery.sblxbm = data;\n      await this.getParameters();\n      await this.getSbxhBySblx(data); //根据设备类型获取设备型号下拉框\n    },\n    //设备履历tab页点击事件\n    async handleSbllDescTabNameClick(tab, event) {\n      switch (tab.name) {\n        case \"ztbgjl\":\n          await this.getResumList();\n          break;\n        case \"yh\":\n          await this.getYhList();\n          break;\n        case \"sy\":\n          await this.getSyList();\n          break;\n        case \"blgk\":\n          await this.getBlgkList();\n          break;\n        case \"qtsbwt\":\n          await this.getqtsbwtList();\n          break;\n        case \"jx\":\n          await this.getJxList();\n          break;\n        case \"jdbh\":\n          await this.getJdbhList();\n          break;\n        case \"tzjl\":\n          await this.getTzjlList();\n          break;\n      }\n    },\n    //保存设备信息\n    submit() {\n      this.assetSubmitLoading = true;\n      this.$refs[\"sbxxForm\"].validate(valid => {\n        if (valid) {\n          this.addAsset();\n        } else {\n          this.assetSubmitLoading = false;\n          this.$message.error(\"校验未通过！\");\n          return false;\n        }\n      });\n    },\n    //站内设备弹框关闭\n    //清空表单\n    resetForm1() {\n      this.sbxxForm = this.$options.data().form;\n      this.jscsForm = this.$options.data().form;\n      this.$nextTick(function() {\n        this.$refs[\"sbxxForm\"].clearValidate();\n      });\n      this.sbllDescTabName = \"ztbgjl\";\n      this.znsbDialogForm = false;\n    },\n    /**\n     * 添加设备保存基本信息\n     */\n    addAsset() {\n      this.sbxxForm.sbClassCsValue = this.jscsForm;\n      addAsset(this.sbxxForm).then(res => {\n        if (res.code == \"0000\") {\n          this.$message({\n            type: \"success\",\n            message: \"操作成功!\"\n          });\n          this.znsbDialogForm = false;\n          // this.tableAndPageInfo3.pager.pageResize = \"Y\";\n          this.getZnsbData();\n        }\n        this.assetSubmitLoading = false;\n      });\n    },\n    //设备详情操作\n    async assetDetails(row) {\n      row.xh = row.ggxh; //规格型号赋值\n      this.technicalParameters(row);\n      this.resumeQuery.foreignNum = row.sbmc;\n      this.resumeQuery.sblx = row.sblx;\n      this.bdsbid = row.objId;\n      this.sbmc = row.sbmc;\n      this.ssbdz = row.ssbdz;\n      this.getResumList();\n      await this.getSbxhBySblx(row.assetTypeCode); //根据设备类型获取设备型号下拉框\n      //给表单赋值\n      this.sbxxForm = { ...row };\n      this.jgOptionsDataList = [\n        { label: this.sbxxForm.wzmc, value: this.sbxxForm.ssjg }\n      ];\n      //按钮和表单是否可编辑控制\n      this.assetIsDisable = true;\n      this.showFsss = true;\n      //打开设备弹出框\n      this.znsbDialogForm = true;\n    },\n    //获取光伏电站下拉框数据\n    getBdzDataListSelected() {\n      getBdzDataListSelected({}).then(res => {\n        this.bdzOptionsDataList = res.data;\n      });\n    },\n    resetForm() {\n      this.jgDialogFormVisible = false;\n      this.$refs.jgxxForm.resetFields();\n    },\n    //筛选条件重置\n    filterReset() {// 重置变电站查询参数（保留树形选择的基本参数）\n      if (this.bdzdataShow) {\n        this.bdzqueryParams = {\n          pageNum: 1,\n          pageSize: 10\n        };\n      }\n\n      // 重置间隔查询参数（保留所属变电站和间隔大类）\n      if (this.jgdataShow) {\n        const ssbdz = this.jgQueryParams.ssbdz;\n        const jgdl = this.jgQueryParams.jgdl;\n        this.jgQueryParams = {\n          ssbdz: ssbdz,\n          jgdl: jgdl,\n          dydj: undefined,\n          pageSize: 10,\n          pageNum: 1\n        };\n      }\n\n      // 重置站内设备查询参数（保留所属间隔）\n      if (this.znsbdataShow) {\n        this.znsbParams = {\n          pageSize: 10,\n          pageNum: 1\n        };\n        // 保留间隔相关的查询条件\n        this.bdzqueryParams = {\n          ssbdz: \"\",\n          ssjg: this.ssjg,\n          pageNum: 1,\n          pageSize: 10\n        };\n      }\n\n      // 重置筛选条件的数据和复选框\n      this.filterInfo1.data = {\n        ywdwArr: [],\n        jhnyArr: [],\n        xlArr: \"\",\n        jhlxArr: [],\n        jhztArr: \"\",\n        sfdd: \"\"\n      };\n      this.filterInfo1.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n\n      this.filterInfo2.data = {\n        ywdwArr: [],\n        jhnyArr: [],\n        xlArr: \"\",\n        jhlxArr: [],\n        jhztArr: \"\",\n        sfdd: \"\"\n      };\n      this.filterInfo2.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n\n      this.filterInfo3.data = {\n        ywdwArr: [],\n        jhnyArr: [],\n        xlArr: \"\",\n        jhlxArr: [],\n        jhztArr: \"\",\n        sfdd: \"\"\n      };\n      this.filterInfo3.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n\n      // 重置表格页码\n      if (this.bdzdataShow && this.$refs.bdzTable) {\n        this.$refs.bdzTable.currentPage = 1;\n      }\n      if (this.jgdataShow && this.$refs.jgTable) {\n        this.$refs.jgTable.currentPage = 1;\n      }\n      if (this.znsbdataShow && this.$refs.znsbTable) {\n        this.$refs.znsbTable.currentPage = 1;\n      }\n\n      // 根据当前显示的表格重新加载数据（保持当前的树形选择状态）\n      if (this.bdzdataShow) {\n        this.getbdzData();\n      } else if (this.jgdataShow) {\n        this.getJgData();\n      } else if (this.znsbdataShow) {\n        this.getZnsbData();\n      }\n    },\n    async querySearchAsync(queryString, cb) {\n      const results = queryString\n        ? this.ggxhList.filter(\n            item =>\n              item.label.toLowerCase().indexOf(queryString.toLowerCase()) !== -1\n          )\n        : this.ggxhList;\n      // 确保每个选项都包含 label 和 value 字段\n      const formattedResults = results.map(item => ({\n        label: item.label,\n        value: item.value || item.label // 如果没有 value，使用 label 作为 value\n      }));\n      console.log(\"results:\", formattedResults);\n      cb(formattedResults);\n    },\n    handleSelect(item) {\n      this.isSelectingFromDropdown = true; // 设置标记\n      this.sbxxForm.xh = item.label;\n    },\n    async handleChange(value) {\n      // 如果是从下拉列表选择的，不执行新增操作\n      setTimeout(() => {\n        if (this.isSelectingFromDropdown) {\n          this.isSelectingFromDropdown = false; // 重置标记\n          return;\n        }\n        // 如果输入的值不在选项中，则添加到型号库\n      if (value && !this.ggxhList.some(item => item.label === value)) {\n        try {\n          const params = {\n            sbxh: value,\n            dysblx: this.sbxxForm.assetTypeCode // 从表单中获取设备类型\n          }\n          addSbxh(params).then(res => {\n            if (res.code === '0000') {\n              this.ggxhList.push({\n                label: value,\n                value: value // 添加 value 字段\n              })\n              this.$message.success('已添加到型号库')\n            } else {\n              this.$message.warning(res.msg || '添加型号失败')\n            }\n          })\n        } catch (error) {\n          console.warning('添加型号失败:', error)\n          this.$message.warning('添加型号失败')\n        }\n      }\n      }, 500);\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.imgCls {\n  height: 150px !important;\n}\n\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  height: 70.6vh;\n  overflow: auto;\n}\n\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.box-cardList {\n  height: 70%;\n}\n\n.item {\n  width: 200px;\n  float: left;\n}\n\n#main_container_dj {\n  height: calc(100vh - 84px);\n}\n\n.aside_height {\n  height: 96%;\n}\n\n.defect .el-form-item:nth-child(odd) {\n  margin-right: 70px;\n}\n\n/*背景颜色调整*/\n#main_container_dj,\n#main_container_dj .el-aside {\n  background-color: #b4caf1;\n}\n\n///deep/ .qxlr_dialog_insert .el-dialog__header {\n//  background-color: #8eb3f5;\n//}\n//\n///deep/ .pmyBtn {\n//  background: #8eb3f5;\n//}\n\n/*/deep/ .add_sy_tyrq .el-input--medium .el-input__inner {*/\n/*  width: 200px;*/\n/*}*/\n\n/*添加弹出框得宽度*/\n/*/deep/ .el-input--medium .el-input__inner {*/\n/*  width: 200px;*/\n/*}*/\n.el-select {\n  width: 100%;\n}\n\n.el-date-editor {\n  width: 100%;\n}\n\n.el-carousel__item h3 {\n  color: #475669;\n  font-size: 14px;\n  opacity: 0.75;\n  line-height: 150px;\n}\n\n.el-carousel__item:nth-child(2n) {\n  background-color: #99a9bf;\n}\n\n.el-carousel__item:nth-child(2n + 1) {\n  background-color: #d3dce6;\n}\n\n/* 设置滚动条的样式 */\n::-webkit-scrollbar {\n  width: 12px;\n}\n\n/* 滚动槽 */\n::-webkit-scrollbar-track {\n  //-webkit-box-shadow:inset006pxrgba(0,0,0,0.3);\n  border-radius: 10px;\n}\n\n/* 滚动条滑块 */\n::-webkit-scrollbar-thumb {\n  border-radius: 10px;\n  background: rgba(0, 0, 0, 0.1);\n  //-webkit-box-shadow:gba(0,0,0,0.5);\n}\n\n::-webkit-scrollbar-thumb:window-inactive {\n  background: rgba(0, 0, 0, 0.1);\n}\n\n[data-v-67a974b1]::-webkit-scrollbar {\n  width: 8px;\n}\n\n.item {\n  width: 225px;\n  float: left;\n}\n\n/deep/ .box-card {\n  margin: 0 6px;\n}\n\n//有子节点 且未展开\n.el-tree ::v-deep .el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/add.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n\n//有子节点 且已展开\n.el-tree\n  ::v-deep\n  .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/prep.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n\n//没有子节点\n.el-tree ::v-deep .el-tree-node__expand-icon.is-leaf::before {\n  background: transparent;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n\n.categoryTreeIcons {\n  background: url(\"../../../../assets/icons/icon/icon1.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n\n.tableIcon {\n  background: url(\"../../../../assets/icons/icon/icon2.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n\n.classIcon {\n  background: url(\"../../../../assets/icons/icon/icon3.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n</style>\n<style>\n#pic_form .el-form-item__content {\n  margin-left: 0 !important;\n}\n\n#imgId .el-carousel__container {\n  background-color: #ffffff !important;\n}\n</style>\n"]}]}