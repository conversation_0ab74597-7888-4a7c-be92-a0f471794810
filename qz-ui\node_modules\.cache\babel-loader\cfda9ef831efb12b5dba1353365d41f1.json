{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\jbwh_edit.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\jbwh_edit.vue", "mtime": 1706897323219}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["jbwh_edit.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4DA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,WADA;AAEA,EAAA,KAAA,EAAA;AACA,IAAA,EAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAAA,EAAA;AAFA;AADA,GAFA;AAQA,EAAA,IARA,kBAQA;AACA,WAAA;AACA,MAAA,QAAA,EAAA;AACA,QAAA,EAAA,EAAA,EADA;AACA;AACA,QAAA,IAAA,EAAA,EAFA,CAEA;;AAFA,OADA;AAKA,MAAA,OAAA,EAAA,KALA;AAKA;AACA,MAAA,KAAA,EAAA,EANA;AAMA;AACA,MAAA,SAAA,EAAA,IAAA,GAAA;AAPA,KAAA;AASA,GAlBA;AAmBA,EAAA,KAAA,EAAA;AACA,IAAA,EAAA,EAAA;AACA,MAAA,OADA,mBACA,MADA,EACA,MADA,EACA;AACA,aAAA,QAAA,CAAA,EAAA,GAAA,MAAA;AACA,aAAA,OAAA;AACA,OAJA;AAKA,MAAA,IAAA,EAAA,IALA;AAMA,MAAA,SAAA,EAAA;AANA;AADA,GAnBA;AA6BA,EAAA,OA7BA,qBA6BA,CACA,CA9BA;AA+BA,EAAA,OAAA,EAAA;AACA,IAAA,gBADA,4BACA,GADA,EACA,GADA,EACA;AAAA;;AACA,MAAA,GAAA,CAAA,OAAA,CAAA,UAAA,GAAA,EAAA,GAAA,EAAA;AACA,YAAA,CAAA,KAAA,CAAA,SAAA,CAAA,GAAA,CAAA,GAAA,CAAA,EAAA;AACA,UAAA,KAAA,CAAA,SAAA,CAAA,GAAA,CAAA,GAAA,EAAA,GAAA;AACA;AACA,OAJA;AAKA,WAAA,YAAA,CAAA,GAAA;AACA,KARA;AASA;AACA,IAAA,YAVA,wBAUA,GAVA,EAUA;AACA,UAAA,GAAA,GAAA,QAAA,CAAA,cAAA,CAAA,SAAA,CAAA;;AACA,UAAA,QAAA,CAAA,SAAA,EAAA;AACA,YAAA,GAAA,GAAA,QAAA,CAAA,SAAA,CAAA,WAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,GAAA,GAAA;AACA,OAHA,MAGA,IAAA,OAAA,GAAA,CAAA,cAAA,KAAA,QAAA,IAAA,OAAA,GAAA,CAAA,YAAA,KAAA,QAAA,EAAA;AACA,YAAA,QAAA,GAAA,GAAA,CAAA,cAAA;AACA,YAAA,MAAA,GAAA,GAAA,CAAA,YAAA;AACA,YAAA,SAAA,GAAA,QAAA;AACA,YAAA,MAAA,GAAA,GAAA,CAAA,KAAA;AACA,QAAA,GAAA,CAAA,KAAA,GAAA,MAAA,CAAA,SAAA,CAAA,CAAA,EAAA,QAAA,IAAA,GAAA,GAAA,MAAA,CAAA,SAAA,CAAA,MAAA,EAAA,MAAA,CAAA,MAAA,CAAA;AACA,aAAA,QAAA,CAAA,EAAA,GAAA,GAAA,CAAA,KAAA,CANA,CAMA;;AACA,QAAA,SAAA,IAAA,GAAA,CAAA,MAAA;AACA,QAAA,GAAA,CAAA,cAAA,GAAA,GAAA,CAAA,YAAA,GAAA,SAAA;AACA,OATA,MASA;AACA,QAAA,GAAA,CAAA,KAAA,IAAA,GAAA;AACA,aAAA,QAAA,CAAA,EAAA,GAAA,GAAA,CAAA,KAAA,CAFA,CAEA;AACA;;AACA,WAAA,OAAA;AACA,KA7BA;AA8BA;AACA,IAAA,QA/BA,oBA+BA,GA/BA,EA+BA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,MAAA,CAAA,YAAA,CAAA,GAAA,IAAA,QAAA,GAAA,oCAAA,GAAA,GAAA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,KAjCA;AAkCA;AACA,IAAA,OAnCA,qBAmCA;AACA,UAAA,aAAA,GAAA,KAAA,QAAA,CAAA,EAAA;AACA,MAAA,aAAA,GAAA,aAAA,CAAA,OAAA,CAAA,UAAA,EAAA,IAAA,EACA,OADA,CACA,KADA,EACA,IADA,EAEA,OAFA,CAEA,OAFA,EAEA,IAFA,EAGA,OAHA,CAGA,eAHA,EAGA,KAHA,EAIA,OAJA,CAIA,cAJA,EAIA,KAJA,EAKA,OALA,CAKA,aALA,EAKA,MALA,EAMA,OANA,CAMA,eANA,EAMA,OANA,EAOA,OAPA,CAOA,KAPA,EAOA,MAPA,EAQA,OARA,CAQA,IARA,EAQA,IARA,EASA,OATA,CASA,KATA,EASA,MATA,EAUA,OAVA,CAUA,IAVA,EAUA,IAVA,EAWA,OAXA,CAWA,KAXA,EAWA,IAXA,EAYA,OAZA,CAYA,KAZA,EAYA,KAZA,EAaA,OAbA,CAaA,OAbA,EAaA,IAbA,EAcA,OAdA,CAcA,KAdA,EAcA,IAdA,EAeA,OAfA,CAeA,SAfA,EAeA,IAfA,EAgBA,OAhBA,CAgBA,kBAhBA,EAgBA,MAhBA,EAiBA,OAjBA,CAiBA,UAjBA,EAiBA,MAjBA,CAAA;;AAmBA,UAAA,KAAA,SAAA,EAAA;AACA,aAAA,SAAA,CAAA,OAAA,CAAA,UAAA,GAAA,EAAA,GAAA,EAAA;AACA,UAAA,aAAA,GAAA,aAAA,CAAA,UAAA,CAAA,GAAA,EAAA,GAAA,CAAA;AACA,SAFA;AAGA;;AACA,WAAA,QAAA,CAAA,IAAA,GAAA,aAAA;AACA,KA9DA;AA+DA;AACA,IAAA,QAhEA,sBAgEA;AAAA;;AACA,WAAA,OAAA,GAAA,KAAA;AACA,UAAA,GAAA,GAAA,KAAA,QAAA,CAAA,EAAA;AACA,yBAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,KAAA,SAAA,EAAA;AACA,UAAA,MAAA,CAAA,OAAA,GAAA,IAAA;;AACA,UAAA,MAAA,CAAA,QAAA,CAAA;AACA,YAAA,IAAA,EAAA,SADA;AAEA,YAAA,OAAA,EAAA;AAFA,WAAA;AAIA,SANA,MAMA;AACA,UAAA,MAAA,CAAA,OAAA,GAAA,KAAA;;AACA,UAAA,MAAA,CAAA,QAAA,CAAA;AACA,YAAA,IAAA,EAAA,OADA;AAEA,YAAA,OAAA,EAAA;AAFA,WAAA;AAIA;AACA,OAdA;AAeA,KAlFA;AAmFA;AACA,IAAA,OApFA,qBAoFA;AACA,UAAA,KAAA,OAAA,EAAA;AACA,aAAA,KAAA,GAAA,KAAA,QAAA,CAAA,EAAA;AACA,aAAA,KAAA,CAAA,UAAA,EAAA,KAAA,KAAA,EAFA,CAEA;;AACA,aAAA,KAAA,CAAA,SAAA,EAHA,CAGA;AAEA,OALA,MAKA;AACA,aAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,OADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA;AACA,KAhGA;AAiGA;AACA,IAAA,QAlGA,sBAkGA;AACA,WAAA,OAAA,GAAA,KAAA;AACA,WAAA,QAAA,CAAA,EAAA,GAAA,EAAA;AACA,WAAA,OAAA;AACA,KAtGA;AAuGA;AACA,IAAA,OAxGA,qBAwGA;AACA,WAAA,OAAA,GAAA,KAAA;AACA,WAAA,QAAA,CAAA,EAAA,GAAA,6DAAA;AACA,WAAA,OAAA;AACA,KA5GA;AA6GA;AACA,IAAA,OA9GA,qBA8GA;AACA,WAAA,OAAA,GAAA,KAAA;AACA,WAAA,QAAA,CAAA,EAAA,GAAA,uBACA,KADA,GAEA,oBAFA,GAGA,KAHA,GAIA,QAJA,GAKA,KALA,GAMA,oBANA,GAOA,GAPA;AAQA,WAAA,OAAA;AACA,KAzHA;AA0HA;AACA,IAAA,OA3HA,qBA2HA;AACA,WAAA,OAAA,GAAA,KAAA;AACA,WAAA,QAAA,CAAA,EAAA,GAAA,uBACA,KADA,GAEA,oBAFA,GAGA,KAHA,GAIA,QAJA,GAKA,KALA,GAMA,oBANA,GAOA,GAPA;AAQA,WAAA,OAAA;AACA,KAtIA;AAuIA;AACA,IAAA,OAxIA,qBAwIA;AACA,WAAA,OAAA,GAAA,KAAA;AACA,WAAA,QAAA,CAAA,EAAA,GAAA,uBACA,KADA,GAEA,oBAFA,GAGA,KAHA,GAIA,QAJA,GAKA,KALA,GAMA,oBANA,GAOA,GAPA;AAQA,WAAA,OAAA;AACA;AAnJA;AA/BA,C", "sourcesContent": ["<template>\n  <el-row :gutter=\"16\">\n    <!--  原始公式  -->\n    <el-col :span=\"24\">\n      <div>\n        <el-button type=\"primary\" @click=\"check_jb\">验证</el-button>\n        <el-button type=\"primary\" @click=\"save_jb\">保存</el-button>\n        <el-button type=\"primary\" @click=\"init_jb\">初始化</el-button>\n        <el-button type=\"primary\" @click=\"clear_jb\">清空</el-button>\n        <el-button type=\"success\" @click=\"init_yb\">一般C</el-button>\n        <el-button type=\"success\" @click=\"init_yz\">一般B</el-button>\n        <el-button type=\"success\" @click=\"init_wj\">一般A</el-button>\n      </div>\n    </el-col>\n    <el-col :span=\"24\">\n      <div class=\"row\" style=\"border:1px;\" id=\"atChat\">\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' && ')\">&&</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' || ')\">||</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' > ')\">></a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' >= ')\">>=</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' < ')\"><</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' <= ')\"><=</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' == ')\">==</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' != ')\">!=</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' Math.abs() ')\">abs</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' ( ')\">(</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' ) ')\">)</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' { ')\">{</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' } ')\">}</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick('elseIf')\">else if</a>\n      </div>\n    </el-col>\n\n    <!--  规则解释  -->\n    <el-col :span=\"24\">\n      <el-form :model=\"editForm\" ref=\"editForm\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item prop=\"gs\">\n              <el-input id=\"jb_text\" name=\"pptjms\" type=\"textarea\" :rows=\"10\" v-model=\"editForm.gs\" placeholder=\"请输入脚本\"\n                        v-on:input.native=\"jb_show\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <div>\n              <span>规则解释</span>\n            </div>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item prop=\"gzjs\">\n              <el-input id=\"jb_text_show\" name=\"pptjms\" type=\"textarea\" :rows=\"10\" v-model=\"editForm.gzjs\" disabled/>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n    </el-col>\n  </el-row>\n</template>\n\n<script>\nimport {checkJb} from '@/api/dagangOilfield/bzgl/sbztpjbzk/jbwh'\n\nexport default {\n  name: 'jbwh_edit',\n  props: {\n    jb: {\n      type: String,\n      default: ''\n    },\n  },\n  data() {\n    return {\n      editForm: {\n        gs: '',//公式\n        gzjs: '',//规则解释\n      },\n      checkJB: false,//是否验证脚本\n      jbVal: '',//返回给组件中的脚本字符串\n      parentMap: new Map(),\n    };\n  },\n  watch: {\n    jb: {\n      handler(newVal, oldVal) {\n        this.editForm.gs = newVal;\n        this.jb_show();\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  mounted() {\n  },\n  methods: {\n    processParentVal(val, map) {\n      map.forEach((val, key) => {\n        if (!this.parentMap.has(key)) {\n          this.parentMap.set(key, val);\n        }\n      })\n      this.trTableClick(val);\n    },\n    //放置光标数据\n    trTableClick(str,) {\n      let obj = document.getElementById('jb_text');\n      if (document.selection) {\n        let sel = document.selection.createRange();\n        sel.text = str;\n      } else if (typeof obj.selectionStart === 'number' && typeof obj.selectionEnd === 'number') {\n        let startPos = obj.selectionStart;\n        let endPos = obj.selectionEnd;\n        let cursorPos = startPos;\n        let tmpStr = obj.value;\n        obj.value = tmpStr.substring(0, startPos) + str + tmpStr.substring(endPos, tmpStr.length);\n        this.editForm.gs = obj.value;//设置公式值\n        cursorPos += str.length;\n        obj.selectionStart = obj.selectionEnd = cursorPos;\n      } else {\n        obj.value += str;\n        this.editForm.gs = obj.value;//设置公式值\n      }\n      this.jb_show();\n    },\n    //绑定a标签点击事件\n    async btnClick(val) {\n      await this.trTableClick(val == 'elseIf' ? \"\\nelse if()\\n{\\n    return    ;\\n}\" : val);\n    },\n    //脚本翻译\n    jb_show() {\n      let ruleScriptStr = this.editForm.gs;\n      ruleScriptStr = ruleScriptStr.replace(/else if/g, \"如果\")\n          .replace(/if/g, \"如果\")\n          .replace(/else/g, \"否则\")\n          .replace(/getParameter/g, \"参数值\")\n          .replace(/getColValue/g, \"参数值\")\n          .replace(/getXxdData/g, \"信息点值\")\n          .replace(/getZxXxdData/g, \"字信息点值\")\n          .replace(/>=/g, \"大于等于\")\n          .replace(/>/g, \"大于\")\n          .replace(/<=/g, \"小于等于\")\n          .replace(/</g, \"小于\")\n          .replace(/==/g, \"等于\")\n          .replace(/!=/g, \"不等于\")\n          .replace(/\\|\\|/g, \"或者\")\n          .replace(/&&/g, \"并且\")\n          .replace(/return/g, \"返回\")\n          .replace(/(Math.abs)\\s*\\(/g, \"绝对值(\")\n          .replace(/getQxdj/g, \"隐患等级\");\n\n      if (this.parentMap) {\n        this.parentMap.forEach((val, key) => {\n          ruleScriptStr = ruleScriptStr.replaceAll(key, val);\n        })\n      }\n      this.editForm.gzjs = ruleScriptStr;\n    },\n    //脚本验证\n    check_jb() {\n      this.checkJB = false;\n      let str = this.editForm.gs;\n      checkJb(str).then(res => {\n        if (res !== '脚本定义错误！') {\n          this.checkJB = true;\n          this.$message({\n            type: 'success',\n            message: '脚本执行成功!'\n          })\n        } else {\n          this.checkJB = false;\n          this.$message({\n            type: 'error',\n            message: '脚本定义错误!'\n          })\n        }\n      })\n    },\n    //脚本保存\n    save_jb() {\n      if (this.checkJB) {\n        this.jbVal = this.editForm.gs;\n        this.$emit('setJbVal', this.jbVal);//将脚本的值传递给父页面\n        this.$emit('jbClose');//关闭脚本弹框\n\n      } else {\n        this.$message({\n          type: 'error',\n          message: '脚本没有验证或脚本定义错误，请进行验证后或定义正确脚本在保存！'\n        })\n      }\n    },\n    //清空脚本\n    clear_jb() {\n      this.checkJB = false;\n      this.editForm.gs = \"\";\n      this.jb_show();\n    },\n    //初始化\n    init_jb() {\n      this.checkJB = false;\n      this.editForm.gs = \"if()\\n{\\n    return    1;\\n}\\nelse\\n{\\n    return     0;\\n}\";\n      this.jb_show();\n    },\n    //一般\n    init_yb() {\n      this.checkJB = false;\n      this.editForm.gs = \"if(getQxdj()>=0)\\n\" +\n          \"{\\n\" +\n          \"    return    1;\\n\" +\n          \"}\\n\" +\n          \"else\\n\" +\n          \"{\\n\" +\n          \"    return    0;\\n\" +\n          \"}\";\n      this.jb_show();\n    },\n    //严重\n    init_yz() {\n      this.checkJB = false;\n      this.editForm.gs = \"if(getQxdj()>=1)\\n\" +\n          \"{\\n\" +\n          \"    return    1;\\n\" +\n          \"}\\n\" +\n          \"else\\n\" +\n          \"{\\n\" +\n          \"    return    0;\\n\" +\n          \"}\";\n      this.jb_show();\n    },\n    //危急\n    init_wj() {\n      this.checkJB = false;\n      this.editForm.gs = \"if(getQxdj()>=2)\\n\" +\n          \"{\\n\" +\n          \"    return    1;\\n\" +\n          \"}\\n\" +\n          \"else\\n\" +\n          \"{\\n\" +\n          \"    return    0;\\n\" +\n          \"}\";\n      this.jb_show();\n    },\n  },\n}\n</script>\n\n<style scoped lang=\"scss\">\n.row {\n  padding-top: 20px;\n  padding-bottom: 20px;\n  font-size: 20px;\n}\n\n.btn {\n  padding: 14px;\n\n  &:hover {\n    color: #00c39a;\n  }\n}\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk"}]}