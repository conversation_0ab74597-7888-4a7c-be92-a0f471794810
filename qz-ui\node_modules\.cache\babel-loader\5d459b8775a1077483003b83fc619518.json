{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\processdefinition\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\processdefinition\\index.vue", "mtime": 1706897322046}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5Lm1hcCIpOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Cgp2YXIgX2F1dGggPSByZXF1aXJlKCJAL3V0aWxzL2F1dGgiKTsKCnZhciBfcHJvY2Vzc2RlZmluaXRpb24gPSByZXF1aXJlKCJAL2FwaS9hY3Rpdml0aS9wcm9jZXNzZGVmaW5pdGlvbiIpOwoKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gewogIG5hbWU6ICJQcm9jZXNzRGVmaW5pdGlvbiIsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8v5LiK5Lyg5Zu+54mH5pe255qE6K+35rGC5aS0CiAgICAgIGhlYWRlcjoge30sCiAgICAgIC8vIOmBrue9qeWxggogICAgICBsb2FkaW5nOiB0cnVlLAogICAgICAvLyDpgInkuK3mlbDnu4QKICAgICAgaWRzOiBbXSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g55So5oi36KGo5qC85pWw5o2uCiAgICAgIHByb2Nlc3NkZWZpbml0aW9uOiBudWxsLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIC8vIOmDqOmXqOWQjeensAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIGtleTogdW5kZWZpbmVkLAogICAgICAgIG5hbWU6IHVuZGVmaW5lZAogICAgICB9LAogICAgICBmaWxlTGlzdDogW10KICAgIH07CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgLy/ojrflj5Z0b2tlbgogICAgdGhpcy5oZWFkZXIudG9rZW4gPSAoMCwgX2F1dGguZ2V0VG9rZW4pKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDmtYvor5XmlbDmja7lrZflhbjmlrnms5Xnu5PmnZ8KCiAgICAvKiog5p+l6K+i55So5oi35YiX6KGoICovCiAgICBnZXRMaXN0OiBmdW5jdGlvbiBnZXRMaXN0KCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwoKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgKDAsIF9wcm9jZXNzZGVmaW5pdGlvbi5saXN0KSh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzLnByb2Nlc3NkZWZpbml0aW9uID0gcmVzcG9uc2UuZGF0YS5yZWNvcmRzOwogICAgICAgIF90aGlzLnRvdGFsID0gcmVzcG9uc2UuZGF0YS50b3RhbDsKICAgICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgIH0pOwogICAgfSwKCiAgICAvKioKICAgICAqIOihqOWNlemHjee9rgogICAgICogKi8KICAgIHJlc2V0OiBmdW5jdGlvbiByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIGtleTogdW5kZWZpbmVkLAogICAgICAgIG5hbWU6IHVuZGVmaW5lZCwKICAgICAgICBjYXRlZ29yeTogdW5kZWZpbmVkCiAgICAgIH07CiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICB9LAoKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVF1ZXJ5OiBmdW5jdGlvbiBoYW5kbGVRdWVyeSgpIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAoKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8KICAgIHJlc2V0UXVlcnk6IGZ1bmN0aW9uIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gW107CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKCiAgICAvKioq5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uKioqLwogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLnVzZXJJZDsKICAgICAgfSk7CiAgICB9LAoKICAgIC8qKiDnvJbovpHmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVVwZGF0ZTogZnVuY3Rpb24gaGFuZGxlVXBkYXRlKHJvdykgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKCiAgICAgIHZhciBzdXNwZW5kU3RhdGUgPSByb3cuc3VzcGVuZFN0YXRlOwogICAgICAoMCwgX3Byb2Nlc3NkZWZpbml0aW9uLnN1c3BlbmRPckFjdGl2ZUFwcGx5KSh7CiAgICAgICAgaWQ6IHJvdy5pZCwKICAgICAgICBzdXNwZW5kU3RhdGU6IHJvdy5zdXNwZW5kU3RhdGUKICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgaWYgKHJlcy5jb2RlID09ICIwMDAwIikgewogICAgICAgICAgX3RoaXMyLm1zZ1N1Y2Nlc3Moc3VzcGVuZFN0YXRlID09ICIyIiA/ICLmv4DmtLvmiJDlip8iIDogIuaMgui1t+aIkOWKnyIpOwoKICAgICAgICAgIF90aGlzMi5nZXRMaXN0KCk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCgogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRGVsZXRlOiBmdW5jdGlvbiBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwoKICAgICAgdmFyIGRlcGxveW1lbnRJZCA9IHJvdy5kZXBsb3ltZW50SWQ7CiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOa1geeoi0lE5Li6IicgKyByb3cuaWQgKyAnIueahOaVsOaNrumhuT8nLCAi6K2m5ZGKIiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgcmV0dXJuICgwLCBfcHJvY2Vzc2RlZmluaXRpb24uZGVsKShkZXBsb3ltZW50SWQpOwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczMuZ2V0TGlzdCgpOwoKICAgICAgICBfdGhpczMubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHt9KTsKICAgIH0sCgogICAgLyoqCiAgICAgKiDkuIrkvKDmiJDlip/kvJrov5Tlm55yZXNwb25zZSzmi7/liLDlkI7nq68vdXBsb2Fk55qE6L+U5Zue5YC8CiAgICAgKiBAcGFyYW0gcmVzcG9uc2UKICAgICAqIEBwYXJhbSBmaWxlCiAgICAgKi8KICAgIGhhbmRsZUF2YXRhclN1Y2Nlc3M6IGZ1bmN0aW9uIGhhbmRsZUF2YXRhclN1Y2Nlc3MocmVzcG9uc2UsIGZpbGUpIHsKICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgIHRoaXMubXNnU3VjY2Vzcygi6YOo572y5oiQ5Yqf77yBIik7CiAgICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cpOwogICAgICAgIHRoaXMuJHJlZnMudXBsb2FkLmNsZWFyRmlsZXMoKTsKICAgICAgfQogICAgfSwKCiAgICAvKiog5LiK5Lyg5paH5Lu2ICoqLwogICAgYmVmb3JlVXBsb2FkOiBmdW5jdGlvbiBiZWZvcmVVcGxvYWQoKSB7fSwKCiAgICAvKiog6L2s5YyW5qih5Z6LICoqLwogICAgaGFuZGxlQ29udmVydFRvTW9kZWw6IGZ1bmN0aW9uIGhhbmRsZUNvbnZlcnRUb01vZGVsKHJvdykgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKCiAgICAgICgwLCBfcHJvY2Vzc2RlZmluaXRpb24uY29udmVydFRvTW9kZWwpKHJvdy5pZCkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXM0Lm1zZ1N1Y2Nlc3MoIui9rOaNouaIkOWKn++8gSIpOwogICAgICB9KTsKICAgIH0KICB9Cn07CmV4cG9ydHMuZGVmYXVsdCA9IF9kZWZhdWx0Ow=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;AA+GA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;AACA,EAAA,IAAA,EAAA,mBADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA;AACA,MAAA,MAAA,EAAA,EAFA;AAGA;AACA,MAAA,OAAA,EAAA,IAJA;AAKA;AACA,MAAA,GAAA,EAAA,EANA;AAOA;AACA,MAAA,UAAA,EAAA,IARA;AASA;AACA,MAAA,KAAA,EAAA,CAVA;AAWA;AACA,MAAA,iBAAA,EAAA,IAZA;AAaA;AACA,MAAA,IAAA,EAAA,KAdA;AAeA;AACA;AACA,MAAA,IAAA,EAAA,EAjBA;AAkBA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,GAAA,EAAA,SAHA;AAIA,QAAA,IAAA,EAAA;AAJA,OAnBA;AAyBA,MAAA,QAAA,EAAA;AAzBA,KAAA;AA2BA,GA9BA;AA+BA,EAAA,OA/BA,qBA+BA;AACA,SAAA,OAAA;AACA,GAjCA;AAkCA,EAAA,OAlCA,qBAkCA;AACA;AACA,SAAA,MAAA,CAAA,KAAA,GAAA,qBAAA;AACA,GArCA;AAsCA,EAAA,OAAA,EAAA;AACA;;AACA;AACA,IAAA,OAHA,qBAGA;AAAA;;AACA,WAAA,OAAA,GAAA,IAAA;AACA,mCAAA,KAAA,WAAA,EAAA,IAAA,CACA,UAAA,QAAA,EAAA;AACA,QAAA,KAAA,CAAA,iBAAA,GAAA,QAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,KAAA,CAAA,KAAA,GAAA,QAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,KAAA,CAAA,OAAA,GAAA,KAAA;AACA,OALA;AAOA,KAZA;;AAaA;;;AAGA,IAAA,KAhBA,mBAgBA;AACA,WAAA,IAAA,GAAA;AACA,QAAA,GAAA,EAAA,SADA;AAEA,QAAA,IAAA,EAAA,SAFA;AAGA,QAAA,QAAA,EAAA;AAHA,OAAA;AAKA,WAAA,SAAA,CAAA,MAAA;AACA,KAvBA;;AAwBA;AACA,IAAA,WAzBA,yBAyBA;AACA,WAAA,WAAA,CAAA,OAAA,GAAA,CAAA;AACA,WAAA,OAAA;AACA,KA5BA;;AA6BA;AACA,IAAA,UA9BA,wBA8BA;AACA,WAAA,SAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,WAAA;AACA,WAAA,WAAA;AACA,KAlCA;;AAmCA;AACA,IAAA,qBApCA,iCAoCA,SApCA,EAoCA;AACA,WAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,MAAA;AAAA,OAAA,CAAA;AACA,KAtCA;;AAuCA;AACA,IAAA,YAxCA,wBAwCA,GAxCA,EAwCA;AAAA;;AACA,UAAA,YAAA,GAAA,GAAA,CAAA,YAAA;AACA,mDAAA;AAAA,QAAA,EAAA,EAAA,GAAA,CAAA,EAAA;AAAA,QAAA,YAAA,EAAA,GAAA,CAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,YAAA,IAAA,GAAA,GAAA,MAAA,GAAA,MAAA;;AACA,UAAA,MAAA,CAAA,OAAA;AACA;AACA,OALA;AAMA,KAhDA;;AAiDA;AACA,IAAA,YAlDA,wBAkDA,GAlDA,EAkDA;AAAA;;AACA,UAAA,YAAA,GAAA,GAAA,CAAA,YAAA;AACA,WAAA,QAAA,CACA,iBAAA,GAAA,CAAA,EAAA,GAAA,QADA,EAEA,IAFA,EAGA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAHA,EASA,IATA,CASA,YAAA;AACA,eAAA,4BAAA,YAAA,CAAA;AACA,OAXA,EAYA,IAZA,CAYA,YAAA;AACA,QAAA,MAAA,CAAA,OAAA;;AACA,QAAA,MAAA,CAAA,UAAA,CAAA,MAAA;AACA,OAfA,EAgBA,KAhBA,CAgBA,YAAA,CACA,CAjBA;AAkBA,KAtEA;;AAuEA;;;;;AAKA,IAAA,mBA5EA,+BA4EA,QA5EA,EA4EA,IA5EA,EA4EA;AACA,UAAA,QAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,aAAA,UAAA,CAAA,OAAA;AACA,aAAA,OAAA;AACA,OAHA,MAGA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,QAAA,CAAA,GAAA;AACA,aAAA,KAAA,CAAA,MAAA,CAAA,UAAA;AACA;AACA,KApFA;;AAqFA;AACA,IAAA,YAtFA,0BAsFA,CAEA,CAxFA;;AAyFA;AACA,IAAA,oBA1FA,gCA0FA,GA1FA,EA0FA;AAAA;;AACA,6CAAA,GAAA,CAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,UAAA,CAAA,OAAA;AACA,OAFA;AAGA;AA9FA;AAtCA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n      <el-form class=\"searchForm1\" :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"120px\">\n        <el-form-item label=\"流程key：\" prop=\"key\">\n          <el-input v-model=\"queryParams.key\" placeholder=\"请输入流程key\" clearable class=\"common-width\"\n                    @keyup.enter.native=\"handleQuery\"/>\n        </el-form-item>\n        <el-form-item label=\"名称：\" prop=\"name\">\n          <el-input v-model=\"queryParams.name\" placeholder=\"请输入名称\" clearable class=\"common-width\"\n                    @keyup.enter.native=\"handleQuery\"/>\n        </el-form-item>\n        <el-form-item label=\"所属分类：\" prop=\"category\">\n          <el-input v-model=\"queryParams.category\" placeholder=\"请输入所属分类\" clearable class=\"common-width\"\n                    @keyup.enter.native=\"handleQuery\"/>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"cyan\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\n          <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\" type=\"warning\">重置</el-button>\n        </el-form-item>\n      </el-form>\n    <el-row :gutter=\"20\">\n      <el-col :span=\"24\">\n        <el-white class=\"button-group\">\n          <el-row :gutter=\"10\" class=\"mb8 pull-right\">\n            <el-col :span=\"1.5\">\n              <el-upload\n                :on-success=\"handleAvatarSuccess\"\n                accept=\".bpmn,.zip,.bar\"\n                :headers=\"header\"\n                action=\"/activiti-api/definition/upload\"\n                :before-upload=\"beforeUpload\"\n                ref=\"upload\"\n                multiple\n                :show-file-list=\"false\"\n              >\n                <el-button type=\"primary\" icon=\"el-icon-upload\"  >部署流程定义 </el-button>\n              </el-upload>\n            </el-col>\n          </el-row>\n          <el-table style=\"margin-top: 15px;\" stripe border v-loading=\"loading\" :data=\"processdefinition\" @selection-change=\"handleSelectionChange\" height=\"65vh\">\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n            <el-table-column label=\"流程ID\" fixed align=\"center\" prop=\"id\" width=\"160\"/>\n            <el-table-column label=\"流程KEY\" align=\"center\" prop=\"key\"  />\n            <el-table-column label=\"流程名称\" align=\"center\" prop=\"name\" width=\"200\"/>\n            <el-table-column label=\"版本\"  align=\"center\" prop=\"version\" width=\"120\" />\n            <el-table-column label=\"流程描述\" align=\"center\" prop=\"description\" width=\"120\" >\n              <template slot-scope=\"scope\">\n                <el-popover v-if=\"scope.row.description.length>20\" trigger=\"hover\" placement=\"top\" width=\"200\">\n                  {{ scope.row.description }}\n                  <div slot=\"reference\" >\n                    {{ scope.row.description.substring(0,20)+'...' }}\n                  </div>\n                </el-popover>\n                <span v-else >\n            {{ scope.row.description}}\n          </span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"所属分类\" align=\"center\" prop=\"category\"  width=\"200\"  />\n            <el-table-column label=\"部署时间\" align=\"center\" prop=\"deploymentTime\" width=\"200\"/>\n            <el-table-column label=\"流程定义\" align=\"center\" prop=\"resourceName\" width=\"200\">\n              <template slot-scope=\"scope\">\n                <el-popover v-if=\"scope.row.resourceName.length>20\" trigger=\"hover\" placement=\"top\" width=\"200\">\n                  <a style=\"color: #409EFF\" :href=\"'/activiti-api/definition/readResource?pdid='+scope.row.id+'&resourceName='+scope.row.resourceName\" target=\"_blank\" >{{ scope.row.resourceName }}</a>\n                  <div slot=\"reference\" >\n                    {{ scope.row.resourceName.substring(0,20)+'...' }}\n                  </div>\n                </el-popover>\n                <span v-else >\n             <a style=\"color: #409EFF\" :href=\"'/activiti-api/definition/readResource?pdid='+scope.row.id+'&resourceName='+scope.row.resourceName\" target=\"_blank\" >{{ scope.row.resourceName }}</a>\n          </span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"流程图\" align=\"center\" prop=\"diagramResourceName\" width=\"200\">\n              <template slot-scope=\"scope\">\n                <a style=\"color: #409EFF\" :href=\"'/activiti-api/definition/readResource?pdid='+scope.row.id+'&resourceName='+scope.row.diagramResourceName\" target=\"_blank\" >{{scope.row.key}}.png</a>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"流程定义状态\" align=\"center\" prop=\"suspendStateName\" width=\"100\" >\n              <template slot-scope=\"scope\">\n                <span v-if=\"scope.row.suspendState==1\" style=\"color: #67C23A\">已激活</span>\n                <span v-else style=\"color: #E6A23C\">已挂起</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"操作\"  fixed=\"right\" align=\"center\"   class-name=\"small-padding fixed-width\" width=\"180\">\n              <template slot-scope=\"scope\">\n                <slot v-if=\"scope.row.suspendState==2\">\n                  <el-button size=\"mini\" type=\"text\" icon=\"el-icon-check\" @click=\"handleUpdate(scope.row)\" >激活</el-button>\n                </slot>\n                <slot  v-else>\n                  <el-button size=\"mini\" type=\"text\" icon=\"el-icon-time\" @click=\"handleUpdate(scope.row)\" >挂起</el-button>\n                </slot>\n                <el-button size=\"mini\" type=\"text\" icon=\"el-icon-sort\" @click=\"handleConvertToModel(scope.row)\" >转模型</el-button>\n                <el-button  size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\"  >删除</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n\n          <pagination v-show=\"total>0\"\n                      :total=\"total\"\n                      :page.sync=\"queryParams.pageNum\"\n                      :limit.sync=\"queryParams.pageSize\"\n                      @pagination=\"getList\"\n          />\n        </el-white>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\n  import {\n    list,del,suspendOrActiveApply,convertToModel\n  } from \"@/api/activiti/processdefinition\";\n  export default {\n    name: \"ProcessDefinition\",\n    data() {\n      return {\n        //上传图片时的请求头\n      header: {},\n        // 遮罩层\n        loading: true,\n        // 选中数组\n        ids: [],\n        // 显示搜索条件\n        showSearch: true,\n        // 总条数\n        total: 0,\n        // 用户表格数据\n        processdefinition: null,\n        // 是否显示弹出层\n        open: false,\n        // 部门名称\n        // 表单参数\n        form: {},\n        // 查询参数\n        queryParams: {\n          pageNum: 1,\n          pageSize: 10,\n          key: undefined,\n          name: undefined,\n        },\n        fileList:[]\n      };\n    },\n    created() {\n      this.getList();\n    },\n    mounted() {\n      //获取token\n    this.header.token = getToken();\n    },\n    methods: {\n      // 测试数据字典方法结束\n      /** 查询用户列表 */\n      getList() {\n        this.loading = true;\n        list(this.queryParams).then(\n          (response) => {\n            this.processdefinition = response.data.records;\n            this.total = response.data.total;\n            this.loading = false;\n          }\n        );\n      },\n      /**\n       * 表单重置\n       * */\n      reset() {\n        this.form = {\n          key: undefined,\n          name: undefined,\n          category:undefined\n        };\n        this.resetForm(\"form\");\n      },\n      /** 搜索按钮操作 */\n      handleQuery() {\n        this.queryParams.pageNum = 1;\n        this.getList();\n      },\n      /** 重置按钮操作 */\n      resetQuery() {\n        this.dateRange = [];\n        this.resetForm(\"queryForm\");\n        this.handleQuery();\n      },\n      /***多选框选中数据***/\n      handleSelectionChange(selection) {\n        this.ids = selection.map((item) => item.userId);\n      },\n      /** 编辑按钮操作 */\n      handleUpdate(row) {\n        let suspendState = row.suspendState;\n        suspendOrActiveApply({id:row.id,suspendState:row.suspendState}).then(res =>{\n          if(res.code==\"0000\"){\n            this.msgSuccess(suspendState==\"2\"?\"激活成功\":\"挂起成功\");\n            this.getList();\n          }\n        })\n      },\n      /** 删除按钮操作 */\n      handleDelete(row) {\n        const deploymentId = row.deploymentId;\n        this.$confirm(\n          '是否确认删除流程ID为\"' + row.id + '\"的数据项?',\n          \"警告\",\n          {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\",\n          }\n        )\n          .then(function () {\n            return del(deploymentId);\n          })\n          .then(() => {\n            this.getList();\n            this.msgSuccess(\"删除成功\");\n          })\n          .catch(function () {\n          });\n      },\n      /**\n       * 上传成功会返回response,拿到后端/upload的返回值\n       * @param response\n       * @param file\n       */\n      handleAvatarSuccess (response, file) {\n        if (response.code === \"0000\") {\n          this.msgSuccess(\"部署成功！\")\n          this.getList();\n        } else {\n          this.$message.error(response.msg)\n          this.$refs.upload.clearFiles()\n        }\n      },\n      /** 上传文件 **/\n      beforeUpload(){\n\n      },\n      /** 转化模型 **/\n      handleConvertToModel(row){\n        convertToModel(row.id).then(res=>{\n          this.msgSuccess(\"转换成功！\")\n        })\n      }\n    },\n  };\n</script>\n"], "sourceRoot": "src/views/activiti/processdefinition"}]}