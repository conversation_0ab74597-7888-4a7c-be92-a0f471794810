{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\ysbzk.vue?vue&type=style&index=0&id=44fcead8&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\ysbzk.vue", "mtime": 1706897323967}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouaGVhZC1jb250YWluZXIgewogIG1hcmdpbjogMCBhdXRvOwogIHdpZHRoOiA5OCU7CiAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gMjI1cHgpOwogIG92ZXJmbG93OiBhdXRvOwp9CgouYm94LWNhcmQgewogIG1hcmdpbi1ib3R0b206IDE1cHg7CgogIC5lbC1jYXJkX19oZWFkZXIgewogICAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDIzNSwgMjQ1LCAyNTUpICFpbXBvcnRhbnQ7CiAgfQp9CgouYm94LWNhcmRMaXN0IHsKICBoZWlnaHQ6IDU2JTsKfQoKLml0ZW0gewogIHdpZHRoOiAyMDBweDsKICBmbG9hdDogbGVmdDsKfQoKI21haW5fY29udGFpbmVyX2RqIHsKICBoZWlnaHQ6IGNhbGMoMTAwdmggLSA4NHB4KTsKfQoKLmFzaWRlX2hlaWdodCB7CiAgaGVpZ2h0OiA5NiU7Cn0KCi5kZWZlY3QgLmVsLWZvcm0taXRlbTpudGgtY2hpbGQob2RkKSB7CiAgbWFyZ2luLXJpZ2h0OiA3MHB4Owp9CgovKuiDjOaZr+minOiJsuiwg+aVtCovCiNtYWluX2NvbnRhaW5lcl9kaiwKI21haW5fY29udGFpbmVyX2RqIC5lbC1hc2lkZSB7CiAgYmFja2dyb3VuZC1jb2xvcjogI2I0Y2FmMTsKfQoKL2RlZXAvICNxeGxyX2RpYWxvZ19pbnNlcnQgLmVsLWRpYWxvZ19faGVhZGVyIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjOGViM2Y1Owp9CgovZGVlcC8gLnBteUJ0biB7CiAgYmFja2dyb3VuZDogIzhlYjNmNTsKfQo="}, {"version": 3, "sources": ["ysbzk.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+jBA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "ysbzk.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row>\n      <el-filter\n        :data=\"filterInfo.data\"\n        :field-list=\"filterInfo.fieldList\"\n        :width=\"{ labelWidth: 80, itemWidth: 230 }\"\n        @onfocusEvent=\"inputFocusEvent\"\n        @handleReset=\"getReset\"\n        @handleEvent=\"handleEvent\"\n      />\n      <!--右侧列表-->\n      <el-col :span=\"24\">\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button\n              type=\"primary\"\n              icon=\"el-icon-plus\"\n              v-hasPermi=\"['bzysbzk:button:add']\"\n              @click=\"addMainData\"\n              >新增</el-button\n            >\n            <el-button\n              type=\"success\"\n              icon=\"el-icon-download\"\n              @click=\"exportExcel\"\n              >导出</el-button\n            >\n          </div>\n          <div class=\"button_btn\">验收标准库</div>\n          <comp-table\n            ref=\"maintable\"\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            @sort-events=\"sortChange\"\n            @rowClick=\"handleCurrentChange\"\n            v-loading=\"mainLoading\"\n            height=\"70.2vh\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              :resizable=\"false\"\n              width=\"120\"\n            >\n              <!-- <el-table-column label=\"操作\" fixed=\"right\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"180\"> -->\n              <template slot-scope=\"scope\">\n                <el-button\n                  type=\"text\"\n                  v-hasPermi=\"['bzysbzk:button:update']\"\n                  @click.stop=\"updateMainData(scope.row)\"\n                  class=\"updateBtn el-icon-edit\"\n                  title=\"修改\"\n                >\n                </el-button>\n                <el-button\n                  type=\"text\"\n                  @click.stop=\"showMainData(scope.row)\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n                <el-button\n                  type=\"text\"\n                  v-hasPermi=\"['bzysbzk:button:delete']\"\n                  @click.stop=\"deleteMainData(scope.row)\"\n                  title=\"删除\"\n                  class=\"el-icon-delete\"\n                >\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n          <!-- </el-table> -->\n          <!-- <pagination v-show=\"mainParanms.total>0\" :total=\"mainParanms.total\" :page.sync=\"mainParanms.pageNum\"\n            :limit.sync=\"mainParanms.pageSize\" @pagination=\"getMainStandardData\" /> -->\n        </el-white>\n        <el-white>\n          <acceptance-detail ref=\"detail\"></acceptance-detail>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <dialog-form\n      ref=\"dialogForm\"\n      label-width=\"150px\"\n      out-width=\"50%\"\n      :reminder=\"reminder\"\n      :rows=\"rows\"\n      @onfocusEvent=\"inputFocusEvent\"\n      @save=\"saveMainData\"\n      @inputChange1=\"getSblxList\"\n    />\n\n    <el-dialog\n      :append-to-body=\"true\"\n      title=\"设备分类\"\n      :visible.sync=\"showDeviceTree\"\n      width=\"400px\"\n      v-if=\"showDeviceTree\"\n      v-dialogDrag\n    >\n      <device-tree\n        @getDeviceTypeData=\"getDeviceTypeData\"\n        @closeDeviceTypeDialog=\"closeDeviceTypeDialog\"\n      >\n      </device-tree>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport dialogForm from \"com/dialogFrom/dialogForm\";\nimport {\n  deleteBzYsbzzb,\n  getBzYsbzzb,\n  saveOrUpdateBzYsbzzb,\n  exportExcel\n} from \"@/api/bzgl/ysbzk/ysbzk\";\nimport AcceptanceDetail from \"@/views/dagangOilfield/bzgl/ysbzkgl/acceptanceDetail\";\nimport DeviceTree from \"@/views/dagangOilfield/bzgl/sbbzk/deviceTree\";\nimport { getDictTypeData } from \"@/api/system/dict/data\";\nimport { getSblxDataListSelected } from \"@/api/dagangOilfield/asset/bdsbtz\";\n\nexport default {\n  components: { DeviceTree, AcceptanceDetail, dialogForm },\n  name: \"ysbzk\",\n  data() {\n    return {\n      filterParams: {},\n      filterInfo: {\n        data: {\n          spb: \"\",\n          sbfl: \"\",\n          // sbflmc: \"\"\n          jxfl: \"\",\n          xmmc: \"\",\n          bz: \"\",\n          yslx: \"\"\n        }, //查询条件\n        fieldList: [\n          {\n            label: \"所属专业\",\n            type: \"select\",\n            value: \"spb\",\n            options: [\n              { label: \"变电\", value: \"变电\" },\n              { label: \"配电\", value: \"配电\" },\n              { label: \"输电\", value: \"输电\" }\n            ],\n            clearable: true\n          },\n          {\n            label: \"设备分类\",\n            type: \"select\",\n            value: \"sbfl\",\n            options: [],\n            clearable: true\n          },\n          { label: \"验收名称\", type: \"input\", value: \"jxfl\" },\n          { label: \"项目名称\", type: \"input\", value: \"xmmc\" },\n          // { label: '备注', type: 'input', value: 'bz' },\n          {\n            label: \"验收类型\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"yslx\",\n            options: [],\n            clearable: true\n          }\n        ]\n      },\n      currentUser: this.$store.getters.name,\n      //新增或修改标题\n      reminder: \"修改\",\n      //弹出框form表单每行显示几个\n      rows: 2,\n      //主表新增表单数据\n      formList: [\n        {\n          label: \"验收类型：\",\n          value: \"\",\n          type: \"select\",\n          name: \"yslx\",\n          default: true,\n          options: [],\n          rules: { required: true, message: \"请输入验收类型\" }\n        },\n        {\n          label: \"验收标准名称：\",\n          value: \"\",\n          type: \"textarea\",\n          name: \"jxfl\",\n          default: true,\n          rules: { required: true, message: \"请输入验收标准名称\" }\n        },\n        {\n          label: \"项目名称：\",\n          value: \"\",\n          type: \"input\",\n          name: \"xmmc\",\n          default: true,\n          rules: { required: true, message: \"请输入项目名称\" }\n        },\n        {\n          label: \"所属专业：\",\n          value: \"\",\n          type: \"selectChange1\",\n          name: \"spb\",\n          default: true,\n          options: [\n            { label: \"变电\", value: \"变电\" },\n            { label: \"配电\", value: \"配电\" },\n            { label: \"输电\", value: \"输电\" }\n          ],\n          rules: { required: true, message: \"请输入验收类型\" }\n        },\n        {\n          label: \"设备分类：\",\n          value: \"\",\n          type: \"select\",\n          name: \"sbfl\",\n          default: true,\n          options: [],\n          rules: { required: true, message: \"请输入设备分类\" }\n        },\n        /* {\n          label: '设备分类：',\n          value: '',\n          type: 'input',\n          name: 'sbfl',\n          default: true,\n          hidden: false,\n          rules: { required: true, message: '请输入设备分类' }\n        },*/\n        {\n          label: \"备注：\",\n          value: \"\",\n          name: \"bz\",\n          default: true,\n          type: \"textarea\",\n          rules: { required: false, message: \"请输入备注\" }\n        },\n\n        {\n          label: \"主键id:\",\n          value: \"\",\n          name: \"id\",\n          default: false,\n          type: \"input\",\n          hidden: false,\n          rules: { required: false }\n        }\n      ],\n      //主表加载\n      mainLoading: false,\n      //验收标准主表查询条件\n      mainParanms: {\n        pageNum: 1,\n        pageSize: 10,\n        total: 0\n      },\n      //验收标准主表数据\n      // mainTableData: [],\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageResize: '',\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: false,\n          serialNumber: false\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: 'yslxName', label: '验收类型',minWidth: '80'},\n          { prop: 'spb', label: '所属专业',minWidth: '80', custom: true},\n          { prop: 'sbflmc', label: '设备分类',minWidth: '80'},\n          { prop: 'jxfl', label: '验收名称',minWidth: '220'},\n          { prop: 'xmmc', label: '项目名称',minWidth: '100'},\n          { prop: 'bz', label: '备注',minWidth: '80'},\n        ]\n      },\n      //主表选中行数据\n      mainTableSelectRows: [],\n      //是否展示设备分类树\n      showDeviceTree: false,\n      valFlag: \"\",\n      //验收类型下拉框数据\n      yslxOptions: []\n    };\n  },\n  mounted() {\n    this.initDomain();\n  },\n  methods: {\n    sortChange({ column, prop, order }) {\n      if (order) {\n        if (order.indexOf(\"desc\") > -1) {\n          this.filterParams.mySorts = [{ prop: prop, asc: false }];\n        } else {\n          this.filterParams.mySorts = [{ prop: prop, asc: true }];\n        }\n      }\n      this.getData();\n    },\n    getData(params) {\n      this.filterParams = { ...this.filterParams, ...params };\n      const param = this.filterParams;\n      getBzYsbzzb(param).then(response => {\n        // this.mainTableData = response.data.records;\n        // this.mainParanms.total = response.data.total;\n        this.tableAndPageInfo.tableData = response.data.records;\n        this.tableAndPageInfo.pager.total = response.data.total\n        this.tableAndPageInfo.tableData.forEach(item => {\n          this.yslxOptions.forEach(element => {\n            if (item.yslx === element.value) {\n              item.yslxName = element.label;\n            }\n          });\n        });\n        if (response.data.total > 0) {\n          this.handleCurrentChange(response.data.records[0]);\n        }\n        this.mainLoading = false;\n      });\n    },\n    getSblxList(val) {\n      this.formList.map(async item => {\n        if (item.name === \"sbfl\") {\n          item.value = \"\";\n          let sblxParam = {\n            type: val + \"设备\"\n          };\n          item.options = await this.getSblxDataListSelected(sblxParam);\n        }\n      });\n    },\n    handleEvent(val, val1) {\n      if (val.label === \"spb\" && val.value && val.value !== \"\") {\n        this.$set(val1, \"sbfl\", \"\");\n        this.filterInfo.fieldList.map(async item => {\n          if (item.value === \"sbfl\") {\n            let sblxParam = {\n              type: val.value + \"设备\"\n            };\n            item.options = await this.getSblxDataListSelected(sblxParam);\n          }\n        });\n      }\n    },\n    /**\n     * 获取设备类型下拉框数据\n     */\n    getSblxDataListSelected(sblxParam) {\n      return getSblxDataListSelected(sblxParam).then(res => {\n        return res.data;\n      });\n    },\n    //重置按钮\n    getReset() {\n      this.filterParams = {};\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n      this.$refs.detail.getReset();\n    },\n\n    //获取验收标准主表数据\n    // getMainStandardData() {\n    //   this.mainLoading = true;\n    //   const param = { ...this.filterParams, ...this.mainParanms };\n    //   getBzYsbzzb(param).then(response => {\n    //     this.mainTableData = response.data.records;\n    //     this.mainParanms.total = response.data.total;\n    //     this.mainTableData.forEach(item => {\n    //       this.yslxOptions.forEach(element => {\n    //         if (item.yslx === element.value) {\n    //           item.yslxName = element.label;\n    //         }\n    //       });\n    //     });\n    //     if (response.data.total > 0) {\n    //       this.handleCurrentChange(response.data.records[0]);\n    //     }\n    //     this.mainLoading = false;\n    //   });\n    // },\n    //验收标准主表行点击事件逻辑\n    handleCurrentChange(val) {\n      // this.selectData = []\n      // 清空所有选择\n      // this.$refs.maintable.clearSelection();\n      //  选中当前选择\n      // this.$refs.maintable.toggleRowSelection(val);\n      // this.selectData.push(val)\n      // this.$refs.maintable.setCurrentRow(val);\n\n      //给子组件传值\n      if (this.valFlag === val) {\n        return;\n      }\n      this.valFlag = val;\n      this.$refs.detail.getMainTableSelectedRow([val]);\n    },\n    //复选框选中逻辑\n    handleSelectionChange(val) {\n      this.mainTableSelectRows = val;\n      console.log(this.mainTableSelectRows);\n      // this.$refs.detail.getMainTableSelectedRow(val)\n    },\n    //保存主表数据\n    saveMainData(formData) {\n      let message = \"\";\n      if (formData.id === \"\" || !formData.id) {\n        message = \"新增成功\";\n      } else {\n        message = \"修改成功\";\n      }\n      saveOrUpdateBzYsbzzb(formData).then(res => {\n        if (res.code === \"0000\") {\n          this.$message.success(message);\n          this.getData();\n        } else {\n          this.$message.error(\"操作失败\");\n        }\n      });\n    },\n    //新增主表数据\n    addMainData() {\n      this.reminder = \"新增\";\n      const addForm = this.formList.map(item => {\n        if (item.name === \"yslx\") {\n          item.options = this.yslxOptions;\n        }\n        return item;\n      });\n      this.$refs.dialogForm.showzzc(addForm);\n    },\n    //修改主表数据\n    updateMainData(row) {\n      this.getSblxList(row.spb);\n      const updateList = this.formList.map(item => {\n        item.value = row[item.name];\n        if (item.name === \"yslx\") {\n          item.options = this.yslxOptions;\n        }\n        return item;\n      });\n      this.reminder = \"修改\";\n      this.$refs.dialogForm.showzzc(updateList);\n    },\n    //查看主表数据详情\n    showMainData(row) {\n      this.getSblxList(row.spb);\n      const infoList = this.formList.map(item => {\n        item.value = row[item.name];\n        if (item.name === \"yslx\") {\n          item.options = this.yslxOptions;\n        }\n        return item;\n      });\n      this.reminder = \"详情\";\n      this.$refs.dialogForm.showxq(infoList);\n    },\n    //删除主表数据\n    deleteMainData(row) {\n      this.form = { ...row };\n      this.$confirm(\"确认删除选中数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          deleteBzYsbzzb([this.form.id]).then(res => {\n            if (res.code === \"0000\") {\n              this.$message({\n                message: \"删除成功\",\n                type: \"success\"\n              });\n            } else {\n              this.$message.error(\"操作失败\");\n            }\n            this.getData();\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    //初始话下拉框数据\n    async initDomain() {\n      let { data: yslx } = await getDictTypeData(\"yslx\");\n      this.yslxOptions = yslx;\n      this.filterInfo.fieldList.map(item => {\n        if (item.value === \"yslx\") {\n          return (item.options = yslx);\n        }\n      });\n      this.getData();\n    },\n\n    //input输入框鼠标聚焦事件\n    inputFocusEvent(val) {\n      this.isFilter = false;\n      if (val.target.name === \"sbflmc\") {\n        this.showDeviceTree = true;\n      }\n\n      if (val.target.name === \"sbfl\") {\n        this.showDeviceTree = true;\n        this.isFilter = true;\n      }\n    },\n    //获取设备分类树数据\n    getDeviceTypeData(res) {\n      if (res.length > 0) {\n        if (this.isFilter) {\n          let sbfl = \"\";\n          let sbflmc = \"\";\n          res.forEach(item => {\n            sbflmc += item.name + \",\";\n            sbfl += item.code + \",\";\n          });\n          this.filterInfo.data.sbfl = sbfl.substring(0, sbfl.length - 1);\n          this.filterInfo.data.sbflmc = sbflmc.substring(0, sbflmc.length - 1);\n          this.showDeviceTree = false;\n        } else {\n          if (res.length === 1) {\n            this.formList.forEach(item => {\n              if (item.name === \"sbflmc\") {\n                item.value = res[0].name;\n              }\n              if (item.name === \"sbfl\") {\n                item.value = res[0].code;\n              }\n            });\n            this.showDeviceTree = false;\n          } else {\n            this.$message.warning(\"请选择单条设备数据\");\n          }\n        }\n      } else {\n        this.showDeviceTree = false;\n      }\n    },\n    closeDeviceTypeDialog() {\n      this.showDeviceTree = false;\n    },\n    //导出excel\n    exportExcel() {\n      // if(!this.selectData.length > 0){\n      //   this.$message.warning('请在左侧勾选要导出的数据')\n      //   return\n      // }\n      let fileName = \"验收标准库\";\n      let exportUrl = \"/bzYsbzzb\";\n      exportExcel(exportUrl, this.filterParams, fileName);\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  height: calc(100vh - 225px);\n  overflow: auto;\n}\n\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.box-cardList {\n  height: 56%;\n}\n\n.item {\n  width: 200px;\n  float: left;\n}\n\n#main_container_dj {\n  height: calc(100vh - 84px);\n}\n\n.aside_height {\n  height: 96%;\n}\n\n.defect .el-form-item:nth-child(odd) {\n  margin-right: 70px;\n}\n\n/*背景颜色调整*/\n#main_container_dj,\n#main_container_dj .el-aside {\n  background-color: #b4caf1;\n}\n\n/deep/ #qxlr_dialog_insert .el-dialog__header {\n  background-color: #8eb3f5;\n}\n\n/deep/ .pmyBtn {\n  background: #8eb3f5;\n}\n</style>\n"]}]}