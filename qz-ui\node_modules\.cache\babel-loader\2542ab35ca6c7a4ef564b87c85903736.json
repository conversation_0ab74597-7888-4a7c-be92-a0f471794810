{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\dgTodoItem\\proclamationTodo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\dgTodoItem\\proclamationTodo.vue", "mtime": 1706897322043}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["proclamationTodo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAsGA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;AACA,EAAA,IAAA,EAAA,kBADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA;AAFA,OADA;AAQA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,YAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,kBAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,gBAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,CARA;AAcA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAdA,OARA;AAwBA,MAAA,UAAA,EAAA,EAxBA;AAyBA,MAAA,MAAA,EAAA;AACA,QAAA,IAAA,EAAA;AADA,OAzBA;AA4BA,MAAA,UAAA,EAAA,IA5BA;AA6BA,MAAA,UAAA,EAAA;AACA,QAAA,EAAA,EAAA,CADA;AAEA,QAAA,EAAA,EAAA;AAFA,OA7BA;AAiCA,MAAA,QAAA,EAAA,KAjCA;AAkCA,MAAA,QAAA,EAAA;AACA,QAAA,QAAA,EAAA;AADA,OAlCA;AAqCA,MAAA,UAAA,EAAA;AArCA,KAAA;AAuCA,GA1CA;AA2CA,EAAA,OA3CA,qBA2CA;AACA,SAAA,OAAA,CAAA,KAAA,MAAA,CAAA,KAAA;AACA,GA7CA;AA8CA,EAAA,OA9CA,qBA8CA,CAAA,CA9CA;AA+CA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,cAFA,0BAEA,EAFA,EAEA;AACA,sCAAA,EAAA;AACA,KAJA;AAKA,IAAA,OALA,mBAKA,KALA,EAKA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,GADA,+DACA,KADA,GACA,KAAA,CAAA,MADA;AAEA,gBAAA,GAAA,CAAA,QAAA,GAAA,KAAA,CAAA,MAAA,CAAA,OAAA,CAAA,IAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AAHA;AAAA,uBAIA,+BAAA,GAAA,CAJA;;AAAA;AAAA;AAIA,gBAAA,IAJA,wBAIA,IAJA;AAIA,gBAAA,IAJA,wBAIA,IAJA;;AAKA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,KAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,KAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AARA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,KAdA;AAeA,IAAA,YAfA,wBAeA,IAfA,EAeA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KAjBA;AAkBA,IAAA,WAlBA,uBAkBA,IAlBA,EAkBA,CAAA,CAlBA;AAoBA,IAAA,UApBA,sBAoBA,GApBA,EAoBA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,QAAA,mCAAA,GAAA;AACA,gBAAA,MAAA,CAAA,UAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,QAAA,GAAA,IAAA,CAHA,CAIA;;AAJA,sBAKA,GAAA,CAAA,MAAA,IAAA,CALA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAMA,kCAAA;AACA,kBAAA,QAAA,EAAA,GAAA,CAAA,EADA;AAEA,kBAAA,QAAA,EAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA;AAFA,iBAAA,CANA;;AAAA;AAAA;AAAA,uBAUA,MAAA,CAAA,OAAA,EAVA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,KAhCA;AAiCA,IAAA,SAjCA,uBAiCA;AACA,WAAA,QAAA,GAAA;AACA,QAAA,QAAA,EAAA;AADA,OAAA;AAGA,KArCA;AAsCA,IAAA,WAtCA,uBAsCA,GAtCA,EAsCA;AACA,WAAA,IAAA,GAAA,IAAA,KAAA,UAAA,EAAA;AACA,YAAA,GAAA,KAAA,GAAA,CAAA,IAAA,EAAA;AACA,eAAA,MAAA,CAAA,QAAA,GAAA,KAAA,UAAA,CAAA,GAAA,CAAA;AACA,eAAA,OAAA;AACA;AACA;AACA;AA7CA;AA/CA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-white>\n      <el-filter\n        ref=\"filter1\"\n        :data=\"filterInfo.data\"\n        :field-list=\"filterInfo.fieldList\"\n        :width=\"{ labelWidth: 180, itemWidth: 200 }\"\n        @handleReset=\"filterReset\"\n      ></el-filter>\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"selectChange\"\n        height=\"75vh\"\n      >\n        <el-table-column\n          slot=\"table_six\"\n          align=\"center\"\n          style=\"display: block;height: auto\"\n          label=\"状态\"\n          min-width=\"180\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-badge\n              :value=\"scope.row.isRead == 0 ? '未读' : '已读'\"\n              :type=\"scope.row.isRead == 0 ? 'danger' : 'primary'\"\n              class=\"item\"\n            >\n            </el-badge>\n          </template>\n        </el-table-column>\n        <el-table-column slot=\"table_seven\" label=\"附件\" prop=\"attachment\">\n          <template slot-scope=\"scope\">\n            <span v-if=\"scope.row.fileList.length > 0\">有附件</span>\n            <span v-else>无附件</span>\n          </template>\n        </el-table-column>\n        <el-table-column\n          slot=\"table_eight\"\n          align=\"center\"\n          fixed=\"right\"\n          style=\"display: block\"\n          label=\"操作\"\n          min-width=\"70\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" size=\"small\" @click=\"getDetails(scope.row)\"\n              >查看</el-button\n            >\n          </template>\n        </el-table-column>\n      </comp-table>\n    </el-white>\n    <el-dialog\n      title=\"公告详情\"\n      :visible.sync=\"openInfo\"\n      width=\"50%\"\n      append-to-body\n      :close-on-click-modal=\"false\"\n      v-dialogDrag\n    >\n      <el-form ref=\"formInfo\" :model=\"formInfo\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"标题：\" prop=\"title\">\n              <el-input v-model=\"formInfo.title\" :disabled=\"isDisabled\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"内容：\" prop=\"content\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"3\"\n                v-model=\"formInfo.content\"\n                :disabled=\"isDisabled\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-form-item\n            label=\"附件：\"\n            prop=\"attachment\"\n            v-if=\"formInfo.fileList.length > 0\"\n          >\n            <template slot-scope=\"scope\">\n              <span v-for=\"it in formInfo.fileList\">{{ it.fileOldName }}</span>\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"downloadHandle(formInfo.id)\"\n                >下载</el-button\n              >\n            </template>\n          </el-form-item>\n        </el-row>\n      </el-form>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getNoticePage, updateReadStatus } from \"@/api/activiti/DgTodoItem\";\nimport { downloadByBusinessId } from \"@/api/tool/file\";\nexport default {\n  name: \"proclamationTodo\",\n  data() {\n    return {\n      filterInfo: {\n        data: {},\n        fieldList: [\n          { label: \"公告标题\", type: \"input\", value: \"title\" },\n          { label: \"发布人\", type: \"input\", value: \"senderName\" }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"title\", label: \"公告标题\", minWidth: \"140\" },\n          { prop: \"senderName\", label: \"发布人\", minWidth: \"120\" },\n          { prop: \"publishStartTime\", label: \"发布时间\", minWidth: \"120\" },\n          { prop: \"publishEndTime\", label: \"结束时间\", minWidth: \"120\" }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      selectRows: [],\n      params: {\n        type: 0\n      },\n      activeName: \"db\",\n      tabRefresh: {\n        db: 0,\n        yb: 1\n      },\n      openInfo: false,\n      formInfo: {\n        fileList: []\n      },\n      isDisabled: false\n    };\n  },\n  created() {\n    this.getData(this.$route.query);\n  },\n  mounted() {},\n  methods: {\n    /**下载附件*/\n    downloadHandle(id) {\n      downloadByBusinessId(id);\n    },\n    async getData(param) {\n      let par = { ...param, ...this.params };\n      par.userName = this.$store.getters.name;\n      console.log(par);\n      let { code, data } = await getNoticePage(par);\n      if (code === \"0000\") {\n        this.tableAndPageInfo.tableData = data.records;\n        this.tableAndPageInfo.pager.total = data.total;\n      }\n    },\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n    filterReset(data) {},\n\n    async getDetails(row) {\n      this.formInfo = { ...row };\n      this.isDisabled = true;\n      this.openInfo = true;\n      //如果是未查看状态，点击查看时变成已查看\n      if (row.isRead == 0) {\n        await updateReadStatus({\n          noticeId: row.id,\n          userName: this.$store.getters.name\n        });\n        await this.getData();\n      }\n    },\n    closeForm() {\n      this.formInfo = {\n        fileList: []\n      };\n    },\n    handleClick(tab) {\n      for (let key in this.tabRefresh) {\n        if (key === tab.name) {\n          this.params.isHandle = this.tabRefresh[key];\n          this.getData();\n        }\n      }\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n/*  .el-badge {\n\n  /deep/.el-badge__content\n  {\n    !*&.is-fixed {\n      position: absolute;\n      right: 10px;\n      top: 0;\n      transform: translateX(100%) translateY(-50%);\n    }*!\n    margin-top: 0.45vh;\n    font-size: 10px;\n  }\n\n  }*/\n.item {\n  width: 8.5rem;\n  height: 1.25rem;\n}\n</style>\n"], "sourceRoot": "src/views/activiti/dgTodoItem"}]}