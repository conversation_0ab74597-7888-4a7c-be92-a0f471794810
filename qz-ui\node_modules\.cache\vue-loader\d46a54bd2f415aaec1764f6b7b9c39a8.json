{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\jxbzk.vue?vue&type=style&index=0&id=34e4e9e0&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\jxbzk.vue", "mtime": 1706897322614}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5ib3gtY2FyZHsKICBtYXJnaW4tYm90dG9tOiAxNXB4OwogIC5lbC1jYXJkX19oZWFkZXJ7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjM1LCAyNDUsIDI1NSkgIWltcG9ydGFudDsKICB9Cn0KLml0ZW17CiAgd2lkdGg6IDIwMHB4O2hlaWdodDogMTQ4cHg7IGZsb2F0OiBsZWZ0Owp9Cg=="}, {"version": 3, "sources": ["jxbzk.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2LA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "jxbzk.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl", "sourcesContent": ["<template>\n  <el-container id=\"main_container_dj\">\n    <el-container>\n      <el-main>\n        <el-card class=\"box-card\" shadow=\"never\" ref=\"search\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>监测项与阀值</span>\n          </div>\n          <div>\n            <el-col :span=\"24\" :xs=\"24\">\n              <div class=\"search-condition\">\n                <el-row :gutter=\"10\" class=\"mb8 pull-right\">\n                  <el-col :span=\"1.5\">\n                    <el-button type=\"primary\"\n                               @click=\"isSearchShow = isSearchShow?false:true\"\n                    >筛选</el-button>\n                  </el-col>\n                  <el-col :span=\"1.5\">\n                    <el-button class=\"mb8\" @click=\"addSensorButton\"\n                               type=\"primary \"\n                    >添加\n                    </el-button>\n                  </el-col>\n                  <el-col :span=\"1.5\">\n                    <el-button  class=\"mb8\" @click=\"updateSensorButton\"\n                                type=\"warning \"\n                    >修改</el-button>\n                  </el-col>\n                  <el-col :span=\"1.5\">\n                    <el-button class=\"mb8\" @click=\"deleteSensorButton\"\n                               type=\"danger\"\n                               :disabled=\"multipleSensor\">删除\n                    </el-button>\n                  </el-col>\n                </el-row>\n              <div class=\"clearfix\" />\n            </div>\n            </el-col>\n          </div>\n          <div>\n            <el-form :model=\"queryParams\" ref=\"queryForm\" v-show=\"isSearchShow\"  class=\"searchForm\" :inline=\"true\" label-width=\"120px\">\n              <el-form-item label=\"监测项目：\" prop=\"gzpType\">\n                <el-select v-model=\"queryParams.gzpType\"  placeholder=\"请选择监测项目\" clearable >\n                  <el-option v-for=\"item in gzpTypeOptions\"\n                             :key=\"item.value\"\n                             :label=\"item.label\"\n                             :value=\"item.value\"></el-option>\n                </el-select>\n              </el-form-item>\n              <el-form-item label=\"监测项：\" prop=\"ancuoTerm\">\n                <el-input v-model=\"queryParams.ancuoTerm\" placeholder=\"请输入监测项\" clearable class=\"common-width\"\n                          @keyup.enter.native=\"handleQuery\"/>\n              </el-form-item>\n              <el-form-item>\n                <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\n                <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\n              </el-form-item>\n            </el-form>\n          </div>\n        </el-card>\n\n        <el-card class=\"box-card\" shadow=\"never\">\n          <el-table stripe border v-loading=\"loading\" :data=\"userList\" @selection-change=\"handleSelectionChange\">\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n            <el-table-column label=\"监测项目\" align=\"center\" prop=\"userId\"/>\n            <el-table-column label=\"监测项\" align=\"center\" prop=\"userName\" :show-overflow-tooltip=\"true\"/>\n            <el-table-column label=\"结果上下线\" align=\"center\" prop=\"userName\" :show-overflow-tooltip=\"true\"/>\n            <el-table-column label=\"逻辑符\" align=\"center\" prop=\"userName\" :show-overflow-tooltip=\"true\"/>\n          </el-table>\n\n          <pagination v-show=\"total>0\"\n                      :total=\"total\"\n                      :page.sync=\"queryParams.pageNum\"\n                      :limit.sync=\"queryParams.pageSize\"\n                      @pagination=\"getList\"/>\n        </el-card>\n        <el-footer>\n        </el-footer>\n      </el-main>\n    </el-container>\n\n  </el-container>\n\n</template>\n\n<script>\n\n  export default {\n    name: \"jcbzk\",\n    data() {\n      return {\n        //组织树\n        treeOptions:[\n          {\n          label: '断路器',\n        }, {\n          label: '变压器',\n          children: [{\n            label: '冷却系统',\n            children: [{\n              label: '温控运行情况',\n\n            }, {\n              label: '油箱',\n\n            }, {\n              label: '铁芯',\n\n            }, {\n              label: '绕组',\n\n            }]\n          }]\n        }],\n        //删除是否可用\n        multipleSensor: true,\n        //查询参数\n        queryParams:{\n          pageNum: 1,\n          pageSize: 10,\n          ancuoTerm:''\n        },\n        //填入数据校验\n        rules: {\n          ancuoTerm: [\n          ]\n        },\n        //表单开关\n        isSearchShow:false,\n        //工作票类型下拉菜单\n        gzpTypeOptions:[\n          {\n            value: 'type1',\n            label: '类型1'\n          }, {\n            value: 'type2',\n            label: '类型2'\n          }\n        ]\n\n\n      };\n    },\n    watch: {\n    },\n    created() {\n      this.getList();\n\n    },\n    methods: {\n      //树节点点击事件\n      handleNodeClick(data) {\n\n      },\n      //添加按钮\n      addSensorButton(){\n\n      },\n      //编辑按钮\n      updateSensorButton(){\n\n      },\n      //删除按钮\n      deleteSensorButton(){\n\n      },\n      //导出按钮\n      handleExport(){\n\n      },\n      //查询列表\n      getList(){\n\n      },\n      //搜索\n      handleQuery(){\n\n      },\n      //重置\n      resetQuery(){\n        this.resetForm(\"queryForm\");\n      }\n    }\n  };\n</script>\n\n<style lang='scss' scoped>\n.box-card{\n  margin-bottom: 15px;\n  .el-card__header{\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n.item{\n  width: 200px;height: 148px; float: left;\n}\n</style>\n"]}]}