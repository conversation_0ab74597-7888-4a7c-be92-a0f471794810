{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\yjgzwh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\yjgzwh.vue", "mtime": 1706897323226}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["yjgzwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA", "file": "yjgzwh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n<!--    <el-white>-->\n<!--      <el-filter-->\n<!--        :data=\"filterInfo.data\"-->\n<!--        :field-list=\"filterInfo.fieldList\"-->\n<!--        @handleReset=\"getReset\"-->\n<!--      />-->\n<!--    </el-white>-->\n    <div>\n      <el-white class=\"button-group\">\n        <div class=\"button_btn\">\n          <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"getInster\"\n          >新增</el-button>\n          <el-button type=\"danger\" icon=\"el-icon-delete\"  @click=\"getDelete\"\n          >删除</el-button>\n          <!--              <el-button type=\"warning\" icon=\"el-icon-edit\" @click=\"getUpdate\"-->\n          <!--              >修改</el-button>-->\n          <!--              <el-button type=\"cyan\" icon=\"el-icon-download\" @click=\"getDetails\"-->\n          <!--              >导出</el-button>-->\n        </div>\n        <comp-table :table-and-page-info=\"tableAndPageInfo\"  @update:multipleSelection=\"selectChange\"/>\n\n      </el-white>\n    </div>\n\n    <!--新增、修改、详情弹框-->\n    <el-dialog :title=\"title\" :visible.sync=\"show\" width=\"50%\" append-to-body @close=\"getInsterClose\" v-dialogDrag>\n      <el-form label-width=\"130px\" ref=\"form\" :model=\"form\" :disabled=\"isDisabled\">\n        <el-row :gutter=\"8\" class=\"pull-left\" >\n          <el-col :span=\"12\">\n            <el-form-item  label=\"预警方式：\" prop=\"yjfs\">\n              <el-select placeholder=\"请选择预警方式\" v-model=\"form.yjfs\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item  label=\"开始评价结果：\" prop=\"kspjjg\">\n              <el-select placeholder=\"请选择开始评价结果\" v-model=\"form.kspjjg\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item  label=\"结束评价结果：\" prop=\"jspjjg\">\n              <el-select placeholder=\"请选择结束评价结果\" v-model=\"form.jspjjg\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item  label=\"预警消息：\" prop=\"yjxx\">\n              <el-input v-model=\"form.yjxx\" placeholder=\"请输入预警消息\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item  label=\"预警级别：\" prop=\"yjjb\">\n              <el-select placeholder=\"请选择预警级别\" v-model=\"form.yjjb\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item  label=\"评价导则：\" prop=\"pjdz\">\n              <el-select placeholder=\"请选择评价导则\" v-model=\"form.pjdz\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item  label=\"规则分类：\" prop=\"gzfl\">\n              <el-select placeholder=\"请选择规则分类\" v-model=\"form.gzfl\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"getInsterClose\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"saveRow\">保 存</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  import {getList,saveOrUpdate,remove,exportExcel} from '@/api/dagangOilfield/bzgl/sbztpjbzk/yjgzwh'\n    export default {\n      name: \"yjgzwh\",\n      data(){\n        return{\n          isDisabled:false,\n          selectRows:[],\n          params:{\n\n          },\n          //新增按钮form表单\n          form:{\n            yjfs:'',\n            kspjjg:'',\n            jspjjg:'',\n            yjxx:'',\n            yjjb:'',\n            pjdz:'',\n            gzfl:'',\n          },\n          title:'',\n          show:false,\n          filterInfo: {\n            data: {\n              ywdwArr: [],\n            },\n            fieldList: [\n              {label: '设备类型', type: 'select', value: 'roleName', multiple: true, options: []},\n            ]\n          },\n          tableAndPageInfo: {\n            pager: {\n              pageSize: 10,\n              pageNum: 1,\n              total: 0,\n              sizes: [10, 20, 50, 100]\n            },\n            option: {\n              checkBox: true,\n              serialNumber: true\n            },\n            tableData: [],\n            tableHeader: [\n              {prop: 'yjfs', label: '预警方式', minWidth: '120'},\n              {prop: 'kspjjg', label: '开始评价结果', minWidth: '180'},\n              {prop: 'jspjjg', label: '结束评价结果', minWidth: '120'},\n              {prop: 'yjxx', label: '预警消息', minWidth: '250'},\n              {prop: 'yjjb', label: '预警级别', minWidth: '250'},\n              {prop: 'pjdz', label: '评价导则', minWidth: '250'},\n              {prop: 'gzfl', label: '规则分类', minWidth: '250'},\n              {\n                prop: 'operation',\n                label: '操作',\n                minWidth: '130px',\n                style: {display: 'block'},\n                //操作列固定再右侧\n                fixed:'right',\n                operation: [\n                  {name: '修改', clickFun: this.getUpdate},\n                  {name: '详情', clickFun: this.getDetails},\n                ]\n              },\n            ]\n          },\n        }\n      },\n      mounted(){\n        this.getData()\n      },\n      methods:{\n        //列表查询\n        async getData(params){\n          try {\n            const param={...this.params,...params}\n            const {data,code} = await getList(param);\n            if(code==='0000'){\n              this.tableAndPageInfo.tableData=data.records\n              this.tableAndPageInfo.pager.total=data.total\n            }\n          }catch (e) {\n            console.log(e)\n          }\n        },\n        getDetails(row){\n          this.isDisabled = true\n          this.title='详情'\n          this.form={...row}\n          this.show=true\n        },\n\n        //新增按钮\n        getInster(){\n          this.isDisabled = false\n          this.resetForm('form')\n          this.show=true\n          this.title = '新增'\n        },\n        //修改按钮\n        getUpdate(row){\n          this.isDisabled = false\n          this.title = '修改'\n          this.form={...row}\n          this.show=true\n        },\n        //删除按钮\n        async getDelete(){\n          if(this.selectRows.length<1){\n            this.$message.warning(\"请选择正确的数据！！！\")\n            return\n          }\n          let ids=this.selectRows.map(item=>{return item.objId});\n          this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }).then(() => {\n            remove(ids).then(({code })=>{\n              if(code==='0000'){\n                this.$message({\n                  type: 'success',\n                  message: '删除成功!'\n                });\n                this.getData()\n              }else{\n                this.$message({\n                  type: 'error',\n                  message: '删除失败!'\n                });\n              }\n            })\n          }).catch(() => {\n            this.$message({\n              type: 'info',\n              message: '已取消删除'\n            });\n          });\n          this.getData()\n        },\n        //新增弹框关闭\n        getInsterClose(){\n          this.show=false\n          this.resetForm('form')\n        },\n        //确定按钮\n        async saveRow(){\n          try {\n            let {code}=await saveOrUpdate(this.form)\n            if(code==='0000'){\n              this.$message.success(\"操作成功\")\n            }\n          }catch (e) {\n            console.log(e)\n          }\n          //重置page页从1开始\n          this.tableAndPageInfo.pager.pageResize = 'Y'\n          this.getData()\n          this.show=false\n        },\n\n        //选择行\n        selectChange(rows){\n          this.selectRows=rows\n        },\n\n      }\n    }\n</script>\n\n<style scoped>\n\n</style>\n"]}]}