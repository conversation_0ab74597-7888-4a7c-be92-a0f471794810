{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\syzxm.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\syzxm.vue", "mtime": 1706897323692}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["syzxm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAiNA;;AAQA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,WAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,IADA;AAEA;AACA,MAAA,WAAA,EAAA,IAHA;AAIA;AACA,MAAA,KAAA,EAAA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,QAAA,EAAA,OAFA;AAGA,QAAA,MAAA,EAAA,gBAAA,IAAA,EAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA;AACA,mBAAA,IAAA;AACA;AACA;AAPA,OALA;AAcA;AACA,MAAA,sBAAA,EAAA,IAfA;AAgBA;AACA,MAAA,oBAAA,EAAA,KAjBA;AAkBA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,OAAA,EAAA,SADA;AAEA,QAAA,GAAA,EAAA;AAFA,OAnBA;AAuBA;AACA,MAAA,iBAAA,EAAA,IAxBA;AAyBA;AACA,MAAA,kBAAA,EAAA,KA1BA;AA2BA;AACA,MAAA,UAAA,EAAA,EA5BA;AA6BA;AACA,MAAA,mBAAA,EAAA,IA9BA;AA+BA;AACA,MAAA,WAAA,EAAA,EAhCA;AAiCA;AACA,MAAA,cAAA,EAAA;AACA,QAAA,OAAA,EAAA,SADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,OAAA,EAAA;AAHA,OAlCA;AAuCA;AACA,MAAA,QAAA,EAAA,CAxCA;AAyCA;AACA,MAAA,cAAA,EAAA,OA1CA;AA2CA;AACA,MAAA,eAAA,EAAA,KA5CA;AA8CA;AACA,MAAA,UAAA,EAAA,EA/CA;AAgDA;AACA,MAAA,KAAA,EAAA;AACA,QAAA,KAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AADA,OAjDA;AAsDA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,IAAA,EAAA,EADA;AAEA,UAAA,KAAA,EAAA;AAFA,SADA;AAKA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,MAFA;AAGA,UAAA,IAAA,EAAA,QAHA;AAIA,UAAA,OAAA,EAAA,CACA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WADA,EAEA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAFA,EAGA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAHA,EAIA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAJA,EAKA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WALA,EAMA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WANA,CAJA;AAYA,UAAA,SAAA,EAAA;AAZA,SADA,EAeA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,KAAA,EAAA,OAFA;AAGA,UAAA,IAAA,EAAA,OAHA;AAIA,UAAA,SAAA,EAAA;AAJA,SAfA;AALA,OAvDA;AAmFA;AACA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SARA,CASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AApBA,SARA;AA8BA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AA9BA,OApFA;AAoHA;AACA,MAAA,eAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OArHA;AA0HA,MAAA,MAAA,EAAA,EA1HA;AA2HA;AACA,MAAA,uBAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAJA,EAKA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OALA,EAMA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OANA,CA5HA;AAoIA;AACA,MAAA,sBAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,CArIA;AAyIA;AACA,MAAA,wBAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,CA1IA;AA8IA;AACA,MAAA,uBAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,GAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,CA/IA;AAoJA;AACA,MAAA,IAAA,EAAA,EArJA;AAsJA;AACA,MAAA,aAAA,EAAA,KAvJA;AAwJA;AACA,MAAA,UAAA,EAAA,KAzJA;AA0JA;AACA,MAAA,SAAA,EAAA,EA3JA;AA4JA;AACA,MAAA,KAAA,EAAA,EA7JA;AA+JA;AACA,MAAA,WAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA;AADA,OADA,EAGA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,QAAA,EAAA,CAAA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,QAAA,EAAA,CAAA;AACA,YAAA,KAAA,EAAA;AADA,WAAA,EAGA;AACA,YAAA,KAAA,EAAA;AADA,WAHA,EAMA;AACA,YAAA,KAAA,EAAA;AADA,WANA,EASA;AACA,YAAA,KAAA,EAAA;AADA,WATA;AAFA,SAAA;AAFA,OAHA,CAhKA;AAsLA;AACA,MAAA,cAAA,EAAA,IAvLA;AAwLA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,SAAA,EAAA;AAHA,OAzLA;AA8LA;AACA,MAAA,YAAA,EAAA,KA/LA;AAgMA;AACA,MAAA,cAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAIA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAJA;AAjMA,KAAA;AA4MA,GA/MA;AAgNA,EAAA,KAAA,EAAA,EAhNA;AAiNA,EAAA,OAjNA,qBAiNA;AACA,SAAA,OAAA;AAEA,GApNA;AAqNA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,QAFA,oBAEA,IAFA,EAEA,OAFA,EAEA;AAAA;;AACA,UAAA,YAAA,GAAA;AACA,QAAA,GAAA,EAAA,EADA;AAEA,QAAA,OAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,MAAA;AAFA,OAAA;;AAIA,UAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA;AACA,QAAA,YAAA,CAAA,GAAA,GAAA,IAAA;AACA,eAAA,KAAA,WAAA,CAAA,YAAA,EAAA,OAAA,CAAA;AACA;;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,YAAA,CAAA,GAAA,GAAA,IAAA,CAAA,IAAA,CAAA,IAAA;;AACA,QAAA,KAAA,CAAA,WAAA,CAAA,YAAA,EAAA,OAAA;AACA,OAHA,EAGA,GAHA,CAAA;AAKA,KAhBA;AAkBA;AACA,IAAA,WAnBA,uBAmBA,QAnBA,EAmBA,OAnBA,EAmBA;AACA,+CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,SAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,GAAA;AACA,YAAA,IAAA,EAAA,IAAA,CAAA,IADA;AAEA,YAAA,KAAA,EAAA,IAAA,CAAA,KAFA;AAGA,YAAA,EAAA,EAAA,IAAA,CAAA,EAHA;AAIA,YAAA,GAAA,EAAA,IAAA,CAAA,GAJA;AAKA,YAAA,IAAA,EAAA,KALA;AAMA,YAAA,IAAA,EAAA,IAAA,CAAA;AANA,WAAA;AAQA,UAAA,SAAA,CAAA,IAAA,CAAA,IAAA;AACA,SAVA;AAWA,QAAA,OAAA,CAAA,SAAA,CAAA;AACA,OAdA;AAeA,KAnCA;AAqCA;AACA,IAAA,eAtCA,2BAsCA,IAtCA,EAsCA;AACA,UAAA,IAAA,CAAA,KAAA,KAAA,GAAA,EAAA;AACA;AACA,aAAA,WAAA,GAAA,KAAA;AACA,aAAA,QAAA,GAAA,IAAA;AACA,aAAA,MAAA,GAAA,IAAA,CAAA,IAAA;AACA,aAAA,eAAA,CAAA,MAAA,GAAA,IAAA,CAAA,IAAA;AACA,aAAA,OAAA;AACA,OAPA,MAOA;AACA,aAAA,WAAA,GAAA,IAAA;AACA;AACA,KAjDA;AAmDA;AACA,IAAA,MApDA,oBAoDA;AACA,WAAA,UAAA,CAAA,GAAA,GAAA,SAAA;AACA,WAAA,kBAAA,GAAA,IAAA;AACA,KAvDA;AAwDA;AACA,IAAA,gBAzDA,8BAyDA;AAAA;;AACA,WAAA,UAAA,CAAA,OAAA,GAAA,KAAA,UAAA,CAAA,KAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,KAAA,UAAA;AACA,sCAAA,KAAA,UAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,OAAA;;AACA,UAAA,MAAA,CAAA,kBAAA,GAAA,KAAA;;AACA,UAAA,MAAA,CAAA,cAAA;AACA;AACA,OANA;AAOA,KAnEA;AAoEA;AACA,IAAA,iBArEA,+BAqEA;AACA,WAAA,kBAAA,GAAA,KAAA;AACA,KAvEA;AAwEA;AACA,IAAA,cAzEA,4BAyEA;AAAA;;AACA,WAAA,cAAA,CAAA,OAAA,GAAA,KAAA,UAAA,CAAA,KAAA;AACA,qCAAA,KAAA,cAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,QAAA,MAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,MAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,OAJA;AAKA,KAhFA;AAiFA;AACA,IAAA,wBAlFA,oCAkFA,IAlFA,EAkFA;AACA,WAAA,kBAAA,GAAA,IAAA;AAEA,KArFA;AAsFA;AACA,IAAA,SAvFA,uBAuFA;AAAA;;AACA,UAAA,KAAA,kBAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,aAAA;AACA;AACA;;AACA,UAAA,GAAA,GAAA,KAAA,kBAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA;AACA,OAFA,CAAA;AAGA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,kCAAA,GAAA,EAAA,IAAA,CAAA,gBAAA;AAAA,cAAA,IAAA,QAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,cAAA;AACA,WANA,MAMA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,cAAA;AACA;AACA,SAdA;AAeA,OApBA,EAoBA,KApBA,CAoBA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OAzBA;AA0BA,KAzHA;AA0HA;AACA,IAAA,SA3HA,uBA2HA;AACA,UAAA,KAAA,UAAA,CAAA,IAAA,IAAA,IAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,kBAAA;AACA,OAFA,MAEA;AACA;AACA,aAAA,eAAA,GAAA,IAAA;AACA,aAAA,cAAA,CAAA,OAAA,GAAA,KAAA,UAAA,CAAA,KAAA,CAHA,CAIA;;AACA,aAAA,cAAA;AACA;AACA,KArIA;AAsIA;AACA,IAAA,qBAvIA,iCAuIA,IAvIA,EAuIA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,mBAAA,GAAA,IAAA,CAAA,MAAA,IAAA,CAAA,CAFA,CAGA;;AACA,WAAA,UAAA,GAAA,IAAA,CAAA,CAAA,CAAA;AACA,KA5IA;AA6IA;AACA,IAAA,OA9IA,mBA8IA,MA9IA,EA8IA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,MAAA,CAAA,eAAA,+DAAA,MAAA,CAAA,eAAA,GAAA,MAAA;AACA,gBAAA,KAHA,GAGA,MAAA,CAAA,eAHA;AAAA;AAAA,uBAIA,4BAAA,KAAA,CAJA;;AAAA;AAAA;AAIA,gBAAA,IAJA,yBAIA,IAJA;AAIA,gBAAA,IAJA,yBAIA,IAJA;;AAKA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AARA;AAAA;;AAAA;AAAA;AAAA;AAUA,gBAAA,OAAA,CAAA,GAAA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,KA1JA;AA2JA;AACA,IAAA,cA5JA,0BA4JA,GA5JA,EA4JA;AACA;AACA,WAAA,aAAA,GAAA,IAAA,CAFA,CAGA;;AACA,WAAA,IAAA,GAAA,GAAA,CAJA,CAKA;;AACA,WAAA,UAAA,GAAA,IAAA,CANA,CAOA;;AACA,WAAA,KAAA,GAAA,IAAA;AACA,KArKA;AAsKA;AACA,IAAA,aAvKA,yBAuKA,GAvKA,EAuKA;AACA;AACA,WAAA,aAAA,GAAA,IAAA,CAFA,CAGA;;AACA,WAAA,IAAA,GAAA,GAAA,CAJA,CAKA;;AACA,WAAA,UAAA,GAAA,KAAA,CANA,CAOA;;AACA,WAAA,KAAA,GAAA,IAAA;AACA,KAhLA;AAiLA;AACA,IAAA,eAlLA,6BAkLA;AACA;AACA,WAAA,aAAA,GAAA,IAAA,CAFA,CAGA;;AACA,WAAA,IAAA,GAAA,EAAA,CAJA,CAKA;;AACA,WAAA,UAAA,GAAA,KAAA,CANA,CAOA;;AACA,WAAA,KAAA,GAAA,IAAA;AACA,KA3LA;AA4LA;AACA,IAAA,SA7LA,qBA6LA,GA7LA,EA6LA;AAAA;;AACA,WAAA,IAAA,GAAA,GAAA;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,2BAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,cAAA,IAAA,SAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,YAAA,MAAA,CAAA,OAAA;AACA,WAPA,MAOA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,YAAA,MAAA,CAAA,OAAA;AACA;AACA,SAhBA;AAiBA,OAtBA,EAsBA,KAtBA,CAsBA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OA3BA;AA6BA,KA5NA;AA6NA;AACA,IAAA,UA9NA,wBA8NA;AAAA;;AACA,WAAA,IAAA,CAAA,MAAA,GAAA,KAAA,MAAA;AACA,WAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,mCAAA,MAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,GAAA,CAAA,GAAA;;AACA,cAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,cAAA,MAAA,CAAA,OAAA;;AACA,cAAA,MAAA,CAAA,aAAA,GAAA,KAAA;AACA,aALA,MAKA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA;AACA;AACA,WATA;AAWA;AACA,OAdA;AAeA,KA/OA;AAgPA;AACA,IAAA,KAjPA,mBAiPA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,KAnPA;AAoPA;AACA,IAAA,QArPA,sBAqPA;AACA,WAAA,eAAA,GAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OAAA;AAKA,KA3PA;AA4PA;AACA,IAAA,kBA7PA,gCA6PA,CAEA,CA/PA;AAgQA;AACA,IAAA,YAjQA,0BAiQA,CAEA,CAnQA;AAqQA;AACA,IAAA,WAtQA,yBAsQA,CAEA,CAxQA;AAyQA;AACA,IAAA,UA1QA,wBA0QA;AACA,WAAA,SAAA,CAAA,WAAA;AACA,KA5QA;AA6QA;AACA,IAAA,WA9QA,yBA8QA;AAAA;;AACA,WAAA,IAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,YAAA;AACA,QAAA,MAAA,CAAA,IAAA,GAAA,MAAA,CAAA,QAAA,CAAA,IAAA,GAAA,IAAA;;AACA,QAAA,MAAA,CAAA,SAAA,CAAA,MAAA;AACA,OAHA;AAIA;AApRA;AArNA,C", "sourcesContent": ["<template>\n\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row :gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card class=\"aside_height\" shadow=\"never\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备类型</span>\n          </div>\n          <div style=\" overflow: auto;height: 84vh\">\n            <el-tree id=\"tree\"\n                     highlight-current\n                     :props=\"props\"\n                     :load=\"loadNode\"\n                     lazy\n                     :default-expanded-keys=\"['1']\"\n                     @node-expand=\"handleNodeClick\"\n                     @node-click=\"handleNodeClick\"\n            />\n          </div>\n        </el-card>\n      </el-col>\n\n\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 180, itemWidth: 300 }\"\n          @handleReset=\"getReset\"\n        />\n\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"addSensorButton\" :disabled=\"addDisabled\"\n            >新增\n            </el-button>\n            <!--<el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"gettableAndPageInforesizableDelete\"-->\n            <!--&gt;删除-->\n            <!--</el-button>-->\n            <!-- <el-button type=\"primary\" icon=\"el-icon-edit\" @click=\"addZxmKxz\" :disabled=\"whmjzButtonDisabled\"\n            >维护枚举项\n            </el-button> -->\n          </div>\n          <comp-table :table-and-page-info=\"tableAndPageInfo\"\n                      @update:multipleSelection=\"handleSelectionChange\"\n                      height=\"69.6vh\"\n          >\n            <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\"\n                             width=\"160\"\n                             :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button @click=\"undateDetails(scope.row)\" v-show=\"scope.row.createBy == currentUser\" type=\"text\"\n                           size=\"small\" title=\"修改\"  class='el-icon-edit'\n                >\n                </el-button>\n                <el-button @click=\"getDetailsInfo(scope.row)\" type=\"text\" size=\"small\" title=\"详情\" class=\"el-icon-view\"></el-button>\n                <el-button @click=\"getDelete(scope.row)\" v-show=\"scope.row.createBy == currentUser\" type=\"text\"\n                           size=\"small\" title=\"删除\" class=\"el-icon-delete\"\n                >\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!-- 详情/新增/修改 -->\n    <el-dialog :title=title :visible.sync=\"isShowDetails\" width=\"60%\" @close=\"handleClose\" v-dialogDrag>\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :disabled=\"isDisabled\" :rules=\"rules\">\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"子项目名称：\" prop=\"zxmmc\">\n              <el-input v-model=\"form.zxmmc\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"子项目描述：\" prop=\"zxmms\">\n              <el-input v-model=\"form.zxmms\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"数值单位：\" prop=\"szdw\">\n              <el-input v-model=\"form.szdw\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"数据精度：\" prop=\"sjjd\">\n              <el-input v-model=\"form.sjjd\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"结果类型：\" prop=\"jglx\">\n              <el-select v-model=\"form.jglx\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in jglxOptionsSelectedList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"计算列：\" prop=\"jsl\">\n              <el-select v-model=\"form.jsl\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in jslOptionsSelectedList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否为空：\" prop=\"sfkwk\">\n              <el-select v-model=\"form.sfkwk\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in sfkwkOptionsSelectedList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否显示：\" prop=\"sfxs\">\n              <el-select v-model=\"form.sfxs\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in sfxsOptionsSelectedList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button v-if=\"title==='新增' || title==='修改'\" type=\"primary\" @click=\"commitForm\">确 认</el-button>\n      </div>\n    </el-dialog>\n    <!--枚举项值弹出框-->\n    <el-dialog :title=\"mjxDialogTitle\" :visible.sync=\"isShowMjzDialog\" width=\"50%\" v-dialogDrag>\n      <el-white class=\"button-group\">\n        <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n          <el-col\n            style=\"display: flex;justify-content: space-between;align-items: center;\"\n          >\n            <div>\n              <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"addMjz\"\n              >新增\n              </el-button>\n              <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"deleteMjz\"\n              >删除\n              </el-button>\n            </div>\n          </el-col>\n        </el-row>\n      </el-white>\n      <el-table stripe border :data=\"mjzDataList\" @selection-change=\"handleSelectionMjzChange\"\n                :header-cell-style=\"{'text-align':'center'}\" :cell-style=\"{'text-align':'center'}\"\n      >\n        <el-table-column type=\"selection\" width=\"55\"/>\n        <el-table-column label=\"序号\" type=\"index\" width=\"50\">\n        </el-table-column>\n        <el-table-column label=\"枚举值\" prop=\"kxz\" :show-overflow-tooltip=\"true\"/>\n      </el-table>\n      <pagination\n        v-show=\"Mjztotal>0\"\n        :total=\"Mjztotal\"\n        :page.sync=\"mjzQueryParams.pageNum\"\n        :limit.sync=\"mjzQueryParams.pageSize\"\n        @pagination=\"getMjzDataList\"\n      />\n    </el-dialog>\n    <!--枚举值新增弹窗-->\n    <el-dialog :title=\"mjzAddDialogTitle\" :visible.sync=\"isShowMjzAddDialog\" width=\"30%\" v-dialogDrag>\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"mjzAddForm\" :disabled=\"mjzAddDialogDisabled\">\n        <el-row :gutter=\"8\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"枚举值：\" prop=\"zxmmc\">\n              <el-input v-model=\"mjzAddForm.kxz\"/>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" v-show=\"addMjzDialogButtonShow\">\n        <el-button @click=\"closeAddMjzDialog\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"commitAddMjzForm\">确 认</el-button>\n      </div>\n    </el-dialog>\n  </div>\n\n</template>\n\n<script>\nimport {\n  getPageDataList,\n  getPageKxzDataList,\n  remove,\n  removeKxzData,\n  saveOrUpdate,\n  saveOrUpdateKxzData\n} from '@/api/dagangOilfield/bzgl/syzxm'\nimport { getDeviceClassTreeNodeByPid } from '@/api/dagangOilfield/bzgl/sblxwh/sblxwh'\n\nexport default {\n  name: 'lpbzk',\n  data() {\n    return {\n      currentUser: this.$store.getters.name,\n      //新增按钮控制\n      addDisabled: true,\n      //树结构懒加载参数\n      props: {\n        label: 'name',\n        children: 'zones',\n        isLeaf: (data, node) => {\n          if (node.level === 2) {\n            return true\n          }\n        }\n      },\n      //枚举值新增弹出框底部按钮控制显示\n      addMjzDialogButtonShow: true,\n      //控制枚举值新增弹出框内容是否可编辑\n      mjzAddDialogDisabled: false,\n      //枚举值新增form表单\n      mjzAddForm: {\n        syzxmid: undefined,\n        kxz: undefined\n      },\n      //枚举值新增弹出框标题\n      mjzAddDialogTitle: '新增',\n      //枚举值新增弹出框控制\n      isShowMjzAddDialog: false,\n      //选中子项目时获取到的第一行数据用来查询枚举值\n      mjzRowForm: {},\n      //维护枚举值button按钮\n      whmjzButtonDisabled: true,\n      //枚举值数据\n      mjzDataList: [],\n      //枚举值参数\n      mjzQueryParams: {\n        syzxmid: undefined,\n        pageSize: 10,\n        pageNum: 1\n      },\n      //枚举值总数\n      Mjztotal: 0,\n      //枚举项弹出框标题\n      mjxDialogTitle: '枚举值维护',\n      //枚举项弹出框\n      isShowMjzDialog: false,\n\n      //删除选择列\n      selectRows: [],\n      //表单验证\n      rules: {\n        zxmmc: [\n          { required: true, message: '请输入子项目名称', trigger: 'blur' }\n        ]\n      },\n      //筛选框\n      filterInfo: {\n        data: {\n          jglx: '',\n          zxmmc: ''\n        },\n        fieldList: [\n          {\n            label: '结果类型',\n            value: 'jglx',\n            type: 'select',\n            options: [\n              { label: '图片', value: '图片' },\n              { label: '数字', value: '数字' },\n              { label: '日期', value: '日期' },\n              { label: '单选', value: '单选' },\n              { label: '枚举', value: '枚举' },\n              { label: '字符', value: '字符' }\n            ],\n            clearable: true\n          },\n          {\n            label: '子项目名称',\n            value: 'zxmmc',\n            type: 'input',\n            clearable: true\n          }\n        ]\n      },\n      //列表页\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: '子项目名称', prop: 'zxmmc', minWidth: '150' },\n          { label: '数值单位', prop: 'szdw', minWidth: '70' },\n          { label: '结果类型', prop: 'jglx', minWidth: '100' },\n          { label: '计算列', prop: 'jsl', minWidth: '60' },\n          { label: '是否可为空', prop: 'sfkwk', minWidth: '80' },\n          { label: '是否显示', prop: 'sfxs', minWidth: '70' },\n          { label: '子项目描述', prop: 'zxmms', minWidth: '150' },\n          { label: '数据精度', prop: 'sjjd', minWidth: '100' }\n          // {\n          //   prop: 'operation',\n          //   label: '操作',\n          //   minWidth: '100px',\n          //   style: {display: 'block'},\n          //   //操作列固定再右侧\n          //   fixed: 'right',\n          //   operation: [\n          //     {name: '修改', clickFun: this.undateDetails},\n          //     {name: '详情', clickFun: this.getDetailsInfo},\n          //   ]\n          // },\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      //查询试验子项目参数\n      querySyzxmParam: {\n        pageNum: 1,\n        pageSize: 10,\n        sblxbm: undefined\n      },\n      sblxbm: '',\n      //结果类型下拉框数据\n      jglxOptionsSelectedList: [\n        { label: '图片', value: '图片' },\n        { label: '数字', value: '数字' },\n        { label: '日期', value: '日期' },\n        { label: '单选', value: '单选' },\n        { label: '枚举', value: '枚举' },\n        { label: '字符', value: '字符' }\n      ],\n      //计算列\n      jslOptionsSelectedList: [\n        { label: '是', value: '是' },\n        { label: '否', value: '否' }\n      ],\n      //是否为空\n      sfkwkOptionsSelectedList: [\n        { label: '是', value: '是' },\n        { label: '否', value: '否' }\n      ],\n      //是否显示\n      sfxsOptionsSelectedList: [\n        { label: '是', value: '是' },\n        { label: '否', value: '否' }\n      ],\n\n      //form表单\n      form: {},\n      //是否显示弹框\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //表单数据\n      dataTable: [],\n      //标题\n      title: '',\n\n      //组织树\n      treeOptions: [\n        {\n          label: '断路器'\n        }, {\n          label: '变压器',\n          children: [{\n            label: '冷却系统',\n            children: [{\n              label: '温控运行情况'\n\n            }, {\n              label: '油箱'\n\n            }, {\n              label: '铁芯'\n\n            }, {\n              label: '绕组'\n\n            }]\n          }]\n        }],\n      //删除是否可用\n      multipleSensor: true,\n      //查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        ancuoTerm: ''\n      },\n      //表单开关\n      isSearchShow: false,\n      //工作票类型下拉菜单\n      gzpTypeOptions: [\n        {\n          value: 'type1',\n          label: '类型1'\n        }, {\n          value: 'type2',\n          label: '类型2'\n        }\n      ]\n\n    }\n  },\n  watch: {},\n  created() {\n    this.getData()\n\n  },\n  methods: {\n    //懒加载函数\n    loadNode(node, resolve) {\n      let TreeparamMap = {\n        pid: '',\n        spbLogo: ['输电设备', '变电设备','配电设备']\n      }\n      if (node.level === 0) {\n        TreeparamMap.pid = 'sb'\n        return this.getTreeNode(TreeparamMap, resolve)\n      }\n      setTimeout(() => {\n        TreeparamMap.pid = node.data.code\n        this.getTreeNode(TreeparamMap, resolve)\n      }, 500)\n\n    },\n\n    //获取树节点数据\n    getTreeNode(paramMap, resolve) {\n      getDeviceClassTreeNodeByPid(paramMap).then(res => {\n        let treeNodes = []\n        res.data.forEach(item => {\n          let node = {\n            name: item.name,\n            level: item.level,\n            id: item.id,\n            pid: item.pid,\n            leaf: false,\n            code: item.code\n          }\n          treeNodes.push(node)\n        })\n        resolve(treeNodes)\n      })\n    },\n\n    //树节点点击事件\n    handleNodeClick(data) {\n      if (data.level === '1') {\n        //开放新增按钮\n        this.addDisabled = false\n        this.treeForm = data\n        this.sblxbm = data.code\n        this.querySyzxmParam.sblxbm = data.code\n        this.getData()\n      } else {\n        this.addDisabled = true\n      }\n    },\n\n    //枚举值新增按钮\n    addMjz() {\n      this.mjzAddForm.kxz = undefined\n      this.isShowMjzAddDialog = true\n    },\n    //提交新增枚举值弹出框表单\n    commitAddMjzForm() {\n      this.mjzAddForm.syzxmid = this.mjzRowForm.objId\n      console.log(this.mjzAddForm)\n      saveOrUpdateKxzData(this.mjzAddForm).then(res => {\n        if (res.code == '0000') {\n          this.$message.success('操作成功！')\n          this.isShowMjzAddDialog = false\n          this.getMjzDataList()\n        }\n      })\n    },\n    //取消按钮(枚举值新增弹出框)\n    closeAddMjzDialog() {\n      this.isShowMjzAddDialog = false\n    },\n    //获取枚举值列表方法\n    getMjzDataList() {\n      this.mjzQueryParams.syzxmid = this.mjzRowForm.objId\n      getPageKxzDataList(this.mjzQueryParams).then(res => {\n        console.log(res)\n        this.Mjztotal = res.data.total\n        this.mjzDataList = res.data.records\n      })\n    },\n    //枚举值行选中事件\n    handleSelectionMjzChange(rows) {\n      this.selectedKxzDataRow = rows\n\n    },\n    //删除枚举值列表\n    deleteMjz() {\n      if (this.selectedKxzDataRow.length < 1) {\n        this.$message.warning('请选择正确的数据！！！')\n        return\n      }\n      let ids = this.selectedKxzDataRow.map(item => {\n        return item.objId\n      })\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        removeKxzData(ids).then(({ code }) => {\n          if (code === '0000') {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            })\n            this.getMjzDataList()\n          } else {\n            this.$message({\n              type: 'error',\n              message: '删除失败!'\n            })\n            this.getMjzDataList()\n          }\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        })\n      })\n    },\n    //维护枚举值按钮\n    addZxmKxz() {\n      if (this.mjzRowForm.jglx != '枚举') {\n        this.$message.warning('请选择结果类型为枚举类型的数据！')\n      } else {\n        //打开弹窗\n        this.isShowMjzDialog = true\n        this.mjzQueryParams.syzxmid = this.mjzRowForm.objId\n        //获取枚举值列表\n        this.getMjzDataList()\n      }\n    },\n    //行选中\n    handleSelectionChange(rows) {\n      this.selectRows = rows\n      this.whmjzButtonDisabled = rows.length != 1\n      //获取到当前行对象\n      this.mjzRowForm = rows[0]\n    },\n    //列表查询子项目列表\n    async getData(params) {\n      try {\n        this.querySyzxmParam = { ...this.querySyzxmParam, ...params }\n        const param = this.querySyzxmParam\n        const { data, code } = await getPageDataList(param)\n        if (code === '0000') {\n          this.tableAndPageInfo.tableData = data.records\n          this.tableAndPageInfo.pager.total = data.total\n        }\n      } catch (e) {\n        console.log(e)\n      }\n    },\n    //获取详情\n    getDetailsInfo(row) {\n      //打开弹窗\n      this.isShowDetails = true\n      //给表单赋值\n      this.form = row\n      //表单不可编辑\n      this.isDisabled = true\n      //设置弹出框标题\n      this.title = '详情'\n    },\n    //修改按钮\n    undateDetails(row) {\n      //打开弹窗\n      this.isShowDetails = true\n      //给表单赋值\n      this.form = row\n      //表单可编辑\n      this.isDisabled = false\n      //设置弹出框标题\n      this.title = '修改'\n    },\n    //添加按钮\n    addSensorButton() {\n      //打开弹窗\n      this.isShowDetails = true\n      //给表单置空\n      this.form = {}\n      //表单可编辑\n      this.isDisabled = false\n      //设置弹出框标题\n      this.title = '新增'\n    },\n    //删除按钮\n    getDelete(row) {\n      this.form = row\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        remove([this.form.objId]).then(({ code }) => {\n          if (code === '0000') {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            })\n            this.tableAndPageInfo.pager.pageResize = 'Y'\n            this.getData()\n          } else {\n            this.$message({\n              type: 'error',\n              message: '删除失败!'\n            })\n            this.tableAndPageInfo.pager.pageResize = 'Y'\n            this.getData()\n          }\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        })\n      })\n\n    },\n    //确认提交表单\n    commitForm() {\n      this.form.sblxbm = this.sblxbm\n      this.$refs['form'].validate((valid) => {\n        if (valid) {\n          saveOrUpdate(this.form).then(res => {\n            if (res.code === '0000') {\n              this.$message.success(res.msg)\n              this.tableAndPageInfo.pager.pageResize = 'Y'\n              this.getData()\n              this.isShowDetails = false\n            } else {\n              this.$message.error(res.msg)\n            }\n          })\n\n        }\n      })\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false\n    },\n    //定义重置方法\n    getReset() {\n      this.querySyzxmParam = {\n        pageNum: 1,\n        pageSize: 10,\n        sblxbm: undefined\n      }\n    },\n    //删除按钮\n    deleteSensorButton() {\n\n    },\n    //导出按钮\n    handleExport() {\n\n    },\n\n    //搜索\n    handleQuery() {\n\n    },\n    //重置\n    resetQuery() {\n      this.resetForm('queryForm')\n    },\n    //清空表单数据\n    handleClose() {\n      this.form = {}\n      this.$nextTick(() => {\n        this.form = this.$options.data().form\n        this.resetForm('form')\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk"}]}