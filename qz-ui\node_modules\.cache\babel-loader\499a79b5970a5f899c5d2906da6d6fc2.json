{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\jszlgl\\tzgl.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\jszlgl\\tzgl.vue", "mtime": 1706897325228}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZm9yLWVhY2giKTsKCnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5pbmNsdWRlcyIpOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmluZGV4LW9mIik7CgpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuam9pbiIpOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5Lm1hcCIpOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmZ1bmN0aW9uLm5hbWUiKTsKCnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYyIpOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5pbmNsdWRlcyIpOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5zcGxpdCIpOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuZm9yLWVhY2giKTsKCk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwOwoKdmFyIF90eXBlb2YyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvdHlwZW9mIikpOwoKdmFyIF9vYmplY3RTcHJlYWQyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvb2JqZWN0U3ByZWFkMiIpKTsKCnJlcXVpcmUoInJlZ2VuZXJhdG9yLXJ1bnRpbWUvcnVudGltZSIpOwoKdmFyIF9hc3luY1RvR2VuZXJhdG9yMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovU2hhbW1wb29sL3dvcmsvY29kZS9kZ3l0LzAxXHU0RUUzXHU3ODAxL3F6LXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2FzeW5jVG9HZW5lcmF0b3IiKSk7Cgp2YXIgX2F1dGggPSByZXF1aXJlKCJAL3V0aWxzL2F1dGgiKTsKCnZhciBfYnpnZmdsID0gcmVxdWlyZSgiQC9hcGkvZGFnYW5nT2lsZmllbGQvYnpnbC9iemdmZ2wvYnpnZmdsIik7Cgp2YXIgX2ZpbGUgPSByZXF1aXJlKCJAL2FwaS90b29sL2ZpbGUiKTsKCnZhciBfYWN0aXZpdGlfdHpnbCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiY29tL2FjdGl2aXRpX3R6Z2wiKSk7Cgp2YXIgX3Byb2Nlc3NUYXNrID0gcmVxdWlyZSgiQC9hcGkvYWN0aXZpdGkvcHJvY2Vzc1Rhc2siKTsKCnZhciBfdGltZUxpbmUgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoImNvbS90aW1lTGluZSIpKTsKCnZhciBfQ29tcFRhYmxlID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJjb20vQ29tcFRhYmxlIikpOwoKdmFyIF9kYXRhID0gcmVxdWlyZSgiQC9hcGkvc3lzdGVtL2RpY3QvZGF0YSIpOwoKdmFyIF92dWVUcmVlc2VsZWN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdCIpKTsKCnJlcXVpcmUoIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0L2Rpc3QvdnVlLXRyZWVzZWxlY3QuY3NzIik7Cgp2YXIgX3NkZ3QgPSByZXF1aXJlKCJAL2FwaS9kYWdhbmdPaWxmaWVsZC9hc3NldC9zZGd0Iik7Cgp2YXIgX3NkeGxTZWxlY3RlZCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC92aWV3cy9kYWdhbmdPaWxmaWVsZC94amdsL3NkeGovc2R4bFNlbGVjdGVkIikpOwoKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gewogIGNvbXBvbmVudHM6IHsKICAgIENvbXBUYWJsZTogX0NvbXBUYWJsZS5kZWZhdWx0LAogICAgdGltZUxpbmU6IF90aW1lTGluZS5kZWZhdWx0LAogICAgYWN0aXZpdGk6IF9hY3Rpdml0aV90emdsLmRlZmF1bHQsCiAgICBIaXN0b3J5TGlzdDogX3Byb2Nlc3NUYXNrLkhpc3RvcnlMaXN0LAogICAgdHJlZXNlbGVjdDogX3Z1ZVRyZWVzZWxlY3QuZGVmYXVsdCwKICAgIHNkeGxTZWxlY3RlZDogX3NkeGxTZWxlY3RlZC5kZWZhdWx0CiAgfSwKICBuYW1lOiAidHpnbCIsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHZhciBfdGhpcyA9IHRoaXM7CgogICAgdmFyIHZhbGlkYXRlUGFzcyA9IGZ1bmN0aW9uIHZhbGlkYXRlUGFzcyhydWxlLCB2YWx1ZSwgY2FsbGJhY2spIHsKICAgICAgaWYgKCFfdGhpcy5mb3JtLnhsKSB7CiAgICAgICAgY2FsbGJhY2sobmV3IEVycm9yKCkpOwogICAgICB9IGVsc2UgewogICAgICAgIGNhbGxiYWNrKCk7CiAgICAgIH0KICAgIH07CgogICAgcmV0dXJuIHsKICAgICAgY3VycmVudFVzZXI6IHRoaXMuJHN0b3JlLmdldHRlcnMubmFtZSwKICAgICAgLy/nur/ot6/pgInmi6nlvLnlh7oKICAgICAgaXNTaG93WGxEaWFsb2c6IGZhbHNlLAogICAgICBzaG93VHpGbGFnOiBmYWxzZSwKICAgICAgeGxEYXRhOiBbXSwKICAgICAgZGlhbG9nSW1hZ2VVcmw6ICIiLAogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgc2F2ZUxvYWRpbmc6IGZhbHNlLAogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgKICAgICAgc2luZ2xlOiB0cnVlLAogICAgICAvL+e6v+i3r+agkeaVsOaNrgogICAgICB0cmVlT3B0aW9uczogW10sCiAgICAgIC8vdGFi6aG15ZCN56ewCiAgICAgIGFjdGl2ZU5hbWU6ICIxMTAiLAogICAgICBhY3RpdmVOYW1lVG93OiAiMSIsCiAgICAgIGR5ZGpMaXN0OiBbXSwKICAgICAgYnV0dG9uTmFtZTogIiIsCiAgICAgIGJ1dHRvbk5hbWVTaG93OiBmYWxzZSwKICAgICAgdGltZUxpbmVTaG93OiBmYWxzZSwKICAgICAgdGltZURhdGE6IFtdLAogICAgICBvcGVuTG9hZGluZ0ltZzogZmFsc2UsCiAgICAgIGltZ1NyYzogIiIsCiAgICAgIGFjdGl2aXRpT3B0aW9uOiB7CiAgICAgICAgdGl0bGU6ICLkuIrmiqUiCiAgICAgIH0sCiAgICAgIGlzU2hvdzogZmFsc2UsCiAgICAgIHByb2Nlc3NEYXRhOiB7CiAgICAgICAgcHJvY2Vzc0RlZmluaXRpb25LZXk6ICJ0enNwbGMiLAogICAgICAgIGJ1c2luZXNzS2V5OiAiIiwKICAgICAgICBidXNpbmVzc1R5cGU6ICLlm77nurjlrqHmibnmtYHnqIsiLAogICAgICAgIHZhcmlhYmxlczoge30sCiAgICAgICAgbmV4dFVzZXI6ICIiLAogICAgICAgIGRlZmF1bHRGcm9tOiB0cnVlLAogICAgICAgIHByb2Nlc3NUeXBlOiAiY29tcGxldGUiCiAgICAgIH0sCiAgICAgIGlzRGlzYWJsZWQ6IGZhbHNlLAogICAgICBpc2xhc3Q6ICIxIiwKICAgICAgcGFyYW1zOiB7CiAgICAgICAgd2pseDogIjEiLAogICAgICAgIGZicnE6ICIiLAogICAgICAgIHp4cnE6ICIiLAogICAgICAgIHN0YXR1czogIiIsCiAgICAgICAgd2pseExpc3Q6IFtdLAogICAgICAgIHN0YXR1c0xpc3Q6IFtdCiAgICAgIH0sCiAgICAgIHRpdGxlOiAiIiwKICAgICAgc2VsZWN0Um93czogW10sCiAgICAgIGlkczogW10sCiAgICAgIGZvcm06IHsKICAgICAgICB3amx4OiAiMSIsCiAgICAgICAgZmlsZUxpc3Q6IFtdLAogICAgICAgIHhsOiAiIgogICAgICB9LAogICAgICBmaWx0ZXJJbmZvOiB7CiAgICAgICAgZGF0YTogewogICAgICAgICAgLy8gd2pseDogJzEnLAogICAgICAgICAgZmJycUFycjogIiIsCiAgICAgICAgICB6eHJxQXJyOiAiIiwKICAgICAgICAgIHN0YXR1czogIiIsCiAgICAgICAgICB3amx4TGlzdDogW10sCiAgICAgICAgICBzdGF0dXNMaXN0OiBbXSwKICAgICAgICAgIHdqYmg6ICIiLAogICAgICAgICAgd2ptYzogIiIKICAgICAgICB9LAogICAgICAgIGZpZWxkTGlzdDogW3sKICAgICAgICAgIGxhYmVsOiAi5Zu+57q4566h55CG5paH5Lu257yW5Y+3IiwKICAgICAgICAgIHR5cGU6ICJpbnB1dCIsCiAgICAgICAgICB2YWx1ZTogIndqYmgiCiAgICAgICAgfSwgewogICAgICAgICAgbGFiZWw6ICLlm77nurjnrqHnkIbmlofku7blkI3np7AiLAogICAgICAgICAgdHlwZTogImlucHV0IiwKICAgICAgICAgIHZhbHVlOiAid2ptYyIKICAgICAgICB9LCB7CiAgICAgICAgICBsYWJlbDogIueKtuaAgSIsCiAgICAgICAgICB0eXBlOiAic2VsZWN0IiwKICAgICAgICAgIHZhbHVlOiAic3RhdHVzTGlzdCIsCiAgICAgICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgICAgIG9wdGlvbnM6IFtdCiAgICAgICAgfV0KICAgICAgfSwKICAgICAgdGFibGVBbmRQYWdlSW5mbzogewogICAgICAgIHBhZ2VyOiB7CiAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgdG90YWw6IDAsCiAgICAgICAgICBzaXplczogWzEwLCAyMCwgNTAsIDEwMF0KICAgICAgICB9LAogICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgdGFibGVIZWFkZXI6IFt7CiAgICAgICAgICBwcm9wOiAid2piaCIsCiAgICAgICAgICBsYWJlbDogIuWbvue6uOeuoeeQhuaWh+S7tue8luWPtyIsCiAgICAgICAgICBtaW5XaWR0aDogIjE2MCIsCiAgICAgICAgICBjdXN0b206IHRydWUKICAgICAgICB9LCB7CiAgICAgICAgICBwcm9wOiAid2ptYyIsCiAgICAgICAgICBsYWJlbDogIuWbvue6uOeuoeeQhuaWh+S7tuWQjeensCIsCiAgICAgICAgICBtaW5XaWR0aDogIjE2MCIKICAgICAgICB9LCAvLyB7IHByb3A6ICd3amx4TmFtZScsIGxhYmVsOiAn5paH5Lu257G75Z6LJywgbWluV2lkdGg6ICcxMjAnIH0sCiAgICAgICAgewogICAgICAgICAgcHJvcDogImZicnEiLAogICAgICAgICAgbGFiZWw6ICLlj5HluIPml6XmnJ8iLAogICAgICAgICAgbWluV2lkdGg6ICIxMjAiCiAgICAgICAgfSwgewogICAgICAgICAgcHJvcDogInp4cnEiLAogICAgICAgICAgbGFiZWw6ICLmiafooYzml6XmnJ8iLAogICAgICAgICAgbWluV2lkdGg6ICIxMjAiCiAgICAgICAgfSwgewogICAgICAgICAgcHJvcDogImZiciIsCiAgICAgICAgICBsYWJlbDogIuWPkeW4g+S6uiIsCiAgICAgICAgICBtaW5XaWR0aDogIjEyMCIKICAgICAgICB9LCAvLyB7IHByb3A6ICd6eHInLCBsYWJlbDogJ+aJp+ihjOS6uicsIG1pbldpZHRoOiAnMTIwJyB9LAogICAgICAgIHsKICAgICAgICAgIHByb3A6ICJzY3NqIiwKICAgICAgICAgIGxhYmVsOiAi5LiK5Lyg5pe26Ze0IiwKICAgICAgICAgIG1pbldpZHRoOiAiMTQwIiwKICAgICAgICAgIGN1c3RvbTogdHJ1ZQogICAgICAgIH0sIHsKICAgICAgICAgIHByb3A6ICJzdGF0dXNOYW1lIiwKICAgICAgICAgIGxhYmVsOiAi54q25oCBIiwKICAgICAgICAgIG1pbldpZHRoOiAiMTAwIgogICAgICAgIH1dLAogICAgICAgIG9wdGlvbjogewogICAgICAgICAgY2hlY2tCb3g6IHRydWUsCiAgICAgICAgICBzZXJpYWxOdW1iZXI6IHRydWUKICAgICAgICB9CiAgICAgIH0sCiAgICAgIEhpZ2hsaWdodFNob3c6IHRydWUsCiAgICAgIGRlZmF1bHRQcm9wczoge30sCiAgICAgIGZpbGVPcGVuOiBmYWxzZSwKICAgICAgLy8g5paH5Lu25LiK5Lyg5pWw5o2uCiAgICAgIHVwbG9hZERhdGE6IHsKICAgICAgICB0eXBlOiAiIiwKICAgICAgICBidXNpbmVzc0lkOiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgaW1nTGlzdDogW10sCiAgICAgIC8vIOaWh+S7tuS4iuS8oOivt+axguWktAogICAgICB1cEhlYWRlcjogewogICAgICAgIHRva2VuOiAoMCwgX2F1dGguZ2V0VG9rZW4pKCkKICAgICAgfSwKICAgICAgdmFsdWU6IG51bGwsCiAgICAgIC8v5paH5Lu257G75Z6L5LiL5ouJ5qGG5pWw5o2uCiAgICAgIC8vIGZpbGVUeXBlT3B0aW9uczogW10sCiAgICAgIC8v54q25oCB5LiL5ouJ5qGG5pWw5o2uCiAgICAgIHN0YXR1c09wdGlvbnM6IFt7CiAgICAgICAgdmFsdWU6IDAsCiAgICAgICAgbGFiZWw6ICLnur/ot6/liIblhazlj7jlvoXkuIrmiqUiCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogMSwKICAgICAgICBsYWJlbDogIue6v+i3r+WIhuWFrOWPuOmihuWvvOWuoeaJuSIKICAgICAgfSwgewogICAgICAgIHZhbHVlOiAyLAogICAgICAgIGxhYmVsOiAi55Sf5Lqn5oqA5pyv56eR5LiT5bel56Gu6K6kIgogICAgICB9LCB7CiAgICAgICAgdmFsdWU6IDMsCiAgICAgICAgbGFiZWw6ICLnlJ/kuqfmioDmnK/np5Hpooblr7zlrqHmibkiCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogNSwKICAgICAgICBsYWJlbDogIueUteWKm+iwg+W6puacquaOpeaUtu+8jOenkeaKgOS4reW/g+acquaOpeaUtiIKICAgICAgfSwgewogICAgICAgIHZhbHVlOiA2LAogICAgICAgIGxhYmVsOiAi55S15Yqb6LCD5bqm5bey5o6l5pS244CB56eR5oqA5Lit5b+D5pyq5o6l5pS2IgogICAgICB9LCB7CiAgICAgICAgdmFsdWU6IDcsCiAgICAgICAgbGFiZWw6ICLnp5HmioDkuK3lv4Plt7LmjqXmlLbjgIHnlLXlipvosIPluqbmnKrmjqXmlLYiCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogNCwKICAgICAgICBsYWJlbDogIue7k+adnyIKICAgICAgfV0sCiAgICAgIC8v5qCh6aqM6KeE5YiZCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgd2piaDogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuaWh+S7tue8luWPt+S4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICB3am1jOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5paH5Lu25ZCN56ew5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGZicjogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuWPkeW4g+S6uuS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICAvLyB6eHI6IFsKICAgICAgICAvLyAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfmiafooYzkurrkuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnYmx1cicgfQogICAgICAgIC8vIF0sCiAgICAgICAgenhycTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuaJp+ihjOaXpeacn+S4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAic2VsZWN0IgogICAgICAgIH1dLAogICAgICAgIGZicnE6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLlj5HluIPml6XmnJ/kuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogInNlbGVjdCIKICAgICAgICB9XSwKICAgICAgICBzY3NqOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5LiK5Lyg5pe26Ze05LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJzZWxlY3QiCiAgICAgICAgfV0sCiAgICAgICAgZHlkajogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAic2VsZWN0IgogICAgICAgIH1dLAogICAgICAgIHhsOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5LiN6IO95Li656m6IiwKICAgICAgICAgIHZhbGlkYXRvcjogdmFsaWRhdGVQYXNzLAogICAgICAgICAgdHJpZ2dlcjogImNoYW5nZSIKICAgICAgICB9XQogICAgICB9CiAgICB9OwogIH0sCiAgd2F0Y2g6IHt9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB2YXIgX3RoaXMyID0gdGhpczsKCiAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSggLyojX19QVVJFX18qL3JlZ2VuZXJhdG9yUnVudGltZS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUoKSB7CiAgICAgIHZhciBfeWllbGQkZ2V0RGljdFR5cGVEYXQsIGRnX2R5ZGo7CgogICAgICByZXR1cm4gcmVnZW5lcmF0b3JSdW50aW1lLndyYXAoZnVuY3Rpb24gX2NhbGxlZSQoX2NvbnRleHQpIHsKICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgc3dpdGNoIChfY29udGV4dC5wcmV2ID0gX2NvbnRleHQubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDI7CiAgICAgICAgICAgICAgcmV0dXJuICgwLCBfZGF0YS5nZXREaWN0VHlwZURhdGEpKCJkZ19keWRqIik7CgogICAgICAgICAgICBjYXNlIDI6CiAgICAgICAgICAgICAgX3lpZWxkJGdldERpY3RUeXBlRGF0ID0gX2NvbnRleHQuc2VudDsKICAgICAgICAgICAgICBkZ19keWRqID0gX3lpZWxkJGdldERpY3RUeXBlRGF0LmRhdGE7CiAgICAgICAgICAgICAgX3RoaXMyLmR5ZGpMaXN0ID0gZGdfZHlkajsKCiAgICAgICAgICAgICAgX3RoaXMyLnRyZWVMaXN0KCk7CgogICAgICAgICAgICAgIF90aGlzMi5pbml0RG9tYWluKCk7CgogICAgICAgICAgICBjYXNlIDc6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LnN0b3AoKTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0sIF9jYWxsZWUpOwogICAgfSkpKCk7CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdGhpcy5nZXREYXRhKHRoaXMuJHJvdXRlLnF1ZXJ5KTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGhhbmRsZVJlbW92ZTogZnVuY3Rpb24gaGFuZGxlUmVtb3ZlKGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgIGlmIChmaWxlLmZpbGVJZCkgewogICAgICAgIHRoaXMuZGVsZXRlRmlsZUJ5SWQoZmlsZSk7CiAgICAgICAgdGhpcy5mb3JtLmZpbGVMaXN0ID0gZmlsZUxpc3Q7CiAgICAgIH0KICAgIH0sCiAgICBoYW5kbGVDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZUNoYW5nZShmaWxlLCBmaWxlTGlzdCkgewogICAgICB0aGlzLmltZ0xpc3QgPSBmaWxlTGlzdDsKICAgIH0sCiAgICBoYW5kbGVQaWN0dXJlQ2FyZFByZXZpZXc6IGZ1bmN0aW9uIGhhbmRsZVBpY3R1cmVDYXJkUHJldmlldyhmaWxlKSB7CiAgICAgIC8vIHRoaXMuZGlhbG9nSW1hZ2VVcmwgPSBmaWxlLnVybDsKICAgICAgdGhpcy5kaWFsb2dWaXNpYmxlID0gdHJ1ZTsKICAgIH0sCiAgICAvL+WFs+mXree6v+i3r+W8ueeqlwogICAgaGFuZGxlQ29udHJvbFN5c2JTZWxlY3REaWFsb2c6IGZ1bmN0aW9uIGhhbmRsZUNvbnRyb2xTeXNiU2VsZWN0RGlhbG9nKGlzU2hvdykgewogICAgICB0aGlzLmlzU2hvd1hsRGlhbG9nID0gaXNTaG93OwogICAgfSwKICAgIC8v57q/6Lev6YCJ5oup5LqL5Lu2CiAgICBzeXNiU2VsZWN0ZWRDbGljazogZnVuY3Rpb24gc3lzYlNlbGVjdGVkQ2xpY2soKSB7CiAgICAgIGlmICh0aGlzLmlzRGlzYWJsZWQpIHsKICAgICAgICB0aGlzLmlzU2hvd1hsRGlhbG9nID0gZmFsc2U7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5pc1Nob3dYbERpYWxvZyA9IHRydWU7CiAgICAgIH0KICAgIH0sCiAgICAvL+e6v+i3r+mAieaLqeaOpeaUtuaVsOaNrgogICAgaGFuZGxlQWNjZXB0U2JEYXRhOiBmdW5jdGlvbiBoYW5kbGVBY2NlcHRTYkRhdGEoc2JEYXRhLCBpbmRleCkgewogICAgICB0aGlzLmZvcm0ueGwgPSBzYkRhdGEubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgcmV0dXJuIGl0ZW0ubGFiZWw7CiAgICAgIH0pLmpvaW4oIiwiKTsKICAgIH0sCiAgICBnZXRQYWdlOiBmdW5jdGlvbiBnZXRQYWdlKHhsbWMpIHsKICAgICAgdmFyIHF1ZXJ5ID0gewogICAgICAgIGxpbmVOYW1lOiB4bG1jCiAgICAgIH07CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICBwYXRoOiB0aGlzLnBhdGgsCiAgICAgICAgcXVlcnk6IHF1ZXJ5CiAgICAgIH0pOwogICAgICBjb25zb2xlLmxvZyh4bG1jKTsKICAgIH0sCiAgICBzb3J0Q2hhbmdlOiBmdW5jdGlvbiBzb3J0Q2hhbmdlKF9yZWYpIHsKICAgICAgdmFyIGNvbHVtbiA9IF9yZWYuY29sdW1uLAogICAgICAgICAgcHJvcCA9IF9yZWYucHJvcCwKICAgICAgICAgIG9yZGVyID0gX3JlZi5vcmRlcjsKCiAgICAgIGlmIChvcmRlcikgewogICAgICAgIGlmIChvcmRlci5pbmRleE9mKCJkZXNjIikgPiAtMSkgewogICAgICAgICAgdGhpcy5wYXJhbXMubXlTb3J0cyA9IFt7CiAgICAgICAgICAgIHByb3A6IHByb3AsCiAgICAgICAgICAgIGFzYzogZmFsc2UKICAgICAgICAgIH1dOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLnBhcmFtcy5teVNvcnRzID0gW3sKICAgICAgICAgICAgcHJvcDogcHJvcCwKICAgICAgICAgICAgYXNjOiB0cnVlCiAgICAgICAgICB9XTsKICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5wYXJhbXMubXlTb3J0cyA9IFt7CiAgICAgICAgICBwcm9wOiAidXBkYXRlVGltZSIsCiAgICAgICAgICBhc2M6IGZhbHNlCiAgICAgICAgfV07CiAgICAgIH0KCiAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgfSwKICAgIC8v6I635Y+W5qCR57uT5p6E5pWw5o2uCiAgICB0cmVlTGlzdDogZnVuY3Rpb24gdHJlZUxpc3QoKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwoKICAgICAgKDAsIF9zZGd0LmdldFRyZWVMaXN0KSgpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzMy50cmVlT3B0aW9ucyA9IHJlcy5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVDbGljazogZnVuY3Rpb24gaGFuZGxlQ2xpY2soKSB7CiAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci5wYWdlUmVzaXplID0gIlkiOwogICAgICB0aGlzLmZpbHRlclJlc2V0KCk7CiAgICAgIHRoaXMuZ2V0RGF0YSh0aGlzLiRyb3V0ZS5xdWVyeSk7CiAgICB9LAogICAgZ2V0U2hvdzogZnVuY3Rpb24gZ2V0U2hvdyhyb3cpIHsKICAgICAgc3dpdGNoIChyb3cuc3RhdHVzKSB7CiAgICAgICAgY2FzZSAwOgogICAgICAgICAgdGhpcy5idXR0b25OYW1lID0gIuaPkCDkuqQiOwoKICAgICAgICAgIGlmICh0aGlzLiRzdG9yZS5nZXR0ZXJzLm5hbWUgPT09IHRoaXMuZm9ybS5jcmVhdGVCeSkgewogICAgICAgICAgICB0aGlzLmJ1dHRvbk5hbWVTaG93ID0gdHJ1ZTsKICAgICAgICAgIH0KCiAgICAgICAgICBicmVhazsKCiAgICAgICAgY2FzZSAxOgogICAgICAgICAgdGhpcy5idXR0b25OYW1lID0gIumAmiDov4ciOwoKICAgICAgICAgIGlmICh0aGlzLiRzdG9yZS5nZXR0ZXJzLm5hbWUgPT09IHRoaXMuZm9ybS5mZ3NzcHIpIHsKICAgICAgICAgICAgdGhpcy5idXR0b25OYW1lU2hvdyA9IHRydWU7CiAgICAgICAgICB9CgogICAgICAgICAgYnJlYWs7CgogICAgICAgIGNhc2UgMjoKICAgICAgICAgIHRoaXMuYnV0dG9uTmFtZSA9ICLpgJog6L+HIjsKCiAgICAgICAgICBpZiAodGhpcy4kc3RvcmUuZ2V0dGVycy5uYW1lID09PSB0aGlzLmZvcm0uc2NremdzcHIpIHsKICAgICAgICAgICAgdGhpcy5idXR0b25OYW1lU2hvdyA9IHRydWU7CiAgICAgICAgICB9CgogICAgICAgICAgYnJlYWs7CgogICAgICAgIGNhc2UgMzoKICAgICAgICAgIHRoaXMuYnV0dG9uTmFtZSA9ICLpgJog6L+HIjsKCiAgICAgICAgICBpZiAodGhpcy4kc3RvcmUuZ2V0dGVycy5uYW1lID09PSB0aGlzLmZvcm0uc2NrbGRzcHIpIHsKICAgICAgICAgICAgdGhpcy5idXR0b25OYW1lU2hvdyA9IHRydWU7CiAgICAgICAgICB9CgogICAgICAgICAgYnJlYWs7CgogICAgICAgIGNhc2UgNToKICAgICAgICAgIHRoaXMuYnV0dG9uTmFtZSA9ICLmjqUg5pS2IjsKCiAgICAgICAgICBpZiAodGhpcy5mb3JtLmRsZGR6eHNoci5pbmNsdWRlcyh0aGlzLmN1cnJlbnRVc2VyKSkgewogICAgICAgICAgICB0aGlzLmJ1dHRvbk5hbWVTaG93ID0gdHJ1ZTsKICAgICAgICAgIH0KCiAgICAgICAgICBpZiAodGhpcy5mb3JtLmtqenhzaHIuaW5jbHVkZXModGhpcy5jdXJyZW50VXNlcikpIHsKICAgICAgICAgICAgdGhpcy5idXR0b25OYW1lU2hvdyA9IHRydWU7CiAgICAgICAgICB9CgogICAgICAgICAgYnJlYWs7CgogICAgICAgIGNhc2UgNjoKICAgICAgICAgIHRoaXMuYnV0dG9uTmFtZSA9ICLmjqUg5pS2IjsKCiAgICAgICAgICBpZiAodGhpcy5mb3JtLmtqenhzaHIuaW5jbHVkZXModGhpcy5jdXJyZW50VXNlcikpIHsKICAgICAgICAgICAgdGhpcy5idXR0b25OYW1lU2hvdyA9IHRydWU7CiAgICAgICAgICB9CgogICAgICAgICAgYnJlYWs7CgogICAgICAgIGNhc2UgNzoKICAgICAgICAgIHRoaXMuYnV0dG9uTmFtZSA9ICLmjqUg5pS2IjsKCiAgICAgICAgICBpZiAodGhpcy5mb3JtLmRsZGR6eHNoci5pbmNsdWRlcyh0aGlzLmN1cnJlbnRVc2VyKSkgewogICAgICAgICAgICB0aGlzLmJ1dHRvbk5hbWVTaG93ID0gdHJ1ZTsKICAgICAgICAgIH0KCiAgICAgICAgICBicmVhazsKICAgICAgfQogICAgfSwKICAgIC8v5YWz6Zet5rWB56iL5Y6G5Y+y5by55qGGCiAgICBjb2xzZVRpbWVMaW5lOiBmdW5jdGlvbiBjb2xzZVRpbWVMaW5lKCkgewogICAgICB0aGlzLnRpbWVMaW5lU2hvdyA9IGZhbHNlOwogICAgfSwKICAgIC8v5rWB56iL5Y6G5Y+y5p+l55yLCiAgICBzaG93VGltZUxpbmU6IGZ1bmN0aW9uIHNob3dUaW1lTGluZShyb3cpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CgogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSggLyojX19QVVJFX18qL3JlZ2VuZXJhdG9yUnVudGltZS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUyKCkgewogICAgICAgIHZhciBfeWllbGQkSGlzdG9yeUxpc3QsIGNvZGUsIGRhdGE7CgogICAgICAgIHJldHVybiByZWdlbmVyYXRvclJ1bnRpbWUud3JhcChmdW5jdGlvbiBfY2FsbGVlMiQoX2NvbnRleHQyKSB7CiAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0Mi5wcmV2ID0gX2NvbnRleHQyLm5leHQpIHsKICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICBfdGhpczQucHJvY2Vzc0RhdGEuYnVzaW5lc3NLZXkgPSByb3cub2JqSWQ7CiAgICAgICAgICAgICAgICBfY29udGV4dDIubmV4dCA9IDM7CiAgICAgICAgICAgICAgICByZXR1cm4gKDAsIF9wcm9jZXNzVGFzay5IaXN0b3J5TGlzdCkoX3RoaXM0LnByb2Nlc3NEYXRhKTsKCiAgICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgICAgX3lpZWxkJEhpc3RvcnlMaXN0ID0gX2NvbnRleHQyLnNlbnQ7CiAgICAgICAgICAgICAgICBjb2RlID0gX3lpZWxkJEhpc3RvcnlMaXN0LmNvZGU7CiAgICAgICAgICAgICAgICBkYXRhID0gX3lpZWxkJEhpc3RvcnlMaXN0LmRhdGE7CiAgICAgICAgICAgICAgICBfdGhpczQudGltZURhdGEgPSBkYXRhOwogICAgICAgICAgICAgICAgX3RoaXM0LnRpbWVMaW5lU2hvdyA9IHRydWU7CgogICAgICAgICAgICAgIGNhc2UgODoKICAgICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Mi5zdG9wKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMik7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8v5rWB56iL5Zu+54mH5p+l55yLCiAgICBzaG93UHJvY2Vzc0ltZzogZnVuY3Rpb24gc2hvd1Byb2Nlc3NJbWcocm93KSB7CiAgICAgIHRoaXMub3BlbkxvYWRpbmdJbWcgPSB0cnVlOwogICAgICB0aGlzLmltZ1NyYyA9ICIvYWN0aXZpdGktYXBpL3Byb2Nlc3MvcmVhZC1yZXNvdXJjZT9wcm9jZXNzRGVmaW5pdGlvbktleT10enNwbGMmYnVzaW5lc3NLZXk9IiArIHJvdy5vYmpJZCArICImdD0iICsgbmV3IERhdGUoKS5nZXRUaW1lKCk7CiAgICB9LAogICAgY2xvc2VBY3Rpdml0aTogZnVuY3Rpb24gY2xvc2VBY3Rpdml0aSgpIHsKICAgICAgaWYgKHRoaXMuJHJlZnMuYWN0aXZpdGkuJHJlZnMuZm9ybSkgewogICAgICAgIHRoaXMuJHJlZnMuYWN0aXZpdGkuJHJlZnMuZm9ybS5yZXNldEZpZWxkcygpOwogICAgICB9CgogICAgICB0aGlzLmlzU2hvdyA9IGZhbHNlOwogICAgfSwKICAgIGRlbGV0ZUZpbGVCeUlkOiBmdW5jdGlvbiBkZWxldGVGaWxlQnlJZChmaWxlKSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwoKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoIC8qI19fUFVSRV9fKi9yZWdlbmVyYXRvclJ1bnRpbWUubWFyayhmdW5jdGlvbiBfY2FsbGVlMygpIHsKICAgICAgICB2YXIgX3lpZWxkJGRlbGV0ZUJ5SWQsIGNvZGU7CgogICAgICAgIHJldHVybiByZWdlbmVyYXRvclJ1bnRpbWUud3JhcChmdW5jdGlvbiBfY2FsbGVlMyQoX2NvbnRleHQzKSB7CiAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0My5wcmV2ID0gX2NvbnRleHQzLm5leHQpIHsKICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICBfY29udGV4dDMubmV4dCA9IDI7CiAgICAgICAgICAgICAgICByZXR1cm4gKDAsIF9maWxlLmRlbGV0ZUJ5SWQpKGZpbGUuZmlsZUlkKTsKCiAgICAgICAgICAgICAgY2FzZSAyOgogICAgICAgICAgICAgICAgX3lpZWxkJGRlbGV0ZUJ5SWQgPSBfY29udGV4dDMuc2VudDsKICAgICAgICAgICAgICAgIGNvZGUgPSBfeWllbGQkZGVsZXRlQnlJZC5jb2RlOwoKICAgICAgICAgICAgICAgIGlmICghKGNvZGUgPT09ICIwMDAwIikpIHsKICAgICAgICAgICAgICAgICAgX2NvbnRleHQzLm5leHQgPSA4OwogICAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICBfY29udGV4dDMubmV4dCA9IDc7CiAgICAgICAgICAgICAgICByZXR1cm4gX3RoaXM1LmdldEZpbGVMaXN0KGZpbGUuYnVzaW5lc3NJZCk7CgogICAgICAgICAgICAgIGNhc2UgNzoKICAgICAgICAgICAgICAgIC8vIGF3YWl0IHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICAgICAgX3RoaXM1LiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLAogICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5paH5Lu25Yig6Zmk5oiQ5YqfISIKICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICBjYXNlIDg6CiAgICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDMuc3RvcCgpOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTMpOwogICAgICB9KSkoKTsKICAgIH0sCiAgICBjbGVhclVwbG9hZDogZnVuY3Rpb24gY2xlYXJVcGxvYWQoKSB7CiAgICAgIGlmICh0aGlzLiRyZWZzLnVwbG9hZCkgewogICAgICAgIHRoaXMuJHJlZnMudXBsb2FkLmNsZWFyRmlsZXMoKTsKICAgICAgICB0aGlzLiRyZWZzLnVwbG9hZF92aXNvLmNsZWFyRmlsZXMoKTsKICAgICAgfQogICAgfSwKICAgIGdldEZpbGVMaXN0OiBmdW5jdGlvbiBnZXRGaWxlTGlzdChpZCkgewogICAgICB2YXIgX3RoaXM2ID0gdGhpczsKCiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKCAvKiNfX1BVUkVfXyovcmVnZW5lcmF0b3JSdW50aW1lLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTQoKSB7CiAgICAgICAgdmFyIF95aWVsZCRnZXRMaXN0QnlCdXNpbiwgY29kZSwgZGF0YTsKCiAgICAgICAgcmV0dXJuIHJlZ2VuZXJhdG9yUnVudGltZS53cmFwKGZ1bmN0aW9uIF9jYWxsZWU0JChfY29udGV4dDQpIHsKICAgICAgICAgIHdoaWxlICgxKSB7CiAgICAgICAgICAgIHN3aXRjaCAoX2NvbnRleHQ0LnByZXYgPSBfY29udGV4dDQubmV4dCkgewogICAgICAgICAgICAgIGNhc2UgMDoKICAgICAgICAgICAgICAgIF9jb250ZXh0NC5uZXh0ID0gMjsKICAgICAgICAgICAgICAgIHJldHVybiAoMCwgX2ZpbGUuZ2V0TGlzdEJ5QnVzaW5lc3NJZCkoewogICAgICAgICAgICAgICAgICBidXNpbmVzc0lkOiBpZAogICAgICAgICAgICAgICAgfSk7CgogICAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICAgIF95aWVsZCRnZXRMaXN0QnlCdXNpbiA9IF9jb250ZXh0NC5zZW50OwogICAgICAgICAgICAgICAgY29kZSA9IF95aWVsZCRnZXRMaXN0QnlCdXNpbi5jb2RlOwogICAgICAgICAgICAgICAgZGF0YSA9IF95aWVsZCRnZXRMaXN0QnlCdXNpbi5kYXRhOwoKICAgICAgICAgICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICAgICAgX3RoaXM2LmZvcm0uZmlsZUxpc3QgPSBkYXRhOwogICAgICAgICAgICAgICAgICBfdGhpczYuaW1nTGlzdCA9IFtdOwogICAgICAgICAgICAgICAgICBkYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgICAgICAgICBpZiAoaXRlbS5maWxlVHlwZSAhPSAidnNkIikgewogICAgICAgICAgICAgICAgICAgICAgX3RoaXM2LmltZ0xpc3QucHVzaCh7CiAgICAgICAgICAgICAgICAgICAgICAgIHVybDogaXRlbS5maWxlVXJsLAogICAgICAgICAgICAgICAgICAgICAgICBmaWxlSWQ6IGl0ZW0uZmlsZUlkLAogICAgICAgICAgICAgICAgICAgICAgICBidXNpbmVzc0lkOiBpdGVtLmJ1c2luZXNzSWQKICAgICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgIGNhc2UgNjoKICAgICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0NC5zdG9wKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlNCk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8v5pel5pyf5qC85byP5YyWICB5eXl5LU1NLWRkCiAgICBkYXRlRm9ybWF0dGVyOiBmdW5jdGlvbiBkYXRlRm9ybWF0dGVyKGQpIHsKICAgICAgdmFyIHllYXIgPSBkLmdldEZ1bGxZZWFyKCk7CiAgICAgIHZhciBtb250aCA9IGQuZ2V0TW9udGgoKSA8IDkgPyAiMCIgKyAoZC5nZXRNb250aCgpICsgMSkgOiAiIiArIChkLmdldE1vbnRoKCkgKyAxKTsKICAgICAgdmFyIGRheSA9IGQuZ2V0RGF0ZSgpIDwgMTAgPyAiMCIgKyBkLmdldERhdGUoKSA6ICIiICsgZC5nZXREYXRlKCk7CiAgICAgIHJldHVybiB5ZWFyICsgIi0iICsgbW9udGggKyAiLSIgKyBkYXk7CiAgICB9LAogICAgLy/liJfooajmn6Xor6IKICAgIC8v54K55Ye75byA5YWz5pe26Kem5Y+RCiAgICBzd2l0Y2hDaGFoZ2U6IGZ1bmN0aW9uIHN3aXRjaENoYWhnZSh2YWx1ZSkgewogICAgICB0aGlzLmdldERhdGEoKTsKICAgIH0sCiAgICBnZXREYXRhOiBmdW5jdGlvbiBnZXREYXRhKHBhcmFtcykgewogICAgICB2YXIgX3RoaXM3ID0gdGhpczsKCiAgICAgIHJldHVybiAoMCwgX2FzeW5jVG9HZW5lcmF0b3IyLmRlZmF1bHQpKCAvKiNfX1BVUkVfXyovcmVnZW5lcmF0b3JSdW50aW1lLm1hcmsoZnVuY3Rpb24gX2NhbGxlZTUoKSB7CiAgICAgICAgdmFyIHBhcmFtOwogICAgICAgIHJldHVybiByZWdlbmVyYXRvclJ1bnRpbWUud3JhcChmdW5jdGlvbiBfY2FsbGVlNSQoX2NvbnRleHQ1KSB7CiAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0NS5wcmV2ID0gX2NvbnRleHQ1Lm5leHQpIHsKICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICBfdGhpczcuJHJlZnMuY29tcFRhYmxlLmxvYWRpbmcgPSB0cnVlOwoKICAgICAgICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgICAgICAgIC8vdGFic+WPguaVsAogICAgICAgICAgICAgICAgICBpZiAoX3RoaXM3LmFjdGl2ZU5hbWVUb3cgIT09ICIxIikgewogICAgICAgICAgICAgICAgICAgIF90aGlzNy5wYXJhbXMuZHlkaiA9ICIiOwogICAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgIF90aGlzNy5wYXJhbXMuZHlkaiA9IF90aGlzNy5hY3RpdmVOYW1lOwogICAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgICBfdGhpczcucGFyYW1zID0gKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKCgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7fSwgX3RoaXM3LnBhcmFtcyksIHBhcmFtcyk7CiAgICAgICAgICAgICAgICAgIHBhcmFtID0gX3RoaXM3LnBhcmFtczsKCiAgICAgICAgICAgICAgICAgIGlmIChwYXJhbS5mYnJxQXJyICYmIHBhcmFtLmZicnFBcnIubGVuZ3RoID4gMCkgewogICAgICAgICAgICAgICAgICAgIHBhcmFtLmZicnFTdGFydCA9IF90aGlzNy5kYXRlRm9ybWF0dGVyKHBhcmFtLmZicnFBcnJbMF0pOwogICAgICAgICAgICAgICAgICAgIHBhcmFtLmZicnFFbmQgPSBfdGhpczcuZGF0ZUZvcm1hdHRlcihwYXJhbS5mYnJxQXJyWzFdKTsKICAgICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgICAgaWYgKHBhcmFtLnp4cnFBcnIgJiYgcGFyYW0uenhycUFyci5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgICAgICAgICAgcGFyYW0uenhycVN0YXJ0ID0gX3RoaXM3LmRhdGVGb3JtYXR0ZXIocGFyYW0uenhycUFyclswXSk7CiAgICAgICAgICAgICAgICAgICAgcGFyYW0uenhycUVuZCA9IF90aGlzNy5kYXRlRm9ybWF0dGVyKHBhcmFtLnp4cnFBcnJbMV0pOwogICAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgICBwYXJhbS5pc2xhc3QgPSBfdGhpczcuYWN0aXZlTmFtZVRvdzsKCiAgICAgICAgICAgICAgICAgIGlmICghcGFyYW0ubXlTb3J0cykgewogICAgICAgICAgICAgICAgICAgIHBhcmFtLm15U29ydHMgPSBbewogICAgICAgICAgICAgICAgICAgICAgcHJvcDogInVwZGF0ZVRpbWUiLAogICAgICAgICAgICAgICAgICAgICAgYXNjOiBmYWxzZQogICAgICAgICAgICAgICAgICAgIH1dOwogICAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgICAoMCwgX2J6Z2ZnbC5nZXRMaXN0VFopKHBhcmFtKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgICAgICAgICAgX3RoaXM3LnRhYmxlQW5kUGFnZUluZm8udGFibGVEYXRhID0gcmVzLmRhdGEucmVjb3JkczsKICAgICAgICAgICAgICAgICAgICAgIF90aGlzNy50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnRvdGFsID0gcmVzLmRhdGEudG90YWw7CgogICAgICAgICAgICAgICAgICAgICAgX3RoaXM3LnRhYmxlQW5kUGFnZUluZm8udGFibGVEYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgICAgICAgICAgICAgX3RoaXM3LnN0YXR1c09wdGlvbnMuZm9yRWFjaChmdW5jdGlvbiAoZWxlbWVudCkgewogICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChpdGVtLnN0YXR1cyA9PT0gZWxlbWVudC52YWx1ZSkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgaXRlbS5zdGF0dXNOYW1lID0gZWxlbWVudC5sYWJlbDsKICAgICAgICAgICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgICAgICBfdGhpczcuJHJlZnMuY29tcFRhYmxlLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgICAgICAgICAgIF90aGlzNy4kcmVmcy5jb21wVGFibGUubG9hZGluZyA9IGZhbHNlOwogICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhlKTsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgY2FzZSAyOgogICAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ1LnN0b3AoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWU1KTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgLy/mlrDlop4KICAgIHNhdmVSb3c6IGZ1bmN0aW9uIHNhdmVSb3coKSB7CiAgICAgIHZhciBfdGhpczggPSB0aGlzOwoKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoIC8qI19fUFVSRV9fKi9yZWdlbmVyYXRvclJ1bnRpbWUubWFyayhmdW5jdGlvbiBfY2FsbGVlNygpIHsKICAgICAgICByZXR1cm4gcmVnZW5lcmF0b3JSdW50aW1lLndyYXAoZnVuY3Rpb24gX2NhbGxlZTckKF9jb250ZXh0NykgewogICAgICAgICAgd2hpbGUgKDEpIHsKICAgICAgICAgICAgc3dpdGNoIChfY29udGV4dDcucHJldiA9IF9jb250ZXh0Ny5uZXh0KSB7CiAgICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgICAgX3RoaXM4LnNhdmVMb2FkaW5nID0gdHJ1ZTsKICAgICAgICAgICAgICAgIF9jb250ZXh0Ny5uZXh0ID0gMzsKICAgICAgICAgICAgICAgIHJldHVybiBfdGhpczguJHJlZnNbImZvcm0iXS52YWxpZGF0ZSggLyojX19QVVJFX18qL2Z1bmN0aW9uICgpIHsKICAgICAgICAgICAgICAgICAgdmFyIF9yZWYyID0gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSggLyojX19QVVJFX18qL3JlZ2VuZXJhdG9yUnVudGltZS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWU2KHZhbGlkKSB7CiAgICAgICAgICAgICAgICAgICAgdmFyIF95aWVsZCRzYXZlT3JVcGRhdGUsIGNvZGUsIGRhdGE7CgogICAgICAgICAgICAgICAgICAgIHJldHVybiByZWdlbmVyYXRvclJ1bnRpbWUud3JhcChmdW5jdGlvbiBfY2FsbGVlNiQoX2NvbnRleHQ2KSB7CiAgICAgICAgICAgICAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICAgICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0Ni5wcmV2ID0gX2NvbnRleHQ2Lm5leHQpIHsKICAgICAgICAgICAgICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoIXZhbGlkKSB7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jb250ZXh0Ni5uZXh0ID0gMTg7CiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF9jb250ZXh0Ni5uZXh0ID0gMzsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAoMCwgX2J6Z2ZnbC5zYXZlT3JVcGRhdGUpKF90aGlzOC5mb3JtKTsKCiAgICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgICAgICAgICAgICAgICAgX3lpZWxkJHNhdmVPclVwZGF0ZSA9IF9jb250ZXh0Ni5zZW50OwogICAgICAgICAgICAgICAgICAgICAgICAgICAgY29kZSA9IF95aWVsZCRzYXZlT3JVcGRhdGUuY29kZTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGEgPSBfeWllbGQkc2F2ZU9yVXBkYXRlLmRhdGE7CgogICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCEoY29kZSA9PT0gIjAwMDAiKSkgewogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBfY29udGV4dDYubmV4dCA9IDE2OwogICAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBfdGhpczgudXBsb2FkRGF0YS5idXNpbmVzc0lkID0gZGF0YS5vYmpJZDsKCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBfdGhpczguJHJlZnMudXBsb2FkLnN1Ym1pdCgpOwoKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF90aGlzOC4kcmVmcy51cGxvYWRfdmlzby5zdWJtaXQoKTsKCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBfdGhpczguY2xlYXJVcGxvYWQoKTsKCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBfdGhpczguZmlsZU9wZW4gPSBmYWxzZTsKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF90aGlzOC5zYXZlTG9hZGluZyA9IGZhbHNlOwoKICAgICAgICAgICAgICAgICAgICAgICAgICAgIF90aGlzOC4kbWVzc2FnZS5zdWNjZXNzKCLmk43kvZzmiJDlip8iKTsKCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBfY29udGV4dDYubmV4dCA9IDE2OwogICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIF90aGlzOC5nZXREYXRhKCk7CgogICAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgMTY6CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBfY29udGV4dDYubmV4dCA9IDE5OwogICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7CgogICAgICAgICAgICAgICAgICAgICAgICAgIGNhc2UgMTg6CiAgICAgICAgICAgICAgICAgICAgICAgICAgICBfdGhpczguc2F2ZUxvYWRpbmcgPSBmYWxzZTsKCiAgICAgICAgICAgICAgICAgICAgICAgICAgY2FzZSAxOToKICAgICAgICAgICAgICAgICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Ni5zdG9wKCk7CiAgICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgICB9LCBfY2FsbGVlNik7CiAgICAgICAgICAgICAgICAgIH0pKTsKCiAgICAgICAgICAgICAgICAgIHJldHVybiBmdW5jdGlvbiAoX3gpIHsKICAgICAgICAgICAgICAgICAgICByZXR1cm4gX3JlZjIuYXBwbHkodGhpcywgYXJndW1lbnRzKTsKICAgICAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgICAgIH0oKSk7CgogICAgICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Ny5zdG9wKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlNyk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIC8v57yW6L6R5oyJ6ZKuCiAgICB1cGRhdGVSb3c6IGZ1bmN0aW9uIHVwZGF0ZVJvdyhyb3cpIHsKICAgICAgdGhpcy5nZXRGaWxlTGlzdChyb3cub2JqSWQpOwogICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZTsgLy8gdGhpcy5jbGVhclVwbG9hZCgpCgogICAgICB0aGlzLnRpdGxlID0gIuS/ruaUueWbvue6uCI7CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICB0aGlzLmZvcm0gPSAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIHJvdyk7CiAgICAgIHRoaXMuZmlsZU9wZW4gPSB0cnVlOwogICAgfSwKICAgIC8v6aG26YOo57yW6L6R5oyJ6ZKuCiAgICB1cGRhdGVUb3BSb3c6IGZ1bmN0aW9uIHVwZGF0ZVRvcFJvdygpIHsKICAgICAgaWYgKHRoaXMuc2VsZWN0Um93cy5sZW5ndGggPCAxKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7fpgInmi6nmraPnoa7nmoTmlbDmja7vvIHvvIHvvIEiKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIGlmICh0aGlzLnNlbGVjdFJvd3MubGVuZ3RoID4gMSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi5Y+q6IO96YCJ5Lit5LiA5p2h5pWw5o2u77yB77yB77yBIik7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICB2YXIgcm93ID0gdGhpcy5zZWxlY3RSb3dzWzBdOwogICAgICBjb25zb2xlLmxvZygicm93Iiwgcm93KTsKICAgICAgdGhpcy5nZXRGaWxlTGlzdChyb3cub2JqSWQpOwogICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZTsgLy8gdGhpcy5jbGVhclVwbG9hZCgpCgogICAgICB0aGlzLnRpdGxlID0gIuS/ruaUueWbvue6uCI7CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICB0aGlzLmZvcm0gPSAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIHJvdyk7CiAgICAgIHRoaXMuZmlsZU9wZW4gPSB0cnVlOwogICAgfSwKICAgIC8v6K+m5oOF5oyJ6ZKuCiAgICBnZXRJbmZvOiBmdW5jdGlvbiBnZXRJbmZvKHJvdykgewogICAgICB0aGlzLmdldEZpbGVMaXN0KHJvdy5vYmpJZCk7CiAgICAgIHRoaXMuYnV0dG9uTmFtZVNob3cgPSBmYWxzZTsKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gdHJ1ZTsKICAgICAgdGhpcy50aXRsZSA9ICLlm77nurjor6bmg4UiOwogICAgICB0aGlzLmZvcm0gPSAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIHJvdyk7CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IHRydWU7CiAgICAgIHRoaXMuZmlsZU9wZW4gPSB0cnVlOwogICAgICB0aGlzLmdldFNob3cocm93KTsKICAgIH0sCiAgICAvL+e6v+i3r+i3s+i9rOmhtQogICAgZ2V0WGxJbmZvOiBmdW5jdGlvbiBnZXRYbEluZm8ocm93KSB7CiAgICAgIHRoaXMueGxEYXRhID0gW107CgogICAgICBpZiAocm93LnhsKSB7CiAgICAgICAgdGhpcy54bERhdGEgPSByb3cueGwuc3BsaXQoIiwiKTsKICAgICAgfQoKICAgICAgdGhpcy5zaG93VHpGbGFnID0gdHJ1ZTsKICAgIH0sCiAgICAvL+WIoOmZpAogICAgZGVsZXRlUm93OiBmdW5jdGlvbiBkZWxldGVSb3coaWQpIHsKICAgICAgdmFyIF90aGlzOSA9IHRoaXM7CgogICAgICB2YXIgb2JqSWQgPSAiIjsKCiAgICAgIGlmICgoMCwgX3R5cGVvZjIuZGVmYXVsdCkoaWQpID09PSAib2JqZWN0IikgewogICAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPCAxKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+mAieaLqeato+ehrueahOaVsOaNru+8ge+8ge+8gSIpOwogICAgICAgICAgcmV0dXJuOwogICAgICAgIH0KCiAgICAgICAgb2JqSWQgPSB0aGlzLmlkc1swXTsKICAgICAgfSBlbHNlIHsKICAgICAgICBvYmpJZCA9IGlkOwogICAgICB9CgogICAgICB0aGlzLiRjb25maXJtKCLmraTmk43kvZzlsIbmsLjkuYXliKDpmaTor6XmlbDmja4sIOaYr+WQpue7p+e7rT8iLCAi5o+Q56S6IiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgKDAsIF9iemdmZ2wucmVtb3ZlKShKU09OLnN0cmluZ2lmeShvYmpJZCkpLnRoZW4oZnVuY3Rpb24gKF9yZWYzKSB7CiAgICAgICAgICB2YXIgY29kZSA9IF9yZWYzLmNvZGU7CgogICAgICAgICAgaWYgKGNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICBfdGhpczkuJG1lc3NhZ2UoewogICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgICAgICBtZXNzYWdlOiAi5Yig6Zmk5oiQ5YqfISIKICAgICAgICAgICAgfSk7CgogICAgICAgICAgICBfdGhpczkuZ2V0RGF0YSgpOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgX3RoaXM5LiRtZXNzYWdlKHsKICAgICAgICAgICAgICB0eXBlOiAiZXJyb3IiLAogICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTlpLHotKUhIgogICAgICAgICAgICB9KTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkge30pOwogICAgfSwKICAgIGFkZFJvdzogZnVuY3Rpb24gYWRkUm93KCkgewogICAgICB0aGlzLnNhdmVMb2FkaW5nID0gZmFsc2U7CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgICB0aGlzLmltZ0xpc3QgPSBbXTsgLy8gdGhpcy5jbGVhclVwbG9hZCgpCgogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgd2pseDogIjEiLAogICAgICAgIGZpbGVMaXN0OiBbXQogICAgICB9OwogICAgICB0aGlzLnRpdGxlID0gIuaWsOWinuWbvue6uCI7IC8v6I635Y+W5b2T5YmN55m75b2V5Lq65ZCN56ewCgogICAgICB0aGlzLmZvcm0uZmJyID0gdGhpcy4kc3RvcmUuZ2V0dGVycy5uaWNrTmFtZTsKICAgICAgdGhpcy5maWxlT3BlbiA9IHRydWU7CiAgICB9LAogICAgYWRkSGlzdG9yeVJvdzogZnVuY3Rpb24gYWRkSGlzdG9yeVJvdygpIHsKICAgICAgdGhpcy5zYXZlTG9hZGluZyA9IGZhbHNlOwogICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZTsKICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgICAgdGhpcy5pbWdMaXN0ID0gW107IC8vIHRoaXMuY2xlYXJVcGxvYWQoKQoKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIHdqbHg6ICIxIiwKICAgICAgICBmaWxlTGlzdDogW10KICAgICAgfTsKICAgICAgdGhpcy50aXRsZSA9ICLmlrDlop7lm77nurgiOyAvL+iOt+WPluW9k+WJjeeZu+W9leS6uuWQjeensAoKICAgICAgdGhpcy5mb3JtLmZiciA9IHRoaXMuJHN0b3JlLmdldHRlcnMubmlja05hbWU7CiAgICAgIHRoaXMuZm9ybS5kYXRhRmxhZyA9ICLljoblj7LmlrDlop4iOwogICAgICB0aGlzLmZpbGVPcGVuID0gdHJ1ZTsKICAgIH0sCiAgICBmaWx0ZXJSZXNldDogZnVuY3Rpb24gZmlsdGVyUmVzZXQoKSB7CiAgICAgIHRoaXMuJHJlZnMuY29tcFRhYmxlLiRyZWZzLm11bHRpcGxlVGFibGUuY2xlYXJTb3J0KCk7CiAgICAgIHRoaXMucGFyYW1zID0gewogICAgICAgIHdqbHg6ICIxIiwKICAgICAgICBmYnJxOiAiIiwKICAgICAgICB6eHJxOiAiIiwKICAgICAgICBzdGF0dXM6ICIiLAogICAgICAgIHdqbHhMaXN0OiBbXSwKICAgICAgICBzdGF0dXNMaXN0OiBbXQogICAgICB9OwogICAgfSwKCiAgICAvKioKICAgICAqIOS4iuS8oOaWh+S7tuS5i+WJjeeahOWkhOeQhgogICAgICovCiAgICBiZWZvcmVVcGxvYWQ6IGZ1bmN0aW9uIGJlZm9yZVVwbG9hZChmaWxlKSB7CiAgICAgIHZhciBmaWxlU2l6ZSA9IGZpbGUuc2l6ZSA8IDEwMjQgKiAxMDI0ICogNTA7IC8vMTBNCgogICAgICBpZiAoIWZpbGVTaXplKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5LiK5Lyg5paH5Lu25aSn5bCP5LiN6IO96LaF6L+HIDUwTUIhIik7CiAgICAgIH0KCiAgICAgIHZhciBzaXplID0gZmlsZS5zaXplIC8gMTAyNDsKICAgIH0sCgogICAgLyoqCiAgICAgKiDkuIrkvKDmiJDlip/lm57osIPlh73mlbAKICAgICAqLwogICAgb25TdWNjZXNzOiBmdW5jdGlvbiBvblN1Y2Nlc3MocmVzcG9uc2UsIGZpbGUsIGZpbGVMaXN0KSB7fSwKCiAgICAvKirkuIvovb3pmYTku7YqLwogICAgZG93bmxvYWRIYW5kbGU6IGZ1bmN0aW9uIGRvd25sb2FkSGFuZGxlKGZpbGUpIHsKICAgICAgKDAsIF9maWxlLmRvd25sb2FkQnlGaWxlSWQpKGZpbGUuZmlsZUlkLCBmaWxlLmZpbGVPbGROYW1lKTsKICAgIH0sCgogICAgLyoqCiAgICAgKiDlhbPpl63kuIrkvKDpmYTku7blr7nor53moYYKICAgICAqLwogICAgdXBsb2FkQ2xvc2U6IGZ1bmN0aW9uIHVwbG9hZENsb3NlKCkgewogICAgICB0aGlzLmltZ0xpc3QgPSBbXTsKICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgICAgdGhpcy5mb3JtLmF0dGFjaG1lbnRuYW1lID0gIiI7CiAgICAgIHRoaXMuZmlsZU9wZW4gPSBmYWxzZTsKICAgIH0sCiAgICAvL+mAieaLqeihjAogICAgc2VsZWN0Q2hhbmdlOiBmdW5jdGlvbiBzZWxlY3RDaGFuZ2Uocm93cykgewogICAgICB0aGlzLnNlbGVjdFJvd3MgPSByb3dzOwogICAgICB0aGlzLmlkcyA9IHJvd3MubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgcmV0dXJuIGl0ZW0ub2JqSWQ7CiAgICAgIH0pOwogICAgICB0aGlzLnNpbmdsZSA9IHJvd3MubGVuZ3RoICE9PSAxOwogICAgfSwKICAgIGluaXREb21haW46IGZ1bmN0aW9uIGluaXREb21haW4oKSB7CiAgICAgIHZhciBfdGhpczEwID0gdGhpczsKCiAgICAgIHRoaXMuZmlsdGVySW5mby5maWVsZExpc3QuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIGlmIChpdGVtLnZhbHVlID09PSAic3RhdHVzTGlzdCIpIHsKICAgICAgICAgIGl0ZW0ub3B0aW9ucyA9IF90aGlzMTAuc3RhdHVzT3B0aW9uczsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8v5rWB56iL57uT5p2f5Zue6LCDCiAgICB0b2RvUmVzdWx0OiBmdW5jdGlvbiB0b2RvUmVzdWx0KGRhdGEpIHsKICAgICAgdmFyIF90aGlzMTEgPSB0aGlzOwoKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoIC8qI19fUFVSRV9fKi9yZWdlbmVyYXRvclJ1bnRpbWUubWFyayhmdW5jdGlvbiBfY2FsbGVlOCgpIHsKICAgICAgICB2YXIgcm93MSwgcm93OwogICAgICAgIHJldHVybiByZWdlbmVyYXRvclJ1bnRpbWUud3JhcChmdW5jdGlvbiBfY2FsbGVlOCQoX2NvbnRleHQ4KSB7CiAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0OC5wcmV2ID0gX2NvbnRleHQ4Lm5leHQpIHsKICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICByb3cxID0ge307CgogICAgICAgICAgICAgICAgaWYgKCEoZGF0YS5wcm9jZXNzVHlwZSA9PT0gInJvbGxiYWNrIikpIHsKICAgICAgICAgICAgICAgICAgX2NvbnRleHQ4Lm5leHQgPSAxMzsKICAgICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgX2NvbnRleHQ4LnQwID0gZGF0YS5hY3RpdmVUYXNrTmFtZTsKICAgICAgICAgICAgICAgIF9jb250ZXh0OC5uZXh0ID0gX2NvbnRleHQ4LnQwID09PSAieGxmZ3NzYiIgPyA1IDogX2NvbnRleHQ4LnQwID09PSAieGxmZ3NzcCIgPyA3IDogX2NvbnRleHQ4LnQwID09PSAic2NremdxciIgPyA5IDogMTE7CiAgICAgICAgICAgICAgICBicmVhazsKCiAgICAgICAgICAgICAgY2FzZSA1OgogICAgICAgICAgICAgICAgcm93MS5zdGF0dXMgPSAwOwogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0OC5hYnJ1cHQoImJyZWFrIiwgMTEpOwoKICAgICAgICAgICAgICBjYXNlIDc6CiAgICAgICAgICAgICAgICByb3cxLnN0YXR1cyA9IDE7CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ4LmFicnVwdCgiYnJlYWsiLCAxMSk7CgogICAgICAgICAgICAgIGNhc2UgOToKICAgICAgICAgICAgICAgIHJvdzEuc3RhdHVzID0gMjsKICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDguYWJydXB0KCJicmVhayIsIDExKTsKCiAgICAgICAgICAgICAgY2FzZSAxMToKICAgICAgICAgICAgICAgIF9jb250ZXh0OC5uZXh0ID0gMzU7CiAgICAgICAgICAgICAgICBicmVhazsKCiAgICAgICAgICAgICAgY2FzZSAxMzoKICAgICAgICAgICAgICAgIF9jb250ZXh0OC50MSA9IGRhdGEuYWN0aXZlVGFza05hbWU7CiAgICAgICAgICAgICAgICBfY29udGV4dDgubmV4dCA9IF9jb250ZXh0OC50MSA9PT0gInhsZmdzc3AiID8gMTYgOiBfY29udGV4dDgudDEgPT09ICJzY2t6Z3FyIiA/IDE5IDogX2NvbnRleHQ4LnQxID09PSAic2NrbGRzcCIgPyAyMiA6IF9jb250ZXh0OC50MSA9PT0gInp4ZGRrenp4c2hranh4enhzaCIgPyAyNSA6IF9jb250ZXh0OC50MSA9PT0gImtqeHh6eHNoIiA/IDI5IDogX2NvbnRleHQ4LnQxID09PSAienhkZGt6enhzaCIgPyAzMSA6IF9jb250ZXh0OC50MSA9PT0gIue7k+adnyIgPyAzMyA6IDM1OwogICAgICAgICAgICAgICAgYnJlYWs7CgogICAgICAgICAgICAgIGNhc2UgMTY6CiAgICAgICAgICAgICAgICByb3cxLnN0YXR1cyA9IDE7CiAgICAgICAgICAgICAgICByb3cxLmZnc3NwciA9IGRhdGEubmV4dFVzZXI7CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ4LmFicnVwdCgiYnJlYWsiLCAzNSk7CgogICAgICAgICAgICAgIGNhc2UgMTk6CiAgICAgICAgICAgICAgICByb3cxLnN0YXR1cyA9IDI7CiAgICAgICAgICAgICAgICByb3cxLnNja3pnc3ByID0gZGF0YS5uZXh0VXNlcjsKICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dDguYWJydXB0KCJicmVhayIsIDM1KTsKCiAgICAgICAgICAgICAgY2FzZSAyMjoKICAgICAgICAgICAgICAgIHJvdzEuc3RhdHVzID0gMzsKICAgICAgICAgICAgICAgIHJvdzEuc2NrbGRzcHIgPSBkYXRhLm5leHRVc2VyOwogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0OC5hYnJ1cHQoImJyZWFrIiwgMzUpOwoKICAgICAgICAgICAgICBjYXNlIDI1OgogICAgICAgICAgICAgICAgcm93MS5zdGF0dXMgPSA1OwogICAgICAgICAgICAgICAgcm93MS5ranp4c2hyID0gZGF0YS5uZXh0VXNlcjsKICAgICAgICAgICAgICAgIHJvdzEuZGxkZHp4c2hyID0gZGF0YS5uZXh0VXNlcjI7CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ4LmFicnVwdCgiYnJlYWsiLCAzNSk7CgogICAgICAgICAgICAgIGNhc2UgMjk6CiAgICAgICAgICAgICAgICByb3cxLnN0YXR1cyA9IDY7CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ4LmFicnVwdCgiYnJlYWsiLCAzNSk7CgogICAgICAgICAgICAgIGNhc2UgMzE6CiAgICAgICAgICAgICAgICByb3cxLnN0YXR1cyA9IDc7CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ4LmFicnVwdCgiYnJlYWsiLCAzNSk7CgogICAgICAgICAgICAgIGNhc2UgMzM6CiAgICAgICAgICAgICAgICByb3cxLnN0YXR1cyA9IDQ7CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ4LmFicnVwdCgiYnJlYWsiLCAzNSk7CgogICAgICAgICAgICAgIGNhc2UgMzU6CiAgICAgICAgICAgICAgICByb3cgPSB7CiAgICAgICAgICAgICAgICAgIG9iaklkOiBkYXRhLmJ1c2luZXNzS2V5LAogICAgICAgICAgICAgICAgICBpc1N0YXJ0OiAxLAogICAgICAgICAgICAgICAgICBpc0JhY2s6IGRhdGEucHJvY2Vzc1R5cGUgPT09ICJyb2xsYmFjayIgPyAxIDogMAogICAgICAgICAgICAgICAgfTsKICAgICAgICAgICAgICAgIHJvdyA9ICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSgoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIHJvdyksIHJvdzEpOwogICAgICAgICAgICAgICAgKDAsIF9iemdmZ2wuc2F2ZU9yVXBkYXRlKShyb3cpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICAgICAgICBfdGhpczExLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLmk43kvZzmiJDlip8hIgogICAgICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICAgICAgICBfdGhpczExLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIucGFnZVJlc2l6ZSA9ICJZIjsKCiAgICAgICAgICAgICAgICAgICAgX3RoaXMxMS5nZXREYXRhKCk7CiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICBjYXNlIDM4OgogICAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQ4LnN0b3AoKTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWU4KTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgc3VibWl0OiBmdW5jdGlvbiBzdWJtaXQodHlwZSkgewogICAgICB0aGlzLmZpbGVPcGVuID0gZmFsc2U7CgogICAgICBpZiAodHlwZSA9PT0gImNvbXBsZXRlIikgewogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuc3RhdHVzID0gdGhpcy5mb3JtLnN0YXR1czsKCiAgICAgICAgc3dpdGNoICh0aGlzLmZvcm0uc3RhdHVzKSB7CiAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgIHRoaXMuaXNTaG93ID0gdHJ1ZTsKICAgICAgICAgICAgdGhpcy5hY3Rpdml0aU9wdGlvbi50aXRsZSA9ICLkuIrmiqUiOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmRlZmF1bHRGcm9tID0gdHJ1ZTsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5wcm9jZXNzVHlwZSA9ICJjb21wbGV0ZSI7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuYnVzaW5lc3NLZXkgPSB0aGlzLmZvcm0ub2JqSWQ7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnBhc3MgPSB0cnVlOwogICAgICAgICAgICB0aGlzLmlzU2hvdyA9IHRydWU7CiAgICAgICAgICAgIGJyZWFrOwoKICAgICAgICAgIGNhc2UgMToKICAgICAgICAgICAgdGhpcy5hY3Rpdml0aU9wdGlvbi50aXRsZSA9ICLpgJrov4ciOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmRlZmF1bHRGcm9tID0gdHJ1ZTsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5wcm9jZXNzVHlwZSA9ICJjb21wbGV0ZSI7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuYnVzaW5lc3NLZXkgPSB0aGlzLmZvcm0ub2JqSWQ7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnBhc3MgPSB0cnVlOwogICAgICAgICAgICB0aGlzLmlzU2hvdyA9IHRydWU7CiAgICAgICAgICAgIGJyZWFrOwoKICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgdGhpcy5hY3Rpdml0aU9wdGlvbi50aXRsZSA9ICLpgJrov4ciOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmRlZmF1bHRGcm9tID0gdHJ1ZTsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5wcm9jZXNzVHlwZSA9ICJjb21wbGV0ZSI7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuYnVzaW5lc3NLZXkgPSB0aGlzLmZvcm0ub2JqSWQ7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnBhc3MgPSB0cnVlOwogICAgICAgICAgICB0aGlzLmlzU2hvdyA9IHRydWU7CiAgICAgICAgICAgIGJyZWFrOwoKICAgICAgICAgIGNhc2UgMzoKICAgICAgICAgICAgdGhpcy5hY3Rpdml0aU9wdGlvbi50aXRsZSA9ICLpgJrov4ciOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmRlZmF1bHRGcm9tID0gdHJ1ZTsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5wcm9jZXNzVHlwZSA9ICJjb21wbGV0ZSI7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuYnVzaW5lc3NLZXkgPSB0aGlzLmZvcm0ub2JqSWQ7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnBhc3MgPSB0cnVlOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnRhc2tJZCA9ICJzY2tsZHNwIjsKICAgICAgICAgICAgdGhpcy5pc1Nob3cgPSB0cnVlOwogICAgICAgICAgICBicmVhazsKCiAgICAgICAgICBjYXNlIDU6CiAgICAgICAgICAgIHRoaXMuYWN0aXZpdGlPcHRpb24udGl0bGUgPSAi5o6l5pS2IjsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5kZWZhdWx0RnJvbSA9IGZhbHNlOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnByb2Nlc3NUeXBlID0gImNvbXBsZXRlIjsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5idXNpbmVzc0tleSA9IHRoaXMuZm9ybS5vYmpJZDsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS52YXJpYWJsZXMucGFzcyA9IHRydWU7CgogICAgICAgICAgICBpZiAodGhpcy5mb3JtLmRsZGR6eHNoci5pbmNsdWRlcyh0aGlzLmN1cnJlbnRVc2VyKSkgewogICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudGFza0lkID0gInp4ZGRrenp4c2giOwogICAgICAgICAgICB9CgogICAgICAgICAgICBpZiAodGhpcy5mb3JtLmtqenhzaHIgPT09IHRoaXMuY3VycmVudFVzZXIpIHsKICAgICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnRhc2tJZCA9ICJranh4enhzaCI7CiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIHRoaXMuaXNTaG93ID0gdHJ1ZTsKICAgICAgICAgICAgYnJlYWs7CgogICAgICAgICAgY2FzZSA2OgogICAgICAgICAgICB0aGlzLmFjdGl2aXRpT3B0aW9uLnRpdGxlID0gIuaOpeaUtiI7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuZGVmYXVsdEZyb20gPSBmYWxzZTsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5wcm9jZXNzVHlwZSA9ICJjb21wbGV0ZSI7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuYnVzaW5lc3NLZXkgPSB0aGlzLmZvcm0ub2JqSWQ7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnBhc3MgPSB0cnVlOwogICAgICAgICAgICB0aGlzLmlzU2hvdyA9IHRydWU7CiAgICAgICAgICAgIGJyZWFrOwoKICAgICAgICAgIGNhc2UgNzoKICAgICAgICAgICAgdGhpcy5hY3Rpdml0aU9wdGlvbi50aXRsZSA9ICLmjqXmlLYiOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmRlZmF1bHRGcm9tID0gZmFsc2U7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEucHJvY2Vzc1R5cGUgPSAiY29tcGxldGUiOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmJ1c2luZXNzS2V5ID0gdGhpcy5mb3JtLm9iaklkOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnZhcmlhYmxlcy5wYXNzID0gdHJ1ZTsKICAgICAgICAgICAgdGhpcy5pc1Nob3cgPSB0cnVlOwogICAgICAgICAgICBicmVhazsKICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5hY3Rpdml0aU9wdGlvbi50aXRsZSA9ICLlm57pgIDljp/lm6DloavlhpkiOwogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuZGVmYXVsdEZyb20gPSB0cnVlOwogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEucHJvY2Vzc1R5cGUgPSAicm9sbGJhY2siOwogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuYnVzaW5lc3NLZXkgPSB0aGlzLmZvcm0ub2JqSWQ7CiAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS52YXJpYWJsZXMucGFzcyA9IGZhbHNlOwogICAgICAgIHRoaXMuaXNTaG93ID0gdHJ1ZTsKICAgICAgfQogICAgfQogIH0KfTsKZXhwb3J0cy5kZWZhdWx0ID0gX2RlZmF1bHQ7"}, {"version": 3, "sources": ["tzgl.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0cA;;AACA;;AAMA;;AAKA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,UAAA,EAAA;AACA,IAAA,SAAA,EAAA,kBADA;AAEA,IAAA,QAAA,EAAA,iBAFA;AAGA,IAAA,QAAA,EAAA,sBAHA;AAIA,IAAA,WAAA,EAAA,wBAJA;AAKA,IAAA,UAAA,EAAA,sBALA;AAMA,IAAA,YAAA,EAAA;AANA,GADA;AASA,EAAA,IAAA,EAAA,MATA;AAUA,EAAA,IAVA,kBAUA;AAAA;;AACA,QAAA,YAAA,GAAA,SAAA,YAAA,CAAA,IAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACA,UAAA,CAAA,KAAA,CAAA,IAAA,CAAA,EAAA,EAAA;AACA,QAAA,QAAA,CAAA,IAAA,KAAA,EAAA,CAAA;AACA,OAFA,MAEA;AACA,QAAA,QAAA;AACA;AACA,KANA;;AAOA,WAAA;AACA,MAAA,WAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,IADA;AAEA;AACA,MAAA,cAAA,EAAA,KAHA;AAIA,MAAA,UAAA,EAAA,KAJA;AAKA,MAAA,MAAA,EAAA,EALA;AAMA,MAAA,cAAA,EAAA,EANA;AAOA,MAAA,aAAA,EAAA,KAPA;AAQA,MAAA,WAAA,EAAA,KARA;AASA;AACA,MAAA,MAAA,EAAA,IAVA;AAWA;AACA,MAAA,WAAA,EAAA,EAZA;AAaA;AACA,MAAA,UAAA,EAAA,KAdA;AAeA,MAAA,aAAA,EAAA,GAfA;AAgBA,MAAA,QAAA,EAAA,EAhBA;AAiBA,MAAA,UAAA,EAAA,EAjBA;AAkBA,MAAA,cAAA,EAAA,KAlBA;AAmBA,MAAA,YAAA,EAAA,KAnBA;AAoBA,MAAA,QAAA,EAAA,EApBA;AAqBA,MAAA,cAAA,EAAA,KArBA;AAsBA,MAAA,MAAA,EAAA,EAtBA;AAuBA,MAAA,cAAA,EAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAvBA;AAwBA,MAAA,MAAA,EAAA,KAxBA;AAyBA,MAAA,WAAA,EAAA;AACA,QAAA,oBAAA,EAAA,QADA;AAEA,QAAA,WAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,QAHA;AAIA,QAAA,SAAA,EAAA,EAJA;AAKA,QAAA,QAAA,EAAA,EALA;AAMA,QAAA,WAAA,EAAA,IANA;AAOA,QAAA,WAAA,EAAA;AAPA,OAzBA;AAkCA,MAAA,UAAA,EAAA,KAlCA;AAmCA,MAAA,MAAA,EAAA,GAnCA;AAoCA,MAAA,MAAA,EAAA;AACA,QAAA,IAAA,EAAA,GADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,MAAA,EAAA,EAJA;AAKA,QAAA,QAAA,EAAA,EALA;AAMA,QAAA,UAAA,EAAA;AANA,OApCA;AA4CA,MAAA,KAAA,EAAA,EA5CA;AA6CA,MAAA,UAAA,EAAA,EA7CA;AA8CA,MAAA,GAAA,EAAA,EA9CA;AA+CA,MAAA,IAAA,EAAA;AACA,QAAA,IAAA,EAAA,GADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,EAAA,EAAA;AAHA,OA/CA;AAoDA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA;AACA,UAAA,OAAA,EAAA,EAFA;AAGA,UAAA,OAAA,EAAA,EAHA;AAIA,UAAA,MAAA,EAAA,EAJA;AAKA,UAAA,QAAA,EAAA,EALA;AAMA,UAAA,UAAA,EAAA,EANA;AAOA,UAAA,IAAA,EAAA,EAPA;AAQA,UAAA,IAAA,EAAA;AARA,SADA;AAWA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,UAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,UAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA,EAGA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,YAHA;AAIA,UAAA,QAAA,EAAA,IAJA;AAKA,UAAA,OAAA,EAAA;AALA,SAHA;AAXA,OApDA;AA2EA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,UAFA;AAGA,UAAA,QAAA,EAAA,KAHA;AAIA,UAAA,MAAA,EAAA;AAJA,SADA,EAOA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,UAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,EAQA;AACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SATA,EAUA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAVA,EAWA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAXA,EAYA;AACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,MAAA,EAAA;AAAA,SAbA,EAcA;AAAA,UAAA,IAAA,EAAA,YAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAdA,CARA;AAwBA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAxBA,OA3EA;AAqGA,MAAA,aAAA,EAAA,IArGA;AAsGA,MAAA,YAAA,EAAA,EAtGA;AAuGA,MAAA,QAAA,EAAA,KAvGA;AAwGA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,UAAA,EAAA;AAFA,OAzGA;AA6GA,MAAA,OAAA,EAAA,EA7GA;AA8GA;AACA,MAAA,QAAA,EAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OA/GA;AAgHA,MAAA,KAAA,EAAA,IAhHA;AAiHA;AACA;AACA;AACA,MAAA,aAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,EAaA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAbA,EAiBA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAjBA,EAqBA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OArBA,EAyBA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAzBA,EA6BA;AACA,QAAA,KAAA,EAAA,CADA;AAEA,QAAA,KAAA,EAAA;AAFA,OA7BA,CApHA;AAsJA;AACA,MAAA,KAAA,EAAA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,GAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAPA;AAQA;AACA;AACA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAXA;AAcA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAdA;AAiBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAjBA;AAoBA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CApBA;AAqBA,QAAA,EAAA,EAAA,CACA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,OAAA,EAAA,MAFA;AAGA,UAAA,SAAA,EAAA,YAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SADA;AArBA;AAvJA,KAAA;AAsLA,GAxMA;AAyMA,EAAA,KAAA,EAAA,EAzMA;AA0MA,EAAA,OA1MA,qBA0MA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBACA,2BAAA,SAAA,CADA;;AAAA;AAAA;AACA,cAAA,OADA,yBACA,IADA;AAEA,cAAA,MAAA,CAAA,QAAA,GAAA,OAAA;;AACA,cAAA,MAAA,CAAA,QAAA;;AACA,cAAA,MAAA,CAAA,UAAA;;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,GA/MA;AAgNA,EAAA,OAhNA,qBAgNA;AACA,SAAA,OAAA,CAAA,KAAA,MAAA,CAAA,KAAA;AACA,GAlNA;AAmNA,EAAA,OAAA,EAAA;AACA,IAAA,YADA,wBACA,IADA,EACA,QADA,EACA;AACA,UAAA,IAAA,CAAA,MAAA,EAAA;AACA,aAAA,cAAA,CAAA,IAAA;AACA,aAAA,IAAA,CAAA,QAAA,GAAA,QAAA;AACA;AACA,KANA;AAOA,IAAA,YAPA,wBAOA,IAPA,EAOA,QAPA,EAOA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,KATA;AAUA,IAAA,wBAVA,oCAUA,IAVA,EAUA;AACA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,KAbA;AAcA;AACA,IAAA,6BAfA,yCAeA,MAfA,EAeA;AACA,WAAA,cAAA,GAAA,MAAA;AACA,KAjBA;AAkBA;AACA,IAAA,iBAnBA,+BAmBA;AACA,UAAA,KAAA,UAAA,EAAA;AACA,aAAA,cAAA,GAAA,KAAA;AACA,OAFA,MAEA;AACA,aAAA,cAAA,GAAA,IAAA;AACA;AACA,KAzBA;AA0BA;AACA,IAAA,kBA3BA,8BA2BA,MA3BA,EA2BA,KA3BA,EA2BA;AACA,WAAA,IAAA,CAAA,EAAA,GAAA,MAAA,CACA,GADA,CACA,UAAA,IAAA,EAAA;AACA,eAAA,IAAA,CAAA,KAAA;AACA,OAHA,EAIA,IAJA,CAIA,GAJA,CAAA;AAKA,KAjCA;AAkCA,IAAA,OAlCA,mBAkCA,IAlCA,EAkCA;AACA,UAAA,KAAA,GAAA;AAAA,QAAA,QAAA,EAAA;AAAA,OAAA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA;AACA,QAAA,IAAA,EAAA,KAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAAA;AAIA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA;AACA,KAzCA;AA0CA,IAAA,UA1CA,4BA0CA;AAAA,UAAA,MAAA,QAAA,MAAA;AAAA,UAAA,IAAA,QAAA,IAAA;AAAA,UAAA,KAAA,QAAA,KAAA;;AACA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,CAAA,OAAA,CAAA,MAAA,IAAA,CAAA,CAAA,EAAA;AACA,eAAA,MAAA,CAAA,OAAA,GAAA,CAAA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,GAAA,EAAA;AAAA,WAAA,CAAA;AACA,SAFA,MAEA;AACA,eAAA,MAAA,CAAA,OAAA,GAAA,CAAA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,GAAA,EAAA;AAAA,WAAA,CAAA;AACA;AACA,OANA,MAMA;AACA,aAAA,MAAA,CAAA,OAAA,GAAA,CAAA;AAAA,UAAA,IAAA,EAAA,YAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAAA,CAAA;AACA;;AACA,WAAA,OAAA;AACA,KArDA;AAsDA;AACA,IAAA,QAvDA,sBAuDA;AAAA;;AACA,+BAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KA3DA;AA4DA,IAAA,WA5DA,yBA4DA;AACA,WAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;AACA,WAAA,WAAA;AACA,WAAA,OAAA,CAAA,KAAA,MAAA,CAAA,KAAA;AACA,KAhEA;AAiEA,IAAA,OAjEA,mBAiEA,GAjEA,EAiEA;AACA,cAAA,GAAA,CAAA,MAAA;AACA,aAAA,CAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,MAAA,CAAA,OAAA,CAAA,IAAA,KAAA,KAAA,IAAA,CAAA,QAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA;;AACA;;AACA,aAAA,CAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,MAAA,CAAA,OAAA,CAAA,IAAA,KAAA,KAAA,IAAA,CAAA,MAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA;;AACA;;AACA,aAAA,CAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,MAAA,CAAA,OAAA,CAAA,IAAA,KAAA,KAAA,IAAA,CAAA,QAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA;;AACA;;AACA,aAAA,CAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,MAAA,CAAA,OAAA,CAAA,IAAA,KAAA,KAAA,IAAA,CAAA,QAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA;;AACA;;AACA,aAAA,CAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,IAAA,CAAA,SAAA,CAAA,QAAA,CAAA,KAAA,WAAA,CAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA;;AACA,cAAA,KAAA,IAAA,CAAA,OAAA,CAAA,QAAA,CAAA,KAAA,WAAA,CAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA;;AACA;;AACA,aAAA,CAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,IAAA,CAAA,OAAA,CAAA,QAAA,CAAA,KAAA,WAAA,CAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA;;AACA;;AACA,aAAA,CAAA;AACA,eAAA,UAAA,GAAA,KAAA;;AACA,cAAA,KAAA,IAAA,CAAA,SAAA,CAAA,QAAA,CAAA,KAAA,WAAA,CAAA,EAAA;AACA,iBAAA,cAAA,GAAA,IAAA;AACA;;AACA;AA7CA;AA+CA,KAjHA;AAkHA;AACA,IAAA,aAnHA,2BAmHA;AACA,WAAA,YAAA,GAAA,KAAA;AACA,KArHA;AAsHA;AACA,IAAA,YAvHA,wBAuHA,GAvHA,EAuHA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,KAAA;AADA;AAAA,uBAEA,8BAAA,MAAA,CAAA,WAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,sBAEA,IAFA;AAEA,gBAAA,IAFA,sBAEA,IAFA;AAGA,gBAAA,MAAA,CAAA,QAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,YAAA,GAAA,IAAA;;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,KA5HA;AA6HA;AACA,IAAA,cA9HA,0BA8HA,GA9HA,EA8HA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,WAAA,MAAA,GACA,iFACA,GAAA,CAAA,KADA,GAEA,KAFA,GAGA,IAAA,IAAA,GAAA,OAAA,EAJA;AAKA,KArIA;AAsIA,IAAA,aAtIA,2BAsIA;AACA,UAAA,KAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,EAAA;AACA,aAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,CAAA,WAAA;AACA;;AACA,WAAA,MAAA,GAAA,KAAA;AACA,KA3IA;AA4IA,IAAA,cA5IA,0BA4IA,IA5IA,EA4IA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,sBAAA,IAAA,CAAA,MAAA,CADA;;AAAA;AAAA;AACA,gBAAA,IADA,qBACA,IADA;;AAAA,sBAEA,IAAA,KAAA,MAFA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAGA,MAAA,CAAA,WAAA,CAAA,IAAA,CAAA,UAAA,CAHA;;AAAA;AAIA;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA;AACA,kBAAA,IAAA,EAAA,SADA;AAEA,kBAAA,OAAA,EAAA;AAFA,iBAAA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,KAtJA;AAuJA,IAAA,WAvJA,yBAuJA;AACA,UAAA,KAAA,KAAA,CAAA,MAAA,EAAA;AACA,aAAA,KAAA,CAAA,MAAA,CAAA,UAAA;AACA,aAAA,KAAA,CAAA,WAAA,CAAA,UAAA;AACA;AACA,KA5JA;AA6JA,IAAA,WA7JA,uBA6JA,EA7JA,EA6JA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,+BAAA;AAAA,kBAAA,UAAA,EAAA;AAAA,iBAAA,CADA;;AAAA;AAAA;AACA,gBAAA,IADA,yBACA,IADA;AACA,gBAAA,IADA,yBACA,IADA;;AAEA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,IAAA,CAAA,QAAA,GAAA,IAAA;AACA,kBAAA,MAAA,CAAA,OAAA,GAAA,EAAA;AACA,kBAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,wBAAA,IAAA,CAAA,QAAA,IAAA,KAAA,EAAA;AACA,sBAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA;AACA,wBAAA,GAAA,EAAA,IAAA,CAAA,OADA;AAEA,wBAAA,MAAA,EAAA,IAAA,CAAA,MAFA;AAGA,wBAAA,UAAA,EAAA,IAAA,CAAA;AAHA,uBAAA;AAKA;AACA,mBARA;AASA;;AAdA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA,KA5KA;AA6KA;AACA,IAAA,aA9KA,yBA8KA,CA9KA,EA8KA;AACA,UAAA,IAAA,GAAA,CAAA,CAAA,WAAA,EAAA;AACA,UAAA,KAAA,GACA,CAAA,CAAA,QAAA,KAAA,CAAA,GAAA,OAAA,CAAA,CAAA,QAAA,KAAA,CAAA,CAAA,GAAA,MAAA,CAAA,CAAA,QAAA,KAAA,CAAA,CADA;AAEA,UAAA,GAAA,GAAA,CAAA,CAAA,OAAA,KAAA,EAAA,GAAA,MAAA,CAAA,CAAA,OAAA,EAAA,GAAA,KAAA,CAAA,CAAA,OAAA,EAAA;AACA,aAAA,IAAA,GAAA,GAAA,GAAA,KAAA,GAAA,GAAA,GAAA,GAAA;AACA,KApLA;AAqLA;AACA;AACA,IAAA,YAvLA,wBAuLA,KAvLA,EAuLA;AACA,WAAA,OAAA;AACA,KAzLA;AA0LA,IAAA,OA1LA,mBA0LA,MA1LA,EA0LA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,SAAA,CAAA,OAAA,GAAA,IAAA;;AACA,oBAAA;AACA;AACA,sBAAA,MAAA,CAAA,aAAA,KAAA,GAAA,EAAA;AACA,oBAAA,MAAA,CAAA,MAAA,CAAA,IAAA,GAAA,EAAA;AACA,mBAFA,MAEA;AACA,oBAAA,MAAA,CAAA,MAAA,CAAA,IAAA,GAAA,MAAA,CAAA,UAAA;AACA;;AACA,kBAAA,MAAA,CAAA,MAAA,+DAAA,MAAA,CAAA,MAAA,GAAA,MAAA;AACA,kBAAA,KARA,GAQA,MAAA,CAAA,MARA;;AASA,sBAAA,KAAA,CAAA,OAAA,IAAA,KAAA,CAAA,OAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,oBAAA,KAAA,CAAA,SAAA,GAAA,MAAA,CAAA,aAAA,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;AACA,oBAAA,KAAA,CAAA,OAAA,GAAA,MAAA,CAAA,aAAA,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;AACA;;AACA,sBAAA,KAAA,CAAA,OAAA,IAAA,KAAA,CAAA,OAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,oBAAA,KAAA,CAAA,SAAA,GAAA,MAAA,CAAA,aAAA,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;AACA,oBAAA,KAAA,CAAA,OAAA,GAAA,MAAA,CAAA,aAAA,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;AACA;;AAEA,kBAAA,KAAA,CAAA,MAAA,GAAA,MAAA,CAAA,aAAA;;AAEA,sBAAA,CAAA,KAAA,CAAA,OAAA,EAAA;AACA,oBAAA,KAAA,CAAA,OAAA,GAAA,CAAA;AAAA,sBAAA,IAAA,EAAA,YAAA;AAAA,sBAAA,GAAA,EAAA;AAAA,qBAAA,CAAA;AACA;;AACA,yCAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,wBAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,sBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;;AAEA,sBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,wBAAA,MAAA,CAAA,aAAA,CAAA,OAAA,CAAA,UAAA,OAAA,EAAA;AACA,8BAAA,IAAA,CAAA,MAAA,KAAA,OAAA,CAAA,KAAA,EAAA;AACA,4BAAA,IAAA,CAAA,UAAA,GAAA,OAAA,CAAA,KAAA;AACA;AACA,yBAJA;AAKA,uBANA;AAOA;;AACA,oBAAA,MAAA,CAAA,KAAA,CAAA,SAAA,CAAA,OAAA,GAAA,KAAA;AACA,mBAdA;AAeA,iBAtCA,CAsCA,OAAA,CAAA,EAAA;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,SAAA,CAAA,OAAA,GAAA,KAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,CAAA;AACA;;AA3CA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA4CA,KAtOA;AAuOA;AACA,IAAA,OAxOA,qBAwOA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,WAAA,GAAA,IAAA;AADA;AAAA,uBAEA,MAAA,CAAA,KAAA,CAAA,MAAA,EAAA,QAAA;AAAA,oGAAA,kBAAA,KAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA,iCACA,KADA;AAAA;AAAA;AAAA;;AAAA;AAAA,mCAEA,0BAAA,MAAA,CAAA,IAAA,CAFA;;AAAA;AAAA;AAEA,4BAAA,IAFA,uBAEA,IAFA;AAEA,4BAAA,IAFA,uBAEA,IAFA;;AAAA,kCAGA,IAAA,KAAA,MAHA;AAAA;AAAA;AAAA;;AAIA,4BAAA,MAAA,CAAA,UAAA,CAAA,UAAA,GAAA,IAAA,CAAA,KAAA;;AACA,4BAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,MAAA;;AACA,4BAAA,MAAA,CAAA,KAAA,CAAA,WAAA,CAAA,MAAA;;AACA,4BAAA,MAAA,CAAA,WAAA;;AACA,4BAAA,MAAA,CAAA,QAAA,GAAA,KAAA;AACA,4BAAA,MAAA,CAAA,WAAA,GAAA,KAAA;;AACA,4BAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AAVA;AAAA,mCAWA,MAAA,CAAA,OAAA,EAXA;;AAAA;AAAA;AAAA;;AAAA;AAcA,4BAAA,MAAA,CAAA,WAAA,GAAA,KAAA;;AAdA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAA;;AAAA;AAAA;AAAA;AAAA,oBAFA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmBA,KA3PA;AA4PA;AACA,IAAA,SA7PA,qBA6PA,GA7PA,EA6PA;AACA,WAAA,WAAA,CAAA,GAAA,CAAA,KAAA;AACA,WAAA,UAAA,GAAA,KAAA,CAFA,CAGA;;AACA,WAAA,KAAA,GAAA,MAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,QAAA,GAAA,IAAA;AACA,KArQA;AAsQA;AACA,IAAA,YAvQA,0BAuQA;AACA,UAAA,KAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,aAAA;AACA;AACA;;AACA,UAAA,KAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,aAAA;AACA;AACA;;AACA,UAAA,GAAA,GAAA,KAAA,UAAA,CAAA,CAAA,CAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,KAAA,EAAA,GAAA;AACA,WAAA,WAAA,CAAA,GAAA,CAAA,KAAA;AACA,WAAA,UAAA,GAAA,KAAA,CAZA,CAaA;;AACA,WAAA,KAAA,GAAA,MAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,QAAA,GAAA,IAAA;AACA,KAzRA;AA0RA;AACA,IAAA,OA3RA,mBA2RA,GA3RA,EA2RA;AACA,WAAA,WAAA,CAAA,GAAA,CAAA,KAAA;AACA,WAAA,cAAA,GAAA,KAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,KAAA,GAAA,MAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,QAAA,GAAA,IAAA;AACA,WAAA,OAAA,CAAA,GAAA;AACA,KApSA;AAqSA;AACA,IAAA,SAtSA,qBAsSA,GAtSA,EAsSA;AACA,WAAA,MAAA,GAAA,EAAA;;AACA,UAAA,GAAA,CAAA,EAAA,EAAA;AACA,aAAA,MAAA,GAAA,GAAA,CAAA,EAAA,CAAA,KAAA,CAAA,GAAA,CAAA;AACA;;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KA5SA;AA6SA;AACA,IAAA,SA9SA,qBA8SA,EA9SA,EA8SA;AAAA;;AACA,UAAA,KAAA,GAAA,EAAA;;AACA,UAAA,sBAAA,EAAA,MAAA,QAAA,EAAA;AACA,YAAA,KAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,eAAA,QAAA,CAAA,OAAA,CAAA,aAAA;AACA;AACA;;AACA,QAAA,KAAA,GAAA,KAAA,GAAA,CAAA,CAAA,CAAA;AACA,OANA,MAMA;AACA,QAAA,KAAA,GAAA,EAAA;AACA;;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAKA,IALA,CAKA,YAAA;AACA,4BAAA,IAAA,CAAA,SAAA,CAAA,KAAA,CAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,cAAA,IAAA,SAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,OAAA;AACA,WANA,MAMA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAIA;AACA,SAbA;AAcA,OApBA,EAqBA,KArBA,CAqBA,YAAA,CAAA,CArBA;AAsBA,KA/UA;AAiVA,IAAA,MAjVA,oBAiVA;AACA,WAAA,WAAA,GAAA,KAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,SAAA,CAAA,MAAA;AACA,WAAA,OAAA,GAAA,EAAA,CAJA,CAKA;;AACA,WAAA,IAAA,GAAA;AACA,QAAA,IAAA,EAAA,GADA;AAEA,QAAA,QAAA,EAAA;AAFA,OAAA;AAIA,WAAA,KAAA,GAAA,MAAA,CAVA,CAWA;;AACA,WAAA,IAAA,CAAA,GAAA,GAAA,KAAA,MAAA,CAAA,OAAA,CAAA,QAAA;AACA,WAAA,QAAA,GAAA,IAAA;AACA,KA/VA;AAgWA,IAAA,aAhWA,2BAgWA;AACA,WAAA,WAAA,GAAA,KAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,SAAA,CAAA,MAAA;AACA,WAAA,OAAA,GAAA,EAAA,CAJA,CAKA;;AACA,WAAA,IAAA,GAAA;AACA,QAAA,IAAA,EAAA,GADA;AAEA,QAAA,QAAA,EAAA;AAFA,OAAA;AAIA,WAAA,KAAA,GAAA,MAAA,CAVA,CAWA;;AACA,WAAA,IAAA,CAAA,GAAA,GAAA,KAAA,MAAA,CAAA,OAAA,CAAA,QAAA;AACA,WAAA,IAAA,CAAA,QAAA,GAAA,MAAA;AACA,WAAA,QAAA,GAAA,IAAA;AACA,KA/WA;AAgXA,IAAA,WAhXA,yBAgXA;AACA,WAAA,KAAA,CAAA,SAAA,CAAA,KAAA,CAAA,aAAA,CAAA,SAAA;AACA,WAAA,MAAA,GAAA;AACA,QAAA,IAAA,EAAA,GADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,MAAA,EAAA,EAJA;AAKA,QAAA,QAAA,EAAA,EALA;AAMA,QAAA,UAAA,EAAA;AANA,OAAA;AAQA,KA1XA;;AA2XA;;;AAGA,IAAA,YA9XA,wBA8XA,IA9XA,EA8XA;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,IAAA,GAAA,OAAA,IAAA,GAAA,EAAA,CADA,CACA;;AACA,UAAA,CAAA,QAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,kBAAA;AACA;;AACA,UAAA,IAAA,GAAA,IAAA,CAAA,IAAA,GAAA,IAAA;AACA,KApYA;;AAsYA;;;AAGA,IAAA,SAzYA,qBAyYA,QAzYA,EAyYA,IAzYA,EAyYA,QAzYA,EAyYA,CAAA,CAzYA;;AA0YA;AACA,IAAA,cA3YA,0BA2YA,IA3YA,EA2YA;AACA,kCAAA,IAAA,CAAA,MAAA,EAAA,IAAA,CAAA,WAAA;AACA,KA7YA;;AA8YA;;;AAGA,IAAA,WAjZA,yBAiZA;AACA,WAAA,OAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,MAAA;AACA,WAAA,IAAA,CAAA,cAAA,GAAA,EAAA;AACA,WAAA,QAAA,GAAA,KAAA;AACA,KAtZA;AAuZA;AACA,IAAA,YAxZA,wBAwZA,IAxZA,EAwZA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,GAAA,GAAA,IAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,KAAA;AAAA,OAAA,CAAA;AACA,WAAA,MAAA,GAAA,IAAA,CAAA,MAAA,KAAA,CAAA;AACA,KA5ZA;AA6ZA,IAAA,UA7ZA,wBA6ZA;AAAA;;AACA,WAAA,UAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,KAAA,KAAA,YAAA,EAAA;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,OAAA,CAAA,aAAA;AACA;AACA,OAJA;AAKA,KAnaA;AAoaA;AACA,IAAA,UAraA,sBAqaA,IAraA,EAqaA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,IADA,GACA,EADA;;AAAA,sBAEA,IAAA,CAAA,WAAA,KAAA,UAFA;AAAA;AAAA;AAAA;;AAAA,+BAGA,IAAA,CAAA,cAHA;AAAA,kDAIA,SAJA,wBAOA,SAPA,wBAUA,SAVA;AAAA;;AAAA;AAKA,gBAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AALA;;AAAA;AAQA,gBAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AARA;;AAAA;AAWA,gBAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AAXA;;AAAA;AAAA;AAAA;;AAAA;AAAA,+BAeA,IAAA,CAAA,cAfA;AAAA,kDAgBA,SAhBA,yBAoBA,SApBA,yBAwBA,SAxBA,yBA4BA,oBA5BA,yBAiCA,UAjCA,yBAoCA,YApCA,yBAuCA,IAvCA;AAAA;;AAAA;AAiBA,gBAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AACA,gBAAA,IAAA,CAAA,MAAA,GAAA,IAAA,CAAA,QAAA;AAlBA;;AAAA;AAqBA,gBAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AACA,gBAAA,IAAA,CAAA,QAAA,GAAA,IAAA,CAAA,QAAA;AAtBA;;AAAA;AAyBA,gBAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AACA,gBAAA,IAAA,CAAA,QAAA,GAAA,IAAA,CAAA,QAAA;AA1BA;;AAAA;AA6BA,gBAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AACA,gBAAA,IAAA,CAAA,OAAA,GAAA,IAAA,CAAA,QAAA;AACA,gBAAA,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA,SAAA;AA/BA;;AAAA;AAkCA,gBAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AAlCA;;AAAA;AAqCA,gBAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AArCA;;AAAA;AAwCA,gBAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AAxCA;;AAAA;AA4CA,gBAAA,GA5CA,GA4CA;AACA,kBAAA,KAAA,EAAA,IAAA,CAAA,WADA;AAEA,kBAAA,OAAA,EAAA,CAFA;AAGA,kBAAA,MAAA,EAAA,IAAA,CAAA,WAAA,KAAA,UAAA,GAAA,CAAA,GAAA;AAHA,iBA5CA;AAiDA,gBAAA,GAAA,+DAAA,GAAA,GAAA,IAAA,CAAA;AACA,0CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA,sBAAA,QAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,oBAAA,OAAA,CAAA,QAAA,CAAA;AACA,sBAAA,IAAA,EAAA,SADA;AAEA,sBAAA,OAAA,EAAA;AAFA,qBAAA;;AAIA,oBAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,oBAAA,OAAA,CAAA,OAAA;AACA;AACA,iBATA;;AAlDA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA4DA,KAjeA;AAkeA,IAAA,MAleA,kBAkeA,IAleA,EAkeA;AACA,WAAA,QAAA,GAAA,KAAA;;AACA,UAAA,IAAA,KAAA,UAAA,EAAA;AACA,aAAA,WAAA,CAAA,MAAA,GAAA,KAAA,IAAA,CAAA,MAAA;;AACA,gBAAA,KAAA,IAAA,CAAA,MAAA;AACA,eAAA,CAAA;AACA,iBAAA,MAAA,GAAA,IAAA;AACA,iBAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,iBAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,iBAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,iBAAA,WAAA,CAAA,WAAA,GAAA,KAAA,IAAA,CAAA,KAAA;AACA,iBAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,iBAAA,MAAA,GAAA,IAAA;AACA;;AACA,eAAA,CAAA;AACA,iBAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,iBAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,iBAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,iBAAA,WAAA,CAAA,WAAA,GAAA,KAAA,IAAA,CAAA,KAAA;AACA,iBAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,iBAAA,MAAA,GAAA,IAAA;AACA;;AACA,eAAA,CAAA;AACA,iBAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,iBAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,iBAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,iBAAA,WAAA,CAAA,WAAA,GAAA,KAAA,IAAA,CAAA,KAAA;AACA,iBAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,iBAAA,MAAA,GAAA,IAAA;AACA;;AACA,eAAA,CAAA;AACA,iBAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,iBAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,iBAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,iBAAA,WAAA,CAAA,WAAA,GAAA,KAAA,IAAA,CAAA,KAAA;AACA,iBAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,iBAAA,WAAA,CAAA,MAAA,GAAA,SAAA;AACA,iBAAA,MAAA,GAAA,IAAA;AACA;;AACA,eAAA,CAAA;AACA,iBAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,iBAAA,WAAA,CAAA,WAAA,GAAA,KAAA;AACA,iBAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,iBAAA,WAAA,CAAA,WAAA,GAAA,KAAA,IAAA,CAAA,KAAA;AACA,iBAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;;AACA,gBAAA,KAAA,IAAA,CAAA,SAAA,CAAA,QAAA,CAAA,KAAA,WAAA,CAAA,EAAA;AACA,mBAAA,WAAA,CAAA,MAAA,GAAA,YAAA;AACA;;AACA,gBAAA,KAAA,IAAA,CAAA,OAAA,KAAA,KAAA,WAAA,EAAA;AACA,mBAAA,WAAA,CAAA,MAAA,GAAA,UAAA;AACA;;AACA,iBAAA,MAAA,GAAA,IAAA;AACA;;AACA,eAAA,CAAA;AACA,iBAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,iBAAA,WAAA,CAAA,WAAA,GAAA,KAAA;AACA,iBAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,iBAAA,WAAA,CAAA,WAAA,GAAA,KAAA,IAAA,CAAA,KAAA;AACA,iBAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,iBAAA,MAAA,GAAA,IAAA;AACA;;AACA,eAAA,CAAA;AACA,iBAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,iBAAA,WAAA,CAAA,WAAA,GAAA,KAAA;AACA,iBAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,iBAAA,WAAA,CAAA,WAAA,GAAA,KAAA,IAAA,CAAA,KAAA;AACA,iBAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,iBAAA,MAAA,GAAA,IAAA;AACA;AAhEA;AAkEA,OApEA,MAoEA;AACA,aAAA,cAAA,CAAA,KAAA,GAAA,QAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,aAAA,WAAA,CAAA,WAAA,GAAA,KAAA,IAAA,CAAA,KAAA;AACA,aAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,KAAA;AACA,aAAA,MAAA,GAAA,IAAA;AACA;AACA;AAhjBA;AAnNA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"4\">\n      <el-col :span=\"24\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 150, itemWidth: 200 }\"\n          @handleReset=\"filterReset\"\n        />\n      </el-col>\n    </el-row>\n    <el-white class=\"button-group\">\n      <el-row class=\"pull-right button_btn\" :gutter=\"20\">\n        <el-col :gutter=\"4\" :span=\"1.5\">\n          <el-button\n            type=\"primary\"\n            icon=\"el-icon-plus\"\n            v-hasPermi=\"['tzgl:records:add']\"\n            @click=\"addRow\"\n            >新增</el-button\n          >\n          <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"addHistoryRow\"\n            >临时历史新增</el-button\n          >\n          <el-button\n            type=\"danger\"\n            icon=\"el-icon-delete\"\n            :disabled=\"single\"\n            v-hasPermi=\"['tzgl:records:delete']\"\n            @click=\"deleteRow\"\n            >删除</el-button\n          >\n          <el-button\n            type=\"primary\"\n            icon=\"el-icon-edit\"\n            :disabled=\"single\"\n            v-hasPermi=\"['tzgl:records:edit']\"\n            @click=\"updateTopRow\"\n            >修改</el-button\n          >\n        </el-col>\n        <el-col :span=\"5\">\n          <template>\n            <el-tabs v-model=\"activeNameTow\" @tab-click=\"handleClick\">\n              <el-tab-pane label=\"正式图纸\" name=\"1\"></el-tab-pane>\n              <el-tab-pane label=\"待审批图纸\" name=\"3\"></el-tab-pane>\n              <el-tab-pane label=\"往期图纸\" name=\"2\"></el-tab-pane>\n            </el-tabs>\n          </template>\n        </el-col>\n        <el-col :span=\"10\">\n          <el-tabs\n            v-model=\"activeName\"\n            @tab-click=\"handleClick\"\n            type=\"card\"\n            v-if=\"this.activeNameTow == '1'\"\n          >\n            <el-tab-pane\n              :label=\"iter.label\"\n              v-for=\"iter in dydjList\"\n              :name=\"iter.value\"\n            >\n            </el-tab-pane>\n          </el-tabs>\n        </el-col>\n      </el-row>\n      <comp-table\n        ref=\"compTable\"\n        :table-and-page-info=\"tableAndPageInfo\"\n        :isSingle=\"this.single\"\n        @update:multipleSelection=\"selectChange\"\n        @sort-events=\"sortChange\"\n        height=\"67.6vh\"\n      >\n        <el-table-column\n          slot=\"table_eight\"\n          align=\"center\"\n          fixed=\"right\"\n          style=\"display: block\"\n          label=\"操作\"\n          min-width=\"120\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              @click=\"getInfo(scope.row)\"\n              title=\"详情\"\n              class=\"el-icon-view\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              @click=\"updateRow(scope.row)\"\n              v-if=\"\n                scope.row.createBy === $store.getters.name &&\n                  scope.row.status === 0\n              \"\n              title=\"修改\"\n              class=\"el-icon-edit\"\n            >\n            </el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              @click=\"deleteRow(scope.row.objId)\"\n              v-if=\"\n                scope.row.createBy === $store.getters.name &&\n                  scope.row.status === 0\n              \"\n              title=\"删除\"\n              class=\"el-icon-delete\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              @click=\"showTimeLine(scope.row)\"\n              v-if=\"scope.row.isStart === 1 && scope.row.status != 4\"\n              title=\"流程查看\"\n              class=\"el-icon-lcck commonIcon\"\n            >\n            </el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              @click=\"showProcessImg(scope.row)\"\n              v-if=\"scope.row.isStart === 1 && scope.row.status != 4\"\n              title=\"流程图\"\n              class=\"el-icon-lct commonIcon\"\n            >\n            </el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              @click=\"getXlInfo(scope.row)\"\n              title=\"线路跳转\"\n              class=\"el-icon-discover\"\n            ></el-button>\n          </template>\n        </el-table-column>\n      </comp-table>\n      <el-dialog\n        :visible.sync=\"fileOpen\"\n        :title=\"title\"\n        width=\"50%\"\n        v-dialogDrag\n      >\n        <el-form\n          ref=\"form\"\n          :model=\"form\"\n          label-width=\"150px\"\n          :disabled=\"isDisabled\"\n          :rules=\"rules\"\n        >\n          <el-row>\n            <el-col :span=\"12\">\n              <el-form-item label=\"图纸管理文件编号:\" prop=\"wjbh\">\n                <el-input\n                  placeholder=\"请输入文件编号\"\n                  clearable\n                  v-model=\"form.wjbh\"\n                ></el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"图纸管理文件名称:\" prop=\"wjmc\">\n                <el-input\n                  type=\"textarea\"\n                  :autosize=\"{ minRows: 1, maxRows: 2 }\"\n                  placeholder=\"选择输入文件名称\"\n                  clearable\n                  v-model=\"form.wjmc\"\n                ></el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"发布人:\" prop=\"fbr\">\n                <el-input\n                  placeholder=\"选择发布人\"\n                  clearable\n                  v-model=\"form.fbr\"\n                  suffix-icon=\"el-icon-s-custom\"\n                ></el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"执行日期:\" prop=\"zxrq\">\n                <el-date-picker\n                  type=\"datetime\"\n                  placeholder=\"选择日期时间\"\n                  format=\"yyyy-MM-dd\"\n                  value-format=\"yyyy-MM-dd\"\n                  clearable\n                  v-model=\"form.zxrq\"\n                  style=\"width: 100%\"\n                ></el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"发布日期:\" prop=\"fbrq\">\n                <el-date-picker\n                  type=\"datetime\"\n                  placeholder=\"选择日期时间\"\n                  format=\"yyyy-MM-dd\"\n                  value-format=\"yyyy-MM-dd\"\n                  clearable\n                  v-model=\"form.fbrq\"\n                  style=\"width: 100%\"\n                ></el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"上传时间:\" prop=\"scsj\">\n                <el-date-picker\n                  type=\"datetime\"\n                  placeholder=\"选择日期时间\"\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\n                  clearable\n                  v-model=\"form.scsj\"\n                  style=\"width: 100%\"\n                ></el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"电压等级\" prop=\"dydj\">\n                <el-select v-model=\"form.dydj\" placeholder=\"请选择电压等级\">\n                  <el-option\n                    v-for=\"item in dydjList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n\n            <el-col :span=\"12\">\n              <el-form-item label=\"关联线路\" prop=\"xl\">\n                <el-input\n                  style=\"width:100%\"\n                  v-model=\"form.xl\"\n                  placeholder=\"请选择\"\n                  v-on:click.native=\"sysbSelectedClick\"\n                >\n                </el-input>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <el-form-item label=\"备注\" prop=\"remark\">\n              <el-input\n                v-model=\"form.remark\"\n                type=\"textarea\"\n                :row=\"2\"\n                :placeholder=\"isDisabled ? '' : '备注'\"\n              ></el-input>\n            </el-form-item>\n          </el-row>\n          <el-row>\n            <!--            <el-col :span=\"12\">\n            <el-form-item label=\"线路名称\" prop=\"xl\">\n                <treeselect v-model=\"form.xl\" :options=\"treeOptions\" :disabled=\"isDisabled\"/>\n            </el-form-item>\n            </el-col>-->\n            <el-col :span=\"24\">\n              <el-form-item label=\"图片上传:\" prop=\"attachmentid\">\n                <template slot-scope=\"scope\">\n                  <el-upload\n                    multiple\n                    class=\"upload-demo\"\n                    accept=\".jpg,.png\"\n                    ref=\"upload\"\n                    action=\"/isc-api/file/upload\"\n                    list-type=\"picture-card\"\n                    :file-list=\"imgList\"\n                    :before-upload=\"beforeUpload\"\n                    :data=\"uploadData\"\n                    :headers=\"upHeader\"\n                    :on-remove=\"handleRemove\"\n                    :on-preview=\"handlePictureCardPreview\"\n                    :on-change=\"handleChange\"\n                    :auto-upload=\"false\"\n                  >\n                    <!-- <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选取文件</el-button> -->\n                    <i class=\"el-icon-plus\"></i>\n                  </el-upload>\n                </template>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item\n                label=\"VISO文件上传:\"\n                prop=\"VISO\"\n                v-if=\"this.title != '图纸详情'\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-upload\n                    multiple\n                    class=\"upload-demo\"\n                    accept=\".vsd\"\n                    ref=\"upload_viso\"\n                    action=\"/isc-api/file/upload\"\n                    :before-upload=\"beforeUpload\"\n                    :data=\"uploadData\"\n                    :headers=\"upHeader\"\n                    :auto-upload=\"false\"\n                  >\n                    <el-button slot=\"trigger\" size=\"small\" type=\"primary\"\n                      >选取文件</el-button\n                    >\n                  </el-upload>\n                </template>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item\n                label=\"已上传附件：\"\n                prop=\"attachment\"\n                v-if=\"form.fileList.length > 0\"\n              >\n                <el-col :span=\"24\" v-for=\"(item, index) in form.fileList\">\n                  <!-- <el-form-item :label=\"(index+1).toString()\"> -->\n                  {{ index + 1 }}、\n                  {{ item.fileOldName }}\n                  <el-form>\n                    <el-button\n                      type=\"primary\"\n                      size=\"mini\"\n                      @click=\"downloadHandle(item)\"\n                      >下载</el-button\n                    >\n                    <el-button\n                      v-if=\"!isDisabled\"\n                      type=\"danger\"\n                      size=\"mini\"\n                      @click=\"deleteFileById(item)\"\n                      >删除</el-button\n                    >\n                    <!-- <viewer>\n                        <img v-if=\"item.fileType != 'vsd'\" :src=\"item.fileUrl\" width=\"30%\" height=\"30%\"/>\n                      </viewer> -->\n                  </el-form>\n                  <!-- </el-form-item> -->\n                </el-col>\n              </el-form-item>\n            </el-col>\n            <!-- v-if=\"this.title != '图纸详情'\" -->\n          </el-row>\n        </el-form>\n        <div slot=\"footer\">\n          <el-button @click=\"uploadClose\">取 消</el-button>\n          <el-button\n            v-if=\"\n              isDisabled && buttonNameShow && form.status > 0 && form.status < 4\n            \"\n            type=\"primary\"\n            @click=\"submit('rollback')\"\n            >退 回\n          </el-button>\n          <el-button\n            v-if=\"isDisabled && buttonNameShow\"\n            type=\"primary\"\n            @click=\"submit('complete')\"\n            >{{ buttonName }}\n          </el-button>\n          <el-button\n            v-if=\"!isDisabled\"\n            type=\"primary\"\n            @click=\"saveRow\"\n            :loading=\"saveLoading\"\n            >保 存\n          </el-button>\n        </div>\n      </el-dialog>\n    </el-white>\n    <time-line\n      :value=\"timeLineShow\"\n      :timeData=\"timeData\"\n      @closeTimeLine=\"colseTimeLine\"\n    />\n    <el-dialog\n      title=\"审批流程图\"\n      :visible.sync=\"openLoadingImg\"\n      width=\"56%\"\n      append-to-body\n      :close-on-click-modal=\"false\"\n      v-dialogDrag\n    >\n      <div>\n        <img :src=\"imgSrc\" style=\"width: 100%\" />\n      </div>\n    </el-dialog>\n\n    <el-dialog :visible.sync=\"dialogVisible\" v-dialogDrag title=\"图片预览\">\n      <!-- <img width=\"100%\" :src=\"dialogImageUrl\" alt=\"\"> -->\n      <div>\n        <el-carousel\n          trigger=\"click\"\n          height=\"150px\"\n          indicator-position=\"none\"\n          type=\"card\"\n          class=\"imgCls\"\n          arrow=\"always\"\n          :autoplay=\"false\"\n        >\n          <el-carousel-item v-for=\"(img, index) in imgList\" :key=\"index\">\n            <viewer :images=\"imgList\" style=\"z-index: 999\">\n              <img :src=\"img.url\" width=\"100%\" height=\"100%\" />\n            </viewer>\n          </el-carousel-item>\n        </el-carousel>\n      </div>\n    </el-dialog>\n    <activiti\n      ref=\"activiti\"\n      :processData=\"processData\"\n      :isShow=\"isShow\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n    ></activiti>\n\n    <!--线路选择组件-->\n    <el-dialog\n      title=\"线路选择\"\n      :visible.sync=\"isShowXlDialog\"\n      width=\"20%\"\n      v-if=\"isShowXlDialog\"\n    >\n      <sdxl-selected\n        @closeSysbSelectDialog=\"handleControlSysbSelectDialog\"\n        @handleAcceptSbData=\"handleAcceptSbData\"\n        :isMutiply=\"true\"\n      ></sdxl-selected>\n    </el-dialog>\n\n    <el-dialog :visible.sync=\"showTzFlag\" v-dialogDrag title=\"线路跳转\">\n      <div v-for=\"(xlmc, index) in xlData\" :key=\"index\">\n        <router-link\n          :to=\"{\n            path: '/dwzygl/dwzygl/sdsbgl/dagangOilfield/dwzygl/sdsbgl/sdsb',\n            query: { lineName: xlmc }\n          }\"\n        >\n          <el-button type=\"text\" size=\"small\" style=\"color:#337ab7;\">{{\n            xlmc\n          }}</el-button>\n        </router-link>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport {\n  getListTZ,\n  remove,\n  saveOrUpdate,\n  updateById\n} from \"@/api/dagangOilfield/bzgl/bzgfgl/bzgfgl\";\nimport {\n  deleteById,\n  downloadByFileId,\n  getListByBusinessId\n} from \"@/api/tool/file\";\nimport activiti from \"com/activiti_tzgl\";\nimport { HistoryList } from \"@/api/activiti/processTask\";\nimport timeLine from \"com/timeLine\";\nimport CompTable from \"com/CompTable\";\nimport { getDictTypeData } from \"@/api/system/dict/data\";\nimport treeselect from \"@riophae/vue-treeselect\";\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\nimport { getTreeList } from \"@/api/dagangOilfield/asset/sdgt\";\nimport sdxlSelected from \"@/views/dagangOilfield/xjgl/sdxj/sdxlSelected\";\n\nexport default {\n  components: {\n    CompTable,\n    timeLine,\n    activiti,\n    HistoryList,\n    treeselect,\n    sdxlSelected\n  },\n  name: \"tzgl\",\n  data() {\n    var validatePass = (rule, value, callback) => {\n      if (!this.form.xl) {\n        callback(new Error());\n      } else {\n        callback();\n      }\n    };\n    return {\n      currentUser: this.$store.getters.name,\n      //线路选择弹出\n      isShowXlDialog: false,\n      showTzFlag: false,\n      xlData: [],\n      dialogImageUrl: \"\",\n      dialogVisible: false,\n      saveLoading: false,\n      // 非单个禁用\n      single: true,\n      //线路树数据\n      treeOptions: [],\n      //tab页名称\n      activeName: \"110\",\n      activeNameTow: \"1\",\n      dydjList: [],\n      buttonName: \"\",\n      buttonNameShow: false,\n      timeLineShow: false,\n      timeData: [],\n      openLoadingImg: false,\n      imgSrc: \"\",\n      activitiOption: { title: \"上报\" },\n      isShow: false,\n      processData: {\n        processDefinitionKey: \"tzsplc\",\n        businessKey: \"\",\n        businessType: \"图纸审批流程\",\n        variables: {},\n        nextUser: \"\",\n        defaultFrom: true,\n        processType: \"complete\"\n      },\n      isDisabled: false,\n      islast: \"1\",\n      params: {\n        wjlx: \"1\",\n        fbrq: \"\",\n        zxrq: \"\",\n        status: \"\",\n        wjlxList: [],\n        statusList: []\n      },\n      title: \"\",\n      selectRows: [],\n      ids: [],\n      form: {\n        wjlx: \"1\",\n        fileList: [],\n        xl: \"\"\n      },\n      filterInfo: {\n        data: {\n          // wjlx: '1',\n          fbrqArr: \"\",\n          zxrqArr: \"\",\n          status: \"\",\n          wjlxList: [],\n          statusList: [],\n          wjbh: \"\",\n          wjmc: \"\"\n        },\n        fieldList: [\n          { label: \"图纸管理文件编号\", type: \"input\", value: \"wjbh\" },\n          { label: \"图纸管理文件名称\", type: \"input\", value: \"wjmc\" },\n          {\n            label: \"状态\",\n            type: \"select\",\n            value: \"statusList\",\n            multiple: true,\n            options: []\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          {\n            prop: \"wjbh\",\n            label: \"图纸管理文件编号\",\n            minWidth: \"160\",\n            custom: true\n          },\n          { prop: \"wjmc\", label: \"图纸管理文件名称\", minWidth: \"160\" },\n          // { prop: 'wjlxName', label: '文件类型', minWidth: '120' },\n          { prop: \"fbrq\", label: \"发布日期\", minWidth: \"120\" },\n          { prop: \"zxrq\", label: \"执行日期\", minWidth: \"120\" },\n          { prop: \"fbr\", label: \"发布人\", minWidth: \"120\" },\n          // { prop: 'zxr', label: '执行人', minWidth: '120' },\n          { prop: \"scsj\", label: \"上传时间\", minWidth: \"140\", custom: true },\n          { prop: \"statusName\", label: \"状态\", minWidth: \"100\" }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      HighlightShow: true,\n      defaultProps: {},\n      fileOpen: false,\n      // 文件上传数据\n      uploadData: {\n        type: \"\",\n        businessId: undefined\n      },\n      imgList: [],\n      // 文件上传请求头\n      upHeader: { token: getToken() },\n      value: null,\n      //文件类型下拉框数据\n      // fileTypeOptions: [],\n      //状态下拉框数据\n      statusOptions: [\n        {\n          value: 0,\n          label: \"线路分公司待上报\"\n        },\n        {\n          value: 1,\n          label: \"线路分公司领导审批\"\n        },\n        {\n          value: 2,\n          label: \"生产技术科专工确认\"\n        },\n        {\n          value: 3,\n          label: \"生产技术科领导审批\"\n        },\n        {\n          value: 5,\n          label: \"电力调度未接收，科技中心未接收\"\n        },\n        {\n          value: 6,\n          label: \"电力调度已接收、科技中心未接收\"\n        },\n        {\n          value: 7,\n          label: \"科技中心已接收、电力调度未接收\"\n        },\n        {\n          value: 4,\n          label: \"结束\"\n        }\n      ],\n      //校验规则\n      rules: {\n        wjbh: [\n          { required: true, message: \"文件编号不能为空\", trigger: \"blur\" }\n        ],\n        wjmc: [\n          { required: true, message: \"文件名称不能为空\", trigger: \"blur\" }\n        ],\n        fbr: [{ required: true, message: \"发布人不能为空\", trigger: \"blur\" }],\n        // zxr: [\n        //   { required: true, message: '执行人不能为空', trigger: 'blur' }\n        // ],\n        zxrq: [\n          { required: true, message: \"执行日期不能为空\", trigger: \"select\" }\n        ],\n        fbrq: [\n          { required: true, message: \"发布日期不能为空\", trigger: \"select\" }\n        ],\n        scsj: [\n          { required: true, message: \"上传时间不能为空\", trigger: \"select\" }\n        ],\n        dydj: [{ required: true, message: \"不能为空\", trigger: \"select\" }],\n        xl: [\n          {\n            required: true,\n            message: \"不能为空\",\n            validator: validatePass,\n            trigger: \"change\"\n          }\n        ]\n      }\n    };\n  },\n  watch: {},\n  async created() {\n    let { data: dg_dydj } = await getDictTypeData(\"dg_dydj\");\n    this.dydjList = dg_dydj;\n    this.treeList();\n    this.initDomain();\n  },\n  mounted() {\n    this.getData(this.$route.query);\n  },\n  methods: {\n    handleRemove(file, fileList) {\n      if (file.fileId) {\n        this.deleteFileById(file);\n        this.form.fileList = fileList;\n      }\n    },\n    handleChange(file, fileList) {\n      this.imgList = fileList;\n    },\n    handlePictureCardPreview(file) {\n      // this.dialogImageUrl = file.url;\n      this.dialogVisible = true;\n    },\n    //关闭线路弹窗\n    handleControlSysbSelectDialog(isShow) {\n      this.isShowXlDialog = isShow;\n    },\n    //线路选择事件\n    sysbSelectedClick() {\n      if (this.isDisabled) {\n        this.isShowXlDialog = false;\n      } else {\n        this.isShowXlDialog = true;\n      }\n    },\n    //线路选择接收数据\n    handleAcceptSbData(sbData, index) {\n      this.form.xl = sbData\n        .map(item => {\n          return item.label;\n        })\n        .join(\",\");\n    },\n    getPage(xlmc) {\n      let query = { lineName: xlmc };\n      this.$router.push({\n        path: this.path,\n        query: query\n      });\n      console.log(xlmc);\n    },\n    sortChange({ column, prop, order }) {\n      if (order) {\n        if (order.indexOf(\"desc\") > -1) {\n          this.params.mySorts = [{ prop: prop, asc: false }];\n        } else {\n          this.params.mySorts = [{ prop: prop, asc: true }];\n        }\n      } else {\n        this.params.mySorts = [{ prop: \"updateTime\", asc: false }];\n      }\n      this.getData();\n    },\n    //获取树结构数据\n    treeList() {\n      getTreeList().then(res => {\n        this.treeOptions = res.data;\n      });\n    },\n    handleClick() {\n      this.tableAndPageInfo.pager.pageResize = \"Y\";\n      this.filterReset();\n      this.getData(this.$route.query);\n    },\n    getShow(row) {\n      switch (row.status) {\n        case 0:\n          this.buttonName = \"提 交\";\n          if (this.$store.getters.name === this.form.createBy) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case 1:\n          this.buttonName = \"通 过\";\n          if (this.$store.getters.name === this.form.fgsspr) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case 2:\n          this.buttonName = \"通 过\";\n          if (this.$store.getters.name === this.form.sckzgspr) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case 3:\n          this.buttonName = \"通 过\";\n          if (this.$store.getters.name === this.form.sckldspr) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case 5:\n          this.buttonName = \"接 收\";\n          if (this.form.dlddzxshr.includes(this.currentUser)) {\n            this.buttonNameShow = true;\n          }\n          if (this.form.kjzxshr.includes(this.currentUser)) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case 6:\n          this.buttonName = \"接 收\";\n          if (this.form.kjzxshr.includes(this.currentUser)) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case 7:\n          this.buttonName = \"接 收\";\n          if (this.form.dlddzxshr.includes(this.currentUser)) {\n            this.buttonNameShow = true;\n          }\n          break;\n      }\n    },\n    //关闭流程历史弹框\n    colseTimeLine() {\n      this.timeLineShow = false;\n    },\n    //流程历史查看\n    async showTimeLine(row) {\n      this.processData.businessKey = row.objId;\n      let { code, data } = await HistoryList(this.processData);\n      this.timeData = data;\n      this.timeLineShow = true;\n    },\n    //流程图片查看\n    showProcessImg(row) {\n      this.openLoadingImg = true;\n      this.imgSrc =\n        \"/activiti-api/process/read-resource?processDefinitionKey=tzsplc&businessKey=\" +\n        row.objId +\n        \"&t=\" +\n        new Date().getTime();\n    },\n    closeActiviti() {\n      if (this.$refs.activiti.$refs.form) {\n        this.$refs.activiti.$refs.form.resetFields();\n      }\n      this.isShow = false;\n    },\n    async deleteFileById(file) {\n      let { code } = await deleteById(file.fileId);\n      if (code === \"0000\") {\n        await this.getFileList(file.businessId);\n        // await this.getData();\n        this.$message({\n          type: \"success\",\n          message: \"文件删除成功!\"\n        });\n      }\n    },\n    clearUpload() {\n      if (this.$refs.upload) {\n        this.$refs.upload.clearFiles();\n        this.$refs.upload_viso.clearFiles();\n      }\n    },\n    async getFileList(id) {\n      let { code, data } = await getListByBusinessId({ businessId: id });\n      if (code === \"0000\") {\n        this.form.fileList = data;\n        this.imgList = [];\n        data.forEach(item => {\n          if (item.fileType != \"vsd\") {\n            this.imgList.push({\n              url: item.fileUrl,\n              fileId: item.fileId,\n              businessId: item.businessId\n            });\n          }\n        });\n      }\n    },\n    //日期格式化  yyyy-MM-dd\n    dateFormatter(d) {\n      let year = d.getFullYear();\n      let month =\n        d.getMonth() < 9 ? \"0\" + (d.getMonth() + 1) : \"\" + (d.getMonth() + 1);\n      let day = d.getDate() < 10 ? \"0\" + d.getDate() : \"\" + d.getDate();\n      return year + \"-\" + month + \"-\" + day;\n    },\n    //列表查询\n    //点击开关时触发\n    switchChahge(value) {\n      this.getData();\n    },\n    async getData(params) {\n      this.$refs.compTable.loading = true;\n      try {\n        //tabs参数\n        if (this.activeNameTow !== \"1\") {\n          this.params.dydj = \"\";\n        } else {\n          this.params.dydj = this.activeName;\n        }\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        if (param.fbrqArr && param.fbrqArr.length > 0) {\n          param.fbrqStart = this.dateFormatter(param.fbrqArr[0]);\n          param.fbrqEnd = this.dateFormatter(param.fbrqArr[1]);\n        }\n        if (param.zxrqArr && param.zxrqArr.length > 0) {\n          param.zxrqStart = this.dateFormatter(param.zxrqArr[0]);\n          param.zxrqEnd = this.dateFormatter(param.zxrqArr[1]);\n        }\n\n        param.islast = this.activeNameTow;\n\n        if (!param.mySorts) {\n          param.mySorts = [{ prop: \"updateTime\", asc: false }];\n        }\n        getListTZ(param).then(res => {\n          if (res.code === \"0000\") {\n            this.tableAndPageInfo.tableData = res.data.records;\n            this.tableAndPageInfo.pager.total = res.data.total;\n\n            this.tableAndPageInfo.tableData.forEach(item => {\n              this.statusOptions.forEach(element => {\n                if (item.status === element.value) {\n                  item.statusName = element.label;\n                }\n              });\n            });\n          }\n          this.$refs.compTable.loading = false;\n        });\n      } catch (e) {\n        this.$refs.compTable.loading = false;\n        console.log(e);\n      }\n    },\n    //新增\n    async saveRow() {\n      this.saveLoading = true;\n      await this.$refs[\"form\"].validate(async valid => {\n        if (valid) {\n          let { code, data } = await saveOrUpdate(this.form);\n          if (code === \"0000\") {\n            this.uploadData.businessId = data.objId;\n            this.$refs.upload.submit();\n            this.$refs.upload_viso.submit();\n            this.clearUpload();\n            this.fileOpen = false;\n            this.saveLoading = false;\n            this.$message.success(\"操作成功\");\n            await this.getData();\n          }\n        } else {\n          this.saveLoading = false;\n        }\n      });\n    },\n    //编辑按钮\n    updateRow(row) {\n      this.getFileList(row.objId);\n      this.isDisabled = false;\n      // this.clearUpload()\n      this.title = \"修改图纸\";\n      this.isDisabled = false;\n      this.form = { ...row };\n      this.fileOpen = true;\n    },\n    //顶部编辑按钮\n    updateTopRow() {\n      if (this.selectRows.length < 1) {\n        this.$message.warning(\"请选择正确的数据！！！\");\n        return;\n      }\n      if (this.selectRows.length > 1) {\n        this.$message.warning(\"只能选中一条数据！！！\");\n        return;\n      }\n      let row = this.selectRows[0];\n      console.log(\"row\", row);\n      this.getFileList(row.objId);\n      this.isDisabled = false;\n      // this.clearUpload()\n      this.title = \"修改图纸\";\n      this.isDisabled = false;\n      this.form = { ...row };\n      this.fileOpen = true;\n    },\n    //详情按钮\n    getInfo(row) {\n      this.getFileList(row.objId);\n      this.buttonNameShow = false;\n      this.isDisabled = true;\n      this.title = \"图纸详情\";\n      this.form = { ...row };\n      this.isDisabled = true;\n      this.fileOpen = true;\n      this.getShow(row);\n    },\n    //线路跳转页\n    getXlInfo(row) {\n      this.xlData = [];\n      if (row.xl) {\n        this.xlData = row.xl.split(\",\");\n      }\n      this.showTzFlag = true;\n    },\n    //删除\n    deleteRow(id) {\n      let objId = \"\";\n      if (typeof id === \"object\") {\n        if (this.ids.length < 1) {\n          this.$message.warning(\"请选择正确的数据！！！\");\n          return;\n        }\n        objId = this.ids[0];\n      } else {\n        objId = id;\n      }\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove(JSON.stringify(objId)).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {});\n    },\n\n    addRow() {\n      this.saveLoading = false;\n      this.isDisabled = false;\n      this.resetForm(\"form\");\n      this.imgList = [];\n      // this.clearUpload()\n      this.form = {\n        wjlx: \"1\",\n        fileList: []\n      };\n      this.title = \"新增图纸\";\n      //获取当前登录人名称\n      this.form.fbr = this.$store.getters.nickName;\n      this.fileOpen = true;\n    },\n    addHistoryRow() {\n      this.saveLoading = false;\n      this.isDisabled = false;\n      this.resetForm(\"form\");\n      this.imgList = [];\n      // this.clearUpload()\n      this.form = {\n        wjlx: \"1\",\n        fileList: []\n      };\n      this.title = \"新增图纸\";\n      //获取当前登录人名称\n      this.form.fbr = this.$store.getters.nickName;\n      this.form.dataFlag = \"历史新增\";\n      this.fileOpen = true;\n    },\n    filterReset() {\n      this.$refs.compTable.$refs.multipleTable.clearSort();\n      this.params = {\n        wjlx: \"1\",\n        fbrq: \"\",\n        zxrq: \"\",\n        status: \"\",\n        wjlxList: [],\n        statusList: []\n      };\n    },\n    /**\n     * 上传文件之前的处理\n     */\n    beforeUpload(file) {\n      const fileSize = file.size < 1024 * 1024 * 50; //10M\n      if (!fileSize) {\n        this.$message.error(\"上传文件大小不能超过 50MB!\");\n      }\n      let size = file.size / 1024;\n    },\n\n    /**\n     * 上传成功回调函数\n     */\n    onSuccess(response, file, fileList) {},\n    /**下载附件*/\n    downloadHandle(file) {\n      downloadByFileId(file.fileId, file.fileOldName);\n    },\n    /**\n     * 关闭上传附件对话框\n     */\n    uploadClose() {\n      this.imgList = [];\n      this.resetForm(\"form\");\n      this.form.attachmentname = \"\";\n      this.fileOpen = false;\n    },\n    //选择行\n    selectChange(rows) {\n      this.selectRows = rows;\n      this.ids = rows.map(item => item.objId);\n      this.single = rows.length !== 1;\n    },\n    initDomain() {\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.value === \"statusList\") {\n          item.options = this.statusOptions;\n        }\n      });\n    },\n    //流程结束回调\n    async todoResult(data) {\n      let row1 = {};\n      if (data.processType === \"rollback\") {\n        switch (data.activeTaskName) {\n          case \"xlfgssb\":\n            row1.status = 0;\n            break;\n          case \"xlfgssp\":\n            row1.status = 1;\n            break;\n          case \"sckzgqr\":\n            row1.status = 2;\n            break;\n        }\n      } else {\n        switch (data.activeTaskName) {\n          case \"xlfgssp\":\n            row1.status = 1;\n            row1.fgsspr = data.nextUser;\n            break;\n          case \"sckzgqr\":\n            row1.status = 2;\n            row1.sckzgspr = data.nextUser;\n            break;\n          case \"sckldsp\":\n            row1.status = 3;\n            row1.sckldspr = data.nextUser;\n            break;\n          case \"zxddkzzxshkjxxzxsh\":\n            row1.status = 5;\n            row1.kjzxshr = data.nextUser;\n            row1.dlddzxshr = data.nextUser2;\n            break;\n          case \"kjxxzxsh\":\n            row1.status = 6;\n            break;\n          case \"zxddkzzxsh\":\n            row1.status = 7;\n            break;\n          case \"结束\":\n            row1.status = 4;\n            break;\n        }\n      }\n      let row = {\n        objId: data.businessKey,\n        isStart: 1,\n        isBack: data.processType === \"rollback\" ? 1 : 0\n      };\n      row = { ...row, ...row1 };\n      saveOrUpdate(row).then(response => {\n        if (response.code === \"0000\") {\n          this.$message({\n            type: \"success\",\n            message: \"操作成功!\"\n          });\n          this.tableAndPageInfo.pager.pageResize = \"Y\";\n          this.getData();\n        }\n      });\n    },\n    submit(type) {\n      this.fileOpen = false;\n      if (type === \"complete\") {\n        this.processData.status = this.form.status;\n        switch (this.form.status) {\n          case 0:\n            this.isShow = true;\n            this.activitiOption.title = \"上报\";\n            this.processData.defaultFrom = true;\n            this.processData.processType = \"complete\";\n            this.processData.businessKey = this.form.objId;\n            this.processData.variables.pass = true;\n            this.isShow = true;\n            break;\n          case 1:\n            this.activitiOption.title = \"通过\";\n            this.processData.defaultFrom = true;\n            this.processData.processType = \"complete\";\n            this.processData.businessKey = this.form.objId;\n            this.processData.variables.pass = true;\n            this.isShow = true;\n            break;\n          case 2:\n            this.activitiOption.title = \"通过\";\n            this.processData.defaultFrom = true;\n            this.processData.processType = \"complete\";\n            this.processData.businessKey = this.form.objId;\n            this.processData.variables.pass = true;\n            this.isShow = true;\n            break;\n          case 3:\n            this.activitiOption.title = \"通过\";\n            this.processData.defaultFrom = true;\n            this.processData.processType = \"complete\";\n            this.processData.businessKey = this.form.objId;\n            this.processData.variables.pass = true;\n            this.processData.taskId = \"sckldsp\";\n            this.isShow = true;\n            break;\n          case 5:\n            this.activitiOption.title = \"接收\";\n            this.processData.defaultFrom = false;\n            this.processData.processType = \"complete\";\n            this.processData.businessKey = this.form.objId;\n            this.processData.variables.pass = true;\n            if (this.form.dlddzxshr.includes(this.currentUser)) {\n              this.processData.taskId = \"zxddkzzxsh\";\n            }\n            if (this.form.kjzxshr === this.currentUser) {\n              this.processData.taskId = \"kjxxzxsh\";\n            }\n            this.isShow = true;\n            break;\n          case 6:\n            this.activitiOption.title = \"接收\";\n            this.processData.defaultFrom = false;\n            this.processData.processType = \"complete\";\n            this.processData.businessKey = this.form.objId;\n            this.processData.variables.pass = true;\n            this.isShow = true;\n            break;\n          case 7:\n            this.activitiOption.title = \"接收\";\n            this.processData.defaultFrom = false;\n            this.processData.processType = \"complete\";\n            this.processData.businessKey = this.form.objId;\n            this.processData.variables.pass = true;\n            this.isShow = true;\n            break;\n        }\n      } else {\n        this.activitiOption.title = \"回退原因填写\";\n        this.processData.defaultFrom = true;\n        this.processData.processType = \"rollback\";\n        this.processData.businessKey = this.form.objId;\n        this.processData.variables.pass = false;\n        this.isShow = true;\n      }\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.style-bottom {\n  margin-bottom: 4vh;\n}\n\n.pull-left {\n  margin-left: 2vw !important;\n}\n.el-carousel__item h3 {\n  color: #475669;\n  font-size: 14px;\n  opacity: 0.75;\n  line-height: 150px;\n}\n\n.el-carousel__item:nth-child(2n) {\n  background-color: #99a9bf;\n}\n\n.el-carousel__item:nth-child(2n + 1) {\n  background-color: #d3dce6;\n}\n.imgCls {\n  height: 310px !important;\n}\n</style>\n\n<style>\n.el-carousel__container {\n  background-color: #ffffff !important;\n}\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>field/jszlgl"}]}