{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\activitimodel\\leave.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\activitimodel\\leave.vue", "mtime": 1706897321992}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["leave.vue"], "names": [], "mappings": ";;;;;;;;;;;AAwHA;;AAGA;;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,eAAA,EAAA;AAAA,GAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA,MAAA,UAAA,EAAA,IADA;AAEA;AACA,MAAA,OAAA,EAAA,IAHA;AAIA,MAAA,cAAA,EAAA,KAJA;AAKA,MAAA,WAAA,EAAA,KALA;AAMA;AACA,MAAA,GAAA,EAAA,EAPA;AAQA;AACA,MAAA,MAAA,EAAA,IATA;AAUA;AACA,MAAA,QAAA,EAAA,IAXA;AAYA;AACA,MAAA,UAAA,EAAA,IAbA;AAcA;AACA,MAAA,KAAA,EAAA,CAfA;AAgBA,MAAA,MAAA,EAAA,EAhBA;AAiBA;AACA,MAAA,SAAA,EAAA,IAlBA;AAmBA;AACA,MAAA,KAAA,EAAA,IApBA;AAqBA;AACA,MAAA,WAAA,EAAA,EAtBA;AAuBA;AACA,MAAA,IAAA,EAAA,KAxBA;AAyBA;AACA;AACA,MAAA,IAAA,EAAA,EA3BA;AA4BA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA;AAFA,OA7BA;AAiCA;AACA,MAAA,KAAA,EAAA;AACA,QAAA,KAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,QAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,OAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAVA;AAlCA,KAAA;AAiDA,GArDA;AAsDA,EAAA,OAtDA,qBAsDA;AACA,SAAA,OAAA;AACA,GAxDA;AAyDA,EAAA,OAAA,EAAA;AACA;;AACA;AACA,IAAA,OAHA,qBAGA;AAAA;;AACA,WAAA,OAAA,GAAA,IAAA;AACA,uBAAA,KAAA,WAAA,EAAA,IAAA,CACA,UAAA,QAAA,EAAA;AACA,QAAA,KAAA,CAAA,SAAA,GAAA,QAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,KAAA,CAAA,KAAA,GAAA,QAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,KAAA,CAAA,OAAA,GAAA,KAAA;AACA,OALA;AAOA,KAZA;;AAaA;;;AAGA,IAAA,MAhBA,oBAgBA;AACA,WAAA,IAAA,GAAA,KAAA;AACA,WAAA,KAAA;AACA,KAnBA;;AAoBA;;;AAGA,IAAA,KAvBA,mBAuBA;AACA,WAAA,IAAA,GAAA;AACA,QAAA,GAAA,EAAA,SADA;AAEA,QAAA,IAAA,EAAA,SAFA;AAGA,QAAA,WAAA,EAAA;AAHA,OAAA;AAKA,WAAA,SAAA,CAAA,MAAA;AACA,KA9BA;;AA+BA;AACA,IAAA,WAhCA,yBAgCA;AACA,WAAA,WAAA,CAAA,OAAA,GAAA,CAAA;AACA,WAAA,OAAA;AACA,KAnCA;;AAoCA;AACA,IAAA,UArCA,wBAqCA;AACA,WAAA,SAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,WAAA;AACA,WAAA,WAAA;AACA,KAzCA;;AA0CA;AACA,IAAA,qBA3CA,iCA2CA,SA3CA,EA2CA;AACA,WAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,MAAA;AAAA,OAAA,CAAA;AACA,WAAA,MAAA,GAAA,SAAA,CAAA,MAAA,IAAA,CAAA;AACA,WAAA,QAAA,GAAA,CAAA,SAAA,CAAA,MAAA;AACA,KA/CA;;AAgDA;AACA,IAAA,SAjDA,uBAiDA;AACA,WAAA,KAAA;AACA,WAAA,IAAA,GAAA,IAAA;AACA,KApDA;;AAqDA;AACA,IAAA,YAtDA,wBAsDA,GAtDA,EAsDA;AAAA;;AACA,UAAA,EAAA,GAAA,GAAA,CAAA,EAAA;AACA,WAAA,QAAA,CACA,WADA,EAEA,IAFA,EAGA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAHA,EASA,IATA,CASA,YAAA;AACA,eAAA,mBAAA,EAAA,CAAA;AACA,OAXA,EAYA,IAZA,CAYA,YAAA;AACA,QAAA,MAAA,CAAA,OAAA;;AACA,QAAA,MAAA,CAAA,UAAA,CAAA,MAAA;AACA,OAfA,EAgBA,KAhBA,CAgBA,YAAA,CACA,CAjBA;AAkBA,KA1EA;;AA2EA;AACA,IAAA,UAAA,EAAA,sBAAA;AAAA;;AACA,WAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,2BAAA,MAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA,gBAAA,QAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,cAAA,MAAA,CAAA,UAAA,CAAA,MAAA;;AACA,cAAA,MAAA,CAAA,IAAA,GAAA,KAAA;;AACA,cAAA,MAAA,CAAA,OAAA;AACA;AACA,WANA;AAOA;AACA,OAVA;AAWA,KAxFA;;AAyFA;AACA,IAAA,YA1FA,wBA0FA,GA1FA,EA0FA;AAAA;;AACA,UAAA,EAAA,GAAA,GAAA,CAAA,EAAA;AACA,WAAA,QAAA,CACA,eAAA,EAAA,GAAA,QADA,EAEA,IAFA,EAGA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAHA,EASA,IATA,CASA,YAAA;AACA,eAAA,gBAAA,EAAA,CAAA;AACA,OAXA,EAYA,IAZA,CAYA,YAAA;AACA,QAAA,MAAA,CAAA,OAAA;;AACA,QAAA,MAAA,CAAA,UAAA,CAAA,MAAA;AACA,OAfA,EAgBA,KAhBA,CAgBA,YAAA,CACA,CAjBA;AAkBA,KA9GA;;AA+GA;AACA,IAAA,YAhHA,wBAgHA,GAhHA,EAgHA;AACA,MAAA,WAAA,CAAA,GAAA,CAAA,EAAA,CAAA;AACA,KAlHA;;AAmHA;AACA,IAAA,YApHA,wBAoHA,GApHA,EAoHA;AAAA;;AACA,MAAA,MAAA,CAAA,GAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,GAAA,CAAA,GAAA;AACA,SAFA,MAEA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,GAAA,CAAA,GAAA;AACA;AACA,OANA;AAOA,KA5HA;;AA6HA;AACA,IAAA,aA9HA,yBA8HA,GA9HA,EA8HA;AACA,WAAA,WAAA,GAAA,IAAA;AACA,WAAA,UAAA,GAAA,GAAA,CAAA,UAAA;AACA,KAjIA;;AAkIA;AACA,IAAA,UAnIA,sBAmIA,GAnIA,EAmIA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,WAAA,MAAA,GAAA,oDAAA,GAAA,CAAA,UAAA,GAAA,KAAA,GAAA,IAAA,IAAA,GAAA,OAAA,EAAA;AACA,KAtIA;;AAuIA;AACA,IAAA,YAxIA,wBAwIA,GAxIA,EAwIA;AAAA;;AACA,UAAA,YAAA,GAAA,GAAA,CAAA,YAAA;AACA,mDAAA;AAAA,QAAA,EAAA,EAAA,GAAA,CAAA,EAAA;AAAA,QAAA,YAAA,EAAA,GAAA,CAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,YAAA,IAAA,GAAA,GAAA,MAAA,GAAA,MAAA;;AACA,UAAA,MAAA,CAAA,OAAA;AACA;AACA,OALA;AAMA;AAhJA;AAzDA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n      <el-form class=\"searchForm1\" :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"120px\">\n        <el-form-item label=\"key：\" prop=\"key\">\n          <el-input v-model=\"queryParams.key\" placeholder=\"请输入key\" clearable class=\"common-width\"\n                    @keyup.enter.native=\"handleQuery\"/>\n        </el-form-item>\n        <el-form-item label=\"名称：\" prop=\"name\">\n          <el-input v-model=\"queryParams.name\" placeholder=\"名称\" clearable class=\"common-width\"\n                    @keyup.enter.native=\"handleQuery\"/>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"cyan\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\n          <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\" type=\"warning\">重置</el-button>\n        </el-form-item>\n      </el-form>\n\n    <el-row :gutter=\"20\">\n      <el-col :span=\"24\">\n        <el-white class=\"button-group\">\n          <el-row :gutter=\"10\" class=\"mb8 pull-right\">\n            <el-col :span=\"1.5\">\n              <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd\"\n              >新增<!-- v-hasPermi=\"['activiti:model:add']\"-->\n              </el-button>\n            </el-col>\n          </el-row>\n          <el-table style=\"margin-top: 15px;\" stripe border v-loading=\"loading\" :data=\"leaveList\" @selection-change=\"handleSelectionChange\">\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n            <el-table-column label=\"id\" align=\"center\" prop=\"id\" width=\"60\"/>\n            <el-table-column label=\"请假类型\" align=\"center\" prop=\"type\"  />\n            <el-table-column label=\"标题\" align=\"center\" prop=\"title\" />\n            <el-table-column label=\"原因\" align=\"center\" prop=\"reason\" width=\"120\"/>\n            <el-table-column label=\"开始时间\" align=\"center\" prop=\"startTime\"  />\n            <el-table-column label=\"结束时间\" align=\"center\" prop=\"endTime\"  />\n            <el-table-column label=\"流程实例ID\" align=\"center\" prop=\"instanceId\"  />\n            <el-table-column label=\"创建人\" align=\"center\" prop=\"createBy\"  />\n            <el-table-column label=\"申请人\" align=\"center\" prop=\"applyUser\"  />\n            <el-table-column label=\"申请时间\" align=\"center\" prop=\"applyTime\"  />\n            <el-table-column label=\"当前任务名称\" align=\"center\" prop=\"taskName\"  />\n            <el-table-column label=\"操作\" align=\"center\"   class-name=\"small-padding fixed-width\">\n              <template slot-scope=\"scope\">\n                <slot v-if=\"!scope.row.instanceId\">\n                  <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleCommit(scope.row)\" >提交申请</el-button>\n                  <el-button size=\"mini\" type=\"text\" icon=\"el-icon-upload\" @click=\"handleDeploy(scope.row)\" >编辑</el-button>\n                  <el-button  size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\"  >删除</el-button>\n                </slot>\n                <slot v-else>\n                  <el-button size=\"mini\" type=\"text\"   @click=\"handleHistory(scope.row)\" >审批历史</el-button>\n                  <el-button size=\"mini\" type=\"text\"   @click=\"handlePlan(scope.row)\" >进度查看</el-button>\n                  <slot v-if=\"scope.row.taskName.indexOf('已结束')==-1\">\n                    <el-button size=\"mini\" type=\"text\"   @click=\"handleCancelApply(scope.row)\" >撤销</el-button>\n                    <slot v-if=\"scope.row.suspendState==2\">\n                      <el-button size=\"mini\" type=\"text\" icon=\"el-icon-check\" @click=\"handleUpdate(scope.row)\" >激活</el-button>\n                    </slot>\n                    <slot  v-else>\n                      <el-button size=\"mini\" type=\"text\" icon=\"el-icon-time\" @click=\"handleUpdate(scope.row)\" >挂起</el-button>\n                    </slot>\n                  </slot>\n                </slot>\n              </template>\n            </el-table-column>\n          </el-table>\n          <pagination v-show=\"total>0\"\n                      :total=\"total\"\n                      :page.sync=\"queryParams.pageNum\"\n                      :limit.sync=\"queryParams.pageSize\"\n                      @pagination=\"getList\"\n          />\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!-- 添加或修改参数配置对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"480px\" append-to-body :close-on-click-modal=false v-dialogDrag>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"60px\">\n            <el-form-item label=\"标题\" prop=\"name\">\n              <el-input v-model=\"form.title\" placeholder=\"请输入名称\" maxlength=\"30\"/>\n            </el-form-item>\n            <el-form-item label=\"原因\" prop=\"reason\">\n              <el-input v-model=\"form.reason\" placeholder=\"请输入key\" maxlength=\"30\"/>\n            </el-form-item>\n            <el-form-item label=\"开始时间\">\n              <el-date-picker\n                v-model=\"form.startTime\"\n                type=\"datetime\"\n                format=\"yyyy-MM-dd HH:mm:ss\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n                placeholder=\"选择日期时间\">\n              </el-date-picker>\n            </el-form-item>\n          <el-form-item label=\"结束时间\">\n            <el-date-picker\n              v-model=\"form.endTime\"\n              type=\"datetime\"\n              format=\"yyyy-MM-dd HH:mm:ss\"\n              value-format=\"yyyy-MM-dd HH:mm:ss\"\n              placeholder=\"选择日期时间\">\n            </el-date-picker>\n          </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog title=\"审批历史\" :visible.sync=\"openHistory\" width=\"800px\" append-to-body :close-on-click-modal=false v-dialogDrag>\n      <slot v-if=\"instanceId!=null\">\n        <activiti-history :instance-id=\"instanceId\" />\n      </slot>\n    </el-dialog>\n\n    <el-dialog title=\"审批进度\" :visible.sync=\"openLoadingImg\" width=\"800px\" append-to-body :close-on-click-modal=false v-dialogDrag>\n      <img :src=\"imgSrc\"/>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  import {\n    list,save,del,commit\n  } from \"@/api/activiti/leave\";\n  import {\n    suspendOrActiveApply\n  } from \"@/api/activiti/processdefinition\";\n  import ActivitiHistory from \"../todoitem/history\";\n  export default {\n    name: \"Leave\",\n    components: {ActivitiHistory},\n    data() {\n      return {\n        instanceId:null,\n        // 遮罩层\n        loading: true,\n        openLoadingImg:false,\n        openHistory:false,\n        // 选中数组\n        ids: [],\n        // 非单个禁用\n        single: true,\n        // 非多个禁用\n        multiple: true,\n        // 显示搜索条件\n        showSearch: true,\n        // 总条数\n        total: 0,\n        imgSrc:'',\n        // 用户表格数据\n        leaveList: null,\n        // 弹出层标题\n        title: \"请假\",\n        // 部门树选项\n        deptOptions: [],\n        // 是否显示弹出层\n        open: false,\n        // 部门名称\n        // 表单参数\n        form: {},\n        // 查询参数\n        queryParams: {\n          pageNum: 1,\n          pageSize: 10,\n        },\n        // 表单校验\n        rules: {\n          title: [\n            {required: true, message: \"key不能为空\", trigger: \"blur\"},\n          ],\n          reason: [\n            {required: true, message: \"名称不能为空\", trigger: \"blur\"},\n          ],\n          startTime: [\n            {required: true, message: \"开始时间不能为空\", trigger: \"blur\"},\n          ],\n          endTime: [\n            {required: true, message: \"结束时间不能为空\", trigger: \"blur\"},\n          ],\n        },\n      };\n    },\n    created() {\n      this.getList();\n    },\n    methods: {\n      // 测试数据字典方法结束\n      /** 查询用户列表 */\n      getList() {\n        this.loading = true;\n        list(this.queryParams).then(\n          (response) => {\n            this.leaveList = response.data.records;\n            this.total = response.data.total;\n            this.loading = false;\n          }\n        );\n      },\n     /**\n      * 取消按钮\n      * */\n      cancel() {\n        this.open = false;\n        this.reset();\n      },\n      /**\n       * 表单重置\n       * */\n      reset() {\n        this.form = {\n          key: undefined,\n          name: undefined,\n          description:undefined\n        };\n        this.resetForm(\"form\");\n      },\n      /** 搜索按钮操作 */\n      handleQuery() {\n        this.queryParams.pageNum = 1;\n        this.getList();\n      },\n      /** 重置按钮操作 */\n      resetQuery() {\n        this.dateRange = [];\n        this.resetForm(\"queryForm\");\n        this.handleQuery();\n      },\n      /***多选框选中数据***/\n      handleSelectionChange(selection) {\n        this.ids = selection.map((item) => item.userId);\n        this.single = selection.length != 1;\n        this.multiple = !selection.length;\n      },\n      /** 新增按钮操作 */\n      handleAdd() {\n        this.reset();\n        this.open = true;\n      },\n      /** 编辑按钮操作 */\n      handleCommit(row) {\n        const id = row.id;\n        this.$confirm(\n          '确认要提交申请吗?',\n          \"警告\",\n          {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\",\n          }\n        )\n          .then(function () {\n            return commit(id);\n          })\n          .then(() => {\n            this.getList();\n            this.msgSuccess(\"提交成功\");\n          })\n          .catch(function () {\n          });\n      },\n      /** 提交按钮 */\n      submitForm: function () {\n        this.$refs[\"form\"].validate((valid) => {\n          if (valid) {\n            save(this.form).then((response) => {\n              if (response.code === '0000') {\n                this.msgSuccess(\"新增成功\");\n                this.open = false;\n                this.getList();\n              }\n            });\n          }\n        });\n      },\n      /** 删除按钮操作 */\n      handleDelete(row) {\n        const id = row.id;\n        this.$confirm(\n          '是否确认删除id为\"' + id + '\"的数据项?',\n          \"警告\",\n          {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\",\n          }\n        )\n          .then(function () {\n            return del(id);\n          })\n          .then(() => {\n            this.getList();\n            this.msgSuccess(\"删除成功\");\n          })\n          .catch(function () {\n          });\n      },\n      /***  导出bpm文件 ***/\n      handleExport(row){\n        exportModel(row.id);\n      },\n      /** 部署 **/\n      handleDeploy(row){\n        deploy(row.id).then(res =>{\n          if(res.code=='0000'){\n            this.msgSuccess(res.msg);\n          }else{\n            this.msgError(res.msg);\n          }\n        })\n      },\n      /***  审批历史 ***/\n      handleHistory(row){\n        this.openHistory = true;\n        this.instanceId = row.instanceId\n      },\n      /**** 进度查看  ****/\n      handlePlan(row){\n        this.openLoadingImg = true\n        this.imgSrc =\"/activiti-api/process/read-resource?instanceId=\"+row.instanceId+\"&t=\"+new Date().getTime();\n      },\n      /** 编辑按钮操作 */\n      handleUpdate(row) {\n        let suspendState = row.suspendState;\n        suspendOrActiveApply({id:row.id,suspendState:row.suspendState}).then(res =>{\n          if(res.code==\"0000\"){\n            this.msgSuccess(suspendState==\"2\"?\"激活成功\":\"挂起成功\");\n            this.getList();\n          }\n        })\n      },\n    },\n  };\n</script>\n"], "sourceRoot": "src/views/activiti/activitimodel"}]}