{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\activiti_yxgc\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\activiti_yxgc\\index.vue", "mtime": 1720606743102}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZm9yLWVhY2giKTsKCnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLmZvci1lYWNoIik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKCnJlcXVpcmUoInJlZ2VuZXJhdG9yLXJ1bnRpbWUvcnVudGltZSIpOwoKdmFyIF9hc3luY1RvR2VuZXJhdG9yMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovU2hhbW1wb29sL3dvcmsvY29kZS9kZ3l0LzAxXHU0RUUzXHU3ODAxL3F6LXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2FzeW5jVG9HZW5lcmF0b3IiKSk7Cgp2YXIgX29iamVjdFNwcmVhZDIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkQ6L1NoYW1tcG9vbC93b3JrL2NvZGUvZGd5dC8wMVx1NEVFM1x1NzgwMS9xei11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9vYmplY3RTcHJlYWQyIikpOwoKdmFyIF9wcm9jZXNzVGFzayA9IHJlcXVpcmUoIkAvYXBpL2FjdGl2aXRpL3Byb2Nlc3NUYXNrIik7Cgp2YXIgX2VsZW1lbnRVaSA9IHJlcXVpcmUoImVsZW1lbnQtdWkiKTsKCnZhciBfdXNlcmdyb3VwID0gcmVxdWlyZSgiQC9hcGkvc3lzdGVtL3VzZXJncm91cCIpOwoKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0MiA9IHsKICBuYW1lOiAiaW5kZXgiLAogIHByb3BzOiB7CiAgICAvKua1geeoi+WPkei1t+W/heWhq+aVsOaNrgogICAgcHJvY2Vzc0RhdGE6ewogICAgICBwcm9jZXNzRGVmaW5pdGlvbktleToi5rWB56iL5a6a5LmJ55qEa2V5IiwvL+W/heWhqwogICAgICB0YXNrSWQ6IuS7u+WKoWlk77yM5aaC5p6c5Lu75Yqh5Zyo5Luj5Yqe5YiX6KGo5pe277yM5Lya5qC55o2u5Luj5Yqe5YiX6KGo6I635Y+WdGFza2lk77yM5ZCm5YiZ6ZyA5Lyg5YWl5Lia5YqhaWTmnaXnoa7lrpp0YXNrIiwKICAgICAgYnVzaW5lc3NLZXk6IuS4muWKoWlk77yM5a+55bqU5Lia5Yqh55qE5Li76ZSuIiwvL3Rhc2tpZOWSjGJ1c2luZXNzS2V55Lik6ICF5b+F6aG75pyJ5YW25Lit5LiA5Liq5omN5Y+v5Lul56Gu5a6a5LiA5LiqdGFzawogICAgICBidXNpbmVzc1R5cGU6IuS4muWKoeexu+Wei++8jOeUqOS6juWMuuWIhuS4jeWQjOeahOS4muWKoSIvL+W/heWhqwogICAgICB2YXJpYWJsZXM6IuaLk+WxleWPguaVsCIvL+a1geeoi+WumuS5ieS4reiuvue9rueahOWPguaVsCwKICAgICAgbmV4dFVzZXI6IuWmguaenOa1geeoi+WunuS+i+S4reW5tuacqumFjee9ruavj+S4gOS4quiKgueCueeahOWkhOeQhuS6uu+8jOWImemcgOimgeeUqOaIt+aJi+WKqOmAieaLqeavj+S4gOS4quiKgueCueeahOWkhOeQhuS6uiIsCiAgICAgIHByb2Nlc3NUeXBlOidjb21wbGV0ZSxyb2xsYmFjaycsCiAgICAgIGRlZmF1bHRGcm9tOnRydWUsZmFsc2Ug5piv5ZCm6ZyA6KaB6buY6K6k6KGo5Y2VCiAgICB9Ki8KICAgIHByb2Nlc3NEYXRhOiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdDogZnVuY3Rpb24gX2RlZmF1bHQoKSB7CiAgICAgICAgcmV0dXJuIHsKICAgICAgICAgIGRlZmF1bHRGcm9tOiB0cnVlLAogICAgICAgICAgcHJvY2Vzc1R5cGU6ICJjb21wbGV0ZSIKICAgICAgICB9OwogICAgICB9CiAgICB9LAogICAgLy/mmL7npLrpmpDol48KICAgIGlzU2hvdzogewogICAgICB0eXBlOiBCb29sZWFuLAogICAgICBkZWZhdWx0OiBmYWxzZQogICAgfSwKICAgIC8v5a2Q57uE5Lu26buY6K6k5Y+C5pWwCiAgICBvcHRpb246IHsKICAgICAgdHlwZTogT2JqZWN0LAogICAgICBkZWZhdWx0OiBmdW5jdGlvbiBfZGVmYXVsdCgpIHsKICAgICAgICByZXR1cm4gewogICAgICAgICAgdGl0bGU6ICLlrqHmibkiCiAgICAgICAgfTsKICAgICAgfQogICAgfQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGZvcm06IHt9LAogICAgICBkYXRhczoge30sCiAgICAgIGxvYWRpbmc6IG51bGwsCiAgICAgIGRsZGRzaHJPcHRpb25zOiBbXSwKICAgICAga2p6eHNock9wdGlvbnM6IFtdLAogICAgICB1c2Vyc09wdGlvbnM6IFtdCiAgICB9OwogIH0sCiAgd2F0Y2g6IHsKICAgIHByb2Nlc3NEYXRhOiB7CiAgICAgIGhhbmRsZXI6IGZ1bmN0aW9uIGhhbmRsZXIobmV3VmFsLCBvbGRWYWwpIHsKICAgICAgICB2YXIgX3RoaXMgPSB0aGlzOwoKICAgICAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgICAgICBfdGhpcy4kc2V0KF90aGlzLmZvcm0sICJuZXh0VXNlcjIiLCBbXSk7CgogICAgICAgICAgX3RoaXMuZGF0YXMgPSAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIG5ld1ZhbCk7CgogICAgICAgICAgaWYgKF90aGlzLmRhdGFzLnRhc2tJZCA9PT0gImZnc2xkc2giKSB7CiAgICAgICAgICAgICgwLCBfdXNlcmdyb3VwLmdldFVzZXJzKSh7CiAgICAgICAgICAgICAgcGVyc29uR3JvdXBJZDogODAsCiAgICAgICAgICAgICAgZGVwdElkOiAiIiwKICAgICAgICAgICAgICBkZXB0TmFtZTogIiIKICAgICAgICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgICAgX3RoaXMuZGxkZHNock9wdGlvbnMgPSByZXMuZGF0YTsKICAgICAgICAgICAgICBfdGhpcy5mb3JtLm5leHRVc2VyMiA9IF90aGlzLmRsZGRzaHJPcHRpb25zOwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgKDAsIF91c2VyZ3JvdXAuZ2V0VXNlcnMpKHsKICAgICAgICAgICAgICBwZXJzb25Hcm91cElkOiA4MSwKICAgICAgICAgICAgICBkZXB0SWQ6ICIiLAogICAgICAgICAgICAgIGRlcHROYW1lOiAiIgogICAgICAgICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgICBfdGhpcy5ranp4c2hyT3B0aW9ucyA9IHJlcy5kYXRhOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGlmIChfdGhpcy5kYXRhcy5wZXJzb25Hcm91cElkKSB7CiAgICAgICAgICAgICAgKDAsIF91c2VyZ3JvdXAuZ2V0VXNlcnMpKHsKICAgICAgICAgICAgICAgIHBlcnNvbkdyb3VwSWQ6IF90aGlzLmRhdGFzLnBlcnNvbkdyb3VwSWQsCiAgICAgICAgICAgICAgICBkZXB0SWQ6ICIiLAogICAgICAgICAgICAgICAgZGVwdE5hbWU6ICIiCiAgICAgICAgICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgICAgICBfdGhpcy51c2Vyc09wdGlvbnMgPSByZXMuZGF0YTsKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9LAogICAgICBkZWVwOiB0cnVlLAogICAgICBpbW1lZGlhdGU6IHRydWUKICAgIH0KICB9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7fSwKICBtZXRob2RzOiB7CiAgICAvL+a1geeoi+aPkOS6pO+8jOa1geeoi+aLkue7nQogICAgdG9kb1N1Ym1pdDogZnVuY3Rpb24gdG9kb1N1Ym1pdCgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CgogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSggLyojX19QVVJFX18qL3JlZ2VuZXJhdG9yUnVudGltZS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUoKSB7CiAgICAgICAgdmFyIG5leHRVc2VyLCBuZXh0TmljaywgcmVzdWx0RGF0YSwgX3lpZWxkJGNvbXBsZXRlWXhnY1RhLCBjb2RlLCBkYXRhOwoKICAgICAgICByZXR1cm4gcmVnZW5lcmF0b3JSdW50aW1lLndyYXAoZnVuY3Rpb24gX2NhbGxlZSQoX2NvbnRleHQpIHsKICAgICAgICAgIHdoaWxlICgxKSB7CiAgICAgICAgICAgIHN3aXRjaCAoX2NvbnRleHQucHJldiA9IF9jb250ZXh0Lm5leHQpIHsKICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICBpZiAoIV90aGlzMi5kYXRhcy5yb3V0ZVBhdGgpIHsKICAgICAgICAgICAgICAgICAgX3RoaXMyLmRhdGFzLnJvdXRlUGF0aCA9IF90aGlzMi4kcm91dGUucGF0aDsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICBpZiAoX3RoaXMyLmZvcm0ubmV4dFVzZXIpIHsKICAgICAgICAgICAgICAgICAgX3RoaXMyLmRhdGFzLm5leHRVc2VyID0gX3RoaXMyLmZvcm0ubmV4dFVzZXIudXNlck5hbWU7CiAgICAgICAgICAgICAgICAgIF90aGlzMi5kYXRhcy5uZXh0VXNlck5pY2tOYW1lID0gX3RoaXMyLmZvcm0ubmV4dFVzZXIubmlja05hbWU7CiAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgaWYgKF90aGlzMi5mb3JtLm5leHRVc2VyMiAmJiBfdGhpczIuZm9ybS5uZXh0VXNlcjIubGVuZ3RoID4gMCkgewogICAgICAgICAgICAgICAgICBuZXh0VXNlciA9ICIiOwogICAgICAgICAgICAgICAgICBuZXh0TmljayA9ICIiOwoKICAgICAgICAgICAgICAgICAgX3RoaXMyLmZvcm0ubmV4dFVzZXIyLmZvckVhY2goZnVuY3Rpb24gKGUpIHsKICAgICAgICAgICAgICAgICAgICBuZXh0VXNlciArPSBlLnVzZXJOYW1lICsgIiwiOwogICAgICAgICAgICAgICAgICAgIG5leHROaWNrICs9IGUubmlja05hbWUgKyAiLCI7CiAgICAgICAgICAgICAgICAgIH0pOyAvL+WOu+aOieacgOWQjuS4gOS4qumAl+WPtwoKCiAgICAgICAgICAgICAgICAgIGlmIChuZXh0VXNlci5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgICAgICAgICAgbmV4dFVzZXIgPSBuZXh0VXNlci5zdWJzdHJpbmcoMCwgbmV4dFVzZXIubGVuZ3RoIC0gMSk7CiAgICAgICAgICAgICAgICAgICAgbmV4dE5pY2sgPSBuZXh0Tmljay5zdWJzdHJpbmcoMCwgbmV4dE5pY2subGVuZ3RoIC0gMSk7CiAgICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAgIF90aGlzMi5kYXRhcy5uZXh0VXNlcjIgPSBuZXh0VXNlcjsKICAgICAgICAgICAgICAgICAgX3RoaXMyLmRhdGFzLm5leHRVc2VyTmlja05hbWUyID0gbmV4dE5pY2s7CiAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgaWYgKCEoX3RoaXMyLmRhdGFzLnRhc2tJZCA9PT0gImZnc2xkc2giKSkgewogICAgICAgICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gNzsKICAgICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgICAgICB9CgogICAgICAgICAgICAgICAgaWYgKCEoIV90aGlzMi5kYXRhcy5uZXh0VXNlciB8fCAhX3RoaXMyLmRhdGFzLm5leHRVc2VyMikpIHsKICAgICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDc7CiAgICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICAgIF90aGlzMi4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICAgIHR5cGU6ICJlcnJvciIsCiAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLor7fpgInmi6nkurrlkZghIgogICAgICAgICAgICAgICAgfSk7CgogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0LmFicnVwdCgicmV0dXJuIik7CgogICAgICAgICAgICAgIGNhc2UgNzoKICAgICAgICAgICAgICAgIGlmICghKCFfdGhpczIuZGF0YXMubmV4dFVzZXIgJiYgX3RoaXMyLmRhdGFzLnByb2Nlc3NUeXBlID09PSAiY29tcGxldGUiICYmIF90aGlzMi5kYXRhcy5kZWZhdWx0RnJvbSkpIHsKICAgICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDEwOwogICAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICBfdGhpczIuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgICB0eXBlOiAiZXJyb3IiLAogICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi6K+36YCJ5oup5Lq65ZGYISIKICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5hYnJ1cHQoInJldHVybiIpOwoKICAgICAgICAgICAgICBjYXNlIDEwOgogICAgICAgICAgICAgICAgaWYgKF90aGlzMi5mb3JtLmNvbW1lbnQpIHsKICAgICAgICAgICAgICAgICAgX3RoaXMyLmRhdGFzLnZhcmlhYmxlcy5jb21tZW50ID0gX3RoaXMyLmZvcm0uY29tbWVudDsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICBfdGhpczIuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgICAgICAgLy8g5Lul5pyN5Yqh55qE5pa55byP6LCD55So55qEIExvYWRpbmcg6ZyA6KaB5byC5q2l5YWz6ZetCiAgICAgICAgICAgICAgICAgIF90aGlzMi5sb2FkaW5nID0gX2VsZW1lbnRVaS5Mb2FkaW5nLnNlcnZpY2UoewogICAgICAgICAgICAgICAgICAgIGxvY2s6IHRydWUsCiAgICAgICAgICAgICAgICAgICAgLy9sb2Nr55qE5L+u5pS556ymLS3pu5jorqTmmK9mYWxzZQogICAgICAgICAgICAgICAgICAgIHRleHQ6ICLmtYHnqIvov5vooYzkuK3vvIzor7fnqI3lkI4iLAogICAgICAgICAgICAgICAgICAgIC8v5pi+56S65Zyo5Yqg6L295Zu+5qCH5LiL5pa555qE5Yqg6L295paH5qGICiAgICAgICAgICAgICAgICAgICAgc3Bpbm5lcjogImVsLWljb24tbG9hZGluZyIsCiAgICAgICAgICAgICAgICAgICAgLy/oh6rlrprkuYnliqDovb3lm77moIfnsbvlkI0KICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAicmdiYSgwLCAwLCAwLCAwLjcpIiwKICAgICAgICAgICAgICAgICAgICAvL+mBrue9qeWxguminOiJsgogICAgICAgICAgICAgICAgICAgIHRhcmdldDogZG9jdW1lbnQucXVlcnlTZWxlY3RvcigiI2RpYWxvZ0FjdCIpCiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgfSk7CgogICAgICAgICAgICAgICAgX2NvbnRleHQucHJldiA9IDEyOwogICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDE1OwogICAgICAgICAgICAgICAgcmV0dXJuICgwLCBfcHJvY2Vzc1Rhc2suY29tcGxldGVZeGdjVGFzaykoX3RoaXMyLmRhdGFzKTsKCiAgICAgICAgICAgICAgY2FzZSAxNToKICAgICAgICAgICAgICAgIF95aWVsZCRjb21wbGV0ZVl4Z2NUYSA9IF9jb250ZXh0LnNlbnQ7CiAgICAgICAgICAgICAgICBjb2RlID0gX3lpZWxkJGNvbXBsZXRlWXhnY1RhLmNvZGU7CiAgICAgICAgICAgICAgICBkYXRhID0gX3lpZWxkJGNvbXBsZXRlWXhnY1RhLmRhdGE7CgogICAgICAgICAgICAgICAgaWYgKGNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgICAgICByZXN1bHREYXRhID0gZGF0YTsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICBpZiAoY29kZSkgewogICAgICAgICAgICAgICAgICBfdGhpczIuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgICAgICAgICAvLyDku6XmnI3liqHnmoTmlrnlvI/osIPnlKjnmoQgTG9hZGluZyDpnIDopoHlvILmraXlhbPpl60KICAgICAgICAgICAgICAgICAgICBfdGhpczIubG9hZGluZy5jbG9zZSgpOwogICAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gMjU7CiAgICAgICAgICAgICAgICBicmVhazsKCiAgICAgICAgICAgICAgY2FzZSAyMjoKICAgICAgICAgICAgICAgIF9jb250ZXh0LnByZXYgPSAyMjsKICAgICAgICAgICAgICAgIF9jb250ZXh0LnQwID0gX2NvbnRleHRbImNhdGNoIl0oMTIpOwoKICAgICAgICAgICAgICAgIF90aGlzMi4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgICAgICAgICAgICAvLyDku6XmnI3liqHnmoTmlrnlvI/osIPnlKjnmoQgTG9hZGluZyDpnIDopoHlvILmraXlhbPpl60KICAgICAgICAgICAgICAgICAgX3RoaXMyLmxvYWRpbmcuY2xvc2UoKTsKICAgICAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgICBjYXNlIDI1OgogICAgICAgICAgICAgICAgaWYgKHJlc3VsdERhdGEpIHsKICAgICAgICAgICAgICAgICAgcmVzdWx0RGF0YS5wcm9jZXNzVHlwZSA9IF90aGlzMi5kYXRhcy5wcm9jZXNzVHlwZTsKCiAgICAgICAgICAgICAgICAgIF90aGlzMi4kZW1pdCgidG9kb0RhdGEiLCByZXN1bHREYXRhKTsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICBfdGhpczIudG9DbG9zZSgpOwoKICAgICAgICAgICAgICBjYXNlIDI3OgogICAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQuc3RvcCgpOwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZSwgbnVsbCwgW1sxMiwgMjJdXSk7CiAgICAgIH0pKSgpOwogICAgfSwKICAgIHRvQ2xvc2U6IGZ1bmN0aW9uIHRvQ2xvc2UoKSB7CiAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICJuZXh0VXNlciIsICIiKTsKICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgImNvbW1lbnQiLCAiIik7CiAgICAgIHRoaXMuJGVtaXQoInRvQ2xvc2UiLCAiY2xvc2UiKTsKICAgIH0KICB9Cn07CmV4cG9ydHMuZGVmYXVsdCA9IF9kZWZhdWx0Mjs="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAqIA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAEA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,KAAA,EAAA;AACA;;;;;;;;;;;AAWA,IAAA,WAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAFA,sBAEA;AACA,eAAA;AACA,UAAA,WAAA,EAAA,IADA;AAEA,UAAA,WAAA,EAAA;AAFA,SAAA;AAIA;AAPA,KAZA;AAqBA;AACA,IAAA,MAAA,EAAA;AACA,MAAA,IAAA,EAAA,OADA;AAEA,MAAA,OAAA,EAAA;AAFA,KAtBA;AA0BA;AACA,IAAA,MAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAFA,sBAEA;AACA,eAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAAA;AACA;AAJA;AA3BA,GAFA;AAoCA,EAAA,IApCA,kBAoCA;AACA,WAAA;AACA,MAAA,IAAA,EAAA,EADA;AAEA,MAAA,KAAA,EAAA,EAFA;AAGA,MAAA,OAAA,EAAA,IAHA;AAIA,MAAA,cAAA,EAAA,EAJA;AAKA,MAAA,cAAA,EAAA,EALA;AAMA,MAAA,YAAA,EAAA;AANA,KAAA;AAQA,GA7CA;AA8CA,EAAA,KAAA,EAAA;AACA,IAAA,WAAA,EAAA;AACA,MAAA,OADA,mBACA,MADA,EACA,MADA,EACA;AAAA;;AACA,aAAA,SAAA,CAAA,YAAA;AACA,UAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,WAAA,EAAA,EAAA;;AACA,UAAA,KAAA,CAAA,KAAA,mCAAA,MAAA;;AACA,cAAA,KAAA,CAAA,KAAA,CAAA,MAAA,KAAA,SAAA,EAAA;AACA,qCAAA;AAAA,cAAA,aAAA,EAAA,EAAA;AAAA,cAAA,MAAA,EAAA,EAAA;AAAA,cAAA,QAAA,EAAA;AAAA,aAAA,EAAA,IAAA,CACA,UAAA,GAAA,EAAA;AACA,cAAA,KAAA,CAAA,cAAA,GAAA,GAAA,CAAA,IAAA;AACA,cAAA,KAAA,CAAA,IAAA,CAAA,SAAA,GAAA,KAAA,CAAA,cAAA;AACA,aAJA;AAMA,qCAAA;AAAA,cAAA,aAAA,EAAA,EAAA;AAAA,cAAA,MAAA,EAAA,EAAA;AAAA,cAAA,QAAA,EAAA;AAAA,aAAA,EAAA,IAAA,CACA,UAAA,GAAA,EAAA;AACA,cAAA,KAAA,CAAA,cAAA,GAAA,GAAA,CAAA,IAAA;AACA,aAHA;AAKA,WAZA,MAYA;AACA,gBAAA,KAAA,CAAA,KAAA,CAAA,aAAA,EAAA;AACA,uCAAA;AACA,gBAAA,aAAA,EAAA,KAAA,CAAA,KAAA,CAAA,aADA;AAEA,gBAAA,MAAA,EAAA,EAFA;AAGA,gBAAA,QAAA,EAAA;AAHA,eAAA,EAIA,IAJA,CAIA,UAAA,GAAA,EAAA;AACA,gBAAA,KAAA,CAAA,YAAA,GAAA,GAAA,CAAA,IAAA;AACA,eANA;AAOA;AACA;AACA,SA1BA;AA2BA,OA7BA;AA8BA,MAAA,IAAA,EAAA,IA9BA;AA+BA,MAAA,SAAA,EAAA;AA/BA;AADA,GA9CA;AAiFA,EAAA,OAjFA,qBAiFA,CAAA,CAjFA;AAkFA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,UAFA,wBAEA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,oBAAA,CAAA,MAAA,CAAA,KAAA,CAAA,SAAA,EAAA;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,SAAA,GAAA,MAAA,CAAA,MAAA,CAAA,IAAA;AACA;;AACA,oBAAA,MAAA,CAAA,IAAA,CAAA,QAAA,EAAA;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,QAAA,GAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,QAAA;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,gBAAA,GAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,QAAA;AACA;;AAEA,oBAAA,MAAA,CAAA,IAAA,CAAA,SAAA,IAAA,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,kBAAA,QADA,GACA,EADA;AAEA,kBAAA,QAFA,GAEA,EAFA;;AAGA,kBAAA,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,CAAA,EAAA;AACA,oBAAA,QAAA,IAAA,CAAA,CAAA,QAAA,GAAA,GAAA;AACA,oBAAA,QAAA,IAAA,CAAA,CAAA,QAAA,GAAA,GAAA;AACA,mBAHA,EAHA,CAOA;;;AACA,sBAAA,QAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,oBAAA,QAAA,GAAA,QAAA,CAAA,SAAA,CAAA,CAAA,EAAA,QAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA,oBAAA,QAAA,GAAA,QAAA,CAAA,SAAA,CAAA,CAAA,EAAA,QAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA;;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,SAAA,GAAA,QAAA;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,iBAAA,GAAA,QAAA;AACA;;AAvBA,sBAyBA,MAAA,CAAA,KAAA,CAAA,MAAA,KAAA,SAzBA;AAAA;AAAA;AAAA;;AAAA,sBA0BA,CAAA,MAAA,CAAA,KAAA,CAAA,QAAA,IAAA,CAAA,MAAA,CAAA,KAAA,CAAA,SA1BA;AAAA;AAAA;AAAA;;AA2BA,gBAAA,MAAA,CAAA,QAAA,CAAA;AACA,kBAAA,IAAA,EAAA,OADA;AAEA,kBAAA,OAAA,EAAA;AAFA,iBAAA;;AA3BA;;AAAA;AAAA,sBAmCA,CAAA,MAAA,CAAA,KAAA,CAAA,QAAA,IACA,MAAA,CAAA,KAAA,CAAA,WAAA,KAAA,UADA,IAEA,MAAA,CAAA,KAAA,CAAA,WArCA;AAAA;AAAA;AAAA;;AAuCA,gBAAA,MAAA,CAAA,QAAA,CAAA;AACA,kBAAA,IAAA,EAAA,OADA;AAEA,kBAAA,OAAA,EAAA;AAFA,iBAAA;;AAvCA;;AAAA;AA6CA,oBAAA,MAAA,CAAA,IAAA,CAAA,OAAA,EAAA;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,SAAA,CAAA,OAAA,GAAA,MAAA,CAAA,IAAA,CAAA,OAAA;AACA;;AACA,gBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,kBAAA,MAAA,CAAA,OAAA,GAAA,mBAAA,OAAA,CAAA;AACA,oBAAA,IAAA,EAAA,IADA;AACA;AACA,oBAAA,IAAA,EAAA,WAFA;AAEA;AACA,oBAAA,OAAA,EAAA,iBAHA;AAGA;AACA,oBAAA,UAAA,EAAA,oBAJA;AAIA;AACA,oBAAA,MAAA,EAAA,QAAA,CAAA,aAAA,CAAA,YAAA;AALA,mBAAA,CAAA;AAOA,iBATA;;AAhDA;AAAA;AAAA,uBA4DA,mCAAA,MAAA,CAAA,KAAA,CA5DA;;AAAA;AAAA;AA4DA,gBAAA,IA5DA,yBA4DA,IA5DA;AA4DA,gBAAA,IA5DA,yBA4DA,IA5DA;;AA6DA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,UAAA,GAAA,IAAA;AACA;;AACA,oBAAA,IAAA,EAAA;AACA,kBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,oBAAA,MAAA,CAAA,OAAA,CAAA,KAAA;AACA,mBAHA;AAIA;;AArEA;AAAA;;AAAA;AAAA;AAAA;;AAuEA,gBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,kBAAA,MAAA,CAAA,OAAA,CAAA,KAAA;AACA,iBAHA;;AAvEA;AA4EA,oBAAA,UAAA,EAAA;AACA,kBAAA,UAAA,CAAA,WAAA,GAAA,MAAA,CAAA,KAAA,CAAA,WAAA;;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,UAAA,EAAA,UAAA;AACA;;AACA,gBAAA,MAAA,CAAA,OAAA;;AAhFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiFA,KAnFA;AAoFA,IAAA,OApFA,qBAoFA;AACA,WAAA,IAAA,CAAA,KAAA,IAAA,EAAA,UAAA,EAAA,EAAA;AACA,WAAA,IAAA,CAAA,KAAA,IAAA,EAAA,SAAA,EAAA,EAAA;AACA,WAAA,KAAA,CAAA,SAAA,EAAA,OAAA;AACA;AAxFA;AAlFA,C", "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      id=\"dialogAct\"\n      class=\"tree\"\n      :title=\"option.title ? option.title : ''\"\n      :top=\"option.top ? option.top : '10vh'\"\n      v-dialogDrag\n      :visible.sync=\"isShow\"\n      :width=\"option.width ? option.width : '25%'\"\n      :close-on-click-modal=\"false\"\n      @close=\"toClose\"\n      :destroy-on-colse=\"true\"\n      :modal-append-to-body=\"false\"\n    >\n      <slot name=\"Dialog_content\" />\n      <div v-if=\"datas.defaultFrom\">\n        <el-form\n          label-width=\"120px\"\n          ref=\"form\"\n          :model=\"form\"\n          v-if=\"datas.taskId === 'fgsldsh'\"\n        >\n          <div>\n            <el-row>\n              <div v-if=\"datas.processType === 'complete'\">\n                <el-col :span=\"24\">\n                  <el-form-item prop=\"nextUser2\" label=\"电力调度中心审核人\">\n                    <el-select\n                      v-model=\"form.nextUser2\"\n                      placeholder=\"请选择人员\"\n                      style=\"width: 100%;\"\n                      value-key=\"userName\"\n                      clearable\n                      filterable\n                      multiple\n                      disabled\n                    >\n                      <el-option\n                        v-for=\"item in dlddshrOptions\"\n                        :key=\"item.userName\"\n                        :label=\"item.nickName\"\n                        :value=\"item\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"24\">\n                  <el-form-item prop=\"nextUser\" label=\"科技中心审核人\">\n                    <el-select\n                      v-model=\"form.nextUser\"\n                      placeholder=\"请选择人员\"\n                      style=\"width: 100%;\"\n                      value-key=\"userName\"\n                      clearable\n                      filterable\n                    >\n                      <el-option\n                        v-for=\"item in kjzxshrOptions\"\n                        :key=\"item.userName\"\n                        :label=\"item.nickName\"\n                        :value=\"{\n                          userName: item.userName,\n                          nickName: item.nickName\n                        }\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n              </div>\n            </el-row>\n          </div>\n        </el-form>\n        <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" v-else>\n          <div>\n            <el-row>\n              <div v-if=\"datas.processType === 'complete'\">\n                <el-col :span=\"24\">\n                  <el-form-item prop=\"nextUser\" label=\"审核人\">\n                    <el-select\n                      v-model=\"form.nextUser\"\n                      placeholder=\"请选择人员\"\n                      style=\"width: 100%;\"\n                      value-key=\"userName\"\n                      clearable\n                      filterable\n                    >\n                      <el-option\n                        v-for=\"item in usersOptions\"\n                        :key=\"item.userName\"\n                        :label=\"item.nickName\"\n                        :value=\"{\n                          userName: item.userName,\n                          nickName: item.nickName\n                        }\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n              </div>\n              <el-col :span=\"24\">\n                <el-form-item\n                  prop=\"comment\"\n                  label=\"回退原因：\"\n                  v-if=\"datas.processType === 'rollback'\"\n                >\n                  <el-input\n                    style=\"width: 100%\"\n                    v-model=\"form.comment\"\n                    type=\"textarea\"\n                    placeholder=\"请输入原因\"\n                  />\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </div>\n        </el-form>\n      </div>\n      <div v-else>\n        <span>请确定是否提交</span>\n      </div>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"info\" @click=\"toClose\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"todoSubmit('form')\">确定</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { completeYxgcTask } from \"@/api/activiti/processTask\";\nimport { Loading } from \"element-ui\";\nimport { getUsers } from \"@/api/system/usergroup\";\n\nexport default {\n  name: \"index\",\n  props: {\n    /*流程发起必填数据\n    processData:{\n      processDefinitionKey:\"流程定义的key\",//必填\n      taskId:\"任务id，如果任务在代办列表时，会根据代办列表获取taskid，否则需传入业务id来确定task\",\n      businessKey:\"业务id，对应业务的主键\",//taskid和businessKey两者必须有其中一个才可以确定一个task\n      businessType:\"业务类型，用于区分不同的业务\"//必填\n      variables:\"拓展参数\"//流程定义中设置的参数,\n      nextUser:\"如果流程实例中并未配置每一个节点的处理人，则需要用户手动选择每一个节点的处理人\",\n      processType:'complete,rollback',\n      defaultFrom:true,false 是否需要默认表单\n    }*/\n    processData: {\n      type: Object,\n      default() {\n        return {\n          defaultFrom: true,\n          processType: \"complete\"\n        };\n      }\n    },\n    //显示隐藏\n    isShow: {\n      type: Boolean,\n      default: false\n    },\n    //子组件默认参数\n    option: {\n      type: Object,\n      default() {\n        return { title: \"审批\" };\n      }\n    }\n  },\n  data() {\n    return {\n      form: {},\n      datas: {},\n      loading: null,\n      dlddshrOptions: [],\n      kjzxshrOptions: [],\n      usersOptions: []\n    };\n  },\n  watch: {\n    processData: {\n      handler(newVal, oldVal) {\n        this.$nextTick(() => {\n          this.$set(this.form, \"nextUser2\", []);\n          this.datas = { ...newVal };\n          if (this.datas.taskId === \"fgsldsh\") {\n            getUsers({ personGroupId: 80, deptId: \"\", deptName: \"\" }).then(\n              res => {\n                this.dlddshrOptions = res.data;\n                this.form.nextUser2 = this.dlddshrOptions;\n              }\n            );\n            getUsers({ personGroupId: 81, deptId: \"\", deptName: \"\" }).then(\n              res => {\n                this.kjzxshrOptions = res.data;\n              }\n            );\n          } else {\n            if (this.datas.personGroupId) {\n              getUsers({\n                personGroupId: this.datas.personGroupId,\n                deptId: \"\",\n                deptName: \"\"\n              }).then(res => {\n                this.usersOptions = res.data;\n              });\n            }\n          }\n        });\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  mounted() {},\n  methods: {\n    //流程提交，流程拒绝\n    async todoSubmit() {\n      if (!this.datas.routePath) {\n        this.datas.routePath = this.$route.path;\n      }\n      if (this.form.nextUser) {\n        this.datas.nextUser = this.form.nextUser.userName;\n        this.datas.nextUserNickName = this.form.nextUser.nickName;\n      }\n\n      if (this.form.nextUser2 && this.form.nextUser2.length > 0) {\n        let nextUser = \"\";\n        let nextNick = \"\";\n        this.form.nextUser2.forEach(e => {\n          nextUser += e.userName + \",\";\n          nextNick += e.nickName + \",\";\n        });\n        //去掉最后一个逗号\n        if (nextUser.length > 0) {\n          nextUser = nextUser.substring(0, nextUser.length - 1);\n          nextNick = nextNick.substring(0, nextNick.length - 1);\n        }\n        this.datas.nextUser2 = nextUser;\n        this.datas.nextUserNickName2 = nextNick;\n      }\n\n      if (this.datas.taskId === \"fgsldsh\") {\n        if (!this.datas.nextUser || !this.datas.nextUser2) {\n          this.$message({\n            type: \"error\",\n            message: \"请选择人员!\"\n          });\n          return;\n        }\n      }\n      if (\n        !this.datas.nextUser &&\n        this.datas.processType === \"complete\" &&\n        this.datas.defaultFrom\n      ) {\n        this.$message({\n          type: \"error\",\n          message: \"请选择人员!\"\n        });\n        return;\n      }\n      if (this.form.comment) {\n        this.datas.variables.comment = this.form.comment;\n      }\n      this.$nextTick(() => {\n        // 以服务的方式调用的 Loading 需要异步关闭\n        this.loading = Loading.service({\n          lock: true, //lock的修改符--默认是false\n          text: \"流程进行中，请稍后\", //显示在加载图标下方的加载文案\n          spinner: \"el-icon-loading\", //自定义加载图标类名\n          background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n          target: document.querySelector(\"#dialogAct\")\n        });\n      });\n      let resultData;\n      try {\n        let { code, data } = await completeYxgcTask(this.datas);\n        if (code === \"0000\") {\n          resultData = data;\n        }\n        if (code) {\n          this.$nextTick(() => {\n            // 以服务的方式调用的 Loading 需要异步关闭\n            this.loading.close();\n          });\n        }\n      } catch (e) {\n        this.$nextTick(() => {\n          // 以服务的方式调用的 Loading 需要异步关闭\n          this.loading.close();\n        });\n      }\n      if (resultData) {\n        resultData.processType = this.datas.processType;\n        this.$emit(\"todoData\", resultData);\n      }\n      this.toClose();\n    },\n    toClose() {\n      this.$set(this.form, \"nextUser\", \"\");\n      this.$set(this.form, \"comment\", \"\");\n      this.$emit(\"toClose\", \"close\");\n    }\n  }\n};\n</script>\n\n<style scoped></style>\n"], "sourceRoot": "src/components/activiti_yxgc"}]}