{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\sybglr.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\sybglr.vue", "mtime": 1706897323683}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgc2VsZWN0U3l4ekRhdGFCeVN5enlibSwKICBnZXRTeXp5U2VsZWN0ZWRPcHRpb25zLAogIGdldFN5YmdqbERhdGFCeVBhZ2UsCiAgc2F2ZU9yVXBkYXRlU3liZ2psLAogIHJlbW92ZVN5YmdqbERhdGEsCiAgZ2V0TW91bGRWYWx1ZSwKICBnZXRDaGlsZHNWYWx1ZSwKICBzYXZlQ2hpbGRzVmFsdWUsCiAgdXBkYXRlU3liZ2psLAogIGdldGJkekxpc3QsCiAgc2hvd0J1dHRlbkpTLAogIGdldFRvZG9JdGVtWWQKfSBmcm9tICJAL2FwaS9kYWdhbmdPaWxmaWVsZC9iemdsL3N5YmdsciI7CmltcG9ydCB7CiAgZ2V0RGV2aWNlQ2xhc3NUcmVlTm9kZUJ5UGlkLAogIGdldFBhZ2VEYXRhTGlzdCwKICBnZXRUYWJsZSwKICByZW1vdmUsCiAgc2F2ZU9yVXBkYXRlCn0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYnpnbC9zeW1way9zeW1wayI7CmltcG9ydCB7IExvYWRpbmcgfSBmcm9tICJlbGVtZW50LXVpIjsKaW1wb3J0IGFjdGl2aXRpIGZyb20gImNvbS9hY3Rpdml0aV9zeWJnIjsKaW1wb3J0IHRpbWVMaW5lIGZyb20gImNvbS90aW1lTGluZSI7CmltcG9ydCB7IEhpc3RvcnlMaXN0IH0gZnJvbSAiQC9hcGkvYWN0aXZpdGkvcHJvY2Vzc1Rhc2siOwovL+ivlemqjOiuvuWkh+WcsOeCueWtkOe7hOS7tgppbXBvcnQgc3lzYmRkIGZyb20gIkAvdmlld3MvZGFnYW5nT2lsZmllbGQvYnpnbC9zeWJ6ay9zeWJnamxjb21tZW50L3N5c2JkZCI7Ci8v6K+V6aqM5Y2V5L2N5LiL5ouJ5qGG5p+l6K+iCmltcG9ydCB7IGdldE9yZ2FuaXphdGlvblNlbGVjdGVkIH0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYXNzZXQvYmRzYnR6IjsKLy/or5Xpqozorr7lpIfpgInmi6kKaW1wb3J0IHN5c2JTZWxlY3RlZGJnIGZyb20gIkAvdmlld3MvZGFnYW5nT2lsZmllbGQvYnpnbC9zeWJ6ay9zeWJnamxjb21tZW50L3N5c2JTZWxlY3RlZGJnIjsKLy/or5XpqozmqKHmnb/pgInmi6kKaW1wb3J0IHN5bWJTeXhtU2VsZWN0IGZyb20gIkAvdmlld3MvZGFnYW5nT2lsZmllbGQvYnpnbC9zeWJ6ay9zeWJnamxjb21tZW50L3N5bWJTeXhtU2VsZWN0IjsKLy8g6K+V6aqM5pWw5o2u5a+55q+UCmltcG9ydCB6eG1JbmZvIGZyb20gIkAvdmlld3MvZGFnYW5nT2lsZmllbGQvYnpnbC9zeWJ6ay96eG1JbmZvIjsKLy/ojrflj5bnur/ot6/kuIvmi4nmlbDmja4KaW1wb3J0IHsgZ2V0U2RMaW5lU2VsZWN0ZWQgfSBmcm9tICJAL2FwaS9kYWdhbmdPaWxmaWVsZC9hc3NldC9zZHhsIjsKaW1wb3J0IHsgZ2V0UGRzU2VsZWN0ZWQgfSBmcm9tICJAL2FwaS9kYWdhbmdPaWxmaWVsZC9hc3NldC9wZHNnbCI7Ci8vcGRm5a+85Ye65bel5YW3CmltcG9ydCBodG1sVG9QZGYgZnJvbSAiQC91dGlscy9wcmludC9odG1sVG9QZGYiOwppbXBvcnQgdGFibGVQZGYgZnJvbSAiLi90YWJsZVBkZiI7CmltcG9ydCB7IGZvcm1hdHRlckRhdGVUaW1lIH0gZnJvbSAiQC91dGlscy9oYW5kbGVEYXRhIjsKaW1wb3J0IHsgZ2V0RGljdFR5cGVEYXRhIH0gZnJvbSAiQC9hcGkvc3lzdGVtL2RpY3QvZGF0YSI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAic3liZ2xyIiwKICBjb21wb25lbnRzOiB7CiAgICBzeXNiZGQsCiAgICBzeXNiU2VsZWN0ZWRiZywKICAgIHN5bWJTeXhtU2VsZWN0LAogICAgaHRtbFRvUGRmLAogICAgYWN0aXZpdGksCiAgICB0aW1lTGluZSwKICAgIHRhYmxlUGRmLAogICAgenhtSW5mbwogIH0sCiAgZGF0YSgpIHsKICAgIHZhciB2YWxpZGF0ZVNibWMgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7CiAgICAgIGlmICghdGhpcy5mb3JtLnNibWMpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoKSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgY2FsbGJhY2soKTsKICAgICAgfQogICAgfTsKICAgIHZhciB2YWxpZGF0ZVN5bWIgPSAocnVsZSwgdmFsdWUsIGNhbGxiYWNrKSA9PiB7CiAgICAgIGlmICghdGhpcy5mb3JtLnN5bWIpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoKSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgY2FsbGJhY2soKTsKICAgICAgfQogICAgfTsKICAgIHJldHVybiB7CiAgICAgIC8vIOivlemqjOaVsOaNruWvueavlOeUqAogICAgICBpc1Nob3dNcEluZm86IGZhbHNlLAogICAgICByb3dEYXRhOiB7fSwKICAgICAgbXhEYXRhOiBbXSwKICAgICAgLy8KICAgICAgc2hvd0J1dHRvbjogZmFsc2UsCiAgICAgIHN5cnlEaWFsb2c6IHRydWUsCiAgICAgIGJjRGlzYWJsZWQ6IGZhbHNlLAogICAgICB0YWJsZWJveDM6ICIiLAogICAgICB0YWJsZWJveDI6ICIiLAogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgY3VyclVzZXI6IHRoaXMuJHN0b3JlLmdldHRlcnMubmFtZSwKICAgICAgY3VyclVzZXJOYW1lOiB0aGlzLiRzdG9yZS5nZXR0ZXJzLm5pY2tOYW1lLAogICAgICBzc2R3bGQ6IHVuZGVmaW5lZCwgLy/miYDlsZ7ljZXkvY3pooblr7wKICAgICAgc2Nremc6IHVuZGVmaW5lZCwgLy/nlJ/kuqfnp5HkuJPlt6UKICAgICAgY3JlYXRlQnk6IHVuZGVmaW5lZCwgLy/mlrDlu7rkuroKICAgICAgY2xpY2tSb3c6IHt9LAogICAgICAvL+a1geeoi+WbvuafpeeciwogICAgICBvcGVuTG9hZGluZ0ltZzogZmFsc2UsCiAgICAgIGltZ1NyYzogIiIsIC8v5rWB56iL5Zu+5p+l55yL5Zyw5Z2ACiAgICAgIHRpbWVMaW5lU2hvdzogZmFsc2UsCiAgICAgIHRpbWVEYXRhOiBbXSwKICAgICAgLy/lvLnlh7rmoYbmoIfpopgKICAgICAgYWN0aXZpdGlPcHRpb246IHsgdGl0bGU6ICLkuIrmiqUiIH0sCiAgICAgIC8v5bel5L2c5rWB5by556qXCiAgICAgIGlzU2hvd0FjdGl2aXRpOiBmYWxzZSwKICAgICAgLy8g5aSa6YCJ5qGG6YCJ6YCJ5Lit55qE5pWw5o2uCiAgICAgIHNlbGVjdERhdGE6IFtdLAogICAgICAvL+W3peS9nOa1geS8oOWFpeWPguaVsAogICAgICBwcm9jZXNzRGF0YTogewogICAgICAgIHByb2Nlc3NEZWZpbml0aW9uS2V5OiAic3liZ3NoIiwKICAgICAgICBidXNpbmVzc0tleTogIiIsCiAgICAgICAgYnVzaW5lc3NUeXBlOiAi6K+V6aqM5oql5ZGK5a6h5qC4IiwKICAgICAgICB2YXJpYWJsZXM6IHt9LAogICAgICAgIGRlZmF1bHRGcm9tOiB0cnVlLAogICAgICAgIHByb2Nlc3NUeXBlOiAiY29tcGxldGUiCiAgICAgIH0sCiAgICAgIC8v5Z+65pys5L+h5oGv6KGo5qC85pWw5o2uCiAgICAgIGJhc2ljRGF0YTogewogICAgICAgIHN5ZHdtYzogIiIsIC8v6K+V6aqM5Y2V5L2NCiAgICAgICAgc3l4ejogIiIsIC8v6K+V6aqM5oCn6LSoCiAgICAgICAgc3lycTogIiIsIC8vICDor5Xpqozml6XmnJ8KICAgICAgICBzeXJ5aWQ6ICIiLCAvLyDor5XpqozkurrlkZgKICAgICAgICBienJpZDogIiIsIC8vIOe8luWGmeS6ugogICAgICAgIHNocmlkOiAiIiwgLy8g5a6h5qC45Lq6CiAgICAgICAgdXBkYXRlQnk6ICIiLCAvLyDmibnlh4bkuroKICAgICAgICB0cTogIiIsIC8vIOivlemqjOWkqeawlAogICAgICAgIHdkOiAiIiwgLy8g5rip5bqmCiAgICAgICAgc2Q6ICIiLCAvLyDmub/luqYKICAgICAgICBzeWRkOiAiIiwgLy/or5XpqozlnLDngrkKICAgICAgICB3dGR3OiAiIiwgLy/lp5TmiZjljZXkvY0KICAgICAgICB5eGJoOiAiIiwgLy/ov5DooYznvJblj7cKICAgICAgICBzY2NqOiAiIiwgLy/nlJ/kuqfljoLlrrYKICAgICAgICBjY3JxOiAiIiwgLy/lh7rljoLml6XmnJ/vvIwKICAgICAgICBjY2JoOiAiIiwgLy/lh7rljoLnvJblj7cKICAgICAgICBzYnhoOiAiIiwgLy/orr7lpIflnovlj7cKICAgICAgICBlZGR5OiAiIiwgLy/pop3lrprnlLXljosoa1YpCiAgICAgICAgZWRkbDogIiIsIC8v6aKd5a6a55S15rWBKEEpCiAgICAgICAgeHM6ICIiLCAvL+ebuOaVsAogICAgICAgIC8v55u45pWw5o6l57q/57uE5YirCiAgICAgICAgZWRybDogIiIgLC8v6aKd5a6a5a656YePKE1WQSkKICAgICAgICAvL+eUteWOi+e7hOWQiAogICAgICAgIC8v55S15rWB57uE5ZCICiAgICAgICAgLy/lrrnph4/nu4TlkIgKICAgICAgICB0eXJxOiIiCiAgICAgIH0sCiAgICAgIENvbnQ6IFsKICAgICAgICB7CiAgICAgICAgICBiZHo6ICLlj5jnlLXnq5kiLAogICAgICAgICAgbmFtZTogIiIsCiAgICAgICAgICBiZHoxOiAi5aeU5omY5Y2V5L2NIiwKICAgICAgICAgIG5hbWUxOiAiIiwKICAgICAgICAgIGJkejI6ICLor5XpqozljZXkvY0iLAogICAgICAgICAgbmFtZTI6ICIiLAogICAgICAgICAgYmR6MzogIui/kOihjOe8luWPtyIsCiAgICAgICAgICBuYW1lMzogIiIKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGJkejogIuivlemqjOaAp+i0qCIsCiAgICAgICAgICBuYW1lOiAiIiwKICAgICAgICAgIGJkejE6ICLor5Xpqozml6XmnJ8iLAogICAgICAgICAgbmFtZTE6ICIiLAogICAgICAgICAgYmR6MjogIuivlemqjOS6uuWRmCIsCiAgICAgICAgICBuYW1lMjogIiIsCiAgICAgICAgICBiZHozOiAi6K+V6aqM5Zyw54K5IiwKICAgICAgICAgIG5hbWUzOiAiIgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgYmR6OiAi5oql5ZGK5pel5pyfIiwKICAgICAgICAgIG5hbWU6ICIiLAogICAgICAgICAgYmR6MTogIue8luWGmeS6uiIsCiAgICAgICAgICBuYW1lMTogIiIsCiAgICAgICAgICBiZHoyOiAi5a6h5qC45Lq6IiwKICAgICAgICAgIG5hbWUyOiAiIiwKICAgICAgICAgIGJkejM6ICLmibnlh4bkuroiLAogICAgICAgICAgbmFtZTM6ICIiCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBiZHo6ICLor5XpqozlpKnmsJQiLAogICAgICAgICAgbmFtZTogIiIsCiAgICAgICAgICBiZHoxOiAi546v5aKD5rip5bqm77yI4oSD77yJIiwKICAgICAgICAgIG5hbWUxOiAiIiwKICAgICAgICAgIGJkejI6ICLnjq/looPnm7jlr7nmub/luqbvvIgl77yJIiwKICAgICAgICAgIG5hbWUyOiAiIiwKICAgICAgICAgIGJkejM6ICLmipXov5Dml6XmnJ8iLAogICAgICAgICAgbmFtZTM6ICIiCiAgICAgICAgfQogICAgICBdLAogICAgICBhbGxBbGlnbjogbnVsbCwKICAgICAgdGl0bGVOYW1lOiAiIiwgLy/loavlhpnmqKHmnb/moIfpopgKICAgICAgLy/kuIvovb3lvLnlh7rmoYbmjqfliLYKICAgICAgaXNTaG93RG93bkxvYWREaWFsb2c6IGZhbHNlLAoKICAgICAgaXNTaG93RG93bkxvYWREaWFsb2dBOiBmYWxzZSwKICAgICAgbWJJbmZvOiB7fSwKICAgICAgLy/kuLvorr7lpIfpgInmi6nkvKDpgJLlrZDnu4Tku7blj4LmlbAKICAgICAgc2VsZWN0ZWRTYlBhcmFtOiB7CiAgICAgICAgbHg6ICJiZCIsCiAgICAgICAgc2JtYzogIiIKICAgICAgfSwKICAgICAgLy/kuLvorr7lpIfpgInmi6nkvKDpgJLlrZDnu4Tku7blj4LmlbAKICAgICAgbWFpbkRhdGE6IHsKICAgICAgICBseDogIiIKICAgICAgfSwKICAgICAgLy/or5Xpqozorr7lpIfpgInmi6nml7bnu5nor5XpqozmqKHmnb/lrZDnu4Tku7bkvKDpgJLlj4LmlbAKICAgICAgc3ltYkRhdGE6IHsKICAgICAgICBzYmx4aWQ6ICIiCiAgICAgIH0sCgogICAgICAvL+iuvuWkh+S4k+S4mgogICAgICBzYnp5TGlzdDogWwogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAi6L6T55S16K6+5aSHIiwKICAgICAgICAgIHZhbHVlOiAi6L6T55S16K6+5aSHIgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICLlj5jnlLXorr7lpIciLAogICAgICAgICAgdmFsdWU6ICLlj5jnlLXorr7lpIciCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogIumFjeeUteiuvuWkhyIsCiAgICAgICAgICB2YWx1ZTogIumFjeeUteiuvuWkhyIKICAgICAgICB9CiAgICAgIF0sCiAgICAgIC8v6K+V6aqM5qih5p2/5by55Ye65qGG5o6n5Yi2CiAgICAgIGlzU2hvd1N5bWJEaWFsb2c6IGZhbHNlLAogICAgICAvL+ivlemqjOiuvuWkh+W8ueWHuuahhuaOp+WItgogICAgICBpc1Nob3dTeXNiRGlhbG9nOiBmYWxzZSwKICAgICAgLy/or5Xpqozorr7lpIflnLDngrnlvLnlh7rmoYbmjqfliLYKICAgICAgaXNTaG93U3lzYmRkRGlhbG9nOiBmYWxzZSwKICAgICAgLy/or5XpqozmiqXlkYrorrDlvZXmlrDlop7lvLnlh7rmoYbooajljZUKICAgICAgZm9ybTogewogICAgICAgIC8v5Zu65a6a5LiN5Y+v5riF56m6CiAgICAgICAgc2p6eTogIiIsCiAgICAgICAgLy/or5XpqozkuJPkuJrnvJbnoIEKICAgICAgICBzeXp5Ym06ICIiLAogICAgICAgIC8v6K+V6aqM5LiT5Lia5ZCN56ewCiAgICAgICAgc3l6eTogIiIsCiAgICAgICAgLy/or5XpqozmgKfotKjnvJbnoIEKICAgICAgICBzeXh6Ym06ICIiLAogICAgICAgIC8v6K+V6aqM5oCn6LSo5ZCN56ewCiAgICAgICAgc3l4ejogIiIsCiAgICAgICAgLy/orr7lpIflnLDngrnlkI3np7AKICAgICAgICBzeWRkOiAiIiwKICAgICAgICBzeWRkaWQ6ICIiLCAvL+ivlemqjOWcsOeCuWlkCiAgICAgICAgLy/or5Xpqozorr7lpIdpZAogICAgICAgIHN5c2JpZDogIiIsCiAgICAgICAgc2JtYzogIiIsIC8v6K6+5aSH5ZCN56ewCiAgICAgICAgc3lkdzogIiIsIC8v6K+V6aqM5Y2V5L2NaWQKICAgICAgICBzeXJxOiB1bmRlZmluZWQsIC8vIOivlemqjOaXpeacnwogICAgICAgIHN5bWM6ICIiLCAvL+ivlemqjOWQjeensAogICAgICAgIHdkOiAiIiwgLy/muKnluqYKICAgICAgICBzZDogIiIsIC8v5rm/5bqmCiAgICAgICAgeXc6ICIiLCAvL+ayuea4qQogICAgICAgIHRxOiAiIiwgLy/lpKnmsJQKICAgICAgICBzeW1iOiAiIiwgLy/or5XpqozmqKHmnb/lkI3np7AKICAgICAgICBzeW1iaWQ6ICIiLCAvL+ivlemqjOaooeadv2lkCiAgICAgICAgYnpyaWQ6ICIiLCAvL+e8luWItuS6uuWQjeensO+8jOWQjumdouaUueeUqOS4i+aLieahhgogICAgICAgIHNocmlkOiAiIiwgLy/lrqHmoLjkurrlkI3np7DvvIzlkI7pnaLmlLnkuLrkuIvmi4nmoYYKICAgICAgICBzeXJ5aWQ6ICIiLCAvL+ivlemqjOS6uuWRmOOAguWQjumdouaUueS4uuS4i+aLieahhgogICAgICAgIGJ6OiAiIiAvL+Wkh+azqAogICAgICB9LAogICAgICBhc3NldFR5cGVDb2RlOiAiIiwgLy/orr7lpIfnsbvlnovnvJbnoIEKICAgICAgLy/or6bmg4XlvLnmoYbmmK/lkKbmmL7npLoKICAgICAgaXNTaG93RGV0YWlsczogZmFsc2UsCiAgICAgIC8v5pi+56S65Y+W5raI56Gu6K6k5oyJ6ZKuCiAgICAgIGlzU2hvdzogdHJ1ZSwKICAgICAgLy/mmK/lkKbnpoHnlKgKICAgICAgaXNEaXNhYmxlZDogZmFsc2UsCiAgICAgIHp5aXNEaXNhYmxlZDogZmFsc2UsCiAgICAgIC8v6K+V6aqM5LiT5LiaCiAgICAgIHN5enlMaXN0OiBbXSwKICAgICAgLy/or5XpqozmgKfotKgKICAgICAgc3l4ekxpc3Q6IFtdLAogICAgICAvL+iuvuWkh+WcsOeCuQogICAgICBzYmRkTGlzdDogW10sCiAgICAgIC8v6K+V6aqM5Y2V5L2NCiAgICAgIHN5ZHdMaXN0OiBbXSwKICAgICAgLy/mtYHnqIvnirbmgIEKICAgICAgaXNoZzogIiIsCiAgICAgIHN5YmdqbGxkOiAiIiwKICAgICAgc2VsZWN0QTogW10sCiAgICAgIHNoZW5ncGRmOiAwLAogICAgICBpbnB1dHBkZjogMCwKICAgICAgZG93bmxvYWRwZGY6IDAsCiAgICAgIHN5eG1pZDogIiIsCiAgICAgIHN5c2JpZDogIiIsCiAgICAgIC8v6K+V6aqM5aSp5rCUCiAgICAgIHRxTGlzdDogWwogICAgICAgIHsgbGFiZWw6ICLmmbQiLCB2YWx1ZTogIuaZtCIgfSwKICAgICAgICB7IGxhYmVsOiAi6Zi0IiwgdmFsdWU6ICLpmLQiIH0sCiAgICAgICAgeyBsYWJlbDogIumbviIsIHZhbHVlOiAi6Zu+IiB9LAogICAgICAgIHsgbGFiZWw6ICLlsI/pm6giLCB2YWx1ZTogIuWwj+mbqCIgfSwKICAgICAgICB7IGxhYmVsOiAi5Lit6ZuoIiwgdmFsdWU6ICLkuK3pm6giIH0sCiAgICAgICAgeyBsYWJlbDogIuWkp+mbqCIsIHZhbHVlOiAi5aSn6ZuoIiB9LAogICAgICAgIHsgbGFiZWw6ICLpm7fpm6giLCB2YWx1ZTogIumbt+mbqCIgfSwKICAgICAgICB7IGxhYmVsOiAi5bCP6ZuqIiwgdmFsdWU6ICLlsI/pm6oiIH0sCiAgICAgICAgeyBsYWJlbDogIuS4rembqiIsIHZhbHVlOiAi5Lit6ZuqIiB9LAogICAgICAgIHsgbGFiZWw6ICLlpKfpm6oiLCB2YWx1ZTogIuWkp+mbqiIgfQogICAgICBdLAogICAgICAvL+ivlemqjOaKpeWRiuiusOW9leaWsOWinuW8ueeql+ahhuagh+mimAogICAgICB0aXRsZTogIiIsCiAgICAgIGZpbHRlckluZm86IHsKICAgICAgICBkYXRhOiB7CiAgICAgICAgICBzeW1jOiAiIiwKICAgICAgICAgIHNqenk6ICIiLAogICAgICAgICAgc3lkZGlkOiAiIiwKICAgICAgICAgIHN5bWI6ICIiLAogICAgICAgICAgc3lycUFycjogW10sCiAgICAgICAgICBzeXp5OiAiIiwKICAgICAgICAgIHN5eHo6ICIiLAogICAgICAgICAgc2JtYzogIiIsCiAgICAgICAgICBpc2hnOiAiIgogICAgICAgIH0sCiAgICAgICAgZmllbGRMaXN0OiBbCiAgICAgICAgICB7IGxhYmVsOiAi6K+V6aqM5ZCN56ewIiwgdHlwZTogImlucHV0IiwgdmFsdWU6ICJzeW1jIiB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIuiuvuWkh+S4k+S4miIsCiAgICAgICAgICAgIHR5cGU6ICJzZWxlY3QiLAogICAgICAgICAgICB2YWx1ZTogInNqenkiLAogICAgICAgICAgICBvcHRpb25zOiBbCiAgICAgICAgICAgICAgeyBsYWJlbDogIui+k+eUteiuvuWkhyIsIHZhbHVlOiAi6L6T55S16K6+5aSHIiB9LAogICAgICAgICAgICAgIHsgbGFiZWw6ICLlj5jnlLXorr7lpIciLCB2YWx1ZTogIuWPmOeUteiuvuWkhyIgfSwKICAgICAgICAgICAgICB7IGxhYmVsOiAi6YWN55S16K6+5aSHIiwgdmFsdWU6ICLphY3nlLXorr7lpIciIH0KICAgICAgICAgICAgXSwKICAgICAgICAgICAgY2xlYXJhYmxlOiBmYWxzZQogICAgICAgICAgfSwKICAgICAgICAgIHsgbGFiZWw6ICLorr7lpIflnLDngrkiLCB0eXBlOiAic2VsZWN0IiwgdmFsdWU6ICJzeWRkaWQiLCBvcHRpb25zOiBbXSB9LAogICAgICAgICAgeyBsYWJlbDogIuivlemqjOaooeadv+WQjeensCIsIHR5cGU6ICJpbnB1dCIsIHZhbHVlOiAic3ltYiIgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLor5Xpqozml6XmnJ8iLAogICAgICAgICAgICB0eXBlOiAiZGF0ZSIsCiAgICAgICAgICAgIHZhbHVlOiAic3lycUFyciIsCiAgICAgICAgICAgIGRhdGVUeXBlOiAiZGF0ZXJhbmdlIiwKICAgICAgICAgICAgZm9ybWF0OiAieXl5eS1NTS1kZCIKICAgICAgICAgIH0sCiAgICAgICAgICB7IGxhYmVsOiAi6K+V6aqM5LiT5LiaIiwgdHlwZTogInNlbGVjdCIsIHZhbHVlOiAic3l6eSIsIG9wdGlvbnM6IFtdIH0sCiAgICAgICAgICB7IGxhYmVsOiAi6K+V6aqM5oCn6LSoIiwgdHlwZTogImlucHV0IiwgdmFsdWU6ICJzeXh6IiB9LAogICAgICAgICAgeyBsYWJlbDogIuivlemqjOiuvuWkhyIsIHR5cGU6ICJpbnB1dCIsIHZhbHVlOiAic2JtYyIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLmtYHnqIvnirbmgIEiLCB0eXBlOiAic2VsZWN0IiwgdmFsdWU6ICJpc2hnIiwgb3B0aW9uczogW10gfQogICAgICAgIF0KICAgICAgfSwKICAgICAgLy/mn6Xor6LmiqXlkYrorrDlvZXlj4LmlbAKICAgICAgcXVlcnlQYXJhbTogewogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBwYWdlTnVtOiAxCiAgICAgICAgLy8gc3lkZGlkOiAiIgogICAgICB9LAogICAgICB0YWJsZUFuZFBhZ2VJbmZvOiB7CiAgICAgICAgcGFnZXI6IHsKICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgIHNpemVzOiBbMTAsIDIwLCA1MCwgMF0KICAgICAgICB9LAogICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgdGFibGVIZWFkZXI6IFsKICAgICAgICAgIHsgbGFiZWw6ICLor5XpqozkuJPkuJoiLCBwcm9wOiAic3l6eSIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLor5XpqozmgKfotKgiLCBwcm9wOiAic3l4eiIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLor5XpqozlkI3np7AiLCBwcm9wOiAic3ltYyIsIG1pbldpZHRoOiAiMTAwIiB9LAogICAgICAgICAgeyBsYWJlbDogIuiuvuWkh+WcsOeCuSIsIHByb3A6ICJzeWRkIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi6K+V6aqM6K6+5aSHIiwgcHJvcDogInNibWMiLCBtaW5XaWR0aDogIjIwMCIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLor5XpqozmqKHmnb/lkI3np7AiLCBwcm9wOiAic3ltYiIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBsYWJlbDogIuWkqeawlCIsIHByb3A6ICJ0cSIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLor5Xpqozml6XmnJ8iLCBwcm9wOiAic3lycSIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLor5XpqozkurrlkZgiLCBwcm9wOiAic3lyeWlkIiB9LAogICAgICAgICAgeyBsYWJlbDogIua1geeoi+eKtuaAgSIsIHByb3A6ICJ6dG1jIiB9CiAgICAgICAgICAvLyB7CiAgICAgICAgICAvLyAgIHByb3A6ICdvcGVyYXRpb24nLAogICAgICAgICAgLy8gICBsYWJlbDogJ+ivlemqjOaKpeWRiicsCiAgICAgICAgICAvLyAgIG1pbldpZHRoOiAnMTIwcHgnLAogICAgICAgICAgLy8gICBzdHlsZToge2Rpc3BsYXk6ICdibG9jayd9LAogICAgICAgICAgLy8gICAvL+aTjeS9nOWIl+WbuuWumuWGjeWPs+S+pwogICAgICAgICAgLy8gICBvcGVyYXRpb246IFsKICAgICAgICAgIC8vICAgICB7bmFtZTogJ+Whq+WGmeaKpeWRiicsIGNsaWNrRnVuOiB0aGlzLnNhdmVTeWJnSW5mb30sCiAgICAgICAgICAvLyAgIF0KICAgICAgICAgIC8vIH0sCiAgICAgICAgICAvLyB7CiAgICAgICAgICAvLyAgIHByb3A6ICdvcGVyYXRpb24nLAogICAgICAgICAgLy8gICBsYWJlbDogJ+aTjeS9nCcsCiAgICAgICAgICAvLyAgIG1pbldpZHRoOiAnMTIwcHgnLAogICAgICAgICAgLy8gICBzdHlsZToge2Rpc3BsYXk6ICdibG9jayd9LAogICAgICAgICAgLy8gICAvL+aTjeS9nOWIl+WbuuWumuWGjeWPs+S+pwogICAgICAgICAgLy8gICBmaXhlZDogJ3JpZ2h0JywKICAgICAgICAgIC8vICAgb3BlcmF0aW9uOiBbCiAgICAgICAgICAvLyAgICAge25hbWU6ICfkv67mlLknLCBjbGlja0Z1bjogdGhpcy51cGRhdGVEZXRhaWxzfSwKICAgICAgICAgIC8vICAgICB7bmFtZTogJ+ivpuaDhScsIGNsaWNrRnVuOiB0aGlzLmdldERldGFpbHNJbmZvfSwKICAgICAgICAgIC8vICAgXQogICAgICAgICAgLy8gfSwKICAgICAgICBdLAogICAgICAgIG9wdGlvbjogeyBjaGVja0JveDogdHJ1ZSwgc2VyaWFsTnVtYmVyOiB0cnVlIH0KICAgICAgfSwKICAgICAgLy/or5XpqozmiqXlkYrorrDlvZXpgInkuK0KICAgICAgc2VsZWN0Um93czogW10sCiAgICAgIHN0cjogIiIsCiAgICAgIHNhdmVEYXRhOiB7fSwgLy/pgInkuK3loavlhpnmiqXlkYrnmoTlgLwKICAgICAgcnVsZXM6IHsKICAgICAgICBzeXp5Ym06IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fovpPlhaXor5XpqozkuJPkuJoiLCB0cmlnZ2VyOiAic2VsZWN0IiB9CiAgICAgICAgXSwKICAgICAgICBzeXh6Ym06IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fovpPlhaXor5XpqozmgKfotKgiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgc3lkZDogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fovpPlhaXorr7lpIflnLDngrkiLCB0cmlnZ2VyOiAiYmx1ciIgfV0sCiAgICAgICAgLy8gc3l4emJtOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+i+k+WFpeiuvuWkh+WcsOeCuSIsIHRyaWdnZXI6ICJibHVyIiB9XSwKICAgICAgICBzeW1iOiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgICBtZXNzYWdlOiAi6K+36YCJ5oup6K+V6aqM5qih5p2/IiwKICAgICAgICAgICAgdmFsaWRhdG9yOiB2YWxpZGF0ZVN5bWIsCiAgICAgICAgICAgIHRyaWdnZXI6ICJjaGFuZ2UiCiAgICAgICAgICB9CiAgICAgICAgXSwKICAgICAgICBzanp5OiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqeiuvuWkh+S4k+S4miIsIHRyaWdnZXI6ICJibHVyIiB9XSwKICAgICAgICBzYm1jOiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgICBtZXNzYWdlOiAi6K+36YCJ5oup6K+V6aqM6K6+5aSHIiwKICAgICAgICAgICAgdmFsaWRhdG9yOiB2YWxpZGF0ZVNibWMsCiAgICAgICAgICAgIHRyaWdnZXI6ICJjaGFuZ2UiCiAgICAgICAgICB9CiAgICAgICAgXSwKICAgICAgICBzeW1jOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+i+k+WFpeivlemqjOWQjeensCIsIHRyaWdnZXI6ICJibHVyIiB9XQogICAgICB9LAogICAgICBqbERhdGE6ICIiLCAvL+e7k+iuuuaJgOWcqOihjOagh+ivhgogICAgICBpc0psRGlzYWJsZWQ6IGZhbHNlIC8v57uT6K665piv5ZCm5LiN5Y+v57yW6L6RCiAgICB9OwogIH0sCgogIG1vdW50ZWQoKSB7CiAgICAvL+iOt+WPluivlemqjOS4k+S4muS4i+aLieahhuaVsOaNrgogICAgdGhpcy5nZXRPcHRpb25zKCk7CiAgICAvL+iOt+WPluivlemqjOWNleS9jeS4i+aLieahhgogICAgdGhpcy5nZXRPcmdhbml6YXRpb25TZWxlY3RlZCgpOwogICAgLy/ojrflj5bmlbDmja7liJfooagKICAgIHRoaXMuZ2V0RGF0YSh0aGlzLiRyb3V0ZS5xdWVyeSk7CiAgICAvL+afpeivouW9k+WJjeeZu+W9leS6uuaYr+WQpuacieivlemqjOaKpeWRiuWhq+WGmeadg+mZkAogICAgbGV0IHBhcmFtcyA9IHsKICAgICAgZ3JvdXBJZDogNzQsCiAgICAgIHVzZXJOYW1lOiB0aGlzLiRzdG9yZS5nZXR0ZXJzLm5hbWUKICAgIH07CiAgICBzaG93QnV0dGVuSlMocGFyYW1zKS50aGVuKHJlcyA9PiB7CiAgICAgIGlmIChyZXMuZGF0YS5sZW5ndGggPiAwKSB7CiAgICAgICAgdGhpcy5zaG93QnV0dG9uID0gdHJ1ZTsKICAgICAgfQogICAgfSk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvL+i/m+W6puadoeeKtuaAgeabtOaWsAogICAgcHJvZ3Jlc3Nmb3JtYXRlKHByb2NlbnQpIHsKICAgICAgcmV0dXJuIHRoaXMuaW5wdXRwZGYgPT09IDEwMCA/ICLlr7zlh7rlrozmiJAiIDogYCR7cHJvY2VudH0gJWA7CiAgICB9LAogICAgLy/ojrflj5bor5XpqozlrZDpobnnm67kv6Hmga8KICAgIGFzeW5jIGdldFp4bW1wSW5mbyhyb3cpIHsKICAgICAgLy8g6K+V6aqM5oql5ZGK5Lit6I635Y+W6K+V6aqM6aG555uu5qih5p2/55qEc3ltYmlkCiAgICAgIGxldCBzeW1iaWQgPSByb3cuc3ltYmlkOwogICAgICAvLyDor5Xpqozpobnnm67mqKHmnb/kuK3ojrflj5blrZDpobnnm67mqKHmnb/mlbDmja4KICAgICAgLy9yb3dEYXRhCiAgICAgIGxldCB7IGRhdGE6IHp4bU1iRGF0YSB9ID0gYXdhaXQgZ2V0TW91bGRWYWx1ZSh7IHN5bWJpZDogcm93LnN5bWJpZCB9KTsKICAgICAgdGhpcy5yb3dEYXRhID0genhtTWJEYXRhW09iamVjdC5rZXlzKHp4bU1iRGF0YSlbMV1dWzBdOwogICAgICAvL214RGF0YQogICAgICBnZXRDaGlsZHNWYWx1ZShyb3cpLnRoZW4ocmVzdWx0ID0+IHsKICAgICAgICBsZXQgc3lzal9kYXRhID0gcmVzdWx0LmRhdGEuc3l4bTsKICAgICAgICB0aGlzLm14RGF0YSA9IHN5c2pfZGF0YVtPYmplY3Qua2V5cyhzeXNqX2RhdGEpWzBdXTsKICAgICAgICB0aGlzLmlzU2hvd01wSW5mbyA9IHRydWU7CiAgICAgIH0pOwogICAgfSwKICAgIC8v5YWz6Zet6K+V6aqM5pWw5o2u5a+55q+U5by55qGGCiAgICBjbG9zZUluZm9EaWFsb2coKSB7CiAgICAgIHRoaXMuaXNTaG93TXBJbmZvID0gZmFsc2U7CiAgICB9LAogICAgLy/mkJzntKLmoYZzYmRkaWQg6YCJ5oup5LqL5Lu2CiAgICBzYmRkaWRDbGljayh2YWwsIHZhbDEpIHsKICAgICAgaWYgKHZhbDEgPT09ICJzeWRkbWMiKSB7CiAgICAgICAgdGhpcy5tYWluRGF0YS5seCA9ICIiOwogICAgICAgIHRoaXMuaXNTaG93U3lzYmRkRGlhbG9nID0gdHJ1ZTsKICAgICAgfQogICAgfSwKCiAgICAvL+ivpuaDheaMiemSrgogICAgZ2V0RGV0YWlsc0luZm8ocm93KSB7CiAgICAgIHRoaXMudGl0bGUgPSAi6K+V6aqM5oql5ZGK6K+m5oOFIjsKICAgICAgLy/kuI3mmL7npLrlj5bmtojnoa7orqTmjInpkq4KICAgICAgdGhpcy5pc1Nob3cgPSBmYWxzZTsKICAgICAgLy/npoHnlKjooajljZUKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gdHJ1ZTsKICAgICAgdGhpcy56eWlzRGlzYWJsZWQgPSB0cnVlOwogICAgICAvL+aJk+W8gOW8ueeqlwogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlOwogICAgICB0aGlzLmZvcm0gPSB7IC4uLnJvdyB9OwogICAgICAvL+iwg+eUqOiOt+WPluivlemqjOaAp+i0qOaWueazlea4suafk+ivlemqjOaAp+i0qAogICAgICB0aGlzLnNlbGVjdFN5eHpEYXRhQnlTeXp5Ym0xKCk7CiAgICB9LAogICAgLy/kv67mlLnmjInpkq4KICAgIHVwZGF0ZURldGFpbHMocm93KSB7CiAgICAgIHRoaXMudGl0bGUgPSAi6K+V6aqM5oql5ZGK5L+u5pS5IjsKICAgICAgLy/mmL7npLrlj5bmtojnoa7orqTmjInpkq4KICAgICAgdGhpcy5pc1Nob3cgPSB0cnVlOwogICAgICB0aGlzLnp5aXNEaXNhYmxlZCA9IHRydWU7CiAgICAgIC8v56aB55So6KGo5Y2VCiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICAvL+aJk+W8gOW8ueeqlwogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlOwogICAgICB0aGlzLmZvcm0gPSB7IC4uLnJvdyB9OwogICAgICAvL+iwg+eUqOiOt+WPluivlemqjOaAp+i0qOaWueazlea4suafk+ivlemqjOaAp+i0qAogICAgICB0aGlzLnNlbGVjdFN5eHpEYXRhQnlTeXp5Ym0xKCk7CiAgICB9LAogICAgLy/loavlhpnmiqXlkYoKICAgIGFzeW5jIHNhdmVTeWJnSW5mbyhyb3cpIHsKICAgICAgdGhpcy5mb3JtID0geyAuLi5yb3cgfTsKICAgICAgdGhpcy5jbGlja1JvdyA9IHJvdzsKICAgICAgKHRoaXMuYmNEaXNhYmxlZCA9IHRydWUpLCAodGhpcy5pc2hnID0gcm93LmlzaGcpOwogICAgICB0aGlzLmNyZWF0ZUJ5ID0gcm93LmNyZWF0ZUJ5OwogICAgICB0aGlzLnNzZHdsZCA9IHJvdy5zc2R3bGQ7CiAgICAgIHRoaXMuc2NremcgPSByb3cuc2Nremc7CiAgICAgIHRoaXMuc2F2ZURhdGEgPSByb3c7CiAgICAgIHRoaXMuYmFzaWNEYXRhID0gcm93OwogICAgICB0aGlzLnN5YmdqbGxkID0gcm93Lm9iaklkOwogICAgICB0aGlzLnN5bWJpZCA9IHJvdy5zeW1iaWQ7CiAgICAgIHRoaXMuc3lzYmlkID0gcm93LnN5c2JpZDsKICAgICAgdGhpcy5pc1Nob3dEb3duTG9hZERpYWxvZyA9IHRydWU7CiAgICAgIHRoaXMudGl0bGVOYW1lID0gcm93LnN5bWI7CiAgICAgIC8v5Zyo5aGr5YaZ5oql5ZGK5pe277yM6K+35rGC5ZCO5Y+w5aSE55CG5pWw5o2uCiAgICAgIGNvbnN0IHRoYXQgPSB0aGlzOwogICAgICBhd2FpdCBnZXRNb3VsZFZhbHVlKHsgc3ltYmlkOiByb3cuc3ltYmlkIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICByZXMgPSByZXMuZGF0YTsKICAgICAgICBnZXRDaGlsZHNWYWx1ZShyb3cpLnRoZW4ocmVzdWx0ID0+IHsKICAgICAgICAgIGxldCBzYm1wX2RhdGEgPSByZXN1bHQuZGF0YS5zYm1wOwogICAgICAgICAgbGV0IHN5c2pfZGF0YSA9IHJlc3VsdC5kYXRhLnN5eG07CiAgICAgICAgICBsZXQgYXJyID0gW107CiAgICAgICAgICBmb3IgKGxldCBpdGVtIGluIHNibXBfZGF0YSkgewogICAgICAgICAgICBhcnIucHVzaCh7IFtpdGVtXTogc2JtcF9kYXRhW2l0ZW1dIH0pOwogICAgICAgICAgfQogICAgICAgICAgbGV0IGFycjEgPSBbXTsKICAgICAgICAgIGZvciAobGV0IHZhbCBpbiBzeXNqX2RhdGEpIHsKICAgICAgICAgICAgYXJyMS5wdXNoKHsgW3ZhbF06IHN5c2pfZGF0YVt2YWxdIH0pOwogICAgICAgICAgfQogICAgICAgICAgdGhhdC5oYW5kbGVTYm1wKHJlc1tPYmplY3Qua2V5cyhyZXMpWzBdXSwgYXJyLCAiIiwgImgyX3RhYmxlIik7CiAgICAgICAgICB0aGF0LmhhbmRsZVNibXAocmVzW09iamVjdC5rZXlzKHJlcylbMV1dLCBhcnIxLCAiIiwgImgzX3RhYmxlIik7CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgICB0aGlzLmZvcm0gPSB7IC4uLnJvdyB9OwogICAgfSwKCiAgICAvL+afpeeci+ivlemqjOaKpeWRigogICAgU3liZ0luZm8ocm93KSB7CiAgICAgIHRoaXMuZm9ybSA9IHsgLi4ucm93IH07CiAgICAgIHRoaXMuY2xpY2tSb3cgPSByb3c7CiAgICAgIHRoaXMuaXNoZyA9IHJvdy5pc2hnOwogICAgICB0aGlzLmJ6enNwID0gcm93LmJ6enNwOwogICAgICB0aGlzLmJjRGlzYWJsZWQgPSB0cnVlOwogICAgICB0aGlzLmNyZWF0ZUJ5ID0gcm93LmNyZWF0ZUJ5OwogICAgICB0aGlzLnNzZHdsZCA9IHJvdy5zc2R3bGQ7CiAgICAgIHRoaXMuc2NremcgPSByb3cuc2Nremc7CiAgICAgIHRoaXMuc2F2ZURhdGEgPSByb3c7CiAgICAgIHRoaXMuYmFzaWNEYXRhID0gcm93OwogICAgICB0aGlzLnN5YmdqbGxkID0gcm93Lm9iaklkOwogICAgICB0aGlzLnN5bWJpZCA9IHJvdy5zeW1iaWQ7CiAgICAgIHRoaXMuc3lzYmlkID0gcm93LnN5c2JpZDsKICAgICAgdGhpcy5pc1Nob3dEb3duTG9hZERpYWxvZyA9IHRydWU7CiAgICAgIHRoaXMudGl0bGVOYW1lID0gcm93LnN5bWI7CiAgICAgIGxldCBnemltZzEgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgiZ3ppbWcxIik7CiAgICAgIGd6aW1nMSAmJiBnemltZzEucmVtb3ZlKCk7CiAgICAgIGxldCBnemltZzIgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgiZ3ppbWcyIik7CiAgICAgIGd6aW1nMiAmJiBnemltZzIucmVtb3ZlKCk7CiAgICAgIC8v5Zyo5aGr5YaZ5oql5ZGK5pe277yM6K+35rGC5ZCO5Y+w5aSE55CG5pWw5o2uCiAgICAgIGNvbnN0IHRoYXQgPSB0aGlzOwogICAgICBnZXRNb3VsZFZhbHVlKHsgc3ltYmlkOiByb3cuc3ltYmlkIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICByZXMgPSByZXMuZGF0YTsKICAgICAgICBnZXRDaGlsZHNWYWx1ZShyb3cpLnRoZW4ocmVzdWx0ID0+IHsKICAgICAgICAgIGxldCBzYm1wX2RhdGEgPSByZXN1bHQuZGF0YS5zYm1wOwogICAgICAgICAgbGV0IHN5c2pfZGF0YSA9IHJlc3VsdC5kYXRhLnN5eG07CiAgICAgICAgICBsZXQgYXJyID0gW107CiAgICAgICAgICBmb3IgKGxldCBpdGVtIGluIHNibXBfZGF0YSkgewogICAgICAgICAgICBhcnIucHVzaCh7IFtpdGVtXTogc2JtcF9kYXRhW2l0ZW1dIH0pOwogICAgICAgICAgfQogICAgICAgICAgbGV0IGFycjEgPSBbXTsKICAgICAgICAgIGZvciAobGV0IHZhbCBpbiBzeXNqX2RhdGEpIHsKICAgICAgICAgICAgYXJyMS5wdXNoKHsgW3ZhbF06IHN5c2pfZGF0YVt2YWxdIH0pOwogICAgICAgICAgfQogICAgICAgICAgdGhhdC5oYW5kbGVTYm1wKHJlc1tPYmplY3Qua2V5cyhyZXMpWzBdXSwgYXJyLCAiIiwgImgyX3RhYmxlIik7CiAgICAgICAgICB0aGF0LmhhbmRsZVNibXAocmVzW09iamVjdC5rZXlzKHJlcylbMV1dLCBhcnIxLCAiIiwgImgzX3RhYmxlIik7CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgICBzZXRUaW1lb3V0KGFzeW5jICgpID0+IHsKICAgICAgICB0aGlzLnNob3dUZXN0KCk7CiAgICAgIH0sIDMwMDApOwogICAgfSwKCiAgICBoYW5kbGVTYm1wKGRhdGFOdW0sIGRhdGFBcnIsIHN0ciwgdGFibGVCb3gsIHJldHVyblN0cmluZykgewogICAgICBmb3IgKGxldCBrID0gMDsgayA8IGRhdGFOdW0ubGVuZ3RoOyBrKyspIHsKICAgICAgICB2YXIgaHMgPSBkYXRhTnVtW2tdLmFIczsKICAgICAgICB2YXIgbHMgPSBkYXRhTnVtW2tdLmFMczsKICAgICAgICBsZXQgZGF0YSA9IGRhdGFBcnJba107CiAgICAgICAgZm9yICh2YXIgaXRlbSBpbiBkYXRhKSB7CiAgICAgICAgICAvLyBzdHIgKz0gIjx0ciBzdHlsZT0ndGV4dC1hbGlnbjpsZWZ0Oyc+PHRoIGNvbHNwYW49IisgbHMgKyI+IisgaXRlbSArIjwvdGg+PC90cj4iOwogICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBoczsgaSsrKSB7CiAgICAgICAgICAgIC8v5pyJ5Yeg6KGM5bCx5o+S5YWl5Yeg6KGMCiAgICAgICAgICAgIGxldCB0ZW1wID0gIjx0ciBjbGFzcz0nc3BsaXRDbGFzcyc+IjsKICAgICAgICAgICAgZm9yIChsZXQgaiA9IDA7IGogPCBkYXRhW2l0ZW1dLmxlbmd0aDsgaisrKSB7CiAgICAgICAgICAgICAgLy/lvqrnjq/mlbDmja7nnIvmr4/ooYzmnInlh6DliJcKICAgICAgICAgICAgICBpZiAoaSA9PSBkYXRhW2l0ZW1dW2pdLnJvd2luZGV4KSB7CiAgICAgICAgICAgICAgICB2YXIgbnJicyA9IGRhdGFbaXRlbV1bal0ubnJiczsKICAgICAgICAgICAgICAgIHZhciBzamx4ID0gZGF0YVtpdGVtXVtqXS5zamx4OyAvL+aVsOaNruexu+WeiwogICAgICAgICAgICAgICAgdmFyIG9iaklkID0gZGF0YVtpdGVtXVtqXS5vYmpJZDsKICAgICAgICAgICAgICAgIHZhciB0eHQgPSBkYXRhW2l0ZW1dW2pdLnRleHQ7CiAgICAgICAgICAgICAgICB2YXIgbnIgPSAiIjsKICAgICAgICAgICAgICAgIGlmIChucmJzID09PSAi57uT6K66IikgewogICAgICAgICAgICAgICAgICB0aGlzLmpsRGF0YSA9IGRhdGFbaXRlbV1baiArIDFdOyAvL+S/neWtmOS4i+S4gOS4quWNleWFg+agvOWGheWuuQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgaWYgKHNqbHggPT0gIlNUUklORyIpIHsKICAgICAgICAgICAgICAgICAgLy/liKTmlq3mlbDmja7nsbvlnovkuLpzdHJpbmfnmoTooajnpLrkuLrnqbrmoLzvvIzlj6/nvJbovpEKICAgICAgICAgICAgICAgICAgaWYgKCF0eHQpIHsKICAgICAgICAgICAgICAgICAgICB0eHQgPSAiIjsKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICBuciA9CiAgICAgICAgICAgICAgICAgICAgIjxpbnB1dCB0eXBlPSd0ZXh0JyBzdHlsZT0nYm9yZGVyOiBub25lO3dpZHRoOiA5OSU7dGV4dC1hbGlnbjogY2VudGVyOycgY2xhc3M9IiArCiAgICAgICAgICAgICAgICAgICAgdGFibGVCb3ggKwogICAgICAgICAgICAgICAgICAgICIgaWQ9IiArCiAgICAgICAgICAgICAgICAgICAgb2JqSWQgKwogICAgICAgICAgICAgICAgICAgICIgdmFsdWU9IiArCiAgICAgICAgICAgICAgICAgICAgdHh0ICsKICAgICAgICAgICAgICAgICAgICAiPiI7CiAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICBuciA9IG5yYnM7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBpZiAoZGF0YVtpdGVtXVtqXS5jb2xzcGFuICE9ICIxIikgewogICAgICAgICAgICAgICAgICAvL+WIpOaWrWNvbHNwYW7kuI3kuLox55qE6K+d5Li65Y+v57yW6L6R55qECiAgICAgICAgICAgICAgICAgIGlmICh0aGlzLmpsRGF0YSAmJiB0aGlzLmpsRGF0YS5vYmpJZCA9PT0gb2JqSWQpIHsKICAgICAgICAgICAgICAgICAgICAvL+W9k+WJjeWNleWFg+agvOaYr+e7k+iuuuaJgOWcqOWNleWFg+agvAogICAgICAgICAgICAgICAgICAgIGlmICh0aGlzLmZvcm0uaXNoZyA9PSA1IHx8IHRoaXMuZm9ybS5pc2hnID09IDYpIHsKICAgICAgICAgICAgICAgICAgICAgIC8v5Y+q5pyJ54+t57uE6ZW/5a6h5om55ZKM5Y6G5Y+y5b2V5YWl5piv5Y+v57yW6L6R55qECiAgICAgICAgICAgICAgICAgICAgICB0ZW1wICs9CiAgICAgICAgICAgICAgICAgICAgICAgICI8dGQgdGFiaW5kZXg9Jy0xJyBjb2xzcGFuPSciICsKICAgICAgICAgICAgICAgICAgICAgICAgZGF0YVtpdGVtXVtqXS5jb2xzcGFuICsKICAgICAgICAgICAgICAgICAgICAgICAgIicgcm93c3Bhbj0nIiArCiAgICAgICAgICAgICAgICAgICAgICAgIGRhdGFbaXRlbV1bal0ucm93c3BhbiArCiAgICAgICAgICAgICAgICAgICAgICAgICInID4iICsKICAgICAgICAgICAgICAgICAgICAgICAgbnIgKwogICAgICAgICAgICAgICAgICAgICAgICAiPC90ZD4iOwogICAgICAgICAgICAgICAgICAgICAgdGhpcy5pc0psRGlzYWJsZWQgPSBmYWxzZTsKICAgICAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICAgICAgLy/mmL7npLrljp/lgLzvvIzkuI3lj6/nvJbovpEKICAgICAgICAgICAgICAgICAgICAgIHRlbXAgKz0KICAgICAgICAgICAgICAgICAgICAgICAgIjx0ZCB0YWJpbmRleD0nLTEnIGNvbHNwYW49JyIgKwogICAgICAgICAgICAgICAgICAgICAgICBkYXRhW2l0ZW1dW2pdLmNvbHNwYW4gKwogICAgICAgICAgICAgICAgICAgICAgICAiJyByb3dzcGFuPSciICsKICAgICAgICAgICAgICAgICAgICAgICAgZGF0YVtpdGVtXVtqXS5yb3dzcGFuICsKICAgICAgICAgICAgICAgICAgICAgICAgIicgaWQ9JyIgKwogICAgICAgICAgICAgICAgICAgICAgICBvYmpJZCArCiAgICAgICAgICAgICAgICAgICAgICAgICInIGNsYXNzPSdfb2JqSWQnIiArCiAgICAgICAgICAgICAgICAgICAgICAgICIgPiIgKwogICAgICAgICAgICAgICAgICAgICAgICB0eHQgKwogICAgICAgICAgICAgICAgICAgICAgICAiPC90ZD4iOwogICAgICAgICAgICAgICAgICAgICAgdGhpcy5pc0psRGlzYWJsZWQgPSB0cnVlOwogICAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgICB0ZW1wICs9CiAgICAgICAgICAgICAgICAgICAgICAiPHRkIHRhYmluZGV4PSctMScgY29sc3Bhbj0nIiArCiAgICAgICAgICAgICAgICAgICAgICBkYXRhW2l0ZW1dW2pdLmNvbHNwYW4gKwogICAgICAgICAgICAgICAgICAgICAgIicgcm93c3Bhbj0nIiArCiAgICAgICAgICAgICAgICAgICAgICBkYXRhW2l0ZW1dW2pdLnJvd3NwYW4gKwogICAgICAgICAgICAgICAgICAgICAgIicgPiIgKwogICAgICAgICAgICAgICAgICAgICAgbnIgKwogICAgICAgICAgICAgICAgICAgICAgIjwvdGQ+IjsKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgICAgdGVtcCArPSAiPHRkIHRhYmluZGV4PSctMSc+IiArIG5yICsgIjwvdGQ+IjsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgICAgdGVtcCArPSAiPC90cj4iOwogICAgICAgICAgICBzdHIgKz0gdGVtcDsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KICAgICAgaWYgKHJldHVyblN0cmluZykgewogICAgICAgIHJldHVybiBzdHI7CiAgICAgIH0KICAgICAgaWYgKHRhYmxlQm94ID09PSAiaDJfdGFibGUiKSB7CiAgICAgICAgdGhpcy50YWJsZWJveDIgPSBzdHI7CiAgICAgIH0gZWxzZSBpZiAodGFibGVCb3ggPT09ICJoM190YWJsZSIpIHsKICAgICAgICB0aGlzLnRhYmxlYm94MyA9IHN0cjsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzW3RhYmxlQm94XSA9IHN0cjsKICAgICAgfQogICAgfSwKICAgIC8v5YWz6Zet5aGr5YaZ5oql5ZGK5by55qGGCiAgICBjbG9zZVlsRGlhbG9nKCkgewogICAgICB0aGlzLmlzU2hvd0Rvd25Mb2FkRGlhbG9nID0gZmFsc2U7CiAgICB9LAoKICAgIGRjcGRmKCkgewogICAgICBsZXQgdGhhdCA9IHRoaXM7CiAgICAgIGlmICghdGhhdC5zZWxlY3RSb3dzLmxlbmd0aCA+IDApIHsKICAgICAgICB0aGF0LiRtZXNzYWdlLndhcm5pbmcoIuivt+WFiOmAieS4remcgOimgeaJuemHj+WvvOWHuueahOaVsOaNriIpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB0aGF0LmlucHV0cGRmID0gMDsKICAgICAgbGV0IHNlbGVjdFJvd3MgPSB0aGF0LnNlbGVjdFJvd3M7CiAgICAgIGNvbnNvbGUubG9nKHNlbGVjdFJvd3MsICJzZWxlY3QiKTsKICAgICAgdGhhdC5zZWxlY3RBID0gc2VsZWN0Um93cy5tYXAoKGUsIGkpID0+IHsKICAgICAgICBnZXRNb3VsZFZhbHVlKHsgc3ltYmlkOiBlLnN5bWJpZCB9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgICByZXMgPSByZXMuZGF0YTsKICAgICAgICAgIGdldENoaWxkc1ZhbHVlKGUpLnRoZW4ocmVzdWx0ID0+IHsKICAgICAgICAgICAgbGV0IHNibXBfZGF0YSA9IHJlc3VsdC5kYXRhLnNibXA7CiAgICAgICAgICAgIGxldCBzeXNqX2RhdGEgPSByZXN1bHQuZGF0YS5zeXhtOwogICAgICAgICAgICBsZXQgYXJyID0gW107CiAgICAgICAgICAgIGZvciAobGV0IGl0ZW0gaW4gc2JtcF9kYXRhKSB7CiAgICAgICAgICAgICAgYXJyLnB1c2goeyBbaXRlbV06IHNibXBfZGF0YVtpdGVtXSB9KTsKICAgICAgICAgICAgfQogICAgICAgICAgICBsZXQgYXJyMSA9IFtdOwogICAgICAgICAgICBmb3IgKGxldCB2YWwgaW4gc3lzal9kYXRhKSB7CiAgICAgICAgICAgICAgYXJyMS5wdXNoKHsgW3ZhbF06IHN5c2pfZGF0YVt2YWxdIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICAgIGUudGFibGVib3gyID0gdGhhdC5oYW5kbGVTYm1wKAogICAgICAgICAgICAgIHJlc1tPYmplY3Qua2V5cyhyZXMpWzBdXSwKICAgICAgICAgICAgICBhcnIsCiAgICAgICAgICAgICAgIiIsCiAgICAgICAgICAgICAgIiIsCiAgICAgICAgICAgICAgdHJ1ZQogICAgICAgICAgICApOwogICAgICAgICAgICBlLnRhYmxlYm94MyA9IHRoYXQuaGFuZGxlU2JtcCgKICAgICAgICAgICAgICByZXNbT2JqZWN0LmtleXMocmVzKVsxXV0sCiAgICAgICAgICAgICAgYXJyMSwKICAgICAgICAgICAgICAiIiwKICAgICAgICAgICAgICAiIiwKICAgICAgICAgICAgICB0cnVlCiAgICAgICAgICAgICk7CiAgICAgICAgICAgIGUuc3lzaiA9IHN5c2pfZGF0YTsKICAgICAgICAgIH0pOwogICAgICAgIH0pOwogICAgICAgIC8vIHRoYXQuaW5wdXRwZGYgPSAoKGkgKyAxIC8gc2VsZWN0Um93cy5sZW5ndGgpICogMTAwKS50b0ZpeGVkKDIpCiAgICAgICAgLy8gY29uc29sZS5sb2codGhhdC5pbnB1dHBkZiwgJ2lucHV0cGRmMS4uLicpOwogICAgICAgIHJldHVybiBlOwogICAgICB9KTsKICAgICAgY29uc29sZS5sb2codGhhdC5zZWxlY3RBLCAic2VsZWN0Iik7CiAgICAgIHRoYXQuaXNTaG93RG93bkxvYWREaWFsb2dBID0gdHJ1ZTsKICAgICAgdGhhdC5pbnB1dHBkZiA9IDMwOwogICAgICAvLyBjb25zb2xlLmxvZyh0aGF0LmlucHV0cGRmLCAnaW5wdXRwZGYyLi4uJyk7CiAgICAgIGxldCBsZW5ndGggPSB0aGF0LnNlbGVjdEEubGVuZ3RoOwogICAgICBzZXRUaW1lb3V0KGFzeW5jICgpID0+IHsKICAgICAgICB0aGF0LiRyZWZzW2B0YWJsZVBkZiR7bGVuZ3RoIC0gMX1gXSAmJgogICAgICAgICAgdGhhdC4kcmVmc1tgdGFibGVQZGYke2xlbmd0aCAtIDF9YF1bMF0uJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgICAgc2V0VGltZW91dChhc3luYyAoKSA9PiB7CiAgICAgICAgICAgICAgbGV0IHBkZmFsbCA9IGF3YWl0IFByb21pc2UuYWxsKAogICAgICAgICAgICAgICAgdGhhdC5zZWxlY3RBLm1hcChhc3luYyAoZSwgaSkgPT4gewogICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhlLCAiaXRlbS4uIik7CiAgICAgICAgICAgICAgICAgIGxldCBuZXdWYXIgPSBhd2FpdCB0aGF0LmV4cG9ydFBkZigKICAgICAgICAgICAgICAgICAgICBgdGFibGVQZGYke2l9YCwKICAgICAgICAgICAgICAgICAgICB0cnVlLAogICAgICAgICAgICAgICAgICAgIGAke2Uuc3ltYn1fJHtpfWAsCiAgICAgICAgICAgICAgICAgICAgdHJ1ZSwKICAgICAgICAgICAgICAgICAgICBlLnN5c2oKICAgICAgICAgICAgICAgICAgKTsKICAgICAgICAgICAgICAgICAgdGhhdC5zaGVuZ3BkZiA9ICgoaSArIDEgLyBsZW5ndGgpICogMTAwKS50b0ZpeGVkKDIpOwogICAgICAgICAgICAgICAgICByZXR1cm4gbmV3VmFyOwogICAgICAgICAgICAgICAgfSkKICAgICAgICAgICAgICApOwogICAgICAgICAgICAgIGh0bWxUb1BkZi56aXBDaGFuZ2UoCiAgICAgICAgICAgICAgICBwZGZhbGwsCiAgICAgICAgICAgICAgICAi6K+V6aqM5oql5ZGKXyIgKyBuZXcgRGF0ZSgpLmdldFRpbWUoKSwKICAgICAgICAgICAgICAgIChpdGVtLCBpKSA9PiB7CiAgICAgICAgICAgICAgICAgIHRoYXQuZG93bmxvYWRwZGYgPSAoKGkgKyAxIC8gbGVuZ3RoKSAqIDEwMCkudG9GaXhlZCgyKTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICApOwogICAgICAgICAgICAgIGNvbnNvbGUubG9nKCJwZGYiLCAiISEiKTsKICAgICAgICAgICAgICB0aGF0LmlucHV0cGRmID0gMTAwOwogICAgICAgICAgICB9LCAzMDAwKTsKICAgICAgICAgIH0pOwogICAgICAgIHRoYXQuaW5wdXRwZGYgPSA2MDsKICAgICAgICAvLyBjb25zb2xlLmxvZygncGRmJywgJ0BAJykKICAgICAgfSwgMzAwMCk7CiAgICB9LAogICAgLy/lr7zlh7pwZGYKICAgIGFzeW5jIGV4cG9ydFBkZihyZWZzbmFtZSA9ICJ0YWJsZVBkZiIsIHRhYmxlYWxsLCBuYW1lLCBmbGFnID0gZmFsc2UsIHN5c2opIHsKICAgICAgbGV0IHRhYmxlc3NzID0gdGhpcy4kcmVmc1tyZWZzbmFtZV07CiAgICAgIGlmICh0YWJsZXNzcyBpbnN0YW5jZW9mIEFycmF5KSB7CiAgICAgICAgdGFibGVzc3MgPSB0YWJsZXNzc1swXTsKICAgICAgfQogICAgICBsZXQgZWxlbWVudCA9IHRhYmxlc3NzLiRlbDsKCiAgICAgIGVsZW1lbnQuc3R5bGUucG9zaXRpb24gPSAicmVsYXRpdmUiOwogICAgICB2YXIgamxpbnB1dCA9ICIiOwogICAgICAvL+aJuemHj+WvvOWHuuagh+iusAogICAgICBpZiAoZmxhZykgewogICAgICAgIGZvciAoY29uc3QgaXRlbSBpbiBzeXNqKSB7CiAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHN5c2pbaXRlbV0ubGVuZ3RoOyBpKyspIHsKICAgICAgICAgICAgaWYgKHN5c2pbaXRlbV1baV0ubnJicyA9PT0gIue7k+iuuiIpIHsKICAgICAgICAgICAgICBqbGlucHV0ID0gc3lzaltpdGVtXVtpICsgMV0udGV4dDsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICB2YXIgaW1nMSA9ICIiOwogICAgICAgIHZhciBpbWcyID0gIiI7CiAgICAgICAgbGV0IGpsRG9tID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQodGhpcy5qbERhdGEub2JqSWQpOwogICAgICAgIGlmIChqbERvbSkgewogICAgICAgICAgamxpbnB1dCA9IHRoaXMuaXNKbERpc2FibGVkID8gamxEb20uaW5uZXJUZXh0IDogamxEb20udmFsdWU7CiAgICAgICAgfQogICAgICB9CgogICAgICBpZiAoL+S4jeWQiOagvC9naS50ZXN0KGpsaW5wdXQpKSB7CiAgICAgICAgaW1nMSA9ICIvaW1hZ2UvcXVhbGlmaWVkLnBuZyI7CiAgICAgICAgaW1nMiA9ICIvaW1hZ2UvdGVzdF90ZXN0LnBuZyI7CiAgICAgIH0gZWxzZSBpZiAoL+WQiOagvC9naS50ZXN0KGpsaW5wdXQpKSB7CiAgICAgICAgaW1nMSA9ICIvaW1hZ2UvdW5xdWFsaWZpZWQucG5nIjsKICAgICAgICBpbWcyID0gIi9pbWFnZS90ZXN0X3Rlc3QucG5nIjsKICAgICAgfQogICAgICAvL+WQiOagvCBvciDkuI3lkIjmoLwg55uW56ugCiAgICAgIHZhciBpbWdib3gxID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgiZGl2Iik7CiAgICAgIGltZ2JveDEuc3R5bGUucG9zaXRpb24gPSAiYWJzb2x1dGUiOwogICAgICBpbWdib3gxLmlkID0gImd6aW1nMSI7CiAgICAgIGltZ2JveDEuc3R5bGUuYm90dG9tID0gIjAiOwogICAgICBpbWdib3gxLnN0eWxlLnJpZ2h0ID0gIjAiOwogICAgICBpbWdib3gxLnN0eWxlLnRvcCA9ICIwIjsKICAgICAgaW1nYm94MS5zdHlsZS5tYXJnaW5SaWdodCA9ICIxMHB4IjsKICAgICAgaW1nYm94MS5zdHlsZS5tYXJnaW5Ub3AgPSAiOTBweCI7CiAgICAgIHZhciBpbWdEb2N1bWVudDEgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCJpbWciKTsKICAgICAgaW1nRG9jdW1lbnQxLnNldEF0dHJpYnV0ZSgic3JjIiwgaW1nMSk7CiAgICAgIGltZ2JveDEuYXBwZW5kQ2hpbGQoaW1nRG9jdW1lbnQxKTsKICAgICAgZWxlbWVudC5hcHBlbmRDaGlsZChpbWdib3gxKTsKICAgICAgLy/nm5blhaznq6AKICAgICAgdmFyIGltZ2JveDIgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCJkaXYiKTsKICAgICAgaW1nYm94Mi5zdHlsZS5wb3NpdGlvbiA9ICJhYnNvbHV0ZSI7CiAgICAgIGltZ2JveDIuaWQgPSAiZ3ppbWcyIjsKICAgICAgaW1nYm94Mi5zdHlsZS5ib3R0b20gPSAiMCI7CiAgICAgIGltZ2JveDIuc3R5bGUucmlnaHQgPSAiMCI7CiAgICAgIGltZ2JveDIuc3R5bGUudG9wID0gIjAiOwogICAgICBpbWdib3gyLnN0eWxlLm1hcmdpblJpZ2h0ID0gIjMwMHB4IjsKICAgICAgaW1nYm94Mi5zdHlsZS5tYXJnaW5Ub3AgPSAiMTBweCI7CiAgICAgIHZhciBpbWdEb2N1bWVudDIgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCJpbWciKTsKICAgICAgaW1nRG9jdW1lbnQyLnNldEF0dHJpYnV0ZSgic3JjIiwgaW1nMik7CiAgICAgIGltZ2JveDIuYXBwZW5kQ2hpbGQoaW1nRG9jdW1lbnQyKTsKICAgICAgZWxlbWVudC5hcHBlbmRDaGlsZChpbWdib3gyKTsKCiAgICAgIGF3YWl0IGh0bWxUb1BkZi5vdXRQdXRQZGZGbihlbGVtZW50LCAic3BsaXRDbGFzcyIpOwogICAgICBsZXQgcGRmID0gYXdhaXQgaHRtbFRvUGRmLmRvd25sb2FkUERGKGVsZW1lbnQsIG5hbWUgfHwgdGhpcy50aXRsZU5hbWUpOwogICAgICBpZiAodGFibGVhbGwpIHsKICAgICAgICByZXR1cm4gcGRmOwogICAgICB9CiAgICAgIHBkZi5wZGYuc2F2ZShwZGYubmFtZSk7CiAgICAgIHRoaXMuY2xvc2VZbERpYWxvZygpOwogICAgICAvLyDmgaLlpI3ljp/ooajmoLzmoLflvI8KICAgICAgbGV0IGd6aW1nMSA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCJnemltZzEiKTsKICAgICAgZ3ppbWcxICYmIGd6aW1nMS5yZW1vdmUoKTsKICAgICAgbGV0IGd6aW1nMiA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCJnemltZzIiKTsKICAgICAgZ3ppbWcyICYmIGd6aW1nMi5yZW1vdmUoKTsKICAgIH0sCiAgICBzaG93VGVzdCgpIHsKICAgICAgbGV0IHRhYmxlc3NzID0gdGhpcy4kcmVmc1sidGFibGVQZGYiXTsKICAgICAgaWYgKHRhYmxlc3NzIGluc3RhbmNlb2YgQXJyYXkpIHsKICAgICAgICB0YWJsZXNzcyA9IHRhYmxlc3NzWzBdOwogICAgICB9CiAgICAgIGxldCBlbGVtZW50ID0gdGFibGVzc3MuJGVsOwoKICAgICAgZWxlbWVudC5zdHlsZS5wb3NpdGlvbiA9ICJyZWxhdGl2ZSI7CiAgICAgIHZhciBqbGlucHV0ID0gIiI7CiAgICAgIHZhciBpbWcxID0gIiI7CiAgICAgIHZhciBpbWcyID0gIiI7CiAgICAgIGxldCBqbERvbSA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKHRoaXMuamxEYXRhLm9iaklkKTsKICAgICAgaWYgKGpsRG9tKSB7CiAgICAgICAgamxpbnB1dCA9IHRoaXMuaXNKbERpc2FibGVkID8gamxEb20uaW5uZXJUZXh0IDogamxEb20udmFsdWU7CiAgICAgIH0KICAgICAgaWYgKC/kuI3lkIjmoLwvZ2kudGVzdChqbGlucHV0KSkgewogICAgICAgIGltZzEgPSAiL2ltYWdlL3F1YWxpZmllZC5wbmciOwogICAgICAgIGltZzIgPSAiL2ltYWdlL3Rlc3RfdGVzdC5wbmciOwogICAgICB9IGVsc2UgaWYgKC/lkIjmoLwvZ2kudGVzdChqbGlucHV0KSkgewogICAgICAgIGltZzEgPSAiL2ltYWdlL3VucXVhbGlmaWVkLnBuZyI7CiAgICAgICAgaW1nMiA9ICIvaW1hZ2UvdGVzdF90ZXN0LnBuZyI7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICB9CiAgICAgIC8v5ZCI5qC8IG9yIOS4jeWQiOagvCDnm5bnq6AKICAgICAgdmFyIGltZ2JveDEgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCJkaXYiKTsKICAgICAgaW1nYm94MS5zdHlsZS5wb3NpdGlvbiA9ICJhYnNvbHV0ZSI7CiAgICAgIGltZ2JveDEuaWQgPSAiZ3ppbWcxIjsKICAgICAgaW1nYm94MS5zdHlsZS5ib3R0b20gPSAiMCI7CiAgICAgIGltZ2JveDEuc3R5bGUucmlnaHQgPSAiMCI7CiAgICAgIGltZ2JveDEuc3R5bGUudG9wID0gIjAiOwogICAgICBpbWdib3gxLnN0eWxlLm1hcmdpblJpZ2h0ID0gIjEwcHgiOwogICAgICBpbWdib3gxLnN0eWxlLm1hcmdpblRvcCA9ICI5MHB4IjsKICAgICAgdmFyIGltZ0RvY3VtZW50MSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoImltZyIpOwogICAgICBpbWdEb2N1bWVudDEuc2V0QXR0cmlidXRlKCJzcmMiLCBpbWcxKTsKICAgICAgaW1nYm94MS5hcHBlbmRDaGlsZChpbWdEb2N1bWVudDEpOwogICAgICBlbGVtZW50LmFwcGVuZENoaWxkKGltZ2JveDEpOwogICAgICAvL+ebluWFrOeroAogICAgICB2YXIgaW1nYm94MiA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoImRpdiIpOwogICAgICBpbWdib3gyLnN0eWxlLnBvc2l0aW9uID0gImFic29sdXRlIjsKICAgICAgaW1nYm94Mi5pZCA9ICJnemltZzIiOwogICAgICBpbWdib3gyLnN0eWxlLmJvdHRvbSA9ICIwIjsKICAgICAgaW1nYm94Mi5zdHlsZS5yaWdodCA9ICIwIjsKICAgICAgaW1nYm94Mi5zdHlsZS50b3AgPSAiMCI7CiAgICAgIGltZ2JveDIuc3R5bGUubWFyZ2luUmlnaHQgPSAiMzAwcHgiOwogICAgICBpbWdib3gyLnN0eWxlLm1hcmdpblRvcCA9ICIxMHB4IjsKICAgICAgdmFyIGltZ0RvY3VtZW50MiA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoImltZyIpOwogICAgICBpbWdEb2N1bWVudDIuc2V0QXR0cmlidXRlKCJzcmMiLCBpbWcyKTsKICAgICAgaW1nYm94Mi5hcHBlbmRDaGlsZChpbWdEb2N1bWVudDIpOwogICAgICBlbGVtZW50LmFwcGVuZENoaWxkKGltZ2JveDIpOwogICAgfSwKICAgIC8v5L+d5a2Y5aGr5YaZ5oql5ZGK5pWw5o2uCiAgICBzYXZlWWxEaWFsb2coKSB7CiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAvLyDku6XmnI3liqHnmoTmlrnlvI/osIPnlKjnmoQgTG9hZGluZyDpnIDopoHlvILmraXlhbPpl60KICAgICAgICB0aGlzLmxvYWRpbmcgPSBMb2FkaW5nLnNlcnZpY2UoewogICAgICAgICAgbG9jazogdHJ1ZSwgLy9sb2Nr55qE5L+u5pS556ymLS3pu5jorqTmmK9mYWxzZQogICAgICAgICAgdGV4dDogIua1geeoi+i/m+ihjOS4re+8jOivt+eojeWQjiIsIC8v5pi+56S65Zyo5Yqg6L295Zu+5qCH5LiL5pa555qE5Yqg6L295paH5qGICiAgICAgICAgICBzcGlubmVyOiAiZWwtaWNvbi1sb2FkaW5nIiwgLy/oh6rlrprkuYnliqDovb3lm77moIfnsbvlkI0KICAgICAgICAgIGJhY2tncm91bmQ6ICJyZ2JhKDAsIDAsIDAsIDAuNykiLCAvL+mBrue9qeWxguminOiJsgogICAgICAgICAgdGFyZ2V0OiBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCIjZGlhbG9nQWN0c3QiKQogICAgICAgIH0pOwogICAgICB9KTsKICAgICAgLy/osIPnlKjlrZDnu4Tku7bkuK3nmoTmlrnms5XvvIzojrflj5bljZXlhYPmoLznmoTlgLwKICAgICAgbGV0IHBhcmFtcyA9IHRoaXMuJHJlZnMudGFibGVQZGYuZ2V0UGFyYW1zKCk7CiAgICAgIHNhdmVDaGlsZHNWYWx1ZSh7CiAgICAgICAgc3ltYmlkOiB0aGlzLnNhdmVEYXRhLnN5bWJpZCwKICAgICAgICBvYmpJZDogdGhpcy5zYXZlRGF0YS5vYmpJZCwKICAgICAgICBwYXJhbXM6IHBhcmFtcwogICAgICB9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsCiAgICAgICAgICAgIG1lc3NhZ2U6ICLkv53lrZjmiJDlip8hIgogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgICAgLy/mm7TmlrDor5XpqozkurrlkZgKICAgICAgdXBkYXRlU3liZ2psKHRoaXMuY2xpY2tSb3cpLnRoZW4ocmVzID0+IHsKICAgICAgICBpZiAocmVzLmNvZGUgPT0gIjAwMDAiKSB7CiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICAgIC8vIOS7peacjeWKoeeahOaWueW8j+iwg+eUqOeahCBMb2FkaW5nIOmcgOimgeW8guatpeWFs+mXrQogICAgICAgICAgICB0aGlzLmxvYWRpbmcuY2xvc2UoKTsKICAgICAgICAgIH0pOwogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLmk43kvZzmiJDlip/vvIEiKTsKICAgICAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IGZhbHNlOwogICAgICAgICAgdGhpcy5pc1Nob3dEb3duTG9hZERpYWxvZyA9IGZhbHNlOwogICAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvL+aKpeWRiuiusOW9leWIl+ihqOafpeivogogICAgYXN5bmMgZ2V0RGF0YShwYXJhbXMpIHsKICAgICAgdHJ5IHsKICAgICAgICB0aGlzLnF1ZXJ5UGFyYW0gPSB7IC4uLnRoaXMucXVlcnlQYXJhbSwgLi4ucGFyYW1zIH07CiAgICAgICAgdGhpcy4kcmVmcy5zeWJnbHIubG9hZGluZyA9IHRydWU7CiAgICAgICAgY29uc3QgcGFyYW0gPSB7IC4uLnRoaXMucXVlcnlQYXJhbSwgLi4ucGFyYW1zIH07CiAgICAgICAgY29uc3QgeyBkYXRhLCBjb2RlIH0gPSBhd2FpdCBnZXRTeWJnamxEYXRhQnlQYWdlKHBhcmFtKTsKICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8udGFibGVEYXRhID0gZGF0YS5yZWNvcmRzOwogICAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnRvdGFsID0gZGF0YS50b3RhbDsKICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgICAgLy8g5Lul5pyN5Yqh55qE5pa55byP6LCD55So55qEIExvYWRpbmcg6ZyA6KaB5byC5q2l5YWz6ZetCiAgICAgICAgICAgIHRoaXMuJHJlZnMuc3liZ2xyLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgIC8vIOS7peacjeWKoeeahOaWueW8j+iwg+eUqOeahCBMb2FkaW5nIOmcgOimgeW8guatpeWFs+mXrQogICAgICAgICAgdGhpcy4kcmVmcy5zeWJnbHIubG9hZGluZyA9IGZhbHNlOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgLy/or5XpqozmqKHmnb/ngrnlh7vkuovku7YKICAgIHN5bWJTZWxlY3RlZENsaWNrKCkgewogICAgICBpZiAodGhpcy5mb3JtLnNibWMgPT09ICIiIHx8IHRoaXMuZm9ybS5zYm1jID09PSB1bmRlZmluZWQpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+mAieaLqeivlemqjOiuvuWkhyIpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB0aGlzLmlzU2hvd1N5bWJEaWFsb2cgPSB0cnVlOwogICAgfSwKICAgIC8v6K+V6aqM5qih5p2/5o6l5pS25pWw5o2uCiAgICBoYW5kbGVBY2NlcHRNYkRhdGEobWJEYXRhKSB7CiAgICAgIC8v5qih5p2/5ZCN56ewCiAgICAgIC8vIHRoaXMuZm9ybS5zeW1iID0gbWJEYXRhLm1ibWM7CiAgICAgIGNvbnNvbGUubG9nKCJtYkRhdGEiLCBtYkRhdGEpOwogICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLCAic3ltYiIsIG1iRGF0YS5tYm1jKTsKICAgICAgLy/mqKHmnb9pZAogICAgICB0aGlzLmZvcm0uc3ltYmlkID0gbWJEYXRhLm9iaklkOwogICAgfSwKICAgIC8v5YWz6Zet5qih5p2/5by556qXCiAgICBoYW5kbGVDb250cm9sU3ltYlNlbGVjdERpYWxvZyhpc1Nob3cpIHsKICAgICAgdGhpcy5pc1Nob3dTeW1iRGlhbG9nID0gaXNTaG93OwogICAgfSwKICAgIC8v57uE5Lu25o6l5Y+X6K6+5aSH5Y+C5pWw5pWw5o2uCiAgICBoYW5kbGVBY2NlcHRTYkRhdGEoc2JEYXRhKSB7CiAgICAgIGNvbnNvbGUubG9nKCJzYkRhdGEiLCBzYkRhdGEpOwogICAgICBsZXQgc2JtY1MgPSBbXTsKICAgICAgbGV0IHN5c2JTID0gW107CiAgICAgIHNiRGF0YS5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgIHNibWNTLnB1c2goaXRlbS5zYm1jKTsKICAgICAgICBzeXNiUy5wdXNoKGl0ZW0ub2JqSWQpOwogICAgICB9KTsKICAgICAgdGhpcy5mb3JtLnNibWMgPSBzYm1jUy5qb2luKCIsIik7CiAgICAgIHRoaXMuZm9ybS5zeXNiaWQgPSBzeXNiUy5qb2luKCIsIik7CiAgICAgIHRoaXMuc3ltYkRhdGEuc2JseGlkID0gc2JEYXRhWzBdLnNibHhibTsKICAgICAgdGhpcy5mb3JtLnl4YmggPSBzYkRhdGFbMF0ud3ptYzsKICAgICAgdGhpcy5mb3JtLnR5cnEgPSBzYkRhdGFbMF0udHlycTsKICAgICAgLy8gdGhpcy5mb3JtLnNibWMgPSBzYkRhdGEuc2JtYzsKICAgICAgLy8gdGhpcy5mb3JtLnN5c2JpZCA9IHNiRGF0YS5vYmpJZDsKICAgICAgLy8gY29uc29sZS5sb2coInNiRGF0YSIsc2JEYXRhKTsKICAgICAgLy8gLy/orr7lpIfnsbvlnovnvJbnoIEKICAgICAgLy8gLy/nur/ot68gc2JseGJtIHNibHhibSAgc2JseGJtCiAgICAgIC8vIHRoaXMuc3ltYkRhdGEuc2JseGlkPXNiRGF0YS5zYmx4Ym07CiAgICB9LAogICAgLy/mjqfliLblhbPpl63or5Xpqozorr7lpIflvLnlh7rmoYYKICAgIGhhbmRsZUNvbnRyb2xTeXNiU2VsZWN0RGlhbG9nKGlzU2hvdykgewogICAgICB0aGlzLmlzU2hvd1N5c2JEaWFsb2cgPSBpc1Nob3c7CiAgICB9LAogICAgLy/ojrflj5bor5XpqozljZXkvY3mlbDmja4KICAgIGdldE9yZ2FuaXphdGlvblNlbGVjdGVkKCkgewogICAgICBsZXQgcGFyZW50SWQgPSAiMTAwMSI7CiAgICAgIGdldE9yZ2FuaXphdGlvblNlbGVjdGVkKHsgcGFyZW50SWQ6IHBhcmVudElkIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICByZXMuZGF0YS5mb3JFYWNoKChpdGVtLCBpbmRleCkgPT4gewogICAgICAgICAgaWYgKGl0ZW0udmFsdWUgPT09IDQwMDIpIHsKICAgICAgICAgICAgbGV0IHN5ZHcgPSB7fTsKICAgICAgICAgICAgc3lkdy5sYWJlbCA9IGl0ZW0ubGFiZWw7CiAgICAgICAgICAgIHN5ZHcudmFsdWUgPSBpdGVtLnZhbHVlOwogICAgICAgICAgICB0aGlzLnN5ZHdMaXN0LnB1c2goc3lkdyk7CiAgICAgICAgICB9CiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIC8v5o6l5Y+X6K+V6aqM6K6+5aSH5Zyw54K55qCR57uT5p6E5pWw5o2uCiAgICBoYW5kbGVBY2Nlc3NUcmVlRGF0YSh0cmVlTm9kZSkgewogICAgICBjb25zb2xlLmxvZygidHJlZU5vZGUiLCB0cmVlTm9kZSk7CiAgICAgIGlmICh0aGlzLm1haW5EYXRhLmx4ID09PSAiIikgewogICAgICAgIGNvbnNvbGUubG9nKCJ0aGlzLm1haW5EYXRhLmx4IiwgIjEyMyIpOwogICAgICAgIGxldCBzeWRkbWMgPSBkb2N1bWVudC5nZXRFbGVtZW50c0J5TmFtZSgic3lkZG1jIik7CiAgICAgICAgY29uc29sZS5sb2coInN5ZGRtYyIsIHN5ZGRtYyk7CiAgICAgICAgaWYgKHN5ZGRtYykgewogICAgICAgICAgc3lkZG1jWzBdLnZhbHVlID0gdHJlZU5vZGUubGFiZWw7CiAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW0uc3lkZGlkID0gdHJlZU5vZGUuaWQ7CiAgICAgICAgfQogICAgICB9CiAgICAgIC8v57uZ6KGo5Y2V6K6+5aSH5Zyw54K56LWL5YC8CiAgICAgIHRoaXMuZm9ybS5zeWRkID0gdHJlZU5vZGUubGFiZWw7CiAgICAgIHRoaXMuYmFzaWNEYXRhLnN5ZGQgPSB0cmVlTm9kZS5sYWJlbDsKICAgICAgLy/mlbDmja7lupPlrZjlgqjlgLwKICAgICAgdGhpcy5mb3JtLnN5ZGRpZCA9IHRyZWVOb2RlLmlkOwogICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLCAic2JtYyIsICIiKTsKICAgIH0sCiAgICAvL+WtkOe7hOS7tuWFs+mXreivlemqjOiuvuWkh+WcsOeCueW8ueeqlwogICAgaGFuZGxlQ2xvc2VTeWRkRGlhbG9nKHN5ZGREaWFsb2dDb250cm9sKSB7CiAgICAgIHRoaXMuaXNTaG93U3lzYmRkRGlhbG9nID0gc3lkZERpYWxvZ0NvbnRyb2w7CiAgICB9LAogICAgLy/ojrflj5bor5XpqozkuJPkuJrkuIvmi4nmoYbmlbDmja4KICAgIGdldE9wdGlvbnMoKSB7CiAgICAgIGdldFN5enlTZWxlY3RlZE9wdGlvbnMoKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5zeXp5TGlzdCA9IHJlcy5kYXRhOwogICAgICAgIHRoaXMuZmlsdGVySW5mby5maWVsZExpc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIGlmIChpdGVtLnZhbHVlID09ICJzeXp5IikgewogICAgICAgICAgICBpdGVtLm9wdGlvbnMgPSB0aGlzLnN5enlMaXN0OwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9KTsKICAgICAgZ2V0RGljdFR5cGVEYXRhKCJzeWJnX3p0Y2wiKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5maWx0ZXJJbmZvLmZpZWxkTGlzdC5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgaWYgKGl0ZW0udmFsdWUgPT0gImlzaGciKSB7CiAgICAgICAgICAgIGl0ZW0ub3B0aW9ucyA9IHJlcy5kYXRhLm1hcChlID0+IHsKICAgICAgICAgICAgICBlLnZhbHVlID0gZS5udW12YWx1ZQogICAgICAgICAgICAgIHJldHVybiBlCiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICAvL+agueaNruivlemqjOS4k+S4mue8lueggeiOt+WPluivlemqjOaAp+i0qOS/oeaBrwogICAgc2VsZWN0U3l4ekRhdGFCeVN5enlibSgpIHsKICAgICAgLy9jaGFuZ2Xkuovku7blj5HnlJ/mlLnlj5jml7bvvIzmuIXnqbrkuYvliY3or5XpqozmgKfotKgKICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgInN5eHpibSIsICIiKTsKICAgICAgdGhpcy5zeXh6TGlzdCA9IFtdOwogICAgICBsZXQgc3l6eWJtID0gdGhpcy5mb3JtLnN5enlibTsKICAgICAgc2VsZWN0U3l4ekRhdGFCeVN5enlibSh7IHN5enlibSB9KS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLnN5eHpMaXN0ID0gcmVzcG9uc2UuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLy/ngrnlh7vkv67mlLnor6bmg4XmjInpkq7ml7blm57mmL7or5XpqozmgKfotKjml7bkvb/nlKgKICAgIHNlbGVjdFN5eHpEYXRhQnlTeXp5Ym0xKCkgewogICAgICB0aGlzLnN5eHpMaXN0ID0gW107CiAgICAgIGxldCBzeXp5Ym0gPSB0aGlzLmZvcm0uc3l6eWJtOwogICAgICBzZWxlY3RTeXh6RGF0YUJ5U3l6eWJtKHsgc3l6eWJtIH0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuc3l4ekxpc3QgPSByZXNwb25zZS5kYXRhOwogICAgICB9KTsKICAgIH0sCgogICAgLy/orr7lpIfkuJPkuJrlj5HnlJ/lj5jljJbml7bvvIzmuIXnqbrorr7lpIflnLDngrnmlbDmja4KICAgIGFzeW5jIHNlbGVjdHNienkodmFsKSB7CiAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICJzeWRkIiwgIiIpOwogICAgICBsZXQgc2JkZGFsbCA9IFtdOwogICAgICB0aGlzLnNiZGRMaXN0ID0gW107CiAgICAgIHN3aXRjaCAodmFsKSB7CiAgICAgICAgY2FzZSAi5Y+Y55S16K6+5aSHIjoKICAgICAgICAgIHNiZGRhbGwgPSBhd2FpdCBnZXRiZHpMaXN0KHt9KTsKICAgICAgICAgIHNiZGRhbGwuZm9yRWFjaCgoaXRlbSwgaW5kZXgpID0+IHsKICAgICAgICAgICAgbGV0IGJkeiA9IHt9OwogICAgICAgICAgICBiZHoubGFiZWwgPSBpdGVtLmJkem1jOwogICAgICAgICAgICBiZHoudmFsdWUgPSBpdGVtLm9iaklkOwogICAgICAgICAgICB0aGlzLnNiZGRMaXN0LnB1c2goYmR6KTsKICAgICAgICAgIH0pOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSAi6L6T55S16K6+5aSHIjoKICAgICAgICAgIGxldCB7IGRhdGEgfSA9IGF3YWl0IGdldFNkTGluZVNlbGVjdGVkKHt9KTsKICAgICAgICAgIHRoaXMuc2JkZExpc3QgPSBkYXRhOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSAi6YWN55S16K6+5aSHIjoKICAgICAgICAgIGF3YWl0IHRoaXMuZ2V0UGRzU2VsZWN0ZWQoKTsKICAgICAgICAgIGJyZWFrOwogICAgICB9CiAgICB9LAoKICAgIG9uY2xja2RkKHZhbCkgewogICAgICBjb25zb2xlLmxvZyh2YWwpOwogICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLCAic3lkZCIsIHZhbC5sYWJlbCk7CiAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICJzeWRkaWQiLCB2YWwudmFsdWUpOwogICAgfSwKCiAgICAvL+iOt+WPlumFjeeUteWupOS4i+aLieaVsOaNrgogICAgYXN5bmMgZ2V0UGRzU2VsZWN0ZWQoKSB7CiAgICAgIHRoaXMuc2JkZExpc3QgPSBbXTsKICAgICAgYXdhaXQgZ2V0UGRzU2VsZWN0ZWQoe30pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLnNiZGRMaXN0ID0gcmVzLmRhdGE7CiAgICAgIH0pOwogICAgfSwKCiAgICAvL+iuvuWkh+WcsOeCueWPkeeUn+WPmOWMluaXtu+8jOa4heepuuivlemqjOiuvuWkh+eahOaVsOaNrgogICAgY2hhbmdlSW5wdXQoKSB7CiAgICAgIGNvbnNvbGUubG9nKCIxMjMxMjMiKTsKICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgInNibWMiLCAiIik7CiAgICB9LAoKICAgIC8v6K+V6aqM6K6+5aSH5Zyw54K554K55Ye75LqL5Lu2CiAgICBzeXNiZGRDbGljaygpIHsKICAgICAgaWYgKHRoaXMuZm9ybS5zanp5ID09PSAiIiB8fCB0aGlzLmZvcm0uc2p6eSA9PT0gdW5kZWZpbmVkKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7fpgInmi6norr7lpIfkuJPkuJoiKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgaWYgKHRoaXMuZm9ybS5zanp5ID09PSAi5Y+Y55S16K6+5aSHIikgewogICAgICAgIHRoaXMubWFpbkRhdGEubHggPSAiMiI7CiAgICAgIH0gZWxzZSBpZiAodGhpcy5mb3JtLnNqenkgPT09ICLphY3nlLXorr7lpIciKSB7CiAgICAgICAgdGhpcy5tYWluRGF0YS5seCA9ICIzIjsKICAgICAgfSBlbHNlIGlmICh0aGlzLmZvcm0uc2p6eSA9PT0gIui+k+eUteiuvuWkhyIpIHsKICAgICAgICB0aGlzLm1haW5EYXRhLmx4ID0gIjEiOwogICAgICB9CiAgICAgIHRoaXMuaXNTaG93U3lzYmRkRGlhbG9nID0gdHJ1ZTsKICAgIH0sCiAgICAvL+ivlemqjOiuvuWkh+mAieaLqeS6i+S7tgogICAgc3lzYlNlbGVjdGVkQ2xpY2soKSB7CiAgICAgIC8v6K+V6aqM6K6+5aSH5Y+R55Sf5Y+Y5YyW5pe277yM5riF56m66K+V6aqM5qih5p2/5b6X5pWw5o2uCiAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICJzeW1iIiwgIiIpOwogICAgICBpZiAodGhpcy5mb3JtLnNqenkgPT09ICIiIHx8IHRoaXMuZm9ybS5zanp5ID09PSB1bmRlZmluZWQpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+mAieaLqeiuvuWkh+S4k+S4miIpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBpZiAodGhpcy5mb3JtLnN5ZGQgPT09ICIiIHx8IHRoaXMuZm9ybS5zeWRkID09PSB1bmRlZmluZWQpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+mAieaLqeiuvuWkh+WcsOeCuSIpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBpZiAodGhpcy5mb3JtLnNqenkgPT09ICLlj5jnlLXorr7lpIciKSB7CiAgICAgICAgdGhpcy5zZWxlY3RlZFNiUGFyYW0ubHggPSAiYmQiOwogICAgICAgIHRoaXMuc2VsZWN0ZWRTYlBhcmFtLmZzc3MgPSAi6ZmE5bGeIjsKICAgICAgfSBlbHNlIGlmICh0aGlzLmZvcm0uc2p6eSA9PT0gIumFjeeUteiuvuWkhyIpIHsKICAgICAgICB0aGlzLnNlbGVjdGVkU2JQYXJhbS5seCA9ICJwZCI7CiAgICAgIH0gZWxzZSBpZiAodGhpcy5mb3JtLnNqenkgPT09ICLovpPnlLXorr7lpIciKSB7CiAgICAgICAgdGhpcy5zZWxlY3RlZFNiUGFyYW0ubHggPSAic2QiOwogICAgICB9CiAgICAgIHRoaXMuc2VsZWN0ZWRTYlBhcmFtLnNibWMgPSB0aGlzLmZvcm0uc3lkZDsgLy/miYDlsZ7kvY3nva4KICAgICAgdGhpcy5pc1Nob3dTeXNiRGlhbG9nID0gdHJ1ZTsKICAgIH0sCiAgICAvL+mHjee9ruaMiemSrgogICAgZ2V0UmVzZXQoZGF0YSkgewogICAgICAvL+S4jeWPr+S7pea4heepuuiuvuWkh+S4k+S4mgogICAgICAvLyBsZXQgc3lkZG1jID0gZG9jdW1lbnQuZ2V0RWxlbWVudHNCeU5hbWUoInN5ZGRtYyIpOwogICAgICAvLyBjb25zb2xlLmxvZygic3lkZG1jIiwgc3lkZG1jKTsKICAgICAgLy8gaWYgKHN5ZGRtYykgewogICAgICAvLyAgIHN5ZGRtY1swXS52YWx1ZSA9ICIiOwogICAgICAvLyB9CiAgICAgIHRoaXMucXVlcnlQYXJhbSA9IHt9OwogICAgfSwKICAgIC8v5LiL5ouJ5qGGY2hhbmdl5LqL5Lu2CiAgICBhc3luYyBoYW5kbGVFdmVudCh2YWwsIHZhbDEpIHsKICAgICAgLy/lj5jnlLXnq5nmn6Xor6LkuIvmi4nmoYbpgInpobnmoLnmja7miYDpgInliIblhazlj7jluKblh7rmnaUKICAgICAgaWYgKHZhbC5sYWJlbCA9PT0gInNqenkiICYmIHZhbC52YWx1ZSAmJiB2YWwudmFsdWUgIT09ICIiKSB7CiAgICAgICAgdGhpcy4kc2V0KHZhbDEsICJzeWRkaWQiLCAiIik7CiAgICAgICAgYXdhaXQgdGhpcy5zZWxlY3RzYnp5KHZhbC52YWx1ZSk7CiAgICAgICAgdGhpcy5maWx0ZXJJbmZvLmZpZWxkTGlzdC5tYXAoaXRlbSA9PiB7CiAgICAgICAgICBpZiAoaXRlbS52YWx1ZSA9PSAic3lkZGlkIikgewogICAgICAgICAgICBjb25zb2xlLmxvZygib3B0aW9uc19zeWRkaWQiLCB0aGlzLnNiZGRMaXN0KTsKICAgICAgICAgICAgcmV0dXJuIChpdGVtLm9wdGlvbnMgPSB0aGlzLnNiZGRMaXN0KTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfQogICAgfSwKICAgIC8v5paw5aKe5oyJ6ZKuCiAgICBnZXRJbnNlcnQoKSB7CiAgICAgIC8v5riF56m66K+V6aqM5oCn6LSo5pWw5o2uCiAgICAgIHRoaXMuc3l4ekxpc3QgPSBbXTsKICAgICAgdGhpcy50aXRsZSA9ICLmlrDlu7ror5XpqozmiqXlkYoiOwogICAgICAvL+ihqOWNleS4jeemgeeUqAogICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZTsKICAgICAgLy/lsZXnpLrlj5bmtojnoa7orqTmjInpkq4KICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gdHJ1ZTsKICAgICAgdGhpcy5pc1Nob3cgPSB0cnVlOwoKICAgICAgLy/muIXnqbrooajljZUKICAgICAgdGhpcy5mb3JtID0ge307CiAgICAgIHRoaXMuZm9ybS5pc2hnID0gMTsKICAgIH0sCgogICAgaW5pdEZvcm1EYXRhKCkgewoKICAgIH0sCgogICAgLy8gICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAvLyAgIHRoaXMuc2VsZWN0RGF0YSA9IHNlbGVjdGlvbgogICAgLy8gfSwKICAgIC8v5Y6G5Y+y5pWw5o2u5b2V5YWlCiAgICBnZXRJbnNlcnRscygpIHsKICAgICAgLy/muIXnqbror5XpqozmgKfotKjmlbDmja4KICAgICAgdGhpcy5zeXh6TGlzdCA9IFtdOwogICAgICB0aGlzLnRpdGxlID0gIuaWsOW7uuivlemqjOaKpeWRiiI7CiAgICAgIC8v6KGo5Y2V5LiN56aB55SoCiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICB0aGlzLnp5aXNEaXNhYmxlZCA9IHRydWU7CiAgICAgIC8v5bGV56S65Y+W5raI56Gu6K6k5oyJ6ZKuCiAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IHRydWU7CiAgICAgIHRoaXMuaXNTaG93ID0gdHJ1ZTsKICAgICAgdGhpcy5zeXJ5RGlhbG9nID0gZmFsc2U7CiAgICAgIC8v5riF56m66KGo5Y2VCiAgICAgIHRoaXMuZm9ybSA9IHt9OwogICAgICB0aGlzLmZvcm0uaXNoZyA9IDY7CiAgICB9LAoKICAgIC8v5Yig6Zmk5oyJ6ZKuCiAgICBkZWxldGVSb3cob2JqSWQpIHsKICAgICAgdGhpcy4kY29uZmlybSgi5q2k5pON5L2c5bCG5rC45LmF5Yig6Zmk6K+l5pWw5o2uLCDmmK/lkKbnu6fnu60/IiwgIuaPkOekuiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pCiAgICAgICAgLnRoZW4oKCkgPT4gewogICAgICAgICAgcmVtb3ZlU3liZ2psRGF0YShKU09OLnN0cmluZ2lmeShvYmpJZCkpLnRoZW4oKHsgY29kZSB9KSA9PiB7CiAgICAgICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTmiJDlip8hIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci5wYWdlUmVzaXplID0gIlkiOwogICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTlpLHotKUhIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci5wYWdlUmVzaXplID0gIlkiOwogICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoKSA9PiB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogImluZm8iLAogICAgICAgICAgICBtZXNzYWdlOiAi5bey5Y+W5raI5Yig6ZmkIgogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICB9LAogICAgLy/ooajmoLzlpJrpgInmoYYKICAgIGhhbmRlbFNlbGVjdENoYW5nZShyb3dzKSB7CiAgICAgIHRoaXMuc2VsZWN0Um93cyA9IHJvd3M7CiAgICAgIGNvbnNvbGUubG9nKHRoaXMuc2VsZWN0Um93cywgIjEyMyIpOwogICAgfSwKICAgIC8v5Y+W5raI5oyJ6ZKuCiAgICBjbG9zZSgpIHsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gZmFsc2U7CiAgICB9LAogICAgLy/kv53lrZjmjInpkq4KICAgIHNhdmUoKSB7CiAgICAgIHRoaXMuJHJlZnMuZm9ybS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBzYXZlT3JVcGRhdGVTeWJnamwodGhpcy5mb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PSAiMDAwMCIpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKn++8gSIpOwogICAgICAgICAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKCiAgICAvL+S4iuaKpeOAgeaPkOS6pOWuoeaguAogICAgYXN5bmMgY29tbWl0U2goKSB7CiAgICAgIGxldCB0eXBlID0gImNvbXBsZXRlIjsKICAgICAgaWYgKHRoaXMuaXNoZyA9PSAxKSB7CiAgICAgICAgLy/kuIrmiqUKICAgICAgICAvL+W9k+WJjeeKtuaAgeS4uuaWsOW7uu+8jOS4i+S4gOeOr+iKguePree7hOmVv+WuoeaJuQogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLmlzaGcgPSA1OwogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnRpdGxlID0gIuePree7hOmVv+WuoeaJuSI7CiAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5kZWZhdWx0RnJvbSA9IHRydWU7CiAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5qeGdzID0gZmFsc2U7CiAgICAgIH0gZWxzZSBpZiAodGhpcy5pc2hnID09IDUpIHsKICAgICAgICAvL+W9k+WJjeeKtuaAgeS4uuePree7hOmVv+WuoeaJue+8jOS4i+S4gOeOr+iKguWIhuWFrOWPuOWuoeaguAogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLmlzaGcgPSAyOwogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnRpdGxlID0gIuWIhuWFrOWPuOWuoeaguCI7CiAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5kZWZhdWx0RnJvbSA9IHRydWU7CiAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5qeGdzID0gZmFsc2U7CiAgICAgIH0gZWxzZSBpZiAodGhpcy5pc2hnID09IDIpIHsKICAgICAgICAvL+W9k+WJjeeKtuaAgeS4uuWIhuWFrOWPuOWuoeaguO+8jOS4i+S4gOeOr+iKguWxnuWcsOWNleS9jeWuoeaguAogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLmlzaGcgPSA3OwogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnRpdGxlID0gIuWxnuWcsOWNleS9jeWuoeaguCI7CiAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5kZWZhdWx0RnJvbSA9IHRydWU7CiAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5qeGdzID0gZmFsc2U7CiAgICAgICAgdHlwZSA9ICJjb21wbGV0ZU1hbnkiOyAvL+S4gOWPkeWkmuaUtgogICAgICB9IGVsc2UgaWYgKHRoaXMuaXNoZyA9PSA3KSB7CiAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS52YXJpYWJsZXMuaXNoZyA9IDM7CiAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS52YXJpYWJsZXMudGl0bGUgPSAi55Sf5Lqn56eR5LiT5bel5a6h5qC4IjsKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmRlZmF1bHRGcm9tID0gdHJ1ZTsKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmp4Z3MgPSBmYWxzZTsKICAgICAgfSBlbHNlIGlmICh0aGlzLmlzaGcgPT0gMykgewogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLmlzaGcgPSAzOwogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnRpdGxlID0gIuWQiOagvOmqjOaUtiI7CiAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5kZWZhdWx0RnJvbSA9IGZhbHNlOwogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuanhncyA9IGZhbHNlOwogICAgICB9IGVsc2UgaWYgKHRoaXMuaXNoZyA9PSA0KSB7CiAgICAgICAgLy/lvZPliY3nirbmgIHlhbPpl60KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuW9k+WJjea1geeoi+W3suWFs+mXre+8ge+8geaXoOazleaPkOS6pOWuoeaguCIpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBjb25zb2xlLmxvZygidGhpcy5jbGlja1JvdyIsIHRoaXMuY2xpY2tSb3cpOwogICAgICAvL+W8gOWni+aJp+ihjOivt+axggogICAgICB0aGlzLmdldFNiRnNCaigKICAgICAgICB7IGRhdGE6IHRoaXMuY2xpY2tSb3csIHR5cGU6IHR5cGUgfSwKICAgICAgICB7CiAgICAgICAgICBkZWZhdWx0Rm9ybTogdGhpcy5wcm9jZXNzRGF0YS5kZWZhdWx0RnJvbSwKICAgICAgICAgIGp4Z3M6IHRoaXMucHJvY2Vzc0RhdGEuanhncwogICAgICAgIH0KICAgICAgKTsKICAgICAgLy/or6bmg4XmoYblhbPpl60KICAgICAgdGhpcy5jbG9zZVlsRGlhbG9nKCk7CiAgICAgIC8v5oGi5aSN5YiG6aG1CiAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci5wYWdlUmVzaXplID0gIlkiOwogICAgICAvL+mHjeaWsOafpeaVsOaNrgogICAgICBhd2FpdCB0aGlzLmdldERhdGEoKTsKICAgIH0sCiAgICAvL+a1geeoi+WbvueJh+afpeeciwogICAgc2hvd1Byb2Nlc3NJbWcocm93KSB7CiAgICAgIHRoaXMub3BlbkxvYWRpbmdJbWcgPSB0cnVlOwogICAgICB0aGlzLmltZ1NyYyA9CiAgICAgICAgIi9hY3Rpdml0aS1hcGkvcHJvY2Vzcy9yZWFkLXJlc291cmNlP3Byb2Nlc3NEZWZpbml0aW9uS2V5PXN5YmdzaCZidXNpbmVzc0tleT0iICsKICAgICAgICByb3cub2JqSWQgKwogICAgICAgICImdD0iICsKICAgICAgICBuZXcgRGF0ZSgpLmdldFRpbWUoKTsKICAgIH0sCgogICAgLy/kuIrmiqXlj5HpgIHlip7nu5MKICAgIGdldFNiRnNCaihhcmdzLCBpc1Nob3cpIHsKICAgICAgbGV0IHJvdyA9IHsgLi4uYXJncy5kYXRhIH07CiAgICAgIHRoaXMuYWN0aXZpdGlPcHRpb24udGl0bGUgPSBhcmdzLnRpdGxlOwogICAgICB0aGlzLnByb2Nlc3NEYXRhLnZhcmlhYmxlcy56eSA9IHJvdy5zanp5OwogICAgICBpZiAoYXJncy50eXBlID09PSAiY29tcGxldGUiKSB7CiAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5kZWZhdWx0RnJvbSA9IGlzU2hvdy5kZWZhdWx0Rm9ybTsgLy/mmK/lkKbmmL7npLrpgInkurrlkZgKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnByb2Nlc3NUeXBlID0gImNvbXBsZXRlIjsKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmp4Z3MgPSBpc1Nob3cuanhnczsKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmJ1c2luZXNzS2V5ID0gcm93Lm9iaklkOwogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnBhc3MgPSB0cnVlOwogICAgICB9IGVsc2UgaWYgKGFyZ3MudHlwZSA9PT0gImNvbXBsZXRlTWFueSIpIHsKICAgICAgICAvL+S4gOWPkeWkmuaUtgogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuZGVmYXVsdEZyb20gPSBpc1Nob3cuZGVmYXVsdEZvcm07IC8v5piv5ZCm5pi+56S66YCJ5Lq65ZGYCiAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5wcm9jZXNzVHlwZSA9ICJjb21wbGV0ZU1hbnkiOwogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuanhncyA9IGlzU2hvdy5qeGdzOwogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuYnVzaW5lc3NLZXkgPSByb3cub2JqSWQ7CiAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS52YXJpYWJsZXMucGFzcyA9IHRydWU7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS52YXJpYWJsZXMudGl0bGUgPSAi5Zue6YCA5Y6f5Zug5o+Q5oqlIjsKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmRlZmF1bHRGcm9tID0gdHJ1ZTsKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnByb2Nlc3NUeXBlID0gInJvbGxiYWNrIjsKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmJ1c2luZXNzS2V5ID0gcm93Lm9iaklkOwogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnBhc3MgPSBmYWxzZTsKICAgICAgfQogICAgICB0aGlzLmlzU2hvd0FjdGl2aXRpID0gdHJ1ZTsKICAgIH0sCiAgICAvL+WbnumAgOaMiemSrgogICAgaGFuZGxlVGgodHlwZSkgewogICAgICB0aGlzLmdldFNiRnNCaigKICAgICAgICB7IHR5cGU6IHR5cGUsIGRhdGE6IHRoaXMuY2xpY2tSb3cgfSwKICAgICAgICB7IGRlZmF1bHRGb3JtOiBmYWxzZSB9CiAgICAgICk7CiAgICB9LAoKICAgIGFzeW5jIHNob3dUaW1lTGluZShyb3cpIHsKICAgICAgbGV0IHsgY29kZSwgZGF0YSB9ID0gYXdhaXQgSGlzdG9yeUxpc3QoeyBidXNpbmVzc0tleTogcm93Lm9iaklkIH0pOwogICAgICB0aGlzLnRpbWVEYXRhID0gZGF0YTsKICAgICAgdGhpcy50aW1lTGluZVNob3cgPSB0cnVlOwogICAgfSwKCiAgICAvL+WFs+mXreW3peS9nOa1geW8ueeqlwogICAgY2xvc2VBY3Rpdml0aSgpIHsKICAgICAgdGhpcy5pc1Nob3dBY3Rpdml0aSA9IGZhbHNlOwogICAgfSwKCiAgICAvL+WFs+mXreaXtumXtOe6v+afpeeci+mhtemdogogICAgY29sc2VUaW1lTGluZSgpIHsKICAgICAgdGhpcy50aW1lTGluZVNob3cgPSBmYWxzZTsKICAgIH0sCgogICAgLy/lt6XkvZzmtYHlm57kvKDmlbDmja4KICAgIGFzeW5jIHRvZG9SZXN1bHQoZGF0YSkgewogICAgICBjb25zb2xlLmxvZygidGhpcy5kYXRhIiwgZGF0YSk7CiAgICAgIHN3aXRjaCAoZGF0YS5hY3RpdmVUYXNrTmFtZSkgewogICAgICAgIGNhc2UgIuivlemqjOaKpeWRiuWhq+WGmSI6CiAgICAgICAgICB0aGlzLmlzaGcgPSAxOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSAi6aKG5a+85a6h5om5IjoKICAgICAgICAgIHRoaXMuaXNoZyA9IDI7CiAgICAgICAgICBicmVhazsKICAgICAgICBjYXNlICLlsZ7lnLDljZXkvY3lrqHmibkiOgogICAgICAgICAgdGhpcy5pc2hnID0gNzsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgIueUn+S6p+enkeS4k+W3peWuoeaJuSI6CiAgICAgICAgICB0aGlzLmlzaGcgPSAzOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSAi57uT5p2fIjoKICAgICAgICAgIHRoaXMuaXNoZyA9IDQ7CiAgICAgICAgICBicmVhazsKICAgICAgICBjYXNlICLnj63nu4TlrqHmibkiOgogICAgICAgICAgdGhpcy5pc2hnID0gNTsKICAgICAgICAgIGJyZWFrOwogICAgICB9CiAgICAgIGxldCByb3cgPSB7fTsKICAgICAgaWYgKHRoaXMuaXNoZyA9PSAyKSB7CiAgICAgICAgcm93ID0gewogICAgICAgICAgb2JqSWQ6IGRhdGEuYnVzaW5lc3NLZXksCiAgICAgICAgICBpc2hnOiB0aGlzLmlzaGcsCiAgICAgICAgICBzc2R3bGQ6IGRhdGEubmV4dFVzZXIsCiAgICAgICAgICBiZ3JxOiBmb3JtYXR0ZXJEYXRlVGltZShuZXcgRGF0ZSgpLCAieXl5eS1NTS1kZCIpIC8v6K6+572u5oql5ZGK5pel5pyf5a2X5q6177yM5Li65om55YeG5Lq65a6h5om55pe26Ze0CiAgICAgICAgfTsKICAgICAgfSBlbHNlIGlmICh0aGlzLmlzaGcgPT0gMykgewogICAgICAgIHJvdyA9IHsKICAgICAgICAgIG9iaklkOiBkYXRhLmJ1c2luZXNzS2V5LAogICAgICAgICAgaXNoZzogdGhpcy5pc2hnLAogICAgICAgICAgc2Nremc6IGRhdGEubmV4dFVzZXIKICAgICAgICB9OwogICAgICB9IGVsc2UgaWYgKHRoaXMuaXNoZyA9PSA1KSB7CiAgICAgICAgcm93ID0gewogICAgICAgICAgb2JqSWQ6IGRhdGEuYnVzaW5lc3NLZXksCiAgICAgICAgICBpc2hnOiB0aGlzLmlzaGcsCiAgICAgICAgICBienpzcDogZGF0YS5uZXh0VXNlck5pY2tOYW1lCiAgICAgICAgfTsKICAgICAgfSBlbHNlIGlmICh0aGlzLmlzaGcgPT0gNykgewogICAgICAgIHJvdyA9IHsKICAgICAgICAgIG9iaklkOiBkYXRhLmJ1c2luZXNzS2V5LAogICAgICAgICAgaXNoZzogdGhpcy5pc2hnLAogICAgICAgICAgc2Rkd3NocjogZGF0YS5uZXh0VXNlcgogICAgICAgIH07CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcm93ID0geyBvYmpJZDogZGF0YS5idXNpbmVzc0tleSwgaXNoZzogdGhpcy5pc2hnIH07CiAgICAgIH0KICAgICAgY29uc29sZS5sb2coInJvdyIsIHJvdyk7CiAgICAgIHVwZGF0ZVN5YmdqbChyb3cpLnRoZW4ocmVzID0+IHsKICAgICAgICBpZiAocmVzLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLmk43kvZzmiJDlip8iKTsKICAgICAgICAgIC8v6YeN572ucGFnZemhteS7jjHlvIDlp4sKICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci5wYWdlUmVzaXplID0gIlkiOwogICAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["sybglr.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4nBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "sybglr.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- id=\"app\" -->\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n      @handleReset=\"getReset\"\n      @handleEvent=\"handleEvent\"\n    />\n    <el-white class=\"button-group1\">\n      <div class=\"button_btn\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-plue\"\n          @click=\"getInsert\"\n          v-hasPermi=\"['sybglr:button:add']\"\n          >新增\n        </el-button>\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-plue\"\n          @click=\"getInsertls\"\n          v-hasPermi=\"['sybglrls:button:add']\"\n          >历史数据新增\n        </el-button>\n        <el-button type=\"primary\" icon=\"el-icon-download\" @click=\"dcpdf\"\n          >批量导出</el-button\n        >\n        <!-- <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"deleteRow\">删除</el-button> -->\n      </div>\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"handelSelectChange\"\n        height=\"63vh\"\n        ref=\"sybglr\"\n      >\n        <el-table-column\n          slot=\"table_eight\"\n          align=\"center\"\n          fixed=\"right\"\n          style=\"display: block\"\n          label=\"操作\"\n          width=\"200\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <!--    公共按钮      -->\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              @click=\"getDetailsInfo(scope.row)\"\n              title=\"详情\"\n              class=\"el-icon-view\"\n            >\n            </el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"\n                (scope.row.ishg == 1 || scope.row.ishg == 6) &&\n                  scope.row.createBy == currUser\n              \"\n              @click=\"updateDetails(scope.row)\"\n              title=\"编辑\"\n              class=\"el-icon-edit\"\n            ></el-button>\n             <el-button\n              type=\"text\"\n              size=\"small\"\n              v-hasPermi=\"['sybglrls:button:update']\"\n              @click=\"updateDetails(scope.row)\"\n              title=\"编辑\"\n              class=\"el-icon-edit\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"scope.row.ishg != 1 && scope.row.ishg != 6\"\n              @click=\"showTimeLine(scope.row)\"\n              title=\"流程查看\"\n              class=\"el-icon-lcck commonIcon\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"scope.row.ishg != 1 && scope.row.ishg != 6\"\n              @click=\"showProcessImg(scope.row)\"\n              title=\"流程图\"\n              class=\"el-icon-lct commonIcon\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"scope.row.createBy == currUser && scope.row.ishg == 6\"\n              @click=\"saveSybgInfo(scope.row)\"\n              title=\"编辑报告\"\n              class=\"el-icon-edit-outline\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              @click=\"SybgInfo(scope.row)\"\n              title=\"查看报告\"\n              class=\"el-icon-tickets\"\n            >\n            </el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"scope.row.ishg == 1 && showButton\"\n              @click=\"saveSybgInfo(scope.row)\"\n              title=\"编辑报告\"\n              class=\"el-icon-edit-outline\"\n            ></el-button>\n            <el-button\n              @click=\"getZxmmpInfo(scope.row)\"\n              type=\"text\"\n              size=\"small\"\n              title=\"数据对比\"\n              class=\"el-icon-edit-outline\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"\n                ((scope.row.ishg == 1 || scope.row.ishg == 6) &&\n                  scope.row.createBy == currUser) ||\n                  currUser === 'admin'\n              \"\n              @click=\"deleteRow(scope.row.objId)\"\n              title=\"删除\"\n              class=\"el-icon-delete\"\n            ></el-button>\n          </template>\n        </el-table-column>\n      </comp-table>\n    </el-white>\n\n    <!--详情/新增/修改-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"50%\"\n      v-dialogDrag\n    >\n      <el-form\n        label-width=\"130px\"\n        ref=\"form\"\n        :model=\"form\"\n        :disabled=\"isDisabled\"\n        :rules=\"rules\"\n      >\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"设备专业：\" prop=\"sjzy\">\n              <el-select\n                @change=\"selectsbzy\"\n                v-model=\"form.sjzy\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in sbzyList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验专业：\" prop=\"syzybm\">\n              <el-select\n                @change=\"selectSyxzDataBySyzybm\"\n                v-model=\"form.syzybm\"\n                placeholder=\"请选择试验专业\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in syzyList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验性质：\" prop=\"syxzbm\">\n              <el-select\n                v-model=\"form.syxzbm\"\n                placeholder=\"请选择试验性质\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in syxzList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"设备地点：\" prop=\"sydd\">\n              <!-- <el-input v-model=\"form.sydd\"   placeholder=\"请输入设备地点\" v-on:click.native=\"sysbddClick()\"\n                        style=\"width: 100%\"></el-input> -->\n              <el-select\n                v-model=\"form.sydd\"\n                placeholder=\"请选择设备地点\"\n                @change=\"onclckdd\"\n                style=\"width: 100%\"\n                filterable\n                clearable\n              >\n                <el-option\n                  v-for=\"item in sbddList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验设备：\" prop=\"sbmc\">\n              <el-input\n                v-model=\"form.sbmc\"\n                placeholder=\"请输入试验设备\"\n                v-on:click.native=\"sysbSelectedClick()\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"委托单位：\" prop=\"wtdw\">\n              <el-input\n                v-model=\"form.wtdw\"\n                placeholder=\"请输入委托单位\"\n                style=\"width: 100%\"\n              >\n              </el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验单位：\" prop=\"sydw\">\n              <el-select\n                v-model=\"form.sydw\"\n                placeholder=\"请选择试验单位\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in sydwList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验日期：\" prop=\"syrq\">\n              <el-date-picker\n                v-model=\"form.syrq\"\n                type=\"date\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"请选择试验日期\"\n                style=\"width: 100%\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验名称：\" prop=\"symc\">\n              <el-input\n                v-model=\"form.symc\"\n                placeholder=\"请输入试验名称\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"温度(℃)：\" prop=\"wd\">\n              <el-input\n                v-model=\"form.wd\"\n                placeholder=\"请输入温度\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"湿度(%)：\" prop=\"sd\">\n              <el-input\n                v-model=\"form.sd\"\n                placeholder=\"请输入湿度\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"油温(℃)：\" prop=\"yw\">\n              <el-input\n                v-model=\"form.yw\"\n                placeholder=\"请输入油温\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验天气：\" prop=\"tq\">\n              <el-select\n                v-model=\"form.tq\"\n                placeholder=\"请选择试验天气\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in tqList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验模板：\" prop=\"symb\">\n              <el-input\n                v-model=\"form.symb\"\n                placeholder=\"请输入试验模板\"\n                v-on:click.native=\"symbSelectedClick()\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n\n          <!-- <el-col :span=\"12\">\n            <el-form-item label=\"审核人：\" prop=\"shrid\">\n              <el-input v-model=\"form.shrid\" placeholder=\"请输入审核人\" style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"12\">\n            <el-form-item label=\"编制人：\" prop=\"bzrid\">\n              <el-input\n                v-model=\"form.bzrid\"\n                placeholder=\"请输入编制人\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验人员：\" prop=\"syryid\">\n              <el-input\n                v-model=\"form.syryid\"\n                placeholder=\"请输入试验人员\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"审核人：\" prop=\"bzzsp\">\n              <el-input\n                v-model=\"form.bzzsp\"\n                placeholder=\"请输入审核人\"\n                style=\"width: 100%\"\n                :disabled=\"syryDialog\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"批准人：\" prop=\"ssdwldmc\">\n              <el-input\n                v-model=\"form.ssdwldmc\"\n                placeholder=\"请输入批准人\"\n                style=\"width: 100%\"\n                :disabled=\"syryDialog\"\n              >\n              </el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <!-- <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"试验结论：\" prop=\"bz\">\n              <el-input type=\"textarea\" v-model=\"form.syjl\" placeholder=\"请输入试验结论\"\n                        style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row> -->\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注：\" prop=\"bz\">\n              <el-input\n                type=\"textarea\"\n                v-model=\"form.bz\"\n                placeholder=\"请输入备注\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" v-show=\"isShow\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"save\">确 定</el-button>\n      </div>\n    </el-dialog>\n    <!--试验设备地点弹窗-->\n    <el-dialog\n      title=\"试验设备地点\"\n      :visible.sync=\"isShowSysbddDialog\"\n      width=\"20%\"\n      v-if=\"isShowSysbddDialog\"\n      v-dialogDrag\n    >\n      <sysbdd\n        :mainData=\"mainData\"\n        @accessTreeData=\"handleAccessTreeData\"\n        @closeSyddDialog=\"handleCloseSyddDialog\"\n      >\n      </sysbdd>\n    </el-dialog>\n    <!--试验设备选择弹框-->\n    <el-dialog\n      title=\"设备选择\"\n      :visible.sync=\"isShowSysbDialog\"\n      width=\"60%\"\n      v-if=\"isShowSysbDialog\"\n      v-dialogDrag\n    >\n      <sysb-selectedbg\n        @handleAcceptSbData=\"handleAcceptSbData\"\n        :selectedSbParam=\"selectedSbParam\"\n        @closeSysbSelectDialog=\"handleControlSysbSelectDialog\"\n      ></sysb-selectedbg>\n    </el-dialog>\n    <!--试验模板选择组件-->\n    <el-dialog\n      title=\"模板选择\"\n      :visible.sync=\"isShowSymbDialog\"\n      width=\"70%\"\n      v-if=\"isShowSymbDialog\"\n      v-dialogDrag\n    >\n      <symbSyxm-select\n        :symbData=\"symbData\"\n        @handleAcceptMbData=\"handleAcceptMbData\"\n        @closeSymbSelectDialog=\"handleControlSymbSelectDialog\"\n      ></symbSyxm-select>\n    </el-dialog>\n    <!--填写报告模板-->\n    <!--htmlToPdf插件-->\n    <el-dialog\n      title=\"预览\"\n      :visible.sync=\"isShowDownLoadDialog\"\n      width=\"70%\"\n      class=\"outPut\"\n      id=\"dialogActst\"\n      v-dialogDrag\n    >\n      <div style=\"height: 500px;overflow-y: scroll;\">\n        <tablePdf\n          ref=\"tablePdf\"\n          :basic-data=\"basicData\"\n          :tablebox3=\"tablebox3\"\n          :tablebox2=\"tablebox2\"\n        ></tablePdf>\n      </div>\n      <div slot=\"footer\">\n        <el-button\n          @click=\"closeYlDialog\"\n          v-if=\"this.form.ishg == 1 || this.form.ishg == 6\"\n          >取 消</el-button\n        >\n        <el-button\n          type=\"primary\"\n          @click=\"saveYlDialog\"\n          v-if=\"\n            (this.form.ishg == 1 ||\n              this.form.ishg == 6 ||\n              this.form.ishg == 5) &&\n              this.bcDisabled\n          \"\n          >保存\n        </el-button>\n        <el-button type=\"primary\" @click=\"exportPdf('tablePdf')\"\n          >导 出</el-button\n        >\n        <el-button\n          type=\"primary\"\n          @click=\"commitSh\"\n          v-if=\"this.form.ishg == 1 && this.form.createBy == currUser\"\n          >上报\n        </el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"commitSh\"\n          v-if=\"this.form.ishg == 2 && this.form.ssdwld == currUser\"\n          >审批\n        </el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"commitSh\"\n          v-if=\"\n            this.form.ishg == 7 &&\n              this.form.sddwshr &&\n              this.form.sddwshr.includes(currUser)\n          \"\n          >审批\n        </el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"commitSh\"\n          v-if=\"this.form.ishg == 3 && this.form.sckzg == currUser\"\n          >审批\n        </el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"commitSh\"\n          v-if=\"this.form.ishg == 5 && this.form.bzzsp == currUserName\"\n          >审批\n        </el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"handleTh('rollback')\"\n          v-if=\"this.form.ishg == 2 && this.form.ssdwld == currUser\"\n        >\n          回退</el-button\n        >\n        <el-button\n          type=\"primary\"\n          @click=\"handleTh('rollback')\"\n          v-if=\"this.form.ishg == 3 && this.form.sckzg == currUser\"\n        >\n          回退</el-button\n        >\n        <el-button\n          type=\"primary\"\n          @click=\"handleTh('rollback')\"\n          v-if=\"this.form.ishg == 5 && this.form.bzzsp == currUserName\"\n          >回退</el-button\n        >\n        <el-button\n          type=\"primary\"\n          @click=\"handleTh('rollback')\"\n          v-if=\"this.form.ishg == 7 && this.form.sddwshr == currUser\"\n          >回退</el-button\n        >\n      </div>\n    </el-dialog>\n    <el-dialog\n      title=\"批量导出\"\n      :visible.sync=\"isShowDownLoadDialogA\"\n      width=\"70%\"\n      class=\"outPut\"\n      id=\"dialogActst\"\n      v-dialogDrag\n    >\n      <div style=\"height: 400px;overflow-y: scroll;\">\n        <tablePdf\n          :ref=\"`tablePdf${index}`\"\n          v-for=\"(item, index) in selectA\"\n          :basic-data=\"item\"\n          :tablebox3=\"item.tablebox3\"\n          :tablebox2=\"item.tablebox2\"\n        ></tablePdf>\n      </div>\n      <el-progress\n        :percentage=\"inputpdf\"\n        :format=\"progressformate\"\n      ></el-progress>\n      <!-- <el-progress :percentage=\"shengpdf\" status=\"success\"></el-progress>\n      <el-progress :percentage=\"downloadpdf\" status=\"warning\"></el-progress> -->\n    </el-dialog>\n\n    <!--  工作流  -->\n    <activiti\n      :processData=\"processData\"\n      :isShow=\"isShowActiviti\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n      v-if=\"isShowActiviti\"\n    ></activiti>\n    <time-line\n      :value=\"timeLineShow\"\n      :timeData=\"timeData\"\n      @closeTimeLine=\"colseTimeLine\"\n    />\n    <!--流程图查看-->\n    <el-dialog\n      title=\"审批流程图\"\n      :visible.sync=\"openLoadingImg\"\n      width=\"56%\"\n      append-to-body\n      :close-on-click-modal=\"false\"\n      v-dialogDrag\n    >\n      <div>\n        <img :src=\"imgSrc\" style=\"width: 100%\" />\n      </div>\n    </el-dialog>\n    <!-- 试验数据对比 -->\n    <el-dialog\n      :visible.sync=\"isShowMpInfo\"\n      v-dialogDrag\n      v-if=\"isShowMpInfo\"\n      width=\"80%\"\n      title=\"试验项目内容\"\n      :append-to-body=\"true\"\n      @close=\"closeInfoDialog\"\n    >\n      <zxm-info\n        :mp-data=\"rowData\"\n        :mx-data.sync=\"mxData\"\n        @closeInfoDialog=\"closeInfoDialog\"\n      />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  selectSyxzDataBySyzybm,\n  getSyzySelectedOptions,\n  getSybgjlDataByPage,\n  saveOrUpdateSybgjl,\n  removeSybgjlData,\n  getMouldValue,\n  getChildsValue,\n  saveChildsValue,\n  updateSybgjl,\n  getbdzList,\n  showButtenJS,\n  getTodoItemYd\n} from \"@/api/dagangOilfield/bzgl/sybglr\";\nimport {\n  getDeviceClassTreeNodeByPid,\n  getPageDataList,\n  getTable,\n  remove,\n  saveOrUpdate\n} from \"@/api/dagangOilfield/bzgl/sympk/sympk\";\nimport { Loading } from \"element-ui\";\nimport activiti from \"com/activiti_sybg\";\nimport timeLine from \"com/timeLine\";\nimport { HistoryList } from \"@/api/activiti/processTask\";\n//试验设备地点子组件\nimport sysbdd from \"@/views/dagangOilfield/bzgl/sybzk/sybgjlcomment/sysbdd\";\n//试验单位下拉框查询\nimport { getOrganizationSelected } from \"@/api/dagangOilfield/asset/bdsbtz\";\n//试验设备选择\nimport sysbSelectedbg from \"@/views/dagangOilfield/bzgl/sybzk/sybgjlcomment/sysbSelectedbg\";\n//试验模板选择\nimport symbSyxmSelect from \"@/views/dagangOilfield/bzgl/sybzk/sybgjlcomment/symbSyxmSelect\";\n// 试验数据对比\nimport zxmInfo from \"@/views/dagangOilfield/bzgl/sybzk/zxmInfo\";\n//获取线路下拉数据\nimport { getSdLineSelected } from \"@/api/dagangOilfield/asset/sdxl\";\nimport { getPdsSelected } from \"@/api/dagangOilfield/asset/pdsgl\";\n//pdf导出工具\nimport htmlToPdf from \"@/utils/print/htmlToPdf\";\nimport tablePdf from \"./tablePdf\";\nimport { formatterDateTime } from \"@/utils/handleData\";\nimport { getDictTypeData } from \"@/api/system/dict/data\";\nexport default {\n  name: \"sybglr\",\n  components: {\n    sysbdd,\n    sysbSelectedbg,\n    symbSyxmSelect,\n    htmlToPdf,\n    activiti,\n    timeLine,\n    tablePdf,\n    zxmInfo\n  },\n  data() {\n    var validateSbmc = (rule, value, callback) => {\n      if (!this.form.sbmc) {\n        callback(new Error());\n      } else {\n        callback();\n      }\n    };\n    var validateSymb = (rule, value, callback) => {\n      if (!this.form.symb) {\n        callback(new Error());\n      } else {\n        callback();\n      }\n    };\n    return {\n      // 试验数据对比用\n      isShowMpInfo: false,\n      rowData: {},\n      mxData: [],\n      //\n      showButton: false,\n      syryDialog: true,\n      bcDisabled: false,\n      tablebox3: \"\",\n      tablebox2: \"\",\n      loading: false,\n      currUser: this.$store.getters.name,\n      currUserName: this.$store.getters.nickName,\n      ssdwld: undefined, //所属单位领导\n      sckzg: undefined, //生产科专工\n      createBy: undefined, //新建人\n      clickRow: {},\n      //流程图查看\n      openLoadingImg: false,\n      imgSrc: \"\", //流程图查看地址\n      timeLineShow: false,\n      timeData: [],\n      //弹出框标题\n      activitiOption: { title: \"上报\" },\n      //工作流弹窗\n      isShowActiviti: false,\n      // 多选框选选中的数据\n      selectData: [],\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"sybgsh\",\n        businessKey: \"\",\n        businessType: \"试验报告审核\",\n        variables: {},\n        defaultFrom: true,\n        processType: \"complete\"\n      },\n      //基本信息表格数据\n      basicData: {\n        sydwmc: \"\", //试验单位\n        syxz: \"\", //试验性质\n        syrq: \"\", //  试验日期\n        syryid: \"\", // 试验人员\n        bzrid: \"\", // 编写人\n        shrid: \"\", // 审核人\n        updateBy: \"\", // 批准人\n        tq: \"\", // 试验天气\n        wd: \"\", // 温度\n        sd: \"\", // 湿度\n        sydd: \"\", //试验地点\n        wtdw: \"\", //委托单位\n        yxbh: \"\", //运行编号\n        sccj: \"\", //生产厂家\n        ccrq: \"\", //出厂日期，\n        ccbh: \"\", //出厂编号\n        sbxh: \"\", //设备型号\n        eddy: \"\", //额定电压(kV)\n        eddl: \"\", //额定电流(A)\n        xs: \"\", //相数\n        //相数接线组别\n        edrl: \"\" ,//额定容量(MVA)\n        //电压组合\n        //电流组合\n        //容量组合\n        tyrq:\"\"\n      },\n      Cont: [\n        {\n          bdz: \"变电站\",\n          name: \"\",\n          bdz1: \"委托单位\",\n          name1: \"\",\n          bdz2: \"试验单位\",\n          name2: \"\",\n          bdz3: \"运行编号\",\n          name3: \"\"\n        },\n        {\n          bdz: \"试验性质\",\n          name: \"\",\n          bdz1: \"试验日期\",\n          name1: \"\",\n          bdz2: \"试验人员\",\n          name2: \"\",\n          bdz3: \"试验地点\",\n          name3: \"\"\n        },\n        {\n          bdz: \"报告日期\",\n          name: \"\",\n          bdz1: \"编写人\",\n          name1: \"\",\n          bdz2: \"审核人\",\n          name2: \"\",\n          bdz3: \"批准人\",\n          name3: \"\"\n        },\n        {\n          bdz: \"试验天气\",\n          name: \"\",\n          bdz1: \"环境温度（℃）\",\n          name1: \"\",\n          bdz2: \"环境相对湿度（%）\",\n          name2: \"\",\n          bdz3: \"投运日期\",\n          name3: \"\"\n        }\n      ],\n      allAlign: null,\n      titleName: \"\", //填写模板标题\n      //下载弹出框控制\n      isShowDownLoadDialog: false,\n\n      isShowDownLoadDialogA: false,\n      mbInfo: {},\n      //主设备选择传递子组件参数\n      selectedSbParam: {\n        lx: \"bd\",\n        sbmc: \"\"\n      },\n      //主设备选择传递子组件参数\n      mainData: {\n        lx: \"\"\n      },\n      //试验设备选择时给试验模板子组件传递参数\n      symbData: {\n        sblxid: \"\"\n      },\n\n      //设备专业\n      sbzyList: [\n        {\n          label: \"输电设备\",\n          value: \"输电设备\"\n        },\n        {\n          label: \"变电设备\",\n          value: \"变电设备\"\n        },\n        {\n          label: \"配电设备\",\n          value: \"配电设备\"\n        }\n      ],\n      //试验模板弹出框控制\n      isShowSymbDialog: false,\n      //试验设备弹出框控制\n      isShowSysbDialog: false,\n      //试验设备地点弹出框控制\n      isShowSysbddDialog: false,\n      //试验报告记录新增弹出框表单\n      form: {\n        //固定不可清空\n        sjzy: \"\",\n        //试验专业编码\n        syzybm: \"\",\n        //试验专业名称\n        syzy: \"\",\n        //试验性质编码\n        syxzbm: \"\",\n        //试验性质名称\n        syxz: \"\",\n        //设备地点名称\n        sydd: \"\",\n        syddid: \"\", //试验地点id\n        //试验设备id\n        sysbid: \"\",\n        sbmc: \"\", //设备名称\n        sydw: \"\", //试验单位id\n        syrq: undefined, // 试验日期\n        symc: \"\", //试验名称\n        wd: \"\", //温度\n        sd: \"\", //湿度\n        yw: \"\", //油温\n        tq: \"\", //天气\n        symb: \"\", //试验模板名称\n        symbid: \"\", //试验模板id\n        bzrid: \"\", //编制人名称，后面改用下拉框\n        shrid: \"\", //审核人名称，后面改为下拉框\n        syryid: \"\", //试验人员。后面改为下拉框\n        bz: \"\" //备注\n      },\n      assetTypeCode: \"\", //设备类型编码\n      //详情弹框是否显示\n      isShowDetails: false,\n      //显示取消确认按钮\n      isShow: true,\n      //是否禁用\n      isDisabled: false,\n      zyisDisabled: false,\n      //试验专业\n      syzyList: [],\n      //试验性质\n      syxzList: [],\n      //设备地点\n      sbddList: [],\n      //试验单位\n      sydwList: [],\n      //流程状态\n      ishg: \"\",\n      sybgjlld: \"\",\n      selectA: [],\n      shengpdf: 0,\n      inputpdf: 0,\n      downloadpdf: 0,\n      syxmid: \"\",\n      sysbid: \"\",\n      //试验天气\n      tqList: [\n        { label: \"晴\", value: \"晴\" },\n        { label: \"阴\", value: \"阴\" },\n        { label: \"雾\", value: \"雾\" },\n        { label: \"小雨\", value: \"小雨\" },\n        { label: \"中雨\", value: \"中雨\" },\n        { label: \"大雨\", value: \"大雨\" },\n        { label: \"雷雨\", value: \"雷雨\" },\n        { label: \"小雪\", value: \"小雪\" },\n        { label: \"中雪\", value: \"中雪\" },\n        { label: \"大雪\", value: \"大雪\" }\n      ],\n      //试验报告记录新增弹窗框标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          symc: \"\",\n          sjzy: \"\",\n          syddid: \"\",\n          symb: \"\",\n          syrqArr: [],\n          syzy: \"\",\n          syxz: \"\",\n          sbmc: \"\",\n          ishg: \"\"\n        },\n        fieldList: [\n          { label: \"试验名称\", type: \"input\", value: \"symc\" },\n          {\n            label: \"设备专业\",\n            type: \"select\",\n            value: \"sjzy\",\n            options: [\n              { label: \"输电设备\", value: \"输电设备\" },\n              { label: \"变电设备\", value: \"变电设备\" },\n              { label: \"配电设备\", value: \"配电设备\" }\n            ],\n            clearable: false\n          },\n          { label: \"设备地点\", type: \"select\", value: \"syddid\", options: [] },\n          { label: \"试验模板名称\", type: \"input\", value: \"symb\" },\n          {\n            label: \"试验日期\",\n            type: \"date\",\n            value: \"syrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"试验专业\", type: \"select\", value: \"syzy\", options: [] },\n          { label: \"试验性质\", type: \"input\", value: \"syxz\" },\n          { label: \"试验设备\", type: \"input\", value: \"sbmc\" },\n          { label: \"流程状态\", type: \"select\", value: \"ishg\", options: [] }\n        ]\n      },\n      //查询报告记录参数\n      queryParam: {\n        pageSize: 10,\n        pageNum: 1\n        // syddid: \"\"\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 0]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"试验专业\", prop: \"syzy\" },\n          { label: \"试验性质\", prop: \"syxz\" },\n          { label: \"试验名称\", prop: \"symc\", minWidth: \"100\" },\n          { label: \"设备地点\", prop: \"sydd\", minWidth: \"120\" },\n          { label: \"试验设备\", prop: \"sbmc\", minWidth: \"200\" },\n          { label: \"试验模板名称\", prop: \"symb\", minWidth: \"120\" },\n          { label: \"天气\", prop: \"tq\" },\n          { label: \"试验日期\", prop: \"syrq\" },\n          { label: \"试验人员\", prop: \"syryid\" },\n          { label: \"流程状态\", prop: \"ztmc\" }\n          // {\n          //   prop: 'operation',\n          //   label: '试验报告',\n          //   minWidth: '120px',\n          //   style: {display: 'block'},\n          //   //操作列固定再右侧\n          //   operation: [\n          //     {name: '填写报告', clickFun: this.saveSybgInfo},\n          //   ]\n          // },\n          // {\n          //   prop: 'operation',\n          //   label: '操作',\n          //   minWidth: '120px',\n          //   style: {display: 'block'},\n          //   //操作列固定再右侧\n          //   fixed: 'right',\n          //   operation: [\n          //     {name: '修改', clickFun: this.updateDetails},\n          //     {name: '详情', clickFun: this.getDetailsInfo},\n          //   ]\n          // },\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      //试验报告记录选中\n      selectRows: [],\n      str: \"\",\n      saveData: {}, //选中填写报告的值\n      rules: {\n        syzybm: [\n          { required: true, message: \"请输入试验专业\", trigger: \"select\" }\n        ],\n        syxzbm: [\n          { required: true, message: \"请输入试验性质\", trigger: \"blur\" }\n        ],\n        sydd: [{ required: true, message: \"请输入设备地点\", trigger: \"blur\" }],\n        // syxzbm: [{ required: true, message: \"请输入设备地点\", trigger: \"blur\" }],\n        symb: [\n          {\n            required: true,\n            message: \"请选择试验模板\",\n            validator: validateSymb,\n            trigger: \"change\"\n          }\n        ],\n        sjzy: [{ required: true, message: \"请选择设备专业\", trigger: \"blur\" }],\n        sbmc: [\n          {\n            required: true,\n            message: \"请选择试验设备\",\n            validator: validateSbmc,\n            trigger: \"change\"\n          }\n        ],\n        symc: [{ required: true, message: \"请输入试验名称\", trigger: \"blur\" }]\n      },\n      jlData: \"\", //结论所在行标识\n      isJlDisabled: false //结论是否不可编辑\n    };\n  },\n\n  mounted() {\n    //获取试验专业下拉框数据\n    this.getOptions();\n    //获取试验单位下拉框\n    this.getOrganizationSelected();\n    //获取数据列表\n    this.getData(this.$route.query);\n    //查询当前登录人是否有试验报告填写权限\n    let params = {\n      groupId: 74,\n      userName: this.$store.getters.name\n    };\n    showButtenJS(params).then(res => {\n      if (res.data.length > 0) {\n        this.showButton = true;\n      }\n    });\n  },\n  methods: {\n    //进度条状态更新\n    progressformate(procent) {\n      return this.inputpdf === 100 ? \"导出完成\" : `${procent} %`;\n    },\n    //获取试验子项目信息\n    async getZxmmpInfo(row) {\n      // 试验报告中获取试验项目模板的symbid\n      let symbid = row.symbid;\n      // 试验项目模板中获取子项目模板数据\n      //rowData\n      let { data: zxmMbData } = await getMouldValue({ symbid: row.symbid });\n      this.rowData = zxmMbData[Object.keys(zxmMbData)[1]][0];\n      //mxData\n      getChildsValue(row).then(result => {\n        let sysj_data = result.data.syxm;\n        this.mxData = sysj_data[Object.keys(sysj_data)[0]];\n        this.isShowMpInfo = true;\n      });\n    },\n    //关闭试验数据对比弹框\n    closeInfoDialog() {\n      this.isShowMpInfo = false;\n    },\n    //搜索框sbddid 选择事件\n    sbddidClick(val, val1) {\n      if (val1 === \"syddmc\") {\n        this.mainData.lx = \"\";\n        this.isShowSysbddDialog = true;\n      }\n    },\n\n    //详情按钮\n    getDetailsInfo(row) {\n      this.title = \"试验报告详情\";\n      //不显示取消确认按钮\n      this.isShow = false;\n      //禁用表单\n      this.isDisabled = true;\n      this.zyisDisabled = true;\n      //打开弹窗\n      this.isShowDetails = true;\n      this.form = { ...row };\n      //调用获取试验性质方法渲染试验性质\n      this.selectSyxzDataBySyzybm1();\n    },\n    //修改按钮\n    updateDetails(row) {\n      this.title = \"试验报告修改\";\n      //显示取消确认按钮\n      this.isShow = true;\n      this.zyisDisabled = true;\n      //禁用表单\n      this.isDisabled = false;\n      //打开弹窗\n      this.isShowDetails = true;\n      this.form = { ...row };\n      //调用获取试验性质方法渲染试验性质\n      this.selectSyxzDataBySyzybm1();\n    },\n    //填写报告\n    async saveSybgInfo(row) {\n      this.form = { ...row };\n      this.clickRow = row;\n      (this.bcDisabled = true), (this.ishg = row.ishg);\n      this.createBy = row.createBy;\n      this.ssdwld = row.ssdwld;\n      this.sckzg = row.sckzg;\n      this.saveData = row;\n      this.basicData = row;\n      this.sybgjlld = row.objId;\n      this.symbid = row.symbid;\n      this.sysbid = row.sysbid;\n      this.isShowDownLoadDialog = true;\n      this.titleName = row.symb;\n      //在填写报告时，请求后台处理数据\n      const that = this;\n      await getMouldValue({ symbid: row.symbid }).then(res => {\n        res = res.data;\n        getChildsValue(row).then(result => {\n          let sbmp_data = result.data.sbmp;\n          let sysj_data = result.data.syxm;\n          let arr = [];\n          for (let item in sbmp_data) {\n            arr.push({ [item]: sbmp_data[item] });\n          }\n          let arr1 = [];\n          for (let val in sysj_data) {\n            arr1.push({ [val]: sysj_data[val] });\n          }\n          that.handleSbmp(res[Object.keys(res)[0]], arr, \"\", \"h2_table\");\n          that.handleSbmp(res[Object.keys(res)[1]], arr1, \"\", \"h3_table\");\n        });\n      });\n      this.form = { ...row };\n    },\n\n    //查看试验报告\n    SybgInfo(row) {\n      this.form = { ...row };\n      this.clickRow = row;\n      this.ishg = row.ishg;\n      this.bzzsp = row.bzzsp;\n      this.bcDisabled = true;\n      this.createBy = row.createBy;\n      this.ssdwld = row.ssdwld;\n      this.sckzg = row.sckzg;\n      this.saveData = row;\n      this.basicData = row;\n      this.sybgjlld = row.objId;\n      this.symbid = row.symbid;\n      this.sysbid = row.sysbid;\n      this.isShowDownLoadDialog = true;\n      this.titleName = row.symb;\n      let gzimg1 = document.getElementById(\"gzimg1\");\n      gzimg1 && gzimg1.remove();\n      let gzimg2 = document.getElementById(\"gzimg2\");\n      gzimg2 && gzimg2.remove();\n      //在填写报告时，请求后台处理数据\n      const that = this;\n      getMouldValue({ symbid: row.symbid }).then(res => {\n        res = res.data;\n        getChildsValue(row).then(result => {\n          let sbmp_data = result.data.sbmp;\n          let sysj_data = result.data.syxm;\n          let arr = [];\n          for (let item in sbmp_data) {\n            arr.push({ [item]: sbmp_data[item] });\n          }\n          let arr1 = [];\n          for (let val in sysj_data) {\n            arr1.push({ [val]: sysj_data[val] });\n          }\n          that.handleSbmp(res[Object.keys(res)[0]], arr, \"\", \"h2_table\");\n          that.handleSbmp(res[Object.keys(res)[1]], arr1, \"\", \"h3_table\");\n        });\n      });\n      setTimeout(async () => {\n        this.showTest();\n      }, 3000);\n    },\n\n    handleSbmp(dataNum, dataArr, str, tableBox, returnString) {\n      for (let k = 0; k < dataNum.length; k++) {\n        var hs = dataNum[k].aHs;\n        var ls = dataNum[k].aLs;\n        let data = dataArr[k];\n        for (var item in data) {\n          // str += \"<tr style='text-align:left;'><th colspan=\"+ ls +\">\"+ item +\"</th></tr>\";\n          for (let i = 0; i < hs; i++) {\n            //有几行就插入几行\n            let temp = \"<tr class='splitClass'>\";\n            for (let j = 0; j < data[item].length; j++) {\n              //循环数据看每行有几列\n              if (i == data[item][j].rowindex) {\n                var nrbs = data[item][j].nrbs;\n                var sjlx = data[item][j].sjlx; //数据类型\n                var objId = data[item][j].objId;\n                var txt = data[item][j].text;\n                var nr = \"\";\n                if (nrbs === \"结论\") {\n                  this.jlData = data[item][j + 1]; //保存下一个单元格内容\n                }\n                if (sjlx == \"STRING\") {\n                  //判断数据类型为string的表示为空格，可编辑\n                  if (!txt) {\n                    txt = \"\";\n                  }\n                  nr =\n                    \"<input type='text' style='border: none;width: 99%;text-align: center;' class=\" +\n                    tableBox +\n                    \" id=\" +\n                    objId +\n                    \" value=\" +\n                    txt +\n                    \">\";\n                } else {\n                  nr = nrbs;\n                }\n                if (data[item][j].colspan != \"1\") {\n                  //判断colspan不为1的话为可编辑的\n                  if (this.jlData && this.jlData.objId === objId) {\n                    //当前单元格是结论所在单元格\n                    if (this.form.ishg == 5 || this.form.ishg == 6) {\n                      //只有班组长审批和历史录入是可编辑的\n                      temp +=\n                        \"<td tabindex='-1' colspan='\" +\n                        data[item][j].colspan +\n                        \"' rowspan='\" +\n                        data[item][j].rowspan +\n                        \"' >\" +\n                        nr +\n                        \"</td>\";\n                      this.isJlDisabled = false;\n                    } else {\n                      //显示原值，不可编辑\n                      temp +=\n                        \"<td tabindex='-1' colspan='\" +\n                        data[item][j].colspan +\n                        \"' rowspan='\" +\n                        data[item][j].rowspan +\n                        \"' id='\" +\n                        objId +\n                        \"' class='_objId'\" +\n                        \" >\" +\n                        txt +\n                        \"</td>\";\n                      this.isJlDisabled = true;\n                    }\n                  } else {\n                    temp +=\n                      \"<td tabindex='-1' colspan='\" +\n                      data[item][j].colspan +\n                      \"' rowspan='\" +\n                      data[item][j].rowspan +\n                      \"' >\" +\n                      nr +\n                      \"</td>\";\n                  }\n                } else {\n                  temp += \"<td tabindex='-1'>\" + nr + \"</td>\";\n                }\n              }\n            }\n            temp += \"</tr>\";\n            str += temp;\n          }\n        }\n      }\n      if (returnString) {\n        return str;\n      }\n      if (tableBox === \"h2_table\") {\n        this.tablebox2 = str;\n      } else if (tableBox === \"h3_table\") {\n        this.tablebox3 = str;\n      } else {\n        this[tableBox] = str;\n      }\n    },\n    //关闭填写报告弹框\n    closeYlDialog() {\n      this.isShowDownLoadDialog = false;\n    },\n\n    dcpdf() {\n      let that = this;\n      if (!that.selectRows.length > 0) {\n        that.$message.warning(\"请先选中需要批量导出的数据\");\n        return;\n      }\n      that.inputpdf = 0;\n      let selectRows = that.selectRows;\n      console.log(selectRows, \"select\");\n      that.selectA = selectRows.map((e, i) => {\n        getMouldValue({ symbid: e.symbid }).then(res => {\n          res = res.data;\n          getChildsValue(e).then(result => {\n            let sbmp_data = result.data.sbmp;\n            let sysj_data = result.data.syxm;\n            let arr = [];\n            for (let item in sbmp_data) {\n              arr.push({ [item]: sbmp_data[item] });\n            }\n            let arr1 = [];\n            for (let val in sysj_data) {\n              arr1.push({ [val]: sysj_data[val] });\n            }\n            e.tablebox2 = that.handleSbmp(\n              res[Object.keys(res)[0]],\n              arr,\n              \"\",\n              \"\",\n              true\n            );\n            e.tablebox3 = that.handleSbmp(\n              res[Object.keys(res)[1]],\n              arr1,\n              \"\",\n              \"\",\n              true\n            );\n            e.sysj = sysj_data;\n          });\n        });\n        // that.inputpdf = ((i + 1 / selectRows.length) * 100).toFixed(2)\n        // console.log(that.inputpdf, 'inputpdf1...');\n        return e;\n      });\n      console.log(that.selectA, \"select\");\n      that.isShowDownLoadDialogA = true;\n      that.inputpdf = 30;\n      // console.log(that.inputpdf, 'inputpdf2...');\n      let length = that.selectA.length;\n      setTimeout(async () => {\n        that.$refs[`tablePdf${length - 1}`] &&\n          that.$refs[`tablePdf${length - 1}`][0].$nextTick(() => {\n            setTimeout(async () => {\n              let pdfall = await Promise.all(\n                that.selectA.map(async (e, i) => {\n                  console.log(e, \"item..\");\n                  let newVar = await that.exportPdf(\n                    `tablePdf${i}`,\n                    true,\n                    `${e.symb}_${i}`,\n                    true,\n                    e.sysj\n                  );\n                  that.shengpdf = ((i + 1 / length) * 100).toFixed(2);\n                  return newVar;\n                })\n              );\n              htmlToPdf.zipChange(\n                pdfall,\n                \"试验报告_\" + new Date().getTime(),\n                (item, i) => {\n                  that.downloadpdf = ((i + 1 / length) * 100).toFixed(2);\n                }\n              );\n              console.log(\"pdf\", \"!!\");\n              that.inputpdf = 100;\n            }, 3000);\n          });\n        that.inputpdf = 60;\n        // console.log('pdf', '@@')\n      }, 3000);\n    },\n    //导出pdf\n    async exportPdf(refsname = \"tablePdf\", tableall, name, flag = false, sysj) {\n      let tablesss = this.$refs[refsname];\n      if (tablesss instanceof Array) {\n        tablesss = tablesss[0];\n      }\n      let element = tablesss.$el;\n\n      element.style.position = \"relative\";\n      var jlinput = \"\";\n      //批量导出标记\n      if (flag) {\n        for (const item in sysj) {\n          for (let i = 0; i < sysj[item].length; i++) {\n            if (sysj[item][i].nrbs === \"结论\") {\n              jlinput = sysj[item][i + 1].text;\n              break;\n            }\n          }\n        }\n      } else {\n        var img1 = \"\";\n        var img2 = \"\";\n        let jlDom = document.getElementById(this.jlData.objId);\n        if (jlDom) {\n          jlinput = this.isJlDisabled ? jlDom.innerText : jlDom.value;\n        }\n      }\n\n      if (/不合格/gi.test(jlinput)) {\n        img1 = \"/image/qualified.png\";\n        img2 = \"/image/test_test.png\";\n      } else if (/合格/gi.test(jlinput)) {\n        img1 = \"/image/unqualified.png\";\n        img2 = \"/image/test_test.png\";\n      }\n      //合格 or 不合格 盖章\n      var imgbox1 = document.createElement(\"div\");\n      imgbox1.style.position = \"absolute\";\n      imgbox1.id = \"gzimg1\";\n      imgbox1.style.bottom = \"0\";\n      imgbox1.style.right = \"0\";\n      imgbox1.style.top = \"0\";\n      imgbox1.style.marginRight = \"10px\";\n      imgbox1.style.marginTop = \"90px\";\n      var imgDocument1 = document.createElement(\"img\");\n      imgDocument1.setAttribute(\"src\", img1);\n      imgbox1.appendChild(imgDocument1);\n      element.appendChild(imgbox1);\n      //盖公章\n      var imgbox2 = document.createElement(\"div\");\n      imgbox2.style.position = \"absolute\";\n      imgbox2.id = \"gzimg2\";\n      imgbox2.style.bottom = \"0\";\n      imgbox2.style.right = \"0\";\n      imgbox2.style.top = \"0\";\n      imgbox2.style.marginRight = \"300px\";\n      imgbox2.style.marginTop = \"10px\";\n      var imgDocument2 = document.createElement(\"img\");\n      imgDocument2.setAttribute(\"src\", img2);\n      imgbox2.appendChild(imgDocument2);\n      element.appendChild(imgbox2);\n\n      await htmlToPdf.outPutPdfFn(element, \"splitClass\");\n      let pdf = await htmlToPdf.downloadPDF(element, name || this.titleName);\n      if (tableall) {\n        return pdf;\n      }\n      pdf.pdf.save(pdf.name);\n      this.closeYlDialog();\n      // 恢复原表格样式\n      let gzimg1 = document.getElementById(\"gzimg1\");\n      gzimg1 && gzimg1.remove();\n      let gzimg2 = document.getElementById(\"gzimg2\");\n      gzimg2 && gzimg2.remove();\n    },\n    showTest() {\n      let tablesss = this.$refs[\"tablePdf\"];\n      if (tablesss instanceof Array) {\n        tablesss = tablesss[0];\n      }\n      let element = tablesss.$el;\n\n      element.style.position = \"relative\";\n      var jlinput = \"\";\n      var img1 = \"\";\n      var img2 = \"\";\n      let jlDom = document.getElementById(this.jlData.objId);\n      if (jlDom) {\n        jlinput = this.isJlDisabled ? jlDom.innerText : jlDom.value;\n      }\n      if (/不合格/gi.test(jlinput)) {\n        img1 = \"/image/qualified.png\";\n        img2 = \"/image/test_test.png\";\n      } else if (/合格/gi.test(jlinput)) {\n        img1 = \"/image/unqualified.png\";\n        img2 = \"/image/test_test.png\";\n      } else {\n        return false;\n      }\n      //合格 or 不合格 盖章\n      var imgbox1 = document.createElement(\"div\");\n      imgbox1.style.position = \"absolute\";\n      imgbox1.id = \"gzimg1\";\n      imgbox1.style.bottom = \"0\";\n      imgbox1.style.right = \"0\";\n      imgbox1.style.top = \"0\";\n      imgbox1.style.marginRight = \"10px\";\n      imgbox1.style.marginTop = \"90px\";\n      var imgDocument1 = document.createElement(\"img\");\n      imgDocument1.setAttribute(\"src\", img1);\n      imgbox1.appendChild(imgDocument1);\n      element.appendChild(imgbox1);\n      //盖公章\n      var imgbox2 = document.createElement(\"div\");\n      imgbox2.style.position = \"absolute\";\n      imgbox2.id = \"gzimg2\";\n      imgbox2.style.bottom = \"0\";\n      imgbox2.style.right = \"0\";\n      imgbox2.style.top = \"0\";\n      imgbox2.style.marginRight = \"300px\";\n      imgbox2.style.marginTop = \"10px\";\n      var imgDocument2 = document.createElement(\"img\");\n      imgDocument2.setAttribute(\"src\", img2);\n      imgbox2.appendChild(imgDocument2);\n      element.appendChild(imgbox2);\n    },\n    //保存填写报告数据\n    saveYlDialog() {\n      this.$nextTick(() => {\n        // 以服务的方式调用的 Loading 需要异步关闭\n        this.loading = Loading.service({\n          lock: true, //lock的修改符--默认是false\n          text: \"流程进行中，请稍后\", //显示在加载图标下方的加载文案\n          spinner: \"el-icon-loading\", //自定义加载图标类名\n          background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n          target: document.querySelector(\"#dialogActst\")\n        });\n      });\n      //调用子组件中的方法，获取单元格的值\n      let params = this.$refs.tablePdf.getParams();\n      saveChildsValue({\n        symbid: this.saveData.symbid,\n        objId: this.saveData.objId,\n        params: params\n      }).then(res => {\n        if (res.code === \"0000\") {\n          this.$message({\n            type: \"success\",\n            message: \"保存成功!\"\n          });\n        }\n      });\n      //更新试验人员\n      updateSybgjl(this.clickRow).then(res => {\n        if (res.code == \"0000\") {\n          this.$nextTick(() => {\n            // 以服务的方式调用的 Loading 需要异步关闭\n            this.loading.close();\n          });\n          this.$message.success(\"操作成功！\");\n          this.isShowDetails = false;\n          this.isShowDownLoadDialog = false;\n          this.getData();\n        }\n      });\n    },\n    //报告记录列表查询\n    async getData(params) {\n      try {\n        this.queryParam = { ...this.queryParam, ...params };\n        this.$refs.sybglr.loading = true;\n        const param = { ...this.queryParam, ...params };\n        const { data, code } = await getSybgjlDataByPage(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          this.$nextTick(() => {\n            // 以服务的方式调用的 Loading 需要异步关闭\n            this.$refs.sybglr.loading = false;\n          });\n        }\n      } catch (e) {\n        this.$nextTick(() => {\n          // 以服务的方式调用的 Loading 需要异步关闭\n          this.$refs.sybglr.loading = false;\n        });\n      }\n    },\n    //试验模板点击事件\n    symbSelectedClick() {\n      if (this.form.sbmc === \"\" || this.form.sbmc === undefined) {\n        this.$message.warning(\"请选择试验设备\");\n        return;\n      }\n      this.isShowSymbDialog = true;\n    },\n    //试验模板接收数据\n    handleAcceptMbData(mbData) {\n      //模板名称\n      // this.form.symb = mbData.mbmc;\n      console.log(\"mbData\", mbData);\n      this.$set(this.form, \"symb\", mbData.mbmc);\n      //模板id\n      this.form.symbid = mbData.objId;\n    },\n    //关闭模板弹窗\n    handleControlSymbSelectDialog(isShow) {\n      this.isShowSymbDialog = isShow;\n    },\n    //组件接受设备参数数据\n    handleAcceptSbData(sbData) {\n      console.log(\"sbData\", sbData);\n      let sbmcS = [];\n      let sysbS = [];\n      sbData.forEach(item => {\n        sbmcS.push(item.sbmc);\n        sysbS.push(item.objId);\n      });\n      this.form.sbmc = sbmcS.join(\",\");\n      this.form.sysbid = sysbS.join(\",\");\n      this.symbData.sblxid = sbData[0].sblxbm;\n      this.form.yxbh = sbData[0].wzmc;\n      this.form.tyrq = sbData[0].tyrq;\n      // this.form.sbmc = sbData.sbmc;\n      // this.form.sysbid = sbData.objId;\n      // console.log(\"sbData\",sbData);\n      // //设备类型编码\n      // //线路 sblxbm sblxbm  sblxbm\n      // this.symbData.sblxid=sbData.sblxbm;\n    },\n    //控制关闭试验设备弹出框\n    handleControlSysbSelectDialog(isShow) {\n      this.isShowSysbDialog = isShow;\n    },\n    //获取试验单位数据\n    getOrganizationSelected() {\n      let parentId = \"1001\";\n      getOrganizationSelected({ parentId: parentId }).then(res => {\n        res.data.forEach((item, index) => {\n          if (item.value === 4002) {\n            let sydw = {};\n            sydw.label = item.label;\n            sydw.value = item.value;\n            this.sydwList.push(sydw);\n          }\n        });\n      });\n    },\n    //接受试验设备地点树结构数据\n    handleAccessTreeData(treeNode) {\n      console.log(\"treeNode\", treeNode);\n      if (this.mainData.lx === \"\") {\n        console.log(\"this.mainData.lx\", \"123\");\n        let syddmc = document.getElementsByName(\"syddmc\");\n        console.log(\"syddmc\", syddmc);\n        if (syddmc) {\n          syddmc[0].value = treeNode.label;\n          this.queryParam.syddid = treeNode.id;\n        }\n      }\n      //给表单设备地点赋值\n      this.form.sydd = treeNode.label;\n      this.basicData.sydd = treeNode.label;\n      //数据库存储值\n      this.form.syddid = treeNode.id;\n      this.$set(this.form, \"sbmc\", \"\");\n    },\n    //子组件关闭试验设备地点弹窗\n    handleCloseSyddDialog(syddDialogControl) {\n      this.isShowSysbddDialog = syddDialogControl;\n    },\n    //获取试验专业下拉框数据\n    getOptions() {\n      getSyzySelectedOptions().then(res => {\n        this.syzyList = res.data;\n        this.filterInfo.fieldList.forEach(item => {\n          if (item.value == \"syzy\") {\n            item.options = this.syzyList;\n          }\n        });\n      });\n      getDictTypeData(\"sybg_ztcl\").then(res => {\n        this.filterInfo.fieldList.forEach(item => {\n          if (item.value == \"ishg\") {\n            item.options = res.data.map(e => {\n              e.value = e.numvalue\n              return e\n            });\n          }\n        });\n      });\n    },\n    //根据试验专业编码获取试验性质信息\n    selectSyxzDataBySyzybm() {\n      //change事件发生改变时，清空之前试验性质\n      this.$set(this.form, \"syxzbm\", \"\");\n      this.syxzList = [];\n      let syzybm = this.form.syzybm;\n      selectSyxzDataBySyzybm({ syzybm }).then(response => {\n        this.syxzList = response.data;\n      });\n    },\n    //点击修改详情按钮时回显试验性质时使用\n    selectSyxzDataBySyzybm1() {\n      this.syxzList = [];\n      let syzybm = this.form.syzybm;\n      selectSyxzDataBySyzybm({ syzybm }).then(response => {\n        this.syxzList = response.data;\n      });\n    },\n\n    //设备专业发生变化时，清空设备地点数据\n    async selectsbzy(val) {\n      this.$set(this.form, \"sydd\", \"\");\n      let sbddall = [];\n      this.sbddList = [];\n      switch (val) {\n        case \"变电设备\":\n          sbddall = await getbdzList({});\n          sbddall.forEach((item, index) => {\n            let bdz = {};\n            bdz.label = item.bdzmc;\n            bdz.value = item.objId;\n            this.sbddList.push(bdz);\n          });\n          break;\n        case \"输电设备\":\n          let { data } = await getSdLineSelected({});\n          this.sbddList = data;\n          break;\n        case \"配电设备\":\n          await this.getPdsSelected();\n          break;\n      }\n    },\n\n    onclckdd(val) {\n      console.log(val);\n      this.$set(this.form, \"sydd\", val.label);\n      this.$set(this.form, \"syddid\", val.value);\n    },\n\n    //获取配电室下拉数据\n    async getPdsSelected() {\n      this.sbddList = [];\n      await getPdsSelected({}).then(res => {\n        this.sbddList = res.data;\n      });\n    },\n\n    //设备地点发生变化时，清空试验设备的数据\n    changeInput() {\n      console.log(\"123123\");\n      this.$set(this.form, \"sbmc\", \"\");\n    },\n\n    //试验设备地点点击事件\n    sysbddClick() {\n      if (this.form.sjzy === \"\" || this.form.sjzy === undefined) {\n        this.$message.warning(\"请选择设备专业\");\n        return;\n      }\n      if (this.form.sjzy === \"变电设备\") {\n        this.mainData.lx = \"2\";\n      } else if (this.form.sjzy === \"配电设备\") {\n        this.mainData.lx = \"3\";\n      } else if (this.form.sjzy === \"输电设备\") {\n        this.mainData.lx = \"1\";\n      }\n      this.isShowSysbddDialog = true;\n    },\n    //试验设备选择事件\n    sysbSelectedClick() {\n      //试验设备发生变化时，清空试验模板得数据\n      this.$set(this.form, \"symb\", \"\");\n      if (this.form.sjzy === \"\" || this.form.sjzy === undefined) {\n        this.$message.warning(\"请选择设备专业\");\n        return;\n      }\n      if (this.form.sydd === \"\" || this.form.sydd === undefined) {\n        this.$message.warning(\"请选择设备地点\");\n        return;\n      }\n      if (this.form.sjzy === \"变电设备\") {\n        this.selectedSbParam.lx = \"bd\";\n        this.selectedSbParam.fsss = \"附属\";\n      } else if (this.form.sjzy === \"配电设备\") {\n        this.selectedSbParam.lx = \"pd\";\n      } else if (this.form.sjzy === \"输电设备\") {\n        this.selectedSbParam.lx = \"sd\";\n      }\n      this.selectedSbParam.sbmc = this.form.sydd; //所属位置\n      this.isShowSysbDialog = true;\n    },\n    //重置按钮\n    getReset(data) {\n      //不可以清空设备专业\n      // let syddmc = document.getElementsByName(\"syddmc\");\n      // console.log(\"syddmc\", syddmc);\n      // if (syddmc) {\n      //   syddmc[0].value = \"\";\n      // }\n      this.queryParam = {};\n    },\n    //下拉框change事件\n    async handleEvent(val, val1) {\n      //变电站查询下拉框选项根据所选分公司带出来\n      if (val.label === \"sjzy\" && val.value && val.value !== \"\") {\n        this.$set(val1, \"syddid\", \"\");\n        await this.selectsbzy(val.value);\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"syddid\") {\n            console.log(\"options_syddid\", this.sbddList);\n            return (item.options = this.sbddList);\n          }\n        });\n      }\n    },\n    //新增按钮\n    getInsert() {\n      //清空试验性质数据\n      this.syxzList = [];\n      this.title = \"新建试验报告\";\n      //表单不禁用\n      this.isDisabled = false;\n      //展示取消确认按钮\n      this.isShowDetails = true;\n      this.isShow = true;\n\n      //清空表单\n      this.form = {};\n      this.form.ishg = 1;\n    },\n\n    initFormData() {\n\n    },\n\n    //   handleSelectionChange(selection) {\n    //   this.selectData = selection\n    // },\n    //历史数据录入\n    getInsertls() {\n      //清空试验性质数据\n      this.syxzList = [];\n      this.title = \"新建试验报告\";\n      //表单不禁用\n      this.isDisabled = false;\n      this.zyisDisabled = true;\n      //展示取消确认按钮\n      this.isShowDetails = true;\n      this.isShow = true;\n      this.syryDialog = false;\n      //清空表单\n      this.form = {};\n      this.form.ishg = 6;\n    },\n\n    //删除按钮\n    deleteRow(objId) {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          removeSybgjlData(JSON.stringify(objId)).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    //表格多选框\n    handelSelectChange(rows) {\n      this.selectRows = rows;\n      console.log(this.selectRows, \"123\");\n    },\n    //取消按钮\n    close() {\n      this.isShowDetails = false;\n    },\n    //保存按钮\n    save() {\n      this.$refs.form.validate(valid => {\n        if (valid) {\n          saveOrUpdateSybgjl(this.form).then(res => {\n            if (res.code == \"0000\") {\n              this.$message.success(\"操作成功！\");\n              this.isShowDetails = false;\n              this.getData();\n            }\n          });\n        }\n      });\n    },\n\n    //上报、提交审核\n    async commitSh() {\n      let type = \"complete\";\n      if (this.ishg == 1) {\n        //上报\n        //当前状态为新建，下一环节班组长审批\n        this.processData.variables.ishg = 5;\n        this.processData.variables.title = \"班组长审批\";\n        this.processData.defaultFrom = true;\n        this.processData.jxgs = false;\n      } else if (this.ishg == 5) {\n        //当前状态为班组长审批，下一环节分公司审核\n        this.processData.variables.ishg = 2;\n        this.processData.variables.title = \"分公司审核\";\n        this.processData.defaultFrom = true;\n        this.processData.jxgs = false;\n      } else if (this.ishg == 2) {\n        //当前状态为分公司审核，下一环节属地单位审核\n        this.processData.variables.ishg = 7;\n        this.processData.variables.title = \"属地单位审核\";\n        this.processData.defaultFrom = true;\n        this.processData.jxgs = false;\n        type = \"completeMany\"; //一发多收\n      } else if (this.ishg == 7) {\n        this.processData.variables.ishg = 3;\n        this.processData.variables.title = \"生产科专工审核\";\n        this.processData.defaultFrom = true;\n        this.processData.jxgs = false;\n      } else if (this.ishg == 3) {\n        this.processData.variables.ishg = 3;\n        this.processData.variables.title = \"合格验收\";\n        this.processData.defaultFrom = false;\n        this.processData.jxgs = false;\n      } else if (this.ishg == 4) {\n        //当前状态关闭\n        this.$message.warning(\"当前流程已关闭！！无法提交审核\");\n        return;\n      }\n      console.log(\"this.clickRow\", this.clickRow);\n      //开始执行请求\n      this.getSbFsBj(\n        { data: this.clickRow, type: type },\n        {\n          defaultForm: this.processData.defaultFrom,\n          jxgs: this.processData.jxgs\n        }\n      );\n      //详情框关闭\n      this.closeYlDialog();\n      //恢复分页\n      this.tableAndPageInfo.pager.pageResize = \"Y\";\n      //重新查数据\n      await this.getData();\n    },\n    //流程图片查看\n    showProcessImg(row) {\n      this.openLoadingImg = true;\n      this.imgSrc =\n        \"/activiti-api/process/read-resource?processDefinitionKey=sybgsh&businessKey=\" +\n        row.objId +\n        \"&t=\" +\n        new Date().getTime();\n    },\n\n    //上报发送办结\n    getSbFsBj(args, isShow) {\n      let row = { ...args.data };\n      this.activitiOption.title = args.title;\n      this.processData.variables.zy = row.sjzy;\n      if (args.type === \"complete\") {\n        this.processData.defaultFrom = isShow.defaultForm; //是否显示选人员\n        this.processData.processType = \"complete\";\n        this.processData.jxgs = isShow.jxgs;\n        this.processData.businessKey = row.objId;\n        this.processData.variables.pass = true;\n      } else if (args.type === \"completeMany\") {\n        //一发多收\n        this.processData.defaultFrom = isShow.defaultForm; //是否显示选人员\n        this.processData.processType = \"completeMany\";\n        this.processData.jxgs = isShow.jxgs;\n        this.processData.businessKey = row.objId;\n        this.processData.variables.pass = true;\n      } else {\n        this.processData.variables.title = \"回退原因提报\";\n        this.processData.defaultFrom = true;\n        this.processData.processType = \"rollback\";\n        this.processData.businessKey = row.objId;\n        this.processData.variables.pass = false;\n      }\n      this.isShowActiviti = true;\n    },\n    //回退按钮\n    handleTh(type) {\n      this.getSbFsBj(\n        { type: type, data: this.clickRow },\n        { defaultForm: false }\n      );\n    },\n\n    async showTimeLine(row) {\n      let { code, data } = await HistoryList({ businessKey: row.objId });\n      this.timeData = data;\n      this.timeLineShow = true;\n    },\n\n    //关闭工作流弹窗\n    closeActiviti() {\n      this.isShowActiviti = false;\n    },\n\n    //关闭时间线查看页面\n    colseTimeLine() {\n      this.timeLineShow = false;\n    },\n\n    //工作流回传数据\n    async todoResult(data) {\n      console.log(\"this.data\", data);\n      switch (data.activeTaskName) {\n        case \"试验报告填写\":\n          this.ishg = 1;\n          break;\n        case \"领导审批\":\n          this.ishg = 2;\n          break;\n        case \"属地单位审批\":\n          this.ishg = 7;\n          break;\n        case \"生产科专工审批\":\n          this.ishg = 3;\n          break;\n        case \"结束\":\n          this.ishg = 4;\n          break;\n        case \"班组审批\":\n          this.ishg = 5;\n          break;\n      }\n      let row = {};\n      if (this.ishg == 2) {\n        row = {\n          objId: data.businessKey,\n          ishg: this.ishg,\n          ssdwld: data.nextUser,\n          bgrq: formatterDateTime(new Date(), \"yyyy-MM-dd\") //设置报告日期字段，为批准人审批时间\n        };\n      } else if (this.ishg == 3) {\n        row = {\n          objId: data.businessKey,\n          ishg: this.ishg,\n          sckzg: data.nextUser\n        };\n      } else if (this.ishg == 5) {\n        row = {\n          objId: data.businessKey,\n          ishg: this.ishg,\n          bzzsp: data.nextUserNickName\n        };\n      } else if (this.ishg == 7) {\n        row = {\n          objId: data.businessKey,\n          ishg: this.ishg,\n          sddwshr: data.nextUser\n        };\n      } else {\n        row = { objId: data.businessKey, ishg: this.ishg };\n      }\n      console.log(\"row\", row);\n      updateSybgjl(row).then(res => {\n        if (res.code === \"0000\") {\n          this.$message.success(\"操作成功\");\n          //重置page页从1开始\n          this.tableAndPageInfo.pager.pageResize = \"Y\";\n          this.getData();\n        }\n      });\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.button-group {\n  padding-left: 30px;\n  padding-right: 30px;\n  display: flex;\n  justify-content: flex-end;\n}\n\n// #h2_table,\n// #h3_table {\n//   border-bottom: 1px solid #000;\n//   tr {\n//     //td:nth-child(1){\n//     //  border-left:none;\n//     //}\n//     td {\n//       border-left: 1px solid #000;\n//       border-top: 1px solid #000\n//     }\n//   }\n//   text-align: center;\n// }\n\n// #h1_table {\n//   width: 100%;\n//   text-align: center;\n//   //border-right:1px solid #000;\n//   border-bottom: 1px solid #000;\n\n//   tr {\n//     line-height: 35px;\n\n//     //td:nth-child(1){\n//     //  border-left:none;\n//     //}\n//     td {\n//       border-left: 1px solid #000;\n//       border-top: 1px solid #000\n//     }\n//   }\n// }\n\n// #saveCont {\n//   border: 1px solid #000;\n//   width: 100%;\n//   height: 60vh;\n//   overflow: auto;\n// }\n\n// .printTitle {\n//   line-height: 35px;\n// }\n\n// /deep/ #h2_table tr,\n// /deep/ #h3_table tr {\n//   height: 35px;\n// }\n\n// /deep/ #h2_table td,\n// /deep/ #h3_table td {\n//   border: 1px solid #000\n// }\n\n// /deep/ #h2_table input,\n// /deep/ #h3_table input {\n//   display: inline-block;\n//   height: 35px;\n// }\n</style>\n"]}]}