{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\activiti_czp\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\activiti_czp\\index.vue", "mtime": 1706897320815}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/activiti_czp", "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      id=\"dialogAct\"\n      class=\"tree\"\n      :title=\"option.title ? option.title : ''\"\n      :top=\"option.top ? option.top : '10vh'\"\n      v-dialogDrag\n      :visible.sync=\"isShow\"\n      :width=\"option.width ? option.width : '25%'\"\n      :close-on-click-modal=\"false\"\n      @close=\"toClose\"\n      :destroy-on-colse=\"true\"\n      :modal-append-to-body=\"false\"\n    >\n      <slot name=\"Dialog_content\" />\n      <el-form\n        label-width=\"120px\"\n        ref=\"form\"\n        :model=\"form\"\n        v-if=\"datas.defaultFrom\"\n      >\n        <div>\n          <el-row>\n            <div v-if=\"datas.processType === 'complete'\">\n              <el-col :span=\"24\" v-if=\"datas.nextUser ? datas.nextUser : true\">\n                <el-form-item prop=\"nextUser\" :label=\"datas.rylx\">\n                  <el-select\n                    v-model=\"form.nextUser\"\n                    placeholder=\"请选择人员\"\n                    style=\"width: 100%;\"\n                    value-key=\"userName\"\n                    clearable\n                    filterable\n                  >\n                    <el-option\n                      v-for=\"item in gzfzrOptions\"\n                      :key=\"item.userName\"\n                      :label=\"item.nickName\"\n                      :value=\"{\n                        userName: item.userName,\n                        nickName: item.nickName\n                      }\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </div>\n            <el-col :span=\"24\">\n              <el-form-item\n                prop=\"comment\"\n                label=\"回退原因：\"\n                v-if=\"datas.processType === 'rollback'\"\n              >\n                <el-input\n                  style=\"width: 100%\"\n                  v-model=\"form.comment\"\n                  type=\"textarea\"\n                  placeholder=\"请输入原因\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n      </el-form>\n      <span v-else>请确定是否提交</span>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"info\" @click=\"toClose\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"todoSubmit('form')\">确定</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { completeTask } from \"@/api/activiti/processTask\";\nimport { Loading } from \"element-ui\";\nimport { getUsers } from \"@/api/system/usergroup\";\nexport default {\n  name: \"index\",\n  props: {\n    /*流程发起必填数据\n        processData:{\n          processDefinitionKey:\"流程定义的key\",//必填\n          taskId:\"任务id，如果任务在代办列表时，会根据代办列表获取taskid，否则需传入业务id来确定task\",\n          businessKey:\"业务id，对应业务的主键\",//taskid和businessKey两者必须有其中一个才可以确定一个task\n          businessType:\"业务类型，用于区分不同的业务\"//必填\n          variables:\"拓展参数\"//流程定义中设置的参数,\n          nextUser:\"如果流程实例中并未配置每一个节点的处理人，则需要用户手动选择每一个节点的处理人\",\n          processType:'complete,rollback',\n          defaultFrom:true,false 是否需要默认表单\n        }*/\n    processData: {\n      type: Object,\n      default() {\n        return {\n          defaultFrom: true,\n          processType: \"complete\"\n        };\n      }\n    },\n    //显示隐藏\n    isShow: {\n      type: Boolean,\n      default: false\n    },\n    //子组件默认参数\n    option: {\n      type: Object,\n      default() {\n        return { title: \"审批\" };\n      }\n    }\n  },\n  data() {\n    return {\n      disabled: false,\n      form: {},\n      datas: {},\n      loading: null,\n      gzfzrOptions: []\n    };\n  },\n  watch: {\n    processData: {\n      handler(newVal, oldVal) {\n        this.$nextTick(() => {\n          this.datas = { ...newVal };\n          if (this.datas.personGroupId) {\n            getUsers({\n              personGroupId: this.datas.personGroupId,\n              deptId: this.datas.dw,\n              deptName: \"\"\n            }).then(res => {\n              this.gzfzrOptions = res.data;\n            });\n          }\n        });\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  mounted() {},\n  methods: {\n    //流程提交，流程拒绝\n    async todoSubmit() {\n      if (!this.datas.routePath) {\n        this.datas.routePath = this.$route.path;\n      }\n      if (this.form.nextUser) {\n        this.datas.nextUser = this.form.nextUser.userName;\n        this.datas.nextUserNickName = this.form.nextUser.nickName;\n      } else {\n        this.datas.nextUser = undefined;\n        this.datas.nextUserNickName = undefined;\n      }\n\n      if (\n        !this.datas.nextUser &&\n        this.datas.processType === \"complete\" &&\n        this.datas.defaultFrom\n      ) {\n        this.$message({\n          type: \"error\",\n          message: \"请选择人员!\"\n        });\n        return;\n      }\n      if (this.form.comment) {\n        this.datas.variables.comment = this.form.comment;\n      }\n      this.$nextTick(() => {\n        // 以服务的方式调用的 Loading 需要异步关闭\n        this.loading = Loading.service({\n          lock: true, //lock的修改符--默认是false\n          text: \"流程进行中，请稍后\", //显示在加载图标下方的加载文案\n          spinner: \"el-icon-loading\", //自定义加载图标类名\n          background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n          target: document.querySelector(\"#dialogAct\")\n        });\n      });\n      let resultData;\n      try {\n        let { code, data } = await completeTask(this.datas);\n        if (code === \"0000\") {\n          resultData = data;\n        }\n        if (code) {\n          this.$nextTick(() => {\n            // 以服务的方式调用的 Loading 需要异步关闭\n            this.loading.close();\n          });\n        }\n      } catch (e) {\n        this.$nextTick(() => {\n          // 以服务的方式调用的 Loading 需要异步关闭\n          this.loading.close();\n        });\n      }\n      if (resultData) {\n        resultData.processType = this.datas.processType;\n        this.$emit(\"todoData\", resultData);\n      }\n      this.toClose();\n    },\n    toClose() {\n      this.$set(this.form,'nextUser',\"\")\n      this.$set(this.form,'comment',\"\")\n      this.$emit(\"toClose\", \"close\");\n    }\n  }\n};\n</script>\n\n<style scoped></style>\n"]}]}