<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qz.mapper.asset.AssetBdMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qz.entity.asset.AssetBd">
        <id column="obj_id" property="objId"/>
        <result column="ssbdz" property="ssbdz"/>
        <result column="sbmc" property="sbmc"/>
        <result column="sbdm" property="sbdm"/>
        <result column="ddsbh" property="ddsbh"/>
        <result column="dydjbm" property="dydjbm"/>
        <result column="dydj" property="dydj"/>
        <result column="tyrq" property="tyrq"/>
        <result column="ssjg" property="ssjg"/>
        <result column="xb" property="xb"/>
        <result column="xs" property="xs"/>
        <result column="ccrq" property="ccrq"/>
        <result column="azwz" property="azwz"/>
        <result column="yt" property="yt"/>
        <result column="xh" property="xh"/>
        <result column="cpdh" property="cpdh"/>
        <result column="eddy" property="eddy"/>
        <result column="edpl" property="edpl"/>
        <result column="sbzt" property="sbzt"/>
        <result column="syhj" property="syhj"/>
        <result column="sccj" property="sccj"/>
        <result column="zzgj" property="zzgj"/>
        <result column="zhsblx" property="zhsblx"/>
        <result column="zhsblxbm" property="zhsblxbm"/>
        <result column="eddl" property="eddl"/>
        <result column="yxbh" property="yxbh"/>
        <result column="ccbh" property="ccbh"/>
        <result column="create_time" property="createTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="bz" property="bz"/>
        <result column="siteid" property="siteid"/>
        <result column="asset_type_name" property="assetTypeName"/>
        <result column="asset_type_code" property="assetTypeCode"/>
        <result column="ssgs" property="ssgs"/>
        <result column="deptname" property="deptname"/>
        <result column="bdzmc" property="bdzmc"/>
        <result column="wzmc" property="wzmc"/>
        <result column="sblxmc" property="sblxmc"/>
        <result column="dydjName" property="dydjName"/>
        <result column="bbh" property="bbh"/>

        <result column="sblxbm" property="sblxbm"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        asset.obj_id, asset.ssbdz, asset.sbmc, asset.sbdm, asset.ddsbh, asset.dydjbm, asset.dydj, asset.tyrq, asset.ssjg, asset.xb, asset.xs,
         asset.ccrq, asset.azwz, asset.yt, asset.cpdh, asset.eddy, asset.edpl, asset.sbzt, asset.syhj, asset.sccj, asset.zzgj, asset.zhsblx,
         asset.zhsblxbm, asset.eddl, asset.yxbh, asset.ccbh, asset.create_time, asset.create_by,asset.update_time, asset.update_by,
         asset.bz, asset.siteid,asset.asset_type_name,asset.asset_type_code,asset.ssgs,edrl,ljzbh,qszl,yzl,dwgs,kzsh,kzdl,fzsh,lqfs,dlkz,jddj1,jddj2,jddj3,jddj4,bbh,
        </sql>

    <select id="getBdAsesetListPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        asset.xh as  `ggxh`,
        assetType.sblx as `sblxmc`,
        assetType.sblxbm as `sblxbm`,
        max_sys_dict_data.dict_label as `dydjName`,
        dept.deptname,
        bdz.bdzmc,
        jg.jgmc as wzmc
        from asset_bd asset
        LEFT JOIN locations_bdz bdz on asset.ssbdz = bdz.sbdm
        left join locations_jg jg on asset.ssjg = jg.wzbm
        LEFT JOIN mwt_ud_gy_sblx assetType on asset.asset_type_code = assetType.SBLXBM
        left join qz_isc_dgyt.max_sys_dept dept on asset.ssgs = dept.deptid
        left join qz_isc_dgyt.max_sys_dict_data on asset.dydjbm = qz_isc_dgyt.max_sys_dict_data.dict_value  AND qz_isc_dgyt.max_sys_dict_data.dict_type = 'dg_dydj'
        <where>
            <if test="assetBd.ssgs!=null and assetBd.ssgs!= '' ">
                and asset.ssgs=#{assetBd.ssgs}
            </if>
            <if test="assetBd.ssbdz!=null and assetBd.ssbdz!= ''">
                and asset.ssbdz=#{assetBd.ssbdz}
            </if>
            <if test="assetBd.fsss!=null and assetBd.fsss!= ''">
                and assetType.sblx != "附属设施"
            </if>
            <if test="assetBd.ssjg!=null and assetBd.ssjg!= ''">
                and asset.ssjg like concat ('%',#{assetBd.ssjg},'%')
            </if>
            <if test="assetBd.sbmc!=null and assetBd.sbmc!= ''">
                and asset.sbmc like concat('%', #{assetBd.sbmc},'%')
            </if>
            <if test="assetBd.assetTypeCode!=null and assetBd.assetTypeCode!= ''">
                and asset.asset_type_code=#{assetBd.assetTypeCode}
            </if>
            <if test="assetBd.sbzt!=null and assetBd.sbzt!= ''">
                and asset.sbzt=#{assetBd.sbzt}
            </if>
            <if test="assetBd.dydjbm!=null and assetBd.dydjbm!= ''">
                and asset.dydjbm=#{assetBd.dydjbm}
            </if>
            <if test="assetBd.deptname!=null and assetBd.deptname!= ''">
                and dept.deptname=#{assetBd.deptname}
            </if>
            <if test="assetBd.bdzmc!=null and assetBd.bdzmc!= ''">
                and bdz.bdzmc like concat ('%',#{assetBd.bdzmc},'%')
            </if>
            <if test="assetBd.wzmc!=null and assetBd.wzmc!= ''">
                and jg.jgmc=#{assetBd.wzmc}
            </if>
            <if test="assetBd.sblxmc!=null and assetBd.sblxmc!= ''">
                and assetType.sblx like concat ('%',#{assetBd.sblxmc},'%')
            </if>
            <if test="assetBd.sblxbm!=null and assetBd.sblxbm!= ''">
                and assetType.sblxbm like concat ('%',#{assetBd.sblxbm},'%')
            </if>
            <if test="assetBd.dydjName!=null and assetBd.dydjName!= ''">
                and max_sys_dict_data.dict_label=#{assetBd.dydjName}
            </if>
            <if test="assetBd.xh!=null and assetBd.xh!= ''">
                and asset.xh like concat ('%',#{assetBd.xh},'%')
            </if>
            <if test="assetBd.eddy!=null and assetBd.eddy!= ''">
                and asset.eddy=#{assetBd.eddy}
            </if>
            <if test="assetBd.eddl!=null and assetBd.eddl!= ''">
                and asset.eddl=#{assetBd.eddl}
            </if>
            <if test="assetBd.edpl!=null and assetBd.edpl!= ''">
                and asset.edpl=#{assetBd.edpl}
            </if>
            <if test="assetBd.sccj!=null and assetBd.sccj!= ''">
                and asset.sccj like concat ('%',#{assetBd.sccj},'%')
            </if>
            <if test="assetBd.ggxh!=null and assetBd.ggxh!= ''">
                and asset.xh like concat ('%',#{assetBd.ggxh},'%')
            </if>
            <if test="assetBd.tyBeginDate != null and assetBd.tyBeginDate != ''">
                and asset.tyrq &gt;= #{assetBd.tyBeginDate}
            </if>
            <if test="assetBd.tyEndDate != null and assetBd.tyEndDate != ''">
                and asset.tyrq &lt;= #{assetBd.tyEndDate}
            </if>
        </where>
        ORDER BY assetType.sblxbm
    </select>
    <!--获取变电站下拉框数据列表-->
    <select id="getBdzDataListSelected" resultType="java.util.Map">
        select sbdm as `value`,
        bdzmc as `label`
        from locations_bdz
        <where>
            <if test="locationsBdz.ssdwbm!=null and locationsBdz.ssdwbm!= ''">
                and ssdwbm=#{locationsBdz.ssdwbm}
            </if>
            <if test="locationsBdz.sbdm!=null and locationsBdz.sbdm!= ''">
                and sbdm=#{locationsBdz.sbdm}
            </if>
        </where>
        group by sbdm,bdzmc
    </select>
    <select id="getMapBdzList" resultType="java.util.Map">
        select obj_id as `value`,
        bdzmc as `label`
        from locations_bdz
    </select>
    <!--获取间隔下拉框数据列表-->
    <select id="getJgDataListSelected" resultType="java.util.Map">
        SELECT
        wzbm as `value`,
        jgmc as `label`
        FROM
        locations_jg
        <where>
            <if test="locationsJg.ssbdz!=null and locationsJg.ssbdz!= ''">
                and locations_jg.ssbdz=#{locationsJg.ssbdz}
            </if>
        </where>
        GROUP BY wzbm,wzmc
        ORDER BY locations_jg.px
    </select>
    <!--获取设备类型下拉框数据-->
    <select id="getSblxDataListSelected" resultType="java.util.Map">
        SELECT
            sblxbm as `value`,
            sblx as `label`
        FROM
            mwt_ud_gy_sblx
        WHERE
            SBLXBM like 'bd%'
            AND cj = '1'
        GROUP BY
            sblxbm,
            sblx
        ORDER BY mwt_ud_gy_sblx.px

    </select>
    <select id="getBdzDataList" resultType="java.util.Map">
        select sbdm as id,
               bdzmc as label
        from locations_bdz
        <where>
          <if test="bdzmc != '' and bdzmc != null">
              and bdzmc = #{bdzmc}
          </if>
        </where>

    </select>
    <select id="getJgData" resultType="java.util.Map">
        SELECT
            wzbm as id,
          jgmc as label
        FROM
            locations_jg
        <where>
            ssbdz=#{ssbdz}
        </where>
    </select>

    <!--查询变电站数量-->
    <select id="getBdzTj" resultType="java.lang.String">
        SELECT count(1) FROM locations_bdz
    </select>
    <select id="getGfdzTj" resultType="java.lang.String">
        SELECT count(1) FROM locations_bdz_gf
    </select>
    <!--查询变压器数量-->
    <select id="getByqTj" resultType="java.lang.String">
        SELECT count(1) FROM asset_bd where asset_type_code = 'bd01'
    </select>

    <!--查询线路数量信息-->
    <select id="get6kvXlTj" parameterType="java.util.List" resultType="java.util.Map">
        select
            dydj,
            count(1) xl,
            sum(total_length) cd,
            (select count(1) from asset_yx_tower where dydj = line.dydj) gts,
            sum(dl_length) dlcd
        from
            sd_line line
        group by
            dydj
        order by
            dydj desc
    </select>

    <!--查询配电站数量-->
    <select id="getPdzTj" resultType="java.lang.String">
        select count(1) from locations_pds
    </select>
    <!--查询变压器数量-->
    <select id="getPdByqTj" resultType="java.lang.String">
        SELECT count(1) FROM dwzy_pdsbjbxx where sbmc like concat ('%','变压器','%')
    </select>
    <!--查询配电柜数量-->
    <select id="getPdgTj" resultType="java.lang.String">
        select count(1) as num,'pdg' as lx from rocket_dgyt.locations_pdg;
    </select>
    <select id="getDefectStatistics" parameterType="java.util.List" resultType="java.util.Map">
        select t2.qxlb,IFNULL(t1.num,0) num,IFNULL(t2.num_all ,0) num_all from
            (select COUNT(1) num,qxlb from  mwt_ud_sbdyx_bdqxjl where lczt !=7 GROUP BY qxlb) t1 right join
                (select COUNT(1) num_all,qxlb from  mwt_ud_sbdyx_bdqxjl GROUP BY qxlb) t2 on t1.qxlb = t2.qxlb WHERE t2.qxlb is not null ORDER BY t2.qxlb
    </select>

    <select id="getBdAssetByCode" resultType="java.util.Map">
        select
        asset.obj_id as sbid,
        asset.xh as  `ggxh`,
        asset.dydjbm,
        assetType.sblx as `sblxmc`,
        assetType.sblxbm as `sblxbm`,
        asset.sbmc, asset.sbdm,
        bdz.bdzmc,
        jg.jgmc as wzmc,jg.wzbm
        from asset_bd asset
        LEFT JOIN locations_bdz bdz on asset.ssbdz = bdz.sbdm
        left join locations_jg jg on asset.ssjg = jg.wzbm
        LEFT JOIN mwt_ud_gy_sblx assetType on asset.asset_type_code = assetType.SBLXBM
        <where>
             asset.ssjg
            <foreach collection="assetBd.ssjgList" open="in (" separator="," close=")" item="item">
                #{item.id}
            </foreach>
            <if test="assetBd.assetTypeCode != '' and assetBd.assetTypeCode != null">
                and  find_in_set(asset.asset_type_code,#{assetBd.assetTypeCode})
            </if>
            <if test="assetBd.sbmc != '' and assetBd.sbmc != null">
                and  asset.sbmc like concat ('%',#{assetBd.sbmc})
            </if>
        </where>
     </select>

    <select id="selectSbXQ" resultType="com.qz.entity.asset.AssetBd">
        SELECT
        asset.*,
        jg.jgmc ,
        bdz.bdzmc
        <if  test="param.sbmcs != '' and param.sbmcs != null">
            ,GROUP_CONCAT(asset.sbmc) as sbmcs
        </if>
        FROM
        asset_bd asset
        LEFT JOIN locations_bdz bdz ON asset.ssbdz = bdz.sbdm
        LEFT JOIN locations_jg jg ON asset.ssjg = jg.wzbm
        <where>
            <if test="param.sysbids != '' and param.sysbids != null">
              and   find_in_set(asset.obj_id,#{param.sysbids})
            </if>
            <if test="param.xb != '' and param.xb != null">
                and   asset.xb=#{param.xb}
            </if>
            <if test="param.sbmc != '' and param.sbmc != null">
                and   asset.sbmc like concat ('%',#{param.sbmc},'%')
            </if>
        </where>
           GROUP BY asset.ssbdz
    </select>

</mapper>
