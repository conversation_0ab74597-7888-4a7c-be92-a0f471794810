{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\xldzcz\\dzczp.vue?vue&type=template&id=67457e13&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\xldzcz\\dzczp.vue", "mtime": 1719919561036}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImFwcC1jb250YWluZXIiPgogIDxlbC1maWx0ZXIKICAgIDpkYXRhPSJmaWx0ZXJJbmZvLmRhdGEiCiAgICA6ZmllbGQtbGlzdD0iZmlsdGVySW5mby5maWVsZExpc3QiCiAgICA6d2lkdGg9InsgbGFiZWxXaWR0aDogMTAwLCBpdGVtV2lkdGg6IDIwMCB9IgogICAgQGhhbmRsZVJlc2V0PSJmaWx0ZXJSZXNldCIKICAvPgogIDxkaXY+CiAgICA8ZWwtd2hpdGUgY2xhc3M9ImJ1dHRvbi1ncm91cCI+CiAgICAgIDxkaXYgY2xhc3M9ImJ1dHRvbl9idG4iPgogICAgICAgIDxlbC1idXR0b24KICAgICAgICAgIHR5cGU9ImRhbmdlciIKICAgICAgICAgIGljb249ImVsLWljb24tZGVsZXRlIgogICAgICAgICAgdi1pZj0iaGFzU3VwZXJSb2xlIgogICAgICAgICAgQGNsaWNrPSJkZWxldGVSb3ciCiAgICAgICAgICA6ZGlzYWJsZWQ9InNpbmdsZSIKICAgICAgICAgID7liKDpmaQ8L2VsLWJ1dHRvbgogICAgICAgID4KICAgICAgPC9kaXY+CiAgICAgIDxjb21wLXRhYmxlCiAgICAgICAgOnRhYmxlLWFuZC1wYWdlLWluZm89InRhYmxlQW5kUGFnZUluZm8iCiAgICAgICAgQHVwZGF0ZTptdWx0aXBsZVNlbGVjdGlvbj0ic2VsZWN0Q2hhbmdlIgogICAgICAgIGhlaWdodD0iNjl2aCIKICAgICAgICB2LWxvYWRpbmc9ImxvYWRpbmciCiAgICAgID4KICAgICAgICA8IS0tIDxlbC10YWJsZS1jb2x1bW4KICAgICAgICAgIHByb3A9InN0YXR1c0NuIgogICAgICAgICAgc2xvdD0idGFibGVfc2l4IgogICAgICAgICAgYWxpZ249ImNlbnRlciIKICAgICAgICAgIHN0eWxlPSJkaXNwbGF5OiBibG9jayIKICAgICAgICAgIGxhYmVsPSLmtYHnqIvnirbmgIEiCiAgICAgICAgICBtaW4td2lkdGg9IjEyMCIKICAgICAgICAgIDpyZXNpemFibGU9ImZhbHNlIgogICAgICAgID4KICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90LXNjb3BlPSJzY29wZSI+CiAgICAgICAgICAgIDxlbC1iYWRnZQogICAgICAgICAgICAgIHYtaWY9InNjb3BlLnJvdy5pc0JhY2sgPT09IDEiCiAgICAgICAgICAgICAgdmFsdWU9IumAgOWbniIKICAgICAgICAgICAgICBjbGFzcz0iaXRlbSIKICAgICAgICAgICAgICB0eXBlPSJkYW5nZXIiCiAgICAgICAgICAgID4KICAgICAgICAgICAgPC9lbC1iYWRnZT4KICAgICAgICAgICAgPHNwYW4+e3sgc2NvcGUucm93LnN0YXR1c0NuIH19PC9zcGFuPgogICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICA8L2VsLXRhYmxlLWNvbHVtbj4gLS0+CiAgICAgICAgPGVsLXRhYmxlLWNvbHVtbgogICAgICAgICAgc2xvdD0idGFibGVfZWlnaHQiCiAgICAgICAgICBhbGlnbj0iY2VudGVyIgogICAgICAgICAgZml4ZWQ9InJpZ2h0IgogICAgICAgICAgc3R5bGU9ImRpc3BsYXk6IGJsb2NrIgogICAgICAgICAgbGFiZWw9IuaTjeS9nCIKICAgICAgICAgIHdpZHRoPSIxNjAiCiAgICAgICAgICA6cmVzaXphYmxlPSJmYWxzZSIKICAgICAgICA+CiAgICAgICAgICA8dGVtcGxhdGUgc2xvdC1zY29wZT0ic2NvcGUiPgogICAgICAgICAgICA8ZWwtYnV0dG9uCiAgICAgICAgICAgICAgQGNsaWNrPSJnZXREZXRhaWxzKHNjb3BlLnJvdykiCiAgICAgICAgICAgICAgdHlwZT0idGV4dCIKICAgICAgICAgICAgICBzaXplPSJzbWFsbCIKICAgICAgICAgICAgICB0aXRsZT0i6K+m5oOFIgogICAgICAgICAgICAgIGNsYXNzPSJlbC1pY29uLXZpZXciCiAgICAgICAgICAgIC8+CiAgICAgICAgICAgIDxlbC1idXR0b24KICAgICAgICAgICAgICBAY2xpY2s9ImdldFVwZGF0ZShzY29wZS5yb3cpIgogICAgICAgICAgICAgIGNsYXNzPSJlbC1pY29uLWVkaXQiCiAgICAgICAgICAgICAgdGl0bGU9Iue8lui+kSIKICAgICAgICAgICAgICB2LWlmPSIKICAgICAgICAgICAgICAgIChzY29wZS5yb3cuc3RhdHVzID09PSAnMCcgJiYgc2NvcGUucm93LmNyZWF0ZUJ5ID09PSBjdXJyZW50VXNlcikgfHwgaGFzU3VwZXJSb2xlCiAgICAgICAgICAgICAgIgogICAgICAgICAgICAgIHR5cGU9InRleHQiCiAgICAgICAgICAgICAgc2l6ZT0ic21hbGwiCiAgICAgICAgICAgIC8+CiAgICAgICAgICAgIDxlbC1idXR0b24KICAgICAgICAgICAgICBAY2xpY2s9ImRlbGV0ZVJvdyhzY29wZS5yb3cpIgogICAgICAgICAgICAgIGNsYXNzPSJlbC1pY29uLWRlbGV0ZSIKICAgICAgICAgICAgICB0aXRsZT0i5Yig6ZmkIgogICAgICAgICAgICAgIHYtaWY9IgogICAgICAgICAgICAgICAgc2NvcGUucm93LnN0YXR1cyA9PT0gJzAnICYmIHNjb3BlLnJvdy5jcmVhdGVCeSA9PT0gY3VycmVudFVzZXIKICAgICAgICAgICAgICAiCiAgICAgICAgICAgICAgdHlwZT0idGV4dCIKICAgICAgICAgICAgICBzaXplPSJzbWFsbCIKICAgICAgICAgICAgLz4KICAgICAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgICAgIHR5cGU9InRleHQiCiAgICAgICAgICAgICAgc2l6ZT0ic21hbGwiCiAgICAgICAgICAgICAgQGNsaWNrPSJudWxsaWZ5R3pwKHNjb3BlLnJvdy5vYmpJZCkiCiAgICAgICAgICAgICAgdi1pZj0ic2NvcGUucm93LnN0YXR1cyA8IDQgJiYgc2NvcGUucm93LnN0YXR1cyA+IDAgJiYKICAgICAgICAgICAgICAoIHNjb3BlLnJvdy5jcmVhdGVCeSA9PT0gY3VycmVudFVzZXIgfHwgKHNjb3BlLnJvdy5ienNwciAmJiBzY29wZS5yb3cuYnpzcHIgPT09IGN1cnJlbnRVc2VyKSAKICAgICAgICAgICAgICB8fCAoc2NvcGUucm93LmZnc3NwciAmJiBzY29wZS5yb3cuZmdzc3ByID09PSBjdXJyZW50VXNlcikgCiAgICAgICAgICAgICAgfHwgKHNjb3BlLnJvdy5ianIgJiYgc2NvcGUucm93LmJqciA9PT0gY3VycmVudFVzZXIpCiAgICAgICAgICAgICAgfHwgaGFzU3VwZXJSb2xlICkKICAgICAgICAgICAgICAiCiAgICAgICAgICAgICAgdGl0bGU9IuS9nOW6nyIKICAgICAgICAgICAgICBjbGFzcz0iZWwtaWNvbi1jbG9zZSIKICAgICAgICAgICAgPgogICAgICAgICAgICA8L2VsLWJ1dHRvbj4KICAgICAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgICAgIEBjbGljaz0ic2hvd1RpbWVMaW5lKHNjb3BlLnJvdykiCiAgICAgICAgICAgICAgdi1pZj0ic2NvcGUucm93LmlzU3RhcnQgPT09IDEiCiAgICAgICAgICAgICAgdHlwZT0idGV4dCIKICAgICAgICAgICAgICBzaXplPSJzbWFsbCIKICAgICAgICAgICAgICBjbGFzcz0iZWwtaWNvbi1sY2NrIGNvbW1vbkljb24iCiAgICAgICAgICAgICAgdGl0bGU9Iua1geeoi+afpeeciyIKICAgICAgICAgICAgLz4KICAgICAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgICAgIEBjbGljaz0ic2hvd1Byb2Nlc3NJbWcoc2NvcGUucm93KSIKICAgICAgICAgICAgICB2LWlmPSJzY29wZS5yb3cuaXNTdGFydCA9PT0gMSIKICAgICAgICAgICAgICB0eXBlPSJ0ZXh0IgogICAgICAgICAgICAgIHNpemU9InNtYWxsIgogICAgICAgICAgICAgIGNsYXNzPSJlbC1pY29uLWxjdCBjb21tb25JY29uIgogICAgICAgICAgICAgIHRpdGxlPSLmtYHnqIvlm74iCiAgICAgICAgICAgIC8+CiAgICAgICAgICAgIDwhLS0gICAgICAgICAgICAgIDxlbC1idXR0b24gQGNsaWNrPSJleHBvcnRXb3JkKHNjb3BlLnJvdykiIHYtaWY9InNjb3BlLnJvdy5zdGF0dXMgPT0gJ+W+heWKnue7kyciIHR5cGU9InRleHQiIHNpemU9InNtYWxsIj4KICAgICAgICAgICAgICAgICAgICAgICAgICAgIOWvvOWHuldvcmQKICAgICAgICAgICAgICAgICAgICAgICAgICA8L2VsLWJ1dHRvbj4tLT4KICAgICAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgICAgIEBjbGljaz0icHJldmlld0ZpbGUoc2NvcGUucm93KSIKICAgICAgICAgICAgICB2LWlmPSJzY29wZS5yb3cuc3RhdHVzID4gMiIKICAgICAgICAgICAgICB0eXBlPSJ0ZXh0IgogICAgICAgICAgICAgIHNpemU9InNtYWxsIgogICAgICAgICAgICAgIGNsYXNzPSJlbC1pY29uLXpvb20taW4iCiAgICAgICAgICAgICAgdGl0bGU9IumihOiniCIKICAgICAgICAgICAgLz4KICAgICAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgICAgIEBjbGljaz0iZXhwb3J0UGRmKHNjb3BlLnJvdykiCiAgICAgICAgICAgICAgdi1pZj0ic2NvcGUucm93LnN0YXR1cyA+IDIiCiAgICAgICAgICAgICAgdHlwZT0idGV4dCIKICAgICAgICAgICAgICBzaXplPSJzbWFsbCIKICAgICAgICAgICAgICBjbGFzcz0iZWwtaWNvbi1wZGYtZXhwb3J0IGNvbW1vbkljb24iCiAgICAgICAgICAgICAgdGl0bGU9IuWvvOWHunBkZiIKICAgICAgICAgICAgLz4KICAgICAgICAgIDwvdGVtcGxhdGU+CiAgICAgICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgICAgIDwvY29tcC10YWJsZT4KICAgIDwvZWwtd2hpdGU+CiAgPC9kaXY+CgogIDwhLS0g6K+m5oOFL+aWsOWini/kv67mlLkgLS0+CiAgPGVsLWRpYWxvZwogICAgOnRpdGxlPSJ0aXRsZSIKICAgIDp2aXNpYmxlLnN5bmM9ImlzU2hvd0RldGFpbHMiCiAgICB2LWlmPSJpc1Nob3dEZXRhaWxzIgogICAgd2lkdGg9IjYwJSIKICAgIHYtZGlhbG9nRHJhZwogID4KICAgIDxlbC1mb3JtIGxhYmVsLXdpZHRoPSIxMjBweCIgcmVmPSJmb3JtIiA6bW9kZWw9ImZvcm0iIDpydWxlcz0icnVsZXMiPgogICAgICA8ZGl2PgogICAgICAgIDxlbC1yb3c+CiAgICAgICAgICA8ZWwtY29sIDpzcGFuPSIyNCI+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuW3peS9nOWQjeensCIgcHJvcD0iZ3ptYyI+CiAgICAgICAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICAgICAgICB0eXBlPSJ0ZXh0YXJlYSIKICAgICAgICAgICAgICAgIDpyb3dzPSIyIgogICAgICAgICAgICAgICAgdi1tb2RlbD0iZm9ybS5nem1jIgogICAgICAgICAgICAgICAgOmRpc2FibGVkPSJpc0Rpc2FibGVkIgogICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeW3peS9nOWQjeensCIKICAgICAgICAgICAgICAvPgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgIDwvZWwtY29sPgogICAgICAgIDwvZWwtcm93PgogICAgICAgIDxlbC1yb3c+CiAgICAgICAgICA8ZWwtY29sIDpzcGFuPSI4Ij4KICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i57yW5Y+3IiBwcm9wPSJibSI+CiAgICAgICAgICAgICAgPGVsLWlucHV0IHYtbW9kZWw9ImZvcm0uYm0iIGRpc2FibGVkIHBsYWNlaG9sZGVyPSLor7fovpPlhaUiIC8+CiAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgICA8ZWwtY29sIDpzcGFuPSI4Ij4KICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i57q/6Lev5ZCN56ewIiBwcm9wPSJ4bG1jIj4KICAgICAgICAgICAgICA8ZWwtaW5wdXQKICAgICAgICAgICAgICAgIHYtbW9kZWw9ImZvcm0ueGxtYyIKICAgICAgICAgICAgICAgIGRpc2FibGVkCiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36L6T5YWl57q/6Lev5ZCN56ewIgogICAgICAgICAgICAgIC8+CiAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgPC9lbC1yb3c+CiAgICAgICAgPGVsLXJvdz4KICAgICAgICAgIDxlbC1jb2wgOnNwYW49IjgiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtCiAgICAgICAgICAgICAgbGFiZWw9IuaTjeS9nOS6uiIKICAgICAgICAgICAgICBwcm9wPSJjenIiCiAgICAgICAgICAgICAgOnJ1bGVzPSIKICAgICAgICAgICAgICAgIGZvcm0uc3RhdHVzID09PSAnMycgJiYgZm9ybS5zZnl6eCA9PT0gJ+W3suaJp+ihjCcKICAgICAgICAgICAgICAgICAgPyBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+S4jeiDveS4uuepuicgfV0KICAgICAgICAgICAgICAgICAgOiBbeyByZXF1aXJlZDogZmFsc2UgfV0KICAgICAgICAgICAgICAiCiAgICAgICAgICAgID4KICAgICAgICAgICAgICA8ZWwtaW5wdXQKICAgICAgICAgICAgICAgIHYtbW9kZWw9ImZvcm0uY3pyIgogICAgICAgICAgICAgICAgOmRpc2FibGVkPSJpc0Rpc2FibGVkQmoiCiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36L6T5YWl5pON5L2c5Lq6IgogICAgICAgICAgICAgIC8+CiAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgICA8ZWwtY29sIDpzcGFuPSI4Ij4KICAgICAgICAgICAgPGVsLWZvcm0taXRlbQogICAgICAgICAgICAgIGxhYmVsPSLnm5HmiqTkuroiCiAgICAgICAgICAgICAgcHJvcD0iamhyIgogICAgICAgICAgICAgIDpydWxlcz0iCiAgICAgICAgICAgICAgICBmb3JtLnN0YXR1cyA9PT0gJzMnICYmIGZvcm0uc2Z5enggPT09ICflt7LmiafooYwnCiAgICAgICAgICAgICAgICAgID8gW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfkuI3og73kuLrnqbonIH1dCiAgICAgICAgICAgICAgICAgIDogW3sgcmVxdWlyZWQ6IGZhbHNlIH1dCiAgICAgICAgICAgICAgIgogICAgICAgICAgICA+CiAgICAgICAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICAgICAgICB2LW1vZGVsPSJmb3JtLmpociIKICAgICAgICAgICAgICAgIDpkaXNhYmxlZD0iaXNEaXNhYmxlZEJqIgogICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeebkeaKpOS6uiIKICAgICAgICAgICAgICAvPgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgIDwvZWwtY29sPgogICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iOCI+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuaXpeacnyIgcHJvcD0icnEiPgogICAgICAgICAgICAgIDxlbC1kYXRlLXBpY2tlcgogICAgICAgICAgICAgICAgdi1tb2RlbD0iZm9ybS5ycSIKICAgICAgICAgICAgICAgIDpkaXNhYmxlZD0iaXNEaXNhYmxlZEJqIgogICAgICAgICAgICAgICAgdHlwZT0iZGF0ZSIKICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLpgInmi6nml6XmnJ8iCiAgICAgICAgICAgICAgICBmb3JtYXQ9Inl5eXktTU0tZGQiCiAgICAgICAgICAgICAgICB2YWx1ZS1mb3JtYXQ9Inl5eXktTU0tZGQiCiAgICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDEwMCUiCiAgICAgICAgICAgICAgPgogICAgICAgICAgICAgIDwvZWwtZGF0ZS1waWNrZXI+CiAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgICA8ZWwtY29sIDpzcGFuPSI4Ij4KICAgICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5pON5L2c6aG55pWw77yaIiBwcm9wPSJjenhzIj4KICAgICAgICAgICAgICA8ZWwtaW5wdXQtbnVtYmVyCiAgICAgICAgICAgICAgICB2LW1vZGVsPSJmb3JtLmN6eHMiCiAgICAgICAgICAgICAgICBkaXNhYmxlZAogICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeaTjeS9nOmhueaVsCIKICAgICAgICAgICAgICAvPgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgIDwvZWwtY29sPgogICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iOCI+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuW3suaJp+ihjOmhueaVsO+8miIgcHJvcD0ieXp4Y3p4cyI+CiAgICAgICAgICAgICAgPGVsLWlucHV0LW51bWJlcgogICAgICAgICAgICAgICAgdi1tb2RlbD0iZm9ybS55enhjenhzIgogICAgICAgICAgICAgICAgZGlzYWJsZWQKICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaXlt7LmiafooYzmk43kvZzpobnmlbAiCiAgICAgICAgICAgICAgLz4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICA8L2VsLWNvbD4KICAgICAgICAgIDxlbC1jb2wgOnNwYW49IjgiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLmnKrmiafooYzpobnmlbDvvJoiIHByb3A9Ind6eGN6eHMiPgogICAgICAgICAgICAgIDxlbC1pbnB1dC1udW1iZXIKICAgICAgICAgICAgICAgIHYtbW9kZWw9ImZvcm0ud3p4Y3p4cyIKICAgICAgICAgICAgICAgIGRpc2FibGVkCiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36L6T5YWl5pyq5omn6KGM5pON5L2c6aG55pWwIgogICAgICAgICAgICAgIC8+CiAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgPC9lbC1yb3c+CiAgICAgICAgPGVsLXJvdz4KICAgICAgICAgIDxlbC1jb2wgOnNwYW49IjgiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLmmK/lkKblt7LmiafooYzvvJoiIHByb3A9InNmeXp4Ij4KICAgICAgICAgICAgICA8ZWwtc2VsZWN0CiAgICAgICAgICAgICAgICB2LW1vZGVsPSJmb3JtLnNmeXp4IgogICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+mAieaLqSIKICAgICAgICAgICAgICAgIDpkaXNhYmxlZD0iaXNEaXNhYmxlZEJqIgogICAgICAgICAgICAgICAgQGNoYW5nZT0iaGFuZGxlQ2hhbmdlT2ZTZnp4IgogICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgIDxlbC1vcHRpb24KICAgICAgICAgICAgICAgICAgdi1mb3I9Iml0ZW0gaW4gc2Z5enhMaXN0IgogICAgICAgICAgICAgICAgICA6a2V5PSJpdGVtLnZhbHVlIgogICAgICAgICAgICAgICAgICA6bGFiZWw9Iml0ZW0ubGFiZWwiCiAgICAgICAgICAgICAgICAgIDp2YWx1ZT0iaXRlbS52YWx1ZSIKICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgIDwvZWwtb3B0aW9uPgogICAgICAgICAgICAgIDwvZWwtc2VsZWN0PgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgIDwvZWwtY29sPgogICAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMjQiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtCiAgICAgICAgICAgICAgbGFiZWw9IuacquaJp+ihjOWOn+WboO+8miIKICAgICAgICAgICAgICBwcm9wPSJ3enh5eSIKICAgICAgICAgICAgICB2LWlmPSJmb3JtLnNmeXp4ID09PSAn5pyq5omn6KGMJyIKICAgICAgICAgICAgPgogICAgICAgICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgICAgICAgdHlwZT0idGV4dGFyZWEiCiAgICAgICAgICAgICAgICB2LW1vZGVsPSJmb3JtLnd6eHl5IgogICAgICAgICAgICAgICAgOmRpc2FibGVkPSJpc0Rpc2FibGVkQmoiCiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36L6T5YWl5YaF5a65IgogICAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOiAxMDAlIgogICAgICAgICAgICAgIC8+CiAgICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgPC9lbC1yb3c+CgogICAgICAgIDxlbC1yb3c+CiAgICAgICAgICA8ZWwtY29sIDpzcGFuPSIyNCI+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuS4iuS8oOWbvueJh++8miI+CiAgICAgICAgICAgICAgPGVsLXVwbG9hZAogICAgICAgICAgICAgICAgOmRpc2FibGVkPSJpc0Rpc2FibGVkQmoiCiAgICAgICAgICAgICAgICBhY3Rpb249IiIKICAgICAgICAgICAgICAgIHJlZj0idXBsb2FkSW1nIgogICAgICAgICAgICAgICAgYWNjZXB0PSJpbWFnZS9qcGVnLGltYWdlL2pwZyxpbWFnZS9wbmciCiAgICAgICAgICAgICAgICA6aGVhZGVycz0iaGVhZGVyIgogICAgICAgICAgICAgICAgOm11bHRpcGxlPSJ0cnVlIgogICAgICAgICAgICAgICAgOm9uLWNoYW5nZT0iaGFuZGxlQ2hhbmdlIgogICAgICAgICAgICAgICAgOmRhdGE9InVwbG9hZEltZ0RhdGEiCiAgICAgICAgICAgICAgICA6ZmlsZS1saXN0PSJpbWdMaXN0IgogICAgICAgICAgICAgICAgOmF1dG8tdXBsb2FkPSJmYWxzZSIKICAgICAgICAgICAgICAgIGxpc3QtdHlwZT0icGljdHVyZS1jYXJkIgogICAgICAgICAgICAgICAgOm9uLXByZXZpZXc9ImhhbmRsZVBpY3R1cmVDYXJkUHJldmlldyIKICAgICAgICAgICAgICAgIDpvbi1wcm9ncmVzcz0iaGFuZGxlUHJvZ3Jlc3MiCiAgICAgICAgICAgICAgICA6b24tcmVtb3ZlPSJoYW5kbGVSZW1vdmUiCiAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgPGkgY2xhc3M9ImVsLWljb24tcGx1cyI+PC9pPgogICAgICAgICAgICAgIDwvZWwtdXBsb2FkPgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgIDwvZWwtY29sPgogICAgICAgIDwvZWwtcm93PgogICAgICA8L2Rpdj4KICAgICAgPCEtLemihOiniOS/oeaBry0tPgogICAgICA8ZGl2PgogICAgICAgIDxlbC1kaWFsb2cKICAgICAgICAgIDp0aXRsZT0idGl0bGV5bCIKICAgICAgICAgIDp2aXNpYmxlLnN5bmM9InlsIgogICAgICAgICAgYXBwZW5kLXRvLWJvZHkKICAgICAgICAgIHdpZHRoPSI2MCUiCiAgICAgICAgPgogICAgICAgICAgPGVsLXRhYmxlCiAgICAgICAgICAgIDpkYXRhPSJwcm9wVGFibGVEYXRhLmNvbEZpcnN0IgogICAgICAgICAgICBib3JkZXIKICAgICAgICAgICAgc3RyaXBlCiAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogMTAwJSIKICAgICAgICAgICAgOmRlZmF1bHQtc29ydD0ieyBwcm9wOiAneGgnLCBvcmRlcjogJ2FzY2VuZGluZycgfSIKICAgICAgICAgID4KICAgICAgICAgICAgPGVsLXRhYmxlLWNvbHVtbgogICAgICAgICAgICAgIGFsaWduPSJjZW50ZXIiCiAgICAgICAgICAgICAgcHJvcD0ieGgiCiAgICAgICAgICAgICAgd2lkdGg9IjEwMHB4IgogICAgICAgICAgICAgIGxhYmVsPSLluo/lj7ciCiAgICAgICAgICAgICAgc29ydGFibGUKICAgICAgICAgICAgPgogICAgICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90LXNjb3BlPSJzY29wZSI+CiAgICAgICAgICAgICAgICA8ZWwtaW5wdXQKICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeW6j+WPtyIKICAgICAgICAgICAgICAgICAgdi1tb2RlbD0ic2NvcGUucm93LnhoIgogICAgICAgICAgICAgICAgICBkaXNhYmxlZAogICAgICAgICAgICAgICAgPjwvZWwtaW5wdXQ+CiAgICAgICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgICAgICAgICAgIDxlbC10YWJsZS1jb2x1bW4gYWxpZ249ImNlbnRlciIgcHJvcD0iY3pydyIgbGFiZWw9IuaTjeS9nOmhueebriI+CiAgICAgICAgICAgICAgPHRlbXBsYXRlIHNsb3Qtc2NvcGU9InNjb3BlIj4KICAgICAgICAgICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgICAgICAgICB0eXBlPSJ0ZXh0YXJlYSIKICAgICAgICAgICAgICAgICAgOmF1dG9zaXplPSJ7IG1pblJvd3M6IDEsIG1heFJvd3M6IDQgfSIKICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeaTjeS9nOmhueebriIKICAgICAgICAgICAgICAgICAgdi1tb2RlbD0ic2NvcGUucm93LmN6cnciCiAgICAgICAgICAgICAgICAgIGRpc2FibGVkCiAgICAgICAgICAgICAgICA+PC9lbC1pbnB1dD4KICAgICAgICAgICAgICA8L3RlbXBsYXRlPgogICAgICAgICAgICA8L2VsLXRhYmxlLWNvbHVtbj4KCiAgICAgICAgICAgIDxlbC10YWJsZS1jb2x1bW4KICAgICAgICAgICAgICBhbGlnbj0iY2VudGVyIgogICAgICAgICAgICAgIHByb3A9InhsciIKICAgICAgICAgICAgICBsYWJlbD0i5LiL5Luk5Lq6IgogICAgICAgICAgICAgIHdpZHRoPSIxMDAiCiAgICAgICAgICAgID4KICAgICAgICAgICAgICA8dGVtcGxhdGUgc2xvdC1zY29wZT0ic2NvcGUiPgogICAgICAgICAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaXlhoXlrrkiCiAgICAgICAgICAgICAgICAgIHYtbW9kZWw9InNjb3BlLnJvdy54bHIiCiAgICAgICAgICAgICAgICAgIGRpc2FibGVkCiAgICAgICAgICAgICAgICA+PC9lbC1pbnB1dD4KICAgICAgICAgICAgICA8L3RlbXBsYXRlPgogICAgICAgICAgICA8L2VsLXRhYmxlLWNvbHVtbj4KCiAgICAgICAgICAgIDxlbC10YWJsZS1jb2x1bW4KICAgICAgICAgICAgICBhbGlnbj0iY2VudGVyIgogICAgICAgICAgICAgIHByb3A9Inhsc2oiCiAgICAgICAgICAgICAgbGFiZWw9IuS4i+S7pOaXtumXtCIKICAgICAgICAgICAgICB3aWR0aD0iMjA0IgogICAgICAgICAgICA+CiAgICAgICAgICAgICAgPHRlbXBsYXRlIHNsb3Qtc2NvcGU9InNjb3BlIj4KICAgICAgICAgICAgICAgIDxlbC1kYXRlLXBpY2tlcgogICAgICAgICAgICAgICAgICB2LW1vZGVsPSJzY29wZS5yb3cueGxzaiIKICAgICAgICAgICAgICAgICAgZGlzYWJsZWQKICAgICAgICAgICAgICAgICAgdHlwZT0iZGF0ZXRpbWUiCiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLpgInmi6nkuIvku6Tml7bpl7QiCiAgICAgICAgICAgICAgICAgIGZvcm1hdD0iSEg6bW0iCiAgICAgICAgICAgICAgICAgIHZhbHVlLWZvcm1hdD0ieXl5eS1NTS1kZCBISDptbSIKICAgICAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOiAxMDAlIgogICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgPC9lbC1kYXRlLXBpY2tlcj4KICAgICAgICAgICAgICA8L3RlbXBsYXRlPgogICAgICAgICAgICA8L2VsLXRhYmxlLWNvbHVtbj4KCiAgICAgICAgICAgIDxlbC10YWJsZS1jb2x1bW4KICAgICAgICAgICAgICBhbGlnbj0iY2VudGVyIgogICAgICAgICAgICAgIHByb3A9Imhsc2oiCiAgICAgICAgICAgICAgbGFiZWw9IuWbnuS7pOaXtumXtCIKICAgICAgICAgICAgICB3aWR0aD0iMjA0IgogICAgICAgICAgICA+CiAgICAgICAgICAgICAgPHRlbXBsYXRlIHNsb3Qtc2NvcGU9InNjb3BlIj4KICAgICAgICAgICAgICAgIDxlbC1kYXRlLXBpY2tlcgogICAgICAgICAgICAgICAgICB2LW1vZGVsPSJzY29wZS5yb3cuaGxzaiIKICAgICAgICAgICAgICAgICAgZGlzYWJsZWQKICAgICAgICAgICAgICAgICAgdHlwZT0iZGF0ZXRpbWUiCiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLpgInmi6nlm57ku6Tml7bpl7QiCiAgICAgICAgICAgICAgICAgIGZvcm1hdD0iSEg6bW0iCiAgICAgICAgICAgICAgICAgIHZhbHVlLWZvcm1hdD0ieXl5eS1NTS1kZCBISDptbSIKICAgICAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOiAxMDAlIgogICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgPC9lbC1kYXRlLXBpY2tlcj4KICAgICAgICAgICAgICA8L3RlbXBsYXRlPgogICAgICAgICAgICA8L2VsLXRhYmxlLWNvbHVtbj4KICAgICAgICAgICAgPGVsLXRhYmxlLWNvbHVtbgogICAgICAgICAgICAgIHR5cGU9InNmd2MiCiAgICAgICAgICAgICAgd2lkdGg9IjUwIgogICAgICAgICAgICAgIGxhYmVsPSLmmK/lkKblrozmiJAiCiAgICAgICAgICAgICAgYWxpZ249ImNlbnRlciIKICAgICAgICAgICAgPgogICAgICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90LXNjb3BlPSJzY29wZSI+CiAgICAgICAgICAgICAgICA8ZWwtY2hlY2tib3ggdi1tb2RlbD0ic2NvcGUucm93LnNmd2MiIGRpc2FibGVkPjwvZWwtY2hlY2tib3g+CiAgICAgICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgICAgICAgICA8L2VsLXRhYmxlPgogICAgICAgIDwvZWwtZGlhbG9nPgogICAgICA8L2Rpdj4KICAgICAgPCEtLeWIl+ihqC0tPgogICAgICA8ZGl2PgogICAgICAgIDxkaXYgYWxpZ249ImxlZnQiPgogICAgICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJpbmZvIiBAY2xpY2s9ImhhbmRsZVlsQ2hhbmdlIj7pooTop4g8L2VsLWJ1dHRvbj4KICAgICAgICA8L2Rpdj4KICAgICAgICA8ZGl2IGFsaWduPSJyaWdodCI+CiAgICAgICAgICA8ZWwtY2hlY2tib3ggQGNoYW5nZT0iaGFuZGxlQ2hlY2tBbGxDaGFuZ2UiIDpkaXNhYmxlZD0iaXNEaXNhYmxlZEJqIgogICAgICAgICAgICA+5YWo6YCJPC9lbC1jaGVja2JveAogICAgICAgICAgPgogICAgICAgIDwvZGl2PgogICAgICAgIDwhLS3nrKzkuIDkuKrooajmoLwtLT4KICAgICAgICA8ZWwtdGFibGUKICAgICAgICAgIDpkYXRhPSJwcm9wVGFibGVEYXRhLmNvbEZpcnN0IgogICAgICAgICAgaGVpZ2h0PSIyMDAiCiAgICAgICAgICBib3JkZXIKICAgICAgICAgIHN0cmlwZQogICAgICAgICAgc3R5bGU9IndpZHRoOiAxMDAlIgogICAgICAgICAgOmRlZmF1bHQtc29ydD0ieyBwcm9wOiAneGgnLCBvcmRlcjogJ2FzY2VuZGluZycgfSIKICAgICAgICA+CiAgICAgICAgICA8ZWwtdGFibGUtY29sdW1uCiAgICAgICAgICAgIGFsaWduPSJjZW50ZXIiCiAgICAgICAgICAgIHByb3A9InhoIgogICAgICAgICAgICB3aWR0aD0iMTAwcHgiCiAgICAgICAgICAgIGxhYmVsPSLluo/lj7ciCiAgICAgICAgICAgIHNvcnRhYmxlCiAgICAgICAgICA+CiAgICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90LXNjb3BlPSJzY29wZSI+CiAgICAgICAgICAgICAgPGVsLWlucHV0LW51bWJlcgogICAgICAgICAgICAgICAgc2l6ZT0ic21hbGwiCiAgICAgICAgICAgICAgICB2LW1vZGVsPSJzY29wZS5yb3cueGgiCiAgICAgICAgICAgICAgICA6bWluPSIxIgogICAgICAgICAgICAgICAgOnByZWNpc2lvbj0iMCIKICAgICAgICAgICAgICAgIGNvbnRyb2xzLXBvc2l0aW9uPSJyaWdodCIKICAgICAgICAgICAgICAgIDpkaXNhYmxlZD0iaXNEaXNhYmxlZCIKICAgICAgICAgICAgICA+PC9lbC1pbnB1dC1udW1iZXI+CiAgICAgICAgICAgIDwvdGVtcGxhdGU+CiAgICAgICAgICA8L2VsLXRhYmxlLWNvbHVtbj4KICAgICAgICAgIDxlbC10YWJsZS1jb2x1bW4gYWxpZ249ImNlbnRlciIgcHJvcD0iY3pydyIgbGFiZWw9IuaTjeS9nOmhueebriI+CiAgICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90LXNjb3BlPSJzY29wZSI+CiAgICAgICAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36L6T5YWl5pON5L2c6aG555uuIgogICAgICAgICAgICAgICAgdi1tb2RlbD0ic2NvcGUucm93LmN6cnciCiAgICAgICAgICAgICAgICA6ZGlzYWJsZWQ9ImlzRGlzYWJsZWQiCiAgICAgICAgICAgICAgPjwvZWwtaW5wdXQ+CiAgICAgICAgICAgIDwvdGVtcGxhdGU+CiAgICAgICAgICA8L2VsLXRhYmxlLWNvbHVtbj4KCiAgICAgICAgICA8ZWwtdGFibGUtY29sdW1uCiAgICAgICAgICAgIGFsaWduPSJjZW50ZXIiCiAgICAgICAgICAgIHByb3A9InhsciIKICAgICAgICAgICAgbGFiZWw9IuS4i+S7pOS6uiIKICAgICAgICAgICAgd2lkdGg9IjEwMCIKICAgICAgICAgID4KICAgICAgICAgICAgPHRlbXBsYXRlIHNsb3Qtc2NvcGU9InNjb3BlIj4KICAgICAgICAgICAgICA8ZWwtaW5wdXQKICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaXlhoXlrrkiCiAgICAgICAgICAgICAgICB2LW1vZGVsPSJzY29wZS5yb3cueGxyIgogICAgICAgICAgICAgICAgOmRpc2FibGVkPSJpc0Rpc2FibGVkQmoiCiAgICAgICAgICAgICAgPjwvZWwtaW5wdXQ+CiAgICAgICAgICAgIDwvdGVtcGxhdGU+CiAgICAgICAgICA8L2VsLXRhYmxlLWNvbHVtbj4KICAgICAgICAgIDxlbC10YWJsZS1jb2x1bW4KICAgICAgICAgICAgYWxpZ249ImNlbnRlciIKICAgICAgICAgICAgcHJvcD0ieGxzaiIKICAgICAgICAgICAgbGFiZWw9IuS4i+S7pOaXtumXtCIKICAgICAgICAgICAgd2lkdGg9IjIwNCIKICAgICAgICAgID4KICAgICAgICAgICAgPHRlbXBsYXRlIHNsb3Qtc2NvcGU9InNjb3BlIj4KICAgICAgICAgICAgICA8ZWwtZGF0ZS1waWNrZXIKICAgICAgICAgICAgICAgIHYtbW9kZWw9InNjb3BlLnJvdy54bHNqIgogICAgICAgICAgICAgICAgOmRpc2FibGVkPSJpc0Rpc2FibGVkQmoiCiAgICAgICAgICAgICAgICB0eXBlPSJkYXRldGltZSIKICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLpgInmi6nkuIvku6Tml7bpl7QiCiAgICAgICAgICAgICAgICBmb3JtYXQ9IkhIOm1tIgogICAgICAgICAgICAgICAgdmFsdWUtZm9ybWF0PSJ5eXl5LU1NLWRkIEhIOm1tIgogICAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOiAxMDAlIgogICAgICAgICAgICAgID4KICAgICAgICAgICAgICA8L2VsLWRhdGUtcGlja2VyPgogICAgICAgICAgICA8L3RlbXBsYXRlPgogICAgICAgICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgICAgICAgICA8ZWwtdGFibGUtY29sdW1uCiAgICAgICAgICAgIGFsaWduPSJjZW50ZXIiCiAgICAgICAgICAgIHByb3A9Imhsc2oiCiAgICAgICAgICAgIGxhYmVsPSLlm57ku6Tml7bpl7QiCiAgICAgICAgICAgIHdpZHRoPSIyMDQiCiAgICAgICAgICA+CiAgICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90LXNjb3BlPSJzY29wZSI+CiAgICAgICAgICAgICAgPGVsLWRhdGUtcGlja2VyCiAgICAgICAgICAgICAgICB2LW1vZGVsPSJzY29wZS5yb3cuaGxzaiIKICAgICAgICAgICAgICAgIDpkaXNhYmxlZD0iaXNEaXNhYmxlZEJqIgogICAgICAgICAgICAgICAgdHlwZT0iZGF0ZXRpbWUiCiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6YCJ5oup5Zue5Luk5pe26Ze0IgogICAgICAgICAgICAgICAgZm9ybWF0PSJISDptbSIKICAgICAgICAgICAgICAgIHZhbHVlLWZvcm1hdD0ieXl5eS1NTS1kZCBISDptbSIKICAgICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogMTAwJSIKICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgPC9lbC1kYXRlLXBpY2tlcj4KICAgICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgIDwvZWwtdGFibGUtY29sdW1uPgogICAgICAgICAgPGVsLXRhYmxlLWNvbHVtbgogICAgICAgICAgICB0eXBlPSJzZndjIgogICAgICAgICAgICB3aWR0aD0iNTAiCiAgICAgICAgICAgIGxhYmVsPSLmmK/lkKblrozmiJAiCiAgICAgICAgICAgIGFsaWduPSJjZW50ZXIiCiAgICAgICAgICA+CiAgICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90LXNjb3BlPSJzY29wZSI+CiAgICAgICAgICAgICAgPGVsLWNoZWNrYm94CiAgICAgICAgICAgICAgICB2LW1vZGVsPSJzY29wZS5yb3cuc2Z3YyIKICAgICAgICAgICAgICAgIDpkaXNhYmxlZD0iaXNEaXNhYmxlZEJqIgogICAgICAgICAgICAgICAgQGNoYW5nZT0iaGFuZGxlQ2hlY2tDaGFuZ2UiCiAgICAgICAgICAgICAgPjwvZWwtY2hlY2tib3g+CiAgICAgICAgICAgIDwvdGVtcGxhdGU+CiAgICAgICAgICA8L2VsLXRhYmxlLWNvbHVtbj4KICAgICAgICAgIDxlbC10YWJsZS1jb2x1bW4gYWxpZ249ImNlbnRlciIgd2lkdGg9IjgwIj4KICAgICAgICAgICAgPHRlbXBsYXRlIHNsb3Q9ImhlYWRlciIgc2xvdC1zY29wZT0ic2NvcGUiPgogICAgICAgICAgICAgIDxlbC1idXR0b24KICAgICAgICAgICAgICAgIHNpemU9InNtYWxsIgogICAgICAgICAgICAgICAgdHlwZT0icHJpbWFyeSIKICAgICAgICAgICAgICAgIGljb249ImVsLWljb24tcGx1cyIKICAgICAgICAgICAgICAgIDpkaXNhYmxlZD0iaXNEaXNhYmxlZCIKICAgICAgICAgICAgICAgIEBjbGljaz0ibGlzdEZpcnN0QWRkKHNjb3BlLiRpbmRleCwgc2NvcGUucm93KSIKICAgICAgICAgICAgICA+PC9lbC1idXR0b24+CiAgICAgICAgICAgIDwvdGVtcGxhdGU+CiAgICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90LXNjb3BlPSJzY29wZSI+CiAgICAgICAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgICAgICAgdHlwZT0idGV4dCIKICAgICAgICAgICAgICAgIGljb249ImVsLWljb24tZGVsZXRlIgogICAgICAgICAgICAgICAgOmRpc2FibGVkPSJpc0Rpc2FibGVkIgogICAgICAgICAgICAgICAgQGNsaWNrPSJsaXN0Rmlyc3REZWwoc2NvcGUuJGluZGV4LCBzY29wZS5yb3cpIgogICAgICAgICAgICAgID48L2VsLWJ1dHRvbj4KICAgICAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgICAgIDwvZWwtdGFibGUtY29sdW1uPgogICAgICAgIDwvZWwtdGFibGU+CiAgICAgIDwvZGl2PgogICAgPC9lbC1mb3JtPgoKICAgIDxkaXYgYWxpZ249InJpZ2h0IiBzbG90PSJmb290ZXIiPgogICAgICA8ZWwtYnV0dG9uIEBjbGljaz0iY2xvc2UiPuWPliDmtog8L2VsLWJ1dHRvbj4KICAgICAgPGVsLWJ1dHRvbgogICAgICAgIHYtaWY9ImlzRGlzYWJsZWQgJiYgYnV0dG9uTmFtZVNob3cgJiYgZm9ybS5zdGF0dXMgPiAwIgogICAgICAgIHR5cGU9ImluZm8iCiAgICAgICAgQGNsaWNrPSJnZXRTYkZzQmooJ3JvbGxiYWNrJykiCiAgICAgICAgPumAgCDlm54KICAgICAgPC9lbC1idXR0b24+CiAgICAgIDxlbC1idXR0b24KICAgICAgICB2LWlmPSJpc0Rpc2FibGVkICYmIGJ1dHRvbk5hbWVTaG93IgogICAgICAgIHR5cGU9InN1Y2Nlc3MiCiAgICAgICAgQGNsaWNrPSJnZXRTYkZzQmooJ2NvbXBsZXRlJykiCiAgICAgICAgPnt7IGJ1dHRvbk5hbWUgfX0KICAgICAgPC9lbC1idXR0b24+CiAgICAgIDxlbC1idXR0b24gdi1pZj0iIWlzRGlzYWJsZWQiIHR5cGU9InByaW1hcnkiIEBjbGljaz0ic2F2ZVJvdyIKICAgICAgICA+5L+dIOWtmDwvZWwtYnV0dG9uCiAgICAgID4KICAgIDwvZGl2PgogIDwvZWwtZGlhbG9nPgoKICA8IS0t57q/6Lev6YCJ5oup57uE5Lu2LS0+CiAgPGVsLWRpYWxvZwogICAgdGl0bGU9Iue6v+i3r+mAieaLqSIKICAgIDp2aXNpYmxlLnN5bmM9ImlzU2hvd1hsRGlhbG9nIgogICAgd2lkdGg9IjIwJSIKICAgIHYtaWY9ImlzU2hvd1hsRGlhbG9nIgogICAgdi1kaWFsb2dEcmFnCiAgPgogICAgPHNkeGwtc2VsZWN0ZWQKICAgICAgQGNsb3NlU3lzYlNlbGVjdERpYWxvZz0iaGFuZGxlQ29udHJvbFN5c2JTZWxlY3REaWFsb2ciCiAgICAgIEBoYW5kbGVBY2NlcHRTYkRhdGE9ImhhbmRsZUFjY2VwdFNiRGF0YSIKICAgID48L3NkeGwtc2VsZWN0ZWQ+CiAgPC9lbC1kaWFsb2c+CgogIDwhLS0gIOW3peS9nOa1gSAgLS0+CiAgPGFjdGl2aXRpCiAgICByZWY9ImFjdGl2aXRpIgogICAgOnByb2Nlc3NEYXRhPSJwcm9jZXNzRGF0YSIKICAgIDppc1Nob3c9ImlzU2hvdyIKICAgIDpvcHRpb249ImFjdGl2aXRpT3B0aW9uIgogICAgQHRvZG9EYXRhPSJ0b2RvUmVzdWx0IgogICAgQHRvQ2xvc2U9ImNsb3NlQWN0aXZpdGkiCiAgPjwvYWN0aXZpdGk+CiAgPHRpbWUtbGluZQogICAgOnZhbHVlPSJ0aW1lTGluZVNob3ciCiAgICA6dGltZURhdGE9InRpbWVEYXRhIgogICAgQGNsb3NlVGltZUxpbmU9ImNvbHNlVGltZUxpbmUiCiAgLz4KICA8IS0t5rWB56iL5Zu+5p+l55yLLS0+CiAgPGVsLWRpYWxvZwogICAgdGl0bGU9IuWuoeaJuea1geeoi+WbviIKICAgIDp2aXNpYmxlLnN5bmM9Im9wZW5Mb2FkaW5nSW1nIgogICAgd2lkdGg9IjU2JSIKICAgIGFwcGVuZC10by1ib2R5CiAgICA6Y2xvc2Utb24tY2xpY2stbW9kYWw9ImZhbHNlIgogID4KICAgIDxkaXY+CiAgICAgIDxpbWcgOnNyYz0iaW1nU3JjIiBzdHlsZT0id2lkdGg6IDEwMCUiIC8+CiAgICA8L2Rpdj4KICA8L2VsLWRpYWxvZz4KCiAgPCEtLeWbvueJh+aUvuWkp+WxleekuuW8ueWHuuahhi0tPgogIDxlbC1pbWFnZS12aWV3ZXIKICAgIHYtaWY9ImltZ0RpYWxvZ1Zpc2libGUiCiAgICA6b24tY2xvc2U9Im9uQ2xvc2UiCiAgICA6dXJsLWxpc3Q9ImRpYWxvZ0ltYWdlVXJsIgogICAgekluZGV4PSIzMDAwIgogIC8+CjwvZGl2Pgo="}, null]}