{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\bdgl\\bdztz.vue?vue&type=style&index=1&lang=css&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\bdgl\\bdztz.vue", "mtime": 1706897324520}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgojcGljX2Zvcm0gLmVsLWZvcm0taXRlbV9fY29udGVudCB7CiAgbWFyZ2luLWxlZnQ6IDAgIWltcG9ydGFudDsKfQo="}, {"version": 3, "sources": ["bdztz.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAm8CA;AACA;AACA", "file": "bdztz.vue", "sourceRoot": "src/views/dagangOilfield/dwzygl/bdgl", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"16\">\n      <el-col :span=\"4\">\n        <el-card shadow=\"never\" style=\"background:#e0f8ed;padding-top:10px;\">\n          <div>\n            <el-col>\n              <el-form label-width=\"80px\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"所属公司:\"\n                    >·\n                    <el-select\n                      v-model=\"treeForm.ssdwbm\"\n                      placeholder=\"请选择所属公司\"\n                      @change=\"getNewTreeInfo\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in OrganizationSelectedList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"电压等级:\">\n                    <el-select\n                      v-model=\"treeForm.dydjbm\"\n                      placeholder=\"请选择电压等级\"\n                      @change=\"getNewTreeInfo\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in VoltageLevelSelectedList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      ></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"快速查询:\">\n                    <el-input\n                      placeholder=\"输入关键字进行过滤\"\n                      v-model=\"filterText\"\n                      clearable\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-form>\n            </el-col>\n          </div>\n          <div class=\"text head-container\">\n            <el-col style=\"padding:0;\">\n              <el-tree\n                :expand-on-click-node=\"true\"\n                id=\"tree\"\n                highlight-current\n                :data=\"treeOptions\"\n                @node-click=\"handleNodeClick\"\n                :highlight-current=\"true\"\n                ref=\"tree\"\n                :filter-node-method=\"filterNode\"\n                node-key=\"id\"\n                :default-expanded-keys=\"['0']\"\n                :default-checked-keys=\"['0']\"\n                accordion\n              />\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <!--        <el-filter-->\n        <!--          ref=\"filter1\"-->\n        <!--          :data=\"filterInfo.data\"-->\n        <!--          :field-list=\"filterInfo.fieldList\"-->\n        <!--          :width=\"{ labelWidth: 120, itemWidth: 180 }\"-->\n        <!--          @handleReset=\"filterReset\"-->\n        <!--        />-->\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button\n              icon=\"el-icon-plus\"\n              @click=\"bdzAddSensorButton\"\n              v-hasPermi=\"['bdztz:button:add']\"\n              type=\"primary\"\n              >新增</el-button\n            >\n            <el-button\n              icon=\"el-icon-delete\"\n              v-hasPermi=\"['bdztz:button:delete']\"\n              type=\"danger\"\n              @click=\"deleteBdz\"\n              >删除</el-button\n            >\n          </div>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"70vh\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"updatebdz(scope.row)\"\n                  v-hasPermi=\"['bdztz:button:update']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                ></el-button>\n                <el-button\n                  @click=\"bdzDetails(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!--变电站所用弹出框开始-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"bdzDialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"60%\"\n      v-dialogDrag\n      :before-close=\"removeForm\"\n    >\n      <el-form :model=\"jbxxForm\" label-width=\"130px\" :rules=\"rules\" ref=\"form\">\n        <div\n          class=\"block\"\n          style=\"width: 50%;height: 50%;margin-bottom: 2%;float: right\"\n        >\n          <span class=\"demonstration\">变电站图片</span>\n          <el-carousel\n            trigger=\"click\"\n            height=\"150px\"\n            indicator-position=\"none\"\n            :interval=\"2000\"\n            type=\"card\"\n          >\n            <el-carousel-item v-for=\"(img, index) in imgList\" :key=\"index\">\n              <viewer :images=\"imgList\" style=\"z-index: 999\">\n                <img :src=\"img.url\" width=\"100%\" height=\"100%\" />\n              </viewer>\n            </el-carousel-item>\n          </el-carousel>\n        </div>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属单位\" prop=\"ssdwbm\">\n              <el-select\n                v-model=\"jbxxForm.ssdwbm\"\n                placeholder=\"所属单位\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option\n                  v-for=\"item in OrganizationSelectedList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"变电站数字编号\" prop=\"bdzszbh\">\n              <el-input\n                v-model=\"jbxxForm.bdzszbh\"\n                :disabled=\"isDisabled\"\n                placeholder=\"请输入变电站数字编号\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"变电站名称\" prop=\"bdzmc\">\n              <el-input\n                v-model=\"jbxxForm.bdzmc\"\n                :disabled=\"isDisabled\"\n                placeholder=\"请输入变电站名称\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <!--          <el-col :span=\"8\">-->\n          <!--            <el-form-item label=\"设备代码\" prop=\"sbdm\">-->\n          <!--              <el-input v-model=\"jbxxForm.sbdm\" :disabled=\"isDisabled\" placeholder=\"请输入设备代码\"></el-input>-->\n          <!--            </el-form-item>-->\n          <!--          </el-col>-->\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属电网\" prop=\"ssdw\">\n              <el-input\n                v-model=\"jbxxForm.ssdw\"\n                :disabled=\"isDisabled\"\n                placeholder=\"请输入所属电网\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属基地站\" prop=\"ssjdz\">\n              <el-select\n                v-model=\"jbxxForm.ssjdz\"\n                placeholder=\"请输入所属基地站\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option\n                  v-for=\"item in ssjdzList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电压等级\" prop=\"dydjbm\">\n              <el-select\n                v-model=\"jbxxForm.dydjbm\"\n                placeholder=\"请选择电压等级\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"设备状态\" prop=\"sbzt\">\n              <el-select\n                v-model=\"jbxxForm.sbzt\"\n                placeholder=\"请选择设备状态\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option\n                  v-for=\"item in sbzt\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"投运日期\" class=\"add_sy_tyrq\" prop=\"tyrq\">\n              <el-date-picker\n                v-model=\"jbxxForm.tyrq\"\n                type=\"date\"\n                placeholder=\"选择日期\"\n                format=\"yyyy-MM-dd\"\n                value-format=\"yyyy-MM-dd\"\n                :disabled=\"isDisabled\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电站类型\" prop=\"dzlx\">\n              <el-select v-model=\"jbxxForm.dzlx\" :disabled=\"isDisabled\">\n                <el-option value=\"变电站\" label=\"变电站\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否综合自动化站\" prop=\"sfzhzdh\">\n              <el-select v-model=\"jbxxForm.sfzhzdh\" :disabled=\"isDisabled\">\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否数字化变电站\" prop=\"sfszhbdz\">\n              <el-select v-model=\"jbxxForm.sfszhbdz\" :disabled=\"isDisabled\">\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否枢纽站\" prop=\"sfsnz\">\n              <el-select v-model=\"jbxxForm.sfsnz\" :disabled=\"isDisabled\">\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item\n              label=\"退运日期\"\n              class=\"add_sy_tyrq\"\n              prop=\"returnDate\"\n            >\n              <el-date-picker\n                v-model=\"jbxxForm.returnDate\"\n                type=\"date\"\n                placeholder=\"选择日期\"\n                format=\"yyyy-MM-dd\"\n                value-format=\"yyyy-MM-dd\"\n                :disabled=\"isDisabled\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"占地面积\" prop=\"zymj\">\n              <el-input\n                v-model=\"jbxxForm.zymj\"\n                :disabled=\"isDisabled\"\n                placeholder=\"请输入占地面积\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"污秽等级\" prop=\"whdj\">\n              <el-select v-model=\"jbxxForm.whdj\" :disabled=\"isDisabled\">\n                <el-option value=\"a级\" label=\"a级\"></el-option>\n                <el-option value=\"b级\" label=\"b级\"></el-option>\n                <el-option value=\"c级\" label=\"c级\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"值班方式\" prop=\"zbfs\">\n              <el-select v-model=\"jbxxForm.zbfs\" :disabled=\"isDisabled\">\n                <el-option value=\"有人值班\" label=\"有人值班\"></el-option>\n                <el-option value=\"无人值班\" label=\"无人值班\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否光纤通讯\" prop=\"sfgqtx\">\n              <el-select v-model=\"jbxxForm.sfgqtx\" :disabled=\"isDisabled\">\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"海拔\" prop=\"hb\">\n              <el-input v-model=\"jbxxForm.hb\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"8\">\n            <el-form-item label=\"工程编号\" prop=\"gcbh\">\n              <el-input\n                v-model=\"jbxxForm.gcbh\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"8\">\n            <el-form-item label=\"设计单位\" prop=\"sjdw\">\n              <el-input\n                v-model=\"jbxxForm.sjdw\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"8\">\n            <el-form-item label=\"监理单位\" prop=\"jldw\">\n              <el-input\n                v-model=\"jbxxForm.jldw\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电站重要级别\" prop=\"zyjb\">\n              <el-select v-model=\"jbxxForm.zyjb\" :disabled=\"isDisabled\">\n                <el-option value=\"A\" label=\"A\"></el-option>\n                <el-option value=\"B\" label=\"B\"></el-option>\n                <el-option value=\"C\" label=\"C\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"布置方式\" prop=\"bzfs\">\n              <el-select v-model=\"jbxxForm.bzfs\" :disabled=\"isDisabled\">\n                <el-option value=\"户内\" label=\"户内\"></el-option>\n                <el-option value=\"户外\" label=\"户外\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"站址\" prop=\"bdzdz\">\n              <el-input\n                v-model=\"jbxxForm.bdzdz\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"建筑面积\" prop=\"jzmj\">\n              <el-input\n                v-model=\"jbxxForm.jzmj\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"联系电话\" prop=\"phone\">\n              <el-input\n                v-model=\"jbxxForm.phone\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"工程名称\" prop=\"gcmc\">\n              <el-input\n                v-model=\"jbxxForm.gcmc\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"施工单位\" prop=\"sgdw\">\n              <el-input\n                v-model=\"jbxxForm.sgdw\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"地区特征\" prop=\"dqtz\">\n              <el-select v-model=\"jbxxForm.dqtz\" :disabled=\"isDisabled\">\n                <el-option value=\"户内\" label=\"户内\"></el-option>\n                <el-option value=\"户外\" label=\"户外\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"最高调度管辖权\" prop=\"zgddgxq\">\n              <el-input\n                v-model=\"jbxxForm.zgddgxq\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否满足N-1\" prop=\"sfmzn\">\n              <el-select v-model=\"jbxxForm.sfmzn\" :disabled=\"isDisabled\">\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否接入故障系统\" prop=\"sfjrgzxt\">\n              <el-select v-model=\"jbxxForm.sfjrgzxt\" :disabled=\"isDisabled\">\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否接入AVC\" prop=\"sfjravc\">\n              <el-select v-model=\"jbxxForm.sfjravc\" :disabled=\"isDisabled\">\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否集中监控\" prop=\"sfjzjk\">\n              <el-select v-model=\"jbxxForm.sfjzjk\" :disabled=\"isDisabled\">\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"接入的监控中心\" prop=\"jkzxmc\">\n              <el-input\n                v-model=\"jbxxForm.jkzxmc\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"经度\" prop=\"jd\">\n              <el-input\n                v-model=\"jbxxForm.jd\"\n                :disabled=\"isDisabled\"\n                placeholder=\"请输入经度\"\n              >\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"纬度\" prop=\"wd\">\n              <el-input\n                v-model=\"jbxxForm.wd\"\n                :disabled=\"isDisabled\"\n                placeholder=\"请输入纬度\"\n              >\n              </el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\" prop=\"bz\">\n              <el-input\n                v-model=\"jbxxForm.bz\"\n                :disabled=\"isDisabled\"\n                type=\"textarea\"\n                rows=\"2\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-form-item\n            label=\"已上传图片：\"\n            prop=\"attachment\"\n            v-if=\"jbxxForm.attachment.length > 0\"\n            id=\"pic_form\"\n          >\n            <el-col\n              :span=\"24\"\n              v-for=\"(item, index) in jbxxForm.attachment\"\n              style=\"margin-left: 0\"\n            >\n              <el-form-item :label=\"(index + 1).toString()\">\n                {{ item.fileOldName }}\n                <el-button\n                  v-if=\"!isDisabled\"\n                  type=\"error\"\n                  size=\"mini\"\n                  @click=\"deleteFileById(item.fileId)\"\n                  >删除</el-button\n                >\n              </el-form-item>\n            </el-col>\n          </el-form-item>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-form-item label=\"上传图片：\" v-if=\"!isDisabled\">\n            <el-upload\n              list-type=\"picture-card\"\n              class=\"upload-demo\"\n              accept=\".jpg,.png\"\n              ref=\"upload\"\n              :headers=\"header\"\n              action=\"/isc-api/file/upload\"\n              :before-upload=\"beforeUpload\"\n              :data=\"uploadData\"\n              single\n              :auto-upload=\"false\"\n              multiple\n            >\n              <el-button slot=\"trigger\" type=\"primary\">选取文件</el-button>\n              <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png文件</div>\n            </el-upload>\n          </el-form-item>\n        </el-row>\n\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"间隔单元\" prop=\"jgdy\">-->\n        <!--              <el-select v-model=\"jbxxForm.jgdy\" placeholder=\"请输入间隔单元\">-->\n        <!--              </el-select>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"相别\" prop=\"xb\">-->\n        <!--              <el-input v-model=\"jbxxForm.xb\" :disabled=\"isDisabled\" placeholder=\"请输入相别\"></el-input>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"相数\" prop=\"xs\">-->\n        <!--              <el-select v-model=\"jbxxForm.xs\" placeholder=\"\" :disabled=\"isDisabled\" placeholder=\"请输入相数\">-->\n        <!--              </el-select>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"出厂日期\" class=\"add_sy_tyrq\" prop=\"ccrq\">-->\n        <!--              <el-date-picker-->\n        <!--                v-model=\"jbxxForm.ccrq\"-->\n        <!--                type=\"date\"-->\n        <!--                format=\"yyyy-MM-dd\"-->\n        <!--                value-format=\"yyyy-MM-dd\"-->\n        <!--                placeholder=\"选择日期\" :disabled=\"isDisabled\">-->\n        <!--              </el-date-picker>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"安装位置\" prop=\"azwz\">-->\n        <!--              <el-input v-model=\"jbxxForm.azwz\" :disabled=\"isDisabled\" placeholder=\"请输入安装位置\"></el-input>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"用途\" prop=\"yt\">-->\n        <!--              <el-input v-model=\"jbxxForm.yt\" :disabled=\"isDisabled\" placeholder=\"请输入用途\"></el-input>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"型号\" prop=\"xh\">-->\n        <!--              <el-select v-model=\"jbxxForm.xh\" placeholder=\"请输入型号\" :disabled=\"isDisabled\" >-->\n        <!--              </el-select>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"产品代号\" prop=\"cpdh\">-->\n        <!--              <el-input v-model=\"jbxxForm.cpdh\" :disabled=\"isDisabled\" placeholder=\"请输入产品代号\"></el-input>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"额定电压\" prop=\"eddy\">-->\n        <!--              <el-select v-model=\"jbxxForm.eddy\" placeholder=\"请选择额定电压\" :disabled=\"isDisabled\">-->\n        <!--              </el-select>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"额定频率\">-->\n        <!--              <el-select v-model=\"jbxxForm.edpl\" placeholder=\"请选择额定频率\" :disabled=\"isDisabled\">-->\n        <!--              </el-select>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"使用环境\">-->\n        <!--              <el-input v-model=\"jbxxForm.syhj\" placeholder=\"请输入使用环境\" :disabled=\"isDisabled\">-->\n        <!--              </el-input>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"生产厂家\">-->\n        <!--              <el-select v-model=\"jbxxForm.sccj\" placeholder=\"请选择生产厂家\" :disabled=\"isDisabled\">-->\n        <!--              </el-select>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"制造国家\">-->\n        <!--              <el-select v-model=\"jbxxForm.zzgj\" placeholder=\"请选择制造国家\" :disabled=\"isDisabled\">-->\n        <!--              </el-select>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"组合设备类型\">-->\n        <!--              <el-select v-model=\"jbxxForm.zhsblx\" placeholder=\"请选择组合设备类型\" :disabled=\"isDisabled\">-->\n        <!--              </el-select>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"组合设备类型名称\">-->\n        <!--              <el-input v-model=\"jbxxForm.zhsblxmc\" :disabled=\"isDisabled\" placeholder=\"请选择组合设备类型\"></el-input>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"额定电流\">-->\n        <!--              <el-input v-model=\"jbxxForm.eddl\" placeholder=\"请输入额定电流\" :disabled=\"isDisabled\">-->\n        <!--              </el-input>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"运行编号\">-->\n        <!--              <el-input v-model=\"jbxxForm.yxbh\" :disabled=\"isDisabled\" placeholder=\"请输入运行编号\"></el-input>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n        <!--          <el-col :span=\"8\">-->\n        <!--            <el-form-item label=\"出厂编号\">-->\n        <!--              <el-input v-model=\"jbxxForm.ccbh\" :disabled=\"isDisabled\" placeholder=\"请输入出厂编号\"></el-input>-->\n        <!--            </el-form-item>-->\n        <!--          </el-col>-->\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"removeForm\">取 消</el-button>\n        <el-button\n          v-if=\"title == '变电站台账修改' || title == '变电站台账新增'\"\n          type=\"primary\"\n          @click=\"addBdz\"\n          class=\"pmyBtn\"\n          >确 定\n        </el-button>\n      </div>\n    </el-dialog>\n    <!--变电站所用间隔弹出框结束-->\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport {\n  addBdz,\n  getTreeInfo,\n  removeBdz\n} from \"@/api/dagangOilfield/asset/bdsbtz\";\nimport { getNewTreeInfo, getBdzList } from \"@/api/dagangOilfield/asset/bdztz\";\nimport { treeselect } from \"@/api/system/dept\";\nimport treeselect1 from \"@riophae/vue-treeselect\";\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\nimport {\n  downloadByBusinessId,\n  getListByBusinessId,\n  deleteById\n} from \"@/api/tool/file\";\nimport { getFgsOptions, getSelectOptionsByOrgType } from \"@/api/yxgl/bdyxgl/zbgl\";\n\nexport default {\n  name: \"qxbzk\",\n  components: { treeselect1 },\n  data() {\n    return {\n      uploadData: {\n        businessId: undefined\n      },\n      //树结构监听属性\n      filterText: \"\",\n      //标题\n      title: \"\",\n      //组织结构下拉数据\n      OrganizationSelectedList: [],\n      //所属基地站\n      ssjdzList: [],\n      //树结构上面得筛选框参数\n      treeForm: {},\n      //电压等级下拉框数据\n      VoltageLevelSelectedList: [\n        { label: \"110kV\", value: \"110\" },\n        { label: \"35kV\", value: \"35\" },\n        { label: \"10kV\", value: \"10\" },\n        { label: \"6kV\", value: \"6\" }\n      ],\n      imgList: [\n        // {\n        //   url: require('@/assets/image/bdz.png')\n        // },\n        // {\n        //   url: require('@/assets/image/bdz.png')\n        // },\n        // {\n        //   url: require('@/assets/image/bdz.png')\n        // }\n      ],\n      options: [\n        {\n          value: \"110\",\n          label: \"110kV\"\n        },\n        {\n          value: \"35\",\n          label: \"35kV\"\n        },\n        {\n          value: \"10\",\n          label: \"10kV\"\n        },\n        {\n          value: \"6\",\n          label: \"6kV\"\n        }\n      ],\n      //变电站信息是否可编辑\n      isDisabled: false,\n      //上传图片时的请求头\n      header: {},\n      //间隔信息是否显示\n      jgShow: false,\n      //设备信息展示\n      assetIsDisable: false,\n\n      //技术参数动态展示集合\n      jscsLabelList: [\n        { label: \"额定容量_高压\", jscsbm: \"edrlgy\", value: \"\", type: \"input\" },\n        {\n          label: \"产品代号\",\n          jscsbm: \"cpdh\",\n          value: \"\",\n          type: \"select\",\n          options: [\n            { label: \"下拉内容一\", value: \"1\" },\n            { label: \"下拉内容二\", value: \"2\" }\n          ]\n        },\n        { label: \"空载损耗_kW\", jscsbm: \"kzsh\", value: \"\", type: \"input\" },\n        { label: \"空载电流_%\", jscsbm: \"kzdl\", value: \"\", type: \"input\" },\n        { label: \"短路损耗中低\", jscsbm: \"dlshzd\", value: \"\", type: \"input\" },\n        { label: \"短路损耗高低\", jscsbm: \"dlshgd\", value: \"\", type: \"date\" },\n        { label: \"空载电流_%\", jscsbm: \"kzdl\", value: \"\", type: \"input\" },\n        { label: \"短路损耗中低\", jscsbm: \"dlshzd\", value: \"\", type: \"input\" },\n        { label: \"短路损耗高低\", jscsbm: \"dlshgd\", value: \"\", type: \"date\" }\n      ],\n      //技术参数绑定\n      jscsForm: {},\n      //单位下拉数据\n      deptOptions: [],\n\n      isShow1: false,\n      isShow2: false,\n      isShow3: false,\n      filterInfo: {},\n      filterInfo1: {\n        data: {\n          ywdwArr: [],\n          jhnyArr: [],\n          xlArr: \"\",\n          jhlxArr: [],\n          jhztArr: \"\",\n          sfdd: \"\"\n        },\n        fieldList: [\n          {\n            label: \"所属公司\",\n            type: \"select\",\n            value: \"roleName\",\n            multiple: true,\n            options: []\n          },\n          { label: \"变电站名称\", type: \"input\", value: \"roleKey\" },\n          {\n            label: \"电压等级\",\n            type: \"select\",\n            value: \"jhlxArr\",\n            multiple: true,\n            options: []\n          },\n          {\n            label: \"变电站类型\",\n            type: \"select\",\n            value: \"jhlxArr\",\n            multiple: true,\n            options: []\n          }\n        ]\n      },\n      //通用列表参数\n      tableAndPageInfo: {},\n      //变电站数据\n      tableAndPageInfo1: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"ssdwmc\", label: \"所属公司\", minWidth: \"120\" },\n          { prop: \"bdzmc\", label: \"变电站名称\", minWidth: \"180\" },\n          { prop: \"dydj\", label: \"电压等级\", minWidth: \"120\" },\n          { prop: \"sbzt\", label: \"设备状态\", minWidth: \"250\" },\n          { prop: \"sfsnz\", label: \"是否枢纽站\", minWidth: \"140\" },\n          { prop: \"sblx\", label: \"设备类型\", minWidth: \"120\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"120\" }\n          /*{\n              fixed: \"right\",\n              prop: 'operation',\n              label: '操作',\n              minWidth: '130px',\n              style: {display: 'block'},\n              operation: [\n                {name: '修改', clickFun: this.updatebdz},\n                {name: '详情', clickFun: this.bdzDetails},\n              ]\n            },*/\n        ]\n      },\n      jgQueryParams: {\n        ssbdz: undefined,\n        dydj: undefined,\n        pageSize: 10,\n        pageNum: 1\n      },\n      filterInfo3: {\n        data: {\n          ywdwArr: [],\n          jhnyArr: [],\n          xlArr: \"\",\n          jhlxArr: [],\n          jhztArr: \"\",\n          sfdd: \"\"\n        },\n        fieldList: [\n          {\n            label: \"所属公司\",\n            type: \"select\",\n            value: \"ywdwArr\",\n            multiple: true,\n            options: []\n          },\n          {\n            label: \"所属电站\",\n            type: \"select\",\n            value: \"jhnyArr\",\n            multiple: true,\n            options: []\n          },\n          {\n            label: \"电压等级\",\n            type: \"select\",\n            value: \"jhlxArr\",\n            multiple: true,\n            options: []\n          },\n          {\n            label: \"变电站类型\",\n            type: \"select\",\n            value: \"jhztArr\",\n            multiple: true,\n            options: []\n          }\n        ]\n      },\n\n      //设备履历tab页\n      sbllDescTabName: \"syjl\",\n\n      //设备基本信息\n      jbxxForm: {\n        attachment: [],\n        objId: undefined,\n        ssdwmc: undefined,\n        ssdwbm: undefined,\n        sbdm: undefined,\n        ddsbh: undefined,\n        dydj: undefined,\n        tyrq: undefined,\n        jgdy: undefined,\n        xb: undefined,\n        xs: undefined,\n        ccrq: undefined,\n        azwz: undefined,\n        yt: undefined,\n        fzr: undefined,\n        cpdh: undefined,\n        eddy: undefined,\n        edpl: undefined,\n        sbzt: undefined,\n        syhj: undefined,\n        sccj: undefined,\n        zzgj: undefined,\n        zhsblx: undefined,\n        zhsblxmc: undefined,\n        eddl: undefined,\n        yxbh: undefined,\n        ccbh: undefined,\n        bdzmc: undefined,\n        bdzszbh: undefined, //变电站数字编号\n        ssdw: undefined, //所属电网\n        dzlx: undefined, //电站类型\n        sfzhzdh: undefined, //是否综合自动化站\n        sfszhbdz: undefined, //是否数字化变电站\n        returnDate: undefined, //退运日期\n        zymj: undefined, //占地面积\n        whdj: undefined, //污秽等级\n        zbfs: undefined, //值班方式\n        sfgqtx: undefined, //是否光纤通讯\n        hb: undefined, //海拔\n        gcbh: undefined, //工程编号\n        sjdw: undefined, //设计单位\n        jldw: undefined, //监理单位\n        zyjb: undefined, // 电站重要级别\n        bzfs: undefined, //布置方式\n        bdzdz: undefined, //变电站地址\n        jzmj: undefined, // 建筑面积\n        phone: undefined, //联系电话\n        gcmc: undefined, // 工程名称\n        sgdw: undefined, //施工单位\n        dqtz: undefined, //地区特征\n        zgddgxq: undefined, // 最高调度管辖权\n        sfmzn: undefined, // 是否满足n-1\n        sfjrgzxt: undefined, //是否接入故障信息远传系统\n        sfjravc: undefined, //是否接入avc\n        sfjzjk: undefined, //是否集中监控\n        jkzxmc: undefined, //接入得监控中心\n        bz: undefined //备注\n      },\n      bdzqueryParams: {\n        //bm:undefined,\n        ssdwbm: undefined,\n        pageNum: 1,\n        pageSize: 10\n      },\n      assetQueryParams: {\n        ssjg: undefined,\n        pageNum: 1,\n        pageSize: 10\n      },\n      //设备详情页底部确认取消按钮控制\n      sbCommitDialogCotrol: true,\n      //弹出框tab页\n      activeTabName: \"sbDesc\",\n      //变电站展示\n      bdzShowTable: true,\n      //间隔展示\n      jgShowTable: false,\n      //设备展示\n      sbShowTable: false,\n      //设备弹出框\n      dialogFormVisible: false,\n      //变电站添加按钮弹出框\n      bdzDialogFormVisible: false,\n      //间隔添加按钮弹出框\n      jgDialogFormVisible: false,\n      //弹出框表单\n      form: {},\n\n      loading: false,\n      //组织树\n      treeOptions: [],\n\n      //变电站挂接数据\n      newTestData: [],\n\n      //删除是否可用\n      multipleSensor: true,\n      //查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        roleKey: \"\",\n        roleName: \"\",\n        status: \"\"\n      },\n      sbzt: [\n        {\n          value: \"在运\",\n          label: \"在运\"\n        },\n        {\n          value: \"停运\",\n          label: \"停运\"\n        },\n        {\n          value: \"报废\",\n          label: \"报废\"\n        }\n      ],\n      showSearch: true,\n      rules: {\n        ssdwbm: [\n          { required: true, message: \"请选择所属公司\", trigger: \"change\" }\n        ],\n        // sbdm: [{required: true, message: '请填写设备代码', trigger: 'blur'}],\n        bdzmc: [\n          { required: true, message: \"请填写变电站名称\", trigger: \"blur\" }\n        ],\n        dydjbm: [\n          { required: true, message: \"请选择电压等级\", trigger: \"blur\" }\n        ],\n        sbzt: [{ required: true, message: \"请选择设备状态\", trigger: \"blur\" }]\n      }\n    };\n  },\n  watch: {\n    //监听筛选框值发生变化进而筛选树结构\n    filterText(val) {\n      this.$refs.tree.filter(val);\n    }\n  },\n  created() {\n    //获取新的设备拓扑树\n    this.getNewTreeInfo();\n    //初始化加载时加载所有变电站信息\n    this.getFgsOptions()\n    this.getJdzOptions()\n    this.getData();\n    //初始化时加载页面内容\n    this.tableAndPageInfo = { ...this.tableAndPageInfo1 };\n  },\n  mounted() {\n    //获取token\n    this.header.token = getToken();\n  },\n  methods: {\n    //树监听事件\n    filterNode(value, data) {\n      if (!value) return true;\n      return data.label.indexOf(value) !== -1;\n    },\n    //获取新的设备拓扑树\n    getNewTreeInfo() {\n      getNewTreeInfo(this.treeForm).then(res => {\n        this.treeOptions = res.data;\n      });\n    },\n    /**\n     * 获取分公司下拉数据\n     */\n    getFgsOptions() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.OrganizationSelectedList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"fgs\") {\n            return (item.options = this.OrganizationSelectedList);\n          }\n        });\n      });\n    },\n    /**\n     * 获取基地站下拉数据\n     */\n    getJdzOptions() {\n      getSelectOptionsByOrgType(JSON.stringify(\"07\")).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.ssjdzList = res.data;\n      });\n    },\n    //旧树形数据获取\n    getTreeInfoList() {\n      getTreeInfo().then(res => {\n        this.treeOptions = res.data;\n      });\n    },\n    //列表查询\n    async getData(params) {\n      const param = { ...this.bdzqueryParams, ...params };\n      await getBdzList(param).then(res => {\n        this.tableAndPageInfo1.tableData = res.data.records;\n        this.tableAndPageInfo1.pager.total = res.data.total;\n        //给页面赋值\n        this.$nextTick(function() {\n          this.tableAndPageInfo = { ...this.tableAndPageInfo1 };\n        });\n      });\n    },\n    addBdz() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          addBdz(this.jbxxForm).then(res => {\n            if (res.code === \"0000\") {\n              this.uploadData.businessId = res.data.objId;\n              this.submitUpload();\n              this.bdzDialogFormVisible = false;\n              this.$message.success(\"操作成功\");\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getNewTreeInfo();\n              this.getData();\n            } else {\n              this.bdzDialogFormVisible = false;\n            }\n          });\n        } else {\n          return false;\n        }\n      });\n    },\n    /**\n     * 表格多选框\n     */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.objId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /**\n     * 删除变电站\n     */\n    deleteBdz() {\n      if (this.ids.length != 0) {\n        this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\"\n        }).then(() => {\n          removeBdz(this.ids).then(res => {\n            this.$message({\n              type: \"success\",\n              message: \"删除成功!\"\n            });\n            this.getNewTreeInfo();\n            this.tableAndPageInfo.pager.pageResize = \"Y\";\n            this.getData();\n          });\n        });\n      } else {\n        this.$message({\n          type: \"info\",\n          message: \"请选择至少一条数据!\"\n        });\n      }\n    },\n\n    //修改按钮\n    updatebdz(row) {\n      this.$nextTick(async function() {\n        this.clearUpload();\n        this.jbxxForm = { ...row };\n        this.jbxxForm.attachment = [];\n        await this.getFileList();\n        this.isDisabled = false;\n        this.title = \"变电站台账修改\";\n        this.bdzDialogFormVisible = true;\n      });\n    },\n    //详情方法\n    bdzDetails(row) {\n      this.$nextTick(async function() {\n        this.clearUpload();\n        this.jbxxForm = { ...row };\n        this.jbxxForm.attachment = [];\n        await this.getFileList();\n        this.bdzDialogFormVisible = true;\n        this.isDisabled = true;\n        this.title = \"变电站台账详情\";\n      });\n    },\n    //设备添加按钮\n    sbAddSensorButton() {\n      this.dialogFormVisible = true;\n    },\n    clearUpload() {\n      if (this.$refs.upload) {\n        this.$refs.upload.clearFiles();\n      }\n    },\n    //变电站添加按钮\n    bdzAddSensorButton() {\n      this.clearUpload();\n      this.imgList = [];\n      this.isDisabled = false;\n      this.bdzDialogFormVisible = true;\n      this.title = \"变电站台账新增\";\n    },\n    beforeUpload(file) {\n      const fileSize = file.size < 1024 * 1024 * 50;\n      if (!fileSize) {\n        this.$message.error(\"上传文件大小不能超过 50MB!\");\n      }\n    },\n    submitUpload() {\n      this.$refs.upload.submit();\n    },\n    /**下载附件*/\n    downloadHandle(id) {\n      downloadByBusinessId(id);\n    },\n    //每页展示数量点击事件\n    handleSizeChange() {},\n    //页码改变事件\n    handleCurrentChange() {},\n    //树点击事件\n    async handleNodeClick(data, e) {\n      if (data.identifier == \"0\") {\n        this.isShow1 = true;\n        //获取变电站数据\n        this.getData();\n      } else {\n        await this.getData({ sbdm: data.id });\n        await this.bdzDetails(this.tableAndPageInfo.tableData[0]);\n      }\n    },\n    async getFileList() {\n      let { code, data } = await getListByBusinessId({\n        businessId: this.jbxxForm.objId\n      });\n      if (code === \"0000\") {\n        this.jbxxForm.attachment = data;\n        this.imgList = data.map(item => {\n          let item1 = {};\n          item1.name = item.fileName;\n          item1.url = item.fileUrl;\n          return item1;\n        });\n      }\n    },\n    async deleteFileById(id) {\n      let { code } = await deleteById(id);\n      if (code === \"0000\") {\n        await this.getFileList();\n        this.$message({\n          type: \"success\",\n          message: \"文件删除成功!\"\n        });\n      }\n    },\n\n    removeForm() {\n      this.jbxxForm = {\n        attachment: []\n      };\n      this.$nextTick(function() {\n        this.$refs[\"form\"].clearValidate();\n      });\n      this.bdzDialogFormVisible = false;\n    },\n    //缺陷标准库新增完成\n    qxcommit() {\n      this.dialogFormVisible = false;\n      this.$message.success(\"新增成功\");\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  height: 76vh;\n  overflow: auto;\n}\n\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.box-cardList {\n  height: 70%;\n}\n\n.item {\n  width: 200px;\n  float: left;\n}\n\n#main_container_dj {\n  height: calc(100vh - 84px);\n}\n\n.aside_height {\n  height: 96%;\n}\n\n.defect .el-form-item:nth-child(odd) {\n  margin-right: 70px;\n}\n\n/*背景颜色调整*/\n#main_container_dj,\n#main_container_dj .el-aside {\n  background-color: #b4caf1;\n}\n\n///deep/ .qxlr_dialog_insert .el-dialog__header {\n//  background-color: #8eb3f5;\n//}\n//\n///deep/ .pmyBtn {\n//  background: #8eb3f5;\n//}\n\n/*/deep/ .add_sy_tyrq .el-input--medium .el-input__inner {*/\n/*  width: 200px;*/\n/*}*/\n\n/*添加弹出框得宽度*/\n/*/deep/ .el-input--medium .el-input__inner {*/\n/*  width: 200px;*/\n/*}*/\n.el-select {\n  width: 100%;\n}\n\n.el-date-editor {\n  width: 100%;\n}\n\n.el-carousel__item h3 {\n  color: #475669;\n  font-size: 14px;\n  opacity: 0.75;\n  line-height: 150px;\n}\n\n.el-carousel__item:nth-child(2n) {\n  background-color: #99a9bf;\n}\n\n.el-carousel__item:nth-child(2n + 1) {\n  background-color: #d3dce6;\n}\n\n/* 设置滚动条的样式 */\n::-webkit-scrollbar {\n  width: 12px;\n}\n\n/* 滚动槽 */\n::-webkit-scrollbar-track {\n  //-webkit-box-shadow:inset006pxrgba(0,0,0,0.3);\n  border-radius: 10px;\n}\n\n/* 滚动条滑块 */\n::-webkit-scrollbar-thumb {\n  border-radius: 10px;\n  background: rgba(0, 0, 0, 0.1);\n  //-webkit-box-shadow:gba(0,0,0,0.5);\n}\n\n::-webkit-scrollbar-thumb:window-inactive {\n  background: rgba(0, 0, 0, 0.1);\n}\n\n[data-v-67a974b1]::-webkit-scrollbar {\n  width: 8px;\n}\n\n.item {\n  width: 225px;\n  float: left;\n}\n\n/deep/ .box-card {\n  margin: 0 6px;\n}\n\n//有子节点 且未展开\n.el-tree ::v-deep .el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/add.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n//有子节点 且已展开\n.el-tree\n  ::v-deep\n  .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/prep.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n//没有子节点\n.el-tree ::v-deep .el-tree-node__expand-icon.is-leaf::before {\n  background: transparent;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n</style>\n<style>\n#pic_form .el-form-item__content {\n  margin-left: 0 !important;\n}\n</style>\n"]}]}