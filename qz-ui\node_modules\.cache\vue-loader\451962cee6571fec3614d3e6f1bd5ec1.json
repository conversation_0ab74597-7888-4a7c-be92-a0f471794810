{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk.vue", "mtime": 1706897323430}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogInF4YnprIiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy/nu4Tnu4fmoJEKICAgICAgdHJlZU9wdGlvbnM6WwogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5pat6Lev5ZmoJywKICAgICAgICB9LCB7CiAgICAgICAgICBsYWJlbDogJ+WPmOWOi+WZqCcsCiAgICAgICAgICBjaGlsZHJlbjogW3sKICAgICAgICAgICAgbGFiZWw6ICflhrfljbTns7vnu58nLAogICAgICAgICAgICBjaGlsZHJlbjogW3sKICAgICAgICAgICAgICBsYWJlbDogJ+a4qeaOp+i/kOihjOaDheWGtScsCgogICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgbGFiZWw6ICfmsrnnrrEnLAoKICAgICAgICAgICAgfSwgewogICAgICAgICAgICAgIGxhYmVsOiAn6ZOB6IqvJywKCiAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICBsYWJlbDogJ+e7lee7hCcsCgogICAgICAgICAgICB9XQogICAgICAgICAgfV0KICAgICAgICB9XSwKICAgICAgLy/liKDpmaTmmK/lkKblj6/nlKgKICAgICAgbXVsdGlwbGVTZW5zb3I6IHRydWUsCiAgICAgIC8v5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOnsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMAogICAgICB9CgoKICAgIH07CiAgfSwKICB3YXRjaDogewogIH0sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpOwoKICB9LAogIG1ldGhvZHM6IHsKICAgIC8v5qCR6IqC54K554K55Ye75LqL5Lu2CiAgICBoYW5kbGVOb2RlQ2xpY2soZGF0YSkgewoKICAgIH0sCiAgICAvL+a3u+WKoOaMiemSrgogICAgYWRkU2Vuc29yQnV0dG9uKCl7CgogICAgfSwKICAgIC8v57yW6L6R5oyJ6ZKuCiAgICB1cGRhdGVTZW5zb3JCdXR0b24oKXsKCiAgICB9LAogICAgLy/liKDpmaTmjInpkq4KICAgIGRlbGV0ZVNlbnNvckJ1dHRvbigpewoKICAgIH0sCiAgICAvL+WvvOWHuuaMiemSrgogICAgaGFuZGxlRXhwb3J0KCl7CgogICAgfSwKICAgIC8v5p+l6K+i5YiX6KGoCiAgICBnZXRMaXN0KCl7CgogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["sbztpjbzk.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "sbztpjbzk.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl", "sourcesContent": ["<template>\n  <el-container id=\"main_container_dj\">\n    <el-aside>\n      <el-card class=\"box-card aside_height\" shadow=\"never\">\n        <div slot=\"header\" class=\"clearfix\">\n          <span>设备</span>\n        </div>\n        <div class=\"text item head-container\">\n          <el-col>\n            <el-tree :expand-on-click-node=\"false\"\n                     id=\"tree\"\n                     :data=\"treeOptions\"\n                     :default-expanded-keys=\"['1']\"\n                     @node-click=\"handleNodeClick\"\n                     node-key=\"nodeId\"/>\n          </el-col>\n        </div>\n      </el-card>\n    </el-aside>\n    <el-container>\n      <el-main>\n        <el-card class=\"box-card\" shadow=\"never\" ref=\"search\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>操作区域</span>\n          </div>\n          <div>\n            <el-col :span=\"24\" :xs=\"24\">\n              <div class=\"search-condition\">\n                <el-row :gutter=\"10\" class=\"mb8 pull-right\">\n                  <el-col :span=\"1.5\">\n                    <el-button class=\"mb8\" @click=\"addSensorButton\"\n                               type=\"primary \" icon=\"el-icon-plus\"\n                    >添加\n                    </el-button>\n                  </el-col>\n                  <el-col :span=\"1.5\">\n                    <el-button  class=\"mb8\" @click=\"updateSensorButton\"\n                                type=\"warning \" icon=\"el-icon-edit\"\n                    >修改</el-button>\n                  </el-col>\n                  <el-col :span=\"1.5\">\n                    <el-button class=\"mb8\" @click=\"deleteSensorButton\"\n                               type=\"danger\" icon=\"el-icon-delete\"\n                               :disabled=\"multipleSensor\">删除\n                    </el-button>\n                  </el-col>\n                  <el-col :span=\"1.5\">\n                    <el-button class=\"el-button--cyan\" icon=\"el-icon-download\" @click=\"handleExport\">导出</el-button>\n                  </el-col>\n                </el-row>\n                <div class=\"clearfix\" />\n              </div>\n            </el-col>\n          </div>\n        </el-card>\n\n        <el-card class=\"box-card\" shadow=\"never\">\n          <el-table stripe border v-loading=\"loading\" :data=\"userList\" @selection-change=\"handleSelectionChange\">\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n            <el-table-column label=\"缺陷程度编码\" align=\"center\" prop=\"userId\"/>\n            <el-table-column label=\"隐患等级\" align=\"center\" prop=\"userName\" :show-overflow-tooltip=\"true\"/>\n            <el-table-column label=\"缺陷程度\" align=\"center\" prop=\"nickName\" :show-overflow-tooltip=\"true\"/>\n          </el-table>\n\n          <pagination v-show=\"total>0\"\n                      :total=\"total\"\n                      :page.sync=\"queryParams.pageNum\"\n                      :limit.sync=\"queryParams.pageSize\"\n                      @pagination=\"getList\"/>\n        </el-card>\n        <el-footer>\n        </el-footer>\n      </el-main>\n    </el-container>\n\n  </el-container>\n\n</template>\n\n<script>\n\n  export default {\n    name: \"qxbzk\",\n    data() {\n      return {\n        //组织树\n        treeOptions:[\n          {\n            label: '断路器',\n          }, {\n            label: '变压器',\n            children: [{\n              label: '冷却系统',\n              children: [{\n                label: '温控运行情况',\n\n              }, {\n                label: '油箱',\n\n              }, {\n                label: '铁芯',\n\n              }, {\n                label: '绕组',\n\n              }]\n            }]\n          }],\n        //删除是否可用\n        multipleSensor: true,\n        //查询参数\n        queryParams:{\n          pageNum: 1,\n          pageSize: 10\n        }\n\n\n      };\n    },\n    watch: {\n    },\n    created() {\n      this.getList();\n\n    },\n    methods: {\n      //树节点点击事件\n      handleNodeClick(data) {\n\n      },\n      //添加按钮\n      addSensorButton(){\n\n      },\n      //编辑按钮\n      updateSensorButton(){\n\n      },\n      //删除按钮\n      deleteSensorButton(){\n\n      },\n      //导出按钮\n      handleExport(){\n\n      },\n      //查询列表\n      getList(){\n\n      }\n    }\n  };\n</script>\n\n<style lang='scss' scoped>\n  .head-container{\n    margin: 0 auto;\n    width: 98%;\n    max-height: 79vh;\n    overflow: auto;\n  }\n  .box-card{\n    margin-bottom: 15px;\n    .el-card__header{\n      background-color: rgb(235, 245, 255) !important;\n    }\n  }\n  .item{\n    width: 200px;height: 148px; float: left;\n  }\n</style>\n"]}]}