{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\lpbzk\\czpzysyk.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\lpbzk\\czpzysyk.vue", "mtime": 1706897322614}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["czpzysyk.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAsDA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;AACA,EAAA,IAAA,EAAA,UADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,IAAA,EAAA;AACA,QAAA,KAAA,EAAA,EADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OADA;AAMA,MAAA,OAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAAA,EAAA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAAA,CANA;AAOA,MAAA,KAAA,EAAA,EAPA;AAQA,MAAA,IAAA,EAAA,KARA;AASA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,IAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA;AAFA,SADA;AAKA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,CAAA;AAAA,YAAA,KAAA,EAAA,KAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAAA,EAAA;AAAA,YAAA,KAAA,EAAA,KAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA;AALA,OATA;AAmBA,MAAA,UAAA,EAAA,KAnBA;AAoBA,MAAA,UAAA,EAAA,EApBA;AAqBA,MAAA,gBAAA,EAAA;AAEA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SAFA;AASA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SATA;AAaA,QAAA,SAAA,EAAA,EAbA;AAcA,QAAA,WAAA,EAAA,CAEA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AACA,UAAA,IAAA,EAAA,WADA;AAEA,UAAA,KAAA,EAAA,IAFA;AAGA,UAAA,QAAA,EAAA,OAHA;AAIA,UAAA,KAAA,EAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WAJA;AAKA;AACA,UAAA,KAAA,EAAA,OANA;AAOA,UAAA,SAAA,EAAA,CACA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WADA,EAEA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WAFA;AAPA,SAJA;AAdA,OArBA;AAqDA;AACA,MAAA,WAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA;AADA,OADA,EAGA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,QAAA,EAAA,CAAA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,QAAA,EAAA,CAAA;AACA,YAAA,KAAA,EAAA;AADA,WAAA,EAEA;AACA,YAAA,KAAA,EAAA;AADA,WAFA,EAKA;AACA,YAAA,KAAA,EAAA;AADA,WALA,EAQA;AACA,YAAA,KAAA,EAAA;AADA,WARA;AAFA,SAAA;AAFA,OAHA,CAtDA;AA2EA;AACA,MAAA,cAAA,EAAA,IA5EA;AA6EA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,SAAA,EAAA;AAHA,OA9EA;AAmFA;AACA,MAAA,KAAA,EAAA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAJA,OApFA;AA4FA;AACA,MAAA,YAAA,EAAA,KA7FA;AA8FA;AACA,MAAA,cAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAIA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAJA,CA/FA;AAyGA,MAAA,IAAA,EAAA,CACA;AACA,QAAA,EAAA,EAAA,CADA;AAGA,QAAA,KAAA,EAAA,OAHA;AAKA,QAAA,IAAA,EAAA;AALA,OADA,EAQA;AACA,QAAA,EAAA,EAAA,CADA;AAGA,QAAA,KAAA,EAAA,OAHA;AAKA,QAAA,IAAA,EAAA;AALA,OARA,EAeA;AACA,QAAA,EAAA,EAAA,CADA;AAGA,QAAA,KAAA,EAAA,OAHA;AAKA,QAAA,IAAA,EAAA;AALA,OAfA,EAsBA;AACA,QAAA,EAAA,EAAA,CADA;AAGA,QAAA,KAAA,EAAA,OAHA;AAKA,QAAA,IAAA,EAAA;AALA,OAtBA,CAzGA;AAuIA,MAAA,MAAA,EAAA;AAvIA,KAAA;AA2IA,GA9IA;AA+IA,EAAA,KAAA,EAAA,EA/IA;AAiJA,EAAA,OAjJA,qBAiJA,CAEA,CAnJA;AAoJA,EAAA,OApJA,qBAoJA;AACA,SAAA,OAAA;AACA,GAtJA;AAuJA,EAAA,OAAA,EAAA;AACA,IAAA,OADA,mBACA,MADA,EACA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,oBAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,IAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA;AACA;;AACA,gBAAA,KAJA,+DAIA,KAAA,CAAA,MAJA,GAIA,MAJA;AAAA;AAAA,uBAKA,uBAAA,KAAA,CALA;;AAAA;AAAA;AAKA,gBAAA,IALA,kBAKA,IALA;AAKA,gBAAA,IALA,kBAKA,IALA;;AAMA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,KAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,KAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AATA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KAZA;AAaA;AACA,IAAA,eAdA,2BAcA,IAdA,EAcA,CAEA,CAhBA;AAiBA;AACA,IAAA,eAlBA,6BAkBA;AACA,WAAA,IAAA,GAAA,IAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,IAAA,GAAA;AACA,QAAA,KAAA,EAAA,EADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAKA,KAAA,KAAA,GAAA,IALA;AAMA,KA3BA;AA4BA,IAAA,cA5BA,4BA4BA;AACA,WAAA,IAAA,GAAA,KAAA;AACA,KA9BA;AA+BA;AACA,IAAA,SAhCA,qBAgCA,GAhCA,EAgCA;AACA,WAAA,KAAA,GAAA,IAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,IAAA,GAAA,IAAA;AACA,KArCA;AAsCA;AACA,IAAA,OAvCA,mBAuCA,GAvCA,EAuCA;AACA,WAAA,KAAA,GAAA,IAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,IAAA,GAAA,IAAA;AACA,KA5CA;AA6CA,IAAA,OA7CA,qBA6CA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,MAAA,CAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,sBAAA,KAAA,EAAA;AACA,gDAAA,MAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,0BAAA;AACA,4BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,0BAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA;AACA,uBAJA,CAIA,OAAA,CAAA,EAAA;AACA,wBAAA,OAAA,CAAA,GAAA,CAAA,CAAA;AACA;;AACA,sBAAA,MAAA,CAAA,OAAA;AACA,qBATA;AAUA,mBAXA,MAWA;AACA,2BAAA,KAAA;AACA;;AACA,kBAAA,MAAA,CAAA,IAAA,GAAA,KAAA;AACA,iBAhBA;;AAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmBA,KAhEA;AAiEA;AACA,IAAA,SAlEA,uBAkEA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBACA,MAAA,CAAA,UAAA,CAAA,MAAA,GAAA,CADA;AAAA;AAAA;AAAA;;AAEA,gBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,aAAA;;AAFA;;AAAA;AAKA,gBAAA,GALA,GAKA,MAAA,CAAA,UAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AAAA,yBAAA,IAAA,CAAA,KAAA;AAAA,iBAAA,CALA;;AAMA,gBAAA,MAAA,CAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAIA,IAJA,CAIA,YAAA;AACA,wCAAA,GAAA,EAAA,IAAA,CAAA,gBAAA;AAAA,wBAAA,IAAA,QAAA,IAAA;;AACA,wBAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,SADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;;AAIA,sBAAA,MAAA,CAAA,OAAA;AACA,qBANA,MAMA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,OADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;AAIA;AACA,mBAbA;AAcA,iBAnBA,EAmBA,KAnBA,CAmBA,YAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA;AACA,oBAAA,IAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;AAIA,iBAxBA;;AAyBA,gBAAA,MAAA,CAAA,OAAA;;AA/BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgCA,KAlGA;AAmGA,IAAA,YAnGA,wBAmGA,IAnGA,EAmGA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KArGA;AAsGA;AACA,IAAA,YAvGA,0BAuGA,CAEA,CAzGA;AA0GA;AACA,IAAA,OA3GA,qBA2GA,CAEA,CA7GA;AA8GA;AACA,IAAA,WA/GA,yBA+GA,CAEA,CAjHA;AAkHA;AACA,IAAA,UAnHA,wBAmHA;AACA,WAAA,SAAA,CAAA,WAAA;AACA,KArHA;AAsHA;AACA,IAAA,WAvHA,yBAuHA;AAAA;;AACA,WAAA,IAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,YAAA;AACA,QAAA,MAAA,CAAA,IAAA,GAAA,MAAA,CAAA,QAAA,CAAA,IAAA,GAAA,IAAA;;AACA,QAAA,MAAA,CAAA,SAAA,CAAA,MAAA;AACA,OAHA;AAIA;AA7HA;AAvJA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n        />\n    <div>\n      <el-white class=\"button-group\">\n        <div class=\"button_btn\">\n          <el-button @click=\"addSensorButton\"\n                     type=\"primary \" icon=\"el-icon-plus\"\n          >新增\n          </el-button>\n\n          <el-button class=\"mb8\" @click=\"deleteRow\"\n                     type=\"danger\" icon=\"el-icon-delete\"\n          >删除\n          </el-button>\n        </div>\n      </el-white>\n      <comp-table :table-and-page-info=\"tableAndPageInfo\"  @update:multipleSelection=\"selectChange\"/>\n    </div>\n    <!--新增、修改、详情弹框-->\n    <el-dialog :title=title :visible.sync=\"show\" width=\"30%\" append-to-body @close=\"handleClose\" v-dialogDrag>\n      <el-form label-width=\"130px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row :gutter=\"8\" class=\"pull-left\" >\n          <el-col :span=\"24\">\n            <el-form-item  label=\"操作类型：\" prop=\"czlx\">\n              <el-select placeholder=\"请选择操作类型\" v-model=\"form.czlx\" :disabled=\"isDisabled\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item  label=\"操作描述：\" prop=\"czms\">\n              <el-input v-model=\"form.czms\" placeholder=\"请输入操作描述\"  :disabled=\"isDisabled\"/>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"getInsterClose\">取 消</el-button>\n        <el-button v-if=\"title==='新增' || title==='修改'\" type=\"primary\" @click=\"saveRow\">确 认</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  import {getList,saveOrUpdate,remove,exportExcel} from '@/api/dagangOilfield/bzgl/lpbzk/czpzysyk'\n  export default {\n    name: \"czpzysyk\",\n    data() {\n      return {\n        form:{\n          objId:'',\n          czlx:'',\n          czms:''\n        },\n        options:[{label:'类型1',value:'类型1'},{label:'类型2',value:'类型2'}],\n        title:'',\n        show:false,\n        filterInfo: {\n          data: {\n            czms: '',\n            czlx: [],\n          },\n          fieldList: [\n            {label: '操作类型', type: 'select', value: 'czlx', multiple: true, options:[{label:'类型1',value:'类型1'},{label:'类型2',value:'类型2'}]},\n            {label: '操作描述', type: 'input', value: 'czms'},\n          ]\n        },\n        isDisabled:false,\n        selectRows:[],\n        tableAndPageInfo: {\n\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n\n            {prop: 'czlx', label:'操作类型', minWidth: '180'},\n            {prop: 'czms', label: '操作描述', minWidth: '180'},\n            {\n              prop: 'operation',\n              label: '操作',\n              minWidth: '130px',\n              style: {display: 'block'},\n              //操作列固定再右侧\n              fixed:'right',\n              operation: [\n                {name: '修改', clickFun: this.updateRow},\n                {name: '详情', clickFun: this.getInfo},\n              ]\n            },\n          ]\n        },\n        //组织树\n        treeOptions:[\n          {\n            label: '断路器',\n          }, {\n            label: '变压器',\n            children: [{\n              label: '冷却系统',\n              children: [{\n                label: '温控运行情况',\n              }, {\n                label: '油箱',\n\n              }, {\n                label: '铁芯',\n\n              }, {\n                label: '绕组',\n\n              }]\n            }]\n          }],\n        //删除是否可用\n        multipleSensor: true,\n        //查询参数\n        queryParams:{\n          pageNum: 1,\n          pageSize: 10,\n          ancuoTerm:''\n        },\n        //填入数据校验\n        rules: {\n          czlx: [\n            { required: true, message: '操作类型不能为空', trigger: 'change' }\n          ],\n          czms:[\n            { required: true, message: '操作描述不能为空', trigger: 'change' }\n          ]\n        },\n        //表单开关\n        isSearchShow:false,\n        //工作票类型下拉菜单\n        gzpTypeOptions:[\n          {\n            value: 'type1',\n            label: '类型1'\n          }, {\n            value: 'type2',\n            label: '类型2'\n          }\n        ],\n\n        list: [\n          {\n            id: 1,\n\n            gzplx: \"合上、拉开\",\n\n            acdy: \"合上xx开关\",\n          },\n          {\n            id: 2,\n\n            gzplx: \"合上、拉开\",\n\n            acdy: \"拉开xx开关\",\n          },\n          {\n            id: 3,\n\n            gzplx: \"投入、退出\",\n\n            acdy: \"投入xx压板\",\n          },\n          {\n            id: 4,\n\n            gzplx: \"投入、退出\",\n\n            acdy: \"退出xx压板\",\n          }\n        ],\n      params:{\n\n      }\n      };\n    },\n    watch: {\n    },\n    created() {\n\n    },\n    mounted() {\n      this.getData();\n    },\n    methods: {\n      async getData(params){\n         if (params){\n           params.czlx = params.czlx.join(',')\n         }\n          const param={...this.params,...params}\n          const {data,code} = await getList(param);\n          if(code==='0000'){\n            this.tableAndPageInfo.tableData=data.records\n            this.tableAndPageInfo.pager.total=data.total\n          }\n\n      },\n      //树节点点击事件\n      handleNodeClick(data) {\n\n      },\n      //添加按钮\n      addSensorButton(){\n        this.show=true\n        this.isDisabled=false\n        this.form={\n            objId:'',\n            czlx:'',\n            czms:''\n        },\n        this.title = '新增'\n      },\n      getInsterClose(){\n        this.show=false\n      },\n      //编辑按钮\n      updateRow(row){\n        this.title = '修改'\n        this.isDisabled=false\n        this.form={...row}\n        this.show=true\n      },\n      //详情按钮\n      getInfo(row){\n        this.title = '详情'\n        this.form={...row}\n        this.isDisabled=true\n        this.show=true\n      },\n      async saveRow(){\n\n        this.$refs['form'].validate(valid => {\n          if (valid) {\n            saveOrUpdate(this.form).then(res => {\n              try {\n                if (res.code === '0000') {\n                  this.$message.success('操作成功')\n                }\n              } catch (e) {\n                console.log(e)\n              }\n              this.getData()\n            })\n          } else {\n            return false\n          }\n          this.show =  false\n        })\n      },\n      //删除按钮\n      async deleteRow(){\n        if(this.selectRows.length<1){\n          this.$message.warning(\"请选择正确的数据！！！\")\n          return\n        }\n        let ids=this.selectRows.map(item=>{return item.objId});\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove(ids).then(({code })=>{\n            if(code==='0000'){\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              });\n              this.getData()\n            }else{\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              });\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          });\n        });\n        this.getData()\n      },\n      selectChange(rows){\n        this.selectRows=rows\n      },\n      //导出按钮\n      handleExport(){\n\n      },\n      //查询列表\n      getList(){\n\n      },\n      //搜索\n      handleQuery(){\n\n      },\n      //重置\n      resetQuery(){\n        this.resetForm(\"queryForm\");\n      },\n      //清空表单数据\n      handleClose(){\n        this.form={};\n        this.$nextTick(() => {\n          this.form = this.$options.data().form;\n          this.resetForm(\"form\");\n        });\n      },\n    }\n  };\n</script>\n\n<style lang='scss' scoped>\n  .box-card{\n    margin-bottom: 15px;\n    .el-card__header{\n      background-color: rgb(235, 245, 255) !important;\n    }\n  }\n  .item{\n    width: 200px;height: 148px; float: left;\n  }\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/lpbzk"}]}