{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\Index\\SbInfo.vue?vue&type=style&index=0&id=7a4c5a6c&scoped=true&lang=scss&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\Index\\SbInfo.vue", "mtime": 1755539914743}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["SbInfo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6LA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "SbInfo.vue", "sourceRoot": "src/components/Index", "sourcesContent": ["<template>\n  <div :sbInfoSpanNum=\"sbInfoSpanNum\" class=\"borderCls\" :class=\"sbIDivClass\">\n    <div style=\"height: 90%\">\n      <div class=\"txtTitle\">\n        <span class=\"txtContent\">设备基本信息</span>\n      </div>\n      <div class=\"sbInfo_cont\">\n        <ul>\n          <li>\n            <i class=\"icon bd\"></i>变电\n          </li>\n          <li class=\"cont_child\"><span class=\"fontCls\">变电站：</span><span>个</span><span>{{this.bdzNum}}</span></li>\n          <li class=\"cont_child\"><span class=\"fontCls\">变压器：</span><span>个</span><span>{{this.byqNum}}</span></li>\n<!--          <li class=\"cont_child\"><span>总容量：</span><span>个</span><span>336</span></li>-->\n        </ul>\n        <ul>\n          <li><i class=\"icon line\"></i>线路</li>\n          <li class=\"cont_child\"><span class=\"fontCls\">线路数：</span><span>条</span><span>{{this.xlNum}}</span></li>\n          <li class=\"cont_child\"><span class=\"fontCls\">长度：</span><span>Km</span><span>{{this.xlcd}}</span></li>\n          <li class=\"cont_child gts\"><span class=\"fontCls\">杆塔数：</span><span>个</span><span>{{this.gtNum}}</span></li>\n          <li class=\"cont_child gts\"><span class=\"fontCls\">电缆长度：</span><span>Km</span><span>{{this.gtcd}}</span></li>\n        </ul>\n        <ul>\n          <li><i class=\"icon pds\"></i>配电室</li>\n          <li class=\"cont_child\"><span class=\"fontCls\">配电室：</span><span>座</span><span>{{this.pdsNum}}</span></li>\n          <li class=\"cont_child\"><span class=\"fontCls\">配电柜：</span><span>个</span><span>{{this.pdgNum}}</span></li>\n        </ul>\n        <ul>\n          <li>\n            <i class=\"icon bd\"></i>新能源\n          </li>\n          <li class=\"cont_child\"><span class=\"fontCls\">光伏电站：</span><span>个</span><span>{{this.bdzNum}}</span></li>\n        </ul>\n      </div>\n<!--      <div  ref=\"gdchart\" class=\"tjHeight\">\n      </div>-->\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nimport { mapState } from 'vuex'\nimport {getSbInfo} from '@/api/index/SbInfo'\n\nexport default {\n  name: 'SbInfo',//工单完成情况\n  props:{\n    sbInfoSpanNum:{\n      type:Number,\n      default:7,\n    },\n    sbIDivClass:'',\n  },\n  data() {\n    return {\n      //用于布局动态设置高度\n      activeClass:1,\n      tjCharts:null,//统计图对象\n      //默认值\n      fpNum:[302,120,278],\n      finNum:[150,100,234],\n      bdzNum:null,//变电站\n      byqNum:null,//变压器\n      xlcd:null,//线路长度\n      xlNum:null,//线路数量\n      gtNum:null,//杆塔数量\n      gtcd:null,//电缆长度\n      pdsNum:null,//配电室数量\n      pdgNum:null,//配电柜数量\n    }\n  },\n  mounted() {\n   // this.showGdCharts();\n   this.showSbInfo();\n  },\n  methods: {\n    //工单完成情况\n    showGdCharts(){\n      let bar_dv = this.$refs.gdchart;\n      let myChart = echarts.init(bar_dv);\n      this.tjCharts = myChart;\n\n      let option;\n      option = {\n        title: {\n          subtext: '单位：个',\n          left: '4%'\n        },\n        legend: {\n          top:'3%',\n          right: '6%',\n          textStyle:{\n            fontSize:16,\n          }\n        },\n        grid: {\n          left: '4%',\n          right: '4%',\n          bottom: '1%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'category',\n          data:['变电', '线路', '配电']\n        },\n        yAxis: {},\n        series: [\n          {\n            type: 'bar' ,\n            name:'分配工单数',\n            barWidth:24,\n            itemStyle: {\n              color: new echarts.graphic.LinearGradient(\n                0, 0, 0, 1,\n                [\n                  {offset: 0, color: '#11ba6d'},\n                  {offset: 1, color: '#b5e0cd'}\n                ]\n              )\n            },\n            data:this.fpNum,\n          },\n          {\n            type: 'bar',\n            name:'已完成工单数',\n            barWidth:24,\n            itemStyle: {\n              color: new echarts.graphic.LinearGradient(\n                0, 0, 0, 1,\n                [\n                  {offset: 0, color: '#ff9a09'},\n                  {offset: 1, color: '#faead3'}\n                ]\n              )\n            },\n            data:this.finNum\n          }\n        ]\n      };\n      option && myChart.setOption(option);\n    },\n    //重新加载eCharts图表\n    reloadCharts(){\n      this.tjCharts.resize();\n    },\n    async showSbInfo(){\n      let {data,code} = await getSbInfo()\n      this.bdzNum=data.BdzTj//变电站\n      this.byqNum=data.PdByqTj,//变压器\n      this.xlcd= Number(data.cd).toFixed(2),//线路长度\n      this.xlNum=data.xl,//线路数量\n      this.gtNum=data.tower,//杆塔数量\n      this.gtcd=(data.dl/1000).toFixed(2),//电缆长度\n      this.pdsNum=data.pdz,//配电室数量\n      this.pdgNum=data.pdg//配电柜数量\n      this.gfdzNum=data.gfxdz//光伏电站数量\n    }\n  },\n  computed: {\n    ...mapState([\"settings\",\"app\"]),\n    //工作票完成情况\n    workOrder() {\n      return this.$store.state.settings.workOrder;\n    },\n    //菜单伸缩状态\n    opened() {\n      return this.$store.state.app.sidebar.opened;\n    },\n  },\n  watch:{\n    workOrder(newVal){\n      if(newVal){\n        this.reloadCharts();\n      }\n    },\n    wkoSpanNum(newVal){\n      this.reloadCharts();\n    },\n    opened(newVal) {\n      //重新加载统计图\n      setTimeout(()=>{\n        //this.tjCharts.resize();\n      },200)\n    }\n  }\n}\n</script>\n<style scoped lang=\"scss\">\n.fontCls{\n  font-weight: 800;\n}\n.spanTxt{\n  float: right;\n  padding-right: 4%;\n  font-size: 16px;\n  color: #b1b1b1;\n  font-weight: 500;\n}\n.sbInfo_cont{\n  height: 100%;\n  padding:0 0 12px 12px;\n  .icon{\n    display: inline-block;\n    width: 17px;\n    height: 17px;\n    margin-right: 10px;\n  }\n  .bd{\n    background: url('../../assets/image/equ_icon1.png') no-repeat;\n    background-size: 100% 100%;\n  }\n  .line{\n    background: url('../../assets/image/equ_icon2.png') no-repeat;\n    background-size: 100% 100%;\n  }\n  .pds{\n    background: url('../../assets/image/equ_icon3.png') no-repeat;\n    background-size: 100% 100%;\n  }\n  ul{\n    list-style-type: none;\n    margin:0;\n    padding:0;\n    li:first-child{\n      line-height: 37px;\n      font-weight: 600;\n      color: #4d4d4d;\n      font-size: 18px;\n    }\n  }\n  .cont_child{\n    color: #b1b1b1;\n    width: 46%;\n    height: 30px;\n    line-height: 30px;\n    display: inline-block;\n    margin-right: 15px;\n    padding: 0 4px;\n    box-shadow: #e7e0e0 0.5px 0.5px 0.5px 1px;\n    span:first-child{\n      float: left;\n     /* width: 80px;*/\n    }\n    span:nth-child(2){\n      float:right;\n      margin-right: 0;\n  /*    max-width: 26px;*/\n    }\n    span:last-child{\n      color:#4d4d4d;\n      font-size: 25px;\n      float: right;\n/*      max-width: 89px;*/\n    }\n  }\n  .gts{\n    margin-top:10px\n  }\n}\n.txtContent{\n  width: 142px;\n}\n</style>\n\n"]}]}