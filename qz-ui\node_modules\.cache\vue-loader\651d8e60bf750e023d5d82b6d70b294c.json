{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\bdgl\\bdsbgl.vue?vue&type=template&id=47374033&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\bdgl\\bdsbgl.vue", "mtime": 1706897324519}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}