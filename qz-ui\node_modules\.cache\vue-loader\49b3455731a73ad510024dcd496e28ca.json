{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\ysbzk1.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\ysbzk1.vue", "mtime": 1706897323967}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBkaWFsb2dGb3JtIGZyb20gJ2NvbS9kaWFsb2dGcm9tL2RpYWxvZ0Zvcm0nCmltcG9ydCB7IGRlbGV0ZUJ6WXNienpiLCBnZXRCellzYnp6Yiwgc2F2ZU9yVXBkYXRlQnpZc2J6emIgfSBmcm9tICdAL2FwaS9iemdsL3lzYnprL3lzYnprJwppbXBvcnQgQWNjZXB0YW5jZURldGFpbCBmcm9tICdAL3ZpZXdzL2RhZ2FuZ09pbGZpZWxkL2J6Z2wveXNiemtnbC9hY2NlcHRhbmNlRGV0YWlsMScKaW1wb3J0IERldmljZVRyZWUgZnJvbSAnQC92aWV3cy9kYWdhbmdPaWxmaWVsZC9iemdsL3NiYnprL2RldmljZVRyZWUnCmltcG9ydCB7IGdldERpY3RUeXBlRGF0YSB9IGZyb20gJ0AvYXBpL3N5c3RlbS9kaWN0L2RhdGEnCgpleHBvcnQgZGVmYXVsdCB7CiAgY29tcG9uZW50czogeyBEZXZpY2VUcmVlLCBBY2NlcHRhbmNlRGV0YWlsLCBkaWFsb2dGb3JtIH0sCiAgbmFtZTogJ3lzYnprJywKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZmlsdGVyUGFyYW1zOiB7fSwKICAgICAgZmlsdGVySW5mbzogewogICAgICAgIGRhdGE6IHsKICAgICAgICAgIHNiZmw6IiIsCiAgICAgICAgICBzYmZsbWM6IiIKICAgICAgICB9LC8v5p+l6K+i5p2h5Lu2CiAgICAgICAgZmllbGRMaXN0OiBbCgogICAgICAgICAgeyBsYWJlbDogJ+iuvuWkh+WIhuexuycsIHR5cGU6ICdpbnB1dFlzYnprJywgdmFsdWU6ICdzYmZsbWMnLCBuYW1lOiAnc2JmbCcgfSwKICAgICAgICAgIHsgbGFiZWw6ICfpqozmlLblkI3np7AnLCB0eXBlOiAnaW5wdXQnLCB2YWx1ZTogJ2p4ZmwnIH0sCiAgICAgICAgICB7IGxhYmVsOiAn6aG555uu5ZCN56ewJywgdHlwZTogJ2lucHV0JywgdmFsdWU6ICd4bW1jJyB9LAogICAgICAgICAgeyBsYWJlbDogJ+Wkh+azqCcsIHR5cGU6ICdpbnB1dCcsIHZhbHVlOiAnYnonIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAn6aqM5pS257G75Z6LJywKICAgICAgICAgICAgdHlwZTogJ2NoZWNrYm94JywKICAgICAgICAgICAgY2hlY2tib3hWYWx1ZTogW10sCiAgICAgICAgICAgIHZhbHVlOiAneXNseCcsCiAgICAgICAgICAgIG9wdGlvbnM6IFsKICAgICAgICAgICAgICB7IGxhYmVsOiAn55S15rCU6K6+5aSH57G7JywgdmFsdWU6ICdkcXNibCcgfSwKICAgICAgICAgICAgICB7IGxhYmVsOiAn5Zyf5bu66K6+5pa957G7JywgdmFsdWU6ICd0anNzbCcgfSwKICAgICAgICAgICAgICB7IGxhYmVsOiAn5YW25LuWJywgdmFsdWU6ICdxdCcgfQogICAgICAgICAgICBdLAogICAgICAgICAgICBjbGVhcmFibGU6IHRydWUKICAgICAgICAgIH0sCiAgICAgICAgXQogICAgICB9LAoKICAgICAgY3VycmVudFVzZXI6IHRoaXMuJHN0b3JlLmdldHRlcnMubmFtZSwKICAgICAgLy/mlrDlop7miJbkv67mlLnmoIfpopgKICAgICAgcmVtaW5kZXI6ICfkv67mlLknLAogICAgICAvL+W8ueWHuuahhmZvcm3ooajljZXmr4/ooYzmmL7npLrlh6DkuKoKICAgICAgcm93czogMiwKICAgICAgLy/kuLvooajmlrDlop7ooajljZXmlbDmja4KICAgICAgZm9ybUxpc3Q6IFsKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+mqjOaUtuexu+Wei++8micsCiAgICAgICAgICB2YWx1ZTogJycsCiAgICAgICAgICB0eXBlOiAnc2VsZWN0JywKICAgICAgICAgIG5hbWU6ICd5c2x4JywKICAgICAgICAgIGRlZmF1bHQ6IHRydWUsCiAgICAgICAgICBvcHRpb25zOiBbXSwKICAgICAgICAgIHJ1bGVzOiB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl6aqM5pS257G75Z6LJyB9CiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBsYWJlbDogJ+iuvuWkh+WIhuexu++8micsCiAgICAgICAgICB2YWx1ZTogJycsCiAgICAgICAgICB0eXBlOiAnaW5wdXQnLAogICAgICAgICAgbmFtZTogJ3NiZmxtYycsCiAgICAgICAgICBkZWZhdWx0OiB0cnVlLAogICAgICAgICAgcnVsZXM6IHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXorr7lpIfliIbnsbsnIH0KICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn6aqM5pS25qCH5YeG5ZCN56ew77yaJywKICAgICAgICAgIHZhbHVlOiAnJywKICAgICAgICAgIHR5cGU6ICdpbnB1dCcsCiAgICAgICAgICBuYW1lOiAnanhmbCcsCiAgICAgICAgICBkZWZhdWx0OiB0cnVlLAogICAgICAgICAgcnVsZXM6IHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXpqozmlLbmoIflh4blkI3np7AnIH0KICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn6aG555uu5ZCN56ew77yaJywKICAgICAgICAgIHZhbHVlOiAnJywKICAgICAgICAgIHR5cGU6ICdpbnB1dCcsCiAgICAgICAgICBuYW1lOiAneG1tYycsCiAgICAgICAgICBkZWZhdWx0OiB0cnVlLAogICAgICAgICAgcnVsZXM6IHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXpobnnm67lkI3np7AnIH0KICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5omA5bGe5LiT5Lia77yaJywKICAgICAgICAgIHZhbHVlOiAnJywKICAgICAgICAgIHR5cGU6ICdzZWxlY3QnLAogICAgICAgICAgbmFtZTogJ3NwYicsCiAgICAgICAgICBkZWZhdWx0OiB0cnVlLAogICAgICAgICAgb3B0aW9uczogW3sgbGFiZWw6ICflj5jnlLUnLCB2YWx1ZTogJ+WPmOeUtScgfSwgeyBsYWJlbDogJ+mFjeeUtScsIHZhbHVlOiAn6YWN55S1JyB9LCB7IGxhYmVsOiAn6L6T55S1JywgdmFsdWU6ICfovpPnlLUnIH1dLAogICAgICAgICAgcnVsZXM6IHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXpqozmlLbnsbvlnosnIH0KICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5aSH5rOo77yaJywKICAgICAgICAgIHZhbHVlOiAnJywKICAgICAgICAgIG5hbWU6ICdieicsCiAgICAgICAgICBkZWZhdWx0OiB0cnVlLAogICAgICAgICAgdHlwZTogJ3RleHRhcmVhJywKICAgICAgICAgIHJ1bGVzOiB7IHJlcXVpcmVkOiBmYWxzZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeWkh+azqCcgfQogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgbGFiZWw6ICforr7lpIfliIbnsbvvvJonLAogICAgICAgICAgdmFsdWU6ICcnLAogICAgICAgICAgdHlwZTogJ2lucHV0JywKICAgICAgICAgIG5hbWU6ICdzYmZsJywKICAgICAgICAgIGRlZmF1bHQ6IHRydWUsCiAgICAgICAgICBoaWRkZW46IGZhbHNlLAogICAgICAgICAgcnVsZXM6IHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7fovpPlhaXorr7lpIfliIbnsbsnIH0KICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIGxhYmVsOiAn5Li76ZSuaWTvvJonLAogICAgICAgICAgdmFsdWU6ICcnLAogICAgICAgICAgbmFtZTogJ2lkJywKICAgICAgICAgIGRlZmF1bHQ6IGZhbHNlLAogICAgICAgICAgdHlwZTogJ2lucHV0JywKICAgICAgICAgIGhpZGRlbjogZmFsc2UsCiAgICAgICAgICBydWxlczogeyByZXF1aXJlZDogZmFsc2UgfQogICAgICAgIH0KICAgICAgXSwKICAgICAgLy/kuLvooajliqDovb0KICAgICAgbWFpbkxvYWRpbmc6IGZhbHNlLAogICAgICAvL+mqjOaUtuagh+WHhuS4u+ihqOafpeivouadoeS7tgogICAgICBtYWluUGFyYW5tczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHRvdGFsOiAwCiAgICAgIH0sCiAgICAgIC8v6aqM5pS25qCH5YeG5Li76KGo5pWw5o2uCiAgICAgIG1haW5UYWJsZURhdGE6IFtdLAogICAgICAvL+S4u+ihqOmAieS4reihjOaVsOaNrgogICAgICBtYWluVGFibGVTZWxlY3RSb3dzOiBbXSwKICAgICAgLy/mmK/lkKblsZXnpLrorr7lpIfliIbnsbvmoJEKICAgICAgc2hvd0RldmljZVRyZWU6IGZhbHNlLAogICAgICAvL+mqjOaUtuexu+Wei+S4i+aLieahhuaVsOaNrgogICAgICB5c2x4T3B0aW9uczogW10KICAgIH0KICB9LAogIG1vdW50ZWQoKSB7CiAgICB0aGlzLmluaXREb21haW4oKQoKICB9LAogIG1ldGhvZHM6IHsKICAgIGdldERhdGEocGFyYW1zKSB7CiAgICAgIHRoaXMuZmlsdGVyUGFyYW1zID0geyAuLi50aGlzLmZpbHRlclBhcmFtcywgLi4ucGFyYW1zIH0KICAgICAgY29uc3QgcGFyYW0gPSB0aGlzLmZpbHRlclBhcmFtcwogICAgICBnZXRCellzYnp6YihwYXJhbSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5tYWluVGFibGVEYXRhID0gcmVzcG9uc2UuZGF0YS5yZWNvcmRzCiAgICAgICAgdGhpcy5tYWluUGFyYW5tcy50b3RhbCA9IHJlc3BvbnNlLmRhdGEudG90YWwKICAgICAgICB0aGlzLm1haW5UYWJsZURhdGEuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIHRoaXMueXNseE9wdGlvbnMuZm9yRWFjaChlbGVtZW50ID0+IHsKICAgICAgICAgICAgaWYgKGl0ZW0ueXNseCA9PT0gZWxlbWVudC52YWx1ZSkgewogICAgICAgICAgICAgIGl0ZW0ueXNseE5hbWUgPSBlbGVtZW50LmxhYmVsCiAgICAgICAgICAgIH0KICAgICAgICAgIH0pCiAgICAgICAgfSkKICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS50b3RhbCA+IDApIHsKICAgICAgICAgIGNvbnNvbGUubG9nKCfmiafooYwnKQogICAgICAgICAgdGhpcy5oYW5kbGVDdXJyZW50Q2hhbmdlKHJlc3BvbnNlLmRhdGEucmVjb3Jkc1swXSkKICAgICAgICAgIGNvbnNvbGUubG9nKCfmiafooYwnLCByZXNwb25zZS5kYXRhLnJlY29yZHNbMF0pCiAgICAgICAgfQogICAgICAgIHRoaXMubWFpbkxvYWRpbmcgPSBmYWxzZQogICAgICB9KQogICAgfSwKCiAgICBoYW5kbGVFdmVudCh2YWwxLCB2YWwyKSB7CiAgICAgIHRoaXMuZmlsdGVyUGFyYW1zID0gdmFsMgogICAgfSwKCiAgICAvL+mHjee9ruaMiemSrgogICAgZ2V0UmVzZXQoKSB7CiAgICAgIHRoaXMuZmlsdGVyUGFyYW1zID0ge30KICAgICAgdGhpcy5maWx0ZXJJbmZvLmZpZWxkTGlzdC5mb3JFYWNoKGl0ZW09PnsKICAgICAgICBpZihpdGVtLnR5cGUgPT09ICdjaGVja2JveCcpewogICAgICAgICAgaXRlbS5jaGVja2JveFZhbHVlID0gW107CiAgICAgICAgfQogICAgICB9KQogICAgfSwKCiAgICAvL+iOt+WPlumqjOaUtuagh+WHhuS4u+ihqOaVsOaNrgogICAgZ2V0TWFpblN0YW5kYXJkRGF0YSgpIHsKICAgICAgdGhpcy5tYWluTG9hZGluZyA9IHRydWUKICAgICAgY29uc3QgcGFyYW0gPSB7IC4uLnRoaXMuZmlsdGVyUGFyYW1zLCAuLi50aGlzLm1haW5QYXJhbm1zIH0KICAgICAgZ2V0QnpZc2J6emIocGFyYW0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMubWFpblRhYmxlRGF0YSA9IHJlc3BvbnNlLmRhdGEucmVjb3JkcwogICAgICAgIHRoaXMubWFpblBhcmFubXMudG90YWwgPSByZXNwb25zZS5kYXRhLnRvdGFsCiAgICAgICAgdGhpcy5tYWluVGFibGVEYXRhLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICB0aGlzLnlzbHhPcHRpb25zLmZvckVhY2goZWxlbWVudCA9PiB7CiAgICAgICAgICAgIGlmIChpdGVtLnlzbHggPT09IGVsZW1lbnQudmFsdWUpIHsKICAgICAgICAgICAgICBpdGVtLnlzbHhOYW1lID0gZWxlbWVudC5sYWJlbAogICAgICAgICAgICB9CiAgICAgICAgICB9KQogICAgICAgIH0pCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEudG90YWwgPiAwKSB7CiAgICAgICAgICB0aGlzLmhhbmRsZUN1cnJlbnRDaGFuZ2UocmVzcG9uc2UuZGF0YS5yZWNvcmRzWzBdKQogICAgICAgIH0KICAgICAgICB0aGlzLm1haW5Mb2FkaW5nID0gZmFsc2UKICAgICAgfSkKICAgIH0sCiAgICAvL+mqjOaUtuagh+WHhuS4u+ihqOihjOeCueWHu+S6i+S7tumAu+i+kQogICAgaGFuZGxlQ3VycmVudENoYW5nZSh2YWwpIHsKICAgICAgdGhpcy5zZWxlY3REYXRhID0gW10KICAgICAgLy8g5riF56m65omA5pyJ6YCJ5oupCiAgICAgIHRoaXMuJHJlZnMubWFpbnRhYmxlLmNsZWFyU2VsZWN0aW9uKCkKICAgICAgLy8gIOmAieS4reW9k+WJjemAieaLqQogICAgICB0aGlzLiRyZWZzLm1haW50YWJsZS50b2dnbGVSb3dTZWxlY3Rpb24odmFsKQogICAgICB0aGlzLnNlbGVjdERhdGEucHVzaCh2YWwpCiAgICAgIHRoaXMuJHJlZnMubWFpbnRhYmxlLnNldEN1cnJlbnRSb3codmFsKQoKICAgICAgLy/nu5nlrZDnu4Tku7bkvKDlgLwKICAgICAgdGhpcy4kcmVmcy5kZXRhaWwuZ2V0TWFpblRhYmxlU2VsZWN0ZWRSb3coW3ZhbF0pCiAgICB9LAogICAgLy/lpI3pgInmoYbpgInkuK3pgLvovpEKICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZSh2YWwpIHsKICAgICAgdGhpcy5tYWluVGFibGVTZWxlY3RSb3dzID0gdmFsCiAgICAgIHRoaXMuJHJlZnMuZGV0YWlsLmdldE1haW5UYWJsZVNlbGVjdGVkUm93KHZhbCkKICAgIH0sCiAgICAvL+S/neWtmOS4u+ihqOaVsOaNrgogICAgc2F2ZU1haW5EYXRhKGZvcm1EYXRhKSB7CiAgICAgIGxldCBtZXNzYWdlID0gJycKICAgICAgaWYgKGZvcm1EYXRhLmlkID09PSAnJyB8fCAhZm9ybURhdGEuaWQpIHsKICAgICAgICBtZXNzYWdlID0gJ+aWsOWinuaIkOWKnycKICAgICAgfSBlbHNlIHsKICAgICAgICBtZXNzYWdlID0gJ+S/ruaUueaIkOWKnycKICAgICAgfQogICAgICBzYXZlT3JVcGRhdGVCellzYnp6Yihmb3JtRGF0YSkudGhlbihyZXMgPT4gewogICAgICAgIGlmIChyZXMuY29kZSA9PT0gJzAwMDAnKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MobWVzc2FnZSkKICAgICAgICAgIHRoaXMuZ2V0TWFpblN0YW5kYXJkRGF0YSgpCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+aTjeS9nOWksei0pScpCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIC8v5paw5aKe5Li76KGo5pWw5o2uCiAgICBhZGRNYWluRGF0YSgpIHsKICAgICAgdGhpcy5yZW1pbmRlciA9ICfmlrDlop4nCgogICAgICAvL+WIneWni+ivnWZvcm1MaXN05pWw5o2uCiAgICAgIC8vIHRoaXMuZm9ybUxpc3QgPSB0aGlzLiRvcHRpb25zLmRhdGEoKS5mb3JtTGlzdAoKICAgICAgY29uc3QgYWRkRm9ybSA9IHRoaXMuZm9ybUxpc3QubWFwKGl0ZW0gPT4gewogICAgICAgIGlmIChpdGVtLm5hbWUgPT09ICd5c2x4JykgewogICAgICAgICAgaXRlbS5vcHRpb25zID0gdGhpcy55c2x4T3B0aW9ucwogICAgICAgIH0KICAgICAgICByZXR1cm4gaXRlbQogICAgICB9KQogICAgICBjb25zb2xlLmxvZygiYWRkRm9ybT09PT09OTk5PT09PT0+IiwgYWRkRm9ybSkKICAgICAgdGhpcy4kcmVmcy5kaWFsb2dGb3JtLnNob3d6emMoYWRkRm9ybSkKICAgIH0sCiAgICAvL+S/ruaUueS4u+ihqOaVsOaNrgogICAgdXBkYXRlTWFpbkRhdGEocm93KSB7CiAgICAgIGNvbnN0IHVwZGF0ZUxpc3QgPSB0aGlzLmZvcm1MaXN0Lm1hcChpdGVtID0+IHsKICAgICAgICBpdGVtLnZhbHVlID0gcm93W2l0ZW0ubmFtZV0KICAgICAgICBpZiAoaXRlbS5uYW1lID09PSAneXNseCcpIHsKICAgICAgICAgIGl0ZW0ub3B0aW9ucyA9IHRoaXMueXNseE9wdGlvbnMKICAgICAgICB9CiAgICAgICAgcmV0dXJuIGl0ZW0KICAgICAgfSkKICAgICAgdGhpcy5yZW1pbmRlciA9ICfkv67mlLknCiAgICAgIHRoaXMuJHJlZnMuZGlhbG9nRm9ybS5zaG93enpjKHVwZGF0ZUxpc3QpCiAgICB9LAogICAgLy/mn6XnnIvkuLvooajmlbDmja7or6bmg4UKICAgIHNob3dNYWluRGF0YShyb3cpIHsKICAgICAgY29uc3QgaW5mb0xpc3QgPSB0aGlzLmZvcm1MaXN0Lm1hcChpdGVtID0+IHsKICAgICAgICBpdGVtLnZhbHVlID0gcm93W2l0ZW0ubmFtZV0KICAgICAgICByZXR1cm4gaXRlbQogICAgICB9KQogICAgICB0aGlzLnJlbWluZGVyID0gJ+ivpuaDhScKICAgICAgdGhpcy4kcmVmcy5kaWFsb2dGb3JtLnNob3d4cShpbmZvTGlzdCkKICAgIH0sCiAgICAvL+WIoOmZpOS4u+ihqOaVsOaNrgogICAgZGVsZXRlTWFpbkRhdGEocm93KSB7CiAgICAgIHRoaXMuZm9ybSA9IHsgLi4ucm93IH0KICAgICAgdGhpcy4kY29uZmlybSgn56Gu6K6k5Yig6Zmk6YCJ5Lit5pWw5o2uLCDmmK/lkKbnu6fnu60/JywgJ+aPkOekuicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIC8vIGxldCBpZHMgPSBbXQogICAgICAgIC8vIHRoaXMubWFpblRhYmxlU2VsZWN0Um93cy5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgIC8vICAgaWRzLnB1c2goaXRlbS5pZCkKICAgICAgICAvLyB9KQoKICAgICAgICBkZWxldGVCellzYnp6YihbdGhpcy5mb3JtLmlkXSkudGhlbihyZXMgPT4gewogICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAnMDAwMCcpIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOaIkOWKnycsCiAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnCiAgICAgICAgICAgIH0pCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmk43kvZzlpLHotKUnKQogICAgICAgICAgfQogICAgICAgICAgdGhpcy5nZXRNYWluU3RhbmRhcmREYXRhKCkKICAgICAgICB9KQogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICB0eXBlOiAnaW5mbycsCiAgICAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raI5Yig6ZmkJwogICAgICAgIH0pCiAgICAgIH0pCiAgICB9LAogICAgLy/liJ3lp4vor53kuIvmi4nmoYbmlbDmja4KICAgIGFzeW5jIGluaXREb21haW4oKSB7CgogICAgICBsZXQgeyBkYXRhOiB5c2x4IH0gPSBhd2FpdCBnZXREaWN0VHlwZURhdGEoJ3lzbHgnKQogICAgICB0aGlzLnlzbHhPcHRpb25zID0geXNseAoKICAgICAgdGhpcy5nZXRNYWluU3RhbmRhcmREYXRhKCkKICAgIH0sCgogICAgLy9pbnB1dOi+k+WFpeahhum8oOagh+iBmueEpuS6i+S7tgogICAgaW5wdXRGb2N1c0V2ZW50KHZhbCkgewogICAgICB0aGlzLmlzRmlsdGVyID0gZmFsc2UKICAgICAgaWYgKHZhbC50YXJnZXQubmFtZSA9PT0gJ3NiZmxtYycpIHsKICAgICAgICB0aGlzLnNob3dEZXZpY2VUcmVlID0gdHJ1ZQogICAgICB9CgogICAgICBpZiAodmFsLnRhcmdldC5uYW1lID09PSAnc2JmbCcpIHsKICAgICAgICB0aGlzLnNob3dEZXZpY2VUcmVlID0gdHJ1ZQogICAgICAgIHRoaXMuaXNGaWx0ZXIgPSB0cnVlCiAgICAgIH0KICAgIH0sCgogICAgLy/ojrflj5borr7lpIfliIbnsbvmoJHmlbDmja4KICAgIGdldERldmljZVR5cGVEYXRhKHJlcykgewogICAgICBpZiAocmVzLmxlbmd0aD4wKXsKICAgICAgICBpZiAodGhpcy5pc0ZpbHRlcikgewogICAgICAgICAgbGV0IHNiZmwgPSAnJwogICAgICAgICAgbGV0IHNiZmxtYyA9ICcnCiAgICAgICAgICByZXMuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgICAgc2JmbG1jICs9IGl0ZW0ubmFtZSArICcsJwogICAgICAgICAgICBzYmZsICs9IGl0ZW0uY29kZSArICcsJwogICAgICAgICAgfSkKICAgICAgICAgIHRoaXMuZmlsdGVySW5mby5kYXRhLnNiZmwgPSBzYmZsLnN1YnN0cmluZygwLCBzYmZsLmxlbmd0aCAtIDEpCiAgICAgICAgICB0aGlzLmZpbHRlckluZm8uZGF0YS5zYmZsbWMgPSBzYmZsbWMuc3Vic3RyaW5nKDAsIHNiZmxtYy5sZW5ndGggLSAxKQogICAgICAgICAgdGhpcy5zaG93RGV2aWNlVHJlZSA9IGZhbHNlCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGlmIChyZXMubGVuZ3RoID09PSAxKSB7CiAgICAgICAgICAgIHRoaXMuZm9ybUxpc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgICAgICBpZiAoaXRlbS5uYW1lID09PSAnc2JmbG1jJykgewogICAgICAgICAgICAgICAgaXRlbS52YWx1ZSA9IHJlc1swXS5uYW1lCiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIGlmIChpdGVtLm5hbWUgPT09ICdzYmZsJykgewogICAgICAgICAgICAgICAgaXRlbS52YWx1ZSA9ICByZXNbMF0uY29kZQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSkKICAgICAgICAgICAgdGhpcy5zaG93RGV2aWNlVHJlZSA9IGZhbHNlCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+mAieaLqeWNleadoeiuvuWkh+aVsOaNricpCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9ZWxzZSB7CiAgICAgICAgdGhpcy5zaG93RGV2aWNlVHJlZSA9IGZhbHNlCiAgICAgIH0KICAgIH0sCiAgICBjbG9zZURldmljZVR5cGVEaWFsb2coKSB7CiAgICAgIHRoaXMuc2hvd0RldmljZVRyZWUgPSBmYWxzZQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["ysbzk1.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2FA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ysbzk1.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row>\n      <el-filter\n        :data=\"filterInfo.data\"\n        :field-list=\"filterInfo.fieldList\"\n        :width=\"{ labelWidth: 80, itemWidth: 230 }\"\n        @onfocusEvent=\"inputFocusEvent\"\n        @handleReset=\"getReset\"\n        @handleEvent=\"handleEvent\"\n      />\n      <!--右侧列表-->\n      <el-col :span=\"24\">\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">临时验收标准库</div>\n          <el-table\n            ref=\"maintable\"\n            stripe\n            border\n            height=\"65vh\"\n            v-loading=\"mainLoading\"\n            :data=\"mainTableData\"\n            @row-click=\"handleCurrentChange\"\n            @selection-change=\"handleSelectionChange\"\n          >\n            <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n            <el-table-column label=\"验收类型\" align=\"center\" prop=\"yslxName\"/>\n            <el-table-column label=\"设备分类\" align=\"center\" prop=\"sbflmc\" :show-overflow-tooltip=\"true\"/>\n            <el-table-column label=\"验收名称\" align=\"center\" prop=\"jxfl\" :show-overflow-tooltip=\"true\">\n              <template slot-scope=\"scope\">\n            <el-popover v-if=\"scope.row.jxfl&&scope.row.jxfl.length>15\" trigger=\"hover\" placement=\"top\"\n              style=\"overflow: hidden;text-overflow: ellipsis;white-space: nowrap;\">\n              {{ scope.row.jxfl }}\n              <div slot=\"reference\">\n                {{ scope.row.jxfl.substring(0,15)+'...' }}\n              </div>\n            </el-popover>\n            <span v-else>\n              {{ scope.row.jxfl}}\n            </span>\n          </template>\n            </el-table-column>\n            <el-table-column label=\"项目名称\" align=\"center\" prop=\"xmmc\" :show-overflow-tooltip=\"true\"/>\n            <el-table-column label=\"备注\" align=\"center\" prop=\"bz\" :show-overflow-tooltip=\"true\"/>\n            <el-table-column label=\"操作\" fixed=\"right\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"180\">\n              <template slot-scope=\"scope\">\n                <el-button type=\"text\" @click=\"showMainData(scope.row)\" title=\"详情\" class=\"el-icon-view\"></el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n          <pagination v-show=\"mainParanms.total>0\"\n                      :total=\"mainParanms.total\"\n                      :page.sync=\"mainParanms.pageNum\"\n                      :limit.sync=\"mainParanms.pageSize\"\n                      @pagination=\"getMainStandardData\"\n          />\n        </el-white>\n        <el-white>\n          <acceptance-detail ref=\"detail\"></acceptance-detail>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <dialog-form\n      ref=\"dialogForm\"\n      label-width=\"150px\"\n      out-width=\"50%\"\n      :reminder=\"reminder\"\n      :rows=\"rows\"\n      @onfocusEvent=\"inputFocusEvent\"\n      @save=\"saveMainData\"\n    />\n    <el-dialog\n    v-dialogDrag\n      :append-to-body=\"true\"\n      title=\"设备分类\"\n      :visible.sync=\"showDeviceTree\"\n      width=\"400px\"\n      v-if=\"showDeviceTree\"\n    >\n      <device-tree\n        @getDeviceTypeData=\"getDeviceTypeData\"\n        @closeDeviceTypeDialog=\"closeDeviceTypeDialog\"\n      >\n      </device-tree>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport dialogForm from 'com/dialogFrom/dialogForm'\nimport { deleteBzYsbzzb, getBzYsbzzb, saveOrUpdateBzYsbzzb } from '@/api/bzgl/ysbzk/ysbzk'\nimport AcceptanceDetail from '@/views/dagangOilfield/bzgl/ysbzkgl/acceptanceDetail1'\nimport DeviceTree from '@/views/dagangOilfield/bzgl/sbbzk/deviceTree'\nimport { getDictTypeData } from '@/api/system/dict/data'\n\nexport default {\n  components: { DeviceTree, AcceptanceDetail, dialogForm },\n  name: 'ysbzk',\n  data() {\n    return {\n      filterParams: {},\n      filterInfo: {\n        data: {\n          sbfl:\"\",\n          sbflmc:\"\"\n        },//查询条件\n        fieldList: [\n\n          { label: '设备分类', type: 'inputYsbzk', value: 'sbflmc', name: 'sbfl' },\n          { label: '验收名称', type: 'input', value: 'jxfl' },\n          { label: '项目名称', type: 'input', value: 'xmmc' },\n          { label: '备注', type: 'input', value: 'bz' },\n          {\n            label: '验收类型',\n            type: 'checkbox',\n            checkboxValue: [],\n            value: 'yslx',\n            options: [\n              { label: '电气设备类', value: 'dqsbl' },\n              { label: '土建设施类', value: 'tjssl' },\n              { label: '其他', value: 'qt' }\n            ],\n            clearable: true\n          },\n        ]\n      },\n\n      currentUser: this.$store.getters.name,\n      //新增或修改标题\n      reminder: '修改',\n      //弹出框form表单每行显示几个\n      rows: 2,\n      //主表新增表单数据\n      formList: [\n        {\n          label: '验收类型：',\n          value: '',\n          type: 'select',\n          name: 'yslx',\n          default: true,\n          options: [],\n          rules: { required: true, message: '请输入验收类型' }\n        },\n        {\n          label: '设备分类：',\n          value: '',\n          type: 'input',\n          name: 'sbflmc',\n          default: true,\n          rules: { required: true, message: '请输入设备分类' }\n        },\n        {\n          label: '验收标准名称：',\n          value: '',\n          type: 'input',\n          name: 'jxfl',\n          default: true,\n          rules: { required: true, message: '请输入验收标准名称' }\n        },\n        {\n          label: '项目名称：',\n          value: '',\n          type: 'input',\n          name: 'xmmc',\n          default: true,\n          rules: { required: true, message: '请输入项目名称' }\n        },\n        {\n          label: '所属专业：',\n          value: '',\n          type: 'select',\n          name: 'spb',\n          default: true,\n          options: [{ label: '变电', value: '变电' }, { label: '配电', value: '配电' }, { label: '输电', value: '输电' }],\n          rules: { required: true, message: '请输入验收类型' }\n        },\n        {\n          label: '备注：',\n          value: '',\n          name: 'bz',\n          default: true,\n          type: 'textarea',\n          rules: { required: false, message: '请输入备注' }\n        },\n        {\n          label: '设备分类：',\n          value: '',\n          type: 'input',\n          name: 'sbfl',\n          default: true,\n          hidden: false,\n          rules: { required: true, message: '请输入设备分类' }\n        },\n        {\n          label: '主键id：',\n          value: '',\n          name: 'id',\n          default: false,\n          type: 'input',\n          hidden: false,\n          rules: { required: false }\n        }\n      ],\n      //主表加载\n      mainLoading: false,\n      //验收标准主表查询条件\n      mainParanms: {\n        pageNum: 1,\n        pageSize: 10,\n        total: 0\n      },\n      //验收标准主表数据\n      mainTableData: [],\n      //主表选中行数据\n      mainTableSelectRows: [],\n      //是否展示设备分类树\n      showDeviceTree: false,\n      //验收类型下拉框数据\n      yslxOptions: []\n    }\n  },\n  mounted() {\n    this.initDomain()\n\n  },\n  methods: {\n    getData(params) {\n      this.filterParams = { ...this.filterParams, ...params }\n      const param = this.filterParams\n      getBzYsbzzb(param).then(response => {\n        this.mainTableData = response.data.records\n        this.mainParanms.total = response.data.total\n        this.mainTableData.forEach(item => {\n          this.yslxOptions.forEach(element => {\n            if (item.yslx === element.value) {\n              item.yslxName = element.label\n            }\n          })\n        })\n        if (response.data.total > 0) {\n          console.log('执行')\n          this.handleCurrentChange(response.data.records[0])\n          console.log('执行', response.data.records[0])\n        }\n        this.mainLoading = false\n      })\n    },\n\n    handleEvent(val1, val2) {\n      this.filterParams = val2\n    },\n\n    //重置按钮\n    getReset() {\n      this.filterParams = {}\n      this.filterInfo.fieldList.forEach(item=>{\n        if(item.type === 'checkbox'){\n          item.checkboxValue = [];\n        }\n      })\n    },\n\n    //获取验收标准主表数据\n    getMainStandardData() {\n      this.mainLoading = true\n      const param = { ...this.filterParams, ...this.mainParanms }\n      getBzYsbzzb(param).then(response => {\n        this.mainTableData = response.data.records\n        this.mainParanms.total = response.data.total\n        this.mainTableData.forEach(item => {\n          this.yslxOptions.forEach(element => {\n            if (item.yslx === element.value) {\n              item.yslxName = element.label\n            }\n          })\n        })\n        if (response.data.total > 0) {\n          this.handleCurrentChange(response.data.records[0])\n        }\n        this.mainLoading = false\n      })\n    },\n    //验收标准主表行点击事件逻辑\n    handleCurrentChange(val) {\n      this.selectData = []\n      // 清空所有选择\n      this.$refs.maintable.clearSelection()\n      //  选中当前选择\n      this.$refs.maintable.toggleRowSelection(val)\n      this.selectData.push(val)\n      this.$refs.maintable.setCurrentRow(val)\n\n      //给子组件传值\n      this.$refs.detail.getMainTableSelectedRow([val])\n    },\n    //复选框选中逻辑\n    handleSelectionChange(val) {\n      this.mainTableSelectRows = val\n      this.$refs.detail.getMainTableSelectedRow(val)\n    },\n    //保存主表数据\n    saveMainData(formData) {\n      let message = ''\n      if (formData.id === '' || !formData.id) {\n        message = '新增成功'\n      } else {\n        message = '修改成功'\n      }\n      saveOrUpdateBzYsbzzb(formData).then(res => {\n        if (res.code === '0000') {\n          this.$message.success(message)\n          this.getMainStandardData()\n        } else {\n          this.$message.error('操作失败')\n        }\n      })\n    },\n    //新增主表数据\n    addMainData() {\n      this.reminder = '新增'\n\n      //初始话formList数据\n      // this.formList = this.$options.data().formList\n\n      const addForm = this.formList.map(item => {\n        if (item.name === 'yslx') {\n          item.options = this.yslxOptions\n        }\n        return item\n      })\n      console.log(\"addForm=====999=====>\", addForm)\n      this.$refs.dialogForm.showzzc(addForm)\n    },\n    //修改主表数据\n    updateMainData(row) {\n      const updateList = this.formList.map(item => {\n        item.value = row[item.name]\n        if (item.name === 'yslx') {\n          item.options = this.yslxOptions\n        }\n        return item\n      })\n      this.reminder = '修改'\n      this.$refs.dialogForm.showzzc(updateList)\n    },\n    //查看主表数据详情\n    showMainData(row) {\n      const infoList = this.formList.map(item => {\n        item.value = row[item.name]\n        return item\n      })\n      this.reminder = '详情'\n      this.$refs.dialogForm.showxq(infoList)\n    },\n    //删除主表数据\n    deleteMainData(row) {\n      this.form = { ...row }\n      this.$confirm('确认删除选中数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // let ids = []\n        // this.mainTableSelectRows.forEach(item => {\n        //   ids.push(item.id)\n        // })\n\n        deleteBzYsbzzb([this.form.id]).then(res => {\n          if (res.code === '0000') {\n            this.$message({\n              message: '删除成功',\n              type: 'success'\n            })\n          } else {\n            this.$message.error('操作失败')\n          }\n          this.getMainStandardData()\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        })\n      })\n    },\n    //初始话下拉框数据\n    async initDomain() {\n\n      let { data: yslx } = await getDictTypeData('yslx')\n      this.yslxOptions = yslx\n\n      this.getMainStandardData()\n    },\n\n    //input输入框鼠标聚焦事件\n    inputFocusEvent(val) {\n      this.isFilter = false\n      if (val.target.name === 'sbflmc') {\n        this.showDeviceTree = true\n      }\n\n      if (val.target.name === 'sbfl') {\n        this.showDeviceTree = true\n        this.isFilter = true\n      }\n    },\n\n    //获取设备分类树数据\n    getDeviceTypeData(res) {\n      if (res.length>0){\n        if (this.isFilter) {\n          let sbfl = ''\n          let sbflmc = ''\n          res.forEach(item => {\n            sbflmc += item.name + ','\n            sbfl += item.code + ','\n          })\n          this.filterInfo.data.sbfl = sbfl.substring(0, sbfl.length - 1)\n          this.filterInfo.data.sbflmc = sbflmc.substring(0, sbflmc.length - 1)\n          this.showDeviceTree = false\n        } else {\n          if (res.length === 1) {\n            this.formList.forEach(item => {\n              if (item.name === 'sbflmc') {\n                item.value = res[0].name\n              }\n              if (item.name === 'sbfl') {\n                item.value =  res[0].code\n              }\n            })\n            this.showDeviceTree = false\n          } else {\n            this.$message.warning('请选择单条设备数据')\n          }\n        }\n      }else {\n        this.showDeviceTree = false\n      }\n    },\n    closeDeviceTypeDialog() {\n      this.showDeviceTree = false\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  height: calc(100vh - 225px);\n  overflow: auto;\n}\n\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.box-cardList {\n  height: 56%;\n}\n\n.item {\n  width: 200px;\n  float: left;\n}\n\n#main_container_dj {\n  height: calc(100vh - 84px);\n}\n\n.aside_height {\n  height: 96%;\n}\n\n.defect .el-form-item:nth-child(odd) {\n  margin-right: 70px;\n}\n\n/*背景颜色调整*/\n#main_container_dj, #main_container_dj .el-aside {\n  background-color: #b4caf1;\n}\n\n/deep/ #qxlr_dialog_insert .el-dialog__header {\n  background-color: #8eb3f5;\n}\n\n/deep/ .pmyBtn {\n  background: #8eb3f5;\n}\n</style>\n"]}]}