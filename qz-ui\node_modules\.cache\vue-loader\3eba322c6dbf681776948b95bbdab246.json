{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\todoitem\\todo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\todoitem\\todo.vue", "mtime": 1706897322087}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3RUb2RvLGdldEJ1c2luZXNzRGF0YSxjb21wbGV0ZX0gZnJvbSAiQC9hcGkvYWN0aXZpdGkvdG9kb2l0ZW0iOwppbXBvcnQgQWN0aXZpdGlIaXN0b3J5IGZyb20gIi4vaGlzdG9yeSI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiVG9kb0l0ZW0iLAogIGNvbXBvbmVudHM6IHtBY3Rpdml0aUhpc3Rvcnl9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBvcGVuSW5mbzpmYWxzZSwKICAgICAgaW5zdGFuY2VJZDpudWxsLAogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIHRpdGxlOiflrqHmibknLAogICAgICBpbWdTcmM6JycsCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIGRhdGVSYW5nZTogW10sCiAgICAgIGRhdGVSYW5nZTE6W10sCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g55So5oi36KGo5qC85pWw5o2uCiAgICAgIHRvZG9MaXN0OiBudWxsLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIG9wZW5IaXN0b3J5OmZhbHNlLAogICAgICBvcGVuTG9hZGluZ0ltZzpmYWxzZSwKICAgICAgLy8g6YOo6Zeo5ZCN56ewCiAgICAgIC8vIOihqOWNleWPguaVsAogICAgICBmb3JtOiB7CiAgICAgICAgZXh0RGF0YTp7fQogICAgICB9LAogICAgICBmb3JtSW5mbzp7fSwKICAgICAgc2VsZWN0RGF0ZTpbXSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgaWQ6dW5kZWZpbmVkLAogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIGl0ZW1OYW1lOiB1bmRlZmluZWQsCiAgICAgICAgbW9kdWxlOiB1bmRlZmluZWQsCiAgICAgICAgdGFza05hbWU6dW5kZWZpbmVkLAogICAgICAgIHRvZG9Vc2VyTmFtZTp1bmRlZmluZWQsCiAgICAgICAgaGFuZGxlVXNlck5hbWU6dW5kZWZpbmVkLAogICAgICB9LAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICAnZXh0RGF0YS5wYXNzJzogWwogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5a6h5om55oSP6KeB5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIifSwKICAgICAgICBdLAogICAgICAgIG5hbWU6IFsKICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWQjeensOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIn0sCiAgICAgICAgXSwKICAgICAgfSwKICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgICB0aGlzLnF1ZXJ5UGFyYW1zLmlkID0gdGhpcy4kcm91dGUucGFyYW1zLmlkOwogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOafpeivoueUqOaIt+WIl+ihqCAqLwogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgbGlzdFRvZG8odGhpcy5xdWVyeVBhcmFtcykudGhlbigKICAgICAgICAocmVzcG9uc2UpID0+IHsKICAgICAgICAgIHRoaXMudG9kb0xpc3QgPSByZXNwb25zZS5kYXRhLnJlY29yZHM7CiAgICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UuZGF0YS50b3RhbDsKICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICAgIH0KICAgICAgKTsKICAgIH0sCiAgIC8qKgogICAgKiDlj5bmtojmjInpkq4KICAgICogKi8KICAgIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvKioKICAgICAqIOihqOWNlemHjee9rgogICAgICogKi8KICAgIHJlc2V0KCkgewogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgYnVzaW5lc3NJZDp1bmRlZmluZWQsCiAgICAgICAgYXBwbHlVc2VyIDogdW5kZWZpbmVkLAogICAgICAgIGFwcGx5VGltZTp1bmRlZmluZWQsCiAgICAgICAgdGl0bGU6dW5kZWZpbmVkLAogICAgICAgIHJlYXNvbjp1bmRlZmluZWQsCiAgICAgICAgYnVzaW5lc3NUeXBlOnVuZGVmaW5lZCwKICAgICAgICBleHREYXRhOnsKICAgICAgICAgIHBhc3M6IHRydWUsCiAgICAgICAgICBkZXNjcmlwdGlvbjp1bmRlZmluZWQKICAgICAgICB9LAogICAgICB9OwogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICAgIHJlc2V0Rm9ybUluZm8oKXsKICAgICAgdGhpcy5mb3JtSW5mbyA9IHsKICAgICAgICBidXNpbmVzc0lkOnVuZGVmaW5lZCwKICAgICAgICBhcHBseVVzZXIgOiB1bmRlZmluZWQsCiAgICAgICAgYXBwbHlUaW1lOnVuZGVmaW5lZCwKICAgICAgICB0aXRsZTp1bmRlZmluZWQsCiAgICAgICAgcmVhc29uOnVuZGVmaW5lZCwKICAgICAgICB0eXBlOnVuZGVmaW5lZCwKICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm1JbmZvIik7CiAgICB9LAogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8KICAgIHJlc2V0UXVlcnkoKSB7CiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gW107CiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsKICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOwogICAgfSwKICAgIC8qKirlpJrpgInmoYbpgInkuK3mlbDmja4qKiovCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuc2VsZWN0RGF0ZSA9IHNlbGVjdGlvbjsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKChpdGVtKSA9PiBpdGVtLnVzZXJJZCk7CiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPSAxOwogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7CiAgICB9LAogICAgLyoqIOWKnueQhuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlQ29tcGxldGUocm93KSB7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgbGV0IGlkcyA9IHRoaXMuaWRzOwogICAgICBpZihpZHMubGVuZ3RoIT0xKXsKICAgICAgICB0aGlzLm1zZ1dhcm5pbmcoIuivt+mAieaLqeS4gOadoeaVsOaNriIpOwogICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQogICAgICBsZXQgc2VsZWN0Um93ID0gdGhpcy5zZWxlY3REYXRlWzBdOwogICAgICBsZXQgcXVlcnkgPSB7dGFza0lkOnNlbGVjdFJvdy50YXNrSWQsbW9kdWxlOnNlbGVjdFJvdy5tb2R1bGV9CiAgICAgIGxldCBfdGhpcyA9IHRoaXM7CiAgICAgIGdldEJ1c2luZXNzRGF0YShxdWVyeSkudGhlbihyZXM9PnsKICAgICAgICBsZXQgcmVzcG9uc2VEYXRhID0gcmVzLmRhdGE7CiAgICAgICAgX3RoaXMuZm9ybS5hcHBseVRpbWUgPSByZXNwb25zZURhdGEuYXBwbHlUaW1lOwogICAgICAgIF90aGlzLmZvcm0uYXBwbHlVc2VyID0gcmVzcG9uc2VEYXRhLmFwcGx5VXNlcjsKICAgICAgICBfdGhpcy5mb3JtLmJ1c2luZXNzSWQgPSByZXNwb25zZURhdGEuYnVzaW5lc3NJZDsKICAgICAgICBfdGhpcy5mb3JtLmJ1c2luZXNzVHlwZSA9IHJlc3BvbnNlRGF0YS5idXNpbmVzc1R5cGU7CiAgICAgICAgX3RoaXMuZm9ybS5pbnN0YW5jZUlkID0gcmVzcG9uc2VEYXRhLmluc3RhbmNlSWQ7CiAgICAgICAgX3RoaXMuZm9ybS5yZWFzb24gPSByZXNwb25zZURhdGEucmVhc29uOwogICAgICAgIF90aGlzLmZvcm0udGFza0lkID0gcmVzcG9uc2VEYXRhLnRhc2tJZDsKICAgICAgICBfdGhpcy5mb3JtLnRpdGxlID0gcmVzcG9uc2VEYXRhLnRpdGxlOwogICAgICAgIF90aGlzLmZvcm0udmFyaWFibGVzID0gcmVzcG9uc2VEYXRhLnZhcmlhYmxlczsKICAgICAgICBfdGhpcy5mb3JtLmV4dERhdGEucGFzcyA9IHRydWU7CiAgICAgIH0pCgogICAgfSwKICAgIC8qKiDmj5DkuqTmjInpkq4gKi8KICAgIHN1Ym1pdEZvcm06IGZ1bmN0aW9uICgpIHsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKCh2YWxpZCkgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgbGV0IHNlbGVjdERhdGEgPSB0aGlzLnNlbGVjdERhdGVbMF07CiAgICAgICAgICBjb21wbGV0ZSh0aGlzLmZvcm0sc2VsZWN0RGF0YS50YXNrSWQpLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAnMDAwMCcpIHsKICAgICAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIuWuoeaJueaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDnlLPor7for6bmg4XmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZUFwcGx5SW5mbygpIHsKICAgICAgdGhpcy5yZXNldEZvcm1JbmZvKCk7CiAgICAgIGxldCBpZHMgPSB0aGlzLmlkczsKICAgICAgaWYoaWRzLmxlbmd0aCE9MSl7CiAgICAgICAgdGhpcy5tc2dXYXJuaW5nKCLor7fpgInmi6nkuIDmnaHmlbDmja4iKTsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgICAgbGV0IHNlbGVjdFJvdyA9IHRoaXMuc2VsZWN0RGF0ZVswXTsKICAgICAgbGV0IHF1ZXJ5ID0ge3Rhc2tJZDpzZWxlY3RSb3cudGFza0lkLG1vZHVsZTpzZWxlY3RSb3cubW9kdWxlfQogICAgICB0aGlzLm9wZW5JbmZvID0gdHJ1ZTsKICAgICAgZ2V0QnVzaW5lc3NEYXRhKHF1ZXJ5KS50aGVuKHJlcz0+ewogICAgICAgIHRoaXMuZm9ybUluZm8gPSByZXMuZGF0YTsKICAgICAgfSkKICAgIH0sCiAgICAvKioqICDlrqHmibnljoblj7IgKioqLwogICAgaGFuZGxlSGlzdG9yeShyb3cpewogICAgICAgIHRoaXMub3Blbkhpc3RvcnkgPSB0cnVlOwogICAgICAgIHRoaXMuaW5zdGFuY2VJZCA9IHJvdy5pbnN0YW5jZUlkCiAgICB9LAogICAgLyoqIOWnlOaJmCAqKi8KICAgIGhhbmRsZURlbGVnYXRpb24ocm93KXsKICAgICAgZGVwbG95KHJvdy5pZCkudGhlbihyZXMgPT57CiAgICAgICAgaWYocmVzLmNvZGU9PScwMDAwJyl7CiAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MocmVzLm1zZyk7CiAgICAgICAgfWVsc2V7CiAgICAgICAgICB0aGlzLm1zZ0Vycm9yKHJlcy5tc2cpOwogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICAvKioqKiDov5vluqbmn6XnnIsgICoqKiovCiAgICBoYW5kbGVQbGFuKHJvdyl7CiAgICAgIHRoaXMub3BlbkxvYWRpbmdJbWcgPSB0cnVlCiAgICAgIHRoaXMuaW1nU3JjID0iL2FjdGl2aXRpLWFwaS9wcm9jZXNzL3JlYWQtcmVzb3VyY2U/aW5zdGFuY2VJZD0iK3Jvdy5pbnN0YW5jZUlkKyImdD0iK25ldyBEYXRlKCkuZ2V0VGltZSgpOwogICAgfSwKICAgIC8qKiDmmK/lkKbmn6XnnIsgKiovCiAgICBoYXNMb29rT3Zlcihyb3cpewogICAgICByZXR1cm4gcm93LmlzVmlldz09JzAnPyflkKYnOnJvdy5pc1ZpZXc9JzEnPyfmmK8nOictJzsKICAgIH0sCiAgICAvKiog5piv5ZCm5aSE55CGICoqLwogICAgaGFzSGFuZGxlcihyb3cpewogICAgICByZXR1cm4gcm93LmlzVmlldz09JzAnPyflkKYnOnJvdy5pc1ZpZXc9JzEnPyfmmK8nOictJzsKICAgIH0sCiAgICAvKiog5a6h5om55Y6G5Y+y56Gu5a6aICAqKi8KICAgIHN1Ym1pdEhpc3RvcnkoKXsKICAgICAgdGhpcy5vcGVuSGlzdG9yeSA9IGZhbHNlCiAgICB9LAogICAgLyoqKiog5YWz6Zet5a6h5om55Y6G5Y+yICAqKioqLwogICAgY2FuY2VsSGlzdG9yeSgpewogICAgICB0aGlzLm9wZW5IaXN0b3J5ID0gZmFsc2UKICAgIH0sCiAgICAvKiog6L+b5bqm5p+l55yLICAqKi8KICAgIHN1Ym1pdExvYWRpbmdJbWcoKXsKICAgICAgdGhpcy5vcGVuTG9hZGluZ0ltZyA9IGZhbHNlOwogICAgfSwKICAgIGNhbmNlbExvYWRpbmdJbWcoKXsKICAgICAgdGhpcy5vcGVuTG9hZGluZ0ltZyA9IGZhbHNlCiAgICB9LAogIH0sCn07Cg=="}, {"version": 3, "sources": ["todo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "todo.vue", "sourceRoot": "src/views/activiti/todoitem", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n      <el-form class=\"searchForm1\" :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"100px\">\n        <el-row>\n          <el-col :span=\"6\">\n            <el-form-item label=\"事项标题：\" prop=\"itemName\">\n              <el-input v-model=\"queryParams.itemName\" placeholder=\"请输入事项标题\" clearable\n                        @keyup.enter.native=\"handleQuery\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"6\">\n            <el-form-item label=\"模块名称：\" prop=\"module\">\n              <el-input v-model=\"queryParams.module\" placeholder=\"请输入模块名称\" clearable\n                        @keyup.enter.native=\"handleQuery\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"6\">\n            <el-form-item label=\"任务名称：\" prop=\"taskName\">\n              <el-input v-model=\"queryParams.taskName\" placeholder=\"请输入任务名称\" clearable\n                        @keyup.enter.native=\"handleQuery\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"6\">\n            <el-form-item label=\"待办人：\" prop=\"todoUserName\">\n              <el-input v-model=\"queryParams.todoUserName\" placeholder=\"请输入待办人\" clearable\n                        @keyup.enter.native=\"handleQuery\"/>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"6\">\n            <el-form-item label=\"处理人：\" prop=\"handleUserName\">\n              <el-input v-model=\"queryParams.handleUserName\" placeholder=\"请输入处理人\" clearable c\n                        @keyup.enter.native=\"handleQuery\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"9\">\n            <el-form-item label=\"通知时间:\">\n              <el-date-picker\n                unlink-panels\n                v-model=\"dateRange\"\n                value-format=\"yyyy-MM-dd\"\n                type=\"daterange\"\n                range-separator=\"-\"\n                start-placeholder=\"开始日期\"\n                end-placeholder=\"结束日期\"\n              ></el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"9\">\n            <el-form-item label=\"处理时间:\">\n              <el-date-picker\n                unlink-panels\n                v-model=\"dateRange1\"\n                value-format=\"yyyy-MM-dd\"\n                type=\"daterange\"\n                range-separator=\"-\"\n                start-placeholder=\"开始日期\"\n                end-placeholder=\"结束日期\"\n              ></el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item>\n          <el-button type=\"cyan\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\n          <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\" type=\"warning\">重置</el-button>\n        </el-form-item>\n      </el-form>\n    <el-row :gutter=\"20\">\n      <el-col :span=\"24\">\n        <el-white class=\"button-group\">\n          <el-row :gutter=\"10\" class=\"mb8 pull-right\">\n            <el-col :span=\"1.5\">\n              <el-button type=\"primary\" :disabled=\"single\"   @click=\"handleComplete()\" >办理</el-button>\n              <!-- <el-button  type=\"primary\" :disabled=\"single\" @click=\"handleDelegation()\" >委托</el-button>-->\n              <el-button  type=\"primary\" :disabled=\"single\"  @click=\"handleApplyInfo()\"  >申请详情</el-button>\n            </el-col>\n          </el-row>\n          <el-table style=\"margin-top: 15px;\" stripe border v-loading=\"loading\" :data=\"todoList\" @selection-change=\"handleSelectionChange\">\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n            <el-table-column label=\"事项标题\" align=\"center\" prop=\"itemName\" width=\"160px\" fixed >\n              <template slot-scope=\"scope\">\n                <router-link   :to=\"{name:'报销管理' ,params:{appId:scope.row.businessId}}\"  class=\"link-type\" v-if=\"scope.row.module=='repayment'\">\n                  <span>{{ scope.row.itemName }}</span>\n                </router-link>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"申请人\" align=\"center\" prop=\"applyUserName\"     />\n            <el-table-column label=\"待办人\" align=\"center\" prop=\"todoUserName\"     />\n            <el-table-column label=\"处理人\" align=\"center\" prop=\"handleUserName\"    />\n            <el-table-column label=\"创建时间\" align=\"center\" prop=\"todoTime\" width=\"160px\"  />\n            <el-table-column label=\"处理时间\" align=\"center\" prop=\"handleTime\" width=\"160px\"  />\n            <el-table-column label=\"待办任务名称\" align=\"center\" prop=\"nodeName\"  width=\"160px\" />\n            <el-table-column label=\"操作\" align=\"center\"   class-name=\"small-padding fixed-width\" width=\"160px\" fixed=\"right\">\n              <template slot-scope=\"scope\">\n                <el-button size=\"mini\" type=\"text\"   @click=\"handleHistory(scope.row)\" >审批历史</el-button>\n                <el-button size=\"mini\" type=\"text\"   @click=\"handlePlan(scope.row)\" >流程查看</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n\n          <pagination v-show=\"total>0\"\n                      :total=\"total\"\n                      :page.sync=\"queryParams.pageNum\"\n                      :limit.sync=\"queryParams.pageSize\"\n                      @pagination=\"getList\"\n          />\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!-- 添加或修改参数配置对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body :close-on-click-modal=false v-dialogDrag>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n            <el-form-item label=\"申请人：\" prop=\"applyName    \">\n              <el-input v-model=\"form.applyUser\" placeholder=\"请输入名称\" maxlength=\"30\"/>\n            </el-form-item>\n            <el-form-item label=\"申请时间\">\n            <el-date-picker\n              v-model=\"form.applyTime\"\n              type=\"datetime\"\n              format=\"yyyy-MM-dd HH:mm:ss\"\n              value-format=\"yyyy-MM-dd HH:mm:ss\"\n              placeholder=\"选择日期时间\">\n            </el-date-picker>\n          </el-form-item>\n            <el-form-item label=\"标题：\" prop=\"title\">\n              <el-input v-model=\"form.title\" placeholder=\"请输入名称\" maxlength=\"30\"/>\n            </el-form-item>\n            <el-form-item label=\"内容：\" prop=\"reason\">\n              <el-input v-model=\"form.reason\" placeholder=\"请输入名称\" maxlength=\"30\"/>\n            </el-form-item>\n            <el-form-item label=\"审批意见：\" prop=\"pass\">\n              <el-select v-model=\"form.extData.pass\" placeholder=\"请选择\">\n                <el-option label=\"同意\" :value=\"true\" />\n                <el-option label=\"拒绝\" :value=\"false\" />\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"批注：\" prop=\"description\">\n              <el-input v-model=\"form.extData.description\" type=\"textarea\" placeholder=\"请输入内容\"></el-input>\n            </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog title=\"申请详情\" :visible.sync=\"openInfo\" width=\"600px\" append-to-body :close-on-click-modal=false v-dialogDrag>\n      <el-form ref=\"formInfo\" :model=\"formInfo\"   label-width=\"120px\">\n        <el-form-item label=\"申请人：\" prop=\"applyName    \">\n          <el-input v-model=\"formInfo.applyUser\" placeholder=\"请输入名称\" maxlength=\"30\"/>\n        </el-form-item>\n        <el-form-item label=\"申请时间\">\n          <el-date-picker\n            v-model=\"formInfo.applyTime\"\n            type=\"datetime\"\n            format=\"yyyy-MM-dd HH:mm:ss\"\n            value-format=\"yyyy-MM-dd HH:mm:ss\"\n            placeholder=\"选择日期时间\">\n          </el-date-picker>\n        </el-form-item>\n        <el-form-item label=\"标题：\" prop=\"title\">\n          <el-input v-model=\"formInfo.title\" placeholder=\"请输入名称\" maxlength=\"30\"/>\n        </el-form-item>\n        <el-form-item label=\"内容：\" prop=\"reason\">\n          <el-input v-model=\"formInfo.reason\" placeholder=\"请输入名称\" maxlength=\"30\"/>\n        </el-form-item>\n      </el-form>\n    </el-dialog>\n\n    <el-dialog title=\"审批历史\" :visible.sync=\"openHistory\" width=\"800px\" append-to-body :close-on-click-modal=false v-dialogDrag>\n     <slot v-if=\"instanceId!=null\">\n       <activiti-history :instance-id=\"instanceId\" />\n     </slot>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitHistory\">确 定</el-button>\n        <el-button @click=\"cancelHistory\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog title=\"审批进度\" :visible.sync=\"openLoadingImg\" width=\"800px\" append-to-body :close-on-click-modal=false v-dialogDrag>\n      <img :src=\"imgSrc\"/>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitLoadingImg\">确 定</el-button>\n        <el-button @click=\"cancelLoadingImg\">关 闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  import { listTodo,getBusinessData,complete} from \"@/api/activiti/todoitem\";\n  import ActivitiHistory from \"./history\";\n  export default {\n    name: \"TodoItem\",\n    components: {ActivitiHistory},\n    data() {\n      return {\n        openInfo:false,\n        instanceId:null,\n        // 遮罩层\n        loading: true,\n        // 选中数组\n        ids: [],\n        title:'审批',\n        imgSrc:'',\n        // 非单个禁用\n        single: true,\n        // 非多个禁用\n        multiple: true,\n        // 显示搜索条件\n        showSearch: true,\n        dateRange: [],\n        dateRange1:[],\n        // 总条数\n        total: 0,\n        // 用户表格数据\n        todoList: null,\n        // 是否显示弹出层\n        open: false,\n        openHistory:false,\n        openLoadingImg:false,\n        // 部门名称\n        // 表单参数\n        form: {\n          extData:{}\n        },\n        formInfo:{},\n        selectDate:[],\n        // 查询参数\n        queryParams: {\n          id:undefined,\n          pageNum: 1,\n          pageSize: 10,\n          itemName: undefined,\n          module: undefined,\n          taskName:undefined,\n          todoUserName:undefined,\n          handleUserName:undefined,\n        },\n        // 表单校验\n        rules: {\n          'extData.pass': [\n            {required: true, message: \"审批意见不能为空\", trigger: \"blur\"},\n          ],\n          name: [\n            {required: true, message: \"名称不能为空\", trigger: \"blur\"},\n          ],\n        },\n      };\n    },\n    created() {\n      this.getList();\n      this.queryParams.id = this.$route.params.id;\n    },\n    methods: {\n      /** 查询用户列表 */\n      getList() {\n        this.loading = true;\n        listTodo(this.queryParams).then(\n          (response) => {\n            this.todoList = response.data.records;\n            this.total = response.data.total;\n            this.loading = false;\n          }\n        );\n      },\n     /**\n      * 取消按钮\n      * */\n      cancel() {\n        this.open = false;\n        this.reset();\n      },\n      /**\n       * 表单重置\n       * */\n      reset() {\n        this.form = {\n          businessId:undefined,\n          applyUser : undefined,\n          applyTime:undefined,\n          title:undefined,\n          reason:undefined,\n          businessType:undefined,\n          extData:{\n            pass: true,\n            description:undefined\n          },\n        };\n        this.resetForm(\"form\");\n      },\n      resetFormInfo(){\n        this.formInfo = {\n          businessId:undefined,\n          applyUser : undefined,\n          applyTime:undefined,\n          title:undefined,\n          reason:undefined,\n          type:undefined,\n        };\n        this.resetForm(\"formInfo\");\n      },\n      /** 搜索按钮操作 */\n      handleQuery() {\n        this.queryParams.pageNum = 1;\n        this.getList();\n      },\n      /** 重置按钮操作 */\n      resetQuery() {\n        this.dateRange = [];\n        this.resetForm(\"queryForm\");\n        this.handleQuery();\n      },\n      /***多选框选中数据***/\n      handleSelectionChange(selection) {\n        this.selectDate = selection;\n        this.ids = selection.map((item) => item.userId);\n        this.single = selection.length != 1;\n        this.multiple = !selection.length;\n      },\n      /** 办理按钮操作 */\n      handleComplete(row) {\n        this.reset();\n        this.open = true;\n        let ids = this.ids;\n        if(ids.length!=1){\n          this.msgWarning(\"请选择一条数据\");\n          return false;\n        }\n        let selectRow = this.selectDate[0];\n        let query = {taskId:selectRow.taskId,module:selectRow.module}\n        let _this = this;\n        getBusinessData(query).then(res=>{\n          let responseData = res.data;\n          _this.form.applyTime = responseData.applyTime;\n          _this.form.applyUser = responseData.applyUser;\n          _this.form.businessId = responseData.businessId;\n          _this.form.businessType = responseData.businessType;\n          _this.form.instanceId = responseData.instanceId;\n          _this.form.reason = responseData.reason;\n          _this.form.taskId = responseData.taskId;\n          _this.form.title = responseData.title;\n          _this.form.variables = responseData.variables;\n          _this.form.extData.pass = true;\n        })\n\n      },\n      /** 提交按钮 */\n      submitForm: function () {\n        this.$refs[\"form\"].validate((valid) => {\n          if (valid) {\n            let selectData = this.selectDate[0];\n            complete(this.form,selectData.taskId).then((response) => {\n              if (response.code === '0000') {\n                this.msgSuccess(\"审批成功\");\n                this.open = false;\n                this.getList();\n              }\n            });\n          }\n        });\n      },\n      /** 申请详情按钮操作 */\n      handleApplyInfo() {\n        this.resetFormInfo();\n        let ids = this.ids;\n        if(ids.length!=1){\n          this.msgWarning(\"请选择一条数据\");\n          return false;\n        }\n        let selectRow = this.selectDate[0];\n        let query = {taskId:selectRow.taskId,module:selectRow.module}\n        this.openInfo = true;\n        getBusinessData(query).then(res=>{\n          this.formInfo = res.data;\n        })\n      },\n      /***  审批历史 ***/\n      handleHistory(row){\n          this.openHistory = true;\n          this.instanceId = row.instanceId\n      },\n      /** 委托 **/\n      handleDelegation(row){\n        deploy(row.id).then(res =>{\n          if(res.code=='0000'){\n            this.msgSuccess(res.msg);\n          }else{\n            this.msgError(res.msg);\n          }\n        })\n      },\n      /**** 进度查看  ****/\n      handlePlan(row){\n        this.openLoadingImg = true\n        this.imgSrc =\"/activiti-api/process/read-resource?instanceId=\"+row.instanceId+\"&t=\"+new Date().getTime();\n      },\n      /** 是否查看 **/\n      hasLookOver(row){\n        return row.isView=='0'?'否':row.isView='1'?'是':'-';\n      },\n      /** 是否处理 **/\n      hasHandler(row){\n        return row.isView=='0'?'否':row.isView='1'?'是':'-';\n      },\n      /** 审批历史确定  **/\n      submitHistory(){\n        this.openHistory = false\n      },\n      /**** 关闭审批历史  ****/\n      cancelHistory(){\n        this.openHistory = false\n      },\n      /** 进度查看  **/\n      submitLoadingImg(){\n        this.openLoadingImg = false;\n      },\n      cancelLoadingImg(){\n        this.openLoadingImg = false\n      },\n    },\n  };\n</script>\n"]}]}