{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\symbwh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\symbwh.vue", "mtime": 1706897323686}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["symbwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+ZA;;AACA;;AAYA;;AACA;;AACA;;AACA;;AACA;;AAIA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAxBA;eA0BA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,MAAA,EAAA,eAAA;AAAA,IAAA,YAAA,EAAA;AAAA,GAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA,MAAA,WAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,IADA;AAEA;AACA,MAAA,cAAA,EAAA,CACA;AACA,QAAA,QAAA,EAAA,MADA;AAEA,QAAA,QAAA,EAAA,MAFA;AAGA,QAAA,QAAA,EAAA,MAHA;AAIA,QAAA,QAAA,EAAA,MAJA;AAKA,QAAA,QAAA,EAAA,EALA;AAMA,QAAA,QAAA,EAAA,EANA;AAOA,QAAA,QAAA,EAAA,EAPA;AAQA,QAAA,QAAA,EAAA;AARA,OADA,EAWA;AACA,QAAA,QAAA,EAAA,MADA;AAEA,QAAA,QAAA,EAAA,KAFA;AAGA,QAAA,QAAA,EAAA,KAHA;AAIA,QAAA,QAAA,EAAA,KAJA;AAKA,QAAA,QAAA,EAAA,EALA;AAMA,QAAA,QAAA,EAAA,EANA;AAOA,QAAA,QAAA,EAAA,EAPA;AAQA,QAAA,QAAA,EAAA;AARA,OAXA,EAqBA;AACA,QAAA,QAAA,EAAA,MADA;AAEA,QAAA,QAAA,EAAA,SAFA;AAGA,QAAA,QAAA,EAAA,WAHA;AAIA,QAAA,QAAA,EAAA,MAJA;AAKA,QAAA,QAAA,EAAA,EALA;AAMA,QAAA,QAAA,EAAA,EANA;AAOA,QAAA,QAAA,EAAA,EAPA;AAQA,QAAA,QAAA,EAAA;AARA,OArBA,CAHA;AAmCA;AACA,MAAA,cAAA,EAAA,CAAA;AACA,oBAAA,MADA;AAEA,oBAAA,MAFA;AAGA,oBAAA,EAHA;AAIA,oBAAA,EAJA;AAKA,oBAAA,EALA;AAMA,oBAAA;AANA,OAAA,CApCA;AA4CA;AACA,MAAA,GAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAA,EAAA;AAAA;AACA,QAAA,OAAA,EAAA,EADA;AACA;AACA,QAAA,MAAA,EAAA,EAFA,CAEA;;AAFA,OAAA,CA7CA;AAiDA;AACA,MAAA,oBAAA,EAAA,KAlDA;AAmDA,MAAA,QAAA,EAAA;AACA,QAAA,EAAA,EAAA,WADA;AACA;AACA,QAAA,QAAA,EAAA,QAFA;AAEA;AACA,QAAA,YAAA,EAAA,QAHA;AAIA,QAAA,OAAA,EAAA;AAJA,OAnDA;AA0DA,MAAA,MAAA,EAAA,EA1DA;AA2DA;AACA,MAAA,SAAA,EAAA,EA5DA;AA6DA;AACA,MAAA,SAAA,EAAA,EA9DA;AA+DA;AACA,MAAA,kBAAA,EAAA,KAhEA;AAiEA,MAAA,cAAA,EAAA;AACA;AACA,QAAA,MAAA,EAAA,SAFA;AAGA;AACA,QAAA,UAAA,EAAA,EAJA;AAKA,QAAA,QAAA,EAAA,CALA;AAMA,QAAA,IAAA,EAAA;AANA,OAjEA;AAyEA;AACA,MAAA,uBAAA,EAAA,KA1EA;AA2EA;AACA,MAAA,mBAAA,EAAA,KA5EA;AA6EA;AACA,MAAA,kBAAA,EAAA;AACA,QAAA,MAAA,EAAA,SADA;AAEA,QAAA,MAAA,EAAA,EAFA;AAGA,QAAA,OAAA,EAAA,CAHA;AAIA,QAAA,QAAA,EAAA,EAJA;AAKA,QAAA,QAAA,EAAA,CALA;AAMA,QAAA,KAAA,EAAA;AANA,OA9EA;AAsFA;AACA,MAAA,iBAAA,EAAA,EAvFA;AAwFA;AACA,MAAA,cAAA,EAAA,CAzFA;AA0FA;AACA,MAAA,SAAA,EAAA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AADA,OA3FA;AAgGA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,IAAA,EAAA;AADA,SADA;AAIA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA;AAJA,OAjGA;AAyGA;AACA,MAAA,WAAA,EAAA,IA1GA;AA2GA;AACA,MAAA,KAAA,EAAA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,QAAA,EAAA,OAFA;AAGA,QAAA,MAAA,EAAA;AAHA,OA5GA;AAiHA;AACA,MAAA,UAAA,EAAA,EAlHA;AAmHA;AACA,MAAA,IAAA,EAAA,EApHA;AAqHA;AACA,MAAA,cAAA,EAAA;AACA,QAAA,MAAA,EAAA,SADA;AAEA,QAAA,IAAA,EAAA,SAFA;AAGA,QAAA,OAAA,EAAA,CAHA;AAIA,QAAA,QAAA,EAAA;AAJA,OAtHA;AA4HA;AACA,MAAA,QAAA,EAAA,EA7HA;AA8HA;AACA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,CAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,SARA;AAyBA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAzBA,OA/HA;AA0JA;AACA,MAAA,WAAA,EAAA,EA3JA;AA6JA,MAAA,aAAA,EAAA,KA7JA;AA8JA,MAAA,KAAA,EAAA,EA9JA;AAgKA;AACA,MAAA,cAAA,EAAA,IAjKA;AAkKA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,EAAA,EAAA,SADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,QAAA,EAAA;AAHA,OAnKA;AAwKA,MAAA,UAAA,EAAA,KAxKA;AAyKA,MAAA,MAAA,EAAA,IAzKA;AA2KA;AACA,MAAA,eAAA,EAAA,MA5KA;AA6KA;AACA,MAAA,gBAAA,EAAA,KA9KA;AAgLA;AACA,MAAA,SAAA,EAAA,CAjLA;AAkLA;AACA,MAAA,eAAA,EAAA;AACA,QAAA,MAAA,EAAA,SADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,QAAA,EAAA,EAHA;AAIA,QAAA,QAAA,EAAA,CAJA;AAKA,QAAA,KAAA,EAAA;AALA,OAnLA;AA0LA,MAAA,MAAA,EAAA,SA1LA;AA6LA;AACA,MAAA,gBAAA,EAAA;AACA,QAAA,MAAA,EAAA,SADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,OAAA,EAAA;AAHA,OA9LA;AAoMA;AACA,MAAA,cAAA,EAAA,EArMA;AAsMA;AACA,MAAA,eAAA,EAAA,EAvMA;AAwMA;AACA,MAAA,QAAA,EAAA,EAzMA;AA0MA;AACA,MAAA,eAAA,EAAA,EA3MA;AA4MA;AACA,MAAA,qBAAA,EAAA,EA7MA;AA8MA;AACA,MAAA,YAAA,EAAA,KA/MA;AAgNA;AACA,MAAA,OAAA,EAAA,EAjNA;AAmNA;AACA,MAAA,MAAA,EAAA,EApNA;AAsNA;AACA,MAAA,YAAA,EAAA,EAvNA;AAwNA;AACA,MAAA,UAAA,EAAA,CAAA,MAAA,EAAA,IAAA,EAAA,IAAA,CAzNA;AA0NA,MAAA,OAAA,EAAA,CA1NA,CA0NA;;AA1NA,KAAA;AA4NA,GAhOA;AAiOA,EAAA,KAAA,EAAA,EAjOA;AAkOA,EAAA,OAlOA,qBAkOA;AACA;AACA,SAAA,OAAA;AACA,GArOA;AAsOA,EAAA,OAtOA,qBAsOA,CACA,CAvOA;AAwOA,EAAA,OAAA,EAAA;AACA,IAAA,QADA,oBACA,GADA,EACA,MADA,EACA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,KAAA,EAAA,GAAA,EAAA,MAAA;AACA,MAAA,GAAA,CAAA,MAAA,CAAA,QAAA,GAAA,QAAA,CAAA,GAAA,IAAA;AACA,KAJA;AAKA,IAAA,OALA,qBAKA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,MAAA,EAAA,IAAA,EAAA,KAAA,SAAA;AACA,KAPA;AAQA;AACA,IAAA,eATA,iCASA;AAAA,UAAA,GAAA,QAAA,GAAA;AAAA,UAAA,MAAA,QAAA,MAAA;AAAA,UAAA,QAAA,QAAA,QAAA;AAAA,UAAA,WAAA,QAAA,WAAA;;AACA,UAAA,KAAA,UAAA,CAAA,QAAA,CAAA,GAAA,CAAA,IAAA,CAAA,EAAA;AACA,YAAA,WAAA,GAAA,CAAA,EAAA;AACA,iBAAA,CAAA,CAAA,EAAA,GAAA,CAAA,QAAA,CAAA;AACA;AACA;AACA,KAfA;AAgBA;AACA,IAAA,cAjBA,iCAiBA;AAAA,UAAA,GAAA,SAAA,GAAA;AAAA,UAAA,MAAA,SAAA,MAAA;AAAA,UAAA,QAAA,SAAA,QAAA;AAAA,UAAA,WAAA,SAAA,WAAA;;AACA,UAAA,WAAA,GAAA,CAAA,EAAA;AACA,eAAA,CAAA,CAAA,EAAA,CAAA,CAAA;AACA;AACA,KArBA;AAsBA;AACA,IAAA,YAvBA,wBAuBA,GAvBA,EAuBA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,GAAA,EADA,CAEA;AACA;AACA;;AACA,WAAA,oBAAA,GAAA,IAAA,CALA,CAMA;;AACA,WAAA,cAAA,CAAA,GAAA;AACA,KA/BA;AAgCA;AACA,IAAA,aAjCA,2BAiCA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,UAAA,OAAA,GAAA,CAAA,CAFA,CAEA;;AACA,UAAA,KAAA,GAAA,MAAA;AACA,UAAA,MAAA,GAAA,IAAA;AACA,MAAA,IAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,YAAA,MAAA,GAAA,QAAA,CAAA,cAAA,CAAA,gBAAA,CAAA;AACA,YAAA,UAAA,GAAA,MAAA,CAAA,WAAA,GAAA,KAAA,GAAA,MAAA;AACA,YAAA,MAAA,GAAA,QAAA,CAAA,cAAA,CAAA,gBAAA,EAAA,UAAA,CAJA,CAIA;;AACA,aAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,MAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,UAAA,IAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,CAAA,EAAA,OAAA,EADA,CAEA;;AACA,cAAA,QAAA,GAAA,IAAA,CAAA,IAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,SAAA,GAAA,MAAA,CAAA,CAAA,CAAA,CAAA,YAAA,IAAA,UAAA,CAAA;AACA,cAAA,YAAA,GAAA,MAAA,CAAA,CAAA,CAAA,CAAA,YAAA;AACA,UAAA,OAAA,IAAA,YAAA;AACA;;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,IAAA,EAAA,QAAA,CAAA,cAAA,CAAA,gBAAA,EAAA,UAAA,EAZA,CAaA;AACA,OAdA;AAeA,KArDA;AAsDA;AACA,IAAA,YAvDA,0BAuDA;AACA,UAAA,IAAA,GAAA,IAAA;AACA,UAAA,OAAA,GAAA,CAAA,CAFA,CAEA;;AACA,UAAA,KAAA,GAAA,MAAA;AACA,UAAA,MAAA,GAAA,IAAA;AACA,MAAA,IAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,YAAA,MAAA,GAAA,QAAA,CAAA,cAAA,CAAA,gBAAA,CAAA;AACA,YAAA,UAAA,GAAA,MAAA,CAAA,WAAA,GAAA,KAAA,GAAA,MAAA;AACA,YAAA,MAAA,GAAA,QAAA,CAAA,cAAA,CAAA,gBAAA,EAAA,UAAA,CAJA,CAIA;;AACA,aAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,MAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,cAAA,YAAA,GAAA,MAAA,CAAA,CAAA,CAAA,CAAA,YAAA;AACA,UAAA,OAAA,IAAA,YAAA,CAFA,CAGA;;AACA,cAAA,OAAA,IAAA,MAAA,EAAA;AACA,YAAA,IAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,CAAA,EAAA,OAAA;AACA;AACA,SAZA,CAaA;;AACA,OAdA;AAeA,KA3EA;AA4EA;AACA,IAAA,OA7EA,mBA6EA,GA7EA,EA6EA,OA7EA,EA6EA;AAAA;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA,EAAA,GAAA,EAAA,OAAA,EADA,CAEA;;AACA,UAAA,KAAA,GAAA,MAAA;AACA,UAAA,MAAA,GAAA,IAAA,CAJA,CAKA;AACA;AACA;AACA;;AACA,UAAA,OAAA,IAAA,MAAA,EAAA;AACA;AACA,QAAA,OAAA,GAAA,OAAA,GAAA,GAAA,CAAA,YAAA,CAFA,CAGA;AACA;;AACA,QAAA,GAAA,CAAA,UAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,OAAA,IAAA,IAAA,CAAA,YAAA;;AACA,cAAA,OAAA,IAAA,MAAA,EAAA;AACA;AACA,YAAA,OAAA,GAAA,OAAA,GAAA,IAAA,CAAA,YAAA;AACA,YAAA,OAAA,CAAA,GAAA,CAAA,MAAA,EAAA,IAAA,EAAA,OAAA,EAAA,IAAA,CAAA,UAAA;AACA,YAAA,IAAA,CAAA,UAAA,CAAA,OAAA,CAAA,UAAA,KAAA,EAAA;AACA,cAAA,OAAA,IAAA,KAAA,CAAA,YAAA;;AACA,kBAAA,OAAA,IAAA,MAAA,EAAA;AAEA,gBAAA,OAAA,CAAA,GAAA,CAAA,QAAA,EAAA,KAAA,EAFA,CAGA;;AACA,oBAAA,WAAA,GAAA,KAAA,CAAA,UAAA;AACA,oBAAA,IAAA,GAAA,KAAA,CAAA,WAAA,CALA,CAMA;;AACA,oBAAA,OAAA,GAAA,QAAA,CAAA,aAAA,CAAA,MAAA,CAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,UAAA,GAAA,MAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,MAAA,GAAA,OAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,KAAA,GAAA,QAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,OAAA,GAAA,OAAA;AACA,gBAAA,OAAA,CAAA,KAAA,CAAA,SAAA,GAAA,gBAAA,CAZA,CAaA;AACA;;AACA,oBAAA,IAAA,EAAA;AACA,kBAAA,WAAA,CAAA,YAAA,CAAA,OAAA,EAAA,IAAA;AACA,iBAFA,MAEA;AACA,kBAAA,WAAA,CAAA,WAAA,CAAA,OAAA;AACA;;AACA,gBAAA,KAAA,CAAA,WAAA;AACA;AACA,aAxBA,EAJA,CA8BA;AACA,WAjCA,CAkCA;;AACA,SAnCA;AAoCA;AACA,KAhIA;AAiIA;AACA,IAAA,WAlIA,yBAkIA;AACA,yBAAA,WAAA,CAAA,QAAA,CAAA,aAAA,CAAA,iBAAA,CAAA,EAAA,KAAA,MAAA,CAAA,IAAA;AACA,KApIA;AAqIA;AACA,IAAA,iBAtIA,6BAsIA,KAtIA,EAsIA;AAAA;;AACA,qCAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,MAAA,GAAA,GAAA,CAAA,IAAA,CADA,CAEA;AACA;AACA;;AACA,QAAA,MAAA,CAAA,oBAAA,GAAA,IAAA;AACA,OANA;AAQA,KA/IA;AAgJA;AACA,IAAA,cAjJA,0BAiJA,OAjJA,EAiJA;AACA;AACA,WAAA,YAAA,GAAA,EAAA,CAFA,CAGA;AACA;;AACA,UAAA,IAAA,GAAA,IAAA;AACA,iCAAA;AAAA,QAAA,MAAA,EAAA,OAAA,CAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,GAAA,GAAA,CAAA,IAAA;AACA,YAAA,GAAA,GAAA,QAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA,OAAA,CAAA,KAAA;AACA,0CAAA;AAAA,UAAA,MAAA,EAAA,OAAA,CAAA;AAAA,SAAA,EAAA,IAAA,CAAA,UAAA,MAAA,EAAA;AACA,cAAA,SAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA;AACA,cAAA,SAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA;AACA,cAAA,GAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,SAAA,EAAA,IAAA,GAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,mCAAA,GAAA,EAAA,SAAA,CAAA,GAAA,CAAA;AACA,WAFA;AAGA,cAAA,IAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,SAAA,EAAA,IAAA,GAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,mCAAA,GAAA,EAAA,SAAA,CAAA,GAAA,CAAA;AACA,WAFA;AAGA,UAAA,IAAA,CAAA,UAAA,CAAA,GAAA,CAAA,MAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,EAAA,GAAA,EAAA,EAAA,EAAA,UAAA;AACA,UAAA,IAAA,CAAA,UAAA,CAAA,GAAA,CAAA,MAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,EAAA,IAAA,EAAA,EAAA,EAAA,UAAA;AACA,SAbA;AAcA,OAlBA;AAmBA,KA1KA;AA2KA,IAAA,UA3KA,sBA2KA,OA3KA,EA2KA,OA3KA,EA2KA,GA3KA,EA2KA,QA3KA,EA2KA;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,OAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,YAAA,EAAA,GAAA,OAAA,CAAA,CAAA,CAAA,CAAA,GAAA;AACA,YAAA,EAAA,GAAA,OAAA,CAAA,CAAA,CAAA,CAAA,GAAA;AACA,aAAA,OAAA,GAAA,MAAA,MAAA,CAAA,EAAA,CAAA;AACA,YAAA,IAAA,GAAA,OAAA,CAAA,CAAA,CAAA;;AACA,aAAA,IAAA,IAAA,IAAA,IAAA,EAAA;AACA,cAAA,OAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AAAA;AACA,YAAA,GAAA,IAAA,8CAAA,EAAA,GAAA,GAAA,GAAA,IAAA,GAAA,YAAA;AACA;;AACA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,EAAA,EAAA,CAAA,EAAA,EAAA;AAAA;AACA,gBAAA,IAAA,GAAA,MAAA;;AACA,iBAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,IAAA,CAAA,IAAA,CAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AAAA;AACA,kBAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,QAAA,EAAA;AACA,oBAAA,IAAA,GAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,IAAA;AACA,oBAAA,IAAA,GAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAFA,CAEA;;AACA,oBAAA,KAAA,GAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,KAAA;AACA,oBAAA,EAAA,GAAA,EAAA;;AACA,oBAAA,IAAA,IAAA,IAAA,EAAA;AACA,kBAAA,IAAA,GAAA,EAAA;AACA;;AACA,gBAAA,EAAA,GAAA,IAAA;;AACA,oBAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,OAAA,IAAA,GAAA,EAAA;AAAA;AACA,kBAAA,IAAA,IAAA,gCACA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,OADA,GAEA,aAFA,GAGA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,OAHA,GAIA,kBAJA,GAKA,KAAA,OAAA,GAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,OALA,GAMA,MANA,GAOA,EAPA,GAOA,OAPA;AAQA,iBATA,MASA;AACA,kBAAA,IAAA,IAAA,gCACA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,OADA,GAEA,aAFA,GAGA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,OAHA,GAIA,kCAJA,GAKA,KAAA,OAAA,GAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,OALA,GAMA,KANA,GAOA,EAPA,GAOA,OAPA;AAQA;AACA;AACA;;AACA,YAAA,IAAA,IAAA,OAAA;AACA,YAAA,GAAA,IAAA,IAAA;AACA;AACA;AACA,OA/CA,CAgDA;;;AACA,WAAA,SAAA,CAAA,YAAA;AACA,YAAA,SAAA,GAAA,QAAA,CAAA,cAAA,CAAA,QAAA,CAAA;AACA,QAAA,SAAA,CAAA,SAAA,GAAA,EAAA;AACA,QAAA,SAAA,CAAA,SAAA,GAAA,GAAA;AACA,OAJA;AAKA,KAjOA;AAkOA;AACA,IAAA,cAnOA,0BAmOA,MAnOA,EAmOA,YAnOA,EAmOA;AACA,UAAA,QAAA,GAAA,EAAA;AACA,MAAA,QAAA,CAAA,MAAA,GAAA,MAAA;;AACA,WAAA,IAAA,GAAA,IAAA,YAAA,CAAA,CAAA,CAAA,EAAA;AACA,QAAA,QAAA,CAAA,GAAA,CAAA,GAAA,YAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA;AACA;;AACA,WAAA,YAAA,CAAA,IAAA,CAAA,QAAA;AACA,KA1OA;AA2OA;AACA,IAAA,mBA5OA,iCA4OA;AAAA;;AACA,WAAA,GAAA,GAAA,EAAA,CADA,CAEA;AACA;;AACA,UAAA,IAAA,GAAA,KAAA,YAAA;;AACA,UAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AAAA,mCACA,CADA;AAEA,cAAA,SAAA,GAAA,IAAA,CAAA,CAAA,CAAA;AACA,cAAA,KAAA,GAAA,SAAA,CAAA,MAAA,CAHA,CAGA;;AACA,cAAA,MAAA,GAAA,SAAA,CAAA,MAAA,CAJA,CAIA;;AACA,cAAA,OAAA,GAAA,SAAA,CAAA,OAAA,CALA,CAKA;;AACA,cAAA,EAAA,GAAA,CACA;AACA,qBAAA,KADA;AACA;AACA,2BAAA,MAFA,CAEA;;AAFA,WADA,CAAA;AAMA,UAAA,OAAA,CAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,EAAA,CAAA,IAAA,CAAA;AACA,uBAAA,GAAA,CAAA,OADA;AACA;AACA,6BAAA,EAFA,CAEA;;AAFA,aAAA;AAIA,WALA;AAMA,cAAA,EAAA,GAAA,EAAA;AACA,UAAA,MAAA,CAAA,OAAA,CAAA,UAAA,EAAA,EAAA;AACA,YAAA,EAAA,CAAA,IAAA,CAAA;AACA,sBAAA,EAAA,CAAA,IADA;AAEA,0BAAA,OAAA,CAAA,MAFA,CAEA;;AAFA,aAAA;AAIA,WALA,EAnBA,CAyBA;;AACA,UAAA,EAAA,CAAA,IAAA,CACA;AACA,oBAAA,IADA;AAEA,wBAAA,OAAA,CAAA,MAFA,CAEA;;AAFA,WADA,EAKA;AACA,oBAAA,MADA;AAEA,wBAAA,OAAA,CAAA,MAFA,CAEA;;AAFA,WALA,EASA;AACA,oBAAA,IADA;AAEA,wBAAA,OAAA,CAAA,MAFA,CAEA;;AAFA,WATA,EAaA;AACA,oBAAA,IADA;AAEA,wBAAA,OAAA,CAAA,MAFA,CAEA;;AAFA,WAbA;;AAkBA,UAAA,MAAA,CAAA,GAAA,CAAA,IAAA,CAAA;AACA,YAAA,KAAA,EAAA,KADA;AAEA,YAAA,OAAA,EAAA,EAFA;AAGA,YAAA,MAAA,EAAA;AAHA,WAAA;AA5CA;;AACA,aAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,IAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AAAA,gBAAA,CAAA;AAgDA;AACA,OAvDA,CAwDA;;AACA;;;;;;;;;;;;;;;AAeA,KApTA;AAqTA;AACA,IAAA,WAtTA,uBAsTA,MAtTA,EAsTA,GAtTA,EAsTA;AACA;AACA,2BAAA,cAAA,EAAA,IAAA,CAAA,EAAA,EAFA,CAGA;;AACA,WAAA,MAAA,GAAA,EAAA;AACA,WAAA,MAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CALA,CAMA;;AACA,UAAA,GAAA,GAAA,EAAA,CAPA,CAQA;;AACA,UAAA,MAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,YAAA,MAAA,CAAA,CAAA,CAAA,CAAA,IAAA,IAAA,GAAA,EAAA;AAAA;AACA;AACA,UAAA,GAAA,IAAA,2DACA,mDADA,GAEA,mDAFA,GAGA,mDAHA,GAIA,OAJA,CAFA,CAOA;;AACA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,MAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,YAAA,GAAA,IAAA,MAAA;AACA,YAAA,GAAA,IAAA,6CAAA;AACA,YAAA,GAAA,IAAA,MAAA,CAAA,CAAA,CAAA,CAAA,KAAA,GAAA,OAAA;AACA,YAAA,GAAA,IAAA,YAAA;AACA,YAAA,GAAA,IAAA,YAAA;AACA,YAAA,GAAA,IAAA,YAAA;AACA,YAAA,GAAA,IAAA,OAAA;AACA;AACA,SAjBA,MAiBA;AAAA;AACA;AACA;AACA,cAAA,GAAA,GAAA,CAAA,CAHA,CAIA;;AACA,cAAA,KAAA,GAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAAA,MAAA,GAAA,GAAA,CAAA,CALA,CAMA;;AACA,eAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,EAAA,CAAA,EAAA,EAAA;AACA,YAAA,GAAA,IAAA,MAAA,CADA,CAEA;;AACA,iBAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,GAAA,EAAA,CAAA,EAAA,EAAA;AACA,kBAAA,CAAA,GAAA,GAAA,GAAA,CAAA,GAAA,MAAA,CAAA,MAAA,EAAA;AACA,gBAAA,GAAA,IAAA,6CAAA,CADA,CAEA;;AACA,gBAAA,GAAA,IAAA,MAAA,CAAA,CAAA,GAAA,GAAA,GAAA,CAAA,CAAA,CAAA,KAAA,GAAA,OAAA,CAHA,CAIA;;AACA,gBAAA,GAAA,IAAA,MAAA,CAAA,CAAA,GAAA,GAAA,GAAA,CAAA,CAAA,CAAA,IAAA,IAAA,CAAA,GAAA,4DAAA,GAAA,SAAA,MAAA,CAAA,CAAA,GAAA,GAAA,GAAA,CAAA,CAAA,CAAA,WAAA,GAAA,OAAA;AACA;AACA;;AACA,YAAA,GAAA,IAAA,OAAA;AACA;AACA;AACA,OAjDA,CAkDA;;;AACA,WAAA,SAAA,CAAA,YAAA;AACA,6BAAA,cAAA,EAAA,MAAA,CAAA,GAAA;AACA,OAFA,EAnDA,CAsDA;;AACA,WAAA,oBAAA,GAAA,IAAA;AACA,KA9WA;AA+WA;AACA,IAAA,aAhXA,2BAgXA;AACA;AACA,WAAA,MAAA,GAAA,EAAA,CAFA,CAGA;;AACA,WAAA,oBAAA,GAAA,KAAA;AACA,KArXA;AAuXA;AACA,IAAA,eAxXA,2BAwXA,GAxXA,EAwXA;AACA;AACA,WAAA,kBAAA,GAAA,IAAA,CAFA,CAGA;;AACA,WAAA,SAAA,GAAA,GAAA;AACA,KA7XA;AA8XA;AACA,IAAA,eA/XA,6BA+XA;AAAA;;AACA,oCAAA,KAAA,kBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,iBAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,MAAA,CAAA,kBAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,MAAA,CAAA,cAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,OAJA;AAKA,KArYA;AAsYA;AACA,IAAA,SAvYA,uBAuYA;AACA,WAAA,eAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,KA1YA;AA4YA;AACA,IAAA,iBA7YA,+BA6YA;AACA,WAAA,mBAAA,GAAA,KAAA;AACA,WAAA,kBAAA,CAAA,IAAA,GAAA,SAAA;AACA,KAhZA;AAkZA;AACA,IAAA,gBAnZA,8BAmZA;AAAA;;AACA,WAAA,QAAA,GAAA,EAAA;;AACA,UAAA,KAAA,cAAA,CAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,aAAA,EADA,CAEA;;AACA,aAAA,mBAAA,GAAA,KAAA;AACA,OAJA,MAIA;AACA,aAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,eAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,eAAA,cAAA,GAAA,EAAA;AACA,eAAA,cAAA,CAAA,IAAA,GAAA,KAAA,eAAA,CAAA,CAAA,EAAA,KAAA;AACA,eAAA,cAAA,CAAA,MAAA,GAAA,KAAA,MAAA;AACA,eAAA,cAAA,CAAA,QAAA,GAAA,CAAA;AACA,eAAA,QAAA,CAAA,IAAA,CAAA,KAAA,cAAA;AACA,SAPA,CAQA;;;AACA,qCAAA,KAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA,WAFA,MAEA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;AACA,WALA,CAMA;;;AACA,UAAA,MAAA,CAAA,mBAAA,GAAA,KAAA,CAPA,CAQA;;AACA,UAAA,MAAA,CAAA,2BAAA;AACA,SAVA;AAWA;AACA,KA9aA;AAgbA;AACA,IAAA,6BAjbA,yCAibA,IAjbA,EAibA;AACA,WAAA,cAAA,CAAA,UAAA,GAAA,IAAA;AACA,WAAA,eAAA,GAAA,EAAA;AACA,WAAA,eAAA,GAAA,IAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,KAAA,cAAA,CAAA,UAAA;AACA,KAtbA;AAwbA;AACA,IAAA,eAzbA,6BAybA;AACA,WAAA,eAAA;AACA,KA3bA;AA6bA;AACA,IAAA,aA9bA,2BA8bA;AACA,WAAA,kBAAA,CAAA,MAAA,GAAA,EAAA;AACA,WAAA,eAAA;AACA,KAjcA;AAmcA;AACA,IAAA,cApcA,4BAocA;AAAA;;AACA,0CAAA,KAAA,gBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,MAAA,CAAA,eAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,OAHA;AAIA,KAzcA;AA4cA;AACA,IAAA,eA7cA,2BA6cA,GA7cA,EA6cA;AACA;AACA,WAAA,cAAA,GAAA,EAAA;AACA,WAAA,eAAA,GAAA,EAAA;AACA,WAAA,eAAA,GAAA,EAAA,CAJA,CAKA;;AACA,WAAA,gBAAA,GAAA,IAAA,CANA,CAOA;;AACA,WAAA,eAAA,CAAA,MAAA,GAAA,GAAA,CAAA,KAAA;AACA,WAAA,MAAA,GAAA,GAAA,CAAA,KAAA,CATA,CAUA;;AACA,WAAA,kBAAA,CAAA,MAAA,GAAA,GAAA,CAAA,KAAA,CAXA,CAYA;;AACA,WAAA,cAAA,CAAA,MAAA,GAAA,GAAA,CAAA,KAAA,CAbA,CAcA;;AACA,WAAA,2BAAA;AACA,KA7dA;AA+dA;AACA,IAAA,2BAheA,yCAgeA;AAAA;;AACA,mCAAA,KAAA,eAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,cAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,MAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,MAAA,CAAA,eAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,OAJA;AAKA,KAteA;AAueA;AACA,IAAA,wBAxeA,oCAweA,IAxeA,EAweA;AACA,WAAA,qBAAA,GAAA,IAAA;AACA,KA1eA;AA4eA;AACA,IAAA,YA7eA,0BA6eA;AAAA;;AACA,UAAA,KAAA,qBAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,aAAA;AACA;AACA;;AACA,UAAA,GAAA,GAAA,KAAA,qBAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,eAAA,IAAA,CAAA,MAAA;AACA,OAFA,CAAA;AAGA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,uCAAA,GAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,cAAA,IAAA,SAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,2BAAA;AACA,WANA,MAMA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,2BAAA;AACA;AACA,SAdA;AAeA,OApBA,EAoBA,KApBA,CAoBA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OAzBA;AA0BA,KA/gBA;AAghBA;AACA,IAAA,oBAjhBA,gCAihBA,GAjhBA,EAihBA;AACA,WAAA,gBAAA,CAAA,MAAA,GAAA,GAAA,CAAA,MAAA;AACA,WAAA,cAAA;AACA,KAphBA;AAqhBA;AACA,IAAA,QAthBA,oBAshBA,IAthBA,EAshBA,OAthBA,EAshBA;AAAA;;AACA,UAAA,YAAA,GAAA;AACA,QAAA,GAAA,EAAA,EADA;AAEA,QAAA,OAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,MAAA;AAFA,OAAA;;AAIA,UAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA;AACA,QAAA,YAAA,CAAA,GAAA,GAAA,IAAA;AACA,eAAA,KAAA,WAAA,CAAA,YAAA,EAAA,OAAA,CAAA;AACA;;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,YAAA,CAAA,GAAA,GAAA,IAAA,CAAA,IAAA,CAAA,IAAA;;AACA,QAAA,MAAA,CAAA,WAAA,CAAA,YAAA,EAAA,OAAA;AACA,OAHA,EAGA,GAHA,CAAA;AAIA,KAniBA;AAoiBA;AACA,IAAA,WAriBA,uBAqiBA,QAriBA,EAqiBA,OAriBA,EAqiBA;AACA,+CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,SAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,GAAA;AACA,YAAA,IAAA,EAAA,IAAA,CAAA,IADA;AAEA,YAAA,KAAA,EAAA,IAAA,CAAA,KAFA;AAGA,YAAA,EAAA,EAAA,IAAA,CAAA,EAHA;AAIA,YAAA,GAAA,EAAA,IAAA,CAAA,GAJA;AAKA,YAAA,IAAA,EAAA,KALA;AAMA,YAAA,IAAA,EAAA,IAAA,CAAA;AANA,WAAA;AAQA,UAAA,SAAA,CAAA,IAAA,CAAA,IAAA;AACA,SAVA;AAWA,QAAA,OAAA,CAAA,SAAA,CAAA;AACA,OAdA;AAeA,KArjBA;AAsjBA;AACA,IAAA,IAvjBA,kBAujBA;AAAA;;AACA,WAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,oCAAA,OAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,cAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,GAAA,CAAA,GAAA;;AACA,cAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,cAAA,OAAA,CAAA,OAAA;;AACA,cAAA,OAAA,CAAA,aAAA,GAAA,KAAA;AACA,aALA,MAKA;AACA,cAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,GAAA;AACA;AACA,WATA;AAWA;AACA,OAdA;AAeA,KAvkBA;AAwkBA;AACA,IAAA,eAzkBA,2BAykBA,IAzkBA,EAykBA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA;;AACA,UAAA,IAAA,CAAA,KAAA,IAAA,GAAA,EAAA;AACA;AACA,aAAA,WAAA,GAAA,KAAA;AACA,aAAA,QAAA,GAAA,IAAA;AACA,aAAA,cAAA,CAAA,MAAA,GAAA,IAAA,CAAA,IAAA;AACA,aAAA,OAAA;AACA,OANA,MAMA;AACA,aAAA,WAAA,GAAA,IAAA;AACA;AACA,KAplBA;AAqlBA;AACA,IAAA,eAtlBA,6BAslBA;AACA,WAAA,IAAA,GAAA,EAAA;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,KAAA,QAAA,CAAA,IAAA;AACA,WAAA,IAAA,CAAA,MAAA,GAAA,KAAA,QAAA,CAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,KAAA,GAAA,IAAA;AACA,KA5lBA;AA6lBA;AACA,IAAA,OA9lBA,mBA8lBA,MA9lBA,EA8lBA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,OAAA,CAAA,cAAA,+DAAA,OAAA,CAAA,cAAA,GAAA,MAAA;AACA,gBAAA,KAHA,GAGA,OAAA,CAAA,cAHA;AAAA;AAAA,uBAIA,mCAAA,KAAA,CAJA;;AAAA;AAAA;AAIA,gBAAA,IAJA,yBAIA,IAJA;AAIA,gBAAA,IAJA,yBAIA,IAJA;;AAKA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AARA;AAAA;;AAAA;AAAA;AAAA;AAUA,gBAAA,OAAA,CAAA,GAAA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,KA1mBA;;AA4mBA;;;AAGA,IAAA,qBA/mBA,iCA+mBA,IA/mBA,EA+mBA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KAjnBA;AAonBA,IAAA,KApnBA,mBAonBA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,KAtnBA;AAwnBA;AACA,IAAA,aAznBA,yBAynBA,GAznBA,EAynBA;AACA,WAAA,KAAA,GAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,IAAA,GAAA,GAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,MAAA,GAAA,IAAA;AACA,KA/nBA;AAioBA,IAAA,cAjoBA,0BAioBA,GAjoBA,EAioBA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,GAAA;AACA,KAnoBA;AAqoBA;AACA,IAAA,UAtoBA,sBAsoBA,GAtoBA,EAsoBA;AACA,WAAA,KAAA,GAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,IAAA,GAAA,GAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,MAAA,GAAA,KAAA;AACA,KA5oBA;AA8oBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA,IAAA,kBAlrBA,8BAkrBA,EAlrBA,EAkrBA;AAAA;;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAKA,IALA,CAKA,YAAA;AACA,gCAAA,IAAA,CAAA,SAAA,CAAA,EAAA,CAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,cAAA,IAAA,SAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,OAAA,CAAA,OAAA;AACA,WANA,MAMA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAIA;AACA,SAbA;AAcA,OApBA,EAqBA,KArBA,CAqBA,YAAA;AACA,QAAA,OAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OA1BA;AA2BA,WAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;AACA,WAAA,OAAA;AACA,KAhtBA;AAktBA;AACA,IAAA,YAntBA,0BAmtBA,CAEA,CArtBA;AAwtBA;AACA,IAAA,eAztBA,2BAytBA,GAztBA,EAytBA;AACA,WAAA,YAAA,GAAA,IAAA;AACA,WAAA,OAAA,GAAA,GAAA;AACA,KA5tBA;AA8tBA;AACA,IAAA,aA/tBA,2BA+tBA;AACA,WAAA,YAAA,GAAA,KAAA;AACA,KAjuBA;AAouBA,IAAA,WApuBA,yBAouBA;AACA,WAAA,cAAA,GAAA;AACA,QAAA,MAAA,EAAA,SADA;AAEA,QAAA,IAAA,EAAA,SAFA;AAGA,QAAA,OAAA,EAAA,CAHA;AAIA,QAAA,QAAA,EAAA;AAJA,OAAA;AAMA,KA3uBA;AA4uBA;AACA,IAAA,WA7uBA,yBA6uBA;AAAA;;AACA,WAAA,IAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,YAAA;AACA,QAAA,OAAA,CAAA,IAAA,GAAA,OAAA,CAAA,QAAA,CAAA,IAAA,GAAA,IAAA;;AACA,QAAA,OAAA,CAAA,SAAA,CAAA,MAAA;AACA,OAHA;AAIA;AAnvBA;AAxOA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row :gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card class=\"aside_height\" shadow=\"never\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备类型</span>\n          </div>\n          <div style=\" overflow: auto;height: 84vh\">\n            <el-col style=\"padding:0\">\n              <el-tree id=\"tree\"\n                       :props=\"props\"\n                       highlight-current\n                       :load=\"loadNode\"\n                       lazy\n                       :default-expanded-keys=\"['1']\"\n                       @node-expand=\"handleNodeClick\"\n                       @node-click=\"handleNodeClick\"/>\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 140, itemWidth: 190 }\"\n          @handleReset=\"filterReset\"\n        />\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button @click=\"addSensorButton\"\n                       type=\"primary\" icon=\"el-icon-plus\"\n            >新增\n            </el-button>\n          </div>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"69.8vh\">\n            <el-table-column slot=\"table_six\" align=\"center\" label=\"关联铭牌\" min-width=\"150\" :resizable=\"false\">\n              <template slot-scope=\"scope\">\n                <el-button @click=\"handleClickGlMp(scope.row)\" type=\"text\" size=\"small\" v-if=\"scope.row.sfty == '是'\">\n                  查看铭牌\n                </el-button>\n                <el-button @click=\"handleClickGlMp(scope.row)\" type=\"text\" size=\"small\" v-else>关联铭牌</el-button>\n              </template>\n            </el-table-column>\n\n            <el-table-column slot=\"table_seven\" align=\"center\" label=\"关联项目\" min-width=\"150\" :resizable=\"false\">\n              <template slot-scope=\"scope\">\n                <el-button @click=\"handleClickGlxm(scope.row)\" type=\"text\" size=\"small\" v-if=\"scope.row.sfty == '是'\">\n                  查看项目\n                </el-button>\n                <el-button @click=\"handleClickGlxm(scope.row)\" type=\"text\" size=\"small\" v-else>关联项目</el-button>\n              </template>\n            </el-table-column>\n\n            <!-- <el-table-column slot=\"table_eight\" align=\"center\" label=\"定义模板内容\" min-width=\"150\" :resizable=\"false\">\n              <template slot-scope=\"scope\">\n                <el-button @click=\"handleClickMbnr(scope.row)\" type=\"text\" size=\"small\" v-if=\"scope.row.sfty == '是'\">\n                  查看模板内容\n                </el-button>\n                <el-button @click=\"handleClickMbnr(scope.row)\" type=\"text\" size=\"small\" v-else>定义模板内容</el-button>\n              </template>\n            </el-table-column> -->\n\n            <el-table-column slot=\"table_eight\" align=\"center\" label=\"模板详情\" min-width=\"150\" :resizable=\"false\" height=\"70vh\">\n              <template slot-scope=\"scope\">\n<!--                <el-button @click=\"handleMbInfo(scope.row)\" v-print=\"printObj\">默认按钮</el-button>-->\n                <el-button  type=\"text\" size=\"small\" @click=\"handleMbInfo(scope.row)\">模板详情</el-button>\n              </template>\n            </el-table-column>\n            <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\" width=\"160\"\n                             :resizable=\"false\">\n              <template slot-scope=\"scope\">\n                <el-button @click=\"updateDetails(scope.row)\" v-show=\"scope.row.createBy == currentUser\" type=\"text\" size=\"small\" title=\"修改\"  class='el-icon-edit'></el-button>\n                <el-button @click=\"getDetails(scope.row)\" type=\"text\" size=\"small\" title=\"详情\" class=\"el-icon-view\"></el-button>\n                <el-button type=\"text\" size=\"small\"  v-show=\"scope.row.createBy == currentUser\" @click=\"deleteSensorButton(scope.row.objId)\" title=\"删除\" class=\"el-icon-delete\"></el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n    <!--新增\\修改\\详情弹出-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"60%\"\n      @close=\"handleClose\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"mbzbRules\">\n        <el-row :gutter=\"8\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"设备类型：\" prop=\"sblx\">\n              <el-input\n                v-model=\"form.sblx\"\n                disabled\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"设备类型编码：\" prop=\"sblxbm\">\n              <el-input\n                v-model=\"form.sblxid\"\n                disabled\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"模板名称：\" prop=\"mbmc\">\n              <el-input\n                placeholder=\"请输入试验部位名称\"\n                v-model=\"form.mbmc\"\n                :disabled=\"isDisabled\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否默认：\" prop=\"sfmr\">\n              <el-select\n                v-model=\"form.sfmr\"\n                style=\"width: 100%\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option label=\"是\" value=\"是\"></el-option>\n                <el-option label=\"否\" value=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否停用：\" prop=\"sfty\">\n              <el-select\n                v-model=\"form.sfty\"\n                style=\"width: 100%\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option label=\"是\" value=\"是\"></el-option>\n                <el-option label=\"否\" value=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" v-show=\"isShow\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"save\">确 认</el-button>\n      </div>\n    </el-dialog>\n\n    <!--关联铭牌弹框-->\n    <el-dialog\n      :visible.sync=\"showMpDialog\"\n      v-dialogDrag\n      title=\"已关联铭牌\"\n      v-if=\"showMpDialog\"\n    >\n      <glsymp\n        :main-data=\"rowData\"\n        :tree-data=\"treeForm\"\n        @closeMpDialog=\"closeMpDialog\"\n      ></glsymp>\n    </el-dialog>\n\n    <!--关联试验项目弹出框-->\n    <el-dialog\n      :title=\"glxmDialogTitle\"\n      v-dialogDrag\n      :visible.sync=\"isGlxmDialogShow\"\n      width=\"60%\"\n    >\n      <el-row :gutter=\"3\">\n        <div class=\"mb8 pull-right\">\n          <el-button\n            type=\"primary\"\n            icon=\"el-icon-plus\"\n            size=\"mini\"\n            @click=\"addMbGlXm\"\n          >新增项目</el-button\n          >\n          <el-button\n            type=\"danger\"\n            icon=\"el-icon-delete\"\n            size=\"mini\"\n            @click=\"deleteMbGlXm\"\n          >删除项目</el-button\n          >\n        </div>\n      </el-row>\n      <el-row :gutter=\"20\">\n        <el-table\n          :data=\"mbGlxmDataList\"\n          @selection-change=\"handleGlxmSelectedChange\"\n          @row-click=\"handleMbGlxmRowClick\"\n        >\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n          <el-table-column\n            label=\"序号\"\n            type=\"index\"\n            width=\"50\"\n            align=\"center\"\n          ></el-table-column>\n          <el-table-column\n            prop=\"mpmc\"\n            label=\"项目名称\"\n            align=\"center\"\n          ></el-table-column>\n        </el-table>\n        <pagination\n          v-show=\"glxmQueryParams.total > 0\"\n          :total=\"glxmQueryParams.total\"\n          :page.sync=\"glxmQueryParams.pageNum\"\n          :limit.sync=\"glxmQueryParams.pageSize\"\n          @pagination=\"getSymbGlsyxmDataListByPage\"/>\n      </el-row>\n    </el-dialog>\n    <!--列表新增关联项目弹窗调用-->\n    <el-dialog\n      :title=\"xmLibraryAddDialogTitle\"\n      v-dialogDrag\n      :visible.sync=\"isShowAddGlxmDialog\"\n      width=\"50%\"\n      @close=\"closeAddMjzDialog\"\n    >\n      <el-form label-width=\"120px\">\n        <el-row :gutter=\"3\">\n          <el-col :span=\"10\">\n            <el-form-item label=\"项目名称：\">\n              <el-input v-model=\"xmLibraryQueryForm.mpmc\" />\n            </el-form-item>\n          </el-col>\n          <div class=\"mb8 pull-right\">\n            <el-button\n              type=\"cyan\"\n              size=\"mini\"\n              icon=\"el-icon-search\"\n              @click=\"selectxmLibrary\"\n            >查询</el-button\n            >\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetxmSearch\"\n            >重置</el-button\n            >\n          </div>\n        </el-row>\n      </el-form>\n      <el-table\n        stripe\n        border\n        :data=\"xmLibraryDataList\"\n        @selection-change=\"handleSelectedXmLibraryChange\"\n        :header-cell-style=\"{ 'text-align': 'center' }\"\n        :cell-style=\"{ 'text-align': 'center' }\"\n      >\n        <el-table-column type=\"selection\" width=\"55\" />\n        <el-table-column label=\"序号\" type=\"index\" width=\"50\">\n        </el-table-column>\n        <el-table-column\n          label=\"试验项目\"\n          prop=\"mpmc\"\n          :show-overflow-tooltip=\"true\"\n        />\n      </el-table>\n      <pagination\n        v-show=\"xmLibraryQueryForm.total > 0\"\n        :total=\"xmLibraryQueryForm.total\"\n        :page.sync=\"xmLibraryQueryForm.pageNum\"\n        :limit.sync=\"xmLibraryQueryForm.pageSize\"\n        @pagination=\"getXmLiraryData\"\n      />\n      <div slot=\"footer\">\n        <el-button @click=\"closeAddMjzDialog\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"commitAddMjzForm\">确 认</el-button>\n      </div>\n    </el-dialog>\n    <!--子组件定义模板内容-->\n    <!-- <el-dialog title=\"项目关联部位\" :visible.sync=\"isShowXmGlbwDialog\" v-if=\"isShowXmGlbwDialog\"> -->\n    <!-- <symbwh-dymbnr ref=\"symbwhDymbnrRef\" :mb-data=\"mbRowData\"></symbwh-dymbnr>\n  </el-dialog> -->\n    <!--  打印vue print nb插件-->\n    <div v-show=\"false\">\n      <div id=\"printHtmlId\" style=\"background:white;\">\n        <!--模板-->\n        <div style=\"text-align: center\">\n          <p>{{mbInfo.mbmc}}</p>\n        </div>\n        <p>葫芦娃，葫芦娃</p>\n        <p>一根藤上七朵花 </p>\n        <p>小小树藤是我家 啦啦啦啦 </p>\n        <p>叮当当咚咚当当　浇不大</p>\n        <p> 叮当当咚咚当当 是我家</p>\n        <p> 啦啦啦啦</p>\n        <p>...</p>\n        <div class=\"describle\">\n          <el-form :model=\"mbInfo\" :rules=\"mbzbRules\" ref=\"from\" class=\"demo-ruleForm\">\n            <el-form-item label=\"姓名:\" prop=\"name\">\n              <el-input v-model=\"mbInfo.mbmc\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"描述:\" prop=\"describle\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"4\"\n                :maxlength=\"2000\"\n                placeholder=\"\"\n                v-model=\"mbInfo.mbmc\">\n              </el-input>\n            </el-form-item>\n          </el-form>\n        </div>\n      </div>\n    </div>\n    <!--htmlToPdf插件-->\n    <el-dialog title=\"预览\" :visible.sync=\"isShowDownLoadDialog\" width=\"60%\" class=\"outPut\" v-dialogDrag>\n      <!--      <el-button @click=\"downloadPdf\" >导出</el-button>-->\n      <el-button @click=\"outputPdfFun\" v-print=\"printObj\">导出</el-button>\n      <div style=\"width: 100%;height:60vh;overflow: auto\">\n        <div id=\"printContentId\">\n          <div style=\"text-align: center\">\n            <h2>{{mbInfo.mbmc}}</h2></div>\n          <div >\n            <div style=\"font-size: 20px;float: left;width:100%;border-left: 1px solid #000;border-right: 1px solid #000;border-top: 1px solid #000;\">一、基本信息</div>\n            <el-table\n              :data=\"tableData_jbxx\"\n              border\n              style=\"border: 1px solid #000;\"\n              @cell-click=\"editData\"\n            >\n              <el-table-column\n                property=\"date\"\n                prop=\"column_1\"\n                label=\"变电站\"\n              >\n              </el-table-column>\n              <el-table-column\n                @click=\"elClick\"\n                prop=\"column_5\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_2\"\n                label=\"委托单位\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_6\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_3\"\n                label=\"试验单位\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_7\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_4\"\n                label=\"运行编号\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_8\"\n                label=\"\"\n              >\n              </el-table-column>\n            </el-table>\n          </div>\n          <!--  第二个表格内容   -->\n          <div class=\"printTitle\" style=\"font-size: 20px;padding-left: 0;\">二、设备铭牌</div>\n          <table id=\"h2_table\" style=\"width: 100%;border-collapse: collapse;\" border=\"1\"></table>\n          <!--  第三个表格内容   -->\n          <div class=\"printTitle\" style=\"font-size: 20px;padding-left: 0;\">三、试验数据</div>\n          <table id=\"h3_table\" style=\"width: 100%;border-collapse: collapse;\" border=\"1\"></table>\n<!--          <div >\n            <div style=\"font-size: 20px;float: left;width:100%;border-left: 1px solid #000;border-right: 1px solid #000;border-bottom: 1px solid #000;\">三、试验数据</div>\n            <div v-for=\"item in arr\" style=\"width: 100%\">\n              <div class=\"printTitle\">{{item.title}}</div>\n              <el-table :data=\"item.bwList\" style=\"width:100%;border: 1px solid #000;\" border\n                        :span-method=\"arraySpanMethod\">\n                <template v-for='(val) in item.zxmList'>\n                  <el-table-column\n                    :prop=\"val.column_name\"\n                    width=\"auto\"\n                    :label=\"val.label\"\n                  >\n                  </el-table-column>\n\n                </template>\n\n              </el-table>\n            </div>\n          </div>-->\n        </div>\n      </div>\n      <div slot=\"footer\">\n        <el-button @click=\"closeYlDialog\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n//引入jquery,暂时没用\nimport $ from \"jquery\"\nimport {\n  addMbGlxmBatchToMbxm,\n  getPageDataListTosymb,\n  getSymbGlsyxmDataListByPage,\n  getXmLiraryData,\n  remove,\n  saveOrUpdate,\n  getMbGlMpinfoData,\n  getMbGlXmAndBw,\n  getMwtUdSyMpxqByMbzb,\n  removeSymb,\n} from '@/api/dagangOilfield/bzgl/symbwh'\nimport {getDeviceClassTreeNodeByPid} from '@/api/dagangOilfield/bzgl/sblxwh/sblxwh'\nimport {getGlSyzxmDataListByPage} from '@/api/dagangOilfield/bzgl/syxm'\nimport Glsymp from '@/views/dagangOilfield/bzgl/sybzk/glsymp'\nimport symbwhDymbnr from '@/views/dagangOilfield/bzgl/sybzk/symbwhDymbnr'\nimport { getPageNoDataList } from \"@/api/dagangOilfield/bzgl/sympk/sympk\";\nimport { getMpmcDataById,saveMwtUdSyMbmp,deleteMwtUdSyMbmp } from \"@/api/dagangOilfield/bzgl/symbwh\";\n\n\nimport htmlToPdf from '@/utils/print/htmlToPdf'\nimport syxm from \"./syxm\";\nimport { getChildsValue, getMouldValue } from '@/api/dagangOilfield/bzgl/sybglr'\n\nexport default {\n  name: 'sybwk',\n  components: {Glsymp, symbwhDymbnr},\n  data() {\n    return {\n      currentUser: this.$store.getters.name,\n      //基本信息表格数据\n      tableData_jbxx: [\n        {\n          column_1: \"试验性质\",\n          column_2: \"试验日期\",\n          column_3: \"试验人员\",\n          column_4: \"试验地点\",\n          column_5: \"\",\n          column_6: \"\",\n          column_7: \"\",\n          column_8: \"\",\n        },\n        {\n          column_1: \"报告日期\",\n          column_2: \"编写人\",\n          column_3: \"审核人\",\n          column_4: \"批准人\",\n          column_5: \"\",\n          column_6: \"\",\n          column_7: \"\",\n          column_8: \"\",\n        },\n        {\n          column_1: \"试验天气\",\n          column_2: \"环境温度（℃）\",\n          column_3: \"环境相对湿度（%）\",\n          column_4: \"投运日期\",\n          column_5: \"\",\n          column_6: \"\",\n          column_7: \"\",\n          column_8: \"\",\n        },\n      ],\n      //设备铭牌表格数据\n      tableData_sbmp:[{\n        'column_1':'额定电压',\n        'column_2':'设备型号',\n        'column_3':'',\n        'column_4':'',\n        'column_5':'',\n        'column_6':'',\n      },],\n      //要循环的试验表格数据\n      arr:[{title:\"\",//试验名称\n        zxmList:[],//子项目数据（表头）\n        bwList:[],//部位数据（第一列开头）\n      }],\n      //下载弹出框控制\n      isShowDownLoadDialog: false,\n      printObj: {\n        id: \"previewId\", // 必填，渲染打印的内容使用\n        popTitle: \"&nbsp;\", //\n        previewTitle: \"&nbsp;\",\n        preview: false,\n      },\n\n      mbInfo: {},\n      //打印内容div中id值\n      previewId: \"\",\n      //定义模板内容弹出框传递参数\n      mbRowData: {},\n      //定义模板内容弹出框\n      isShowXmGlbwDialog: false,\n      xmSelectedForm: {\n        //试验模板id\n        symbid: undefined,\n        //试验项目数据集合\n        xmDataRows: [],\n        isMpSyxm: 1,\n        mpid: undefined,\n      },\n      //项目库弹出框标题\n      xmLibraryAddDialogTitle: '项目库',\n      //项目库弹出框控制\n      isShowAddGlxmDialog: false,\n      //项目库查询参数\n      xmLibraryQueryForm: {\n        symbid: undefined,\n        syxmmc: '',\n        pageNum: 1,\n        pageSize: 10,\n        isMpSyxm: 1,\n        total:0,\n      },\n      //项目库数据\n      xmLibraryDataList: [],\n      //项目库项目总数\n      xmLibraryTotal: 0,\n      //表单验证\n      mbzbRules: {\n        mbmc: [\n          {required: true, message: '请输入模板名称', trigger: 'blur'}\n        ]\n      },\n      // 筛选条件\n      filterInfo: {\n        data: {\n          mbmc: ''\n        },\n        fieldList: [\n          {label: '模板名称', type: 'input', value: 'mbmc', multiple: true}\n        ]\n      },\n      //新增按钮控制\n      addDisabled: true,\n      //树结构懒加载参数\n      props: {\n        label: 'name',\n        children: 'zones',\n        isLeaf: 'leaf'\n      },\n      //删除选择列\n      selectRows: [],\n      //弹出框表单\n      form: {},\n      //查询试验部位参数\n      querySyBwParam: {\n        sblxid: undefined,\n        mbmc: undefined,\n        pageNum: 1,\n        pageSize: 10\n      },\n      //点击树节点赋值\n      treeForm: {},\n      //试验部位列表\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          {label: '模板名称', prop: 'mbmc', minWidth: '100'},\n          {label: '是否默认', prop: 'sfmr', minWidth: '100'},\n          {label: '是否停用', prop: 'sfty', minWidth: '100'},\n          // {\n          //   prop: 'operation',\n          //   label: '操作',\n          //   minWidth: '120px',\n          //   style: {display: 'block'},\n          //   //操作列固定再右侧\n          //   fixed: 'right',\n          //   // operation: [\n          //   //   {name: '修改', clickFun: this.updateDetails},\n          //   //   {name: '详情', clickFun: this.getDetails},\n          //   // ]\n          // }\n        ],\n        option: {checkBox: true, serialNumber: true}\n      },\n      //组织树\n      treeOptions: [],\n\n      isShowDetails: false,\n      title: '',\n\n      //删除是否可用\n      multipleSensor: true,\n      //查询参数\n      queryParams: {\n        bm: undefined,\n        pageNum: 1,\n        pageSize: 10\n      },\n      isDisabled: false,\n      isShow: true,\n\n      //关联项目弹出框title\n      glxmDialogTitle: '关联项目',\n      //关联项目弹出框控制展开\n      isGlxmDialogShow: false,\n\n      //关联项目total\n      glxmTotal: 0,\n      //关联项目查询参数\n      glxmQueryParams: {\n        symbid: undefined,\n        pageNum: 1,\n        pageSize: 10,\n        isMpSyxm:1,\n        total:0,\n      },\n      symbid: undefined,\n\n\n      //关联子项目查询参数\n      glzxmQueryParams: {\n        syxmid: undefined,\n        pageSize: 10,\n        pageNum: 1\n      },\n\n      //模板关联项目数据\n      mbGlxmDataList: [],\n      //新增模板与项目关联选中的数据\n      xmmbSelectedRow: [],\n      //新增数据\n      xmmblist: [],\n      //项目关联的子项目数据\n      zxmGlmbDataList: [],\n      //模板关联项目选中框数据\n      selectedRowDataChange: [],\n      //显示铭牌弹框\n      showMpDialog: false,\n      //选中行数据\n      rowData: {},\n\n      //关联名牌便利\n      mpList: [],\n\n      //试验数据\n      sysjDataList: [],\n      //试验表格默认固定的行\n      defaultRow:[\"仪器型号\",\"结论\",\"备注\"],\n      tdWidth: 0, //一个单元格所占宽度\n    }\n  },\n  watch: {},\n  created() {\n    //获取数据列表\n    this.getData()\n  },\n  mounted() {\n  },\n  methods: {\n    editData(row,column){\n      console.log('111',row,column);\n      row[column.property+\"isShow\"] = true;\n    },\n    elClick(){\n      console.log('this',this,this.innerHTML);\n    },\n    //试验数据表格合并方法\n    arraySpanMethod({ row, column, rowIndex, columnIndex }) {\n      if(this.defaultRow.includes(row.SYBW)){\n        if (columnIndex > 0) {\n          return [1,row.totalNum]\n        }\n      }\n    },\n    //设备铭牌表格合并方法\n    sbmpSpanMethod({ row, column, rowIndex, columnIndex }){\n      if (columnIndex > 3) {\n        return [1,2]\n      }\n    },\n    //模板详情按钮\n    handleMbInfo(row) {\n      console.log(\"sdfsd--s\",row);\n      //获取当前模板id加载页面信息\n      // this.getMbGlMpinfoData(row);\n      //打开弹出框\n      this.isShowDownLoadDialog = true;\n      //获取试验数据\n      this.getMbGlXmAndBw(row);\n    },\n    //测试\n    outputPdfFun1(){\n      let that = this;\n      let heights = 0;//最终累计高度\n      const width = 592.28;\n      const height = 1400;\n      that.$nextTick(()=>{\n        debugger\n        let target = document.getElementById('printContentId');\n        let pageHeight = target.scrollWidth / width * height;\n        let childs = document.getElementById('printContentId').childNodes;  //表格的第一级子内容\n        for(let i=0;i<childs.length;i++){\n          that.loopFun(childs[i],heights)\n          // console.log('childs',childs[i]);\n          let multiple = Math.ceil((childs[i].offsetTop + childs[i].offsetHeight)/pageHeight);\n          let childsHeight = childs[i].offsetHeight;\n          heights += childsHeight;\n        }\n        console.log('zi',document.getElementById('printContentId').childNodes)\n        //this.downloadPdf();\n      })\n    },\n    //\n    outputPdfFun(){\n      let that = this;\n      let heights = 0;//最终累计高度\n      const width = 592.28;\n      const height = 1400;\n      that.$nextTick(()=>{\n        // debugger\n        let target = document.getElementById('printContentId');\n        let pageHeight = target.scrollWidth / width * height;\n        let childs = document.getElementById('printContentId').childNodes;  //表格的第一级子内容\n        for(let i=0;i<childs.length;i++){\n          let childsHeight = childs[i].offsetHeight;\n          heights += childsHeight;\n          //判断每一个子元素的高度是否超出一页pdf的高度,没超过就累加\n          if(heights >= height){\n            that.loopFun(childs[i],heights)\n          }\n        }\n        //this.downloadPdf();\n      })\n    },\n    //测试方法\n    loopFun(val,heights){\n      console.log('打印',val,heights);\n      //  let heights = 0;//最终累计高度\n      const width = 592.28;\n      const height = 1488;\n      // let childsHeight = val.offsetHeight;\n      // heights += childsHeight;\n      //console.log('高度',heights)\n      //判断每一个子元素的高度是否超出一页pdf的高度,没超过就累加\n      if(heights >= height){\n        //先减去超出页面的div高度\n        heights = heights - val.offsetHeight;\n        // console.log('hei1',val.childNodes)\n        //在超过的子元素进行循环判断，查找在那一块超过\n        val.childNodes.forEach(item=> {\n          heights += item.offsetHeight;\n          if(heights >= height){\n            //先减去超出页面的div高度\n            heights = heights - item.offsetHeight;\n            console.log('item',item,heights,item.childNodes)\n            item.childNodes.forEach(value=> {\n              heights += value.offsetHeight;\n              if(heights >= height){\n\n                console.log('item11',value)\n                //获取父节点\n                let childParent = value.parentNode;\n                let next = value.nextSibling;\n                //创建空白标签\n                let newNode = document.createElement('span');\n                newNode.style.background = '#fff';\n                newNode.style.height = '100px';\n                newNode.style.width = '1030px';\n                newNode.style.display = 'block';\n                newNode.style.borderTop = '1px solid #000';\n                // console.log('4444',val3,next,newNode,childParent);\n                //console.log('xiajiedian',val3.nextSibling)\n                if(next){\n                  childParent.insertBefore(newNode,next)\n                }else{\n                  childParent.appentChild(newNode);\n                }\n                this.downloadPdf()\n              }\n            })\n\n            //this.loopFun(item,heights)\n          }\n          // this.loopFun(item,heights)\n        })\n      }\n    },\n    //导出pdf操作\n    downloadPdf() {\n      htmlToPdf.downloadPDF(document.querySelector('#printContentId'), this.mbInfo.mbmc)\n    },\n    //获取当前模板id加载页面信息\n    getMbGlMpinfoData(param) {\n      getMbGlMpinfoData(param).then(res => {\n        this.mpList = res.data;\n        //调用渲染铭牌页面开始\n        // this.applyMpHtml(this.mpList, param);\n        //打开弹出框\n        this.isShowDownLoadDialog = true;\n      })\n\n    },\n    //获取试验数据\n    getMbGlXmAndBw(rowData) {\n      //每次获取数据前先清空，再添加，否则多次进入页面时会获取重复数据\n      this.sysjDataList = [];\n      //获取设备铭牌数据\n      //在填写报告时，请求后台处理数据\n      const that = this;\n      getMouldValue({symbid:rowData.objId}).then(res=>{\n        res = res.data;\n        let key = 'symbid';\n        rowData[key] = rowData.objId;\n        getMwtUdSyMpxqByMbzb({symbid:rowData.objId}).then(result=>{\n          let sbmp_data = result.data.sbmp;\n          let sysj_data = result.data.syxm;\n          let arr = [];\n          Object.keys(sbmp_data).sort().forEach(function(key){\n            arr.push({[key]: sbmp_data[key]})\n          })\n          let arr1 = [];\n          Object.keys(sysj_data).sort().forEach(function(key){\n            arr1.push({[key]: sysj_data[key]})\n          })\n          that.handleSbmp(res[Object.keys(res)[0]],arr,'','h2_table');\n          that.handleSbmp(res[Object.keys(res)[1]],arr1,'','h3_table');\n        })\n      })\n    },\n    handleSbmp(dataNum,dataArr,str,tableBox){\n      for(let k = 0;k<dataNum.length;k++){\n        var hs = dataNum[k].aHs;\n        var ls = dataNum[k].aLs;\n        this.tdWidth = 100 / Number(ls);\n        let data = dataArr[k];\n        for(var item in data) {\n          if(dataArr.length > 1){   //判断若该模块只有一个子内容则不显示title\n            str += \"<tr style='text-align:left;'><th colspan=\"+ ls +\">\"+ item +\"</th></tr>\";\n          }\n          for (let i = 0; i < hs; i++) {//有几行就插入几行\n            let temp = \"<tr>\"\n            for (let j = 0; j < data[item].length; j++) {   //循环数据看每行有几列\n              if (i == data[item][j].rowindex) {\n                var nrbs = data[item][j].nrbs;\n                var sjlx = data[item][j].sjlx;  //数据类型\n                var objId = data[item][j].objId;\n                var nr = '';\n                if(nrbs == null){\n                  nrbs = \"\";\n                }\n                nr = nrbs;\n                if (data[item][j].colspan != '1') {    //判断colspan不为1的话为可编辑的\n                  temp += \"<td tabindex='-1' colspan='\"\n                    + data[item][j].colspan\n                    + \"' rowspan='\"\n                    + data[item][j].rowspan\n                    + \"' style='width: \" +\n                    this.tdWidth * data[item][j].colspan +\n                    \"px'>\"\n                    + nr + \"</td>\";\n                } else {\n                  temp += \"<td tabindex='-1' colspan='\"\n                    + data[item][j].colspan\n                    + \"' rowspan='\"\n                    + data[item][j].rowspan\n                    + \"' style='min-width:100px;width: \" +\n                    this.tdWidth * data[item][j].colspan +\n                    \"%'>\"\n                    + nr + \"</td>\";\n                }\n              }\n            }\n            temp += \"</tr>\"\n            str += temp;\n          }\n        }\n      }\n      //渲染页面\n      this.$nextTick(() => {\n        var tableBox1 = document.getElementById(tableBox);\n        tableBox1.innerHTML = '';\n        tableBox1.innerHTML = str;\n      })\n    },\n    //解析后台试验数据\n    analysisSyData(syxmmc, zxmAndBwData) {\n      let sysjData = {}\n      sysjData.syxmmc = syxmmc;\n      for (let key in zxmAndBwData[0]) {\n        sysjData[key] = zxmAndBwData[0][key]\n      }\n      this.sysjDataList.push(sysjData);\n    },\n    //渲染实验数据到页面\n    applySysjDataToHtml() {\n      this.arr = [];\n      // $('#sysjTableId').html(\"\");\n      //进行数据处理重组\n      let data = this.sysjDataList;\n      if (data.length > 0) {\n        for (let i = 0; i < data.length; i++) {\n          let dataChild = data[i];\n          let title = dataChild.syxmmc;//试验项目名称\n          let bwList = dataChild.bwList; //部位list\n          let zxmList = dataChild.zxmList; //子项目list\n          let hx = [\n            {\n              \"label\":title, //第一个表头为试验项目名称\n              \"column_name\":\"SYBW\", //第一列对应的字段名（试验部位）\n            },\n          ];\n          zxmList.forEach(zxm=>{\n            hx.push({\n              \"label\":zxm.syzxmmc, //每列的表头\n              \"column_name\":\"\", //每列对应的数值暂时设置为空白\n            })\n          })\n          let sx = [];\n          bwList.forEach(bw=>{\n            sx.push({\n              \"SYBW\":bw.SYBW,\n              \"totalNum\":zxmList.length//记录总列数，用于固定行的合并事件\n            })\n          })\n          //后四行固定\n          sx.push(\n            {\n              \"SYBW\":\"结果\",\n              \"totalNum\":zxmList.length//记录总列数，用于固定行的合并事件\n            },\n            {\n              \"SYBW\":\"仪器型号\",\n              \"totalNum\":zxmList.length//记录总列数，用于固定行的合并事件\n            },\n            {\n              \"SYBW\":\"结论\",\n              \"totalNum\":zxmList.length//记录总列数，用于固定行的合并事件\n            },\n            {\n              \"SYBW\":\"备注\",\n              \"totalNum\":zxmList.length//记录总列数，用于固定行的合并事件\n            }\n          )\n          this.arr.push({\n            title:title,\n            zxmList:hx,\n            bwList:sx,\n          });\n        }\n      }\n      //拼接的铭牌表格\n      /*     let str = \"\";\n           if (this.sysjDataList.length > 0) {\n             for (let i = 0; i < this.sysjDataList.length; i++) {\n               //拼接项目序号\n               let xmIndex = i + 1;\n               str += \"<tr><td colspan='5' style='text-align: left;font-weight: bold;font-size: 15px'>\" + xmIndex + \"、\" + this.sysjDataList[i].syxmmc + \"</td></tr>\";\n               // this.sysjDataList[i].bwList;\n               // this.sysjDataList[i].zxmList;\n               // str += \"<tr><td>\"+this.sysjDataList[i].syxmmc+\"</td><td v-for=item in this.sysjDataList[i].bwList></td></tr>\"\n\n             }\n             this.$nextTick(() => {\n               $('#sysjTableId').append(str)\n             })\n           }*/\n    },\n    //渲染铭牌页面开始mpList:反回的铭牌列表  row：模板行对象\n    applyMpHtml(mpList, row) {\n      //每次打开需要重新渲染一次,先将置空\n      $('#sbmpTbodyId').html(\"\");\n      //清空重新赋值\n      this.mbInfo = {}\n      this.mbInfo.mbmc = row.mbmc;\n      //拼接的铭牌表格\n      let str = \"\";\n      //先判断是否分相铭牌\n      if (mpList.length > 0) {\n        if (mpList[0].SFFX == '1') { //当前铭牌为分相铭牌时\n          //写死第一行\n          str += \"<tr><td style='padding: 10px;font-size: 15px;'>相别</td>\" +\n            \"<td style='padding: 10px;font-size: 15px;'>A</td>\" +\n            \"<td style='padding: 10px;font-size: 15px;'>B</td>\" +\n            \"<td style='padding: 10px;font-size: 15px;'>C</td>\" +\n            \"</tr>\";\n          //开始遍历展示\n          for (let a = 0; a < mpList.length; a++) {\n            str += \"<tr>\"\n            str += \"<td style='padding: 10px;font-size: 15px;'>\";\n            str += mpList[a].title + \"</td>\";\n            str += \"<td></td>>\"\n            str += \"<td></td>>\"\n            str += \"<td></td>>\"\n            str += \"</tr>\"\n          }\n        } else {  //铭牌不分相\n          //当前铭牌不属于分相铭牌\n          //每列展示单元格数量\n          let col = 3;\n          //展示行数\n          var lines = Math.ceil(mpList.length / col);\n          //遍历展示行数\n          for (var i = 0; i < lines; i++) {\n            str += \"<tr>\";\n            //遍历列\n            for (var j = 0; j < col; j++) {\n              if (i * col + j < mpList.length) {\n                str += \"<td style='padding: 10px;font-size: 15px;'>\";\n                //铭牌标题赋值\n                str += mpList[i * col + j].title + \"</td>\";\n                //铭牌值赋值\n                str += mpList[i * col + j].sfmb == 1 ? \"<td> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>\" : \"<td>\" + mpList[i * col + j].column_name + \"</td>\"\n              }\n            }\n            str += \"</tr>\";\n          }\n        }\n      }\n      //渲染铭牌页面\n      this.$nextTick(() => {\n        $('#sbmpTbodyId').append(str)\n      })\n      //打开弹出框\n      this.isShowDownLoadDialog = true;\n    },\n    //关闭预览弹出框\n    closeYlDialog() {\n      //清空表单\n      this.mbInfo = {};\n      //赋值完关闭弹窗\n      this.isShowDownLoadDialog = false;\n    }\n    ,\n    //定义模板内容\n    handleClickMbnr(row) {\n      //打开组件弹出框\n      this.isShowXmGlbwDialog = true;\n      //给子组件传递数据\n      this.mbRowData = row;\n    },\n    //获取项目库项目数据\n    getXmLiraryData() {\n      getPageNoDataList(this.xmLibraryQueryForm).then(res => {\n        this.xmLibraryDataList = res.data.records\n        this.xmLibraryQueryForm.total=res.data.total;\n        this.xmLibraryTotal = res.data.total\n      });\n    },\n    //项目弹出框新增按钮\n    addMbGlXm() {\n      this.getXmLiraryData()\n      this.isShowAddGlxmDialog = true\n    }\n    ,\n    //项目库弹出框取消按钮\n    closeAddMjzDialog() {\n      this.isShowAddGlxmDialog = false;\n      this.xmLibraryQueryForm.mpmc = undefined;\n    }\n    ,\n    //项目库弹窗确认按钮\n    commitAddMjzForm() {\n      this.xmmblist=[];\n      if (this.xmSelectedForm.xmDataRows.length < 1) {\n        this.$message.info('未关联项目！！！已取消')\n        //如果未选中数据,则直接关闭弹窗\n        this.isShowAddGlxmDialog = false\n      } else {\n        for (let i = 0; i < this.xmmbSelectedRow.length; i++) {\n          this.xmSelectedForm = {};\n          this.xmSelectedForm.mpid = this.xmmbSelectedRow[i].objId;\n          this.xmSelectedForm.symbid = this.symbid;\n          this.xmSelectedForm.isMpSyxm = 1;\n          this.xmmblist.push(this.xmSelectedForm);\n        }\n        //若选择数据后\n        saveMwtUdSyMbmp(this.xmmblist).then(res => {\n          if (res.code === '0000') {\n            this.$message.success('关联成功')\n          } else {\n            this.$message.error('关联失败！！')\n          }\n          //关闭弹窗\n          this.isShowAddGlxmDialog = false\n          //调用获取关联子项目列表\n          this.getSymbGlsyxmDataListByPage()\n        })\n      }\n    }\n    ,\n    //项目库行选中事件\n    handleSelectedXmLibraryChange(rows) {\n      this.xmSelectedForm.xmDataRows = rows\n      this.xmmbSelectedRow=[];\n      this.xmmbSelectedRow=rows;\n      console.log(\"sdfys--\",this.xmSelectedForm.xmDataRows);\n    }\n    ,\n    //项目库查询按钮\n    selectxmLibrary() {\n      this.getXmLiraryData()\n    }\n    ,\n    //项目库重置按钮\n    resetxmSearch() {\n      this.xmLibraryQueryForm.syxmmc = ''\n      this.getXmLiraryData()\n    }\n    ,\n    //获取关联子列表方法\n    getZxmDataList() {\n      getGlSyzxmDataListByPage(this.glzxmQueryParams).then(res => {\n        this.glzxmTotal = res.data.total\n        this.zxmGlmbDataList = res.data.records\n      })\n    }\n    ,\n\n    //关联项目\n    handleClickGlxm(row) {\n      //清空原来子项目数据\n      this.mbGlxmDataList=[];\n      this.zxmGlmbDataList = [];\n      this.xmmbSelectedRow=[];\n      //打开关联项目弹出框\n      this.isGlxmDialogShow = true\n      //给参数赋值\n      this.glxmQueryParams.symbid = row.objId;\n      this.symbid = row.objId;\n      //查询项目库数据时参数\n      this.xmLibraryQueryForm.symbid = row.objId\n      //给试验项目库添加时使用\n      this.xmSelectedForm.symbid = row.objId\n      //获取模板关联项目数据\n      this.getSymbGlsyxmDataListByPage()\n    }\n    ,\n    //获取关联项目弹出框数据\n    getSymbGlsyxmDataListByPage() {\n      getMpmcDataById(this.glxmQueryParams).then(res => {\n        this.mbGlxmDataList = res.data.records\n        this.glxmTotal = res.data.total\n        this.glxmQueryParams.total=res.data.total\n      });\n    },\n    //试验项目复选框点击时间点击操作\n    handleGlxmSelectedChange(rows) {\n      this.selectedRowDataChange = rows\n    }\n    ,\n    //删除模板关联项目\n    deleteMbGlXm() {\n      if (this.selectedRowDataChange.length < 1) {\n        this.$message.warning('请选择正确的数据！！！')\n        return;\n      }\n      let ids = this.selectedRowDataChange.map(item => {\n        return item.mbmpId;\n      });\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        deleteMwtUdSyMbmp(ids).then(({code}) => {\n          if (code === '0000') {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            });\n            this.getSymbGlsyxmDataListByPage()\n          } else {\n            this.$message({\n              type: 'error',\n              message: '删除失败!'\n            })\n            this.getSymbGlsyxmDataListByPage()\n          }\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        })\n      })\n    },\n    //试验项目点击行数据时的单机操作\n    handleMbGlxmRowClick(row) {\n      this.glzxmQueryParams.syxmid = row.syxmid\n      this.getZxmDataList()\n    },\n    //懒加载函数\n    loadNode(node, resolve) {\n      let TreeparamMap = {\n        pid: '',\n        spbLogo: ['输电设备', '变电设备','配电设备']\n      }\n      if (node.level === 0) {\n        TreeparamMap.pid = 'sb'\n        return this.getTreeNode(TreeparamMap, resolve)\n      }\n      setTimeout(() => {\n        TreeparamMap.pid = node.data.code\n        this.getTreeNode(TreeparamMap, resolve)\n      }, 500)\n    },\n    //获取树节点数据\n    getTreeNode(paramMap, resolve) {\n      getDeviceClassTreeNodeByPid(paramMap).then(res => {\n        let treeNodes = []\n        res.data.forEach(item => {\n          let node = {\n            name: item.name,\n            level: item.level,\n            id: item.id,\n            pid: item.pid,\n            leaf: false,\n            code: item.code\n          }\n          treeNodes.push(node)\n        })\n        resolve(treeNodes)\n      })\n    },\n    //添加后确认保存按钮\n    save() {\n      this.$refs['form'].validate((valid) => {\n        if (valid) {\n          saveOrUpdate(this.form).then(res => {\n            if (res.code === '0000') {\n              this.$message.success(res.msg)\n              this.tableAndPageInfo.pager.pageResize = 'Y'\n              this.getData()\n              this.isShowDetails = false\n            } else {\n              this.$message.error(res.msg)\n            }\n          });\n\n        }\n      })\n    },\n    //树节点点击事件\n    handleNodeClick(data) {\n      console.log(data)\n      if (data.level != '0') {\n        //新增按钮可点击\n        this.addDisabled = false\n        this.treeForm = data\n        this.querySyBwParam.sblxid = data.code\n        this.getData()\n      } else {\n        this.addDisabled = true\n      }\n    },\n    //添加按钮\n    addSensorButton() {\n      this.form = {}\n      this.form.sblx = this.treeForm.name;\n      this.form.sblxid = this.treeForm.code;\n      this.isShowDetails = true;\n      this.title = '新增';\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.querySyBwParam = {...this.querySyBwParam, ...params}\n        const param = this.querySyBwParam\n        const {data, code} = await getPageDataListTosymb(param)\n        if (code === '0000') {\n          this.tableAndPageInfo.tableData = data.records\n          this.tableAndPageInfo.pager.total = data.total\n        }\n      } catch (e) {\n        console.log(e)\n      }\n    }\n    ,\n    /**\n     * 表格多选框\n     */\n    handleSelectionChange(rows) {\n      this.selectRows = rows\n    }\n    ,\n\n    close() {\n      this.isShowDetails = false\n    }\n    ,\n    //修改模板主表内容\n    updateDetails(row) {\n      this.title = '修改';\n      this.isShowDetails = true;\n      this.form = row;\n      this.isDisabled = false;\n      this.isShow = true;\n    },\n\n    createTemplate(row) {\n      console.log(row)\n    }\n    ,\n    //查看模板主表详情按钮\n    getDetails(row) {\n      this.title = '详情'\n      this.isShowDetails = true;\n      this.form = row;\n      this.isDisabled = true;\n      this.isShow = false;\n    },\n\n    //删除按钮\n    // deleteSensorButton(objId) {\n    //   debugger;\n    //   console.log(\"123+objId\",objId);\n    //   this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n    //     confirmButtonText: \"确定\",\n    //     cancelButtonText: \"取消\",\n    //     type: \"warning\",\n    //   })\n    //    .then(() => {\n    //     removeSymb(JSON.stringify(objId)).then(({code}) => {\n    //       if (code === '0000') {\n    //         this.$message({\n    //           type: 'success',\n    //           message: '删除成功!'\n    //         })\n    //         this.tableAndPageInfo.pager.pageResize = 'Y'\n    //         this.getData()\n    //       } else {\n    //         this.$message({\n    //           type: 'error',\n    //           message: '删除失败!'\n    //         })\n    //         this.tableAndPageInfo.pager.pageResize = 'Y'\n    //         this.getData()\n    //       }\n    //     })\n    //   }).catch(() => {\n    //     this.$message({\n    //       type: 'info',\n    //       message: '已取消删除'\n    //     })\n    //   })\n\n    // },\n\n     deleteSensorButton(id) {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      })\n        .then(() => {\n          removeSymb(JSON.stringify(id)).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\",\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\",\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\",\n          });\n        });\n      this.tableAndPageInfo.pager.pageResize = \"Y\";\n      this.getData();\n    },\n\n    //导出按钮\n    handleExport() {\n\n    }\n    ,\n\n    //关联铭牌点击事件\n    handleClickGlMp(row) {\n      this.showMpDialog = true\n      this.rowData = row\n    }\n    ,\n    //关闭试验铭牌弹窗\n    closeMpDialog() {\n      this.showMpDialog = false\n    }\n    ,\n\n    filterReset() {\n      this.querySyBwParam = {\n        sblxid: undefined,\n        mbmc: undefined,\n        pageNum: 1,\n        pageSize: 10\n      }\n    },\n    //清空表单数据\n    handleClose(){\n      this.form={};\n      this.$nextTick(() => {\n        this.form = this.$options.data().form;\n        this.resetForm(\"form\");\n      });\n    },\n  }\n}\n</script>\n\n<style lang='scss' scoped>\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n/deep/.el-table td div{\n  white-space: pre-wrap;\n}\n//设备铭牌\n#h2_table,#h3_table{\n  text-align: center;\n}\n/deep/ #h2_table tr,/deep/ #h3_table tr{\n  height: 35px;\n}\n/deep/ #h2_table input,/deep/ #h3_table input{\n  display: inline-block;\n  height: 35px;\n}\n/*导出pdf格式设置开始*/\n#printContentId {\n  background-color: #fff;\n  width: 100%;\n  /* height: 400px; */\n  margin: auto;\n  padding: 14px 30px;\n  box-sizing: border-box;\n  //试验数据样式\n  .printTitle{\n    text-align: left;\n    line-height: 40px;\n    border-left: 1px solid #000;\n    border-right: 1px solid #000;\n    //border-bottom: 1px solid #000;\n    padding-left: 10px;\n  }\n}\n//修改table表头颜色\n/deep/  #printContentId .el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th{\n  background: #fff;\n  border-color: #000;\n  font-weight: inherit;\n}\n/deep/  #printContentId .el-table--enable-row-transition .el-table__body td{\n  border-color: #000;\n}\n\n.table_style td, th {\n  padding: 10px;\n  font-size: 15px;\n}\n\n.table_style {\n  border-collapse: collapse;\n  width: 100%;\n  text-align: center;\n}\n\n/* 设置滚动条的样式 */\n::-webkit-scrollbar {\n  width: 12px;\n}\n\n/* 滚动槽 */\n::-webkit-scrollbar-track {\n  //-webkit-box-shadow:inset006pxrgba(0,0,0,0.3);\n  border-radius: 10px;\n}\n\n/* 滚动条滑块 */\n::-webkit-scrollbar-thumb {\n  border-radius: 10px;\n  background: rgba(0, 0, 0, 0.1);\n  //-webkit-box-shadow:gba(0,0,0,0.5);\n}\n\n::-webkit-scrollbar-thumb:window-inactive {\n  background: rgba(0, 0, 0, 0.1);\n}\n\n[data-v-67a974b1]::-webkit-scrollbar {\n  width: 8px;\n}\n\n.item {\n  width: 225px;\n  float: left;\n}\n\n/*导出pdf格式设置结束*/\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  max-height: 79vh;\n  overflow: auto;\n}\n</style>\n<style lang=\"scss\">\n/*新加修改导出pdf页面样式*/\n.outPut .el-dialog__body{\n  padding:25px !important;\n  /* background: #e79b9b;*/\n  .el-button{\n    // margin-bottom: 15px;\n  }\n}\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk"}]}