{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\bddzcz\\dzczml.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\bddzcz\\dzczml.vue", "mtime": 1748606190948}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON>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"}, {"version": 3, "sources": ["dzczml.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAm2CA;;AACA;;AACA;;AAUA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,QADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,oBAAA,EAAA,6BAAA;AAAA,IAAA,SAAA,EAAA,kBAAA;AAAA,IAAA,QAAA,EAAA,iBAAA;AAAA,IAAA,QAAA,EAAA;AAAA,GAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA,MAAA,YAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,YADA;AAEA;AACA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,oBAAA,EAAA,OADA;AAEA,QAAA,WAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,OAHA;AAIA,QAAA,SAAA,EAAA,EAJA;AAKA,QAAA,WAAA,EAAA,IALA;AAMA,QAAA,QAAA,EAAA,EANA;AAOA,QAAA,WAAA,EAAA;AAPA,OAJA;AAaA,MAAA,MAAA,EAAA,KAbA;AAcA;AACA,MAAA,cAAA,EAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAfA;AAgBA,MAAA,QAAA,EAAA,CACA;AACA;AACA;AAHA,OAhBA;AAqBA,MAAA,KAAA,EAAA;AACA,QAAA,KAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CADA;AAEA,QAAA,GAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAFA;AAGA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AAHA,OArBA;AA0BA,MAAA,MAAA,EAAA;AACA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CADA;AAEA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAFA;AAGA,QAAA,GAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAHA;AAIA,QAAA,GAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAJA;AAKA,QAAA,GAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CALA;AAMA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CANA;AAOA,QAAA,OAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAPA;AAQA,QAAA,OAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CARA;AASA,QAAA,KAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CATA;AAUA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AAVA,OA1BA;AAsCA,MAAA,WAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,IAtCA;AAuCA,MAAA,EAAA,EAAA,KAvCA;AAwCA,MAAA,WAAA,EAAA,EAxCA;AAyCA,MAAA,OAAA,EAAA,EAzCA;AA0CA;AACA,MAAA,cAAA,EAAA,EA3CA;AA4CA;AACA,MAAA,gBAAA,EAAA,KA7CA;AA8CA;AACA,MAAA,aAAA,EAAA;AACA,QAAA,UAAA,EAAA,EADA,CACA;;AADA,OA/CA;AAkDA;AACA,MAAA,MAAA,EAAA,EAnDA;AAoDA;AACA,MAAA,OAAA,EAAA,EArDA;AAsDA,MAAA,QAAA,EAAA,KAtDA;AAuDA,MAAA,OAAA,EAAA,EAvDA;AAwDA;AACA,MAAA,iBAAA,EAAA,EAzDA;AA0DA,MAAA,SAAA,EAAA,KA1DA;AA2DA,MAAA,SAAA,EAAA,KA3DA;AA4DA,MAAA,OAAA,EAAA,KA5DA;AA6DA,MAAA,aAAA,EAAA,KA7DA;AA8DA,MAAA,gBAAA,EAAA,KA9DA;AA+DA,MAAA,gBAAA,EAAA,KA/DA;AAgEA;AACA,MAAA,gBAAA,EAAA,KAjEA;AAkEA,MAAA,gBAAA,EAAA,KAlEA;AAmEA;AACA,MAAA,GAAA,EAAA,EApEA;AAqEA,MAAA,UAAA,EAAA,EArEA;AAsEA;AACA,MAAA,MAAA,EAAA;AACA;AACA,QAAA,EAAA,EAAA;AAFA,OAvEA;AA2EA;AACA,MAAA,WAAA,EAAA,EA5EA;AA6EA;AACA,MAAA,cAAA,EAAA;AACA,QAAA,MAAA,EAAA,GADA;AAEA,QAAA,EAAA,EAAA;AAFA,OA9EA;AAkFA;AACA,MAAA,aAAA,EAAA;AACA,QAAA,GAAA,EAAA,IADA;AACA;AACA,QAAA,QAAA,EAAA;AAFA,OAnFA;AAuFA;AACA,MAAA,kBAAA,EAAA;AACA,QAAA,GAAA,EAAA,IADA;AACA;AACA,QAAA,QAAA,EAAA;AAFA,OAxFA;AA4FA;AACA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,CARA;AAaA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,YAAA,EAAA;AAAA,SAbA;AAcA,QAAA,GAAA,EAAA,IAdA,CAcA;;AAdA,OA7FA;AA6GA;AACA,MAAA,kBAAA,EAAA;AACA,QAAA,GAAA,EAAA,IADA;AACA;AACA,QAAA,QAAA,EAAA;AAFA,OA9GA;AAkHA;AACA,MAAA,gBAAA,EAAA;AACA,QAAA,GAAA,EAAA,IADA;AACA;AACA,QAAA,QAAA,EAAA;AAFA,OAnHA;AAuHA;AACA,MAAA,IAAA,EAAA;AACA,QAAA,EAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,GAAA,EAAA,EAJA;AAKA,QAAA,IAAA,EAAA,EALA;AAMA,QAAA,GAAA,EAAA,EANA;AAOA,QAAA,IAAA,EAAA,EAPA;AAQA,QAAA,GAAA,EAAA,EARA;AASA,QAAA,IAAA,EAAA,EATA;AAUA,QAAA,MAAA,EAAA;AAVA,OAxHA;AAoIA;AACA,MAAA,OAAA,EAAA;AACA,QAAA,EAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA,EAJA;AAKA,QAAA,IAAA,EAAA,EALA;AAMA,QAAA,GAAA,EAAA,EANA;AAOA,QAAA,GAAA,EAAA,EAPA;AAQA,QAAA,GAAA,EAAA,EARA;AASA,QAAA,GAAA,EAAA,EATA;AAUA,QAAA,EAAA,EAAA,CAVA;AAUA;AACA,QAAA,QAAA,EAAA,EAXA;AAYA,QAAA,MAAA,EAAA;AAZA,OArIA;AAmJA;AACA,MAAA,OAAA,EAAA;AACA,QAAA,EAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA,EAJA;AAKA,QAAA,IAAA,EAAA,EALA;AAMA,QAAA,GAAA,EAAA,EANA;AAOA,QAAA,GAAA,EAAA,EAPA;AAQA,QAAA,GAAA,EAAA,EARA;AASA,QAAA,GAAA,EAAA,EATA;AAUA,QAAA,EAAA,EAAA,CAVA;AAUA;AACA,QAAA,QAAA,EAAA,EAXA;AAYA,QAAA,MAAA,EAAA;AAZA,OApJA;AAmKA;AACA,MAAA,mBAAA,EAAA,EApKA;AAsKA;AACA,MAAA,aAAA,EAAA,KAvKA;AAwKA;AACA,MAAA,UAAA,EAAA,KAzKA;AA0KA;AACA,MAAA,KAAA,EAAA,EA3KA;AA4KA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,EAAA,EAAA,EADA;AAEA,UAAA,KAAA,EAAA,EAFA;AAGA,UAAA,OAAA,EAAA,EAHA;AAIA,UAAA,GAAA,EAAA,EAJA;AAKA,UAAA,OAAA,EAAA,EALA;AAMA,UAAA,GAAA,EAAA,EANA;AAOA,UAAA,IAAA,EAAA,EAPA;AAQA,UAAA,GAAA,EAAA,EARA;AASA,UAAA,OAAA,EAAA;AATA,SADA;AAWA;AACA,QAAA,SAAA,EAAA,CACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAA,KAAA,EAAA,KADA;AAEA,UAAA,KAAA,EAAA,KAFA;AAGA,UAAA,IAAA,EAAA,QAHA;AAIA,UAAA,OAAA,EAAA,EAJA;AAKA,UAAA,SAAA,EAAA;AALA,SAVA,EAiBA;AACA,UAAA,KAAA,EAAA,KADA;AAEA,UAAA,KAAA,EAAA,OAFA;AAGA,UAAA,IAAA,EAAA,QAHA;AAIA,UAAA,OAAA,EAAA,EAJA;AAKA,UAAA,SAAA,EAAA,IALA;AAMA,UAAA,UAAA,EAAA;AANA,SAjBA,EAyBA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,SAFA;AAGA,UAAA,IAAA,EAAA,MAHA;AAIA,UAAA,QAAA,EAAA,WAJA;AAKA,UAAA,MAAA,EAAA;AALA,SAzBA,EAgCA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SAhCA,EAiCA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,SAFA;AAGA,UAAA,IAAA,EAAA,MAHA;AAIA,UAAA,QAAA,EAAA,WAJA;AAKA,UAAA,MAAA,EAAA;AALA,SAjCA,EAwCA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SAxCA,EAyCA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SAzCA,EA0CA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SA1CA,EA2CA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,SAFA;AAGA,UAAA,IAAA,EAAA,MAHA;AAIA,UAAA,QAAA,EAAA,WAJA;AAKA,UAAA,MAAA,EAAA;AALA,SA3CA;AAZA,OA5KA;AA4OA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA;AACA;AACA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SARA,EASA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SATA,EAUA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAVA,EAWA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAXA,CARA;AAqBA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AArBA,OA5OA;AAmQA,MAAA,YAAA,EAAA,EAnQA;AAoQA,MAAA,OAAA,EAAA,EApQA;AAqQA,MAAA,UAAA,EAAA,EArQA;AAsQA,MAAA,OAAA,EAAA;AAtQA,KAAA;AAwQA,GA5QA;AA6QA,EAAA,OA7QA,qBA6QA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA,cAAA,KAAA,CAAA,MAAA,CAAA,KAAA,GAAA,qBAAA,CAFA,CAGA;;AACA,cAAA,KAAA,CAAA,aAAA;;AACA,cAAA,KAAA,CAAA,mBAAA;;AALA;AAAA,qBAMA,KAAA,CAAA,aAAA,CAAA,EAAA,EAAA,EAAA,CANA;;AAAA;AAMA,cAAA,KAAA,CAAA,YANA;AAAA;AAAA,qBAOA,KAAA,CAAA,aAAA,CAAA,EAAA,EAAA,EAAA,CAPA;;AAAA;AAOA,cAAA,KAAA,CAAA,UAPA;AAAA;AAAA,qBAQA,KAAA,CAAA,aAAA,CAAA,EAAA,EAAA,EAAA,CARA;;AAAA;AAQA,cAAA,KAAA,CAAA,OARA;AAAA;AAAA,qBASA,KAAA,CAAA,OAAA,EATA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,GAvRA;AAwRA,EAAA,OAAA,EAAA;AACA;;;AAGA,IAAA,aAJA,2BAIA;AAAA;;AACA,+BAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAAA,QAAA,EAAA;AACA,SAFA;AAGA,QAAA,MAAA,CAAA,mBAAA,GAAA,GAAA,CAAA,IAAA;;AACA,QAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,IAAA,KAAA,EAAA;AACA,mBAAA,IAAA,CAAA,OAAA,GAAA,MAAA,CAAA,mBAAA;AACA;AACA,SAJA;AAKA,OAVA;AAWA,KAhBA;AAiBA;AACA,IAAA,SAlBA,uBAkBA;AACA,iCAAA,KAAA,MAAA,EAAA,QAAA;AACA,KApBA;AAqBA;AACA,IAAA,UAtBA,sBAsBA,IAtBA,EAsBA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,GADA,GACA;AACA,kBAAA,KAAA,EAAA,IAAA,CAAA,WADA;AAEA,kBAAA,OAAA,EAAA,CAFA;AAGA,kBAAA,MAAA,EAAA,IAAA,CAAA,WAAA,KAAA,UAAA,GAAA,CAAA,GAAA;AAHA,iBADA;AAMA,gBAAA,GAAA,CAAA,MAAA,GAAA,GAAA;AACA,gBAAA,GAAA,CAAA,KAAA,GAAA,IAAA,CAAA,QAAA;AAPA;AAAA,uBAQA,yBAAA,GAAA,CARA;;AAAA;AAAA;AAQA,gBAAA,IARA,qBAQA,IARA;;AAAA,sBASA,IAAA,KAAA,MATA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAUA,MAAA,CAAA,OAAA,EAVA;;AAAA;AAAA;AAAA;;AAAA;AAYA,gBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AAZA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA,KApCA;AAqCA;AACA,IAAA,aAtCA,2BAsCA;AACA,UAAA,KAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,EAAA;AACA,aAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,CAAA,WAAA;AACA;;AACA,WAAA,MAAA,GAAA,KAAA;AACA,KA3CA;AA4CA,IAAA,mBA5CA,iCA4CA;AAAA;;AACA,sCAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KAhDA;AAiDA;AACA,IAAA,eAlDA,2BAkDA,QAlDA,EAkDA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA,gBAAA,MAAA,CAAA,iBAAA,GAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,EAAA,OAAA,EAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,EAAA,KAAA,EAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,OAAA,GAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,EAAA,KAAA,EAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAPA,CAQA;;;AARA;AAAA,uBASA,MAAA,CAAA,SAAA,CAAA,QAAA,CATA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,KA5DA;AA6DA,IAAA,SA7DA,qBA6DA,QA7DA,EA6DA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA,gBAAA,MAAA,CAAA,gBAAA,CAAA,QAAA;;AAFA;AAAA,uBAGA,MAAA,CAAA,aAAA,CAAA,EAAA,EAAA,QAAA,CAHA;;AAAA;AAGA,gBAAA,MAAA,CAAA,OAHA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,KAjEA;AAkEA,IAAA,cAlEA,4BAkEA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,WAAA,EAAA,GAAA,IAAA;AACA,KArEA;AAsEA,IAAA,UAtEA,wBAsEA;AAAA;;AACA,WAAA,aAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA,IAAA,CAAA,UAAA,CACA,MAAA,CAAA,WAAA,CAAA,MADA,EAEA,MAAA,CAAA,WAAA,CAAA,MAFA,CAAA;AAIA,OALA;AAMA,KA7EA;AA8EA;AACA,IAAA,UA/EA,wBA+EA;AAAA;;AACA,UAAA,MAAA,GAAA,EAAA,CADA,CACA;;AACA,UAAA,SAAA,GAAA,CAAA,KAAA,EAAA,KAAA,CAAA;AACA,UAAA,QAAA,GAAA,IAAA,QAAA,EAAA,CAHA,CAIA;;AACA,WAAA,OAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,GAAA,IAAA,SAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA;AACA;;AACA,QAAA,QAAA,CAAA,MAAA,CAAA,OAAA,EAAA,IAAA,CAAA,GAAA;AACA,OALA;AAMA,MAAA,QAAA,CAAA,MAAA,CAAA,YAAA,EAAA,KAAA,aAAA,CAAA,UAAA,EAXA,CAWA;;AACA,MAAA,QAAA,CAAA,MAAA,CAAA,QAAA,EAAA,MAAA,EAZA,CAYA;;AACA,MAAA,QAAA,CAAA,MAAA,CAAA,MAAA,EAAA,SAAA,EAbA,CAaA;;AACA,uBACA,WADA,CACA,2BADA,EACA,QADA,EACA,CADA,EAEA,IAFA,CAEA,UAAA,GAAA,EAAA;AACA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,EAAA,CAFA,CAGA;;AACA,QAAA,MAAA,CAAA,OAAA;AACA,OAPA,EAQA,KARA,CAQA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,SAAA;AACA,OAVA;AAWA,KAxGA;AAyGA;AACA,IAAA,YA1GA,wBA0GA,IA1GA,EA0GA,QA1GA,EA0GA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,KA5GA;AA6GA,IAAA,cA7GA,0BA6GA,KA7GA,EA6GA,IA7GA,EA6GA,QA7GA,EA6GA,CAAA,CA7GA;AA8GA;AACA,IAAA,YA/GA,wBA+GA,IA/GA,EA+GA,QA/GA,EA+GA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,KAjHA;AAkHA;AACA,IAAA,wBAnHA,oCAmHA,IAnHA,EAmHA;AACA,WAAA,cAAA,GAAA,IAAA,CAAA,GAAA;AACA,WAAA,gBAAA,GAAA,IAAA;AACA,KAtHA;AAuHA;AACA,IAAA,OAxHA,mBAwHA,MAxHA,EAwHA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,MAAA,CAAA,MAAA,+DAAA,MAAA,CAAA,MAAA,GAAA,MAAA;AACA,gBAAA,KAHA,GAGA,MAAA,CAAA,MAHA;AAIA,gBAAA,MAAA,CAAA,OAAA,GAAA,IAAA;AACA,gBAAA,KAAA,CAAA,OAAA,GAAA,CAAA;AAAA,kBAAA,IAAA,EAAA,YAAA;AAAA,kBAAA,GAAA,EAAA;AAAA,iBAAA,CAAA;AALA;AAAA,uBAMA,uBAAA,KAAA,CANA;;AAAA;AAAA;AAMA,gBAAA,IANA,kBAMA,IANA;AAMA,gBAAA,IANA,kBAMA,IANA;;AAOA,oBAAA,IAAA,KAAA,MAAA,EAAA;AAAA,uEACA,IAAA,CAAA,OADA;;AAAA;AACA,wEAAA;AAAA,sBAAA,CAAA;AACA,sBAAA,CAAA,CAAA,KAAA,GAAA,MAAA,CAAA,UAAA,CAAA,CAAA,CAAA,GAAA,CAAA;AACA,sBAAA,CAAA,CAAA,KAAA,GAAA,MAAA,CAAA,cAAA,CAAA,CAAA,CAAA,GAAA,CAAA;AACA,sBAAA,CAAA,CAAA,KAAA,GAAA,MAAA,CAAA,cAAA,CAAA,CAAA,CAAA,GAAA,CAAA;AACA,sBAAA,CAAA,CAAA,KAAA,GAAA,MAAA,CAAA,SAAA,CAAA,CAAA,CAAA,GAAA,CAAA;AACA,sBAAA,CAAA,CAAA,QAAA,GAAA,MAAA,CAAA,SAAA,CAAA,CAAA,CAAA,MAAA,CAAA;AACA;AAPA;AAAA;AAAA;AAAA;AAAA;;AAQA,kBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA,kBAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA;;AAlBA;AAAA;;AAAA;AAAA;AAAA;AAoBA,gBAAA,OAAA,CAAA,GAAA;;AApBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsBA,KA9IA;AAgJA;AACA,IAAA,SAjJA,uBAiJA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,KAAA,GAAA,YAAA;AACA,gBAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,gBAAA,OAAA,CAAA,IAAA,GAAA;AAAA,kBAAA,GAAA,EAAA,OAAA,CAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,QAAA;AAAA,iBAAA;AAHA;AAAA,uBAIA,OAAA,CAAA,SAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,CAJA;;AAAA;AAKA,gBAAA,OAAA,CAAA,IAAA,CAAA,MAAA,GAAA,GAAA;AACA,gBAAA,OAAA,CAAA,aAAA,GAAA,IAAA;;AANA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,KAxJA;AAyJA;AACA,IAAA,SA1JA,qBA0JA,GA1JA,EA0JA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,KAAA,GAAA,YAAA;AACA,gBAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,gBAAA,OAAA,CAAA,IAAA,mCAAA,GAAA;AAHA;AAAA,uBAIA,OAAA,CAAA,SAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,CAJA;;AAAA;AAKA,gBAAA,OAAA,CAAA,aAAA,GAAA,IAAA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,KAhKA;AAkKA;AACA,IAAA,UAnKA,sBAmKA,GAnKA,EAmKA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,KAAA,GAAA,YAAA;AACA,gBAAA,OAAA,CAAA,IAAA,mCAAA,GAAA;AAFA;AAAA,uBAGA,OAAA,CAAA,SAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,CAHA;;AAAA;AAIA,gBAAA,OAAA,CAAA,UAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,aAAA,GAAA,IAAA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,KAzKA;;AA0KA;;;AAGA,IAAA,SA7KA,uBA6KA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,QAAA;AAAA,mGAAA,kBAAA,KAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA,iCACA,KADA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA,mCAGA,4BAAA,OAAA,CAAA,IAAA,CAHA;;AAAA;AAAA;AAGA,4BAAA,IAHA,uBAGA,IAHA;AAGA,4BAAA,IAHA,uBAGA,IAHA;;AAAA,kCAIA,IAAA,KAAA,MAJA;AAAA;AAAA;AAAA;;AAKA;AACA,4BAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;AACA,4BAAA,OAAA,CAAA,aAAA,GAAA,KAAA;AAPA;AAAA,mCAQA,OAAA,CAAA,OAAA,EARA;;AAAA;AASA;AACA,4BAAA,OAAA,CAAA,aAAA,GAAA,IAAA;AACA,4BAAA,OAAA,CAAA,aAAA,CAAA,QAAA,GAAA,EAAA,CAXA,CAYA;;AACA,4BAAA,OAAA,CAAA,SAAA,CAAA,YAAA;AACA,mCAAA,KAAA,CAAA,SAAA,EAAA,aAAA;AACA,6BAFA;;AAGA,4BAAA,OAAA,CAAA,OAAA,GAAA,EAAA,CAhBA,CAiBA;;AACA,4BAAA,OAAA,CAAA,OAAA,CAAA,EAAA,GAAA,CAAA;AACA,4BAAA,OAAA,CAAA,OAAA,CAAA,MAAA,GAAA,GAAA;AACA,4BAAA,OAAA,CAAA,OAAA,CAAA,IAAA,GAAA,IAAA,CAAA,KAAA;AACA,4BAAA,OAAA,CAAA,OAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;;AACA,gCAAA,IAAA,CAAA,GAAA,EAAA;AACA,8BAAA,OAAA,CAAA,OAAA,CAAA,GAAA,GAAA,IAAA,CAAA,GAAA;AACA;;AACA,4BAAA,OAAA,CAAA,OAAA,CAAA,IAAA,GAAA,IAAA,CAAA,IAAA;AACA,4BAAA,OAAA,CAAA,OAAA,CAAA,GAAA,GAAA,IAAA,CAAA,GAAA;AACA,4BAAA,OAAA,CAAA,OAAA,CAAA,EAAA,GAAA,IAAA,CAAA,EAAA;AACA,4BAAA,OAAA,CAAA,UAAA,GAAA,IAAA;AACA,4BAAA,OAAA,CAAA,gBAAA,GAAA,IAAA;;AA7BA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAgCA,4BAAA,OAAA,CAAA,GAAA;;AAhCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAA;;AAAA;AAAA;AAAA;AAAA,oBADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqCA,KAlNA;;AAmNA;;;AAGA,IAAA,SAtNA,uBAsNA;AACA,WAAA,gBAAA,GAAA,IAAA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,WAAA,OAAA,CAAA,KAAA,GAAA,KAAA,IAAA,CAAA,KAAA;AACA,WAAA,OAAA,CAAA,GAAA,GAAA,KAAA,IAAA,CAAA,GAAA;AACA,WAAA,OAAA,CAAA,IAAA,GAAA,KAAA,IAAA,CAAA,IAAA;AACA,WAAA,OAAA,CAAA,GAAA,GAAA,KAAA,IAAA,CAAA,GAAA;AACA,WAAA,OAAA,CAAA,MAAA,GAAA,GAAA;AACA,WAAA,OAAA,CAAA,EAAA,GAAA,CAAA;AACA,WAAA,OAAA,CAAA,IAAA,GAAA,KAAA,IAAA,CAAA,KAAA;AACA,KAhOA;AAiOA,IAAA,OAjOA,qBAiOA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,QAAA;AAAA,oGAAA,mBAAA,KAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA,iCACA,KADA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA,mCAGA,4BAAA,OAAA,CAAA,IAAA,CAHA;;AAAA;AAAA;AAGA,4BAAA,IAHA,wBAGA,IAHA;;AAAA,kCAIA,IAAA,KAAA,MAJA;AAAA;AAAA;AAAA;;AAKA,4BAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA,EALA,CAMA;;;AACA,4BAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;AACA,4BAAA,OAAA,CAAA,aAAA,GAAA,KAAA;AARA;AAAA,mCASA,OAAA,CAAA,OAAA,EATA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAYA,4BAAA,OAAA,CAAA,GAAA;;AAZA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAA;;AAAA;AAAA;AAAA;AAAA,oBADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA,KAlPA;AAmPA;AACA,IAAA,SApPA,qBAoPA,GApPA,EAoPA;AAAA;;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAKA,IALA,CAKA,YAAA;AACA,8BAAA,CAAA,OAAA,CAAA,IAAA,CAAA,KAAA,CAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,cAAA,IAAA,SAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,OAAA,CAAA,OAAA;AACA,WANA,MAMA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAIA;AACA,SAbA;AAcA,OApBA,EAqBA,KArBA,CAqBA,YAAA,CAAA,CArBA;AAsBA,KA5QA;AA6QA;AACA,IAAA,KA9QA,mBA8QA;AACA,WAAA,gBAAA,GAAA,KAAA;AACA,WAAA,gBAAA,GAAA,KAAA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,KAlRA;AAmRA;AACA,IAAA,YApRA,wBAoRA,SApRA,EAoRA;AACA;AACA;AACA;AACA,WAAA,UAAA,GAAA,SAAA;AACA,KAzRA;AA0RA;AACA,IAAA,YA3RA,0BA2RA;AACA,UAAA,GAAA,GAAA;AACA,QAAA,EAAA,EAAA,EADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA;AAJA,OAAA;AAMA,WAAA,aAAA,CAAA,QAAA,CAAA,IAAA,CAAA,GAAA;AACA,WAAA,aAAA,CAAA,GAAA,GAAA,GAAA;AACA,WAAA,OAAA,CAAA,IAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,WAAA,OAAA,CAAA,OAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,KAtSA;AAuSA;AACA,IAAA,YAxSA,wBAwSA,GAxSA,EAwSA;AACA,UAAA,GAAA,CAAA,KAAA,EAAA;AACA,aAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA,KAAA;AACA;;AACA,WAAA,aAAA,CAAA,QAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA,CACA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,IAAA,KAAA,GAAA,CAAA,IAAA;AAAA,OADA,CAAA;AAGA,WAAA,OAAA,CAAA,IAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,WAAA,OAAA,CAAA,OAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,KAjTA;AAkTA;AACA,IAAA,QAnTA,sBAmTA;AACA,WAAA,gBAAA,GAAA,KAAA;AACA,WAAA,gBAAA,GAAA,KAAA;AACA,KAtTA;AAwTA;AACA,IAAA,UAzTA,wBAyTA;AAAA;;AACA,WAAA,OAAA,CAAA,QAAA,GAAA,KAAA,aAAA,CAAA,QAAA;AACA,WAAA,OAAA,CAAA,SAAA,GAAA,KAAA,GAAA;AACA,UAAA,UAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,IAAA,CAAA,UAAA,IAAA;AAAA,eAAA,CAAA,IAAA,CAAA,IAAA;AAAA,OAAA,CAAA;;AACA,UAAA,UAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,cAAA;AACA;;AACA,WAAA,KAAA,CAAA,SAAA,EAAA,QAAA;AAAA,0FAAA,mBAAA,KAAA;AAAA;AAAA;AAAA;AAAA;AACA,sBAAA,KAAA,IAAA,CAAA,UAAA,EAAA;AACA,mDAAA,OAAA,CAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,0BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,wBAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,wBAAA,OAAA,CAAA,gBAAA,GAAA,KAAA,CAFA,CAGA;;AACA,wBAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,wBAAA,OAAA,CAAA,OAAA;AACA;AACA,qBARA;AASA;;AAXA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAAA;;AAAA;AAAA;AAAA;AAAA;AAaA,KA7UA;AA+UA;AACA,IAAA,SAhVA,uBAgVA;AAAA;;AACA,WAAA,OAAA,CAAA,QAAA,GAAA,KAAA,aAAA,CAAA,QAAA;AACA,WAAA,OAAA,CAAA,SAAA,GAAA,KAAA,GAAA;AACA,UAAA,UAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,IAAA,CAAA,UAAA,IAAA;AAAA,eAAA,CAAA,IAAA,CAAA,IAAA;AAAA,OAAA,CAAA;;AACA,UAAA,UAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,cAAA;AACA;;AACA,WAAA,KAAA,CAAA,SAAA,EAAA,QAAA;AAAA,0FAAA,mBAAA,KAAA;AAAA;AAAA;AAAA;AAAA;AACA,sBAAA,KAAA,IAAA,CAAA,UAAA,EAAA;AACA,mDAAA,OAAA,CAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,0BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA;AACA,wBAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,wBAAA,OAAA,CAAA,OAAA;;AACA,4BAAA,IAAA,GAAA,GAAA,CAAA,IAAA;AACA,wBAAA,OAAA,CAAA,gBAAA,GAAA,KAAA;AACA,wBAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,wBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA,CAAA,KAAA;AACA,wBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,wBAAA,OAAA,CAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,wBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,wBAAA,OAAA,CAAA,WAAA,CAAA,IAAA,GAAA,OAAA;AACA,wBAAA,OAAA,CAAA,WAAA,CAAA,EAAA,GAAA,IAAA,CAAA,GAAA;AACA,wBAAA,OAAA,CAAA,WAAA,CAAA,aAAA,GAAA,EAAA;AACA,wBAAA,OAAA,CAAA,WAAA,CAAA,SAAA,GACA,gDADA;AAEA,wBAAA,OAAA,CAAA,MAAA,GAAA,IAAA;AACA;AACA,qBAnBA;AAoBA;;AAtBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAAA;;AAAA;AAAA;AAAA;AAAA;AAwBA,KA/WA;AAiXA;AACA,IAAA,UAlXA,wBAkXA;AAAA;;AACA,WAAA,KAAA,CAAA,SAAA,EAAA,QAAA;AAAA,0FAAA,mBAAA,KAAA;AAAA;AAAA;AAAA;AAAA;AACA,sBAAA,KAAA,EAAA;AACA,mDAAA,OAAA,CAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,0BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,wBAAA,OAAA,CAAA,aAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA,CADA,CAEA;;AACA,wBAAA,OAAA,CAAA,UAAA;;AACA,wBAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,wBAAA,OAAA,CAAA,gBAAA,GAAA,KAAA,CALA,CAMA;;AACA,wBAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,wBAAA,OAAA,CAAA,OAAA;AACA;AACA,qBAXA;AAYA;;AAdA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAAA;;AAAA;AAAA;AAAA;AAAA;AAgBA,KAnYA;;AAoYA;;;AAGA;AACA,IAAA,YAxYA,0BAwYA;AACA,WAAA,KAAA,GAAA,QAAA;AACA,WAAA,gBAAA,GAAA,IAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,SAAA,GAAA,IAAA;AACA,WAAA,WAAA,CAAA,IAAA,GAAA,KAAA,IAAA,CAAA,KAAA;AACA,WAAA,WAAA;AACA,KA/YA;AAgZA,IAAA,QAhZA,oBAgZA,GAhZA,EAgZA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAEA,wBAAA;AAAA,kBAAA,KAAA,EAAA,GAAA,CAAA;AAAA,iBAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,sBAEA,IAFA;AAEA,gBAAA,IAFA,sBAEA,IAFA;;AAGA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,kBAAA,CAAA,QAAA,GAAA,IAAA;AACA;;AALA;AAAA;;AAAA;AAAA;AAAA;AAOA,gBAAA,OAAA,CAAA,GAAA;;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,KAzZA;AA0ZA;AACA,IAAA,WA3ZA,uBA2ZA,MA3ZA,EA2ZA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,KADA,+DACA,MADA,GACA,OAAA,CAAA,WADA;AAEA,qCAAA,KAAA,EAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA,kBAAA,OAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,QAAA,CAAA,IAAA,CAAA,OAAA;AACA,kBAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,QAAA,CAAA,IAAA,CAAA,KAAA;AACA,iBAHA;;AAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,KAjaA;AAkaA;AACA,IAAA,UAnaA,wBAmaA;AACA,WAAA,WAAA,GAAA,EAAA;AACA,WAAA,WAAA;AACA,KAtaA;AAuaA;AACA,IAAA,UAxaA,wBAwaA;AACA,WAAA,aAAA,CAAA,QAAA,GAAA,KAAA,kBAAA,CAAA,QAAA;AACA,WAAA,aAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,CAAA,EAAA;AACA,QAAA,CAAA,CAAA,IAAA,GAAA,qBAAA;AACA,OAFA;AAGA,WAAA,OAAA,CAAA,IAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,WAAA,OAAA,CAAA,OAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,WAAA,gBAAA,GAAA,KAAA;AACA,KAhbA;;AAibA;;;AAGA;AACA,IAAA,WArbA,yBAqbA;AACA,WAAA,KAAA,GAAA,QAAA;AACA,WAAA,gBAAA,GAAA,IAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,SAAA,GAAA,IAAA;AACA,WAAA,cAAA,CAAA,KAAA,GAAA,KAAA,IAAA,CAAA,KAAA;AACA,WAAA,cAAA;AACA,KA5bA;AA6bA;AACA,IAAA,WA9bA,uBA8bA,GA9bA,EA8bA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAEA,0BAAA,GAAA,CAAA,KAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,sBAEA,IAFA;AAEA,gBAAA,IAFA,sBAEA,IAFA;;AAGA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,kBAAA,CAAA,QAAA,GAAA,IAAA;AACA;;AALA;AAAA;;AAAA;AAAA;AAAA;AAOA,gBAAA,OAAA,CAAA,GAAA;;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,KAvcA;AAwcA;AACA,IAAA,UAzcA,wBAycA;AACA,WAAA,aAAA,CAAA,QAAA,GAAA,KAAA,kBAAA,CAAA,QAAA;AACA,WAAA,OAAA,CAAA,IAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,WAAA,OAAA,CAAA,OAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,WAAA,aAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,CAAA,EAAA;AACA,QAAA,CAAA,CAAA,IAAA,GAAA,KAAA;AACA,QAAA,CAAA,CAAA,IAAA,GAAA,qBAAA;AACA,OAHA;AAIA,WAAA,gBAAA,GAAA,KAAA;AACA,KAldA;AAmdA;AACA,IAAA,cApdA,4BAodA;AAAA;;AACA,+BAAA,KAAA,cAAA,EAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA,QAAA,OAAA,CAAA,gBAAA,CAAA,QAAA,GAAA,QAAA,CAAA,IAAA,CAAA,OAAA;AACA,OAFA;AAGA,KAxdA;AAydA;AACA,IAAA,aA1dA,2BA0dA;AACA,WAAA,cAAA,GAAA;AACA,QAAA,MAAA,EAAA,GADA;AAEA,QAAA,EAAA,EAAA;AAFA,OAAA;AAIA,WAAA,cAAA;AACA,KAheA;;AAieA;;;AAGA,IAAA,gBApeA,4BAoeA,GApeA,EAoeA;AAAA;;AACA,sCAAA;AAAA,QAAA,MAAA,EAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,iBAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KAxeA;AAyeA;AACA,IAAA,WA1eA,uBA0eA,GA1eA,EA0eA,UA1eA,EA0eA;AAAA;;AACA,UAAA,GAAA,CAAA,KAAA,KAAA,KAAA,EAAA;AACA,wCAAA;AAAA,UAAA,MAAA,EAAA,GAAA,CAAA,KAAA,CAAA,QAAA;AAAA,SAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,gBAAA,IAAA,CAAA,KAAA,KAAA,OAAA,EAAA;AACA,cAAA,OAAA,CAAA,IAAA,CAAA,UAAA,EAAA,OAAA,EAAA,EAAA;;AACA,qBAAA,IAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;AACA;AACA,WALA;AAMA,SAPA;AAQA;AACA,KArfA;AAsfA,IAAA,WAtfA,yBAsfA;AACA,WAAA,MAAA,GAAA;AACA;AACA,QAAA,EAAA,EAAA;AAFA,OAAA;AAIA,KA3fA;AA4fA,IAAA,UA5fA,sBA4fA,IA5fA,EA4fA;AACA,UAAA,4BAAA,GAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,SAAA,CAAA,KAAA,mBAAA,CAAA,CAAA;AACA,MAAA,4BAAA,CAAA,IAAA,CAAA;AAAA,QAAA,KAAA,EAAA,SAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAAA;AACA,MAAA,4BAAA,CAAA,IAAA,CAAA;AAAA,QAAA,KAAA,EAAA,SAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAAA;;AACA,UAAA,IAAA,EAAA;AACA,YAAA,MAAA,GAAA,4BAAA,CAAA,MAAA,CAAA,UAAA,CAAA;AAAA,iBAAA,CAAA,CAAA,KAAA,KAAA,IAAA;AAAA,SAAA,CAAA;;AACA,YAAA,MAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,iBAAA,MAAA,CAAA,CAAA,CAAA,CAAA,KAAA;AACA,SAFA,MAEA;AACA,iBAAA,EAAA;AACA;AACA,OAPA,MAOA;AACA,eAAA,EAAA;AACA;AACA,KA1gBA;AA2gBA,IAAA,aA3gBA,yBA2gBA,aA3gBA,EA2gBA,MA3gBA,EA2gBA;AACA,aAAA,yBAAA;AACA,QAAA,aAAA,EAAA,aADA;AAEA,QAAA,MAAA,EAAA,MAFA;AAGA,QAAA,QAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,UAAA,GAAA,EAAA;AACA,eAAA,GAAA,CAAA,IAAA;AACA,OANA,CAAA;AAOA,KAnhBA;AAohBA,IAAA,cAphBA,0BAohBA,GAphBA,EAohBA;AACA,UAAA,GAAA,EAAA;AACA,YAAA,MAAA,GAAA,KAAA,YAAA,CAAA,MAAA,CAAA,UAAA,CAAA;AAAA,iBAAA,CAAA,CAAA,QAAA,KAAA,GAAA;AAAA,SAAA,CAAA;;AACA,YAAA,MAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,iBAAA,MAAA,CAAA,CAAA,CAAA,CAAA,QAAA;AACA,SAFA,MAEA;AACA,iBAAA,EAAA;AACA;AACA,OAPA,MAOA;AACA,eAAA,EAAA;AACA;AACA,KA/hBA;AAiiBA,IAAA,SAjiBA,qBAiiBA,GAjiBA,EAiiBA;AACA,UAAA,GAAA,EAAA;AACA,YAAA,MAAA,GAAA,KAAA,UAAA,CAAA,MAAA,CAAA,UAAA,CAAA;AAAA,iBAAA,CAAA,CAAA,QAAA,KAAA,GAAA;AAAA,SAAA,CAAA;;AACA,YAAA,MAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,iBAAA,MAAA,CAAA,CAAA,CAAA,CAAA,QAAA;AACA,SAFA,MAEA;AACA,iBAAA,EAAA;AACA;AACA,OAPA,MAOA;AACA,eAAA,EAAA;AACA;AACA,KA5iBA;AA6iBA;AACA,IAAA,UA9iBA,wBA8iBA;AACA,UAAA,MAAA,GAAA;AACA,QAAA,IAAA,EAAA,KAAA,MADA;AAEA,QAAA,GAAA,EAAA;AAFA,OAAA;AAIA,UAAA,QAAA,GAAA,YAAA;;AACA,UAAA,CAAA,KAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,QAAA,MAAA,CAAA,IAAA,GAAA,KAAA,MAAA;AACA,0CAAA,MAAA,EAAA,QAAA;AACA,OAHA,MAGA;AACA,QAAA,MAAA,CAAA,IAAA,GAAA,KAAA,UAAA;AACA,6CAAA,MAAA,EAAA,QAAA;AACA;AACA,KA3jBA;AA4jBA,IAAA,cA5jBA,0BA4jBA,GA5jBA,EA4jBA;AACA,UAAA,GAAA,EAAA;AACA,aAAA,IAAA,CAAA,KAAA,IAAA,EAAA,MAAA,EAAA,IAAA;AACA,aAAA,IAAA,CAAA,KAAA,IAAA,EAAA,KAAA,EAAA,EAAA;AACA,aAAA,IAAA,CAAA,KAAA,IAAA,EAAA,QAAA,EAAA,EAAA;AACA,OAJA,MAIA;AACA,aAAA,IAAA,CAAA,KAAA,IAAA,EAAA,MAAA,EAAA,IAAA;AACA,aAAA,IAAA,CAAA,KAAA,IAAA,EAAA,KAAA,EAAA,EAAA;AACA,aAAA,IAAA,CAAA,KAAA,IAAA,EAAA,KAAA,EAAA,EAAA;AACA;AACA;AAtkBA;AAxRA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n      @handleEvent=\"handleEvent\"\n      @handleReset=\"filterReset\"\n    />\n    <div>\n      <el-white class=\"button-group\">\n        <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n          <el-col\n            style=\"\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n          \"\n          >\n            <div>\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-plus\"\n                @click=\"getInster\"\n                v-hasPermi=\"['dzczml:button:add']\"\n                >新增\n              </el-button>\n              <!-- <el-button type=\"primary\" @click=\"exportFun\"\n              >导出\n              </el-button> -->\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-download\"\n                @click=\"exportWord\"\n                >导出</el-button\n              >\n            </div>\n          </el-col>\n        </el-row>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"selectChange\"\n          height=\"66vh\"\n          v-loading=\"loading\"\n        >\n          <el-table-column\n            slot=\"table_eight\"\n            align=\"center\"\n            fixed=\"right\"\n            style=\"display: block\"\n            label=\"操作\"\n            width=\"160\"\n            :resizable=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                @click=\"getDetails(scope.row)\"\n                type=\"text\"\n                size=\"small\"\n                title=\"详情\"\n                class=\"el-icon-view\"\n              ></el-button>\n              <el-button\n                @click=\"getUpdate(scope.row)\"\n                v-if=\"(scope.row.createBy === currentUser || hasSuperRole) && !scope.row.czp\"\n                type=\"text\"\n                size=\"small\"\n                title=\"编辑\"\n                class=\"el-icon-edit\"\n              >\n              </el-button>\n              <el-button\n                @click=\"deleteRow(scope.row)\"\n                v-if=\"\n                  (scope.row.createBy === currentUser ||\n                    hasSuperRole) &&\n                    !scope.row.czp\n                \"\n                type=\"text\"\n                size=\"small\"\n                title=\"删除\"\n                class=\"el-icon-delete\"\n              >\n              </el-button>\n            </template>\n          </el-table-column>\n        </comp-table>\n      </el-white>\n    </div>\n\n    <!-- 详情/新增/修改 操作命令页-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <div>\n          <el-row style=\"display: flex;flex-wrap: wrap;\">\n            <el-col :span=\"8\">\n              <el-form-item label=\"令类型：\" prop=\"zslOrYbl\">\n                <el-switch\n                  :disabled=\"isDisabled\"\n                  style=\"width: 100%\"\n                  v-model=\"form.zslOrYbl\"\n                  active-color=\"#13ce66\"\n                  inactive-color=\"#ff4949\"\n                  active-text=\"正式令\"\n                  inactive-text=\"预备令\"\n                  @change=\"changeZslOrYbl\"\n                >\n                </el-switch>\n              </el-form-item>\n            </el-col>\n            <!--            <el-col :span=\"8\">-->\n            <!--              <el-form-item label=\"编号：\" prop=\"bh\">-->\n            <!--                <el-input v-model=\"form.bh\" disabled placeholder=\"确认后编号自动生成\"/>-->\n            <!--              </el-form-item>-->\n            <!--            </el-col>-->\n            <el-col :span=\"8\">\n              <el-form-item label=\"分公司：\" prop=\"fgs\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.fgs\"\n                  @change=\"handleFgsChange\"\n                  disabled\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in ssgsOptionsDataList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"变电站：\" prop=\"bdzmc\">\n                <el-select\n                  style=\"width: 100%\"\n                  filterable\n                  clearable\n                  v-model=\"form.bdzmc\"\n                  ref=\"bdzmc\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择变电站\"\n                >\n                  <el-option\n                    v-for=\"item in wzDataListOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"!form.zslOrYbl\"\n                label=\"通知时间：\"\n                prop=\"tzsj\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: false }]\n                    : [{ required: true, message: '不能为空' }]\n                \"\n              >\n                <el-date-picker\n                  v-model=\"form.tzsj\"\n                  :disabled=\"isDisabled\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"!form.zslOrYbl\"\n                label=\"通知人：\"\n                prop=\"tzr\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: false }]\n                    : [{ required: true, message: '不能为空' }]\n                \"\n              >\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.tzr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in tzrOrXlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"!form.zslOrYbl\"\n                label=\"预备令接令人：\"\n                prop=\"ybljlr\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: false }]\n                    : [{ required: true, message: '不能为空' }]\n                \"\n              >\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.ybljlr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in jlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"form.zslOrYbl\"\n                label=\"下令时间：\"\n                prop=\"xlsj\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-date-picker\n                  v-model=\"form.xlsj\"\n                  :disabled=\"isDisabled\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"form.zslOrYbl\"\n                label=\"下令人：\"\n                prop=\"xlr\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.xlr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in tzrOrXlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"form.zslOrYbl\"\n                label=\"接令人：\"\n                prop=\"jlr\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.jlr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in jlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"回令时间：\" prop=\"hlsj\">\n                <el-date-picker\n                  v-model=\"form.hlsj\"\n                  :disabled=\"isDisabled\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  :placeholder=\"isDisabled ? '' : '选择日期时间'\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-form-item label=\"操作任务：\" prop=\"czrw\">\n            <el-input\n              type=\"textarea\"\n              v-model=\"form.czrw\"\n              :disabled=\"isDisabled\"\n              placeholder=\"请输入操作任务\"\n              :rows=\"3\"\n            />\n          </el-form-item>\n        </div>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">关 闭</el-button>\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"saveRow\"\n          >保 存</el-button\n        >\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"createCzp\"\n          >开操作票\n        </el-button>\n        <el-button\n          v-if=\"isDisabled && (form.createBy === currentUser || hasSuperRole)&& !form.czp\"\n          type=\"primary\"\n          @click=\"createBjp\"\n          >开办结票\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!--操作票开票页-->\n    <el-dialog\n      title=\"变电倒闸操作票开票\"\n      :visible.sync=\"isCzpShowDetails\"\n      append-to-body\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form\n        label-width=\"120px\"\n        ref=\"formCzp\"\n        :model=\"formCzp\"\n        :rules=\"czpRules\"\n      >\n        <div>\n          <el-row>\n            <!--            <el-col :span=\"8\">-->\n            <!--              <el-form-item label=\"编号：\" prop=\"bm\">-->\n            <!--                <el-input v-model=\"formCzp.bm\" disabled placeholder=\"确认后编号自动生成\"/>-->\n            <!--              </el-form-item>-->\n            <!--            </el-col>-->\n            <el-col :span=\"8\">\n              <el-form-item label=\"分公司：\" prop=\"fgs\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formCzp.fgs\"\n                  @change=\"handleFgsChange\"\n                  disabled\n                  placeholder=\"请选择分公司\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in ssgsOptionsDataList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"变电站：\" prop=\"bdzmc\">\n                <el-select\n                  style=\"width: 100%\"\n                  filterable\n                  v-model=\"formCzp.bdzmc\"\n                  disabled\n                  placeholder=\"请输入内容\"\n                >\n                  <el-option\n                    v-for=\"item in wzDataListOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作开始时间：\" prop=\"kssj\">\n                <el-date-picker\n                  disabled\n                  v-model=\"formCzp.kssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作结束时间：\" prop=\"jssj\">\n                <el-date-picker\n                  disabled\n                  v-model=\"formCzp.jssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作人：\" prop=\"czr\">\n                <el-input\n                  v-model=\"formCzp.czr\"\n                  :disabled=\"isDisabledCzp\"\n                  placeholder=\"请输入内容\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"监护人：\" prop=\"jhr\">\n                <el-input\n                  v-model=\"formCzp.jhr\"\n                  :disabled=\"isDisabledCzp\"\n                  placeholder=\"请输入内容\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"下令人：\" prop=\"xlr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formCzp.xlr\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                  disabled\n                >\n                  <el-option\n                    v-for=\"item in tzrOrXlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作项数：\" prop=\"czxs\">\n                <el-input-number\n                  v-model=\"formCzp.czxs\"\n                  disabled\n                  placeholder=\"请输入操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"已执行项数：\" prop=\"yzxczxs\">\n                <el-input-number\n                  v-model=\"formCzp.yzxczxs\"\n                  disabled\n                  placeholder=\"请输入已执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"未执行项数：\" prop=\"wzxczxs\">\n                <el-input-number\n                  v-model=\"formCzp.wzxczxs\"\n                  disabled\n                  placeholder=\"请输入未执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"操作任务：\" prop=\"czrw\">\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"formCzp.czrw\"\n                  :disabled=\"isDisabledCzp\"\n                  placeholder=\"请输入内容\"\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"上传图片：\">\n                <el-upload\n                  :disabled=\"isDisabledCzp\"\n                  action=\"\"\n                  ref=\"uploadImg\"\n                  accept=\"image/jpeg,image/jpg,image/png\"\n                  :headers=\"header\"\n                  :multiple=\"true\"\n                  :on-change=\"handleChange\"\n                  :data=\"uploadImgData\"\n                  :file-list=\"imgList\"\n                  :auto-upload=\"false\"\n                  :on-preview=\"handlePictureCardPreview\"\n                  :on-progress=\"handleProgress\"\n                  :on-remove=\"handleRemove\"\n                >\n                  <el-button type=\"primary\" size=\"small\">选择文件</el-button>\n                </el-upload>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n\n        <div>\n          <el-dialog :title=\"titleyl\" :visible.sync=\"yl\" append-to-body>\n            <!--第一个表格-->\n            <div style=\"margin-bottom:10px;\">\n              <el-row>\n                <el-col :span=\"8\">\n                  <el-input\n                    v-model=\"replaceData.oldStr\"\n                    style=\"width:80%\"\n                    placeholder=\"查找字符串\"\n                  >\n                  </el-input>\n                </el-col>\n\n                <el-col :span=\"8\">\n                  <!-- <span> ---</span> -->\n                  <el-input\n                    v-model=\"replaceData.newStr\"\n                    style=\"width:80%\"\n                    placeholder=\"替换后字符串\"\n                  >\n                  </el-input>\n                </el-col>\n                <el-col :span=\"4\">\n                  <el-button\n                    type=\"primary\"\n                    icon=\"el-icon-edit\"\n                    @click=\"replaceStr\"\n                    >批量替换</el-button\n                  >\n                </el-col>\n              </el-row>\n            </div>\n\n            <el-table\n              :data=\"propTableData.colFirst\"\n              ref=\"propTable\"\n              disabled\n              border\n              stripe\n              style=\"width: 100%\"\n              :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n            >\n              <el-table-column\n                align=\"center\"\n                width=\"120\"\n                prop=\"xh\"\n                label=\"顺序号\"\n                sortable\n              >\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"请输入顺序号\"\n                      disabled\n                      v-model=\"scope.row.xh\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"请输入操作项目\"\n                      disabled\n                      v-model=\"scope.row.czrw\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-dialog>\n        </div>\n        <!--列表-->\n        <div>\n          <el-white class=\"mb8 pull-right\">\n            <el-button type=\"primary\" @click=\"getDxpkLists\">典型票库</el-button>\n            <el-button type=\"warning\" @click=\"getLspkList\">历史票库</el-button>\n            <el-button\n              type=\"info\"\n              @click=\"handleYlChange\"\n              style=\"text-align: right\"\n              >预览</el-button\n            >\n          </el-white>\n          <!--第一个表格-->\n          <el-table\n            :data=\"propTableData.colFirst\"\n            height=\"300\"\n            border\n            stripe\n            style=\"width: 100%\"\n            element-loading-text=\"正在获取数据\"\n            element-loading-spinner=\"el-icon-loading\"\n            v-loading=\"loading\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <el-table-column\n              align=\"center\"\n              width=\"120\"\n              prop=\"xh\"\n              label=\"顺序号\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input-number\n                    size=\"small\"\n                    v-model=\"scope.row.xh\"\n                    :min=\"1\"\n                    :precision=\"0\"\n                    controls-position=\"right\"\n                  ></el-input-number>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入操作项目\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" width=\"80\">\n              <template slot=\"header\" slot-scope=\"scope\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  icon=\"el-icon-plus\"\n                  @click=\"listFirstAdd(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n              <template slot-scope=\"scope\">\n                <el-button\n                  type=\"text\"\n                  icon=\"el-icon-delete\"\n                  @click=\"listFirstDel(scope.row)\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"closeCzp\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"saveRowCzp\">保 存</el-button>\n        <el-button type=\"primary\" @click=\"submitCzp\">上 报</el-button>\n      </div>\n    </el-dialog>\n\n    <!--开办结票-->\n    <el-dialog\n      title=\"开办结票\"\n      :visible.sync=\"isBjpShowDetails\"\n      append-to-body\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form\n        label-width=\"120px\"\n        ref=\"formBjp\"\n        :model=\"formBjp\"\n        :rules=\"rules2\"\n      >\n        <div>\n          <el-row>\n            <el-col :span=\"8\">\n              <el-form-item label=\"编号：\" prop=\"bm\">\n                <el-input\n                  v-model=\"formBjp.bm\"\n                  disabled\n                  placeholder=\"确认后编号自动生成\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"分公司：\" prop=\"fgs\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formBjp.fgs\"\n                  @change=\"handleFgsChange\"\n                  disabled\n                  placeholder=\"请选择分公司\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in ssgsOptionsDataList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"变电站：\" prop=\"bdzmc\">\n                <el-select\n                  style=\"width: 100%\"\n                  filterable\n                  v-model=\"formBjp.bdzmc\"\n                  placeholder=\"请输入内容\"\n                  disabled\n                >\n                  <el-option\n                    v-for=\"item in wzDataListOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作开始时间：\" prop=\"kssj\">\n                <el-date-picker\n                  v-model=\"formBjp.kssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作结束时间：\" prop=\"jssj\">\n                <el-date-picker\n                  v-model=\"formBjp.jssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作人：\" prop=\"czr\">\n                <el-input v-model=\"formBjp.czr\" placeholder=\"请输入内容\" />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"监护人：\" prop=\"jhr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formBjp.jhr\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in jlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"下令人：\" prop=\"xlr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formBjp.xlr\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in tzrOrXlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"审票人：\" prop=\"bzspr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formBjp.bzspr\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in sprList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作项数：\" prop=\"czxs\">\n                <el-input-number\n                  v-model=\"formBjp.czxs\"\n                  placeholder=\"请输入操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"已执行项数：\" prop=\"yzxczxs\">\n                <el-input-number\n                  v-model=\"formBjp.yzxczxs\"\n                  placeholder=\"请输入已执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"未执行项数：\" prop=\"wzxczxs\">\n                <el-input-number\n                  v-model=\"formBjp.wzxczxs\"\n                  placeholder=\"请输入未执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"操作任务：\" prop=\"czrw\">\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"formBjp.czrw\"\n                  placeholder=\"请输入内容\"\n                  disabled\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"上传图片：\">\n                <el-upload\n                  action=\"\"\n                  ref=\"uploadImg\"\n                  accept=\"image/jpeg,image/jpg,image/png\"\n                  :headers=\"header\"\n                  :multiple=\"true\"\n                  :on-change=\"handleChange\"\n                  :data=\"uploadImgData\"\n                  :file-list=\"imgList\"\n                  :auto-upload=\"false\"\n                  list-type=\"picture-card\"\n                  :on-preview=\"handlePictureCardPreview\"\n                  :on-progress=\"handleProgress\"\n                  :on-remove=\"handleRemove\"\n                >\n                  <i class=\"el-icon-plus\"></i>\n                </el-upload>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"closeCzp\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"saveRowBjp\">确 认</el-button>\n      </div>\n    </el-dialog>\n\n    <!--典型票库-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isDxpShowDetails\"\n      width=\"50%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"180px\" ref=\"form\" :model=\"form\">\n        <el-row>\n          <!--查询及列表-->\n          <el-col :span=\"24\">\n            <el-form\n              :model=\"queryParams\"\n              class=\"searchForm\"\n              ref=\"queryForm\"\n              label-width=\"100px\"\n              v-show=\"isShowSx\"\n            >\n              <el-row>\n                <el-col :span=\"12\">\n                  <el-form-item label=\"典型票名称：\" prop=\"dxpmc\">\n                    <el-input\n                      placeholder=\"请输入典型票名称\"\n                      v-model=\"queryParams.dxpmc\"\n                      clearable\n                    />\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"12\">\n                  <el-form-item label=\"操作任务：\" prop=\"czrw\">\n                    <el-input\n                      placeholder=\"请输入操作任务\"\n                      v-model=\"queryParams.czrw\"\n                      clearable\n                    />\n                  </el-form-item>\n                </el-col>\n                <div style=\"float: right;margin-bottom: 10px\">\n                  <el-button\n                    type=\"primary\"\n                    icon=\"el-icon-search\"\n                    @click=\"handleQuery\"\n                    >搜索</el-button\n                  >\n                  <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\"\n                    >重置</el-button\n                  >\n                </div>\n              </el-row>\n            </el-form>\n            <div style=\"float: left;margin-bottom: 10px\">\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-search\"\n                @click=\"isShowSx = isShowSx ? false : true\"\n                >筛 选</el-button\n              >\n            </div>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <!--主表信息-->\n            <comp-table\n              :table-and-page-info=\"propTableDataDxp\"\n              height=\"400\"\n              border\n              stripe\n              style=\"width: 100%\"\n              max-height=\"60vh\"\n              @getMethod=\"handleQuery\"\n              @rowClick=\"changeMx\"\n            >\n            </comp-table>\n          </el-col>\n        </el-row>\n        <!--子表信息-->\n        <el-row>\n          <el-table\n            :data=\"propTableDataDxpMx.colFirst\"\n            :disabled=\"isDisabled\"\n            height=\"300\"\n            border\n            stripe\n            style=\"width: 100%\"\n            max-height=\"40vh\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <!--子表列表-->\n            <el-table-column\n              align=\"center\"\n              prop=\"xh\"\n              label=\"序号\"\n              width=\"100\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"序号\"\n                    :disabled=\"isShowDxp\"\n                    v-model=\"scope.row.xh\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"操作项目\"\n                    :disabled=\"isShowDxp\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n          </el-table>\n        </el-row>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"saveRowDxp\">确 认 </el-button>\n      </div>\n    </el-dialog>\n\n    <!--历史票库-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isLspShowDetails\"\n      width=\"50%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"180px\" ref=\"form\" :model=\"form\">\n        <el-form\n          :model=\"queryParamsLsp\"\n          class=\"searchForm\"\n          ref=\"queryForm\"\n          label-width=\"100px\"\n          v-show=\"isShowSx\"\n        >\n          <el-row>\n            <el-col :span=\"12\">\n              <el-form-item label=\"操作时间\" prop=\"czsjArr\">\n                <el-date-picker\n                  type=\"daterange\"\n                  range-separator=\"-\"\n                  start-placeholder=\"开始时间\"\n                  end-placeholder=\"结束时间\"\n                  v-model=\"queryParamsLsp.czsjArr\"\n                  clearable\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <!--<el-col :span=\"12\">\n              <el-form-item label=\"操作结束时间\" prop=\"jssj\">\n                <el-input placeholder=\"请输入操作结束时间\" v-model=\"queryParamsLsp.jssj\" clearable primary style=\"width: 100%\"/>\n              </el-form-item>\n            </el-col>-->\n            <el-col :span=\"24\">\n              <el-form-item label=\"操作任务\" prop=\"czrw\">\n                <el-input\n                  type=\"textarea\"\n                  :rows=\"1\"\n                  placeholder=\"请输入操作任务\"\n                  v-model=\"queryParamsLsp.czrw\"\n                  clearable\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <el-col :span=\"12\">\n              <el-form-item label=\"操作人\" prop=\"czr\">\n                <el-input\n                  placeholder=\"请输入操作人\"\n                  v-model=\"queryParamsLsp.czr\"\n                  clearable\n                  primary\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"监护人\" prop=\"jhr\">\n                <el-input\n                  placeholder=\"请输入监护人\"\n                  v-model=\"queryParamsLsp.jhr\"\n                  clearable\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <el-col :span=\"12\">\n              <el-form-item label=\"下令人\" prop=\"xlr\">\n                <el-input\n                  placeholder=\"请输入下令人\"\n                  v-model=\"queryParamsLsp.xlr\"\n                  clearable\n                  primary\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n            <div style=\"float: right;margin-bottom: 10px\">\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-search\"\n                @click=\"handleQueryLsp\"\n                >搜索</el-button\n              >\n              <el-button icon=\"el-icon-refresh\" @click=\"resetQueryLsp\"\n                >重置</el-button\n              >\n            </div>\n          </el-row>\n        </el-form>\n        <!--主表信息-->\n        <div>\n          <div style=\"float: left;margin-bottom: 10px\">\n            <el-button\n              type=\"primary\"\n              icon=\"el-icon-search\"\n              @click=\"isShowSx = isShowSx ? false : true\"\n              >筛 选</el-button\n            >\n          </div>\n          <el-table\n            @row-click=\"changeLspMx\"\n            :data=\"propTableDataLsp.colFirst\"\n            :disabled=\"isDisabled\"\n            height=\"300\"\n            border\n            stripe\n            style=\"width: 100%\"\n            max-height=\"40vh\"\n          >\n            <el-table-column\n              align=\"center\"\n              prop=\"bdzmcs\"\n              label=\"变电站\"\n              width=\"170\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入变电站\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.bdzmcs\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"kssj\"\n              label=\"操作开始时间\"\n              width=\"170\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入开始时间\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.kssj\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"jssj\"\n              label=\"操作结束时间\"\n              width=\"170\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入结束时间\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.jssj\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"czrw\"\n              label=\"操作任务\"\n              width=\"230\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    type=\"textarea\"\n                    :rows=\"2\"\n                    placeholder=\"请输入操作任务\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"czr\"\n              label=\"操作人\"\n              width=\"120\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.czr\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"jhr\"\n              label=\"监护人\"\n              width=\"120\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.jhr\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"xlr\"\n              label=\"下令人\"\n              width=\"120\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.xlrmc\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"spr\"\n              label=\"审票人\"\n              width=\"150\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.bzsprmc\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n        <!--子表信息-->\n        <div>\n          <el-table\n            :data=\"propTableDataLspMx.colFirst\"\n            :disabled=\"isDisabled\"\n            height=\"300\"\n            border\n            stripe\n            style=\"width: 100%\"\n            max-height=\"40vh\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <!--子表列表-->\n            <el-table-column\n              align=\"center\"\n              width=\"120\"\n              prop=\"xh\"\n              label=\"顺序号\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入顺序号\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.xh\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入操作项目\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"saveRowLsp\">确 认 </el-button>\n      </div>\n    </el-dialog>\n\n    <!--图片放大展示弹出框-->\n    <el-dialog title=\"放大\" :visible.sync=\"imgDialogVisible\" v-dialogDrag>\n      <img width=\"100%\" :src=\"dialogImageUrl\" alt=\"\" />\n    </el-dialog>\n\n    <!--  工作流  -->\n    <activiti\n      ref=\"activiti\"\n      :processData=\"processData\"\n      :isShow=\"isShow\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n    ></activiti>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport api from \"@/utils/request\";\nimport {\n  exportExcel,\n  getBdzSelectList,\n  getList,\n  remove,\n  saveOrUpdate,\n  saveOrUpdateCzp,\n  exportWordByselection,\n  exportWordByparams\n} from \"@/api/yxgl/bdyxgl/bddzczml\";\nimport { getDxpkList, getLists } from \"@/api/bzgl/dxczp\";\nimport { getListLsp, getLspkList, updateById } from \"@/api/yxgl/bdyxgl/bddzczp\";\nimport ElectronicAuthDialog from \"com/ElectronicAuthDialog\";\nimport CompTable from \"com/CompTable\";\nimport ElFilter from \"com/ElFilter\";\nimport { getUsers } from \"@/api/system/usergroup\";\nimport activiti from \"com/activiti_czp\";\nimport { getUUID } from \"@/utils/ruoyi\";\nimport { getFgsOptions } from \"@/api/yxgl/bdyxgl/zbgl\";\n\nexport default {\n  name: \"dzczml\",\n  components: { ElectronicAuthDialog, CompTable, ElFilter, activiti },\n  data() {\n    return {\n      hasSuperRole:this.$store.getters.hasSuperRole,\n      //loading:false,\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"czpsh\",\n        businessKey: \"\",\n        businessType: \"倒闸操作票\",\n        variables: {},\n        defaultFrom: true,\n        nextUser: \"\",\n        processType: \"complete\"\n      },\n      isShow: false,\n      //弹出框标题\n      activitiOption: { title: \"上报\" },\n      czpRules: {\n        // xlr: [\n        //   {required: true, message: '不能为空', trigger: 'blur'}\n        // ],\n      },\n      rules: {\n        bdzmc: [{ required: true, message: \"不能为空\", trigger: \"select\" }],\n        fgs: [{ required: true, message: \"不能为空\", trigger: \"select\" }],\n        czrw: [{ required: true, message: \"不能为空\", trigger: \"blur\" }]\n      },\n      rules2: {\n        kssj: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        jssj: [{ required: true, message: \"不能为空\", trigger: \"select\" }],\n        czr: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        jhr: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        xlr: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        czxs: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        yzxczxs: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        wzxczxs: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        bzspr: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        czrw: [{ required: true, message: \"不能为空\", trigger: \"blur\" }]\n      },\n      currentUser: this.$store.getters.name,\n      yl: false,\n      replaceData: {},\n      titleyl: \"\",\n      //图片地址url\n      dialogImageUrl: \"\",\n      //展示图片dialog控制\n      imgDialogVisible: false,\n      //上传图片时的携带的其他参数\n      uploadImgData: {\n        businessId: \"\" //携带的表单主键id\n      },\n      //上传图片时的请求头\n      header: {},\n      //图片list\n      imgList: [],\n      isShowSx: false,\n      bdzList: [],\n      //弹出框内新增时下拉框所属位置数据\n      wzDataListOptions: [],\n      isShowDxp: false,\n      isShowLsp: false,\n      loading: false,\n      isDisabledCzp: false,\n      isDxpShowDetails: false,\n      isLspShowDetails: false,\n      //操作票弹框是否显示\n      isCzpShowDetails: false,\n      isBjpShowDetails: false,\n      // 多选框选中的id\n      ids: [],\n      selectData: [],\n      //倒闸操作票命令\n      params: {\n        //变电\n        lx: 2\n      },\n      //典型票查询条件\n      queryParams: {},\n      //历史票查询条件\n      queryParamsLsp: {\n        status: \"4\",\n        lx: 2\n      },\n      //弹出框中表格数据\n      propTableData: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //弹出框中表格数据\n      propTableDataDxpMx: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //弹出框中表格数据\n      propTableDataDxp: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 1,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"变电站\", prop: \"sbmcms\", minWidth: \"200\" },\n          { label: \"典型操作票名称\", prop: \"dxpmc\", minWidth: \"200\" },\n          { label: \"操作任务\", prop: \"czrw\", minWidth: \"200\" }\n        ],\n        option: { checkBox: false, serialNumber: true },\n        sel: null // 选中行\n      },\n      //弹出框中表格数据\n      propTableDataLspMx: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //弹出框中表格数据\n      propTableDataLsp: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //form表单\n      form: {\n        bh: \"\",\n        bdzmc: \"\",\n        tzsj: \"\",\n        tzr: \"\",\n        xlsj: \"\",\n        xlr: \"\",\n        czrw: \"\",\n        jlr: \"\",\n        hlsj: \"\",\n        status: \"\"\n      },\n      // 操作票\n      formCzp: {\n        bh: \"\",\n        bdzmc: \"\",\n        kssj: \"\",\n        jssj: \"\",\n        czrw: \"\",\n        czr: \"\",\n        jhr: \"\",\n        xlr: \"\",\n        spr: \"\",\n        lx: 2, //变电\n        colFirst: [],\n        status: \"\"\n      },\n      // 办结票\n      formBjp: {\n        bh: \"\",\n        bdzmc: \"\",\n        kssj: \"\",\n        jssj: \"\",\n        czrw: \"\",\n        czr: \"\",\n        jhr: \"\",\n        xlr: \"\",\n        spr: \"\",\n        lx: 2, //变电\n        colFirst: [],\n        status: \"\"\n      },\n\n      //所属分公司下拉框数据\n      ssgsOptionsDataList: [],\n\n      //详情弹框是否显示\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          bh: \"\",\n          bdzmc: \"\",\n          tzsjArr: \"\",\n          tzr: \"\",\n          xlsjArr: \"\",\n          xlr: \"\",\n          czrw: \"\",\n          jlr: \"\",\n          hlsjArr: \"\"\n        }, //查询条件\n        fieldList: [\n          // {\n          //   label: '令',\n          //   type: 'switch',\n          //   value: 'yblOrZsl',\n          //   textStart:'预备令',\n          //   textEnd:'正式令'\n          // },\n          // {label: '编号', value: 'bh', type: 'input', clearable: true},\n          // /*{ label: '状态', value: 'status', type: 'select', clearable: true },*/\n          {\n            label: \"分公司\",\n            value: \"fgs\",\n            type: \"select\",\n            options: [],\n            clearable: true\n          },\n          {\n            label: \"变电站\",\n            value: \"bdzmc\",\n            type: \"select\",\n            options: [],\n            clearable: true,\n            filterable: true\n          },\n          {\n            label: \"通知时间\",\n            value: \"tzsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"通知人\", value: \"tzr\", type: \"input\", clearable: true },\n          {\n            label: \"下令时间\",\n            value: \"xlsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"下令人\", value: \"xlr\", type: \"input\", clearable: true },\n          { label: \"操作任务\", value: \"czrw\", type: \"input\", clearable: true },\n          { label: \"接令人\", value: \"jlr\", type: \"input\", clearable: true },\n          {\n            label: \"回令时间\",\n            value: \"hlsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          /* { label: '状态', prop: 'status', minWidth: '70' },*/\n          { label: \"分公司\", prop: \"fgsmc\", minWidth: \"120\" },\n          { label: \"变电站\", prop: \"bdzmcs\", minWidth: \"120\" },\n          { label: \"通知人\", prop: \"tzrxm\", minWidth: \"80\" },\n          { label: \"通知时间\", prop: \"tzsj\", minWidth: \"140\" },\n          { label: \"下令人\", prop: \"xlrmc\", minWidth: \"80\" },\n          { label: \"下令时间\", prop: \"xlsj\", minWidth: \"140\" },\n          { label: \"操作任务\", prop: \"czrw\", minWidth: \"160\" },\n          { label: \"接令人\", prop: \"jlrxm\", minWidth: \"80\" },\n          { label: \"预备令接令人\", prop: \"ybljlrxm\", minWidth: \"100\" },\n          { label: \"回令时间\", prop: \"hlsj\", minWidth: \"140\" }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      tzrOrXlrList: [],\n      jlrList: [],\n      alljlrList: [],\n      sprList: []\n    };\n  },\n  async mounted() {\n    //获取token\n    this.header.token = getToken();\n    //变电站下拉框\n    this.getFgsOptions();\n    this.getAllBdzSelectList();\n    this.tzrOrXlrList = await this.getGroupUsers(61, \"\");\n    this.alljlrList = await this.getGroupUsers(62, \"\");\n    this.sprList = await this.getGroupUsers(13, \"\");\n    await this.getData();\n  },\n  methods: {\n    /**\n     * 获取分公司下拉数据\n     */\n    getFgsOptions() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.ssgsOptionsDataList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"fgs\") {\n            return (item.options = this.ssgsOptionsDataList);\n          }\n        });\n      });\n    },\n    //导出\n    exportFun() {\n      exportExcel(this.params, \"操作命令信息\");\n    },\n    //工作流回传数据\n    async todoResult(data) {\n      let row = {\n        objId: data.businessKey,\n        isStart: 1,\n        isBack: data.processType === \"rollback\" ? 1 : 0\n      };\n      row.status = \"1\";\n      row.bzspr = data.nextUser;\n      let { code } = await updateById(row);\n      if (code === \"0000\") {\n        await this.getData();\n      } else {\n        this.$message.warning(\"操作失败\");\n      }\n    },\n    //关闭弹窗\n    closeActiviti() {\n      if (this.$refs.activiti.$refs.form) {\n        this.$refs.activiti.$refs.form.resetFields();\n      }\n      this.isShow = false;\n    },\n    getAllBdzSelectList() {\n      getBdzSelectList({}).then(res => {\n        this.bdzList = res.data;\n      });\n    },\n    //所属公司change事件\n    async handleFgsChange(fgsValue) {\n      //清空之前得选中值\n      this.wzDataListOptions = [];\n      this.$set(this.form, \"bdzmc\", \"\");\n      this.$set(this.form, \"tzr\", \"\");\n      this.jlrList = [];\n      this.$set(this.form, \"jlr\", \"\");\n      this.$set(this.form, \"ybljlr\", \"\");\n      //获取变电站方法\n      await this.fgsChange(fgsValue);\n    },\n    async fgsChange(fgsValue) {\n      //获取变电站方法\n      this.getBdzSelectList(fgsValue);\n      this.jlrList = await this.getGroupUsers(62, fgsValue);\n    },\n    handleYlChange() {\n      this.titleyl = \"查看操作项目\";\n      this.yl = true;\n    },\n    replaceStr() {\n      this.propTableData.colFirst.forEach(item => {\n        item.czrw = item.czrw.replaceAll(\n          this.replaceData.oldStr,\n          this.replaceData.newStr\n        );\n      });\n    },\n    //改造后的上传多个图片文件\n    uploadForm() {\n      var newUrl = []; //用来存放当前未改动过的图片url,此图片不进行删除处理，其余当前业务id下面的图片数据将进行删除\n      var imageType = [\"png\", \"jpg\"];\n      const formData = new FormData();\n      // 因为要传一个文件数组过去，所以要循环append\n      this.imgList.forEach(file => {\n        if (file.raw == undefined) {\n          newUrl.push(file.url);\n        }\n        formData.append(\"files\", file.raw);\n      });\n      formData.append(\"businessId\", this.uploadImgData.businessId); // 自定义参数\n      formData.append(\"newUrl\", newUrl); // 未改动过的图片url\n      formData.append(\"type\", imageType); // 未改动过的图片url\n      api\n        .requestPost(\"/isc-api/file/uploadFiles\", formData, 1)\n        .then(res => {\n          // 清空图片列表（一定要清空，否则上传成功后还是会调用handleChange（）函数，上传成功后列表中还存在图片）\n          this.imgList = [];\n          //查询接口数据\n          this.getData();\n        })\n        .catch(res => {\n          this.$message.success(\"图片上传失败！\");\n        });\n    },\n    // 选择文件时，往fileList里添加\n    handleChange(file, fileList) {\n      this.imgList = fileList;\n    },\n    handleProgress(event, file, fileList) {},\n    //图片移除\n    handleRemove(file, fileList) {\n      this.imgList = fileList;\n    },\n    //图片放大\n    handlePictureCardPreview(file) {\n      this.dialogImageUrl = file.url;\n      this.imgDialogVisible = true;\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        this.loading = true;\n        param.mySorts = [{ prop: \"updateTime\", asc: false }];\n        const { data, code } = await getList(param);\n        if (code === \"0000\") {\n          for (let i of data.records) {\n            i.fgsmc = this.formatSsgs(i.fgs);\n            i.tzrxm = this.formatXlrOrTzr(i.tzr);\n            i.xlrxm = this.formatXlrOrTzr(i.xlr);\n            i.jlrxm = this.formatJlr(i.jlr);\n            i.ybljlrxm = this.formatJlr(i.ybljlr);\n          }\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          this.loading = false;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n\n    //新增按钮\n    async getInster() {\n      this.title = \"变电倒闸操作命令增加\";\n      this.isDisabled = false;\n      this.form = { fgs: this.$store.getters.deptId.toString() };\n      await this.fgsChange(this.form.fgs);\n      this.form.status = \"0\";\n      this.isShowDetails = true;\n    },\n    //修改按钮\n    async getUpdate(row) {\n      this.title = \"变电倒闸操作命令修改\";\n      this.isDisabled = false;\n      this.form = { ...row };\n      await this.fgsChange(this.form.fgs);\n      this.isShowDetails = true;\n    },\n\n    //详情按钮\n    async getDetails(row) {\n      this.title = \"变电倒闸操作命令详情\";\n      this.form = { ...row };\n      await this.fgsChange(this.form.fgs);\n      this.isDisabled = true;\n      this.isShowDetails = true;\n    },\n    /**\n     * 操作票开票按钮\n     * */\n    async createCzp() {\n      await this.$refs[\"form\"].validate(async valid => {\n        if (valid) {\n          try {\n            let { code, data } = await saveOrUpdate(this.form);\n            if (code === \"0000\") {\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.isShowDetails = false;\n              await this.getData();\n              //操作票弹出框\n              this.isDisabledCzp = true;\n              this.propTableData.colFirst = [];\n              //清除校验提示\n              this.$nextTick(function() {\n                this.$refs[\"formCzp\"].clearValidate();\n              });\n              this.formCzp = {};\n              //变电\n              this.formCzp.lx = 2;\n              this.formCzp.status = \"0\";\n              this.formCzp.czml = data.objId;\n              this.formCzp.bdzmc = data.bdzmc;\n              if (data.xlr) {\n                this.formCzp.xlr = data.xlr;\n              }\n              this.formCzp.czrw = data.czrw;\n              this.formCzp.fgs = data.fgs;\n              this.formCzp.bm = data.bh;\n              this.isDisabled = true;\n              this.isCzpShowDetails = true;\n            }\n          } catch (e) {\n            console.log(e);\n          }\n        }\n      });\n    },\n    /**\n     * 开办结票\n     * */\n    createBjp() {\n      this.isBjpShowDetails = true;\n      this.isShowDetails = false;\n      this.formBjp.bdzmc = this.form.bdzmc;\n      this.formBjp.xlr = this.form.xlr;\n      this.formBjp.czrw = this.form.czrw;\n      this.formBjp.fgs = this.form.fgs;\n      this.formBjp.status = \"4\";\n      this.formBjp.lx = 2;\n      this.formBjp.czml = this.form.objId;\n    },\n    async saveRow() {\n      await this.$refs[\"form\"].validate(async valid => {\n        if (valid) {\n          try {\n            let { code } = await saveOrUpdate(this.form);\n            if (code === \"0000\") {\n              this.$message.success(\"操作成功\");\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.isShowDetails = false;\n              await this.getData();\n            }\n          } catch (e) {\n            console.log(e);\n          }\n        }\n      });\n    },\n    //删除按钮\n    deleteRow(row) {\n      this.form = { ...row };\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove([this.form.objId]).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {});\n    },\n    //关闭弹窗\n    close() {\n      this.isDxpShowDetails = false;\n      this.isLspShowDetails = false;\n      this.isShowDetails = false;\n    },\n    //选择行\n    selectChange(selection) {\n      // this.ids = selection.map(item => item.objId);\n      // this.single = selection.length !== 1\n      // this.multiple = !selection.length\n      this.selectData = selection;\n    },\n    //表格新增\n    listFirstAdd() {\n      let row = {\n        xh: \"\",\n        czrw: \"\",\n        sfwc: \"\",\n        uuid: getUUID()\n      };\n      this.propTableData.colFirst.push(row);\n      this.propTableData.sel = row;\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //表格删除\n    listFirstDel(row) {\n      if (row.objId) {\n        this.ids.push(row.objId);\n      }\n      this.propTableData.colFirst = this.propTableData.colFirst.filter(\n        item => item.uuid !== row.uuid\n      );\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //操作票关闭弹窗\n    closeCzp() {\n      this.isCzpShowDetails = false;\n      this.isBjpShowDetails = false;\n    },\n\n    //操作票确定按钮\n    saveRowCzp() {\n      this.formCzp.colFirst = this.propTableData.colFirst;\n      this.formCzp.objIdList = this.ids;\n      let tableValid = this.propTableData.colFirst.some(item => !item.czrw);\n      if (tableValid) {\n        this.$message.error(\"操作项目存在空项，请检查\");\n      }\n      this.$refs[\"formCzp\"].validate(async valid => {\n        if (valid && !tableValid) {\n          saveOrUpdateCzp(this.formCzp).then(res => {\n            if (res.code === \"0000\") {\n              this.$message.success(\"操作成功\");\n              this.isCzpShowDetails = false;\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            }\n          });\n        }\n      });\n    },\n\n    //直接上报操作票\n    submitCzp() {\n      this.formCzp.colFirst = this.propTableData.colFirst;\n      this.formCzp.objIdList = this.ids;\n      let tableValid = this.propTableData.colFirst.some(item => !item.czrw);\n      if (tableValid) {\n        this.$message.error(\"操作项目存在空项，请检查\");\n      }\n      this.$refs[\"formCzp\"].validate(async valid => {\n        if (valid && !tableValid) {\n          saveOrUpdateCzp(this.formCzp).then(res => {\n            if (res.code === \"0000\") {\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n              let data = res.data;\n              this.isCzpShowDetails = false;\n              this.processData.variables.pass = true;\n              this.processData.businessKey = data.objId;\n              this.processData.processType = \"complete\";\n              this.activitiOption.title = \"提交\";\n              this.processData.defaultFrom = true;\n              this.processData.rylx = \"班组审核人\";\n              this.processData.dw = data.fgs;\n              this.processData.personGroupId = 13;\n              this.processData.routePath =\n                \"/bdgl/bddzcz/dagangOilfield/czpgl/bddzcz/dzczp\";\n              this.isShow = true;\n            }\n          });\n        }\n      });\n    },\n\n    //办结票确定按钮\n    saveRowBjp() {\n      this.$refs[\"formBjp\"].validate(async valid => {\n        if (valid) {\n          saveOrUpdateCzp(this.formBjp).then(res => {\n            if (res.code === \"0000\") {\n              this.uploadImgData.businessId = res.data.objId;\n              //开始上传图片\n              this.uploadForm();\n              this.$message.success(\"操作成功\");\n              this.isBjpShowDetails = false;\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            }\n          });\n        }\n      });\n    },\n    /**\n     * -----------------------------------典型票库---------------------------------------\n     * */\n    //获取典型操作票\n    getDxpkLists() {\n      this.title = \"典型票库查询\";\n      this.isDxpShowDetails = true;\n      this.isDisabled = false;\n      this.isShowDxp = true;\n      this.queryParams.sbmc = this.form.bdzmc;\n      this.handleQuery();\n    },\n    async changeMx(row) {\n      try {\n        const { data, code } = await getDxpkList({ objId: row.objId });\n        if (code === \"0000\") {\n          this.propTableDataDxpMx.colFirst = data;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //查询条件\n    async handleQuery(params) {\n      let param = { ...params, ...this.queryParams };\n      getLists(param).then(response => {\n        this.propTableDataDxp.tableData = response.data.records;\n        this.propTableDataDxp.pager.total = response.data.total;\n      });\n    },\n    //重置条件\n    resetQuery() {\n      this.queryParams = {};\n      this.handleQuery();\n    },\n    //典型票库确认按钮\n    saveRowDxp() {\n      this.propTableData.colFirst = this.propTableDataDxpMx.colFirst;\n      this.propTableData.colFirst.forEach(e => {\n        e.uuid = getUUID();\n      });\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n      this.isDxpShowDetails = false;\n    },\n    /**\n     * -----------------------------------历史票库---------------------------------------\n     * */\n    //获取历史操作票\n    getLspkList() {\n      this.title = \"历史票库查询\";\n      this.isLspShowDetails = true;\n      this.isDisabled = false;\n      this.isShowLsp = true;\n      this.queryParamsLsp.bdzmc = this.form.bdzmc;\n      this.handleQueryLsp();\n    },\n    // 当点击行时，传入参数查询\n    async changeLspMx(row) {\n      try {\n        const { data, code } = await getLspkList(row.objId);\n        if (code === \"0000\") {\n          this.propTableDataLspMx.colFirst = data;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //历史票库确认按钮\n    saveRowLsp() {\n      this.propTableData.colFirst = this.propTableDataLspMx.colFirst;\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n      this.propTableData.colFirst.forEach(e => {\n        e.sfwc = false;\n        e.uuid = getUUID();\n      });\n      this.isLspShowDetails = false;\n    },\n    //查询条件\n    handleQueryLsp() {\n      getListLsp(this.queryParamsLsp).then(response => {\n        this.propTableDataLsp.colFirst = response.data.records;\n      });\n    },\n    //重置条件\n    resetQueryLsp() {\n      this.queryParamsLsp = {\n        status: \"4\",\n        lx: 2\n      };\n      this.handleQueryLsp();\n    },\n    /**\n     * 获取变电站下拉框数据\n     */\n    getBdzSelectList(fgs) {\n      getBdzSelectList({ ssdwbm: fgs }).then(res => {\n        this.wzDataListOptions = res.data;\n      });\n    },\n    //下拉框change事件\n    handleEvent(val, eventValue) {\n      if (val.label === \"fgs\") {\n        getBdzSelectList({ ssdwbm: val.value.toString() }).then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value === \"bdzmc\") {\n              this.$set(eventValue, \"bdzmc\", \"\");\n              return (item.options = res.data);\n            }\n          });\n        });\n      }\n    },\n    filterReset() {\n      this.params = {\n        //变电\n        lx: 2\n      };\n    },\n    formatSsgs(ssgs) {\n      let pageOrganizationSelectedList = JSON.parse(JSON.stringify(this.ssgsOptionsDataList))\n      pageOrganizationSelectedList.push({label: '港东变电分公司', value: '3002'})\n      pageOrganizationSelectedList.push({label: '港中变电分公司', value: '3003'})\n      if (ssgs) {\n        let filter = pageOrganizationSelectedList.filter(g => g.value === ssgs);\n        if (filter.length > 0) {\n          return filter[0].label;\n        } else {\n          return \"\";\n        }\n      } else {\n        return \"\";\n      }\n    },\n    getGroupUsers(personGroupId, deptId) {\n      return getUsers({\n        personGroupId: personGroupId,\n        deptId: deptId,\n        deptName: \"\"\n      }).then(res => {\n        return res.data;\n      });\n    },\n    formatXlrOrTzr(xlr) {\n      if (xlr) {\n        let filter = this.tzrOrXlrList.filter(g => g.userName === xlr);\n        if (filter.length > 0) {\n          return filter[0].nickName;\n        } else {\n          return \"\";\n        }\n      } else {\n        return \"\";\n      }\n    },\n\n    formatJlr(jlr) {\n      if (jlr) {\n        let filter = this.alljlrList.filter(g => g.userName === jlr);\n        if (filter.length > 0) {\n          return filter[0].nickName;\n        } else {\n          return \"\";\n        }\n      } else {\n        return \"\";\n      }\n    },\n    //导出word\n    exportWord() {\n      let params = {\n        data: this.params,\n        url: \"bzBddzczml\"\n      };\n      let fileName = \"变电倒闸操作命令记录\";\n      if (!this.selectData.length > 0) {\n        params.data = this.params;\n        exportWordByparams(params, fileName);\n      } else {\n        params.data = this.selectData;\n        exportWordByselection(params, fileName);\n      }\n    },\n    changeZslOrYbl(val) {\n      if (val) {\n        this.$set(this.form, \"tzsj\", null);\n        this.$set(this.form, \"tzr\", \"\");\n        this.$set(this.form, \"ybljlr\", \"\");\n      } else {\n        this.$set(this.form, \"xlsj\", null);\n        this.$set(this.form, \"xlr\", \"\");\n        this.$set(this.form, \"jlr\", \"\");\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.card-style {\n  background-color: #e0eff2;\n  line-height: 3vh;\n  border-radius: 3px;\n  font-weight: 800;\n  padding-left: 1vw;\n  margin-bottom: 3vh;\n}\n\n/*控制input输入框边框是否显示*/\n.elInput >>> .el-input__inner {\n  border: 0;\n}\n\n.box-card {\n  margin-bottom: 2vh !important;\n}\n</style>\n"], "sourceRoot": "src/views/dagangOilfield/czpgl/bddzcz"}]}