{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\Index\\WorkTicket.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\Index\\WorkTicket.vue", "mtime": 1755543656534}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkID0gcmVxdWlyZSgiRDovU2hhbW1wb29sL3dvcmsvY29kZS9kZ3l0LzAxXHU0RUUzXHU3ODAxL3F6LXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlV2lsZGNhcmQiKTsKCnZhciBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0ID0gcmVxdWlyZSgiRDovU2hhbW1wb29sL3dvcmsvY29kZS9kZ3l0LzAxXHU0RUUzXHU3ODAxL3F6LXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdCIpOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmZvci1lYWNoIik7CgpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMubnVtYmVyLmNvbnN0cnVjdG9yIik7CgpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5mb3ItZWFjaCIpOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Cgp2YXIgX29iamVjdFNwcmVhZDIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkQ6L1NoYW1tcG9vbC93b3JrL2NvZGUvZGd5dC8wMVx1NEVFM1x1NzgwMS9xei11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9vYmplY3RTcHJlYWQyIikpOwoKdmFyIGVjaGFydHMgPSBfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZChyZXF1aXJlKCJlY2hhcnRzIikpOwoKdmFyIF92dWV4ID0gcmVxdWlyZSgidnVleCIpOwoKdmFyIF9wdWJsaWNGdW4gPSByZXF1aXJlKCJAL2xheW91dC9taXhpbi9wdWJsaWNGdW4iKTsKCnZhciBfZ3pwZ2wgPSByZXF1aXJlKCJAL2FwaS95eGdsL2d6cGdsL2d6cGdsIik7Cgp2YXIgX2RhdGEgPSByZXF1aXJlKCJAL2FwaS9zeXN0ZW0vZGljdC9kYXRhLmpzIik7Cgp2YXIgX3J1b3lpID0gcmVxdWlyZSgiQC91dGlscy9ydW95aSIpOwoKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gewogIG5hbWU6ICdXb3JrVGlja2V0JywKICAvL+W3peWNleWujOaIkOaDheWGtQogIHByb3BzOiB7CiAgICB3b3JrVFNwYW5OdW06IHsKICAgICAgdHlwZTogTnVtYmVyLAogICAgICBkZWZhdWx0OiA3CiAgICB9LAogICAgd2tvVERpdkNsYXNzOiAnJwogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8v55So5LqO5biD5bGA5Yqo5oCB6K6+572u6auY5bqmCiAgICAgIGFjdGl2ZUNsYXNzOiAxLAogICAgICB0akNoYXJ0czogbnVsbCwKICAgICAgLy/nu5/orqHlm77lr7nosaEKICAgICAgLy/pu5jorqTlgLwKICAgICAgbm9GaW5OdW06IFtdLAogICAgICBmaW5OdW06IFtdLAogICAgICBjb250TnVtOiBbXSwKICAgICAgdmFsdWU6ICgwLCBfcnVveWkucGFyc2VUaW1lKShuZXcgRGF0ZSgpLCAne3l9JyksCiAgICAgIG9wdGlvbnM6IFtdCiAgICB9OwogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHZhciBfdGhpcyA9IHRoaXM7CgogICAgdGhpcy5nZXREYXRhKHRoaXMudmFsdWUpOwogICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIGZ1bmN0aW9uICgpIHsKICAgICAgX3RoaXMucmVsb2FkQ2hhcnRzKCk7CiAgICB9KTsgLy8gdGhpcy5nZXRZZWFycygpOy8v6I635Y+W5bm05bqm5LiL5ouJ5qGGCiAgfSwKICBtZXRob2RzOiB7CiAgICBnZXRZZWFyczogZnVuY3Rpb24gZ2V0WWVhcnMoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwoKICAgICAgKDAsIF9kYXRhLmdldERpY3RUeXBlRGF0YSkoJ3Nob3V5ZV9uZCcpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIHJlcy5kYXRhLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgIF90aGlzMi5vcHRpb25zLnB1c2goewogICAgICAgICAgICBsYWJlbDogaXRlbS5sYWJlbCwKICAgICAgICAgICAgdmFsdWU6IGl0ZW0ubnVtdmFsdWUKICAgICAgICAgIH0pOwogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICBjaGFuZ2VZZWFyOiBmdW5jdGlvbiBjaGFuZ2VZZWFyKHZhbCkgewogICAgICBpZiAoIXZhbCkgewogICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQoKICAgICAgdGhpcy5nZXREYXRhKHZhbCk7CiAgICB9LAogICAgZ2V0RGF0YTogZnVuY3Rpb24gZ2V0RGF0YSh5ZWFyKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwoKICAgICAgKDAsIF9nenBnbC5jb3VudEd6cCkoeWVhcikudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXMzLm5vRmluTnVtID0gcmVzLmRhdGFbMl07CiAgICAgICAgX3RoaXMzLmZpbk51bSA9IHJlcy5kYXRhWzFdOwogICAgICAgIF90aGlzMy5jb250TnVtID0gcmVzLmRhdGFbMF07CgogICAgICAgIF90aGlzMy5zaG93R2RDaGFydHMoKTsKICAgICAgfSk7CiAgICB9LAogICAgLy/lt6XljZXlrozmiJDmg4XlhrUKICAgIHNob3dHZENoYXJ0czogZnVuY3Rpb24gc2hvd0dkQ2hhcnRzKCkgewogICAgICB2YXIgYmFyX2R2ID0gdGhpcy4kcmVmcy5nZGNoYXJ0OwogICAgICB2YXIgbXlDaGFydCA9IGVjaGFydHMuaW5pdChiYXJfZHYpOwogICAgICB0aGlzLnRqQ2hhcnRzID0gbXlDaGFydDsKICAgICAgdmFyIG9wdGlvbjsKICAgICAgb3B0aW9uID0gewogICAgICAgIHRpdGxlOiB7CiAgICAgICAgICBzdWJ0ZXh0OiAn5Y2V5L2N77ya5LiqJywKICAgICAgICAgIGxlZnQ6ICc0JScKICAgICAgICB9LAogICAgICAgIHRvb2x0aXA6IHsKICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywKICAgICAgICAgIGF4aXNQb2ludGVyOiB7CiAgICAgICAgICAgIHR5cGU6ICdjcm9zcycsCiAgICAgICAgICAgIGNyb3NzU3R5bGU6IHsKICAgICAgICAgICAgICBjb2xvcjogJyM5OTknCiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIGxlZ2VuZDogewogICAgICAgICAgaXRlbVdpZHRoOiAoMCwgX3B1YmxpY0Z1bi5mb250U2l6ZSkoMTQpLAogICAgICAgICAgLy8g6K6+572u5Zu+5L6L5Zu+5b2i55qE5a69CiAgICAgICAgICBpdGVtSGVpZ2h0OiAoMCwgX3B1YmxpY0Z1bi5mb250U2l6ZSkoMTQpLAogICAgICAgICAgdG9wOiAnMyUnLAogICAgICAgICAgcmlnaHQ6ICc2JScsCiAgICAgICAgICB0ZXh0U3R5bGU6IHsKICAgICAgICAgICAgZm9udFNpemU6ICgwLCBfcHVibGljRnVuLmZvbnRTaXplKSgxNikKICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIGdyaWQ6IHsKICAgICAgICAgIGxlZnQ6ICc0JScsCiAgICAgICAgICByaWdodDogJzQlJywKICAgICAgICAgIGJvdHRvbTogJzElJywKICAgICAgICAgIGNvbnRhaW5MYWJlbDogdHJ1ZQogICAgICAgIH0sCiAgICAgICAgeEF4aXM6IHsKICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsCiAgICAgICAgICBkYXRhOiBbJ+WPmOeUteS4gOenjScsICflj5jnlLXkuoznp40nLCAn57q/6Lev5LiA56eNJywgJ+e6v+i3r+S6jOenjScsICfnlLXnvIbkuIDnp40nLCAn55S157yG5LqM56eNJywgJ+mFjeeUteS4gOenjScsICfphY3nlLXkuoznp40nLCAn5paw6IO95rqQ5LiA56eNJywgJ+aWsOiDvea6kOS6jOenjSddLAogICAgICAgICAgYXhpc0xhYmVsOiB7CiAgICAgICAgICAgIGludGVydmFsOiAwLAogICAgICAgICAgICByb3RhdGU6IDMwCiAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICB5QXhpczoge30sCiAgICAgICAgc2VyaWVzOiBbewogICAgICAgICAgdHlwZTogJ2JhcicsCiAgICAgICAgICBzdGFjazogJ0FkJywKICAgICAgICAgIG5hbWU6ICflrozmiJDmlbAnLAogICAgICAgICAgYmFyV2lkdGg6ICgwLCBfcHVibGljRnVuLmZvbnRTaXplKSgyNCksCiAgICAgICAgICBpdGVtU3R5bGU6IHsKICAgICAgICAgICAgY29sb3I6IG5ldyBlY2hhcnRzLmdyYXBoaWMuTGluZWFyR3JhZGllbnQoMCwgMCwgMCwgMSwgW3sKICAgICAgICAgICAgICBvZmZzZXQ6IDAsCiAgICAgICAgICAgICAgY29sb3I6ICcjMDBENkIxJwogICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgb2Zmc2V0OiAxLAogICAgICAgICAgICAgIGNvbG9yOiAnIzAwRDZCMScKICAgICAgICAgICAgfV0pCiAgICAgICAgICB9LAogICAgICAgICAgZGF0YTogdGhpcy5maW5OdW0KICAgICAgICB9LCB7CiAgICAgICAgICB0eXBlOiAnYmFyJywKICAgICAgICAgIHN0YWNrOiAnQWQnLAogICAgICAgICAgbmFtZTogJ+aJp+ihjOaVsCcsCiAgICAgICAgICBiYXJXaWR0aDogKDAsIF9wdWJsaWNGdW4uZm9udFNpemUpKDI0KSwKICAgICAgICAgIGl0ZW1TdHlsZTogewogICAgICAgICAgICBjb2xvcjogbmV3IGVjaGFydHMuZ3JhcGhpYy5MaW5lYXJHcmFkaWVudCgwLCAwLCAwLCAxLCBbewogICAgICAgICAgICAgIG9mZnNldDogMCwKICAgICAgICAgICAgICBjb2xvcjogJyM3NmVlZDInCiAgICAgICAgICAgIH0sIHsKICAgICAgICAgICAgICBvZmZzZXQ6IDEsCiAgICAgICAgICAgICAgY29sb3I6ICcjNzZlZWQyJwogICAgICAgICAgICB9XSkKICAgICAgICAgIH0sCiAgICAgICAgICBkYXRhOiB0aGlzLm5vRmluTnVtCiAgICAgICAgfSwgewogICAgICAgICAgdHlwZTogJ2xpbmUnLAogICAgICAgICAgbmFtZTogJ+W8gOelqOaAu+aVsCcsCiAgICAgICAgICBiYXJXaWR0aDogKDAsIF9wdWJsaWNGdW4uZm9udFNpemUpKDI0KSwKICAgICAgICAgIGl0ZW1TdHlsZTogewogICAgICAgICAgICBub3JtYWw6IHsKICAgICAgICAgICAgICBsYWJlbDogewogICAgICAgICAgICAgICAgc2hvdzogdHJ1ZQogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgY29sb3I6ICcjMDBDOTk0JwogICAgICAgICAgICB9CiAgICAgICAgICB9LAogICAgICAgICAgZGF0YTogdGhpcy5jb250TnVtCiAgICAgICAgfV0KICAgICAgfTsKICAgICAgb3B0aW9uICYmIG15Q2hhcnQuc2V0T3B0aW9uKG9wdGlvbik7CiAgICB9LAogICAgLy/ph43mlrDliqDovb1lQ2hhcnRz5Zu+6KGoCiAgICByZWxvYWRDaGFydHM6IGZ1bmN0aW9uIHJlbG9hZENoYXJ0cygpIHsKICAgICAgdGhpcy50akNoYXJ0cy5yZXNpemUoKTsKICAgIH0KICB9LAogIGNvbXB1dGVkOiAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCAoMCwgX3Z1ZXgubWFwU3RhdGUpKFsic2V0dGluZ3MiLCAiYXBwIl0pKSwge30sIHsKICAgIC8v5bel5L2c56Wo5a6M5oiQ5oOF5Ya1CiAgICB3b3JrT3JkZXI6IGZ1bmN0aW9uIHdvcmtPcmRlcigpIHsKICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLnNldHRpbmdzLndvcmtPcmRlcjsKICAgIH0sCiAgICAvL+iPnOWNleS8uOe8qeeKtuaAgQogICAgb3BlbmVkOiBmdW5jdGlvbiBvcGVuZWQoKSB7CiAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5zdGF0ZS5hcHAuc2lkZWJhci5vcGVuZWQ7CiAgICB9CiAgfSksCiAgd2F0Y2g6IHsKICAgIHdvcmtPcmRlcjogZnVuY3Rpb24gd29ya09yZGVyKG5ld1ZhbCkgewogICAgICBpZiAobmV3VmFsKSB7CiAgICAgICAgdGhpcy5yZWxvYWRDaGFydHMoKTsKICAgICAgfQogICAgfSwKICAgIHdrb1NwYW5OdW06IGZ1bmN0aW9uIHdrb1NwYW5OdW0obmV3VmFsKSB7CiAgICAgIHRoaXMucmVsb2FkQ2hhcnRzKCk7CiAgICB9LAogICAgb3BlbmVkOiBmdW5jdGlvbiBvcGVuZWQobmV3VmFsKSB7Ly/ph43mlrDliqDovb3nu5/orqHlm74KCiAgICAgIC8qc2V0VGltZW91dCgoKT0+ewogICAgICAgIHRoaXMudGpDaGFydHMucmVzaXplKCk7CiAgICAgIH0sMjAwKSovCiAgICB9CiAgfQp9OwpleHBvcnRzLmRlZmF1bHQgPSBfZGVmYXVsdDs="}, {"version": 3, "sources": ["WorkTicket.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAsBA;;AACA;;AACA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,YADA;AACA;AACA,EAAA,KAAA,EAAA;AACA,IAAA,YAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAAA,EAAA;AAFA,KADA;AAKA,IAAA,YAAA,EAAA;AALA,GAFA;AASA,EAAA,IATA,kBASA;AACA,WAAA;AACA;AACA,MAAA,WAAA,EAAA,CAFA;AAGA,MAAA,QAAA,EAAA,IAHA;AAGA;AACA;AACA,MAAA,QAAA,EAAA,EALA;AAMA,MAAA,MAAA,EAAA,EANA;AAOA,MAAA,OAAA,EAAA,EAPA;AAQA,MAAA,KAAA,EAAA,sBAAA,IAAA,IAAA,EAAA,EAAA,KAAA,CARA;AASA,MAAA,OAAA,EAAA;AATA,KAAA;AAWA,GArBA;AAsBA,EAAA,OAtBA,qBAsBA;AAAA;;AACA,SAAA,OAAA,CAAA,KAAA,KAAA;AACA,IAAA,MAAA,CAAA,gBAAA,CAAA,QAAA,EAAA,YAAA;AACA,MAAA,KAAA,CAAA,YAAA;AACA,KAFA,EAFA,CAKA;AACA,GA5BA;AA6BA,EAAA,OAAA,EAAA;AACA,IAAA,QADA,sBACA;AAAA;;AACA,iCAAA,WAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA,KAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA;AAAA,WAAA;AACA,SAFA;AAGA,OAJA;AAKA,KAPA;AAQA,IAAA,UARA,sBAQA,GARA,EAQA;AACA,UAAA,CAAA,GAAA,EAAA;AACA,eAAA,KAAA;AACA;;AACA,WAAA,OAAA,CAAA,GAAA;AACA,KAbA;AAcA,IAAA,OAdA,mBAcA,IAdA,EAcA;AAAA;;AACA,2BAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AACA,QAAA,MAAA,CAAA,MAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA;;AACA,QAAA,MAAA,CAAA,YAAA;AACA,OALA;AAMA,KArBA;AAsBA;AACA,IAAA,YAvBA,0BAuBA;AACA,UAAA,MAAA,GAAA,KAAA,KAAA,CAAA,OAAA;AACA,UAAA,OAAA,GAAA,OAAA,CAAA,IAAA,CAAA,MAAA,CAAA;AACA,WAAA,QAAA,GAAA,OAAA;AAEA,UAAA,MAAA;AACA,MAAA,MAAA,GAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,OAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA;AAFA,SADA;AAKA,QAAA,OAAA,EAAA;AACA,UAAA,OAAA,EAAA,MADA;AAEA,UAAA,WAAA,EAAA;AACA,YAAA,IAAA,EAAA,OADA;AAEA,YAAA,UAAA,EAAA;AACA,cAAA,KAAA,EAAA;AADA;AAFA;AAFA,SALA;AAcA,QAAA,MAAA,EAAA;AACA,UAAA,SAAA,EAAA,yBAAA,EAAA,CADA;AACA;AACA,UAAA,UAAA,EAAA,yBAAA,EAAA,CAFA;AAGA,UAAA,GAAA,EAAA,IAHA;AAIA,UAAA,KAAA,EAAA,IAJA;AAKA,UAAA,SAAA,EAAA;AACA,YAAA,QAAA,EAAA,yBAAA,EAAA;AADA;AALA,SAdA;AAuBA,QAAA,IAAA,EAAA;AACA,UAAA,IAAA,EAAA,IADA;AAEA,UAAA,KAAA,EAAA,IAFA;AAGA,UAAA,MAAA,EAAA,IAHA;AAIA,UAAA,YAAA,EAAA;AAJA,SAvBA;AA6BA,QAAA,KAAA,EAAA;AACA,UAAA,IAAA,EAAA,UADA;AAEA,UAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,OAAA,EAAA,OAAA,CAFA;AAGA,UAAA,SAAA,EAAA;AAAA,YAAA,QAAA,EAAA,CAAA;AAAA,YAAA,MAAA,EAAA;AAAA;AAHA,SA7BA;AAkCA,QAAA,KAAA,EAAA,EAlCA;AAmCA,QAAA,MAAA,EAAA,CACA;AACA,UAAA,IAAA,EAAA,KADA;AAEA,UAAA,KAAA,EAAA,IAFA;AAGA,UAAA,IAAA,EAAA,KAHA;AAIA,UAAA,QAAA,EAAA,yBAAA,EAAA,CAJA;AAKA,UAAA,SAAA,EAAA;AACA,YAAA,KAAA,EAAA,IAAA,OAAA,CAAA,OAAA,CAAA,cAAA,CACA,CADA,EACA,CADA,EACA,CADA,EACA,CADA,EAEA,CACA;AAAA,cAAA,MAAA,EAAA,CAAA;AAAA,cAAA,KAAA,EAAA;AAAA,aADA,EAEA;AAAA,cAAA,MAAA,EAAA,CAAA;AAAA,cAAA,KAAA,EAAA;AAAA,aAFA,CAFA;AADA,WALA;AAcA,UAAA,IAAA,EAAA,KAAA;AAdA,SADA,EAiBA;AACA,UAAA,IAAA,EAAA,KADA;AAEA,UAAA,KAAA,EAAA,IAFA;AAGA,UAAA,IAAA,EAAA,KAHA;AAIA,UAAA,QAAA,EAAA,yBAAA,EAAA,CAJA;AAKA,UAAA,SAAA,EAAA;AACA,YAAA,KAAA,EAAA,IAAA,OAAA,CAAA,OAAA,CAAA,cAAA,CACA,CADA,EACA,CADA,EACA,CADA,EACA,CADA,EAEA,CACA;AAAA,cAAA,MAAA,EAAA,CAAA;AAAA,cAAA,KAAA,EAAA;AAAA,aADA,EAEA;AAAA,cAAA,MAAA,EAAA,CAAA;AAAA,cAAA,KAAA,EAAA;AAAA,aAFA,CAFA;AADA,WALA;AAcA,UAAA,IAAA,EAAA,KAAA;AAdA,SAjBA,EAiCA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,QAAA,EAAA,yBAAA,EAAA,CAHA;AAIA,UAAA,SAAA,EAAA;AACA,YAAA,MAAA,EAAA;AAAA,cAAA,KAAA,EAAA;AAAA,gBAAA,IAAA,EAAA;AAAA,eAAA;AAAA,cAAA,KAAA,EAAA;AAAA;AADA,WAJA;AAOA,UAAA,IAAA,EAAA,KAAA;AAPA,SAjCA;AAnCA,OAAA;AA+EA,MAAA,MAAA,IAAA,OAAA,CAAA,SAAA,CAAA,MAAA,CAAA;AACA,KA7GA;AA8GA;AACA,IAAA,YA/GA,0BA+GA;AACA,WAAA,QAAA,CAAA,MAAA;AACA;AAjHA,GA7BA;AAgJA,EAAA,QAAA,8DACA,oBAAA,CAAA,UAAA,EAAA,KAAA,CAAA,CADA;AAEA;AACA,IAAA,SAHA,uBAGA;AACA,aAAA,KAAA,MAAA,CAAA,KAAA,CAAA,QAAA,CAAA,SAAA;AACA,KALA;AAMA;AACA,IAAA,MAPA,oBAOA;AACA,aAAA,KAAA,MAAA,CAAA,KAAA,CAAA,GAAA,CAAA,OAAA,CAAA,MAAA;AACA;AATA,IAhJA;AA2JA,EAAA,KAAA,EAAA;AACA,IAAA,SADA,qBACA,MADA,EACA;AACA,UAAA,MAAA,EAAA;AACA,aAAA,YAAA;AACA;AACA,KALA;AAMA,IAAA,UANA,sBAMA,MANA,EAMA;AACA,WAAA,YAAA;AACA,KARA;AASA,IAAA,MATA,kBASA,MATA,EASA,CACA;;AACA;;;AAGA;AAdA;AA3JA,C", "sourcesContent": ["<template>\n  <div :workTSpanNum=\"workTSpanNum\" class=\"borderCls\" :class=\"wkoTDivClass\">\n    <div>\n      <div class=\"txtTitle\">\n        <span class=\"txtContent\">工作票</span>\n        <!-- <el-select  v-model=\"value\" class=\"selectBtn\" @change=\"changeYear\">\n          <el-option\n            v-for=\"item in options\"\n            :key=\"item.value\"\n            :label=\"item.label\"\n            :value=\"item.value\">\n          </el-option>\n        </el-select> -->\n        <el-date-picker type=\"year\" value-format=\"yyyy\" v-model=\"value\" class=\"selectBtn\" @change=\"changeYear\"/>\n      </div>\n      <div  ref=\"gdchart\" class=\"tjHeight\">\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nimport { mapState } from 'vuex'\nimport { fontSize} from '@/layout/mixin/publicFun'\nimport {countGzp} from \"@/api/yxgl/gzpgl/gzpgl\";\nimport {getDictTypeData} from '@/api/system/dict/data.js'\nimport { parseTime } from '@/utils/ruoyi'\n\nexport default {\n  name: 'WorkTicket',//工单完成情况\n  props:{\n    workTSpanNum:{\n      type:Number,\n      default:7,\n    },\n    wkoTDivClass:'',\n  },\n  data() {\n    return {\n      //用于布局动态设置高度\n      activeClass:1,\n      tjCharts:null,//统计图对象\n      //默认值\n      noFinNum:[],\n      finNum:[],\n      contNum:[],\n      value: parseTime(new Date(),'{y}'),\n      options: [],\n    }\n  },\n  mounted() {\n   this.getData(this.value)\n   window.addEventListener('resize',()=>{\n      this.reloadCharts();\n    })\n    // this.getYears();//获取年度下拉框\n  },\n  methods: {\n    getYears(){\n      getDictTypeData('shouye_nd').then(res=>{\n        res.data.forEach(item=>{\n          this.options.push({label:item.label,value:item.numvalue})\n        })\n      })\n    },\n    changeYear(val){\n      if(!val){\n        return false;\n      }\n      this.getData(val)\n    },\n    getData(year){\n      countGzp(year).then(res => {\n        this.noFinNum = res.data[2];\n        this.finNum = res.data[1];\n        this.contNum = res.data[0];\n        this.showGdCharts();\n      })\n    },\n    //工单完成情况\n    showGdCharts(){\n      let bar_dv = this.$refs.gdchart;\n      let myChart = echarts.init(bar_dv);\n      this.tjCharts = myChart;\n\n      let option;\n      option = {\n        title: {\n          subtext: '单位：个',\n          left: '4%'\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'cross',\n            crossStyle: {\n              color: '#999'\n            }\n          }\n        },\n        legend: {\n          itemWidth: fontSize(14),// 设置图例图形的宽\n          itemHeight: fontSize(14),\n          top:'3%',\n          right: '6%',\n          textStyle:{\n            fontSize:fontSize(16),\n          }\n        },\n        grid: {\n          left: '4%',\n          right: '4%',\n          bottom: '1%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'category',\n          data:['变电一种', '变电二种', '线路一种', '线路二种', '电缆一种', '电缆二种', '配电一种','配电二种','新能源一种','新能源二种'],\n          axisLabel:{interval: 0,rotate:30}\n        },\n        yAxis: {},\n        series: [\n          {\n            type: 'bar' ,\n            stack: 'Ad',\n            name:'完成数',\n            barWidth:fontSize(24),\n            itemStyle: {\n              color: new echarts.graphic.LinearGradient(\n                0, 0, 0, 1,\n                [\n                  {offset: 0, color: '#00D6B1'},\n                  {offset: 1, color: '#00D6B1'}\n                ]\n              )\n            },\n            data:this.finNum,\n          },\n          {\n            type: 'bar',\n            stack: 'Ad',\n            name:'执行数',\n            barWidth:fontSize(24),\n            itemStyle: {\n              color: new echarts.graphic.LinearGradient(\n                0, 0, 0, 1,\n                [\n                  {offset: 0, color: '#76eed2'},\n                  {offset: 1, color: '#76eed2'}\n                ]\n              )\n            },\n            data:this.noFinNum\n          },\n          {\n            type: 'line' ,\n            name:'开票总数',\n            barWidth:fontSize(24),\n            itemStyle: {\n               normal: {label : {show: true},color: '#00C994',}\n            },\n            data:this.contNum,\n          },\n        ]\n      };\n      option && myChart.setOption(option);\n    },\n    //重新加载eCharts图表\n    reloadCharts(){\n      this.tjCharts.resize();\n    },\n  },\n  computed: {\n    ...mapState([\"settings\",\"app\"]),\n    //工作票完成情况\n    workOrder() {\n      return this.$store.state.settings.workOrder;\n    },\n    //菜单伸缩状态\n    opened() {\n      return this.$store.state.app.sidebar.opened;\n    },\n  },\n  watch:{\n    workOrder(newVal){\n      if(newVal){\n        this.reloadCharts();\n      }\n    },\n    wkoSpanNum(newVal){\n      this.reloadCharts();\n    },\n    opened(newVal) {\n      //重新加载统计图\n      /*setTimeout(()=>{\n        this.tjCharts.resize();\n      },200)*/\n    }\n  }\n}\n</script>\n<style>\n.spanTxt{\n  background: #fff;\n  height: 35px;\n  line-height: 35px;\n  margin-top: 6px;\n  margin-right: 9px;\n  padding-left: 12px;\n}\n.selectBtn{\n  font-size: 10kV然气站变电站4px;\n}\n</style>\n\n"], "sourceRoot": "src/components/Index"}]}