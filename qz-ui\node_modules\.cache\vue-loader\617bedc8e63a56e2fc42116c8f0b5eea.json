{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sbbgsq.vue?vue&type=template&id=a16637a6&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sbbgsq.vue", "mtime": 1706897324964}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}