{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\todoitem\\done.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\todoitem\\done.vue", "mtime": 1706897322085}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGxpc3REb25lIH0gZnJvbSAiQC9hcGkvYWN0aXZpdGkvdG9kb2l0ZW0iOwppbXBvcnQgQWN0aXZpdGlIaXN0b3J5IGZyb20gIi4vaGlzdG9yeSI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiRG9uZSIsCiAgY29tcG9uZW50czoge0FjdGl2aXRpSGlzdG9yeX0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGluc3RhbmNlSWQ6bnVsbCwKICAgICAgb3Blbkhpc3Rvcnk6ZmFsc2UsCiAgICAgIG9wZW5Mb2FkaW5nSW1nOmZhbHNlLAogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIHRpdGxlOiflrqHmibknLAogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgKICAgICAgc2luZ2xlOiB0cnVlLAogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgKICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tgogICAgICBzaG93U2VhcmNoOiB0cnVlLAogICAgICBkYXRlUmFuZ2U6IFtdLAogICAgICBkYXRlUmFuZ2UxOltdLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOeUqOaIt+ihqOagvOaVsOaNrgogICAgICB0b2RvTGlzdDogbnVsbCwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDpg6jpl6jlkI3np7AKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHt9LAogICAgICAvLyDmn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBpdGVtTmFtZTogdW5kZWZpbmVkLAogICAgICAgIG1vZHVsZTogdW5kZWZpbmVkLAogICAgICAgIHRhc2tOYW1lOnVuZGVmaW5lZCwKICAgICAgICB0b2RvVXNlck5hbWU6dW5kZWZpbmVkLAogICAgICAgIGhhbmRsZVVzZXJOYW1lOnVuZGVmaW5lZCwKICAgICAgfSwKICAgICAgLy8g6KGo5Y2V5qCh6aqMCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgcGFzczogWwogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5a6h5om55oSP6KeB5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIifSwKICAgICAgICBdLAogICAgICAgIG5hbWU6IFsKICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWQjeensOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIn0sCiAgICAgICAgXSwKICAgICAgfSwKICAgIH07CiAgfSwKICBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i55So5oi35YiX6KGoICovCiAgICBnZXRMaXN0KCkgewogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBsaXN0RG9uZSh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKAogICAgICAgIChyZXNwb25zZSkgPT4gewogICAgICAgICAgdGhpcy50b2RvTGlzdCA9IHJlc3BvbnNlLmRhdGEucmVjb3JkczsKICAgICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS5kYXRhLnRvdGFsOwogICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgfQogICAgICApOwogICAgfSwKICAgIC8qKgogICAgICog5Y+W5raI5oyJ6ZKuCiAgICAgKiAqLwogICAgY2FuY2VsKCkgewogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsKICAgICAgdGhpcy5yZXNldCgpOwogICAgfSwKICAgIC8qKgogICAgICog6KGo5Y2V6YeN572uCiAgICAgKiAqLwogICAgcmVzZXQoKSB7CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICBwYXNzOiB1bmRlZmluZWQsCiAgICAgICAgbmFtZTogdW5kZWZpbmVkLAogICAgICAgIGRlc2NyaXB0aW9uOnVuZGVmaW5lZAogICAgICB9OwogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovCiAgICByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLmRhdGVSYW5nZSA9IFtdOwogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICAvKioq5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uKioqLwogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoKGl0ZW0pID0+IGl0ZW0udXNlcklkKTsKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9IDE7CiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsKICAgIH0sCiAgICAvKiog5Yqe55CG5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVDb21wbGV0ZShyb3cpIHsKICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgbGV0IGlkcyA9IHRoaXMuaWRzOwogICAgICBpZihpZHMubGVuZ3RoIT0xKXsKICAgICAgICB0aGlzLm1zZ1dhcm5pbmcoIuivt+mAieaLqeS4gOadoeaVsOaNriIpOwogICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQogICAgfSwKICAgIC8qKiDmj5DkuqTmjInpkq4gKi8KICAgIHN1Ym1pdEZvcm06IGZ1bmN0aW9uICgpIHsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKCh2YWxpZCkgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgc2F2ZSh0aGlzLmZvcm0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7CiAgICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAnMDAwMCcpIHsKICAgICAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOwogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgICAgICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHtwYXRoOicvYWN0aXZpdGkvb25saW5lbW9kZWxlci8nK3Jlc3BvbnNlLmRhdGF9KTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog55Sz6K+35oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVBcHBseUluZm8ocm93KSB7CgogICAgfSwKICAgIC8qKiogIOWuoeaJueWOhuWPsuaWh+S7tiAqKiovCiAgICBoYW5kbGVIaXN0b3J5KHJvdyl7CiAgICAgIHRoaXMub3Blbkhpc3RvcnkgPSB0cnVlCiAgICAgIHRoaXMuaW5zdGFuY2VJZCA9IHJvdy5pbnN0YW5jZUlkCiAgICB9LAogICAgLyoqIOWuoeaJueWOhuWPsuehruWumiAgKiovCiAgICBzdWJtaXRIaXN0b3J5KCl7CiAgICAgIHRoaXMub3Blbkhpc3RvcnkgPSBmYWxzZQogICAgfSwKICAgIC8qKioqIOWFs+mXreWuoeaJueWOhuWPsiAgKioqKi8KICAgIGNhbmNlbEhpc3RvcnkoKXsKICAgICAgdGhpcy5vcGVuSGlzdG9yeSA9IGZhbHNlCiAgICB9LAogICAgLyoqIOWnlOaJmCAqKi8KICAgIGhhbmRsZURlbGVnYXRpb24ocm93KXsKICAgICAgZGVwbG95KHJvdy5pZCkudGhlbihyZXMgPT57CiAgICAgICAgaWYocmVzLmNvZGU9PScwMDAwJyl7CiAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MocmVzLm1zZyk7CiAgICAgICAgfWVsc2V7CiAgICAgICAgICB0aGlzLm1zZ0Vycm9yKHJlcy5tc2cpOwogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICAvKioqKiDov5vluqbmn6XnnIsgICoqKiovCiAgICBoYW5kbGVQbGFuKHJvdyl7CiAgICAgIHRoaXMub3BlbkxvYWRpbmdJbWcgPSB0cnVlCiAgICAgIHRoaXMuaW1nU3JjID0iL2FjdGl2aXRpLWFwaS9wcm9jZXNzL3JlYWQtcmVzb3VyY2U/aW5zdGFuY2VJZD0iK3Jvdy5pbnN0YW5jZUlkKyImdD0iK25ldyBEYXRlKCkuZ2V0VGltZSgpOwogICAgfSwKICAgIC8qKiDmmK/lkKbmn6XnnIsgKiovCiAgICBoYXNMb29rT3Zlcihyb3cpewogICAgICByZXR1cm4gcm93LmlzVmlldz09JzAnPyflkKYnOnJvdy5pc1ZpZXc9JzEnPyfmmK8nOictJzsKICAgIH0sCiAgICAvKiog5piv5ZCm5aSE55CGICoqLwogICAgaGFzSGFuZGxlcihyb3cpewogICAgICByZXR1cm4gcm93LmlzVmlldz09JzAnPyflkKYnOnJvdy5pc1ZpZXc9JzEnPyfmmK8nOictJzsKICAgIH0sCiAgICBjYW5jZWxMb2FkaW5nSW1nKCl7CiAgICAgIHRoaXMub3BlbkxvYWRpbmdJbWcgPSBmYWxzZTsKICAgIH0KICB9LAp9Owo="}, {"version": 3, "sources": ["done.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "done.vue", "sourceRoot": "src/views/activiti/todoitem", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form class=\"searchForm1\" :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" >\n      <el-row>\n        <el-col :span=\"6\">\n          <el-form-item label=\"事项标题：\" prop=\"itemName\">\n            <el-input v-model=\"queryParams.itemName\" placeholder=\"请输入事项标题\" clearable\n                      @keyup.enter.native=\"handleQuery\" />\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-form-item label=\"任务名称：\" prop=\"taskName\">\n            <el-input v-model=\"queryParams.taskName\" placeholder=\"请输入待办任务名称\" clearable\n                      @keyup.enter.native=\"handleQuery\"/>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-form-item label=\"待办人：\" prop=\"todoUserName\">\n            <el-input v-model=\"queryParams.todoUserName\" placeholder=\"请输入待办人\" clearable\n                      @keyup.enter.native=\"handleQuery\"/>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-form-item label=\"处理人名称：\" prop=\"handleUserName\">\n            <el-input v-model=\"queryParams.handleUserName\" placeholder=\"请输入处理人名称\" clearable c\n                      @keyup.enter.native=\"handleQuery\"/>\n          </el-form-item>\n        </el-col>\n      </el-row>\n      <el-row>\n        <el-col :span=\"12\">\n          <el-form-item label=\"通知时间:\">\n            <el-date-picker\n              unlink-panels\n              v-model=\"dateRange\"\n              value-format=\"yyyy-MM-dd\"\n              type=\"daterange\"\n              range-separator=\"-\"\n              start-placeholder=\"开始日期\"\n              end-placeholder=\"结束日期\"\n            ></el-date-picker>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item label=\"处理时间:\">\n            <el-date-picker\n              unlink-panels\n              v-model=\"dateRange1\"\n              value-format=\"yyyy-MM-dd\"\n              type=\"daterange\"\n              range-separator=\"-\"\n              start-placeholder=\"开始日期\"\n              end-placeholder=\"结束日期\"\n            ></el-date-picker>\n          </el-form-item>\n        </el-col>\n      </el-row>\n      <el-form-item>\n        <el-button type=\"cyan\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\" type=\"warning\">重置</el-button>\n      </el-form-item>\n    </el-form>\n    <el-row :gutter=\"20\">\n      <el-col :span=\"24\">\n        <el-white class=\"button-group\">\n          <el-table style=\"margin-top: 15px;\" stripe border v-loading=\"loading\" :data=\"todoList\" @selection-change=\"handleSelectionChange\">\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n            <el-table-column label=\"事项标题\" align=\"center\" prop=\"itemName\" width=\"160px\" fixed >\n              <template slot-scope=\"scope\">\n                <router-link   :to=\"{name:'报销管理' ,params:{appId:scope.row.businessId}}\"  class=\"link-type\" v-if=\"scope.row.module=='repayment'\">\n                  <span>{{ scope.row.itemName }}</span>\n                </router-link>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"申请人名称\" align=\"center\" prop=\"applyUserName\"     />\n            <el-table-column label=\"待办人名称\" align=\"center\" prop=\"todoUserName\"    />\n            <el-table-column label=\"处理人名称\" align=\"center\" prop=\"handleUserName\"    />\n            <el-table-column label=\"创建时间\" align=\"center\" prop=\"todoTime\" width=\"160px\"  />\n            <el-table-column label=\"处理时间\" align=\"center\" prop=\"handleTime\" width=\"160px\"  />\n            <el-table-column label=\"待办任务名称\" align=\"center\" prop=\"nodeName\"  width=\"160px\" />\n            <el-table-column label=\"操作\" align=\"center\"   class-name=\"small-padding fixed-width\" width=\"160px\" fixed=\"right\">\n              <template slot-scope=\"scope\">\n                <el-button size=\"mini\" type=\"text\"   @click=\"handleHistory(scope.row)\" >审批历史</el-button>\n                <el-button size=\"mini\" type=\"text\"   @click=\"handlePlan(scope.row)\" >流程查看</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n\n          <pagination v-show=\"total>0\"\n                      :total=\"total\"\n                      :page.sync=\"queryParams.pageNum\"\n                      :limit.sync=\"queryParams.pageSize\"\n                      @pagination=\"getList\"\n          />\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!-- 添加或修改参数配置对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body :close-on-click-modal=false v-dialogDrag>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n        <el-form-item label=\"申请人：\" prop=\"name\">\n          <el-input v-model=\"form.name\" placeholder=\"请输入名称\" maxlength=\"30\"/>\n        </el-form-item>\n        <el-form-item label=\"审批意见：\" prop=\"pass\">\n          <el-select v-model=\"form.pass\" placeholder=\"请选择\">\n            <el-option label=\"同意\" :value=\"true\" />\n            <el-option label=\"拒绝\" :value=\"false\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"批注：\" prop=\"description\">\n          <el-input v-model=\"form.description\" type=\"textarea\" placeholder=\"请输入内容\"></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n\n    <el-dialog title=\"审批历史\" :visible.sync=\"openHistory\" width=\"800px\" append-to-body :close-on-click-modal=false v-dialogDrag>\n      <slot v-if=\"instanceId!=null\">\n        <activiti-history :instance-id=\"instanceId\" />\n      </slot>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitHistory\">确 定</el-button>\n        <el-button @click=\"cancelHistory\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n\n    <el-dialog title=\"审批进度\" :visible.sync=\"openLoadingImg\" width=\"800px\" append-to-body :close-on-click-modal=false v-dialogDrag>\n      <img :src=\"imgSrc\"/>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"cancelLoadingImg\">关 闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  import { listDone } from \"@/api/activiti/todoitem\";\n  import ActivitiHistory from \"./history\";\n  export default {\n    name: \"Done\",\n    components: {ActivitiHistory},\n    data() {\n      return {\n        instanceId:null,\n        openHistory:false,\n        openLoadingImg:false,\n        // 遮罩层\n        loading: true,\n        // 选中数组\n        ids: [],\n        title:'审批',\n        // 非单个禁用\n        single: true,\n        // 非多个禁用\n        multiple: true,\n        // 显示搜索条件\n        showSearch: true,\n        dateRange: [],\n        dateRange1:[],\n        // 总条数\n        total: 0,\n        // 用户表格数据\n        todoList: null,\n        // 是否显示弹出层\n        open: false,\n        // 部门名称\n        // 表单参数\n        form: {},\n        // 查询参数\n        queryParams: {\n          pageNum: 1,\n          pageSize: 10,\n          itemName: undefined,\n          module: undefined,\n          taskName:undefined,\n          todoUserName:undefined,\n          handleUserName:undefined,\n        },\n        // 表单校验\n        rules: {\n          pass: [\n            {required: true, message: \"审批意见不能为空\", trigger: \"blur\"},\n          ],\n          name: [\n            {required: true, message: \"名称不能为空\", trigger: \"blur\"},\n          ],\n        },\n      };\n    },\n    created() {\n      this.getList();\n    },\n    methods: {\n      /** 查询用户列表 */\n      getList() {\n        this.loading = true;\n        listDone(this.queryParams).then(\n          (response) => {\n            this.todoList = response.data.records;\n            this.total = response.data.total;\n            this.loading = false;\n          }\n        );\n      },\n      /**\n       * 取消按钮\n       * */\n      cancel() {\n        this.open = false;\n        this.reset();\n      },\n      /**\n       * 表单重置\n       * */\n      reset() {\n        this.form = {\n          pass: undefined,\n          name: undefined,\n          description:undefined\n        };\n        this.resetForm(\"form\");\n      },\n      /** 搜索按钮操作 */\n      handleQuery() {\n        this.queryParams.pageNum = 1;\n        this.getList();\n      },\n      /** 重置按钮操作 */\n      resetQuery() {\n        this.dateRange = [];\n        this.resetForm(\"queryForm\");\n        this.handleQuery();\n      },\n      /***多选框选中数据***/\n      handleSelectionChange(selection) {\n        this.ids = selection.map((item) => item.userId);\n        this.single = selection.length != 1;\n        this.multiple = !selection.length;\n      },\n      /** 办理按钮操作 */\n      handleComplete(row) {\n        this.open = true;\n        let ids = this.ids;\n        if(ids.length!=1){\n          this.msgWarning(\"请选择一条数据\");\n          return false;\n        }\n      },\n      /** 提交按钮 */\n      submitForm: function () {\n        this.$refs[\"form\"].validate((valid) => {\n          if (valid) {\n            save(this.form).then((response) => {\n              if (response.code === '0000') {\n                this.msgSuccess(\"新增成功\");\n                this.open = false;\n                this.getList();\n                this.$router.push({path:'/activiti/onlinemodeler/'+response.data});\n              }\n            });\n          }\n        });\n      },\n      /** 申请按钮操作 */\n      handleApplyInfo(row) {\n\n      },\n      /***  审批历史文件 ***/\n      handleHistory(row){\n        this.openHistory = true\n        this.instanceId = row.instanceId\n      },\n      /** 审批历史确定  **/\n      submitHistory(){\n        this.openHistory = false\n      },\n      /**** 关闭审批历史  ****/\n      cancelHistory(){\n        this.openHistory = false\n      },\n      /** 委托 **/\n      handleDelegation(row){\n        deploy(row.id).then(res =>{\n          if(res.code=='0000'){\n            this.msgSuccess(res.msg);\n          }else{\n            this.msgError(res.msg);\n          }\n        })\n      },\n      /**** 进度查看  ****/\n      handlePlan(row){\n        this.openLoadingImg = true\n        this.imgSrc =\"/activiti-api/process/read-resource?instanceId=\"+row.instanceId+\"&t=\"+new Date().getTime();\n      },\n      /** 是否查看 **/\n      hasLookOver(row){\n        return row.isView=='0'?'否':row.isView='1'?'是':'-';\n      },\n      /** 是否处理 **/\n      hasHandler(row){\n        return row.isView=='0'?'否':row.isView='1'?'是':'-';\n      },\n      cancelLoadingImg(){\n        this.openLoadingImg = false;\n      }\n    },\n  };\n</script>\n"]}]}