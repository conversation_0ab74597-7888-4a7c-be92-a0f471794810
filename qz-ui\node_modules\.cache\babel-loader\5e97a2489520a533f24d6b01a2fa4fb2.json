{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\jxbzk\\jxxmwh.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\jxbzk\\jxxmwh.js", "mtime": 1706897313923}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0TGlzdCA9IGdldExpc3Q7CmV4cG9ydHMuc2F2ZU9yVXBkYXRlID0gc2F2ZU9yVXBkYXRlOwpleHBvcnRzLnJlbW92ZSA9IHJlbW92ZTsKZXhwb3J0cy5leHBvcnRFeGNlbCA9IGV4cG9ydEV4Y2VsOwoKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Cgp2YXIgYmFzZVVybCA9ICIvbWFuYWdlci1hcGkiOyAvLyDmn6Xor6LkuJPkuJrnpajmk43kvZzmnK/or63ooagKCmZ1bmN0aW9uIGdldExpc3QocGFyYW1zKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcvYnpqeHhtL3BhZ2UnLCBwYXJhbXMsIDEpOwp9IC8vIOa3u+WKoOaIluS/ruaUuQoKCmZ1bmN0aW9uIHNhdmVPclVwZGF0ZShwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy9iemp4eG0vc2F2ZU9yVXBkYXRlJywgcGFyYW1zLCAxKTsKfSAvLyDmt7vliqDmiJbkv67mlLkKCgpmdW5jdGlvbiByZW1vdmUocGFyYW1zKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcvYnpqeHhtL3JlbW92ZScsIHBhcmFtcywgMSk7Cn0gLy8g5a+85Ye6CgoKZnVuY3Rpb24gZXhwb3J0RXhjZWwocGFyYW1zKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcvYnpqeHhtL2V4cG9ydCcsIHBhcmFtcywgMSk7Cn0="}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagang<PERSON>field/bzgl/jxbzk/jxxmwh.js"], "names": ["baseUrl", "getList", "params", "api", "requestPost", "saveOrUpdate", "remove", "exportExcel"], "mappings": ";;;;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,cAAhB,C,CAEA;;AACO,SAASC,OAAT,CAAiBC,MAAjB,EAAyB;AAC9B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,cAAxB,EAAuCE,MAAvC,EAA8C,CAA9C,CAAP;AACD,C,CAED;;;AACO,SAASG,YAAT,CAAsBH,MAAtB,EAA8B;AACnC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,sBAAxB,EAA+CE,MAA/C,EAAsD,CAAtD,CAAP;AACD,C,CACD;;;AACO,SAASI,MAAT,CAAgBJ,MAAhB,EAAwB;AAC7B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,gBAAxB,EAAyCE,MAAzC,EAAgD,CAAhD,CAAP;AACD,C,CAED;;;AACO,SAASK,WAAT,CAAqBL,MAArB,EAA6B;AAClC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,gBAAxB,EAAyCE,MAAzC,EAAgD,CAAhD,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\nconst baseUrl = \"/manager-api\";\n\n// 查询专业票操作术语表\nexport function getList(params) {\n  return api.requestPost(baseUrl+'/bzjxxm/page',params,1)\n}\n\n// 添加或修改\nexport function saveOrUpdate(params) {\n  return api.requestPost(baseUrl+'/bzjxxm/saveOrUpdate',params,1)\n}\n// 添加或修改\nexport function remove(params) {\n  return api.requestPost(baseUrl+'/bzjxxm/remove',params,1)\n}\n\n// 导出\nexport function exportExcel(params) {\n  return api.requestPost(baseUrl+'/bzjxxm/export',params,1)\n}\n\n\n"]}]}