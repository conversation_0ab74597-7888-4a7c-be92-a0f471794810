{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\technicalParameter.vue?vue&type=style&index=0&id=24a9a1cc&scoped=true&lang=css&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\technicalParameter.vue", "mtime": 1706897322897}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLnBhZ2luYXRpb24tY29udGFpbmVyIHsKICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgaGVpZ2h0OiAyNXB4OwogIG1hcmdpbi1ib3R0b206IDIwcHggIWltcG9ydGFudDsKICBtYXJnaW4tdG9wOiAxNXB4OwogIHBhZGRpbmc6IDEwcHggMjBweCAhaW1wb3J0YW50Owp9Cg=="}, {"version": 3, "sources": ["technicalParameter.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAySA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "technicalParameter.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbbzk", "sourcesContent": ["<template>\n  <div>\n    <el-white class=\"button-group\">\n      <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"addTechnicalParameter\">新增</el-button>\n      <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"deleteTechnicalParameter\">删除</el-button>\n    </el-white>\n    <el-table\n      stripe\n      border\n      v-loading=\"loading\"\n      :data=\"tableData\"\n      @selection-change=\"handleSelectionChange\">\n      <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n      <el-table-column label=\"技术参数编码\" align=\"center\" prop=\"jscsbm\"/>\n      <el-table-column label=\"技术参数名称\" align=\"center\" prop=\"jscsmc\" :show-overflow-tooltip=\"true\"/>\n      <el-table-column label=\"单位\" align=\"center\" prop=\"dw\" :show-overflow-tooltip=\"true\"/>\n      <el-table-column label=\"显示类型\" align=\"center\" prop=\"cslx\" :show-overflow-tooltip=\"true\"/>\n      <el-table-column label=\"操作\" fixed=\"right\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"180\">\n        <template slot-scope=\"scope\">\n          <el-button type=\"text\" @click=\"updateTechnicalParameter(scope.row)\" class=\"updateBtn\">修改</el-button>\n          <el-button type=\"text\" @click=\"getDetails(scope.row)\">详情</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <pagination\n      v-show=\"queryParams.total > 0\"\n      :total=\"queryParams.total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"/>\n    <dialog-form\n      ref=\"dialogForm\"\n      :append-to-body=\"true\"\n      label-width=\"150px\"\n      out-width=\"50%\"\n      :reminder=\"reminder\"\n      :rows=\"rows\"\n      @inputChange1=\"paramsTypeChange\"\n      @save=\"saveTechnicalParameter\"\n    />\n  </div>\n</template>\n\n<script>\nimport {\n  deleteTechnicalParameter,\n  getTechnicalParameter,\n  saveOrUpdateTechnicalParameter\n} from '@/api/dagangOilfield/bzgl/sblxwh/jscs'\nimport DialogForm from 'com/dialogFrom/dialogForm'\n\nexport default {\n  components: { DialogForm },\n  props: {\n    deviceTypeData: {\n      type: Object\n    }\n  },\n  name: 'technicalParameter',\n  data() {\n    return {\n      reminder: '新增',\n      rows: 2,\n      //表单数据\n      tableData: [],\n      //查询条件\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        total: 0,\n        sblxbm: ''\n      },\n      loading: false,\n      formList: [\n        {\n          label: '设备类型名称：',\n          value: '',\n          type: 'disabled',\n          name: 'sblxmc',\n          default: true,\n          rules: { required: true, message: '请选择设备类型名称' }\n        },\n        {\n          label: '技术参数名称：',\n          value: '',\n          name: 'jscsmc',\n          default: true,\n          type: 'input',\n          rules: { required: true, message: '请输入技术参数名称' }\n        },\n        {\n          label: '技术参数类型：',\n          value: '',\n          name: 'jscslx',\n          default: true,\n          type: 'selectChange1',\n          options: [{ label: '字符', value: '字符' }, { label: '数值', value: '数值' }],\n          rules: { required: true, message: '请选择技术参数类型' }\n        },\n        {\n          label: '技术参数编码：',\n          value: '',\n          name: 'jscsbm',\n          default: true,\n          type: 'select',\n          options: [],\n          rules: { required: true, message: '请选择技术参数编码' }\n        },\n        {\n          label: '单位：',\n          value: '',\n          type: 'input',\n          name: 'dw',\n          default: true,\n          rules: { required: false, message: '请输入单位' }\n        },\n        {\n          label: '显示类型：',\n          value: '',\n          type: 'select',\n          name: 'cslx',\n          default: true,\n          options: [\n            { label: 'input', value: 'input' },\n            { label: 'select', value: 'select' },\n            { label: 'date', value: 'date' },\n            { label: 'datetime', value: 'datetime' }\n          ],\n          rules: { required: false, message: '请选择显示类型' }\n        },\n        {\n          label: '排序：',\n          value: '',\n          type: 'input',\n          name: 'px',\n          default: true,\n          rules: { required: false, message: '请输入排序' }\n        },\n        {\n          label: '设备类型编码：',\n          value: '',\n          type: 'input',\n          name: 'sblxbm',\n          default: true,\n          hidden: false,\n          rules: { required: true, message: '请选择设备类型编码' }\n        },\n        {\n          label: 'id：',\n          value: '',\n          name: 'objId',\n          default: true,\n          hidden: false,\n          type: 'input',\n          rules: { required: false, message: '请输入编码' }\n        }\n      ],\n      //选中行数据\n      selectedRowDatas: []\n\n    }\n  },\n  mounted() {\n    this.getList()\n  },\n  methods: {\n    //获取技术参数数据\n    getList() {\n      this.loading = true\n      this.queryParams.sblxbm = this.deviceTypeData.sblxbm\n      getTechnicalParameter(this.queryParams).then(res => {\n        this.tableData = res.data.records\n        this.queryParams.total = res.data.total\n        this.loading = false\n      })\n    },\n    //新增技术参数\n    addTechnicalParameter() {\n      this.reminder = '新增'\n      //初始化formList数据\n      this.formList = this.$options.data().formList\n      const addForm = this.formList.map(item => {\n        if (item.name === 'sblxbm') {\n          item.value = this.deviceTypeData.sblxbm\n        }\n        if (item.name === 'sblxmc') {\n          item.value = this.deviceTypeData.sblx\n        }\n        return item\n      })\n      this.$refs.dialogForm.showzzc(addForm)\n    },\n    //批量删除技术参数数据\n    deleteTechnicalParameter() {\n\n      this.$confirm('确认删除选中数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n\n        let ids = []\n        this.selectedRowDatas.forEach(item => {\n          ids.push(item.objId)\n        })\n\n        deleteTechnicalParameter(ids).then(res => {\n          if (res.code === '0000') {\n            this.$message.success('删除成功')\n            this.getList()\n          } else {\n            this.$message.error('操作失败')\n          }\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        })\n      })\n    },\n    //修改基础参数数据\n    updateTechnicalParameter(row) {\n      const updateList = this.formList.map(item => {\n        item.value = row[item.name]\n        return item\n      })\n      this.reminder = '修改'\n      this.$refs.dialogForm.showzzc(updateList)\n    },\n    //技术参数详情\n    getDetails(row) {\n      const infoList = this.formList.map(item => {\n        item.value = row[item.name]\n        return item\n      })\n      this.reminder = '详情'\n      this.$refs.dialogForm.showxq(infoList)\n    },\n    //保存技术参数\n    saveTechnicalParameter(formData) {\n      let message = ''\n      if (formData.objId === '' || !formData.objId) {\n        message = '新增成功'\n      } else {\n        message = '修改成功'\n      }\n      saveOrUpdateTechnicalParameter(formData).then(res => {\n        if (res.code === '0000') {\n          this.$message.success(message)\n          this.getList()\n        } else {\n          this.$message.error('操作失败')\n        }\n      })\n\n    },\n    //行选择事件\n    handleSelectionChange(row) {\n      this.selectedRowDatas = row\n    },\n\n    paramsTypeChange(val) {\n      //技术参数编码下拉框数据\n      let tenhnicalParamOptions = []\n      let type = ''\n      if (val === '字符') {\n        type = 'zf_value'\n        this.getTechnicalParameterOptions(type, tenhnicalParamOptions)\n      } else if (val === '数值') {\n        type = 'sz_value'\n        this.getTechnicalParameterOptions(type, tenhnicalParamOptions)\n      }\n      this.$refs.dialogForm.addUpdateList.list.forEach(item => {\n        if (item.name === 'jscsbm') {\n          item.options = tenhnicalParamOptions\n          item.value = ''\n          return\n        }\n      })\n    },\n    //设置技术参数编码下拉框数据\n    getTechnicalParameterOptions(type, tenhnicalParamOptions) {\n      for (let i = 0; i < 20; i++) {\n        let index = i + 1\n        tenhnicalParamOptions.push({\n          label: type + index,\n          value: type + index\n        })\n      }\n\n    }\n  }\n}\n</script>\n\n<style scoped>\n.pagination-container {\n  position: relative;\n  height: 25px;\n  margin-bottom: 20px !important;\n  margin-top: 15px;\n  padding: 10px 20px !important;\n}\n</style>\n"]}]}