{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\activitimodel\\modeler.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\activitimodel\\modeler.vue", "mtime": 1706897321992}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiTW9kZWxlciIsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHNyYzogIiIsCiAgICAgIGhlaWdodDogZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LmNsaWVudEhlaWdodCAtIDk0LjUgKyAicHg7IiwKICAgICAgbG9hZGluZzogdHJ1ZQogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICBjb25zdCBtb2RlcklkID0gdGhpcy4kcm91dGUucGFyYW1zICYmIHRoaXMuJHJvdXRlLnBhcmFtcy5tb2RlbElkOwogICAgbGV0IHVybCA9IGAvYWN0aXZpdGktYXBpL21vZGVsZXIuaHRtbD9tb2RlbElkPWArbW9kZXJJZDsKICAgIHRoaXMuc3JjID0gdXJsCiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbigpIHsKICAgIGNvbnN0IHRoYXQgPSB0aGlzOwogICAgd2luZG93Lm9ucmVzaXplID0gZnVuY3Rpb24gdGVtcCgpIHsKICAgICAgdGhhdC5oZWlnaHQgPSBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xpZW50SGVpZ2h0IC0gOTQuNSArICJweDsiOwogICAgfTsKICB9Cn07Cg=="}, {"version": 3, "sources": ["modeler.vue"], "names": [], "mappings": ";;;;;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "modeler.vue", "sourceRoot": "src/views/activiti/activitimodel", "sourcesContent": ["<template>\n  <div :style=\"'height:'+ height\">\n    <iframe :src=\"src\" frameborder=\"no\" style=\"width: 100%;height: 100%\" scrolling=\"auto\" />\n  </div>\n</template>\n<script>\n  export default {\n    name: \"Modeler\",\n    data() {\n      return {\n        src: \"\",\n        height: document.documentElement.clientHeight - 94.5 + \"px;\",\n        loading: true\n      };\n    },\n    created() {\n      const moderId = this.$route.params && this.$route.params.modelId;\n      let url = `/activiti-api/modeler.html?modelId=`+moderId;\n      this.src = url\n    },\n    mounted: function() {\n      const that = this;\n      window.onresize = function temp() {\n        that.height = document.documentElement.clientHeight - 94.5 + \"px;\";\n      };\n    }\n  };\n</script>\n"]}]}