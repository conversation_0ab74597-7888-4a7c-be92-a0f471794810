{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\activitimodel\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\activitimodel\\index.vue", "mtime": 1706897321992}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;AA4EA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;AACA,EAAA,IAAA,EAAA,eADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA;AACA,MAAA,OAAA,EAAA,IAFA;AAGA;AACA,MAAA,GAAA,EAAA,EAJA;AAKA;AACA,MAAA,MAAA,EAAA,IANA;AAOA;AACA,MAAA,QAAA,EAAA,IARA;AASA;AACA,MAAA,UAAA,EAAA,IAVA;AAWA;AACA,MAAA,KAAA,EAAA,CAZA;AAaA;AACA,MAAA,SAAA,EAAA,IAdA;AAeA;AACA,MAAA,KAAA,EAAA,MAhBA;AAiBA;AACA,MAAA,WAAA,EAAA,EAlBA;AAmBA;AACA,MAAA,IAAA,EAAA,KApBA;AAqBA;AACA;AACA,MAAA,IAAA,EAAA,EAvBA;AAwBA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,GAAA,EAAA,SAHA;AAIA,QAAA,IAAA,EAAA;AAJA,OAzBA;AA+BA;AACA,MAAA,KAAA,EAAA;AACA,QAAA,GAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,QAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAJA;AAhCA,KAAA;AAyCA,GA5CA;AA6CA,EAAA,OA7CA,qBA6CA;AACA,SAAA,OAAA;AACA,GA/CA;AAgDA,EAAA,OAAA,EAAA;AACA;;AACA;AACA,IAAA,OAHA,qBAGA;AAAA;;AACA,WAAA,OAAA,GAAA,IAAA;AACA,+BAAA,KAAA,WAAA,EAAA,IAAA,CACA,UAAA,QAAA,EAAA;AACA,QAAA,KAAA,CAAA,SAAA,GAAA,QAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,KAAA,CAAA,KAAA,GAAA,QAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,KAAA,CAAA,OAAA,GAAA,KAAA;AACA,OALA;AAOA,KAZA;;AAaA;;;AAGA,IAAA,MAhBA,oBAgBA;AACA,WAAA,IAAA,GAAA,KAAA;AACA,WAAA,KAAA;AACA,KAnBA;;AAoBA;;;AAGA,IAAA,KAvBA,mBAuBA;AACA,WAAA,IAAA,GAAA;AACA,QAAA,GAAA,EAAA,SADA;AAEA,QAAA,IAAA,EAAA,SAFA;AAGA,QAAA,WAAA,EAAA;AAHA,OAAA;AAKA,WAAA,SAAA,CAAA,MAAA;AACA,KA9BA;;AA+BA;AACA,IAAA,WAhCA,yBAgCA;AACA,WAAA,WAAA,CAAA,OAAA,GAAA,CAAA;AACA,WAAA,OAAA;AACA,KAnCA;;AAoCA;AACA,IAAA,UArCA,wBAqCA;AACA,WAAA,SAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,WAAA;AACA,WAAA,WAAA;AACA,KAzCA;;AA0CA;AACA,IAAA,qBA3CA,iCA2CA,SA3CA,EA2CA;AACA,WAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,MAAA;AAAA,OAAA,CAAA;AACA,WAAA,MAAA,GAAA,SAAA,CAAA,MAAA,IAAA,CAAA;AACA,WAAA,QAAA,GAAA,CAAA,SAAA,CAAA,MAAA;AACA,KA/CA;;AAgDA;AACA,IAAA,SAjDA,uBAiDA;AACA,WAAA,KAAA;AACA,WAAA,IAAA,GAAA,IAAA;AACA,KApDA;;AAqDA;AACA,IAAA,YAtDA,wBAsDA,GAtDA,EAsDA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA;AAAA,QAAA,IAAA,EAAA,6BAAA,GAAA,CAAA;AAAA,OAAA;AACA,KAxDA;;AAyDA;AACA,IAAA,UAAA,EAAA,sBAAA;AAAA;;AACA,WAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,mCAAA,MAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA,gBAAA,QAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,cAAA,MAAA,CAAA,UAAA,CAAA,MAAA;;AACA,cAAA,MAAA,CAAA,IAAA,GAAA,KAAA;;AACA,cAAA,MAAA,CAAA,OAAA;;AACA,cAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA;AAAA,gBAAA,IAAA,EAAA,6BAAA,QAAA,CAAA;AAAA,eAAA;AACA;AACA,WAPA;AAQA;AACA,OAXA;AAYA,KAvEA;;AAwEA;AACA,IAAA,YAzEA,wBAyEA,GAzEA,EAyEA;AAAA;;AACA,UAAA,EAAA,GAAA,GAAA,CAAA,EAAA;AACA,WAAA,QAAA,CACA,eAAA,EAAA,GAAA,QADA,EAEA,IAFA,EAGA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAHA,EASA,IATA,CASA,YAAA;AACA,eAAA,wBAAA,EAAA,CAAA;AACA,OAXA,EAYA,IAZA,CAYA,YAAA;AACA,QAAA,MAAA,CAAA,OAAA;;AACA,QAAA,MAAA,CAAA,UAAA,CAAA,MAAA;AACA,OAfA,EAgBA,KAhBA,CAgBA,YAAA,CACA,CAjBA;AAkBA,KA7FA;;AA8FA;AACA,IAAA,YA/FA,wBA+FA,GA/FA,EA+FA;AACA,sCAAA,GAAA,CAAA,EAAA;AACA,KAjGA;;AAkGA;AACA,IAAA,YAnGA,wBAmGA,GAnGA,EAmGA;AAAA;;AACA,iCAAA,GAAA,CAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,GAAA,CAAA,GAAA;AACA,SAFA,MAEA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,GAAA,CAAA,GAAA;AACA;AACA,OANA;AAOA;AA3GA;AAhDA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form class=\"searchForm1\" :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"120px\" >\n        <el-form-item label=\"key：\" prop=\"key\">\n          <el-input v-model=\"queryParams.key\" placeholder=\"请输入key\" clearable class=\"common-width\"\n                    @keyup.enter.native=\"handleQuery\"/>\n        </el-form-item>\n        <el-form-item label=\"名称：\" prop=\"name\">\n          <el-input v-model=\"queryParams.name\" placeholder=\"名称\" clearable class=\"common-width\"\n                    @keyup.enter.native=\"handleQuery\"/>\n        </el-form-item>\n        <el-form-item>\n          <el-button type=\"cyan\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\n          <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\" type=\"warning\">重置</el-button>\n        </el-form-item>\n      </el-form>\n    <el-row :gutter=\"20\">\n      <el-col :span=\"24\">\n        <el-white class=\"button-group\">\n          <el-row :gutter=\"10\" class=\"mb8 pull-right\">\n            <el-col :span=\"1.5\">\n              <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"handleAdd\"\n              >创建模型<!-- v-hasPermi=\"['activiti:model:add']\"-->\n              </el-button>\n            </el-col>\n          </el-row>\n          <el-table style=\"margin-top: 15px;\" stripe border v-loading=\"loading\" :data=\"modelList\" @selection-change=\"handleSelectionChange\" height=\"63.5vh\">\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n            <el-table-column label=\"id\" align=\"center\" prop=\"id\" width=\"60\"/>\n            <el-table-column label=\"key\" align=\"center\" prop=\"key\"  />\n            <el-table-column label=\"名称\" align=\"center\" prop=\"name\" />\n            <el-table-column label=\"版本\" align=\"center\" prop=\"version\" width=\"120\"/>\n            <el-table-column label=\"最后更新时间\" align=\"center\" prop=\"lastUpdateTime\" >\n            </el-table-column>\n            <el-table-column label=\"操作\" align=\"center\"   class-name=\"small-padding fixed-width\">\n              <template slot-scope=\"scope\">\n                <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row)\" >编辑</el-button>\n                <el-button size=\"mini\" type=\"text\" icon=\"el-icon-upload\" @click=\"handleDeploy(scope.row)\" >部署</el-button>\n                <el-button  size=\"mini\" type=\"text\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\"  >删除</el-button>\n                <el-button size=\"mini\" type=\"text\" icon=\"el-icon-download\" @click=\"handleExport(scope.row)\" >导出\n                </el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n          <pagination v-show=\"total>0\"\n                      :total=\"total\"\n                      :page.sync=\"queryParams.pageNum\"\n                      :limit.sync=\"queryParams.pageSize\"\n                      @pagination=\"getList\"\n          />\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!-- 添加或修改参数配置对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"400px\" append-to-body :close-on-click-modal=false v-dialogDrag>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"60px\">\n            <el-form-item label=\"名称\" prop=\"name\">\n              <el-input v-model=\"form.name\" placeholder=\"请输入名称\" maxlength=\"30\"/>\n            </el-form-item>\n            <el-form-item label=\"key\" prop=\"key\">\n              <el-input v-model=\"form.key\" placeholder=\"请输入key\" maxlength=\"30\"/>\n            </el-form-item>\n            <el-form-item label=\"备注\">\n              <el-input v-model=\"form.description\" type=\"textarea\" placeholder=\"请输入内容\"></el-input>\n            </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  import {\n    list,save,del,exportModel,deploy\n  } from \"@/api/activiti/activitimodel\";\n  export default {\n    name: \"ActivitiModel\",\n    data() {\n      return {\n        // 遮罩层\n        loading: true,\n        // 选中数组\n        ids: [],\n        // 非单个禁用\n        single: true,\n        // 非多个禁用\n        multiple: true,\n        // 显示搜索条件\n        showSearch: true,\n        // 总条数\n        total: 0,\n        // 用户表格数据\n        modelList: null,\n        // 弹出层标题\n        title: \"创建模型\",\n        // 部门树选项\n        deptOptions: [],\n        // 是否显示弹出层\n        open: false,\n        // 部门名称\n        // 表单参数\n        form: {},\n        // 查询参数\n        queryParams: {\n          pageNum: 1,\n          pageSize: 10,\n          key: undefined,\n          name: undefined,\n        },\n        // 表单校验\n        rules: {\n          key: [\n            {required: true, message: \"key不能为空\", trigger: \"blur\"},\n          ],\n          name: [\n            {required: true, message: \"名称不能为空\", trigger: \"blur\"},\n          ],\n        },\n      };\n    },\n    created() {\n      this.getList();\n    },\n    methods: {\n      // 测试数据字典方法结束\n      /** 查询用户列表 */\n      getList() {\n        this.loading = true;\n        list(this.queryParams).then(\n          (response) => {\n            this.modelList = response.data.records;\n            this.total = response.data.total;\n            this.loading = false;\n          }\n        );\n      },\n     /**\n      * 取消按钮\n      * */\n      cancel() {\n        this.open = false;\n        this.reset();\n      },\n      /**\n       * 表单重置\n       * */\n      reset() {\n        this.form = {\n          key: undefined,\n          name: undefined,\n          description:undefined\n        };\n        this.resetForm(\"form\");\n      },\n      /** 搜索按钮操作 */\n      handleQuery() {\n        this.queryParams.pageNum = 1;\n        this.getList();\n      },\n      /** 重置按钮操作 */\n      resetQuery() {\n        this.dateRange = [];\n        this.resetForm(\"queryForm\");\n        this.handleQuery();\n      },\n      /***多选框选中数据***/\n      handleSelectionChange(selection) {\n        this.ids = selection.map((item) => item.userId);\n        this.single = selection.length != 1;\n        this.multiple = !selection.length;\n      },\n      /** 新增按钮操作 */\n      handleAdd() {\n        this.reset();\n        this.open = true;\n      },\n      /** 编辑按钮操作 */\n      handleUpdate(row) {\n        this.$router.push({path:'/activiti/onlinemodeler/'+row.id});\n      },\n      /** 提交按钮 */\n      submitForm: function () {\n        this.$refs[\"form\"].validate((valid) => {\n          if (valid) {\n            save(this.form).then((response) => {\n              if (response.code === '0000') {\n                this.msgSuccess(\"新增成功\");\n                this.open = false;\n                this.getList();\n                this.$router.push({path:'/activiti/onlinemodeler/'+response.data});\n              }\n            });\n          }\n        });\n      },\n      /** 删除按钮操作 */\n      handleDelete(row) {\n        const id = row.id;\n        this.$confirm(\n          '是否确认删除id为\"' + id + '\"的数据项?',\n          \"警告\",\n          {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\",\n          }\n        )\n          .then(function () {\n            return del(id);\n          })\n          .then(() => {\n            this.getList();\n            this.msgSuccess(\"删除成功\");\n          })\n          .catch(function () {\n          });\n      },\n      /***  导出bpm文件 ***/\n      handleExport(row){\n        exportModel(row.id);\n      },\n      /** 部署 **/\n      handleDeploy(row){\n        deploy(row.id).then(res =>{\n          if(res.code=='0000'){\n            this.msgSuccess(res.msg);\n          }else{\n            this.msgError(res.msg);\n          }\n        })\n      }\n    },\n  };\n</script>\n"], "sourceRoot": "src/views/activiti/activitimodel"}]}