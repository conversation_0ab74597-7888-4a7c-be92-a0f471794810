{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\gfdzcz\\components\\czp_cx.vue?vue&type=style&index=0&id=758d2def&scoped=true&lang=css&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\gfdzcz\\components\\czp_cx.vue", "mtime": 1751379544089}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmNhcmQtc3R5bGUgewogIGJhY2tncm91bmQtY29sb3I6ICNlMGVmZjI7CiAgbGluZS1oZWlnaHQ6IDN2aDsKICBib3JkZXItcmFkaXVzOiAzcHg7CiAgZm9udC13ZWlnaHQ6IDgwMDsKICBwYWRkaW5nLWxlZnQ6IDF2dzsKICBtYXJnaW4tYm90dG9tOiAzdmg7Cn0KCi8q5o6n5Yi2aW5wdXTovpPlhaXmoYbovrnmoYbmmK/lkKbmmL7npLoqLwouZWxJbnB1dCA+Pj4gLmVsLWlucHV0X19pbm5lciB7CiAgYm9yZGVyOiAwOwp9CgouY2VudGVyIHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7IC8q6K6pZGl25YaF6YOo5paH5a2X5bGF5LitKi8KfQo="}, {"version": 3, "sources": ["czp_cx.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2uBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "czp_cx.vue", "sourceRoot": "src/views/dagangOilfield/czpgl/gfdzcz/components", "sourcesContent": ["<template>\n  <div>\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n      @handleReset=\"getReset\"\n      @handleEvent=\"handleEvent\"\n    />\n    <el-white class=\"button-group\">\n      <el-row class=\"pull-right button_btn\" :gutter=\"20\">\n        <el-col :gutter=\"4\" :span=\"1.5\">\n          <el-button type=\"primary\" @click=\"exportFun\">导出 </el-button>\n        </el-col>\n      </el-row>\n      <div>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          height=\"53vh\"\n          v-loading=\"loading\"\n        />\n      </div>\n    </el-white>\n\n    <!-- 详情/新增/修改 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\">\n        <div>\n          <el-row>\n            <el-col :span=\"8\">\n              <el-form-item label=\"编号：\" prop=\"bm\">\n                <el-input\n                  v-model=\"form.bm\"\n                  placeholder=\"确认后编号自动生成\"\n                  disabled\n                >\n                </el-input>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"分公司：\" prop=\"fgs\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.fgs\"\n                  placeholder=\"请选择分公司\"\n                  disabled\n                >\n                  <el-option\n                    v-for=\"item in ssgsOptionsDataList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"光伏电站名称：\" prop=\"bdzmc\">\n                <el-select\n                  style=\"width: 100%\"\n                  filterable\n                  v-model=\"form.bdzmc\"\n                  disabled\n                  placeholder=\"请选择光伏电站\"\n                >\n                  <el-option\n                    v-for=\"item in bdzList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"下令人：\" prop=\"xlr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.xlr\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                  disabled\n                >\n                  <el-option\n                    v-for=\"item in xlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"审票人：\" prop=\"bzspr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.bzspr\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                  disabled\n                >\n                  <el-option\n                    v-for=\"item in sprList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"操作开始时间：\"\n                prop=\"kssj\"\n                label-width=\"140px\"\n              >\n                <el-date-picker\n                  disabled\n                  v-model=\"form.kssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"操作结束时间：\"\n                prop=\"jssj\"\n                label-width=\"140px\"\n              >\n                <el-date-picker\n                  disabled\n                  v-model=\"form.jssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作人：\" prop=\"czr\">\n                <el-input\n                  v-model=\"form.czr\"\n                  placeholder=\"请输入内容\"\n                  disabled\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"监护人：\" prop=\"jhr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.jhr\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                  disabled\n                >\n                  <el-option\n                    v-for=\"item in jlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作项数：\" prop=\"czxs\">\n                <el-input-number\n                  v-model=\"form.czxs\"\n                  placeholder=\"请输入操作项数\"\n                  disabled\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"已执行项数：\" prop=\"yzxczxs\">\n                <el-input-number\n                  v-model=\"form.yzxczxs\"\n                  placeholder=\"请输入已执行操作项数\"\n                  disabled\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"未执行项数：\" prop=\"wzxczxs\">\n                <el-input-number\n                  v-model=\"form.wzxczxs\"\n                  placeholder=\"请输入未执行操作项数\"\n                  disabled\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"是否已执行：\" prop=\"sfyzx\">\n                <el-select v-model=\"form.sfyzx\" placeholder=\"请选择\" disabled>\n                  <el-option\n                    v-for=\"item in sfyzxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item\n                label=\"未执行原因：\"\n                prop=\"wzxyy\"\n                v-if=\"form.sfyzx === '未执行'\"\n              >\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"form.wzxyy\"\n                  placeholder=\"请输入内容\"\n                  style=\"width: 100%\"\n                  disabled\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"操作任务：\" prop=\"czrw\">\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"form.czrw\"\n                  placeholder=\"请输入内容\"\n                  style=\"width: 100%\"\n                  disabled\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"上传图片：\">\n                <el-upload\n                  disabled\n                  action=\"\"\n                  ref=\"uploadImg\"\n                  accept=\"image/jpeg,image/jpg,image/png\"\n                  :headers=\"header\"\n                  :multiple=\"true\"\n                  :on-change=\"handleChange\"\n                  :data=\"uploadImgData\"\n                  :file-list=\"imgList\"\n                  :auto-upload=\"false\"\n                  list-type=\"picture-card\"\n                  :on-preview=\"handlePictureCardPreview\"\n                  :on-progress=\"handleProgress\"\n                  :on-remove=\"handleRemove\"\n                >\n                  <i class=\"el-icon-plus\"></i>\n                </el-upload>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n\n        <!--预览，查看全部操作项-->\n        <div>\n          <div align=\"left\">\n            <el-button type=\"info\" @click=\"handleYlChange\">预览</el-button>\n          </div>\n          <el-dialog :title=\"titleyl\" :visible.sync=\"yl\" append-to-body>\n            <!--第一个表格-->\n            <el-table\n              :data=\"propTableData.colFirst\"\n              ref=\"propTable\"\n              disabled\n              border\n              stripe\n              style=\"width: 100%\"\n              :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n            >\n              <el-table-column\n                align=\"center\"\n                width=\"120\"\n                prop=\"xh\"\n                label=\"顺序号\"\n                sortable\n              >\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"请输入顺序号\"\n                      disabled\n                      v-model=\"scope.row.xh\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"请输入操作项目\"\n                      disabled\n                      v-model=\"scope.row.czrw\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-dialog>\n        </div>\n\n        <!--列表-->\n        <div>\n          <!--第一个表格-->\n          <el-table\n            :data=\"propTableData.colFirst\"\n            ref=\"propTable\"\n            height=\"300\"\n            border\n            stripe\n            disabled\n            style=\"width: 100%\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <el-table-column\n              align=\"center\"\n              width=\"120\"\n              prop=\"xh\"\n              label=\"顺序号\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input-number\n                    size=\"small\"\n                    v-model=\"scope.row.xh\"\n                    :min=\"1\"\n                    :precision=\"0\"\n                    controls-position=\"right\"\n                    disabled\n                  ></el-input-number>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入操作项目\"\n                    v-model=\"scope.row.czrw\"\n                    disabled\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" width=\"80\" label=\"是否完成\">\n              <template slot-scope=\"scope\">\n                <el-checkbox v-model=\"scope.row.sfwc\" disabled></el-checkbox>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <!--图片放大展示弹出框-->\n    <el-image-viewer\n      v-if=\"imgDialogVisible\"\n      :on-close=\"onClose\"\n      :url-list=\"dialogImageUrl\"\n      zIndex=\"3000\"\n    />\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport {\n  exportBdCzpExcel,\n  getCzpmxList,\n  getListLsp,\n  remove\n} from \"@/api/yxgl/bdyxgl/bddzczp\";\nimport { getUsers } from \"@/api/system/usergroup\";\nimport ElImageViewer from \"element-ui/packages/image/src/image-viewer\";\nimport { getFgsOptions } from \"@/api/yxgl/gfyxgl/gfzbgl\";\nimport { getBdzDataListSelected as getBdzSelectList} from \"@/api/dagangOilfield/asset/gfsbtz\";\n\nexport default {\n  name: \"dzczcxtj\",\n  components: { ElImageViewer },\n  data() {\n    return {\n      // 是否已执行下拉框\n      sfyzxList: [\n        { label: \"已执行\", value: \"已执行\" },\n        { label: \"未执行\", value: \"未执行\" }\n      ],\n      jlrList: [],\n      xlrList: [],\n      sprList: [],\n      //所属分公司下拉框数据\n      ssgsOptionsDataList: [],\n      loading: false,\n      titleyl: \"\",\n      yl: false,\n      //图片地址url\n      dialogImageUrl: \"\",\n      //展示图片dialog控制\n      imgDialogVisible: false,\n      //上传图片时的携带的其他参数\n      uploadImgData: {\n        businessId: \"\" //携带的表单主键id\n      },\n      //上传图片时的请求头\n      header: {},\n      //图片list\n      imgList: [],\n      bdzList: [],\n      // 多选框选中的id\n      ids: [],\n      //弹出框中表格数据\n      propTableData: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //form表单\n      form: {\n        bh: \"\",\n        bdzmc: \"\",\n        kssj: \"\",\n        jssj: \"\",\n        czrw: \"\",\n        czr: \"\",\n        jhr: \"\",\n        xlr: \"\",\n        spr: \"\",\n        lx: 4, //光伏\n        colFirst: []\n      },\n      //详情弹框是否显示\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          bh: \"\",\n          fgs: \"\",\n          bdzmc: \"\",\n          czsjArr: [],\n          czr: \"\",\n          czrw: \"\",\n          jhrmc: \"\",\n          xlrmc: \"\",\n          bzsprmc: \"\"\n        }, //查询条件\n        fieldList: [\n          { label: \"编号\", value: \"bm\", type: \"input\", clearable: true },\n          {\n            label: \"分公司\",\n            value: \"fgs\",\n            type: \"select\",\n            clearable: true,\n            options: []\n          },\n          {\n            label: \"光伏电站名称\",\n            value: \"bdzmc\",\n            type: \"select\",\n            clearable: true,\n            options: []\n          },\n          { label: \"操作任务\", value: \"czrw\", type: \"input\", clearable: true },\n          {\n            label: \"操作时间\",\n            value: \"czsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"操作人\", value: \"czr\", type: \"input\", clearable: true },\n          { label: \"监护人\", value: \"jhrmc\", type: \"input\", clearable: true },\n          { label: \"下令人\", value: \"xlrmc\", type: \"input\", clearable: true },\n          { label: \"审票人\", value: \"bzsprmc\", type: \"input\", clearable: true },\n          // { label: \"操作项数\", value: \"czxs\", type: \"input\", clearable: true },\n          // {\n          //   label: \"已执行项数\",\n          //   value: \"yzxczxs\",\n          //   type: \"input\",\n          //   clearable: true\n          // },\n          // {\n          //   label: \"未执行项数\",\n          //   value: \"wzxczxs\",\n          //   type: \"input\",\n          //   clearable: true\n          // }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        //{ czrw: '任务一', czr: '张三', dzxl: '中山路102号线', gzbz: 'pzl', zpbm: '部门一', whbz: '班组一' }\n        tableData: [],\n        tableHeader: [\n          { label: \"编号\", prop: \"bm\", minWidth: \"80\" },\n          { label: \"分公司\", prop: \"fgsmc\", minWidth: \"100\" },\n          { label: \"光伏电站名称\", prop: \"gfzmc\", minWidth: \"130\" },\n          { label: \"操作任务\", prop: \"czrw\", minWidth: \"160\" },\n          { label: \"操作开始时间\", prop: \"kssj\", minWidth: \"130\" },\n          { label: \"操作结束时间\", prop: \"jssj\", minWidth: \"130\" },\n          { label: \"操作人\", prop: \"czr\", minWidth: \"70\" },\n          { label: \"监护人\", prop: \"jhrmc\", minWidth: \"70\" },\n          { label: \"下令人\", prop: \"xlrmc\", minWidth: \"70\" },\n          { label: \"审票人\", prop: \"bzsprmc\", minWidth: \"120\" },\n          { label: \"操作项数\", prop: \"czxs\", minWidth: \"80\" },\n          { label: \"已执行项数\", prop: \"yzxczxs\", minWidth: \"90\" },\n          { label: \"未执行项数\", prop: \"wzxczxs\", minWidth: \"90\" },\n          {\n            prop: \"operation\",\n            label: \"操作\",\n            minWidth: \"80px\",\n            style: { display: \"block\" },\n            //操作列固定再右侧\n            fixed: \"right\",\n            operation: [{ name: \"详情\", clickFun: this.getDetails }]\n          }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        //光伏\n        lx: 4,\n        //用来区分历史票库，1-已办结，2-未办结\n        status: \"4\",\n        mySorts: [{ prop: \"kssj\", asc: false }]\n      }\n    };\n  },\n  async mounted() {\n    this.xlrList = await this.getGroupUsers(132, \"\");\n    this.sprList = await this.getGroupUsers(134, \"\");\n    this.jlrList = await this.getGroupUsers(133, \"\");\n    //获取token\n    this.header.token = getToken();\n    this.getData({ objId: this.qxjlObjId });\n    this.getFgsOptions();\n    //获取光伏电站下拉框数据\n    this.getBdzSelectList();\n    if (\"admin\" === this.$store.getters.name) {\n      let option = { name: \"删除\", clickFun: this.deleteRow };\n      this.tableAndPageInfo.tableHeader[13].operation.push(option);\n    }\n  },\n  methods: {\n    /**\n     * 获取分公司下拉数据\n     */\n    getFgsOptions() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.ssgsOptionsDataList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"fgs\") {\n            return (item.options = this.ssgsOptionsDataList);\n          }\n        });\n      });\n    },\n    //导出\n    exportFun() {\n      exportBdCzpExcel(this.params, \"操作票信息\");\n    },\n    //删除按钮\n    async deleteRow(row) {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove(row.objId).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    getGroupUsers(personGroupId, deptId) {\n      return getUsers({\n        personGroupId: personGroupId,\n        deptId: deptId,\n        deptName: \"\"\n      }).then(res => {\n        return res.data;\n      });\n    },\n    // 预览按钮\n    handleYlChange() {\n      this.titleyl = \"查看操作项目\";\n      this.yl = true;\n    },\n    // 选择文件时，往fileList里添加\n    handleChange(file, fileList) {\n      this.imgList = fileList;\n    },\n    handleProgress(event, file, fileList) {\n      console.log(\"event\", event);\n      console.log(\"file\", file);\n      console.log(\"fileList\", fileList);\n    },\n    //图片移除\n    handleRemove(file, fileList) {\n      this.imgList = fileList;\n    },\n    //图片放大\n    handlePictureCardPreview(file) {\n      this.dialogImageUrl = [file.url];\n      this.imgDialogVisible = true;\n    },\n    onClose() {\n      this.imgDialogVisible = false;\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params };\n        this.loading = true;\n        const { data, code } = await getListLsp(this.params);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          this.loading = false;\n        }\n      } catch (e) {}\n    },\n    //重置按钮\n    getReset() {},\n    //选中行\n    handleSelectionChange() {},\n    /**\n     *获取操作票明细\n     */\n    async getCzpmx(row) {\n      try {\n        const { data, code } = await getCzpmxList({ objId: row.objId });\n        if (code === \"0000\") {\n          this.propTableData.colFirst = data;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n\n    //详情按钮\n    getDetails(row) {\n      this.getCzpmx(row);\n      this.title = \"变电操作票详情查看\";\n      this.form = { ...row };\n      //图片结合\n      this.imgList = this.form.imgList;\n      this.isDisabled = true;\n      this.isShowDetails = true;\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false;\n    },\n\n    /**\n     * 获取光伏电站下拉框数据\n     */\n    getBdzSelectList() {\n      getBdzSelectList({}).then(res => {\n        this.bdzList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"bdzmc\") {\n            return (item.options = this.bdzList);\n          }\n        });\n      });\n    },\n    //下拉框change事件\n    handleEvent(val) {\n      //光伏电站查询下拉框选项根据所选分公司带出来\n      if (val.label === \"fgs\" && val.value && val.value !== \"\") {\n        let form = {\n          ssdwbm: val.value\n        };\n        getBdzSelectList(form).then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value == \"bdzmc\") {\n              return (item.options = res.data);\n            }\n          });\n        });\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.card-style {\n  background-color: #e0eff2;\n  line-height: 3vh;\n  border-radius: 3px;\n  font-weight: 800;\n  padding-left: 1vw;\n  margin-bottom: 3vh;\n}\n\n/*控制input输入框边框是否显示*/\n.elInput >>> .el-input__inner {\n  border: 0;\n}\n\n.center {\n  text-align: center; /*让div内部文字居中*/\n}\n</style>\n"]}]}