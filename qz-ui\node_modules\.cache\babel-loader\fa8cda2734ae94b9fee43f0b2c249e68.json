{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\gqjgl\\xlgqj.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\gqjgl\\xlgqj.vue", "mtime": 1706897324701}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["xlgqj.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AA0WA;;AACA;;AAWA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,UAAA,EAAA;AAAA,IAAA,SAAA,EAAA,kBAAA;AAAA,IAAA,QAAA,EAAA;AAAA,GADA;AAEA,EAAA,IAAA,EAAA,OAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA,MAAA,QAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,IADA;AAEA;AACA,MAAA,KAAA,EAAA;AACA,QAAA,EAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CADA;AAEA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAFA;AAGA,QAAA,EAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAHA;AAIA,QAAA,GAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AAJA,OAHA;AASA,MAAA,UAAA,EAAA,MATA;AAUA,MAAA,MAAA,EAAA;AACA,QAAA,IAAA,EAAA;AADA,OAVA;AAaA;AACA,MAAA,UAAA,EAAA,KAdA;AAeA;AACA,MAAA,QAAA,EAAA,OAhBA;AAkBA;AACA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,MAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,MAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SARA,CASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AArBA;AAZA,OAnBA;AAuDA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,GAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA,EAFA;AAGA,UAAA,IAAA,EAAA,EAHA;AAIA,UAAA,KAAA,EAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAJA;AAPA,OAxDA;AAsEA;AACA,MAAA,oBAAA,EAAA,KAvEA;AAwEA;AACA,MAAA,2BAAA,EAAA,KAzEA;AA0EA;AACA,MAAA,iBAAA,EAAA,KA3EA;AA4EA;AACA,MAAA,IAAA,EAAA,EA7EA;AA8EA,MAAA,OAAA,EAAA,EA9EA;AA+EA;AACA,MAAA,qBAAA,EAAA,KAhFA;AAiFA;AACA,MAAA,wBAAA,EAAA,KAlFA;AAmFA;AACA,MAAA,IAAA,EAAA;AACA,QAAA,IAAA,EAAA;AADA,OApFA;AAuFA,MAAA,OAAA,EAAA,KAvFA;AAwFA;AACA,MAAA,SAAA,EAAA,EAzFA;AA0FA;AACA,MAAA,SAAA,EAAA,EA3FA;AA4FA;AACA,MAAA,cAAA,EAAA,IA7FA;AA8FA,MAAA,UAAA,EAAA,IA9FA;AA+FA;AACA,MAAA,UAAA,EAAA,EAhGA;AAiGA;AACA,MAAA,iBAAA,EAAA;AACA,QAAA,UAAA,EAAA;AADA,OAlGA;AAqGA;AACA,MAAA,eAAA,EAAA,EAtGA;AAwGA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,KAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA,CAFA;AAGA,QAAA,QAAA,EAAA,EAHA;AAIA,QAAA,OAAA,EAAA;AAJA,OAzGA;AAgHA;AACA,MAAA,MAAA,EAAA;AACA,QAAA,EAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,MAAA,EAAA,EAHA;AAIA,QAAA,QAAA,EAAA,EAJA;AAKA,QAAA,MAAA,EAAA,EALA;AAMA,QAAA,QAAA,EAAA,EANA;AAOA,QAAA,IAAA,EAAA,EAPA;AAQA,QAAA,QAAA,EAAA,EARA;AASA,QAAA,QAAA,EAAA,EATA;AAUA,QAAA,MAAA,EAAA;AAVA,OAjHA;AA8HA,MAAA,UAAA,EAAA,KA9HA;AAgIA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,KAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA,CAFA;AAGA,QAAA,QAAA,EAAA,EAHA;AAIA,QAAA,OAAA,EAAA;AAJA,OAjIA;AAuIA;AACA,MAAA,MAAA,EAAA;AACA,QAAA,EAAA,EAAA,EADA;AAEA,QAAA,MAAA,EAAA,EAFA;AAGA,QAAA,QAAA,EAAA,EAHA;AAIA,QAAA,MAAA,EAAA,EAJA;AAKA,QAAA,QAAA,EAAA,EALA;AAMA,QAAA,IAAA,EAAA,EANA;AAOA,QAAA,IAAA,EAAA,EAPA;AAQA,QAAA,MAAA,EAAA,EARA;AASA,QAAA,KAAA,EAAA;AATA,OAxIA;AAoJA;AACA,MAAA,WAAA,EAAA,EArJA;AAsJA;AACA,MAAA,SAAA,EAAA,KAvJA;AAwJA;AACA,MAAA,YAAA,EAAA,EAzJA;AA0JA;AACA,MAAA,SAAA,EAAA,KA3JA;AA4JA;AACA,MAAA,YAAA,EAAA;AA7JA,KAAA;AA+JA,GAnKA;AAoKA,EAAA,KAAA,EAAA,EApKA;AAqKA,EAAA,OArKA,qBAqKA,CAEA,CAvKA;AAwKA,EAAA,OAxKA,qBAwKA;AACA,SAAA,OAAA;AACA,GA1KA;AA2KA,EAAA,OAAA,EAAA;AACA,IAAA,WADA,yBACA;AACA,iCAAA,KAAA,MAAA,EAAA,OAAA;AACA,KAHA;AAIA,IAAA,UAJA,4BAIA;AAAA,UAAA,MAAA,QAAA,MAAA;AAAA,UAAA,IAAA,QAAA,IAAA;AAAA,UAAA,KAAA,QAAA,KAAA;;AACA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,CAAA,OAAA,CAAA,MAAA,IAAA,CAAA,CAAA,EAAA;AACA,eAAA,MAAA,CAAA,OAAA,GAAA,CAAA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,GAAA,EAAA;AAAA,WAAA,CAAA;AACA,SAFA,MAEA;AACA,eAAA,MAAA,CAAA,OAAA,GAAA,CAAA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,GAAA,EAAA;AAAA,WAAA,CAAA;AACA;AACA,OANA,MAMA;AACA,aAAA,MAAA,CAAA,OAAA,GAAA,CAAA;AAAA,UAAA,IAAA,EAAA,YAAA;AAAA,UAAA,GAAA,EAAA;AAAA,SAAA,CAAA;AACA;;AACA,WAAA,OAAA;AACA,KAfA;AAgBA,IAAA,WAhBA,yBAgBA;AACA,WAAA,OAAA;AACA,KAlBA;;AAmBA;AACA,IAAA,cApBA,0BAoBA,EApBA,EAoBA;AACA,0BAAA,EAAA;AACA,KAtBA;;AAuBA;;;;AAIA,IAAA,mBA3BA,+BA2BA,IA3BA,EA2BA;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,IAAA,GAAA,OAAA,IAAA,GAAA,EAAA,CADA,CACA;;AACA,UAAA,CAAA,QAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,kBAAA;AACA;;AACA,UAAA,IAAA,GAAA,IAAA,CAAA,IAAA,GAAA,IAAA;AACA,KAjCA;;AAkCA;;;;;;AAMA,IAAA,gBAxCA,4BAwCA,QAxCA,EAwCA,IAxCA,EAwCA,QAxCA,EAwCA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA,QAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA,IAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA,QAAA,EAHA,CAKA;;AACA,WAAA,IAAA,CAAA,YAAA,GAAA,QAAA,CAAA,IAAA,CAAA,UAAA,CANA,CAOA;;AACA,WAAA,IAAA,CAAA,cAAA,GAAA,QAAA,CAAA,IAAA,CAAA,OAAA,CAAA,WAAA;AACA,KAjDA;;AAmDA;;;;;AAKA,IAAA,mBAxDA,+BAwDA,IAxDA,EAwDA,QAxDA,EAwDA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,wBAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,IAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,QAAA;AACA,KA5DA;;AA6DA;;;AAGA,IAAA,mBAhEA,iCAgEA;AACA;AACA,WAAA,iBAAA,CAAA,UAAA,GAAA,qBAAA;AACA,WAAA,KAAA,CAAA,aAAA,CAAA,MAAA;AACA,KApEA;AAsEA;AACA,IAAA,OAvEA,mBAuEA,MAvEA,EAuEA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,KAFA,+DAEA,KAAA,CAAA,MAFA,GAEA,MAFA,GAGA;;AAHA;AAAA,uBAIA,uBAAA,KAAA,CAJA;;AAAA;AAAA;AAIA,gBAAA,IAJA,kBAIA,IAJA;AAIA,gBAAA,IAJA,kBAIA,IAJA;;AAKA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,KAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,KAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AARA;AAAA;;AAAA;AAAA;AAAA;AAUA,gBAAA,OAAA,CAAA,GAAA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,KAnFA;AAqFA;AACA,IAAA,eAtFA,6BAsFA;AACA,WAAA,UAAA,GAAA,KAAA,CADA,CAEA;;AACA,WAAA,iBAAA,GAAA,IAAA,CAHA,CAIA;;AACA,WAAA,QAAA,GAAA,OAAA,CALA,CAMA;;AACA,WAAA,IAAA,GAAA;AACA,QAAA,QAAA,EAAA,KAAA,UADA;AAEA,QAAA,IAAA,EAAA;AAFA,OAAA;AAIA,KAjGA;AAkGA;AACA,IAAA,UAnGA,sBAmGA,GAnGA,EAmGA;AACA;AACA,WAAA,iBAAA,GAAA,IAAA,CAFA,CAGA;;AACA,WAAA,QAAA,GAAA,OAAA,CAJA,CAKA;;AACA,WAAA,UAAA,GAAA,IAAA,CANA,CAOA;;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,KA5GA;AA6GA;AACA,IAAA,aA9GA,yBA8GA,GA9GA,EA8GA;AACA;AACA,WAAA,iBAAA,GAAA,IAAA,CAFA,CAGA;;AACA,WAAA,QAAA,GAAA,OAAA,CAJA,CAKA;;AACA,WAAA,UAAA,GAAA,KAAA,CANA,CAOA;;AACA,WAAA,IAAA,mCAAA,GAAA;AAEA,KAxHA;AAyHA;AACA,IAAA,QA1HA,sBA0HA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,MAAA,CAAA,KAAA,CAAA,MAAA,EAAA,QAAA;AAAA,oGAAA,kBAAA,KAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA,iCACA,KADA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA,mCAGA,4BAAA,MAAA,CAAA,IAAA,CAHA;;AAAA;AAAA;AAGA,4BAAA,IAHA,uBAGA,IAHA;;AAIA,gCAAA,IAAA,KAAA,MAAA,EAAA;AACA,8BAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA;;AANA;AAAA;;AAAA;AAAA;AAAA;AAQA,4BAAA,OAAA,CAAA,GAAA;;AARA;AAUA;AACA,4BAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,4BAAA,MAAA,CAAA,OAAA;;AACA,4BAAA,MAAA,CAAA,iBAAA,GAAA,KAAA;;AAbA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAA;;AAAA;AAAA;AAAA;AAAA,oBADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA,KA1IA;AA2IA;AACA,IAAA,SA5IA,qBA4IA,EA5IA,EA4IA;AAAA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,MAAA,GAAA,CAAA,IAAA,CAAA,EAAA;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,8BAAA,GAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,cAAA,IAAA,SAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,OAAA;AACA,WANA,MAMA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAIA;AACA,SAbA;AAcA,OAnBA,EAmBA,KAnBA,CAmBA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OAxBA;AAyBA,WAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;AACA,WAAA,OAAA;AACA,KAjLA;AAmLA;AACA,IAAA,mBApLA,+BAoLA,GApLA,EAoLA;AACA,WAAA,YAAA,GAAA,EAAA;AACA,WAAA,WAAA,GAAA,GAAA;AACA,WAAA,WAAA,CAAA,KAAA,GAAA,GAAA,CAAA,KAAA;AACA,WAAA,qBAAA,GAAA,IAAA;AACA,WAAA,WAAA;AACA,KA1LA;AA4LA;AACA,IAAA,mBA7LA,+BA6LA,GA7LA,EA6LA;AACA,WAAA,WAAA,GAAA,GAAA;AACA,WAAA,WAAA,CAAA,KAAA,GAAA,GAAA,CAAA,KAAA;AACA,WAAA,oBAAA,GAAA,IAAA;AACA,WAAA,YAAA;AACA,KAlMA;AAmMA;AACA,IAAA,WApMA,yBAoMA;AACA,WAAA,MAAA,GAAA,KAAA,QAAA,CAAA,IAAA,GAAA,MAAA;AACA,WAAA,MAAA,CAAA,KAAA,GAAA,KAAA,WAAA,CAAA,KAAA;AACA,WAAA,2BAAA,GAAA,IAAA;AACA,KAxMA;AAyMA,IAAA,QAzMA,oBAyMA,GAzMA,EAyMA;AACA,WAAA,MAAA,GAAA,GAAA;AACA,WAAA,2BAAA,GAAA,IAAA;AACA,KA5MA;AA6MA;AACA,IAAA,WA9MA,yBA8MA;AACA,WAAA,MAAA,GAAA,KAAA,QAAA,CAAA,IAAA,GAAA,MAAA;AACA,WAAA,MAAA,CAAA,KAAA,GAAA,KAAA,WAAA,CAAA,KAAA;AACA,WAAA,wBAAA,GAAA,IAAA;AACA,KAlNA;AAmNA,IAAA,QAnNA,oBAmNA,GAnNA,EAmNA;AACA,WAAA,MAAA,GAAA,GAAA;AACA,WAAA,wBAAA,GAAA,IAAA;AACA,KAtNA;AAuNA;AACA,IAAA,gBAxNA,8BAwNA,CAEA,CA1NA;AA2NA;AACA,IAAA,mBA5NA,iCA4NA,CAEA,CA9NA;AA+NA;AACA,IAAA,eAhOA,6BAgOA,CAEA,CAlOA;AAoOA,IAAA,WApOA,yBAoOA,CAEA,CAtOA;AAuOA;AACA,IAAA,YAxOA,wBAwOA,IAxOA,EAwOA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KA1OA;AA4OA;AACA,IAAA,WA7OA,yBA6OA;AAAA;;AACA,WAAA,SAAA,GAAA,IAAA;AACA,oCAAA,KAAA,WAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,MAAA,CAAA,WAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,MAAA,CAAA,SAAA,GAAA,KAAA;AACA,OAJA;AAKA,KApPA;AAsPA;AACA,IAAA,cAvPA,4BAuPA;AAAA;;AACA,6CAAA,KAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,WAAA;;AACA,QAAA,MAAA,CAAA,wBAAA,GAAA,KAAA;AACA,OAHA;AAIA,KA5PA;AA6PA;AACA,IAAA,UA9PA,wBA8PA;AAAA;;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,YAAA,GAAA,GAAA,EAAA;;AACA,QAAA,MAAA,CAAA,YAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA,SAFA;;AAGA,yCAAA,GAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,cAAA,IAAA,SAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAKA,WANA,MAMA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAIA;;AACA,UAAA,MAAA,CAAA,WAAA;AACA,SAdA;AAeA,OAxBA,EAwBA,KAxBA,CAwBA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OA7BA;AA8BA,KA7RA;AA+RA;AACA,IAAA,YAhSA,0BAgSA;AAAA;;AACA,WAAA,SAAA,GAAA,IAAA;AACA,0CAAA,KAAA,WAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,MAAA,CAAA,WAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,MAAA,CAAA,SAAA,GAAA,KAAA;AACA,OAJA;AAKA,KAvSA;AAwSA;AACA,IAAA,cAzSA,4BAySA;AAAA;;AACA,mDAAA,KAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,YAAA;;AACA,QAAA,MAAA,CAAA,2BAAA,GAAA,KAAA;AACA,OAHA;AAIA,KA9SA;AA+SA,IAAA,YA/SA,0BA+SA;AAAA;;AAEA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,YAAA,GAAA,GAAA,EAAA;;AACA,QAAA,MAAA,CAAA,YAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA,SAFA;;AAGA,+CAAA,GAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,cAAA,IAAA,SAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAKA,WANA,MAMA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAIA;;AACA,UAAA,MAAA,CAAA,YAAA;AACA,SAdA;AAeA,OAxBA,EAwBA,KAxBA,CAwBA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OA7BA;AA8BA,KA/UA;AAiVA,IAAA,UAjVA,sBAiVA,IAjVA,EAiVA;AACA,WAAA,KAAA,CAAA,OAAA,CAAA,kBAAA,CAAA,IAAA;AACA,KAnVA;AAoVA,IAAA,eApVA,2BAoVA,GApVA,EAoVA;AACA,WAAA,YAAA,GAAA,GAAA;AACA,KAtVA;AAuVA,IAAA,UAvVA,sBAuVA,IAvVA,EAuVA;AACA,WAAA,KAAA,CAAA,OAAA,CAAA,kBAAA,CAAA,IAAA;AACA,KAzVA;AA0VA,IAAA,eA1VA,2BA0VA,GA1VA,EA0VA;AACA,WAAA,YAAA,GAAA,GAAA;AACA;AA5VA;AA3KA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      ref=\"filter1\"\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      @handleReset=\"filterReset\"\n    />\n    <el-white class=\"button-group1\">\n<!--      <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\" type=\"card\">\n        <el-tab-pane label=\"个人工具\" name=\"grgj\">\n        </el-tab-pane>\n        <el-tab-pane label=\"公用工具\" name=\"gygj\">\n        </el-tab-pane>\n      </el-tabs>-->\n      <div class=\"button_btn\">\n        <el-button @click=\"addSensorButton\"  v-hasPermi=\"['xlgql:button:add']\" icon=\"el-icon-plus\" type=\"primary\">新增\n<!--                   v-hasPermi=\"['gqjgl:button:insert']\"-->\n        </el-button>\n        <el-button type=\"primary\" icon=\"el-icon-download\" @click=\"exportExcel()\">导出</el-button>\n      </div>\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"selectChange\"\n        height=\"67vh\"\n        @sort-events= \"sortChange\"\n      >\n      <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\" width=\"180\" :resizable=\"false\">\n        <template slot-scope=\"scope\">\n           <el-button  type=\"text\" size=\"small\" @click=\"getGqjInfo(scope.row)\" class=\"el-icon-view\" title=\"详情\"></el-button>\n           <el-button  type=\"text\" size=\"small\" v-if=\"scope.row.createBy==currUser\"  @click=\"updateGqjInfo(scope.row)\" class='el-icon-edit' title=\"编辑\"></el-button>\n           <el-button  type=\"text\" size=\"small\" v-if=\"scope.row.createBy==currUser\"  @click=\"deleteRow(scope.row.objId)\" class=\"el-icon-delete\" title=\"删除\"></el-button>\n        </template>\n      </el-table-column>\n      </comp-table>\n    </el-white>\n    <!-- 工器具台账详情修改新增 -->\n    <el-dialog :title=\"gqjTital\" :visible.sync=\"dialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"60%\" v-dialogDrag>\n      <el-form :model=\"form\" label-width=\"80px\" :disabled=\"isDisabled\" :rules=\"rules\" ref=\"form\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"名称\" prop=\"sbmc\">\n              <el-input v-model=\"form.sbmc\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"规格型号\" prop=\"xh\">\n              <el-input v-model=\"form.xh\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"单位\" prop=\"ssgs\">\n              <el-input v-model=\"form.ssgs\" placeholder=\"\" :disabled=\"isDisabled\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"数量\" prop=\"sl\">\n              <el-input-number :min=\"1\" v-model=\"form.sl\" :disabled=\"isDisabled\"></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\" >\n            <el-form-item label=\"负责人\" prop=\"fzr\">\n              <el-input v-model=\"form.fzr\" placeholder=\"\" :disabled=\"isDisabled\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"生产厂家\" style=\"width: 100%\">\n              <el-input v-model=\"form.sccj\" placeholder=\"\" :disabled=\"isDisabled\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"启用年月\" class=\"add_sy_tyrq\">\n              <el-date-picker\n                  v-model=\"form.tyrq\"\n                  type=\"date\"\n                  placeholder=\"选择日期\"\n                  value-format=\"yyyy-MM-dd\"\n                  :disabled=\"isDisabled\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"存放地点\">\n              <el-input v-model=\"form.cfdd\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\">\n              <el-input type=\"textarea\" v-model=\"form.bz\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n<!--        <el-row :gutter=\"4\" class=\"pull-left\">\n          <el-col :span=\"20\">\n            <el-form-item label=\"附件:\" prop=\"attachmentid\">\n              <template slot-scope=\"scope\">\n                <span><a style=\"color: blue\" @click=\"downloadHandle(form.attachmentid)\">{{ form.attachmentname }}</a>\n                </span>\n                <el-upload\n                  class=\"upload-demo\"\n                  accept=\".jpg,.png,.rar,.txt,.zip,.doc,.ppt,.xls,.pdf,.docx,.xlsx,.mp4,.avi,.rmvb\"\n                  ref=\"uploadGqjInfo\"\n                  action=\"/isc-api/file/upload\"\n                  :before-upload=\"gqjInfoBeforeUpload\"\n                  :on-success=\"gqjInfoonSuccess\"\n                  :on-remove=\"gqjInfohandleRemove\"\n                  :data=\"gqjInfoUploadData\"\n                  :headers=\"gqjInfoUpHeader\"\n                  :auto-upload=\"false\">\n                  <el-button slot=\"trigger\" size=\"small\" type=\"primary\">选取文件</el-button>\n                  <el-button style=\"margin-left: 10px;\" size=\"small\" type=\"success\" @click=\"gqjInfoSubmitUpload\">上传到服务器\n                  </el-button>\n                </el-upload>\n              </template>\n            </el-form-item>\n          </el-col>\n        </el-row>-->\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"qxcommit\" v-if=\"gqjTital === '工器具新增' || gqjTital === '工器具修改' \" class=\"pmyBtn\">\n          确 定\n        </el-button>\n      </div>\n    </el-dialog>\n    <!--试验报告弹出框-->\n    <el-dialog title=\"试验报告记录\" :visible.sync=\"sybgDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"60%\">\n      <el-form :model=\"form\" label-width=\"80px\">\n        <el-form-item style=\"float: right\">\n          <el-button class=\"mb8\" @click=\"addSyButton\" type=\"primary\" icon=\"el-icon-plus\"\n                     v-hasPermi=\"['gqjgl:button:sydialog:insert']\">添加\n          </el-button>\n          <el-button class=\"mb8\" @click=\"deleteYxSy\" type=\"danger\" icon=\"el-icon-delete\" v-hasPermi=\"['gqjgl:button:sydialog:delete']\">删除\n          </el-button>\n        </el-form-item>\n      </el-form>\n      <el-table\n        ref=\"syTable\"\n        stripe\n        border\n        v-loading=\"syLoading\"\n        :data=\"gqjsyList\"\n        @row-click=\"syRowClick\"\n        @selection-change=\"syCurrentChange\"\n        max-height=\"550\">\n        <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n        <el-table-column label=\"试验单位\" align=\"center\" prop=\"sydwName\"></el-table-column>\n        <el-table-column label=\"试验人员\" align=\"center\" prop=\"syryName\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"试验结论\" align=\"center\" prop=\"syjlName\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"试验时间\" align=\"center\" prop=\"sysj\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column\n          align=\"center\"\n          label=\"操作\"\n          width=\"150\">\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" size=\"small\">下载附件</el-button>\n            <el-button type=\"text\" @click=\"updateSy(scope.row)\" size=\"small\">修改</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      <pagination\n        v-show=\"syQueryForm.total>0\"\n        :total=\"syQueryForm.total\"\n        :page.sync=\"syQueryForm.pageNum\"\n        :limit.sync=\"syQueryForm.pageSize\"\n        @pagination=\"getYxSyData\"\n      />\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"sybgDialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"sybgDialogFormVisible = false\" class=\"pmyBtn\">确 定</el-button>\n      </div>\n    </el-dialog>\n    <!--添加试验报告-->\n    <el-dialog title=\"添加试验报告\" :visible.sync=\"addSybgDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"50%\">\n      <el-form label-width=\"80px\" v-model=\"syFrom\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item hidden>\n              <el-input v-model=\"syFrom.id\"></el-input>\n              <el-input v-model=\"syFrom.gqjId\"></el-input>\n              <el-input v-model=\"syFrom.sydwId\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"试验单位\">\n<!--              <el-select v-model=\"syFrom.sydwName\" placeholder=\"\" :disabled=\"isSyDetail\"></el-select>-->\n              <el-input v-model=\"syFrom.sydwName\" placeholder=\"\" :disabled=\"isSyDetail\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item hidden label=\"试验人员id\">\n              <el-input v-model=\"syFrom.syryId\" hidden></el-input>\n            </el-form-item>\n            <el-form-item label=\"试验人员\">\n              <!--<el-select v-model=\"syFrom.syryName\" placeholder=\"\">\n              </el-select>-->\n              <el-input v-model=\"syFrom.syryName\" :disabled=\"isSyDetail\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"试验时间\" class=\"add_sy_tyrq\">\n              <el-date-picker\n                :disabled=\"isSyDetail\"\n                v-model=\"syFrom.sysj\"\n                type=\"date\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"选择日期时间\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item hidden>\n              <el-input type=\"textarea\" v-model=\"syFrom.syjlCode\" hidden :disabled=\"isSyDetail\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"试验结论\">\n              <el-input type=\"textarea\" v-model=\"syFrom.syjlName\" :disabled=\"isSyDetail\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"20\">\n            <el-form-item label=\"备注\">\n              <el-input type=\"textarea\" v-model=\"syFrom.remark\" :disabled=\"isSyDetail\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n<!--        <el-row :gutter=\"20\">-->\n<!--          <el-upload-->\n<!--            style=\"float: right\"-->\n<!--            class=\"upload-demo\"-->\n<!--            action=\"https://jsonplaceholder.typicode.com/posts/\"-->\n<!--            :on-preview=\"handlePreview\"-->\n<!--            :on-remove=\"handleRemove\"-->\n<!--            :before-remove=\"beforeRemove\"-->\n<!--            multiple-->\n<!--            :limit=\"3\"-->\n<!--            :on-exceed=\"handleExceed\"-->\n<!--            :file-list=\"fileList\">-->\n<!--            <el-button size=\"small\" type=\"primary\">点击上传</el-button>-->\n<!--            <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png文件，且不超过500kb</div>-->\n<!--          </el-upload>-->\n<!--        </el-row>-->\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"addSybgDialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"saveOrUpdateSy\" class=\"pmyBtn\">确 定</el-button>\n      </div>\n    </el-dialog>\n    <!--检修记录-->\n    <!--检修记录弹出框-->\n    <el-dialog title=\"检修维护记录\" :visible.sync=\"jwxDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"60%\">\n      <el-form :model=\"form\" label-width=\"80px\">\n        <el-form-item style=\"float: right\">\n          <el-button class=\"mb8\" @click=\"addJxButton\" type=\"primary\" icon=\"el-icon-plus\"\n                     v-hasPermi=\"['gqjgl:button:sydialog:insert']\">\n            添加\n          </el-button>\n          <el-button class=\"mb8\" @click=\"deleteJxData\" type=\"danger\" icon=\"el-icon-delete\" v-hasPermi=\"['gqjgl:button:sydialog:delete']\">删除\n          </el-button>\n        </el-form-item>\n      </el-form>\n      <el-table\n        ref=\"jxTable\"\n        stripe\n        border\n        v-loading=\"jxLoading\"\n        :data=\"gqjJxList\"\n        @row-click=\"jxRowClick\"\n        @selection-change=\"jxCurrentChange\"\n        max-height=\"550\">\n        <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n        <el-table-column label=\"检修单位\" align=\"center\" prop=\"jxdwName\"></el-table-column>\n        <el-table-column label=\"检修人员\" align=\"center\" prop=\"jxryName\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"检修结果\" align=\"center\" prop=\"jxjg\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"检修时间\" align=\"center\" prop=\"jxsj\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column\n          align=\"center\"\n          label=\"操作\"\n          width=\"150\">\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" size=\"small\">下载附件</el-button>\n            <el-button type=\"text\" @click=\"updateJx(scope.row)\" size=\"small\">修改</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      <pagination\n        v-show=\"jxQueryForm.total>0\"\n        :total=\"jxQueryForm.total\"\n        :page.sync=\"jxQueryForm.pageNum\"\n        :limit.sync=\"jxQueryForm.pageSize\"\n        @pagination=\"getJxRecords\"\n      />\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"jwxDialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"jwxDialogFormVisible = false\" class=\"pmyBtn\">确 定</el-button>\n      </div>\n    </el-dialog>\n    <!--添加检修记录-->\n    <el-dialog title=\"添加检修维护记录\" :visible.sync=\"addJwxSybgDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"50%\">\n      <el-form label-width=\"80px\" :model=\"jxForm\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item hidden>\n              <el-input v-model=\"jxForm.id\"></el-input>\n              <el-input v-model=\"jxForm.gqjId\"></el-input>\n              <el-input v-model=\"jxForm.jxdwId\"></el-input>\n              <el-input v-model=\"jxForm.jxryId\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"检修单位\">\n<!--              <el-select v-model=\"jxForm.jxdwName\" placeholder=\"\"></el-select>-->\n              <el-input v-model=\"jxForm.jxdwName\" placeholder=\"\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"检修人员\">\n<!--              <el-select v-model=\"jxForm.jxryName\" placeholder=\"\"></el-select>-->\n              <el-input v-model=\"jxForm.jxryName\" placeholder=\"\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"检修时间\" class=\"add_sy_tyrq\">\n              <el-date-picker\n                v-model=\"jxForm.jxsj\"\n                type=\"date\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"选择日期时间\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"检修结果\">\n              <el-input type=\"textarea\" v-model=\"jxForm.jxjg\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\">\n              <el-input type=\"textarea\" v-model=\"jxForm.remark\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"addJwxSybgDialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"saveOrUpdateJx\" class=\"pmyBtn\">确 定</el-button>\n      </div>\n    </el-dialog>\n    <!-- 弹出框结束 -->\n  </div>\n\n</template>\n\n<script>\nimport { getUUID } from '@/utils/ruoyi'\nimport {\n  deleteAssetGqjJxRecords,\n  deleteYxSyRecords, exportExcel,\n  getAssetGqjJxRecords,\n  getList,\n  getYxSyRecords,\n  remove,\n  saveOrUpdate,\n  saveOrUpdateAssetGqjJxRecords,\n  saveOrUpdateYxSyRecords\n} from '@/api/dagangOilfield/asset/assetGqj'\nimport CompTable from 'com/CompTable'\nimport ElFilter from 'com/ElFilter'\nimport { download } from '@/api/tool/file'\n\nexport default {\n    components: {CompTable, ElFilter},\n    name: \"gqjgl\",\n    data() {\n      return {\n        currUser:this.$store.getters.name,\n        // 表单校验\n        rules: {\n          sl: [{ required: true, message: '请输入', trigger: 'blur' }],\n          sbmc: [{ required: true, message: '请输入', trigger: 'blur' }],\n          xh: [{ required: true, message: '请输入', trigger: 'blur' }],\n          fzr: [{ required: true, message: '请输入', trigger: 'blur' }],\n        },\n        activeName:\"grgj\",\n        params:{\n          type:\"xl\"\n        },\n        //工器具详情框字段控制\n        isDisabled: false,\n        //工器具弹出框表头\n        gqjTital: \"工器具新增\",\n\n        //表格内容\n        tableAndPageInfo: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n            {prop: 'sbmc', label: '名称', minWidth: '180',custom:true},\n            {prop: 'xh', label: '规格型号', minWidth: '180'},\n            {prop: 'fzr', label: '负责人', minWidth: '120'},\n            {prop: 'ssgs', label: '单位', minWidth: '120'},\n            {prop: 'sl', label: '数量', minWidth: '50'},\n            {prop: 'sccj', label: '生产厂家', minWidth: '120'},\n            {prop: 'tyrq', label: '启用年月', minWidth: '120',custom:true},\n            {prop: 'cfdd', label: '存放地点', minWidth: '250'},\n            // {\n            //   fixed: \"right\",\n            //   prop: 'operation',\n            //   label: '操作',\n            //   minWidth: '150px',\n            //   style: {display: 'block'},\n            //   operation: [\n            //     // {name: '试验', clickFun: this.handleSearchSYClick},\n            //     // {name: '检修', clickFun: this.handleSerchJWXClick},\n            //     {name: '修改', clickFun: this.updateGqjInfo},\n            //     {name: '详情', clickFun: this.getGqjInfo},\n            //   ],\n            // },\n          ]\n        },\n        //筛选条件\n        filterInfo: {\n          data: {\n            fzr: '',\n            ssgs: '',\n            yxbz: '',\n            phone: '',\n          },\n          fieldList: [\n            {label: '名称', type: 'input', value: 'sbmc'},\n            {label: '规格型号', type: 'input', value: 'xh'},\n            {label: '负责人', type: 'input', value: 'fzr'},\n            {label: '存放地点', type: 'input', value: 'cfdd'},\n          ]\n        },\n        //检修记录弹出框\n        jwxDialogFormVisible: false,\n        //添加检修记录弹出框\n        addJwxSybgDialogFormVisible: false,\n        //工器具弹出框\n        dialogFormVisible: false,\n        //试验时间\n        sysj: '',\n        fildtps: [],\n        //试验弹出框\n        sybgDialogFormVisible: false,\n        //添加试验报告\n        addSybgDialogFormVisible: false,\n        //弹出框表单\n        form: {\n          type:\"xl\"\n        },\n        loading: false,\n        //工器具试验数据集合\n        gqjsyList: [],\n        //检修数据集合\n        gqjJxList:[],\n        //删除是否可用\n        multipleSensor: true,\n        showSearch: true,\n        //删除选择列\n        selectRows: [],\n        //工器具文件上传参数\n        gqjInfoUploadData: {\n          businessId: undefined,\n        },\n        //工器具文件上传请求头\n        gqjInfoUpHeader: {},\n\n        //试验查询条件\n        syQueryForm: {\n          gqjId: '',\n          total: 0,\n          pageSize: 10,\n          pageNum: 1\n        },\n\n        //试验新增表单数据\n        syFrom: {\n          id: '',\n          gqjId: '',\n          sydwId: '',\n          sydwName: '',\n          syryId: '',\n          syryName: '',\n          sysj: '',\n          syjlCode: '',\n          syjlName: '',\n          remark: ''\n        },\n\n        isSyDetail:false,\n\n        //检修查询条件\n        jxQueryForm: {\n          gqjId: '',\n          total: 0,\n          pageSize: 10,\n          pageNum: 1\n        },\n        //检修表单\n        jxForm: {\n          id: '',\n          jxdwId: '',\n          jxdwName: '',\n          jxryId: '',\n          jxryName: '',\n          jxjg: '',\n          jxsj: '',\n          remark: '',\n          gqjId: ''\n        },\n\n        //主表选中行数据\n        mainRowData: {},\n        //试验table加载\n        syLoading: false,\n        //试验选中行\n        sySelectRows: [],\n        //检修table加载\n        jxLoading: false,\n        //检修选中行\n        jxSelectRows: []\n      };\n    },\n    watch: {},\n    created() {\n\n    },\n    mounted() {\n      this.getData();\n    },\n    methods: {\n      exportExcel(){\n        exportExcel(this.params,'线路工器具')\n      },\n      sortChange({column, prop, order}){\n        if (order){\n          if (order.indexOf(\"desc\")>-1){\n            this.params.mySorts = [{prop:prop,asc:false}]\n          }else {\n            this.params.mySorts = [{prop:prop,asc:true}]\n          }\n        }else {\n          this.params.mySorts = [{prop:'updateTime',asc:false}]\n        }\n        this.getData();\n      },\n      handleClick() {\n        this.getData();\n      },\n      /**下载附件*/\n      downloadHandle(id) {\n        download(id)\n      },\n      /**\n       * 上传附附件之前的处理函数\n       * @param file\n       */\n      gqjInfoBeforeUpload(file) {\n        const fileSize = file.size < 1024 * 1024 * 50 //10M\n        if (!fileSize) {\n          this.$message.error('上传文件大小不能超过 50MB!')\n        }\n        let size = file.size / 1024\n      },\n      /**\n       * 上传附件成功调用的函数\n       * @param response\n       * @param file\n       * @param fileList\n       */\n      gqjInfoonSuccess(response, file, fileList) {\n        console.log(\"response:\", response)\n        console.log(\"file:\", file)\n        console.log(\"fileList:\", fileList)\n\n        //文件id\n        this.form.attachmentid = response.data.businessId\n        //文件名称\n        this.form.attachmentname = response.data.sysFile.fileOldName\n      },\n\n      /**\n       * 移除文件\n       * @param file\n       * @param fileList\n       */\n      gqjInfohandleRemove(file, fileList) {\n      console.log(\"删除文件狗子++++++++++++++++\")\n        console.log(file);\n      console.log(fileList)\n      },\n      /**\n       * 工器具上传文件到服务器\n       */\n      gqjInfoSubmitUpload(){\n        debugger\n        this.gqjInfoUploadData.businessId = getUUID()\n        this.$refs.uploadGqjInfo.submit();\n      },\n\n      //工器具列表查询\n      async getData(params) {\n        try {\n          const param = {...this.params, ...params}\n          //param.isPublic = this.activeName;\n          const {data, code} = await getList(param);\n          if (code === '0000') {\n            this.tableAndPageInfo.tableData = data.records\n            this.tableAndPageInfo.pager.total = data.total\n          }\n        } catch (e) {\n          console.log(e)\n        }\n      },\n\n      //工器具列表新增按钮\n      addSensorButton() {\n        this.isDisabled = false;\n        //打开弹出框\n        this.dialogFormVisible = true;\n        //设置弹出框表头\n        this.gqjTital = \"工器具新增\";\n        //清空弹出框内容\n        this.form = {\n          isPublic:this.activeName,\n          type:\"xl\"\n        };\n      },\n      //工器具列表详情按钮\n      getGqjInfo(row) {\n        //打开弹出框\n        this.dialogFormVisible = true;\n        //设置弹出框表头\n        this.gqjTital = \"工器具详情\";\n        //禁用所有输入框\n        this.isDisabled = true;\n        //给弹出框赋值\n        this.form = {...row}\n      },\n      //工器具修改按钮\n      updateGqjInfo(row) {\n        //打开弹出框\n        this.dialogFormVisible = true;\n        //设置弹出框表头\n        this.gqjTital = \"工器具修改\";\n        //开启弹出框内输入框编辑权限\n        this.isDisabled = false;\n        //给弹出框内赋值\n        this.form = {...row};\n\n      },\n      //工器具列表新增修改保存\n      async qxcommit() {\n        await this.$refs['form'].validate(async (valid) => {\n          if (valid) {\n            try {\n              let {code} = await saveOrUpdate(this.form)\n              if (code === '0000') {\n                this.$message.success(\"操作成功\")\n              }\n            } catch (e) {\n              console.log(e)\n            }\n            //恢复分页\n            this.tableAndPageInfo.pager.pageResize = 'Y';\n            this.getData();\n            this.dialogFormVisible = false;\n          }})\n      },\n      //删除工器具列表\n      deleteRow(id) {\n        // if (this.selectRows.length < 1) {\n        //   this.$message.warning(\"请选择正确的数据！！！\")\n        //   return\n        // }\n        // let ids = this.selectRows.map(item => {\n        //   return item.objId\n        // });\n        let ids=[];\n        ids.push(id);\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove(ids).then(({code}) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              });\n              this.getData()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              });\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          });\n        });\n        this.tableAndPageInfo.pager.pageResize = 'Y';\n        this.getData()\n      },\n\n      //查看试验\n      handleSearchSYClick(row) {\n        this.sySelectRows = []\n        this.mainRowData = row\n        this.syQueryForm.gqjId = row.objId\n        this.sybgDialogFormVisible = true\n        this.getYxSyData()\n      },\n\n      //查看检修\n      handleSerchJWXClick(row) {\n        this.mainRowData = row\n        this.jxQueryForm.gqjId = row.objId\n        this.jwxDialogFormVisible = true\n        this.getJxRecords()\n      },\n      //添加检修\n      addJxButton() {\n        this.jxForm = this.$options.data().jxForm\n        this.jxForm.gqjId = this.mainRowData.objId\n        this.addJwxSybgDialogFormVisible = true\n      },\n      updateJx(row) {\n        this.jxForm = row\n        this.addJwxSybgDialogFormVisible = true\n      },\n      //添加试验\n      addSyButton() {\n        this.syFrom = this.$options.data().syFrom\n        this.syFrom.gqjId = this.mainRowData.objId\n        this.addSybgDialogFormVisible = true\n      },\n      updateSy(row) {\n        this.syFrom = row\n        this.addSybgDialogFormVisible = true\n      },\n      //每页展示数量点击事件\n      handleSizeChange() {\n\n      },\n      //页码改变事件\n      handleCurrentChange() {\n\n      },\n      //树点击事件\n      handleNodeClick() {\n\n      },\n\n      filterReset() {\n\n      },\n      //选择每一行\n      selectChange(rows) {\n        this.selectRows = rows\n      },\n\n      //获取试验记录数据\n      getYxSyData() {\n        this.syLoading = true\n        getYxSyRecords(this.syQueryForm).then(res => {\n          this.gqjsyList = res.data.records\n          this.syQueryForm.total = res.data.total\n          this.syLoading = false\n        })\n      },\n\n      //新增修改试验记录数据\n      saveOrUpdateSy() {\n        saveOrUpdateYxSyRecords(this.syFrom).then(res => {\n          this.getYxSyData()\n          this.addSybgDialogFormVisible = false\n        })\n      },\n      //批量删除试验数据\n      deleteYxSy() {\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          let ids = []\n          this.sySelectRows.forEach(item => {\n            ids.push(item.id)\n          })\n          deleteYxSyRecords(ids).then(({ code }) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n            }\n            this.getYxSyData()\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n\n      //获取检修记录数据\n      getJxRecords() {\n        this.jxLoading = true\n        getAssetGqjJxRecords(this.jxQueryForm).then(res => {\n          this.gqjJxList = res.data.records\n          this.jxQueryForm.total = res.data.total\n          this.jxLoading = false\n        })\n      },\n      //新增修改检修记录数据\n      saveOrUpdateJx() {\n        saveOrUpdateAssetGqjJxRecords(this.jxForm).then(res => {\n          this.getJxRecords()\n          this.addJwxSybgDialogFormVisible = false\n        })\n      },\n      deleteJxData() {\n\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          let ids = []\n          this.jxSelectRows.forEach(item => {\n            ids.push(item.id)\n          })\n          deleteAssetGqjJxRecords(ids).then(({ code }) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n            }\n            this.getJxRecords()\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n\n      syRowClick(rows) {\n        this.$refs.syTable.toggleRowSelection(rows)\n      },\n      syCurrentChange(val) {\n        this.sySelectRows = val\n      },\n      jxRowClick(rows) {\n        this.$refs.jxTable.toggleRowSelection(rows)\n      },\n      jxCurrentChange(val) {\n        this.jxSelectRows = val\n      }\n    }\n  };\n</script>\n\n<style lang='scss' scoped>\n\n  .button-group {\n    padding-left: 30px;\n    padding-right: 30px;\n    display: flex;\n    justify-content: flex-end;\n  }\n\n  .qxlr_dialog_insert {\n    margin-top: 6vh !important\n  }\n\n  /*/deep/ .qxlr_dialog_insert .el-input--medium .el-input__inner{*/\n  /*  width: 100%;*/\n  /*}*/\n  .el-select {\n    width: 100%;\n  }\n\n  .el-date-editor {\n    width: 100%;\n  }\n</style>\n"], "sourceRoot": "src/views/dagangOilfield/dwzygl/gqjgl"}]}