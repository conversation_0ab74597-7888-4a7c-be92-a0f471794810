{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\gqjgl\\gfgqj.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\gqjgl\\gfgqj.vue", "mtime": 1752489170836}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFVVSUQgfSBmcm9tICJAL3V0aWxzL3J1b3lpIjsKaW1wb3J0IHsKICBkZWxldGVBc3NldEdxakp4UmVjb3JkcywKICBkZWxldGVZeFN5UmVjb3JkcywKICBnZXRBc3NldEdxakp4UmVjb3JkcywKICBnZXRMaXN0LAogIGdldFl4U3lSZWNvcmRzLAogIHJlbW92ZSwKICBzYXZlT3JVcGRhdGUsCiAgc2F2ZU9yVXBkYXRlQXNzZXRHcWpKeFJlY29yZHMsCiAgc2F2ZU9yVXBkYXRlWXhTeVJlY29yZHMsCiAgZXhwb3J0RXhjZWwsCiAgZ2V0R2ZCZHpTZWxlY3RMaXN0Cn0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYXNzZXQvYXNzZXRHcWoiOwppbXBvcnQgQ29tcFRhYmxlIGZyb20gImNvbS9Db21wVGFibGUiOwppbXBvcnQgRWxGaWx0ZXIgZnJvbSAiY29tL0VsRmlsdGVyIjsKaW1wb3J0IHsgZ2V0RGljdFR5cGVEYXRhIH0gZnJvbSAiQC9hcGkvc3lzdGVtL2RpY3QvZGF0YSI7CmltcG9ydCB7IGdldEZnc09wdGlvbnMgfSBmcm9tICJAL2FwaS95eGdsL2dmeXhnbC9nZnpiZ2wiOwoKCmV4cG9ydCBkZWZhdWx0IHsKICBjb21wb25lbnRzOiB7IENvbXBUYWJsZSwgRWxGaWx0ZXIgfSwKICBuYW1lOiAiZ3FqZ2wiLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBnZmdxamx4OiBbXSwKICAgICAgY3VyclVzZXI6IHRoaXMuJHN0b3JlLmdldHRlcnMubmFtZSwKICAgICAgcGFyYW1zOiB7CiAgICAgICAgdHlwZTogImdmIgogICAgICB9LAogICAgICBiZHpBbGxMaXN0OiBbXSwKICAgICAgYmR6TGlzdDogW10sCiAgICAgIC8v57uE57uH57uT5p6E5LiL5ouJ5pWw5o2uCiAgICAgIG9yZ2FuaXphdGlvblNlbGVjdGVkTGlzdDogW10sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczogewogICAgICAgIHNsOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+i+k+WFpSIsIHRyaWdnZXI6ICJibHVyIiB9XSwKICAgICAgICBzc2dzOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqSIsIHRyaWdnZXI6ICJibHVyIiB9XSwKICAgICAgICAvLyBiZHo6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oupJywgdHJpZ2dlcjogJ2JsdXInIH1dLAogICAgICAgIHNibWM6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWlIiwgdHJpZ2dlcjogImJsdXIiIH1dLAogICAgICAgIHNibHg6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oupIiwgdHJpZ2dlcjogImJsdXIiIH1dLAogICAgICAgIGZ6cjogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fovpPlhaUiLCB0cmlnZ2VyOiAiYmx1ciIgfV0KICAgICAgfSwKICAgICAgLy/lt6Xlmajlhbfor6bmg4XmoYblrZfmrrXmjqfliLYKICAgICAgaXNEaXNhYmxlZDogZmFsc2UsCiAgICAgIC8v5bel5Zmo5YW35by55Ye65qGG6KGo5aS0CiAgICAgIGdxalRpdGFsOiAi5bel5Zmo5YW35paw5aKeIiwKICAgICAgLy/ooajmoLzlhoXlrrkKICAgICAgdGFibGVBbmRQYWdlSW5mbzogewogICAgICAgIHBhZ2VyOiB7CiAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgdG90YWw6IDAsCiAgICAgICAgICBzaXplczogWzEwLCAyMCwgNTAsIDEwMF0KICAgICAgICB9LAogICAgICAgIG9wdGlvbjogewogICAgICAgICAgY2hlY2tCb3g6IHRydWUsCiAgICAgICAgICBzZXJpYWxOdW1iZXI6IHRydWUKICAgICAgICB9LAogICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgdGFibGVIZWFkZXI6IFsKICAgICAgICAgIHsgcHJvcDogInNzZ3NtYyIsIGxhYmVsOiAi5omA5bGe5YWs5Y+4IiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJiZHptYyIsIGxhYmVsOiAi5omA5bGe5YWJ5LyP55S156uZIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJzYm1jIiwgbGFiZWw6ICLlkI3np7AiLCBtaW5XaWR0aDogIjE4MCIgfSwKICAgICAgICAgIHsgcHJvcDogInhoIiwgbGFiZWw6ICLop4TmoLzlnovlj7ciLCBtaW5XaWR0aDogIjE4MCIgfSwKICAgICAgICAgIHsgcHJvcDogImZ6ciIsIGxhYmVsOiAi6aKG55So5Lq6IiwgbWluV2lkdGg6ICIxODAiIH0sCiAgICAgICAgICAvLyB7cHJvcDogJ2NjYmgnLCBsYWJlbDogJ+e8luWPtycsIG1pbldpZHRoOiAnMTgwJ30sCiAgICAgICAgICB7IHByb3A6ICJqeXpxIiwgbGFiZWw6ICLkvb/nlKjlubTpmZAiLCBtaW5XaWR0aDogIjE4MCIgfSwKICAgICAgICAgIHsgcHJvcDogInR5cnEiLCBsYWJlbDogIumihuWPluaXtumXtCIsIG1pbldpZHRoOiAiMjUwIiB9CiAgICAgICAgICAvLyB7CiAgICAgICAgICAvLyAgIGZpeGVkOiAicmlnaHQiLAogICAgICAgICAgLy8gICBwcm9wOiAnb3BlcmF0aW9uJywKICAgICAgICAgIC8vICAgbGFiZWw6ICfmk43kvZwnLAogICAgICAgICAgLy8gICBtaW5XaWR0aDogJzE1MHB4JywKICAgICAgICAgIC8vICAgc3R5bGU6IHtkaXNwbGF5OiAnYmxvY2snfSwKICAgICAgICAgIC8vICAgb3BlcmF0aW9uOiBbCiAgICAgICAgICAvLyAgICAge25hbWU6ICfkv67mlLknLCBjbGlja0Z1bjogdGhpcy51cGRhdGVHcWpJbmZvfSwKICAgICAgICAgIC8vICAgICB7bmFtZTogJ+ivpuaDhScsIGNsaWNrRnVuOiB0aGlzLmdldEdxakluZm99LAogICAgICAgICAgLy8gICBdLAogICAgICAgICAgLy8gfSwKICAgICAgICBdCiAgICAgIH0sCiAgICAgIC8v562b6YCJ5p2h5Lu2CiAgICAgIGZpbHRlckluZm86IHsKICAgICAgICBkYXRhOiB7CiAgICAgICAgICBmenI6ICIiLAogICAgICAgICAgc3NnczogIiIsCiAgICAgICAgICB5eGJ6OiAiIiwKICAgICAgICAgIHBob25lOiAiIgogICAgICAgIH0sCiAgICAgICAgZmllbGRMaXN0OiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi5YiG5YWs5Y+4IiwKICAgICAgICAgICAgdmFsdWU6ICJzc2dzIiwKICAgICAgICAgICAgdHlwZTogInNlbGVjdCIsCiAgICAgICAgICAgIG9wdGlvbnM6IFtdLAogICAgICAgICAgICBjbGVhcmFibGU6IHRydWUKICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi5YWJ5LyP55S156uZIiwKICAgICAgICAgICAgdmFsdWU6ICJiZHoiLAogICAgICAgICAgICB0eXBlOiAic2VsZWN0IiwKICAgICAgICAgICAgb3B0aW9uczogW10sCiAgICAgICAgICAgIGNsZWFyYWJsZTogdHJ1ZQogICAgICAgICAgfSwKICAgICAgICAgIHsgbGFiZWw6ICLlkI3np7AiLCB0eXBlOiAiaW5wdXQiLCB2YWx1ZTogInNibWMiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi6KeE5qC85Z6L5Y+3IiwgdHlwZTogImlucHV0IiwgdmFsdWU6ICJ4aCIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLpoobnlKjkuroiLCB0eXBlOiAiaW5wdXQiLCB2YWx1ZTogImZ6ciIgfQogICAgICAgICAgLy8ge2xhYmVsOiAn5oqV6L+Q5pel5pyfJywgdHlwZTogJ2RhdGUnLCB2YWx1ZTogJ3R5cnFBcnInLGRhdGVUeXBlOiAnZGF0ZXJhbmdlJyxmb3JtYXQ6ICd5eXl5LU1NLWRkJ30sCiAgICAgICAgXQogICAgICB9LAogICAgICAvL+ajgOS/ruiusOW9leW8ueWHuuahhgogICAgICBqd3hEaWFsb2dGb3JtVmlzaWJsZTogZmFsc2UsCiAgICAgIC8v5re75Yqg5qOA5L+u6K6w5b2V5by55Ye65qGGCiAgICAgIGFkZEp3eFN5YmdEaWFsb2dGb3JtVmlzaWJsZTogZmFsc2UsCiAgICAgIC8v5bel5Zmo5YW35by55Ye65qGGCiAgICAgIGRpYWxvZ0Zvcm1WaXNpYmxlOiBmYWxzZSwKICAgICAgLy/or5Xpqozml7bpl7QKICAgICAgc3lzajogIiIsCiAgICAgIGZpbGR0cHM6IFtdLAogICAgICAvL+ivlemqjOW8ueWHuuahhgogICAgICBzeWJnRGlhbG9nRm9ybVZpc2libGU6IGZhbHNlLAogICAgICAvL+a3u+WKoOivlemqjOaKpeWRigogICAgICBhZGRTeWJnRGlhbG9nRm9ybVZpc2libGU6IGZhbHNlLAogICAgICAvL+W8ueWHuuahhuihqOWNlQogICAgICBmb3JtOiB7CiAgICAgICAgdHlwZTogImdmIgogICAgICB9LAogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgLy/lt6Xlmajlhbfor5XpqozmlbDmja7pm4blkIgKICAgICAgZ3Fqc3lMaXN0OiBbXSwKICAgICAgLy/mo4Dkv67mlbDmja7pm4blkIgKICAgICAgZ3FqSnhMaXN0OiBbXSwKICAgICAgLy/liKDpmaTmmK/lkKblj6/nlKgKICAgICAgbXVsdGlwbGVTZW5zb3I6IHRydWUsCiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8v5Yig6Zmk6YCJ5oup5YiXCiAgICAgIHNlbGVjdFJvd3M6IFtdLAogICAgICAvL+W3peWZqOWFt+aWh+S7tuS4iuS8oOWPguaVsAogICAgICBncWpJbmZvVXBsb2FkRGF0YTogewogICAgICAgIGJ1c2luZXNzSWQ6IHVuZGVmaW5lZAogICAgICB9LAogICAgICAvL+W3peWZqOWFt+aWh+S7tuS4iuS8oOivt+axguWktAogICAgICBncWpJbmZvVXBIZWFkZXI6IHt9LAoKICAgICAgLy/or5Xpqozmn6Xor6LmnaHku7YKICAgICAgc3lRdWVyeUZvcm06IHsKICAgICAgICBncWpJZDogIiIsCiAgICAgICAgdG90YWw6IDAsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIHBhZ2VOdW06IDEKICAgICAgfSwKCiAgICAgIC8v6K+V6aqM5paw5aKe6KGo5Y2V5pWw5o2uCiAgICAgIHN5RnJvbTogewogICAgICAgIGlkOiAiIiwKICAgICAgICBncWpJZDogIiIsCiAgICAgICAgc3lkd0lkOiAiIiwKICAgICAgICBzeWR3TmFtZTogIiIsCiAgICAgICAgc3lyeUlkOiAiIiwKICAgICAgICBzeXJ5TmFtZTogIiIsCiAgICAgICAgc3lzajogIiIsCiAgICAgICAgc3lqbENvZGU6ICIiLAogICAgICAgIHN5amxOYW1lOiAiIiwKICAgICAgICByZW1hcms6ICIiCiAgICAgIH0sCgogICAgICBpc1N5RGV0YWlsOiBmYWxzZSwKCiAgICAgIC8v5qOA5L+u5p+l6K+i5p2h5Lu2CiAgICAgIGp4UXVlcnlGb3JtOiB7CiAgICAgICAgZ3FqSWQ6ICIiLAogICAgICAgIHRvdGFsOiAwLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBwYWdlTnVtOiAxCiAgICAgIH0sCiAgICAgIC8v5qOA5L+u6KGo5Y2VCiAgICAgIGp4Rm9ybTogewogICAgICAgIGlkOiAiIiwKICAgICAgICBqeGR3SWQ6ICIiLAogICAgICAgIGp4ZHdOYW1lOiAiIiwKICAgICAgICBqeHJ5SWQ6ICIiLAogICAgICAgIGp4cnlOYW1lOiAiIiwKICAgICAgICBqeGpnOiAiIiwKICAgICAgICBqeHNqOiAiIiwKICAgICAgICByZW1hcms6ICIiLAogICAgICAgIGdxaklkOiAiIgogICAgICB9LAoKICAgICAgLy/kuLvooajpgInkuK3ooYzmlbDmja4KICAgICAgbWFpblJvd0RhdGE6IHt9LAogICAgICAvL+ivlemqjHRhYmxl5Yqg6L29CiAgICAgIHN5TG9hZGluZzogZmFsc2UsCiAgICAgIC8v6K+V6aqM6YCJ5Lit6KGMCiAgICAgIHN5U2VsZWN0Um93czogW10sCiAgICAgIC8v5qOA5L+udGFibGXliqDovb0KICAgICAganhMb2FkaW5nOiBmYWxzZSwKICAgICAgLy/mo4Dkv67pgInkuK3ooYwKICAgICAganhTZWxlY3RSb3dzOiBbXQogICAgfTsKICB9LAogIHdhdGNoOiB7fSwKICBjcmVhdGVkKCkgewogICAgLy/ojrflj5bnu4Tnu4fnu5PmnoTkuIvmi4nmlbDmja4KICAgIHRoaXMuZ2V0RmdzT3B0aW9ucygpOwogIH0sCiAgbW91bnRlZCgpIHsKICAgIGdldEdmQmR6U2VsZWN0TGlzdCh7fSkudGhlbihhc3luYyByZXMgPT4gewogICAgICB0aGlzLmJkekFsbExpc3QgPSByZXMuZGF0YTsKICAgICAgbGV0IHsgZGF0YTogZ2ZncWpseCB9ID0gYXdhaXQgZ2V0RGljdFR5cGVEYXRhKCJnZmdxamx4Iik7CiAgICAgIHRoaXMuZ2ZncWpseCA9IGdmZ3FqbHg7CiAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgfSk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKioKICAgICAqIOiOt+WPluWIhuWFrOWPuOS4i+aLieaVsOaNrgogICAgICovCiAgICBnZXRGZ3NPcHRpb25zKCkgewogICAgICBnZXRGZ3NPcHRpb25zKHt9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgcmVzLmRhdGEuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIGl0ZW0udmFsdWUgPSBpdGVtLnZhbHVlLnRvU3RyaW5nKCk7CiAgICAgICAgfSk7CiAgICAgICAgdGhpcy5vcmdhbml6YXRpb25TZWxlY3RlZExpc3QgPSByZXMuZGF0YTsKICAgICAgICB0aGlzLmZpbHRlckluZm8uZmllbGRMaXN0Lm1hcChpdGVtID0+IHsKICAgICAgICAgIGlmIChpdGVtLnZhbHVlID09ICJzc2dzIikgewogICAgICAgICAgICByZXR1cm4gKGl0ZW0ub3B0aW9ucyA9IHRoaXMub3JnYW5pemF0aW9uU2VsZWN0ZWRMaXN0KTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgZXhwb3J0RXhjZWwoKSB7CiAgICAgIGV4cG9ydEV4Y2VsKHRoaXMucGFyYW1zLCAi5YWJ5LyP5bel5Zmo5YW3Iik7CiAgICB9LAogICAgZm9ybWF0QmR6KGlkKSB7CiAgICAgIGlmIChpZCkgewogICAgICAgIHJldHVybiB0aGlzLmJkekFsbExpc3QuZmlsdGVyKGcgPT4gZy52YWx1ZSA9PT0gaWQpWzBdLmxhYmVsOwogICAgICB9IGVsc2UgewogICAgICAgIHJldHVybiAiIjsKICAgICAgfQogICAgfSwKICAgIC8v6I635Y+W5YWJ5LyP55S156uZ5LiL5ouJ5qGGCiAgICBnZXRCZHpMaXN0KCkgewogICAgICBnZXRHZkJkelNlbGVjdExpc3QoeyBzc2R3Ym06IHRoaXMuZm9ybS5zc2dzIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmJkekxpc3QgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAoKICAgIGhhbmRsZUV2ZW50KHZhbCwgZXZlbnRWYWx1ZSkgewogICAgICBpZiAodmFsLmxhYmVsID09PSAic3NncyIpIHsKICAgICAgICBnZXRHZkJkelNlbGVjdExpc3QoeyBzc2R3Ym06IHZhbC52YWx1ZS50b1N0cmluZygpIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgIHRoaXMuZmlsdGVySW5mby5maWVsZExpc3QubWFwKGl0ZW0gPT4gewogICAgICAgICAgICBpZiAoaXRlbS52YWx1ZSA9PSAiYmR6IikgewogICAgICAgICAgICAgIHRoaXMuJHNldChldmVudFZhbHVlLCAiYmR6IiwgIiIpOwogICAgICAgICAgICAgIHJldHVybiAoaXRlbS5vcHRpb25zID0gcmVzLmRhdGEpOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9KTsKICAgICAgfQogICAgfSwKICAgIC8qKgogICAgICog5LiK5Lyg6ZmE6ZmE5Lu25LmL5YmN55qE5aSE55CG5Ye95pWwCiAgICAgKiBAcGFyYW0gZmlsZQogICAgICovCiAgICBncWpJbmZvQmVmb3JlVXBsb2FkKGZpbGUpIHsKICAgICAgY29uc3QgZmlsZVNpemUgPSBmaWxlLnNpemUgPCAxMDI0ICogMTAyNCAqIDUwOyAvLzEwTQogICAgICBpZiAoIWZpbGVTaXplKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5LiK5Lyg5paH5Lu25aSn5bCP5LiN6IO96LaF6L+HIDUwTUIhIik7CiAgICAgIH0KICAgICAgbGV0IHNpemUgPSBmaWxlLnNpemUgLyAxMDI0OwogICAgfSwKICAgIC8qKgogICAgICog5LiK5Lyg6ZmE5Lu25oiQ5Yqf6LCD55So55qE5Ye95pWwCiAgICAgKiBAcGFyYW0gcmVzcG9uc2UKICAgICAqIEBwYXJhbSBmaWxlCiAgICAgKiBAcGFyYW0gZmlsZUxpc3QKICAgICAqLwogICAgZ3FqSW5mb29uU3VjY2VzcyhyZXNwb25zZSwgZmlsZSwgZmlsZUxpc3QpIHsKICAgICAgLy/mlofku7ZpZAogICAgICB0aGlzLmZvcm0uYXR0YWNobWVudGlkID0gcmVzcG9uc2UuZGF0YS5idXNpbmVzc0lkOwogICAgICAvL+aWh+S7tuWQjeensAogICAgICB0aGlzLmZvcm0uYXR0YWNobWVudG5hbWUgPSByZXNwb25zZS5kYXRhLnN5c0ZpbGUuZmlsZU9sZE5hbWU7CiAgICB9LAoKICAgIC8qKgogICAgICog56e76Zmk5paH5Lu2CiAgICAgKiBAcGFyYW0gZmlsZQogICAgICogQHBhcmFtIGZpbGVMaXN0CiAgICAgKi8KICAgIGdxakluZm9oYW5kbGVSZW1vdmUoZmlsZSwgZmlsZUxpc3QpIHt9LAogICAgLyoqCiAgICAgKiDlt6XlmajlhbfkuIrkvKDmlofku7bliLDmnI3liqHlmagKICAgICAqLwogICAgZ3FqSW5mb1N1Ym1pdFVwbG9hZCgpIHsKICAgICAgZGVidWdnZXI7CiAgICAgIHRoaXMuZ3FqSW5mb1VwbG9hZERhdGEuYnVzaW5lc3NJZCA9IGdldFVVSUQoKTsKICAgICAgdGhpcy4kcmVmcy51cGxvYWRHcWpJbmZvLnN1Ym1pdCgpOwogICAgfSwKICAgIGZvcm1hdFNzZ3Moc3NncykgewogICAgICBpZiAoc3NncyAmJiBzc2dzICE9PSAiMzAwMiIpIHsKICAgICAgICByZXR1cm4gdGhpcy5vcmdhbml6YXRpb25TZWxlY3RlZExpc3QuZmlsdGVyKGcgPT4gZy52YWx1ZSA9PT0gc3NncylbMF0KICAgICAgICAgIC5sYWJlbDsKICAgICAgfSBlbHNlIHsKICAgICAgICByZXR1cm4gIua4r+S4nOWFieS8j+eUteWIhuWFrOWPuCI7CiAgICAgIH0KICAgIH0sCiAgICAvL+W3peWZqOWFt+WIl+ihqOafpeivogogICAgYXN5bmMgZ2V0RGF0YShwYXJhbXMpIHsKICAgICAgICB0aGlzLnBhcmFtcyA9IHsgLi4udGhpcy5wYXJhbXMsIC4uLnBhcmFtcyB9OwogICAgICAgIGNvbnN0IHsgZGF0YSwgY29kZSB9ID0gYXdhaXQgZ2V0TGlzdCh0aGlzLnBhcmFtcyk7CiAgICAgICAgaWYgKGNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgZm9yIChsZXQgaSBvZiBkYXRhLnJlY29yZHMpIHsKICAgICAgICAgICAgaS5zc2dzbWMgPSB0aGlzLmZvcm1hdFNzZ3MoaS5zc2dzKTsKICAgICAgICAgICAgaS5iZHptYyA9IHRoaXMuZm9ybWF0QmR6KGkuYmR6KTsKICAgICAgICAgIH0KICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby50YWJsZURhdGEgPSBkYXRhLnJlY29yZHM7CiAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIudG90YWwgPSBkYXRhLnRvdGFsOwogICAgICAgIH0KICAgIH0sCgogICAgLy/lt6XlmajlhbfliJfooajmlrDlop7mjInpkq4KICAgIGFkZFNlbnNvckJ1dHRvbigpIHsKICAgICAgLy/lvIDlkK/lvLnlh7rmoYblhoXovpPlhaXmoYbnvJbovpHmnYPpmZAKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gZmFsc2U7CiAgICAgIC8v5omT5byA5by55Ye65qGGCiAgICAgIHRoaXMuZGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgICAvL+iuvue9ruW8ueWHuuahhuihqOWktAogICAgICB0aGlzLmdxalRpdGFsID0gIuW3peWZqOWFt+aWsOWiniI7CiAgICAgIC8v5riF56m65by55Ye65qGG5YaF5a65CiAgICAgIHRoaXMuZm9ybSA9IHsKICAgICAgICB0eXBlOiAiZ2YiCiAgICAgIH07CiAgICB9LAogICAgLy/lt6XlmajlhbfliJfooajor6bmg4XmjInpkq4KICAgIGdldEdxakluZm8ocm93KSB7CiAgICAgIC8v5omT5byA5by55Ye65qGGCiAgICAgIHRoaXMuZGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgICAvL+iuvue9ruW8ueWHuuahhuihqOWktAogICAgICB0aGlzLmdxalRpdGFsID0gIuW3peWZqOWFt+ivpuaDhSI7CiAgICAgIC8v56aB55So5omA5pyJ6L6T5YWl5qGGCiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IHRydWU7CiAgICAgIC8v57uZ5by55Ye65qGG6LWL5YC8CiAgICAgIHRoaXMuZm9ybSA9IHsgLi4ucm93IH07CiAgICAgIHRoaXMuZ2V0QmR6TGlzdCgpOwogICAgfSwKICAgIC8v5bel5Zmo5YW35L+u5pS55oyJ6ZKuCiAgICB1cGRhdGVHcWpJbmZvKHJvdykgewogICAgICAvL+aJk+W8gOW8ueWHuuahhgogICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZTsKICAgICAgLy/orr7nva7lvLnlh7rmoYbooajlpLQKICAgICAgdGhpcy5ncWpUaXRhbCA9ICLlt6Xlmajlhbfkv67mlLkiOwogICAgICAvL+W8gOWQr+W8ueWHuuahhuWGhei+k+WFpeahhue8lui+keadg+mZkAogICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZTsKICAgICAgLy/nu5nlvLnlh7rmoYblhoXotYvlgLwKICAgICAgdGhpcy5mb3JtID0geyAuLi5yb3cgfTsKICAgICAgdGhpcy5nZXRCZHpMaXN0KCk7CiAgICB9LAogICAgLy/lt6XlmajlhbfliJfooajmlrDlop7kv67mlLnkv53lrZgKICAgIGFzeW5jIHF4Y29tbWl0KCkgewogICAgICBhd2FpdCB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUoYXN5bmMgdmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgbGV0IHsgY29kZSB9ID0gYXdhaXQgc2F2ZU9yVXBkYXRlKHRoaXMuZm9ybSk7CiAgICAgICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKnyIpOwogICAgICAgICAgICB9CiAgICAgICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgICAgIGNvbnNvbGUubG9nKGUpOwogICAgICAgICAgfQogICAgICAgICAgLy/mgaLlpI3liIbpobUKICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci5wYWdlUmVzaXplID0gIlkiOwogICAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gZmFsc2U7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvL+WIoOmZpOW3peWZqOWFt+WIl+ihqAogICAgZGVsZXRlUm93KGlkKSB7CiAgICAgIC8vIGlmICh0aGlzLnNlbGVjdFJvd3MubGVuZ3RoIDwgMSkgewogICAgICAvLyAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+36YCJ5oup5q2j56Gu55qE5pWw5o2u77yB77yB77yBIikKICAgICAgLy8gICByZXR1cm4KICAgICAgLy8gfQogICAgICAvLyBsZXQgaWRzID0gdGhpcy5zZWxlY3RSb3dzLm1hcChpdGVtID0+IHsKICAgICAgLy8gICByZXR1cm4gaXRlbS5vYmpJZAogICAgICAvLyB9KTsKICAgICAgbGV0IG9iaiA9IFtdOwogICAgICBvYmoucHVzaChpZCk7CiAgICAgIHRoaXMuJGNvbmZpcm0oIuatpOaTjeS9nOWwhuawuOS5heWIoOmZpOivpeaVsOaNriwg5piv5ZCm57un57utPyIsICLmj5DnpLoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KQogICAgICAgIC50aGVuKCgpID0+IHsKICAgICAgICAgIHJlbW92ZShvYmopLnRoZW4oKHsgY29kZSB9KSA9PiB7CiAgICAgICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTmiJDlip8hIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTlpLHotKUhIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoKSA9PiB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogImluZm8iLAogICAgICAgICAgICBtZXNzYWdlOiAi5bey5Y+W5raI5Yig6ZmkIgogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci5wYWdlUmVzaXplID0gIlkiOwogICAgICB0aGlzLmdldERhdGEoKTsKICAgIH0sCgogICAgLy/mn6XnnIvor5XpqowKICAgIGhhbmRsZVNlYXJjaFNZQ2xpY2socm93KSB7CiAgICAgIHRoaXMuc3lTZWxlY3RSb3dzID0gW107CiAgICAgIHRoaXMubWFpblJvd0RhdGEgPSByb3c7CiAgICAgIHRoaXMuc3lRdWVyeUZvcm0uZ3FqSWQgPSByb3cub2JqSWQ7CiAgICAgIHRoaXMuc3liZ0RpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZTsKICAgICAgdGhpcy5nZXRZeFN5RGF0YSgpOwogICAgfSwKCiAgICAvL+afpeeci+ajgOS/rgogICAgaGFuZGxlU2VyY2hKV1hDbGljayhyb3cpIHsKICAgICAgdGhpcy5tYWluUm93RGF0YSA9IHJvdzsKICAgICAgdGhpcy5qeFF1ZXJ5Rm9ybS5ncWpJZCA9IHJvdy5vYmpJZDsKICAgICAgdGhpcy5qd3hEaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CiAgICAgIHRoaXMuZ2V0SnhSZWNvcmRzKCk7CiAgICB9LAogICAgLy/mt7vliqDmo4Dkv64KICAgIGFkZEp4QnV0dG9uKCkgewogICAgICB0aGlzLmp4Rm9ybSA9IHRoaXMuJG9wdGlvbnMuZGF0YSgpLmp4Rm9ybTsKICAgICAgdGhpcy5qeEZvcm0uZ3FqSWQgPSB0aGlzLm1haW5Sb3dEYXRhLm9iaklkOwogICAgICB0aGlzLmFkZEp3eFN5YmdEaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgdXBkYXRlSngocm93KSB7CiAgICAgIHRoaXMuanhGb3JtID0gcm93OwogICAgICB0aGlzLmFkZEp3eFN5YmdEaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgLy/mt7vliqDor5XpqowKICAgIGFkZFN5QnV0dG9uKCkgewogICAgICB0aGlzLnN5RnJvbSA9IHRoaXMuJG9wdGlvbnMuZGF0YSgpLnN5RnJvbTsKICAgICAgdGhpcy5zeUZyb20uZ3FqSWQgPSB0aGlzLm1haW5Sb3dEYXRhLm9iaklkOwogICAgICB0aGlzLmFkZFN5YmdEaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgdXBkYXRlU3kocm93KSB7CiAgICAgIHRoaXMuc3lGcm9tID0gcm93OwogICAgICB0aGlzLmFkZFN5YmdEaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgLy/mr4/pobXlsZXnpLrmlbDph4/ngrnlh7vkuovku7YKICAgIGhhbmRsZVNpemVDaGFuZ2UoKSB7fSwKICAgIC8v6aG156CB5pS55Y+Y5LqL5Lu2CiAgICBoYW5kbGVDdXJyZW50Q2hhbmdlKCkge30sCiAgICAvL+agkeeCueWHu+S6i+S7tgogICAgaGFuZGxlTm9kZUNsaWNrKCkge30sCgogICAgZmlsdGVyUmVzZXQoKSB7fSwKICAgIC8v6YCJ5oup5q+P5LiA6KGMCiAgICBzZWxlY3RDaGFuZ2Uocm93cykgewogICAgICB0aGlzLnNlbGVjdFJvd3MgPSByb3dzOwogICAgfSwKCiAgICAvL+iOt+WPluivlemqjOiusOW9leaVsOaNrgogICAgZ2V0WXhTeURhdGEoKSB7CiAgICAgIHRoaXMuc3lMb2FkaW5nID0gdHJ1ZTsKICAgICAgZ2V0WXhTeVJlY29yZHModGhpcy5zeVF1ZXJ5Rm9ybSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuZ3Fqc3lMaXN0ID0gcmVzLmRhdGEucmVjb3JkczsKICAgICAgICB0aGlzLnN5UXVlcnlGb3JtLnRvdGFsID0gcmVzLmRhdGEudG90YWw7CiAgICAgICAgdGhpcy5zeUxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAoKICAgIC8v5paw5aKe5L+u5pS56K+V6aqM6K6w5b2V5pWw5o2uCiAgICBzYXZlT3JVcGRhdGVTeSgpIHsKICAgICAgc2F2ZU9yVXBkYXRlWXhTeVJlY29yZHModGhpcy5zeUZyb20pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmdldFl4U3lEYXRhKCk7CiAgICAgICAgdGhpcy5hZGRTeWJnRGlhbG9nRm9ybVZpc2libGUgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLy/mibnph4/liKDpmaTor5XpqozmlbDmja4KICAgIGRlbGV0ZVl4U3koKSB7CiAgICAgIHRoaXMuJGNvbmZpcm0oIuatpOaTjeS9nOWwhuawuOS5heWIoOmZpOivpeaVsOaNriwg5piv5ZCm57un57utPyIsICLmj5DnpLoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KQogICAgICAgIC50aGVuKCgpID0+IHsKICAgICAgICAgIGxldCBpZHMgPSBbXTsKICAgICAgICAgIHRoaXMuc3lTZWxlY3RSb3dzLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICAgIGlkcy5wdXNoKGl0ZW0uaWQpOwogICAgICAgICAgfSk7CiAgICAgICAgICBkZWxldGVZeFN5UmVjb3JkcyhpZHMpLnRoZW4oKHsgY29kZSB9KSA9PiB7CiAgICAgICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTmiJDlip8hIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTlpLHotKUhIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICAgIHRoaXMuZ2V0WXhTeURhdGEoKTsKICAgICAgICAgIH0pOwogICAgICAgIH0pCiAgICAgICAgLmNhdGNoKCgpID0+IHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAiaW5mbyIsCiAgICAgICAgICAgIG1lc3NhZ2U6ICLlt7Llj5bmtojliKDpmaQiCiAgICAgICAgICB9KTsKICAgICAgICB9KTsKICAgIH0sCgogICAgLy/ojrflj5bmo4Dkv67orrDlvZXmlbDmja4KICAgIGdldEp4UmVjb3JkcygpIHsKICAgICAgdGhpcy5qeExvYWRpbmcgPSB0cnVlOwogICAgICBnZXRBc3NldEdxakp4UmVjb3Jkcyh0aGlzLmp4UXVlcnlGb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5ncWpKeExpc3QgPSByZXMuZGF0YS5yZWNvcmRzOwogICAgICAgIHRoaXMuanhRdWVyeUZvcm0udG90YWwgPSByZXMuZGF0YS50b3RhbDsKICAgICAgICB0aGlzLmp4TG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvL+aWsOWinuS/ruaUueajgOS/ruiusOW9leaVsOaNrgogICAgc2F2ZU9yVXBkYXRlSngoKSB7CiAgICAgIHNhdmVPclVwZGF0ZUFzc2V0R3FqSnhSZWNvcmRzKHRoaXMuanhGb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5nZXRKeFJlY29yZHMoKTsKICAgICAgICB0aGlzLmFkZEp3eFN5YmdEaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICBkZWxldGVKeERhdGEoKSB7CiAgICAgIHRoaXMuJGNvbmZpcm0oIuatpOaTjeS9nOWwhuawuOS5heWIoOmZpOivpeaVsOaNriwg5piv5ZCm57un57utPyIsICLmj5DnpLoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KQogICAgICAgIC50aGVuKCgpID0+IHsKICAgICAgICAgIGxldCBpZHMgPSBbXTsKICAgICAgICAgIHRoaXMuanhTZWxlY3RSb3dzLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICAgIGlkcy5wdXNoKGl0ZW0uaWQpOwogICAgICAgICAgfSk7CiAgICAgICAgICBkZWxldGVBc3NldEdxakp4UmVjb3JkcyhpZHMpLnRoZW4oKHsgY29kZSB9KSA9PiB7CiAgICAgICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTmiJDlip8hIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTlpLHotKUhIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICAgIHRoaXMuZ2V0SnhSZWNvcmRzKCk7CiAgICAgICAgICB9KTsKICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoKSA9PiB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogImluZm8iLAogICAgICAgICAgICBtZXNzYWdlOiAi5bey5Y+W5raI5Yig6ZmkIgogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICB9LAoKICAgIHN5Um93Q2xpY2socm93cykgewogICAgICB0aGlzLiRyZWZzLnN5VGFibGUudG9nZ2xlUm93U2VsZWN0aW9uKHJvd3MpOwogICAgfSwKICAgIHN5Q3VycmVudENoYW5nZSh2YWwpIHsKICAgICAgdGhpcy5zeVNlbGVjdFJvd3MgPSB2YWw7CiAgICB9LAogICAganhSb3dDbGljayhyb3dzKSB7CiAgICAgIHRoaXMuJHJlZnMuanhUYWJsZS50b2dnbGVSb3dTZWxlY3Rpb24ocm93cyk7CiAgICB9LAogICAganhDdXJyZW50Q2hhbmdlKHZhbCkgewogICAgICB0aGlzLmp4U2VsZWN0Um93cyA9IHZhbDsKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["gfgqj.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAw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file": "gfgqj.vue", "sourceRoot": "src/views/dagangOilfield/dwzygl/gqjgl", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      ref=\"filter1\"\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      @handleReset=\"filterReset\"\n      @handleEvent=\"handleEvent\"\n    />\n    <el-white class=\"button-group1\">\n      <div class=\"button_btn\">\n        <el-button\n          @click=\"addSensorButton\"\n          v-hasPermi=\"['gfgql:button:add']\"\n          icon=\"el-icon-plus\"\n          type=\"primary\"\n          >新增\n        </el-button>\n        <el-button type=\"primary\" icon=\"el-icon-download\" @click=\"exportExcel()\"\n          >导出</el-button\n        >\n      </div>\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"selectChange\"\n        height=\"63vh\"\n      >\n        <el-table-column\n          slot=\"table_eight\"\n          align=\"center\"\n          fixed=\"right\"\n          style=\"display: block\"\n          label=\"操作\"\n          width=\"180\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              @click=\"getGqjInfo(scope.row)\"\n              class=\"el-icon-view\"\n              title=\"详情\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"scope.row.createBy == currUser\"\n              @click=\"updateGqjInfo(scope.row)\"\n              class=\"el-icon-edit\"\n              title=\"编辑\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"scope.row.createBy == currUser\"\n              @click=\"deleteRow(scope.row.objId)\"\n              class=\"el-icon-delete\"\n              title=\"删除\"\n            ></el-button>\n          </template>\n        </el-table-column>\n      </comp-table>\n    </el-white>\n    <!-- 工器具台账详情修改新增 -->\n    <el-dialog\n      :title=\"gqjTital\"\n      :visible.sync=\"dialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form\n        :model=\"form\"\n        label-width=\"80px\"\n        :disabled=\"isDisabled\"\n        :rules=\"rules\"\n        ref=\"form\"\n      >\n        <el-row>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属公司\" prop=\"ssgs\">\n              <el-select\n                v-model=\"form.ssgs\"\n                placeholder=\"所属公司\"\n                clearable\n                :disabled=\"isDisabled\"\n                @change=\"getBdzList\"\n              >\n                <el-option\n                  v-for=\"item in organizationSelectedList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属光伏电站\" prop=\"bdz\">\n              <el-select\n                placeholder=\"请选择光伏电站\"\n                clearable\n                v-model=\"form.bdz\"\n                :disabled=\"isDisabled\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in bdzList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"名称\" prop=\"sbmc\">\n              <el-input v-model=\"form.sbmc\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"设备分类\" prop=\"sblx\">\n              <el-select\n                placeholder=\"请选择\"\n                clearable\n                v-model=\"form.sblx\"\n                :disabled=\"isDisabled\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in gfgqjlx\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"规格型号\" prop=\"xh\">\n              <el-input v-model=\"form.xh\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <!--          <el-col :span=\"8\">\n                      <el-form-item label=\"编号\" style=\"width: 100%\">\n                        <el-input v-model=\"form.ccbh\" :disabled=\"isDisabled\"></el-input>\n                      </el-form-item>\n                    </el-col>-->\n          <el-col :span=\"8\">\n            <el-form-item label=\"数量\" prop=\"sl\">\n              <el-input-number\n                :min=\"1\"\n                v-model=\"form.sl\"\n                :disabled=\"isDisabled\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"使用年限\">\n              <el-input v-model=\"form.jyzq\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"领用人\" prop=\"fzr\">\n              <el-input v-model=\"form.fzr\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"批准人\" prop=\"cfdd\">\n              <el-input v-model=\"form.cfdd\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"领取时间\" prop=\"tyrq\">\n              <el-date-picker\n                v-model=\"form.tyrq\"\n                type=\"date\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"选择日期\"\n                :disabled=\"isDisabled\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\">\n              <el-input\n                type=\"textarea\"\n                v-model=\"form.bz\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"qxcommit\"\n          v-if=\"gqjTital === '工器具新增' || gqjTital === '工器具修改'\"\n          class=\"pmyBtn\"\n        >\n          确 定\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getUUID } from \"@/utils/ruoyi\";\nimport {\n  deleteAssetGqjJxRecords,\n  deleteYxSyRecords,\n  getAssetGqjJxRecords,\n  getList,\n  getYxSyRecords,\n  remove,\n  saveOrUpdate,\n  saveOrUpdateAssetGqjJxRecords,\n  saveOrUpdateYxSyRecords,\n  exportExcel,\n  getGfBdzSelectList\n} from \"@/api/dagangOilfield/asset/assetGqj\";\nimport CompTable from \"com/CompTable\";\nimport ElFilter from \"com/ElFilter\";\nimport { getDictTypeData } from \"@/api/system/dict/data\";\nimport { getFgsOptions } from \"@/api/yxgl/gfyxgl/gfzbgl\";\n\n\nexport default {\n  components: { CompTable, ElFilter },\n  name: \"gqjgl\",\n  data() {\n    return {\n      gfgqjlx: [],\n      currUser: this.$store.getters.name,\n      params: {\n        type: \"gf\"\n      },\n      bdzAllList: [],\n      bdzList: [],\n      //组织结构下拉数据\n      organizationSelectedList: [],\n      // 表单校验\n      rules: {\n        sl: [{ required: true, message: \"请输入\", trigger: \"blur\" }],\n        ssgs: [{ required: true, message: \"请选择\", trigger: \"blur\" }],\n        // bdz: [{ required: true, message: '请选择', trigger: 'blur' }],\n        sbmc: [{ required: true, message: \"请输入\", trigger: \"blur\" }],\n        sblx: [{ required: true, message: \"请选择\", trigger: \"blur\" }],\n        fzr: [{ required: true, message: \"请输入\", trigger: \"blur\" }]\n      },\n      //工器具详情框字段控制\n      isDisabled: false,\n      //工器具弹出框表头\n      gqjTital: \"工器具新增\",\n      //表格内容\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"ssgsmc\", label: \"所属公司\", minWidth: \"120\" },\n          { prop: \"bdzmc\", label: \"所属光伏电站\", minWidth: \"120\" },\n          { prop: \"sbmc\", label: \"名称\", minWidth: \"180\" },\n          { prop: \"xh\", label: \"规格型号\", minWidth: \"180\" },\n          { prop: \"fzr\", label: \"领用人\", minWidth: \"180\" },\n          // {prop: 'ccbh', label: '编号', minWidth: '180'},\n          { prop: \"jyzq\", label: \"使用年限\", minWidth: \"180\" },\n          { prop: \"tyrq\", label: \"领取时间\", minWidth: \"250\" }\n          // {\n          //   fixed: \"right\",\n          //   prop: 'operation',\n          //   label: '操作',\n          //   minWidth: '150px',\n          //   style: {display: 'block'},\n          //   operation: [\n          //     {name: '修改', clickFun: this.updateGqjInfo},\n          //     {name: '详情', clickFun: this.getGqjInfo},\n          //   ],\n          // },\n        ]\n      },\n      //筛选条件\n      filterInfo: {\n        data: {\n          fzr: \"\",\n          ssgs: \"\",\n          yxbz: \"\",\n          phone: \"\"\n        },\n        fieldList: [\n          {\n            label: \"分公司\",\n            value: \"ssgs\",\n            type: \"select\",\n            options: [],\n            clearable: true\n          },\n          {\n            label: \"光伏电站\",\n            value: \"bdz\",\n            type: \"select\",\n            options: [],\n            clearable: true\n          },\n          { label: \"名称\", type: \"input\", value: \"sbmc\" },\n          { label: \"规格型号\", type: \"input\", value: \"xh\" },\n          { label: \"领用人\", type: \"input\", value: \"fzr\" }\n          // {label: '投运日期', type: 'date', value: 'tyrqArr',dateType: 'daterange',format: 'yyyy-MM-dd'},\n        ]\n      },\n      //检修记录弹出框\n      jwxDialogFormVisible: false,\n      //添加检修记录弹出框\n      addJwxSybgDialogFormVisible: false,\n      //工器具弹出框\n      dialogFormVisible: false,\n      //试验时间\n      sysj: \"\",\n      fildtps: [],\n      //试验弹出框\n      sybgDialogFormVisible: false,\n      //添加试验报告\n      addSybgDialogFormVisible: false,\n      //弹出框表单\n      form: {\n        type: \"gf\"\n      },\n      loading: false,\n      //工器具试验数据集合\n      gqjsyList: [],\n      //检修数据集合\n      gqjJxList: [],\n      //删除是否可用\n      multipleSensor: true,\n      showSearch: true,\n      //删除选择列\n      selectRows: [],\n      //工器具文件上传参数\n      gqjInfoUploadData: {\n        businessId: undefined\n      },\n      //工器具文件上传请求头\n      gqjInfoUpHeader: {},\n\n      //试验查询条件\n      syQueryForm: {\n        gqjId: \"\",\n        total: 0,\n        pageSize: 10,\n        pageNum: 1\n      },\n\n      //试验新增表单数据\n      syFrom: {\n        id: \"\",\n        gqjId: \"\",\n        sydwId: \"\",\n        sydwName: \"\",\n        syryId: \"\",\n        syryName: \"\",\n        sysj: \"\",\n        syjlCode: \"\",\n        syjlName: \"\",\n        remark: \"\"\n      },\n\n      isSyDetail: false,\n\n      //检修查询条件\n      jxQueryForm: {\n        gqjId: \"\",\n        total: 0,\n        pageSize: 10,\n        pageNum: 1\n      },\n      //检修表单\n      jxForm: {\n        id: \"\",\n        jxdwId: \"\",\n        jxdwName: \"\",\n        jxryId: \"\",\n        jxryName: \"\",\n        jxjg: \"\",\n        jxsj: \"\",\n        remark: \"\",\n        gqjId: \"\"\n      },\n\n      //主表选中行数据\n      mainRowData: {},\n      //试验table加载\n      syLoading: false,\n      //试验选中行\n      sySelectRows: [],\n      //检修table加载\n      jxLoading: false,\n      //检修选中行\n      jxSelectRows: []\n    };\n  },\n  watch: {},\n  created() {\n    //获取组织结构下拉数据\n    this.getFgsOptions();\n  },\n  mounted() {\n    getGfBdzSelectList({}).then(async res => {\n      this.bdzAllList = res.data;\n      let { data: gfgqjlx } = await getDictTypeData(\"gfgqjlx\");\n      this.gfgqjlx = gfgqjlx;\n      this.getData();\n    });\n  },\n  methods: {\n    /**\n     * 获取分公司下拉数据\n     */\n    getFgsOptions() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.organizationSelectedList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"ssgs\") {\n            return (item.options = this.organizationSelectedList);\n          }\n        });\n      });\n    },\n    exportExcel() {\n      exportExcel(this.params, \"光伏工器具\");\n    },\n    formatBdz(id) {\n      if (id) {\n        return this.bdzAllList.filter(g => g.value === id)[0].label;\n      } else {\n        return \"\";\n      }\n    },\n    //获取光伏电站下拉框\n    getBdzList() {\n      getGfBdzSelectList({ ssdwbm: this.form.ssgs }).then(res => {\n        this.bdzList = res.data;\n      });\n    },\n\n    handleEvent(val, eventValue) {\n      if (val.label === \"ssgs\") {\n        getGfBdzSelectList({ ssdwbm: val.value.toString() }).then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value == \"bdz\") {\n              this.$set(eventValue, \"bdz\", \"\");\n              return (item.options = res.data);\n            }\n          });\n        });\n      }\n    },\n    /**\n     * 上传附附件之前的处理函数\n     * @param file\n     */\n    gqjInfoBeforeUpload(file) {\n      const fileSize = file.size < 1024 * 1024 * 50; //10M\n      if (!fileSize) {\n        this.$message.error(\"上传文件大小不能超过 50MB!\");\n      }\n      let size = file.size / 1024;\n    },\n    /**\n     * 上传附件成功调用的函数\n     * @param response\n     * @param file\n     * @param fileList\n     */\n    gqjInfoonSuccess(response, file, fileList) {\n      //文件id\n      this.form.attachmentid = response.data.businessId;\n      //文件名称\n      this.form.attachmentname = response.data.sysFile.fileOldName;\n    },\n\n    /**\n     * 移除文件\n     * @param file\n     * @param fileList\n     */\n    gqjInfohandleRemove(file, fileList) {},\n    /**\n     * 工器具上传文件到服务器\n     */\n    gqjInfoSubmitUpload() {\n      debugger;\n      this.gqjInfoUploadData.businessId = getUUID();\n      this.$refs.uploadGqjInfo.submit();\n    },\n    formatSsgs(ssgs) {\n      if (ssgs && ssgs !== \"3002\") {\n        return this.organizationSelectedList.filter(g => g.value === ssgs)[0]\n          .label;\n      } else {\n        return \"港东光伏电分公司\";\n      }\n    },\n    //工器具列表查询\n    async getData(params) {\n        this.params = { ...this.params, ...params };\n        const { data, code } = await getList(this.params);\n        if (code === \"0000\") {\n          for (let i of data.records) {\n            i.ssgsmc = this.formatSsgs(i.ssgs);\n            i.bdzmc = this.formatBdz(i.bdz);\n          }\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n        }\n    },\n\n    //工器具列表新增按钮\n    addSensorButton() {\n      //开启弹出框内输入框编辑权限\n      this.isDisabled = false;\n      //打开弹出框\n      this.dialogFormVisible = true;\n      //设置弹出框表头\n      this.gqjTital = \"工器具新增\";\n      //清空弹出框内容\n      this.form = {\n        type: \"gf\"\n      };\n    },\n    //工器具列表详情按钮\n    getGqjInfo(row) {\n      //打开弹出框\n      this.dialogFormVisible = true;\n      //设置弹出框表头\n      this.gqjTital = \"工器具详情\";\n      //禁用所有输入框\n      this.isDisabled = true;\n      //给弹出框赋值\n      this.form = { ...row };\n      this.getBdzList();\n    },\n    //工器具修改按钮\n    updateGqjInfo(row) {\n      //打开弹出框\n      this.dialogFormVisible = true;\n      //设置弹出框表头\n      this.gqjTital = \"工器具修改\";\n      //开启弹出框内输入框编辑权限\n      this.isDisabled = false;\n      //给弹出框内赋值\n      this.form = { ...row };\n      this.getBdzList();\n    },\n    //工器具列表新增修改保存\n    async qxcommit() {\n      await this.$refs[\"form\"].validate(async valid => {\n        if (valid) {\n          try {\n            let { code } = await saveOrUpdate(this.form);\n            if (code === \"0000\") {\n              this.$message.success(\"操作成功\");\n            }\n          } catch (e) {\n            console.log(e);\n          }\n          //恢复分页\n          this.tableAndPageInfo.pager.pageResize = \"Y\";\n          this.getData();\n          this.dialogFormVisible = false;\n        }\n      });\n    },\n    //删除工器具列表\n    deleteRow(id) {\n      // if (this.selectRows.length < 1) {\n      //   this.$message.warning(\"请选择正确的数据！！！\")\n      //   return\n      // }\n      // let ids = this.selectRows.map(item => {\n      //   return item.objId\n      // });\n      let obj = [];\n      obj.push(id);\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove(obj).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n      this.tableAndPageInfo.pager.pageResize = \"Y\";\n      this.getData();\n    },\n\n    //查看试验\n    handleSearchSYClick(row) {\n      this.sySelectRows = [];\n      this.mainRowData = row;\n      this.syQueryForm.gqjId = row.objId;\n      this.sybgDialogFormVisible = true;\n      this.getYxSyData();\n    },\n\n    //查看检修\n    handleSerchJWXClick(row) {\n      this.mainRowData = row;\n      this.jxQueryForm.gqjId = row.objId;\n      this.jwxDialogFormVisible = true;\n      this.getJxRecords();\n    },\n    //添加检修\n    addJxButton() {\n      this.jxForm = this.$options.data().jxForm;\n      this.jxForm.gqjId = this.mainRowData.objId;\n      this.addJwxSybgDialogFormVisible = true;\n    },\n    updateJx(row) {\n      this.jxForm = row;\n      this.addJwxSybgDialogFormVisible = true;\n    },\n    //添加试验\n    addSyButton() {\n      this.syFrom = this.$options.data().syFrom;\n      this.syFrom.gqjId = this.mainRowData.objId;\n      this.addSybgDialogFormVisible = true;\n    },\n    updateSy(row) {\n      this.syFrom = row;\n      this.addSybgDialogFormVisible = true;\n    },\n    //每页展示数量点击事件\n    handleSizeChange() {},\n    //页码改变事件\n    handleCurrentChange() {},\n    //树点击事件\n    handleNodeClick() {},\n\n    filterReset() {},\n    //选择每一行\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n\n    //获取试验记录数据\n    getYxSyData() {\n      this.syLoading = true;\n      getYxSyRecords(this.syQueryForm).then(res => {\n        this.gqjsyList = res.data.records;\n        this.syQueryForm.total = res.data.total;\n        this.syLoading = false;\n      });\n    },\n\n    //新增修改试验记录数据\n    saveOrUpdateSy() {\n      saveOrUpdateYxSyRecords(this.syFrom).then(res => {\n        this.getYxSyData();\n        this.addSybgDialogFormVisible = false;\n      });\n    },\n    //批量删除试验数据\n    deleteYxSy() {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          let ids = [];\n          this.sySelectRows.forEach(item => {\n            ids.push(item.id);\n          });\n          deleteYxSyRecords(ids).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n            this.getYxSyData();\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n\n    //获取检修记录数据\n    getJxRecords() {\n      this.jxLoading = true;\n      getAssetGqjJxRecords(this.jxQueryForm).then(res => {\n        this.gqjJxList = res.data.records;\n        this.jxQueryForm.total = res.data.total;\n        this.jxLoading = false;\n      });\n    },\n    //新增修改检修记录数据\n    saveOrUpdateJx() {\n      saveOrUpdateAssetGqjJxRecords(this.jxForm).then(res => {\n        this.getJxRecords();\n        this.addJwxSybgDialogFormVisible = false;\n      });\n    },\n    deleteJxData() {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          let ids = [];\n          this.jxSelectRows.forEach(item => {\n            ids.push(item.id);\n          });\n          deleteAssetGqjJxRecords(ids).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n            this.getJxRecords();\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n\n    syRowClick(rows) {\n      this.$refs.syTable.toggleRowSelection(rows);\n    },\n    syCurrentChange(val) {\n      this.sySelectRows = val;\n    },\n    jxRowClick(rows) {\n      this.$refs.jxTable.toggleRowSelection(rows);\n    },\n    jxCurrentChange(val) {\n      this.jxSelectRows = val;\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.button-group {\n  padding-left: 30px;\n  padding-right: 30px;\n  display: flex;\n  justify-content: flex-end;\n}\n\n.qxlr_dialog_insert {\n  margin-top: 6vh !important;\n}\n\n/*/deep/ .qxlr_dialog_insert .el-input--medium .el-input__inner{*/\n/*  width: 100%;*/\n/*}*/\n.el-select {\n  width: 100%;\n}\n\n.el-date-editor {\n  width: 100%;\n}\n</style>\n"]}]}