{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\pdgl\\pdggl.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\pdgl\\pdggl.vue", "mtime": 1706897324785}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgZ2V0UGRzVHJlZUxpc3QsIGdldFBkZ0xpc3QsIGFkZFBkZywgcmVtb3ZlUGRnLCBnZXRQZGdPbmUsCn0gZnJvbSAnQC9hcGkvZGFnYW5nT2lsZmllbGQvYXNzZXQvcGRnJwogIGltcG9ydCB7Z2V0RGljdFR5cGVEYXRhfSBmcm9tICJAL2FwaS9zeXN0ZW0vZGljdC9kYXRhIjsKICBpbXBvcnQgewogICAgZ2V0T3JnYW5pemF0aW9uU2VsZWN0ZWQsCiAgfSBmcm9tICdAL2FwaS9kYWdhbmdPaWxmaWVsZC9hc3NldC9iZHNidHonCiAgaW1wb3J0IHtnZXRQZHNPcHRpb25zRGF0YUxpc3R9IGZyb20gIkAvYXBpL2RhZ2FuZ09pbGZpZWxkL2Fzc2V0L3Bkc2dsIjsKICBleHBvcnQgZGVmYXVsdCB7CiAgICBuYW1lOiAicXhiemsiLAogICAgZGF0YSgpIHsKICAgICAgcmV0dXJuIHsKICAgICAgICAvL+mFjeeUteWupOS4i+aLieahhgogICAgICAgIHBkc09wdGlvbnNEYXRhTGlzdDpbXSwKICAgICAgICBmaWx0ZXJJbmZvOiB7CiAgICAgICAgICBkYXRhOiB7CiAgICAgICAgICAgIGtnZ21jOiAnJywKICAgICAgICAgICAgeXhiaDogJycsCiAgICAgICAgICB9LAogICAgICAgICAgZmllbGRMaXN0OiBbCiAgICAgICAgICAgIHtsYWJlbDogJ+W8gOWFs+afnOWQjeensCcsIHR5cGU6ICdpbnB1dCcsIHZhbHVlOiAna2dnbWMnfSwKICAgICAgICAgICAge2xhYmVsOiAn6L+Q6KGM57yW5Y+3JywgdHlwZTogJ2lucHV0JywgdmFsdWU6ICd5eGJoJ30sCiAgICAgICAgICBdCiAgICAgICAgfSwKICAgICAgICB0YWJsZUFuZFBhZ2VJbmZvOiB7CiAgICAgICAgICBwYWdlcjogewogICAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgICBzaXplczogWzEwLCAyMCwgNTAsIDEwMF0KICAgICAgICAgIH0sCiAgICAgICAgICBvcHRpb246IHsKICAgICAgICAgICAgY2hlY2tCb3g6IHRydWUsCiAgICAgICAgICAgIHNlcmlhbE51bWJlcjogdHJ1ZQogICAgICAgICAgfSwKICAgICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgICB0YWJsZUhlYWRlcjogWwogICAgICAgICAgICAvL3twcm9wOiAnc3NncycsIGxhYmVsOiAn5omA5bGe5YWs5Y+4JywgbWluV2lkdGg6ICcxMjAnfSwKICAgICAgICAgICAge3Byb3A6ICdzc3pzbWMnLCBsYWJlbDogJ+aJgOWxnuermeWupOWQjeensCcsIG1pbldpZHRoOiAnMTgwJ30sCiAgICAgICAgICAgIHtwcm9wOiAnc3N6c3l4YmgnLCBsYWJlbDogJ+aJgOWxnuermeWupOi/kOihjOe8luWPtycsIG1pbldpZHRoOiAnMTIwJ30sCiAgICAgICAgICAgIHtwcm9wOiAna2dnbWMnLCBsYWJlbDogJ+W8gOWFs+afnOWQjeensCcsIG1pbldpZHRoOiAnMTIwJ30sCiAgICAgICAgICAgIHtwcm9wOiAneXhiaCcsIGxhYmVsOiAn6L+Q6KGM57yW5Y+3JywgbWluV2lkdGg6ICcxNDAnfSwKICAgICAgICAgICAgLyp7cHJvcDogJ2VycEJtJywgbGFiZWw6ICdFUlDnvJbnoIEnLCBtaW5XaWR0aDogJzEyMCd9LAogICAgICAgICAgICB7cHJvcDogJ2JncicsIGxhYmVsOiAn5L+d566h5Lq6JywgbWluV2lkdGg6ICcxMjAnfSwKICAgICAgICAgICAge3Byb3A6ICd6Y2JkZnMnLCBsYWJlbDogJ+i1hOS6p+WPmOWKqOaWueW8jycsIG1pbldpZHRoOiAnMTIwJ30sCiAgICAgICAgICAgIHtwcm9wOiAnemNzeCcsIGxhYmVsOiAn6LWE5Lqn5bGe5oCnJywgbWluV2lkdGg6ICcxODAnfSwKICAgICAgICAgICAge3Byb3A6ICd6Y2JoJywgbGFiZWw6ICfotYTkuqfnvJblj7cnLCBtaW5XaWR0aDogJzEyMCd9LAogICAgICAgICAgICB7cHJvcDogJ3dic1lzJywgbGFiZWw6ICdXQlPlhYPntKAnLCBtaW5XaWR0aDogJzEyMCd9LCovCiAgICAgICAgICAgIHtwcm9wOiAndHlycScsIGxhYmVsOiAn5oqV6L+Q5pel5pyfJywgbWluV2lkdGg6ICcxNDAnfSwKICAgICAgICAgICAge3Byb3A6ICdzY2NqJywgbGFiZWw6ICfnlJ/kuqfljoLlrrYnLCBtaW5XaWR0aDogJzEyMCd9LAogICAgICAgICAgICB7cHJvcDogJ2tnZ3hoJywgbGFiZWw6ICflvIDlhbPmn5zlnovlj7cnLCBtaW5XaWR0aDogJzEyMCd9LAogICAgICAgICAgICAvL3twcm9wOiAnemN4eicsIGxhYmVsOiAn6LWE5Lqn5oCn6LSoJywgbWluV2lkdGg6ICcxMjAnfSwKICAgICAgICAgICAge3Byb3A6ICdjY3JxJywgbGFiZWw6ICflh7rljoLml6XmnJ8nLCBtaW5XaWR0aDogJzEyMCd9LAogICAgICAgICAgICB7cHJvcDogJ3p0JywgbGFiZWw6ICfnirbmgIEnLCBtaW5XaWR0aDogJzE4MCd9LAogICAgICAgICAgICAvL3twcm9wOiAnZHF0eicsIGxhYmVsOiAn5Zyw5Yy654m55b6BJywgbWluV2lkdGg6ICcxMjAnfSwKICAgICAgICAgICAge3Byb3A6ICdkeWRqJywgbGFiZWw6ICfnlLXljovnrYnnuqcnLCBtaW5XaWR0aDogJzEyMCd9LAogICAgICAgICAgICAvKntwcm9wOiAna2d5dCcsIGxhYmVsOiAn5byA5YWz55So6YCUJywgbWluV2lkdGg6ICcxNDAnfSwKICAgICAgICAgICAge3Byb3A6ICdrZ2dseCcsIGxhYmVsOiAn5byA5YWz5p+c57G75Z6LJywgbWluV2lkdGg6ICcxMjAnfSwKICAgICAgICAgICAge3Byb3A6ICdmaGRqJywgbGFiZWw6ICfpmLLmiqTnrYnnuqcnLCBtaW5XaWR0aDogJzEyMCd9LAogICAgICAgICAgICB7cHJvcDogJ2d0Y2MnLCBsYWJlbDogJ+afnOS9k+WwuuWvuCcsIG1pbldpZHRoOiAnMTIwJ30sCiAgICAgICAgICAgIHtwcm9wOiAnYnN4cycsIGxhYmVsOiAn6Zet6ZSB5Z6L5byPJywgbWluV2lkdGg6ICcxMjAnfSwKICAgICAgICAgICAge3Byb3A6ICdlZHJ3ZGRsJywgbGFiZWw6ICfpop3lrprng63nqLPlrprnlLXmtYEnLCBtaW5XaWR0aDogJzE4MCd9LAogICAgICAgICAgICB7cHJvcDogJ2Vkcndkc2onLCBsYWJlbDogJ+mineWumueDreeos+WumuaXtumXtCcsIG1pbldpZHRoOiAnMTIwJ30sCiAgICAgICAgICAgIHtwcm9wOiAnZWRkd2RkbCcsIGxhYmVsOiAn6aKd5a6a5Yqo56iz5a6a55S15rWBJywgbWluV2lkdGg6ICcxMjAnfSwKICAgICAgICAgICAge3Byb3A6ICdlZGdwbnNkeScsIGxhYmVsOiAn6aKd5a6a5bel6aKR6ICQ5Y+X55S15Y6LJywgbWluV2lkdGg6ICcxNDAnfSwqLwogICAgICAgICAgICAvKnsKICAgICAgICAgICAgICBmaXhlZDogInJpZ2h0IiwKICAgICAgICAgICAgICBwcm9wOiAnb3BlcmF0aW9uJywKICAgICAgICAgICAgICBsYWJlbDogJ+aTjeS9nCcsCiAgICAgICAgICAgICAgbWluV2lkdGg6ICcxMzBweCcsCiAgICAgICAgICAgICAgc3R5bGU6IHtkaXNwbGF5OiAnYmxvY2snfSwKICAgICAgICAgICAgICBvcGVyYXRpb246IFsKICAgICAgICAgICAgICAgIHtuYW1lOiAn5L+u5pS5JywgY2xpY2tGdW46IHRoaXMucGRnVXBkYXRlfSwKICAgICAgICAgICAgICAgIHtuYW1lOiAn6K+m5oOFJywgY2xpY2tGdW46IHRoaXMucGRnRGV0YWlsc30sCiAgICAgICAgICAgICAgXQogICAgICAgICAgICB9LCovCiAgICAgICAgICBdCiAgICAgICAgfSwKICAgICAgICBPcmdhbml6YXRpb25TZWxlY3RlZExpc3Q6W10sCgogICAgICAgIGJkekRpYWxvZ0Zvcm1WaXNpYmxlOiBmYWxzZSwKICAgICAgICAvL+W8ueWHuuahhuihqOWNlQogICAgICAgIGZvcm06IHt9LAoKICAgICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgICAvL+e7hOe7h+agkQogICAgICAgIHNlbGVjdFJvd3M6W10sCiAgICAgICAgLy/lj5jnlLXnq5nmjILmjqXmlbDmja4KICAgICAgICBuZXdUZXN0RGF0YTogW10sCiAgICAgICAgdHJlZU9wdGlvbnM6W10sCiAgICAgICAgLy/liKDpmaTmmK/lkKblj6/nlKgKICAgICAgICBtdWx0aXBsZVNlbnNvcjogdHJ1ZSwKICAgICAgICAvL+afpeivouWPguaVsAogICAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcm9sZUtleTogJycsCiAgICAgICAgICByb2xlTmFtZTogJycsCiAgICAgICAgICBzdGF0dXM6ICcnLAogICAgICAgIH0sCiAgICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKCiAgICAgICAgcnVsZXM6ewogICAgICAgICAgLy9zc2dzOlt7cmVxdWlyZWQ6dHJ1ZSxtZXNzYWdlOifor7fpgInmi6nmiYDlsZ7lhazlj7gnLHRyaWdnZXI6J2NoYW5nZSd9XSwKICAgICAgICAgIHNzenM6W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+mAieaLqeaJgOWxnuermeWupCcsdHJpZ2dlcjonY2hhbmdlJ31dLAogICAgICAgICAga2dnbWM6W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+i+k+WFpeW8gOWFs+afnOWQjeensCcsdHJpZ2dlcjonYmx1cid9XSwKICAgICAgICAgIHl4Ymg6W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+i+k+WFpei/kOihjOe8luWPtycsdHJpZ2dlcjonYmx1cid9XSwKICAgICAgICAgIC8qZXJwQm06W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+i+k+WFpUVSUOe8lueggScsdHJpZ2dlcjonYmx1cid9XSwKICAgICAgICAgIGJncjpbe3JlcXVpcmVkOnRydWUsbWVzc2FnZTon6K+36L6T5YWl5L+d566h5Lq6Jyx0cmlnZ2VyOidibHVyJ31dLAogICAgICAgICAgemNiZGZzOlt7cmVxdWlyZWQ6dHJ1ZSxtZXNzYWdlOifor7fovpPlhaXotYTkuqflj5jliqjmlrnlvI8nLHRyaWdnZXI6J2JsdXInfV0sCiAgICAgICAgICB6Y3N4Olt7cmVxdWlyZWQ6dHJ1ZSxtZXNzYWdlOifor7fovpPlhaXotYTkuqflsZ7mgKcnLHRyaWdnZXI6J2JsdXInfV0sCiAgICAgICAgICB6Y2JoOlt7cmVxdWlyZWQ6dHJ1ZSxtZXNzYWdlOifor7fovpPlhaXotYTkuqfnvJblj7cnLHRyaWdnZXI6J2JsdXInfV0sCiAgICAgICAgICB3YnNZczpbe3JlcXVpcmVkOnRydWUsbWVzc2FnZTon6K+36L6T5YWlV0JT5YWD57SgJyx0cmlnZ2VyOidibHVyJ31dLCovCiAgICAgICAgICB0eXJxOlt7cmVxdWlyZWQ6dHJ1ZSxtZXNzYWdlOifor7fpgInmi6nmipXov5Dml6XmnJ8nLHRyaWdnZXI6J2NoYW5nZSd9XSwKICAgICAgICAgIHNjY2o6W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+i+k+WFpeeUn+S6p+WOguWuticsdHJpZ2dlcjonYmx1cid9XSwKICAgICAgICAgIGtnZ3hoOlt7cmVxdWlyZWQ6dHJ1ZSxtZXNzYWdlOifor7fovpPlhaXlvIDlhbPmn5zlnovlj7cnLHRyaWdnZXI6J2JsdXInfV0sCiAgICAgICAgICAvL3pjeHo6W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+i+k+WFpei1hOS6p+aAp+i0qCcsdHJpZ2dlcjonYmx1cid9XSwKICAgICAgICAgIGNjcnE6W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+mAieaLqeWHuuWOguaXpeacnycsdHJpZ2dlcjonY2hhbmdlJ31dLAogICAgICAgICAgenQ6W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+mAieaLqeeKtuaAgScsdHJpZ2dlcjonY2hhbmdlJ31dLAogICAgICAgICAgLy9kcXR6Olt7cmVxdWlyZWQ6dHJ1ZSxtZXNzYWdlOifor7fovpPlhaXlnLDljLrnibnlvoEnLHRyaWdnZXI6J2JsdXInfV0sCiAgICAgICAgICBkeWRqOlt7cmVxdWlyZWQ6dHJ1ZSxtZXNzYWdlOifor7fpgInmi6nnlLXljovnrYnnuqcnLHRyaWdnZXI6J2NoYW5nZSd9XSwKICAgICAgICAgIC8qa2d5dDpbe3JlcXVpcmVkOnRydWUsbWVzc2FnZTon6K+36YCJ5oup5byA5YWz55So6YCUJyx0cmlnZ2VyOidjaGFuZ2UnfV0sCiAgICAgICAgICBrZ2dseDpbe3JlcXVpcmVkOnRydWUsbWVzc2FnZTon6K+36YCJ5oup5byA5YWz5p+c57G75Z6LJyx0cmlnZ2VyOidjaGFuZ2UnfV0sCiAgICAgICAgICBmaGRqOlt7cmVxdWlyZWQ6dHJ1ZSxtZXNzYWdlOifor7fovpPlhaXpmLLmiqTnrYnnuqcnLHRyaWdnZXI6J2JsdXInfV0sCiAgICAgICAgICBndGNjOlt7cmVxdWlyZWQ6dHJ1ZSxtZXNzYWdlOifor7fovpPlhaXmn5zkvZPlsLrlr7gnLHRyaWdnZXI6J2JsdXInfV0sCiAgICAgICAgICBic3hzOlt7cmVxdWlyZWQ6dHJ1ZSxtZXNzYWdlOifor7fovpPlhaXpl63plIHlnovlvI8nLHRyaWdlcjonYmx1cid9XSwKICAgICAgICAgIGVkcndkZGw6W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+i+k+WFpemineWumueDreeos+WumueUtea1gScsdHJpZ2dlcjonYmx1cid9XSwKICAgICAgICAgIGVkcndkc2o6W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+mAieaLqemineWumueDreeos+WumuaXtumXtCcsdHJpZ2dlcjonY2hhbmdlJ31dLAogICAgICAgICAgZWRkd2RkbDpbe3JlcXVpcmVkOnRydWUsbWVzc2FnZTon6K+36L6T5YWl6aKd5a6a5Yqo56iz5a6a55S15rWBJyx0cmlnZ2VyOidibHVyJ31dLAogICAgICAgICAgZWRncG5zZHk6W3tyZXF1aXJlZDp0cnVlLG1lc3NhZ2U6J+ivt+i+k+WFpemineWumuWFrOmikeiAkOWPl+eUteWOiycsdHJpZ2dlcjonYmx1cid9XSovCiAgICAgICAgfSwKCiAgICAgICAgLy/moJHnu5PmnoTkuIrpnaLlvpfnrZvpgInmoYblj4LmlbAKICAgICAgICB0cmVlRm9ybToge30sCiAgICAgICAgcGRna2d5dExpc3Q6WwogICAgICAgICAge3ZhbHVlOiAn5Ye657q/JywKICAgICAgICAgICAgbGFiZWw6ICflh7rnur8nfSwKICAgICAgICAgIHt2YWx1ZTogJ+i/m+e6vycsCiAgICAgICAgICAgIGxhYmVsOiAn6L+b57q/J30sCiAgICAgICAgICB7dmFsdWU6ICfogZTnu5wnLAogICAgICAgICAgICBsYWJlbDogJ+iBlOe7nCd9CiAgICAgICAgXSwKICAgICAgICBwZGd6dExpc3Q6W10sCiAgICAgICAga2dnbHhMaXN0OltdLAogICAgICAgIGR5ZGpMaXN0OltdLAogICAgICAgIHNob3dCdXR0b246dHJ1ZSwKICAgICAgICBzZWxlY3RUcmVlRGF0YTp7fSwKICAgICAgICBpc0Rpc2FibGVkOmZhbHNlLAogICAgICB9OwogICAgfSwKICAgIHdhdGNoOiB7fSwKICAgIGNyZWF0ZWQoKSB7CiAgICAgIHRoaXMuaXNTaG93MSA9IHRydWUKICAgICAgLy/liJ3lp4vljJbliqDovb3ml7bliqDovb3miYDmnInlj5jnlLXnq5nkv6Hmga8KICAgICAgdGhpcy5pbml0RGljdERhdGEoKQogICAgICB0aGlzLmdldE9yZ2FuaXphdGlvblNlbGVjdGVkKCkKICAgICAgdGhpcy5nZXROZXdUcmVlSW5mbygpCiAgICAgIHRoaXMuZ2V0RGF0YSgpCiAgICAgIHRoaXMuZ2V0UGRzT3B0aW9uc0RhdGFMaXN0KCkKICAgIH0sCiAgICBtZXRob2RzOiB7CiAgICAgIC8v6I635Y+W6YWN55S15a6k5LiL5ouJ5qGG5pWw5o2uCiAgICAgIGdldFBkc09wdGlvbnNEYXRhTGlzdCgpIHsKICAgICAgICBnZXRQZHNPcHRpb25zRGF0YUxpc3Qoe30pLnRoZW4ocmVzID0+IHsKICAgICAgICAgIHRoaXMucGRzT3B0aW9uc0RhdGFMaXN0ID0gcmVzLmRhdGE7CiAgICAgICAgfSkKICAgICAgfSwKICAgICAgLy/liJfooajmn6Xor6IKICAgICAgYXN5bmMgZ2V0RGF0YShwYXJhbSkgewogICAgICAgIGNvbnN0IHBhcj17Li4udGhpcy5wYXJhbXMsLi4ucGFyYW19CiAgICAgICAgdHJ5IHsKICAgICAgICAgIGxldCB7ZGF0YSxjb2RlfT1hd2FpdCBnZXRQZGdMaXN0KHBhcikKICAgICAgICAgIGlmKGNvZGU9PT0nMDAwMCcpewogICAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8udGFibGVEYXRhID0gZGF0YS5yZWNvcmRzOwogICAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIudG90YWwgPSBkYXRhLnRvdGFsOwogICAgICAgICAgfQogICAgICAgIH1jYXRjaCAoZSkgewogICAgICAgICAgY29uc29sZS5sb2coZSkKICAgICAgICB9CgogICAgICB9LAogICAgICBhc3luYyBpbml0RGljdERhdGEoKXsKICAgICAgICBsZXQge2RhdGE6cGRna2d5dH09YXdhaXQgZ2V0RGljdFR5cGVEYXRhKCdwZGdrZ3l0JykKICAgICAgICBsZXQge2RhdGE6cGRnenR9PWF3YWl0IGdldERpY3RUeXBlRGF0YSgncGRnenQnKQogICAgICAgIGxldCB7ZGF0YTprZ2dseH09YXdhaXQgZ2V0RGljdFR5cGVEYXRhKCdrZ2dseCcpCiAgICAgICAgbGV0IHtkYXRhOmRnX2R5ZGp9PWF3YWl0IGdldERpY3RUeXBlRGF0YSgnZGdfZHlkaicpCiAgICAgICAgLy90aGlzLnBkZ2tneXRMaXN0PXBkZ2tneXQKICAgICAgICB0aGlzLnBkZ3p0TGlzdD1wZGd6dAogICAgICAgIHRoaXMua2dnbHhMaXN0PWtnZ2x4CiAgICAgICAgdGhpcy5keWRqTGlzdD1kZ19keWRqCiAgICAgIH0sCiAgICAgIGdldE5ld1RyZWVJbmZvKCkgewogICAgICAgIGdldFBkc1RyZWVMaXN0KHRoaXMudHJlZUZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgIHRoaXMudHJlZU9wdGlvbnMgPSByZXMuZGF0YTsKICAgICAgICB9KQogICAgICB9LAogICAgICAvL+agkeebkeWQrOS6i+S7tgogICAgICBmaWx0ZXJOb2RlKHZhbHVlLCBkYXRhKSB7CiAgICAgICAgaWYgKCF2YWx1ZSkgcmV0dXJuIHRydWU7CiAgICAgICAgcmV0dXJuIGRhdGEubGFiZWwuaW5kZXhPZih2YWx1ZSkgIT09IC0xOwogICAgICB9LAogICAgICAvL+mFjeeUteafnOa3u+WKoOaMiemSrgogICAgICBiZHpBZGRTZW5zb3JCdXR0b24oKSB7CiAgICAgICAgdGhpcy5pc0Rpc2FibGVkID0gZmFsc2U7CiAgICAgICAgdGhpcy5zaG93QnV0dG9uID0gdHJ1ZTsKICAgICAgICB0aGlzLmJkekRpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZQogICAgICB9LAogICAgICAvL+avj+mhteWxleekuuaVsOmHj+eCueWHu+S6i+S7tgogICAgICBoYW5kbGVTaXplQ2hhbmdlKCkgewoKICAgICAgfSwKICAgICAgc2VsZWN0Q2hhbmdlKHJvd3MpIHsKICAgICAgICB0aGlzLmlkcyA9IHJvd3MubWFwKGl0ZW0gPT4gaXRlbS5vYmpJZCkKICAgICAgICB0aGlzLnNlbGVjdFJvd3MgPSByb3dzCiAgICAgIH0sCgogICAgICAvL+mhteeggeaUueWPmOS6i+S7tgogICAgICBoYW5kbGVDdXJyZW50Q2hhbmdlKCkgewoKICAgICAgfSwKICAgICAgLy/ojrflj5bnu4Tnu4fnu5PmnoTkuIvmi4nmoYbmlbDmja4KICAgICAgZ2V0T3JnYW5pemF0aW9uU2VsZWN0ZWQoKSB7CiAgICAgICAgbGV0IHBhcmVudElkID0gJzEwMDEnOwogICAgICAgIGdldE9yZ2FuaXphdGlvblNlbGVjdGVkKHtwYXJlbnRJZDogcGFyZW50SWR9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgICB0aGlzLk9yZ2FuaXphdGlvblNlbGVjdGVkTGlzdCA9IHJlcy5kYXRhCiAgICAgICAgfSkKICAgICAgfSwKICAgICAgLy/moJHngrnlh7vkuovku7YKICAgICAgYXN5bmMgaGFuZGxlTm9kZUNsaWNrKGRhdGEsIGUpIHsKICAgICAgICB0aGlzLnNlbGVjdFRyZWVEYXRhPWRhdGEKICAgICAgICBpZiAoZGF0YS5pZGVudGlmaWVyID09ICcxJyl7CiAgICAgICAgICAvL+mFjeeUteafnOivpuaDhQogICAgICAgICAgdGhpcy5wZHNPcHRpb25zRGF0YUxpc3Q9W107CiAgICAgICAgICB0aGlzLnBkc09wdGlvbnNEYXRhTGlzdC5sYWJlbD1kYXRhLmxhYmVsOwogICAgICAgICAgdGhpcy5wZHNPcHRpb25zRGF0YUxpc3QudmFsdWU9ZGF0YS5pZDsKICAgICAgICAgIGF3YWl0IHRoaXMuZ2V0RGF0YSh7c3N6czpkYXRhLmlkfSkKICAgICAgICB9ZWxzZSBpZihkYXRhLmlkZW50aWZpZXIgPT0gJzInKXsKICAgICAgICAgIGF3YWl0IHRoaXMuZ2V0RGF0YSh7b2JqSWQ6ZGF0YS5pZH0pCiAgICAgICAgICBhd2FpdCB0aGlzLnBkZ0RldGFpbHModGhpcy50YWJsZUFuZFBhZ2VJbmZvLnRhYmxlRGF0YVswXSkKICAgICAgICB9ZWxzZXsKICAgICAgICAgIGF3YWl0IHRoaXMuZ2V0RGF0YSgpCiAgICAgICAgfQogICAgICB9LAogICAgICAgcGRnVXBkYXRlKHJvdykgewogICAgICAgICBnZXRQZGdPbmUoe29iaklkOnJvdy5vYmpJZH0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgdGhpcy5iZHpEaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CiAgICAgICAgICAgICB0aGlzLmZvcm0gPSByZXMuZGF0YTsKICAgICAgICAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICAgICAgICAgdGhpcy5zaG93QnV0dG9uID0gdHJ1ZTsKICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5pON5L2c5aSx6LSlIik7CiAgICAgICAgICAgfQogICAgICAgICB9KQogICAgICB9LAogICAgICAvLy9mb3Jt5by556qXCiAgICAgICBwZGdEZXRhaWxzKHJvdykgewogICAgICAgICBnZXRQZGdPbmUoe29iaklkOnJvdy5vYmpJZH0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgIHRoaXMuYmR6RGlhbG9nRm9ybVZpc2libGUgPSB0cnVlOwogICAgICAgICAgICB0aGlzLmZvcm0gPSByZXMuZGF0YTsKICAgICAgICAgICAgdGhpcy5pc0Rpc2FibGVkID0gdHJ1ZTsKICAgICAgICAgICAgdGhpcy5zaG93QnV0dG9uID0gZmFsc2U7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLmk43kvZzlpLHotKUiKTsKICAgICAgICAgIH0KICAgICAgICB9KQogICAgICB9LAogICAgICAvL+aWsOWingogICAgICBzYXZlKCkgewogICAgICAgIHRoaXMuJHJlZnMuZm9ybS52YWxpZGF0ZSgodmFsaWQpID0+IHsKICAgICAgICAgIGlmKHZhbGlkKXsKICAgICAgICAgICAgYWRkUGRnKHRoaXMuZm9ybSkudGhlbihyZXMgPT4gewogICAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PSAiMDAwMCIpIHsKICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5pON5L2c5oiQ5YqfIik7CiAgICAgICAgICAgICAgICB0aGlzLmJkekRpYWxvZ0Zvcm1WaXNpYmxlID0gZmFsc2U7CiAgICAgICAgICAgICAgICB0aGlzLmdldE5ld1RyZWVJbmZvKCk7CiAgICAgICAgICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5pON5L2c5aSx6LSlIik7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KTsKICAgICAgICAgIH1lbHNlewogICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICAgICAgICB2YXIgaXNFcnJvciA9IGRvY3VtZW50LmdldEVsZW1lbnRzQnlDbGFzc05hbWUoImlzLWVycm9yIik7CiAgICAgICAgICAgICAgaWYgKGlzRXJyb3JbMF0ucXVlcnlTZWxlY3RvcignaW5wdXQnKSkgewogICAgICAgICAgICAgICAgaXNFcnJvclswXS5xdWVyeVNlbGVjdG9yKCdpbnB1dCcpLmZvY3VzKCk7CiAgICAgICAgICAgICAgfSBlbHNlIGlmIChpc0Vycm9yWzBdLnF1ZXJ5U2VsZWN0b3IoJ3RleHRhcmVhJykpIHsKICAgICAgICAgICAgICAgIGlzRXJyb3JbMF0ucXVlcnlTZWxlY3RvcigndGV4dGFyZWEnKS5mb2N1cygpOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSwgMSkKICAgICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9LAoKICAgICAgaGFuZGxlQ2xvc2UoKXsKICAgICAgICB0aGlzLmZvcm09e307CiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgdGhpcy5mb3JtID0gdGhpcy4kb3B0aW9ucy5kYXRhKCkuZm9ybTsKICAgICAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7CiAgICAgICAgfSk7CiAgICAgIH0sCgogICAgICAvKioKICAgICAgICog5Yig6Zmk6YWN55S15p+cCiAgICAgICAqLwogICAgICByZW1vdmUoKSB7CiAgICAgICAgaWYgKHRoaXMuaWRzLmxlbmd0aCAhPT0gMCkgewogICAgICAgICAgdGhpcy4kY29uZmlybSgn5q2k5pON5L2c5bCG5rC45LmF5Yig6Zmk6K+l5pWw5o2uLCDmmK/lkKbnu6fnu60/JywgJ+aPkOekuicsIHsKICAgICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICAgICAgcmVtb3ZlUGRnKHRoaXMuaWRzKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAn5Yig6Zmk5oiQ5YqfIScKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICB0aGlzLmdldE5ld1RyZWVJbmZvKCk7CiAgICAgICAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgICAgICAgIH0pCiAgICAgICAgICB9KQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogJ2luZm8nLAogICAgICAgICAgICBtZXNzYWdlOiAn6K+36YCJ5oup6Iez5bCR5LiA5p2h5pWw5o2uIScKICAgICAgICAgIH0pOwogICAgICAgIH0KCiAgICAgIH0sCiAgICB9CiAgfTsK"}, {"version": 3, "sources": ["pdggl.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "pdggl.vue", "sourceRoot": "src/views/dagangOilfield/dwzygl/pdgl", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card shadow=\"never\" style=\"background:#e0f8ed\">\n          <div>\n            <el-col>\n              <el-form label-width=\"80px\">\n                <el-col :span=\"24\">\n                  <el-form-item label=\"所属公司:\">\n                    <el-select v-model=\"treeForm.ssgs\" placeholder=\"请选择所属公司\" @change=\"getNewTreeInfo\" clearable>\n                      <el-option v-for=\"item in OrganizationSelectedList\"\n                                 :key=\"item.value\"\n                                 :label=\"item.label\"\n                                 :value=\"item.value\"></el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"快速查询:\">\n                    <el-input\n                      placeholder=\"输入关键字进行过滤\"\n                      v-model=\"treeForm.pdsmc\"\n                      @change=\"getNewTreeInfo\"\n                      clearable/>\n                  </el-form-item>\n                </el-col>\n              </el-form>\n            </el-col>\n          </div>\n          <div class=\"text item head-container\">\n            <el-col>\n              <el-tree :expand-on-click-node=\"true\"\n                       id=\"tree\"\n                       :data=\"treeOptions\"\n                       @node-click=\"handleNodeClick\"\n                       :highlight-current=\"true\"\n                       ref=\"tree\"\n                       :filter-node-method=\"filterNode\"\n                       node-key=\"id\"\n                       :default-expanded-keys=\"['0']\"\n                       :default-checked-keys=\"['0']\"\n                       accordion\n              />\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 120, itemWidth: 180 }\"\n        />\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button icon=\"el-icon-plus\" @click=\"bdzAddSensorButton\" v-hasPermi=\"['pdgtz:button:add']\" type=\"primary\">新增</el-button>\n            <el-button icon=\"el-icon-delete\" v-hasPermi=\"['pdgtz:button:delete']\" type=\"danger\" @click=\"remove\">删除</el-button>\n          </div>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"selectChange\"\n            height=\"70vh\"\n          >\n          <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\" width=\"160\"\n                           :resizable=\"false\">\n            <template slot-scope=\"scope\">\n              <el-button @click=\"pdgUpdate(scope.row)\" v-hasPermi=\"['pdgtz:button:update']\" type=\"text\" size=\"small\" title=\"修改\"  class='el-icon-edit'></el-button>\n              <el-button @click=\"pdgDetails(scope.row)\" type=\"text\" size=\"small\" title=\"详情\" class=\"el-icon-view\"></el-button>\n            </template>\n          </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!-- 一次设备弹出框结束展示设备履历 -->\n    <!--变电站所用弹出框开始-->\n    <el-dialog title=\"配电柜详情\" :visible.sync=\"bdzDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"60%\" @close=\"handleClose\">\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"130px\">\n        <el-row :gutter=\"20\">\n<!--          <el-col :span=\"8\">\n            <el-form-item label=\"所属公司\" prop=\"ssgs\">\n              <el-select v-model=\"form.ssgs\" placeholder=\"请选择所属公司\" :disabled=\"isDisabled\" clearable>\n                <el-option v-for=\"item in OrganizationSelectedList\"\n                           :key=\"item.value\"\n                           :label=\"item.label\"\n                           :value=\"item.label\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>-->\n<!--          <el-col :span=\"8\">\n            <el-form-item label=\"所属站室名称\" prop=\"sszsmc\">\n              <el-input v-model=\"form.sszsmc\" placeholder=\"请输入所属站室名称\" :disabled=\"true\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属站室运行编号\" prop=\"sszsyxbh\">\n              <el-input v-model=\"form.sszsyxbh\" placeholder=\"请输入所属站室运行编号\" :disabled=\"true\"></el-input>\n            </el-form-item>\n          </el-col>-->\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属站室\" prop=\"sszs\">\n              <el-select v-model=\"form.sszs\" placeholder=\"请选择所属电站\"  :disabled=\"isDisabled\" clearable>\n                <el-option\n                  v-for=\"item in pdsOptionsDataList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"开关柜名称\" prop=\"kggmc\">\n              <el-input v-model=\"form.kggmc\" placeholder=\"请输入开关柜名称\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"运行编号\" prop=\"yxbh\">\n              <el-input v-model=\"form.yxbh\" placeholder=\"请输入运行编号\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"投运日期：\" prop=\"tyrq\">\n              <el-date-picker\n                type=\"date\"\n                :disabled=\"isDisabled\"\n                placeholder=\"选择日期\"\n                value-format=\"yyyy-MM-dd\"\n                v-model=\"form.tyrq\"\n                style=\"width: 100%\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"生产厂家\" prop=\"sccj\">\n              <el-input v-model=\"form.sccj\" placeholder=\"请输入生产厂家\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n<!--          <el-col :span=\"8\">\n            <el-form-item label=\"ERP编码\" prop=\"erpBm\">\n              <el-input v-model=\"form.erpBm\" placeholder=\"请输入ERP编码\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>-->\n<!--        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"保管人\" prop=\"bgr\">\n              <el-input v-model=\"form.bgr\" placeholder=\"请输入保管人\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"资产变动方式\" prop=\"zcbdfs\">\n              <el-input v-model=\"form.zcbdfs\" placeholder=\"请输入资产变动方式\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"资产属性\" prop=\"zcsx\">\n              <el-input v-model=\"form.zcsx\" placeholder=\"请输入资产属性\"  :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-col :span=\"8\">\n            <el-form-item label=\"资产编号\" prop=\"zcbh\">\n              <el-input v-model=\"form.zcbh\" placeholder=\"请输入资产编号\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"WBS元素\" prop=\"wbsYs\">\n              <el-input v-model=\"form.wbsYs\" placeholder=\"请输入WBS元素\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          -->\n         <el-col :span=\"8\">\n            <el-form-item label=\"开关柜型号\" prop=\"kggxh\">\n              <el-input v-model=\"form.kggxh\" placeholder=\"请输入开关柜型号\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"出厂日期：\" prop=\"ccrq\">\n              <el-date-picker\n                type=\"date\"\n                :disabled=\"isDisabled\"\n                placeholder=\"选择日期\"\n                value-format=\"yyyy-MM-dd\"\n                v-model=\"form.ccrq\"\n                style=\"width: 100%\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"状态\" prop=\"zt\">\n              <el-select v-model=\"form.zt\" placeholder=\"请选择状态\" :disabled=\"isDisabled\" clearable>\n                <el-option v-for=\"item in pdgztList\"\n                           :key=\"item.value\"\n                           :label=\"item.label\"\n                           :value=\"item.label\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"资产性质\" prop=\"zcxz\">\n              <el-input v-model=\"form.zcxz\" placeholder=\"请输入资产性质\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>-->\n<!--          <el-col :span=\"8\">\n            <el-form-item label=\"地区特征\" prop=\"dqtz\">\n              <el-input v-model=\"form.dqtz\" placeholder=\"请输入地区特征\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>-->\n          <el-col :span=\"8\">\n            <el-form-item label=\"电压等级(KV)\" prop=\"dydj\">\n              <el-input-number :min=\"0\" :precision=\"2\" v-model=\"form.dydj\" placeholder=\"请输入电压等级\" :disabled=\"isDisabled\"></el-input-number>\n<!--              <el-select v-model=\"form.dydj\" placeholder=\"请选择电压等级\" :disabled=\"isDisabled\"  clearable>\n                <el-option v-for=\"item in dydjList\"\n                           :key=\"item.value\"\n                           :label=\"item.label\"\n                           :value=\"item.label\"></el-option>\n              </el-select>-->\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"开关用途\" prop=\"kgyt\">\n              <el-select v-model=\"form.kgyt\" placeholder=\"请选择开关用途\" :disabled=\"isDisabled\"  clearable>\n                <el-option v-for=\"item in pdgkgytList\"\n                           :key=\"item.value\"\n                           :label=\"item.label\"\n                           :value=\"item.label\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"开关柜类型\" prop=\"kgglx\">\n              <el-input v-model=\"form.kgglx\" placeholder=\"请输入开关柜类型\" :disabled=\"isDisabled\"></el-input>\n<!--              <el-select v-model=\"form.kgglx\" placeholder=\"请选择开关柜类型\" :disabled=\"isDisabled\"  clearable>\n                <el-option v-for=\"item in kgglxList\"\n                           :key=\"item.value\"\n                           :label=\"item.label\"\n                           :value=\"item.label\"></el-option>\n              </el-select>-->\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n<!--          <el-col :span=\"8\">\n            <el-form-item label=\"防护等级\" prop=\"fhdj\">\n              <el-input v-model=\"form.fhdj\" placeholder=\"请输入防护等级\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"柜体尺寸\" prop=\"gtcc\">\n              <el-input v-model=\"form.gtcc\" placeholder=\"请输入柜体尺寸\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>-->\n          <el-col :span=\"8\">\n            <el-form-item label=\"闭锁型式\" prop=\"bsxs\">\n              <el-input v-model=\"form.bsxs\" placeholder=\"请输入闭锁型式\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\" prop=\"bz\">\n              <el-input v-model=\"form.bz\" type=\"textarea\" rows=\"2\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n<!--        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"额定热稳定电流\" prop=\"edrwddl\">\n              <el-input v-model=\"form.edrwddl\" placeholder=\"请输入额定热稳定电流\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"额定热稳定时间：\" prop=\"edrwdsj\">\n              <el-date-picker\n                type=\"datetime\"\n                :disabled=\"isDisabled\"\n                placeholder=\"选择日期\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n                v-model=\"form.edrwdsj\"\n                style=\"width: 100%\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"额定动稳定电流\" prop=\"eddwddl\">\n              <el-input v-model=\"form.eddwddl\" placeholder=\"请输入额定动稳定电流\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"额定工频耐受电压\" prop=\"edgpnsdy\">\n              <el-input v-model=\"form.edgpnsdy\" placeholder=\"请输入额定工频耐受电压\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>-->\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\" v-show=\"showButton\">\n        <el-button @click=\"bdzDialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"save\" class=\"pmyBtn\">确 定</el-button>\n      </div>\n    </el-dialog>\n  </div>\n\n</template>\n\n<script>\nimport {\n  getPdsTreeList, getPdgList, addPdg, removePdg, getPdgOne,\n} from '@/api/dagangOilfield/asset/pdg'\n  import {getDictTypeData} from \"@/api/system/dict/data\";\n  import {\n    getOrganizationSelected,\n  } from '@/api/dagangOilfield/asset/bdsbtz'\n  import {getPdsOptionsDataList} from \"@/api/dagangOilfield/asset/pdsgl\";\n  export default {\n    name: \"qxbzk\",\n    data() {\n      return {\n        //配电室下拉框\n        pdsOptionsDataList:[],\n        filterInfo: {\n          data: {\n            kggmc: '',\n            yxbh: '',\n          },\n          fieldList: [\n            {label: '开关柜名称', type: 'input', value: 'kggmc'},\n            {label: '运行编号', type: 'input', value: 'yxbh'},\n          ]\n        },\n        tableAndPageInfo: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n            //{prop: 'ssgs', label: '所属公司', minWidth: '120'},\n            {prop: 'sszsmc', label: '所属站室名称', minWidth: '180'},\n            {prop: 'sszsyxbh', label: '所属站室运行编号', minWidth: '120'},\n            {prop: 'kggmc', label: '开关柜名称', minWidth: '120'},\n            {prop: 'yxbh', label: '运行编号', minWidth: '140'},\n            /*{prop: 'erpBm', label: 'ERP编码', minWidth: '120'},\n            {prop: 'bgr', label: '保管人', minWidth: '120'},\n            {prop: 'zcbdfs', label: '资产变动方式', minWidth: '120'},\n            {prop: 'zcsx', label: '资产属性', minWidth: '180'},\n            {prop: 'zcbh', label: '资产编号', minWidth: '120'},\n            {prop: 'wbsYs', label: 'WBS元素', minWidth: '120'},*/\n            {prop: 'tyrq', label: '投运日期', minWidth: '140'},\n            {prop: 'sccj', label: '生产厂家', minWidth: '120'},\n            {prop: 'kggxh', label: '开关柜型号', minWidth: '120'},\n            //{prop: 'zcxz', label: '资产性质', minWidth: '120'},\n            {prop: 'ccrq', label: '出厂日期', minWidth: '120'},\n            {prop: 'zt', label: '状态', minWidth: '180'},\n            //{prop: 'dqtz', label: '地区特征', minWidth: '120'},\n            {prop: 'dydj', label: '电压等级', minWidth: '120'},\n            /*{prop: 'kgyt', label: '开关用途', minWidth: '140'},\n            {prop: 'kgglx', label: '开关柜类型', minWidth: '120'},\n            {prop: 'fhdj', label: '防护等级', minWidth: '120'},\n            {prop: 'gtcc', label: '柜体尺寸', minWidth: '120'},\n            {prop: 'bsxs', label: '闭锁型式', minWidth: '120'},\n            {prop: 'edrwddl', label: '额定热稳定电流', minWidth: '180'},\n            {prop: 'edrwdsj', label: '额定热稳定时间', minWidth: '120'},\n            {prop: 'eddwddl', label: '额定动稳定电流', minWidth: '120'},\n            {prop: 'edgpnsdy', label: '额定工频耐受电压', minWidth: '140'},*/\n            /*{\n              fixed: \"right\",\n              prop: 'operation',\n              label: '操作',\n              minWidth: '130px',\n              style: {display: 'block'},\n              operation: [\n                {name: '修改', clickFun: this.pdgUpdate},\n                {name: '详情', clickFun: this.pdgDetails},\n              ]\n            },*/\n          ]\n        },\n        OrganizationSelectedList:[],\n\n        bdzDialogFormVisible: false,\n        //弹出框表单\n        form: {},\n\n        loading: false,\n        //组织树\n        selectRows:[],\n        //变电站挂接数据\n        newTestData: [],\n        treeOptions:[],\n        //删除是否可用\n        multipleSensor: true,\n        //查询参数\n        queryParams: {\n          pageNum: 1,\n          pageSize: 10,\n          roleKey: '',\n          roleName: '',\n          status: '',\n        },\n        showSearch: true,\n\n        rules:{\n          //ssgs:[{required:true,message:'请选择所属公司',trigger:'change'}],\n          sszs:[{required:true,message:'请选择所属站室',trigger:'change'}],\n          kggmc:[{required:true,message:'请输入开关柜名称',trigger:'blur'}],\n          yxbh:[{required:true,message:'请输入运行编号',trigger:'blur'}],\n          /*erpBm:[{required:true,message:'请输入ERP编码',trigger:'blur'}],\n          bgr:[{required:true,message:'请输入保管人',trigger:'blur'}],\n          zcbdfs:[{required:true,message:'请输入资产变动方式',trigger:'blur'}],\n          zcsx:[{required:true,message:'请输入资产属性',trigger:'blur'}],\n          zcbh:[{required:true,message:'请输入资产编号',trigger:'blur'}],\n          wbsYs:[{required:true,message:'请输入WBS元素',trigger:'blur'}],*/\n          tyrq:[{required:true,message:'请选择投运日期',trigger:'change'}],\n          sccj:[{required:true,message:'请输入生产厂家',trigger:'blur'}],\n          kggxh:[{required:true,message:'请输入开关柜型号',trigger:'blur'}],\n          //zcxz:[{required:true,message:'请输入资产性质',trigger:'blur'}],\n          ccrq:[{required:true,message:'请选择出厂日期',trigger:'change'}],\n          zt:[{required:true,message:'请选择状态',trigger:'change'}],\n          //dqtz:[{required:true,message:'请输入地区特征',trigger:'blur'}],\n          dydj:[{required:true,message:'请选择电压等级',trigger:'change'}],\n          /*kgyt:[{required:true,message:'请选择开关用途',trigger:'change'}],\n          kgglx:[{required:true,message:'请选择开关柜类型',trigger:'change'}],\n          fhdj:[{required:true,message:'请输入防护等级',trigger:'blur'}],\n          gtcc:[{required:true,message:'请输入柜体尺寸',trigger:'blur'}],\n          bsxs:[{required:true,message:'请输入闭锁型式',triger:'blur'}],\n          edrwddl:[{required:true,message:'请输入额定热稳定电流',trigger:'blur'}],\n          edrwdsj:[{required:true,message:'请选择额定热稳定时间',trigger:'change'}],\n          eddwddl:[{required:true,message:'请输入额定动稳定电流',trigger:'blur'}],\n          edgpnsdy:[{required:true,message:'请输入额定公频耐受电压',trigger:'blur'}]*/\n        },\n\n        //树结构上面得筛选框参数\n        treeForm: {},\n        pdgkgytList:[\n          {value: '出线',\n            label: '出线'},\n          {value: '进线',\n            label: '进线'},\n          {value: '联络',\n            label: '联络'}\n        ],\n        pdgztList:[],\n        kgglxList:[],\n        dydjList:[],\n        showButton:true,\n        selectTreeData:{},\n        isDisabled:false,\n      };\n    },\n    watch: {},\n    created() {\n      this.isShow1 = true\n      //初始化加载时加载所有变电站信息\n      this.initDictData()\n      this.getOrganizationSelected()\n      this.getNewTreeInfo()\n      this.getData()\n      this.getPdsOptionsDataList()\n    },\n    methods: {\n      //获取配电室下拉框数据\n      getPdsOptionsDataList() {\n        getPdsOptionsDataList({}).then(res => {\n          this.pdsOptionsDataList = res.data;\n        })\n      },\n      //列表查询\n      async getData(param) {\n        const par={...this.params,...param}\n        try {\n          let {data,code}=await getPdgList(par)\n          if(code==='0000'){\n            this.tableAndPageInfo.tableData = data.records;\n            this.tableAndPageInfo.pager.total = data.total;\n          }\n        }catch (e) {\n          console.log(e)\n        }\n\n      },\n      async initDictData(){\n        let {data:pdgkgyt}=await getDictTypeData('pdgkgyt')\n        let {data:pdgzt}=await getDictTypeData('pdgzt')\n        let {data:kgglx}=await getDictTypeData('kgglx')\n        let {data:dg_dydj}=await getDictTypeData('dg_dydj')\n        //this.pdgkgytList=pdgkgyt\n        this.pdgztList=pdgzt\n        this.kgglxList=kgglx\n        this.dydjList=dg_dydj\n      },\n      getNewTreeInfo() {\n        getPdsTreeList(this.treeForm).then(res => {\n          this.treeOptions = res.data;\n        })\n      },\n      //树监听事件\n      filterNode(value, data) {\n        if (!value) return true;\n        return data.label.indexOf(value) !== -1;\n      },\n      //配电柜添加按钮\n      bdzAddSensorButton() {\n        this.isDisabled = false;\n        this.showButton = true;\n        this.bdzDialogFormVisible = true\n      },\n      //每页展示数量点击事件\n      handleSizeChange() {\n\n      },\n      selectChange(rows) {\n        this.ids = rows.map(item => item.objId)\n        this.selectRows = rows\n      },\n\n      //页码改变事件\n      handleCurrentChange() {\n\n      },\n      //获取组织结构下拉框数据\n      getOrganizationSelected() {\n        let parentId = '1001';\n        getOrganizationSelected({parentId: parentId}).then(res => {\n          this.OrganizationSelectedList = res.data\n        })\n      },\n      //树点击事件\n      async handleNodeClick(data, e) {\n        this.selectTreeData=data\n        if (data.identifier == '1'){\n          //配电柜详情\n          this.pdsOptionsDataList=[];\n          this.pdsOptionsDataList.label=data.label;\n          this.pdsOptionsDataList.value=data.id;\n          await this.getData({sszs:data.id})\n        }else if(data.identifier == '2'){\n          await this.getData({objId:data.id})\n          await this.pdgDetails(this.tableAndPageInfo.tableData[0])\n        }else{\n          await this.getData()\n        }\n      },\n       pdgUpdate(row) {\n         getPdgOne({objId:row.objId}).then(res => {\n           if (res.code === \"0000\") {\n             this.bdzDialogFormVisible = true;\n             this.form = res.data;\n             this.isDisabled = false;\n             this.showButton = true;\n           } else {\n             this.$message.error(\"操作失败\");\n           }\n         })\n      },\n      ///form弹窗\n       pdgDetails(row) {\n         getPdgOne({objId:row.objId}).then(res => {\n          if (res.code === \"0000\") {\n            this.bdzDialogFormVisible = true;\n            this.form = res.data;\n            this.isDisabled = true;\n            this.showButton = false;\n          } else {\n            this.$message.error(\"操作失败\");\n          }\n        })\n      },\n      //新增\n      save() {\n        this.$refs.form.validate((valid) => {\n          if(valid){\n            addPdg(this.form).then(res => {\n              if (res.code == \"0000\") {\n                this.$message.success(\"操作成功\");\n                this.bdzDialogFormVisible = false;\n                this.getNewTreeInfo();\n                this.getData();\n              } else {\n                this.$message.error(\"操作失败\");\n              }\n            });\n          }else{\n            setTimeout(() => {\n              var isError = document.getElementsByClassName(\"is-error\");\n              if (isError[0].querySelector('input')) {\n                isError[0].querySelector('input').focus();\n              } else if (isError[0].querySelector('textarea')) {\n                isError[0].querySelector('textarea').focus();\n              }\n            }, 1)\n            return false;\n          }\n        });\n      },\n\n      handleClose(){\n        this.form={};\n        this.$nextTick(() => {\n          this.form = this.$options.data().form;\n          this.resetForm(\"form\");\n        });\n      },\n\n      /**\n       * 删除配电柜\n       */\n      remove() {\n        if (this.ids.length !== 0) {\n          this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }).then(() => {\n            removePdg(this.ids).then(res => {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              });\n              this.getNewTreeInfo();\n              this.getData();\n            })\n          })\n        } else {\n          this.$message({\n            type: 'info',\n            message: '请选择至少一条数据!'\n          });\n        }\n\n      },\n    }\n  };\n</script>\n\n<style lang='scss' scoped>\n  .head-container {\n    margin: 0 auto;\n    width: 98%;\n    height: calc(100vh - 225px);\n    overflow: auto;\n  }\n\n  .box-card {\n    margin-bottom: 15px;\n\n    .el-card__header {\n      background-color: rgb(235, 245, 255) !important;\n    }\n  }\n\n  .box-cardList {\n    height: 70%;\n  }\n\n  .item {\n    width: 200px;\n    float: left;\n  }\n\n  #main_container_dj {\n    height: calc(100vh - 84px);\n  }\n\n  .aside_height {\n    height: 96%;\n  }\n\n  .defect .el-form-item:nth-child(odd) {\n    margin-right: 70px;\n  }\n\n  /*!*弹出框内宽度设置*!*/\n  .el-select {\n    width: 100%;\n  }\n\n  .el-date-editor{\n    width: 100%;\n  }\n\n  .el-carousel__item h3 {\n    color: #475669;\n    font-size: 14px;\n    opacity: 0.75;\n    line-height: 150px;\n\n  }\n\n  .el-carousel__item:nth-child(2n) {\n    background-color: #99a9bf;\n  }\n\n  .el-carousel__item:nth-child(2n+1) {\n    background-color: #d3dce6;\n  }\n  //有子节点 且未展开\n  .el-tree ::v-deep .el-icon-caret-right:before {\n    background: url(\"../../../../assets/image/add.png\") no-repeat 0;\n    content: \"\";\n    display: block;\n    width: 16px;\n    height: 16px;\n    font-size: 16px;\n    background-size: 16px;\n  }\n  //有子节点 且已展开\n  .el-tree\n  ::v-deep\n  .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {\n    background: url(\"../../../../assets/image/prep.png\") no-repeat 0;\n    content: \"\";\n    display: block;\n    width: 16px;\n    height: 16px;\n    font-size: 16px;\n    background-size: 16px;\n  }\n  //没有子节点\n  .el-tree ::v-deep .el-tree-node__expand-icon.is-leaf::before {\n    background: transparent;\n    content: \"\";\n    display: block;\n    width: 16px;\n    height: 16px;\n    font-size: 16px;\n    background-size: 16px;\n  }\n</style>\n"]}]}