{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\syzyyxzwh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\syzyyxzwh.vue", "mtime": 1706897323692}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["syzyyxzwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmFA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "syzyyxzwh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card class=\"aside_height\" shadow=\"never\">\n          <div style=\" overflow: auto;height: 90vh\">\n            <el-col style=\"padding:0\">\n              <el-tree :expand-on-click-node=\"false\"\n                       highlight-current\n                       id=\"tree\"\n                       :data=\"treeOptions\"\n                       :default-expanded-keys=\"['1']\"\n                       @node-click=\"handleNodeClick\"\n                       node-key=\"nodeId\"\n              />\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-white>\n          <el-white class=\"button-group\">\n            <div class=\"button_btn\">\n              <el-button @click=\"addSensorButton\"\n                         type=\"primary\" icon=\"el-icon-plus\"\n              >新增\n              </el-button>\n              <!--<el-button @click=\"handleDelete\"-->\n              <!--           type=\"danger\" icon=\"el-icon-delete\">删除-->\n              <!--</el-button>-->\n            </div>\n            <comp-table :table-and-page-info=\"tableAndPageInfo\" @update:multipleSelection=\"handleSelectionChange\"\n                        height=\"76.8vh\"\n            >\n              <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\"\n                               width=\"160\"\n                               :resizable=\"false\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-button @click=\"updateInfo(scope.row)\" v-show=\"scope.row.createBy == currentUser\" type=\"text\"\n                             size=\"small\" title=\"修改\"  class='el-icon-edit'\n                  >\n                  </el-button>\n                  <el-button @click=\"detailsInfo(scope.row)\" type=\"text\" size=\"small\" title=\"详情\" class=\"el-icon-view\"></el-button>\n                  <el-button @click=\"handleDelete(scope.row)\" v-show=\"scope.row.createBy == currentUser\" type=\"text\"\n                             size=\"small\"  title=\"删除\" class=\"el-icon-delete\"\n                  >\n                  </el-button>\n                </template>\n              </el-table-column>\n            </comp-table>\n          </el-white>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <el-dialog :title=title :visible.sync=\"isShowDetails\" width=\"40%\" v-dialogDrag>\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验性质名称：\" prop=\"syxzmc\" label-width=\"140px\">\n              <el-input placeholder=\"请输入试验性质名称\" v-model=\"form.syxzmc\" :disabled=\"isDisabled\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"编码：\" prop=\"bm\">\n              <el-input placeholder=\"请选择编码\" v-model=\"form.bm\" :disabled=\"isDisabled\" style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button v-if=\"title==='新增' || title==='修改'\" type=\"primary\" @click=\"save\">确 认</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getPageDataList, getTreeData, remove, saveOrUpdate } from '@/api/dagangOilfield/bzgl/syzyxz'\n\nexport default {\n  name: 'syzyyxzwh',\n  data() {\n    return {\n      currentUser: this.$store.getters.name,\n      form: {\n        objId: undefined,\n        syzyid: undefined,\n        syxzmc: undefined,\n        bm: undefined\n      },\n      isShowDetails: false,\n      title: '',\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: '试验性质名称', prop: 'syxzmc', minWidth: '150' },\n          { label: '编码', prop: 'bm', minWidth: '150' }\n          // {\n          //   prop: 'operation',\n          //   label: '操作',\n          //   minWidth: '100px',\n          //   style: {display: 'block'},\n          //   //操作列固定再右侧\n          //   fixed: 'right',\n          //   operation: [\n          //     {name: '修改', clickFun: this.updateInfo},\n          //     {name: '详情', clickFun: this.detailsInfo},\n          //\n          //   ]\n          // },\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      //组织树\n      treeOptions: [],\n      //查询参数\n      queryParams: {\n        syzyid: undefined,\n        pageNum: 1,\n        pageSize: 10\n      },\n      isDisabled: false,\n      rules: {\n        syxzmc: [\n          {required: true, message: \"试验性质名称不能为空\", trigger: \"blur\"},\n        ],\n        bm: [\n          {required: true, message: \"编码不能为空\", trigger: \"blur\"},\n        ]\n      },\n    }\n  },\n  watch: {},\n  created() {\n    this.getData()\n    this.getTreeOption()\n  },\n  methods: {\n    //查询树方法\n    getTreeOption() {\n      getTreeData().then(res => {\n        this.treeOptions = res.data\n      })\n    },\n    //树节点点击事件\n    handleNodeClick(data) {\n      if (data.id != undefined) {\n        this.form.syzyid = data.id\n        this.queryParams.syzyid = data.id\n        this.getData()\n      }\n    },\n    //查询列表\n    getData(params) {\n      const param = { ...this.queryParams, ...params }\n      getPageDataList(param).then(res => {\n        console.log(res)\n        this.tableAndPageInfo.tableData = res.data.records\n        this.tableAndPageInfo.pager.total = res.data.total\n      })\n    },\n    //添加按钮\n    addSensorButton() {\n      if (this.form.syzyid === undefined) {\n        this.$message.warning('请在选择专业后新增记录')\n        return\n      }\n      //打开新增弹窗\n      this.isShowDetails = true\n      //使弹出框表单内容可以进行编辑\n      this.isDisabled = false\n      //置空表单\n      this.form.syxzmc = undefined\n      this.form.bm = undefined\n      this.form.objId = undefined\n      //设置弹出框标题\n      this.title = '新增'\n    },\n    //保存按钮\n    async save() {\n      this.$refs['form'].validate((valid) => {\n        if (valid) {\n          try {\n            saveOrUpdate(this.form).then(res=>{\n              if (res.code === '0000') {\n                this.$message.success('操作成功')\n                this.form.syzyid = undefined\n              }\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = 'Y'\n              this.getData()\n              this.isShowDetails = false\n            })\n          } catch (e) {\n            console.log(e)\n          }\n        }else{\n          this.$message.error(\"校验未通过！\");\n          return false;\n        }\n      })\n    },\n    /**\n     * 表格多选框\n     */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.objId)\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false\n      this.form.syzyid = undefined\n    },\n    //编辑按钮\n    updateInfo(row) {\n      this.title = '修改'\n      this.isDisabled = false\n      this.isShowDetails = true\n      this.form = { ...row }\n      this.form.syzyid = undefined\n    },\n    //详情按钮\n    detailsInfo(row) {\n      this.title = '详情'\n      //打开弹窗\n      this.isShowDetails = true\n      //把行数据给弹出框表单\n      this.form = { ...row }\n      //将表单不可编辑\n      this.isDisabled = true\n      this.form.syzyid = undefined\n    },\n    /**\n     * 处理批量删除\n     */\n    handleDelete(row) {\n      this.form = row\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        remove([this.form.objId]).then(res => {\n          if (res.code === '0000') {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            })\n          } else {\n            this.$message({\n              type: 'error',\n              message: '删除失败!'\n            })\n          }\n          this.getData()\n        })\n      })\n\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  max-height: 79vh;\n  overflow: auto;\n}\n</style>\n"]}]}