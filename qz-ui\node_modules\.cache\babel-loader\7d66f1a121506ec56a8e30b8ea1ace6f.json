{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\syxm.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\syxm.js", "mtime": 1706897314376}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagangOilfield/bzgl/syxm.js"], "names": ["baseUrl", "getPageDataList", "query", "api", "requestPost", "getSyzyOptionsSelected", "getGlSyzxmDataListByPage", "getSyzxmLibraryDataListByPage", "addBatchSyzxmToXmzxm", "remove", "removeKxzData", "saveOrUpdate", "getSblxTree", "requestGet", "saveOrUpdateKxzData", "getBwSelect", "getZxmSelect"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;;AAEA,IAAMA,OAAO,GAAG,cAAhB;AAEA;;;;;;AAKO,SAASC,eAAT,CAAyBC,KAAzB,EAAgC;AACrC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,6BAA1B,EAAyDE,KAAzD,EAAgE,CAAhE,CAAP;AACD;AAED;;;;;;;AAKO,SAASG,sBAAT,GAAkC;AACvC,SAAOF,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,8BAA1B,EAA0D,IAA1D,EAAgE,CAAhE,CAAP;AACD;AAGD;;;;;;;AAKO,SAASM,wBAAT,CAAkCJ,KAAlC,EAAyC;AAC9C,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,gCAA1B,EAA4DE,KAA5D,EAAmE,CAAnE,CAAP;AACD;AAGD;;;;;;;AAKO,SAASK,6BAAT,CAAuCL,KAAvC,EAA8C;AACnD,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,qCAA1B,EAAiEE,KAAjE,EAAwE,CAAxE,CAAP;AACD;AAED;;;;;;;AAKO,SAASM,oBAAT,CAA8BN,KAA9B,EAAqC;AAC1C,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,4BAA1B,EAAwDE,KAAxD,EAA+D,CAA/D,CAAP;AACD;AAED;;;;;;;AAKO,SAASO,MAAT,CAAgBP,KAAhB,EAAuB;AAC5B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,kBAA1B,EAA8CE,KAA9C,EAAqD,CAArD,CAAP;AACD;AAED;;;;;;;AAKO,SAASQ,aAAT,CAAuBR,KAAvB,EAA8B;AACnC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,uBAA1B,EAAmDE,KAAnD,EAA0D,CAA1D,CAAP;AACD;AAGD;;;;;;;AAKO,SAASS,YAAT,CAAsBT,KAAtB,EAA6B;AAClC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,oBAA1B,EAAgDE,KAAhD,EAAuD,CAAvD,CAAP;AACD;AAED;;;;;;;;AAOO,SAASU,WAAT,CAAqBV,KAArB,EAA4B;AACjC,SAAOC,iBAAIU,UAAJ,CAAeb,OAAO,GAAG,uBAAzB,EAAkDE,KAAlD,EAAyD,CAAzD,CAAP;AACD;AAGD;;;;;;;AAKO,SAASY,mBAAT,CAA6BZ,KAA7B,EAAoC;AACzC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,4BAA1B,EAAwDE,KAAxD,EAA+D,CAA/D,CAAP;AACD;AAED;;;;;;AAIO,SAASa,WAAT,CAAqBb,KAArB,EAA2B;AAChC,SAAOC,iBAAIU,UAAJ,CAAeb,OAAO,GAAC,yBAAvB,EAAiDE,KAAjD,EAAuD,CAAvD,CAAP;AACD;AAED;;;;;;AAIO,SAASc,YAAT,CAAsBd,KAAtB,EAA4B;AACjC,SAAOC,iBAAIU,UAAJ,CAAeb,OAAO,GAAC,sBAAvB,EAA8CE,KAA9C,EAAoD,CAApD,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\n\nconst baseUrl = \"/manager-api\";\n\n/**\n * 查询试验项目方法\n * @param query\n * @returns {Promise | Promise<unknown>}\n */\nexport function getPageDataList(query) {\n  return api.requestPost(baseUrl + '/syxm/getSyxmDataListByPage', query, 2);\n}\n\n/**\n * 获取试验专业下拉框数据\n * @param\n * @returns {Promise | Promise<any>}\n */\nexport function getSyzyOptionsSelected() {\n  return api.requestPost(baseUrl + '/syxm/getSyzyOptionsSelected', null, 2);\n}\n\n\n/**\n * 获取关联试验子项目列表\n * @param query\n * @returns {Promise | Promise<any>}\n */\nexport function getGlSyzxmDataListByPage(query) {\n  return api.requestPost(baseUrl + '/syxm/getGlSyzxmDataListByPage', query, 2);\n}\n\n\n/**\n * 分页获取试验子项目库数据\n * @param query\n * @returns {Promise | Promise<any>}\n */\nexport function getSyzxmLibraryDataListByPage(query) {\n  return api.requestPost(baseUrl + '/syxm/getSyzxmLibraryDataListByPage', query, 2);\n}\n\n/**\n * 批量添加子项目到关联子项目数据表中\n * @param query\n * @returns {Promise | Promise<any>}\n */\nexport function addBatchSyzxmToXmzxm(query) {\n  return api.requestPost(baseUrl + '/syxm/addBatchSyzxmToXmzxm', query, 2);\n}\n\n/***\n * 删除项目\n * @param query\n * @returns {Promise | Promise<unknown>}\n */\nexport function remove(query) {\n  return api.requestPost(baseUrl + '/syxm/removeData', query, 2)\n}\n\n/**\n * 删除关联子列表数据\n * @param query\n * @returns {Promise | Promise<any>}\n */\nexport function removeKxzData(query) {\n  return api.requestPost(baseUrl + '/syxm/removeGlZxmData', query, 2)\n}\n\n\n/**\n * 添加或修改方法试验项目\n * @param query\n * @returns {Promise | Promise<unknown>}\n */\nexport function saveOrUpdate(query) {\n  return api.requestPost(baseUrl + '/syxm/saveOrUpdate', query, 2);\n}\n\n/**\n *\n * 获取试验部位左侧树结构\n * @param query\n * @returns {Promise | Promise<any>}\n */\n\nexport function getSblxTree(query) {\n  return api.requestGet(baseUrl + '/sybw/getSblxTreeInfo', query, 2);\n}\n\n\n/**\n * 添加或修改方法\n * @param query\n * @returns {Promise | Promise<unknown>}\n */\nexport function saveOrUpdateKxzData(query) {\n  return api.requestPost(baseUrl + '/syzxm/saveOrUpdateKxzData', query, 2);\n}\n\n/**\n * 根据设备类型编码获取部位下拉表\n * @returns \n */\nexport function getBwSelect(query){\n  return api.requestGet(baseUrl+'/sybw/getMwtUdSySybwVal',query,2);\n}\n\n/**\n * 获取试验子项目下拉框数据\n * @param {}  \n */\nexport function getZxmSelect(query){\n  return api.requestGet(baseUrl+'/syzxm/getZxmdyValue',query,1)\n}\n"]}]}