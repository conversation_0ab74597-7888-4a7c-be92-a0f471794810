{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\dgTodoItem\\processTodo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\dgTodoItem\\processTodo.vue", "mtime": 1755545381662}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["processTodo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAyJA;;AACA;;AACA;;AAMA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,UAAA,EAAA;AAAA,IAAA,QAAA,EAAA;AAAA,GADA;AAEA,EAAA,IAAA,EAAA,aAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,MAAA,EAAA,EADA;AAEA,UAAA,YAAA,EAAA,EAFA;AAGA,UAAA,cAAA,EAAA,EAHA;AAIA,UAAA,aAAA,EAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAJA;AAPA,OADA;AAeA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,YAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,cAAA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,gBAAA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,eAAA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,IAAA,EAAA,YAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SARA,CARA;AAkBA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAlBA,OAfA;AAmCA,MAAA,MAAA,EAAA;AACA,QAAA,QAAA,EAAA;AADA,OAnCA;AAsCA,MAAA,UAAA,EAAA,IAtCA;AAuCA,MAAA,UAAA,EAAA,EAvCA;AAwCA,MAAA,UAAA,EAAA;AACA,QAAA,EAAA,EAAA,CADA;AAEA,QAAA,EAAA,EAAA;AAFA,OAxCA;AA4CA,MAAA,QAAA,EAAA,KA5CA;AA6CA,MAAA,QAAA,EAAA,EA7CA;AA8CA,MAAA,UAAA,EAAA,KA9CA;AA+CA,MAAA,QAAA,EAAA,EA/CA;AAgDA,MAAA,YAAA,EAAA,KAhDA;AAiDA,MAAA,WAAA,EAAA;AACA,QAAA,oBAAA,EAAA,EADA;AAEA,QAAA,WAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,EAHA;AAIA,QAAA,SAAA,EAAA,EAJA;AAKA,QAAA,QAAA,EAAA,EALA;AAMA,QAAA,WAAA,EAAA;AANA;AAjDA,KAAA;AA0DA,GA9DA;AA+DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAA,QAAA,kCACA,oBAAA,UAAA,EAAA;AACA,IAAA,eAAA,EAAA,YADA;AAEA,IAAA,eAAA,EAAA,YAFA;AAGA,IAAA,eAAA,EAAA,YAHA;AAIA,IAAA,mBAAA,EAAA,gBAJA;AAKA,IAAA,cAAA,EAAA,WALA;AAMA,IAAA,UAAA,EAAA;AANA,GAAA,CADA,CAtEA;AAgFA,EAAA,OAhFA,qBAgFA;AACA;AACA,SAAA,gBAAA;AACA,GAnFA;AAoFA,EAAA,OApFA,qBAoFA;AAAA;;AACA;AACA,SAAA,SAAA,CAAA,YAAA;AACA,MAAA,KAAA,CAAA,qBAAA,GADA,CAEA;;;AACA,MAAA,KAAA,CAAA,iBAAA;AACA,KAJA;AAKA,GA3FA;AA6FA;AACA,EAAA,SA9FA,uBA8FA;AAAA;;AACA,IAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EADA,CAEA;;AACA,SAAA,OAAA,CAAA,KAAA,MAAA,CAAA,KAAA,EAHA,CAKA;;AACA,SAAA,SAAA,CAAA,YAAA;AACA;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,MAAA,CAAA,qBAAA,GADA,CAEA;;;AACA,QAAA,MAAA,CAAA,iBAAA;AACA,OAJA,EAIA,GAJA,CAAA;AAKA,KAPA;AAQA,GA5GA;AA8GA;AACA,EAAA,WA/GA,yBA+GA;AACA,IAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EADA,CAEA;;AACA,SAAA,kCAAA,GAHA,CAIA;;AACA,SAAA,oBAAA;AACA,GArHA;AAuHA;AACA,EAAA,aAxHA,2BAwHA;AACA;AACA,SAAA,oBAAA;AACA,GA3HA;AA4HA,EAAA,gBA5HA,4BA4HA,EA5HA,EA4HA,IA5HA,EA4HA,IA5HA,EA4HA;AACA;AACA;AACA;AACA,IAAA,OAAA,CAAA,GAAA,CAAA,iBAAA,EAAA,KAAA,mBAAA;AACA,IAAA,IAAA;AACA,GAlIA;AAmIA,EAAA,OAAA,8DACA,sBAAA,UAAA,EAAA,CAAA,eAAA,EAAA,gBAAA,EAAA,gBAAA,EAAA,oBAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,kBAAA,CAAA,CADA;AAGA;AACA,IAAA,gBAJA,8BAIA;AACA;AACA,UAAA,cAAA,GAAA,CAAA;;AACA,UAAA,KAAA,gBAAA,EAAA;AACA,QAAA,cAAA,GAAA,KAAA,gBAAA,CAAA,SAAA;AACA,OAFA,MAEA;AACA;AACA,YAAA,YAAA,GAAA,QAAA,CAAA,aAAA,CAAA,yBAAA,KACA,QAAA,CAAA,aAAA,CAAA,+BAAA,CADA;;AAEA,YAAA,YAAA,EAAA;AACA,UAAA,cAAA,GAAA,YAAA,CAAA,SAAA;AACA;AACA;;AAEA,MAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,cAAA;AAEA,WAAA,aAAA,CAAA;AACA,QAAA,UAAA,EAAA,KAAA,UADA;AAEA,QAAA,UAAA,EAAA,KAAA,UAAA,CAAA,IAFA;AAGA,QAAA,UAAA,EAAA;AACA,UAAA,QAAA,EAAA,KAAA,gBAAA,CAAA,KAAA,CAAA,QADA;AAEA,UAAA,OAAA,EAAA,KAAA,gBAAA,CAAA,KAAA,CAAA;AAFA,SAHA;AAOA,QAAA,cAAA,EAAA,cAPA;AAQA,QAAA,SAAA,EAAA,KAAA,gBAAA,CAAA,SARA;AASA,QAAA,KAAA,EAAA,KAAA,gBAAA,CAAA,KAAA,CAAA;AATA,OAAA;AAWA,KA/BA;AAiCA;AACA,IAAA,kCAlCA,gDAkCA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,eAAA,EAAA,KAAA,mBAAA;AAEA,WAAA,aAAA,CAAA;AACA,QAAA,UAAA,EAAA,KAAA,UADA;AAEA,QAAA,UAAA,EAAA,KAAA,UAAA,CAAA,IAFA;AAGA,QAAA,UAAA,EAAA;AACA,UAAA,QAAA,EAAA,KAAA,gBAAA,CAAA,KAAA,CAAA,QADA;AAEA,UAAA,OAAA,EAAA,KAAA,gBAAA,CAAA,KAAA,CAAA;AAFA,SAHA;AAOA,QAAA,cAAA,EAAA,KAAA,mBAPA;AAOA;AACA,QAAA,SAAA,EAAA,KAAA,gBAAA,CAAA,SARA;AASA,QAAA,KAAA,EAAA,KAAA,gBAAA,CAAA,KAAA,CAAA;AATA,OAAA;AAWA,KAhDA;AAkDA;AACA,IAAA,gBAnDA,8BAmDA;AACA;AACA,UAAA,KAAA,eAAA,IAAA,KAAA,eAAA,KAAA,KAAA,UAAA,EAAA;AACA,aAAA,UAAA,GAAA,KAAA,eAAA;AACA,aAAA,MAAA,CAAA,QAAA,GAAA,KAAA,UAAA,CAAA,KAAA,eAAA,CAAA;AACA,OALA,CAOA;;;AACA,UAAA,KAAA,eAAA,IAAA,MAAA,CAAA,IAAA,CAAA,KAAA,eAAA,EAAA,MAAA,GAAA,CAAA,EAAA;AACA,aAAA,UAAA,CAAA,IAAA,mCAAA,KAAA,eAAA;AACA,OAVA,CAYA;;;AACA,UAAA,KAAA,eAAA,EAAA;AACA,aAAA,gBAAA,CAAA,KAAA,CAAA,QAAA,GAAA,KAAA,eAAA,CAAA,QAAA;AACA,aAAA,gBAAA,CAAA,KAAA,CAAA,OAAA,GAAA,KAAA,eAAA,CAAA,OAAA;AACA,OAhBA,CAkBA;AACA;;;AACA,WAAA,OAAA,CAAA,KAAA,MAAA,CAAA,KAAA;AACA,KAxEA;AA0EA;AACA,IAAA,qBA3EA,mCA2EA;AAAA;;AACA,UAAA,KAAA,mBAAA,GAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,YAAA;AACA;AACA,cAAA,eAAA,GAAA,IAAA,CAFA,CAIA;;AACA,UAAA,eAAA,GAAA,QAAA,CAAA,aAAA,CAAA,yBAAA,CAAA,CALA,CAOA;;AACA,cAAA,CAAA,eAAA,EAAA;AACA,YAAA,eAAA,GAAA,QAAA,CAAA,aAAA,CAAA,+BAAA,CAAA;AACA,WAVA,CAYA;;;AACA,cAAA,CAAA,eAAA,EAAA;AACA,gBAAA,WAAA,GAAA,MAAA,CAAA,GAAA,CAAA,aAAA,CAAA,OAAA,CAAA;;AACA,gBAAA,WAAA,EAAA;AACA,cAAA,eAAA,GAAA,WAAA,CAAA,aAAA,CAAA,yBAAA,CAAA;AACA;AACA;;AAEA,UAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,MAAA,CAAA,mBAAA,EAAA,KAAA,EAAA,eAAA;;AAEA,cAAA,eAAA,EAAA;AACA,YAAA,eAAA,CAAA,SAAA,GAAA,MAAA,CAAA,mBAAA,CADA,CAGA;;AACA,YAAA,UAAA,CAAA,YAAA;AACA,cAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA,eAAA,CAAA,SAAA;AACA,aAFA,EAEA,GAFA,CAAA;AAGA,WAPA,MAOA;AACA,YAAA,OAAA,CAAA,IAAA,CAAA,WAAA;AACA;AACA,SAhCA;AAiCA;AACA,KA/GA;AAiHA;AACA,IAAA,iBAlHA,+BAkHA;AAAA;;AACA,WAAA,SAAA,CAAA,YAAA;AACA;AACA,YAAA,MAAA,CAAA,cAAA,EAAA;AACA,UAAA,MAAA,CAAA,oBAAA;AACA,SAJA,CAMA;;;AACA,YAAA,eAAA,GAAA,IAAA,CAPA,CASA;;AACA,QAAA,eAAA,GAAA,QAAA,CAAA,aAAA,CAAA,yBAAA,CAAA,CAVA,CAYA;;AACA,YAAA,CAAA,eAAA,EAAA;AACA,UAAA,eAAA,GAAA,QAAA,CAAA,aAAA,CAAA,+BAAA,CAAA;AACA,SAfA,CAiBA;;;AACA,YAAA,CAAA,eAAA,EAAA;AACA,cAAA,WAAA,GAAA,MAAA,CAAA,GAAA,CAAA,aAAA,CAAA,OAAA,CAAA;;AACA,cAAA,WAAA,EAAA;AACA,YAAA,eAAA,GAAA,WAAA,CAAA,aAAA,CAAA,yBAAA,CAAA;AACA;AACA,SAvBA,CAyBA;;;AACA,YAAA,CAAA,eAAA,EAAA;AACA,cAAA,MAAA,GAAA,QAAA,CAAA,gBAAA,CAAA,yBAAA,CAAA;;AADA,mEAEA,MAFA;AAAA;;AAAA;AAEA,gEAAA;AAAA,kBAAA,KAAA;AACA,kBAAA,OAAA,GAAA,KAAA,CAAA,OAAA,CAAA,WAAA,CAAA;;AACA,kBAAA,OAAA,IAAA,OAAA,CAAA,KAAA,CAAA,MAAA,KAAA,MAAA,EAAA;AACA,gBAAA,eAAA,GAAA,KAAA;AACA;AACA;AACA;AARA;AAAA;AAAA;AAAA;AAAA;AASA;;AAEA,QAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,eAAA;;AAEA,YAAA,eAAA,EAAA;AACA,UAAA,MAAA,CAAA,gBAAA,GAAA,eAAA,CADA,CACA;;AACA,UAAA,MAAA,CAAA,cAAA,GAAA,MAAA,CAAA,QAAA,CAAA,YAAA;AACA,gBAAA,gBAAA,GAAA,eAAA,CAAA,SAAA;AACA,YAAA,OAAA,CAAA,GAAA,CAAA,SAAA,EAAA,gBAAA,EAFA,CAGA;;AACA,YAAA,MAAA,CAAA,kBAAA,CAAA,gBAAA;AACA,WALA,EAKA,GALA,CAAA;AAMA,UAAA,eAAA,CAAA,gBAAA,CAAA,QAAA,EAAA,MAAA,CAAA,cAAA;AACA,SATA,MASA;AACA,UAAA,OAAA,CAAA,IAAA,CAAA,oBAAA;AACA;AACA,OAnDA;AAoDA,KAvKA;AAyKA;AACA,IAAA,oBA1KA,kCA0KA;AACA,UAAA,KAAA,gBAAA,IAAA,KAAA,cAAA,EAAA;AACA,aAAA,gBAAA,CAAA,mBAAA,CAAA,QAAA,EAAA,KAAA,cAAA;;AACA,aAAA,cAAA,GAAA,IAAA;AACA,aAAA,gBAAA,GAAA,IAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,SAAA;AACA;AACA,KAjLA;AAmLA;AACA,IAAA,QApLA,oBAoLA,IApLA,EAoLA,KApLA,EAoLA;AACA,UAAA,SAAA;AACA,UAAA,YAAA,GAAA,CAAA;AACA,aAAA,YAAA;AAAA;;AAAA,0CAAA,IAAA;AAAA,UAAA,IAAA;AAAA;;AACA,YAAA,WAAA,GAAA,IAAA,CAAA,GAAA,EAAA;;AAEA,YAAA,WAAA,GAAA,YAAA,GAAA,KAAA,EAAA;AACA,UAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,IAAA;AACA,UAAA,YAAA,GAAA,WAAA;AACA,SAHA,MAGA;AACA,UAAA,YAAA,CAAA,SAAA,CAAA;AACA,UAAA,SAAA,GAAA,UAAA,CAAA,YAAA;AACA,YAAA,IAAA,CAAA,KAAA,CAAA,MAAA,EAAA,IAAA;AACA,YAAA,YAAA,GAAA,IAAA,CAAA,GAAA,EAAA;AACA,WAHA,EAGA,KAAA,IAAA,WAAA,GAAA,YAAA,CAHA,CAAA;AAIA;AACA,OAbA;AAcA,KArMA;AAuMA;AACA,IAAA,sBAxMA,kCAwMA,OAxMA,EAwMA,KAxMA,EAwMA;AACA,UAAA,WAAA,GAAA,KAAA,gBAAA,CAAA,KAAA,CAAA,OAAA;AACA,UAAA,QAAA,GAAA,KAAA,gBAAA,CAAA,KAAA,CAAA,QAAA;AACA,UAAA,OAAA,GAAA,IAAA,CAAA,IAAA,CAAA,KAAA,GAAA,QAAA,KAAA,CAAA;AAEA,MAAA,OAAA,CAAA,GAAA,CAAA,OAAA,EAAA;AACA,QAAA,WAAA,EAAA,WADA;AAEA,QAAA,OAAA,EAAA,OAFA;AAGA,QAAA,KAAA,EAAA,KAHA;AAIA,QAAA,aAAA,EAAA,OAAA,CAAA,MAJA;AAKA,QAAA,OAAA,EAAA,OAAA,CAAA,MAAA,GAAA;AALA,OAAA,EALA,CAaA;;AACA,UAAA,WAAA,GAAA,OAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,6BAAA,WAAA,2CAAA,OAAA,2CAAA,OAAA;AACA,aAAA,gBAAA,CAAA,KAAA,CAAA,OAAA,GAAA,OAAA,CAFA,CAGA;;AACA,aAAA,cAAA,CAAA;AACA,UAAA,QAAA,EAAA,QADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,aAAA,kBAAA,CAAA,CAAA,EARA,CAQA;;AACA,eAAA,IAAA,CATA,CASA;AACA,OAxBA,CA0BA;;;AACA,UAAA,OAAA,CAAA,MAAA,KAAA,CAAA,IAAA,KAAA,GAAA,CAAA,IAAA,WAAA,GAAA,CAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,6BAAA,WAAA,iDAAA,KAAA,6CAAA,OAAA;AACA,aAAA,gBAAA,CAAA,KAAA,CAAA,OAAA,GAAA,OAAA,CAFA,CAGA;;AACA,aAAA,cAAA,CAAA;AACA,UAAA,QAAA,EAAA,QADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,aAAA,kBAAA,CAAA,CAAA,EARA,CAQA;;AACA,eAAA,IAAA,CATA,CASA;AACA,OArCA,CAuCA;;;AACA,UAAA,KAAA,KAAA,CAAA,IAAA,WAAA,KAAA,CAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,aAAA;AACA,aAAA,gBAAA,CAAA,KAAA,CAAA,OAAA,GAAA,CAAA,CAFA,CAGA;;AACA,aAAA,cAAA,CAAA;AACA,UAAA,QAAA,EAAA,QADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,aAAA,kBAAA,CAAA,CAAA,EARA,CAQA;;AACA,eAAA,IAAA,CATA,CASA;AACA;;AAEA,aAAA,KAAA,CApDA,CAoDA;AACA,KA7PA;AA+PA;AACA,IAAA,qBAhQA,mCAgQA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,gBAAA,EADA,CAGA;;AACA,UAAA,UAAA,GAAA,CACA;AAAA,QAAA,IAAA,EAAA,eAAA;AAAA,QAAA,EAAA,EAAA,QAAA,CAAA,aAAA,CAAA,gBAAA;AAAA,OADA,EAEA;AAAA,QAAA,IAAA,EAAA,wBAAA;AAAA,QAAA,EAAA,EAAA,QAAA,CAAA,aAAA,CAAA,yBAAA;AAAA,OAFA,EAGA;AAAA,QAAA,IAAA,EAAA,8BAAA;AAAA,QAAA,EAAA,EAAA,QAAA,CAAA,aAAA,CAAA,+BAAA;AAAA,OAHA,EAIA;AAAA,QAAA,IAAA,EAAA,qBAAA;AAAA,QAAA,EAAA,EAAA,KAAA,GAAA,CAAA,aAAA,CAAA,+BAAA;AAAA,OAJA,CAAA;AAOA,MAAA,UAAA,CAAA,OAAA,CAAA,UAAA,SAAA,EAAA;AACA,YAAA,SAAA,CAAA,EAAA,EAAA;AACA,UAAA,OAAA,CAAA,GAAA,WAAA,SAAA,CAAA,IAAA,QAAA;AACA,YAAA,OAAA,EAAA,SAAA,CAAA,EADA;AAEA,YAAA,SAAA,EAAA,SAAA,CAAA,EAAA,CAAA,SAFA;AAGA,YAAA,YAAA,EAAA,SAAA,CAAA,EAAA,CAAA,YAHA;AAIA,YAAA,YAAA,EAAA,SAAA,CAAA,EAAA,CAAA,YAJA;AAKA,YAAA,SAAA,EAAA,SAAA,CAAA,EAAA,CAAA,YAAA,GAAA,SAAA,CAAA,EAAA,CAAA;AALA,WAAA;AAOA,SARA,MAQA;AACA,UAAA,OAAA,CAAA,GAAA,WAAA,SAAA,CAAA,IAAA;AACA;AACA,OAZA,EAXA,CAyBA;;AACA,UAAA,WAAA,GAAA,QAAA,CAAA,gBAAA,CAAA,yBAAA,CAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,2BAAA,EAAA,WAAA;AACA,MAAA,WAAA,CAAA,OAAA,CAAA,UAAA,OAAA,EAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,mBAAA,KAAA,QAAA;AACA,UAAA,OAAA,EAAA,OADA;AAEA,UAAA,SAAA,EAAA,OAAA,CAAA,SAFA;AAGA,UAAA,YAAA,EAAA,OAAA,CAAA,YAHA;AAIA,UAAA,YAAA,EAAA,OAAA,CAAA,YAJA;AAKA,UAAA,SAAA,EAAA,OAAA,CAAA,YAAA,GAAA,OAAA,CAAA,YALA;AAMA,UAAA,WAAA,EAAA,OAAA,CAAA,OAAA,CAAA,WAAA;AANA,SAAA;AAQA,OATA;AAWA,MAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,KAAA,mBAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,cAAA;AACA,KAzSA;AA2SA,IAAA,OA3SA,mBA2SA,KA3SA,EA2SA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA;AACA,gBAAA,MAAA,CAAA,MAAA,2FAAA,MAAA,CAAA,MAAA,GAAA,KAAA,GAAA,MAAA,CAAA,UAAA,CAAA,IAAA;;AAEA,oBAAA,CAAA,MAAA,CAAA,MAAA,CAAA,UAAA,EAAA;AACA,kBAAA,MAAA,CAAA,MAAA,CAAA,UAAA,GAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA,IAAA;AACA;;AANA;AAAA,uBAQA,wFACA,MAAA,CAAA,MADA;AAEA,kBAAA,OAAA,EAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,OAFA;AAGA,kBAAA,QAAA,EAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA;AAHA,mBARA;;AAAA;AAAA;AAQA,gBAAA,IARA,qBAQA,IARA;AAQA,gBAAA,IARA,qBAQA,IARA;;AAAA,sBAcA,IAAA,KAAA,MAdA;AAAA;AAAA;AAAA;;AAeA;AACA,gBAAA,kBAhBA,GAgBA,MAAA,CAAA,sBAAA,CAAA,IAAA,CAAA,OAAA,EAAA,IAAA,CAAA,KAAA,CAhBA;;AAAA,qBAkBA,kBAlBA;AAAA;AAAA;AAAA;;AAmBA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,eAAA;AApBA,iDAqBA,MAAA,CAAA,OAAA,CAAA,KAAA,CArBA;;AAAA;AAwBA,gBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,gBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAzBA,CA2BA;;AACA,gBAAA,MAAA,CAAA,aAAA,CAAA;AACA,kBAAA,SAAA,EAAA,IAAA,CAAA,OADA;AAEA,kBAAA,KAAA,EAAA,IAAA,CAAA;AAFA,iBAAA,EA5BA,CAiCA;;;AACA,gBAAA,MAAA,CAAA,gBAAA,GAlCA,CAoCA;;;AACA,gBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,kBAAA,UAAA,CAAA,YAAA;AACA,oBAAA,MAAA,CAAA,qBAAA;;AACA,oBAAA,MAAA,CAAA,iBAAA;AACA,mBAHA,EAGA,GAHA,CAAA;AAIA,iBANA;;AArCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA6CA,KAxVA;AAyVA,IAAA,YAzVA,wBAyVA,IAzVA,EAyVA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KA3VA;AA6VA;AACA,IAAA,iBA9VA,6BA8VA,GA9VA,EA8VA,IA9VA,EA8VA;AACA;AACA,WAAA,cAAA,CAAA,IAAA,EAFA,CAGA;;AACA,UAAA,GAAA,CAAA,KAAA,KAAA,SAAA,EAAA;AACA,aAAA,UAAA,CAAA,IAAA,GAAA,IAAA;AACA,aAAA,gBAAA,CAAA,KAAA,CAAA,OAAA,GAAA,CAAA,CAFA,CAEA;;AACA,aAAA,OAAA,GAHA,CAIA;;AACA,aAAA,cAAA,CAAA;AACA,UAAA,QAAA,EAAA,KAAA,gBAAA,CAAA,KAAA,CAAA,QADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA;AACA,KA5WA;AA8WA;AACA,IAAA,WA/WA,uBA+WA,IA/WA,EA+WA;AACA,WAAA,UAAA,CAAA,IAAA,GAAA,IAAA;AACA,WAAA,gBAAA,CAAA,KAAA,CAAA,OAAA,GAAA,CAAA,CAFA,CAEA;;AACA,WAAA,OAAA,GAHA,CAIA;;AACA,WAAA,cAAA,CAAA,IAAA,EALA,CAMA;;AACA,WAAA,cAAA,CAAA;AACA,QAAA,QAAA,EAAA,KAAA,gBAAA,CAAA,KAAA,CAAA,QADA;AAEA,QAAA,OAAA,EAAA;AAFA,OAAA;AAIA,KA1XA;AA2XA,IAAA,UA3XA,sBA2XA,GA3XA,EA2XA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,QAAA,mCAAA,GAAA;AACA,gBAAA,MAAA,CAAA,UAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,QAAA,GAAA,IAAA,CAHA,CAIA;AACA;AACA;AACA;AACA;;AARA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,KApYA;AAqYA,IAAA,UArYA,sBAqYA,GArYA,EAqYA;AAAA;;AACA,WAAA,QAAA,CAAA,iBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAKA,IALA,CAKA,YAAA;AACA,kCAAA,CAAA,GAAA,CAAA,KAAA,CAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,OAAA,EAAA,MADA;AAEA,cAAA,IAAA,EAAA;AAFA,aAAA;AAIA,WALA,MAKA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;;AACA,UAAA,MAAA,CAAA,OAAA;AACA,SAVA;AAWA,OAjBA,EAkBA,KAlBA,CAkBA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OAvBA;AAwBA,KA9ZA;AA+ZA,IAAA,WA/ZA,uBA+ZA,GA/ZA,EA+ZA;AACA,WAAA,IAAA,GAAA,IAAA,KAAA,UAAA,EAAA;AACA,YAAA,GAAA,KAAA,GAAA,CAAA,IAAA,EAAA;AACA,eAAA,UAAA,GAAA,GAAA,CAAA,IAAA;AACA,eAAA,MAAA,CAAA,QAAA,GAAA,KAAA,UAAA,CAAA,GAAA,CAAA,CAFA,CAGA;;AACA,eAAA,gBAAA,CAAA,KAAA,CAAA,OAAA,GAAA,CAAA;AACA,eAAA,OAAA,GALA,CAMA;;AACA,eAAA,gBAAA;AACA;AACA;AACA,KA3aA;AA4aA,IAAA,SA5aA,uBA4aA;AACA,WAAA,QAAA,GAAA,EAAA;AACA,KA9aA;AA+aA,IAAA,MA/aA,kBA+aA,GA/aA,EA+aA;AACA;AACA,WAAA,eAAA;;AAEA,UAAA,GAAA,CAAA,SAAA,KAAA,SAAA,IAAA,KAAA,EAAA;AACA;AACA,YAAA,QAAA,GAAA,KAAA,MAAA,CAAA,OAAA,CAAA,QAAA;;AACA,YAAA,QAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AAAA,oEACA,QADA;AAAA;;AAAA;AACA,mEAAA;AAAA,kBAAA,OAAA;;AACA,kBAAA,OAAA,CAAA,IAAA,KAAA,OAAA,EAAA;AACA,qBAAA,OAAA,CAAA,IAAA,CAAA;AACA,kBAAA,IAAA,EAAA,OAAA,CAAA,IADA;AAEA,kBAAA,KAAA,EAAA;AAAA,oBAAA,KAAA,EAAA,GAAA,CAAA,UAAA;AAAA,oBAAA,MAAA,EAAA,GAAA,CAAA;AAAA;AAFA,iBAAA;AAIA;AACA;AACA;AATA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA,OAdA,MAcA;AACA,aAAA,OAAA,CAAA,IAAA,CAAA;AACA,UAAA,IAAA,EAAA,GAAA,CAAA,SADA;AAEA,UAAA,KAAA,EAAA;AAAA,YAAA,KAAA,EAAA,GAAA,CAAA,UAAA;AAAA,YAAA,MAAA,EAAA,GAAA,CAAA;AAAA;AAFA,SAAA;AAIA;AACA,KAvcA;AAwcA,IAAA,YAxcA,wBAwcA,GAxcA,EAwcA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,UAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,oBAAA,GAAA,GAAA,CAAA,SAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,YAAA,GAAA,GAAA,CAAA,MAAA;AAHA;AAAA,uBAIA,8BAAA,MAAA,CAAA,WAAA,CAJA;;AAAA;AAAA;AAIA,gBAAA,IAJA,sBAIA,IAJA;AAIA,gBAAA,IAJA,sBAIA,IAJA;AAKA,gBAAA,MAAA,CAAA,QAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,YAAA,GAAA,IAAA;;AANA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,KA/cA;AAgdA;AACA,IAAA,aAjdA,2BAidA;AACA,WAAA,YAAA,GAAA,KAAA;AACA,KAndA;AAodA;AACA,IAAA,gBArdA,4BAqdA,GArdA,EAqdA;AACA,WAAA,gBAAA,CAAA,KAAA,CAAA,QAAA,GAAA,GAAA;AACA,WAAA,gBAAA,CAAA,KAAA,CAAA,OAAA,GAAA,CAAA,CAFA,CAEA;;AACA,WAAA,OAAA,GAHA,CAIA;;AACA,WAAA,cAAA,CAAA;AACA,QAAA,QAAA,EAAA,GADA;AAEA,QAAA,OAAA,EAAA;AAFA,OAAA;AAIA,KA9dA;AA+dA,IAAA,mBA/dA,+BA+dA,GA/dA,EA+dA;AACA,WAAA,gBAAA,CAAA,KAAA,CAAA,OAAA,GAAA,GAAA;AACA,WAAA,OAAA,GAFA,CAGA;;AACA,WAAA,cAAA,CAAA;AACA,QAAA,QAAA,EAAA,KAAA,gBAAA,CAAA,KAAA,CAAA,QADA;AAEA,QAAA,OAAA,EAAA;AAFA,OAAA;AAIA,KAveA;AAweA;AACA,IAAA,YAzeA,wBAyeA,IAzeA,EAyeA;AACA,WAAA,UAAA,CAAA,IAAA,GAAA,IAAA;AACA,WAAA,gBAAA,CAAA,KAAA,CAAA,OAAA,GAAA,CAAA,CAFA,CAEA;;AACA,WAAA,OAAA,CAAA,IAAA,EAHA,CAIA;;AACA,WAAA,cAAA,CAAA,IAAA,EALA,CAMA;;AACA,WAAA,cAAA,CAAA;AACA,QAAA,QAAA,EAAA,KAAA,gBAAA,CAAA,KAAA,CAAA,QADA;AAEA,QAAA,OAAA,EAAA;AAFA,OAAA;AAIA;AApfA;AAnIA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-white>\n      <el-tabs v-model=\"activeName\" type=\"card\" @tab-click=\"handleClick\">\n        <el-tab-pane name=\"db\">\n          <span slot=\"label\"><i class=\"el-icon-s-order\"></i>待办</span>\n        </el-tab-pane>\n        <el-tab-pane name=\"yb\">\n          <span slot=\"label\"><i class=\"el-icon-s-claim\"></i>已办</span>\n        </el-tab-pane>\n      </el-tabs>\n      <el-filter\n        ref=\"filter1\"\n        :data=\"filterInfo.data\"\n        :field-list=\"filterInfo.fieldList\"\n        :width=\"{ labelWidth: 180, itemWidth: 200 }\"\n        @handleReset=\"filterReset\"\n        @handleEvent=\"handleFilterEvent\"\n        :btnHidden=\"false\"\n      ></el-filter>\n\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"selectChange\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"handleCurrentChange\"\n        height=\"75vh\"\n      >\n        <el-table-column\n          slot=\"table_start\"\n          align=\"center\"\n          style=\"display: block;word-break : normal;\"\n          label=\"事项标题\"\n          min-width=\"200\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-badge\n              :value=\"\n                scope.row.isHandle == 0\n                  ? scope.row.itemContent &&\n                    scope.row.itemContent.includes('退回')\n                    ? '被退回'\n                    : '待办理'\n                  : '已办理'\n              \"\n              class=\"item\"\n              :type=\"\n                scope.row.isHandle == 0\n                  ? scope.row.itemContent &&\n                    scope.row.itemContent.includes('退回')\n                    ? 'warning'\n                    : 'danger'\n                  : 'primary'\n              \"\n            >\n            </el-badge>\n            <el-button type=\"text\" size=\"small\" @click=\"goPage(scope.row)\">{{\n              scope.row.itemName\n            }}</el-button>\n          </template>\n        </el-table-column>\n        <el-table-column\n          slot=\"table_eight\"\n          align=\"center\"\n          fixed=\"right\"\n          style=\"display: block\"\n          label=\"操作\"\n          min-width=\"100\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              title=\"跳转\"\n              class=\"el-icon-discover\"\n              @click=\"goPage(scope.row)\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              title=\"流程查看\"\n              class=\"el-icon-lcck commonIcon\"\n              @click=\"showTimeLine(scope.row)\"\n              v-if=\"scope.row.moduleKey !== 'probfbk'\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              @click=\"deleteTodo(scope.row)\"\n              title=\"删除\"\n              class=\"el-icon-delete\"\n            >\n            </el-button>\n          </template>\n        </el-table-column>\n      </comp-table>\n    </el-white>\n\n    <el-dialog\n      title=\"待办详情\"\n      :visible.sync=\"openInfo\"\n      width=\"50%\"\n      append-to-body\n      :close-on-click-modal=\"false\"\n      v-dialogDrag\n    >\n      <el-form ref=\"formInfo\" :model=\"formInfo\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"标题：\" prop=\"itemName\">\n              <el-input v-model=\"formInfo.itemName\" :disabled=\"isDisabled\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"内容：\" prop=\"itemContent\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"3\"\n                v-model=\"formInfo.itemContent\"\n                :disabled=\"isDisabled\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"模块名称：\" prop=\"module\">\n              <el-input v-model=\"formInfo.module\" :disabled=\"isDisabled\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"通知时间：\" prop=\"todoTime\">\n              <el-date-picker\n                v-model=\"formInfo.todoTime\"\n                type=\"datetime\"\n                :disabled=\"isDisabled\"\n                format=\"yyyy-MM-dd HH:mm:ss\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n    </el-dialog>\n    <time-line\n      :value=\"timeLineShow\"\n      :timeData=\"timeData\"\n      @closeTimeLine=\"colseTimeLine\"\n    />\n  </div>\n</template>\n\n<script>\nimport timeLine from \"com/timeLine\";\nimport { HistoryList } from \"@/api/activiti/processTask\";\nimport {\n  list,\n  listByPage,\n  insertOrUpdateTodoItem,\n  delByIds\n} from \"@/api/activiti/DgTodoItem\";\nimport { mapState, mapMutations, mapActions } from 'vuex'\n\nexport default {\n  components: { timeLine },\n  name: \"processTodo\",\n  data() {\n    return {\n      filterInfo: {\n        data: {\n          module: \"\",\n          todoUserName: \"\",\n          handleUserName: \"\",\n          applyUserName: \"\"\n        },\n        fieldList: [\n          { label: \"待办来源\", type: \"input\", value: \"module\" },\n          { label: \"待办人\", type: \"input\", value: \"todoUserName\" },\n          { label: \"处理人\", type: \"input\", value: \"handleUserName\" },\n          { label: \"发起人\", type: \"input\", value: \"applyUserName\" }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"module\", label: \"待办来源\", minWidth: \"120\" },\n          { prop: \"taskName\", label: \"任务名称\", minWidth: \"140\" },\n          { prop: \"isHandleCn\", label: \"是否已办\", minWidth: \"80\" },\n          { prop: \"todoUserName\", label: \"待办人名称\", minWidth: \"100\" },\n          { prop: \"handleUserName\", label: \"处理人名称\", minWidth: \"100\" },\n          { prop: \"applyUserName\", label: \"发起人名称\", minWidth: \"100\" },\n          { prop: \"todoTime\", label: \"通知时间\", minWidth: \"150\" },\n          { prop: \"handleTime\", label: \"处理时间\", minWidth: \"150\" }\n        ],\n        option: { checkBox: false, serialNumber: true }\n      },\n      params: {\n        isHandle: 0\n      },\n      activeName: \"db\",\n      selectRows: [],\n      tabRefresh: {\n        db: 0,\n        yb: 1\n      },\n      openInfo: false,\n      formInfo: {},\n      isDisabled: false,\n      timeData: [],\n      timeLineShow: false,\n      processData: {\n        processDefinitionKey: \"\",\n        businessKey: \"\",\n        businessType: \"\",\n        variables: {},\n        nextUser: \"\",\n        processType: \"complete\"\n      }\n    };\n  },\n  // watch: {\n  //   //触发计算函数时执行\n  //   qxjlObjIdChange(val) {\n  //     this.getData({ objId: val });\n  //   }\n  // },\n  //计算函数\n  computed: {\n    ...mapState('todoList', {\n      savedActiveName: 'activeName',\n      savedFilterInfo: 'filterInfo',\n      savedPagination: 'pagination',\n      savedScrollPosition: 'scrollPosition',\n      savedTableData: 'tableData',\n      savedTotal: 'total'\n    })\n  },\n  created() {\n    // 从 store 恢复状态\n    this.restorePageState()\n  },\n  mounted() {\n    // 恢复滚动位置\n    this.$nextTick(() => {\n      this.restoreScrollPosition()\n      // 添加滚动监听\n      this.addScrollListener()\n    })\n  },\n\n  // keep-alive组件激活时调用\n  activated() {\n    console.log('组件激活，刷新数据')\n    // 每次激活时刷新数据，确保显示最新状态\n    this.getData(this.$route.query)\n\n    // 恢复滚动位置\n    this.$nextTick(() => {\n      // 使用setTimeout确保页面完全渲染\n      setTimeout(() => {\n        this.restoreScrollPosition()\n        // 确保滚动监听存在\n        this.addScrollListener()\n      }, 200)\n    })\n  },\n\n  // keep-alive组件失活时调用\n  deactivated() {\n    console.log('组件失活，保存状态')\n    // 先保存当前状态（使用当前已知的滚动位置，不重新获取）\n    this.saveStateWithCurrentScrollPosition()\n    // 移除滚动监听\n    this.removeScrollListener()\n  },\n\n  // 组件销毁前\n  beforeDestroy() {\n    // 移除滚动监听\n    this.removeScrollListener()\n  },\n  beforeRouteLeave(to, from, next) {\n    // 在路由离开前保存页面状态\n    // 注意：不要在这里调用saveCurrentState，因为此时表格可能已经被重置\n    // 滚动位置应该已经通过滚动监听实时保存了\n    console.log('路由离开，当前保存的滚动位置:', this.savedScrollPosition)\n    next()\n  },\n  methods: {\n    ...mapActions('todoList', ['savePageState', 'saveFilterInfo', 'savePagination', 'saveScrollPosition', 'saveTableData', 'markNeedRefresh', 'clearRefreshFlag']),\n\n    // 保存当前状态\n    saveCurrentState() {\n      // 使用保存的滚动容器引用，或者重新查找\n      let scrollPosition = 0\n      if (this._scrollContainer) {\n        scrollPosition = this._scrollContainer.scrollTop\n      } else {\n        // 重新查找滚动容器\n        const tableWrapper = document.querySelector('.el-table__body-wrapper') ||\n                           document.querySelector('.wrap .el-table__body-wrapper')\n        if (tableWrapper) {\n          scrollPosition = tableWrapper.scrollTop\n        }\n      }\n\n      console.log('保存滚动位置:', scrollPosition)\n\n      this.savePageState({\n        activeName: this.activeName,\n        filterInfo: this.filterInfo.data,\n        pagination: {\n          pageSize: this.tableAndPageInfo.pager.pageSize,\n          pageNum: this.tableAndPageInfo.pager.pageNum\n        },\n        scrollPosition: scrollPosition,\n        tableData: this.tableAndPageInfo.tableData,\n        total: this.tableAndPageInfo.pager.total\n      })\n    },\n\n    // 使用当前已保存的滚动位置保存状态（避免重新获取可能为0的值）\n    saveStateWithCurrentScrollPosition() {\n      console.log('使用当前已保存的滚动位置:', this.savedScrollPosition)\n\n      this.savePageState({\n        activeName: this.activeName,\n        filterInfo: this.filterInfo.data,\n        pagination: {\n          pageSize: this.tableAndPageInfo.pager.pageSize,\n          pageNum: this.tableAndPageInfo.pager.pageNum\n        },\n        scrollPosition: this.savedScrollPosition, // 使用已保存的值\n        tableData: this.tableAndPageInfo.tableData,\n        total: this.tableAndPageInfo.pager.total\n      })\n    },\n\n    // 恢复页面状态的方法\n    restorePageState() {\n      // 恢复标签页\n      if (this.savedActiveName && this.savedActiveName !== this.activeName) {\n        this.activeName = this.savedActiveName\n        this.params.isHandle = this.tabRefresh[this.savedActiveName]\n      }\n\n      // 恢复筛选条件\n      if (this.savedFilterInfo && Object.keys(this.savedFilterInfo).length > 0) {\n        this.filterInfo.data = { ...this.savedFilterInfo }\n      }\n\n      // 恢复分页信息\n      if (this.savedPagination) {\n        this.tableAndPageInfo.pager.pageSize = this.savedPagination.pageSize\n        this.tableAndPageInfo.pager.pageNum = this.savedPagination.pageNum\n      }\n\n      // 直接获取最新数据，不使用缓存数据避免显示已办理的待办\n      // 缓存数据仅用于滚动位置恢复，不用于数据显示\n      this.getData(this.$route.query);\n    },\n\n    // 恢复滚动位置\n    restoreScrollPosition() {\n      if (this.savedScrollPosition > 0) {\n        this.$nextTick(() => {\n          // 尝试多种可能的滚动容器选择器\n          let scrollContainer = null\n\n          // 1. 尝试标准的el-table滚动容器\n          scrollContainer = document.querySelector('.el-table__body-wrapper')\n\n          // 2. 如果没找到，尝试comp-table的包装器\n          if (!scrollContainer) {\n            scrollContainer = document.querySelector('.wrap .el-table__body-wrapper')\n          }\n\n          // 3. 如果还没找到，尝试通过comp-table组件查找\n          if (!scrollContainer) {\n            const compTableEl = this.$el.querySelector('.wrap')\n            if (compTableEl) {\n              scrollContainer = compTableEl.querySelector('.el-table__body-wrapper')\n            }\n          }\n\n          console.log('恢复滚动位置:', this.savedScrollPosition, '容器:', scrollContainer)\n\n          if (scrollContainer) {\n            scrollContainer.scrollTop = this.savedScrollPosition\n\n            // 验证设置是否成功\n            setTimeout(() => {\n              console.log('滚动位置恢复验证:', scrollContainer.scrollTop)\n            }, 100)\n          } else {\n            console.warn('未找到表格滚动容器')\n          }\n        })\n      }\n    },\n\n    // 添加滚动监听\n    addScrollListener() {\n      this.$nextTick(() => {\n        // 如果已经有监听器，先移除\n        if (this._scrollHandler) {\n          this.removeScrollListener()\n        }\n\n        // 使用和恢复滚动位置相同的逻辑查找滚动容器\n        let scrollContainer = null\n\n        // 1. 尝试标准的el-table滚动容器\n        scrollContainer = document.querySelector('.el-table__body-wrapper')\n\n        // 2. 如果没找到，尝试comp-table的包装器\n        if (!scrollContainer) {\n          scrollContainer = document.querySelector('.wrap .el-table__body-wrapper')\n        }\n\n        // 3. 如果还没找到，尝试通过comp-table组件查找\n        if (!scrollContainer) {\n          const compTableEl = this.$el.querySelector('.wrap')\n          if (compTableEl) {\n            scrollContainer = compTableEl.querySelector('.el-table__body-wrapper')\n          }\n        }\n\n        // 4. 最后尝试通过高度属性查找\n        if (!scrollContainer) {\n          const tables = document.querySelectorAll('.el-table__body-wrapper')\n          for (let table of tables) {\n            const tableEl = table.closest('.el-table')\n            if (tableEl && tableEl.style.height === '75vh') {\n              scrollContainer = table\n              break\n            }\n          }\n        }\n\n        console.log('添加滚动监听的容器:', scrollContainer)\n\n        if (scrollContainer) {\n          this._scrollContainer = scrollContainer // 保存引用\n          this._scrollHandler = this.throttle(() => {\n            const currentScrollTop = scrollContainer.scrollTop\n            console.log('滚动位置变化:', currentScrollTop)\n            // 直接更新store中的滚动位置\n            this.saveScrollPosition(currentScrollTop)\n          }, 300)\n          scrollContainer.addEventListener('scroll', this._scrollHandler)\n        } else {\n          console.warn('未找到表格滚动容器，无法添加滚动监听')\n        }\n      })\n    },\n\n    // 移除滚动监听\n    removeScrollListener() {\n      if (this._scrollContainer && this._scrollHandler) {\n        this._scrollContainer.removeEventListener('scroll', this._scrollHandler)\n        this._scrollHandler = null\n        this._scrollContainer = null\n        console.log('滚动监听已移除')\n      }\n    },\n\n    // 节流函数\n    throttle(func, delay) {\n      let timeoutId\n      let lastExecTime = 0\n      return function (...args) {\n        const currentTime = Date.now()\n\n        if (currentTime - lastExecTime > delay) {\n          func.apply(this, args)\n          lastExecTime = currentTime\n        } else {\n          clearTimeout(timeoutId)\n          timeoutId = setTimeout(() => {\n            func.apply(this, args)\n            lastExecTime = Date.now()\n          }, delay - (currentTime - lastExecTime))\n        }\n      }\n    },\n\n    // 检查并修正页码\n    checkAndCorrectPageNum(records, total) {\n      const currentPage = this.tableAndPageInfo.pager.pageNum\n      const pageSize = this.tableAndPageInfo.pager.pageSize\n      const maxPage = Math.ceil(total / pageSize) || 1\n\n      console.log('页码检查:', {\n        currentPage,\n        maxPage,\n        total,\n        recordsLength: records.length,\n        hasData: records.length > 0\n      })\n\n      // 情况1：当前页超出了最大页数\n      if (currentPage > maxPage) {\n        console.log(`当前页${currentPage}超出最大页${maxPage}，修正到第${maxPage}页`)\n        this.tableAndPageInfo.pager.pageNum = maxPage\n        // 更新store中的分页信息，并清除滚动位置\n        this.savePagination({\n          pageSize: pageSize,\n          pageNum: maxPage\n        })\n        this.saveScrollPosition(0) // 清除滚动位置\n        return true // 需要重新请求\n      }\n\n      // 情况2：当前页没有数据，但总数大于0（说明数据在其他页）\n      if (records.length === 0 && total > 0 && currentPage > 1) {\n        console.log(`当前页${currentPage}无数据但总数${total}>0，修正到第${maxPage}页`)\n        this.tableAndPageInfo.pager.pageNum = maxPage\n        // 更新store中的分页信息，并清除滚动位置\n        this.savePagination({\n          pageSize: pageSize,\n          pageNum: maxPage\n        })\n        this.saveScrollPosition(0) // 清除滚动位置\n        return true // 需要重新请求\n      }\n\n      // 情况3：总数为0，确保在第1页\n      if (total === 0 && currentPage !== 1) {\n        console.log('总数为0，修正到第1页')\n        this.tableAndPageInfo.pager.pageNum = 1\n        // 更新store中的分页信息，并清除滚动位置\n        this.savePagination({\n          pageSize: pageSize,\n          pageNum: 1\n        })\n        this.saveScrollPosition(0) // 清除滚动位置\n        return true // 需要重新请求\n      }\n\n      return false // 不需要修正\n    },\n\n    // 调试方法：查看页面中所有可能的滚动容器\n    debugScrollContainers() {\n      console.log('=== 调试滚动容器 ===')\n\n      // 查找所有可能的滚动容器\n      const containers = [\n        { name: 'app-container', el: document.querySelector('.app-container') },\n        { name: 'el-table__body-wrapper', el: document.querySelector('.el-table__body-wrapper') },\n        { name: 'wrap .el-table__body-wrapper', el: document.querySelector('.wrap .el-table__body-wrapper') },\n        { name: 'comp-table内的wrapper', el: this.$el.querySelector('.wrap .el-table__body-wrapper') }\n      ]\n\n      containers.forEach(container => {\n        if (container.el) {\n          console.log(`${container.name}:`, {\n            element: container.el,\n            scrollTop: container.el.scrollTop,\n            scrollHeight: container.el.scrollHeight,\n            clientHeight: container.el.clientHeight,\n            hasScroll: container.el.scrollHeight > container.el.clientHeight\n          })\n        } else {\n          console.log(`${container.name}: 未找到`)\n        }\n      })\n\n      // 查找所有el-table__body-wrapper\n      const allWrappers = document.querySelectorAll('.el-table__body-wrapper')\n      console.log('所有el-table__body-wrapper:', allWrappers)\n      allWrappers.forEach((wrapper, index) => {\n        console.log(`wrapper ${index}:`, {\n          element: wrapper,\n          scrollTop: wrapper.scrollTop,\n          scrollHeight: wrapper.scrollHeight,\n          clientHeight: wrapper.clientHeight,\n          hasScroll: wrapper.scrollHeight > wrapper.clientHeight,\n          parentTable: wrapper.closest('.el-table')\n        })\n      })\n\n      console.log('当前保存的滚动位置:', this.savedScrollPosition)\n      console.log('=== 调试结束 ===')\n    },\n    \n    async getData(param) {\n      // 合并路由参数和筛选条件\n      this.params = { ...this.params, ...param, ...this.filterInfo.data }\n\n      if (!this.params.todoUserId) {\n        this.params.todoUserId = this.$store.getters.name\n      }\n\n      let { code, data } = await listByPage({\n        ...this.params,\n        pageNum: this.tableAndPageInfo.pager.pageNum,\n        pageSize: this.tableAndPageInfo.pager.pageSize\n      })\n\n      if (code === \"0000\") {\n        // 检查页码是否需要修正\n        const needPageCorrection = this.checkAndCorrectPageNum(data.records, data.total)\n\n        if (needPageCorrection) {\n          // 页码需要修正，重新请求数据\n          console.log('页码需要修正，重新请求数据')\n          return this.getData(param)\n        }\n\n        this.tableAndPageInfo.tableData = data.records\n        this.tableAndPageInfo.pager.total = data.total\n\n        // 保存表格数据到store\n        this.saveTableData({\n          tableData: data.records,\n          total: data.total\n        })\n\n        // 清除刷新标记，表示数据已是最新\n        this.clearRefreshFlag()\n\n        // 数据加载完成后，延迟恢复滚动位置和添加滚动监听\n        this.$nextTick(() => {\n          // 使用setTimeout确保表格完全渲染\n          setTimeout(() => {\n            this.restoreScrollPosition()\n            this.addScrollListener()\n          }, 100)\n        })\n      }\n    },\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n\n    // 处理筛选条件变化事件\n    handleFilterEvent(obj, data) {\n      // 实时保存筛选条件\n      this.saveFilterInfo(data)\n      // 如果是回车或者change事件，触发搜索\n      if (obj.value !== undefined) {\n        this.filterInfo.data = data\n        this.tableAndPageInfo.pager.pageNum = 1 // 重置到第一页\n        this.getData()\n        // 保存分页状态\n        this.savePagination({\n          pageSize: this.tableAndPageInfo.pager.pageSize,\n          pageNum: 1\n        })\n      }\n    },\n\n    // 重置筛选条件\n    filterReset(data) {\n      this.filterInfo.data = data\n      this.tableAndPageInfo.pager.pageNum = 1 // 重置到第一页\n      this.getData()\n      // 保存重置后的筛选条件\n      this.saveFilterInfo(data)\n      // 保存分页状态\n      this.savePagination({\n        pageSize: this.tableAndPageInfo.pager.pageSize,\n        pageNum: 1\n      })\n    },\n    async getDetails(row) {\n      this.formInfo = { ...row };\n      this.isDisabled = true;\n      this.openInfo = true;\n      //如果是未查看状态，点击查看时变成已查看\n      // if(row.isView==0){\n      //   await insertOrUpdateTodoItem({objId:row.objId,isView:'1'})\n      //   this.getData()\n      // }\n    },\n    deleteTodo(row) {\n      this.$confirm(\"确认删除选中数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          delByIds([row.objId]).then(res => {\n            if (res.code === \"0000\") {\n              this.$message({\n                message: \"删除成功\",\n                type: \"success\"\n              });\n            } else {\n              this.$message.error(\"操作失败\");\n            }\n            this.getData();\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    handleClick(tab) {\n      for (let key in this.tabRefresh) {\n        if (key === tab.name) {\n          this.activeName = tab.name\n          this.params.isHandle = this.tabRefresh[key]\n          // 切换标签页时重置分页到第一页\n          this.tableAndPageInfo.pager.pageNum = 1\n          this.getData()\n          // 保存当前状态\n          this.saveCurrentState()\n        }\n      }\n    },\n    closeForm() {\n      this.formInfo = {};\n    },\n    goPage(row) {\n      // 标记需要刷新数据，因为用户可能会办理待办\n      this.markNeedRefresh()\n\n      if (row.moduleKey === \"gzplccs\" && false) {\n        //解决工作票跳转404的问题,疑似舍弃了\n        const topMenus = this.$store.getters.topMenus;\n        if (topMenus.length > 0) {\n          for (const topMenu of topMenus) {\n            if (topMenu.name === \"工作票管理\") {\n              this.$router.push({\n                path: topMenu.path,\n                query: { objId: row.businessId, module: row.moduleKey }\n              });\n              break;\n            }\n          }\n        }\n      } else {\n        this.$router.push({\n          path: row.routePath,\n          query: { objId: row.businessId, module: row.moduleKey }\n        });\n      }\n    },\n    async showTimeLine(row) {\n      this.processData.businessKey = row.businessId;\n      this.processData.processDefinitionKey = row.moduleKey;\n      this.processData.businessType = row.module;\n      let { code, data } = await HistoryList(this.processData);\n      this.timeData = data;\n      this.timeLineShow = true;\n    },\n    //关闭流程查看页面\n    colseTimeLine() {\n      this.timeLineShow = false;\n    },\n    // 监听分页变化\n    handleSizeChange(val) {\n      this.tableAndPageInfo.pager.pageSize = val\n      this.tableAndPageInfo.pager.pageNum = 1 // 重置到第一页\n      this.getData()\n      // 保存分页状态\n      this.savePagination({\n        pageSize: val,\n        pageNum: 1\n      })\n    },\n    handleCurrentChange(val) {\n      this.tableAndPageInfo.pager.pageNum = val\n      this.getData()\n      // 保存分页状态\n      this.savePagination({\n        pageSize: this.tableAndPageInfo.pager.pageSize,\n        pageNum: val\n      })\n    },\n    // 监听筛选条件变化\n    handleFilter(data) {\n      this.filterInfo.data = data\n      this.tableAndPageInfo.pager.pageNum = 1 // 重置到第一页\n      this.getData(data)\n      // 保存筛选条件\n      this.saveFilterInfo(data)\n      // 保存分页状态\n      this.savePagination({\n        pageSize: this.tableAndPageInfo.pager.pageSize,\n        pageNum: 1\n      })\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.item {\n  width: 8.5rem;\n  height: 1.25rem;\n}\n</style>\n"], "sourceRoot": "src/views/activiti/dgTodoItem"}]}