{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\todoitem\\history.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\todoitem\\history.vue", "mtime": 1706897322086}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["history.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "history.vue", "sourceRoot": "src/views/activiti/todoitem", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form class=\"searchForm1\" :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" label-width=\"100px\">\n          <el-form-item label=\"节点名称：\" prop=\"activityName\">\n            <el-input v-model=\"queryParams.activityName\" placeholder=\"请输入节点名称\" clearable\n                      @keyup.enter.native=\"handleQuery\"/>\n          </el-form-item>\n          <el-form-item label=\"办理人ID：\" prop=\"assignee\">\n            <el-input v-model=\"queryParams.assignee\" placeholder=\"请输入办理人ID\" clearable\n                      @keyup.enter.native=\"handleQuery\"/>\n          </el-form-item>\n        <el-form-item>\n          <el-button type=\"cyan\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\n          <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\" type=\"warning\">重置</el-button>\n        </el-form-item>\n    </el-form>\n    <el-row :gutter=\"20\">\n      <el-col :span=\"24\">\n        <el-white class=\"button-group\">\n          <el-table style=\"margin-top: 15px;\" stripe border v-loading=\"loading\" :data=\"historyList\" height=\"400\" @selection-change=\"handleSelectionChange\">\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n            <el-table-column label=\"节点名称\" align=\"center\" prop=\"activityName\" fixed  />\n            <el-table-column label=\"处理人\" align=\"center\" prop=\"assigneeName\" />\n            <el-table-column label=\"审批意见\" align=\"center\" prop=\"comment\" width=\"160\"/>\n            <el-table-column label=\"提报时间\" align=\"center\" prop=\"startTime\" width=\"160\" />\n            <el-table-column label=\"审批时间\" align=\"center\" prop=\"endTime\" width=\"160\"/>\n          </el-table>\n        </el-white>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\n  import { historyList } from \"@/api/activiti/process\";\n  export default {\n    name: \"ActivitiHistory\",\n    props:{\n      instanceId:{\n        type: String,\n        default: null\n      }\n    },\n    data() {\n      return {\n        ins:this.instanceId,\n        // 遮罩层\n        loading: true,\n        // 选中数组\n        ids: [],\n        // 非单个禁用\n        single: true,\n        // 非多个禁用\n        multiple: true,\n        // 显示搜索条件\n        showSearch: true,\n        dateRange: [],\n        dateRange1:[],\n        // 总条数\n        total: 0,\n        // 用户表格数据\n        historyList: null,\n        // 是否显示弹出层\n        // 部门名称\n        // 表单参数\n        form: {},\n        // 查询参数\n        queryParams: {\n          pageNum: 1,\n          pageSize: 10,\n          activityName: undefined,\n          assignee: undefined,\n        },\n      };\n    },\n    watch: {\n      //监听父组件传值\n      instanceId(val) {\n        this.ins = val;\n        this.getList();\n      },\n    },\n\n    created() {\n      this.getList();\n    },\n    methods: {\n      /** 查询用户列表 */\n      getList() {\n        this.loading = true;\n        this.queryParams.instanceId = this.ins;\n        historyList(this.queryParams).then(\n          (response) => {\n            this.historyList = response.data;\n            this.total = response.data.length;\n            this.loading = false;\n          }\n        );\n      },\n      /**\n       * 取消按钮\n       * */\n      cancel() {\n        this.open = false;\n        this.reset();\n      },\n      /**\n       * 表单重置\n       * */\n      reset() {\n        this.form = {\n          pass: undefined,\n          name: undefined,\n          description:undefined\n        };\n        this.resetForm(\"form\");\n      },\n      /** 搜索按钮操作 */\n      handleQuery() {\n        this.queryParams.pageNum = 1;\n        this.getList();\n      },\n      /** 重置按钮操作 */\n      resetQuery() {\n        this.dateRange = [];\n        this.resetForm(\"queryForm\");\n        this.handleQuery();\n      },\n      /***多选框选中数据***/\n      handleSelectionChange(selection) {\n        this.ids = selection.map((item) => item.userId);\n        this.single = selection.length != 1;\n        this.multiple = !selection.length;\n      },\n    },\n  };\n</script>\n"]}]}