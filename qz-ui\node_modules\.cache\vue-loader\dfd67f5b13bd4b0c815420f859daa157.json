{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sympk\\sympk.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sympk\\sympk.vue", "mtime": 1706897323740}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCgppbXBvcnQgewogIGdldERldmljZUNsYXNzVHJlZU5vZGVCeVBpZCwKICBnZXRQYWdlRGF0YUxpc3QsCiAgZ2V0VGFibGUsCiAgcmVtb3ZlLAogIHNhdmVPclVwZGF0ZSwKICB1cGRhdGVUYWJsZU51bQp9IGZyb20gJ0AvYXBpL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvc3ltcGsvc3ltcGsnCmltcG9ydCBTeW1wSW5mbyBmcm9tICdAL3ZpZXdzL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvc3ltcGsvc3ltcEluZm8nCmltcG9ydCBNcHhxSW5mbyBmcm9tICdAL3ZpZXdzL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvc3ltcGsvbXB4cUluZm8nCgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ3N5bXBrJywKICBjb21wb25lbnRzOiB7IE1weHFJbmZvLCBTeW1wSW5mbyB9LAogIGluamVjdDogWydyZWxvYWQnXSwvL2luamVjdOazqOWFpeaguee7hOS7tueahHJlbG9hZOaWueazlQogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBwYXJhbXM6IHt9LAogICAgICAvL+etm+mAieahhgogICAgICBmaWx0ZXJJbmZvOiB7CiAgICAgICAgZGF0YTogewogICAgICAgICAgbXBtYzogJycsCiAgICAgICAgICB6eTogJycsCiAgICAgICAgICBzZmZ4OiAnJwogICAgICAgIH0sCiAgICAgICAgZmllbGRMaXN0OiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAn6ZOt54mM5ZCN56ewJywKICAgICAgICAgICAgdmFsdWU6ICdtcG1jJywKICAgICAgICAgICAgdHlwZTogJ2lucHV0JywKICAgICAgICAgICAgY2xlYXJhYmxlOiB0cnVlCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogJ+aYr+WQpuWIhuebuCcsCiAgICAgICAgICAgIHZhbHVlOiAnc2ZmeCcsCiAgICAgICAgICAgIHR5cGU6ICdzZWxlY3QnLAogICAgICAgICAgICBvcHRpb25zOiBbCiAgICAgICAgICAgICAge2xhYmVsOiAn5pivJywgdmFsdWU6ICcxJ30sCiAgICAgICAgICAgICAge2xhYmVsOiAn5ZCmJywgdmFsdWU6ICcwJ30KICAgICAgICAgICAgXSwKICAgICAgICAgICAgY2xlYXJhYmxlOiB0cnVlCiAgICAgICAgICB9CiAgICAgICAgXQogICAgICB9LAogICAgICBjdXJyZW50VXNlcjogdGhpcy4kc3RvcmUuZ2V0dGVycy5uYW1lLAogICAgICBpZHM6IFtdLAogICAgICBwcm9wczogewogICAgICAgIGxhYmVsOiAnbmFtZScsCiAgICAgICAgY2hpbGRyZW46ICd6b25lcycsCiAgICAgICAgaXNMZWFmOiAnbGVhZicKICAgICAgfSwKICAgICAgLy/kuJPkuJrkuIvmi4nmoYbmlbDmja4KICAgICAgb3B0aW9uczogW3sgbGFiZWw6ICfovpPnlLUnLCB2YWx1ZTogJ1NEJyB9LCB7IGxhYmVsOiAn5Y+Y55S1JywgdmFsdWU6ICdCRCcgfV0sCiAgICAgIC8v5piv5ZCm5YiG55u45LiL5ouJ5qGG5pWw5o2uCiAgICAgIHNmZnhPcHRpb25zOiBbeyBsYWJlbDogJ+aYrycsIHZhbHVlOiAnMScgfSwgeyBsYWJlbDogJ+WQpicsIHZhbHVlOiAnMCcgfV0sCiAgICAgIGZvcm06IHsKICAgICAgICBvYmpJZDogdW5kZWZpbmVkLAogICAgICAgIHNibHhibTogdW5kZWZpbmVkLAogICAgICAgIG1wbWM6IHVuZGVmaW5lZCwKICAgICAgICB6eTogdW5kZWZpbmVkLAogICAgICAgIHNmZng6ICcnLAogICAgICAgIGlzTXBTeXhtOiAwCiAgICAgIH0sCiAgICAgIGR5Rm9ybTogewogICAgICAgIG9ial9pZDogdW5kZWZpbmVkLAogICAgICAgIGFfaHM6IDAsCiAgICAgICAgYV9oc09sZDogMCwKICAgICAgICBhX2xzOiAwLAogICAgICAgIGFfbHNPbGQ6IDAsCiAgICAgICAgYl9oczogMCwKICAgICAgICBiX2hzT2xkOiAwLAogICAgICAgIGJfbHM6IDAsCiAgICAgICAgYl9sc09sZDogMCwKICAgICAgICByb3dTcGFuTnVtOiAxLAogICAgICAgIGNvbFNwYW5OdW06IDEsCiAgICAgICAgbGJiczogdW5kZWZpbmVkLAogICAgICAgIHRpdGxlOiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgaXNTaG93RGV0YWlsczogZmFsc2UsCiAgICAgIGlzU2hvd0R5RGV0YWlsczogZmFsc2UsCiAgICAgIHRpdGxlOiAnJywKICAgICAgdGFibGVBbmRQYWdlSW5mbzogewogICAgICAgIHBhZ2VyOiB7CiAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgdG90YWw6IDAsCiAgICAgICAgICBzaXplczogWzEwLCAyMCwgNTAsIDEwMF0KICAgICAgICB9LAogICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgdGFibGVIZWFkZXI6IFsKICAgICAgICAgIHsgbGFiZWw6ICfpk63niYzlkI3np7AnLCBwcm9wOiAnbXBtYycsIG1pbldpZHRoOiAnMTgwJyB9LAogICAgICAgICAgeyBsYWJlbDogJ+S4k+S4micsIHByb3A6ICd6eU5hbWUnLCBtaW5XaWR0aDogJzIwMCcgfSwKICAgICAgICAgIHsgbGFiZWw6ICfmmK/lkKbliIbnm7gnLCBwcm9wOiAnc2ZmeE5hbWUnLCBtaW5XaWR0aDogJzIwMCcgfQogICAgICAgICAgLy8gewogICAgICAgICAgLy8gICBwcm9wOiAnb3BlcmF0aW9uJywKICAgICAgICAgIC8vICAgbGFiZWw6ICfmk43kvZwnLAogICAgICAgICAgLy8gICBtaW5XaWR0aDogJzEwMHB4JywKICAgICAgICAgIC8vICAgc3R5bGU6IHsgZGlzcGxheTogJ2Jsb2NrJyB9LAogICAgICAgICAgLy8gICAvL+aTjeS9nOWIl+WbuuWumuWGjeWPs+S+pwogICAgICAgICAgLy8gICBmaXhlZDogJ3JpZ2h0JywKICAgICAgICAgIC8vICAgb3BlcmF0aW9uOiBbCiAgICAgICAgICAvLyAgICAgeyBuYW1lOiAn5L+u5pS5JywgY2xpY2tGdW46IHRoaXMudXBkYXRlRGV0YWlscyB9LAogICAgICAgICAgLy8gICAgIHsgbmFtZTogJ+ivpuaDhScsIGNsaWNrRnVuOiB0aGlzLmdldERldGFpbHMgfSwKICAgICAgICAgIC8vICAgICB7IG5hbWU6ICflrprkuYnor6bmg4UnLCBjbGlja0Z1bjogdGhpcy5nZXROYW1lcGxhdGVJbmZvIH0KICAgICAgICAgIC8vICAgXQogICAgICAgICAgLy8gfQogICAgICAgIF0sCiAgICAgICAgb3B0aW9uOiB7IGNoZWNrQm94OiB0cnVlLCBzZXJpYWxOdW1iZXI6IHRydWUgfQogICAgICB9LAogICAgICAvL+WIoOmZpOaYr+WQpuWPr+eUqAogICAgICBtdWx0aXBsZVNlbnNvcjogdHJ1ZSwKICAgICAgLy/mn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICB6eTogJycsCiAgICAgICAgc2JseGJtOiB1bmRlZmluZWQsCiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgaXNNcFN5eG06IDAKICAgICAgfSwKICAgICAgaXNEaXNhYmxlZDogZmFsc2UsCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgbXBtYzogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfor7floavlhpnpk63niYzlkI3np7AnLCB0cmlnZ2VyOiAnYmx1cicgfV0sCiAgICAgICAgenk6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup5LiT5LiaJywgdHJpZ2dlcjogJ2JsdXInIH1dLAogICAgICAgIHNmZng6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup5piv5ZCm5YiG55u4JywgdHJpZ2dlcjogJ2NoYW5nZScgfV0KICAgICAgfSwKICAgICAgc2VsZWN0aW9uOiBbXSwgLy/orrDlvZXmnIDlkI7kuIDmrKHpgInkuK3nmoTooYzmlbDmja4KICAgICAgdGFibGVEYXRhQTogdW5kZWZpbmVkLCAgLy/ooajmoLxB5pWw5o2uCiAgICAgIHRhYmxlRGF0YUI6IHVuZGVmaW5lZCwgIC8v6KGo5qC8QuaVsOaNrgogICAgICBpc1Nob3dNcEluZm86IGZhbHNlLAogICAgICAvL+mAieS4reihjOaVsOaNrgogICAgICByb3dEYXRhOiB7fSwKICAgICAgLy/orr7lpIfnsbvlnovnvJbnoIEKICAgICAgc2JseGJtOiAnJywKICAgICAgbXhEYXRhOiBbXS8v6KGo5qC85piO57uG5pWw5o2uCiAgICB9CiAgfSwKICB3YXRjaDoge30sCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpCgogIH0sCiAgbWV0aG9kczogewogICAgZ2V0RGF0YShwYXJhbSl7CiAgICAgIHRoaXMuZ2V0TGlzdChwYXJhbSkKICAgIH0sCiAgICBoYW5kbGVFdmVudCh2YXIxLCB2YXIyKXsKICAgICAgdGhpcy5wYXJhbXMgPSB2YXIyCiAgICB9LAogICAgLy/lrprkuYnph43nva7mlrnms5UKICAgIGdldFJlc2V0KCkgewogICAgICB0aGlzLnBhcmFtcyA9IHt9CiAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICB9LAogICAgLy/moJHoioLngrnngrnlh7vkuovku7YKICAgIGhhbmRsZU5vZGVDbGljayhkYXRhKSB7CiAgICAgIHRoaXMuZm9ybS5zYmx4Ym0gPSBkYXRhLmNvZGUKICAgICAgdGhpcy5zYmx4Ym0gPSBkYXRhLmNvZGUKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zYmx4Ym0gPSBkYXRhLmNvZGUKICAgICAgaWYgKGRhdGEucGlkICE9ICdzYicpewogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuenkgPSBkYXRhLnBpZC5zdWJzdHJpbmcoMCwgMikKICAgICAgfQogICAgICB0aGlzLmdldExpc3QoKQogICAgfSwKICAgIC8qKgogICAgICog6KGo5qC85aSa6YCJ5qGGCiAgICAgKi8KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5vYmpJZCkKICAgICAgdGhpcy5zZWxlY3Rpb24gPSBzZWxlY3Rpb24KICAgIH0sCiAgICAvL+a3u+WKoOaMiemSrgogICAgYWRkQnV0dG9uKCkgewogICAgICBpZiAodGhpcy5mb3JtLnNibHhibSAhPSB1bmRlZmluZWQpIHsKICAgICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlCiAgICAgICAgdGhpcy5pc0Rpc2FibGVkID0gZmFsc2UKICAgICAgICB0aGlzLmZvcm0ub2JqSWQgPSB1bmRlZmluZWQKICAgICAgICB0aGlzLmZvcm0ubXBtYyA9IHVuZGVmaW5lZAogICAgICAgIHRoaXMuZm9ybS56eSA9IHVuZGVmaW5lZAogICAgICAgIHRoaXMudGl0bGUgPSAn5paw5aKeJwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup5bem5L6n5qCR6IqC54K55paw5aKe5pWw5o2u77yBJykKICAgICAgICByZXR1cm4KICAgICAgfQogICAgfSwKICAgIGNsb3NlKCkgewogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSBmYWxzZQogICAgfSwKICAgIHVwZGF0ZURldGFpbHMocm93KSB7CiAgICAgIHRoaXMudGl0bGUgPSAn5L+u5pS5JwogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlCiAgICAgIHRoaXMuZm9ybSA9IHJvdwogICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZQogICAgfSwKICAgIGdldERldGFpbHMocm93KSB7CiAgICAgIHRoaXMudGl0bGUgPSAn6K+m5oOFJwogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlCiAgICAgIHRoaXMuZm9ybSA9IHJvdwogICAgICB0aGlzLmlzRGlzYWJsZWQgPSB0cnVlCiAgICB9LAogICAgc2F2ZSgpIHsKICAgICAgdGhpcy4kcmVmcy5mb3JtLnZhbGlkYXRlKCh2YWxpZCkgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgc2F2ZU9yVXBkYXRlKHRoaXMuZm9ybSkudGhlbihyZXMgPT4gewogICAgICAgICAgICBpZiAocmVzLmNvZGUgPT0gJzAwMDAnKSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfkv53lrZjmiJDlip/vvIEnKQogICAgICAgICAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IGZhbHNlCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgICAgICAgfQogICAgICAgICAgfSkKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign6K+36L6T5YWl5omA5pyJ5b+F5aGr5a2X5q6177yBJykKICAgICAgICAgIHJldHVybiBmYWxzZQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICAvL+WIoOmZpOaMiemSrgogICAgZGVsZXRlQnV0dG9uKHJvdykgewogICAgICB0aGlzLmZvcm0gPSByb3cKICAgICAgdGhpcy4kY29uZmlybSgn5q2k5pON5L2c5bCG5rC45LmF5Yig6Zmk6K+l5pWw5o2uLCDmmK/lkKbnu6fnu60/JywgJ+aPkOekuicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgIHJlbW92ZShbdGhpcy5mb3JtLm9iaklkXSkudGhlbihyZXMgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICAgICAgbWVzc2FnZTogJ+WIoOmZpOaIkOWKnyEnCiAgICAgICAgICB9KQogICAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgICB9KQogICAgICB9KQogICAgfSwKICAgIC8v5a6a5LmJ6K+m5oOF5oyJ6ZKuCiAgICB2aWV3QnV0dG9uKCkgewogICAgICBpZiAodGhpcy5pZHMubGVuZ3RoID09IDEpIHsKICAgICAgICB0aGlzLmlzU2hvd0R5RGV0YWlscyA9IHRydWUKICAgICAgICB0aGlzLmR5Rm9ybS5vYmpfaWQgPSB0aGlzLmlkc1swXQogICAgICAgIHRoaXMuZHlGb3JtLmFfaHMgPSB0aGlzLnNlbGVjdGlvblswXS5hX2hzCiAgICAgICAgdGhpcy5keUZvcm0uYV9oc09sZCA9IHRoaXMuc2VsZWN0aW9uWzBdLmFfaHMKICAgICAgICB0aGlzLmR5Rm9ybS5hX2xzID0gdGhpcy5zZWxlY3Rpb25bMF0uYV9scwogICAgICAgIHRoaXMuZHlGb3JtLmFfbHNPbGQgPSB0aGlzLnNlbGVjdGlvblswXS5hX2xzCiAgICAgICAgdGhpcy5keUZvcm0uYl9ocyA9IHRoaXMuc2VsZWN0aW9uWzBdLmJfaHMKICAgICAgICB0aGlzLmR5Rm9ybS5iX2hzT2xkID0gdGhpcy5zZWxlY3Rpb25bMF0uYl9ocwogICAgICAgIHRoaXMuZHlGb3JtLmJfbHMgPSB0aGlzLnNlbGVjdGlvblswXS5iX2xzCiAgICAgICAgdGhpcy5keUZvcm0uYl9sc09sZCA9IHRoaXMuc2VsZWN0aW9uWzBdLmJfbHMKICAgICAgICB0aGlzLmR5Rm9ybS50aXRsZSA9IHRoaXMuc2VsZWN0aW9uWzBdLm1wbWMKICAgICAgICAvL0HooajmoLzliqDovb0KICAgICAgICBpZiAodGhpcy5zZWxlY3Rpb25bMF0uYV9ocyAhPSAnJyAmJiB0aGlzLnNlbGVjdGlvblswXS5hX2xzICE9ICcnICYmIHRoaXMuc2VsZWN0aW9uWzBdLmFfaHMgIT0gMCAmJiB0aGlzLnNlbGVjdGlvblswXS5hX2xzICE9IDApIHsKICAgICAgICAgIHRoaXMuZHlGb3JtLmxiYnMgPSAnQScKICAgICAgICAgIGdldFRhYmxlKHRoaXMuZHlGb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIHZhciBzdHIgPSAnJwogICAgICAgICAgICBmb3IgKHZhciBpID0gMDsgaSA8IHRoaXMuc2VsZWN0aW9uWzBdLmFfaHM7IGkrKykgewogICAgICAgICAgICAgIHZhciB0ZW1wID0gJzx0cj4nCiAgICAgICAgICAgICAgZm9yICh2YXIgayA9IDA7IGsgPCByZXMuZGF0YS5sZW5ndGg7IGsrKykgewogICAgICAgICAgICAgICAgaWYgKGkgPT0gcmVzLmRhdGFba10ucm93aW5kZXgpIHsKICAgICAgICAgICAgICAgICAgdmFyIG5yYnMgPSByZXMuZGF0YVtrXS5ucmJzCiAgICAgICAgICAgICAgICAgIHZhciBucmx4ID0gcmVzLmRhdGFba10ubnJseAogICAgICAgICAgICAgICAgICAvL+WumuS5ieminOiJsgogICAgICAgICAgICAgICAgICB2YXIgY29sb3IgPSAnJwogICAgICAgICAgICAgICAgICBpZiAobnJseCA9PSAnMScgfHwgbnJseCA9PSAnMicgfHwgbnJseCA9PSAnMycgfHwgbnJseCA9PSAnNCcpIHsvL+eZveiJsuWNleWFg+agvDrliqjmgIHlsZ7mgKcKICAgICAgICAgICAgICAgICAgICBjb2xvciA9ICcgYmdjb2xvcj0id2hpdGUiICcKICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmIChucmx4ID09ICc1Jykgey8v54Gw6Imy5Y2V5YWD5qC8OumdmeaAgeaWh+acrAogICAgICAgICAgICAgICAgICAgIGNvbG9yID0gJyBiZ2NvbG9yPSIjRUVFRUUwIiAnCiAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAobnJseCA9PSAnJyB8fCBucmx4ID09IHVuZGVmaW5lZCB8fCBucmx4ID09ICd1bmRlZmluZWQnKSB7Ly/mtYXnu7/oibLljZXlhYPmoLw66L+Y5pyq5a6a5LmJ5bGe5oCnCiAgICAgICAgICAgICAgICAgICAgY29sb3IgPSAnIGJnY29sb3I9IiNDN0VEQ0MiICcKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICBpZiAobnJicyA9PSAnJyB8fCBucmJzID09ICd1bmRlZmluZWQnIHx8IG5yYnMgPT0gdW5kZWZpbmVkKSB7CiAgICAgICAgICAgICAgICAgICAgbnJicyA9ICctJwogICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKG5yYnMuaW5kZXhPZignfCcpID09IC0xIHx8IG5yYnMgPT0gJ+KEgycgfHwgbnJicyA9PSAnLyUnKSB7CgogICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKG5yYnMuaW5kZXhPZignfCcpICE9IC0xKSB7CiAgICAgICAgICAgICAgICAgICAgbnJicyA9IG5yYnMucmVwbGFjZSgvW15cdTRlMDAtXHU5ZmE1XS9naSwgJycpCiAgICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAgIGlmIChyZXMuZGF0YVtrXS5jb2xzcGFuICE9ICcxJyB8fCByZXMuZGF0YVtrXS5yb3dzcGFuICE9ICcxJykgewogICAgICAgICAgICAgICAgICAgIHRlbXAgKz0gJzx0ZCB0YWJpbmRleD1cJy0xXCcnICsgY29sb3IgKyAnY29sc3Bhbj1cJycgKyByZXMuZGF0YVtrXS5jb2xzcGFuICsgJ1wnIHJvd3NwYW49XCcnICsgcmVzLmRhdGFba10ucm93c3BhbiArICdcJyBpZD1cJycgKyByZXMuZGF0YVtrXS5vYmpfaWQgKyAnXCc+JyArIG5yYnMgKyAnPC90ZD4nCiAgICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgdGVtcCArPSAnPHRkIHRhYmluZGV4PVwnLTFcJycgKyBjb2xvciArICcgaWQ9XCcnICsgcmVzLmRhdGFba10ub2JqX2lkICsgJ1wnIEBjbGljaz1cJ3RkY2xpY2soXCdBXCcsdGhpcylcJz4nICsgbnJicyArICc8L3RkPicKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB0ZW1wICs9ICc8L3RyPicKICAgICAgICAgICAgICBzdHIgKz0gdGVtcAogICAgICAgICAgICB9CiAgICAgICAgICAgIHRoaXMudGFibGVEYXRhQSA9IHVuZGVmaW5lZAogICAgICAgICAgICB0aGlzLnRhYmxlRGF0YUEgPSBzdHIKICAgICAgICAgIH0pCiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMudGFibGVEYXRhQSA9IHVuZGVmaW5lZAogICAgICAgIH0KICAgICAgICAvL0LooajmoLzliqDovb0KICAgICAgICBpZiAodGhpcy5zZWxlY3Rpb25bMF0uYl9ocyAhPSAnJyAmJiB0aGlzLnNlbGVjdGlvblswXS5iX2xzICE9ICcnICYmIHRoaXMuc2VsZWN0aW9uWzBdLmJfaHMgIT0gMCAmJiB0aGlzLnNlbGVjdGlvblswXS5iX2xzICE9IDApIHsKICAgICAgICAgIHRoaXMuZHlGb3JtLmxiYnMgPSAnQicKICAgICAgICAgIGdldFRhYmxlKHRoaXMuZHlGb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIHZhciBzdHIgPSAnJwogICAgICAgICAgICBmb3IgKHZhciBpID0gMDsgaSA8IHRoaXMuc2VsZWN0aW9uWzBdLmJfaHM7IGkrKykgewogICAgICAgICAgICAgIHZhciB0ZW1wID0gJzx0cj4nCiAgICAgICAgICAgICAgZm9yICh2YXIgayA9IDA7IGsgPCByZXMuZGF0YS5sZW5ndGg7IGsrKykgewogICAgICAgICAgICAgICAgaWYgKGkgPT0gcmVzLmRhdGFba10ucm93aW5kZXgpIHsKICAgICAgICAgICAgICAgICAgdmFyIG5yYnMgPSByZXMuZGF0YVtrXS5ucmJzCiAgICAgICAgICAgICAgICAgIHZhciBucmx4ID0gcmVzLmRhdGFba10ubnJseAogICAgICAgICAgICAgICAgICAvL+WumuS5ieminOiJsgogICAgICAgICAgICAgICAgICB2YXIgY29sb3IgPSAnJwogICAgICAgICAgICAgICAgICBpZiAobnJseCA9PSAnMScgfHwgbnJseCA9PSAnMicgfHwgbnJseCA9PSAnMycgfHwgbnJseCA9PSAnNCcpIHsvL+eZveiJsuWNleWFg+agvDrliqjmgIHlsZ7mgKcKICAgICAgICAgICAgICAgICAgICBjb2xvciA9ICcgYmdjb2xvcj0id2hpdGUiICcKICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmIChucmx4ID09ICc1Jykgey8v54Gw6Imy5Y2V5YWD5qC8OumdmeaAgeaWh+acrAogICAgICAgICAgICAgICAgICAgIGNvbG9yID0gJyBiZ2NvbG9yPSIjRUVFRUUwIiAnCiAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAobnJseCA9PSAnJyB8fCBucmx4ID09IHVuZGVmaW5lZCB8fCBucmx4ID09ICd1bmRlZmluZWQnKSB7Ly/mtYXnu7/oibLljZXlhYPmoLw66L+Y5pyq5a6a5LmJ5bGe5oCnCiAgICAgICAgICAgICAgICAgICAgY29sb3IgPSAnIGJnY29sb3I9IiNDN0VEQ0MiICcKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICBpZiAobnJicyA9PSAnJyB8fCBucmJzID09ICd1bmRlZmluZWQnIHx8IG5yYnMgPT0gdW5kZWZpbmVkKSB7CiAgICAgICAgICAgICAgICAgICAgbnJicyA9ICctJwogICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKG5yYnMuaW5kZXhPZignfCcpID09IC0xIHx8IG5yYnMgPT0gJ+KEgycgfHwgbnJicyA9PSAnLyUnKSB7CgogICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKG5yYnMuaW5kZXhPZignfCcpICE9IC0xKSB7CiAgICAgICAgICAgICAgICAgICAgbnJicyA9IG5yYnMucmVwbGFjZSgvW15cdTRlMDAtXHU5ZmE1XS9naSwgJycpCiAgICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICAgIGlmIChyZXMuZGF0YVtrXS5jb2xzcGFuICE9ICcxJyB8fCByZXMuZGF0YVtrXS5yb3dzcGFuICE9ICcxJykgewogICAgICAgICAgICAgICAgICAgIHRlbXAgKz0gJzx0ZCB0YWJpbmRleD1cJy0xXCcnICsgY29sb3IgKyAnY29sc3Bhbj1cJycgKyByZXMuZGF0YVtrXS5jb2xzcGFuICsgJ1wnIHJvd3NwYW49XCcnICsgcmVzLmRhdGFba10ucm93c3BhbiArICdcJyBpZD1cJycgKyByZXMuZGF0YVtrXS5vYmpfaWQgKyAnXCc+JyArIG5yYnMgKyAnPC90ZD4nCiAgICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgdGVtcCArPSAnPHRkIHRhYmluZGV4PVwnLTFcJycgKyBjb2xvciArICcgaWQ9XCcnICsgcmVzLmRhdGFba10ub2JqX2lkICsgJ1wnPicgKyBucmJzICsgJzwvdGQ+JwogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIHRlbXAgKz0gJzwvdHI+JwogICAgICAgICAgICAgIHN0ciArPSB0ZW1wCiAgICAgICAgICAgIH0KICAgICAgICAgICAgdGhpcy50YWJsZURhdGFCID0gdW5kZWZpbmVkCiAgICAgICAgICAgIHRoaXMudGFibGVEYXRhQiA9IHN0cgogICAgICAgICAgfSkKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy50YWJsZURhdGFCID0gdW5kZWZpbmVkCiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ2luZm8nLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+mAieaLqeS4gOadoeaVsOaNriEnCiAgICAgICAgfSkKICAgICAgfQogICAgfSwKICAgIC8v5p+l6K+i5YiX6KGoCiAgICBnZXRMaXN0KHBhcmFtcykgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zID0gey4uLnRoaXMucXVlcnlQYXJhbXMsIC4uLnBhcmFtc307CiAgICAgIGxldCBwYXJhbSA9IHsuLi50aGlzLnF1ZXJ5UGFyYW1zLC4uLnBhcmFtc30KICAgICAgZ2V0UGFnZURhdGFMaXN0KHBhcmFtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnRhYmxlRGF0YSA9IHJlcy5kYXRhLnJlY29yZHMKICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIudG90YWwgPSByZXMuZGF0YS50b3RhbAoKICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8udGFibGVEYXRhLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICB0aGlzLm9wdGlvbnMuZm9yRWFjaChlbGVtZW50ID0+IHsKICAgICAgICAgICAgaWYgKGl0ZW0uenkgPT09IGVsZW1lbnQudmFsdWUpIHsKICAgICAgICAgICAgICBpdGVtLnp5TmFtZSA9IGVsZW1lbnQubGFiZWwKICAgICAgICAgICAgfQogICAgICAgICAgfSkKICAgICAgICAgIHRoaXMuc2ZmeE9wdGlvbnMuZm9yRWFjaChlbGVtZW50ID0+IHsKICAgICAgICAgICAgaWYgKGl0ZW0uc2ZmeCA9PT0gZWxlbWVudC52YWx1ZSkgewogICAgICAgICAgICAgIGl0ZW0uc2ZmeE5hbWUgPSBlbGVtZW50LmxhYmVsCiAgICAgICAgICAgIH0KICAgICAgICAgIH0pCiAgICAgICAgfSkKICAgICAgfSkKICAgIH0sCiAgICAvL+aHkuWKoOi9veWHveaVsAogICAgbG9hZE5vZGUobm9kZSwgcmVzb2x2ZSkgewogICAgICBsZXQgVHJlZXBhcmFtTWFwID0gewogICAgICAgIHBpZDogJycsCiAgICAgICAgc3BiTG9nbzogWyfovpPnlLXorr7lpIcnLCAn5Y+Y55S16K6+5aSHJywn6YWN55S16K6+5aSHJ10KICAgICAgfQogICAgICBpZiAobm9kZS5sZXZlbCA9PT0gMCkgewogICAgICAgIFRyZWVwYXJhbU1hcC5waWQgPSAnc2InCiAgICAgICAgcmV0dXJuIHRoaXMuZ2V0VHJlZU5vZGUoVHJlZXBhcmFtTWFwLCByZXNvbHZlKQogICAgICB9CiAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgIFRyZWVwYXJhbU1hcC5waWQgPSBub2RlLmRhdGEuY29kZQogICAgICAgIHRoaXMuZ2V0VHJlZU5vZGUoVHJlZXBhcmFtTWFwLCByZXNvbHZlKQogICAgICB9LCA1MDApCgogICAgfSwKICAgIC8v6I635Y+W5qCR6IqC54K55pWw5o2uCiAgICBnZXRUcmVlTm9kZShwYXJhbU1hcCwgcmVzb2x2ZSkgewogICAgICBnZXREZXZpY2VDbGFzc1RyZWVOb2RlQnlQaWQocGFyYW1NYXApLnRoZW4ocmVzID0+IHsKICAgICAgICBsZXQgdHJlZU5vZGVzID0gW10KICAgICAgICByZXMuZGF0YS5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgbGV0IG5vZGUgPSB7CiAgICAgICAgICAgIG5hbWU6IGl0ZW0ubmFtZSwKICAgICAgICAgICAgbGV2ZWw6IGl0ZW0ubGV2ZWwsCiAgICAgICAgICAgIGlkOiBpdGVtLmlkLAogICAgICAgICAgICBwaWQ6IGl0ZW0ucGlkLAogICAgICAgICAgICBsZWFmOiBmYWxzZSwKICAgICAgICAgICAgY29kZTogaXRlbS5jb2RlCiAgICAgICAgICB9CiAgICAgICAgICB0cmVlTm9kZXMucHVzaChub2RlKQogICAgICAgIH0pCiAgICAgICAgcmVzb2x2ZSh0cmVlTm9kZXMpCiAgICAgIH0pCiAgICB9LAogICAgLy/liJvlu7pB6KGo5qC85oyJ6ZKu5Yqf6IO9CiAgICBzYXZlVGFibGVBKCkgewogICAgICAvL+WIpOaWreWhq+WGmeeahOihjOaVsOS4juWIl+aVsO+8jOWAvOaYr+WQpuespuWQiOagh+WHhgogICAgICBpZiAodGhpcy5keUZvcm0uYV9ocyA8PSAwIHx8IHRoaXMuZHlGb3JtLmFfbHMgPD0gMCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogJ2luZm8nLAogICAgICAgICAgbWVzc2FnZTogJ+S4jeWFgeiuuOWhq+WGmTDmiJblsI/kuo4w55qE5pWwIScKICAgICAgICB9KQogICAgICAgIHJldHVybgogICAgICB9CiAgICAgIC8v5Yik5pat6KGM5ZKM5YiX55qE5Y+Y5YyW5oOF5Ya1CiAgICAgIGxldCByb3dfZGlmZmVyID0gdGhpcy5keUZvcm0uYV9oc09sZCAtIHRoaXMuZHlGb3JtLmFfaHMgIC8v5a+55YmN5ZCO5Y+Y5YyW55qE6KGM5YGa5beu77yM5Yik5pat5piv5ZCm5aKe5Yqg5oiW5YeP5bCR6KGMCiAgICAgIGxldCBjb2xfZGlmZmVyID0gdGhpcy5keUZvcm0uYV9sc09sZCAtIHRoaXMuZHlGb3JtLmFfbHMgIC8v5a+55YmN5ZCO5Y+Y5YyW55qE6KGM5YGa5beu77yM5Yik5pat5piv5ZCm5aKe5Yqg5oiW5YeP5bCR5YiXCiAgICAgIGlmIChyb3dfZGlmZmVyID09IDAgJiYgY29sX2RpZmZlciA9PSAwKSB7Ly/ooYzliJfml6Dlj5HnlJ/lj5jljJbvvIzkuI3ov5vooYzku7vkvZXmk43kvZwKICAgICAgICByZXR1cm4KICAgICAgfSBlbHNlIGlmIChyb3dfZGlmZmVyID4gMCB8fCBjb2xfZGlmZmVyID4gMCkgey8v6KGM5oiW6ICF5YiX5YeP5bCRLOaPkOekuueUqOaIt+aYr+WQpuWIoOmZpAogICAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruWumuWIoOmZpOihjOWIlz8nLCAn5o+Q56S6JywgewogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICAgIC8v6K6+572u57G75Yir5qCH6K+GCiAgICAgICAgICB0aGlzLmR5Rm9ybS5sYmJzID0gJ0EnCiAgICAgICAgICB1cGRhdGVUYWJsZU51bSh0aGlzLmR5Rm9ybSkudGhlbihyZXMgPT4gewogICAgICAgICAgICBpZiAocmVzLmNvZGUgPT0gJzAwMDAnKSB7CiAgICAgICAgICAgICAgdGhpcy5yZWxvYWRUYWJsZSgnQScpCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip/vvIEnKQogICAgICAgICAgICAgIHRoaXMuZHlGb3JtLmFfaHNPbGQgPSB0aGlzLmR5Rm9ybS5hX2hzCiAgICAgICAgICAgICAgdGhpcy5keUZvcm0uYV9sc09sZCA9IHRoaXMuZHlGb3JtLmFfbHMKICAgICAgICAgICAgfQogICAgICAgICAgfSkKICAgICAgICB9KQogICAgICB9IGVsc2UgewogICAgICAgIC8v6K6+572u57G75Yir5qCH6K+GCiAgICAgICAgdGhpcy5keUZvcm0ubGJicyA9ICdBJwogICAgICAgIHVwZGF0ZVRhYmxlTnVtKHRoaXMuZHlGb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICBpZiAocmVzLmNvZGUgPT0gJzAwMDAnKSB7CiAgICAgICAgICAgIHRoaXMucmVsb2FkVGFibGUoJ0EnKQogICAgICAgICAgICB0aGlzLmR5Rm9ybS5hX2hzT2xkID0gdGhpcy5keUZvcm0uYV9ocwogICAgICAgICAgICB0aGlzLmR5Rm9ybS5hX2xzT2xkID0gdGhpcy5keUZvcm0uYV9scwogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgIH0KICAgIH0sCiAgICAvL+WIm+W7ukLooajmoLzmjInpkq7lip/og70KICAgIHNhdmVUYWJsZUIoKSB7CiAgICAgIC8v5Yik5pat5aGr5YaZ55qE6KGM5pWw5LiO5YiX5pWw77yM5YC85piv5ZCm56ym5ZCI5qCH5YeGCiAgICAgIGlmICh0aGlzLmR5Rm9ybS5iX2hzIDw9IDAgfHwgdGhpcy5keUZvcm0uYl9scyA8PSAwKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICB0eXBlOiAnaW5mbycsCiAgICAgICAgICBtZXNzYWdlOiAn5LiN5YWB6K645aGr5YaZMOaIluWwj+S6jjDnmoTmlbAhJwogICAgICAgIH0pCiAgICAgICAgcmV0dXJuCiAgICAgIH0KICAgICAgLy/liKTmlq3ooYzlkozliJfnmoTlj5jljJbmg4XlhrUKICAgICAgbGV0IHJvd19kaWZmZXIgPSB0aGlzLmR5Rm9ybS5iX2hzT2xkIC0gdGhpcy5keUZvcm0uYl9ocyAgLy/lr7nliY3lkI7lj5jljJbnmoTooYzlgZrlt67vvIzliKTmlq3mmK/lkKblop7liqDmiJblh4/lsJHooYwKICAgICAgbGV0IGNvbF9kaWZmZXIgPSB0aGlzLmR5Rm9ybS5iX2xzT2xkIC0gdGhpcy5keUZvcm0uYl9scyAgLy/lr7nliY3lkI7lj5jljJbnmoTooYzlgZrlt67vvIzliKTmlq3mmK/lkKblop7liqDmiJblh4/lsJHliJcKICAgICAgaWYgKHJvd19kaWZmZXIgPT0gMCAmJiBjb2xfZGlmZmVyID09IDApIHsvL+ihjOWIl+aXoOWPkeeUn+WPmOWMlu+8jOS4jei/m+ihjOS7u+S9leaTjeS9nAogICAgICAgIHJldHVybgogICAgICB9IGVsc2UgaWYgKHJvd19kaWZmZXIgPiAwIHx8IGNvbF9kaWZmZXIgPiAwKSB7Ly/ooYzmiJbogIXliJflh4/lsJEs5o+Q56S655So5oi35piv5ZCm5Yig6ZmkCiAgICAgICAgdGhpcy4kY29uZmlybSgn56Gu5a6a5Yig6Zmk6KGM5YiXPycsICfmj5DnpLonLCB7CiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgIH0pLnRoZW4oKCkgPT4gewogICAgICAgICAgLy/orr7nva7nsbvliKvmoIfor4YKICAgICAgICAgIHRoaXMuZHlGb3JtLmxiYnMgPSAnQicKICAgICAgICAgIHVwZGF0ZVRhYmxlTnVtKHRoaXMuZHlGb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PSAnMDAwMCcpIHsKICAgICAgICAgICAgICB0aGlzLnJlbG9hZFRhYmxlKCdCJykKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WIoOmZpOaIkOWKn++8gScpCiAgICAgICAgICAgICAgdGhpcy5keUZvcm0uYl9oc09sZCA9IHRoaXMuZHlGb3JtLmJfaHMKICAgICAgICAgICAgICB0aGlzLmR5Rm9ybS5iX2xzT2xkID0gdGhpcy5keUZvcm0uYl9scwogICAgICAgICAgICB9CiAgICAgICAgICB9KQogICAgICAgIH0pCiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy/orr7nva7nsbvliKvmoIfor4YKICAgICAgICB0aGlzLmR5Rm9ybS5sYmJzID0gJ0InCiAgICAgICAgdXBkYXRlVGFibGVOdW0odGhpcy5keUZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgIGlmIChyZXMuY29kZSA9PSAnMDAwMCcpIHsKICAgICAgICAgICAgdGhpcy5yZWxvYWRUYWJsZSgnQicpCiAgICAgICAgICAgIHRoaXMuZHlGb3JtLmJfaHNPbGQgPSB0aGlzLmR5Rm9ybS5iX2hzCiAgICAgICAgICAgIHRoaXMuZHlGb3JtLmJfbHNPbGQgPSB0aGlzLmR5Rm9ybS5iX2xzCiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgfQogICAgfSwKICAgIC8v6YeN5paw5Yqg6L296KGo5qC8CiAgICByZWxvYWRUYWJsZShyZWxvYWRfdGFibGVfZmxhZykgewogICAgICBpZiAocmVsb2FkX3RhYmxlX2ZsYWcgPT0gJ0InKSB7CiAgICAgICAgdmFyIHJvd051bUIgPSB0aGlzLmR5Rm9ybS5iX2hzCiAgICAgICAgZ2V0VGFibGUodGhpcy5keUZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICAgIGlmIChyb3dOdW1CICE9IDApIHsKICAgICAgICAgICAgdmFyIHN0cjEgPSAnJwogICAgICAgICAgICBmb3IgKHZhciBpID0gMDsgaSA8IHJvd051bUI7IGkrKykgewogICAgICAgICAgICAgIHZhciB0ZW1wID0gJzx0cj4nCiAgICAgICAgICAgICAgZm9yICh2YXIgayA9IDA7IGsgPCByZXMuZGF0YS5sZW5ndGg7IGsrKykgewogICAgICAgICAgICAgICAgaWYgKGkgPT0gcmVzLmRhdGFba10ucm93aW5kZXgpIHsKICAgICAgICAgICAgICAgICAgdmFyIG5yYnMgPSByZXMuZGF0YVtrXS5ucmJzCiAgICAgICAgICAgICAgICAgIHZhciBucmx4ID0gcmVzLmRhdGFba10ubnJseAogICAgICAgICAgICAgICAgICB2YXIgY29sb3IgPSAnJwogICAgICAgICAgICAgICAgICBpZiAobnJseCA9PSAnMScgfHwgbnJseCA9PSAnMicgfHwgbnJseCA9PSAnMycgfHwgbnJseCA9PSAnNCcpIHsvL+eZveiJsuWNleWFg+agvDrliqjmgIHlsZ7mgKcKICAgICAgICAgICAgICAgICAgICBjb2xvciA9ICcgYmdjb2xvcj0id2hpdGUiICcKICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmIChucmx4ID09ICc1Jykgey8v54Gw6Imy5Y2V5YWD5qC8OumdmeaAgeaWh+acrAogICAgICAgICAgICAgICAgICAgIGNvbG9yID0gJyBiZ2NvbG9yPSIjRUVFRUUwIiAnCiAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAobnJseCA9PSAnJyB8fCBucmx4ID09IHVuZGVmaW5lZCB8fCBucmx4ID09ICd1bmRlZmluZWQnKSB7Ly/mtYXnu7/oibLljZXlhYPmoLw66L+Y5pyq5a6a5LmJ5bGe5oCnCiAgICAgICAgICAgICAgICAgICAgY29sb3IgPSAnIGJnY29sb3I9IiNDN0VEQ0MiICcKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICBpZiAobnJicyA9PSAnJyB8fCBucmJzID09ICd1bmRlZmluZWQnIHx8IG5yYnMgPT0gdW5kZWZpbmVkKSB7CiAgICAgICAgICAgICAgICAgICAgbnJicyA9ICctJwogICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKG5yYnMuaW5kZXhPZignfCcpID09IC0xIHx8IG5yYnMgPT0gJ+KEgycgfHwgbnJicyA9PSAnLyUnKSB7CgogICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKG5yYnMuaW5kZXhPZignfCcpICE9IC0xKSB7CiAgICAgICAgICAgICAgICAgICAgbnJicyA9IG5yYnMucmVwbGFjZSgvW15cdTRlMDAtXHU5ZmE1XS9naSwgJycpCiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgaWYgKHJlcy5kYXRhW2tdLmNvbHNwYW4gIT0gJzEnIHx8IHJlcy5kYXRhW2tdLnJvd3NwYW4gIT0gJzEnKSB7CiAgICAgICAgICAgICAgICAgICAgdGVtcCArPSAnPHRkIHRhYmluZGV4PVwnLTFcJycgKyBjb2xvciArICcgY29sc3Bhbj1cJycgKyByZXMuZGF0YVtrXS5jb2xzcGFuICsgJ1wnIHJvd3NwYW49XCcnICsgcmVzLmRhdGFba10ucm93c3BhbiArICdcJyBpZD1cJycgKyByZXMuZGF0YVtrXS5vYmpfaWQgKyAnXCc+JyArIG5yYnMgKyAnPC90ZD4nCiAgICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgdGVtcCArPSAnPHRkIHRhYmluZGV4PVwnLTFcJycgKyBjb2xvciArICdpZD1cJycgKyByZXMuZGF0YVtrXS5vYmpfaWQgKyAnXCc+JyArIG5yYnMgKyAnPC90ZD4nCiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgdGVtcCArPSAnPC90cj4nCiAgICAgICAgICAgICAgc3RyMSArPSB0ZW1wCiAgICAgICAgICAgIH0KICAgICAgICAgICAgdGhpcy50YWJsZURhdGFCID0gdW5kZWZpbmVkCiAgICAgICAgICAgIHRoaXMudGFibGVEYXRhQiA9IHN0cjEKICAgICAgICAgIH0KICAgICAgICB9KQogICAgICB9IGVsc2UgewogICAgICAgIHZhciByb3dOdW1BID0gdGhpcy5keUZvcm0uYV9ocwogICAgICAgIGdldFRhYmxlKHRoaXMuZHlGb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICBpZiAocm93TnVtQSAhPSAwKSB7CiAgICAgICAgICAgIHZhciBzdHIxID0gJycKICAgICAgICAgICAgZm9yICh2YXIgaSA9IDA7IGkgPCByb3dOdW1BOyBpKyspIHsKICAgICAgICAgICAgICB2YXIgdGVtcCA9ICc8dHI+JwogICAgICAgICAgICAgIGZvciAodmFyIGsgPSAwOyBrIDwgcmVzLmRhdGEubGVuZ3RoOyBrKyspIHsKICAgICAgICAgICAgICAgIGlmIChpID09IHJlcy5kYXRhW2tdLnJvd2luZGV4KSB7CiAgICAgICAgICAgICAgICAgIHZhciBucmJzID0gcmVzLmRhdGFba10ubnJicwogICAgICAgICAgICAgICAgICB2YXIgbnJseCA9IHJlcy5kYXRhW2tdLm5ybHgKICAgICAgICAgICAgICAgICAgdmFyIGNvbG9yID0gJycKICAgICAgICAgICAgICAgICAgaWYgKG5ybHggPT0gJzEnIHx8IG5ybHggPT0gJzInIHx8IG5ybHggPT0gJzMnIHx8IG5ybHggPT0gJzQnKSB7Ly/nmb3oibLljZXlhYPmoLw65Yqo5oCB5bGe5oCnCiAgICAgICAgICAgICAgICAgICAgY29sb3IgPSAnIGJnY29sb3I9IndoaXRlIiAnCiAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAobnJseCA9PSAnNScpIHsvL+eBsOiJsuWNleWFg+agvDrpnZnmgIHmlofmnKwKICAgICAgICAgICAgICAgICAgICBjb2xvciA9ICcgYmdjb2xvcj0iI0VFRUVFMCIgJwogICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKG5ybHggPT0gJycgfHwgbnJseCA9PSB1bmRlZmluZWQgfHwgbnJseCA9PSAndW5kZWZpbmVkJykgey8v5rWF57u/6Imy5Y2V5YWD5qC8Oui/mOacquWumuS5ieWxnuaApwogICAgICAgICAgICAgICAgICAgIGNvbG9yID0gJyBiZ2NvbG9yPSIjQzdFRENDIiAnCiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgICAgaWYgKG5yYnMgPT0gJycgfHwgbnJicyA9PSAndW5kZWZpbmVkJyB8fCBucmJzID09IHVuZGVmaW5lZCkgewogICAgICAgICAgICAgICAgICAgIG5yYnMgPSAnLScKICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmIChucmJzLmluZGV4T2YoJ3wnKSA9PSAtMSB8fCBucmJzID09ICfihIMnIHx8IG5yYnMgPT0gJy8lJykgewoKICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmIChucmJzLmluZGV4T2YoJ3wnKSAhPSAtMSkgewogICAgICAgICAgICAgICAgICAgIG5yYnMgPSBucmJzLnJlcGxhY2UoL1teXHU0ZTAwLVx1OWZhNV0vZ2ksICcnKQogICAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICAgIGlmIChyZXMuZGF0YVtrXS5jb2xzcGFuICE9ICcxJyB8fCByZXMuZGF0YVtrXS5yb3dzcGFuICE9ICcxJykgewogICAgICAgICAgICAgICAgICAgIHRlbXAgKz0gJzx0ZCB0YWJpbmRleD1cJy0xXCcnICsgY29sb3IgKyAnY29sc3Bhbj1cJycgKyByZXMuZGF0YVtrXS5jb2xzcGFuICsgJ1wnIHJvd3NwYW49XCcnICsgcmVzLmRhdGFba10ucm93c3BhbiArICdcJyBpZD1cJycgKyByZXMuZGF0YVtrXS5vYmpfaWQgKyAnXCc+JyArIG5yYnMgKyAnPC90ZD4nCiAgICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgdGVtcCArPSAnPHRkIHRhYmluZGV4PVwnLTFcJycgKyBjb2xvciArICdpZD1cJycgKyByZXMuZGF0YVtrXS5vYmpfaWQgKyAnXCc+JyArIG5yYnMgKyAnPC90ZD4nCiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgdGVtcCArPSAnPC90cj4nCiAgICAgICAgICAgICAgc3RyMSArPSB0ZW1wCiAgICAgICAgICAgIH0KICAgICAgICAgICAgdGhpcy50YWJsZURhdGFBID0gdW5kZWZpbmVkCiAgICAgICAgICAgIHRoaXMudGFibGVEYXRhQSA9IHN0cjEKICAgICAgICAgIH0KICAgICAgICB9KQogICAgICB9CiAgICB9LAogICAgaGFuZGxlKHNpZ24sIG9iaikgewogICAgICB0aGlzLmR5Rm9ybS5sYmJzID0gc2lnbgoKICAgIH0sCiAgICAvL+iOt+WPlumTreeJjOS/oeaBrwogICAgZ2V0TmFtZXBsYXRlSW5mbyhyb3cpIHsKICAgICAgdGhpcy5yb3dEYXRhID0gcm93CiAgICAgIHRoaXMucm93RGF0YS5zYmx4Ym0gPSB0aGlzLnNibHhibQogICAgICBsZXQgcGFyYW1zID0gSlNPTi5zdHJpbmdpZnkoewogICAgICAgICdvYmpfaWQnOiByb3cub2JqSWQsCiAgICAgICAgJ2xiYnMnOiAnQScKICAgICAgfSkKICAgICAgZ2V0VGFibGUocGFyYW1zKS50aGVuKHJlcyA9PiB7CiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAnMDAwMCcpIHsKICAgICAgICAgIHRoaXMubXhEYXRhID0gcmVzLmRhdGEvL+mcgOimgeWFiOiuvue9ruaVsOaNruWGjeW8ueahhu+8jOWQpuWImeaVsOaNruS8oOS4jei/h+WOuwogICAgICAgICAgdGhpcy5pc1Nob3dNcEluZm8gPSB0cnVlCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIC8v5YWz6Zet6ZOt54mM5YaF5a655by55qGGCiAgICBjbG9zZUluZm9EaWFsb2coKSB7CiAgICAgIHRoaXMuaXNTaG93TXBJbmZvID0gZmFsc2UKICAgICAgLy/liLfmlrDniLbpobXpnaIKICAgICAgdGhpcy5yZWxvYWQoKQogICAgfSwKICAgIHNhdmVTcGFuKCkgewogICAgfSwKICAgIHJlc2V0KCkgewogICAgfSwKICAgIGVkaXRDZWxsUHJvcGVydGllcygpIHsKICAgIH0sCiAgICByZXNldENlbGxQcm9wZXJ0aWVzKCkgewogICAgfSwKICAgIC8v5riF56m66KGo5Y2V5pWw5o2uCiAgICBoYW5kbGVDbG9zZSgpIHsKICAgICAgdGhpcy5mb3JtID0ge30KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIHRoaXMuZm9ybSA9IHRoaXMuJG9wdGlvbnMuZGF0YSgpLmZvcm0KICAgICAgICB0aGlzLnJlc2V0Rm9ybSgnZm9ybScpCiAgICAgIH0pCiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["sympk.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8PA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "sympk.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sympk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row :gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card class=\"aside_height\" shadow=\"never\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备类型</span>\n          </div>\n          <div style=\" overflow: auto;height: 84vh\">\n            <el-tree\n              highlight-current\n              id=\"tree\"\n              :props=\"props\"\n              :load=\"loadNode\"\n              lazy\n              @node-click=\"handleNodeClick\"\n              :default-expanded-keys=\"['1']\"\n            />\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 180, itemWidth: 300 }\"\n          @handleReset=\"getReset\"\n          @handleEvent=\"handleEvent\"\n        />\n        <el-white>\n          <el-white class=\"button-group\">\n            <el-button class=\"mb8\" @click=\"addButton\" type=\"primary\" icon=\"el-icon-plus\">\n              新增\n            </el-button>\n            <!--<el-button class=\"mb8\" @click=\"deleteButton\" type=\"danger\" icon=\"el-icon-delete\">-->\n            <!--  删除-->\n            <!--</el-button>-->\n            <!--<el-button class=\"mb8\" v-show=\"false\" @click=\"viewButton\" type=\"primary\" icon=\"el-icon-circle-plus-outline\">-->\n            <!--  定义详情-->\n            <!--</el-button>-->\n          </el-white>\n          <comp-table :table-and-page-info=\"tableAndPageInfo\" @update:multipleSelection=\"handleSelectionChange\"  height=\"68.8vh\">\n            <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\"\n                             width=\"160\"\n                             :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button @click=\"updateDetails(scope.row)\" v-show=\"scope.row.createUser == currentUser\" type=\"text\"\n                           size=\"small\"  title=\"修改\"  class='el-icon-edit'\n                >\n                </el-button>\n                <el-button @click=\"getDetails(scope.row)\" type=\"text\" size=\"small\" title=\"详情\" class=\"el-icon-view\"></el-button>\n                <el-button @click=\"getNameplateInfo(scope.row)\" v-show=\"scope.row.createUser == currentUser\" type=\"text\"\n                           size=\"small\" title=\"定义详情\" class=\"el-icon-edit-outline\"\n                >\n                </el-button>\n                <el-button @click=\"deleteButton(scope.row)\" v-show=\"scope.row.createUser == currentUser\" type=\"text\"\n                           size=\"small\" title=\"删除\" class=\"el-icon-delete\"\n                >\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n    <!-- 新增、修改、详情界面 -->\n    <el-dialog :title=title :visible.sync=\"isShowDetails\" width=\"40%\" @close=\"handleClose\" v-dialogDrag>\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"铭牌名称：\" prop=\"mpmc\">\n              <el-input placeholder=\"铭牌名称\" v-model=\"form.mpmc\" :disabled=\"isDisabled\" style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"专业：\" prop=\"zy\">\n              <el-select placeholder=\"专业\" v-model=\"form.zy\" :disabled=\"isDisabled\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in options\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否分相：\" prop=\"sffx\">\n              <el-select placeholder=\"是否分相\" v-model=\"form.sffx\" :disabled=\"isDisabled\" style=\"width: 100%\">\n                <el-option\n                  v-for=\"item in sffxOptions\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button v-if=\"title==='新增' || title==='修改'\" type=\"primary\" @click=\"save\">确 认</el-button>\n      </div>\n    </el-dialog>\n    <!-- 定义详情界面 -->\n    <el-dialog title=\"定义铭牌详情\" :visible.sync=\"isShowDyDetails\" width=\"75%\" v-dialogDrag>\n      <el-form label-width=\"120px\" ref=\"dyForm\" :model=\"dyForm\">\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"6\">\n            <el-row>\n              <h4\n                style=\"margin-left: 15px;font-family: PingFangSC-Regular;font-size:14px;color:#333333;text-align:center;font-weight: bold;\"\n              >A表格</h4>\n            </el-row>\n            <el-row>\n              <el-form-item label=\"行:\" prop=\"a_hs\">\n                <el-col :span=\"20\">\n                  <el-input v-model=\"dyForm.a_hs\" style=\"width: 100%;\"></el-input>\n                </el-col>\n              </el-form-item>\n            </el-row>\n            <el-row>\n              <el-form-item label=\"列:\" prop=\"a_ls\">\n                <el-col :span=\"20\">\n                  <el-input v-model=\"dyForm.a_ls\" style=\"width: 100%;\"></el-input>\n                </el-col>\n              </el-form-item>\n            </el-row>\n            <el-row style=\"text-align:center;\">\n              <el-button type=\"primary\" @click=\"saveTableA\">创建A表格</el-button>\n            </el-row>\n            <hr style=\"height:1px;border:none;border-top:1px ridge #DBDBDB;width: 100%;\">\n            <el-row>\n              <h4\n                style=\"margin-left: 15px;font-family: PingFangSC-Regular;font-size:14px;color:#333333;text-align:center;font-weight: bold;\"\n              >B表格</h4>\n            </el-row>\n            <el-row>\n              <el-form-item label=\"行:\" prop=\"b_hs\">\n                <el-col :span=\"20\">\n                  <el-input v-model=\"dyForm.b_hs\" style=\"width: 100%;\"></el-input>\n                </el-col>\n              </el-form-item>\n            </el-row>\n            <el-row>\n              <el-form-item label=\"列:\" prop=\"b_ls\">\n                <el-col :span=\"20\">\n                  <el-input v-model=\"dyForm.b_ls\" style=\"width: 100%;\"></el-input>\n                </el-col>\n              </el-form-item>\n            </el-row>\n            <el-row style=\"text-align:center;\">\n              <el-button type=\"primary\" @click=\"saveTableB\">创建B表格</el-button>\n            </el-row>\n            <hr style=\"height:1px;border:none;border-top:1px ridge #DBDBDB;width: 100%;\">\n            <el-row>\n              <h4\n                style=\"margin-left: 15px;font-family: PingFangSC-Regular;font-size:14px;color:#333333;text-align:center;font-weight: bold;\"\n              >单元格操作</h4>\n            </el-row>\n            <el-row>\n              <el-form-item label=\"行跨度:\" prop=\"rowSpanNum\">\n                <el-col :span=\"20\">\n                  <el-input v-model=\"dyForm.rowSpanNum\" style=\"width: 100%;\"></el-input>\n                </el-col>\n              </el-form-item>\n            </el-row>\n            <el-row>\n              <el-form-item label=\"列跨度:\" prop=\"colSpanNum\">\n                <el-col :span=\"20\">\n                  <el-input v-model=\"dyForm.colSpanNum\" style=\"width: 100%;\"></el-input>\n                </el-col>\n              </el-form-item>\n            </el-row>\n            <el-row style=\"text-align:center;\">\n              <el-button type=\"primary\" @click=\"saveSpan\">保存</el-button>\n              <el-button type=\"danger\" @click=\"reset\">清除</el-button>\n            </el-row>\n            <hr style=\"height:1px;border:none;border-top:1px ridge #DBDBDB;width: 100%;\">\n            <el-row style=\"text-align:center;\">\n              <el-button @click=\"editCellProperties\">编辑单元格属性</el-button>\n              <el-button @click=\"resetCellProperties\">重置单元格内容</el-button>\n            </el-row>\n            <hr style=\"height:1px;border:none;border-top:1px ridge #DBDBDB;width: 100%;\">\n            <el-row style=\"text-align:center;\">\n              <p style=\"color: red;\">灰色单元格:静态文本</p>\n              <p style=\"color: red;\">白色单元格:动态属性</p>\n              <p style=\"color: red;\">浅灰色单元格:只读的动态属性</p>\n              <p style=\"color: red;\">浅绿色单元格:还未定义属性</p>\n              <p style=\"color: red;\">浅黄色单元格:当前选中的单元格</p>\n            </el-row>\n          </el-col>\n          <el-col :span=\"18\">\n            <el-row>\n              <h4\n                style=\"margin-left: 15px;font-family: PingFangSC-Regular;font-size:14px;color:#333333;text-align:left;font-weight: bold;\"\n              >A表格</h4>\n            </el-row>\n            <el-row>\n              <h3 v-html=\"dyForm.title\"\n                  style=\"font-weight: bold;margin: 0px 0px 0px 0px;height: 45px;background-color:#d5ddfd;line-height:2.1;font-size: 21px;text-align:center;\"\n              ></h3>\n            </el-row>\n            <el-row>\n              <el-col :span=\"24\">\n                <table v-html=\"tableDataA\" border=\"1\" style=\"width: 100%;\" @cell-click=\"handle\">\n                </table>\n              </el-col>\n            </el-row>\n            <hr style=\"height:1px;border:none;border-top:1px ridge #DBDBDB;width: 100%;\">\n            <el-row>\n              <h4\n                style=\"margin-left: 15px;font-family: PingFangSC-Regular;font-size:14px;color:#333333;text-align:left;font-weight: bold;\"\n              >B表格</h4>\n            </el-row>\n            <el-row>\n              <el-col :span=\"24\">\n                <table v-html=\"tableDataB\" border=\"1\" style=\"width: 100%;\">\n                </table>\n              </el-col>\n            </el-row>\n          </el-col>\n        </el-row>\n      </el-form>\n    </el-dialog>\n\n    <el-dialog\n      :visible.sync=\"isShowMpInfo\"\n      v-if=\"isShowMpInfo\"\n      v-dialogDrag\n      width=\"68%\"\n      title=\"铭牌内容\"\n      :append-to-body=\"true\"\n      @close=\"closeInfoDialog\"\n    >\n      <!--        <symp-info\n                :mp-data=\"rowData\"\n                @closeInfoDialog=\"closeInfoDialog\"></symp-info>-->\n      <mpxq-info :mp-data=\"rowData\" :mx-data.sync=\"mxData\"\n                 @closeInfoDialog=\"closeInfoDialog\"\n      />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n\nimport {\n  getDeviceClassTreeNodeByPid,\n  getPageDataList,\n  getTable,\n  remove,\n  saveOrUpdate,\n  updateTableNum\n} from '@/api/dagangOilfield/bzgl/sympk/sympk'\nimport SympInfo from '@/views/dagangOilfield/bzgl/sympk/sympInfo'\nimport MpxqInfo from '@/views/dagangOilfield/bzgl/sympk/mpxqInfo'\n\nexport default {\n  name: 'sympk',\n  components: { MpxqInfo, SympInfo },\n  inject: ['reload'],//inject注入根组件的reload方法\n  data() {\n    return {\n      params: {},\n      //筛选框\n      filterInfo: {\n        data: {\n          mpmc: '',\n          zy: '',\n          sffx: ''\n        },\n        fieldList: [\n          {\n            label: '铭牌名称',\n            value: 'mpmc',\n            type: 'input',\n            clearable: true\n          },\n          {\n            label: '是否分相',\n            value: 'sffx',\n            type: 'select',\n            options: [\n              {label: '是', value: '1'},\n              {label: '否', value: '0'}\n            ],\n            clearable: true\n          }\n        ]\n      },\n      currentUser: this.$store.getters.name,\n      ids: [],\n      props: {\n        label: 'name',\n        children: 'zones',\n        isLeaf: 'leaf'\n      },\n      //专业下拉框数据\n      options: [{ label: '输电', value: 'SD' }, { label: '变电', value: 'BD' }],\n      //是否分相下拉框数据\n      sffxOptions: [{ label: '是', value: '1' }, { label: '否', value: '0' }],\n      form: {\n        objId: undefined,\n        sblxbm: undefined,\n        mpmc: undefined,\n        zy: undefined,\n        sffx: '',\n        isMpSyxm: 0\n      },\n      dyForm: {\n        obj_id: undefined,\n        a_hs: 0,\n        a_hsOld: 0,\n        a_ls: 0,\n        a_lsOld: 0,\n        b_hs: 0,\n        b_hsOld: 0,\n        b_ls: 0,\n        b_lsOld: 0,\n        rowSpanNum: 1,\n        colSpanNum: 1,\n        lbbs: undefined,\n        title: undefined\n      },\n      isShowDetails: false,\n      isShowDyDetails: false,\n      title: '',\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: '铭牌名称', prop: 'mpmc', minWidth: '180' },\n          { label: '专业', prop: 'zyName', minWidth: '200' },\n          { label: '是否分相', prop: 'sffxName', minWidth: '200' }\n          // {\n          //   prop: 'operation',\n          //   label: '操作',\n          //   minWidth: '100px',\n          //   style: { display: 'block' },\n          //   //操作列固定再右侧\n          //   fixed: 'right',\n          //   operation: [\n          //     { name: '修改', clickFun: this.updateDetails },\n          //     { name: '详情', clickFun: this.getDetails },\n          //     { name: '定义详情', clickFun: this.getNameplateInfo }\n          //   ]\n          // }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      //删除是否可用\n      multipleSensor: true,\n      //查询参数\n      queryParams: {\n        zy: '',\n        sblxbm: undefined,\n        pageNum: 1,\n        pageSize: 10,\n        isMpSyxm: 0\n      },\n      isDisabled: false,\n      rules: {\n        mpmc: [{ required: true, message: '请填写铭牌名称', trigger: 'blur' }],\n        zy: [{ required: true, message: '请选择专业', trigger: 'blur' }],\n        sffx: [{ required: true, message: '请选择是否分相', trigger: 'change' }]\n      },\n      selection: [], //记录最后一次选中的行数据\n      tableDataA: undefined,  //表格A数据\n      tableDataB: undefined,  //表格B数据\n      isShowMpInfo: false,\n      //选中行数据\n      rowData: {},\n      //设备类型编码\n      sblxbm: '',\n      mxData: []//表格明细数据\n    }\n  },\n  watch: {},\n  created() {\n    this.getList()\n\n  },\n  methods: {\n    getData(param){\n      this.getList(param)\n    },\n    handleEvent(var1, var2){\n      this.params = var2\n    },\n    //定义重置方法\n    getReset() {\n      this.params = {}\n      this.getList()\n    },\n    //树节点点击事件\n    handleNodeClick(data) {\n      this.form.sblxbm = data.code\n      this.sblxbm = data.code\n      this.queryParams.sblxbm = data.code\n      if (data.pid != 'sb'){\n        this.queryParams.zy = data.pid.substring(0, 2)\n      }\n      this.getList()\n    },\n    /**\n     * 表格多选框\n     */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.objId)\n      this.selection = selection\n    },\n    //添加按钮\n    addButton() {\n      if (this.form.sblxbm != undefined) {\n        this.isShowDetails = true\n        this.isDisabled = false\n        this.form.objId = undefined\n        this.form.mpmc = undefined\n        this.form.zy = undefined\n        this.title = '新增'\n      } else {\n        this.$message.warning('请选择左侧树节点新增数据！')\n        return\n      }\n    },\n    close() {\n      this.isShowDetails = false\n    },\n    updateDetails(row) {\n      this.title = '修改'\n      this.isShowDetails = true\n      this.form = row\n      this.isDisabled = false\n    },\n    getDetails(row) {\n      this.title = '详情'\n      this.isShowDetails = true\n      this.form = row\n      this.isDisabled = true\n    },\n    save() {\n      this.$refs.form.validate((valid) => {\n        if (valid) {\n          saveOrUpdate(this.form).then(res => {\n            if (res.code == '0000') {\n              this.$message.success('保存成功！')\n              this.isShowDetails = false\n              this.getList()\n            }\n          })\n        } else {\n          this.$message.error('请输入所有必填字段！')\n          return false\n        }\n      })\n    },\n    //删除按钮\n    deleteButton(row) {\n      this.form = row\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        remove([this.form.objId]).then(res => {\n          this.$message({\n            type: 'success',\n            message: '删除成功!'\n          })\n          this.getList()\n        })\n      })\n    },\n    //定义详情按钮\n    viewButton() {\n      if (this.ids.length == 1) {\n        this.isShowDyDetails = true\n        this.dyForm.obj_id = this.ids[0]\n        this.dyForm.a_hs = this.selection[0].a_hs\n        this.dyForm.a_hsOld = this.selection[0].a_hs\n        this.dyForm.a_ls = this.selection[0].a_ls\n        this.dyForm.a_lsOld = this.selection[0].a_ls\n        this.dyForm.b_hs = this.selection[0].b_hs\n        this.dyForm.b_hsOld = this.selection[0].b_hs\n        this.dyForm.b_ls = this.selection[0].b_ls\n        this.dyForm.b_lsOld = this.selection[0].b_ls\n        this.dyForm.title = this.selection[0].mpmc\n        //A表格加载\n        if (this.selection[0].a_hs != '' && this.selection[0].a_ls != '' && this.selection[0].a_hs != 0 && this.selection[0].a_ls != 0) {\n          this.dyForm.lbbs = 'A'\n          getTable(this.dyForm).then(res => {\n            var str = ''\n            for (var i = 0; i < this.selection[0].a_hs; i++) {\n              var temp = '<tr>'\n              for (var k = 0; k < res.data.length; k++) {\n                if (i == res.data[k].rowindex) {\n                  var nrbs = res.data[k].nrbs\n                  var nrlx = res.data[k].nrlx\n                  //定义颜色\n                  var color = ''\n                  if (nrlx == '1' || nrlx == '2' || nrlx == '3' || nrlx == '4') {//白色单元格:动态属性\n                    color = ' bgcolor=\"white\" '\n                  } else if (nrlx == '5') {//灰色单元格:静态文本\n                    color = ' bgcolor=\"#EEEEE0\" '\n                  } else if (nrlx == '' || nrlx == undefined || nrlx == 'undefined') {//浅绿色单元格:还未定义属性\n                    color = ' bgcolor=\"#C7EDCC\" '\n                  }\n                  if (nrbs == '' || nrbs == 'undefined' || nrbs == undefined) {\n                    nrbs = '-'\n                  } else if (nrbs.indexOf('|') == -1 || nrbs == '℃' || nrbs == '/%') {\n\n                  } else if (nrbs.indexOf('|') != -1) {\n                    nrbs = nrbs.replace(/[^\\u4e00-\\u9fa5]/gi, '')\n                  }\n\n                  if (res.data[k].colspan != '1' || res.data[k].rowspan != '1') {\n                    temp += '<td tabindex=\\'-1\\'' + color + 'colspan=\\'' + res.data[k].colspan + '\\' rowspan=\\'' + res.data[k].rowspan + '\\' id=\\'' + res.data[k].obj_id + '\\'>' + nrbs + '</td>'\n                  } else {\n                    temp += '<td tabindex=\\'-1\\'' + color + ' id=\\'' + res.data[k].obj_id + '\\' @click=\\'tdclick(\\'A\\',this)\\'>' + nrbs + '</td>'\n                  }\n                }\n              }\n              temp += '</tr>'\n              str += temp\n            }\n            this.tableDataA = undefined\n            this.tableDataA = str\n          })\n        } else {\n          this.tableDataA = undefined\n        }\n        //B表格加载\n        if (this.selection[0].b_hs != '' && this.selection[0].b_ls != '' && this.selection[0].b_hs != 0 && this.selection[0].b_ls != 0) {\n          this.dyForm.lbbs = 'B'\n          getTable(this.dyForm).then(res => {\n            var str = ''\n            for (var i = 0; i < this.selection[0].b_hs; i++) {\n              var temp = '<tr>'\n              for (var k = 0; k < res.data.length; k++) {\n                if (i == res.data[k].rowindex) {\n                  var nrbs = res.data[k].nrbs\n                  var nrlx = res.data[k].nrlx\n                  //定义颜色\n                  var color = ''\n                  if (nrlx == '1' || nrlx == '2' || nrlx == '3' || nrlx == '4') {//白色单元格:动态属性\n                    color = ' bgcolor=\"white\" '\n                  } else if (nrlx == '5') {//灰色单元格:静态文本\n                    color = ' bgcolor=\"#EEEEE0\" '\n                  } else if (nrlx == '' || nrlx == undefined || nrlx == 'undefined') {//浅绿色单元格:还未定义属性\n                    color = ' bgcolor=\"#C7EDCC\" '\n                  }\n                  if (nrbs == '' || nrbs == 'undefined' || nrbs == undefined) {\n                    nrbs = '-'\n                  } else if (nrbs.indexOf('|') == -1 || nrbs == '℃' || nrbs == '/%') {\n\n                  } else if (nrbs.indexOf('|') != -1) {\n                    nrbs = nrbs.replace(/[^\\u4e00-\\u9fa5]/gi, '')\n                  }\n\n                  if (res.data[k].colspan != '1' || res.data[k].rowspan != '1') {\n                    temp += '<td tabindex=\\'-1\\'' + color + 'colspan=\\'' + res.data[k].colspan + '\\' rowspan=\\'' + res.data[k].rowspan + '\\' id=\\'' + res.data[k].obj_id + '\\'>' + nrbs + '</td>'\n                  } else {\n                    temp += '<td tabindex=\\'-1\\'' + color + ' id=\\'' + res.data[k].obj_id + '\\'>' + nrbs + '</td>'\n                  }\n                }\n              }\n              temp += '</tr>'\n              str += temp\n            }\n            this.tableDataB = undefined\n            this.tableDataB = str\n          })\n        } else {\n          this.tableDataB = undefined\n        }\n      } else {\n        this.$message({\n          type: 'info',\n          message: '请选择一条数据!'\n        })\n      }\n    },\n    //查询列表\n    getList(params) {\n      this.queryParams = {...this.queryParams, ...params};\n      let param = {...this.queryParams,...params}\n      getPageDataList(param).then(res => {\n        this.tableAndPageInfo.tableData = res.data.records\n        this.tableAndPageInfo.pager.total = res.data.total\n\n        this.tableAndPageInfo.tableData.forEach(item => {\n          this.options.forEach(element => {\n            if (item.zy === element.value) {\n              item.zyName = element.label\n            }\n          })\n          this.sffxOptions.forEach(element => {\n            if (item.sffx === element.value) {\n              item.sffxName = element.label\n            }\n          })\n        })\n      })\n    },\n    //懒加载函数\n    loadNode(node, resolve) {\n      let TreeparamMap = {\n        pid: '',\n        spbLogo: ['输电设备', '变电设备','配电设备']\n      }\n      if (node.level === 0) {\n        TreeparamMap.pid = 'sb'\n        return this.getTreeNode(TreeparamMap, resolve)\n      }\n      setTimeout(() => {\n        TreeparamMap.pid = node.data.code\n        this.getTreeNode(TreeparamMap, resolve)\n      }, 500)\n\n    },\n    //获取树节点数据\n    getTreeNode(paramMap, resolve) {\n      getDeviceClassTreeNodeByPid(paramMap).then(res => {\n        let treeNodes = []\n        res.data.forEach(item => {\n          let node = {\n            name: item.name,\n            level: item.level,\n            id: item.id,\n            pid: item.pid,\n            leaf: false,\n            code: item.code\n          }\n          treeNodes.push(node)\n        })\n        resolve(treeNodes)\n      })\n    },\n    //创建A表格按钮功能\n    saveTableA() {\n      //判断填写的行数与列数，值是否符合标准\n      if (this.dyForm.a_hs <= 0 || this.dyForm.a_ls <= 0) {\n        this.$message({\n          type: 'info',\n          message: '不允许填写0或小于0的数!'\n        })\n        return\n      }\n      //判断行和列的变化情况\n      let row_differ = this.dyForm.a_hsOld - this.dyForm.a_hs  //对前后变化的行做差，判断是否增加或减少行\n      let col_differ = this.dyForm.a_lsOld - this.dyForm.a_ls  //对前后变化的行做差，判断是否增加或减少列\n      if (row_differ == 0 && col_differ == 0) {//行列无发生变化，不进行任何操作\n        return\n      } else if (row_differ > 0 || col_differ > 0) {//行或者列减少,提示用户是否删除\n        this.$confirm('确定删除行列?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          //设置类别标识\n          this.dyForm.lbbs = 'A'\n          updateTableNum(this.dyForm).then(res => {\n            if (res.code == '0000') {\n              this.reloadTable('A')\n              this.$message.success('删除成功！')\n              this.dyForm.a_hsOld = this.dyForm.a_hs\n              this.dyForm.a_lsOld = this.dyForm.a_ls\n            }\n          })\n        })\n      } else {\n        //设置类别标识\n        this.dyForm.lbbs = 'A'\n        updateTableNum(this.dyForm).then(res => {\n          if (res.code == '0000') {\n            this.reloadTable('A')\n            this.dyForm.a_hsOld = this.dyForm.a_hs\n            this.dyForm.a_lsOld = this.dyForm.a_ls\n          }\n        })\n      }\n    },\n    //创建B表格按钮功能\n    saveTableB() {\n      //判断填写的行数与列数，值是否符合标准\n      if (this.dyForm.b_hs <= 0 || this.dyForm.b_ls <= 0) {\n        this.$message({\n          type: 'info',\n          message: '不允许填写0或小于0的数!'\n        })\n        return\n      }\n      //判断行和列的变化情况\n      let row_differ = this.dyForm.b_hsOld - this.dyForm.b_hs  //对前后变化的行做差，判断是否增加或减少行\n      let col_differ = this.dyForm.b_lsOld - this.dyForm.b_ls  //对前后变化的行做差，判断是否增加或减少列\n      if (row_differ == 0 && col_differ == 0) {//行列无发生变化，不进行任何操作\n        return\n      } else if (row_differ > 0 || col_differ > 0) {//行或者列减少,提示用户是否删除\n        this.$confirm('确定删除行列?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          //设置类别标识\n          this.dyForm.lbbs = 'B'\n          updateTableNum(this.dyForm).then(res => {\n            if (res.code == '0000') {\n              this.reloadTable('B')\n              this.$message.success('删除成功！')\n              this.dyForm.b_hsOld = this.dyForm.b_hs\n              this.dyForm.b_lsOld = this.dyForm.b_ls\n            }\n          })\n        })\n      } else {\n        //设置类别标识\n        this.dyForm.lbbs = 'B'\n        updateTableNum(this.dyForm).then(res => {\n          if (res.code == '0000') {\n            this.reloadTable('B')\n            this.dyForm.b_hsOld = this.dyForm.b_hs\n            this.dyForm.b_lsOld = this.dyForm.b_ls\n          }\n        })\n      }\n    },\n    //重新加载表格\n    reloadTable(reload_table_flag) {\n      if (reload_table_flag == 'B') {\n        var rowNumB = this.dyForm.b_hs\n        getTable(this.dyForm).then(res => {\n          if (rowNumB != 0) {\n            var str1 = ''\n            for (var i = 0; i < rowNumB; i++) {\n              var temp = '<tr>'\n              for (var k = 0; k < res.data.length; k++) {\n                if (i == res.data[k].rowindex) {\n                  var nrbs = res.data[k].nrbs\n                  var nrlx = res.data[k].nrlx\n                  var color = ''\n                  if (nrlx == '1' || nrlx == '2' || nrlx == '3' || nrlx == '4') {//白色单元格:动态属性\n                    color = ' bgcolor=\"white\" '\n                  } else if (nrlx == '5') {//灰色单元格:静态文本\n                    color = ' bgcolor=\"#EEEEE0\" '\n                  } else if (nrlx == '' || nrlx == undefined || nrlx == 'undefined') {//浅绿色单元格:还未定义属性\n                    color = ' bgcolor=\"#C7EDCC\" '\n                  }\n                  if (nrbs == '' || nrbs == 'undefined' || nrbs == undefined) {\n                    nrbs = '-'\n                  } else if (nrbs.indexOf('|') == -1 || nrbs == '℃' || nrbs == '/%') {\n\n                  } else if (nrbs.indexOf('|') != -1) {\n                    nrbs = nrbs.replace(/[^\\u4e00-\\u9fa5]/gi, '')\n                  }\n                  if (res.data[k].colspan != '1' || res.data[k].rowspan != '1') {\n                    temp += '<td tabindex=\\'-1\\'' + color + ' colspan=\\'' + res.data[k].colspan + '\\' rowspan=\\'' + res.data[k].rowspan + '\\' id=\\'' + res.data[k].obj_id + '\\'>' + nrbs + '</td>'\n                  } else {\n                    temp += '<td tabindex=\\'-1\\'' + color + 'id=\\'' + res.data[k].obj_id + '\\'>' + nrbs + '</td>'\n                  }\n                }\n              }\n              temp += '</tr>'\n              str1 += temp\n            }\n            this.tableDataB = undefined\n            this.tableDataB = str1\n          }\n        })\n      } else {\n        var rowNumA = this.dyForm.a_hs\n        getTable(this.dyForm).then(res => {\n          if (rowNumA != 0) {\n            var str1 = ''\n            for (var i = 0; i < rowNumA; i++) {\n              var temp = '<tr>'\n              for (var k = 0; k < res.data.length; k++) {\n                if (i == res.data[k].rowindex) {\n                  var nrbs = res.data[k].nrbs\n                  var nrlx = res.data[k].nrlx\n                  var color = ''\n                  if (nrlx == '1' || nrlx == '2' || nrlx == '3' || nrlx == '4') {//白色单元格:动态属性\n                    color = ' bgcolor=\"white\" '\n                  } else if (nrlx == '5') {//灰色单元格:静态文本\n                    color = ' bgcolor=\"#EEEEE0\" '\n                  } else if (nrlx == '' || nrlx == undefined || nrlx == 'undefined') {//浅绿色单元格:还未定义属性\n                    color = ' bgcolor=\"#C7EDCC\" '\n                  }\n                  if (nrbs == '' || nrbs == 'undefined' || nrbs == undefined) {\n                    nrbs = '-'\n                  } else if (nrbs.indexOf('|') == -1 || nrbs == '℃' || nrbs == '/%') {\n\n                  } else if (nrbs.indexOf('|') != -1) {\n                    nrbs = nrbs.replace(/[^\\u4e00-\\u9fa5]/gi, '')\n                  }\n                  if (res.data[k].colspan != '1' || res.data[k].rowspan != '1') {\n                    temp += '<td tabindex=\\'-1\\'' + color + 'colspan=\\'' + res.data[k].colspan + '\\' rowspan=\\'' + res.data[k].rowspan + '\\' id=\\'' + res.data[k].obj_id + '\\'>' + nrbs + '</td>'\n                  } else {\n                    temp += '<td tabindex=\\'-1\\'' + color + 'id=\\'' + res.data[k].obj_id + '\\'>' + nrbs + '</td>'\n                  }\n                }\n              }\n              temp += '</tr>'\n              str1 += temp\n            }\n            this.tableDataA = undefined\n            this.tableDataA = str1\n          }\n        })\n      }\n    },\n    handle(sign, obj) {\n      this.dyForm.lbbs = sign\n\n    },\n    //获取铭牌信息\n    getNameplateInfo(row) {\n      this.rowData = row\n      this.rowData.sblxbm = this.sblxbm\n      let params = JSON.stringify({\n        'obj_id': row.objId,\n        'lbbs': 'A'\n      })\n      getTable(params).then(res => {\n        if (res.code === '0000') {\n          this.mxData = res.data//需要先设置数据再弹框，否则数据传不过去\n          this.isShowMpInfo = true\n        }\n      })\n    },\n    //关闭铭牌内容弹框\n    closeInfoDialog() {\n      this.isShowMpInfo = false\n      //刷新父页面\n      this.reload()\n    },\n    saveSpan() {\n    },\n    reset() {\n    },\n    editCellProperties() {\n    },\n    resetCellProperties() {\n    },\n    //清空表单数据\n    handleClose() {\n      this.form = {}\n      this.$nextTick(() => {\n        this.form = this.$options.data().form\n        this.resetForm('form')\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  max-height: 79vh;\n  overflow: auto;\n}\n</style>\n"]}]}