{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\gfdzcz\\components\\fwzz_fgssh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\gfdzcz\\components\\fwzz_fgssh.vue", "mtime": 1751373004029}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgZ2V0TGlzdEZ3enpqcywKICBzYXZlT3JVcGRhdGVGd3p6anMsCiAgcmVtb3ZlRnd6empzLAogIGV4cG9ydFdvcmQKfSBmcm9tICJAL2FwaS95eGdsL2dmeXhnbC9nZnpiZ2wiOwppbXBvcnQgeyBnZXRCZHpEYXRhTGlzdFNlbGVjdGVkIGFzIGdldEJkelNlbGVjdExpc3R9IGZyb20gIkAvYXBpL2RhZ2FuZ09pbGZpZWxkL2Fzc2V0L2dmc2J0eiI7Ci8v5rWB56iLCmltcG9ydCBhY3Rpdml0aSBmcm9tICJjb20vYWN0aXZpdGkiOwppbXBvcnQgdGltZUxpbmUgZnJvbSAiY29tL3RpbWVMaW5lIjsKaW1wb3J0IHsgSGlzdG9yeUxpc3QgfSBmcm9tICJAL2FwaS9hY3Rpdml0aS9wcm9jZXNzVGFzayI7CmltcG9ydCB7IGdldEZnc09wdGlvbnMgfSBmcm9tICJAL2FwaS95eGdsL2dmeXhnbC9nZnpiZ2wiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogImZ3enpfZmdzc2giLAogIGNvbXBvbmVudHM6IHsgYWN0aXZpdGksIHRpbWVMaW5lIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGJqcjogIiIsCiAgICAgIGN1cnJlbnRVc2VyOiB0aGlzLiRzdG9yZS5nZXR0ZXJzLm5hbWUsCiAgICAgIC8v5bel5L2c5rWB5by556qXCiAgICAgIGlzU2hvdzogZmFsc2UsCiAgICAgIC8v5bel5L2c5rWB5Lyg5YWl5Y+C5pWwCiAgICAgIHByb2Nlc3NEYXRhOiB7CiAgICAgICAgcHJvY2Vzc0RlZmluaXRpb25LZXk6ICJmd3p6anNnaiIsCiAgICAgICAgYnVzaW5lc3NLZXk6ICIiLAogICAgICAgIGJ1c2luZXNzVHlwZTogIumYsuivr+ijhee<PERSON><PERSON><PERSON>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"}, {"version": 3, "sources": ["fwzz_fgssh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgoBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "fwzz_fgssh.vue", "sourceRoot": "src/views/dagangOilfield/czpgl/gfdzcz/components", "sourcesContent": ["<template>\n  <div>\n    <!--搜索条件-->\n    <el-filter\n      ref=\"filter1\"\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n      @handleReset=\"filterReset\"\n      @handleEvent=\"handleEvent\"\n    />\n\n    <!--<el-white class=\"button-group\">\n      <el-button\n        v-show=\"selectNode==='accident' || selectNode==='operations' || selectNode==='groundingFailureLogging' || selectNode==='tripFailureLogging' || selectNode==='DeviceUnlocks' || selectNode==='OnloadPressure'\"\n        type=\"primary\" icon=\"el-icon-plus\" @click=\"addRow\">新增\n      </el-button>-->\n    <el-white class=\"button-group\">\n      <div style=\"height: 50px\">\n        <el-button type=\"success\" icon=\"el-icon-download\" @click=\"exportWord\"\n          >导出</el-button\n        >\n      </div>\n      <div>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"handleSelectionChange\"\n          height=\"58vh\"\n        >\n          <el-table-column\n            slot=\"table_eight\"\n            align=\"center\"\n            fixed=\"right\"\n            style=\"display: block\"\n            label=\"操作\"\n            width=\"160\"\n            :resizable=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                @click=\"getDetails(scope.row)\"\n                title=\"详情\"\n                class=\"el-icon-view\"\n              ></el-button>\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                v-show=\"\n                  scope.row.createBy == currentUser ||\n                    'admin' === $store.getters.name\n                \"\n                @click=\"handleDelete(scope.row)\"\n                class=\"el-icon-delete\"\n              ></el-button>\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                v-show=\"scope.row.status != '待办结'\"\n                @click=\"getTg(scope.row)\"\n                title=\"通过\"\n                class=\"el-icon-circle-check\"\n              ></el-button>\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                v-show=\"\n                  scope.row.status == '待办结' &&\n                    scope.row.createBy == currentUser\n                \"\n                @click=\"getBj(scope.row)\"\n                title=\"办结\"\n                class=\"el-icon-s-promotion\"\n              ></el-button>\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                @click=\"showTimeLine(scope.row)\"\n                title=\"流程查看\"\n                class=\"el-icon-lcck commonIcon\"\n              ></el-button>\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                v-show=\"scope.row.status != '待办结'\"\n                @click=\"showProcessImg(scope.row)\"\n                title=\"流程图\"\n                class=\"el-icon-lct commonIcon\"\n              ></el-button>\n            </template>\n          </el-table-column>\n        </comp-table>\n      </div>\n    </el-white>\n\n    <!--光伏分公司审批弹框-->\n    <el-dialog\n      :title=\"titles\"\n      :visible.sync=\"isShowBdfgssh\"\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"130px\" ref=\"form\" :model=\"form\">\n        <!--防误装置解锁工具使用审批-->\n        <el-row>\n          <el-col :span=\"8\">\n            <el-form-item label=\"状态\" prop=\"status\">\n              <el-input\n                style=\"width:100%\"\n                :disabled=\"true\"\n                v-model=\"form.status\"\n              >\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"分公司\" prop=\"fgs\">\n              <el-select\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.fgs\"\n                placeholder=\"请选择分公司\"\n                @change=\"fgsChangeFun\"\n              >\n                <el-option\n                  v-for=\"item in fgsList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"光伏站\" prop=\"bdz\">\n              <el-select\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.bdz\"\n                placeholder=\"请选择光伏站\"\n              >\n                <el-option\n                  v-for=\"item in bdzList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"时间\" prop=\"sj\">\n              <el-date-picker\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.sj\"\n                type=\"datetime\"\n                placeholder=\"选择日期时间\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"设备名称\" prop=\"sbmc\">\n              <el-select\n                filterable\n                allow-create\n                default-first-option\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.sbmc\"\n                placeholder=\"请选择或输入设备\"\n              >\n                <el-option value=\"变压器\" label=\"变压器\"></el-option>\n                <el-option value=\"隔离开关\" label=\"隔离开关\"></el-option>\n                <el-option value=\"隔离刀闸\" label=\"隔离倒闸\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"工作任务\" prop=\"gzrw\">\n              <el-input\n                style=\"width:100%\"\n                type=\"textarea\"\n                :rows=\"2\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.gzrw\"\n                placeholder=\"请输入内容\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"使用原因\" prop=\"syyy\">\n              <el-input\n                style=\"width:100%\"\n                type=\"textarea\"\n                :rows=\"2\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.syyy\"\n                placeholder=\"请输入内容\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"记录人\" prop=\"jlr\">\n              <el-select\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.jlr\"\n                clearable\n                placeholder=\"请选择记录人\"\n              >\n                <el-option\n                  v-for=\"item in jlrList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"记录时间\" prop=\"jlsj\">\n              <el-date-picker\n                v-model=\"form.jlsj\"\n                :disabled=\"isDisabled\"\n                type=\"datetime\"\n                style=\"width:100%\"\n                placeholder=\"选择日期时间\"\n                default-time=\"12:00:00\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"分公司五防专责\" prop=\"wfzz1\">\n              <el-input\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.wfzz1\"\n                placeholder=\"请输入内容\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"审核意见\" prop=\"shyj1\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"2\"\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.shyj1\"\n                placeholder=\"请输入审核意见\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"分公司主管领导\" prop=\"zgld1\">\n              <el-input\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.zgld1\"\n                placeholder=\"请输入内容\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"审核意见\" prop=\"shyj2\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"2\"\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.shyj2\"\n                placeholder=\"请输入审核意见\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"生产科五防专责\" prop=\"wfzz2\">\n              <el-select\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.wfzz2\"\n                placeholder=\"请输入内容\"\n              >\n                <el-option label=\"王涛\" value=\"王涛\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"审核意见\" prop=\"shyj3\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"2\"\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.shyj3\"\n                placeholder=\"请输入审核意见\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"生产科主管领导\" prop=\"zgld2\">\n              <el-select\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.zgld2\"\n                placeholder=\"请输入内容\"\n              >\n                <el-option label=\"郑双健\" value=\"郑双健\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"审核意见\" prop=\"shyj4\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"2\"\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.shyj4\"\n                placeholder=\"请输入审核意见\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"监护人\" prop=\"jhr\">\n              <el-input\n                style=\"width:100%\"\n                :disabled=\"isDisabledBj\"\n                v-model=\"form.jhr\"\n                placeholder=\"请输入内容\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"操作人\" prop=\"czr\">\n              <el-input\n                style=\"width:100%\"\n                :disabled=\"isDisabledBj\"\n                v-model=\"form.czr\"\n                placeholder=\"请输入内容\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"操作解锁/事故解锁\" prop=\"czsgjs\">\n              <el-select\n                style=\"width:100%\"\n                :disabled=\"isDisabledBj\"\n                v-model=\"form.czsgjs\"\n                placeholder=\"请输入内容\"\n              >\n                <el-option label=\"操作解锁\" value=\"操作解锁\"></el-option>\n                <el-option label=\"事故解锁\" value=\"事故解锁\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"handleClose\">取 消</el-button>\n        <el-button\n          v-show=\"titles == '办结'\"\n          type=\"primary\"\n          @click=\"submitFormFwzzjs\"\n          >确 定\n        </el-button>\n        <el-button\n          v-show=\"titles != '办结'\"\n          type=\"primary\"\n          @click=\"handleTh('rollback')\"\n          >回 退</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <!--光伏分公司审批 回退 弹框-->\n    <el-dialog\n      :title=\"titles\"\n      :visible.sync=\"isShowHt\"\n      width=\"40%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"100px\" ref=\"form\" :model=\"form\">\n        <!--防误装置解锁工具使用审批-->\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"意见：\" prop=\"bz\">\n              <el-input\n                style=\"width:100%\"\n                type=\"textarea\"\n                :rows=\"2\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.bz\"\n                placeholder=\"请输入审核意见\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"handleClose\">取 消</el-button>\n        <el-button v-if=\"titles == '回退'\" type=\"primary\" @click=\"submitFormHt\"\n          >回 退\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!--光伏分公司审批 通过 分公司五防专责 弹框-->\n    <el-dialog\n      :title=\"titles\"\n      :visible.sync=\"isShowFgswf\"\n      width=\"30%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"130px\" ref=\"form\" :model=\"form\">\n        <!--防误装置解锁工具使用审批-->\n        <div>\n          <el-row>\n            <el-col :span=\"24\">\n              <el-form-item label=\"分公司五防专责：\" prop=\"wfzz1\">\n                <el-input\n                  style=\"width:100%\"\n                  :disabled=\"isDisabled\"\n                  v-model=\"form.wfzz1\"\n                  placeholder=\"请输入分公司五防专责人员\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <el-col :span=\"24\">\n              <el-form-item label=\"审核意见：\" prop=\"shyj1\">\n                <el-input\n                  type=\"textarea\"\n                  :rows=\"2\"\n                  style=\"width:100%\"\n                  :disabled=\"isDisabled\"\n                  v-model=\"form.shyj1\"\n                  placeholder=\"请输入审核意见\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <div align=\"right\" slot=\"footer\">\n            <el-button @click=\"handleClose\">取 消</el-button>\n            <el-button\n              v-if=\"titles == '待分公司五防专责审核'\"\n              type=\"primary\"\n              @click=\"submitFormSh1\"\n              >提 交\n            </el-button>\n          </div>\n        </div>\n      </el-form>\n    </el-dialog>\n\n    <!--光伏分公司审批 通过 分公司主管领导 弹框-->\n    <el-dialog\n      :title=\"titles\"\n      :visible.sync=\"isShowTg1\"\n      width=\"30%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"130px\" ref=\"form\" :model=\"form\">\n        <!--防误装置解锁工具使用审批-->\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"分公司主管领导：\" prop=\"zgld1\">\n              <el-input\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.zgld1\"\n                placeholder=\"请输入分公司主管领导\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"审核意见：\" prop=\"shyj2\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"2\"\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.shyj2\"\n                placeholder=\"请输入审核意见\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"handleClose\">取 消</el-button>\n        <el-button\n          v-if=\"titles == '待分公司主管领导审核'\"\n          type=\"primary\"\n          @click=\"submitFormTg\"\n          >提 交\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!--光伏分公司审批 通过 生产科五防专责 弹框-->\n    <el-dialog\n      :title=\"titles\"\n      :visible.sync=\"isShowTg2\"\n      width=\"30%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"130px\" ref=\"form\" :model=\"form\">\n        <!--防误装置解锁工具使用审批-->\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"生产科五防专责：\" prop=\"wfzz2\">\n              <el-input\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.wfzz2\"\n                placeholder=\"请输入生产科五防专责人员\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"审核意见：\" prop=\"shyj3\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"2\"\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.shyj3\"\n                placeholder=\"请输入审核意见\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"handleClose\">取 消</el-button>\n        <el-button\n          v-if=\"titles == '待生产科五防专责审核'\"\n          type=\"primary\"\n          @click=\"submitFormTg2\"\n          >提 交\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!--光伏分公司审批 通过 生产科主管领导 弹框-->\n    <el-dialog\n      :title=\"titles\"\n      :visible.sync=\"isShowTg3\"\n      width=\"30%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"130px\" ref=\"form\" :model=\"form\">\n        <!--防误装置解锁工具使用审批-->\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"生产科主管领导：\" prop=\"zgld2\">\n              <el-input\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.zgld2\"\n                placeholder=\"请输入生产科主管领导\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"审核意见：\" prop=\"shyj4\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"2\"\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.shyj4\"\n                placeholder=\"请输入审核意见\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"handleClose\">取 消</el-button>\n        <el-button\n          v-if=\"titles == '待生产科主管领导审核'\"\n          type=\"primary\"\n          @click=\"submitFormTg3\"\n          >提 交</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <!--工作流需要-->\n    <activiti\n      :processData=\"processData\"\n      :isShow=\"isShow\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n    ></activiti>\n    <time-line\n      :value=\"timeLineShow\"\n      :timeData=\"timeData\"\n      @closeTimeLine=\"colseTimeLine\"\n    />\n    <!--流程图查看-->\n    <el-dialog\n      title=\"审批流程图\"\n      :visible.sync=\"openLoadingImg\"\n      width=\"56%\"\n      append-to-body\n      :close-on-click-modal=\"false\"\n    >\n      <div>\n        <img :src=\"imgSrc\" style=\"width: 100%\" />\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  getListFwzzjs,\n  saveOrUpdateFwzzjs,\n  removeFwzzjs,\n  exportWord\n} from \"@/api/yxgl/gfyxgl/gfzbgl\";\nimport { getBdzDataListSelected as getBdzSelectList} from \"@/api/dagangOilfield/asset/gfsbtz\";\n//流程\nimport activiti from \"com/activiti\";\nimport timeLine from \"com/timeLine\";\nimport { HistoryList } from \"@/api/activiti/processTask\";\nimport { getFgsOptions } from \"@/api/yxgl/gfyxgl/gfzbgl\";\nexport default {\n  name: \"fwzz_fgssh\",\n  components: { activiti, timeLine },\n  data() {\n    return {\n      bjr: \"\",\n      currentUser: this.$store.getters.name,\n      //工作流弹窗\n      isShow: false,\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"fwzzjsgj\",\n        businessKey: \"\",\n        businessType: \"防误装置解锁工具使用\",\n        variables: {},\n        defaultFrom: true,\n        nextUser: \"\",\n        processType: \"complete\"\n      },\n      //流程图查看\n      openLoadingImg: false,\n      imgSrc: \"\", //流程图查看地址\n      timeData: [],\n      timeLineShow: false,\n      //弹出框标题\n      activitiOption: { title: \"上报\" },\n      isShowYj: true,\n      selectedSbParam: \"all\",\n      isShowSysbDialog: false,\n      bdzList: [],\n      isDisabled: false,\n      isShowHt: false,\n      isShowFgswf: false,\n      isShowTg1: false,\n      isShowTg2: false,\n      isShowTg3: false,\n      isShowTg4: false,\n      isDisabledBj: false,\n      selectNode: \"\",\n      fgsList: [],\n      form: {\n        lx: 2,\n        status: \"\",\n        wfzz1: \"\",\n        zgld1: \"\",\n        wfzz2: \"\",\n        zgld2: \"\"\n      },\n      // 多选框选中的id\n      ids: [],\n      // 多选框选选中的数据\n      selectData: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 对话框标题\n      title: \"\",\n      titles: \"\",\n      isShowBdfgssh: false,\n      filterInfo: {},\n      tableAndPageInfo: {},\n      /**\n       *  防误装置解锁工具使用登记\n       *  */\n      filterInfo1: {\n        data: {\n          dlqbh: \"\",\n          sjArr: [],\n          djr: \"\"\n        },\n        fieldList: [\n          {\n            label: \"分公司\",\n            type: \"select\",\n            value: \"fgs\",\n            checkboxValue: [],\n            options: []\n          },\n          { label: \"光伏电站\", type: \"select\", value: \"bdz\", options: [] },\n          {\n            label: \"时间\",\n            value: \"sjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"设备名称\", type: \"input\", value: \"sbmc\" },\n          {\n            label: \"状态\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"status\",\n            clearable: true,\n            options: [\n              { label: \"待分公司五防专责审核\", value: \"待分公司五防专责审核\" },\n              { label: \"待分公司主管领导审核\", value: \"待分公司主管领导审核\" },\n              { label: \"待生产科五防专责审核\", value: \"待生产科五防专责审核\" },\n              { label: \"待生产科主管领导审核\", value: \"待生产科主管领导审核\" },\n              { label: \"待办结\", value: \"待办结\" }\n            ]\n          }\n          /*{ label: '分公司五防专责', type: 'input', value: 'wfzz1' },\n            { label: '分公司主管领导', type: 'input', value: 'zgld1' },\n            { label: '生产科五防专责', type: 'input', value: 'wfzz2' },\n            { label: '生产科主管领导', type: 'input', value: 'zgld2' }*/\n        ]\n      },\n      tableAndPageInfo1: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"status\", label: \"状态\", minWidth: \"100\" },\n          { prop: \"fgsCn\", label: \"分公司\", minWidth: \"100\" },\n          { prop: \"bdzmc\", label: \"光伏电站\", minWidth: \"120\" },\n          { prop: \"sj\", label: \"时间\", minWidth: \"160\" },\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"120\" }\n          /*{ prop: 'wfzz1', label: '分公司五防专责', minWidth: '120' },\n            { prop: 'zgld1', label: '分公司主管领导', minWidth: '120' },\n            { prop: 'wfzz2', label: '生产科五防专责', minWidth: '120' },\n            { prop: 'zgld2', label: '生产科主管领导', minWidth: '120' },*/\n          /*{\n              prop: 'operation',\n              label: '操作',\n              fixed: 'right',\n              minWidth: '130px',\n              style: { display: 'block' },\n              operation: [\n                { name: '详情', clickFun: this.getDetails },\n                { name: '回退', clickFun: this.getHt },\n                { name: '通过', clickFun: this.getTg }\n                /!*{ name: '附件查看', clickFun: this.FjInfoList },*!/\n              ]\n            }*/\n        ]\n      },\n      params: {\n        lx: 2\n      },\n      jlrList: [] //记录人下拉框\n    };\n  },\n  created() {\n    this.filterInfo = this.filterInfo1;\n    this.tableAndPageInfo = this.tableAndPageInfo1;\n    //列表查询\n    this.getData();\n    //获取光伏站下拉框数据\n    // this.getBdzSelectList()\n    //获取分公司下拉框\n    this.getFgsList();\n  },\n  methods: {\n    //下拉框change事件\n    handleEvent(val) {\n      //光伏站查询下拉框选项根据所选分公司带出来\n      if (val.label === \"fgs\" && val.value && val.value !== \"\") {\n        let form = {\n          ssdwbm: val.value\n        };\n        getBdzSelectList(form).then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value === \"bdz\") {\n              return (item.options = res.data);\n            }\n          });\n        });\n      }\n    },\n    //获取分公司下拉框\n    getFgsList() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.fgsList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"fgs\") {\n            return (item.options = this.fgsList);\n          }\n        });\n      });\n    },\n    /**\n     * 办结\n     * */\n    getBj(row) {\n      this.titles = \"办结\";\n      this.form = { ...row };\n      this.isShowBdfgssh = true;\n      this.isDisabled = true;\n      this.isDisabledBj = false;\n    },\n    //关闭弹窗\n    closeActiviti() {\n      this.isShow = false;\n    },\n    //回退按钮\n    async handleTh(type) {\n      this.getSbFsBj({ type: type, data: this.form }, { defaultForm: false });\n      /*try {\n          if (this.form.status == '待分公司五防专责审核') {\n            this.form.status = '待上报'\n            this.form.lx = 1\n          } else if (this.form.status == '待分公司主管领导审核') {\n            this.form.status = '待分公司五防专责审核'\n          } else if (this.form.status == '待生产科五防专责审核') {\n            this.form.status = '待分公司主管领导审核'\n          } else if (this.form.status == '待生产科主管领导审核') {\n            this.form.status = '待生产科五防专责审核'\n          }\n          let { code } = await saveOrUpdateFwzzjs(this.form)\n          if (code === '0000') {\n            this.$message.success('操作成功')\n          }\n        } catch (e) {\n          console.log(e)\n        }\n        this.getData()\n        this.isShowBdfgssh = false*/\n    },\n    //关闭流程查看页面\n    colseTimeLine() {\n      this.timeLineShow = false;\n    },\n    async showTimeLine(row) {\n      this.processData.businessKey = row.objId;\n      let { code, data } = await HistoryList(this.processData);\n      this.timeData = data;\n      this.timeLineShow = true;\n    },\n    //流程图片查看\n    showProcessImg(row) {\n      console.log(row);\n      this.openLoadingImg = true;\n      this.imgSrc =\n        \"/activiti-api/process/read-resource?processDefinitionKey=fwzzjsgj&businessKey=\" +\n        row.objId +\n        \"&t=\" +\n        new Date().getTime();\n    },\n    //工作流回传数据\n    async todoResult(data) {\n      console.log(\"工作流回传数据data:\", data);\n      switch (data.activeTaskName) {\n        case \"防误装置信息填报\":\n          this.form.status = \"待修改\";\n          break;\n        case \"分公司五防专责审核\":\n          this.form.status = \"待修改\";\n          break;\n        case \"分公司主管领导审核\":\n          this.form.status = \"待分公司主管领导审核\";\n          break;\n        case \"生产科五防专责审核\":\n          this.form.status = \"待生产科五防专责审核\";\n          break;\n        case \"生产科主管领导审核\":\n          this.form.status = \"待生产科主管领导审核\";\n          break;\n        case \"结束\":\n          this.form.status = \"待办结\";\n      }\n      let row = {};\n      if (this.form.status == \"待修改\") {\n        row = {\n          objId: data.businessKey,\n          status: this.form.status,\n          lx: 1\n        };\n      } else if (this.form.status == \"待分公司主管领导审核\") {\n        row = {\n          objId: data.businessKey,\n          status: this.form.status,\n          lx: 2\n        };\n      } else if (this.form.status == \"待生产科五防专责审核\") {\n        row = {\n          objId: data.businessKey,\n          status: this.form.status,\n          lx: 2\n        };\n      } else if (this.form.status == \"待生产科主管领导审核\") {\n        row = {\n          objId: data.businessKey,\n          status: this.form.status,\n          lx: 2\n        };\n      } else if (this.form.status == \"待办结\") {\n        row = {\n          objId: data.businessKey,\n          status: this.form.status,\n          lx: 2\n        };\n      }\n      saveOrUpdateFwzzjs(row).then(res => {\n        console.log(\"res数据\", res);\n        if (res.code === \"0000\") {\n          this.$message.success(\"操作成功\");\n          //重置page页从1开始\n          this.tableAndPageInfo.pager.pageResize = \"Y\";\n          this.getData();\n          this.resetForm();\n        }\n      });\n    },\n    /**\n     * 根据表格名称获取对应的数据\n     */\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        const { data, code } = await getListFwzzjs(param);\n        console.log(\"防误装置解锁工具使用登记\");\n        console.log(data);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    /**\n     * 新增\n     */\n    addRow() {\n      this.titles = \"\";\n      this.isShowBdfgssh = true;\n    },\n    /**\n     * 回退\n     */\n    getHt(row) {\n      this.titles = \"回退\";\n      this.isShowHt = true;\n      this.form = { ...row };\n      this.isDisabled = false;\n    },\n    /**\n     * 通过1\n     */\n    getTg(row) {\n      this.form = { ...row };\n      this.isDisabled = false;\n      console.log(this.form);\n      if (this.form.status == \"待分公司五防专责审核\") {\n        this.titles = \"待分公司五防专责审核\";\n        this.isShowFgswf = true;\n        return;\n      } else if (this.form.status == \"待分公司主管领导审核\") {\n        this.titles = \"待分公司主管领导审核\";\n        this.isShowTg1 = true;\n        return;\n      } else if (this.form.status == \"待生产科五防专责审核\") {\n        this.titles = \"待生产科五防专责审核\";\n        this.isShowTg2 = true;\n        return;\n      } else if (this.form.status == \"待生产科主管领导审核\") {\n        this.titles = \"待生产科主管领导审核\";\n        this.isShowTg3 = true;\n        return;\n      } else if (\n        this.form.wfzz1 != null &&\n        this.form.zgld1 != null &&\n        this.form.wfzz2 != null &&\n        this.form.zgld2 != null\n      ) {\n        this.submitFormTg4();\n        return;\n      }\n    },\n    //上报发送办结  发送给分公司主管领导审核\n    getSbFsBj(args, isShow) {\n      let row = { ...args.data };\n      if (args.type === \"complete\") {\n        this.processData.defaultFrom = isShow.defaultForm; //是否显示选人员\n        this.processData.processType = \"complete\";\n        this.processData.jxgs = isShow.jxgs;\n        this.processData.businessKey = row.objId;\n        this.processData.variables.pass = true;\n      } else if (args.type === \"completeByGroup\") {\n        if (this.form.status == \"待分公司五防专责审核\") {\n          this.activitiOption.title = \"发送给分公司主管领导审核\";\n        } else if (this.form.status == \"待分公司主管领导审核\") {\n          this.activitiOption.title = \"发送给生产科五防专责审核\";\n        } else if (this.form.status == \"待生产科五防专责审核\") {\n          this.activitiOption.title = \"发送给生产科主管领导审核\";\n        } else if (this.form.status == \"待生产科主管领导审核\") {\n          this.activitiOption.title = \"审核完毕\";\n        }\n        this.processData.defaultFrom = isShow.defaultForm; //是否显示选人员\n        this.processData.processType = \"completeByGroup\";\n        this.processData.jxgs = isShow.jxgs;\n        this.processData.businessKey = row.objId;\n        this.processData.variables.pass = true;\n        this.processData.variables.personGroupId = isShow.personGroupId;\n      } else {\n        this.activitiOption.title = \"回退原因提报\";\n        this.processData.defaultFrom = true;\n        this.processData.processType = \"rollback\";\n        this.processData.businessKey = row.objId;\n        this.processData.variables.pass = true;\n      }\n      this.isShow = true;\n    },\n    /**\n     * 详情\n     */\n    getDetails(row) {\n      this.titles = \"详情查看\";\n      this.isDisabled = true;\n      this.isDisabledBj = true;\n      this.isShowBdfgssh = true;\n      this.form = { ...row };\n      this.bdzList = [{ label: row.bdzmc, value: row.bdz }];\n      this.jlrList = [{ label: row.jlrCn, value: row.jlr }];\n    },\n    /**\n     * 提交 防误装置解锁工具使用登记 回退 表单\n     */\n    async submitFormHt() {\n      try {\n        this.form.lx = 1;\n        this.form.status = \"待修改\";\n        let { code } = await saveOrUpdateFwzzjs(this.form);\n        if (code === \"0000\") {\n          this.$message.success(\"操作成功\");\n          await this.getData();\n        }\n      } catch (e) {\n        console.log(e);\n      }\n      this.isShowHt = false;\n    },\n    /**\n     * 提交 防误装置解锁工具使用登记 通过-分公司五防专责 表单\n     */\n    async submitFormSh1() {\n      this.processData.defaultFrom = true;\n      this.processData.jxgs = false;\n      try {\n        let { code, data } = await saveOrUpdateFwzzjs(this.form);\n        if (code === \"0000\") {\n          this.$message.success(\"操作成功\");\n          this.getSbFsBj(\n            { data: data, type: \"completeByGroup\" },\n            {\n              defaultForm: this.processData.defaultFrom,\n              jxgs: this.processData.jxgs,\n              personGroupId: 19\n            }\n          );\n        }\n      } catch (e) {\n        console.log(e);\n      }\n      this.getData();\n      this.isShowFgswf = false;\n    },\n    /**\n     * 提交 防误装置解锁工具使用登记 通过-分公司主管领导 表单\n     */\n    async submitFormTg() {\n      this.processData.defaultFrom = true;\n      this.processData.jxgs = false;\n      try {\n        /*this.form.lx = 2\n          this.form.status = '待生产科五防专责审核'*/\n        let { code, data } = await saveOrUpdateFwzzjs(this.form);\n        if (code === \"0000\") {\n          this.$message.success(\"操作成功\");\n          this.getSbFsBj(\n            { data: data, type: \"completeByGroup\" },\n            {\n              defaultForm: this.processData.defaultFrom,\n              jxgs: this.processData.jxgs,\n              personGroupId: 20\n            }\n          );\n        }\n      } catch (e) {\n        console.log(e);\n      }\n      this.getData();\n      this.isShowTg1 = false;\n    },\n    /**\n     * 提交 防误装置解锁工具使用登记 通过-生产科五防专责 表单\n     */\n    async submitFormTg2() {\n      this.processData.defaultFrom = true;\n      this.processData.jxgs = false;\n      try {\n        /*this.form.lx = 2\n          this.form.status = '待生产科主管领导审核'*/\n        let { code, data } = await saveOrUpdateFwzzjs(this.form);\n        if (code === \"0000\") {\n          this.$message.success(\"操作成功\");\n          this.getSbFsBj(\n            { data: data, type: \"completeByGroup\" },\n            {\n              defaultForm: this.processData.defaultFrom,\n              jxgs: this.processData.jxgs,\n              personGroupId: 21\n            }\n          );\n        }\n      } catch (e) {\n        console.log(e);\n      }\n      this.getData();\n      this.isShowTg2 = false;\n    },\n    /**\n     * 提交 防误装置解锁工具使用登记 通过-生产科主管领导 表单\n     */\n    async submitFormTg3() {\n      this.processData.defaultFrom = false;\n      this.processData.jxgs = false;\n      try {\n        let { code, data } = await saveOrUpdateFwzzjs(this.form);\n        if (code === \"0000\") {\n          this.$message.success(\"操作成功\");\n          this.getSbFsBj(\n            { data: data, type: \"completeByGroup\" },\n            {\n              defaultForm: this.processData.defaultFrom,\n              jxgs: this.processData.jxgs,\n              personGroupId: 0\n            }\n          );\n          await this.getData();\n        }\n      } catch (e) {\n        console.log(e);\n      }\n      this.isShowTg3 = false;\n    },\n    /**\n     * 提交 防误装置解锁工具使用登记 通过-全部 表单\n     */\n    async submitFormTg4() {\n      this.$confirm(\"确认审核通过该数据吗?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          this.form.lx = 3;\n          this.form.status = \"待办结\";\n          saveOrUpdateFwzzjs(this.form).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"审核通过!\"\n              });\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"审核不通过!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消审核\"\n          });\n        });\n      /*try {\n          this.form.lx = 3\n          this.form.status = '待办结'\n          let { code } = await saveOrUpdateFwzzjs(this.form)\n          if (code === '0000') {\n            this.$message.success('操作成功')\n            await this.getData()\n          }\n        } catch (e) {\n          console.log(e)\n        }\n        this.isShowTg3 = false*/\n    },\n\n    /**\n     * 删除按钮\n     */\n    async handleDelete(row) {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          //防误装置解锁工具使用登记\n          removeFwzzjs(JSON.stringify(row.objId)).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    fgsChangeFun() {},\n    //导出word\n    exportWord() {\n      if (!this.selectData.length > 0) {\n        this.$message.warning(\"请先选中要导出的数据\");\n        return;\n      }\n      try {\n        let fileName = \"防误装置解锁工具使用记录\";\n        let exportUrl = \"yxFwzzjsgjsyjl\";\n        let params = {\n          data: this.selectData,\n          url: exportUrl\n        };\n        exportWord(params, fileName);\n      } catch (e) {\n        this.$message.error(\"导出失败！\");\n      }\n    },\n    handleClose() {\n      this.isShowBdfgssh = false;\n      this.isShowFgswf = false;\n      this.isShowHt = false;\n      this.isShowTg1 = false;\n      this.isShowTg2 = false;\n      this.isShowTg3 = false;\n      this.isShowTg4 = false;\n    },\n    async submitFormFwzzjs() {\n      try {\n        this.form.status = \"已办结\";\n        this.form.lx = 4;\n        let { code, data } = await saveOrUpdateFwzzjs(this.form);\n        if (code === \"0000\") {\n          this.$message.success(\"操作成功\");\n        }\n      } catch (e) {\n        console.log(e);\n      }\n      this.getData();\n      this.isShowBdfgssh = false;\n    },\n    /**\n     * 多选款选中数据\n     * @param row\n     */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.objId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n      this.selectData = selection;\n    },\n    filterReset() {\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n      this.params = {\n        lx: 2\n      };\n    },\n    /**\n     * 获取光伏站下拉框数据\n     */\n    getBdzSelectList() {\n      getBdzSelectList({}).then(res => {\n        this.bdzList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"bdz\") {\n            return (item.options = this.bdzList);\n          }\n        });\n      });\n    }\n  }\n};\n</script>\n\n<style scoped></style>\n"]}]}