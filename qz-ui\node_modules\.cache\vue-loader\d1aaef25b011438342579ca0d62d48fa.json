{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\sybglr.vue?vue&type=style&index=0&id=3a376a48&scoped=true&lang=scss&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\sybglr.vue", "mtime": 1706897323683}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouYnV0dG9uLWdyb3VwIHsKICBwYWRkaW5nLWxlZnQ6IDMwcHg7CiAgcGFkZGluZy1yaWdodDogMzBweDsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7Cn0KCi8vICNoMl90YWJsZSwKLy8gI2gzX3RhYmxlIHsKLy8gICBib3JkZXItYm90dG9tOiAxcHggc29saWQgIzAwMDsKLy8gICB0ciB7Ci8vICAgICAvL3RkOm50aC1jaGlsZCgxKXsKLy8gICAgIC8vICBib3JkZXItbGVmdDpub25lOwovLyAgICAgLy99Ci8vICAgICB0ZCB7Ci8vICAgICAgIGJvcmRlci1sZWZ0OiAxcHggc29saWQgIzAwMDsKLy8gICAgICAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICMwMDAKLy8gICAgIH0KLy8gICB9Ci8vICAgdGV4dC1hbGlnbjogY2VudGVyOwovLyB9CgovLyAjaDFfdGFibGUgewovLyAgIHdpZHRoOiAxMDAlOwovLyAgIHRleHQtYWxpZ246IGNlbnRlcjsKLy8gICAvL2JvcmRlci1yaWdodDoxcHggc29saWQgIzAwMDsKLy8gICBib3JkZXItYm90dG9tOiAxcHggc29saWQgIzAwMDsKCi8vICAgdHIgewovLyAgICAgbGluZS1oZWlnaHQ6IDM1cHg7CgovLyAgICAgLy90ZDpudGgtY2hpbGQoMSl7Ci8vICAgICAvLyAgYm9yZGVyLWxlZnQ6bm9uZTsKLy8gICAgIC8vfQovLyAgICAgdGQgewovLyAgICAgICBib3JkZXItbGVmdDogMXB4IHNvbGlkICMwMDA7Ci8vICAgICAgIGJvcmRlci10b3A6IDFweCBzb2xpZCAjMDAwCi8vICAgICB9Ci8vICAgfQovLyB9CgovLyAjc2F2ZUNvbnQgewovLyAgIGJvcmRlcjogMXB4IHNvbGlkICMwMDA7Ci8vICAgd2lkdGg6IDEwMCU7Ci8vICAgaGVpZ2h0OiA2MHZoOwovLyAgIG92ZXJmbG93OiBhdXRvOwovLyB9CgovLyAucHJpbnRUaXRsZSB7Ci8vICAgbGluZS1oZWlnaHQ6IDM1cHg7Ci8vIH0KCi8vIC9kZWVwLyAjaDJfdGFibGUgdHIsCi8vIC9kZWVwLyAjaDNfdGFibGUgdHIgewovLyAgIGhlaWdodDogMzVweDsKLy8gfQoKLy8gL2RlZXAvICNoMl90YWJsZSB0ZCwKLy8gL2RlZXAvICNoM190YWJsZSB0ZCB7Ci8vICAgYm9yZGVyOiAxcHggc29saWQgIzAwMAovLyB9CgovLyAvZGVlcC8gI2gyX3RhYmxlIGlucHV0LAovLyAvZGVlcC8gI2gzX3RhYmxlIGlucHV0IHsKLy8gICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7Ci8vICAgaGVpZ2h0OiAzNXB4OwovLyB9Cg=="}, {"version": 3, "sources": ["sybglr.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmjEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "sybglr.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- id=\"app\" -->\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n      @handleReset=\"getReset\"\n      @handleEvent=\"handleEvent\"\n    />\n    <el-white class=\"button-group1\">\n      <div class=\"button_btn\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-plue\"\n          @click=\"getInsert\"\n          v-hasPermi=\"['sybglr:button:add']\"\n          >新增\n        </el-button>\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-plue\"\n          @click=\"getInsertls\"\n          v-hasPermi=\"['sybglrls:button:add']\"\n          >历史数据新增\n        </el-button>\n        <el-button type=\"primary\" icon=\"el-icon-download\" @click=\"dcpdf\"\n          >批量导出</el-button\n        >\n        <!-- <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"deleteRow\">删除</el-button> -->\n      </div>\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"handelSelectChange\"\n        height=\"63vh\"\n        ref=\"sybglr\"\n      >\n        <el-table-column\n          slot=\"table_eight\"\n          align=\"center\"\n          fixed=\"right\"\n          style=\"display: block\"\n          label=\"操作\"\n          width=\"200\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <!--    公共按钮      -->\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              @click=\"getDetailsInfo(scope.row)\"\n              title=\"详情\"\n              class=\"el-icon-view\"\n            >\n            </el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"\n                (scope.row.ishg == 1 || scope.row.ishg == 6) &&\n                  scope.row.createBy == currUser\n              \"\n              @click=\"updateDetails(scope.row)\"\n              title=\"编辑\"\n              class=\"el-icon-edit\"\n            ></el-button>\n             <el-button\n              type=\"text\"\n              size=\"small\"\n              v-hasPermi=\"['sybglrls:button:update']\"\n              @click=\"updateDetails(scope.row)\"\n              title=\"编辑\"\n              class=\"el-icon-edit\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"scope.row.ishg != 1 && scope.row.ishg != 6\"\n              @click=\"showTimeLine(scope.row)\"\n              title=\"流程查看\"\n              class=\"el-icon-lcck commonIcon\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"scope.row.ishg != 1 && scope.row.ishg != 6\"\n              @click=\"showProcessImg(scope.row)\"\n              title=\"流程图\"\n              class=\"el-icon-lct commonIcon\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"scope.row.createBy == currUser && scope.row.ishg == 6\"\n              @click=\"saveSybgInfo(scope.row)\"\n              title=\"编辑报告\"\n              class=\"el-icon-edit-outline\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              @click=\"SybgInfo(scope.row)\"\n              title=\"查看报告\"\n              class=\"el-icon-tickets\"\n            >\n            </el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"scope.row.ishg == 1 && showButton\"\n              @click=\"saveSybgInfo(scope.row)\"\n              title=\"编辑报告\"\n              class=\"el-icon-edit-outline\"\n            ></el-button>\n            <el-button\n              @click=\"getZxmmpInfo(scope.row)\"\n              type=\"text\"\n              size=\"small\"\n              title=\"数据对比\"\n              class=\"el-icon-edit-outline\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"\n                ((scope.row.ishg == 1 || scope.row.ishg == 6) &&\n                  scope.row.createBy == currUser) ||\n                  currUser === 'admin'\n              \"\n              @click=\"deleteRow(scope.row.objId)\"\n              title=\"删除\"\n              class=\"el-icon-delete\"\n            ></el-button>\n          </template>\n        </el-table-column>\n      </comp-table>\n    </el-white>\n\n    <!--详情/新增/修改-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"50%\"\n      v-dialogDrag\n    >\n      <el-form\n        label-width=\"130px\"\n        ref=\"form\"\n        :model=\"form\"\n        :disabled=\"isDisabled\"\n        :rules=\"rules\"\n      >\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"设备专业：\" prop=\"sjzy\">\n              <el-select\n                @change=\"selectsbzy\"\n                v-model=\"form.sjzy\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in sbzyList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验专业：\" prop=\"syzybm\">\n              <el-select\n                @change=\"selectSyxzDataBySyzybm\"\n                v-model=\"form.syzybm\"\n                placeholder=\"请选择试验专业\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in syzyList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验性质：\" prop=\"syxzbm\">\n              <el-select\n                v-model=\"form.syxzbm\"\n                placeholder=\"请选择试验性质\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in syxzList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"设备地点：\" prop=\"sydd\">\n              <!-- <el-input v-model=\"form.sydd\"   placeholder=\"请输入设备地点\" v-on:click.native=\"sysbddClick()\"\n                        style=\"width: 100%\"></el-input> -->\n              <el-select\n                v-model=\"form.sydd\"\n                placeholder=\"请选择设备地点\"\n                @change=\"onclckdd\"\n                style=\"width: 100%\"\n                filterable\n                clearable\n              >\n                <el-option\n                  v-for=\"item in sbddList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验设备：\" prop=\"sbmc\">\n              <el-input\n                v-model=\"form.sbmc\"\n                placeholder=\"请输入试验设备\"\n                v-on:click.native=\"sysbSelectedClick()\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"委托单位：\" prop=\"wtdw\">\n              <el-input\n                v-model=\"form.wtdw\"\n                placeholder=\"请输入委托单位\"\n                style=\"width: 100%\"\n              >\n              </el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验单位：\" prop=\"sydw\">\n              <el-select\n                v-model=\"form.sydw\"\n                placeholder=\"请选择试验单位\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in sydwList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验日期：\" prop=\"syrq\">\n              <el-date-picker\n                v-model=\"form.syrq\"\n                type=\"date\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"请选择试验日期\"\n                style=\"width: 100%\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验名称：\" prop=\"symc\">\n              <el-input\n                v-model=\"form.symc\"\n                placeholder=\"请输入试验名称\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"温度(℃)：\" prop=\"wd\">\n              <el-input\n                v-model=\"form.wd\"\n                placeholder=\"请输入温度\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"湿度(%)：\" prop=\"sd\">\n              <el-input\n                v-model=\"form.sd\"\n                placeholder=\"请输入湿度\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"油温(℃)：\" prop=\"yw\">\n              <el-input\n                v-model=\"form.yw\"\n                placeholder=\"请输入油温\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验天气：\" prop=\"tq\">\n              <el-select\n                v-model=\"form.tq\"\n                placeholder=\"请选择试验天气\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"item in tqList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验模板：\" prop=\"symb\">\n              <el-input\n                v-model=\"form.symb\"\n                placeholder=\"请输入试验模板\"\n                v-on:click.native=\"symbSelectedClick()\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n\n          <!-- <el-col :span=\"12\">\n            <el-form-item label=\"审核人：\" prop=\"shrid\">\n              <el-input v-model=\"form.shrid\" placeholder=\"请输入审核人\" style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"12\">\n            <el-form-item label=\"编制人：\" prop=\"bzrid\">\n              <el-input\n                v-model=\"form.bzrid\"\n                placeholder=\"请输入编制人\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验人员：\" prop=\"syryid\">\n              <el-input\n                v-model=\"form.syryid\"\n                placeholder=\"请输入试验人员\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"审核人：\" prop=\"bzzsp\">\n              <el-input\n                v-model=\"form.bzzsp\"\n                placeholder=\"请输入审核人\"\n                style=\"width: 100%\"\n                :disabled=\"syryDialog\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"批准人：\" prop=\"ssdwldmc\">\n              <el-input\n                v-model=\"form.ssdwldmc\"\n                placeholder=\"请输入批准人\"\n                style=\"width: 100%\"\n                :disabled=\"syryDialog\"\n              >\n              </el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <!-- <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"试验结论：\" prop=\"bz\">\n              <el-input type=\"textarea\" v-model=\"form.syjl\" placeholder=\"请输入试验结论\"\n                        style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row> -->\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注：\" prop=\"bz\">\n              <el-input\n                type=\"textarea\"\n                v-model=\"form.bz\"\n                placeholder=\"请输入备注\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" v-show=\"isShow\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"save\">确 定</el-button>\n      </div>\n    </el-dialog>\n    <!--试验设备地点弹窗-->\n    <el-dialog\n      title=\"试验设备地点\"\n      :visible.sync=\"isShowSysbddDialog\"\n      width=\"20%\"\n      v-if=\"isShowSysbddDialog\"\n      v-dialogDrag\n    >\n      <sysbdd\n        :mainData=\"mainData\"\n        @accessTreeData=\"handleAccessTreeData\"\n        @closeSyddDialog=\"handleCloseSyddDialog\"\n      >\n      </sysbdd>\n    </el-dialog>\n    <!--试验设备选择弹框-->\n    <el-dialog\n      title=\"设备选择\"\n      :visible.sync=\"isShowSysbDialog\"\n      width=\"60%\"\n      v-if=\"isShowSysbDialog\"\n      v-dialogDrag\n    >\n      <sysb-selectedbg\n        @handleAcceptSbData=\"handleAcceptSbData\"\n        :selectedSbParam=\"selectedSbParam\"\n        @closeSysbSelectDialog=\"handleControlSysbSelectDialog\"\n      ></sysb-selectedbg>\n    </el-dialog>\n    <!--试验模板选择组件-->\n    <el-dialog\n      title=\"模板选择\"\n      :visible.sync=\"isShowSymbDialog\"\n      width=\"70%\"\n      v-if=\"isShowSymbDialog\"\n      v-dialogDrag\n    >\n      <symbSyxm-select\n        :symbData=\"symbData\"\n        @handleAcceptMbData=\"handleAcceptMbData\"\n        @closeSymbSelectDialog=\"handleControlSymbSelectDialog\"\n      ></symbSyxm-select>\n    </el-dialog>\n    <!--填写报告模板-->\n    <!--htmlToPdf插件-->\n    <el-dialog\n      title=\"预览\"\n      :visible.sync=\"isShowDownLoadDialog\"\n      width=\"70%\"\n      class=\"outPut\"\n      id=\"dialogActst\"\n      v-dialogDrag\n    >\n      <div style=\"height: 500px;overflow-y: scroll;\">\n        <tablePdf\n          ref=\"tablePdf\"\n          :basic-data=\"basicData\"\n          :tablebox3=\"tablebox3\"\n          :tablebox2=\"tablebox2\"\n        ></tablePdf>\n      </div>\n      <div slot=\"footer\">\n        <el-button\n          @click=\"closeYlDialog\"\n          v-if=\"this.form.ishg == 1 || this.form.ishg == 6\"\n          >取 消</el-button\n        >\n        <el-button\n          type=\"primary\"\n          @click=\"saveYlDialog\"\n          v-if=\"\n            (this.form.ishg == 1 ||\n              this.form.ishg == 6 ||\n              this.form.ishg == 5) &&\n              this.bcDisabled\n          \"\n          >保存\n        </el-button>\n        <el-button type=\"primary\" @click=\"exportPdf('tablePdf')\"\n          >导 出</el-button\n        >\n        <el-button\n          type=\"primary\"\n          @click=\"commitSh\"\n          v-if=\"this.form.ishg == 1 && this.form.createBy == currUser\"\n          >上报\n        </el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"commitSh\"\n          v-if=\"this.form.ishg == 2 && this.form.ssdwld == currUser\"\n          >审批\n        </el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"commitSh\"\n          v-if=\"\n            this.form.ishg == 7 &&\n              this.form.sddwshr &&\n              this.form.sddwshr.includes(currUser)\n          \"\n          >审批\n        </el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"commitSh\"\n          v-if=\"this.form.ishg == 3 && this.form.sckzg == currUser\"\n          >审批\n        </el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"commitSh\"\n          v-if=\"this.form.ishg == 5 && this.form.bzzsp == currUserName\"\n          >审批\n        </el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"handleTh('rollback')\"\n          v-if=\"this.form.ishg == 2 && this.form.ssdwld == currUser\"\n        >\n          回退</el-button\n        >\n        <el-button\n          type=\"primary\"\n          @click=\"handleTh('rollback')\"\n          v-if=\"this.form.ishg == 3 && this.form.sckzg == currUser\"\n        >\n          回退</el-button\n        >\n        <el-button\n          type=\"primary\"\n          @click=\"handleTh('rollback')\"\n          v-if=\"this.form.ishg == 5 && this.form.bzzsp == currUserName\"\n          >回退</el-button\n        >\n        <el-button\n          type=\"primary\"\n          @click=\"handleTh('rollback')\"\n          v-if=\"this.form.ishg == 7 && this.form.sddwshr == currUser\"\n          >回退</el-button\n        >\n      </div>\n    </el-dialog>\n    <el-dialog\n      title=\"批量导出\"\n      :visible.sync=\"isShowDownLoadDialogA\"\n      width=\"70%\"\n      class=\"outPut\"\n      id=\"dialogActst\"\n      v-dialogDrag\n    >\n      <div style=\"height: 400px;overflow-y: scroll;\">\n        <tablePdf\n          :ref=\"`tablePdf${index}`\"\n          v-for=\"(item, index) in selectA\"\n          :basic-data=\"item\"\n          :tablebox3=\"item.tablebox3\"\n          :tablebox2=\"item.tablebox2\"\n        ></tablePdf>\n      </div>\n      <el-progress\n        :percentage=\"inputpdf\"\n        :format=\"progressformate\"\n      ></el-progress>\n      <!-- <el-progress :percentage=\"shengpdf\" status=\"success\"></el-progress>\n      <el-progress :percentage=\"downloadpdf\" status=\"warning\"></el-progress> -->\n    </el-dialog>\n\n    <!--  工作流  -->\n    <activiti\n      :processData=\"processData\"\n      :isShow=\"isShowActiviti\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n      v-if=\"isShowActiviti\"\n    ></activiti>\n    <time-line\n      :value=\"timeLineShow\"\n      :timeData=\"timeData\"\n      @closeTimeLine=\"colseTimeLine\"\n    />\n    <!--流程图查看-->\n    <el-dialog\n      title=\"审批流程图\"\n      :visible.sync=\"openLoadingImg\"\n      width=\"56%\"\n      append-to-body\n      :close-on-click-modal=\"false\"\n      v-dialogDrag\n    >\n      <div>\n        <img :src=\"imgSrc\" style=\"width: 100%\" />\n      </div>\n    </el-dialog>\n    <!-- 试验数据对比 -->\n    <el-dialog\n      :visible.sync=\"isShowMpInfo\"\n      v-dialogDrag\n      v-if=\"isShowMpInfo\"\n      width=\"80%\"\n      title=\"试验项目内容\"\n      :append-to-body=\"true\"\n      @close=\"closeInfoDialog\"\n    >\n      <zxm-info\n        :mp-data=\"rowData\"\n        :mx-data.sync=\"mxData\"\n        @closeInfoDialog=\"closeInfoDialog\"\n      />\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  selectSyxzDataBySyzybm,\n  getSyzySelectedOptions,\n  getSybgjlDataByPage,\n  saveOrUpdateSybgjl,\n  removeSybgjlData,\n  getMouldValue,\n  getChildsValue,\n  saveChildsValue,\n  updateSybgjl,\n  getbdzList,\n  showButtenJS,\n  getTodoItemYd\n} from \"@/api/dagangOilfield/bzgl/sybglr\";\nimport {\n  getDeviceClassTreeNodeByPid,\n  getPageDataList,\n  getTable,\n  remove,\n  saveOrUpdate\n} from \"@/api/dagangOilfield/bzgl/sympk/sympk\";\nimport { Loading } from \"element-ui\";\nimport activiti from \"com/activiti_sybg\";\nimport timeLine from \"com/timeLine\";\nimport { HistoryList } from \"@/api/activiti/processTask\";\n//试验设备地点子组件\nimport sysbdd from \"@/views/dagangOilfield/bzgl/sybzk/sybgjlcomment/sysbdd\";\n//试验单位下拉框查询\nimport { getOrganizationSelected } from \"@/api/dagangOilfield/asset/bdsbtz\";\n//试验设备选择\nimport sysbSelectedbg from \"@/views/dagangOilfield/bzgl/sybzk/sybgjlcomment/sysbSelectedbg\";\n//试验模板选择\nimport symbSyxmSelect from \"@/views/dagangOilfield/bzgl/sybzk/sybgjlcomment/symbSyxmSelect\";\n// 试验数据对比\nimport zxmInfo from \"@/views/dagangOilfield/bzgl/sybzk/zxmInfo\";\n//获取线路下拉数据\nimport { getSdLineSelected } from \"@/api/dagangOilfield/asset/sdxl\";\nimport { getPdsSelected } from \"@/api/dagangOilfield/asset/pdsgl\";\n//pdf导出工具\nimport htmlToPdf from \"@/utils/print/htmlToPdf\";\nimport tablePdf from \"./tablePdf\";\nimport { formatterDateTime } from \"@/utils/handleData\";\nimport { getDictTypeData } from \"@/api/system/dict/data\";\nexport default {\n  name: \"sybglr\",\n  components: {\n    sysbdd,\n    sysbSelectedbg,\n    symbSyxmSelect,\n    htmlToPdf,\n    activiti,\n    timeLine,\n    tablePdf,\n    zxmInfo\n  },\n  data() {\n    var validateSbmc = (rule, value, callback) => {\n      if (!this.form.sbmc) {\n        callback(new Error());\n      } else {\n        callback();\n      }\n    };\n    var validateSymb = (rule, value, callback) => {\n      if (!this.form.symb) {\n        callback(new Error());\n      } else {\n        callback();\n      }\n    };\n    return {\n      // 试验数据对比用\n      isShowMpInfo: false,\n      rowData: {},\n      mxData: [],\n      //\n      showButton: false,\n      syryDialog: true,\n      bcDisabled: false,\n      tablebox3: \"\",\n      tablebox2: \"\",\n      loading: false,\n      currUser: this.$store.getters.name,\n      currUserName: this.$store.getters.nickName,\n      ssdwld: undefined, //所属单位领导\n      sckzg: undefined, //生产科专工\n      createBy: undefined, //新建人\n      clickRow: {},\n      //流程图查看\n      openLoadingImg: false,\n      imgSrc: \"\", //流程图查看地址\n      timeLineShow: false,\n      timeData: [],\n      //弹出框标题\n      activitiOption: { title: \"上报\" },\n      //工作流弹窗\n      isShowActiviti: false,\n      // 多选框选选中的数据\n      selectData: [],\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"sybgsh\",\n        businessKey: \"\",\n        businessType: \"试验报告审核\",\n        variables: {},\n        defaultFrom: true,\n        processType: \"complete\"\n      },\n      //基本信息表格数据\n      basicData: {\n        sydwmc: \"\", //试验单位\n        syxz: \"\", //试验性质\n        syrq: \"\", //  试验日期\n        syryid: \"\", // 试验人员\n        bzrid: \"\", // 编写人\n        shrid: \"\", // 审核人\n        updateBy: \"\", // 批准人\n        tq: \"\", // 试验天气\n        wd: \"\", // 温度\n        sd: \"\", // 湿度\n        sydd: \"\", //试验地点\n        wtdw: \"\", //委托单位\n        yxbh: \"\", //运行编号\n        sccj: \"\", //生产厂家\n        ccrq: \"\", //出厂日期，\n        ccbh: \"\", //出厂编号\n        sbxh: \"\", //设备型号\n        eddy: \"\", //额定电压(kV)\n        eddl: \"\", //额定电流(A)\n        xs: \"\", //相数\n        //相数接线组别\n        edrl: \"\" ,//额定容量(MVA)\n        //电压组合\n        //电流组合\n        //容量组合\n        tyrq:\"\"\n      },\n      Cont: [\n        {\n          bdz: \"变电站\",\n          name: \"\",\n          bdz1: \"委托单位\",\n          name1: \"\",\n          bdz2: \"试验单位\",\n          name2: \"\",\n          bdz3: \"运行编号\",\n          name3: \"\"\n        },\n        {\n          bdz: \"试验性质\",\n          name: \"\",\n          bdz1: \"试验日期\",\n          name1: \"\",\n          bdz2: \"试验人员\",\n          name2: \"\",\n          bdz3: \"试验地点\",\n          name3: \"\"\n        },\n        {\n          bdz: \"报告日期\",\n          name: \"\",\n          bdz1: \"编写人\",\n          name1: \"\",\n          bdz2: \"审核人\",\n          name2: \"\",\n          bdz3: \"批准人\",\n          name3: \"\"\n        },\n        {\n          bdz: \"试验天气\",\n          name: \"\",\n          bdz1: \"环境温度（℃）\",\n          name1: \"\",\n          bdz2: \"环境相对湿度（%）\",\n          name2: \"\",\n          bdz3: \"投运日期\",\n          name3: \"\"\n        }\n      ],\n      allAlign: null,\n      titleName: \"\", //填写模板标题\n      //下载弹出框控制\n      isShowDownLoadDialog: false,\n\n      isShowDownLoadDialogA: false,\n      mbInfo: {},\n      //主设备选择传递子组件参数\n      selectedSbParam: {\n        lx: \"bd\",\n        sbmc: \"\"\n      },\n      //主设备选择传递子组件参数\n      mainData: {\n        lx: \"\"\n      },\n      //试验设备选择时给试验模板子组件传递参数\n      symbData: {\n        sblxid: \"\"\n      },\n\n      //设备专业\n      sbzyList: [\n        {\n          label: \"输电设备\",\n          value: \"输电设备\"\n        },\n        {\n          label: \"变电设备\",\n          value: \"变电设备\"\n        },\n        {\n          label: \"配电设备\",\n          value: \"配电设备\"\n        }\n      ],\n      //试验模板弹出框控制\n      isShowSymbDialog: false,\n      //试验设备弹出框控制\n      isShowSysbDialog: false,\n      //试验设备地点弹出框控制\n      isShowSysbddDialog: false,\n      //试验报告记录新增弹出框表单\n      form: {\n        //固定不可清空\n        sjzy: \"\",\n        //试验专业编码\n        syzybm: \"\",\n        //试验专业名称\n        syzy: \"\",\n        //试验性质编码\n        syxzbm: \"\",\n        //试验性质名称\n        syxz: \"\",\n        //设备地点名称\n        sydd: \"\",\n        syddid: \"\", //试验地点id\n        //试验设备id\n        sysbid: \"\",\n        sbmc: \"\", //设备名称\n        sydw: \"\", //试验单位id\n        syrq: undefined, // 试验日期\n        symc: \"\", //试验名称\n        wd: \"\", //温度\n        sd: \"\", //湿度\n        yw: \"\", //油温\n        tq: \"\", //天气\n        symb: \"\", //试验模板名称\n        symbid: \"\", //试验模板id\n        bzrid: \"\", //编制人名称，后面改用下拉框\n        shrid: \"\", //审核人名称，后面改为下拉框\n        syryid: \"\", //试验人员。后面改为下拉框\n        bz: \"\" //备注\n      },\n      assetTypeCode: \"\", //设备类型编码\n      //详情弹框是否显示\n      isShowDetails: false,\n      //显示取消确认按钮\n      isShow: true,\n      //是否禁用\n      isDisabled: false,\n      zyisDisabled: false,\n      //试验专业\n      syzyList: [],\n      //试验性质\n      syxzList: [],\n      //设备地点\n      sbddList: [],\n      //试验单位\n      sydwList: [],\n      //流程状态\n      ishg: \"\",\n      sybgjlld: \"\",\n      selectA: [],\n      shengpdf: 0,\n      inputpdf: 0,\n      downloadpdf: 0,\n      syxmid: \"\",\n      sysbid: \"\",\n      //试验天气\n      tqList: [\n        { label: \"晴\", value: \"晴\" },\n        { label: \"阴\", value: \"阴\" },\n        { label: \"雾\", value: \"雾\" },\n        { label: \"小雨\", value: \"小雨\" },\n        { label: \"中雨\", value: \"中雨\" },\n        { label: \"大雨\", value: \"大雨\" },\n        { label: \"雷雨\", value: \"雷雨\" },\n        { label: \"小雪\", value: \"小雪\" },\n        { label: \"中雪\", value: \"中雪\" },\n        { label: \"大雪\", value: \"大雪\" }\n      ],\n      //试验报告记录新增弹窗框标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          symc: \"\",\n          sjzy: \"\",\n          syddid: \"\",\n          symb: \"\",\n          syrqArr: [],\n          syzy: \"\",\n          syxz: \"\",\n          sbmc: \"\",\n          ishg: \"\"\n        },\n        fieldList: [\n          { label: \"试验名称\", type: \"input\", value: \"symc\" },\n          {\n            label: \"设备专业\",\n            type: \"select\",\n            value: \"sjzy\",\n            options: [\n              { label: \"输电设备\", value: \"输电设备\" },\n              { label: \"变电设备\", value: \"变电设备\" },\n              { label: \"配电设备\", value: \"配电设备\" }\n            ],\n            clearable: false\n          },\n          { label: \"设备地点\", type: \"select\", value: \"syddid\", options: [] },\n          { label: \"试验模板名称\", type: \"input\", value: \"symb\" },\n          {\n            label: \"试验日期\",\n            type: \"date\",\n            value: \"syrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"试验专业\", type: \"select\", value: \"syzy\", options: [] },\n          { label: \"试验性质\", type: \"input\", value: \"syxz\" },\n          { label: \"试验设备\", type: \"input\", value: \"sbmc\" },\n          { label: \"流程状态\", type: \"select\", value: \"ishg\", options: [] }\n        ]\n      },\n      //查询报告记录参数\n      queryParam: {\n        pageSize: 10,\n        pageNum: 1\n        // syddid: \"\"\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 0]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"试验专业\", prop: \"syzy\" },\n          { label: \"试验性质\", prop: \"syxz\" },\n          { label: \"试验名称\", prop: \"symc\", minWidth: \"100\" },\n          { label: \"设备地点\", prop: \"sydd\", minWidth: \"120\" },\n          { label: \"试验设备\", prop: \"sbmc\", minWidth: \"200\" },\n          { label: \"试验模板名称\", prop: \"symb\", minWidth: \"120\" },\n          { label: \"天气\", prop: \"tq\" },\n          { label: \"试验日期\", prop: \"syrq\" },\n          { label: \"试验人员\", prop: \"syryid\" },\n          { label: \"流程状态\", prop: \"ztmc\" }\n          // {\n          //   prop: 'operation',\n          //   label: '试验报告',\n          //   minWidth: '120px',\n          //   style: {display: 'block'},\n          //   //操作列固定再右侧\n          //   operation: [\n          //     {name: '填写报告', clickFun: this.saveSybgInfo},\n          //   ]\n          // },\n          // {\n          //   prop: 'operation',\n          //   label: '操作',\n          //   minWidth: '120px',\n          //   style: {display: 'block'},\n          //   //操作列固定再右侧\n          //   fixed: 'right',\n          //   operation: [\n          //     {name: '修改', clickFun: this.updateDetails},\n          //     {name: '详情', clickFun: this.getDetailsInfo},\n          //   ]\n          // },\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      //试验报告记录选中\n      selectRows: [],\n      str: \"\",\n      saveData: {}, //选中填写报告的值\n      rules: {\n        syzybm: [\n          { required: true, message: \"请输入试验专业\", trigger: \"select\" }\n        ],\n        syxzbm: [\n          { required: true, message: \"请输入试验性质\", trigger: \"blur\" }\n        ],\n        sydd: [{ required: true, message: \"请输入设备地点\", trigger: \"blur\" }],\n        // syxzbm: [{ required: true, message: \"请输入设备地点\", trigger: \"blur\" }],\n        symb: [\n          {\n            required: true,\n            message: \"请选择试验模板\",\n            validator: validateSymb,\n            trigger: \"change\"\n          }\n        ],\n        sjzy: [{ required: true, message: \"请选择设备专业\", trigger: \"blur\" }],\n        sbmc: [\n          {\n            required: true,\n            message: \"请选择试验设备\",\n            validator: validateSbmc,\n            trigger: \"change\"\n          }\n        ],\n        symc: [{ required: true, message: \"请输入试验名称\", trigger: \"blur\" }]\n      },\n      jlData: \"\", //结论所在行标识\n      isJlDisabled: false //结论是否不可编辑\n    };\n  },\n\n  mounted() {\n    //获取试验专业下拉框数据\n    this.getOptions();\n    //获取试验单位下拉框\n    this.getOrganizationSelected();\n    //获取数据列表\n    this.getData(this.$route.query);\n    //查询当前登录人是否有试验报告填写权限\n    let params = {\n      groupId: 74,\n      userName: this.$store.getters.name\n    };\n    showButtenJS(params).then(res => {\n      if (res.data.length > 0) {\n        this.showButton = true;\n      }\n    });\n  },\n  methods: {\n    //进度条状态更新\n    progressformate(procent) {\n      return this.inputpdf === 100 ? \"导出完成\" : `${procent} %`;\n    },\n    //获取试验子项目信息\n    async getZxmmpInfo(row) {\n      // 试验报告中获取试验项目模板的symbid\n      let symbid = row.symbid;\n      // 试验项目模板中获取子项目模板数据\n      //rowData\n      let { data: zxmMbData } = await getMouldValue({ symbid: row.symbid });\n      this.rowData = zxmMbData[Object.keys(zxmMbData)[1]][0];\n      //mxData\n      getChildsValue(row).then(result => {\n        let sysj_data = result.data.syxm;\n        this.mxData = sysj_data[Object.keys(sysj_data)[0]];\n        this.isShowMpInfo = true;\n      });\n    },\n    //关闭试验数据对比弹框\n    closeInfoDialog() {\n      this.isShowMpInfo = false;\n    },\n    //搜索框sbddid 选择事件\n    sbddidClick(val, val1) {\n      if (val1 === \"syddmc\") {\n        this.mainData.lx = \"\";\n        this.isShowSysbddDialog = true;\n      }\n    },\n\n    //详情按钮\n    getDetailsInfo(row) {\n      this.title = \"试验报告详情\";\n      //不显示取消确认按钮\n      this.isShow = false;\n      //禁用表单\n      this.isDisabled = true;\n      this.zyisDisabled = true;\n      //打开弹窗\n      this.isShowDetails = true;\n      this.form = { ...row };\n      //调用获取试验性质方法渲染试验性质\n      this.selectSyxzDataBySyzybm1();\n    },\n    //修改按钮\n    updateDetails(row) {\n      this.title = \"试验报告修改\";\n      //显示取消确认按钮\n      this.isShow = true;\n      this.zyisDisabled = true;\n      //禁用表单\n      this.isDisabled = false;\n      //打开弹窗\n      this.isShowDetails = true;\n      this.form = { ...row };\n      //调用获取试验性质方法渲染试验性质\n      this.selectSyxzDataBySyzybm1();\n    },\n    //填写报告\n    async saveSybgInfo(row) {\n      this.form = { ...row };\n      this.clickRow = row;\n      (this.bcDisabled = true), (this.ishg = row.ishg);\n      this.createBy = row.createBy;\n      this.ssdwld = row.ssdwld;\n      this.sckzg = row.sckzg;\n      this.saveData = row;\n      this.basicData = row;\n      this.sybgjlld = row.objId;\n      this.symbid = row.symbid;\n      this.sysbid = row.sysbid;\n      this.isShowDownLoadDialog = true;\n      this.titleName = row.symb;\n      //在填写报告时，请求后台处理数据\n      const that = this;\n      await getMouldValue({ symbid: row.symbid }).then(res => {\n        res = res.data;\n        getChildsValue(row).then(result => {\n          let sbmp_data = result.data.sbmp;\n          let sysj_data = result.data.syxm;\n          let arr = [];\n          for (let item in sbmp_data) {\n            arr.push({ [item]: sbmp_data[item] });\n          }\n          let arr1 = [];\n          for (let val in sysj_data) {\n            arr1.push({ [val]: sysj_data[val] });\n          }\n          that.handleSbmp(res[Object.keys(res)[0]], arr, \"\", \"h2_table\");\n          that.handleSbmp(res[Object.keys(res)[1]], arr1, \"\", \"h3_table\");\n        });\n      });\n      this.form = { ...row };\n    },\n\n    //查看试验报告\n    SybgInfo(row) {\n      this.form = { ...row };\n      this.clickRow = row;\n      this.ishg = row.ishg;\n      this.bzzsp = row.bzzsp;\n      this.bcDisabled = true;\n      this.createBy = row.createBy;\n      this.ssdwld = row.ssdwld;\n      this.sckzg = row.sckzg;\n      this.saveData = row;\n      this.basicData = row;\n      this.sybgjlld = row.objId;\n      this.symbid = row.symbid;\n      this.sysbid = row.sysbid;\n      this.isShowDownLoadDialog = true;\n      this.titleName = row.symb;\n      let gzimg1 = document.getElementById(\"gzimg1\");\n      gzimg1 && gzimg1.remove();\n      let gzimg2 = document.getElementById(\"gzimg2\");\n      gzimg2 && gzimg2.remove();\n      //在填写报告时，请求后台处理数据\n      const that = this;\n      getMouldValue({ symbid: row.symbid }).then(res => {\n        res = res.data;\n        getChildsValue(row).then(result => {\n          let sbmp_data = result.data.sbmp;\n          let sysj_data = result.data.syxm;\n          let arr = [];\n          for (let item in sbmp_data) {\n            arr.push({ [item]: sbmp_data[item] });\n          }\n          let arr1 = [];\n          for (let val in sysj_data) {\n            arr1.push({ [val]: sysj_data[val] });\n          }\n          that.handleSbmp(res[Object.keys(res)[0]], arr, \"\", \"h2_table\");\n          that.handleSbmp(res[Object.keys(res)[1]], arr1, \"\", \"h3_table\");\n        });\n      });\n      setTimeout(async () => {\n        this.showTest();\n      }, 3000);\n    },\n\n    handleSbmp(dataNum, dataArr, str, tableBox, returnString) {\n      for (let k = 0; k < dataNum.length; k++) {\n        var hs = dataNum[k].aHs;\n        var ls = dataNum[k].aLs;\n        let data = dataArr[k];\n        for (var item in data) {\n          // str += \"<tr style='text-align:left;'><th colspan=\"+ ls +\">\"+ item +\"</th></tr>\";\n          for (let i = 0; i < hs; i++) {\n            //有几行就插入几行\n            let temp = \"<tr class='splitClass'>\";\n            for (let j = 0; j < data[item].length; j++) {\n              //循环数据看每行有几列\n              if (i == data[item][j].rowindex) {\n                var nrbs = data[item][j].nrbs;\n                var sjlx = data[item][j].sjlx; //数据类型\n                var objId = data[item][j].objId;\n                var txt = data[item][j].text;\n                var nr = \"\";\n                if (nrbs === \"结论\") {\n                  this.jlData = data[item][j + 1]; //保存下一个单元格内容\n                }\n                if (sjlx == \"STRING\") {\n                  //判断数据类型为string的表示为空格，可编辑\n                  if (!txt) {\n                    txt = \"\";\n                  }\n                  nr =\n                    \"<input type='text' style='border: none;width: 99%;text-align: center;' class=\" +\n                    tableBox +\n                    \" id=\" +\n                    objId +\n                    \" value=\" +\n                    txt +\n                    \">\";\n                } else {\n                  nr = nrbs;\n                }\n                if (data[item][j].colspan != \"1\") {\n                  //判断colspan不为1的话为可编辑的\n                  if (this.jlData && this.jlData.objId === objId) {\n                    //当前单元格是结论所在单元格\n                    if (this.form.ishg == 5 || this.form.ishg == 6) {\n                      //只有班组长审批和历史录入是可编辑的\n                      temp +=\n                        \"<td tabindex='-1' colspan='\" +\n                        data[item][j].colspan +\n                        \"' rowspan='\" +\n                        data[item][j].rowspan +\n                        \"' >\" +\n                        nr +\n                        \"</td>\";\n                      this.isJlDisabled = false;\n                    } else {\n                      //显示原值，不可编辑\n                      temp +=\n                        \"<td tabindex='-1' colspan='\" +\n                        data[item][j].colspan +\n                        \"' rowspan='\" +\n                        data[item][j].rowspan +\n                        \"' id='\" +\n                        objId +\n                        \"' class='_objId'\" +\n                        \" >\" +\n                        txt +\n                        \"</td>\";\n                      this.isJlDisabled = true;\n                    }\n                  } else {\n                    temp +=\n                      \"<td tabindex='-1' colspan='\" +\n                      data[item][j].colspan +\n                      \"' rowspan='\" +\n                      data[item][j].rowspan +\n                      \"' >\" +\n                      nr +\n                      \"</td>\";\n                  }\n                } else {\n                  temp += \"<td tabindex='-1'>\" + nr + \"</td>\";\n                }\n              }\n            }\n            temp += \"</tr>\";\n            str += temp;\n          }\n        }\n      }\n      if (returnString) {\n        return str;\n      }\n      if (tableBox === \"h2_table\") {\n        this.tablebox2 = str;\n      } else if (tableBox === \"h3_table\") {\n        this.tablebox3 = str;\n      } else {\n        this[tableBox] = str;\n      }\n    },\n    //关闭填写报告弹框\n    closeYlDialog() {\n      this.isShowDownLoadDialog = false;\n    },\n\n    dcpdf() {\n      let that = this;\n      if (!that.selectRows.length > 0) {\n        that.$message.warning(\"请先选中需要批量导出的数据\");\n        return;\n      }\n      that.inputpdf = 0;\n      let selectRows = that.selectRows;\n      console.log(selectRows, \"select\");\n      that.selectA = selectRows.map((e, i) => {\n        getMouldValue({ symbid: e.symbid }).then(res => {\n          res = res.data;\n          getChildsValue(e).then(result => {\n            let sbmp_data = result.data.sbmp;\n            let sysj_data = result.data.syxm;\n            let arr = [];\n            for (let item in sbmp_data) {\n              arr.push({ [item]: sbmp_data[item] });\n            }\n            let arr1 = [];\n            for (let val in sysj_data) {\n              arr1.push({ [val]: sysj_data[val] });\n            }\n            e.tablebox2 = that.handleSbmp(\n              res[Object.keys(res)[0]],\n              arr,\n              \"\",\n              \"\",\n              true\n            );\n            e.tablebox3 = that.handleSbmp(\n              res[Object.keys(res)[1]],\n              arr1,\n              \"\",\n              \"\",\n              true\n            );\n            e.sysj = sysj_data;\n          });\n        });\n        // that.inputpdf = ((i + 1 / selectRows.length) * 100).toFixed(2)\n        // console.log(that.inputpdf, 'inputpdf1...');\n        return e;\n      });\n      console.log(that.selectA, \"select\");\n      that.isShowDownLoadDialogA = true;\n      that.inputpdf = 30;\n      // console.log(that.inputpdf, 'inputpdf2...');\n      let length = that.selectA.length;\n      setTimeout(async () => {\n        that.$refs[`tablePdf${length - 1}`] &&\n          that.$refs[`tablePdf${length - 1}`][0].$nextTick(() => {\n            setTimeout(async () => {\n              let pdfall = await Promise.all(\n                that.selectA.map(async (e, i) => {\n                  console.log(e, \"item..\");\n                  let newVar = await that.exportPdf(\n                    `tablePdf${i}`,\n                    true,\n                    `${e.symb}_${i}`,\n                    true,\n                    e.sysj\n                  );\n                  that.shengpdf = ((i + 1 / length) * 100).toFixed(2);\n                  return newVar;\n                })\n              );\n              htmlToPdf.zipChange(\n                pdfall,\n                \"试验报告_\" + new Date().getTime(),\n                (item, i) => {\n                  that.downloadpdf = ((i + 1 / length) * 100).toFixed(2);\n                }\n              );\n              console.log(\"pdf\", \"!!\");\n              that.inputpdf = 100;\n            }, 3000);\n          });\n        that.inputpdf = 60;\n        // console.log('pdf', '@@')\n      }, 3000);\n    },\n    //导出pdf\n    async exportPdf(refsname = \"tablePdf\", tableall, name, flag = false, sysj) {\n      let tablesss = this.$refs[refsname];\n      if (tablesss instanceof Array) {\n        tablesss = tablesss[0];\n      }\n      let element = tablesss.$el;\n\n      element.style.position = \"relative\";\n      var jlinput = \"\";\n      //批量导出标记\n      if (flag) {\n        for (const item in sysj) {\n          for (let i = 0; i < sysj[item].length; i++) {\n            if (sysj[item][i].nrbs === \"结论\") {\n              jlinput = sysj[item][i + 1].text;\n              break;\n            }\n          }\n        }\n      } else {\n        var img1 = \"\";\n        var img2 = \"\";\n        let jlDom = document.getElementById(this.jlData.objId);\n        if (jlDom) {\n          jlinput = this.isJlDisabled ? jlDom.innerText : jlDom.value;\n        }\n      }\n\n      if (/不合格/gi.test(jlinput)) {\n        img1 = \"/image/qualified.png\";\n        img2 = \"/image/test_test.png\";\n      } else if (/合格/gi.test(jlinput)) {\n        img1 = \"/image/unqualified.png\";\n        img2 = \"/image/test_test.png\";\n      }\n      //合格 or 不合格 盖章\n      var imgbox1 = document.createElement(\"div\");\n      imgbox1.style.position = \"absolute\";\n      imgbox1.id = \"gzimg1\";\n      imgbox1.style.bottom = \"0\";\n      imgbox1.style.right = \"0\";\n      imgbox1.style.top = \"0\";\n      imgbox1.style.marginRight = \"10px\";\n      imgbox1.style.marginTop = \"90px\";\n      var imgDocument1 = document.createElement(\"img\");\n      imgDocument1.setAttribute(\"src\", img1);\n      imgbox1.appendChild(imgDocument1);\n      element.appendChild(imgbox1);\n      //盖公章\n      var imgbox2 = document.createElement(\"div\");\n      imgbox2.style.position = \"absolute\";\n      imgbox2.id = \"gzimg2\";\n      imgbox2.style.bottom = \"0\";\n      imgbox2.style.right = \"0\";\n      imgbox2.style.top = \"0\";\n      imgbox2.style.marginRight = \"300px\";\n      imgbox2.style.marginTop = \"10px\";\n      var imgDocument2 = document.createElement(\"img\");\n      imgDocument2.setAttribute(\"src\", img2);\n      imgbox2.appendChild(imgDocument2);\n      element.appendChild(imgbox2);\n\n      await htmlToPdf.outPutPdfFn(element, \"splitClass\");\n      let pdf = await htmlToPdf.downloadPDF(element, name || this.titleName);\n      if (tableall) {\n        return pdf;\n      }\n      pdf.pdf.save(pdf.name);\n      this.closeYlDialog();\n      // 恢复原表格样式\n      let gzimg1 = document.getElementById(\"gzimg1\");\n      gzimg1 && gzimg1.remove();\n      let gzimg2 = document.getElementById(\"gzimg2\");\n      gzimg2 && gzimg2.remove();\n    },\n    showTest() {\n      let tablesss = this.$refs[\"tablePdf\"];\n      if (tablesss instanceof Array) {\n        tablesss = tablesss[0];\n      }\n      let element = tablesss.$el;\n\n      element.style.position = \"relative\";\n      var jlinput = \"\";\n      var img1 = \"\";\n      var img2 = \"\";\n      let jlDom = document.getElementById(this.jlData.objId);\n      if (jlDom) {\n        jlinput = this.isJlDisabled ? jlDom.innerText : jlDom.value;\n      }\n      if (/不合格/gi.test(jlinput)) {\n        img1 = \"/image/qualified.png\";\n        img2 = \"/image/test_test.png\";\n      } else if (/合格/gi.test(jlinput)) {\n        img1 = \"/image/unqualified.png\";\n        img2 = \"/image/test_test.png\";\n      } else {\n        return false;\n      }\n      //合格 or 不合格 盖章\n      var imgbox1 = document.createElement(\"div\");\n      imgbox1.style.position = \"absolute\";\n      imgbox1.id = \"gzimg1\";\n      imgbox1.style.bottom = \"0\";\n      imgbox1.style.right = \"0\";\n      imgbox1.style.top = \"0\";\n      imgbox1.style.marginRight = \"10px\";\n      imgbox1.style.marginTop = \"90px\";\n      var imgDocument1 = document.createElement(\"img\");\n      imgDocument1.setAttribute(\"src\", img1);\n      imgbox1.appendChild(imgDocument1);\n      element.appendChild(imgbox1);\n      //盖公章\n      var imgbox2 = document.createElement(\"div\");\n      imgbox2.style.position = \"absolute\";\n      imgbox2.id = \"gzimg2\";\n      imgbox2.style.bottom = \"0\";\n      imgbox2.style.right = \"0\";\n      imgbox2.style.top = \"0\";\n      imgbox2.style.marginRight = \"300px\";\n      imgbox2.style.marginTop = \"10px\";\n      var imgDocument2 = document.createElement(\"img\");\n      imgDocument2.setAttribute(\"src\", img2);\n      imgbox2.appendChild(imgDocument2);\n      element.appendChild(imgbox2);\n    },\n    //保存填写报告数据\n    saveYlDialog() {\n      this.$nextTick(() => {\n        // 以服务的方式调用的 Loading 需要异步关闭\n        this.loading = Loading.service({\n          lock: true, //lock的修改符--默认是false\n          text: \"流程进行中，请稍后\", //显示在加载图标下方的加载文案\n          spinner: \"el-icon-loading\", //自定义加载图标类名\n          background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n          target: document.querySelector(\"#dialogActst\")\n        });\n      });\n      //调用子组件中的方法，获取单元格的值\n      let params = this.$refs.tablePdf.getParams();\n      saveChildsValue({\n        symbid: this.saveData.symbid,\n        objId: this.saveData.objId,\n        params: params\n      }).then(res => {\n        if (res.code === \"0000\") {\n          this.$message({\n            type: \"success\",\n            message: \"保存成功!\"\n          });\n        }\n      });\n      //更新试验人员\n      updateSybgjl(this.clickRow).then(res => {\n        if (res.code == \"0000\") {\n          this.$nextTick(() => {\n            // 以服务的方式调用的 Loading 需要异步关闭\n            this.loading.close();\n          });\n          this.$message.success(\"操作成功！\");\n          this.isShowDetails = false;\n          this.isShowDownLoadDialog = false;\n          this.getData();\n        }\n      });\n    },\n    //报告记录列表查询\n    async getData(params) {\n      try {\n        this.queryParam = { ...this.queryParam, ...params };\n        this.$refs.sybglr.loading = true;\n        const param = { ...this.queryParam, ...params };\n        const { data, code } = await getSybgjlDataByPage(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          this.$nextTick(() => {\n            // 以服务的方式调用的 Loading 需要异步关闭\n            this.$refs.sybglr.loading = false;\n          });\n        }\n      } catch (e) {\n        this.$nextTick(() => {\n          // 以服务的方式调用的 Loading 需要异步关闭\n          this.$refs.sybglr.loading = false;\n        });\n      }\n    },\n    //试验模板点击事件\n    symbSelectedClick() {\n      if (this.form.sbmc === \"\" || this.form.sbmc === undefined) {\n        this.$message.warning(\"请选择试验设备\");\n        return;\n      }\n      this.isShowSymbDialog = true;\n    },\n    //试验模板接收数据\n    handleAcceptMbData(mbData) {\n      //模板名称\n      // this.form.symb = mbData.mbmc;\n      console.log(\"mbData\", mbData);\n      this.$set(this.form, \"symb\", mbData.mbmc);\n      //模板id\n      this.form.symbid = mbData.objId;\n    },\n    //关闭模板弹窗\n    handleControlSymbSelectDialog(isShow) {\n      this.isShowSymbDialog = isShow;\n    },\n    //组件接受设备参数数据\n    handleAcceptSbData(sbData) {\n      console.log(\"sbData\", sbData);\n      let sbmcS = [];\n      let sysbS = [];\n      sbData.forEach(item => {\n        sbmcS.push(item.sbmc);\n        sysbS.push(item.objId);\n      });\n      this.form.sbmc = sbmcS.join(\",\");\n      this.form.sysbid = sysbS.join(\",\");\n      this.symbData.sblxid = sbData[0].sblxbm;\n      this.form.yxbh = sbData[0].wzmc;\n      this.form.tyrq = sbData[0].tyrq;\n      // this.form.sbmc = sbData.sbmc;\n      // this.form.sysbid = sbData.objId;\n      // console.log(\"sbData\",sbData);\n      // //设备类型编码\n      // //线路 sblxbm sblxbm  sblxbm\n      // this.symbData.sblxid=sbData.sblxbm;\n    },\n    //控制关闭试验设备弹出框\n    handleControlSysbSelectDialog(isShow) {\n      this.isShowSysbDialog = isShow;\n    },\n    //获取试验单位数据\n    getOrganizationSelected() {\n      let parentId = \"1001\";\n      getOrganizationSelected({ parentId: parentId }).then(res => {\n        res.data.forEach((item, index) => {\n          if (item.value === 4002) {\n            let sydw = {};\n            sydw.label = item.label;\n            sydw.value = item.value;\n            this.sydwList.push(sydw);\n          }\n        });\n      });\n    },\n    //接受试验设备地点树结构数据\n    handleAccessTreeData(treeNode) {\n      console.log(\"treeNode\", treeNode);\n      if (this.mainData.lx === \"\") {\n        console.log(\"this.mainData.lx\", \"123\");\n        let syddmc = document.getElementsByName(\"syddmc\");\n        console.log(\"syddmc\", syddmc);\n        if (syddmc) {\n          syddmc[0].value = treeNode.label;\n          this.queryParam.syddid = treeNode.id;\n        }\n      }\n      //给表单设备地点赋值\n      this.form.sydd = treeNode.label;\n      this.basicData.sydd = treeNode.label;\n      //数据库存储值\n      this.form.syddid = treeNode.id;\n      this.$set(this.form, \"sbmc\", \"\");\n    },\n    //子组件关闭试验设备地点弹窗\n    handleCloseSyddDialog(syddDialogControl) {\n      this.isShowSysbddDialog = syddDialogControl;\n    },\n    //获取试验专业下拉框数据\n    getOptions() {\n      getSyzySelectedOptions().then(res => {\n        this.syzyList = res.data;\n        this.filterInfo.fieldList.forEach(item => {\n          if (item.value == \"syzy\") {\n            item.options = this.syzyList;\n          }\n        });\n      });\n      getDictTypeData(\"sybg_ztcl\").then(res => {\n        this.filterInfo.fieldList.forEach(item => {\n          if (item.value == \"ishg\") {\n            item.options = res.data.map(e => {\n              e.value = e.numvalue\n              return e\n            });\n          }\n        });\n      });\n    },\n    //根据试验专业编码获取试验性质信息\n    selectSyxzDataBySyzybm() {\n      //change事件发生改变时，清空之前试验性质\n      this.$set(this.form, \"syxzbm\", \"\");\n      this.syxzList = [];\n      let syzybm = this.form.syzybm;\n      selectSyxzDataBySyzybm({ syzybm }).then(response => {\n        this.syxzList = response.data;\n      });\n    },\n    //点击修改详情按钮时回显试验性质时使用\n    selectSyxzDataBySyzybm1() {\n      this.syxzList = [];\n      let syzybm = this.form.syzybm;\n      selectSyxzDataBySyzybm({ syzybm }).then(response => {\n        this.syxzList = response.data;\n      });\n    },\n\n    //设备专业发生变化时，清空设备地点数据\n    async selectsbzy(val) {\n      this.$set(this.form, \"sydd\", \"\");\n      let sbddall = [];\n      this.sbddList = [];\n      switch (val) {\n        case \"变电设备\":\n          sbddall = await getbdzList({});\n          sbddall.forEach((item, index) => {\n            let bdz = {};\n            bdz.label = item.bdzmc;\n            bdz.value = item.objId;\n            this.sbddList.push(bdz);\n          });\n          break;\n        case \"输电设备\":\n          let { data } = await getSdLineSelected({});\n          this.sbddList = data;\n          break;\n        case \"配电设备\":\n          await this.getPdsSelected();\n          break;\n      }\n    },\n\n    onclckdd(val) {\n      console.log(val);\n      this.$set(this.form, \"sydd\", val.label);\n      this.$set(this.form, \"syddid\", val.value);\n    },\n\n    //获取配电室下拉数据\n    async getPdsSelected() {\n      this.sbddList = [];\n      await getPdsSelected({}).then(res => {\n        this.sbddList = res.data;\n      });\n    },\n\n    //设备地点发生变化时，清空试验设备的数据\n    changeInput() {\n      console.log(\"123123\");\n      this.$set(this.form, \"sbmc\", \"\");\n    },\n\n    //试验设备地点点击事件\n    sysbddClick() {\n      if (this.form.sjzy === \"\" || this.form.sjzy === undefined) {\n        this.$message.warning(\"请选择设备专业\");\n        return;\n      }\n      if (this.form.sjzy === \"变电设备\") {\n        this.mainData.lx = \"2\";\n      } else if (this.form.sjzy === \"配电设备\") {\n        this.mainData.lx = \"3\";\n      } else if (this.form.sjzy === \"输电设备\") {\n        this.mainData.lx = \"1\";\n      }\n      this.isShowSysbddDialog = true;\n    },\n    //试验设备选择事件\n    sysbSelectedClick() {\n      //试验设备发生变化时，清空试验模板得数据\n      this.$set(this.form, \"symb\", \"\");\n      if (this.form.sjzy === \"\" || this.form.sjzy === undefined) {\n        this.$message.warning(\"请选择设备专业\");\n        return;\n      }\n      if (this.form.sydd === \"\" || this.form.sydd === undefined) {\n        this.$message.warning(\"请选择设备地点\");\n        return;\n      }\n      if (this.form.sjzy === \"变电设备\") {\n        this.selectedSbParam.lx = \"bd\";\n        this.selectedSbParam.fsss = \"附属\";\n      } else if (this.form.sjzy === \"配电设备\") {\n        this.selectedSbParam.lx = \"pd\";\n      } else if (this.form.sjzy === \"输电设备\") {\n        this.selectedSbParam.lx = \"sd\";\n      }\n      this.selectedSbParam.sbmc = this.form.sydd; //所属位置\n      this.isShowSysbDialog = true;\n    },\n    //重置按钮\n    getReset(data) {\n      //不可以清空设备专业\n      // let syddmc = document.getElementsByName(\"syddmc\");\n      // console.log(\"syddmc\", syddmc);\n      // if (syddmc) {\n      //   syddmc[0].value = \"\";\n      // }\n      this.queryParam = {};\n    },\n    //下拉框change事件\n    async handleEvent(val, val1) {\n      //变电站查询下拉框选项根据所选分公司带出来\n      if (val.label === \"sjzy\" && val.value && val.value !== \"\") {\n        this.$set(val1, \"syddid\", \"\");\n        await this.selectsbzy(val.value);\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"syddid\") {\n            console.log(\"options_syddid\", this.sbddList);\n            return (item.options = this.sbddList);\n          }\n        });\n      }\n    },\n    //新增按钮\n    getInsert() {\n      //清空试验性质数据\n      this.syxzList = [];\n      this.title = \"新建试验报告\";\n      //表单不禁用\n      this.isDisabled = false;\n      //展示取消确认按钮\n      this.isShowDetails = true;\n      this.isShow = true;\n\n      //清空表单\n      this.form = {};\n      this.form.ishg = 1;\n    },\n\n    initFormData() {\n\n    },\n\n    //   handleSelectionChange(selection) {\n    //   this.selectData = selection\n    // },\n    //历史数据录入\n    getInsertls() {\n      //清空试验性质数据\n      this.syxzList = [];\n      this.title = \"新建试验报告\";\n      //表单不禁用\n      this.isDisabled = false;\n      this.zyisDisabled = true;\n      //展示取消确认按钮\n      this.isShowDetails = true;\n      this.isShow = true;\n      this.syryDialog = false;\n      //清空表单\n      this.form = {};\n      this.form.ishg = 6;\n    },\n\n    //删除按钮\n    deleteRow(objId) {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          removeSybgjlData(JSON.stringify(objId)).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    //表格多选框\n    handelSelectChange(rows) {\n      this.selectRows = rows;\n      console.log(this.selectRows, \"123\");\n    },\n    //取消按钮\n    close() {\n      this.isShowDetails = false;\n    },\n    //保存按钮\n    save() {\n      this.$refs.form.validate(valid => {\n        if (valid) {\n          saveOrUpdateSybgjl(this.form).then(res => {\n            if (res.code == \"0000\") {\n              this.$message.success(\"操作成功！\");\n              this.isShowDetails = false;\n              this.getData();\n            }\n          });\n        }\n      });\n    },\n\n    //上报、提交审核\n    async commitSh() {\n      let type = \"complete\";\n      if (this.ishg == 1) {\n        //上报\n        //当前状态为新建，下一环节班组长审批\n        this.processData.variables.ishg = 5;\n        this.processData.variables.title = \"班组长审批\";\n        this.processData.defaultFrom = true;\n        this.processData.jxgs = false;\n      } else if (this.ishg == 5) {\n        //当前状态为班组长审批，下一环节分公司审核\n        this.processData.variables.ishg = 2;\n        this.processData.variables.title = \"分公司审核\";\n        this.processData.defaultFrom = true;\n        this.processData.jxgs = false;\n      } else if (this.ishg == 2) {\n        //当前状态为分公司审核，下一环节属地单位审核\n        this.processData.variables.ishg = 7;\n        this.processData.variables.title = \"属地单位审核\";\n        this.processData.defaultFrom = true;\n        this.processData.jxgs = false;\n        type = \"completeMany\"; //一发多收\n      } else if (this.ishg == 7) {\n        this.processData.variables.ishg = 3;\n        this.processData.variables.title = \"生产科专工审核\";\n        this.processData.defaultFrom = true;\n        this.processData.jxgs = false;\n      } else if (this.ishg == 3) {\n        this.processData.variables.ishg = 3;\n        this.processData.variables.title = \"合格验收\";\n        this.processData.defaultFrom = false;\n        this.processData.jxgs = false;\n      } else if (this.ishg == 4) {\n        //当前状态关闭\n        this.$message.warning(\"当前流程已关闭！！无法提交审核\");\n        return;\n      }\n      console.log(\"this.clickRow\", this.clickRow);\n      //开始执行请求\n      this.getSbFsBj(\n        { data: this.clickRow, type: type },\n        {\n          defaultForm: this.processData.defaultFrom,\n          jxgs: this.processData.jxgs\n        }\n      );\n      //详情框关闭\n      this.closeYlDialog();\n      //恢复分页\n      this.tableAndPageInfo.pager.pageResize = \"Y\";\n      //重新查数据\n      await this.getData();\n    },\n    //流程图片查看\n    showProcessImg(row) {\n      this.openLoadingImg = true;\n      this.imgSrc =\n        \"/activiti-api/process/read-resource?processDefinitionKey=sybgsh&businessKey=\" +\n        row.objId +\n        \"&t=\" +\n        new Date().getTime();\n    },\n\n    //上报发送办结\n    getSbFsBj(args, isShow) {\n      let row = { ...args.data };\n      this.activitiOption.title = args.title;\n      this.processData.variables.zy = row.sjzy;\n      if (args.type === \"complete\") {\n        this.processData.defaultFrom = isShow.defaultForm; //是否显示选人员\n        this.processData.processType = \"complete\";\n        this.processData.jxgs = isShow.jxgs;\n        this.processData.businessKey = row.objId;\n        this.processData.variables.pass = true;\n      } else if (args.type === \"completeMany\") {\n        //一发多收\n        this.processData.defaultFrom = isShow.defaultForm; //是否显示选人员\n        this.processData.processType = \"completeMany\";\n        this.processData.jxgs = isShow.jxgs;\n        this.processData.businessKey = row.objId;\n        this.processData.variables.pass = true;\n      } else {\n        this.processData.variables.title = \"回退原因提报\";\n        this.processData.defaultFrom = true;\n        this.processData.processType = \"rollback\";\n        this.processData.businessKey = row.objId;\n        this.processData.variables.pass = false;\n      }\n      this.isShowActiviti = true;\n    },\n    //回退按钮\n    handleTh(type) {\n      this.getSbFsBj(\n        { type: type, data: this.clickRow },\n        { defaultForm: false }\n      );\n    },\n\n    async showTimeLine(row) {\n      let { code, data } = await HistoryList({ businessKey: row.objId });\n      this.timeData = data;\n      this.timeLineShow = true;\n    },\n\n    //关闭工作流弹窗\n    closeActiviti() {\n      this.isShowActiviti = false;\n    },\n\n    //关闭时间线查看页面\n    colseTimeLine() {\n      this.timeLineShow = false;\n    },\n\n    //工作流回传数据\n    async todoResult(data) {\n      console.log(\"this.data\", data);\n      switch (data.activeTaskName) {\n        case \"试验报告填写\":\n          this.ishg = 1;\n          break;\n        case \"领导审批\":\n          this.ishg = 2;\n          break;\n        case \"属地单位审批\":\n          this.ishg = 7;\n          break;\n        case \"生产科专工审批\":\n          this.ishg = 3;\n          break;\n        case \"结束\":\n          this.ishg = 4;\n          break;\n        case \"班组审批\":\n          this.ishg = 5;\n          break;\n      }\n      let row = {};\n      if (this.ishg == 2) {\n        row = {\n          objId: data.businessKey,\n          ishg: this.ishg,\n          ssdwld: data.nextUser,\n          bgrq: formatterDateTime(new Date(), \"yyyy-MM-dd\") //设置报告日期字段，为批准人审批时间\n        };\n      } else if (this.ishg == 3) {\n        row = {\n          objId: data.businessKey,\n          ishg: this.ishg,\n          sckzg: data.nextUser\n        };\n      } else if (this.ishg == 5) {\n        row = {\n          objId: data.businessKey,\n          ishg: this.ishg,\n          bzzsp: data.nextUserNickName\n        };\n      } else if (this.ishg == 7) {\n        row = {\n          objId: data.businessKey,\n          ishg: this.ishg,\n          sddwshr: data.nextUser\n        };\n      } else {\n        row = { objId: data.businessKey, ishg: this.ishg };\n      }\n      console.log(\"row\", row);\n      updateSybgjl(row).then(res => {\n        if (res.code === \"0000\") {\n          this.$message.success(\"操作成功\");\n          //重置page页从1开始\n          this.tableAndPageInfo.pager.pageResize = \"Y\";\n          this.getData();\n        }\n      });\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.button-group {\n  padding-left: 30px;\n  padding-right: 30px;\n  display: flex;\n  justify-content: flex-end;\n}\n\n// #h2_table,\n// #h3_table {\n//   border-bottom: 1px solid #000;\n//   tr {\n//     //td:nth-child(1){\n//     //  border-left:none;\n//     //}\n//     td {\n//       border-left: 1px solid #000;\n//       border-top: 1px solid #000\n//     }\n//   }\n//   text-align: center;\n// }\n\n// #h1_table {\n//   width: 100%;\n//   text-align: center;\n//   //border-right:1px solid #000;\n//   border-bottom: 1px solid #000;\n\n//   tr {\n//     line-height: 35px;\n\n//     //td:nth-child(1){\n//     //  border-left:none;\n//     //}\n//     td {\n//       border-left: 1px solid #000;\n//       border-top: 1px solid #000\n//     }\n//   }\n// }\n\n// #saveCont {\n//   border: 1px solid #000;\n//   width: 100%;\n//   height: 60vh;\n//   overflow: auto;\n// }\n\n// .printTitle {\n//   line-height: 35px;\n// }\n\n// /deep/ #h2_table tr,\n// /deep/ #h3_table tr {\n//   height: 35px;\n// }\n\n// /deep/ #h2_table td,\n// /deep/ #h3_table td {\n//   border: 1px solid #000\n// }\n\n// /deep/ #h2_table input,\n// /deep/ #h3_table input {\n//   display: inline-block;\n//   height: 35px;\n// }\n</style>\n"]}]}