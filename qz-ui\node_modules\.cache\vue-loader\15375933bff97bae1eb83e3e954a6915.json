{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\zxmInfo.vue?vue&type=style&index=1&lang=css&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\zxmInfo.vue", "mtime": 1706897323692}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KI21weHFfcmlnaHQgdGQgew0KICBib3JkZXI6IDFweCBzb2xpZCAjMDAwOw0KICBoZWlnaHQ6IDM1cHg7DQogIGxpbmUtaGVpZ2h0OiAzNXB4Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQp9DQojbXB4cV9yaWdodCB0ciB7DQogIGhlaWdodDogMzVweDsNCn0NCiNtcHhxX3JpZ2h0IC5hdGMgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjMTFiYTZkOw0KfQ0KI21weHFfcmlnaHQgLmlucHV0X2NscyB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgYm9yZGVyOiBub25lOw0KICB3aWR0aDogOTklOw0KICBoZWlnaHQ6IDk5JTsNCn0NCg=="}, {"version": 3, "sources": ["zxmInfo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAysBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "zxmInfo.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk", "sourcesContent": ["<template>\r\n  <div class=\"syxmxq_info\">\r\n    <table id=\"mpxq_right\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\"></table>\r\n\r\n    <div id=\"syxmxq_left\">\r\n      <ul class=\"ul2_cont\">\r\n        <li>单元格信息</li>\r\n        <li>\r\n          单元格类型：<el-input\r\n            v-model=\"nrlx\"\r\n            placeholder=\"\"\r\n            class=\"inp1\"\r\n            disabled\r\n            @input=\"val => checkInput(val, 'nrlx')\"\r\n          ></el-input>\r\n        </li>\r\n        <li>\r\n          本次试验数值：<el-input\r\n            v-model=\"text\"\r\n            placeholder=\"\"\r\n            class=\"inp1\"\r\n            disabled\r\n            @input=\"val => checkInput(val, 'text')\"\r\n          ></el-input>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n\r\n    <div id=\"syxmxq_button\" v-show=\"changeTr.id\">\r\n      <dataChart :cell-id=\"changeTr.id\"></dataChart>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { Loading } from \"element-ui\";\r\nimport {\r\n  resetCells,\r\n  createTable,\r\n  mergeCells,\r\n  editCells,\r\n  getCells\r\n} from \"@/api/dagangOilfield/bzgl/sympk/sympInfo\";\r\nimport { getTable } from \"@/api/dagangOilfield/bzgl/sympk/sympk\";\r\nimport { getBwSelect, getZxmSelect } from \"@/api/dagangOilfield/bzgl/syxm\";\r\nimport dataChart from \"@/views/dagangOilfield/bzgl/sybzk/dataChart.vue\";\r\nexport default {\r\n  components: { dataChart },\r\n  props: {\r\n    mpData: {\r\n      type: Object\r\n    },\r\n    mxData: {\r\n      type: Array\r\n    }\r\n  },\r\n  name: \"zxmInfo\",\r\n  data() {\r\n    return {\r\n      //初始表格的行数 列数\r\n      hs: \"\",\r\n      ls: \"\",\r\n      //初始合并行数 列数\r\n      addhs: \"\",\r\n      addls: \"\",\r\n      nrlx: \"\",\r\n      text: \"\",\r\n      //一行的数据\r\n      cellData: \"\",\r\n      //选中合并行、列的tr\r\n      changeTr: \"\",\r\n      //查询条件\r\n      params: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        mpid: \"\",\r\n        zy: \"\",\r\n        sblxbm: \"\",\r\n        zxmId: \"\"\r\n      },\r\n      loading: null, //遮罩层\r\n      tdWidth: 0, //一个单元格所占宽度\r\n      tdMap: new Map(), //用于存放被合并或拆分的单元格（key:当前点击的单元格,value:被处理过的单元格数组）\r\n      tableData: this.mxData, //表格数据\r\n      sblxbm: undefined, //设备类型编码\r\n\r\n      title: \"单元格属性定义\",\r\n      form: {\r\n        objId: undefined,\r\n        readonly: undefined,\r\n        nrlx: undefined,\r\n        bwId: undefined,\r\n        zxmId: undefined,\r\n        nrbs: undefined\r\n      },\r\n      show: false,\r\n      bwList: [], //试验部位\r\n      zxmmDataList: [], //子项目所有结果数据\r\n      zxmmcList: [], //子项目\r\n      hidde: false,\r\n      hiddebw: false,\r\n      hiddezxm: false,\r\n      nrlxList: [\r\n        { label: \"静态文本\", value: \"静态文本\" },\r\n        { label: \"试验子项目\", value: \"试验子项目\" },\r\n        { label: \"试验部位\", value: \"试验部位\" },\r\n        { label: \"试验数据\", value: \"试验数据\" }\r\n      ],\r\n      readonlyList: [\r\n        { label: \"是\", value: \"Y\" },\r\n        { label: \"否\", value: \"N\" }\r\n      ]\r\n    };\r\n  },\r\n\r\n  mounted() {\r\n    //获取表格初始行数和列数\r\n    this.initTableData();\r\n  },\r\n  methods: {\r\n    //获取铭牌内容数据\r\n    initTableData() {\r\n      //初始化遮罩层\r\n      this.loading = Loading.service({\r\n        text: \"加载中，请稍后...\",\r\n        background: \"rgba(109,106,106,0.35)\"\r\n      });\r\n      this.hs =\r\n        typeof this.mpData.AHs != \"undefined\"\r\n          ? this.mpData.AHs\r\n          : this.mpData.aHs;\r\n      this.ls =\r\n        typeof this.mpData.ALs != \"undefined\"\r\n          ? this.mpData.ALs\r\n          : this.mpData.aLs;\r\n      this.sblxbm = this.mpData.sblxbm;\r\n      //更新输入框的值\r\n      this.updateInputValue([\"hs\", \"ls\"]);\r\n      this.processTable();\r\n      this.loading.close(); //关闭遮罩层\r\n    },\r\n\r\n    selectzxmvalue(val) {\r\n      let obj = {};\r\n      obj = this.zxmmcList.find(item => {\r\n        return item.value === val;\r\n      });\r\n      this.form.nrbs = obj.laber;\r\n    },\r\n\r\n    selectbwvalue(val) {\r\n      let obj = {};\r\n      obj = this.bwList.find(item => {\r\n        return item.value === val;\r\n      });\r\n      this.form.nrbs = obj.laber;\r\n    },\r\n\r\n    selectvalue(val) {\r\n      const { value, label } = val;\r\n      if (label == \"静态文本\") {\r\n        this.hidde = true;\r\n        this.hiddebw = false;\r\n        this.hiddezxm = false;\r\n        this.form.nrlx = \"静态文本\";\r\n      }\r\n      if (label == \"试验子项目\") {\r\n        this.hiddezxm = true;\r\n        this.hiddebw = false;\r\n        this.hidde = false;\r\n        this.form.nrlx = \"试验子项目\";\r\n      }\r\n      if (label == \"试验部位\") {\r\n        this.hiddebw = true;\r\n        this.hiddezxm = false;\r\n        this.hidde = false;\r\n        this.form.nrlx = \"试验部位\";\r\n      }\r\n      if (label == \"试验数据\") {\r\n        this.hidde = false;\r\n        this.hiddebw = false;\r\n        this.hiddezxm = false;\r\n        this.form.nrlx = \"试验数据\";\r\n      }\r\n    },\r\n\r\n    //获取部位下拉框\r\n    getSybw() {\r\n      getBwSelect({ sblxbm: this.sblxbm }).then(res => {\r\n        this.bwList = res.data;\r\n      });\r\n    },\r\n\r\n    // 获取试验子项目下拉框数据\r\n    getSyzxm() {\r\n      getZxmSelect().then(res => {\r\n        this.zxmmcList = res.data;\r\n      });\r\n    },\r\n\r\n    //根据行数和列数创建表格\r\n    processTable() {\r\n      var tbody = document.getElementById(\"mpxq_right\");\r\n      if (tbody != null) {\r\n        tbody.innerHTML = \"\";\r\n        let hs = this.hs;\r\n        let ls = this.ls;\r\n        this.tdWidth = 100 / Number(ls);\r\n        let str = \"\";\r\n\r\n        for (let i = 0; i < hs; i++) {\r\n          let temp = \"<tr>\";\r\n          for (let j = 0; j < this.tableData.length; j++) {\r\n            let item = this.tableData[j];\r\n            let sjlx = item.sjlx; //数据类型\r\n            let text = item.text;\r\n            let nrbs = item.nrbs == null ? (text ? text : \"\") : item.nrbs;\r\n            if (item.rowindex === i.toString()) {\r\n              temp +=\r\n                \"<td class='trName' id='\" +\r\n                item.objId +\r\n                \"' style='width: \" +\r\n                this.tdWidth * item.colspan +\r\n                \"%' rowspan='\" +\r\n                item.rowspan +\r\n                \"' colspan='\" +\r\n                item.colspan +\r\n                \"'>\" +\r\n                nrbs +\r\n                \"</td>\";\r\n            }\r\n          }\r\n          temp += \"</tr>\";\r\n          str += temp;\r\n        }\r\n        tbody.innerHTML = str;\r\n        // //给循环出来的单元格加上点击事件\r\n        this.addClickEvent();\r\n      }\r\n    },\r\n\r\n    //手动创建表格\r\n    createTable() {\r\n      //初始化遮罩层\r\n      this.loading = Loading.service({\r\n        text: \"加载中，请稍后...\",\r\n        background: \"rgba(109,106,106,0.35)\"\r\n      });\r\n      let params = JSON.stringify({\r\n        objId: this.mpData.objId, //铭牌id\r\n        aHs: Number(this.hs), //行数\r\n        aLs: Number(this.ls), //列数\r\n        lbbs: \"A\" //类别标识，表示修改的A表格\r\n      });\r\n      createTable(params).then(res => {\r\n        if (res.code === \"0000\") {\r\n          this.updateTable();\r\n        } else {\r\n          this.$message.error(\"操作失败\");\r\n          this.loading.close(); //关闭遮罩层\r\n        }\r\n      });\r\n    },\r\n    //给循环出来的单元格加上点击事件\r\n    addClickEvent() {\r\n      let trArr = document.getElementsByClassName(\"trName\"); //不可编辑的单元格\r\n      let inputArr = document.getElementsByClassName(\"input_cls\"); //可编辑的单元格\r\n      let that = this;\r\n      if (trArr != null) {\r\n        //循环所有的tr\r\n        for (let i = 0; i < trArr.length; i++) {\r\n          trArr[i].onclick = function() {\r\n            that.changeTr = this;\r\n            that.addhs = that.changeTr.rowSpan;\r\n            that.addls = that.changeTr.colSpan;\r\n            that.cellData = that.getCellEle(that.changeTr.id);\r\n            that.nrlx = that.cellData.nrlx;\r\n            that.text = that.cellData.text;\r\n          };\r\n        }\r\n      }\r\n      if (inputArr != null) {\r\n        //循环所有的tr\r\n        for (let i = 0; i < inputArr.length; i++) {\r\n          inputArr[i].onclick = function() {\r\n            that.changeTr = this;\r\n            that.addhs = that.changeTr.rowSpan;\r\n            that.addls = that.changeTr.colSpan;\r\n            that.cellData = that.getCellEle();\r\n            that.nrlx = that.cellData.nrlx;\r\n            that.text = that.cellData.text;\r\n            console.log(that.cellData);\r\n          };\r\n        }\r\n      }\r\n    },\r\n    //合并拆分保存\r\n    saveChangeTable() {\r\n      //初始化遮罩层\r\n      this.loading = Loading.service({\r\n        text: \"加载中，请稍后...\",\r\n        background: \"rgba(109,106,106,0.35)\"\r\n      });\r\n\r\n      let params = JSON.stringify({\r\n        objId: this.changeTr.id,\r\n        rowspan: this.addhs,\r\n        colspan: this.addls\r\n      });\r\n      //先请求接口，如果后台可以执行合并或拆分，则将最新的表格数据请求回来进行前端展示\r\n      mergeCells(params).then(res => {\r\n        if (res.code === \"0000\") {\r\n          this.updateTable();\r\n        } else {\r\n          this.$message.error(\"操作失败\");\r\n          this.loading.close(); //关闭遮罩层\r\n        }\r\n      });\r\n    },\r\n    //处理合并或拆分\r\n    processTr(ids) {\r\n      //点击的单元格id\r\n      let clickId = this.changeTr.id;\r\n      let arr1 = []; //需要重新设置map的数组\r\n      //如果之前已经处理过该单元格,则先将其还原\r\n      if (this.tdMap.has(clickId)) {\r\n        this.tdMap.get(clickId).forEach(item => {\r\n          if (item != null) {\r\n            this.resetCell(item);\r\n            item.style.display = \"table-cell\";\r\n          }\r\n        });\r\n        //操作完后将数据从map中删除\r\n        this.tdMap.delete(clickId);\r\n      }\r\n      let processEle = []; //被处理的元素\r\n\r\n      //现将连带受影响的单元格还原，再进行隐藏处理\r\n      if (ids.length > 0) {\r\n        //执行还原\r\n        ids.forEach(id1 => {\r\n          let ele = document.getElementById(id1);\r\n          //如果此次处理的单元格中有已经被处理过的，先将其还原\r\n          if (this.tdMap.has(id1)) {\r\n            this.tdMap.get(id1).forEach(item => {\r\n              this.resetCell(item);\r\n              item.style.display = \"table-cell\";\r\n            });\r\n            //操作完后将数据从map中删除\r\n            this.tdMap.delete(id1);\r\n          }\r\n          //处理被连带的已经合并过的单元格\r\n          if (ele) {\r\n            let className = ele.className;\r\n            if (this.tdMap.has(className)) {\r\n              let mergeCell = document.getElementById(className); //被连带的已经合并过的cell\r\n              if (mergeCell) {\r\n                this.resetCell(mergeCell);\r\n              }\r\n              this.tdMap.get(className).forEach(item => {\r\n                //需要把此次要隐藏的单元格排除掉，不然隐藏完下次循环又会放出来\r\n                if (!ids.includes(item)) {\r\n                  item.style.display = \"table-cell\";\r\n                } else {\r\n                  arr1.push(item);\r\n                }\r\n              });\r\n              //处理完成后，更新map中的值，将处理过的排除掉\r\n              if (arr1.length > 0) {\r\n                this.tdMap.set(className, arr1);\r\n              } else {\r\n                //操作完后将数据从map中删除\r\n                this.tdMap.delete(className);\r\n              }\r\n            }\r\n          }\r\n        });\r\n        //执行隐藏\r\n        ids.forEach(id => {\r\n          let ele = document.getElementById(id);\r\n          //将多余的单元格隐藏\r\n          if (ele) {\r\n            processEle.push(ele); //添加数据保存到map中\r\n\r\n            document.getElementById(id).style.display = \"none\";\r\n            //将className设置给被操作的单元格，方便下次有连带操作时对单元格进行处理\r\n            document.getElementById(id).className = clickId;\r\n          }\r\n        });\r\n        //重新设置map中的值\r\n        this.tdMap.set(clickId, processEle);\r\n      }\r\n    },\r\n    //取消更改的合并行、列数\r\n    clearChangeTable() {},\r\n\r\n    //关闭弹窗\r\n    getInsterClose() {\r\n      this.show = false;\r\n      this.form = {\r\n        objId: undefined,\r\n        readonly: undefined,\r\n        nrlx: undefined,\r\n        nrbs: undefined,\r\n        zxmId: undefined,\r\n        bwId: undefined\r\n      };\r\n    },\r\n\r\n    //进行合并或拆分操作\r\n    mergeTable(hs, ls, addh, addl) {\r\n      if (hs === 1) {\r\n        //合并列\r\n        if (ls >= 1) {\r\n          this.mergeCells(addh, addl, hs, ls);\r\n        }\r\n      } else {\r\n        if (hs > 1) {\r\n          //多行\r\n          //合并行\r\n          if (ls === 1) {\r\n            this.mergeRows(addh, addl, hs, ls);\r\n          } else if (ls > 1) {\r\n            //合并多行多列\r\n            this.mergeRowsAndCells(addh, addl, hs, ls);\r\n          }\r\n        }\r\n      }\r\n      //要合并的单元格进行合并\r\n      this.changeTr.style.width = this.tdWidth * ls + \"%\"; //设置合并后的单元格宽度\r\n      this.changeTr.rowSpan = this.addhs;\r\n      this.changeTr.colSpan = this.addls;\r\n    },\r\n    /**\r\n     * 第一种情况，合并列（一行多列）\r\n     * @param h 当前元素所在行\r\n     * @param l 当前元素所在列\r\n     * @param hs 要合并的行数\r\n     * @param ls 要合并的列数\r\n     */\r\n    mergeCells(h, l, hs, ls) {\r\n      let removeIds = []; //要删除的元素的id数组\r\n      let ls_xh = ls; //要循环的列数\r\n      if (ls > this.ls - l) {\r\n        //不能超过剩余可操作的列数\r\n        ls_xh = this.ls - l;\r\n      }\r\n      for (let i = 1; i < ls_xh; i++) {\r\n        removeIds.push(h + \"|\" + (l + i));\r\n      }\r\n      //删除多余单元格\r\n      this.processTr(removeIds);\r\n    },\r\n    /**\r\n     * 第二种情况，合并行（多行一列）\r\n     * @param h 当前元素所在行\r\n     * @param l 当前元素所在列\r\n     * @param hs 要合并的行数\r\n     * @param ls 要合并的列数\r\n     */\r\n    mergeRows(h, l, hs, ls) {\r\n      let removeIds = []; //要删除的元素的id数组\r\n      let hs_xh = hs; //要循环的行数\r\n      if (hs > this.hs - h) {\r\n        //不能超过剩余可操作的行数\r\n        hs_xh = this.hs - h;\r\n      }\r\n      console.log(\"hs_xh\", hs_xh);\r\n      for (let i = 1; i < hs_xh; i++) {\r\n        removeIds.push(h + i + \"|\" + l);\r\n      }\r\n      //删除多余单元格\r\n      this.processTr(removeIds);\r\n    },\r\n    /**\r\n     * 第三种情况，合并多行多列\r\n     * @param h 当前元素所在行\r\n     * @param l 当前元素所在列\r\n     * @param hs 要合并的行数\r\n     * @param ls 要合并的列数\r\n     */\r\n    mergeRowsAndCells(h, l, hs, ls) {\r\n      let removeIds = []; //要删除的元素的id数组\r\n      let removeId = \"\";\r\n      //先循环行（从当前行开始循环）\r\n      for (let j = 0; j < hs; j++) {\r\n        //循环列\r\n        for (let i = 0; i < ls; i++) {\r\n          //从当前列循环\r\n          removeId = h + j + \"|\" + (l + i);\r\n          //将当前单元格排除掉\r\n          if (removeId !== h + \"|\" + l) {\r\n            removeIds.push(removeId);\r\n          }\r\n        }\r\n      }\r\n      //删除多余单元格\r\n      this.processTr(removeIds);\r\n    },\r\n    //更新输入框的值\r\n    updateInputValue(arrs) {\r\n      for (let i = 0; i < arrs.length; i++) {\r\n        let ele = document.getElementById(arrs[i]);\r\n        if (ele != null && typeof ele != \"undefined\") {\r\n          switch (arrs[i]) {\r\n            case \"hs\":\r\n              ele.value = this.hs;\r\n              break;\r\n            case \"ls\":\r\n              ele.value = this.ls;\r\n              break;\r\n            case \"addhs\":\r\n              ele.value = this.addhs;\r\n              break;\r\n            case \"addls\":\r\n              ele.value = this.addls;\r\n              break;\r\n          }\r\n        }\r\n      }\r\n    },\r\n    //重置单元格内容\r\n    resetTable() {\r\n      //初始化遮罩层\r\n      this.loading = Loading.service({\r\n        text: \"加载中，请稍后...\",\r\n        background: \"rgba(109,106,106,0.35)\"\r\n      });\r\n      let objId = this.changeTr.id;\r\n      let params = this.getCellEle(objId);\r\n      resetCells(params).then(res => {\r\n        if (res.code === \"0000\") {\r\n          this.updateTable();\r\n        } else {\r\n          this.$message.error(\"操作失败\");\r\n        }\r\n        this.loading.close(); //关闭遮罩层\r\n      });\r\n    },\r\n    //单元格属性编辑并保存\r\n    saveTdValue() {\r\n      this.show = true;\r\n      this.getSybw();\r\n      this.getSyzxm();\r\n      //   this.form.readonly = this.cellData.readonly;\r\n      //   this.form.nrlx = this.cellData.nrlx;\r\n      //   this.form.nrbs =this.cellData.nrbs;\r\n      //   this.form.objId =this.cellData.objId;\r\n      //初始化遮罩层\r\n      // this.loading = Loading.service({\r\n      //   text:\"加载中，请稍后...\",\r\n      //   background:'rgba(109,106,106,0.35)',\r\n      // })\r\n      // let objId = this.changeTr.id;\r\n      // let val = this.changeTr.getElementsByTagName(\"input\")[0].value;\r\n      // let params = this.getCellEle(objId);\r\n      // params.nrbs = val;\r\n      // editCells(params).then(res=>{\r\n      //   if(res.code==='0000'){\r\n      //     this.updateTable();\r\n      //   }else{\r\n      //     this.$message.error('操作失败');\r\n      //     this.loading .close();//关闭遮罩层\r\n      //   }\r\n      // })\r\n    },\r\n\r\n    //单元格属性编辑\r\n    async saveRow() {\r\n      try {\r\n        this.form.objId = this.changeTr.id;\r\n        this.form.mpid = this.cellData.mpid;\r\n        console.log(\"--form--\" + this.form);\r\n        let { code } = await editCells(this.form);\r\n        if (code === \"0000\") {\r\n          this.updateTable();\r\n          this.$message.success(\"操作成功\");\r\n        }\r\n      } catch (e) {\r\n        console.log(e);\r\n      }\r\n      this.show = false;\r\n    },\r\n\r\n    //重置单元格属性（宽度，合并行数，合并列数）\r\n    resetCell(ele) {\r\n      if (ele) {\r\n        ele.style.width = this.tdWidth + \"%\";\r\n        ele.rowSpan = \"1\";\r\n        ele.colSpan = \"1\";\r\n      }\r\n    },\r\n    //输入框校验\r\n    checkInput(val, changeType) {\r\n      switch (changeType) {\r\n        case \"hs\":\r\n          this.hs = val;\r\n          break;\r\n        case \"ls\":\r\n          this.ls = val;\r\n          break;\r\n        case \"addhs\":\r\n          this.addhs = val;\r\n          break;\r\n        case \"addls\":\r\n          this.addls = val;\r\n          break;\r\n        case \"nrlx\":\r\n          this.nrlx = val;\r\n          break;\r\n      }\r\n    },\r\n    //获取单元格明细数据\r\n    getCellDetail(hs, ls) {\r\n      let result = null;\r\n      this.tableData.forEach(item => {\r\n        if (\r\n          item.rowindex === hs.toString() &&\r\n          item.colindex === ls.toString()\r\n        ) {\r\n          result = item;\r\n          if (result.nrbs == null) {\r\n            result.nrbs = \"\";\r\n          }\r\n        }\r\n      });\r\n      return result;\r\n    },\r\n    //获取某个单元格对象\r\n    getCellEle(objId) {\r\n      let result = null;\r\n      this.tableData.forEach(item => {\r\n        if (item.objId === objId) {\r\n          result = item;\r\n        }\r\n      });\r\n      return result;\r\n    },\r\n    //获取最新的表格并重新渲染\r\n    updateTable() {\r\n      let param = JSON.stringify({\r\n        obj_id: this.mpData.objId,\r\n        lbbs: \"A\"\r\n      });\r\n      //获取最新的表格数据\r\n      getTable(param).then(res1 => {\r\n        if (res1.code === \"0000\") {\r\n          this.tableData = res1.data;\r\n          //根据最新的表格数据重新画\r\n          this.processTable();\r\n          this.$message.success(res1.msg);\r\n        } else {\r\n          this.$message.error(\"无法获取更新后的表格数据！\");\r\n        }\r\n        this.loading.close(); //关闭遮罩层\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.syxmxq_info {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n#syxmxq_left {\r\n  margin-left: 20px;\r\n  ul {\r\n    list-style-type: none;\r\n    margin: 0;\r\n    padding: 8px;\r\n  }\r\n  border: 1px solid #0cc283;\r\n  width: 28%;\r\n  li:nth-child(1) {\r\n    font-weight: 700;\r\n  }\r\n  li {\r\n    line-height: 48px;\r\n    padding-left: 8px;\r\n    .el-input {\r\n      width: 70%;\r\n    }\r\n  }\r\n}\r\n#syxmxq_button {\r\n  margin-top: 20px;\r\n  ul {\r\n    list-style-type: none;\r\n    margin: 0;\r\n    padding: 8px;\r\n  }\r\n  border: 1px solid #0cc283;\r\n  width: 99.5%;\r\n}\r\n.change_btn {\r\n  margin-top: 10px !important;\r\n  height: 36px !important;\r\n}\r\n.change_btn:nth-child(1) {\r\n  margin-left: 29%;\r\n}\r\n.change_btn:nth-child(2) {\r\n  margin-left: 20%;\r\n}\r\n#mpxq_right {\r\n  width: 70%;\r\n  height: 180px;\r\n  border: 1px solid #000;\r\n}\r\n</style>\r\n<style>\r\n#mpxq_right td {\r\n  border: 1px solid #000;\r\n  height: 35px;\r\n  line-height: 35px;\r\n  text-align: center;\r\n}\r\n#mpxq_right tr {\r\n  height: 35px;\r\n}\r\n#mpxq_right .atc {\r\n  background-color: #11ba6d;\r\n}\r\n#mpxq_right .input_cls {\r\n  text-align: center;\r\n  border: none;\r\n  width: 99%;\r\n  height: 99%;\r\n}\r\n</style>\r\n"]}]}