{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\gswh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\gswh.vue", "mtime": 1706897323215}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["gswh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAwBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "gswh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk", "sourcesContent": ["<template>\n  <el-row :gutter=\"16\">\n    <el-col :span=\"12\">\n      <div class=\"txtTitle\">\n        <span @click=\"click('hsk')\" :class=\"this.flag === 'hsk'?'tabActive':'noActive'\" class=\"oneBtn\">\n          <span class=\"allBtn\">函数库</span>\n        </span>\n        <span @click=\"click('syxm')\" :class=\"this.flag === 'syxm'?'tabActive':'noActive'\" class=\"twoBtn\">\n          <span class=\"allBtn\">试验项目表格</span>\n        </span>\n      </div>\n      <el-aside width=\"15vw\" style=\"background-color: rgb(238, 241, 246)\" v-show=\"this.flag === 'hsk'\">\n        <el-tree :data=\"treeData\" :props=\"defaultProps\" @node-click=\"handleNodeClick\"\n                 style=\"line-height: 2vh;height: 47vh; padding:10px;\"></el-tree>\n      </el-aside>\n      <table id=\"mpxq_right\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" v-show=\"this.flag === 'syxm'\"></table>\n    </el-col>\n    <!--  脚本编辑  -->\n    <el-col :span=\"12\">\n      <GsEdit ref=\"editJb\" :jb=\"jb\" :tableData=\"tableData\" @setJbVal=\"setJbVal\" @jbClose=\"jbClose\"></GsEdit>\n    </el-col>\n  </el-row>\n</template>\n<script>\nimport GsEdit from \"@/views/dagangOilfield/bzgl/sbztpjbzk/gswh_edit\";\nimport {getTreeHsk} from \"@/api/dagangOilfield/bzgl/hskwh\";\nimport {getById, getTable} from \"@/api/dagangOilfield/bzgl/sympk/sympk\";\nimport {getPageList} from \"@/api/dagangOilfield/bzgl/sbztpjbzk/ztxxsjgl\";\nimport {Loading} from \"element-ui\";\n\nexport default {\n  name: 'gswh',\n  components: {GsEdit},\n  props: {\n    jb: {\n      type: String,\n      default:'',\n    },\n    syxm:{\n      type: String,\n      default:'',\n    }\n  },\n  data() {\n    return {\n      mpData:{},\n      //初始表格的行数 列数\n      hs: \"\",\n      ls: \"\",\n      tdWidth: 0, //一个单元格所占宽度\n      tableData:[],\n      //树数据\n      treeData: [],\n      defaultProps: {\n        children: 'children',\n        label: 'label'\n      },\n      flag:'hsk'\n    };\n  },\n  async mounted() {\n    getTreeHsk().then(res => {\n      this.treeData = res.data;\n    });\n  },\n  watch: {\n    syxm: {\n      async handler(newVal, oldVal) {\n        if (newVal){\n          const {data, code} = await getById(newVal);\n          if (code === \"0000\") {\n            this.mpData = data;\n            this.initTableData();\n            //获取表格数据\n            getTable({obj_id: newVal, lbbs: \"A\"}).then((res1) => {\n              if (res1.code === \"0000\") {\n                this.tableData = res1.data;\n                this.processTable();\n              }\n            });\n          }\n        }\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  methods: {\n    //更新输入框的值\n    updateInputValue(arrs) {\n      for (let i = 0; i < arrs.length; i++) {\n        let ele = document.getElementById(arrs[i]);\n        if (ele != null && typeof ele != \"undefined\") {\n          switch (arrs[i]) {\n            case \"hs\":\n              ele.value = this.hs;\n              break;\n            case \"ls\":\n              ele.value = this.ls;\n              break;\n            case \"addhs\":\n              ele.value = this.addhs;\n              break;\n            case \"addls\":\n              ele.value = this.addls;\n              break;\n          }\n        }\n      }\n    },\n    //获取铭牌内容数据\n    initTableData() {\n\n      this.hs = typeof (this.mpData.AHs) != 'undefined'?this.mpData.AHs:this.mpData.aHs;\n      this.ls = typeof (this.mpData.ALs) != 'undefined'?this.mpData.ALs:this.mpData.aLs;\n      //更新输入框的值\n      this.updateInputValue([\"hs\", \"ls\"]);\n      this.processTable();\n    },\n    //根据行数和列数创建表格\n    processTable() {\n      var tbody = document.getElementById(\"mpxq_right\");\n      if (tbody != null) {\n        tbody.innerHTML = \"\";\n        let hs = this.hs;\n        let ls = this.ls;\n        this.tdWidth = 100 / Number(ls);\n        let str = \"\";\n\n        for (let i = 0; i < hs; i++) {\n          let temp = \"<tr>\";\n          for (let j = 0; j < this.tableData.length; j++) {\n            let item = this.tableData[j];\n            let nrbs = item.nrbs == null ? \"-\" : item.nrbs;\n            if (item.rowindex === i.toString()) {\n              temp +=\n                  \"<td class='trName' id='\" +\n                  item.objId +\n                  \"' style='width: \" +\n                  this.tdWidth * item.colspan +\n                  \"%' rowspan='\" +\n                  item.rowspan +\n                  \"' colspan='\" +\n                  item.colspan +\n                  \"'>\" +\n                  nrbs +\n                  \"</td>\";\n            }\n          }\n          temp += \"</tr>\";\n          str += temp;\n        }\n        tbody.innerHTML = str;\n     //给循环出来的单元格加上点击事件\n        this.addClickEvent();\n      }\n    },\n    //tab切换\n    click(mainTab){\n      this.flag = mainTab;\n    },\n    //树点击方法\n    handleNodeClick(data,node) {\n      if (node.level === 2) {\n        this.$refs.editJb.trTableClick(data.label+\"()\");\n      }\n    },\n\n    setJbVal(val) {\n      this.$emit('setJbVal', val);\n    },\n    //关闭脚本弹框\n    jbClose() {\n      this.$emit('jbClose');\n    },\n    //给循环出来的单元格加上点击事件\n    addClickEvent() {\n      let trArr = document.getElementsByClassName(\"trName\"); //不可编辑的单元格\n      let that = this;\n      if (trArr != null) {\n        //循环所有的tr\n        for (let i = 0; i < trArr.length; i++) {\n          trArr[i].ondblclick = function () {\n             that.getCellEle(this.id);\n          };\n        }\n      }\n    },\n\n    //获取某个单元格对象\n    getCellEle(objId) {\n      let result = null;\n      this.tableData.forEach((item) => {\n        if (item.objId === objId) {\n          result = item;\n        }\n      });\n      if (result.sjlx === \"STRING\"){\n        this.$refs.editJb.trTableClick(objId);\n      }else {\n          this.$message.warning(\"只能选择可编辑单元格！\")\n      }\n    },\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.jbwh_box {\n  padding: 20px 0 0 20px;\n}\n\n.jbwh_box1 {\n  margin-top: -18px;\n}\n\n.tabActive {\n  //width: 10%;\n  float: left;\n  color: #fff;\n  background: #02b988;\n  border-top: 0;\n}\n\n.noActive {\n  //width: 10%;\n  float: left;\n  background: #fff;\n  color: #545252;\n\n  &:hover {\n    background: #FFFFFF;\n    color: #359076;\n  }\n}\n\n.oneBtn {\n  margin-right: -15px;\n}\n\n.twoBtn {\n  transform: skewX(33deg);\n  border-right: 1px solid #9a989869;\n\n  .allBtn {\n    transform: skewX(-33deg);\n    display: inline-block;\n  }\n}\n\n</style>\n<style>\n#mpxq_right td {\n  border: 1px solid #000;\n  height: 35px;\n  line-height: 35px;\n  text-align: center;\n}\n#mpxq_right tr {\n  height: 35px;\n}\n#mpxq_right .atc {\n  background-color: #11ba6d;\n}\n#mpxq_right .input_cls {\n  text-align: center;\n  border: none;\n  width: 99%;\n  height: 99%;\n}\n</style>\n\n"]}]}