{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\symbwh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\symbwh.vue", "mtime": 1706897323686}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCi8v5byV5YWlanF1ZXJ5LOaaguaXtuayoeeUqAppbXBvcnQgJCBmcm9tICJqcXVlcnkiCmltcG9ydCB7CiAgYWRkTWJHbHhtQmF0Y2hUb01ieG0sCiAgZ2V0UGFnZURhdGFMaXN0VG9zeW1iLAogIGdldFN5bWJHbHN5eG1EYXRhTGlzdEJ5UGFnZSwKICBnZXRYbUxpcmFyeURhdGEsCiAgcmVtb3ZlLAogIHNhdmVPclVwZGF0ZSwKICBnZXRNYkdsTXBpbmZvRGF0YSwKICBnZXRNYkdsWG1BbmRCdywKICBnZXRNd3RVZFN5TXB4cUJ5TWJ6YiwKICByZW1vdmVTeW1iLAp9IGZyb20gJ0AvYXBpL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvc3ltYndoJwppbXBvcnQge2dldERldmljZUNsYXNzVHJlZU5vZGVCeVBpZH0gZnJvbSAnQC9hcGkvZGFnYW5nT2lsZmllbGQvYnpnbC9zYmx4d2gvc2JseHdoJwppbXBvcnQge2dldEdsU3l6eG1EYXRhTGlzdEJ5UGFnZX0gZnJvbSAnQC9hcGkvZGFnYW5nT2lsZmllbGQvYnpnbC9zeXhtJwppbXBvcnQgR2xzeW1wIGZyb20gJ0Avdmlld3MvZGFnYW5nT2lsZmllbGQvYnpnbC9zeWJ6ay9nbHN5bXAnCmltcG9ydCBzeW1id2hEeW1ibnIgZnJvbSAnQC92aWV3cy9kYWdhbmdPaWxmaWVsZC9iemdsL3N5YnprL3N5bWJ3aER5bWJucicKaW1wb3J0IHsgZ2V0UGFnZU5vRGF0YUxpc3QgfSBmcm9tICJAL2FwaS9kYWdhbmdPaWxmaWVsZC9iemdsL3N5bXBrL3N5bXBrIjsKaW1wb3J0IHsgZ2V0TXBtY0RhdGFCeUlkLHNhdmVNd3RVZFN5TWJtcCxkZWxldGVNd3RVZFN5TWJtcCB9IGZyb20gIkAvYXBpL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvc3ltYndoIjsKCgppbXBvcnQgaHRtbFRvUGRmIGZyb20gJ0AvdXRpbHMvcHJpbnQvaHRtbFRvUGRmJwppbXBvcnQgc3l4bSBmcm9tICIuL3N5eG0iOwppbXBvcnQgeyBnZXRDaGlsZHNWYWx1ZSwgZ2V0TW91bGRWYWx1ZSB9IGZyb20gJ0AvYXBpL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvc3liZ2xyJwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdzeWJ3aycsCiAgY29tcG9uZW50czoge0dsc3ltcCwgc3ltYndoRHltYm5yfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgY3VycmVudFVzZXI6IHRoaXMuJHN0b3JlLmdldHRlcnMubmFtZSwKICAgICAgLy/ln7rmnKzkv6Hmga/ooajmoLzmlbDmja4KICAgICAgdGFibGVEYXRhX2pieHg6IFsKICAgICAgICB7CiAgICAgICAgICBjb2x1bW5fMTogIuivlemqjOaAp+i0qCIsCiAgICAgICAgICBjb2x1bW5fMjogIuivlemqjOaXpeacnyIsCiAgICAgICAgICBjb2x1bW5fMzogIuivlemqjOS6uuWRmCIsCiAgICAgICAgICBjb2x1bW5fNDogIuivlemqjOWcsOeCuSIsCiAgICAgICAgICBjb2x1bW5fNTogIiIsCiAgICAgICAgICBjb2x1bW5fNjogIiIsCiAgICAgICAgICBjb2x1bW5fNzogIiIsCiAgICAgICAgICBjb2x1bW5fODogIiIsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBjb2x1bW5fMTogIuaKpeWRiuaXpeacnyIsCiAgICAgICAgICBjb2x1bW5fMjogIue8luWGmeS6uiIsCiAgICAgICAgICBjb2x1bW5fMzogIuWuoeaguOS6uiIsCiAgICAgICAgICBjb2x1bW5fNDogIuaJueWHhuS6uiIsCiAgICAgICAgICBjb2x1bW5fNTogIiIsCiAgICAgICAgICBjb2x1bW5fNjogIiIsCiAgICAgICAgICBjb2x1bW5fNzogIiIsCiAgICAgICAgICBjb2x1bW5fODogIiIsCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICBjb2x1bW5fMTogIuivlemqjOWkqeawlCIsCiAgICAgICAgICBjb2x1bW5fMjogIueOr+Wig+a4qeW6pu+8iOKEg++8iSIsCiAgICAgICAgICBjb2x1bW5fMzogIueOr+Wig+ebuOWvuea5v+W6pu+8iCXvvIkiLAogICAgICAgICAgY29sdW1uXzQ6ICLmipXov5Dml6XmnJ8iLAogICAgICAgICAgY29sdW1uXzU6ICIiLAogICAgICAgICAgY29sdW1uXzY6ICIiLAogICAgICAgICAgY29sdW1uXzc6ICIiLAogICAgICAgICAgY29sdW1uXzg6ICIiLAogICAgICAgIH0sCiAgICAgIF0sCiAgICAgIC8v6K6+5aSH6ZOt54mM6KGo5qC85pWw5o2uCiAgICAgIHRhYmxlRGF0YV9zYm1wOlt7CiAgICAgICAgJ2NvbHVtbl8xJzon6aKd5a6a55S15Y6LJywKICAgICAgICAnY29sdW1uXzInOiforr7lpIflnovlj7cnLAogICAgICAgICdjb2x1bW5fMyc6JycsCiAgICAgICAgJ2NvbHVtbl80JzonJywKICAgICAgICAnY29sdW1uXzUnOicnLAogICAgICAgICdjb2x1bW5fNic6JycsCiAgICAgIH0sXSwKICAgICAgLy/opoHlvqrnjq/nmoTor5XpqozooajmoLzmlbDmja4KICAgICAgYXJyOlt7dGl0bGU6IiIsLy/or5XpqozlkI3np7AKICAgICAgICB6eG1MaXN0OltdLC8v5a2Q6aG555uu5pWw5o2u77yI6KGo5aS077yJCiAgICAgICAgYndMaXN0OltdLC8v6YOo5L2N5pWw5o2u77yI56ys5LiA5YiX5byA5aS077yJCiAgICAgIH1dLAogICAgICAvL+S4i+i9veW8ueWHuuahhuaOp+WItgogICAgICBpc1Nob3dEb3duTG9hZERpYWxvZzogZmFsc2UsCiAgICAgIHByaW50T2JqOiB7CiAgICAgICAgaWQ6ICJwcmV2aWV3SWQiLCAvLyDlv4XloavvvIzmuLLmn5PmiZPljbDnmoTlhoXlrrnkvb/nlKgKICAgICAgICBwb3BUaXRsZTogIiZuYnNwOyIsIC8vCiAgICAgICAgcHJldmlld1RpdGxlOiAiJm5ic3A7IiwKICAgICAgICBwcmV2aWV3OiBmYWxzZSwKICAgICAgfSwKCiAgICAgIG1iSW5mbzoge30sCiAgICAgIC8v5omT5Y2w5YaF5a65ZGl25LitaWTlgLwKICAgICAgcHJldmlld0lkOiAiIiwKICAgICAgLy/lrprkuYnmqKHmnb/lhoXlrrnlvLnlh7rmoYbkvKDpgJLlj4LmlbAKICAgICAgbWJSb3dEYXRhOiB7fSwKICAgICAgLy/lrprkuYnmqKHmnb/lhoXlrrnlvLnlh7rmoYYKICAgICAgaXNTaG93WG1HbGJ3RGlhbG9nOiBmYWxzZSwKICAgICAgeG1TZWxlY3RlZEZvcm06IHsKICAgICAgICAvL+ivlemqjOaooeadv2lkCiAgICAgICAgc3ltYmlkOiB1bmRlZmluZWQsCiAgICAgICAgLy/or5Xpqozpobnnm67mlbDmja7pm4blkIgKICAgICAgICB4bURhdGFSb3dzOiBbXSwKICAgICAgICBpc01wU3l4bTogMSwKICAgICAgICBtcGlkOiB1bmRlZmluZWQsCiAgICAgIH0sCiAgICAgIC8v6aG555uu5bqT5by55Ye65qGG5qCH6aKYCiAgICAgIHhtTGlicmFyeUFkZERpYWxvZ1RpdGxlOiAn6aG555uu5bqTJywKICAgICAgLy/pobnnm67lupPlvLnlh7rmoYbmjqfliLYKICAgICAgaXNTaG93QWRkR2x4bURpYWxvZzogZmFsc2UsCiAgICAgIC8v6aG555uu5bqT5p+l6K+i5Y+C5pWwCiAgICAgIHhtTGlicmFyeVF1ZXJ5Rm9ybTogewogICAgICAgIHN5bWJpZDogdW5kZWZpbmVkLAogICAgICAgIHN5eG1tYzogJycsCiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgaXNNcFN5eG06IDEsCiAgICAgICAgdG90YWw6MCwKICAgICAgfSwKICAgICAgLy/pobnnm67lupPmlbDmja4KICAgICAgeG1MaWJyYXJ5RGF0YUxpc3Q6IFtdLAogICAgICAvL+mhueebruW6k+mhueebruaAu+aVsAogICAgICB4bUxpYnJhcnlUb3RhbDogMCwKICAgICAgLy/ooajljZXpqozor4EKICAgICAgbWJ6YlJ1bGVzOiB7CiAgICAgICAgbWJtYzogWwogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36L6T5YWl5qih5p2/5ZCN56ewJywgdHJpZ2dlcjogJ2JsdXInfQogICAgICAgIF0KICAgICAgfSwKICAgICAgLy8g562b6YCJ5p2h5Lu2CiAgICAgIGZpbHRlckluZm86IHsKICAgICAgICBkYXRhOiB7CiAgICAgICAgICBtYm1jOiAnJwogICAgICAgIH0sCiAgICAgICAgZmllbGRMaXN0OiBbCiAgICAgICAgICB7bGFiZWw6ICfmqKHmnb/lkI3np7AnLCB0eXBlOiAnaW5wdXQnLCB2YWx1ZTogJ21ibWMnLCBtdWx0aXBsZTogdHJ1ZX0KICAgICAgICBdCiAgICAgIH0sCiAgICAgIC8v5paw5aKe5oyJ6ZKu5o6n5Yi2CiAgICAgIGFkZERpc2FibGVkOiB0cnVlLAogICAgICAvL+agkee7k+aehOaHkuWKoOi9veWPguaVsAogICAgICBwcm9wczogewogICAgICAgIGxhYmVsOiAnbmFtZScsCiAgICAgICAgY2hpbGRyZW46ICd6b25lcycsCiAgICAgICAgaXNMZWFmOiAnbGVhZicKICAgICAgfSwKICAgICAgLy/liKDpmaTpgInmi6nliJcKICAgICAgc2VsZWN0Um93czogW10sCiAgICAgIC8v5by55Ye65qGG6KGo5Y2VCiAgICAgIGZvcm06IHt9LAogICAgICAvL+afpeivouivlemqjOmDqOS9jeWPguaVsAogICAgICBxdWVyeVN5QndQYXJhbTogewogICAgICAgIHNibHhpZDogdW5kZWZpbmVkLAogICAgICAgIG1ibWM6IHVuZGVmaW5lZCwKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMAogICAgICB9LAogICAgICAvL+eCueWHu+agkeiKgueCuei1i+WAvAogICAgICB0cmVlRm9ybToge30sCiAgICAgIC8v6K+V6aqM6YOo5L2N5YiX6KGoCiAgICAgIHRhYmxlQW5kUGFnZUluZm86IHsKICAgICAgICBwYWdlcjogewogICAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgICAgcGFnZU51bTogMSwKICAgICAgICAgIHRvdGFsOiAwLAogICAgICAgICAgc2l6ZXM6IFsxMCwgMjAsIDUwLCAxMDBdCiAgICAgICAgfSwKICAgICAgICB0YWJsZURhdGE6IFtdLAogICAgICAgIHRhYmxlSGVhZGVyOiBbCiAgICAgICAgICB7bGFiZWw6ICfmqKHmnb/lkI3np7AnLCBwcm9wOiAnbWJtYycsIG1pbldpZHRoOiAnMTAwJ30sCiAgICAgICAgICB7bGFiZWw6ICfmmK/lkKbpu5jorqQnLCBwcm9wOiAnc2ZtcicsIG1pbldpZHRoOiAnMTAwJ30sCiAgICAgICAgICB7bGFiZWw6ICfmmK/lkKblgZznlKgnLCBwcm9wOiAnc2Z0eScsIG1pbldpZHRoOiAnMTAwJ30sCiAgICAgICAgICAvLyB7CiAgICAgICAgICAvLyAgIHByb3A6ICdvcGVyYXRpb24nLAogICAgICAgICAgLy8gICBsYWJlbDogJ+aTjeS9nCcsCiAgICAgICAgICAvLyAgIG1pbldpZHRoOiAnMTIwcHgnLAogICAgICAgICAgLy8gICBzdHlsZToge2Rpc3BsYXk6ICdibG9jayd9LAogICAgICAgICAgLy8gICAvL+aTjeS9nOWIl+WbuuWumuWGjeWPs+S+pwogICAgICAgICAgLy8gICBmaXhlZDogJ3JpZ2h0JywKICAgICAgICAgIC8vICAgLy8gb3BlcmF0aW9uOiBbCiAgICAgICAgICAvLyAgIC8vICAge25hbWU6ICfkv67mlLknLCBjbGlja0Z1bjogdGhpcy51cGRhdGVEZXRhaWxzfSwKICAgICAgICAgIC8vICAgLy8gICB7bmFtZTogJ+ivpuaDhScsIGNsaWNrRnVuOiB0aGlzLmdldERldGFpbHN9LAogICAgICAgICAgLy8gICAvLyBdCiAgICAgICAgICAvLyB9CiAgICAgICAgXSwKICAgICAgICBvcHRpb246IHtjaGVja0JveDogdHJ1ZSwgc2VyaWFsTnVtYmVyOiB0cnVlfQogICAgICB9LAogICAgICAvL+e7hOe7h+agkQogICAgICB0cmVlT3B0aW9uczogW10sCgogICAgICBpc1Nob3dEZXRhaWxzOiBmYWxzZSwKICAgICAgdGl0bGU6ICcnLAoKICAgICAgLy/liKDpmaTmmK/lkKblj6/nlKgKICAgICAgbXVsdGlwbGVTZW5zb3I6IHRydWUsCiAgICAgIC8v5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgYm06IHVuZGVmaW5lZCwKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMAogICAgICB9LAogICAgICBpc0Rpc2FibGVkOiBmYWxzZSwKICAgICAgaXNTaG93OiB0cnVlLAoKICAgICAgLy/lhbPogZTpobnnm67lvLnlh7rmoYZ0aXRsZQogICAgICBnbHhtRGlhbG9nVGl0bGU6ICflhbPogZTpobnnm64nLAogICAgICAvL+WFs+iBlOmhueebruW8ueWHuuahhuaOp+WItuWxleW8gAogICAgICBpc0dseG1EaWFsb2dTaG93OiBmYWxzZSwKCiAgICAgIC8v5YWz6IGU6aG555uudG90YWwKICAgICAgZ2x4bVRvdGFsOiAwLAogICAgICAvL+WFs+iBlOmhueebruafpeivouWPguaVsAogICAgICBnbHhtUXVlcnlQYXJhbXM6IHsKICAgICAgICBzeW1iaWQ6IHVuZGVmaW5lZCwKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBpc01wU3l4bToxLAogICAgICAgIHRvdGFsOjAsCiAgICAgIH0sCiAgICAgIHN5bWJpZDogdW5kZWZpbmVkLAoKCiAgICAgIC8v5YWz6IGU5a2Q6aG555uu5p+l6K+i5Y+C5pWwCiAgICAgIGdsenhtUXVlcnlQYXJhbXM6IHsKICAgICAgICBzeXhtaWQ6IHVuZGVmaW5lZCwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgcGFnZU51bTogMQogICAgICB9LAoKICAgICAgLy/mqKHmnb/lhbPogZTpobnnm67mlbDmja4KICAgICAgbWJHbHhtRGF0YUxpc3Q6IFtdLAogICAgICAvL+aWsOWinuaooeadv+S4jumhueebruWFs+iBlOmAieS4reeahOaVsOaNrgogICAgICB4bW1iU2VsZWN0ZWRSb3c6IFtdLAogICAgICAvL+aWsOWinuaVsOaNrgogICAgICB4bW1ibGlzdDogW10sCiAgICAgIC8v6aG555uu5YWz6IGU55qE5a2Q6aG555uu5pWw5o2uCiAgICAgIHp4bUdsbWJEYXRhTGlzdDogW10sCiAgICAgIC8v5qih5p2/5YWz6IGU6aG555uu6YCJ5Lit5qGG5pWw5o2uCiAgICAgIHNlbGVjdGVkUm93RGF0YUNoYW5nZTogW10sCiAgICAgIC8v5pi+56S66ZOt54mM5by55qGGCiAgICAgIHNob3dNcERpYWxvZzogZmFsc2UsCiAgICAgIC8v6YCJ5Lit6KGM5pWw5o2uCiAgICAgIHJvd0RhdGE6IHt9LAoKICAgICAgLy/lhbPogZTlkI3niYzkvr/liKkKICAgICAgbXBMaXN0OiBbXSwKCiAgICAgIC8v6K+V6aqM5pWw5o2uCiAgICAgIHN5c2pEYXRhTGlzdDogW10sCiAgICAgIC8v6K+V6aqM6KGo5qC86buY6K6k5Zu65a6a55qE6KGMCiAgICAgIGRlZmF1bHRSb3c6WyLku6rlmajlnovlj7ciLCLnu5PorroiLCLlpIfms6giXSwKICAgICAgdGRXaWR0aDogMCwgLy/kuIDkuKrljZXlhYPmoLzmiYDljaDlrr3luqYKICAgIH0KICB9LAogIHdhdGNoOiB7fSwKICBjcmVhdGVkKCkgewogICAgLy/ojrflj5bmlbDmja7liJfooagKICAgIHRoaXMuZ2V0RGF0YSgpCiAgfSwKICBtb3VudGVkKCkgewogIH0sCiAgbWV0aG9kczogewogICAgZWRpdERhdGEocm93LGNvbHVtbil7CiAgICAgIGNvbnNvbGUubG9nKCcxMTEnLHJvdyxjb2x1bW4pOwogICAgICByb3dbY29sdW1uLnByb3BlcnR5KyJpc1Nob3ciXSA9IHRydWU7CiAgICB9LAogICAgZWxDbGljaygpewogICAgICBjb25zb2xlLmxvZygndGhpcycsdGhpcyx0aGlzLmlubmVySFRNTCk7CiAgICB9LAogICAgLy/or5XpqozmlbDmja7ooajmoLzlkIjlubbmlrnms5UKICAgIGFycmF5U3Bhbk1ldGhvZCh7IHJvdywgY29sdW1uLCByb3dJbmRleCwgY29sdW1uSW5kZXggfSkgewogICAgICBpZih0aGlzLmRlZmF1bHRSb3cuaW5jbHVkZXMocm93LlNZQlcpKXsKICAgICAgICBpZiAoY29sdW1uSW5kZXggPiAwKSB7CiAgICAgICAgICByZXR1cm4gWzEscm93LnRvdGFsTnVtXQogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIC8v6K6+5aSH6ZOt54mM6KGo5qC85ZCI5bm25pa55rOVCiAgICBzYm1wU3Bhbk1ldGhvZCh7IHJvdywgY29sdW1uLCByb3dJbmRleCwgY29sdW1uSW5kZXggfSl7CiAgICAgIGlmIChjb2x1bW5JbmRleCA+IDMpIHsKICAgICAgICByZXR1cm4gWzEsMl0KICAgICAgfQogICAgfSwKICAgIC8v5qih5p2/6K+m5oOF5oyJ6ZKuCiAgICBoYW5kbGVNYkluZm8ocm93KSB7CiAgICAgIGNvbnNvbGUubG9nKCJzZGZzZC0tcyIscm93KTsKICAgICAgLy/ojrflj5blvZPliY3mqKHmnb9pZOWKoOi9vemhtemdouS/oeaBrwogICAgICAvLyB0aGlzLmdldE1iR2xNcGluZm9EYXRhKHJvdyk7CiAgICAgIC8v5omT5byA5by55Ye65qGGCiAgICAgIHRoaXMuaXNTaG93RG93bkxvYWREaWFsb2cgPSB0cnVlOwogICAgICAvL+iOt+WPluivlemqjOaVsOaNrgogICAgICB0aGlzLmdldE1iR2xYbUFuZEJ3KHJvdyk7CiAgICB9LAogICAgLy/mtYvor5UKICAgIG91dHB1dFBkZkZ1bjEoKXsKICAgICAgbGV0IHRoYXQgPSB0aGlzOwogICAgICBsZXQgaGVpZ2h0cyA9IDA7Ly/mnIDnu4jntK/orqHpq5jluqYKICAgICAgY29uc3Qgd2lkdGggPSA1OTIuMjg7CiAgICAgIGNvbnN0IGhlaWdodCA9IDE0MDA7CiAgICAgIHRoYXQuJG5leHRUaWNrKCgpPT57CiAgICAgICAgZGVidWdnZXIKICAgICAgICBsZXQgdGFyZ2V0ID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ3ByaW50Q29udGVudElkJyk7CiAgICAgICAgbGV0IHBhZ2VIZWlnaHQgPSB0YXJnZXQuc2Nyb2xsV2lkdGggLyB3aWR0aCAqIGhlaWdodDsKICAgICAgICBsZXQgY2hpbGRzID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ3ByaW50Q29udGVudElkJykuY2hpbGROb2RlczsgIC8v6KGo5qC855qE56ys5LiA57qn5a2Q5YaF5a65CiAgICAgICAgZm9yKGxldCBpPTA7aTxjaGlsZHMubGVuZ3RoO2krKyl7CiAgICAgICAgICB0aGF0Lmxvb3BGdW4oY2hpbGRzW2ldLGhlaWdodHMpCiAgICAgICAgICAvLyBjb25zb2xlLmxvZygnY2hpbGRzJyxjaGlsZHNbaV0pOwogICAgICAgICAgbGV0IG11bHRpcGxlID0gTWF0aC5jZWlsKChjaGlsZHNbaV0ub2Zmc2V0VG9wICsgY2hpbGRzW2ldLm9mZnNldEhlaWdodCkvcGFnZUhlaWdodCk7CiAgICAgICAgICBsZXQgY2hpbGRzSGVpZ2h0ID0gY2hpbGRzW2ldLm9mZnNldEhlaWdodDsKICAgICAgICAgIGhlaWdodHMgKz0gY2hpbGRzSGVpZ2h0OwogICAgICAgIH0KICAgICAgICBjb25zb2xlLmxvZygnemknLGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdwcmludENvbnRlbnRJZCcpLmNoaWxkTm9kZXMpCiAgICAgICAgLy90aGlzLmRvd25sb2FkUGRmKCk7CiAgICAgIH0pCiAgICB9LAogICAgLy8KICAgIG91dHB1dFBkZkZ1bigpewogICAgICBsZXQgdGhhdCA9IHRoaXM7CiAgICAgIGxldCBoZWlnaHRzID0gMDsvL+acgOe7iOe0r+iuoemrmOW6pgogICAgICBjb25zdCB3aWR0aCA9IDU5Mi4yODsKICAgICAgY29uc3QgaGVpZ2h0ID0gMTQwMDsKICAgICAgdGhhdC4kbmV4dFRpY2soKCk9PnsKICAgICAgICAvLyBkZWJ1Z2dlcgogICAgICAgIGxldCB0YXJnZXQgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgncHJpbnRDb250ZW50SWQnKTsKICAgICAgICBsZXQgcGFnZUhlaWdodCA9IHRhcmdldC5zY3JvbGxXaWR0aCAvIHdpZHRoICogaGVpZ2h0OwogICAgICAgIGxldCBjaGlsZHMgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgncHJpbnRDb250ZW50SWQnKS5jaGlsZE5vZGVzOyAgLy/ooajmoLznmoTnrKzkuIDnuqflrZDlhoXlrrkKICAgICAgICBmb3IobGV0IGk9MDtpPGNoaWxkcy5sZW5ndGg7aSsrKXsKICAgICAgICAgIGxldCBjaGlsZHNIZWlnaHQgPSBjaGlsZHNbaV0ub2Zmc2V0SGVpZ2h0OwogICAgICAgICAgaGVpZ2h0cyArPSBjaGlsZHNIZWlnaHQ7CiAgICAgICAgICAvL+WIpOaWreavj+S4gOS4quWtkOWFg+e0oOeahOmrmOW6puaYr+WQpui2heWHuuS4gOmhtXBkZueahOmrmOW6pizmsqHotoXov4flsLHntK/liqAKICAgICAgICAgIGlmKGhlaWdodHMgPj0gaGVpZ2h0KXsKICAgICAgICAgICAgdGhhdC5sb29wRnVuKGNoaWxkc1tpXSxoZWlnaHRzKQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgICAvL3RoaXMuZG93bmxvYWRQZGYoKTsKICAgICAgfSkKICAgIH0sCiAgICAvL+a1i+ivleaWueazlQogICAgbG9vcEZ1bih2YWwsaGVpZ2h0cyl7CiAgICAgIGNvbnNvbGUubG9nKCfmiZPljbAnLHZhbCxoZWlnaHRzKTsKICAgICAgLy8gIGxldCBoZWlnaHRzID0gMDsvL+acgOe7iOe0r+iuoemrmOW6pgogICAgICBjb25zdCB3aWR0aCA9IDU5Mi4yODsKICAgICAgY29uc3QgaGVpZ2h0ID0gMTQ4ODsKICAgICAgLy8gbGV0IGNoaWxkc0hlaWdodCA9IHZhbC5vZmZzZXRIZWlnaHQ7CiAgICAgIC8vIGhlaWdodHMgKz0gY2hpbGRzSGVpZ2h0OwogICAgICAvL2NvbnNvbGUubG9nKCfpq5jluqYnLGhlaWdodHMpCiAgICAgIC8v5Yik5pat5q+P5LiA5Liq5a2Q5YWD57Sg55qE6auY5bqm5piv5ZCm6LaF5Ye65LiA6aG1cGRm55qE6auY5bqmLOayoei2hei/h+Wwsee0r+WKoAogICAgICBpZihoZWlnaHRzID49IGhlaWdodCl7CiAgICAgICAgLy/lhYjlh4/ljrvotoXlh7rpobXpnaLnmoRkaXbpq5jluqYKICAgICAgICBoZWlnaHRzID0gaGVpZ2h0cyAtIHZhbC5vZmZzZXRIZWlnaHQ7CiAgICAgICAgLy8gY29uc29sZS5sb2coJ2hlaTEnLHZhbC5jaGlsZE5vZGVzKQogICAgICAgIC8v5Zyo6LaF6L+H55qE5a2Q5YWD57Sg6L+b6KGM5b6q546v5Yik5pat77yM5p+l5om+5Zyo6YKj5LiA5Z2X6LaF6L+HCiAgICAgICAgdmFsLmNoaWxkTm9kZXMuZm9yRWFjaChpdGVtPT4gewogICAgICAgICAgaGVpZ2h0cyArPSBpdGVtLm9mZnNldEhlaWdodDsKICAgICAgICAgIGlmKGhlaWdodHMgPj0gaGVpZ2h0KXsKICAgICAgICAgICAgLy/lhYjlh4/ljrvotoXlh7rpobXpnaLnmoRkaXbpq5jluqYKICAgICAgICAgICAgaGVpZ2h0cyA9IGhlaWdodHMgLSBpdGVtLm9mZnNldEhlaWdodDsKICAgICAgICAgICAgY29uc29sZS5sb2coJ2l0ZW0nLGl0ZW0saGVpZ2h0cyxpdGVtLmNoaWxkTm9kZXMpCiAgICAgICAgICAgIGl0ZW0uY2hpbGROb2Rlcy5mb3JFYWNoKHZhbHVlPT4gewogICAgICAgICAgICAgIGhlaWdodHMgKz0gdmFsdWUub2Zmc2V0SGVpZ2h0OwogICAgICAgICAgICAgIGlmKGhlaWdodHMgPj0gaGVpZ2h0KXsKCiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnaXRlbTExJyx2YWx1ZSkKICAgICAgICAgICAgICAgIC8v6I635Y+W54i26IqC54K5CiAgICAgICAgICAgICAgICBsZXQgY2hpbGRQYXJlbnQgPSB2YWx1ZS5wYXJlbnROb2RlOwogICAgICAgICAgICAgICAgbGV0IG5leHQgPSB2YWx1ZS5uZXh0U2libGluZzsKICAgICAgICAgICAgICAgIC8v5Yib5bu656m655m95qCH562+CiAgICAgICAgICAgICAgICBsZXQgbmV3Tm9kZSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ3NwYW4nKTsKICAgICAgICAgICAgICAgIG5ld05vZGUuc3R5bGUuYmFja2dyb3VuZCA9ICcjZmZmJzsKICAgICAgICAgICAgICAgIG5ld05vZGUuc3R5bGUuaGVpZ2h0ID0gJzEwMHB4JzsKICAgICAgICAgICAgICAgIG5ld05vZGUuc3R5bGUud2lkdGggPSAnMTAzMHB4JzsKICAgICAgICAgICAgICAgIG5ld05vZGUuc3R5bGUuZGlzcGxheSA9ICdibG9jayc7CiAgICAgICAgICAgICAgICBuZXdOb2RlLnN0eWxlLmJvcmRlclRvcCA9ICcxcHggc29saWQgIzAwMCc7CiAgICAgICAgICAgICAgICAvLyBjb25zb2xlLmxvZygnNDQ0NCcsdmFsMyxuZXh0LG5ld05vZGUsY2hpbGRQYXJlbnQpOwogICAgICAgICAgICAgICAgLy9jb25zb2xlLmxvZygneGlhamllZGlhbicsdmFsMy5uZXh0U2libGluZykKICAgICAgICAgICAgICAgIGlmKG5leHQpewogICAgICAgICAgICAgICAgICBjaGlsZFBhcmVudC5pbnNlcnRCZWZvcmUobmV3Tm9kZSxuZXh0KQogICAgICAgICAgICAgICAgfWVsc2V7CiAgICAgICAgICAgICAgICAgIGNoaWxkUGFyZW50LmFwcGVudENoaWxkKG5ld05vZGUpOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgdGhpcy5kb3dubG9hZFBkZigpCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9KQoKICAgICAgICAgICAgLy90aGlzLmxvb3BGdW4oaXRlbSxoZWlnaHRzKQogICAgICAgICAgfQogICAgICAgICAgLy8gdGhpcy5sb29wRnVuKGl0ZW0saGVpZ2h0cykKICAgICAgICB9KQogICAgICB9CiAgICB9LAogICAgLy/lr7zlh7pwZGbmk43kvZwKICAgIGRvd25sb2FkUGRmKCkgewogICAgICBodG1sVG9QZGYuZG93bmxvYWRQREYoZG9jdW1lbnQucXVlcnlTZWxlY3RvcignI3ByaW50Q29udGVudElkJyksIHRoaXMubWJJbmZvLm1ibWMpCiAgICB9LAogICAgLy/ojrflj5blvZPliY3mqKHmnb9pZOWKoOi9vemhtemdouS/oeaBrwogICAgZ2V0TWJHbE1waW5mb0RhdGEocGFyYW0pIHsKICAgICAgZ2V0TWJHbE1waW5mb0RhdGEocGFyYW0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLm1wTGlzdCA9IHJlcy5kYXRhOwogICAgICAgIC8v6LCD55So5riy5p+T6ZOt54mM6aG16Z2i5byA5aeLCiAgICAgICAgLy8gdGhpcy5hcHBseU1wSHRtbCh0aGlzLm1wTGlzdCwgcGFyYW0pOwogICAgICAgIC8v5omT5byA5by55Ye65qGGCiAgICAgICAgdGhpcy5pc1Nob3dEb3duTG9hZERpYWxvZyA9IHRydWU7CiAgICAgIH0pCgogICAgfSwKICAgIC8v6I635Y+W6K+V6aqM5pWw5o2uCiAgICBnZXRNYkdsWG1BbmRCdyhyb3dEYXRhKSB7CiAgICAgIC8v5q+P5qyh6I635Y+W5pWw5o2u5YmN5YWI5riF56m677yM5YaN5re75Yqg77yM5ZCm5YiZ5aSa5qyh6L+b5YWl6aG16Z2i5pe25Lya6I635Y+W6YeN5aSN5pWw5o2uCiAgICAgIHRoaXMuc3lzakRhdGFMaXN0ID0gW107CiAgICAgIC8v6I635Y+W6K6+5aSH6ZOt54mM5pWw5o2uCiAgICAgIC8v5Zyo5aGr5YaZ5oql5ZGK5pe277yM6K+35rGC5ZCO5Y+w5aSE55CG5pWw5o2uCiAgICAgIGNvbnN0IHRoYXQgPSB0aGlzOwogICAgICBnZXRNb3VsZFZhbHVlKHtzeW1iaWQ6cm93RGF0YS5vYmpJZH0pLnRoZW4ocmVzPT57CiAgICAgICAgcmVzID0gcmVzLmRhdGE7CiAgICAgICAgbGV0IGtleSA9ICdzeW1iaWQnOwogICAgICAgIHJvd0RhdGFba2V5XSA9IHJvd0RhdGEub2JqSWQ7CiAgICAgICAgZ2V0TXd0VWRTeU1weHFCeU1iemIoe3N5bWJpZDpyb3dEYXRhLm9iaklkfSkudGhlbihyZXN1bHQ9PnsKICAgICAgICAgIGxldCBzYm1wX2RhdGEgPSByZXN1bHQuZGF0YS5zYm1wOwogICAgICAgICAgbGV0IHN5c2pfZGF0YSA9IHJlc3VsdC5kYXRhLnN5eG07CiAgICAgICAgICBsZXQgYXJyID0gW107CiAgICAgICAgICBPYmplY3Qua2V5cyhzYm1wX2RhdGEpLnNvcnQoKS5mb3JFYWNoKGZ1bmN0aW9uKGtleSl7CiAgICAgICAgICAgIGFyci5wdXNoKHtba2V5XTogc2JtcF9kYXRhW2tleV19KQogICAgICAgICAgfSkKICAgICAgICAgIGxldCBhcnIxID0gW107CiAgICAgICAgICBPYmplY3Qua2V5cyhzeXNqX2RhdGEpLnNvcnQoKS5mb3JFYWNoKGZ1bmN0aW9uKGtleSl7CiAgICAgICAgICAgIGFycjEucHVzaCh7W2tleV06IHN5c2pfZGF0YVtrZXldfSkKICAgICAgICAgIH0pCiAgICAgICAgICB0aGF0LmhhbmRsZVNibXAocmVzW09iamVjdC5rZXlzKHJlcylbMF1dLGFyciwnJywnaDJfdGFibGUnKTsKICAgICAgICAgIHRoYXQuaGFuZGxlU2JtcChyZXNbT2JqZWN0LmtleXMocmVzKVsxXV0sYXJyMSwnJywnaDNfdGFibGUnKTsKICAgICAgICB9KQogICAgICB9KQogICAgfSwKICAgIGhhbmRsZVNibXAoZGF0YU51bSxkYXRhQXJyLHN0cix0YWJsZUJveCl7CiAgICAgIGZvcihsZXQgayA9IDA7azxkYXRhTnVtLmxlbmd0aDtrKyspewogICAgICAgIHZhciBocyA9IGRhdGFOdW1ba10uYUhzOwogICAgICAgIHZhciBscyA9IGRhdGFOdW1ba10uYUxzOwogICAgICAgIHRoaXMudGRXaWR0aCA9IDEwMCAvIE51bWJlcihscyk7CiAgICAgICAgbGV0IGRhdGEgPSBkYXRhQXJyW2tdOwogICAgICAgIGZvcih2YXIgaXRlbSBpbiBkYXRhKSB7CiAgICAgICAgICBpZihkYXRhQXJyLmxlbmd0aCA+IDEpeyAgIC8v5Yik5pat6Iul6K+l5qih5Z2X5Y+q5pyJ5LiA5Liq5a2Q5YaF5a655YiZ5LiN5pi+56S6dGl0bGUKICAgICAgICAgICAgc3RyICs9ICI8dHIgc3R5bGU9J3RleHQtYWxpZ246bGVmdDsnPjx0aCBjb2xzcGFuPSIrIGxzICsiPiIrIGl0ZW0gKyI8L3RoPjwvdHI+IjsKICAgICAgICAgIH0KICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgaHM7IGkrKykgey8v5pyJ5Yeg6KGM5bCx5o+S5YWl5Yeg6KGMCiAgICAgICAgICAgIGxldCB0ZW1wID0gIjx0cj4iCiAgICAgICAgICAgIGZvciAobGV0IGogPSAwOyBqIDwgZGF0YVtpdGVtXS5sZW5ndGg7IGorKykgeyAgIC8v5b6q546v5pWw5o2u55yL5q+P6KGM5pyJ5Yeg5YiXCiAgICAgICAgICAgICAgaWYgKGkgPT0gZGF0YVtpdGVtXVtqXS5yb3dpbmRleCkgewogICAgICAgICAgICAgICAgdmFyIG5yYnMgPSBkYXRhW2l0ZW1dW2pdLm5yYnM7CiAgICAgICAgICAgICAgICB2YXIgc2pseCA9IGRhdGFbaXRlbV1bal0uc2pseDsgIC8v5pWw5o2u57G75Z6LCiAgICAgICAgICAgICAgICB2YXIgb2JqSWQgPSBkYXRhW2l0ZW1dW2pdLm9iaklkOwogICAgICAgICAgICAgICAgdmFyIG5yID0gJyc7CiAgICAgICAgICAgICAgICBpZihucmJzID09IG51bGwpewogICAgICAgICAgICAgICAgICBucmJzID0gIiI7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICBuciA9IG5yYnM7CiAgICAgICAgICAgICAgICBpZiAoZGF0YVtpdGVtXVtqXS5jb2xzcGFuICE9ICcxJykgeyAgICAvL+WIpOaWrWNvbHNwYW7kuI3kuLox55qE6K+d5Li65Y+v57yW6L6R55qECiAgICAgICAgICAgICAgICAgIHRlbXAgKz0gIjx0ZCB0YWJpbmRleD0nLTEnIGNvbHNwYW49JyIKICAgICAgICAgICAgICAgICAgICArIGRhdGFbaXRlbV1bal0uY29sc3BhbgogICAgICAgICAgICAgICAgICAgICsgIicgcm93c3Bhbj0nIgogICAgICAgICAgICAgICAgICAgICsgZGF0YVtpdGVtXVtqXS5yb3dzcGFuCiAgICAgICAgICAgICAgICAgICAgKyAiJyBzdHlsZT0nd2lkdGg6ICIgKwogICAgICAgICAgICAgICAgICAgIHRoaXMudGRXaWR0aCAqIGRhdGFbaXRlbV1bal0uY29sc3BhbiArCiAgICAgICAgICAgICAgICAgICAgInB4Jz4iCiAgICAgICAgICAgICAgICAgICAgKyBuciArICI8L3RkPiI7CiAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICB0ZW1wICs9ICI8dGQgdGFiaW5kZXg9Jy0xJyBjb2xzcGFuPSciCiAgICAgICAgICAgICAgICAgICAgKyBkYXRhW2l0ZW1dW2pdLmNvbHNwYW4KICAgICAgICAgICAgICAgICAgICArICInIHJvd3NwYW49JyIKICAgICAgICAgICAgICAgICAgICArIGRhdGFbaXRlbV1bal0ucm93c3BhbgogICAgICAgICAgICAgICAgICAgICsgIicgc3R5bGU9J21pbi13aWR0aDoxMDBweDt3aWR0aDogIiArCiAgICAgICAgICAgICAgICAgICAgdGhpcy50ZFdpZHRoICogZGF0YVtpdGVtXVtqXS5jb2xzcGFuICsKICAgICAgICAgICAgICAgICAgICAiJSc+IgogICAgICAgICAgICAgICAgICAgICsgbnIgKyAiPC90ZD4iOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgICB0ZW1wICs9ICI8L3RyPiIKICAgICAgICAgICAgc3RyICs9IHRlbXA7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICAgIC8v5riy5p+T6aG16Z2iCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICB2YXIgdGFibGVCb3gxID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQodGFibGVCb3gpOwogICAgICAgIHRhYmxlQm94MS5pbm5lckhUTUwgPSAnJzsKICAgICAgICB0YWJsZUJveDEuaW5uZXJIVE1MID0gc3RyOwogICAgICB9KQogICAgfSwKICAgIC8v6Kej5p6Q5ZCO5Y+w6K+V6aqM5pWw5o2uCiAgICBhbmFseXNpc1N5RGF0YShzeXhtbWMsIHp4bUFuZEJ3RGF0YSkgewogICAgICBsZXQgc3lzakRhdGEgPSB7fQogICAgICBzeXNqRGF0YS5zeXhtbWMgPSBzeXhtbWM7CiAgICAgIGZvciAobGV0IGtleSBpbiB6eG1BbmRCd0RhdGFbMF0pIHsKICAgICAgICBzeXNqRGF0YVtrZXldID0genhtQW5kQndEYXRhWzBdW2tleV0KICAgICAgfQogICAgICB0aGlzLnN5c2pEYXRhTGlzdC5wdXNoKHN5c2pEYXRhKTsKICAgIH0sCiAgICAvL+a4suafk+WunumqjOaVsOaNruWIsOmhtemdogogICAgYXBwbHlTeXNqRGF0YVRvSHRtbCgpIHsKICAgICAgdGhpcy5hcnIgPSBbXTsKICAgICAgLy8gJCgnI3N5c2pUYWJsZUlkJykuaHRtbCgiIik7CiAgICAgIC8v6L+b6KGM5pWw5o2u5aSE55CG6YeN57uECiAgICAgIGxldCBkYXRhID0gdGhpcy5zeXNqRGF0YUxpc3Q7CiAgICAgIGlmIChkYXRhLmxlbmd0aCA+IDApIHsKICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGRhdGEubGVuZ3RoOyBpKyspIHsKICAgICAgICAgIGxldCBkYXRhQ2hpbGQgPSBkYXRhW2ldOwogICAgICAgICAgbGV0IHRpdGxlID0gZGF0YUNoaWxkLnN5eG1tYzsvL+ivlemqjOmhueebruWQjeensAogICAgICAgICAgbGV0IGJ3TGlzdCA9IGRhdGFDaGlsZC5id0xpc3Q7IC8v6YOo5L2NbGlzdAogICAgICAgICAgbGV0IHp4bUxpc3QgPSBkYXRhQ2hpbGQuenhtTGlzdDsgLy/lrZDpobnnm65saXN0CiAgICAgICAgICBsZXQgaHggPSBbCiAgICAgICAgICAgIHsKICAgICAgICAgICAgICAibGFiZWwiOnRpdGxlLCAvL+esrOS4gOS4quihqOWktOS4uuivlemqjOmhueebruWQjeensAogICAgICAgICAgICAgICJjb2x1bW5fbmFtZSI6IlNZQlciLCAvL+esrOS4gOWIl+WvueW6lOeahOWtl+auteWQje+8iOivlemqjOmDqOS9je+8iQogICAgICAgICAgICB9LAogICAgICAgICAgXTsKICAgICAgICAgIHp4bUxpc3QuZm9yRWFjaCh6eG09PnsKICAgICAgICAgICAgaHgucHVzaCh7CiAgICAgICAgICAgICAgImxhYmVsIjp6eG0uc3l6eG1tYywgLy/mr4/liJfnmoTooajlpLQKICAgICAgICAgICAgICAiY29sdW1uX25hbWUiOiIiLCAvL+avj+WIl+WvueW6lOeahOaVsOWAvOaaguaXtuiuvue9ruS4uuepuueZvQogICAgICAgICAgICB9KQogICAgICAgICAgfSkKICAgICAgICAgIGxldCBzeCA9IFtdOwogICAgICAgICAgYndMaXN0LmZvckVhY2goYnc9PnsKICAgICAgICAgICAgc3gucHVzaCh7CiAgICAgICAgICAgICAgIlNZQlciOmJ3LlNZQlcsCiAgICAgICAgICAgICAgInRvdGFsTnVtIjp6eG1MaXN0Lmxlbmd0aC8v6K6w5b2V5oC75YiX5pWw77yM55So5LqO5Zu65a6a6KGM55qE5ZCI5bm25LqL5Lu2CiAgICAgICAgICAgIH0pCiAgICAgICAgICB9KQogICAgICAgICAgLy/lkI7lm5vooYzlm7rlrpoKICAgICAgICAgIHN4LnB1c2goCiAgICAgICAgICAgIHsKICAgICAgICAgICAgICAiU1lCVyI6Iue7k+aenCIsCiAgICAgICAgICAgICAgInRvdGFsTnVtIjp6eG1MaXN0Lmxlbmd0aC8v6K6w5b2V5oC75YiX5pWw77yM55So5LqO5Zu65a6a6KGM55qE5ZCI5bm25LqL5Lu2CiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIHsKICAgICAgICAgICAgICAiU1lCVyI6IuS7quWZqOWei+WPtyIsCiAgICAgICAgICAgICAgInRvdGFsTnVtIjp6eG1MaXN0Lmxlbmd0aC8v6K6w5b2V5oC75YiX5pWw77yM55So5LqO5Zu65a6a6KGM55qE5ZCI5bm25LqL5Lu2CiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIHsKICAgICAgICAgICAgICAiU1lCVyI6Iue7k+iuuiIsCiAgICAgICAgICAgICAgInRvdGFsTnVtIjp6eG1MaXN0Lmxlbmd0aC8v6K6w5b2V5oC75YiX5pWw77yM55So5LqO5Zu65a6a6KGM55qE5ZCI5bm25LqL5Lu2CiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIHsKICAgICAgICAgICAgICAiU1lCVyI6IuWkh+azqCIsCiAgICAgICAgICAgICAgInRvdGFsTnVtIjp6eG1MaXN0Lmxlbmd0aC8v6K6w5b2V5oC75YiX5pWw77yM55So5LqO5Zu65a6a6KGM55qE5ZCI5bm25LqL5Lu2CiAgICAgICAgICAgIH0KICAgICAgICAgICkKICAgICAgICAgIHRoaXMuYXJyLnB1c2goewogICAgICAgICAgICB0aXRsZTp0aXRsZSwKICAgICAgICAgICAgenhtTGlzdDpoeCwKICAgICAgICAgICAgYndMaXN0OnN4LAogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9CiAgICAgIC8v5ou85o6l55qE6ZOt54mM6KGo5qC8CiAgICAgIC8qICAgICBsZXQgc3RyID0gIiI7CiAgICAgICAgICAgaWYgKHRoaXMuc3lzakRhdGFMaXN0Lmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdGhpcy5zeXNqRGF0YUxpc3QubGVuZ3RoOyBpKyspIHsKICAgICAgICAgICAgICAgLy/mi7zmjqXpobnnm67luo/lj7cKICAgICAgICAgICAgICAgbGV0IHhtSW5kZXggPSBpICsgMTsKICAgICAgICAgICAgICAgc3RyICs9ICI8dHI+PHRkIGNvbHNwYW49JzUnIHN0eWxlPSd0ZXh0LWFsaWduOiBsZWZ0O2ZvbnQtd2VpZ2h0OiBib2xkO2ZvbnQtc2l6ZTogMTVweCc+IiArIHhtSW5kZXggKyAi44CBIiArIHRoaXMuc3lzakRhdGFMaXN0W2ldLnN5eG1tYyArICI8L3RkPjwvdHI+IjsKICAgICAgICAgICAgICAgLy8gdGhpcy5zeXNqRGF0YUxpc3RbaV0uYndMaXN0OwogICAgICAgICAgICAgICAvLyB0aGlzLnN5c2pEYXRhTGlzdFtpXS56eG1MaXN0OwogICAgICAgICAgICAgICAvLyBzdHIgKz0gIjx0cj48dGQ+Iit0aGlzLnN5c2pEYXRhTGlzdFtpXS5zeXhtbWMrIjwvdGQ+PHRkIHYtZm9yPWl0ZW0gaW4gdGhpcy5zeXNqRGF0YUxpc3RbaV0uYndMaXN0PjwvdGQ+PC90cj4iCgogICAgICAgICAgICAgfQogICAgICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgICAgICAkKCcjc3lzalRhYmxlSWQnKS5hcHBlbmQoc3RyKQogICAgICAgICAgICAgfSkKICAgICAgICAgICB9Ki8KICAgIH0sCiAgICAvL+a4suafk+mTreeJjOmhtemdouW8gOWni21wTGlzdDrlj43lm57nmoTpk63niYzliJfooaggIHJvd++8muaooeadv+ihjOWvueixoQogICAgYXBwbHlNcEh0bWwobXBMaXN0LCByb3cpIHsKICAgICAgLy/mr4/mrKHmiZPlvIDpnIDopoHph43mlrDmuLLmn5PkuIDmrKEs5YWI5bCG572u56m6CiAgICAgICQoJyNzYm1wVGJvZHlJZCcpLmh0bWwoIiIpOwogICAgICAvL+a4heepuumHjeaWsOi1i+WAvAogICAgICB0aGlzLm1iSW5mbyA9IHt9CiAgICAgIHRoaXMubWJJbmZvLm1ibWMgPSByb3cubWJtYzsKICAgICAgLy/mi7zmjqXnmoTpk63niYzooajmoLwKICAgICAgbGV0IHN0ciA9ICIiOwogICAgICAvL+WFiOWIpOaWreaYr+WQpuWIhuebuOmTreeJjAogICAgICBpZiAobXBMaXN0Lmxlbmd0aCA+IDApIHsKICAgICAgICBpZiAobXBMaXN0WzBdLlNGRlggPT0gJzEnKSB7IC8v5b2T5YmN6ZOt54mM5Li65YiG55u46ZOt54mM5pe2CiAgICAgICAgICAvL+WGmeatu+esrOS4gOihjAogICAgICAgICAgc3RyICs9ICI8dHI+PHRkIHN0eWxlPSdwYWRkaW5nOiAxMHB4O2ZvbnQtc2l6ZTogMTVweDsnPuebuOWIqzwvdGQ+IiArCiAgICAgICAgICAgICI8dGQgc3R5bGU9J3BhZGRpbmc6IDEwcHg7Zm9udC1zaXplOiAxNXB4Oyc+QTwvdGQ+IiArCiAgICAgICAgICAgICI8dGQgc3R5bGU9J3BhZGRpbmc6IDEwcHg7Zm9udC1zaXplOiAxNXB4Oyc+QjwvdGQ+IiArCiAgICAgICAgICAgICI8dGQgc3R5bGU9J3BhZGRpbmc6IDEwcHg7Zm9udC1zaXplOiAxNXB4Oyc+QzwvdGQ+IiArCiAgICAgICAgICAgICI8L3RyPiI7CiAgICAgICAgICAvL+W8gOWni+mBjeWOhuWxleekugogICAgICAgICAgZm9yIChsZXQgYSA9IDA7IGEgPCBtcExpc3QubGVuZ3RoOyBhKyspIHsKICAgICAgICAgICAgc3RyICs9ICI8dHI+IgogICAgICAgICAgICBzdHIgKz0gIjx0ZCBzdHlsZT0ncGFkZGluZzogMTBweDtmb250LXNpemU6IDE1cHg7Jz4iOwogICAgICAgICAgICBzdHIgKz0gbXBMaXN0W2FdLnRpdGxlICsgIjwvdGQ+IjsKICAgICAgICAgICAgc3RyICs9ICI8dGQ+PC90ZD4+IgogICAgICAgICAgICBzdHIgKz0gIjx0ZD48L3RkPj4iCiAgICAgICAgICAgIHN0ciArPSAiPHRkPjwvdGQ+PiIKICAgICAgICAgICAgc3RyICs9ICI8L3RyPiIKICAgICAgICAgIH0KICAgICAgICB9IGVsc2UgeyAgLy/pk63niYzkuI3liIbnm7gKICAgICAgICAgIC8v5b2T5YmN6ZOt54mM5LiN5bGe5LqO5YiG55u46ZOt54mMCiAgICAgICAgICAvL+avj+WIl+WxleekuuWNleWFg+agvOaVsOmHjwogICAgICAgICAgbGV0IGNvbCA9IDM7CiAgICAgICAgICAvL+WxleekuuihjOaVsAogICAgICAgICAgdmFyIGxpbmVzID0gTWF0aC5jZWlsKG1wTGlzdC5sZW5ndGggLyBjb2wpOwogICAgICAgICAgLy/pgY3ljoblsZXnpLrooYzmlbAKICAgICAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgbGluZXM7IGkrKykgewogICAgICAgICAgICBzdHIgKz0gIjx0cj4iOwogICAgICAgICAgICAvL+mBjeWOhuWIlwogICAgICAgICAgICBmb3IgKHZhciBqID0gMDsgaiA8IGNvbDsgaisrKSB7CiAgICAgICAgICAgICAgaWYgKGkgKiBjb2wgKyBqIDwgbXBMaXN0Lmxlbmd0aCkgewogICAgICAgICAgICAgICAgc3RyICs9ICI8dGQgc3R5bGU9J3BhZGRpbmc6IDEwcHg7Zm9udC1zaXplOiAxNXB4Oyc+IjsKICAgICAgICAgICAgICAgIC8v6ZOt54mM5qCH6aKY6LWL5YC8CiAgICAgICAgICAgICAgICBzdHIgKz0gbXBMaXN0W2kgKiBjb2wgKyBqXS50aXRsZSArICI8L3RkPiI7CiAgICAgICAgICAgICAgICAvL+mTreeJjOWAvOi1i+WAvAogICAgICAgICAgICAgICAgc3RyICs9IG1wTGlzdFtpICogY29sICsgal0uc2ZtYiA9PSAxID8gIjx0ZD4gJm5ic3A7Jm5ic3A7Jm5ic3A7Jm5ic3A7Jm5ic3A7Jm5ic3A7Jm5ic3A7Jm5ic3A7PC90ZD4iIDogIjx0ZD4iICsgbXBMaXN0W2kgKiBjb2wgKyBqXS5jb2x1bW5fbmFtZSArICI8L3RkPiIKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgICAgc3RyICs9ICI8L3RyPiI7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CiAgICAgIC8v5riy5p+T6ZOt54mM6aG16Z2iCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAkKCcjc2JtcFRib2R5SWQnKS5hcHBlbmQoc3RyKQogICAgICB9KQogICAgICAvL+aJk+W8gOW8ueWHuuahhgogICAgICB0aGlzLmlzU2hvd0Rvd25Mb2FkRGlhbG9nID0gdHJ1ZTsKICAgIH0sCiAgICAvL+WFs+mXremihOiniOW8ueWHuuahhgogICAgY2xvc2VZbERpYWxvZygpIHsKICAgICAgLy/muIXnqbrooajljZUKICAgICAgdGhpcy5tYkluZm8gPSB7fTsKICAgICAgLy/otYvlgLzlrozlhbPpl63lvLnnqpcKICAgICAgdGhpcy5pc1Nob3dEb3duTG9hZERpYWxvZyA9IGZhbHNlOwogICAgfQogICAgLAogICAgLy/lrprkuYnmqKHmnb/lhoXlrrkKICAgIGhhbmRsZUNsaWNrTWJucihyb3cpIHsKICAgICAgLy/miZPlvIDnu4Tku7blvLnlh7rmoYYKICAgICAgdGhpcy5pc1Nob3dYbUdsYndEaWFsb2cgPSB0cnVlOwogICAgICAvL+e7meWtkOe7hOS7tuS8oOmAkuaVsOaNrgogICAgICB0aGlzLm1iUm93RGF0YSA9IHJvdzsKICAgIH0sCiAgICAvL+iOt+WPlumhueebruW6k+mhueebruaVsOaNrgogICAgZ2V0WG1MaXJhcnlEYXRhKCkgewogICAgICBnZXRQYWdlTm9EYXRhTGlzdCh0aGlzLnhtTGlicmFyeVF1ZXJ5Rm9ybSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMueG1MaWJyYXJ5RGF0YUxpc3QgPSByZXMuZGF0YS5yZWNvcmRzCiAgICAgICAgdGhpcy54bUxpYnJhcnlRdWVyeUZvcm0udG90YWw9cmVzLmRhdGEudG90YWw7CiAgICAgICAgdGhpcy54bUxpYnJhcnlUb3RhbCA9IHJlcy5kYXRhLnRvdGFsCiAgICAgIH0pOwogICAgfSwKICAgIC8v6aG555uu5by55Ye65qGG5paw5aKe5oyJ6ZKuCiAgICBhZGRNYkdsWG0oKSB7CiAgICAgIHRoaXMuZ2V0WG1MaXJhcnlEYXRhKCkKICAgICAgdGhpcy5pc1Nob3dBZGRHbHhtRGlhbG9nID0gdHJ1ZQogICAgfQogICAgLAogICAgLy/pobnnm67lupPlvLnlh7rmoYblj5bmtojmjInpkq4KICAgIGNsb3NlQWRkTWp6RGlhbG9nKCkgewogICAgICB0aGlzLmlzU2hvd0FkZEdseG1EaWFsb2cgPSBmYWxzZTsKICAgICAgdGhpcy54bUxpYnJhcnlRdWVyeUZvcm0ubXBtYyA9IHVuZGVmaW5lZDsKICAgIH0KICAgICwKICAgIC8v6aG555uu5bqT5by556qX56Gu6K6k5oyJ6ZKuCiAgICBjb21taXRBZGRNanpGb3JtKCkgewogICAgICB0aGlzLnhtbWJsaXN0PVtdOwogICAgICBpZiAodGhpcy54bVNlbGVjdGVkRm9ybS54bURhdGFSb3dzLmxlbmd0aCA8IDEpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmluZm8oJ+acquWFs+iBlOmhueebru+8ge+8ge+8geW3suWPlua2iCcpCiAgICAgICAgLy/lpoLmnpzmnKrpgInkuK3mlbDmja4s5YiZ55u05o6l5YWz6Zet5by556qXCiAgICAgICAgdGhpcy5pc1Nob3dBZGRHbHhtRGlhbG9nID0gZmFsc2UKICAgICAgfSBlbHNlIHsKICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IHRoaXMueG1tYlNlbGVjdGVkUm93Lmxlbmd0aDsgaSsrKSB7CiAgICAgICAgICB0aGlzLnhtU2VsZWN0ZWRGb3JtID0ge307CiAgICAgICAgICB0aGlzLnhtU2VsZWN0ZWRGb3JtLm1waWQgPSB0aGlzLnhtbWJTZWxlY3RlZFJvd1tpXS5vYmpJZDsKICAgICAgICAgIHRoaXMueG1TZWxlY3RlZEZvcm0uc3ltYmlkID0gdGhpcy5zeW1iaWQ7CiAgICAgICAgICB0aGlzLnhtU2VsZWN0ZWRGb3JtLmlzTXBTeXhtID0gMTsKICAgICAgICAgIHRoaXMueG1tYmxpc3QucHVzaCh0aGlzLnhtU2VsZWN0ZWRGb3JtKTsKICAgICAgICB9CiAgICAgICAgLy/oi6XpgInmi6nmlbDmja7lkI4KICAgICAgICBzYXZlTXd0VWRTeU1ibXAodGhpcy54bW1ibGlzdCkudGhlbihyZXMgPT4gewogICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAnMDAwMCcpIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCflhbPogZTmiJDlip8nKQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5YWz6IGU5aSx6LSl77yB77yBJykKICAgICAgICAgIH0KICAgICAgICAgIC8v5YWz6Zet5by556qXCiAgICAgICAgICB0aGlzLmlzU2hvd0FkZEdseG1EaWFsb2cgPSBmYWxzZQogICAgICAgICAgLy/osIPnlKjojrflj5blhbPogZTlrZDpobnnm67liJfooagKICAgICAgICAgIHRoaXMuZ2V0U3ltYkdsc3l4bURhdGFMaXN0QnlQYWdlKCkKICAgICAgICB9KQogICAgICB9CiAgICB9CiAgICAsCiAgICAvL+mhueebruW6k+ihjOmAieS4reS6i+S7tgogICAgaGFuZGxlU2VsZWN0ZWRYbUxpYnJhcnlDaGFuZ2Uocm93cykgewogICAgICB0aGlzLnhtU2VsZWN0ZWRGb3JtLnhtRGF0YVJvd3MgPSByb3dzCiAgICAgIHRoaXMueG1tYlNlbGVjdGVkUm93PVtdOwogICAgICB0aGlzLnhtbWJTZWxlY3RlZFJvdz1yb3dzOwogICAgICBjb25zb2xlLmxvZygic2RmeXMtLSIsdGhpcy54bVNlbGVjdGVkRm9ybS54bURhdGFSb3dzKTsKICAgIH0KICAgICwKICAgIC8v6aG555uu5bqT5p+l6K+i5oyJ6ZKuCiAgICBzZWxlY3R4bUxpYnJhcnkoKSB7CiAgICAgIHRoaXMuZ2V0WG1MaXJhcnlEYXRhKCkKICAgIH0KICAgICwKICAgIC8v6aG555uu5bqT6YeN572u5oyJ6ZKuCiAgICByZXNldHhtU2VhcmNoKCkgewogICAgICB0aGlzLnhtTGlicmFyeVF1ZXJ5Rm9ybS5zeXhtbWMgPSAnJwogICAgICB0aGlzLmdldFhtTGlyYXJ5RGF0YSgpCiAgICB9CiAgICAsCiAgICAvL+iOt+WPluWFs+iBlOWtkOWIl+ihqOaWueazlQogICAgZ2V0WnhtRGF0YUxpc3QoKSB7CiAgICAgIGdldEdsU3l6eG1EYXRhTGlzdEJ5UGFnZSh0aGlzLmdsenhtUXVlcnlQYXJhbXMpLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmdsenhtVG90YWwgPSByZXMuZGF0YS50b3RhbAogICAgICAgIHRoaXMuenhtR2xtYkRhdGFMaXN0ID0gcmVzLmRhdGEucmVjb3JkcwogICAgICB9KQogICAgfQogICAgLAoKICAgIC8v5YWz6IGU6aG555uuCiAgICBoYW5kbGVDbGlja0dseG0ocm93KSB7CiAgICAgIC8v5riF56m65Y6f5p2l5a2Q6aG555uu5pWw5o2uCiAgICAgIHRoaXMubWJHbHhtRGF0YUxpc3Q9W107CiAgICAgIHRoaXMuenhtR2xtYkRhdGFMaXN0ID0gW107CiAgICAgIHRoaXMueG1tYlNlbGVjdGVkUm93PVtdOwogICAgICAvL+aJk+W8gOWFs+iBlOmhueebruW8ueWHuuahhgogICAgICB0aGlzLmlzR2x4bURpYWxvZ1Nob3cgPSB0cnVlCiAgICAgIC8v57uZ5Y+C5pWw6LWL5YC8CiAgICAgIHRoaXMuZ2x4bVF1ZXJ5UGFyYW1zLnN5bWJpZCA9IHJvdy5vYmpJZDsKICAgICAgdGhpcy5zeW1iaWQgPSByb3cub2JqSWQ7CiAgICAgIC8v5p+l6K+i6aG555uu5bqT5pWw5o2u5pe25Y+C5pWwCiAgICAgIHRoaXMueG1MaWJyYXJ5UXVlcnlGb3JtLnN5bWJpZCA9IHJvdy5vYmpJZAogICAgICAvL+e7meivlemqjOmhueebruW6k+a3u+WKoOaXtuS9v+eUqAogICAgICB0aGlzLnhtU2VsZWN0ZWRGb3JtLnN5bWJpZCA9IHJvdy5vYmpJZAogICAgICAvL+iOt+WPluaooeadv+WFs+iBlOmhueebruaVsOaNrgogICAgICB0aGlzLmdldFN5bWJHbHN5eG1EYXRhTGlzdEJ5UGFnZSgpCiAgICB9CiAgICAsCiAgICAvL+iOt+WPluWFs+iBlOmhueebruW8ueWHuuahhuaVsOaNrgogICAgZ2V0U3ltYkdsc3l4bURhdGFMaXN0QnlQYWdlKCkgewogICAgICBnZXRNcG1jRGF0YUJ5SWQodGhpcy5nbHhtUXVlcnlQYXJhbXMpLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLm1iR2x4bURhdGFMaXN0ID0gcmVzLmRhdGEucmVjb3JkcwogICAgICAgIHRoaXMuZ2x4bVRvdGFsID0gcmVzLmRhdGEudG90YWwKICAgICAgICB0aGlzLmdseG1RdWVyeVBhcmFtcy50b3RhbD1yZXMuZGF0YS50b3RhbAogICAgICB9KTsKICAgIH0sCiAgICAvL+ivlemqjOmhueebruWkjemAieahhueCueWHu+aXtumXtOeCueWHu+aTjeS9nAogICAgaGFuZGxlR2x4bVNlbGVjdGVkQ2hhbmdlKHJvd3MpIHsKICAgICAgdGhpcy5zZWxlY3RlZFJvd0RhdGFDaGFuZ2UgPSByb3dzCiAgICB9CiAgICAsCiAgICAvL+WIoOmZpOaooeadv+WFs+iBlOmhueebrgogICAgZGVsZXRlTWJHbFhtKCkgewogICAgICBpZiAodGhpcy5zZWxlY3RlZFJvd0RhdGFDaGFuZ2UubGVuZ3RoIDwgMSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup5q2j56Gu55qE5pWw5o2u77yB77yB77yBJykKICAgICAgICByZXR1cm47CiAgICAgIH0KICAgICAgbGV0IGlkcyA9IHRoaXMuc2VsZWN0ZWRSb3dEYXRhQ2hhbmdlLm1hcChpdGVtID0+IHsKICAgICAgICByZXR1cm4gaXRlbS5tYm1wSWQ7CiAgICAgIH0pOwogICAgICB0aGlzLiRjb25maXJtKCfmraTmk43kvZzlsIbmsLjkuYXliKDpmaTor6XmlbDmja4sIOaYr+WQpue7p+e7rT8nLCAn5o+Q56S6JywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgZGVsZXRlTXd0VWRTeU1ibXAoaWRzKS50aGVuKCh7Y29kZX0pID0+IHsKICAgICAgICAgIGlmIChjb2RlID09PSAnMDAwMCcpIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgICAgIG1lc3NhZ2U6ICfliKDpmaTmiJDlip8hJwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgdGhpcy5nZXRTeW1iR2xzeXhtRGF0YUxpc3RCeVBhZ2UoKQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgICAgICAgICBtZXNzYWdlOiAn5Yig6Zmk5aSx6LSlIScKICAgICAgICAgICAgfSkKICAgICAgICAgICAgdGhpcy5nZXRTeW1iR2xzeXhtRGF0YUxpc3RCeVBhZ2UoKQogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgIHR5cGU6ICdpbmZvJywKICAgICAgICAgIG1lc3NhZ2U6ICflt7Llj5bmtojliKDpmaQnCiAgICAgICAgfSkKICAgICAgfSkKICAgIH0sCiAgICAvL+ivlemqjOmhueebrueCueWHu+ihjOaVsOaNruaXtueahOWNleacuuaTjeS9nAogICAgaGFuZGxlTWJHbHhtUm93Q2xpY2socm93KSB7CiAgICAgIHRoaXMuZ2x6eG1RdWVyeVBhcmFtcy5zeXhtaWQgPSByb3cuc3l4bWlkCiAgICAgIHRoaXMuZ2V0WnhtRGF0YUxpc3QoKQogICAgfSwKICAgIC8v5oeS5Yqg6L295Ye95pWwCiAgICBsb2FkTm9kZShub2RlLCByZXNvbHZlKSB7CiAgICAgIGxldCBUcmVlcGFyYW1NYXAgPSB7CiAgICAgICAgcGlkOiAnJywKICAgICAgICBzcGJMb2dvOiBbJ+i+k+eUteiuvuWkhycsICflj5jnlLXorr7lpIcnLCfphY3nlLXorr7lpIcnXQogICAgICB9CiAgICAgIGlmIChub2RlLmxldmVsID09PSAwKSB7CiAgICAgICAgVHJlZXBhcmFtTWFwLnBpZCA9ICdzYicKICAgICAgICByZXR1cm4gdGhpcy5nZXRUcmVlTm9kZShUcmVlcGFyYW1NYXAsIHJlc29sdmUpCiAgICAgIH0KICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgVHJlZXBhcmFtTWFwLnBpZCA9IG5vZGUuZGF0YS5jb2RlCiAgICAgICAgdGhpcy5nZXRUcmVlTm9kZShUcmVlcGFyYW1NYXAsIHJlc29sdmUpCiAgICAgIH0sIDUwMCkKICAgIH0sCiAgICAvL+iOt+WPluagkeiKgueCueaVsOaNrgogICAgZ2V0VHJlZU5vZGUocGFyYW1NYXAsIHJlc29sdmUpIHsKICAgICAgZ2V0RGV2aWNlQ2xhc3NUcmVlTm9kZUJ5UGlkKHBhcmFtTWFwKS50aGVuKHJlcyA9PiB7CiAgICAgICAgbGV0IHRyZWVOb2RlcyA9IFtdCiAgICAgICAgcmVzLmRhdGEuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIGxldCBub2RlID0gewogICAgICAgICAgICBuYW1lOiBpdGVtLm5hbWUsCiAgICAgICAgICAgIGxldmVsOiBpdGVtLmxldmVsLAogICAgICAgICAgICBpZDogaXRlbS5pZCwKICAgICAgICAgICAgcGlkOiBpdGVtLnBpZCwKICAgICAgICAgICAgbGVhZjogZmFsc2UsCiAgICAgICAgICAgIGNvZGU6IGl0ZW0uY29kZQogICAgICAgICAgfQogICAgICAgICAgdHJlZU5vZGVzLnB1c2gobm9kZSkKICAgICAgICB9KQogICAgICAgIHJlc29sdmUodHJlZU5vZGVzKQogICAgICB9KQogICAgfSwKICAgIC8v5re75Yqg5ZCO56Gu6K6k5L+d5a2Y5oyJ6ZKuCiAgICBzYXZlKCkgewogICAgICB0aGlzLiRyZWZzWydmb3JtJ10udmFsaWRhdGUoKHZhbGlkKSA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBzYXZlT3JVcGRhdGUodGhpcy5mb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gJzAwMDAnKSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKHJlcy5tc2cpCiAgICAgICAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnBhZ2VSZXNpemUgPSAnWScKICAgICAgICAgICAgICB0aGlzLmdldERhdGEoKQogICAgICAgICAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IGZhbHNlCiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubXNnKQogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIC8v5qCR6IqC54K554K55Ye75LqL5Lu2CiAgICBoYW5kbGVOb2RlQ2xpY2soZGF0YSkgewogICAgICBjb25zb2xlLmxvZyhkYXRhKQogICAgICBpZiAoZGF0YS5sZXZlbCAhPSAnMCcpIHsKICAgICAgICAvL+aWsOWinuaMiemSruWPr+eCueWHuwogICAgICAgIHRoaXMuYWRkRGlzYWJsZWQgPSBmYWxzZQogICAgICAgIHRoaXMudHJlZUZvcm0gPSBkYXRhCiAgICAgICAgdGhpcy5xdWVyeVN5QndQYXJhbS5zYmx4aWQgPSBkYXRhLmNvZGUKICAgICAgICB0aGlzLmdldERhdGEoKQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuYWRkRGlzYWJsZWQgPSB0cnVlCiAgICAgIH0KICAgIH0sCiAgICAvL+a3u+WKoOaMiemSrgogICAgYWRkU2Vuc29yQnV0dG9uKCkgewogICAgICB0aGlzLmZvcm0gPSB7fQogICAgICB0aGlzLmZvcm0uc2JseCA9IHRoaXMudHJlZUZvcm0ubmFtZTsKICAgICAgdGhpcy5mb3JtLnNibHhpZCA9IHRoaXMudHJlZUZvcm0uY29kZTsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gdHJ1ZTsKICAgICAgdGhpcy50aXRsZSA9ICfmlrDlop4nOwogICAgfSwKICAgIC8v5YiX6KGo5p+l6K+iCiAgICBhc3luYyBnZXREYXRhKHBhcmFtcykgewogICAgICB0cnkgewogICAgICAgIHRoaXMucXVlcnlTeUJ3UGFyYW0gPSB7Li4udGhpcy5xdWVyeVN5QndQYXJhbSwgLi4ucGFyYW1zfQogICAgICAgIGNvbnN0IHBhcmFtID0gdGhpcy5xdWVyeVN5QndQYXJhbQogICAgICAgIGNvbnN0IHtkYXRhLCBjb2RlfSA9IGF3YWl0IGdldFBhZ2VEYXRhTGlzdFRvc3ltYihwYXJhbSkKICAgICAgICBpZiAoY29kZSA9PT0gJzAwMDAnKSB7CiAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8udGFibGVEYXRhID0gZGF0YS5yZWNvcmRzCiAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIudG90YWwgPSBkYXRhLnRvdGFsCiAgICAgICAgfQogICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgY29uc29sZS5sb2coZSkKICAgICAgfQogICAgfQogICAgLAogICAgLyoqCiAgICAgKiDooajmoLzlpJrpgInmoYYKICAgICAqLwogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHJvd3MpIHsKICAgICAgdGhpcy5zZWxlY3RSb3dzID0gcm93cwogICAgfQogICAgLAoKICAgIGNsb3NlKCkgewogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSBmYWxzZQogICAgfQogICAgLAogICAgLy/kv67mlLnmqKHmnb/kuLvooajlhoXlrrkKICAgIHVwZGF0ZURldGFpbHMocm93KSB7CiAgICAgIHRoaXMudGl0bGUgPSAn5L+u5pS5JzsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gdHJ1ZTsKICAgICAgdGhpcy5mb3JtID0gcm93OwogICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZTsKICAgICAgdGhpcy5pc1Nob3cgPSB0cnVlOwogICAgfSwKCiAgICBjcmVhdGVUZW1wbGF0ZShyb3cpIHsKICAgICAgY29uc29sZS5sb2cocm93KQogICAgfQogICAgLAogICAgLy/mn6XnnIvmqKHmnb/kuLvooajor6bmg4XmjInpkq4KICAgIGdldERldGFpbHMocm93KSB7CiAgICAgIHRoaXMudGl0bGUgPSAn6K+m5oOFJwogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlOwogICAgICB0aGlzLmZvcm0gPSByb3c7CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IHRydWU7CiAgICAgIHRoaXMuaXNTaG93ID0gZmFsc2U7CiAgICB9LAoKICAgIC8v5Yig6Zmk5oyJ6ZKuCiAgICAvLyBkZWxldGVTZW5zb3JCdXR0b24ob2JqSWQpIHsKICAgIC8vICAgZGVidWdnZXI7CiAgICAvLyAgIGNvbnNvbGUubG9nKCIxMjMrb2JqSWQiLG9iaklkKTsKICAgIC8vICAgdGhpcy4kY29uZmlybSgi5q2k5pON5L2c5bCG5rC45LmF5Yig6Zmk6K+l5pWw5o2uLCDmmK/lkKbnu6fnu60/IiwgIuaPkOekuiIsIHsKICAgIC8vICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAvLyAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAvLyAgICAgdHlwZTogIndhcm5pbmciLAogICAgLy8gICB9KQogICAgLy8gICAgLnRoZW4oKCkgPT4gewogICAgLy8gICAgIHJlbW92ZVN5bWIoSlNPTi5zdHJpbmdpZnkob2JqSWQpKS50aGVuKCh7Y29kZX0pID0+IHsKICAgIC8vICAgICAgIGlmIChjb2RlID09PSAnMDAwMCcpIHsKICAgIC8vICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAvLyAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgLy8gICAgICAgICAgIG1lc3NhZ2U6ICfliKDpmaTmiJDlip8hJwogICAgLy8gICAgICAgICB9KQogICAgLy8gICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIucGFnZVJlc2l6ZSA9ICdZJwogICAgLy8gICAgICAgICB0aGlzLmdldERhdGEoKQogICAgLy8gICAgICAgfSBlbHNlIHsKICAgIC8vICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAvLyAgICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgIC8vICAgICAgICAgICBtZXNzYWdlOiAn5Yig6Zmk5aSx6LSlIScKICAgIC8vICAgICAgICAgfSkKICAgIC8vICAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnBhZ2VSZXNpemUgPSAnWScKICAgIC8vICAgICAgICAgdGhpcy5nZXREYXRhKCkKICAgIC8vICAgICAgIH0KICAgIC8vICAgICB9KQogICAgLy8gICB9KS5jYXRjaCgoKSA9PiB7CiAgICAvLyAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAvLyAgICAgICB0eXBlOiAnaW5mbycsCiAgICAvLyAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raI5Yig6ZmkJwogICAgLy8gICAgIH0pCiAgICAvLyAgIH0pCgogICAgLy8gfSwKCiAgICAgZGVsZXRlU2Vuc29yQnV0dG9uKGlkKSB7CiAgICAgIHRoaXMuJGNvbmZpcm0oIuatpOaTjeS9nOWwhuawuOS5heWIoOmZpOivpeaVsOaNriwg5piv5ZCm57un57utPyIsICLmj5DnpLoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwKICAgICAgfSkKICAgICAgICAudGhlbigoKSA9PiB7CiAgICAgICAgICByZW1vdmVTeW1iKEpTT04uc3RyaW5naWZ5KGlkKSkudGhlbigoeyBjb2RlIH0pID0+IHsKICAgICAgICAgICAgaWYgKGNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLAogICAgICAgICAgICAgICAgbWVzc2FnZTogIuWIoOmZpOaIkOWKnyEiLAogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTlpLHotKUhIiwKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfSkKICAgICAgICAuY2F0Y2goKCkgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICJpbmZvIiwKICAgICAgICAgICAgbWVzc2FnZTogIuW3suWPlua2iOWIoOmZpCIsCiAgICAgICAgICB9KTsKICAgICAgICB9KTsKICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnBhZ2VSZXNpemUgPSAiWSI7CiAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgfSwKCiAgICAvL+WvvOWHuuaMiemSrgogICAgaGFuZGxlRXhwb3J0KCkgewoKICAgIH0KICAgICwKCiAgICAvL+WFs+iBlOmTreeJjOeCueWHu+S6i+S7tgogICAgaGFuZGxlQ2xpY2tHbE1wKHJvdykgewogICAgICB0aGlzLnNob3dNcERpYWxvZyA9IHRydWUKICAgICAgdGhpcy5yb3dEYXRhID0gcm93CiAgICB9CiAgICAsCiAgICAvL+WFs+mXreivlemqjOmTreeJjOW8ueeqlwogICAgY2xvc2VNcERpYWxvZygpIHsKICAgICAgdGhpcy5zaG93TXBEaWFsb2cgPSBmYWxzZQogICAgfQogICAgLAoKICAgIGZpbHRlclJlc2V0KCkgewogICAgICB0aGlzLnF1ZXJ5U3lCd1BhcmFtID0gewogICAgICAgIHNibHhpZDogdW5kZWZpbmVkLAogICAgICAgIG1ibWM6IHVuZGVmaW5lZCwKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMAogICAgICB9CiAgICB9LAogICAgLy/muIXnqbrooajljZXmlbDmja4KICAgIGhhbmRsZUNsb3NlKCl7CiAgICAgIHRoaXMuZm9ybT17fTsKICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIHRoaXMuZm9ybSA9IHRoaXMuJG9wdGlvbnMuZGF0YSgpLmZvcm07CiAgICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgICAgfSk7CiAgICB9LAogIH0KfQo="}, {"version": 3, "sources": ["symbwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8ZA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "symbwh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row :gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card class=\"aside_height\" shadow=\"never\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备类型</span>\n          </div>\n          <div style=\" overflow: auto;height: 84vh\">\n            <el-col style=\"padding:0\">\n              <el-tree id=\"tree\"\n                       :props=\"props\"\n                       highlight-current\n                       :load=\"loadNode\"\n                       lazy\n                       :default-expanded-keys=\"['1']\"\n                       @node-expand=\"handleNodeClick\"\n                       @node-click=\"handleNodeClick\"/>\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 140, itemWidth: 190 }\"\n          @handleReset=\"filterReset\"\n        />\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button @click=\"addSensorButton\"\n                       type=\"primary\" icon=\"el-icon-plus\"\n            >新增\n            </el-button>\n          </div>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"69.8vh\">\n            <el-table-column slot=\"table_six\" align=\"center\" label=\"关联铭牌\" min-width=\"150\" :resizable=\"false\">\n              <template slot-scope=\"scope\">\n                <el-button @click=\"handleClickGlMp(scope.row)\" type=\"text\" size=\"small\" v-if=\"scope.row.sfty == '是'\">\n                  查看铭牌\n                </el-button>\n                <el-button @click=\"handleClickGlMp(scope.row)\" type=\"text\" size=\"small\" v-else>关联铭牌</el-button>\n              </template>\n            </el-table-column>\n\n            <el-table-column slot=\"table_seven\" align=\"center\" label=\"关联项目\" min-width=\"150\" :resizable=\"false\">\n              <template slot-scope=\"scope\">\n                <el-button @click=\"handleClickGlxm(scope.row)\" type=\"text\" size=\"small\" v-if=\"scope.row.sfty == '是'\">\n                  查看项目\n                </el-button>\n                <el-button @click=\"handleClickGlxm(scope.row)\" type=\"text\" size=\"small\" v-else>关联项目</el-button>\n              </template>\n            </el-table-column>\n\n            <!-- <el-table-column slot=\"table_eight\" align=\"center\" label=\"定义模板内容\" min-width=\"150\" :resizable=\"false\">\n              <template slot-scope=\"scope\">\n                <el-button @click=\"handleClickMbnr(scope.row)\" type=\"text\" size=\"small\" v-if=\"scope.row.sfty == '是'\">\n                  查看模板内容\n                </el-button>\n                <el-button @click=\"handleClickMbnr(scope.row)\" type=\"text\" size=\"small\" v-else>定义模板内容</el-button>\n              </template>\n            </el-table-column> -->\n\n            <el-table-column slot=\"table_eight\" align=\"center\" label=\"模板详情\" min-width=\"150\" :resizable=\"false\" height=\"70vh\">\n              <template slot-scope=\"scope\">\n<!--                <el-button @click=\"handleMbInfo(scope.row)\" v-print=\"printObj\">默认按钮</el-button>-->\n                <el-button  type=\"text\" size=\"small\" @click=\"handleMbInfo(scope.row)\">模板详情</el-button>\n              </template>\n            </el-table-column>\n            <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\" width=\"160\"\n                             :resizable=\"false\">\n              <template slot-scope=\"scope\">\n                <el-button @click=\"updateDetails(scope.row)\" v-show=\"scope.row.createBy == currentUser\" type=\"text\" size=\"small\" title=\"修改\"  class='el-icon-edit'></el-button>\n                <el-button @click=\"getDetails(scope.row)\" type=\"text\" size=\"small\" title=\"详情\" class=\"el-icon-view\"></el-button>\n                <el-button type=\"text\" size=\"small\"  v-show=\"scope.row.createBy == currentUser\" @click=\"deleteSensorButton(scope.row.objId)\" title=\"删除\" class=\"el-icon-delete\"></el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n    <!--新增\\修改\\详情弹出-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"60%\"\n      @close=\"handleClose\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"mbzbRules\">\n        <el-row :gutter=\"8\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"设备类型：\" prop=\"sblx\">\n              <el-input\n                v-model=\"form.sblx\"\n                disabled\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"设备类型编码：\" prop=\"sblxbm\">\n              <el-input\n                v-model=\"form.sblxid\"\n                disabled\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"模板名称：\" prop=\"mbmc\">\n              <el-input\n                placeholder=\"请输入试验部位名称\"\n                v-model=\"form.mbmc\"\n                :disabled=\"isDisabled\"\n                style=\"width: 100%\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否默认：\" prop=\"sfmr\">\n              <el-select\n                v-model=\"form.sfmr\"\n                style=\"width: 100%\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option label=\"是\" value=\"是\"></el-option>\n                <el-option label=\"否\" value=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否停用：\" prop=\"sfty\">\n              <el-select\n                v-model=\"form.sfty\"\n                style=\"width: 100%\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option label=\"是\" value=\"是\"></el-option>\n                <el-option label=\"否\" value=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" v-show=\"isShow\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"save\">确 认</el-button>\n      </div>\n    </el-dialog>\n\n    <!--关联铭牌弹框-->\n    <el-dialog\n      :visible.sync=\"showMpDialog\"\n      v-dialogDrag\n      title=\"已关联铭牌\"\n      v-if=\"showMpDialog\"\n    >\n      <glsymp\n        :main-data=\"rowData\"\n        :tree-data=\"treeForm\"\n        @closeMpDialog=\"closeMpDialog\"\n      ></glsymp>\n    </el-dialog>\n\n    <!--关联试验项目弹出框-->\n    <el-dialog\n      :title=\"glxmDialogTitle\"\n      v-dialogDrag\n      :visible.sync=\"isGlxmDialogShow\"\n      width=\"60%\"\n    >\n      <el-row :gutter=\"3\">\n        <div class=\"mb8 pull-right\">\n          <el-button\n            type=\"primary\"\n            icon=\"el-icon-plus\"\n            size=\"mini\"\n            @click=\"addMbGlXm\"\n          >新增项目</el-button\n          >\n          <el-button\n            type=\"danger\"\n            icon=\"el-icon-delete\"\n            size=\"mini\"\n            @click=\"deleteMbGlXm\"\n          >删除项目</el-button\n          >\n        </div>\n      </el-row>\n      <el-row :gutter=\"20\">\n        <el-table\n          :data=\"mbGlxmDataList\"\n          @selection-change=\"handleGlxmSelectedChange\"\n          @row-click=\"handleMbGlxmRowClick\"\n        >\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n          <el-table-column\n            label=\"序号\"\n            type=\"index\"\n            width=\"50\"\n            align=\"center\"\n          ></el-table-column>\n          <el-table-column\n            prop=\"mpmc\"\n            label=\"项目名称\"\n            align=\"center\"\n          ></el-table-column>\n        </el-table>\n        <pagination\n          v-show=\"glxmQueryParams.total > 0\"\n          :total=\"glxmQueryParams.total\"\n          :page.sync=\"glxmQueryParams.pageNum\"\n          :limit.sync=\"glxmQueryParams.pageSize\"\n          @pagination=\"getSymbGlsyxmDataListByPage\"/>\n      </el-row>\n    </el-dialog>\n    <!--列表新增关联项目弹窗调用-->\n    <el-dialog\n      :title=\"xmLibraryAddDialogTitle\"\n      v-dialogDrag\n      :visible.sync=\"isShowAddGlxmDialog\"\n      width=\"50%\"\n      @close=\"closeAddMjzDialog\"\n    >\n      <el-form label-width=\"120px\">\n        <el-row :gutter=\"3\">\n          <el-col :span=\"10\">\n            <el-form-item label=\"项目名称：\">\n              <el-input v-model=\"xmLibraryQueryForm.mpmc\" />\n            </el-form-item>\n          </el-col>\n          <div class=\"mb8 pull-right\">\n            <el-button\n              type=\"cyan\"\n              size=\"mini\"\n              icon=\"el-icon-search\"\n              @click=\"selectxmLibrary\"\n            >查询</el-button\n            >\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetxmSearch\"\n            >重置</el-button\n            >\n          </div>\n        </el-row>\n      </el-form>\n      <el-table\n        stripe\n        border\n        :data=\"xmLibraryDataList\"\n        @selection-change=\"handleSelectedXmLibraryChange\"\n        :header-cell-style=\"{ 'text-align': 'center' }\"\n        :cell-style=\"{ 'text-align': 'center' }\"\n      >\n        <el-table-column type=\"selection\" width=\"55\" />\n        <el-table-column label=\"序号\" type=\"index\" width=\"50\">\n        </el-table-column>\n        <el-table-column\n          label=\"试验项目\"\n          prop=\"mpmc\"\n          :show-overflow-tooltip=\"true\"\n        />\n      </el-table>\n      <pagination\n        v-show=\"xmLibraryQueryForm.total > 0\"\n        :total=\"xmLibraryQueryForm.total\"\n        :page.sync=\"xmLibraryQueryForm.pageNum\"\n        :limit.sync=\"xmLibraryQueryForm.pageSize\"\n        @pagination=\"getXmLiraryData\"\n      />\n      <div slot=\"footer\">\n        <el-button @click=\"closeAddMjzDialog\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"commitAddMjzForm\">确 认</el-button>\n      </div>\n    </el-dialog>\n    <!--子组件定义模板内容-->\n    <!-- <el-dialog title=\"项目关联部位\" :visible.sync=\"isShowXmGlbwDialog\" v-if=\"isShowXmGlbwDialog\"> -->\n    <!-- <symbwh-dymbnr ref=\"symbwhDymbnrRef\" :mb-data=\"mbRowData\"></symbwh-dymbnr>\n  </el-dialog> -->\n    <!--  打印vue print nb插件-->\n    <div v-show=\"false\">\n      <div id=\"printHtmlId\" style=\"background:white;\">\n        <!--模板-->\n        <div style=\"text-align: center\">\n          <p>{{mbInfo.mbmc}}</p>\n        </div>\n        <p>葫芦娃，葫芦娃</p>\n        <p>一根藤上七朵花 </p>\n        <p>小小树藤是我家 啦啦啦啦 </p>\n        <p>叮当当咚咚当当　浇不大</p>\n        <p> 叮当当咚咚当当 是我家</p>\n        <p> 啦啦啦啦</p>\n        <p>...</p>\n        <div class=\"describle\">\n          <el-form :model=\"mbInfo\" :rules=\"mbzbRules\" ref=\"from\" class=\"demo-ruleForm\">\n            <el-form-item label=\"姓名:\" prop=\"name\">\n              <el-input v-model=\"mbInfo.mbmc\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"描述:\" prop=\"describle\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"4\"\n                :maxlength=\"2000\"\n                placeholder=\"\"\n                v-model=\"mbInfo.mbmc\">\n              </el-input>\n            </el-form-item>\n          </el-form>\n        </div>\n      </div>\n    </div>\n    <!--htmlToPdf插件-->\n    <el-dialog title=\"预览\" :visible.sync=\"isShowDownLoadDialog\" width=\"60%\" class=\"outPut\" v-dialogDrag>\n      <!--      <el-button @click=\"downloadPdf\" >导出</el-button>-->\n      <el-button @click=\"outputPdfFun\" v-print=\"printObj\">导出</el-button>\n      <div style=\"width: 100%;height:60vh;overflow: auto\">\n        <div id=\"printContentId\">\n          <div style=\"text-align: center\">\n            <h2>{{mbInfo.mbmc}}</h2></div>\n          <div >\n            <div style=\"font-size: 20px;float: left;width:100%;border-left: 1px solid #000;border-right: 1px solid #000;border-top: 1px solid #000;\">一、基本信息</div>\n            <el-table\n              :data=\"tableData_jbxx\"\n              border\n              style=\"border: 1px solid #000;\"\n              @cell-click=\"editData\"\n            >\n              <el-table-column\n                property=\"date\"\n                prop=\"column_1\"\n                label=\"变电站\"\n              >\n              </el-table-column>\n              <el-table-column\n                @click=\"elClick\"\n                prop=\"column_5\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_2\"\n                label=\"委托单位\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_6\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_3\"\n                label=\"试验单位\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_7\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_4\"\n                label=\"运行编号\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_8\"\n                label=\"\"\n              >\n              </el-table-column>\n            </el-table>\n          </div>\n          <!--  第二个表格内容   -->\n          <div class=\"printTitle\" style=\"font-size: 20px;padding-left: 0;\">二、设备铭牌</div>\n          <table id=\"h2_table\" style=\"width: 100%;border-collapse: collapse;\" border=\"1\"></table>\n          <!--  第三个表格内容   -->\n          <div class=\"printTitle\" style=\"font-size: 20px;padding-left: 0;\">三、试验数据</div>\n          <table id=\"h3_table\" style=\"width: 100%;border-collapse: collapse;\" border=\"1\"></table>\n<!--          <div >\n            <div style=\"font-size: 20px;float: left;width:100%;border-left: 1px solid #000;border-right: 1px solid #000;border-bottom: 1px solid #000;\">三、试验数据</div>\n            <div v-for=\"item in arr\" style=\"width: 100%\">\n              <div class=\"printTitle\">{{item.title}}</div>\n              <el-table :data=\"item.bwList\" style=\"width:100%;border: 1px solid #000;\" border\n                        :span-method=\"arraySpanMethod\">\n                <template v-for='(val) in item.zxmList'>\n                  <el-table-column\n                    :prop=\"val.column_name\"\n                    width=\"auto\"\n                    :label=\"val.label\"\n                  >\n                  </el-table-column>\n\n                </template>\n\n              </el-table>\n            </div>\n          </div>-->\n        </div>\n      </div>\n      <div slot=\"footer\">\n        <el-button @click=\"closeYlDialog\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n//引入jquery,暂时没用\nimport $ from \"jquery\"\nimport {\n  addMbGlxmBatchToMbxm,\n  getPageDataListTosymb,\n  getSymbGlsyxmDataListByPage,\n  getXmLiraryData,\n  remove,\n  saveOrUpdate,\n  getMbGlMpinfoData,\n  getMbGlXmAndBw,\n  getMwtUdSyMpxqByMbzb,\n  removeSymb,\n} from '@/api/dagangOilfield/bzgl/symbwh'\nimport {getDeviceClassTreeNodeByPid} from '@/api/dagangOilfield/bzgl/sblxwh/sblxwh'\nimport {getGlSyzxmDataListByPage} from '@/api/dagangOilfield/bzgl/syxm'\nimport Glsymp from '@/views/dagangOilfield/bzgl/sybzk/glsymp'\nimport symbwhDymbnr from '@/views/dagangOilfield/bzgl/sybzk/symbwhDymbnr'\nimport { getPageNoDataList } from \"@/api/dagangOilfield/bzgl/sympk/sympk\";\nimport { getMpmcDataById,saveMwtUdSyMbmp,deleteMwtUdSyMbmp } from \"@/api/dagangOilfield/bzgl/symbwh\";\n\n\nimport htmlToPdf from '@/utils/print/htmlToPdf'\nimport syxm from \"./syxm\";\nimport { getChildsValue, getMouldValue } from '@/api/dagangOilfield/bzgl/sybglr'\n\nexport default {\n  name: 'sybwk',\n  components: {Glsymp, symbwhDymbnr},\n  data() {\n    return {\n      currentUser: this.$store.getters.name,\n      //基本信息表格数据\n      tableData_jbxx: [\n        {\n          column_1: \"试验性质\",\n          column_2: \"试验日期\",\n          column_3: \"试验人员\",\n          column_4: \"试验地点\",\n          column_5: \"\",\n          column_6: \"\",\n          column_7: \"\",\n          column_8: \"\",\n        },\n        {\n          column_1: \"报告日期\",\n          column_2: \"编写人\",\n          column_3: \"审核人\",\n          column_4: \"批准人\",\n          column_5: \"\",\n          column_6: \"\",\n          column_7: \"\",\n          column_8: \"\",\n        },\n        {\n          column_1: \"试验天气\",\n          column_2: \"环境温度（℃）\",\n          column_3: \"环境相对湿度（%）\",\n          column_4: \"投运日期\",\n          column_5: \"\",\n          column_6: \"\",\n          column_7: \"\",\n          column_8: \"\",\n        },\n      ],\n      //设备铭牌表格数据\n      tableData_sbmp:[{\n        'column_1':'额定电压',\n        'column_2':'设备型号',\n        'column_3':'',\n        'column_4':'',\n        'column_5':'',\n        'column_6':'',\n      },],\n      //要循环的试验表格数据\n      arr:[{title:\"\",//试验名称\n        zxmList:[],//子项目数据（表头）\n        bwList:[],//部位数据（第一列开头）\n      }],\n      //下载弹出框控制\n      isShowDownLoadDialog: false,\n      printObj: {\n        id: \"previewId\", // 必填，渲染打印的内容使用\n        popTitle: \"&nbsp;\", //\n        previewTitle: \"&nbsp;\",\n        preview: false,\n      },\n\n      mbInfo: {},\n      //打印内容div中id值\n      previewId: \"\",\n      //定义模板内容弹出框传递参数\n      mbRowData: {},\n      //定义模板内容弹出框\n      isShowXmGlbwDialog: false,\n      xmSelectedForm: {\n        //试验模板id\n        symbid: undefined,\n        //试验项目数据集合\n        xmDataRows: [],\n        isMpSyxm: 1,\n        mpid: undefined,\n      },\n      //项目库弹出框标题\n      xmLibraryAddDialogTitle: '项目库',\n      //项目库弹出框控制\n      isShowAddGlxmDialog: false,\n      //项目库查询参数\n      xmLibraryQueryForm: {\n        symbid: undefined,\n        syxmmc: '',\n        pageNum: 1,\n        pageSize: 10,\n        isMpSyxm: 1,\n        total:0,\n      },\n      //项目库数据\n      xmLibraryDataList: [],\n      //项目库项目总数\n      xmLibraryTotal: 0,\n      //表单验证\n      mbzbRules: {\n        mbmc: [\n          {required: true, message: '请输入模板名称', trigger: 'blur'}\n        ]\n      },\n      // 筛选条件\n      filterInfo: {\n        data: {\n          mbmc: ''\n        },\n        fieldList: [\n          {label: '模板名称', type: 'input', value: 'mbmc', multiple: true}\n        ]\n      },\n      //新增按钮控制\n      addDisabled: true,\n      //树结构懒加载参数\n      props: {\n        label: 'name',\n        children: 'zones',\n        isLeaf: 'leaf'\n      },\n      //删除选择列\n      selectRows: [],\n      //弹出框表单\n      form: {},\n      //查询试验部位参数\n      querySyBwParam: {\n        sblxid: undefined,\n        mbmc: undefined,\n        pageNum: 1,\n        pageSize: 10\n      },\n      //点击树节点赋值\n      treeForm: {},\n      //试验部位列表\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          {label: '模板名称', prop: 'mbmc', minWidth: '100'},\n          {label: '是否默认', prop: 'sfmr', minWidth: '100'},\n          {label: '是否停用', prop: 'sfty', minWidth: '100'},\n          // {\n          //   prop: 'operation',\n          //   label: '操作',\n          //   minWidth: '120px',\n          //   style: {display: 'block'},\n          //   //操作列固定再右侧\n          //   fixed: 'right',\n          //   // operation: [\n          //   //   {name: '修改', clickFun: this.updateDetails},\n          //   //   {name: '详情', clickFun: this.getDetails},\n          //   // ]\n          // }\n        ],\n        option: {checkBox: true, serialNumber: true}\n      },\n      //组织树\n      treeOptions: [],\n\n      isShowDetails: false,\n      title: '',\n\n      //删除是否可用\n      multipleSensor: true,\n      //查询参数\n      queryParams: {\n        bm: undefined,\n        pageNum: 1,\n        pageSize: 10\n      },\n      isDisabled: false,\n      isShow: true,\n\n      //关联项目弹出框title\n      glxmDialogTitle: '关联项目',\n      //关联项目弹出框控制展开\n      isGlxmDialogShow: false,\n\n      //关联项目total\n      glxmTotal: 0,\n      //关联项目查询参数\n      glxmQueryParams: {\n        symbid: undefined,\n        pageNum: 1,\n        pageSize: 10,\n        isMpSyxm:1,\n        total:0,\n      },\n      symbid: undefined,\n\n\n      //关联子项目查询参数\n      glzxmQueryParams: {\n        syxmid: undefined,\n        pageSize: 10,\n        pageNum: 1\n      },\n\n      //模板关联项目数据\n      mbGlxmDataList: [],\n      //新增模板与项目关联选中的数据\n      xmmbSelectedRow: [],\n      //新增数据\n      xmmblist: [],\n      //项目关联的子项目数据\n      zxmGlmbDataList: [],\n      //模板关联项目选中框数据\n      selectedRowDataChange: [],\n      //显示铭牌弹框\n      showMpDialog: false,\n      //选中行数据\n      rowData: {},\n\n      //关联名牌便利\n      mpList: [],\n\n      //试验数据\n      sysjDataList: [],\n      //试验表格默认固定的行\n      defaultRow:[\"仪器型号\",\"结论\",\"备注\"],\n      tdWidth: 0, //一个单元格所占宽度\n    }\n  },\n  watch: {},\n  created() {\n    //获取数据列表\n    this.getData()\n  },\n  mounted() {\n  },\n  methods: {\n    editData(row,column){\n      console.log('111',row,column);\n      row[column.property+\"isShow\"] = true;\n    },\n    elClick(){\n      console.log('this',this,this.innerHTML);\n    },\n    //试验数据表格合并方法\n    arraySpanMethod({ row, column, rowIndex, columnIndex }) {\n      if(this.defaultRow.includes(row.SYBW)){\n        if (columnIndex > 0) {\n          return [1,row.totalNum]\n        }\n      }\n    },\n    //设备铭牌表格合并方法\n    sbmpSpanMethod({ row, column, rowIndex, columnIndex }){\n      if (columnIndex > 3) {\n        return [1,2]\n      }\n    },\n    //模板详情按钮\n    handleMbInfo(row) {\n      console.log(\"sdfsd--s\",row);\n      //获取当前模板id加载页面信息\n      // this.getMbGlMpinfoData(row);\n      //打开弹出框\n      this.isShowDownLoadDialog = true;\n      //获取试验数据\n      this.getMbGlXmAndBw(row);\n    },\n    //测试\n    outputPdfFun1(){\n      let that = this;\n      let heights = 0;//最终累计高度\n      const width = 592.28;\n      const height = 1400;\n      that.$nextTick(()=>{\n        debugger\n        let target = document.getElementById('printContentId');\n        let pageHeight = target.scrollWidth / width * height;\n        let childs = document.getElementById('printContentId').childNodes;  //表格的第一级子内容\n        for(let i=0;i<childs.length;i++){\n          that.loopFun(childs[i],heights)\n          // console.log('childs',childs[i]);\n          let multiple = Math.ceil((childs[i].offsetTop + childs[i].offsetHeight)/pageHeight);\n          let childsHeight = childs[i].offsetHeight;\n          heights += childsHeight;\n        }\n        console.log('zi',document.getElementById('printContentId').childNodes)\n        //this.downloadPdf();\n      })\n    },\n    //\n    outputPdfFun(){\n      let that = this;\n      let heights = 0;//最终累计高度\n      const width = 592.28;\n      const height = 1400;\n      that.$nextTick(()=>{\n        // debugger\n        let target = document.getElementById('printContentId');\n        let pageHeight = target.scrollWidth / width * height;\n        let childs = document.getElementById('printContentId').childNodes;  //表格的第一级子内容\n        for(let i=0;i<childs.length;i++){\n          let childsHeight = childs[i].offsetHeight;\n          heights += childsHeight;\n          //判断每一个子元素的高度是否超出一页pdf的高度,没超过就累加\n          if(heights >= height){\n            that.loopFun(childs[i],heights)\n          }\n        }\n        //this.downloadPdf();\n      })\n    },\n    //测试方法\n    loopFun(val,heights){\n      console.log('打印',val,heights);\n      //  let heights = 0;//最终累计高度\n      const width = 592.28;\n      const height = 1488;\n      // let childsHeight = val.offsetHeight;\n      // heights += childsHeight;\n      //console.log('高度',heights)\n      //判断每一个子元素的高度是否超出一页pdf的高度,没超过就累加\n      if(heights >= height){\n        //先减去超出页面的div高度\n        heights = heights - val.offsetHeight;\n        // console.log('hei1',val.childNodes)\n        //在超过的子元素进行循环判断，查找在那一块超过\n        val.childNodes.forEach(item=> {\n          heights += item.offsetHeight;\n          if(heights >= height){\n            //先减去超出页面的div高度\n            heights = heights - item.offsetHeight;\n            console.log('item',item,heights,item.childNodes)\n            item.childNodes.forEach(value=> {\n              heights += value.offsetHeight;\n              if(heights >= height){\n\n                console.log('item11',value)\n                //获取父节点\n                let childParent = value.parentNode;\n                let next = value.nextSibling;\n                //创建空白标签\n                let newNode = document.createElement('span');\n                newNode.style.background = '#fff';\n                newNode.style.height = '100px';\n                newNode.style.width = '1030px';\n                newNode.style.display = 'block';\n                newNode.style.borderTop = '1px solid #000';\n                // console.log('4444',val3,next,newNode,childParent);\n                //console.log('xiajiedian',val3.nextSibling)\n                if(next){\n                  childParent.insertBefore(newNode,next)\n                }else{\n                  childParent.appentChild(newNode);\n                }\n                this.downloadPdf()\n              }\n            })\n\n            //this.loopFun(item,heights)\n          }\n          // this.loopFun(item,heights)\n        })\n      }\n    },\n    //导出pdf操作\n    downloadPdf() {\n      htmlToPdf.downloadPDF(document.querySelector('#printContentId'), this.mbInfo.mbmc)\n    },\n    //获取当前模板id加载页面信息\n    getMbGlMpinfoData(param) {\n      getMbGlMpinfoData(param).then(res => {\n        this.mpList = res.data;\n        //调用渲染铭牌页面开始\n        // this.applyMpHtml(this.mpList, param);\n        //打开弹出框\n        this.isShowDownLoadDialog = true;\n      })\n\n    },\n    //获取试验数据\n    getMbGlXmAndBw(rowData) {\n      //每次获取数据前先清空，再添加，否则多次进入页面时会获取重复数据\n      this.sysjDataList = [];\n      //获取设备铭牌数据\n      //在填写报告时，请求后台处理数据\n      const that = this;\n      getMouldValue({symbid:rowData.objId}).then(res=>{\n        res = res.data;\n        let key = 'symbid';\n        rowData[key] = rowData.objId;\n        getMwtUdSyMpxqByMbzb({symbid:rowData.objId}).then(result=>{\n          let sbmp_data = result.data.sbmp;\n          let sysj_data = result.data.syxm;\n          let arr = [];\n          Object.keys(sbmp_data).sort().forEach(function(key){\n            arr.push({[key]: sbmp_data[key]})\n          })\n          let arr1 = [];\n          Object.keys(sysj_data).sort().forEach(function(key){\n            arr1.push({[key]: sysj_data[key]})\n          })\n          that.handleSbmp(res[Object.keys(res)[0]],arr,'','h2_table');\n          that.handleSbmp(res[Object.keys(res)[1]],arr1,'','h3_table');\n        })\n      })\n    },\n    handleSbmp(dataNum,dataArr,str,tableBox){\n      for(let k = 0;k<dataNum.length;k++){\n        var hs = dataNum[k].aHs;\n        var ls = dataNum[k].aLs;\n        this.tdWidth = 100 / Number(ls);\n        let data = dataArr[k];\n        for(var item in data) {\n          if(dataArr.length > 1){   //判断若该模块只有一个子内容则不显示title\n            str += \"<tr style='text-align:left;'><th colspan=\"+ ls +\">\"+ item +\"</th></tr>\";\n          }\n          for (let i = 0; i < hs; i++) {//有几行就插入几行\n            let temp = \"<tr>\"\n            for (let j = 0; j < data[item].length; j++) {   //循环数据看每行有几列\n              if (i == data[item][j].rowindex) {\n                var nrbs = data[item][j].nrbs;\n                var sjlx = data[item][j].sjlx;  //数据类型\n                var objId = data[item][j].objId;\n                var nr = '';\n                if(nrbs == null){\n                  nrbs = \"\";\n                }\n                nr = nrbs;\n                if (data[item][j].colspan != '1') {    //判断colspan不为1的话为可编辑的\n                  temp += \"<td tabindex='-1' colspan='\"\n                    + data[item][j].colspan\n                    + \"' rowspan='\"\n                    + data[item][j].rowspan\n                    + \"' style='width: \" +\n                    this.tdWidth * data[item][j].colspan +\n                    \"px'>\"\n                    + nr + \"</td>\";\n                } else {\n                  temp += \"<td tabindex='-1' colspan='\"\n                    + data[item][j].colspan\n                    + \"' rowspan='\"\n                    + data[item][j].rowspan\n                    + \"' style='min-width:100px;width: \" +\n                    this.tdWidth * data[item][j].colspan +\n                    \"%'>\"\n                    + nr + \"</td>\";\n                }\n              }\n            }\n            temp += \"</tr>\"\n            str += temp;\n          }\n        }\n      }\n      //渲染页面\n      this.$nextTick(() => {\n        var tableBox1 = document.getElementById(tableBox);\n        tableBox1.innerHTML = '';\n        tableBox1.innerHTML = str;\n      })\n    },\n    //解析后台试验数据\n    analysisSyData(syxmmc, zxmAndBwData) {\n      let sysjData = {}\n      sysjData.syxmmc = syxmmc;\n      for (let key in zxmAndBwData[0]) {\n        sysjData[key] = zxmAndBwData[0][key]\n      }\n      this.sysjDataList.push(sysjData);\n    },\n    //渲染实验数据到页面\n    applySysjDataToHtml() {\n      this.arr = [];\n      // $('#sysjTableId').html(\"\");\n      //进行数据处理重组\n      let data = this.sysjDataList;\n      if (data.length > 0) {\n        for (let i = 0; i < data.length; i++) {\n          let dataChild = data[i];\n          let title = dataChild.syxmmc;//试验项目名称\n          let bwList = dataChild.bwList; //部位list\n          let zxmList = dataChild.zxmList; //子项目list\n          let hx = [\n            {\n              \"label\":title, //第一个表头为试验项目名称\n              \"column_name\":\"SYBW\", //第一列对应的字段名（试验部位）\n            },\n          ];\n          zxmList.forEach(zxm=>{\n            hx.push({\n              \"label\":zxm.syzxmmc, //每列的表头\n              \"column_name\":\"\", //每列对应的数值暂时设置为空白\n            })\n          })\n          let sx = [];\n          bwList.forEach(bw=>{\n            sx.push({\n              \"SYBW\":bw.SYBW,\n              \"totalNum\":zxmList.length//记录总列数，用于固定行的合并事件\n            })\n          })\n          //后四行固定\n          sx.push(\n            {\n              \"SYBW\":\"结果\",\n              \"totalNum\":zxmList.length//记录总列数，用于固定行的合并事件\n            },\n            {\n              \"SYBW\":\"仪器型号\",\n              \"totalNum\":zxmList.length//记录总列数，用于固定行的合并事件\n            },\n            {\n              \"SYBW\":\"结论\",\n              \"totalNum\":zxmList.length//记录总列数，用于固定行的合并事件\n            },\n            {\n              \"SYBW\":\"备注\",\n              \"totalNum\":zxmList.length//记录总列数，用于固定行的合并事件\n            }\n          )\n          this.arr.push({\n            title:title,\n            zxmList:hx,\n            bwList:sx,\n          });\n        }\n      }\n      //拼接的铭牌表格\n      /*     let str = \"\";\n           if (this.sysjDataList.length > 0) {\n             for (let i = 0; i < this.sysjDataList.length; i++) {\n               //拼接项目序号\n               let xmIndex = i + 1;\n               str += \"<tr><td colspan='5' style='text-align: left;font-weight: bold;font-size: 15px'>\" + xmIndex + \"、\" + this.sysjDataList[i].syxmmc + \"</td></tr>\";\n               // this.sysjDataList[i].bwList;\n               // this.sysjDataList[i].zxmList;\n               // str += \"<tr><td>\"+this.sysjDataList[i].syxmmc+\"</td><td v-for=item in this.sysjDataList[i].bwList></td></tr>\"\n\n             }\n             this.$nextTick(() => {\n               $('#sysjTableId').append(str)\n             })\n           }*/\n    },\n    //渲染铭牌页面开始mpList:反回的铭牌列表  row：模板行对象\n    applyMpHtml(mpList, row) {\n      //每次打开需要重新渲染一次,先将置空\n      $('#sbmpTbodyId').html(\"\");\n      //清空重新赋值\n      this.mbInfo = {}\n      this.mbInfo.mbmc = row.mbmc;\n      //拼接的铭牌表格\n      let str = \"\";\n      //先判断是否分相铭牌\n      if (mpList.length > 0) {\n        if (mpList[0].SFFX == '1') { //当前铭牌为分相铭牌时\n          //写死第一行\n          str += \"<tr><td style='padding: 10px;font-size: 15px;'>相别</td>\" +\n            \"<td style='padding: 10px;font-size: 15px;'>A</td>\" +\n            \"<td style='padding: 10px;font-size: 15px;'>B</td>\" +\n            \"<td style='padding: 10px;font-size: 15px;'>C</td>\" +\n            \"</tr>\";\n          //开始遍历展示\n          for (let a = 0; a < mpList.length; a++) {\n            str += \"<tr>\"\n            str += \"<td style='padding: 10px;font-size: 15px;'>\";\n            str += mpList[a].title + \"</td>\";\n            str += \"<td></td>>\"\n            str += \"<td></td>>\"\n            str += \"<td></td>>\"\n            str += \"</tr>\"\n          }\n        } else {  //铭牌不分相\n          //当前铭牌不属于分相铭牌\n          //每列展示单元格数量\n          let col = 3;\n          //展示行数\n          var lines = Math.ceil(mpList.length / col);\n          //遍历展示行数\n          for (var i = 0; i < lines; i++) {\n            str += \"<tr>\";\n            //遍历列\n            for (var j = 0; j < col; j++) {\n              if (i * col + j < mpList.length) {\n                str += \"<td style='padding: 10px;font-size: 15px;'>\";\n                //铭牌标题赋值\n                str += mpList[i * col + j].title + \"</td>\";\n                //铭牌值赋值\n                str += mpList[i * col + j].sfmb == 1 ? \"<td> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>\" : \"<td>\" + mpList[i * col + j].column_name + \"</td>\"\n              }\n            }\n            str += \"</tr>\";\n          }\n        }\n      }\n      //渲染铭牌页面\n      this.$nextTick(() => {\n        $('#sbmpTbodyId').append(str)\n      })\n      //打开弹出框\n      this.isShowDownLoadDialog = true;\n    },\n    //关闭预览弹出框\n    closeYlDialog() {\n      //清空表单\n      this.mbInfo = {};\n      //赋值完关闭弹窗\n      this.isShowDownLoadDialog = false;\n    }\n    ,\n    //定义模板内容\n    handleClickMbnr(row) {\n      //打开组件弹出框\n      this.isShowXmGlbwDialog = true;\n      //给子组件传递数据\n      this.mbRowData = row;\n    },\n    //获取项目库项目数据\n    getXmLiraryData() {\n      getPageNoDataList(this.xmLibraryQueryForm).then(res => {\n        this.xmLibraryDataList = res.data.records\n        this.xmLibraryQueryForm.total=res.data.total;\n        this.xmLibraryTotal = res.data.total\n      });\n    },\n    //项目弹出框新增按钮\n    addMbGlXm() {\n      this.getXmLiraryData()\n      this.isShowAddGlxmDialog = true\n    }\n    ,\n    //项目库弹出框取消按钮\n    closeAddMjzDialog() {\n      this.isShowAddGlxmDialog = false;\n      this.xmLibraryQueryForm.mpmc = undefined;\n    }\n    ,\n    //项目库弹窗确认按钮\n    commitAddMjzForm() {\n      this.xmmblist=[];\n      if (this.xmSelectedForm.xmDataRows.length < 1) {\n        this.$message.info('未关联项目！！！已取消')\n        //如果未选中数据,则直接关闭弹窗\n        this.isShowAddGlxmDialog = false\n      } else {\n        for (let i = 0; i < this.xmmbSelectedRow.length; i++) {\n          this.xmSelectedForm = {};\n          this.xmSelectedForm.mpid = this.xmmbSelectedRow[i].objId;\n          this.xmSelectedForm.symbid = this.symbid;\n          this.xmSelectedForm.isMpSyxm = 1;\n          this.xmmblist.push(this.xmSelectedForm);\n        }\n        //若选择数据后\n        saveMwtUdSyMbmp(this.xmmblist).then(res => {\n          if (res.code === '0000') {\n            this.$message.success('关联成功')\n          } else {\n            this.$message.error('关联失败！！')\n          }\n          //关闭弹窗\n          this.isShowAddGlxmDialog = false\n          //调用获取关联子项目列表\n          this.getSymbGlsyxmDataListByPage()\n        })\n      }\n    }\n    ,\n    //项目库行选中事件\n    handleSelectedXmLibraryChange(rows) {\n      this.xmSelectedForm.xmDataRows = rows\n      this.xmmbSelectedRow=[];\n      this.xmmbSelectedRow=rows;\n      console.log(\"sdfys--\",this.xmSelectedForm.xmDataRows);\n    }\n    ,\n    //项目库查询按钮\n    selectxmLibrary() {\n      this.getXmLiraryData()\n    }\n    ,\n    //项目库重置按钮\n    resetxmSearch() {\n      this.xmLibraryQueryForm.syxmmc = ''\n      this.getXmLiraryData()\n    }\n    ,\n    //获取关联子列表方法\n    getZxmDataList() {\n      getGlSyzxmDataListByPage(this.glzxmQueryParams).then(res => {\n        this.glzxmTotal = res.data.total\n        this.zxmGlmbDataList = res.data.records\n      })\n    }\n    ,\n\n    //关联项目\n    handleClickGlxm(row) {\n      //清空原来子项目数据\n      this.mbGlxmDataList=[];\n      this.zxmGlmbDataList = [];\n      this.xmmbSelectedRow=[];\n      //打开关联项目弹出框\n      this.isGlxmDialogShow = true\n      //给参数赋值\n      this.glxmQueryParams.symbid = row.objId;\n      this.symbid = row.objId;\n      //查询项目库数据时参数\n      this.xmLibraryQueryForm.symbid = row.objId\n      //给试验项目库添加时使用\n      this.xmSelectedForm.symbid = row.objId\n      //获取模板关联项目数据\n      this.getSymbGlsyxmDataListByPage()\n    }\n    ,\n    //获取关联项目弹出框数据\n    getSymbGlsyxmDataListByPage() {\n      getMpmcDataById(this.glxmQueryParams).then(res => {\n        this.mbGlxmDataList = res.data.records\n        this.glxmTotal = res.data.total\n        this.glxmQueryParams.total=res.data.total\n      });\n    },\n    //试验项目复选框点击时间点击操作\n    handleGlxmSelectedChange(rows) {\n      this.selectedRowDataChange = rows\n    }\n    ,\n    //删除模板关联项目\n    deleteMbGlXm() {\n      if (this.selectedRowDataChange.length < 1) {\n        this.$message.warning('请选择正确的数据！！！')\n        return;\n      }\n      let ids = this.selectedRowDataChange.map(item => {\n        return item.mbmpId;\n      });\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        deleteMwtUdSyMbmp(ids).then(({code}) => {\n          if (code === '0000') {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            });\n            this.getSymbGlsyxmDataListByPage()\n          } else {\n            this.$message({\n              type: 'error',\n              message: '删除失败!'\n            })\n            this.getSymbGlsyxmDataListByPage()\n          }\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        })\n      })\n    },\n    //试验项目点击行数据时的单机操作\n    handleMbGlxmRowClick(row) {\n      this.glzxmQueryParams.syxmid = row.syxmid\n      this.getZxmDataList()\n    },\n    //懒加载函数\n    loadNode(node, resolve) {\n      let TreeparamMap = {\n        pid: '',\n        spbLogo: ['输电设备', '变电设备','配电设备']\n      }\n      if (node.level === 0) {\n        TreeparamMap.pid = 'sb'\n        return this.getTreeNode(TreeparamMap, resolve)\n      }\n      setTimeout(() => {\n        TreeparamMap.pid = node.data.code\n        this.getTreeNode(TreeparamMap, resolve)\n      }, 500)\n    },\n    //获取树节点数据\n    getTreeNode(paramMap, resolve) {\n      getDeviceClassTreeNodeByPid(paramMap).then(res => {\n        let treeNodes = []\n        res.data.forEach(item => {\n          let node = {\n            name: item.name,\n            level: item.level,\n            id: item.id,\n            pid: item.pid,\n            leaf: false,\n            code: item.code\n          }\n          treeNodes.push(node)\n        })\n        resolve(treeNodes)\n      })\n    },\n    //添加后确认保存按钮\n    save() {\n      this.$refs['form'].validate((valid) => {\n        if (valid) {\n          saveOrUpdate(this.form).then(res => {\n            if (res.code === '0000') {\n              this.$message.success(res.msg)\n              this.tableAndPageInfo.pager.pageResize = 'Y'\n              this.getData()\n              this.isShowDetails = false\n            } else {\n              this.$message.error(res.msg)\n            }\n          });\n\n        }\n      })\n    },\n    //树节点点击事件\n    handleNodeClick(data) {\n      console.log(data)\n      if (data.level != '0') {\n        //新增按钮可点击\n        this.addDisabled = false\n        this.treeForm = data\n        this.querySyBwParam.sblxid = data.code\n        this.getData()\n      } else {\n        this.addDisabled = true\n      }\n    },\n    //添加按钮\n    addSensorButton() {\n      this.form = {}\n      this.form.sblx = this.treeForm.name;\n      this.form.sblxid = this.treeForm.code;\n      this.isShowDetails = true;\n      this.title = '新增';\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.querySyBwParam = {...this.querySyBwParam, ...params}\n        const param = this.querySyBwParam\n        const {data, code} = await getPageDataListTosymb(param)\n        if (code === '0000') {\n          this.tableAndPageInfo.tableData = data.records\n          this.tableAndPageInfo.pager.total = data.total\n        }\n      } catch (e) {\n        console.log(e)\n      }\n    }\n    ,\n    /**\n     * 表格多选框\n     */\n    handleSelectionChange(rows) {\n      this.selectRows = rows\n    }\n    ,\n\n    close() {\n      this.isShowDetails = false\n    }\n    ,\n    //修改模板主表内容\n    updateDetails(row) {\n      this.title = '修改';\n      this.isShowDetails = true;\n      this.form = row;\n      this.isDisabled = false;\n      this.isShow = true;\n    },\n\n    createTemplate(row) {\n      console.log(row)\n    }\n    ,\n    //查看模板主表详情按钮\n    getDetails(row) {\n      this.title = '详情'\n      this.isShowDetails = true;\n      this.form = row;\n      this.isDisabled = true;\n      this.isShow = false;\n    },\n\n    //删除按钮\n    // deleteSensorButton(objId) {\n    //   debugger;\n    //   console.log(\"123+objId\",objId);\n    //   this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n    //     confirmButtonText: \"确定\",\n    //     cancelButtonText: \"取消\",\n    //     type: \"warning\",\n    //   })\n    //    .then(() => {\n    //     removeSymb(JSON.stringify(objId)).then(({code}) => {\n    //       if (code === '0000') {\n    //         this.$message({\n    //           type: 'success',\n    //           message: '删除成功!'\n    //         })\n    //         this.tableAndPageInfo.pager.pageResize = 'Y'\n    //         this.getData()\n    //       } else {\n    //         this.$message({\n    //           type: 'error',\n    //           message: '删除失败!'\n    //         })\n    //         this.tableAndPageInfo.pager.pageResize = 'Y'\n    //         this.getData()\n    //       }\n    //     })\n    //   }).catch(() => {\n    //     this.$message({\n    //       type: 'info',\n    //       message: '已取消删除'\n    //     })\n    //   })\n\n    // },\n\n     deleteSensorButton(id) {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      })\n        .then(() => {\n          removeSymb(JSON.stringify(id)).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\",\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\",\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\",\n          });\n        });\n      this.tableAndPageInfo.pager.pageResize = \"Y\";\n      this.getData();\n    },\n\n    //导出按钮\n    handleExport() {\n\n    }\n    ,\n\n    //关联铭牌点击事件\n    handleClickGlMp(row) {\n      this.showMpDialog = true\n      this.rowData = row\n    }\n    ,\n    //关闭试验铭牌弹窗\n    closeMpDialog() {\n      this.showMpDialog = false\n    }\n    ,\n\n    filterReset() {\n      this.querySyBwParam = {\n        sblxid: undefined,\n        mbmc: undefined,\n        pageNum: 1,\n        pageSize: 10\n      }\n    },\n    //清空表单数据\n    handleClose(){\n      this.form={};\n      this.$nextTick(() => {\n        this.form = this.$options.data().form;\n        this.resetForm(\"form\");\n      });\n    },\n  }\n}\n</script>\n\n<style lang='scss' scoped>\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n/deep/.el-table td div{\n  white-space: pre-wrap;\n}\n//设备铭牌\n#h2_table,#h3_table{\n  text-align: center;\n}\n/deep/ #h2_table tr,/deep/ #h3_table tr{\n  height: 35px;\n}\n/deep/ #h2_table input,/deep/ #h3_table input{\n  display: inline-block;\n  height: 35px;\n}\n/*导出pdf格式设置开始*/\n#printContentId {\n  background-color: #fff;\n  width: 100%;\n  /* height: 400px; */\n  margin: auto;\n  padding: 14px 30px;\n  box-sizing: border-box;\n  //试验数据样式\n  .printTitle{\n    text-align: left;\n    line-height: 40px;\n    border-left: 1px solid #000;\n    border-right: 1px solid #000;\n    //border-bottom: 1px solid #000;\n    padding-left: 10px;\n  }\n}\n//修改table表头颜色\n/deep/  #printContentId .el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th{\n  background: #fff;\n  border-color: #000;\n  font-weight: inherit;\n}\n/deep/  #printContentId .el-table--enable-row-transition .el-table__body td{\n  border-color: #000;\n}\n\n.table_style td, th {\n  padding: 10px;\n  font-size: 15px;\n}\n\n.table_style {\n  border-collapse: collapse;\n  width: 100%;\n  text-align: center;\n}\n\n/* 设置滚动条的样式 */\n::-webkit-scrollbar {\n  width: 12px;\n}\n\n/* 滚动槽 */\n::-webkit-scrollbar-track {\n  //-webkit-box-shadow:inset006pxrgba(0,0,0,0.3);\n  border-radius: 10px;\n}\n\n/* 滚动条滑块 */\n::-webkit-scrollbar-thumb {\n  border-radius: 10px;\n  background: rgba(0, 0, 0, 0.1);\n  //-webkit-box-shadow:gba(0,0,0,0.5);\n}\n\n::-webkit-scrollbar-thumb:window-inactive {\n  background: rgba(0, 0, 0, 0.1);\n}\n\n[data-v-67a974b1]::-webkit-scrollbar {\n  width: 8px;\n}\n\n.item {\n  width: 225px;\n  float: left;\n}\n\n/*导出pdf格式设置结束*/\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  max-height: 79vh;\n  overflow: auto;\n}\n</style>\n<style lang=\"scss\">\n/*新加修改导出pdf页面样式*/\n.outPut .el-dialog__body{\n  padding:25px !important;\n  /* background: #e79b9b;*/\n  .el-button{\n    // margin-bottom: 15px;\n  }\n}\n</style>\n"]}]}