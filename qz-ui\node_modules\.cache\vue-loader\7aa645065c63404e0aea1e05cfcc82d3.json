{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\blgk\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\blgk\\index.vue", "mtime": 1750579918247}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgcmVtb3ZlQmxnaywKICBzYXZlT3JVcGRhdGUsCiAgZ2V0RGF0YSwKICBnZXRBbGxTYmx4TGlzdCwKICBnZXRCbGdrU2JseEJ5SmcsCiAgZ2V0U2IsCiAgY2xlYW4KfSBmcm9tICJAL2FwaS9ibGdrL2JsZ2siOwppbXBvcnQgeyBnZXRMeCwgZ2V0TXMsIGdldEZseWogfSBmcm9tICJAL2FwaS9ibGdrL2JsZ2tiemsiOwppbXBvcnQgeyBnZXREZXB0TGlzdEJ5SWQgfSBmcm9tICJAL2FwaS9zeXN0ZW0vZGVwdCI7CmltcG9ydCB7IGdldEJkelNlbGVjdExpc3QgfSBmcm9tICJAL2FwaS95eGdsL2JkeXhnbC9iZHhqenFweiI7CmltcG9ydCB7IGdldEpnRGF0YUxpc3RTZWxlY3RlZCB9IGZyb20gIkAvYXBpL2RhZ2FuZ09pbGZpZWxkL2Fzc2V0L2Jkc2J0eiI7CmltcG9ydCB7IGZvcm1hdHRlckRhdGVUaW1lIH0gZnJvbSAiQC91dGlscy9oYW5kbGVEYXRhIjsKaW1wb3J0IHsgTG9hZGluZyB9IGZyb20gImVsZW1lbnQtdWkiOwppbXBvcnQgeyBnZXREaWN0VHlwZURhdGEgfSBmcm9tICJAL2FwaS9zeXN0ZW0vZGljdC9kYXRhIjsKaW1wb3J0IHNiQ2hvb3NlIGZyb20gIkAvdmlld3MvZGFnYW5nT2lsZmllbGQvYmxnay9jaG9vc2VCZHNiIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiYmxnayIsCiAgY29tcG9uZW50czogeyBzYkNob29zZSB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBoYXNTdXBlclJvbGU6IHRoaXMuJHN0b3JlLmdldHRlcnMuaGFzU3VwZXJSb2xlLAogICAgICBpc1Nob3c6IGZhbHNlLCAvL+W8ueahhuaYr+WQpuaYvuekugogICAgICBsb2FkaW5nOiBmYWxzZSwKICAgICAgLy/mmK/lkKbnpoHnlKgKICAgICAgaXNEaXNhYmxlZDogZmFsc2UsCiAgICAgIGZvcm06IHt9LAogICAgICAvL+afpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBwYWdlTnVtOiAxCiAgICAgIH0sCiAgICAgIC8v6K+m5oOF5a+56K+d5qGG5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgZmlsdGVySW5mbzogewogICAgICAgIGRhdGE6IHsKICAgICAgICAgIC8vIGZnc21jOiAiIiwKICAgICAgICAgIGJkem1jOiAiIiwKICAgICAgICAgIGpnbWM6ICIiLAogICAgICAgICAgc2JtYzogIiIsCiAgICAgICAgICBzYmx4OiAiIiwKICAgICAgICAgIGlzQ2xlYW46ICIiCiAgICAgICAgfSwKICAgICAgICBmaWVsZExpc3Q6IFsKICAgICAgICAgIC8vIHsgbGFiZWw6ICLliIblhazlj7giLCB0eXBlOiAic2VsZWN0IiwgdmFsdWU6ICJmZ3NtYyIsIG9wdGlvbnM6IFtdIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5Y+Y55S156uZIiwgdHlwZTogImlucHV0IiwgdmFsdWU6ICJiZHptYyIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLpl7TpmpTlkI3np7AiLCB0eXBlOiAiaW5wdXQiLCB2YWx1ZTogImpnbWMiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi6K6+5aSH5ZCN56ewIiwgdHlwZTogImlucHV0IiwgdmFsdWU6ICJzYm1jIiB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIuiuvuWkh+exu+WeiyIsCiAgICAgICAgICAgIHR5cGU6ICJzZWxlY3QiLAogICAgICAgICAgICB2YWx1ZTogInNibHgiLAogICAgICAgICAgICBvcHRpb25zOiBbXSwKICAgICAgICAgICAgY2xlYXJhYmxlOiB0cnVlLAogICAgICAgICAgICBmaWx0ZXJhYmxlOiB0cnVlCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIuaYr+WQpua2iOmZpCIsCiAgICAgICAgICAgIHR5cGU6ICJzZWxlY3QiLAogICAgICAgICAgICB2YWx1ZTogImlzQ2xlYW4iLAogICAgICAgICAgICBvcHRpb25zOiBbXSwKICAgICAgICAgICAgY2xlYXJhYmxlOiB0cnVlCiAgICAgICAgICB9CiAgICAgICAgXQogICAgICB9LAogICAgICB0YWJsZUFuZFBhZ2VJbmZvOiB7CiAgICAgICAgcGFnZXI6IHsKICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgIHNpemVzOiBbMTAsIDIwLCA1MCwgMTAwXQogICAgICAgIH0sCiAgICAgICAgb3B0aW9uOiB7IGNoZWNrQm94OiBmYWxzZSwgc2VyaWFsTnVtYmVyOiB0cnVlIH0sCiAgICAgICAgdGFibGVEYXRhOiBbXSwKICAgICAgICB0YWJsZUhlYWRlcjogWwogICAgICAgICAgeyBwcm9wOiAiZmdzbWMiLCBsYWJlbDogIuWIhuWFrOWPuCIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBwcm9wOiAiYmR6bWMiLCBsYWJlbDogIuWPmOeUteermSIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBwcm9wOiAiamdtYyIsIGxhYmVsOiAi6Ze06ZqU5ZCN56ewIiwgbWluV2lkdGg6ICIxMDAiIH0sCiAgICAgICAgICB7IHByb3A6ICJzYmx4bWMiLCBsYWJlbDogIuiuvuWkh+exu+WeiyIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBwcm9wOiAic2JtYyIsIGxhYmVsOiAi6K6+5aSH5ZCN56ewIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJtcyIsIGxhYmVsOiAi5LiN6Imv5bel5Ya15o+P6L+wIiwgbWluV2lkdGg6ICIxNDAiLCBzaG93UG9wOiB0cnVlIH0sCiAgICAgICAgICB7IHByb3A6ICJmbHlqQ24iLCBsYWJlbDogIuWIhuexu+S+neaNriIsIG1pbldpZHRoOiAiMTQwIiwgc2hvd1BvcDogdHJ1ZSB9LAogICAgICAgICAgeyBwcm9wOiAibHJybWMiLCBsYWJlbDogIuW9leWFpeS6uiIsIG1pbldpZHRoOiAiODAiIH0sCiAgICAgICAgICB7IHByb3A6ICJmeHNqIiwgbGFiZWw6ICLlj5HnjrDml7bpl7QiLCBtaW5XaWR0aDogIjExMCIgfSwKICAgICAgICAgIHsgcHJvcDogInhjc2oiLCBsYWJlbDogIua2iOmZpOaXtumXtCIsIG1pbldpZHRoOiAiMTEwIiB9CiAgICAgICAgXQogICAgICB9LAogICAgICBydWxlczogewogICAgICAgIGZnc21jOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWIhuWFrOWPuOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9XSwKICAgICAgICBiZHptYzogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlj5jnlLXnq5nkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfV0sCiAgICAgICAgamdtYzogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLpl7TpmpTkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfV0sCiAgICAgICAgc2JseG1jOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K6+5aSH57G75Z6L5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIHR5cGU6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuI3oia/lt6XlhrXnsbvlnovkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAic2VsZWN0IiB9CiAgICAgICAgXSwKICAgICAgICBzYm1jOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuiuvuWkh+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9XSwKICAgICAgICBtczogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuS4jeiJr+W3peWGteS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJzZWxlY3QiIH0KICAgICAgICBdLAogICAgICAgIGZseWo6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLliIbnsbvkvp3mja7kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAic2VsZWN0IiB9CiAgICAgICAgXQogICAgICB9LAogICAgICBmZ3NMaXN0OiBbXSwgLy/liIblhazlj7jkuIvmi4nmoYYKICAgICAgYmR6TGlzdDogW10sIC8v5Y+Y55S156uZ5LiL5ouJ5qGGCiAgICAgIGpnTGlzdDogW10sIC8v6Ze06ZqU5LiL5ouJ5qGGCiAgICAgIHNibHhMaXN0OiBbXSwgLy/pl7TpmpTkuIvmi4nmoYYKICAgICAgc2JMaXN0OiBbXSwgLy/orr7lpIfkuIvmi4nmoYYKICAgICAgbXNMaXN0OiBbXSwgLy/mj4/ov7DkuIvmi4nmoYYKICAgICAgZmx5akxpc3Q6IFtdLCAvL+WIhuexu+S+neaNruS4i+aLieahhgogICAgICBseExpc3Q6IFtdLCAvL+exu+Wei+S4i+aLieahhgogICAgICBzYXZlTG9hZGluZzogbnVsbCwgLy/kv53lrZjml7bnmoTpga7nvanlsYIKICAgICAgaXNTaG93U2JDaG9vc2U6IGZhbHNlLCAvL+aYr+WQpuaYvuekuuiuvuWkh+mAieaLqeW8ueahhgogICAgICBzYkRhdGE6IHt9IC8v6K6+5aSH5pWw5o2uCiAgICB9OwogIH0sCiAgY3JlYXRlZCgpIHsKICAgIC8v5YiX6KGo5p+l6K+iCiAgICB0aGlzLmdldERhdGEoKTsKICAgIHRoaXMuZ2V0T3B0aW9ucygpOyAvL+iOt+WPlua2ieWPiuWIsOeahOS4i+aLieahhuWtl+WFuOWAvAogIH0sCiAgbWV0aG9kczogewogICAgLy/pgInmi6norr7lpIcKICAgIGNob29zZVNiRnVuKCkgewogICAgICB0aGlzLmlzU2hvd1NiQ2hvb3NlID0gdHJ1ZTsKICAgIH0sCiAgICAvL+WFs+mXreiuvuWkh+mAieaLqeW8ueahhgogICAgY2xvc2VDaG9vc2VGdW4oKSB7CiAgICAgIHRoaXMuaXNTaG93U2JDaG9vc2UgPSBmYWxzZTsKICAgIH0sCiAgICAvL+iOt+WPluiuvuWkh+aVsOaNrgogICAgYXN5bmMgZ2V0U2JEYXRhRnVuKHNiRGF0YSkgewogICAgICB0aGlzLnNiRGF0YSA9IHNiRGF0YTsKICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgImZnc21jIiwgc2JEYXRhLmRlcHRuYW1lKTsKICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgImJkem1jIiwgc2JEYXRhLmJkem1jKTsKICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgImpnbWMiLCBzYkRhdGEud3ptYyk7CiAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICJzYmx4bWMiLCBzYkRhdGEuc2JseG1jKTsKICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgInNibHgiLCBzYkRhdGEuc2JseGJtKTsKICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgInNibWMiLCBzYkRhdGEuc2JtYyk7CiAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICJzYmlkIiwgc2JEYXRhLm9iaklkKTsKICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgInR5cGUiLCAiIik7CiAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICJtcyIsICIiKTsKICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgImZseWoiLCAiIik7CiAgICAgIC8v6I635Y+W5LiN6Imv5bel5Ya157G75Z6L5LiL5ouJ5qGGCiAgICAgIHRoaXMubHhMaXN0ID0gW107CiAgICAgIGF3YWl0IGdldEx4KHsgc2JseDogc2JEYXRhLnNibHhibSB9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgcmVzLmRhdGEuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgIHRoaXMubHhMaXN0LnB1c2goeyBsYWJlbDogaXRlbS5sYWJlbCwgdmFsdWU6IGl0ZW0ubGFiZWwgfSk7CiAgICAgICAgfSk7CiAgICAgICAgdGhpcy5seExpc3QucHVzaCh7IGxhYmVsOiAi5YW25LuWIiwgdmFsdWU6ICLlhbbku5YiIH0pOwogICAgICB9KTsKICAgIH0sCiAgICAvL+afpeivouS4i+aLieahhuaVsOaNrgogICAgYXN5bmMgZ2V0T3B0aW9ucygpIHsKICAgICAgYXdhaXQgdGhpcy5nZXRTZkxpc3QoKTsgLy/mmK8v5ZCm77yM5a2X5YW4CiAgICAgIGF3YWl0IHRoaXMuZ2V0QWxsU2JseExpc3QoKTsKICAgIH0sCiAgICAvL+iOt+WPluaJgOacieiuvuWkh+exu+Wei+S4i+aLieahhueUqOS6juafpeivogogICAgZ2V0QWxsU2JseExpc3QoKSB7CiAgICAgIGdldEFsbFNibHhMaXN0KHsgenk6ICJiZHNiIiB9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5maWx0ZXJJbmZvLmZpZWxkTGlzdC5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgaWYgKGl0ZW0udmFsdWUgPT09ICJzYmx4IikgewogICAgICAgICAgICBpdGVtLm9wdGlvbnMgPSByZXMuZGF0YTsKICAgICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICAvL+iOt+WPluaYry/lkKbvvIzlrZflhbgKICAgIGdldFNmTGlzdCgpIHsKICAgICAgZ2V0RGljdFR5cGVEYXRhKCJzeXNfc2YiKS50aGVuKHJlcyA9PiB7CiAgICAgICAgbGV0IHNmTGlzdCA9IFtdOwogICAgICAgIHJlcy5kYXRhLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICBzZkxpc3QucHVzaCh7IGxhYmVsOiBpdGVtLmxhYmVsLCB2YWx1ZTogaXRlbS52YWx1ZSB9KTsKICAgICAgICB9KTsKICAgICAgICB0aGlzLmZpbHRlckluZm8uZmllbGRMaXN0LmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICBpZiAoaXRlbS52YWx1ZSA9PT0gImlzQ2xlYW4iKSB7CiAgICAgICAgICAgIGl0ZW0ub3B0aW9ucyA9IHNmTGlzdDsKICAgICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICAvL+agueaNruWIhuWFrOWPuOiOt+WPluWPmOeUteermQogICAgYXN5bmMgZ2V0QmR6U2VsZWN0TGlzdCh2YWwpIHsKICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgImJkeiIsICIiKTsKICAgICAgYXdhaXQgZ2V0QmR6U2VsZWN0TGlzdCh7IHNzZHdibTogdmFsIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmJkekxpc3QgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLy/moLnmja7lj5jnlLXnq5nojrflj5bpl7TpmpQKICAgIGFzeW5jIGdldEpnU2VsZWN0TGlzdCh2YWwpIHsKICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgImpnIiwgIiIpOwogICAgICBhd2FpdCBnZXRKZ0RhdGFMaXN0U2VsZWN0ZWQoeyBzc2JkejogdmFsIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmpnTGlzdCA9IHJlcy5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICAvL+mXtOmalOS4i+aLieahhmNoYW5nZeS6i+S7tgogICAgYXN5bmMgamdDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICJzYmx4IiwgIiIpOwogICAgICBhd2FpdCBnZXRCbGdrU2JseEJ5SmcoeyBzc2pnOiB2YWwgfSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuc2JseExpc3QgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLy/orr7lpIfnsbvlnovkuIvmi4nmoYZjaGFuZ2Xkuovku7YKICAgIGFzeW5jIHNibHhDaGFuZ2UodmFsKSB7CiAgICAgIC8v6YeN572u6KGo5Y2V5Y+K5a2X5YW4CiAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICJzYmlkIiwgIiIpOwogICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLCAidHlwZSIsICIiKTsKICAgICAgdGhpcy5zYkxpc3QgPSBbXTsKICAgICAgdGhpcy5tc0xpc3QgPSBbXTsKICAgICAgdGhpcy5seExpc3QgPSBbXTsKICAgICAgLy/ojrflj5borr7lpIfkuIvmi4nmoYYKICAgICAgYXdhaXQgZ2V0U2IoeyBzYmx4OiB2YWwsIHNzamc6IHRoaXMuZm9ybS5qZyB9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5zYkxpc3QgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICAgIC8v6I635Y+W5LiN6Imv5bel5Ya157G75Z6L5LiL5ouJ5qGGCiAgICAgIGF3YWl0IGdldEx4KHsgc2JseDogdmFsIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICByZXMuZGF0YS5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgdGhpcy5seExpc3QucHVzaCh7IGxhYmVsOiBpdGVtLmxhYmVsLCB2YWx1ZTogaXRlbS5sYWJlbCB9KTsKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgYXN5bmMgbHhDaGFuZ2UodmFsKSB7CiAgICAgIC8v6YeN572u6KGo5Y2V5Y+K5a2X5YW4CiAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICJtcyIsICIiKTsKICAgICAgdGhpcy5tc0xpc3QgPSBbXTsKICAgICAgLy/ojrflj5bkuI3oia/lt6XlhrXmj4/ov7DkuIvmi4nmoYYKICAgICAgYXdhaXQgZ2V0TXMoeyBzYmx4OiB0aGlzLmZvcm0uc2JseCwgdHlwZTogdmFsIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLm1zTGlzdCA9IHJlcy5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICAvL+S4jeiJr+W3peWGteaPj+i/sOS4i+aLieahhmNoYW5nZeS6i+S7tgogICAgYXN5bmMgbXNDaGFuZ2UodmFsKSB7CiAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICJmbHlqIiwgIiIpOwogICAgICB0aGlzLmZseWpMaXN0ID0gW107CiAgICAgIGxldCBtc0xhYmVsID0gIiI7CiAgICAgIHRoaXMubXNMaXN0LmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgaWYgKGl0ZW0udmFsdWUgPT0gdmFsKSB7CiAgICAgICAgICBtc0xhYmVsID0gaXRlbS5sYWJlbDsKICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICB9CiAgICAgIH0pOwogICAgICAvL+iOt+WPluWIhuexu+S+neaNruS4i+aLieahhgogICAgICBhd2FpdCBnZXRGbHlqKHsgc2JseDogdGhpcy5mb3JtLnNibHgsIG1zOiBtc0xhYmVsIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmZseWpMaXN0ID0gcmVzLmRhdGE7CiAgICAgIH0pOwogICAgfSwKICAgIC8v5L+d5a2Y6KGo5Y2VCiAgICBhc3luYyBzYXZlRnVuKCkgewogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgLy/orr7nva7omZrmi5/lrZfmrrXnlKjkuo7kv53lrZjorr7lpIflvILluLjlj4rkuovmlYXlpITnkIbmlbDmja4KICAgICAgICAgIGxldCBtcyA9ICIiOwogICAgICAgICAgbGV0IGZseWpDbiA9ICIiOwogICAgICAgICAgdGhpcy5tc0xpc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICAgICAgaWYgKGl0ZW0udmFsdWUgPT0gdGhpcy5mb3JtLm1zKSB7CiAgICAgICAgICAgICAgbXMgPSBpdGVtLmxhYmVsOwogICAgICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgICB0aGlzLmZseWpMaXN0LmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICAgIGlmIChpdGVtLnZhbHVlID09IHRoaXMuZm9ybS5mbHlqKSB7CiAgICAgICAgICAgICAgZmx5akNuID0gaXRlbS5sYWJlbDsKICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgICAgaWYgKE9iamVjdC5rZXlzKHRoaXMuc2JEYXRhKS5sZW5ndGggIT09IDApIHsKICAgICAgICAgICAgdGhpcy5mb3JtLmZncyA9IHRoaXMuc2JEYXRhLnNzZ3M7CiAgICAgICAgICAgIHRoaXMuZm9ybS5iZHogPSB0aGlzLnNiRGF0YS5zc2JkejsKICAgICAgICAgIH0KICAgICAgICAgIGlmIChtcykgewogICAgICAgICAgICB0aGlzLmZvcm0ubXMgPSBtczsKICAgICAgICAgIH0KICAgICAgICAgIHRoaXMuZm9ybS5mbHlqQ24gPSBmbHlqQ247CiAgICAgICAgICBpZiAodGhpcy5mb3JtLnhjc2opIHsKICAgICAgICAgICAgdGhpcy5mb3JtLmlzQ2xlYW4gPSAi5pivIjsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHRoaXMuZm9ybS5pc0NsZWFuID0gIuWQpiI7IC8v6K6+572u5piv5ZCm5raI6Zmk5Li65ZCmCiAgICAgICAgICB9CiAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICAgIHRoaXMuc2F2ZUxvYWRpbmcgPSBMb2FkaW5nLnNlcnZpY2UoewogICAgICAgICAgICAgIGxvY2s6IHRydWUsIC8vbG9ja+eahOS/ruaUueespi0t6buY6K6k5pivZmFsc2UKICAgICAgICAgICAgICB0ZXh0OiAi5L+d5a2Y5Lit77yM6K+356iN5ZCOIiwgLy/mmL7npLrlnKjliqDovb3lm77moIfkuIvmlrnnmoTliqDovb3mlofmoYgKICAgICAgICAgICAgICBzcGlubmVyOiAiZWwtaWNvbi1sb2FkaW5nIiwgLy/oh6rlrprkuYnliqDovb3lm77moIfnsbvlkI0KICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAicmdiYSgwLCAwLCAwLCAwLjcpIiwgLy/pga7nvanlsYLpopzoibIKICAgICAgICAgICAgICB0YXJnZXQ6IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoIiNibGdrX2RpdiIpCiAgICAgICAgICAgIH0pOwogICAgICAgICAgICBzYXZlT3JVcGRhdGUodGhpcy5mb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5pON5L2c5oiQ5YqfISIpOwogICAgICAgICAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgICAgICAgICAgICB0aGlzLnNhdmVMb2FkaW5nLmNsb3NlKCk7CiAgICAgICAgICAgICAgICB0aGlzLmlzU2hvdyA9IGZhbHNlOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSk7CiAgICAgICAgICB9KTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7IHR5cGU6ICJlcnJvciIsIG1lc3NhZ2U6ICLmoKHpqozmnKrpgJrov4ciIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgZmlsdGVyUmVzZXQoKSB7CiAgICAgIHRoaXMuZmlsdGVySW5mby5maWVsZExpc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICBpZiAoaXRlbS50eXBlID09PSAiY2hlY2tib3giKSB7CiAgICAgICAgICBpdGVtLmNoZWNrYm94VmFsdWUgPSBbXTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8v5YiG6aG15p+l6K+i5YiX6KGoCiAgICBnZXREYXRhKHBhcmFtcykgewogICAgICAvL+WPguaVsOWQiOW5tgogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICBjb25zdCBwYXJhbSA9IHsgLi4udGhpcy5xdWVyeVBhcmFtcywgLi4ucGFyYW1zIH07CiAgICAgIHRoaXMucXVlcnlQYXJhbXMgPSBwYXJhbTsKICAgICAgZ2V0RGF0YShwYXJhbSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby50YWJsZURhdGEgPSByZXMuZGF0YS5yZWNvcmRzOwogICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci50b3RhbCA9IHJlcy5kYXRhLnRvdGFsOwogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvKioKICAgICAqIOaWsOWinuS4jeiJr+W3peWGtQogICAgICovCiAgICBhZGRSb3coKSB7CiAgICAgIHRoaXMudGl0bGUgPSAi5paw5aKe5LiN6Imv5bel5Ya1IjsKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gZmFsc2U7CiAgICAgIHRoaXMuZm9ybSA9IHt9OwogICAgICB0aGlzLmZvcm0ubHJyID0gdGhpcy4kc3RvcmUuZ2V0dGVycy5uYW1lOwogICAgICB0aGlzLmZvcm0ubHJybWMgPSB0aGlzLiRzdG9yZS5nZXR0ZXJzLm5pY2tOYW1lOwogICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLCAiZnhzaiIsIGZvcm1hdHRlckRhdGVUaW1lKG5ldyBEYXRlKCksICJ5eXl5LU1NLWRkIikpOwogICAgICB0aGlzLmlzU2hvdyA9IHRydWU7CiAgICB9LAogICAgLyoqCiAgICAgKiDnvJbovpEKICAgICAqLwogICAgYXN5bmMgaGFuZGxlVXBkYXRlKHJvdykgewogICAgICB0aGlzLnNhdmVMb2FkaW5nID0gTG9hZGluZy5zZXJ2aWNlKHsKICAgICAgICBsb2NrOiB0cnVlLCAvL2xvY2vnmoTkv67mlLnnrKYtLem7mOiupOaYr2ZhbHNlCiAgICAgICAgdGV4dDogIuWKoOi9veS4re+8jOivt+eojeWQjiIsIC8v5pi+56S65Zyo5Yqg6L295Zu+5qCH5LiL5pa555qE5Yqg6L295paH5qGICiAgICAgICAgc3Bpbm5lcjogImVsLWljb24tbG9hZGluZyIsIC8v6Ieq5a6a5LmJ5Yqg6L295Zu+5qCH57G75ZCNCiAgICAgICAgYmFja2dyb3VuZDogInJnYmEoMCwgMCwgMCwgMC43KSIgLy/pga7nvanlsYLpopzoibIKICAgICAgfSk7CiAgICAgIHRoaXMudGl0bGUgPSAi57yW6L6R5LiN6Imv5bel5Ya1IjsKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gZmFsc2U7CiAgICAgIHRoaXMuZm9ybSA9IHsgLi4ucm93IH07CiAgICAgIHRoaXMuZm9ybS5mZ3MgPSByb3cuZmdzOwogICAgICB0aGlzLmZvcm0uYmR6ID0gcm93LmJkejsKICAgICAgLy/lpITnkIbkuIvmi4nmoYblm57mmL4KICAgICAgdGhpcy5seExpc3QgPSBbXTsKICAgICAgdGhpcy5tc0xpc3QgPSBbXTsKICAgICAgdGhpcy5mbHlqTGlzdCA9IFtdOwogICAgICAvL+iOt+WPluS4jeiJr+W3peWGteexu+Wei+S4i+aLieahhgogICAgICBhd2FpdCBnZXRMeCh7IHNibHg6IHJvdy5zYmx4IH0pLnRoZW4ocmVzID0+IHsKICAgICAgICByZXMuZGF0YS5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgdGhpcy5seExpc3QucHVzaCh7IGxhYmVsOiBpdGVtLmxhYmVsLCB2YWx1ZTogaXRlbS5sYWJlbCB9KTsKICAgICAgICB9KTsKICAgICAgICB0aGlzLmx4TGlzdC5wdXNoKHsgbGFiZWw6ICLlhbbku5YiLCB2YWx1ZTogIuWFtuS7liIgfSk7CiAgICAgIH0pOwogICAgICAvL+iOt+WPluS4jeiJr+W3peWGteaPj+i/sOS4i+aLieahhgogICAgICBhd2FpdCBnZXRNcyh7IHNibHg6IHRoaXMuZm9ybS5zYmx4LCB0eXBlOiByb3cudHlwZSB9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5tc0xpc3QgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICAgIGlmICh0aGlzLm1zTGlzdC5sZW5ndGgpIHsKICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLCAibXMiLCByb3cuZmx5aik7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgIm1zIiwgcm93Lm1zKTsKICAgICAgfQogICAgICAvL+iOt+WPluWIhuexu+S+neaNruS4i+aLieahhgogICAgICBhd2FpdCBnZXRGbHlqKHsgc2JseDogdGhpcy5mb3JtLnNibHgsIG1zOiByb3cubXMgfSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuZmx5akxpc3QgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICAgIC8v5YWz6Zet6YGu572p5bGCCiAgICAgIHRoaXMuc2F2ZUxvYWRpbmcuY2xvc2UoKTsKICAgICAgdGhpcy5pc1Nob3cgPSB0cnVlOwogICAgfSwKICAgIC8qKgogICAgICog6K+m5oOF5p+l55yLCiAgICAgKi8KICAgIGdldERldGFpbHMocm93KSB7CiAgICAgIHRoaXMuZm9ybSA9IHsgLi4ucm93IH07CiAgICAgIC8v5aSE55CG5Zue5pi+6Zeu6aKYCiAgICAgIHRoaXMuZm9ybS5mbHlqID0gcm93LmZseWpDbjsKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gdHJ1ZTsKICAgICAgdGhpcy5pc1Nob3cgPSB0cnVlOwogICAgICB0aGlzLnRpdGxlID0gIuS4jeiJr+W3peWGteivpuaDhSI7CiAgICB9LAogICAgLyoqCiAgICAgKiDliKDpmaQKICAgICAqLwogICAgYXN5bmMgaGFuZGxlRGVsZXRlQnlJZChpZCkgewogICAgICB0aGlzLiRjb25maXJtKCLmraTmk43kvZzlsIbmsLjkuYXliKDpmaTor6XmlbDmja4sIOaYr+WQpue7p+e7rT8iLCAi5o+Q56S6IiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkKICAgICAgICAudGhlbigoKSA9PiB7CiAgICAgICAgICByZW1vdmVCbGdrKFtpZF0pLnRoZW4oKHsgY29kZSB9KSA9PiB7CiAgICAgICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTmiJDlip8hIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTlpLHotKUhIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoKSA9PiB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogImluZm8iLAogICAgICAgICAgICBtZXNzYWdlOiAi5bey5Y+W5raI5Yig6ZmkIgogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICB9LAogICAgLyoqCiAgICAgKiDmtojpmaQKICAgICAqLwogICAgYXN5bmMgaGFuZGxlQ2xlYW4ocm93KSB7CiAgICAgIHRoaXMuJGNvbmZpcm0oIuaYr+WQpuehruWumua2iOmZpOS4jeiJr+W3peWGtT8iLCAi5o+Q56S6IiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkKICAgICAgICAudGhlbigoKSA9PiB7CiAgICAgICAgICBsZXQgZm9ybTEgPSB7IC4uLnJvdyB9OwogICAgICAgICAgY2xlYW4oZm9ybTEpLnRoZW4oKHsgY29kZSB9KSA9PiB7CiAgICAgICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLmtojpmaTmiJDlip8hIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLmtojpmaTlpLHotKUhIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoKSA9PiB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgdHlwZTogImluZm8iLAogICAgICAgICAgICBtZXNzYWdlOiAi5bey5Y+W5raI5raI6ZmkIgogICAgICAgICAgfSk7CiAgICAgICAgfSk7CiAgICB9LAogICAgLy/lvLnmoYblhbPpl63kuovku7YKICAgIGNsb3NlRnVuKCkgewogICAgICB0aGlzLmZvcm0gPSB7fTsKICAgICAgdGhpcy5pc1Nob3cgPSBmYWxzZTsKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8SA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/blgk", "sourcesContent": ["<template>\n  <el-row class=\"app-container\">\n    <el-row>\n      <!--基本信息查询及显示-->\n      <el-col :span=\"24\">\n        <!--搜索条件-->\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          @handleReset=\"filterReset\"\n          :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n        />\n      </el-col>\n    </el-row>\n\n    <el-row>\n      <el-col>\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button\n              type=\"primary\"\n              icon=\"el-icon-plus\"\n              v-hasPermi=\"['blgk:button:add']\"\n              @click=\"addRow\"\n              >新增</el-button\n            >\n          </div>\n          <comp-table :table-and-page-info=\"tableAndPageInfo\" height=\"65vh\">\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"200\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  icon=\"el-icon-edit\"\n                  title=\"编辑\"\n                  @click=\"handleUpdate(scope.row)\"\n                  v-if=\"\n                    (scope.row.createBy === $store.getters.name ) ||\n                      hasSuperRole\n                  \"\n                />\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"getDetails(scope.row)\"\n                  icon=\"el-icon-view\"\n                  title=\"详情\"\n                />\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  icon=\"el-icon-remove\"\n                  title=\"消除\"\n                  @click=\"handleClean(scope.row)\"\n                  v-if=\"\n                    scope.row.createBy === $store.getters.name &&\n                      scope.row.isClean != '是'\n                  \"\n                />\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  icon=\"el-icon-delete\"\n                  title=\"删除\"\n                  @click=\"handleDeleteById(scope.row.objId)\"\n                  v-if=\"\n                    scope.row.createBy === $store.getters.name || hasSuperRole  \n                  \"\n                />\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!-- 新增、详情弹出对话框 -->\n    <el-dialog\n      id=\"blgk_div\"\n      :title=\"title\"\n      :visible.sync=\"isShow\"\n      width=\"60%\"\n      @close=\"closeFun\"\n      v-dialogDrag\n    >\n      <div class=\"button_btn\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          v-if=\"!isDisabled\"\n          @click=\"chooseSbFun\"\n          >选择设备</el-button\n        >\n      </div>\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item prop=\"fgsmc\" label=\"分公司：\">\n              <el-input\n                v-model=\"form.fgsmc\"\n                :disabled=\"true\"\n                style=\"width:80%\"\n                placeholder=\"请选择分公司\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"变电站：\" prop=\"bdzmc\">\n              <el-input\n                v-model=\"form.bdzmc\"\n                :disabled=\"true\"\n                style=\"width:80%\"\n                placeholder=\"请选择变电站\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item prop=\"jgmc\" label=\"间隔：\">\n              <el-input\n                v-model=\"form.jgmc\"\n                :disabled=\"true\"\n                style=\"width:80%\"\n                placeholder=\"请选择间隔\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"设备类型：\" prop=\"sblxmc\">\n              <el-input\n                v-model=\"form.sblxmc\"\n                :disabled=\"true\"\n                style=\"width:80%\"\n                placeholder=\"请选择设备类型\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"设备：\" prop=\"sbmc\">\n              <el-input\n                v-model=\"form.sbmc\"\n                :disabled=\"true\"\n                style=\"width:80%\"\n                placeholder=\"请选择设备\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"不良工况类型:\" prop=\"type\">\n              <el-select\n                @change=\"lxChange\"\n                placeholder=\"请选择不良工况类型\"\n                v-model=\"form.type\"\n                style=\"width:80%\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option\n                  v-for=\"item in lxList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"不良工况描述：\" prop=\"ms\">\n              <el-select\n                @change=\"msChange\"\n                placeholder=\"请选择描述\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.ms\"\n                style=\"width: 91%\"\n                filterable\n                :allow-create=\"this.form.type === '其他'\"\n              >\n                <el-option\n                  v-for=\"item in msList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\" v-if=\"this.form.type !== '其他'\">\n            <el-form-item label=\"分类依据：\" prop=\"flyj\">\n              <el-select\n                :disabled=\"isDisabled\"\n                style=\"width: 91%\"\n                v-model=\"form.flyj\"\n                placeholder=\"请选择分类依据\"\n              >\n                <el-option\n                  v-for=\"item in flyjList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"录入人：\" prop=\"lrrmc\">\n              <el-input\n                v-model=\"form.lrrmc\"\n                :disabled=\"true\"\n                style=\"width:80%\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"发现时间：\" prop=\"fxsj\">\n              <el-date-picker\n                style=\"width:80%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.fxsj\"\n                type=\"date\"\n                format=\"yyyy-MM-dd\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"请选择时间\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"消除时间：\" prop=\"xcsj\">\n              <el-date-picker\n                style=\"width:80%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.xcsj\"\n                type=\"date\"\n                format=\"yyyy-MM-dd\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"请选择时间\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注：\" prop=\"bz\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"5\"\n                style=\"width: 92%\"\n                v-model=\"form.bz\"\n                :disabled=\"isDisabled\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun\">取 消</el-button>\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"saveFun\"\n          >保 存</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <!--新主设备选择框组件-->\n    <el-dialog\n      title=\"设备选择\"\n      :visible.sync=\"isShowSbChoose\"\n      width=\"86%\"\n      v-if=\"isShowSbChoose\"\n      v-dialogDrag\n    >\n      <sb-choose\n        @closeChooseFun=\"closeChooseFun\"\n        @getSbDataFun=\"getSbDataFun\"\n      />\n    </el-dialog>\n  </el-row>\n</template>\n\n<script>\nimport {\n  removeBlgk,\n  saveOrUpdate,\n  getData,\n  getAllSblxList,\n  getBlgkSblxByJg,\n  getSb,\n  clean\n} from \"@/api/blgk/blgk\";\nimport { getLx, getMs, getFlyj } from \"@/api/blgk/blgkbzk\";\nimport { getDeptListById } from \"@/api/system/dept\";\nimport { getBdzSelectList } from \"@/api/yxgl/bdyxgl/bdxjzqpz\";\nimport { getJgDataListSelected } from \"@/api/dagangOilfield/asset/bdsbtz\";\nimport { formatterDateTime } from \"@/utils/handleData\";\nimport { Loading } from \"element-ui\";\nimport { getDictTypeData } from \"@/api/system/dict/data\";\nimport sbChoose from \"@/views/dagangOilfield/blgk/chooseBdsb\";\n\nexport default {\n  name: \"blgk\",\n  components: { sbChoose },\n  data() {\n    return {\n      hasSuperRole: this.$store.getters.hasSuperRole,\n      isShow: false, //弹框是否显示\n      loading: false,\n      //是否禁用\n      isDisabled: false,\n      form: {},\n      //查询参数\n      queryParams: {\n        pageSize: 10,\n        pageNum: 1\n      },\n      //详情对话框标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          // fgsmc: \"\",\n          bdzmc: \"\",\n          jgmc: \"\",\n          sbmc: \"\",\n          sblx: \"\",\n          isClean: \"\"\n        },\n        fieldList: [\n          // { label: \"分公司\", type: \"select\", value: \"fgsmc\", options: [] },\n          { label: \"变电站\", type: \"input\", value: \"bdzmc\" },\n          { label: \"间隔名称\", type: \"input\", value: \"jgmc\" },\n          { label: \"设备名称\", type: \"input\", value: \"sbmc\" },\n          {\n            label: \"设备类型\",\n            type: \"select\",\n            value: \"sblx\",\n            options: [],\n            clearable: true,\n            filterable: true\n          },\n          {\n            label: \"是否消除\",\n            type: \"select\",\n            value: \"isClean\",\n            options: [],\n            clearable: true\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: { checkBox: false, serialNumber: true },\n        tableData: [],\n        tableHeader: [\n          { prop: \"fgsmc\", label: \"分公司\", minWidth: \"120\" },\n          { prop: \"bdzmc\", label: \"变电站\", minWidth: \"120\" },\n          { prop: \"jgmc\", label: \"间隔名称\", minWidth: \"100\" },\n          { prop: \"sblxmc\", label: \"设备类型\", minWidth: \"120\" },\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"120\" },\n          { prop: \"ms\", label: \"不良工况描述\", minWidth: \"140\", showPop: true },\n          { prop: \"flyjCn\", label: \"分类依据\", minWidth: \"140\", showPop: true },\n          { prop: \"lrrmc\", label: \"录入人\", minWidth: \"80\" },\n          { prop: \"fxsj\", label: \"发现时间\", minWidth: \"110\" },\n          { prop: \"xcsj\", label: \"消除时间\", minWidth: \"110\" }\n        ]\n      },\n      rules: {\n        fgsmc: [{ required: true, message: \"分公司不能为空\", trigger: \"blur\" }],\n        bdzmc: [{ required: true, message: \"变电站不能为空\", trigger: \"blur\" }],\n        jgmc: [{ required: true, message: \"间隔不能为空\", trigger: \"blur\" }],\n        sblxmc: [\n          { required: true, message: \"设备类型不能为空\", trigger: \"blur\" }\n        ],\n        type: [\n          { required: true, message: \"不良工况类型不能为空\", trigger: \"select\" }\n        ],\n        sbmc: [{ required: true, message: \"设备不能为空\", trigger: \"blur\" }],\n        ms: [\n          { required: true, message: \"不良工况不能为空\", trigger: \"select\" }\n        ],\n        flyj: [\n          { required: true, message: \"分类依据不能为空\", trigger: \"select\" }\n        ]\n      },\n      fgsList: [], //分公司下拉框\n      bdzList: [], //变电站下拉框\n      jgList: [], //间隔下拉框\n      sblxList: [], //间隔下拉框\n      sbList: [], //设备下拉框\n      msList: [], //描述下拉框\n      flyjList: [], //分类依据下拉框\n      lxList: [], //类型下拉框\n      saveLoading: null, //保存时的遮罩层\n      isShowSbChoose: false, //是否显示设备选择弹框\n      sbData: {} //设备数据\n    };\n  },\n  created() {\n    //列表查询\n    this.getData();\n    this.getOptions(); //获取涉及到的下拉框字典值\n  },\n  methods: {\n    //选择设备\n    chooseSbFun() {\n      this.isShowSbChoose = true;\n    },\n    //关闭设备选择弹框\n    closeChooseFun() {\n      this.isShowSbChoose = false;\n    },\n    //获取设备数据\n    async getSbDataFun(sbData) {\n      this.sbData = sbData;\n      this.$set(this.form, \"fgsmc\", sbData.deptname);\n      this.$set(this.form, \"bdzmc\", sbData.bdzmc);\n      this.$set(this.form, \"jgmc\", sbData.wzmc);\n      this.$set(this.form, \"sblxmc\", sbData.sblxmc);\n      this.$set(this.form, \"sblx\", sbData.sblxbm);\n      this.$set(this.form, \"sbmc\", sbData.sbmc);\n      this.$set(this.form, \"sbid\", sbData.objId);\n      this.$set(this.form, \"type\", \"\");\n      this.$set(this.form, \"ms\", \"\");\n      this.$set(this.form, \"flyj\", \"\");\n      //获取不良工况类型下拉框\n      this.lxList = [];\n      await getLx({ sblx: sbData.sblxbm }).then(res => {\n        res.data.forEach(item => {\n          this.lxList.push({ label: item.label, value: item.label });\n        });\n        this.lxList.push({ label: \"其他\", value: \"其他\" });\n      });\n    },\n    //查询下拉框数据\n    async getOptions() {\n      await this.getSfList(); //是/否，字典\n      await this.getAllSblxList();\n    },\n    //获取所有设备类型下拉框用于查询\n    getAllSblxList() {\n      getAllSblxList({ zy: \"bdsb\" }).then(res => {\n        this.filterInfo.fieldList.forEach(item => {\n          if (item.value === \"sblx\") {\n            item.options = res.data;\n            return false;\n          }\n        });\n      });\n    },\n    //获取是/否，字典\n    getSfList() {\n      getDictTypeData(\"sys_sf\").then(res => {\n        let sfList = [];\n        res.data.forEach(item => {\n          sfList.push({ label: item.label, value: item.value });\n        });\n        this.filterInfo.fieldList.forEach(item => {\n          if (item.value === \"isClean\") {\n            item.options = sfList;\n            return false;\n          }\n        });\n      });\n    },\n    //根据分公司获取变电站\n    async getBdzSelectList(val) {\n      this.$set(this.form, \"bdz\", \"\");\n      await getBdzSelectList({ ssdwbm: val }).then(res => {\n        this.bdzList = res.data;\n      });\n    },\n    //根据变电站获取间隔\n    async getJgSelectList(val) {\n      this.$set(this.form, \"jg\", \"\");\n      await getJgDataListSelected({ ssbdz: val }).then(res => {\n        this.jgList = res.data;\n      });\n    },\n    //间隔下拉框change事件\n    async jgChange(val) {\n      this.$set(this.form, \"sblx\", \"\");\n      await getBlgkSblxByJg({ ssjg: val }).then(res => {\n        this.sblxList = res.data;\n      });\n    },\n    //设备类型下拉框change事件\n    async sblxChange(val) {\n      //重置表单及字典\n      this.$set(this.form, \"sbid\", \"\");\n      this.$set(this.form, \"type\", \"\");\n      this.sbList = [];\n      this.msList = [];\n      this.lxList = [];\n      //获取设备下拉框\n      await getSb({ sblx: val, ssjg: this.form.jg }).then(res => {\n        this.sbList = res.data;\n      });\n      //获取不良工况类型下拉框\n      await getLx({ sblx: val }).then(res => {\n        res.data.forEach(item => {\n          this.lxList.push({ label: item.label, value: item.label });\n        });\n      });\n    },\n    async lxChange(val) {\n      //重置表单及字典\n      this.$set(this.form, \"ms\", \"\");\n      this.msList = [];\n      //获取不良工况描述下拉框\n      await getMs({ sblx: this.form.sblx, type: val }).then(res => {\n        this.msList = res.data;\n      });\n    },\n    //不良工况描述下拉框change事件\n    async msChange(val) {\n      this.$set(this.form, \"flyj\", \"\");\n      this.flyjList = [];\n      let msLabel = \"\";\n      this.msList.forEach(item => {\n        if (item.value == val) {\n          msLabel = item.label;\n          return false;\n        }\n      });\n      //获取分类依据下拉框\n      await getFlyj({ sblx: this.form.sblx, ms: msLabel }).then(res => {\n        this.flyjList = res.data;\n      });\n    },\n    //保存表单\n    async saveFun() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          //设置虚拟字段用于保存设备异常及事故处理数据\n          let ms = \"\";\n          let flyjCn = \"\";\n          this.msList.forEach(item => {\n            if (item.value == this.form.ms) {\n              ms = item.label;\n              return false;\n            }\n          });\n          this.flyjList.forEach(item => {\n            if (item.value == this.form.flyj) {\n              flyjCn = item.label;\n              return false;\n            }\n          });\n          if (Object.keys(this.sbData).length !== 0) {\n            this.form.fgs = this.sbData.ssgs;\n            this.form.bdz = this.sbData.ssbdz;\n          }\n          if (ms) {\n            this.form.ms = ms;\n          }\n          this.form.flyjCn = flyjCn;\n          if (this.form.xcsj) {\n            this.form.isClean = \"是\";\n          } else {\n            this.form.isClean = \"否\"; //设置是否消除为否\n          }\n          this.$nextTick(() => {\n            this.saveLoading = Loading.service({\n              lock: true, //lock的修改符--默认是false\n              text: \"保存中，请稍后\", //显示在加载图标下方的加载文案\n              spinner: \"el-icon-loading\", //自定义加载图标类名\n              background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n              target: document.querySelector(\"#blgk_div\")\n            });\n            saveOrUpdate(this.form).then(res => {\n              if (res.code === \"0000\") {\n                this.$message.success(\"操作成功!\");\n                this.getData();\n                this.saveLoading.close();\n                this.isShow = false;\n              }\n            });\n          });\n        } else {\n          this.$message({ type: \"error\", message: \"校验未通过\" });\n        }\n      });\n    },\n    filterReset() {\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n    },\n    //分页查询列表\n    getData(params) {\n      //参数合并\n      this.loading = true;\n      const param = { ...this.queryParams, ...params };\n      this.queryParams = param;\n      getData(param).then(res => {\n        this.tableAndPageInfo.tableData = res.data.records;\n        this.tableAndPageInfo.pager.total = res.data.total;\n        this.loading = false;\n      });\n    },\n    /**\n     * 新增不良工况\n     */\n    addRow() {\n      this.title = \"新增不良工况\";\n      this.isDisabled = false;\n      this.form = {};\n      this.form.lrr = this.$store.getters.name;\n      this.form.lrrmc = this.$store.getters.nickName;\n      this.$set(this.form, \"fxsj\", formatterDateTime(new Date(), \"yyyy-MM-dd\"));\n      this.isShow = true;\n    },\n    /**\n     * 编辑\n     */\n    async handleUpdate(row) {\n      this.saveLoading = Loading.service({\n        lock: true, //lock的修改符--默认是false\n        text: \"加载中，请稍后\", //显示在加载图标下方的加载文案\n        spinner: \"el-icon-loading\", //自定义加载图标类名\n        background: \"rgba(0, 0, 0, 0.7)\" //遮罩层颜色\n      });\n      this.title = \"编辑不良工况\";\n      this.isDisabled = false;\n      this.form = { ...row };\n      this.form.fgs = row.fgs;\n      this.form.bdz = row.bdz;\n      //处理下拉框回显\n      this.lxList = [];\n      this.msList = [];\n      this.flyjList = [];\n      //获取不良工况类型下拉框\n      await getLx({ sblx: row.sblx }).then(res => {\n        res.data.forEach(item => {\n          this.lxList.push({ label: item.label, value: item.label });\n        });\n        this.lxList.push({ label: \"其他\", value: \"其他\" });\n      });\n      //获取不良工况描述下拉框\n      await getMs({ sblx: this.form.sblx, type: row.type }).then(res => {\n        this.msList = res.data;\n      });\n      if (this.msList.length) {\n        this.$set(this.form, \"ms\", row.flyj);\n      } else {\n        this.$set(this.form, \"ms\", row.ms);\n      }\n      //获取分类依据下拉框\n      await getFlyj({ sblx: this.form.sblx, ms: row.ms }).then(res => {\n        this.flyjList = res.data;\n      });\n      //关闭遮罩层\n      this.saveLoading.close();\n      this.isShow = true;\n    },\n    /**\n     * 详情查看\n     */\n    getDetails(row) {\n      this.form = { ...row };\n      //处理回显问题\n      this.form.flyj = row.flyjCn;\n      this.isDisabled = true;\n      this.isShow = true;\n      this.title = \"不良工况详情\";\n    },\n    /**\n     * 删除\n     */\n    async handleDeleteById(id) {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          removeBlgk([id]).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    /**\n     * 消除\n     */\n    async handleClean(row) {\n      this.$confirm(\"是否确定消除不良工况?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          let form1 = { ...row };\n          clean(form1).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"消除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"消除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消消除\"\n          });\n        });\n    },\n    //弹框关闭事件\n    closeFun() {\n      this.form = {};\n      this.isShow = false;\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.card1 {\n  margin-bottom: 6px;\n}\n\n.search-condition {\n  font-size: 13px;\n  color: #9c9c9c;\n\n  .el-select {\n    .el-input {\n      width: 100%;\n    }\n  }\n\n  .el-col {\n    vertical-align: middle;\n    line-height: 32px;\n    text-align: left;\n  }\n}\n</style>\n"]}]}