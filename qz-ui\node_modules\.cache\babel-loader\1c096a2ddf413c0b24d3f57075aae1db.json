{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\gzt\\gztMain.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\gzt\\gztMain.vue", "mtime": 1706897325053}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["gztMain.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAyJA;;AACA;;AACA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IADA,kBACA;AACA,WAAA;AACA,MAAA,QAAA,EAAA,EADA;AAEA,MAAA,YAAA,EAAA,KAFA;AAGA,MAAA,OAAA,EAAA,EAHA;AAIA,MAAA,WAAA,EAAA,QAJA;AAKA,MAAA,SAAA,EAAA,YALA;AAMA,MAAA,SAAA,EAAA,QANA;AAOA,MAAA,KAAA,EAAA,IAPA;AAQA,MAAA,QAAA,EAAA,EARA;AASA,MAAA,KAAA,EAAA,EATA;AAUA,MAAA,QAAA,EAAA,EAVA;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,eAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,cAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,CAMA;AANA;AARA,OA/BA;AAgDA,MAAA,MAAA,EAAA,EAhDA;AAiDA,MAAA,WAAA,EAAA,EAjDA;AAkDA,MAAA,QAAA,EAAA,KAlDA;AAmDA,MAAA,QAAA,EAAA,EAnDA;AAoDA,MAAA,eAAA,EAAA,EApDA;AAqDA,MAAA,qBAAA,EAAA;AArDA,KAAA;AAuDA,GAzDA;AA0DA;AACA,EAAA,UAAA,EAAA;AAAA,IAAA,SAAA,EAAA,kBAAA;AAAA,IAAA,QAAA,EAAA,iBAAA;AAAA,IAAA,UAAA,EAAA,mBAAA;AAAA,IAAA,QAAA,EAAA;AAAA,GA3DA;AA4DA,EAAA,OA5DA,qBA4DA;AAAA;;AACA;AACA,SAAA,gBAAA;AACA,SAAA,KAAA,GAAA,WAAA,CAAA,YAAA;AACA,MAAA,MAAA,CAAA,gBAAA;AACA,KAFA,EAEA,IAFA,CAAA;AAGA,SAAA,uBAAA;AACA,SAAA,OAAA;AACA,GApEA;AAqEA,EAAA,OAAA,EAAA;AACA,IAAA,uBADA,qCACA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,yCAAA,MAAA,CAAA,WAAA,CADA;;AAAA;AAAA;AACA,gBAAA,IADA,yBACA,IADA;AACA,gBAAA,IADA,yBACA,IADA;;AAEA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,qBAAA,GAAA,IAAA;AACA;;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,KANA;AAOA;AACA,IAAA,aARA,2BAQA;AACA,WAAA,YAAA,GAAA,KAAA;AACA,KAVA;AAWA,IAAA,MAXA,kBAWA,GAXA,EAWA;AACA,UAAA,GAAA,CAAA,SAAA,KAAA,SAAA,EAAA;AACA;AACA,YAAA,QAAA,GAAA,KAAA,MAAA,CAAA,OAAA,CAAA,QAAA;;AACA,YAAA,QAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AAAA,mEACA,QADA;AAAA;;AAAA;AACA,gEAAA;AAAA,kBAAA,OAAA;;AACA,kBAAA,OAAA,CAAA,IAAA,KAAA,OAAA,EAAA;AACA,qBAAA,OAAA,CAAA,IAAA,CAAA;AACA,kBAAA,IAAA,EAAA,OAAA,CAAA,IADA;AAEA,kBAAA,KAAA,EAAA;AAAA,oBAAA,KAAA,EAAA,GAAA,CAAA,UAAA;AAAA,oBAAA,MAAA,EAAA,GAAA,CAAA;AAAA;AAFA,iBAAA;AAIA;AACA;AACA;AATA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA,OAdA,MAcA;AACA,aAAA,OAAA,CAAA,IAAA,CAAA;AACA,UAAA,IAAA,EAAA,GAAA,CAAA,SADA;AAEA,UAAA,KAAA,EAAA;AAAA,YAAA,KAAA,EAAA,GAAA,CAAA,UAAA;AAAA,YAAA,MAAA,EAAA,GAAA,CAAA;AAAA;AAFA,SAAA;AAIA;AACA,KAhCA;AAiCA,IAAA,UAjCA,sBAiCA,GAjCA,EAiCA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,QAAA,mCAAA,GAAA;AACA,gBAAA,MAAA,CAAA,QAAA,GAAA,IAAA;;AAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,KApCA;AAqCA,IAAA,YArCA,wBAqCA,GArCA,EAqCA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,WAAA,GAAA,GAAA,CAAA,UAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,oBAAA,GAAA,GAAA,CAAA,SAAA;AACA,gBAAA,MAAA,CAAA,WAAA,CAAA,YAAA,GAAA,GAAA,CAAA,MAAA;AAHA;AAAA,uBAIA,8BAAA,MAAA,CAAA,WAAA,CAJA;;AAAA;AAAA;AAIA,gBAAA,IAJA,sBAIA,IAJA;AAIA,gBAAA,IAJA,sBAIA,IAJA;AAKA,gBAAA,MAAA,CAAA,QAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,YAAA,GAAA,IAAA;;AANA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,KA5CA;AA6CA,IAAA,WA7CA,uBA6CA,IA7CA,EA6CA;AACA,WAAA,SAAA,GAAA,IAAA;AACA,WAAA,OAAA;AACA,KAhDA;AAiDA,IAAA,iBAjDA,+BAiDA;AACA,WAAA,OAAA;AACA,KAnDA;AAoDA,IAAA,OApDA,mBAoDA,KApDA,EAoDA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,GADA,+DACA,KADA,GACA,MAAA,CAAA,MADA;;AAEA,oBAAA,MAAA,CAAA,SAAA,KAAA,QAAA,EAAA;AACA,kBAAA,GAAA,CAAA,UAAA,GAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA,IAAA;AACA,kBAAA,GAAA,CAAA,QAAA,GAAA,CAAA;AACA;;AACA,oBAAA,MAAA,CAAA,SAAA,KAAA,SAAA,EAAA;AACA,kBAAA,GAAA,CAAA,WAAA,GAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA,IAAA;AACA,kBAAA,GAAA,CAAA,QAAA,GAAA,CAAA;AACA,kBAAA,GAAA,CAAA,SAAA,GAAA,MAAA,CAAA,WAAA;AACA;;AACA,oBAAA,MAAA,CAAA,SAAA,KAAA,UAAA,EAAA;AACA,kBAAA,GAAA,CAAA,QAAA,GAAA,CAAA;AACA,kBAAA,GAAA,CAAA,SAAA,GAAA,MAAA,CAAA,WAAA;AACA;;AAEA,gBAAA,GAAA,CAAA,QAAA,GAAA,MAAA,CAAA,OAAA;AAhBA;AAAA,uBAiBA,4BAAA,GAAA,CAjBA;;AAAA;AAAA;AAiBA,gBAAA,IAjBA,qBAiBA,IAjBA;AAiBA,gBAAA,IAjBA,qBAiBA,IAjBA;;AAkBA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AACA,iDAAA,GAAA,EAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA,sBAAA,QAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,oBAAA,MAAA,CAAA,eAAA,GAAA,QAAA,CAAA,IAAA;AACA;AACA,iBAJA;;AAtBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA2BA,KA/EA;AAgFA;AACA,IAAA,gBAjFA,8BAiFA;AACA,UAAA,KAAA,GAAA,IAAA;;AACA,UAAA,IAAA,GAAA,IAAA,IAAA,GAAA,WAAA,EAAA,CAFA,CAEA;;AACA,UAAA,KAAA,GAAA,IAAA,IAAA,GAAA,QAAA,KAAA,CAAA,CAHA,CAGA;;AACA,UAAA,GAAA,GAAA,IAAA,IAAA,GAAA,OAAA,EAAA,CAJA,CAIA;;AACA,UAAA,KAAA,GAAA,IAAA,IAAA,GAAA,QAAA,EAAA,CALA,CAKA;;AACA,UAAA,OAAA,GAAA,IAAA,IAAA,GAAA,UAAA,EAAA,CANA,CAMA;;AACA,UAAA,OAAA,GAAA,IAAA,IAAA,GAAA,UAAA,EAAA,CAPA,CAOA;;AACA,UAAA,IAAA,GAAA,IAAA,IAAA,GAAA,MAAA,EAAA;AACA,UAAA,KAAA,GAAA,CAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,GAAA,CAAA;AACA,UAAA,OAAA,GAAA,OAAA,KAAA,CAAA,IAAA,CAAA,CAVA,CAWA;;AACA,UAAA,KAAA,GAAA,EAAA,EAAA;AACA,QAAA,KAAA,GAAA,MAAA,KAAA;AACA;;AACA,UAAA,OAAA,GAAA,EAAA,EAAA;AACA,QAAA,OAAA,GAAA,MAAA,OAAA;AACA;;AACA,UAAA,OAAA,GAAA,EAAA,EAAA;AACA,QAAA,OAAA,GAAA,MAAA,OAAA;AACA,OApBA,CAqBA;;;AACA,MAAA,KAAA,CAAA,KAAA,GAAA,KAAA,GAAA,GAAA,GAAA,OAAA,GAAA,GAAA,GAAA,OAAA;AACA,MAAA,KAAA,CAAA,QAAA,GAAA,IAAA,GAAA,GAAA,GAAA,KAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA;AACA,MAAA,KAAA,CAAA,QAAA,GAAA,OAAA;AACA;AA1GA;AArEA,C", "sourcesContent": ["<template>\n  <div class=\"gztcontent\">\n    <el-row :gutter=\"1\">\n      <el-col :span=\"6\">\n        <div class=\"grid-content bg-purple\">\n          <el-row :gutter=\"1\">\n            <el-col :span=\"24\">\n              <div  class=\"timecard\">\n                <div class=\"date\" >\n                  <p class=\"dataTime\"> <i class=\"el-icon-date\"></i>{{ dataTime }}</p>\n                  <p class=\"weektime\"> {{ weekTime }}</p>\n                  <p class=\"time\" style=\"margin-top:-20px\"> {{ times }}</p>\n                </div>\n              </div>\n              <div class=\"analysisTask\">\n                      <powerbox class=\"powercutbox\"></powerbox>\n                    <!-- <ul class=\"chartListUl\">\n                      <li> <img src=\"@/assets/image/gzt2.png\" alt=\"\" /><p>待我处理</p>\n                        <p style=\"color: red; font-size:20px;margin-top: -5px;\">{{countDgTodoItemNumber.tackle}}</p>\n                      </li>\n                      <li> <img src=\"@/assets/image/gzt1.png\" alt=\"\" /> <p>由我发起</p>\n                        <p style=\"color: yellow;font-size:20px;margin-top: -5px;\">{{countDgTodoItemNumber.sponsor}}</p>\n                      </li>\n                      <li> <img src=\"@/assets/image/gzt3.png\" alt=\"\" /><p>处理完成</p>\n                        <p style=\"color: green;font-size:20px;margin-top: -5px;\">{{countDgTodoItemNumber.finished}}</p>\n                      </li>\n                    </ul> -->\n                  </div>\n            </el-col>\n          </el-row>\n        </div>\n      </el-col>\n      <el-col :span=\"10\">\n        <div class=\"grid-content bg-purple\">\n          <SingleLine class=\"SingleLinebox\"></SingleLine>\n        </div>\n      </el-col>\n      <el-col :span=\"8\">\n        <div class=\"grid-content bg-purple\" >\n          <Workbench class=\"noticebox\"></Workbench>\n        </div>\n      </el-col>\n    </el-row>\n    <el-row :gutter=\"1\">\n      <el-col :span=\"24\">\n        <div class=\"agency\">\n          <div class=\"txtTitle\">\n            <div>\n              <span @click=\"handleClick('tackle')\"\n                :class=\"this.agencyVal === 'tackle'?'tabActive':'noActive'\">待我处理  {{countDgTodoItemNumber.tackle}}</span>\n              <span @click=\"handleClick('sponsor')\"\n                :class=\"this.agencyVal === 'sponsor'?'tabActive':'noActive'\">由我发起  {{countDgTodoItemNumber.sponsor}}</span>\n              <span @click=\"handleClick('finished')\"\n                :class=\"this.agencyVal === 'finished'?'tabActive':'noActive'\">处理完成  {{countDgTodoItemNumber.finished}}</span>\n            </div>\n            <div style=\"float: right;margin-top:-37px ;\">\n              <el-input placeholder=\"请输入关键字\" v-model=\"keyword\" style=\"width:180px \"></el-input>\n              <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"getData()\"></el-button>\n            </div>\n          </div>\n          <div class=\"tabandSearch\">\n            <div>\n              <el-radio-group v-model=\"tabPosition\" @change=\"changeTabPosition\" v-if=\"!(agencyVal==='tackle')\">\n                <el-radio-button label=\"qxlccs\">设备隐患({{ countDgTodoItem.qxlccs }})</el-radio-button>\n                <el-radio-button label=\"sbsswxlc\">设施维护({{ countDgTodoItem.sbsswxlc }})</el-radio-button>\n                <el-radio-button label=\"gzplccs\">工作票({{ countDgTodoItem.gzplccs }})</el-radio-button>\n                <el-radio-button label=\"czplc,czpsh\">倒闸操作票({{ countDgTodoItem.czp }})</el-radio-button>\n                <el-radio-button label=\"fwzzjsgj\">防误装置({{ countDgTodoItem.fwzzjsgj }})</el-radio-button>\n                <el-radio-button label=\"gzrwdActivity\">工作任务单({{ countDgTodoItem.gzrwdActivity }})</el-radio-button>\n                <el-radio-button label=\"tdsqd_approval\">停电申请单({{ countDgTodoItem.tdsqd_approval }})</el-radio-button>\n                <el-radio-button label=\"sbztbg\">设备状态变更({{ countDgTodoItem.sbztbg }})</el-radio-button>\n                <el-radio-button label=\"yjbdfdActive\">应急发电工作单({{ countDgTodoItem.yjbdfdActive }})</el-radio-button>\n                <el-radio-button label=\"tzsplc\">图纸审批({{ countDgTodoItem.tzsplc }})</el-radio-button>\n                <el-radio-button label=\"uavflightplan\">无人机计划({{ countDgTodoItem.uavflightplan }})</el-radio-button>\n                <el-radio-button label=\"jxjhlc,jxjhlchz\">停电检修({{ countDgTodoItem.jxjhlc }})</el-radio-button>\n                <el-radio-button label=\"zjxftdjh,zjxftdjhhz\">非停电检修({{ countDgTodoItem.zjxftdjh}})</el-radio-button>\n              </el-radio-group>\n            </div>\n          </div>\n          <comp-table :table-and-page-info=\"tableAndPageInfo\" height=\"68vh\">\n            <el-table-column slot=\"table_six\" align=\"center\" style=\"display: block\" label=\"处理人\" min-width=\"120\"\n              :resizable=\"false\" v-if=\"agencyVal==='finished'\">\n              <template slot-scope=\"scope\">\n                {{ scope.row.handleUserName }}\n              </template>\n            </el-table-column>\n            <el-table-column slot=\"table_seven\" align=\"center\" style=\"display: block\" label=\"处理时间\" min-width=\"120\"\n              :resizable=\"false\" v-if=\"agencyVal==='finished'\">\n              <template slot-scope=\"scope\">\n                {{ scope.row.handleTime }}\n              </template>\n            </el-table-column>\n            <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作选项\"\n              min-width=\"120\" :resizable=\"false\">\n              <template slot-scope=\"scope\">\n                <el-button type=\"text\" size=\"small\" v-if=\"agencyVal==='tackle'\" @click=\"goPage(scope.row)\">处理\n                </el-button>\n                <el-button type=\"text\" size=\"small\" @click=\"getDetails(scope.row)\">详情</el-button>\n                <el-button type=\"text\" size=\"small\" @click=\"showTimeLine(scope.row)\">流程查看</el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </div>\n      </el-col>\n      <!-- <el-col :span=\"8\">\n      <div class=\"recentWork\">\n        <div class=\"txtTitle\">\n          <span :class=\"this.workValue == 'recentWork'?'tabActive':'noActive'\">近期处理工作</span>\n          <el-card>\n            <el-timeline>\n              <el-timeline-item v-for=\"(activity, index) in activities\" :key=\"index\"\n                                :icon=\"activity.icon\" :type=\"activity.type\" :color=\"activity.color\"\n                                :size=\"activity.size\" :timestamp=\"activity.timestamp\">\n                {{ activity.content }}\n              </el-timeline-item>\n            </el-timeline>\n          </el-card>\n        </div>\n      </div>\n    </el-col> -->\n    </el-row>\n    <el-dialog title=\"待办详情\" :visible.sync=\"openInfo\" width=\"50%\" append-to-body :close-on-click-modal=false>\n      <el-form ref=\"formInfo\" :model=\"formInfo\" label-width=\"120px\" disabled>\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"标题：\" prop=\"itemName\">\n              <el-input v-model=\"formInfo.itemName\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"内容：\" prop=\"itemContent\">\n              <el-input type=\"textarea\" :rows=\"3\" v-model=\"formInfo.itemContent\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"模块名称：\" prop=\"module\">\n              <el-input v-model=\"formInfo.module\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"通知时间：\" prop=\"todoTime\">\n              <el-date-picker v-model=\"formInfo.todoTime\" type=\"datetime\" format=\"yyyy-MM-dd HH:mm:ss\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n    </el-dialog>\n    <time-line :value=\"timeLineShow\" :timeData=\"timeData\" @closeTimeLine=\"colseTimeLine\" />\n  </div>\n</template>\n<script>\nimport Workbench from '@/components/Index/Workbench'\nimport powerbox from './powerbox.vue'\nimport SingleLine from '@/components/Index/SingleLine'\nimport { countDgTodoItem, countDgTodoItemByStatus, listByPage } from \"@/api/activiti/DgTodoItem\";\nimport { HistoryList } from \"@/api/activiti/processTask\";\nimport timeLine from 'com/timeLine'\n\nexport default {\n  data() {\n    return {\n      timeData: [],\n      timeLineShow: false,\n      keyword: '',\n      tabPosition: 'qxlccs',\n      workValue: 'recentWork',\n      agencyVal: 'tackle',\n      timer: null,\n      dataTime: '',\n      times: '',\n      weekTime: '',\n      //时间线\n      // activities: [{\n      //   content: '支持使用图标',\n      //   timestamp: '2018-04-12 20:46',\n      //   size: 'large',\n      //   type: 'primary',\n      //   icon: 'el-icon-more'\n      // }, {\n      //   content: '支持自定义颜色',\n      //   timestamp: '2018-04-03 20:46',\n      //   color: '#0bbd87'\n      // }, {\n      //   content: '支持自定义尺寸',\n      //   timestamp: '2018-04-03 20:46',\n      //   size: 'large'\n      // }, {\n      //   content: '默认样式的节点',\n      //   timestamp: '2018-04-03 20:46'\n      // }],\n      //表格\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: 'itemName', label: '工作内容', minWidth: '120' },\n          { prop: 'applyUserName', label: '创建人', minWidth: '120' },\n          { prop: 'todoUserName', label: '接收人', minWidth: '120' },\n          { prop: 'taskName', label: '当前节点', minWidth: '120' },\n          { prop: 'todoTime', label: '通知时间', minWidth: '120' },\n          // { prop: 'xh', label: '等待时间', minWidth: '120' },\n        ],\n      },\n      params: {},\n      processData: {},\n      openInfo: false,\n      formInfo: {},\n      countDgTodoItem: {},\n      countDgTodoItemNumber: {}\n    }\n  },\n  // 通知 公告组件\n  components: { Workbench, timeLine, SingleLine,powerbox},\n  mounted() {\n    //定时\n    this.getTimesInterval()\n    this.timer = setInterval(() => {\n      this.getTimesInterval()\n    }, 1000)\n    this.countDgTodoItemByStatus();\n    this.getData()\n  },\n  methods: {\n    async countDgTodoItemByStatus() {\n      let { code, data } = await countDgTodoItemByStatus(this.processData)\n      if (code === '0000') {\n        this.countDgTodoItemNumber = data\n      }\n    },\n    //关闭流程查看页面\n    colseTimeLine() {\n      this.timeLineShow = false\n    },\n    goPage(row) {\n      if (row.moduleKey === \"gzplccs\") {\n        //解决工作票跳转404的问题\n        const topMenus = this.$store.getters.topMenus\n        if (topMenus.length > 0) {\n          for (const topMenu of topMenus) {\n            if (topMenu.name === '工作票管理') {\n              this.$router.push({\n                path: topMenu.path,\n                query: { objId: row.businessId, module: row.moduleKey }\n              })\n              break;\n            }\n          }\n        }\n      } else {\n        this.$router.push({\n          path: row.routePath,\n          query: { objId: row.businessId, module: row.moduleKey }\n        })\n      }\n    },\n    async getDetails(row) {\n      this.formInfo = { ...row }\n      this.openInfo = true\n    },\n    async showTimeLine(row) {\n      this.processData.businessKey = row.businessId\n      this.processData.processDefinitionKey = row.moduleKey\n      this.processData.businessType = row.module\n      let { code, data } = await HistoryList(this.processData)\n      this.timeData = data\n      this.timeLineShow = true\n    },\n    handleClick(data) {\n      this.agencyVal = data\n      this.getData();\n    },\n    changeTabPosition() {\n      this.getData();\n    },\n    async getData(param) {\n      let par = { ...param, ...this.params }\n      if (this.agencyVal === \"tackle\") {\n        par.todoUserId = this.$store.getters.name\n        par.isHandle = 0\n      }\n      if (this.agencyVal === \"sponsor\") {\n        par.applyUserId = this.$store.getters.name\n        par.isHandle = 0\n        par.moduleKey = this.tabPosition;\n      }\n      if (this.agencyVal === \"finished\") {\n        par.isHandle = 1\n        par.moduleKey = this.tabPosition;\n      }\n\n      par.itemName = this.keyword\n      let { code, data } = await listByPage(par)\n      if (code === '0000') {\n        this.tableAndPageInfo.tableData = data.records\n        this.tableAndPageInfo.pager.total = data.total\n      }\n      countDgTodoItem(par).then(response => {\n        if (response.code === '0000') {\n          this.countDgTodoItem = response.data;\n        }\n      })\n    },\n    //获取时间\n    getTimesInterval() {\n      let _this = this;\n      let year = new Date().getFullYear(); //获取当前时间的年份\n      let month = new Date().getMonth() + 1; //获取当前时间的月份\n      let day = new Date().getDate(); //获取当前时间的天数\n      let hours = new Date().getHours(); //获取当前时间的小时\n      let minutes = new Date().getMinutes(); //获取当前时间的分数\n      let seconds = new Date().getSeconds(); //获取当前时间的秒数\n      let week = new Date().getDay();\n      let weeks = [\"日\", \"一\", \"二\", \"三\", \"四\", \"五\", \"六\"];\n      let getWeek = \"星期\" + weeks[week];\n      //当小于 10 的是时候，在前面加 0\n      if (hours < 10) {\n        hours = \"0\" + hours;\n      }\n      if (minutes < 10) {\n        minutes = \"0\" + minutes;\n      }\n      if (seconds < 10) {\n        seconds = \"0\" + seconds;\n      }\n      //拼接格式化当前时间\n      _this.times = hours + \":\" + minutes + \":\" + seconds;\n      _this.dataTime = year + \"年\" + month + \"月\" + day + \"日\";\n      _this.weekTime = getWeek\n    },\n  },\n}\n</script>\n<style lang=\"scss\" >\n.el-row {\n  &:last-child {\n    margin-bottom: 0;\n  }\n}\n.gztcontent {\n  overflow: -moz-scrollbars-none;\n  padding: 1px;\n  .el-col {\n    border-radius: 4px;\n  }\n  .bg-purple-dark {\n    background: #99a9bf;\n  }\n  .bg-purple {\n    .timecard {\n      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n      height: 50px;\n      background: #fefefe;\n      span {\n        font-size: 24px;\n        font-weight: bold;\n        color: #a3a3a3;\n      }\n      p {\n        font-size: 20px;\n        font-weight: bold;\n        color: #a3a3a3;\n        display: inline-block;\n        margin-right: 5px;\n\n      }\n      .date{\n        height: 50px;\n        padding-left:8px ;\n        border:solid 1px #d7d7d7;\n        background-color: #f7f7f7;\n        text-align: center;\n        .time {\n        color: #359076;\n        font-size: 24px;\n        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;\n      }\n      .weektime {\n        font-weight: bold;\n        font-size: 20px;\n        color: #a3a3a3;\n      }\n      }\n\n    }\n        // .chartListUl {\n        //   list-style: none;\n        //   display: flex;\n        //   justify-content: space-between; // space-between：两端对齐，子元素间隔相等 space-around：子元素两侧的间隔相等。\n        //   flex-wrap: wrap; //  wrap（向下换）\n        //   margin-top:30px ;\n        //   margin-left:-35px ;\n        //   li {\n        //     width: calc((100% - 70px) / 3); // 给子元素定宽\n        //     height: 200px; // height: calc(100% - 40px); 用calc 用的会比较多。\n        //   }\n        // }\n        // .chartListUl:after {\n        //   // 使用伪类元素占据单位，不影响页面\n        //   content: \"\";\n        //   height: 0;\n        //   width: calc((100% - 72px) / 3);;\n        // }\n      }\n    .gztcard {\n      display: flex;\n      flex-direction: column;\n\n      span {\n        color: #a3a3a3;\n        font-size: 20px;\n        font-weight: bold;\n      }\n\n      p {\n        font-weight: bold;\n        font-size: 40px;\n      }\n\n      img {\n        width: 70px;\n        height: 70px;\n      }\n    }\n  }\n\n  .bg-purple-light {\n    background: #e5e9f2;\n  }\n\n  .grid-content {\n    border-radius: 4px;\n\n    .noticebox {\n      max-height: 331px !important;\n      //box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n      margin-bottom: 1px;\n    }\n    .SingleLinebox {\n      height: 331px !important;\n      //box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n      .bzwj_ul{\n        max-height: 278px;\n      }\n      margin-top: 0;\n      margin-right: 0 !important;\n      margin-bottom: 1px;\n    }\n    .powercutbox{\n      max-height: 331px;\n    }\n  }\n  .row-bg {\n    padding: 10px 0;\n    background-color: #f9fafc;\n  }\n\n  .agency {\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n    .tabActive {\n      cursor: pointer;\n      color: #359076;\n      background: #fff;\n      display: inline-block;\n      width: 10%;\n      text-align: center;\n      border-top: 2px solid #00C994\n    }\n    .noActive {\n      color: #b1b1b1;\n      cursor: pointer;\n      display: inline-block;\n      width: 10%;\n      text-align: center;\n\n      &:hover {\n        background: #FFFFFF;\n        color: #359076;\n      }\n    }\n\n    .tabandSearch {\n      padding: 10px 5px 10px 5px;\n    }\n  }\n\n  .recentWork {\n    margin-top: 35px;\n    height: 420px;\n  }\n\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/gzt"}]}