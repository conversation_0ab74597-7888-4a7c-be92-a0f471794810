{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\sybgjlcomment\\symbSyxmSelect.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\sybgjlcomment\\symbSyxmSelect.vue", "mtime": 1706897323435}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCi8v5byV5YWlanF1ZXJ5LOaaguaXtuayoeeUqAppbXBvcnQgJCBmcm9tICJqcXVlcnkiCmltcG9ydCB7CiAgYWRkTWJHbHhtQmF0Y2hUb01ieG0sCiAgZ2V0UGFnZURhdGFMaXN0VG9zeW1iLAogIGdldFN5bWJHbHN5eG1EYXRhTGlzdEJ5UGFnZSwKICBnZXRYbUxpcmFyeURhdGEsCiAgcmVtb3ZlLAogIHNhdmVPclVwZGF0ZSwKICBnZXRNYkdsTXBpbmZvRGF0YSwKICBnZXRNYkdsWG1BbmRCdwp9IGZyb20gJ0AvYXBpL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvc3ltYndoJwppbXBvcnQge2dldERldmljZUNsYXNzVHJlZU5vZGVCeVBpZH0gZnJvbSAnQC9hcGkvZGFnYW5nT2lsZmllbGQvYnpnbC9zYmx4d2gvc2JseHdoJwppbXBvcnQge2dldEdsU3l6eG1EYXRhTGlzdEJ5UGFnZX0gZnJvbSAnQC9hcGkvZGFnYW5nT2lsZmllbGQvYnpnbC9zeXhtJwppbXBvcnQgR2xzeW1wIGZyb20gJ0Avdmlld3MvZGFnYW5nT2lsZmllbGQvYnpnbC9zeWJ6ay9nbHN5bXAnCmltcG9ydCBzeW1id2hEeW1ibnIgZnJvbSAnQC92aWV3cy9kYWdhbmdPaWxmaWVsZC9iemdsL3N5YnprL3N5bWJ3aER5bWJucicKaW1wb3J0IGh0bWxUb1BkZiBmcm9tICdAL3V0aWxzL3ByaW50L2h0bWxUb1BkZicKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnc3ltYlN5eG1TZWxlY3QnLAogIGNvbXBvbmVudHM6IHtHbHN5bXAsIHN5bWJ3aER5bWJucn0sCiAgIHByb3BzOiB7CiAgICAvL+e7hOS7tuS8oOWAvAogICAgc3ltYkRhdGE6IHsKICAgICAgdHlwZTogT2JqZWN0LAogICAgICBkZWZhdWx0OiAoKSA9PiAoewogICAgICAgIHNibHhpZDonJywgIAogICAgICB9KQogICAgfSwKCiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy/ln7rmnKzkv6Hmga/ooajmoLzmlbDmja4KICAgICAgdGFibGVEYXRhX2pieHg6IFsKICAgICAgICB7CiAgICAgICAgICAnY29sdW1uXzEnOiAn6K+V6aqM5oCn6LSoJywKICAgICAgICAgICdjb2x1bW5fMic6ICfor5Xpqozml6XmnJ8nLAogICAgICAgICAgJ2NvbHVtbl8zJzogJ+ivlemqjOS6uuWRmCcsCiAgICAgICAgICAnY29sdW1uXzQnOiAn6K+V6aqM5Zyw54K5JywKICAgICAgICAgICdjb2x1bW5fNSc6ICcnLAogICAgICAgICAgJ2NvbHVtbl82JzogJycsCiAgICAgICAgICAnY29sdW1uXzcnOiAnJywKICAgICAgICAgICdjb2x1bW5fOCc6ICcnLAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgJ2NvbHVtbl8xJzogJ+aKpeWRiuaXpeacnycsCiAgICAgICAgICAnY29sdW1uXzInOiAn57yW5YaZ5Lq6JywKICAgICAgICAgICdjb2x1bW5fMyc6ICflrqHmoLjkuronLAogICAgICAgICAgJ2NvbHVtbl80JzogJ+aJueWHhuS6uicsCiAgICAgICAgICAnY29sdW1uXzUnOiAnJywKICAgICAgICAgICdjb2x1bW5fNic6ICcnLAogICAgICAgICAgJ2NvbHVtbl83JzogJycsCiAgICAgICAgICAnY29sdW1uXzgnOiAnJywKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgICdjb2x1bW5fMSc6ICfor5XpqozlpKnmsJQnLAogICAgICAgICAgJ2NvbHVtbl8yJzogJ+eOr+Wig+a4qeW6pu+8iOKEg++8iScsCiAgICAgICAgICAnY29sdW1uXzMnOiAn546v5aKD55u45a+55rm/5bqm77yIJe+8iScsCiAgICAgICAgICAnY29sdW1uXzQnOiAn5oqV6L+Q5pel5pyfJywKICAgICAgICAgICdjb2x1bW5fNSc6ICcnLAogICAgICAgICAgJ2NvbHVtbl82JzogJycsCiAgICAgICAgICAnY29sdW1uXzcnOiAnJywKICAgICAgICAgICdjb2x1bW5fOCc6ICcnLAogICAgICAgIH0KICAgICAgXSwKICAgICAgLy/orr7lpIfpk63niYzooajmoLzmlbDmja4KICAgICAgdGFibGVEYXRhX3NibXA6IFt7CiAgICAgICAgJ2NvbHVtbl8xJzogJ+mineWumueUteWOiycsCiAgICAgICAgJ2NvbHVtbl8yJzogJ+iuvuWkh+Wei+WPtycsCiAgICAgICAgJ2NvbHVtbl8zJzogJycsCiAgICAgICAgJ2NvbHVtbl80JzogJycsCiAgICAgICAgJ2NvbHVtbl81JzogJycsCiAgICAgICAgJ2NvbHVtbl82JzogJycsCiAgICAgIH0sXSwKICAgICAgLy/opoHlvqrnjq/nmoTor5XpqozooajmoLzmlbDmja4KICAgICAgYXJyOiBbewogICAgICAgIHRpdGxlOiAiIiwvL+ivlemqjOWQjeensAogICAgICAgIHp4bUxpc3Q6IFtdLC8v5a2Q6aG555uu5pWw5o2u77yI6KGo5aS077yJCiAgICAgICAgYndMaXN0OiBbXSwvL+mDqOS9jeaVsOaNru+8iOesrOS4gOWIl+W8gOWktO+8iQogICAgICB9XSwKICAgICAgLy/kuIvovb3lvLnlh7rmoYbmjqfliLYKICAgICAgaXNTaG93RG93bkxvYWREaWFsb2c6IGZhbHNlLAogICAgICBwcmludE9iajogewogICAgICAgIGlkOiAicHJldmlld0lkIiwgLy8g5b+F5aGr77yM5riy5p+T5omT5Y2w55qE5YaF5a655L2/55SoCiAgICAgICAgcG9wVGl0bGU6ICImbmJzcDsiLCAvLwogICAgICAgIHByZXZpZXdUaXRsZTogIiZuYnNwOyIsCiAgICAgICAgcHJldmlldzogZmFsc2UsCiAgICAgIH0sCiAgICAgIG1iSW5mbzoge30sCiAgICAgIC8v5omT5Y2w5YaF5a65ZGl25LitaWTlgLwKICAgICAgcHJldmlld0lkOiAiIiwKICAgICAgLy/lrprkuYnmqKHmnb/lhoXlrrnlvLnlh7rmoYbkvKDpgJLlj4LmlbAKICAgICAgbWJSb3dEYXRhOiB7fSwKICAgICAgLy/lrprkuYnmqKHmnb/lhoXlrrnlvLnlh7rmoYYKICAgICAgaXNTaG93WG1HbGJ3RGlhbG9nOiBmYWxzZSwKICAgICAgeG1TZWxlY3RlZEZvcm06IHsKICAgICAgICAvL+ivlemqjOaooeadv2lkCiAgICAgICAgc3ltYmlkOiB1bmRlZmluZWQsCiAgICAgICAgLy/or5Xpqozpobnnm67mlbDmja7pm4blkIgKICAgICAgICB4bURhdGFSb3dzOiBbXQogICAgICB9LAogICAgICAvL+mhueebruW6k+W8ueWHuuahhuagh+mimAogICAgICB4bUxpYnJhcnlBZGREaWFsb2dUaXRsZTogJ+mhueebruW6kycsCiAgICAgIC8v6aG555uu5bqT5by55Ye65qGG5o6n5Yi2CiAgICAgIGlzU2hvd0FkZEdseG1EaWFsb2c6IGZhbHNlLAogICAgICAvL+mhueebruW6k+afpeivouWPguaVsAogICAgICB4bUxpYnJhcnlRdWVyeUZvcm06IHsKICAgICAgICBzeW1iaWQ6IHVuZGVmaW5lZCwKICAgICAgICBzeXhtbWM6ICcnLAogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwCiAgICAgIH0sCiAgICAgIC8v6aG555uu5bqT5pWw5o2uCiAgICAgIHhtTGlicmFyeURhdGFMaXN0OiBbXSwKICAgICAgLy/pobnnm67lupPpobnnm67mgLvmlbAKICAgICAgeG1MaWJyYXJ5VG90YWw6IDAsCiAgICAgIC8v6KGo5Y2V6aqM6K+BCiAgICAgIG1iemJSdWxlczogewogICAgICAgIG1ibWM6IFsKICAgICAgICAgIHtyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeaooeadv+WQjeensCcsIHRyaWdnZXI6ICdibHVyJ30KICAgICAgICBdCiAgICAgIH0sCiAgICAgIC8vIOetm+mAieadoeS7tgogICAgICBmaWx0ZXJJbmZvOiB7CiAgICAgICAgZGF0YTogewogICAgICAgICAgbWJtYzogJycKICAgICAgICB9LAogICAgICAgIGZpZWxkTGlzdDogWwogICAgICAgICAge2xhYmVsOiAn5qih5p2/5ZCN56ewJywgdHlwZTogJ2lucHV0JywgdmFsdWU6ICdtYm1jJywgbXVsdGlwbGU6IHRydWV9CiAgICAgICAgXQogICAgICB9LAogICAgICAvL+aWsOWinuaMiemSruaOp+WItgogICAgICBhZGREaXNhYmxlZDogdHJ1ZSwKICAgICAgLy/liKDpmaTpgInmi6nliJcKICAgICAgc2VsZWN0Um93czogW10sCiAgICAgIC8v6YCJ5Lit55qE5Y2V5p2h5a+56LGhCiAgICAgIHNlbGVjdFJvd0RhdGE6IHt9LAogICAgICAvL+W8ueWHuuahhuihqOWNlQogICAgICBmb3JtOiB7fSwKICAgICAgLy/mn6Xor6Lor5Xpqozpg6jkvY3lj4LmlbAKICAgICAgcXVlcnlTeUJ3UGFyYW06IHsKICAgICAgICBzYmx4aWQ6IHVuZGVmaW5lZCwKICAgICAgICBtYm1jOiB1bmRlZmluZWQsCiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAKICAgICAgfSwKICAgICAgLy/ngrnlh7vmoJHoioLngrnotYvlgLwKICAgICAgdHJlZUZvcm06IHt9LAogICAgICAvL+ivlemqjOmDqOS9jeWIl+ihqAogICAgICB0YWJsZUFuZFBhZ2VJbmZvOiB7CiAgICAgICAgcGFnZXI6IHsKICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgIHNpemVzOiBbMTAsIDIwLCA1MCwgMTAwXQogICAgICAgIH0sCiAgICAgICAgdGFibGVEYXRhOiBbXSwKICAgICAgICB0YWJsZUhlYWRlcjogWwogICAgICAgICAge2xhYmVsOiAn5qih5p2/5ZCN56ewJywgcHJvcDogJ21ibWMnLCBtaW5XaWR0aDogJzEwMCd9LAogICAgICAgICAge2xhYmVsOiAn5piv5ZCm6buY6K6kJywgcHJvcDogJ3NmbXInLCBtaW5XaWR0aDogJzEwMCd9LAogICAgICAgICAge2xhYmVsOiAn5piv5ZCm5YGc55SoJywgcHJvcDogJ3NmdHknLCBtaW5XaWR0aDogJzEwMCd9LAogICAgICAgIF0sCiAgICAgICAgb3B0aW9uOiB7Y2hlY2tCb3g6IHRydWUsIHNlcmlhbE51bWJlcjogdHJ1ZX0KICAgICAgfSwKICAgICAgLy/nu4Tnu4fmoJEKICAgICAgdHJlZU9wdGlvbnM6IFtdLAoKICAgICAgaXNTaG93RGV0YWlsczogZmFsc2UsCiAgICAgIHRpdGxlOiAnJywKCiAgICAgIC8v5Yig6Zmk5piv5ZCm5Y+v55SoCiAgICAgIG11bHRpcGxlU2Vuc29yOiB0cnVlLAogICAgICAvL+afpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIGJtOiB1bmRlZmluZWQsCiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAKICAgICAgfSwKICAgICAgaXNEaXNhYmxlZDogZmFsc2UsCiAgICAgIGlzU2hvdzogdHJ1ZSwKCiAgICAgIC8v5YWz6IGU6aG555uu5by55Ye65qGGdGl0bGUKICAgICAgZ2x4bURpYWxvZ1RpdGxlOiAn5YWz6IGU6aG555uuJywKICAgICAgLy/lhbPogZTpobnnm67lvLnlh7rmoYbmjqfliLblsZXlvIAKICAgICAgaXNHbHhtRGlhbG9nU2hvdzogZmFsc2UsCgogICAgICAvL+WFs+iBlOmhueebrnRvdGFsCiAgICAgIGdseG1Ub3RhbDogMCwKICAgICAgLy/lhbPogZTpobnnm67mn6Xor6Llj4LmlbAKICAgICAgZ2x4bVF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgc3ltYmlkOiB1bmRlZmluZWQsCiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAKICAgICAgfSwKCiAgICAgIC8v5YWz6IGU5a2Q6aG555uudG90YWwKICAgICAgZ2x6eG1Ub3RhbDogMCwKICAgICAgLy/lhbPogZTlrZDpobnnm67mn6Xor6Llj4LmlbAKICAgICAgZ2x6eG1RdWVyeVBhcmFtczogewogICAgICAgIHN5eG1pZDogdW5kZWZpbmVkLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBwYWdlTnVtOiAxCiAgICAgIH0sCgogICAgICAvL+aooeadv+WFs+iBlOmhueebruaVsOaNrgogICAgICBtYkdseG1EYXRhTGlzdDogW10sCiAgICAgIC8v6aG555uu5YWz6IGU55qE5a2Q6aG555uu5pWw5o2uCiAgICAgIHp4bUdsbWJEYXRhTGlzdDogW10sCiAgICAgIC8v5qih5p2/5YWz6IGU6aG555uu6YCJ5Lit5qGG5pWw5o2uCiAgICAgIHNlbGVjdGVkUm93RGF0YUNoYW5nZTogW10sCiAgICAgIC8v5pi+56S66ZOt54mM5by55qGGCiAgICAgIHNob3dNcERpYWxvZzogZmFsc2UsCiAgICAgIC8v6YCJ5Lit6KGM5pWw5o2uCiAgICAgIHJvd0RhdGE6IHt9LAoKICAgICAgLy/lhbPogZTlkI3niYzkvr/liKkKICAgICAgbXBMaXN0OiBbXSwKCiAgICAgIC8v6K+V6aqM5pWw5o2uCiAgICAgIHN5c2pEYXRhTGlzdDogW10sCiAgICAgIC8v6K+V6aqM6KGo5qC86buY6K6k5Zu65a6a55qE6KGMCiAgICAgIGRlZmF1bHRSb3c6IFsi5Luq5Zmo5Z6L5Y+3IiwgIue7k+iuuiIsICLlpIfms6giXSwKICAgIH0KICB9LAogIHdhdGNoOiB7fSwKICBjcmVhdGVkKCkgewogICAgLy/ojrflj5bmlbDmja7liJfooagKICAgIHRoaXMuZ2V0RGF0YSgpCiAgfSwKICBtb3VudGVkKCkgewogIH0sCiAgbWV0aG9kczogewogICAgLy/or5XpqozmlbDmja7ooajmoLzlkIjlubbmlrnms5UKICAgIGFycmF5U3Bhbk1ldGhvZCh7cm93LCBjb2x1bW4sIHJvd0luZGV4LCBjb2x1bW5JbmRleH0pIHsKICAgICAgaWYgKHRoaXMuZGVmYXVsdFJvdy5pbmNsdWRlcyhyb3cuU1lCVykpIHsKICAgICAgICBpZiAoY29sdW1uSW5kZXggPiAwKSB7CiAgICAgICAgICByZXR1cm4gWzEsIHJvdy50b3RhbE51bV0KICAgICAgICB9CiAgICAgIH0KICAgIH0sCiAgICAvL+iuvuWkh+mTreeJjOihqOagvOWQiOW5tuaWueazlQogICAgc2JtcFNwYW5NZXRob2Qoe3JvdywgY29sdW1uLCByb3dJbmRleCwgY29sdW1uSW5kZXh9KSB7CiAgICAgIGlmIChjb2x1bW5JbmRleCA+IDMpIHsKICAgICAgICByZXR1cm4gWzEsIDJdCiAgICAgIH0KICAgIH0sCiAgICAvL+aooeadv+ivpuaDheaMiemSrgogICAgaGFuZGxlTWJJbmZvKHJvdykgewogICAgICAvL+iOt+WPluW9k+WJjeaooeadv2lk5Yqg6L296aG16Z2i5L+h5oGvCiAgICAgIHRoaXMuZ2V0TWJHbE1waW5mb0RhdGEocm93KTsKICAgICAgLy/ojrflj5bor5XpqozmlbDmja4KICAgICAgdGhpcy5nZXRNYkdsWG1BbmRCdyhyb3cpOwogICAgfSwKICAgIC8v5a+85Ye6cGRm5pON5L2cCiAgICBkb3dubG9hZFBkZigpIHsKICAgICAgaHRtbFRvUGRmLmRvd25sb2FkUERGKGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoJyNwcmludENvbnRlbnRJZCcpLCB0aGlzLm1iSW5mby5tYm1jKQogICAgfSwKICAgIC8v6I635Y+W5b2T5YmN5qih5p2/aWTliqDovb3pobXpnaLkv6Hmga8KICAgIGdldE1iR2xNcGluZm9EYXRhKHBhcmFtKSB7CiAgICAgIGdldE1iR2xNcGluZm9EYXRhKHBhcmFtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5tcExpc3QgPSByZXMuZGF0YTsKICAgICAgICAvL+iwg+eUqOa4suafk+mTreeJjOmhtemdouW8gOWniwogICAgICAgIC8vIHRoaXMuYXBwbHlNcEh0bWwodGhpcy5tcExpc3QsIHBhcmFtKTsKICAgICAgICAvL+aJk+W8gOW8ueWHuuahhgogICAgICAgIHRoaXMuaXNTaG93RG93bkxvYWREaWFsb2cgPSB0cnVlOwogICAgICB9KQoKICAgIH0sCiAgICAvL+iOt+WPluivlemqjOaVsOaNrgogICAgZ2V0TWJHbFhtQW5kQncocm93RGF0YSkgewogICAgICAvL+avj+asoeiOt+WPluaVsOaNruWJjeWFiOa4heepuu+8jOWGjea3u+WKoO+8jOWQpuWImeWkmuasoei/m+WFpemhtemdouaXtuS8muiOt+WPlumHjeWkjeaVsOaNrgogICAgICB0aGlzLnN5c2pEYXRhTGlzdCA9IFtdOwogICAgICBnZXRNYkdsWG1BbmRCdyhyb3dEYXRhKS50aGVuKHJlcyA9PiB7CiAgICAgICAgbGV0IHJlc01hcCA9IHJlcy5kYXRhOwogICAgICAgIC8v6YGN5Y6G6L+U5Zue57uT5p6cCiAgICAgICAgZm9yIChsZXQga2V5IGluIHJlc01hcCkgewogICAgICAgICAgLy/op6PmnpDor5XpqozmlbDmja4KICAgICAgICAgIHRoaXMuYW5hbHlzaXNTeURhdGEoa2V5LCByZXNNYXBba2V5XSk7CiAgICAgICAgfQogICAgICAgIC8v55S76K+V6aqM5pWw5o2u6aG16Z2iCiAgICAgICAgdGhpcy5hcHBseVN5c2pEYXRhVG9IdG1sKCk7CiAgICAgIH0pCiAgICB9LAogICAgLy/op6PmnpDlkI7lj7Dor5XpqozmlbDmja4KICAgIGFuYWx5c2lzU3lEYXRhKHN5eG1tYywgenhtQW5kQndEYXRhKSB7CiAgICAgIGxldCBzeXNqRGF0YSA9IHt9CiAgICAgIHN5c2pEYXRhLnN5eG1tYyA9IHN5eG1tYzsKICAgICAgZm9yIChsZXQga2V5IGluIHp4bUFuZEJ3RGF0YVswXSkgewogICAgICAgIHN5c2pEYXRhW2tleV0gPSB6eG1BbmRCd0RhdGFbMF1ba2V5XQogICAgICB9CiAgICAgIHRoaXMuc3lzakRhdGFMaXN0LnB1c2goc3lzakRhdGEpOwogICAgfSwKICAgIC8v5riy5p+T5a6e6aqM5pWw5o2u5Yiw6aG16Z2iCiAgICBhcHBseVN5c2pEYXRhVG9IdG1sKCkgewogICAgICB0aGlzLmFyciA9IFtdOwogICAgICAvLyAkKCcjc3lzalRhYmxlSWQnKS5odG1sKCIiKTsKICAgICAgLy/ov5vooYzmlbDmja7lpITnkIbph43nu4QKICAgICAgbGV0IGRhdGEgPSB0aGlzLnN5c2pEYXRhTGlzdDsKICAgICAgaWYgKGRhdGEubGVuZ3RoID4gMCkgewogICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgZGF0YS5sZW5ndGg7IGkrKykgewogICAgICAgICAgbGV0IGRhdGFDaGlsZCA9IGRhdGFbaV07CiAgICAgICAgICBsZXQgdGl0bGUgPSBkYXRhQ2hpbGQuc3l4bW1jOy8v6K+V6aqM6aG555uu5ZCN56ewCiAgICAgICAgICBsZXQgYndMaXN0ID0gZGF0YUNoaWxkLmJ3TGlzdDsgLy/pg6jkvY1saXN0CiAgICAgICAgICBsZXQgenhtTGlzdCA9IGRhdGFDaGlsZC56eG1MaXN0OyAvL+WtkOmhueebrmxpc3QKICAgICAgICAgIGxldCBoeCA9IFsKICAgICAgICAgICAgewogICAgICAgICAgICAgICJsYWJlbCI6IHRpdGxlLCAvL+esrOS4gOS4quihqOWktOS4uuivlemqjOmhueebruWQjeensAogICAgICAgICAgICAgICJjb2x1bW5fbmFtZSI6ICJTWUJXIiwgLy/nrKzkuIDliJflr7nlupTnmoTlrZfmrrXlkI3vvIjor5Xpqozpg6jkvY3vvIkKICAgICAgICAgICAgfSwKICAgICAgICAgIF07CiAgICAgICAgICB6eG1MaXN0LmZvckVhY2goenhtID0+IHsKICAgICAgICAgICAgaHgucHVzaCh7CiAgICAgICAgICAgICAgImxhYmVsIjogenhtLnN5enhtbWMsIC8v5q+P5YiX55qE6KGo5aS0CiAgICAgICAgICAgICAgImNvbHVtbl9uYW1lIjogIiIsIC8v5q+P5YiX5a+55bqU55qE5pWw5YC85pqC5pe26K6+572u5Li656m655m9CiAgICAgICAgICAgIH0pCiAgICAgICAgICB9KQogICAgICAgICAgbGV0IHN4ID0gW107CiAgICAgICAgICBid0xpc3QuZm9yRWFjaChidyA9PiB7CiAgICAgICAgICAgIHN4LnB1c2goewogICAgICAgICAgICAgICJTWUJXIjogYncuU1lCVywKICAgICAgICAgICAgICAidG90YWxOdW0iOiB6eG1MaXN0Lmxlbmd0aC8v6K6w5b2V5oC75YiX5pWw77yM55So5LqO5Zu65a6a6KGM55qE5ZCI5bm25LqL5Lu2CiAgICAgICAgICAgIH0pCiAgICAgICAgICB9KQogICAgICAgICAgLy/lkI7lm5vooYzlm7rlrpoKICAgICAgICAgIHN4LnB1c2goCiAgICAgICAgICAgIHsKICAgICAgICAgICAgICAiU1lCVyI6ICLnu5PmnpwiLAogICAgICAgICAgICAgICJ0b3RhbE51bSI6IHp4bUxpc3QubGVuZ3RoLy/orrDlvZXmgLvliJfmlbDvvIznlKjkuo7lm7rlrprooYznmoTlkIjlubbkuovku7YKICAgICAgICAgICAgfSwKICAgICAgICAgICAgewogICAgICAgICAgICAgICJTWUJXIjogIuS7quWZqOWei+WPtyIsCiAgICAgICAgICAgICAgInRvdGFsTnVtIjogenhtTGlzdC5sZW5ndGgvL+iusOW9leaAu+WIl+aVsO+8jOeUqOS6juWbuuWumuihjOeahOWQiOW5tuS6i+S7tgogICAgICAgICAgICB9LAogICAgICAgICAgICB7CiAgICAgICAgICAgICAgIlNZQlciOiAi57uT6K66IiwKICAgICAgICAgICAgICAidG90YWxOdW0iOiB6eG1MaXN0Lmxlbmd0aC8v6K6w5b2V5oC75YiX5pWw77yM55So5LqO5Zu65a6a6KGM55qE5ZCI5bm25LqL5Lu2CiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIHsKICAgICAgICAgICAgICAiU1lCVyI6ICLlpIfms6giLAogICAgICAgICAgICAgICJ0b3RhbE51bSI6IHp4bUxpc3QubGVuZ3RoLy/orrDlvZXmgLvliJfmlbDvvIznlKjkuo7lm7rlrprooYznmoTlkIjlubbkuovku7YKICAgICAgICAgICAgfQogICAgICAgICAgKQogICAgICAgICAgdGhpcy5hcnIucHVzaCh7CiAgICAgICAgICAgIHRpdGxlOiB0aXRsZSwKICAgICAgICAgICAgenhtTGlzdDogaHgsCiAgICAgICAgICAgIGJ3TGlzdDogc3gsCiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0KICAgICAgLy/mi7zmjqXnmoTpk63niYzooajmoLwKICAgICAgLyogICAgIGxldCBzdHIgPSAiIjsKICAgICAgICAgICBpZiAodGhpcy5zeXNqRGF0YUxpc3QubGVuZ3RoID4gMCkgewogICAgICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLnN5c2pEYXRhTGlzdC5sZW5ndGg7IGkrKykgewogICAgICAgICAgICAgICAvL+aLvOaOpemhueebruW6j+WPtwogICAgICAgICAgICAgICBsZXQgeG1JbmRleCA9IGkgKyAxOwogICAgICAgICAgICAgICBzdHIgKz0gIjx0cj48dGQgY29sc3Bhbj0nNScgc3R5bGU9J3RleHQtYWxpZ246IGxlZnQ7Zm9udC13ZWlnaHQ6IGJvbGQ7Zm9udC1zaXplOiAxNXB4Jz4iICsgeG1JbmRleCArICLjgIEiICsgdGhpcy5zeXNqRGF0YUxpc3RbaV0uc3l4bW1jICsgIjwvdGQ+PC90cj4iOwogICAgICAgICAgICAgICAvLyB0aGlzLnN5c2pEYXRhTGlzdFtpXS5id0xpc3Q7CiAgICAgICAgICAgICAgIC8vIHRoaXMuc3lzakRhdGFMaXN0W2ldLnp4bUxpc3Q7CiAgICAgICAgICAgICAgIC8vIHN0ciArPSAiPHRyPjx0ZD4iK3RoaXMuc3lzakRhdGFMaXN0W2ldLnN5eG1tYysiPC90ZD48dGQgdi1mb3I9aXRlbSBpbiB0aGlzLnN5c2pEYXRhTGlzdFtpXS5id0xpc3Q+PC90ZD48L3RyPiIKCiAgICAgICAgICAgICB9CiAgICAgICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICAgICAgICQoJyNzeXNqVGFibGVJZCcpLmFwcGVuZChzdHIpCiAgICAgICAgICAgICB9KQogICAgICAgICAgIH0qLwogICAgfSwKICAgIC8v5riy5p+T6ZOt54mM6aG16Z2i5byA5aeLbXBMaXN0OuWPjeWbnueahOmTreeJjOWIl+ihqCAgcm9377ya5qih5p2/6KGM5a+56LGhCiAgICBhcHBseU1wSHRtbChtcExpc3QsIHJvdykgewogICAgICAvL+avj+asoeaJk+W8gOmcgOimgemHjeaWsOa4suafk+S4gOasoSzlhYjlsIbnva7nqboKICAgICAgJCgnI3NibXBUYm9keUlkJykuaHRtbCgiIik7CiAgICAgIC8v5riF56m66YeN5paw6LWL5YC8CiAgICAgIHRoaXMubWJJbmZvID0ge30KICAgICAgdGhpcy5tYkluZm8ubWJtYyA9IHJvdy5tYm1jOwogICAgICAvL+aLvOaOpeeahOmTreeJjOihqOagvAogICAgICBsZXQgc3RyID0gIiI7CiAgICAgIC8v5YWI5Yik5pat5piv5ZCm5YiG55u46ZOt54mMCiAgICAgIGlmIChtcExpc3QubGVuZ3RoID4gMCkgewogICAgICAgIGlmIChtcExpc3RbMF0uU0ZGWCA9PSAnMScpIHsgLy/lvZPliY3pk63niYzkuLrliIbnm7jpk63niYzml7YKICAgICAgICAgIC8v5YaZ5q2756ys5LiA6KGMCiAgICAgICAgICBzdHIgKz0gIjx0cj48dGQgc3R5bGU9J3BhZGRpbmc6IDEwcHg7Zm9udC1zaXplOiAxNXB4Oyc+55u45YirPC90ZD4iICsKICAgICAgICAgICAgIjx0ZCBzdHlsZT0ncGFkZGluZzogMTBweDtmb250LXNpemU6IDE1cHg7Jz5BPC90ZD4iICsKICAgICAgICAgICAgIjx0ZCBzdHlsZT0ncGFkZGluZzogMTBweDtmb250LXNpemU6IDE1cHg7Jz5CPC90ZD4iICsKICAgICAgICAgICAgIjx0ZCBzdHlsZT0ncGFkZGluZzogMTBweDtmb250LXNpemU6IDE1cHg7Jz5DPC90ZD4iICsKICAgICAgICAgICAgIjwvdHI+IjsKICAgICAgICAgIC8v5byA5aeL6YGN5Y6G5bGV56S6CiAgICAgICAgICBmb3IgKGxldCBhID0gMDsgYSA8IG1wTGlzdC5sZW5ndGg7IGErKykgewogICAgICAgICAgICBzdHIgKz0gIjx0cj4iCiAgICAgICAgICAgIHN0ciArPSAiPHRkIHN0eWxlPSdwYWRkaW5nOiAxMHB4O2ZvbnQtc2l6ZTogMTVweDsnPiI7CiAgICAgICAgICAgIHN0ciArPSBtcExpc3RbYV0udGl0bGUgKyAiPC90ZD4iOwogICAgICAgICAgICBzdHIgKz0gIjx0ZD48L3RkPj4iCiAgICAgICAgICAgIHN0ciArPSAiPHRkPjwvdGQ+PiIKICAgICAgICAgICAgc3RyICs9ICI8dGQ+PC90ZD4+IgogICAgICAgICAgICBzdHIgKz0gIjwvdHI+IgogICAgICAgICAgfQogICAgICAgIH0gZWxzZSB7ICAvL+mTreeJjOS4jeWIhuebuAogICAgICAgICAgLy/lvZPliY3pk63niYzkuI3lsZ7kuo7liIbnm7jpk63niYwKICAgICAgICAgIC8v5q+P5YiX5bGV56S65Y2V5YWD5qC85pWw6YePCiAgICAgICAgICBsZXQgY29sID0gMzsKICAgICAgICAgIC8v5bGV56S66KGM5pWwCiAgICAgICAgICB2YXIgbGluZXMgPSBNYXRoLmNlaWwobXBMaXN0Lmxlbmd0aCAvIGNvbCk7CiAgICAgICAgICAvL+mBjeWOhuWxleekuuihjOaVsAogICAgICAgICAgZm9yICh2YXIgaSA9IDA7IGkgPCBsaW5lczsgaSsrKSB7CiAgICAgICAgICAgIHN0ciArPSAiPHRyPiI7CiAgICAgICAgICAgIC8v6YGN5Y6G5YiXCiAgICAgICAgICAgIGZvciAodmFyIGogPSAwOyBqIDwgY29sOyBqKyspIHsKICAgICAgICAgICAgICBpZiAoaSAqIGNvbCArIGogPCBtcExpc3QubGVuZ3RoKSB7CiAgICAgICAgICAgICAgICBzdHIgKz0gIjx0ZCBzdHlsZT0ncGFkZGluZzogMTBweDtmb250LXNpemU6IDE1cHg7Jz4iOwogICAgICAgICAgICAgICAgLy/pk63niYzmoIfpopjotYvlgLwKICAgICAgICAgICAgICAgIHN0ciArPSBtcExpc3RbaSAqIGNvbCArIGpdLnRpdGxlICsgIjwvdGQ+IjsKICAgICAgICAgICAgICAgIC8v6ZOt54mM5YC86LWL5YC8CiAgICAgICAgICAgICAgICBzdHIgKz0gbXBMaXN0W2kgKiBjb2wgKyBqXS5zZm1iID09IDEgPyAiPHRkPiAmbmJzcDsmbmJzcDsmbmJzcDsmbmJzcDsmbmJzcDsmbmJzcDsmbmJzcDsmbmJzcDs8L3RkPiIgOiAiPHRkPiIgKyBtcExpc3RbaSAqIGNvbCArIGpdLmNvbHVtbl9uYW1lICsgIjwvdGQ+IgogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgICBzdHIgKz0gIjwvdHI+IjsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KICAgICAgLy/muLLmn5Ppk63niYzpobXpnaIKICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICQoJyNzYm1wVGJvZHlJZCcpLmFwcGVuZChzdHIpCiAgICAgIH0pCiAgICAgIC8v5omT5byA5by55Ye65qGGCiAgICAgIHRoaXMuaXNTaG93RG93bkxvYWREaWFsb2cgPSB0cnVlOwogICAgfSwKICAgIC8v5YWz6Zet6aKE6KeI5by55Ye65qGGCiAgICBjbG9zZVlsRGlhbG9nKCkgewogICAgICAvL+a4heepuuihqOWNlQogICAgICB0aGlzLm1iSW5mbyA9IHt9OwogICAgICAvL+i1i+WAvOWujOWFs+mXreW8ueeqlwogICAgICB0aGlzLmlzU2hvd0Rvd25Mb2FkRGlhbG9nID0gZmFsc2U7CiAgICB9CiAgICAsCiAgICAvL+WumuS5ieaooeadv+WGheWuuQogICAgaGFuZGxlQ2xpY2tNYm5yKHJvdykgewogICAgICAvL+aJk+W8gOe7hOS7tuW8ueWHuuahhgogICAgICB0aGlzLmlzU2hvd1htR2xid0RpYWxvZyA9IHRydWU7CiAgICAgIC8v57uZ5a2Q57uE5Lu25Lyg6YCS5pWw5o2uCiAgICAgIHRoaXMubWJSb3dEYXRhID0gcm93OwogICAgfQogICAgLAogICAgLy/ojrflj5bpobnnm67lupPpobnnm67mlbDmja4KICAgIGdldFhtTGlyYXJ5RGF0YSgpIHsKICAgICAgZ2V0WG1MaXJhcnlEYXRhKHRoaXMueG1MaWJyYXJ5UXVlcnlGb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy54bUxpYnJhcnlEYXRhTGlzdCA9IHJlcy5kYXRhLnJlY29yZHMKICAgICAgICB0aGlzLnhtTGlicmFyeVRvdGFsID0gcmVzLmRhdGEudG90YWwKICAgICAgfSkKICAgIH0KICAgICwKICAgIC8v6aG555uu5by55Ye65qGG5paw5aKe5oyJ6ZKuCiAgICBhZGRNYkdsWG0oKSB7CiAgICAgIHRoaXMuZ2V0WG1MaXJhcnlEYXRhKCkKICAgICAgdGhpcy5pc1Nob3dBZGRHbHhtRGlhbG9nID0gdHJ1ZQogICAgfQogICAgLAogICAgLy/pobnnm67lupPlvLnlh7rmoYblj5bmtojmjInpkq4KICAgIGNsb3NlQWRkTWp6RGlhbG9nKCkgewogICAgICB0aGlzLmlzU2hvd0FkZEdseG1EaWFsb2cgPSBmYWxzZQogICAgfQogICAgLAogICAgLy/pobnnm67lupPlvLnnqpfnoa7orqTmjInpkq4KICAgIGNvbW1pdEFkZE1qekZvcm0oKSB7CiAgICAgIGlmICh0aGlzLnhtU2VsZWN0ZWRGb3JtLnhtRGF0YVJvd3MubGVuZ3RoIDwgMSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygn5pyq5YWz6IGU6aG555uu77yB77yB77yB5bey5Y+W5raIJykKICAgICAgICAvL+WmguaenOacqumAieS4reaVsOaNrizliJnnm7TmjqXlhbPpl63lvLnnqpcKICAgICAgICB0aGlzLmlzU2hvd0FkZEdseG1EaWFsb2cgPSBmYWxzZQogICAgICB9IGVsc2UgewogICAgICAgIGNvbnNvbGUubG9nKHRoaXMueG1TZWxlY3RlZEZvcm0pCiAgICAgICAgLy/oi6XpgInmi6nmlbDmja7lkI4KICAgICAgICBhZGRNYkdseG1CYXRjaFRvTWJ4bSh0aGlzLnhtU2VsZWN0ZWRGb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICBpZiAocmVzLmNvZGUgPT09ICcwMDAwJykgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WFs+iBlOaIkOWKnycpCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCflhbPogZTlpLHotKXvvIHvvIEnKQogICAgICAgICAgfQogICAgICAgICAgLy/lhbPpl63lvLnnqpcKICAgICAgICAgIHRoaXMuaXNTaG93QWRkR2x4bURpYWxvZyA9IGZhbHNlCiAgICAgICAgICAvL+iwg+eUqOiOt+WPluWFs+iBlOWtkOmhueebruWIl+ihqAogICAgICAgICAgdGhpcy5nZXRTeW1iR2xzeXhtRGF0YUxpc3RCeVBhZ2UoKQogICAgICAgIH0pCiAgICAgIH0KICAgIH0KICAgICwKICAgIC8v6aG555uu5bqT6KGM6YCJ5Lit5LqL5Lu2CiAgICBoYW5kbGVTZWxlY3RlZFhtTGlicmFyeUNoYW5nZShyb3dzKSB7CiAgICAgIHRoaXMueG1TZWxlY3RlZEZvcm0ueG1EYXRhUm93cyA9IHJvd3MKICAgIH0KICAgICwKICAgIC8v6aG555uu5bqT5p+l6K+i5oyJ6ZKuCiAgICBzZWxlY3R4bUxpYnJhcnkoKSB7CiAgICAgIHRoaXMuZ2V0WG1MaXJhcnlEYXRhKCkKICAgIH0KICAgICwKICAgIC8v6aG555uu5bqT6YeN572u5oyJ6ZKuCiAgICByZXNldHhtU2VhcmNoKCkgewogICAgICB0aGlzLnhtTGlicmFyeVF1ZXJ5Rm9ybS5zeXhtbWMgPSAnJwogICAgICB0aGlzLmdldFhtTGlyYXJ5RGF0YSgpCiAgICB9CiAgICAsCiAgICAvL+iOt+WPluWFs+iBlOWtkOWIl+ihqOaWueazlQogICAgZ2V0WnhtRGF0YUxpc3QoKSB7CiAgICAgIGdldEdsU3l6eG1EYXRhTGlzdEJ5UGFnZSh0aGlzLmdsenhtUXVlcnlQYXJhbXMpLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmdsenhtVG90YWwgPSByZXMuZGF0YS50b3RhbAogICAgICAgIHRoaXMuenhtR2xtYkRhdGFMaXN0ID0gcmVzLmRhdGEucmVjb3JkcwogICAgICB9KQogICAgfQogICAgLAoKICAgIC8v5YWz6IGU6aG555uuCiAgICBoYW5kbGVDbGlja0dseG0ocm93KSB7CiAgICAgIC8v5riF56m65Y6f5p2l5a2Q6aG555uu5pWw5o2uCiAgICAgIHRoaXMuenhtR2xtYkRhdGFMaXN0ID0gW10KICAgICAgLy/miZPlvIDlhbPogZTpobnnm67lvLnlh7rmoYYKICAgICAgdGhpcy5pc0dseG1EaWFsb2dTaG93ID0gdHJ1ZQogICAgICAvL+e7meWPguaVsOi1i+WAvAogICAgICB0aGlzLmdseG1RdWVyeVBhcmFtcy5zeW1iaWQgPSByb3cub2JqSWQKICAgICAgLy/mn6Xor6Lpobnnm67lupPmlbDmja7ml7blj4LmlbAKICAgICAgdGhpcy54bUxpYnJhcnlRdWVyeUZvcm0uc3ltYmlkID0gcm93Lm9iaklkCiAgICAgIC8v57uZ6K+V6aqM6aG555uu5bqT5re75Yqg5pe25L2/55SoCiAgICAgIHRoaXMueG1TZWxlY3RlZEZvcm0uc3ltYmlkID0gcm93Lm9iaklkCiAgICAgIC8v6I635Y+W5qih5p2/5YWz6IGU6aG555uu5pWw5o2uCiAgICAgIHRoaXMuZ2V0U3ltYkdsc3l4bURhdGFMaXN0QnlQYWdlKCkKICAgIH0KICAgICwKICAgIC8v6I635Y+W5YWz6IGU6aG555uu5by55Ye65qGG5pWw5o2uCiAgICBnZXRTeW1iR2xzeXhtRGF0YUxpc3RCeVBhZ2UoKSB7CiAgICAgIGdldFN5bWJHbHN5eG1EYXRhTGlzdEJ5UGFnZSh0aGlzLmdseG1RdWVyeVBhcmFtcykudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMubWJHbHhtRGF0YUxpc3QgPSByZXMuZGF0YS5yZWNvcmRzCiAgICAgICAgdGhpcy5nbHhtVG90YWwgPSByZXMuZGF0YS50b3RhbAogICAgICB9KQogICAgfQogICAgLAogICAgLy/or5Xpqozpobnnm67lpI3pgInmoYbngrnlh7vml7bpl7Tngrnlh7vmk43kvZwKICAgIGhhbmRsZUdseG1TZWxlY3RlZENoYW5nZShyb3dzKSB7CiAgICAgIHRoaXMuc2VsZWN0ZWRSb3dEYXRhQ2hhbmdlID0gcm93cwogICAgfQogICAgLAogICAgLy/liKDpmaTmqKHmnb/lhbPogZTpobnnm64KICAgIGRlbGV0ZU1iR2xYbSgpIHsKICAgICAgaWYgKHRoaXMuc2VsZWN0ZWRSb3dEYXRhQ2hhbmdlLmxlbmd0aCA8IDEpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+mAieaLqeato+ehrueahOaVsOaNru+8ge+8ge+8gScpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KICAgICAgbGV0IGlkcyA9IHRoaXMuc2VsZWN0ZWRSb3dEYXRhQ2hhbmdlLm1hcChpdGVtID0+IHsKICAgICAgICByZXR1cm4gaXRlbS5vYmpJZAogICAgICB9KQogICAgICB0aGlzLiRjb25maXJtKCfmraTmk43kvZzlsIbmsLjkuYXliKDpmaTor6XmlbDmja4sIOaYr+WQpue7p+e7rT8nLCAn5o+Q56S6JywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgcmVtb3ZlKGlkcykudGhlbigoe2NvZGV9KSA9PiB7CiAgICAgICAgICBpZiAoY29kZSA9PT0gJzAwMDAnKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICAgICAgICBtZXNzYWdlOiAn5Yig6Zmk5oiQ5YqfIScKICAgICAgICAgICAgfSkKICAgICAgICAgICAgdGhpcy5nZXRTeW1iR2xzeXhtRGF0YUxpc3RCeVBhZ2UoKQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgICAgICAgICBtZXNzYWdlOiAn5Yig6Zmk5aSx6LSlIScKICAgICAgICAgICAgfSkKICAgICAgICAgICAgdGhpcy5nZXRTeW1iR2xzeXhtRGF0YUxpc3RCeVBhZ2UoKQogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgIH0pLmNhdGNoKCgpID0+IHsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgIHR5cGU6ICdpbmZvJywKICAgICAgICAgIG1lc3NhZ2U6ICflt7Llj5bmtojliKDpmaQnCiAgICAgICAgfSkKICAgICAgfSkKICAgIH0sCiAgICAvL+ivlemqjOmhueebrueCueWHu+ihjOaVsOaNruaXtueahOWNleacuuaTjeS9nAogICAgaGFuZGxlTWJHbHhtUm93Q2xpY2socm93KSB7CiAgICAgIHRoaXMuZ2x6eG1RdWVyeVBhcmFtcy5zeXhtaWQgPSByb3cuc3l4bWlkCiAgICAgIHRoaXMuZ2V0WnhtRGF0YUxpc3QoKQogICAgfSwKICAgIC8v5oeS5Yqg6L295Ye95pWwCiAgICBsb2FkTm9kZShub2RlLCByZXNvbHZlKSB7CiAgICAgIGxldCBUcmVlcGFyYW1NYXAgPSB7CiAgICAgICAgcGlkOiAnJywKICAgICAgICBzcGJMb2dvOiBbJ+i+k+eUteiuvuWkhycsICflj5jnlLXorr7lpIcnLCfphY3nlLXorr7lpIcnXQogICAgICB9CiAgICAgIGlmIChub2RlLmxldmVsID09PSAwKSB7CiAgICAgICAgVHJlZXBhcmFtTWFwLnBpZCA9ICdzYicKICAgICAgICByZXR1cm4gdGhpcy5nZXRUcmVlTm9kZShUcmVlcGFyYW1NYXAsIHJlc29sdmUpCiAgICAgIH0KICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgVHJlZXBhcmFtTWFwLnBpZCA9IG5vZGUuZGF0YS5jb2RlCiAgICAgICAgdGhpcy5nZXRUcmVlTm9kZShUcmVlcGFyYW1NYXAsIHJlc29sdmUpCiAgICAgIH0sIDUwMCkKCiAgICB9LAogICAgLy/ojrflj5bmoJHoioLngrnmlbDmja4KICAgIGdldFRyZWVOb2RlKHBhcmFtTWFwLCByZXNvbHZlKSB7CiAgICAgIGdldERldmljZUNsYXNzVHJlZU5vZGVCeVBpZChwYXJhbU1hcCkudGhlbihyZXMgPT4gewogICAgICAgIGxldCB0cmVlTm9kZXMgPSBbXQogICAgICAgIHJlcy5kYXRhLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICBsZXQgbm9kZSA9IHsKICAgICAgICAgICAgbmFtZTogaXRlbS5uYW1lLAogICAgICAgICAgICBsZXZlbDogaXRlbS5sZXZlbCwKICAgICAgICAgICAgaWQ6IGl0ZW0uaWQsCiAgICAgICAgICAgIHBpZDogaXRlbS5waWQsCiAgICAgICAgICAgIGxlYWY6IGZhbHNlLAogICAgICAgICAgICBjb2RlOiBpdGVtLmNvZGUKICAgICAgICAgIH0KICAgICAgICAgIHRyZWVOb2Rlcy5wdXNoKG5vZGUpCiAgICAgICAgfSkKICAgICAgICByZXNvbHZlKHRyZWVOb2RlcykKICAgICAgfSkKICAgIH0sCiAgICAvL+a3u+WKoOWQjuehruiupOS/neWtmOaMiemSrgogICAgc2F2ZSgpIHsKICAgICAgdGhpcy4kcmVmc1snZm9ybSddLnZhbGlkYXRlKCh2YWxpZCkgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgc2F2ZU9yVXBkYXRlKHRoaXMuZm9ybSkudGhlbihyZXMgPT4gewogICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09ICcwMDAwJykgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2VzcyhyZXMubXNnKQogICAgICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci5wYWdlUmVzaXplID0gJ1knCiAgICAgICAgICAgICAgdGhpcy5nZXREYXRhKCkKICAgICAgICAgICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSBmYWxzZQogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1zZykKICAgICAgICAgICAgfQogICAgICAgICAgfSkKCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIC8v5qCR6IqC54K554K55Ye75LqL5Lu2CiAgICBoYW5kbGVOb2RlQ2xpY2soZGF0YSkgewogICAgICBjb25zb2xlLmxvZygn5qCR6IqC54K554K55Ye7JykKICAgICAgY29uc29sZS5sb2coZGF0YSkKICAgICAgaWYgKGRhdGEubGV2ZWwgIT0gJzAnICYmIGRhdGEubGV2ZWwgIT0gJzEnKSB7CiAgICAgICAgLy/mlrDlop7mjInpkq7lj6/ngrnlh7sKICAgICAgICB0aGlzLmFkZERpc2FibGVkID0gZmFsc2UKICAgICAgICB0aGlzLnRyZWVGb3JtID0gZGF0YQogICAgICAgIHRoaXMucXVlcnlTeUJ3UGFyYW0uc2JseGlkID0gZGF0YS5jb2RlCiAgICAgICAgdGhpcy5nZXREYXRhKCkKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmFkZERpc2FibGVkID0gdHJ1ZQogICAgICB9CiAgICB9LAogICAgLy/mt7vliqDmjInpkq4KICAgIGFkZFNlbnNvckJ1dHRvbigpIHsKICAgICAgdGhpcy5mb3JtID0ge30KICAgICAgdGhpcy5mb3JtLnNibHggPSB0aGlzLnRyZWVGb3JtLm5hbWUKICAgICAgdGhpcy5mb3JtLnNibHhpZCA9IHRoaXMudHJlZUZvcm0uY29kZQogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlCiAgICAgIHRoaXMudGl0bGUgPSAn5paw5aKeJwogICAgfSwKICAgIC8v5YiX6KGo5p+l6K+iCiAgICBhc3luYyBnZXREYXRhKHBhcmFtcykgewogICAgICB0cnkgewogICAgICAgIGNvbnN0IHBhcmFtID0gey4uLnRoaXMucXVlcnlTeUJ3UGFyYW0sIC4uLnBhcmFtc30KICAgICAgICBwYXJhbS5zYmx4aWQ9dGhpcy5zeW1iRGF0YS5zYmx4aWQ7CiAgICAgICAgY29uc3Qge2RhdGEsIGNvZGV9ID0gYXdhaXQgZ2V0UGFnZURhdGFMaXN0VG9zeW1iKHBhcmFtKQogICAgICAgIGlmIChjb2RlID09PSAnMDAwMCcpIHsKICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby50YWJsZURhdGEgPSBkYXRhLnJlY29yZHMKICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci50b3RhbCA9IGRhdGEudG90YWwKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICBjb25zb2xlLmxvZyhlKQogICAgICB9CiAgICB9CiAgICAsCiAgICAvKioKICAgICAqIOihqOagvOWkmumAieahhgogICAgICovCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uocm93cykgewogICAgICB0aGlzLnNlbGVjdFJvd3MgPSByb3dzOwogICAgfSwKCiAgICBjbG9zZSgpIHsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gZmFsc2UKICAgIH0KICAgICwKICAgIC8v5L+u5pS55qih5p2/5Li76KGo5YaF5a65CiAgICB1cGRhdGVEZXRhaWxzKHJvdykgewogICAgICB0aGlzLnRpdGxlID0gJ+S/ruaUuScKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gdHJ1ZQogICAgICB0aGlzLmZvcm0gPSByb3cKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gZmFsc2UKICAgICAgdGhpcy5pc1Nob3cgPSB0cnVlCiAgICB9CiAgICAsCgogICAgY3JlYXRlVGVtcGxhdGUocm93KSB7CiAgICAgIGNvbnNvbGUubG9nKHJvdykKICAgIH0KICAgICwKICAgIC8v5p+l55yL5qih5p2/5Li76KGo6K+m5oOF5oyJ6ZKuCiAgICBnZXREZXRhaWxzKHJvdykgewogICAgICB0aGlzLnRpdGxlID0gJ+ivpuaDhScKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gdHJ1ZQogICAgICB0aGlzLmZvcm0gPSByb3cKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gdHJ1ZQogICAgICB0aGlzLmlzU2hvdyA9IGZhbHNlCiAgICB9CiAgICAsCgogICAgLy/liKDpmaTmjInpkq4KICAgIGRlbGV0ZVNlbnNvckJ1dHRvbigpIHsKICAgICAgaWYgKHRoaXMuc2VsZWN0Um93cy5sZW5ndGggPCAxKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fpgInmi6nmraPnoa7nmoTmlbDmja7vvIHvvIHvvIEnKQogICAgICAgIHJldHVybgogICAgICB9CiAgICAgIGxldCBpZHMgPSB0aGlzLnNlbGVjdFJvd3MubWFwKGl0ZW0gPT4gewogICAgICAgIHJldHVybiBpdGVtLm9iaklkCiAgICAgIH0pCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+atpOaTjeS9nOWwhuawuOS5heWIoOmZpOivpeaVsOaNriwg5piv5ZCm57un57utPycsICfmj5DnpLonLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICfnoa7lrponLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICflj5bmtognLAogICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICByZW1vdmUoaWRzKS50aGVuKCh7Y29kZX0pID0+IHsKICAgICAgICAgIGlmIChjb2RlID09PSAnMDAwMCcpIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgICAgIG1lc3NhZ2U6ICfliKDpmaTmiJDlip8hJwogICAgICAgICAgICB9KQogICAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIucGFnZVJlc2l6ZSA9ICdZJwogICAgICAgICAgICB0aGlzLmdldERhdGEoKQogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgdHlwZTogJ2Vycm9yJywKICAgICAgICAgICAgICBtZXNzYWdlOiAn5Yig6Zmk5aSx6LSlIScKICAgICAgICAgICAgfSkKICAgICAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnBhZ2VSZXNpemUgPSAnWScKICAgICAgICAgICAgdGhpcy5nZXREYXRhKCkKICAgICAgICAgIH0KICAgICAgICB9KQogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICB0eXBlOiAnaW5mbycsCiAgICAgICAgICBtZXNzYWdlOiAn5bey5Y+W5raI5Yig6ZmkJwogICAgICAgIH0pCiAgICAgIH0pCgogICAgfQogICAgLAogICAgLy/lr7zlh7rmjInpkq4KICAgIGhhbmRsZUV4cG9ydCgpIHsKCiAgICB9CiAgICAsCgogICAgLy/lhbPogZTpk63niYzngrnlh7vkuovku7YKICAgIGhhbmRsZUNsaWNrR2xNcChyb3cpIHsKICAgICAgdGhpcy5zaG93TXBEaWFsb2cgPSB0cnVlCiAgICAgIHRoaXMucm93RGF0YSA9IHJvdwogICAgfQogICAgLAogICAgLy/lhbPpl63or5Xpqozpk63niYzlvLnnqpcKICAgIGNsb3NlTXBEaWFsb2coKSB7CiAgICAgIHRoaXMuc2hvd01wRGlhbG9nID0gZmFsc2UKICAgIH0KICAgICwKCiAgICBmaWx0ZXJSZXNldCgpIHsKICAgIH0sCgogICAgLy/lhbPpl63or5XpqozmqKHmnb/lvLnnqpcKICAgIGNsb3NlU3ltYkNvbW1lbnQoKSB7CiAgICAgIHRoaXMuJGVtaXQoImNsb3NlU3ltYlNlbGVjdERpYWxvZyIsIGZhbHNlKQogICAgfSwKICAgIC8v54K55Ye756Gu6K6k5ZCO57uZ54i257uE5Lu25Lyg6YCS5pWw5o2uCiAgICAvLyB0aGlzLnNlbGVjdFJvd0RhdGEgIT0gdW5kZWZpbmVkICYmIEpTT04uc3RyaW5naWZ5KHRoaXMuc2VsZWN0Um93RGF0YSkgIT0gInt9IgogICAgY29tbWl0TWJkYXRhKCkgewogICAgICBpZiAodGhpcy5zZWxlY3RSb3dzLmxlbmd0aCA9PSAxICYmIHRoaXMuc2VsZWN0Um93cyAhPSB1bmRlZmluZWQpIHsKICAgICAgIHRoaXMuJGVtaXQoImhhbmRsZUFjY2VwdE1iRGF0YSIsdGhpcy5zZWxlY3RSb3dzWzBdKTsKICAgICAgICB0aGlzLiRlbWl0KCJjbG9zZVN5bWJTZWxlY3REaWFsb2ciLCBmYWxzZSkKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+mAieaLqeS4gOadoeaVsOaNriIpCiAgICAgIH0KICAgIH0sCgogIH0KfQo="}, {"version": 3, "sources": ["symbSyxmSelect.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2bA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "symbSyxmSelect.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk/sybgjlcomment", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row>\n      <!--右侧列表-->\n      <el-col :span=\"24\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 140, itemWidth: 190 }\"\n          @handleReset=\"filterReset\"\n        />\n        <el-white>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            :is-single=\"true\"\n            height=\"61.5vh\">\n\n            <!--            <el-table-column slot=\"table_six\" align=\"center\" label=\"关联铭牌\" min-width=\"150\" :resizable=\"false\">-->\n            <!--              <template slot-scope=\"scope\">-->\n            <!--                <el-button @click=\"handleClickGlMp(scope.row)\" type=\"text\" size=\"small\" v-if=\"scope.row.sfty == '是'\">-->\n            <!--                  查看铭牌-->\n            <!--                </el-button>-->\n            <!--                <el-button @click=\"handleClickGlMp(scope.row)\" type=\"text\" size=\"small\" v-else>关联铭牌</el-button>-->\n            <!--              </template>-->\n            <!--            </el-table-column>-->\n\n            <!--            <el-table-column slot=\"table_seven\" align=\"center\" label=\"关联项目\" min-width=\"150\" :resizable=\"false\">-->\n            <!--              <template slot-scope=\"scope\">-->\n            <!--                <el-button @click=\"handleClickGlxm(scope.row)\" type=\"text\" size=\"small\" v-if=\"scope.row.sfty == '是'\">-->\n            <!--                  查看项目-->\n            <!--                </el-button>-->\n            <!--                <el-button @click=\"handleClickGlxm(scope.row)\" type=\"text\" size=\"small\" v-else>关联项目</el-button>-->\n            <!--              </template>-->\n            <!--            </el-table-column>-->\n\n            <!--            <el-table-column slot=\"table_eight\" align=\"center\" label=\"定义模板内容\" min-width=\"150\" :resizable=\"false\">-->\n            <!--              <template slot-scope=\"scope\">-->\n            <!--                <el-button @click=\"handleClickMbnr(scope.row)\" type=\"text\" size=\"small\" v-if=\"scope.row.sfty == '是'\">-->\n            <!--                  查看模板内容-->\n            <!--                </el-button>-->\n            <!--                <el-button @click=\"handleClickMbnr(scope.row)\" type=\"text\" size=\"small\" v-else>定义模板内容</el-button>-->\n            <!--              </template>-->\n            <!--            </el-table-column>-->\n\n            <el-table-column slot=\"table_eight\" align=\"center\" label=\"模板详情\" min-width=\"150\" :resizable=\"false\">\n              <template slot-scope=\"scope\">\n                <el-button v-print=\"printObj\" type=\"text\" size=\"small\" @click=\"handleMbInfo(scope.row)\">模板详情</el-button>\n              </template>\n            </el-table-column>\n\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n    <el-row>\n      <div style=\"text-align: right;margin-top: 2vh\">\n        <el-button @click=\"closeSymbComment\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"commitMbdata\">确 定</el-button>\n      </div>\n    </el-row>\n    <!--新增\\修改\\详情弹出-->\n    <el-dialog :title=title :visible.sync=\"isShowDetails\" width=\"60%\" v-dialogDrag>\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"mbzbRules\">\n        <el-row :gutter=\"8\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"设备类型：\" prop=\"sblx\">\n              <el-input v-model=\"form.sblx\" disabled style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"设备类型编码：\" prop=\"sblxbm\">\n              <el-input v-model=\"form.sblxid\" disabled style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"模板名称：\" prop=\"mbmc\">\n              <el-input placeholder=\"请输入试验部位名称\" v-model=\"form.mbmc\" :disabled=\"isDisabled\"\n                        style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否默认：\" prop=\"sfmr\">\n              <el-select v-model=\"form.sfmr\" style=\"width: 100%\" :disabled=\"isDisabled\">\n                <el-option label=\"是\" value=\"是\"></el-option>\n                <el-option label=\"否\" value=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否停用：\" prop=\"sfty\">\n              <el-select v-model=\"form.sfty\" style=\"width: 100%\" :disabled=\"isDisabled\">\n                <el-option label=\"是\" value=\"是\"></el-option>\n                <el-option label=\"否\" value=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" v-show=\"isShow\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"save\">确 认</el-button>\n      </div>\n    </el-dialog>\n\n    <!--关联铭牌弹框-->\n    <el-dialog :visible.sync=\"showMpDialog\" title=\"已关联铭牌\" v-if=\"showMpDialog\" v-dialogDrag>\n      <glsymp :main-data=\"rowData\" :tree-data=\"treeForm\" @closeMpDialog=\"closeMpDialog\"></glsymp>\n    </el-dialog>\n\n    <!--关联试验项目弹出框-->\n    <el-dialog :title=\"glxmDialogTitle\" :visible.sync=\"isGlxmDialogShow\" width=\"60%\" v-dialogDrag>\n      <el-row :gutter=\"3\">\n        <div class=\"mb8 pull-right\">\n          <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"mini\" @click=\"addMbGlXm\">新增项目</el-button>\n          <el-button type=\"danger\" icon=\"el-icon-delete\" size=\"mini\" @click=\"deleteMbGlXm\">删除项目</el-button>\n        </div>\n      </el-row>\n      <el-row :gutter=\"20\">\n        <el-col :span=\"12\">\n          <el-table :data=\"mbGlxmDataList\" @selection-change=\"handleGlxmSelectedChange\"\n                    @row-click=\"handleMbGlxmRowClick\">\n            <el-table-column label=\"试验项目\" align=\"center\">\n              <el-table-column type=\"selection\" width=\"55\" align=\"center\"/>\n              <el-table-column label=\"序号\" type=\"index\" width=\"50\" align=\"center\"></el-table-column>\n              <el-table-column prop=\"syxmmc\" label=\"项目名称\" width=\"180\" align=\"center\"></el-table-column>\n              <el-table-column prop=\"syxmms\" label=\"项目描述\" align=\"center\"></el-table-column>\n            </el-table-column>\n          </el-table>\n          <pagination\n            :total=\"glxmTotal\"\n            :page.sync=\"glxmQueryParams.pageNum\"\n            :limit.sync=\"glxmQueryParams.pageSize\"\n            @pagination=\"getSymbGlsyxmDataListByPage\"\n          />\n        </el-col>\n        <el-col :span=\"12\">\n          <el-table :data=\"zxmGlmbDataList\">\n            <el-table-column label=\"试验子项目\" align=\"center\">\n              <el-table-column prop=\"syzxmmc\" label=\"子项目名称\" width=\"180\" align=\"center\"></el-table-column>\n              <el-table-column prop=\"syzxmms\" label=\"子项目描述\" align=\"center\"></el-table-column>\n            </el-table-column>\n          </el-table>\n          <pagination\n            :total=\"glzxmTotal\"\n            :page.sync=\"glzxmQueryParams.pageNum\"\n            :limit.sync=\"glzxmQueryParams.pageSize\"\n            @pagination=\"getZxmDataList\"\n          />\n        </el-col>\n      </el-row>\n\n    </el-dialog>\n    <!--列表新增关联项目弹窗调用-->\n    <el-dialog :title=\"xmLibraryAddDialogTitle\" :visible.sync=\"isShowAddGlxmDialog\" width=\"50%\" v-dialogDrag>\n      <el-form label-width=\"120px\">\n        <el-row :gutter=\"3\">\n          <el-col :span=\"10\">\n            <el-form-item label=\"项目名称：\">\n              <el-input v-model=\"xmLibraryQueryForm.syxmmc\"/>\n            </el-form-item>\n          </el-col>\n          <div class=\"mb8 pull-right\">\n            <el-button type=\"cyan\" size=\"mini\" icon=\"el-icon-search\" @click=\"selectxmLibrary\">查询</el-button>\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetxmSearch\">重置</el-button>\n          </div>\n        </el-row>\n      </el-form>\n      <el-table stripe border :data=\"xmLibraryDataList\" @selection-change=\"handleSelectedXmLibraryChange\"\n                :header-cell-style=\"{'text-align':'center'}\" :cell-style=\"{'text-align':'center'}\">\n        <el-table-column type=\"selection\" width=\"55\"/>\n        <el-table-column label=\"序号\" type=\"index\" width=\"50\">\n        </el-table-column>\n        <el-table-column label=\"试验项目\" prop=\"syxmmc\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"项目描述\" prop=\"syxmms\" :show-overflow-tooltip=\"true\"/>\n      </el-table>\n      <pagination\n        v-show=\"xmLibraryTotal>0\"\n        :total=\"xmLibraryTotal\"\n        :page.sync=\"xmLibraryQueryForm.pageNum\"\n        :limit.sync=\"xmLibraryQueryForm.pageSize\"\n        @pagination=\"getXmLiraryData\"\n      />\n      <div slot=\"footer\">\n        <el-button @click=\"closeAddMjzDialog\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"commitAddMjzForm\">确 认</el-button>\n      </div>\n    </el-dialog>\n    <!--子组件定义模板内容-->\n    <el-dialog title=\"项目关联部位\" :visible.sync=\"isShowXmGlbwDialog\" v-if=\"isShowXmGlbwDialog\" v-dialogDrag>\n      <symbwh-dymbnr ref=\"symbwhDymbnrRef\" :mb-data=\"mbRowData\"></symbwh-dymbnr>\n    </el-dialog>\n    <!--  打印vue print nb插件-->\n    <div v-show=\"false\">\n      <div id=\"printHtmlId\" style=\"background:white;\">\n        <!--模板-->\n        <div style=\"text-align: center\">\n          <p>{{mbInfo.mbmc}}</p>\n        </div>\n        <p>葫芦娃，葫芦娃</p>\n        <p>一根藤上七朵花 </p>\n        <p>小小树藤是我家 啦啦啦啦 </p>\n        <p>叮当当咚咚当当　浇不大</p>\n        <p> 叮当当咚咚当当 是我家</p>\n        <p> 啦啦啦啦</p>\n        <p>...</p>\n        <div class=\"describle\">\n          <el-form :model=\"mbInfo\" :rules=\"mbzbRules\" ref=\"from\" class=\"demo-ruleForm\">\n            <el-form-item label=\"姓名:\" prop=\"name\">\n              <el-input v-model=\"mbInfo.mbmc\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"描述:\" prop=\"describle\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"4\"\n                :maxlength=\"2000\"\n                placeholder=\"\"\n                v-model=\"mbInfo.mbmc\">\n              </el-input>\n            </el-form-item>\n          </el-form>\n        </div>\n      </div>\n    </div>\n    <!--htmlToPdf插件-->\n    <el-dialog title=\"预览\" :visible.sync=\"isShowDownLoadDialog\" width=\"60%\" append-to-body v-dialogDrag>\n      <el-button @click=\"downloadPdf\">导出</el-button>\n      <div style=\"width: 100%;height:60vh;overflow: auto\">\n        <div id=\"printContentId\">\n          <div style=\"text-align: center\">\n            <h2>{{mbInfo.mbmc}}</h2></div>\n          <div>\n            <div\n              style=\"font-size: 20px;float: left;width:100%;border-left: 1px solid #000;border-right: 1px solid #000;border-top: 1px solid #000;\">\n              一、基本信息\n            </div>\n            <el-table\n              :data=\"tableData_jbxx\"\n              border\n              style=\"border: 1px solid #000;\"\n            >\n              <el-table-column\n                prop=\"column_1\"\n                label=\"变电站\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_5\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_2\"\n                label=\"委托单位\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_6\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_3\"\n                label=\"试验单位\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_7\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_4\"\n                label=\"运行编号\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_8\"\n                label=\"\"\n              >\n              </el-table-column>\n            </el-table>\n            <!--            <tbody>\n                        <tr>\n                          <td>变电站</td>\n                          <td></td>\n                          <td>委托单位</td>\n                          <td></td>\n                          <td>试验单位</td>\n                          <td></td>\n                          <td>运行编号</td>\n                          <td></td>\n                        </tr>\n                        <tr>\n                          <td>试验性质</td>\n                          <td></td>\n                          <td>试验日期</td>\n                          <td></td>\n                          <td>试验人员</td>\n                          <td></td>\n                          <td>试验地点</td>\n                          <td></td>\n                        </tr>\n                        <tr>\n                          <td>报告日期</td>\n                          <td></td>\n                          <td>编写人</td>\n                          <td></td>\n                          <td>审核人</td>\n                          <td></td>\n                          <td>批准人</td>\n                          <td></td>\n                        </tr>\n                        <tr>\n                          <td>试验天气</td>\n                          <td></td>\n                          <td>环境温度（℃）</td>\n                          <td></td>\n                          <td>环境相对湿度（%）</td>\n                          <td></td>\n                          <td>投运日期</td>\n                          <td></td>\n                        </tr>\n                        </tbody>-->\n          </div>\n          <div>\n            <div\n              style=\"font-size: 20px;float: left;width:100%;border-left: 1px solid #000;border-right: 1px solid #000;\">\n              二、设备铭牌\n            </div>\n            <el-table\n              :data=\"tableData_sbmp\"\n              border\n              :span-method=\"sbmpSpanMethod\"\n              style=\"border: 1px solid #000;\"\n            >\n              <el-table-column\n                prop=\"column_1\"\n                label=\"生产厂家\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_4\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_2\"\n                label=\"出厂编号\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_5\"\n                label=\"\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_3\"\n                label=\"出厂日期\"\n              >\n              </el-table-column>\n              <el-table-column\n                prop=\"column_6\"\n                label=\"\"\n              >\n              </el-table-column>\n            </el-table>\n            <!--            <tbody id=\"sbmpTbodyId\">\n                        </tbody>-->\n          </div>\n          <div>\n            <div\n              style=\"font-size: 20px;float: left;width:100%;border-left: 1px solid #000;border-right: 1px solid #000;border-bottom: 1px solid #000;\">\n              三、试验数据\n            </div>\n            <div v-for=\"item in arr\" style=\"width: 100%\">\n              <div class=\"printTitle\">{{item.title}}</div>\n              <el-table :data=\"item.bwList\" style=\"width:100%;border: 1px solid #000;\" border\n                        :span-method=\"arraySpanMethod\">\n                <template v-for='(val) in item.zxmList'>\n                  <el-table-column\n                    :prop=\"val.column_name\"\n                    width=\"auto\"\n                    :label=\"val.label\"\n                  >\n                  </el-table-column>\n\n                </template>\n\n              </el-table>\n            </div>\n\n            <!--            <tbody id=\"sysjTableId\">\n                        <tr>\n                          <td colspan=\"5\" style=\"text-align: left;font-weight: bold\">12121212</td>\n                        </tr>\n                        <tr>\n                          <td>部位</td>\n                          <td>回路电阻初值(μΩ)</td>\n                          <td>回路电阻(μΩ)</td>\n                          <td>主回路电阻初值差(%)</td>\n                          <td>是否合格</td>\n                        </tr>\n                        <tr>\n                          <td>部位1</td>\n                          <td></td>\n                          <td></td>\n                          <td></td>\n                          <td></td>\n                        </tr>\n                        <tr>\n                          <td>部位2</td>\n                          <td></td>\n                          <td></td>\n                          <td></td>\n                          <td></td>\n                        </tr>\n                        <tr>\n                          <td>仪器型号</td>\n                          <td colspan=\"4\"></td>\n                        </tr>\n                        <tr>\n                          <td>结论</td>\n                          <td colspan=\"4\"></td>\n                        </tr>\n                        <tr>\n                          <td>备注</td>\n                          <td colspan=\"4\"></td>\n                        </tr>\n                        </tbody>-->\n          </div>\n        </div>\n      </div>\n      <div slot=\"footer\">\n        <el-button @click=\"closeYlDialog\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  //引入jquery,暂时没用\n  import $ from \"jquery\"\n  import {\n    addMbGlxmBatchToMbxm,\n    getPageDataListTosymb,\n    getSymbGlsyxmDataListByPage,\n    getXmLiraryData,\n    remove,\n    saveOrUpdate,\n    getMbGlMpinfoData,\n    getMbGlXmAndBw\n  } from '@/api/dagangOilfield/bzgl/symbwh'\n  import {getDeviceClassTreeNodeByPid} from '@/api/dagangOilfield/bzgl/sblxwh/sblxwh'\n  import {getGlSyzxmDataListByPage} from '@/api/dagangOilfield/bzgl/syxm'\n  import Glsymp from '@/views/dagangOilfield/bzgl/sybzk/glsymp'\n  import symbwhDymbnr from '@/views/dagangOilfield/bzgl/sybzk/symbwhDymbnr'\n  import htmlToPdf from '@/utils/print/htmlToPdf'\n\n  export default {\n    name: 'symbSyxmSelect',\n    components: {Glsymp, symbwhDymbnr},\n     props: {\n      //组件传值\n      symbData: {\n        type: Object,\n        default: () => ({\n          sblxid:'',  \n        })\n      },\n\n    },\n    data() {\n      return {\n        //基本信息表格数据\n        tableData_jbxx: [\n          {\n            'column_1': '试验性质',\n            'column_2': '试验日期',\n            'column_3': '试验人员',\n            'column_4': '试验地点',\n            'column_5': '',\n            'column_6': '',\n            'column_7': '',\n            'column_8': '',\n          },\n          {\n            'column_1': '报告日期',\n            'column_2': '编写人',\n            'column_3': '审核人',\n            'column_4': '批准人',\n            'column_5': '',\n            'column_6': '',\n            'column_7': '',\n            'column_8': '',\n          },\n          {\n            'column_1': '试验天气',\n            'column_2': '环境温度（℃）',\n            'column_3': '环境相对湿度（%）',\n            'column_4': '投运日期',\n            'column_5': '',\n            'column_6': '',\n            'column_7': '',\n            'column_8': '',\n          }\n        ],\n        //设备铭牌表格数据\n        tableData_sbmp: [{\n          'column_1': '额定电压',\n          'column_2': '设备型号',\n          'column_3': '',\n          'column_4': '',\n          'column_5': '',\n          'column_6': '',\n        },],\n        //要循环的试验表格数据\n        arr: [{\n          title: \"\",//试验名称\n          zxmList: [],//子项目数据（表头）\n          bwList: [],//部位数据（第一列开头）\n        }],\n        //下载弹出框控制\n        isShowDownLoadDialog: false,\n        printObj: {\n          id: \"previewId\", // 必填，渲染打印的内容使用\n          popTitle: \"&nbsp;\", //\n          previewTitle: \"&nbsp;\",\n          preview: false,\n        },\n        mbInfo: {},\n        //打印内容div中id值\n        previewId: \"\",\n        //定义模板内容弹出框传递参数\n        mbRowData: {},\n        //定义模板内容弹出框\n        isShowXmGlbwDialog: false,\n        xmSelectedForm: {\n          //试验模板id\n          symbid: undefined,\n          //试验项目数据集合\n          xmDataRows: []\n        },\n        //项目库弹出框标题\n        xmLibraryAddDialogTitle: '项目库',\n        //项目库弹出框控制\n        isShowAddGlxmDialog: false,\n        //项目库查询参数\n        xmLibraryQueryForm: {\n          symbid: undefined,\n          syxmmc: '',\n          pageNum: 1,\n          pageSize: 10\n        },\n        //项目库数据\n        xmLibraryDataList: [],\n        //项目库项目总数\n        xmLibraryTotal: 0,\n        //表单验证\n        mbzbRules: {\n          mbmc: [\n            {required: true, message: '请输入模板名称', trigger: 'blur'}\n          ]\n        },\n        // 筛选条件\n        filterInfo: {\n          data: {\n            mbmc: ''\n          },\n          fieldList: [\n            {label: '模板名称', type: 'input', value: 'mbmc', multiple: true}\n          ]\n        },\n        //新增按钮控制\n        addDisabled: true,\n        //删除选择列\n        selectRows: [],\n        //选中的单条对象\n        selectRowData: {},\n        //弹出框表单\n        form: {},\n        //查询试验部位参数\n        querySyBwParam: {\n          sblxid: undefined,\n          mbmc: undefined,\n          pageNum: 1,\n          pageSize: 10\n        },\n        //点击树节点赋值\n        treeForm: {},\n        //试验部位列表\n        tableAndPageInfo: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          tableData: [],\n          tableHeader: [\n            {label: '模板名称', prop: 'mbmc', minWidth: '100'},\n            {label: '是否默认', prop: 'sfmr', minWidth: '100'},\n            {label: '是否停用', prop: 'sfty', minWidth: '100'},\n          ],\n          option: {checkBox: true, serialNumber: true}\n        },\n        //组织树\n        treeOptions: [],\n\n        isShowDetails: false,\n        title: '',\n\n        //删除是否可用\n        multipleSensor: true,\n        //查询参数\n        queryParams: {\n          bm: undefined,\n          pageNum: 1,\n          pageSize: 10\n        },\n        isDisabled: false,\n        isShow: true,\n\n        //关联项目弹出框title\n        glxmDialogTitle: '关联项目',\n        //关联项目弹出框控制展开\n        isGlxmDialogShow: false,\n\n        //关联项目total\n        glxmTotal: 0,\n        //关联项目查询参数\n        glxmQueryParams: {\n          symbid: undefined,\n          pageNum: 1,\n          pageSize: 10\n        },\n\n        //关联子项目total\n        glzxmTotal: 0,\n        //关联子项目查询参数\n        glzxmQueryParams: {\n          syxmid: undefined,\n          pageSize: 10,\n          pageNum: 1\n        },\n\n        //模板关联项目数据\n        mbGlxmDataList: [],\n        //项目关联的子项目数据\n        zxmGlmbDataList: [],\n        //模板关联项目选中框数据\n        selectedRowDataChange: [],\n        //显示铭牌弹框\n        showMpDialog: false,\n        //选中行数据\n        rowData: {},\n\n        //关联名牌便利\n        mpList: [],\n\n        //试验数据\n        sysjDataList: [],\n        //试验表格默认固定的行\n        defaultRow: [\"仪器型号\", \"结论\", \"备注\"],\n      }\n    },\n    watch: {},\n    created() {\n      //获取数据列表\n      this.getData()\n    },\n    mounted() {\n    },\n    methods: {\n      //试验数据表格合并方法\n      arraySpanMethod({row, column, rowIndex, columnIndex}) {\n        if (this.defaultRow.includes(row.SYBW)) {\n          if (columnIndex > 0) {\n            return [1, row.totalNum]\n          }\n        }\n      },\n      //设备铭牌表格合并方法\n      sbmpSpanMethod({row, column, rowIndex, columnIndex}) {\n        if (columnIndex > 3) {\n          return [1, 2]\n        }\n      },\n      //模板详情按钮\n      handleMbInfo(row) {\n        //获取当前模板id加载页面信息\n        this.getMbGlMpinfoData(row);\n        //获取试验数据\n        this.getMbGlXmAndBw(row);\n      },\n      //导出pdf操作\n      downloadPdf() {\n        htmlToPdf.downloadPDF(document.querySelector('#printContentId'), this.mbInfo.mbmc)\n      },\n      //获取当前模板id加载页面信息\n      getMbGlMpinfoData(param) {\n        getMbGlMpinfoData(param).then(res => {\n          this.mpList = res.data;\n          //调用渲染铭牌页面开始\n          // this.applyMpHtml(this.mpList, param);\n          //打开弹出框\n          this.isShowDownLoadDialog = true;\n        })\n\n      },\n      //获取试验数据\n      getMbGlXmAndBw(rowData) {\n        //每次获取数据前先清空，再添加，否则多次进入页面时会获取重复数据\n        this.sysjDataList = [];\n        getMbGlXmAndBw(rowData).then(res => {\n          let resMap = res.data;\n          //遍历返回结果\n          for (let key in resMap) {\n            //解析试验数据\n            this.analysisSyData(key, resMap[key]);\n          }\n          //画试验数据页面\n          this.applySysjDataToHtml();\n        })\n      },\n      //解析后台试验数据\n      analysisSyData(syxmmc, zxmAndBwData) {\n        let sysjData = {}\n        sysjData.syxmmc = syxmmc;\n        for (let key in zxmAndBwData[0]) {\n          sysjData[key] = zxmAndBwData[0][key]\n        }\n        this.sysjDataList.push(sysjData);\n      },\n      //渲染实验数据到页面\n      applySysjDataToHtml() {\n        this.arr = [];\n        // $('#sysjTableId').html(\"\");\n        //进行数据处理重组\n        let data = this.sysjDataList;\n        if (data.length > 0) {\n          for (let i = 0; i < data.length; i++) {\n            let dataChild = data[i];\n            let title = dataChild.syxmmc;//试验项目名称\n            let bwList = dataChild.bwList; //部位list\n            let zxmList = dataChild.zxmList; //子项目list\n            let hx = [\n              {\n                \"label\": title, //第一个表头为试验项目名称\n                \"column_name\": \"SYBW\", //第一列对应的字段名（试验部位）\n              },\n            ];\n            zxmList.forEach(zxm => {\n              hx.push({\n                \"label\": zxm.syzxmmc, //每列的表头\n                \"column_name\": \"\", //每列对应的数值暂时设置为空白\n              })\n            })\n            let sx = [];\n            bwList.forEach(bw => {\n              sx.push({\n                \"SYBW\": bw.SYBW,\n                \"totalNum\": zxmList.length//记录总列数，用于固定行的合并事件\n              })\n            })\n            //后四行固定\n            sx.push(\n              {\n                \"SYBW\": \"结果\",\n                \"totalNum\": zxmList.length//记录总列数，用于固定行的合并事件\n              },\n              {\n                \"SYBW\": \"仪器型号\",\n                \"totalNum\": zxmList.length//记录总列数，用于固定行的合并事件\n              },\n              {\n                \"SYBW\": \"结论\",\n                \"totalNum\": zxmList.length//记录总列数，用于固定行的合并事件\n              },\n              {\n                \"SYBW\": \"备注\",\n                \"totalNum\": zxmList.length//记录总列数，用于固定行的合并事件\n              }\n            )\n            this.arr.push({\n              title: title,\n              zxmList: hx,\n              bwList: sx,\n            });\n          }\n        }\n        //拼接的铭牌表格\n        /*     let str = \"\";\n             if (this.sysjDataList.length > 0) {\n               for (let i = 0; i < this.sysjDataList.length; i++) {\n                 //拼接项目序号\n                 let xmIndex = i + 1;\n                 str += \"<tr><td colspan='5' style='text-align: left;font-weight: bold;font-size: 15px'>\" + xmIndex + \"、\" + this.sysjDataList[i].syxmmc + \"</td></tr>\";\n                 // this.sysjDataList[i].bwList;\n                 // this.sysjDataList[i].zxmList;\n                 // str += \"<tr><td>\"+this.sysjDataList[i].syxmmc+\"</td><td v-for=item in this.sysjDataList[i].bwList></td></tr>\"\n\n               }\n               this.$nextTick(() => {\n                 $('#sysjTableId').append(str)\n               })\n             }*/\n      },\n      //渲染铭牌页面开始mpList:反回的铭牌列表  row：模板行对象\n      applyMpHtml(mpList, row) {\n        //每次打开需要重新渲染一次,先将置空\n        $('#sbmpTbodyId').html(\"\");\n        //清空重新赋值\n        this.mbInfo = {}\n        this.mbInfo.mbmc = row.mbmc;\n        //拼接的铭牌表格\n        let str = \"\";\n        //先判断是否分相铭牌\n        if (mpList.length > 0) {\n          if (mpList[0].SFFX == '1') { //当前铭牌为分相铭牌时\n            //写死第一行\n            str += \"<tr><td style='padding: 10px;font-size: 15px;'>相别</td>\" +\n              \"<td style='padding: 10px;font-size: 15px;'>A</td>\" +\n              \"<td style='padding: 10px;font-size: 15px;'>B</td>\" +\n              \"<td style='padding: 10px;font-size: 15px;'>C</td>\" +\n              \"</tr>\";\n            //开始遍历展示\n            for (let a = 0; a < mpList.length; a++) {\n              str += \"<tr>\"\n              str += \"<td style='padding: 10px;font-size: 15px;'>\";\n              str += mpList[a].title + \"</td>\";\n              str += \"<td></td>>\"\n              str += \"<td></td>>\"\n              str += \"<td></td>>\"\n              str += \"</tr>\"\n            }\n          } else {  //铭牌不分相\n            //当前铭牌不属于分相铭牌\n            //每列展示单元格数量\n            let col = 3;\n            //展示行数\n            var lines = Math.ceil(mpList.length / col);\n            //遍历展示行数\n            for (var i = 0; i < lines; i++) {\n              str += \"<tr>\";\n              //遍历列\n              for (var j = 0; j < col; j++) {\n                if (i * col + j < mpList.length) {\n                  str += \"<td style='padding: 10px;font-size: 15px;'>\";\n                  //铭牌标题赋值\n                  str += mpList[i * col + j].title + \"</td>\";\n                  //铭牌值赋值\n                  str += mpList[i * col + j].sfmb == 1 ? \"<td> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td>\" : \"<td>\" + mpList[i * col + j].column_name + \"</td>\"\n                }\n              }\n              str += \"</tr>\";\n            }\n          }\n        }\n        //渲染铭牌页面\n        this.$nextTick(() => {\n          $('#sbmpTbodyId').append(str)\n        })\n        //打开弹出框\n        this.isShowDownLoadDialog = true;\n      },\n      //关闭预览弹出框\n      closeYlDialog() {\n        //清空表单\n        this.mbInfo = {};\n        //赋值完关闭弹窗\n        this.isShowDownLoadDialog = false;\n      }\n      ,\n      //定义模板内容\n      handleClickMbnr(row) {\n        //打开组件弹出框\n        this.isShowXmGlbwDialog = true;\n        //给子组件传递数据\n        this.mbRowData = row;\n      }\n      ,\n      //获取项目库项目数据\n      getXmLiraryData() {\n        getXmLiraryData(this.xmLibraryQueryForm).then(res => {\n          this.xmLibraryDataList = res.data.records\n          this.xmLibraryTotal = res.data.total\n        })\n      }\n      ,\n      //项目弹出框新增按钮\n      addMbGlXm() {\n        this.getXmLiraryData()\n        this.isShowAddGlxmDialog = true\n      }\n      ,\n      //项目库弹出框取消按钮\n      closeAddMjzDialog() {\n        this.isShowAddGlxmDialog = false\n      }\n      ,\n      //项目库弹窗确认按钮\n      commitAddMjzForm() {\n        if (this.xmSelectedForm.xmDataRows.length < 1) {\n          this.$message.info('未关联项目！！！已取消')\n          //如果未选中数据,则直接关闭弹窗\n          this.isShowAddGlxmDialog = false\n        } else {\n          console.log(this.xmSelectedForm)\n          //若选择数据后\n          addMbGlxmBatchToMbxm(this.xmSelectedForm).then(res => {\n            if (res.code === '0000') {\n              this.$message.success('关联成功')\n            } else {\n              this.$message.error('关联失败！！')\n            }\n            //关闭弹窗\n            this.isShowAddGlxmDialog = false\n            //调用获取关联子项目列表\n            this.getSymbGlsyxmDataListByPage()\n          })\n        }\n      }\n      ,\n      //项目库行选中事件\n      handleSelectedXmLibraryChange(rows) {\n        this.xmSelectedForm.xmDataRows = rows\n      }\n      ,\n      //项目库查询按钮\n      selectxmLibrary() {\n        this.getXmLiraryData()\n      }\n      ,\n      //项目库重置按钮\n      resetxmSearch() {\n        this.xmLibraryQueryForm.syxmmc = ''\n        this.getXmLiraryData()\n      }\n      ,\n      //获取关联子列表方法\n      getZxmDataList() {\n        getGlSyzxmDataListByPage(this.glzxmQueryParams).then(res => {\n          this.glzxmTotal = res.data.total\n          this.zxmGlmbDataList = res.data.records\n        })\n      }\n      ,\n\n      //关联项目\n      handleClickGlxm(row) {\n        //清空原来子项目数据\n        this.zxmGlmbDataList = []\n        //打开关联项目弹出框\n        this.isGlxmDialogShow = true\n        //给参数赋值\n        this.glxmQueryParams.symbid = row.objId\n        //查询项目库数据时参数\n        this.xmLibraryQueryForm.symbid = row.objId\n        //给试验项目库添加时使用\n        this.xmSelectedForm.symbid = row.objId\n        //获取模板关联项目数据\n        this.getSymbGlsyxmDataListByPage()\n      }\n      ,\n      //获取关联项目弹出框数据\n      getSymbGlsyxmDataListByPage() {\n        getSymbGlsyxmDataListByPage(this.glxmQueryParams).then(res => {\n          this.mbGlxmDataList = res.data.records\n          this.glxmTotal = res.data.total\n        })\n      }\n      ,\n      //试验项目复选框点击时间点击操作\n      handleGlxmSelectedChange(rows) {\n        this.selectedRowDataChange = rows\n      }\n      ,\n      //删除模板关联项目\n      deleteMbGlXm() {\n        if (this.selectedRowDataChange.length < 1) {\n          this.$message.warning('请选择正确的数据！！！')\n          return\n        }\n        let ids = this.selectedRowDataChange.map(item => {\n          return item.objId\n        })\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove(ids).then(({code}) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getSymbGlsyxmDataListByPage()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n              this.getSymbGlsyxmDataListByPage()\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n      //试验项目点击行数据时的单机操作\n      handleMbGlxmRowClick(row) {\n        this.glzxmQueryParams.syxmid = row.syxmid\n        this.getZxmDataList()\n      },\n      //懒加载函数\n      loadNode(node, resolve) {\n        let TreeparamMap = {\n          pid: '',\n          spbLogo: ['输电设备', '变电设备','配电设备']\n        }\n        if (node.level === 0) {\n          TreeparamMap.pid = 'sb'\n          return this.getTreeNode(TreeparamMap, resolve)\n        }\n        setTimeout(() => {\n          TreeparamMap.pid = node.data.code\n          this.getTreeNode(TreeparamMap, resolve)\n        }, 500)\n\n      },\n      //获取树节点数据\n      getTreeNode(paramMap, resolve) {\n        getDeviceClassTreeNodeByPid(paramMap).then(res => {\n          let treeNodes = []\n          res.data.forEach(item => {\n            let node = {\n              name: item.name,\n              level: item.level,\n              id: item.id,\n              pid: item.pid,\n              leaf: false,\n              code: item.code\n            }\n            treeNodes.push(node)\n          })\n          resolve(treeNodes)\n        })\n      },\n      //添加后确认保存按钮\n      save() {\n        this.$refs['form'].validate((valid) => {\n          if (valid) {\n            saveOrUpdate(this.form).then(res => {\n              if (res.code === '0000') {\n                this.$message.success(res.msg)\n                this.tableAndPageInfo.pager.pageResize = 'Y'\n                this.getData()\n                this.isShowDetails = false\n              } else {\n                this.$message.error(res.msg)\n              }\n            })\n\n          }\n        })\n      },\n      //树节点点击事件\n      handleNodeClick(data) {\n        console.log('树节点点击')\n        console.log(data)\n        if (data.level != '0' && data.level != '1') {\n          //新增按钮可点击\n          this.addDisabled = false\n          this.treeForm = data\n          this.querySyBwParam.sblxid = data.code\n          this.getData()\n        } else {\n          this.addDisabled = true\n        }\n      },\n      //添加按钮\n      addSensorButton() {\n        this.form = {}\n        this.form.sblx = this.treeForm.name\n        this.form.sblxid = this.treeForm.code\n        this.isShowDetails = true\n        this.title = '新增'\n      },\n      //列表查询\n      async getData(params) {\n        try {\n          const param = {...this.querySyBwParam, ...params}\n          param.sblxid=this.symbData.sblxid;\n          const {data, code} = await getPageDataListTosymb(param)\n          if (code === '0000') {\n            this.tableAndPageInfo.tableData = data.records\n            this.tableAndPageInfo.pager.total = data.total\n          }\n        } catch (e) {\n          console.log(e)\n        }\n      }\n      ,\n      /**\n       * 表格多选框\n       */\n      handleSelectionChange(rows) {\n        this.selectRows = rows;\n      },\n\n      close() {\n        this.isShowDetails = false\n      }\n      ,\n      //修改模板主表内容\n      updateDetails(row) {\n        this.title = '修改'\n        this.isShowDetails = true\n        this.form = row\n        this.isDisabled = false\n        this.isShow = true\n      }\n      ,\n\n      createTemplate(row) {\n        console.log(row)\n      }\n      ,\n      //查看模板主表详情按钮\n      getDetails(row) {\n        this.title = '详情'\n        this.isShowDetails = true\n        this.form = row\n        this.isDisabled = true\n        this.isShow = false\n      }\n      ,\n\n      //删除按钮\n      deleteSensorButton() {\n        if (this.selectRows.length < 1) {\n          this.$message.warning('请选择正确的数据！！！')\n          return\n        }\n        let ids = this.selectRows.map(item => {\n          return item.objId\n        })\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove(ids).then(({code}) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.tableAndPageInfo.pager.pageResize = 'Y'\n              this.getData()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n              this.tableAndPageInfo.pager.pageResize = 'Y'\n              this.getData()\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n\n      }\n      ,\n      //导出按钮\n      handleExport() {\n\n      }\n      ,\n\n      //关联铭牌点击事件\n      handleClickGlMp(row) {\n        this.showMpDialog = true\n        this.rowData = row\n      }\n      ,\n      //关闭试验铭牌弹窗\n      closeMpDialog() {\n        this.showMpDialog = false\n      }\n      ,\n\n      filterReset() {\n      },\n\n      //关闭试验模板弹窗\n      closeSymbComment() {\n        this.$emit(\"closeSymbSelectDialog\", false)\n      },\n      //点击确认后给父组件传递数据\n      // this.selectRowData != undefined && JSON.stringify(this.selectRowData) != \"{}\"\n      commitMbdata() {\n        if (this.selectRows.length == 1 && this.selectRows != undefined) {\n         this.$emit(\"handleAcceptMbData\",this.selectRows[0]);\n          this.$emit(\"closeSymbSelectDialog\", false)\n        } else {\n          this.$message.warning(\"请选择一条数据\")\n        }\n      },\n\n    }\n  }\n</script>\n\n<style lang='scss' scoped>118\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n/*导出pdf格式设置开始*/\n#printContentId {\n  background-color: #fff;\n  width: 100%;\n  /* height: 400px; */\n  margin: auto;\n  padding: 16px;\n  box-sizing: border-box;\n  //试验数据样式\n  .printTitle {\n    text-align: left;\n    line-height: 40px;\n    border-left: 1px solid #000;\n    border-right: 1px solid #000;\n    //border-bottom: 1px solid #000;\n    padding-left: 10px;\n  }\n}\n\n//修改table表头颜色\n/deep/ #printContentId .el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th {\n  background: #fff;\n  border-color: #000;\n  font-weight: inherit;\n}\n\n/deep/ #printContentId .el-table--enable-row-transition .el-table__body td {\n  border-color: #000;\n}\n\n.table_style td, th {\n  padding: 10px;\n  font-size: 15px;\n}\n\n.table_style {\n  border-collapse: collapse;\n  width: 100%;\n  text-align: center;\n  /* border-bottom: 0;\n   border-left: 0;\n   border-right: 0;*/\n}\n\n/* 设置滚动条的样式 */\n::-webkit-scrollbar {\n  width: 12px;\n}\n\n/* 滚动槽 */\n::-webkit-scrollbar-track {\n  //-webkit-box-shadow:inset006pxrgba(0,0,0,0.3);\n  border-radius: 10px;\n}\n\n/* 滚动条滑块 */\n::-webkit-scrollbar-thumb {\n  border-radius: 10px;\n  background: rgba(0, 0, 0, 0.1);\n  //-webkit-box-shadow:gba(0,0,0,0.5);\n}\n\n::-webkit-scrollbar-thumb:window-inactive {\n  background: rgba(0, 0, 0, 0.1);\n}\n\n[data-v-67a974b1]::-webkit-scrollbar {\n  width: 8px;\n}\n\n.item {\n  width: 225px;\n  float: left;\n}\n\n/*导出pdf格式设置结束*/\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  max-height: 79vh;\n  overflow: auto;\n}\n\n.app-container {\n  padding: 3px;\n}\n</style>\n"]}]}