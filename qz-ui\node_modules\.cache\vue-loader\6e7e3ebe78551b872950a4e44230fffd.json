{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\blgk\\index.vue?vue&type=template&id=38738f5e&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\blgk\\index.vue", "mtime": 1750579918247}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxlbC1yb3cgY2xhc3M9ImFwcC1jb250YWluZXIiPgogIDxlbC1yb3c+CiAgICA8IS0t5Z+65pys5L+h5oGv5p+l6K+i5Y+K5pi+56S6LS0+CiAgICA8ZWwtY29sIDpzcGFuPSIyNCI+CiAgICAgIDwhLS3mkJzntKLmnaHku7YtLT4KICAgICAgPGVsLWZpbHRlcgogICAgICAgIHJlZj0iZmlsdGVyMSIKICAgICAgICA6ZGF0YT0iZmlsdGVySW5mby5kYXRhIgogICAgICAgIDpmaWVsZC1saXN0PSJmaWx0ZXJJbmZvLmZpZWxkTGlzdCIKICAgICAgICBAaGFuZGxlUmVzZXQ9ImZpbHRlclJlc2V0IgogICAgICAgIDp3aWR0aD0ieyBsYWJlbFdpZHRoOiAxMDAsIGl0ZW1XaWR0aDogMjAwIH0iCiAgICAgIC8+CiAgICA8L2VsLWNvbD4KICA8L2VsLXJvdz4KCiAgPGVsLXJvdz4KICAgIDxlbC1jb2w+CiAgICAgIDxlbC13aGl0ZSBjbGFzcz0iYnV0dG9uLWdyb3VwIj4KICAgICAgICA8ZGl2IGNsYXNzPSJidXR0b25fYnRuIj4KICAgICAgICAgIDxlbC1idXR0b24KICAgICAgICAgICAgdHlwZT0icHJpbWFyeSIKICAgICAgICAgICAgaWNvbj0iZWwtaWNvbi1wbHVzIgogICAgICAgICAgICB2LWhhc1Blcm1pPSJbJ2JsZ2s6YnV0dG9uOmFkZCddIgogICAgICAgICAgICBAY2xpY2s9ImFkZFJvdyIKICAgICAgICAgICAgPuaWsOWinjwvZWwtYnV0dG9uCiAgICAgICAgICA+CiAgICAgICAgPC9kaXY+CiAgICAgICAgPGNvbXAtdGFibGUgOnRhYmxlLWFuZC1wYWdlLWluZm89InRhYmxlQW5kUGFnZUluZm8iIGhlaWdodD0iNjV2aCI+CiAgICAgICAgICA8ZWwtdGFibGUtY29sdW1uCiAgICAgICAgICAgIHNsb3Q9InRhYmxlX2VpZ2h0IgogICAgICAgICAgICBhbGlnbj0iY2VudGVyIgogICAgICAgICAgICBmaXhlZD0icmlnaHQiCiAgICAgICAgICAgIHN0eWxlPSJkaXNwbGF5OiBibG9jayIKICAgICAgICAgICAgbGFiZWw9IuaTjeS9nCIKICAgICAgICAgICAgd2lkdGg9IjIwMCIKICAgICAgICAgICAgOnJlc2l6YWJsZT0iZmFsc2UiCiAgICAgICAgICA+CiAgICAgICAgICAgIDx0ZW1wbGF0ZSBzbG90LXNjb3BlPSJzY29wZSI+CiAgICAgICAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgICAgICAgdHlwZT0idGV4dCIKICAgICAgICAgICAgICAgIHNpemU9InNtYWxsIgogICAgICAgICAgICAgICAgaWNvbj0iZWwtaWNvbi1lZGl0IgogICAgICAgICAgICAgICAgdGl0bGU9Iue8lui+kSIKICAgICAgICAgICAgICAgIEBjbGljaz0iaGFuZGxlVXBkYXRlKHNjb3BlLnJvdykiCiAgICAgICAgICAgICAgICB2LWlmPSIKICAgICAgICAgICAgICAgICAgKHNjb3BlLnJvdy5jcmVhdGVCeSA9PT0gJHN0b3JlLmdldHRlcnMubmFtZSApIHx8CiAgICAgICAgICAgICAgICAgICAgaGFzU3VwZXJSb2xlCiAgICAgICAgICAgICAgICAiCiAgICAgICAgICAgICAgLz4KICAgICAgICAgICAgICA8ZWwtYnV0dG9uCiAgICAgICAgICAgICAgICB0eXBlPSJ0ZXh0IgogICAgICAgICAgICAgICAgc2l6ZT0ic21hbGwiCiAgICAgICAgICAgICAgICBAY2xpY2s9ImdldERldGFpbHMoc2NvcGUucm93KSIKICAgICAgICAgICAgICAgIGljb249ImVsLWljb24tdmlldyIKICAgICAgICAgICAgICAgIHRpdGxlPSLor6bmg4UiCiAgICAgICAgICAgICAgLz4KICAgICAgICAgICAgICA8ZWwtYnV0dG9uCiAgICAgICAgICAgICAgICB0eXBlPSJ0ZXh0IgogICAgICAgICAgICAgICAgc2l6ZT0ic21hbGwiCiAgICAgICAgICAgICAgICBpY29uPSJlbC1pY29uLXJlbW92ZSIKICAgICAgICAgICAgICAgIHRpdGxlPSLmtojpmaQiCiAgICAgICAgICAgICAgICBAY2xpY2s9ImhhbmRsZUNsZWFuKHNjb3BlLnJvdykiCiAgICAgICAgICAgICAgICB2LWlmPSIKICAgICAgICAgICAgICAgICAgc2NvcGUucm93LmNyZWF0ZUJ5ID09PSAkc3RvcmUuZ2V0dGVycy5uYW1lICYmCiAgICAgICAgICAgICAgICAgICAgc2NvcGUucm93LmlzQ2xlYW4gIT0gJ+aYrycKICAgICAgICAgICAgICAgICIKICAgICAgICAgICAgICAvPgogICAgICAgICAgICAgIDxlbC1idXR0b24KICAgICAgICAgICAgICAgIHR5cGU9InRleHQiCiAgICAgICAgICAgICAgICBzaXplPSJzbWFsbCIKICAgICAgICAgICAgICAgIGljb249ImVsLWljb24tZGVsZXRlIgogICAgICAgICAgICAgICAgdGl0bGU9IuWIoOmZpCIKICAgICAgICAgICAgICAgIEBjbGljaz0iaGFuZGxlRGVsZXRlQnlJZChzY29wZS5yb3cub2JqSWQpIgogICAgICAgICAgICAgICAgdi1pZj0iCiAgICAgICAgICAgICAgICAgIHNjb3BlLnJvdy5jcmVhdGVCeSA9PT0gJHN0b3JlLmdldHRlcnMubmFtZSB8fCBoYXNTdXBlclJvbGUgIAogICAgICAgICAgICAgICAgIgogICAgICAgICAgICAgIC8+CiAgICAgICAgICAgIDwvdGVtcGxhdGU+CiAgICAgICAgICA8L2VsLXRhYmxlLWNvbHVtbj4KICAgICAgICA8L2NvbXAtdGFibGU+CiAgICAgIDwvZWwtd2hpdGU+CiAgICA8L2VsLWNvbD4KICA8L2VsLXJvdz4KCiAgPCEtLSDmlrDlop7jgIHor6bmg4XlvLnlh7rlr7nor53moYYgLS0+CiAgPGVsLWRpYWxvZwogICAgaWQ9ImJsZ2tfZGl2IgogICAgOnRpdGxlPSJ0aXRsZSIKICAgIDp2aXNpYmxlLnN5bmM9ImlzU2hvdyIKICAgIHdpZHRoPSI2MCUiCiAgICBAY2xvc2U9ImNsb3NlRnVuIgogICAgdi1kaWFsb2dEcmFnCiAgPgogICAgPGRpdiBjbGFzcz0iYnV0dG9uX2J0biI+CiAgICAgIDxlbC1idXR0b24KICAgICAgICB0eXBlPSJwcmltYXJ5IgogICAgICAgIGljb249ImVsLWljb24tcGx1cyIKICAgICAgICB2LWlmPSIhaXNEaXNhYmxlZCIKICAgICAgICBAY2xpY2s9ImNob29zZVNiRnVuIgogICAgICAgID7pgInmi6norr7lpIc8L2VsLWJ1dHRvbgogICAgICA+CiAgICA8L2Rpdj4KICAgIDxlbC1mb3JtIGxhYmVsLXdpZHRoPSIxMjBweCIgcmVmPSJmb3JtIiA6bW9kZWw9ImZvcm0iIDpydWxlcz0icnVsZXMiPgogICAgICA8ZWwtcm93IDpndXR0ZXI9IjgiIGNsYXNzPSJwdWxsLWxlZnQiPgogICAgICAgIDxlbC1jb2wgOnNwYW49IjEyIj4KICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gcHJvcD0iZmdzbWMiIGxhYmVsPSLliIblhazlj7jvvJoiPgogICAgICAgICAgICA8ZWwtaW5wdXQKICAgICAgICAgICAgICB2LW1vZGVsPSJmb3JtLmZnc21jIgogICAgICAgICAgICAgIDpkaXNhYmxlZD0idHJ1ZSIKICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6ODAlIgogICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fpgInmi6nliIblhazlj7giCiAgICAgICAgICAgIC8+CiAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICA8L2VsLWNvbD4KICAgICAgICA8ZWwtY29sIDpzcGFuPSIxMiI+CiAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLlj5jnlLXnq5nvvJoiIHByb3A9ImJkem1jIj4KICAgICAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICAgICAgdi1tb2RlbD0iZm9ybS5iZHptYyIKICAgICAgICAgICAgICA6ZGlzYWJsZWQ9InRydWUiCiAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOjgwJSIKICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36YCJ5oup5Y+Y55S156uZIgogICAgICAgICAgICAvPgogICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgPC9lbC1jb2w+CiAgICAgIDwvZWwtcm93PgoKICAgICAgPGVsLXJvdyA6Z3V0dGVyPSI4IiBjbGFzcz0icHVsbC1sZWZ0Ij4KICAgICAgICA8ZWwtY29sIDpzcGFuPSIxMiI+CiAgICAgICAgICA8ZWwtZm9ybS1pdGVtIHByb3A9ImpnbWMiIGxhYmVsPSLpl7TpmpTvvJoiPgogICAgICAgICAgICA8ZWwtaW5wdXQKICAgICAgICAgICAgICB2LW1vZGVsPSJmb3JtLmpnbWMiCiAgICAgICAgICAgICAgOmRpc2FibGVkPSJ0cnVlIgogICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDo4MCUiCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+mAieaLqemXtOmalCIKICAgICAgICAgICAgLz4KICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgIDwvZWwtY29sPgogICAgICAgIDxlbC1jb2wgOnNwYW49IjEyIj4KICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuiuvuWkh+exu+Wei++8miIgcHJvcD0ic2JseG1jIj4KICAgICAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICAgICAgdi1tb2RlbD0iZm9ybS5zYmx4bWMiCiAgICAgICAgICAgICAgOmRpc2FibGVkPSJ0cnVlIgogICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDo4MCUiCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+mAieaLqeiuvuWkh+exu+WeiyIKICAgICAgICAgICAgLz4KICAgICAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICAgIDwvZWwtY29sPgogICAgICA8L2VsLXJvdz4KCiAgICAgIDxlbC1yb3cgOmd1dHRlcj0iOCIgY2xhc3M9InB1bGwtbGVmdCI+CiAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i6K6+5aSH77yaIiBwcm9wPSJzYm1jIj4KICAgICAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICAgICAgdi1tb2RlbD0iZm9ybS5zYm1jIgogICAgICAgICAgICAgIDpkaXNhYmxlZD0idHJ1ZSIKICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6ODAlIgogICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fpgInmi6norr7lpIciCiAgICAgICAgICAgIC8+CiAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICA8L2VsLWNvbD4KICAgICAgICA8ZWwtY29sIDpzcGFuPSIxMiI+CiAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLkuI3oia/lt6XlhrXnsbvlnos6IiBwcm9wPSJ0eXBlIj4KICAgICAgICAgICAgPGVsLXNlbGVjdAogICAgICAgICAgICAgIEBjaGFuZ2U9Imx4Q2hhbmdlIgogICAgICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fpgInmi6nkuI3oia/lt6XlhrXnsbvlnosiCiAgICAgICAgICAgICAgdi1tb2RlbD0iZm9ybS50eXBlIgogICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDo4MCUiCiAgICAgICAgICAgICAgOmRpc2FibGVkPSJpc0Rpc2FibGVkIgogICAgICAgICAgICA+CiAgICAgICAgICAgICAgPGVsLW9wdGlvbgogICAgICAgICAgICAgICAgdi1mb3I9Iml0ZW0gaW4gbHhMaXN0IgogICAgICAgICAgICAgICAgOmtleT0iaXRlbS52YWx1ZSIKICAgICAgICAgICAgICAgIDpsYWJlbD0iaXRlbS5sYWJlbCIKICAgICAgICAgICAgICAgIDp2YWx1ZT0iU3RyaW5nKGl0ZW0udmFsdWUpIgogICAgICAgICAgICAgID4KICAgICAgICAgICAgICA8L2VsLW9wdGlvbj4KICAgICAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICA8L2VsLWNvbD4KICAgICAgPC9lbC1yb3c+CgogICAgICA8ZWwtcm93IDpndXR0ZXI9IjgiIGNsYXNzPSJwdWxsLWxlZnQiPgogICAgICAgIDxlbC1jb2wgOnNwYW49IjI0Ij4KICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuS4jeiJr+W3peWGteaPj+i/sO+8miIgcHJvcD0ibXMiPgogICAgICAgICAgICA8ZWwtc2VsZWN0CiAgICAgICAgICAgICAgQGNoYW5nZT0ibXNDaGFuZ2UiCiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+mAieaLqeaPj+i/sCIKICAgICAgICAgICAgICA6ZGlzYWJsZWQ9ImlzRGlzYWJsZWQiCiAgICAgICAgICAgICAgdi1tb2RlbD0iZm9ybS5tcyIKICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6IDkxJSIKICAgICAgICAgICAgICBmaWx0ZXJhYmxlCiAgICAgICAgICAgICAgOmFsbG93LWNyZWF0ZT0idGhpcy5mb3JtLnR5cGUgPT09ICflhbbku5YnIgogICAgICAgICAgICA+CiAgICAgICAgICAgICAgPGVsLW9wdGlvbgogICAgICAgICAgICAgICAgdi1mb3I9Iml0ZW0gaW4gbXNMaXN0IgogICAgICAgICAgICAgICAgOmtleT0iaXRlbS52YWx1ZSIKICAgICAgICAgICAgICAgIDpsYWJlbD0iaXRlbS5sYWJlbCIKICAgICAgICAgICAgICAgIDp2YWx1ZT0iaXRlbS52YWx1ZSIKICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgPC9lbC1vcHRpb24+CiAgICAgICAgICAgIDwvZWwtc2VsZWN0PgogICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMjQiIHYtaWY9InRoaXMuZm9ybS50eXBlICE9PSAn5YW25LuWJyI+CiAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLliIbnsbvkvp3mja7vvJoiIHByb3A9ImZseWoiPgogICAgICAgICAgICA8ZWwtc2VsZWN0CiAgICAgICAgICAgICAgOmRpc2FibGVkPSJpc0Rpc2FibGVkIgogICAgICAgICAgICAgIHN0eWxlPSJ3aWR0aDogOTElIgogICAgICAgICAgICAgIHYtbW9kZWw9ImZvcm0uZmx5aiIKICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36YCJ5oup5YiG57G75L6d5o2uIgogICAgICAgICAgICA+CiAgICAgICAgICAgICAgPGVsLW9wdGlvbgogICAgICAgICAgICAgICAgdi1mb3I9Iml0ZW0gaW4gZmx5akxpc3QiCiAgICAgICAgICAgICAgICA6a2V5PSJpdGVtLnZhbHVlIgogICAgICAgICAgICAgICAgOmxhYmVsPSJpdGVtLmxhYmVsIgogICAgICAgICAgICAgICAgOnZhbHVlPSJpdGVtLnZhbHVlIgogICAgICAgICAgICAgID4KICAgICAgICAgICAgICA8L2VsLW9wdGlvbj4KICAgICAgICAgICAgPC9lbC1zZWxlY3Q+CiAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICA8L2VsLWNvbD4KICAgICAgPC9lbC1yb3c+CgogICAgICA8ZWwtcm93IDpndXR0ZXI9IjgiIGNsYXNzPSJwdWxsLWxlZnQiPgogICAgICAgIDxlbC1jb2wgOnNwYW49IjEyIj4KICAgICAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IuW9leWFpeS6uu+8miIgcHJvcD0ibHJybWMiPgogICAgICAgICAgICA8ZWwtaW5wdXQKICAgICAgICAgICAgICB2LW1vZGVsPSJmb3JtLmxycm1jIgogICAgICAgICAgICAgIDpkaXNhYmxlZD0idHJ1ZSIKICAgICAgICAgICAgICBzdHlsZT0id2lkdGg6ODAlIgogICAgICAgICAgICAvPgogICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5Y+R546w5pe26Ze077yaIiBwcm9wPSJmeHNqIj4KICAgICAgICAgICAgPGVsLWRhdGUtcGlja2VyCiAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOjgwJSIKICAgICAgICAgICAgICA6ZGlzYWJsZWQ9ImlzRGlzYWJsZWQiCiAgICAgICAgICAgICAgdi1tb2RlbD0iZm9ybS5meHNqIgogICAgICAgICAgICAgIHR5cGU9ImRhdGUiCiAgICAgICAgICAgICAgZm9ybWF0PSJ5eXl5LU1NLWRkIgogICAgICAgICAgICAgIHZhbHVlLWZvcm1hdD0ieXl5eS1NTS1kZCIKICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36YCJ5oup5pe26Ze0IgogICAgICAgICAgICAvPgogICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgPGVsLWNvbCA6c3Bhbj0iMTIiPgogICAgICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5raI6Zmk5pe26Ze077yaIiBwcm9wPSJ4Y3NqIj4KICAgICAgICAgICAgPGVsLWRhdGUtcGlja2VyCiAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOjgwJSIKICAgICAgICAgICAgICA6ZGlzYWJsZWQ9ImlzRGlzYWJsZWQiCiAgICAgICAgICAgICAgdi1tb2RlbD0iZm9ybS54Y3NqIgogICAgICAgICAgICAgIHR5cGU9ImRhdGUiCiAgICAgICAgICAgICAgZm9ybWF0PSJ5eXl5LU1NLWRkIgogICAgICAgICAgICAgIHZhbHVlLWZvcm1hdD0ieXl5eS1NTS1kZCIKICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36YCJ5oup5pe26Ze0IgogICAgICAgICAgICAvPgogICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgPC9lbC1jb2w+CiAgICAgIDwvZWwtcm93PgoKICAgICAgPGVsLXJvdyA6Z3V0dGVyPSI4IiBjbGFzcz0icHVsbC1sZWZ0Ij4KICAgICAgICA8ZWwtY29sIDpzcGFuPSIyNCI+CiAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLlpIfms6jvvJoiIHByb3A9ImJ6Ij4KICAgICAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICAgICAgdHlwZT0idGV4dGFyZWEiCiAgICAgICAgICAgICAgOnJvd3M9IjUiCiAgICAgICAgICAgICAgc3R5bGU9IndpZHRoOiA5MiUiCiAgICAgICAgICAgICAgdi1tb2RlbD0iZm9ybS5ieiIKICAgICAgICAgICAgICA6ZGlzYWJsZWQ9ImlzRGlzYWJsZWQiCiAgICAgICAgICAgIC8+CiAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICA8L2VsLWNvbD4KICAgICAgPC9lbC1yb3c+CiAgICA8L2VsLWZvcm0+CgogICAgPGRpdiBzbG90PSJmb290ZXIiPgogICAgICA8ZWwtYnV0dG9uIEBjbGljaz0iY2xvc2VGdW4iPuWPliDmtog8L2VsLWJ1dHRvbj4KICAgICAgPGVsLWJ1dHRvbiB2LWlmPSIhaXNEaXNhYmxlZCIgdHlwZT0icHJpbWFyeSIgQGNsaWNrPSJzYXZlRnVuIgogICAgICAgID7kv50g5a2YPC9lbC1idXR0b24KICAgICAgPgogICAgPC9kaXY+CiAgPC9lbC1kaWFsb2c+CgogIDwhLS3mlrDkuLvorr7lpIfpgInmi6nmoYbnu4Tku7YtLT4KICA8ZWwtZGlhbG9nCiAgICB0aXRsZT0i6K6+5aSH6YCJ5oupIgogICAgOnZpc2libGUuc3luYz0iaXNTaG93U2JDaG9vc2UiCiAgICB3aWR0aD0iODYlIgogICAgdi1pZj0iaXNTaG93U2JDaG9vc2UiCiAgICB2LWRpYWxvZ0RyYWcKICA+CiAgICA8c2ItY2hvb3NlCiAgICAgIEBjbG9zZUNob29zZUZ1bj0iY2xvc2VDaG9vc2VGdW4iCiAgICAgIEBnZXRTYkRhdGFGdW49ImdldFNiRGF0YUZ1biIKICAgIC8+CiAgPC9lbC1kaWFsb2c+CjwvZWwtcm93Pgo="}, null]}