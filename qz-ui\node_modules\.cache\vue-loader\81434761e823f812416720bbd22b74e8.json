{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\ztlxxdAndPdyj.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\ztlxxdAndPdyj.vue", "mtime": 1706897323231}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBadGx4eGQgZnJvbSAiQC92aWV3cy9kYWdhbmdPaWxmaWVsZC9iemdsL3NienRwamJ6ay96dGx4eGQiOwppbXBvcnQgUGR5aiBmcm9tICJAL3ZpZXdzL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvc2J6dHBqYnprL3BkeWoiOwoKZXhwb3J0IGRlZmF1bHQgewogIHByb3BzOiB7CiAgICBtcERhdGE6IHsKICAgICAgdHlwZTogT2JqZWN0CiAgICB9CiAgfSwKbmFtZTonenRseHhkQW5kUGR5aicsCmNvbXBvbmVudHM6e1p0bHh4ZCxQZHlqfSwKIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBhY3RpdmVOYW1lOiAnZmlyc3QnLAogICAgICB0YWJzSW5kZXg6IDEKICAgIH0KICB9LAogIG1vdW50ZWQoKSB7CiAgfSwKICAgIG1ldGhvZHM6IHsKICAgIC8vdGFi5Lmf5YiH5o2i54K55Ye75LqL5Lu2CiAgICBoYW5kbGVDbGljayh0YWIpIHsKICAgICAgdGhpcy50YWJzSW5kZXggPSB0YWIubmFtZSA9PT0gJ2ZpcnN0JyA/IDEgOiAyCiAgICB9LAogICAgLy/lhbPpl63lvLnnqpcKICAgIGNsb3NlRGlhbG9nKCkgewogICAgICB0aGlzLiRlbWl0KCdjbG9zZVBhcmFtRGlhbG9nJykKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["ztlxxdAndPdyj.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAmBA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ztlxxdAndPdyj.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk", "sourcesContent": ["<template>\n  <div>\n    <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\n      <el-tab-pane label=\"状态量信息点\" name=\"first\">\n        <Ztlxxd ref=\"ztlxxd\" v-if=\"tabsIndex === 1 \" :mp-data=\"mpData\"/>\n      </el-tab-pane>\n      <el-tab-pane label=\"判断依据\" name=\"second\">\n        <Pdyj ref=\"pdyj\" v-if=\"tabsIndex === 2 \" :mp-data=\"mpData\" :sblx=\"mpData.sblxId\"/>\n      </el-tab-pane>\n    </el-tabs>\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"closeDialog\" style=\"margin-left: 92%;margin-top: 1%;\">取 消</el-button>\n    </div>\n  </div>\n</template>\n\n\n\n<script>\nimport Ztlxxd from \"@/views/dagangOilfield/bzgl/sbztpjbzk/ztlxxd\";\nimport Pdyj from \"@/views/dagangOilfield/bzgl/sbztpjbzk/pdyj\";\n\nexport default {\n  props: {\n    mpData: {\n      type: Object\n    }\n  },\nname:'ztlxxdAndPdyj',\ncomponents:{Ztlxxd,Pdyj},\n data() {\n    return {\n      activeName: 'first',\n      tabsIndex: 1\n    }\n  },\n  mounted() {\n  },\n    methods: {\n    //tab也切换点击事件\n    handleClick(tab) {\n      this.tabsIndex = tab.name === 'first' ? 1 : 2\n    },\n    //关闭弹窗\n    closeDialog() {\n      this.$emit('closeParamDialog')\n    }\n  }\n}\n</script>\n\n<style scoped>\n\n</style>\n"]}]}