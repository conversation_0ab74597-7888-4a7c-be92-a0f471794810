{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\gqjgl\\xlgqj.vue?vue&type=template&id=65554d12&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\gqjgl\\xlgqj.vue", "mtime": 1706897324701}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}