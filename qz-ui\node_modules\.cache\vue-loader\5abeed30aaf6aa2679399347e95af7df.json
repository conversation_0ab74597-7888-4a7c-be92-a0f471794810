{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\xldzcz\\dzczml.vue?vue&type=style&index=0&id=7f94b70c&scoped=true&lang=css&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\xldzcz\\dzczml.vue", "mtime": 1719917121296}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5jYXJkLXN0eWxlIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZTBlZmYyOwogIGxpbmUtaGVpZ2h0OiAzdmg7CiAgYm9yZGVyLXJhZGl1czogM3B4OwogIGZvbnQtd2VpZ2h0OiA4MDA7CiAgcGFkZGluZy1sZWZ0OiAxdnc7CiAgbWFyZ2luLWJvdHRvbTogM3ZoOwp9CgovKuaOp+WItmlucHV06L6T5YWl5qGG6L655qGG5piv5ZCm5pi+56S6Ki8KLmVsSW5wdXQgPj4+IC5lbC1pbnB1dF9faW5uZXIgewogIGJvcmRlcjogMDsKfQoKLmNlbnRlciB7CiAgdGV4dC1hbGlnbjogY2VudGVyOyAvKuiuqWRpduWGhemDqOaWh+Wtl+WxheS4rSovCn0K"}, {"version": 3, "sources": ["dzczml.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA87BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "dzczml.vue", "sourceRoot": "src/views/dagangOilfield/czpgl/xldzcz", "sourcesContent": ["<template>\n  <div class=\"app-container\" style=\"padding:1px\">\n    <!--    <el-white>-->\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n      @handleReset=\"getReset\"\n    />\n    <!--    </el-white>-->\n\n    <div>\n      <el-white class=\"button-group\">\n        <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n          <el-col\n            style=\"\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n          \"\n          >\n            <div style=\"margin-bottom: 8px;\">\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-plus\"\n                @click=\"getInster\"\n                v-hasPermi=\"['xldzczml:button:add']\"\n                >新增\n              </el-button>\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-download\"\n                @click=\"exportWord\"\n                >导出</el-button\n              >\n            </div>\n          </el-col>\n        </el-row>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"selectChange\"\n          height=\"65vh\"\n          v-loading=\"loading\"\n        >\n          <el-table-column\n            slot=\"table_eight\"\n            align=\"center\"\n            fixed=\"right\"\n            style=\"display: block\"\n            label=\"操作\"\n            width=\"160\"\n            :resizable=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                @click=\"getDetails(scope.row)\"\n                title=\"详情\"\n                class=\"el-icon-view\"\n              ></el-button>\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                v-if=\"(scope.row.createBy === currentUser || hasSuperRole) && !scope.row.czp\"\n                @click=\"getUpdate(scope.row)\"\n                title=\"编辑\"\n                class=\"el-icon-edit\"\n              >\n              </el-button>\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                v-if=\"(scope.row.createBy === currentUser || hasSuperRole) && !scope.row.czp\"\n                @click=\"deleteRow(scope.row)\"\n                title=\"删除\"\n                class=\"el-icon-delete\"\n              >\n              </el-button>\n            </template>\n          </el-table-column>\n        </comp-table>\n      </el-white>\n    </div>\n\n    <!-- 详情/新增/修改  倒闸操作命令页面-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"50%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <div>\n          <el-row>\n            <el-col :span=\"12\">\n              <el-form-item label=\"编号：\" prop=\"bh\">\n                <el-input\n                  v-model=\"form.bh\"\n                  :disabled=\"true\"\n                  placeholder=\"保存后自动生成\"\n                />\n              </el-form-item>\n            </el-col>\n            <!--<el-col :span=\"12\">\n              <el-form-item label=\"状态\" prop=\"status\">\n                <el-input v-model=\"form.status\" :disabled=\"true\" placeholder=\"请输入状态\"/>\n              </el-form-item>\n            </el-col>-->\n            <el-col :span=\"12\">\n              <el-form-item label=\"线路名称\" prop=\"xlmc\">\n                <el-input\n                  v-model=\"form.xlmc\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请输入线路名称\"\n                  v-on:click.native=\"sysbSelectedClick()\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"通知时间\" prop=\"tzsj\">\n                <el-date-picker\n                  v-model=\"form.tzsj\"\n                  :disabled=\"isDisabled\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"预备人下令人\" prop=\"yblxlr\">\n                <el-input\n                  v-model=\"form.yblxlr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请输入预备人下令人\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"预备令接令人\" prop=\"ybljlr\">\n                <el-input\n                  v-model=\"form.ybljlr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请输入预备令接令人\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"倒闸操作时间\" prop=\"dzczsj\">\n                <el-date-picker\n                  v-model=\"form.dzczsj\"\n                  :disabled=\"isDisabled\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  :placeholder=\"isDisabled ? '' : '选择日期时间'\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"恢复操作时间\" prop=\"hfczsj\">\n                <el-date-picker\n                  v-model=\"form.hfczsj\"\n                  :disabled=\"isDisabled\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  :placeholder=\"isDisabled ? '' : '选择日期时间'\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <el-col :span=\"24\">\n              <el-form-item label=\"工作名称\" prop=\"gzmc\">\n                <el-input\n                  type=\"textarea\"\n                  :rows=\"2\"\n                  v-model=\"form.gzmc\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请输入工作名称\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n        <!--预览信息-->\n        <el-dialog :title=\"titleyl\" :visible.sync=\"yl\" append-to-body>\n          <el-table\n            :data=\"propTableData.colFirst\"\n            height=\"200\"\n            border\n            stripe\n            style=\"width: 100%\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <el-table-column\n              align=\"center\"\n              prop=\"xh\"\n              width=\"100px\"\n              label=\"序号\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <el-input\n                  placeholder=\"请输入序号\"\n                  v-model=\"scope.row.xh\"\n                  :disabled=\"true\"\n                ></el-input>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作任务\">\n              <template slot-scope=\"scope\">\n                <el-input\n                  placeholder=\"操作任务\"\n                  v-model=\"scope.row.czrw\"\n                  :disabled=\"true\"\n                ></el-input>\n              </template>\n            </el-table-column>\n          </el-table>\n        </el-dialog>\n        <!--列表-->\n        <div>\n          <el-white class=\"mb8 pull-right\">\n            <el-button\n              type=\"info\"\n              @click=\"handleYlChange\"\n              style=\"text-align: right\"\n              >预览</el-button\n            >\n          </el-white>\n          <!--第一个表格-->\n          <el-table\n            :data=\"propTableData.colFirst\"\n            height=\"200\"\n            border\n            stripe\n            style=\"width: 100%\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <el-table-column\n              align=\"center\"\n              prop=\"xh\"\n              width=\"100px\"\n              label=\"顺序号\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <el-input-number\n                  size=\"small\"\n                  v-model=\"scope.row.xh\"\n                  :min=\"1\"\n                  :precision=\"0\"\n                  controls-position=\"right\"\n                  :disabled=\"isDisabled\"\n                ></el-input-number>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作任务\">\n              <template slot-scope=\"scope\">\n                <el-input\n                  placeholder=\"操作任务\"\n                  v-model=\"scope.row.czrw\"\n                  :disabled=\"isDisabled\"\n                ></el-input>\n              </template>\n            </el-table-column>\n\n            <el-table-column align=\"center\" width=\"80\">\n              <template slot=\"header\" slot-scope=\"scope\">\n                <el-button\n                  size=\"small\"\n                  type=\"primary\"\n                  icon=\"el-icon-plus\"\n                  :disabled=\"isDisabled\"\n                  @click=\"listFirstAdd(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n              <template slot-scope=\"scope\">\n                <el-button\n                  type=\"text\"\n                  icon=\"el-icon-delete\"\n                  :disabled=\"isDisabled\"\n                  @click=\"listFirstDel(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">关 闭</el-button>\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"saveRow\"\n          >保 存</el-button\n        >\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"createCzp\"\n          >开操作票\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 详情/新增/修改  倒闸操作票页面-->\n    <el-dialog\n      :title=\"titleCzp\"\n      :visible.sync=\"isShowDetailsCzp\"\n      width=\"50%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"130px\" ref=\"formCzp\" :model=\"formCzp\">\n        <div>\n          <div>\n            <el-row>\n              <el-col :span=\"12\">\n                <el-form-item label=\"线路名称\" prop=\"xlmc\">\n                  <el-input\n                    v-model=\"formCzp.xlmc\"\n                    :disabled=\"isDisabled\"\n                    placeholder=\"请输入线路名称\"\n                  />\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row>\n              <el-col :span=\"24\">\n                <el-form-item label=\"工作名称\" prop=\"gzmc\">\n                  <el-input\n                    type=\"textarea\"\n                    :rows=\"2\"\n                    v-model=\"form.gzmc\"\n                    :disabled=\"isDisabled\"\n                    placeholder=\"请输入工作名称\"\n                  />\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </div>\n\n          <!--列表-->\n          <div>\n            <!--第一个表格-->\n            <el-table\n              :data=\"propTableDataCzp.colFirst\"\n              height=\"200\"\n              border\n              stripe\n              style=\"width: 100%\"\n              :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n            >\n              <el-table-column\n                align=\"center\"\n                prop=\"xh\"\n                width=\"100px\"\n                label=\"序号\"\n                sortable\n              >\n                <template slot-scope=\"scope\">\n                  <el-input\n                    placeholder=\"请输入序号\"\n                    v-model=\"scope.row.xh\"\n                    :disabled=\"isDisabled\"\n                  ></el-input>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n                <template slot-scope=\"scope\">\n                  <el-input\n                    placeholder=\"请输入操作项目\"\n                    v-model=\"scope.row.czrw\"\n                    :disabled=\"isDisabled\"\n                  ></el-input>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" width=\"80\">\n                <template slot=\"header\" slot-scope=\"scope\">\n                  <el-button\n                    size=\"small\"\n                    type=\"primary\"\n                    icon=\"el-icon-plus\"\n                    :disabled=\"isDisabled\"\n                    @click=\"listFirstAddCzp(scope.$index, scope.row)\"\n                  ></el-button>\n                </template>\n                <template slot-scope=\"scope\">\n                  <el-button\n                    type=\"text\"\n                    icon=\"el-icon-delete\"\n                    :disabled=\"isDisabled\"\n                    @click=\"listFirstDelCzp(scope.$index, scope.row)\"\n                  ></el-button>\n                </template>\n              </el-table-column>\n            </el-table>\n          </div>\n        </div>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"closeCzp\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"saveRowCzp\">确 认</el-button>\n        <el-button type=\"primary\" @click=\"submitCzp\">上 报</el-button>\n      </div>\n    </el-dialog>\n\n    <!--线路选择组件-->\n    <el-dialog\n      title=\"线路选择\"\n      :visible.sync=\"isShowXlDialog\"\n      width=\"20%\"\n      v-if=\"isShowXlDialog\"\n      v-dialogDrag\n    >\n      <sdxl-selected\n        @closeSysbSelectDialog=\"handleControlSysbSelectDialog\"\n        @handleAcceptSbData=\"handleAcceptSbData\"\n      ></sdxl-selected>\n    </el-dialog>\n\n    <!--  工作流  -->\n    <activiti\n      ref=\"activiti\"\n      :processData=\"processData\"\n      :isShow=\"isShow\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n    ></activiti>\n  </div>\n</template>\n\n<script>\nimport {\n  getList,\n  queryZb,\n  saveOrUpdate,\n  remove,\n  exportWordByselection,\n  exportWordByparams\n} from \"@/api/yxgl/sdyxgl/sddzczml\";\nimport { saveOrUpdates } from \"@/api/yxgl/sdyxgl/sddzczp\";\nimport ElectronicAuthDialog from \"com/ElectronicAuthDialog\";\nimport sdxlSelected from \"@/views/dagangOilfield/xjgl/sdxj/sdxlSelected1\";\nimport { saveOrUpdateCzp } from \"@/api/yxgl/bdyxgl/bddzczml\";\nimport activiti from \"com/activiti_czp\";\nimport { updateById } from \"@/api/yxgl/bdyxgl/bddzczp\";\nimport { getUUID } from \"@/utils/ruoyi\";\n\nexport default {\n  name: \"dzczml\",\n  components: { ElectronicAuthDialog, sdxlSelected, activiti },\n  data() {\n    return {\n      hasSuperRole:this.$store.getters.hasSuperRole,\n      // 多选框选中的id\n      ids: [],\n      // 多选框选选中的数据\n      selectData: [],\n      // 非单个禁用\n      single: undefined,\n      // 非多个禁用\n      multiple: true,\n      loading: false,\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"czplc\",\n        businessKey: \"\",\n        businessType: \"倒闸操作票\",\n        variables: {},\n        defaultFrom: true,\n        nextUser: \"\",\n        processType: \"complete\"\n      },\n      isShow: false,\n      //弹出框标题\n      activitiOption: { title: \"上报\" },\n      rules: {\n        xlmc: [\n          { required: true, message: \"线路名称不能为空\", trigger: \"select\" }\n        ],\n        tzsj: [\n          { required: true, message: \"通知时间不能为空\", trigger: \"blur\" }\n        ],\n        yblxlr: [\n          { required: true, message: \"预备人下令人不能为空\", trigger: \"blur\" }\n        ],\n        ybljlr: [\n          { required: true, message: \"预备人接令人不能为空\", trigger: \"blur\" }\n        ]\n      }, //表单必填校验\n      titleyl: \"\",\n      // 获取当前登录人账号\n      currentUser: this.$store.getters.name,\n      yl: false,\n      //线路选择弹出\n      isShowXlDialog: false,\n      // 多选框选中的id\n      ids: [],\n      //form表单\n      form: {\n        bh: \"\",\n        xlmc: \"\",\n        tzsj: \"\",\n        yblxlr: \"\",\n        czrw: \"\",\n        ybljlr: \"\",\n        status: \"\",\n        gzmc: \"\"\n      },\n      // 操作票form表单\n      formCzp: {\n        bh: \"\",\n        xlmc: \"\",\n        czr: \"\",\n        jhr: \"\",\n        sdshr: \"\",\n        lx: 1, //输电\n        colFirst: []\n      },\n      //弹出框中表格数据\n      propTableData: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //弹出框中表格数据\n      propTableDataCzp: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //操作命令详情弹框是否显示\n      isShowDetails: false,\n      //操作票弹框是否显示\n      isShowDetailsCzp: false,\n      //是否禁用\n      isDisabled: false,\n      authIsShow: false,\n      isSubmit: false,\n      //标题\n      title: \"\",\n      titleCzp: \"\",\n      filterInfo: {\n        data: {\n          column: \"\"\n        }, //查询条件\n        fieldList: [\n          { label: \"编号\", value: \"bh\", type: \"input\", clearable: true },\n          /*{\n            label: '状态',\n            value: 'status',\n            type: 'select',\n            clearable: true,\n            options: [{ label: '已执行', value: '已执行' }, { label: '未执行', value: '未执行' }]\n          },*/\n          { label: \"线路名称\", value: \"xlmc\", type: \"input\", clearable: true },\n          {\n            label: \"通知时间\",\n            value: \"tzsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          {\n            label: \"预备令下令人\",\n            value: \"yblxlr\",\n            type: \"input\",\n            clearable: true\n          },\n          {\n            label: \"预备令接令人\",\n            value: \"ybljlr\",\n            type: \"input\",\n            clearable: true\n          },\n          { label: \"工作名称\", value: \"gzmc\", type: \"input\", clearable: true }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"编号\", prop: \"bh\", minWidth: \"120\" },\n          /*{ label: '状态', prop: 'status', minWidth: '60' },*/\n          { label: \"线路名称\", prop: \"xlmc\", minWidth: \"120\" },\n          { label: \"通知时间\", prop: \"tzsj\", minWidth: \"160\" },\n          { label: \"预备令下令人\", prop: \"yblxlr\", minWidth: \"120\" },\n          { label: \"预备令接令人\", prop: \"ybljlr\", minWidth: \"120\" },\n          { label: \"工作名称\", prop: \"gzmc\", minWidth: \"120\" }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        bh: \"\",\n        xlmc: \"\",\n        tzsj: \"\",\n        yblxlr: \"\",\n        czrw: \"\",\n        ybljlr: \"\"\n      },\n      selectRows: []\n    };\n  },\n  mounted() {\n    //列表查询\n    this.getData();\n  },\n  methods: {\n    //工作流回传数据\n    async todoResult(data) {\n      let row = {\n        objId: data.businessKey,\n        isStart: 1,\n        isBack: data.processType === \"rollback\" ? 1 : 0\n      };\n      row.status = \"2\";\n      row.fgsspr = data.nextUser;\n      let { code } = await updateById(row);\n      if (code === \"0000\") {\n        await this.getData();\n      } else {\n        this.$message.warning(\"操作失败\");\n      }\n    },\n    //关闭弹窗\n    closeActiviti() {\n      if (this.$refs.activiti.$refs.form) {\n        this.$refs.activiti.$refs.form.resetFields();\n      }\n      this.isShow = false;\n    },\n    handleYlChange() {\n      this.titleyl = \"查看操作项目\";\n      this.yl = true;\n    },\n    //线路选择接收数据\n    handleAcceptSbData(sbData) {\n      // this.form.xlmcbm = sbData.id;\n      let str = \"\";\n      sbData.forEach(e => {\n        str += e.label + \",\";\n      });\n      //去掉最后一个逗号\n      str = str.substr(0, str.length - 1);\n      this.form.xlmc = str;\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        this.loading = true;\n        param.mySorts = [{ prop: \"updateTime\", asc: false }];\n        const { data, code } = await getList(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          this.loading = false;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //根据主表查询子表方法\n    async getListZb(row) {\n      try {\n        const { data, code } = await queryZb({ objId: row.objId });\n        if (code === \"0000\") {\n          this.propTableData.colFirst = data;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //新增按钮\n    async getInster() {\n      this.propTableData.colFirst = [];\n      this.title = \"输电倒闸操作命令新增\";\n      this.isDisabled = false;\n      if (this.single) {\n        this.form = this.single;\n        const { data, code } = await queryZb({ objId: this.form.objId });\n        if (code === \"0000\") {\n          this.propTableData.colFirst = data;\n          this.propTableData.colFirst.forEach(e => {\n            e.objId = \"\";\n          });\n        }\n        this.form.objId = \"\";\n        this.form.czp = \"\";\n        this.form.bh = \"\";\n      } else {\n        this.form = {};\n      }\n      this.isShowDetails = true;\n      this.form.status = \"\";\n    },\n    //修改按钮\n    getUpdate(row) {\n      this.getListZb(row);\n      this.title = \"输电倒闸操作命令修改\";\n      this.isDisabled = false;\n      this.form = { ...row };\n      this.isShowDetails = true;\n    },\n    //详情按钮\n    getDetails(row) {\n      this.getListZb(row);\n      this.title = \"输电倒闸操作命令详情\";\n      this.form = { ...row };\n      this.isDisabled = true;\n      this.isShowDetails = true;\n    },\n    //开操作票按钮\n    async createCzp() {\n      await this.$refs[\"form\"].validate(async valid => {\n        if (valid) {\n          try {\n            this.form.colFirst = this.propTableData.colFirst;\n            this.form.objIdList = this.ids;\n            let { code, data } = await saveOrUpdate(this.form);\n            if (code === \"0000\") {\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.isShowDetails = false;\n              await this.getData();\n              this.titleCzp = \"操作票开票\";\n              this.isShowDetailsCzp = true;\n              this.isDisabled = true;\n              this.formCzp = {};\n              //清除校验提示\n              this.$nextTick(function() {\n                this.$refs[\"formCzp\"].clearValidate();\n              });\n              this.formCzp.xlmc = data.xlmc;\n              this.formCzp.gzmc = data.gzmc;\n              this.formCzp.czml = data.objId;\n              this.formCzp.status = \"0\";\n              this.formCzp.fgs = \"3010\";\n              //输电\n              this.formCzp.lx = 1;\n              this.propTableDataCzp.colFirst = this.propTableData.colFirst;\n              this.formCzp.czxs = this.propTableData.colFirst.length;\n              this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n            }\n          } catch (e) {\n            console.log(e);\n          }\n        }\n      });\n    },\n    // 开操作票页 确认按钮\n    async saveRowCzp() {\n      this.formCzp.colFirst = this.propTableDataCzp.colFirst;\n      this.formCzp.objIdList = this.ids;\n      // let tableValid = this.propTableData.colFirst.some(item => !item.czrw)\n      // if (tableValid) {\n      //   this.$message.error(\"操作任务存在空项，请检查\");\n      // }\n      saveOrUpdates(this.formCzp).then(res => {\n        if (res.code === \"0000\") {\n          this.$message.success(\"操作成功\");\n          this.isShowDetailsCzp = false;\n          //重置page页从1开始\n          this.tableAndPageInfo.pager.pageResize = \"Y\";\n          this.getData();\n        }\n      });\n    },\n\n    //直接上报操作票\n    submitCzp() {\n      this.formCzp.colFirst = this.propTableDataCzp.colFirst;\n      this.formCzp.objIdList = this.ids;\n      saveOrUpdateCzp(this.formCzp).then(res => {\n        if (res.code === \"0000\") {\n          //重置page页从1开始\n          this.tableAndPageInfo.pager.pageResize = \"Y\";\n          this.getData();\n          let data = res.data;\n          this.isShowDetailsCzp = false;\n          this.processData.variables.pass = true;\n          this.processData.businessKey = data.objId;\n          this.processData.processType = \"complete\";\n          this.activitiOption.title = \"提交\";\n          this.processData.defaultFrom = true;\n          this.processData.rylx = \"分公司审核人\";\n          this.processData.dw = data.fgs;\n          this.processData.personGroupId = 14;\n          this.processData.routePath = \"/czpgl/xldzcz/dzczp\";\n          this.isShow = true;\n        }\n      });\n    },\n\n    saveRow() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          try {\n            this.form.colFirst = this.propTableData.colFirst;\n            this.form.objIdList = this.ids;\n            saveOrUpdate(this.form).then(res => {\n              if (res.code === \"0000\") {\n                this.$message.success(\"操作成功\");\n              }\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n              this.isShowDetails = false;\n            });\n          } catch (e) {\n            console.log(e);\n          }\n        }\n      });\n    },\n    //删除按钮\n    async deleteRow(row) {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove([row.objId]).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    //表格新增\n    listFirstAdd() {\n      let row = {\n        objId: \"\",\n        czrw: \"\",\n        uuid: getUUID()\n      };\n      this.propTableData.colFirst.push(row);\n      this.propTableData.sel = row;\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //表格删除\n    listFirstDel(index, row) {\n      if (row.objId) {\n        this.ids.push(row.objId);\n      }\n      this.propTableData.colFirst.splice(index, 1);\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //表格新增\n    listFirstAddCzp() {\n      let row = {\n        isSet: true,\n        // xh:'',\n        czrw: \"\",\n        xlr: \"\",\n        xlsj: \"\",\n        hlsj: \"\",\n        sfwc: \"\"\n      };\n      this.propTableDataCzp.colFirst.push(row);\n      this.propTableDataCzp.sel = row;\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //表格删除\n    listFirstDelCzp(index, row) {\n      this.ids.push(row.objId);\n      this.propTableDataCzp.colFirst.splice(index, 1);\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false;\n    },\n    //关闭弹窗\n    closeCzp() {\n      this.isShowDetailsCzp = false;\n    },\n    //选择行\n    selectChange(selection) {\n      this.single = selection.length > 0 ? { ...selection[0] } : undefined;\n      this.ids = selection.map(item => item.objId);\n      // this.single = selection.length !== 1\n      // this.multiple = !selection.length\n      this.selectData = selection;\n    },\n    //重置按钮\n    getReset() {\n      this.params = {\n        bh: \"\",\n        xlmc: \"\",\n        tzsj: \"\",\n        yblxlr: \"\",\n        czrw: \"\",\n        ybljlr: \"\"\n      };\n    },\n    //关闭线路弹窗\n    handleControlSysbSelectDialog(isShow) {\n      this.isShowXlDialog = isShow;\n    },\n    //线路选择事件\n    sysbSelectedClick() {\n      this.isShowXlDialog = true;\n    },\n    //导出word\n    exportWord() {\n      let params = {\n        data: this.params,\n        url: \"bzSddzczml\"\n      };\n      let fileName = \"线路倒闸操作命令记录\";\n      if (!this.selectData.length > 0) {\n        params.data = this.params;\n        exportWordByparams(params, fileName);\n      } else {\n        params.data = this.selectData;\n        exportWordByselection(params, fileName);\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.card-style {\n  background-color: #e0eff2;\n  line-height: 3vh;\n  border-radius: 3px;\n  font-weight: 800;\n  padding-left: 1vw;\n  margin-bottom: 3vh;\n}\n\n/*控制input输入框边框是否显示*/\n.elInput >>> .el-input__inner {\n  border: 0;\n}\n\n.center {\n  text-align: center; /*让div内部文字居中*/\n}\n</style>\n"]}]}