{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\gfdzcz\\components\\fwzz_dj.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\gfdzcz\\components\\fwzz_dj.vue", "mtime": 1751368578944}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCi8v5rWB56iLCmltcG9ydCBhY3Rpdml0aSBmcm9tICJjb20vYWN0aXZpdGkiOwppbXBvcnQgdGltZUxpbmUgZnJvbSAiY29tL3RpbWVMaW5lIjsKaW1wb3J0IHsgSGlzdG9yeUxpc3QgfSBmcm9tICJAL2FwaS9hY3Rpdml0aS9wcm9jZXNzVGFzayI7CmltcG9ydCB7IGdldExpc3RMc3AgfSBmcm9tICJAL2FwaS95eGdsL2dmeXhnbC9nZmR6Y3pwIjsKaW1wb3J0IHsgZ2V0QmR6RGF0YUxpc3RTZWxlY3RlZCBhcyBnZXRCZHpTZWxlY3RMaXN0fSBmcm9tICJAL2FwaS9kYWdhbmdPaWxmaWVsZC9hc3NldC9nZnNidHoiOwppbXBvcnQgewogIGdldExpc3RGd3p6anMsCiAgc2F2ZU9yVXBkYXRlRnd6empzLAogIHJlbW92ZUZ3enpqcwp9IGZyb20gIkAvYXBpL3l4Z2wvZ2Z5eGdsL2dmemJnbCI7CmltcG9ydCBEZXZpY2VUcmVlIGZyb20gIkAvdmlld3MvZGFnYW5nT2lsZmllbGQvYnpnbC9zYmJ6ay9kZXZpY2VUcmVlIjsKLy/mlrDpgInmi6norr7lpIcKaW1wb3J0IHN5c2JTZWxlY3RlZCBmcm9tICJAL3ZpZXdzL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvc3liemsvc3liZ2psY29tbWVudC9zeXNiU2VsZWN0ZWQiOwppbXBvcnQgeyBnZXRGZ3NPcHRpb25zIH0gZnJvbSAiQC9hcGkveXhnbC9nZnl4Z2wvZ2Z6YmdsIjsKCmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiZnd6el9kaiIsCiAgY29tcG9uZW50czogeyBEZXZpY2VUcmVlLCBzeXNiU2VsZWN0ZWQsIGFjdGl2aXRpLCB0aW1lTGluZSB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBmZ3NMaXN0OiBbXSwgLy/liIblhazlj7jkuIvmi4nmoYYKICAgICAgY3VycmVudFVzZXI6IHRoaXMuJHN0b3JlLmdldHRlcnMubmFtZSwKICAgICAgaXNTaG93RGV0YWlsc0N6cDogZmFsc2UsCiAgICAgIHByb3BUYWJsZURhdGFDenA6IHsKICAgICAgICBjb2xGaXJzdDogW10KICAgICAgfSwKICAgICAgaXNEaXNhYmxlZEN6cDogZmFsc2UsCiAgICAgIC8v5Li76K6+5aSH6YCJ5oup5Lyg6YCS5a2Q57uE5Lu25Y+C5pWwCiAgICAgIHNlbGVjdGVkU2JQYXJhbTogewogICAgICAgIGx4OiAiZ2YiLAogICAgICAgIHNibWM6ICIiCiAgICAgIH0sCiAgICAgIC8v5Li76K6+5aSH5by55Ye65qGGCiAgICAgIGlzU2hvd1N5c2JEaWFsb2c6IGZhbHNlLAogICAgICAvL+W3peS9nOa1geW8ueeqlwogICAgICBpc1Nob3c6IGZhbHNlLAogICAgICAvL+W3peS9nOa1geS8oOWFpeWPguaVsAogICAgICBwcm9jZXNzRGF0YTogewogICAgICAgIHByb2Nlc3NEZWZpbml0aW9uS2V5OiAiZnd6empzZ2oiLAogICAgICAgIGJ1c2luZXNzS2V5OiAiIiwKICAgICAgICBidXNpbmVzc1R5cGU6ICLpmLLor6/oo4Xnva7op6PplIHlt6Xlhbfkvb/nlKgiLAogICAgICAgIHZhcmlhYmxlczoge30sCiAgICAgICAgZGVmYXVsdEZyb206IHRydWUsCiAgICAgICAgbmV4dFVzZXI6ICIiLAogICAgICAgIHByb2Nlc3NUeXBlOiAiY29tcGxldGUiCiAgICAgIH0sCiAgICAgIC8v5rWB56iL5Zu+5p+l55yLCiAgICAgIG9wZW5Mb2FkaW5nSW1nOiBmYWxzZSwKICAgICAgaW1nU3JjOiAiIiwgLy/mtYHnqIvlm77mn6XnnIvlnLDlnYAKICAgICAgdGltZURhdGE6IFtdLAogICAgICB0aW1lTGluZVNob3c6IGZhbHNlLAogICAgICAvL+W8ueWHuuahhuagh+mimAogICAgICBhY3Rpdml0aU9wdGlvbjogeyB0aXRsZTogIuS4iuaKpSIgfSwKICAgICAgaXNTaG93WWo6IHRydWUsCiAgICAgIFpiRGlhbG9nRm9ybVZpc2libGU6IGZhbHNlLAoKICAgICAgYmR6TGlzdDogW10sCiAgICAgIGlzRGlzYWJsZWQ6IGZhbHNlLAogICAgICBmb3JtOiB7CiAgICAgICAgbHg6ICIiLAogICAgICAgIHN0YXR1czogIiIKICAgICAgfSwKICAgICAgZm9ybVNoOiB7CiAgICAgICAgd2Z6ejE6ICIiCiAgICAgIH0sCiAgICAgIC8vIOWkmumAieahhumAieS4reeahGlkCiAgICAgIGlkczogW10sCiAgICAgIC8vIOWkmumAieahhumAiemAieS4reeahOaVsOaNrgogICAgICBzZWxlY3REYXRhOiBbXSwKICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgLy8g6Z2e5aSa5Liq56aB55SoCiAgICAgIG11bHRpcGxlOiB0cnVlLAogICAgICAvLyDlr7nor53moYbmoIfpopgKICAgICAgdGl0bGU6ICIiLAogICAgICB0aXRsZXM6ICIiLAogICAgICBpc1Nob3dEZXRhaWxzOiBmYWxzZSwKICAgICAgaXNTaG93QmRmZ3NzaDogZmFsc2UsCiAgICAgIGlzU2hvd0JkZmdzc2hGb3JtOiBmYWxzZSwKICAgICAgLy/mk43kvZznpajliJfooajpgInkuK3lkI4KICAgICAgYXNzZXRJbmZvOiB7fSwKICAgICAgLyoqCiAgICAgICAqICDpmLLor6/oo4Xnva7op6PplIHlt6Xlhbfkvb/nlKjnmbvorrAKICAgICAgICogICovCiAgICAgIGZpbHRlckluZm86IHsKICAgICAgICBkYXRhOiB7CiAgICAgICAgICBkbHFiaDogIiIsCiAgICAgICAgICBzakFycjogW10sCiAgICAgICAgICBkanI6ICIiLAogICAgICAgICAgYmR6OiAiIgogICAgICAgIH0sCiAgICAgICAgZmllbGRMaXN0OiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIGxhYmVsOiAi54q25oCBIiwKICAgICAgICAgICAgdHlwZTogInNlbGVjdCIsCiAgICAgICAgICAgIHZhbHVlOiAic3RhdHVzIiwKICAgICAgICAgICAgY2xlYXJhYmxlOiB0cnVlLAogICAgICAgICAgICBvcHRpb25zOiBbCiAgICAgICAgICAgICAgeyBsYWJlbDogIuW+heS4iuaKpSIsIHZhbHVlOiAi5b6F5LiK5oqlIiB9LAogICAgICAgICAgICAgIHsgbGFiZWw6ICLlvoXkv67mlLkiLCB2YWx1ZTogIuW+heS/ruaUuSIgfQogICAgICAgICAgICBdCiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIuWIhuWFrOWPuCIsCiAgICAgICAgICAgIHR5cGU6ICJzZWxlY3QiLAogICAgICAgICAgICB2YWx1ZTogImZncyIsCiAgICAgICAgICAgIGNoZWNrYm94VmFsdWU6IFtdLAogICAgICAgICAgICBvcHRpb25zOiBbXQogICAgICAgICAgfSwKICAgICAgICAgIHsgbGFiZWw6ICLlhYnkvI/nlLXnq5kiLCB0eXBlOiAic2VsZWN0IiwgdmFsdWU6ICJiZHoiLCBvcHRpb25zOiBbXSB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIuaXtumXtCIsCiAgICAgICAgICAgIHZhbHVlOiAic2pBcnIiLAogICAgICAgICAgICB0eXBlOiAiZGF0ZSIsCiAgICAgICAgICAgIGRhdGVUeXBlOiAiZGF0ZXJhbmdlIiwKICAgICAgICAgICAgZm9ybWF0OiAieXl5eS1NTS1kZCIKICAgICAgICAgIH0sCiAgICAgICAgICB7IGxhYmVsOiAi6K6+5aSH5ZCN56ewIiwgdHlwZTogImlucHV0IiwgdmFsdWU6ICJzYm1jIiB9CiAgICAgICAgICAvKnsgbGFiZWw6ICflt6XkvZzku7vliqEnLCB0eXBlOiAnaW5wdXQnLCB2YWx1ZTogJ2d6cncnIH0qLwogICAgICAgIF0KICAgICAgfSwKICAgICAgdGFibGVBbmRQYWdlSW5mbzogewogICAgICAgIHBhZ2VyOiB7CiAgICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgICAgdG90YWw6IDAsCiAgICAgICAgICBzaXplczogWzEwLCAyMCwgNTAsIDEwMF0KICAgICAgICB9LAogICAgICAgIG9wdGlvbjogewogICAgICAgICAgY2hlY2tCb3g6IHRydWUsCiAgICAgICAgICBzZXJpYWxOdW1iZXI6IHRydWUKICAgICAgICB9LAogICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgdGFibGVIZWFkZXI6IFsKICAgICAgICAgIHsgcHJvcDogInN0YXR1cyIsIGxhYmVsOiAi54q25oCBIiwgbWluV2lkdGg6ICI2MCIgfSwKICAgICAgICAgIHsgcHJvcDogImZnc0NuIiwgbGFiZWw6ICLliIblhazlj7giLCBtaW5XaWR0aDogIjgwIiB9LAogICAgICAgICAgeyBwcm9wOiAiYmR6bWMiLCBsYWJlbDogIuWFieS8j+eUteermSIsIG1pbldpZHRoOiAiODAiIH0sCiAgICAgICAgICB7IHByb3A6ICJzaiIsIGxhYmVsOiAi5pe26Ze0IiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IHByb3A6ICJzYm1jIiwgbGFiZWw6ICLorr7lpIflkI3np7AiLCBtaW5XaWR0aDogIjEwMCIgfQogICAgICAgICAgLyp7IHByb3A6ICdnenJ3JywgbGFiZWw6ICflt6XkvZzku7vliqEnLCBtaW5XaWR0aDogJzEzMCcgfSwqLwogICAgICAgICAgLyp7CiAgICAgICAgICAgIHByb3A6ICdvcGVyYXRpb24nLAogICAgICAgICAgICBsYWJlbDogJ+aTjeS9nCcsCiAgICAgICAgICAgIGZpeGVkOiAncmlnaHQnLAogICAgICAgICAgICBtaW5XaWR0aDogJzExMHB4JywKICAgICAgICAgICAgc3R5bGU6IHsgZGlzcGxheTogJ2Jsb2NrJyB9LAogICAgICAgICAgICBvcGVyYXRpb246IFsKICAgICAgICAgICAgICB7IG5hbWU6ICfkv67mlLknLCBjbGlja0Z1bjogdGhpcy51cGRhdGVSb3cgfSwKICAgICAgICAgICAgICB7IG5hbWU6ICfor6bmg4UnLCBjbGlja0Z1bjogdGhpcy5nZXREZXRhaWxzIH0sCiAgICAgICAgICAgICAgeyBuYW1lOiAn5o+Q5LqkJywgY2xpY2tGdW46IHRoaXMuZ2V0U2IgfQogICAgICAgICAgICAgIC8hKnsgbmFtZTogJ+mZhOS7tuafpeeciycsIGNsaWNrRnVuOiB0aGlzLkZqSW5mb0xpc3QgfSwqIS8KICAgICAgICAgICAgXQogICAgICAgICAgfSovCiAgICAgICAgXQogICAgICB9LAogICAgICBwYXJhbXM6IHsKICAgICAgICBseDogMQogICAgICB9LAogICAgICBwYXJhbXNDenA6IHsKICAgICAgICBseDogMiwKICAgICAgICAvKnN0YXR1czogJ+W3suWKnue7kycsKi8KICAgICAgICBzZmJqOiAxCiAgICAgIH0sCiAgICAgIHJ1bGVzOiB7CiAgICAgICAgZmdzOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWIhuWFrOWPuOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJzZWxlY3QiIH1dLAogICAgICAgIGJkejogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlhYnkvI/nlLXnq5nkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAic2VsZWN0IiB9XSwKICAgICAgICBzajogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLml7bpl7TkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9XSwKICAgICAgICBzYm1jOiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5beh6KeG57G75Yir5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICBdLAogICAgICAgIGd6cnc6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlt6XkvZzku7vliqHkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQogICAgICAgIF0sCiAgICAgICAgc3l5eTogWwogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuS9v+eUqOWOn+WboOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgXSwKICAgICAgICBqbHI6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K6w5b2V5Lq65LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH1dLAogICAgICAgIGpsc2o6IFsKICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLorrDlvZXml7bpl7TkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9CiAgICAgICAgXQogICAgICB9LAogICAgICBqbHJMaXN0OiBbXSAvL+iusOW9leS6uuS4i+aLieahhgogICAgfTsKICB9LAogIGNyZWF0ZWQoKSB7CiAgICAvL+WIl+ihqOafpeivogogICAgdGhpcy5nZXREYXRhKCk7CiAgICAvL+iOt+WPluWFieS8j+eUteermeS4i+aLieahhuaVsOaNrgogICAgLy8gdGhpcy5nZXRCZHpTZWxlY3RMaXN0KCkKICAgIC8v6I635Y+W5YiG5YWs5Y+45LiL5ouJ5qGGCiAgICB0aGlzLmdldEZnc0xpc3QoKTsKICB9LAoKICBtZXRob2RzOiB7CiAgICAvL+S4i+aLieahhmNoYW5nZeS6i+S7tgogICAgaGFuZGxlRXZlbnQodmFsKSB7CiAgICAgIC8v5YWJ5LyP55S156uZ5p+l6K+i5LiL5ouJ5qGG6YCJ6aG55qC55o2u5omA6YCJ5YiG5YWs5Y+45bim5Ye65p2lCiAgICAgIGlmICh2YWwubGFiZWwgPT09ICJmZ3MiICYmIHZhbC52YWx1ZSAmJiB2YWwudmFsdWUgIT09ICIiKSB7CiAgICAgICAgbGV0IGZvcm0gPSB7CiAgICAgICAgICBzc2R3Ym06IHZhbC52YWx1ZQogICAgICAgIH07CiAgICAgICAgZ2V0QmR6U2VsZWN0TGlzdChmb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICB0aGlzLmZpbHRlckluZm8uZmllbGRMaXN0Lm1hcChpdGVtID0+IHsKICAgICAgICAgICAgaWYgKGl0ZW0udmFsdWUgPT09ICJiZHoiKSB7CiAgICAgICAgICAgICAgcmV0dXJuIChpdGVtLm9wdGlvbnMgPSByZXMuZGF0YSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgLyoqCiAgICAgKiDojrflj5blhYnkvI/nlLXnq5nkuIvmi4nmoYbmlbDmja4KICAgICAqLwogICAgZ2V0QmR6TGlzdCh2YWwpIHsKICAgICAgZ2V0QmR6U2VsZWN0TGlzdCh7IHNzZHdibTogdmFsIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmJkekxpc3QgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLy/ojrflj5bliIblhazlj7jkuIvmi4nmoYYKICAgIGdldEZnc0xpc3QoKSB7CiAgICAgIGdldEZnc09wdGlvbnMoe30pLnRoZW4ocmVzID0+IHsKICAgICAgICByZXMuZGF0YS5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgaXRlbS52YWx1ZSA9IGl0ZW0udmFsdWUudG9TdHJpbmcoKTsKICAgICAgICB9KTsKICAgICAgICB0aGlzLmZnc0xpc3QgPSByZXMuZGF0YTsKICAgICAgICB0aGlzLmZpbHRlckluZm8uZmllbGRMaXN0Lm1hcChpdGVtID0+IHsKICAgICAgICAgIGlmIChpdGVtLnZhbHVlID09ICJmZ3MiKSB7CiAgICAgICAgICAgIHJldHVybiAoaXRlbS5vcHRpb25zID0gdGhpcy5mZ3NMaXN0KTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9LAogICAgYXN5bmMgZmdzQ2hhbmdlRnVuKHZhbCkgewogICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLCAiYmR6IiwgIiIpOwogICAgICBhd2FpdCB0aGlzLmdldEJkekxpc3QodmFsKTsKICAgIH0sCiAgICAvL+S/neWtmOmAieS4reeahOaTjeS9nOelqAogICAgc2F2ZVJvd0N6cCgpIHsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzQ3pwID0gZmFsc2U7CiAgICAgIHRoaXMuZm9ybS5iZHogPSB0aGlzLmFzc2V0SW5mby5iZHptYzsKICAgICAgdGhpcy5mb3JtLmd6cncgPSB0aGlzLmFzc2V0SW5mby5jenJ3OwogICAgICB0aGlzLmJkekxpc3QgPSBbCiAgICAgICAgeyBsYWJlbDogdGhpcy5hc3NldEluZm8uYmR6bWNzLCB2YWx1ZTogdGhpcy5hc3NldEluZm8uYmR6bWMgfQogICAgICBdOwogICAgfSwKICAgIC8v6KGM6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2VzKHJvd3MpIHsKICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLpgInkuK3nmoTmlbDmja4iKTsKICAgICAgaWYgKHJvd3MubGVuZ3RoID4gMSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi5Y+q6IO96YCJ5Lit5LiA5p2h5pWw5o2u77yB77yB77yBIik7CiAgICAgICAgLy/muIXnqbrpgInkuK3mlrnms5UKICAgICAgICBpZiAocm93cykgewogICAgICAgICAgcm93cy5mb3JFYWNoKHJvdyA9PiB7CiAgICAgICAgICAgIHRoaXMuJHJlZnMuY3pwVGFibGUudG9nZ2xlUm93U2VsZWN0aW9uKHJvdyk7CiAgICAgICAgICB9KTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kcmVmcy5jenBUYWJsZS5jbGVhclNlbGVjdGlvbigpOwogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICAvL+iOt+WPluW9k+WJjemAieS4reeahOWNleadoeaVsOaNrgogICAgICAgIHRoaXMuYXNzZXRJbmZvID0gcm93c1swXTsKICAgICAgfQogICAgfSwKICAgIHNob3dBc3NldFNlbGVjdCgpIHsKICAgICAgLy8gdGhpcy5hc3NldFNlbGVjdCA9IHRydWU7CiAgICAgIGlmICh0aGlzLmZvcm0uYmR6ID09PSAiIiB8fCB0aGlzLmZvcm0uYmR6ID09PSB1bmRlZmluZWQpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+mAieaLqeWFieS8j+eUteermeWQjeensCIpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICAvLyDmi7/liLDlhYnkvI/nlLXnq5nmsYnlrZcKICAgICAgbGV0IHNibWNtYyA9ICIiOwogICAgICB0aGlzLmJkekxpc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICBpZiAoaXRlbS52YWx1ZSA9PSB0aGlzLmZvcm0uYmR6KSB7CiAgICAgICAgICBzYm1jbWMgPSBpdGVtLmxhYmVsOwogICAgICAgIH0KICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0pOwogICAgICB0aGlzLnNlbGVjdGVkU2JQYXJhbS5zYm1jID0gc2JtY21jOyAvL+aJgOWxnuS9jee9rgogICAgICB0aGlzLmlzU2hvd1N5c2JEaWFsb2cgPSB0cnVlOwogICAgfSwKICAgIGNsb3NlQXNzZXRTZWxlY3QoKSB7CiAgICAgIHRoaXMuaXNTaG93U3lzYkRpYWxvZyA9IGZhbHNlOwogICAgfSwKICAgIGdldEFzc2V0SW5mbyhpbmZvKSB7CiAgICAgIHRoaXMuZm9ybS5zYm1jID0gaW5mby5zYm1jOyAvL+S4u+iuvuWkh+WQjeensAogICAgfSwKICAgIC8v5riF56m6CiAgICBjaGFuZ2UoZSkgewogICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpOwogICAgfSwKICAgIC8v5YWz6Zet5by556qXCiAgICBjbG9zZUFjdGl2aXRpKCkgewogICAgICB0aGlzLmlzU2hvdyA9IGZhbHNlOwogICAgfSwKICAgIC8v5Zue6YCA5oyJ6ZKuCiAgICBoYW5kbGVUaCh0eXBlKSB7CiAgICAgIHRoaXMuZ2V0U2JGc0JqKHsgdHlwZTogdHlwZSwgZGF0YTogdGhpcy5mb3JtIH0sIHsgZGVmYXVsdEZvcm06IGZhbHNlIH0pOwogICAgfSwKICAgIC8v5YWz6Zet5rWB56iL5p+l55yL6aG16Z2iCiAgICBjb2xzZVRpbWVMaW5lKCkgewogICAgICB0aGlzLnRpbWVMaW5lU2hvdyA9IGZhbHNlOwogICAgfSwKICAgIC8v5rWB56iL5p+l55yLCiAgICBhc3luYyBzaG93VGltZUxpbmUocm93KSB7CiAgICAgIHRoaXMucHJvY2Vzc0RhdGEuYnVzaW5lc3NLZXkgPSByb3cub2JqSWQ7CiAgICAgIGxldCB7IGNvZGUsIGRhdGEgfSA9IGF3YWl0IEhpc3RvcnlMaXN0KHRoaXMucHJvY2Vzc0RhdGEpOwogICAgICB0aGlzLnRpbWVEYXRhID0gZGF0YTsKICAgICAgdGhpcy50aW1lTGluZVNob3cgPSB0cnVlOwogICAgfSwKICAgIC8v5rWB56iL5Zu+54mH5p+l55yLCiAgICBzaG93UHJvY2Vzc0ltZyhyb3cpIHsKICAgICAgdGhpcy5vcGVuTG9hZGluZ0ltZyA9IHRydWU7CiAgICAgIHRoaXMuaW1nU3JjID0KICAgICAgICAiL2FjdGl2aXRpLWFwaS9wcm9jZXNzL3JlYWQtcmVzb3VyY2U/cHJvY2Vzc0RlZmluaXRpb25LZXk9Znd6empzZ2omYnVzaW5lc3NLZXk9IiArCiAgICAgICAgcm93Lm9iaklkICsKICAgICAgICAiJnQ9IiArCiAgICAgICAgbmV3IERhdGUoKS5nZXRUaW1lKCk7CiAgICB9LAogICAgLy/lt6XkvZzmtYHlm57kvKDmlbDmja4KICAgIGFzeW5jIHRvZG9SZXN1bHQoZGF0YSkgewogICAgICBsZXQgcm93ID0ge307CiAgICAgIHJvdyA9IHsgb2JqSWQ6IGRhdGEuYnVzaW5lc3NLZXksIHN0YXR1czogIuW+heWIhuWFrOWPuOS6lOmYsuS4k+i0o+WuoeaguCIsIGx4OiAyIH07CiAgICAgIHNhdmVPclVwZGF0ZUZ3enpqcyhyb3cpLnRoZW4ocmVzID0+IHsKICAgICAgICBpZiAocmVzLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLmk43kvZzmiJDlip8iKTsKICAgICAgICAgIC8v6YeN572ucGFnZemhteS7jjHlvIDlp4sKICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci5wYWdlUmVzaXplID0gIlkiOwogICAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKioKICAgICAqIOagueaNruihqOagvOWQjeensOiOt+WPluWvueW6lOeahOaVsOaNrgogICAgICovCiAgICBhc3luYyBnZXREYXRhKHBhcmFtcykgewogICAgICB0cnkgewogICAgICAgIHRoaXMucGFyYW1zID0geyAuLi50aGlzLnBhcmFtcywgLi4ucGFyYW1zIH07CiAgICAgICAgY29uc3QgcGFyYW0gPSB0aGlzLnBhcmFtczsKICAgICAgICBjb25zdCB7IGRhdGEsIGNvZGUgfSA9IGF3YWl0IGdldExpc3RGd3p6anMocGFyYW0pOwogICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby50YWJsZURhdGEgPSBkYXRhLnJlY29yZHM7CiAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIudG90YWwgPSBkYXRhLnRvdGFsOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZSkge30KICAgIH0sCiAgICAvKioKICAgICAqIOWFs+iBlOaTjeS9nOelqAogICAgICogKi8KICAgIGFzeW5jIGdldEN6cCgpIHsKICAgICAgLyppZiAodGhpcy5mb3JtLmJkeikgewogICAgICAgIHRoaXMuaXNTaG93RGV0YWlsc0N6cCA9IHRydWUKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmlzU2hvd0RldGFpbHNDenAgPSBmYWxzZQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+35YWI6YCJ5oup5YWJ5LyP55S156uZ77yB77yB77yBIikKICAgICAgfSovCiAgICAgIHRoaXMuaXNTaG93RGV0YWlsc0N6cCA9IHRydWU7CiAgICAgIHRoaXMuaXNEaXNhYmxlZEN6cCA9IHRydWU7CiAgICAgIHRoaXMudGl0bGUgPSAi6YCJ5oup5pON5L2c56WoIjsKICAgICAgbGV0IHBhcmFtID0gdGhpcy5wYXJhbXNDenA7CiAgICAgIGxldCBkYXRhID0gYXdhaXQgZ2V0TGlzdExzcChwYXJhbSk7CiAgICAgIHRoaXMucHJvcFRhYmxlRGF0YUN6cC5jb2xGaXJzdCA9IGRhdGEuZGF0YS5yZWNvcmRzOwogICAgfSwKICAgIC8qKgogICAgICog5paw5aKeCiAgICAgKi8KICAgIGFkZFJvdygpIHsKICAgICAgdGhpcy5mb3JtID0ge307CiAgICAgIHRoaXMuZm9ybS5zdGF0dXMgPSAi5paw5bu6IjsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gdHJ1ZTsKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gZmFsc2U7CiAgICAgIHRoaXMudGl0bGVzID0gIumYsuivr+ijhee9ruino+mUgeW3peWFt+S9v+eUqOeZu+iusCI7CiAgICAgIHRoaXMuaXNTaG93WWogPSBmYWxzZTsKICAgICAgLy8g5ou/5Yiw5pel5pyf44CB5pif5pyf44CB5aSp5rCUCiAgICAgIGxldCBub3cgPSBuZXcgRGF0ZSgpOwogICAgICBsZXQgeWVhciA9IG5vdy5nZXRGdWxsWWVhcigpOyAvL+W+l+WIsOW5tOS7vQogICAgICBsZXQgbW9udGggPSBub3cuZ2V0TW9udGgoKTsgLy/lvpfliLDmnIjku70KICAgICAgbGV0IGRhdGUgPSBub3cuZ2V0RGF0ZSgpOyAvL+W+l+WIsOaXpeacnwogICAgICBsZXQgaG91ciA9IG5vdy5nZXRIb3VycygpOwogICAgICBsZXQgbWludXRlcyA9IG5vdy5nZXRNaW51dGVzKCk7CiAgICAgIGxldCBzZWNvbmRzID0gbm93LmdldFNlY29uZHMoKTsKICAgICAgbW9udGggPSBtb250aCArIDE7CiAgICAgIG1vbnRoID0gbW9udGgudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAiMCIpOwogICAgICBkYXRlID0gZGF0ZS50b1N0cmluZygpLnBhZFN0YXJ0KDIsICIwIik7CiAgICAgIGlmIChob3VyLnRvU3RyaW5nKCkubGVuZ3RoICE9IDIpIHsKICAgICAgICAvLyDmi7zmjqXml7bvvIzlpoLmnpzplb/luqbkuLox77yM5L+d5a2Y5oql6ZSZCiAgICAgICAgaG91ciA9ICIwIiArIGhvdXI7CiAgICAgIH0KICAgICAgaWYgKG1pbnV0ZXMudG9TdHJpbmcoKS5sZW5ndGggIT0gMikgewogICAgICAgIC8vIOaLvOaOpeWIhumSn++8jOWmguaenOmVv+W6puS4ujHvvIzkv53lrZjmiqXplJkKICAgICAgICBtaW51dGVzID0gIjAiICsgbWludXRlczsKICAgICAgfQogICAgICBpZiAoc2Vjb25kcy50b1N0cmluZygpLmxlbmd0aCAhPSAyKSB7CiAgICAgICAgLy8g5ou85o6l56eS77yM5aaC5p6c6ZW/5bqm5Li6Me+8jOS/neWtmOaKpemUmQogICAgICAgIHNlY29uZHMgPSAiMCIgKyBzZWNvbmRzOwogICAgICB9CiAgICAgIGxldCBkZWZhdWx0RGF0ZSA9IGAke3llYXJ9LSR7bW9udGh9LSR7ZGF0ZX0gJHtob3VyfToke21pbnV0ZXN9OiR7c2Vjb25kc31gOwogICAgICB0aGlzLmZvcm0uamxzaiA9IGRlZmF1bHREYXRlOwogICAgICAvL+iOt+WPluW9k+WJjeeZu+W9leeUqOaIt+WvueixoQogICAgICBsZXQgbG9naW5Vc2VyID0gdGhpcy4kc3RvcmUuZ2V0dGVyczsKICAgICAgdGhpcy5mb3JtLmpsciA9IGxvZ2luVXNlci5uYW1lOwogICAgICB0aGlzLmpsckxpc3QgPSBbeyBsYWJlbDogbG9naW5Vc2VyLm5pY2tOYW1lLCB2YWx1ZTogbG9naW5Vc2VyLm5hbWUgfV07CiAgICB9LAogICAgLyoqCiAgICAgKiDkv67mlLkKICAgICAqLwogICAgdXBkYXRlUm93KHJvdykgewogICAgICB0aGlzLnRpdGxlcyA9ICLkv67mlLnpmLLor6/oo4Xnva7op6PplIHlt6Xlhbfkvb/nlKjnmbvorrAiOwogICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZTsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gdHJ1ZTsKICAgICAgdGhpcy5mb3JtID0geyAuLi5yb3cgfTsKICAgICAgaWYgKHRoaXMuZm9ybS5zdGF0dXMgPT0gIuW3suS/ruaUuSIpIHsKICAgICAgICB0aGlzLmlzU2hvd1lqID0gdHJ1ZTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmlzU2hvd1lqID0gZmFsc2U7CiAgICAgIH0KICAgICAgdGhpcy5iZHpMaXN0ID0gW3sgbGFiZWw6IHJvdy5iZHptYywgdmFsdWU6IHJvdy5iZHogfV07CiAgICAgIHRoaXMuamxyTGlzdCA9IFt7IGxhYmVsOiByb3cuamxyQ24sIHZhbHVlOiByb3cuamxyIH1dOwogICAgfSwKICAgIC8qKgogICAgICog6K+m5oOFCiAgICAgKi8KICAgIGdldERldGFpbHMocm93KSB7CiAgICAgIHRoaXMudGl0bGVzID0gIuivpuaDheafpeeci+mYsuivr+ijhee9ruino+mUgeW3peWFt+S9v+eUqOeZu+iusCI7CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IHRydWU7CiAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IHRydWU7CiAgICAgIHRoaXMuZm9ybSA9IHsgLi4ucm93IH07CiAgICAgIGlmICh0aGlzLmZvcm0uc3RhdHVzID09ICLlt7Lkv67mlLkiKSB7CiAgICAgICAgdGhpcy5pc1Nob3dZaiA9IHRydWU7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5pc1Nob3dZaiA9IGZhbHNlOwogICAgICB9CiAgICAgIHRoaXMuYmR6TGlzdCA9IFt7IGxhYmVsOiByb3cuYmR6bWMsIHZhbHVlOiByb3cuYmR6IH1dOwogICAgICB0aGlzLmpsckxpc3QgPSBbeyBsYWJlbDogcm93LmpsckNuLCB2YWx1ZTogcm93LmpsciB9XTsKICAgIH0sCiAgICAvKioKICAgICAqIOaWsOWinuahhuS4iuaKpQogICAgICogKi8KICAgIGFzeW5jIGdldFNiRm9ybSgpIHsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIHRyeSB7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuZGVmYXVsdEZyb20gPSB0cnVlOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmp4Z3MgPSBmYWxzZTsKICAgICAgICAgICAgc2F2ZU9yVXBkYXRlRnd6empzKHRoaXMuZm9ybSkudGhlbihyZXMgPT4gewogICAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKnyIpOwogICAgICAgICAgICAgICAgdGhpcy5nZXRTYkZzQmooCiAgICAgICAgICAgICAgICAgIHsgZGF0YTogcmVzLmRhdGEsIHR5cGU6ICJjb21wbGV0ZUJ5R3JvdXAiIH0sCiAgICAgICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgICAgICBkZWZhdWx0Rm9ybTogdGhpcy5wcm9jZXNzRGF0YS5kZWZhdWx0RnJvbSwKICAgICAgICAgICAgICAgICAgICBqeGdzOiB0aGlzLnByb2Nlc3NEYXRhLmp4Z3MsCiAgICAgICAgICAgICAgICAgICAgcGVyc29uR3JvdXBJZDogMTgKICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgLy/mgaLlpI3liIbpobUKICAgICAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIucGFnZVJlc2l6ZSA9ICJZIjsKICAgICAgICAgICAgICAvL+mHjeaWsOafpeaVsOaNrgogICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICAgIHRoaXMuaXNTaG93QmRmZ3NzaCA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0gY2F0Y2ggKGUpIHt9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuagoemqjOacqumAmui/h++8gSIpOwogICAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy/kuIrmiqXlj5HpgIHlip7nu5MKICAgIGdldFNiRnNCaihhcmdzLCBpc1Nob3cpIHsKICAgICAgbGV0IHJvdyA9IHsgLi4uYXJncy5kYXRhIH07CiAgICAgIGlmIChhcmdzLnR5cGUgPT09ICJjb21wbGV0ZSIpIHsKICAgICAgICB0aGlzLmFjdGl2aXRpT3B0aW9uLnRpdGxlID0gIuWPkemAgee7meWIhuWFrOWPuOS6lOmYsuS4k+i0o+WuoeaguCI7CiAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5kZWZhdWx0RnJvbSA9IGlzU2hvdy5kZWZhdWx0Rm9ybTsgLy/mmK/lkKbmmL7npLrpgInkurrlkZgKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnByb2Nlc3NUeXBlID0gImNvbXBsZXRlIjsKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmp4Z3MgPSBpc1Nob3cuanhnczsKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmJ1c2luZXNzS2V5ID0gcm93Lm9iaklkOwogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnBhc3MgPSB0cnVlOwogICAgICB9IGVsc2UgaWYgKGFyZ3MudHlwZSA9PT0gImNvbXBsZXRlQnlHcm91cCIpIHsKICAgICAgICBpZiAodGhpcy5mb3JtLnN0YXR1cyA9PSAi5paw5bu6IikgewogICAgICAgICAgdGhpcy5hY3Rpdml0aU9wdGlvbi50aXRsZSA9ICLlj5HpgIHnu5nliIblhazlj7jkupTpmLLkuJPotKPlrqHmoLgiOwogICAgICAgIH0KICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmRlZmF1bHRGcm9tID0gaXNTaG93LmRlZmF1bHRGb3JtOyAvL+aYr+WQpuaYvuekuumAieS6uuWRmAogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEucHJvY2Vzc1R5cGUgPSAiY29tcGxldGVCeUdyb3VwIjsKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmp4Z3MgPSBpc1Nob3cuanhnczsKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLmJ1c2luZXNzS2V5ID0gcm93Lm9iaklkOwogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnBhc3MgPSB0cnVlOwogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnBlcnNvbkdyb3VwSWQgPSBpc1Nob3cucGVyc29uR3JvdXBJZDsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmFjdGl2aXRpT3B0aW9uLnRpdGxlID0gIuWbnumAgOWOn+WboOaPkOaKpSI7CiAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5kZWZhdWx0RnJvbSA9IHRydWU7CiAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5wcm9jZXNzVHlwZSA9ICJyb2xsYmFjayI7CiAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5idXNpbmVzc0tleSA9IHJvdy5vYmpJZDsKICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnZhcmlhYmxlcy5wYXNzID0gdHJ1ZTsKICAgICAgfQogICAgICB0aGlzLmlzU2hvdyA9IHRydWU7CiAgICB9LAogICAgLyoqCiAgICAgKiDliKDpmaTmjInpkq4KICAgICAqLwogICAgYXN5bmMgaGFuZGxlRGVsZXRlKHJvdykgewogICAgICB0aGlzLiRjb25maXJtKCLmraTmk43kvZzlsIbmsLjkuYXliKDpmaTor6XmlbDmja4sIOaYr+WQpue7p+e7rT8iLCAi5o+Q56S6IiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkKICAgICAgICAudGhlbigoKSA9PiB7CiAgICAgICAgICAvL+mYsuivr+ijhee9ruino+mUgeW3peWFt+S9v+eUqOeZu+iusAogICAgICAgICAgcmVtb3ZlRnd6empzKHJvdy5vYmpJZCkudGhlbigoeyBjb2RlIH0pID0+IHsKICAgICAgICAgICAgaWYgKGNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLAogICAgICAgICAgICAgICAgbWVzc2FnZTogIuWIoOmZpOaIkOWKnyEiCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICB0eXBlOiAiZXJyb3IiLAogICAgICAgICAgICAgICAgbWVzc2FnZTogIuWIoOmZpOWksei0pSEiCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0pCiAgICAgICAgLmNhdGNoKCgpID0+IHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICB0eXBlOiAiaW5mbyIsCiAgICAgICAgICAgIG1lc3NhZ2U6ICLlt7Llj5bmtojliKDpmaQiCiAgICAgICAgICB9KTsKICAgICAgICB9KTsKICAgIH0sCiAgICAvKioKICAgICAqIOaPkOS6pCDpmLLor6/oo4Xnva7op6PplIHlt6Xlhbfkvb/nlKjnmbvorrAg6KGo5Y2VCiAgICAgKi8KICAgIGFzeW5jIHN1Ym1pdEZvcm1Gd3p6anMoKSB7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB0cnkgewogICAgICAgICAgICB0aGlzLmZvcm0ud2Z6ejEgPSB0aGlzLmZvcm1TaC53Znp6MTsKICAgICAgICAgICAgdGhpcy5mb3JtLmx4ID0gMTsKICAgICAgICAgICAgdGhpcy5mb3JtLnN0YXR1cyA9ICLlvoXkuIrmiqUiOwogICAgICAgICAgICBzYXZlT3JVcGRhdGVGd3p6anModGhpcy5mb3JtKS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5pON5L2c5oiQ5YqfIik7CiAgICAgICAgICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gZmFsc2U7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBjYXRjaCAoZSkge30KICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5qCh6aqM5pyq6YCa6L+H77yBIik7CiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvL+WFs+mXreaMiemSrgogICAgaGFuZGxlQ2xvc2UoKSB7CiAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IGZhbHNlOwogICAgICB0aGlzLmlzU2hvd0JkZmdzc2ggPSBmYWxzZTsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzQ3pwID0gZmFsc2U7CiAgICB9LAogICAgLyoqCiAgICAgKiDlpJrpgInmrL7pgInkuK3mlbDmja4KICAgICAqIEBwYXJhbSByb3cKICAgICAqLwogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLm9iaklkKTsKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOwogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7CiAgICAgIHRoaXMuc2VsZWN0RGF0YSA9IHNlbGVjdGlvbjsKICAgIH0sCiAgICBmaWx0ZXJSZXNldCgpIHsKICAgICAgdGhpcy5wYXJhbXMgPSB7CiAgICAgICAgbHg6IDEKICAgICAgfTsKICAgIH0sCiAgICAvL+WvvOWHundvcmQKICAgIGV4cG9ydFdvcmQoKSB7CiAgICAgIGlmICghdGhpcy5zZWxlY3REYXRhLmxlbmd0aCA+IDApIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+WFiOmAieS4reimgeWvvOWHuueahOaVsOaNriIpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB0cnkgewogICAgICAgIGxldCBmaWxlTmFtZSA9ICLpmLLor6/oo4Xnva7op6PplIHlt6Xlhbfkvb/nlKjorrDlvZUiOwogICAgICAgIGxldCBleHBvcnRVcmwgPSAieXhGd3p6anNnanN5amwiOwogICAgICAgIGxldCBwYXJhbXMgPSB7CiAgICAgICAgICBkYXRhOiB0aGlzLnNlbGVjdERhdGEsCiAgICAgICAgICB1cmw6IGV4cG9ydFVybAogICAgICAgIH07CiAgICAgICAgZXhwb3J0V29yZChwYXJhbXMsIGZpbGVOYW1lKTsKICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuWvvOWHuuWksei0pe+8gSIpOwogICAgICB9CiAgICB9LAogICAgLyoqCiAgICAgKiDojrflj5blhYnkvI/nlLXnq5nkuIvmi4nmoYbmlbDmja4KICAgICAqLwogICAgZ2V0QmR6U2VsZWN0TGlzdCgpIHsKICAgICAgZ2V0QmR6U2VsZWN0TGlzdCh7fSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuYmR6TGlzdCA9IHJlcy5kYXRhOwogICAgICAgIHRoaXMuZmlsdGVySW5mby5maWVsZExpc3QubWFwKGl0ZW0gPT4gewogICAgICAgICAgaWYgKGl0ZW0udmFsdWUgPT0gImJkeiIpIHsKICAgICAgICAgICAgcmV0dXJuIChpdGVtLm9wdGlvbnMgPSB0aGlzLmJkekxpc3QpOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICAvL+ivlemqjOiuvuWkh+mAieaLqeS6i+S7tgogICAgc3lzYlNlbGVjdGVkQ2xpY2soKSB7CiAgICAgIHRoaXMuaXNTaG93U3lzYkRpYWxvZyA9IHRydWU7CiAgICB9LAogICAgLy/nu4Tku7bmjqXlj5forr7lpIflj4LmlbDmlbDmja4KICAgIGhhbmRsZUFjY2VwdFNiRGF0YShzYkRhdGEpIHsKICAgICAgdGhpcy5mb3JtLnNibWMgPSBzYkRhdGEuc2JtYzsKICAgICAgdGhpcy5mb3JtLnN5c2JpZCA9IHNiRGF0YS5vYmpJZDsKICAgIH0sCiAgICAvL+aOp+WItuWFs+mXreivlemqjOiuvuWkh+W8ueWHuuahhgogICAgaGFuZGxlQ29udHJvbFN5c2JTZWxlY3REaWFsb2coaXNTaG93KSB7CiAgICAgIHRoaXMuaXNTaG93U3lzYkRpYWxvZyA9IGlzU2hvdzsKICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["fwzz_dj.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2WA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "fwzz_dj.vue", "sourceRoot": "src/views/dagangOilfield/czpgl/gfdzcz/components", "sourcesContent": ["<template>\n  <div>\n    <!--搜索条件-->\n    <el-filter\n      ref=\"filter1\"\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n      @handleReset=\"filterReset\"\n      @handleEvent=\"handleEvent\"\n    />\n\n    <el-white class=\"button-group\">\n      <div style=\"height: 50px\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          v-hasPermi=\"['fwzz:button:add']\"\n          @click=\"addRow\"\n          >新增</el-button\n        >\n        <!--<el-button type=\"danger\" icon=\"el-icon-plus\" @click=\"handleDelete\">删除</el-button>-->\n        <el-button type=\"success\" icon=\"el-icon-download\" @click=\"exportWord\"\n          >导出</el-button\n        >\n      </div>\n      <div>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"handleSelectionChange\"\n          height=\"58vh\"\n        >\n          <el-table-column\n            slot=\"table_eight\"\n            align=\"center\"\n            fixed=\"right\"\n            style=\"display: block\"\n            label=\"操作\"\n            width=\"160\"\n            :resizable=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                @click=\"getDetails(scope.row)\"\n                title=\"详情\"\n                class=\"el-icon-view\"\n              ></el-button>\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                v-show=\"scope.row.createBy == currentUser\"\n                @click=\"updateRow(scope.row)\"\n                title=\"编辑\"\n                class=\"el-icon-edit\"\n              ></el-button>\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                v-show=\"\n                  scope.row.createBy == currentUser ||\n                    'admin' === $store.getters.name\n                \"\n                @click=\"handleDelete(scope.row)\"\n                title=\"删除\"\n                class=\"el-icon-delete\"\n              ></el-button>\n              <!--<el-button type=\"text\" size=\"small\" @click=\"showTimeLine(scope.row)\">流程查看</el-button>\n              <el-button type=\"text\" size=\"small\" @click=\"showProcessImg(scope.row)\">流程图</el-button>-->\n            </template>\n          </el-table-column>\n        </comp-table>\n      </div>\n    </el-white>\n\n    <!--防误装置解锁工具使用登记-->\n    <el-dialog\n      :title=\"titles\"\n      :visible.sync=\"isShowDetails\"\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"130px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row>\n          <el-col :span=\"8\">\n            <el-form-item label=\"状态\" prop=\"status\">\n              <el-input\n                style=\"width:100%\"\n                :disabled=\"true\"\n                v-model=\"form.status\"\n              >\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"分公司\" prop=\"fgs\">\n              <el-select\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.fgs\"\n                placeholder=\"请选择分公司\"\n                @change=\"fgsChangeFun\"\n              >\n                <el-option\n                  v-for=\"item in fgsList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"光伏电站\" prop=\"bdz\">\n              <el-select\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.bdz\"\n                clearable\n                placeholder=\"请选择光伏电站\"\n              >\n                <el-option\n                  v-for=\"item in bdzList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"时间\" prop=\"sj\">\n              <el-date-picker\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.sj\"\n                type=\"datetime\"\n                placeholder=\"选择日期时间\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\"\n            ><!--@focus=\"sbmcDialog\"-->\n            <el-form-item label=\"设备名称\" prop=\"sbmc\">\n              <el-input\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.sbmc\"\n                placeholder=\"请选择或输入设备\"\n                @input=\"change($event)\"\n              >\n                <i\n                  slot=\"suffix\"\n                  class=\"el-input__icon el-icon-search\"\n                  @click=\"showAssetSelect\"\n                ></i>\n              </el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"21\">\n            <el-form-item label=\"工作任务\" prop=\"gzrw\">\n              <el-input\n                style=\"width:100%\"\n                type=\"textarea\"\n                :rows=\"3\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.gzrw\"\n                placeholder=\"请输入内容\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"3\">\n            <el-white class=\"mb8 pull-right\">\n              <el-button type=\"primary\" @click=\"getCzp\" :disabled=\"isDisabled\"\n                >关联操作票</el-button\n              >\n            </el-white>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"使用原因\" prop=\"syyy\">\n              <el-input\n                style=\"width:100%\"\n                type=\"textarea\"\n                :rows=\"3\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.syyy\"\n                placeholder=\"请输入内容\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"记录人\" prop=\"jlr\">\n              <el-select\n                style=\"width:100%\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.jlr\"\n                clearable\n                placeholder=\"请选择记录人\"\n              >\n                <el-option\n                  v-for=\"item in jlrList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"记录时间\" prop=\"jlsj\">\n              <el-date-picker\n                v-model=\"form.jlsj\"\n                :disabled=\"isDisabled\"\n                type=\"datetime\"\n                style=\"width:100%\"\n                placeholder=\"选择日期时间\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <!--<div v-if=\"form.status == '待修改'\">\n          <el-row>\n            <el-col :span=\"24\">\n              <el-form-item label=\"意见\" prop=\"bz\">\n                <el-input style=\"width:100%\" type=\"textarea\" :rows=\"2\" :disabled=\"true\" v-model=\"form.bz\"\n                          placeholder=\"(审核退回意见)\"/>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>-->\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"handleClose\">取 消</el-button>\n        <el-button\n          v-if=\"\n            titles == '防误装置解锁工具使用登记' ||\n              titles == '修改防误装置解锁工具使用登记'\n          \"\n          type=\"primary\"\n          @click=\"submitFormFwzzjs\"\n          >保 存\n        </el-button>\n        <el-button\n          v-if=\"\n            titles == '防误装置解锁工具使用登记' ||\n              titles == '详情查看防误装置解锁工具使用登记'\n          \"\n          type=\"primary\"\n          @click=\"getSbForm\"\n          >提 交\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!--试验设备选择弹框-->\n    <el-dialog\n      title=\"设备选择\"\n      :visible.sync=\"isShowSysbDialog\"\n      width=\"60%\"\n      v-if=\"isShowSysbDialog\"\n      v-dialogDrag\n    >\n      <sysb-selected\n        @handleAcceptSbData=\"handleAcceptSbData\"\n        :selectedSbParam=\"selectedSbParam\"\n        @closeSysbSelectDialog=\"handleControlSysbSelectDialog\"\n      ></sysb-selected>\n    </el-dialog>\n\n    <!--工作流需要-->\n    <activiti\n      :processData=\"processData\"\n      :isShow=\"isShow\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n    ></activiti>\n    <time-line\n      :value=\"timeLineShow\"\n      :timeData=\"timeData\"\n      @closeTimeLine=\"colseTimeLine\"\n    />\n    <!--流程图查看-->\n    <el-dialog\n      title=\"审批流程图\"\n      :visible.sync=\"openLoadingImg\"\n      width=\"56%\"\n      append-to-body\n      :close-on-click-modal=\"false\"\n    >\n      <div>\n        <img :src=\"imgSrc\" style=\"width: 100%\" />\n      </div>\n    </el-dialog>\n\n    <!--关联操作票-->\n    <el-dialog :title=\"title\" :visible.sync=\"isShowDetailsCzp\" width=\"50%\">\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\">\n        <el-row>\n          <el-col>\n            <!--主表信息-->\n            <el-table\n              :data=\"propTableDataCzp.colFirst\"\n              :disabled=\"isDisabledCzp\"\n              height=\"300\"\n              border\n              stripe\n              style=\"width: 100%\"\n              max-height=\"40vh\"\n              @selection-change=\"handleSelectionChanges\"\n              ref=\"czpTable\"\n            >\n              <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n              <el-table-column\n                align=\"center\"\n                prop=\"bdzmc\"\n                label=\"光伏电站名称\"\n                width=\"200\"\n              >\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"请选择光伏电站名称\"\n                      :disabled=\"isDisabledCzp\"\n                      v-model=\"scope.row.bdzmcs\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" prop=\"czrw\" label=\"操作任务\">\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      type=\"textarea\"\n                      :rows=\"2\"\n                      placeholder=\"请输入操作任务\"\n                      :disabled=\"isDisabledCzp\"\n                      v-model=\"scope.row.czrw\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"handleClose\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"saveRowCzp\">确 认 </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n//流程\nimport activiti from \"com/activiti\";\nimport timeLine from \"com/timeLine\";\nimport { HistoryList } from \"@/api/activiti/processTask\";\nimport { getListLsp } from \"@/api/yxgl/gfyxgl/gfdzczp\";\nimport { getBdzDataListSelected as getBdzSelectList} from \"@/api/dagangOilfield/asset/gfsbtz\";\nimport {\n  getListFwzzjs,\n  saveOrUpdateFwzzjs,\n  removeFwzzjs\n} from \"@/api/yxgl/gfyxgl/gfzbgl\";\nimport DeviceTree from \"@/views/dagangOilfield/bzgl/sbbzk/deviceTree\";\n//新选择设备\nimport sysbSelected from \"@/views/dagangOilfield/bzgl/sybzk/sybgjlcomment/sysbSelected\";\nimport { getFgsOptions } from \"@/api/yxgl/gfyxgl/gfzbgl\";\n\nexport default {\n  name: \"fwzz_dj\",\n  components: { DeviceTree, sysbSelected, activiti, timeLine },\n  data() {\n    return {\n      fgsList: [], //分公司下拉框\n      currentUser: this.$store.getters.name,\n      isShowDetailsCzp: false,\n      propTableDataCzp: {\n        colFirst: []\n      },\n      isDisabledCzp: false,\n      //主设备选择传递子组件参数\n      selectedSbParam: {\n        lx: \"gf\",\n        sbmc: \"\"\n      },\n      //主设备弹出框\n      isShowSysbDialog: false,\n      //工作流弹窗\n      isShow: false,\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"fwzzjsgj\",\n        businessKey: \"\",\n        businessType: \"防误装置解锁工具使用\",\n        variables: {},\n        defaultFrom: true,\n        nextUser: \"\",\n        processType: \"complete\"\n      },\n      //流程图查看\n      openLoadingImg: false,\n      imgSrc: \"\", //流程图查看地址\n      timeData: [],\n      timeLineShow: false,\n      //弹出框标题\n      activitiOption: { title: \"上报\" },\n      isShowYj: true,\n      ZbDialogFormVisible: false,\n\n      bdzList: [],\n      isDisabled: false,\n      form: {\n        lx: \"\",\n        status: \"\"\n      },\n      formSh: {\n        wfzz1: \"\"\n      },\n      // 多选框选中的id\n      ids: [],\n      // 多选框选选中的数据\n      selectData: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 对话框标题\n      title: \"\",\n      titles: \"\",\n      isShowDetails: false,\n      isShowBdfgssh: false,\n      isShowBdfgsshForm: false,\n      //操作票列表选中后\n      assetInfo: {},\n      /**\n       *  防误装置解锁工具使用登记\n       *  */\n      filterInfo: {\n        data: {\n          dlqbh: \"\",\n          sjArr: [],\n          djr: \"\",\n          bdz: \"\"\n        },\n        fieldList: [\n          {\n            label: \"状态\",\n            type: \"select\",\n            value: \"status\",\n            clearable: true,\n            options: [\n              { label: \"待上报\", value: \"待上报\" },\n              { label: \"待修改\", value: \"待修改\" }\n            ]\n          },\n          {\n            label: \"分公司\",\n            type: \"select\",\n            value: \"fgs\",\n            checkboxValue: [],\n            options: []\n          },\n          { label: \"光伏电站\", type: \"select\", value: \"bdz\", options: [] },\n          {\n            label: \"时间\",\n            value: \"sjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"设备名称\", type: \"input\", value: \"sbmc\" }\n          /*{ label: '工作任务', type: 'input', value: 'gzrw' }*/\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"status\", label: \"状态\", minWidth: \"60\" },\n          { prop: \"fgsCn\", label: \"分公司\", minWidth: \"80\" },\n          { prop: \"bdzmc\", label: \"光伏电站\", minWidth: \"80\" },\n          { prop: \"sj\", label: \"时间\", minWidth: \"120\" },\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"100\" }\n          /*{ prop: 'gzrw', label: '工作任务', minWidth: '130' },*/\n          /*{\n            prop: 'operation',\n            label: '操作',\n            fixed: 'right',\n            minWidth: '110px',\n            style: { display: 'block' },\n            operation: [\n              { name: '修改', clickFun: this.updateRow },\n              { name: '详情', clickFun: this.getDetails },\n              { name: '提交', clickFun: this.getSb }\n              /!*{ name: '附件查看', clickFun: this.FjInfoList },*!/\n            ]\n          }*/\n        ]\n      },\n      params: {\n        lx: 1\n      },\n      paramsCzp: {\n        lx: 2,\n        /*status: '已办结',*/\n        sfbj: 1\n      },\n      rules: {\n        fgs: [{ required: true, message: \"分公司不能为空\", trigger: \"select\" }],\n        bdz: [{ required: true, message: \"光伏电站不能为空\", trigger: \"select\" }],\n        sj: [{ required: true, message: \"时间不能为空\", trigger: \"change\" }],\n        sbmc: [\n          { required: true, message: \"巡视类别不能为空\", trigger: \"blur\" }\n        ],\n        gzrw: [\n          { required: true, message: \"工作任务不能为空\", trigger: \"blur\" }\n        ],\n        syyy: [\n          { required: true, message: \"使用原因不能为空\", trigger: \"blur\" }\n        ],\n        jlr: [{ required: true, message: \"记录人不能为空\", trigger: \"blur\" }],\n        jlsj: [\n          { required: true, message: \"记录时间不能为空\", trigger: \"change\" }\n        ]\n      },\n      jlrList: [] //记录人下拉框\n    };\n  },\n  created() {\n    //列表查询\n    this.getData();\n    //获取光伏电站下拉框数据\n    // this.getBdzSelectList()\n    //获取分公司下拉框\n    this.getFgsList();\n  },\n\n  methods: {\n    //下拉框change事件\n    handleEvent(val) {\n      //光伏电站查询下拉框选项根据所选分公司带出来\n      if (val.label === \"fgs\" && val.value && val.value !== \"\") {\n        let form = {\n          ssdwbm: val.value\n        };\n        getBdzSelectList(form).then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value === \"bdz\") {\n              return (item.options = res.data);\n            }\n          });\n        });\n      }\n    },\n    /**\n     * 获取光伏电站下拉框数据\n     */\n    getBdzList(val) {\n      getBdzSelectList({ ssdwbm: val }).then(res => {\n        this.bdzList = res.data;\n      });\n    },\n    //获取分公司下拉框\n    getFgsList() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.fgsList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"fgs\") {\n            return (item.options = this.fgsList);\n          }\n        });\n      });\n    },\n    async fgsChangeFun(val) {\n      this.$set(this.form, \"bdz\", \"\");\n      await this.getBdzList(val);\n    },\n    //保存选中的操作票\n    saveRowCzp() {\n      this.isShowDetailsCzp = false;\n      this.form.bdz = this.assetInfo.bdzmc;\n      this.form.gzrw = this.assetInfo.czrw;\n      this.bdzList = [\n        { label: this.assetInfo.bdzmcs, value: this.assetInfo.bdzmc }\n      ];\n    },\n    //行选中数据\n    handleSelectionChanges(rows) {\n      this.$message.success(\"选中的数据\");\n      if (rows.length > 1) {\n        this.$message.warning(\"只能选中一条数据！！！\");\n        //清空选中方法\n        if (rows) {\n          rows.forEach(row => {\n            this.$refs.czpTable.toggleRowSelection(row);\n          });\n        } else {\n          this.$refs.czpTable.clearSelection();\n        }\n      } else {\n        //获取当前选中的单条数据\n        this.assetInfo = rows[0];\n      }\n    },\n    showAssetSelect() {\n      // this.assetSelect = true;\n      if (this.form.bdz === \"\" || this.form.bdz === undefined) {\n        this.$message.warning(\"请选择光伏电站名称\");\n        return;\n      }\n      // 拿到光伏电站汉字\n      let sbmcmc = \"\";\n      this.bdzList.forEach(item => {\n        if (item.value == this.form.bdz) {\n          sbmcmc = item.label;\n        }\n        return false;\n      });\n      this.selectedSbParam.sbmc = sbmcmc; //所属位置\n      this.isShowSysbDialog = true;\n    },\n    closeAssetSelect() {\n      this.isShowSysbDialog = false;\n    },\n    getAssetInfo(info) {\n      this.form.sbmc = info.sbmc; //主设备名称\n    },\n    //清空\n    change(e) {\n      this.$forceUpdate();\n    },\n    //关闭弹窗\n    closeActiviti() {\n      this.isShow = false;\n    },\n    //回退按钮\n    handleTh(type) {\n      this.getSbFsBj({ type: type, data: this.form }, { defaultForm: false });\n    },\n    //关闭流程查看页面\n    colseTimeLine() {\n      this.timeLineShow = false;\n    },\n    //流程查看\n    async showTimeLine(row) {\n      this.processData.businessKey = row.objId;\n      let { code, data } = await HistoryList(this.processData);\n      this.timeData = data;\n      this.timeLineShow = true;\n    },\n    //流程图片查看\n    showProcessImg(row) {\n      this.openLoadingImg = true;\n      this.imgSrc =\n        \"/activiti-api/process/read-resource?processDefinitionKey=fwzzjsgj&businessKey=\" +\n        row.objId +\n        \"&t=\" +\n        new Date().getTime();\n    },\n    //工作流回传数据\n    async todoResult(data) {\n      let row = {};\n      row = { objId: data.businessKey, status: \"待分公司五防专责审核\", lx: 2 };\n      saveOrUpdateFwzzjs(row).then(res => {\n        if (res.code === \"0000\") {\n          this.$message.success(\"操作成功\");\n          //重置page页从1开始\n          this.tableAndPageInfo.pager.pageResize = \"Y\";\n          this.getData();\n        }\n      });\n    },\n    /**\n     * 根据表格名称获取对应的数据\n     */\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        const { data, code } = await getListFwzzjs(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n        }\n      } catch (e) {}\n    },\n    /**\n     * 关联操作票\n     * */\n    async getCzp() {\n      /*if (this.form.bdz) {\n        this.isShowDetailsCzp = true\n      } else {\n        this.isShowDetailsCzp = false\n        this.$message.warning(\"请先选择光伏电站！！！\")\n      }*/\n      this.isShowDetailsCzp = true;\n      this.isDisabledCzp = true;\n      this.title = \"选择操作票\";\n      let param = this.paramsCzp;\n      let data = await getListLsp(param);\n      this.propTableDataCzp.colFirst = data.data.records;\n    },\n    /**\n     * 新增\n     */\n    addRow() {\n      this.form = {};\n      this.form.status = \"新建\";\n      this.isShowDetails = true;\n      this.isDisabled = false;\n      this.titles = \"防误装置解锁工具使用登记\";\n      this.isShowYj = false;\n      // 拿到日期、星期、天气\n      let now = new Date();\n      let year = now.getFullYear(); //得到年份\n      let month = now.getMonth(); //得到月份\n      let date = now.getDate(); //得到日期\n      let hour = now.getHours();\n      let minutes = now.getMinutes();\n      let seconds = now.getSeconds();\n      month = month + 1;\n      month = month.toString().padStart(2, \"0\");\n      date = date.toString().padStart(2, \"0\");\n      if (hour.toString().length != 2) {\n        // 拼接时，如果长度为1，保存报错\n        hour = \"0\" + hour;\n      }\n      if (minutes.toString().length != 2) {\n        // 拼接分钟，如果长度为1，保存报错\n        minutes = \"0\" + minutes;\n      }\n      if (seconds.toString().length != 2) {\n        // 拼接秒，如果长度为1，保存报错\n        seconds = \"0\" + seconds;\n      }\n      let defaultDate = `${year}-${month}-${date} ${hour}:${minutes}:${seconds}`;\n      this.form.jlsj = defaultDate;\n      //获取当前登录用户对象\n      let loginUser = this.$store.getters;\n      this.form.jlr = loginUser.name;\n      this.jlrList = [{ label: loginUser.nickName, value: loginUser.name }];\n    },\n    /**\n     * 修改\n     */\n    updateRow(row) {\n      this.titles = \"修改防误装置解锁工具使用登记\";\n      this.isDisabled = false;\n      this.isShowDetails = true;\n      this.form = { ...row };\n      if (this.form.status == \"已修改\") {\n        this.isShowYj = true;\n      } else {\n        this.isShowYj = false;\n      }\n      this.bdzList = [{ label: row.bdzmc, value: row.bdz }];\n      this.jlrList = [{ label: row.jlrCn, value: row.jlr }];\n    },\n    /**\n     * 详情\n     */\n    getDetails(row) {\n      this.titles = \"详情查看防误装置解锁工具使用登记\";\n      this.isDisabled = true;\n      this.isShowDetails = true;\n      this.form = { ...row };\n      if (this.form.status == \"已修改\") {\n        this.isShowYj = true;\n      } else {\n        this.isShowYj = false;\n      }\n      this.bdzList = [{ label: row.bdzmc, value: row.bdz }];\n      this.jlrList = [{ label: row.jlrCn, value: row.jlr }];\n    },\n    /**\n     * 新增框上报\n     * */\n    async getSbForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          try {\n            this.processData.defaultFrom = true;\n            this.processData.jxgs = false;\n            saveOrUpdateFwzzjs(this.form).then(res => {\n              if (res.code === \"0000\") {\n                this.$message.success(\"操作成功\");\n                this.getSbFsBj(\n                  { data: res.data, type: \"completeByGroup\" },\n                  {\n                    defaultForm: this.processData.defaultFrom,\n                    jxgs: this.processData.jxgs,\n                    personGroupId: 18\n                  }\n                );\n              }\n              //恢复分页\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              //重新查数据\n              this.getData();\n              this.isShowBdfgssh = false;\n              this.isShowDetails = false;\n              this.isDisabled = false;\n            });\n          } catch (e) {}\n        } else {\n          this.$message.error(\"校验未通过！\");\n          return false;\n        }\n      });\n    },\n    //上报发送办结\n    getSbFsBj(args, isShow) {\n      let row = { ...args.data };\n      if (args.type === \"complete\") {\n        this.activitiOption.title = \"发送给分公司五防专责审核\";\n        this.processData.defaultFrom = isShow.defaultForm; //是否显示选人员\n        this.processData.processType = \"complete\";\n        this.processData.jxgs = isShow.jxgs;\n        this.processData.businessKey = row.objId;\n        this.processData.variables.pass = true;\n      } else if (args.type === \"completeByGroup\") {\n        if (this.form.status == \"新建\") {\n          this.activitiOption.title = \"发送给分公司五防专责审核\";\n        }\n        this.processData.defaultFrom = isShow.defaultForm; //是否显示选人员\n        this.processData.processType = \"completeByGroup\";\n        this.processData.jxgs = isShow.jxgs;\n        this.processData.businessKey = row.objId;\n        this.processData.variables.pass = true;\n        this.processData.variables.personGroupId = isShow.personGroupId;\n      } else {\n        this.activitiOption.title = \"回退原因提报\";\n        this.processData.defaultFrom = true;\n        this.processData.processType = \"rollback\";\n        this.processData.businessKey = row.objId;\n        this.processData.variables.pass = true;\n      }\n      this.isShow = true;\n    },\n    /**\n     * 删除按钮\n     */\n    async handleDelete(row) {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          //防误装置解锁工具使用登记\n          removeFwzzjs(row.objId).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    /**\n     * 提交 防误装置解锁工具使用登记 表单\n     */\n    async submitFormFwzzjs() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          try {\n            this.form.wfzz1 = this.formSh.wfzz1;\n            this.form.lx = 1;\n            this.form.status = \"待上报\";\n            saveOrUpdateFwzzjs(this.form).then(res => {\n              if (res.code === \"0000\") {\n                this.$message.success(\"操作成功\");\n                this.getData();\n              }\n              this.isShowDetails = false;\n            });\n          } catch (e) {}\n        } else {\n          this.$message.error(\"校验未通过！\");\n          return false;\n        }\n      });\n    },\n    //关闭按钮\n    handleClose() {\n      this.isShowDetails = false;\n      this.isShowBdfgssh = false;\n      this.isShowDetailsCzp = false;\n    },\n    /**\n     * 多选款选中数据\n     * @param row\n     */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.objId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n      this.selectData = selection;\n    },\n    filterReset() {\n      this.params = {\n        lx: 1\n      };\n    },\n    //导出word\n    exportWord() {\n      if (!this.selectData.length > 0) {\n        this.$message.warning(\"请先选中要导出的数据\");\n        return;\n      }\n      try {\n        let fileName = \"防误装置解锁工具使用记录\";\n        let exportUrl = \"yxFwzzjsgjsyjl\";\n        let params = {\n          data: this.selectData,\n          url: exportUrl\n        };\n        exportWord(params, fileName);\n      } catch (e) {\n        this.$message.error(\"导出失败！\");\n      }\n    },\n    /**\n     * 获取光伏电站下拉框数据\n     */\n    getBdzSelectList() {\n      getBdzSelectList({}).then(res => {\n        this.bdzList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"bdz\") {\n            return (item.options = this.bdzList);\n          }\n        });\n      });\n    },\n    //试验设备选择事件\n    sysbSelectedClick() {\n      this.isShowSysbDialog = true;\n    },\n    //组件接受设备参数数据\n    handleAcceptSbData(sbData) {\n      this.form.sbmc = sbData.sbmc;\n      this.form.sysbid = sbData.objId;\n    },\n    //控制关闭试验设备弹出框\n    handleControlSysbSelectDialog(isShow) {\n      this.isShowSysbDialog = isShow;\n    }\n  }\n};\n</script>\n\n<style scoped></style>\n"]}]}