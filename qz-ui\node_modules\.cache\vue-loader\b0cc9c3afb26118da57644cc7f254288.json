{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\pddzcz\\dzczp.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\pddzcz\\dzczp.vue", "mtime": 1748604805608}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsKaW1wb3J0IHsKICBnZXRDenBteExpc3QsCiAgZ2V0TGlzdCwKICByZW1vdmUsCiAgc2F2ZU9yVXBkYXRlCn0gZnJvbSAiQC9hcGkveXhnbC9wZHl4Z2wvcGRkemN6cCI7CmltcG9ydCB7CiAgZXhwb3J0UGRmLAogIGV4cG9ydFdvcmQsCiAgcHJldmlld0ZpbGUsCiAgdXBkYXRlQnlJZAp9IGZyb20gIkAvYXBpL3l4Z2wvYmR5eGdsL2JkZHpjenAiOwppbXBvcnQgYXBpIGZyb20gIkAvdXRpbHMvcmVxdWVzdCI7Ci8v5rWB56iLCmltcG9ydCBhY3Rpdml0aSBmcm9tICJjb20vYWN0aXZpdGlfY3pwIjsKaW1wb3J0IHRpbWVMaW5lIGZyb20gImNvbS90aW1lTGluZSI7CmltcG9ydCB7IEhpc3RvcnlMaXN0IH0gZnJvbSAiQC9hcGkvYWN0aXZpdGkvcHJvY2Vzc1Rhc2siOwppbXBvcnQgeyBnZXRQZHNPcHRpb25zRGF0YUxpc3QgfSBmcm9tICJAL2FwaS9kYWdhbmdPaWxmaWVsZC9hc3NldC9wZHNnbCI7CmltcG9ydCBFbEltYWdlVmlld2VyIGZyb20gImVsZW1lbnQtdWkvcGFja2FnZXMvaW1hZ2Uvc3JjL2ltYWdlLXZpZXdlciI7CmltcG9ydCB7IGV4cG9ydFRvRXhjZWwsIGltcG9ydEZyb21FeGNlbCB9IGZyb20gIkAvY29tcG9uZW50cy9jb21tb24vZXhjZWwuanMiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogImR6Y3pwIiwKICBjb21wb25lbnRzOiB7IGFjdGl2aXRpLCB0aW1lTGluZSwgRWxJbWFnZVZpZXdlciB9LAogIHByb3BzOiB7CiAgICBpc1RqOiB7CiAgICAgIHR5cGU6IEJvb2xlYW4sCiAgICAgIGRlZmF1bHQ6IGZhbHNlCiAgICB9CiAgfSwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgaGFzU3VwZXJSb2xlOiB0aGlzLiRzdG9yZS5nZXR0ZXJzLmhhc1N1cGVyUm9sZSwKICAgICAgcGRzT3B0aW9uc0RhdGFMaXN0OiBbXSwKICAgICAgaXNEaXNhYmxlZEJqOiB0cnVlLAogICAgICBzdGF0dXNPcHRpb25zOiBbCiAgICAgICAgewogICAgICAgICAgdmFsdWU6ICIwIiwKICAgICAgICAgIGxhYmVsOiAi5pON5L2c56Wo5aGr5oqlIgogICAgICAgIH0sCiAgICAgICAgLy8gewogICAgICAgIC8vICAgdmFsdWU6ICIxIiwKICAgICAgICAvLyAgIGxhYmVsOiAi54+t57uE5a6h5qC4IgogICAgICAgIC8vIH0sCiAgICAgICAgewogICAgICAgICAgdmFsdWU6ICIyIiwKICAgICAgICAgIGxhYmVsOiAi5YiG5YWs5Y+45a6h5qC4IgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdmFsdWU6ICIzIiwKICAgICAgICAgIGxhYmVsOiAi5pON5L2c56Wo5Yqe57uTIgogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgdmFsdWU6ICI0IiwKICAgICAgICAgIGxhYmVsOiAi57uT5p2fIgogICAgICAgIH0seyBsYWJlbDogIuS9nOW6nyIsIHZhbHVlOiAiNyIgfQogICAgICBdLAogICAgICBidXR0b25OYW1lU2hvdzogZmFsc2UsCiAgICAgIGJ1dHRvbk5hbWU6ICIiLAogICAgICBydWxlczogewogICAgICAgIGZnczogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLljZXkvY3kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAic2VsZWN0IiB9XSwKICAgICAgICBwZHptYzogW3sgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLljZXkvY3kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAic2VsZWN0IiB9XSwKICAgICAgICAvLyBmbHI6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5Y+R5Luk5Lq65LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfV0sCiAgICAgICAgLy8gc2xyOiBbeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWPl+S7pOS6uuS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH1dLAogICAgICAgIC8vIGZsc2o6IFsKICAgICAgICAvLyAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlj5Hku6Tml7bpl7TkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9CiAgICAgICAgLy8gXSwKICAgICAgICBjenJ3OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5pON5L2c5Lu75Yqh5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfQogICAgICAgIF0KICAgICAgICAvLyBjenI6IFt7IG1lc3NhZ2U6ICLmk43kvZzkurrkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfV0sCiAgICAgICAgLy8gamhyOiBbeyBtZXNzYWdlOiAi55uR5oqk5Lq65LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH1dLAogICAgICAgIC8vIGtzc2o6IFt7IG1lc3NhZ2U6ICLmk43kvZzlvIDlp4vml7bpl7TkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9XSwKICAgICAgICAvLyBqc3NqOiBbeyBtZXNzYWdlOiAi5pON5L2c57uT5p2f5pe26Ze05LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfV0KICAgICAgfSwKICAgICAgYmpyOiAiIiwKICAgICAgLy8g5piv5ZCm5bey5omn6KGM5LiL5ouJ5qGGCiAgICAgIHNmeXp4TGlzdDogWwogICAgICAgIHsgbGFiZWw6ICLlt7LmiafooYwiLCB2YWx1ZTogIuW3suaJp+ihjCIgfSwKICAgICAgICB7IGxhYmVsOiAi5pyq5omn6KGMIiwgdmFsdWU6ICLmnKrmiafooYwiIH0KICAgICAgXSwKICAgICAgLy8g6I635Y+W5b2T5YmN55m75b2V5Lq66LSm5Y+3CiAgICAgIGN1cnJlbnRVc2VyOiB0aGlzLiRzdG9yZS5nZXR0ZXJzLm5hbWUsCiAgICAgIC8v5bel5L2c5rWB5Lyg5YWl5Y+C5pWwCiAgICAgIHByb2Nlc3NEYXRhOiB7CiAgICAgICAgcHJvY2Vzc0RlZmluaXRpb25LZXk6ICJjenBsYyIsCiAgICAgICAgYnVzaW5lc3NLZXk6ICIiLAogICAgICAgIGJ1c2luZXNzVHlwZTogIuWAkumXuOaTjeS9nOelqCIsCiAgICAgICAgdmFyaWFibGVzOiB7fSwKICAgICAgICBkZWZhdWx0RnJvbTogdHJ1ZSwKICAgICAgICBuZXh0VXNlcjogIiIsCiAgICAgICAgcHJvY2Vzc1R5cGU6ICJjb21wbGV0ZSIKICAgICAgfSwKICAgICAgLy/lt6XkvZzmtYHlvLnnqpcKICAgICAgaXNTaG93OiBmYWxzZSwKICAgICAgLy/mtYHnqIvlm77mn6XnnIsKICAgICAgb3BlbkxvYWRpbmdJbWc6IGZhbHNlLAogICAgICBpbWdTcmM6ICIiLCAvL+a1geeoi+Wbvuafpeeci+WcsOWdgAogICAgICB0aW1lRGF0YTogW10sCiAgICAgIHRpbWVMaW5lU2hvdzogZmFsc2UsCiAgICAgIC8v5by55Ye65qGG5qCH6aKYCiAgICAgIGFjdGl2aXRpT3B0aW9uOiB7IHRpdGxlOiAiIiB9LAogICAgICB0aXRsZXlsOiAiIiwKICAgICAgLy/lm77niYflnLDlnYB1cmwKICAgICAgZGlhbG9nSW1hZ2VVcmw6ICIiLAogICAgICAvL+WxleekuuWbvueJh2RpYWxvZ+aOp+WItgogICAgICBpbWdEaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgLy/kuIrkvKDlm77niYfml7bnmoTmkLrluKbnmoTlhbbku5blj4LmlbAKICAgICAgdXBsb2FkSW1nRGF0YTogewogICAgICAgIGJ1c2luZXNzSWQ6ICIiIC8v5pC65bim55qE6KGo5Y2V5Li76ZSuaWQKICAgICAgfSwKICAgICAgLy/kuIrkvKDlm77niYfml7bnmoTor7fmsYLlpLQKICAgICAgaGVhZGVyOiB7fSwKICAgICAgLy/lm77niYdsaXN0CiAgICAgIGltZ0xpc3Q6IFtdLAogICAgICBsb2dpbkZvcm06IHsKICAgICAgICB1c2VyTmFtZTogIiIsCiAgICAgICAgcGFzc3dvcmQ6ICIiLAogICAgICAgIHJlbWVtYmVyTWU6IGZhbHNlLAogICAgICAgIGNvZGU6ICI2NjY2IiwKICAgICAgICB1dWlkOiAiIgogICAgICB9LAogICAgICBzZWxlY3Rpb246IFtdLAogICAgICB5bDogZmFsc2UsCiAgICAgIC8vIOWkmumAieahhumAieS4reeahGlkCiAgICAgIGlkczogW10sCiAgICAgIHNpbmdsZTogdHJ1ZSwKICAgICAgbXVsdGlwbGU6IHRydWUsCiAgICAgIC8v5by55Ye65qGG5Lit6KGo5qC85pWw5o2uCiAgICAgIHByb3BUYWJsZURhdGE6IHsKICAgICAgICBzZWw6IG51bGwsIC8vIOmAieS4reihjAogICAgICAgIGNvbEZpcnN0OiBbXQogICAgICB9LAogICAgICBkd1NlbGVjdGVkOiBbeyBsYWJlbDogIumFjeeUtei/kOe7tOWIhuWFrOWPuCIsIHZhbHVlOiAiMzAxMyIgfV0sCiAgICAgIC8vZm9ybeihqOWNlQogICAgICBmb3JtOiB7CiAgICAgICAgc3RhdHVzOiAiIiwKICAgICAgICBrc3NqOiAiIiwKICAgICAgICBqc3NqOiAiIiwKICAgICAgICBjenJ3OiAiIiwKICAgICAgICBmbHI6ICIiLAogICAgICAgIHNscjogIiIsCiAgICAgICAgZmxzajogIiIsCiAgICAgICAgbHg6IDMsIC8v6YWN55S1CiAgICAgICAgY29sRmlyc3Q6IFtdLAogICAgICAgIHBkem1jOiAiIiwKICAgICAgICBjenhzOiAwLAogICAgICAgIHl6eGN6eHM6IDAsCiAgICAgICAgd3p4Y3p4czogMAogICAgICB9LAogICAgICBmb3JtQ3pwOiB7CiAgICAgICAgcGRzaHI6ICIiLAogICAgICAgIHlqOiAiIgogICAgICB9LAogICAgICAvL+ivpuaDheW8ueahhuaYr+WQpuaYvuekugogICAgICBpc1Nob3dEZXRhaWxzOiBmYWxzZSwKICAgICAgLy/mmK/lkKbnpoHnlKgKICAgICAgaXNEaXNhYmxlZDogZmFsc2UsCiAgICAgIC8v5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgZmlsdGVySW5mbzogewogICAgICAgIGRhdGE6IHsKICAgICAgICAgIGJtOiAiIiwKICAgICAgICAgIHNscjogIiIsCiAgICAgICAgICBmbHI6ICIiLAogICAgICAgICAgY3pzakFycjogW10sCiAgICAgICAgICBmbHNqQXJyOiBbXSwKICAgICAgICB9LAogICAgICAgIGZpZWxkTGlzdDogWwogICAgICAgICAgeyBsYWJlbDogIue8luWPtyIsIHZhbHVlOiAiYm0iLCB0eXBlOiAiaW5wdXQiLCBjbGVhcmFibGU6IHRydWUgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLmk43kvZzml7bpl7QiLAogICAgICAgICAgICB2YWx1ZTogImN6c2pBcnIiLAogICAgICAgICAgICB0eXBlOiAiZGF0ZSIsCiAgICAgICAgICAgIGRhdGVUeXBlOiAiZGF0ZXJhbmdlIiwKICAgICAgICAgICAgZm9ybWF0OiAieXl5eS1NTS1kZCIKICAgICAgICAgIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5Y+X5Luk5Lq6IiwgdHlwZTogImlucHV0IiwgdmFsdWU6ICJzbHIiLCBjbGVhcmFibGU6IHRydWUgfSwKICAgICAgICAgIHsgbGFiZWw6ICLlj5Hku6TkuroiLCB0eXBlOiAiaW5wdXQiLCB2YWx1ZTogImZsciIsIGNsZWFyYWJsZTogdHJ1ZSB9LAogICAgICAgICAgewogICAgICAgICAgICBsYWJlbDogIuWPkeS7pOaXtumXtCIsCiAgICAgICAgICAgIHZhbHVlOiAiZmxzakFyciIsCiAgICAgICAgICAgIHR5cGU6ICJkYXRlIiwKICAgICAgICAgICAgZGF0ZVR5cGU6ICJkYXRlcmFuZ2UiLAogICAgICAgICAgICBmb3JtYXQ6ICJ5eXl5LU1NLWRkIgogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbGFiZWw6ICLnirbmgIEiLAogICAgICAgICAgICB2YWx1ZTogInN0YXR1cyIsCiAgICAgICAgICAgIHR5cGU6ICJjaGVja2JveCIsCiAgICAgICAgICAgIGNoZWNrYm94VmFsdWU6IFtdLAogICAgICAgICAgICBjbGVhcmFibGU6IHRydWUsCiAgICAgICAgICAgIG9wdGlvbnM6IFsKICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICB2YWx1ZTogIjAiLAogICAgICAgICAgICAgICAgbGFiZWw6ICLmk43kvZznpajloavmiqUiCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAvKnsKICAgICAgICAgICAgICAgIHZhbHVlOiAnMScsCiAgICAgICAgICAgICAgICBsYWJlbDogJ+ePree7hOWuoeaguCcKICAgICAgICAgICAgICB9LCovCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgdmFsdWU6ICIyIiwKICAgICAgICAgICAgICAgIGxhYmVsOiAi5YiG5YWs5Y+45a6h5qC4IgogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgdmFsdWU6ICIzIiwKICAgICAgICAgICAgICAgIGxhYmVsOiAi5pON5L2c56Wo5Yqe57uTIgogICAgICAgICAgICAgIH0seyBsYWJlbDogIuS9nOW6nyIsIHZhbHVlOiAiNyIgfQogICAgICAgICAgICBdCiAgICAgICAgICB9CiAgICAgICAgXQogICAgICB9LAogICAgICB0YWJsZUFuZFBhZ2VJbmZvOiB7CiAgICAgICAgcGFnZXI6IHsKICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgIHNpemVzOiBbMTAsIDIwLCA1MCwgMTAwXQogICAgICAgIH0sCiAgICAgICAgb3B0aW9uOiB7CiAgICAgICAgICBjaGVja0JveDogdHJ1ZSwKICAgICAgICAgIHNlcmlhbE51bWJlcjogdHJ1ZQogICAgICAgIH0sCiAgICAgICAgdGFibGVEYXRhOiBbXSwKICAgICAgICB0YWJsZUhlYWRlcjogWwogICAgICAgICAgeyBsYWJlbDogIue8luWPtyIsIHByb3A6ICJibSIsIG1pbldpZHRoOiAiODAiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi54q25oCBIiwgcHJvcDogInN0YXR1c0NuIiwgbWluV2lkdGg6ICI4MCIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLphY3nlLXnq5nlkI3np7AiLCBwcm9wOiAicGR6Q24iLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLmk43kvZzlvIDlp4vml7bpl7QiLCBwcm9wOiAia3NzaiIsIG1pbldpZHRoOiAiMTIwIiB9LAogICAgICAgICAgeyBsYWJlbDogIuaTjeS9nOe7k+adn+aXtumXtCIsIHByb3A6ICJqc3NqIiwgbWluV2lkdGg6ICIxMjAiIH0sCiAgICAgICAgICB7IGxhYmVsOiAi5pON5L2c5Lu75YqhIiwgcHJvcDogImN6cnciLCBtaW5XaWR0aDogIjEyMCIgfSwKICAgICAgICAgIC8vIHtsYWJlbDogJ+WuoeaguOS6uicsIHByb3A6ICdwZHNocicsIG1pbldpZHRoOiAnMTAwJ30sCiAgICAgICAgICB7IGxhYmVsOiAi5Y+R5Luk5Lq6IiwgcHJvcDogImZsciIsIG1pbldpZHRoOiAiMTAwIiB9LAogICAgICAgICAgeyBsYWJlbDogIuWPl+S7pOS6uiIsIHByb3A6ICJzbHIiLCBtaW5XaWR0aDogIjEwMCIgfSwKICAgICAgICAgIHsgbGFiZWw6ICLlj5Hku6Tml7bpl7QiLCBwcm9wOiAiZmxzaiIsIG1pbldpZHRoOiAiMTIwIiB9CiAgICAgICAgXQogICAgICB9LAogICAgICBzZWxlY3RSb3dzOiBbXSwKICAgICAgcGFyYW1zOiB7CiAgICAgICAgLy/phY3nlLUKICAgICAgICBseDogMywKICAgICAgICBzdGF0dXM6ICIwLDEsMiwzLDQiIC8v54q25oCB44CC5bCG57uT5p2f54q25oCB55qE6L+H5ruk5o6JCiAgICAgIH0sCiAgICAgIC8v5o2i6KGM5pON5L2cCiAgICAgIG9sZEluZGV4OiB1bmRlZmluZWQsCiAgICAgIG5ld0luZGV4OiB1bmRlZmluZWQsCiAgICAgIGZsYWc6ICJzdGFydCIKICAgIH07CiAgfSwKICBtb3VudGVkKCkgewogICAgLy/ojrflj5Z0b2tlbgogICAgdGhpcy5oZWFkZXIudG9rZW4gPSBnZXRUb2tlbigpOwogICAgdGhpcy5nZXRQZHNPcHRpb25zRGF0YUxpc3QoKTsKICAgIC8v6I635Y+W5YiX6KGo5pWw5o2uCiAgICB0aGlzLmdldERhdGEodGhpcy4kcm91dGUucXVlcnkpOwogIH0sCiAgbWV0aG9kczogewogICAgLy/kvZzlup/npagKICAgIG51bGxpZnlHenAob2JqSWQpIHsKICAgICAgdGhpcy4kY29uZmlybSgi56Wo5L2c5bqf5ZCO5Y+q6IO95p+l55yL77yM5LiN6IO96L+b6KGM5Lu75L2V5pON5L2c77yM56Gu6K6k5L2c5bqf5ZCXPyIsICLmj5DnpLoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KQogICAgICAgIC50aGVuKGFzeW5jICgpID0+IHsKICAgICAgICAgIGxldCB7IGNvZGUgfSA9IGF3YWl0IHVwZGF0ZUJ5SWQoeyBzdGF0dXM6IDcsIG9iaklkOiBvYmpJZCB9KTsKICAgICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLmk43kvZzmiJDlip8hISIpOwogICAgICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICAgIH0KICAgICAgICB9KQogICAgICAgIC5jYXRjaCgoKSA9PiB7fSk7CiAgICB9LAogICAgZXhwb3J0RXhjZWwoKSB7CiAgICAgIGxldCBleGNlbERhdGEgPSB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QubWFwKGl0ZW0gPT4gKHsgIuaTjeS9nOmhueebriI6IGl0ZW0uY3pyd30pKTsKICAgICAgZXhwb3J0VG9FeGNlbChleGNlbERhdGEsICLmk43kvZzpobnnm64ueGxzeCIpOwogICAgfSwKICAgIGltcG9ydEV4Y2VsKGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgIGxldCBmaWxlTmFtZSA9IGZpbGUubmFtZQogICAgICBpZiAoIWZpbGVOYW1lLmluY2x1ZGVzKCLmk43kvZzpobnnm64iKSkgewogICAgICAgIHRoaXMubXNnRXJyb3IoIuaWh+S7tuacieivr++8jOivt+ajgOafpSIpCiAgICAgICAgdGhpcy4kcmVmcy51cGxvYWQuY2xlYXJGaWxlcygpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KICAgICAgaW1wb3J0RnJvbUV4Y2VsKGZpbGUpCiAgICAgICAgLnRoZW4oZGF0YSA9PiB7CiAgICAgICAgICB0aGlzLmlkcyA9IHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdC5tYXAoaXRlbSA9PiBpdGVtLm9iaklkKQogICAgICAgICAgdGhpcy5wcm9wVGFibGVEYXRhLmNvbEZpcnN0ID0gZGF0YS5tYXAoaXRlbSA9PiAoe3hoOiBpdGVtLl9fcm93TnVtX18gLCBjenJ3OiBpdGVtWyLmk43kvZzpobnnm64iXX0pKTsKICAgICAgICB9KQogICAgICAgIC5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgICBjb25zb2xlLmVycm9yKCLlr7zlhaXlpLHotKUiLCBlcnJvcik7CiAgICAgICAgfSk7CiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLmNsZWFyRmlsZXMoKQogICAgfSwKICAgIC8v6I635Y+W6YWN55S15a6k5LiL5ouJ5qGG5pWw5o2uCiAgICBnZXRQZHNPcHRpb25zRGF0YUxpc3QoKSB7CiAgICAgIGdldFBkc09wdGlvbnNEYXRhTGlzdCh7fSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMucGRzT3B0aW9uc0RhdGFMaXN0ID0gcmVzLmRhdGE7CiAgICAgIH0pOwogICAgfSwKICAgIGdldFNob3coKSB7CiAgICAgIHRoaXMuYnV0dG9uTmFtZVNob3cgPSBmYWxzZTsKICAgICAgc3dpdGNoICh0aGlzLmZvcm0uc3RhdHVzKSB7CiAgICAgICAgY2FzZSAiMCI6CiAgICAgICAgICB0aGlzLmJ1dHRvbk5hbWUgPSAi5LiKIOaKpSI7CiAgICAgICAgICBpZiAodGhpcy5jdXJyZW50VXNlciA9PT0gdGhpcy5mb3JtLmNyZWF0ZUJ5KSB7CiAgICAgICAgICAgIHRoaXMuYnV0dG9uTmFtZVNob3cgPSB0cnVlOwogICAgICAgICAgfQogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSAiMSI6CiAgICAgICAgICB0aGlzLmJ1dHRvbk5hbWUgPSAi5o+QIOS6pCI7CiAgICAgICAgICBpZiAodGhpcy5mb3JtLmJ6c3ByID09PSB0aGlzLmN1cnJlbnRVc2VyKSB7CiAgICAgICAgICAgIHRoaXMuYnV0dG9uTmFtZVNob3cgPSB0cnVlOwogICAgICAgICAgfQogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSAiMiI6CiAgICAgICAgICB0aGlzLmJ1dHRvbk5hbWUgPSAi5o+QIOS6pCI7CiAgICAgICAgICBpZiAodGhpcy5mb3JtLmZnc3NwciA9PT0gdGhpcy5jdXJyZW50VXNlcikgewogICAgICAgICAgICB0aGlzLmJ1dHRvbk5hbWVTaG93ID0gdHJ1ZTsKICAgICAgICAgIH0KICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgIjMiOgogICAgICAgICAgdGhpcy5idXR0b25OYW1lID0gIuWKniDnu5MiOwogICAgICAgICAgaWYgKHRoaXMuZm9ybS5ianIgPT09IHRoaXMuY3VycmVudFVzZXIpIHsKICAgICAgICAgICAgdGhpcy5idXR0b25OYW1lU2hvdyA9IHRydWU7CiAgICAgICAgICAgIHRoaXMuaXNEaXNhYmxlZEJqID0gZmFsc2U7CiAgICAgICAgICB9CiAgICAgICAgICBicmVhazsKICAgICAgfQogICAgfSwKICAgIGZpbHRlclJlc2V0KCkgewogICAgICB0aGlzLnBhcmFtcyA9IHsKICAgICAgICAvL+mFjeeUtQogICAgICAgIGx4OiAzLAogICAgICAgIHN0YXR1czogIjAsMSwyLDMsNCIgLy/nirbmgIHjgILlsIbnu5PmnZ/nirbmgIHnmoTov4fmu6TmjokKICAgICAgfTsKICAgIH0sCiAgICAvL+mihOiniOaWh+S7tgogICAgYXN5bmMgcHJldmlld0ZpbGUocm93KSB7CiAgICAgIHRyeSB7CiAgICAgICAgbGV0IGV4cG9ydERhdGEgPSB7IC4uLnJvdyB9OwogICAgICAgIGF3YWl0IHByZXZpZXdGaWxlKGV4cG9ydERhdGEsICJwZHpkemN6cCIpOwogICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6aKE6KeI5aSx6LSl77yBIik7CiAgICAgIH0KICAgIH0sCiAgICAvL+a4heepugogICAgY2hhbmdlKGUpIHsKICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKTsKICAgIH0sCiAgICAvL+WFs+mXreW8ueeqlwogICAgY2xvc2VBY3Rpdml0aSgpIHsKICAgICAgaWYgKHRoaXMuJHJlZnMuYWN0aXZpdGkuJHJlZnMuZm9ybSkgewogICAgICAgIHRoaXMuJHJlZnMuYWN0aXZpdGkuJHJlZnMuZm9ybS5yZXNldEZpZWxkcygpOwogICAgICB9CiAgICAgIHRoaXMuaXNTaG93ID0gZmFsc2U7CiAgICB9LAogICAgLy/lhbPpl63mtYHnqIvmn6XnnIvpobXpnaIKICAgIGNvbHNlVGltZUxpbmUoKSB7CiAgICAgIHRoaXMudGltZUxpbmVTaG93ID0gZmFsc2U7CiAgICB9LAogICAgYXN5bmMgc2hvd1RpbWVMaW5lKHJvdykgewogICAgICB0aGlzLnByb2Nlc3NEYXRhLmJ1c2luZXNzS2V5ID0gcm93Lm9iaklkOwogICAgICBsZXQgeyBjb2RlLCBkYXRhIH0gPSBhd2FpdCBIaXN0b3J5TGlzdCh0aGlzLnByb2Nlc3NEYXRhKTsKICAgICAgdGhpcy50aW1lRGF0YSA9IGRhdGE7CiAgICAgIHRoaXMudGltZUxpbmVTaG93ID0gdHJ1ZTsKICAgIH0sCiAgICAvL+a1geeoi+WbvueJh+afpeeciwogICAgc2hvd1Byb2Nlc3NJbWcocm93KSB7CiAgICAgIGNvbnNvbGUubG9nKHJvdyk7CiAgICAgIHRoaXMub3BlbkxvYWRpbmdJbWcgPSB0cnVlOwogICAgICB0aGlzLmltZ1NyYyA9CiAgICAgICAgIi9hY3Rpdml0aS1hcGkvcHJvY2Vzcy9yZWFkLXJlc291cmNlP3Byb2Nlc3NEZWZpbml0aW9uS2V5PWN6cGxjJmJ1c2luZXNzS2V5PSIgKwogICAgICAgIHJvdy5vYmpJZCArCiAgICAgICAgIiZ0PSIgKwogICAgICAgIG5ldyBEYXRlKCkuZ2V0VGltZSgpOwogICAgfSwKICAgIC8v5pS56YCg5ZCO55qE5LiK5Lyg5aSa5Liq5Zu+54mH5paH5Lu2CiAgICB1cGxvYWRGb3JtKCkgewogICAgICB2YXIgbmV3VXJsID0gW107IC8v55So5p2l5a2Y5pS+5b2T5YmN5pyq5pS55Yqo6L+H55qE5Zu+54mHdXJsLOatpOWbvueJh+S4jei/m+ihjOWIoOmZpOWkhOeQhu+8jOWFtuS9meW9k+WJjeS4muWKoWlk5LiL6Z2i55qE5Zu+54mH5pWw5o2u5bCG6L+b6KGM5Yig6ZmkCiAgICAgIC8vIGNvbnNvbGUubG9nKHRoaXMuJHJlZnMudXBsb2FkSW1nLmZpbGVMaXN0KQogICAgICB2YXIgaW1hZ2VUeXBlID0gWyJwbmciLCAianBnIl07CiAgICAgIGNvbnNvbGUubG9nKCLlvIDlp4vkuIrkvKDlm77niYciKTsKICAgICAgY29uc29sZS5sb2codGhpcy5pbWdMaXN0KTsKICAgICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKTsKICAgICAgLy8g5Zug5Li66KaB5Lyg5LiA5Liq5paH5Lu25pWw57uE6L+H5Y6777yM5omA5Lul6KaB5b6q546vYXBwZW5kCiAgICAgIHRoaXMuaW1nTGlzdC5mb3JFYWNoKGZpbGUgPT4gewogICAgICAgIGlmIChmaWxlLnJhdyA9PSB1bmRlZmluZWQpIHsKICAgICAgICAgIGNvbnNvbGUubG9nKGZpbGUudXJsKTsKICAgICAgICAgIG5ld1VybC5wdXNoKGZpbGUudXJsKTsKICAgICAgICB9CiAgICAgICAgZm9ybURhdGEuYXBwZW5kKCJmaWxlcyIsIGZpbGUucmF3KTsKICAgICAgfSk7CiAgICAgIGZvcm1EYXRhLmFwcGVuZCgiYnVzaW5lc3NJZCIsIHRoaXMudXBsb2FkSW1nRGF0YS5idXNpbmVzc0lkKTsgLy8g6Ieq5a6a5LmJ5Y+C5pWwCiAgICAgIGZvcm1EYXRhLmFwcGVuZCgibmV3VXJsIiwgbmV3VXJsKTsgLy8g5pyq5pS55Yqo6L+H55qE5Zu+54mHdXJsCiAgICAgIGZvcm1EYXRhLmFwcGVuZCgidHlwZSIsIGltYWdlVHlwZSk7IC8vIOacquaUueWKqOi/h+eahOWbvueJh3VybAogICAgICBhcGkKICAgICAgICAucmVxdWVzdFBvc3QoIi9pc2MtYXBpL2ZpbGUvdXBsb2FkRmlsZXMiLCBmb3JtRGF0YSwgMSkKICAgICAgICAudGhlbihyZXMgPT4gewogICAgICAgICAgY29uc29sZS5sb2coIuS4iuS8oOWbvueJh+aIkOWKn+eahOWPjeWbnuWAvCIpOwogICAgICAgICAgY29uc29sZS5sb2cocmVzKTsKICAgICAgICAgIC8vIOa4heepuuWbvueJh+WIl+ihqO+8iOS4gOWumuimgea4heepuu+8jOWQpuWImeS4iuS8oOaIkOWKn+WQjui/mOaYr+S8muiwg+eUqGhhbmRsZUNoYW5nZe+8iO+8ieWHveaVsO+8jOS4iuS8oOaIkOWKn+WQjuWIl+ihqOS4rei/mOWtmOWcqOWbvueJh++8iQogICAgICAgICAgdGhpcy5pbWdMaXN0ID0gW107CiAgICAgICAgICAvL+afpeivouaOpeWPo+aVsOaNrgogICAgICAgICAgdGhpcy5nZXREYXRhKCk7CiAgICAgICAgfSkKICAgICAgICAuY2F0Y2gocmVzID0+IHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5Zu+54mH5LiK5Lyg5aSx6LSl77yBIik7CiAgICAgICAgfSk7CiAgICB9LAogICAgLy8g6YCJ5oup5paH5Lu25pe277yM5b6AZmlsZUxpc3Tph4zmt7vliqAKICAgIGhhbmRsZUNoYW5nZShmaWxlLCBmaWxlTGlzdCkgewogICAgICB0aGlzLmltZ0xpc3QgPSBmaWxlTGlzdDsKICAgIH0sCiAgICBoYW5kbGVQcm9ncmVzcyhldmVudCwgZmlsZSwgZmlsZUxpc3QpIHsKICAgICAgY29uc29sZS5sb2coImV2ZW50IiwgZXZlbnQpOwogICAgICBjb25zb2xlLmxvZygiZmlsZSIsIGZpbGUpOwogICAgICBjb25zb2xlLmxvZygiZmlsZUxpc3QiLCBmaWxlTGlzdCk7CiAgICB9LAogICAgLy/lm77niYfnp7vpmaQKICAgIGhhbmRsZVJlbW92ZShmaWxlLCBmaWxlTGlzdCkgewogICAgICB0aGlzLmltZ0xpc3QgPSBmaWxlTGlzdDsKICAgIH0sCiAgICAvL+WbvueJh+aUvuWkpwogICAgaGFuZGxlUGljdHVyZUNhcmRQcmV2aWV3KGZpbGUpIHsKICAgICAgdGhpcy5kaWFsb2dJbWFnZVVybCA9IFtmaWxlLnVybF07CiAgICAgIHRoaXMuaW1nRGlhbG9nVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgb25DbG9zZSgpIHsKICAgICAgdGhpcy5pbWdEaWFsb2dWaXNpYmxlID0gZmFsc2U7CiAgICB9LAogICAgLy8g6aKE6KeI5by55qGGCiAgICBoYW5kbGVZbENoYW5nZSgpIHsKICAgICAgdGhpcy50aXRsZXlsID0gIuafpeeci+aTjeS9nOmhueebriI7CiAgICAgIHRoaXMueWwgPSB0cnVlOwogICAgfSwKICAgIC8vIOWFqOmAieahhgogICAgaGFuZGxlQ2hlY2tBbGxDaGFuZ2UodmFsKSB7CiAgICAgIGlmICh2YWwpIHsKICAgICAgICB0aGlzLmZvcm0ueXp4Y3p4cyA9IHRoaXMuZm9ybS5jenhzOwogICAgICAgIHRoaXMuZm9ybS53enhjenhzID0gMDsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmZvcm0ud3p4Y3p4cyA9IHRoaXMuZm9ybS5jenhzOwogICAgICAgIHRoaXMuZm9ybS55enhjenhzID0gMDsKICAgICAgfQogICAgICB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICBpdGVtLnNmd2MgPSB2YWw7CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2UoaW5kZXgpIHsKICAgICAgY29uc29sZS5sb2coaW5kZXgpOwogICAgICBpZiAodGhpcy5mbGFnID09PSAic3RhcnQiKSB7CiAgICAgICAgdGhpcy5vbGRJbmRleCA9IGluZGV4OwogICAgICAgIHRoaXMuZmxhZyA9ICJlbmQiOwogICAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygi6K+354K55Ye76ZyA6KaB5pu05o2i5Yiw55qE6KGMIik7CiAgICAgIH0gZWxzZSBpZiAodGhpcy5mbGFnID09ICJlbmQiKSB7CiAgICAgICAgdGhpcy5uZXdJbmRleCA9IGluZGV4OwogICAgICAgIHRoaXMuZmxhZyA9ICJzdGFydCI7CiAgICAgICAgY29uc3Qgb2xkQ3VycmVudFJvdyA9IHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdC5zcGxpY2UoCiAgICAgICAgICB0aGlzLm9sZEluZGV4LAogICAgICAgICAgMQogICAgICAgIClbMF07CiAgICAgICAgdGhpcy5wcm9wVGFibGVEYXRhLmNvbEZpcnN0LnNwbGljZSh0aGlzLm5ld0luZGV4LCAwLCBvbGRDdXJyZW50Um93KTsKICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuabtOaNoumhuuW6j+aIkOWKnyIpOwogICAgICB9CiAgICB9LAogICAgaGFuZGxlQ2hlY2tDaGFuZ2UodmFsKSB7CiAgICAgIGlmICh2YWwpIHsKICAgICAgICArK3RoaXMuZm9ybS55enhjenhzOwogICAgICB9IGVsc2UgewogICAgICAgIC0tdGhpcy5mb3JtLnl6eGN6eHM7CiAgICAgIH0KICAgICAgdGhpcy5mb3JtLnd6eGN6eHMgPSB0aGlzLmZvcm0uY3p4cyAtIHRoaXMuZm9ybS55enhjenhzOwogICAgfSwKICAgIC8v5bel5L2c5rWB5Zue5Lyg5pWw5o2uCiAgICBhc3luYyB0b2RvUmVzdWx0KGRhdGEpIHsKICAgICAgbGV0IHJvdyA9IHsKICAgICAgICBvYmpJZDogZGF0YS5idXNpbmVzc0tleSwKICAgICAgICBpc1N0YXJ0OiAxLAogICAgICAgIGlzQmFjazogZGF0YS5wcm9jZXNzVHlwZSA9PT0gInJvbGxiYWNrIiA/IDEgOiAwCiAgICAgIH07CiAgICAgIGlmIChkYXRhLnByb2Nlc3NUeXBlID09PSAicm9sbGJhY2siKSB7CiAgICAgICAgc3dpdGNoIChkYXRhLmFjdGl2ZVRhc2tOYW1lKSB7CiAgICAgICAgICBjYXNlICLmk43kvZznpajloavmiqUiOgogICAgICAgICAgICByb3cuc3RhdHVzID0gIjAiOwogICAgICAgICAgICBicmVhazsKICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgc3dpdGNoIChkYXRhLmFjdGl2ZVRhc2tOYW1lKSB7CiAgICAgICAgICBjYXNlICLliIblhazlj7jlrqHmoLgiOgogICAgICAgICAgICByb3cuc3RhdHVzID0gIjIiOwogICAgICAgICAgICByb3cuZmdzc3ByID0gZGF0YS5uZXh0VXNlcjsKICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICBjYXNlICLmk43kvZznpajlip7nu5MiOgogICAgICAgICAgICByb3cuc3RhdHVzID0gIjMiOwogICAgICAgICAgICByb3cuYmpyID0gZGF0YS5uZXh0VXNlcjsKICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICBjYXNlICLnu5PmnZ8iOgogICAgICAgICAgICByb3cuc3RhdHVzID0gIjQiOwogICAgICAgICAgICBicmVhazsKICAgICAgICB9CiAgICAgIH0KICAgICAgbGV0IHsgY29kZSB9ID0gYXdhaXQgdXBkYXRlQnlJZChyb3cpOwogICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgYXdhaXQgdGhpcy5nZXREYXRhKCk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLmk43kvZzlpLHotKUiKTsKICAgICAgfQogICAgfSwKICAgIC8v5LiK5oql5Y+R6YCB5Yqe57uTCiAgICBhc3luYyBnZXRTYkZzQmoodHlwZSkgewogICAgICBsZXQgcm93ID0geyAuLi50aGlzLmZvcm0gfTsKICAgICAgaWYgKHR5cGUgPT09ICJjb21wbGV0ZSIpIHsKICAgICAgICBzd2l0Y2ggKHJvdy5zdGF0dXMpIHsKICAgICAgICAgIGNhc2UgIjAiOgogICAgICAgICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSBmYWxzZTsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS52YXJpYWJsZXMucGFzcyA9IHRydWU7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuYnVzaW5lc3NLZXkgPSByb3cub2JqSWQ7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEucHJvY2Vzc1R5cGUgPSB0eXBlOwogICAgICAgICAgICAvLyB0aGlzLnByb2Nlc3NEYXRhLmR3ID0gcm93LmZnczsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5yeWx4ID0gIuWIhuWFrOWPuOWuoeaguOS6uiI7CiAgICAgICAgICAgIHRoaXMuYWN0aXZpdGlPcHRpb24udGl0bGUgPSAi5o+Q5LqkIjsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5kZWZhdWx0RnJvbSA9IHRydWU7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEucGVyc29uR3JvdXBJZCA9IDE0OwogICAgICAgICAgICB0aGlzLmlzU2hvdyA9IHRydWU7CiAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgY2FzZSAiMiI6CiAgICAgICAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IGZhbHNlOwogICAgICAgICAgICB0aGlzLnByb2Nlc3NEYXRhLnZhcmlhYmxlcy5wYXNzID0gdHJ1ZTsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5idXNpbmVzc0tleSA9IHJvdy5vYmpJZDsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5wcm9jZXNzVHlwZSA9IHR5cGU7CiAgICAgICAgICAgIHRoaXMuYWN0aXZpdGlPcHRpb24udGl0bGUgPSAi5o+Q5LqkIjsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5kZWZhdWx0RnJvbSA9IHRydWU7CiAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEucGVyc29uR3JvdXBJZCA9IDE1OwogICAgICAgICAgICAvLyB0aGlzLnByb2Nlc3NEYXRhLmR3ID0gcm93LmZnczsKICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5yeWx4ID0gIuWKnue7k+S6uiI7CiAgICAgICAgICAgIHRoaXMuaXNTaG93ID0gdHJ1ZTsKICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICBjYXNlICIzIjoKICAgICAgICAgICAgdGhpcy5mb3JtLmNvbEZpcnN0ID0gdGhpcy5wcm9wVGFibGVEYXRhLmNvbEZpcnN0OwogICAgICAgICAgICB0aGlzLmZvcm0ub2JqSWRMaXN0ID0gdGhpcy5pZHM7CiAgICAgICAgICAgIGF3YWl0IHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZShhc3luYyB2YWxpZCA9PiB7CiAgICAgICAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICAgICAgICB0aGlzLnVwbG9hZEltZ0RhdGEuYnVzaW5lc3NJZCA9IHRoaXMuZm9ybS5vYmpJZDsKICAgICAgICAgICAgICAgIHRoaXMudXBsb2FkRm9ybSgpOwogICAgICAgICAgICAgICAgYXdhaXQgc2F2ZU9yVXBkYXRlKHRoaXMuZm9ybSkudGhlbihyZXMgPT4gewogICAgICAgICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICAgICAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IGZhbHNlOwogICAgICAgICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnBhc3MgPSB0cnVlOwogICAgICAgICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuYnVzaW5lc3NLZXkgPSByb3cub2JqSWQ7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5wcm9jZXNzVHlwZSA9IHR5cGU7CiAgICAgICAgICAgICAgICAgICAgdGhpcy5hY3Rpdml0aU9wdGlvbi50aXRsZSA9ICLlip7nu5MiOwogICAgICAgICAgICAgICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuZGVmYXVsdEZyb20gPSBmYWxzZTsKICAgICAgICAgICAgICAgICAgICB0aGlzLmlzU2hvdyA9IHRydWU7CiAgICAgICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5aSx6LSlIik7CiAgICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IGZhbHNlOwogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuYnVzaW5lc3NLZXkgPSByb3cub2JqSWQ7CiAgICAgICAgdGhpcy5wcm9jZXNzRGF0YS5wcm9jZXNzVHlwZSA9IHR5cGU7CiAgICAgICAgdGhpcy5hY3Rpdml0aU9wdGlvbi50aXRsZSA9ICLlm57pgIDljp/lm6DloavlhpkiOwogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEuZGVmYXVsdEZyb20gPSB0cnVlOwogICAgICAgIHRoaXMucHJvY2Vzc0RhdGEudmFyaWFibGVzLnBhc3MgPSBmYWxzZTsKICAgICAgICB0aGlzLmlzU2hvdyA9IHRydWU7CiAgICAgIH0KICAgIH0sCiAgICAvL+WIl+ihqOafpeivogogICAgYXN5bmMgZ2V0RGF0YShwYXJhbXMpIHsKICAgICAgdHJ5IHsKICAgICAgICB0aGlzLnBhcmFtcyA9IHsgLi4udGhpcy5wYXJhbXMsIC4uLnBhcmFtcyB9OwogICAgICAgIGNvbnN0IHBhcmFtID0gdGhpcy5wYXJhbXM7CiAgICAgICAgcGFyYW0ubXlTb3J0cyA9IFt7IHByb3A6ICJ1cGRhdGVUaW1lIiwgYXNjOiBmYWxzZSB9XTsKICAgICAgICBpZiAoIXBhcmFtLnN0YXR1cykgewogICAgICAgICAgcGFyYW0uc3RhdHVzID0gIjAsMSwyLDMsNCI7CiAgICAgICAgfQogICAgICAgIGNvbnN0IHsgZGF0YSwgY29kZSB9ID0gYXdhaXQgZ2V0TGlzdChwYXJhbSk7CiAgICAgICAgaWYgKGNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgZm9yIChsZXQgaSBvZiBkYXRhLnJlY29yZHMpIHsKICAgICAgICAgICAgaS5mZ3NtYyA9ICLphY3nlLXov5Dnu7TliIblhazlj7giOwogICAgICAgICAgICB0aGlzLnN0YXR1c09wdGlvbnMuZm9yRWFjaChlbGVtZW50ID0+IHsKICAgICAgICAgICAgICBpZiAoaS5zdGF0dXMgPT09IGVsZW1lbnQudmFsdWUpIHsKICAgICAgICAgICAgICAgIGkuc3RhdHVzQ24gPSBlbGVtZW50LmxhYmVsOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8udGFibGVEYXRhID0gZGF0YS5yZWNvcmRzOwogICAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnRvdGFsID0gZGF0YS50b3RhbDsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICBjb25zb2xlLmxvZyhlKTsKICAgICAgfQogICAgfSwKICAgIC8v56Gu5a6a5oyJ6ZKuCiAgICBhc3luYyBzYXZlUm93KCkgewogICAgICBhd2FpdCB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUoYXN5bmMgdmFsaWQgPT4gewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgdHJ5IHsKICAgICAgICAgICAgLy/kv53lrZjliJfooajluo/lj7cKICAgICAgICAgICAgbGV0IHRhYmxlQXJyID0gdGhpcy5wcm9wVGFibGVEYXRhLmNvbEZpcnN0OwogICAgICAgICAgICBmb3IgKHZhciBpID0gMDsgaSA8IHRhYmxlQXJyLmxlbmd0aDsgaSsrKSB7CiAgICAgICAgICAgICAgdGFibGVBcnJbaV0ueGggPSBpICsgMTsKICAgICAgICAgICAgfQogICAgICAgICAgICB0aGlzLmZvcm0uY29sRmlyc3QgPSB0YWJsZUFycjsKICAgICAgICAgICAgdGhpcy5mb3JtLm9iaklkTGlzdCA9IHRoaXMuaWRzOwogICAgICAgICAgICB0aGlzLmZvcm0uc3RhdHVzID0gIjAiOwogICAgICAgICAgICBsZXQgeyBjb2RlLCBkYXRhIH0gPSBhd2FpdCBzYXZlT3JVcGRhdGUodGhpcy5mb3JtKTsKICAgICAgICAgICAgaWYgKGNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5pON5L2c5oiQ5YqfIik7CiAgICAgICAgICAgICAgLy/ph43nva5wYWdl6aG15LuOMeW8gOWniwogICAgICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci5wYWdlUmVzaXplID0gIlkiOwogICAgICAgICAgICAgIGF3YWl0IHRoaXMuZ2V0RGF0YSgpOwogICAgICAgICAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IGZhbHNlOwogICAgICAgICAgICB9CiAgICAgICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgICAgIGNvbnNvbGUubG9nKGUpOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy/kv67mlLnmjInpkq4KICAgIGdldFVwZGF0ZShyb3cpIHsKICAgICAgdGhpcy5nZXRDenBteChyb3cpOwogICAgICB0aGlzLnRpdGxlID0gIumFjeeUteWAkumXuOaTjeS9nOelqOS/ruaUuSI7CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICB0aGlzLmZvcm0gPSB7IC4uLnJvdyB9OwogICAgICAvL+WbvueJh+e7k+WQiAogICAgICB0aGlzLmltZ0xpc3QgPSB0aGlzLmZvcm0uaW1nTGlzdDsKICAgICAgdGhpcy5pc0Rpc2FibGVkQmogPSBmYWxzZTsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gdHJ1ZTsKICAgICAgdGhpcy5nZXRTaG93KCk7CiAgICB9LAogICAgLy/or6bmg4UKICAgIGdldEluZm8ocm93KSB7CiAgICAgIHRoaXMuZ2V0Q3pwbXgocm93KTsKICAgICAgdGhpcy50aXRsZSA9ICLphY3nlLXmk43kvZznpajor6bmg4Xmn6XnnIsiOwogICAgICB0aGlzLmZvcm0gPSB7IC4uLnJvdyB9OwogICAgICAvL+WbvueJh+e7k+WQiAogICAgICB0aGlzLmltZ0xpc3QgPSB0aGlzLmZvcm0uaW1nTGlzdDsKICAgICAgdGhpcy5pc0Rpc2FibGVkID0gdHJ1ZTsKICAgICAgdGhpcy5pc0Rpc2FibGVkQmogPSB0cnVlOwogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlOwogICAgICB0aGlzLmdldFNob3coKTsKICAgIH0sCiAgICAvL+aWsOWingogICAgZ2V0SW5zdGVyKCkgewogICAgICB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QgPSBbXTsKICAgICAgdGhpcy50aXRsZSA9ICLphY3nlLXlgJLpl7jmk43kvZznpajlop7liqAiOwogICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZTsKICAgICAgdGhpcy5mb3JtID0geyBmZ3M6IHRoaXMuJHN0b3JlLmdldHRlcnMuZGVwdElkLnRvU3RyaW5nKCkgfTsKICAgICAgdGhpcy5mb3JtLnN0YXR1cyA9ICLmlrDlu7oiOwogICAgICAvL+mFjeeUtQogICAgICB0aGlzLmZvcm0ubHggPSAzOwogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlOwogICAgfSwKICAgIC8v5Yig6Zmk5oyJ6ZKuCiAgICBhc3luYyBkZWxldGVSb3cocm93KSB7CiAgICAgIGxldCBvYmpJZCA9ICIiOwogICAgICBpZiAocm93Lm9iaklkKSB7CiAgICAgICAgb2JqSWQgPSByb3cub2JqSWQ7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgb2JqSWQgPSB0aGlzLmlkc1swXTsKICAgICAgfQogICAgICB0aGlzLiRjb25maXJtKCLmraTmk43kvZzlsIbmsLjkuYXliKDpmaTor6XmlbDmja4sIOaYr+WQpue7p+e7rT8iLCAi5o+Q56S6IiwgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwKICAgICAgICB0eXBlOiAid2FybmluZyIKICAgICAgfSkKICAgICAgICAudGhlbigoKSA9PiB7CiAgICAgICAgICByZW1vdmUob2JqSWQpLnRoZW4oKHsgY29kZSB9KSA9PiB7CiAgICAgICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTmiJDlip8hIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIC8v6YeN572ucGFnZemhteS7jjHlvIDlp4sKICAgICAgICAgICAgICAvLyB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIucGFnZVJlc2l6ZSA9ICJZIjsKICAgICAgICAgICAgICB0aGlzLmdldERhdGEoKTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgIHR5cGU6ICJlcnJvciIsCiAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5Yig6Zmk5aSx6LSlISIKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfSkKICAgICAgICAuY2F0Y2goKCkgPT4gewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICJpbmZvIiwKICAgICAgICAgICAgbWVzc2FnZTogIuW3suWPlua2iOWIoOmZpCIKICAgICAgICAgIH0pOwogICAgICAgIH0pOwogICAgfSwKICAgIC8v6KGo5qC85paw5aKeCiAgICBsaXN0Rmlyc3RBZGQoaW5kZXgpIHsKICAgICAgbGV0IHJvdyA9IHsKICAgICAgICBjenhtOiAiIiwKICAgICAgICBzZndjOiAiIgogICAgICB9OwogICAgICB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QucHVzaChyb3cpOwogICAgICB0aGlzLnByb3BUYWJsZURhdGEuc2VsID0gcm93OwogICAgICB0aGlzLmZvcm0uY3p4cyA9IHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdC5sZW5ndGg7CiAgICAgIHRoaXMuZm9ybS53enhjenhzID0gdGhpcy5wcm9wVGFibGVEYXRhLmNvbEZpcnN0Lmxlbmd0aDsKICAgIH0sCiAgICAvL+ihqOagvOWIoOmZpAogICAgbGlzdEZpcnN0RGVsKGluZGV4LCByb3cpIHsKICAgICAgdGhpcy5pZHMucHVzaChyb3cub2JqSWQpOwogICAgICB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3Quc3BsaWNlKGluZGV4LCAxKTsKICAgICAgY29uc29sZS5sb2coInNwbGljZSBpbmRleCA9PSAiLCBpbmRleCk7CiAgICAgIHRoaXMuZm9ybS5jenhzID0gdGhpcy5wcm9wVGFibGVEYXRhLmNvbEZpcnN0Lmxlbmd0aDsKICAgICAgdGhpcy5mb3JtLnd6eGN6eHMgPSB0aGlzLnByb3BUYWJsZURhdGEuY29sRmlyc3QubGVuZ3RoOwogICAgfSwKICAgIC8v5YWz6Zet5by556qXCiAgICBjbG9zZSgpIHsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gZmFsc2U7CiAgICB9LAogICAgLy/pgInmi6nooYwKICAgIHNlbGVjdENoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5vYmpJZCk7CiAgICAgIHRoaXMuc2VsZWN0aW9uID0gc2VsZWN0aW9uOwogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDE7CiAgICAgIHRoaXMuZm9ybSA9IHRoaXMuc2VsZWN0aW9uWzBdOwogICAgfSwKICAgIC8qKgogICAgICrojrflj5bmk43kvZznpajmmI7nu4YKICAgICAqLwogICAgYXN5bmMgZ2V0Q3pwbXgocm93KSB7CiAgICAgIHRyeSB7CiAgICAgICAgY29uc3QgeyBkYXRhLCBjb2RlIH0gPSBhd2FpdCBnZXRDenBteExpc3QoeyBvYmpJZDogcm93Lm9iaklkIH0pOwogICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIC8v57uZbGlzdOa3u+WKoOWtl+autQogICAgICAgICAgZGF0YS5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgICB0aGlzLiRzZXQoaXRlbSwgImlzU2V0IiwgdHJ1ZSk7CiAgICAgICAgICB9KTsKICAgICAgICAgIHRoaXMucHJvcFRhYmxlRGF0YS5jb2xGaXJzdCA9IGRhdGE7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgY29uc29sZS5sb2coZSk7CiAgICAgIH0KICAgIH0sCiAgICAvL+WvvOWHundvcmQKICAgIGFzeW5jIGV4cG9ydFdvcmQocm93KSB7CiAgICAgIHRyeSB7CiAgICAgICAgbGV0IGV4cG9ydERhdGEgPSB7IC4uLnJvdyB9OwogICAgICAgIGF3YWl0IGV4cG9ydFdvcmQoZXhwb3J0RGF0YSwgInBkemR6Y3pwIiwgIumFjeeUteWAkumXuOaTjeS9nOelqC5kb2N4Iik7CiAgICAgIH0gY2F0Y2ggKGUpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLlr7zlh7rlpLHotKXvvIEiKTsKICAgICAgfQogICAgfSwKICAgIC8v5a+85Ye6UGRmCiAgICBhc3luYyBleHBvcnRQZGYocm93KSB7CiAgICAgIHRyeSB7CiAgICAgICAgbGV0IGV4cG9ydERhdGEgPSB7IC4uLnJvdyB9OwogICAgICAgIGF3YWl0IGV4cG9ydFBkZihleHBvcnREYXRhLCAicGR6ZHpjenAiLCAi6YWN55S15YCS6Ze45pON5L2c56WoLnBkZiIpOwogICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5a+85Ye65aSx6LSl77yBIik7CiAgICAgIH0KICAgIH0KICB9Cn07Cg=="}, {"version": 3, "sources": ["dzczp.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwpBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "dzczp.vue", "sourceRoot": "src/views/dagangOilfield/czpgl/pddzcz", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 180 }\"\n      @handleReset=\"filterReset\"\n    />\n    <el-white class=\"button-group\">\n      <div class=\"button_btn\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-plus\"\n          @click=\"getInster\"\n          v-hasPermi=\"['pddzczp:button:add']\"\n          >新增\n        </el-button>\n        <el-button\n          type=\"danger\"\n          icon=\"el-icon-delete\"\n          v-if=\"hasSuperRole\"\n          @click=\"deleteRow\"\n          :disabled=\"single\"\n          >删除</el-button\n        >\n      </div>\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"selectChange\"\n        height=\"65vh\"\n      >\n        <!-- <el-table-column\n          prop=\"statusCn\"\n          slot=\"table_start\"\n          align=\"center\"\n          style=\"display: block\"\n          label=\"流程状态\"\n          min-width=\"120\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-badge\n              v-if=\"scope.row.isBack === 1\"\n              value=\"退回\"\n              class=\"item\"\n              type=\"danger\"\n            >\n            </el-badge>\n            <span>{{ scope.row.statusCn }}</span>\n          </template>\n        </el-table-column> -->\n        <el-table-column\n          slot=\"table_eight\"\n          align=\"center\"\n          fixed=\"right\"\n          style=\"display: block\"\n          label=\"操作\"\n          width=\"160\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              @click=\"getInfo(scope.row)\"\n              type=\"text\"\n              size=\"small\"\n              title=\"详情\"\n              class=\"el-icon-view\"\n            />\n            <el-button\n              @click=\"getUpdate(scope.row)\"\n              class=\"el-icon-edit\"\n              title=\"编辑\"\n              v-if=\"\n                (scope.row.status === '0' &&\n                  scope.row.createBy === currentUser) ||\n                  hasSuperRole\n              \"\n              type=\"text\"\n              size=\"small\"\n            />\n            <el-button\n              @click=\"deleteRow(scope.row)\"\n              class=\"el-icon-delete\"\n              title=\"删除\"\n              v-if=\"\n                scope.row.status === '0' &&\n                  scope.row.createBy === currentUser &&\n                  scope.row.isStart === 0\n              \"\n              type=\"text\"\n              size=\"small\"\n            />\n            <el-button\n                type=\"text\"\n                size=\"small\"\n                @click=\"nullifyGzp(scope.row.objId)\"\n                v-if=\"scope.row.status < 4 && scope.row.status > 0 &&\n                ( scope.row.createBy === currentUser || (scope.row.bzspr && scope.row.bzspr === currentUser) \n                || (scope.row.fgsspr && scope.row.fgsspr === currentUser) \n                || (scope.row.bjr && scope.row.bjr === currentUser)\n                || hasSuperRole )\n                \"\n                title=\"作废\"\n                class=\"el-icon-close\"\n              >\n              </el-button>\n            <el-button\n              @click=\"showTimeLine(scope.row)\"\n              v-if=\"scope.row.isStart === 1\"\n              type=\"text\"\n              size=\"small\"\n              class=\"el-icon-lcck commonIcon\"\n              title=\"流程查看\"\n            />\n            <el-button\n              @click=\"showProcessImg(scope.row)\"\n              v-if=\"scope.row.isStart === 1\"\n              type=\"text\"\n              size=\"small\"\n              class=\"el-icon-lct commonIcon\"\n              title=\"流程图\"\n            />\n            <!--              <el-button @click=\"exportWord(scope.row)\" v-if=\"scope.row.status == '待办结'\" type=\"text\" size=\"small\">\n                              导出Word\n                            </el-button>-->\n            <el-button\n              @click=\"previewFile(scope.row)\"\n              v-if=\"scope.row.status > 2\"\n              type=\"text\"\n              size=\"small\"\n              class=\"el-icon-zoom-in\"\n              title=\"预览\"\n            />\n            <el-button\n              @click=\"exportPdf(scope.row)\"\n              v-if=\"scope.row.status > 2\"\n              type=\"text\"\n              size=\"small\"\n              class=\"el-icon-pdf-export commonIcon\"\n              title=\"导出pdf\"\n            />\n          </template>\n        </el-table-column>\n      </comp-table>\n    </el-white>\n\n    <!-- 详情/新增/修改 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <div>\n          <el-row>\n            <el-col :span=\"8\">\n              <el-form-item label=\"单位：\" prop=\"fgs\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.fgs\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择单位\"\n                  disabled\n                >\n                  <el-option\n                    v-for=\"item in dwSelected\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\"\n                  />\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"配电站名称：\" prop=\"bdzmc\">\n                <el-select\n                  style=\"width: 100%\"\n                  filterable\n                  v-model=\"form.bdzmc\"\n                  :disabled=\"isDisabled\"\n                  :placeholder=\"isDisabled ? '' : '请选择'\"\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in pdsOptionsDataList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"编号：\" prop=\"bm\">\n                <el-input\n                  v-model=\"form.bm\"\n                  disabled\n                  placeholder=\"确认后编号自动生成\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"发令人：\"\n                prop=\"flr\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-input\n                  v-model=\"form.flr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请输入发令人\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"受令人：\"\n                prop=\"slr\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-input\n                  v-model=\"form.slr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请输入受令人\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"发令时间：\"\n                prop=\"flsj\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-date-picker\n                  v-model=\"form.flsj\"\n                  :disabled=\"isDisabledBj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  :placeholder=\"isDisabled ? '' : '选择日期时间'\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"操作开始时间：\"\n                prop=\"kssj\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-date-picker\n                  v-model=\"form.kssj\"\n                  :disabled=\"isDisabledBj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  :placeholder=\"isDisabled ? '' : '选择日期时间'\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"操作结束时间：\"\n                prop=\"jssj\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-date-picker\n                  v-model=\"form.jssj\"\n                  :disabled=\"isDisabledBj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  :placeholder=\"isDisabled ? '' : '选择日期时间'\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"操作人：\"\n                prop=\"czr\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-input\n                  v-model=\"form.czr\"\n                  :disabled=\"isDisabledBj\"\n                  :placeholder=\"isDisabledBj ? '' : '请输入操作人'\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"监护人：\"\n                prop=\"jhr\"\n                :rules=\"\n                  form.status === '3' && form.sfyzx === '已执行'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-input\n                  v-model=\"form.jhr\"\n                  :disabled=\"isDisabledBj\"\n                  :placeholder=\"isDisabledBj ? '' : '请输入监护人'\"\n                />\n              </el-form-item>\n            </el-col>\n            <!--            <el-col :span=\"8\">-->\n            <!--              <el-form-item label=\"审核人：\" prop=\"pdshr\">-->\n            <!--                <el-input style=\"width:100%\" :disabled=\"true\" v-model=\"form.pdshr\" placeholder=\"请输入审核人\"/>-->\n            <!--              </el-form-item>-->\n            <!--            </el-col>-->\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作项数：\" prop=\"czxs\">\n                <el-input-number\n                  v-model=\"form.czxs\"\n                  disabled\n                  placeholder=\"请输入操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"已执行项数：\" prop=\"yzxczxs\">\n                <el-input-number\n                  v-model=\"form.yzxczxs\"\n                  disabled\n                  placeholder=\"请输入已执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"未执行项数：\" prop=\"wzxczxs\">\n                <el-input-number\n                  v-model=\"form.wzxczxs\"\n                  disabled\n                  placeholder=\"请输入未执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                label=\"是否已执行：\"\n                prop=\"sfyzx\"\n                :rules=\"\n                  form.status === '3'\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-select\n                  v-model=\"form.sfyzx\"\n                  :placeholder=\"isDisabled ? '' : '请选择'\"\n                  :disabled=\"isDisabledBj\"\n                >\n                  <el-option\n                    v-for=\"item in sfyzxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"操作任务：\" prop=\"czrw\">\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"form.czrw\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请输入操作任务\"\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"备注：\" prop=\"bz\">\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"form.bz\"\n                  :disabled=\"isDisabled\"\n                  :placeholder=\"isDisabled ? '' : '请输入内容'\"\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <el-col :span=\"24\">\n              <el-form-item label=\"上传图片：\" v-if=\"\">\n                <el-upload\n                  :disabled=\"isDisabledBj\"\n                  action=\"\"\n                  ref=\"uploadImg\"\n                  accept=\"image/jpeg,image/jpg,image/png\"\n                  :headers=\"header\"\n                  :multiple=\"true\"\n                  :on-change=\"handleChange\"\n                  :data=\"uploadImgData\"\n                  :file-list=\"imgList\"\n                  :auto-upload=\"false\"\n                  list-type=\"picture-card\"\n                  :on-preview=\"handlePictureCardPreview\"\n                  :on-progress=\"handleProgress\"\n                  :on-remove=\"handleRemove\"\n                >\n                  <i class=\"el-icon-plus\"></i>\n                </el-upload>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n        <!--预览的内容-->\n        <div>\n          <div align=\"left\">\n            <el-upload\n              v-if=\"!isDisabled\"\n              action=\"\"\n              ref=\"upload\"\n              accept=\".xlsx\"\n              :limit=\"1\"\n              :auto-upload=\"false\"\n              :show-file-list=\"false\"\n              :on-change=\"importExcel\"\n            >\n              <el-button type=\"info\" @click.stop=\"handleYlChange\"\n                >预览</el-button\n              >\n              <el-button\n                type=\"success\"\n                icon=\"el-icon-download\"\n                @click.stop=\"exportExcel\"\n                >导出</el-button\n              >\n              <el-button type=\"success\" icon=\"el-icon-upload\">导入</el-button>\n              <div\n                slot=\"tip\"\n                class=\"el-upload__tip\"\n                style=\"width: 220px;color: red\"\n              >\n                先导出，再导入，只能上传当前页面导出的Excel文件\n              </div>\n            </el-upload>\n          </div>\n          <el-dialog :title=\"titleyl\" :visible.sync=\"yl\" append-to-body>\n            <!--第一个表格-->\n            <el-table\n              :data=\"propTableData.colFirst\"\n              disabled\n              border\n              stripe\n              style=\"width: 100%\"\n              :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n            >\n              <el-table-column\n                width=\"80\"\n                align=\"center\"\n                prop=\"xh\"\n                label=\"序号\"\n                sortable\n              >\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"序号\"\n                      disabled\n                      v-model=\"scope.row.xh\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"请输入操作项目\"\n                      disabled\n                      v-model=\"scope.row.czrw\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-dialog>\n        </div>\n        <!--列表-->\n        <div>\n          <div align=\"right\">\n            <el-checkbox @change=\"handleCheckAllChange\" :disabled=\"isDisabledBj\"\n              >全选</el-checkbox\n            >\n          </div>\n          <!--第一个表格-->\n          <el-table\n            :data=\"propTableData.colFirst\"\n            :disabled=\"isDisabled\"\n            height=\"300\"\n            border\n            stripe\n            style=\"width: 100%\"\n          >\n            <el-table-column width=\"70\" align=\"center\" label=\"换行操作\">\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"handleCurrentChange(scope.$index)\"\n                  type=\"text\"\n                  size=\"small\"\n                  :disabled=\"isDisabled\"\n                  >{{ flag === \"start\" ? \"点击换行\" : \"换到此行\" }}</el-button\n                >\n              </template>\n            </el-table-column>\n\n            <el-table-column\n              type=\"index\"\n              width=\"60\"\n              align=\"center\"\n              label=\"顺序号\"\n            >\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入操作项目\"\n                    :disabled=\"isDisabled\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              type=\"sfwc\"\n              width=\"120\"\n              label=\"是否完成\"\n              align=\"center\"\n            >\n              <template slot-scope=\"scope\">\n                <el-checkbox\n                  v-model=\"scope.row.sfwc\"\n                  :disabled=\"isDisabledBj\"\n                  @change=\"handleCheckChange\"\n                ></el-checkbox>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" width=\"80\">\n              <template slot=\"header\" slot-scope=\"scope\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  :disabled=\"isDisabled\"\n                  icon=\"el-icon-plus\"\n                  @click=\"listFirstAdd(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n              <template slot-scope=\"scope\">\n                <el-button\n                  type=\"text\"\n                  icon=\"el-icon-delete\"\n                  :disabled=\"isDisabled\"\n                  @click=\"listFirstDel(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button\n          v-if=\"\n            isDisabled &&\n              buttonNameShow &&\n              form.status > 0 &&\n              form.status !== '3'\n          \"\n          type=\"info\"\n          @click=\"getSbFsBj('rollback')\"\n          >退 回\n        </el-button>\n        <el-button\n          v-if=\"isDisabled && buttonNameShow\"\n          type=\"success\"\n          @click=\"getSbFsBj('complete')\"\n          >{{ buttonName }}\n        </el-button>\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"saveRow\"\n          >保 存</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <!--图片放大展示弹出框-->\n    <el-image-viewer\n      v-if=\"imgDialogVisible\"\n      :on-close=\"onClose\"\n      :url-list=\"dialogImageUrl\"\n      zIndex=\"3000\"\n    />\n\n    <!--  工作流  -->\n    <activiti\n      ref=\"activiti\"\n      :processData=\"processData\"\n      :isShow=\"isShow\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n    ></activiti>\n    <time-line\n      :value=\"timeLineShow\"\n      :timeData=\"timeData\"\n      @closeTimeLine=\"colseTimeLine\"\n    />\n    <!--流程图查看-->\n    <el-dialog\n      title=\"审批流程图\"\n      :visible.sync=\"openLoadingImg\"\n      width=\"56%\"\n      append-to-body\n      :close-on-click-modal=\"false\"\n      v-dialogDrag\n    >\n      <div>\n        <img :src=\"imgSrc\" style=\"width: 100%\" />\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport {\n  getCzpmxList,\n  getList,\n  remove,\n  saveOrUpdate\n} from \"@/api/yxgl/pdyxgl/pddzczp\";\nimport {\n  exportPdf,\n  exportWord,\n  previewFile,\n  updateById\n} from \"@/api/yxgl/bdyxgl/bddzczp\";\nimport api from \"@/utils/request\";\n//流程\nimport activiti from \"com/activiti_czp\";\nimport timeLine from \"com/timeLine\";\nimport { HistoryList } from \"@/api/activiti/processTask\";\nimport { getPdsOptionsDataList } from \"@/api/dagangOilfield/asset/pdsgl\";\nimport ElImageViewer from \"element-ui/packages/image/src/image-viewer\";\nimport { exportToExcel, importFromExcel } from \"@/components/common/excel.js\";\nexport default {\n  name: \"dzczp\",\n  components: { activiti, timeLine, ElImageViewer },\n  props: {\n    isTj: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      hasSuperRole: this.$store.getters.hasSuperRole,\n      pdsOptionsDataList: [],\n      isDisabledBj: true,\n      statusOptions: [\n        {\n          value: \"0\",\n          label: \"操作票填报\"\n        },\n        // {\n        //   value: \"1\",\n        //   label: \"班组审核\"\n        // },\n        {\n          value: \"2\",\n          label: \"分公司审核\"\n        },\n        {\n          value: \"3\",\n          label: \"操作票办结\"\n        },\n        {\n          value: \"4\",\n          label: \"结束\"\n        },{ label: \"作废\", value: \"7\" }\n      ],\n      buttonNameShow: false,\n      buttonName: \"\",\n      rules: {\n        fgs: [{ required: true, message: \"单位不能为空\", trigger: \"select\" }],\n        pdzmc: [{ required: true, message: \"单位不能为空\", trigger: \"select\" }],\n        // flr: [{ required: true, message: \"发令人不能为空\", trigger: \"change\" }],\n        // slr: [{ required: true, message: \"受令人不能为空\", trigger: \"change\" }],\n        // flsj: [\n        //   { required: true, message: \"发令时间不能为空\", trigger: \"change\" }\n        // ],\n        czrw: [\n          { required: true, message: \"操作任务不能为空\", trigger: \"change\" }\n        ]\n        // czr: [{ message: \"操作人不能为空\", trigger: \"blur\" }],\n        // jhr: [{ message: \"监护人不能为空\", trigger: \"blur\" }],\n        // kssj: [{ message: \"操作开始时间不能为空\", trigger: \"change\" }],\n        // jssj: [{ message: \"操作结束时间不能为空\", trigger: \"change\" }]\n      },\n      bjr: \"\",\n      // 是否已执行下拉框\n      sfyzxList: [\n        { label: \"已执行\", value: \"已执行\" },\n        { label: \"未执行\", value: \"未执行\" }\n      ],\n      // 获取当前登录人账号\n      currentUser: this.$store.getters.name,\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"czplc\",\n        businessKey: \"\",\n        businessType: \"倒闸操作票\",\n        variables: {},\n        defaultFrom: true,\n        nextUser: \"\",\n        processType: \"complete\"\n      },\n      //工作流弹窗\n      isShow: false,\n      //流程图查看\n      openLoadingImg: false,\n      imgSrc: \"\", //流程图查看地址\n      timeData: [],\n      timeLineShow: false,\n      //弹出框标题\n      activitiOption: { title: \"\" },\n      titleyl: \"\",\n      //图片地址url\n      dialogImageUrl: \"\",\n      //展示图片dialog控制\n      imgDialogVisible: false,\n      //上传图片时的携带的其他参数\n      uploadImgData: {\n        businessId: \"\" //携带的表单主键id\n      },\n      //上传图片时的请求头\n      header: {},\n      //图片list\n      imgList: [],\n      loginForm: {\n        userName: \"\",\n        password: \"\",\n        rememberMe: false,\n        code: \"6666\",\n        uuid: \"\"\n      },\n      selection: [],\n      yl: false,\n      // 多选框选中的id\n      ids: [],\n      single: true,\n      multiple: true,\n      //弹出框中表格数据\n      propTableData: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      dwSelected: [{ label: \"配电运维分公司\", value: \"3013\" }],\n      //form表单\n      form: {\n        status: \"\",\n        kssj: \"\",\n        jssj: \"\",\n        czrw: \"\",\n        flr: \"\",\n        slr: \"\",\n        flsj: \"\",\n        lx: 3, //配电\n        colFirst: [],\n        pdzmc: \"\",\n        czxs: 0,\n        yzxczxs: 0,\n        wzxczxs: 0\n      },\n      formCzp: {\n        pdshr: \"\",\n        yj: \"\"\n      },\n      //详情弹框是否显示\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          bm: \"\",\n          slr: \"\",\n          flr: \"\",\n          czsjArr: [],\n          flsjArr: [],\n        },\n        fieldList: [\n          { label: \"编号\", value: \"bm\", type: \"input\", clearable: true },\n          {\n            label: \"操作时间\",\n            value: \"czsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"受令人\", type: \"input\", value: \"slr\", clearable: true },\n          { label: \"发令人\", type: \"input\", value: \"flr\", clearable: true },\n          {\n            label: \"发令时间\",\n            value: \"flsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          {\n            label: \"状态\",\n            value: \"status\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            clearable: true,\n            options: [\n              {\n                value: \"0\",\n                label: \"操作票填报\"\n              },\n              /*{\n                value: '1',\n                label: '班组审核'\n              },*/\n              {\n                value: \"2\",\n                label: \"分公司审核\"\n              },\n              {\n                value: \"3\",\n                label: \"操作票办结\"\n              },{ label: \"作废\", value: \"7\" }\n            ]\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"编号\", prop: \"bm\", minWidth: \"80\" },\n          { label: \"状态\", prop: \"statusCn\", minWidth: \"80\" },\n          { label: \"配电站名称\", prop: \"pdzCn\", minWidth: \"120\" },\n          { label: \"操作开始时间\", prop: \"kssj\", minWidth: \"120\" },\n          { label: \"操作结束时间\", prop: \"jssj\", minWidth: \"120\" },\n          { label: \"操作任务\", prop: \"czrw\", minWidth: \"120\" },\n          // {label: '审核人', prop: 'pdshr', minWidth: '100'},\n          { label: \"发令人\", prop: \"flr\", minWidth: \"100\" },\n          { label: \"受令人\", prop: \"slr\", minWidth: \"100\" },\n          { label: \"发令时间\", prop: \"flsj\", minWidth: \"120\" }\n        ]\n      },\n      selectRows: [],\n      params: {\n        //配电\n        lx: 3,\n        status: \"0,1,2,3,4\" //状态。将结束状态的过滤掉\n      },\n      //换行操作\n      oldIndex: undefined,\n      newIndex: undefined,\n      flag: \"start\"\n    };\n  },\n  mounted() {\n    //获取token\n    this.header.token = getToken();\n    this.getPdsOptionsDataList();\n    //获取列表数据\n    this.getData(this.$route.query);\n  },\n  methods: {\n    //作废票\n    nullifyGzp(objId) {\n      this.$confirm(\"票作废后只能查看，不能进行任何操作，确认作废吗?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(async () => {\n          let { code } = await updateById({ status: 7, objId: objId });\n          if (code === \"0000\") {\n            this.$message.success(\"操作成功!!\");\n            this.getData();\n          }\n        })\n        .catch(() => {});\n    },\n    exportExcel() {\n      let excelData = this.propTableData.colFirst.map(item => ({ \"操作项目\": item.czrw}));\n      exportToExcel(excelData, \"操作项目.xlsx\");\n    },\n    importExcel(file, fileList) {\n      let fileName = file.name\n      if (!fileName.includes(\"操作项目\")) {\n        this.msgError(\"文件有误，请检查\")\n        this.$refs.upload.clearFiles()\n        return\n      }\n      importFromExcel(file)\n        .then(data => {\n          this.ids = this.propTableData.colFirst.map(item => item.objId)\n          this.propTableData.colFirst = data.map(item => ({xh: item.__rowNum__ , czrw: item[\"操作项目\"]}));\n        })\n        .catch(error => {\n          console.error(\"导入失败\", error);\n        });\n      this.$refs.upload.clearFiles()\n    },\n    //获取配电室下拉框数据\n    getPdsOptionsDataList() {\n      getPdsOptionsDataList({}).then(res => {\n        this.pdsOptionsDataList = res.data;\n      });\n    },\n    getShow() {\n      this.buttonNameShow = false;\n      switch (this.form.status) {\n        case \"0\":\n          this.buttonName = \"上 报\";\n          if (this.currentUser === this.form.createBy) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case \"1\":\n          this.buttonName = \"提 交\";\n          if (this.form.bzspr === this.currentUser) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case \"2\":\n          this.buttonName = \"提 交\";\n          if (this.form.fgsspr === this.currentUser) {\n            this.buttonNameShow = true;\n          }\n          break;\n        case \"3\":\n          this.buttonName = \"办 结\";\n          if (this.form.bjr === this.currentUser) {\n            this.buttonNameShow = true;\n            this.isDisabledBj = false;\n          }\n          break;\n      }\n    },\n    filterReset() {\n      this.params = {\n        //配电\n        lx: 3,\n        status: \"0,1,2,3,4\" //状态。将结束状态的过滤掉\n      };\n    },\n    //预览文件\n    async previewFile(row) {\n      try {\n        let exportData = { ...row };\n        await previewFile(exportData, \"pdzdzczp\");\n      } catch (e) {\n        this.$message.error(\"预览失败！\");\n      }\n    },\n    //清空\n    change(e) {\n      this.$forceUpdate();\n    },\n    //关闭弹窗\n    closeActiviti() {\n      if (this.$refs.activiti.$refs.form) {\n        this.$refs.activiti.$refs.form.resetFields();\n      }\n      this.isShow = false;\n    },\n    //关闭流程查看页面\n    colseTimeLine() {\n      this.timeLineShow = false;\n    },\n    async showTimeLine(row) {\n      this.processData.businessKey = row.objId;\n      let { code, data } = await HistoryList(this.processData);\n      this.timeData = data;\n      this.timeLineShow = true;\n    },\n    //流程图片查看\n    showProcessImg(row) {\n      console.log(row);\n      this.openLoadingImg = true;\n      this.imgSrc =\n        \"/activiti-api/process/read-resource?processDefinitionKey=czplc&businessKey=\" +\n        row.objId +\n        \"&t=\" +\n        new Date().getTime();\n    },\n    //改造后的上传多个图片文件\n    uploadForm() {\n      var newUrl = []; //用来存放当前未改动过的图片url,此图片不进行删除处理，其余当前业务id下面的图片数据将进行删除\n      // console.log(this.$refs.uploadImg.fileList)\n      var imageType = [\"png\", \"jpg\"];\n      console.log(\"开始上传图片\");\n      console.log(this.imgList);\n      const formData = new FormData();\n      // 因为要传一个文件数组过去，所以要循环append\n      this.imgList.forEach(file => {\n        if (file.raw == undefined) {\n          console.log(file.url);\n          newUrl.push(file.url);\n        }\n        formData.append(\"files\", file.raw);\n      });\n      formData.append(\"businessId\", this.uploadImgData.businessId); // 自定义参数\n      formData.append(\"newUrl\", newUrl); // 未改动过的图片url\n      formData.append(\"type\", imageType); // 未改动过的图片url\n      api\n        .requestPost(\"/isc-api/file/uploadFiles\", formData, 1)\n        .then(res => {\n          console.log(\"上传图片成功的反回值\");\n          console.log(res);\n          // 清空图片列表（一定要清空，否则上传成功后还是会调用handleChange（）函数，上传成功后列表中还存在图片）\n          this.imgList = [];\n          //查询接口数据\n          this.getData();\n        })\n        .catch(res => {\n          this.$message.success(\"图片上传失败！\");\n        });\n    },\n    // 选择文件时，往fileList里添加\n    handleChange(file, fileList) {\n      this.imgList = fileList;\n    },\n    handleProgress(event, file, fileList) {\n      console.log(\"event\", event);\n      console.log(\"file\", file);\n      console.log(\"fileList\", fileList);\n    },\n    //图片移除\n    handleRemove(file, fileList) {\n      this.imgList = fileList;\n    },\n    //图片放大\n    handlePictureCardPreview(file) {\n      this.dialogImageUrl = [file.url];\n      this.imgDialogVisible = true;\n    },\n    onClose() {\n      this.imgDialogVisible = false;\n    },\n    // 预览弹框\n    handleYlChange() {\n      this.titleyl = \"查看操作项目\";\n      this.yl = true;\n    },\n    // 全选框\n    handleCheckAllChange(val) {\n      if (val) {\n        this.form.yzxczxs = this.form.czxs;\n        this.form.wzxczxs = 0;\n      } else {\n        this.form.wzxczxs = this.form.czxs;\n        this.form.yzxczxs = 0;\n      }\n      this.propTableData.colFirst.forEach(item => {\n        item.sfwc = val;\n      });\n    },\n    handleCurrentChange(index) {\n      console.log(index);\n      if (this.flag === \"start\") {\n        this.oldIndex = index;\n        this.flag = \"end\";\n        this.$message.info(\"请点击需要更换到的行\");\n      } else if (this.flag == \"end\") {\n        this.newIndex = index;\n        this.flag = \"start\";\n        const oldCurrentRow = this.propTableData.colFirst.splice(\n          this.oldIndex,\n          1\n        )[0];\n        this.propTableData.colFirst.splice(this.newIndex, 0, oldCurrentRow);\n        this.$message.success(\"更换顺序成功\");\n      }\n    },\n    handleCheckChange(val) {\n      if (val) {\n        ++this.form.yzxczxs;\n      } else {\n        --this.form.yzxczxs;\n      }\n      this.form.wzxczxs = this.form.czxs - this.form.yzxczxs;\n    },\n    //工作流回传数据\n    async todoResult(data) {\n      let row = {\n        objId: data.businessKey,\n        isStart: 1,\n        isBack: data.processType === \"rollback\" ? 1 : 0\n      };\n      if (data.processType === \"rollback\") {\n        switch (data.activeTaskName) {\n          case \"操作票填报\":\n            row.status = \"0\";\n            break;\n        }\n      } else {\n        switch (data.activeTaskName) {\n          case \"分公司审核\":\n            row.status = \"2\";\n            row.fgsspr = data.nextUser;\n            break;\n          case \"操作票办结\":\n            row.status = \"3\";\n            row.bjr = data.nextUser;\n            break;\n          case \"结束\":\n            row.status = \"4\";\n            break;\n        }\n      }\n      let { code } = await updateById(row);\n      if (code === \"0000\") {\n        await this.getData();\n      } else {\n        this.$message.warning(\"操作失败\");\n      }\n    },\n    //上报发送办结\n    async getSbFsBj(type) {\n      let row = { ...this.form };\n      if (type === \"complete\") {\n        switch (row.status) {\n          case \"0\":\n            this.isShowDetails = false;\n            this.processData.variables.pass = true;\n            this.processData.businessKey = row.objId;\n            this.processData.processType = type;\n            // this.processData.dw = row.fgs;\n            this.processData.rylx = \"分公司审核人\";\n            this.activitiOption.title = \"提交\";\n            this.processData.defaultFrom = true;\n            this.processData.personGroupId = 14;\n            this.isShow = true;\n            break;\n          case \"2\":\n            this.isShowDetails = false;\n            this.processData.variables.pass = true;\n            this.processData.businessKey = row.objId;\n            this.processData.processType = type;\n            this.activitiOption.title = \"提交\";\n            this.processData.defaultFrom = true;\n            this.processData.personGroupId = 15;\n            // this.processData.dw = row.fgs;\n            this.processData.rylx = \"办结人\";\n            this.isShow = true;\n            break;\n          case \"3\":\n            this.form.colFirst = this.propTableData.colFirst;\n            this.form.objIdList = this.ids;\n            await this.$refs[\"form\"].validate(async valid => {\n              if (valid) {\n                this.uploadImgData.businessId = this.form.objId;\n                this.uploadForm();\n                await saveOrUpdate(this.form).then(res => {\n                  if (res.code === \"0000\") {\n                    this.getData();\n                    this.isShowDetails = false;\n                    this.processData.variables.pass = true;\n                    this.processData.businessKey = row.objId;\n                    this.processData.processType = type;\n                    this.activitiOption.title = \"办结\";\n                    this.processData.defaultFrom = false;\n                    this.isShow = true;\n                  } else {\n                    this.$message.error(\"失败\");\n                  }\n                });\n              }\n            });\n        }\n      } else {\n        this.isShowDetails = false;\n        this.processData.businessKey = row.objId;\n        this.processData.processType = type;\n        this.activitiOption.title = \"回退原因填写\";\n        this.processData.defaultFrom = true;\n        this.processData.variables.pass = false;\n        this.isShow = true;\n      }\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        param.mySorts = [{ prop: \"updateTime\", asc: false }];\n        if (!param.status) {\n          param.status = \"0,1,2,3,4\";\n        }\n        const { data, code } = await getList(param);\n        if (code === \"0000\") {\n          for (let i of data.records) {\n            i.fgsmc = \"配电运维分公司\";\n            this.statusOptions.forEach(element => {\n              if (i.status === element.value) {\n                i.statusCn = element.label;\n              }\n            });\n          }\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //确定按钮\n    async saveRow() {\n      await this.$refs[\"form\"].validate(async valid => {\n        if (valid) {\n          try {\n            //保存列表序号\n            let tableArr = this.propTableData.colFirst;\n            for (var i = 0; i < tableArr.length; i++) {\n              tableArr[i].xh = i + 1;\n            }\n            this.form.colFirst = tableArr;\n            this.form.objIdList = this.ids;\n            this.form.status = \"0\";\n            let { code, data } = await saveOrUpdate(this.form);\n            if (code === \"0000\") {\n              this.$message.success(\"操作成功\");\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              await this.getData();\n              this.isShowDetails = false;\n            }\n          } catch (e) {\n            console.log(e);\n          }\n        }\n      });\n    },\n    //修改按钮\n    getUpdate(row) {\n      this.getCzpmx(row);\n      this.title = \"配电倒闸操作票修改\";\n      this.isDisabled = false;\n      this.form = { ...row };\n      //图片结合\n      this.imgList = this.form.imgList;\n      this.isDisabledBj = false;\n      this.isShowDetails = true;\n      this.getShow();\n    },\n    //详情\n    getInfo(row) {\n      this.getCzpmx(row);\n      this.title = \"配电操作票详情查看\";\n      this.form = { ...row };\n      //图片结合\n      this.imgList = this.form.imgList;\n      this.isDisabled = true;\n      this.isDisabledBj = true;\n      this.isShowDetails = true;\n      this.getShow();\n    },\n    //新增\n    getInster() {\n      this.propTableData.colFirst = [];\n      this.title = \"配电倒闸操作票增加\";\n      this.isDisabled = false;\n      this.form = { fgs: this.$store.getters.deptId.toString() };\n      this.form.status = \"新建\";\n      //配电\n      this.form.lx = 3;\n      this.isShowDetails = true;\n    },\n    //删除按钮\n    async deleteRow(row) {\n      let objId = \"\";\n      if (row.objId) {\n        objId = row.objId;\n      } else {\n        objId = this.ids[0];\n      }\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove(objId).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              //重置page页从1开始\n              // this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    //表格新增\n    listFirstAdd(index) {\n      let row = {\n        czxm: \"\",\n        sfwc: \"\"\n      };\n      this.propTableData.colFirst.push(row);\n      this.propTableData.sel = row;\n      this.form.czxs = this.propTableData.colFirst.length;\n      this.form.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //表格删除\n    listFirstDel(index, row) {\n      this.ids.push(row.objId);\n      this.propTableData.colFirst.splice(index, 1);\n      console.log(\"splice index == \", index);\n      this.form.czxs = this.propTableData.colFirst.length;\n      this.form.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false;\n    },\n    //选择行\n    selectChange(selection) {\n      this.ids = selection.map(item => item.objId);\n      this.selection = selection;\n      this.single = selection.length !== 1;\n      this.form = this.selection[0];\n    },\n    /**\n     *获取操作票明细\n     */\n    async getCzpmx(row) {\n      try {\n        const { data, code } = await getCzpmxList({ objId: row.objId });\n        if (code === \"0000\") {\n          //给list添加字段\n          data.forEach(item => {\n            this.$set(item, \"isSet\", true);\n          });\n          this.propTableData.colFirst = data;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //导出word\n    async exportWord(row) {\n      try {\n        let exportData = { ...row };\n        await exportWord(exportData, \"pdzdzczp\", \"配电倒闸操作票.docx\");\n      } catch (e) {\n        this.$message.error(\"导出失败！\");\n      }\n    },\n    //导出Pdf\n    async exportPdf(row) {\n      try {\n        let exportData = { ...row };\n        await exportPdf(exportData, \"pdzdzczp\", \"配电倒闸操作票.pdf\");\n      } catch (e) {\n        this.$message.error(\"导出失败！\");\n      }\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.item {\n  width: 8.5rem;\n  height: 1.25rem;\n}\n</style>\n"]}]}