{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\dwzygl\\pdsbgl\\pdsbztbg.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\dwzygl\\pdsbgl\\pdsbztbg.js", "mtime": 1706897314385}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMudXBkYXRlU3RhdHVzID0gdXBkYXRlU3RhdHVzOwpleHBvcnRzLmdldFJlc3VtRGF0YUxpc3QgPSBnZXRSZXN1bURhdGFMaXN0OwoKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Cgp2YXIgYmFzZVVybCA9ICIvbWFuYWdlci1hcGkiOyAvL+eKtuaAgeWPmOabtAoKZnVuY3Rpb24gdXBkYXRlU3RhdHVzKHBhcmFtcykgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3RQb3N0KGJhc2VVcmwgKyAnL2R3enlQZHNienRiZy91cGRhdGVTdGF0dXMnLCBwYXJhbXMsIDEpOwp9IC8v5p+l6K+i54q25oCB5Y+Y5pu05L+h5oGvCgoKZnVuY3Rpb24gZ2V0UmVzdW1EYXRhTGlzdChwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgIi9kd3p5UGRzYnp0YmcvbGlzdCIsIHBhcmFtcywgMSk7Cn0="}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagangOilfield/dwzygl/pdsbgl/pdsbztbg.js"], "names": ["baseUrl", "updateStatus", "params", "api", "requestPost", "getResumDataList"], "mappings": ";;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,cAAhB,C,CAEA;;AACO,SAASC,YAAT,CAAsBC,MAAtB,EAA6B;AAClC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,4BAAxB,EAAqDE,MAArD,EAA4D,CAA5D,CAAP;AACD,C,CAED;;;AACO,SAASG,gBAAT,CAA0BH,MAA1B,EAAiC;AACtC,SAAQC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,oBAAxB,EAA6CE,MAA7C,EAAoD,CAApD,CAAR;AACD", "sourcesContent": ["import api from '@/utils/request'\nconst baseUrl = \"/manager-api\";\n\n//状态变更\nexport function updateStatus(params){\n  return api.requestPost(baseUrl+'/dwzyPdsbztbg/updateStatus',params,1)\n}\n\n//查询状态变更信息\nexport function getResumDataList(params){\n  return  api.requestPost(baseUrl+\"/dwzyPdsbztbg/list\",params,1);\n}\n"]}]}