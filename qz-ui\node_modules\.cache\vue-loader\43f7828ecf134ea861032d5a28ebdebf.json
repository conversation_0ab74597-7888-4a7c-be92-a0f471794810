{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\dgTodoItem\\processTodo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\dgTodoItem\\processTodo.vue", "mtime": 1755545381662}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB0aW1lTGluZSBmcm9tICJjb20vdGltZUxpbmUiOwppbXBvcnQgeyBIaXN0b3J5TGlzdCB9IGZyb20gIkAvYXBpL2FjdGl2aXRpL3Byb2Nlc3NUYXNrIjsKaW1wb3J0IHsKICBsaXN0LAogIGxpc3RCeVBhZ2UsCiAgaW5zZXJ0T3JVcGRhdGVUb2RvSXRlbSwKICBkZWxCeUlkcwp9IGZyb20gIkAvYXBpL2FjdGl2aXRpL0RnVG9kb0l0ZW0iOwppbXBvcnQgeyBtYXBTdGF0ZSwgbWFwTXV0YXRpb25zLCBtYXBBY3Rpb25zIH0gZnJvbSAndnVleCcKCmV4cG9ydCBkZWZhdWx0IHsKICBjb21wb25lbnRzOiB7IHRpbWVMaW5lIH0sCiAgbmFtZTogInByb2Nlc3NUb2RvIiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZmlsdGVySW5mbzogewogICAgICAgIGRhdGE6IHsKICAgICAgICAgIG1vZHVsZTogIiIsCiAgICAgICAgICB0b2RvVXNlck5hbWU6ICIiLAogICAgICAgICAgaGFuZGxlVXNlck5hbWU6ICIiLAogICAgICAgICAgYXBwbHlVc2VyTmFtZTogIiIKICAgICAgICB9LAogICAgICAgIGZpZWxkTGlzdDogWwogICAgICAgICAgeyBsYWJlbDogIuW+heW<PERSON>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"}, {"version": 3, "sources": ["processTodo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAy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file": "processTodo.vue", "sourceRoot": "src/views/activiti/dgTodoItem", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-white>\n      <el-tabs v-model=\"activeName\" type=\"card\" @tab-click=\"handleClick\">\n        <el-tab-pane name=\"db\">\n          <span slot=\"label\"><i class=\"el-icon-s-order\"></i>待办</span>\n        </el-tab-pane>\n        <el-tab-pane name=\"yb\">\n          <span slot=\"label\"><i class=\"el-icon-s-claim\"></i>已办</span>\n        </el-tab-pane>\n      </el-tabs>\n      <el-filter\n        ref=\"filter1\"\n        :data=\"filterInfo.data\"\n        :field-list=\"filterInfo.fieldList\"\n        :width=\"{ labelWidth: 180, itemWidth: 200 }\"\n        @handleReset=\"filterReset\"\n        @handleEvent=\"handleFilterEvent\"\n        :btnHidden=\"false\"\n      ></el-filter>\n\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"selectChange\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"handleCurrentChange\"\n        height=\"75vh\"\n      >\n        <el-table-column\n          slot=\"table_start\"\n          align=\"center\"\n          style=\"display: block;word-break : normal;\"\n          label=\"事项标题\"\n          min-width=\"200\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-badge\n              :value=\"\n                scope.row.isHandle == 0\n                  ? scope.row.itemContent &&\n                    scope.row.itemContent.includes('退回')\n                    ? '被退回'\n                    : '待办理'\n                  : '已办理'\n              \"\n              class=\"item\"\n              :type=\"\n                scope.row.isHandle == 0\n                  ? scope.row.itemContent &&\n                    scope.row.itemContent.includes('退回')\n                    ? 'warning'\n                    : 'danger'\n                  : 'primary'\n              \"\n            >\n            </el-badge>\n            <el-button type=\"text\" size=\"small\" @click=\"goPage(scope.row)\">{{\n              scope.row.itemName\n            }}</el-button>\n          </template>\n        </el-table-column>\n        <el-table-column\n          slot=\"table_eight\"\n          align=\"center\"\n          fixed=\"right\"\n          style=\"display: block\"\n          label=\"操作\"\n          min-width=\"100\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              title=\"跳转\"\n              class=\"el-icon-discover\"\n              @click=\"goPage(scope.row)\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              title=\"流程查看\"\n              class=\"el-icon-lcck commonIcon\"\n              @click=\"showTimeLine(scope.row)\"\n              v-if=\"scope.row.moduleKey !== 'probfbk'\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              @click=\"deleteTodo(scope.row)\"\n              title=\"删除\"\n              class=\"el-icon-delete\"\n            >\n            </el-button>\n          </template>\n        </el-table-column>\n      </comp-table>\n    </el-white>\n\n    <el-dialog\n      title=\"待办详情\"\n      :visible.sync=\"openInfo\"\n      width=\"50%\"\n      append-to-body\n      :close-on-click-modal=\"false\"\n      v-dialogDrag\n    >\n      <el-form ref=\"formInfo\" :model=\"formInfo\" label-width=\"120px\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item label=\"标题：\" prop=\"itemName\">\n              <el-input v-model=\"formInfo.itemName\" :disabled=\"isDisabled\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"内容：\" prop=\"itemContent\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"3\"\n                v-model=\"formInfo.itemContent\"\n                :disabled=\"isDisabled\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"模块名称：\" prop=\"module\">\n              <el-input v-model=\"formInfo.module\" :disabled=\"isDisabled\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"通知时间：\" prop=\"todoTime\">\n              <el-date-picker\n                v-model=\"formInfo.todoTime\"\n                type=\"datetime\"\n                :disabled=\"isDisabled\"\n                format=\"yyyy-MM-dd HH:mm:ss\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n    </el-dialog>\n    <time-line\n      :value=\"timeLineShow\"\n      :timeData=\"timeData\"\n      @closeTimeLine=\"colseTimeLine\"\n    />\n  </div>\n</template>\n\n<script>\nimport timeLine from \"com/timeLine\";\nimport { HistoryList } from \"@/api/activiti/processTask\";\nimport {\n  list,\n  listByPage,\n  insertOrUpdateTodoItem,\n  delByIds\n} from \"@/api/activiti/DgTodoItem\";\nimport { mapState, mapMutations, mapActions } from 'vuex'\n\nexport default {\n  components: { timeLine },\n  name: \"processTodo\",\n  data() {\n    return {\n      filterInfo: {\n        data: {\n          module: \"\",\n          todoUserName: \"\",\n          handleUserName: \"\",\n          applyUserName: \"\"\n        },\n        fieldList: [\n          { label: \"待办来源\", type: \"input\", value: \"module\" },\n          { label: \"待办人\", type: \"input\", value: \"todoUserName\" },\n          { label: \"处理人\", type: \"input\", value: \"handleUserName\" },\n          { label: \"发起人\", type: \"input\", value: \"applyUserName\" }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"module\", label: \"待办来源\", minWidth: \"120\" },\n          { prop: \"taskName\", label: \"任务名称\", minWidth: \"140\" },\n          { prop: \"isHandleCn\", label: \"是否已办\", minWidth: \"80\" },\n          { prop: \"todoUserName\", label: \"待办人名称\", minWidth: \"100\" },\n          { prop: \"handleUserName\", label: \"处理人名称\", minWidth: \"100\" },\n          { prop: \"applyUserName\", label: \"发起人名称\", minWidth: \"100\" },\n          { prop: \"todoTime\", label: \"通知时间\", minWidth: \"150\" },\n          { prop: \"handleTime\", label: \"处理时间\", minWidth: \"150\" }\n        ],\n        option: { checkBox: false, serialNumber: true }\n      },\n      params: {\n        isHandle: 0\n      },\n      activeName: \"db\",\n      selectRows: [],\n      tabRefresh: {\n        db: 0,\n        yb: 1\n      },\n      openInfo: false,\n      formInfo: {},\n      isDisabled: false,\n      timeData: [],\n      timeLineShow: false,\n      processData: {\n        processDefinitionKey: \"\",\n        businessKey: \"\",\n        businessType: \"\",\n        variables: {},\n        nextUser: \"\",\n        processType: \"complete\"\n      }\n    };\n  },\n  // watch: {\n  //   //触发计算函数时执行\n  //   qxjlObjIdChange(val) {\n  //     this.getData({ objId: val });\n  //   }\n  // },\n  //计算函数\n  computed: {\n    ...mapState('todoList', {\n      savedActiveName: 'activeName',\n      savedFilterInfo: 'filterInfo',\n      savedPagination: 'pagination',\n      savedScrollPosition: 'scrollPosition',\n      savedTableData: 'tableData',\n      savedTotal: 'total'\n    })\n  },\n  created() {\n    // 从 store 恢复状态\n    this.restorePageState()\n  },\n  mounted() {\n    // 恢复滚动位置\n    this.$nextTick(() => {\n      this.restoreScrollPosition()\n      // 添加滚动监听\n      this.addScrollListener()\n    })\n  },\n\n  // keep-alive组件激活时调用\n  activated() {\n    console.log('组件激活，刷新数据')\n    // 每次激活时刷新数据，确保显示最新状态\n    this.getData(this.$route.query)\n\n    // 恢复滚动位置\n    this.$nextTick(() => {\n      // 使用setTimeout确保页面完全渲染\n      setTimeout(() => {\n        this.restoreScrollPosition()\n        // 确保滚动监听存在\n        this.addScrollListener()\n      }, 200)\n    })\n  },\n\n  // keep-alive组件失活时调用\n  deactivated() {\n    console.log('组件失活，保存状态')\n    // 先保存当前状态（使用当前已知的滚动位置，不重新获取）\n    this.saveStateWithCurrentScrollPosition()\n    // 移除滚动监听\n    this.removeScrollListener()\n  },\n\n  // 组件销毁前\n  beforeDestroy() {\n    // 移除滚动监听\n    this.removeScrollListener()\n  },\n  beforeRouteLeave(to, from, next) {\n    // 在路由离开前保存页面状态\n    // 注意：不要在这里调用saveCurrentState，因为此时表格可能已经被重置\n    // 滚动位置应该已经通过滚动监听实时保存了\n    console.log('路由离开，当前保存的滚动位置:', this.savedScrollPosition)\n    next()\n  },\n  methods: {\n    ...mapActions('todoList', ['savePageState', 'saveFilterInfo', 'savePagination', 'saveScrollPosition', 'saveTableData', 'markNeedRefresh', 'clearRefreshFlag']),\n\n    // 保存当前状态\n    saveCurrentState() {\n      // 使用保存的滚动容器引用，或者重新查找\n      let scrollPosition = 0\n      if (this._scrollContainer) {\n        scrollPosition = this._scrollContainer.scrollTop\n      } else {\n        // 重新查找滚动容器\n        const tableWrapper = document.querySelector('.el-table__body-wrapper') ||\n                           document.querySelector('.wrap .el-table__body-wrapper')\n        if (tableWrapper) {\n          scrollPosition = tableWrapper.scrollTop\n        }\n      }\n\n      console.log('保存滚动位置:', scrollPosition)\n\n      this.savePageState({\n        activeName: this.activeName,\n        filterInfo: this.filterInfo.data,\n        pagination: {\n          pageSize: this.tableAndPageInfo.pager.pageSize,\n          pageNum: this.tableAndPageInfo.pager.pageNum\n        },\n        scrollPosition: scrollPosition,\n        tableData: this.tableAndPageInfo.tableData,\n        total: this.tableAndPageInfo.pager.total\n      })\n    },\n\n    // 使用当前已保存的滚动位置保存状态（避免重新获取可能为0的值）\n    saveStateWithCurrentScrollPosition() {\n      console.log('使用当前已保存的滚动位置:', this.savedScrollPosition)\n\n      this.savePageState({\n        activeName: this.activeName,\n        filterInfo: this.filterInfo.data,\n        pagination: {\n          pageSize: this.tableAndPageInfo.pager.pageSize,\n          pageNum: this.tableAndPageInfo.pager.pageNum\n        },\n        scrollPosition: this.savedScrollPosition, // 使用已保存的值\n        tableData: this.tableAndPageInfo.tableData,\n        total: this.tableAndPageInfo.pager.total\n      })\n    },\n\n    // 恢复页面状态的方法\n    restorePageState() {\n      // 恢复标签页\n      if (this.savedActiveName && this.savedActiveName !== this.activeName) {\n        this.activeName = this.savedActiveName\n        this.params.isHandle = this.tabRefresh[this.savedActiveName]\n      }\n\n      // 恢复筛选条件\n      if (this.savedFilterInfo && Object.keys(this.savedFilterInfo).length > 0) {\n        this.filterInfo.data = { ...this.savedFilterInfo }\n      }\n\n      // 恢复分页信息\n      if (this.savedPagination) {\n        this.tableAndPageInfo.pager.pageSize = this.savedPagination.pageSize\n        this.tableAndPageInfo.pager.pageNum = this.savedPagination.pageNum\n      }\n\n      // 直接获取最新数据，不使用缓存数据避免显示已办理的待办\n      // 缓存数据仅用于滚动位置恢复，不用于数据显示\n      this.getData(this.$route.query);\n    },\n\n    // 恢复滚动位置\n    restoreScrollPosition() {\n      if (this.savedScrollPosition > 0) {\n        this.$nextTick(() => {\n          // 尝试多种可能的滚动容器选择器\n          let scrollContainer = null\n\n          // 1. 尝试标准的el-table滚动容器\n          scrollContainer = document.querySelector('.el-table__body-wrapper')\n\n          // 2. 如果没找到，尝试comp-table的包装器\n          if (!scrollContainer) {\n            scrollContainer = document.querySelector('.wrap .el-table__body-wrapper')\n          }\n\n          // 3. 如果还没找到，尝试通过comp-table组件查找\n          if (!scrollContainer) {\n            const compTableEl = this.$el.querySelector('.wrap')\n            if (compTableEl) {\n              scrollContainer = compTableEl.querySelector('.el-table__body-wrapper')\n            }\n          }\n\n          console.log('恢复滚动位置:', this.savedScrollPosition, '容器:', scrollContainer)\n\n          if (scrollContainer) {\n            scrollContainer.scrollTop = this.savedScrollPosition\n\n            // 验证设置是否成功\n            setTimeout(() => {\n              console.log('滚动位置恢复验证:', scrollContainer.scrollTop)\n            }, 100)\n          } else {\n            console.warn('未找到表格滚动容器')\n          }\n        })\n      }\n    },\n\n    // 添加滚动监听\n    addScrollListener() {\n      this.$nextTick(() => {\n        // 如果已经有监听器，先移除\n        if (this._scrollHandler) {\n          this.removeScrollListener()\n        }\n\n        // 使用和恢复滚动位置相同的逻辑查找滚动容器\n        let scrollContainer = null\n\n        // 1. 尝试标准的el-table滚动容器\n        scrollContainer = document.querySelector('.el-table__body-wrapper')\n\n        // 2. 如果没找到，尝试comp-table的包装器\n        if (!scrollContainer) {\n          scrollContainer = document.querySelector('.wrap .el-table__body-wrapper')\n        }\n\n        // 3. 如果还没找到，尝试通过comp-table组件查找\n        if (!scrollContainer) {\n          const compTableEl = this.$el.querySelector('.wrap')\n          if (compTableEl) {\n            scrollContainer = compTableEl.querySelector('.el-table__body-wrapper')\n          }\n        }\n\n        // 4. 最后尝试通过高度属性查找\n        if (!scrollContainer) {\n          const tables = document.querySelectorAll('.el-table__body-wrapper')\n          for (let table of tables) {\n            const tableEl = table.closest('.el-table')\n            if (tableEl && tableEl.style.height === '75vh') {\n              scrollContainer = table\n              break\n            }\n          }\n        }\n\n        console.log('添加滚动监听的容器:', scrollContainer)\n\n        if (scrollContainer) {\n          this._scrollContainer = scrollContainer // 保存引用\n          this._scrollHandler = this.throttle(() => {\n            const currentScrollTop = scrollContainer.scrollTop\n            console.log('滚动位置变化:', currentScrollTop)\n            // 直接更新store中的滚动位置\n            this.saveScrollPosition(currentScrollTop)\n          }, 300)\n          scrollContainer.addEventListener('scroll', this._scrollHandler)\n        } else {\n          console.warn('未找到表格滚动容器，无法添加滚动监听')\n        }\n      })\n    },\n\n    // 移除滚动监听\n    removeScrollListener() {\n      if (this._scrollContainer && this._scrollHandler) {\n        this._scrollContainer.removeEventListener('scroll', this._scrollHandler)\n        this._scrollHandler = null\n        this._scrollContainer = null\n        console.log('滚动监听已移除')\n      }\n    },\n\n    // 节流函数\n    throttle(func, delay) {\n      let timeoutId\n      let lastExecTime = 0\n      return function (...args) {\n        const currentTime = Date.now()\n\n        if (currentTime - lastExecTime > delay) {\n          func.apply(this, args)\n          lastExecTime = currentTime\n        } else {\n          clearTimeout(timeoutId)\n          timeoutId = setTimeout(() => {\n            func.apply(this, args)\n            lastExecTime = Date.now()\n          }, delay - (currentTime - lastExecTime))\n        }\n      }\n    },\n\n    // 检查并修正页码\n    checkAndCorrectPageNum(records, total) {\n      const currentPage = this.tableAndPageInfo.pager.pageNum\n      const pageSize = this.tableAndPageInfo.pager.pageSize\n      const maxPage = Math.ceil(total / pageSize) || 1\n\n      console.log('页码检查:', {\n        currentPage,\n        maxPage,\n        total,\n        recordsLength: records.length,\n        hasData: records.length > 0\n      })\n\n      // 情况1：当前页超出了最大页数\n      if (currentPage > maxPage) {\n        console.log(`当前页${currentPage}超出最大页${maxPage}，修正到第${maxPage}页`)\n        this.tableAndPageInfo.pager.pageNum = maxPage\n        // 更新store中的分页信息，并清除滚动位置\n        this.savePagination({\n          pageSize: pageSize,\n          pageNum: maxPage\n        })\n        this.saveScrollPosition(0) // 清除滚动位置\n        return true // 需要重新请求\n      }\n\n      // 情况2：当前页没有数据，但总数大于0（说明数据在其他页）\n      if (records.length === 0 && total > 0 && currentPage > 1) {\n        console.log(`当前页${currentPage}无数据但总数${total}>0，修正到第${maxPage}页`)\n        this.tableAndPageInfo.pager.pageNum = maxPage\n        // 更新store中的分页信息，并清除滚动位置\n        this.savePagination({\n          pageSize: pageSize,\n          pageNum: maxPage\n        })\n        this.saveScrollPosition(0) // 清除滚动位置\n        return true // 需要重新请求\n      }\n\n      // 情况3：总数为0，确保在第1页\n      if (total === 0 && currentPage !== 1) {\n        console.log('总数为0，修正到第1页')\n        this.tableAndPageInfo.pager.pageNum = 1\n        // 更新store中的分页信息，并清除滚动位置\n        this.savePagination({\n          pageSize: pageSize,\n          pageNum: 1\n        })\n        this.saveScrollPosition(0) // 清除滚动位置\n        return true // 需要重新请求\n      }\n\n      return false // 不需要修正\n    },\n\n    // 调试方法：查看页面中所有可能的滚动容器\n    debugScrollContainers() {\n      console.log('=== 调试滚动容器 ===')\n\n      // 查找所有可能的滚动容器\n      const containers = [\n        { name: 'app-container', el: document.querySelector('.app-container') },\n        { name: 'el-table__body-wrapper', el: document.querySelector('.el-table__body-wrapper') },\n        { name: 'wrap .el-table__body-wrapper', el: document.querySelector('.wrap .el-table__body-wrapper') },\n        { name: 'comp-table内的wrapper', el: this.$el.querySelector('.wrap .el-table__body-wrapper') }\n      ]\n\n      containers.forEach(container => {\n        if (container.el) {\n          console.log(`${container.name}:`, {\n            element: container.el,\n            scrollTop: container.el.scrollTop,\n            scrollHeight: container.el.scrollHeight,\n            clientHeight: container.el.clientHeight,\n            hasScroll: container.el.scrollHeight > container.el.clientHeight\n          })\n        } else {\n          console.log(`${container.name}: 未找到`)\n        }\n      })\n\n      // 查找所有el-table__body-wrapper\n      const allWrappers = document.querySelectorAll('.el-table__body-wrapper')\n      console.log('所有el-table__body-wrapper:', allWrappers)\n      allWrappers.forEach((wrapper, index) => {\n        console.log(`wrapper ${index}:`, {\n          element: wrapper,\n          scrollTop: wrapper.scrollTop,\n          scrollHeight: wrapper.scrollHeight,\n          clientHeight: wrapper.clientHeight,\n          hasScroll: wrapper.scrollHeight > wrapper.clientHeight,\n          parentTable: wrapper.closest('.el-table')\n        })\n      })\n\n      console.log('当前保存的滚动位置:', this.savedScrollPosition)\n      console.log('=== 调试结束 ===')\n    },\n    \n    async getData(param) {\n      // 合并路由参数和筛选条件\n      this.params = { ...this.params, ...param, ...this.filterInfo.data }\n\n      if (!this.params.todoUserId) {\n        this.params.todoUserId = this.$store.getters.name\n      }\n\n      let { code, data } = await listByPage({\n        ...this.params,\n        pageNum: this.tableAndPageInfo.pager.pageNum,\n        pageSize: this.tableAndPageInfo.pager.pageSize\n      })\n\n      if (code === \"0000\") {\n        // 检查页码是否需要修正\n        const needPageCorrection = this.checkAndCorrectPageNum(data.records, data.total)\n\n        if (needPageCorrection) {\n          // 页码需要修正，重新请求数据\n          console.log('页码需要修正，重新请求数据')\n          return this.getData(param)\n        }\n\n        this.tableAndPageInfo.tableData = data.records\n        this.tableAndPageInfo.pager.total = data.total\n\n        // 保存表格数据到store\n        this.saveTableData({\n          tableData: data.records,\n          total: data.total\n        })\n\n        // 清除刷新标记，表示数据已是最新\n        this.clearRefreshFlag()\n\n        // 数据加载完成后，延迟恢复滚动位置和添加滚动监听\n        this.$nextTick(() => {\n          // 使用setTimeout确保表格完全渲染\n          setTimeout(() => {\n            this.restoreScrollPosition()\n            this.addScrollListener()\n          }, 100)\n        })\n      }\n    },\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n\n    // 处理筛选条件变化事件\n    handleFilterEvent(obj, data) {\n      // 实时保存筛选条件\n      this.saveFilterInfo(data)\n      // 如果是回车或者change事件，触发搜索\n      if (obj.value !== undefined) {\n        this.filterInfo.data = data\n        this.tableAndPageInfo.pager.pageNum = 1 // 重置到第一页\n        this.getData()\n        // 保存分页状态\n        this.savePagination({\n          pageSize: this.tableAndPageInfo.pager.pageSize,\n          pageNum: 1\n        })\n      }\n    },\n\n    // 重置筛选条件\n    filterReset(data) {\n      this.filterInfo.data = data\n      this.tableAndPageInfo.pager.pageNum = 1 // 重置到第一页\n      this.getData()\n      // 保存重置后的筛选条件\n      this.saveFilterInfo(data)\n      // 保存分页状态\n      this.savePagination({\n        pageSize: this.tableAndPageInfo.pager.pageSize,\n        pageNum: 1\n      })\n    },\n    async getDetails(row) {\n      this.formInfo = { ...row };\n      this.isDisabled = true;\n      this.openInfo = true;\n      //如果是未查看状态，点击查看时变成已查看\n      // if(row.isView==0){\n      //   await insertOrUpdateTodoItem({objId:row.objId,isView:'1'})\n      //   this.getData()\n      // }\n    },\n    deleteTodo(row) {\n      this.$confirm(\"确认删除选中数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          delByIds([row.objId]).then(res => {\n            if (res.code === \"0000\") {\n              this.$message({\n                message: \"删除成功\",\n                type: \"success\"\n              });\n            } else {\n              this.$message.error(\"操作失败\");\n            }\n            this.getData();\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n    },\n    handleClick(tab) {\n      for (let key in this.tabRefresh) {\n        if (key === tab.name) {\n          this.activeName = tab.name\n          this.params.isHandle = this.tabRefresh[key]\n          // 切换标签页时重置分页到第一页\n          this.tableAndPageInfo.pager.pageNum = 1\n          this.getData()\n          // 保存当前状态\n          this.saveCurrentState()\n        }\n      }\n    },\n    closeForm() {\n      this.formInfo = {};\n    },\n    goPage(row) {\n      // 标记需要刷新数据，因为用户可能会办理待办\n      this.markNeedRefresh()\n\n      if (row.moduleKey === \"gzplccs\" && false) {\n        //解决工作票跳转404的问题,疑似舍弃了\n        const topMenus = this.$store.getters.topMenus;\n        if (topMenus.length > 0) {\n          for (const topMenu of topMenus) {\n            if (topMenu.name === \"工作票管理\") {\n              this.$router.push({\n                path: topMenu.path,\n                query: { objId: row.businessId, module: row.moduleKey }\n              });\n              break;\n            }\n          }\n        }\n      } else {\n        this.$router.push({\n          path: row.routePath,\n          query: { objId: row.businessId, module: row.moduleKey }\n        });\n      }\n    },\n    async showTimeLine(row) {\n      this.processData.businessKey = row.businessId;\n      this.processData.processDefinitionKey = row.moduleKey;\n      this.processData.businessType = row.module;\n      let { code, data } = await HistoryList(this.processData);\n      this.timeData = data;\n      this.timeLineShow = true;\n    },\n    //关闭流程查看页面\n    colseTimeLine() {\n      this.timeLineShow = false;\n    },\n    // 监听分页变化\n    handleSizeChange(val) {\n      this.tableAndPageInfo.pager.pageSize = val\n      this.tableAndPageInfo.pager.pageNum = 1 // 重置到第一页\n      this.getData()\n      // 保存分页状态\n      this.savePagination({\n        pageSize: val,\n        pageNum: 1\n      })\n    },\n    handleCurrentChange(val) {\n      this.tableAndPageInfo.pager.pageNum = val\n      this.getData()\n      // 保存分页状态\n      this.savePagination({\n        pageSize: this.tableAndPageInfo.pager.pageSize,\n        pageNum: val\n      })\n    },\n    // 监听筛选条件变化\n    handleFilter(data) {\n      this.filterInfo.data = data\n      this.tableAndPageInfo.pager.pageNum = 1 // 重置到第一页\n      this.getData(data)\n      // 保存筛选条件\n      this.saveFilterInfo(data)\n      // 保存分页状态\n      this.savePagination({\n        pageSize: this.tableAndPageInfo.pager.pageSize,\n        pageNum: 1\n      })\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.item {\n  width: 8.5rem;\n  height: 1.25rem;\n}\n</style>\n"]}]}