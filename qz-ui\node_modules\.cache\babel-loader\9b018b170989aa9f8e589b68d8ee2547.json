{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sdsbgl\\sdgt.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sdsbgl\\sdgt.vue", "mtime": 1706897324964}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON>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"}, {"version": 3, "sources": ["sdgt.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAirCA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,MADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,UAAA,EAAA;AAFA,OADA;AAKA;AACA,MAAA,WAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAJA,EAKA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OALA,EAMA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OANA,EAOA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAPA,EAQA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OARA,EASA;AAAA,QAAA,KAAA,EAAA,QAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OATA,EAUA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAVA,EAWA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAXA,EAYA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAZA,EAaA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAbA,EAcA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAdA,EAeA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAfA,EAgBA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAhBA,EAiBA;AAAA,QAAA,KAAA,EAAA,IAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAjBA,EAkBA;AAAA,QAAA,KAAA,EAAA,OAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAlBA,EAmBA;AAAA,QAAA,KAAA,EAAA,KAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAnBA,CANA;AA2BA;AACA,MAAA,GAAA,EAAA,EA5BA;AA6BA;AACA,MAAA,UAAA,EAAA,KA9BA;AA+BA;AACA,MAAA,UAAA,EAAA,IAhCA;AAiCA;AACA,MAAA,OAAA,EAAA,EAlCA;AAmCA;AACA,MAAA,QAAA,EAAA,EApCA;AAqCA;AACA,MAAA,SAAA,EAAA,EAtCA;AAuCA;AACA,MAAA,OAAA,EAAA,EAxCA;AAyCA;AACA,MAAA,OAAA,EAAA,EA1CA;AA2CA;AACA,MAAA,OAAA,EAAA,EA5CA;AA6CA;AACA,MAAA,OAAA,EAAA,EA9CA;AA+CA;AACA,MAAA,SAAA,EAAA,EAhDA;AAiDA;AACA,MAAA,OAAA,EAAA,EAlDA;AAoDA;AACA,MAAA,mBAAA,EAAA,KArDA;AAsDA;AACA,MAAA,oBAAA,EAAA,KAvDA;AAwDA;AACA,MAAA,qBAAA,EAAA,KAzDA;AA0DA;AACA,MAAA,mBAAA,EAAA,KA3DA;AA4DA;AACA,MAAA,mBAAA,EAAA,KA7DA;AA8DA;AACA,MAAA,mBAAA,EAAA,KA/DA;AAgEA;AACA,MAAA,mBAAA,EAAA,KAjEA;AAkEA;AACA,MAAA,qBAAA,EAAA,KAnEA;AAoEA;AACA,MAAA,mBAAA,EAAA,KArEA;AAuEA;AACA,MAAA,MAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA,EAJA;AAKA,QAAA,IAAA,EAAA,EALA;AAMA,QAAA,GAAA,EAAA,EANA;AAOA,QAAA,IAAA,EAAA;AAPA,OAxEA;AAiFA;AACA,MAAA,OAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,KAAA,EAAA,EAJA;AAKA,QAAA,KAAA,EAAA,EALA;AAMA,QAAA,KAAA,EAAA,EANA;AAOA,QAAA,KAAA,EAAA,EAPA;AAQA,QAAA,GAAA,EAAA,EARA;AASA,QAAA,IAAA,EAAA;AATA,OAlFA;AA6FA;AACA,MAAA,QAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA,EAJA;AAKA,QAAA,GAAA,EAAA,EALA;AAMA,QAAA,IAAA,EAAA;AANA,OA9FA;AAsGA;AACA,MAAA,MAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA,EAJA;AAKA,QAAA,IAAA,EAAA,EALA;AAMA,QAAA,KAAA,EAAA,EANA;AAOA,QAAA,KAAA,EAAA,EAPA;AAQA,QAAA,GAAA,EAAA;AARA,OAvGA;AAiHA;AACA,MAAA,MAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,EAAA,EAAA,EAJA;AAKA,QAAA,MAAA,EAAA,EALA;AAMA,QAAA,IAAA,EAAA,EANA;AAOA,QAAA,EAAA,EAAA,EAPA;AAQA,QAAA,GAAA,EAAA,EARA;AASA,QAAA,IAAA,EAAA;AATA,OAlHA;AA6HA;AACA,MAAA,MAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA,EAJA;AAKA,QAAA,IAAA,EAAA,EALA;AAMA,QAAA,IAAA,EAAA,EANA;AAOA,QAAA,KAAA,EAAA,EAPA;AAQA,QAAA,KAAA,EAAA,EARA;AASA,QAAA,KAAA,EAAA,EATA;AAUA,QAAA,OAAA,EAAA,EAVA;AAWA,QAAA,GAAA,EAAA;AAXA,OA9HA;AA2IA;AACA,MAAA,MAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA,EAJA;AAKA,QAAA,IAAA,EAAA,EALA;AAMA,QAAA,EAAA,EAAA,EANA;AAOA,QAAA,IAAA,EAAA,EAPA;AAQA,QAAA,IAAA,EAAA,EARA;AASA,QAAA,GAAA,EAAA;AATA,OA5IA;AAuJA;AACA,MAAA,QAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA,EAJA;AAKA,QAAA,GAAA,EAAA,EALA;AAMA,QAAA,IAAA,EAAA;AANA,OAxJA;AAgKA;AACA,MAAA,MAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA,EAJA;AAKA,QAAA,EAAA,EAAA,EALA;AAMA,QAAA,IAAA,EAAA,EANA;AAOA,QAAA,GAAA,EAAA;AAPA,OAjKA;AA0KA,MAAA,WAAA,EAAA;AACA,QAAA,UAAA,EAAA,SADA;AAEA,QAAA,IAAA,EAAA;AAFA,OA1KA;AA8KA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA;AAFA,OA9KA;AAkLA,MAAA,aAAA,EAAA,KAlLA;AAmLA,MAAA,OAAA,EAAA,CAAA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAAA,EAGA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAHA,EAMA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,KAAA,EAAA;AAFA,OANA,EASA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,CAnLA;AA+LA,MAAA,IAAA,EAAA,CAAA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAAA,EAGA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAHA,EAMA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OANA,CA/LA;AAyMA;AACA,MAAA,WAAA,EAAA,EA1MA;AA2MA;AACA,MAAA,iBAAA,EAAA,EA5MA;;AA6MA;AACA;AACA,MAAA,YAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,IAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA,EAFA;AAGA,UAAA,IAAA,EAAA,EAHA;AAIA,UAAA,IAAA,EAAA,EAJA;AAKA,UAAA,IAAA,EAAA,EALA;AAMA,UAAA,GAAA,EAAA,EANA;AAOA,UAAA,IAAA,EAAA;AAPA,SADA;AAUA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA;AAVA,OA/MA;AA6NA,MAAA,kBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA;AACA;;;;;;;;;;;AAPA,SAZA;AA+BA,QAAA,OAAA,EAAA;AACA,UAAA,IAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA,EAFA;AAGA,UAAA,IAAA,EAAA,EAHA;AAIA,UAAA,IAAA,EAAA,EAJA;AAKA,UAAA,IAAA,EAAA,EALA;AAMA,UAAA,GAAA,EAAA,EANA;AAOA,UAAA,IAAA,EAAA;AAPA;AA/BA,OA7NA;;AAsQA;AACA;AACA,MAAA,aAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,IAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA,EAFA;AAGA,UAAA,KAAA,EAAA,EAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA,EALA;AAMA,UAAA,KAAA,EAAA,EANA;AAOA,UAAA,GAAA,EAAA,EAPA;AAQA,UAAA,IAAA,EAAA;AARA,SADA;AAWA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA;AAXA,OAxQA;AAuRA,MAAA,mBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,UAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA;AAOA;;;AAGA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,IAHA;AAIA,UAAA,QAAA,EAAA,KAJA;AAKA,UAAA,KAAA,EAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WALA;AAMA,UAAA,SAAA,EAAA,CACA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WADA,EAEA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WAFA;AANA,SAVA,CAZA;AAkCA,QAAA,QAAA,EAAA;AACA,UAAA,IAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA,EAFA;AAGA,UAAA,KAAA,EAAA,EAHA;AAIA,UAAA,KAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA,EALA;AAMA,UAAA,KAAA,EAAA,EANA;AAOA,UAAA,GAAA,EAAA,EAPA;AAQA,UAAA,IAAA,EAAA;AARA;AAlCA,OAvRA;;AAoUA;AACA;AACA,MAAA,YAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA;AAFA,OAtUA;AA4UA,MAAA,kBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,UAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA;AAMA;AACA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,EAQA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,IAHA;AAIA,UAAA,QAAA,EAAA,KAJA;AAKA,UAAA,KAAA,EAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WALA;AAMA,UAAA,SAAA,EAAA,CACA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WADA,EAEA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WAFA;AANA,SARA,CAZA;AAgCA,QAAA,OAAA,EAAA;AACA,UAAA,IAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA,EAFA;AAGA,UAAA,IAAA,EAAA,EAHA;AAIA,UAAA,IAAA,EAAA,EAJA;AAKA,UAAA,IAAA,EAAA,EALA;AAMA,UAAA,KAAA,EAAA,EANA;AAOA,UAAA,KAAA,EAAA,EAPA;AAQA,UAAA,GAAA,EAAA;AARA;AAhCA,OA5UA;;AAuXA;AACA;AACA,MAAA,YAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA;AAFA,OAzXA;AA+XA,MAAA,kBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA;AAOA;AACA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,IAHA;AAIA,UAAA,QAAA,EAAA,KAJA;AAKA,UAAA,KAAA,EAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WALA;AAMA,UAAA,SAAA,EAAA,CACA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WADA,EAEA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WAFA;AANA,SARA,CAZA;AAgCA,QAAA,OAAA,EAAA;AACA,UAAA,IAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA,EAFA;AAGA,UAAA,IAAA,EAAA,EAHA;AAIA,UAAA,EAAA,EAAA,EAJA;AAKA,UAAA,MAAA,EAAA,EALA;AAMA,UAAA,IAAA,EAAA,EANA;AAOA,UAAA,EAAA,EAAA,EAPA;AAQA,UAAA,GAAA,EAAA,EARA;AASA,UAAA,IAAA,EAAA;AATA;AAhCA,OA/XA;;AA2aA;AACA;AACA,MAAA,YAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA;AAFA,OA7aA;AAmbA,MAAA,kBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,IAHA;AAIA,UAAA,QAAA,EAAA,KAJA;AAKA,UAAA,KAAA,EAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WALA;AAMA,UAAA,SAAA,EAAA,CACA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WADA,EAEA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WAFA;AANA,SAPA,CAZA;AA+BA,QAAA,OAAA,EAAA;AACA,UAAA,IAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA,EAFA;AAGA,UAAA,IAAA,EAAA,EAHA;AAIA,UAAA,IAAA,EAAA,EAJA;AAKA,UAAA,IAAA,EAAA,EALA;AAMA,UAAA,IAAA,EAAA,EANA;AAOA,UAAA,KAAA,EAAA,EAPA;AAQA,UAAA,KAAA,EAAA,EARA;AASA,UAAA,KAAA,EAAA,EATA;AAUA,UAAA,OAAA,EAAA,EAVA;AAWA,UAAA,GAAA,EAAA;AAXA;AA/BA,OAnbA;;AAgeA;AACA;AACA,MAAA,YAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA;AAFA,OAleA;AAweA,MAAA,kBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,IAHA;AAIA,UAAA,QAAA,EAAA,KAJA;AAKA,UAAA,KAAA,EAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WALA;AAMA,UAAA,SAAA,EAAA,CACA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WADA,EAEA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WAFA;AANA,SAPA,CAZA;AA+BA,QAAA,OAAA,EAAA;AACA,UAAA,IAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA,EAFA;AAGA,UAAA,IAAA,EAAA,EAHA;AAIA,UAAA,IAAA,EAAA,EAJA;AAKA,UAAA,IAAA,EAAA,EALA;AAMA,UAAA,EAAA,EAAA,EANA;AAOA,UAAA,IAAA,EAAA,EAPA;AAQA,UAAA,IAAA,EAAA,EARA;AASA,UAAA,GAAA,EAAA;AATA;AA/BA,OAxeA;;AAmhBA;AACA;AACA,MAAA,YAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA;AAFA,OArhBA;AA2hBA,MAAA,kBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,IAHA;AAIA,UAAA,QAAA,EAAA,KAJA;AAKA,UAAA,KAAA,EAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WALA;AAMA,UAAA,SAAA,EAAA,CACA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WADA,EAEA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WAFA;AANA,SAPA,CAZA;AA+BA,QAAA,OAAA,EAAA;AACA,UAAA,IAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA,EAFA;AAGA,UAAA,IAAA,EAAA,EAHA;AAIA,UAAA,IAAA,EAAA,EAJA;AAKA,UAAA,EAAA,EAAA,EALA;AAMA,UAAA,IAAA,EAAA,EANA;AAOA,UAAA,GAAA,EAAA;AAPA;AA/BA,OA3hBA;AAokBA,MAAA,aAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,YAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA;AAZA,OApkBA;AA0lBA;AACA,MAAA,wBAAA,EAAA,EA3lBA;AA4lBA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,IAAA,EAAA,EADA;AAEA,UAAA,QAAA,EAAA,EAFA;AAGA,UAAA,IAAA,EAAA;AAHA,SADA;AAMA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,MADA;AACA,UAAA,IAAA,EAAA,OADA;AACA,UAAA,KAAA,EAAA,UADA;AACA,UAAA,OAAA,EAAA;AADA,SADA,EAIA;AACA,UAAA,KAAA,EAAA,MADA;AACA,UAAA,IAAA,EAAA,QADA;AACA,UAAA,KAAA,EAAA,MADA;AACA,UAAA,OAAA,EAAA,CAAA;AAAA,YAAA,KAAA,EAAA,KAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAAA,EAAA;AAAA,YAAA,KAAA,EAAA,MAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAAA,EAAA;AAAA,YAAA,KAAA,EAAA,MAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAAA,EAAA;AAAA,YAAA,KAAA,EAAA,OAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAAA;AADA,SAJA,EAQA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA,CAAA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAAA,EAAA;AAAA,YAAA,KAAA,EAAA,IAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAAA;AAJA,SARA;AANA,OA5lBA;AAmnBA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,EAQA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,WAFA;AAGA,UAAA,KAAA,EAAA,IAHA;AAIA,UAAA,QAAA,EAAA,OAJA;AAKA,UAAA,KAAA,EAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WALA;AAMA,UAAA,SAAA,EAAA,CACA;AAAA,YAAA,IAAA,EAAA,MAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WADA,EAEA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WAFA,EAGA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WAHA;AANA,SARA;AAZA,OAnnBA;AAqpBA;AACA,MAAA,aAAA,EAAA,IAtpBA;AAupBA;AACA,MAAA,mBAAA,EAAA,KAxpBA;AAypBA;AACA,MAAA,cAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,KAAA,EAAA,KAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,IAAA,EAAA,SALA;AAMA,QAAA,IAAA,EAAA,YANA;AAOA,QAAA,GAAA,EAAA;AAPA,OADA,EASA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,KAAA,EAAA,KAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,IAAA,EAAA,SALA;AAMA,QAAA,IAAA,EAAA,YANA;AAOA,QAAA,GAAA,EAAA;AAPA,OATA,EAiBA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,KAAA,EAAA,KAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,IAAA,EAAA,SALA;AAMA,QAAA,IAAA,EAAA,YANA;AAOA,QAAA,GAAA,EAAA;AAPA,OAjBA,EAyBA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,KAAA,EAAA,KAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,IAAA,EAAA,SALA;AAMA,QAAA,IAAA,EAAA,YANA;AAOA,QAAA,GAAA,EAAA;AAPA,OAzBA,EAiCA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,KAAA,EAAA,KAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,IAAA,EAAA,SALA;AAMA,QAAA,IAAA,EAAA,YANA;AAOA,QAAA,GAAA,EAAA;AAPA,OAjCA,CA1pBA;AAqsBA;AACA,MAAA,YAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,GAAA,EAAA,MAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,IAAA,EAAA,IAJA;AAKA,QAAA,IAAA,EAAA,MALA;AAMA,QAAA,IAAA,EAAA,OANA;AAOA,QAAA,IAAA,EAAA;AAPA,OADA,EASA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,GAAA,EAAA,MAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,IAAA,EAAA,IAJA;AAKA,QAAA,IAAA,EAAA,MALA;AAMA,QAAA,IAAA,EAAA,OANA;AAOA,QAAA,IAAA,EAAA;AAPA,OATA,EAiBA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,GAAA,EAAA,MAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,IAAA,EAAA,IAJA;AAKA,QAAA,IAAA,EAAA,MALA;AAMA,QAAA,IAAA,EAAA,OANA;AAOA,QAAA,IAAA,EAAA;AAPA,OAjBA,EAyBA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,GAAA,EAAA,MAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,IAAA,EAAA,IAJA;AAKA,QAAA,IAAA,EAAA,MALA;AAMA,QAAA,IAAA,EAAA,OANA;AAOA,QAAA,IAAA,EAAA;AAPA,OAzBA,EAiCA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,GAAA,EAAA,MAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,IAAA,EAAA,IAJA;AAKA,QAAA,IAAA,EAAA,MALA;AAMA,QAAA,IAAA,EAAA,OANA;AAOA,QAAA,IAAA,EAAA;AAPA,OAjCA,CAtsBA;AAivBA;AACA,MAAA,YAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,MAFA;AAGA,QAAA,IAAA,EAAA,OAHA;AAIA,QAAA,IAAA,EAAA,OAJA;AAKA,QAAA,IAAA,EAAA,MALA;AAMA,QAAA,IAAA,EAAA,EANA;AAOA,QAAA,EAAA,EAAA,GAPA;AAQA,QAAA,IAAA,EAAA,YARA;AASA,QAAA,GAAA,EAAA,IATA;AAUA,QAAA,IAAA,EAAA;AAVA,OADA,EAYA;AACA,QAAA,IAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,MAFA;AAGA,QAAA,IAAA,EAAA,OAHA;AAIA,QAAA,IAAA,EAAA,OAJA;AAKA,QAAA,IAAA,EAAA,MALA;AAMA,QAAA,IAAA,EAAA,EANA;AAOA,QAAA,EAAA,EAAA,GAPA;AAQA,QAAA,IAAA,EAAA,YARA;AASA,QAAA,GAAA,EAAA,IATA;AAUA,QAAA,IAAA,EAAA;AAVA,OAZA,EAuBA;AACA,QAAA,IAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,MAFA;AAGA,QAAA,IAAA,EAAA,OAHA;AAIA,QAAA,IAAA,EAAA,OAJA;AAKA,QAAA,IAAA,EAAA,MALA;AAMA,QAAA,IAAA,EAAA,EANA;AAOA,QAAA,EAAA,EAAA,GAPA;AAQA,QAAA,IAAA,EAAA,YARA;AASA,QAAA,GAAA,EAAA,IATA;AAUA,QAAA,IAAA,EAAA;AAVA,OAvBA,EAkCA;AACA,QAAA,IAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,MAFA;AAGA,QAAA,IAAA,EAAA,OAHA;AAIA,QAAA,IAAA,EAAA,OAJA;AAKA,QAAA,IAAA,EAAA,MALA;AAMA,QAAA,IAAA,EAAA,EANA;AAOA,QAAA,EAAA,EAAA,GAPA;AAQA,QAAA,IAAA,EAAA,YARA;AASA,QAAA,GAAA,EAAA,IATA;AAUA,QAAA,IAAA,EAAA;AAVA,OAlCA,EA6CA;AACA,QAAA,IAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,MAFA;AAGA,QAAA,IAAA,EAAA,OAHA;AAIA,QAAA,IAAA,EAAA,OAJA;AAKA,QAAA,IAAA,EAAA,MALA;AAMA,QAAA,IAAA,EAAA,EANA;AAOA,QAAA,EAAA,EAAA,GAPA;AAQA,QAAA,IAAA,EAAA,YARA;AASA,QAAA,GAAA,EAAA,IATA;AAUA,QAAA,IAAA,EAAA;AAVA,OA7CA,EAwDA;AACA,QAAA,IAAA,EAAA,IADA;AAEA,QAAA,IAAA,EAAA,MAFA;AAGA,QAAA,IAAA,EAAA,OAHA;AAIA,QAAA,IAAA,EAAA,OAJA;AAKA,QAAA,IAAA,EAAA,MALA;AAMA,QAAA,IAAA,EAAA,EANA;AAOA,QAAA,EAAA,EAAA,GAPA;AAQA,QAAA,IAAA,EAAA,YARA;AASA,QAAA,GAAA,EAAA,IATA;AAUA,QAAA,IAAA,EAAA;AAVA,OAxDA,CAlvBA;AAuzBA;AACA,MAAA,eAAA,EAAA,MAxzBA;AAyzBA;AACA,MAAA,OAAA,EAAA,CACA;AACA;AACA;AAHA,OA1zBA;AA+zBA,MAAA,IAAA,EAAA,KA/zBA;AAg0BA;AACA,MAAA,MAAA,EAAA,EAj0BA;AAk0BA;AACA,MAAA,QAAA,EAAA;AACA,QAAA,UAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA,SAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA;AAJA,OAn0BA;AAy0BA;AACA,MAAA,oBAAA,EAAA,IA10BA;AA20BA;AACA,MAAA,aAAA,EAAA,QA50BA;AA60BA;AACA,MAAA,YAAA,EAAA,IA90BA;AA+0BA;AACA,MAAA,WAAA,EAAA,KAh1BA;AAi1BA;AACA,MAAA,WAAA,EAAA,KAl1BA;AAm1BA;AACA,MAAA,iBAAA,EAAA,KAp1BA;AAq1BA;AACA,MAAA,oBAAA,EAAA,KAt1BA;AAu1BA;AACA,MAAA,mBAAA,EAAA,KAx1BA;AAy1BA;AACA,MAAA,IAAA,EAAA,EA11BA;AA41BA,MAAA,OAAA,EAAA,KA51BA;AA61BA;AACA,MAAA,WAAA,EAAA,CAAA;AACA,QAAA,KAAA,EAAA,UADA;AAEA,QAAA,EAAA,EAAA,GAFA;AAGA,QAAA,GAAA,EAAA,SAHA;AAIA,QAAA,UAAA,EAAA,GAJA;AAKA,QAAA,QAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,EAAA,EAAA,KAFA;AAGA,UAAA,GAAA,EAAA,GAHA;AAIA,UAAA,UAAA,EAAA,GAJA;AAKA,UAAA,QAAA,EAAA,CACA;AACA,YAAA,KAAA,EAAA,aADA;AAEA,YAAA,EAAA,EAAA,SAFA;AAGA,YAAA,GAAA,EAAA,GAHA;AAIA,YAAA,UAAA,EAAA,GAJA;AAKA,YAAA,QAAA,EAAA;AALA,WADA,EAQA;AACA,YAAA,KAAA,EAAA,aADA;AAEA,YAAA,EAAA,EAAA,SAFA;AAGA,YAAA,GAAA,EAAA,GAHA;AAIA,YAAA,UAAA,EAAA,GAJA;AAKA,YAAA,QAAA,EAAA;AALA,WARA;AALA,SADA,EAuBA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,EAAA,EAAA,KAFA;AAGA,UAAA,GAAA,EAAA,SAHA;AAIA,UAAA,UAAA,EAAA,GAJA;AAKA,UAAA,QAAA,EAAA;AALA,SAvBA,EA8BA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,EAAA,EAAA,KAFA;AAGA,UAAA,GAAA,EAAA,SAHA;AAIA,UAAA,UAAA,EAAA,GAJA;AAKA,UAAA,QAAA,EAAA;AALA,SA9BA,EAqCA;AACA,UAAA,KAAA,EAAA,KADA;AAEA,UAAA,EAAA,EAAA,KAFA;AAGA,UAAA,GAAA,EAAA,SAHA;AAIA,UAAA,UAAA,EAAA,GAJA;AAKA,UAAA,QAAA,EAAA;AALA,SArCA;AALA,OAAA,CA91BA;AAi5BA;AACA,MAAA,OAAA,EAAA,CACA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,IAAA,EAAA,OAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,GAAA,EAAA,IALA;AAMA,QAAA,IAAA,EAAA;AANA,OADA,EAQA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,IAAA,EAAA,OAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,GAAA,EAAA,IALA;AAMA,QAAA,IAAA,EAAA;AANA,OARA,EAeA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,IAAA,EAAA,OAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,GAAA,EAAA,IALA;AAMA,QAAA,IAAA,EAAA;AANA,OAfA,EAsBA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,IAAA,EAAA,OAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,GAAA,EAAA,IALA;AAMA,QAAA,IAAA,EAAA;AANA,OAtBA,EA6BA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,IAAA,EAAA,OAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,GAAA,EAAA,IALA;AAMA,QAAA,IAAA,EAAA;AANA,OA7BA,EAoCA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,IAAA,EAAA,OAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,GAAA,EAAA,IALA;AAMA,QAAA,IAAA,EAAA;AANA,OApCA,EA2CA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,IAAA,EAAA,OAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,GAAA,EAAA,IALA;AAMA,QAAA,IAAA,EAAA;AANA,OA3CA,EAkDA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,IAAA,EAAA,OAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,GAAA,EAAA,IALA;AAMA,QAAA,IAAA,EAAA;AANA,OAlDA,EAyDA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,IAAA,EAAA,OAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,GAAA,EAAA,IALA;AAMA,QAAA,IAAA,EAAA;AANA,OAzDA,EAgEA;AACA,QAAA,IAAA,EAAA,YADA;AAEA,QAAA,IAAA,EAAA,OAFA;AAGA,QAAA,IAAA,EAAA,IAHA;AAIA,QAAA,IAAA,EAAA,MAJA;AAKA,QAAA,GAAA,EAAA,IALA;AAMA,QAAA,IAAA,EAAA;AANA,OAhEA,CAl5BA;AA49BA,MAAA,UAAA,EAAA,EA59BA;AA69BA;AACA,MAAA,WAAA,EAAA,EA99BA;AAg+BA;AACA,MAAA,cAAA,EAAA,IAj+BA;AAk+BA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,OAAA,EAAA,EAHA;AAIA,QAAA,QAAA,EAAA,EAJA;AAKA,QAAA,MAAA,EAAA,EALA;AAMA,QAAA,QAAA,EAAA;AANA,OAn+BA;AA2+BA,MAAA,UAAA,EAAA,IA3+BA;AA6+BA;AACA,MAAA,YAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,OAAA,EAAA,CAJA;AAKA,QAAA,QAAA,EAAA;AALA,OA9+BA;AAs/BA,MAAA,KAAA,EAAA;AACA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CADA,CAEA;AACA;AACA;AACA;;AALA;AASA;;;;;;;;AA//BA,KAAA;AA0gCA,GA7gCA;AA8gCA,EAAA,KAAA,EAAA,EA9gCA;AA+gCA,EAAA,OA/gCA,qBA+gCA;AACA;AACA,SAAA,QAAA;AACA,SAAA,YAAA;AAEA,GAphCA;AAqhCA,EAAA,OArhCA,qBAqhCA;AACA;AACA,SAAA,MAAA,CAAA,KAAA,GAAA,qBAAA;AACA,SAAA,mBAAA;AACA,SAAA,WAAA,mCAAA,KAAA,YAAA;AACA,SAAA,iBAAA,mCAAA,KAAA,kBAAA;AACA,SAAA,OAAA;AACA,SAAA,UAAA;AACA,SAAA,SAAA;AACA,SAAA,SAAA;AACA,SAAA,SAAA;AACA,SAAA,SAAA;AACA,SAAA,SAAA;AACA,GAliCA;AAmiCA,EAAA,OAAA,EAAA;AACA;AACA;AACA,IAAA,YAHA,wBAGA,GAHA,EAGA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,WAAA,MAAA,mCAAA,GAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,KARA;AASA;AACA,IAAA,OAVA,qBAUA;AACA,WAAA,OAAA,GAAA,MAAA;AACA,WAAA,MAAA,GAAA,EAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,KAfA;AAgBA;AACA,IAAA,WAjBA,uBAiBA,GAjBA,EAiBA;AACA,WAAA,OAAA,GAAA,MAAA;AACA,WAAA,MAAA,mCAAA,GAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,KAtBA;AAuBA;AACA,IAAA,QAxBA,sBAwBA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBACA,KAAA,CAAA,GAAA,CAAA,MAAA,GAAA,CADA;AAAA;AAAA;AAAA;;AAEA,gBAAA,KAAA,CAAA,QAAA,CAAA,OAAA,CAAA,aAAA;;AAFA;;AAAA;AAKA,gBAAA,KAAA,CAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAIA,IAJA,CAIA,YAAA;AACA;AACA,wCAAA,KAAA,CAAA,GAAA,EAAA,IAAA,CAAA,gBAAA;AAAA,wBAAA,IAAA,QAAA,IAAA;;AACA,wBAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,KAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,SADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;;AAIA,sBAAA,KAAA,CAAA,OAAA;AACA,qBANA,MAMA;AACA,sBAAA,KAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,OADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;AAIA;AACA,mBAbA;AAcA,iBApBA,EAoBA,KApBA,CAoBA,YAAA;AACA,kBAAA,KAAA,CAAA,QAAA,CAAA;AACA,oBAAA,IAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;AAIA,iBAzBA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA+BA,KAvDA;AAwDA;AACA,IAAA,QAzDA,sBAyDA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAEA,4BAAA,MAAA,CAAA,MAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,yBAEA,IAFA;;AAAA,sBAGA,IAAA,KAAA,MAHA;AAAA;AAAA;AAAA;;AAIA,gBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AAJA;AAAA,uBAKA,MAAA,CAAA,OAAA,EALA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAQA,gBAAA,OAAA,CAAA,GAAA;;AARA;AAUA,gBAAA,MAAA,CAAA,mBAAA,GAAA,KAAA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KApEA;;AAsEA;AACA;AACA,IAAA,aAxEA,yBAwEA,GAxEA,EAwEA;AACA,WAAA,QAAA,GAAA,SAAA;AACA,WAAA,OAAA,mCAAA,GAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,oBAAA,GAAA,IAAA;AACA,KA7EA;AA8EA;AACA,IAAA,QA/EA,sBA+EA;AACA,WAAA,QAAA,GAAA,OAAA;AACA,WAAA,OAAA,GAAA,EAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,oBAAA,GAAA,IAAA;AACA,KApFA;AAqFA;AACA,IAAA,YAtFA,wBAsFA,GAtFA,EAsFA;AACA,WAAA,QAAA,GAAA,OAAA;AACA,WAAA,OAAA,mCAAA,GAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,oBAAA,GAAA,IAAA;AACA,KA3FA;AA4FA;AACA,IAAA,SA7FA,uBA6FA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBACA,MAAA,CAAA,GAAA,CAAA,MAAA,GAAA,CADA;AAAA;AAAA;AAAA;;AAEA,gBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,aAAA;;AAFA;;AAAA;AAKA,gBAAA,MAAA,CAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAIA,IAJA,CAIA,YAAA;AACA,0CAAA,MAAA,CAAA,GAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,wBAAA,IAAA,SAAA,IAAA;;AACA,wBAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,SADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;;AAIA,sBAAA,MAAA,CAAA,UAAA;AACA,qBANA,MAMA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,OADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;AAIA;AACA,mBAbA;AAcA,iBAnBA,EAmBA,KAnBA,CAmBA,YAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA;AACA,oBAAA,IAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;AAIA,iBAxBA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA8BA,KA3HA;AA4HA;AACA,IAAA,SA7HA,uBA6HA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAEA,8BAAA,MAAA,CAAA,OAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,yBAEA,IAFA;;AAAA,sBAGA,IAAA,KAAA,MAHA;AAAA;AAAA;AAAA;;AAIA,gBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AAJA;AAAA,uBAKA,MAAA,CAAA,UAAA,EALA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAQA,gBAAA,OAAA,CAAA,GAAA;;AARA;AAUA,gBAAA,MAAA,CAAA,oBAAA,GAAA,KAAA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KAxIA;AA0IA;AACA,IAAA,SA3IA,uBA2IA;AACA,WAAA,SAAA,GAAA,MAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,qBAAA,GAAA,IAAA;AACA,KA/IA;;AAgJA;AACA;AACA,IAAA,YAlJA,wBAkJA,GAlJA,EAkJA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,WAAA,MAAA,mCAAA,GAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,KAvJA;AAwJA;AACA,IAAA,OAzJA,qBAyJA;AACA,WAAA,OAAA,GAAA,MAAA;AACA,WAAA,MAAA,GAAA,EAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,KA9JA;AA+JA;AACA,IAAA,WAhKA,uBAgKA,GAhKA,EAgKA;AACA,WAAA,OAAA,GAAA,MAAA;AACA,WAAA,MAAA,mCAAA,GAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,KArKA;AAsKA;AACA,IAAA,QAvKA,sBAuKA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBACA,MAAA,CAAA,GAAA,CAAA,MAAA,GAAA,CADA;AAAA;AAAA;AAAA;;AAEA,gBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,aAAA;;AAFA;;AAAA;AAKA,gBAAA,MAAA,CAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAIA,IAJA,CAIA,YAAA;AACA,wCAAA,MAAA,CAAA,GAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,wBAAA,IAAA,SAAA,IAAA;;AACA,wBAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,SADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;;AAIA,sBAAA,MAAA,CAAA,SAAA;AACA,qBANA,MAMA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,OADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;AAIA;AACA,mBAbA;AAcA,iBAnBA,EAmBA,KAnBA,CAmBA,YAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA;AACA,oBAAA,IAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;AAIA,iBAxBA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA8BA,KArMA;AAsMA;AACA,IAAA,QAvMA,sBAuMA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAEA,4BAAA,MAAA,CAAA,MAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,yBAEA,IAFA;;AAAA,sBAGA,IAAA,KAAA,MAHA;AAAA;AAAA;AAAA;;AAIA,gBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AAJA;AAAA,uBAKA,MAAA,CAAA,SAAA,EALA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAQA,gBAAA,OAAA,CAAA,GAAA;;AARA;AAUA,gBAAA,MAAA,CAAA,mBAAA,GAAA,KAAA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KAlNA;;AAmNA;AACA;AACA,IAAA,YArNA,wBAqNA,GArNA,EAqNA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,WAAA,MAAA,mCAAA,GAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,KA1NA;AA2NA;AACA,IAAA,OA5NA,qBA4NA;AACA,WAAA,OAAA,GAAA,MAAA;AACA,WAAA,MAAA,GAAA,EAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,KAjOA;AAkOA;AACA,IAAA,WAnOA,uBAmOA,GAnOA,EAmOA;AACA,WAAA,OAAA,GAAA,MAAA;AACA,WAAA,MAAA,mCAAA,GAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,KAxOA;AAyOA;AACA,IAAA,QA1OA,sBA0OA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBACA,MAAA,CAAA,GAAA,CAAA,MAAA,GAAA,CADA;AAAA;AAAA;AAAA;;AAEA,gBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,aAAA;;AAFA;;AAAA;AAKA,gBAAA,MAAA,CAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAIA,IAJA,CAIA,YAAA;AACA,wCAAA,MAAA,CAAA,GAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,wBAAA,IAAA,SAAA,IAAA;;AACA,wBAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,SADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;;AAIA,sBAAA,MAAA,CAAA,SAAA;AACA,qBANA,MAMA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,OADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;AAIA;AACA,mBAbA;AAcA,iBAnBA,EAmBA,KAnBA,CAmBA,YAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA;AACA,oBAAA,IAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;AAIA,iBAxBA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA8BA,KAxQA;AAyQA;AACA,IAAA,QA1QA,sBA0QA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAEA,4BAAA,MAAA,CAAA,MAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,yBAEA,IAFA;;AAAA,sBAGA,IAAA,KAAA,MAHA;AAAA;AAAA;AAAA;;AAIA,gBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AAJA;AAAA,uBAKA,MAAA,CAAA,SAAA,EALA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAQA,gBAAA,OAAA,CAAA,GAAA;;AARA;AAUA,gBAAA,MAAA,CAAA,mBAAA,GAAA,KAAA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KArRA;;AAuRA;AACA;AACA,IAAA,YAzRA,wBAyRA,GAzRA,EAyRA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,WAAA,MAAA,mCAAA,GAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,KA9RA;AA+RA;AACA,IAAA,OAhSA,qBAgSA;AACA,WAAA,OAAA,GAAA,MAAA;AACA,WAAA,MAAA,GAAA,EAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,KArSA;AAsSA;AACA,IAAA,WAvSA,uBAuSA,GAvSA,EAuSA;AACA,WAAA,OAAA,GAAA,MAAA;AACA,WAAA,MAAA,mCAAA,GAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,KA5SA;AA6SA;AACA,IAAA,QA9SA,sBA8SA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBACA,MAAA,CAAA,GAAA,CAAA,MAAA,GAAA,CADA;AAAA;AAAA;AAAA;;AAEA,gBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,aAAA;;AAFA;;AAAA;AAKA,gBAAA,MAAA,CAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAIA,IAJA,CAIA,YAAA;AACA,wCAAA,MAAA,CAAA,GAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,wBAAA,IAAA,SAAA,IAAA;;AACA,wBAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,SADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;;AAIA,sBAAA,MAAA,CAAA,SAAA;AACA,qBANA,MAMA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,OADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;AAIA;AACA,mBAbA;AAcA,iBAnBA,EAmBA,KAnBA,CAmBA,YAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA;AACA,oBAAA,IAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;AAIA,iBAxBA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA8BA,KA5UA;AA6UA;AACA,IAAA,QA9UA,sBA8UA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAEA,4BAAA,OAAA,CAAA,MAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,yBAEA,IAFA;;AAAA,sBAGA,IAAA,KAAA,MAHA;AAAA;AAAA;AAAA;;AAIA,gBAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AAJA;AAAA,uBAKA,OAAA,CAAA,SAAA,EALA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAQA,gBAAA,OAAA,CAAA,GAAA;;AARA;AAUA,gBAAA,OAAA,CAAA,mBAAA,GAAA,KAAA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KAzVA;;AA2VA;AACA;AACA,IAAA,YA7VA,wBA6VA,GA7VA,EA6VA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,WAAA,MAAA,mCAAA,GAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,KAlWA;AAmWA;AACA,IAAA,OApWA,qBAoWA;AACA,WAAA,OAAA,GAAA,MAAA;AACA,WAAA,MAAA,GAAA,EAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,KAzWA;AA0WA;AACA,IAAA,WA3WA,uBA2WA,GA3WA,EA2WA;AACA,WAAA,OAAA,GAAA,MAAA;AACA,WAAA,MAAA,mCAAA,GAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,KAhXA;AAiXA;AACA,IAAA,QAlXA,sBAkXA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBACA,OAAA,CAAA,GAAA,CAAA,MAAA,GAAA,CADA;AAAA;AAAA;AAAA;;AAEA,gBAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,aAAA;;AAFA;;AAAA;AAKA,gBAAA,OAAA,CAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAIA,IAJA,CAIA,YAAA;AACA,wCAAA,OAAA,CAAA,GAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,wBAAA,IAAA,SAAA,IAAA;;AACA,wBAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,OAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,SADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;;AAIA,sBAAA,OAAA,CAAA,SAAA;AACA,qBANA,MAMA;AACA,sBAAA,OAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,OADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;AAIA;AACA,mBAbA;AAcA,iBAnBA,EAmBA,KAnBA,CAmBA,YAAA;AACA,kBAAA,OAAA,CAAA,QAAA,CAAA;AACA,oBAAA,IAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;AAIA,iBAxBA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA8BA,KAhZA;AAiZA;AACA,IAAA,QAlZA,sBAkZA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAEA,4BAAA,OAAA,CAAA,MAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,yBAEA,IAFA;;AAAA,sBAGA,IAAA,KAAA,MAHA;AAAA;AAAA;AAAA;;AAIA,gBAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AAJA;AAAA,uBAKA,OAAA,CAAA,SAAA,EALA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAQA,gBAAA,OAAA,CAAA,GAAA;;AARA;AAUA,gBAAA,OAAA,CAAA,mBAAA,GAAA,KAAA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KA7ZA;;AA+ZA;AACA;AACA,IAAA,SAjaA,uBAiaA;AACA,WAAA,SAAA,GAAA,QAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,qBAAA,GAAA,IAAA;AACA,KAraA;;AAuaA;AACA;AACA,IAAA,YAzaA,wBAyaA,GAzaA,EAyaA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,WAAA,MAAA,mCAAA,GAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,KA9aA;AA+aA;AACA,IAAA,OAhbA,qBAgbA;AACA,WAAA,OAAA,GAAA,MAAA;AACA,WAAA,MAAA,GAAA,EAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,KArbA;AAsbA;AACA,IAAA,WAvbA,uBAubA,GAvbA,EAubA;AACA,WAAA,OAAA,GAAA,MAAA;AACA,WAAA,MAAA,mCAAA,GAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,KA5bA;AA6bA;AACA,IAAA,QA9bA,sBA8bA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBACA,OAAA,CAAA,GAAA,CAAA,MAAA,GAAA,CADA;AAAA;AAAA;AAAA;;AAEA,gBAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,aAAA;;AAFA;;AAAA;AAKA,gBAAA,OAAA,CAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAIA,IAJA,CAIA,YAAA;AACA,wCAAA,OAAA,CAAA,GAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,wBAAA,IAAA,SAAA,IAAA;;AACA,wBAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,OAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,SADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;;AAIA,sBAAA,OAAA,CAAA,SAAA;AACA,qBANA,MAMA;AACA,sBAAA,OAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,OADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;AAIA;AACA,mBAbA;AAcA,iBAnBA,EAmBA,KAnBA,CAmBA,YAAA;AACA,kBAAA,OAAA,CAAA,QAAA,CAAA;AACA,oBAAA,IAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;AAIA,iBAxBA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA8BA,KA5dA;AA6dA;AACA,IAAA,QA9dA,sBA8dA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAEA,4BAAA,OAAA,CAAA,MAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,yBAEA,IAFA;;AAAA,sBAGA,IAAA,KAAA,MAHA;AAAA;AAAA;AAAA;;AAIA,gBAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AAJA;AAAA,uBAKA,OAAA,CAAA,SAAA,EALA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAQA,gBAAA,OAAA,CAAA,GAAA;;AARA;AAUA,gBAAA,OAAA,CAAA,mBAAA,GAAA,KAAA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KAzeA;;AA2eA;AACA;AACA,IAAA,OA7eA,mBA6eA,MA7eA,EA6eA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,KAFA,+DAEA,OAAA,CAAA,OAFA,GAEA,MAFA;AAAA;AAAA,uBAGA,uBAAA,KAAA,CAHA;;AAAA;AAAA;AAGA,gBAAA,IAHA,oBAGA,IAHA;AAGA,gBAAA,IAHA,oBAGA,IAHA;;AAIA;AACA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,kBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,OAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AARA;AAAA;;AAAA;AAAA;AAAA;AAUA,gBAAA,OAAA,CAAA,GAAA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,KAzfA;AA0fA;AACA,IAAA,UA3fA,sBA2fA,MA3fA,EA2fA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,KAFA,+DAEA,OAAA,CAAA,QAFA,GAEA,MAFA;AAAA;AAAA,uBAGA,yBAAA,KAAA,CAHA;;AAAA;AAAA;AAGA,gBAAA,IAHA,qBAGA,IAHA;AAGA,gBAAA,IAHA,qBAGA,IAHA;;AAIA;AACA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,mBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,OAAA,CAAA,mBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AARA;AAAA;;AAAA;AAAA;AAAA;AAUA,gBAAA,OAAA,CAAA,GAAA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,KAvgBA;AAwgBA;AACA,IAAA,SAzgBA,qBAygBA,MAzgBA,EAygBA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,KAFA,+DAEA,OAAA,CAAA,OAFA,GAEA,MAFA;AAAA;AAAA,uBAGA,uBAAA,KAAA,CAHA;;AAAA;AAAA;AAGA,gBAAA,IAHA,oBAGA,IAHA;AAGA,gBAAA,IAHA,oBAGA,IAHA;;AAIA;AACA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,kBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,OAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AARA;AAAA;;AAAA;AAAA;AAAA;AAUA,gBAAA,OAAA,CAAA,GAAA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,KArhBA;AAshBA;AACA,IAAA,SAvhBA,qBAuhBA,MAvhBA,EAuhBA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,KAFA,+DAEA,OAAA,CAAA,OAFA,GAEA,MAFA;AAAA;AAAA,uBAGA,uBAAA,KAAA,CAHA;;AAAA;AAAA;AAGA,gBAAA,IAHA,oBAGA,IAHA;AAGA,gBAAA,IAHA,oBAGA,IAHA;;AAIA;AACA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,kBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,OAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AARA;AAAA;;AAAA;AAAA;AAAA;AAUA,gBAAA,OAAA,CAAA,GAAA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,KAniBA;AAoiBA;AACA,IAAA,SAriBA,qBAqiBA,MAriBA,EAqiBA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,KAFA,+DAEA,OAAA,CAAA,OAFA,GAEA,MAFA;AAAA;AAAA,uBAGA,uBAAA,KAAA,CAHA;;AAAA;AAAA;AAGA,gBAAA,IAHA,oBAGA,IAHA;AAGA,gBAAA,IAHA,oBAGA,IAHA;;AAIA;AACA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,kBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,OAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AARA;AAAA;;AAAA;AAAA;AAAA;AAUA,gBAAA,OAAA,CAAA,GAAA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,KAjjBA;AAkjBA;AACA,IAAA,SAnjBA,qBAmjBA,MAnjBA,EAmjBA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,KAFA,+DAEA,OAAA,CAAA,OAFA,GAEA,MAFA;AAAA;AAAA,uBAGA,uBAAA,KAAA,CAHA;;AAAA;AAAA;AAGA,gBAAA,IAHA,oBAGA,IAHA;AAGA,gBAAA,IAHA,oBAGA,IAHA;;AAIA;AACA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,kBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,OAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AARA;AAAA;;AAAA;AAAA;AAAA;AAUA,gBAAA,OAAA,CAAA,GAAA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,KA/jBA;AAikBA;AACA,IAAA,SAlkBA,qBAkkBA,MAlkBA,EAkkBA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,KAFA,+DAEA,OAAA,CAAA,OAFA,GAEA,MAFA;AAAA;AAAA,uBAGA,uBAAA,KAAA,CAHA;;AAAA;AAAA;AAGA,gBAAA,IAHA,oBAGA,IAHA;AAGA,gBAAA,IAHA,oBAGA,IAHA;;AAIA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,kBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,OAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AAPA;AAAA;;AAAA;AAAA;AAAA;AASA,gBAAA,OAAA,CAAA,GAAA;;AATA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KA7kBA;AA+kBA,IAAA,mBA/kBA,iCA+kBA;AACA,WAAA,UAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,KAAA,IAAA,MAAA,EAAA;AACA,UAAA,IAAA,CAAA,OAAA,GAAA,CACA;AAAA,YAAA,KAAA,EAAA,OAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WADA,EAEA;AAAA,YAAA,KAAA,EAAA,MAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAFA,EAGA;AAAA,YAAA,KAAA,EAAA,MAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAHA,EAIA;AAAA,YAAA,KAAA,EAAA,KAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAJA,CAAA;AAMA;AACA,OATA;AAUA,KA1lBA;AA4lBA,IAAA,SAAA,EAAA,qBAAA;AAAA;;AACA,WAAA,KAAA,CAAA,UAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,oCAAA,OAAA,CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EACA;AACA,gBAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,cAAA,OAAA,CAAA,GAAA,CAAA,GAAA,CAAA,IAAA;AACA,cAAA,OAAA,CAAA,UAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;;AACA,cAAA,OAAA,CAAA,YAAA;;AACA,cAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,cAAA,OAAA,CAAA,mBAAA,GAAA,KAAA;;AACA,cAAA,OAAA,CAAA,YAAA;AACA,aAPA,MAOA;AACA,cAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,OAAA;;AACA,qBAAA,KAAA;AACA;AAEA,WAdA;AAeA,SAhBA,MAgBA;AACA,UAAA,UAAA,CAAA,YAAA;AACA,gBAAA,OAAA,GAAA,QAAA,CAAA,sBAAA,CAAA,UAAA,CAAA;;AACA,gBAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,OAAA,CAAA,EAAA;AACA,cAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,OAAA,EAAA,KAAA;AACA,aAFA,MAEA,IAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,UAAA,CAAA,EAAA;AACA,cAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,UAAA,EAAA,KAAA;AACA;AACA,WAPA,EAOA,CAPA,CAAA;AAQA,iBAAA,KAAA;AACA;AACA,OA5BA;AA6BA,KA1nBA;AA4nBA,IAAA,YAAA,EAAA,sBAAA,MAAA,EAAA;AAAA;;AACA,WAAA,YAAA,+DAAA,KAAA,YAAA,GAAA,MAAA;AACA,UAAA,KAAA,+DAAA,KAAA,YAAA,GAAA,MAAA,CAAA;AACA,2BAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,OAHA;AAIA,KAnoBA;;AAqoBA;;;AAGA,IAAA,qBAxoBA,iCAwoBA,SAxoBA,EAwoBA;AACA,WAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,KAAA;AAAA,OAAA,CAAA;AACA,WAAA,MAAA,GAAA,SAAA,CAAA,MAAA,KAAA,CAAA;AACA,WAAA,QAAA,GAAA,CAAA,SAAA,CAAA,MAAA;AACA,KA5oBA;;AA6oBA;;;AAGA,IAAA,UAhpBA,wBAgpBA;AAAA;;AACA,UAAA,KAAA,GAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,UAAA,iBAAA,EAAA,IADA;AAEA,UAAA,gBAAA,EAAA,IAFA;AAGA,UAAA,IAAA,EAAA;AAHA,SAAA,EAIA,IAJA,CAIA,YAAA;AACA,8BAAA,OAAA,CAAA,GAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,OAAA,CAAA,YAAA;AACA,WANA;AAOA,SAZA;AAaA,OAdA,MAcA;AACA,aAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA;AACA,KArqBA;AAsqBA,IAAA,YAtqBA,wBAsqBA,GAtqBA,EAsqBA;AACA,WAAA,UAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA;AACA,WAAA,UAAA,CAAA,KAAA,GAAA,GAAA,CAAA,KAAA,EACA,KAAA,aAAA,GAAA,IADA;AAGA,KA3qBA;AA4qBA,IAAA,YA5qBA,0BA4qBA;AAAA;;AACA,WAAA,QAAA,CAAA,eAAA,KAAA,UAAA,CAAA,IAAA,GAAA,GAAA,EAAA,EAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,UAAA,GAAA,EAAA;AACA,gCAAA,OAAA,CAAA,UAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA;;AACA,YAAA,OAAA,CAAA,aAAA,GAAA,KAAA;;AACA,YAAA,OAAA,CAAA,YAAA;AACA;AACA,SANA;AAOA,OAZA;AAaA,KA1rBA;AA2rBA,IAAA,WA3rBA,yBA2rBA;AACA,UAAA,KAAA,KAAA,CAAA,MAAA,EAAA;AACA,aAAA,KAAA,CAAA,MAAA,CAAA,UAAA;AACA;AACA,KA/rBA;AAgsBA,IAAA,SAAA;AAAA,6FAAA,mBAAA,GAAA;AAAA;AAAA;AAAA;AAAA;AACA,qBAAA,WAAA;AACA,qBAAA,WAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA;AACA,qBAAA,WAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA;AACA,qBAAA,YAAA;AACA,qBAAA,QAAA,mCAAA,GAAA;AACA,qBAAA,QAAA,CAAA,UAAA,GAAA,EAAA;AANA;AAAA,uBAOA,KAAA,WAAA,EAPA;;AAAA;AAQA,qBAAA,mBAAA,GAAA,IAAA;AACA,qBAAA,IAAA,GAAA,KAAA;;AATA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA,OAhsBA;AA2sBA,IAAA,WAAA;AAAA,+FAAA,mBAAA,GAAA;AAAA;AAAA;AAAA;AAAA;AACA,qBAAA,WAAA;AACA,qBAAA,WAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA;AACA,qBAAA,WAAA,CAAA,IAAA,GAAA,GAAA,CAAA,IAAA;AACA,qBAAA,YAAA;AACA,qBAAA,QAAA,mCAAA,GAAA;AACA,qBAAA,QAAA,CAAA,UAAA,GAAA,EAAA;AANA;AAAA,uBAOA,KAAA,WAAA,EAPA;;AAAA;AAQA,qBAAA,mBAAA,GAAA,IAAA;AACA,qBAAA,IAAA,GAAA,IAAA;;AATA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA,OA3sBA;AAstBA,IAAA,WAttBA,yBAstBA,CAEA,CAxtBA;AAytBA,IAAA,oBAztBA,kCAytBA;AACA,WAAA,YAAA;AACA,KA3tBA;AA4tBA,IAAA,QA5tBA,sBA4tBA;AAAA;;AACA,+BAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KAhuBA;AAkuBA,IAAA,WAluBA,yBAkuBA;AACA,WAAA,QAAA,GAAA;AACA,QAAA,UAAA,EAAA;AADA,OAAA;AAGA,WAAA,SAAA,CAAA,YAAA;AACA,aAAA,KAAA,CAAA,UAAA,EAAA,aAAA;AACA,OAFA;AAGA,WAAA,mBAAA,GAAA,KAAA;AACA,KA1uBA;AA2uBA,IAAA,YA3uBA,wBA2uBA,GA3uBA,EA2uBA;AAAA;;AACA,UAAA,MAAA,+DAAA,GAAA,GAAA,KAAA,WAAA,CAAA;AACA,kCAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,aAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,OAAA,CAAA,aAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,OAHA;AAIA,KAjvBA;;AAmvBA;;;;AAKA;AACA,IAAA,iBAzvBA,+BAyvBA,CAEA,CA3vBA;AA4vBA;AACA,IAAA,kBA7vBA,gCA6vBA;AACA,UAAA,KAAA,QAAA,CAAA,MAAA,IAAA,SAAA,EAAA;AACA,aAAA,OAAA,GAAA,EAAA;AACA,aAAA,IAAA,GAAA,KAAA;AACA,aAAA,mBAAA,GAAA,IAAA;AACA,OAJA,MAIA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,eAAA;AACA;AACA,KArwBA;AAswBA;AACA,IAAA,aAvwBA,2BAuwBA;AACA,WAAA,mBAAA,GAAA,IAAA;AACA,KAzwBA;AA0wBA,IAAA,YA1wBA,wBA0wBA,IA1wBA,EA0wBA;AACA,WAAA,GAAA,GAAA,IAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,KAAA;AAAA,OAAA,CAAA;AACA,WAAA,MAAA,GAAA,IAAA,CAAA,MAAA,KAAA,CAAA;AACA,WAAA,QAAA,GAAA,CAAA,IAAA,CAAA,MAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KA/wBA;AAgxBA;AACA,IAAA,gBAjxBA,8BAixBA,CAEA,CAnxBA;AAoxBA;AACA,IAAA,mBArxBA,iCAqxBA,CAEA,CAvxBA;AAwxBA;AACA,IAAA,eAzxBA,2BAyxBA,IAzxBA,EAyxBA;AACA,UAAA,IAAA,CAAA,UAAA,IAAA,GAAA,EAAA;AACA,aAAA,QAAA,CAAA,QAAA,GAAA,IAAA,CAAA,KAAA;AACA,aAAA,QAAA,CAAA,MAAA,GAAA,IAAA,CAAA,EAAA,CAFA,CAGA;;AACA,YAAA,MAAA;AACA,aAAA,UAAA,CAAA,IAAA,CAAA,QAAA,GAAA,IAAA,CAAA,KAAA;AACA,aAAA,YAAA,CAAA,KAAA,UAAA,CAAA,IAAA;AACA;AACA,KAlyBA;AAmyBA,IAAA,YAnyBA,wBAmyBA,IAnyBA,EAmyBA;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,IAAA,GAAA,OAAA,IAAA,GAAA,EAAA;;AACA,UAAA,CAAA,QAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,kBAAA;AACA;AACA,KAxyBA;AAyyBA,IAAA,YAzyBA,0BAyyBA;AACA,WAAA,KAAA,CAAA,MAAA,CAAA,MAAA;AACA,KA3yBA;AA4yBA,IAAA,WA5yBA,yBA4yBA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,+BAAA;AAAA,kBAAA,UAAA,EAAA,OAAA,CAAA,QAAA,CAAA;AAAA,iBAAA,CADA;;AAAA;AAAA;AACA,gBAAA,IADA,yBACA,IADA;AACA,gBAAA,IADA,yBACA,IADA;;AAEA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,QAAA,CAAA,UAAA,GAAA,IAAA;AACA,kBAAA,OAAA,CAAA,OAAA,GAAA,IAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,wBAAA,KAAA,GAAA,EAAA;AACA,oBAAA,KAAA,CAAA,IAAA,GAAA,IAAA,CAAA,QAAA;AACA,oBAAA,KAAA,CAAA,GAAA,GAAA,IAAA,CAAA,OAAA;AACA,2BAAA,KAAA;AACA,mBALA,CAAA;AAMA;;AAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,KAvzBA;AAwzBA,IAAA,cAxzBA,0BAwzBA,EAxzBA,EAwzBA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,sBAAA,EAAA,CADA;;AAAA;AAAA;AACA,gBAAA,IADA,qBACA,IADA;;AAAA,sBAEA,IAAA,KAAA,MAFA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAGA,OAAA,CAAA,WAAA,EAHA;;AAAA;AAIA,gBAAA,OAAA,CAAA,QAAA,CAAA;AACA,kBAAA,IAAA,EAAA,SADA;AAEA,kBAAA,OAAA,EAAA;AAFA,iBAAA;;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAj0BA;AAniCA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"16\">\n      <el-col :span=\"4\">\n        <el-card>\n          <!--<div class=\"clearfix\" slot=\"header\">\n            <span></span>\n          </div>-->\n          <div style=\"overflow: auto;height: 80vh\">\n            <el-tree :expand-on-click-node=\"true\"\n                     highlight-current\n                     id=\"tree\"\n                     :data=\"treeOptions\"\n                     :default-expanded-keys=\"['1']\"\n                     @node-click=\"handleNodeClick\"\n                     node-key=\"nodeId\"\n                     accordion\n            />\n          </div>\n        </el-card>\n      </el-col>\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :btnHidden=\"false\"\n          @getMethod=\"getTableList\"\n          @handleReset=\"filterReset\"\n        />\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button type=\"primary\" v-hasPermi=\"['gttz:button:add']\" icon=\"el-icon-plus\" @click=\"bdzAddSensorButton\">新增</el-button>\n            <el-button type=\"danger\" v-hasPermi=\"['gttz:button:delete']\" icon=\"el-icon-delete\" @click=\"deleteInfo\">删除</el-button>\n          </div>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"500\"\n            @getMethod=\"getTableList\"\n          >\n            <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\" width=\"160\"\n                             :resizable=\"false\">\n              <template slot-scope=\"scope\">\n                <el-button @click=\"updatebdz(scope.row)\" v-hasPermi=\"['gttz:button:update']\" type=\"text\" size=\"small\" title=\"修改\"  class='el-icon-edit'></el-button>\n                <el-button @click=\"bdzDetails(scope.row)\" type=\"text\" size=\"small\" title=\"详情\" class=\"el-icon-view\"></el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!-- 杆塔弹出框开始展示设备履历 -->\n    <el-dialog title=\"杆塔详情\" :visible.sync=\"gtDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"60%\"\n               :before-close=\"handleClose\">\n      <el-tabs v-model=\"activeTabName\">\n        <!--基本信息-->\n        <el-tab-pane label=\"基本信息\" name=\"sbDesc\">\n          <div class=\"block\" style=\"width: 50%;height: 50%;margin-bottom: 2%;float: right\">\n            <span class=\"demonstration\">设备图片</span>\n            <el-carousel trigger=\"click\" height=\"150px\" indicator-position=\"none\" :interval=\"2000\" type=\"card\">\n              <el-carousel-item v-for=\"(img,index) in imgList\" :key=\"index\">\n                <viewer :images=\"imgList\">\n                  <img :src=\"img.url\" width=\"100%\" height=\"100%\"/>\n                </viewer>\n              </el-carousel-item>\n            </el-carousel>\n          </div>\n          <el-form :model=\"jbxxForm\" ref=\"jbxxForm\" :disabled=\"show\" :rules=\"rules\" label-width=\"130px\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔编号\" prop=\"gtbh\">\n                  <el-input v-model=\"jbxxForm.gtbh\" placeholder=\"请输入杆塔编号\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔名称\" prop=\"gtmc\">\n                  <el-input v-model=\"jbxxForm.gtmc\" placeholder=\"请输入杆塔名称\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"所属线路\" prop=\"lineName\">\n                  <el-input v-model=\"jbxxForm.lineName\" placeholder=\"请输入所属线路\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔材质\" prop=\"gtcz\">\n                  <el-select v-model=\"jbxxForm.gtcz\" placeholder=\"请选择杆塔材质\" clearable>\n                    <el-option label=\"木杆\" value=\"木杆\"></el-option>\n                    <el-option label=\"水泥杆\" value=\"水泥杆\"></el-option>\n                    <el-option label=\"轻型铁塔\" value=\"轻型铁塔\"></el-option>\n                    <el-option label=\"钢管塔\" value=\"钢管塔\"></el-option>\n                    <el-option label=\"铁塔\" value=\"铁塔\"></el-option>\n                    <el-option label=\"门架\" value=\"门架\"></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"运行班组\" prop=\"yxbz\">\n                  <el-select v-model=\"jbxxForm.yxbz\" placeholder=\"请选择运行班组\" clearable>\n                    <el-option label=\"线路一班\" value=\"线路一班\"></el-option>\n                    <el-option label=\"线路二班\" value=\"线路二班\"></el-option>\n                    <el-option label=\"线路三班\" value=\"线路三班\"></el-option>\n                    <el-option label=\"线路四班\" value=\"线路四班\"></el-option>\n                    <el-option label=\"线路五班\" value=\"线路五班\"></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"所属部门\" prop=\"ssbm\">\n                  <el-input v-model=\"jbxxForm.ssbm\" placeholder=\"请输入所属部门\" disabled></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"电压等级\" prop=\"dydj\">\n                  <el-select v-model=\"jbxxForm.dydj\" placeholder=\"请选择电压等级\" clearable>\n                    <el-option\n                      v-for=\"item in options\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\">\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔型式\" prop=\"gtxs\">\n                  <el-select v-model=\"jbxxForm.gtxs\" placeholder=\"请选择杆塔型式\" clearable>\n                    <el-option label=\"三杆\" value=\"三杆\"></el-option>\n                    <el-option label=\"其它\" value=\"其它\"></el-option>\n                    <el-option label=\"单杆\" value=\"单杆\"></el-option>\n                    <el-option label=\"双杆\" value=\"双杆\"></el-option>\n                    <el-option label=\"拉线塔\" value=\"拉线塔\"></el-option>\n                    <el-option label=\"自立塔\" value=\"自立塔\"></el-option>\n                    <el-option label=\"钢管塔\" value=\"钢管塔\"></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔性质\" prop=\"gtNature\">\n                  <el-select v-model=\"jbxxForm.gtNature\" placeholder=\"请选择杆塔性质\" clearable>\n                    <el-option label=\"分支\" value=\"分支\"></el-option>\n                    <el-option label=\"换位\" value=\"换位\"></el-option>\n                    <el-option label=\"直线\" value=\"直线\"></el-option>\n                    <el-option label=\"终端\" value=\"终端\"></el-option>\n                    <el-option label=\"耐张\" value=\"耐张\"></el-option>\n                    <el-option label=\"转角\" value=\"转角\"></el-option>\n                    <el-option label=\"门架\" value=\"门架\"></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔形状\" prop=\"gtxz\">\n                    <el-select v-model=\"jbxxForm.gtxz\" placeholder=\"请选择杆塔形状\" clearable>\n                      <el-option\n                        v-for=\"item in gtxzOptions\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\">\n                      </el-option>\n                    </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"相序\" prop=\"xx\">\n                  <el-select v-model=\"jbxxForm.xx\" placeholder=\"请选择相序\" clearable>\n                    <el-option label=\"ABC\" value=\"ABC\"></el-option>\n                    <el-option label=\"ACB\" value=\"ACB\"></el-option>\n                    <el-option label=\"BAC\" value=\"BAC\"></el-option>\n                    <el-option label=\"BCA\" value=\"BCA\"></el-option>\n                    <el-option label=\"CAB\" value=\"CAB\"></el-option>\n                    <el-option label=\"CBA\" value=\"CBA\"></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔呼称高(m)\" prop=\"gthcg\">\n                  <el-input v-model=\"jbxxForm.gthcg\" type=\"number\" placeholder=\"请输入杆塔呼称高\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔高度(m)\" prop=\"towerHeight\">\n                  <el-input v-model=\"jbxxForm.towerHeight\" type=\"number\" placeholder=\"请输入杆塔高度\"></el-input>\n                </el-form-item>\n              </el-col>\n\n              <el-col :span=\"8\">\n                <el-form-item label=\"至上基塔档距(m)\" prop=\"zsjtdj\">\n                  <el-input v-model=\"jbxxForm.zsjtdj\" type=\"number\" placeholder=\"请输入至上基塔档距\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"导线排列方式\" prop=\"dxplfs\">\n                  <el-select v-model=\"jbxxForm.dxplfs\" placeholder=\"请选择导线排列方式\" clearable>\n                    <el-option label=\"三角\" value=\"三角\"></el-option>\n                    <el-option label=\"垂直\" value=\"垂直\"></el-option>\n                    <el-option label=\"水平\" value=\"水平\"></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"是否同杆并架\" prop=\"sftgbj\">\n                  <el-select v-model=\"jbxxForm.sftgbj\" placeholder=\"请选择是否同杆并架\" clearable>\n                    <el-option label=\"是\" value=\"是\"></el-option>\n                    <el-option label=\"否\" value=\"否\"></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"杆塔规格型号\" prop=\"gtggxh\">\n                  <el-input v-model=\"jbxxForm.gtggxh\"  placeholder=\"请输入杆塔规格型号\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"坐标经度\" prop=\"jd\">\n                  <el-input v-model=\"jbxxForm.jd\" placeholder=\"请输入坐标经度\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"坐标纬度\" prop=\"wd\">\n                  <el-input v-model=\"jbxxForm.wd\" placeholder=\"请输入坐标纬度\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"同杆架设回路数\" prop=\"tgjshls\">\n                  <el-input v-model=\"jbxxForm.tgjshls\" placeholder=\"请输入同杆架设回路数\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"同杆线路位置\" prop=\"tgxlwz\">\n                  <el-input v-model=\"jbxxForm.tgxlwz\" placeholder=\"请输入同杆线路位置\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"是否换相\" prop=\"sfhx\">\n                  <el-select v-model=\"jbxxForm.sfhx\" placeholder=\"请选择是否换相\" clearable>\n                    <el-option label=\"是\" value=\"是\"></el-option>\n                    <el-option label=\"否\" value=\"否\"></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"投运日期\" class=\"add_sy_tyrq\" prop=\"tyrq\">\n                  <el-date-picker\n                    v-model=\"jbxxForm.tyrq\"\n                    type=\"date\"\n                    placeholder=\"选择日期\"\n                    format=\"yyyy-MM-dd\"\n                    value-format=\"yyyy-MM-dd\">\n                  </el-date-picker>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"接地体材料\" prop=\"jdtcl\">\n                  <el-select v-model=\"jbxxForm.jdtcl\" placeholder=\"请选择接地体材料\" clearable>\n                    <el-option label=\"石墨\" value=\"石墨\"></el-option>\n                    <el-option label=\"钢材\" value=\"钢材\"></el-option>\n                    <el-option label=\"其它\" value=\"其它\"></el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"实际阻值\" prop=\"sjzz\">\n                  <el-input v-model=\"jbxxForm.sjzz\" placeholder=\"请输入实际阻值\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"导线型号\" prop=\"dxxh\">\n                  <el-input v-model=\"jbxxForm.dxxh\" placeholder=\"请输入导线型号\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"地线型号\" prop=\"jdxh\">\n                  <el-input v-model=\"jbxxForm.jdxh\" placeholder=\"请输入地线型号\"></el-input>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"8\">\n                <el-form-item label=\"设备状态\" prop=\"sbzk\">\n                  <el-select v-model=\"jbxxForm.sbzt\" placeholder=\"请选择设备状况\">\n                    <el-option\n                      v-for=\"item in sbzt\"\n                      :key=\"item.value\"\n                      :label=\"item.label\"\n                      :value=\"item.value\">\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n<!--              <el-col :span=\"8\">-->\n<!--                <el-form-item label=\"负责人\" prop=\"fzr\">-->\n<!--                  <el-select v-model=\"jbxxForm.fzr\" placeholder=\"请选择负责人\"></el-select>-->\n<!--                </el-form-item>-->\n<!--              </el-col>-->\n            </el-row>\n            <el-row>\n              <el-form-item label=\"备注\" prop=\"bz\">\n                <el-input v-model=\"jbxxForm.bz\" type=\"textarea\" :row=\"2\" placeholder=\"备注\"></el-input>\n              </el-form-item>\n            </el-row>\n\n            <el-row :gutter=\"20\">\n              <el-form-item label=\"已上传图片：\" prop=\"attachment\" v-if=\"jbxxForm.attachment.length>0\" id=\"pic_form\">\n                <el-col :span=\"24\" v-for=\"(item,index) in jbxxForm.attachment\" style=\"margin-left: 0\">\n                  <el-form-item :label=\"(index+1).toString()\">\n                    {{item.fileOldName}}\n                    <el-button v-if=\"!show\" type=\"error\" size=\"mini\" @click=\"deleteFileById(item.fileId)\">删除</el-button>\n                  </el-form-item>\n                </el-col>\n              </el-form-item>\n            </el-row>\n            <el-row :gutter=\"20\">\n              <el-form-item label=\"上传图片：\" v-if=\"!show\">\n                <el-upload list-type=\"picture-card\"\n                           class=\"upload-demo\"\n                           accept=\".jpg,.png\"\n                           ref=\"upload\"\n                           :headers=\"header\"\n                           action=\"/isc-api/file/upload\"\n                           :before-upload=\"beforeUpload\"\n                           :data=\"uploadData\"\n                           single\n                           :auto-upload=\"false\"\n                           multiple>\n                  <el-button slot=\"trigger\"  type=\"primary\">选取文件</el-button>\n                  <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png文件</div>\n                </el-upload>\n              </el-form-item>\n            </el-row>\n\n<!--              <el-col :span=\"8\">-->\n<!--                <el-form-item label=\"设计污秽等级\" prop=\"whdj\">-->\n<!--                  <el-input v-model=\"jbxxForm.whdj\" placeholder=\"请输入污秽等级\"></el-input>-->\n<!--                </el-form-item>-->\n<!--              </el-col>-->\n<!--              <el-col :span=\"8\">-->\n<!--                <el-form-item label=\"土壤性质\" prop=\"trxz\">-->\n<!--                  <el-select v-model=\"jbxxForm.trxz\" placeholder=\"请选择土壤性质\">-->\n<!--                  </el-select>-->\n<!--                </el-form-item>-->\n<!--              </el-col>-->\n\n\n<!--              <el-col :span=\"8\">-->\n<!--                <el-form-item label=\"实际污秽等级\" prop=\"sjwhdj\">-->\n<!--                  <el-select v-model=\"jbxxForm.sjwhdj\" placeholder=\"请选择实际污秽等级\">-->\n<!--                  </el-select>-->\n<!--                </el-form-item>-->\n<!--              </el-col>-->\n<!--              <el-col :span=\"8\">-->\n<!--                <el-form-item label=\"设计K值(曲率)\" prop=\"edpl\">-->\n<!--                  <el-select v-model=\"jbxxForm.edpl\" placeholder=\"请选择K值(曲率)\">-->\n<!--                  </el-select>-->\n<!--                </el-form-item>-->\n<!--              </el-col>-->\n\n\n<!--              <el-col :span=\"8\">-->\n<!--                <el-form-item label=\"建设日期\" class=\"add_sy_tyrq\" prop=\"jsrq\">-->\n<!--                  <el-date-picker-->\n<!--                    v-model=\"jbxxForm.jsrq\"-->\n<!--                    type=\"date\"-->\n<!--                    placeholder=\"选择日期\"-->\n<!--                    format=\"yyyy-MM-dd\"-->\n<!--                    value-format=\"yyyy-MM-dd\">-->\n<!--                  </el-date-picker>-->\n<!--                </el-form-item>-->\n<!--              </el-col>-->\n\n\n<!--              <el-col :span=\"8\">-->\n<!--                <el-form-item label=\"基础形式\" prop=\"jcxs\">-->\n<!--                  <el-select v-model=\"jbxxForm.jcxs\" placeholder=\"请选择基础设施\">-->\n<!--                  </el-select>-->\n<!--                </el-form-item>-->\n<!--              </el-col>-->\n\n\n\n<!--              <el-col :span=\"8\">-->\n<!--                <el-form-item label=\"生产厂家\" prop=\"sccj\">-->\n<!--                  <el-input v-model=\"jbxxForm.sccj\" placeholder=\"请输入生产厂家\"></el-input>-->\n<!--                </el-form-item>-->\n<!--              </el-col>-->\n          </el-form>\n        </el-tab-pane>\n\n        <!--设备--><!--暂时不要了 2022.06.16-->\n        <!--<el-tab-pane label=\"设备\" name=\"sbjscs\">\n          <el-tabs v-model=\"gtgjsbTabName\" type=\"card\">\n            &lt;!&ndash;基础&ndash;&gt;\n            <el-tab-pane label=\"基础\" name=\"jc\">\n              &lt;!&ndash;查询及列表&ndash;&gt;\n              <el-col :span=\"24\">\n                <el-card class=\"box-card\">\n                  <el-filter\n                    ref=\"filter1\"\n                    :data=\"filterInfoJC.data\"\n                    :field-list=\"filterInfoJC.fieldList\"\n                    @handleReset=\"filterReset\"\n                    :width=\"{ labelWidth: 140, itemWidth: 190 }\"\n                  />\n                  <el-white class=\"button-group\">\n                    <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n                      <el-col\n                        style=\"\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\">\n                        <div>\n                          <el-button icon=\"el-icon-plus\" type=\"primary\" @click=\"JcClick\">新增</el-button>\n                          <el-button icon=\"el-icon-delete\" type=\"danger\" @click=\"JcDelete\">删除</el-button>\n                        </div>\n                      </el-col>\n                    </el-row>\n                  </el-white>\n                  <comp-table :table-and-page-info=\"tableAndPageInfoJC\" @update:multipleSelection=\"selectChange\"/>\n                </el-card>\n              </el-col>\n              &lt;!&ndash;列表页的基础弹出框展示&ndash;&gt;\n              <el-dialog :title=\"jctitle\" :visible.sync=\"JcDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"50%\"\n                         append-to-body>\n                &lt;!&ndash;基本信息&ndash;&gt;\n                <el-form :model=\"jcForm\" label-width=\"100px\">\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线路\" prop=\"ssxl\">\n                        <el-select v-model=\"jcForm.ssxl\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线段\" prop=\"ssxd\">\n                        <el-select v-model=\"jcForm.ssxd\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属杆塔\" prop=\"ssgt\">\n                        <el-select v-model=\"jcForm.ssgt\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"基础型式\" prop=\"jcxs\">\n                        <el-select v-model=\"jcForm.jcxs\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"基础材质\" prop=\"jccz\">\n                        <el-select v-model=\"jcForm.jccz\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"竣工日期\" prop=\"jgrq\">\n                        <el-date-picker\n                          v-model=\"jcForm.jgrq\"\n                          :disabled=\"isDisabled\"\n                          type=\"date\"\n                          placeholder=\"选择日期\">\n                        </el-date-picker>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"记录人\" prop=\"jlr\">\n                        <el-select v-model=\"jcForm.jlr\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                </el-form>\n\n                <div slot=\"footer\" class=\"dialog-footer\">\n                  <el-button @click=\"JcDialogFormVisible = false\">取 消</el-button>\n                  <el-button v-if=\"jctitle=='基础增加' || jctitle=='基础修改'\" type=\"primary\" @click=\"JcSubmit\"\n                             class=\"pmyBtn\">确 定</el-button>\n                </div>\n\n              </el-dialog>\n            </el-tab-pane>\n\n            &lt;!&ndash;绝缘子&ndash;&gt;\n            <el-tab-pane label=\"绝缘子\" name=\"jyz\">\n              &lt;!&ndash;查询及列表&ndash;&gt;\n              <el-col :span=\"24\">\n                <el-card class=\"box-card\">\n                  <el-filter\n                    ref=\"filter1\"\n                    :data=\"filterInfoJyz.data\"\n                    :field-list=\"filterInfoJyz.fieldList\"\n                    @handleReset=\"filterReset\"\n                    :width=\"{ labelWidth: 140, itemWidth: 190 }\"\n                  />\n                  <el-white class=\"button-group\">\n                    <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n                      <el-col\n                        style=\"\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\">\n                        <div>\n                          <el-button icon=\"el-icon-plus\" type=\"primary\" @click=\"JyzClick\">新增</el-button>\n                          <el-button icon=\"el-icon-delete\" type=\"danger\" @click=\"JyzDelete\">删除</el-button>\n                        </div>\n                      </el-col>\n                    </el-row>\n                  </el-white>\n                  <comp-table :table-and-page-info=\"tableAndPageInfoJyz\" @update:multipleSelection=\"selectChange\"/>\n                </el-card>\n              </el-col>\n              &lt;!&ndash;列表页的基础弹出框展示&ndash;&gt;\n              <el-dialog :title=\"jyztitle\" :visible.sync=\"JyzDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"50%\"\n                         append-to-body>\n                &lt;!&ndash;基本信息&ndash;&gt;\n                <el-form :model=\"jyzForm\" label-width=\"100px\">\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线路\" prop=\"ssxl\">\n                        <el-select v-model=\"jyzForm.ssxl\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线段\" prop=\"ssxd\">\n                        <el-select v-model=\"jyzForm.ssxd\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属杆塔\" prop=\"ssgt\">\n                        <el-select v-model=\"jyzForm.ssgt\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"绝缘子数量\" prop=\"jyzsl\">\n                        <el-input v-model=\"jyzForm.jyzsl\" :disabled=\"isDisabled\" placeholder=\"(个)\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"绝缘子型号\" prop=\"jyzxh\">\n                        <el-input v-model=\"jyzForm.jyzxh\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"绝缘子型式 \" prop=\"jyzxs\">\n                        <el-input v-model=\"jyzForm.jyzxs\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"绝缘子材料\" prop=\"jyzcl\">\n                        <el-input v-model=\"jyzForm.jyzcl\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"记录人\" prop=\"jlr\">\n                        <el-input v-model=\"jyzForm.jlr\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"生产厂家 \" prop=\"sccj\">\n                        <el-input v-model=\"jyzForm.sccj\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                </el-form>\n\n                <div slot=\"footer\" class=\"dialog-footer\">\n                  <el-button @click=\"JyzDialogFormVisible = false\">取 消</el-button>\n                  <el-button v-if=\"jyztitle=='绝缘子增加' || jyztitle=='绝缘子修改'\" type=\"primary\" @click=\"JyzSubmit\"\n                             class=\"pmyBtn\">确 定\n                  </el-button>\n                </div>\n\n              </el-dialog>\n            </el-tab-pane>\n\n            &lt;!&ndash;接地&ndash;&gt;\n            <el-tab-pane label=\"接地\" name=\"jd\">\n              &lt;!&ndash;查询及列表&ndash;&gt;\n              <el-col :span=\"24\">\n                <el-card class=\"box-card\">\n                  <el-filter\n                    ref=\"filter1\"\n                    :data=\"filterInfoJd.data\"\n                    :field-list=\"filterInfoJd.fieldList\"\n                    @handleReset=\"filterReset\"\n                    :width=\"{ labelWidth: 140, itemWidth: 190 }\"\n                  />\n                  <el-white class=\"button-group\">\n                    <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n                      <el-col\n                        style=\"\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\">\n                        <div>\n                          <el-button icon=\"el-icon-plus\" type=\"primary\" @click=\"JdClick\">新增</el-button>\n                          <el-button icon=\"el-icon-delete\" type=\"danger\" @click=\"JdDelete\">删除</el-button>\n                        </div>\n                      </el-col>\n                    </el-row>\n                  </el-white>\n                  <comp-table :table-and-page-info=\"tableAndPageInfoJd\" @update:multipleSelection=\"selectChange\"/>\n                </el-card>\n              </el-col>\n              &lt;!&ndash;列表页的基础弹出框展示&ndash;&gt;\n              <el-dialog :title=\"jdtitle\" :visible.sync=\"JdDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"50%\"\n                         append-to-body>\n                &lt;!&ndash;基本信息&ndash;&gt;\n                <el-form :model=\"jdForm\" label-width=\"100px\">\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线路\" prop=\"ssxl\">\n                        <el-select v-model=\"jdForm.ssxl\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线段\" prop=\"ssxd\">\n                        <el-select v-model=\"jdForm.ssxd\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属杆塔\" prop=\"ssgt\">\n                        <el-select v-model=\"jdForm.ssgt\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"接地材质\" prop=\"jdcz\">\n                        <el-input v-model=\"jdForm.jdcz\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"接地类型\" prop=\"jdlx\">\n                        <el-input v-model=\"jdForm.jdlx\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"接地极数量\" prop=\"jdjsl\">\n                        <el-input v-model=\"jdForm.jdjsl\" :disabled=\"isDisabled\" placeholder=\"(个)\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"接地电阻值\" prop=\"jddzz\">\n                        <el-input v-model=\"jdForm.jddzz\" :disabled=\"isDisabled\" placeholder=\"(Ω)\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"记录人\" prop=\"jlr\">\n                        <el-input v-model=\"jdForm.jlr\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                </el-form>\n\n                <div slot=\"footer\" class=\"dialog-footer\">\n                  <el-button @click=\"JdDialogFormVisible = false\">取 消</el-button>\n                  <el-button v-if=\"jdtitle=='接地增加' || jdtitle=='接地修改'\" type=\"primary\" @click=\"JdSubmit\" class=\"pmyBtn\">确\n                    定\n                  </el-button>\n                </div>\n\n              </el-dialog>\n            </el-tab-pane>\n\n            &lt;!&ndash;金具&ndash;&gt;\n            <el-tab-pane label=\"金具\" name=\"jj\">\n              &lt;!&ndash;查询及列表&ndash;&gt;\n              <el-col :span=\"24\">\n                <el-card class=\"box-card\">\n                  <el-filter\n                    ref=\"filter1\"\n                    :data=\"filterInfoJj.data\"\n                    :field-list=\"filterInfoJj.fieldList\"\n                    @handleReset=\"filterReset\"\n                    :width=\"{ labelWidth: 140, itemWidth: 190 }\"\n                  />\n                  <el-white class=\"button-group\">\n                    <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n                      <el-col\n                        style=\"\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\">\n                        <div>\n                          <el-button icon=\"el-icon-plus\" type=\"primary\" @click=\"JjClick\">新增</el-button>\n                          <el-button icon=\"el-icon-delete\" type=\"danger\" @click=\"JjDelete\">删除</el-button>\n                        </div>\n                      </el-col>\n                    </el-row>\n                  </el-white>\n                  <comp-table :table-and-page-info=\"tableAndPageInfoJj\" @update:multipleSelection=\"selectChange\"/>\n                </el-card>\n              </el-col>\n              &lt;!&ndash;列表页的基础弹出框展示&ndash;&gt;\n              <el-dialog :title=\"jjtitle\" :visible.sync=\"JjDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"50%\"\n                         append-to-body>\n                &lt;!&ndash;基本信息&ndash;&gt;\n                <el-form :model=\"jjForm\" label-width=\"100px\">\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线路\" prop=\"ssxl\">\n                        <el-select v-model=\"jjForm.ssxl\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线段\" prop=\"ssxd\">\n                        <el-select v-model=\"jjForm.ssxd\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属杆塔\" prop=\"ssgt\">\n                        <el-select v-model=\"jjForm.ssgt\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"描述\" prop=\"ms\">\n                        <el-input v-model=\"jjForm.ms\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"金具型号规格\" prop=\"jjxhgg\">\n                        <el-input v-model=\"jjForm.jjxhgg\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"金具类别\" prop=\"jjlb\">\n                        <el-input v-model=\"jjForm.jjlb\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"数量(个)\" prop=\"sl\">\n                        <el-input v-model=\"jjForm.sl\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"记录人\" prop=\"jlr\">\n                        <el-input v-model=\"jjForm.jlr\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"生产厂家\" prop=\"sccj\">\n                        <el-input v-model=\"jjForm.sccj\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                </el-form>\n\n                <div slot=\"footer\" class=\"dialog-footer\">\n                  <el-button @click=\"JjDialogFormVisible = false\">取 消</el-button>\n                  <el-button v-if=\"jjtitle=='金具增加' || jjtitle=='金具修改'\" type=\"primary\" @click=\"JjSubmit\" class=\"pmyBtn\">确\n                    定\n                  </el-button>\n                </div>\n\n              </el-dialog>\n            </el-tab-pane>\n\n            &lt;!&ndash;拉线&ndash;&gt;\n            <el-tab-pane label=\"拉线\" name=\"lx\">\n              &lt;!&ndash;查询及列表&ndash;&gt;\n              <el-col :span=\"24\">\n                <el-card class=\"box-card\">\n                  <el-filter\n                    ref=\"filter1\"\n                    :data=\"filterInfoLx.data\"\n                    :field-list=\"filterInfoLx.fieldList\"\n                    @handleReset=\"filterReset\"\n                    :width=\"{ labelWidth: 140, itemWidth: 190 }\"\n                  />\n                  <el-white class=\"button-group\">\n                    <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n                      <el-col\n                        style=\"\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\">\n                        <div>\n                          <el-button icon=\"el-icon-plus\" type=\"primary\" @click=\"LxClick\">新增</el-button>\n                          <el-button icon=\"el-icon-delete\" type=\"danger\" @click=\"LxDelete\">删除</el-button>\n                        </div>\n                      </el-col>\n                    </el-row>\n                  </el-white>\n                  <comp-table :table-and-page-info=\"tableAndPageInfoLx\" @update:multipleSelection=\"selectChange\"/>\n                </el-card>\n              </el-col>\n              &lt;!&ndash;列表页的基础弹出框展示&ndash;&gt;\n              <el-dialog :title=\"lxtitle\" :visible.sync=\"LxDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"50%\"\n                         append-to-body>\n                &lt;!&ndash;基本信息&ndash;&gt;\n                <el-form :model=\"lxForm\" label-width=\"100px\">\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线路\" prop=\"ssxl\">\n                        <el-select v-model=\"lxForm.ssxl\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线段\" prop=\"ssxd\">\n                        <el-select v-model=\"lxForm.ssxd\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属杆塔\" prop=\"ssgt\">\n                        <el-select v-model=\"lxForm.ssgt\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"拉线型式\" prop=\"lxxs\">\n                        <el-input v-model=\"lxForm.lxxs\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"拉线数量\" prop=\"lxsl\">\n                        <el-input v-model=\"lxForm.lxsl\" :disabled=\"isDisabled\" placeholder=\"(个)\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"戗杆规格\" prop=\"qggg\">\n                        <el-input v-model=\"lxForm.qggg\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"拉线棒规格\" prop=\"lxbgg\">\n                        <el-input v-model=\"lxForm.lxbgg\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"钢绞线规格\" prop=\"gjxgg\">\n                        <el-input v-model=\"lxForm.gjxgg\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"拉线盘规格\" prop=\"lxpgg\">\n                        <el-input v-model=\"lxForm.lxpgg\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"拉线绝缘子规格\" prop=\"lxjyzgg\">\n                        <el-input v-model=\"lxForm.lxjyzgg\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"记录人\" prop=\"jlr\">\n                        <el-input v-model=\"lxForm.jlr\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                </el-form>\n\n                <div slot=\"footer\" class=\"dialog-footer\">\n                  <el-button @click=\"LxDialogFormVisible = false\">取 消</el-button>\n                  <el-button v-if=\"lxtitle=='拉线增加' || lxtitle=='拉线修改'\" type=\"primary\" @click=\"LxSubmit\" class=\"pmyBtn\">确 定</el-button>\n                </div>\n\n              </el-dialog>\n            </el-tab-pane>\n\n            &lt;!&ndash;附属&ndash;&gt;\n            <el-tab-pane label=\"附属\" name=\"fs\">\n              &lt;!&ndash;查询及列表&ndash;&gt;\n              <el-col :span=\"24\">\n                <el-card class=\"box-card\">\n                  <el-filter\n                    ref=\"filter1\"\n                    :data=\"filterInfoFs.data\"\n                    :field-list=\"filterInfoFs.fieldList\"\n                    @handleReset=\"filterReset\"\n                    :width=\"{ labelWidth: 140, itemWidth: 190 }\"\n                  />\n                  <el-white class=\"button-group\">\n                    <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n                      <el-col\n                        style=\"\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\">\n                        <div>\n                          <el-button icon=\"el-icon-plus\" type=\"primary\" @click=\"FsClick\">新增</el-button>\n                          <el-button icon=\"el-icon-delete\" type=\"danger\" @click=\"FsDelete\">删除</el-button>\n                        </div>\n                      </el-col>\n                    </el-row>\n                  </el-white>\n                  <comp-table :table-and-page-info=\"tableAndPageInfoFs\" @update:multipleSelection=\"selectChange\"/>\n                </el-card>\n              </el-col>\n              &lt;!&ndash;列表页的基础弹出框展示&ndash;&gt;\n              <el-dialog :title=\"fstitle\" :visible.sync=\"FsDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"50%\"\n                         append-to-body>\n                &lt;!&ndash;基本信息&ndash;&gt;\n                <el-form :model=\"fsForm\" label-width=\"100px\">\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线路\" prop=\"ssxl\">\n                        <el-select v-model=\"fsForm.ssxl\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线段\" prop=\"ssxd\">\n                        <el-select v-model=\"fsForm.ssxd\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"设备名称\" prop=\"sbmc\">\n                        <el-select v-model=\"fsForm.sbmc\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"附属类别\" prop=\"fslb\">\n                        <el-input v-model=\"fsForm.fslb\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"投运日期\" prop=\"tyrq\">\n                        <el-date-picker\n                          v-model=\"fsForm.tyrq\"\n                          :disabled=\"isDisabled\"\n                          type=\"date\"\n                          placeholder=\"选择日期\">\n                        </el-date-picker>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"数量 \" prop=\"sl\">\n                        <el-input v-model=\"fsForm.sl\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"生产厂家\" prop=\"sccj\">\n                        <el-input v-model=\"fsForm.sccj\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"出厂日期\" prop=\"ccrq\">\n                        <el-date-picker\n                          v-model=\"fsForm.ccrq\"\n                          :disabled=\"isDisabled\"\n                          type=\"date\"\n                          placeholder=\"选择日期\">\n                        </el-date-picker>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"记录人\" prop=\"jlr\">\n                        <el-input v-model=\"fsForm.jlr\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                </el-form>\n\n                <div slot=\"footer\" class=\"dialog-footer\">\n                  <el-button @click=\"FsDialogFormVisible = false\">取 消</el-button>\n                  <el-button v-if=\"fstitle=='附属增加' || fstitle=='附属修改'\" type=\"primary\" @click=\"FsSubmit\" class=\"pmyBtn\">确\n                    定\n                  </el-button>\n                </div>\n\n              </el-dialog>\n            </el-tab-pane>\n\n            &lt;!&ndash;同杆并架&ndash;&gt;\n            &lt;!&ndash;<el-tab-pane label=\"同杆并架\" name=\"tgbj\">\n              <el-card>\n                <el-form :model=\"queryParams\" ref=\"queryForm\" v-show=\"showSearch\" :inline=\"true\" class=\"form_box\">\n                  <el-form-item label=\"电压等级\" prop=\"roleName\">\n                    <el-select v-model=\"form.ssxl\" placeholder=\"请选择电压等级\" size=\"small\">\n                      <el-option label=\"110kV\" value=\"10\"></el-option>\n                      <el-option label=\"35kV\" value=\"11\"></el-option>\n                      <el-option label=\"10kV\" value=\"12\"></el-option>\n                      <el-option label=\"6kV\" value=\"13\"></el-option>\n                    </el-select>\n                  </el-form-item>\n                  <el-form-item label=\"设备状态\" prop=\"roleName\">\n                    <el-select v-model=\"form.sbzt\" placeholder=\"请选择设备状态\" size=\"small\">\n                      <el-option\n                        v-for=\"item in sbzt\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\">\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                  <el-form-item class=\"mb8 pull-right\">\n                    <el-button type=\"cyan\" icon=\"el-icon-search\" style=\"margin-left: 5px;\" size=\"small\">搜索\n                    </el-button>\n                    <el-button icon=\"el-icon-refresh\" size=\"small\">重置</el-button>\n                  </el-form-item>\n                  <el-form>\n                    <el-form-item style=\"float: right\">\n                      <el-button class=\"mb8\" @click=\"bdzAddSensorButton\" type=\"primary\">添加</el-button>\n                      <el-button class=\"mb8\" type=\"warning\">修改</el-button>\n                      <el-button class=\"mb8\" type=\"danger\">删除</el-button>\n                    </el-form-item>\n                  </el-form>\n                </el-form>\n              </el-card>\n              <el-card class=\"box-cardList\" shadow=\"never\">\n                <el-table stripe border v-loading=\"loading\" max-height=\"300\">\n                  <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n                  <el-table-column label=\"所属线路\" align=\"center\" prop=\"ssxl\"/>\n                  <el-table-column label=\"电压等级\" align=\"center\" prop=\"dydj\" :show-overflow-tooltip=\"true\"/>\n                  <el-table-column label=\"设备状态\" align=\"center\" prop=\"sbzt\" :show-overflow-tooltip=\"true\"/>\n                  <el-table-column label=\"杆塔材质\" align=\"center\" prop=\"gtcz\"/>\n                  <el-table-column label=\"负责人\" align=\"center\" prop=\"fzr\" :show-overflow-tooltip=\"true\"/>\n                  <el-table-column label=\"投运日期\" align=\"center\" prop=\"tyrq\" :show-overflow-tooltip=\"true\"/>\n                  <el-table-column\n                    align=\"center\"\n                    label=\"操作\"\n                    width=\"100\">\n                    <template slot-scope=\"scope\">\n                      <el-button @click=\"bdzAddSensorButton(scope.row)\" type=\"text\" size=\"small\">详情</el-button>\n                    </template>\n                  </el-table-column>\n                </el-table>\n              </el-card>\n              <el-pagination\n                style=\"float: right;margin-top: 10px\"\n                @size-change=\"handleSizeChange\"\n                @current-change=\"handleCurrentChange\"\n                :current-page=\"1\"\n                :page-sizes=\"[100, 200, 300, 400]\"\n                :page-size=\"100\"\n                layout=\"total, sizes, prev, pager, next, jumper\"\n                :total=\"400\">\n              </el-pagination>\n            </el-tab-pane>&ndash;&gt;\n\n            &lt;!&ndash;横担&ndash;&gt;\n            <el-tab-pane label=\"横担\" name=\"hd\">\n              &lt;!&ndash;查询及列表&ndash;&gt;\n              <el-col :span=\"24\">\n                <el-card class=\"box-card\">\n                  <el-filter\n                    ref=\"filter1\"\n                    :data=\"filterInfoHd.data\"\n                    :field-list=\"filterInfoHd.fieldList\"\n                    @handleReset=\"filterReset\"\n                    :width=\"{ labelWidth: 140, itemWidth: 190 }\"\n                  />\n                  <el-white class=\"button-group\">\n                    <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n                      <el-col\n                        style=\"\n              display: flex;\n              justify-content: space-between;\n              align-items: center;\">\n                        <div>\n                          <el-button icon=\"el-icon-plus\" type=\"primary\" @click=\"HdClick\">新增</el-button>\n                          <el-button icon=\"el-icon-delete\" type=\"danger\" @click=\"HdDelete\">删除</el-button>\n                        </div>\n                      </el-col>\n                    </el-row>\n                  </el-white>\n                  <comp-table :table-and-page-info=\"tableAndPageInfoHd\" @update:multipleSelection=\"selectChange\"/>\n                </el-card>\n              </el-col>\n              &lt;!&ndash;列表页的基础弹出框展示&ndash;&gt;\n              <el-dialog :title=\"hdtitle\" :visible.sync=\"HdDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"50%\"\n                         append-to-body>\n                &lt;!&ndash;基本信息&ndash;&gt;\n                <el-form :model=\"hdForm\" label-width=\"100px\">\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线路\" prop=\"ssxl\">\n                        <el-select v-model=\"hdForm.ssxl\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属线段\" prop=\"ssxd\">\n                        <el-select v-model=\"hdForm.ssxd\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"所属杆塔\" prop=\"ssgt\">\n                        <el-select v-model=\"hdForm.ssgt\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-select>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"横担类型\" prop=\"hdlx\">\n                        <el-input v-model=\"hdForm.hdlx\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"数量\" prop=\"sl\">\n                        <el-input v-model=\"hdForm.sl\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"生产厂家\" prop=\"sccj\">\n                        <el-input v-model=\"hdForm.sccj\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                  <el-row>\n                    <el-col :span=\"8\">\n                      <el-form-item label=\"记录人\" prop=\"jl\">\n                        <el-input v-model=\"hdForm.jl\" :disabled=\"isDisabled\" placeholder=\"请输入内容\"></el-input>\n                      </el-form-item>\n                    </el-col>\n                  </el-row>\n                </el-form>\n\n                <div slot=\"footer\" class=\"dialog-footer\">\n                  <el-button @click=\"HdDialogFormVisible = false\">取 消</el-button>\n                  <el-button v-if=\"hdtitle=='横担增加' || hdtitle=='横担修改'\" type=\"primary\" @click=\"HdSubmit\" class=\"pmyBtn\">确 定\n                  </el-button>\n                </div>\n\n              </el-dialog>\n            </el-tab-pane>\n          </el-tabs>\n        </el-tab-pane>-->\n\n        <!--设备履历-->\n        <el-tab-pane label=\"设备履历\" name=\"sbRecord\">\n          <!--@tab-click=\"handleSbllDescTabNameClick\"-->\n          <el-tabs v-model=\"sbllDescTabName\" type=\"card\">\n            <el-tab-pane label=\"缺陷记录\" name=\"qxjl\">\n              <el-table stripe border v-loading=\"loading\" :data=\"sbllqxjlList\" max-height=\"550\">\n                <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n                <el-table-column label=\"所属线路\" align=\"center\" prop=\"lineName\"/>\n                <el-table-column label=\"杆塔号\" align=\"center\" prop=\"gth\" :show-overflow-tooltip=\"true\"/>\n                <el-table-column label=\"设备类型\" align=\"center\" prop=\"sblx\" :show-overflow-tooltip=\"true\"/>\n                <el-table-column label=\"缺陷性质\" align=\"center\" prop=\"qxxz\" :show-overflow-tooltip=\"true\"/>\n                <el-table-column label=\"电压等级\" align=\"center\" prop=\"dydj\" :show-overflow-tooltip=\"true\"/>\n                <el-table-column label=\"设备型号\" align=\"center\" prop=\"sbxh\"/>\n                <el-table-column label=\"生产厂家\" align=\"center\" prop=\"sccj\" :show-overflow-tooltip=\"true\"/>\n              </el-table>\n            </el-tab-pane>\n            <el-tab-pane label=\"状态变更记录\" name=\"ztbgjl\">\n              <comp-table\n                :table-and-page-info=\"resumPageInfo\"\n                @getMethod=\"getResumList\"\n                @update:multipleSelection=\"handleSelectionChange\"\n                height=\"30vh\"\n              />\n            </el-tab-pane>\n          </el-tabs>\n        </el-tab-pane>\n\n      </el-tabs>\n      <div slot=\"footer\" class=\"dialog-footer\" v-show=\"!show\">\n        <el-button @click=\"handleClose\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"addGtInfo\">确 定</el-button>\n      </div>\n    </el-dialog>\n    <!--设备状态变更弹框展示-->\n    <el-dialog title=\"设备状态变更\" :visible.sync=\"dialogVisible\" width=\"30%\" append-to-body>\n      <el-form :inline=\"true\" label-width=\"100px\" class=\"qxlr_dialog_insert\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"设备状态:\">\n              <el-select v-model=\"updateList.sbzt\">\n                <el-option\n                  v-for=\"item in sbzt\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitStatus\">确 定</el-button>\n      </div>\n    </el-dialog>\n\n\n  </div>\n\n</template>\n\n<script>\n  import { getToken } from \"@/utils/auth\";\n  import { getListgt, gtremove, saveOrUpdategt, getTreeList, updateStatus } from '@/api/dagangOilfield/asset/sdgt'\n  import { getResumDataList } from '@/api/dagangOilfield/asset/sdsb'\n  import { getListJc, removeJc, saveOrUpdateJc } from '@/api/dagangOilfield/dwzygl/sdsbgl/sdgtjc'\n  import { getListJyz, removeJyz, saveOrUpdateJyz } from '@/api/dagangOilfield/dwzygl/sdsbgl/sdgtjyz'\n  import { getListJd, removeJd, saveOrUpdateJd } from '@/api/dagangOilfield/dwzygl/sdsbgl/sdgtjd'\n  import { getListJj, removeJj, saveOrUpdateJj } from '@/api/dagangOilfield/dwzygl/sdsbgl/sdgtjj'\n  import { getListLx, removeLx, saveOrUpdateLx } from '@/api/dagangOilfield/dwzygl/sdsbgl/sdgtlx'\n  import { getListFs, removeFs, saveOrUpdateFs } from '@/api/dagangOilfield/dwzygl/sdsbgl/sdgtfs'\n  import { getListHd, removeHd, saveOrUpdateHd } from '@/api/dagangOilfield/dwzygl/sdsbgl/sdgthd'\n  import {deleteById, getListByBusinessId} from \"@/api/tool/file\";\n\n  export default {\n    name: 'sdgt',\n    data() {\n      return {\n        uploadData: {\n          type:\"\",\n          businessId: undefined\n        },\n        //杆塔形状结合\n        gtxzOptions:[\n          {label:'4字型',value:'4字型'},\n          {label:'A型',value:'A型'},\n          {label:'三角形',value:'三角形'},\n          {label:'上字型',value:'上字型'},\n          {label:'伞型终端',value:'伞型终端'},\n          {label:'倒伞型',value:'倒伞型'},\n          {label:'分歧型',value:'分歧型'},\n          {label:'分崎型',value:'分崎型'},\n          {label:'叉骨型换位塔',value:'叉骨型换位塔'},\n          {label:'干字型',value:'干字型'},\n          {label:'拉V型',value:'拉V型'},\n          {label:'桥型',value:'桥型'},\n          {label:'猫头型',value:'猫头型'},\n          {label:'紧凑型',value:'紧凑型'},\n          {label:'羊角型',value:'羊角型'},\n          {label:'酒杯型',value:'酒杯型'},\n          {label:'门型',value:'门型'},\n          {label:'门型拉线塔',value:'门型拉线塔'},\n          {label:'鼓型塔',value:'鼓型塔'},\n        ],\n        // 多选框选中的id\n        ids: [],\n        //是否禁用\n        isDisabled: false,\n        // 单击tab选中的节点\n        selectNode: 'jc',\n        //基础标题\n        jctitle: '',\n        //绝缘子标题\n        jyztitle: '',\n        //导线标题\n        daoxtitle: '',\n        //地线标题\n        jdtitle: '',\n        //金具标题\n        jjtitle: '',\n        //拉线标题\n        lxtitle: '',\n        //附属标题\n        fstitle: '',\n        //同杆并架标题\n        tgbjtitle: '',\n        //横担标题\n        hdtitle: '',\n\n        //基础弹出框是否弹出\n        JcDialogFormVisible: false,\n        //绝缘子弹出框是否弹出\n        JyzDialogFormVisible: false,\n        //导线弹出框是否弹出\n        DaoxDialogFormVisible: false,\n        //接地弹出框是否弹出\n        JdDialogFormVisible: false,\n        //金具弹出框是否弹出\n        JjDialogFormVisible: false,\n        //拉线弹出框是否弹出\n        LxDialogFormVisible: false,\n        //附属弹出框是否弹出\n        FsDialogFormVisible: false,\n        //同杆并架弹出框是否弹出\n        TgbjDialogFormVisible: false,\n        //横担弹出框是否弹出\n        HdDialogFormVisible: false,\n\n        //基础form\n        jcForm: {\n          ssxl: '',\n          ssxd: '',\n          ssgt: '',\n          jcxs: '',\n          jccz: '',\n          jlr: '',\n          jgrq: ''\n        },\n        //绝缘子form\n        jyzForm: {\n          ssxl: '',\n          ssxd: '',\n          ssgt: '',\n          jyzsl: '',\n          jyzxh: '',\n          jyzxs: '',\n          jyzcl: '',\n          jlr: '',\n          sccj: ''\n        },\n        //导线form\n        daoxForm: {\n          ssxd: '',\n          ssgt: '',\n          jcxs: '',\n          jccz: '',\n          jlr: '',\n          jgrq: ''\n        },\n        //接地form\n        jdForm: {\n          ssxl: '',\n          ssxd: '',\n          ssgt: '',\n          jdcz: '',\n          jdlx: '',\n          jdjsl: '',\n          jddzz: '',\n          jlr: ''\n        },\n        //金具form\n        jjForm: {\n          ssxl: '',\n          ssxd: '',\n          ssgt: '',\n          ms: '',\n          jjxhgg: '',\n          jjlb: '',\n          sl: '',\n          jlr: '',\n          sccj: ''\n        },\n        //拉线form\n        lxForm: {\n          ssxl: '',\n          ssxd: '',\n          ssgt: '',\n          lxxs: '',\n          lxsl: '',\n          qggg: '',\n          lxbgg: '',\n          gjxgg: '',\n          lxpgg: '',\n          lxjyzgg: '',\n          jlr: ''\n        },\n        //附属form\n        fsForm: {\n          ssxl: '',\n          ssxd: '',\n          sbmc: '',\n          fslb: '',\n          tyrq: '',\n          sl: '',\n          sccj: '',\n          ccrq: '',\n          jlr: ''\n        },\n        //同杆并架form\n        tgbjForm: {\n          ssxd: '',\n          ssgt: '',\n          jcxs: '',\n          jccz: '',\n          jlr: '',\n          jgrq: ''\n        },\n        //横担form\n        hdForm: {\n          ssxl: '',\n          ssxd: '',\n          ssgt: '',\n          hdlx: '',\n          sl: '',\n          sccj: '',\n          jlr: ''\n        },\n        resumeQuery: {\n          foreignNum: undefined,\n          sblx: undefined\n        },\n        updateList: {\n          sbzt: '',\n          objId: ''\n        },\n        dialogVisible: false,\n        options: [{\n          value: '110kV',\n          label: '110kV'\n        }, {\n          value: '35kV',\n          label: '35kV'\n        }, {\n          value: '10kV',\n          label: '10kV'\n        }, {\n          value: '6kV',\n          label: '6kV'\n        }], sbzt: [{\n          value: '在运',\n          label: '在运'\n        }, {\n          value: '停运',\n          label: '停运'\n        }, {\n          value: '报废',\n          label: '报废'\n        }],\n        //筛选条件\n        filterInfos: {},\n        //列表信息查看\n        tableAndPageInfos: {},\n        /*---------------------基础--------------------*/\n        //输电杆塔设备基础\n        filterInfoJC: {\n          data: {\n            ssxl: '',\n            ssxd: '',\n            ssgt: '',\n            jcxs: '',\n            jccz: '',\n            jlr: '',\n            jgrq: '',\n          },\n          fieldList: [\n            { label: '所属线路', type: 'input', value: 'ssxl', },\n            { label: '所属杆塔', type: 'input', value: 'ssgt', }]\n        },\n        tableAndPageInfoJC: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n            { prop: 'ssxl', label: '所属线路', minWidth: '120' },\n            { prop: 'ssgt', label: '所属杆塔', minWidth: '100' },\n            { prop: 'jcxs', label: '基础型式', minWidth: '120' },\n            { prop: 'jccz', label: '基础材质', minWidth: '100' },\n            { prop: 'jlr', label: '记录人', minWidth: '80' },\n            { prop: 'jgrq', label: '竣工日期', minWidth: '90' },\n           /* {\n              fixed: 'right',\n              prop: 'operation',\n              label: '操作',\n              minWidth: '120px',\n              style: { display: 'block' },\n              operation: [\n                { name: '修改', clickFun: this.getJcUpdate },\n                { name: '详情', clickFun: this.getJcDetails }\n              ]\n            }*/\n          ],\n          paramJc: {\n            ssxl: '',\n            ssxd: '',\n            ssgt: '',\n            jcxs: '',\n            jccz: '',\n            jlr: '',\n            jgrq: ''\n          }\n        },\n        /*---------------------绝缘子--------------------*/\n        //输电杆塔绝缘子\n        filterInfoJyz: {\n          data: {\n            ssxd: '',\n            ssgt: '',\n            jyzsl: '',\n            jyzxh: '',\n            jyzxs: '',\n            jyzcl: '',\n            jlr: '',\n            sccj: ''\n          },\n          fieldList: [\n            { label: '所属线路', type: 'select', value: 'ssxl', multiple: true, options: [] },\n            { label: '所属线段', type: 'select', value: 'ssxd', multiple: true, options: [] }]\n        },\n        tableAndPageInfoJyz: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n            { prop: 'ssxl', label: '所属线路', minWidth: '100' },\n            { prop: 'ssxd', label: '所属线段', minWidth: '100' },\n            { prop: 'ssgt', label: '所属杆塔', minWidth: '70 ' },\n            { prop: 'jyzsl', label: '绝缘子数量(个)', minWidth: '60' },\n            { prop: 'jyzxh', label: '绝缘子型号 ', minWidth: '100' },\n            { prop: 'jyzxs', label: '绝缘子型式', minWidth: '75' },\n            /*{ prop: 'jyzcl', label: '绝缘子材料', minWidth: '80' },\n            { prop: 'jlr', label: '记录人', minWidth: '80' },\n            { prop: 'sccj', label: '生产厂家', minWidth: '100' },*/\n            {\n              fixed: 'right',\n              prop: 'operation',\n              label: '操作',\n              minWidth: '100',\n              style: { display: 'block' },\n              operation: [\n                { name: '修改', clickFun: this.getJyzUpdate },\n                { name: '详情', clickFun: this.getJyzDetails }\n              ]\n            }\n          ],\n          paramJyz: {\n            ssxd: '',\n            ssgt: '',\n            jyzsl: '',\n            jyzxh: '',\n            jyzxs: '',\n            jyzcl: '',\n            jlr: '',\n            sccj: ''\n          }\n        },\n        /*---------------------接地--------------------*/\n        //输电杆塔接地\n        filterInfoJd: {\n          data: {},\n          fieldList: [\n            { label: '所属线段', type: 'select', value: 'ssxd', multiple: true, options: [] },\n            { label: '所属杆塔', type: 'select', value: 'ssgt', multiple: true, options: [] }]\n        },\n        tableAndPageInfoJd: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n            { prop: 'ssxd', label: '所属线段', minWidth: '100' },\n            { prop: 'ssgt', label: '所属杆塔', minWidth: '100' },\n            { prop: 'jdcz', label: '接地材质', minWidth: '100' },\n            { prop: 'jdlx', label: '接地类型', minWidth: '80' },\n            { prop: 'jdjsl', label: '接地极数量(个)', minWidth: '60' },\n            /*{ prop: 'jddzz', label: '接地电阻值(Ω)', minWidth: '60' },*/\n            { prop: 'jlr', label: '记录人', minWidth: '70' },\n            {\n              fixed: 'right',\n              prop: 'operation',\n              label: '操作',\n              minWidth: '100',\n              style: { display: 'block' },\n              operation: [\n                { name: '修改', clickFun: this.getJdUpdate },\n                { name: '详情', clickFun: this.getJdDetails }\n              ]\n            }\n          ],\n          paramJd: {\n            ssxl: '',\n            ssxd: '',\n            ssgt: '',\n            jdcz: '',\n            jdlx: '',\n            jdjsl: '',\n            jddzz: '',\n            jlr: ''\n          }\n        },\n        /*---------------------金具--------------------*/\n        //输电杆塔接地\n        filterInfoJj: {\n          data: {},\n          fieldList: [\n            { label: '所属线路', type: 'select', value: 'ssxl', multiple: true, options: [] },\n            { label: '所属线段', type: 'select', value: 'ssxd', multiple: true, options: [] }]\n        },\n        tableAndPageInfoJj: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n            { prop: 'ssxl', label: '所属线路', minWidth: '100' },\n            { prop: 'ssxd', label: '所属线段', minWidth: '100' },\n            { prop: 'ssgt', label: '所属杆塔', minWidth: '100' },\n            { prop: 'jjxhgg', label: '金具型号规格', minWidth: '120' },\n            { prop: 'jjlb', label: '金具类别', minWidth: '80' },\n            { prop: 'sl', label: '数量(个)', minWidth: '80' },\n            /*{ prop: 'jlr', label: '记录人', minWidth: '80' },*/\n            {\n              fixed: 'right',\n              prop: 'operation',\n              label: '操作',\n              minWidth: '100',\n              style: { display: 'block' },\n              operation: [\n                { name: '修改', clickFun: this.getJjUpdate },\n                { name: '详情', clickFun: this.getJjDetails }\n              ]\n            }\n          ],\n          paramJj: {\n            ssxl: '',\n            ssxd: '',\n            ssgt: '',\n            ms: '',\n            jjxhgg: '',\n            jjlb: '',\n            sl: '',\n            jlr: '',\n            sccj: ''\n          }\n        },\n        /*---------------------拉线--------------------*/\n        //输电杆塔接地\n        filterInfoLx: {\n          data: {},\n          fieldList: [\n            { label: '所属线路', type: 'select', value: 'ssxl', multiple: true, options: [] },\n            { label: '所属线段', type: 'select', value: 'ssxd', multiple: true, options: [] }]\n        },\n        tableAndPageInfoLx: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n            { prop: 'ssxl', label: '所属线路', minWidth: '100' },\n            { prop: 'ssxd', label: '所属线段', minWidth: '100' },\n            { prop: 'ssgt', label: '所属杆塔', minWidth: '100' },\n            { prop: 'lxxs', label: '拉线型式', minWidth: '90' },\n            { prop: 'lxsl', label: '拉线数量', minWidth: '70' },\n            { prop: 'qggg', label: '戗杆规格', minWidth: '90' },\n            {\n              fixed: 'right',\n              prop: 'operation',\n              label: '操作',\n              minWidth: '100',\n              style: { display: 'block' },\n              operation: [\n                { name: '修改', clickFun: this.getLxUpdate },\n                { name: '详情', clickFun: this.getLxDetails }\n              ]\n            }\n          ],\n          paramLx: {\n            ssxl: '',\n            ssxd: '',\n            ssgt: '',\n            lxxs: '',\n            lxsl: '',\n            qggg: '',\n            lxbgg: '',\n            gjxgg: '',\n            lxpgg: '',\n            lxjyzgg: '',\n            jlr: ''\n          }\n        },\n        /*---------------------附属--------------------*/\n        //输电杆塔接地\n        filterInfoFs: {\n          data: {},\n          fieldList: [\n            { label: '所属线路', type: 'select', value: 'ssxl', multiple: true, options: [] },\n            { label: '所属线段', type: 'select', value: 'ssxd', multiple: true, options: [] }]\n        },\n        tableAndPageInfoFs: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n            { prop: 'ssxl', label: '所属线路', minWidth: '100' },\n            { prop: 'ssxd', label: '所属线段', minWidth: '100' },\n            { prop: 'sbmc', label: '设备名称', minWidth: '100' },\n            { prop: 'fslb', label: '附属类别', minWidth: '100' },\n            { prop: 'tyrq', label: '投运日期', minWidth: '80' },\n            { prop: 'sl', label: '数量', minWidth: '60' },\n            {\n              fixed: 'right',\n              prop: 'operation',\n              label: '操作',\n              minWidth: '100',\n              style: { display: 'block' },\n              operation: [\n                { name: '修改', clickFun: this.getFsUpdate },\n                { name: '详情', clickFun: this.getFsDetails }\n              ]\n            }\n          ],\n          paramFs: {\n            ssxl: '',\n            ssxd: '',\n            sbmc: '',\n            fslb: '',\n            tyrq: '',\n            sl: '',\n            sccj: '',\n            ccrq: '',\n            jlr: ''\n          }\n        },\n        /*---------------------横担--------------------*/\n        //输电杆塔接地\n        filterInfoHd: {\n          data: {},\n          fieldList: [\n            { label: '所属线路', type: 'select', value: 'ssxl', multiple: true, options: [] },\n            { label: '所属线段', type: 'select', value: 'ssxd', multiple: true, options: [] }]\n        },\n        tableAndPageInfoHd: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n            { prop: 'ssxl', label: '所属线路', minWidth: '100' },\n            { prop: 'ssxd', label: '所属线段', minWidth: '100' },\n            { prop: 'ssgt', label: '所属杆塔', minWidth: '100' },\n            { prop: 'hdlx', label: '横担类型', minWidth: '100' },\n            { prop: 'sl', label: '数量', minWidth: '100' },\n            { prop: 'sccj', label: '生产厂家', minWidth: '100' },\n            {\n              fixed: 'right',\n              prop: 'operation',\n              label: '操作',\n              minWidth: '100',\n              style: { display: 'block' },\n              operation: [\n                { name: '修改', clickFun: this.getHdUpdate },\n                { name: '详情', clickFun: this.getHdDetails }\n              ]\n            }\n          ],\n          paramHd: {\n            ssxl: '',\n            ssxd: '',\n            ssgt: '',\n            hdlx: '',\n            sl: '',\n            sccj: '',\n            jlr: ''\n          },\n        },\n        resumPageInfo: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n            { prop: 'foreignNum', label: '设备名称', minWidth: '120' },\n            { prop: 'sblx', label: '设备类型', minWidth: '180' },\n            { prop: 'bglx', label: '变更类型', minWidth: '120' },\n            { prop: 'ms', label: '描述', minWidth: '250' },\n            { prop: 'bgr', label: '变更人', minWidth: '140' },\n            { prop: 'bgsj', label: '变更时间', minWidth: '140' }\n          ]\n        },\n\n        //电压等级下拉框\n        voltageLevelListSelected: [],\n        filterInfo: {\n          data: {\n            sbzt: '',\n            lineName:'',\n            dydj:'',\n          },\n          fieldList: [\n            {\n              label: '所属线路', type: 'input', value: 'lineName', options: []\n            },\n            {\n              label: '电压等级', type: 'select', value: 'dydj', options: [ { label: '6kV', value: '6kV' },{ label: '10kV', value: '10kV' }, { label: '35kV', value: '35kV' }, { label: '110kV', value: '110kV' },\n               ]\n            },\n            {\n              label: '设备状态',\n              type: 'select',\n              value: 'sbzt',\n              options: [{ label: '在运', value: '在运' }, { label: '停运', value: '停运' }]\n            },\n          ]\n        },\n\n        tableAndPageInfo: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n            { prop: 'lineName', label: '所属线路', minWidth: '120' },\n            { prop: 'gtmc', label: '杆塔名称', minWidth: '130' },\n            { prop: 'gtbh', label: '杆塔编号', minWidth: '100' },\n            { prop: 'dydj', label: '电压等级', minWidth: '100' },\n            { prop: 'sbzt', label: '设备状态', minWidth: '50' },\n            { prop: 'gtcz', label: '杆塔材质', minWidth: '50' },\n            { prop: 'tyrq', label: '投运日期', minWidth: '120' },\n            {\n              fixed: 'right',\n              prop: 'operation',\n              label: '操作',\n              minWidth: '130px',\n              style: { display: 'block' },\n              operation: [\n                { name: '状态变更', clickFun: this.updateStatus },\n                { name: '修改', clickFun: this.updateRow },\n                { name: '详情', clickFun: this.detailsInfo }\n              ]\n            }\n          ]\n        },\n        //杆塔挂接设备tab页\n        gtgjsbTabName: 'jc',\n        //杆塔详情弹出框\n        gtDialogFormVisible: false,\n        //设备履历状态变更记录\n        sbllztbgjlList: [\n          {\n            ssgs: '110kVXXX线路',\n            bdzmc: '001',\n            sblx: '杆塔',\n            bglx: '状态变更',\n            desc: '状态变更为报废',\n            bgsj: '2022-12-12',\n            bgr: '张三'\n          }, {\n            ssgs: '110kVXXX线路',\n            bdzmc: '001',\n            sblx: '杆塔',\n            bglx: '状态变更',\n            desc: '状态变更为报废',\n            bgsj: '2022-12-12',\n            bgr: '张三'\n          }, {\n            ssgs: '110kVXXX线路',\n            bdzmc: '001',\n            sblx: '杆塔',\n            bglx: '状态变更',\n            desc: '状态变更为报废',\n            bgsj: '2022-12-12',\n            bgr: '张三'\n          }, {\n            ssgs: '110kVXXX线路',\n            bdzmc: '001',\n            sblx: '杆塔',\n            bglx: '状态变更',\n            desc: '状态变更为报废',\n            bgsj: '2022-12-12',\n            bgr: '张三'\n          }, {\n            ssgs: '110kVXXX线路',\n            bdzmc: '001',\n            sblx: '杆塔',\n            bglx: '状态变更',\n            desc: '状态变更为报废',\n            bgsj: '2022-12-12',\n            bgr: '张三'\n          }\n        ],\n        //设备履历缺陷记录数据集合\n        sbllqxjlList: [\n          {\n            ssgs: '110kVXXX线路',\n            gth: '001#',\n            sblx: '杆塔',\n            qxxz: '严重',\n            dydj: '35kV',\n            sbxh: 'XXX型号',\n            sccj: 'XXX厂家'\n          }, {\n            ssgs: '110kVXXX线路',\n            gth: '001#',\n            sblx: '杆塔',\n            qxxz: '严重',\n            dydj: '35kV',\n            sbxh: 'XXX型号',\n            sccj: 'XXX厂家'\n          }, {\n            ssgs: '110kVXXX线路',\n            gth: '001#',\n            sblx: '杆塔',\n            qxxz: '严重',\n            dydj: '35kV',\n            sbxh: 'XXX型号',\n            sccj: 'XXX厂家'\n          }, {\n            ssgs: '110kVXXX线路',\n            gth: '001#',\n            sblx: '杆塔',\n            qxxz: '严重',\n            dydj: '35kV',\n            sbxh: 'XXX型号',\n            sccj: 'XXX厂家'\n          }, {\n            ssgs: '110kVXXX线路',\n            gth: '001#',\n            sblx: '杆塔',\n            qxxz: '严重',\n            dydj: '35kV',\n            sbxh: 'XXX型号',\n            sccj: 'XXX厂家'\n          }\n        ],\n        //设备履历试验记录数据\n        sblvsyjlList: [\n          {\n            syzy: '带电',\n            syxz: '例行试验',\n            symc: 'XXXXX',\n            gzdd: 'XXX平台',\n            sysb: '主变压器',\n            sybg: '',\n            tq: '晴',\n            syrq: '2022-01-01',\n            lrr: '张三',\n            syjl: 'XXXXX'\n          }, {\n            syzy: '带电',\n            syxz: '例行试验',\n            symc: 'XXXXX',\n            gzdd: 'XXX平台',\n            sysb: '主变压器',\n            sybg: '',\n            tq: '晴',\n            syrq: '2022-01-01',\n            lrr: '张三',\n            syjl: 'XXXXX'\n          }, {\n            syzy: '带电',\n            syxz: '例行试验',\n            symc: 'XXXXX',\n            gzdd: 'XXX平台',\n            sysb: '主变压器',\n            sybg: '',\n            tq: '晴',\n            syrq: '2022-01-01',\n            lrr: '张三',\n            syjl: 'XXXXX'\n          }, {\n            syzy: '带电',\n            syxz: '例行试验',\n            symc: 'XXXXX',\n            gzdd: 'XXX平台',\n            sysb: '主变压器',\n            sybg: '',\n            tq: '晴',\n            syrq: '2022-01-01',\n            lrr: '张三',\n            syjl: 'XXXXX'\n          }, {\n            syzy: '带电',\n            syxz: '例行试验',\n            symc: 'XXXXX',\n            gzdd: 'XXX平台',\n            sysb: '主变压器',\n            sybg: '',\n            tq: '晴',\n            syrq: '2022-01-01',\n            lrr: '张三',\n            syjl: 'XXXXX'\n          }, {\n            syzy: '带电',\n            syxz: '例行试验',\n            symc: 'XXXXX',\n            gzdd: 'XXX平台',\n            sysb: '主变压器',\n            sybg: '',\n            tq: '晴',\n            syrq: '2022-01-01',\n            lrr: '张三',\n            syjl: 'XXXXX'\n          }\n        ],\n        //设备履历tab页\n        sbllDescTabName: 'qxjl',\n        //轮播图片\n        imgList: [\n          // {\n          //   url: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg'\n          // },\n        ],\n        show: false,\n        //上传图片时的请求头\n      header: {},\n        //设备基本信息\n        jbxxForm: {\n          attachment:[],\n          objId: undefined,\n          gtbh:'',\n          ssbm:'线路分公司'\n        },\n        //设备详情页底部确认取消按钮控制\n        sbCommitDialogCotrol: true,\n        //弹出框tab页\n        activeTabName: 'sbDesc',\n        //变电站展示\n        bdzShowTable: true,\n        //间隔展示\n        jgShowTable: false,\n        //设备展示\n        sbShowTable: false,\n        //设备弹出框\n        dialogFormVisible: false,\n        //变电站添加按钮弹出框\n        bdzDialogFormVisible: false,\n        //间隔添加按钮弹出框\n        jgDialogFormVisible: false,\n        //弹出框表单\n        form: {},\n\n        loading: false,\n        //组织树\n        treeOptions: [{\n          label: '大港油田电力公司',\n          id: '1',\n          pid: undefined,\n          identifier: '0',\n          children: [\n            {\n              label: '110kV',\n              id: '101',\n              pid: '1',\n              identifier: '1',\n              children: [\n                {\n                  label: '110kVXXXX线路',\n                  id: '1010101',\n                  pid: '1',\n                  identifier: '2',\n                  children: []\n                },\n                {\n                  label: '110kVXXXX线路',\n                  id: '1010102',\n                  pid: '1',\n                  identifier: '2',\n                  children: []\n                }\n              ]\n            },\n            {\n              label: '35kV',\n              id: '102',\n              pid: undefined,\n              identifier: '1',\n              children: []\n            },\n            {\n              label: '10kV',\n              id: '103',\n              pid: undefined,\n              identifier: '1',\n              children: []\n            },\n            {\n              label: '6kV',\n              id: '104',\n              pid: undefined,\n              identifier: '1',\n              children: []\n            }\n          ]\n        }],\n        //所有变电站数据集合\n        bdzList: [\n          {\n            ssxl: '110kVXXX线路',\n            dydj: '110kV',\n            sbzt: '在运',\n            gtcz: '角钢材质',\n            fzr: '张三',\n            tyrq: '2022-12-12'\n          }, {\n            ssxl: '110kVXXX线路',\n            dydj: '110kV',\n            sbzt: '在运',\n            gtcz: '角钢材质',\n            fzr: '张三',\n            tyrq: '2022-12-12'\n          }, {\n            ssxl: '110kVXXX线路',\n            dydj: '110kV',\n            sbzt: '在运',\n            gtcz: '角钢材质',\n            fzr: '张三',\n            tyrq: '2022-12-12'\n          }, {\n            ssxl: '110kVXXX线路',\n            dydj: '110kV',\n            sbzt: '在运',\n            gtcz: '角钢材质',\n            fzr: '张三',\n            tyrq: '2022-12-12'\n          }, {\n            ssxl: '110kVXXX线路',\n            dydj: '110kV',\n            sbzt: '在运',\n            gtcz: '角钢材质',\n            fzr: '张三',\n            tyrq: '2022-12-12'\n          }, {\n            ssxl: '110kVXXX线路',\n            dydj: '110kV',\n            sbzt: '在运',\n            gtcz: '角钢材质',\n            fzr: '张三',\n            tyrq: '2022-12-12'\n          }, {\n            ssxl: '110kVXXX线路',\n            dydj: '110kV',\n            sbzt: '在运',\n            gtcz: '角钢材质',\n            fzr: '张三',\n            tyrq: '2022-12-12'\n          }, {\n            ssxl: '110kVXXX线路',\n            dydj: '110kV',\n            sbzt: '在运',\n            gtcz: '角钢材质',\n            fzr: '张三',\n            tyrq: '2022-12-12'\n          }, {\n            ssxl: '110kVXXX线路',\n            dydj: '110kV',\n            sbzt: '在运',\n            gtcz: '角钢材质',\n            fzr: '张三',\n            tyrq: '2022-12-12'\n          }, {\n            ssxl: '110kVXXX线路',\n            dydj: '110kV',\n            sbzt: '在运',\n            gtcz: '角钢材质',\n            fzr: '张三',\n            tyrq: '2022-12-12'\n          }\n        ],\n\n        selectRows: [],\n        //变电站挂接数据\n        newTestData: [],\n\n        //删除是否可用\n        multipleSensor: true,\n        //查询参数\n        queryParams: {\n          pageNum: 1,\n          pageSize: 10,\n          roleKey: '',\n          roleName: '',\n          status: '',\n          lineName: undefined\n        },\n        showSearch: true,\n\n        //查询杆塔参数\n        queryGtParam:{\n          sbzt: '',\n          lineName:'',\n          dydj:'',\n          pageNum: 1,\n          pageSize: 10,\n        },\n\n        rules:{\n          gtbh:[{required:true,message:'请输入杆塔编号',trigger:'blur'}],\n          // ssbm:[{required:true,message:'请输入所属部门',trigger:'blur'}],\n          // yxbz:[{required:true,message:'请输入运行班组',trigger:'blur'}],\n          // sftgbj:[{required:true,message:'请选择是否同杆并架',trigger:'change'}],\n          // dydj:[{required:true,message:'请选择电压等级',trigger:'change'}],\n        }\n\n\n        /*rules:{\n          ssdwbm:[{ required: true, message: '请选择所属单位', trigger: 'change' }],\n          sbdm:[{required: true, message: '请填写设备代码', trigger: 'blur' }],\n          bdzmc:[{required: true, message: '请填写变电站名称', trigger: 'blur' }],\n          dydj:[{required: true, message: '请选择电压等级', trigger: 'blur' }],\n          sbzt:[{required: true, message: '请选择设备状态', trigger: 'blur' }],\n        },*/\n\n\n\n      }\n    },\n    watch: {},\n    created() {\n      //初始化加载时加载所有变电站信息\n      this.treeList()\n      this.getTableList()\n\n    },\n    mounted() {\n      //获取token\n    this.header.token = getToken();\n      this.getVoltageLeVelList()\n      this.filterInfos = { ...this.filterInfoJC }\n      this.tableAndPageInfos = { ...this.tableAndPageInfoJC }\n      this.getData()\n      this.getDataJyz()\n      this.getDataJd()\n      this.getDataJj()\n      this.getDataLx()\n      this.getDataFs()\n      this.getDataHd()\n    },\n    methods: {\n      /**----------------------------------------基础-------------------------------------------*/\n      //获取基础详情\n      getJcDetails(row) {\n        this.jctitle = '基础详情查看'\n        this.jcForm = { ...row }\n        this.isDisabled = true\n        this.JcDialogFormVisible = true\n      },\n      //基础增加弹框\n      JcClick() {\n        this.jctitle = '基础增加'\n        this.jcForm = {}\n        this.isDisabled = false\n        this.JcDialogFormVisible = true\n      },\n      //基础修改\n      getJcUpdate(row) {\n        this.jctitle = '基础修改'\n        this.jcForm = { ...row }\n        this.isDisabled = false\n        this.JcDialogFormVisible = true\n      },\n      //基础删除\n      async JcDelete() {\n        if (this.ids.length < 1) {\n          this.$message.warning('请选择正确的数据！！！')\n          return\n        }\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          //设备异常及事故\n          removeJc(this.ids).then(({ code }) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getData()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n      //基础 提交按钮\n      async JcSubmit() {\n        try {\n          let { code } = await saveOrUpdateJc(this.jcForm)\n          if (code === '0000') {\n            this.$message.success('操作成功')\n            await this.getData()\n          }\n        } catch (e) {\n          console.log(e)\n        }\n        this.JcDialogFormVisible = false\n      },\n\n      /**----------------------------------------绝缘子-------------------------------------------*/\n      //获取绝缘子详情\n      getJyzDetails(row) {\n        this.jyztitle = '绝缘子详情查看'\n        this.jyzForm = { ...row }\n        this.isDisabled = true\n        this.JyzDialogFormVisible = true\n      },\n      //绝缘子增加弹框\n      JyzClick() {\n        this.jyztitle = '绝缘子增加'\n        this.jyzForm = {}\n        this.isDisabled = false\n        this.JyzDialogFormVisible = true\n      },\n      //绝缘子修改\n      getJyzUpdate(row) {\n        this.jyztitle = '绝缘子修改'\n        this.jyzForm = { ...row }\n        this.isDisabled = false\n        this.JyzDialogFormVisible = true\n      },\n      //绝缘子\n      async JyzDelete() {\n        if (this.ids.length < 1) {\n          this.$message.warning('请选择正确的数据！！！')\n          return\n        }\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          removeJyz(this.ids).then(({ code }) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getDataJyz()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n      //绝缘子 提交按钮\n      async JyzSubmit() {\n        try {\n          let { code } = await saveOrUpdateJyz(this.jyzForm)\n          if (code === '0000') {\n            this.$message.success('操作成功')\n            await this.getDataJyz()\n          }\n        } catch (e) {\n          console.log(e)\n        }\n        this.JyzDialogFormVisible = false\n      },\n\n      //导线增加弹框\n      DaoxClick() {\n        this.daoxtitle = '导线增加'\n        this.isDisabled = false\n        this.DaoxDialogFormVisible = true\n      },\n      /**----------------------------------------接地-------------------------------------------*/\n      //获取接地详情\n      getJdDetails(row) {\n        this.jdtitle = '接地详情查看'\n        this.jdForm = { ...row }\n        this.isDisabled = true\n        this.JdDialogFormVisible = true\n      },\n      //接地增加弹框\n      JdClick() {\n        this.jdtitle = '接地增加'\n        this.jdForm = {}\n        this.isDisabled = false\n        this.JdDialogFormVisible = true\n      },\n      //接地修改\n      getJdUpdate(row) {\n        this.jdtitle = '接地修改'\n        this.jdForm = { ...row }\n        this.isDisabled = false\n        this.JdDialogFormVisible = true\n      },\n      //接地删除\n      async JdDelete() {\n        if (this.ids.length < 1) {\n          this.$message.warning('请选择正确的数据！！！')\n          return\n        }\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          removeJd(this.ids).then(({ code }) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getDataJd()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n      //接地 提交按钮\n      async JdSubmit() {\n        try {\n          let { code } = await saveOrUpdateJd(this.jdForm)\n          if (code === '0000') {\n            this.$message.success('操作成功')\n            await this.getDataJd()\n          }\n        } catch (e) {\n          console.log(e)\n        }\n        this.JdDialogFormVisible = false\n      },\n      /**----------------------------------------金具-------------------------------------------*/\n      //获取金具详情\n      getJjDetails(row) {\n        this.jjtitle = '金具详情查看'\n        this.jjForm = { ...row }\n        this.isDisabled = true\n        this.JjDialogFormVisible = true\n      },\n      //金具增加弹框\n      JjClick() {\n        this.jjtitle = '金具增加'\n        this.jjForm = {}\n        this.isDisabled = false\n        this.JjDialogFormVisible = true\n      },\n      //金具修改\n      getJjUpdate(row) {\n        this.jjtitle = '金具修改'\n        this.jjForm = { ...row }\n        this.isDisabled = false\n        this.JjDialogFormVisible = true\n      },\n      //金具删除\n      async JjDelete() {\n        if (this.ids.length < 1) {\n          this.$message.warning('请选择正确的数据！！！')\n          return\n        }\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          removeJj(this.ids).then(({ code }) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getDataJj()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n      //金具 提交按钮\n      async JjSubmit() {\n        try {\n          let { code } = await saveOrUpdateJj(this.jjForm)\n          if (code === '0000') {\n            this.$message.success('操作成功')\n            await this.getDataJj()\n          }\n        } catch (e) {\n          console.log(e)\n        }\n        this.JjDialogFormVisible = false\n      },\n\n      /**----------------------------------------拉线-------------------------------------------*/\n      //获取拉线详情\n      getLxDetails(row) {\n        this.lxtitle = '拉线详情查看'\n        this.lxForm = { ...row }\n        this.isDisabled = true\n        this.LxDialogFormVisible = true\n      },\n      //拉线增加弹框\n      LxClick() {\n        this.lxtitle = '拉线增加'\n        this.lxForm = {}\n        this.isDisabled = false\n        this.LxDialogFormVisible = true\n      },\n      //金具修改\n      getLxUpdate(row) {\n        this.lxtitle = '拉线修改'\n        this.lxForm = { ...row }\n        this.isDisabled = false\n        this.LxDialogFormVisible = true\n      },\n      //金具删除\n      async LxDelete() {\n        if (this.ids.length < 1) {\n          this.$message.warning('请选择正确的数据！！！')\n          return\n        }\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          removeLx(this.ids).then(({ code }) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getDataLx()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n      //金具 提交按钮\n      async LxSubmit() {\n        try {\n          let { code } = await saveOrUpdateLx(this.lxForm)\n          if (code === '0000') {\n            this.$message.success('操作成功')\n            await this.getDataLx()\n          }\n        } catch (e) {\n          console.log(e)\n        }\n        this.LxDialogFormVisible = false\n      },\n\n      /**----------------------------------------附属-------------------------------------------*/\n      //获取附属详情\n      getFsDetails(row) {\n        this.fstitle = '附属详情查看'\n        this.fsForm = { ...row }\n        this.isDisabled = true\n        this.FsDialogFormVisible = true\n      },\n      //附属增加弹框\n      FsClick() {\n        this.fstitle = '附属增加'\n        this.fsForm = {}\n        this.isDisabled = false\n        this.FsDialogFormVisible = true\n      },\n      //附属修改\n      getFsUpdate(row) {\n        this.fstitle = '附属修改'\n        this.fsForm = { ...row }\n        this.isDisabled = false\n        this.FsDialogFormVisible = true\n      },\n      //附属删除\n      async FsDelete() {\n        if (this.ids.length < 1) {\n          this.$message.warning('请选择正确的数据！！！')\n          return\n        }\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          removeFs(this.ids).then(({ code }) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getDataFs()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n      //附属 提交按钮\n      async FsSubmit() {\n        try {\n          let { code } = await saveOrUpdateFs(this.fsForm)\n          if (code === '0000') {\n            this.$message.success('操作成功')\n            await this.getDataFs()\n          }\n        } catch (e) {\n          console.log(e)\n        }\n        this.FsDialogFormVisible = false\n      },\n\n      /**----------------------------------------同杆并架-------------------------------------------*/\n      //同杆并架增加弹框\n      TgbjClick() {\n        this.tgbjtitle = '同杆并架增加'\n        this.isDisabled = false\n        this.TgbjDialogFormVisible = true\n      },\n\n      /**----------------------------------------横担-------------------------------------------*/\n      //获取附属详情\n      getHdDetails(row) {\n        this.hdtitle = '横担详情查看'\n        this.hdForm = { ...row }\n        this.isDisabled = true\n        this.HdDialogFormVisible = true\n      },\n      //横担增加弹框\n      HdClick() {\n        this.hdtitle = '横担增加'\n        this.hdForm = {}\n        this.isDisabled = false\n        this.HdDialogFormVisible = true\n      },\n      //附属修改\n      getHdUpdate(row) {\n        this.hdtitle = '横担修改'\n        this.hdForm = { ...row }\n        this.isDisabled = false\n        this.HdDialogFormVisible = true\n      },\n      //附属删除\n      async HdDelete() {\n        if (this.ids.length < 1) {\n          this.$message.warning('请选择正确的数据！！！')\n          return\n        }\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          removeHd(this.ids).then(({ code }) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getDataHd()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n      //附属 提交按钮\n      async HdSubmit() {\n        try {\n          let { code } = await saveOrUpdateHd(this.hdForm)\n          if (code === '0000') {\n            this.$message.success('操作成功')\n            await this.getDataHd()\n          }\n        } catch (e) {\n          console.log(e)\n        }\n        this.HdDialogFormVisible = false\n      },\n\n      /** 列表查询 */\n      //获取输电杆塔设备  基础  列表查询\n      async getData(params) {\n        try {\n          const param = { ...this.paramJc, ...params }\n          const { data, code } = await getListJc(param)\n          /*console.log(data)*/\n          if (code === '0000') {\n            this.tableAndPageInfoJC.tableData = data.records\n            this.tableAndPageInfoJC.pager.total = data.total\n          }\n        } catch (e) {\n          console.log(e)\n        }\n      },\n      //获取输电杆塔设备  绝缘子  列表查询\n      async getDataJyz(params) {\n        try {\n          const param = { ...this.paramJyz, ...params }\n          const { data, code } = await getListJyz(param)\n          /*console.log(data)*/\n          if (code === '0000') {\n            this.tableAndPageInfoJyz.tableData = data.records\n            this.tableAndPageInfoJyz.pager.total = data.total\n          }\n        } catch (e) {\n          console.log(e)\n        }\n      },\n      //获取输电杆塔设备  接地  列表查询\n      async getDataJd(params) {\n        try {\n          const param = { ...this.paramJd, ...params }\n          const { data, code } = await getListJd(param)\n          /*console.log(data)*/\n          if (code === '0000') {\n            this.tableAndPageInfoJd.tableData = data.records\n            this.tableAndPageInfoJd.pager.total = data.total\n          }\n        } catch (e) {\n          console.log(e)\n        }\n      },\n      //获取输电杆塔设备  金具  列表查询\n      async getDataJj(params) {\n        try {\n          const param = { ...this.paramJj, ...params }\n          const { data, code } = await getListJj(param)\n          /*console.log(data)*/\n          if (code === '0000') {\n            this.tableAndPageInfoJj.tableData = data.records\n            this.tableAndPageInfoJj.pager.total = data.total\n          }\n        } catch (e) {\n          console.log(e)\n        }\n      },\n      //获取输电杆塔设备  拉线  列表查询\n      async getDataLx(params) {\n        try {\n          const param = { ...this.paramLx, ...params }\n          const { data, code } = await getListLx(param)\n          /*console.log(data)*/\n          if (code === '0000') {\n            this.tableAndPageInfoLx.tableData = data.records\n            this.tableAndPageInfoLx.pager.total = data.total\n          }\n        } catch (e) {\n          console.log(e)\n        }\n      },\n      //获取输电杆塔设备  附属  列表查询\n      async getDataFs(params) {\n        try {\n          const param = { ...this.paramFs, ...params }\n          const { data, code } = await getListFs(param)\n          /*console.log(data)*/\n          if (code === '0000') {\n            this.tableAndPageInfoFs.tableData = data.records\n            this.tableAndPageInfoFs.pager.total = data.total\n          }\n        } catch (e) {\n          console.log(e)\n        }\n      },\n\n      //获取输电杆塔设备  横担  列表查询\n      async getDataHd(params) {\n        try {\n          const param = { ...this.paramHd, ...params }\n          const { data, code } = await getListHd(param)\n          if (code === '0000') {\n            this.tableAndPageInfoHd.tableData = data.records\n            this.tableAndPageInfoHd.pager.total = data.total\n          }\n        } catch (e) {\n          console.log(e)\n        }\n      },\n\n      getVoltageLeVelList() {\n        this.filterInfo.fieldList.forEach(item => {\n          if (item.value == 'ssgs') {\n            item.options = [\n              { label: '110kV', value: '110kV' },\n              { label: '35kV', value: '35kV' },\n              { label: '10kV', value: '10kV' },\n              { label: '6kV', value: '6kV' }\n            ]\n          }\n        })\n      },\n\n      addGtInfo: function() {\n        this.$refs['jbxxForm'].validate((valid) => {\n          if (valid) {\n            saveOrUpdategt(this.jbxxForm).then(res =>\n            {\n              if(res.code=='0000'){\n                console.log(res.data);\n                this.uploadData.businessId = res.data.objId;\n                this.submitUpload();\n                this.$message.success(\"操作成功\");\n                this.gtDialogFormVisible = false;\n                this.getTableList();\n              }else{\n                this.$message.warning(\"操作失败！\");\n                 return false;\n              }\n\n            });\n          } else {\n            setTimeout(() => {\n              var isError = document.getElementsByClassName(\"is-error\");\n              if (isError[0].querySelector('input')) {\n                isError[0].querySelector('input').focus();\n              } else if (isError[0].querySelector('textarea')) {\n                isError[0].querySelector('textarea').focus();\n              }\n            }, 1)\n            return false;\n          }\n        });\n      },\n\n      getTableList: function(params) {\n        this.queryGtParam = {...this.queryGtParam, ...params}\n        const param = {...this.queryGtParam, ...params}\n        getListgt(param).then(res => {\n          this.tableAndPageInfo.tableData = res.data.records\n          this.tableAndPageInfo.pager.total = res.data.total\n        })\n      },\n\n      /**\n       * 表格多选框\n       */\n      handleSelectionChange(selection) {\n        this.ids = selection.map(item => item.objId)\n        this.single = selection.length !== 1\n        this.multiple = !selection.length\n      },\n      /**\n       * 删除\n       */\n      deleteInfo() {\n        if (this.ids.length != 0) {\n          this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }).then(() => {\n            gtremove(this.ids).then(res => {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              this.getTableList()\n            })\n          })\n        } else {\n          this.$message({\n            type: 'info',\n            message: '请选择至少一条数据!'\n          })\n        }\n      },\n      updateStatus(row) {\n        this.updateList.sbzt = row.sbzt\n        this.updateList.objId = row.objId,\n          this.dialogVisible = true\n\n      },\n      submitStatus() {\n        this.$confirm('确认将设备状态修改为' + this.updateList.sbzt + '?', '', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(res => {\n          updateStatus(this.updateList).then(res => {\n            if (res.code == '0000') {\n              this.$message.success('设备状态已变更！')\n              this.dialogVisible = false\n              this.getTableList()\n            }\n          })\n        })\n      },\n      clearUpload(){\n        if ( this.$refs.upload){\n          this.$refs.upload.clearFiles()\n        }\n      },\n      updateRow: async function (row) {\n        this.clearUpload();\n        this.resumeQuery.foreignNum = row.ssbm\n        this.resumeQuery.sblx = row.sblx\n        this.getResumList()\n        this.jbxxForm = {...row}\n        this.jbxxForm.attachment = []\n        await this.getFileList();\n        this.gtDialogFormVisible = true\n        this.show = false\n      },\n      detailsInfo: async function (row) {\n        this.clearUpload();\n        this.resumeQuery.foreignNum = row.ssbm\n        this.resumeQuery.sblx = row.sblx\n        this.getResumList()\n        this.jbxxForm = {...row}\n        this.jbxxForm.attachment = []\n        await this.getFileList();\n        this.gtDialogFormVisible = true\n        this.show = true\n      },\n      filterReset() {\n\n      },\n      filterResetTableList(){\n        this.getTableList();\n      },\n      treeList() {\n        getTreeList().then(res => {\n          this.treeOptions = res.data\n        })\n      },\n\n      handleClose() {\n        this.jbxxForm = {\n          attachment: []\n        }\n        this.$nextTick(function () {\n          this.$refs['jbxxForm'].clearValidate();\n        });\n        this.gtDialogFormVisible = false;\n      },\n      getResumList(par) {\n        let params={...par,...this.resumeQuery}\n        getResumDataList(params).then(res => {\n          this.resumPageInfo.tableData = res.data.records\n          this.resumPageInfo.pager.total = res.data.total\n        })\n      },\n\n      /*//list查询\n      getData() {\n        this.tableAndPageInfo.tableData = this.bdzList\n      },*/\n\n      //设备添加按钮\n      sbAddSensorButton() {\n\n      },\n      //杆塔详情展示\n      bdzAddSensorButton() {\n        if (this.jbxxForm.lineId != undefined) {\n          this.imgList = []\n          this.show = false\n          this.gtDialogFormVisible = true\n        } else {\n          this.$message.info('请先选择所属线路再新增杆塔')\n        }\n      },\n      //间隔添加按钮\n      jgAddjgButton() {\n        this.jgDialogFormVisible = true\n      },\n      selectChange(rows) {\n        this.ids = rows.map(item => item.objId)\n        this.single = rows.length !== 1\n        this.multiple = !rows.length\n        this.selectRows = rows\n      },\n      //每页展示数量点击事件\n      handleSizeChange() {\n\n      },\n      //页码改变事件\n      handleCurrentChange() {\n\n      },\n      //树点击事件\n      handleNodeClick(data) {\n        if (data.identifier == '3') {\n          this.jbxxForm.lineName = data.label\n          this.jbxxForm.lineId = data.id\n          // this.filterInfo.data.lineId=data.id;\n          let lineId\n          this.filterInfo.data.lineName=data.label;\n          this.getTableList(this.filterInfo.data);\n        }\n      },\n      beforeUpload (file) {\n        const fileSize = file.size < 1024 * 1024 * 50\n        if (!fileSize) {\n          this.$message.error('上传文件大小不能超过 50MB!')\n        }\n      },\n      submitUpload() {\n        this.$refs.upload.submit();\n      },\n      async getFileList(){\n        let {code,data}=await  getListByBusinessId({businessId:this.jbxxForm.objId})\n        if(code==='0000'){\n          this.jbxxForm.attachment = data;\n          this.imgList= data.map(item=>{\n            let item1={}\n            item1.name=item.fileName\n            item1.url=item.fileUrl\n            return item1\n          })\n        }\n      },\n      async deleteFileById(id){\n        let {code}=await deleteById(id)\n        if(code==='0000'){\n          await this.getFileList();\n          this.$message({\n            type: 'success',\n            message: '文件删除成功!'\n          });\n        }\n      },\n    }\n  }\n</script>\n\n<style lang='scss' scoped>\n  .head-container {\n    margin: 0 auto;\n    width: 98%;\n    height: calc(100vh - 225px);\n    overflow: auto;\n  }\n\n  .box-card {\n    margin-bottom: 2vh !important;\n\n    .el-card__header {\n      background-color: rgb(235, 245, 255) !important;\n    }\n  }\n\n  .box-cardList {\n    height: 70%;\n  }\n\n  .item {\n    width: 200px;\n    float: left;\n  }\n\n  #main_container_dj {\n    height: calc(100vh - 84px);\n  }\n\n  .aside_height {\n    height: 96%;\n  }\n\n  .defect .el-form-item:nth-child(odd) {\n    margin-right: 70px;\n  }\n\n  /*背景颜色调整*/\n  #main_container_dj, #main_container_dj .el-aside {\n    background-color: #b4caf1;\n  }\n\n  /deep/ .qxlr_dialog_insert .el-dialog__header {\n    background-color: #0cc283;\n  }\n\n  /deep/ .pmyBtn {\n    background: #0cc283;\n  }\n\n\n  /*/deep/ .el-input--medium .el-input__inner {*/\n  /*  width: 200px;*/\n  /*}*/\n  .el-select {\n    width: 100%;\n  }\n\n  .el-date-editor {\n    width: 100%;\n  }\n\n  .el-carousel__item h3 {\n    color: #475669;\n    font-size: 14px;\n    opacity: 0.75;\n    line-height: 150px;\n\n  }\n\n  .el-carousel__item:nth-child(2n) {\n    background-color: #99a9bf;\n  }\n\n  .el-carousel__item:nth-child(2n+1) {\n    background-color: #d3dce6;\n  }\n\n  /deep/ .box-card {\n    margin: 0 6px;\n  }\n  //有子节点 且未展开\n  .el-tree ::v-deep .el-icon-caret-right:before {\n    background: url(\"../../../../assets/image/add.png\") no-repeat 0;\n    content: \"\";\n    display: block;\n    width: 16px;\n    height: 16px;\n    font-size: 16px;\n    background-size: 16px;\n  }\n  //有子节点 且已展开\n  .el-tree\n  ::v-deep\n  .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {\n    background: url(\"../../../../assets/image/prep.png\") no-repeat 0;\n    content: \"\";\n    display: block;\n    width: 16px;\n    height: 16px;\n    font-size: 16px;\n    background-size: 16px;\n  }\n  //没有子节点\n  .el-tree ::v-deep .el-tree-node__expand-icon.is-leaf::before {\n    background: transparent;\n    content: \"\";\n    display: block;\n    width: 16px;\n    height: 16px;\n    font-size: 16px;\n    background-size: 16px;\n  }\n</style>\n<style>\n#pic_form .el-form-item__content{\n  margin-left: 0 !important;\n}\n</style>\n"], "sourceRoot": "src/views/dagangOilfield/dwzygl/sdsbgl"}]}