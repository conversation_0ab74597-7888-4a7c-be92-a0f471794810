{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\syzyyxzwh.vue?vue&type=style&index=0&id=f5570280&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\syzyyxzwh.vue", "mtime": 1706897323692}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouYm94LWNhcmQgewogIG1hcmdpbi1ib3R0b206IDE1cHg7CgogIC5lbC1jYXJkX19oZWFkZXIgewogICAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDIzNSwgMjQ1LCAyNTUpICFpbXBvcnRhbnQ7CiAgfQp9CgouaXRlbSB7CiAgd2lkdGg6IDIwMHB4OwogIGhlaWdodDogMTQ4cHg7CiAgZmxvYXQ6IGxlZnQ7Cn0KCi5oZWFkLWNvbnRhaW5lciB7CiAgbWFyZ2luOiAwIGF1dG87CiAgd2lkdGg6IDk4JTsKICBtYXgtaGVpZ2h0OiA3OXZoOwogIG92ZXJmbG93OiBhdXRvOwp9Cg=="}, {"version": 3, "sources": ["syzyyxzwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsRA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "syzyyxzwh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card class=\"aside_height\" shadow=\"never\">\n          <div style=\" overflow: auto;height: 90vh\">\n            <el-col style=\"padding:0\">\n              <el-tree :expand-on-click-node=\"false\"\n                       highlight-current\n                       id=\"tree\"\n                       :data=\"treeOptions\"\n                       :default-expanded-keys=\"['1']\"\n                       @node-click=\"handleNodeClick\"\n                       node-key=\"nodeId\"\n              />\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-white>\n          <el-white class=\"button-group\">\n            <div class=\"button_btn\">\n              <el-button @click=\"addSensorButton\"\n                         type=\"primary\" icon=\"el-icon-plus\"\n              >新增\n              </el-button>\n              <!--<el-button @click=\"handleDelete\"-->\n              <!--           type=\"danger\" icon=\"el-icon-delete\">删除-->\n              <!--</el-button>-->\n            </div>\n            <comp-table :table-and-page-info=\"tableAndPageInfo\" @update:multipleSelection=\"handleSelectionChange\"\n                        height=\"76.8vh\"\n            >\n              <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\"\n                               width=\"160\"\n                               :resizable=\"false\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-button @click=\"updateInfo(scope.row)\" v-show=\"scope.row.createBy == currentUser\" type=\"text\"\n                             size=\"small\" title=\"修改\"  class='el-icon-edit'\n                  >\n                  </el-button>\n                  <el-button @click=\"detailsInfo(scope.row)\" type=\"text\" size=\"small\" title=\"详情\" class=\"el-icon-view\"></el-button>\n                  <el-button @click=\"handleDelete(scope.row)\" v-show=\"scope.row.createBy == currentUser\" type=\"text\"\n                             size=\"small\"  title=\"删除\" class=\"el-icon-delete\"\n                  >\n                  </el-button>\n                </template>\n              </el-table-column>\n            </comp-table>\n          </el-white>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <el-dialog :title=title :visible.sync=\"isShowDetails\" width=\"40%\" v-dialogDrag>\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"试验性质名称：\" prop=\"syxzmc\" label-width=\"140px\">\n              <el-input placeholder=\"请输入试验性质名称\" v-model=\"form.syxzmc\" :disabled=\"isDisabled\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"编码：\" prop=\"bm\">\n              <el-input placeholder=\"请选择编码\" v-model=\"form.bm\" :disabled=\"isDisabled\" style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button v-if=\"title==='新增' || title==='修改'\" type=\"primary\" @click=\"save\">确 认</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getPageDataList, getTreeData, remove, saveOrUpdate } from '@/api/dagangOilfield/bzgl/syzyxz'\n\nexport default {\n  name: 'syzyyxzwh',\n  data() {\n    return {\n      currentUser: this.$store.getters.name,\n      form: {\n        objId: undefined,\n        syzyid: undefined,\n        syxzmc: undefined,\n        bm: undefined\n      },\n      isShowDetails: false,\n      title: '',\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: '试验性质名称', prop: 'syxzmc', minWidth: '150' },\n          { label: '编码', prop: 'bm', minWidth: '150' }\n          // {\n          //   prop: 'operation',\n          //   label: '操作',\n          //   minWidth: '100px',\n          //   style: {display: 'block'},\n          //   //操作列固定再右侧\n          //   fixed: 'right',\n          //   operation: [\n          //     {name: '修改', clickFun: this.updateInfo},\n          //     {name: '详情', clickFun: this.detailsInfo},\n          //\n          //   ]\n          // },\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      //组织树\n      treeOptions: [],\n      //查询参数\n      queryParams: {\n        syzyid: undefined,\n        pageNum: 1,\n        pageSize: 10\n      },\n      isDisabled: false,\n      rules: {\n        syxzmc: [\n          {required: true, message: \"试验性质名称不能为空\", trigger: \"blur\"},\n        ],\n        bm: [\n          {required: true, message: \"编码不能为空\", trigger: \"blur\"},\n        ]\n      },\n    }\n  },\n  watch: {},\n  created() {\n    this.getData()\n    this.getTreeOption()\n  },\n  methods: {\n    //查询树方法\n    getTreeOption() {\n      getTreeData().then(res => {\n        this.treeOptions = res.data\n      })\n    },\n    //树节点点击事件\n    handleNodeClick(data) {\n      if (data.id != undefined) {\n        this.form.syzyid = data.id\n        this.queryParams.syzyid = data.id\n        this.getData()\n      }\n    },\n    //查询列表\n    getData(params) {\n      const param = { ...this.queryParams, ...params }\n      getPageDataList(param).then(res => {\n        console.log(res)\n        this.tableAndPageInfo.tableData = res.data.records\n        this.tableAndPageInfo.pager.total = res.data.total\n      })\n    },\n    //添加按钮\n    addSensorButton() {\n      if (this.form.syzyid === undefined) {\n        this.$message.warning('请在选择专业后新增记录')\n        return\n      }\n      //打开新增弹窗\n      this.isShowDetails = true\n      //使弹出框表单内容可以进行编辑\n      this.isDisabled = false\n      //置空表单\n      this.form.syxzmc = undefined\n      this.form.bm = undefined\n      this.form.objId = undefined\n      //设置弹出框标题\n      this.title = '新增'\n    },\n    //保存按钮\n    async save() {\n      this.$refs['form'].validate((valid) => {\n        if (valid) {\n          try {\n            saveOrUpdate(this.form).then(res=>{\n              if (res.code === '0000') {\n                this.$message.success('操作成功')\n                this.form.syzyid = undefined\n              }\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = 'Y'\n              this.getData()\n              this.isShowDetails = false\n            })\n          } catch (e) {\n            console.log(e)\n          }\n        }else{\n          this.$message.error(\"校验未通过！\");\n          return false;\n        }\n      })\n    },\n    /**\n     * 表格多选框\n     */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.objId)\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false\n      this.form.syzyid = undefined\n    },\n    //编辑按钮\n    updateInfo(row) {\n      this.title = '修改'\n      this.isDisabled = false\n      this.isShowDetails = true\n      this.form = { ...row }\n      this.form.syzyid = undefined\n    },\n    //详情按钮\n    detailsInfo(row) {\n      this.title = '详情'\n      //打开弹窗\n      this.isShowDetails = true\n      //把行数据给弹出框表单\n      this.form = { ...row }\n      //将表单不可编辑\n      this.isDisabled = true\n      this.form.syzyid = undefined\n    },\n    /**\n     * 处理批量删除\n     */\n    handleDelete(row) {\n      this.form = row\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        remove([this.form.objId]).then(res => {\n          if (res.code === '0000') {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            })\n          } else {\n            this.$message({\n              type: 'error',\n              message: '删除失败!'\n            })\n          }\n          this.getData()\n        })\n      })\n\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  max-height: 79vh;\n  overflow: auto;\n}\n</style>\n"]}]}