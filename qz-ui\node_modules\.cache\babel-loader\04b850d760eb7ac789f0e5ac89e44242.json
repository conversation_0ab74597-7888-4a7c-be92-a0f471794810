{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\pdgl\\pdsbgl.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\pdgl\\pdsbgl.vue", "mtime": 1706897324785}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["pdsbgl.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAsuDA;;AACA;;AAMA;;AAIA;;AASA;;AACA;;AAKA;;AAMA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,QADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,KAAA,EAAA,EADA;AAEA,MAAA,QAAA,EAAA,EAFA;AAEA;AACA,MAAA,QAAA,EAAA,EAHA;AAGA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,UAAA,EAAA;AAFA,OAJA;AAQA,MAAA,IAAA,EAAA,EARA;AASA,MAAA,KAAA,EAAA;AACA,QAAA,OAAA,EAAA,mBADA;AAEA,QAAA,GAAA,EAAA,WAFA;AAGA,QAAA,GAAA,EAAA;AAHA,OATA;AAcA,MAAA,OAAA,EAAA,IAdA;AAeA,MAAA,MAAA,EAAA,KAfA;AAgBA,MAAA,OAAA,EAAA,KAhBA;AAiBA;AACA;AACA,MAAA,OAAA,EAAA;AACA,QAAA,UAAA,EAAA,EADA;AAEA;AACA,QAAA,MAAA,EAAA,SAHA;AAIA,QAAA,MAAA,EAAA,SAJA;AAKA,QAAA,MAAA,EAAA,SALA;AAMA,QAAA,KAAA,EAAA,SANA;AAOA,QAAA,IAAA,EAAA,SAPA;AAQA,QAAA,KAAA,EAAA,SARA;AASA,QAAA,GAAA,EAAA,SATA;AAUA,QAAA,MAAA,EAAA,SAVA;AAWA,QAAA,IAAA,EAAA,SAXA;AAYA,QAAA,IAAA,EAAA,SAZA;AAaA,QAAA,KAAA,EAAA,SAbA;AAcA,QAAA,IAAA,EAAA,SAdA;AAeA,QAAA,IAAA,EAAA,SAfA;AAgBA,QAAA,MAAA,EAAA,SAhBA;AAiBA,QAAA,EAAA,EAAA,SAjBA;AAkBA,QAAA,IAAA,EAAA,SAlBA;AAmBA,QAAA,IAAA,EAAA,SAnBA;AAoBA,QAAA,IAAA,EAAA,SApBA;AAqBA,QAAA,KAAA,EAAA,SArBA;AAsBA,QAAA,KAAA,EAAA,SAtBA;AAuBA,QAAA,IAAA,EAAA,SAvBA;AAwBA,QAAA,MAAA,EAAA,SAxBA;AAyBA,QAAA,QAAA,EAAA,SAzBA;AA0BA,QAAA,KAAA,EAAA,SA1BA;AA2BA,QAAA,MAAA,EAAA,SA3BA;AA4BA,QAAA,OAAA,EAAA,SA5BA;AA6BA,QAAA,OAAA,EAAA,SA7BA;AA8BA,QAAA,YAAA,EAAA,SA9BA;AA+BA,QAAA,KAAA,EAAA,SA/BA;AAgCA,QAAA,KAAA,EAAA,SAhCA;AAiCA,QAAA,IAAA,EAAA,SAjCA;AAkCA,QAAA,KAAA,EAAA,SAlCA;AAmCA,QAAA,EAAA,EAAA,SAnCA;AAoCA,QAAA,MAAA,EAAA,SApCA;AAqCA,QAAA,KAAA,EAAA,SArCA;AAsCA,QAAA,GAAA,EAAA,SAtCA;AAuCA,QAAA,OAAA,EAAA,SAvCA;AAwCA,QAAA,OAAA,EAAA,SAxCA;AAyCA,QAAA,OAAA,EAAA,SAzCA;AA0CA,QAAA,KAAA,EAAA,SA1CA;AA2CA,QAAA,OAAA,EAAA,SA3CA;AA4CA,QAAA,OAAA,EAAA,SA5CA;AA6CA,QAAA,OAAA,EAAA,SA7CA;AA8CA,QAAA,OAAA,EAAA,SA9CA;AA+CA,QAAA,OAAA,EAAA,SA/CA;AAgDA,QAAA,IAAA,EAAA,SAhDA;AAiDA,QAAA,IAAA,EAAA,SAjDA;AAkDA,QAAA,KAAA,EAAA,SAlDA;AAmDA,QAAA,OAAA,EAAA,SAnDA;AAoDA,QAAA,EAAA,EAAA;AApDA,OAnBA;AAyEA,MAAA,oBAAA,EAAA,KAzEA;AA0EA,MAAA,aAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,IAAA,EAAA,EADA;AAEA,UAAA,MAAA,EAAA,EAFA;AAGA,UAAA,IAAA,EAAA,EAHA;AAIA,UAAA,EAAA,EAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,CACA;AACA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAPA,EAQA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,KAAA,EAAA,SAHA;AAIA,UAAA,QAAA,EAAA,WAJA;AAKA,UAAA,MAAA,EAAA;AALA,SARA,EAeA;AACA,UAAA,KAAA,EAAA,QADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,OAAA,EAAA,CACA;AACA,YAAA,KAAA,EAAA,GADA;AAEA,YAAA,KAAA,EAAA;AAFA,WADA,EAKA;AACA,YAAA,KAAA,EAAA,GADA;AAEA,YAAA,KAAA,EAAA;AAFA,WALA;AAJA,SAfA,EA8BA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,IAHA;AAIA,UAAA,OAAA,EAAA,CACA;AACA,YAAA,KAAA,EAAA,IADA;AAEA,YAAA,KAAA,EAAA;AAFA,WADA,EAKA;AACA,YAAA,KAAA,EAAA,MADA;AAEA,YAAA,KAAA,EAAA;AAFA,WALA,EASA;AACA,YAAA,KAAA,EAAA,KADA;AAEA,YAAA,KAAA,EAAA;AAFA,WATA,EAaA;AACA,YAAA,KAAA,EAAA,IADA;AAEA,YAAA,KAAA,EAAA;AAFA,WAbA;AAJA,SA9BA,EAqDA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SArDA,EAsDA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAtDA,EAuDA;AACA,UAAA,KAAA,EAAA,OADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,IAHA;AAIA,UAAA,OAAA,EAAA,CACA;AACA,YAAA,KAAA,EAAA,OADA;AAEA,YAAA,KAAA,EAAA;AAFA,WADA,EAKA;AACA,YAAA,KAAA,EAAA,OADA;AAEA,YAAA,KAAA,EAAA;AAFA,WALA,EASA;AACA,YAAA,KAAA,EAAA,KADA;AAEA,YAAA,KAAA,EAAA;AAFA,WATA;AAJA,SAvDA;AAPA,OA1EA;AA6JA,MAAA,QAAA,EAAA,EA7JA;AA8JA,MAAA,SAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA;AAFA,OA9JA;AAkKA,MAAA,iBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA,CAJA;AAKA,UAAA,UAAA,EAAA;AALA,SADA;AAQA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SARA;AAYA,QAAA,SAAA,EAAA,EAZA;AAaA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SARA,EASA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SATA,EAUA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAVA,EAWA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAXA,EAYA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA;AACA;;;;;;;;;;;AAbA;AAbA,OAlKA;AAyMA,MAAA,OAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,EAaA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAbA,CAzMA;AA2NA,MAAA,KAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,CA3NA;AA0OA;AACA,MAAA,oBAAA,EAAA,KA3OA;AA4OA,MAAA,UAAA,EAAA,IA5OA;AA8OA,MAAA,UAAA,EAAA,KA9OA;AA+OA;AACA,MAAA,kBAAA,EAAA,EAhPA;AAiPA,MAAA,WAAA,EAAA,EAjPA;AAkPA,MAAA,SAAA,EAAA,EAlPA;AAmPA;AACA,MAAA,OAAA,EAAA,EApPA;AAqPA,MAAA,SAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA;AAFA,OArPA;AAyPA,MAAA,aAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,KAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA;AAFA,SADA;AAKA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,UAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAJA,EAKA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,KAAA,EAAA,SAHA;AAIA,UAAA,QAAA,EAAA,WAJA;AAKA,UAAA,MAAA,EAAA;AALA,SALA,EAYA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAZA,EAaA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAbA,EAcA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,KAAA,EAAA,SAHA;AAIA,UAAA,QAAA,EAAA,WAJA;AAKA,UAAA,MAAA,EAAA;AALA,SAdA,EAqBA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,IAHA;AAIA,UAAA,OAAA,EAAA,CACA;AACA,YAAA,KAAA,EAAA,IADA;AAEA,YAAA,KAAA,EAAA;AAFA,WADA,EAKA;AACA,YAAA,KAAA,EAAA,MADA;AAEA,YAAA,KAAA,EAAA;AAFA,WALA,EASA;AACA,YAAA,KAAA,EAAA,KADA;AAEA,YAAA,KAAA,EAAA;AAFA,WATA,EAaA;AACA,YAAA,KAAA,EAAA,IADA;AAEA,YAAA,KAAA,EAAA;AAFA,WAbA,EAiBA;AACA,YAAA,KAAA,EAAA,IADA;AAEA,YAAA,KAAA,EAAA;AAFA,WAjBA;AAJA,SArBA,EAgDA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAhDA;AALA,OAzPA;AAiTA,MAAA,iBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA,CAJA;AAKA,UAAA,UAAA,EAAA;AALA,SADA;AAQA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SARA;AAYA,QAAA,SAAA,EAAA,EAZA;AAaA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,KAAA,EAAA,UAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SARA,EASA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SATA,EAUA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA;AACA;;;;;;;;;;;AAXA;AAbA,OAjTA;AAsVA,MAAA,UAAA,EAAA,EAtVA;AAuVA;AACA,MAAA,kBAAA,EAAA,EAxVA;AAyVA;AACA,MAAA,iBAAA,EAAA,EA1VA;AA2VA;AACA,MAAA,uBAAA,EAAA,EA5VA;AA6VA;AACA,MAAA,QAAA,EAAA,EA9VA;AA+VA;AACA,MAAA,OAAA,EAAA,EAhWA;AAiWA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,EAAA,EAAA,EADA;AAEA,QAAA,EAAA,EAAA;AAFA,OAlWA;AAsWA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,UAAA,EAAA;AADA,OAvWA;AA0WA,MAAA,aAAA,EAAA,KA1WA;AA2WA;AACA,MAAA,UAAA,EAAA,KA5WA;AA6WA;AACA,MAAA,MAAA,EAAA,EA9WA;AA+WA;AACA,MAAA,aAAA,EAAA,KAhXA;AAiXA;AACA,MAAA,UAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,EAaA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAbA,EAiBA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAjBA,CAlXA;AAwYA;AACA,MAAA,YAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,CAzYA;AAwZA;AACA,MAAA,UAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,CAzZA;AAoaA;AACA,MAAA,QAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,EAaA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAbA,CAraA;AAwbA;AACA,MAAA,QAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,EAaA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAbA,CAzbA;AA2cA;AACA,MAAA,QAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,CA5cA;AAudA;AACA,MAAA,QAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,CAxdA;AAkeA;AACA,MAAA,QAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,CAneA;AAifA;AACA,MAAA,QAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,CAlfA;AA4fA;AACA,MAAA,QAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,CA7fA;AAugBA;AACA,MAAA,QAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,EAaA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAbA,CAxgBA;AA0hBA;AACA,MAAA,SAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,KADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,IADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,CA3hBA;AAqiBA;AACA,MAAA,QAAA,EAAA,CACA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,KAAA,EAAA;AAFA,OADA,EAKA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,KAAA,EAAA;AAFA,OALA,EASA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,KAAA,EAAA;AAFA,OATA,EAaA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAbA,EAiBA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAjBA,EAqBA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,KAAA,EAAA;AAFA,OArBA,EAyBA;AACA,QAAA,KAAA,EAAA,GADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAzBA,CAtiBA;AAokBA,MAAA,KAAA,EAAA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;;AAIA;AACA;AACA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CANA;AAOA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAPA;AAQA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CARA;AASA,QAAA,EAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CATA;AAUA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AAVA,OApkBA;AAglBA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,QADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,SAHA;AAIA,UAAA,QAAA,EAAA,IAJA;AAKA,UAAA,OAAA,EAAA;AALA,SADA,EAQA;AACA,UAAA,KAAA,EAAA,UADA;AAEA,UAAA,IAAA,EAAA,OAFA;AAGA,UAAA,KAAA,EAAA,UAHA;AAIA,UAAA,QAAA,EAAA,IAJA;AAKA,UAAA,OAAA,EAAA;AALA,SARA,EAeA;AACA,UAAA,KAAA,EAAA,SADA;AAEA,UAAA,IAAA,EAAA,OAFA;AAGA,UAAA,KAAA,EAAA,SAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SAfA,EAqBA;AACA,UAAA,KAAA,EAAA,WADA;AAEA,UAAA,IAAA,EAAA,OAFA;AAGA,UAAA,KAAA,EAAA,WAHA;AAIA,UAAA,OAAA,EAAA;AAJA,SArBA,EA2BA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SA3BA,EA4BA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SA5BA,EA6BA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,QAHA;AAIA,UAAA,QAAA,EAAA,IAJA;AAKA,UAAA,OAAA,EAAA,CACA;AACA,YAAA,KAAA,EAAA,IADA;AAEA,YAAA,KAAA,EAAA;AAFA,WADA,EAKA;AACA,YAAA,KAAA,EAAA,MADA;AAEA,YAAA,KAAA,EAAA;AAFA,WALA,EASA;AACA,YAAA,KAAA,EAAA,KADA;AAEA,YAAA,KAAA,EAAA;AAFA,WATA,EAaA;AACA,YAAA,KAAA,EAAA,IADA;AAEA,YAAA,KAAA,EAAA;AAFA,WAbA,EAiBA;AACA,YAAA,KAAA,EAAA,IADA;AAEA,YAAA,KAAA,EAAA;AAFA,WAjBA;AALA,SA7BA,EAyDA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,KAAA,EAAA,SAHA;AAIA,UAAA,QAAA,EAAA,WAJA;AAKA,UAAA,MAAA,EAAA;AALA,SAzDA,EAgEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAhEA;AAFA,OAjlBA;AAspBA,MAAA,iBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA,CAJA;AAKA,UAAA,UAAA,EAAA;AALA,SADA;AAQA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SARA;AAYA,QAAA,SAAA,EAAA,EAZA;AAaA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,KAAA,EAAA,UAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,SAAA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,WAAA;AAAA,UAAA,KAAA,EAAA,WAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SARA,EASA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA;AACA;;;;;;;;;;;AAVA;AAbA,OAtpBA;AA0rBA;AACA,MAAA,aAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,YAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA;AAZA,OA3rBA;AAgtBA,MAAA,MAAA,EAAA;AACA,QAAA,EAAA,EAAA,EADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA,EAJA;AAKA,QAAA,OAAA,EAAA,EALA;AAMA,QAAA,IAAA,EAAA,EANA;AAOA,QAAA,IAAA,EAAA,EAPA;AAQA,QAAA,EAAA,EAAA,EARA;AASA,QAAA,MAAA,EAAA,EATA;AAUA,QAAA,QAAA,EAAA,EAVA;AAWA,QAAA,OAAA,EAAA,EAXA;AAYA,QAAA,SAAA,EAAA,EAZA;AAaA,QAAA,EAAA,EAAA,EAbA;AAcA,QAAA,IAAA,EAAA;AAdA,OAhtBA;AAiuBA;AACA,MAAA,QAAA,EAAA,EAluBA;AAouBA;AACA,MAAA,YAAA,EAAA;AACA;;;;;;;;;AADA,OAruBA;AAgvBA;AACA,MAAA,YAAA,EAAA;AACA;;;;;;;;;;;;AADA,OAjvBA;AA+vBA;AACA,MAAA,eAAA,EAAA,MAhwBA;AAkwBA;AACA,MAAA,OAAA,EAAA,EAnwBA;AAowBA;AACA,MAAA,QAAA,EAAA,EArwBA;AAswBA;AACA,MAAA,aAAA,EAAA,QAvwBA;AAwwBA;AACA,MAAA,iBAAA,EAAA,KAzwBA;AA0wBA;AACA,MAAA,OAAA,EAAA,KA3wBA;AA4wBA;AACA,MAAA,0BAAA,EAAA,EA7wBA;AA8wBA;AACA,MAAA,UAAA,EAAA,EA/wBA;AAgxBA;AACA,MAAA,WAAA,EAAA,EAjxBA;AAmxBA;AACA,MAAA,WAAA,EAAA,EApxBA;AAqxBA,MAAA,aAAA,EAAA,EArxBA;AAsxBA,MAAA,UAAA,EAAA;AACA,QAAA,MAAA,EAAA;AADA,OAtxBA;AAyxBA,MAAA,QAAA,EAAA,EAzxBA;AA0xBA,MAAA,OAAA,EAAA,EA1xBA;AA0xBA;AACA,MAAA,SAAA,EAAA,EA3xBA,CA2xBA;;AA3xBA,KAAA;AA6xBA,GAhyBA;AAiyBA,EAAA,KAAA,EAAA,EAjyBA;AAkyBA,EAAA,OAlyBA,qBAkyBA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBACA,KAAA,CAAA,WAAA,EADA;;AAAA;AAEA;AACA,cAAA,KAAA,CAAA,WAAA,GAAA,KAAA,CAAA,OAAA,CAHA,CAIA;;AAJA;AAAA,qBAKA,KAAA,CAAA,UAAA,EALA;;AAAA;AAAA;AAAA,qBAMA,KAAA,CAAA,YAAA,EANA;;AAAA;AAAA;AAAA,qBAOA,KAAA,CAAA,UAAA,EAPA;;AAAA;AAOA;AACA,cAAA,KAAA,CAAA,cAAA;;AACA,cAAA,KAAA,CAAA,IAAA;;AACA,cAAA,KAAA,CAAA,qBAAA;;AACA,2CAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA;AACA,gBAAA,KAAA,CAAA,UAAA,CAAA,SAAA,CAAA,CAAA,EAAA,OAAA,GAAA,GAAA,CAAA,IAAA;AACA,eAHA;;AAXA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA,GAjzBA;AAkzBA,EAAA,OAlzBA,qBAkzBA;AACA;AACA,SAAA,MAAA,CAAA,KAAA,GAAA,qBAAA;AACA,GArzBA;AAszBA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,UAFA,wBAEA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,MAAA,CAAA,QAAA,EADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,KAJA;AAKA;AACA,IAAA,QANA,sBAMA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,2BAAA,eAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,oBAAA,MAAA,CAAA,WAAA,CAAA,IAAA,CAAA;AAAA,sBAAA,KAAA,EAAA,IAAA,CAAA,KAAA;AAAA,sBAAA,KAAA,EAAA,IAAA,CAAA;AAAA,qBAAA;AACA,mBAFA;AAGA,iBAJA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,KAZA;AAaA,IAAA,cAbA,4BAaA;AACA,cAAA,KAAA,QAAA,CAAA,UAAA;AACA,aAAA,GAAA;AAAA;AACA,eAAA,QAAA,CAAA,KAAA,QAAA;AACA;;AACA,aAAA,GAAA;AAAA;AACA,eAAA,UAAA,CAAA,KAAA,QAAA;AACA;AANA;AAQA,KAtBA;AAuBA,IAAA,YAvBA,wBAuBA,IAvBA,EAuBA;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,IAAA,GAAA,OAAA,IAAA,GAAA,EAAA;;AACA,UAAA,CAAA,QAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,kBAAA;AACA;AACA,KA5BA;AA6BA,IAAA,cA7BA,0BA6BA,EA7BA,EA6BA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,sBAAA,EAAA,CADA;;AAAA;AAAA;AACA,gBAAA,IADA,qBACA,IADA;;AAAA,sBAEA,IAAA,KAAA,MAFA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAGA,MAAA,CAAA,WAAA,EAHA;;AAAA;AAIA,gBAAA,MAAA,CAAA,QAAA,CAAA;AACA,kBAAA,IAAA,EAAA,SADA;AAEA,kBAAA,OAAA,EAAA;AAFA,iBAAA;;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,KAtCA;AAuCA,IAAA,YAvCA,0BAuCA;AACA,WAAA,KAAA,CAAA,MAAA,CAAA,MAAA;AACA,KAzCA;AA0CA,IAAA,WA1CA,yBA0CA;AACA,UAAA,KAAA,KAAA,CAAA,MAAA,EAAA;AACA,aAAA,KAAA,CAAA,MAAA,CAAA,UAAA;AACA;AACA,KA9CA;AA+CA,IAAA,WA/CA,yBA+CA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,+BAAA;AACA,kBAAA,UAAA,EAAA,MAAA,CAAA,OAAA,CAAA;AADA,iBAAA,CADA;;AAAA;AAAA;AACA,gBAAA,IADA,yBACA,IADA;AACA,gBAAA,IADA,yBACA,IADA;;AAIA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,OAAA,CAAA,UAAA,GAAA,IAAA;AACA,kBAAA,MAAA,CAAA,OAAA,GAAA,IAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,wBAAA,KAAA,GAAA,EAAA;AACA,oBAAA,KAAA,CAAA,IAAA,GAAA,IAAA,CAAA,QAAA;AACA,oBAAA,KAAA,CAAA,GAAA,GAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA,QAAA,GAAA,IAAA,CAAA,OAAA;AACA,2BAAA,KAAA;AACA,mBALA,CAAA;AAMA;;AAZA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA,KA5DA;AA6DA,IAAA,WA7DA,yBA6DA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,+BAAA;AAAA,kBAAA,QAAA,EAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,MAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,KAjEA;AAkEA;AACA,IAAA,OAnEA,mBAmEA,MAnEA,EAmEA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAAA,MAAA,CAAA,OAAA,EAAA;AACA,kBAAA,MAAA,CAAA,UAAA,CAAA,MAAA;AACA;;AACA,oBAAA,MAAA,CAAA,OAAA,EAAA;AACA,kBAAA,MAAA,CAAA,UAAA,CAAA,MAAA;AACA;;AACA,oBAAA,MAAA,CAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,SAAA,CAAA,MAAA;AACA;;AATA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,KA7EA;AA8EA;AACA,IAAA,eA/EA,6BA+EA;AACA,UAAA,KAAA,OAAA,EAAA;AACA,aAAA,OAAA,GAAA;AAAA,UAAA,UAAA,EAAA;AAAA,SAAA;AACA,aAAA,WAAA;AACA,aAAA,OAAA,GAAA,EAAA;AACA,aAAA,UAAA,GAAA,KAAA;AACA,aAAA,oBAAA,GAAA,IAAA;AACA;;AACA,UAAA,KAAA,OAAA,EAAA;AACA,aAAA,OAAA,CAAA,IAAA,GAAA,KAAA,SAAA,CADA,CACA;;AACA,aAAA,UAAA,GAAA,KAAA;AACA,aAAA,UAAA,GAAA,IAAA;AACA,aAAA,oBAAA,GAAA,IAAA;AACA,aAAA,UAAA,GAAA,KAAA,CALA,CAKA;AACA;;AACA,UAAA,KAAA,MAAA,EAAA;AACA,aAAA,OAAA,GAAA,MAAA;AACA,aAAA,iBAAA,GAAA,IAAA;AACA,aAAA,QAAA,CAAA,IAAA,GAAA,KAAA,IAAA;AACA,aAAA,kBAAA;AACA,aAAA,QAAA,CAAA,KAAA,GAAA,KAAA,KAAA;AACA,aAAA,UAAA,GAAA,KAAA;AACA;AACA,KAtGA;;AAuGA;AACA;AACA,IAAA,wBAzGA,oCAyGA,SAzGA,EAyGA;AACA,WAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,KAAA;AAAA,OAAA,CAAA;AACA,WAAA,MAAA,GAAA,SAAA,CAAA,MAAA,KAAA,CAAA;AACA,WAAA,QAAA,GAAA,CAAA,SAAA,CAAA,MAAA;AACA,KA7GA;AA8GA;AACA,IAAA,UA/GA,sBA+GA,MA/GA,EA+GA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,SAAA,+DAAA,MAAA,CAAA,SAAA,GAAA,MAAA;AACA,gBAAA,KAFA,GAEA,MAAA,CAAA,SAFA;AAAA;AAAA,uBAGA,uBAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,MAAA,CAAA,OAAA,GAAA,IAAA;AACA,kBAAA,MAAA,CAAA,OAAA,GAAA,MAAA,CAAA,MAAA,GAAA,KAAA;AACA,kBAAA,MAAA,CAAA,iBAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;;AACA,kBAAA,MAAA,CAAA,iBAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,oBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,OAAA,EAAA;AACA,0BAAA,IAAA,CAAA,IAAA,IAAA,OAAA,CAAA,KAAA,EAAA;AACA,wBAAA,IAAA,CAAA,MAAA,GAAA,OAAA,CAAA,KAAA;AACA;AACA,qBAJA;AAKA,mBANA;;AAOA,kBAAA,MAAA,CAAA,iBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,iBAZA,CAHA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA,KA/HA;AAgIA;AACA,IAAA,YAjIA,0BAiIA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MADA,GACA;AACA,kBAAA,EAAA,EAAA,MADA;AAEA,kBAAA,EAAA,EAAA,MAAA,CAAA,OAAA,CAAA;AAFA,iBADA;;AAKA,gBAAA,MAAA,CAAA,KAAA,CAAA,SAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,sBAAA,KAAA,EAAA;AACA,uCAAA,MAAA,CAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,0BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA;AACA,+CAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,8BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA,CACA;AACA,yBAHA;AAIA,wBAAA,MAAA,CAAA,UAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;;AACA,wBAAA,MAAA,CAAA,YAAA;;AACA,wBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,cAAA;;AACA,wBAAA,MAAA,CAAA,oBAAA,GAAA,KAAA;;AACA,wBAAA,MAAA,CAAA,UAAA;AACA;AACA,qBAbA;AAcA,mBAfA,MAeA;AACA,oBAAA,UAAA,CAAA,YAAA;AACA,0BAAA,OAAA,GAAA,QAAA,CAAA,sBAAA,CAAA,UAAA,CAAA;;AACA,0BAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,OAAA,CAAA,EAAA;AACA,wBAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,OAAA,EAAA,KAAA;AACA,uBAFA,MAEA,IAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,UAAA,CAAA,EAAA;AACA,wBAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,UAAA,EAAA,KAAA;AACA;AACA,qBAPA,EAOA,CAPA,CAAA;AAQA,2BAAA,KAAA;AACA;AACA,iBA3BA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiCA,KAlKA;AAmKA;AACA,IAAA,WApKA,yBAoKA;AACA,UAAA,KAAA,OAAA,EAAA;AACA,aAAA,OAAA,GAAA;AACA,UAAA,UAAA,EAAA;AADA,SAAA,CADA,CAIA;AACA;AACA;AACA;;AACA,aAAA,oBAAA,GAAA,KAAA;AACA;;AACA,UAAA,KAAA,OAAA,EAAA;AACA,aAAA,OAAA,GAAA,EAAA,CADA,CAEA;AACA;AACA;AACA;;AACA,aAAA,oBAAA,GAAA,KAAA;AACA;AACA,KAvLA;AAwLA;AACA,IAAA,YAzLA,wBAyLA,GAzLA,EAyLA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,WAAA;;AACA,gBAAA,OAAA,CAAA,OAAA,mCAAA,GAAA;AACA,gBAAA,OAAA,CAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AAHA;AAAA,uBAIA,OAAA,CAAA,WAAA,EAJA;;AAAA;AAKA,gBAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,gBAAA,OAAA,CAAA,oBAAA,GAAA,IAAA;;AANA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,KAhMA;AAiMA;AACA,IAAA,QAlMA,oBAkMA,GAlMA,EAkMA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,WAAA;;AACA,gBAAA,OAAA,CAAA,OAAA,mCAAA,GAAA;AACA,gBAAA,OAAA,CAAA,OAAA,CAAA,UAAA,GAAA,EAAA;AAHA;AAAA,uBAIA,OAAA,CAAA,WAAA,EAJA;;AAAA;AAKA,gBAAA,OAAA,CAAA,UAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,oBAAA,GAAA,IAAA;;AANA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,KAzMA;;AA0MA;;AAEA;AACA,IAAA,YA7MA,0BA6MA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,2BAAA,OAAA,CADA;;AAAA;AAAA;AACA,gBAAA,KADA,yBACA,IADA;AAEA,gBAAA,OAAA,CAAA,SAAA,GAAA,KAAA;;AAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,KAhNA;AAiNA,IAAA,UAjNA,sBAiNA,KAjNA,EAiNA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,SAAA,+DAAA,OAAA,CAAA,SAAA,GAAA,KAAA;AACA,gBAAA,GAFA,GAEA,OAAA,CAAA,SAFA;AAAA;AAAA;AAAA,uBAIA,0BAAA,GAAA,CAJA;;AAAA;AAAA;AAIA,gBAAA,IAJA,yBAIA,IAJA;AAIA,gBAAA,IAJA,yBAIA,IAJA;;AAKA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,OAAA,GAAA,IAAA;AACA,kBAAA,OAAA,CAAA,OAAA,GAAA,OAAA,CAAA,MAAA,GAAA,KAAA;AACA,kBAAA,OAAA,CAAA,iBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,OAAA,CAAA,iBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAJA,CAKA;AACA;;AAXA;AAAA;;AAAA;AAAA;AAAA;AAaA,gBAAA,OAAA,CAAA,GAAA;;AAbA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA,KAhOA;AAiOA;AACA,IAAA,SAlOA,qBAkOA,GAlOA,EAkOA;AAAA;;AACA,UAAA,GAAA,CAAA,IAAA,KAAA,KAAA,EAAA;AACA,4BAAA;AAAA,UAAA,KAAA,EAAA,GAAA,CAAA;AAAA,SAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,OAAA,CAAA,oBAAA,GAAA,IAAA;AACA,YAAA,OAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;AACA,YAAA,OAAA,CAAA,OAAA,CAAA,IAAA,GAAA,OAAA,CAAA,SAAA,CAHA,CAGA;;AACA,YAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,YAAA,OAAA,CAAA,UAAA,GAAA,IAAA;AACA,YAAA,OAAA,CAAA,UAAA,GAAA,KAAA,CANA,CAMA;AACA,WAPA,MAOA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,SAXA;AAYA,OAbA,MAaA,IAAA,GAAA,CAAA,IAAA,KAAA,KAAA,EAAA;AACA,kCAAA;AAAA,UAAA,EAAA,EAAA,GAAA,CAAA;AAAA,SAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,OAAA,CAAA,OAAA,GAAA,MAAA;AACA,YAAA,OAAA,CAAA,iBAAA,GAAA,IAAA;AACA,YAAA,OAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,YAAA,OAAA,CAAA,UAAA,GAAA,KAAA;;AACA,YAAA,OAAA,CAAA,kBAAA;;AACA,YAAA,OAAA,CAAA,mBAAA,CAAA,GAAA;AACA,WAPA,MAOA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,SAXA;AAYA;AACA,KA9PA;AA+PA;AACA,IAAA,UAhQA,sBAgQA,GAhQA,EAgQA;AAAA;;AACA,UAAA,GAAA,CAAA,IAAA,KAAA,KAAA,EAAA;AACA,4BAAA;AAAA,UAAA,KAAA,EAAA,GAAA,CAAA;AAAA,SAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,OAAA,CAAA,oBAAA,GAAA,IAAA;AACA,YAAA,OAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;AACA,YAAA,OAAA,CAAA,OAAA,CAAA,IAAA,GAAA,OAAA,CAAA,SAAA,CAHA,CAGA;;AACA,YAAA,OAAA,CAAA,UAAA,GAAA,IAAA;AACA,YAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,YAAA,OAAA,CAAA,UAAA,GAAA,IAAA;AACA,WAPA,MAOA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,SAXA;AAYA,OAbA,MAaA,IAAA,GAAA,CAAA,IAAA,KAAA,KAAA,EAAA;AACA,kCAAA;AAAA,UAAA,EAAA,EAAA,GAAA,CAAA;AAAA,SAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,OAAA,CAAA,OAAA,GAAA,QAAA;AACA,YAAA,OAAA,CAAA,iBAAA,GAAA,IAAA;AACA,YAAA,OAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,YAAA,OAAA,CAAA,UAAA,GAAA,IAAA;;AACA,YAAA,OAAA,CAAA,kBAAA;;AACA,YAAA,OAAA,CAAA,mBAAA,CAAA,GAAA;AACA,WAPA,MAOA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,SAXA;AAYA;AACA,KA5RA;AA6RA;AACA,IAAA,IA9RA,kBA8RA;AAAA;;AACA,WAAA,KAAA,CAAA,OAAA,CAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,UAAA,OAAA,CAAA,OAAA,CAAA,IAAA,GAAA,OAAA,CAAA,OAAA;AACA,2BAAA,OAAA,CAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,cAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,cAAA,OAAA,CAAA,oBAAA,GAAA,KAAA;;AACA,cAAA,OAAA,CAAA,UAAA;AACA,aAJA,MAIA;AACA,cAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,WARA;AASA,SAXA,MAWA;AACA,UAAA,UAAA,CAAA,YAAA;AACA,gBAAA,OAAA,GAAA,QAAA,CAAA,sBAAA,CAAA,UAAA,CAAA;;AACA,gBAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,OAAA,CAAA,EAAA;AACA,cAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,OAAA,EAAA,KAAA;AACA,aAFA,MAEA,IAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,UAAA,CAAA,EAAA;AACA,cAAA,OAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,UAAA,EAAA,KAAA;AACA;AACA,WAPA,EAOA,CAPA,CAAA;AAQA,iBAAA,KAAA;AACA;AACA,OAvBA;AAwBA,KAvTA;;AAyTA;AAEA;AACA,IAAA,SA5TA,qBA4TA,MA5TA,EA4TA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,MAAA,+DAAA,OAAA,CAAA,MAAA,GAAA,MAAA;AACA,gBAAA,KAFA,GAEA,OAAA,CAAA,MAFA,EAGA;;AACA,oBAAA,KAAA,CAAA,OAAA,IAAA,KAAA,CAAA,OAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,kBAAA,KAAA,CAAA,WAAA,GAAA,OAAA,CAAA,aAAA,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;AACA,kBAAA,KAAA,CAAA,SAAA,GAAA,OAAA,CAAA,aAAA,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA;AACA;;AAPA;AAAA,uBAQA,uBAAA,KAAA,CARA;;AAAA;AAAA;AAQA,gBAAA,IARA,kBAQA,IARA;AAQA,gBAAA,IARA,kBAQA,IARA;;AASA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,MAAA,GAAA,IAAA;AACA,kBAAA,OAAA,CAAA,OAAA,GAAA,OAAA,CAAA,OAAA,GAAA,KAAA;AACA,kBAAA,OAAA,CAAA,iBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,OAAA,CAAA,iBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAJA,CAKA;AACA;;AAfA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA,KA5UA;AA6UA;AACA,IAAA,qBA9UA,mCA8UA;AAAA;;AACA,wCAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,kBAAA,GAAA,GAAA,CAAA,IAAA,CADA,CAEA;;AACA,QAAA,OAAA,CAAA,UAAA,CAAA,SAAA,CAAA,CAAA,EAAA,OAAA,GAAA,OAAA,CAAA,kBAAA;AACA,OAJA;AAKA,KApVA;AAqVA;AACA,IAAA,kBAtVA,gCAsVA;AAAA;;AACA,mCAAA;AAAA,QAAA,IAAA,EAAA,KAAA,QAAA,CAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,iBAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KA1VA;AA2VA;AACA,IAAA,qBA5VA,mCA4VA;AACA;AACA,WAAA,IAAA,CAAA,KAAA,QAAA,EAAA,OAAA,EAAA,EAAA;AACA,WAAA,kBAAA;AACA,KAhWA;AAiWA;AACA,IAAA,aAlWA,yBAkWA,CAlWA,EAkWA;AACA,UAAA,IAAA,GAAA,CAAA,CAAA,WAAA,EAAA;AACA,UAAA,KAAA,GACA,CAAA,CAAA,QAAA,KAAA,CAAA,GAAA,OAAA,CAAA,CAAA,QAAA,KAAA,CAAA,CAAA,GAAA,MAAA,CAAA,CAAA,QAAA,KAAA,CAAA,CADA;AAEA,UAAA,GAAA,GAAA,CAAA,CAAA,OAAA,KAAA,EAAA,GAAA,MAAA,CAAA,CAAA,OAAA,EAAA,GAAA,KAAA,CAAA,CAAA,OAAA,EAAA;AACA,aAAA,IAAA,GAAA,GAAA,GAAA,KAAA,GAAA,GAAA,GAAA,GAAA;AACA,KAxWA;;AA0WA;AACA;AACA,IAAA,iBA5WA,+BA4WA;AACA,WAAA,OAAA,GAAA,MAAA;AACA,WAAA,iBAAA,GAAA,IAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,KAhXA;AAiXA;AACA,IAAA,aAlXA,yBAkXA,GAlXA,EAkXA;AAAA;;AACA,gCAAA;AAAA,QAAA,EAAA,EAAA,GAAA,CAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,OAAA,CAAA,OAAA,GAAA,MAAA;AACA,UAAA,OAAA,CAAA,iBAAA,GAAA,IAAA;AACA,UAAA,OAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,UAAA,OAAA,CAAA,UAAA,GAAA,KAAA;;AACA,UAAA,OAAA,CAAA,kBAAA;;AACA,UAAA,OAAA,CAAA,mBAAA,CAAA,GAAA;AACA,SAPA,MAOA;AACA,UAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,OAXA;AAYA,KA/XA;AAgYA;AACA,IAAA,aAjYA,yBAiYA,GAjYA,EAiYA;AAAA;;AACA,gCAAA;AAAA,QAAA,EAAA,EAAA,GAAA,CAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,OAAA,CAAA,OAAA,GAAA,QAAA;AACA,UAAA,OAAA,CAAA,iBAAA,GAAA,IAAA;AACA,UAAA,OAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA,UAAA,OAAA,CAAA,UAAA,GAAA,IAAA;;AACA,UAAA,OAAA,CAAA,kBAAA;;AACA,UAAA,OAAA,CAAA,mBAAA,CAAA,GAAA;AACA,SAPA,MAOA;AACA,UAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,OAXA;AAYA,KA9YA;;AAgZA;AACA;AACA;AACA,IAAA,SAnZA,qBAmZA,EAnZA,EAmZA;AAAA;;AACA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,MAAA,GAAA,CAAA,IAAA,CAAA,EAAA;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAKA,IALA,CAKA,YAAA;AACA,4BAAA,GAAA,EAAA,IAAA,CAAA,gBAAA;AAAA,cAAA,IAAA,QAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,OAAA,CAAA,UAAA;AACA,WANA,MAMA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAIA;AACA,SAbA;AAcA,OApBA,EAqBA,KArBA,CAqBA,YAAA;AACA,QAAA,OAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OA1BA,EAJA,CA+BA;AACA;AACA;AACA;AACA;AACA;AACA,KAxbA;AAybA;AACA,IAAA,SA1bA,qBA0bA,EA1bA,EA0bA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,GADA,GACA,EADA;AAEA,gBAAA,GAAA,CAAA,IAAA,CAAA,EAAA,EAFA,CAGA;;AACA,gBAAA,OAAA,CAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAKA,IALA,CAKA,YAAA;AACA,wCAAA,GAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,wBAAA,IAAA,SAAA,IAAA;;AACA,wBAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,OAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,SADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;;AAIA,sBAAA,OAAA,CAAA,UAAA;AACA,qBANA,MAMA;AACA,sBAAA,OAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,OADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;AAIA;AACA,mBAbA;AAcA,iBApBA,EAqBA,KArBA,CAqBA,YAAA;AACA,kBAAA,OAAA,CAAA,QAAA,CAAA;AACA,oBAAA,IAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;AAIA,iBA1BA,EAJA,CA+BA;;;AA/BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgCA,KA1dA;AA2dA;AACA,IAAA,QA5dA,oBA4dA,EA5dA,EA4dA;AAAA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAA,GAAA,GAAA,EAAA;AACA,MAAA,GAAA,CAAA,IAAA,CAAA,EAAA;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,8BAAA,GAAA,EACA,IADA,CACA,iBAAA;AAAA,cAAA,IAAA,SAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,OAAA,CAAA,OAAA;AACA,WANA,MAMA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAIA;AACA,SAdA,EAeA,KAfA,CAeA,YAAA;AACA,UAAA,OAAA,CAAA,QAAA,CAAA;AACA,YAAA,IAAA,EAAA,MADA;AAEA,YAAA,OAAA,EAAA;AAFA,WAAA;AAIA,SApBA;;AAqBA,QAAA,OAAA,CAAA,SAAA;AACA,OA3BA;AA4BA,KAlgBA;AAmgBA,IAAA,SAngBA,qBAmgBA,GAngBA,EAmgBA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAAA,OAAA,CAAA,OAAA,EAAA;AACA,kBAAA,OAAA,CAAA,SAAA,CAAA,GAAA;AACA;;AACA,oBAAA,OAAA,CAAA,OAAA,EAAA;AACA,kBAAA,OAAA,CAAA,SAAA,CAAA,GAAA;AACA;;AACA,oBAAA,OAAA,CAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,QAAA,CAAA,GAAA;AACA;;AATA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,KA7gBA;AA8gBA;AACA,IAAA,eA/gBA,2BA+gBA,IA/gBA,EA+gBA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA,sBACA,OAAA,CAAA,QAAA,CAAA,UAAA,KAAA,GADA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAEA,uBAAA;AAAA,kBAAA,KAAA,EAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,OAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA,CAAA;;AACA,kBAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,OAAA,EAAA;AACA,wBAAA,OAAA,CAAA,QAAA,CAAA,IAAA,IAAA,OAAA,CAAA,KAAA,EAAA;AACA,sBAAA,OAAA,CAAA,QAAA,CAAA,MAAA,GAAA,OAAA,CAAA,KAAA;AACA;AACA,mBAJA;AAKA,iBAPA,CAFA;;AAAA;AAAA;AAAA;;AAAA;AAAA,sBAUA,OAAA,CAAA,QAAA,CAAA,UAAA,KAAA,GAVA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAWA,0BAAA;AAAA,kBAAA,KAAA,EAAA;AAAA,iBAAA,CAXA;;AAAA;AAAA;AAWA,gBAAA,IAXA,0BAWA,IAXA;AAWA,gBAAA,IAXA,0BAWA,IAXA;;AAYA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,QAAA,GAAA,IAAA,CAAA,OAAA,CAAA,CAAA,CAAA;;AACA,sBAAA,OAAA,CAAA,QAAA,CAAA,IAAA,KAAA,KAAA,EAAA;AACA,oBAAA,OAAA,CAAA,KAAA,GAAA,OAAA,CAAA,QAAA,CAAA,KAAA;AACA;AACA;;AAjBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmBA,KAliBA;AAmiBA;AACA,IAAA,eApiBA,2BAoiBA,IApiBA,EAoiBA,CApiBA,EAoiBA;AACA,WAAA,QAAA,GAAA,IAAA,CADA,CAEA;;AACA,UAAA,IAAA,CAAA,UAAA,KAAA,GAAA,EAAA;AACA,aAAA,SAAA,GAAA;AACA,UAAA,OAAA,EAAA,CADA;AAEA,UAAA,QAAA,EAAA;AAFA,SAAA;AAIA,aAAA,iBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;AACA,aAAA,UAAA;AACA,OAVA,CAWA;;;AACA,UAAA,IAAA,CAAA,UAAA,KAAA,GAAA,EAAA;AACA;AACA,aAAA,SAAA,GAAA;AACA,UAAA,OAAA,EAAA,CADA;AAEA,UAAA,QAAA,EAAA;AAFA,SAAA;AAIA,aAAA,IAAA,GAAA,IAAA,CAAA,EAAA;AACA,aAAA,OAAA,GAAA,IAAA,CAAA,EAAA;AACA,aAAA,SAAA,GAAA,IAAA,CAAA,KAAA;AACA,aAAA,eAAA,CAAA,IAAA,CAAA,EAAA;AACA,aAAA,SAAA,CAAA,IAAA,GAAA,KAAA,IAAA,CAVA,CAWA;;AACA,aAAA,iBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;AACA,aAAA,UAAA;AACA,OA1BA,CA2BA;;;AACA,UAAA,IAAA,CAAA,UAAA,KAAA,GAAA,EAAA;AACA,aAAA,MAAA,GAAA;AACA,UAAA,OAAA,EAAA,CADA;AAEA,UAAA,QAAA,EAAA;AAFA,SAAA;AAIA,aAAA,MAAA,CAAA,OAAA,GAAA,CAAA,IAAA,CAAA,EAAA,CAAA;AACA,aAAA,MAAA,CAAA,IAAA,GAAA,KAAA,IAAA;AACA,aAAA,MAAA,CAAA,KAAA,GAAA,IAAA,CAAA,EAAA;AACA,aAAA,KAAA,GAAA,EAAA;AACA,aAAA,eAAA,CAAA,IAAA,CAAA,EAAA;AACA,aAAA,iBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;AACA,aAAA,SAAA;AACA;AACA,KA7kBA;AA8kBA;AACA,IAAA,WA/kBA,yBA+kBA,CAAA,CA/kBA;;AAglBA;AACA;AACA,IAAA,YAllBA,wBAklBA,GAllBA,EAklBA;AACA,WAAA,UAAA,CAAA,EAAA,GAAA,GAAA,CAAA,EAAA;AACA,WAAA,UAAA,CAAA,EAAA,GAAA,GAAA,CAAA,EAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,KAtlBA;AAulBA;AACA,IAAA,YAxlBA,0BAwlBA;AAAA;;AACA,WAAA,QAAA,CAAA,eAAA,KAAA,UAAA,CAAA,EAAA,GAAA,GAAA,EAAA,EAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,UAAA,GAAA,EAAA;AACA,oCAAA,OAAA,CAAA,UAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA;;AACA,YAAA,OAAA,CAAA,aAAA,GAAA,KAAA;;AACA,YAAA,OAAA,CAAA,OAAA;AACA;AACA,SANA;AAOA,OAZA;AAaA,KAtmBA;AAumBA;AACA,IAAA,YAxmBA,wBAwmBA,GAxmBA,EAwmBA;AAAA;;AACA,UAAA,MAAA,+DAAA,GAAA,GAAA,KAAA,WAAA,CAAA;AACA,sCAAA,MAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,aAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,OAAA,CAAA,aAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,OAHA;AAIA,KA9mBA;AA+mBA;AACA,IAAA,cAhnBA,4BAgnBA;AAAA;;AACA,+BAAA,KAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KApnBA;AAqnBA;AACA,IAAA,YAtnBA,wBAsnBA,IAtnBA,EAsnBA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KAxnBA;AAynBA;AACA,IAAA,SA1nBA,uBA0nBA;AACA,WAAA,QAAA,GAAA,EAAA;AACA,WAAA,QAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,YAAA;AACA,aAAA,KAAA,CAAA,UAAA,EAAA,aAAA;AACA,OAFA;AAGA,WAAA,iBAAA,GAAA,KAAA;AACA,KAjoBA;AAkoBA,IAAA,uBAloBA,qCAkoBA;AAAA;;AACA,UAAA,SAAA,GAAA;AACA,QAAA,IAAA,EAAA;AADA,OAAA;AAGA,2CAAA,SAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,uBAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KAzoBA;AA0oBA,IAAA,UA1oBA,sBA0oBA,IA1oBA,EA0oBA;AACA,WAAA,UAAA,CAAA,MAAA,GAAA,IAAA;AACA,WAAA,aAAA;AACA,KA7oBA;AA8oBA,IAAA,mBA9oBA,+BA8oBA,GA9oBA,EA8oBA;AACA;AACA,WAAA,QAAA,GAAA,EAAA;AACA,WAAA,UAAA,CAAA,MAAA,GAAA,GAAA,CAAA,IAAA;AACA,WAAA,QAAA,CAAA,MAAA,GAAA,GAAA,CAAA,IAAA;AACA,WAAA,QAAA,CAAA,IAAA,GAAA,GAAA,CAAA,QAAA;AACA,WAAA,aAAA;AACA,KArpBA;AAspBA,IAAA,aAtpBA,2BAspBA;AAAA;;AACA,WAAA,aAAA,GAAA,EAAA;AACA,wCAAA,KAAA,UAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,aAAA,GAAA,GAAA,CAAA,IAAA;;AACA,QAAA,OAAA,CAAA,aAAA;AACA,OAHA;AAIA,KA5pBA;AA8pBA,IAAA,aA9pBA,2BA8pBA;AAAA;;AACA,sCAAA,KAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,IAAA,EAAA,EAAA;AACA,UAAA,OAAA,CAAA,QAAA,mCAAA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AACA;AACA,OAJA;AAKA,KApqBA;AAqqBA,IAAA,MArqBA,oBAqqBA;AAAA;;AACA,UAAA;AACA,aAAA,QAAA,CAAA,cAAA,GAAA,KAAA,QAAA;;AADA,iCAEA,4BAAA,KAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,OAAA,CAAA,iBAAA,GAAA,KAAA;;AACA,YAAA,OAAA,CAAA,OAAA;AACA;AACA,SATA,CAFA;AAAA,YAEA,IAFA,sBAEA,IAFA;AAYA,OAZA,CAYA,OAAA,CAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,CAAA;AACA;AACA,KArrBA;AAsrBA,IAAA,IAtrBA,kBAsrBA;AACA,WAAA,uBAAA;AACA;AAxrBA;AAtzBA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"1\">\n      <el-col :span=\"5\">\n        <el-card shadow=\"never\" style=\"background: #e0f8ed; padding-top: 10px\">\n          <div style=\"overflow: auto; height: 89vh\">\n            <el-col>\n              <el-tree\n                :expand-on-click-node=\"true\"\n                highlight-current\n                ref=\"tree\"\n                id=\"tree\"\n                :data=\"treeOptions\"\n                :default-expanded-keys=\"['0']\"\n                @node-click=\"handleNodeClick\"\n                node-key=\"id\"\n                accordion\n              >\n                <span slot-scope=\"{ node, data }\">\n                  <i :class=\"icons[data.icon]\" />\n                  <span style=\"margin-left: 5px\" :title=\"data.label\">{{\n                    data.label\n                  }}</span>\n                </span>\n              </el-tree>\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--右侧列表-->\n      <el-col :span=\"19\" v-if=\"treeNode.identifier && treeNode.identifier !== '0'\" style=\"height: 160px;\">\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button\n              @click=\"viewHeadDetail\"\n              type=\"primary\"\n            >\n              详情\n            </el-button>\n          </div>\n          <div>\n            <!--     配电室基本信息     -->\n            <el-form\n              :model=\"headForm\"\n              label-width=\"120px\"\n              v-if=\"treeNode.identifier === '1'\"\n            >\n              <el-row :gutter=\"20\">\n                <el-col :span=\"6\">\n                  <el-form-item label=\"所属线路名称:\">\n                    <el-input\n                      v-model=\"headForm.ssxlmc\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"所属线路编号:\">\n                    <el-input\n                      v-model=\"headForm.ssxlbh\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"所属线段名称:\">\n                    <el-input\n                      v-model=\"headForm.ssxdmc\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"配电室名称:\">\n                    <el-input\n                      v-model=\"headForm.pdsmc\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n              <el-row :gutter=\"15\">\n                <el-col :span=\"6\">\n                  <el-form-item label=\"运行编号：\">\n                    <el-input\n                      v-model=\"headForm.yxbh\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"运维班组：\">\n                    <el-input\n                      v-model=\"headForm.ywbzmc\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"投运日期：\">\n                    <el-input\n                      v-model=\"headForm.tyrq\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"是否具有环网：\">\n                    <el-input\n                      v-model=\"headForm.sfjyhw\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-row>\n            </el-form>\n            <!--     配电柜基本信息     -->\n            <el-form\n              :model=\"headForm\"\n              label-width=\"140px\"\n              v-if=\"treeNode.identifier === '2'\"\n            >\n              <el-row :gutter=\"20\">\n                <el-col :span=\"6\">\n                  <el-form-item label=\"所属站室名称:\">\n                    <el-input\n                      v-model=\"headForm.sszsmc\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"所属站室运行编号:\">\n                    <el-input\n                      v-model=\"headForm.sszsyxbh\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"开关柜名称:\">\n                    <el-input\n                      v-model=\"headForm.kggmc\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"运行编号:\">\n                    <el-input\n                      v-model=\"headForm.yxbh\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n              <el-row :gutter=\"15\">\n                <el-col :span=\"6\">\n                  <el-form-item label=\"投运日期：\">\n                    <el-input\n                      v-model=\"headForm.tyrq\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"生产厂家：\">\n                    <el-input\n                      v-model=\"headForm.sccj\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"开关柜型号：\">\n                    <el-input\n                      v-model=\"headForm.kggxh\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"出厂日期：\">\n                    <el-input\n                      v-model=\"headForm.ccrq\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-row>\n            </el-form>\n          </div>\n        </el-white>\n      </el-col>\n        <!-- <el-filter\n          ref=\"filter1\"\n          :data=\"pdzfilterInfo.data\"\n          :field-list=\"pdzfilterInfo.fieldList\"\n          :width=\"{ labelWidth: 120, itemWidth: 180 }\"\n          @handleReset=\"filterReset\"\n          v-show=\"pdzshow\"\n        />\n        <el-filter\n          ref=\"filter1\"\n          :data=\"pdgfilterInfo.data\"\n          :field-list=\"pdgfilterInfo.fieldList\"\n          :width=\"{ labelWidth: 120, itemWidth: 180 }\"\n          v-show=\"pdgshow\"\n        />\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 140, itemWidth: 190 }\"\n          @handleReset=\"filterReset\"\n          v-show=\"sbshow\"\n        /> -->\n      <el-col :span=\"19\">\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button\n              icon=\"el-icon-plus\"\n              @click=\"AddSensorButton\"\n              v-hasPermi=\"['pdsbtz:button:add']\"\n              type=\"primary\"\n            >\n              新增\n            </el-button>\n\n          </div>\n\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo1\"\n            @update:multipleSelection=\"pdzhandleSelectionChange\"\n            height=\"78.2vh\"\n            v-show=\"pdzshow\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"pdzgetUpdate(scope.row)\"\n                  v-hasPermi=\"['pdsbtz:button:update']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                >\n                </el-button>\n                <el-button\n                  @click=\"pdzgetXq(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n                <el-button title=\"删除\" icon=\"el-icon-delete\" v-if=\"scope.row.createBy === $store.getters.name\"\n                  v-hasPermi=\"['pdsbtz:button:delete']\" type=\"text\" @click=\"deleteRow(scope.row.objId)\">\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo2\"\n            @update:multipleSelection=\"pdzhandleSelectionChange\"\n            height=\"61.4vh\"\n            v-show=\"pdgshow\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"pdgUpdate(scope.row)\"\n                  v-hasPermi=\"['pdsbtz:button:update']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                >\n                </el-button>\n                <el-button\n                  @click=\"pdgDetails(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n                <el-button title=\"删除\" icon=\"el-icon-delete\"\n                  v-hasPermi=\"['pdsbtz:button:delete']\" type=\"text\" @click=\"deleteRow(scope.row.objId)\">\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo3\"\n            @update:multipleSelection=\"selectChange\"\n            height=\"61.4vh\"\n            v-show=\"sbshow\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"getSbXgButton(scope.row)\"\n                  v-hasPermi=\"['pdsbtz:button:update']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                >\n                </el-button>\n                <el-button\n                  @click=\"getSbXqButton(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n                <el-button title=\"删除\" icon=\"el-icon-delete\" v-if=\"scope.row.createBy === $store.getters.name\"\n                  v-hasPermi=\"['pdsbtz:button:delete']\" type=\"text\" @click=\"deleteRow(scope.row.objId)\"></el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!--配电室新增、修改、详情弹框-->\n    <el-dialog\n    v-dialogDrag\n      title=\"配电站\"\n      :visible.sync=\"pdsDialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"70%\"\n      @close=\"handleClose\"\n    >\n      <el-form\n        ref=\"pdzform\"\n        :model=\"pdzform\"\n        :disabled=\"isDisabled\"\n        :rules=\"rules\"\n        label-width=\"130px\"\n      >\n        <div\n          class=\"block\"\n          style=\"width: 50%; height: 50%; margin-bottom: 2%; float: right\"\n        >\n          <span class=\"demonstration\">配电站图片</span>\n          <el-carousel\n            trigger=\"click\"\n            class=\"imgCls\"\n            indicator-position=\"none\"\n            :interval=\"2000\"\n            type=\"card\"\n            id=\"imgId\"\n          >\n            <el-carousel-item v-for=\"(img, index) in imgList\" :key=\"index\">\n              <viewer :images=\"imgList\">\n                <img :src=\"img.url\" width=\"100%\" height=\"100%\"/>\n              </viewer>\n            </el-carousel-item>\n          </el-carousel>\n        </div>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item  label=\"配电室类型：\" prop=\"lx\">\n              <el-select\n                v-model=\"pdzform.lx\"\n                placeholder=\"请选择配电室类型\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in pdslx\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item  label=\"所属线路名称：\" prop=\"ssxlmc\">\n              <el-input\n                v-model=\"pdzform.ssxlmc\"\n                placeholder=\"请输入所属线路名称\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属线路编号：\" prop=\"ssxlbh\">\n              <el-input\n                v-model=\"pdzform.ssxlbh\"\n                placeholder=\"请输入所属线路编号\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属线段名称：\" prop=\"ssxdmc\">\n              <el-input\n                v-model=\"pdzform.ssxdmc\"\n                placeholder=\"请输入所属线段名称\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"配电室名称：\" prop=\"pdsmc\">\n              <el-input\n                v-model=\"pdzform.pdsmc\"\n                placeholder=\"请输入配电室名称\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"运行编号：\" prop=\"yxbh\">\n              <el-input\n                v-model=\"pdzform.yxbh\"\n                placeholder=\"请输入运行编号\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"运维班组：\" prop=\"ywbz\">\n              <el-select\n                v-model=\"pdzform.ywbz\"\n                placeholder=\"请选择运维班组\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in ywbzList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"投运日期：\" prop=\"tyrq\">\n              <el-date-picker\n                type=\"date\"\n                placeholder=\"选择日期\"\n                value-format=\"yyyy-MM-dd\"\n                v-model=\"pdzform.tyrq\"\n                style=\"width: 100%\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"状态：\" prop=\"zt\">\n              <el-select v-model=\"pdzform.zt\" placeholder=\"请选择状态\">\n                <el-option\n                  v-for=\"item in pdzsbzt\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item v-if=\"pdzform.lx == '箱式变电站'\"  label=\"生产厂家：\" prop=\"sccj\">\n              <el-input v-model=\"pdzform.sccj\" placeholder=\"请输入生产厂家\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"通风方式：\" prop=\"tffs\">\n              <el-input\n                v-model=\"pdzform.tffs\"\n                placeholder=\"请输入通风方式\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"站变用：\" prop=\"zby\">\n              <el-input-number\n                v-model=\"pdzform.zby\"\n                :min=\"0\"\n                placeholder=\"请输入站变用\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item v-if=\"!(pdzform.lx == '柱上变台变')\"  label=\"高压进线柜数量：\" prop=\"gyjxgsl\">\n              <el-input-number\n                v-model=\"pdzform.gyjxgsl\"\n                :min=\"0\"\n                placeholder=\"请输入高压进线柜数量\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"高压计量柜数量：\" prop=\"gyjlgsl\">\n              <el-input-number\n                v-model=\"pdzform.gyjlgsl\"\n                :min=\"0\"\n                placeholder=\"请输入高压计量柜数量\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item v-if=\"!(pdzform.lx == '柱上变台变')\" label=\"高压出线柜数量：\" prop=\"gycxgsl\">\n              <el-input-number\n                v-model=\"pdzform.gycxgsl\"\n                :min=\"0\"\n                placeholder=\"请输入高压出线柜数量\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"PT柜数量：\" prop=\"ptgsl\">\n              <el-input-number\n                v-model=\"pdzform.ptgsl\"\n                :min=\"0\"\n                placeholder=\"请输入PT柜数量\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"母联柜数量：\" prop=\"mlgsl\">\n              <el-input-number\n                v-model=\"pdzform.mlgsl\"\n                :min=\"0\"\n                placeholder=\"请输入母联柜数量\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item v-if=\"!(pdzform.lx == '柱上变台变')\" label=\"低压进线柜数量：\" prop=\"dyjxgsl\">\n              <el-input-number\n                v-model=\"pdzform.dyjxgsl\"\n                :min=\"0\"\n                placeholder=\"低压进线柜数量\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"低压出线柜数量：\" prop=\"dycxgsl\">\n              <el-input-number\n                v-model=\"pdzform.dycxgsl\"\n                :min=\"0\"\n                placeholder=\"请输入低压出线柜数量\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item v-if=\"!(pdzform.lx == '柱上变台变')\" label=\"低压补偿柜数量：\" prop=\"dybcgsl\">\n              <el-input-number\n                v-model=\"pdzform.dybcgsl\"\n                :min=\"0\"\n                placeholder=\"请输入低压补偿柜数量\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item v-if=\"!(pdzform.lx == '柱上变台变')\" label=\"低压计量柜数量：\" prop=\"dyjlgsl\">\n              <el-input-number\n                v-model=\"pdzform.dyjlgsl\"\n                :min=\"0\"\n                placeholder=\"请输入低压计量柜数量\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item v-if=\"!(pdzform.lx == '柱上变台变')\" label=\"低压联络柜数量：\" prop=\"dyllgsl\">\n              <el-input-number\n                v-model=\"pdzform.dyllgsl\"\n                :min=\"0\"\n                placeholder=\"请输入低压联络柜数量\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"配变数量：\" prop=\"pbsl\">\n              <el-input-number\n                v-model=\"pdzform.pbsl\"\n                :min=\"0\"\n                placeholder=\"请输入配变数量\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"配变总容量：\" prop=\"pbzrl\">\n              <el-input-number\n                v-model=\"pdzform.pbzrl\"\n                :min=\"0\"\n                placeholder=\"请输入配变总容量(KVA)\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"蓄电池柜数量：\" prop=\"xdcgsl\">\n              <el-input-number\n                v-model=\"pdzform.xdcgsl\"\n                :min=\"0\"\n                placeholder=\"请输入蓄电池柜数量\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"直流柜数量：\" prop=\"zlgsl\">\n              <el-input-number\n                v-model=\"pdzform.zlgsl\"\n                :min=\"0\"\n                placeholder=\"请输入直流柜数量\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"电杆数量：\" prop=\"dgsl\">\n              <el-input-number\n                v-model=\"pdzform.dgsl\"\n                :min=\"0\"\n                placeholder=\"请输入电杆数量\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"电杆高度(m)：\" prop=\"dggd\">\n              <el-input-number\n                v-model=\"pdzform.dggd\"\n                :min=\"0\"\n                placeholder=\"请输入电杆高度\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item v-if=\"pdzform.lx == '箱式变电站'\"  label=\"箱式变型号：\" prop=\"xsbxh\">\n              <el-input\n                v-model=\"pdzform.xsbxh\"\n                placeholder=\"请输入箱式变型号\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"低压配电柜数量：\" prop=\"dypdgsl\">\n              <el-input-number\n                v-model=\"pdzform.dypdgsl\"\n                :min=\"0\"\n                placeholder=\"请输入低压配电柜数量\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"计量柜数量：\" prop=\"jlgsl\">\n              <el-input-number\n                v-model=\"pdzform.jlgsl\"\n                :min=\"0\"\n                placeholder=\"请输入计量柜数量\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"经度\" prop=\"jd\">\n              <el-input\n                v-model=\"pdzform.jd\"\n                placeholder=\"请输入经度\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"纬度\" prop=\"wd\">\n              <el-input v-model=\"pdzform.wd\" placeholder=\"请输入纬度\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否具有环网：\" prop=\"sfjyhw\">\n              <el-select\n                v-model=\"pdzform.sfjyhw\"\n                placeholder=\"请选择是否具有环网\"\n                :disabled=\"isDisabled\"\n                clearable\n                style=\"width: 80%\"\n              >\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\" prop=\"bz\">\n              <el-input\n                v-model=\"pdzform.bz\"\n                type=\"textarea\"\n                rows=\"2\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-form-item\n            label=\"已上传图片：\"\n            v-if=\"pdzform.attachment.length > 0\"\n            id=\"pic_form\"\n          >\n            <el-col\n              :span=\"24\"\n              v-for=\"(item, index) in pdzform.attachment\"\n              style=\"margin-left: 0\"\n            >\n              <el-form-item :label=\"(index + 1).toString()\">\n                {{ item.fileOldName }}\n                <el-button\n                  v-if=\"!isDisabled\"\n                  type=\"error\"\n                  size=\"mini\"\n                  @click=\"deleteFileById(item.fileId)\"\n                  >删除\n                </el-button>\n              </el-form-item>\n            </el-col>\n          </el-form-item>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-form-item label=\"上传图片：\" v-if=\"!isDisabled\">\n            <el-upload\n              list-type=\"picture-card\"\n              class=\"upload-demo\"\n              accept=\".jpg,.png\"\n              ref=\"upload\"\n              :headers=\"header\"\n              action=\"/isc-api/file/upload\"\n              :before-upload=\"beforeUpload\"\n              :data=\"uploadData\"\n              single\n              :auto-upload=\"false\"\n              multiple\n            >\n              <el-button slot=\"trigger\" type=\"primary\">选取文件</el-button>\n              <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png文件</div>\n            </el-upload>\n          </el-form-item>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\" v-show=\"!isDisabled\">\n        <el-button @click=\"pdsDialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"getDetermine\">确 定</el-button>\n      </div>\n    </el-dialog>\n\n    <!--配电柜新增、修改、详情弹框-->\n    <el-dialog\n    v-dialogDrag\n      title=\"配电柜详情\"\n      :visible.sync=\"bdgDialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"60%\"\n      @close=\"handleClose\"\n    >\n      <el-form\n        ref=\"pdgform\"\n        :model=\"pdgform\"\n        :rules=\"rules\"\n        :disabled=\"pdgdisable\"\n        label-width=\"130px\"\n      >\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属站室\" prop=\"sszs\">\n              <el-input\n                v-model=\"pdgform.sszs\"\n                placeholder=\"请输入所属站室名称\"\n                disabled=\"disabled\"\n              ></el-input>\n<!--              <el-select\n                v-model=\"pdgform.sszs\"\n                placeholder=\"请选择所属电站\"\n                filterable\n                disabled=\"disabled\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in pdgOptionsDataList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>-->\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"开关柜名称\" prop=\"kggmc\">\n              <el-input\n                v-model=\"pdgform.kggmc\"\n                placeholder=\"请输入开关柜名称\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"运行编号\" prop=\"yxbh\">\n              <el-input\n                v-model=\"pdgform.yxbh\"\n                placeholder=\"请输入运行编号\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"投运日期：\" prop=\"tyrq\">\n              <el-date-picker\n                type=\"date\"\n                :disabled=\"isDisabled\"\n                placeholder=\"选择日期\"\n                value-format=\"yyyy-MM-dd\"\n                v-model=\"pdgform.tyrq\"\n                style=\"width: 100%\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"生产厂家\" prop=\"sccj\">\n              <el-input\n                v-model=\"pdgform.sccj\"\n                :placeholder=\"pdgdisable?'':'请输入生产厂家'\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"开关柜型号\" prop=\"kggxh\">\n              <el-input\n                v-model=\"pdgform.kggxh\"\n                placeholder=\"请输入开关柜型号\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"出厂日期：\" prop=\"ccrq\">\n              <el-date-picker\n                type=\"date\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"pdgdisable?'':'选择日期'\"\n                value-format=\"yyyy-MM-dd\"\n                v-model=\"pdgform.ccrq\"\n                style=\"width: 100%\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"状态\" prop=\"zt\">\n              <el-select\n                v-model=\"pdgform.zt\"\n                placeholder=\"请选择状态\"\n                :disabled=\"isDisabled\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in pdgztList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.label\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电压等级(KV)\" prop=\"dydj\">\n              <el-input-number\n                :min=\"0\"\n                :precision=\"2\"\n                v-model=\"pdgform.dydj\"\n                placeholder=\"请输入电压等级\"\n                :disabled=\"isDisabled\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"开关用途\" prop=\"kgyt\">\n              <el-select\n                v-model=\"pdgform.kgyt\"\n                placeholder=\"请选择开关用途\"\n                :disabled=\"isDisabled\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in pdgkgytList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.label\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"开关柜类型\" prop=\"kgglx\">\n              <el-input\n                v-model=\"pdgform.kgglx\"\n                placeholder=\"请输入开关柜类型\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"闭锁型式\" prop=\"bsxs\">\n              <el-input\n                v-model=\"pdgform.bsxs\"\n                placeholder=\"请输入闭锁型式\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\" prop=\"bz\">\n              <el-input\n                v-model=\"pdgform.bz\"\n                type=\"textarea\"\n                rows=\"2\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\" v-show=\"showButton\">\n        <el-button @click=\"bdgDialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"save\" class=\"pmyBtn\">确 定</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 设备基本信息、参数、履历弹出框-->\n    <el-dialog\n      :title=\"sbtitle\"\n      :visible.sync=\"dialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"70%\"\n      v-dialogDrag\n      :before-close=\"resetForm\"\n    >\n      <div>\n        <el-tabs v-model=\"activeTabName\">\n          <!--基本信息-->\n          <el-tab-pane label=\"基本信息\" name=\"sbDesc\">\n            <!--添加信息-->\n            <el-form\n              :model=\"jbxxForm\"\n              :rules=\"rules\"\n              label-width=\"140px\"\n              ref=\"jbxxForm\"\n              :disabled=\"isDisabled\"\n            >\n              <el-row>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"设备类型\" prop=\"sblx\">\n                    <el-select\n                      v-model=\"jbxxForm.sblx\"\n                      placeholder=\"请输入内容\"\n                      @change=\"showParams\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in sblxOptionsDataSelected\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"所属站室\" prop=\"sszs\">\n                    <el-select\n                      v-model=\"jbxxForm.sszs\"\n                      placeholder=\"请输入内容\"\n                      @change=\"pdsOptionsChangeClick()\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in pdsOptionsDataList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"所属开关柜\" prop=\"sskgg\">\n                    <el-select\n                      v-model=\"jbxxForm.sskgg\"\n                      placeholder=\"请输入内容\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in sbOptionsDataList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"设备名称\" prop=\"sbmc\">\n                    <el-input v-model=\"jbxxForm.sbmc\" placeholder=\"请输入内容\">\n                    </el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"运行编号\" prop=\"yxbh\">\n                    <el-input v-model=\"jbxxForm.yxbh\" placeholder=\"请输入内容\">\n                    </el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"投运日期\" prop=\"tyrq\">\n                    <el-date-picker\n                      v-model=\"jbxxForm.tyrq\"\n                      type=\"date\"\n                      value-format=\"yyyy-MM-dd\"\n                      placeholder=\"选择日期\"\n                    >\n                    </el-date-picker>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"状态\" prop=\"zt\">\n                    <el-select\n                      v-model=\"jbxxForm.zt\"\n                      placeholder=\"请输入内容\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in jbxxztList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"电压等级\" prop=\"dydj\">\n                    <el-input v-model=\"jbxxForm.dydj\" placeholder=\"请输入内容\">\n                    </el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"规格型号\" prop=\"ggxh\">\n                    <el-input v-model=\"jbxxForm.ggxh\" placeholder=\"请输入内容\">\n                    </el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"生产厂家\" prop=\"sccj\">\n                    <el-input v-model=\"jbxxForm.sccj\"  :placeholder=\"isDisabled?'':'请输入内容'\">\n                    </el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"出厂日期\" prop=\"ccrq\">\n                    <el-date-picker\n                      v-model=\"jbxxForm.ccrq\"\n                      type=\"date\"\n                      value-format=\"yyyy-MM-dd\"\n                      :placeholder=\"isDisabled?'':'选择日期'\"\n                    >\n                    </el-date-picker>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"开关用途\" prop=\"kgyt\">\n                    <el-select\n                      v-model=\"jbxxForm.kgyt\"\n                      :placeholder=\"isDisabled?'':'请输入内容'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in jbxxkgytList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"灭弧介质\" prop=\"mhjz\">\n                    <el-select\n                      v-model=\"jbxxForm.mhjz\"\n                      :placeholder=\"isDisabled?'':'请输入灭弧介质'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in mhjzList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"操作机构型\" prop=\"czjgxs\">\n                    <el-select\n                      v-model=\"jbxxForm.czjgxs\"\n                      :placeholder=\"isDisabled?'':'请输入操作机构型式'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in lzjgxsList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"操作方式\" prop=\"czfs\">\n                    <el-select\n                      v-model=\"jbxxForm.czfs\"\n                      :placeholder=\"isDisabled?'':'请输入操作方式'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in czfsList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"绝缘介质\" prop=\"jyjz\">\n                    <el-select\n                      v-model=\"jbxxForm.jyjz\"\n                      :placeholder=\"isDisabled?'':'请输入绝缘介质'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in jyjzList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"额定电压   \" prop=\"eddy\">\n                    <el-input\n                      v-model=\"jbxxForm.eddy\"\n                      :placeholder=\"isDisabled?'':'请输入额定电压'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"额定电流   \" prop=\"eddl\">\n                    <el-input-number\n                      v-model=\"jbxxForm.eddl\"\n                      :min=\"0\"\n                      placeholder=\"请输入额定电流\"\n                    ></el-input-number>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"熔丝规格   \" prop=\"rsgg\">\n                    <el-input\n                      v-model=\"jbxxForm.rsgg\"\n                      :placeholder=\"isDisabled?'':'请输入熔丝规格'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"绝缘材质   \" prop=\"jycz\">\n                    <el-select\n                      v-model=\"jbxxForm.jycz\"\n                      :placeholder=\"isDisabled?'':'请输入绝缘材质'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in jyczList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"数量\" prop=\"sl\">\n                    <el-input-number\n                      :min=\"0\"\n                      v-model=\"jbxxForm.sl\"\n                      placeholder=\"请输入数量\"\n                    ></el-input-number>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"额定电流比  \" prop=\"eddlb\">\n                    <el-input\n                      v-model=\"jbxxForm.eddlb\"\n                      :placeholder=\"isDisabled?'':'请输入额定电流比'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"额定电压比  \" prop=\"eddyb\">\n                    <el-input\n                      v-model=\"jbxxForm.eddyb\"\n                      :placeholder=\"isDisabled?'':'请输入额定电压比'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"绝缘方式\" prop=\"jyfs\">\n                    <el-select\n                      v-model=\"jbxxForm.jyfs\"\n                      :placeholder=\"isDisabled?'':'请输入绝缘方式'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in jyfsList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"总容量\" prop=\"zrl\">\n                    <el-input-number\n                      :min=\"0\"\n                      v-model=\"jbxxForm.zrl\"\n                      placeholder=\"请输入总容量\"\n                    ></el-input-number>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"投退方式\" prop=\"ttfs\">\n                    <el-select\n                      v-model=\"jbxxForm.ttfs\"\n                      :placeholder=\"isDisabled?'':'请输入投退方式'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in ttfsList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"单只容量\" prop=\"dzrl\">\n                    <el-input-number\n                      :min=\"0\"\n                      v-model=\"jbxxForm.dzrl\"\n                      placeholder=\"请输入单只容量\"\n                    ></el-input-number>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"资产性质\" prop=\"zcxx\">\n                    <el-select\n                      v-model=\"jbxxForm.zcxx\"\n                      :placeholder=\"isDisabled?'':'请输入资产性质'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in zzxzList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"母线型式\" prop=\"mxxs\">\n                    <el-select\n                      v-model=\"jbxxForm.mxxs\"\n                      :placeholder=\"isDisabled?'':'请输入母线型式'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in mxxsList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"母线材质\" prop=\"mxcz\">\n                    <el-input\n                      v-model=\"jbxxForm.mxcz\"\n                      :placeholder=\"isDisabled?'':'请输入母线材质'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"长度\" prop=\"cd\">\n                    <el-input-number\n                      :min=\"0\"\n                      v-model=\"jbxxForm.cd\"\n                      placeholder=\"请输入长度\"\n                    ></el-input-number>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"额定容量\" prop=\"edrl\">\n                    <el-input-number\n                      :min=\"0\"\n                      v-model=\"jbxxForm.edrl\"\n                      placeholder=\"请输入额定容量\"\n                    ></el-input-number>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"配电变压器类型\" prop=\"byqlx\">\n                    <el-select\n                      v-model=\"jbxxForm.byqlx\"\n                      :placeholder=\"isDisabled?'':'请输入配电变压器类型'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in dyqlxList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"阻抗电压\" prop=\"zkdy\">\n                    <el-input\n                      v-model=\"jbxxForm.zkdy\"\n                      :placeholder=\"isDisabled?'':'请输入阻抗电压'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"短路阻抗\" prop=\"dlzk\">\n                    <el-input\n                      v-model=\"jbxxForm.dlzk\"\n                      :placeholder=\"isDisabled?'':'请输入短路阻抗'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"短路损耗\" prop=\"dlsh\">\n                    <el-input\n                      v-model=\"jbxxForm.dlsh\"\n                      :placeholder=\"isDisabled?'':'请输入短路损耗'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"接线组别\" prop=\"jxzb\">\n                    <el-input\n                      v-model=\"jbxxForm.jxzb\"\n                      :placeholder=\"isDisabled?'':'请输入接线组别'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"调压范围\" prop=\"tyfw\">\n                    <el-input\n                      v-model=\"jbxxForm.tyfw\"\n                      :placeholder=\"isDisabled?'':'请输入调压范围'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"油号\" prop=\"yh\">\n                    <el-input\n                      v-model=\"jbxxForm.yh\"\n                      :placeholder=\"isDisabled?'':'请输入油号'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"油重\" prop=\"yz\">\n                    <el-input-number\n                      :min=\"0\"\n                      v-model=\"jbxxForm.yz\"\n                      placeholder=\"请输入油重\"\n                    ></el-input-number>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"总重\" prop=\"zz\">\n                    <el-input-number\n                      :min=\"0\"\n                      v-model=\"jbxxForm.zz\"\n                      placeholder=\"请输入总重\"\n                    ></el-input-number>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"绝缘等级\" prop=\"jydj\">\n                    <el-select\n                      v-model=\"jbxxForm.jydj\"\n                      :placeholder=\"isDisabled?'':'请输入绝缘等级'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in jydjList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"调压方式\" prop=\"tyfs\">\n                    <el-input\n                      v-model=\"jbxxForm.tyfs\"\n                      :placeholder=\"isDisabled?'':'请输入调压方式'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"铁芯材质\" prop=\"txcz\">\n                    <el-input\n                      v-model=\"jbxxForm.txcz\"\n                      :placeholder=\"isDisabled?'':'请输入铁芯材质'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"芯数及截面\" prop=\"xsjjm\">\n                    <el-input\n                      v-model=\"jbxxForm.xsjjm\"\n                      :placeholder=\"isDisabled?'':'请输入芯数及截面'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"负载名称\" prop=\"fzmc\">\n                    <el-input\n                      v-model=\"jbxxForm.fzmc\"\n                      :placeholder=\"isDisabled?'':'请输入负载名称'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"电缆材质\" prop=\"dlcz\">\n                    <el-input\n                      v-model=\"jbxxForm.dlcz\"\n                      :placeholder=\"isDisabled?'':'请输入电缆材质'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"备注\" prop=\"bz\">\n                    <el-input\n                      v-model=\"jbxxForm.bz\"\n                      :placeholder=\"isDisabled?'':'请输入内容'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n            </el-form>\n          </el-tab-pane>\n\n          <!--技术参数-->\n          <el-tab-pane label=\"技术参数\" name=\"sbjscs\">\n            <el-form :model=\"jscsForm\" label-width=\"130px\">\n              <el-col :span=\"8\" v-for=\"item in jscsLabelList\">\n                <el-form-item\n                  :label=\"\n                    item.dw != ''\n                      ? item.label + '(' + item.dw + ')'\n                      : item.label\n                  \"\n                >\n                  <el-input\n                    v-if=\"item.type === 'input'\"\n                    v-model=\"jscsForm[item.jscsbm]\"\n                    placeholder=\"\"\n                  >\n                  </el-input>\n                  <el-select\n                    clearable\n                    v-if=\"item.type === 'select'\"\n                    v-model=\"jscsForm[item.jscsbm]\"\n                    placeholder=\"\"\n                  >\n                    <el-option\n                      v-for=\"(childItem, key) in item.options\"\n                      :key=\"key\"\n                      :label=\"childItem.label\"\n                      :value=\"childItem.value\"\n                      :disabled=\"childItem.disabled\"\n                      style=\"display: flex; align-items: center\"\n                      clearable\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </el-form>\n          </el-tab-pane>\n\n          <!--设备履历-->\n          <el-tab-pane label=\"设备履历\" name=\"sbRecord\">\n            <el-tabs\n              v-model=\"sbllDescTabName\"\n              @tab-click=\"handleSbllDescTabNameClick\"\n              type=\"card\"\n            >\n              <el-tab-pane label=\"试验记录\" name=\"syjl\">\n                <el-table\n                  stripe\n                  border\n                  v-loading=\"loading\"\n                  :data=\"sblvsyjlList\"\n                  max-height=\"550\"\n                >\n                  <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n                  <el-table-column\n                    label=\"试验专业\"\n                    align=\"center\"\n                    prop=\"syzy\"\n                  />\n                  <el-table-column\n                    label=\"试验性质\"\n                    align=\"center\"\n                    prop=\"syxz\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"试验名称\"\n                    align=\"center\"\n                    prop=\"symc\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"工作地点\"\n                    align=\"center\"\n                    prop=\"gzdd\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"试验设备\"\n                    align=\"center\"\n                    prop=\"sysb\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"试验报告\"\n                    align=\"center\"\n                    prop=\"sybg\"\n                  />\n                  <el-table-column\n                    label=\"天气\"\n                    align=\"center\"\n                    prop=\"tq\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"试验日期\"\n                    align=\"center\"\n                    prop=\"syrq\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"录入人\"\n                    align=\"center\"\n                    prop=\"lrr\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"试验结论\"\n                    align=\"center\"\n                    prop=\"syjl\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                </el-table>\n              </el-tab-pane>\n              <!-- <el-tab-pane label=\"缺陷记录\" name=\"qxjl\">\n                <el-table\n                  stripe\n                  border\n                  v-loading=\"loading\"\n                  :data=\"sbllqxjlList\"\n                  max-height=\"550\"\n                >\n                  <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n                  <el-table-column\n                    label=\"所属公司\"\n                    align=\"center\"\n                    prop=\"ssgs\"\n                  />\n                  <el-table-column\n                    label=\"变电站名称\"\n                    align=\"center\"\n                    prop=\"bdzmc\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"设备类型\"\n                    align=\"center\"\n                    prop=\"sblx\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"缺陷性质\"\n                    align=\"center\"\n                    prop=\"qxxz\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"电压等级\"\n                    align=\"center\"\n                    prop=\"dydj\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"设备型号\"\n                    align=\"center\"\n                    prop=\"sbxh\"\n                  />\n                  <el-table-column\n                    label=\"生产厂家\"\n                    align=\"center\"\n                    prop=\"sccj\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                </el-table>\n              </el-tab-pane>\n              <el-tab-pane label=\"状态变更记录\" name=\"ztbgjl\">\n                <comp-table\n                  :table-and-page-info=\"resumPageInfo\"\n                  @getMethod=\"getResumList\"\n                  @update:multipleSelection=\"selectChange\"\n                  height=\"500\"\n                />\n              </el-tab-pane> -->\n            </el-tabs>\n          </el-tab-pane>\n        </el-tabs>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\" v-show=\"!isDisabled\">\n        <el-button @click=\"resetForm\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submit\" class=\"submit\"\n          >确 定\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!--状态变更弹出框展示-->\n    <el-dialog\n      title=\"设备状态变更\"\n      :visible.sync=\"dialogVisible\"\n      width=\"30%\"\n      append-to-body\n      v-dialogDrag\n    >\n      <el-form :inline=\"true\" label-width=\"100px\" class=\"qxlr_dialog_insert\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"设备状态:\">\n              <el-select v-model=\"updateList.zt\">\n                <el-option\n                  v-for=\"item in jbxxztList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitStatus\">确 定</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport {\n  getList,\n  saveOrUpdate,\n  remove,\n  getPdsbOne,\n} from \"@/api/dagangOilfield/dwzygl/pdsbgl/pdsbjbxx\";\nimport {\n  updateStatus,\n  getResumDataList,\n} from \"@/api/dagangOilfield/dwzygl/pdsbgl/pdsbztbg\";\nimport {\n  addPdg,\n  getPdgList,\n  getPdgListSelected,\n  getPdgOne,\n  getPdsTreeList,\n  removePdg,\n  getMergePdgInfo,\n} from \"@/api/dagangOilfield/asset/pdg\";\nimport { getSblxDataListSelected } from \"@/api/dagangOilfield/asset/bdsbtz\";\nimport {\n  getParamDataList,\n  getParamsValue,\n  saveParamValue,\n} from \"@/api/dagangOilfield/asset/parameters\";\nimport {\n  addPds,\n  getPdsList,\n  getPdsOptionsDataList,\n  removePds,\n} from \"@/api/dagangOilfield/asset/pdsgl\";\nimport { getDictTypeData } from \"@/api/system/dict/data\";\nimport { selectDeptOneAndTwo } from \"@/api/system/dept\";\nimport { deleteById, getListByBusinessId } from \"@/api/tool/file\";\nimport {adddwzyfstz} from \"@/api/dagangOilfield/asset/jgtz\";\n\nexport default {\n  name: \"pdsbgl\",\n  data() {\n    return {\n      sskgg:\"\",\n      treeNode:'',//树节点标记\n      headForm: {}, //头部表单信息\n      uploadData: {\n        type: \"\",\n        businessId: undefined,\n      },\n      sszs: \"\",\n      icons: {\n        pdsList: \"categoryTreeIcons\",\n        pds: \"tableIcon\",\n        pdg: \"classIcon\",\n      },\n      pdzshow: true,\n      sbshow: false,\n      pdgshow: false,\n      //配电站相关\n      //弹出框表单\n      pdzform: {\n        attachment: [],\n        //ssgs: undefined,\n        ssxlmc: undefined,\n        ssxlbh: undefined,\n        ssxdmc: undefined,\n        pdsmc: undefined,\n        yxbh: undefined,\n        erpBm: undefined,\n        bgr: undefined,\n        zcbdfs: undefined,\n        zcsx: undefined,\n        zcbh: undefined,\n        wbsYs: undefined,\n        zcxz: undefined,\n        tyrq: undefined,\n        sfjyhw: undefined,\n        zt: undefined,\n        dqtz: undefined,\n        sccj: undefined,\n        sgdw: undefined,\n        jzwcc: undefined,\n        jzwcz: undefined,\n        tffs: undefined,\n        dlqgsl: undefined,\n        dyhgqgsl: undefined,\n        drgsl: undefined,\n        dyjxfs: undefined,\n        dypxgsl: undefined,\n        fhkggsl: undefined,\n        fhkgrdqzhgsl: undefined,\n        jlgsl: undefined,\n        mlgsl: undefined,\n        pbsl: undefined,\n        pbzrl: undefined,\n        wz: undefined,\n        xdcgsl: undefined,\n        zlgsl: undefined,\n        zby: undefined,\n        gyjxgsl: undefined,\n        gyjlgsl: undefined,\n        gycxgsl: undefined,\n        ptgsl: undefined,\n        dyjxgsl: undefined,\n        dycxgsl: undefined,\n        dybcgsl: undefined,\n        dyjlgsl: undefined,\n        dyllgsl: undefined,\n        dgsl: undefined,\n        dggd: undefined,\n        xsbxh: undefined,\n        dypdgsl: undefined,\n        lx: undefined,\n      },\n      pdsDialogFormVisible: false,\n      pdzfilterInfo: {\n        data: {\n          ssgs: [],\n          ssxlmc: \"\",\n          yxbh: \"\",\n          zt: [],\n        },\n        fieldList: [\n          // {label: '所属公司', type: 'select', value: 'ssgs', multiple: true, options: []},\n          { label: \"所属线路名称\", type: \"input\", value: \"ssxlmc\" },\n          { label: \"配电室名称\", type: \"input\", value: \"pdsmc\" },\n          { label: \"所属线路编号\", type: \"input\", value: \"ssxlbh\" },\n          { label: \"所属线段名称\", type: \"input\", value: \"ssxdmc\" },\n          { label: \"运行编号\", type: \"input\", value: \"yxbh\" },\n          { label: \"运维班组\", type: \"input\", value: \"ywbzmc\" },\n          {\n            label: \"投运日期\",\n            type: \"date\",\n            value: \"tyrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\",\n          },\n          {\n            label: \"是否具有环网\",\n            type: \"select\",\n            value: \"sfjyhw\",\n            options: [\n              {\n                value: \"是\",\n                label: \"是\",\n              },\n              {\n                value: \"否\",\n                label: \"否\",\n              },\n            ],\n          },\n          {\n            label: \"状态\",\n            type: \"select\",\n            value: \"zt\",\n            options: [\n              {\n                value: \"在运\",\n                label: \"在运\",\n              },\n              {\n                value: \"停止使用\",\n                label: \"停止使用\",\n              },\n              {\n                value: \"未就绪\",\n                label: \"未就绪\",\n              },\n              {\n                value: \"报废\",\n                label: \"报废\",\n              },\n            ],\n          },\n          { label: \"生产厂家\", type: \"input\", value: \"sccj\" },\n          { label: \"通风方式\", type: \"input\", value: \"tffs\" },\n          {\n            label: \"配电室类型\",\n            type: \"select\",\n            value: \"lx\",\n            options: [\n              {\n                value: \"箱式变电站\",\n                label: \"箱式变电站\",\n              },\n              {\n                value: \"柱上变台变\",\n                label: \"柱上变台变\",\n              },\n              {\n                value: \"配电室\",\n                label: \"配电室\",\n              },\n            ],\n          },\n        ],\n      },\n      ywbzList: [],\n      pdzParams: {\n        pageNum: 1,\n        pageSize: 10,\n      },\n      tableAndPageInfo1: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n          pageResize: \"\",\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true,\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"ssxlmc\", label: \"所属线路名称\", minWidth: \"180\" },\n          { prop: \"ssxlbh\", label: \"所属线路编号\", minWidth: \"120\" },\n          { prop: \"ssxdmc\", label: \"所属线段名称\", minWidth: \"180\" },\n          { prop: \"pdsmc\", label: \"配电室名称\", minWidth: \"140\" },\n          { prop: \"yxbh\", label: \"运行编号\", minWidth: \"120\" },\n          { prop: \"ywbzmc\", label: \"运维班组\", minWidth: \"120\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"120\" },\n          { prop: \"sfjyhw\", label: \"是否具有环网\", minWidth: \"120\" },\n          { prop: \"zt\", label: \"状态\", minWidth: \"120\" },\n          { prop: \"sccj\", label: \"生产厂家\", minWidth: \"120\" },\n          { prop: \"tffs\", label: \"通风方式\", minWidth: \"120\" },\n          { prop: \"lx\", label: \"配电室类型\", minWidth: \"120\" },\n          /*{\n              fixed: \"right\",\n              prop: 'operation',\n              label: '操作',\n              minWidth: '130px',\n              style: {display: 'block'},\n              operation: [\n                {name: '修改', clickFun: this.pdzgetUpdate},\n                {name: '详情', clickFun: this.pdzgetXq},\n              ]\n            },*/\n        ],\n      },\n      pdzsbzt: [\n        {\n          value: \"在运\",\n          label: \"在运\",\n        },\n        {\n          value: \"停止使用\",\n          label: \"停止使用\",\n        },\n        {\n          value: \"未就绪\",\n          label: \"未就绪\",\n        },\n        {\n          value: \"报废\",\n          label: \"报废\",\n        },\n      ],\n      pdslx: [\n        {\n          value: \"箱式变电站\",\n          label: \"箱式变电站\",\n        },\n        {\n          value: \"柱上变台变\",\n          label: \"柱上变台变\",\n        },\n        {\n          value: \"配电室\",\n          label: \"配电室\",\n        },\n      ],\n\n      //配电柜相关\n      bdgDialogFormVisible: false,\n      showButton: true,\n\n      pdgdisable:false,\n      //配电室下拉框\n      pdgOptionsDataList: [],\n      pdgkgytList: [],\n      pdgztList: [],\n      //弹出框表单\n      pdgform: {},\n      pdgParams: {\n        pageNum: 1,\n        pageSize: 10,\n      },\n      pdgfilterInfo: {\n        data: {\n          kggmc: \"\",\n          yxbh: \"\",\n        },\n        fieldList: [\n          { label: \"所属站室名称\", type: \"input\", value: \"sszsmc\" },\n          { label: \"所属站室运行编号\", type: \"input\", value: \"sszsyxbh\" },\n          { label: \"开关柜名称\", type: \"input\", value: \"kggmc\" },\n          { label: \"运行编号\", type: \"input\", value: \"yxbh\" },\n          {\n            label: \"投运日期\",\n            type: \"date\",\n            value: \"tyrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\",\n          },\n          { label: \"生产厂家\", type: \"input\", value: \"sccj\" },\n          { label: \"开关柜型号\", type: \"input\", value: \"kggxh\" },\n          {\n            label: \"出厂日期\",\n            type: \"date\",\n            value: \"tyrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\",\n          },\n          {\n            label: \"状态\",\n            type: \"select\",\n            value: \"zt\",\n            options: [\n              {\n                value: \"在运\",\n                label: \"在运\",\n              },\n              {\n                value: \"停止使用\",\n                label: \"停止使用\",\n              },\n              {\n                value: \"未就绪\",\n                label: \"未就绪\",\n              },\n              {\n                value: \"报废\",\n                label: \"报废\",\n              },\n              {\n                value: \"备用\",\n                label: \"备用\",\n              },\n            ],\n          },\n          { label: \"电压等级\", type: \"input\", value: \"dydj\" },\n        ],\n      },\n      tableAndPageInfo2: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n          pageResize: \"\",\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true,\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"sszsmc\", label: \"所属站室名称\", minWidth: \"180\" },\n          { prop: \"sszsyxbh\", label: \"所属站室运行编号\", minWidth: \"120\" },\n          { prop: \"kggmc\", label: \"开关柜名称\", minWidth: \"120\" },\n          { prop: \"yxbh\", label: \"运行编号\", minWidth: \"140\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"140\" },\n          { prop: \"sccj\", label: \"生产厂家\", minWidth: \"120\" },\n          { prop: \"kggxh\", label: \"开关柜型号\", minWidth: \"120\" },\n          { prop: \"ccrq\", label: \"出厂日期\", minWidth: \"120\" },\n          { prop: \"zt\", label: \"状态\", minWidth: \"180\" },\n          { prop: \"dydj\", label: \"电压等级\", minWidth: \"120\" },\n          /*{\n              fixed: \"right\",\n              prop: 'operation',\n              label: '操作',\n              minWidth: '130px',\n              style: {display: 'block'},\n              operation: [\n                {name: '修改', clickFun: this.pdgUpdate},\n                {name: '详情', clickFun: this.pdgDetails},\n              ]\n            },*/\n        ],\n      },\n      selectRows: [],\n      //配电室下拉框\n      pdsOptionsDataList: [],\n      //配电柜下拉框\n      sbOptionsDataList: [],\n      //设备类型\n      sblxOptionsDataSelected: [],\n      //左侧树筛选条件\n      treeForm: {},\n      //设备弹出框标题\n      sbtitle: \"\",\n      //状态变更信息\n      updateList: {\n        zt: \"\",\n        id: \"\",\n      },\n      //状态信息查询\n      resumeQuery: {\n        foreignNum: undefined,\n      },\n      dialogVisible: false,\n      //是否禁用\n      isDisabled: false,\n      //上传图片时的请求头\n      header: {},\n      //\n      tableDisabled:false,\n      //基本信息的状态\n      jbxxztList: [\n        {\n          value: \"在运\",\n          label: \"在运\",\n        },\n        {\n          value: \"停止使用\",\n          label: \"停止使用\",\n        },\n        {\n          value: \"未就绪\",\n          label: \"未就绪\",\n        },\n        {\n          value: \"报废\",\n          label: \"报废\",\n        },\n        {\n          value: \"备用\",\n          label: \"备用\",\n        },\n      ],\n      //开关用途\n      jbxxkgytList: [\n        {\n          value: \"进线\",\n          label: \"进线\",\n        },\n        {\n          value: \"出线\",\n          label: \"出线\",\n        },\n        {\n          value: \"联络\",\n          label: \"联络\",\n        },\n      ],\n\n      //操作机构型式\n      lzjgxsList: [\n        {\n          value: \"弹簧\",\n          label: \"弹簧\",\n        },\n        {\n          value: \"永磁\",\n          label: \"永磁\",\n        },\n      ],\n\n      //灭弧介质\n      mhjzList: [\n        {\n          value: \"空气\",\n          label: \"空气\",\n        },\n        {\n          value: \"充油\",\n          label: \"充油\",\n        },\n        {\n          value: \"真空\",\n          label: \"真空\",\n        },\n        {\n          value: \"SF6\",\n          label: \"SF6\",\n        },\n      ],\n\n      //绝缘介质\n      jyjzList: [\n        {\n          value: \"空气\",\n          label: \"空气\",\n        },\n        {\n          value: \"充油\",\n          label: \"充油\",\n        },\n        {\n          value: \"真空\",\n          label: \"真空\",\n        },\n        {\n          value: \"SF6\",\n          label: \"SF6\",\n        },\n      ],\n      //绝缘材质\n      jyczList: [\n        {\n          value: \"瓷\",\n          label: \"瓷\",\n        },\n        {\n          value: \"复合\",\n          label: \"复合\",\n        },\n      ],\n\n      //绝缘方式\n      jyfsList: [\n        {\n          value: \"油浸式\",\n          label: \"油浸式\",\n        },\n        {\n          value: \"干式\",\n          label: \"干式\",\n        },\n      ],\n      //操作方式\n      czfsList: [\n        {\n          value: \"手动\",\n          label: \"手动\",\n        },\n        {\n          value: \"自动\",\n          label: \"自动\",\n        },\n        {\n          value: \"手动/自动\",\n          label: \"手动/自动\",\n        },\n      ],\n      //操作方式\n      ttfsList: [\n        {\n          value: \"手动\",\n          label: \"手动\",\n        },\n        {\n          value: \"自动式\",\n          label: \"自动式\",\n        },\n      ],\n      //资产性质\n      zzxzList: [\n        {\n          value: \"公用\",\n          label: \"公用\",\n        },\n        {\n          value: \"专用\",\n          label: \"专用\",\n        },\n      ],\n      //母线型式\n      mxxsList: [\n        {\n          value: \"排式\",\n          label: \"排式\",\n        },\n        {\n          value: \"线式\",\n          label: \"线式\",\n        },\n        {\n          value: \"TMY\",\n          label: \"TMY\",\n        },\n        {\n          value: \"LMY\",\n          label: \"LMY\",\n        },\n      ],\n      //配电变压器类型\n      dyqlxList: [\n        {\n          value: \"降变压\",\n          label: \"降变压\",\n        },\n        {\n          value: \"升压\",\n          label: \"升压\",\n        },\n      ],\n      //配电变压器类型\n      jydjList: [\n        {\n          value: \"A\",\n          label: \"A\",\n        },\n        {\n          value: \"B\",\n          label: \"B\",\n        },\n        {\n          value: \"C\",\n          label: \"C\",\n        },\n        {\n          value: \"D\",\n          label: \"D\",\n        },\n        {\n          value: \"E\",\n          label: \"E\",\n        },\n        {\n          value: \"F\",\n          label: \"F\",\n        },\n        {\n          value: \"H\",\n          label: \"H\",\n        },\n      ],\n      rules: {\n        sblx: [\n          { required: true, message: \"请选择设备类型\", trigger: \"change\" },\n        ],\n        /*sszs: [{required: true, message: '所属站室', trigger: 'change'}],*/\n        //sskgg: [{required: true, message: '所属开关柜', trigger: 'change'}],\n        sbmc: [{ required: true, message: \"设备名称\", trigger: \"blur\" }],\n        yxbh: [{ required: true, message: \"请输入运行编号\", trigger: \"blur\" }],\n        tyrq: [{ required: true, message: \"投运日期\", trigger: \"change\" }],\n        zt: [{ required: true, message: \"状态\", trigger: \"change\" }],\n        dydj: [{ required: true, message: \"电压等级\", trigger: \"blur\" }],\n      },\n      //设备基本信息\n      filterInfo: {\n        data: {},\n        fieldList: [\n          {\n            label: \"所属站室名称\",\n            type: \"select\",\n            value: \"pdsList\",\n            multiple: true,\n            options: [],\n          },\n          {\n            label: \"所属站室运行编号\",\n            type: \"input\",\n            value: \"sszsyxbh\",\n            multiple: true,\n            options: [],\n          },\n          {\n            label: \"所属开关柜名称\",\n            type: \"input\",\n            value: \"sskggmc\",\n            options: [],\n          },\n          {\n            label: \"所属开关柜运行编号\",\n            type: \"input\",\n            value: \"sskggyxbh\",\n            options: [],\n          },\n          { label: \"设备名称\", type: \"input\", value: \"sbmc\" },\n          { label: \"运行编号\", type: \"input\", value: \"yxbh\" },\n          {\n            label: \"状态\",\n            type: \"select\",\n            value: \"ztList\",\n            multiple: true,\n            options: [\n              {\n                value: \"在运\",\n                label: \"在运\",\n              },\n              {\n                value: \"停止使用\",\n                label: \"停止使用\",\n              },\n              {\n                value: \"未就绪\",\n                label: \"未就绪\",\n              },\n              {\n                value: \"报废\",\n                label: \"报废\",\n              },\n              {\n                value: \"备用\",\n                label: \"备用\",\n              },\n            ],\n          },\n          {\n            label: \"投运日期\",\n            type: \"date\",\n            value: \"tyrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\",\n          },\n          { label: \"规格型号\", type: \"input\", value: \"ggxh\" },\n        ],\n      },\n      tableAndPageInfo3: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n          pageResize: \"\"\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true,\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"sszsmc\", label: \"所属站室名称\", minWidth: \"150\" },\n          { prop: \"sszsyxbh\", label: \"所属站室运行编号\", minWidth: \"150\" },\n          { prop: \"sskggmc\", label: \"所属开关柜名称\", minWidth: \"150\" },\n          { prop: \"sskggyxbh\", label: \"所属开关柜运行编号\", minWidth: \"150\" },\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"120\" },\n          { prop: \"yxbh\", label: \"运行编号\", minWidth: \"120\" },\n          { prop: \"zt\", label: \"状态\", minWidth: \"80\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"100\" },\n          { prop: \"ggxh\", label: \"规格型号\", minWidth: \"120\" },\n          /*{\n              fixed: 'right',\n              prop: 'operation',\n              label: '操作',\n              minWidth: '120px',\n              style: {display: 'block'},\n              operation: [\n                {name: '修改', clickFun: this.getSbXgButton},\n                {name: '详情', clickFun: this.getSbXqButton}\n              ]\n            }*/\n        ],\n      },\n      //状态变更记录信息\n      resumPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true,\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"foreignNum\", label: \"设备名称\", minWidth: \"120\" },\n          { prop: \"sblx\", label: \"设备类型\", minWidth: \"180\" },\n          { prop: \"bglx\", label: \"变更类型\", minWidth: \"120\" },\n          { prop: \"ms\", label: \"描述\", minWidth: \"230\" },\n          { prop: \"bgr\", label: \"变更人\", minWidth: \"140\" },\n          { prop: \"bgsj\", label: \"变更时间\", minWidth: \"160\" },\n        ],\n      },\n      params: {\n        id: \"\",\n        sbmc: \"\",\n        ggxh: \"\",\n        dydj: \"\",\n        tyrqStr: [],\n        sccj: \"\",\n        yxbh: \"\",\n        bz: \"\",\n        sszsmc: \"\",\n        sszsyxbh: \"\",\n        sskggmc: \"\",\n        sskggyxbh: \"\",\n        zt: \"\",\n        kgyt: \"\",\n      },\n\n      //技术参数基本信息\n      jscsForm: {},\n\n      //设备履历缺陷记录数据集合\n      sbllqxjlList: [\n        /*{\n            ssgs: '港东分公司',\n            dzmc: '1号变电站',\n            sblx: '主变压器',\n            qxxz: '严重',\n            dydj: '35kV',\n            sbxh: 'XXX型号',\n            sccj: 'XXX厂家'\n          }*/\n      ],\n      //设备履历试验记录数据\n      sblvsyjlList: [\n        /*{\n            syzy: '带电',\n            syxz: '例行试验',\n            symc: 'XXXXX',\n            gzdd: 'XXX平台',\n            sysb: '主变压器',\n            sybg: '',\n            tq: '晴',\n            syrq: '2022-01-01',\n            lrr: '张三',\n            syjl: 'XXXXX'\n          }*/\n      ],\n      //设备履历tab页\n      sbllDescTabName: \"syjl\",\n\n      //轮播图片\n      imgList: [],\n      //配电设备基本信息\n      jbxxForm: {},\n      //弹出框tab页\n      activeTabName: \"sbDesc\",\n      //设备弹出框\n      dialogFormVisible: false,\n      //加载信息\n      loading: false,\n      //设备履历\n      handleSbllDescTabNameClick: {},\n      // 单击下拉树选中的节点\n      selectNode: \"\",\n      //组织树\n      treeOptions: [],\n\n      //变电站挂接数据\n      newTestData: [],\n      jscsLabelList: [],\n      paramQuery: {\n        sblxbm: undefined,\n      },\n      sbParams: {},\n      pdgSszs:'',//配电柜所属站室id\n      pdgSszsmc:'',//配电柜所属站室名称\n    };\n  },\n  watch: {},\n  async created() {\n    await this.getYwbzList();\n    //初始化加载时加载所有变电站信息\n    this.newTestData = this.bdzList;\n    // this.getData();\n    await this.getpdzData(); //请求配电站数据\n    await this.initDictData();\n    await this.getOptions();//获取下拉框字典\n    this.getNewTreeInfo();\n    this.init();\n    this.getPdsOptionsDataList();\n    getPdgListSelected({}).then((res) => {\n      //所属配电柜筛查条件\n      this.filterInfo.fieldList[1].options = res.data;\n    });\n  },\n  mounted() {\n    //获取token\n    this.header.token = getToken();\n  },\n  methods: {\n    //获取下拉框字典值\n    async getOptions(){\n      await this.getPdgLx();//获取配电柜类型\n    },\n    //获取配电柜类型\n    async getPdgLx(){\n      await getDictTypeData('dwzy_pdg_kkyt').then(res=>{\n        res.data.forEach(item=>{\n          this.pdgkgytList.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    viewHeadDetail(){\n      switch (this.treeNode.identifier){\n        case \"1\"://配电室\n          this.pdzgetXq(this.headForm);\n          break;\n        case \"2\"://配电柜\n          this.pdgDetails(this.headForm);\n          break;\n      }\n    },\n    beforeUpload(file) {\n      const fileSize = file.size < 1024 * 1024 * 50;\n      if (!fileSize) {\n        this.$message.error(\"上传文件大小不能超过 50MB!\");\n      }\n    },\n    async deleteFileById(id) {\n      let { code } = await deleteById(id);\n      if (code === \"0000\") {\n        await this.getFileList();\n        this.$message({\n          type: \"success\",\n          message: \"文件删除成功!\",\n        });\n      }\n    },\n    submitUpload() {\n      this.$refs.upload.submit();\n    },\n    clearUpload() {\n      if (this.$refs.upload) {\n        this.$refs.upload.clearFiles();\n      }\n    },\n    async getFileList() {\n      let { code, data } = await getListByBusinessId({\n        businessId: this.pdzform.objId,\n      });\n      if (code === \"0000\") {\n        this.pdzform.attachment = data;\n        this.imgList = data.map((item) => {\n          let item1 = {};\n          item1.name = item.fileName;\n          item1.url = this.$store.getters.currHost + item.fileUrl;\n          return item1;\n        });\n      }\n    },\n    async getYwbzList() {\n      await selectDeptOneAndTwo({ parentId: 3013 }).then((res) => {\n        this.ywbzList = res.data;\n      });\n    },\n    //列表查询\n    async getData(params) {\n      if (this.pdzshow) {\n        this.getpdzData(params);\n      }\n      if (this.pdgshow) {\n        this.getpdgData(params);\n      }\n      if (this.sbshow) {\n        this.getsbData(params);\n      }\n    },\n    //新增按钮\n    AddSensorButton() {\n      if (this.pdzshow) {\n        this.pdzform = { attachment: [] };\n        this.clearUpload();\n        this.imgList = [];\n        this.isDisabled = false;\n        this.pdsDialogFormVisible = true;\n      }\n      if (this.pdgshow) {\n        this.pdgform.sszs = this.pdgSszsmc;//所属站室回显问题\n        this.isDisabled = false;\n        this.showButton = true;\n        this.bdgDialogFormVisible = true;\n        this.pdgdisable = false;//配电柜表单可编辑\n      }\n      if (this.sbshow) {\n        this.sbtitle = \"设备新增\";\n        this.dialogFormVisible = true;\n        this.jbxxForm.sszs = this.sszs\n        this.getPdgListSelected();\n        this.jbxxForm.sskgg = this.sskgg\n        this.isDisabled = false;\n      }\n    },\n    /*-----------------------配电站相关开始--------------------------*/\n    //表格多选框\n    pdzhandleSelectionChange(selection) {\n      this.ids = selection.map((item) => item.objId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    //配电站表格数据\n    async getpdzData(params) {\n      this.pdzParams = { ...this.pdzParams, ...params };\n      const param = this.pdzParams\n      await getPdsList(param).then((res) => {\n        this.pdzshow = true;\n        this.pdgshow = this.sbshow = false;\n        this.tableAndPageInfo1.tableData = res.data.records;\n        this.tableAndPageInfo1.tableData.forEach((item) => {\n          this.ywbzList.forEach((element) => {\n            if (item.ywbz == element.value) {\n              item.ywbzmc = element.label;\n            }\n          });\n        });\n        this.tableAndPageInfo1.pager.total = res.data.total;\n      });\n    },\n    //保存确定按钮\n    async getDetermine() {\n      let params={\n        lx:\"配电设备\",\n        mc:this.pdzform.pdsmc,\n      }\n      this.$refs[\"pdzform\"].validate((valid) => {\n        if (valid) {\n          addPds(this.pdzform).then((res) => {\n            if (res.code === \"0000\") {\n               //新增成功后发送通知\n               adddwzyfstz(params).then(res =>{\n                    if(res.code === \"0000\"){\n                  }\n                 });\n              this.uploadData.businessId = res.data.objId;\n              this.submitUpload();\n              this.$message.success(\"操作成功，通知已发送！！\");\n              this.pdsDialogFormVisible = false;\n              this.getpdzData();\n            }\n          });\n        } else {\n          setTimeout(() => {\n            var isError = document.getElementsByClassName(\"is-error\");\n            if (isError[0].querySelector(\"input\")) {\n              isError[0].querySelector(\"input\").focus();\n            } else if (isError[0].querySelector(\"textarea\")) {\n              isError[0].querySelector(\"textarea\").focus();\n            }\n          }, 1);\n          return false;\n        }\n      });\n    },\n    //关闭配电站弹框\n    handleClose() {\n      if (this.pdzshow) {\n        this.pdzform = {\n          attachment: [],\n        };\n        // this.$nextTick(() => {\n        //   this.pdzform = this.$options.data().form\n        //   this.resetForm('pdzform')\n        // })\n        this.pdsDialogFormVisible = false;\n      }\n      if (this.pdgshow) {\n        this.pdgform = {};\n        // this.$nextTick(() => {\n        //   this.pdgform = this.$options.data().form\n        //   this.resetForm('pdgform')\n        // })\n        this.bdgDialogFormVisible = false;\n      }\n    },\n    //修改\n    async pdzgetUpdate(row) {\n      this.clearUpload();\n      this.pdzform = { ...row };\n      this.pdzform.attachment = [];\n      await this.getFileList();\n      this.isDisabled = false;\n      this.pdsDialogFormVisible = true;\n    },\n    //详情\n    async pdzgetXq(row) {\n      this.clearUpload();\n      this.pdzform = { ...row };\n      this.pdzform.attachment = [];\n      await this.getFileList();\n      this.isDisabled = true;\n      this.pdsDialogFormVisible = true;\n    },\n    /*---------------------配电站相关结束----------------------*/\n\n    /*---------------------配电柜相关开始-----------------------*/\n    async initDictData() {\n      let { data: pdgzt } = await getDictTypeData(\"pdgzt\");\n      this.pdgztList = pdgzt;\n    },\n    async getpdgData(param) {\n      this.pdgParams = { ...this.pdgParams, ...param };\n      const par = this.pdgParams;\n      try {\n        let { data, code } = await getMergePdgInfo(par);\n        if (code === \"0000\") {\n          this.pdgshow = true;\n          this.pdzshow = this.sbshow = false;\n          this.tableAndPageInfo2.tableData = data.records;\n          this.tableAndPageInfo2.pager.total = data.total;\n          // this.tableAndPageInfo2.pager.pageResize = 'Y';\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //修改\n    pdgUpdate(row) {\n      if (row.sjlx === \"kgg\") {\n        getPdgOne({ objId: row.objId }).then((res) => {\n          if (res.code === \"0000\") {\n            this.bdgDialogFormVisible = true;\n            this.pdgform = res.data;\n            this.pdgform.sszs = this.pdgSszsmc;//处理回显\n            this.isDisabled = false;\n            this.showButton = true;\n            this.pdgdisable = false;//配电柜表单可编辑\n          } else {\n            this.$message.error(\"操作失败\");\n          }\n        });\n      } else if (row.sjlx === \"byq\") {\n        getPdsbOne({ id: row.objId }).then((res) => {\n          if (res.code === \"0000\") {\n            this.sbtitle = \"设备修改\";\n            this.dialogFormVisible = true;\n            this.jbxxForm = res.data;\n            this.isDisabled = false;\n            this.getPdgListSelected();\n            this.technicalParameters(row);\n          } else {\n            this.$message.error(\"操作失败\");\n          }\n        });\n      }\n    },\n    //详情\n    pdgDetails(row) {\n      if (row.sjlx === \"kgg\") {\n        getPdgOne({ objId: row.objId }).then((res) => {\n          if (res.code === \"0000\") {\n            this.bdgDialogFormVisible = true;\n            this.pdgform = res.data;\n            this.pdgform.sszs = this.pdgSszsmc;//回显处理\n            this.isDisabled = true;\n            this.showButton = false;\n            this.pdgdisable = true\n          } else {\n            this.$message.error(\"操作失败\");\n          }\n        });\n      } else if (row.sjlx === \"byq\") {\n        getPdsbOne({ id: row.objId }).then((res) => {\n          if (res.code === \"0000\") {\n            this.sbtitle = \"设备详情查看\";\n            this.dialogFormVisible = true;\n            this.jbxxForm = res.data;\n            this.isDisabled = true;\n            this.getPdgListSelected();\n            this.technicalParameters(row);\n          } else {\n            this.$message.error(\"操作失败\");\n          }\n        });\n      }\n    },\n    //保存修改内容\n    save() {\n      this.$refs.pdgform.validate((valid) => {\n        if (valid) {\n          this.pdgform.sszs = this.pdgSszs;\n          addPdg(this.pdgform).then((res) => {\n            if (res.code == \"0000\") {\n              this.$message.success(\"操作成功\");\n              this.bdgDialogFormVisible = false;\n              this.getpdgData();\n            } else {\n              this.$message.error(\"操作失败\");\n            }\n          });\n        } else {\n          setTimeout(() => {\n            var isError = document.getElementsByClassName(\"is-error\");\n            if (isError[0].querySelector(\"input\")) {\n              isError[0].querySelector(\"input\").focus();\n            } else if (isError[0].querySelector(\"textarea\")) {\n              isError[0].querySelector(\"textarea\").focus();\n            }\n          }, 1);\n          return false;\n        }\n      });\n    },\n\n    /*---------------------配电柜相关结束-----------------------*/\n\n    //设备表格数据\n    async getsbData(params) {\n      this.params = { ...this.params, ...params };\n      const param = this.params;\n      //投用日期范围查询\n      if (param.tyrqStr && param.tyrqStr.length > 0) {\n        param.tyBeginDate = this.dateFormatter(param.tyrqStr[0]);\n        param.tyEndDate = this.dateFormatter(param.tyrqStr[1]);\n      }\n      const { data, code } = await getList(param);\n      if (code === \"0000\") {\n        this.sbshow = true;\n        this.pdgshow = this.pdzshow = false;\n        this.tableAndPageInfo3.tableData = data.records;\n        this.tableAndPageInfo3.pager.total = data.total;\n        // this.tableAndPageInfo3.pager.pageResize = 'Y';\n      }\n    },\n    //获取配电室下拉框数据\n    getPdsOptionsDataList() {\n      getPdsOptionsDataList({}).then((res) => {\n        this.pdsOptionsDataList = res.data\n        //所属配电室筛查条件\n        this.filterInfo.fieldList[0].options = this.pdsOptionsDataList;\n      });\n    },\n    //获取配电柜下拉框数据\n    getPdgListSelected() {\n      getPdgListSelected({ sszs: this.jbxxForm.sszs }).then((res) => {\n        this.sbOptionsDataList = res.data;\n      });\n    },\n    //配电室下拉框中的change事件\n    pdsOptionsChangeClick() {\n      //当发生change事件时先清空之前的信息\n      this.$set(this.jbxxForm, \"sskgg\", \"\");\n      this.getPdgListSelected();\n    },\n    //日期格式化  yyyy-MM-dd\n    dateFormatter(d) {\n      let year = d.getFullYear();\n      let month =\n        d.getMonth() < 9 ? \"0\" + (d.getMonth() + 1) : \"\" + (d.getMonth() + 1);\n      let day = d.getDate() < 10 ? \"0\" + d.getDate() : \"\" + d.getDate();\n      return year + \"-\" + month + \"-\" + day;\n    },\n\n    /*----------------------设备---------------------*/\n    //设备添加按钮\n    sbAddSensorButton() {\n      this.sbtitle = \"设备新增\";\n      this.dialogFormVisible = true;\n      this.isDisabled = false;\n    },\n    //设备基本信息修改\n    getSbXgButton(row) {\n      getPdsbOne({ id: row.id }).then((res) => {\n        if (res.code === \"0000\") {\n          this.sbtitle = \"设备修改\";\n          this.dialogFormVisible = true;\n          this.jbxxForm = res.data;\n          this.isDisabled = false;\n          this.getPdgListSelected();\n          this.technicalParameters(row);\n        } else {\n          this.$message.error(\"操作失败\");\n        }\n      });\n    },\n    //设备详情按钮\n    getSbXqButton(row) {\n      getPdsbOne({ id: row.id }).then((res) => {\n        if (res.code === \"0000\") {\n          this.sbtitle = \"设备详情查看\";\n          this.dialogFormVisible = true;\n          this.jbxxForm = res.data;\n          this.isDisabled = true;\n          this.getPdgListSelected();\n          this.technicalParameters(row);\n        } else {\n          this.$message.error(\"操作失败\");\n        }\n      });\n    },\n\n    /*----------------------配电室---------------------*/\n    //删除按钮\n    //删除配电柜\n    removepdg(id) {\n      // if (this.ids.length !== 0) {\n        let obj=[];\n      obj.push(id);\n        this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\",\n        })\n        .then(() => {\n          removePdg(obj).then(({ code }) => {\n              if (code === \"0000\") {\n                this.$message({\n                  type: \"success\",\n                  message: \"删除成功!\",\n                });\n                this.getpdgData();\n              } else {\n                this.$message({\n                  type: \"error\",\n                  message: \"删除失败!\",\n                });\n              }\n            });\n          })\n         .catch(() => {\n            this.$message({\n              type: \"info\",\n              message: \"已取消删除\",\n            });\n          });\n      // } else {\n      //   this.$message({\n      //     type: \"info\",\n      //     message: \"请选择至少一条数据!\",\n      //   });\n      // }\n    },\n    //删除配电室\n    async deletePds(id) {\n      let obj=[];\n      obj.push(id);\n      // if (this.ids.length != 0) {\n        this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\",\n        })\n          .then(() => {\n            removePds(obj).then(({ code }) => {\n              if (code === \"0000\") {\n                this.$message({\n                  type: \"success\",\n                  message: \"删除成功!\",\n                });\n                this.getpdzData();\n              } else {\n                this.$message({\n                  type: \"error\",\n                  message: \"删除失败!\",\n                });\n              }\n            });\n          })\n          .catch(() => {\n            this.$message({\n              type: \"info\",\n              message: \"已取消删除\",\n            });\n          });\n      // }\n    },\n    //删除设备\n    removesb(id) {\n      // if (this.selectRows.length < 1) {\n      //   this.$message.warning(\"请选择正确的数据！！！\");\n      //   return;\n      // }\n      // let ids = this.selectRows.map((item) => {\n      //   return item.id;\n      // });\n      let obj=[];\n      obj.push(id);\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      }).then(() => {\n        remove(obj)\n          .then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\",\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\",\n              });\n            }\n          })\n          .catch(() => {\n            this.$message({\n              type: \"info\",\n              message: \"已取消删除\",\n            });\n          });\n        this.getsbData();\n      });\n    },\n    async deleteRow(row) {\n      if (this.pdzshow) {\n        this.deletePds(row);\n      }\n      if (this.pdgshow) {\n        this.removepdg(row);\n      }\n      if (this.sbshow) {\n        this.removesb(row);\n      }\n    },\n    //获取HeadForm数据\n    async getHeadFormData(sbId){\n      if(this.treeNode.identifier === \"1\"){//配电站\n        await getPdsList({objId:sbId}).then((res) => {\n          this.headForm = res.data.records[0];\n          this.ywbzList.forEach((element) => {\n            if (this.headForm.ywbz == element.value) {\n              this.headForm.ywbzmc = element.label;\n            }\n          });\n        });\n      }else if(this.treeNode.identifier === \"2\"){//配电柜\n        let { data, code } = await getMergePdgInfo({objId:sbId});\n        if (code === \"0000\") {\n          this.headForm = data.records[0];\n          if (this.headForm.sjlx === \"kgg\"){\n            this.sskgg = this.headForm.objId\n          }\n        }\n      }\n    },\n    //树点击事件\n    handleNodeClick(data, e) {\n      this.treeNode = data;\n      //点击根节点显示所有\n      if (data.identifier === \"0\") {\n        this.pdzParams = {\n          pageNum: 1,\n          pageSize: 10,\n        }\n        this.tableAndPageInfo1.pager.pageResize = 'Y';\n        this.getpdzData();\n      }\n      //点击配电室节点显示附属设备（只归属与配电室不归属任何配电柜的设备）\n      if (data.identifier === \"1\") {\n        //重置Currentpage查询\n        this.pdgParams = {\n          pageNum: 1,\n          pageSize: 10,\n        }\n        this.sszs = data.id;\n        this.pdgSszs = data.id;\n        this.pdgSszsmc = data.label;\n        this.getHeadFormData(data.id);\n        this.pdgParams.sszs = this.sszs;\n        //重置pageNum显示\n        this.tableAndPageInfo2.pager.pageResize = 'Y';\n        this.getpdgData();\n      }\n      //点击配电柜节点显示所属配电设备\n      if (data.identifier === \"2\") {\n        this.params = {\n          pageNum: 1,\n          pageSize: 10,\n        }\n        this.params.pdgList = [data.id];\n        this.params.sszs = this.sszs;\n        this.params.sskgg = data.id;\n        this.sskgg=\"\"\n        this.getHeadFormData(data.id);\n        this.tableAndPageInfo3.pager.pageResize = 'Y';\n        this.getsbData();\n      }\n    },\n    //重置按钮\n    filterReset() {},\n    /*----------------------状态变更---------------------*/\n    //修改设备状态\n    updateStatus(row) {\n      this.updateList.zt = row.zt;\n      this.updateList.id = row.id;\n      this.dialogVisible = true;\n    },\n    //状态变更提交信息\n    submitStatus() {\n      this.$confirm(\"确认将设备状态修改为\" + this.updateList.zt + \"?\", \"\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      }).then((res) => {\n        updateStatus(this.updateList).then((res) => {\n          if (res.code == \"0000\") {\n            this.$message.success(\"设备状态已变更！\");\n            this.dialogVisible = false;\n            this.getData();\n          }\n        });\n      });\n    },\n    //查询状态变更记录信息\n    getResumList(par) {\n      let params = { ...par, ...this.resumeQuery };\n      getResumDataList(params).then((res) => {\n        this.resumPageInfo.tableData = res.data.records;\n        this.resumPageInfo.pager.total = res.data.total;\n      });\n    },\n    //左侧树形数据获取\n    getNewTreeInfo() {\n      getPdsTreeList(this.treeForm).then((res) => {\n        this.treeOptions = res.data;\n      });\n    },\n    //筛选条件\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n    //重置表单\n    resetForm() {\n      this.jbxxForm = {};\n      this.jscsForm = {};\n      this.$nextTick(function () {\n        this.$refs[\"jbxxForm\"].clearValidate();\n      });\n      this.dialogFormVisible = false;\n    },\n    getSblxDataListSelected() {\n      let sblxParam = {\n        type: \"配电设备\",\n      };\n      getSblxDataListSelected(sblxParam).then((res) => {\n        this.sblxOptionsDataSelected = res.data;\n      });\n    },\n    showParams(data) {\n      this.paramQuery.sblxbm = data;\n      this.getParameters();\n    },\n    technicalParameters(row) {\n      //设备类型\n      this.jscsForm = {};\n      this.paramQuery.sblxbm = row.sblx;\n      this.jscsForm.sblxbm = row.sblx;\n      this.jscsForm.sbbm = row.sszsyxbh;\n      this.getParameters();\n    },\n    getParameters() {\n      this.jscsLabelList = [];\n      getParamDataList(this.paramQuery).then((res) => {\n        this.jscsLabelList = res.data;\n        this.getParamValue();\n      });\n    },\n\n    getParamValue() {\n      getParamsValue(this.jscsForm).then((res) => {\n        if (res.data != \"\") {\n          this.jscsForm = { ...res.data[0] };\n        }\n      });\n    },\n    submit() {\n      try {\n        this.jbxxForm.sbClassCsValue = this.jscsForm;\n        let { code } = saveOrUpdate(this.jbxxForm).then((res) => {\n          if (res.code === \"0000\") {\n            this.$message({\n              type: \"success\",\n              message: \"操作成功!\",\n            });\n            this.dialogFormVisible = false;\n            this.getData();\n          }\n        });\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    init() {\n      this.getSblxDataListSelected();\n    },\n  },\n};\n</script>\n\n<style lang='scss' scoped>\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  height: calc(100vh - 225px);\n  overflow: auto;\n}\n\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.box-cardList {\n  height: 70%;\n}\n\n.item {\n  width: 200px;\n  float: left;\n}\n\n#main_container_dj {\n  height: calc(100vh - 84px);\n}\n\n.aside_height {\n  height: 81vh;\n}\n\n.defect .el-form-item:nth-child(odd) {\n  margin-right: 70px;\n}\n\n/*背景颜色调整*/\n#main_container_dj,\n#main_container_dj .el-aside {\n  background-color: #b4caf1;\n}\n\n/deep/ .qxlr_dialog_insert .el-dialog__header {\n  background-color: #0cc283;\n}\n\n/deep/ .pmyBtn {\n  background: #0cc283;\n}\n\n/*!*弹出框内宽度设置*!*/\n.el-select {\n  width: 100%;\n}\n\n.el-date-editor {\n  width: 100%;\n}\n\n.el-carousel__item h3 {\n  color: #475669;\n  font-size: 14px;\n  opacity: 0.75;\n  line-height: 150px;\n}\n\n.el-carousel__item:nth-child(2n) {\n  background-color: #99a9bf;\n}\n\n.el-carousel__item:nth-child(2n + 1) {\n  background-color: #d3dce6;\n}\n\n//有子节点 且未展开\n.el-tree ::v-deep .el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/add.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n\n//有子节点 且已展开\n.el-tree\n  ::v-deep\n  .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/prep.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n\n//没有子节点\n.el-tree ::v-deep .el-tree-node__expand-icon.is-leaf::before {\n  background: transparent;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n\n.categoryTreeIcons {\n  background: url(\"../../../../assets/icons/icon/icon1.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n\n.tableIcon {\n  background: url(\"../../../../assets/icons/icon/icon2.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n\n.classIcon {\n  background: url(\"../../../../assets/icons/icon/icon5.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n\n.imgCls {\n  height: 150px !important;\n}\n\n#pic_form .el-form-item__content {\n  margin-left: 0 !important;\n}\n</style>\n<style>\n#imgId .el-carousel__container {\n  background-color: #ffffff !important;\n}\n</style>\n"], "sourceRoot": "src/views/dagangOilfield/dwzygl/pdgl"}]}