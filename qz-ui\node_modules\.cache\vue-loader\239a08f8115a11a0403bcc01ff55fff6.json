{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\bddzcz\\components\\czp_tj.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\bddzcz\\components\\czp_tj.vue", "mtime": 1706897324125}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["czp_tj.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAuBA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "czp_tj.vue", "sourceRoot": "src/views/dagangOilfield/czpgl/bddzcz/components", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 110, itemWidth: 220 }\"\n      @handleEvent=\"handleEvent\"\n    />\n\n    <el-white class=\"button-group\">\n      <div>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"selectChange\"\n          height=\"67.5vh\"\n          v-loading=\"loading\"\n        />\n      </div>\n    </el-white>\n  </div>\n</template>\n\n<script>\nimport { getListTj, getBdzSelectList } from \"@/api/yxgl/bdyxgl/bddzczp\";\nimport { getFgsOptions } from \"@/api/yxgl/bdyxgl/zbgl\";\n\nexport default {\n  name: \"czp_tj\",\n  mounted() {\n    //列表查询\n    this.getData();\n    //获取变电站下拉框数据\n    this.getBdzSelectList();\n    this.getFgsOptions();\n  },\n  data() {\n    return {\n      organizationSelectedList: [],\n      loading: false,\n      // 查询变电站列表\n      bdzList: {},\n      //查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      // 查询数据总条数\n      total: 0,\n      filterInfo: {\n        data: {\n          fgs: \"\",\n          bdzmc: \"\"\n        }, //查询条件\n        fieldList: [\n          {\n            label: \"分公司\",\n            value: \"fgs\",\n            type: \"select\",\n            clearable: true,\n            options: [\n            ]\n          },\n          {\n            label: \"变电站名称\",\n            value: \"bdzmc\",\n            type: \"select\",\n            clearable: true,\n            filterable: true,\n            options: []\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"分公司\", prop: \"deptname\", minWidth: \"120\" },\n          { label: \"变电站\", prop: \"bdzmcs\", minWidth: \"150\" },\n          { label: \"操作任务\", prop: \"czrw\", minWidth: \"100\" },\n          { label: \"操作项数\", prop: \"czxs\", minWidth: \"100\" },\n          { label: \"已执行项数\", prop: \"yzxczxs\", minWidth: \"100\" },\n          { label: \"未执行项数\", prop: \"wzxczxs\", minWidth: \"100\" }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        lx: 2,\n        sfbj: 1\n      }\n    };\n  },\n  methods: {\n    /**\n     * 获取分公司下拉数据\n     */\n    getFgsOptions() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.organizationSelectedList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"fgs\") {\n            return (item.options = this.organizationSelectedList);\n          }\n        });\n      });\n    },\n    //统计\n    async getData(params) {\n      try {\n        const param = { ...this.params, ...params };\n        this.loading = true;\n        const { data, code } = await getListTj(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          this.loading = false;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    /**\n     * 列表选中\n     * */\n    selectChange(selection) {\n      this.ids = selection.map(item => item.objId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /**\n     * 获取变电站下拉框数据\n     */\n    getBdzSelectList() {\n      getBdzSelectList({}).then(res => {\n        this.bdzList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"bdzmc\") {\n            return (item.options = this.bdzList);\n          }\n        });\n      });\n    },\n    //下拉框change事件\n    handleEvent(val) {\n      //变电站查询下拉框选项根据所选分公司带出来\n      if (val.label === \"fgs\" && val.value && val.value !== \"\") {\n        let form = {\n          ssdwbm: val.value\n        };\n        getBdzSelectList(form).then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value == \"bdzmc\") {\n              return (item.options = res.data);\n            }\n          });\n        });\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n/*列表颜色设置*/\n/deep/ .el-table th {\n  background-color: #e8f7f0;\n}\n</style>\n"]}]}