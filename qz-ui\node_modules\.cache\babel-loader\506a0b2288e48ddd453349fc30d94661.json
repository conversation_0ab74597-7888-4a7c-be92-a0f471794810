{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\hsflkwh.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\hsflkwh.js", "mtime": 1706897313919}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0UGFnZURhdGFMaXN0ID0gZ2V0UGFnZURhdGFMaXN0OwpleHBvcnRzLmdldERhdGFMaXN0ID0gZ2V0RGF0YUxpc3Q7CmV4cG9ydHMucmVtb3ZlID0gcmVtb3ZlOwpleHBvcnRzLnNhdmVPclVwZGF0ZSA9IHNhdmVPclVwZGF0ZTsKCnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwoKdmFyIGJhc2VVcmwgPSAiL2NvbmRpdGlvbi1tYWludGVuYW5jZS1hcGkiOwovKioNCiAqIOafpeivouWHveaVsOW6k+WIhuexu+WIl+ihqA0KICogQHBhcmFtIHF1ZXJ5IA0KICogQHJldHVybnMgDQogKi8KCmZ1bmN0aW9uIGdldFBhZ2VEYXRhTGlzdChxdWVyeSkgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3RQb3N0KGJhc2VVcmwgKyAnL2hzZmxrL2dldE13dFVkR3lIc2Zsa0xpc3QnLCBxdWVyeSwgMik7Cn0KLyoqDQogKiDkuI3luKbliIbpobXmn6Xor6INCiAqIEBwYXJhbSB7Kn0gcXVlcnkgDQogKiBAcmV0dXJucyANCiAqLwoKCmZ1bmN0aW9uIGdldERhdGFMaXN0KHF1ZXJ5KSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcvaHNmbGsvTXd0VWRHeUhzZmxrTGlzdCcsIHF1ZXJ5LCAyKTsKfQovKioNCiAqIOWIoOmZpOS4gOadoeaIluiAheWkmuadoeWHveaVsOW6k+WIhuexuw0KICogQHBhcmFtIHF1ZXJ5IA0KICogQHJldHVybnMgDQogKi8KCgpmdW5jdGlvbiByZW1vdmUocXVlcnkpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy9oc2Zsay9kZWxldGVIc2ZsaycsIHF1ZXJ5LCAyKTsKfQovKioNCiAqIOa3u+WKoOaIluiAheS/ruaUueWHveaVsOW6k+WIhuexuw0KICogQHBhcmFtICBxdWVyeSANCiAqIEByZXR1cm5zIA0KICovCgoKZnVuY3Rpb24gc2F2ZU9yVXBkYXRlKHF1ZXJ5KSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcvaHNmbGsvc2F2ZU9yVXBkYXRlJywgcXVlcnksIDIpOwp9"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagangOilfield/bzgl/hsflkwh.js"], "names": ["baseUrl", "getPageDataList", "query", "api", "requestPost", "getDataList", "remove", "saveOrUpdate"], "mappings": ";;;;;;;;;;;;AAAA;;AAEA,IAAMA,OAAO,GAAG,4BAAhB;AAEA;;;;;;AAKO,SAASC,eAAT,CAAyBC,KAAzB,EAA+B;AAClC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,4BAAxB,EAAqDE,KAArD,EAA4D,CAA5D,CAAP;AACH;AAED;;;;;;;AAKO,SAASG,WAAT,CAAqBH,KAArB,EAA2B;AAC9B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,yBAAxB,EAAkDE,KAAlD,EAAyD,CAAzD,CAAP;AACH;AAED;;;;;;;AAKO,SAASI,MAAT,CAAgBJ,KAAhB,EAAsB;AACzB,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,oBAAxB,EAA6CE,KAA7C,EAAoD,CAApD,CAAP;AACH;AAED;;;;;;;AAKO,SAASK,YAAT,CAAsBL,KAAtB,EAA4B;AAC/B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,qBAAxB,EAA8CE,KAA9C,EAAqD,CAArD,CAAP;AACH", "sourcesContent": ["import api from '@/utils/request'\r\n\r\nconst baseUrl = \"/condition-maintenance-api\";\r\n\r\n/**\r\n * 查询函数库分类列表\r\n * @param query \r\n * @returns \r\n */\r\nexport function getPageDataList(query){\r\n    return api.requestPost(baseUrl+'/hsflk/getMwtUdGyHsflkList',query, 2)\r\n}\r\n\r\n/**\r\n * 不带分页查询\r\n * @param {*} query \r\n * @returns \r\n */\r\nexport function getDataList(query){\r\n    return api.requestPost(baseUrl+'/hsflk/MwtUdGyHsflkList',query, 2)\r\n}\r\n\r\n/**\r\n * 删除一条或者多条函数库分类\r\n * @param query \r\n * @returns \r\n */\r\nexport function remove(query){\r\n    return api.requestPost(baseUrl+'/hsflk/deleteHsflk',query, 2)\r\n}\r\n\r\n/**\r\n * 添加或者修改函数库分类\r\n * @param  query \r\n * @returns \r\n */\r\nexport function saveOrUpdate(query){\r\n    return api.requestPost(baseUrl+'/hsflk/saveOrUpdate',query, 2)\r\n}"]}]}