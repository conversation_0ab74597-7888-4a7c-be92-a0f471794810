{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\pdgl\\pdsbgl.vue?vue&type=style&index=1&lang=css&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\pdgl\\pdsbgl.vue", "mtime": 1706897324785}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCiNpbWdJZCAuZWwtY2Fyb3VzZWxfX2NvbnRhaW5lciB7CiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZmZmZiAhaW1wb3J0YW50Owp9Cg=="}, {"version": 3, "sources": ["pdsbgl.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAu4GA;AACA;AACA", "file": "pdsbgl.vue", "sourceRoot": "src/views/dagangOilfield/dwzygl/pdgl", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"1\">\n      <el-col :span=\"5\">\n        <el-card shadow=\"never\" style=\"background: #e0f8ed; padding-top: 10px\">\n          <div style=\"overflow: auto; height: 89vh\">\n            <el-col>\n              <el-tree\n                :expand-on-click-node=\"true\"\n                highlight-current\n                ref=\"tree\"\n                id=\"tree\"\n                :data=\"treeOptions\"\n                :default-expanded-keys=\"['0']\"\n                @node-click=\"handleNodeClick\"\n                node-key=\"id\"\n                accordion\n              >\n                <span slot-scope=\"{ node, data }\">\n                  <i :class=\"icons[data.icon]\" />\n                  <span style=\"margin-left: 5px\" :title=\"data.label\">{{\n                    data.label\n                  }}</span>\n                </span>\n              </el-tree>\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--右侧列表-->\n      <el-col :span=\"19\" v-if=\"treeNode.identifier && treeNode.identifier !== '0'\" style=\"height: 160px;\">\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button\n              @click=\"viewHeadDetail\"\n              type=\"primary\"\n            >\n              详情\n            </el-button>\n          </div>\n          <div>\n            <!--     配电室基本信息     -->\n            <el-form\n              :model=\"headForm\"\n              label-width=\"120px\"\n              v-if=\"treeNode.identifier === '1'\"\n            >\n              <el-row :gutter=\"20\">\n                <el-col :span=\"6\">\n                  <el-form-item label=\"所属线路名称:\">\n                    <el-input\n                      v-model=\"headForm.ssxlmc\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"所属线路编号:\">\n                    <el-input\n                      v-model=\"headForm.ssxlbh\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"所属线段名称:\">\n                    <el-input\n                      v-model=\"headForm.ssxdmc\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"配电室名称:\">\n                    <el-input\n                      v-model=\"headForm.pdsmc\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n              <el-row :gutter=\"15\">\n                <el-col :span=\"6\">\n                  <el-form-item label=\"运行编号：\">\n                    <el-input\n                      v-model=\"headForm.yxbh\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"运维班组：\">\n                    <el-input\n                      v-model=\"headForm.ywbzmc\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"投运日期：\">\n                    <el-input\n                      v-model=\"headForm.tyrq\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"是否具有环网：\">\n                    <el-input\n                      v-model=\"headForm.sfjyhw\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-row>\n            </el-form>\n            <!--     配电柜基本信息     -->\n            <el-form\n              :model=\"headForm\"\n              label-width=\"140px\"\n              v-if=\"treeNode.identifier === '2'\"\n            >\n              <el-row :gutter=\"20\">\n                <el-col :span=\"6\">\n                  <el-form-item label=\"所属站室名称:\">\n                    <el-input\n                      v-model=\"headForm.sszsmc\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"所属站室运行编号:\">\n                    <el-input\n                      v-model=\"headForm.sszsyxbh\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"开关柜名称:\">\n                    <el-input\n                      v-model=\"headForm.kggmc\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"运行编号:\">\n                    <el-input\n                      v-model=\"headForm.yxbh\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-row>\n\n              <el-row :gutter=\"15\">\n                <el-col :span=\"6\">\n                  <el-form-item label=\"投运日期：\">\n                    <el-input\n                      v-model=\"headForm.tyrq\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"生产厂家：\">\n                    <el-input\n                      v-model=\"headForm.sccj\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"开关柜型号：\">\n                    <el-input\n                      v-model=\"headForm.kggxh\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n\n                <el-col :span=\"6\">\n                  <el-form-item label=\"出厂日期：\">\n                    <el-input\n                      v-model=\"headForm.ccrq\"\n                      disabled=\"disabled\"\n                    />\n                  </el-form-item>\n                </el-col>\n              </el-row>\n            </el-form>\n          </div>\n        </el-white>\n      </el-col>\n        <!-- <el-filter\n          ref=\"filter1\"\n          :data=\"pdzfilterInfo.data\"\n          :field-list=\"pdzfilterInfo.fieldList\"\n          :width=\"{ labelWidth: 120, itemWidth: 180 }\"\n          @handleReset=\"filterReset\"\n          v-show=\"pdzshow\"\n        />\n        <el-filter\n          ref=\"filter1\"\n          :data=\"pdgfilterInfo.data\"\n          :field-list=\"pdgfilterInfo.fieldList\"\n          :width=\"{ labelWidth: 120, itemWidth: 180 }\"\n          v-show=\"pdgshow\"\n        />\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :width=\"{ labelWidth: 140, itemWidth: 190 }\"\n          @handleReset=\"filterReset\"\n          v-show=\"sbshow\"\n        /> -->\n      <el-col :span=\"19\">\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button\n              icon=\"el-icon-plus\"\n              @click=\"AddSensorButton\"\n              v-hasPermi=\"['pdsbtz:button:add']\"\n              type=\"primary\"\n            >\n              新增\n            </el-button>\n\n          </div>\n\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo1\"\n            @update:multipleSelection=\"pdzhandleSelectionChange\"\n            height=\"78.2vh\"\n            v-show=\"pdzshow\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"pdzgetUpdate(scope.row)\"\n                  v-hasPermi=\"['pdsbtz:button:update']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                >\n                </el-button>\n                <el-button\n                  @click=\"pdzgetXq(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n                <el-button title=\"删除\" icon=\"el-icon-delete\" v-if=\"scope.row.createBy === $store.getters.name\"\n                  v-hasPermi=\"['pdsbtz:button:delete']\" type=\"text\" @click=\"deleteRow(scope.row.objId)\">\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo2\"\n            @update:multipleSelection=\"pdzhandleSelectionChange\"\n            height=\"61.4vh\"\n            v-show=\"pdgshow\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"pdgUpdate(scope.row)\"\n                  v-hasPermi=\"['pdsbtz:button:update']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                >\n                </el-button>\n                <el-button\n                  @click=\"pdgDetails(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n                <el-button title=\"删除\" icon=\"el-icon-delete\"\n                  v-hasPermi=\"['pdsbtz:button:delete']\" type=\"text\" @click=\"deleteRow(scope.row.objId)\">\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo3\"\n            @update:multipleSelection=\"selectChange\"\n            height=\"61.4vh\"\n            v-show=\"sbshow\"\n          >\n            <el-table-column\n              slot=\"table_eight\"\n              align=\"center\"\n              fixed=\"right\"\n              style=\"display: block\"\n              label=\"操作\"\n              width=\"160\"\n              :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  @click=\"getSbXgButton(scope.row)\"\n                  v-hasPermi=\"['pdsbtz:button:update']\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"修改\"\n                  class=\"el-icon-edit\"\n                >\n                </el-button>\n                <el-button\n                  @click=\"getSbXqButton(scope.row)\"\n                  type=\"text\"\n                  size=\"small\"\n                  title=\"详情\"\n                  class=\"el-icon-view\"\n                ></el-button>\n                <el-button title=\"删除\" icon=\"el-icon-delete\" v-if=\"scope.row.createBy === $store.getters.name\"\n                  v-hasPermi=\"['pdsbtz:button:delete']\" type=\"text\" @click=\"deleteRow(scope.row.objId)\"></el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!--配电室新增、修改、详情弹框-->\n    <el-dialog\n    v-dialogDrag\n      title=\"配电站\"\n      :visible.sync=\"pdsDialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"70%\"\n      @close=\"handleClose\"\n    >\n      <el-form\n        ref=\"pdzform\"\n        :model=\"pdzform\"\n        :disabled=\"isDisabled\"\n        :rules=\"rules\"\n        label-width=\"130px\"\n      >\n        <div\n          class=\"block\"\n          style=\"width: 50%; height: 50%; margin-bottom: 2%; float: right\"\n        >\n          <span class=\"demonstration\">配电站图片</span>\n          <el-carousel\n            trigger=\"click\"\n            class=\"imgCls\"\n            indicator-position=\"none\"\n            :interval=\"2000\"\n            type=\"card\"\n            id=\"imgId\"\n          >\n            <el-carousel-item v-for=\"(img, index) in imgList\" :key=\"index\">\n              <viewer :images=\"imgList\">\n                <img :src=\"img.url\" width=\"100%\" height=\"100%\"/>\n              </viewer>\n            </el-carousel-item>\n          </el-carousel>\n        </div>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item  label=\"配电室类型：\" prop=\"lx\">\n              <el-select\n                v-model=\"pdzform.lx\"\n                placeholder=\"请选择配电室类型\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in pdslx\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item  label=\"所属线路名称：\" prop=\"ssxlmc\">\n              <el-input\n                v-model=\"pdzform.ssxlmc\"\n                placeholder=\"请输入所属线路名称\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属线路编号：\" prop=\"ssxlbh\">\n              <el-input\n                v-model=\"pdzform.ssxlbh\"\n                placeholder=\"请输入所属线路编号\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属线段名称：\" prop=\"ssxdmc\">\n              <el-input\n                v-model=\"pdzform.ssxdmc\"\n                placeholder=\"请输入所属线段名称\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"配电室名称：\" prop=\"pdsmc\">\n              <el-input\n                v-model=\"pdzform.pdsmc\"\n                placeholder=\"请输入配电室名称\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"运行编号：\" prop=\"yxbh\">\n              <el-input\n                v-model=\"pdzform.yxbh\"\n                placeholder=\"请输入运行编号\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"运维班组：\" prop=\"ywbz\">\n              <el-select\n                v-model=\"pdzform.ywbz\"\n                placeholder=\"请选择运维班组\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in ywbzList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"投运日期：\" prop=\"tyrq\">\n              <el-date-picker\n                type=\"date\"\n                placeholder=\"选择日期\"\n                value-format=\"yyyy-MM-dd\"\n                v-model=\"pdzform.tyrq\"\n                style=\"width: 100%\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"状态：\" prop=\"zt\">\n              <el-select v-model=\"pdzform.zt\" placeholder=\"请选择状态\">\n                <el-option\n                  v-for=\"item in pdzsbzt\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item v-if=\"pdzform.lx == '箱式变电站'\"  label=\"生产厂家：\" prop=\"sccj\">\n              <el-input v-model=\"pdzform.sccj\" placeholder=\"请输入生产厂家\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"通风方式：\" prop=\"tffs\">\n              <el-input\n                v-model=\"pdzform.tffs\"\n                placeholder=\"请输入通风方式\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"站变用：\" prop=\"zby\">\n              <el-input-number\n                v-model=\"pdzform.zby\"\n                :min=\"0\"\n                placeholder=\"请输入站变用\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item v-if=\"!(pdzform.lx == '柱上变台变')\"  label=\"高压进线柜数量：\" prop=\"gyjxgsl\">\n              <el-input-number\n                v-model=\"pdzform.gyjxgsl\"\n                :min=\"0\"\n                placeholder=\"请输入高压进线柜数量\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"高压计量柜数量：\" prop=\"gyjlgsl\">\n              <el-input-number\n                v-model=\"pdzform.gyjlgsl\"\n                :min=\"0\"\n                placeholder=\"请输入高压计量柜数量\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item v-if=\"!(pdzform.lx == '柱上变台变')\" label=\"高压出线柜数量：\" prop=\"gycxgsl\">\n              <el-input-number\n                v-model=\"pdzform.gycxgsl\"\n                :min=\"0\"\n                placeholder=\"请输入高压出线柜数量\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"PT柜数量：\" prop=\"ptgsl\">\n              <el-input-number\n                v-model=\"pdzform.ptgsl\"\n                :min=\"0\"\n                placeholder=\"请输入PT柜数量\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"母联柜数量：\" prop=\"mlgsl\">\n              <el-input-number\n                v-model=\"pdzform.mlgsl\"\n                :min=\"0\"\n                placeholder=\"请输入母联柜数量\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item v-if=\"!(pdzform.lx == '柱上变台变')\" label=\"低压进线柜数量：\" prop=\"dyjxgsl\">\n              <el-input-number\n                v-model=\"pdzform.dyjxgsl\"\n                :min=\"0\"\n                placeholder=\"低压进线柜数量\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"低压出线柜数量：\" prop=\"dycxgsl\">\n              <el-input-number\n                v-model=\"pdzform.dycxgsl\"\n                :min=\"0\"\n                placeholder=\"请输入低压出线柜数量\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item v-if=\"!(pdzform.lx == '柱上变台变')\" label=\"低压补偿柜数量：\" prop=\"dybcgsl\">\n              <el-input-number\n                v-model=\"pdzform.dybcgsl\"\n                :min=\"0\"\n                placeholder=\"请输入低压补偿柜数量\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item v-if=\"!(pdzform.lx == '柱上变台变')\" label=\"低压计量柜数量：\" prop=\"dyjlgsl\">\n              <el-input-number\n                v-model=\"pdzform.dyjlgsl\"\n                :min=\"0\"\n                placeholder=\"请输入低压计量柜数量\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item v-if=\"!(pdzform.lx == '柱上变台变')\" label=\"低压联络柜数量：\" prop=\"dyllgsl\">\n              <el-input-number\n                v-model=\"pdzform.dyllgsl\"\n                :min=\"0\"\n                placeholder=\"请输入低压联络柜数量\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"配变数量：\" prop=\"pbsl\">\n              <el-input-number\n                v-model=\"pdzform.pbsl\"\n                :min=\"0\"\n                placeholder=\"请输入配变数量\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"配变总容量：\" prop=\"pbzrl\">\n              <el-input-number\n                v-model=\"pdzform.pbzrl\"\n                :min=\"0\"\n                placeholder=\"请输入配变总容量(KVA)\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"蓄电池柜数量：\" prop=\"xdcgsl\">\n              <el-input-number\n                v-model=\"pdzform.xdcgsl\"\n                :min=\"0\"\n                placeholder=\"请输入蓄电池柜数量\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"直流柜数量：\" prop=\"zlgsl\">\n              <el-input-number\n                v-model=\"pdzform.zlgsl\"\n                :min=\"0\"\n                placeholder=\"请输入直流柜数量\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"电杆数量：\" prop=\"dgsl\">\n              <el-input-number\n                v-model=\"pdzform.dgsl\"\n                :min=\"0\"\n                placeholder=\"请输入电杆数量\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"电杆高度(m)：\" prop=\"dggd\">\n              <el-input-number\n                v-model=\"pdzform.dggd\"\n                :min=\"0\"\n                placeholder=\"请输入电杆高度\"\n              >\n              </el-input-number>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item v-if=\"pdzform.lx == '箱式变电站'\"  label=\"箱式变型号：\" prop=\"xsbxh\">\n              <el-input\n                v-model=\"pdzform.xsbxh\"\n                placeholder=\"请输入箱式变型号\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"低压配电柜数量：\" prop=\"dypdgsl\">\n              <el-input-number\n                v-model=\"pdzform.dypdgsl\"\n                :min=\"0\"\n                placeholder=\"请输入低压配电柜数量\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col> -->\n          <!-- <el-col :span=\"8\">\n            <el-form-item label=\"计量柜数量：\" prop=\"jlgsl\">\n              <el-input-number\n                v-model=\"pdzform.jlgsl\"\n                :min=\"0\"\n                placeholder=\"请输入计量柜数量\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col> -->\n          <el-col :span=\"8\">\n            <el-form-item label=\"经度\" prop=\"jd\">\n              <el-input\n                v-model=\"pdzform.jd\"\n                placeholder=\"请输入经度\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"纬度\" prop=\"wd\">\n              <el-input v-model=\"pdzform.wd\" placeholder=\"请输入纬度\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"是否具有环网：\" prop=\"sfjyhw\">\n              <el-select\n                v-model=\"pdzform.sfjyhw\"\n                placeholder=\"请选择是否具有环网\"\n                :disabled=\"isDisabled\"\n                clearable\n                style=\"width: 80%\"\n              >\n                <el-option value=\"是\" label=\"是\"></el-option>\n                <el-option value=\"否\" label=\"否\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\" prop=\"bz\">\n              <el-input\n                v-model=\"pdzform.bz\"\n                type=\"textarea\"\n                rows=\"2\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-form-item\n            label=\"已上传图片：\"\n            v-if=\"pdzform.attachment.length > 0\"\n            id=\"pic_form\"\n          >\n            <el-col\n              :span=\"24\"\n              v-for=\"(item, index) in pdzform.attachment\"\n              style=\"margin-left: 0\"\n            >\n              <el-form-item :label=\"(index + 1).toString()\">\n                {{ item.fileOldName }}\n                <el-button\n                  v-if=\"!isDisabled\"\n                  type=\"error\"\n                  size=\"mini\"\n                  @click=\"deleteFileById(item.fileId)\"\n                  >删除\n                </el-button>\n              </el-form-item>\n            </el-col>\n          </el-form-item>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-form-item label=\"上传图片：\" v-if=\"!isDisabled\">\n            <el-upload\n              list-type=\"picture-card\"\n              class=\"upload-demo\"\n              accept=\".jpg,.png\"\n              ref=\"upload\"\n              :headers=\"header\"\n              action=\"/isc-api/file/upload\"\n              :before-upload=\"beforeUpload\"\n              :data=\"uploadData\"\n              single\n              :auto-upload=\"false\"\n              multiple\n            >\n              <el-button slot=\"trigger\" type=\"primary\">选取文件</el-button>\n              <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png文件</div>\n            </el-upload>\n          </el-form-item>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\" v-show=\"!isDisabled\">\n        <el-button @click=\"pdsDialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"getDetermine\">确 定</el-button>\n      </div>\n    </el-dialog>\n\n    <!--配电柜新增、修改、详情弹框-->\n    <el-dialog\n    v-dialogDrag\n      title=\"配电柜详情\"\n      :visible.sync=\"bdgDialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"60%\"\n      @close=\"handleClose\"\n    >\n      <el-form\n        ref=\"pdgform\"\n        :model=\"pdgform\"\n        :rules=\"rules\"\n        :disabled=\"pdgdisable\"\n        label-width=\"130px\"\n      >\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属站室\" prop=\"sszs\">\n              <el-input\n                v-model=\"pdgform.sszs\"\n                placeholder=\"请输入所属站室名称\"\n                disabled=\"disabled\"\n              ></el-input>\n<!--              <el-select\n                v-model=\"pdgform.sszs\"\n                placeholder=\"请选择所属电站\"\n                filterable\n                disabled=\"disabled\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in pdgOptionsDataList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>-->\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"开关柜名称\" prop=\"kggmc\">\n              <el-input\n                v-model=\"pdgform.kggmc\"\n                placeholder=\"请输入开关柜名称\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"运行编号\" prop=\"yxbh\">\n              <el-input\n                v-model=\"pdgform.yxbh\"\n                placeholder=\"请输入运行编号\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"投运日期：\" prop=\"tyrq\">\n              <el-date-picker\n                type=\"date\"\n                :disabled=\"isDisabled\"\n                placeholder=\"选择日期\"\n                value-format=\"yyyy-MM-dd\"\n                v-model=\"pdgform.tyrq\"\n                style=\"width: 100%\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"生产厂家\" prop=\"sccj\">\n              <el-input\n                v-model=\"pdgform.sccj\"\n                :placeholder=\"pdgdisable?'':'请输入生产厂家'\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"开关柜型号\" prop=\"kggxh\">\n              <el-input\n                v-model=\"pdgform.kggxh\"\n                placeholder=\"请输入开关柜型号\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"出厂日期：\" prop=\"ccrq\">\n              <el-date-picker\n                type=\"date\"\n                :disabled=\"isDisabled\"\n                :placeholder=\"pdgdisable?'':'选择日期'\"\n                value-format=\"yyyy-MM-dd\"\n                v-model=\"pdgform.ccrq\"\n                style=\"width: 100%\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"状态\" prop=\"zt\">\n              <el-select\n                v-model=\"pdgform.zt\"\n                placeholder=\"请选择状态\"\n                :disabled=\"isDisabled\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in pdgztList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.label\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"电压等级(KV)\" prop=\"dydj\">\n              <el-input-number\n                :min=\"0\"\n                :precision=\"2\"\n                v-model=\"pdgform.dydj\"\n                placeholder=\"请输入电压等级\"\n                :disabled=\"isDisabled\"\n              ></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"开关用途\" prop=\"kgyt\">\n              <el-select\n                v-model=\"pdgform.kgyt\"\n                placeholder=\"请选择开关用途\"\n                :disabled=\"isDisabled\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in pdgkgytList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.label\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"开关柜类型\" prop=\"kgglx\">\n              <el-input\n                v-model=\"pdgform.kgglx\"\n                placeholder=\"请输入开关柜类型\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"闭锁型式\" prop=\"bsxs\">\n              <el-input\n                v-model=\"pdgform.bsxs\"\n                placeholder=\"请输入闭锁型式\"\n                :disabled=\"isDisabled\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\" prop=\"bz\">\n              <el-input\n                v-model=\"pdgform.bz\"\n                type=\"textarea\"\n                rows=\"2\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\" v-show=\"showButton\">\n        <el-button @click=\"bdgDialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"save\" class=\"pmyBtn\">确 定</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 设备基本信息、参数、履历弹出框-->\n    <el-dialog\n      :title=\"sbtitle\"\n      :visible.sync=\"dialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"70%\"\n      v-dialogDrag\n      :before-close=\"resetForm\"\n    >\n      <div>\n        <el-tabs v-model=\"activeTabName\">\n          <!--基本信息-->\n          <el-tab-pane label=\"基本信息\" name=\"sbDesc\">\n            <!--添加信息-->\n            <el-form\n              :model=\"jbxxForm\"\n              :rules=\"rules\"\n              label-width=\"140px\"\n              ref=\"jbxxForm\"\n              :disabled=\"isDisabled\"\n            >\n              <el-row>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"设备类型\" prop=\"sblx\">\n                    <el-select\n                      v-model=\"jbxxForm.sblx\"\n                      placeholder=\"请输入内容\"\n                      @change=\"showParams\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in sblxOptionsDataSelected\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"所属站室\" prop=\"sszs\">\n                    <el-select\n                      v-model=\"jbxxForm.sszs\"\n                      placeholder=\"请输入内容\"\n                      @change=\"pdsOptionsChangeClick()\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in pdsOptionsDataList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"所属开关柜\" prop=\"sskgg\">\n                    <el-select\n                      v-model=\"jbxxForm.sskgg\"\n                      placeholder=\"请输入内容\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in sbOptionsDataList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"设备名称\" prop=\"sbmc\">\n                    <el-input v-model=\"jbxxForm.sbmc\" placeholder=\"请输入内容\">\n                    </el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"运行编号\" prop=\"yxbh\">\n                    <el-input v-model=\"jbxxForm.yxbh\" placeholder=\"请输入内容\">\n                    </el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"投运日期\" prop=\"tyrq\">\n                    <el-date-picker\n                      v-model=\"jbxxForm.tyrq\"\n                      type=\"date\"\n                      value-format=\"yyyy-MM-dd\"\n                      placeholder=\"选择日期\"\n                    >\n                    </el-date-picker>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"状态\" prop=\"zt\">\n                    <el-select\n                      v-model=\"jbxxForm.zt\"\n                      placeholder=\"请输入内容\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in jbxxztList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"电压等级\" prop=\"dydj\">\n                    <el-input v-model=\"jbxxForm.dydj\" placeholder=\"请输入内容\">\n                    </el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"规格型号\" prop=\"ggxh\">\n                    <el-input v-model=\"jbxxForm.ggxh\" placeholder=\"请输入内容\">\n                    </el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"生产厂家\" prop=\"sccj\">\n                    <el-input v-model=\"jbxxForm.sccj\"  :placeholder=\"isDisabled?'':'请输入内容'\">\n                    </el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"出厂日期\" prop=\"ccrq\">\n                    <el-date-picker\n                      v-model=\"jbxxForm.ccrq\"\n                      type=\"date\"\n                      value-format=\"yyyy-MM-dd\"\n                      :placeholder=\"isDisabled?'':'选择日期'\"\n                    >\n                    </el-date-picker>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"开关用途\" prop=\"kgyt\">\n                    <el-select\n                      v-model=\"jbxxForm.kgyt\"\n                      :placeholder=\"isDisabled?'':'请输入内容'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in jbxxkgytList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"灭弧介质\" prop=\"mhjz\">\n                    <el-select\n                      v-model=\"jbxxForm.mhjz\"\n                      :placeholder=\"isDisabled?'':'请输入灭弧介质'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in mhjzList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"操作机构型\" prop=\"czjgxs\">\n                    <el-select\n                      v-model=\"jbxxForm.czjgxs\"\n                      :placeholder=\"isDisabled?'':'请输入操作机构型式'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in lzjgxsList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"操作方式\" prop=\"czfs\">\n                    <el-select\n                      v-model=\"jbxxForm.czfs\"\n                      :placeholder=\"isDisabled?'':'请输入操作方式'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in czfsList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"绝缘介质\" prop=\"jyjz\">\n                    <el-select\n                      v-model=\"jbxxForm.jyjz\"\n                      :placeholder=\"isDisabled?'':'请输入绝缘介质'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in jyjzList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"额定电压   \" prop=\"eddy\">\n                    <el-input\n                      v-model=\"jbxxForm.eddy\"\n                      :placeholder=\"isDisabled?'':'请输入额定电压'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"额定电流   \" prop=\"eddl\">\n                    <el-input-number\n                      v-model=\"jbxxForm.eddl\"\n                      :min=\"0\"\n                      placeholder=\"请输入额定电流\"\n                    ></el-input-number>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"熔丝规格   \" prop=\"rsgg\">\n                    <el-input\n                      v-model=\"jbxxForm.rsgg\"\n                      :placeholder=\"isDisabled?'':'请输入熔丝规格'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"绝缘材质   \" prop=\"jycz\">\n                    <el-select\n                      v-model=\"jbxxForm.jycz\"\n                      :placeholder=\"isDisabled?'':'请输入绝缘材质'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in jyczList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"数量\" prop=\"sl\">\n                    <el-input-number\n                      :min=\"0\"\n                      v-model=\"jbxxForm.sl\"\n                      placeholder=\"请输入数量\"\n                    ></el-input-number>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"额定电流比  \" prop=\"eddlb\">\n                    <el-input\n                      v-model=\"jbxxForm.eddlb\"\n                      :placeholder=\"isDisabled?'':'请输入额定电流比'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"额定电压比  \" prop=\"eddyb\">\n                    <el-input\n                      v-model=\"jbxxForm.eddyb\"\n                      :placeholder=\"isDisabled?'':'请输入额定电压比'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"绝缘方式\" prop=\"jyfs\">\n                    <el-select\n                      v-model=\"jbxxForm.jyfs\"\n                      :placeholder=\"isDisabled?'':'请输入绝缘方式'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in jyfsList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"总容量\" prop=\"zrl\">\n                    <el-input-number\n                      :min=\"0\"\n                      v-model=\"jbxxForm.zrl\"\n                      placeholder=\"请输入总容量\"\n                    ></el-input-number>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"投退方式\" prop=\"ttfs\">\n                    <el-select\n                      v-model=\"jbxxForm.ttfs\"\n                      :placeholder=\"isDisabled?'':'请输入投退方式'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in ttfsList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"单只容量\" prop=\"dzrl\">\n                    <el-input-number\n                      :min=\"0\"\n                      v-model=\"jbxxForm.dzrl\"\n                      placeholder=\"请输入单只容量\"\n                    ></el-input-number>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"资产性质\" prop=\"zcxx\">\n                    <el-select\n                      v-model=\"jbxxForm.zcxx\"\n                      :placeholder=\"isDisabled?'':'请输入资产性质'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in zzxzList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"母线型式\" prop=\"mxxs\">\n                    <el-select\n                      v-model=\"jbxxForm.mxxs\"\n                      :placeholder=\"isDisabled?'':'请输入母线型式'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in mxxsList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"母线材质\" prop=\"mxcz\">\n                    <el-input\n                      v-model=\"jbxxForm.mxcz\"\n                      :placeholder=\"isDisabled?'':'请输入母线材质'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"长度\" prop=\"cd\">\n                    <el-input-number\n                      :min=\"0\"\n                      v-model=\"jbxxForm.cd\"\n                      placeholder=\"请输入长度\"\n                    ></el-input-number>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"额定容量\" prop=\"edrl\">\n                    <el-input-number\n                      :min=\"0\"\n                      v-model=\"jbxxForm.edrl\"\n                      placeholder=\"请输入额定容量\"\n                    ></el-input-number>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"配电变压器类型\" prop=\"byqlx\">\n                    <el-select\n                      v-model=\"jbxxForm.byqlx\"\n                      :placeholder=\"isDisabled?'':'请输入配电变压器类型'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in dyqlxList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"阻抗电压\" prop=\"zkdy\">\n                    <el-input\n                      v-model=\"jbxxForm.zkdy\"\n                      :placeholder=\"isDisabled?'':'请输入阻抗电压'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"短路阻抗\" prop=\"dlzk\">\n                    <el-input\n                      v-model=\"jbxxForm.dlzk\"\n                      :placeholder=\"isDisabled?'':'请输入短路阻抗'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"短路损耗\" prop=\"dlsh\">\n                    <el-input\n                      v-model=\"jbxxForm.dlsh\"\n                      :placeholder=\"isDisabled?'':'请输入短路损耗'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"接线组别\" prop=\"jxzb\">\n                    <el-input\n                      v-model=\"jbxxForm.jxzb\"\n                      :placeholder=\"isDisabled?'':'请输入接线组别'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"调压范围\" prop=\"tyfw\">\n                    <el-input\n                      v-model=\"jbxxForm.tyfw\"\n                      :placeholder=\"isDisabled?'':'请输入调压范围'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"油号\" prop=\"yh\">\n                    <el-input\n                      v-model=\"jbxxForm.yh\"\n                      :placeholder=\"isDisabled?'':'请输入油号'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"油重\" prop=\"yz\">\n                    <el-input-number\n                      :min=\"0\"\n                      v-model=\"jbxxForm.yz\"\n                      placeholder=\"请输入油重\"\n                    ></el-input-number>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"总重\" prop=\"zz\">\n                    <el-input-number\n                      :min=\"0\"\n                      v-model=\"jbxxForm.zz\"\n                      placeholder=\"请输入总重\"\n                    ></el-input-number>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"绝缘等级\" prop=\"jydj\">\n                    <el-select\n                      v-model=\"jbxxForm.jydj\"\n                      :placeholder=\"isDisabled?'':'请输入绝缘等级'\"\n                      clearable\n                    >\n                      <el-option\n                        v-for=\"item in jydjList\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"调压方式\" prop=\"tyfs\">\n                    <el-input\n                      v-model=\"jbxxForm.tyfs\"\n                      :placeholder=\"isDisabled?'':'请输入调压方式'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"铁芯材质\" prop=\"txcz\">\n                    <el-input\n                      v-model=\"jbxxForm.txcz\"\n                      :placeholder=\"isDisabled?'':'请输入铁芯材质'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"芯数及截面\" prop=\"xsjjm\">\n                    <el-input\n                      v-model=\"jbxxForm.xsjjm\"\n                      :placeholder=\"isDisabled?'':'请输入芯数及截面'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"负载名称\" prop=\"fzmc\">\n                    <el-input\n                      v-model=\"jbxxForm.fzmc\"\n                      :placeholder=\"isDisabled?'':'请输入负载名称'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"8\">\n                  <el-form-item label=\"电缆材质\" prop=\"dlcz\">\n                    <el-input\n                      v-model=\"jbxxForm.dlcz\"\n                      :placeholder=\"isDisabled?'':'请输入电缆材质'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n              <el-row>\n                <el-col :span=\"24\">\n                  <el-form-item label=\"备注\" prop=\"bz\">\n                    <el-input\n                      v-model=\"jbxxForm.bz\"\n                      :placeholder=\"isDisabled?'':'请输入内容'\"\n                    ></el-input>\n                  </el-form-item>\n                </el-col>\n              </el-row>\n            </el-form>\n          </el-tab-pane>\n\n          <!--技术参数-->\n          <el-tab-pane label=\"技术参数\" name=\"sbjscs\">\n            <el-form :model=\"jscsForm\" label-width=\"130px\">\n              <el-col :span=\"8\" v-for=\"item in jscsLabelList\">\n                <el-form-item\n                  :label=\"\n                    item.dw != ''\n                      ? item.label + '(' + item.dw + ')'\n                      : item.label\n                  \"\n                >\n                  <el-input\n                    v-if=\"item.type === 'input'\"\n                    v-model=\"jscsForm[item.jscsbm]\"\n                    placeholder=\"\"\n                  >\n                  </el-input>\n                  <el-select\n                    clearable\n                    v-if=\"item.type === 'select'\"\n                    v-model=\"jscsForm[item.jscsbm]\"\n                    placeholder=\"\"\n                  >\n                    <el-option\n                      v-for=\"(childItem, key) in item.options\"\n                      :key=\"key\"\n                      :label=\"childItem.label\"\n                      :value=\"childItem.value\"\n                      :disabled=\"childItem.disabled\"\n                      style=\"display: flex; align-items: center\"\n                      clearable\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </el-form>\n          </el-tab-pane>\n\n          <!--设备履历-->\n          <el-tab-pane label=\"设备履历\" name=\"sbRecord\">\n            <el-tabs\n              v-model=\"sbllDescTabName\"\n              @tab-click=\"handleSbllDescTabNameClick\"\n              type=\"card\"\n            >\n              <el-tab-pane label=\"试验记录\" name=\"syjl\">\n                <el-table\n                  stripe\n                  border\n                  v-loading=\"loading\"\n                  :data=\"sblvsyjlList\"\n                  max-height=\"550\"\n                >\n                  <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n                  <el-table-column\n                    label=\"试验专业\"\n                    align=\"center\"\n                    prop=\"syzy\"\n                  />\n                  <el-table-column\n                    label=\"试验性质\"\n                    align=\"center\"\n                    prop=\"syxz\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"试验名称\"\n                    align=\"center\"\n                    prop=\"symc\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"工作地点\"\n                    align=\"center\"\n                    prop=\"gzdd\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"试验设备\"\n                    align=\"center\"\n                    prop=\"sysb\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"试验报告\"\n                    align=\"center\"\n                    prop=\"sybg\"\n                  />\n                  <el-table-column\n                    label=\"天气\"\n                    align=\"center\"\n                    prop=\"tq\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"试验日期\"\n                    align=\"center\"\n                    prop=\"syrq\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"录入人\"\n                    align=\"center\"\n                    prop=\"lrr\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"试验结论\"\n                    align=\"center\"\n                    prop=\"syjl\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                </el-table>\n              </el-tab-pane>\n              <!-- <el-tab-pane label=\"缺陷记录\" name=\"qxjl\">\n                <el-table\n                  stripe\n                  border\n                  v-loading=\"loading\"\n                  :data=\"sbllqxjlList\"\n                  max-height=\"550\"\n                >\n                  <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\n                  <el-table-column\n                    label=\"所属公司\"\n                    align=\"center\"\n                    prop=\"ssgs\"\n                  />\n                  <el-table-column\n                    label=\"变电站名称\"\n                    align=\"center\"\n                    prop=\"bdzmc\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"设备类型\"\n                    align=\"center\"\n                    prop=\"sblx\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"缺陷性质\"\n                    align=\"center\"\n                    prop=\"qxxz\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"电压等级\"\n                    align=\"center\"\n                    prop=\"dydj\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                  <el-table-column\n                    label=\"设备型号\"\n                    align=\"center\"\n                    prop=\"sbxh\"\n                  />\n                  <el-table-column\n                    label=\"生产厂家\"\n                    align=\"center\"\n                    prop=\"sccj\"\n                    :show-overflow-tooltip=\"true\"\n                  />\n                </el-table>\n              </el-tab-pane>\n              <el-tab-pane label=\"状态变更记录\" name=\"ztbgjl\">\n                <comp-table\n                  :table-and-page-info=\"resumPageInfo\"\n                  @getMethod=\"getResumList\"\n                  @update:multipleSelection=\"selectChange\"\n                  height=\"500\"\n                />\n              </el-tab-pane> -->\n            </el-tabs>\n          </el-tab-pane>\n        </el-tabs>\n      </div>\n      <div slot=\"footer\" class=\"dialog-footer\" v-show=\"!isDisabled\">\n        <el-button @click=\"resetForm\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submit\" class=\"submit\"\n          >确 定\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!--状态变更弹出框展示-->\n    <el-dialog\n      title=\"设备状态变更\"\n      :visible.sync=\"dialogVisible\"\n      width=\"30%\"\n      append-to-body\n      v-dialogDrag\n    >\n      <el-form :inline=\"true\" label-width=\"100px\" class=\"qxlr_dialog_insert\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"设备状态:\">\n              <el-select v-model=\"updateList.zt\">\n                <el-option\n                  v-for=\"item in jbxxztList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitStatus\">确 定</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport {\n  getList,\n  saveOrUpdate,\n  remove,\n  getPdsbOne,\n} from \"@/api/dagangOilfield/dwzygl/pdsbgl/pdsbjbxx\";\nimport {\n  updateStatus,\n  getResumDataList,\n} from \"@/api/dagangOilfield/dwzygl/pdsbgl/pdsbztbg\";\nimport {\n  addPdg,\n  getPdgList,\n  getPdgListSelected,\n  getPdgOne,\n  getPdsTreeList,\n  removePdg,\n  getMergePdgInfo,\n} from \"@/api/dagangOilfield/asset/pdg\";\nimport { getSblxDataListSelected } from \"@/api/dagangOilfield/asset/bdsbtz\";\nimport {\n  getParamDataList,\n  getParamsValue,\n  saveParamValue,\n} from \"@/api/dagangOilfield/asset/parameters\";\nimport {\n  addPds,\n  getPdsList,\n  getPdsOptionsDataList,\n  removePds,\n} from \"@/api/dagangOilfield/asset/pdsgl\";\nimport { getDictTypeData } from \"@/api/system/dict/data\";\nimport { selectDeptOneAndTwo } from \"@/api/system/dept\";\nimport { deleteById, getListByBusinessId } from \"@/api/tool/file\";\nimport {adddwzyfstz} from \"@/api/dagangOilfield/asset/jgtz\";\n\nexport default {\n  name: \"pdsbgl\",\n  data() {\n    return {\n      sskgg:\"\",\n      treeNode:'',//树节点标记\n      headForm: {}, //头部表单信息\n      uploadData: {\n        type: \"\",\n        businessId: undefined,\n      },\n      sszs: \"\",\n      icons: {\n        pdsList: \"categoryTreeIcons\",\n        pds: \"tableIcon\",\n        pdg: \"classIcon\",\n      },\n      pdzshow: true,\n      sbshow: false,\n      pdgshow: false,\n      //配电站相关\n      //弹出框表单\n      pdzform: {\n        attachment: [],\n        //ssgs: undefined,\n        ssxlmc: undefined,\n        ssxlbh: undefined,\n        ssxdmc: undefined,\n        pdsmc: undefined,\n        yxbh: undefined,\n        erpBm: undefined,\n        bgr: undefined,\n        zcbdfs: undefined,\n        zcsx: undefined,\n        zcbh: undefined,\n        wbsYs: undefined,\n        zcxz: undefined,\n        tyrq: undefined,\n        sfjyhw: undefined,\n        zt: undefined,\n        dqtz: undefined,\n        sccj: undefined,\n        sgdw: undefined,\n        jzwcc: undefined,\n        jzwcz: undefined,\n        tffs: undefined,\n        dlqgsl: undefined,\n        dyhgqgsl: undefined,\n        drgsl: undefined,\n        dyjxfs: undefined,\n        dypxgsl: undefined,\n        fhkggsl: undefined,\n        fhkgrdqzhgsl: undefined,\n        jlgsl: undefined,\n        mlgsl: undefined,\n        pbsl: undefined,\n        pbzrl: undefined,\n        wz: undefined,\n        xdcgsl: undefined,\n        zlgsl: undefined,\n        zby: undefined,\n        gyjxgsl: undefined,\n        gyjlgsl: undefined,\n        gycxgsl: undefined,\n        ptgsl: undefined,\n        dyjxgsl: undefined,\n        dycxgsl: undefined,\n        dybcgsl: undefined,\n        dyjlgsl: undefined,\n        dyllgsl: undefined,\n        dgsl: undefined,\n        dggd: undefined,\n        xsbxh: undefined,\n        dypdgsl: undefined,\n        lx: undefined,\n      },\n      pdsDialogFormVisible: false,\n      pdzfilterInfo: {\n        data: {\n          ssgs: [],\n          ssxlmc: \"\",\n          yxbh: \"\",\n          zt: [],\n        },\n        fieldList: [\n          // {label: '所属公司', type: 'select', value: 'ssgs', multiple: true, options: []},\n          { label: \"所属线路名称\", type: \"input\", value: \"ssxlmc\" },\n          { label: \"配电室名称\", type: \"input\", value: \"pdsmc\" },\n          { label: \"所属线路编号\", type: \"input\", value: \"ssxlbh\" },\n          { label: \"所属线段名称\", type: \"input\", value: \"ssxdmc\" },\n          { label: \"运行编号\", type: \"input\", value: \"yxbh\" },\n          { label: \"运维班组\", type: \"input\", value: \"ywbzmc\" },\n          {\n            label: \"投运日期\",\n            type: \"date\",\n            value: \"tyrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\",\n          },\n          {\n            label: \"是否具有环网\",\n            type: \"select\",\n            value: \"sfjyhw\",\n            options: [\n              {\n                value: \"是\",\n                label: \"是\",\n              },\n              {\n                value: \"否\",\n                label: \"否\",\n              },\n            ],\n          },\n          {\n            label: \"状态\",\n            type: \"select\",\n            value: \"zt\",\n            options: [\n              {\n                value: \"在运\",\n                label: \"在运\",\n              },\n              {\n                value: \"停止使用\",\n                label: \"停止使用\",\n              },\n              {\n                value: \"未就绪\",\n                label: \"未就绪\",\n              },\n              {\n                value: \"报废\",\n                label: \"报废\",\n              },\n            ],\n          },\n          { label: \"生产厂家\", type: \"input\", value: \"sccj\" },\n          { label: \"通风方式\", type: \"input\", value: \"tffs\" },\n          {\n            label: \"配电室类型\",\n            type: \"select\",\n            value: \"lx\",\n            options: [\n              {\n                value: \"箱式变电站\",\n                label: \"箱式变电站\",\n              },\n              {\n                value: \"柱上变台变\",\n                label: \"柱上变台变\",\n              },\n              {\n                value: \"配电室\",\n                label: \"配电室\",\n              },\n            ],\n          },\n        ],\n      },\n      ywbzList: [],\n      pdzParams: {\n        pageNum: 1,\n        pageSize: 10,\n      },\n      tableAndPageInfo1: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n          pageResize: \"\",\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true,\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"ssxlmc\", label: \"所属线路名称\", minWidth: \"180\" },\n          { prop: \"ssxlbh\", label: \"所属线路编号\", minWidth: \"120\" },\n          { prop: \"ssxdmc\", label: \"所属线段名称\", minWidth: \"180\" },\n          { prop: \"pdsmc\", label: \"配电室名称\", minWidth: \"140\" },\n          { prop: \"yxbh\", label: \"运行编号\", minWidth: \"120\" },\n          { prop: \"ywbzmc\", label: \"运维班组\", minWidth: \"120\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"120\" },\n          { prop: \"sfjyhw\", label: \"是否具有环网\", minWidth: \"120\" },\n          { prop: \"zt\", label: \"状态\", minWidth: \"120\" },\n          { prop: \"sccj\", label: \"生产厂家\", minWidth: \"120\" },\n          { prop: \"tffs\", label: \"通风方式\", minWidth: \"120\" },\n          { prop: \"lx\", label: \"配电室类型\", minWidth: \"120\" },\n          /*{\n              fixed: \"right\",\n              prop: 'operation',\n              label: '操作',\n              minWidth: '130px',\n              style: {display: 'block'},\n              operation: [\n                {name: '修改', clickFun: this.pdzgetUpdate},\n                {name: '详情', clickFun: this.pdzgetXq},\n              ]\n            },*/\n        ],\n      },\n      pdzsbzt: [\n        {\n          value: \"在运\",\n          label: \"在运\",\n        },\n        {\n          value: \"停止使用\",\n          label: \"停止使用\",\n        },\n        {\n          value: \"未就绪\",\n          label: \"未就绪\",\n        },\n        {\n          value: \"报废\",\n          label: \"报废\",\n        },\n      ],\n      pdslx: [\n        {\n          value: \"箱式变电站\",\n          label: \"箱式变电站\",\n        },\n        {\n          value: \"柱上变台变\",\n          label: \"柱上变台变\",\n        },\n        {\n          value: \"配电室\",\n          label: \"配电室\",\n        },\n      ],\n\n      //配电柜相关\n      bdgDialogFormVisible: false,\n      showButton: true,\n\n      pdgdisable:false,\n      //配电室下拉框\n      pdgOptionsDataList: [],\n      pdgkgytList: [],\n      pdgztList: [],\n      //弹出框表单\n      pdgform: {},\n      pdgParams: {\n        pageNum: 1,\n        pageSize: 10,\n      },\n      pdgfilterInfo: {\n        data: {\n          kggmc: \"\",\n          yxbh: \"\",\n        },\n        fieldList: [\n          { label: \"所属站室名称\", type: \"input\", value: \"sszsmc\" },\n          { label: \"所属站室运行编号\", type: \"input\", value: \"sszsyxbh\" },\n          { label: \"开关柜名称\", type: \"input\", value: \"kggmc\" },\n          { label: \"运行编号\", type: \"input\", value: \"yxbh\" },\n          {\n            label: \"投运日期\",\n            type: \"date\",\n            value: \"tyrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\",\n          },\n          { label: \"生产厂家\", type: \"input\", value: \"sccj\" },\n          { label: \"开关柜型号\", type: \"input\", value: \"kggxh\" },\n          {\n            label: \"出厂日期\",\n            type: \"date\",\n            value: \"tyrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\",\n          },\n          {\n            label: \"状态\",\n            type: \"select\",\n            value: \"zt\",\n            options: [\n              {\n                value: \"在运\",\n                label: \"在运\",\n              },\n              {\n                value: \"停止使用\",\n                label: \"停止使用\",\n              },\n              {\n                value: \"未就绪\",\n                label: \"未就绪\",\n              },\n              {\n                value: \"报废\",\n                label: \"报废\",\n              },\n              {\n                value: \"备用\",\n                label: \"备用\",\n              },\n            ],\n          },\n          { label: \"电压等级\", type: \"input\", value: \"dydj\" },\n        ],\n      },\n      tableAndPageInfo2: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n          pageResize: \"\",\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true,\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"sszsmc\", label: \"所属站室名称\", minWidth: \"180\" },\n          { prop: \"sszsyxbh\", label: \"所属站室运行编号\", minWidth: \"120\" },\n          { prop: \"kggmc\", label: \"开关柜名称\", minWidth: \"120\" },\n          { prop: \"yxbh\", label: \"运行编号\", minWidth: \"140\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"140\" },\n          { prop: \"sccj\", label: \"生产厂家\", minWidth: \"120\" },\n          { prop: \"kggxh\", label: \"开关柜型号\", minWidth: \"120\" },\n          { prop: \"ccrq\", label: \"出厂日期\", minWidth: \"120\" },\n          { prop: \"zt\", label: \"状态\", minWidth: \"180\" },\n          { prop: \"dydj\", label: \"电压等级\", minWidth: \"120\" },\n          /*{\n              fixed: \"right\",\n              prop: 'operation',\n              label: '操作',\n              minWidth: '130px',\n              style: {display: 'block'},\n              operation: [\n                {name: '修改', clickFun: this.pdgUpdate},\n                {name: '详情', clickFun: this.pdgDetails},\n              ]\n            },*/\n        ],\n      },\n      selectRows: [],\n      //配电室下拉框\n      pdsOptionsDataList: [],\n      //配电柜下拉框\n      sbOptionsDataList: [],\n      //设备类型\n      sblxOptionsDataSelected: [],\n      //左侧树筛选条件\n      treeForm: {},\n      //设备弹出框标题\n      sbtitle: \"\",\n      //状态变更信息\n      updateList: {\n        zt: \"\",\n        id: \"\",\n      },\n      //状态信息查询\n      resumeQuery: {\n        foreignNum: undefined,\n      },\n      dialogVisible: false,\n      //是否禁用\n      isDisabled: false,\n      //上传图片时的请求头\n      header: {},\n      //\n      tableDisabled:false,\n      //基本信息的状态\n      jbxxztList: [\n        {\n          value: \"在运\",\n          label: \"在运\",\n        },\n        {\n          value: \"停止使用\",\n          label: \"停止使用\",\n        },\n        {\n          value: \"未就绪\",\n          label: \"未就绪\",\n        },\n        {\n          value: \"报废\",\n          label: \"报废\",\n        },\n        {\n          value: \"备用\",\n          label: \"备用\",\n        },\n      ],\n      //开关用途\n      jbxxkgytList: [\n        {\n          value: \"进线\",\n          label: \"进线\",\n        },\n        {\n          value: \"出线\",\n          label: \"出线\",\n        },\n        {\n          value: \"联络\",\n          label: \"联络\",\n        },\n      ],\n\n      //操作机构型式\n      lzjgxsList: [\n        {\n          value: \"弹簧\",\n          label: \"弹簧\",\n        },\n        {\n          value: \"永磁\",\n          label: \"永磁\",\n        },\n      ],\n\n      //灭弧介质\n      mhjzList: [\n        {\n          value: \"空气\",\n          label: \"空气\",\n        },\n        {\n          value: \"充油\",\n          label: \"充油\",\n        },\n        {\n          value: \"真空\",\n          label: \"真空\",\n        },\n        {\n          value: \"SF6\",\n          label: \"SF6\",\n        },\n      ],\n\n      //绝缘介质\n      jyjzList: [\n        {\n          value: \"空气\",\n          label: \"空气\",\n        },\n        {\n          value: \"充油\",\n          label: \"充油\",\n        },\n        {\n          value: \"真空\",\n          label: \"真空\",\n        },\n        {\n          value: \"SF6\",\n          label: \"SF6\",\n        },\n      ],\n      //绝缘材质\n      jyczList: [\n        {\n          value: \"瓷\",\n          label: \"瓷\",\n        },\n        {\n          value: \"复合\",\n          label: \"复合\",\n        },\n      ],\n\n      //绝缘方式\n      jyfsList: [\n        {\n          value: \"油浸式\",\n          label: \"油浸式\",\n        },\n        {\n          value: \"干式\",\n          label: \"干式\",\n        },\n      ],\n      //操作方式\n      czfsList: [\n        {\n          value: \"手动\",\n          label: \"手动\",\n        },\n        {\n          value: \"自动\",\n          label: \"自动\",\n        },\n        {\n          value: \"手动/自动\",\n          label: \"手动/自动\",\n        },\n      ],\n      //操作方式\n      ttfsList: [\n        {\n          value: \"手动\",\n          label: \"手动\",\n        },\n        {\n          value: \"自动式\",\n          label: \"自动式\",\n        },\n      ],\n      //资产性质\n      zzxzList: [\n        {\n          value: \"公用\",\n          label: \"公用\",\n        },\n        {\n          value: \"专用\",\n          label: \"专用\",\n        },\n      ],\n      //母线型式\n      mxxsList: [\n        {\n          value: \"排式\",\n          label: \"排式\",\n        },\n        {\n          value: \"线式\",\n          label: \"线式\",\n        },\n        {\n          value: \"TMY\",\n          label: \"TMY\",\n        },\n        {\n          value: \"LMY\",\n          label: \"LMY\",\n        },\n      ],\n      //配电变压器类型\n      dyqlxList: [\n        {\n          value: \"降变压\",\n          label: \"降变压\",\n        },\n        {\n          value: \"升压\",\n          label: \"升压\",\n        },\n      ],\n      //配电变压器类型\n      jydjList: [\n        {\n          value: \"A\",\n          label: \"A\",\n        },\n        {\n          value: \"B\",\n          label: \"B\",\n        },\n        {\n          value: \"C\",\n          label: \"C\",\n        },\n        {\n          value: \"D\",\n          label: \"D\",\n        },\n        {\n          value: \"E\",\n          label: \"E\",\n        },\n        {\n          value: \"F\",\n          label: \"F\",\n        },\n        {\n          value: \"H\",\n          label: \"H\",\n        },\n      ],\n      rules: {\n        sblx: [\n          { required: true, message: \"请选择设备类型\", trigger: \"change\" },\n        ],\n        /*sszs: [{required: true, message: '所属站室', trigger: 'change'}],*/\n        //sskgg: [{required: true, message: '所属开关柜', trigger: 'change'}],\n        sbmc: [{ required: true, message: \"设备名称\", trigger: \"blur\" }],\n        yxbh: [{ required: true, message: \"请输入运行编号\", trigger: \"blur\" }],\n        tyrq: [{ required: true, message: \"投运日期\", trigger: \"change\" }],\n        zt: [{ required: true, message: \"状态\", trigger: \"change\" }],\n        dydj: [{ required: true, message: \"电压等级\", trigger: \"blur\" }],\n      },\n      //设备基本信息\n      filterInfo: {\n        data: {},\n        fieldList: [\n          {\n            label: \"所属站室名称\",\n            type: \"select\",\n            value: \"pdsList\",\n            multiple: true,\n            options: [],\n          },\n          {\n            label: \"所属站室运行编号\",\n            type: \"input\",\n            value: \"sszsyxbh\",\n            multiple: true,\n            options: [],\n          },\n          {\n            label: \"所属开关柜名称\",\n            type: \"input\",\n            value: \"sskggmc\",\n            options: [],\n          },\n          {\n            label: \"所属开关柜运行编号\",\n            type: \"input\",\n            value: \"sskggyxbh\",\n            options: [],\n          },\n          { label: \"设备名称\", type: \"input\", value: \"sbmc\" },\n          { label: \"运行编号\", type: \"input\", value: \"yxbh\" },\n          {\n            label: \"状态\",\n            type: \"select\",\n            value: \"ztList\",\n            multiple: true,\n            options: [\n              {\n                value: \"在运\",\n                label: \"在运\",\n              },\n              {\n                value: \"停止使用\",\n                label: \"停止使用\",\n              },\n              {\n                value: \"未就绪\",\n                label: \"未就绪\",\n              },\n              {\n                value: \"报废\",\n                label: \"报废\",\n              },\n              {\n                value: \"备用\",\n                label: \"备用\",\n              },\n            ],\n          },\n          {\n            label: \"投运日期\",\n            type: \"date\",\n            value: \"tyrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\",\n          },\n          { label: \"规格型号\", type: \"input\", value: \"ggxh\" },\n        ],\n      },\n      tableAndPageInfo3: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n          pageResize: \"\"\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true,\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"sszsmc\", label: \"所属站室名称\", minWidth: \"150\" },\n          { prop: \"sszsyxbh\", label: \"所属站室运行编号\", minWidth: \"150\" },\n          { prop: \"sskggmc\", label: \"所属开关柜名称\", minWidth: \"150\" },\n          { prop: \"sskggyxbh\", label: \"所属开关柜运行编号\", minWidth: \"150\" },\n          { prop: \"sbmc\", label: \"设备名称\", minWidth: \"120\" },\n          { prop: \"yxbh\", label: \"运行编号\", minWidth: \"120\" },\n          { prop: \"zt\", label: \"状态\", minWidth: \"80\" },\n          { prop: \"tyrq\", label: \"投运日期\", minWidth: \"100\" },\n          { prop: \"ggxh\", label: \"规格型号\", minWidth: \"120\" },\n          /*{\n              fixed: 'right',\n              prop: 'operation',\n              label: '操作',\n              minWidth: '120px',\n              style: {display: 'block'},\n              operation: [\n                {name: '修改', clickFun: this.getSbXgButton},\n                {name: '详情', clickFun: this.getSbXqButton}\n              ]\n            }*/\n        ],\n      },\n      //状态变更记录信息\n      resumPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true,\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"foreignNum\", label: \"设备名称\", minWidth: \"120\" },\n          { prop: \"sblx\", label: \"设备类型\", minWidth: \"180\" },\n          { prop: \"bglx\", label: \"变更类型\", minWidth: \"120\" },\n          { prop: \"ms\", label: \"描述\", minWidth: \"230\" },\n          { prop: \"bgr\", label: \"变更人\", minWidth: \"140\" },\n          { prop: \"bgsj\", label: \"变更时间\", minWidth: \"160\" },\n        ],\n      },\n      params: {\n        id: \"\",\n        sbmc: \"\",\n        ggxh: \"\",\n        dydj: \"\",\n        tyrqStr: [],\n        sccj: \"\",\n        yxbh: \"\",\n        bz: \"\",\n        sszsmc: \"\",\n        sszsyxbh: \"\",\n        sskggmc: \"\",\n        sskggyxbh: \"\",\n        zt: \"\",\n        kgyt: \"\",\n      },\n\n      //技术参数基本信息\n      jscsForm: {},\n\n      //设备履历缺陷记录数据集合\n      sbllqxjlList: [\n        /*{\n            ssgs: '港东分公司',\n            dzmc: '1号变电站',\n            sblx: '主变压器',\n            qxxz: '严重',\n            dydj: '35kV',\n            sbxh: 'XXX型号',\n            sccj: 'XXX厂家'\n          }*/\n      ],\n      //设备履历试验记录数据\n      sblvsyjlList: [\n        /*{\n            syzy: '带电',\n            syxz: '例行试验',\n            symc: 'XXXXX',\n            gzdd: 'XXX平台',\n            sysb: '主变压器',\n            sybg: '',\n            tq: '晴',\n            syrq: '2022-01-01',\n            lrr: '张三',\n            syjl: 'XXXXX'\n          }*/\n      ],\n      //设备履历tab页\n      sbllDescTabName: \"syjl\",\n\n      //轮播图片\n      imgList: [],\n      //配电设备基本信息\n      jbxxForm: {},\n      //弹出框tab页\n      activeTabName: \"sbDesc\",\n      //设备弹出框\n      dialogFormVisible: false,\n      //加载信息\n      loading: false,\n      //设备履历\n      handleSbllDescTabNameClick: {},\n      // 单击下拉树选中的节点\n      selectNode: \"\",\n      //组织树\n      treeOptions: [],\n\n      //变电站挂接数据\n      newTestData: [],\n      jscsLabelList: [],\n      paramQuery: {\n        sblxbm: undefined,\n      },\n      sbParams: {},\n      pdgSszs:'',//配电柜所属站室id\n      pdgSszsmc:'',//配电柜所属站室名称\n    };\n  },\n  watch: {},\n  async created() {\n    await this.getYwbzList();\n    //初始化加载时加载所有变电站信息\n    this.newTestData = this.bdzList;\n    // this.getData();\n    await this.getpdzData(); //请求配电站数据\n    await this.initDictData();\n    await this.getOptions();//获取下拉框字典\n    this.getNewTreeInfo();\n    this.init();\n    this.getPdsOptionsDataList();\n    getPdgListSelected({}).then((res) => {\n      //所属配电柜筛查条件\n      this.filterInfo.fieldList[1].options = res.data;\n    });\n  },\n  mounted() {\n    //获取token\n    this.header.token = getToken();\n  },\n  methods: {\n    //获取下拉框字典值\n    async getOptions(){\n      await this.getPdgLx();//获取配电柜类型\n    },\n    //获取配电柜类型\n    async getPdgLx(){\n      await getDictTypeData('dwzy_pdg_kkyt').then(res=>{\n        res.data.forEach(item=>{\n          this.pdgkgytList.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    viewHeadDetail(){\n      switch (this.treeNode.identifier){\n        case \"1\"://配电室\n          this.pdzgetXq(this.headForm);\n          break;\n        case \"2\"://配电柜\n          this.pdgDetails(this.headForm);\n          break;\n      }\n    },\n    beforeUpload(file) {\n      const fileSize = file.size < 1024 * 1024 * 50;\n      if (!fileSize) {\n        this.$message.error(\"上传文件大小不能超过 50MB!\");\n      }\n    },\n    async deleteFileById(id) {\n      let { code } = await deleteById(id);\n      if (code === \"0000\") {\n        await this.getFileList();\n        this.$message({\n          type: \"success\",\n          message: \"文件删除成功!\",\n        });\n      }\n    },\n    submitUpload() {\n      this.$refs.upload.submit();\n    },\n    clearUpload() {\n      if (this.$refs.upload) {\n        this.$refs.upload.clearFiles();\n      }\n    },\n    async getFileList() {\n      let { code, data } = await getListByBusinessId({\n        businessId: this.pdzform.objId,\n      });\n      if (code === \"0000\") {\n        this.pdzform.attachment = data;\n        this.imgList = data.map((item) => {\n          let item1 = {};\n          item1.name = item.fileName;\n          item1.url = this.$store.getters.currHost + item.fileUrl;\n          return item1;\n        });\n      }\n    },\n    async getYwbzList() {\n      await selectDeptOneAndTwo({ parentId: 3013 }).then((res) => {\n        this.ywbzList = res.data;\n      });\n    },\n    //列表查询\n    async getData(params) {\n      if (this.pdzshow) {\n        this.getpdzData(params);\n      }\n      if (this.pdgshow) {\n        this.getpdgData(params);\n      }\n      if (this.sbshow) {\n        this.getsbData(params);\n      }\n    },\n    //新增按钮\n    AddSensorButton() {\n      if (this.pdzshow) {\n        this.pdzform = { attachment: [] };\n        this.clearUpload();\n        this.imgList = [];\n        this.isDisabled = false;\n        this.pdsDialogFormVisible = true;\n      }\n      if (this.pdgshow) {\n        this.pdgform.sszs = this.pdgSszsmc;//所属站室回显问题\n        this.isDisabled = false;\n        this.showButton = true;\n        this.bdgDialogFormVisible = true;\n        this.pdgdisable = false;//配电柜表单可编辑\n      }\n      if (this.sbshow) {\n        this.sbtitle = \"设备新增\";\n        this.dialogFormVisible = true;\n        this.jbxxForm.sszs = this.sszs\n        this.getPdgListSelected();\n        this.jbxxForm.sskgg = this.sskgg\n        this.isDisabled = false;\n      }\n    },\n    /*-----------------------配电站相关开始--------------------------*/\n    //表格多选框\n    pdzhandleSelectionChange(selection) {\n      this.ids = selection.map((item) => item.objId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    //配电站表格数据\n    async getpdzData(params) {\n      this.pdzParams = { ...this.pdzParams, ...params };\n      const param = this.pdzParams\n      await getPdsList(param).then((res) => {\n        this.pdzshow = true;\n        this.pdgshow = this.sbshow = false;\n        this.tableAndPageInfo1.tableData = res.data.records;\n        this.tableAndPageInfo1.tableData.forEach((item) => {\n          this.ywbzList.forEach((element) => {\n            if (item.ywbz == element.value) {\n              item.ywbzmc = element.label;\n            }\n          });\n        });\n        this.tableAndPageInfo1.pager.total = res.data.total;\n      });\n    },\n    //保存确定按钮\n    async getDetermine() {\n      let params={\n        lx:\"配电设备\",\n        mc:this.pdzform.pdsmc,\n      }\n      this.$refs[\"pdzform\"].validate((valid) => {\n        if (valid) {\n          addPds(this.pdzform).then((res) => {\n            if (res.code === \"0000\") {\n               //新增成功后发送通知\n               adddwzyfstz(params).then(res =>{\n                    if(res.code === \"0000\"){\n                  }\n                 });\n              this.uploadData.businessId = res.data.objId;\n              this.submitUpload();\n              this.$message.success(\"操作成功，通知已发送！！\");\n              this.pdsDialogFormVisible = false;\n              this.getpdzData();\n            }\n          });\n        } else {\n          setTimeout(() => {\n            var isError = document.getElementsByClassName(\"is-error\");\n            if (isError[0].querySelector(\"input\")) {\n              isError[0].querySelector(\"input\").focus();\n            } else if (isError[0].querySelector(\"textarea\")) {\n              isError[0].querySelector(\"textarea\").focus();\n            }\n          }, 1);\n          return false;\n        }\n      });\n    },\n    //关闭配电站弹框\n    handleClose() {\n      if (this.pdzshow) {\n        this.pdzform = {\n          attachment: [],\n        };\n        // this.$nextTick(() => {\n        //   this.pdzform = this.$options.data().form\n        //   this.resetForm('pdzform')\n        // })\n        this.pdsDialogFormVisible = false;\n      }\n      if (this.pdgshow) {\n        this.pdgform = {};\n        // this.$nextTick(() => {\n        //   this.pdgform = this.$options.data().form\n        //   this.resetForm('pdgform')\n        // })\n        this.bdgDialogFormVisible = false;\n      }\n    },\n    //修改\n    async pdzgetUpdate(row) {\n      this.clearUpload();\n      this.pdzform = { ...row };\n      this.pdzform.attachment = [];\n      await this.getFileList();\n      this.isDisabled = false;\n      this.pdsDialogFormVisible = true;\n    },\n    //详情\n    async pdzgetXq(row) {\n      this.clearUpload();\n      this.pdzform = { ...row };\n      this.pdzform.attachment = [];\n      await this.getFileList();\n      this.isDisabled = true;\n      this.pdsDialogFormVisible = true;\n    },\n    /*---------------------配电站相关结束----------------------*/\n\n    /*---------------------配电柜相关开始-----------------------*/\n    async initDictData() {\n      let { data: pdgzt } = await getDictTypeData(\"pdgzt\");\n      this.pdgztList = pdgzt;\n    },\n    async getpdgData(param) {\n      this.pdgParams = { ...this.pdgParams, ...param };\n      const par = this.pdgParams;\n      try {\n        let { data, code } = await getMergePdgInfo(par);\n        if (code === \"0000\") {\n          this.pdgshow = true;\n          this.pdzshow = this.sbshow = false;\n          this.tableAndPageInfo2.tableData = data.records;\n          this.tableAndPageInfo2.pager.total = data.total;\n          // this.tableAndPageInfo2.pager.pageResize = 'Y';\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //修改\n    pdgUpdate(row) {\n      if (row.sjlx === \"kgg\") {\n        getPdgOne({ objId: row.objId }).then((res) => {\n          if (res.code === \"0000\") {\n            this.bdgDialogFormVisible = true;\n            this.pdgform = res.data;\n            this.pdgform.sszs = this.pdgSszsmc;//处理回显\n            this.isDisabled = false;\n            this.showButton = true;\n            this.pdgdisable = false;//配电柜表单可编辑\n          } else {\n            this.$message.error(\"操作失败\");\n          }\n        });\n      } else if (row.sjlx === \"byq\") {\n        getPdsbOne({ id: row.objId }).then((res) => {\n          if (res.code === \"0000\") {\n            this.sbtitle = \"设备修改\";\n            this.dialogFormVisible = true;\n            this.jbxxForm = res.data;\n            this.isDisabled = false;\n            this.getPdgListSelected();\n            this.technicalParameters(row);\n          } else {\n            this.$message.error(\"操作失败\");\n          }\n        });\n      }\n    },\n    //详情\n    pdgDetails(row) {\n      if (row.sjlx === \"kgg\") {\n        getPdgOne({ objId: row.objId }).then((res) => {\n          if (res.code === \"0000\") {\n            this.bdgDialogFormVisible = true;\n            this.pdgform = res.data;\n            this.pdgform.sszs = this.pdgSszsmc;//回显处理\n            this.isDisabled = true;\n            this.showButton = false;\n            this.pdgdisable = true\n          } else {\n            this.$message.error(\"操作失败\");\n          }\n        });\n      } else if (row.sjlx === \"byq\") {\n        getPdsbOne({ id: row.objId }).then((res) => {\n          if (res.code === \"0000\") {\n            this.sbtitle = \"设备详情查看\";\n            this.dialogFormVisible = true;\n            this.jbxxForm = res.data;\n            this.isDisabled = true;\n            this.getPdgListSelected();\n            this.technicalParameters(row);\n          } else {\n            this.$message.error(\"操作失败\");\n          }\n        });\n      }\n    },\n    //保存修改内容\n    save() {\n      this.$refs.pdgform.validate((valid) => {\n        if (valid) {\n          this.pdgform.sszs = this.pdgSszs;\n          addPdg(this.pdgform).then((res) => {\n            if (res.code == \"0000\") {\n              this.$message.success(\"操作成功\");\n              this.bdgDialogFormVisible = false;\n              this.getpdgData();\n            } else {\n              this.$message.error(\"操作失败\");\n            }\n          });\n        } else {\n          setTimeout(() => {\n            var isError = document.getElementsByClassName(\"is-error\");\n            if (isError[0].querySelector(\"input\")) {\n              isError[0].querySelector(\"input\").focus();\n            } else if (isError[0].querySelector(\"textarea\")) {\n              isError[0].querySelector(\"textarea\").focus();\n            }\n          }, 1);\n          return false;\n        }\n      });\n    },\n\n    /*---------------------配电柜相关结束-----------------------*/\n\n    //设备表格数据\n    async getsbData(params) {\n      this.params = { ...this.params, ...params };\n      const param = this.params;\n      //投用日期范围查询\n      if (param.tyrqStr && param.tyrqStr.length > 0) {\n        param.tyBeginDate = this.dateFormatter(param.tyrqStr[0]);\n        param.tyEndDate = this.dateFormatter(param.tyrqStr[1]);\n      }\n      const { data, code } = await getList(param);\n      if (code === \"0000\") {\n        this.sbshow = true;\n        this.pdgshow = this.pdzshow = false;\n        this.tableAndPageInfo3.tableData = data.records;\n        this.tableAndPageInfo3.pager.total = data.total;\n        // this.tableAndPageInfo3.pager.pageResize = 'Y';\n      }\n    },\n    //获取配电室下拉框数据\n    getPdsOptionsDataList() {\n      getPdsOptionsDataList({}).then((res) => {\n        this.pdsOptionsDataList = res.data\n        //所属配电室筛查条件\n        this.filterInfo.fieldList[0].options = this.pdsOptionsDataList;\n      });\n    },\n    //获取配电柜下拉框数据\n    getPdgListSelected() {\n      getPdgListSelected({ sszs: this.jbxxForm.sszs }).then((res) => {\n        this.sbOptionsDataList = res.data;\n      });\n    },\n    //配电室下拉框中的change事件\n    pdsOptionsChangeClick() {\n      //当发生change事件时先清空之前的信息\n      this.$set(this.jbxxForm, \"sskgg\", \"\");\n      this.getPdgListSelected();\n    },\n    //日期格式化  yyyy-MM-dd\n    dateFormatter(d) {\n      let year = d.getFullYear();\n      let month =\n        d.getMonth() < 9 ? \"0\" + (d.getMonth() + 1) : \"\" + (d.getMonth() + 1);\n      let day = d.getDate() < 10 ? \"0\" + d.getDate() : \"\" + d.getDate();\n      return year + \"-\" + month + \"-\" + day;\n    },\n\n    /*----------------------设备---------------------*/\n    //设备添加按钮\n    sbAddSensorButton() {\n      this.sbtitle = \"设备新增\";\n      this.dialogFormVisible = true;\n      this.isDisabled = false;\n    },\n    //设备基本信息修改\n    getSbXgButton(row) {\n      getPdsbOne({ id: row.id }).then((res) => {\n        if (res.code === \"0000\") {\n          this.sbtitle = \"设备修改\";\n          this.dialogFormVisible = true;\n          this.jbxxForm = res.data;\n          this.isDisabled = false;\n          this.getPdgListSelected();\n          this.technicalParameters(row);\n        } else {\n          this.$message.error(\"操作失败\");\n        }\n      });\n    },\n    //设备详情按钮\n    getSbXqButton(row) {\n      getPdsbOne({ id: row.id }).then((res) => {\n        if (res.code === \"0000\") {\n          this.sbtitle = \"设备详情查看\";\n          this.dialogFormVisible = true;\n          this.jbxxForm = res.data;\n          this.isDisabled = true;\n          this.getPdgListSelected();\n          this.technicalParameters(row);\n        } else {\n          this.$message.error(\"操作失败\");\n        }\n      });\n    },\n\n    /*----------------------配电室---------------------*/\n    //删除按钮\n    //删除配电柜\n    removepdg(id) {\n      // if (this.ids.length !== 0) {\n        let obj=[];\n      obj.push(id);\n        this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\",\n        })\n        .then(() => {\n          removePdg(obj).then(({ code }) => {\n              if (code === \"0000\") {\n                this.$message({\n                  type: \"success\",\n                  message: \"删除成功!\",\n                });\n                this.getpdgData();\n              } else {\n                this.$message({\n                  type: \"error\",\n                  message: \"删除失败!\",\n                });\n              }\n            });\n          })\n         .catch(() => {\n            this.$message({\n              type: \"info\",\n              message: \"已取消删除\",\n            });\n          });\n      // } else {\n      //   this.$message({\n      //     type: \"info\",\n      //     message: \"请选择至少一条数据!\",\n      //   });\n      // }\n    },\n    //删除配电室\n    async deletePds(id) {\n      let obj=[];\n      obj.push(id);\n      // if (this.ids.length != 0) {\n        this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\",\n        })\n          .then(() => {\n            removePds(obj).then(({ code }) => {\n              if (code === \"0000\") {\n                this.$message({\n                  type: \"success\",\n                  message: \"删除成功!\",\n                });\n                this.getpdzData();\n              } else {\n                this.$message({\n                  type: \"error\",\n                  message: \"删除失败!\",\n                });\n              }\n            });\n          })\n          .catch(() => {\n            this.$message({\n              type: \"info\",\n              message: \"已取消删除\",\n            });\n          });\n      // }\n    },\n    //删除设备\n    removesb(id) {\n      // if (this.selectRows.length < 1) {\n      //   this.$message.warning(\"请选择正确的数据！！！\");\n      //   return;\n      // }\n      // let ids = this.selectRows.map((item) => {\n      //   return item.id;\n      // });\n      let obj=[];\n      obj.push(id);\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      }).then(() => {\n        remove(obj)\n          .then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\",\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\",\n              });\n            }\n          })\n          .catch(() => {\n            this.$message({\n              type: \"info\",\n              message: \"已取消删除\",\n            });\n          });\n        this.getsbData();\n      });\n    },\n    async deleteRow(row) {\n      if (this.pdzshow) {\n        this.deletePds(row);\n      }\n      if (this.pdgshow) {\n        this.removepdg(row);\n      }\n      if (this.sbshow) {\n        this.removesb(row);\n      }\n    },\n    //获取HeadForm数据\n    async getHeadFormData(sbId){\n      if(this.treeNode.identifier === \"1\"){//配电站\n        await getPdsList({objId:sbId}).then((res) => {\n          this.headForm = res.data.records[0];\n          this.ywbzList.forEach((element) => {\n            if (this.headForm.ywbz == element.value) {\n              this.headForm.ywbzmc = element.label;\n            }\n          });\n        });\n      }else if(this.treeNode.identifier === \"2\"){//配电柜\n        let { data, code } = await getMergePdgInfo({objId:sbId});\n        if (code === \"0000\") {\n          this.headForm = data.records[0];\n          if (this.headForm.sjlx === \"kgg\"){\n            this.sskgg = this.headForm.objId\n          }\n        }\n      }\n    },\n    //树点击事件\n    handleNodeClick(data, e) {\n      this.treeNode = data;\n      //点击根节点显示所有\n      if (data.identifier === \"0\") {\n        this.pdzParams = {\n          pageNum: 1,\n          pageSize: 10,\n        }\n        this.tableAndPageInfo1.pager.pageResize = 'Y';\n        this.getpdzData();\n      }\n      //点击配电室节点显示附属设备（只归属与配电室不归属任何配电柜的设备）\n      if (data.identifier === \"1\") {\n        //重置Currentpage查询\n        this.pdgParams = {\n          pageNum: 1,\n          pageSize: 10,\n        }\n        this.sszs = data.id;\n        this.pdgSszs = data.id;\n        this.pdgSszsmc = data.label;\n        this.getHeadFormData(data.id);\n        this.pdgParams.sszs = this.sszs;\n        //重置pageNum显示\n        this.tableAndPageInfo2.pager.pageResize = 'Y';\n        this.getpdgData();\n      }\n      //点击配电柜节点显示所属配电设备\n      if (data.identifier === \"2\") {\n        this.params = {\n          pageNum: 1,\n          pageSize: 10,\n        }\n        this.params.pdgList = [data.id];\n        this.params.sszs = this.sszs;\n        this.params.sskgg = data.id;\n        this.sskgg=\"\"\n        this.getHeadFormData(data.id);\n        this.tableAndPageInfo3.pager.pageResize = 'Y';\n        this.getsbData();\n      }\n    },\n    //重置按钮\n    filterReset() {},\n    /*----------------------状态变更---------------------*/\n    //修改设备状态\n    updateStatus(row) {\n      this.updateList.zt = row.zt;\n      this.updateList.id = row.id;\n      this.dialogVisible = true;\n    },\n    //状态变更提交信息\n    submitStatus() {\n      this.$confirm(\"确认将设备状态修改为\" + this.updateList.zt + \"?\", \"\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      }).then((res) => {\n        updateStatus(this.updateList).then((res) => {\n          if (res.code == \"0000\") {\n            this.$message.success(\"设备状态已变更！\");\n            this.dialogVisible = false;\n            this.getData();\n          }\n        });\n      });\n    },\n    //查询状态变更记录信息\n    getResumList(par) {\n      let params = { ...par, ...this.resumeQuery };\n      getResumDataList(params).then((res) => {\n        this.resumPageInfo.tableData = res.data.records;\n        this.resumPageInfo.pager.total = res.data.total;\n      });\n    },\n    //左侧树形数据获取\n    getNewTreeInfo() {\n      getPdsTreeList(this.treeForm).then((res) => {\n        this.treeOptions = res.data;\n      });\n    },\n    //筛选条件\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n    //重置表单\n    resetForm() {\n      this.jbxxForm = {};\n      this.jscsForm = {};\n      this.$nextTick(function () {\n        this.$refs[\"jbxxForm\"].clearValidate();\n      });\n      this.dialogFormVisible = false;\n    },\n    getSblxDataListSelected() {\n      let sblxParam = {\n        type: \"配电设备\",\n      };\n      getSblxDataListSelected(sblxParam).then((res) => {\n        this.sblxOptionsDataSelected = res.data;\n      });\n    },\n    showParams(data) {\n      this.paramQuery.sblxbm = data;\n      this.getParameters();\n    },\n    technicalParameters(row) {\n      //设备类型\n      this.jscsForm = {};\n      this.paramQuery.sblxbm = row.sblx;\n      this.jscsForm.sblxbm = row.sblx;\n      this.jscsForm.sbbm = row.sszsyxbh;\n      this.getParameters();\n    },\n    getParameters() {\n      this.jscsLabelList = [];\n      getParamDataList(this.paramQuery).then((res) => {\n        this.jscsLabelList = res.data;\n        this.getParamValue();\n      });\n    },\n\n    getParamValue() {\n      getParamsValue(this.jscsForm).then((res) => {\n        if (res.data != \"\") {\n          this.jscsForm = { ...res.data[0] };\n        }\n      });\n    },\n    submit() {\n      try {\n        this.jbxxForm.sbClassCsValue = this.jscsForm;\n        let { code } = saveOrUpdate(this.jbxxForm).then((res) => {\n          if (res.code === \"0000\") {\n            this.$message({\n              type: \"success\",\n              message: \"操作成功!\",\n            });\n            this.dialogFormVisible = false;\n            this.getData();\n          }\n        });\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    init() {\n      this.getSblxDataListSelected();\n    },\n  },\n};\n</script>\n\n<style lang='scss' scoped>\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  height: calc(100vh - 225px);\n  overflow: auto;\n}\n\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.box-cardList {\n  height: 70%;\n}\n\n.item {\n  width: 200px;\n  float: left;\n}\n\n#main_container_dj {\n  height: calc(100vh - 84px);\n}\n\n.aside_height {\n  height: 81vh;\n}\n\n.defect .el-form-item:nth-child(odd) {\n  margin-right: 70px;\n}\n\n/*背景颜色调整*/\n#main_container_dj,\n#main_container_dj .el-aside {\n  background-color: #b4caf1;\n}\n\n/deep/ .qxlr_dialog_insert .el-dialog__header {\n  background-color: #0cc283;\n}\n\n/deep/ .pmyBtn {\n  background: #0cc283;\n}\n\n/*!*弹出框内宽度设置*!*/\n.el-select {\n  width: 100%;\n}\n\n.el-date-editor {\n  width: 100%;\n}\n\n.el-carousel__item h3 {\n  color: #475669;\n  font-size: 14px;\n  opacity: 0.75;\n  line-height: 150px;\n}\n\n.el-carousel__item:nth-child(2n) {\n  background-color: #99a9bf;\n}\n\n.el-carousel__item:nth-child(2n + 1) {\n  background-color: #d3dce6;\n}\n\n//有子节点 且未展开\n.el-tree ::v-deep .el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/add.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n\n//有子节点 且已展开\n.el-tree\n  ::v-deep\n  .el-tree-node__expand-icon.expanded.el-icon-caret-right:before {\n  background: url(\"../../../../assets/image/prep.png\") no-repeat 0;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n\n//没有子节点\n.el-tree ::v-deep .el-tree-node__expand-icon.is-leaf::before {\n  background: transparent;\n  content: \"\";\n  display: block;\n  width: 16px;\n  height: 16px;\n  font-size: 16px;\n  background-size: 16px;\n}\n\n.categoryTreeIcons {\n  background: url(\"../../../../assets/icons/icon/icon1.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n\n.tableIcon {\n  background: url(\"../../../../assets/icons/icon/icon2.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n\n.classIcon {\n  background: url(\"../../../../assets/icons/icon/icon5.png\") center no-repeat;\n  background-size: 100% 100%;\n  padding: 1px 10px !important;\n}\n\n.imgCls {\n  height: 150px !important;\n}\n\n#pic_form .el-form-item__content {\n  margin-left: 0 !important;\n}\n</style>\n<style>\n#imgId .el-carousel__container {\n  background-color: #ffffff !important;\n}\n</style>\n"]}]}