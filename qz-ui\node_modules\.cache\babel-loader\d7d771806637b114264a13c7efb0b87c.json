{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\sccjk.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\sccjk.vue", "mtime": 1749558220836}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["sccjk.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAgIA;;AAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,gBAAA,EAAA,EADA;AAEA,MAAA,IAAA,EAAA,EAFA;AAGA;AACA,MAAA,aAAA,EAAA,KAJA;AAKA;AACA,MAAA,UAAA,EAAA,KANA;AAOA;AACA,MAAA,KAAA,EAAA,EARA;AASA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,MAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA;AAFA,SADA;AAIA;AACA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,SAFA;AAGA,UAAA,IAAA,EAAA,mBAHA;AAIA,UAAA,SAAA,EAAA,IAJA;AAKA,UAAA,UAAA,EAAA,IALA;AAMA,UAAA,QAAA,EAAA,IANA;AAOA,UAAA,OAAA,EAAA;AAPA,SADA,EAUA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAVA;AALA,OATA;AA2BA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA;AACA;;;;;;;;;;;;AAHA,SARA;AAwBA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AAxBA,OA3BA;AAqDA,MAAA,MAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,MAAA,EAAA,EAHA;AAIA,QAAA,OAAA,EAAA;AAJA,OArDA;AA2DA,MAAA,UAAA,EAAA,EA3DA;AA4DA,MAAA,cAAA,EAAA,KA5DA;AA6DA,MAAA,QAAA,EAAA,KA7DA;AA8DA,MAAA,KAAA,EAAA;AACA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAPA;AA9DA,KAAA;AA0EA,GA7EA;AA8EA,EAAA,OA9EA,qBA8EA;AACA;AACA,SAAA,OAAA;AACA,SAAA,mBAAA;AACA,GAlFA;AAmFA,EAAA,KAAA,EAAA;AACA,IAAA,aADA,yBACA,GADA,EACA;AACA,UAAA,GAAA,EAAA;AACA,YAAA,EAAA,GAAA,KAAA,GAAA,CAAA,aAAA,CAAA,YAAA,CAAA;AACA,QAAA,EAAA,CAAA,KAAA,CAAA,IAAA,GAAA,CAAA;AACA,QAAA,EAAA,CAAA,KAAA,CAAA,GAAA,GAAA,CAAA;AACA;AACA;AAPA,GAnFA;AA4FA,EAAA,OAAA,EAAA;AACA,IAAA,mBADA,iCACA;AAAA;;AACA,uCAAA,CAAA,MAAA,EAAA,MAAA,EAAA,MAAA,CAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,KAAA,CAAA,gBAAA,GAAA,GAAA,CAAA,IAAA;;AACA,UAAA,KAAA,CAAA,UAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,gBAAA,IAAA,CAAA,KAAA,KAAA,SAAA,EAAA;AACA,cAAA,IAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;AACA;AACA;AACA,WALA;AAMA,SARA,MAQA;AACA,UAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,UAAA;AACA;AACA,OAZA;AAaA,KAfA;AAgBA;AACA,IAAA,OAjBA,mBAiBA,MAjBA,EAiBA;AAAA;;AACA,WAAA,KAAA,CAAA,KAAA,CAAA,OAAA,GAAA,IAAA;AACA,WAAA,MAAA,+DAAA,KAAA,MAAA,GAAA,MAAA;AACA,UAAA,KAAA,GAAA,KAAA,MAAA;AACA,0BAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA;AACA,cAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,YAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;;AACA,YAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,cAAA,MAAA,CAAA,KAAA,CAAA,KAAA,CAAA,OAAA,GAAA,KAAA;AACA,aAHA;AAIA;AACA,SATA,CASA,OAAA,CAAA,EAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,CAAA;AACA;AACA,OAbA;AAcA,KAnCA;AAoCA;AACA,IAAA,QArCA,sBAqCA,CAAA,CArCA;AAsCA;AACA,IAAA,qBAvCA,mCAuCA,CAAA,CAvCA;AAwCA;AACA,IAAA,SAzCA,uBAyCA;AACA,WAAA,KAAA,GAAA,SAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,IAAA,GAAA,EAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,KA9CA;AA+CA;AACA,IAAA,SAhDA,qBAgDA,GAhDA,EAgDA;AACA,WAAA,KAAA,GAAA,SAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,KArDA;AAsDA;AACA,IAAA,UAvDA,sBAuDA,GAvDA,EAuDA;AACA,WAAA,KAAA,GAAA,WAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,KA5DA;;AA6DA;;;;;;;AAOA,IAAA,OApEA,qBAoEA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,sBAAA,KAAA,EAAA;AACA,6CAAA,MAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,0BAAA;AACA,4BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,0BAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA;AACA,uBAJA,CAIA,OAAA,CAAA,EAAA;AACA,wBAAA,OAAA,CAAA,GAAA,CAAA,CAAA;AACA;;AACA,sBAAA,MAAA,CAAA,OAAA;AACA,qBATA;AAUA,mBAXA,MAWA;AACA,2BAAA,KAAA;AACA;;AACA,kBAAA,MAAA,CAAA,aAAA,GAAA,KAAA;AACA,iBAhBA;;AADA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA,KAtFA;AAuFA;AACA,IAAA,SAxFA,qBAwFA,EAxFA,EAwFA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAA,KARA,GAQA,EARA;AASA,gBAAA,KAAA,CAAA,IAAA,CAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAKA,IALA,CAKA,YAAA;AACA,qCAAA,KAAA,EAAA,IAAA,CAAA,gBAAA;AAAA,wBAAA,IAAA,QAAA,IAAA;;AACA,wBAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,SADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;;AAIA,sBAAA,MAAA,CAAA,OAAA;AACA,qBANA,MAMA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,OADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;AAIA;AACA,mBAbA;AAcA,iBApBA,EAqBA,KArBA,CAqBA,YAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA;AACA,oBAAA,IAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;AAIA,iBA1BA;;AA2BA,gBAAA,MAAA,CAAA,OAAA;;AArCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsCA,KA9HA;AA+HA;AACA,IAAA,KAhIA,mBAgIA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,KAlIA;AAmIA,IAAA,YAnIA,wBAmIA,IAnIA,EAmIA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KArIA;AAsIA;AACA,IAAA,oBAvIA,kCAuIA;AACA,WAAA,QAAA,GAAA,KAAA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,KA1IA;AA2IA;AACA,IAAA,eA5IA,2BA4IA,GA5IA,EA4IA;AACA,UAAA,GAAA,CAAA,MAAA,CAAA,IAAA,KAAA,QAAA,EAAA;AACA,aAAA,cAAA,GAAA,IAAA;AACA,aAAA,QAAA,GAAA,IAAA;AACA;AACA,KAjJA;AAkJA;AACA,IAAA,iBAnJA,6BAmJA,GAnJA,EAmJA;AAAA;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,MAAA,EAAA,GAAA;;AACA,UAAA,KAAA,QAAA,EAAA;AACA,aAAA,UAAA,CAAA,IAAA,CAAA,OAAA,GAAA,EAAA;AACA,aAAA,UAAA,CAAA,IAAA,CAAA,MAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,OAAA,EAAA;AACA,YAAA,MAAA,CAAA,UAAA,CAAA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA;;AACA,YAAA,MAAA,CAAA,UAAA,CAAA,IAAA,CAAA,MAAA,IAAA,IAAA,CAAA,IAAA,GAAA,GAAA;AACA;AACA,SALA;AAOA,aAAA,UAAA,CAAA,IAAA,CAAA,MAAA,GAAA,KAAA,UAAA,CAAA,IAAA,CAAA,MAAA,CAAA,SAAA,CACA,CADA,EAEA,KAAA,UAAA,CAAA,IAAA,CAAA,MAAA,CAAA,MAAA,GAAA,CAFA,CAAA;AAIA,aAAA,cAAA,GAAA,KAAA;AACA,OAfA,MAeA;AACA,YAAA,SAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,OAAA,EAAA;AACA,YAAA,SAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,SAJA;;AAKA,YAAA,SAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,eAAA,IAAA,CAAA,MAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA,eAAA,IAAA,CAAA,MAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA,eAAA,cAAA,GAAA,KAAA;AACA,SAJA,MAIA;AACA,eAAA,QAAA,CAAA,OAAA,CAAA,WAAA;AACA;AACA;AACA,KAnLA;AAoLA,IAAA,qBApLA,mCAoLA;AACA,WAAA,cAAA,GAAA,KAAA;AACA,KAtLA;AAuLA;AACA,IAAA,WAxLA,yBAwLA;AAAA;;AACA,WAAA,IAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,YAAA;AACA,QAAA,MAAA,CAAA,IAAA,GAAA,MAAA,CAAA,QAAA,CAAA,IAAA,GAAA,IAAA;;AACA,QAAA,MAAA,CAAA,SAAA,CAAA,MAAA;AACA,OAHA;AAIA;AA9LA;AA5FA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 120, itemWidth: 230 }\"\n      @handleReset=\"getReset\"\n    />\n\n    <el-white class=\"button-group1\">\n      <div class=\"button_btn\">\n        <el-button\n          type=\"primary\"\n          v-hasPermi=\"['bzsccj:button:add']\"\n          icon=\"el-icon-plus\"\n          @click=\"getInster\"\n          >新增\n        </el-button>\n      </div>\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"selectChange\"\n        height=\"68vh\"\n        ref=\"sccjk\"\n      >\n        <el-table-column\n          slot=\"table_eight\"\n          align=\"center\"\n          fixed=\"right\"\n          style=\"display: block\"\n          label=\"操作\"\n          width=\"160\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              @click=\"getUpdate(scope.row)\"\n              v-hasPermi=\"['bzsccj:button:update']\"\n              type=\"text\"\n              size=\"small\"\n              class=\"el-icon-edit\"\n              title=\"修改\"\n            >\n            </el-button>\n            <el-button\n              @click=\"getDetails(scope.row)\"\n              type=\"text\"\n              size=\"small\"\n              title=\"详情\"\n              class=\"el-icon-view\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              v-if=\"scope.row.createBy === $store.getters.name\"\n              title=\"删除\"\n              icon=\"el-icon-delete\"\n              @click=\"deleteRow(scope.row.sccjkId)\"\n            >\n            </el-button>\n          </template>\n        </el-table-column>\n      </comp-table>\n    </el-white>\n\n    <!-- 详情/新增/修改 -->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"30%\"\n      @close=\"handleClose\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"130px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row class=\"box-card\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"设备分类：\" prop=\"dysblx\">\n              <el-select\n                placeholder=\"请选择设备分类\"\n                v-model=\"form.dysblx\"\n                :disabled=\"isDisabled\"\n                clearable\n                filterable\n                class=\"custom-group\"\n              >\n                <el-option-group\n                  v-for=\"group in DevicesListGroup\"\n                  :key=\"group.label\"\n                  :label=\"group.label\"\n                >\n                  <el-option\n                    v-for=\"item in group.sbDataList\"\n                    :key=\"item.code\"\n                    :label=\"item.name\"\n                    :value=\"item.code\"\n                  >\n                  </el-option>\n                </el-option-group>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"生产厂家名称：\" prop=\"sccjmc\">\n              <el-input\n                clearable\n                placeholder=\"请输入生产厂家名称\"\n                :disabled=\"isDisabled\"\n                v-model=\"form.sccjmc\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\" size=\"small\">取 消</el-button>\n        <el-button\n          v-if=\"title === '生产厂家库增加' || title === '生产厂家库修改'\"\n          type=\"primary\"\n          size=\"small\"\n          @click=\"saveRow\"\n          >确 认\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  getList,\n  remove,\n  saveOrUpdate\n} from \"@/api/dagangOilfield/bzgl/sbbzk/sccjk\";\nimport { getDeviceClassGroup } from \"@/api/dagangOilfield/bzgl/sblxwh/sblxwh\";\nexport default {\n  name: \"index\",\n  data() {\n    return {\n      DevicesListGroup: [],\n      form: {},\n      //详情弹框是否显示\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          sccjmc: \"\",\n          sblxArr: []\n        }, //查询条件\n        fieldList: [\n          {\n            label: \"设备分类\",\n            value: \"sblxArr\",\n            type: \"selectGroupjxxmwh\",\n            clearable: true,\n            filterable: true,\n            multiple: true,\n            options: []\n          },\n          { label: \"生产厂家名称\", type: \"input\", value: \"sccjmc\" }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 120]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"设备分类\", prop: \"sblxmc\", minWidth: \"180\" },\n          { label: \"生产厂家名称\", prop: \"sccjmc\", minWidth: \"180\" }\n          /*{\n            prop: 'operation',\n            label: '操作',\n            minWidth: '130px',\n            style: { display: 'block' },\n            //操作列固定再右侧\n            fixed: 'right',\n            operation: [\n              { name: '修改', clickFun: this.getUpdate },\n              { name: '详情', clickFun: this.getDetails }\n            ]\n          }*/\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        pageNum: 1,\n        pageSize: 10,\n        sccjmc: \"\",\n        sblxArr: []\n      },\n      selectRows: [],\n      showDeviceTree: false,\n      isFilter: false,\n      rules: {\n        dysblx: [\n          { required: true, message: \"设备分类不能为空\", trigger: \"change\" }\n        ],\n        sblxmc: [\n          { required: true, message: \"设备分类不能为空\", trigger: \"change\" }\n        ],\n        sccjmc: [\n          { required: true, message: \"生产厂家不能为空\", trigger: \"blur\" }\n        ]\n      }\n    };\n  },\n  mounted() {\n    //列表查询\n    this.getData();\n    this.getDeviceClassGroup();\n  },\n  watch: {\n    isShowDetails(val) {\n      if (val) {\n        const el = this.$el.querySelector(\".el-dialog\");\n        el.style.left = 0;\n        el.style.top = 0;\n      }\n    }\n  },\n  methods: {\n    getDeviceClassGroup() {\n      getDeviceClassGroup([\"bdsb\", \"pdsb\", \"sdsb\"]).then(res => {\n        if (res.code === \"0000\") {\n          this.DevicesListGroup = res.data;\n          this.filterInfo.fieldList.forEach(item => {\n            if (item.value === \"sblxArr\") {\n              item.options = res.data;\n              return;\n            }\n          });\n        } else {\n          this.$message.error(\"获取设备分类失败\");\n        }\n      });\n    },\n    //列表查询\n    getData(params) {\n      this.$refs.sccjk.loading = true;\n      this.params = { ...this.params, ...params };\n      const param = this.params;\n      getList(param).then(res => {\n        try {\n          if (res.code === \"0000\") {\n            this.tableAndPageInfo.tableData = res.data.records;\n            this.tableAndPageInfo.pager.total = res.data.total;\n            this.$nextTick(() => {\n              // 以服务的方式调用的 Loading 需要异步关闭\n              this.$refs.sccjk.loading = false;\n            });\n          }\n        } catch (e) {\n          console.log(e);\n        }\n      });\n    },\n    //重置按钮\n    getReset() {},\n    //选中行\n    handleSelectionChange() {},\n    //新增按钮\n    getInster() {\n      this.title = \"生产厂家库增加\";\n      this.isDisabled = false;\n      this.form = {};\n      this.isShowDetails = true;\n    },\n    //修改按钮\n    getUpdate(row) {\n      this.title = \"生产厂家库修改\";\n      this.isDisabled = false;\n      this.form = { ...row };\n      this.isShowDetails = true;\n    },\n    //详情按钮\n    getDetails(row) {\n      this.title = \"生产厂家库详情查看\";\n      this.form = { ...row };\n      this.isDisabled = true;\n      this.isShowDetails = true;\n    },\n    /*//附件查看\n    getFjInfoList() {\n      this.title = '附件查看'\n      this.isDisabled = true\n      this.isShowDetails = true\n      this.form={...row}\n    },*/\n    async saveRow() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          saveOrUpdate(this.form).then(res => {\n            try {\n              if (res.code === \"0000\") {\n                this.$message.success(\"操作成功\");\n              }\n            } catch (e) {\n              console.log(e);\n            }\n            this.getData();\n          });\n        } else {\n          return false;\n        }\n        this.isShowDetails = false;\n      });\n    },\n    //删除按钮\n    async deleteRow(id) {\n      // if (this.selectRows.length < 1) {\n      //   this.$message.warning('请选择正确的数据！！！')\n      //   return\n      // }\n      // let ids = this.selectRows.map(item => {\n      //   return item.sccjkId\n      // })\n      let sccjk = [];\n      sccjk.push(id);\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove(sccjk).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n      this.getData();\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false;\n    },\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n    //显示设备类型树弹窗\n    showDeviceTreeDialog() {\n      this.isFilter = false;\n      this.showDeviceTree = true;\n    },\n    //鼠标聚焦事件\n    inputFocusEvent(val) {\n      if (val.target.name === \"dysblx\") {\n        this.showDeviceTree = true;\n        this.isFilter = true;\n      }\n    },\n    //获取设备分类数据\n    getDeviceTypeData(res) {\n      console.log(\"res：\", res);\n      if (this.isFilter) {\n        this.filterInfo.data.sblxArr = [];\n        this.filterInfo.data.dysblx = \"\";\n        res.forEach(item => {\n          if (item.checked) {\n            this.filterInfo.data.sblxArr.push(item.code);\n            this.filterInfo.data.dysblx += item.name + \",\";\n          }\n        });\n\n        this.filterInfo.data.dysblx = this.filterInfo.data.dysblx.substring(\n          0,\n          this.filterInfo.data.dysblx.length - 1\n        );\n        this.showDeviceTree = false;\n      } else {\n        let treeNodes = [];\n        res.forEach(item => {\n          if (item.checked) {\n            treeNodes.push(item);\n          }\n        });\n        if (treeNodes.length === 1) {\n          this.form.sblxmc = treeNodes[0].name;\n          this.form.dysblx = treeNodes[0].code;\n          this.showDeviceTree = false;\n        } else {\n          this.$message.warning(\"请选择单条设备数据\");\n        }\n      }\n    },\n    closeDeviceTypeDialog() {\n      this.showDeviceTree = false;\n    },\n    //清空表单数据\n    handleClose() {\n      this.form = {};\n      this.$nextTick(() => {\n        this.form = this.$options.data().form;\n        this.resetForm(\"form\");\n      });\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.button-group {\n  padding-left: 30px;\n  padding-right: 30px;\n  display: flex;\n  justify-content: flex-end;\n}\n\n/*控制input输入框边框是否显示*/\n.elInput >>> .el-input__inner {\n  border: 0;\n}\n\n.box-card {\n  margin-bottom: 2vh !important;\n}\n\n/deep/ .el-select-group__title {\n  font-size: 14px;\n  font-weight: bold;\n  color: #11ba6b;\n  padding: 8px 0;\n}\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbbzk"}]}