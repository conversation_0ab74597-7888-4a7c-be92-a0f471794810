{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sympk\\sympInfo.vue?vue&type=style&index=0&id=741a570e&scoped=true&lang=css&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sympk\\sympInfo.vue", "mtime": 1706897323740}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5mb3JtLWl0ZW0gewogIHdpZHRoOiA4MCU7Cn0K"}, {"version": 3, "sources": ["sympInfo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0PA;AACA;AACA", "file": "sympInfo.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sympk", "sourcesContent": ["<template>\n  <div>\n    <el-white>\n      <el-white class=\"button-group\">\n        <el-button class=\"mb8\" @click=\"addButton\" type=\"primary\" icon=\"el-icon-plus\">\n          新增\n        </el-button>\n        <el-button class=\"mb8\" @click=\"deleteButton\" type=\"danger\" icon=\"el-icon-delete\">\n          删除\n        </el-button>\n      </el-white>\n      <comp-table ref=\"mainTable\" :table-and-page-info=\"tableAndPageInfo\"\n                  @update:multipleSelection=\"handleSelectionChange\"/>\n    </el-white>\n\n    <el-dialog\n      :title=\"dialogTittle\"\n      v-dialogDrag\n      :visible.sync=\"isShowDetails\"\n      :append-to-body=\"true\"\n      v-if=\"isShowDetails\"\n      width=\"40%\">\n      <el-form ref=\"mpInfoForm\" :model=\"mpInfoForm\" label-width=\"80px\">\n        <el-row :gutter=\"8\">\n          <el-col :span=\"12\">\n            <el-form-item prop=\"title\" label=\"标题:\">\n              <el-input v-model=\"mpInfoForm.title\" class=\"form-item\" placeholder=\"请输入标题\"\n                        :disabled=\"isDetails\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"内容来源:\" prop=\"contentSource\">\n              <el-select clearable :disabled=\"isDetails\" class=\"form-item\" v-model=\"mpInfoForm.contentSource\">\n                <el-option v-for=\"item in contentSourceOptions\"\n                           :key=\"item.value\"\n                           :label=\"item.label\"\n                           :value=\"item.value\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"字段名称:\" prop=\"columnName\">\n              <el-select clearable :disabled=\"isDetails\" class=\"form-item\" v-model=\"mpInfoForm.columnName\">\n                <el-option v-for=\"item in columnNameOptions\"\n                           :key=\"item.value\"\n                           :label=\"item.label\"\n                           :value=\"item.value\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"handleClose\">取 消</el-button>\n        <el-button v-show=\"!isDetails\" type=\"primary\" @click=\"submitForm\">确 定</el-button>\n      </div>\n    </el-dialog>\n\n  </div>\n\n</template>\n\n<script>\nimport {\n  deleteNameplateContent,\n  getColumnNameOptions,\n  getNameplateContent,\n  saveNameplateContent\n} from '@/api/dagangOilfield/bzgl/sympk/sympInfo'\n\nexport default {\n  props: {\n    mpData: {\n      type: Object\n    }\n  },\n  name: 'sympInfo',\n  data() {\n    return {\n      //表单 分页数据\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [], tableHeader: [\n          { label: '标题', prop: 'title', minWidth: '180' },\n          { label: '内容来源', prop: 'contentSourceName', minWidth: '200' },\n          { label: '字段名', prop: 'zdmc', minWidth: '200' },\n          {\n            prop: 'operation',\n            label: '操作',\n            minWidth: '100px',\n            style: { display: 'block' },\n            //操作列固定再右侧\n            fixed: 'right',\n            operation: [\n              { name: '修改', clickFun: this.updateDetails },\n              { name: '详情', clickFun: this.getDetails }\n            ]\n          }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      mpInfoForm: {\n        id: '',\n        title: '',\n        order: '',\n        contentSource: '',\n        columnName: '',\n        mpid: ''\n      },\n      //是否展示详情弹窗\n      isShowDetails: false,\n      //铭牌内容弹窗标题\n      dialogTittle: '',\n      //是否详情\n      isDetails: false,\n      //内容来源下拉框数据\n      contentSourceOptions: [{ label: '技术参数', value: 'jscs' }, { label: '设备', value: 'sb' }],\n      //字段名称下拉框数据\n      columnNameOptions: [],\n      //选中行数据\n      selectedRowData: [],\n      //查询条件\n      params: {\n        pageNum: 1,\n        pageSize: 10,\n        mpid: '',\n        zy: ''\n      }\n    }\n  },\n  mounted() {\n    this.getData()\n  },\n  methods: {\n    //获取铭牌内容数据\n    getData() {\n      debugger;\n      this.$refs.mainTable.loading = true\n      this.params.mpid = this.mpData.obj_id\n      this.params.zy = this.mpData.zy\n      this.params.sblxbm = this.mpData.sblxbm\n      getNameplateContent(this.params).then(res => {\n        if (res.code === '0000') {\n          this.tableAndPageInfo.tableData = res.data.records\n          this.tableAndPageInfo.pager.total = res.data.total\n          //列表转码\n          this.tableAndPageInfo.tableData.forEach(item => {\n            //内容来源为设备时 切除专业编码\n            if (item.contentSource.indexOf('sb') === 0) {\n              item.contentSource = item.contentSource.substring(0, item.contentSource.length - 2)\n            }\n            this.contentSourceOptions.forEach(element => {\n              if (item.contentSource.indexOf(element.value) === 0) {\n                item.contentSourceName = element.label\n              }\n            })\n          })\n          this.$refs.mainTable.loading = false\n        }\n      })\n    },\n    //新增按钮\n    addButton() {\n      this.dialogTittle = '新增铭牌内容'\n      this.mpInfoForm = this.$options.data().mpInfoForm\n      this.mpInfoForm.mpid = this.mpData.obj_id\n      this.isShowDetails = true\n      this.isDetails = false\n    },\n    //修改\n    updateDetails(row) {\n      this.dialogTittle = '修改铭牌内容'\n      this.mpInfoForm = row\n      this.isDetails = false\n      this.isShowDetails = true\n    },\n    //详情\n    getDetails(row) {\n      this.dialogTittle = '铭牌内容详情'\n      this.mpInfoForm = row\n      this.isDetails = true\n      this.isShowDetails = true\n    },\n    //删除\n    deleteButton() {\n\n      this.$confirm('是否确认删除当前勾选的数据?', '警告', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function() {\n        let ids = []\n        this.selectedRowData.forEach(item => {\n          ids.push(item.id)\n        })\n\n        deleteNameplateContent(ids).then(res => {\n          if (res.code === '0000') {\n            this.$message.success('删除成功')\n          } else {\n            this.$message.error(res.msg)\n          }\n        })\n      }).catch(function() {\n      })\n\n    },\n\n    handleSelectionChange(row) {\n      this.selectedRowData = row\n    },\n\n    handleClose() {\n      this.isShowDetails = false\n    },\n    submitForm() {\n      this.mpInfoForm.zy = this.mpData.zy\n      saveNameplateContent(this.mpInfoForm).then(res => {\n        if (res.code === '0000') {\n          this.isShowDetails = false\n          this.getData()\n        }\n      })\n    }\n\n  },\n  watch: {\n    'mpInfoForm.contentSource'(val) {\n      if (!val || val === '') {\n        this.columnNameOptions = []\n      } else {\n        let params = {}\n        params.type = val\n        params.zy = this.mpData.zy\n        params.sblxbm = this.mpData.sblxbm\n        getColumnNameOptions(params).then(res => {\n          this.columnNameOptions = res.data\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.form-item {\n  width: 80%;\n}\n</style>\n"]}]}