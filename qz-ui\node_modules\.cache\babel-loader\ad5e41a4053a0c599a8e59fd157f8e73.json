{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\jxjjhgl\\jxgl\\jxjl.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\jxjjhgl\\jxgl\\jxjl.js", "mtime": 1706897314684}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0TGlzdCA9IGdldExpc3Q7CmV4cG9ydHMucmVtb3ZlID0gcmVtb3ZlOwpleHBvcnRzLnNhdmVPclVwZGF0ZSA9IHNhdmVPclVwZGF0ZTsKCnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwoKdmFyIGJhc2VVcmwgPSAiL3JlcGFpci1zY2hlZHVsZS1hcGkiOyAvL+WIl+ihqOafpeivogoKZnVuY3Rpb24gZ2V0TGlzdChwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy9qeGpsL3BhZ2UnLCBwYXJhbXMsIDEpOwp9IC8v5Yig6ZmkCgoKZnVuY3Rpb24gcmVtb3ZlKHBhcmFtcykgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3RQb3N0KGJhc2VVcmwgKyAnL2p4amwvcmVtb3ZlJywgcGFyYW1zLCAxKTsKfSAvL+S/ruaUueaIluaWsOWingoKCmZ1bmN0aW9uIHNhdmVPclVwZGF0ZShwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy9qeGpsL3NhdmVPclVwZGF0ZScsIHBhcmFtcywgMSk7Cn0="}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagang<PERSON>field/jxjjhgl/jxgl/jxjl.js"], "names": ["baseUrl", "getList", "params", "api", "requestPost", "remove", "saveOrUpdate"], "mappings": ";;;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,sBAAhB,C,CACA;;AACO,SAASC,OAAT,CAAiBC,MAAjB,EAAyB;AAC9B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,YAAxB,EAAqCE,MAArC,EAA4C,CAA5C,CAAP;AACD,C,CACD;;;AACO,SAASG,MAAT,CAAgBH,MAAhB,EAAwB;AAC7B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,cAAxB,EAAuCE,MAAvC,EAA8C,CAA9C,CAAP;AACD,C,CACD;;;AACO,SAASI,YAAT,CAAsBJ,MAAtB,EAA8B;AACnC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,oBAAxB,EAA6CE,MAA7C,EAAoD,CAApD,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\nconst baseUrl = \"/repair-schedule-api\";\n//列表查询\nexport function getList(params) {\n  return api.requestPost(baseUrl+'/jxjl/page',params,1)\n}\n//删除\nexport function remove(params) {\n  return api.requestPost(baseUrl+'/jxjl/remove',params,1)\n}\n//修改或新增\nexport function saveOrUpdate(params) {\n  return api.requestPost(baseUrl+'/jxjl/saveOrUpdate',params,1)\n}\n\n\n"]}]}