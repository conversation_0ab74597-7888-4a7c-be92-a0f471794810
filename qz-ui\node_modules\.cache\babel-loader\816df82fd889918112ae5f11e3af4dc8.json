{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\jxbzk\\jxzqwh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\jxbzk\\jxzqwh.vue", "mtime": 1706897322435}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["jxzqwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AA+GA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,QADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,UAAA,EAAA;AAAA,GAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA,MAAA,WAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,IADA;AAEA;AACA,MAAA,IAAA,EAAA,EAHA;AAIA;AACA,MAAA,aAAA,EAAA,KALA;AAMA;AACA,MAAA,UAAA,EAAA,KAPA;AAQA;AACA,MAAA,SAAA,EAAA,EATA;AAUA,MAAA,QAAA,EAAA,EAVA;AAWA,MAAA,QAAA,EAAA,EAXA;AAYA;AACA,MAAA,KAAA,EAAA,EAbA;AAcA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,OAAA,EAAA,EADA;AAEA,UAAA,IAAA,EAAA,EAFA;AAGA,UAAA,OAAA,EAAA,EAHA;AAIA,UAAA,IAAA,EAAA,EAJA;AAKA,UAAA,IAAA,EAAA;AALA,SADA;AAQA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,MAFA;AAGA,UAAA,IAAA,EAAA,OAHA;AAIA,UAAA,SAAA,EAAA;AAJA,SADA,EASA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,MAFA;AAGA,UAAA,IAAA,EAAA;AAHA,SATA;AAcA;;;;;;;;;;AAUA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,MAFA;AAGA,UAAA,IAAA,EAAA,UAHA;AAIA,UAAA,aAAA,EAAA,EAJA;AAKA,UAAA,QAAA,EAAA,IALA;AAMA,UAAA,OAAA,EAAA,EANA;AAOA,UAAA,SAAA,EAAA;AAPA,SAxBA,EAiCA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,MAFA;AAGA;AACA,UAAA,IAAA,EAAA,UAJA;AAKA,UAAA,aAAA,EAAA,EALA;AAMA,UAAA,OAAA,EAAA,EANA;AAOA,UAAA,SAAA,EAAA;AAPA,SAjCA;AARA,OAdA;AAkEA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA;AACA;;;;;;;;;;;;AALA,SARA;AA0BA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AA1BA,OAlEA;AA8FA,MAAA,MAAA,EAAA;AACA,QAAA,OAAA,EAAA,EADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA;AAJA,OA9FA;AAoGA,MAAA,UAAA,EAAA,EApGA;AAqGA,MAAA,cAAA,EAAA,KArGA;AAsGA,MAAA,QAAA,EAAA,KAtGA;AAuGA,MAAA,KAAA,EAAA;AACA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAVA;AAaA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAbA;AAvGA,KAAA;AAyHA,GA7HA;AA8HA,EAAA,OA9HA,qBA8HA;AACA,SAAA,UAAA;AACA,GAhIA;AAiIA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,OAFA,mBAEA,MAFA,EAEA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,KAAA,CAAA,MAAA,+DAAA,KAAA,CAAA,MAAA,GAAA,MAAA;AACA,gBAAA,KAHA,GAGA,KAAA,CAAA,MAHA;AAIA,gBAAA,KAAA,CAAA,OAAA,GAAA,KAAA,CAAA,IAAA,KAAA,EAAA,GAAA,EAAA,GAAA,KAAA,CAAA,OAAA;AAJA;AAAA,uBAKA,qBAAA,KAAA,CALA;;AAAA;AAAA;AAKA,gBAAA,IALA,kBAKA,IALA;AAKA,gBAAA,IALA,kBAKA,IALA;;AAMA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,KAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,KAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;;AAEA,kBAAA,KAAA,CAAA,gBAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,oBAAA,KAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,OAAA,EAAA;AACA,0BAAA,IAAA,CAAA,IAAA,KAAA,OAAA,CAAA,KAAA,EAAA;AACA,wBAAA,IAAA,CAAA,QAAA,GAAA,OAAA,CAAA,KAAA;AACA;AACA;AACA,qBALA;;AAMA,oBAAA,KAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,OAAA,EAAA;AACA,0BAAA,IAAA,CAAA,IAAA,KAAA,OAAA,CAAA,KAAA,EAAA;AACA,wBAAA,IAAA,CAAA,QAAA,GAAA,OAAA,CAAA,KAAA;AACA;AACA;AACA,qBALA;AAMA,mBAbA;AAcA;;AAxBA;AAAA;;AAAA;AAAA;AAAA;AA0BA,gBAAA,OAAA,CAAA,GAAA;;AA1BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA4BA,KA9BA;AA+BA;AACA,IAAA,QAhCA,sBAgCA;AACA,WAAA,UAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,KAAA,UAAA,EAAA;AACA,UAAA,IAAA,CAAA,aAAA,GAAA,EAAA;AACA;AACA,OAJA;AAKA,WAAA,MAAA,GAAA;AACA,QAAA,OAAA,EAAA,EADA;AAEA,QAAA,OAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA;AAJA,OAAA;AAMA,KA5CA;AA6CA;AACA,IAAA,qBA9CA,mCA8CA,CAEA,CAhDA;AAiDA;AACA,IAAA,UAlDA,wBAkDA;AACA,WAAA,KAAA,GAAA,QAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,KAvDA;AAwDA;AACA,IAAA,SAzDA,uBAyDA;AACA,WAAA,KAAA,GAAA,QAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,IAAA,GAAA;AACA,QAAA,IAAA,EAAA,EADA;AAEA,QAAA,MAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA,EAJA;AAKA,QAAA,IAAA,EAAA,EALA;AAMA,QAAA,KAAA,EAAA;AANA,OAAA;AAQA,KArEA;AAsEA;AACA,IAAA,SAvEA,qBAuEA,GAvEA,EAuEA;AACA,WAAA,KAAA,GAAA,QAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,KA5EA;AA6EA;AACA,IAAA,OA9EA,mBA8EA,GA9EA,EA8EA;AACA,WAAA,KAAA,GAAA,QAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,KAnFA;AAoFA,IAAA,OApFA,qBAoFA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,MAAA,CAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,sBAAA,KAAA,EAAA;AACA,8CAAA,MAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AAEA,0BAAA;AACA,4BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,0BAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA;AACA,uBAJA,CAIA,OAAA,CAAA,EAAA;AACA,wBAAA,OAAA,CAAA,GAAA,CAAA,CAAA;AACA;;AACA,sBAAA,MAAA,CAAA,OAAA;AACA,qBAVA;AAWA,mBAZA,MAYA;AACA,2BAAA,KAAA;AACA;;AACA,kBAAA,MAAA,CAAA,aAAA,GAAA,KAAA;AACA,iBAjBA;;AAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoBA,KAxGA;AAyGA;AACA,IAAA,SA1GA,qBA0GA,GA1GA,EA0GA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,IAAA,mCAAA,GAAA;;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAIA,IAJA,CAIA,YAAA;AACA,sCAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,EAAA,IAAA,CAAA,gBAAA;AAAA,wBAAA,IAAA,QAAA,IAAA;;AACA,wBAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,SADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;;AAIA,sBAAA,MAAA,CAAA,OAAA;AACA,qBANA,MAMA;AACA,sBAAA,MAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,OADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;AAIA;AACA,mBAbA;AAcA,iBAnBA,EAmBA,KAnBA,CAmBA,YAAA;AACA,kBAAA,MAAA,CAAA,QAAA,CAAA;AACA,oBAAA,IAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;AAIA,iBAxBA;;AAFA;AAAA,uBA2BA,MAAA,CAAA,OAAA,EA3BA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA4BA,KAtIA;AAuIA;AACA,IAAA,KAxIA,mBAwIA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,KA1IA;AA2IA,IAAA,YA3IA,wBA2IA,IA3IA,EA2IA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KA7IA;AA+IA,IAAA,oBA/IA,kCA+IA;AACA,WAAA,QAAA,GAAA,KAAA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,KAlJA;AAoJA,IAAA,UApJA,wBAoJA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAEA,2BAAA,MAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,yBAEA,IAFA;AAGA,gBAAA,MAAA,CAAA,QAAA,GAAA,IAAA;AAHA;AAAA,uBAIA,2BAAA,MAAA,CAJA;;AAAA;AAAA;AAIA,gBAAA,IAJA,0BAIA,IAJA;AAKA,gBAAA,MAAA,CAAA,QAAA,GAAA,IAAA;;AAEA,gBAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,sBAAA,IAAA,CAAA,KAAA,KAAA,MAAA,EAAA;AACA,oBAAA,IAAA,CAAA,OAAA,GAAA,IAAA;AACA;;AACA,sBAAA,IAAA,CAAA,KAAA,KAAA,MAAA,EAAA;AACA,oBAAA,IAAA,CAAA,OAAA,GAAA,IAAA;AACA;AACA,iBAPA,EAPA,CAeA;;;AACA,gBAAA,MAAA,CAAA,OAAA;;AAhBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA,KArKA;AAuKA,IAAA,eAvKA,2BAuKA,GAvKA,EAuKA;AACA,UAAA,GAAA,CAAA,MAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,aAAA,cAAA,GAAA,IAAA;AACA,aAAA,QAAA,GAAA,IAAA;AACA;AACA,KA5KA;AA8KA,IAAA,iBA9KA,6BA8KA,GA9KA,EA8KA;AAAA;;AACA,UAAA,KAAA,QAAA,EAAA;AACA,aAAA,UAAA,CAAA,IAAA,CAAA,OAAA,GAAA,EAAA;AACA,aAAA,UAAA,CAAA,IAAA,CAAA,IAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,OAAA,EAAA;AACA,YAAA,MAAA,CAAA,UAAA,CAAA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA;;AACA,YAAA,MAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA,IAAA,IAAA,CAAA,IAAA,GAAA,GAAA;AACA;AACA,SALA;AAOA,aAAA,UAAA,CAAA,IAAA,CAAA,IAAA,GAAA,KAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA,KAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA,aAAA,cAAA,GAAA,KAAA;AACA,OAZA,MAYA;AACA,YAAA,SAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,OAAA,EAAA;AACA,YAAA,SAAA,CAAA,IAAA,CAAA,IAAA;AACA;AACA,SAJA;;AAKA,YAAA,SAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,eAAA,IAAA,CAAA,MAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA,eAAA,IAAA,CAAA,IAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA,IAAA;AACA,eAAA,cAAA,GAAA,KAAA;AACA,SAJA,MAIA;AACA,eAAA,QAAA,CAAA,OAAA,CAAA,WAAA;AACA;AACA;AACA,KA1MA;AA2MA,IAAA,qBA3MA,mCA2MA;AACA,WAAA,cAAA,GAAA,KAAA;AACA,KA7MA;AA8MA;AACA,IAAA,WA/MA,yBA+MA;AAAA;;AACA,WAAA,IAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,YAAA;AACA,QAAA,MAAA,CAAA,IAAA,GAAA,MAAA,CAAA,QAAA,CAAA,IAAA,GAAA,IAAA;;AACA,QAAA,MAAA,CAAA,SAAA,CAAA,MAAA;AACA,OAHA;AAIA;AArNA;AAjIA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      @handleReset=\"getReset\"\n      @onfocusEvent=\"inputFocusEvent\"\n    />\n    <div>\n      <el-white class=\"button-group\">\n        <div class=\"button_btn\">\n          <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"getInsert\">新增</el-button>\n        </div>\n        <comp-table :table-and-page-info=\"tableAndPageInfo\" @update:multipleSelection=\"selectChange\">\n          <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\" width=\"160\"\n                           :resizable=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button @click=\"updateRow(scope.row)\" v-show=\"scope.row.createBy == currentUser\"  type=\"text\" size=\"small\" title=\"修改\"  class='el-icon-edit'>\n              </el-button>\n              <el-button @click=\"getInfo(scope.row)\" type=\"text\" size=\"small\" title=\"详情\" class=\"el-icon-view\"></el-button>\n              <el-button type=\"text\" size=\"small\" v-show=\"scope.row.createBy == currentUser\" @click=\"deleteRow(scope.row)\" title=\"删除\" class=\"el-icon-delete\"></el-button>\n            </template>\n          </el-table-column>\n        </comp-table>\n      </el-white>\n      <!-- 详情/新增/修改 -->\n      <el-dialog :title=title :visible.sync=\"isShowDetails\" width=\"40%\" @close=\"handleClose\" v-dialogDrag>\n        <el-form\n          label-width=\"120px\"\n          ref=\"form\"\n          :model=\"form\"\n          :rules=\"rules\"\n        >\n          <el-row :gutter=\"8\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item v-show=\"false\" label=\"设备分类:\" prop=\"sbfl\">\n                <el-input v-model=\"form.sbfl\"></el-input>\n                <el-input v-model=\"form.objId\"></el-input>\n              </el-form-item>\n              <el-form-item label=\"设备分类：\" prop=\"sbflmc\">\n                <el-input placeholder=\"请选择设备分类\" @focus=\"showDeviceTreeDialog\" v-model=\"form.sbflmc\"\n                          :disabled=\"isDisabled\" clearable\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"检修分类：\" prop=\"jxfl\">\n                <el-select placeholder=\"请选择检修分类\" clearable v-model=\"form.jxfl\" :disabled=\"isDisabled\"\n                           style=\"width: 100%\"\n                >\n                  <el-option\n                    v-for=\"item in jxflList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"周期单位：\" prop=\"zqdw\">\n                <el-select placeholder=\"请选择周期单位\" clearable v-model=\"form.zqdw\" :disabled=\"isDisabled\"\n                           style=\"width: 100%\"\n                >\n                  <el-option\n                    v-for=\"item in zqdwList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"检修周期：\" clearable prop=\"jxzq\">\n                <el-input-number placeholder=\"请输入检修周期\" v-model=\"form.jxzq\" :disabled=\"isDisabled\"/>\n              </el-form-item>\n            </el-col>\n\n          </el-row>\n        </el-form>\n        <div slot=\"footer\">\n          <el-button @click=\"close\">取 消</el-button>\n          <el-button v-if=\"title==='检修周期新增'||title==='检修周期修改'\" type=\"primary\" @click=\"saveRow\">确 认</el-button>\n        </div>\n      </el-dialog>\n\n      <el-dialog\n        :append-to-body=\"true\"\n        title=\"设备分类\"\n        v-dialogDrag\n        :visible.sync=\"showDeviceTree\"\n        width=\"400px\"\n        v-if=\"showDeviceTree\"\n      >\n        <device-tree\n          @getDeviceTypeData=\"getDeviceTypeData\"\n          @closeDeviceTypeDialog=\"closeDeviceTypeDialog\"\n        >\n        </device-tree>\n      </el-dialog>\n\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getList, remove, saveOrUpdate } from '@/api/dagangOilfield/bzgl/jxbzk/jxzqwh'\nimport DeviceTree from '@/views/dagangOilfield/bzgl/sbbzk/deviceTree'\nimport { getDictTypeData } from '@/api/system/dict/data'\n\nexport default {\n  name: 'jxzqwh',\n  components: { DeviceTree },\n  data() {\n    return {\n      currentUser: this.$store.getters.name,\n      //form表单\n      form: {},\n      //是否显示弹框\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //表单数据\n      dataTable: [],\n      zqdwList: [],\n      jxflList: [],\n      //标题\n      title: '',\n      filterInfo: {\n        data: {\n          sbflArr: [],\n          sbfl: '',\n          jxflArr: [],\n          jxzq: '',\n          zqdw: ''\n        },\n        fieldList: [\n          {\n            label: '设备分类',\n            value: 'sbfl',\n            type: 'input',\n            clearable: true\n          },\n\n\n          {\n            label: '检修周期',\n            value: 'jxzq',\n            type: 'input'\n          },\n          /*{\n            label: '检修分类',\n            value: 'jxflArr',\n            type: 'select',\n            // type: 'checkbox',\n            // checkboxValue: [],\n            multiple: true,\n            options: [],\n            clearable: true\n          },*/\n          {\n            label: '检修分类',\n            value: 'jxfl',\n            type: 'checkbox',\n            checkboxValue: [],\n            multiple: true,\n            options: [],\n            clearable: true\n          },\n          {\n            label: '周期单位',\n            value: 'zqdw',\n            // type: 'select',\n              type: 'checkbox',\n            checkboxValue: [],\n            options: [],\n            clearable: true\n          },\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 120]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: '设备分类', prop: 'sbflmc', minWidth: '100' },\n          { label: '检修分类', prop: 'jxflName', minWidth: '150' },\n          { label: '周期单位', prop: 'zqdwName', minWidth: '150' },\n          { label: '检修周期', prop: 'jxzq', minWidth: '80' }\n          /*{\n            prop: 'operation',\n            label: '操作',\n            minWidth: '130px',\n            style: { display: 'block' },\n            //操作列固定再右侧\n            fixed: 'right',\n            operation: [\n              { name: '修改', clickFun: this.updateRow },\n              { name: '详情', clickFun: this.getInfo }\n            ]\n          }*/\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        sbflArr: [],\n        jxflArr: [],\n        jxzq: '',\n        zqdw: ''\n      },\n      selectRows: [],\n      showDeviceTree: false,\n      isFilter: false,\n      rules: {\n        sbflmc: [\n          { required: true, message: '设备分类不能为空', trigger: 'change' }\n        ],\n        sbfl: [\n          { required: true, message: '设备分类不能为空', trigger: 'change' }\n        ],\n        jxfl: [\n          { required: true, message: '检修分类不能为空', trigger: 'change' }\n        ],\n        zqdw: [\n          { required: true, message: '周期单位不能为空', trigger: 'change' }\n        ],\n        jxzq: [\n          { required: true, message: '检修周期不能为空', trigger: 'blur' }\n        ]\n      }\n    }\n  },\n  mounted() {\n    this.initDomain()\n  },\n  methods: {\n    //列表查询\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params }\n        const param = this.params\n        param.sbflArr = param.sbfl === '' ? [] : param.sbflArr\n        const { data, code } = await getList(param)\n        if (code === '0000') {\n          this.tableAndPageInfo.tableData = data.records\n          this.tableAndPageInfo.pager.total = data.total\n\n          this.tableAndPageInfo.tableData.forEach(item => {\n            this.jxflList.forEach(element => {\n              if (item.jxfl === element.value) {\n                item.jxflName = element.label\n                return\n              }\n            })\n            this.zqdwList.forEach(element => {\n              if (item.zqdw === element.value) {\n                item.zqdwName = element.label\n                return\n              }\n            })\n          })\n        }\n      } catch (e) {\n        console.log(e)\n      }\n    },\n    //重置按钮\n    getReset() {\n   this.filterInfo.fieldList.forEach(item=>{\n        if(item.type === 'checkbox'){\n          item.checkboxValue = [];\n        }\n      })\n      this.params =  {\n        sbflArr: [],\n        jxflArr: [],\n        jxzq: '',\n        zqdw: ''\n      }\n    },\n    //选中行\n    handleSelectionChange() {\n\n    },\n    //详情\n    getDetails() {\n      this.title = '检修周期详情'\n      this.isDisabled = true\n      this.isShowDetails = true\n      this.form = { ...row }\n    },\n    //新增\n    getInsert() {\n      this.title = '检修周期新增'\n      this.isDisabled = false\n      this.isShowDetails = true\n      this.form = {\n        sbfl: '',\n        sbflmc: '',\n        jxfl: '',\n        zqdw: '',\n        jxzq: '',\n        objId: ''\n      }\n    },\n    //编辑按钮\n    updateRow(row) {\n      this.title = '检修周期修改'\n      this.isDisabled = false\n      this.form = { ...row }\n      this.isShowDetails = true\n    },\n    //详情按钮\n    getInfo(row) {\n      this.title = '检修周期详情'\n      this.form = { ...row }\n      this.isDisabled = true\n      this.isShowDetails = true\n    },\n    async saveRow() {\n\n      this.$refs['form'].validate(valid => {\n        if (valid) {\n          saveOrUpdate(this.form).then(res => {\n\n            try {\n              if (res.code === '0000') {\n                this.$message.success('操作成功')\n              }\n            } catch (e) {\n              console.log(e)\n            }\n            this.getData()\n          })\n        } else {\n          return false\n        }\n        this.isShowDetails = false\n      })\n    },\n    //删除按钮\n    async deleteRow(row) {\n      this.form = { ...row }\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        remove([this.form.objId]).then(({ code }) => {\n          if (code === '0000') {\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            })\n            this.getData()\n          } else {\n            this.$message({\n              type: 'error',\n              message: '删除失败!'\n            })\n          }\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        })\n      })\n      await this.getData()\n    },\n    //关闭弹窗\n    close() {\n      this.isShowDetails = false\n    },\n    selectChange(rows) {\n      this.selectRows = rows\n    },\n\n    showDeviceTreeDialog() {\n      this.isFilter = false\n      this.showDeviceTree = true\n    },\n\n    async initDomain() {\n\n      let { data: jxfl } = await getDictTypeData('jxfl')\n      this.jxflList = jxfl\n      let { data: zqdw } = await getDictTypeData('zqdw')\n      this.zqdwList = zqdw\n\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.value === 'jxfl') {\n          item.options = jxfl\n        }\n        if (item.value === 'zqdw') {\n          item.options = zqdw\n        }\n      })\n      //列表查询\n      this.getData()\n    },\n\n    inputFocusEvent(val) {\n      if (val.target.name === 'sbfl') {\n        this.showDeviceTree = true\n        this.isFilter = true\n      }\n    },\n\n    getDeviceTypeData(res) {\n      if (this.isFilter) {\n        this.filterInfo.data.sbflArr = []\n        this.filterInfo.data.sbfl = ''\n        res.forEach(item => {\n          if (item.checked) {\n            this.filterInfo.data.sbflArr.push(item.code)\n            this.filterInfo.data.sbfl += item.name + ','\n          }\n        })\n\n        this.filterInfo.data.sbfl = this.filterInfo.data.sbfl.substring(0, this.filterInfo.data.sbfl.length - 1)\n        this.showDeviceTree = false\n      } else {\n        let treeNodes = []\n        res.forEach(item => {\n          if (item.checked) {\n            treeNodes.push(item)\n          }\n        })\n        if (treeNodes.length === 1) {\n          this.form.sbflmc = treeNodes[0].name\n          this.form.sbfl = treeNodes[0].code\n          this.showDeviceTree = false\n        } else {\n          this.$message.warning('请选择单条设备数据')\n        }\n      }\n    },\n    closeDeviceTypeDialog() {\n      this.showDeviceTree = false\n    },\n    //清空表单数据\n    handleClose() {\n      this.form = {}\n      this.$nextTick(() => {\n        this.form = this.$options.data().form\n        this.resetForm('form')\n      })\n    }\n\n  }\n}\n</script>\n\n<style scoped>\n\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/jxbzk"}]}