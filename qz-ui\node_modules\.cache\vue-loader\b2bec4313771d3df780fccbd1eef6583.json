{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\glsymp.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\glsymp.vue", "mtime": 1706897323433}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["glsymp.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+FA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "glsymp.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk", "sourcesContent": ["<template>\n  <div>\n    <el-row :gutter=\"3\">\n      <div class=\"mb8 pull-right\">\n        <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"mini\" @click=\"addMp\"\n          >关联铭牌</el-button\n        >\n        <el-button\n          type=\"danger\"\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          @click=\"deleteMp\"\n          >取消关联</el-button\n        >\n      </div>\n    </el-row>\n    <el-row :gutter=\"20\">\n     \n        <el-table\n          ref=\"mpTable\"\n          :data=\"mpTableData\"\n          @selection-change=\"handleSelectedChange\"\n          @row-click=\"handleRowClick\"\n        >\n           <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n            <el-table-column\n            label=\"序号\"\n            type=\"index\"\n            width=\"50\"\n            align=\"center\"\n          ></el-table-column>\n            <el-table-column\n              label=\"铭牌名称\"\n              prop=\"mpmc\"\n              align=\"center\"\n            ></el-table-column>\n        </el-table>\n     \n        <pagination\n          v-show=\"mpInfoParams.total > 0\"\n          :total=\"mpInfoParams.total\"\n          :page.sync=\"mpInfoParams.pageNum\"\n          :limit.sync=\"mpInfoParams.pageSize\"\n          @pagination=\"getMainData\"\n        />\n    </el-row>\n\n    <el-dialog\n      title=\"关联铭牌\"\n      v-dialogDrag\n      width=\"40%\"\n      :visible.sync=\"showAddForm\"\n      v-if=\"showAddForm\"\n      append-to-body\n    >\n      <el-table\n        ref=\"addTable\"\n        :data=\"addTable\"\n        @current-change=\"handleCurrentChangeAddForm\"\n        @selection-change=\"handleSelectedChangeAddForm\"\n      >\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n        <el-table-column\n          label=\"序号\"\n          type=\"index\"\n          width=\"50\"\n          align=\"center\"\n        ></el-table-column>\n        <el-table-column\n          label=\"铭牌名称\"\n          align=\"center\"\n          prop=\"mpmc\"\n        ></el-table-column>\n        <el-table-column\n          label=\"专业\"\n          align=\"center\"\n          prop=\"zyName\"\n        ></el-table-column>\n      </el-table>\n      <pagination\n        v-show=\"mpDialogParams.total > 0\"\n        :total=\"mpDialogParams.total\"\n        :page.sync=\"mpDialogParams.pageNum\"\n        :limit.sync=\"mpDialogParams.pageSize\"\n        @pagination=\"getMpDialogData\"\n      />\n      <div slot=\"footer\">\n        <el-button @click=\"closeAddMp\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"commitAddMp\">确 认</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  deleteMwtUdSyMbmp,\n  getMpmcDataById,\n  saveMwtUdSyMbmp,\n} from \"@/api/dagangOilfield/bzgl/symbwh\";\nimport { getNameplateContent } from \"@/api/dagangOilfield/bzgl/sympk/sympInfo\";\nimport { getPageNoDataList } from \"@/api/dagangOilfield/bzgl/sympk/sympk\";\n\nexport default {\n  name: \"glsymp\",\n  props: {\n    //模板数据\n    mainData: {\n      type: Object,\n    },\n    //设备树数据\n    treeData: {\n      type: Object,\n    },\n  },\n  data() {\n    return {\n      //铭牌表单数据\n      mpTableData: [],\n      //铭牌内容表单数据\n      mpInfoTableData: [],\n      //名牌查询条件\n      mpParams: {\n        mbId: \"\",\n        isMpSyxm:0,\n      },\n        //删除选择列\n      selectRows: [],\n      //铭牌内容查询条件\n      mpInfoParams: {\n        pageNum: 1,\n        pageSize: 10,\n        total: 0,\n        mpid: \"\",\n        zy: \"\",\n        sblxbm: \"\",\n        isMpSyxm: 0,\n        symbid:\"\",\n      },\n      //显示关联铭牌弹窗\n      showAddForm: false,\n      //关联铭牌弹窗铭牌表单数据\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"\", label: \"供电单位\", minWidth: \"120\" },\n          { prop: \"\", label: \"电压等级\", minWidth: \"140\" },\n          { prop: \"\", label: \"线路名称\", minWidth: \"180\" },\n          { prop: \"\", label: \"输电班组\", minWidth: \"120\" },\n          { prop: \"\", label: \"投运时间\", minWidth: \"120\" },\n          { prop: \"\", label: \"上次检修时间\", minWidth: \"120\" },\n        ],\n      },\n      //新增弹窗表单数据\n      addTable: [],\n      //铭牌弹窗查询条件\n      mpDialogParams: {\n        pageNum: 1,\n        pageSize: 10,\n        total: 0,\n        sblxbm: \"\",\n        isMpSyxm: 0,\n      },\n      //新增弹窗列表选中数据\n      addFormSelectedRow: [],\n      //专业下拉框数据\n      options: [\n        { label: \"输电\", value: \"SD\" },\n        { label: \"变电\", value: \"BD\" },\n      ],\n      //关联表单数据\n      addFormData: {\n        id: \"\",\n        symbid: \"\",\n        mpid: \"\",\n        ismpsyxm: 0,\n      },\n      mplist: [],\n    };\n  },\n  mounted() {\n    this.getMainData();\n  },\n  methods: {\n    //主表复选框选中\n    handleSelectedChange(row) {\n       console.log(\"sdfsd-\",row);\n       this.selectRows = row;\n    },\n    //行点击事件\n    handleRowClick(rows) {\n     \n    },\n    //新增弹窗表格选中行数据\n    handleSelectedChangeAddForm(row) {\n      this.addFormSelectedRow = row;\n    },\n    //新增弹窗行点击切换事件\n    handleCurrentChangeAddForm(row) {},\n\n    //关联铭牌\n    addMp() {\n        this.showAddForm = true;\n        this.getMpDialogData();\n    },\n    //取消关联铭牌\n    deleteMp() {\n     if (this.selectRows.length < 1) {\n        this.$message.warning(\"请选择正确的数据！！！\");\n        return;\n      }\n      let ids = this.selectRows.map((item) => {\n        return item.mbmpId;\n      });\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      }).then(() => {\n            ids.push(this.mpTableData[0].id);\n            deleteMwtUdSyMbmp(ids).then((res) => {\n              if (res.code === \"0000\") {\n                this.$message.success(\"取消关联成功\");\n                this.getMainData();\n              } else {\n                this.$message.error(\"取消关联失败!请稍后重试!\");\n              }\n            });\n          })\n          .catch(() => {\n            this.$message({\n              type: \"info\",\n              message: \"已取消操作\",\n            });\n          });\n      \n    },\n    getMainData() {\n      //初始置空铭牌表与铭牌信息表数据\n      this.mpTableData = [];\n      this.mpInfoTableData = [];\n      debugger;\n      //设置模板id\n      this.mpInfoParams.symbid = this.mainData.objId;\n      console.log(\"kaishi\",this.mainData);\n      getMpmcDataById(this.mpInfoParams).then((res) => {\n        if (res.code === \"0000\") {\n          this.mpTableData = res.data.records;\n          this.mpInfoParams.total=res.data.total\n          // if (res.data.length > 0) {\n          //   this.mpInfoParams.mpid = res.data[0].mpid;\n          //   this.mpInfoParams.zy = res.data[0].zy;\n          //   this.mpInfoParams.sblxbm = this.mainData.sblxid;\n          //   this.getMpInfoData();\n          // }\n        }\n      });\n    },\n    //获取铭牌内容数据\n    getMpInfoData() {\n      getNameplateContent(this.mpInfoParams).then((res) => {\n        if (res.code === \"0000\") {\n          this.mpInfoTableData = res.data.records;\n          this.mpInfoParams.total = res.data.total;\n        }\n      });\n    },\n    //获取新增铭牌弹窗表单数据\n    getMpDialogData() {\n      this.mpDialogParams.sblxbm = this.mainData.sblxid;\n      getPageNoDataList(this.mpDialogParams).then((res) => {\n        debugger;\n        if (res.code === \"0000\") {\n          this.addTable = res.data.records;\n          this.mpDialogParams.total = res.data.total;\n\n          this.addTable.forEach((item) => {\n            this.options.forEach((element) => {\n              if (item.zy === element.value) {\n                item.zyName = element.label;\n              }\n            });\n          });\n        }\n      });\n    },\n    closeAddMp() {\n      this.showAddForm = false;\n    },\n    //保存铭牌\n    commitAddMp() {\n      this.mplist=[];\n      let number = this.addFormSelectedRow.length;\n      console.log(\"12-\", this.addFormSelectedRow);\n      debugger;\n      for (let i = 0; i < number; i++) {\n        this.addFormData={};\n        this.addFormData.mpid = this.addFormSelectedRow[i].objId;\n        this.addFormData.symbid = this.mainData.objId;\n        this.addFormData.isMpSyxm=0;\n        this.mplist.push(this.addFormData);\n      }\n      console.log(\"--mpidsr-\", this.mplist);\n      saveMwtUdSyMbmp(this.mplist).then((res) => {\n        if (res.code === \"0000\") {\n          this.showAddForm = false;\n          this.getMainData();\n          this.$message.success(\"关联铭牌成功\");\n        }\n      });\n    },\n  },\n}\n</script>\n\n<style scoped>\n</style>\n"]}]}