{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\gqjgl\\pdgqj.vue?vue&type=style&index=0&id=27af0b12&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\gqjgl\\pdgqj.vue", "mtime": 1706897324701}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmJ1dHRvbi1ncm91cCB7CiAgcGFkZGluZy1sZWZ0OiAzMHB4OwogIHBhZGRpbmctcmlnaHQ6IDMwcHg7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtZW5kOwp9CgoucXhscl9kaWFsb2dfaW5zZXJ0IHsKICBtYXJnaW4tdG9wOiA2dmggIWltcG9ydGFudAp9CgovKi9kZWVwLyAucXhscl9kaWFsb2dfaW5zZXJ0IC5lbC1pbnB1dC0tbWVkaXVtIC5lbC1pbnB1dF9faW5uZXJ7Ki8KLyogIHdpZHRoOiAxMDAlOyovCi8qfSovCi5lbC1zZWxlY3QgewogIHdpZHRoOiAxMDAlOwp9CgouZWwtZGF0ZS1lZGl0b3IgewogIHdpZHRoOiAxMDAlOwp9Cg=="}, {"version": 3, "sources": ["pdgqj.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA04BA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "pdgqj.vue", "sourceRoot": "src/views/dagangOilfield/dwzygl/gqjgl", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      ref=\"filter1\"\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      @handleReset=\"filterReset\"\n    />\n    <el-white class=\"button-group1\">\n      <div class=\"button_btn\">\n        <el-button @click=\"addSensorButton\" icon=\"el-icon-plus\" v-hasPermi=\"['pdgql:button:add']\"  type=\"primary\">新增\n        </el-button>\n        <el-button type=\"primary\" icon=\"el-icon-download\" @click=\"exportExcel()\">导出</el-button>\n      </div>\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"selectChange\"\n        height=\"67vh\"\n        >\n      <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\" width=\"180\" :resizable=\"false\">\n        <template slot-scope=\"scope\">\n           <el-button  type=\"text\" size=\"small\" @click=\"getGqjInfo(scope.row)\" class=\"el-icon-view\" title=\"详情\"></el-button>\n           <el-button  type=\"text\" size=\"small\" v-if=\"scope.row.createBy==currUser\"  @click=\"updateGqjInfo(scope.row)\" class='el-icon-edit' title=\"编辑\"></el-button>\n           <el-button  type=\"text\" size=\"small\" v-if=\"scope.row.createBy==currUser\"  @click=\"deleteRow(scope.row.objId)\" class=\"el-icon-delete\" title=\"删除\"></el-button>\n        </template>\n      </el-table-column>\n      </comp-table>\n    </el-white>\n    <!-- 工器具台账详情修改新增 -->\n    <el-dialog :title=\"gqjTital\" :visible.sync=\"dialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"60%\" v-dialogDrag>\n      <el-form :model=\"form\" label-width=\"80px\" :disabled=\"isDisabled\" :rules=\"rules\" ref=\"form\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"名称\" prop=\"sbmc\">\n              <el-input v-model=\"form.sbmc\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"数量\" prop=\"sl\">\n              <el-input-number :min=\"1\" v-model=\"form.sl\" :disabled=\"isDisabled\"></el-input-number>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"管理单位\" prop=\"ssgs\">\n              <el-input v-model=\"form.ssgs\" placeholder=\"\" :disabled=\"isDisabled\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"负责人\" prop=\"fzr\">\n              <el-input v-model=\"form.fzr\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"存放地点\">\n              <el-input v-model=\"form.cfdd\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"使用情况\">\n              <el-input v-model=\"form.sytj\" placeholder=\"\" :disabled=\"isDisabled\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"检验时间\">\n              <el-date-picker\n                  v-model=\"form.jysj\"\n                  type=\"date\"\n                  value-format=\"yyyy-MM-dd\"\n                  placeholder=\"选择日期\"\n                  :disabled=\"isDisabled\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"出厂日期\" class=\"add_sy_tyrq\">\n              <el-date-picker\n                  v-model=\"form.ccrq\"\n                  type=\"date\"\n                  value-format=\"yyyy-MM-dd\"\n                  placeholder=\"选择日期\"\n                  :disabled=\"isDisabled\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"购入日期\" class=\"add_sy_tyrq\">\n              <el-date-picker\n                  v-model=\"form.grrq\"\n                  type=\"date\"\n                  value-format=\"yyyy-MM-dd\"\n                  placeholder=\"选择日期\"\n                  :disabled=\"isDisabled\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"有效期\">\n              <el-input v-model=\"form.yxq\" placeholder=\"\" :disabled=\"isDisabled\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"有效期限\" class=\"add_sy_tyrq\">\n              <el-date-picker\n                  v-model=\"form.yxqx\"\n                  type=\"date\"\n                  value-format=\"yyyy-MM-dd\"\n                  placeholder=\"选择日期\"\n                  :disabled=\"isDisabled\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"校验单位\">\n              <el-input v-model=\"form.jydw\" placeholder=\"\" :disabled=\"isDisabled\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"校验结果\">\n              <el-input v-model=\"form.jyjg\" placeholder=\"\" :disabled=\"isDisabled\">\n              </el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"截至日期\" class=\"add_sy_tyrq\">\n              <el-date-picker\n                  v-model=\"form.jzrq\"\n                  type=\"date\"\n                  value-format=\"yyyy-MM-dd\"\n                  placeholder=\"选择日期\"\n                  :disabled=\"isDisabled\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\">\n              <el-input type=\"textarea\" v-model=\"form.bz\" :disabled=\"isDisabled\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"qxcommit\" v-if=\"gqjTital === '工器具新增' || gqjTital === '工器具修改' \" class=\"pmyBtn\">\n          确 定\n        </el-button>\n      </div>\n    </el-dialog>\n    <!--试验报告弹出框-->\n    <el-dialog title=\"试验报告记录\" :visible.sync=\"sybgDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"60%\">\n      <el-form :model=\"form\" label-width=\"80px\">\n        <el-form-item style=\"float: right\">\n          <el-button class=\"mb8\" @click=\"addSyButton\" type=\"primary\" icon=\"el-icon-plus\"\n                     v-hasPermi=\"['gqjgl:button:sydialog:insert']\">添加\n          </el-button>\n          <el-button class=\"mb8\" @click=\"deleteYxSy\" type=\"danger\" icon=\"el-icon-delete\" v-hasPermi=\"['gqjgl:button:sydialog:delete']\">删除\n          </el-button>\n        </el-form-item>\n      </el-form>\n      <el-table\n        ref=\"syTable\"\n        stripe\n        border\n        v-loading=\"syLoading\"\n        :data=\"gqjsyList\"\n        @row-click=\"syRowClick\"\n        @selection-change=\"syCurrentChange\"\n        max-height=\"550\">\n        <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n        <el-table-column label=\"试验单位\" align=\"center\" prop=\"sydwName\"></el-table-column>\n        <el-table-column label=\"试验人员\" align=\"center\" prop=\"syryName\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"试验结论\" align=\"center\" prop=\"syjlName\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"试验时间\" align=\"center\" prop=\"sysj\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column\n          align=\"center\"\n          label=\"操作\"\n          width=\"150\">\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" size=\"small\">下载附件</el-button>\n            <el-button type=\"text\" @click=\"updateSy(scope.row)\" size=\"small\">修改</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      <pagination\n        v-show=\"syQueryForm.total>0\"\n        :total=\"syQueryForm.total\"\n        :page.sync=\"syQueryForm.pageNum\"\n        :limit.sync=\"syQueryForm.pageSize\"\n        @pagination=\"getYxSyData\"\n      />\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"sybgDialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"sybgDialogFormVisible = false\" class=\"pmyBtn\">确 定</el-button>\n      </div>\n    </el-dialog>\n    <!--添加试验报告-->\n    <el-dialog title=\"添加试验报告\" :visible.sync=\"addSybgDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"50%\">\n      <el-form label-width=\"80px\" v-model=\"syFrom\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item hidden>\n              <el-input v-model=\"syFrom.id\"></el-input>\n              <el-input v-model=\"syFrom.gqjId\"></el-input>\n              <el-input v-model=\"syFrom.sydwId\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"试验单位\">\n<!--              <el-select v-model=\"syFrom.sydwName\" placeholder=\"\" :disabled=\"isSyDetail\"></el-select>-->\n              <el-input v-model=\"syFrom.sydwName\" placeholder=\"\" :disabled=\"isSyDetail\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item hidden label=\"试验人员id\">\n              <el-input v-model=\"syFrom.syryId\" hidden></el-input>\n            </el-form-item>\n            <el-form-item label=\"试验人员\">\n              <!--<el-select v-model=\"syFrom.syryName\" placeholder=\"\">\n              </el-select>-->\n              <el-input v-model=\"syFrom.syryName\" :disabled=\"isSyDetail\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"试验时间\" class=\"add_sy_tyrq\">\n              <el-date-picker\n                :disabled=\"isSyDetail\"\n                v-model=\"syFrom.sysj\"\n                type=\"date\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"选择日期时间\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item hidden>\n              <el-input type=\"textarea\" v-model=\"syFrom.syjlCode\" hidden :disabled=\"isSyDetail\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"试验结论\">\n              <el-input type=\"textarea\" v-model=\"syFrom.syjlName\" :disabled=\"isSyDetail\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"20\">\n            <el-form-item label=\"备注\">\n              <el-input type=\"textarea\" v-model=\"syFrom.remark\" :disabled=\"isSyDetail\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n<!--        <el-row :gutter=\"20\">-->\n<!--          <el-upload-->\n<!--            style=\"float: right\"-->\n<!--            class=\"upload-demo\"-->\n<!--            action=\"https://jsonplaceholder.typicode.com/posts/\"-->\n<!--            :on-preview=\"handlePreview\"-->\n<!--            :on-remove=\"handleRemove\"-->\n<!--            :before-remove=\"beforeRemove\"-->\n<!--            multiple-->\n<!--            :limit=\"3\"-->\n<!--            :on-exceed=\"handleExceed\"-->\n<!--            :file-list=\"fileList\">-->\n<!--            <el-button size=\"small\" type=\"primary\">点击上传</el-button>-->\n<!--            <div slot=\"tip\" class=\"el-upload__tip\">只能上传jpg/png文件，且不超过500kb</div>-->\n<!--          </el-upload>-->\n<!--        </el-row>-->\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"addSybgDialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"saveOrUpdateSy\" class=\"pmyBtn\">确 定</el-button>\n      </div>\n    </el-dialog>\n    <!--检修记录-->\n    <!--检修记录弹出框-->\n    <el-dialog title=\"检修维护记录\" :visible.sync=\"jwxDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"60%\">\n      <el-form :model=\"form\" label-width=\"80px\">\n        <el-form-item style=\"float: right\">\n          <el-button class=\"mb8\" @click=\"addJxButton\" type=\"primary\" icon=\"el-icon-plus\"\n                     v-hasPermi=\"['gqjgl:button:sydialog:insert']\">\n            添加\n          </el-button>\n          <el-button class=\"mb8\" @click=\"deleteJxData\" type=\"danger\" icon=\"el-icon-delete\" v-hasPermi=\"['gqjgl:button:sydialog:delete']\">删除\n          </el-button>\n        </el-form-item>\n      </el-form>\n      <el-table\n        ref=\"jxTable\"\n        stripe\n        border\n        v-loading=\"jxLoading\"\n        :data=\"gqjJxList\"\n        @row-click=\"jxRowClick\"\n        @selection-change=\"jxCurrentChange\"\n        max-height=\"550\">\n        <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n        <el-table-column label=\"检修单位\" align=\"center\" prop=\"jxdwName\"></el-table-column>\n        <el-table-column label=\"检修人员\" align=\"center\" prop=\"jxryName\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"检修结果\" align=\"center\" prop=\"jxjg\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"检修时间\" align=\"center\" prop=\"jxsj\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column\n          align=\"center\"\n          label=\"操作\"\n          width=\"150\">\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" size=\"small\">下载附件</el-button>\n            <el-button type=\"text\" @click=\"updateJx(scope.row)\" size=\"small\">修改</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      <pagination\n        v-show=\"jxQueryForm.total>0\"\n        :total=\"jxQueryForm.total\"\n        :page.sync=\"jxQueryForm.pageNum\"\n        :limit.sync=\"jxQueryForm.pageSize\"\n        @pagination=\"getJxRecords\"\n      />\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"jwxDialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"jwxDialogFormVisible = false\" class=\"pmyBtn\">确 定</el-button>\n      </div>\n    </el-dialog>\n    <!--添加检修记录-->\n    <el-dialog title=\"添加检修维护记录\" :visible.sync=\"addJwxSybgDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"50%\">\n      <el-form label-width=\"80px\" :model=\"jxForm\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item hidden>\n              <el-input v-model=\"jxForm.id\"></el-input>\n              <el-input v-model=\"jxForm.gqjId\"></el-input>\n              <el-input v-model=\"jxForm.jxdwId\"></el-input>\n              <el-input v-model=\"jxForm.jxryId\"></el-input>\n            </el-form-item>\n            <el-form-item label=\"检修单位\">\n<!--              <el-select v-model=\"jxForm.jxdwName\" placeholder=\"\"></el-select>-->\n              <el-input v-model=\"jxForm.jxdwName\" placeholder=\"\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"检修人员\">\n<!--              <el-select v-model=\"jxForm.jxryName\" placeholder=\"\"></el-select>-->\n              <el-input v-model=\"jxForm.jxryName\" placeholder=\"\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"检修时间\" class=\"add_sy_tyrq\">\n              <el-date-picker\n                v-model=\"jxForm.jxsj\"\n                type=\"date\"\n                value-format=\"yyyy-MM-dd\"\n                placeholder=\"选择日期时间\">\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"检修结果\">\n              <el-input type=\"textarea\" v-model=\"jxForm.jxjg\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注\">\n              <el-input type=\"textarea\" v-model=\"jxForm.remark\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"addJwxSybgDialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"saveOrUpdateJx\" class=\"pmyBtn\">确 定</el-button>\n      </div>\n    </el-dialog>\n    <!-- 弹出框结束 -->\n  </div>\n\n</template>\n\n<script>\nimport { getUUID } from '@/utils/ruoyi'\nimport {\n  deleteAssetGqjJxRecords,\n  deleteYxSyRecords, exportExcel,\n  getAssetGqjJxRecords,\n  getList,\n  getYxSyRecords,\n  remove,\n  saveOrUpdate,\n  saveOrUpdateAssetGqjJxRecords,\n  saveOrUpdateYxSyRecords\n} from '@/api/dagangOilfield/asset/assetGqj'\nimport CompTable from 'com/CompTable'\nimport ElFilter from 'com/ElFilter'\n\nexport default {\n    components: {CompTable, ElFilter},\n    name: \"gqjgl\",\n    data() {\n      return {\n        currUser:this.$store.getters.name,\n        // 表单校验\n        rules: {\n          ssgs: [{ required: true, message: '请选择', trigger: 'blur' }],\n          sl: [{ required: true, message: '请选择', trigger: 'blur' }],\n          sbmc: [{ required: true, message: '请输入', trigger: 'blur' }],\n          fzr: [{ required: true, message: '请输入', trigger: 'blur' }],\n        },\n        params:{\n          type:\"pd\"\n        },\n        //工器具详情框字段控制\n        isDisabled: false,\n        //工器具弹出框表头\n        gqjTital: \"工器具新增\",\n\n        //表格内容\n        tableAndPageInfo: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n            {prop: 'sbmc', label: '名称', minWidth: '180'},\n            {prop: 'ssgs', label: '管理单位', minWidth: '120'},\n            {prop: 'sl', label: '数量', minWidth: '120'},\n            {prop: 'fzr', label: '负责人', minWidth: '120'},\n            {prop: 'cfdd', label: '存放地点', minWidth: '250'},\n            {prop: 'sytj', label: '使用情况', minWidth: '120'},\n            {prop: 'jysj', label: '校验时间', minWidth: '120'},\n            {prop: 'ccrq', label: '出厂日期', minWidth: '120'},\n            {prop: 'grrq', label: '购入日期', minWidth: '120'},\n            // {\n            //   fixed: \"right\",\n            //   prop: 'operation',\n            //   label: '操作',\n            //   minWidth: '150px',\n            //   style: {display: 'block'},\n            //   operation: [\n            //     // {name: '试验', clickFun: this.handleSearchSYClick},\n            //     // {name: '检修', clickFun: this.handleSerchJWXClick},\n            //     {name: '修改', clickFun: this.updateGqjInfo},\n            //     {name: '详情', clickFun: this.getGqjInfo},\n\n            //   ],\n\n            // },\n\n          ]\n        },\n        //筛选条件\n        filterInfo: {\n          data: {\n            fzr: '',\n            ssgs: '',\n            yxbz: '',\n            phone: '',\n          },\n          fieldList: [\n            {label: '名称', type: 'input', value: 'sbmc'},\n            {label: '负责人', type: 'input', value: 'fzr'},\n            {value: 'cfdd', label: '存放地点', type: 'input'},\n            // {label: '投运日期', type: 'date', value: 'tyrqArr',dateType: 'daterange',format: 'yyyy-MM-dd'},\n          ]\n        },\n        //检修记录弹出框\n        jwxDialogFormVisible: false,\n        //添加检修记录弹出框\n        addJwxSybgDialogFormVisible: false,\n        //工器具弹出框\n        dialogFormVisible: false,\n        //试验时间\n        sysj: '',\n        fildtps: [],\n        //试验弹出框\n        sybgDialogFormVisible: false,\n        //添加试验报告\n        addSybgDialogFormVisible: false,\n        //弹出框表单\n        form: {\n          type:\"pd\"\n        },\n        loading: false,\n        //工器具试验数据集合\n        gqjsyList: [],\n        //检修数据集合\n        gqjJxList:[],\n        //删除是否可用\n        multipleSensor: true,\n        showSearch: true,\n        //删除选择列\n        selectRows: [],\n        //工器具文件上传参数\n        gqjInfoUploadData: {\n          businessId: undefined,\n        },\n        //工器具文件上传请求头\n        gqjInfoUpHeader: {},\n\n        //试验查询条件\n        syQueryForm: {\n          gqjId: '',\n          total: 0,\n          pageSize: 10,\n          pageNum: 1\n        },\n\n        //试验新增表单数据\n        syFrom: {\n          id: '',\n          gqjId: '',\n          sydwId: '',\n          sydwName: '',\n          syryId: '',\n          syryName: '',\n          sysj: '',\n          syjlCode: '',\n          syjlName: '',\n          remark: ''\n        },\n\n        isSyDetail:false,\n\n        //检修查询条件\n        jxQueryForm: {\n          gqjId: '',\n          total: 0,\n          pageSize: 10,\n          pageNum: 1\n        },\n        //检修表单\n        jxForm: {\n          id: '',\n          jxdwId: '',\n          jxdwName: '',\n          jxryId: '',\n          jxryName: '',\n          jxjg: '',\n          jxsj: '',\n          remark: '',\n          gqjId: ''\n        },\n\n        //主表选中行数据\n        mainRowData: {},\n        //试验table加载\n        syLoading: false,\n        //试验选中行\n        sySelectRows: [],\n        //检修table加载\n        jxLoading: false,\n        //检修选中行\n        jxSelectRows: []\n      };\n    },\n    watch: {},\n    created() {\n\n    },\n    mounted() {\n      this.getData();\n    },\n    methods: {\n      exportExcel(){\n        exportExcel(this.params,'配电工器具')\n      },\n      /**\n       * 上传附附件之前的处理函数\n       * @param file\n       */\n      gqjInfoBeforeUpload(file) {\n        const fileSize = file.size < 1024 * 1024 * 50 //10M\n        if (!fileSize) {\n          this.$message.error('上传文件大小不能超过 50MB!')\n        }\n        let size = file.size / 1024\n      },\n      /**\n       * 上传附件成功调用的函数\n       * @param response\n       * @param file\n       * @param fileList\n       */\n      gqjInfoonSuccess(response, file, fileList) {\n        //文件id\n        this.form.attachmentid = response.data.businessId\n        //文件名称\n        this.form.attachmentname = response.data.sysFile.fileOldName\n      },\n\n      /**\n       * 移除文件\n       * @param file\n       * @param fileList\n       */\n      gqjInfohandleRemove(file, fileList) {\n\n      },\n      /**\n       * 工器具上传文件到服务器\n       */\n      gqjInfoSubmitUpload(){\n        debugger\n        this.gqjInfoUploadData.businessId = getUUID()\n        this.$refs.uploadGqjInfo.submit();\n      },\n\n      //工器具列表查询\n      async getData(params) {\n        try {\n          const param = {...this.params, ...params}\n          const {data, code} = await getList(param);\n          if (code === '0000') {\n            this.tableAndPageInfo.tableData = data.records\n            this.tableAndPageInfo.pager.total = data.total\n          }\n        } catch (e) {\n          console.log(e)\n        }\n      },\n\n      //工器具列表新增按钮\n      addSensorButton() {\n        this.isDisabled = false;\n        //打开弹出框\n        this.dialogFormVisible = true;\n        //设置弹出框表头\n        this.gqjTital = \"工器具新增\";\n        //清空弹出框内容\n        this.form = {\n          type:\"pd\"\n        };\n      },\n      //工器具列表详情按钮\n      getGqjInfo(row) {\n        //打开弹出框\n        this.dialogFormVisible = true;\n        //设置弹出框表头\n        this.gqjTital = \"工器具详情\";\n        //禁用所有输入框\n        this.isDisabled = true;\n        //给弹出框赋值\n        this.form = {...row}\n      },\n      //工器具修改按钮\n      updateGqjInfo(row) {\n        //打开弹出框\n        this.dialogFormVisible = true;\n        //设置弹出框表头\n        this.gqjTital = \"工器具修改\";\n        //开启弹出框内输入框编辑权限\n        this.isDisabled = false;\n        //给弹出框内赋值\n        this.form = {...row};\n\n      },\n      //工器具列表新增修改保存\n      async qxcommit() {\n        await this.$refs['form'].validate(async (valid) => {\n          if (valid) {\n            try {\n              let {code} = await saveOrUpdate(this.form)\n              if (code === '0000') {\n                this.$message.success(\"操作成功\")\n              }\n            } catch (e) {\n              console.log(e)\n            }\n            //恢复分页\n            this.tableAndPageInfo.pager.pageResize = 'Y';\n            this.getData();\n            this.dialogFormVisible = false;\n          }\n        })\n      },\n      //删除工器具列表\n      deleteRow(id) {\n        // if (this.selectRows.length < 1) {\n        //   this.$message.warning(\"请选择正确的数据！！！\")\n        //   return\n        // }\n        // let ids = this.selectRows.map(item => {\n        //   return item.objId\n        // });\n        let obj=[];\n        obj.push(id);\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove(obj).then(({code}) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              });\n              this.getData()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              });\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          });\n        });\n        this.tableAndPageInfo.pager.pageResize = 'Y';\n        this.getData()\n      },\n\n      //查看试验\n      handleSearchSYClick(row) {\n        this.sySelectRows = []\n        this.mainRowData = row\n        this.syQueryForm.gqjId = row.objId\n        this.sybgDialogFormVisible = true\n        this.getYxSyData()\n      },\n\n      //查看检修\n      handleSerchJWXClick(row) {\n        this.mainRowData = row\n        this.jxQueryForm.gqjId = row.objId\n        this.jwxDialogFormVisible = true\n        this.getJxRecords()\n      },\n      //添加检修\n      addJxButton() {\n        this.jxForm = this.$options.data().jxForm\n        this.jxForm.gqjId = this.mainRowData.objId\n        this.addJwxSybgDialogFormVisible = true\n      },\n      updateJx(row) {\n        this.jxForm = row\n        this.addJwxSybgDialogFormVisible = true\n      },\n      //添加试验\n      addSyButton() {\n        this.syFrom = this.$options.data().syFrom\n        this.syFrom.gqjId = this.mainRowData.objId\n        this.addSybgDialogFormVisible = true\n      },\n      updateSy(row) {\n        this.syFrom = row\n        this.addSybgDialogFormVisible = true\n      },\n      //每页展示数量点击事件\n      handleSizeChange() {\n\n      },\n      //页码改变事件\n      handleCurrentChange() {\n\n      },\n      //树点击事件\n      handleNodeClick() {\n\n      },\n\n      filterReset() {\n\n      },\n      //选择每一行\n      selectChange(rows) {\n        this.selectRows = rows\n      },\n\n      //获取试验记录数据\n      getYxSyData() {\n        this.syLoading = true\n        getYxSyRecords(this.syQueryForm).then(res => {\n          this.gqjsyList = res.data.records\n          this.syQueryForm.total = res.data.total\n          this.syLoading = false\n        })\n      },\n\n      //新增修改试验记录数据\n      saveOrUpdateSy() {\n        saveOrUpdateYxSyRecords(this.syFrom).then(res => {\n          this.getYxSyData()\n          this.addSybgDialogFormVisible = false\n        })\n      },\n      //批量删除试验数据\n      deleteYxSy() {\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          let ids = []\n          this.sySelectRows.forEach(item => {\n            ids.push(item.id)\n          })\n          deleteYxSyRecords(ids).then(({ code }) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n            }\n            this.getYxSyData()\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n\n      //获取检修记录数据\n      getJxRecords() {\n        this.jxLoading = true\n        getAssetGqjJxRecords(this.jxQueryForm).then(res => {\n          this.gqjJxList = res.data.records\n          this.jxQueryForm.total = res.data.total\n          this.jxLoading = false\n        })\n      },\n      //新增修改检修记录数据\n      saveOrUpdateJx() {\n        saveOrUpdateAssetGqjJxRecords(this.jxForm).then(res => {\n          this.getJxRecords()\n          this.addJwxSybgDialogFormVisible = false\n        })\n      },\n      deleteJxData() {\n\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          let ids = []\n          this.jxSelectRows.forEach(item => {\n            ids.push(item.id)\n          })\n          deleteAssetGqjJxRecords(ids).then(({ code }) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n            }\n            this.getJxRecords()\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n\n      syRowClick(rows) {\n        this.$refs.syTable.toggleRowSelection(rows)\n      },\n      syCurrentChange(val) {\n        this.sySelectRows = val\n      },\n      jxRowClick(rows) {\n        this.$refs.jxTable.toggleRowSelection(rows)\n      },\n      jxCurrentChange(val) {\n        this.jxSelectRows = val\n      }\n    }\n  };\n</script>\n\n<style lang='scss' scoped>\n\n  .button-group {\n    padding-left: 30px;\n    padding-right: 30px;\n    display: flex;\n    justify-content: flex-end;\n  }\n\n  .qxlr_dialog_insert {\n    margin-top: 6vh !important\n  }\n\n  /*/deep/ .qxlr_dialog_insert .el-input--medium .el-input__inner{*/\n  /*  width: 100%;*/\n  /*}*/\n  .el-select {\n    width: 100%;\n  }\n\n  .el-date-editor {\n    width: 100%;\n  }\n</style>\n"]}]}