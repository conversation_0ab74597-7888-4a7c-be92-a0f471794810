{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\gfdzcz\\components\\czp_tj.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\gfdzcz\\components\\czp_tj.vue", "mtime": 1751379558074}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["czp_tj.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAuBA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "czp_tj.vue", "sourceRoot": "src/views/dagangOilfield/czpgl/gfdzcz/components", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 110, itemWidth: 220 }\"\n      @handleEvent=\"handleEvent\"\n    />\n\n    <el-white class=\"button-group\">\n      <div>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"selectChange\"\n          height=\"67.5vh\"\n          v-loading=\"loading\"\n        />\n      </div>\n    </el-white>\n  </div>\n</template>\n\n<script>\nimport { getListTj } from \"@/api/yxgl/bdyxgl/bddzczp\";\nimport { getBdzDataListSelected as getBdzSelectList} from \"@/api/dagangOilfield/asset/gfsbtz\";\nimport { getFgsOptions } from \"@/api/yxgl/gfyxgl/gfzbgl\";\n\nexport default {\n  name: \"czp_tj\",\n  mounted() {\n    //列表查询\n    this.getData();\n    //获取光伏电站下拉框数据\n    this.getBdzSelectList();\n    this.getFgsOptions();\n  },\n  data() {\n    return {\n      organizationSelectedList: [],\n      loading: false,\n      // 查询光伏电站列表\n      bdzList: {},\n      //查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      // 查询数据总条数\n      total: 0,\n      filterInfo: {\n        data: {\n          fgs: \"\",\n          bdzmc: \"\"\n        }, //查询条件\n        fieldList: [\n          {\n            label: \"分公司\",\n            value: \"fgs\",\n            type: \"select\",\n            clearable: true,\n            options: [\n            ]\n          },\n          {\n            label: \"光伏电站名称\",\n            value: \"bdzmc\",\n            type: \"select\",\n            clearable: true,\n            filterable: true,\n            options: []\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"分公司\", prop: \"deptname\", minWidth: \"120\" },\n          { label: \"光伏电站\", prop: \"bdzmcs\", minWidth: \"150\" },\n          { label: \"操作任务\", prop: \"czrw\", minWidth: \"100\" },\n          { label: \"操作项数\", prop: \"czxs\", minWidth: \"100\" },\n          { label: \"已执行项数\", prop: \"yzxczxs\", minWidth: \"100\" },\n          { label: \"未执行项数\", prop: \"wzxczxs\", minWidth: \"100\" }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        lx: 4,\n        sfbj: 1\n      }\n    };\n  },\n  methods: {\n    /**\n     * 获取分公司下拉数据\n     */\n    getFgsOptions() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.organizationSelectedList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"fgs\") {\n            return (item.options = this.organizationSelectedList);\n          }\n        });\n      });\n    },\n    //统计\n    async getData(params) {\n      try {\n        const param = { ...this.params, ...params };\n        this.loading = true;\n        const { data, code } = await getListTj(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          this.loading = false;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    /**\n     * 列表选中\n     * */\n    selectChange(selection) {\n      this.ids = selection.map(item => item.objId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /**\n     * 获取光伏电站下拉框数据\n     */\n    getBdzSelectList() {\n      getBdzSelectList({}).then(res => {\n        this.bdzList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"bdzmc\") {\n            return (item.options = this.bdzList);\n          }\n        });\n      });\n    },\n    //下拉框change事件\n    handleEvent(val) {\n      //光伏电站查询下拉框选项根据所选分公司带出来\n      if (val.label === \"fgs\" && val.value && val.value !== \"\") {\n        let form = {\n          ssdwbm: val.value\n        };\n        getBdzSelectList(form).then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value == \"bdzmc\") {\n              return (item.options = res.data);\n            }\n          });\n        });\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n/*列表颜色设置*/\n/deep/ .el-table th {\n  background-color: #e8f7f0;\n}\n</style>\n"]}]}