{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\yxgl\\bdyxgl\\components\\sbqx.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\yxgl\\bdyxgl\\components\\sbqx.vue", "mtime": 1755528799754}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["sbqx.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAy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file": "sbqx.vue", "sourceRoot": "src/views/dagangOilfield/yxgl/bdyxgl/components", "sourcesContent": ["<template>\n  <div class=\"\" id=\"dialogActstdiv\">\n    <el-row class=\"sbqx\">\n      <!--   Tab页签   -->\n      <el-col :span=\"24\" class=\"sbqx_box\">\n        <div class=\"txtTitle\">\n          <span\n            @click=\"click('bdqx')\"\n            :class=\"this.flag === 'bdqx' ? 'tabActive' : 'noActive'\"\n            class=\"oneBtn\"\n          >\n            <span class=\"allBtn\">变电隐患</span>\n          </span>\n          <span\n            @click=\"click('xlqx')\"\n            :class=\"this.flag === 'xlqx' ? 'tabActive' : 'noActive'\"\n            class=\"twoBtn\"\n          >\n            <span class=\"allBtn\">线路隐患</span>\n          </span>\n          <span\n            @click=\"click('pdqx')\"\n            :class=\"this.flag === 'pdqx' ? 'tabActive' : 'noActive'\"\n            class=\"twoBtn\"\n          >\n            <span class=\"allBtn\">配电隐患</span>\n          </span>\n          <span\n            @click=\"click('gfqx')\"\n            :class=\"this.flag === 'gfqx' ? 'tabActive' : 'noActive'\"\n            class=\"twoBtn\"\n          >\n            <span class=\"allBtn\">新能源隐患</span>\n          </span>\n        </div>\n      </el-col>\n      <!--   变电缺陷   -->\n      <el-col :span=\"24\" v-if=\"this.flag === 'bdqx'\" class=\"sbqx_boxT\">\n        <qxgl_ys></qxgl_ys>\n      </el-col>\n      <!--   线路缺陷   -->\n      <el-col :span=\"24\" v-if=\"this.flag === 'xlqx'\" class=\"sbqx_boxT\">\n        <qxgl_xl></qxgl_xl>\n      </el-col>\n      <!--   配电缺陷   -->\n      <el-col :span=\"24\" v-if=\"this.flag === 'pdqx'\" class=\"sbqx_boxT\">\n        <qxgl_pd></qxgl_pd>\n      </el-col>\n      <!--   供服缺陷   -->\n      <el-col :span=\"24\" v-if=\"this.flag === 'gfqx'\" class=\"sbqx_boxT\">\n        <qxgl_gf></qxgl_gf>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nimport Qxgl_ys from \"@/views/dagangOilfield/yxgl/bdyxgl/components/qxgl_ys\";\nimport Qxgl_xl from \"@/views/dagangOilfield/yxgl/bdyxgl/components/qxgl_xl\";\nimport Qxgl_pd from \"@/views/dagangOilfield/yxgl/bdyxgl/components/qxgl_pd\";\nimport Qxgl_gf from \"@/views/dagangOilfield/yxgl/gfyxgl/components/qxgl_gf\";\n\nexport default {\n  name: \"sbqx\",\n  components: {\n    Qxgl_pd,\n    Qxgl_xl,\n    Qxgl_ys,\n    Qxgl_gf\n  },\n\n  data() {\n    return {\n      flag: \"bdqx\", //默认展示变电缺陷的内容\n      flyjMap: new Map(),\n      sbbjList: [], //设备部件list\n      sbbwList: [], //设备部位list\n      qxmsList: [], //缺陷描述list\n      qxflList: [], //缺陷分类list\n      qxflData: [], //缺陷分类所有数据\n      jsyyList: [],\n      //滚动条高度\n      scroll: \"\",\n      //当前显示的菜单区域\n      istyle: -1,\n      deptId: undefined,\n      jsbutten: false,\n      currUser: \"\",\n      currUserdw: this.$store.getters.deptId,\n      //提交审核按钮\n      buttonTjshShow: false,\n      zt: \"\",\n      // 消项验收结论\n      isShowYs: false,\n      // 消除处理三个复选框\n      isShowXqcl: false,\n      // 生产科处理建议\n      isShowScksh: false,\n      // 分公司处理建议\n      isShowFgssh: false,\n      tableDatas: [],\n      //巡视点位弹框\n      isShowXsdw: false,\n      //上传图片时的携带的其他参数\n      uploadImgData: {\n        businessId: \"\" //携带的表单主键id\n      },\n      //上传图片时的请求头\n      header: {},\n      //缺陷标准库dialog\n      isShowQxbzDialog: false,\n      //工作票\n      isShowGzp: false,\n      //使用当前设备类型编码查询缺陷标准库\n\n      //主设备选择传递子组件参数\n      selectedSbParam: {\n        lx: \"bd\",\n        sbmc: \"\"\n      },\n      //主设备弹出框\n      isShowSysbDialog: false,\n      //操作审核按钮\n      shButtonControl: true,\n      videoForm: {\n        showVideoPath: \"\"\n      },\n      //展示图片dialog控制\n      imgDialogVisible: false,\n      //图片地址url\n      dialogImageUrl: \"\",\n      //图片list\n      imgList: [],\n      //消项验收\n      xqimgList: [],\n      //缺陷类别\n      qxlbOptions: [\n        { label: \"变电\", value: \"变电\" },\n        { label: \"配电\", value: \"配电\" },\n        { label: \"输电\", value: \"输电\" }\n      ],\n      //验收结论\n      ysjlDisabled: false,\n      //隐藏部分card数据\n      fgsShCardShow: false,\n      //弹出框中表格数据\n      propTableData: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //锚点跳转按钮名称\n      // buttonArr: ['缺陷上报', '缺陷描述信息', '监控应急措施','消项处理', '消项验收'],\n      buttonArr: [\n        \"缺陷上报\",\n        \"缺陷描述信息\",\n        \"监控应急措施\",\n        \"班组审核\",\n        \"分公司审核\",\n        \"生产科审核\",\n        \"消项处理\",\n        \"消项验收\"\n      ],\n      activeBtn: 0,\n      assetSelect: false,\n      //设备部位\n      sbbwOptions: [\n        { label: \"本体端子箱\", value: \"本体端子箱\" },\n        { label: \"储油柜\", value: \"储油柜\" },\n        { label: \"呼吸器\", value: \"呼吸器\" }\n      ],\n      //弹出框内新增时下拉框变电站数据\n      bdzDataListOptions: [\n        { label: \"35kV然气站变电站\", value: \"35kV然气站变电站\" },\n        { label: \"110kV然气站变电站\", value: \"110kV然气站变电站\" },\n        { label: \"10kV然气站变电站\", value: \"10kV然气站变电站\" }\n      ],\n      //弹出框内新增时归属下拉框数据\n      gsOptionsDataList: [\n        { label: \"运行\", value: \"运行\" },\n        { label: \"分公司\", value: \"分公司\" },\n        { label: \"班组\", value: \"班组\" }\n      ],\n      // 发现方式选项\n      findWayOptions: [\n        { value: \"在线监测\", label: \"在线监测\" },\n        { value: \"人工发现\", label: \"人工发现\" },\n        { value: \"日常巡视\", label: \"日常巡视\" }\n      ],\n      // 检测技术选项\n      detectingOptions: [\n        { value: \"视频监控\", label: \"视频监控\" },\n        { value: \"人工判断\", label: \"人工判断\" },\n        { value: \"红外识别\", label: \"红外识别\" }\n      ],\n      // 设备部件选项\n      partsOptions: [\n        { value: \"本体\", label: \"本体\" },\n        { value: \"非电量保护\", label: \"非电量保护\" },\n        { value: \"基础\", label: \"基础\" },\n        { value: \"冷却器系统\", label: \"冷却器系统\" },\n        { value: \"分接开关\", label: \"分接开关\" },\n        { value: \"套管\", label: \"套管\" }\n      ],\n      // 缺陷性质选项\n      defectQualityOptions: [\n        { value: \"一般\", label: \"一般\" },\n        { value: \"严重\", label: \"严重\" },\n        { value: \"危急\", label: \"危急\" }\n      ],\n      //所属分公司下拉框数据\n      allFgsList: [],\n      //所属分公司下拉框数据\n      ssgsOptionsDataList: [],\n      //变电分公司对象\n      bdfgsObjArr: [],\n      //弹出框控制内容disabled\n      dialogFormDisabled: false,\n      dialogFormDisabledst: false,\n      dialogFormDisabledbz: false, //班组审核意见\n      //是否禁用后续编辑,默认禁用\n      isHistoryDisabled: true,\n      // 下拉树筛选文字\n      filterText: \"\",\n      zsb: \"\",\n      defaultProps: {\n        children: \"children\",\n        label: \"label\"\n      },\n      //选中得行数\n      selectRows: [],\n      // 查询数据总条数\n      total: 0,\n      // 对话框标题\n      title: \"\",\n      // 对话框是否打开\n      open: false,\n      // 新增/修改表单\n      form: {\n        id: undefined,\n        substation: undefined,\n        mainDevice: undefined,\n        defectId: undefined,\n        deviceType: undefined,\n        deviceModel: undefined,\n        manufacturer: undefined,\n        runNumber: undefined,\n        findDate: undefined,\n        attribution: undefined,\n        enterPerson: undefined,\n        enterDept: undefined,\n        findWay: undefined,\n        detecting: undefined,\n        findPerson: undefined,\n        parts: undefined,\n        defectDescription: undefined,\n        classifyGist: undefined,\n        defectQuality: undefined,\n        defectContent: undefined,\n        reason: undefined,\n        condition: undefined,\n        remark: undefined,\n        aqyxcqcs: undefined,\n        xsjcyq: undefined,\n        gzqk: undefined,\n        yjczfa: undefined,\n        qtyq: undefined,\n        fgsshr: undefined,\n        fgsshsj: undefined,\n        qrdqxfl: undefined,\n        fgscljy: undefined,\n        sckshr: undefined,\n        sckshsj: undefined,\n        sckcljy: undefined,\n        xqfzr: undefined,\n        xqfzrdw: undefined,\n        xqclsj: undefined,\n        xqcljg: undefined,\n        xqylwt: undefined,\n        xqysr: undefined,\n        xqyssj: undefined,\n        ysjl: undefined,\n        ysjg: undefined,\n        bzfzr: \"\",\n        bz: \"\",\n        //变电\n        lx: 2,\n        xqfzrldsj: \"\"\n      },\n      lrrdw: undefined,\n      xqfzrdw: undefined,\n      // 多选框选中的数据id\n      ids: [],\n      // 是否单选\n      single: true,\n      // 是否多选\n      multiple: true,\n      // 选中的数据\n      selectData: [],\n      // 归属选项\n      attributionOptions: [{ value: \"a1\", label: \"a1\" }],\n\n      // 缺陷描述选项\n      defectDescriptionOptions: [{ value: \"a1\", label: \"a1\" }],\n      // 分类依据\n      classifyGistOptions: [{ value: \"a1\", label: \"a1\" }],\n\n      // 表单校验\n      rules: {\n        qxnr: [{ required: true, message: \"请输入缺陷内容\", trigger: \"blur\" }],\n        qxlb: [\n          { required: true, message: \"请选择缺陷类别\", trigger: \"select\" }\n        ],\n        ssgs: [{ required: true, message: \"请选择分公司\", trigger: \"select\" }],\n        ssdz: [{ required: true, message: \"请选择所属位置\", trigger: \"blur\" }],\n        sbxh: [{ required: true, message: \"请输入设备型号\", trigger: \"blur\" }],\n        sccj: [{ required: true, message: \"请输入生产厂家\", trigger: \"blur\" }],\n        bzqxBj: [\n          { required: true, message: \"请输入设备部件\", trigger: \"blur\" }\n        ],\n        bzqxBw: [\n          { required: true, message: \"请输入设备部位\", trigger: \"blur\" }\n        ],\n        bzqxQxms: [\n          { required: true, message: \"请输入缺陷描述\", trigger: \"blur\" }\n        ],\n        bzqxFlyj: [\n          { required: true, message: \"请输入分类依据\", trigger: \"blur\" }\n        ],\n        // xsdw: [{ required: true, message: \"请输入巡视点位\", trigger: \"blur\" }],\n        sb: [{ required: true, message: \"请选择主设备\", trigger: \"blur\" }],\n        sblx: [{ required: true, message: \"请选择设备类型\", trigger: \"blur\" }],\n        fxrq: [\n          { required: true, message: \"请选择发现日期\", trigger: \"change\" }\n        ],\n        // xsdw: [\n        //   { required: true, message: \"请选择巡视点位\", trigger: \"change\" },\n        // ],\n        fxr: [{ required: true, message: \"请输入发现人\", trigger: \"change\" }],\n        fxfs: [\n          { required: true, message: \"请选择发现方式\", trigger: \"change\" }\n        ],\n        jcjs: [\n          { required: true, message: \"请选择检测技术\", trigger: \"change\" }\n        ],\n        sbbj: [\n          { required: true, message: \"请选择获取标准库\", trigger: \"change\" }\n        ]\n      },\n      // 表单是否可编辑\n      isEditable: true,\n      filterInfo: {\n        data: {\n          ssgs: [],\n          ssdz: \"\",\n          qxxz: \"\",\n          sbxh: \"\",\n          sb: \"\",\n          sblx: \"\",\n          lczt: []\n        },\n        fieldList: [\n          {\n            label: \"所属公司\",\n            type: \"select\",\n            value: \"ssgs\",\n            multiple: true,\n            options: [\n              { value: \"港东变电分公司\", label: \"港东变电分公司\" },\n              { value: \"港中变电分公司\", label: \"港中变电分公司\" },\n              { value: \"南部变电分公司\", label: \"南部变电分公司\" },\n              { value: \"线路分公司\", label: \"线路分公司\" },\n              { value: \"配电运维分公司\", label: \"配电运维分公司\" }\n            ]\n          },\n          { label: \"所属位置\", type: \"input\", value: \"ssdz\" },\n          { label: \"设备名称\", type: \"input\", value: \"sb\" },\n          { label: \"设备类型\", type: \"input\", value: \"sblx\" },\n          {\n            label: \"缺陷性质\",\n            type: \"select\",\n            value: \"qxxz\",\n            options: [\n              { label: \"一般\", value: \"一般\" },\n              { label: \"严重\", value: \"严重\" },\n              { label: \"危急\", value: \"危急\" }\n            ]\n          },\n          {\n            label: \"状态\",\n            type: \"select\",\n            value: \"lczt\",\n            multiple: true,\n            options: [\n              { label: \"待上报\", value: \"1\" },\n              { label: \"历史录入\", value: \"0\" },\n              { label: \"分公司审核\", value: \"2\" },\n              { label: \"生产科审核\", value: \"3\" },\n              { label: \"检修安排\", value: \"4\" },\n              { label: \"待处理\", value: \"5\" },\n              { label: \"待验收\", value: \"6\" },\n              { label: \"已消项\", value: \"7\" }\n            ]\n          },\n          { label: \"设备型号\", type: \"input\", value: \"sbxh\" }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          pageResize: \"\",\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"ssgs\", label: \"所属公司\", minWidth: \"120\" },\n          { prop: \"ssdz\", label: \"所属位置\", minWidth: \"120\" },\n          { prop: \"sb\", label: \"主设备\", minWidth: \"120\" },\n          { prop: \"sblx\", label: \"设备类型\", minWidth: \"100\" },\n          { prop: \"bzqxQxdj\", label: \"缺陷性质\", minWidth: \"120\" },\n          { prop: \"ztmc\", label: \"状态\", minWidth: \"120\" },\n          // {prop: 'dydj', label: '电压等级', minWidth: '100'},\n          { prop: \"sbxh\", label: \"设备型号\", minWidth: \"120\" },\n          { prop: \"sccj\", label: \"生产厂家\", minWidth: \"120\" },\n          // { prop: \"gzpbh\", label: \"工作票编号\", minWidth: \"120\" },\n          { prop: \"createTime\", label: \"创建时间\", minWidth: \"150\" }\n        ]\n      },\n      params: {\n        //变电\n        lx: 2,\n        lczt: \"1,2,3,4,5,6,7,8,9,11\" //查看所有状态数据\n      },\n      loading: null,\n      openSb: false,\n      form1: {},\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"qxlccs\",\n        businessKey: \"\",\n        businessType: \"缺陷管理\",\n        variables: {},\n        defaultFrom: true,\n        nextUser: \"\",\n        processType: \"complete\"\n      },\n      activitiOption: { title: \"上报\" },\n      timeData: [],\n      timeLineShow: false,\n      isShow: false,\n      openLoadingImg: false,\n      imgSrc: \"\",\n\n      //线路分公司数组\n      lineFgsArr: [\"线路分公司\"],\n      //配电分公司数组\n      pdFgsArr: [\"配电运维分公司\"],\n      //弹出框内新增时下拉框所属位置数据\n      wzDataListOptions: [],\n      //获取设备类型弹出框\n      showSblxTree: false,\n      //获取设备类型弹出框传递参数\n      selectSbp: [],\n      //判断从哪点击的设备类型弹出框\n      isFilter: false,\n      //巡视点位下拉框数据\n      xsdwOptions: [],\n\n      tempFileName: \"设备缺陷表单\",\n\n      //视频上传进度条\n      videoFlag: false,\n      //是否显示视频进度条\n      videoUploadPercent: \"\",\n      videoList: [],\n      paramss: {},\n      xsdwparams: {\n        sswz: \"\",\n        sbmc: \"\",\n        xsdw: \"\"\n      },\n      isDz: false,\n      qxlb: \"变电\"\n    };\n  },\n  methods: {\n    click(mainTab) {\n      this.flag = mainTab;\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.sbqx_box {\n  padding: 20px 0 0 20px;\n}\n.sbqx_boxT {\n  margin-top: -18px;\n}\n.tabActive {\n  width: 10%;\n  float: left;\n  color: #fff;\n  background: #02b988;\n  border-top: 0;\n}\n.noActive {\n  width: 10%;\n  float: left;\n  background: #fff;\n  color: #545252;\n  &:hover {\n    background: #ffffff;\n    color: #359076;\n  }\n}\n.oneBtn {\n  margin-right: -15px;\n}\n.twoBtn {\n  transform: skewX(33deg);\n  border-right: 1px solid #9a989869;\n  .allBtn {\n    transform: skewX(-33deg);\n    display: inline-block;\n  }\n}\n</style>\n"]}]}