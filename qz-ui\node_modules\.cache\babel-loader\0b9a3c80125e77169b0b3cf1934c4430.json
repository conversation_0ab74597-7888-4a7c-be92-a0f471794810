{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\asset\\parameters.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\asset\\parameters.js", "mtime": 1706897313899}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0UGFyYW1EYXRhTGlzdCA9IGdldFBhcmFtRGF0YUxpc3Q7CmV4cG9ydHMuc2F2ZVBhcmFtVmFsdWUgPSBzYXZlUGFyYW1WYWx1ZTsKZXhwb3J0cy5nZXRQYXJhbXNWYWx1ZSA9IGdldFBhcmFtc1ZhbHVlOwoKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Cgp2YXIgYmFzZVVybCA9ICcvbWFuYWdlci1hcGknOwovKioKICog6I635Y+W5oqA5pyv5Y+C5pWwCiAqIEBwYXJhbSBwYXJhbXMKICogQHJldHVybnMge1Byb21pc2UgfCBQcm9taXNlPHVua25vd24+fQogKi8KCmZ1bmN0aW9uIGdldFBhcmFtRGF0YUxpc3QocGFyYW1zKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICIvcGFyYW1ldGVyL2dldERhdGEiLCBwYXJhbXMsIDIpOwp9Ci8qKgogKiDkv53lrZjmioDmnK/lj4LmlbDlgLwKICogQHBhcmFtIHBhcmFtcwogKiBAcmV0dXJucyB7UHJvbWlzZSB8IFByb21pc2U8dW5rbm93bj59CiAqLwoKCmZ1bmN0aW9uIHNhdmVQYXJhbVZhbHVlKHBhcmFtcykgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3RQb3N0KGJhc2VVcmwgKyAiL3BhcmFtZXRlci9zYXZlVmFsdWUiLCBwYXJhbXMsIDIpOwp9Ci8qKgogKiDojrflj5bmioDmnK/lj4LmlbDlgLwKICogQHBhcmFtIHBhcmFtcwogKiBAcmV0dXJucyB7UHJvbWlzZSB8IFByb21pc2U8dW5rbm93bj59CiAqLwoKCmZ1bmN0aW9uIGdldFBhcmFtc1ZhbHVlKHBhcmFtcykgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3RQb3N0KGJhc2VVcmwgKyAiL3BhcmFtZXRlci9nZXREYXRhVmFsdWUiLCBKU09OLnN0cmluZ2lmeShwYXJhbXMpLCAyKTsKfQ=="}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagangOilfield/asset/parameters.js"], "names": ["baseUrl", "getParamDataList", "params", "api", "requestPost", "saveParamValue", "getParamsValue", "JSON", "stringify"], "mappings": ";;;;;;;;;;;AAAA;;AAEA,IAAMA,OAAO,GAAG,cAAhB;AAEA;;;;;;AAKO,SAASC,gBAAT,CAA0BC,MAA1B,EAAiC;AACtC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,oBAAxB,EAA6CE,MAA7C,EAAoD,CAApD,CAAP;AAED;AAED;;;;;;;AAKO,SAAUG,cAAV,CAAyBH,MAAzB,EAAgC;AACrC,SAAQC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,sBAAxB,EAA+CE,MAA/C,EAAsD,CAAtD,CAAR;AACD;AAED;;;;;;;AAKO,SAASI,cAAT,CAAwBJ,MAAxB,EAA+B;AACpC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,yBAAxB,EAAkDO,IAAI,CAACC,SAAL,CAAeN,MAAf,CAAlD,EAAyE,CAAzE,CAAP;AAED", "sourcesContent": ["import api from '@/utils/request'\n\nconst baseUrl = '/manager-api'\n\n/**\n * 获取技术参数\n * @param params\n * @returns {Promise | Promise<unknown>}\n */\nexport function getParamDataList(params){\n  return api.requestPost(baseUrl+\"/parameter/getData\",params,2);\n\n}\n\n/**\n * 保存技术参数值\n * @param params\n * @returns {Promise | Promise<unknown>}\n */\nexport function  saveParamValue(params){\n  return  api.requestPost(baseUrl+\"/parameter/saveValue\",params,2);\n}\n\n/**\n * 获取技术参数值\n * @param params\n * @returns {Promise | Promise<unknown>}\n */\nexport function getParamsValue(params){\n  return api.requestPost(baseUrl+\"/parameter/getDataValue\",JSON.stringify(params),2);\n\n}\n"]}]}