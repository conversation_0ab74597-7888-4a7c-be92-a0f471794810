{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\yxgl\\gfyxgl\\gfdzczml.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\yxgl\\gfyxgl\\gfdzczml.js", "mtime": 1730713774665}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/yxgl/gfyxgl/gfdzczml.js"], "names": ["baseUrl", "baseUrls", "getBdzSelectList", "param", "api", "requestPost", "getList", "params", "saveOrUpdate", "remove", "saveOrUpdateCzp", "exportExcel", "fileName", "exportWordByselection", "requestDownloadFile", "url", "data", "exportWordByparams"], "mappings": ";;;;;;;;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,gBAAhB;AACA,IAAMC,QAAQ,GAAG,cAAjB;AAEA;;;;AAGO,SAASC,gBAAT,CAA0BC,KAA1B,EAAiC;AACtC,SAAOC,iBAAIC,WAAJ,CAAgBJ,QAAQ,GAAC,+BAAzB,EAAyDE,KAAzD,EAA+D,CAA/D,CAAP;AACD,C,CAED;;;AACO,SAASG,OAAT,CAAiBC,MAAjB,EAAyB;AAC9B,SAAOH,iBAAIC,WAAJ,CAAgBL,OAAO,GAAC,kBAAxB,EAA2CO,MAA3C,EAAkD,CAAlD,CAAP;AACD,C,CAED;;;AACO,SAASC,YAAT,CAAsBD,MAAtB,EAA8B;AACnC,SAAOH,iBAAIC,WAAJ,CAAgBL,OAAO,GAAC,0BAAxB,EAAmDO,MAAnD,EAA0D,CAA1D,CAAP;AACD,C,CACD;;;AACO,SAASE,MAAT,CAAgBF,MAAhB,EAAwB;AAC7B,SAAOH,iBAAIC,WAAJ,CAAgBL,OAAO,GAAC,oBAAxB,EAA6CO,MAA7C,EAAoD,CAApD,CAAP;AACD,C,CAGD;;;AACO,SAASG,eAAT,CAAyBH,MAAzB,EAAiC;AACtC,SAAOH,iBAAIC,WAAJ,CAAgBL,OAAO,GAAC,yBAAxB,EAAkDO,MAAlD,EAAyD,CAAzD,CAAP;AACD,C,CACD;;;AACO,SAASI,WAAT,CAAqBJ,MAArB,EAA4BK,QAA5B,EAAsC;AAC3C,SAAOR,iBAAIO,WAAJ,CAAgBX,OAAO,GAAC,yBAAxB,EAAkDO,MAAlD,EAAyDK,QAAzD,CAAP;AACD,C,CACD;;;AACO,SAASC,qBAAT,CAA+BN,MAA/B,EAAsCK,QAAtC,EAAgD;AACrD,SAAOR,iBAAIU,mBAAJ,CAAwBd,OAAO,GAAC,GAAR,GAAYO,MAAM,CAACQ,GAAnB,GAAuB,wBAA/C,EAAwEH,QAAxE,EAAiFL,MAAM,CAACS,IAAxF,EAA6F,CAA7F,EAA+F,KAA/F,CAAP;AACD,C,CACD;;;AACO,SAASC,kBAAT,CAA4BV,MAA5B,EAAmCK,QAAnC,EAA6C;AAClD,SAAOR,iBAAIU,mBAAJ,CAAwBd,OAAO,GAAC,GAAR,GAAYO,MAAM,CAACQ,GAAnB,GAAuB,qBAA/C,EAAqEH,QAArE,EAA8EL,MAAM,CAACS,IAArF,EAA0F,CAA1F,EAA4F,KAA5F,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\nconst baseUrl = \"/operation-api\";\nconst baseUrls = \"/manager-api\";\n\n/**\n * 变电站下拉框数据查询\n * */\nexport function getBdzSelectList(param) {\n  return api.requestPost(baseUrls+'/equipList/getGfBdzSelectList',param,1)\n}\n\n// 查询典型操作票及明细表\nexport function getList(params) {\n  return api.requestPost(baseUrl+'/yxGfdzczml/page',params,1)\n}\n\n// 添加或修改\nexport function saveOrUpdate(params) {\n  return api.requestPost(baseUrl+'/yxGfdzczml/saveOrUpdate',params,1)\n}\n// 删除\nexport function remove(params) {\n  return api.requestPost(baseUrl+'/yxGfdzczml/remove',params,1)\n}\n\n\n// 操作票添加或修改\nexport function saveOrUpdateCzp(params) {\n  return api.requestPost(baseUrl+'/yxBddzczp/saveOrUpdate',params,1)\n}\n// 导出\nexport function exportExcel(params,fileName) {\n  return api.exportExcel(baseUrl+'/yxGfdzczml/exportExcel',params,fileName)\n}\n// 导出Word\nexport function exportWordByselection(params,fileName) {\n  return api.requestDownloadFile(baseUrl+'/'+params.url+'/exportWordByselection',fileName,params.data,1,false)\n}\n// 导出Word\nexport function exportWordByparams(params,fileName) {\n  return api.requestDownloadFile(baseUrl+'/'+params.url+'/exportWordByparams',fileName,params.data,1,false)\n}\n"]}]}