{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\gfdzcz\\dzczml.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\gfdzcz\\dzczml.vue", "mtime": 1751374208669}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["dzczml.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAm2CA;;AACA;;AACA;;AAUA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,QADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,oBAAA,EAAA,6BAAA;AAAA,IAAA,SAAA,EAAA,kBAAA;AAAA,IAAA,QAAA,EAAA,iBAAA;AAAA,IAAA,QAAA,EAAA;AAAA,GAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA,MAAA,YAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,YADA;AAEA;AACA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,oBAAA,EAAA,OADA;AAEA,QAAA,WAAA,EAAA,EAFA;AAGA,QAAA,YAAA,EAAA,OAHA;AAIA,QAAA,SAAA,EAAA,EAJA;AAKA,QAAA,WAAA,EAAA,IALA;AAMA,QAAA,QAAA,EAAA,EANA;AAOA,QAAA,WAAA,EAAA;AAPA,OAJA;AAaA,MAAA,MAAA,EAAA,KAbA;AAcA;AACA,MAAA,cAAA,EAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAfA;AAgBA,MAAA,QAAA,EAAA,CACA;AACA;AACA;AAHA,OAhBA;AAqBA,MAAA,KAAA,EAAA;AACA,QAAA,KAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CADA;AAEA,QAAA,GAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAFA;AAGA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AAHA,OArBA;AA0BA,MAAA,MAAA,EAAA;AACA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CADA;AAEA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAFA;AAGA,QAAA,GAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAHA;AAIA,QAAA,GAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAJA;AAKA,QAAA,GAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CALA;AAMA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CANA;AAOA,QAAA,OAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CAPA;AAQA,QAAA,OAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CARA;AASA,QAAA,KAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA,CATA;AAUA,QAAA,IAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AAVA,OA1BA;AAsCA,MAAA,WAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,IAtCA;AAuCA,MAAA,EAAA,EAAA,KAvCA;AAwCA,MAAA,WAAA,EAAA,EAxCA;AAyCA,MAAA,OAAA,EAAA,EAzCA;AA0CA;AACA,MAAA,cAAA,EAAA,EA3CA;AA4CA;AACA,MAAA,gBAAA,EAAA,KA7CA;AA8CA;AACA,MAAA,aAAA,EAAA;AACA,QAAA,UAAA,EAAA,EADA,CACA;;AADA,OA/CA;AAkDA;AACA,MAAA,MAAA,EAAA,EAnDA;AAoDA;AACA,MAAA,OAAA,EAAA,EArDA;AAsDA,MAAA,QAAA,EAAA,KAtDA;AAuDA,MAAA,OAAA,EAAA,EAvDA;AAwDA;AACA,MAAA,iBAAA,EAAA,EAzDA;AA0DA,MAAA,SAAA,EAAA,KA1DA;AA2DA,MAAA,SAAA,EAAA,KA3DA;AA4DA,MAAA,OAAA,EAAA,KA5DA;AA6DA,MAAA,aAAA,EAAA,KA7DA;AA8DA,MAAA,gBAAA,EAAA,KA9DA;AA+DA,MAAA,gBAAA,EAAA,KA/DA;AAgEA;AACA,MAAA,gBAAA,EAAA,KAjEA;AAkEA,MAAA,gBAAA,EAAA,KAlEA;AAmEA;AACA,MAAA,GAAA,EAAA,EApEA;AAqEA,MAAA,UAAA,EAAA,EArEA;AAsEA;AACA,MAAA,MAAA,EAAA;AACA;AACA,QAAA,EAAA,EAAA;AAFA,OAvEA;AA2EA;AACA,MAAA,WAAA,EAAA,EA5EA;AA6EA;AACA,MAAA,cAAA,EAAA;AACA,QAAA,MAAA,EAAA,GADA;AAEA,QAAA,EAAA,EAAA;AAFA,OA9EA;AAkFA;AACA,MAAA,aAAA,EAAA;AACA,QAAA,GAAA,EAAA,IADA;AACA;AACA,QAAA,QAAA,EAAA;AAFA,OAnFA;AAuFA;AACA,MAAA,kBAAA,EAAA;AACA,QAAA,GAAA,EAAA,IADA;AACA;AACA,QAAA,QAAA,EAAA;AAFA,OAxFA;AA4FA;AACA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,SAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,CARA;AAaA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,YAAA,EAAA;AAAA,SAbA;AAcA,QAAA,GAAA,EAAA,IAdA,CAcA;;AAdA,OA7FA;AA6GA;AACA,MAAA,kBAAA,EAAA;AACA,QAAA,GAAA,EAAA,IADA;AACA;AACA,QAAA,QAAA,EAAA;AAFA,OA9GA;AAkHA;AACA,MAAA,gBAAA,EAAA;AACA,QAAA,GAAA,EAAA,IADA;AACA;AACA,QAAA,QAAA,EAAA;AAFA,OAnHA;AAuHA;AACA,MAAA,IAAA,EAAA;AACA,QAAA,EAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,GAAA,EAAA,EAJA;AAKA,QAAA,IAAA,EAAA,EALA;AAMA,QAAA,GAAA,EAAA,EANA;AAOA,QAAA,IAAA,EAAA,EAPA;AAQA,QAAA,GAAA,EAAA,EARA;AASA,QAAA,IAAA,EAAA,EATA;AAUA,QAAA,MAAA,EAAA;AAVA,OAxHA;AAoIA;AACA,MAAA,OAAA,EAAA;AACA,QAAA,EAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA,EAJA;AAKA,QAAA,IAAA,EAAA,EALA;AAMA,QAAA,GAAA,EAAA,EANA;AAOA,QAAA,GAAA,EAAA,EAPA;AAQA,QAAA,GAAA,EAAA,EARA;AASA,QAAA,GAAA,EAAA,EATA;AAUA,QAAA,EAAA,EAAA,CAVA;AAUA;AACA,QAAA,QAAA,EAAA,EAXA;AAYA,QAAA,MAAA,EAAA;AAZA,OArIA;AAmJA;AACA,MAAA,OAAA,EAAA;AACA,QAAA,EAAA,EAAA,EADA;AAEA,QAAA,KAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA,EAJA;AAKA,QAAA,IAAA,EAAA,EALA;AAMA,QAAA,GAAA,EAAA,EANA;AAOA,QAAA,GAAA,EAAA,EAPA;AAQA,QAAA,GAAA,EAAA,EARA;AASA,QAAA,GAAA,EAAA,EATA;AAUA,QAAA,EAAA,EAAA,CAVA;AAUA;AACA,QAAA,QAAA,EAAA,EAXA;AAYA,QAAA,MAAA,EAAA;AAZA,OApJA;AAmKA;AACA,MAAA,mBAAA,EAAA,EApKA;AAsKA;AACA,MAAA,aAAA,EAAA,KAvKA;AAwKA;AACA,MAAA,UAAA,EAAA,KAzKA;AA0KA;AACA,MAAA,KAAA,EAAA,EA3KA;AA4KA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,EAAA,EAAA,EADA;AAEA,UAAA,KAAA,EAAA,EAFA;AAGA,UAAA,OAAA,EAAA,EAHA;AAIA,UAAA,GAAA,EAAA,EAJA;AAKA,UAAA,OAAA,EAAA,EALA;AAMA,UAAA,GAAA,EAAA,EANA;AAOA,UAAA,IAAA,EAAA,EAPA;AAQA,UAAA,GAAA,EAAA,EARA;AASA,UAAA,OAAA,EAAA;AATA,SADA;AAWA;AACA,QAAA,SAAA,EAAA,CACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAA,KAAA,EAAA,KADA;AAEA,UAAA,KAAA,EAAA,KAFA;AAGA,UAAA,IAAA,EAAA,QAHA;AAIA,UAAA,OAAA,EAAA,EAJA;AAKA,UAAA,SAAA,EAAA;AALA,SAVA,EAiBA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,OAFA;AAGA,UAAA,IAAA,EAAA,QAHA;AAIA,UAAA,OAAA,EAAA,EAJA;AAKA,UAAA,SAAA,EAAA,IALA;AAMA,UAAA,UAAA,EAAA;AANA,SAjBA,EAyBA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,SAFA;AAGA,UAAA,IAAA,EAAA,MAHA;AAIA,UAAA,QAAA,EAAA,WAJA;AAKA,UAAA,MAAA,EAAA;AALA,SAzBA,EAgCA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SAhCA,EAiCA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,SAFA;AAGA,UAAA,IAAA,EAAA,MAHA;AAIA,UAAA,QAAA,EAAA,WAJA;AAKA,UAAA,MAAA,EAAA;AALA,SAjCA,EAwCA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SAxCA,EAyCA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SAzCA,EA0CA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,SAAA,EAAA;AAAA,SA1CA,EA2CA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,KAAA,EAAA,SAFA;AAGA,UAAA,IAAA,EAAA,MAHA;AAIA,UAAA,QAAA,EAAA,WAJA;AAKA,UAAA,MAAA,EAAA;AALA,SA3CA;AAZA,OA5KA;AA4OA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA;AACA;AACA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,EAQA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SARA,EASA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SATA,EAUA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAVA,EAWA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAXA,CARA;AAqBA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AArBA,OA5OA;AAmQA,MAAA,YAAA,EAAA,EAnQA;AAoQA,MAAA,OAAA,EAAA,EApQA;AAqQA,MAAA,UAAA,EAAA,EArQA;AAsQA,MAAA,OAAA,EAAA;AAtQA,KAAA;AAwQA,GA5QA;AA6QA,EAAA,OA7QA,qBA6QA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA,cAAA,KAAA,CAAA,MAAA,CAAA,KAAA,GAAA,qBAAA,CAFA,CAGA;;AACA,cAAA,KAAA,CAAA,aAAA;;AACA,cAAA,KAAA,CAAA,mBAAA;;AALA;AAAA,qBAMA,KAAA,CAAA,aAAA,CAAA,GAAA,EAAA,EAAA,CANA;;AAAA;AAMA,cAAA,KAAA,CAAA,YANA;AAAA;AAAA,qBAOA,KAAA,CAAA,aAAA,CAAA,GAAA,EAAA,EAAA,CAPA;;AAAA;AAOA,cAAA,KAAA,CAAA,UAPA;AAAA;AAAA,qBAQA,KAAA,CAAA,aAAA,CAAA,GAAA,EAAA,EAAA,CARA;;AAAA;AAQA,cAAA,KAAA,CAAA,OARA;AAAA;AAAA,qBASA,KAAA,CAAA,OAAA,EATA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,GAvRA;AAwRA,EAAA,OAAA,EAAA;AACA;;;AAGA,IAAA,aAJA,2BAIA;AAAA;;AACA,iCAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAAA,QAAA,EAAA;AACA,SAFA;AAGA,QAAA,MAAA,CAAA,mBAAA,GAAA,GAAA,CAAA,IAAA;;AACA,QAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,IAAA,KAAA,EAAA;AACA,mBAAA,IAAA,CAAA,OAAA,GAAA,MAAA,CAAA,mBAAA;AACA;AACA,SAJA;AAKA,OAVA;AAWA,KAhBA;AAiBA;AACA,IAAA,SAlBA,uBAkBA;AACA,iCAAA,KAAA,MAAA,EAAA,QAAA;AACA,KApBA;AAqBA;AACA,IAAA,UAtBA,sBAsBA,IAtBA,EAsBA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,GADA,GACA;AACA,kBAAA,KAAA,EAAA,IAAA,CAAA,WADA;AAEA,kBAAA,OAAA,EAAA,CAFA;AAGA,kBAAA,MAAA,EAAA,IAAA,CAAA,WAAA,KAAA,UAAA,GAAA,CAAA,GAAA;AAHA,iBADA;AAMA,gBAAA,GAAA,CAAA,MAAA,GAAA,GAAA;AACA,gBAAA,GAAA,CAAA,KAAA,GAAA,IAAA,CAAA,QAAA;AAPA;AAAA,uBAQA,yBAAA,GAAA,CARA;;AAAA;AAAA;AAQA,gBAAA,IARA,qBAQA,IARA;;AAAA,sBASA,IAAA,KAAA,MATA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAUA,MAAA,CAAA,OAAA,EAVA;;AAAA;AAAA;AAAA;;AAAA;AAYA,gBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AAZA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA,KApCA;AAqCA;AACA,IAAA,aAtCA,2BAsCA;AACA,UAAA,KAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,EAAA;AACA,aAAA,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,CAAA,WAAA;AACA;;AACA,WAAA,MAAA,GAAA,KAAA;AACA,KA3CA;AA4CA,IAAA,mBA5CA,iCA4CA;AAAA;;AACA,sCAAA,EAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KAhDA;AAiDA;AACA,IAAA,eAlDA,2BAkDA,QAlDA,EAkDA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA,gBAAA,MAAA,CAAA,iBAAA,GAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,EAAA,OAAA,EAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,EAAA,KAAA,EAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,OAAA,GAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,EAAA,KAAA,EAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAPA,CAQA;;;AARA;AAAA,uBASA,MAAA,CAAA,SAAA,CAAA,QAAA,CATA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,KA5DA;AA6DA,IAAA,SA7DA,qBA6DA,QA7DA,EA6DA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA,gBAAA,MAAA,CAAA,gBAAA,CAAA,QAAA;;AAFA;AAAA,uBAGA,MAAA,CAAA,aAAA,CAAA,GAAA,EAAA,QAAA,CAHA;;AAAA;AAGA,gBAAA,MAAA,CAAA,OAHA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,KAjEA;AAkEA,IAAA,cAlEA,4BAkEA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,WAAA,EAAA,GAAA,IAAA;AACA,KArEA;AAsEA,IAAA,UAtEA,wBAsEA;AAAA;;AACA,WAAA,aAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,QAAA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA,IAAA,CAAA,UAAA,CACA,MAAA,CAAA,WAAA,CAAA,MADA,EAEA,MAAA,CAAA,WAAA,CAAA,MAFA,CAAA;AAIA,OALA;AAMA,KA7EA;AA8EA;AACA,IAAA,UA/EA,wBA+EA;AAAA;;AACA,UAAA,MAAA,GAAA,EAAA,CADA,CACA;;AACA,UAAA,SAAA,GAAA,CAAA,KAAA,EAAA,KAAA,CAAA;AACA,UAAA,QAAA,GAAA,IAAA,QAAA,EAAA,CAHA,CAIA;;AACA,WAAA,OAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,GAAA,IAAA,SAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA;AACA;;AACA,QAAA,QAAA,CAAA,MAAA,CAAA,OAAA,EAAA,IAAA,CAAA,GAAA;AACA,OALA;AAMA,MAAA,QAAA,CAAA,MAAA,CAAA,YAAA,EAAA,KAAA,aAAA,CAAA,UAAA,EAXA,CAWA;;AACA,MAAA,QAAA,CAAA,MAAA,CAAA,QAAA,EAAA,MAAA,EAZA,CAYA;;AACA,MAAA,QAAA,CAAA,MAAA,CAAA,MAAA,EAAA,SAAA,EAbA,CAaA;;AACA,uBACA,WADA,CACA,2BADA,EACA,QADA,EACA,CADA,EAEA,IAFA,CAEA,UAAA,GAAA,EAAA;AACA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,EAAA,CAFA,CAGA;;AACA,QAAA,MAAA,CAAA,OAAA;AACA,OAPA,EAQA,KARA,CAQA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,SAAA;AACA,OAVA;AAWA,KAxGA;AAyGA;AACA,IAAA,YA1GA,wBA0GA,IA1GA,EA0GA,QA1GA,EA0GA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,KA5GA;AA6GA,IAAA,cA7GA,0BA6GA,KA7GA,EA6GA,IA7GA,EA6GA,QA7GA,EA6GA,CAAA,CA7GA;AA8GA;AACA,IAAA,YA/GA,wBA+GA,IA/GA,EA+GA,QA/GA,EA+GA;AACA,WAAA,OAAA,GAAA,QAAA;AACA,KAjHA;AAkHA;AACA,IAAA,wBAnHA,oCAmHA,IAnHA,EAmHA;AACA,WAAA,cAAA,GAAA,IAAA,CAAA,GAAA;AACA,WAAA,gBAAA,GAAA,IAAA;AACA,KAtHA;AAuHA;AACA,IAAA,OAxHA,mBAwHA,MAxHA,EAwHA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,MAAA,CAAA,MAAA,+DAAA,MAAA,CAAA,MAAA,GAAA,MAAA;AACA,gBAAA,KAHA,GAGA,MAAA,CAAA,MAHA;AAIA,gBAAA,MAAA,CAAA,OAAA,GAAA,IAAA;AACA,gBAAA,KAAA,CAAA,OAAA,GAAA,CAAA;AAAA,kBAAA,IAAA,EAAA,YAAA;AAAA,kBAAA,GAAA,EAAA;AAAA,iBAAA,CAAA;AALA;AAAA,uBAMA,uBAAA,KAAA,CANA;;AAAA;AAAA;AAMA,gBAAA,IANA,kBAMA,IANA;AAMA,gBAAA,IANA,kBAMA,IANA;;AAOA,oBAAA,IAAA,KAAA,MAAA,EAAA;AAAA,uEACA,IAAA,CAAA,OADA;;AAAA;AACA,wEAAA;AAAA,sBAAA,CAAA;AACA,sBAAA,CAAA,CAAA,KAAA,GAAA,MAAA,CAAA,UAAA,CAAA,CAAA,CAAA,GAAA,CAAA;AACA,sBAAA,CAAA,CAAA,KAAA,GAAA,MAAA,CAAA,cAAA,CAAA,CAAA,CAAA,GAAA,CAAA;AACA,sBAAA,CAAA,CAAA,KAAA,GAAA,MAAA,CAAA,cAAA,CAAA,CAAA,CAAA,GAAA,CAAA;AACA,sBAAA,CAAA,CAAA,KAAA,GAAA,MAAA,CAAA,SAAA,CAAA,CAAA,CAAA,GAAA,CAAA;AACA,sBAAA,CAAA,CAAA,QAAA,GAAA,MAAA,CAAA,SAAA,CAAA,CAAA,CAAA,MAAA,CAAA;AACA;AAPA;AAAA;AAAA;AAAA;AAAA;;AAQA,kBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA,kBAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA;;AAlBA;AAAA;;AAAA;AAAA;AAAA;AAoBA,gBAAA,OAAA,CAAA,GAAA;;AApBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsBA,KA9IA;AAgJA;AACA,IAAA,SAjJA,uBAiJA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,KAAA,GAAA,YAAA;AACA,gBAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,gBAAA,OAAA,CAAA,IAAA,GAAA;AAAA,kBAAA,GAAA,EAAA,OAAA,CAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,QAAA;AAAA,iBAAA;AAHA;AAAA,uBAIA,OAAA,CAAA,SAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,CAJA;;AAAA;AAKA,gBAAA,OAAA,CAAA,IAAA,CAAA,MAAA,GAAA,GAAA;AACA,gBAAA,OAAA,CAAA,aAAA,GAAA,IAAA;;AANA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA,KAxJA;AAyJA;AACA,IAAA,SA1JA,qBA0JA,GA1JA,EA0JA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,KAAA,GAAA,YAAA;AACA,gBAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,gBAAA,OAAA,CAAA,IAAA,mCAAA,GAAA;AAHA;AAAA,uBAIA,OAAA,CAAA,SAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,CAJA;;AAAA;AAKA,gBAAA,OAAA,CAAA,aAAA,GAAA,IAAA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,KAhKA;AAkKA;AACA,IAAA,UAnKA,sBAmKA,GAnKA,EAmKA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,OAAA,CAAA,KAAA,GAAA,YAAA;AACA,gBAAA,OAAA,CAAA,IAAA,mCAAA,GAAA;AAFA;AAAA,uBAGA,OAAA,CAAA,SAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,CAHA;;AAAA;AAIA,gBAAA,OAAA,CAAA,UAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,aAAA,GAAA,IAAA;;AALA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,KAzKA;;AA0KA;;;AAGA,IAAA,SA7KA,uBA6KA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,QAAA;AAAA,mGAAA,kBAAA,KAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA,iCACA,KADA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA,mCAGA,4BAAA,OAAA,CAAA,IAAA,CAHA;;AAAA;AAAA;AAGA,4BAAA,IAHA,uBAGA,IAHA;AAGA,4BAAA,IAHA,uBAGA,IAHA;;AAAA,kCAIA,IAAA,KAAA,MAJA;AAAA;AAAA;AAAA;;AAKA;AACA,4BAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;AACA,4BAAA,OAAA,CAAA,aAAA,GAAA,KAAA;AAPA;AAAA,mCAQA,OAAA,CAAA,OAAA,EARA;;AAAA;AASA;AACA,4BAAA,OAAA,CAAA,aAAA,GAAA,IAAA;AACA,4BAAA,OAAA,CAAA,aAAA,CAAA,QAAA,GAAA,EAAA,CAXA,CAYA;;AACA,4BAAA,OAAA,CAAA,SAAA,CAAA,YAAA;AACA,mCAAA,KAAA,CAAA,SAAA,EAAA,aAAA;AACA,6BAFA;;AAGA,4BAAA,OAAA,CAAA,OAAA,GAAA,EAAA,CAhBA,CAiBA;;AACA,4BAAA,OAAA,CAAA,OAAA,CAAA,EAAA,GAAA,CAAA;AACA,4BAAA,OAAA,CAAA,OAAA,CAAA,MAAA,GAAA,GAAA;AACA,4BAAA,OAAA,CAAA,OAAA,CAAA,IAAA,GAAA,IAAA,CAAA,KAAA;AACA,4BAAA,OAAA,CAAA,OAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;;AACA,gCAAA,IAAA,CAAA,GAAA,EAAA;AACA,8BAAA,OAAA,CAAA,OAAA,CAAA,GAAA,GAAA,IAAA,CAAA,GAAA;AACA;;AACA,4BAAA,OAAA,CAAA,OAAA,CAAA,IAAA,GAAA,IAAA,CAAA,IAAA;AACA,4BAAA,OAAA,CAAA,OAAA,CAAA,GAAA,GAAA,IAAA,CAAA,GAAA;AACA,4BAAA,OAAA,CAAA,OAAA,CAAA,EAAA,GAAA,IAAA,CAAA,EAAA;AACA,4BAAA,OAAA,CAAA,UAAA,GAAA,IAAA;AACA,4BAAA,OAAA,CAAA,gBAAA,GAAA,IAAA;;AA7BA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAgCA,4BAAA,OAAA,CAAA,GAAA;;AAhCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAA;;AAAA;AAAA;AAAA;AAAA,oBADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqCA,KAlNA;;AAmNA;;;AAGA,IAAA,SAtNA,uBAsNA;AACA,WAAA,gBAAA,GAAA,IAAA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,WAAA,OAAA,CAAA,KAAA,GAAA,KAAA,IAAA,CAAA,KAAA;AACA,WAAA,OAAA,CAAA,GAAA,GAAA,KAAA,IAAA,CAAA,GAAA;AACA,WAAA,OAAA,CAAA,IAAA,GAAA,KAAA,IAAA,CAAA,IAAA;AACA,WAAA,OAAA,CAAA,GAAA,GAAA,KAAA,IAAA,CAAA,GAAA;AACA,WAAA,OAAA,CAAA,MAAA,GAAA,GAAA;AACA,WAAA,OAAA,CAAA,EAAA,GAAA,CAAA;AACA,WAAA,OAAA,CAAA,IAAA,GAAA,KAAA,IAAA,CAAA,KAAA;AACA,KAhOA;AAiOA,IAAA,OAjOA,qBAiOA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,OAAA,CAAA,KAAA,CAAA,MAAA,EAAA,QAAA;AAAA,oGAAA,mBAAA,KAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA,iCACA,KADA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA,mCAGA,4BAAA,OAAA,CAAA,IAAA,CAHA;;AAAA;AAAA;AAGA,4BAAA,IAHA,wBAGA,IAHA;;AAAA,kCAIA,IAAA,KAAA,MAJA;AAAA;AAAA;AAAA;;AAKA,4BAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA,EALA,CAMA;;;AACA,4BAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;AACA,4BAAA,OAAA,CAAA,aAAA,GAAA,KAAA;AARA;AAAA,mCASA,OAAA,CAAA,OAAA,EATA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAYA,4BAAA,OAAA,CAAA,GAAA;;AAZA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAA;;AAAA;AAAA;AAAA;AAAA,oBADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA,KAlPA;AAmPA;AACA,IAAA,SApPA,qBAoPA,GApPA,EAoPA;AAAA;;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAKA,IALA,CAKA,YAAA;AACA,8BAAA,CAAA,OAAA,CAAA,IAAA,CAAA,KAAA,CAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,cAAA,IAAA,SAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,OAAA,CAAA,OAAA;AACA,WANA,MAMA;AACA,YAAA,OAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAIA;AACA,SAbA;AAcA,OApBA,EAqBA,KArBA,CAqBA,YAAA,CAAA,CArBA;AAsBA,KA5QA;AA6QA;AACA,IAAA,KA9QA,mBA8QA;AACA,WAAA,gBAAA,GAAA,KAAA;AACA,WAAA,gBAAA,GAAA,KAAA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,KAlRA;AAmRA;AACA,IAAA,YApRA,wBAoRA,SApRA,EAoRA;AACA;AACA;AACA;AACA,WAAA,UAAA,GAAA,SAAA;AACA,KAzRA;AA0RA;AACA,IAAA,YA3RA,0BA2RA;AACA,UAAA,GAAA,GAAA;AACA,QAAA,EAAA,EAAA,EADA;AAEA,QAAA,IAAA,EAAA,EAFA;AAGA,QAAA,IAAA,EAAA,EAHA;AAIA,QAAA,IAAA,EAAA;AAJA,OAAA;AAMA,WAAA,aAAA,CAAA,QAAA,CAAA,IAAA,CAAA,GAAA;AACA,WAAA,aAAA,CAAA,GAAA,GAAA,GAAA;AACA,WAAA,OAAA,CAAA,IAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,WAAA,OAAA,CAAA,OAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,KAtSA;AAuSA;AACA,IAAA,YAxSA,wBAwSA,GAxSA,EAwSA;AACA,UAAA,GAAA,CAAA,KAAA,EAAA;AACA,aAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA,KAAA;AACA;;AACA,WAAA,aAAA,CAAA,QAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA,CACA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,IAAA,KAAA,GAAA,CAAA,IAAA;AAAA,OADA,CAAA;AAGA,WAAA,OAAA,CAAA,IAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,WAAA,OAAA,CAAA,OAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,KAjTA;AAkTA;AACA,IAAA,QAnTA,sBAmTA;AACA,WAAA,gBAAA,GAAA,KAAA;AACA,WAAA,gBAAA,GAAA,KAAA;AACA,KAtTA;AAwTA;AACA,IAAA,UAzTA,wBAyTA;AAAA;;AACA,WAAA,OAAA,CAAA,QAAA,GAAA,KAAA,aAAA,CAAA,QAAA;AACA,WAAA,OAAA,CAAA,SAAA,GAAA,KAAA,GAAA;AACA,UAAA,UAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,IAAA,CAAA,UAAA,IAAA;AAAA,eAAA,CAAA,IAAA,CAAA,IAAA;AAAA,OAAA,CAAA;;AACA,UAAA,UAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,cAAA;AACA;;AACA,WAAA,KAAA,CAAA,SAAA,EAAA,QAAA;AAAA,0FAAA,mBAAA,KAAA;AAAA;AAAA;AAAA;AAAA;AACA,sBAAA,KAAA,IAAA,CAAA,UAAA,EAAA;AACA,oBAAA,OAAA,CAAA,GAAA,CAAA,OAAA,CAAA,OAAA,EAAA,WAAA;AACA,mDAAA,OAAA,CAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,0BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,wBAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,wBAAA,OAAA,CAAA,gBAAA,GAAA,KAAA,CAFA,CAGA;;AACA,wBAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,wBAAA,OAAA,CAAA,OAAA;AACA;AACA,qBARA;AASA;;AAZA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAAA;;AAAA;AAAA;AAAA;AAAA;AAcA,KA9UA;AAgVA;AACA,IAAA,SAjVA,uBAiVA;AAAA;;AACA,WAAA,OAAA,CAAA,QAAA,GAAA,KAAA,aAAA,CAAA,QAAA;AACA,WAAA,OAAA,CAAA,SAAA,GAAA,KAAA,GAAA;AACA,UAAA,UAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,IAAA,CAAA,UAAA,IAAA;AAAA,eAAA,CAAA,IAAA,CAAA,IAAA;AAAA,OAAA,CAAA;;AACA,UAAA,UAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,cAAA;AACA;;AACA,WAAA,KAAA,CAAA,SAAA,EAAA,QAAA;AAAA,0FAAA,mBAAA,KAAA;AAAA;AAAA;AAAA;AAAA;AACA,sBAAA,KAAA,IAAA,CAAA,UAAA,EAAA;AACA,mDAAA,OAAA,CAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,0BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA;AACA,wBAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,wBAAA,OAAA,CAAA,OAAA;;AACA,4BAAA,IAAA,GAAA,GAAA,CAAA,IAAA;AACA,wBAAA,OAAA,CAAA,gBAAA,GAAA,KAAA;AACA,wBAAA,OAAA,CAAA,WAAA,CAAA,SAAA,CAAA,IAAA,GAAA,IAAA;AACA,wBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA,CAAA,KAAA;AACA,wBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,UAAA;AACA,wBAAA,OAAA,CAAA,cAAA,CAAA,KAAA,GAAA,IAAA;AACA,wBAAA,OAAA,CAAA,WAAA,CAAA,WAAA,GAAA,IAAA;AACA,wBAAA,OAAA,CAAA,WAAA,CAAA,IAAA,GAAA,OAAA;AACA,wBAAA,OAAA,CAAA,WAAA,CAAA,EAAA,GAAA,IAAA,CAAA,GAAA;AACA,wBAAA,OAAA,CAAA,WAAA,CAAA,aAAA,GAAA,GAAA;AACA,wBAAA,OAAA,CAAA,WAAA,CAAA,SAAA,GACA,iCADA;AAEA,wBAAA,OAAA,CAAA,MAAA,GAAA,IAAA;AACA;AACA,qBAnBA;AAoBA;;AAtBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAAA;;AAAA;AAAA;AAAA;AAAA;AAwBA,KAhXA;AAkXA;AACA,IAAA,UAnXA,wBAmXA;AAAA;;AACA,WAAA,KAAA,CAAA,SAAA,EAAA,QAAA;AAAA,0FAAA,mBAAA,KAAA;AAAA;AAAA;AAAA;AAAA;AACA,sBAAA,KAAA,EAAA;AACA,mDAAA,OAAA,CAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,0BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,wBAAA,OAAA,CAAA,aAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA,CADA,CAEA;;AACA,wBAAA,OAAA,CAAA,UAAA;;AACA,wBAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,wBAAA,OAAA,CAAA,gBAAA,GAAA,KAAA,CALA,CAMA;;AACA,wBAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,wBAAA,OAAA,CAAA,OAAA;AACA;AACA,qBAXA;AAYA;;AAdA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAAA;;AAAA;AAAA;AAAA;AAAA;AAgBA,KApYA;;AAqYA;;;AAGA;AACA,IAAA,YAzYA,0BAyYA;AACA,WAAA,KAAA,GAAA,QAAA;AACA,WAAA,gBAAA,GAAA,IAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,SAAA,GAAA,IAAA;AACA,WAAA,WAAA,CAAA,IAAA,GAAA,KAAA,IAAA,CAAA,KAAA;AACA,WAAA,WAAA;AACA,KAhZA;AAiZA,IAAA,QAjZA,oBAiZA,GAjZA,EAiZA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAEA,0BAAA;AAAA,kBAAA,KAAA,EAAA,GAAA,CAAA;AAAA,iBAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,sBAEA,IAFA;AAEA,gBAAA,IAFA,sBAEA,IAFA;;AAGA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,kBAAA,CAAA,QAAA,GAAA,IAAA;AACA;;AALA;AAAA;;AAAA;AAAA;AAAA;AAOA,gBAAA,OAAA,CAAA,GAAA;;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,KA1ZA;AA2ZA;AACA,IAAA,WA5ZA,uBA4ZA,MA5ZA,EA4ZA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,KADA,+DACA,MADA,GACA,OAAA,CAAA,WADA;AAEA,uCAAA,KAAA,EAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA,kBAAA,OAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,QAAA,CAAA,IAAA,CAAA,OAAA;AACA,kBAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,QAAA,CAAA,IAAA,CAAA,KAAA;AACA,iBAHA;;AAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,KAlaA;AAmaA;AACA,IAAA,UApaA,wBAoaA;AACA,WAAA,WAAA,GAAA,EAAA;AACA,WAAA,WAAA;AACA,KAvaA;AAwaA;AACA,IAAA,UAzaA,wBAyaA;AACA,WAAA,aAAA,CAAA,QAAA,GAAA,KAAA,kBAAA,CAAA,QAAA;AACA,WAAA,aAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,CAAA,EAAA;AACA,QAAA,CAAA,CAAA,IAAA,GAAA,qBAAA;AACA,OAFA;AAGA,WAAA,OAAA,CAAA,IAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,WAAA,OAAA,CAAA,OAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,WAAA,gBAAA,GAAA,KAAA;AACA,KAjbA;;AAkbA;;;AAGA;AACA,IAAA,WAtbA,yBAsbA;AACA,WAAA,KAAA,GAAA,QAAA;AACA,WAAA,gBAAA,GAAA,IAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,SAAA,GAAA,IAAA;AACA,WAAA,cAAA,CAAA,KAAA,GAAA,KAAA,IAAA,CAAA,KAAA;AACA,WAAA,cAAA;AACA,KA7bA;AA8bA;AACA,IAAA,WA/bA,uBA+bA,GA/bA,EA+bA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAEA,0BAAA,GAAA,CAAA,KAAA,CAFA;;AAAA;AAAA;AAEA,gBAAA,IAFA,sBAEA,IAFA;AAEA,gBAAA,IAFA,sBAEA,IAFA;;AAGA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,OAAA,CAAA,kBAAA,CAAA,QAAA,GAAA,IAAA;AACA;;AALA;AAAA;;AAAA;AAAA;AAAA;AAOA,gBAAA,OAAA,CAAA,GAAA;;AAPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,KAxcA;AAycA;AACA,IAAA,UA1cA,wBA0cA;AACA,WAAA,aAAA,CAAA,QAAA,GAAA,KAAA,kBAAA,CAAA,QAAA;AACA,WAAA,OAAA,CAAA,IAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,WAAA,OAAA,CAAA,OAAA,GAAA,KAAA,aAAA,CAAA,QAAA,CAAA,MAAA;AACA,WAAA,aAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,CAAA,EAAA;AACA,QAAA,CAAA,CAAA,IAAA,GAAA,KAAA;AACA,QAAA,CAAA,CAAA,IAAA,GAAA,qBAAA;AACA,OAHA;AAIA,WAAA,gBAAA,GAAA,KAAA;AACA,KAndA;AAodA;AACA,IAAA,cArdA,4BAqdA;AAAA;;AACA,+BAAA,KAAA,cAAA,EAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA,QAAA,OAAA,CAAA,gBAAA,CAAA,QAAA,GAAA,QAAA,CAAA,IAAA,CAAA,OAAA;AACA,OAFA;AAGA,KAzdA;AA0dA;AACA,IAAA,aA3dA,2BA2dA;AACA,WAAA,cAAA,GAAA;AACA,QAAA,MAAA,EAAA,GADA;AAEA,QAAA,EAAA,EAAA;AAFA,OAAA;AAIA,WAAA,cAAA;AACA,KAjeA;;AAkeA;;;AAGA,IAAA,gBAreA,4BAqeA,GAreA,EAqeA;AAAA;;AACA,sCAAA;AAAA,QAAA,MAAA,EAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,iBAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KAzeA;AA0eA;AACA,IAAA,WA3eA,uBA2eA,GA3eA,EA2eA,UA3eA,EA2eA;AAAA;;AACA,UAAA,GAAA,CAAA,KAAA,KAAA,KAAA,EAAA;AACA,wCAAA;AAAA,UAAA,MAAA,EAAA,GAAA,CAAA,KAAA,CAAA,QAAA;AAAA,SAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,UAAA,OAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,gBAAA,IAAA,CAAA,KAAA,KAAA,OAAA,EAAA;AACA,cAAA,OAAA,CAAA,IAAA,CAAA,UAAA,EAAA,OAAA,EAAA,EAAA;;AACA,qBAAA,IAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;AACA;AACA,WALA;AAMA,SAPA;AAQA;AACA,KAtfA;AAufA,IAAA,WAvfA,yBAufA;AACA,WAAA,MAAA,GAAA;AACA;AACA,QAAA,EAAA,EAAA;AAFA,OAAA;AAIA,KA5fA;AA6fA,IAAA,UA7fA,sBA6fA,IA7fA,EA6fA;AACA,UAAA,4BAAA,GAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,SAAA,CAAA,KAAA,mBAAA,CAAA,CAAA;AACA,MAAA,4BAAA,CAAA,IAAA,CAAA;AAAA,QAAA,KAAA,EAAA,SAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAAA;AACA,MAAA,4BAAA,CAAA,IAAA,CAAA;AAAA,QAAA,KAAA,EAAA,SAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAAA;;AACA,UAAA,IAAA,EAAA;AACA,YAAA,MAAA,GAAA,4BAAA,CAAA,MAAA,CAAA,UAAA,CAAA;AAAA,iBAAA,CAAA,CAAA,KAAA,KAAA,IAAA;AAAA,SAAA,CAAA;;AACA,YAAA,MAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,iBAAA,MAAA,CAAA,CAAA,CAAA,CAAA,KAAA;AACA,SAFA,MAEA;AACA,iBAAA,EAAA;AACA;AACA,OAPA,MAOA;AACA,eAAA,EAAA;AACA;AACA,KA3gBA;AA4gBA,IAAA,aA5gBA,yBA4gBA,aA5gBA,EA4gBA,MA5gBA,EA4gBA;AACA,aAAA,yBAAA;AACA,QAAA,aAAA,EAAA,aADA;AAEA,QAAA,MAAA,EAAA,MAFA;AAGA,QAAA,QAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,UAAA,GAAA,EAAA;AACA,eAAA,GAAA,CAAA,IAAA;AACA,OANA,CAAA;AAOA,KAphBA;AAqhBA,IAAA,cArhBA,0BAqhBA,GArhBA,EAqhBA;AACA,UAAA,GAAA,EAAA;AACA,YAAA,MAAA,GAAA,KAAA,YAAA,CAAA,MAAA,CAAA,UAAA,CAAA;AAAA,iBAAA,CAAA,CAAA,QAAA,KAAA,GAAA;AAAA,SAAA,CAAA;;AACA,YAAA,MAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,iBAAA,MAAA,CAAA,CAAA,CAAA,CAAA,QAAA;AACA,SAFA,MAEA;AACA,iBAAA,EAAA;AACA;AACA,OAPA,MAOA;AACA,eAAA,EAAA;AACA;AACA,KAhiBA;AAkiBA,IAAA,SAliBA,qBAkiBA,GAliBA,EAkiBA;AACA,UAAA,GAAA,EAAA;AACA,YAAA,MAAA,GAAA,KAAA,UAAA,CAAA,MAAA,CAAA,UAAA,CAAA;AAAA,iBAAA,CAAA,CAAA,QAAA,KAAA,GAAA;AAAA,SAAA,CAAA;;AACA,YAAA,MAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,iBAAA,MAAA,CAAA,CAAA,CAAA,CAAA,QAAA;AACA,SAFA,MAEA;AACA,iBAAA,EAAA;AACA;AACA,OAPA,MAOA;AACA,eAAA,EAAA;AACA;AACA,KA7iBA;AA8iBA;AACA,IAAA,UA/iBA,wBA+iBA;AACA,UAAA,MAAA,GAAA;AACA,QAAA,IAAA,EAAA,KAAA,MADA;AAEA,QAAA,GAAA,EAAA;AAFA,OAAA;AAIA,UAAA,QAAA,GAAA,YAAA;;AACA,UAAA,CAAA,KAAA,UAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,QAAA,MAAA,CAAA,IAAA,GAAA,KAAA,MAAA;AACA,0CAAA,MAAA,EAAA,QAAA;AACA,OAHA,MAGA;AACA,QAAA,MAAA,CAAA,IAAA,GAAA,KAAA,UAAA;AACA,6CAAA,MAAA,EAAA,QAAA;AACA;AACA,KA5jBA;AA6jBA,IAAA,cA7jBA,0BA6jBA,GA7jBA,EA6jBA;AACA,UAAA,GAAA,EAAA;AACA,aAAA,IAAA,CAAA,KAAA,IAAA,EAAA,MAAA,EAAA,IAAA;AACA,aAAA,IAAA,CAAA,KAAA,IAAA,EAAA,KAAA,EAAA,EAAA;AACA,aAAA,IAAA,CAAA,KAAA,IAAA,EAAA,QAAA,EAAA,EAAA;AACA,OAJA,MAIA;AACA,aAAA,IAAA,CAAA,KAAA,IAAA,EAAA,MAAA,EAAA,IAAA;AACA,aAAA,IAAA,CAAA,KAAA,IAAA,EAAA,KAAA,EAAA,EAAA;AACA,aAAA,IAAA,CAAA,KAAA,IAAA,EAAA,KAAA,EAAA,EAAA;AACA;AACA;AAvkBA;AAxRA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n      @handleEvent=\"handleEvent\"\n      @handleReset=\"filterReset\"\n    />\n    <div>\n      <el-white class=\"button-group\">\n        <el-row :gutter=\"3\" class=\"mb8 pull-right\">\n          <el-col\n            style=\"\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n          \"\n          >\n            <div>\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-plus\"\n                @click=\"getInster\"\n                v-hasPermi=\"['dzczml:button:add']\"\n                >新增\n              </el-button>\n              <!-- <el-button type=\"primary\" @click=\"exportFun\"\n              >导出\n              </el-button> -->\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-download\"\n                @click=\"exportWord\"\n                >导出</el-button\n              >\n            </div>\n          </el-col>\n        </el-row>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"selectChange\"\n          height=\"66vh\"\n          v-loading=\"loading\"\n        >\n          <el-table-column\n            slot=\"table_eight\"\n            align=\"center\"\n            fixed=\"right\"\n            style=\"display: block\"\n            label=\"操作\"\n            width=\"160\"\n            :resizable=\"false\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                @click=\"getDetails(scope.row)\"\n                type=\"text\"\n                size=\"small\"\n                title=\"详情\"\n                class=\"el-icon-view\"\n              ></el-button>\n              <el-button\n                @click=\"getUpdate(scope.row)\"\n                v-if=\"(scope.row.createBy === currentUser || hasSuperRole) && !scope.row.czp\"\n                type=\"text\"\n                size=\"small\"\n                title=\"编辑\"\n                class=\"el-icon-edit\"\n              >\n              </el-button>\n              <el-button\n                @click=\"deleteRow(scope.row)\"\n                v-if=\"\n                  (scope.row.createBy === currentUser ||\n                    hasSuperRole) &&\n                    !scope.row.czp\n                \"\n                type=\"text\"\n                size=\"small\"\n                title=\"删除\"\n                class=\"el-icon-delete\"\n              >\n              </el-button>\n            </template>\n          </el-table-column>\n        </comp-table>\n      </el-white>\n    </div>\n\n    <!-- 详情/新增/修改 操作命令页-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isShowDetails\"\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <div>\n          <el-row style=\"display: flex;flex-wrap: wrap;\">\n            <el-col :span=\"8\">\n              <el-form-item label=\"令类型：\" prop=\"zslOrYbl\">\n                <el-switch\n                  :disabled=\"isDisabled\"\n                  style=\"width: 100%\"\n                  v-model=\"form.zslOrYbl\"\n                  active-color=\"#13ce66\"\n                  inactive-color=\"#ff4949\"\n                  active-text=\"正式令\"\n                  inactive-text=\"预备令\"\n                  @change=\"changeZslOrYbl\"\n                >\n                </el-switch>\n              </el-form-item>\n            </el-col>\n            <!--            <el-col :span=\"8\">-->\n            <!--              <el-form-item label=\"编号：\" prop=\"bh\">-->\n            <!--                <el-input v-model=\"form.bh\" disabled placeholder=\"确认后编号自动生成\"/>-->\n            <!--              </el-form-item>-->\n            <!--            </el-col>-->\n            <el-col :span=\"8\">\n              <el-form-item label=\"分公司：\" prop=\"fgs\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.fgs\"\n                  @change=\"handleFgsChange\"\n                  disabled\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in ssgsOptionsDataList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"光伏电站：\" prop=\"bdzmc\">\n                <el-select\n                  style=\"width: 100%\"\n                  filterable\n                  clearable\n                  v-model=\"form.bdzmc\"\n                  ref=\"bdzmc\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择光伏电站\"\n                >\n                  <el-option\n                    v-for=\"item in wzDataListOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"!form.zslOrYbl\"\n                label=\"通知时间：\"\n                prop=\"tzsj\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: false }]\n                    : [{ required: true, message: '不能为空' }]\n                \"\n              >\n                <el-date-picker\n                  v-model=\"form.tzsj\"\n                  :disabled=\"isDisabled\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"!form.zslOrYbl\"\n                label=\"通知人：\"\n                prop=\"tzr\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: false }]\n                    : [{ required: true, message: '不能为空' }]\n                \"\n              >\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.tzr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in tzrOrXlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"!form.zslOrYbl\"\n                label=\"预备令接令人：\"\n                prop=\"ybljlr\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: false }]\n                    : [{ required: true, message: '不能为空' }]\n                \"\n              >\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.ybljlr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in jlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"form.zslOrYbl\"\n                label=\"下令时间：\"\n                prop=\"xlsj\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-date-picker\n                  v-model=\"form.xlsj\"\n                  :disabled=\"isDisabled\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"form.zslOrYbl\"\n                label=\"下令人：\"\n                prop=\"xlr\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.xlr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in tzrOrXlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item\n                v-if=\"form.zslOrYbl\"\n                label=\"接令人：\"\n                prop=\"jlr\"\n                :rules=\"\n                  form.zslOrYbl\n                    ? [{ required: true, message: '不能为空' }]\n                    : [{ required: false }]\n                \"\n              >\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"form.jlr\"\n                  :disabled=\"isDisabled\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in jlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"回令时间：\" prop=\"hlsj\">\n                <el-date-picker\n                  v-model=\"form.hlsj\"\n                  :disabled=\"isDisabled\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  :placeholder=\"isDisabled ? '' : '选择日期时间'\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-form-item label=\"操作任务：\" prop=\"czrw\">\n            <el-input\n              type=\"textarea\"\n              v-model=\"form.czrw\"\n              :disabled=\"isDisabled\"\n              placeholder=\"请输入操作任务\"\n              :rows=\"3\"\n            />\n          </el-form-item>\n        </div>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">关 闭</el-button>\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"saveRow\"\n          >保 存</el-button\n        >\n        <el-button v-if=\"!isDisabled\" type=\"primary\" @click=\"createCzp\"\n          >开操作票\n        </el-button>\n        <el-button\n          v-if=\"isDisabled && (form.createBy === currentUser || hasSuperRole)&& !form.czp\"\n          type=\"primary\"\n          @click=\"createBjp\"\n          >开办结票\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!--操作票开票页-->\n    <el-dialog\n      title=\"光伏倒闸操作票开票\"\n      :visible.sync=\"isCzpShowDetails\"\n      append-to-body\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form\n        label-width=\"120px\"\n        ref=\"formCzp\"\n        :model=\"formCzp\"\n        :rules=\"czpRules\"\n      >\n        <div>\n          <el-row>\n            <!--            <el-col :span=\"8\">-->\n            <!--              <el-form-item label=\"编号：\" prop=\"bm\">-->\n            <!--                <el-input v-model=\"formCzp.bm\" disabled placeholder=\"确认后编号自动生成\"/>-->\n            <!--              </el-form-item>-->\n            <!--            </el-col>-->\n            <el-col :span=\"8\">\n              <el-form-item label=\"分公司：\" prop=\"fgs\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formCzp.fgs\"\n                  @change=\"handleFgsChange\"\n                  disabled\n                  placeholder=\"请选择分公司\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in ssgsOptionsDataList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"光伏电站：\" prop=\"bdzmc\">\n                <el-select\n                  style=\"width: 100%\"\n                  filterable\n                  v-model=\"formCzp.bdzmc\"\n                  disabled\n                  placeholder=\"请输入内容\"\n                >\n                  <el-option\n                    v-for=\"item in wzDataListOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作开始时间：\" prop=\"kssj\">\n                <el-date-picker\n                  disabled\n                  v-model=\"formCzp.kssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作结束时间：\" prop=\"jssj\">\n                <el-date-picker\n                  disabled\n                  v-model=\"formCzp.jssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作人：\" prop=\"czr\">\n                <el-input\n                  v-model=\"formCzp.czr\"\n                  :disabled=\"isDisabledCzp\"\n                  placeholder=\"请输入内容\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"监护人：\" prop=\"jhr\">\n                <el-input\n                  v-model=\"formCzp.jhr\"\n                  :disabled=\"isDisabledCzp\"\n                  placeholder=\"请输入内容\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"下令人：\" prop=\"xlr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formCzp.xlr\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                  disabled\n                >\n                  <el-option\n                    v-for=\"item in tzrOrXlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作项数：\" prop=\"czxs\">\n                <el-input-number\n                  v-model=\"formCzp.czxs\"\n                  disabled\n                  placeholder=\"请输入操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"已执行项数：\" prop=\"yzxczxs\">\n                <el-input-number\n                  v-model=\"formCzp.yzxczxs\"\n                  disabled\n                  placeholder=\"请输入已执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"未执行项数：\" prop=\"wzxczxs\">\n                <el-input-number\n                  v-model=\"formCzp.wzxczxs\"\n                  disabled\n                  placeholder=\"请输入未执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"操作任务：\" prop=\"czrw\">\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"formCzp.czrw\"\n                  :disabled=\"isDisabledCzp\"\n                  placeholder=\"请输入内容\"\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"上传图片：\">\n                <el-upload\n                  :disabled=\"isDisabledCzp\"\n                  action=\"\"\n                  ref=\"uploadImg\"\n                  accept=\"image/jpeg,image/jpg,image/png\"\n                  :headers=\"header\"\n                  :multiple=\"true\"\n                  :on-change=\"handleChange\"\n                  :data=\"uploadImgData\"\n                  :file-list=\"imgList\"\n                  :auto-upload=\"false\"\n                  :on-preview=\"handlePictureCardPreview\"\n                  :on-progress=\"handleProgress\"\n                  :on-remove=\"handleRemove\"\n                >\n                  <el-button type=\"primary\" size=\"small\">选择文件</el-button>\n                </el-upload>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n\n        <div>\n          <el-dialog :title=\"titleyl\" :visible.sync=\"yl\" append-to-body>\n            <!--第一个表格-->\n            <div style=\"margin-bottom:10px;\">\n              <el-row>\n                <el-col :span=\"8\">\n                  <el-input\n                    v-model=\"replaceData.oldStr\"\n                    style=\"width:80%\"\n                    placeholder=\"查找字符串\"\n                  >\n                  </el-input>\n                </el-col>\n\n                <el-col :span=\"8\">\n                  <!-- <span> ---</span> -->\n                  <el-input\n                    v-model=\"replaceData.newStr\"\n                    style=\"width:80%\"\n                    placeholder=\"替换后字符串\"\n                  >\n                  </el-input>\n                </el-col>\n                <el-col :span=\"4\">\n                  <el-button\n                    type=\"primary\"\n                    icon=\"el-icon-edit\"\n                    @click=\"replaceStr\"\n                    >批量替换</el-button\n                  >\n                </el-col>\n              </el-row>\n            </div>\n\n            <el-table\n              :data=\"propTableData.colFirst\"\n              ref=\"propTable\"\n              disabled\n              border\n              stripe\n              style=\"width: 100%\"\n              :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n            >\n              <el-table-column\n                align=\"center\"\n                width=\"120\"\n                prop=\"xh\"\n                label=\"顺序号\"\n                sortable\n              >\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"请输入顺序号\"\n                      disabled\n                      v-model=\"scope.row.xh\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n                <template slot-scope=\"scope\">\n                  <span>\n                    <el-input\n                      placeholder=\"请输入操作项目\"\n                      disabled\n                      v-model=\"scope.row.czrw\"\n                    ></el-input>\n                  </span>\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-dialog>\n        </div>\n        <!--列表-->\n        <div>\n          <el-white class=\"mb8 pull-right\">\n            <el-button type=\"primary\" @click=\"getDxpkLists\">典型票库</el-button>\n            <el-button type=\"warning\" @click=\"getLspkList\">历史票库</el-button>\n            <el-button\n              type=\"info\"\n              @click=\"handleYlChange\"\n              style=\"text-align: right\"\n              >预览</el-button\n            >\n          </el-white>\n          <!--第一个表格-->\n          <el-table\n            :data=\"propTableData.colFirst\"\n            height=\"300\"\n            border\n            stripe\n            style=\"width: 100%\"\n            element-loading-text=\"正在获取数据\"\n            element-loading-spinner=\"el-icon-loading\"\n            v-loading=\"loading\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <el-table-column\n              align=\"center\"\n              width=\"120\"\n              prop=\"xh\"\n              label=\"顺序号\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input-number\n                    size=\"small\"\n                    v-model=\"scope.row.xh\"\n                    :min=\"1\"\n                    :precision=\"0\"\n                    controls-position=\"right\"\n                  ></el-input-number>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入操作项目\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" width=\"80\">\n              <template slot=\"header\" slot-scope=\"scope\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  icon=\"el-icon-plus\"\n                  @click=\"listFirstAdd(scope.$index, scope.row)\"\n                ></el-button>\n              </template>\n              <template slot-scope=\"scope\">\n                <el-button\n                  type=\"text\"\n                  icon=\"el-icon-delete\"\n                  @click=\"listFirstDel(scope.row)\"\n                ></el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </el-form>\n\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"closeCzp\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"saveRowCzp\">保 存</el-button>\n        <el-button type=\"primary\" @click=\"submitCzp\">上 报</el-button>\n      </div>\n    </el-dialog>\n\n    <!--开办结票-->\n    <el-dialog\n      title=\"开办结票\"\n      :visible.sync=\"isBjpShowDetails\"\n      append-to-body\n      width=\"60%\"\n      v-dialogDrag\n    >\n      <el-form\n        label-width=\"120px\"\n        ref=\"formBjp\"\n        :model=\"formBjp\"\n        :rules=\"rules2\"\n      >\n        <div>\n          <el-row>\n            <el-col :span=\"8\">\n              <el-form-item label=\"编号：\" prop=\"bm\">\n                <el-input\n                  v-model=\"formBjp.bm\"\n                  disabled\n                  placeholder=\"确认后编号自动生成\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"分公司：\" prop=\"fgs\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formBjp.fgs\"\n                  @change=\"handleFgsChange\"\n                  disabled\n                  placeholder=\"请选择分公司\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in ssgsOptionsDataList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"光伏电站：\" prop=\"bdzmc\">\n                <el-select\n                  style=\"width: 100%\"\n                  filterable\n                  v-model=\"formBjp.bdzmc\"\n                  placeholder=\"请输入内容\"\n                  disabled\n                >\n                  <el-option\n                    v-for=\"item in wzDataListOptions\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作开始时间：\" prop=\"kssj\">\n                <el-date-picker\n                  v-model=\"formBjp.kssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作结束时间：\" prop=\"jssj\">\n                <el-date-picker\n                  v-model=\"formBjp.jssj\"\n                  type=\"datetime\"\n                  format=\"yyyy-MM-dd HH:mm\"\n                  value-format=\"yyyy-MM-dd HH:mm\"\n                  placeholder=\"选择日期时间\"\n                  default-time=\"12:00:00\"\n                  style=\"width: 100%\"\n                >\n                </el-date-picker>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作人：\" prop=\"czr\">\n                <el-input v-model=\"formBjp.czr\" placeholder=\"请输入内容\" />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"监护人：\" prop=\"jhr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formBjp.jhr\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in jlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"下令人：\" prop=\"xlr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formBjp.xlr\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in tzrOrXlrList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"审票人：\" prop=\"bzspr\">\n                <el-select\n                  style=\"width: 100%\"\n                  v-model=\"formBjp.bzspr\"\n                  placeholder=\"请选择\"\n                  filterable\n                  clearable\n                >\n                  <el-option\n                    v-for=\"item in sprList\"\n                    :key=\"item.userName\"\n                    :label=\"item.nickName\"\n                    :value=\"item.userName\"\n                  >\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"操作项数：\" prop=\"czxs\">\n                <el-input-number\n                  v-model=\"formBjp.czxs\"\n                  placeholder=\"请输入操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"已执行项数：\" prop=\"yzxczxs\">\n                <el-input-number\n                  v-model=\"formBjp.yzxczxs\"\n                  placeholder=\"请输入已执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"8\">\n              <el-form-item label=\"未执行项数：\" prop=\"wzxczxs\">\n                <el-input-number\n                  v-model=\"formBjp.wzxczxs\"\n                  placeholder=\"请输入未执行操作项数\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"操作任务：\" prop=\"czrw\">\n                <el-input\n                  type=\"textarea\"\n                  v-model=\"formBjp.czrw\"\n                  placeholder=\"请输入内容\"\n                  disabled\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"上传图片：\">\n                <el-upload\n                  action=\"\"\n                  ref=\"uploadImg\"\n                  accept=\"image/jpeg,image/jpg,image/png\"\n                  :headers=\"header\"\n                  :multiple=\"true\"\n                  :on-change=\"handleChange\"\n                  :data=\"uploadImgData\"\n                  :file-list=\"imgList\"\n                  :auto-upload=\"false\"\n                  list-type=\"picture-card\"\n                  :on-preview=\"handlePictureCardPreview\"\n                  :on-progress=\"handleProgress\"\n                  :on-remove=\"handleRemove\"\n                >\n                  <i class=\"el-icon-plus\"></i>\n                </el-upload>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"closeCzp\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"saveRowBjp\">确 认</el-button>\n      </div>\n    </el-dialog>\n\n    <!--典型票库-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isDxpShowDetails\"\n      width=\"50%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"180px\" ref=\"form\" :model=\"form\">\n        <el-row>\n          <!--查询及列表-->\n          <el-col :span=\"24\">\n            <el-form\n              :model=\"queryParams\"\n              class=\"searchForm\"\n              ref=\"queryForm\"\n              label-width=\"100px\"\n              v-show=\"isShowSx\"\n            >\n              <el-row>\n                <el-col :span=\"12\">\n                  <el-form-item label=\"典型票名称：\" prop=\"dxpmc\">\n                    <el-input\n                      placeholder=\"请输入典型票名称\"\n                      v-model=\"queryParams.dxpmc\"\n                      clearable\n                    />\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"12\">\n                  <el-form-item label=\"操作任务：\" prop=\"czrw\">\n                    <el-input\n                      placeholder=\"请输入操作任务\"\n                      v-model=\"queryParams.czrw\"\n                      clearable\n                    />\n                  </el-form-item>\n                </el-col>\n                <div style=\"float: right;margin-bottom: 10px\">\n                  <el-button\n                    type=\"primary\"\n                    icon=\"el-icon-search\"\n                    @click=\"handleQuery\"\n                    >搜索</el-button\n                  >\n                  <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\"\n                    >重置</el-button\n                  >\n                </div>\n              </el-row>\n            </el-form>\n            <div style=\"float: left;margin-bottom: 10px\">\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-search\"\n                @click=\"isShowSx = isShowSx ? false : true\"\n                >筛 选</el-button\n              >\n            </div>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col :span=\"24\">\n            <!--主表信息-->\n            <comp-table\n              :table-and-page-info=\"propTableDataDxp\"\n              height=\"400\"\n              border\n              stripe\n              style=\"width: 100%\"\n              max-height=\"60vh\"\n              @getMethod=\"handleQuery\"\n              @rowClick=\"changeMx\"\n            >\n            </comp-table>\n          </el-col>\n        </el-row>\n        <!--子表信息-->\n        <el-row>\n          <el-table\n            :data=\"propTableDataDxpMx.colFirst\"\n            :disabled=\"isDisabled\"\n            height=\"300\"\n            border\n            stripe\n            style=\"width: 100%\"\n            max-height=\"40vh\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <!--子表列表-->\n            <el-table-column\n              align=\"center\"\n              prop=\"xh\"\n              label=\"序号\"\n              width=\"100\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"序号\"\n                    :disabled=\"isShowDxp\"\n                    v-model=\"scope.row.xh\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"操作项目\"\n                    :disabled=\"isShowDxp\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n          </el-table>\n        </el-row>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"saveRowDxp\">确 认 </el-button>\n      </div>\n    </el-dialog>\n\n    <!--历史票库-->\n    <el-dialog\n      :title=\"title\"\n      :visible.sync=\"isLspShowDetails\"\n      width=\"50%\"\n      v-dialogDrag\n    >\n      <el-form label-width=\"180px\" ref=\"form\" :model=\"form\">\n        <el-form\n          :model=\"queryParamsLsp\"\n          class=\"searchForm\"\n          ref=\"queryForm\"\n          label-width=\"100px\"\n          v-show=\"isShowSx\"\n        >\n          <el-row>\n            <el-col :span=\"12\">\n              <el-form-item label=\"操作时间\" prop=\"czsjArr\">\n                <el-date-picker\n                  type=\"daterange\"\n                  range-separator=\"-\"\n                  start-placeholder=\"开始时间\"\n                  end-placeholder=\"结束时间\"\n                  v-model=\"queryParamsLsp.czsjArr\"\n                  clearable\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <!--<el-col :span=\"12\">\n              <el-form-item label=\"操作结束时间\" prop=\"jssj\">\n                <el-input placeholder=\"请输入操作结束时间\" v-model=\"queryParamsLsp.jssj\" clearable primary style=\"width: 100%\"/>\n              </el-form-item>\n            </el-col>-->\n            <el-col :span=\"24\">\n              <el-form-item label=\"操作任务\" prop=\"czrw\">\n                <el-input\n                  type=\"textarea\"\n                  :rows=\"1\"\n                  placeholder=\"请输入操作任务\"\n                  v-model=\"queryParamsLsp.czrw\"\n                  clearable\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row>\n            <el-col :span=\"12\">\n              <el-form-item label=\"操作人\" prop=\"czr\">\n                <el-input\n                  placeholder=\"请输入操作人\"\n                  v-model=\"queryParamsLsp.czr\"\n                  clearable\n                  primary\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col>\n            <!-- <el-col :span=\"12\">\n              <el-form-item label=\"监护人\" prop=\"jhr\">\n                <el-input\n                  placeholder=\"请输入监护人\"\n                  v-model=\"queryParamsLsp.jhr\"\n                  clearable\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col> -->\n          </el-row>\n          <el-row>\n            <!-- <el-col :span=\"12\">\n              <el-form-item label=\"下令人\" prop=\"xlr\">\n                <el-input\n                  placeholder=\"请输入下令人\"\n                  v-model=\"queryParamsLsp.xlr\"\n                  clearable\n                  primary\n                  style=\"width: 100%\"\n                />\n              </el-form-item>\n            </el-col> -->\n            <div style=\"float: right;margin-bottom: 10px\">\n              <el-button\n                type=\"primary\"\n                icon=\"el-icon-search\"\n                @click=\"handleQueryLsp\"\n                >搜索</el-button\n              >\n              <el-button icon=\"el-icon-refresh\" @click=\"resetQueryLsp\"\n                >重置</el-button\n              >\n            </div>\n          </el-row>\n        </el-form>\n        <!--主表信息-->\n        <div>\n          <div style=\"float: left;margin-bottom: 10px\">\n            <el-button\n              type=\"primary\"\n              icon=\"el-icon-search\"\n              @click=\"isShowSx = isShowSx ? false : true\"\n              >筛 选</el-button\n            >\n          </div>\n          <el-table\n            @row-click=\"changeLspMx\"\n            :data=\"propTableDataLsp.colFirst\"\n            :disabled=\"isDisabled\"\n            height=\"300\"\n            border\n            stripe\n            style=\"width: 100%\"\n            max-height=\"40vh\"\n          >\n            <el-table-column\n              align=\"center\"\n              prop=\"gfzmc\"\n              label=\"光伏电站\"\n              width=\"170\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入光伏电站\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.gfzmc\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"kssj\"\n              label=\"操作开始时间\"\n              width=\"170\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入开始时间\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.kssj\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"jssj\"\n              label=\"操作结束时间\"\n              width=\"170\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入结束时间\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.jssj\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"czrw\"\n              label=\"操作任务\"\n              width=\"230\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    type=\"textarea\"\n                    :rows=\"2\"\n                    placeholder=\"请输入操作任务\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"czr\"\n              label=\"操作人\"\n              width=\"120\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.czr\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"jhrmc\"\n              label=\"监护人\"\n              width=\"120\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.jhrmc\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"xlr\"\n              label=\"下令人\"\n              width=\"120\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.xlrmc\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column\n              align=\"center\"\n              prop=\"spr\"\n              label=\"审票人\"\n              width=\"150\"\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.bzsprmc\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n        <!--子表信息-->\n        <div>\n          <el-table\n            :data=\"propTableDataLspMx.colFirst\"\n            :disabled=\"isDisabled\"\n            height=\"300\"\n            border\n            stripe\n            style=\"width: 100%\"\n            max-height=\"40vh\"\n            :default-sort=\"{ prop: 'xh', order: 'ascending' }\"\n          >\n            <!--子表列表-->\n            <el-table-column\n              align=\"center\"\n              width=\"120\"\n              prop=\"xh\"\n              label=\"顺序号\"\n              sortable\n            >\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入顺序号\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.xh\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n            <el-table-column align=\"center\" prop=\"czrw\" label=\"操作项目\">\n              <template slot-scope=\"scope\">\n                <span>\n                  <el-input\n                    placeholder=\"请输入操作项目\"\n                    :disabled=\"isShowLsp\"\n                    v-model=\"scope.row.czrw\"\n                  ></el-input>\n                </span>\n              </template>\n            </el-table-column>\n          </el-table>\n        </div>\n      </el-form>\n      <div align=\"right\" slot=\"footer\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"saveRowLsp\">确 认 </el-button>\n      </div>\n    </el-dialog>\n\n    <!--图片放大展示弹出框-->\n    <el-dialog title=\"放大\" :visible.sync=\"imgDialogVisible\" v-dialogDrag>\n      <img width=\"100%\" :src=\"dialogImageUrl\" alt=\"\" />\n    </el-dialog>\n\n    <!--  工作流  -->\n    <activiti\n      ref=\"activiti\"\n      :processData=\"processData\"\n      :isShow=\"isShow\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n    ></activiti>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport api from \"@/utils/request\";\nimport {\n  exportExcel,\n  getBdzSelectList,\n  getList,\n  remove,\n  saveOrUpdate,\n  saveOrUpdateCzp,\n  exportWordByselection,\n  exportWordByparams\n} from \"@/api/yxgl/gfyxgl/gfdzczml\";\nimport { getDxpkList, getLists } from \"@/api/bzgl/gfdxczp\";\nimport { getListLsp, getLspkList, updateById } from \"@/api/yxgl/gfyxgl/gfdzczp\";\nimport ElectronicAuthDialog from \"com/ElectronicAuthDialog\";\nimport CompTable from \"com/CompTable\";\nimport ElFilter from \"com/ElFilter\";\nimport { getUsers } from \"@/api/system/usergroup\";\nimport activiti from \"com/activiti_czp\";\nimport { getUUID } from \"@/utils/ruoyi\";\nimport { getFgsOptions } from \"@/api/yxgl/gfyxgl/gfzbgl\";\n\nexport default {\n  name: \"dzczml\",\n  components: { ElectronicAuthDialog, CompTable, ElFilter, activiti },\n  data() {\n    return {\n      hasSuperRole:this.$store.getters.hasSuperRole,\n      //loading:false,\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"czpsh\",\n        businessKey: \"\",\n        businessType: \"倒闸操作票\",\n        variables: {},\n        defaultFrom: true,\n        nextUser: \"\",\n        processType: \"complete\"\n      },\n      isShow: false,\n      //弹出框标题\n      activitiOption: { title: \"上报\" },\n      czpRules: {\n        // xlr: [\n        //   {required: true, message: '不能为空', trigger: 'blur'}\n        // ],\n      },\n      rules: {\n        bdzmc: [{ required: true, message: \"不能为空\", trigger: \"select\" }],\n        fgs: [{ required: true, message: \"不能为空\", trigger: \"select\" }],\n        czrw: [{ required: true, message: \"不能为空\", trigger: \"blur\" }]\n      },\n      rules2: {\n        kssj: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        jssj: [{ required: true, message: \"不能为空\", trigger: \"select\" }],\n        czr: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        jhr: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        xlr: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        czxs: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        yzxczxs: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        wzxczxs: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        bzspr: [{ required: true, message: \"不能为空\", trigger: \"blur\" }],\n        czrw: [{ required: true, message: \"不能为空\", trigger: \"blur\" }]\n      },\n      currentUser: this.$store.getters.name,\n      yl: false,\n      replaceData: {},\n      titleyl: \"\",\n      //图片地址url\n      dialogImageUrl: \"\",\n      //展示图片dialog控制\n      imgDialogVisible: false,\n      //上传图片时的携带的其他参数\n      uploadImgData: {\n        businessId: \"\" //携带的表单主键id\n      },\n      //上传图片时的请求头\n      header: {},\n      //图片list\n      imgList: [],\n      isShowSx: false,\n      bdzList: [],\n      //弹出框内新增时下拉框所属位置数据\n      wzDataListOptions: [],\n      isShowDxp: false,\n      isShowLsp: false,\n      loading: false,\n      isDisabledCzp: false,\n      isDxpShowDetails: false,\n      isLspShowDetails: false,\n      //操作票弹框是否显示\n      isCzpShowDetails: false,\n      isBjpShowDetails: false,\n      // 多选框选中的id\n      ids: [],\n      selectData: [],\n      //倒闸操作票命令\n      params: {\n        //光伏\n        lx: 2\n      },\n      //典型票查询条件\n      queryParams: {},\n      //历史票查询条件\n      queryParamsLsp: {\n        status: \"4\",\n        lx: 4\n      },\n      //弹出框中表格数据\n      propTableData: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //弹出框中表格数据\n      propTableDataDxpMx: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //弹出框中表格数据\n      propTableDataDxp: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 1,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"光伏电站\", prop: \"sbmcms\", minWidth: \"200\" },\n          { label: \"典型操作票名称\", prop: \"dxpmc\", minWidth: \"200\" },\n          { label: \"操作任务\", prop: \"czrw\", minWidth: \"200\" }\n        ],\n        option: { checkBox: false, serialNumber: true },\n        sel: null // 选中行\n      },\n      //弹出框中表格数据\n      propTableDataLspMx: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //弹出框中表格数据\n      propTableDataLsp: {\n        sel: null, // 选中行\n        colFirst: []\n      },\n      //form表单\n      form: {\n        bh: \"\",\n        bdzmc: \"\",\n        tzsj: \"\",\n        tzr: \"\",\n        xlsj: \"\",\n        xlr: \"\",\n        czrw: \"\",\n        jlr: \"\",\n        hlsj: \"\",\n        status: \"\"\n      },\n      // 操作票\n      formCzp: {\n        bh: \"\",\n        bdzmc: \"\",\n        kssj: \"\",\n        jssj: \"\",\n        czrw: \"\",\n        czr: \"\",\n        jhr: \"\",\n        xlr: \"\",\n        spr: \"\",\n        lx: 2, //变电\n        colFirst: [],\n        status: \"\"\n      },\n      // 办结票\n      formBjp: {\n        bh: \"\",\n        bdzmc: \"\",\n        kssj: \"\",\n        jssj: \"\",\n        czrw: \"\",\n        czr: \"\",\n        jhr: \"\",\n        xlr: \"\",\n        spr: \"\",\n        lx: 2, //变电\n        colFirst: [],\n        status: \"\"\n      },\n\n      //所属分公司下拉框数据\n      ssgsOptionsDataList: [],\n\n      //详情弹框是否显示\n      isShowDetails: false,\n      //是否禁用\n      isDisabled: false,\n      //标题\n      title: \"\",\n      filterInfo: {\n        data: {\n          bh: \"\",\n          bdzmc: \"\",\n          tzsjArr: \"\",\n          tzr: \"\",\n          xlsjArr: \"\",\n          xlr: \"\",\n          czrw: \"\",\n          jlr: \"\",\n          hlsjArr: \"\"\n        }, //查询条件\n        fieldList: [\n          // {\n          //   label: '令',\n          //   type: 'switch',\n          //   value: 'yblOrZsl',\n          //   textStart:'预备令',\n          //   textEnd:'正式令'\n          // },\n          // {label: '编号', value: 'bh', type: 'input', clearable: true},\n          // /*{ label: '状态', value: 'status', type: 'select', clearable: true },*/\n          {\n            label: \"分公司\",\n            value: \"fgs\",\n            type: \"select\",\n            options: [],\n            clearable: true\n          },\n          {\n            label: \"光伏电站\",\n            value: \"bdzmc\",\n            type: \"select\",\n            options: [],\n            clearable: true,\n            filterable: true\n          },\n          {\n            label: \"通知时间\",\n            value: \"tzsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"通知人\", value: \"tzr\", type: \"input\", clearable: true },\n          {\n            label: \"下令时间\",\n            value: \"xlsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"下令人\", value: \"xlr\", type: \"input\", clearable: true },\n          { label: \"操作任务\", value: \"czrw\", type: \"input\", clearable: true },\n          { label: \"接令人\", value: \"jlr\", type: \"input\", clearable: true },\n          {\n            label: \"回令时间\",\n            value: \"hlsjArr\",\n            type: \"date\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          /* { label: '状态', prop: 'status', minWidth: '70' },*/\n          { label: \"分公司\", prop: \"fgsmc\", minWidth: \"120\" },\n          { label: \"光伏电站\", prop: \"bdzmcs\", minWidth: \"120\" },\n          { label: \"通知人\", prop: \"tzrxm\", minWidth: \"80\" },\n          { label: \"通知时间\", prop: \"tzsj\", minWidth: \"140\" },\n          { label: \"下令人\", prop: \"xlrmc\", minWidth: \"80\" },\n          { label: \"下令时间\", prop: \"xlsj\", minWidth: \"140\" },\n          { label: \"操作任务\", prop: \"czrw\", minWidth: \"160\" },\n          { label: \"接令人\", prop: \"jlrxm\", minWidth: \"80\" },\n          { label: \"预备令接令人\", prop: \"ybljlrxm\", minWidth: \"100\" },\n          { label: \"回令时间\", prop: \"hlsj\", minWidth: \"140\" }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      tzrOrXlrList: [],\n      jlrList: [],\n      alljlrList: [],\n      sprList: []\n    };\n  },\n  async mounted() {\n    //获取token\n    this.header.token = getToken();\n    //光伏电站下拉框\n    this.getFgsOptions();\n    this.getAllBdzSelectList();\n    this.tzrOrXlrList = await this.getGroupUsers(132, \"\");\n    this.alljlrList = await this.getGroupUsers(133, \"\");\n    this.sprList = await this.getGroupUsers(134, \"\");\n    await this.getData();\n  },\n  methods: {\n    /**\n     * 获取分公司下拉数据\n     */\n    getFgsOptions() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.ssgsOptionsDataList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"fgs\") {\n            return (item.options = this.ssgsOptionsDataList);\n          }\n        });\n      });\n    },\n    //导出\n    exportFun() {\n      exportExcel(this.params, \"操作命令信息\");\n    },\n    //工作流回传数据\n    async todoResult(data) {\n      let row = {\n        objId: data.businessKey,\n        isStart: 1,\n        isBack: data.processType === \"rollback\" ? 1 : 0\n      };\n      row.status = \"1\";\n      row.bzspr = data.nextUser;\n      let { code } = await updateById(row);\n      if (code === \"0000\") {\n        await this.getData();\n      } else {\n        this.$message.warning(\"操作失败\");\n      }\n    },\n    //关闭弹窗\n    closeActiviti() {\n      if (this.$refs.activiti.$refs.form) {\n        this.$refs.activiti.$refs.form.resetFields();\n      }\n      this.isShow = false;\n    },\n    getAllBdzSelectList() {\n      getBdzSelectList({}).then(res => {\n        this.bdzList = res.data;\n      });\n    },\n    //所属公司change事件\n    async handleFgsChange(fgsValue) {\n      //清空之前得选中值\n      this.wzDataListOptions = [];\n      this.$set(this.form, \"bdzmc\", \"\");\n      this.$set(this.form, \"tzr\", \"\");\n      this.jlrList = [];\n      this.$set(this.form, \"jlr\", \"\");\n      this.$set(this.form, \"ybljlr\", \"\");\n      //获取光伏电站方法\n      await this.fgsChange(fgsValue);\n    },\n    async fgsChange(fgsValue) {\n      //获取光伏电站方法\n      this.getBdzSelectList(fgsValue);\n      this.jlrList = await this.getGroupUsers(133, fgsValue);\n    },\n    handleYlChange() {\n      this.titleyl = \"查看操作项目\";\n      this.yl = true;\n    },\n    replaceStr() {\n      this.propTableData.colFirst.forEach(item => {\n        item.czrw = item.czrw.replaceAll(\n          this.replaceData.oldStr,\n          this.replaceData.newStr\n        );\n      });\n    },\n    //改造后的上传多个图片文件\n    uploadForm() {\n      var newUrl = []; //用来存放当前未改动过的图片url,此图片不进行删除处理，其余当前业务id下面的图片数据将进行删除\n      var imageType = [\"png\", \"jpg\"];\n      const formData = new FormData();\n      // 因为要传一个文件数组过去，所以要循环append\n      this.imgList.forEach(file => {\n        if (file.raw == undefined) {\n          newUrl.push(file.url);\n        }\n        formData.append(\"files\", file.raw);\n      });\n      formData.append(\"businessId\", this.uploadImgData.businessId); // 自定义参数\n      formData.append(\"newUrl\", newUrl); // 未改动过的图片url\n      formData.append(\"type\", imageType); // 未改动过的图片url\n      api\n        .requestPost(\"/isc-api/file/uploadFiles\", formData, 1)\n        .then(res => {\n          // 清空图片列表（一定要清空，否则上传成功后还是会调用handleChange（）函数，上传成功后列表中还存在图片）\n          this.imgList = [];\n          //查询接口数据\n          this.getData();\n        })\n        .catch(res => {\n          this.$message.success(\"图片上传失败！\");\n        });\n    },\n    // 选择文件时，往fileList里添加\n    handleChange(file, fileList) {\n      this.imgList = fileList;\n    },\n    handleProgress(event, file, fileList) {},\n    //图片移除\n    handleRemove(file, fileList) {\n      this.imgList = fileList;\n    },\n    //图片放大\n    handlePictureCardPreview(file) {\n      this.dialogImageUrl = file.url;\n      this.imgDialogVisible = true;\n    },\n    //列表查询\n    async getData(params) {\n      try {\n        this.params = { ...this.params, ...params };\n        const param = this.params;\n        this.loading = true;\n        param.mySorts = [{ prop: \"updateTime\", asc: false }];\n        const { data, code } = await getList(param);\n        if (code === \"0000\") {\n          for (let i of data.records) {\n            i.fgsmc = this.formatSsgs(i.fgs);\n            i.tzrxm = this.formatXlrOrTzr(i.tzr);\n            i.xlrxm = this.formatXlrOrTzr(i.xlr);\n            i.jlrxm = this.formatJlr(i.jlr);\n            i.ybljlrxm = this.formatJlr(i.ybljlr);\n          }\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          this.loading = false;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n\n    //新增按钮\n    async getInster() {\n      this.title = \"光伏倒闸操作命令增加\";\n      this.isDisabled = false;\n      this.form = { fgs: this.$store.getters.deptId.toString() };\n      await this.fgsChange(this.form.fgs);\n      this.form.status = \"0\";\n      this.isShowDetails = true;\n    },\n    //修改按钮\n    async getUpdate(row) {\n      this.title = \"光伏倒闸操作命令修改\";\n      this.isDisabled = false;\n      this.form = { ...row };\n      await this.fgsChange(this.form.fgs);\n      this.isShowDetails = true;\n    },\n\n    //详情按钮\n    async getDetails(row) {\n      this.title = \"光伏倒闸操作命令详情\";\n      this.form = { ...row };\n      await this.fgsChange(this.form.fgs);\n      this.isDisabled = true;\n      this.isShowDetails = true;\n    },\n    /**\n     * 操作票开票按钮\n     * */\n    async createCzp() {\n      await this.$refs[\"form\"].validate(async valid => {\n        if (valid) {\n          try {\n            let { code, data } = await saveOrUpdate(this.form);\n            if (code === \"0000\") {\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.isShowDetails = false;\n              await this.getData();\n              //操作票弹出框\n              this.isDisabledCzp = true;\n              this.propTableData.colFirst = [];\n              //清除校验提示\n              this.$nextTick(function() {\n                this.$refs[\"formCzp\"].clearValidate();\n              });\n              this.formCzp = {};\n              //光伏操作票类型为4\n              this.formCzp.lx = 4;\n              this.formCzp.status = \"0\";\n              this.formCzp.czml = data.objId;\n              this.formCzp.bdzmc = data.bdzmc;\n              if (data.xlr) {\n                this.formCzp.xlr = data.xlr;\n              }\n              this.formCzp.czrw = data.czrw;\n              this.formCzp.fgs = data.fgs;\n              this.formCzp.bm = data.bh;\n              this.isDisabled = true;\n              this.isCzpShowDetails = true;\n            }\n          } catch (e) {\n            console.log(e);\n          }\n        }\n      });\n    },\n    /**\n     * 开办结票\n     * */\n    createBjp() {\n      this.isBjpShowDetails = true;\n      this.isShowDetails = false;\n      this.formBjp.bdzmc = this.form.bdzmc;\n      this.formBjp.xlr = this.form.xlr;\n      this.formBjp.czrw = this.form.czrw;\n      this.formBjp.fgs = this.form.fgs;\n      this.formBjp.status = \"4\";\n      this.formBjp.lx = 2;\n      this.formBjp.czml = this.form.objId;\n    },\n    async saveRow() {\n      await this.$refs[\"form\"].validate(async valid => {\n        if (valid) {\n          try {\n            let { code } = await saveOrUpdate(this.form);\n            if (code === \"0000\") {\n              this.$message.success(\"操作成功\");\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.isShowDetails = false;\n              await this.getData();\n            }\n          } catch (e) {\n            console.log(e);\n          }\n        }\n      });\n    },\n    //删除按钮\n    deleteRow(row) {\n      this.form = { ...row };\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove([this.form.objId]).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {});\n    },\n    //关闭弹窗\n    close() {\n      this.isDxpShowDetails = false;\n      this.isLspShowDetails = false;\n      this.isShowDetails = false;\n    },\n    //选择行\n    selectChange(selection) {\n      // this.ids = selection.map(item => item.objId);\n      // this.single = selection.length !== 1\n      // this.multiple = !selection.length\n      this.selectData = selection;\n    },\n    //表格新增\n    listFirstAdd() {\n      let row = {\n        xh: \"\",\n        czrw: \"\",\n        sfwc: \"\",\n        uuid: getUUID()\n      };\n      this.propTableData.colFirst.push(row);\n      this.propTableData.sel = row;\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //表格删除\n    listFirstDel(row) {\n      if (row.objId) {\n        this.ids.push(row.objId);\n      }\n      this.propTableData.colFirst = this.propTableData.colFirst.filter(\n        item => item.uuid !== row.uuid\n      );\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n    },\n    //操作票关闭弹窗\n    closeCzp() {\n      this.isCzpShowDetails = false;\n      this.isBjpShowDetails = false;\n    },\n\n    //操作票确定按钮\n    saveRowCzp() {\n      this.formCzp.colFirst = this.propTableData.colFirst;\n      this.formCzp.objIdList = this.ids;\n      let tableValid = this.propTableData.colFirst.some(item => !item.czrw);\n      if (tableValid) {\n        this.$message.error(\"操作项目存在空项，请检查\");\n      }\n      this.$refs[\"formCzp\"].validate(async valid => {\n        if (valid && !tableValid) {\n          console.log(this.formCzp,'111111111')\n          saveOrUpdateCzp(this.formCzp).then(res => {\n            if (res.code === \"0000\") {\n              this.$message.success(\"操作成功\");\n              this.isCzpShowDetails = false;\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            }\n          });\n        }\n      });\n    },\n\n    //直接上报操作票\n    submitCzp() {\n      this.formCzp.colFirst = this.propTableData.colFirst;\n      this.formCzp.objIdList = this.ids;\n      let tableValid = this.propTableData.colFirst.some(item => !item.czrw);\n      if (tableValid) {\n        this.$message.error(\"操作项目存在空项，请检查\");\n      }\n      this.$refs[\"formCzp\"].validate(async valid => {\n        if (valid && !tableValid) {\n          saveOrUpdateCzp(this.formCzp).then(res => {\n            if (res.code === \"0000\") {\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n              let data = res.data;\n              this.isCzpShowDetails = false;\n              this.processData.variables.pass = true;\n              this.processData.businessKey = data.objId;\n              this.processData.processType = \"complete\";\n              this.activitiOption.title = \"提交\";\n              this.processData.defaultFrom = true;\n              this.processData.rylx = \"班组审核人\";\n              this.processData.dw = data.fgs;\n              this.processData.personGroupId = 134;\n              this.processData.routePath =\n                \"/gfgl/bddzcz/czpgl/gfdzcz/dzczp\";\n              this.isShow = true;\n            }\n          });\n        }\n      });\n    },\n\n    //办结票确定按钮\n    saveRowBjp() {\n      this.$refs[\"formBjp\"].validate(async valid => {\n        if (valid) {\n          saveOrUpdateCzp(this.formBjp).then(res => {\n            if (res.code === \"0000\") {\n              this.uploadImgData.businessId = res.data.objId;\n              //开始上传图片\n              this.uploadForm();\n              this.$message.success(\"操作成功\");\n              this.isBjpShowDetails = false;\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getData();\n            }\n          });\n        }\n      });\n    },\n    /**\n     * -----------------------------------典型票库---------------------------------------\n     * */\n    //获取典型操作票\n    getDxpkLists() {\n      this.title = \"典型票库查询\";\n      this.isDxpShowDetails = true;\n      this.isDisabled = false;\n      this.isShowDxp = true;\n      this.queryParams.sbmc = this.form.bdzmc;\n      this.handleQuery();\n    },\n    async changeMx(row) {\n      try {\n        const { data, code } = await getDxpkList({ objId: row.objId });\n        if (code === \"0000\") {\n          this.propTableDataDxpMx.colFirst = data;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //查询条件\n    async handleQuery(params) {\n      let param = { ...params, ...this.queryParams };\n      getLists(param).then(response => {\n        this.propTableDataDxp.tableData = response.data.records;\n        this.propTableDataDxp.pager.total = response.data.total;\n      });\n    },\n    //重置条件\n    resetQuery() {\n      this.queryParams = {};\n      this.handleQuery();\n    },\n    //典型票库确认按钮\n    saveRowDxp() {\n      this.propTableData.colFirst = this.propTableDataDxpMx.colFirst;\n      this.propTableData.colFirst.forEach(e => {\n        e.uuid = getUUID();\n      });\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n      this.isDxpShowDetails = false;\n    },\n    /**\n     * -----------------------------------历史票库---------------------------------------\n     * */\n    //获取历史操作票\n    getLspkList() {\n      this.title = \"历史票库查询\";\n      this.isLspShowDetails = true;\n      this.isDisabled = false;\n      this.isShowLsp = true;\n      this.queryParamsLsp.bdzmc = this.form.bdzmc;\n      this.handleQueryLsp();\n    },\n    // 当点击行时，传入参数查询\n    async changeLspMx(row) {\n      try {\n        const { data, code } = await getLspkList(row.objId);\n        if (code === \"0000\") {\n          this.propTableDataLspMx.colFirst = data;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //历史票库确认按钮\n    saveRowLsp() {\n      this.propTableData.colFirst = this.propTableDataLspMx.colFirst;\n      this.formCzp.czxs = this.propTableData.colFirst.length;\n      this.formCzp.wzxczxs = this.propTableData.colFirst.length;\n      this.propTableData.colFirst.forEach(e => {\n        e.sfwc = false;\n        e.uuid = getUUID();\n      });\n      this.isLspShowDetails = false;\n    },\n    //查询条件\n    handleQueryLsp() {\n      getListLsp(this.queryParamsLsp).then(response => {\n        this.propTableDataLsp.colFirst = response.data.records;\n      });\n    },\n    //重置条件\n    resetQueryLsp() {\n      this.queryParamsLsp = {\n        status: \"4\",\n        lx: 4\n      };\n      this.handleQueryLsp();\n    },\n    /**\n     * 获取光伏电站下拉框数据\n     */\n    getBdzSelectList(fgs) {\n      getBdzSelectList({ ssdwbm: fgs }).then(res => {\n        this.wzDataListOptions = res.data;\n      });\n    },\n    //下拉框change事件\n    handleEvent(val, eventValue) {\n      if (val.label === \"fgs\") {\n        getBdzSelectList({ ssdwbm: val.value.toString() }).then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value === \"bdzmc\") {\n              this.$set(eventValue, \"bdzmc\", \"\");\n              return (item.options = res.data);\n            }\n          });\n        });\n      }\n    },\n    filterReset() {\n      this.params = {\n        //光伏\n        lx: 2\n      };\n    },\n    formatSsgs(ssgs) {\n      let pageOrganizationSelectedList = JSON.parse(JSON.stringify(this.ssgsOptionsDataList))\n      pageOrganizationSelectedList.push({label: '港东变电分公司', value: '3002'})\n      pageOrganizationSelectedList.push({label: '港中变电分公司', value: '3003'})\n      if (ssgs) {\n        let filter = pageOrganizationSelectedList.filter(g => g.value === ssgs);\n        if (filter.length > 0) {\n          return filter[0].label;\n        } else {\n          return \"\";\n        }\n      } else {\n        return \"\";\n      }\n    },\n    getGroupUsers(personGroupId, deptId) {\n      return getUsers({\n        personGroupId: personGroupId,\n        deptId: deptId,\n        deptName: \"\"\n      }).then(res => {\n        return res.data;\n      });\n    },\n    formatXlrOrTzr(xlr) {\n      if (xlr) {\n        let filter = this.tzrOrXlrList.filter(g => g.userName === xlr);\n        if (filter.length > 0) {\n          return filter[0].nickName;\n        } else {\n          return \"\";\n        }\n      } else {\n        return \"\";\n      }\n    },\n\n    formatJlr(jlr) {\n      if (jlr) {\n        let filter = this.alljlrList.filter(g => g.userName === jlr);\n        if (filter.length > 0) {\n          return filter[0].nickName;\n        } else {\n          return \"\";\n        }\n      } else {\n        return \"\";\n      }\n    },\n    //导出word\n    exportWord() {\n      let params = {\n        data: this.params,\n        url: \"bzBddzczml\"\n      };\n      let fileName = \"光伏倒闸操作命令记录\";\n      if (!this.selectData.length > 0) {\n        params.data = this.params;\n        exportWordByparams(params, fileName);\n      } else {\n        params.data = this.selectData;\n        exportWordByselection(params, fileName);\n      }\n    },\n    changeZslOrYbl(val) {\n      if (val) {\n        this.$set(this.form, \"tzsj\", null);\n        this.$set(this.form, \"tzr\", \"\");\n        this.$set(this.form, \"ybljlr\", \"\");\n      } else {\n        this.$set(this.form, \"xlsj\", null);\n        this.$set(this.form, \"xlr\", \"\");\n        this.$set(this.form, \"jlr\", \"\");\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.card-style {\n  background-color: #e0eff2;\n  line-height: 3vh;\n  border-radius: 3px;\n  font-weight: 800;\n  padding-left: 1vw;\n  margin-bottom: 3vh;\n}\n\n/*控制input输入框边框是否显示*/\n.elInput >>> .el-input__inner {\n  border: 0;\n}\n\n.box-card {\n  margin-bottom: 2vh !important;\n}\n</style>\n"], "sourceRoot": "src/views/dagangOilfield/czpgl/gfdzcz"}]}