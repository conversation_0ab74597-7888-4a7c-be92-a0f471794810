{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\lpbzk\\xsxmpz.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\bzgl\\lpbzk\\xsxmpz.js", "mtime": 1730275668846}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0TGlzdCA9IGdldExpc3Q7CmV4cG9ydHMucXVlcnlaYiA9IHF1ZXJ5WmI7CmV4cG9ydHMuc2F2ZU9yVXBkYXRlID0gc2F2ZU9yVXBkYXRlOwpleHBvcnRzLnJlbW92ZSA9IHJlbW92ZTsKCnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwoKdmFyIGJhc2VVcmwgPSAiL21hbmFnZXItYXBpIjsgLy8g5p+l6K+i5LiT5Lia56Wo5pON5L2c5pyv6K+t6KGoCgpmdW5jdGlvbiBnZXRMaXN0KHBhcmFtcykgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3RQb3N0KGJhc2VVcmwgKyAnL2J6WHN4bXB6L3BhZ2UnLCBwYXJhbXMsIDEpOwp9IC8vIOafpeivouWtkOihqAoKCmZ1bmN0aW9uIHF1ZXJ5WmIocGFyYW1zKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcvYnpYc3htcHovcXVlcnlaYicsIHBhcmFtcywgMSk7Cn0gLy8g5re75Yqg5oiW5L+u5pS5CgoKZnVuY3Rpb24gc2F2ZU9yVXBkYXRlKHBhcmFtcykgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3RQb3N0KGJhc2VVcmwgKyAnL2J6WHN4bXB6L3NhdmVPclVwZGF0ZScsIHBhcmFtcywgMSk7Cn0gLy8g5re75Yqg5oiW5L+u5pS5CgoKZnVuY3Rpb24gcmVtb3ZlKHBhcmFtcykgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3RQb3N0KGJhc2VVcmwgKyAnL2J6WHN4bXB6L3JlbW92ZScsIHBhcmFtcywgMSk7Cn0="}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagang<PERSON>field/bzgl/lpbzk/xsxmpz.js"], "names": ["baseUrl", "getList", "params", "api", "requestPost", "queryZb", "saveOrUpdate", "remove"], "mappings": ";;;;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,cAAhB,C,CAEA;;AACO,SAASC,OAAT,CAAiBC,MAAjB,EAAyB;AAC9B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,gBAAxB,EAAyCE,MAAzC,EAAgD,CAAhD,CAAP;AACD,C,CACD;;;AACO,SAASG,OAAT,CAAiBH,MAAjB,EAAyB;AAC9B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,mBAAxB,EAA4CE,MAA5C,EAAmD,CAAnD,CAAP;AACD,C,CAED;;;AACO,SAASI,YAAT,CAAsBJ,MAAtB,EAA8B;AACnC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,wBAAxB,EAAiDE,MAAjD,EAAwD,CAAxD,CAAP;AACD,C,CACD;;;AACO,SAASK,MAAT,CAAgBL,MAAhB,EAAwB;AAC7B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,kBAAxB,EAA2CE,MAA3C,EAAkD,CAAlD,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\nconst baseUrl = \"/manager-api\";\n\n// 查询专业票操作术语表\nexport function getList(params) {\n  return api.requestPost(baseUrl+'/bzXsxmpz/page',params,1)\n}\n// 查询子表\nexport function queryZb(params) {\n  return api.requestPost(baseUrl+'/bzXsxmpz/queryZb',params,1)\n}\n\n// 添加或修改\nexport function saveOrUpdate(params) {\n  return api.requestPost(baseUrl+'/bzXsxmpz/saveOrUpdate',params,1)\n}\n// 添加或修改\nexport function remove(params) {\n  return api.requestPost(baseUrl+'/bzXsxmpz/remove',params,1)\n}\n\n"]}]}