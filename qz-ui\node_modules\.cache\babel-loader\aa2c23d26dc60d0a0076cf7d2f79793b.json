{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\todoitem\\done.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\activiti\\todoitem\\done.vue", "mtime": 1706897322085}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["done.vue"], "names": [], "mappings": ";;;;;;;;;;;AA+IA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;AACA,EAAA,IAAA,EAAA,MADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,eAAA,EAAA;AAAA,GAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA,MAAA,UAAA,EAAA,IADA;AAEA,MAAA,WAAA,EAAA,KAFA;AAGA,MAAA,cAAA,EAAA,KAHA;AAIA;AACA,MAAA,OAAA,EAAA,IALA;AAMA;AACA,MAAA,GAAA,EAAA,EAPA;AAQA,MAAA,KAAA,EAAA,IARA;AASA;AACA,MAAA,MAAA,EAAA,IAVA;AAWA;AACA,MAAA,QAAA,EAAA,IAZA;AAaA;AACA,MAAA,UAAA,EAAA,IAdA;AAeA,MAAA,SAAA,EAAA,EAfA;AAgBA,MAAA,UAAA,EAAA,EAhBA;AAiBA;AACA,MAAA,KAAA,EAAA,CAlBA;AAmBA;AACA,MAAA,QAAA,EAAA,IApBA;AAqBA;AACA,MAAA,IAAA,EAAA,KAtBA;AAuBA;AACA;AACA,MAAA,IAAA,EAAA,EAzBA;AA0BA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,QAAA,EAAA,SAHA;AAIA,QAAA,MAAA,EAAA,SAJA;AAKA,QAAA,QAAA,EAAA,SALA;AAMA,QAAA,YAAA,EAAA,SANA;AAOA,QAAA,cAAA,EAAA;AAPA,OA3BA;AAoCA;AACA,MAAA,KAAA,EAAA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,QAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AAJA;AArCA,KAAA;AA8CA,GAlDA;AAmDA,EAAA,OAnDA,qBAmDA;AACA,SAAA,OAAA;AACA,GArDA;AAsDA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,OAFA,qBAEA;AAAA;;AACA,WAAA,OAAA,GAAA,IAAA;AACA,8BAAA,KAAA,WAAA,EAAA,IAAA,CACA,UAAA,QAAA,EAAA;AACA,QAAA,KAAA,CAAA,QAAA,GAAA,QAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,KAAA,CAAA,KAAA,GAAA,QAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,KAAA,CAAA,OAAA,GAAA,KAAA;AACA,OALA;AAOA,KAXA;;AAYA;;;AAGA,IAAA,MAfA,oBAeA;AACA,WAAA,IAAA,GAAA,KAAA;AACA,WAAA,KAAA;AACA,KAlBA;;AAmBA;;;AAGA,IAAA,KAtBA,mBAsBA;AACA,WAAA,IAAA,GAAA;AACA,QAAA,IAAA,EAAA,SADA;AAEA,QAAA,IAAA,EAAA,SAFA;AAGA,QAAA,WAAA,EAAA;AAHA,OAAA;AAKA,WAAA,SAAA,CAAA,MAAA;AACA,KA7BA;;AA8BA;AACA,IAAA,WA/BA,yBA+BA;AACA,WAAA,WAAA,CAAA,OAAA,GAAA,CAAA;AACA,WAAA,OAAA;AACA,KAlCA;;AAmCA;AACA,IAAA,UApCA,wBAoCA;AACA,WAAA,SAAA,GAAA,EAAA;AACA,WAAA,SAAA,CAAA,WAAA;AACA,WAAA,WAAA;AACA,KAxCA;;AAyCA;AACA,IAAA,qBA1CA,iCA0CA,SA1CA,EA0CA;AACA,WAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,MAAA;AAAA,OAAA,CAAA;AACA,WAAA,MAAA,GAAA,SAAA,CAAA,MAAA,IAAA,CAAA;AACA,WAAA,QAAA,GAAA,CAAA,SAAA,CAAA,MAAA;AACA,KA9CA;;AA+CA;AACA,IAAA,cAhDA,0BAgDA,GAhDA,EAgDA;AACA,WAAA,IAAA,GAAA,IAAA;AACA,UAAA,GAAA,GAAA,KAAA,GAAA;;AACA,UAAA,GAAA,CAAA,MAAA,IAAA,CAAA,EAAA;AACA,aAAA,UAAA,CAAA,SAAA;AACA,eAAA,KAAA;AACA;AACA,KAvDA;;AAwDA;AACA,IAAA,UAAA,EAAA,sBAAA;AAAA;;AACA,WAAA,KAAA,CAAA,MAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,UAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA,UAAA,QAAA,EAAA;AACA,gBAAA,QAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,cAAA,MAAA,CAAA,UAAA,CAAA,MAAA;;AACA,cAAA,MAAA,CAAA,IAAA,GAAA,KAAA;;AACA,cAAA,MAAA,CAAA,OAAA;;AACA,cAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA;AAAA,gBAAA,IAAA,EAAA,6BAAA,QAAA,CAAA;AAAA,eAAA;AACA;AACA,WAPA;AAQA;AACA,OAXA;AAYA,KAtEA;;AAuEA;AACA,IAAA,eAxEA,2BAwEA,GAxEA,EAwEA,CAEA,CA1EA;;AA2EA;AACA,IAAA,aA5EA,yBA4EA,GA5EA,EA4EA;AACA,WAAA,WAAA,GAAA,IAAA;AACA,WAAA,UAAA,GAAA,GAAA,CAAA,UAAA;AACA,KA/EA;;AAgFA;AACA,IAAA,aAjFA,2BAiFA;AACA,WAAA,WAAA,GAAA,KAAA;AACA,KAnFA;;AAoFA;AACA,IAAA,aArFA,2BAqFA;AACA,WAAA,WAAA,GAAA,KAAA;AACA,KAvFA;;AAwFA;AACA,IAAA,gBAzFA,4BAyFA,GAzFA,EAyFA;AAAA;;AACA,MAAA,MAAA,CAAA,GAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,UAAA,CAAA,GAAA,CAAA,GAAA;AACA,SAFA,MAEA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,GAAA,CAAA,GAAA;AACA;AACA,OANA;AAOA,KAjGA;;AAkGA;AACA,IAAA,UAnGA,sBAmGA,GAnGA,EAmGA;AACA,WAAA,cAAA,GAAA,IAAA;AACA,WAAA,MAAA,GAAA,oDAAA,GAAA,CAAA,UAAA,GAAA,KAAA,GAAA,IAAA,IAAA,GAAA,OAAA,EAAA;AACA,KAtGA;;AAuGA;AACA,IAAA,WAxGA,uBAwGA,GAxGA,EAwGA;AACA,aAAA,GAAA,CAAA,MAAA,IAAA,GAAA,GAAA,GAAA,GAAA,GAAA,CAAA,MAAA,GAAA,MAAA,GAAA,GAAA,GAAA;AACA,KA1GA;;AA2GA;AACA,IAAA,UA5GA,sBA4GA,GA5GA,EA4GA;AACA,aAAA,GAAA,CAAA,MAAA,IAAA,GAAA,GAAA,GAAA,GAAA,GAAA,CAAA,MAAA,GAAA,MAAA,GAAA,GAAA,GAAA;AACA,KA9GA;AA+GA,IAAA,gBA/GA,8BA+GA;AACA,WAAA,cAAA,GAAA,KAAA;AACA;AAjHA;AAtDA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form class=\"searchForm1\" :model=\"queryParams\" ref=\"queryForm\" :inline=\"true\" v-show=\"showSearch\" >\n      <el-row>\n        <el-col :span=\"6\">\n          <el-form-item label=\"事项标题：\" prop=\"itemName\">\n            <el-input v-model=\"queryParams.itemName\" placeholder=\"请输入事项标题\" clearable\n                      @keyup.enter.native=\"handleQuery\" />\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-form-item label=\"任务名称：\" prop=\"taskName\">\n            <el-input v-model=\"queryParams.taskName\" placeholder=\"请输入待办任务名称\" clearable\n                      @keyup.enter.native=\"handleQuery\"/>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-form-item label=\"待办人：\" prop=\"todoUserName\">\n            <el-input v-model=\"queryParams.todoUserName\" placeholder=\"请输入待办人\" clearable\n                      @keyup.enter.native=\"handleQuery\"/>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"6\">\n          <el-form-item label=\"处理人名称：\" prop=\"handleUserName\">\n            <el-input v-model=\"queryParams.handleUserName\" placeholder=\"请输入处理人名称\" clearable c\n                      @keyup.enter.native=\"handleQuery\"/>\n          </el-form-item>\n        </el-col>\n      </el-row>\n      <el-row>\n        <el-col :span=\"12\">\n          <el-form-item label=\"通知时间:\">\n            <el-date-picker\n              unlink-panels\n              v-model=\"dateRange\"\n              value-format=\"yyyy-MM-dd\"\n              type=\"daterange\"\n              range-separator=\"-\"\n              start-placeholder=\"开始日期\"\n              end-placeholder=\"结束日期\"\n            ></el-date-picker>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"12\">\n          <el-form-item label=\"处理时间:\">\n            <el-date-picker\n              unlink-panels\n              v-model=\"dateRange1\"\n              value-format=\"yyyy-MM-dd\"\n              type=\"daterange\"\n              range-separator=\"-\"\n              start-placeholder=\"开始日期\"\n              end-placeholder=\"结束日期\"\n            ></el-date-picker>\n          </el-form-item>\n        </el-col>\n      </el-row>\n      <el-form-item>\n        <el-button type=\"cyan\" icon=\"el-icon-search\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" @click=\"resetQuery\" type=\"warning\">重置</el-button>\n      </el-form-item>\n    </el-form>\n    <el-row :gutter=\"20\">\n      <el-col :span=\"24\">\n        <el-white class=\"button-group\">\n          <el-table style=\"margin-top: 15px;\" stripe border v-loading=\"loading\" :data=\"todoList\" @selection-change=\"handleSelectionChange\">\n            <el-table-column type=\"selection\" width=\"50\" align=\"center\"/>\n            <el-table-column label=\"事项标题\" align=\"center\" prop=\"itemName\" width=\"160px\" fixed >\n              <template slot-scope=\"scope\">\n                <router-link   :to=\"{name:'报销管理' ,params:{appId:scope.row.businessId}}\"  class=\"link-type\" v-if=\"scope.row.module=='repayment'\">\n                  <span>{{ scope.row.itemName }}</span>\n                </router-link>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"申请人名称\" align=\"center\" prop=\"applyUserName\"     />\n            <el-table-column label=\"待办人名称\" align=\"center\" prop=\"todoUserName\"    />\n            <el-table-column label=\"处理人名称\" align=\"center\" prop=\"handleUserName\"    />\n            <el-table-column label=\"创建时间\" align=\"center\" prop=\"todoTime\" width=\"160px\"  />\n            <el-table-column label=\"处理时间\" align=\"center\" prop=\"handleTime\" width=\"160px\"  />\n            <el-table-column label=\"待办任务名称\" align=\"center\" prop=\"nodeName\"  width=\"160px\" />\n            <el-table-column label=\"操作\" align=\"center\"   class-name=\"small-padding fixed-width\" width=\"160px\" fixed=\"right\">\n              <template slot-scope=\"scope\">\n                <el-button size=\"mini\" type=\"text\"   @click=\"handleHistory(scope.row)\" >审批历史</el-button>\n                <el-button size=\"mini\" type=\"text\"   @click=\"handlePlan(scope.row)\" >流程查看</el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n\n          <pagination v-show=\"total>0\"\n                      :total=\"total\"\n                      :page.sync=\"queryParams.pageNum\"\n                      :limit.sync=\"queryParams.pageSize\"\n                      @pagination=\"getList\"\n          />\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!-- 添加或修改参数配置对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body :close-on-click-modal=false v-dialogDrag>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n        <el-form-item label=\"申请人：\" prop=\"name\">\n          <el-input v-model=\"form.name\" placeholder=\"请输入名称\" maxlength=\"30\"/>\n        </el-form-item>\n        <el-form-item label=\"审批意见：\" prop=\"pass\">\n          <el-select v-model=\"form.pass\" placeholder=\"请选择\">\n            <el-option label=\"同意\" :value=\"true\" />\n            <el-option label=\"拒绝\" :value=\"false\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"批注：\" prop=\"description\">\n          <el-input v-model=\"form.description\" type=\"textarea\" placeholder=\"请输入内容\"></el-input>\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n\n    <el-dialog title=\"审批历史\" :visible.sync=\"openHistory\" width=\"800px\" append-to-body :close-on-click-modal=false v-dialogDrag>\n      <slot v-if=\"instanceId!=null\">\n        <activiti-history :instance-id=\"instanceId\" />\n      </slot>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitHistory\">确 定</el-button>\n        <el-button @click=\"cancelHistory\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n\n    <el-dialog title=\"审批进度\" :visible.sync=\"openLoadingImg\" width=\"800px\" append-to-body :close-on-click-modal=false v-dialogDrag>\n      <img :src=\"imgSrc\"/>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"cancelLoadingImg\">关 闭</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n  import { listDone } from \"@/api/activiti/todoitem\";\n  import ActivitiHistory from \"./history\";\n  export default {\n    name: \"Done\",\n    components: {ActivitiHistory},\n    data() {\n      return {\n        instanceId:null,\n        openHistory:false,\n        openLoadingImg:false,\n        // 遮罩层\n        loading: true,\n        // 选中数组\n        ids: [],\n        title:'审批',\n        // 非单个禁用\n        single: true,\n        // 非多个禁用\n        multiple: true,\n        // 显示搜索条件\n        showSearch: true,\n        dateRange: [],\n        dateRange1:[],\n        // 总条数\n        total: 0,\n        // 用户表格数据\n        todoList: null,\n        // 是否显示弹出层\n        open: false,\n        // 部门名称\n        // 表单参数\n        form: {},\n        // 查询参数\n        queryParams: {\n          pageNum: 1,\n          pageSize: 10,\n          itemName: undefined,\n          module: undefined,\n          taskName:undefined,\n          todoUserName:undefined,\n          handleUserName:undefined,\n        },\n        // 表单校验\n        rules: {\n          pass: [\n            {required: true, message: \"审批意见不能为空\", trigger: \"blur\"},\n          ],\n          name: [\n            {required: true, message: \"名称不能为空\", trigger: \"blur\"},\n          ],\n        },\n      };\n    },\n    created() {\n      this.getList();\n    },\n    methods: {\n      /** 查询用户列表 */\n      getList() {\n        this.loading = true;\n        listDone(this.queryParams).then(\n          (response) => {\n            this.todoList = response.data.records;\n            this.total = response.data.total;\n            this.loading = false;\n          }\n        );\n      },\n      /**\n       * 取消按钮\n       * */\n      cancel() {\n        this.open = false;\n        this.reset();\n      },\n      /**\n       * 表单重置\n       * */\n      reset() {\n        this.form = {\n          pass: undefined,\n          name: undefined,\n          description:undefined\n        };\n        this.resetForm(\"form\");\n      },\n      /** 搜索按钮操作 */\n      handleQuery() {\n        this.queryParams.pageNum = 1;\n        this.getList();\n      },\n      /** 重置按钮操作 */\n      resetQuery() {\n        this.dateRange = [];\n        this.resetForm(\"queryForm\");\n        this.handleQuery();\n      },\n      /***多选框选中数据***/\n      handleSelectionChange(selection) {\n        this.ids = selection.map((item) => item.userId);\n        this.single = selection.length != 1;\n        this.multiple = !selection.length;\n      },\n      /** 办理按钮操作 */\n      handleComplete(row) {\n        this.open = true;\n        let ids = this.ids;\n        if(ids.length!=1){\n          this.msgWarning(\"请选择一条数据\");\n          return false;\n        }\n      },\n      /** 提交按钮 */\n      submitForm: function () {\n        this.$refs[\"form\"].validate((valid) => {\n          if (valid) {\n            save(this.form).then((response) => {\n              if (response.code === '0000') {\n                this.msgSuccess(\"新增成功\");\n                this.open = false;\n                this.getList();\n                this.$router.push({path:'/activiti/onlinemodeler/'+response.data});\n              }\n            });\n          }\n        });\n      },\n      /** 申请按钮操作 */\n      handleApplyInfo(row) {\n\n      },\n      /***  审批历史文件 ***/\n      handleHistory(row){\n        this.openHistory = true\n        this.instanceId = row.instanceId\n      },\n      /** 审批历史确定  **/\n      submitHistory(){\n        this.openHistory = false\n      },\n      /**** 关闭审批历史  ****/\n      cancelHistory(){\n        this.openHistory = false\n      },\n      /** 委托 **/\n      handleDelegation(row){\n        deploy(row.id).then(res =>{\n          if(res.code=='0000'){\n            this.msgSuccess(res.msg);\n          }else{\n            this.msgError(res.msg);\n          }\n        })\n      },\n      /**** 进度查看  ****/\n      handlePlan(row){\n        this.openLoadingImg = true\n        this.imgSrc =\"/activiti-api/process/read-resource?instanceId=\"+row.instanceId+\"&t=\"+new Date().getTime();\n      },\n      /** 是否查看 **/\n      hasLookOver(row){\n        return row.isView=='0'?'否':row.isView='1'?'是':'-';\n      },\n      /** 是否处理 **/\n      hasHandler(row){\n        return row.isView=='0'?'否':row.isView='1'?'是':'-';\n      },\n      cancelLoadingImg(){\n        this.openLoadingImg = false;\n      }\n    },\n  };\n</script>\n"], "sourceRoot": "src/views/activiti/todoitem"}]}