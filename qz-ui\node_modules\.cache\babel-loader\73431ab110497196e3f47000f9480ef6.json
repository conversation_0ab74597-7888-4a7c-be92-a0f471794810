{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\ztxxsjglpz.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\ztxxsjglpz.vue", "mtime": 1706897323231}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["ztxxsjglpz.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgIA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;AACA,EAAA,IAAA,EAAA,YADA;AAEA,EAAA,UAAA,EAAA;AAAA,IAAA,IAAA,EAAA;AAAA,GAFA;AAGA,EAAA,IAHA,kBAGA;AACA,WAAA;AACA,MAAA,QAAA,EAAA,KADA;AACA;AACA,MAAA,WAAA,EAAA,EAFA;AAGA,MAAA,WAAA,EAAA,EAHA;AAIA;AACA,MAAA,UAAA,EAAA,IALA;AAMA;AACA,MAAA,YAAA,EAAA;AACA,QAAA,QAAA,EAAA,UADA;AAEA,QAAA,KAAA,EAAA;AAFA,OAPA;AAWA;AACA,MAAA,MAAA,EAAA,EAZA;AAaA;AACA,MAAA,IAAA,EAAA,EAdA;AAeA,MAAA,KAAA,EAAA,EAfA;AAgBA,MAAA,IAAA,EAAA,KAhBA;AAiBA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,OAAA,EAAA;AADA,SADA;AAIA,QAAA,SAAA,EAAA,CACA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,UAHA;AAIA,UAAA,QAAA,EAAA,IAJA;AAKA,UAAA,OAAA,EAAA;AALA,SADA;AAJA,OAjBA;AA+BA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,UAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AACA,UAAA,IAAA,EAAA,WADA;AAEA,UAAA,KAAA,EAAA,IAFA;AAGA,UAAA,QAAA,EAAA,OAHA;AAIA,UAAA,KAAA,EAAA;AAAA,YAAA,OAAA,EAAA;AAAA,WAJA;AAKA;AACA,UAAA,KAAA,EAAA,OANA;AAOA,UAAA,SAAA,EAAA,CACA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WADA,EAEA;AAAA,YAAA,IAAA,EAAA,IAAA;AAAA,YAAA,QAAA,EAAA,KAAA;AAAA,WAFA;AAPA,SANA;AAZA,OA/BA;AA+DA,MAAA,iBAAA,EAAA,EA/DA;AAgEA,MAAA,UAAA,EAAA,EAhEA;AAiEA;AACA,MAAA,QAAA,EAAA,EAlEA;AAmEA;AACA,MAAA,QAAA,EAAA,EApEA;AAqEA;AACA,MAAA,WAAA,EAAA,IAtEA;AAuEA,MAAA,QAAA,EAAA,EAvEA;AAwEA,MAAA,KAAA,EAAA;AACA,QAAA,MAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA;AADA,OAxEA;AA8EA,MAAA,UAAA,EAAA,KA9EA;AA+EA;AACA,MAAA,OAAA,EAAA,EAhFA;AAiFA;AACA,MAAA,kBAAA,EAAA,EAlFA;AAmFA;AACA,MAAA,oBAAA,EAAA;AApFA,KAAA;AAsFA,GA1FA;AA2FA,EAAA,MA3FA,oBA2FA,CACA,CA5FA;AA8FA,EAAA,OA9FA,qBA8FA;AACA;AACA,SAAA,WAAA;AACA,GAjGA;AAkGA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,QAFA,oBAEA,GAFA,EAEA;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,GAAA;AACA,KAJA;AAKA,IAAA,OALA,qBAKA;AACA,WAAA,QAAA,GAAA,KAAA;AACA,KAPA;AAQA,IAAA,aARA,2BAQA;AACA,UAAA,KAAA,IAAA,CAAA,MAAA,EAAA;AACA,aAAA,QAAA,GAAA,IAAA;AACA,OAFA,MAEA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,UAAA;AACA;AAEA,KAfA;AAgBA;AACA,IAAA,WAjBA,yBAiBA;AAAA;;AACA,uCAAA;AAAA,QAAA,IAAA,EAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,UAAA,KAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;AACA;AACA,OAJA;AAKA,KAvBA;AAwBA,IAAA,WAxBA,yBAwBA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBACA,MAAA,CAAA,MADA;AAAA;AAAA;AAAA;;AAEA,gBAAA,IAFA,GAEA,GAFA;;AAGA,oBAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA,IAAA,IAAA,CAAA,CAAA,EAAA;AACA,kBAAA,IAAA,GAAA,GAAA;AACA;;AALA;AAAA,uBAMA,2BAAA;AAAA,kBAAA,IAAA,EAAA,MAAA,CAAA,MAAA;AAAA,kBAAA,IAAA,EAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,sBAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,oBAAA,MAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA;AACA,iBAJA,CANA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,KApCA;AAsCA,IAAA,cAtCA,4BAsCA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBACA,MAAA,CAAA,MADA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAEA,8BAAA;AAAA,kBAAA,MAAA,EAAA,MAAA,CAAA,MAAA;AAAA,kBAAA,QAAA,EAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,sBAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,oBAAA,MAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA;AACA,iBAJA,CAFA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA,KA9CA;AAgDA;AACA,IAAA,eAjDA,2BAiDA,IAjDA,EAiDA,IAjDA,EAiDA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,WAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,iBAAA,CAAA,IAAA,GAAA,EAAA;AACA,gBAAA,MAAA,CAAA,iBAAA,CAAA,MAAA,GAAA,EAAA;AACA,gBAAA,MAAA,CAAA,iBAAA,CAAA,MAAA,GAAA,EAAA;AACA,gBAAA,MAAA,CAAA,iBAAA,CAAA,QAAA,GAAA,EAAA;AACA,gBAAA,MAAA,CAAA,iBAAA,CAAA,IAAA,GAAA,EAAA;AAEA,gBAAA,MAAA,CAAA,QAAA,CAAA,QAAA,GAAA,EAAA;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,MAAA,GAAA,EAAA;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,MAAA,GAAA,EAAA;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,IAAA,GAAA,EAAA;;AAXA,sBAYA,IAAA,CAAA,SAAA,KAAA,GAZA;AAAA;AAAA;AAAA;;AAaA,gBAAA,MAAA,CAAA,MAAA,GAAA,IAAA,CAAA,EAAA;AAbA;AAAA,uBAcA,MAAA,CAAA,WAAA,EAdA;;AAAA;AAAA;AAAA,uBAeA,MAAA,CAAA,cAAA,EAfA;;AAAA;AAAA;AAAA,uBAgBA,MAAA,CAAA,OAAA,EAhBA;;AAAA;AAAA,sBAmBA,IAAA,CAAA,SAAA,KAAA,GAnBA;AAAA;AAAA;AAAA;;AAoBA,gBAAA,MAAA,CAAA,iBAAA,CAAA,MAAA,GAAA,IAAA,CAAA,EAAA;AACA,gBAAA,MAAA,CAAA,MAAA,GAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA;AArBA;AAAA,uBAsBA,MAAA,CAAA,WAAA,EAtBA;;AAAA;AAAA;AAAA,uBAuBA,MAAA,CAAA,cAAA,EAvBA;;AAAA;AAAA;AAAA,uBAwBA,MAAA,CAAA,OAAA,EAxBA;;AAAA;AAAA,sBA2BA,IAAA,CAAA,SAAA,KAAA,GA3BA;AAAA;AAAA;AAAA;;AA4BA,gBAAA,MAAA,CAAA,iBAAA,CAAA,IAAA,GAAA,IAAA,CAAA,EAAA;AACA,gBAAA,MAAA,CAAA,iBAAA,CAAA,MAAA,GAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA;AACA,gBAAA,MAAA,CAAA,MAAA,GAAA,IAAA,CAAA,MAAA,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA;AA9BA;AAAA,uBA+BA,MAAA,CAAA,WAAA,EA/BA;;AAAA;AAAA;AAAA,uBAgCA,MAAA,CAAA,cAAA,EAhCA;;AAAA;AAAA;AAAA,uBAiCA,MAAA,CAAA,OAAA,EAjCA;;AAAA;AAAA,sBAoCA,IAAA,CAAA,SAAA,KAAA,GApCA;AAAA;AAAA;AAAA;;AAqCA,gBAAA,MAAA,CAAA,iBAAA,CAAA,IAAA,GAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA;AACA,gBAAA,MAAA,CAAA,iBAAA,CAAA,MAAA,GAAA,IAAA,CAAA,EAAA;AACA,gBAAA,MAAA,CAAA,iBAAA,CAAA,MAAA,GAAA,IAAA,CAAA,MAAA,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA;AACA,gBAAA,MAAA,CAAA,MAAA,GAAA,IAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA;AAxCA;AAAA,uBAyCA,MAAA,CAAA,WAAA,EAzCA;;AAAA;AAAA;AAAA,uBA0CA,MAAA,CAAA,cAAA,EA1CA;;AAAA;AAAA;AAAA,uBA2CA,MAAA,CAAA,OAAA,EA3CA;;AAAA;AAAA,sBA8CA,IAAA,CAAA,SAAA,KAAA,GA9CA;AAAA;AAAA;AAAA;;AA+CA,gBAAA,MAAA,CAAA,iBAAA,CAAA,IAAA,GAAA,IAAA,CAAA,MAAA,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA;AACA,gBAAA,MAAA,CAAA,iBAAA,CAAA,QAAA,GAAA,IAAA,CAAA,EAAA;AACA,gBAAA,MAAA,CAAA,iBAAA,CAAA,MAAA,GAAA,IAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA;AACA,gBAAA,MAAA,CAAA,iBAAA,CAAA,MAAA,GAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA;AACA,gBAAA,MAAA,CAAA,MAAA,GAAA,IAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA;AAnDA;AAAA,uBAoDA,MAAA,CAAA,WAAA,EApDA;;AAAA;AAAA;AAAA,uBAqDA,MAAA,CAAA,cAAA,EArDA;;AAAA;AAsDA,gBAAA,MAAA,CAAA,QAAA,CAAA,QAAA,GAAA,IAAA,CAAA,EAAA;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,QAAA,GAAA,IAAA,CAAA,KAAA;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,MAAA,GAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,KAAA,GAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,IAAA,GAAA,IAAA,CAAA,MAAA,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,MAAA,GAAA,IAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,MAAA,GAAA,IAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,IAAA,GAAA,IAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA;AACA,gBAAA,MAAA,CAAA,QAAA,CAAA,MAAA,GAAA,IAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA;AACA,gBAAA,MAAA,CAAA,WAAA,GAAA,KAAA;AA/DA;AAAA,uBAgEA,MAAA,CAAA,OAAA,EAhEA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkEA,KAnHA;AAsHA;AACA,IAAA,OAvHA,mBAuHA,MAvHA,EAuHA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,KAFA,+DAEA,MAAA,CAAA,iBAFA,GAEA,MAFA;AAGA,gBAAA,KAAA,CAAA,IAAA,GAAA,MAAA,CAAA,MAAA;AAHA;AAAA,uBAIA,2BAAA,KAAA,CAJA;;AAAA;AAAA;AAIA,gBAAA,IAJA,sBAIA,IAJA;AAIA,gBAAA,IAJA,sBAIA,IAJA;;AAKA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,IAAA,CAAA,OAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,wBAAA,IAAA,CAAA,IAAA,KAAA,IAAA,EAAA;AACA,sBAAA,IAAA,CAAA,MAAA,GAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,MAAA,CAAA,OAAA,CAAA,IAAA,EAAA,IAAA,CAAA,CAAA;AACA,sBAAA,IAAA,CAAA,QAAA,GAAA,MAAA,CAAA,WAAA,CAAA,IAAA,CAAA,MAAA,CAAA;AACA,qBAHA,MAGA;AACA,sBAAA,IAAA,CAAA,QAAA,GAAA,MAAA,CAAA,aAAA,CAAA,IAAA,CAAA,MAAA,CAAA;AACA;AACA,mBAPA;AAQA,kBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AAhBA;AAAA;;AAAA;AAAA;AAAA;AAkBA,gBAAA,OAAA,CAAA,GAAA;;AAlBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoBA,KA3IA;AA8IA;AACA,IAAA,SA/IA,uBA+IA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBACA,MAAA,CAAA,QAAA,CAAA,QADA;AAAA;AAAA;AAAA;;AAEA,gBAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,kBAAA;;AAFA;;AAAA;AAKA,gBAAA,MAAA,CAAA,IAAA,CAAA,QAAA,GAAA,MAAA,CAAA,QAAA,CAAA,QAAA;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,QAAA,GAAA,MAAA,CAAA,QAAA,CAAA,QAAA;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,GAAA,MAAA,CAAA,QAAA,CAAA,MAAA;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,KAAA,GAAA,MAAA,CAAA,QAAA,CAAA,KAAA;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,GAAA,MAAA,CAAA,QAAA,CAAA,MAAA;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,GAAA,MAAA,CAAA,QAAA,CAAA,MAAA;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,IAAA,GAAA,MAAA,CAAA,QAAA,CAAA,IAAA;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,GAAA,MAAA,CAAA,QAAA,CAAA,MAAA;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,IAAA,GAAA,MAAA,CAAA,QAAA,CAAA,IAAA;AACA,gBAAA,MAAA,CAAA,KAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,gBAAA,MAAA,CAAA,IAAA,GAAA,IAAA;;AAhBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA,KAhKA;AAkKA;AACA,IAAA,SAnKA,qBAmKA,GAnKA,EAmKA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,KAAA,GAAA,SAAA;AACA,gBAAA,MAAA,CAAA,UAAA,GAAA,KAAA;AACA,gBAAA,MAAA,CAAA,IAAA,mCAAA,GAAA;AACA,gBAAA,MAAA,CAAA,IAAA,GAAA,IAAA;;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,KAxKA;AA0KA;AACA,IAAA,OA3KA,mBA2KA,GA3KA,EA2KA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,KAAA,GAAA,MAAA;AACA,gBAAA,MAAA,CAAA,IAAA,mCAAA,GAAA;AACA,gBAAA,MAAA,CAAA,UAAA,GAAA,IAAA;AACA,gBAAA,MAAA,CAAA,IAAA,GAAA,IAAA;;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,KAhLA;AAmLA,IAAA,OAnLA,qBAmLA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,MAAA,CAAA,KAAA,CAAA,MAAA,EAAA,QAAA;AAAA,mGAAA,kBAAA,KAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA,iCACA,KADA;AAAA;AAAA;AAAA;;AAAA;;AAGA,gCAAA,MAAA,CAAA,IAAA,CAAA,IAAA,KAAA,IAAA,EAAA;AACA,8BAAA,YADA,GACA,MAAA,CAAA,KAAA,CAAA,UAAA,CAAA,eAAA,GAAA,CAAA,CADA;AAEA,8BAAA,MAAA,CAAA,IAAA,CAAA,QAAA,GAAA,YAAA,CAAA,KAAA;AACA,6BAHA,MAGA;AACA,8BAAA,MAAA,CAAA,IAAA,CAAA,QAAA,GAAA,MAAA,CAAA,IAAA,CAAA,MAAA;AACA;;AARA;AAAA,mCAUA,4BAAA,MAAA,CAAA,IAAA,CAVA;;AAAA;AAAA;AAUA,4BAAA,IAVA,uBAUA,IAVA;;AAWA,gCAAA,IAAA,KAAA,MAAA,EAAA;AACA,8BAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AACA;;AAbA;AAAA;;AAAA;AAAA;AAAA;AAeA,4BAAA,OAAA,CAAA,GAAA;;AAfA;AAAA;AAAA,mCAiBA,MAAA,CAAA,OAAA,EAjBA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAA;;AAAA;AAAA;AAAA;AAAA,oBADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsBA,KAzMA;AA2MA;AACA,IAAA,SA5MA,uBA4MA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBACA,OAAA,CAAA,UAAA,CAAA,MAAA,GAAA,CADA;AAAA;AAAA;AAAA;;AAEA,gBAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,aAAA;;AAFA;;AAAA;AAKA,gBAAA,GALA,GAKA,OAAA,CAAA,UAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,yBAAA,IAAA,CAAA,KAAA;AACA,iBAFA,CALA;;AAQA,gBAAA,OAAA,CAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,kBAAA,iBAAA,EAAA,IADA;AAEA,kBAAA,gBAAA,EAAA,IAFA;AAGA,kBAAA,IAAA,EAAA;AAHA,iBAAA,EAKA,IALA,CAKA,YAAA;AACA,wCAAA,GAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,wBAAA,IAAA,SAAA,IAAA;;AACA,wBAAA,IAAA,KAAA,MAAA,EAAA;AACA,sBAAA,OAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,SADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;;AAIA,sBAAA,OAAA,CAAA,OAAA;AACA,qBANA,MAMA;AACA,sBAAA,OAAA,CAAA,QAAA,CAAA;AACA,wBAAA,IAAA,EAAA,OADA;AAEA,wBAAA,OAAA,EAAA;AAFA,uBAAA;AAIA;AACA,mBAbA;AAcA,iBApBA,EAqBA,KArBA,CAqBA,YAAA;AACA,kBAAA,OAAA,CAAA,QAAA,CAAA;AACA,oBAAA,IAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA;AAFA,mBAAA;AAIA,iBA1BA;;AA2BA,gBAAA,OAAA,CAAA,OAAA;;AAnCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoCA,KAhPA;AAkPA;AACA,IAAA,cAnPA,4BAmPA;AACA,WAAA,IAAA,GAAA,KAAA;AACA,WAAA,IAAA,GAAA,EAAA;AACA,KAtPA;AAwPA,IAAA,YAxPA,wBAwPA,IAxPA,EAwPA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,KA1PA;AA4PA,IAAA,WA5PA,uBA4PA,MA5PA,EA4PA;AACA,UAAA,SAAA,GAAA,EAAA,CADA,CACA;;AACA,eAAA,YAAA,CAAA,YAAA,EAAA,KAAA,EAAA;AACA,QAAA,YAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,KAAA,MAAA,CAAA,KAAA,CAAA,EAAA;AACA,YAAA,SAAA,IAAA,IAAA,CAAA,KAAA,GAAA,GAAA;AACA,YAAA,KAAA;;AACA,gBAAA,IAAA,CAAA,QAAA,EAAA;AACA,cAAA,YAAA,CAAA,IAAA,CAAA,QAAA,EAAA,KAAA,CAAA;AACA;AACA;AACA,SARA;AAUA;;AAEA,MAAA,YAAA,CAAA,KAAA,WAAA,EAAA,CAAA,CAAA;AACA,aAAA,SAAA,CAAA,MAAA,CAAA,CAAA,EAAA,SAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA,KA7QA;AAgRA,IAAA,aAhRA,yBAgRA,MAhRA,EAgRA;AACA,UAAA,MAAA,GAAA,KAAA,WAAA,CAAA,MAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,KAAA,KAAA,MAAA;AAAA,OAAA,CAAA;;AACA,UAAA,MAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,eAAA,MAAA,CAAA,CAAA,CAAA,CAAA,KAAA;AACA,OAFA,MAEA;AACA,eAAA,EAAA;AACA;AACA;AAvRA;AAlGA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row :gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card class=\"aside_height\" shadow=\"never\">\n          <div slot=\"header\" class=\"clearfix\" style=\"text-align: center\">\n            <span>评价设备</span>\n          </div>\n          <div style=\"overflow: auto; height: 84vh\">\n            <el-tree\n                highlight-current\n                :data=\"treedata\"\n                :props=\"defaultProps\"\n                @node-click=\"handleNodeClick\"\n            ></el-tree>\n          </div>\n        </el-card>\n      </el-col>\n      <el-col :span=\"20\">\n        <el-white>\n          <!--          <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\" type=\"card\">-->\n          <!--            <el-tab-pane label=\"缺陷\" name=\"qx\">-->\n          <!--            </el-tab-pane>-->\n          <!--            <el-tab-pane label=\"试验\" name=\"sy\">-->\n          <!--            </el-tab-pane>-->\n          <!--          </el-tabs>-->\n          <el-white class=\"button-group\">\n            <el-button\n                type=\"primary\"\n                icon=\"el-icon-plus\"\n                @click=\"getInster\"\n                :disabled=\"addDisabled\"\n            >新增\n            </el-button\n            >\n            <el-button\n                type=\"danger\"\n                icon=\"el-icon-delete\"\n                @click=\"deleteRow\"\n            >删除\n            </el-button\n            >\n          </el-white>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"selectChange\"\n            height=\"78vh\"\n          />\n        </el-white>\n        <!--新增、修改、详情弹框-->\n        <el-dialog\n            :title=\"title\"\n            v-dialogDrag\n            :visible.sync=\"show\"\n            width=\"50%\"\n            append-to-body\n            @close=\"getInsterClose\"\n        >\n          <el-form label-width=\"130px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n            <el-row :gutter=\"24\" class=\"pull-left\">\n              <el-col :span=\"12\">\n                <el-form-item label=\"评价设备类型：\" prop=\"sblxmc\">\n                  <el-input v-model=\"form.sblxmc\" disabled/>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"部件名称：\" prop=\"sbbjmc\">\n                  <el-input v-model=\"form.sbbjmc\" disabled/>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"状态量名称：\" prop=\"ztlmc\">\n                  <el-input v-model=\"form.ztlmc\" disabled/>\n                </el-form-item>\n              </el-col>\n              <el-col :span=\"12\">\n                <el-form-item label=\"状态量信息点：\" prop=\"ztlxxdmc\">\n                  <el-input v-model=\"form.ztlxxdmc\" disabled/>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row :gutter=\"24\">\n              <el-col :span=\"24\">\n                <el-form-item label=\"关联分类依据：\" prop=\"ztxxbm\" v-if=\"form.xxlx==='qx'\">\n                  <el-cascader ref=\"myCascader\" :options=\"flyjOptions\" v-model=\"form.ztxxbm\" style=\"width: 100%\"\n                               filterable\n                               clearable></el-cascader>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row :gutter=\"24\">\n              <el-col :span=\"24\">\n                <el-form-item label=\"关联试验项目：\" prop=\"ztxxbm\" v-if=\"form.xxlx==='sy'\">\n                  <el-select v-model=\"form.ztxxbm\" style=\"width: 100%\" filterable clearable>\n                    <el-option\n                        v-for=\"item in syxmOptions\"\n                        :key=\"item.value\"\n                        :label=\"item.label\"\n                        :value=\"item.value\">\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </el-row>\n            <el-row :gutter=\"24\">\n              <el-col :span=\"24\">\n                <el-form-item label=\"处理规则：\" prop=\"clgz\" v-if=\"form.xxlx==='sy'\">\n                  <el-input  type=\"textarea\" v-model=\"form.clgz\" v-on:click.native=\"textareaClick\"/>\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </el-form>\n          <div slot=\"footer\" class=\"dialog-footer\">\n            <el-button @click=\"getInsterClose\">关 闭</el-button>\n            <el-button type=\"primary\" @click=\"saveRow\">保 存</el-button>\n          </div>\n        </el-dialog>\n\n        <el-dialog title=\"脚本维护\" :visible.sync=\"isShowJb\" width=\"80%\" append-to-body @close=\"jbClose\" v-dialogDrag>\n          <!--  脚本维护框  -->\n          <gswh @setJbVal=\"setJbVal\" :jb=\"form.clgz\" :syxm=\"form.ztxxbm\" @jbClose=\"jbClose\"></gswh>\n        </el-dialog>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n<script>\nimport {getSblxAndSbbjTree} from \"@/api/dagangOilfield/bzgl/sbztpjbzk/ztlmxwh\";\nimport {getFlyjTree, getPageList, getSybmOptions, remove, saveOrUpdate} from \"@/api/dagangOilfield/bzgl/sbztpjbzk/ztxxsjgl\";\nimport gswh from \"@/views/dagangOilfield/bzgl/sbztpjbzk/gswh\";\nexport default {\n  name: \"ztxxsjglpz\",\n  components: {gswh},\n  data() {\n    return {\n      isShowJb:false,//是否显示脚本维护框\n      syxmOptions: [],\n      flyjOptions: [],\n      //tab页名称\n      activeName: 'qx',\n      //树结构\n      defaultProps: {\n        children: \"children\",\n        label: \"label\",\n      },\n      //设备类型编码\n      sblxbm: \"\",\n      //新增按钮form表单\n      form: {},\n      title: \"\",\n      show: false,\n      filterInfo: {\n        data: {\n          ywdwArr: [],\n        },\n        fieldList: [\n          {\n            label: \"设备类型\",\n            type: \"select\",\n            value: \"roleName\",\n            multiple: true,\n            options: [],\n          },\n        ],\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true,\n        },\n        tableData: [],\n        tableHeader: [\n          {prop: \"sblxmc\", label: \"评价导则设备类型\", minWidth: '100'},\n          {prop: \"sbbjmc\", label: \"部件名称\"},\n          {prop: \"ztlmc\", label: \"状态量名称\"},\n          {prop: \"ztlxxdmc\", label: \"状态量信息点\"},\n          {prop: \"ztxxbmmc\", label: \"关联项\", minWidth: '240'},\n          {\n            prop: \"operation\",\n            label: \"操作\",\n            minWidth: \"130px\",\n            style: {display: \"block\"},\n            //操作列固定再右侧\n            fixed: \"right\",\n            operation: [\n              {name: \"修改\", clickFun: this.updateRow},\n              {name: \"详情\", clickFun: this.getInfo},\n            ],\n          },\n        ],\n      },\n      queryztlmxwhParam: {},\n      selectRows: [],\n      //点击树节点赋值\n      treeForm: {},\n      //组织树\n      treedata: [],\n      //新增按钮控制\n      addDisabled: true,\n      bjmcList: [],\n      rules: {\n        ztxxbm: [\n          {required: true, message: \"不能为空\", trigger: \"blur\"},\n        ],\n      },\n\n      isDisabled: false,\n      //选中行数据\n      rowData: {},\n      //表单选中数据\n      selectedRowDataArr: [],\n      //状态量信息点新增\n      isShowParamsAndParts: false,\n    };\n  },\n  create() {\n  },\n\n  mounted() {\n    //列表查询\n    this.getTreeNode();\n  },\n  methods: {\n    //设置脚本值\n    setJbVal(val){\n      this.form.clgz = val;\n    },\n    jbClose(){\n      this.isShowJb = false;\n    },\n    textareaClick(){\n      if (this.form.ztxxbm){\n        this.isShowJb = true;\n      }else {\n        this.$message.error(\"请先选择试验项目\")\n      }\n\n    },\n    //获取树节点数据\n    getTreeNode() {\n      getSblxAndSbbjTree({type: \"ztlxxglpz\"}).then((res) => {\n        if (res.code === \"0000\") {\n          this.treedata = res.data;\n        }\n      });\n    },\n    async getFlyjTree() {\n      if (this.sblxbm) {\n        let qxlb = '3';\n        if (this.sblxbm.indexOf(\"bd\") > -1) {\n          qxlb = '1'\n        }\n        await getFlyjTree({sblx: this.sblxbm, qxlb: qxlb}).then((res) => {\n          if (res.code === \"0000\") {\n            this.flyjOptions = res.data;\n          }\n        });\n      }\n    },\n\n    async getSybmOptions() {\n      if (this.sblxbm) {\n        await getSybmOptions({sblxbm: this.sblxbm, isMpSyxm: 1}).then((res) => {\n          if (res.code === \"0000\") {\n            this.syxmOptions = res.data;\n          }\n        });\n      }\n    },\n\n    //树节点点击事件\n    async handleNodeClick(data, node) {\n      this.addDisabled = true\n      this.queryztlmxwhParam.sblx = \"\";\n      this.queryztlmxwhParam.sbbjId = \"\";\n      this.queryztlmxwhParam.ztlzbm = \"\";\n      this.queryztlmxwhParam.ztlxxdId = \"\";\n      this.queryztlmxwhParam.xxlx = \"\";\n\n      this.treeForm.ztlxxdId = \"\";\n      this.treeForm.ztlzbm = \"\";\n      this.treeForm.sbbjId = \"\";\n      this.treeForm.sblx = \"\";\n      if (data.nodeLevel === \"1\") {\n        this.sblxbm = data.id;\n        await this.getFlyjTree()\n        await this.getSybmOptions()\n        await this.getData();\n      }\n\n      if (data.nodeLevel === \"2\") {\n        this.queryztlmxwhParam.sbbjId = data.id;\n        this.sblxbm = node.parent.data.id;\n        await this.getFlyjTree()\n        await this.getSybmOptions()\n        await this.getData();\n      }\n\n      if (data.nodeLevel === \"3\") {\n        this.queryztlmxwhParam.xxlx = data.id;\n        this.queryztlmxwhParam.sbbjId = node.parent.data.id;\n        this.sblxbm = node.parent.parent.data.id;\n        await this.getFlyjTree()\n        await this.getSybmOptions()\n        await this.getData();\n      }\n\n      if (data.nodeLevel === \"4\") {\n        this.queryztlmxwhParam.xxlx = node.parent.data.id;\n        this.queryztlmxwhParam.ztlzbm = data.id;\n        this.queryztlmxwhParam.sbbjId = node.parent.parent.data.id;\n        this.sblxbm = node.parent.parent.parent.data.id\n        await this.getFlyjTree()\n        await this.getSybmOptions()\n        await this.getData();\n      }\n\n      if (data.nodeLevel === \"5\") {\n        this.queryztlmxwhParam.xxlx = node.parent.parent.data.id;\n        this.queryztlmxwhParam.ztlxxdId = data.id;\n        this.queryztlmxwhParam.sbbjId = node.parent.parent.parent.data.id;\n        this.queryztlmxwhParam.ztlzbm = node.parent.data.id;\n        this.sblxbm = node.parent.parent.parent.parent.data.id\n        await this.getFlyjTree()\n        await this.getSybmOptions()\n        this.treeForm.ztlxxdId = data.id;\n        this.treeForm.ztlxxdmc = data.label;\n        this.treeForm.ztlzbm = node.parent.data.id;\n        this.treeForm.ztlmc = node.parent.data.label;\n        this.treeForm.xxlx = node.parent.parent.data.id;\n        this.treeForm.sbbjId = node.parent.parent.parent.data.id;\n        this.treeForm.sbbjmc = node.parent.parent.parent.data.label;\n        this.treeForm.sblx = node.parent.parent.parent.parent.data.id;\n        this.treeForm.sblxmc = node.parent.parent.parent.parent.data.label;\n        this.addDisabled = false;\n        await this.getData();\n      }\n    }\n    ,\n\n    //列表查询\n    async getData(params) {\n      try {\n        const param = {...this.queryztlmxwhParam, ...params};\n        param.sblx = this.sblxbm;\n        const {data, code} = await getPageList(param);\n        if (code === \"0000\") {\n          data.records.forEach(item => {\n            if (item.xxlx === 'qx') {\n              item.ztxxbm = JSON.parse(item.ztxxbm.replace(/'/g, \"\\\"\"));\n              item.ztxxbmmc = this.getZtxxbmMc(item.ztxxbm);\n            } else {\n              item.ztxxbmmc = this.getZtxxbmSyMc(item.ztxxbm);\n            }\n          })\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    }\n    ,\n\n    //新增\n    async getInster() {\n      if (!this.treeForm.ztlxxdId) {\n        this.$message.warning(\"请选择左侧对应状态量信息点！！！\");\n        return;\n      }\n      this.form.ztlxxdId = this.treeForm.ztlxxdId\n      this.form.ztlxxdmc = this.treeForm.ztlxxdmc\n      this.form.ztlzbm = this.treeForm.ztlzbm\n      this.form.ztlmc = this.treeForm.ztlmc\n      this.form.sbbjId = this.treeForm.sbbjId\n      this.form.sbbjmc = this.treeForm.sbbjmc\n      this.form.sblx = this.treeForm.sblx\n      this.form.sblxmc = this.treeForm.sblxmc\n      this.form.xxlx = this.treeForm.xxlx\n      this.title = \"新增\";\n      this.isDisabled = false;\n      this.show = true;\n    }\n    ,\n    //编辑按钮\n    async updateRow(row) {\n      this.title = \"状态量模型修改\";\n      this.isDisabled = false;\n      this.form = {...row};\n      this.show = true;\n    }\n    ,\n    //详情按钮\n    async getInfo(row) {\n      this.title = \"详情查看\";\n      this.form = {...row};\n      this.isDisabled = true;\n      this.show = true;\n    }\n    ,\n\n    async saveRow() {\n      await this.$refs['form'].validate(async valid => {\n        if (valid) {\n          try {\n            if (this.form.xxlx === \"qx\") {\n              let checkedNodes = this.$refs.myCascader.getCheckedNodes()[0];\n              this.form.ztxxbmId = checkedNodes.value;\n            } else {\n              this.form.ztxxbmId = this.form.ztxxbm;\n            }\n\n            let {code} = await saveOrUpdate(this.form);\n            if (code === \"0000\") {\n              this.$message.success(\"操作成功\");\n            }\n          } catch (e) {\n            console.log(e);\n          }\n          await this.getData();\n          // this.show = false;\n        }\n      })\n    }\n    ,\n    //删除按钮\n    async deleteRow() {\n      if (this.selectRows.length < 1) {\n        this.$message.warning(\"请选择正确的数据！！！\");\n        return;\n      }\n      let ids = this.selectRows.map((item) => {\n        return item.objId;\n      });\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      })\n          .then(() => {\n            remove(ids).then(({code}) => {\n              if (code === \"0000\") {\n                this.$message({\n                  type: \"success\",\n                  message: \"删除成功!\",\n                });\n                this.getData();\n              } else {\n                this.$message({\n                  type: \"error\",\n                  message: \"删除失败!\",\n                });\n              }\n            });\n          })\n          .catch(() => {\n            this.$message({\n              type: \"info\",\n              message: \"已取消删除\",\n            });\n          });\n      this.getData();\n    }\n    ,\n    //关闭弹窗\n    getInsterClose() {\n      this.show = false;\n      this.form = {};\n    }\n    ,\n    selectChange(rows) {\n      this.selectRows = rows;\n    }\n    ,\n    getZtxxbmMc(ztxxbm) {\n      let returnStr = \"\"; // 存放结果\n      function childrenEach(childrenData, depth) {\n        childrenData.forEach(item => {\n          if (item.value === ztxxbm[depth]) {\n            returnStr += item.label + \"/\";\n            depth++;\n            if (item.children) {\n              childrenEach(item.children, depth);\n            }\n          }\n        })\n\n      }\n\n      childrenEach(this.flyjOptions, 0);\n      return returnStr.substr(0, returnStr.length - 1);\n    }\n    ,\n\n    getZtxxbmSyMc(ztxxbm) {\n      let filter = this.syxmOptions.filter(item => item.value === ztxxbm);\n      if (filter.length > 0) {\n        return filter[0].label\n      } else {\n        return \"\"\n      }\n    }\n    ,\n  },\n};\n</script>\n\n<style lang='scss' scoped>\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  max-height: 79vh;\n  overflow: auto;\n}\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk"}]}