{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\dataChart.vue?vue&type=style&index=0&id=77e3ae4d&scoped=true&lang=scss&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\dataChart.vue", "mtime": 1706897323432}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouZGVmYXVsdENscyB7DQogIHBhZGRpbmctdG9wOiA1cHg7DQogIHBhZGRpbmctYm90dG9tOiAxMHB4Ow0KfQ0K"}, {"version": 3, "sources": ["dataChart.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsKA;AACA;AACA;AACA", "file": "dataChart.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk", "sourcesContent": ["<template>\r\n  <div class=\"defaultCls\">\r\n    <el-white class=\"titleCls_1\">\r\n      <el-row>\r\n        <el-col>\r\n          <div>\r\n            <el-divider direction=\"vertical\" class=\"vertical_d\"></el-divider>\r\n            <span class=\"tjfx_title\">{{ title }}</span>\r\n            <el-divider></el-divider>\r\n          </div>\r\n        </el-col>\r\n        <el-col>\r\n          <div ref=\"thisChart\" class=\"tjHeight3\"></div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-white>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from \"echarts\";\r\nimport { getCellData } from \"@/api/dagangOilfield/bzgl/sybglr\";\r\nexport default {\r\n  name: \"dataChart\",\r\n  props: {\r\n    cellId: {\r\n      type: String\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      title: \"试验数据对比\",\r\n      tjData: [],\r\n      tjx: [], //统计项\r\n      chart: null //统计图对象\r\n    };\r\n  },\r\n  created() {},\r\n  mounted() {\r\n    this.$nextTick(() => {});\r\n  },\r\n  methods: {\r\n    //查询统计数据\r\n    async getTjData() {\r\n      this.tjx = [];\r\n      this.tjData = [];\r\n      //查询数据\r\n      let params = {\r\n        cellId: this.cellId,\r\n        queryNum: \"10\"\r\n      };\r\n      getCellData(params).then(res => {\r\n        if (res.data.length > 1) {\r\n          res.data.forEach(item => {\r\n            let value = Number(item[Object.keys(item)[1]]);\r\n            if (value) {\r\n              this.tjData.push(value);\r\n              this.tjx.push(item[Object.keys(item)[0]].substring(0, 10));\r\n            }\r\n          });\r\n          if (res.data.length != this.tjData.length) {\r\n            this.$message.warning(\"存在空或异常的试验数值，已忽略\");\r\n          }\r\n          console.log(\"处理后的data\",this.tjData);\r\n          //展示统计图\r\n          this.showCharts();\r\n        } else {\r\n          this.$message.info(\"试验次数不足，无法对比\");\r\n        }\r\n      });\r\n    },\r\n    //初始化统计图\r\n    showCharts() {\r\n      let bar_dv = this.$refs.thisChart;\r\n      let myChart = echarts.init(bar_dv);\r\n      this.chart = myChart;\r\n\r\n      let option;\r\n      option = {\r\n        color: [\r\n          \"#4992ff\",\r\n          \"#7cffb2\",\r\n          \"#fddd60\",\r\n          \"#ff6e76\",\r\n          \"#58d9f9\",\r\n          \"#05c091\",\r\n          \"#ff8a45\",\r\n          \"#8d48e3\",\r\n          \"#dd79ff\"\r\n        ],\r\n        tooltip: {\r\n          trigger: \"axis\",\r\n          axisPointer: {\r\n            type: \"cross\",\r\n            crossStyle: {\r\n              color: \"#999\"\r\n            }\r\n          }\r\n        },\r\n        toolbox: {\r\n          feature: {\r\n            magicType: { show: true, type: [\"line\", \"bar\"] },\r\n            restore: { show: true },\r\n            saveAsImage: { show: true }\r\n          }\r\n        },\r\n        legend: {\r\n          top: \"left\"\r\n        },\r\n        xAxis: [\r\n          {\r\n            type: \"category\",\r\n            data: this.tjx,\r\n            axisPointer: {\r\n              type: \"shadow\"\r\n            },\r\n            axisLabel: { interval: 0, rotate: 25 }\r\n          }\r\n        ],\r\n        yAxis: [\r\n          {\r\n            type: \"value\",\r\n            min: 0,\r\n            axisLabel: {\r\n              formatter: \"{value}\"\r\n            }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            name: \"试验数值\",\r\n            type: \"line\",\r\n            barWidth: 25,\r\n            label: {\r\n              show: true,\r\n              position: \"top\"\r\n            },\r\n            tooltip: {\r\n              valueFormatter: function(value) {\r\n                return value;\r\n              }\r\n            },\r\n            data: this.tjData,\r\n            itemStyle: {\r\n              borderRadius: 5,\r\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: \"#abf1e0\" },\r\n                { offset: 1, color: \"#14c8d4\" }\r\n              ])\r\n            }\r\n          }\r\n        ]\r\n      };\r\n      option && myChart.setOption(option);\r\n    }\r\n  },\r\n  watch: {\r\n    cellId(newVal) {\r\n      if (newVal) {\r\n        this.getTjData();\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.defaultCls {\r\n  padding-top: 5px;\r\n  padding-bottom: 10px;\r\n}\r\n</style>\r\n"]}]}