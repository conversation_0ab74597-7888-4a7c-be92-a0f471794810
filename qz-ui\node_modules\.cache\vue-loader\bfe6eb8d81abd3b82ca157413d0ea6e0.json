{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\gqjgl\\pdgqj.vue?vue&type=template&id=27af0b12&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\gqjgl\\pdgqj.vue", "mtime": 1706897324701}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}