{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\bzgl\\ysbzk\\ysbzk.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\bzgl\\ysbzk\\ysbzk.js", "mtime": 1706897313865}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/bzgl/ysbzk/ysbzk.js"], "names": ["baseUrl", "getBzYsbzzb", "params", "api", "requestPost", "saveOrUpdateBzYsbzzb", "deleteBzYsbzzb", "ids", "getBzYsbzmx", "saveOrUpdateBzYsbzmx", "deleteBzYsbzmx", "exportExcel", "url", "fileName"], "mappings": ";;;;;;;;;;;;;;;AAAA;;AAEA,IAAMA,OAAO,GAAG,cAAhB;AAEA;;;;;;AAKO,SAASC,WAAT,CAAqBC,MAArB,EAA6B;AAClC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,uBAA1B,EAAmDE,MAAnD,EAA2D,CAA3D,CAAP;AACD;AAED;;;;;;;AAKO,SAASG,oBAAT,CAA8BH,MAA9B,EAAsC;AAC3C,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,wBAA1B,EAAoDE,MAApD,EAA4D,CAA5D,CAAP;AACD;AAED;;;;;;;AAKO,SAASI,cAAT,CAAwBC,GAAxB,EAA6B;AAClC,SAAOJ,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,kBAA1B,EAA8CO,GAA9C,EAAmD,CAAnD,CAAP;AACD;AAED;;;;;;;AAKO,SAASC,WAAT,CAAqBN,MAArB,EAA6B;AAClC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,uBAA1B,EAAmDE,MAAnD,EAA2D,CAA3D,CAAP;AACD;AAED;;;;;;;AAKO,SAASO,oBAAT,CAA8BP,MAA9B,EAAsC;AAC3C,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,wBAA1B,EAAoDE,MAApD,EAA4D,CAA5D,CAAP;AACD;AAED;;;;;;;AAKO,SAASQ,cAAT,CAAwBH,GAAxB,EAA6B;AAClC,SAAOJ,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAG,0BAA1B,EAAsDO,GAAtD,EAA2D,CAA3D,CAAP;AACD;;AAEM,SAASI,WAAT,CAAqBC,GAArB,EAAyBV,MAAzB,EAAgCW,QAAhC,EAAyC;AAC9C,SAAOV,iBAAIQ,WAAJ,CAAgBX,OAAO,GAACY,GAAR,GAAY,cAA5B,EAA2CV,MAA3C,EAAkDW,QAAlD,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\n\nconst baseUrl = '/manager-api'\n\n/**\n * 获取验收标准主表数据\n * @param params 查询条件\n * @returns {Promise | Promise<unknown>}\n */\nexport function getBzYsbzzb(params) {\n  return api.requestPost(baseUrl + '/bzYsbzzb/getBzYsbzzb', params, 1)\n}\n\n/**\n * 保存或者更新验收标准主表数据\n * @param params 验收标准主表数据\n * @returns {Promise | Promise<unknown>}\n */\nexport function saveOrUpdateBzYsbzzb(params) {\n  return api.requestPost(baseUrl + '/bzYsbzzb/saveOrUpdate', params, 1)\n}\n\n/**\n * 删除验收标准主表数据\n * @param ids\n * @returns {Promise | Promise<unknown>}\n */\nexport function deleteBzYsbzzb(ids) {\n  return api.requestPost(baseUrl + '/bzYsbzzb/delete', ids, 1)\n}\n\n/**\n * 获取验收标准明细标数据\n * @param params 查询条件\n * @returns {Promise | Promise<unknown>}\n */\nexport function getBzYsbzmx(params) {\n  return api.requestPost(baseUrl + '/bzYsbzmx/getBzYsbzmx', params, 1)\n}\n\n/**\n * 新增修改验收标准明细表数据\n * @param params 验收标准明细表数据\n * @returns {Promise | Promise<unknown>}\n */\nexport function saveOrUpdateBzYsbzmx(params) {\n  return api.requestPost(baseUrl + '/bzYsbzmx/saveOrUpdate', params, 1)\n}\n\n/**\n * 删除验收标准明细表数据\n * @param ids 明细表id数组\n * @returns {Promise | Promise<unknown>}\n */\nexport function deleteBzYsbzmx(ids) {\n  return api.requestPost(baseUrl + '/bzYsbzmx/deleteBzYsbzmx', ids, 1)\n}\n\nexport function exportExcel(url,params,fileName){\n  return api.exportExcel(baseUrl+url+'/exportExcel',params,fileName)\n}\n"]}]}