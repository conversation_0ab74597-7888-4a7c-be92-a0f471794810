{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sympk\\mpxqInfo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sympk\\mpxqInfo.vue", "mtime": 1706897323738}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IExvYWRpbmcgfSBmcm9tICJlbGVtZW50LXVpIjsKaW1wb3J0IHsKICByZXNldENlbGxzLAogIGNyZWF0ZVRhYmxlLAogIG1lcmdlQ2VsbHMsCiAgZWRpdENlbGxzLAogIGdldENlbGxzLAp9IGZyb20gIkAvYXBpL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvc3ltcGsvc3ltcEluZm8iOwppbXBvcnQgeyBnZXRUYWJsZSB9IGZyb20gIkAvYXBpL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvc3ltcGsvc3ltcGsiOwoKZXhwb3J0IGRlZmF1bHQgewogIHByb3BzOiB7CiAgICBtcERhdGE6IHsKICAgICAgdHlwZTogT2JqZWN0LAogICAgfSwKICAgIG14RGF0YTogewogICAgICB0eXBlOiBBcnJheSwKICAgIH0sCiAgfSwKICBuYW1lOiAibXB4cUluZm8iLAogIGluamVjdDogWyJyZWxvYWQiXSwgLy9pbmplY3Tms6jlhaXmoLnnu4Tku7bnmoRyZWxvYWTmlrnms5UKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgLy/liJ3lp4vooajmoLznmoTooYzmlbAg5YiX5pWwCiAgICAgIGhzOiAiIiwKICAgICAgbHM6ICIiLAogICAgICAvL+WIneWni+WQiOW5tuihjOaVsCDliJfmlbAKICAgICAgYWRkaHM6ICIiLAogICAgICBhZGRsczogIiIsCiAgICAgIC8v6YCJ5Lit5ZCI5bm26KGM44CB5YiX55qEdHIKICAgICAgY2hhbmdlVHI6ICIiLAogICAgICAvL+afpeivouadoeS7tgogICAgICBwYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxLAogICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICBtcGlkOiAiIiwKICAgICAgICB6eTogIiIsCiAgICAgICAgc2JseGJtOiAiIiwKICAgICAgfSwKICAgICAgdGl0bGU6ICLljZXlhYPmoLzlsZ7mgKflrprkuYkiLAogICAgICBmb3JtOiB7CiAgICAgICAgb2JqSWQ6IHVuZGVmaW5lZCwKICAgICAgICByZWFkb25seTogdW5kZWZpbmVkLAogICAgICAgIG5ybHg6IHVuZGVmaW5lZCwKICAgICAgICBucmJzOiB1bmRlZmluZWQsCiAgICAgICAgbXBpZDogdW5kZWZpbmVkLAogICAgICAgIHJlYWRvbmx5TWM6IHVuZGVmaW5lZCwKICAgICAgfSwKICAgICAgbG9hZGluZzogbnVsbCwgLy/pga7nvanlsYIKICAgICAgdGRXaWR0aDogMCwgLy/kuIDkuKrljZXlhYPmoLzmiYDljaDlrr3luqYKICAgICAgdGRNYXA6IG5ldyBNYXAoKSwgLy/nlKjkuo7lrZjmlL7ooqvlkIjlubbmiJbmi4bliIbnmoTljZXlhYPmoLzvvIhrZXk65b2T5YmN54K55Ye755qE5Y2V5YWD5qC8LHZhbHVlOuiiq+WkhOeQhui/h+eahOWNleWFg+agvOaVsOe7hO+8iQogICAgICB0YWJsZURhdGE6IHRoaXMubXhEYXRhLCAvL+ihqOagvOaVsOaNrgogICAgICBzaG93OiBmYWxzZSwKICAgICAgY2VsbERhdGE6ICIiLAogICAgICBpc0Rpc2FibGVkOiBmYWxzZSwKICAgICAgaXNEaXNhYmxlZE46IGZhbHNlLAogICAgICBucmx4TGlzdDogWwogICAgICAgIHsgbGFiZWw6ICLpnZnmgIHmlofmnKwiLCB2YWx1ZTogIumdmeaAgeaWh+acrCIgfSwKICAgICAgICB7IGxhYmVsOiAi6ZOt54mM5a6e6aqM5pWw5o2uIiwgdmFsdWU6ICLpk63niYzlrp7pqozmlbDmja4iIH0sCiAgICAgIF0sCiAgICAgIHJlYWRvbmx5TGlzdDogWwogICAgICAgIHsgbGFiZWw6ICLmmK8iLCB2YWx1ZTogIlkiIH0sCiAgICAgICAgeyBsYWJlbDogIuWQpiIsIHZhbHVlOiAiTiIgfSwKICAgICAgXSwKICAgICAgcmVhZG9ubHl2YWx1OiAiIiwgLy/pgInmi6nlgLwKICAgICAgcmVhZG9ubHlTZWxlY3Q6ICIiLCAvL+mAieaLqeWAvOaYvuekuuWAvAogICAgfTsKICB9LAogIG1vdW50ZWQoKSB7CiAgICAvL+iOt+WPluihqOagvOWIneWni+ihjOaVsOWSjOWIl+aVsAogICAgdGhpcy5pbml0VGFibGVEYXRhKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvL+iOt+WPlumTreeJjOWGheWuueaVsOaNrgogICAgaW5pdFRhYmxlRGF0YSgpIHsKICAgICAgLy/liJ3lp4vljJbpga7nvanlsYIKICAgICAgdGhpcy5sb2FkaW5nID0gTG9hZGluZy5zZXJ2aWNlKHsKICAgICAgICB0ZXh0OiAi5Yqg6L295Lit77yM6K+356iN5ZCOLi4uIiwKICAgICAgICBiYWNrZ3JvdW5kOiAicmdiYSgxMDksMTA2LDEwNiwwLjM1KSIsCiAgICAgIH0pOwogICAgICB0aGlzLmhzID0gdHlwZW9mICh0aGlzLm1wRGF0YS5BSHMpICE9ICd1bmRlZmluZWQnP3RoaXMubXBEYXRhLkFIczp0aGlzLm1wRGF0YS5hSHM7CiAgICAgIHRoaXMubHMgPSB0eXBlb2YgKHRoaXMubXBEYXRhLkFMcykgIT0gJ3VuZGVmaW5lZCc/dGhpcy5tcERhdGEuQUxzOnRoaXMubXBEYXRhLmFMczsKCiAgICAgIC8v5pu05paw6L6T5YWl5qGG55qE5YC8CiAgICAgIHRoaXMudXBkYXRlSW5wdXRWYWx1ZShbImhzIiwgImxzIl0pOwogICAgICB0aGlzLnByb2Nlc3NUYWJsZSgpOwogICAgICB0aGlzLmxvYWRpbmcuY2xvc2UoKTsgLy/lhbPpl63pga7nvanlsYIKICAgIH0sCiAgICAvL+agueaNruihjOaVsOWSjOWIl+aVsOWIm+W7uuihqOagvAogICAgcHJvY2Vzc1RhYmxlKCkgewogICAgICB2YXIgdGJvZHkgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgibXB4cV9yaWdodCIpOwogICAgICBpZiAodGJvZHkgIT0gbnVsbCkgewogICAgICAgIHRib2R5LmlubmVySFRNTCA9ICIiOwogICAgICAgIGxldCBocyA9IHRoaXMuaHM7CiAgICAgICAgbGV0IGxzID0gdGhpcy5sczsKICAgICAgICB0aGlzLnRkV2lkdGggPSAxMDAgLyBOdW1iZXIobHMpOwogICAgICAgIGxldCBzdHIgPSAiIjsKICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGhzOyBpKyspIHsKICAgICAgICAgIGxldCB0ZW1wID0gIjx0cj4iOwogICAgICAgICAgZm9yIChsZXQgaiA9IDA7IGogPCB0aGlzLnRhYmxlRGF0YS5sZW5ndGg7IGorKykgewogICAgICAgICAgICBsZXQgaXRlbSA9IHRoaXMudGFibGVEYXRhW2pdOwogICAgICAgICAgICBsZXQgbnJicyA9IGl0ZW0ubnJicyA9PSBudWxsID8gIi0iIDogaXRlbS5ucmJzOwogICAgICAgICAgICBpZiAoaXRlbS5yb3dpbmRleCA9PT0gaS50b1N0cmluZygpKSB7CiAgICAgICAgICAgICAgdGVtcCArPQogICAgICAgICAgICAgICAgIjx0ZCBjbGFzcz0ndHJOYW1lJyBpZD0nIiArCiAgICAgICAgICAgICAgICBpdGVtLm9iaklkICsKICAgICAgICAgICAgICAgICInIHN0eWxlPSdtaW4td2lkdGg6NjBweDttYXgtd2lkdGg6MTgwcHg7d2lkdGg6ICIgKwogICAgICAgICAgICAgICAgdGhpcy50ZFdpZHRoICogaXRlbS5jb2xzcGFuICsKICAgICAgICAgICAgICAgICIlJyByb3dzcGFuPSciICsKICAgICAgICAgICAgICAgIGl0ZW0ucm93c3BhbiArCiAgICAgICAgICAgICAgICAiJyBjb2xzcGFuPSciICsKICAgICAgICAgICAgICAgIGl0ZW0uY29sc3BhbiArCiAgICAgICAgICAgICAgICAiJz4iICsKICAgICAgICAgICAgICAgIG5yYnMgKwogICAgICAgICAgICAgICAgIjwvdGQ+IjsKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgICAgdGVtcCArPSAiPC90cj4iOwogICAgICAgICAgc3RyICs9IHRlbXA7CiAgICAgICAgfQogICAgICAgIHRib2R5LmlubmVySFRNTCA9IHN0cjsKICAgICAgICAvLyAvL+e7meW+queOr+WHuuadpeeahOWNleWFg+agvOWKoOS4iueCueWHu+S6i+S7tgogICAgICAgIHRoaXMuYWRkQ2xpY2tFdmVudCgpOwogICAgICB9CiAgICB9LAogICAgLy/miYvliqjliJvlu7rooajmoLwKICAgIGNyZWF0ZVRhYmxlKCkgewogICAgICAvL+WIneWni+WMlumBrue9qeWxggogICAgICB0aGlzLmxvYWRpbmcgPSBMb2FkaW5nLnNlcnZpY2UoewogICAgICAgIHRleHQ6ICLliqDovb3kuK3vvIzor7fnqI3lkI4uLi4iLAogICAgICAgIGJhY2tncm91bmQ6ICJyZ2JhKDEwOSwxMDYsMTA2LDAuMzUpIiwKICAgICAgfSk7CiAgICAgIGxldCBwYXJhbXMgPSBKU09OLnN0cmluZ2lmeSh7CiAgICAgICAgb2JqSWQ6IHRoaXMubXBEYXRhLm9iaklkLCAvL+mTreeJjGlkCiAgICAgICAgYUhzOiBOdW1iZXIodGhpcy5ocyksIC8v6KGM5pWwCiAgICAgICAgYUxzOiBOdW1iZXIodGhpcy5scyksIC8v5YiX5pWwCiAgICAgICAgbGJiczogIkEiLCAvL+exu+WIq+agh+ivhu+8jOihqOekuuS/ruaUueeahEHooajmoLwKICAgICAgfSk7CiAgICAgIGNyZWF0ZVRhYmxlKHBhcmFtcykudGhlbigocmVzKSA9PiB7CiAgICAgICAgY29uc29sZS5sb2coJ+aJi+WKqOWIm+W7uuihqOagvCcscmVzKQogICAgICAgIGlmIChyZXMuY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICB0aGlzLnVwZGF0ZVRhYmxlKCk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuaTjeS9nOWksei0pSIpOwogICAgICAgICAgdGhpcy5sb2FkaW5nLmNsb3NlKCk7IC8v5YWz6Zet6YGu572p5bGCCiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvL+e7meW+queOr+WHuuadpeeahOWNleWFg+agvOWKoOS4iueCueWHu+S6i+S7tgogICAgYWRkQ2xpY2tFdmVudCgpIHsKICAgICAgbGV0IHRyQXJyID0gZG9jdW1lbnQuZ2V0RWxlbWVudHNCeUNsYXNzTmFtZSgidHJOYW1lIik7IC8v5LiN5Y+v57yW6L6R55qE5Y2V5YWD5qC8CiAgICAgIGxldCBpbnB1dEFyciA9IGRvY3VtZW50LmdldEVsZW1lbnRzQnlDbGFzc05hbWUoImlucHV0X2NscyIpOyAvL+WPr+e8lui+keeahOWNleWFg+agvAogICAgICBsZXQgdGhhdCA9IHRoaXM7CiAgICAgIGlmICh0ckFyciAhPSBudWxsKSB7CiAgICAgICAgLy/lvqrnjq/miYDmnInnmoR0cgogICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgdHJBcnIubGVuZ3RoOyBpKyspIHsKICAgICAgICAgIHRyQXJyW2ldLm9uY2xpY2sgPSBmdW5jdGlvbiAoKSB7CiAgICAgICAgICAgIHRoYXQuY2hhbmdlVHIgPSB0aGlzOwogICAgICAgICAgICB0aGF0LmFkZGhzID0gdGhhdC5jaGFuZ2VUci5yb3dTcGFuOwogICAgICAgICAgICB0aGF0LmFkZGxzID0gdGhhdC5jaGFuZ2VUci5jb2xTcGFuOwogICAgICAgICAgICB0aGF0LmNlbGxEYXRhID0gdGhhdC5nZXRDZWxsRWxlKHRoYXQuY2hhbmdlVHIuaWQpOwogICAgICAgICAgfTsKICAgICAgICB9CiAgICAgIH0KICAgICAgaWYgKGlucHV0QXJyICE9IG51bGwpIHsKICAgICAgICAvL+W+queOr+aJgOacieeahHRyCiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBpbnB1dEFyci5sZW5ndGg7IGkrKykgewogICAgICAgICAgaW5wdXRBcnJbaV0ub25jbGljayA9IGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgdGhhdC5jaGFuZ2VUciA9IHRoaXM7CiAgICAgICAgICAgIHRoYXQuYWRkaHMgPSB0aGF0LmNoYW5nZVRyLnJvd1NwYW47CiAgICAgICAgICAgIHRoYXQuYWRkbHMgPSB0aGF0LmNoYW5nZVRyLmNvbFNwYW47CiAgICAgICAgICB9OwogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIC8v5ZCI5bm25ouG5YiG5L+d5a2YCiAgICBzYXZlQ2hhbmdlVGFibGUoKSB7CiAgICAgIC8v5Yid5aeL5YyW6YGu572p5bGCCiAgICAgIHRoaXMubG9hZGluZyA9IExvYWRpbmcuc2VydmljZSh7CiAgICAgICAgdGV4dDogIuWKoOi9veS4re+8jOivt+eojeWQji4uLiIsCiAgICAgICAgYmFja2dyb3VuZDogInJnYmEoMTA5LDEwNiwxMDYsMC4zNSkiLAogICAgICB9KTsKICAgICAgLy/nnIvopoHlkIjlubbnmoTmmK/nrKzlh6DooYzlh6DliJfnmoR0ciAgIGlk5qC85byPICAwfDAgMHwxCiAgICAgIC8qbGV0IGlkID0gdGhpcy5jaGFuZ2VUci5pZDsKICAgICAgbGV0IGFkZGggPSBOdW1iZXIoaWQuc3BsaXQoJ3wnKVswXSk7ICAvL+WPluW9k+WJjeWFg+e0oOeahOihjAogICAgICBsZXQgYWRkbCA9IE51bWJlcihpZC5zcGxpdCgnfCcpWzFdKTsgIC8v5Y+W5b2T5YmN5YWD57Sg55qE5YiXCiAgICAgIGxldCBocyA9IE51bWJlcih0aGlzLmFkZGhzKTsvL+ihjOaVsAogICAgICBsZXQgbHMgPSBOdW1iZXIodGhpcy5hZGRscyk7Ly/liJfmlbAqLwoKICAgICAgbGV0IHBhcmFtcyA9IEpTT04uc3RyaW5naWZ5KHsKICAgICAgICBvYmpJZDogdGhpcy5jaGFuZ2VUci5pZCwKICAgICAgICByb3dzcGFuOiB0aGlzLmFkZGhzLAogICAgICAgIGNvbHNwYW46IHRoaXMuYWRkbHMsCiAgICAgIH0pOwogICAgICAvL+WFiOivt+axguaOpeWPo++8jOWmguaenOWQjuWPsOWPr+S7peaJp+ihjOWQiOW5tuaIluaLhuWIhu+8jOWImeWwhuacgOaWsOeahOihqOagvOaVsOaNruivt+axguWbnuadpei/m+ihjOWJjeerr+WxleekugogICAgICBtZXJnZUNlbGxzKHBhcmFtcykudGhlbigocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIHRoaXMudXBkYXRlVGFibGUoKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5pON5L2c5aSx6LSlIik7CiAgICAgICAgICB0aGlzLmxvYWRpbmcuY2xvc2UoKTsgLy/lhbPpl63pga7nvanlsYIKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8v5aSE55CG5ZCI5bm25oiW5ouG5YiGCiAgICBwcm9jZXNzVHIoaWRzKSB7CiAgICAgIC8v54K55Ye755qE5Y2V5YWD5qC8aWQKICAgICAgbGV0IGNsaWNrSWQgPSB0aGlzLmNoYW5nZVRyLmlkOwogICAgICBsZXQgYXJyMSA9IFtdOyAvL+mcgOimgemHjeaWsOiuvue9rm1hcOeahOaVsOe7hAogICAgICAvL+WmguaenOS5i+WJjeW3sue7j+WkhOeQhui/h+ivpeWNleWFg+agvCzliJnlhYjlsIblhbbov5jljp8KICAgICAgaWYgKHRoaXMudGRNYXAuaGFzKGNsaWNrSWQpKSB7CiAgICAgICAgdGhpcy50ZE1hcC5nZXQoY2xpY2tJZCkuZm9yRWFjaCgoaXRlbSkgPT4gewogICAgICAgICAgaWYgKGl0ZW0gIT0gbnVsbCkgewogICAgICAgICAgICB0aGlzLnJlc2V0Q2VsbChpdGVtKTsKICAgICAgICAgICAgaXRlbS5zdHlsZS5kaXNwbGF5ID0gInRhYmxlLWNlbGwiOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICAgIC8v5pON5L2c5a6M5ZCO5bCG5pWw5o2u5LuObWFw5Lit5Yig6ZmkCiAgICAgICAgdGhpcy50ZE1hcC5kZWxldGUoY2xpY2tJZCk7CiAgICAgIH0KICAgICAgbGV0IHByb2Nlc3NFbGUgPSBbXTsgLy/ooqvlpITnkIbnmoTlhYPntKAKCiAgICAgIC8v546w5bCG6L+e5bim5Y+X5b2x5ZON55qE5Y2V5YWD5qC86L+Y5Y6f77yM5YaN6L+b6KGM6ZqQ6JeP5aSE55CGCiAgICAgIGlmIChpZHMubGVuZ3RoID4gMCkgewogICAgICAgIC8v5omn6KGM6L+Y5Y6fCiAgICAgICAgaWRzLmZvckVhY2goKGlkMSkgPT4gewogICAgICAgICAgbGV0IGVsZSA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKGlkMSk7CiAgICAgICAgICAvL+WmguaenOatpOasoeWkhOeQhueahOWNleWFg+agvOS4reacieW3sue7j+iiq+WkhOeQhui/h+eahO+8jOWFiOWwhuWFtui/mOWOnwogICAgICAgICAgaWYgKHRoaXMudGRNYXAuaGFzKGlkMSkpIHsKICAgICAgICAgICAgdGhpcy50ZE1hcC5nZXQoaWQxKS5mb3JFYWNoKChpdGVtKSA9PiB7CiAgICAgICAgICAgICAgdGhpcy5yZXNldENlbGwoaXRlbSk7CiAgICAgICAgICAgICAgaXRlbS5zdHlsZS5kaXNwbGF5ID0gInRhYmxlLWNlbGwiOwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgLy/mk43kvZzlrozlkI7lsIbmlbDmja7ku45tYXDkuK3liKDpmaQKICAgICAgICAgICAgdGhpcy50ZE1hcC5kZWxldGUoaWQxKTsKICAgICAgICAgIH0KICAgICAgICAgIC8v5aSE55CG6KKr6L+e5bim55qE5bey57uP5ZCI5bm26L+H55qE5Y2V5YWD5qC8CiAgICAgICAgICBpZiAoZWxlKSB7CiAgICAgICAgICAgIGxldCBjbGFzc05hbWUgPSBlbGUuY2xhc3NOYW1lOwogICAgICAgICAgICBpZiAodGhpcy50ZE1hcC5oYXMoY2xhc3NOYW1lKSkgewogICAgICAgICAgICAgIGxldCBtZXJnZUNlbGwgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChjbGFzc05hbWUpOyAvL+iiq+i/nuW4pueahOW3sue7j+WQiOW5tui/h+eahGNlbGwKICAgICAgICAgICAgICBpZiAobWVyZ2VDZWxsKSB7CiAgICAgICAgICAgICAgICB0aGlzLnJlc2V0Q2VsbChtZXJnZUNlbGwpOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB0aGlzLnRkTWFwLmdldChjbGFzc05hbWUpLmZvckVhY2goKGl0ZW0pID0+IHsKICAgICAgICAgICAgICAgIC8v6ZyA6KaB5oqK5q2k5qyh6KaB6ZqQ6JeP55qE5Y2V5YWD5qC85o6S6Zmk5o6J77yM5LiN54S26ZqQ6JeP5a6M5LiL5qyh5b6q546v5Y+I5Lya5pS+5Ye65p2lCiAgICAgICAgICAgICAgICBpZiAoIWlkcy5pbmNsdWRlcyhpdGVtKSkgewogICAgICAgICAgICAgICAgICBpdGVtLnN0eWxlLmRpc3BsYXkgPSAidGFibGUtY2VsbCI7CiAgICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgICBhcnIxLnB1c2goaXRlbSk7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgLy/lpITnkIblrozmiJDlkI7vvIzmm7TmlrBtYXDkuK3nmoTlgLzvvIzlsIblpITnkIbov4fnmoTmjpLpmaTmjokKICAgICAgICAgICAgICBpZiAoYXJyMS5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgICAgICB0aGlzLnRkTWFwLnNldChjbGFzc05hbWUsIGFycjEpOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICAvL+aTjeS9nOWujOWQjuWwhuaVsOaNruS7jm1hcOS4reWIoOmZpAogICAgICAgICAgICAgICAgdGhpcy50ZE1hcC5kZWxldGUoY2xhc3NOYW1lKTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgICAvL+aJp+ihjOmakOiXjwogICAgICAgIGlkcy5mb3JFYWNoKChpZCkgPT4gewogICAgICAgICAgbGV0IGVsZSA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKGlkKTsKICAgICAgICAgIC8v5bCG5aSa5L2Z55qE5Y2V5YWD5qC86ZqQ6JePCiAgICAgICAgICBpZiAoZWxlKSB7CiAgICAgICAgICAgIHByb2Nlc3NFbGUucHVzaChlbGUpOyAvL+a3u+WKoOaVsOaNruS/neWtmOWIsG1hcOS4rQoKICAgICAgICAgICAgZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoaWQpLnN0eWxlLmRpc3BsYXkgPSAibm9uZSI7CiAgICAgICAgICAgIC8v5bCGY2xhc3NOYW1l6K6+572u57uZ6KKr5pON5L2c55qE5Y2V5YWD5qC877yM5pa55L6/5LiL5qyh5pyJ6L+e5bim5pON5L2c5pe25a+55Y2V5YWD5qC86L+b6KGM5aSE55CGCiAgICAgICAgICAgIGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKGlkKS5jbGFzc05hbWUgPSBjbGlja0lkOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICAgIC8v6YeN5paw6K6+572ubWFw5Lit55qE5YC8CiAgICAgICAgdGhpcy50ZE1hcC5zZXQoY2xpY2tJZCwgcHJvY2Vzc0VsZSk7CiAgICAgIH0KICAgIH0sCiAgICAvL+WPlua2iOabtOaUueeahOWQiOW5tuihjOOAgeWIl+aVsAogICAgY2xlYXJDaGFuZ2VUYWJsZSgpIHt9LAoKICAgIHNlbGVjdHZhbHVlKHZhbCkgewogICAgICBsZXQgb2JqID0ge307CiAgICAgIG9iaiA9IHRoaXMucmVhZG9ubHlMaXN0LmZpbmQoKGl0ZW0pID0+IHsKICAgICAgICByZXR1cm4gaXRlbS52YWx1ZSA9PT0gdmFsOwogICAgICB9KTsKICAgICAgaWYgKG9iai52YWx1ZSA9PT0gIk4iKSB7CiAgICAgICAgdGhpcy5mb3JtLm5ybHggPSAi6ZOt54mM5a6e6aqM5pWw5o2uIjsKICAgICAgICB0aGlzLmlzRGlzYWJsZWROID0gdHJ1ZTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLmZvcm0ubnJseCA9ICLpnZnmgIHmlofmnKwiOwogICAgICAgIHRoaXMuaXNEaXNhYmxlZE4gPSBmYWxzZTsKICAgICAgfQogICAgICB0aGlzLmlzRGlzYWJsZWQgPSB0cnVlOwogICAgICAvLyB0aGlzLmZvcm0ucmVhZG9ubHkgPSArb2JqLnZhbHVlOwogICAgfSwKCiAgICAvL+WFs+mXreW8ueeqlwogICAgZ2V0SW5zdGVyQ2xvc2UoKSB7CiAgICAgIHRoaXMuc2hvdyA9IGZhbHNlOwogICAgICB0aGlzLmZvcm0gPSB7fTsKICAgIH0sCgogICAgLy/ov5vooYzlkIjlubbmiJbmi4bliIbmk43kvZwKICAgIG1lcmdlVGFibGUoaHMsIGxzLCBhZGRoLCBhZGRsKSB7CiAgICAgIGlmIChocyA9PT0gMSkgewogICAgICAgIC8v5ZCI5bm25YiXCiAgICAgICAgaWYgKGxzID49IDEpIHsKICAgICAgICAgIHRoaXMubWVyZ2VDZWxscyhhZGRoLCBhZGRsLCBocywgbHMpOwogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICBpZiAoaHMgPiAxKSB7CiAgICAgICAgICAvL+WkmuihjAogICAgICAgICAgLy/lkIjlubbooYwKICAgICAgICAgIGlmIChscyA9PT0gMSkgewogICAgICAgICAgICB0aGlzLm1lcmdlUm93cyhhZGRoLCBhZGRsLCBocywgbHMpOwogICAgICAgICAgfSBlbHNlIGlmIChscyA+IDEpIHsKICAgICAgICAgICAgLy/lkIjlubblpJrooYzlpJrliJcKICAgICAgICAgICAgdGhpcy5tZXJnZVJvd3NBbmRDZWxscyhhZGRoLCBhZGRsLCBocywgbHMpOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgICAvL+imgeWQiOW5tueahOWNleWFg+agvOi/m+ihjOWQiOW5tgogICAgICB0aGlzLmNoYW5nZVRyLnN0eWxlLndpZHRoID0gdGhpcy50ZFdpZHRoICogbHMgKyAiJSI7IC8v6K6+572u5ZCI5bm25ZCO55qE5Y2V5YWD5qC85a695bqmCiAgICAgIHRoaXMuY2hhbmdlVHIucm93U3BhbiA9IHRoaXMuYWRkaHM7CiAgICAgIHRoaXMuY2hhbmdlVHIuY29sU3BhbiA9IHRoaXMuYWRkbHM7CiAgICB9LAogICAgLyoqCiAgICAgKiDnrKzkuIDnp43mg4XlhrXvvIzlkIjlubbliJfvvIjkuIDooYzlpJrliJfvvIkKICAgICAqIEBwYXJhbSBoIOW9k+WJjeWFg+e0oOaJgOWcqOihjAogICAgICogQHBhcmFtIGwg5b2T5YmN5YWD57Sg5omA5Zyo5YiXCiAgICAgKiBAcGFyYW0gaHMg6KaB5ZCI5bm255qE6KGM5pWwCiAgICAgKiBAcGFyYW0gbHMg6KaB5ZCI5bm255qE5YiX5pWwCiAgICAgKi8KICAgIG1lcmdlQ2VsbHMoaCwgbCwgaHMsIGxzKSB7CiAgICAgIGxldCByZW1vdmVJZHMgPSBbXTsgLy/opoHliKDpmaTnmoTlhYPntKDnmoRpZOaVsOe7hAogICAgICBsZXQgbHNfeGggPSBsczsgLy/opoHlvqrnjq/nmoTliJfmlbAKICAgICAgaWYgKGxzID4gdGhpcy5scyAtIGwpIHsKICAgICAgICAvL+S4jeiDvei2hei/h+WJqeS9meWPr+aTjeS9nOeahOWIl+aVsAogICAgICAgIGxzX3hoID0gdGhpcy5scyAtIGw7CiAgICAgIH0KICAgICAgZm9yIChsZXQgaSA9IDE7IGkgPCBsc194aDsgaSsrKSB7CiAgICAgICAgcmVtb3ZlSWRzLnB1c2goaCArICJ8IiArIChsICsgaSkpOwogICAgICB9CiAgICAgIC8v5Yig6Zmk5aSa5L2Z5Y2V5YWD5qC8CiAgICAgIHRoaXMucHJvY2Vzc1RyKHJlbW92ZUlkcyk7CiAgICB9LAogICAgLyoqCiAgICAgKiDnrKzkuoznp43mg4XlhrXvvIzlkIjlubbooYzvvIjlpJrooYzkuIDliJfvvIkKICAgICAqIEBwYXJhbSBoIOW9k+WJjeWFg+e0oOaJgOWcqOihjAogICAgICogQHBhcmFtIGwg5b2T5YmN5YWD57Sg5omA5Zyo5YiXCiAgICAgKiBAcGFyYW0gaHMg6KaB5ZCI5bm255qE6KGM5pWwCiAgICAgKiBAcGFyYW0gbHMg6KaB5ZCI5bm255qE5YiX5pWwCiAgICAgKi8KICAgIG1lcmdlUm93cyhoLCBsLCBocywgbHMpIHsKICAgICAgbGV0IHJlbW92ZUlkcyA9IFtdOyAvL+imgeWIoOmZpOeahOWFg+e0oOeahGlk5pWw57uECiAgICAgIGxldCBoc194aCA9IGhzOyAvL+imgeW+queOr+eahOihjOaVsAogICAgICBpZiAoaHMgPiB0aGlzLmhzIC0gaCkgewogICAgICAgIC8v5LiN6IO96LaF6L+H5Ymp5L2Z5Y+v5pON5L2c55qE6KGM5pWwCiAgICAgICAgaHNfeGggPSB0aGlzLmhzIC0gaDsKICAgICAgfQogICAgICBjb25zb2xlLmxvZygiaHNfeGgiLCBoc194aCk7CiAgICAgIGZvciAobGV0IGkgPSAxOyBpIDwgaHNfeGg7IGkrKykgewogICAgICAgIHJlbW92ZUlkcy5wdXNoKGggKyBpICsgInwiICsgbCk7CiAgICAgIH0KICAgICAgLy/liKDpmaTlpJrkvZnljZXlhYPmoLwKICAgICAgdGhpcy5wcm9jZXNzVHIocmVtb3ZlSWRzKTsKICAgIH0sCiAgICAvKioKICAgICAqIOesrOS4ieenjeaDheWGte+8jOWQiOW5tuWkmuihjOWkmuWIlwogICAgICogQHBhcmFtIGgg5b2T5YmN5YWD57Sg5omA5Zyo6KGMCiAgICAgKiBAcGFyYW0gbCDlvZPliY3lhYPntKDmiYDlnKjliJcKICAgICAqIEBwYXJhbSBocyDopoHlkIjlubbnmoTooYzmlbAKICAgICAqIEBwYXJhbSBscyDopoHlkIjlubbnmoTliJfmlbAKICAgICAqLwogICAgbWVyZ2VSb3dzQW5kQ2VsbHMoaCwgbCwgaHMsIGxzKSB7CiAgICAgIGxldCByZW1vdmVJZHMgPSBbXTsgLy/opoHliKDpmaTnmoTlhYPntKDnmoRpZOaVsOe7hAogICAgICBsZXQgcmVtb3ZlSWQgPSAiIjsKICAgICAgLy/lhYjlvqrnjq/ooYzvvIjku47lvZPliY3ooYzlvIDlp4vlvqrnjq/vvIkKICAgICAgZm9yIChsZXQgaiA9IDA7IGogPCBoczsgaisrKSB7CiAgICAgICAgLy/lvqrnjq/liJcKICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGxzOyBpKyspIHsKICAgICAgICAgIC8v5LuO5b2T5YmN5YiX5b6q546vCiAgICAgICAgICByZW1vdmVJZCA9IGggKyBqICsgInwiICsgKGwgKyBpKTsKICAgICAgICAgIC8v5bCG5b2T5YmN5Y2V5YWD5qC85o6S6Zmk5o6JCiAgICAgICAgICBpZiAocmVtb3ZlSWQgIT09IGggKyAifCIgKyBsKSB7CiAgICAgICAgICAgIHJlbW92ZUlkcy5wdXNoKHJlbW92ZUlkKTsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KICAgICAgLy/liKDpmaTlpJrkvZnljZXlhYPmoLwKICAgICAgdGhpcy5wcm9jZXNzVHIocmVtb3ZlSWRzKTsKICAgIH0sCiAgICAvL+abtOaWsOi+k+WFpeahhueahOWAvAogICAgdXBkYXRlSW5wdXRWYWx1ZShhcnJzKSB7CiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgYXJycy5sZW5ndGg7IGkrKykgewogICAgICAgIGxldCBlbGUgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChhcnJzW2ldKTsKICAgICAgICBpZiAoZWxlICE9IG51bGwgJiYgdHlwZW9mIGVsZSAhPSAidW5kZWZpbmVkIikgewogICAgICAgICAgc3dpdGNoIChhcnJzW2ldKSB7CiAgICAgICAgICAgIGNhc2UgImhzIjoKICAgICAgICAgICAgICBlbGUudmFsdWUgPSB0aGlzLmhzOwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlICJscyI6CiAgICAgICAgICAgICAgZWxlLnZhbHVlID0gdGhpcy5sczsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSAiYWRkaHMiOgogICAgICAgICAgICAgIGVsZS52YWx1ZSA9IHRoaXMuYWRkaHM7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgImFkZGxzIjoKICAgICAgICAgICAgICBlbGUudmFsdWUgPSB0aGlzLmFkZGxzOwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQogICAgfSwKICAgIC8v6YeN572u5Y2V5YWD5qC85YaF5a65CiAgICByZXNldFRhYmxlKCkgewogICAgICAvL+WIneWni+WMlumBrue9qeWxggogICAgICB0aGlzLmxvYWRpbmcgPSBMb2FkaW5nLnNlcnZpY2UoewogICAgICAgIHRleHQ6ICLliqDovb3kuK3vvIzor7fnqI3lkI4uLi4iLAogICAgICAgIGJhY2tncm91bmQ6ICJyZ2JhKDEwOSwxMDYsMTA2LDAuMzUpIiwKICAgICAgfSk7CiAgICAgIGxldCBvYmpJZCA9IHRoaXMuY2hhbmdlVHIuaWQ7CiAgICAgIGxldCBwYXJhbXMgPSB0aGlzLmdldENlbGxFbGUob2JqSWQpOwogICAgICByZXNldENlbGxzKHBhcmFtcykudGhlbigocmVzKSA9PiB7CiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIHRoaXMudXBkYXRlVGFibGUoKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5pON5L2c5aSx6LSlIik7CiAgICAgICAgfQogICAgICAgIHRoaXMubG9hZGluZy5jbG9zZSgpOyAvL+WFs+mXremBrue9qeWxggogICAgICB9KTsKICAgIH0sCiAgICAvL+WNleWFg+agvOWxnuaAp+e8lui+keW5tuS/neWtmAogICAgc2F2ZVRkVmFsdWUoKSB7CiAgICAgIHRoaXMuc2hvdyA9IHRydWU7CiAgICAgIC8v5Yid5aeL5YyW6YGu572p5bGCCiAgICAgIC8vIHRoaXMubG9hZGluZyA9IExvYWRpbmcuc2VydmljZSh7CiAgICAgIC8vICAgdGV4dDoi5Yqg6L295Lit77yM6K+356iN5ZCOLi4uIiwKICAgICAgLy8gICBiYWNrZ3JvdW5kOidyZ2JhKDEwOSwxMDYsMTA2LDAuMzUpJywKICAgICAgLy8gfSkKICAgICAgLy8gbGV0IG9iaklkID0gdGhpcy5jaGFuZ2VUci5pZDsKICAgICAgLy8gbGV0IHZhbCA9IHRoaXMuY2hhbmdlVHIuZ2V0RWxlbWVudHNCeVRhZ05hbWUoImlucHV0IilbMF0udmFsdWU7CiAgICAgIC8vIGxldCBwYXJhbXMgPSB0aGlzLmdldENlbGxFbGUob2JqSWQpOwogICAgICAvLyBwYXJhbXMubnJicyA9IHZhbDsKICAgICAgLy8gZWRpdENlbGxzKHBhcmFtcykudGhlbihyZXM9PnsKICAgICAgLy8gICBpZihyZXMuY29kZT09PScwMDAwJyl7CiAgICAgIC8vICAgICB0aGlzLnVwZGF0ZVRhYmxlKCk7CiAgICAgIC8vICAgfWVsc2V7CiAgICAgIC8vICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmk43kvZzlpLHotKUnKTsKICAgICAgLy8gICAgIHRoaXMubG9hZGluZyAuY2xvc2UoKTsvL+WFs+mXremBrue9qeWxggogICAgICAvLyAgIH0KICAgICAgLy8gfSkKICAgIH0sCgogICAgLy/ljZXlhYPmoLzlsZ7mgKfnvJbovpEKICAgIGFzeW5jIHNhdmVSb3coKSB7CiAgICAgIHRyeSB7CiAgICAgICAgdGhpcy5mb3JtLm9iaklkID0gdGhpcy5jZWxsRGF0YS5vYmpJZDsKICAgICAgICB0aGlzLmZvcm0ubXBpZCA9IHRoaXMuY2VsbERhdGEubXBpZDsKICAgICAgICBsZXQgeyBjb2RlIH0gPSBhd2FpdCBlZGl0Q2VsbHModGhpcy5mb3JtKTsKICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICB0aGlzLnVwZGF0ZVRhYmxlKCk7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKnyIpOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZSkge30KICAgICAgdGhpcy5zaG93ID0gZmFsc2U7CiAgICB9LAoKICAgIC8v6YeN572u5Y2V5YWD5qC85bGe5oCn77yI5a695bqm77yM5ZCI5bm26KGM5pWw77yM5ZCI5bm25YiX5pWw77yJCiAgICByZXNldENlbGwoZWxlKSB7CiAgICAgIGlmIChlbGUpIHsKICAgICAgICBlbGUuc3R5bGUud2lkdGggPSB0aGlzLnRkV2lkdGggKyAiJSI7CiAgICAgICAgZWxlLnJvd1NwYW4gPSAiMSI7CiAgICAgICAgZWxlLmNvbFNwYW4gPSAiMSI7CiAgICAgIH0KICAgIH0sCiAgICAvL+i+k+WFpeahhuagoemqjAogICAgY2hlY2tJbnB1dCh2YWwsIGNoYW5nZVR5cGUpIHsKICAgICAgc3dpdGNoIChjaGFuZ2VUeXBlKSB7CiAgICAgICAgY2FzZSAiaHMiOgogICAgICAgICAgdGhpcy5ocyA9IHZhbDsKICAgICAgICAgIGJyZWFrOwogICAgICAgIGNhc2UgImxzIjoKICAgICAgICAgIHRoaXMubHMgPSB2YWw7CiAgICAgICAgICBicmVhazsKICAgICAgICBjYXNlICJhZGRocyI6CiAgICAgICAgICB0aGlzLmFkZGhzID0gdmFsOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSAiYWRkbHMiOgogICAgICAgICAgdGhpcy5hZGRscyA9IHZhbDsKICAgICAgICAgIGJyZWFrOwogICAgICB9CiAgICB9LAogICAgLy/ojrflj5bljZXlhYPmoLzmmI7nu4bmlbDmja4KICAgIGdldENlbGxEZXRhaWwoaHMsIGxzKSB7CiAgICAgIGxldCByZXN1bHQgPSBudWxsOwogICAgICB0aGlzLnRhYmxlRGF0YS5mb3JFYWNoKChpdGVtKSA9PiB7CiAgICAgICAgaWYgKAogICAgICAgICAgaXRlbS5yb3dpbmRleCA9PT0gaHMudG9TdHJpbmcoKSAmJgogICAgICAgICAgaXRlbS5jb2xpbmRleCA9PT0gbHMudG9TdHJpbmcoKQogICAgICAgICkgewogICAgICAgICAgcmVzdWx0ID0gaXRlbTsKICAgICAgICAgIGlmIChyZXN1bHQubnJicyA9PSBudWxsKSB7CiAgICAgICAgICAgIHJlc3VsdC5ucmJzID0gIiI7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgICAgcmV0dXJuIHJlc3VsdDsKICAgIH0sCiAgICAvL+iOt+WPluafkOS4quWNleWFg+agvOWvueixoQogICAgZ2V0Q2VsbEVsZShvYmpJZCkgewogICAgICBsZXQgcmVzdWx0ID0gbnVsbDsKICAgICAgdGhpcy50YWJsZURhdGEuZm9yRWFjaCgoaXRlbSkgPT4gewogICAgICAgIGlmIChpdGVtLm9iaklkID09PSBvYmpJZCkgewogICAgICAgICAgcmVzdWx0ID0gaXRlbTsKICAgICAgICB9CiAgICAgIH0pOwogICAgICByZXR1cm4gcmVzdWx0OwogICAgfSwKICAgIC8v6I635Y+W5pyA5paw55qE6KGo5qC85bm26YeN5paw5riy5p+TCiAgICB1cGRhdGVUYWJsZSgpIHsKICAgICAgbGV0IHBhcmFtID0gSlNPTi5zdHJpbmdpZnkoewogICAgICAgIG9ial9pZDogdGhpcy5tcERhdGEub2JqSWQsCiAgICAgICAgbGJiczogIkEiLAogICAgICB9KTsKICAgICAgLy/ojrflj5bmnIDmlrDnmoTooajmoLzmlbDmja4KICAgICAgZ2V0VGFibGUocGFyYW0pLnRoZW4oKHJlczEpID0+IHsKICAgICAgICBjb25zb2xlLmxvZygncmVz6KGo5qC85pWw5o2uJyxyZXMxKTsKICAgICAgICBpZiAocmVzMS5jb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIHRoaXMudGFibGVEYXRhID0gcmVzMS5kYXRhOwogICAgICAgICAgLy/moLnmja7mnIDmlrDnmoTooajmoLzmlbDmja7ph43mlrDnlLsKICAgICAgICAgIHRoaXMucHJvY2Vzc1RhYmxlKCk7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MocmVzMS5tc2cpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLml6Dms5Xojrflj5bmm7TmlrDlkI7nmoTooajmoLzmlbDmja7vvIEiKTsKICAgICAgICB9CiAgICAgICAgdGhpcy5sb2FkaW5nLmNsb3NlKCk7IC8v5YWz6Zet6YGu572p5bGCCiAgICAgIH0pOwogICAgfSwKICB9LAp9Owo="}, {"version": 3, "sources": ["mpxqInfo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAw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file": "mpxqInfo.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sympk", "sourcesContent": ["<template>\n  <div class=\"mpxq_info\">\n    <div id=\"mpxq_left\">\n      <ul class=\"ul1_cont\">\n        <li>表格</li>\n        <li>\n          行：<el-input\n            id=\"hs\"\n            v-model=\"hs\"\n            placeholder=\"\"\n            class=\"inp1\"\n            @input=\"(val) => checkInput(val, 'hs')\"\n          ></el-input>\n        </li>\n        <li>\n          列：<el-input\n            id=\"ls\"\n            v-model=\"ls\"\n            placeholder=\"\"\n            class=\"inp1\"\n            @input=\"(val) => checkInput(val, 'ls')\"\n          ></el-input>\n        </li>\n        <el-button type=\"success\" @click=\"createTable\" class=\"change_btn\"\n          >创建表格</el-button\n        >\n      </ul>\n      <ul class=\"ul2_cont\">\n        <li>单元格操作</li>\n        <li>\n          行跨度：<el-input\n            id=\"addhs\"\n            v-model=\"addhs\"\n            placeholder=\"\"\n            class=\"inp2\"\n            @input=\"(val) => checkInput(val, 'addhs')\"\n          ></el-input>\n        </li>\n        <li>\n          列跨度：<el-input\n            id=\"addls\"\n            v-model=\"addls\"\n            placeholder=\"\"\n            class=\"inp2\"\n            @input=\"(val) => checkInput(val, 'addls')\"\n          ></el-input>\n        </li>\n        <el-button type=\"success\" @click=\"saveChangeTable\" class=\"change_btn\"\n          >保存</el-button\n        >\n        <el-button @click=\"clearChangeTable\" class=\"change_btn\">清除</el-button>\n      </ul>\n      <ul class=\"ul3_cont\">\n        <el-button type=\"warning\" @click=\"saveTdValue\">编辑单元格</el-button>\n        <el-button type=\"warning\" @click=\"resetTable\">重置单元格</el-button>\n      </ul>\n    </div>\n    <table id=\"mpxq_right\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\"></table>\n    <!--    <table class=\"mpxq_right\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n      <tr>\n        <td>变电站</td>\n        <td>委托单位</td>\n        <td>试验单位</td>\n        <td>运行编号</td>\n      </tr>\n      <tr>\n        <td>试验性质</td>\n        <td>试验日期</td>\n        <td>试验人员</td>\n        <td>试验地点</td>\n      </tr>\n      <tr>\n        <td>报告日期</td>\n        <td>编写人</td>\n        <td>审核人</td>\n        <td>批准人</td>\n      </tr>\n      <tr>\n        <td>试验天气</td>\n        <td>环境温度（℃）</td>\n        <td>环境相对湿度（%）</td>\n        <td>投运日期</td>\n      </tr>\n    </table>-->\n\n    <el-dialog\n      :title=\"title\"\n      v-dialogDrag\n      :visible.sync=\"show\"\n      width=\"50%\"\n      append-to-body\n      @close=\"getInsterClose\"\n    >\n      <el-form label-width=\"130px\" ref=\"form\" :model=\"form\">\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"是否只读：\" prop=\"readonly\">\n              <el-select\n                placeholder=\"请选择是否只读\"\n                v-model=\"form.readonly\"\n                style=\"width: 100%\"\n                @change=\"selectvalue\"\n              >\n                <el-option\n                  v-for=\"item in readonlyList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"内容类型：：\" prop=\"nrlx\">\n              <el-select\n                placeholder=\"请选择内容类型\"\n                v-model=\"form.nrlx\"\n                style=\"width: 100%\"\n                :disabled=\"isDisabled\"\n              >\n                <el-option\n                  v-for=\"item in nrlxList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"12\">\n            <el-form-item label=\"属性名称：\" prop=\"nrbs\">\n              <el-input\n                v-model=\"form.nrbs\"\n                placeholder=\"请输入属性名称\"\n                :disabled=\"isDisabledN\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"getInsterClose\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"saveRow\">保 存</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { Loading } from \"element-ui\";\nimport {\n  resetCells,\n  createTable,\n  mergeCells,\n  editCells,\n  getCells,\n} from \"@/api/dagangOilfield/bzgl/sympk/sympInfo\";\nimport { getTable } from \"@/api/dagangOilfield/bzgl/sympk/sympk\";\n\nexport default {\n  props: {\n    mpData: {\n      type: Object,\n    },\n    mxData: {\n      type: Array,\n    },\n  },\n  name: \"mpxqInfo\",\n  inject: [\"reload\"], //inject注入根组件的reload方法\n  data() {\n    return {\n      //初始表格的行数 列数\n      hs: \"\",\n      ls: \"\",\n      //初始合并行数 列数\n      addhs: \"\",\n      addls: \"\",\n      //选中合并行、列的tr\n      changeTr: \"\",\n      //查询条件\n      params: {\n        pageNum: 1,\n        pageSize: 10,\n        mpid: \"\",\n        zy: \"\",\n        sblxbm: \"\",\n      },\n      title: \"单元格属性定义\",\n      form: {\n        objId: undefined,\n        readonly: undefined,\n        nrlx: undefined,\n        nrbs: undefined,\n        mpid: undefined,\n        readonlyMc: undefined,\n      },\n      loading: null, //遮罩层\n      tdWidth: 0, //一个单元格所占宽度\n      tdMap: new Map(), //用于存放被合并或拆分的单元格（key:当前点击的单元格,value:被处理过的单元格数组）\n      tableData: this.mxData, //表格数据\n      show: false,\n      cellData: \"\",\n      isDisabled: false,\n      isDisabledN: false,\n      nrlxList: [\n        { label: \"静态文本\", value: \"静态文本\" },\n        { label: \"铭牌实验数据\", value: \"铭牌实验数据\" },\n      ],\n      readonlyList: [\n        { label: \"是\", value: \"Y\" },\n        { label: \"否\", value: \"N\" },\n      ],\n      readonlyvalu: \"\", //选择值\n      readonlySelect: \"\", //选择值显示值\n    };\n  },\n  mounted() {\n    //获取表格初始行数和列数\n    this.initTableData();\n  },\n  methods: {\n    //获取铭牌内容数据\n    initTableData() {\n      //初始化遮罩层\n      this.loading = Loading.service({\n        text: \"加载中，请稍后...\",\n        background: \"rgba(109,106,106,0.35)\",\n      });\n      this.hs = typeof (this.mpData.AHs) != 'undefined'?this.mpData.AHs:this.mpData.aHs;\n      this.ls = typeof (this.mpData.ALs) != 'undefined'?this.mpData.ALs:this.mpData.aLs;\n\n      //更新输入框的值\n      this.updateInputValue([\"hs\", \"ls\"]);\n      this.processTable();\n      this.loading.close(); //关闭遮罩层\n    },\n    //根据行数和列数创建表格\n    processTable() {\n      var tbody = document.getElementById(\"mpxq_right\");\n      if (tbody != null) {\n        tbody.innerHTML = \"\";\n        let hs = this.hs;\n        let ls = this.ls;\n        this.tdWidth = 100 / Number(ls);\n        let str = \"\";\n        for (let i = 0; i < hs; i++) {\n          let temp = \"<tr>\";\n          for (let j = 0; j < this.tableData.length; j++) {\n            let item = this.tableData[j];\n            let nrbs = item.nrbs == null ? \"-\" : item.nrbs;\n            if (item.rowindex === i.toString()) {\n              temp +=\n                \"<td class='trName' id='\" +\n                item.objId +\n                \"' style='min-width:60px;max-width:180px;width: \" +\n                this.tdWidth * item.colspan +\n                \"%' rowspan='\" +\n                item.rowspan +\n                \"' colspan='\" +\n                item.colspan +\n                \"'>\" +\n                nrbs +\n                \"</td>\";\n            }\n          }\n          temp += \"</tr>\";\n          str += temp;\n        }\n        tbody.innerHTML = str;\n        // //给循环出来的单元格加上点击事件\n        this.addClickEvent();\n      }\n    },\n    //手动创建表格\n    createTable() {\n      //初始化遮罩层\n      this.loading = Loading.service({\n        text: \"加载中，请稍后...\",\n        background: \"rgba(109,106,106,0.35)\",\n      });\n      let params = JSON.stringify({\n        objId: this.mpData.objId, //铭牌id\n        aHs: Number(this.hs), //行数\n        aLs: Number(this.ls), //列数\n        lbbs: \"A\", //类别标识，表示修改的A表格\n      });\n      createTable(params).then((res) => {\n        console.log('手动创建表格',res)\n        if (res.code === \"0000\") {\n          this.updateTable();\n        } else {\n          this.$message.error(\"操作失败\");\n          this.loading.close(); //关闭遮罩层\n        }\n      });\n    },\n    //给循环出来的单元格加上点击事件\n    addClickEvent() {\n      let trArr = document.getElementsByClassName(\"trName\"); //不可编辑的单元格\n      let inputArr = document.getElementsByClassName(\"input_cls\"); //可编辑的单元格\n      let that = this;\n      if (trArr != null) {\n        //循环所有的tr\n        for (let i = 0; i < trArr.length; i++) {\n          trArr[i].onclick = function () {\n            that.changeTr = this;\n            that.addhs = that.changeTr.rowSpan;\n            that.addls = that.changeTr.colSpan;\n            that.cellData = that.getCellEle(that.changeTr.id);\n          };\n        }\n      }\n      if (inputArr != null) {\n        //循环所有的tr\n        for (let i = 0; i < inputArr.length; i++) {\n          inputArr[i].onclick = function () {\n            that.changeTr = this;\n            that.addhs = that.changeTr.rowSpan;\n            that.addls = that.changeTr.colSpan;\n          };\n        }\n      }\n    },\n    //合并拆分保存\n    saveChangeTable() {\n      //初始化遮罩层\n      this.loading = Loading.service({\n        text: \"加载中，请稍后...\",\n        background: \"rgba(109,106,106,0.35)\",\n      });\n      //看要合并的是第几行几列的tr   id格式  0|0 0|1\n      /*let id = this.changeTr.id;\n      let addh = Number(id.split('|')[0]);  //取当前元素的行\n      let addl = Number(id.split('|')[1]);  //取当前元素的列\n      let hs = Number(this.addhs);//行数\n      let ls = Number(this.addls);//列数*/\n\n      let params = JSON.stringify({\n        objId: this.changeTr.id,\n        rowspan: this.addhs,\n        colspan: this.addls,\n      });\n      //先请求接口，如果后台可以执行合并或拆分，则将最新的表格数据请求回来进行前端展示\n      mergeCells(params).then((res) => {\n        if (res.code === \"0000\") {\n          this.updateTable();\n        } else {\n          this.$message.error(\"操作失败\");\n          this.loading.close(); //关闭遮罩层\n        }\n      });\n    },\n    //处理合并或拆分\n    processTr(ids) {\n      //点击的单元格id\n      let clickId = this.changeTr.id;\n      let arr1 = []; //需要重新设置map的数组\n      //如果之前已经处理过该单元格,则先将其还原\n      if (this.tdMap.has(clickId)) {\n        this.tdMap.get(clickId).forEach((item) => {\n          if (item != null) {\n            this.resetCell(item);\n            item.style.display = \"table-cell\";\n          }\n        });\n        //操作完后将数据从map中删除\n        this.tdMap.delete(clickId);\n      }\n      let processEle = []; //被处理的元素\n\n      //现将连带受影响的单元格还原，再进行隐藏处理\n      if (ids.length > 0) {\n        //执行还原\n        ids.forEach((id1) => {\n          let ele = document.getElementById(id1);\n          //如果此次处理的单元格中有已经被处理过的，先将其还原\n          if (this.tdMap.has(id1)) {\n            this.tdMap.get(id1).forEach((item) => {\n              this.resetCell(item);\n              item.style.display = \"table-cell\";\n            });\n            //操作完后将数据从map中删除\n            this.tdMap.delete(id1);\n          }\n          //处理被连带的已经合并过的单元格\n          if (ele) {\n            let className = ele.className;\n            if (this.tdMap.has(className)) {\n              let mergeCell = document.getElementById(className); //被连带的已经合并过的cell\n              if (mergeCell) {\n                this.resetCell(mergeCell);\n              }\n              this.tdMap.get(className).forEach((item) => {\n                //需要把此次要隐藏的单元格排除掉，不然隐藏完下次循环又会放出来\n                if (!ids.includes(item)) {\n                  item.style.display = \"table-cell\";\n                } else {\n                  arr1.push(item);\n                }\n              });\n              //处理完成后，更新map中的值，将处理过的排除掉\n              if (arr1.length > 0) {\n                this.tdMap.set(className, arr1);\n              } else {\n                //操作完后将数据从map中删除\n                this.tdMap.delete(className);\n              }\n            }\n          }\n        });\n        //执行隐藏\n        ids.forEach((id) => {\n          let ele = document.getElementById(id);\n          //将多余的单元格隐藏\n          if (ele) {\n            processEle.push(ele); //添加数据保存到map中\n\n            document.getElementById(id).style.display = \"none\";\n            //将className设置给被操作的单元格，方便下次有连带操作时对单元格进行处理\n            document.getElementById(id).className = clickId;\n          }\n        });\n        //重新设置map中的值\n        this.tdMap.set(clickId, processEle);\n      }\n    },\n    //取消更改的合并行、列数\n    clearChangeTable() {},\n\n    selectvalue(val) {\n      let obj = {};\n      obj = this.readonlyList.find((item) => {\n        return item.value === val;\n      });\n      if (obj.value === \"N\") {\n        this.form.nrlx = \"铭牌实验数据\";\n        this.isDisabledN = true;\n      } else {\n        this.form.nrlx = \"静态文本\";\n        this.isDisabledN = false;\n      }\n      this.isDisabled = true;\n      // this.form.readonly = +obj.value;\n    },\n\n    //关闭弹窗\n    getInsterClose() {\n      this.show = false;\n      this.form = {};\n    },\n\n    //进行合并或拆分操作\n    mergeTable(hs, ls, addh, addl) {\n      if (hs === 1) {\n        //合并列\n        if (ls >= 1) {\n          this.mergeCells(addh, addl, hs, ls);\n        }\n      } else {\n        if (hs > 1) {\n          //多行\n          //合并行\n          if (ls === 1) {\n            this.mergeRows(addh, addl, hs, ls);\n          } else if (ls > 1) {\n            //合并多行多列\n            this.mergeRowsAndCells(addh, addl, hs, ls);\n          }\n        }\n      }\n      //要合并的单元格进行合并\n      this.changeTr.style.width = this.tdWidth * ls + \"%\"; //设置合并后的单元格宽度\n      this.changeTr.rowSpan = this.addhs;\n      this.changeTr.colSpan = this.addls;\n    },\n    /**\n     * 第一种情况，合并列（一行多列）\n     * @param h 当前元素所在行\n     * @param l 当前元素所在列\n     * @param hs 要合并的行数\n     * @param ls 要合并的列数\n     */\n    mergeCells(h, l, hs, ls) {\n      let removeIds = []; //要删除的元素的id数组\n      let ls_xh = ls; //要循环的列数\n      if (ls > this.ls - l) {\n        //不能超过剩余可操作的列数\n        ls_xh = this.ls - l;\n      }\n      for (let i = 1; i < ls_xh; i++) {\n        removeIds.push(h + \"|\" + (l + i));\n      }\n      //删除多余单元格\n      this.processTr(removeIds);\n    },\n    /**\n     * 第二种情况，合并行（多行一列）\n     * @param h 当前元素所在行\n     * @param l 当前元素所在列\n     * @param hs 要合并的行数\n     * @param ls 要合并的列数\n     */\n    mergeRows(h, l, hs, ls) {\n      let removeIds = []; //要删除的元素的id数组\n      let hs_xh = hs; //要循环的行数\n      if (hs > this.hs - h) {\n        //不能超过剩余可操作的行数\n        hs_xh = this.hs - h;\n      }\n      console.log(\"hs_xh\", hs_xh);\n      for (let i = 1; i < hs_xh; i++) {\n        removeIds.push(h + i + \"|\" + l);\n      }\n      //删除多余单元格\n      this.processTr(removeIds);\n    },\n    /**\n     * 第三种情况，合并多行多列\n     * @param h 当前元素所在行\n     * @param l 当前元素所在列\n     * @param hs 要合并的行数\n     * @param ls 要合并的列数\n     */\n    mergeRowsAndCells(h, l, hs, ls) {\n      let removeIds = []; //要删除的元素的id数组\n      let removeId = \"\";\n      //先循环行（从当前行开始循环）\n      for (let j = 0; j < hs; j++) {\n        //循环列\n        for (let i = 0; i < ls; i++) {\n          //从当前列循环\n          removeId = h + j + \"|\" + (l + i);\n          //将当前单元格排除掉\n          if (removeId !== h + \"|\" + l) {\n            removeIds.push(removeId);\n          }\n        }\n      }\n      //删除多余单元格\n      this.processTr(removeIds);\n    },\n    //更新输入框的值\n    updateInputValue(arrs) {\n      for (let i = 0; i < arrs.length; i++) {\n        let ele = document.getElementById(arrs[i]);\n        if (ele != null && typeof ele != \"undefined\") {\n          switch (arrs[i]) {\n            case \"hs\":\n              ele.value = this.hs;\n              break;\n            case \"ls\":\n              ele.value = this.ls;\n              break;\n            case \"addhs\":\n              ele.value = this.addhs;\n              break;\n            case \"addls\":\n              ele.value = this.addls;\n              break;\n          }\n        }\n      }\n    },\n    //重置单元格内容\n    resetTable() {\n      //初始化遮罩层\n      this.loading = Loading.service({\n        text: \"加载中，请稍后...\",\n        background: \"rgba(109,106,106,0.35)\",\n      });\n      let objId = this.changeTr.id;\n      let params = this.getCellEle(objId);\n      resetCells(params).then((res) => {\n        if (res.code === \"0000\") {\n          this.updateTable();\n        } else {\n          this.$message.error(\"操作失败\");\n        }\n        this.loading.close(); //关闭遮罩层\n      });\n    },\n    //单元格属性编辑并保存\n    saveTdValue() {\n      this.show = true;\n      //初始化遮罩层\n      // this.loading = Loading.service({\n      //   text:\"加载中，请稍后...\",\n      //   background:'rgba(109,106,106,0.35)',\n      // })\n      // let objId = this.changeTr.id;\n      // let val = this.changeTr.getElementsByTagName(\"input\")[0].value;\n      // let params = this.getCellEle(objId);\n      // params.nrbs = val;\n      // editCells(params).then(res=>{\n      //   if(res.code==='0000'){\n      //     this.updateTable();\n      //   }else{\n      //     this.$message.error('操作失败');\n      //     this.loading .close();//关闭遮罩层\n      //   }\n      // })\n    },\n\n    //单元格属性编辑\n    async saveRow() {\n      try {\n        this.form.objId = this.cellData.objId;\n        this.form.mpid = this.cellData.mpid;\n        let { code } = await editCells(this.form);\n        if (code === \"0000\") {\n          this.updateTable();\n          this.$message.success(\"操作成功\");\n        }\n      } catch (e) {}\n      this.show = false;\n    },\n\n    //重置单元格属性（宽度，合并行数，合并列数）\n    resetCell(ele) {\n      if (ele) {\n        ele.style.width = this.tdWidth + \"%\";\n        ele.rowSpan = \"1\";\n        ele.colSpan = \"1\";\n      }\n    },\n    //输入框校验\n    checkInput(val, changeType) {\n      switch (changeType) {\n        case \"hs\":\n          this.hs = val;\n          break;\n        case \"ls\":\n          this.ls = val;\n          break;\n        case \"addhs\":\n          this.addhs = val;\n          break;\n        case \"addls\":\n          this.addls = val;\n          break;\n      }\n    },\n    //获取单元格明细数据\n    getCellDetail(hs, ls) {\n      let result = null;\n      this.tableData.forEach((item) => {\n        if (\n          item.rowindex === hs.toString() &&\n          item.colindex === ls.toString()\n        ) {\n          result = item;\n          if (result.nrbs == null) {\n            result.nrbs = \"\";\n          }\n        }\n      });\n      return result;\n    },\n    //获取某个单元格对象\n    getCellEle(objId) {\n      let result = null;\n      this.tableData.forEach((item) => {\n        if (item.objId === objId) {\n          result = item;\n        }\n      });\n      return result;\n    },\n    //获取最新的表格并重新渲染\n    updateTable() {\n      let param = JSON.stringify({\n        obj_id: this.mpData.objId,\n        lbbs: \"A\",\n      });\n      //获取最新的表格数据\n      getTable(param).then((res1) => {\n        console.log('res表格数据',res1);\n        if (res1.code === \"0000\") {\n          this.tableData = res1.data;\n          //根据最新的表格数据重新画\n          this.processTable();\n          this.$message.success(res1.msg);\n        } else {\n          this.$message.error(\"无法获取更新后的表格数据！\");\n        }\n        this.loading.close(); //关闭遮罩层\n      });\n    },\n  },\n};\n</script>\n\n<style scoped lang=\"scss\">\n.mpxq_info {\n  display: flex;\n}\n#mpxq_left {\n  margin-right: 20px;\n  ul {\n    list-style-type: none;\n    margin: 0;\n    padding: 8px;\n  }\n  border: 1px solid #0cc283;\n  width: 28%;\n  li:nth-child(1) {\n    font-weight: 700;\n  }\n  li {\n    line-height: 48px;\n    padding-left: 8px;\n    .el-input {\n      width: 70%;\n    }\n  }\n}\n.change_btn {\n  margin-top: 10px !important;\n  height: 36px !important;\n}\n.change_btn:nth-child(1) {\n  margin-left: 29%;\n}\n.change_btn:nth-child(2) {\n  margin-left: 20%;\n}\n#mpxq_right {\n  width: 72%;\n  height: 180px;\n  border: 1px solid #000;\n}\n</style>\n<style>\n#mpxq_right td {\n  border: 1px solid #000;\n  height: 35px;\n  line-height: 35px;\n  text-align: center;\n}\n#mpxq_right tr {\n  height: 35px;\n}\n#mpxq_right .atc {\n  background-color: #11ba6d;\n}\n#mpxq_right .input_cls {\n  text-align: center;\n  border: none;\n  width: 99%;\n  height: 99%;\n}\n</style>\n"]}]}