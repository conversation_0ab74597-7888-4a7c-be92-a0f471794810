{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\assetQuery\\index.vue?vue&type=style&index=0&id=4f050376&scoped=true&lang=css&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\assetQuery\\index.vue", "mtime": 1706897324396}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5oZWFkLWNvbnRhaW5lciB7CiAgbWFyZ2luOiAwIGF1dG87CiAgd2lkdGg6IDk4JTsKICBoZWlnaHQ6IGNhbGMoMTAwdmggLSAyMjVweCk7CiAgb3ZlcmZsb3c6IGF1dG87Cn0KCi5ib3gtY2FyZCB7CiAgbWFyZ2luLWJvdHRvbTogMTVweDsKfQouZWwtY2FyZF9faGVhZGVyIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjM1LCAyNDUsIDI1NSkgIWltcG9ydGFudDsKfQouYm94LWNhcmRMaXN0IHsKICBoZWlnaHQ6IDcwJTsKfQoKLml0ZW0gewogIHdpZHRoOiAyMDBweDsKICBmbG9hdDogbGVmdDsKfQoKLmFzaWRlX2hlaWdodCB7CiAgaGVpZ2h0OiA4MXZoOwp9CgouZWwtc2VsZWN0IHsKICB3aWR0aDogMTAwJTsKfQoKCi9kZWVwLyAucG15QnRuIHsKICBiYWNrZ3JvdW5kOiAjMGNjMjgzOwp9Cgo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkLA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/dagangOilfield/dwzygl/assetQuery", "sourcesContent": ["<template>\n <div class=\"app-container\">\n   <el-row gutter=\"16\">\n     <el-col :span=\"4\">\n       <el-card shadow=\"never\" style=\"background:#e0f8ed;\">\n         <div class=\"text head-container\">\n           <el-col>\n             <el-tree :expand-on-click-node=\"true\"\n                      id=\"tree\"\n                      :data=\"treeOptions\"\n                      :default-expanded-keys=\"['1']\"\n                      @node-click=\"handleNodeClick\"\n                      node-key=\"nodeId\"/>\n           </el-col>\n         </div>\n       </el-card>\n     </el-col>\n\n     <!--右侧列表-->\n     <el-col :span=\"20\">\n      <el-filter\n        ref=\"filter1\"\n        :data=\"filterInfo.data\"\n        :field-list=\"filterInfo.fieldList\"\n        :width=\"{labelWidth: 120, itemWidth: 180}\"\n        @handleReset=\"filterReset\"\n      />\n      <el-white class=\"button-group\">\n        <comp-table :table-and-page-info=\"tableAndPageInfo\" @update:multipleSelection=\"handleSelectionChange\" height=\"500\"/>\n      </el-white>\n     </el-col>\n   </el-row>\n\n   <!--电网资源综合查询新增、修改、详情弹框-->\n   <el-dialog title=\"电网资源综合查询\" :visible.sync=\"dwzyDialogFormVisible\" class=\"qxlr_dialog_insert\" width=\"60%\" v-dialogDrag >\n    <el-form ref=\"form\" :model=\"form\" :disabled=\"isDisabled\"  label-width=\"130px\">\n      <el-row :gutter=\"20\">\n        <el-col :span=\"8\">\n          <el-form-item label=\"设备名称：\" prop=\"sbmc\">\n            <el-input v-model=\"form.sbmc\" placeholder=\"请输入设备名称\"></el-input>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"8\">\n          <el-form-item label=\"电压等级：\" prop=\"dydj\">\n            <el-input v-model=\"form.dydj\" placeholder=\"请输入电压等级\"></el-input>\n          </el-form-item>\n        </el-col>\n      </el-row>\n      <el-row :gutter=\"20\">\n        <el-col :span=\"8\">\n          <el-form-item label=\"生产厂家：\" prop=\"sccj\">\n            <el-input v-model=\"form.sccj\" placeholder=\"请输入生产厂家\"></el-input>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"8\">\n          <el-form-item label=\"投运日期：\" prop=\"tyrq\">\n            <el-date-picker type=\"date\" placeholder=\"选择日期\" value-format=\"yyyy-MM-dd\" v-model=\"form.tyrq\" style=\"width: 100%\">\n            </el-date-picker>\n          </el-form-item>\n        </el-col>\n      </el-row>\n      <el-row :gutter=\"20\">\n        <el-col :span=\"8\">\n          <el-form-item label=\"型号规格：\" prop=\"xhgg\">\n            <el-input v-model=\"form.xhgg\" placeholder=\"请输入型号规格\"></el-input>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"8\">\n          <el-form-item label=\"状态：\" prop=\"zt\">\n            <el-select v-model=\"form.zt\" placeholder=\"请选择状态\"></el-select>\n          </el-form-item>\n        </el-col>\n      </el-row>\n    </el-form>\n    <div slot=\"footer\" class=\"dialog-footer\"  v-show=\"!isDisabled\">\n      <el-button @click=\"dwzyDialogFormVisible = false\">取 消</el-button>\n      <el-button type=\"primary\"  class=\"pmyBtn\">确 定</el-button>\n    </div>\n   </el-dialog>\n </div>\n\n</template>\n\n<script>\n\n    import { getTreeData } from '@/api/dagangOilfield/asset/assetQuery'\n\n    export default {\n      name: \"index\",\n      data(){\n        return{\n          //树结构上的筛选框参数\n          treeForm: {},\n          isDisabled: false,\n          filterInfo: {\n            data: {\n              sbmc: '',\n              dydj: '',\n              sccj: '',\n              tyrqArr: undefined,\n            },\n            fieldList: [\n              {label: '设备名称',type: 'input',value: 'sbmc'},\n              {label: '电压等级',type: 'input',value: 'dydj'},\n              {label: '生产厂家',type: 'input',value: 'sccj'},\n              {label: '投运日期',type: 'date',value: 'tyrqArr',dateType:'daterange',format:'yyyy-MM-dd'},\n            ]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableAndPageInfo: {\n            pager: {\n              pageSize: 10,\n              pageNum: 1,\n              total: 0,\n              size: [10, 20, 50, 100]\n          },\n            tableData: [],\n            tableHeader: [\n              {prop: 'sbmc', label: '设备名称', minWidth: '120'},\n              {prop: 'dydj', label: '电压等级', minWidth: '120'},\n              {prop: 'sccj', label: '生产厂家', minWidth: '120'},\n              {prop: 'tyrq', label: '投运日期', minWidth: '120'},\n              {prop: 'xhgg', label: '型号规格', minWidth: '120'},\n              {prop: 'zt', label: '状态', minWidth: '120'},\n              {\n                fixed: \"right\",\n                prop: 'operation',\n                label: '操作',\n                minWidth: '130px',\n                style: {display: 'block'},\n                operation: [\n                  {name: '修改', clickFun: this.getUpdate},\n                  {name: '详情', clickFun: this.getXq},\n                ]\n              },\n            ]\n          },\n          //电网资源添加按钮弹出框\n          dwzyDialogFormVisible: false,\n          //弹出框表单\n          form: {\n\n          },\n          //组织树\n          treeOptions: [],\n\n\n        }\n      },\n      created() {\n        this.getTreeOptions();\n      },\n      watch: {},\n      methods:{\n        handleNodeClick(){\n\n        },\n        filterReset(){\n\n        },\n        handleSelectionChange(){\n\n        },\n        getTreeOptions(){\n          getTreeData().then(res=>{\n            this.treeOptions=res.data;\n          })\n        },\n      }\n\n\n    }\n</script>\n\n<style scoped type=\"less\">\n  .head-container {\n    margin: 0 auto;\n    width: 98%;\n    height: calc(100vh - 225px);\n    overflow: auto;\n  }\n\n  .box-card {\n    margin-bottom: 15px;\n  }\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n  .box-cardList {\n    height: 70%;\n  }\n\n  .item {\n    width: 200px;\n    float: left;\n  }\n\n  .aside_height {\n    height: 81vh;\n  }\n\n  .el-select {\n    width: 100%;\n  }\n\n\n  /deep/ .pmyBtn {\n    background: #0cc283;\n  }\n\n</style>\n"]}]}