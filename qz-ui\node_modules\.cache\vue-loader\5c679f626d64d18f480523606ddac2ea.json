{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\DictTag\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\DictTag\\index.vue", "mtime": 1706897320305}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJEaWN0VGFnIiwNCiAgcHJvcHM6IHsNCiAgICBvcHRpb25zOiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIGRlZmF1bHQ6IG51bGwsDQogICAgfSwNCiAgICB2YWx1ZTogW051bWJlciwgU3RyaW5nLCBBcnJheV0sDQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgdmFsdWVzKCkgew0KICAgICAgaWYgKHRoaXMudmFsdWUgIT09IG51bGwgJiYgdHlwZW9mIHRoaXMudmFsdWUgIT09ICd1bmRlZmluZWQnKSB7DQogICAgICAgIHJldHVybiBBcnJheS5pc0FycmF5KHRoaXMudmFsdWUpID8gdGhpcy52YWx1ZSA6IFtTdHJpbmcodGhpcy52YWx1ZSldOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmV0dXJuIFtdOw0KICAgICAgfQ0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/DictTag", "sourcesContent": ["<template>\r\n  <div>\r\n    <template v-for=\"(item, index) in options\">\r\n      <template v-if=\"values.includes(item.value)\">\r\n        <span\r\n          :key=\"item.value\"\r\n          :index=\"index\"\r\n          >{{ item.label }}</span\r\n        >\r\n      </template>\r\n    </template>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"DictTag\",\r\n  props: {\r\n    options: {\r\n      type: Array,\r\n      default: null,\r\n    },\r\n    value: [Number, String, Array],\r\n  },\r\n  computed: {\r\n    values() {\r\n      if (this.value !== null && typeof this.value !== 'undefined') {\r\n        return Array.isArray(this.value) ? this.value : [String(this.value)];\r\n      } else {\r\n        return [];\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.el-tag + .el-tag {\r\n  margin-left: 10px;\r\n}\r\n</style>"]}]}