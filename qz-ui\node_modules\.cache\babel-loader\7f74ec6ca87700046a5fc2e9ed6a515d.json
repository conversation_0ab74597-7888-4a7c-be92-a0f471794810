{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\activiti_yjrwd\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\activiti_yjrwd\\index.vue", "mtime": 1720689497805}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AA4EA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBACA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,KAAA,EAAA;AACA;;;;;;;;;;;;AAYA,IAAA,WAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAFA,sBAEA;AACA,eAAA;AACA,UAAA,WAAA,EAAA,IADA;AAEA,UAAA,WAAA,EAAA;AAFA,SAAA;AAIA;AAPA,KAbA;AAsBA;AACA,IAAA,MAAA,EAAA;AACA,MAAA,IAAA,EAAA,OADA;AAEA,MAAA,OAAA,EAAA;AAFA,KAvBA;AA2BA;AACA,IAAA,MAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAFA,sBAEA;AACA,eAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAAA;AACA;AAJA;AA5BA,GAFA;AAqCA,EAAA,IArCA,kBAqCA;AACA,WAAA;AACA,MAAA,QAAA,EAAA,KADA;AAEA,MAAA,IAAA,EAAA,EAFA;AAGA,MAAA,KAAA,EAAA,EAHA;AAIA,MAAA,OAAA,EAAA,IAJA;AAKA,MAAA,SAAA,EAAA;AALA,KAAA;AAOA,GA7CA;AA8CA,EAAA,KAAA,EAAA;AACA,IAAA,WAAA,EAAA;AACA,MAAA,OADA,mBACA,MADA,EACA,MADA,EACA;AAAA;;AACA,aAAA,SAAA,CAAA,YAAA;AACA,UAAA,KAAA,CAAA,KAAA,mCAAA,MAAA,EADA,CAEA;;AACA,cAAA,KAAA,CAAA,KAAA,CAAA,WAAA,KAAA,iBAAA,EAAA;AACA,qCAAA;AACA,cAAA,aAAA,EAAA,KAAA,CAAA,KAAA,CAAA,SAAA,CAAA,aADA;AAEA,cAAA,MAAA,EAAA,KAAA,CAAA,KAAA,CAAA,SAAA,CAAA,MAAA,GACA,KAAA,CAAA,KAAA,CAAA,SAAA,CAAA,MADA,GAEA,CAJA;AAKA,cAAA,QAAA,EAAA,KAAA,CAAA,KAAA,CAAA,SAAA,CAAA,QAAA,GACA,KAAA,CAAA,KAAA,CAAA,SAAA,CAAA,QADA,GAEA;AAPA,aAAA,EAQA,IARA,CAQA,UAAA,GAAA,EAAA;AACA,cAAA,KAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA;AACA,aAVA;AAWA;AACA,SAhBA;AAiBA,OAnBA;AAoBA,MAAA,IAAA,EAAA,IApBA;AAqBA,MAAA,SAAA,EAAA;AArBA;AADA,GA9CA;AAuEA,EAAA,OAvEA,qBAuEA,CAAA,CAvEA;AAwEA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,UAFA,wBAEA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA,sBAEA,MAAA,CAAA,KAAA,CAAA,WAAA,KAAA,UAAA,KACA,MAAA,CAAA,IAAA,CAAA,OAAA,IAAA,IAAA,IAAA,MAAA,CAAA,IAAA,CAAA,OAAA,IAAA,EADA,CAFA;AAAA;AAAA;AAAA;;AAKA;AACA,gBAAA,KAAA,CAAA,WAAA,CAAA;AANA,iDAOA,KAPA;;AAAA;AASA,gBAAA,MAAA,CAAA,KAAA,CAAA,SAAA,GAAA,MAAA,CAAA,MAAA,CAAA,IAAA;;AACA,oBAAA,MAAA,CAAA,IAAA,CAAA,QAAA,EAAA;AACA;AACA,sBAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,oBAAA,QADA,GACA,EADA;;AAEA,oBAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,OAAA,CAAA,UAAA,CAAA,EAAA;AACA,sBAAA,QAAA,IAAA,CAAA,CAAA,QAAA,GAAA,GAAA;AACA,qBAFA,EAFA,CAKA;;;AACA,wBAAA,QAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,sBAAA,QAAA,GAAA,QAAA,CAAA,MAAA,CAAA,CAAA,EAAA,QAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA;;AACA,oBAAA,MAAA,CAAA,KAAA,CAAA,QAAA,GAAA,QAAA;AACA,mBAVA,MAUA;AACA,oBAAA,MAAA,CAAA,KAAA,CAAA,QAAA,GAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,QAAA;AACA,oBAAA,MAAA,CAAA,KAAA,CAAA,gBAAA,GAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,QAAA;AACA;AACA,iBAhBA,MAgBA;AACA,sBAAA,MAAA,CAAA,WAAA,CAAA,YAAA,EAAA;AACA;AACA,wBAAA,MAAA,CAAA,WAAA,CAAA,YAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,sBAAA,SADA,GACA,EADA;AAEA,sBAAA,QAFA,GAEA,EAFA;;AAGA,sBAAA,MAAA,CAAA,WAAA,CAAA,YAAA,CAAA,OAAA,CAAA,UAAA,CAAA,EAAA;AACA,wBAAA,SAAA,IAAA,CAAA,CAAA,QAAA,GAAA,GAAA;AACA,wBAAA,QAAA,IAAA,CAAA,CAAA,QAAA,GAAA,GAAA;AACA,uBAHA,EAHA,CAOA;;;AACA,0BAAA,SAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,wBAAA,SAAA,GAAA,SAAA,CAAA,MAAA,CAAA,CAAA,EAAA,SAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA;;AACA,0BAAA,QAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,wBAAA,QAAA,GAAA,QAAA,CAAA,MAAA,CAAA,CAAA,EAAA,QAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA;;AACA,sBAAA,MAAA,CAAA,KAAA,CAAA,QAAA,GAAA,SAAA;AACA,sBAAA,MAAA,CAAA,KAAA,CAAA,gBAAA,GAAA,QAAA;AACA,qBAhBA,MAgBA;AACA,sBAAA,MAAA,CAAA,KAAA,CAAA,QAAA,GAAA,MAAA,CAAA,WAAA,CAAA,YAAA,CAAA,QAAA;AACA,sBAAA,MAAA,CAAA,KAAA,CAAA,gBAAA,GAAA,MAAA,CAAA,WAAA,CAAA,YAAA,CAAA,QAAA;AACA;AACA,mBAtBA,MAsBA;AACA,oBAAA,MAAA,CAAA,KAAA,CAAA,QAAA,GAAA,SAAA;AACA,oBAAA,MAAA,CAAA,KAAA,CAAA,gBAAA,GAAA,SAAA;AACA;AACA;;AArDA,sBAuDA,CAAA,MAAA,CAAA,KAAA,CAAA,QAAA,IACA,MAAA,CAAA,KAAA,CAAA,WAAA,KAAA,iBADA,IAEA,MAAA,CAAA,KAAA,CAAA,WAzDA;AAAA;AAAA;AAAA;;AA2DA,gBAAA,MAAA,CAAA,QAAA,CAAA;AACA,kBAAA,IAAA,EAAA,OADA;AAEA,kBAAA,OAAA,EAAA;AAFA,iBAAA;;AA3DA;;AAAA;AAiEA,oBAAA,MAAA,CAAA,IAAA,CAAA,OAAA,EAAA;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,SAAA,CAAA,OAAA,GAAA,MAAA,CAAA,IAAA,CAAA,OAAA;AACA;;AACA,gBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,kBAAA,MAAA,CAAA,OAAA,GAAA,mBAAA,OAAA,CAAA;AACA,oBAAA,IAAA,EAAA,IADA;AACA;AACA,oBAAA,IAAA,EAAA,WAFA;AAEA;AACA,oBAAA,OAAA,EAAA,iBAHA;AAGA;AACA,oBAAA,UAAA,EAAA,oBAJA;AAIA;AACA,oBAAA,MAAA,EAAA,QAAA,CAAA,aAAA,CAAA,YAAA;AALA,mBAAA,CAAA;AAOA,iBATA;;AApEA;AAgFA,gBAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,MAAA,CAAA,KAAA;AAhFA;AAAA,uBAiFA,oCAAA,MAAA,CAAA,KAAA,CAjFA;;AAAA;AAAA;AAiFA,gBAAA,IAjFA,yBAiFA,IAjFA;AAiFA,gBAAA,IAjFA,yBAiFA,IAjFA;;AAkFA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,UAAA,GAAA,IAAA;AACA;;AACA,oBAAA,IAAA,EAAA;AACA,kBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,oBAAA,MAAA,CAAA,OAAA,CAAA,KAAA;AACA,mBAHA;AAIA;;AA1FA;AAAA;;AAAA;AAAA;AAAA;;AA4FA,gBAAA,MAAA,CAAA,SAAA,CAAA,YAAA;AACA;AACA,kBAAA,MAAA,CAAA,OAAA,CAAA,KAAA;AACA,iBAHA;;AA5FA;AAiGA,oBAAA,UAAA,EAAA;AACA,kBAAA,UAAA,CAAA,WAAA,GAAA,MAAA,CAAA,KAAA,CAAA,WAAA;;AACA,kBAAA,MAAA,CAAA,KAAA,CAAA,UAAA,EAAA,UAAA;AACA;;AACA,gBAAA,MAAA,CAAA,OAAA;;AArGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsGA,KAxGA;AAyGA,IAAA,OAzGA,qBAyGA;AACA,WAAA,IAAA,CAAA,KAAA,IAAA,EAAA,UAAA,EAAA,EAAA;AACA,WAAA,IAAA,CAAA,KAAA,IAAA,EAAA,SAAA,EAAA,EAAA;AACA,WAAA,KAAA,CAAA,SAAA,EAAA,OAAA;AACA;AA7GA;AAxEA,C", "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      id=\"dialogAct\"\n      class=\"tree\"\n      :title=\"option.title ? option.title : ''\"\n      :top=\"option.top ? option.top : '10vh'\"\n      v-dialogDrag\n      :visible.sync=\"isShow\"\n      :width=\"option.width ? option.width : '25%'\"\n      :close-on-click-modal=\"false\"\n      @close=\"toClose\"\n      :destroy-on-colse=\"true\"\n      :modal-append-to-body=\"false\"\n    >\n      <slot name=\"Dialog_content\" />\n      <el-form\n        label-width=\"120px\"\n        ref=\"form\"\n        :model=\"form\"\n        v-if=\"datas.defaultFrom ? true : false\"\n      >\n        <div>\n          <el-row>\n            <!--    根据人员组选人   -->\n            <div v-if=\"datas.processType === 'completeByGroup'\">\n              <el-col :span=\"24\" v-if=\"datas.nextUser ? datas.nextUser : true\">\n                <el-form-item prop=\"nextUser\" label=\"处理人选择：\">\n                  <el-select\n                    v-model=\"form.nextUser\"\n                    placeholder=\"请选择人员\"\n                    filterable\n                    style=\"width: 100%;\"\n                    value-key=\"userName\"\n                  >\n                    <el-option\n                      v-for=\"item in ryOptions\"\n                      :key=\"item.userName\"\n                      :label=\"item.nickName\"\n                      :value=\"{\n                        userName: item.userName,\n                        nickName: item.nickName\n                      }\"\n                    >\n                    </el-option>\n                  </el-select>\n                </el-form-item>\n              </el-col>\n            </div>\n            <el-col :span=\"24\">\n              <el-form-item\n                prop=\"comment\"\n                label=\"回退原因：\"\n                v-if=\"datas.processType === 'rollback'\"\n              >\n                <el-input\n                  style=\"width: 100%\"\n                  v-model=\"form.comment\"\n                  type=\"textarea\"\n                  placeholder=\"请输入原因\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </div>\n      </el-form>\n      <span v-else>请确定是否提交</span>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"info\" @click=\"toClose\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"todoSubmit('form')\">确定</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { completeYjrwdTask } from \"@/api/activiti/processTask\";\nimport { Loading } from \"element-ui\";\nimport { getUsers } from \"@/api/system/usergroup\";\nexport default {\n  name: \"index\",\n  props: {\n    /*流程发起必填数据\n        processData:{\n          processDefinitionKey:\"流程定义的key\",//必填\n          taskId:\"任务id，如果任务在代办列表时，会根据代办列表获取taskid，否则需传入业务id来确定task\",\n          businessKey:\"业务id，对应业务的主键\",//taskid和businessKey两者必须有其中一个才可以确定一个task\n          businessType:\"业务类型，用于区分不同的业务\"//必填\n          variables:\"拓展参数\"//流程定义中设置的参数,\n          nextUser:\"如果流程实例中并未配置每一个节点的处理人，则需要用户手动选择每一个节点的处理人\",\n          nextUserInfo:{},//手动传的审核人信息\n          processType:'complete,rollback',\n          defaultFrom:true,false 是否需要默认表单\n        }*/\n    processData: {\n      type: Object,\n      default() {\n        return {\n          defaultFrom: true,\n          processType: \"complete\"\n        };\n      }\n    },\n    //显示隐藏\n    isShow: {\n      type: Boolean,\n      default: false\n    },\n    //子组件默认参数\n    option: {\n      type: Object,\n      default() {\n        return { title: \"审批\" };\n      }\n    }\n  },\n  data() {\n    return {\n      disabled: false,\n      form: {},\n      datas: {},\n      loading: null,\n      ryOptions: []\n    };\n  },\n  watch: {\n    processData: {\n      handler(newVal, oldVal) {\n        this.$nextTick(() => {\n          this.datas = { ...newVal };\n          //如果是需要从人员组中选人的，则从人员组查人\n          if (this.datas.processType === \"completeByGroup\") {\n            getUsers({\n              personGroupId: this.datas.variables.personGroupId,\n              deptId: this.datas.variables.deptId\n                ? this.datas.variables.deptId\n                : 0,\n              deptName: this.datas.variables.deptName\n                ? this.datas.variables.deptName\n                : \"\"\n            }).then(res => {\n              this.ryOptions = res.data;\n            });\n          }\n        });\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  mounted() {},\n  methods: {\n    //流程提交，流程拒绝\n    async todoSubmit() {\n      if (\n        this.datas.processType === \"rollback\" &&\n        (this.form.comment == null || this.form.comment == \"\")\n      ) {\n        //如果是回退，则需要做必填校验\n        alert(\"请填写原因后保存！\");\n        return false;\n      }\n      this.datas.routePath = this.$route.path;\n      if (this.form.nextUser) {\n        //处理人多选\n        if (this.form.nextUser.length > 0) {\n          let nextUser = \"\";\n          this.form.nextUser.forEach(e => {\n            nextUser += e.userName + \",\";\n          });\n          //去掉最后一个逗号\n          if (nextUser.length > 0) {\n            nextUser = nextUser.substr(0, nextUser.length - 1);\n          }\n          this.datas.nextUser = nextUser;\n        } else {\n          this.datas.nextUser = this.form.nextUser.userName;\n          this.datas.nextUserNickName = this.form.nextUser.nickName;\n        }\n      } else {\n        if (this.processData.nextUserInfo) {\n          //手动传的用户信息\n          if (this.processData.nextUserInfo.length > 0) {\n            let nextUser = \"\";\n            let nextNick = \"\";\n            this.processData.nextUserInfo.forEach(e => {\n              nextUser += e.userName + \",\";\n              nextNick += e.nickName + \",\";\n            });\n            //去掉最后一个逗号\n            if (nextUser.length > 0) {\n              nextUser = nextUser.substr(0, nextUser.length - 1);\n            }\n            if (nextNick.length > 0) {\n              nextNick = nextNick.substr(0, nextNick.length - 1);\n            }\n            this.datas.nextUser = nextUser;\n            this.datas.nextUserNickName = nextNick;\n          } else {\n            this.datas.nextUser = this.processData.nextUserInfo.userName;\n            this.datas.nextUserNickName = this.processData.nextUserInfo.nickName;\n          }\n        } else {\n          this.datas.nextUser = undefined;\n          this.datas.nextUserNickName = undefined;\n        }\n      }\n      if (\n        !this.datas.nextUser &&\n        this.datas.processType === \"completeByGroup\" &&\n        this.datas.defaultFrom\n      ) {\n        this.$message({\n          type: \"error\",\n          message: \"请选择人员!\"\n        });\n        return;\n      }\n      if (this.form.comment) {\n        this.datas.variables.comment = this.form.comment;\n      }\n      this.$nextTick(() => {\n        // 以服务的方式调用的 Loading 需要异步关闭\n        this.loading = Loading.service({\n          lock: true, //lock的修改符--默认是false\n          text: \"流程进行中，请稍后\", //显示在加载图标下方的加载文案\n          spinner: \"el-icon-loading\", //自定义加载图标类名\n          background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n          target: document.querySelector(\"#dialogAct\")\n        });\n      });\n      let resultData;\n      try {\n        console.log(\"this.datas\", this.datas);\n        let { code, data } = await completeYjrwdTask(this.datas);\n        if (code === \"0000\") {\n          resultData = data;\n        }\n        if (code) {\n          this.$nextTick(() => {\n            // 以服务的方式调用的 Loading 需要异步关闭\n            this.loading.close();\n          });\n        }\n      } catch (e) {\n        this.$nextTick(() => {\n          // 以服务的方式调用的 Loading 需要异步关闭\n          this.loading.close();\n        });\n      }\n      if (resultData) {\n        resultData.processType = this.datas.processType;\n        this.$emit(\"todoData\", resultData);\n      }\n      this.toClose();\n    },\n    toClose() {\n      this.$set(this.form,'nextUser',\"\")\n      this.$set(this.form,'comment',\"\")\n      this.$emit(\"toClose\", \"close\");\n    }\n  }\n};\n</script>\n\n<style scoped></style>\n"], "sourceRoot": "src/components/activiti_yjrwd"}]}