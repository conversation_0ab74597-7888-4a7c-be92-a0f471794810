{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\qxbzk.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\qxbzk.vue", "mtime": 1706897322891}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["qxbzk.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqGA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "qxbzk.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl", "sourcesContent": ["<template>\n  <div>\n    <!--左侧树组件-->\n    <el-row>\n      <el-col :span=\"4\">\n        <el-card class=\"box-card aside_height\" shadow=\"never\">\n          <div slot=\"header\" class=\"clearfix\" style=\"text-align: center\">\n            <span >设备</span>\n          </div>\n          <div style=\" overflow: auto;height: 100vh\">\n            <el-tree :expand-on-click-node=\"false\"\n                     id=\"tree\"\n                     :data=\"treeOptions\"\n                     :default-expanded-keys=\"['1']\"\n                     @node-click=\"handleNodeClick\"\n                     node-key=\"nodeId\"/>\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-white>\n          <el-filter\n            :data=\"filterInfo.data\"\n            :field-list=\"filterInfo.fieldList\"\n            @handleReset=\"getReset\"\n          />\n        </el-white>\n        <div>\n            <el-white class=\"button-group\">\n              <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"addSensorButton\"\n              >新增\n              </el-button>\n<!--              <el-button type=\"warning\" icon=\"el-icon-edit\" @click=\"getUpdateSbflwh\"-->\n<!--              >修改-->\n<!--              </el-button>-->\n              <el-button type=\"danger\" icon=\"el-icon-delete\" :disabled=\"multipleSensor\" @click=\"getDeleteSbflwh\"\n              >删除\n              </el-button>\n            </el-white>\n          <comp-table :table-and-page-info=\"tableAndPageInfo\" @multipleSelection=\"handleSelectionChange\"/>\n\n        </div>\n      </el-col>\n    </el-row>\n    <!-- 弹出框开始 -->\n    <el-dialog title=\"缺陷录入\" :visible.sync=\"dialogFormVisible\" width=\"50%\" v-dialogDrag>\n      <el-form :model=\"form\" label-width=\"130px\" :inline=\"true\">\n        <el-row :gutter=\"8\" class=\"pull-left\" >\n          <el-col :span=\"12\">\n            <el-form-item label=\"分类依据：\" >\n              <el-select v-model=\"form.flyj\" placeholder=\"请选择活动区域\" style=\"width: 100%\">\n                <el-option label=\"XXXX发生损坏\" value=\"1\"></el-option>\n                <el-option label=\"XXX发生渗油\" value=\"2\"></el-option>\n                <el-option label=\"XXX出现断裂\" value=\"3\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"标准描述：\" >\n              <el-select v-model=\"form.bzms\" placeholder=\"标准描述\" style=\"width: 100%\">\n                <el-option label=\"进水或进雪\" value=\"4\"></el-option>\n                <el-option label=\"密封不良\" value=\"5\"></el-option>\n                <el-option label=\"受潮\" value=\"6\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"隐患等级：\" >\n              <el-select v-model=\"form.qxdj\" placeholder=\"请选择活动区域\" style=\"width: 100%\">\n                <el-option label=\"一般C\" value=\"7\"></el-option>\n                <el-option label=\"一般B\" value=\"8\"></el-option>\n                <el-option label=\"一般A\" value=\"9\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"原因类别：\" >\n              <el-select v-model=\"form.yylb\" placeholder=\"请选择活动区域\" style=\"width: 100%\">\n                <el-option label=\"机械部件\" value=\"10\"></el-option>\n                <el-option label=\"本地\" value=\"11\"></el-option>\n                <el-option label=\"其它\" value=\"12\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"qxcommit\" class=\"pmyBtn\">确 定</el-button>\n      </div>\n    </el-dialog>\n    <!-- 弹出框结束 -->\n  </div>\n\n\n</template>\n\n<script>\n\n  import { getPageDataList } from '@/api/dagangOilfield/bzgl/qxbzk'\n\n  export default {\n    name: \"qxbzk\",\n    data() {\n      return {\n        filterInfo: {\n          data: {\n            ywdwArr: [],\n          },\n          fieldList: [\n            {label: '隐患等级', type: 'select', value: 'defectLevel', multiple: true, options: []},\n            {label: '标准描述', type: 'input', value: 'defectLevel'},\n            {label: '设备状态', type: 'select', value: 'defectLevel', multiple: true, options: []},\n            {label: '电压等级', type: 'select', value: 'defectLevel', multiple: true, options: []},\n            {label: '维护班组', type: 'select', value: 'defectLevel'},\n          ]\n        },\n        tableAndPageInfo: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {\n            checkBox: true,\n            serialNumber: true\n          },\n          tableData: [],\n          tableHeader: [\n\n            {prop: 'sbzt', label: '缺陷编码', minWidth: '120'},\n            {prop: 'sbzt', label: '隐患等级', minWidth: '120'},\n            {prop: 'sbzt', label: '标准描述', minWidth: '120'},\n            {\n              prop: 'operation',\n              label: '操作',\n              minWidth: '130px',\n              style: {display: 'block'},\n              //操作列固定再右侧\n              fixed:'right',\n              operation: [\n                {name: '详情', clickFun: this.getDetails},\n                {name: '修改', clickFun: this.getDetails},\n              ]\n            },\n          ]\n        },\n        //弹出框\n        dialogFormVisible:false,\n        //弹出框表单\n        form:{\n\n        },\n\n        loading: false,\n        //组织树\n        treeOptions:[\n          {\n          label: '断路器',\n        }, {\n          label: '1号变压器',\n          children: [{\n            label: '冷却系统',\n            children: [{\n              label: '温控运行情况',\n\n            }, {\n              label: '油箱',\n\n            }, {\n              label: '铁芯',\n\n            }, {\n              label: '绕组',\n\n            }]\n          }]\n        }, {\n            label: '2号变压器',\n            children: [{\n              label: '冷却系统',\n              children: [{\n                label: '温控运行情况',\n\n              }, {\n                label: '油箱',\n\n              }, {\n                label: '铁芯',\n\n              }, {\n                label: '绕组',\n\n              }]\n            }]\n          }, {\n            label: '3号变压器',\n            children: [{\n              label: '冷却系统',\n              children: [{\n                label: '温控运行情况',\n\n              }, {\n                label: '油箱',\n\n              }, {\n                label: '铁芯',\n\n              }, {\n                label: '绕组',\n\n              }]\n            }]\n          }, {\n            label: '4号变压器',\n            children: [{\n              label: '冷却系统',\n              children: [{\n                label: '温控运行情况',\n\n              }, {\n                label: '油箱',\n\n              }, {\n                label: '铁芯',\n\n              }, {\n                label: '绕组',\n\n              }]\n            }]\n          }\n        ],\n        userList:[\n          {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          },{\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          },{\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          },{\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          },\n          {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          },\n          {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          },\n          {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          },\n          {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          },\n          {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          }, {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          }, {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          }, {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          }, {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          }, {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          }, {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          }, {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          }, {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          }, {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          }, {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          }, {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          }, {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          }, {\n            qxcode: 'qx20220113',\n            qxlevel: '一般C',\n            qxdesc:\"1号主变A相有载调压瓦斯继电器中有气体\"\n          },\n        ],\n        //删除是否可用\n        multipleSensor: true,\n        //查询参数\n        queryParams:{\n          pageNum: 1,\n          pageSize: 10,\n          roleKey:'',\n          roleName:'',\n          status:'',\n        },\n        showSearch:true,\n\n      };\n    },\n    watch: {\n    },\n    created() {\n        this.getPageDataList();\n\n    },\n    methods: {\n      /**\n       * 查询数据列表\n       */\n      getPageDataList(){\n        getPageDataList(this.queryParams).then(res=>{\n            this.tableAndPageInfo.tableData=res.data\n        })\n      },\n\n      //添加按钮\n      addSensorButton(){\n        this.dialogFormVisible= true\n\n      },\n      //每页展示数量点击事件\n      handleSizeChange(){\n\n      },\n      //页码改变事件\n      handleCurrentChange(){\n\n      },\n      //树点击事件\n      handleNodeClick(){\n\n      },\n      //缺陷标准库新增完成\n      qxcommit(){\n        this.dialogFormVisible = false;\n        this.$message.success(\"新增成功\")\n      }\n\n    }\n  };\n</script>\n\n<style lang='scss' scoped>\n.head-container{\n  margin: 0 auto;\n  width: 98%;\n  height: calc(100vh - 225px);\n  overflow: auto;\n}\n.box-card{\n  margin-bottom: 15px;\n  .el-card__header{\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n.box-cardList{\n  height: 56%;\n}\n.item{\n  width: 200px;\n  float: left;\n}\n#main_container_dj{\n  height: calc(100vh - 84px);\n}\n.aside_height{\n  height: 96%;\n}\n.defect .el-form-item:nth-child(odd){\n  margin-right: 70px;\n}\n/*背景颜色调整*/\n#main_container_dj,#main_container_dj .el-aside{\n  background-color: #b4caf1;\n}\n/deep/ #qxlr_dialog_insert .el-dialog__header {\n  background-color: #8eb3f5;\n}\n/deep/ .pmyBtn{\n  background:#8eb3f5;\n}\n</style>\n"]}]}