{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\sybwk.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sybzk\\sybwk.vue", "mtime": 1706897323684}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZm9yLWVhY2giKTsKCnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5mdW5jdGlvbi5uYW1lIik7CgpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5mb3ItZWFjaCIpOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Cgp2YXIgX29iamVjdFNwcmVhZDIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkQ6L1NoYW1tcG9vbC93b3JrL2NvZGUvZGd5dC8wMVx1NEVFM1x1NzgwMS9xei11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9vYmplY3RTcHJlYWQyIikpOwoKcmVxdWlyZSgicmVnZW5lcmF0b3ItcnVudGltZS9ydW50aW1lIik7Cgp2YXIgX2FzeW5jVG9HZW5lcmF0b3IyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvYXN5bmNUb0dlbmVyYXRvciIpKTsKCnZhciBfc3lid2sgPSByZXF1aXJlKCJAL2FwaS9kYWdhbmdPaWxmaWVsZC9iemdsL3N5YndrIik7Cgp2YXIgX3NibHh3aCA9IHJlcXVpcmUoIkAvYXBpL2RhZ2FuZ09pbGZpZWxkL2J6Z2wvc2JseHdoL3NibHh3aCIpOwoKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gewogIG5hbWU6ICJzeWJ3ayIsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGN1cnJlbnRVc2VyOiB0aGlzLiRzdG9yZS5nZXR0ZXJzLm5hbWUsCiAgICAgIC8v5paw5aKe5oyJ6ZKu5o6n5Yi2CiAgICAgIGFkZERpc2FibGVkOiB0cnVlLAogICAgICAvL+agkee7k+aehOaHkuWKoOi9veWPguaVsAogICAgICBwcm9wczogewogICAgICAgIGxhYmVsOiAnbmFtZScsCiAgICAgICAgY2hpbGRyZW46ICd6b25lcycsCiAgICAgICAgaXNMZWFmOiBmdW5jdGlvbiBpc0xlYWYoZGF0YSwgbm9kZSkgewogICAgICAgICAgaWYgKG5vZGUubGV2ZWwgPT09IDIpIHsKICAgICAgICAgICAgcmV0dXJuIHRydWU7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9LAogICAgICAvL+WIoOmZpOmAieaLqeWIlwogICAgICBzZWxlY3RSb3dzOiBbXSwKICAgICAgLy/lvLnlh7rmoYbooajljZUKICAgICAgZm9ybToge30sCiAgICAgIC8v5p+l6K+i6K+V6aqM6YOo5L2N5Y+C5pWwCiAgICAgIHF1ZXJ5U3lCd1BhcmFtOiB7CiAgICAgICAgc2JseGJtOiB1bmRlZmluZWQsCiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAKICAgICAgfSwKICAgICAgLy/ngrnlh7vmoJHoioLngrnotYvlgLwKICAgICAgdHJlZUZvcm06IHt9LAogICAgICAvL+ivlemqjOmDqOS9jeWIl+ihqAogICAgICB0YWJsZUFuZFBhZ2VJbmZvOiB7CiAgICAgICAgcGFnZXI6IHsKICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgIHNpemVzOiBbMTAsIDIwLCA1MCwgMTAwXQogICAgICAgIH0sCiAgICAgICAgdGFibGVEYXRhOiBbXSwKICAgICAgICB0YWJsZUhlYWRlcjogW3sKICAgICAgICAgIGxhYmVsOiAn6K6+5aSH57G75Z6LJywKICAgICAgICAgIHByb3A6ICdzYmx4JywKICAgICAgICAgIG1pbldpZHRoOiAnMjAwJwogICAgICAgIH0sIHsKICAgICAgICAgIGxhYmVsOiAn6K6+5aSH57G75Z6L57yW56CBJywKICAgICAgICAgIHByb3A6ICdzYmx4Ym0nLAogICAgICAgICAgbWluV2lkdGg6ICcyMDAnCiAgICAgICAgfSwgewogICAgICAgICAgbGFiZWw6ICfor5Xpqozpg6jkvY0nLAogICAgICAgICAgcHJvcDogJ3N5YncnLAogICAgICAgICAgbWluV2lkdGg6ICcxODAnCiAgICAgICAgfSwgewogICAgICAgICAgbGFiZWw6ICflpIfms6gnLAogICAgICAgICAgcHJvcDogJ2J6JywKICAgICAgICAgIG1pbldpZHRoOiAnMjAwJwogICAgICAgIH0gLy8gewogICAgICAgIC8vICAgcHJvcDogJ29wZXJhdGlvbicsCiAgICAgICAgLy8gICBsYWJlbDogJ+aTjeS9nCcsCiAgICAgICAgLy8gICBtaW5XaWR0aDogJzEwMHB4JywKICAgICAgICAvLyAgIHN0eWxlOiB7ZGlzcGxheTogJ2Jsb2NrJ30sCiAgICAgICAgLy8gICAvL+aTjeS9nOWIl+WbuuWumuWGjeWPs+S+pwogICAgICAgIC8vICAgZml4ZWQ6ICdyaWdodCcsCiAgICAgICAgLy8gICBvcGVyYXRpb246IFsKICAgICAgICAvLyAgICAge25hbWU6ICfkv67mlLknLCBjbGlja0Z1bjogdGhpcy51cGRhdGVEZXRhaWxzfSwKICAgICAgICAvLyAgICAge25hbWU6ICfor6bmg4UnLCBjbGlja0Z1bjogdGhpcy5nZXREZXRhaWxzfSwKICAgICAgICAvLyAgIF0KICAgICAgICAvLyB9LAogICAgICAgIF0sCiAgICAgICAgb3B0aW9uOiB7CiAgICAgICAgICBjaGVja0JveDogdHJ1ZSwKICAgICAgICAgIHNlcmlhbE51bWJlcjogdHJ1ZQogICAgICAgIH0KICAgICAgfSwKICAgICAgLy/nu4Tnu4fmoJEKICAgICAgdHJlZU9wdGlvbnM6IFtdLAogICAgICBpc1Nob3dEZXRhaWxzOiBmYWxzZSwKICAgICAgdGl0bGU6ICcnLAogICAgICAvL+WIoOmZpOaYr+WQpuWPr+eUqAogICAgICBtdWx0aXBsZVNlbnNvcjogdHJ1ZSwKICAgICAgLy/mn6Xor6Llj4LmlbAKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBibTogdW5kZWZpbmVkLAogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwCiAgICAgIH0sCiAgICAgIGlzRGlzYWJsZWQ6IGZhbHNlLAogICAgICBpc1Nob3c6IHRydWUKICAgIH07CiAgfSwKICB3YXRjaDoge30sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIC8v6I635Y+W5pWw5o2u5YiX6KGoCiAgICB0aGlzLmdldERhdGEoKTsgLy/ojrflj5blt6bkvqfmoJHnu5PmnoQKICB9LAogIG1ldGhvZHM6IHsKICAgIC8v5oeS5Yqg6L295Ye95pWwCiAgICBsb2FkTm9kZTogZnVuY3Rpb24gbG9hZE5vZGUobm9kZSwgcmVzb2x2ZSkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwoKICAgICAgdmFyIFRyZWVwYXJhbU1hcCA9IHsKICAgICAgICBwaWQ6ICIiLAogICAgICAgIHNwYkxvZ286IFsi6L6T55S16K6+5aSHIiwgIuWPmOeUteiuvuWkhyIsICLphY3nlLXorr7lpIciXQogICAgICB9OwoKICAgICAgaWYgKG5vZGUubGV2ZWwgPT09IDApIHsKICAgICAgICBUcmVlcGFyYW1NYXAucGlkID0gInNiIjsKICAgICAgICByZXR1cm4gdGhpcy5nZXRUcmVlTm9kZShUcmVlcGFyYW1NYXAsIHJlc29sdmUpOwogICAgICB9CgogICAgICBzZXRUaW1lb3V0KGZ1bmN0aW9uICgpIHsKICAgICAgICBUcmVlcGFyYW1NYXAucGlkID0gbm9kZS5kYXRhLmNvZGU7CgogICAgICAgIF90aGlzLmdldFRyZWVOb2RlKFRyZWVwYXJhbU1hcCwgcmVzb2x2ZSk7CiAgICAgIH0sIDUwMCk7CiAgICB9LAogICAgLy/ojrflj5bmoJHoioLngrnmlbDmja4KICAgIGdldFRyZWVOb2RlOiBmdW5jdGlvbiBnZXRUcmVlTm9kZShwYXJhbU1hcCwgcmVzb2x2ZSkgewogICAgICAoMCwgX3NibHh3aC5nZXREZXZpY2VDbGFzc1RyZWVOb2RlQnlQaWQpKHBhcmFtTWFwKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICB2YXIgdHJlZU5vZGVzID0gW107CiAgICAgICAgcmVzLmRhdGEuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgdmFyIG5vZGUgPSB7CiAgICAgICAgICAgIG5hbWU6IGl0ZW0ubmFtZSwKICAgICAgICAgICAgbGV2ZWw6IGl0ZW0ubGV2ZWwsCiAgICAgICAgICAgIGlkOiBpdGVtLmlkLAogICAgICAgICAgICBwaWQ6IGl0ZW0ucGlkLAogICAgICAgICAgICBsZWFmOiBmYWxzZSwKICAgICAgICAgICAgY29kZTogaXRlbS5jb2RlCiAgICAgICAgICB9OwogICAgICAgICAgdHJlZU5vZGVzLnB1c2gobm9kZSk7CiAgICAgICAgfSk7CiAgICAgICAgcmVzb2x2ZSh0cmVlTm9kZXMpOwogICAgICB9KTsKICAgIH0sCiAgICAvL+a3u+WKoOWQjuehruiupOS/neWtmOaMiemSrgogICAgc2F2ZTogZnVuY3Rpb24gc2F2ZSgpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CgogICAgICAoMCwgX3N5YndrLnNhdmVPclVwZGF0ZSkodGhpcy5mb3JtKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBpZiAocmVzLmNvZGUgPT0gJzAwMDAnKSB7CiAgICAgICAgICBfdGhpczIuJG1lc3NhZ2Uuc3VjY2Vzcygi5pON5L2c5oiQ5Yqf77yBIik7CgogICAgICAgICAgX3RoaXMyLmlzU2hvd0RldGFpbHMgPSBmYWxzZTsKCiAgICAgICAgICBfdGhpczIuZ2V0RGF0YSgpOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy/mtYvor5Xojrflj5bmoJHnu5PmnoQKICAgIFRleHRHZXRUcmVlOiBmdW5jdGlvbiBUZXh0R2V0VHJlZSgpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CgogICAgICAoMCwgX3N5YndrLmdldFNibHhUcmVlKSh7CiAgICAgICAgZnNibHhJZDogInNiIgogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpczMudHJlZU9wdGlvbnMgPSByZXMuZGF0YTsKICAgICAgfSk7CiAgICB9LAogICAgLy/moJHoioLngrnngrnlh7vkuovku7YKICAgIGhhbmRsZU5vZGVDbGljazogZnVuY3Rpb24gaGFuZGxlTm9kZUNsaWNrKGRhdGEpIHsKICAgICAgaWYgKGRhdGEubGV2ZWwgPT09ICcxJykgewogICAgICAgIC8v5byA5pS+5paw5aKe5oyJ6ZKuCiAgICAgICAgdGhpcy5hZGREaXNhYmxlZCA9IGZhbHNlOwogICAgICAgIHRoaXMudHJlZUZvcm0gPSBkYXRhOwogICAgICAgIHRoaXMucXVlcnlTeUJ3UGFyYW0uc2JseGJtID0gZGF0YS5jb2RlOwogICAgICAgIHRoaXMuZ2V0RGF0YSgpOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuYWRkRGlzYWJsZWQgPSB0cnVlOwogICAgICB9CiAgICB9LAogICAgLy/mt7vliqDmjInpkq4KICAgIGFkZFNlbnNvckJ1dHRvbjogZnVuY3Rpb24gYWRkU2Vuc29yQnV0dG9uKCkgewogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlOwogICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZTsKICAgICAgdGhpcy50aXRsZSA9ICfmlrDlop4nOwogICAgICB0aGlzLmZvcm0gPSB7fTsKICAgICAgdGhpcy5mb3JtLnNibHggPSB0aGlzLnRyZWVGb3JtLm5hbWU7CiAgICAgIHRoaXMuZm9ybS5zYmx4Ym0gPSB0aGlzLnRyZWVGb3JtLmNvZGU7CiAgICB9LAogICAgLy/liJfooajmn6Xor6IKICAgIGdldERhdGE6IGZ1bmN0aW9uIGdldERhdGEocGFyYW1zKSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwoKICAgICAgcmV0dXJuICgwLCBfYXN5bmNUb0dlbmVyYXRvcjIuZGVmYXVsdCkoIC8qI19fUFVSRV9fKi9yZWdlbmVyYXRvclJ1bnRpbWUubWFyayhmdW5jdGlvbiBfY2FsbGVlKCkgewogICAgICAgIHZhciBwYXJhbSwgX3lpZWxkJGdldFBhZ2VEYXRhTGlzLCBkYXRhLCBjb2RlOwoKICAgICAgICByZXR1cm4gcmVnZW5lcmF0b3JSdW50aW1lLndyYXAoZnVuY3Rpb24gX2NhbGxlZSQoX2NvbnRleHQpIHsKICAgICAgICAgIHdoaWxlICgxKSB7CiAgICAgICAgICAgIHN3aXRjaCAoX2NvbnRleHQucHJldiA9IF9jb250ZXh0Lm5leHQpIHsKICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICBfY29udGV4dC5wcmV2ID0gMDsKICAgICAgICAgICAgICAgIHBhcmFtID0gKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKCgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7fSwgX3RoaXM0LnF1ZXJ5U3lCd1BhcmFtKSwgcGFyYW1zKTsKICAgICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSA0OwogICAgICAgICAgICAgICAgcmV0dXJuICgwLCBfc3lid2suZ2V0UGFnZURhdGFMaXN0KShwYXJhbSk7CgogICAgICAgICAgICAgIGNhc2UgNDoKICAgICAgICAgICAgICAgIF95aWVsZCRnZXRQYWdlRGF0YUxpcyA9IF9jb250ZXh0LnNlbnQ7CiAgICAgICAgICAgICAgICBkYXRhID0gX3lpZWxkJGdldFBhZ2VEYXRhTGlzLmRhdGE7CiAgICAgICAgICAgICAgICBjb2RlID0gX3lpZWxkJGdldFBhZ2VEYXRhTGlzLmNvZGU7CgogICAgICAgICAgICAgICAgaWYgKGNvZGUgPT09ICcwMDAwJykgewogICAgICAgICAgICAgICAgICBfdGhpczQudGFibGVBbmRQYWdlSW5mby50YWJsZURhdGEgPSBkYXRhLnJlY29yZHM7CiAgICAgICAgICAgICAgICAgIF90aGlzNC50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnRvdGFsID0gZGF0YS50b3RhbDsKICAgICAgICAgICAgICAgIH0KCiAgICAgICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gMTI7CiAgICAgICAgICAgICAgICBicmVhazsKCiAgICAgICAgICAgICAgY2FzZSAxMDoKICAgICAgICAgICAgICAgIF9jb250ZXh0LnByZXYgPSAxMDsKICAgICAgICAgICAgICAgIF9jb250ZXh0LnQwID0gX2NvbnRleHRbImNhdGNoIl0oMCk7CgogICAgICAgICAgICAgIGNhc2UgMTI6CiAgICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5zdG9wKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlLCBudWxsLCBbWzAsIDEwXV0pOwogICAgICB9KSkoKTsKICAgIH0sCgogICAgLyoqCiAgICAgKiDooajmoLzlpJrpgInmoYYKICAgICAqLwogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uocm93cykgewogICAgICB0aGlzLnNlbGVjdFJvd3MgPSByb3dzOwogICAgfSwKICAgIGNsb3NlOiBmdW5jdGlvbiBjbG9zZSgpIHsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gZmFsc2U7CiAgICB9LAogICAgdXBkYXRlRGV0YWlsczogZnVuY3Rpb24gdXBkYXRlRGV0YWlscyhyb3cpIHsKICAgICAgdGhpcy50aXRsZSA9ICfkv67mlLknOwogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlOwogICAgICB0aGlzLmZvcm0gPSByb3c7CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IGZhbHNlOwogICAgICB0aGlzLmlzU2hvdyA9IHRydWU7CiAgICB9LAogICAgZ2V0RGV0YWlsczogZnVuY3Rpb24gZ2V0RGV0YWlscyhyb3cpIHsKICAgICAgdGhpcy50aXRsZSA9ICfor6bmg4UnOwogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlOwogICAgICB0aGlzLmZvcm0gPSByb3c7CiAgICAgIHRoaXMuaXNEaXNhYmxlZCA9IHRydWU7CiAgICAgIHRoaXMuaXNTaG93ID0gZmFsc2U7CiAgICB9LAogICAgLy/liKDpmaTmjInpkq4KICAgIGRlbGV0ZVNlbnNvckJ1dHRvbjogZnVuY3Rpb24gZGVsZXRlU2Vuc29yQnV0dG9uKHJvdykgewogICAgICB2YXIgX3RoaXM1ID0gdGhpczsKCiAgICAgIHRoaXMuZm9ybSA9IHJvdzsKICAgICAgdGhpcy4kY29uZmlybSgn5q2k5pON5L2c5bCG5rC45LmF5Yig6Zmk6K+l5pWw5o2uLCDmmK/lkKbnu6fnu60/JywgJ+aPkOekuicsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogJ+ehruWumicsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogJ+WPlua2iCcsCiAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgICgwLCBfc3lid2sucmVtb3ZlKShbX3RoaXM1LmZvcm0ub2JqSWRdKS50aGVuKGZ1bmN0aW9uIChfcmVmKSB7CiAgICAgICAgICB2YXIgY29kZSA9IF9yZWYuY29kZTsKCiAgICAgICAgICBpZiAoY29kZSA9PT0gJzAwMDAnKSB7CiAgICAgICAgICAgIF90aGlzNS4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLAogICAgICAgICAgICAgIG1lc3NhZ2U6ICfliKDpmaTmiJDlip8hJwogICAgICAgICAgICB9KTsKCiAgICAgICAgICAgIF90aGlzNS50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnBhZ2VSZXNpemUgPSAnWSc7CgogICAgICAgICAgICBfdGhpczUuZ2V0RGF0YSgpOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgX3RoaXM1LiRtZXNzYWdlKHsKICAgICAgICAgICAgICB0eXBlOiAnZXJyb3InLAogICAgICAgICAgICAgIG1lc3NhZ2U6ICfliKDpmaTlpLHotKUhJwogICAgICAgICAgICB9KTsKCiAgICAgICAgICAgIF90aGlzNS50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnBhZ2VSZXNpemUgPSAnWSc7CgogICAgICAgICAgICBfdGhpczUuZ2V0RGF0YSgpOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM1LiRtZXNzYWdlKHsKICAgICAgICAgIHR5cGU6ICdpbmZvJywKICAgICAgICAgIG1lc3NhZ2U6ICflt7Llj5bmtojliKDpmaQnCiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKICAgIC8v5a+85Ye65oyJ6ZKuCiAgICBoYW5kbGVFeHBvcnQ6IGZ1bmN0aW9uIGhhbmRsZUV4cG9ydCgpIHt9CiAgfQp9OwpleHBvcnRzLmRlZmF1bHQgPSBfZGVmYXVsdDs="}, {"version": 3, "sources": ["sybwk.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAmGA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,WAAA,EAAA,KAAA,MAAA,CAAA,OAAA,CAAA,IADA;AAEA;AACA,MAAA,WAAA,EAAA,IAHA;AAIA;AACA,MAAA,KAAA,EAAA;AACA,QAAA,KAAA,EAAA,MADA;AAEA,QAAA,QAAA,EAAA,OAFA;AAGA,QAAA,MAAA,EAAA,gBAAA,IAAA,EAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA;AACA,mBAAA,IAAA;AACA;AACA;AAPA,OALA;AAcA;AACA,MAAA,UAAA,EAAA,EAfA;AAgBA;AACA,MAAA,IAAA,EAAA,EAjBA;AAkBA;AACA,MAAA,cAAA,EAAA;AACA,QAAA,MAAA,EAAA,SADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,QAAA,EAAA;AAHA,OAnBA;AAwBA;AACA,MAAA,QAAA,EAAA,EAzBA;AA0BA;AACA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,EAPA;AAQA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,CAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA,SARA;AA0BA,QAAA,MAAA,EAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,YAAA,EAAA;AAAA;AA1BA,OA3BA;AAuDA;AACA,MAAA,WAAA,EAAA,EAxDA;AA0DA,MAAA,aAAA,EAAA,KA1DA;AA2DA,MAAA,KAAA,EAAA,EA3DA;AA6DA;AACA,MAAA,cAAA,EAAA,IA9DA;AA+DA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,EAAA,EAAA,SADA;AAEA,QAAA,OAAA,EAAA,CAFA;AAGA,QAAA,QAAA,EAAA;AAHA,OAhEA;AAqEA,MAAA,UAAA,EAAA,KArEA;AAsEA,MAAA,MAAA,EAAA;AAtEA,KAAA;AA0EA,GA7EA;AA8EA,EAAA,KAAA,EAAA,EA9EA;AA+EA,EAAA,OA/EA,qBA+EA;AACA;AACA,SAAA,OAAA,GAFA,CAGA;AAGA,GArFA;AAsFA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,QAFA,oBAEA,IAFA,EAEA,OAFA,EAEA;AAAA;;AACA,UAAA,YAAA,GAAA;AACA,QAAA,GAAA,EAAA,EADA;AAEA,QAAA,OAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,MAAA;AAFA,OAAA;;AAIA,UAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA;AACA,QAAA,YAAA,CAAA,GAAA,GAAA,IAAA;AACA,eAAA,KAAA,WAAA,CAAA,YAAA,EAAA,OAAA,CAAA;AACA;;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,YAAA,CAAA,GAAA,GAAA,IAAA,CAAA,IAAA,CAAA,IAAA;;AACA,QAAA,KAAA,CAAA,WAAA,CAAA,YAAA,EAAA,OAAA;AACA,OAHA,EAGA,GAHA,CAAA;AAKA,KAhBA;AAkBA;AACA,IAAA,WAnBA,uBAmBA,QAnBA,EAmBA,OAnBA,EAmBA;AACA,+CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,SAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,GAAA;AACA,YAAA,IAAA,EAAA,IAAA,CAAA,IADA;AAEA,YAAA,KAAA,EAAA,IAAA,CAAA,KAFA;AAGA,YAAA,EAAA,EAAA,IAAA,CAAA,EAHA;AAIA,YAAA,GAAA,EAAA,IAAA,CAAA,GAJA;AAKA,YAAA,IAAA,EAAA,KALA;AAMA,YAAA,IAAA,EAAA,IAAA,CAAA;AANA,WAAA;AAQA,UAAA,SAAA,CAAA,IAAA,CAAA,IAAA;AACA,SAVA;AAWA,QAAA,OAAA,CAAA,SAAA,CAAA;AACA,OAdA;AAeA,KAnCA;AAoCA;AACA,IAAA,IArCA,kBAqCA;AAAA;;AACA,+BAAA,KAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,IAAA,MAAA,EAAA;AACA,UAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,OAAA;;AACA,UAAA,MAAA,CAAA,aAAA,GAAA,KAAA;;AACA,UAAA,MAAA,CAAA,OAAA;AACA;AACA,OANA;AAOA,KA7CA;AA8CA;AACA,IAAA,WA/CA,yBA+CA;AAAA;;AACA,8BAAA;AAAA,QAAA,OAAA,EAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KAnDA;AAsDA;AACA,IAAA,eAvDA,2BAuDA,IAvDA,EAuDA;AACA,UAAA,IAAA,CAAA,KAAA,KAAA,GAAA,EAAA;AACA;AACA,aAAA,WAAA,GAAA,KAAA;AACA,aAAA,QAAA,GAAA,IAAA;AACA,aAAA,cAAA,CAAA,MAAA,GAAA,IAAA,CAAA,IAAA;AACA,aAAA,OAAA;AACA,OANA,MAMA;AACA,aAAA,WAAA,GAAA,IAAA;AACA;AACA,KAjEA;AAkEA;AACA,IAAA,eAnEA,6BAmEA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,KAAA,GAAA,IAAA;AACA,WAAA,IAAA,GAAA,EAAA;AACA,WAAA,IAAA,CAAA,IAAA,GAAA,KAAA,QAAA,CAAA,IAAA;AACA,WAAA,IAAA,CAAA,MAAA,GAAA,KAAA,QAAA,CAAA,IAAA;AACA,KA1EA;AA2EA;AACA,IAAA,OA5EA,mBA4EA,MA5EA,EA4EA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,gBAAA,KAFA,+DAEA,MAAA,CAAA,cAFA,GAEA,MAFA;AAAA;AAAA,uBAGA,4BAAA,KAAA,CAHA;;AAAA;AAAA;AAGA,gBAAA,IAHA,yBAGA,IAHA;AAGA,gBAAA,IAHA,yBAGA,IAHA;;AAIA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,IAAA,CAAA,OAAA;AACA,kBAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,IAAA,CAAA,KAAA;AACA;;AAPA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,KAtFA;;AAwFA;;;AAGA,IAAA,qBA3FA,iCA2FA,IA3FA,EA2FA;AACA,WAAA,UAAA,GAAA,IAAA;AAEA,KA9FA;AAiGA,IAAA,KAjGA,mBAiGA;AACA,WAAA,aAAA,GAAA,KAAA;AACA,KAnGA;AAoGA,IAAA,aApGA,yBAoGA,GApGA,EAoGA;AACA,WAAA,KAAA,GAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,IAAA,GAAA,GAAA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,WAAA,MAAA,GAAA,IAAA;AACA,KA1GA;AA2GA,IAAA,UA3GA,sBA2GA,GA3GA,EA2GA;AACA,WAAA,KAAA,GAAA,IAAA;AACA,WAAA,aAAA,GAAA,IAAA;AACA,WAAA,IAAA,GAAA,GAAA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,WAAA,MAAA,GAAA,KAAA;AACA,KAjHA;AAmHA;AACA,IAAA,kBApHA,8BAoHA,GApHA,EAoHA;AAAA;;AACA,WAAA,IAAA,GAAA,GAAA;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,2BAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,EAAA,IAAA,CAAA,gBAAA;AAAA,cAAA,IAAA,QAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,YAAA,MAAA,CAAA,OAAA;AACA,WAPA,MAOA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,UAAA,GAAA,GAAA;;AACA,YAAA,MAAA,CAAA,OAAA;AACA;AACA,SAhBA;AAiBA,OAtBA,EAsBA,KAtBA,CAsBA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OA3BA;AA6BA,KAnJA;AAoJA;AACA,IAAA,YArJA,0BAqJA,CAEA;AAvJA;AAtFA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row gutter=\"1\">\n      <el-col :span=\"4\">\n        <el-card class=\"aside_height\" shadow=\"never\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>设备类型</span>\n          </div>\n          <div style=\" overflow: auto;height: 84vh\">\n              <el-tree id=\"tree\"\n                       highlight-current\n                       :props=\"props\"\n                       :load=\"loadNode\"\n                       lazy\n                       :default-expanded-keys=\"['1']\"\n                       @node-expand=\"handleNodeClick\"\n                       @node-click=\"handleNodeClick\"/>\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button @click=\"addSensorButton\"\n                       type=\"primary\" icon=\"el-icon-plus\"\n                       :disabled=\"addDisabled\"\n            >新增\n            </el-button>\n            <!--<el-button @click=\"deleteSensorButton\"-->\n            <!--           type=\"danger\" icon=\"el-icon-delete\"-->\n            <!--&gt;删除-->\n            <!--</el-button>-->\n          </div>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"handleSelectionChange\"\n            height=\"78.2vh\">\n            <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\"\n                             width=\"160\"\n                             :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button @click=\"updateDetails(scope.row)\" v-show=\"scope.row.createBy == currentUser\" type=\"text\"\n                           size=\"small\"  title=\"修改\"  class='el-icon-edit'\n                >\n                </el-button>\n                <el-button @click=\"getDetails(scope.row)\" type=\"text\" size=\"small\" title=\"详情\" class=\"el-icon-view\"></el-button>\n                <el-button @click=\"deleteSensorButton(scope.row)\" v-show=\"scope.row.createBy == currentUser\" type=\"text\"\n                           size=\"small\" title=\"删除\" class=\"el-icon-delete\"\n                >\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n    <!--新增\\修改\\详情弹出-->\n    <el-dialog :title=title :visible.sync=\"isShowDetails\" width=\"60%\" v-dialogDrag>\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\">\n        <el-row :gutter=\"8\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"设备类型：\" prop=\"sblx\">\n              <el-input v-model=\"form.sblx\" disabled style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"设备类型编码：\" prop=\"sblxbm\">\n              <el-input v-model=\"form.sblxbm\" disabled style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"试验部位：\" prop=\"sybw\">\n              <el-input placeholder=\"请输入试验部位名称\" v-model=\"form.sybw\" :disabled=\"isDisabled\"\n                        style=\"width: 100%\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"备注：\" prop=\"bz\">\n              <el-input type=\"textarea\" placeholder=\"请输入备注\" v-model=\"form.bz\" :disabled=\"isDisabled\"/>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" v-show=\"isShow\">\n        <el-button @click=\"close\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"save\">确 认</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n\n  import {getPageDataList, remove, saveOrUpdate, getSblxTree} from '@/api/dagangOilfield/bzgl/sybwk'\n  import {getDeviceClassTreeNodeByPid} from '@/api/dagangOilfield/bzgl/sblxwh/sblxwh'\n\n\n  export default {\n    name: \"sybwk\",\n    data() {\n      return {\n        currentUser: this.$store.getters.name,\n        //新增按钮控制\n        addDisabled: true,\n        //树结构懒加载参数\n        props: {\n          label: 'name',\n          children: 'zones',\n          isLeaf: (data, node) => {\n            if (node.level === 2) {\n              return true\n            }\n          },\n        },\n        //删除选择列\n        selectRows: [],\n        //弹出框表单\n        form: {},\n        //查询试验部位参数\n        querySyBwParam: {\n          sblxbm: undefined,\n          pageNum: 1,\n          pageSize: 10\n        },\n        //点击树节点赋值\n        treeForm: {},\n        //试验部位列表\n        tableAndPageInfo: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          tableData: [],\n          tableHeader: [\n            {label: '设备类型', prop: 'sblx', minWidth: '200'},\n            {label: '设备类型编码', prop: 'sblxbm', minWidth: '200'},\n            {label: '试验部位', prop: 'sybw', minWidth: '180'},\n            {label: '备注', prop: 'bz', minWidth: '200'},\n            // {\n            //   prop: 'operation',\n            //   label: '操作',\n            //   minWidth: '100px',\n            //   style: {display: 'block'},\n            //   //操作列固定再右侧\n            //   fixed: 'right',\n            //   operation: [\n            //     {name: '修改', clickFun: this.updateDetails},\n            //     {name: '详情', clickFun: this.getDetails},\n            //   ]\n            // },\n          ],\n          option: {checkBox: true, serialNumber: true},\n        },\n        //组织树\n        treeOptions: [],\n\n        isShowDetails: false,\n        title: '',\n\n        //删除是否可用\n        multipleSensor: true,\n        //查询参数\n        queryParams: {\n          bm: undefined,\n          pageNum: 1,\n          pageSize: 10\n        },\n        isDisabled: false,\n        isShow: true,\n\n\n      };\n    },\n    watch: {},\n    created() {\n      //获取数据列表\n      this.getData();\n      //获取左侧树结构\n\n\n    },\n    methods: {\n      //懒加载函数\n      loadNode(node, resolve) {\n        let TreeparamMap = {\n          pid: \"\",\n          spbLogo: [\"输电设备\", \"变电设备\",\"配电设备\"]\n        };\n        if (node.level === 0) {\n          TreeparamMap.pid = \"sb\";\n          return this.getTreeNode(TreeparamMap, resolve)\n        }\n        setTimeout(() => {\n          TreeparamMap.pid = node.data.code;\n          this.getTreeNode(TreeparamMap, resolve)\n        }, 500)\n\n      },\n\n      //获取树节点数据\n      getTreeNode(paramMap, resolve) {\n        getDeviceClassTreeNodeByPid(paramMap).then(res => {\n          let treeNodes = []\n          res.data.forEach(item => {\n            let node = {\n              name: item.name,\n              level: item.level,\n              id: item.id,\n              pid: item.pid,\n              leaf: false,\n              code: item.code\n            }\n            treeNodes.push(node)\n          })\n          resolve(treeNodes)\n        })\n      },\n      //添加后确认保存按钮\n      save() {\n        saveOrUpdate(this.form).then(res => {\n          if (res.code == '0000') {\n            this.$message.success(\"操作成功！\");\n            this.isShowDetails = false;\n            this.getData();\n          }\n        });\n      },\n      //测试获取树结构\n      TextGetTree() {\n        getSblxTree({fsblxId: \"sb\"}).then(res => {\n          this.treeOptions = res.data;\n        })\n      },\n\n\n      //树节点点击事件\n      handleNodeClick(data) {\n        if (data.level === '1') {\n          //开放新增按钮\n          this.addDisabled = false;\n          this.treeForm = data;\n          this.querySyBwParam.sblxbm = data.code;\n          this.getData();\n        } else {\n          this.addDisabled = true;\n        }\n      },\n      //添加按钮\n      addSensorButton() {\n        this.isShowDetails = true;\n        this.isDisabled = false;\n        this.title = '新增'\n        this.form={};\n        this.form.sblx = this.treeForm.name;\n        this.form.sblxbm = this.treeForm.code;\n      },\n      //列表查询\n      async getData(params) {\n        try {\n          const param = {...this.querySyBwParam, ...params}\n          const {data, code} = await getPageDataList(param);\n          if (code === '0000') {\n            this.tableAndPageInfo.tableData = data.records\n            this.tableAndPageInfo.pager.total = data.total\n          }\n        } catch (e) {\n        }\n      },\n\n      /**\n       * 表格多选框\n       */\n      handleSelectionChange(rows) {\n        this.selectRows = rows\n\n      },\n\n\n      close() {\n        this.isShowDetails = false\n      },\n      updateDetails(row) {\n        this.title = '修改'\n        this.isShowDetails = true;\n        this.form = row;\n        this.isDisabled = false;\n        this.isShow = true;\n      },\n      getDetails(row) {\n        this.title = '详情'\n        this.isShowDetails = true;\n        this.form = row;\n        this.isDisabled = true;\n        this.isShow = false;\n      },\n\n      //删除按钮\n      deleteSensorButton(row) {\n        this.form = row\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          remove([this.form.objId]).then(({code}) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              });\n              this.tableAndPageInfo.pager.pageResize = 'Y';\n              this.getData()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              });\n              this.tableAndPageInfo.pager.pageResize = 'Y';\n              this.getData()\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          });\n        });\n\n      },\n      //导出按钮\n      handleExport() {\n\n      },\n    }\n  };\n</script>\n\n<style lang='scss' scoped>\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  max-height: 79vh;\n  overflow: auto;\n}\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sybzk"}]}