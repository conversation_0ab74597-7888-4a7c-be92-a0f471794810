{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\jxjjhgl\\gdgl\\gzrwdcxtj.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\dagangOilfield\\jxjjhgl\\gdgl\\gzrwdcxtj.js", "mtime": 1706897314680}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0TGlzdCA9IGdldExpc3Q7CmV4cG9ydHMucmVtb3ZlID0gcmVtb3ZlOwpleHBvcnRzLnNhdmVPclVwZGF0ZSA9IHNhdmVPclVwZGF0ZTsKCnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwoKdmFyIGJhc2VVcmwgPSAiL3JlcGFpci1zY2hlZHVsZS1hcGkiOyAvL+WIl+ihqOafpeivogoKZnVuY3Rpb24gZ2V0TGlzdChwYXJhbXMpIHsKICByZXR1cm4gX3JlcXVlc3QuZGVmYXVsdC5yZXF1ZXN0UG9zdChiYXNlVXJsICsgJy9nenJ3ZGN4dGovcGFnZScsIHBhcmFtcywgMSk7Cn0gLy/liKDpmaQKCgpmdW5jdGlvbiByZW1vdmUocGFyYW1zKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcvZ3pyd2RjeHRqL3JlbW92ZScsIHBhcmFtcywgMSk7Cn0gLy/kv67mlLnmiJbmlrDlop4KCgpmdW5jdGlvbiBzYXZlT3JVcGRhdGUocGFyYW1zKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICcvZ3pyd2RjeHRqL3NhdmVPclVwZGF0ZScsIHBhcmFtcywgMSk7Cn0="}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/dagang<PERSON>field/jxjjhgl/gdgl/gzrwdcxtj.js"], "names": ["baseUrl", "getList", "params", "api", "requestPost", "remove", "saveOrUpdate"], "mappings": ";;;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,sBAAhB,C,CACA;;AACO,SAASC,OAAT,CAAiBC,MAAjB,EAAyB;AAC9B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,iBAAxB,EAA0CE,MAA1C,EAAiD,CAAjD,CAAP;AACD,C,CACD;;;AACO,SAASG,MAAT,CAAgBH,MAAhB,EAAwB;AAC7B,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,mBAAxB,EAA4CE,MAA5C,EAAmD,CAAnD,CAAP;AACD,C,CACD;;;AACO,SAASI,YAAT,CAAsBJ,MAAtB,EAA8B;AACnC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,yBAAxB,EAAkDE,MAAlD,EAAyD,CAAzD,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\nconst baseUrl = \"/repair-schedule-api\";\n//列表查询\nexport function getList(params) {\n  return api.requestPost(baseUrl+'/gzrwdcxtj/page',params,1)\n}\n//删除\nexport function remove(params) {\n  return api.requestPost(baseUrl+'/gzrwdcxtj/remove',params,1)\n}\n//修改或新增\nexport function saveOrUpdate(params) {\n  return api.requestPost(baseUrl+'/gzrwdcxtj/saveOrUpdate',params,1)\n}\n\n"]}]}