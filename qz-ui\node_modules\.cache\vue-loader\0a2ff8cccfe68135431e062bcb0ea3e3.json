{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\bddzcz\\dzczcxtj.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\bddzcz\\dzczcxtj.vue", "mtime": 1706897324253}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCBjenBfdGogZnJvbSAnLi9jb21wb25lbnRzL2N6cF90aicKaW1wb3J0IGN6cF9jeCBmcm9tICcuL2NvbXBvbmVudHMvY3pwX2N4JwoKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdkemN6Y3h0aicsCiAgY29tcG9uZW50czogeyBjenBfdGosIGN6cF9jeCB9LAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBhY3RpdmVOYW1lOiAnY3gnCiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICAvL3RhYueCueWHu+S6i+S7tgogICAgaGFuZGxlQ2xpY2sodGFiLCBldmVudCkgewogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["dzczcxtj.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAiBA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "dzczcxtj.vue", "sourceRoot": "src/views/dagangOilfield/czpgl/gfdzcz", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-white>\n      <el-tabs v-model=\"activeName\" type=\"card\" @tab-click=\"handleClick\">\n        <el-tab-pane label=\"查询\" name=\"cx\">\n          <czp_cx></czp_cx>\n        </el-tab-pane>\n        <el-tab-pane label=\"统计\" name=\"tj\">\n          <czp_tj></czp_tj>\n        </el-tab-pane>\n      </el-tabs>\n    </el-white>\n  </div>\n</template>\n\n\n<script>\n  import czp_tj from './components/czp_tj'\n  import czp_cx from './components/czp_cx'\n\n  export default {\n    name: 'dzczcxtj',\n    components: { czp_tj, czp_cx },\n    data() {\n      return {\n        activeName: 'cx'\n      }\n    },\n    methods: {\n      //tab点击事件\n      handleClick(tab, event) {\n      }\n    }\n  }\n</script>\n\n<style scoped>\n\n</style>\n\n"]}]}