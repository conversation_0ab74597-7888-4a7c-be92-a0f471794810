{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\activiti_tzgl\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\components\\activiti_tzgl\\index.vue", "mtime": 1706897321038}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGNvbXBsZXRlVHpnbFRhc2sgfSBmcm9tICJAL2FwaS9hY3Rpdml0aS9wcm9jZXNzVGFzayI7CmltcG9ydCB7IExvYWRpbmcgfSBmcm9tICJlbGVtZW50LXVpIjsKaW1wb3J0IHsgZ2V0VXNlcnMgfSBmcm9tICJAL2FwaS9zeXN0ZW0vdXNlcmdyb3VwIjsKaW1wb3J0IHsgZ2V0R3pwVXNlciB9IGZyb20gIkAvYXBpL3l4Z2wvZ3pwZ2wvZ3pwZ2wiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogImluZGV4IiwKICBwcm9wczogewogICAgLyrmtYHnqIvlj5Hotbflv4XloavmlbDmja4KICAgICAgICBwcm9jZXNzRGF0YTp7CiAgICAgICAgICBwcm9jZXNzRGVmaW5pdGlvbktleToi5rWB56iL5a6a5LmJ55qEa2V5IiwvL+W/heWhqwogICAgICAgICAgdGFza0lkOiLku7vliqFpZO+8jOWmguaenOS7u+WKoeWcqOS7o+WKnuWIl+ihqOaXtu+8jOS8muagueaNruS7o+WKnuWIl+ihqOiOt+WPlnRhc2tpZO+8jOWQpuWImemcgOS8oOWFpeS4muWKoWlk5p2l56Gu5a6adGFzayIsCiAgICAgICAgICBidXNpbmVzc0tleToi5Lia5YqhaWTvvIzlr7nlupTkuJrliqHnmoTkuLvplK4iLC8vdGFza2lk5ZKMYnVzaW5lc3NLZXnkuKTogIXlv4XpobvmnInlhbbkuK3kuIDkuKrmiY3lj6/ku6Xnoa7lrprkuIDkuKp0YXNrCiAgICAgICAgICBidXNpbmVzc1R5cGU6IuS4muWKoeexu+Wei++8jOeUqOS6juWMuuWIhuS4jeWQjOeahOS4muWKoSIvL+W/heWhqwogICAgICAgICAgdmFyaWFibGVzOiLmi5PlsZXlj4LmlbAiLy/mtYHnqIvlrprkuYnkuK3orr7nva7nmoTlj4LmlbAsCiAgICAgICAgICBuZXh0VXNlcjoi5aaC5p6c5rWB56iL5a6e5L6L5Lit5bm25pyq6YWN572u5q+P5LiA5Liq6IqC54K555qE5aSE55CG5Lq677yM5YiZ6ZyA6KaB55So5oi35omL5Yqo6YCJ5oup5q+P5LiA5Liq6IqC54K555qE5aSE55CG5Lq6IiwKICAgICAgICAgIHByb2Nlc3NUeXBlOidjb21wbGV0ZSxyb2xsYmFjaycsCiAgICAgICAgICBkZWZhdWx0RnJvbTp0cnVlLGZhbHNlIOaYr+WQpumcgOimgem7mOiupOihqOWNlQogICAgICAgIH0qLwogICAgcHJvY2Vzc0RhdGE6IHsKICAgICAgdHlwZTogT2JqZWN0LAogICAgICBkZWZhdWx0KCkgewogICAgICAgIHJldHVybiB7CiAgICAgICAgICBkZWZhdWx0RnJvbTogdHJ1ZSwKICAgICAgICAgIHByb2Nlc3NUeXBlOiAiY29tcGxldGUiCiAgICAgICAgfTsKICAgICAgfQogICAgfSwKICAgIC8v5pi+56S66ZqQ6JePCiAgICBpc1Nob3c6IHsKICAgICAgdHlwZTogQm9vbGVhbiwKICAgICAgZGVmYXVsdDogZmFsc2UKICAgIH0sCiAgICAvL+WtkOe7hOS7tum7mOiupOWPguaVsAogICAgb3B0aW9uOiB7CiAgICAgIHR5cGU6IE9iamVjdCwKICAgICAgZGVmYXVsdCgpIHsKICAgICAgICByZXR1cm4geyB0aXRsZTogIuWuoeaJuSIgfTsKICAgICAgfQogICAgfQogIH0sCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGRpc2FibGVkOiBmYWxzZSwKICAgICAgZm9ybToge30sCiAgICAgIGRhdGFzOiB7fSwKICAgICAgbG9hZGluZzogbnVsbCwKICAgICAgZGxkZHNock9wdGlvbnM6IFtdLAogICAgICBranp4c2hyT3B0aW9uczogW10sCiAgICAgIHVzZXJzT3B0aW9uczogW10KICAgIH07CiAgfSwKICB3YXRjaDogewogICAgcHJvY2Vzc0RhdGE6IHsKICAgICAgaGFuZGxlcihuZXdWYWwsIG9sZFZhbCkgewogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgIHRoaXMuZGF0YXMgPSB7IC4uLm5ld1ZhbCB9OwogICAgICAgICAgc3dpdGNoICh0aGlzLmRhdGFzLnN0YXR1cykgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgZ2V0R3pwVXNlcih7CiAgICAgICAgICAgICAgICByeWx4OiAi562+5Y+R5Lq6IiwKICAgICAgICAgICAgICAgIGR3OiAiMzAxMCIKICAgICAgICAgICAgICB9KS50aGVuKHJlcyA9PiB7CiAgICAgICAgICAgICAgICB0aGlzLnVzZXJzT3B0aW9ucyA9IHJlcy5kYXRhOwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgICBjYXNlIDE6CiAgICAgICAgICAgICAgZ2V0VXNlcnMoeyBwZXJzb25Hcm91cElkOiA1MiwgZGVwdElkOiAiIiwgZGVwdE5hbWU6ICIiIH0pLnRoZW4oCiAgICAgICAgICAgICAgICByZXMgPT4gewogICAgICAgICAgICAgICAgICB0aGlzLnVzZXJzT3B0aW9ucyA9IHJlcy5kYXRhOwogICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICk7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICBnZXRVc2Vycyh7IHBlcnNvbkdyb3VwSWQ6IDUzLCBkZXB0SWQ6ICIiLCBkZXB0TmFtZTogIiIgfSkudGhlbigKICAgICAgICAgICAgICAgIHJlcyA9PiB7CiAgICAgICAgICAgICAgICAgIHRoaXMudXNlcnNPcHRpb25zID0gcmVzLmRhdGE7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgKTsKICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgY2FzZSAzOgogICAgICAgICAgICAgIGdldFVzZXJzKHsgcGVyc29uR3JvdXBJZDogMTA4LCBkZXB0SWQ6ICIiLCBkZXB0TmFtZTogIiIgfSkudGhlbigKICAgICAgICAgICAgICAgIHJlcyA9PiB7CiAgICAgICAgICAgICAgICAgIHRoaXMuZGxkZHNock9wdGlvbnMgPSByZXMuZGF0YTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICApOwogICAgICAgICAgICAgIGdldFVzZXJzKHsgcGVyc29uR3JvdXBJZDogMTA5LCBkZXB0SWQ6ICIiLCBkZXB0TmFtZTogIiIgfSkudGhlbigKICAgICAgICAgICAgICAgIHJlcyA9PiB7CiAgICAgICAgICAgICAgICAgIHRoaXMua2p6eHNock9wdGlvbnMgPSByZXMuZGF0YTsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICApOwogICAgICAgICAgICAgIGJyZWFrOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9LAogICAgICBkZWVwOiB0cnVlLAogICAgICBpbW1lZGlhdGU6IHRydWUKICAgIH0KICB9LAogIG1vdW50ZWQoKSB7fSwKICBtZXRob2RzOiB7CiAgICAvL+a1geeoi+aPkOS6pO+8jOa1geeoi+aLkue7nQogICAgYXN5bmMgdG9kb1N1Ym1pdCgpIHsKICAgICAgdGhpcy5kYXRhcy5yb3V0ZVBhdGggPSB0aGlzLiRyb3V0ZS5wYXRoOwogICAgICBpZiAodGhpcy5mb3JtLm5leHRVc2VyKSB7CiAgICAgICAgdGhpcy5kYXRhcy5uZXh0VXNlciA9IHRoaXMuZm9ybS5uZXh0VXNlci51c2VyTmFtZTsKICAgICAgICB0aGlzLmRhdGFzLm5leHRVc2VyTmlja05hbWUgPSB0aGlzLmZvcm0ubmV4dFVzZXIubmlja05hbWU7CiAgICAgIH0KCiAgICAgIGlmICh0aGlzLmZvcm0ubmV4dFVzZXIyKSB7CiAgICAgICAgdGhpcy5kYXRhcy5uZXh0VXNlcjIgPSB0aGlzLmZvcm0ubmV4dFVzZXIyLnVzZXJOYW1lOwogICAgICAgIHRoaXMuZGF0YXMubmV4dFVzZXJOaWNrTmFtZTIgPSB0aGlzLmZvcm0ubmV4dFVzZXIyLm5pY2tOYW1lOwogICAgICB9CiAgICAgIGlmICh0aGlzLmRhdGFzLnRhc2tJZCA9PT0gInNja2xkc3AiKSB7CiAgICAgICAgaWYgKCF0aGlzLmRhdGFzLm5leHRVc2VyIHx8ICF0aGlzLmRhdGFzLm5leHRVc2VyMikgewogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgIHR5cGU6ICJlcnJvciIsCiAgICAgICAgICAgIG1lc3NhZ2U6ICLor7fpgInmi6nkurrlkZghIgogICAgICAgICAgfSk7CiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQogICAgICB9CiAgICAgIGlmICgKICAgICAgICAhdGhpcy5kYXRhcy5uZXh0VXNlciAmJgogICAgICAgIHRoaXMuZGF0YXMucHJvY2Vzc1R5cGUgPT09ICJjb21wbGV0ZSIgJiYKICAgICAgICB0aGlzLmRhdGFzLmRlZmF1bHRGcm9tCiAgICAgICkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgIG1lc3NhZ2U6ICLor7fpgInmi6nkurrlkZghIgogICAgICAgIH0pOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBpZiAodGhpcy5mb3JtLmNvbW1lbnQpIHsKICAgICAgICB0aGlzLmRhdGFzLnZhcmlhYmxlcy5jb21tZW50ID0gdGhpcy5mb3JtLmNvbW1lbnQ7CiAgICAgIH0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIC8vIOS7peacjeWKoeeahOaWueW8j+iwg+eUqOeahCBMb2FkaW5nIOmcgOimgeW8guatpeWFs+mXrQogICAgICAgIHRoaXMubG9hZGluZyA9IExvYWRpbmcuc2VydmljZSh7CiAgICAgICAgICBsb2NrOiB0cnVlLCAvL2xvY2vnmoTkv67mlLnnrKYtLem7mOiupOaYr2ZhbHNlCiAgICAgICAgICB0ZXh0OiAi5rWB56iL6L+b6KGM5Lit77yM6K+356iN5ZCOIiwgLy/mmL7npLrlnKjliqDovb3lm77moIfkuIvmlrnnmoTliqDovb3mlofmoYgKICAgICAgICAgIHNwaW5uZXI6ICJlbC1pY29uLWxvYWRpbmciLCAvL+iHquWumuS5ieWKoOi9veWbvuagh+exu+WQjQogICAgICAgICAgYmFja2dyb3VuZDogInJnYmEoMCwgMCwgMCwgMC43KSIsIC8v6YGu572p5bGC6aKc6ImyCiAgICAgICAgICB0YXJnZXQ6IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoIiNkaWFsb2dBY3QiKQogICAgICAgIH0pOwogICAgICB9KTsKICAgICAgbGV0IHJlc3VsdERhdGE7CiAgICAgIHRyeSB7CiAgICAgICAgbGV0IHsgY29kZSwgZGF0YSB9ID0gYXdhaXQgY29tcGxldGVUemdsVGFzayh0aGlzLmRhdGFzKTsKICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICByZXN1bHREYXRhID0gZGF0YTsKICAgICAgICB9CiAgICAgICAgaWYgKGNvZGUpIHsKICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgICAgLy8g5Lul5pyN5Yqh55qE5pa55byP6LCD55So55qEIExvYWRpbmcg6ZyA6KaB5byC5q2l5YWz6ZetCiAgICAgICAgICAgIHRoaXMubG9hZGluZy5jbG9zZSgpOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgLy8g5Lul5pyN5Yqh55qE5pa55byP6LCD55So55qEIExvYWRpbmcg6ZyA6KaB5byC5q2l5YWz6ZetCiAgICAgICAgICB0aGlzLmxvYWRpbmcuY2xvc2UoKTsKICAgICAgICB9KTsKICAgICAgfQogICAgICBpZiAocmVzdWx0RGF0YSkgewogICAgICAgIHJlc3VsdERhdGEucHJvY2Vzc1R5cGUgPSB0aGlzLmRhdGFzLnByb2Nlc3NUeXBlOwogICAgICAgIHRoaXMuJGVtaXQoInRvZG9EYXRhIiwgcmVzdWx0RGF0YSk7CiAgICAgIH0KICAgICAgdGhpcy50b0Nsb3NlKCk7CiAgICB9LAogICAgdG9DbG9zZSgpIHsKICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwnbmV4dFVzZXInLCIiKQogICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLCduZXh0VXNlcjInLCIiKQogICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLCdjb21tZW50JywiIikKICAgICAgdGhpcy4kZW1pdCgidG9DbG9zZSIsICJjbG9zZSIpOwogICAgfQogIH0KfTsK"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/activiti_tzgl", "sourcesContent": ["<template>\n  <div>\n    <el-dialog\n      id=\"dialogAct\"\n      class=\"tree\"\n      :title=\"option.title ? option.title : ''\"\n      :top=\"option.top ? option.top : '10vh'\"\n      v-dialogDrag\n      :visible.sync=\"isShow\"\n      :width=\"option.width ? option.width : '25%'\"\n      :close-on-click-modal=\"false\"\n      @close=\"toClose\"\n      :destroy-on-colse=\"true\"\n      :modal-append-to-body=\"false\"\n    >\n      <slot name=\"Dialog_content\" />\n      <div v-if=\"datas.defaultFrom\">\n        <el-form\n          label-width=\"120px\"\n          ref=\"form\"\n          :model=\"form\"\n          v-if=\"datas.taskId === 'sckldsp'\"\n        >\n          <div>\n            <el-row>\n              <div v-if=\"datas.processType === 'complete'\">\n                <el-col :span=\"24\">\n                  <el-form-item prop=\"nextUser2\" label=\"电力调度方式岗\">\n                    <el-select\n                      v-model=\"form.nextUser2\"\n                      placeholder=\"请选择人员\"\n                      style=\"width: 100%;\"\n                      value-key=\"userName\"\n                      clearable\n                      filterable\n                    >\n                      <el-option\n                        v-for=\"item in dlddshrOptions\"\n                        :key=\"item.userName\"\n                        :label=\"item.nickName\"\n                        :value=\"item\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n                <el-col :span=\"24\">\n                  <el-form-item prop=\"nextUser\" label=\"科技信息中心远动岗\">\n                    <el-select\n                      v-model=\"form.nextUser\"\n                      placeholder=\"请选择人员\"\n                      style=\"width: 100%;\"\n                      value-key=\"userName\"\n                      clearable\n                      filterable\n                    >\n                      <el-option\n                        v-for=\"item in kjzxshrOptions\"\n                        :key=\"item.userName\"\n                        :label=\"item.nickName\"\n                        :value=\"{\n                          userName: item.userName,\n                          nickName: item.nickName\n                        }\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n              </div>\n            </el-row>\n          </div>\n        </el-form>\n        <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" v-else>\n          <div>\n            <el-row>\n              <div v-if=\"datas.processType === 'complete'\">\n                <el-col :span=\"24\">\n                  <el-form-item prop=\"nextUser\" label=\"审核人\">\n                    <el-select\n                      v-model=\"form.nextUser\"\n                      placeholder=\"请选择人员\"\n                      style=\"width: 100%;\"\n                      value-key=\"userName\"\n                      clearable\n                      filterable\n                    >\n                      <el-option\n                        v-for=\"item in usersOptions\"\n                        :key=\"item.userName\"\n                        :label=\"item.nickName\"\n                        :value=\"{\n                          userName: item.userName,\n                          nickName: item.nickName\n                        }\"\n                      >\n                      </el-option>\n                    </el-select>\n                  </el-form-item>\n                </el-col>\n              </div>\n              <el-col :span=\"24\">\n                <el-form-item\n                  prop=\"comment\"\n                  label=\"回退原因：\"\n                  v-if=\"datas.processType === 'rollback'\"\n                >\n                  <el-input\n                    style=\"width: 100%\"\n                    v-model=\"form.comment\"\n                    type=\"textarea\"\n                    placeholder=\"请输入原因\"\n                  />\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </div>\n        </el-form>\n      </div>\n      <div v-else>\n        <span>请确定是否提交</span>\n      </div>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"info\" @click=\"toClose\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"todoSubmit('form')\">确定</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { completeTzglTask } from \"@/api/activiti/processTask\";\nimport { Loading } from \"element-ui\";\nimport { getUsers } from \"@/api/system/usergroup\";\nimport { getGzpUser } from \"@/api/yxgl/gzpgl/gzpgl\";\nexport default {\n  name: \"index\",\n  props: {\n    /*流程发起必填数据\n        processData:{\n          processDefinitionKey:\"流程定义的key\",//必填\n          taskId:\"任务id，如果任务在代办列表时，会根据代办列表获取taskid，否则需传入业务id来确定task\",\n          businessKey:\"业务id，对应业务的主键\",//taskid和businessKey两者必须有其中一个才可以确定一个task\n          businessType:\"业务类型，用于区分不同的业务\"//必填\n          variables:\"拓展参数\"//流程定义中设置的参数,\n          nextUser:\"如果流程实例中并未配置每一个节点的处理人，则需要用户手动选择每一个节点的处理人\",\n          processType:'complete,rollback',\n          defaultFrom:true,false 是否需要默认表单\n        }*/\n    processData: {\n      type: Object,\n      default() {\n        return {\n          defaultFrom: true,\n          processType: \"complete\"\n        };\n      }\n    },\n    //显示隐藏\n    isShow: {\n      type: Boolean,\n      default: false\n    },\n    //子组件默认参数\n    option: {\n      type: Object,\n      default() {\n        return { title: \"审批\" };\n      }\n    }\n  },\n  data() {\n    return {\n      disabled: false,\n      form: {},\n      datas: {},\n      loading: null,\n      dlddshrOptions: [],\n      kjzxshrOptions: [],\n      usersOptions: []\n    };\n  },\n  watch: {\n    processData: {\n      handler(newVal, oldVal) {\n        this.$nextTick(() => {\n          this.datas = { ...newVal };\n          switch (this.datas.status) {\n            case 0:\n              getGzpUser({\n                rylx: \"签发人\",\n                dw: \"3010\"\n              }).then(res => {\n                this.usersOptions = res.data;\n              });\n              break;\n            case 1:\n              getUsers({ personGroupId: 52, deptId: \"\", deptName: \"\" }).then(\n                res => {\n                  this.usersOptions = res.data;\n                }\n              );\n              break;\n            case 2:\n              getUsers({ personGroupId: 53, deptId: \"\", deptName: \"\" }).then(\n                res => {\n                  this.usersOptions = res.data;\n                }\n              );\n              break;\n            case 3:\n              getUsers({ personGroupId: 108, deptId: \"\", deptName: \"\" }).then(\n                res => {\n                  this.dlddshrOptions = res.data;\n                }\n              );\n              getUsers({ personGroupId: 109, deptId: \"\", deptName: \"\" }).then(\n                res => {\n                  this.kjzxshrOptions = res.data;\n                }\n              );\n              break;\n          }\n        });\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  mounted() {},\n  methods: {\n    //流程提交，流程拒绝\n    async todoSubmit() {\n      this.datas.routePath = this.$route.path;\n      if (this.form.nextUser) {\n        this.datas.nextUser = this.form.nextUser.userName;\n        this.datas.nextUserNickName = this.form.nextUser.nickName;\n      }\n\n      if (this.form.nextUser2) {\n        this.datas.nextUser2 = this.form.nextUser2.userName;\n        this.datas.nextUserNickName2 = this.form.nextUser2.nickName;\n      }\n      if (this.datas.taskId === \"sckldsp\") {\n        if (!this.datas.nextUser || !this.datas.nextUser2) {\n          this.$message({\n            type: \"error\",\n            message: \"请选择人员!\"\n          });\n          return;\n        }\n      }\n      if (\n        !this.datas.nextUser &&\n        this.datas.processType === \"complete\" &&\n        this.datas.defaultFrom\n      ) {\n        this.$message({\n          type: \"error\",\n          message: \"请选择人员!\"\n        });\n        return;\n      }\n      if (this.form.comment) {\n        this.datas.variables.comment = this.form.comment;\n      }\n      this.$nextTick(() => {\n        // 以服务的方式调用的 Loading 需要异步关闭\n        this.loading = Loading.service({\n          lock: true, //lock的修改符--默认是false\n          text: \"流程进行中，请稍后\", //显示在加载图标下方的加载文案\n          spinner: \"el-icon-loading\", //自定义加载图标类名\n          background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n          target: document.querySelector(\"#dialogAct\")\n        });\n      });\n      let resultData;\n      try {\n        let { code, data } = await completeTzglTask(this.datas);\n        if (code === \"0000\") {\n          resultData = data;\n        }\n        if (code) {\n          this.$nextTick(() => {\n            // 以服务的方式调用的 Loading 需要异步关闭\n            this.loading.close();\n          });\n        }\n      } catch (e) {\n        this.$nextTick(() => {\n          // 以服务的方式调用的 Loading 需要异步关闭\n          this.loading.close();\n        });\n      }\n      if (resultData) {\n        resultData.processType = this.datas.processType;\n        this.$emit(\"todoData\", resultData);\n      }\n      this.toClose();\n    },\n    toClose() {\n      this.$set(this.form,'nextUser',\"\")\n      this.$set(this.form,'nextUser2',\"\")\n      this.$set(this.form,'comment',\"\")\n      this.$emit(\"toClose\", \"close\");\n    }\n  }\n};\n</script>\n\n<style scoped></style>\n"]}]}