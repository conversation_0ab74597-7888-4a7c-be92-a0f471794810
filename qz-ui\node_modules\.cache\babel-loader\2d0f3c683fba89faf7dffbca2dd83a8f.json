{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\ztlxxdAndPdyj.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\ztlxxdAndPdyj.vue", "mtime": 1706897323231}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuZnVuY3Rpb24ubmFtZSIpOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Cgp2YXIgX3p0bHh4ZCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC92aWV3cy9kYWdhbmdPaWxmaWVsZC9iemdsL3NienRwamJ6ay96dGx4eGQiKSk7Cgp2YXIgX3BkeWogPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdmlld3MvZGFnYW5nT2lsZmllbGQvYnpnbC9zYnp0cGpiemsvcGR5aiIpKTsKCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IHsKICBwcm9wczogewogICAgbXBEYXRhOiB7CiAgICAgIHR5cGU6IE9iamVjdAogICAgfQogIH0sCiAgbmFtZTogJ3p0bHh4ZEFuZFBkeWonLAogIGNvbXBvbmVudHM6IHsKICAgIFp0bHh4ZDogX3p0bHh4ZC5kZWZhdWx0LAogICAgUGR5ajogX3BkeWouZGVmYXVsdAogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGFjdGl2ZU5hbWU6ICdmaXJzdCcsCiAgICAgIHRhYnNJbmRleDogMQogICAgfTsKICB9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7fSwKICBtZXRob2RzOiB7CiAgICAvL3RhYuS5n+WIh+aNoueCueWHu+S6i+S7tgogICAgaGFuZGxlQ2xpY2s6IGZ1bmN0aW9uIGhhbmRsZUNsaWNrKHRhYikgewogICAgICB0aGlzLnRhYnNJbmRleCA9IHRhYi5uYW1lID09PSAnZmlyc3QnID8gMSA6IDI7CiAgICB9LAogICAgLy/lhbPpl63lvLnnqpcKICAgIGNsb3NlRGlhbG9nOiBmdW5jdGlvbiBjbG9zZURpYWxvZygpIHsKICAgICAgdGhpcy4kZW1pdCgnY2xvc2VQYXJhbURpYWxvZycpOwogICAgfQogIH0KfTsKZXhwb3J0cy5kZWZhdWx0ID0gX2RlZmF1bHQ7"}, {"version": 3, "sources": ["ztlxxdAndPdyj.vue"], "names": [], "mappings": ";;;;;;;;;;;AAmBA;;AACA;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,KAAA,EAAA;AACA,IAAA,MAAA,EAAA;AACA,MAAA,IAAA,EAAA;AADA;AADA,GADA;AAMA,EAAA,IAAA,EAAA,eANA;AAOA,EAAA,UAAA,EAAA;AAAA,IAAA,MAAA,EAAA,eAAA;AAAA,IAAA,IAAA,EAAA;AAAA,GAPA;AAQA,EAAA,IARA,kBAQA;AACA,WAAA;AACA,MAAA,UAAA,EAAA,OADA;AAEA,MAAA,SAAA,EAAA;AAFA,KAAA;AAIA,GAbA;AAcA,EAAA,OAdA,qBAcA,CACA,CAfA;AAgBA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,WAFA,uBAEA,GAFA,EAEA;AACA,WAAA,SAAA,GAAA,GAAA,CAAA,IAAA,KAAA,OAAA,GAAA,CAAA,GAAA,CAAA;AACA,KAJA;AAKA;AACA,IAAA,WANA,yBAMA;AACA,WAAA,KAAA,CAAA,kBAAA;AACA;AARA;AAhBA,C", "sourcesContent": ["<template>\n  <div>\n    <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\n      <el-tab-pane label=\"状态量信息点\" name=\"first\">\n        <Ztlxxd ref=\"ztlxxd\" v-if=\"tabsIndex === 1 \" :mp-data=\"mpData\"/>\n      </el-tab-pane>\n      <el-tab-pane label=\"判断依据\" name=\"second\">\n        <Pdyj ref=\"pdyj\" v-if=\"tabsIndex === 2 \" :mp-data=\"mpData\" :sblx=\"mpData.sblxId\"/>\n      </el-tab-pane>\n    </el-tabs>\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"closeDialog\" style=\"margin-left: 92%;margin-top: 1%;\">取 消</el-button>\n    </div>\n  </div>\n</template>\n\n\n\n<script>\nimport Ztlxxd from \"@/views/dagangOilfield/bzgl/sbztpjbzk/ztlxxd\";\nimport Pdyj from \"@/views/dagangOilfield/bzgl/sbztpjbzk/pdyj\";\n\nexport default {\n  props: {\n    mpData: {\n      type: Object\n    }\n  },\nname:'ztlxxdAndPdyj',\ncomponents:{Ztlxxd,Pdyj},\n data() {\n    return {\n      activeName: 'first',\n      tabsIndex: 1\n    }\n  },\n  mounted() {\n  },\n    methods: {\n    //tab也切换点击事件\n    handleClick(tab) {\n      this.tabsIndex = tab.name === 'first' ? 1 : 2\n    },\n    //关闭弹窗\n    closeDialog() {\n      this.$emit('closeParamDialog')\n    }\n  }\n}\n</script>\n\n<style scoped>\n\n</style>\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk"}]}