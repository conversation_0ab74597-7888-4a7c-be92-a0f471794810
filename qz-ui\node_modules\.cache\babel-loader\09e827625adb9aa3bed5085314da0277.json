{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sdsbgl\\powercheck.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sdsbgl\\powercheck.vue", "mtime": 1706897324964}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZm9yLWVhY2giKTsKCnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAiKTsKCnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLmZvci1lYWNoIik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKCnZhciBfdHlwZW9mMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovU2hhbW1wb29sL3dvcmsvY29kZS9kZ3l0LzAxXHU0RUUzXHU3ODAxL3F6LXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL3R5cGVvZiIpKTsKCnZhciBfb2JqZWN0U3ByZWFkMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRDovU2hhbW1wb29sL3dvcmsvY29kZS9kZ3l0LzAxXHU0RUUzXHU3ODAxL3F6LXVpL25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL29iamVjdFNwcmVhZDIiKSk7CgpyZXF1aXJlKCJyZWdlbmVyYXRvci1ydW50aW1lL3J1bnRpbWUiKTsKCnZhciBfYXN5bmNUb0dlbmVyYXRvcjIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkQ6L1NoYW1tcG9vbC93b3JrL2NvZGUvZGd5dC8wMVx1NEVFM1x1NzgwMS9xei11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9hc3luY1RvR2VuZXJhdG9yIikpOwoKdmFyIF9wb3dlcmNoZWNrID0gcmVxdWlyZSgiQC9hcGkvZGFnYW5nT2lsZmllbGQvYXNzZXQvcG93ZXJjaGVjayIpOwoKdmFyIF9maWxlID0gcmVxdWlyZSgiQC9hcGkvdG9vbC9maWxlIik7CgovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSB7CiAgbmFtZTogInF4YnprIiwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIHN0YXRlTGlzdDogW3sKICAgICAgICBsYWJlbDogIuWuoeaguOmAmui/hyIsCiAgICAgICAgdmFsdWU6ICIxIgogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICLlrqHmoLjmnKrpgJrov4ciLAogICAgICAgIHZhbHVlOiAiMiIKICAgICAgfV0sCiAgICAgIHR5cGVMaXN0OiBbewogICAgICAgIGxhYmVsOiAi6Ieq5Yqo5qCh6aqMIiwKICAgICAgICB2YWx1ZTogMAogICAgICB9LCB7CiAgICAgICAgbGFiZWw6ICLkurrlt6Xkv67mraMiLAogICAgICAgIHZhbHVlOiAxCiAgICAgIH1dLAogICAgICBzaG93OiBmYWxzZSwKICAgICAgZmlsdGVySW5mbzogewogICAgICAgIGRhdGE6IHsKICAgICAgICAgIHRvd2VyTmFtZTogIiIsCiAgICAgICAgICB0b3dlck51bWJlcjogIiIsCiAgICAgICAgICBsb25naXR1ZGU6ICIiLAogICAgICAgICAgbGF0aXR1ZGU6ICIiLAogICAgICAgICAgZGlzdGFuY2U6ICIiLAogICAgICAgICAgY3JlYXRlVXNlck5hbWU6ICIiLAogICAgICAgICAgdHlycUFycjogW10sCiAgICAgICAgICB0eXBlOiAiIiwKICAgICAgICAgIHN0YXRlQ246ICIiCiAgICAgICAgfSwKICAgICAgICBmaWVsZExpc3Q6IFt7CiAgICAgICAgICBsYWJlbDogIuWhlOadhuWQjeensCIsCiAgICAgICAgICB0eXBlOiAiaW5wdXQiLAogICAgICAgICAgdmFsdWU6ICJ0b3dlck5hbWUiCiAgICAgICAgfSwgewogICAgICAgICAgbGFiZWw6ICLmnYbloZTnvJblj7ciLAogICAgICAgICAgdHlwZTogImlucHV0IiwKICAgICAgICAgIHZhbHVlOiAidG93ZXJOdW1iZXIiCiAgICAgICAgfSwgewogICAgICAgICAgbGFiZWw6ICLmoKHlh4bnu4/luqYiLAogICAgICAgICAgdHlwZTogImlucHV0IiwKICAgICAgICAgIHZhbHVlOiAibG9uZ2l0dWRlIgogICAgICAgIH0sIHsKICAgICAgICAgIGxhYmVsOiAi5qCh5YeG57qs5bqmIiwKICAgICAgICAgIHR5cGU6ICJpbnB1dCIsCiAgICAgICAgICB2YWx1ZTogImxhdGl0dWRlIgogICAgICAgIH0sIHsKICAgICAgICAgIGxhYmVsOiAi5LiO5Y6f5Z2Q5qCH6Led56a7IiwKICAgICAgICAgIHR5cGU6ICJpbnB1dCIsCiAgICAgICAgICB2YWx1ZTogImRpc3RhbmNlIgogICAgICAgIH0sIHsKICAgICAgICAgIGxhYmVsOiAi5o+Q5Lqk5Lq6IiwKICAgICAgICAgIHR5cGU6ICJpbnB1dCIsCiAgICAgICAgICB2YWx1ZTogImNyZWF0ZVVzZXJOYW1lIgogICAgICAgIH0sIHsKICAgICAgICAgIGxhYmVsOiAi5o+Q5Lqk5pe26Ze0IiwKICAgICAgICAgIHR5cGU6ICJkYXRlIiwKICAgICAgICAgIHZhbHVlOiAidHlycUFyciIsCiAgICAgICAgICBkYXRlVHlwZTogImRhdGVyYW5nZSIKICAgICAgICB9LCB7CiAgICAgICAgICBsYWJlbDogIuagoemqjOexu+WeiyIsCiAgICAgICAgICB0eXBlOiAic2VsZWN0IiwKICAgICAgICAgIHZhbHVlOiAidHlwZSIsCiAgICAgICAgICBvcHRpb25zOiBbewogICAgICAgICAgICBsYWJlbDogIuiHquWKqOagoemqjCIsCiAgICAgICAgICAgIHZhbHVlOiAiMCIKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgbGFiZWw6ICLkurrlt6Xkv67mraMiLAogICAgICAgICAgICB2YWx1ZTogIjEiCiAgICAgICAgICB9XSwKICAgICAgICAgIGNsZWFyYWJsZTogdHJ1ZQogICAgICAgIH0sIHsKICAgICAgICAgIGxhYmVsOiAi54q25oCBIiwKICAgICAgICAgIHR5cGU6ICJjaGVja2JveCIsCiAgICAgICAgICBjaGVja2JveFZhbHVlOiBbXSwKICAgICAgICAgIHZhbHVlOiAic3RhdGVDbiIsCiAgICAgICAgICBvcHRpb25zOiBbewogICAgICAgICAgICBsYWJlbDogIuacquWuoeaguCIsCiAgICAgICAgICAgIHZhbHVlOiAiMCIKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgbGFiZWw6ICLlrqHmoLjpgJrov4ciLAogICAgICAgICAgICB2YWx1ZTogIjEiCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIGxhYmVsOiAi5a6h5qC45pyq6YCa6L+HIiwKICAgICAgICAgICAgdmFsdWU6ICIyIgogICAgICAgICAgfV0sCiAgICAgICAgICBjbGVhcmFibGU6IHRydWUKICAgICAgICB9XQogICAgICB9LAogICAgICB0YWJsZUFuZFBhZ2VJbmZvOiB7CiAgICAgICAgcGFnZXI6IHsKICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgIHNpemVzOiBbMTAsIDIwLCA1MCwgMTAwXQogICAgICAgIH0sCiAgICAgICAgb3B0aW9uOiB7CiAgICAgICAgICBjaGVja0JveDogdHJ1ZSwKICAgICAgICAgIHNlcmlhbE51bWJlcjogdHJ1ZQogICAgICAgIH0sCiAgICAgICAgdGFibGVEYXRhOiBbXSwKICAgICAgICB0YWJsZUhlYWRlcjogW3sKICAgICAgICAgIHByb3A6ICJ0b3dlck5hbWUiLAogICAgICAgICAgbGFiZWw6ICLmnYbloZTlkI3np7AiLAogICAgICAgICAgbWluV2lkdGg6ICIxNDAiCiAgICAgICAgfSwgewogICAgICAgICAgcHJvcDogInRvd2VyTnVtYmVyIiwKICAgICAgICAgIGxhYmVsOiAi5p2G5aGU57yW5Y+3IiwKICAgICAgICAgIG1pbldpZHRoOiAiMTIwIgogICAgICAgIH0sIHsKICAgICAgICAgIHByb3A6ICJsb25naXR1ZGUiLAogICAgICAgICAgbGFiZWw6ICLmoKHlh4bnu4/luqYiLAogICAgICAgICAgbWluV2lkdGg6ICIxMjAiCiAgICAgICAgfSwgewogICAgICAgICAgcHJvcDogImxhdGl0dWRlIiwKICAgICAgICAgIGxhYmVsOiAi5qCh5YeG57u05bqmIiwKICAgICAgICAgIG1pbldpZHRoOiAiMTIwIgogICAgICAgIH0sIHsKICAgICAgICAgIHByb3A6ICJkaXN0YW5jZSIsCiAgICAgICAgICBsYWJlbDogIuS4juWOn+WdkOagh+i3neemuyIsCiAgICAgICAgICBtaW5XaWR0aDogIjE0MCIKICAgICAgICB9LCB7CiAgICAgICAgICBwcm9wOiAiY3JlYXRlVXNlck5hbWUiLAogICAgICAgICAgbGFiZWw6ICLmj5DkuqTkuroiLAogICAgICAgICAgbWluV2lkdGg6ICIxMjAiCiAgICAgICAgfSwgewogICAgICAgICAgcHJvcDogImNyZWF0ZVRpbWUiLAogICAgICAgICAgbGFiZWw6ICLmj5DkuqTml7bpl7QiLAogICAgICAgICAgbWluV2lkdGg6ICIxNDAiCiAgICAgICAgfSAvLyB7CiAgICAgICAgLy8gICBmaXhlZDogInJpZ2h0IiwKICAgICAgICAvLyAgIHByb3A6ICdvcGVyYXRpb24nLAogICAgICAgIC8vICAgbGFiZWw6ICfmk43kvZwnLAogICAgICAgIC8vICAgbWluV2lkdGg6ICcxMzBweCcsCiAgICAgICAgLy8gICBzdHlsZToge2Rpc3BsYXk6ICdibG9jayd9LAogICAgICAgIC8vICAgb3BlcmF0aW9uOiBbCiAgICAgICAgLy8gICAgIHtuYW1lOiAn5a6h5qC4JywgY2xpY2tGdW46IHRoaXMuZXhhbWluZUluZm8gLGhpZGRlbjp0cnVlfSwKICAgICAgICAvLyAgICAge25hbWU6ICfor6bmg4UnLCBjbGlja0Z1bjogdGhpcy5kZXRhaWxzSW5mb30KICAgICAgICAvLyAgIF0KICAgICAgICAvLyB9LAogICAgICAgIF0KICAgICAgfSwKICAgICAgLy/ova7mkq3lm77niYcKICAgICAgaW1nTGlzdDogW10sCiAgICAgIC8v6K6+5aSH6K+m5oOF6aG15bqV6YOo56Gu6K6k5Y+W5raI5oyJ6ZKu5o6n5Yi2CiAgICAgIHNiQ29tbWl0RGlhbG9nQ290cm9sOiB0cnVlLAogICAgICAvL+W8ueWHuuahhnRhYumhtQogICAgICBhY3RpdmVUYWJOYW1lOiAic2JEZXNjIiwKICAgICAgLy/lj5jnlLXnq5nlsZXnpLoKICAgICAgYmR6U2hvd1RhYmxlOiB0cnVlLAogICAgICAvL+mXtOmalOWxleekugogICAgICBqZ1Nob3dUYWJsZTogZmFsc2UsCiAgICAgIC8v6K6+5aSH5bGV56S6CiAgICAgIHNiU2hvd1RhYmxlOiBmYWxzZSwKICAgICAgZGlhbG9nRm9ybVZpc2libGU6IGZhbHNlLAogICAgICBzaG93RXhhbWluZTogZmFsc2UsCiAgICAgIC8v5by55Ye65qGG6KGo5Y2VCiAgICAgIGZvcm06IHt9LAogICAgICBzZWxlY3RSb3dzOiBbXSwKICAgICAgaWRzOiBbXSwKICAgICAgLy/liKDpmaTmmK/lkKblj6/nlKgKICAgICAgbXVsdGlwbGVTZW5zb3I6IHRydWUsCiAgICAgIC8v5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAKICAgICAgfSwKICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwKICAgICAgdGl0bGU6ICIiLAogICAgICBydWxlczogewogICAgICAgIHN0YXRlOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6K+36YCJ5oup57uT5p6cIiwKICAgICAgICAgIHRyaWdnZXI6ICJjaGFuZ2UiCiAgICAgICAgfV0KICAgICAgfQogICAgfTsKICB9LAogIHdhdGNoOiB7fSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdGhpcy5nZXREYXRhKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBnZXRGaWxlTGlzdDogZnVuY3Rpb24gZ2V0RmlsZUxpc3QoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CgogICAgICByZXR1cm4gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSggLyojX19QVVJFX18qL3JlZ2VuZXJhdG9yUnVudGltZS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUoKSB7CiAgICAgICAgdmFyIF95aWVsZCRnZXRMaXN0QnlCdXNpbiwgY29kZSwgZGF0YTsKCiAgICAgICAgcmV0dXJuIHJlZ2VuZXJhdG9yUnVudGltZS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUkKF9jb250ZXh0KSB7CiAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0LnByZXYgPSBfY29udGV4dC5uZXh0KSB7CiAgICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgICAgX2NvbnRleHQubmV4dCA9IDI7CiAgICAgICAgICAgICAgICByZXR1cm4gKDAsIF9maWxlLmdldExpc3RCeUJ1c2luZXNzSWQpKHsKICAgICAgICAgICAgICAgICAgYnVzaW5lc3NJZDogX3RoaXMuZm9ybS5pZAogICAgICAgICAgICAgICAgfSk7CgogICAgICAgICAgICAgIGNhc2UgMjoKICAgICAgICAgICAgICAgIF95aWVsZCRnZXRMaXN0QnlCdXNpbiA9IF9jb250ZXh0LnNlbnQ7CiAgICAgICAgICAgICAgICBjb2RlID0gX3lpZWxkJGdldExpc3RCeUJ1c2luLmNvZGU7CiAgICAgICAgICAgICAgICBkYXRhID0gX3lpZWxkJGdldExpc3RCeUJ1c2luLmRhdGE7CgogICAgICAgICAgICAgICAgaWYgKGNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgICAgICBfdGhpcy5mb3JtLmF0dGFjaG1lbnQgPSBkYXRhOwogICAgICAgICAgICAgICAgICBfdGhpcy5pbWdMaXN0ID0gZGF0YS5tYXAoZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgICAgICAgICB2YXIgaXRlbTEgPSB7fTsKICAgICAgICAgICAgICAgICAgICBpdGVtMS5uYW1lID0gaXRlbS5maWxlTmFtZTsKICAgICAgICAgICAgICAgICAgICBpdGVtMS51cmwgPSBfdGhpcy4kc3RvcmUuZ2V0dGVycy5jdXJySG9zdCArIGl0ZW0uZmlsZVVybDsKICAgICAgICAgICAgICAgICAgICByZXR1cm4gaXRlbTE7CiAgICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgfQoKICAgICAgICAgICAgICBjYXNlIDY6CiAgICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICAgIHJldHVybiBfY29udGV4dC5zdG9wKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlKTsKICAgICAgfSkpKCk7CiAgICB9LAogICAgZ2V0RGF0YTogZnVuY3Rpb24gZ2V0RGF0YShwYXJhbXMpIHsKICAgICAgdmFyIF90aGlzMiA9IHRoaXM7CgogICAgICB0aGlzLnF1ZXJ5UGFyYW1zID0gKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKCgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7fSwgdGhpcy5xdWVyeVBhcmFtcyksIHBhcmFtcyk7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgIHZhciBwYXJhbSA9ICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSgoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIHRoaXMucXVlcnlQYXJhbXMpLCBwYXJhbXMpOwogICAgICAoMCwgX3Bvd2VyY2hlY2suZ2V0TGlzdCkocGFyYW0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIF90aGlzMi50YWJsZUFuZFBhZ2VJbmZvLnRhYmxlRGF0YSA9IHJlcy5kYXRhLnJlY29yZHM7CiAgICAgICAgX3RoaXMyLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIudG90YWwgPSByZXMuZGF0YS50b3RhbDsKICAgICAgICBfdGhpczIubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCgogICAgLyoqCiAgICAgKiDooajmoLzlpJrpgInmoYYKICAgICAqLwogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLmlkOwogICAgICB9KTsKICAgICAgdGhpcy5zZWxlY3RSb3dzID0gc2VsZWN0aW9uOyAvLyB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDE7CiAgICAgIC8vIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsKICAgIH0sCiAgICBkZXRhaWxzSW5mbzogZnVuY3Rpb24gKCkgewogICAgICB2YXIgX2RldGFpbHNJbmZvID0gKDAsIF9hc3luY1RvR2VuZXJhdG9yMi5kZWZhdWx0KSggLyojX19QVVJFX18qL3JlZ2VuZXJhdG9yUnVudGltZS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUyKHJvdykgewogICAgICAgIHJldHVybiByZWdlbmVyYXRvclJ1bnRpbWUud3JhcChmdW5jdGlvbiBfY2FsbGVlMiQoX2NvbnRleHQyKSB7CiAgICAgICAgICB3aGlsZSAoMSkgewogICAgICAgICAgICBzd2l0Y2ggKF9jb250ZXh0Mi5wcmV2ID0gX2NvbnRleHQyLm5leHQpIHsKICAgICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgICB0aGlzLmZvcm0gPSAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIHJvdyk7CiAgICAgICAgICAgICAgICBfY29udGV4dDIubmV4dCA9IDM7CiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5nZXRGaWxlTGlzdCgpOwoKICAgICAgICAgICAgICBjYXNlIDM6CiAgICAgICAgICAgICAgICB0aGlzLnNob3cgPSB0cnVlOwogICAgICAgICAgICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IHRydWU7CgogICAgICAgICAgICAgIGNhc2UgNToKICAgICAgICAgICAgICBjYXNlICJlbmQiOgogICAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Mi5zdG9wKCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LCBfY2FsbGVlMiwgdGhpcyk7CiAgICAgIH0pKTsKCiAgICAgIGZ1bmN0aW9uIGRldGFpbHNJbmZvKF94KSB7CiAgICAgICAgcmV0dXJuIF9kZXRhaWxzSW5mby5hcHBseSh0aGlzLCBhcmd1bWVudHMpOwogICAgICB9CgogICAgICByZXR1cm4gZGV0YWlsc0luZm87CiAgICB9KCksCiAgICBleGFtaW5lSW5mbzogZnVuY3Rpb24gZXhhbWluZUluZm8ocm93KSB7CiAgICAgIHRoaXMuZm9ybSA9ICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7fSwgcm93KTsKICAgICAgdGhpcy5mb3JtLnN0YXRlID0gIiI7CiAgICAgIHRoaXMuc2hvd0V4YW1pbmUgPSB0cnVlOwogICAgfSwKICAgIHN1Ym1pdEluZm86IGZ1bmN0aW9uIHN1Ym1pdEluZm8oKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwoKICAgICAgdGhpcy4kcmVmc1siZm9ybTIiXS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgICgwLCBfcG93ZXJjaGVjay5leGFtaW5lKShfdGhpczMuZm9ybSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgICAgX3RoaXMzLiRtZXNzYWdlLnN1Y2Nlc3MoIuWuoeaguOaIkOWKn++8gSIpOwoKICAgICAgICAgICAgICBfdGhpczMuZ2V0RGF0YSgpOwoKICAgICAgICAgICAgICBfdGhpczMuc2hvd0V4YW1pbmUgPSBmYWxzZTsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICBfdGhpczMuJG1lc3NhZ2UoewogICAgICAgICAgICAgICAgdHlwZTogImVycm9yIiwKICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLmk43kvZzlpLHotKUhIgogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIC8v5Yig6ZmkCiAgICBkZWxldGVJbmZvczogZnVuY3Rpb24gZGVsZXRlSW5mb3MoaWQpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CgogICAgICBpZiAoKDAsIF90eXBlb2YyLmRlZmF1bHQpKGlkKSA9PT0gIm9iamVjdCIpIHsKICAgICAgICBpZiAodGhpcy5pZHMubGVuZ3RoIDwgMSkgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7flnKjlt6bkvqfli77pgInopoHliKDpmaTnmoTmlbDmja7vvIEiKTsKICAgICAgICAgIHJldHVybjsKICAgICAgICB9CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5pZHMucHVzaChpZCk7CiAgICAgIH0KCiAgICAgIHRoaXMuJGNvbmZpcm0oIuatpOaTjeS9nOWwhuawuOS5heWIoOmZpOivpeaVsOaNriwg5piv5ZCm57un57utPyIsICLmj5DnpLoiLCB7CiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLAogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLAogICAgICAgIHR5cGU6ICJ3YXJuaW5nIgogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICAoMCwgX3Bvd2VyY2hlY2sucmVtb3ZlQmF0Y2gpKF90aGlzNC5pZHMpLnRoZW4oZnVuY3Rpb24gKF9yZWYpIHsKICAgICAgICAgIHZhciBjb2RlID0gX3JlZi5jb2RlOwoKICAgICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgICAgX3RoaXM0LiRtZXNzYWdlKHsKICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsCiAgICAgICAgICAgICAgbWVzc2FnZTogIuWIoOmZpOaIkOWKnyEiCiAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgX3RoaXM0LmdldERhdGEoKTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzNC4kbWVzc2FnZSh7CiAgICAgICAgICB0eXBlOiAiaW5mbyIsCiAgICAgICAgICBtZXNzYWdlOiAi5bey5Y+W5raI5Yig6ZmkIgogICAgICAgIH0pOwogICAgICB9KTsKICAgIH0sCiAgICBmaWx0ZXJSZXNldDogZnVuY3Rpb24gZmlsdGVyUmVzZXQoKSB7CiAgICAgIHRoaXMuZmlsdGVySW5mby5maWVsZExpc3QuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIGlmIChpdGVtLnR5cGUgPT09ICJjaGVja2JveCIpIHsKICAgICAgICAgIGl0ZW0uY2hlY2tib3hWYWx1ZSA9IFtdOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgcmVzZXRGb3JtOiBmdW5jdGlvbiByZXNldEZvcm0oKSB7CiAgICAgIHRoaXMuZm9ybSA9IHt9OwogICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gZmFsc2U7CiAgICB9CiAgfQp9OwpleHBvcnRzLmRlZmF1bHQgPSBfZGVmYXVsdDs="}, {"version": 3, "sources": ["powercheck.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAiPA;;AAMA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,OAAA,EAAA,KADA;AAEA,MAAA,SAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,OAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,CAFA;AAMA,MAAA,QAAA,EAAA,CACA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,KAAA,EAAA,MAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,CANA;AAUA,MAAA,IAAA,EAAA,KAVA;AAWA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,SAAA,EAAA,EADA;AAEA,UAAA,WAAA,EAAA,EAFA;AAGA,UAAA,SAAA,EAAA,EAHA;AAIA,UAAA,QAAA,EAAA,EAJA;AAKA,UAAA,QAAA,EAAA,EALA;AAMA,UAAA,cAAA,EAAA,EANA;AAOA,UAAA,OAAA,EAAA,EAPA;AAQA,UAAA,IAAA,EAAA,EARA;AASA,UAAA,OAAA,EAAA;AATA,SADA;AAYA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SANA,EAOA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,MAFA;AAGA,UAAA,KAAA,EAAA,SAHA;AAIA,UAAA,QAAA,EAAA;AAJA,SAPA,EAaA;AACA,UAAA,KAAA,EAAA,MADA;AAEA,UAAA,IAAA,EAAA,QAFA;AAGA,UAAA,KAAA,EAAA,MAHA;AAIA,UAAA,OAAA,EAAA,CACA;AAAA,YAAA,KAAA,EAAA,MAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WADA,EAEA;AAAA,YAAA,KAAA,EAAA,MAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAFA,CAJA;AAQA,UAAA,SAAA,EAAA;AARA,SAbA,EAuBA;AACA,UAAA,KAAA,EAAA,IADA;AAEA,UAAA,IAAA,EAAA,UAFA;AAGA,UAAA,aAAA,EAAA,EAHA;AAIA,UAAA,KAAA,EAAA,SAJA;AAKA,UAAA,OAAA,EAAA,CACA;AAAA,YAAA,KAAA,EAAA,KAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WADA,EAEA;AAAA,YAAA,KAAA,EAAA,MAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAFA,EAGA;AAAA,YAAA,KAAA,EAAA,OAAA;AAAA,YAAA,KAAA,EAAA;AAAA,WAHA,CALA;AAUA,UAAA,SAAA,EAAA;AAVA,SAvBA;AAZA,OAXA;AA4DA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,WAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,aAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,WAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,UAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,gBAAA;AAAA,UAAA,KAAA,EAAA,KAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,EAOA;AAAA,UAAA,IAAA,EAAA,YAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAPA,CAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAlBA;AAZA,OA5DA;AA6FA;AACA,MAAA,OAAA,EAAA,EA9FA;AA+FA;AACA,MAAA,oBAAA,EAAA,IAhGA;AAiGA;AACA,MAAA,aAAA,EAAA,QAlGA;AAmGA;AACA,MAAA,YAAA,EAAA,IApGA;AAqGA;AACA,MAAA,WAAA,EAAA,KAtGA;AAuGA;AACA,MAAA,WAAA,EAAA,KAxGA;AAyGA,MAAA,iBAAA,EAAA,KAzGA;AA0GA,MAAA,WAAA,EAAA,KA1GA;AA2GA;AACA,MAAA,IAAA,EAAA,EA5GA;AA6GA,MAAA,UAAA,EAAA,EA7GA;AA8GA,MAAA,GAAA,EAAA,EA9GA;AA+GA;AACA,MAAA,cAAA,EAAA,IAhHA;AAiHA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA;AAFA,OAlHA;AAsHA,MAAA,UAAA,EAAA,IAtHA;AAuHA,MAAA,KAAA,EAAA,EAvHA;AAwHA,MAAA,KAAA,EAAA;AACA,QAAA,KAAA,EAAA,CAAA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,OAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAAA;AADA;AAxHA,KAAA;AA4HA,GA/HA;AAgIA,EAAA,KAAA,EAAA,EAhIA;AAiIA,EAAA,OAjIA,qBAiIA;AACA,SAAA,OAAA;AACA,GAnIA;AAoIA,EAAA,OAAA,EAAA;AACA,IAAA,WADA,yBACA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,+BAAA;AACA,kBAAA,UAAA,EAAA,KAAA,CAAA,IAAA,CAAA;AADA,iBAAA,CADA;;AAAA;AAAA;AACA,gBAAA,IADA,yBACA,IADA;AACA,gBAAA,IADA,yBACA,IADA;;AAIA,oBAAA,IAAA,KAAA,MAAA,EAAA;AACA,kBAAA,KAAA,CAAA,IAAA,CAAA,UAAA,GAAA,IAAA;AACA,kBAAA,KAAA,CAAA,OAAA,GAAA,IAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,wBAAA,KAAA,GAAA,EAAA;AACA,oBAAA,KAAA,CAAA,IAAA,GAAA,IAAA,CAAA,QAAA;AACA,oBAAA,KAAA,CAAA,GAAA,GAAA,KAAA,CAAA,MAAA,CAAA,OAAA,CAAA,QAAA,GAAA,IAAA,CAAA,OAAA;AACA,2BAAA,KAAA;AACA,mBALA,CAAA;AAMA;;AAZA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA,KAdA;AAeA,IAAA,OAAA,EAAA,iBAAA,MAAA,EAAA;AAAA;;AACA,WAAA,WAAA,+DAAA,KAAA,WAAA,GAAA,MAAA;AACA,WAAA,OAAA,GAAA,IAAA;AACA,UAAA,KAAA,+DAAA,KAAA,WAAA,GAAA,MAAA,CAAA;AACA,+BAAA,KAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,MAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,MAAA,CAAA,OAAA,GAAA,KAAA;AACA,OAJA;AAKA,KAxBA;;AA0BA;;;AAGA,IAAA,qBA7BA,iCA6BA,SA7BA,EA6BA;AACA,WAAA,GAAA,GAAA,SAAA,CAAA,GAAA,CAAA,UAAA,IAAA;AAAA,eAAA,IAAA,CAAA,EAAA;AAAA,OAAA,CAAA;AACA,WAAA,UAAA,GAAA,SAAA,CAFA,CAGA;AACA;AACA,KAlCA;AAoCA,IAAA,WAAA;AAAA,+FAAA,kBAAA,GAAA;AAAA;AAAA;AAAA;AAAA;AACA,qBAAA,IAAA,mCAAA,GAAA;AADA;AAAA,uBAEA,KAAA,WAAA,EAFA;;AAAA;AAGA,qBAAA,IAAA,GAAA,IAAA;AACA,qBAAA,iBAAA,GAAA,IAAA;;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA,OApCA;AA2CA,IAAA,WAAA,EAAA,qBAAA,GAAA,EAAA;AACA,WAAA,IAAA,mCAAA,GAAA;AACA,WAAA,IAAA,CAAA,KAAA,GAAA,EAAA;AACA,WAAA,WAAA,GAAA,IAAA;AACA,KA/CA;AAgDA,IAAA,UAAA,EAAA,sBAAA;AAAA;;AACA,WAAA,KAAA,CAAA,OAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,YAAA,KAAA,EAAA;AACA,mCAAA,MAAA,CAAA,IAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,OAAA;;AACA,cAAA,MAAA,CAAA,OAAA;;AACA,cAAA,MAAA,CAAA,WAAA,GAAA,KAAA;AACA,aAJA,MAIA;AACA,cAAA,MAAA,CAAA,QAAA,CAAA;AACA,gBAAA,IAAA,EAAA,OADA;AAEA,gBAAA,OAAA,EAAA;AAFA,eAAA;AAIA;AACA,WAXA;AAYA;AACA,OAfA;AAgBA,KAjEA;AAkEA;AACA,IAAA,WAnEA,uBAmEA,EAnEA,EAmEA;AAAA;;AACA,UAAA,sBAAA,EAAA,MAAA,QAAA,EAAA;AACA,YAAA,KAAA,GAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,eAAA,QAAA,CAAA,OAAA,CAAA,eAAA;AACA;AACA;AACA,OALA,MAKA;AACA,aAAA,GAAA,CAAA,IAAA,CAAA,EAAA;AACA;;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAKA,IALA,CAKA,YAAA;AACA,qCAAA,MAAA,CAAA,GAAA,EAAA,IAAA,CAAA,gBAAA;AAAA,cAAA,IAAA,QAAA,IAAA;;AACA,cAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,OAAA;AACA;AACA,SARA;AASA,OAfA,EAgBA,KAhBA,CAgBA,YAAA;AACA,QAAA,MAAA,CAAA,QAAA,CAAA;AACA,UAAA,IAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA;AAFA,SAAA;AAIA,OArBA;AAsBA,KAlGA;AAmGA,IAAA,WAnGA,yBAmGA;AACA,WAAA,UAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,IAAA,CAAA,IAAA,KAAA,UAAA,EAAA;AACA,UAAA,IAAA,CAAA,aAAA,GAAA,EAAA;AACA;AACA,OAJA;AAKA,KAzGA;AA0GA,IAAA,SA1GA,uBA0GA;AACA,WAAA,IAAA,GAAA,EAAA;AACA,WAAA,iBAAA,GAAA,KAAA;AACA;AA7GA;AApIA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      ref=\"filter1\"\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 100, itemWidth: 200 }\"\n      @handleReset=\"filterReset\"\n    />\n    <el-white class=\"button-group\">\n      <div class=\"button_btn\">\n        <!--        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"sbAddSensorButton\">新增</el-button>-->\n        <el-button type=\"danger\" icon=\"el-icon-delete\" v-hasPermi=\"['towerCheck:button:delete']\" @click=\"deleteInfos\"\n          >删除</el-button\n        >\n      </div>\n      <comp-table\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"handleSelectionChange\"\n        v-loading=\"loading\"\n        height=\"69vh\"\n      >\n        <el-table-column\n          slot=\"table_six\"\n          align=\"center\"\n          style=\"display: block;height: auto\"\n          label=\"校验类型\"\n          min-width=\"180\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            {{ scope.row.type === 0 ? \"自动校验\" : \"人工修正\" }}\n          </template>\n        </el-table-column>\n        <el-table-column\n          slot=\"table_seven\"\n          align=\"center\"\n          style=\"display: block;height: auto\"\n          label=\"状态\"\n          min-width=\"180\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-badge v-if=\"scope.row.state === 0\" value=\"未审核\" />\n            <el-badge v-if=\"scope.row.state === 1\" value=\"审核通过\" />\n            <el-badge v-if=\"scope.row.state === 2\" value=\"审核未通过\" />\n          </template>\n        </el-table-column>\n        <el-table-column\n          slot=\"table_eight\"\n          align=\"center\"\n          fixed=\"right\"\n          style=\"display: block\"\n          label=\"操作\"\n          width=\"180\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              v-if=\"scope.row.state === 0\"\n              size=\"mini\"\n              type=\"text\"\n              @click=\"examineInfo(scope.row)\"\n              title=\"审核\"\n              class=\"el-icon-s-claim\"\n            ></el-button>\n            <el-button\n              size=\"mini\"\n              type=\"text\"\n              @click=\"detailsInfo(scope.row)\"\n              title=\"详情\"\n              class=\"el-icon-view\"\n            ></el-button>\n          </template>\n        </el-table-column>\n      </comp-table>\n    </el-white>\n\n    <el-dialog\n      title=\"详情\"\n      :visible.sync=\"dialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"60%\"\n      @close=\"resetForm\"\n      v-dialogDrag\n    >\n      <el-form :model=\"form\" ref=\"form\" :disabled=\"show\" label-width=\"130px\">\n        <div\n          class=\"block\"\n          style=\"width: 50%;height: 50%;margin-bottom: 2%;float: right\"\n        >\n          <span class=\"demonstration\">图片</span>\n          <el-carousel\n            trigger=\"click\"\n            class=\"imgCls\"\n            indicator-position=\"none\"\n            :interval=\"2000\"\n            type=\"card\"\n            id=\"gtjy_imgId\"\n          >\n            <el-carousel-item v-for=\"(img, index) in imgList\" :key=\"index\">\n              <viewer :images=\"imgList\" style=\"z-index: 999\">\n                <img :src=\"img.url\" width=\"100%\" height=\"100%\" />\n              </viewer>\n            </el-carousel-item>\n          </el-carousel>\n        </div>\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"杆塔名称\" prop=\"towerName\">\n              <el-input\n                v-model=\"form.towerName\"\n                placeholder=\"请输入线路名称\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"杆塔编号\" prop=\"towerNumber\">\n              <el-input\n                v-model=\"form.towerNumber\"\n                placeholder=\"请输入线路名称\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"校准经度\" prop=\"longitude\">\n              <el-input\n                v-model=\"form.longitude\"\n                placeholder=\"请输入线路名称\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"校准维度\" prop=\"latitude\">\n              <el-input\n                v-model=\"form.latitude\"\n                placeholder=\"请输入线路名称\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"与原坐标距离\" prop=\"distance\">\n              <el-input\n                v-model=\"form.distance\"\n                placeholder=\"请输入线路名称\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"提交人\" prop=\"createUserName\">\n              <el-input\n                v-model=\"form.createUserName\"\n                placeholder=\"请输入线路名称\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item\n              label=\"提交时间\"\n              class=\"add_sy_tyrq\"\n              prop=\"createTime\"\n            >\n              <el-date-picker\n                v-model=\"form.createTime\"\n                type=\"datetime\"\n                placeholder=\"提交时间\"\n                format=\"yyyy-MM-dd HH:mm:ss\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"校验类型\" prop=\"type\">\n              <el-select\n                v-model=\"form.type\"\n                placeholder=\"请选择线路类型\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in typeList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"resetForm\">取 消</el-button>\n      </div>\n    </el-dialog>\n\n    <el-dialog\n      title=\"审核\"\n      :visible.sync=\"showExamine\"\n      class=\"qxlr_dialog_insert\"\n      width=\"60%\"\n    >\n      <el-form :model=\"form\" ref=\"form2\" :rules=\"rules\" label-width=\"130px\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"审核结果\" prop=\"state\">\n              <el-select\n                v-model=\"form.state\"\n                placeholder=\"请选择审核结果\"\n                clearable\n              >\n                <el-option\n                  v-for=\"item in stateList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"20\">\n          <el-form-item label=\"备注\" prop=\"remark\">\n            <el-input\n              type=\"textarea\"\n              :rows=\"3\"\n              v-model=\"form.remark\"\n            ></el-input>\n          </el-form-item>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"showExamine = false\">取 消</el-button>\n        <el-button type=\"primary\" @click=\"submitInfo\">确认</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  examine,\n  getList,\n  saveOrUpdate,\n  removeBatch\n} from \"@/api/dagangOilfield/asset/powercheck\";\nimport { getListByBusinessId } from \"@/api/tool/file\";\nexport default {\n  name: \"qxbzk\",\n  data() {\n    return {\n      loading: false,\n      stateList: [\n        { label: \"审核通过\", value: \"1\" },\n        { label: \"审核未通过\", value: \"2\" }\n      ],\n      typeList: [\n        { label: \"自动校验\", value: 0 },\n        { label: \"人工修正\", value: 1 }\n      ],\n      show: false,\n      filterInfo: {\n        data: {\n          towerName: \"\",\n          towerNumber: \"\",\n          longitude: \"\",\n          latitude: \"\",\n          distance: \"\",\n          createUserName: \"\",\n          tyrqArr: [],\n          type: \"\",\n          stateCn: \"\"\n        },\n        fieldList: [\n          { label: \"塔杆名称\", type: \"input\", value: \"towerName\" },\n          { label: \"杆塔编号\", type: \"input\", value: \"towerNumber\" },\n          { label: \"校准经度\", type: \"input\", value: \"longitude\" },\n          { label: \"校准纬度\", type: \"input\", value: \"latitude\" },\n          { label: \"与原坐标距离\", type: \"input\", value: \"distance\" },\n          { label: \"提交人\", type: \"input\", value: \"createUserName\" },\n          {\n            label: \"提交时间\",\n            type: \"date\",\n            value: \"tyrqArr\",\n            dateType: \"daterange\"\n          },\n          {\n            label: \"校验类型\",\n            type: \"select\",\n            value: \"type\",\n            options: [\n              { label: \"自动校验\", value: \"0\" },\n              { label: \"人工修正\", value: \"1\" }\n            ],\n            clearable: true\n          },\n          {\n            label: \"状态\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"stateCn\",\n            options: [\n              { label: \"未审核\", value: \"0\" },\n              { label: \"审核通过\", value: \"1\" },\n              { label: \"审核未通过\", value: \"2\" }\n            ],\n            clearable: true\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"towerName\", label: \"杆塔名称\", minWidth: \"140\" },\n          { prop: \"towerNumber\", label: \"杆塔编号\", minWidth: \"120\" },\n          { prop: \"longitude\", label: \"校准经度\", minWidth: \"120\" },\n          { prop: \"latitude\", label: \"校准维度\", minWidth: \"120\" },\n          { prop: \"distance\", label: \"与原坐标距离\", minWidth: \"140\" },\n          { prop: \"createUserName\", label: \"提交人\", minWidth: \"120\" },\n          { prop: \"createTime\", label: \"提交时间\", minWidth: \"140\" }\n          // {\n          //   fixed: \"right\",\n          //   prop: 'operation',\n          //   label: '操作',\n          //   minWidth: '130px',\n          //   style: {display: 'block'},\n          //   operation: [\n          //     {name: '审核', clickFun: this.examineInfo ,hidden:true},\n          //     {name: '详情', clickFun: this.detailsInfo}\n          //   ]\n          // },\n        ]\n      },\n      //轮播图片\n      imgList: [],\n      //设备详情页底部确认取消按钮控制\n      sbCommitDialogCotrol: true,\n      //弹出框tab页\n      activeTabName: \"sbDesc\",\n      //变电站展示\n      bdzShowTable: true,\n      //间隔展示\n      jgShowTable: false,\n      //设备展示\n      sbShowTable: false,\n      dialogFormVisible: false,\n      showExamine: false,\n      //弹出框表单\n      form: {},\n      selectRows: [],\n      ids: [],\n      //删除是否可用\n      multipleSensor: true,\n      //查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      showSearch: true,\n      title: \"\",\n      rules: {\n        state: [{ required: true, message: \"请选择结果\", trigger: \"change\" }]\n      }\n    };\n  },\n  watch: {},\n  created() {\n    this.getData();\n  },\n  methods: {\n    async getFileList() {\n      let { code, data } = await getListByBusinessId({\n        businessId: this.form.id\n      });\n      if (code === \"0000\") {\n        this.form.attachment = data;\n        this.imgList = data.map(item => {\n          let item1 = {};\n          item1.name = item.fileName;\n          item1.url = this.$store.getters.currHost + item.fileUrl;\n          return item1;\n        });\n      }\n    },\n    getData: function(params) {\n      this.queryParams = { ...this.queryParams, ...params };\n      this.loading = true;\n      const param = { ...this.queryParams, ...params };\n      getList(param).then(res => {\n        this.tableAndPageInfo.tableData = res.data.records;\n        this.tableAndPageInfo.pager.total = res.data.total;\n        this.loading = false;\n      });\n    },\n\n    /**\n     * 表格多选框\n     */\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id);\n      this.selectRows = selection\n      // this.single = selection.length !== 1;\n      // this.multiple = !selection.length;\n    },\n\n    detailsInfo: async function(row) {\n      this.form = { ...row };\n      await this.getFileList();\n      this.show = true;\n      this.dialogFormVisible = true;\n    },\n\n    examineInfo: function(row) {\n      this.form = { ...row };\n      this.form.state = \"\";\n      this.showExamine = true;\n    },\n    submitInfo: function() {\n      this.$refs[\"form2\"].validate(valid => {\n        if (valid) {\n          examine(this.form).then(res => {\n            if (res.code === \"0000\") {\n              this.$message.success(\"审核成功！\");\n              this.getData();\n              this.showExamine = false;\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"操作失败!\"\n              });\n            }\n          });\n        }\n      });\n    },\n    //删除\n    deleteInfos(id) {\n      if (typeof id === \"object\") {\n        if (this.ids.length < 1) {\n          this.$message.warning(\"请在左侧勾选要删除的数据！\");\n          return;\n        }\n      } else {\n        this.ids.push(id);\n      }\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          removeBatch(this.ids).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n              type: \"info\",\n              message: \"已取消删除\"\n            });\n        });\n    },\n    filterReset() {\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n    },\n    resetForm() {\n      this.form = {};\n      this.dialogFormVisible = false;\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.imgCls {\n  height: 150px !important;\n}\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  height: calc(100vh - 225px);\n  overflow: auto;\n}\n\n.box-card {\n  margin-bottom: 15px;\n\n  .el-card__header {\n    background-color: rgb(235, 245, 255) !important;\n  }\n}\n\n.box-cardList {\n  height: 70%;\n}\n\n.item {\n  width: 200px;\n  float: left;\n}\n\n#main_container_dj {\n  height: calc(100vh - 84px);\n}\n\n.aside_height {\n  height: 96%;\n}\n\n.defect .el-form-item:nth-child(odd) {\n  margin-right: 70px;\n}\n\n.el-carousel__item h3 {\n  color: #475669;\n  font-size: 14px;\n  opacity: 0.75;\n  line-height: 150px;\n}\n\n.el-carousel__item:nth-child(2n) {\n  background-color: #99a9bf;\n}\n\n.el-carousel__item:nth-child(2n + 1) {\n  background-color: #d3dce6;\n}\n\n/*弹出框内宽度设置*/\n/*/deep/ .el-input--medium .el-input__inner {*/\n/*  width: 200px;*/\n/*}*/\n.el-select {\n  width: 100%;\n}\n\n.el-date-editor {\n  width: 100%;\n}\n\n.divHeader {\n  width: 100%;\n  height: 30px;\n  background-color: #dddddd;\n  margin-bottom: 10px;\n  text-align: left;\n  line-height: 30px;\n  font-weight: bold;\n}\n.el-carousel__item h3 {\n  color: #475669;\n  font-size: 14px;\n  opacity: 0.75;\n  line-height: 150px;\n}\n\n.el-carousel__item:nth-child(2n) {\n  background-color: #99a9bf;\n}\n\n.el-carousel__item:nth-child(2n + 1) {\n  background-color: #d3dce6;\n}\n</style>\n<style>\n#gtjy_imgId .el-carousel__container {\n  background-color: #ffffff !important;\n}\n</style>\n"], "sourceRoot": "src/views/dagangOilfield/dwzygl/sdsbgl"}]}