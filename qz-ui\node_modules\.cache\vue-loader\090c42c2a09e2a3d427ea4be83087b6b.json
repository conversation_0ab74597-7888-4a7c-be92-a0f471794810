{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\equipmentComponents.vue?vue&type=style&index=0&id=576bff18&scoped=true&lang=css&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbbzk\\equipmentComponents.vue", "mtime": 1706897322893}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLnBhZ2luYXRpb24tY29udGFpbmVyIHsKICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgaGVpZ2h0OiAyNXB4OwogIG1hcmdpbi1ib3R0b206IDIwcHggIWltcG9ydGFudDsKICBtYXJnaW4tdG9wOiAxNXB4OwogIHBhZGRpbmc6IDEwcHggMjBweCAhaW1wb3J0YW50Owp9Cg=="}, {"version": 3, "sources": ["equipmentComponents.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkQA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "equipmentComponents.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbbzk", "sourcesContent": ["<template>\n  <div>\n    <el-white>\n      <el-white class=\"button-group\">\n        <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"addEquipmentComponents\">新增</el-button>\n        <el-button type=\"danger\" icon=\"el-icon-delete\" @click=\"deleteEquipmentComponents\">删除</el-button>\n      </el-white>\n      <el-table\n        stripe\n        border\n        v-loading=\"loading\"\n        :data=\"tableData\"\n        @selection-change=\"handleSelectionChange\">\n        <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n        <el-table-column label=\"部件编码\" align=\"center\" prop=\"bjbm\"/>\n        <el-table-column label=\"部件名称\" align=\"center\" prop=\"bjmc\"/>\n        <el-table-column label=\"描述\" align=\"center\" prop=\"ms\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"设备类型名称\" align=\"center\" prop=\"sblxmc\" :show-overflow-tooltip=\"true\"/>\n        <el-table-column label=\"操作\" fixed=\"right\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"180\">\n          <template slot-scope=\"scope\">\n            <el-button type=\"text\" @click=\"updateEquipmentComponents(scope.row)\">修改</el-button>\n            <el-button type=\"text\" @click=\"showDetail(scope.row)\">详情</el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n      <pagination\n        v-show=\"queryParams.total>0\"\n        :total=\"queryParams.total\"\n        :page.sync=\"queryParams.pageNum\"\n        :limit.sync=\"queryParams.pageSize\"\n        @pagination=\"getList\"/>\n    </el-white>\n\n    <dialog-form\n      ref=\"dialogForm\"\n      :append-to-body=\"true\"\n      label-width=\"150px\"\n      out-width=\"50%\"\n      :reminder=\"reminder\"\n      :rows=\"row\"\n      @save=\"saveEquipmentComponents\"\n    />\n  </div>\n</template>\n\n<script>\nimport DialogForm from 'com/dialogFrom/dialogForm'\nimport {\n  deleteEquipmentComponents,\n  getEquipmentComponents,\n  saveEquipmentComponents\n} from '@/api/dagangOilfield/bzgl/sblxwh/sbbj'\n\nexport default {\n  props: {\n    deviceTypeData: {\n      type: Object\n    }\n  },\n  name: 'equipmentComponents',\n  components: { DialogForm },\n  data() {\n    return {\n      reminder: '新增',\n      row: 2,\n      tableData: [],\n      loading: false,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        total: 0,\n        sblxbm: ''\n      },\n      formList: [\n        {\n          label: '部件编码：',\n          value: '',\n          type: 'input',\n          name: 'bjbm',\n          default: true,\n          rules: { required: true, message: '请输入部件编码' }\n        },\n        {\n          label: '部件名称：',\n          value: '',\n          type: 'input',\n          name: 'bjmc',\n          default: true,\n          rules: { required: true, message: '请输入部件名称' }\n        },\n        {\n          label: '单位：',\n          value: '',\n          type: 'input',\n          name: 'dw',\n          default: true,\n          rules: { required: false, message: '请输入单位' }\n        },\n        {\n          label: '设备类型名称：',\n          value: '',\n          type: 'disabled',\n          name: 'sblxmc',\n          default: true,\n          rules: { required: true, message: '请输入属性名称' }\n        },\n        {\n          label: '排序：',\n          value: '',\n          type: 'input',\n          name: 'px',\n          default: true,\n          rules: { required: false, message: '请输入排序' }\n        },\n        {\n          label: '描述：',\n          value: '',\n          type: 'textarea',\n          name: 'ms',\n          default: true,\n          rules: { required: false, message: '请输入描述' }\n        },\n        {\n          label: '备注：',\n          value: '',\n          type: 'textarea',\n          name: 'bz',\n          default: true,\n          rules: { required: false, message: '请输入备注' }\n        },\n\n        {\n          label: 'id：',\n          value: '',\n          type: 'input',\n          name: 'id',\n          default: true,\n          hidden: false,\n          rules: { required: false, message: '请输入id' }\n        },\n        {\n          label: '设备类型编码：',\n          value: '',\n          type: 'input',\n          name: 'sblxbm',\n          default: true,\n          hidden: false,\n          rules: { required: false, message: '请输入设备类型编码' }\n        }\n\n      ],\n      //选中行数据\n      selectedRowData: []\n    }\n  },\n  mounted() {\n    this.getList()\n  },\n  methods: {\n    //获取设备部件数据\n    getList() {\n      this.loading = false\n      console.log(\"---this.deviceTypeData--\"+this.deviceTypeData);\n      this.queryParams.sblxbm = this.deviceTypeData.sblxbm\n      getEquipmentComponents(this.queryParams).then(res => {\n        this.tableData = res.data.records\n        this.queryParams.total = res.data.total\n      })\n      this.loading = false\n    },\n    //新增设备部件\n    addEquipmentComponents() {\n      this.reminder = '新增'\n      this.formList = this.$options.data().formList\n      const addForm = this.formList.map(item => {\n        if (item.name === 'sblxbm') {\n          item.value = this.deviceTypeData.sblxbm\n        }\n        if (item.name === 'sblxmc') {\n          item.value = this.deviceTypeData.sblx\n        }\n        return item\n      })\n      this.$refs.dialogForm.showzzc(addForm)\n    },\n    //修改设备部件\n    updateEquipmentComponents(row) {\n      const updateList = this.formList.map(item => {\n        item.value = row[item.name]\n        return item\n      })\n      this.reminder = '修改'\n      this.$refs.dialogForm.showzzc(updateList)\n    },\n    //查看详情\n    showDetail(row) {\n      const infoList = this.formList.map(item => {\n        item.value = row[item.name]\n        return item\n      })\n      this.reminder = '详情'\n      this.$refs.dialogForm.showxq(infoList)\n    },\n    //批量删除设备部件\n    deleteEquipmentComponents() {\n      this.$confirm('确认删除选中数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n\n        let ids = []\n        this.selectedRowData.forEach(item => {\n          ids.push(item.id)\n        })\n\n        deleteEquipmentComponents(ids).then(res => {\n          if (res.code === '0000') {\n            this.$message.success('删除成功')\n            this.getList()\n          } else {\n            this.$message.error('操作失败')\n          }\n        })\n      }).catch(() => {\n        this.$message({\n          type: 'info',\n          message: '已取消删除'\n        })\n      })\n    },\n    //保存设备部件数据\n    saveEquipmentComponents(formData) {\n      let message = ''\n      if (formData.id === '' || !formData.id) {\n        message = '新增成功'\n      } else {\n        message = '修改成功'\n      }\n      saveEquipmentComponents(formData).then(res => {\n        if (res.code === '0000') {\n          this.$message.success(message)\n          this.getList()\n        } else {\n          this.$message.error('操作失败')\n        }\n      })\n    },\n    //行选中事件\n    handleSelectionChange(row) {\n      this.selectedRowData = row\n    }\n\n  }\n}\n</script>\n\n<style scoped>\n.pagination-container {\n  position: relative;\n  height: 25px;\n  margin-bottom: 20px !important;\n  margin-top: 15px;\n  padding: 10px 20px !important;\n}\n</style>\n"]}]}