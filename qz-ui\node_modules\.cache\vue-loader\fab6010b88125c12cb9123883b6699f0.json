{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\pddzcz\\components\\czp_tj.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\czpgl\\pddzcz\\components\\czp_tj.vue", "mtime": 1748605462598}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["czp_tj.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAsBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "czp_tj.vue", "sourceRoot": "src/views/dagangOilfield/czpgl/pddzcz/components", "sourcesContent": ["<template>\n  <div className=\"app-container\">\n    <el-filter\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      :width=\"{ labelWidth: 110, itemWidth: 220 }\"\n      @handleEvent=\"handleEvent\"\n    />\n\n    <el-white className=\"button-group\">\n      <div>\n        <comp-table\n          :table-and-page-info=\"tableAndPageInfo\"\n          @update:multipleSelection=\"selectChange\"\n          height=\"68vh\"\n        />\n      </div>\n    </el-white>\n  </div>\n</template>\n\n<script>\nimport { getListTj } from \"@/api/yxgl/bdyxgl/bddzczp\";\nimport { getPdsOptionsDataList } from \"@/api/dagangOilfield/asset/pdsgl\";\nexport default {\n  name: \"czp_tj\",\n  mounted() {\n    //列表查询\n    this.getData();\n    //获取配电室下拉框数据\n    this.getPdsOptionsDataList();\n  },\n  data() {\n    return {\n      // 查询变电站列表\n      pdsOptionsDataList: [],\n      //查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      // 查询数据总条数\n      total: 0,\n      filterInfo: {\n        data: {\n          bdzmc: \"\"\n        }, //查询条件\n        fieldList: [\n          {\n            label: \"配电站名称\",\n            value: \"bdzmc\",\n            type: \"select\",\n            clearable: true,\n            filterable: true,\n            options: []\n          }\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        tableData: [],\n        tableHeader: [\n          { label: \"配电站名称\", prop: \"pdzCn\", minWidth: \"150\" },\n          { label: \"操作任务\", prop: \"czrw\", minWidth: \"100\" },\n          { label: \"操作项数\", prop: \"czxs\", minWidth: \"100\" },\n          { label: \"已执行项数\", prop: \"yzxczxs\", minWidth: \"100\" },\n          { label: \"未执行项数\", prop: \"wzxczxs\", minWidth: \"100\" }\n        ],\n        option: { checkBox: true, serialNumber: true }\n      },\n      params: {\n        lx: 3,\n        status: \"4\"\n      }\n    };\n  },\n  methods: {\n    //统计\n    async getData(params) {\n      try {\n        const param = { ...this.params, ...params };\n        const { data, code } = await getListTj(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n        }\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    /**\n     * 列表选中\n     * */\n    selectChange(selection) {\n      this.ids = selection.map(item => item.objId);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    //获取配电室下拉框数据\n    getPdsOptionsDataList() {\n      getPdsOptionsDataList({}).then(res => {\n        this.pdsOptionsDataList = res.data;\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"bdzmc\") {\n            return (item.options = this.pdsOptionsDataList);\n          }\n        });\n      });\n    },\n    //下拉框change事件\n    handleEvent(val) {\n      //变电站查询下拉框选项根据所选分公司带出来\n      if (val.label === \"fgs\" && val.value && val.value !== \"\") {\n        let form = {\n          ssdwbm: val.value\n        };\n        getBdzSelectList(form).then(res => {\n          this.filterInfo.fieldList.map(item => {\n            if (item.value == \"bdzmc\") {\n              return (item.options = res.data);\n            }\n          });\n        });\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n/*列表颜色设置*/\n/deep/ .el-table th {\n  background-color: #e8f7f0;\n}\n</style>\n"]}]}