{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\gswh.vue?vue&type=style&index=1&lang=css&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\gswh.vue", "mtime": 1706897323215}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKI21weHFfcmlnaHQgdGQgewogIGJvcmRlcjogMXB4IHNvbGlkICMwMDA7CiAgaGVpZ2h0OiAzNXB4OwogIGxpbmUtaGVpZ2h0OiAzNXB4OwogIHRleHQtYWxpZ246IGNlbnRlcjsKfQojbXB4cV9yaWdodCB0ciB7CiAgaGVpZ2h0OiAzNXB4Owp9CiNtcHhxX3JpZ2h0IC5hdGMgewogIGJhY2tncm91bmQtY29sb3I6ICMxMWJhNmQ7Cn0KI21weHFfcmlnaHQgLmlucHV0X2NscyB7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIGJvcmRlcjogbm9uZTsKICB3aWR0aDogOTklOwogIGhlaWdodDogOTklOwp9Cg=="}, {"version": 3, "sources": ["gswh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4PA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "gswh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk", "sourcesContent": ["<template>\n  <el-row :gutter=\"16\">\n    <el-col :span=\"12\">\n      <div class=\"txtTitle\">\n        <span @click=\"click('hsk')\" :class=\"this.flag === 'hsk'?'tabActive':'noActive'\" class=\"oneBtn\">\n          <span class=\"allBtn\">函数库</span>\n        </span>\n        <span @click=\"click('syxm')\" :class=\"this.flag === 'syxm'?'tabActive':'noActive'\" class=\"twoBtn\">\n          <span class=\"allBtn\">试验项目表格</span>\n        </span>\n      </div>\n      <el-aside width=\"15vw\" style=\"background-color: rgb(238, 241, 246)\" v-show=\"this.flag === 'hsk'\">\n        <el-tree :data=\"treeData\" :props=\"defaultProps\" @node-click=\"handleNodeClick\"\n                 style=\"line-height: 2vh;height: 47vh; padding:10px;\"></el-tree>\n      </el-aside>\n      <table id=\"mpxq_right\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" v-show=\"this.flag === 'syxm'\"></table>\n    </el-col>\n    <!--  脚本编辑  -->\n    <el-col :span=\"12\">\n      <GsEdit ref=\"editJb\" :jb=\"jb\" :tableData=\"tableData\" @setJbVal=\"setJbVal\" @jbClose=\"jbClose\"></GsEdit>\n    </el-col>\n  </el-row>\n</template>\n<script>\nimport GsEdit from \"@/views/dagangOilfield/bzgl/sbztpjbzk/gswh_edit\";\nimport {getTreeHsk} from \"@/api/dagangOilfield/bzgl/hskwh\";\nimport {getById, getTable} from \"@/api/dagangOilfield/bzgl/sympk/sympk\";\nimport {getPageList} from \"@/api/dagangOilfield/bzgl/sbztpjbzk/ztxxsjgl\";\nimport {Loading} from \"element-ui\";\n\nexport default {\n  name: 'gswh',\n  components: {GsEdit},\n  props: {\n    jb: {\n      type: String,\n      default:'',\n    },\n    syxm:{\n      type: String,\n      default:'',\n    }\n  },\n  data() {\n    return {\n      mpData:{},\n      //初始表格的行数 列数\n      hs: \"\",\n      ls: \"\",\n      tdWidth: 0, //一个单元格所占宽度\n      tableData:[],\n      //树数据\n      treeData: [],\n      defaultProps: {\n        children: 'children',\n        label: 'label'\n      },\n      flag:'hsk'\n    };\n  },\n  async mounted() {\n    getTreeHsk().then(res => {\n      this.treeData = res.data;\n    });\n  },\n  watch: {\n    syxm: {\n      async handler(newVal, oldVal) {\n        if (newVal){\n          const {data, code} = await getById(newVal);\n          if (code === \"0000\") {\n            this.mpData = data;\n            this.initTableData();\n            //获取表格数据\n            getTable({obj_id: newVal, lbbs: \"A\"}).then((res1) => {\n              if (res1.code === \"0000\") {\n                this.tableData = res1.data;\n                this.processTable();\n              }\n            });\n          }\n        }\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  methods: {\n    //更新输入框的值\n    updateInputValue(arrs) {\n      for (let i = 0; i < arrs.length; i++) {\n        let ele = document.getElementById(arrs[i]);\n        if (ele != null && typeof ele != \"undefined\") {\n          switch (arrs[i]) {\n            case \"hs\":\n              ele.value = this.hs;\n              break;\n            case \"ls\":\n              ele.value = this.ls;\n              break;\n            case \"addhs\":\n              ele.value = this.addhs;\n              break;\n            case \"addls\":\n              ele.value = this.addls;\n              break;\n          }\n        }\n      }\n    },\n    //获取铭牌内容数据\n    initTableData() {\n\n      this.hs = typeof (this.mpData.AHs) != 'undefined'?this.mpData.AHs:this.mpData.aHs;\n      this.ls = typeof (this.mpData.ALs) != 'undefined'?this.mpData.ALs:this.mpData.aLs;\n      //更新输入框的值\n      this.updateInputValue([\"hs\", \"ls\"]);\n      this.processTable();\n    },\n    //根据行数和列数创建表格\n    processTable() {\n      var tbody = document.getElementById(\"mpxq_right\");\n      if (tbody != null) {\n        tbody.innerHTML = \"\";\n        let hs = this.hs;\n        let ls = this.ls;\n        this.tdWidth = 100 / Number(ls);\n        let str = \"\";\n\n        for (let i = 0; i < hs; i++) {\n          let temp = \"<tr>\";\n          for (let j = 0; j < this.tableData.length; j++) {\n            let item = this.tableData[j];\n            let nrbs = item.nrbs == null ? \"-\" : item.nrbs;\n            if (item.rowindex === i.toString()) {\n              temp +=\n                  \"<td class='trName' id='\" +\n                  item.objId +\n                  \"' style='width: \" +\n                  this.tdWidth * item.colspan +\n                  \"%' rowspan='\" +\n                  item.rowspan +\n                  \"' colspan='\" +\n                  item.colspan +\n                  \"'>\" +\n                  nrbs +\n                  \"</td>\";\n            }\n          }\n          temp += \"</tr>\";\n          str += temp;\n        }\n        tbody.innerHTML = str;\n     //给循环出来的单元格加上点击事件\n        this.addClickEvent();\n      }\n    },\n    //tab切换\n    click(mainTab){\n      this.flag = mainTab;\n    },\n    //树点击方法\n    handleNodeClick(data,node) {\n      if (node.level === 2) {\n        this.$refs.editJb.trTableClick(data.label+\"()\");\n      }\n    },\n\n    setJbVal(val) {\n      this.$emit('setJbVal', val);\n    },\n    //关闭脚本弹框\n    jbClose() {\n      this.$emit('jbClose');\n    },\n    //给循环出来的单元格加上点击事件\n    addClickEvent() {\n      let trArr = document.getElementsByClassName(\"trName\"); //不可编辑的单元格\n      let that = this;\n      if (trArr != null) {\n        //循环所有的tr\n        for (let i = 0; i < trArr.length; i++) {\n          trArr[i].ondblclick = function () {\n             that.getCellEle(this.id);\n          };\n        }\n      }\n    },\n\n    //获取某个单元格对象\n    getCellEle(objId) {\n      let result = null;\n      this.tableData.forEach((item) => {\n        if (item.objId === objId) {\n          result = item;\n        }\n      });\n      if (result.sjlx === \"STRING\"){\n        this.$refs.editJb.trTableClick(objId);\n      }else {\n          this.$message.warning(\"只能选择可编辑单元格！\")\n      }\n    },\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.jbwh_box {\n  padding: 20px 0 0 20px;\n}\n\n.jbwh_box1 {\n  margin-top: -18px;\n}\n\n.tabActive {\n  //width: 10%;\n  float: left;\n  color: #fff;\n  background: #02b988;\n  border-top: 0;\n}\n\n.noActive {\n  //width: 10%;\n  float: left;\n  background: #fff;\n  color: #545252;\n\n  &:hover {\n    background: #FFFFFF;\n    color: #359076;\n  }\n}\n\n.oneBtn {\n  margin-right: -15px;\n}\n\n.twoBtn {\n  transform: skewX(33deg);\n  border-right: 1px solid #9a989869;\n\n  .allBtn {\n    transform: skewX(-33deg);\n    display: inline-block;\n  }\n}\n\n</style>\n<style>\n#mpxq_right td {\n  border: 1px solid #000;\n  height: 35px;\n  line-height: 35px;\n  text-align: center;\n}\n#mpxq_right tr {\n  height: 35px;\n}\n#mpxq_right .atc {\n  background-color: #11ba6d;\n}\n#mpxq_right .input_cls {\n  text-align: center;\n  border: none;\n  width: 99%;\n  height: 99%;\n}\n</style>\n\n"]}]}