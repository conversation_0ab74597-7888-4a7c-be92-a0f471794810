{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\components\\icons\\svg-icons.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\components\\icons\\svg-icons.js", "mtime": 1706897322181}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5Lm1hcCIpOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC50by1zdHJpbmciKTsKCnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYyIpOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5tYXRjaCIpOwoKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuaXRlcmF0b3IiKTsKCk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAiX19lc01vZHVsZSIsIHsKICB2YWx1ZTogdHJ1ZQp9KTsKZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwOwoKdmFyIHJlcSA9IHJlcXVpcmUuY29udGV4dCgnLi4vLi4vLi4vYXNzZXRzL2ljb25zL3N2ZycsIGZhbHNlLCAvXC5zdmckLyk7Cgp2YXIgcmVxdWlyZUFsbCA9IGZ1bmN0aW9uIHJlcXVpcmVBbGwocmVxdWlyZUNvbnRleHQpIHsKICByZXR1cm4gcmVxdWlyZUNvbnRleHQua2V5cygpOwp9OwoKdmFyIHJlID0gL1wuXC8oLiopXC5zdmcvOwp2YXIgc3ZnSWNvbnMgPSByZXF1aXJlQWxsKHJlcSkubWFwKGZ1bmN0aW9uIChpKSB7CiAgcmV0dXJuIGkubWF0Y2gocmUpWzFdOwp9KTsKdmFyIF9kZWZhdWx0ID0gc3ZnSWNvbnM7CmV4cG9ydHMuZGVmYXVsdCA9IF9kZWZhdWx0Ow=="}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/views/components/icons/svg-icons.js"], "names": ["req", "require", "context", "requireAll", "requireContext", "keys", "re", "svgIcons", "map", "i", "match"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,IAAMA,GAAG,GAAGC,OAAO,CAACC,OAAR,CAAgB,2BAAhB,EAA6C,KAA7C,EAAoD,QAApD,CAAZ;;AACA,IAAMC,UAAU,GAAG,SAAbA,UAAa,CAAAC,cAAc;AAAA,SAAIA,cAAc,CAACC,IAAf,EAAJ;AAAA,CAAjC;;AAEA,IAAMC,EAAE,GAAG,eAAX;AAEA,IAAMC,QAAQ,GAAGJ,UAAU,CAACH,GAAD,CAAV,CAAgBQ,GAAhB,CAAoB,UAAAC,CAAC,EAAI;AACxC,SAAOA,CAAC,CAACC,KAAF,CAAQJ,EAAR,EAAY,CAAZ,CAAP;AACD,CAFgB,CAAjB;eAIeC,Q", "sourcesContent": ["const req = require.context('../../../assets/icons/svg', false, /\\.svg$/)\r\nconst requireAll = requireContext => requireContext.keys()\r\n\r\nconst re = /\\.\\/(.*)\\.svg/\r\n\r\nconst svgIcons = requireAll(req).map(i => {\r\n  return i.match(re)[1]\r\n})\r\n\r\nexport default svgIcons\r\n"]}]}