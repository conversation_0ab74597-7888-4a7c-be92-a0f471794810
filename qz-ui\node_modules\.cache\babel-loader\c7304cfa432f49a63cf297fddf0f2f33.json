{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\blgk\\bzkwh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\blgk\\bzkwh.vue", "mtime": 1706897322268}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["bzkwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAqWA;;AACA;;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;AACA,EAAA,IAAA,EAAA,OADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA,MAAA,IAAA,EAAA,KADA;AAEA,MAAA,UAAA,EAAA;AACA,QAAA,IAAA,EAAA;AACA,UAAA,IAAA,EAAA,EADA;AAEA,UAAA,EAAA,EAAA,EAFA;AAGA,UAAA,IAAA,EAAA,EAHA;AAIA,UAAA,MAAA,EAAA;AAJA,SADA;AAOA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,OAAA;AAAA,UAAA,KAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAJA;AAPA,OAFA;AAgBA,MAAA,gBAAA,EAAA;AACA,QAAA,KAAA,EAAA;AACA,UAAA,QAAA,EAAA,EADA;AAEA,UAAA,OAAA,EAAA,CAFA;AAGA,UAAA,KAAA,EAAA,CAHA;AAIA,UAAA,KAAA,EAAA,CAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,GAAA;AAJA,SADA;AAOA,QAAA,MAAA,EAAA;AACA,UAAA,QAAA,EAAA,IADA;AAEA,UAAA,YAAA,EAAA;AAFA,SAPA;AAWA,QAAA,SAAA,EAAA,EAXA;AAYA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,QAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAHA,EAIA;AAAA,UAAA,IAAA,EAAA,MAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAJA,EAKA;AAAA,UAAA,IAAA,EAAA,IAAA;AAAA,UAAA,KAAA,EAAA,IAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SALA,EAMA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,KAAA,EAAA,MAAA;AAAA,UAAA,QAAA,EAAA;AAAA,SANA,CAOA;AAPA;AAZA,OAhBA;AAsCA,MAAA,WAAA,EAAA,EAtCA;AAuCA,MAAA,WAAA,EAAA,EAvCA;AAuCA;AACA,MAAA,YAAA,EAAA,EAxCA;AAwCA;AACA,MAAA,YAAA,EAAA,KAzCA;AA0CA,MAAA,QAAA,EAAA,KA1CA;AA2CA,MAAA,YAAA,EAAA,KA3CA;AA4CA,MAAA,UAAA,EAAA,KA5CA;AA6CA,MAAA,QAAA,EAAA,EA7CA;AA6CA;AACA,MAAA,SAAA,EAAA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,YAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,EAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,YAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAVA;AAaA,QAAA,EAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,QAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAbA;AAgBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAhBA,CAmBA;AACA;AACA;AACA;;AAtBA,OA9CA;AAqEA;AACA,MAAA,UAAA,EAAA,EAtEA;AAsEA;AACA,MAAA,WAAA,EAAA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,YAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,EAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,YAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAVA;AAaA,QAAA,EAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,QAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAbA;AAgBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAhBA,CAmBA;AACA;AACA;AACA;;AAtBA,OAvEA;AA8FA;AACA,MAAA,MAAA,EAAA,EA/FA;AA+FA;AACA,MAAA,OAAA,EAAA;AACA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CADA;AAIA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,YAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAJA;AAOA,QAAA,EAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,YAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAPA;AAUA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAVA;AAaA,QAAA,EAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,QAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAbA;AAgBA,QAAA,IAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAhBA,CAmBA;AACA;AACA;AACA;;AAtBA,OAhGA;AAuHA;AACA,MAAA,QAAA,EAAA,EAxHA;AAwHA;AACA,MAAA,WAAA,EAAA,EAzHA;AAyHA;AACA,MAAA,UAAA,EAAA,EA1HA;AA0HA;AACA,MAAA,MAAA,EAAA,EA3HA;AA2HA;AACA,MAAA,QAAA,EAAA,EA5HA;AA4HA;AACA,MAAA,MAAA,EAAA,EA7HA;AA6HA;AACA,MAAA,QAAA,EAAA,EA9HA,CA8HA;;AA9HA,KAAA;AAgIA,GAnIA;AAoIA,EAAA,OApIA,qBAoIA;AACA,SAAA,OAAA;AACA,SAAA,WAAA,GAFA,CAGA;;AACA,SAAA,WAAA,GAJA,CAKA;;AACA,SAAA,cAAA,GANA,CAOA;;AACA,SAAA,SAAA,GARA,CASA;;AACA,SAAA,WAAA;AACA,GA/IA;AAgJA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,WAFA,yBAEA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,2CAAA,WAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,oBAAA,KAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AAAA,sBAAA,KAAA,EAAA,IAAA,CAAA,KAAA;AAAA,sBAAA,KAAA,EAAA,IAAA,CAAA;AAAA,qBAAA;AACA,mBAFA;;AAGA,kBAAA,KAAA,CAAA,UAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,wBAAA,IAAA,CAAA,KAAA,KAAA,QAAA,EAAA;AACA,sBAAA,IAAA,CAAA,OAAA,GAAA,KAAA,CAAA,QAAA;AACA,6BAAA,KAAA;AACA;AACA,mBALA;AAMA,iBAVA;;AADA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,KAdA;AAeA;AACA,IAAA,SAhBA,uBAgBA;AAAA;;AACA,iCAAA,SAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,UAAA,MAAA,CAAA,MAAA,CAAA,IAAA,CAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA,KAAA;AAAA,YAAA,KAAA,EAAA,IAAA,CAAA;AAAA,WAAA;AACA,SAFA;AAGA,OAJA;AAKA,KAtBA;AAuBA;AACA,IAAA,WAxBA,yBAwBA;AAAA;;AACA,gCAAA;AAAA,QAAA,EAAA,EAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,MAAA,CAAA,QAAA,GAAA,GAAA,CAAA,IAAA;;AACA,QAAA,MAAA,CAAA,UAAA,CAAA,SAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,cAAA,IAAA,CAAA,KAAA,KAAA,MAAA,EAAA;AACA,YAAA,IAAA,CAAA,OAAA,GAAA,GAAA,CAAA,IAAA;AACA,mBAAA,KAAA;AACA;AACA,SALA;AAMA,OARA;AASA,KAlCA;AAmCA;AACA,IAAA,cApCA,4BAoCA;AAAA;;AACA,WAAA,WAAA,GAAA,EAAA;AACA,gDAAA;AAAA,QAAA,GAAA,EAAA,MAAA;AAAA,QAAA,OAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,MAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,YAAA,GAAA,CAAA,IAAA,EAAA;AACA,UAAA,GAAA,CAAA,IAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,MAAA,CAAA,WAAA,CAAA,IAAA,CAAA;AAAA,cAAA,KAAA,EAAA,IAAA,CAAA,IAAA;AAAA,cAAA,KAAA,EAAA,IAAA,CAAA;AAAA,aAAA;AACA,WAFA;AAGA;AACA,OANA;AAOA,KA7CA;AA8CA;AACA,IAAA,SA/CA,qBA+CA,GA/CA,EA+CA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,MAAA,GAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,QAAA,EAAA,MAAA,EAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,UAAA,EAAA,MAAA,EAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,MAAA,EAAA,MAAA,EAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,QAAA,EAAA,IAAA,EAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,UAAA,EAAA,IAAA,EAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,MAAA,EAAA,IAAA,EAAA,EAAA,EAPA,CAQA;;;AARA;AAAA,uBASA,oBAAA;AAAA,kBAAA,IAAA,EAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,MAAA,CAAA,MAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,CATA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA,KA3DA;AA4DA;AACA,IAAA,aA7DA,yBA6DA,GA7DA,EA6DA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,UAAA,GAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,QAAA,EAAA,IAAA,EAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,UAAA,EAAA,IAAA,EAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,MAAA,EAAA,IAAA,EAAA,EAAA;;AAEA,gBAAA,IANA,GAMA,EANA;;AAOA,gBAAA,MAAA,CAAA,MAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,sBAAA,IAAA,CAAA,KAAA,IAAA,GAAA,EAAA;AACA,oBAAA,IAAA,GAAA,IAAA,CAAA,KAAA;AACA,2BAAA,KAAA;AACA;AACA,iBALA,EAPA,CAaA;;;AAbA;AAAA,uBAcA,oBAAA;AAAA,kBAAA,IAAA,EAAA,MAAA,CAAA,QAAA,CAAA,IAAA;AAAA,kBAAA,IAAA,EAAA;AAAA,iBAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,kBAAA,MAAA,CAAA,UAAA,GAAA,GAAA,CAAA,IAAA;AACA,iBAFA,CAdA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA,KA9EA;AA+EA;AACA,IAAA,SAhFA,qBAgFA,GAhFA,EAgFA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,gBAAA,MAAA,CAAA,QAAA,mCAAA,GAAA,EADA,CAEA;;AAFA;AAAA,uBAGA,MAAA,CAAA,SAAA,CAAA,GAAA,CAAA,IAAA,CAHA;;AAAA;AAAA;AAAA,uBAIA,MAAA,CAAA,aAAA,CAAA,GAAA,CAAA,IAAA,CAJA;;AAAA;AAKA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,QAAA,EAAA,MAAA,EAAA,GAAA,CAAA,IAAA;;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,QAAA,EAAA,IAAA,EAAA,GAAA,CAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,YAAA,GAAA,KAAA;AACA,gBAAA,MAAA,CAAA,UAAA,GAAA,IAAA;;AARA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,KAzFA;AA0FA;AACA,IAAA,SA3FA,qBA2FA,GA3FA,EA2FA;AAAA;;AACA,WAAA,QAAA,CAAA,oBAAA,EAAA,IAAA,EAAA;AACA,QAAA,iBAAA,EAAA,IADA;AAEA,QAAA,gBAAA,EAAA,IAFA;AAGA,QAAA,IAAA,EAAA;AAHA,OAAA,EAIA,IAJA,CAIA,YAAA;AACA,gCAAA,CAAA,GAAA,CAAA,KAAA,CAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,cAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,SADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;;AAIA,YAAA,MAAA,CAAA,OAAA;AACA,WANA,MAMA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA;AACA,cAAA,IAAA,EAAA,OADA;AAEA,cAAA,OAAA,EAAA;AAFA,aAAA;AAIA;AACA,SAbA;AAcA,OAnBA;AAoBA,KAhHA;AAiHA;AACA,IAAA,OAlHA,mBAkHA,GAlHA,EAkHA;AACA,WAAA,QAAA,mCAAA,GAAA;AACA,WAAA,YAAA,GAAA,IAAA;AACA,KArHA;AAsHA;AACA,IAAA,OAvHA,mBAuHA,QAvHA,EAuHA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA,gBAAA,MAAA,CAAA,UAAA,GAAA,EAAA;AACA,gBAAA,MAAA,CAAA,YAAA,GAAA,KAAA,CAHA,CAIA;;AAJA,qBAKA,MAAA,CAAA,WAAA,CAAA,IALA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBAMA,MAAA,CAAA,aAAA,CAAA,MAAA,CAAA,WAAA,CAAA,IAAA,CANA;;AAAA;AAAA,+BAQA,QARA;AAAA,kDASA,IATA,wBAcA,QAdA,yBAmBA,MAnBA;AAAA;;AAAA;AASA;AACA,gBAAA,MAAA,CAAA,MAAA,GAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,MAAA,EAAA,MAAA,EAAA,MAAA,CAAA,WAAA,CAAA,IAAA;;AACA,gBAAA,MAAA,CAAA,QAAA,GAAA,IAAA;AAZA;;AAAA;AAcA;AACA,gBAAA,MAAA,CAAA,UAAA,GAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,UAAA,EAAA,MAAA,EAAA,MAAA,CAAA,WAAA,CAAA,IAAA;;AACA,gBAAA,MAAA,CAAA,YAAA,GAAA,IAAA;AAjBA;;AAAA;AAmBA;AACA,gBAAA,MAAA,CAAA,QAAA,GAAA,EAAA;;AACA,gBAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,QAAA,EAAA,MAAA,EAAA,MAAA,CAAA,WAAA,CAAA,IAAA;;AACA,gBAAA,MAAA,CAAA,UAAA,GAAA,IAAA;AAtBA;;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA2BA,KAlJA;AAmJA;AACA,IAAA,QApJA,oBAoJA,QApJA,EAoJA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBACA,OAAA,CAAA,KAAA,CAAA,QAAA,EAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,sBAAA,KAAA,EAAA;AACA,wBAAA,QAAA,GAAA,EAAA;;AACA,4BAAA,QAAA;AACA,2BAAA,UAAA;AAAA;AACA,wBAAA,QAAA,+DAAA,QAAA,GAAA,OAAA,CAAA,QAAA,CAAA;;AACA,wBAAA,OAAA,CAAA,UAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,8BAAA,IAAA,CAAA,KAAA,KAAA,QAAA,CAAA,EAAA,EAAA;AACA,4BAAA,QAAA,CAAA,EAAA,GAAA,IAAA,CAAA,KAAA;AACA;AACA,yBAJA;;AAKA,wBAAA,OAAA,CAAA,MAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,8BAAA,IAAA,CAAA,KAAA,KAAA,QAAA,CAAA,IAAA,EAAA;AACA,4BAAA,QAAA,CAAA,IAAA,GAAA,IAAA,CAAA,KAAA;AACA;AACA,yBAJA;;AAKA;;AACA,2BAAA,YAAA;AAAA;AACA,wBAAA,QAAA,+DAAA,QAAA,GAAA,OAAA,CAAA,UAAA,CAAA;;AACA,wBAAA,OAAA,CAAA,MAAA,CAAA,OAAA,CAAA,UAAA,IAAA,EAAA;AACA,8BAAA,IAAA,CAAA,KAAA,KAAA,QAAA,CAAA,IAAA,EAAA;AACA,4BAAA,QAAA,CAAA,IAAA,GAAA,IAAA,CAAA,KAAA;AACA;AACA,yBAJA;;AAKA;;AACA,2BAAA,QAAA;AAAA;AACA,wBAAA,QAAA,+DAAA,QAAA,GAAA,OAAA,CAAA,MAAA,CAAA;AACA;;AACA;AACA;AA1BA;;AA4BA,+CAAA,QAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,0BAAA,GAAA,CAAA,IAAA,KAAA,MAAA,EAAA;AACA,wBAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;;AACA,wBAAA,OAAA,CAAA,UAAA,GAAA,KAAA;AACA,wBAAA,OAAA,CAAA,YAAA,GAAA,KAAA;AACA,wBAAA,OAAA,CAAA,YAAA,GAAA,KAAA;AACA,wBAAA,OAAA,CAAA,QAAA,GAAA,KAAA;;AACA,wBAAA,OAAA,CAAA,OAAA,GANA,CAOA;;;AACA,4BAAA,QAAA,IAAA,QAAA,EAAA;AACA,0BAAA,OAAA,CAAA,WAAA,GADA,CAEA;;;AACA,0BAAA,OAAA,CAAA,WAAA;AACA,yBAZA,CAaA;;AACA,uBAdA,MAcA;AACA,wBAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;AACA;AACA,qBAlBA;AAmBA,mBAjDA,MAiDA;AACA,oBAAA,OAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA;;AACA,2BAAA,KAAA;AACA;AACA,iBAtDA,CADA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAwDA,KA5MA;AA6MA;AACA,IAAA,QA9MA,oBA8MA,IA9MA,EA8MA;AACA,WAAA,YAAA,GAAA,KAAA;;AACA,cAAA,IAAA;AACA,aAAA,IAAA;AACA,eAAA,QAAA,GAAA,KAAA;AACA;;AACA,aAAA,QAAA;AACA,eAAA,YAAA,GAAA,KAAA;AACA;;AACA,aAAA,MAAA;AACA,eAAA,UAAA,GAAA,KAAA;AACA;;AACA,aAAA,MAAA;AACA,eAAA,YAAA,GAAA,KAAA;AACA;;AACA;AACA,eAAA,QAAA,GAAA,KAAA;AACA,eAAA,YAAA,GAAA,KAAA;AACA,eAAA,UAAA,GAAA,KAAA;AACA,eAAA,YAAA,GAAA,KAAA;AACA;AAlBA;AAoBA,KApOA;AAqOA;AACA,IAAA,WAtOA,yBAsOA;AACA,WAAA,WAAA,GAAA;AAAA,QAAA,IAAA,EAAA,KAAA;AAAA,OAAA,CADA,CACA;AACA,KAxOA;AAyOA,IAAA,WAzOA,yBAyOA;AAAA;;AACA,6BAAA;AAAA,QAAA,EAAA,EAAA;AAAA,OAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,WAAA,GAAA,GAAA,CAAA,IAAA;AACA,OAFA;AAGA,KA7OA;AA8OA;AACA,IAAA,eA/OA,2BA+OA,IA/OA,EA+OA;AACA,WAAA,YAAA,GAAA,IAAA;;AACA,UAAA,IAAA,CAAA,UAAA,KAAA,GAAA,EAAA;AAAA;AACA,aAAA,WAAA,CAAA,IAAA,GAAA,IAAA,CAAA,EAAA;AACA,OAFA,MAEA;AAAA;AACA,aAAA,WAAA,CAAA,IAAA,GAAA,EAAA;AACA;;AACA,WAAA,OAAA;AACA,KAvPA;AAwPA;AACA,IAAA,OAzPA,mBAyPA,MAzPA,EAyPA;AAAA;;AACA,WAAA,IAAA,GAAA,IAAA;AACA,WAAA,WAAA,+DAAA,KAAA,WAAA,GAAA,MAAA;AACA,4BAAA,KAAA,WAAA,EAAA,IAAA,CAAA,UAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,gBAAA,CAAA,SAAA,GAAA,GAAA,CAAA,IAAA,CAAA,OAAA;AACA,QAAA,OAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA;AACA,QAAA,OAAA,CAAA,IAAA,GAAA,KAAA;AACA,OAJA;AAKA;AAjQA;AAhJA,C", "sourcesContent": ["<template>\n  <div class=\"app-container\" id=\"sbqxDiv\">\n    <!--左侧树组件-->\n    <el-row :gutter=\"1\">\n      <!--   左侧树   -->\n      <el-col :span=\"4\">\n        <el-card style=\"background:#e0f8ed;padding-top:10px;\" shadow=\"never\">\n          <div class=\"text head-container\">\n            <el-col style=\"padding:0;\">\n              <el-tree\n                id=\"tree\"\n                :data=\"treeOptions\"\n                @node-click=\"handleNodeClick\"\n                :highlight-current=\"true\"\n                ref=\"tree\"\n                node-key=\"id\"\n                :default-checked-keys=\"['0']\"\n                :default-expanded-keys=\"['0']\"\n                accordion\n              />\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :btnHidden=\"false\"\n          :width=\"{ labelWidth: 80, itemWidth: 165 }\"\n          @handleReset=\"filterReset\"\n        />\n        <el-white class=\"button-group1\">\n          <div class=\"button_btn\">\n            <el-button type=\"primary\" icon=\"el-icon-plus\" v-hasPermi=\"['blgkbzk:button:add']\" @click=\"addForm('lx')\">新增不良工况类型</el-button>\n            <el-button type=\"primary\" icon=\"el-icon-plus\" v-hasPermi=\"['blgkbzk:button:add']\" @click=\"addForm('blgkms')\">新增不良工况描述</el-button>\n            <el-button type=\"primary\" icon=\"el-icon-plus\" v-hasPermi=\"['blgkbzk:button:add']\" @click=\"addForm('flyj')\">新增分类依据</el-button>\n          </div>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            height=\"67vh\"\n            v-loading=\"load\">\n            <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\" width=\"200\" :resizable=\"false\">\n              <template slot-scope=\"scope\">\n                <el-button type=\"text\" size=\"small\" @click=\"updateRow(scope.row)\" v-hasPermi=\"['blgkbzk:button:update']\" title=\"修改\"  class='el-icon-edit'/>\n                <el-button type=\"text\" size=\"small\" @click=\"viewFun(scope.row)\" title=\"查看\" class='el-icon-view'/>\n                <el-button type=\"text\" size=\"small\" @click=\"deleteRow(scope.row)\" v-hasPermi=\"['blgkbzk:button:delete']\" title=\"删除\" class='el-icon-delete'/>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!--  新增不良工况类型  -->\n    <el-dialog title=\"新增不良工况类型\" :visible.sync=\"isShowLx\" width=\"58%\" @close=\"closeFun('lx')\" v-dialogDrag>\n      <el-form label-width=\"140px\" :rules=\"lxRules\" :model=\"lxForm\" ref=\"lxForm\">\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sblx\" label=\"设备类型\">\n                <el-select placeholder=\"设备类型\" v-model=\"lxForm.sblx\" style=\"width:80%\" filterable clearable>\n                  <el-option\n                    v-for=\"item in allSblxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"不良工况类型:\" prop=\"type\">\n                <el-input v-model=\"lxForm.type\" placeholder=\"请输入类型\" style=\"width: 80%\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"dj\" label=\"等级\">\n                <el-select placeholder=\"等级\" v-model=\"lxForm.dj\" style=\"width:80%\" filterable clearable>\n                  <el-option\n                    v-for=\"item in djList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n<!--            <el-col :span=\"12\">-->\n<!--              <el-form-item label=\"扣分值:\" prop=\"kfz\">-->\n<!--                <el-input v-model=\"lxForm.kfz\" placeholder=\"请输入扣分值\" style=\"width: 80%\"/>-->\n<!--              </el-form-item>-->\n<!--            </el-col>-->\n            <el-col :span=\"12\">\n              <el-form-item prop=\"pjjg\" label=\"评价结果\">\n                <el-select placeholder=\"请选择评价结果\" v-model=\"lxForm.pjjg\" style=\"width:80%\" filterable clearable>\n                  <el-option\n                    v-for=\"item in pjjgList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"不良工况描述:\" prop=\"ms\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"lxForm.ms\" placeholder=\"请输入不良工况描述\" style=\"width: 92%\"/>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"lxForm.flyj\" placeholder=\"请输入分类依据\" style=\"width: 92%\"/>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun('lx')\" size=\"small\">取 消</el-button>\n        <el-button type=\"primary\" size=\"small\" @click=\"saveForm('lxForm')\">保存</el-button>\n      </div>\n    </el-dialog>\n\n    <!--  新增不良工况描述  -->\n    <el-dialog title=\"新增不良工况描述\" :visible.sync=\"isShowBlgkMs\" width=\"58%\" @close=\"closeFun('blgkms')\" v-dialogDrag>\n      <el-form label-width=\"140px\" :rules=\"blgkmsRules\" :model=\"blgkmsForm\" ref=\"blgkmsForm\">\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sblx\" label=\"设备类型\">\n                <el-select placeholder=\"设备类型\" @change=\"getLxList\" v-model=\"blgkmsForm.sblx\" style=\"width:80%\" filterable clearable>\n                  <el-option\n                    v-for=\"item in sblxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"不良工况类型:\" prop=\"type\">\n                <el-select placeholder=\"不良工况类型\" v-model=\"blgkmsForm.type\" style=\"width:80%\" filterable clearable>\n                  <el-option\n                    v-for=\"item in lxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"dj\" label=\"等级\">\n                <el-select placeholder=\"等级\" v-model=\"blgkmsForm.dj\" style=\"width:80%\" filterable clearable>\n                  <el-option\n                    v-for=\"item in djList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n<!--            <el-col :span=\"12\">-->\n<!--              <el-form-item label=\"扣分值:\" prop=\"kfz\">-->\n<!--                <el-input v-model=\"blgkmsForm.kfz\" placeholder=\"请输入扣分值\" style=\"width: 80%\"/>-->\n<!--              </el-form-item>-->\n<!--            </el-col>-->\n            <el-col :span=\"12\">\n              <el-form-item prop=\"pjjg\" label=\"评价结果\">\n                <el-select placeholder=\"请选择评价结果\" v-model=\"blgkmsForm.pjjg\" style=\"width:80%\" filterable clearable>\n                  <el-option\n                    v-for=\"item in pjjgList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"不良工况描述:\" prop=\"ms\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"blgkmsForm.ms\" placeholder=\"请输入不良工况描述\" style=\"width: 92%\"/>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"blgkmsForm.flyj\" placeholder=\"请输入分类依据\" style=\"width: 92%\"/>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun('qxms')\" size=\"small\">取 消</el-button>\n        <el-button type=\"primary\" size=\"small\" @click=\"saveForm('blgkmsForm')\">保存</el-button>\n      </div>\n    </el-dialog>\n\n    <!--  新增分类依据  -->\n    <el-dialog title=\"新增分类依据\" :visible.sync=\"isShowFlyj\"  width=\"58%\" @close=\"closeFun('flyj')\" v-dialogDrag>\n      <el-form label-width=\"140px\" :rules=\"flyjRules\" :model=\"flyjForm\" ref=\"flyjForm\">\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sblx\" label=\"设备类型\">\n                <el-select placeholder=\"设备类型\" v-model=\"flyjForm.sblx\" style=\"width:80%\" @change=\"getLxList\" filterable clearable>\n                  <el-option\n                    v-for=\"item in sblxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item label=\"不良工况类型:\" prop=\"type\">\n                <el-select placeholder=\"不良工况类型\" @change=\"getblgkmsList\" v-model=\"flyjForm.type\" style=\"width:80%\" filterable clearable>\n                  <el-option\n                    v-for=\"item in lxList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"dj\" label=\"等级\">\n                <el-select placeholder=\"等级\" v-model=\"flyjForm.dj\" style=\"width:80%\" filterable clearable>\n                  <el-option\n                    v-for=\"item in djList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n<!--            <el-col :span=\"12\">-->\n<!--              <el-form-item label=\"扣分值:\" prop=\"kfz\">-->\n<!--                <el-input v-model=\"flyjForm.kfz\" placeholder=\"请输入扣分值\" style=\"width: 80%\"/>-->\n<!--              </el-form-item>-->\n<!--            </el-col>-->\n            <el-col :span=\"12\">\n              <el-form-item prop=\"pjjg\" label=\"评价结果\">\n                <el-select placeholder=\"请选择评价结果\" v-model=\"flyjForm.pjjg\" style=\"width:80%\" filterable clearable>\n                  <el-option\n                    v-for=\"item in pjjgList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"item.value\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"24\">\n              <el-form-item label=\"不良工况描述:\" prop=\"ms\">\n                <el-select placeholder=\"不良工况描述\" v-model=\"flyjForm.ms\" style=\"width:90%\" filterable clearable>\n                  <el-option\n                    v-for=\"item in blgkmsList\"\n                    :key=\"item.value\"\n                    :label=\"item.label\"\n                    :value=\"String(item.value)\">\n                  </el-option>\n                </el-select>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"flyjForm.flyj\" placeholder=\"请输入分类依据\" style=\"width: 92%\"\n                />\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun('flyj')\" size=\"small\">取 消</el-button>\n        <el-button type=\"primary\" size=\"small\" @click=\"saveForm('flyjForm')\">保存</el-button>\n      </div>\n    </el-dialog>\n\n    <!--  详情查看  -->\n    <el-dialog title=\"详情查看\" :visible.sync=\"isShowDetail\" v-if=\"isShowDetail\"  width=\"58%\" @close=\"closeFun('view')\" v-dialogDrag>\n      <el-form label-width=\"140px\" :model=\"viewForm\" ref=\"viewForm\">\n        <el-card shadow=\"never\" class=\"box-cont\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>不良工况</span>\n          </div>\n          <el-row :gutter=\"15\" class=\"cont_top\">\n            <el-col :span=\"12\">\n              <el-form-item prop=\"sblxmc\" label=\"设备类型\">\n                <el-input v-model=\"viewForm.sblxmc\" placeholder=\"请输入设备类型\" style=\"width: 92%\" :disabled=\"true\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"type\" label=\"不良工况类型\">\n                <el-input v-model=\"viewForm.type\" placeholder=\"请输入不良工况类型\" style=\"width: 92%\" :disabled=\"true\"/>\n              </el-form-item>\n            </el-col>\n            <el-col :span=\"12\">\n              <el-form-item prop=\"dj\" label=\"等级\">\n                <el-input v-model=\"viewForm.dj\" placeholder=\"请输入不良工况等级\" style=\"width: 92%\" :disabled=\"true\"/>\n              </el-form-item>\n            </el-col>\n<!--            <el-col :span=\"12\">-->\n<!--              <el-form-item label=\"扣分值:\" prop=\"kfz\">-->\n<!--                <el-input v-model=\"viewForm.kfz\" placeholder=\"请输入扣分值\" style=\"width: 80%\" :disabled=\"true\"/>-->\n<!--              </el-form-item>-->\n<!--            </el-col>-->\n            <el-col :span=\"12\">\n              <el-form-item label=\"评价结果:\" prop=\"pjjgCn\">\n                <el-input v-model=\"viewForm.pjjgCn\" placeholder=\"请输入评价结果\" style=\"width: 80%\" :disabled=\"true\"/>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"不良工况描述:\" prop=\"ms\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"viewForm.ms\" placeholder=\"请输入不良工况描述\" style=\"width: 92%\" :disabled=\"true\"/>\n              </el-form-item>\n            </el-col>\n          </el-row>\n          <el-row :gutter=\"15\" class=\"pull-left\">\n            <el-col :span=\"24\">\n              <el-form-item label=\"分类依据:\" prop=\"flyj\">\n                <el-input type=\"textarea\" :rows=\"5\" v-model=\"viewForm.flyj\" placeholder=\"请输入分类依据\" style=\"width: 92%\" :disabled=\"true\"/>\n              </el-form-item>\n            </el-col>\n          </el-row>\n        </el-card>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"closeFun('view')\" size=\"small\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n\n</template>\n\n<script>\nimport {getData,saveOrUpdate,removeBzk,getMs,getLx,getDeviceClassTreeNodeByPid} from '@/api/blgk/blgkbzk'\nimport {getAllSblxList,getBlgkTree} from '@/api/blgk/blgk'\nimport {getDictTypeData} from \"@/api/system/dict/data\";\n\nexport default {\n  name: 'bzkwh',\n  data() {\n    return {\n      load:false,\n      filterInfo: {\n        data: {\n          sblx:'',\n          ms: '',\n          flyj: '',\n          pjjgCn: '',\n        },\n        fieldList: [\n          { label: '设备类型', type: 'select', value: 'sblx',options:[]},\n          { label: '不良工况描述', type: 'input', value: 'ms'},\n          { label: '分类依据', type: 'input', value: 'flyj'},\n          { label: '评价结果', type: 'select', value: 'pjjgCn',options:[]},\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: 'sblxmc', label: '设备类型', minWidth: '140' },\n          { prop: 'type', label: '不良工况类型', minWidth: '140'},\n          { prop: 'ms', label: '不良工况描述', minWidth: '220',showPop:true},\n          { prop: 'flyj', label: '分类依据', minWidth: '220',showPop:true},\n          { prop: 'dj', label: '等级', minWidth: '100'},\n          { prop: 'pjjgCn', label: '评价结果', minWidth: '120'},\n          // { prop: 'kfz', label: '扣分值', minWidth: '80'},\n        ]\n      },\n      queryParams:{},\n      treeOptions: [], //组织树\n      treeNodeData:{},//点击后的树节点数据\n      isShowDetail:false,\n      isShowLx:false,\n      isShowBlgkMs:false,\n      isShowFlyj:false,\n      flyjForm:{},//表单\n      flyjRules:{\n        sblx: [\n          { required: true, message: '设备类型不能为空', trigger: 'select' }\n        ],\n        type: [\n          { required: true, message: '不良工况类型不能为空', trigger: 'select' }\n        ],\n        ms: [\n          { required: true, message: '不良工况描述不能为空', trigger: 'select' }\n        ],\n        flyj: [\n          { required: true, message: '分类依据不能为空', trigger: 'blur' }\n        ],\n        dj: [\n          { required: true, message: '等级不能为空', trigger: 'select' }\n        ],\n        pjjg: [\n          { required: true, message: '评价结果不能为空', trigger: 'select' }\n        ],\n        // kfz: [\n        //   {required: true, message: '扣分值不能为空', trigger: 'blur'},\n        //   {pattern: /^[+-]?(0|([1-9]\\d*))(\\.\\d+)?$/g,message: \"请输入数字\",trigger: \"blur\"},\n        // ],\n      },//校验规则\n      blgkmsForm:{},//表单\n      blgkmsRules:{\n        sblx: [\n          { required: true, message: '设备类型不能为空', trigger: 'select' }\n        ],\n        type: [\n          { required: true, message: '不良工况类型不能为空', trigger: 'select' }\n        ],\n        ms: [\n          { required: true, message: '不良工况描述不能为空', trigger: 'blur' }\n        ],\n        flyj: [\n          { required: true, message: '分类依据不能为空', trigger: 'blur' }\n        ],\n        dj: [\n          { required: true, message: '等级不能为空', trigger: 'select' }\n        ],\n        pjjg: [\n          { required: true, message: '评价结果不能为空', trigger: 'select' }\n        ],\n        // kfz: [\n        //   { required: true, message: '扣分值不能为空', trigger: 'blur' },\n        //   {pattern: /^[+-]?(0|([1-9]\\d*))(\\.\\d+)?$/g,message: \"请输入数字\",trigger: \"blur\"},\n        // ],\n      },//校验规则\n      lxForm:{},//表单\n      lxRules:{\n        sblx: [\n          { required: true, message: '设备类型不能为空', trigger: 'select' }\n        ],\n        type: [\n          { required: true, message: '不良工况类型不能为空', trigger: 'blur' }\n        ],\n        ms: [\n          { required: true, message: '不良工况描述不能为空', trigger: 'blur' }\n        ],\n        flyj: [\n          { required: true, message: '分类依据不能为空', trigger: 'blur' }\n        ],\n        dj: [\n          { required: true, message: '等级不能为空', trigger: 'select' }\n        ],\n        pjjg: [\n          { required: true, message: '评价结果不能为空', trigger: 'select' }\n        ],\n        // kfz: [\n        //   { required: true, message: '扣分值不能为空', trigger: 'blur' },\n        //   {pattern: /^[+-]?(0|([1-9]\\d*))(\\.\\d+)?$/g,message: \"请输入数字\",trigger: \"blur\"},\n        // ],\n      },//校验规则\n      sblxList:[],//设备类型下拉框选项\n      allSblxList:[],//设备类型下拉框选项\n      blgkmsList:[],//不良工况描述下拉框选项\n      lxList:[],//不良工况类型下拉框选项\n      viewForm:{},//查看表单\n      djList:[],//不良工况等级下拉框\n      pjjgList:[],//评价结果下拉框\n    }\n  },\n  created() {\n    this.getData();\n    this.getTreeData();\n    //设备类型下拉框\n    this.getSblxList();\n    //设备类型下拉框\n    this.getAllSblxList();\n    //不良工况等级下拉框\n    this.getDjList();\n    //评价结果下拉框\n    this.getPjjgList();\n  },\n  methods: {\n    //评价结果\n    async getPjjgList() {\n      getDictTypeData('pjgz_pjjg').then(res => {\n        res.data.forEach(item => {\n          this.pjjgList.push({label: item.label, value: item.numvalue})\n        })\n        this.filterInfo.fieldList.forEach(item => {\n          if (item.value === 'pjjgCn') {\n            item.options = this.pjjgList;\n            return false;\n          }\n        })\n      })\n    },\n    //获取不良工况等级下拉框字典\n    getDjList(){\n      getDictTypeData('blgk_dj').then(res=>{\n        res.data.forEach(item=>{\n          this.djList.push({label:item.label,value:item.value})\n        })\n      })\n    },\n    //获取所有设备类型下拉框用于查询\n    getSblxList(){\n      getAllSblxList({zy:'bdsb'}).then(res=>{\n        this.sblxList = res.data;\n        this.filterInfo.fieldList.forEach(item=>{\n          if(item.value === 'sblx'){\n            item.options = res.data;\n            return false;\n          }\n        })\n      })\n    },\n    //获取所有设备类型下拉框用于查询\n    getAllSblxList(){\n      this.allSblxList = [];\n      getDeviceClassTreeNodeByPid({pid:'bdsb',sbpLogo:[\"输电设备\", \"变电设备\", \"配电设备\"]}).then(res=>{\n        if(res.data){\n          res.data.forEach(item=>{\n            this.allSblxList.push({label:item.name,value:item.code});\n          })\n        }\n      })\n    },\n    //获取不良工况类型下拉框\n    async getLxList(val){\n      this.lxList = [];\n      this.$set(this.flyjForm,'type','');\n      this.$set(this.blgkmsForm,'type','');\n      this.$set(this.lxForm,'type','');\n      this.$set(this.flyjForm,'ms','');\n      this.$set(this.blgkmsForm,'ms','');\n      this.$set(this.lxForm,'ms','');\n      //获取不良工况类型下拉框\n      await getLx({sblx:val}).then(res => {\n        this.lxList = res.data;\n      })\n    },\n    //获取不良工况描述下拉框\n    async getblgkmsList(val){\n      this.blgkmsList = [];\n      this.$set(this.flyjForm,'ms','');\n      this.$set(this.blgkmsForm,'ms','');\n      this.$set(this.lxForm,'ms','');\n\n      let type = '';\n      this.lxList.forEach(item=>{\n        if(item.value == val){\n          type = item.label;\n          return false;\n        }\n      })\n      //获取不良工况描述下拉框\n      await getMs({sblx:this.flyjForm.sblx,type: type}).then(res => {\n        this.blgkmsList = res.data;\n      })\n    },\n    //编辑\n    async updateRow(row){\n      this.flyjForm = {...row};\n      //下拉框回显\n      await this.getLxList(row.sblx);\n      await this.getblgkmsList(row.type);\n      this.$set(this.flyjForm,'type',row.type);\n      this.$set(this.flyjForm,'ms',row.ms);\n      this.isShowDetail = false;\n      this.isShowFlyj = true;\n    },\n    //删除\n    deleteRow(row){\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        removeBzk([row.objId]).then(res => {\n          if(res.code === '0000'){\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            });\n            this.getData();\n          }else{\n            this.$message({\n              type: 'error',\n              message: '删除失败!'\n            });\n          }\n        })\n      })\n    },\n    //查看\n    viewFun(row){\n      this.viewForm = {...row};\n      this.isShowDetail = true;\n    },\n    //新增\n    async addForm(formType){\n      //先清空下拉框的值\n      this.blgkmsList = [];\n      this.isShowDetail = false;\n      //如果树节点有值，则带过来\n      if(this.queryParams.sblx){\n        await this.getblgkmsList(this.queryParams.sblx);\n      }\n      switch (formType){\n        case 'lx'://类型\n          this.lxForm = {};\n          this.$set(this.lxForm,'sblx',this.queryParams.sblx);\n          this.isShowLx = true;\n          break;\n        case 'blgkms'://描述\n          this.blgkmsForm = {};\n          this.$set(this.blgkmsForm,'sblx',this.queryParams.sblx);\n          this.isShowBlgkMs = true;\n          break;\n        case 'flyj'://分类依据\n          this.flyjForm = {};\n          this.$set(this.flyjForm,'sblx',this.queryParams.sblx);\n          this.isShowFlyj = true;\n          break;\n        default:\n          break;\n      }\n    },\n    //保存\n    async saveForm(formType){\n      await this.$refs[formType].validate((valid) => {\n        if (valid) {\n          let saveForm = {};\n          switch (formType){\n            case 'flyjForm'://新增分类依据\n              saveForm = {...saveForm,...this.flyjForm};\n              this.blgkmsList.forEach(item=>{\n                if(item.value === saveForm.ms){\n                  saveForm.ms = item.label;\n                }\n              })\n              this.lxList.forEach(item=>{\n                if(item.value === saveForm.type){\n                  saveForm.type = item.label;\n                }\n              })\n              break;\n            case 'blgkmsForm'://新增不良工况描述\n              saveForm = {...saveForm,...this.blgkmsForm};\n              this.lxList.forEach(item=>{\n                if(item.value === saveForm.type){\n                  saveForm.type = item.label;\n                }\n              })\n              break;\n            case 'lxForm'://新增不良工况类型\n              saveForm = {...saveForm,...this.lxForm};\n              break;\n            default:\n              break;\n          }\n          saveOrUpdate(saveForm).then(res=>{\n            if (res.code === '0000') {\n              this.$message.success('操作成功');\n              this.isShowFlyj = false;\n              this.isShowBlgkMs = false;\n              this.isShowDetail = false;\n              this.isShowLx = false;\n              this.getData();\n              //如果是新增类型，保存后需要重新刷新左侧树列表,及其他新增弹框中的设备类型选项\n              if(formType == 'lxForm'){\n                this.getTreeData();\n                //设备类型下拉框\n                this.getSblxList();\n              }\n              //关闭弹框\n            } else {\n              this.$message.error('操作失败')\n            }\n          });\n        } else {\n          this.$message.error('校验未通过！')\n          return false\n        }\n      })\n    },\n    //关闭\n    closeFun(type){\n      this.isShowDetail = false;\n      switch (type){\n        case 'lx':\n          this.isShowLx = false;\n          break;\n        case 'blgkms':\n          this.isShowBlgkMs = false;\n          break;\n        case 'flyj':\n          this.isShowFlyj = false;\n          break;\n        case 'view':\n          this.isShowDetail = false;\n          break;\n        default:\n          this.isShowLx = false;\n          this.isShowBlgkMs = false;\n          this.isShowFlyj = false;\n          this.isShowDetail = false;\n          break;\n      }\n    },\n    //重置按钮\n    filterReset() {\n      this.queryParams = {qxlb:this.qxlb};//重置条件\n    },\n    getTreeData(){\n      getBlgkTree({zy:'bdsb'}).then(res=>{\n        this.treeOptions = res.data;\n      })\n    },\n    //树节点点击事件\n    handleNodeClick(node) {\n      this.treeNodeData = node;\n      if (node.identifier === '1') {//设备类型一级\n        this.queryParams.sblx = node.id;\n      } else{ //上一级\n        this.queryParams.sblx = '';\n      }\n      this.getData()\n    },\n    //查询列表\n    getData(params) {\n      this.load = true\n      this.queryParams = {...this.queryParams, ...params}\n      getData(this.queryParams).then(res => {\n        this.tableAndPageInfo.tableData = res.data.records\n        this.tableAndPageInfo.pager.total = res.data.total\n        this.load = false\n      })\n    },\n  }\n}\n</script>\n\n<style lang='scss' scoped>\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  height: 89vh;\n  max-height: 89vh;\n  overflow: auto;\n}\n/*给左侧数结构header加颜色*/\n.box-card .el-card__header {\n  background: #11ba6d !important;\n}\n.box-card {\n  margin:0;\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.tree {\n  overflow-y: hidden;\n  overflow-x: scroll;\n  width: 80px;\n  height: 500px;\n}\n\n.el-tree {\n  min-width: 100%;\n  display: inline-block !important;\n}\n/deep/ .el-dialog:not(.is-fullscreen) {\n  margin-top: 8vh !important;\n}\n</style>\n<style>\n\n</style>\n\n\n\n\n\n"], "sourceRoot": "src/views/dagang<PERSON>ilfield/blgk"}]}