{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js??ref--13-0!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\component\\userSelect.js", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\api\\component\\userSelect.js", "mtime": 1706897313868}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJEOi9TaGFtbXBvb2wvd29yay9jb2RlL2RneXQvMDFcdTRFRTNcdTc4MDEvcXotdWkvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0Iik7CgpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVwdFRyZWVTZWxlY3QgPSBkZXB0VHJlZVNlbGVjdDsKZXhwb3J0cy5ncm91cFRyZWVTZWxlY3QgPSBncm91cFRyZWVTZWxlY3Q7CmV4cG9ydHMuVXNlcnNCeURlcHRPckdyb3VwID0gVXNlcnNCeURlcHRPckdyb3VwOwoKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Cgp2YXIgYmFzZVVybCA9ICIvaXNjLWFwaSI7Ci8qKgogKiDmn6Xor6Lpg6jpl6jmoJEKICogQHBhcmFtIHF1ZXJ5CiAqIEByZXR1cm5zIHtQcm9taXNlIHwgUHJvbWlzZTxhbnk+fQogKi8KCmZ1bmN0aW9uIGRlcHRUcmVlU2VsZWN0KHF1ZXJ5KSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICIvc3lzdGVtL2RlcHRUcmVlL2RlcHRUcmVlU2VsZWN0IiwgcXVlcnksIDEpOwp9Ci8qKgogKiDmn6Xor6LmnYPpmZDmoJEKICogQHBhcmFtIGRhdGEKICogQHJldHVybnMge1Byb21pc2UgfCBQcm9taXNlPGFueT59CiAqLwoKCmZ1bmN0aW9uIGdyb3VwVHJlZVNlbGVjdChkYXRhKSB7CiAgcmV0dXJuIF9yZXF1ZXN0LmRlZmF1bHQucmVxdWVzdFBvc3QoYmFzZVVybCArICIvc3lzdGVtL2RlcHRUcmVlL2dyb3VwVHJlZVNlbGVjdCIsIGRhdGEsIDEpOwp9Ci8qKgogKiDmoLnmja7pg6jpl6jmiJbmnYPpmZDnu4Tmn6Xor6LkurrlkZgKICogQHBhcmFtIGRhdGEKICogQHJldHVybnMge1Byb21pc2UgfCBQcm9taXNlPGFueT59CiAqLwoKCmZ1bmN0aW9uIFVzZXJzQnlEZXB0T3JHcm91cChkYXRhLCBseCkgewogIHJldHVybiBfcmVxdWVzdC5kZWZhdWx0LnJlcXVlc3RQb3N0KGJhc2VVcmwgKyAiL3N5c3RlbS9kZXB0VHJlZS9Vc2Vyc0J5RGVwdE9yR3JvdXAvIiArIGx4LCBkYXRhLCAxKTsKfQ=="}, {"version": 3, "sources": ["D:/Shammpool/work/code/dgyt/01代码/qz-ui/src/api/component/userSelect.js"], "names": ["baseUrl", "deptTreeSelect", "query", "api", "requestPost", "groupTreeSelect", "data", "UsersByDeptOrGroup", "lx"], "mappings": ";;;;;;;;;;;AAAA;;AACA,IAAMA,OAAO,GAAG,UAAhB;AAEA;;;;;;AAKO,SAASC,cAAT,CAAwBC,KAAxB,EAA+B;AAEpC,SAAOC,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,iCAAxB,EAA0DE,KAA1D,EAAgE,CAAhE,CAAP;AACD;AAED;;;;;;;AAKO,SAASG,eAAT,CAAyBC,IAAzB,EAA+B;AACpC,SAAOH,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,kCAAxB,EAA2DM,IAA3D,EAAgE,CAAhE,CAAP;AACD;AAED;;;;;;;AAKO,SAASC,kBAAT,CAA4BD,IAA5B,EAAiCE,EAAjC,EAAqC;AAC1C,SAAOL,iBAAIC,WAAJ,CAAgBJ,OAAO,GAAC,sCAAR,GAA+CQ,EAA/D,EAAkEF,IAAlE,EAAuE,CAAvE,CAAP;AACD", "sourcesContent": ["import api from '@/utils/request'\nconst baseUrl = \"/isc-api\";\n\n/**\n * 查询部门树\n * @param query\n * @returns {Promise | Promise<any>}\n */\nexport function deptTreeSelect(query) {\n\n  return api.requestPost(baseUrl+\"/system/deptTree/deptTreeSelect\",query,1)\n}\n\n/**\n * 查询权限树\n * @param data\n * @returns {Promise | Promise<any>}\n */\nexport function groupTreeSelect(data) {\n  return api.requestPost(baseUrl+\"/system/deptTree/groupTreeSelect\",data,1)\n}\n\n/**\n * 根据部门或权限组查询人员\n * @param data\n * @returns {Promise | Promise<any>}\n */\nexport function UsersByDeptOrGroup(data,lx) {\n  return api.requestPost(baseUrl+\"/system/deptTree/UsersByDeptOrGroup/\"+lx,data,1)\n}\n"]}]}