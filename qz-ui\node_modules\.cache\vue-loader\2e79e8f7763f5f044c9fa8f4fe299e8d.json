{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\gswh_edit.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\gswh_edit.vue", "mtime": 1706897323217}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["gswh_edit.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "gswh_edit.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk", "sourcesContent": ["<template>\n  <el-row :gutter=\"16\">\n    <!--  原始公式  -->\n    <el-col :span=\"24\">\n      <div>\n        <el-button type=\"primary\" @click=\"check_jb\">验证</el-button>\n        <el-button type=\"primary\" @click=\"save_jb\">保存</el-button>\n        <!--        <el-button type=\"primary\" @click=\"init_jb\">初始化</el-button>-->\n        <el-button type=\"primary\" @click=\"clear_jb\">清空</el-button>\n      </div>\n    </el-col>\n    <el-col :span=\"24\">\n      <div class=\"row\" style=\"border:1px;\" id=\"atChat\">\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' && ')\">&&</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' || ')\">||</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' > ')\">></a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' >= ')\">>=</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' < ')\"><</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' <= ')\"><=</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' == ')\">==</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' != ')\">!=</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' Math.abs() ')\">abs</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' ( ')\">(</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' ) ')\">)</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' { ')\">{</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick(' } ')\">}</a>\n        <a class=\"btn\" href=\"#\" role=\"button\" @click=\"btnClick('elseIf')\">else if</a>\n      </div>\n    </el-col>\n\n    <!--  规则解释  -->\n    <el-col :span=\"24\">\n      <el-form :model=\"editForm\" ref=\"editForm\">\n        <el-row>\n          <el-col :span=\"24\">\n            <el-form-item prop=\"gs\">\n              <el-input id=\"jb_text\" name=\"pptjms\" type=\"textarea\" :rows=\"10\" v-model=\"editForm.gs\" placeholder=\"请输入公式\"\n                        v-on:input.native=\"jb_show\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <div>\n              <span>规则解释</span>\n            </div>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item prop=\"gzjs\">\n              <el-input id=\"jb_text_show\" name=\"pptjms\" type=\"textarea\" :rows=\"10\" v-model=\"editForm.gzjs\" disabled/>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n    </el-col>\n  </el-row>\n</template>\n\n<script>\nimport {verifyexpression} from '@/api/dagangOilfield/bzgl/sbztpjbzk/jbwh'\n\nexport default {\n  name: 'gswh_edit',\n  props: {\n    jb: {\n      type: String,\n      default: ''\n    },\n    tableData: {\n      type: Array,\n      default: ''\n    },\n  },\n  data() {\n    return {\n      editForm: {\n        gs: '',//公式\n        gzjs: '',//规则解释\n      },\n      checkJB: false,//是否验证脚本\n      jbVal: '',//返回给组件中的脚本字符串\n      parentMap: new Map(),\n    };\n  },\n  watch: {\n    tableData: {\n      handler(newVal, oldVal) {\n        newVal.forEach(item => {\n          let rowindex = parseInt(item.rowindex)+1;\n          let colindex = parseInt(item.colindex)+1;\n          this.parentMap.set(item.objId,rowindex+\"行\"+colindex+\"列\");\n        })\n        this.jb_show();\n      },\n      deep: true,\n      immediate: true\n    },\n    jb: {\n      handler(newVal, oldVal) {\n        this.editForm.gs = newVal;\n        this.jb_show();\n      },\n      deep: true,\n      immediate: true\n    }\n  },\n  mounted() {\n  },\n  methods: {\n    //放置光标数据\n    trTableClick(str) {\n      let obj = document.getElementById('jb_text');\n      if (document.selection) {\n        let sel = document.selection.createRange();\n        sel.text = str;\n      } else if (typeof obj.selectionStart === 'number' && typeof obj.selectionEnd === 'number') {\n        let startPos = obj.selectionStart;\n        let endPos = obj.selectionEnd;\n        let cursorPos = startPos;\n        let tmpStr = obj.value;\n        obj.value = tmpStr.substring(0, startPos) + str + tmpStr.substring(endPos, tmpStr.length);\n        this.editForm.gs = obj.value;//设置公式值\n        cursorPos += str.length;\n        obj.selectionStart = obj.selectionEnd = cursorPos;\n      } else {\n        obj.value += str;\n        this.editForm.gs = obj.value;//设置公式值\n      }\n      this.jb_show();\n    },\n    //绑定a标签点击事件\n    async btnClick(val) {\n      await this.trTableClick(val == 'elseIf' ? \"\\nelse if()\\n{\\n    return    ;\\n}\" : val);\n    },\n    //脚本翻译\n    jb_show() {\n      let ruleScriptStr = this.editForm.gs;\n      ruleScriptStr = ruleScriptStr.replace(/else if/g, \"如果\")\n          .replace(/if/g, \"如果\")\n          .replace(/else/g, \"否则\")\n          .replace(/getParameter/g, \"参数值\")\n          .replace(/getColValue/g, \"参数值\")\n          .replace(/getXxdData/g, \"信息点值\")\n          .replace(/getZxXxdData/g, \"字信息点值\")\n          .replace(/>=/g, \"大于等于\")\n          .replace(/>/g, \"大于\")\n          .replace(/<=/g, \"小于等于\")\n          .replace(/</g, \"小于\")\n          .replace(/==/g, \"等于\")\n          .replace(/!=/g, \"不等于\")\n          .replace(/\\|\\|/g, \"或者\")\n          .replace(/&&/g, \"并且\")\n          .replace(/return/g, \"返回\")\n          .replace(/SubItemValue/g, \"单元格值\")\n          .replace(/min/g, \"多个单元格最小值\")\n          .replace(/max/g, \"多个单元格最大值\")\n          .replace(/avg/g, \"多个单元格平均值\")\n          .replace(/(Math.abs)\\s*\\(/g, \"绝对值(\");\n\n      if (this.parentMap) {\n        this.parentMap.forEach((val, key) => {\n          if (ruleScriptStr.includes(key)){\n            this.$(\"#\"+key).css('backgroundColor','red')\n          }\n          else {\n            this.$(\"#\"+key).css('backgroundColor','white')\n          }\n          ruleScriptStr = ruleScriptStr.replaceAll(key, val);\n        })\n      }\n      this.editForm.gzjs = ruleScriptStr;\n    },\n    //脚本验证\n    async check_jb() {\n      this.checkJB = false;\n      let {code, data} = await verifyexpression(this.editForm.gs)\n      if (code === '0000') {\n        if (data) {\n          this.checkJB = true;\n          this.$message.success(\"脚本执行成功\")\n        } else {\n          this.$message.error(\"脚本定义错误\")\n          this.checkJB = false;\n        }\n      }\n    },\n    //脚本保存\n    save_jb() {\n      if (this.checkJB) {\n        this.jbVal = this.editForm.gs;\n        this.$emit('setJbVal', this.jbVal);//将脚本的值传递给父页面\n        this.$emit('jbClose');//关闭脚本弹框\n\n      } else {\n        this.$message({\n          type: 'error',\n          message: '脚本没有验证或脚本定义错误，请进行验证后或定义正确脚本在保存！'\n        })\n      }\n    },\n    //清空脚本\n    clear_jb() {\n      this.checkJB = false;\n      this.editForm.gs = \"\";\n      this.jb_show();\n    },\n    //初始化\n    init_jb() {\n      this.checkJB = false;\n      this.editForm.gs = \"if()\\n{\\n    return    1;\\n}\\nelse\\n{\\n    return     0;\\n}\";\n      this.jb_show();\n    },\n  },\n}\n</script>\n\n<style scoped lang=\"scss\">\n.row {\n  padding-top: 20px;\n  padding-bottom: 20px;\n  font-size: 20px;\n}\n\n.btn {\n  padding: 14px;\n\n  &:hover {\n    color: #00c39a;\n  }\n}\n</style>\n"]}]}