<template>
  <div :sbInfoSpanNum="sbInfoSpanNum" class="borderCls" :class="sbIDivClass">
    <div style="height: 90%">
      <div class="txtTitle">
        <span class="txtContent">设备基本信息</span>
      </div>
      <div class="sbInfo_cont">
        <ul>
          <li>
            <i class="icon bd"></i>变电
          </li>
          <li class="cont_child"><span class="fontCls">变电站：</span><span>个</span><span>{{this.bdzNum}}</span></li>
          <li class="cont_child"><span class="fontCls">变压器：</span><span>个</span><span>{{this.byqNum}}</span></li>
<!--          <li class="cont_child"><span>总容量：</span><span>个</span><span>336</span></li>-->
        </ul>
        <ul>
          <li><i class="icon line"></i>线路</li>
          <li class="cont_child"><span class="fontCls">线路数：</span><span>条</span><span>{{this.xlNum}}</span></li>
          <li class="cont_child"><span class="fontCls">长度：</span><span>Km</span><span>{{this.xlcd}}</span></li>
          <li class="cont_child gts"><span class="fontCls">杆塔数：</span><span>个</span><span>{{this.gtNum}}</span></li>
          <li class="cont_child gts"><span class="fontCls">电缆长度：</span><span>Km</span><span>{{this.gtcd}}</span></li>
        </ul>
        <ul>
          <li><i class="icon pds"></i>配电室</li>
          <li class="cont_child"><span class="fontCls">配电室：</span><span>座</span><span>{{this.pdsNum}}</span></li>
          <li class="cont_child"><span class="fontCls">配电柜：</span><span>个</span><span>{{this.pdgNum}}</span></li>
        </ul>
        <ul>
          <li>
            <i class="icon bd"></i>新能源
          </li>
          <li class="cont_child"><span class="fontCls">光伏电站：</span><span>个</span><span>{{this.gfdzNum}}</span></li>
        </ul>
      </div>
<!--      <div  ref="gdchart" class="tjHeight">
      </div>-->
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { mapState } from 'vuex'
import {getSbInfo} from '@/api/index/SbInfo'

export default {
  name: 'SbInfo',//工单完成情况
  props:{
    sbInfoSpanNum:{
      type:Number,
      default:7,
    },
    sbIDivClass:'',
  },
  data() {
    return {
      //用于布局动态设置高度
      activeClass:1,
      tjCharts:null,//统计图对象
      //默认值
      fpNum:[302,120,278],
      finNum:[150,100,234],
      bdzNum:null,//变电站
      byqNum:null,//变压器
      xlcd:null,//线路长度
      xlNum:null,//线路数量
      gtNum:null,//杆塔数量
      gtcd:null,//电缆长度
      pdsNum:null,//配电室数量
      pdgNum:null,//配电柜数量
    }
  },
  mounted() {
   // this.showGdCharts();
   this.showSbInfo();
  },
  methods: {
    //工单完成情况
    showGdCharts(){
      let bar_dv = this.$refs.gdchart;
      let myChart = echarts.init(bar_dv);
      this.tjCharts = myChart;

      let option;
      option = {
        title: {
          subtext: '单位：个',
          left: '4%'
        },
        legend: {
          top:'3%',
          right: '6%',
          textStyle:{
            fontSize:16,
          }
        },
        grid: {
          left: '4%',
          right: '4%',
          bottom: '1%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data:['变电', '线路', '配电']
        },
        yAxis: {},
        series: [
          {
            type: 'bar' ,
            name:'分配工单数',
            barWidth:24,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(
                0, 0, 0, 1,
                [
                  {offset: 0, color: '#11ba6d'},
                  {offset: 1, color: '#b5e0cd'}
                ]
              )
            },
            data:this.fpNum,
          },
          {
            type: 'bar',
            name:'已完成工单数',
            barWidth:24,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(
                0, 0, 0, 1,
                [
                  {offset: 0, color: '#ff9a09'},
                  {offset: 1, color: '#faead3'}
                ]
              )
            },
            data:this.finNum
          }
        ]
      };
      option && myChart.setOption(option);
    },
    //重新加载eCharts图表
    reloadCharts(){
      this.tjCharts.resize();
    },
    async showSbInfo(){
      let {data,code} = await getSbInfo()
      this.bdzNum=data.BdzTj//变电站
      this.byqNum=data.PdByqTj,//变压器
      this.xlcd= Number(data.cd).toFixed(2),//线路长度
      this.xlNum=data.xl,//线路数量
      this.gtNum=data.tower,//杆塔数量
      this.gtcd=(data.dl/1000).toFixed(2),//电缆长度
      this.pdsNum=data.pdz,//配电室数量
      this.pdgNum=data.pdg//配电柜数量
      this.gfdzNum=data.gfdzTj//光伏电站数量
    }
  },
  computed: {
    ...mapState(["settings","app"]),
    //工作票完成情况
    workOrder() {
      return this.$store.state.settings.workOrder;
    },
    //菜单伸缩状态
    opened() {
      return this.$store.state.app.sidebar.opened;
    },
  },
  watch:{
    workOrder(newVal){
      if(newVal){
        this.reloadCharts();
      }
    },
    wkoSpanNum(newVal){
      this.reloadCharts();
    },
    opened(newVal) {
      //重新加载统计图
      setTimeout(()=>{
        //this.tjCharts.resize();
      },200)
    }
  }
}
</script>
<style scoped lang="scss">
.fontCls{
  font-weight: 800;
}
.spanTxt{
  float: right;
  padding-right: 4%;
  font-size: 16px;
  color: #b1b1b1;
  font-weight: 500;
}
.sbInfo_cont{
  height: 100%;
  padding:0 0 12px 12px;
  .icon{
    display: inline-block;
    width: 17px;
    height: 17px;
    margin-right: 10px;
  }
  .bd{
    background: url('../../assets/image/equ_icon1.png') no-repeat;
    background-size: 100% 100%;
  }
  .line{
    background: url('../../assets/image/equ_icon2.png') no-repeat;
    background-size: 100% 100%;
  }
  .pds{
    background: url('../../assets/image/equ_icon3.png') no-repeat;
    background-size: 100% 100%;
  }
  ul{
    list-style-type: none;
    margin:0;
    padding:0;
    li:first-child{
      line-height: 37px;
      font-weight: 600;
      color: #4d4d4d;
      font-size: 18px;
    }
  }
  .cont_child{
    color: #b1b1b1;
    width: 46%;
    height: 30px;
    line-height: 30px;
    display: inline-block;
    margin-right: 15px;
    padding: 0 4px;
    box-shadow: #e7e0e0 0.5px 0.5px 0.5px 1px;
    span:first-child{
      float: left;
     /* width: 80px;*/
    }
    span:nth-child(2){
      float:right;
      margin-right: 0;
  /*    max-width: 26px;*/
    }
    span:last-child{
      color:#4d4d4d;
      font-size: 25px;
      float: right;
/*      max-width: 89px;*/
    }
  }
  .gts{
    margin-top:10px
  }
}
.txtContent{
  width: 142px;
}
</style>

