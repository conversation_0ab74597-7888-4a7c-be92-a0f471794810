{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\lpbzk\\xlyxbzk.vue?vue&type=style&index=0&id=baabe70a&scoped=true&lang=scss&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\lpbzk\\xlyxbzk.vue", "mtime": 1706897322614}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5jYXJkMSB7CiAgbWFyZ2luLWJvdHRvbTogNnB4Owp9Cgouc2VhcmNoLWNvbmRpdGlvbiB7CiAgLy8gcGFkZGluZzogMjBweDsKICBmb250LXNpemU6IDEzcHg7CiAgY29sb3I6ICM5YzljOWM7CgogIC5lbC1zZWxlY3QgewogICAgLmVsLWlucHV0IHsKICAgICAgd2lkdGg6IDEwMCU7CiAgICB9CiAgfQoKICAuZWwtY29sIHsKICAgIHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7CiAgICBsaW5lLWhlaWdodDogMzJweDsKICAgIHRleHQtYWxpZ246IGxlZnQ7CiAgfQp9Cg=="}, {"version": 3, "sources": ["xlyxbzk.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkdA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "xlyxbzk.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/lpbzk", "sourcesContent": ["<template>\n  <el-row class=\"app-container\">\n    <el-row>\n      <!--基本信息查询及显示-->\n      <el-col :span=\"24\">\n        <!--搜索条件-->\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          @handleReset=\"filterReset\"\n          :width=\"{ labelWidth: 150, itemWidth: 160 }\"\n        />\n      </el-col>\n    </el-row>\n\n    <el-row>\n      <el-col>\n        <el-white class=\"button-group\">\n          <div class=\"button_btn\">\n            <el-button type=\"primary\" v-hasPermi=\"['bzxlstbzk:button:add']\" icon=\"el-icon-plus\" @click=\"addRow\">新增</el-button>\n            <el-button type=\"success\" icon=\"el-icon-download\" @click=\"exportExcel\">导出</el-button>\n          </div>\n          <comp-table :table-and-page-info=\"tableAndPageInfo\" @update:multipleSelection=\"handleSelectionChange\" height=\"65vh\" v-loading=\"loading\">\n          <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\" width=\"160\"\n                           :resizable=\"false\">\n            <template slot-scope=\"scope\">\n              <el-button @click=\"updateDetails(scope.row)\" v-hasPermi=\"['bzxlstbzk:button:update']\" type=\"text\" size=\"small\" title=\"修改\"  class='el-icon-edit'></el-button>\n              <el-button @click=\"getDetails(scope.row)\" type=\"text\" size=\"small\" title=\"详情\" class=\"el-icon-view\"></el-button>\n              <el-button type=\"text\" title=\"删除\" v-if=\"scope.row.createBy === $store.getters.name\" v-hasPermi=\"['bzxlstbzk:button:delete']\" icon=\"el-icon-delete\" @click=\"handleDelete(scope.row.objId)\"></el-button>\n            </template>\n          </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!-- 新增、详情弹出对话框 -->\n    <el-dialog :title=title :visible.sync=\"isShowDetails\" width=\"50%\"  v-dialogDrag>\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"巡视类型：\" prop=\"xslx\">\n              <el-select filterable style=\"width: 100%\" v-model=\"form.xslx\" ref=\"xslx\" :disabled=\"isDisabled\" placeholder=\"请选择巡视类型\" @change=\"xslxChange\">\n                <el-option\n                  v-for=\"item in xslxList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"设备类别：\" prop=\"sblb\">\n              <el-select filterable style=\"width: 100%\" v-model=\"form.sblb\" :disabled=\"isDisabled\" placeholder=\"请选择设备类别\">\n                <el-option\n                  v-for=\"item in sblbList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"电压等级：\" prop=\"dydj\">\n              <el-select filterable style=\"width: 100%\" v-model=\"form.dydj\" ref=\"dydj\" :disabled=\"isDisabled\" placeholder=\"请选择电压等级\">\n                <el-option\n                  v-for=\"item in dydjList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"杆塔性质：\" prop=\"gtcz\">\n              <el-select :disabled=\"isDisabled\" style=\"width: 100%\" v-model=\"form.gtcz\" placeholder=\"请选择杆塔性质\">\n                <el-option\n                  v-for=\"item in gtczList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\">\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row :gutter=\"8\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"巡检项目：\" prop=\"xjxm\">\n              <el-input :disabled=\"isDisabled\" v-model=\"form.xjxm\" placeholder=\"请输入巡检项目\"></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"巡检标准：\" prop=\"xjbz\">\n              <el-input :disabled=\"isDisabled\" type=\"textarea\" v-model=\"form.xjbz\" placeholder=\"请输入巡检标准\"></el-input>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n      </el-form>\n\n      <div slot=\"footer\">\n        <el-button @click=\"uploadClose\">取 消</el-button>\n        <el-button v-if=\"title=='新增线路巡检标准库' || title=='线路运行标准库编辑'\" type=\"primary\" @click=\"saveXlyxbzk\">保 存</el-button>\n      </div>\n\n    </el-dialog>\n\n  </el-row>\n</template>\n\n<script>\n  import {\n    getListXlyxbzk,\n    saveOrUpdateXlyxbzk,\n    removeXlyxbzk,\n  } from '@/api/dagangOilfield/bzgl/lpbzk/xlyxbzk'\n  import {getDictTypeData} from \"@/api/system/dict/data\";\n  import {exportExcel} from '@/api/bzgl/ysbzk/ysbzk'\n\n  export default {\n    name: 'xlyxbzk',\n    data() {\n      return {\n        loading: true,\n        //详情弹框是否显示\n        isShowDetails: false,\n        // 多选框选中的id\n        ids: [],\n        //是否禁用\n        isDisabled: false,\n        form: {},\n        //查询参数\n        queryParams: {\n          pageSize: 10,\n          pageNum: 1,\n          xslx: '',\n          sblb:'',\n          dydj:'',\n          gtcz:'',\n          xjbz:''\n        },\n        //详情对话框标题\n        title: '',\n        // 数据条数\n        total: 0,\n        //巡视类型list\n        xslxList: [\n          {lable: '精细巡视', value: '精细巡视'},\n          {label: '电缆巡视', value: '电缆巡视'},\n          {label: '特殊巡视', value: '特殊巡视'},\n          {label: '通道巡视', value: '通道巡视'}\n        ],\n        //设备类别list\n        sblbList: [\n          {label: '杆塔', value: '杆塔'},\n          {label: '变压器台及箱变', value: '变压器台及箱变'},\n          {label: '电缆分接箱', value: '电缆分接箱'},\n          {label: '高压电缆', value: '高压电缆'}\n        ],\n        //电压等级list\n        dydjList: [\n          {label: '6kV', value: '6kV'},\n          {label: '35kV', value: '35kV'},\n          {label: '110kV', value: '110kV'}\n        ],\n        //杆塔材质list\n        gtczList: [\n          {label: '直线', value: '直线'},\n          {label: '耐涨', value: '耐涨'},\n          {label: '转角', value: '转角'},\n          {label: 'T接', value: 'T接'},\n          {label: '电缆T接', value: '电缆T接'},\n          {label: '终端', value: '终端'},\n          {label: '电缆终端', value: '电缆终端'},\n          {label: '变压器,箱变', value: '变压器,箱变'},\n          {label: '转角,耐涨,终端,电缆终端', value: '转角,耐涨,终端,电缆终端'}\n        ],\n        /**\n         * 无人站巡视计划\n         */\n        filterInfo: {\n          data: {\n            xslx: '',\n            sblb:'',\n            dydj:'',\n            gtcz:'',\n            xjbz:''\n          },\n          fieldList: [\n\n            {\n              label: '设备类别', type: 'select', value: 'sblb',\n              options: [\n                {label: '杆塔', value: '杆塔'},\n                {label: '变压器台及箱变', value: '变压器台及箱变'},\n                {label: '电缆分接箱', value: '电缆分接箱'},\n                {label: '高压电缆', value: '高压电缆'}]\n              ,clearable: true\n            },\n            {\n              label: '杆塔性质', type: 'select', value: 'gtcz',\n              options: [\n                {label: '直线', value: '直线'},\n                {label: '耐涨', value: '耐涨'},\n                {label: '转角', value: '转角'},\n                {label: 'T接', value: 'T接'},\n                {label: '电缆T接', value: '电缆T接'},\n                {label: '终端', value: '终端'},\n                {label: '电缆终端', value: '电缆终端'},\n                {label: '变压器,箱变', value: '变压器,箱变'},\n                {label: '转角,耐涨,终端,电缆终端', value: '转角,耐涨,终端,电缆终端'}]\n              ,clearable: true\n            },\n            { label: '巡检标准', value: 'xjbz', type: 'input',},\n            {\n              label: '电压等级', type: 'checkbox', value: 'dydj',\n              checkboxValue: [],\n              options: [\n                {label: '6kV', value: '6kV'},\n                {label: '35kV', value: '35kV'},\n                {label: '110kV', value: '110kV'}]\n              ,clearable: true\n            },\n            {\n              label: '巡视类型', type: 'checkbox', value: 'xslx',\n              checkboxValue: [],\n              options: [\n                {label: '精细巡视', value: '精细巡视'},\n                {label: '电缆巡视', value: '电缆巡视'},\n                {label: '特殊巡视', value: '特殊巡视'},\n                {label: '通道巡视', value: '通道巡视'}]\n              ,clearable: true\n            },\n\n          ]\n        },\n        tableAndPageInfo: {\n          pager: {\n            pageSize: 10,\n            pageNum: 1,\n            total: 0,\n            sizes: [10, 20, 50, 100]\n          },\n          option: {checkBox: true, serialNumber: true},\n          tableData: [],\n          tableHeader: [\n            {prop: 'xslx', label: '巡视类型', minWidth: '100'},\n            {prop: 'sblb', label: '设备类别', minWidth: '120'},\n            {prop: 'dydj', label: '电压等级', minWidth: '100'},\n            {prop: 'gtcz', label: '杆塔性质', minWidth: '120'},\n            {prop: 'xjxm', label: '巡检项目', minWidth: '120'},\n            {prop: 'xjbz', label: '巡检标准', minWidth: '160'},\n        /*    {\n              prop: 'operation',\n              label: '操作',\n              fixed: 'right',\n              minWidth: '130px',\n              style: {display: 'block'},\n              operation: [\n                {name: '编辑', clickFun: this.updateDetails},\n                {name: '详情', clickFun: this.getDetails}\n              ]\n            }*/\n          ]\n        },\n        xslxbmMap:new Map(),//巡视类型编码\n        // 表单校验\n        rules: {\n          xslx: [\n            {required: true, message: \"请选择巡视类型\", trigger: \"select\"},\n          ],\n          sblb: [\n            {required: true, message: \"请选择设备类别\", trigger: \"select\"},\n          ],\n          dydj: [\n            {required: true, message: \"请选择电压等级\", trigger: \"select\"},\n          ],\n          gtcz: [\n            {required: true, message: \"请选择杆塔材质\", trigger: \"select\"},\n          ],\n          xjxm: [\n            {required: true, message: \"请输入巡检项目\", trigger: \"blur\"},\n          ],\n          xjbz: [\n            {required: true, message: \"请输入巡检标准\", trigger: \"blur\"},\n          ],\n        },\n      }\n    },\n    created() {\n      //列表查询\n      this.getData();\n      this.getXslxbmList();\n    },\n    watch: {\n    isShowDetails(val) {\n      if (val) {\n        const el = this.$el.querySelector('.el-dialog')\n        el.style.left = 0\n        el.style.top = 0\n      }\n    }\n  },\n    methods: {\n      //获取巡视类型编码\n      getXslxbmList(){\n        getDictTypeData('xlxsxm-xslxbm').then(res=>{\n          res.data.forEach(item=>{\n            this.xslxbmMap.set(item.label,item.value);\n          })\n        })\n      },\n      xslxChange(val){\n        this.form.xslxCode = this.xslxbmMap.get(val);\n      },\n      /**\n       * 根据表格名称获取对应的数据\n       */\n      //查询列表\n      getData(params) {\n        this.loading = true\n        this.queryParams={...this.queryParams,...params}\n        //参数合并\n        const param = {...this.queryParams, ...params}\n        getListXlyxbzk(param).then(res => {\n          console.log(res)\n          this.tableAndPageInfo.tableData = res.data.records\n          this.tableAndPageInfo.pager.total = res.data.total\n          this.loading = false\n        });\n      },\n      /**\n       * 新增按钮\n       */\n      addRow() {\n        this.title = '新增线路巡检标准库'\n        this.isDisabled = false\n        this.form = {}\n        this.isShowDetails = true\n      },\n      /**\n       * 详情按钮\n       */\n      getDetails(row) {\n        this.form = {...row}\n        this.isDisabled = true\n        this.isShowDetails = true\n        this.title = '线路运行标准库详情'\n      },\n      //修改按钮\n      updateDetails(row) {\n        this.title = '线路运行标准库编辑';\n        //显示取消确认按钮\n        this.isShow = true;\n        //禁用表单\n        this.isDisabled = false;\n        //打开弹窗\n        this.isShowDetails = true;\n        this.form = {...row};\n      },\n      filterReset(val) {\n      this.filterInfo.fieldList.forEach(item=>{\n        if(item.type === 'checkbox'){\n          item.checkboxValue = [];\n        }\n      })\n    },\n      /**\n       * 删除按钮\n       */\n      async handleDelete(id) {\n        // if (this.ids.length < 1) {\n        //   this.$message.warning('请选择正确的数据！！！')\n        //   return\n        // }\n        let obj=[];\n        obj.push(id);\n        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }).then(() => {\n          removeXlyxbzk(obj).then(({ code }) => {\n            if (code === '0000') {\n              this.$message({\n                type: 'success',\n                message: '删除成功!'\n              })\n              //重置page页从1开始\n              this.tableAndPageInfo.pager.pageResize = 'Y'\n              this.getData()\n            } else {\n              this.$message({\n                type: 'error',\n                message: '删除失败!'\n              })\n            }\n          })\n        }).catch(() => {\n          this.$message({\n            type: 'info',\n            message: '已取消删除'\n          })\n        })\n      },\n\n      /**\n       * 提交表单\n       */\n      async saveXlyxbzk() {\n        this.$refs['form'].validate((valid) => {\n          if (valid) {\n            try {\n              saveOrUpdateXlyxbzk(this.form).then(res=>{\n                if (res.code === '0000') {\n                  this.$message.success('操作成功')\n                  this.getData()\n                }\n                this.isShowDetails = false\n              })\n            } catch (e) {\n            }\n          }else{\n            this.$message.error(\"校验未通过！\");\n            return false;\n          }\n        })\n      },\n      //取消按钮\n      uploadClose() {\n        this.isShowDetails = false;\n      },\n      /**\n       * 多选款选中数据\n       * @param row\n       */\n      handleSelectionChange(selection) {\n        this.ids = selection.map(item => item.objId)\n        this.single = selection.length !== 1\n        this.multiple = !selection.length\n        this.selectData = selection\n      },\n      //导出excel\n      exportExcel() {\n        // if(!this.selectData.length > 0){\n        //   this.$message.warning('请在左侧勾选要导出的数据')\n        //   return\n        // }\n        let fileName = \"线路巡视项目配置\";\n        let exportUrl = \"/xlxjbz\";\n        exportExcel(exportUrl, this.queryParams, fileName);\n      }\n    }\n  }\n</script>\n\n<style scoped lang=\"scss\">\n  .card1 {\n    margin-bottom: 6px;\n  }\n\n  .search-condition {\n    // padding: 20px;\n    font-size: 13px;\n    color: #9c9c9c;\n\n    .el-select {\n      .el-input {\n        width: 100%;\n      }\n    }\n\n    .el-col {\n      vertical-align: middle;\n      line-height: 32px;\n      text-align: left;\n    }\n  }\n</style>\n\n"]}]}