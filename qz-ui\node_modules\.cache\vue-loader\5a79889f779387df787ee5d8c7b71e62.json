{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sbbgsq.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\dwzygl\\sbbgsq.vue", "mtime": 1706897324964}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsKaW1wb3J0IHsKICBnZXRMaXN0LAogIHJlbW92ZSwKICBzYXZlT3JVcGRhdGUsCiAgZG93bmxvYWRCeUZpbGVJZAp9IGZyb20gIkAvYXBpL2RhZ2FuZ09pbGZpZWxkL2Fzc2V0L3NiYmdzcSI7CmltcG9ydCBDb21wVGFibGUgZnJvbSAiY29tL0NvbXBUYWJsZSI7CmltcG9ydCBFbEZpbHRlciBmcm9tICJjb20vRWxGaWx0ZXIiOwppbXBvcnQgeyBmb3JtYXR0ZXJEYXRlVGltZSB9IGZyb20gIkAvdXRpbHMvaGFuZGxlRGF0YSI7Ci8v5rWB56iLCmltcG9ydCBhY3Rpdml0aSBmcm9tICJjb20vYWN0aXZpdGlfc2JiZ3NxIjsKaW1wb3J0IHRpbWVMaW5lIGZyb20gImNvbS90aW1lTGluZSI7CmltcG9ydCB7IEhpc3RvcnlMaXN0IH0gZnJvbSAiQC9hcGkvYWN0aXZpdGkvcHJvY2Vzc1Rhc2siOwppbXBvcnQgYXBpIGZyb20gIkAvdXRpbHMvcmVxdWVzdCI7CmltcG9ydCB7IGdldE9yZ2FuaXphdGlvblNlbGVjdGVkIH0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYXNzZXQvYmRzYnR6IjsKaW1wb3J0IEVsSW1hZ2VWaWV3ZXIgZnJvbSAiZWxlbWVudC11aS9wYWNrYWdlcy9pbWFnZS9zcmMvaW1hZ2Utdmlld2VyIjsKaW1wb3J0IHsgZ2V0RmdzT3B0aW9ucyB9IGZyb20gIkAvYXBpL3l4Z2wvYmR5eGdsL3piZ2wiOwpleHBvcnQgZGVmYXVsdCB7CiAgY29tcG9uZW50czogeyBDb21wVGFibGUsIEVsRmlsdGVyLCBhY3Rpdml0aSwgdGltZUxpbmUsIEVsSW1hZ2VWaWV3ZXIgfSwKICBuYW1lOiAic2JiZ3NxIiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgcnVsZXM6IHsKICAgICAgICBiZ2x4OiBbCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36Y<PERSON><PERSON><PERSON>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"}, {"version": 3, "sources": ["sbbgsq.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6UA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "sbbgsq.vue", "sourceRoot": "src/views/dagangOilfield/dwzygl", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-filter\n      ref=\"filter1\"\n      :data=\"filterInfo.data\"\n      :field-list=\"filterInfo.fieldList\"\n      @handleReset=\"filterReset\"\n    />\n    <el-white class=\"button-group1\">\n      <div class=\"button_btn\">\n        <el-button\n          @click=\"addSensorButton\"\n          icon=\"el-icon-plus\"\n          type=\"primary\"\n          v-hasPermi=\"['sbbgsq:button:add']\"\n          >新增</el-button\n        >\n      </div>\n      <comp-table\n        ref=\"bdywgzrzTable\"\n        :table-and-page-info=\"tableAndPageInfo\"\n        @update:multipleSelection=\"selectChange\"\n        height=\"63vh\"\n      >\n        <el-table-column\n          slot=\"table_eight\"\n          align=\"center\"\n          fixed=\"right\"\n          style=\"display: block\"\n          label=\"操作\"\n          width=\"180\"\n          :resizable=\"false\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              @click=\"getGqjInfo(scope.row)\"\n              title=\"详情\"\n              class=\"el-icon-view\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"\n                scope.row.lczt === '1' &&\n                  $store.getters.name === scope.row.createBy\n              \"\n              @click=\"updateGqjInfo(scope.row)\"\n              title=\"编辑\"\n              class=\"el-icon-edit\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"\n                (scope.row.lczt === '1' &&\n                  $store.getters.name === scope.row.createBy) ||\n                  'admin' === $store.getters.name\n              \"\n              @click=\"deleteRow(scope.row.objId)\"\n              title=\"删除\"\n              class=\"el-icon-delete\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"scope.row.isStart === 1\"\n              @click=\"showTimeLine(scope.row)\"\n              title=\"流程查看\"\n              class=\"el-icon-lcck commonIcon\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              v-if=\"scope.row.isStart === 1\"\n              @click=\"showProcessImg(scope.row)\"\n              title=\"流程图\"\n              class=\"el-icon-lct commonIcon\"\n            ></el-button>\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              @click=\"handleDataFj(scope.row)\"\n              title=\"附件列表\"\n              class=\"el-icon-tickets\"\n            ></el-button>\n          </template>\n        </el-table-column>\n      </comp-table>\n    </el-white>\n    <el-dialog\n      :title=\"gqjTital\"\n      :visible.sync=\"dialogFormVisible\"\n      class=\"qxlr_dialog_insert\"\n      width=\"40%\"\n      v-dialogDrag\n    >\n      <el-form\n        :model=\"form\"\n        ref=\"form\"\n        label-width=\"80px\"\n        :disabled=\"isDisabled\"\n        :rules=\"rules\"\n      >\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\">\n            <el-form-item label=\"所属公司\" prop=\"ssgs\">\n              <el-select v-model=\"form.ssgs\" placeholder=\"\" disabled>\n                <el-option\n                  v-for=\"item in ssgsDataList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"申请人\" prop=\"sqr\">\n              <el-select v-model=\"form.sqr\" placeholder=\"\" disabled>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"变更类型\" prop=\"bglx\">\n              <el-select v-model=\"form.bglx\" placeholder=\"变更类型\" clearable>\n                <el-option\n                  v-for=\"item in bglxList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                ></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"变更说明\" prop=\"description\">\n              <el-input\n                type=\"textarea\"\n                :rows=\"5\"\n                v-model=\"form.description\"\n              ></el-input>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"8\">\n            <el-form-item label=\"申请时间\" prop=\"sqsj\">\n              <el-date-picker\n                v-model=\"form.sqsj\"\n                disabled\n                type=\"datetime\"\n                placeholder=\"选择日期\"\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\n                format=\"yyyy-MM-dd HH:mm:ss\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n\n          <el-col :span=\"24\">\n            <el-form-item label=\"图片信息：\" label-width=\"110px\">\n              <el-upload\n                action=\"\"\n                ref=\"uploadImg\"\n                accept=\".jpg,.png\"\n                :headers=\"header\"\n                :multiple=\"true\"\n                :on-change=\"handleChange\"\n                :data=\"uploadImgData\"\n                :file-list=\"imgList\"\n                :auto-upload=\"false\"\n                list-type=\"picture-card\"\n                :on-preview=\"handlePictureCardPreview\"\n                :on-progress=\"handleProgress\"\n                :on-remove=\"handleRemove\"\n              >\n                <i class=\"el-icon-plus\"></i>\n              </el-upload>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"24\">\n            <el-form-item label=\"附件上传：\" label-width=\"110px\">\n              <el-upload\n                :on-remove=\"handleFjRemove\"\n                class=\"upload-demo\"\n                action=\"\"\n                :headers=\"header\"\n                :on-change=\"handleFjUpload\"\n                :data=\"uploadImgData\"\n                :auto-upload=\"false\"\n                :before-upload=\"beforeFjUpload\"\n                :http-request=\"uoloadOtherFile\"\n                :file-list=\"fjUploadList\"\n              >\n                <el-button size=\"small\" type=\"primary\">点击上传</el-button>\n                <div slot=\"tip\" class=\"el-upload__tip\">文件大小不超过10MB</div>\n              </el-upload>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"qxcommit\"\n          v-if=\"!isDisabled\"\n          class=\"pmyBtn\"\n          >保存</el-button\n        >\n        <el-button\n          type=\"primary\"\n          @click=\"getSbFsBj('complete')\"\n          v-if=\"\n            isDisabled &&\n              form.lczt === '1' &&\n              $store.getters.name === form.createBy\n          \"\n          class=\"pmyBtn\"\n          >提交审核</el-button\n        >\n        <el-button\n          type=\"primary\"\n          @click=\"getSbFsBj('rollback')\"\n          v-if=\"\n            isDisabled &&\n              form.lczt === '2' &&\n              $store.getters.name === form.fgsshr\n          \"\n          class=\"pmyBtn\"\n          >回 退</el-button\n        >\n        <el-button\n          type=\"primary\"\n          @click=\"getSbFsBj('rollback')\"\n          v-if=\"\n            isDisabled &&\n              form.lczt === '3' &&\n              $store.getters.name === form.sckshr\n          \"\n          class=\"pmyBtn\"\n          >回 退</el-button\n        >\n        <el-button\n          type=\"primary\"\n          @click=\"getSbFsBj('complete')\"\n          v-if=\"\n            isDisabled &&\n              form.lczt === '2' &&\n              $store.getters.name === form.fgsshr\n          \"\n          class=\"pmyBtn\"\n          >提交审核</el-button\n        >\n        <el-button\n          type=\"primary\"\n          @click=\"getSbFsBj('complete')\"\n          v-if=\"\n            isDisabled &&\n              form.lczt === '3' &&\n              $store.getters.name === form.sckshr\n          \"\n          class=\"pmyBtn\"\n          >审核通过</el-button\n        >\n      </div>\n    </el-dialog>\n\n    <!--工作流需要-->\n    <activiti\n      ref=\"activiti\"\n      :processData=\"processData\"\n      :isShow=\"isShow\"\n      :option=\"activitiOption\"\n      @todoData=\"todoResult\"\n      @toClose=\"closeActiviti\"\n    ></activiti>\n    <time-line\n      :value=\"timeLineShow\"\n      :timeData=\"timeData\"\n      @closeTimeLine=\"colseTimeLine\"\n    />\n    <!--流程图查看-->\n    <el-dialog\n      title=\"审批流程图\"\n      :visible.sync=\"openLoadingImg\"\n      width=\"56%\"\n      append-to-body\n      :close-on-click-modal=\"false\"\n    >\n      <div>\n        <img :src=\"imgSrc\" style=\"width: 100%\" />\n      </div>\n    </el-dialog>\n    <!--图片放大展示弹出框-->\n    <el-image-viewer\n      v-if=\"imgDialogVisible\"\n      :on-close=\"onClose\"\n      :url-list=\"dialogImageUrl\"\n      zIndex=\"3000\"\n    />\n\n    <!--附件列表展示页面-->\n    <el-dialog title=\"附件信息\" :visible.sync=\"openFjDialog\" width=\"60%\">\n      <el-table :data=\"fjDataList\" style=\"width: 100%\" stripe border fit>\n        <el-table-column\n          prop=\"fileId\"\n          label=\"文件编号\"\n          align=\"center\"\n        ></el-table-column>\n        <el-table-column\n          prop=\"fileType\"\n          label=\"文件类型\"\n          align=\"center\"\n        ></el-table-column>\n        <el-table-column\n          prop=\"name\"\n          label=\"文件名称\"\n          align=\"center\"\n        ></el-table-column>\n        <el-table-column label=\"操作\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <el-button @click=\"downLoadFj(scope.row)\" type=\"text\" size=\"small\"\n              >下载</el-button\n            >\n          </template>\n        </el-table-column>\n      </el-table>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { getToken } from \"@/utils/auth\";\nimport {\n  getList,\n  remove,\n  saveOrUpdate,\n  downloadByFileId\n} from \"@/api/dagangOilfield/asset/sbbgsq\";\nimport CompTable from \"com/CompTable\";\nimport ElFilter from \"com/ElFilter\";\nimport { formatterDateTime } from \"@/utils/handleData\";\n//流程\nimport activiti from \"com/activiti_sbbgsq\";\nimport timeLine from \"com/timeLine\";\nimport { HistoryList } from \"@/api/activiti/processTask\";\nimport api from \"@/utils/request\";\nimport { getOrganizationSelected } from \"@/api/dagangOilfield/asset/bdsbtz\";\nimport ElImageViewer from \"element-ui/packages/image/src/image-viewer\";\nimport { getFgsOptions } from \"@/api/yxgl/bdyxgl/zbgl\";\nexport default {\n  components: { CompTable, ElFilter, activiti, timeLine, ElImageViewer },\n  name: \"sbbgsq\",\n  data() {\n    return {\n      rules: {\n        bglx: [\n          { required: true, message: \"请选择变更类型\", trigger: \"change\" }\n        ],\n        description: [\n          { required: true, message: \"变更说明不能为空\", trigger: \"change\" }\n        ]\n      },\n      //附件列表数据\n      fjDataList: [],\n      //附件列表弹出框\n      openFjDialog: false,\n      fjUploadList: [], //除图片外的上传列表\n      //图片地址url\n      dialogImageUrl: \"\",\n      //展示图片dialog控制\n      imgDialogVisible: false,\n      //上传图片时的携带的其他参数\n      uploadImgData: {\n        businessId: \"\" //携带的表单主键id\n      },\n      //图片list\n      imgList: [],\n      //上传图片时的请求头\n      header: {},\n      //流程图查看地址\n      imgSrc: \"\",\n      //流程图查看\n      openLoadingImg: false,\n      timeData: [],\n      timeLineShow: false,\n      //弹出框标题\n      activitiOption: { title: \"上报\" },\n      //工作流弹窗\n      isShow: false,\n      //工作流传入参数\n      processData: {\n        processDefinitionKey: \"sbztbg\",\n        businessKey: \"\",\n        businessType: \"设备状态变更\",\n        variables: {},\n        defaultFrom: true,\n        nextUser: \"\",\n        processType: \"complete\"\n      },\n      //所属公司\n      ssgsDataList: [],\n      //变更类型list\n      bglxList: [\n        { label: \"新投\", value: \"新投\" },\n        { label: \"切改\", value: \"切改\" },\n        { label: \"退役\", value: \"退役\" },\n        { label: \"更换\", value: \"更换\" },\n        { label: \"资料完善\", value: \"资料完善\" },\n        { label: \"其它\", value: \"其它\" }\n      ],\n      //工器具详情框字段控制\n      isDisabled: false,\n      //工器具弹出框表头\n      gqjTital: \"设备变更申请新增\",\n\n      //表格内容\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: \"ssgsmc\", label: \"分公司\", minWidth: \"120\" },\n          { prop: \"sqr\", label: \"申请人\", minWidth: \"100\" },\n          { prop: \"bglx\", label: \"变更类型\", minWidth: \"100\" },\n          { prop: \"sqsj\", label: \"申请时间\", minWidth: \"120\" },\n          { prop: \"ztmc\", label: \"流程状态\", minWidth: \"100\" },\n          { prop: \"description\", label: \"变更说明\", minWidth: \"250\" }\n        ]\n      },\n      //筛选条件\n      filterInfo: {\n        data: {\n          ssgs: \"\",\n          sqr: \"\",\n          bglx: \"\",\n          lczt: \"\",\n          ztmc: \"\"\n        },\n        fieldList: [\n          { label: \"申请人\", type: \"input\", value: \"sqr\" },\n          {\n            label: \"变更类型\",\n            type: \"select\",\n            value: \"bglx\",\n            options: [\n              { label: \"新投\", value: \"新投\" },\n              { label: \"切改\", value: \"切改\" },\n              { label: \"退役\", value: \"退役\" },\n              { label: \"更换\", value: \"更换\" },\n              { label: \"资料完善\", value: \"资料完善\" },\n              { label: \"其它\", value: \"其它\" }\n            ]\n          },\n          {\n            label: \"申请时间\",\n            type: \"date\",\n            value: \"tyrqArr\",\n            dateType: \"daterange\",\n            format: \"yyyy-MM-dd\"\n          },\n          { label: \"变更说明\", type: \"input\", value: \"description\" },\n          {\n            label: \"分公司\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"ssgs\",\n            options: []\n          },\n          {\n            label: \"流程状态\",\n            type: \"checkbox\",\n            checkboxValue: [],\n            value: \"ztmc\",\n            options: [\n              { label: \"待上报\", value: \"1\" },\n              { label: \"待分公司审核\", value: \"2\" },\n              { label: \"待生产科审核\", value: \"3\" },\n              { label: \"关闭\", value: \"4\" }\n            ]\n          }\n        ]\n      },\n      //工器具弹出框\n      dialogFormVisible: false,\n      //弹出框表单\n      form: {\n        ssgs: this.$store.getters.deptId.toString()\n      },\n      //删除选择列\n      selectRows: [],\n\n      isSyDetail: false,\n      //查询变更记录参数\n      queryBgsqParams: {\n        ssgs: \"\",\n        sqr: \"\",\n        bglx: \"\",\n        lczt: \"\",\n        pageSize: 10,\n        pageNum: 1\n      }\n    };\n  },\n  watch: {},\n  created() {\n    //获取组织结构下拉数据\n    this.getOrganizationSelected();\n  },\n  mounted() {\n    //获取token\n    this.header.token = getToken();\n    this.getData(this.$route.query);\n    this.getFgsOptions();\n  },\n  methods: {\n    /**\n     * 获取分公司下拉数据\n     */\n    getFgsOptions() {\n      getFgsOptions({}).then(res => {\n        res.data.forEach(item => {\n          item.value = item.value.toString();\n        });\n        this.filterInfo.fieldList.map(item => {\n          if (item.value == \"ssgs\") {\n            return (item.options = res.data);\n          }\n        });\n      });\n    },\n    getOrganizationSelected() {\n      getOrganizationSelected({ parentId: \"1001\" }).then(res => {\n        this.ssgsDataList = res.data;\n      });\n    },\n    //下载附件\n    downLoadFj(row) {\n      //点击下载\n      //开始执行请求\n      try {\n        downloadByFileId(row.fileId).then(res => {});\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //行内查看附件列表\n    handleDataFj(row) {\n      this.fjDataList = row.otherFileList;\n      this.openFjDialog = true;\n    },\n    //上传的附件移除\n    handleFjRemove(file, fileList) {\n      this.fjUploadList = fileList;\n    },\n    //附件上传前的钩子函数\n    beforeFjUpload(file) {\n      const isLt10M = file.size / 1024 / 1024 < 10;\n      if (!isLt10M) {\n        this.$message.error(\"上传附件大小不能超过 10MB!\");\n      }\n      return isLt10M;\n    },\n    //除图片外的上传附件信息\n    handleFjUpload(file, fileList) {\n      this.fjUploadList = fileList.slice(-3);\n    },\n    //图片移除\n    handleRemove(file, fileList) {\n      this.imgList = fileList;\n    },\n    //上传图片成功进度条\n    handleProgress(event, file, fileList) {\n      console.log(\"event\", event);\n      console.log(\"file\", file);\n      console.log(\"fileList\", fileList);\n    },\n    //图片放大\n    handlePictureCardPreview(file) {\n      this.dialogImageUrl = [file.url];\n      this.imgDialogVisible = true;\n    },\n    onClose() {\n      this.imgDialogVisible = false;\n    },\n    // 选择图片文件时，往fileList里添加\n    handleChange(file, fileList) {\n      this.imgList = fileList;\n    },\n    //关闭流程查看页面\n    colseTimeLine() {\n      this.timeLineShow = false;\n    },\n    //工作流回传数据\n    async todoResult(data) {\n      let row = {\n        objId: data.businessKey,\n        isStart: 1,\n        isBack: data.processType === \"rollback\" ? 1 : 0\n      };\n      if (data.processType === \"rollback\") {\n        switch (data.activeTaskName) {\n          case \"设备变更信息填报\":\n            row.lczt = 1;\n            break;\n          case \"分公司审核\":\n            row.lczt = 2;\n            break;\n        }\n      } else {\n        //1-待上报；2-分公司审核；3-生产科审核，4-关闭\n        switch (data.activeTaskName) {\n          case \"分公司审核\":\n            row.lczt = 2;\n            row.fgsshr = data.nextUser;\n            break;\n          case \"生产科审批\":\n            row.lczt = 3;\n            row.sckshr = data.nextUser;\n            break;\n          case \"结束\":\n            row.lczt = 4;\n            break;\n        }\n      }\n\n      saveOrUpdate(row).then(res => {\n        if (res.code === \"0000\") {\n          this.$message.success(\"操作成功\");\n          //重置page页从1开始\n          this.tableAndPageInfo.pager.pageResize = \"Y\";\n          this.getData();\n          this.resetForm();\n        }\n      });\n    },\n    //关闭弹窗\n    closeActiviti() {\n      if (this.$refs.activiti.$refs.form) {\n        this.$refs.activiti.$refs.form.resetFields();\n      }\n      this.isShow = false;\n    },\n    //上传图片\n    uploadFormImg() {\n      //用来存放当前未改动过的图片url,此图片不进行删除处理，其余当前业务id下面的图片数据将进行删除\n      var newUrl = [];\n      var imageType = [\"png\", \"jpg\"];\n      const formData = new FormData();\n      // 因为要传一个文件数组过去，所以要循环append\n      this.imgList.forEach(file => {\n        if (file.raw == undefined) {\n          newUrl.push(file.url);\n        }\n        formData.append(\"files\", file.raw);\n      });\n      formData.append(\"businessId\", this.uploadImgData.businessId); // 自定义参数\n      formData.append(\"newUrl\", newUrl); // 未改动过的图片url\n      formData.append(\"type\", imageType); // 未改动过的图片url\n      api\n        .requestPost(\"/isc-api/file/uploadFiles\", formData, 1)\n        .then(res => {\n          // 清空图片列表（一定要清空，否则上传成功后还是会调用handleChange（）函数，上传成功后列表中还存在图片）\n          this.imgList = [];\n          //查询接口数据\n          this.getData();\n        })\n        .catch(res => {\n          this.$message.success(\"图片上传失败！\");\n        });\n    },\n    //上传除图片视频意外的附件\n    uoloadOtherFile() {\n      //用来存放当前未改动过的url,此图片不进行删除处理，其余当前业务id下面的图片数据将进行删除\n      var newUrl = [];\n      var imageType = [\"docx\", \"xlsx\", \"pdf\"];\n      const formData = new FormData();\n      // 因为要传一个文件数组过去，所以要循环append\n      this.fjUploadList.forEach(file => {\n        if (file.raw == undefined) {\n          newUrl.push(file.url);\n        }\n        formData.append(\"files\", file.raw);\n      });\n      formData.append(\"businessId\", this.uploadImgData.businessId); // 自定义参数\n      formData.append(\"newUrl\", newUrl); // 未改动过的图片url\n      formData.append(\"type\", imageType); // 未改动过的图片url\n      api\n        .requestPost(\"/isc-api/file/uploadFiles\", formData, 1)\n        .then(res => {\n          // 清空图片列表（一定要清空，否则上传成功后还是会调用handleChange（）函数，上传成功后列表中还存在图片）\n          this.fjUploadList = [];\n          //查询接口数据\n          this.getData();\n        })\n        .catch(res => {\n          this.$message.success(\"附件上传失败！\");\n        });\n    },\n    //上报发送办结\n    getSbFsBj(type) {\n      this.dialogFormVisible = false;\n      let row = { ...this.form };\n      this.processData.businessKey = row.objId;\n      if (type === \"complete\") {\n        switch (row.lczt) {\n          case \"1\":\n            this.activitiOption.title = \"提交\";\n            this.processData.defaultFrom = true;\n            this.processData.ssgs = row.ssgs;\n            this.processData.personGroupId = 46;\n            break;\n          case \"2\":\n            this.activitiOption.title = \"提交\";\n            this.processData.ssgs = \"\";\n            this.processData.defaultFrom = true;\n            this.processData.personGroupId = 47;\n            break;\n          case \"3\":\n            this.activitiOption.title = \"通过\";\n            this.processData.defaultFrom = false;\n            break;\n        }\n        this.processData.variables.pass = true;\n        this.processData.processType = \"complete\";\n      } else {\n        switch (row.lczt) {\n          case \"2\":\n            this.activitiOption.title = \"回退原因填写\";\n            this.processData.defaultFrom = true;\n            break;\n          case \"3\":\n            this.activitiOption.title = \"回退原因填写\";\n            this.processData.defaultFrom = true;\n            this.isShow = true;\n            break;\n        }\n        this.processData.processType = \"rollback\";\n        this.processData.variables.pass = false;\n      }\n      this.isShow = true;\n    },\n\n    //流程查看\n    async showTimeLine(row) {\n      this.processData.businessKey = row.objId;\n      let { code, data } = await HistoryList(this.processData);\n      this.timeData = data;\n      this.timeLineShow = true;\n    },\n    //流程图片查看\n    showProcessImg(row) {\n      this.openLoadingImg = true;\n      this.imgSrc =\n        \"/activiti-api/process/read-resource?processDefinitionKey=sbztbg&businessKey=\" +\n        row.objId +\n        \"&t=\" +\n        new Date().getTime();\n    },\n    //工器具列表查询\n    async getData(params) {\n      try {\n        this.queryBgsqParams = { ...this.queryBgsqParams, ...params };\n        this.$refs.bdywgzrzTable.loading = true;\n        const param = { ...this.queryBgsqParams, ...params };\n        const { data, code } = await getList(param);\n        if (code === \"0000\") {\n          data.records.forEach(item => {\n            let filter = this.ssgsDataList.filter(item1 => {\n              return item1.value == item.ssgs;\n            });\n            if (filter.length > 0) {\n              item.ssgsmc = filter[0].label;\n            } else {\n              item.ssgsmc = \"\";\n            }\n          });\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n          this.$nextTick(() => {\n            // 以服务的方式调用的 Loading 需要异步关闭\n            this.$refs.bdywgzrzTable.loading = false;\n          });\n        }\n      } catch (e) {\n        console.log(e);\n        this.$nextTick(() => {\n          // 以服务的方式调用的 Loading 需要异步关闭\n          this.$refs.bdywgzrzTable.loading = false;\n        });\n      }\n    },\n    //工器具列表新增按钮\n    addSensorButton() {\n      this.imgList = []; //置空图片\n      //打开弹出框\n      this.dialogFormVisible = true;\n      //设置弹出框表头\n      this.gqjTital = \"设备变更申请新增\";\n      this.isDisabled = false;\n      //清空弹出框内容\n      this.form = {\n        ssgs: this.$store.getters.deptId.toString(),\n        sqsj: formatterDateTime(new Date(), \"yyyy-MM-dd hh:mm:ss\"),\n        sqr: this.$store.getters.nickName,\n        lczt: \"1\"\n      };\n    },\n    //详情按钮\n    getGqjInfo(row) {\n      //打开弹出框\n      this.dialogFormVisible = true;\n      //设置弹出框表头\n      this.gqjTital = \"设备变更申请详情\";\n      //禁用所有输入框\n      this.isDisabled = true;\n      //给弹出框赋值\n      this.form = { ...row };\n      //图片结合\n      this.imgList = this.form.imgList;\n    },\n    //修改按钮\n    updateGqjInfo(row) {\n      //打开弹出框\n      this.dialogFormVisible = true;\n      //设置弹出框表头\n      this.gqjTital = \"设备变更申请修改\";\n      //开启弹出框内输入框编辑权限\n      this.isDisabled = false;\n      //给弹出框内赋值\n      this.form = { ...row };\n      //图片结合\n      this.imgList = this.form.imgList;\n    },\n    //新增修改保存\n    async qxcommit() {\n      try {\n        await this.$refs[\"form\"].validate(valid => {\n          if (valid) {\n            this.dialogFormVisible = false;\n            saveOrUpdate(this.form).then(res => {\n              if (res.code === \"0000\") {\n                //给上传图片附加业务id参数\n                this.uploadImgData.businessId = res.data.objId;\n                //开始上传图片\n                this.uploadFormImg();\n                //上传文档信息\n                this.uoloadOtherFile();\n                this.$message.success(\"操作成功\");\n                //恢复分页\n                this.tableAndPageInfo.pager.pageResize = \"Y\";\n                this.getData();\n              }\n            });\n          }\n        });\n      } catch (e) {\n        console.log(e);\n      }\n    },\n    //删除\n    deleteRow(id) {\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\"\n      })\n        .then(() => {\n          remove(JSON.stringify(id)).then(({ code }) => {\n            if (code === \"0000\") {\n              this.$message({\n                type: \"success\",\n                message: \"删除成功!\"\n              });\n              this.getData();\n            } else {\n              this.$message({\n                type: \"error\",\n                message: \"删除失败!\"\n              });\n            }\n          });\n        })\n        .catch(() => {\n          this.$message({\n            type: \"info\",\n            message: \"已取消删除\"\n          });\n        });\n      this.tableAndPageInfo.pager.pageResize = \"Y\";\n      this.getData();\n    },\n    //重置\n    filterReset() {\n      this.filterInfo.fieldList.forEach(item => {\n        if (item.type === \"checkbox\") {\n          item.checkboxValue = [];\n        }\n      });\n    },\n    //选择每一行\n    selectChange(rows) {\n      this.selectRows = rows;\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.button-group {\n  padding-left: 30px;\n  padding-right: 30px;\n  display: flex;\n  justify-content: flex-end;\n}\n\n.qxlr_dialog_insert {\n  margin-top: 6vh !important;\n}\n\n/*/deep/ .qxlr_dialog_insert .el-input--medium .el-input__inner{*/\n/*  width: 100%;*/\n/*}*/\n.el-select {\n  width: 100%;\n}\n\n.el-date-editor {\n  width: 100%;\n}\n</style>\n"]}]}