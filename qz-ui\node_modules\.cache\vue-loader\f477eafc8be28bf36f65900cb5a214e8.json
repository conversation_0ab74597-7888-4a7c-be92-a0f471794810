{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\sbcs.vue?vue&type=style&index=0&id=5919619a&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\sbcs.vue", "mtime": 1706897323226}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLyog6K6+572u5rua5Yqo5p2h55qE5qC35byPICovCjo6LXdlYmtpdC1zY3JvbGxiYXIgewogIHdpZHRoOiAxMnB4Owp9CgovKiDmu5rliqjmp70gKi8KOjotd2Via2l0LXNjcm9sbGJhci10cmFjayB7CiAgLy8td2Via2l0LWJveC1zaGFkb3c6aW5zZXQwMDZweHJnYmEoMCwwLDAsMC4zKTsKICBib3JkZXItcmFkaXVzOiAxMHB4Owp9CgovKiDmu5rliqjmnaHmu5HlnZcgKi8KOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7CiAgYm9yZGVyLXJhZGl1czogMTBweDsKICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuMSk7CiAgLy8td2Via2l0LWJveC1zaGFkb3c6Z2JhKDAsMCwwLDAuNSk7Cn0KCjo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWI6d2luZG93LWluYWN0aXZlIHsKICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIDAuMSk7Cn0KLmhlYWQtY29udGFpbmVyIHsKICBtYXJnaW46IDAgYXV0bzsKICB3aWR0aDogOTglOwogIGhlaWdodDogODl2aDsKICBtYXgtaGVpZ2h0OiA4OXZoOwogIG92ZXJmbG93OiBhdXRvOwp9Ci8q57uZ5bem5L6n5pWw57uT5p6EaGVhZGVy5Yqg6aKc6ImyKi8KLmJveC1jYXJkIC5lbC1jYXJkX19oZWFkZXIgewogIGJhY2tncm91bmQ6ICMxMWJhNmQgIWltcG9ydGFudDsKfQouYm94LWNhcmQgewogIG1hcmdpbjowOwp9CgouaXRlbSB7CiAgd2lkdGg6IDIwMHB4OwogIGhlaWdodDogMTQ4cHg7CiAgZmxvYXQ6IGxlZnQ7Cn0KCi50cmVlIHsKICBvdmVyZmxvdy15OiBoaWRkZW47CiAgb3ZlcmZsb3cteDogc2Nyb2xsOwogIHdpZHRoOiA4MHB4OwogIGhlaWdodDogNTAwcHg7Cn0KCi5lbC10cmVlIHsKICBtaW4td2lkdGg6IDEwMCU7CiAgZGlzcGxheTogaW5saW5lLWJsb2NrICFpbXBvcnRhbnQ7Cn0KL2RlZXAvIC5lbC1kaWFsb2c6bm90KC5pcy1mdWxsc2NyZWVuKSB7CiAgbWFyZ2luLXRvcDogOHZoICFpbXBvcnRhbnQ7Cn0K"}, {"version": 3, "sources": ["sbcs.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkZA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "sbcs.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\" id=\"sbcsDiv\">\n    <!--左侧树组件-->\n    <el-row :gutter=\"1\">\n      <!--   左侧树   -->\n      <el-col :span=\"4\">\n        <el-card style=\"background:#e0f8ed;padding-top:10px;\" shadow=\"never\">\n          <div class=\"text head-container\">\n            <el-col style=\"padding:0;\">\n              <el-tree\n                id=\"tree\"\n                :data=\"treeOptions\"\n                @node-click=\"handleNodeClick\"\n                :highlight-current=\"true\"\n                ref=\"tree\"\n                :filter-node-method=\"filterNode\"\n                node-key=\"id\"\n                :default-checked-keys=\"['0']\"\n                :default-expanded-keys=\"['0']\"\n                accordion\n              />\n            </el-col>\n          </div>\n        </el-card>\n      </el-col>\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-filter\n          ref=\"filter1\"\n          :data=\"filterInfo.data\"\n          :field-list=\"filterInfo.fieldList\"\n          :btnHidden=\"false\"\n          :width=\"{ labelWidth: 100, itemWidth: 140 }\"\n          @handleReset=\"filterReset\"\n        />\n        <el-white class=\"button-group1\">\n          <div class=\"button_btn\">\n            <el-button type=\"primary\" icon=\"el-icon-plus\" @click=\"addForm\" :disabled=\"!isCanAdd\">新增</el-button>\n          </div>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            height=\"67.2vh\"\n          >\n            <el-table-column slot=\"table_eight\" align=\"center\" fixed=\"right\" style=\"display: block\" label=\"操作\"\n                             width=\"200\" :resizable=\"false\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button type=\"text\" size=\"small\"\n                           @click=\"updateRow(scope.row)\" title=\"修改\"  class='el-icon-edit'\n                >\n                </el-button>\n                <el-button type=\"text\" size=\"small\"\n                           @click=\"deleteRow(scope.row)\" title=\"删除\"  class='el-icon-delete'\n                >\n                </el-button>\n                <el-button type=\"text\" size=\"small\"\n                           @click=\"viewFun(scope.row)\" title=\"查看\" class='el-icon-view'\n                >\n                </el-button>\n              </template>\n            </el-table-column>\n          </comp-table>\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!--  新增设备部件  -->\n    <el-dialog title=\"评价参数维护\" :visible.sync=\"isShow\" width=\"58%\" @close=\"isShow = false\" v-dialogDrag>\n      <el-form label-width=\"140px\" :rules=\"rules\" :model=\"form\" ref=\"form\">\n        <el-row :gutter=\"15\" class=\"cont_top\">\n          <el-col :span=\"12\">\n            <el-form-item prop=\"sblx\" label=\"评价导则\">\n              <el-select placeholder=\"评价导则\" v-model=\"form.sblx\" style=\"width:80%\" :disabled=\"true\">\n                <el-option\n                  v-for=\"item in sblxList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"sbbj\" label=\"评价部件\">\n              <el-select placeholder=\"评价部件\" v-model=\"form.sbbj\" style=\"width:80%\" :disabled=\"true\">\n                <el-option\n                  v-for=\"item in sbbjList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"csmc\" label=\"参数名称\">\n              <el-input v-model=\"form.csmc\" placeholder=\"请输入参数名称\" style=\"width: 80%\" :disabled=\"isDisabled\"/>\n            </el-form-item>\n          </el-col>\n<!--          <el-col :span=\"12\">\n            <el-form-item prop=\"csbm\" label=\"参数编码\">\n              <el-input v-model=\"form.csbm\" placeholder=\"请输入参数编码\" style=\"width: 80%\" :disabled=\"true\"/>\n            </el-form-item>\n          </el-col>-->\n          <el-col :span=\"12\">\n            <el-form-item prop=\"cslx\" label=\"参数类型\">\n              <el-select placeholder=\"请选择参数类型\" v-model=\"form.cslx\" style=\"width:80%\"\n                         filterable clearable :disabled=\"isDisabled\">\n                <el-option\n                  v-for=\"item in cslxList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item prop=\"jldw\" label=\"计量单位\">\n              <el-select placeholder=\"请选择计量单位\" v-model=\"form.jldw\" style=\"width:80%\"\n                         filterable clearable :disabled=\"isDisabled\">\n                <el-option\n                  v-for=\"item in jldwList\"\n                  :key=\"item.value\"\n                  :label=\"item.label\"\n                  :value=\"String(item.value)\"\n                >\n                </el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" v-if=\"isAddForm\">\n        <el-button @click=\"isShow = false\" size=\"small\">取 消</el-button>\n        <el-button type=\"primary\" size=\"small\" @click=\"saveForm\">保存</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n\n<script>\nimport {getPage,saveOrUpdate,delById} from \"@/api/dagangOilfield/bzgl/sbztpjbzk/sbcs\";\nimport { Loading } from 'element-ui'\nimport { getDictTypeData } from '@/api/system/dict/data'\nimport { getSblxAndSbbjTree } from '@/api/dagangOilfield/bzgl/sbztpjbzk/ztlmxwh'\nimport { getSblxList } from '@/api/dagangOilfield/bzgl/sbqxwh/sbqxwh'\n\nexport default {\n  name: \"sbcswh\",\n  data() {\n    return {\n      loading: null,\n      queryParams:{},\n      filterInfo: {\n        data: {\n          sblx: '',\n          csmc: '',\n          cslx: '',\n          jldw: '',\n        },\n        fieldList: [\n          { label: '评价导则', type: 'select', value: 'sblx',options:[]},\n          { label: '参数名称', type: 'input', value: 'csmc'},\n          { label: '参数类型', type: 'select', value: 'cslx',options:[]},\n          { label: '计量单位', type: 'select', value: 'jldw',options:[]},\n        ]\n      },\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100]\n        },\n        option: {\n          checkBox: true,\n          serialNumber: true\n        },\n        tableData: [],\n        tableHeader: [\n          { prop: 'sblxCn', label: '评价导则', minWidth: '120'},\n          { prop: 'sbbjCn', label: '评价部件', minWidth: '120'},\n          { prop: 'csmc', label: '参数名称', minWidth: '140'},\n          // { prop: 'csbm', label: '参数编码', minWidth: '120'},\n          { prop: 'cslx', label: '参数类型', minWidth: '100'},\n          { prop: 'jldw', label: '计量单位', minWidth: '100'},\n        ]\n      },\n      rules:{\n        sblx: [\n          { required: true, message: '评价导则不能为空', trigger: 'select' }\n        ],\n        sbbj: [\n          { required: true, message: '评价部件不能为空', trigger: 'select' }\n        ],\n        csmc: [\n          { required: true, message: '参数名称不能为空', trigger: 'blur' }\n        ],\n        cslx: [\n          { required: true, message: '参数类型不能为空', trigger: 'select' }\n        ],\n        jldw: [\n          { required: true, message: '计量单位不能为空', trigger: 'select' }\n        ],\n      },//校验规则\n      treeOptions: [], //组织树\n      treeNodeData:{},//点击后的树节点数据\n      form:{},//参数表单\n      isShow:false,//是否显示新增弹框\n      isDisabled:false,//是否不可编辑\n      isAddForm:false,//是否新增\n      sblxList:[],//评价导则下拉框\n      cslxList:[],//参数类型下拉框\n      jldwList:[],//计量单位下拉框\n      sbbjList:[],//评价部件下拉框\n      isCanAdd:false,//是否可以点击新增按钮\n      sblxVal:'',//设备类型\n      sbbjVal:'',//设备部件\n    };\n  },\n  mounted() {\n    this.getTreeData();\n    this.getData();\n    this.getOptions();//获取下拉框\n  },\n\n  methods: {\n    //获取设备类型下拉框\n    async getSblxList(){\n      await getSblxList({qxlb:''}).then(res=>{\n          this.filterInfo.fieldList.forEach(item=>{\n            if(item.value ==='sblx'){\n              item.options = res.data;\n              return false;\n            }\n          })\n      })\n    },\n    getOptions(){\n      this.getCslxList();//参数类型下拉框\n      this.getJldwList();//计量单位下拉框\n      this.getSblxList();//评价导则下拉框\n    },\n    async getCslxList(){\n      await getDictTypeData('sbcs-cslx').then(res=>{\n        res.data.forEach(item=>{\n          this.cslxList.push({label:item.label,value:item.value})\n        })\n        this.filterInfo.fieldList.forEach(item1=>{\n          if(item1.value === 'cslx'){\n            item1.options = this.cslxList;\n            return false;\n          }\n        })\n      })\n    },\n    async getJldwList(){\n      await getDictTypeData('sbcs-jldw').then(res=>{\n        res.data.forEach(item=>{\n          this.jldwList.push({label:item.label,value:item.value})\n        })\n        this.filterInfo.fieldList.forEach(item1=>{\n          if(item1.value === 'jldw'){\n            item1.options = this.jldwList;\n            return false;\n          }\n        })\n      })\n    },\n    //新增表单\n    addForm(){\n      this.form = {};\n      this.form.sblx = this.sblxVal;//设置评价导则\n      this.form.sbbj = this.sbbjVal;//设置评价部件\n      this.isShow = true;\n      this.isDisabled = false;\n      this.isAddForm = true;\n    },\n    //重置按钮\n    filterReset() {\n\n    },\n    //树监听事件\n    filterNode(value, data) {\n\n    },\n    //树节点点击事件\n    handleNodeClick(node) {\n      this.isCanAdd = false;\n      this.treeNodeData = node;\n      let nodeLevel= this.treeNodeData.nodeLevel;\n      if(nodeLevel === '0'){//专业一级\n        this.queryParams.sblx = '';\n        this.queryParams.sbbj = '';\n        this.sblxVal ='';\n        this.sbbjVal ='';\n      }else if(nodeLevel === '1'){//设备类型\n        this.queryParams.sblx = this.treeNodeData.id;\n        this.queryParams.sbbj = '';//清空部件条件\n        this.sblxVal = this.treeNodeData.id;\n        this.sblxList = [{label:this.treeNodeData.label,value:this.treeNodeData.id}];\n      }else if(nodeLevel === '2'){//设备部件\n        this.queryParams.sbbj = this.treeNodeData.id;\n        this.sbbjVal = this.treeNodeData.id;\n        this.sbbjList = [{label:this.treeNodeData.label,value:this.treeNodeData.id}];\n        this.isCanAdd = true;\n      }\n      this.getData();\n    },\n\n    //获取评价导则树数据\n    async getTreeData(){\n      await getSblxAndSbbjTree({type:'sbcs'}).then((res) => {\n        if (res.code === \"0000\") {\n          this.treeOptions = res.data;\n        }\n      });\n    },\n    //获取列表\n    async getData(params) {\n      this.loading = true\n      this.queryParams = {...this.queryParams, ...params}\n      await getPage(this.queryParams).then(res=>{\n        this.tableAndPageInfo.tableData = res.data.records\n        this.tableAndPageInfo.pager.total = res.data.total\n        this.loading = false\n      })\n    },\n    //编辑\n    async updateRow(row){\n      //开启遮罩层\n      this.loading = Loading.service({\n        lock: true, //lock的修改符--默认是false\n        text: \"加载中，请稍后\", //显示在加载图标下方的加载文案\n        spinner: \"el-icon-loading\", //自定义加载图标类名\n        background: \"rgba(0, 0, 0, 0.7)\", //遮罩层颜色\n        target: document.querySelector(\"#sbcsDiv\"),\n      });\n      this.form = {...row};\n      this.isShow = true;\n      this.isDisabled = false;\n      this.isAddForm = true;\n      this.loading.close();//关闭遮罩层\n    },\n    //删除\n    deleteRow(row){\n      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        delById([row.id]).then(res => {\n          if(res.code === '0000'){\n            this.$message({\n              type: 'success',\n              message: '删除成功!'\n            });\n            this.getData();\n          }else{\n            this.$message({\n              type: 'error',\n              message: '删除失败!'\n            });\n          }\n        })\n      })\n    },\n    //查看\n    viewFun(row){\n      this.viewForm = {...row};\n      this.isDisabled = true;\n      this.isAddForm = false;\n      this.isShow = true;\n    },\n    //保存\n    async saveForm(){\n      await this.$refs['form'].validate((valid) => {\n        if (valid) {\n          saveOrUpdate(this.form).then(res=>{\n            if (res.code === '0000') {\n              this.$message.success('保存成功')\n              this.getData();\n            } else {\n              this.$message.error('操作失败')\n            }\n            this.isShow = false;\n          });\n        } else {\n          this.$message.error('校验未通过！')\n          return false\n        }\n      })\n    },\n  },\n};\n</script>\n<style lang='scss' scoped>\n/* 设置滚动条的样式 */\n::-webkit-scrollbar {\n  width: 12px;\n}\n\n/* 滚动槽 */\n::-webkit-scrollbar-track {\n  //-webkit-box-shadow:inset006pxrgba(0,0,0,0.3);\n  border-radius: 10px;\n}\n\n/* 滚动条滑块 */\n::-webkit-scrollbar-thumb {\n  border-radius: 10px;\n  background: rgba(0, 0, 0, 0.1);\n  //-webkit-box-shadow:gba(0,0,0,0.5);\n}\n\n::-webkit-scrollbar-thumb:window-inactive {\n  background: rgba(0, 0, 0, 0.1);\n}\n.head-container {\n  margin: 0 auto;\n  width: 98%;\n  height: 89vh;\n  max-height: 89vh;\n  overflow: auto;\n}\n/*给左侧数结构header加颜色*/\n.box-card .el-card__header {\n  background: #11ba6d !important;\n}\n.box-card {\n  margin:0;\n}\n\n.item {\n  width: 200px;\n  height: 148px;\n  float: left;\n}\n\n.tree {\n  overflow-y: hidden;\n  overflow-x: scroll;\n  width: 80px;\n  height: 500px;\n}\n\n.el-tree {\n  min-width: 100%;\n  display: inline-block !important;\n}\n/deep/ .el-dialog:not(.is-fullscreen) {\n  margin-top: 8vh !important;\n}\n</style>\n"]}]}