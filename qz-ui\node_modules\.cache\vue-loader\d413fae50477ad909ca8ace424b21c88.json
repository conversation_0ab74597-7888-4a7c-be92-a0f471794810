{"remainingRequest": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\pjdzwh.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\src\\views\\dagangOilfield\\bzgl\\sbztpjbzk\\pjdzwh.vue", "mtime": 1706897323226}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\Shammpool\\work\\code\\dgyt\\01代码\\qz-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgZ2V0UGFnZURhdGFMaXN0QkosIHJlbW92ZUJKLCBzYXZlT3JVcGRhdGVCSiwKfSBmcm9tICJAL2FwaS9kYWdhbmdPaWxmaWVsZC9iemdsL3NienRwamJ6ay9wamR6d2giOwppbXBvcnQge2dldFNibHhBbmRTYmJqVHJlZX0gZnJvbSAiQC9hcGkvZGFnYW5nT2lsZmllbGQvYnpnbC9zYnp0cGpiemsvenRsbXh3aCI7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogInBqZHp3aCIsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHRyZWVkYXRhOiBbXSwKICAgICAgLy/moJHnu5PmnoQKICAgICAgZGVmYXVsdFByb3BzOiB7CiAgICAgICAgY2hpbGRyZW46ICJjaGlsZHJlbiIsCiAgICAgICAgbGFiZWw6ICJsYWJlbCIsCiAgICAgIH0sCiAgICAgIGZvcm06IHt9LAogICAgICAvL+afpeivouivhOS7t+mDqOS9jeWSjOiuvuWkh+enjeexuwogICAgICBxdWVyeXBqZHp3aFBhcmFtOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgIH0sCiAgICAgIC8v5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy/orr7lpIfpg6jku7bliKDpmaTpgInmi6nliJcKICAgICAgc2VsZWN0Um93czogW10sCiAgICAgIC8v5paw5aKe5oyJ6ZKu5o6n5Yi2CiAgICAgIGFkZERpc2FibGVkOiB0cnVlLAogICAgICAvL+eCueWHu+agkeiKgueCuei1i+WAvAogICAgICB0cmVlRm9ybToge30sCiAgICAgIC8v5piv5ZCm56aB55SoCiAgICAgIGlzRGlzYWJsZWQ6IGZhbHNlLAogICAgICAvL+ivhOS7t+mDqOS7tuWIl+ihqAogICAgICB0YWJsZUFuZFBhZ2VJbmZvOiB7CiAgICAgICAgcGFnZXI6IHsKICAgICAgICAgIHBhZ2VTaXplOiAxMCwKICAgICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgICB0b3RhbDogMCwKICAgICAgICAgIHNpemVzOiBbMTAsIDIwLCA1MCwgMTAwXSwKICAgICAgICB9LAogICAgICAgIHRhYmxlRGF0YTogW10sCiAgICAgICAgdGFibGVIZWFkZXI6IFsKICAgICAgICAgIHtsYWJlbDogIuivhOS7t+iuvuWkhyIsIHByb3A6ICJzYmx4IiwgYWxpZ246ICJjZW50ZXIifSwKICAgICAgICAgIHtsYWJlbDogIumDqOS7tuWQjeensCIsIHByb3A6ICJiam1jIiwgYWxpZ246ICJjZW50ZXIifSwKICAgICAgICAgIC8vIHtsYWJlbDogIuadg+mHjSIsIHByb3A6ICJxeiIsIGFsaWduOiAiY2VudGVyIn0sCiAgICAgICAgICAvLyAgIHsgbGFiZWw6ICLorr7lpIfnp43nsbsiLCBwcm9wOiAic2J6bG1jIiwgYWxpZ246ICJjZW50ZXIiIH0sCiAgICAgICAgICB7bGFiZWw6ICLor4Tku7fojIPlm7QiLCBwcm9wOiAicGpmdyIsIGFsaWduOiAiY2VudGVyIn0sCiAgICAgICAgICB7bGFiZWw6ICLor4Tku7flhoXlrrkiLCBwcm9wOiAicGpuciIsIGFsaWduOiAiY2VudGVyIn0sCiAgICAgICAgICB7CiAgICAgICAgICAgIHByb3A6ICJvcGVyYXRpb24iLAogICAgICAgICAgICBsYWJlbDogIuaTjeS9nCIsCiAgICAgICAgICAgIG1pbldpZHRoOiAiMTAwcHgiLAogICAgICAgICAgICBzdHlsZToge2Rpc3BsYXk6ICJibG9jayJ9LAogICAgICAgICAgICAvL+aTjeS9nOWIl+WbuuWumuWGjeWPs+S+pwogICAgICAgICAgICBmaXhlZDogInJpZ2h0IiwKICAgICAgICAgICAgb3BlcmF0aW9uOiBbCiAgICAgICAgICAgICAge25hbWU6ICLkv67mlLkiLCBjbGlja0Z1bjogdGhpcy51cGRhdGVEZXRhaWxzfSwKICAgICAgICAgICAgICB7bmFtZTogIuivpuaDhSIsIGNsaWNrRnVuOiB0aGlzLmdldERldGFpbHN9LAogICAgICAgICAgICBdLAogICAgICAgICAgfSwKICAgICAgICBdLAogICAgICAgIG9wdGlvbjoge2NoZWNrQm94OiB0cnVlLCBzZXJpYWxOdW1iZXI6IHRydWV9LAogICAgICB9LAoKICAgICAgcnVsZXM6IHsKICAgICAgICBiam1jOiBbCiAgICAgICAgICB7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLpg6jku7blkI3np7DkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciJ9LAogICAgICAgIF0sCiAgICAgICAgcXo6IFt7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLmnYPph43kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciJ9XSwKICAgICAgICBwamZ3OiBbCiAgICAgICAgICB7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor4Tku7fojIPlm7TkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciJ9LAogICAgICAgIF0sCiAgICAgICAgcGpucjogWwogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+E5Lu35YaF5a655LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIifSwKICAgICAgICBdLAogICAgICB9LAogICAgICAvL+aYr+WQpuaYvuekuuW8ueahhgogICAgICBpc1Nob3dEZXRhaWxzOiBmYWxzZSwKICAgICAgLy/nu4Tnu4fmoJEKICAgICAgdHJlZU9wdGlvbnM6IFtdLAogICAgICAvL+mDqOS7tuWQjeensOWIl+ihqAogICAgICBiam1jTGlzdDogW10sCiAgICB9OwogIH0sCiAgd2F0Y2g6IHt9LAogIGNyZWF0ZWQoKSB7CiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy5nZXRUcmVlTm9kZSgpOwogIH0sCiAgbWV0aG9kczogewogICAgLy/ojrflj5bmoJHoioLngrnmlbDmja4KICAgIGdldFRyZWVOb2RlKCkgewogICAgICBnZXRTYmx4QW5kU2JialRyZWUoKS50aGVuKChyZXMpID0+IHsKICAgICAgICBpZiAocmVzLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgdGhpcy50cmVlZGF0YSA9IHJlcy5kYXRhOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLy/ojrflj5blr7zliJnpg6jku7bliJfooagKICAgIGFzeW5jIGdldERhdGFCSihwYXJhbXMpIHsKICAgICAgdHJ5IHsKICAgICAgICBjb25zdCBwYXJhbSA9IHsuLi50aGlzLnF1ZXJ5cGpkendoUGFyYW0sIC4uLnBhcmFtc307CiAgICAgICAgY29uc3Qge2RhdGEsIGNvZGV9ID0gYXdhaXQgZ2V0UGFnZURhdGFMaXN0QkoocGFyYW0pOwogICAgICAgIGlmIChjb2RlID09PSAiMDAwMCIpIHsKICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby50YWJsZURhdGEgPSBkYXRhLnJlY29yZHM7CiAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIudG90YWwgPSBkYXRhLnRvdGFsOwogICAgICAgIH0KICAgICAgfSBjYXRjaCAoZSkgewoKICAgICAgfQogICAgfSwKICAgIC8v5qCR6IqC54K554K55Ye75LqL5Lu2CiAgICBoYW5kbGVOb2RlQ2xpY2soZGF0YSxub2RlKSB7CiAgICAgIHRoaXMuYWRkRGlzYWJsZWQgPSB0cnVlOwogICAgICB0aGlzLnRyZWVGb3JtLnNibHggPSAiIjsKICAgICAgdGhpcy50cmVlRm9ybS5zYmx4aWQgPSAiIjsKICAgICAgdGhpcy5xdWVyeXBqZHp3aFBhcmFtLm9iaklkID0gIiI7CiAgICAgIHRoaXMucXVlcnlwamR6d2hQYXJhbS5zYmx4aWQgPSAiIjsKICAgICAgaWYgKGRhdGEubm9kZUxldmVsID09PSAiMSIpIHsKICAgICAgICB0aGlzLnRyZWVGb3JtLnNibHggPSBkYXRhLmxhYmVsOwogICAgICAgIHRoaXMudHJlZUZvcm0uc2JseGlkID0gZGF0YS5pZDsKICAgICAgICB0aGlzLnF1ZXJ5cGpkendoUGFyYW0uc2JseGlkID0gZGF0YS5pZDsKICAgICAgICB0aGlzLmFkZERpc2FibGVkID0gZmFsc2U7CiAgICAgICAgdGhpcy5nZXREYXRhQkooKTsKICAgICAgfQoKICAgICAvKiBpZiAoZGF0YS5ub2RlTGV2ZWwgPT09ICIyIikgewogICAgICAgIHRoaXMudHJlZUZvcm0uc2JseCA9IG5vZGUucGFyZW50LmRhdGEubGFiZWw7CiAgICAgICAgdGhpcy50cmVlRm9ybS5zYmx4aWQgPSBub2RlLnBhcmVudC5kYXRhLmlkOwogICAgICAgIHRoaXMucXVlcnlwamR6d2hQYXJhbS5vYmpJZCA9IGRhdGEuaWQ7CiAgICAgICAgdGhpcy5hZGREaXNhYmxlZCA9IGZhbHNlOwogICAgICAgIHRoaXMuZ2V0RGF0YUJKKCk7CiAgICAgIH0qLwogICAgfSwKCiAgICAvL+mDqOS7tuS/ruaUuQogICAgdXBkYXRlRGV0YWlscyhyb3cpIHsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gdHJ1ZTsKICAgICAgdGhpcy5mb3JtID0gcm93OwogICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZTsKICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLkiOwogICAgfSwKICAgIC8v6YOo5Lu26K+m5oOFCiAgICBnZXREZXRhaWxzKHJvdykgewogICAgICB0aGlzLnRpdGxlID0gIuivpuaDhSI7CiAgICAgIC8v5omT5byA5by556qXCiAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IHRydWU7CiAgICAgIC8v5oqK6KGM5pWw5o2u57uZ5by55Ye65qGG6KGo5Y2VCiAgICAgIHRoaXMuZm9ybSA9IHsuLi5yb3d9OwogICAgICAvL+WwhuihqOWNleS4jeWPr+e8lui+kQogICAgICB0aGlzLmlzRGlzYWJsZWQgPSB0cnVlOwogICAgfSwKCiAgICAvL+iuvuWkh+mDqOS7tuaPkOS6pAogICAgY29tbWl0QWRkKCkgewogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUoKHZhbGlkKSA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBzYXZlT3JVcGRhdGVCSih0aGlzLmZvcm0pLnRoZW4oKHJlcykgPT4gewogICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09ICIwMDAwIikgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2VzcyhyZXMubXNnKTsKICAgICAgICAgICAgICB0aGlzLnRhYmxlQW5kUGFnZUluZm8ucGFnZXIucGFnZVJlc2l6ZSA9ICJZIjsKICAgICAgICAgICAgICB0aGlzLmdldERhdGFCSigpOwogICAgICAgICAgICAgIHRoaXMuaXNTaG93RGV0YWlscyA9IGZhbHNlOwogICAgICAgICAgICAgIHRoaXMuZm9ybSA9IHt9OwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1zZyk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAoKICAgIC8v6K6+5aSH6YOo5Lu25YWz6ZetCiAgICBjbG9zZSgpIHsKICAgICAgdGhpcy5pc1Nob3dEZXRhaWxzID0gZmFsc2U7CiAgICAgIHRoaXMuZm9ybSA9IHt9OwogICAgfSwKCiAgICAvL+WinuWKoAogICAgYWRkU2Vuc29yQnV0dG9uKCkgewogICAgICB0aGlzLmZvcm0uc2JseCA9IHRoaXMudHJlZUZvcm0uc2JseDsKICAgICAgdGhpcy5mb3JtLnNibHhpZCA9IHRoaXMudHJlZUZvcm0uc2JseGlkOwogICAgICB0aGlzLmlzU2hvd0RldGFpbHMgPSB0cnVlOwogICAgICB0aGlzLmlzRGlzYWJsZWQgPSBmYWxzZTsKICAgICAgdGhpcy50aXRsZSA9ICLmlrDlop4iOwogICAgfSwKCiAgICAvL+WIoOmZpAogICAgZGVsZXRlU2Vuc29yQnV0dG9uKCkgewogICAgICBpZiAodGhpcy5zZWxlY3RSb3dzLmxlbmd0aCA8IDEpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+mAieaLqeato+ehrueahOaVsOaNru+8ge+8ge+8gSIpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBsZXQgaWRzID0gdGhpcy5zZWxlY3RSb3dzLm1hcCgoaXRlbSkgPT4gewogICAgICAgIHJldHVybiBpdGVtLm9iaklkOwogICAgICB9KTsKICAgICAgdGhpcy4kY29uZmlybSgi5q2k5pON5L2c5bCG5rC45LmF5Yig6Zmk6K+l5pWw5o2uLCDmmK/lkKbnu6fnu60/IiwgIuaPkOekuiIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciLAogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICAgICAgcmVtb3ZlQkooaWRzKS50aGVuKCh7Y29kZX0pID0+IHsKICAgICAgICAgICAgICBpZiAoY29kZSA9PT0gIjAwMDAiKSB7CiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLAogICAgICAgICAgICAgICAgICBtZXNzYWdlOiAi5Yig6Zmk5oiQ5YqfISIsCiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICAgIHRoaXMudGFibGVBbmRQYWdlSW5mby5wYWdlci5wYWdlUmVzaXplID0gIlkiOwogICAgICAgICAgICAgICAgdGhpcy5nZXREYXRhQkooKTsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgICAgIHR5cGU6ICJlcnJvciIsCiAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6ICLliKDpmaTlpLHotKUhIiwKICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgICAgdGhpcy50YWJsZUFuZFBhZ2VJbmZvLnBhZ2VyLnBhZ2VSZXNpemUgPSAiWSI7CiAgICAgICAgICAgICAgICB0aGlzLmdldERhdGFCSigpOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfSk7CiAgICAgICAgICB9KQogICAgICAgICAgLmNhdGNoKCgpID0+IHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgdHlwZTogImluZm8iLAogICAgICAgICAgICAgIG1lc3NhZ2U6ICLlt7Llj5bmtojliKDpmaQiLAogICAgICAgICAgICB9KTsKICAgICAgICAgIH0pOwogICAgfSwKCiAgICAvL+iOt+WPluihjOaVsOaNrgogICAgc2VsZWN0Q2hhbmdlKHJvd3MpIHsKICAgICAgdGhpcy5zZWxlY3RSb3dzID0gcm93czsKICAgIH0sCiAgfSwKfTsK"}, {"version": 3, "sources": ["pjdzwh.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwHA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "pjdzwh.vue", "sourceRoot": "src/views/dagang<PERSON>ilfield/bzgl/sbztpjbzk", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!--左侧树组件-->\n    <el-row :gutter=1>\n      <el-col :span=\"4\">\n        <el-card class=\"aside_height\" shadow=\"never\">\n          <div slot=\"header\" class=\"clearfix\">\n            <span>评价设备</span>\n          </div>\n          <div style=\"overflow: auto; height: 84vh\">\n            <el-tree\n                highlight-current\n                :data=\"treedata\"\n                :props=\"defaultProps\"\n                @node-click=\"handleNodeClick\"\n            ></el-tree>\n          </div>\n        </el-card>\n      </el-col>\n\n      <!--右侧列表-->\n      <el-col :span=\"20\">\n        <el-white>\n          <el-white class=\"button-group\">\n            <el-button\n                class=\"mb8\"\n                @click=\"addSensorButton\"\n                type=\"primary\"\n                icon=\"el-icon-plus\"\n                :disabled=\"addDisabled\"\n            >新增\n            </el-button>\n            <el-button\n                class=\"mb8\"\n                @click=\"deleteSensorButton\"\n                type=\"danger\"\n                icon=\"el-icon-delete\"\n            >删除\n            </el-button>\n          </el-white>\n          <comp-table\n            :table-and-page-info=\"tableAndPageInfo\"\n            @update:multipleSelection=\"selectChange\"\n            height=\"77.2vh\"\n          />\n        </el-white>\n      </el-col>\n    </el-row>\n\n    <!-- 设备部件新增、修改、详情、弹出-->\n    <el-dialog\n        :title=\"title\"\n        v-dialogDrag\n        :visible.sync=\"isShowDetails\"\n        width=\"50%\"\n        @close=\"close\"\n    >\n      <el-form label-width=\"120px\" ref=\"form\" :model=\"form\" :rules=\"rules\">\n        <el-row :gutter=\"25\" class=\"pull-left\">\n          <el-col :span=\"12\">\n            <el-form-item label=\"评价设备类型：\" prop=\"sblx\">\n              <el-input\n                  placeholder=\"请输入评价设备类型：\"\n                  v-model=\"form.sblx\"\n                  :disabled=\"true\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"部件名称：\" prop=\"bjmc\">\n              <el-input\n                  placeholder=\"请输入部件名称：\"\n                  v-model=\"form.bjmc\"\n              />\n            </el-form-item>\n          </el-col>\n<!--          <el-col :span=\"12\">\n            <el-form-item label=\"权重：\" prop=\"qz\" v-if=\"form.sblxid&&form.sblxid.indexOf('bd')>-1\">\n              <el-input\n                  placeholder=\"请输入权重：\"\n                  v-model=\"form.qz\"\n                  :disabled=\"isDisabled\"\n                  clearable\n              />\n            </el-form-item>\n          </el-col>-->\n          <el-col :span=\"12\">\n            <el-form-item label=\"评价范围：\" prop=\"pjfw\">\n              <el-input\n                  placeholder=\"评价范围：\"\n                  v-model=\"form.pjfw\"\n                  :disabled=\"isDisabled\"\n                  clearable\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"24\" class=\"pull-left\">\n          <el-col :span=\"24\">\n            <el-form-item label=\"评价内容：\" prop=\"pjnr\">\n              <el-input\n                  type=\"textarea\"\n                  placeholder=\"评价内容：\"\n                  v-model=\"form.pjnr\"\n                  :disabled=\"isDisabled\"\n                  clearable\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\">\n        <el-button @click=\"close\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"commitAdd\">保 存</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  getPageDataListBJ, removeBJ, saveOrUpdateBJ,\n} from \"@/api/dagangOilfield/bzgl/sbztpjbzk/pjdzwh\";\nimport {getSblxAndSbbjTree} from \"@/api/dagangOilfield/bzgl/sbztpjbzk/ztlmxwh\";\n\nexport default {\n  name: \"pjdzwh\",\n  data() {\n    return {\n      treedata: [],\n      //树结构\n      defaultProps: {\n        children: \"children\",\n        label: \"label\",\n      },\n      form: {},\n      //查询评价部位和设备种类\n      querypjdzwhParam: {\n        pageNum: 1,\n        pageSize: 10,\n      },\n      //标题\n      title: \"\",\n      //设备部件删除选择列\n      selectRows: [],\n      //新增按钮控制\n      addDisabled: true,\n      //点击树节点赋值\n      treeForm: {},\n      //是否禁用\n      isDisabled: false,\n      //评价部件列表\n      tableAndPageInfo: {\n        pager: {\n          pageSize: 10,\n          pageNum: 1,\n          total: 0,\n          sizes: [10, 20, 50, 100],\n        },\n        tableData: [],\n        tableHeader: [\n          {label: \"评价设备\", prop: \"sblx\", align: \"center\"},\n          {label: \"部件名称\", prop: \"bjmc\", align: \"center\"},\n          // {label: \"权重\", prop: \"qz\", align: \"center\"},\n          //   { label: \"设备种类\", prop: \"sbzlmc\", align: \"center\" },\n          {label: \"评价范围\", prop: \"pjfw\", align: \"center\"},\n          {label: \"评价内容\", prop: \"pjnr\", align: \"center\"},\n          {\n            prop: \"operation\",\n            label: \"操作\",\n            minWidth: \"100px\",\n            style: {display: \"block\"},\n            //操作列固定再右侧\n            fixed: \"right\",\n            operation: [\n              {name: \"修改\", clickFun: this.updateDetails},\n              {name: \"详情\", clickFun: this.getDetails},\n            ],\n          },\n        ],\n        option: {checkBox: true, serialNumber: true},\n      },\n\n      rules: {\n        bjmc: [\n          {required: true, message: \"部件名称不能为空\", trigger: \"blur\"},\n        ],\n        qz: [{required: true, message: \"权重不能为空\", trigger: \"blur\"}],\n        pjfw: [\n          {required: true, message: \"评价范围不能为空\", trigger: \"blur\"},\n        ],\n        pjnr: [\n          {required: true, message: \"评价内容不能为空\", trigger: \"blur\"},\n        ],\n      },\n      //是否显示弹框\n      isShowDetails: false,\n      //组织树\n      treeOptions: [],\n      //部件名称列表\n      bjmcList: [],\n    };\n  },\n  watch: {},\n  created() {\n  },\n  mounted() {\n    this.getTreeNode();\n  },\n  methods: {\n    //获取树节点数据\n    getTreeNode() {\n      getSblxAndSbbjTree().then((res) => {\n        if (res.code === \"0000\") {\n          this.treedata = res.data;\n        }\n      });\n    },\n    //获取导则部件列表\n    async getDataBJ(params) {\n      try {\n        const param = {...this.querypjdzwhParam, ...params};\n        const {data, code} = await getPageDataListBJ(param);\n        if (code === \"0000\") {\n          this.tableAndPageInfo.tableData = data.records;\n          this.tableAndPageInfo.pager.total = data.total;\n        }\n      } catch (e) {\n\n      }\n    },\n    //树节点点击事件\n    handleNodeClick(data,node) {\n      this.addDisabled = true;\n      this.treeForm.sblx = \"\";\n      this.treeForm.sblxid = \"\";\n      this.querypjdzwhParam.objId = \"\";\n      this.querypjdzwhParam.sblxid = \"\";\n      if (data.nodeLevel === \"1\") {\n        this.treeForm.sblx = data.label;\n        this.treeForm.sblxid = data.id;\n        this.querypjdzwhParam.sblxid = data.id;\n        this.addDisabled = false;\n        this.getDataBJ();\n      }\n\n     /* if (data.nodeLevel === \"2\") {\n        this.treeForm.sblx = node.parent.data.label;\n        this.treeForm.sblxid = node.parent.data.id;\n        this.querypjdzwhParam.objId = data.id;\n        this.addDisabled = false;\n        this.getDataBJ();\n      }*/\n    },\n\n    //部件修改\n    updateDetails(row) {\n      this.isShowDetails = true;\n      this.form = row;\n      this.isDisabled = false;\n      this.title = \"修改\";\n    },\n    //部件详情\n    getDetails(row) {\n      this.title = \"详情\";\n      //打开弹窗\n      this.isShowDetails = true;\n      //把行数据给弹出框表单\n      this.form = {...row};\n      //将表单不可编辑\n      this.isDisabled = true;\n    },\n\n    //设备部件提交\n    commitAdd() {\n      this.$refs[\"form\"].validate((valid) => {\n        if (valid) {\n          saveOrUpdateBJ(this.form).then((res) => {\n            if (res.code === \"0000\") {\n              this.$message.success(res.msg);\n              this.tableAndPageInfo.pager.pageResize = \"Y\";\n              this.getDataBJ();\n              this.isShowDetails = false;\n              this.form = {};\n            } else {\n              this.$message.error(res.msg);\n            }\n          });\n        }\n      });\n    },\n\n    //设备部件关闭\n    close() {\n      this.isShowDetails = false;\n      this.form = {};\n    },\n\n    //增加\n    addSensorButton() {\n      this.form.sblx = this.treeForm.sblx;\n      this.form.sblxid = this.treeForm.sblxid;\n      this.isShowDetails = true;\n      this.isDisabled = false;\n      this.title = \"新增\";\n    },\n\n    //删除\n    deleteSensorButton() {\n      if (this.selectRows.length < 1) {\n        this.$message.warning(\"请选择正确的数据！！！\");\n        return;\n      }\n      let ids = this.selectRows.map((item) => {\n        return item.objId;\n      });\n      this.$confirm(\"此操作将永久删除该数据, 是否继续?\", \"提示\", {\n        confirmButtonText: \"确定\",\n        cancelButtonText: \"取消\",\n        type: \"warning\",\n      }).then(() => {\n            removeBJ(ids).then(({code}) => {\n              if (code === \"0000\") {\n                this.$message({\n                  type: \"success\",\n                  message: \"删除成功!\",\n                });\n                this.tableAndPageInfo.pager.pageResize = \"Y\";\n                this.getDataBJ();\n              } else {\n                this.$message({\n                  type: \"error\",\n                  message: \"删除失败!\",\n                });\n                this.tableAndPageInfo.pager.pageResize = \"Y\";\n                this.getDataBJ();\n              }\n            });\n          })\n          .catch(() => {\n            this.$message({\n              type: \"info\",\n              message: \"已取消删除\",\n            });\n          });\n    },\n\n    //获取行数据\n    selectChange(rows) {\n      this.selectRows = rows;\n    },\n  },\n};\n</script>\n"]}]}